686230bdfc0a6dcd9a76dfb1df7b0476
'use strict';

exports.__esModule = true;
exports.default = void 0;
var ReactNativeFeatureFlags = {
  isLayoutAnimationEnabled: function isLayoutAnimationEnabled() {
    return true;
  },
  shouldEmitW3CPointerEvents: function shouldEmitW3CPointerEvents() {
    return false;
  },
  shouldPressibilityUseW3CPointerEventsForHover: function shouldPressibilityUseW3CPointerEventsForHover() {
    return false;
  },
  animatedShouldDebounceQueueFlush: function animatedShouldDebounceQueueFlush() {
    return false;
  },
  animatedShouldUseSingleOp: function animatedShouldUseSingleOp() {
    return false;
  }
};
var _default = exports.default = ReactNativeFeatureFlags;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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