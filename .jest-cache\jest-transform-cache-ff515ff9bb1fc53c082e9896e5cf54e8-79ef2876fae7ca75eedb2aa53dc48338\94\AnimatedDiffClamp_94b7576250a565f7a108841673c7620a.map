{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_AnimatedInterpolation", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "AnimatedDiffClamp", "_AnimatedWithChildren2", "a", "min", "max", "_this", "_a", "_min", "_max", "_value", "_lastValue", "__getValue", "key", "value", "__makeNative", "platformConfig", "interpolate", "config", "diff", "Math", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag", "_default", "module"], "sources": ["AnimatedDiffClamp.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _AnimatedInterpolation = _interopRequireDefault(require(\"./AnimatedInterpolation\"));\nvar _AnimatedWithChildren = _interopRequireDefault(require(\"./AnimatedWithChildren\"));\nclass AnimatedDiffClamp extends _AnimatedWithChildren.default {\n  constructor(a, min, max) {\n    super();\n    this._a = a;\n    this._min = min;\n    this._max = max;\n    this._value = this._lastValue = this._a.__getValue();\n  }\n  __makeNative(platformConfig) {\n    this._a.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n  interpolate(config) {\n    return new _AnimatedInterpolation.default(this, config);\n  }\n  __getValue() {\n    var value = this._a.__getValue();\n    var diff = value - this._lastValue;\n    this._lastValue = value;\n    this._value = Math.min(Math.max(this._value + diff, this._min), this._max);\n    return this._value;\n  }\n  __attach() {\n    this._a.__addChild(this);\n  }\n  __detach() {\n    this._a.__removeChild(this);\n    super.__detach();\n  }\n  __getNativeConfig() {\n    return {\n      type: 'diffclamp',\n      input: this._a.__getNativeTag(),\n      min: this._min,\n      max: this._max\n    };\n  }\n}\nvar _default = exports.default = AnimatedDiffClamp;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,sBAAsB,GAAGH,sBAAsB,CAACxB,OAAO,0BAA0B,CAAC,CAAC;AACvF,IAAI4B,qBAAqB,GAAGJ,sBAAsB,CAACxB,OAAO,yBAAyB,CAAC,CAAC;AAAC,IAChF6B,iBAAiB,aAAAC,sBAAA;EACrB,SAAAD,kBAAYE,CAAC,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAjC,gBAAA,CAAAU,OAAA,QAAAkB,iBAAA;IACvBK,KAAA,GAAA3B,UAAA,OAAAsB,iBAAA;IACAK,KAAA,CAAKC,EAAE,GAAGJ,CAAC;IACXG,KAAA,CAAKE,IAAI,GAAGJ,GAAG;IACfE,KAAA,CAAKG,IAAI,GAAGJ,GAAG;IACfC,KAAA,CAAKI,MAAM,GAAGJ,KAAA,CAAKK,UAAU,GAAGL,KAAA,CAAKC,EAAE,CAACK,UAAU,CAAC,CAAC;IAAC,OAAAN,KAAA;EACvD;EAAC,IAAA5B,UAAA,CAAAK,OAAA,EAAAkB,iBAAA,EAAAC,sBAAA;EAAA,WAAA5B,aAAA,CAAAS,OAAA,EAAAkB,iBAAA;IAAAY,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAACT,EAAE,CAACQ,YAAY,CAACC,cAAc,CAAC;MACpCvB,aAAA,CAAAQ,iBAAA,4BAAmBe,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAG,WAAWA,CAACC,MAAM,EAAE;MAClB,OAAO,IAAInB,sBAAsB,CAAChB,OAAO,CAAC,IAAI,EAAEmC,MAAM,CAAC;IACzD;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAF,UAAUA,CAAA,EAAG;MACX,IAAIE,KAAK,GAAG,IAAI,CAACP,EAAE,CAACK,UAAU,CAAC,CAAC;MAChC,IAAIO,IAAI,GAAGL,KAAK,GAAG,IAAI,CAACH,UAAU;MAClC,IAAI,CAACA,UAAU,GAAGG,KAAK;MACvB,IAAI,CAACJ,MAAM,GAAGU,IAAI,CAAChB,GAAG,CAACgB,IAAI,CAACf,GAAG,CAAC,IAAI,CAACK,MAAM,GAAGS,IAAI,EAAE,IAAI,CAACX,IAAI,CAAC,EAAE,IAAI,CAACC,IAAI,CAAC;MAC1E,OAAO,IAAI,CAACC,MAAM;IACpB;EAAC;IAAAG,GAAA;IAAAC,KAAA,EACD,SAAAO,QAAQA,CAAA,EAAG;MACT,IAAI,CAACd,EAAE,CAACe,UAAU,CAAC,IAAI,CAAC;IAC1B;EAAC;IAAAT,GAAA;IAAAC,KAAA,EACD,SAAAS,QAAQA,CAAA,EAAG;MACT,IAAI,CAAChB,EAAE,CAACiB,aAAa,CAAC,IAAI,CAAC;MAC3B/B,aAAA,CAAAQ,iBAAA;IACF;EAAC;IAAAY,GAAA;IAAAC,KAAA,EACD,SAAAW,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLC,IAAI,EAAE,WAAW;QACjBC,KAAK,EAAE,IAAI,CAACpB,EAAE,CAACqB,cAAc,CAAC,CAAC;QAC/BxB,GAAG,EAAE,IAAI,CAACI,IAAI;QACdH,GAAG,EAAE,IAAI,CAACI;MACZ,CAAC;IACH;EAAC;AAAA,EApC6BT,qBAAqB,CAACjB,OAAO;AAsC7D,IAAI8C,QAAQ,GAAGhC,OAAO,CAACd,OAAO,GAAGkB,iBAAiB;AAClD6B,MAAM,CAACjC,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}