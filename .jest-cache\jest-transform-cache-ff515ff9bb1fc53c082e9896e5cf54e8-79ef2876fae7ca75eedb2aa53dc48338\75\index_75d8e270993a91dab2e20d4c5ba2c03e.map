{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_InteractionManager", "<PERSON><PERSON><PERSON>", "callback", "delayMS", "_delay", "_callback", "key", "value", "dispose", "options", "abort", "_task<PERSON><PERSON>le", "cancel", "schedule", "_this", "timeoutH<PERSON>le", "setTimeout", "runAfterInteractions", "clearTimeout", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _InteractionManager = _interopRequireDefault(require(\"../../../exports/InteractionManager\"));\n/**\n * A simple class for batching up invocations of a low-pri callback. A timeout is set to run the\n * callback once after a delay, no matter how many times it's scheduled. Once the delay is reached,\n * InteractionManager.runAfterInteractions is used to invoke the callback after any hi-pri\n * interactions are done running.\n *\n * Make sure to cleanup with dispose().  Example:\n *\n *   class Widget extends React.Component {\n *     _batchedSave: new Batchinator(() => this._saveState, 1000);\n *     _saveSate() {\n *       // save this.state to disk\n *     }\n *     componentDidUpdate() {\n *       this._batchedSave.schedule();\n *     }\n *     componentWillUnmount() {\n *       this._batchedSave.dispose();\n *     }\n *     ...\n *   }\n */\nclass Batchinator {\n  constructor(callback, delayMS) {\n    this._delay = delayMS;\n    this._callback = callback;\n  }\n  /*\n   * Cleanup any pending tasks.\n   *\n   * By default, if there is a pending task the callback is run immediately. Set the option abort to\n   * true to not call the callback if it was pending.\n   */\n  dispose(options) {\n    if (options === void 0) {\n      options = {\n        abort: false\n      };\n    }\n    if (this._taskHandle) {\n      this._taskHandle.cancel();\n      if (!options.abort) {\n        this._callback();\n      }\n      this._taskHandle = null;\n    }\n  }\n  schedule() {\n    if (this._taskHandle) {\n      return;\n    }\n    var timeoutHandle = setTimeout(() => {\n      this._taskHandle = _InteractionManager.default.runAfterInteractions(() => {\n        // Note that we clear the handle before invoking the callback so that if the callback calls\n        // schedule again, it will actually schedule another task.\n        this._taskHandle = null;\n        this._callback();\n      });\n    }, this._delay);\n    this._taskHandle = {\n      cancel: () => clearTimeout(timeoutHandle)\n    };\n  }\n}\nvar _default = exports.default = Batchinator;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,mBAAmB,GAAGJ,sBAAsB,CAACH,OAAO,sCAAsC,CAAC,CAAC;AAAC,IAuB3FQ,WAAW;EACf,SAAAA,YAAYC,QAAQ,EAAEC,OAAO,EAAE;IAAA,IAAAT,gBAAA,CAAAG,OAAA,QAAAI,WAAA;IAC7B,IAAI,CAACG,MAAM,GAAGD,OAAO;IACrB,IAAI,CAACE,SAAS,GAAGH,QAAQ;EAC3B;EAAC,WAAAP,aAAA,CAAAE,OAAA,EAAAI,WAAA;IAAAK,GAAA;IAAAC,KAAA,EAOD,SAAAC,OAAOA,CAACC,OAAO,EAAE;MACf,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;QACtBA,OAAO,GAAG;UACRC,KAAK,EAAE;QACT,CAAC;MACH;MACA,IAAI,IAAI,CAACC,WAAW,EAAE;QACpB,IAAI,CAACA,WAAW,CAACC,MAAM,CAAC,CAAC;QACzB,IAAI,CAACH,OAAO,CAACC,KAAK,EAAE;UAClB,IAAI,CAACL,SAAS,CAAC,CAAC;QAClB;QACA,IAAI,CAACM,WAAW,GAAG,IAAI;MACzB;IACF;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAM,QAAQA,CAAA,EAAG;MAAA,IAAAC,KAAA;MACT,IAAI,IAAI,CAACH,WAAW,EAAE;QACpB;MACF;MACA,IAAII,aAAa,GAAGC,UAAU,CAAC,YAAM;QACnCF,KAAI,CAACH,WAAW,GAAGX,mBAAmB,CAACH,OAAO,CAACoB,oBAAoB,CAAC,YAAM;UAGxEH,KAAI,CAACH,WAAW,GAAG,IAAI;UACvBG,KAAI,CAACT,SAAS,CAAC,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAACD,MAAM,CAAC;MACf,IAAI,CAACO,WAAW,GAAG;QACjBC,MAAM,EAAE,SAARA,MAAMA,CAAA;UAAA,OAAQM,YAAY,CAACH,aAAa,CAAC;QAAA;MAC3C,CAAC;IACH;EAAC;AAAA;AAEH,IAAII,QAAQ,GAAGrB,OAAO,CAACD,OAAO,GAAGI,WAAW;AAC5CmB,MAAM,CAACtB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}