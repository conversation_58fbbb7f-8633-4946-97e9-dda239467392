469ef2fe271e1631eca3e3f2a9ebb946
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_o2aoezpu8() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\advancedAnalytics.ts";
  var hash = "fa990f1b5caecbec0143b4dc8578cef898ed9db4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\advancedAnalytics.ts",
    statementMap: {
      "0": {
        start: {
          line: 85,
          column: 41
        },
        end: {
          line: 85,
          column: 43
        }
      },
      "1": {
        start: {
          line: 86,
          column: 21
        },
        end: {
          line: 86,
          column: 25
        }
      },
      "2": {
        start: {
          line: 87,
          column: 22
        },
        end: {
          line: 87,
          column: 24
        }
      },
      "3": {
        start: {
          line: 88,
          column: 26
        },
        end: {
          line: 88,
          column: 31
        }
      },
      "4": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 91,
          column: 46
        }
      },
      "5": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 30
        }
      },
      "6": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 32
        }
      },
      "7": {
        start: {
          line: 104,
          column: 34
        },
        end: {
          line: 118,
          column: 5
        }
      },
      "8": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 120,
          column: 32
        }
      },
      "9": {
        start: {
          line: 123,
          column: 4
        },
        end: {
          line: 123,
          column: 40
        }
      },
      "10": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 128,
          column: 5
        }
      },
      "11": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 31
        }
      },
      "12": {
        start: {
          line: 135,
          column: 4
        },
        end: {
          line: 138,
          column: 7
        }
      },
      "13": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 148,
          column: 7
        }
      },
      "14": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 159,
          column: 22
        }
      },
      "15": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 170,
          column: 19
        }
      },
      "16": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 190,
          column: 46
        }
      },
      "17": {
        start: {
          line: 179,
          column: 32
        },
        end: {
          line: 183,
          column: 12
        }
      },
      "18": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 31
        }
      },
      "19": {
        start: {
          line: 185,
          column: 19
        },
        end: {
          line: 185,
          column: 31
        }
      },
      "20": {
        start: {
          line: 186,
          column: 8
        },
        end: {
          line: 186,
          column: 20
        }
      },
      "21": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 209,
          column: 45
        }
      },
      "22": {
        start: {
          line: 199,
          column: 32
        },
        end: {
          line: 202,
          column: 12
        }
      },
      "23": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 204,
          column: 31
        }
      },
      "24": {
        start: {
          line: 204,
          column: 19
        },
        end: {
          line: 204,
          column: 31
        }
      },
      "25": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 205,
          column: 20
        }
      },
      "26": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 228,
          column: 42
        }
      },
      "27": {
        start: {
          line: 218,
          column: 32
        },
        end: {
          line: 221,
          column: 12
        }
      },
      "28": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 223,
          column: 31
        }
      },
      "29": {
        start: {
          line: 223,
          column: 19
        },
        end: {
          line: 223,
          column: 31
        }
      },
      "30": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 224,
          column: 20
        }
      },
      "31": {
        start: {
          line: 235,
          column: 19
        },
        end: {
          line: 266,
          column: 5
        }
      },
      "32": {
        start: {
          line: 238,
          column: 29
        },
        end: {
          line: 238,
          column: 70
        }
      },
      "33": {
        start: {
          line: 239,
          column: 27
        },
        end: {
          line: 239,
          column: 66
        }
      },
      "34": {
        start: {
          line: 242,
          column: 33
        },
        end: {
          line: 242,
          column: 89
        }
      },
      "35": {
        start: {
          line: 245,
          column: 31
        },
        end: {
          line: 245,
          column: 85
        }
      },
      "36": {
        start: {
          line: 248,
          column: 36
        },
        end: {
          line: 248,
          column: 84
        }
      },
      "37": {
        start: {
          line: 251,
          column: 38
        },
        end: {
          line: 251,
          column: 85
        }
      },
      "38": {
        start: {
          line: 254,
          column: 43
        },
        end: {
          line: 254,
          column: 85
        }
      },
      "39": {
        start: {
          line: 256,
          column: 8
        },
        end: {
          line: 262,
          column: 10
        }
      },
      "40": {
        start: {
          line: 268,
          column: 4
        },
        end: {
          line: 279,
          column: 6
        }
      },
      "41": {
        start: {
          line: 286,
          column: 19
        },
        end: {
          line: 309,
          column: 5
        }
      },
      "42": {
        start: {
          line: 288,
          column: 64
        },
        end: {
          line: 293,
          column: 10
        }
      },
      "43": {
        start: {
          line: 295,
          column: 23
        },
        end: {
          line: 295,
          column: 54
        }
      },
      "44": {
        start: {
          line: 296,
          column: 25
        },
        end: {
          line: 296,
          column: 59
        }
      },
      "45": {
        start: {
          line: 298,
          column: 8
        },
        end: {
          line: 305,
          column: 10
        }
      },
      "46": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 359,
          column: 6
        }
      },
      "47": {
        start: {
          line: 366,
          column: 19
        },
        end: {
          line: 378,
          column: 5
        }
      },
      "48": {
        start: {
          line: 368,
          column: 26
        },
        end: {
          line: 368,
          column: 69
        }
      },
      "49": {
        start: {
          line: 370,
          column: 8
        },
        end: {
          line: 374,
          column: 9
        }
      },
      "50": {
        start: {
          line: 371,
          column: 10
        },
        end: {
          line: 371,
          column: 52
        }
      },
      "51": {
        start: {
          line: 373,
          column: 10
        },
        end: {
          line: 373,
          column: 46
        }
      },
      "52": {
        start: {
          line: 380,
          column: 4
        },
        end: {
          line: 380,
          column: 81
        }
      },
      "53": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 387,
          column: 63
        }
      },
      "54": {
        start: {
          line: 387,
          column: 56
        },
        end: {
          line: 387,
          column: 63
        }
      },
      "55": {
        start: {
          line: 389,
          column: 26
        },
        end: {
          line: 389,
          column: 46
        }
      },
      "56": {
        start: {
          line: 390,
          column: 4
        },
        end: {
          line: 390,
          column: 25
        }
      },
      "57": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 405,
          column: 5
        }
      },
      "58": {
        start: {
          line: 393,
          column: 24
        },
        end: {
          line: 395,
          column: 30
        }
      },
      "59": {
        start: {
          line: 397,
          column: 6
        },
        end: {
          line: 397,
          column: 29
        }
      },
      "60": {
        start: {
          line: 397,
          column: 17
        },
        end: {
          line: 397,
          column: 29
        }
      },
      "61": {
        start: {
          line: 400,
          column: 6
        },
        end: {
          line: 400,
          column: 66
        }
      },
      "62": {
        start: {
          line: 400,
          column: 59
        },
        end: {
          line: 400,
          column: 63
        }
      },
      "63": {
        start: {
          line: 403,
          column: 6
        },
        end: {
          line: 403,
          column: 48
        }
      },
      "64": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 404,
          column: 64
        }
      },
      "65": {
        start: {
          line: 412,
          column: 4
        },
        end: {
          line: 414,
          column: 27
        }
      },
      "66": {
        start: {
          line: 413,
          column: 6
        },
        end: {
          line: 413,
          column: 31
        }
      },
      "67": {
        start: {
          line: 423,
          column: 4
        },
        end: {
          line: 423,
          column: 25
        }
      },
      "68": {
        start: {
          line: 430,
          column: 4
        },
        end: {
          line: 443,
          column: 5
        }
      },
      "69": {
        start: {
          line: 431,
          column: 21
        },
        end: {
          line: 431,
          column: 67
        }
      },
      "70": {
        start: {
          line: 432,
          column: 21
        },
        end: {
          line: 432,
          column: 53
        }
      },
      "71": {
        start: {
          line: 433,
          column: 6
        },
        end: {
          line: 433,
          column: 25
        }
      },
      "72": {
        start: {
          line: 436,
          column: 6
        },
        end: {
          line: 438,
          column: 7
        }
      },
      "73": {
        start: {
          line: 437,
          column: 8
        },
        end: {
          line: 437,
          column: 47
        }
      },
      "74": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 440,
          column: 77
        }
      },
      "75": {
        start: {
          line: 442,
          column: 6
        },
        end: {
          line: 442,
          column: 70
        }
      },
      "76": {
        start: {
          line: 450,
          column: 4
        },
        end: {
          line: 460,
          column: 5
        }
      },
      "77": {
        start: {
          line: 451,
          column: 21
        },
        end: {
          line: 451,
          column: 67
        }
      },
      "78": {
        start: {
          line: 452,
          column: 6
        },
        end: {
          line: 452,
          column: 26
        }
      },
      "79": {
        start: {
          line: 452,
          column: 19
        },
        end: {
          line: 452,
          column: 26
        }
      },
      "80": {
        start: {
          line: 454,
          column: 21
        },
        end: {
          line: 454,
          column: 39
        }
      },
      "81": {
        start: {
          line: 455,
          column: 29
        },
        end: {
          line: 455,
          column: 91
        }
      },
      "82": {
        start: {
          line: 455,
          column: 66
        },
        end: {
          line: 455,
          column: 90
        }
      },
      "83": {
        start: {
          line: 457,
          column: 6
        },
        end: {
          line: 457,
          column: 85
        }
      },
      "84": {
        start: {
          line: 459,
          column: 6
        },
        end: {
          line: 459,
          column: 71
        }
      },
      "85": {
        start: {
          line: 465,
          column: 4
        },
        end: {
          line: 465,
          column: 78
        }
      },
      "86": {
        start: {
          line: 469,
          column: 4
        },
        end: {
          line: 469,
          column: 76
        }
      },
      "87": {
        start: {
          line: 473,
          column: 31
        },
        end: {
          line: 473,
          column: 60
        }
      },
      "88": {
        start: {
          line: 474,
          column: 4
        },
        end: {
          line: 474,
          column: 35
        }
      },
      "89": {
        start: {
          line: 478,
          column: 4
        },
        end: {
          line: 478,
          column: 26
        }
      },
      "90": {
        start: {
          line: 482,
          column: 4
        },
        end: {
          line: 482,
          column: 19
        }
      },
      "91": {
        start: {
          line: 487,
          column: 4
        },
        end: {
          line: 494,
          column: 6
        }
      },
      "92": {
        start: {
          line: 498,
          column: 4
        },
        end: {
          line: 505,
          column: 6
        }
      },
      "93": {
        start: {
          line: 509,
          column: 4
        },
        end: {
          line: 516,
          column: 6
        }
      },
      "94": {
        start: {
          line: 520,
          column: 4
        },
        end: {
          line: 531,
          column: 6
        }
      },
      "95": {
        start: {
          line: 535,
          column: 4
        },
        end: {
          line: 551,
          column: 6
        }
      },
      "96": {
        start: {
          line: 557,
          column: 4
        },
        end: {
          line: 557,
          column: 31
        }
      },
      "97": {
        start: {
          line: 562,
          column: 4
        },
        end: {
          line: 562,
          column: 41
        }
      },
      "98": {
        start: {
          line: 567,
          column: 4
        },
        end: {
          line: 567,
          column: 67
        }
      },
      "99": {
        start: {
          line: 572,
          column: 4
        },
        end: {
          line: 572,
          column: 21
        }
      },
      "100": {
        start: {
          line: 577,
          column: 4
        },
        end: {
          line: 582,
          column: 6
        }
      },
      "101": {
        start: {
          line: 587,
          column: 4
        },
        end: {
          line: 587,
          column: 14
        }
      },
      "102": {
        start: {
          line: 592,
          column: 4
        },
        end: {
          line: 597,
          column: 6
        }
      },
      "103": {
        start: {
          line: 601,
          column: 4
        },
        end: {
          line: 601,
          column: 48
        }
      },
      "104": {
        start: {
          line: 605,
          column: 4
        },
        end: {
          line: 609,
          column: 6
        }
      },
      "105": {
        start: {
          line: 613,
          column: 4
        },
        end: {
          line: 613,
          column: 47
        }
      },
      "106": {
        start: {
          line: 618,
          column: 4
        },
        end: {
          line: 618,
          column: 34
        }
      },
      "107": {
        start: {
          line: 622,
          column: 40
        },
        end: {
          line: 622,
          column: 70
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        },
        loc: {
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 94,
            column: 3
          }
        },
        line: 90
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 99,
            column: 2
          },
          end: {
            line: 99,
            column: 3
          }
        },
        loc: {
          start: {
            line: 103,
            column: 19
          },
          end: {
            line: 129,
            column: 3
          }
        },
        line: 103
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 134,
            column: 2
          },
          end: {
            line: 134,
            column: 3
          }
        },
        loc: {
          start: {
            line: 134,
            column: 97
          },
          end: {
            line: 139,
            column: 3
          }
        },
        line: 134
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 144,
            column: 2
          },
          end: {
            line: 144,
            column: 3
          }
        },
        loc: {
          start: {
            line: 144,
            column: 90
          },
          end: {
            line: 149,
            column: 3
          }
        },
        line: 144
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 154,
            column: 2
          },
          end: {
            line: 154,
            column: 3
          }
        },
        loc: {
          start: {
            line: 154,
            column: 106
          },
          end: {
            line: 160,
            column: 3
          }
        },
        line: 154
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 165,
            column: 2
          },
          end: {
            line: 165,
            column: 3
          }
        },
        loc: {
          start: {
            line: 165,
            column: 111
          },
          end: {
            line: 171,
            column: 3
          }
        },
        line: 165
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 176,
            column: 2
          },
          end: {
            line: 176,
            column: 3
          }
        },
        loc: {
          start: {
            line: 176,
            column: 96
          },
          end: {
            line: 191,
            column: 3
          }
        },
        line: 176
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 178,
            column: 6
          },
          end: {
            line: 178,
            column: 7
          }
        },
        loc: {
          start: {
            line: 178,
            column: 18
          },
          end: {
            line: 187,
            column: 7
          }
        },
        line: 178
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 196,
            column: 2
          },
          end: {
            line: 196,
            column: 3
          }
        },
        loc: {
          start: {
            line: 196,
            column: 77
          },
          end: {
            line: 210,
            column: 3
          }
        },
        line: 196
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 198,
            column: 6
          },
          end: {
            line: 198,
            column: 7
          }
        },
        loc: {
          start: {
            line: 198,
            column: 18
          },
          end: {
            line: 206,
            column: 7
          }
        },
        line: 198
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 215,
            column: 2
          },
          end: {
            line: 215,
            column: 3
          }
        },
        loc: {
          start: {
            line: 215,
            column: 72
          },
          end: {
            line: 229,
            column: 3
          }
        },
        line: 215
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 217,
            column: 6
          },
          end: {
            line: 217,
            column: 7
          }
        },
        loc: {
          start: {
            line: 217,
            column: 18
          },
          end: {
            line: 225,
            column: 7
          }
        },
        line: 217
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 234,
            column: 2
          },
          end: {
            line: 234,
            column: 3
          }
        },
        loc: {
          start: {
            line: 234,
            column: 80
          },
          end: {
            line: 280,
            column: 3
          }
        },
        line: 234
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 236,
            column: 6
          },
          end: {
            line: 236,
            column: 7
          }
        },
        loc: {
          start: {
            line: 236,
            column: 18
          },
          end: {
            line: 263,
            column: 7
          }
        },
        line: 236
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 285,
            column: 2
          },
          end: {
            line: 285,
            column: 3
          }
        },
        loc: {
          start: {
            line: 285,
            column: 78
          },
          end: {
            line: 360,
            column: 3
          }
        },
        line: 285
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 287,
            column: 6
          },
          end: {
            line: 287,
            column: 7
          }
        },
        loc: {
          start: {
            line: 287,
            column: 18
          },
          end: {
            line: 306,
            column: 7
          }
        },
        line: 287
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 365,
            column: 2
          },
          end: {
            line: 365,
            column: 3
          }
        },
        loc: {
          start: {
            line: 365,
            column: 88
          },
          end: {
            line: 381,
            column: 3
          }
        },
        line: 365
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 367,
            column: 6
          },
          end: {
            line: 367,
            column: 7
          }
        },
        loc: {
          start: {
            line: 367,
            column: 18
          },
          end: {
            line: 375,
            column: 7
          }
        },
        line: 367
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 386,
            column: 2
          },
          end: {
            line: 386,
            column: 3
          }
        },
        loc: {
          start: {
            line: 386,
            column: 45
          },
          end: {
            line: 406,
            column: 3
          }
        },
        line: 386
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 400,
            column: 54
          },
          end: {
            line: 400,
            column: 55
          }
        },
        loc: {
          start: {
            line: 400,
            column: 59
          },
          end: {
            line: 400,
            column: 63
          }
        },
        line: 400
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 411,
            column: 2
          },
          end: {
            line: 411,
            column: 3
          }
        },
        loc: {
          start: {
            line: 411,
            column: 37
          },
          end: {
            line: 415,
            column: 3
          }
        },
        line: 411
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 412,
            column: 16
          },
          end: {
            line: 412,
            column: 17
          }
        },
        loc: {
          start: {
            line: 412,
            column: 28
          },
          end: {
            line: 414,
            column: 5
          }
        },
        line: 412
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 420,
            column: 2
          },
          end: {
            line: 420,
            column: 3
          }
        },
        loc: {
          start: {
            line: 420,
            column: 39
          },
          end: {
            line: 424,
            column: 3
          }
        },
        line: 420
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 429,
            column: 2
          },
          end: {
            line: 429,
            column: 3
          }
        },
        loc: {
          start: {
            line: 429,
            column: 72
          },
          end: {
            line: 444,
            column: 3
          }
        },
        line: 429
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 449,
            column: 2
          },
          end: {
            line: 449,
            column: 3
          }
        },
        loc: {
          start: {
            line: 449,
            column: 70
          },
          end: {
            line: 461,
            column: 3
          }
        },
        line: 449
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 455,
            column: 43
          },
          end: {
            line: 455,
            column: 44
          }
        },
        loc: {
          start: {
            line: 455,
            column: 66
          },
          end: {
            line: 455,
            column: 90
          }
        },
        line: 455
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 464,
            column: 2
          },
          end: {
            line: 464,
            column: 3
          }
        },
        loc: {
          start: {
            line: 464,
            column: 38
          },
          end: {
            line: 466,
            column: 3
          }
        },
        line: 464
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 468,
            column: 2
          },
          end: {
            line: 468,
            column: 3
          }
        },
        loc: {
          start: {
            line: 468,
            column: 36
          },
          end: {
            line: 470,
            column: 3
          }
        },
        line: 468
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 472,
            column: 2
          },
          end: {
            line: 472,
            column: 3
          }
        },
        loc: {
          start: {
            line: 472,
            column: 52
          },
          end: {
            line: 475,
            column: 3
          }
        },
        line: 472
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 477,
            column: 2
          },
          end: {
            line: 477,
            column: 3
          }
        },
        loc: {
          start: {
            line: 477,
            column: 32
          },
          end: {
            line: 479,
            column: 3
          }
        },
        line: 477
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 481,
            column: 2
          },
          end: {
            line: 481,
            column: 3
          }
        },
        loc: {
          start: {
            line: 481,
            column: 34
          },
          end: {
            line: 483,
            column: 3
          }
        },
        line: 481
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 486,
            column: 2
          },
          end: {
            line: 486,
            column: 3
          }
        },
        loc: {
          start: {
            line: 486,
            column: 63
          },
          end: {
            line: 495,
            column: 3
          }
        },
        line: 486
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 497,
            column: 2
          },
          end: {
            line: 497,
            column: 3
          }
        },
        loc: {
          start: {
            line: 497,
            column: 61
          },
          end: {
            line: 506,
            column: 3
          }
        },
        line: 497
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 508,
            column: 2
          },
          end: {
            line: 508,
            column: 3
          }
        },
        loc: {
          start: {
            line: 508,
            column: 55
          },
          end: {
            line: 517,
            column: 3
          }
        },
        line: 508
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 519,
            column: 2
          },
          end: {
            line: 519,
            column: 3
          }
        },
        loc: {
          start: {
            line: 519,
            column: 61
          },
          end: {
            line: 532,
            column: 3
          }
        },
        line: 519
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 534,
            column: 2
          },
          end: {
            line: 534,
            column: 3
          }
        },
        loc: {
          start: {
            line: 534,
            column: 52
          },
          end: {
            line: 552,
            column: 3
          }
        },
        line: 534
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 555,
            column: 2
          },
          end: {
            line: 555,
            column: 3
          }
        },
        loc: {
          start: {
            line: 555,
            column: 101
          },
          end: {
            line: 558,
            column: 3
          }
        },
        line: 555
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 560,
            column: 2
          },
          end: {
            line: 560,
            column: 3
          }
        },
        loc: {
          start: {
            line: 560,
            column: 99
          },
          end: {
            line: 563,
            column: 3
          }
        },
        line: 560
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 565,
            column: 2
          },
          end: {
            line: 565,
            column: 3
          }
        },
        loc: {
          start: {
            line: 565,
            column: 95
          },
          end: {
            line: 568,
            column: 3
          }
        },
        line: 565
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 570,
            column: 2
          },
          end: {
            line: 570,
            column: 3
          }
        },
        loc: {
          start: {
            line: 570,
            column: 75
          },
          end: {
            line: 573,
            column: 3
          }
        },
        line: 570
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 575,
            column: 2
          },
          end: {
            line: 575,
            column: 3
          }
        },
        loc: {
          start: {
            line: 575,
            column: 115
          },
          end: {
            line: 583,
            column: 3
          }
        },
        line: 575
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 585,
            column: 2
          },
          end: {
            line: 585,
            column: 3
          }
        },
        loc: {
          start: {
            line: 585,
            column: 92
          },
          end: {
            line: 588,
            column: 3
          }
        },
        line: 585
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 590,
            column: 2
          },
          end: {
            line: 590,
            column: 3
          }
        },
        loc: {
          start: {
            line: 590,
            column: 68
          },
          end: {
            line: 598,
            column: 3
          }
        },
        line: 590
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 600,
            column: 2
          },
          end: {
            line: 600,
            column: 3
          }
        },
        loc: {
          start: {
            line: 600,
            column: 91
          },
          end: {
            line: 602,
            column: 3
          }
        },
        line: 600
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 604,
            column: 2
          },
          end: {
            line: 604,
            column: 3
          }
        },
        loc: {
          start: {
            line: 604,
            column: 59
          },
          end: {
            line: 610,
            column: 3
          }
        },
        line: 604
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 612,
            column: 2
          },
          end: {
            line: 612,
            column: 3
          }
        },
        loc: {
          start: {
            line: 612,
            column: 69
          },
          end: {
            line: 614,
            column: 3
          }
        },
        line: 612
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 616,
            column: 2
          },
          end: {
            line: 616,
            column: 3
          }
        },
        loc: {
          start: {
            line: 616,
            column: 42
          },
          end: {
            line: 619,
            column: 3
          }
        },
        line: 616
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 101,
            column: 4
          },
          end: {
            line: 101,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 101,
            column: 38
          },
          end: {
            line: 101,
            column: 40
          }
        }],
        line: 101
      },
      "1": {
        loc: {
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 102,
            column: 58
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 102,
            column: 45
          },
          end: {
            line: 102,
            column: 58
          }
        }],
        line: 102
      },
      "2": {
        loc: {
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "3": {
        loc: {
          start: {
            line: 134,
            column: 44
          },
          end: {
            line: 134,
            column: 80
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 134,
            column: 78
          },
          end: {
            line: 134,
            column: 80
          }
        }],
        line: 134
      },
      "4": {
        loc: {
          start: {
            line: 144,
            column: 40
          },
          end: {
            line: 144,
            column: 73
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 144,
            column: 71
          },
          end: {
            line: 144,
            column: 73
          }
        }],
        line: 144
      },
      "5": {
        loc: {
          start: {
            line: 154,
            column: 56
          },
          end: {
            line: 154,
            column: 89
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 154,
            column: 87
          },
          end: {
            line: 154,
            column: 89
          }
        }],
        line: 154
      },
      "6": {
        loc: {
          start: {
            line: 165,
            column: 58
          },
          end: {
            line: 165,
            column: 94
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 165,
            column: 92
          },
          end: {
            line: 165,
            column: 94
          }
        }],
        line: 165
      },
      "7": {
        loc: {
          start: {
            line: 176,
            column: 47
          },
          end: {
            line: 176,
            column: 64
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 176,
            column: 59
          },
          end: {
            line: 176,
            column: 64
          }
        }],
        line: 176
      },
      "8": {
        loc: {
          start: {
            line: 177,
            column: 11
          },
          end: {
            line: 190,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 11
          },
          end: {
            line: 190,
            column: 5
          }
        }, {
          start: {
            line: 190,
            column: 9
          },
          end: {
            line: 190,
            column: 45
          }
        }],
        line: 177
      },
      "9": {
        loc: {
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 185,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 185,
            column: 8
          },
          end: {
            line: 185,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 185
      },
      "10": {
        loc: {
          start: {
            line: 196,
            column: 30
          },
          end: {
            line: 196,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 196,
            column: 42
          },
          end: {
            line: 196,
            column: 46
          }
        }],
        line: 196
      },
      "11": {
        loc: {
          start: {
            line: 197,
            column: 11
          },
          end: {
            line: 209,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 197,
            column: 11
          },
          end: {
            line: 209,
            column: 5
          }
        }, {
          start: {
            line: 209,
            column: 9
          },
          end: {
            line: 209,
            column: 44
          }
        }],
        line: 197
      },
      "12": {
        loc: {
          start: {
            line: 204,
            column: 8
          },
          end: {
            line: 204,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 8
          },
          end: {
            line: 204,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "13": {
        loc: {
          start: {
            line: 215,
            column: 27
          },
          end: {
            line: 215,
            column: 44
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 215,
            column: 39
          },
          end: {
            line: 215,
            column: 44
          }
        }],
        line: 215
      },
      "14": {
        loc: {
          start: {
            line: 216,
            column: 11
          },
          end: {
            line: 228,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 216,
            column: 11
          },
          end: {
            line: 228,
            column: 5
          }
        }, {
          start: {
            line: 228,
            column: 9
          },
          end: {
            line: 228,
            column: 41
          }
        }],
        line: 216
      },
      "15": {
        loc: {
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 223,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 223,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "16": {
        loc: {
          start: {
            line: 268,
            column: 11
          },
          end: {
            line: 279,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 268,
            column: 11
          },
          end: {
            line: 268,
            column: 17
          }
        }, {
          start: {
            line: 268,
            column: 21
          },
          end: {
            line: 279,
            column: 5
          }
        }],
        line: 268
      },
      "17": {
        loc: {
          start: {
            line: 285,
            column: 30
          },
          end: {
            line: 285,
            column: 47
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 285,
            column: 42
          },
          end: {
            line: 285,
            column: 47
          }
        }],
        line: 285
      },
      "18": {
        loc: {
          start: {
            line: 311,
            column: 11
          },
          end: {
            line: 359,
            column: 5
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 311,
            column: 11
          },
          end: {
            line: 311,
            column: 17
          }
        }, {
          start: {
            line: 311,
            column: 21
          },
          end: {
            line: 359,
            column: 5
          }
        }],
        line: 311
      },
      "19": {
        loc: {
          start: {
            line: 365,
            column: 52
          },
          end: {
            line: 365,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 365,
            column: 64
          },
          end: {
            line: 365,
            column: 69
          }
        }],
        line: 365
      },
      "20": {
        loc: {
          start: {
            line: 370,
            column: 8
          },
          end: {
            line: 374,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 370,
            column: 8
          },
          end: {
            line: 374,
            column: 9
          }
        }, {
          start: {
            line: 372,
            column: 15
          },
          end: {
            line: 374,
            column: 9
          }
        }],
        line: 370
      },
      "21": {
        loc: {
          start: {
            line: 380,
            column: 11
          },
          end: {
            line: 380,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 380,
            column: 11
          },
          end: {
            line: 380,
            column: 17
          }
        }, {
          start: {
            line: 380,
            column: 21
          },
          end: {
            line: 380,
            column: 80
          }
        }],
        line: 380
      },
      "22": {
        loc: {
          start: {
            line: 387,
            column: 4
          },
          end: {
            line: 387,
            column: 63
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 387,
            column: 4
          },
          end: {
            line: 387,
            column: 63
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 387
      },
      "23": {
        loc: {
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 387,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 387,
            column: 36
          }
        }, {
          start: {
            line: 387,
            column: 40
          },
          end: {
            line: 387,
            column: 54
          }
        }],
        line: 387
      },
      "24": {
        loc: {
          start: {
            line: 397,
            column: 6
          },
          end: {
            line: 397,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 397,
            column: 6
          },
          end: {
            line: 397,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 397
      },
      "25": {
        loc: {
          start: {
            line: 432,
            column: 21
          },
          end: {
            line: 432,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 432,
            column: 30
          },
          end: {
            line: 432,
            column: 48
          }
        }, {
          start: {
            line: 432,
            column: 51
          },
          end: {
            line: 432,
            column: 53
          }
        }],
        line: 432
      },
      "26": {
        loc: {
          start: {
            line: 436,
            column: 6
          },
          end: {
            line: 438,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 436,
            column: 6
          },
          end: {
            line: 438,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 436
      },
      "27": {
        loc: {
          start: {
            line: 452,
            column: 6
          },
          end: {
            line: 452,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 452,
            column: 6
          },
          end: {
            line: 452,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 452
      },
      "28": {
        loc: {
          start: {
            line: 474,
            column: 11
          },
          end: {
            line: 474,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 474,
            column: 11
          },
          end: {
            line: 474,
            column: 19
          }
        }, {
          start: {
            line: 474,
            column: 23
          },
          end: {
            line: 474,
            column: 34
          }
        }],
        line: 474
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0],
      "18": [0, 0],
      "19": [0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fa990f1b5caecbec0143b4dc8578cef898ed9db4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_o2aoezpu8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_o2aoezpu8();
import { withErrorHandling } from "../utils/errorHandler";
import { supabase } from "../lib/supabase";
import AsyncStorage from '@react-native-async-storage/async-storage';
var AdvancedAnalyticsService = function () {
  function AdvancedAnalyticsService() {
    _classCallCheck(this, AdvancedAnalyticsService);
    this.eventQueue = (cov_o2aoezpu8().s[0]++, []);
    this.isOnline = (cov_o2aoezpu8().s[1]++, true);
    this.batchSize = (cov_o2aoezpu8().s[2]++, 50);
    this.flushInterval = (cov_o2aoezpu8().s[3]++, 30000);
    cov_o2aoezpu8().f[0]++;
    cov_o2aoezpu8().s[4]++;
    this.sessionId = this.generateSessionId();
    cov_o2aoezpu8().s[5]++;
    this.startEventBatching();
    cov_o2aoezpu8().s[6]++;
    this.setupNetworkListener();
  }
  return _createClass(AdvancedAnalyticsService, [{
    key: "trackEvent",
    value: (function () {
      var _trackEvent = _asyncToGenerator(function* (eventName) {
        var properties = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_o2aoezpu8().b[0][0]++, {});
        var eventType = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_o2aoezpu8().b[1][0]++, 'user_action');
        cov_o2aoezpu8().f[1]++;
        var event = (cov_o2aoezpu8().s[7]++, {
          id: this.generateEventId(),
          userId: yield this.getCurrentUserId(),
          eventName: eventName,
          eventType: eventType,
          properties: Object.assign({}, properties, {
            timestamp: Date.now(),
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
          }),
          timestamp: new Date().toISOString(),
          sessionId: this.sessionId,
          platform: this.getPlatform(),
          appVersion: this.getAppVersion()
        });
        cov_o2aoezpu8().s[8]++;
        this.eventQueue.push(event);
        cov_o2aoezpu8().s[9]++;
        yield this.storeEventLocally(event);
        cov_o2aoezpu8().s[10]++;
        if (this.eventQueue.length >= this.batchSize) {
          cov_o2aoezpu8().b[2][0]++;
          cov_o2aoezpu8().s[11]++;
          yield this.flushEvents();
        } else {
          cov_o2aoezpu8().b[2][1]++;
        }
      });
      function trackEvent(_x) {
        return _trackEvent.apply(this, arguments);
      }
      return trackEvent;
    }())
  }, {
    key: "trackScreenView",
    value: (function () {
      var _trackScreenView = _asyncToGenerator(function* (screenName) {
        var properties = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_o2aoezpu8().b[3][0]++, {});
        cov_o2aoezpu8().f[2]++;
        cov_o2aoezpu8().s[12]++;
        yield this.trackEvent('screen_view', Object.assign({
          screen_name: screenName
        }, properties));
      });
      function trackScreenView(_x2) {
        return _trackScreenView.apply(this, arguments);
      }
      return trackScreenView;
    }())
  }, {
    key: "trackUserAction",
    value: (function () {
      var _trackUserAction = _asyncToGenerator(function* (action) {
        var context = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_o2aoezpu8().b[4][0]++, {});
        cov_o2aoezpu8().f[3]++;
        cov_o2aoezpu8().s[13]++;
        yield this.trackEvent('user_action', Object.assign({
          action: action
        }, context));
      });
      function trackUserAction(_x3) {
        return _trackUserAction.apply(this, arguments);
      }
      return trackUserAction;
    }())
  }, {
    key: "trackPerformance",
    value: (function () {
      var _trackPerformance = _asyncToGenerator(function* (metric, value) {
        var context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_o2aoezpu8().b[5][0]++, {});
        cov_o2aoezpu8().f[4]++;
        cov_o2aoezpu8().s[14]++;
        yield this.trackEvent('performance_metric', Object.assign({
          metric: metric,
          value: value
        }, context), 'performance');
      });
      function trackPerformance(_x4, _x5) {
        return _trackPerformance.apply(this, arguments);
      }
      return trackPerformance;
    }())
  }, {
    key: "trackBusinessEvent",
    value: (function () {
      var _trackBusinessEvent = _asyncToGenerator(function* (event, value) {
        var properties = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_o2aoezpu8().b[6][0]++, {});
        cov_o2aoezpu8().f[5]++;
        cov_o2aoezpu8().s[15]++;
        yield this.trackEvent('business_event', Object.assign({
          event: event,
          value: value
        }, properties), 'business');
      });
      function trackBusinessEvent(_x6, _x7) {
        return _trackBusinessEvent.apply(this, arguments);
      }
      return trackBusinessEvent;
    }())
  }, {
    key: "getUserBehaviorMetrics",
    value: (function () {
      var _getUserBehaviorMetrics = _asyncToGenerator(function* (userId) {
        var timeRange = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_o2aoezpu8().b[7][0]++, '30d');
        cov_o2aoezpu8().f[6]++;
        cov_o2aoezpu8().s[16]++;
        return (cov_o2aoezpu8().b[8][0]++, withErrorHandling(_asyncToGenerator(function* () {
          cov_o2aoezpu8().f[7]++;
          var _ref2 = (cov_o2aoezpu8().s[17]++, yield supabase.rpc('get_user_behavior_metrics', {
              user_id: userId,
              time_range: timeRange
            })),
            data = _ref2.data,
            error = _ref2.error;
          cov_o2aoezpu8().s[18]++;
          if (error) {
            cov_o2aoezpu8().b[9][0]++;
            cov_o2aoezpu8().s[19]++;
            throw error;
          } else {
            cov_o2aoezpu8().b[9][1]++;
          }
          cov_o2aoezpu8().s[20]++;
          return data;
        }), {
          service: 'Analytics',
          action: 'getUserBehaviorMetrics',
          userId: userId
        }, {
          showUserError: false
        })) || (cov_o2aoezpu8().b[8][1]++, this.getDefaultUserBehaviorMetrics());
      });
      function getUserBehaviorMetrics(_x8) {
        return _getUserBehaviorMetrics.apply(this, arguments);
      }
      return getUserBehaviorMetrics;
    }())
  }, {
    key: "getPerformanceMetrics",
    value: (function () {
      var _getPerformanceMetrics = _asyncToGenerator(function* () {
        var timeRange = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_o2aoezpu8().b[10][0]++, '7d');
        cov_o2aoezpu8().f[8]++;
        cov_o2aoezpu8().s[21]++;
        return (cov_o2aoezpu8().b[11][0]++, withErrorHandling(_asyncToGenerator(function* () {
          cov_o2aoezpu8().f[9]++;
          var _ref4 = (cov_o2aoezpu8().s[22]++, yield supabase.rpc('get_performance_metrics', {
              time_range: timeRange
            })),
            data = _ref4.data,
            error = _ref4.error;
          cov_o2aoezpu8().s[23]++;
          if (error) {
            cov_o2aoezpu8().b[12][0]++;
            cov_o2aoezpu8().s[24]++;
            throw error;
          } else {
            cov_o2aoezpu8().b[12][1]++;
          }
          cov_o2aoezpu8().s[25]++;
          return data;
        }), {
          service: 'Analytics',
          action: 'getPerformanceMetrics'
        }, {
          showUserError: false
        })) || (cov_o2aoezpu8().b[11][1]++, this.getDefaultPerformanceMetrics());
      });
      function getPerformanceMetrics() {
        return _getPerformanceMetrics.apply(this, arguments);
      }
      return getPerformanceMetrics;
    }())
  }, {
    key: "getBusinessMetrics",
    value: (function () {
      var _getBusinessMetrics = _asyncToGenerator(function* () {
        var timeRange = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_o2aoezpu8().b[13][0]++, '30d');
        cov_o2aoezpu8().f[10]++;
        cov_o2aoezpu8().s[26]++;
        return (cov_o2aoezpu8().b[14][0]++, withErrorHandling(_asyncToGenerator(function* () {
          cov_o2aoezpu8().f[11]++;
          var _ref6 = (cov_o2aoezpu8().s[27]++, yield supabase.rpc('get_business_metrics', {
              time_range: timeRange
            })),
            data = _ref6.data,
            error = _ref6.error;
          cov_o2aoezpu8().s[28]++;
          if (error) {
            cov_o2aoezpu8().b[15][0]++;
            cov_o2aoezpu8().s[29]++;
            throw error;
          } else {
            cov_o2aoezpu8().b[15][1]++;
          }
          cov_o2aoezpu8().s[30]++;
          return data;
        }), {
          service: 'Analytics',
          action: 'getBusinessMetrics'
        }, {
          showUserError: false
        })) || (cov_o2aoezpu8().b[14][1]++, this.getDefaultBusinessMetrics());
      });
      function getBusinessMetrics() {
        return _getBusinessMetrics.apply(this, arguments);
      }
      return getBusinessMetrics;
    }())
  }, {
    key: "generatePredictiveInsights",
    value: (function () {
      var _generatePredictiveInsights = _asyncToGenerator(function* (userId) {
        var _this = this,
          _ref8;
        cov_o2aoezpu8().f[12]++;
        var result = (cov_o2aoezpu8().s[31]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_o2aoezpu8().f[13]++;
          var userBehavior = (cov_o2aoezpu8().s[32]++, yield _this.getUserBehaviorMetrics(userId));
          var userEvents = (cov_o2aoezpu8().s[33]++, yield _this.getUserEvents(userId, '90d'));
          var churnProbability = (cov_o2aoezpu8().s[34]++, _this.calculateChurnProbability(userBehavior, userEvents));
          var nextBestAction = (cov_o2aoezpu8().s[35]++, _this.determineNextBestAction(userBehavior, userEvents));
          var recommendedFeatures = (cov_o2aoezpu8().s[36]++, _this.recommendFeatures(userBehavior, userEvents));
          var optimalEngagementTime = (cov_o2aoezpu8().s[37]++, _this.calculateOptimalEngagementTime(userEvents));
          var skillProgressionPrediction = (cov_o2aoezpu8().s[38]++, yield _this.predictSkillProgression(userId));
          cov_o2aoezpu8().s[39]++;
          return {
            churnProbability: churnProbability,
            nextBestAction: nextBestAction,
            recommendedFeatures: recommendedFeatures,
            optimalEngagementTime: optimalEngagementTime,
            skillProgressionPrediction: skillProgressionPrediction
          };
        }), {
          service: 'Analytics',
          action: 'generatePredictiveInsights',
          userId: userId
        }, {
          showUserError: false
        }));
        cov_o2aoezpu8().s[40]++;
        return (_ref8 = (cov_o2aoezpu8().b[16][0]++, result)) != null ? _ref8 : (cov_o2aoezpu8().b[16][1]++, {
          churnProbability: 0,
          nextBestAction: 'Continue training',
          recommendedFeatures: [],
          optimalEngagementTime: '18:00',
          skillProgressionPrediction: {
            currentLevel: 'beginner',
            predictedLevel: 'intermediate',
            timeToAchieve: 90,
            confidence: 0.7
          }
        });
      });
      function generatePredictiveInsights(_x9) {
        return _generatePredictiveInsights.apply(this, arguments);
      }
      return generatePredictiveInsights;
    }())
  }, {
    key: "getAnalyticsDashboard",
    value: (function () {
      var _getAnalyticsDashboard = _asyncToGenerator(function* () {
        var _this2 = this,
          _ref10;
        var timeRange = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_o2aoezpu8().b[17][0]++, '30d');
        cov_o2aoezpu8().f[14]++;
        var result = (cov_o2aoezpu8().s[41]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_o2aoezpu8().f[15]++;
          var _ref0 = (cov_o2aoezpu8().s[42]++, yield Promise.all([_this2.getOverviewMetrics(timeRange), _this2.getAggregatedUserBehavior(timeRange), _this2.getPerformanceMetrics(timeRange), _this2.getBusinessMetrics(timeRange)])),
            _ref1 = _slicedToArray(_ref0, 4),
            overview = _ref1[0],
            userBehavior = _ref1[1],
            performance = _ref1[2],
            business = _ref1[3];
          var trends = (cov_o2aoezpu8().s[43]++, yield _this2.getTrends(timeRange));
          var insights = (cov_o2aoezpu8().s[44]++, yield _this2.getAggregatedInsights());
          cov_o2aoezpu8().s[45]++;
          return {
            overview: overview,
            userBehavior: userBehavior,
            performance: performance,
            business: business,
            insights: insights,
            trends: trends
          };
        }), {
          service: 'Analytics',
          action: 'getAnalyticsDashboard'
        }, {
          showUserError: false
        }));
        cov_o2aoezpu8().s[46]++;
        return (_ref10 = (cov_o2aoezpu8().b[18][0]++, result)) != null ? _ref10 : (cov_o2aoezpu8().b[18][1]++, {
          overview: {
            totalUsers: 0,
            activeUsers: 0,
            sessionCount: 0,
            averageSessionDuration: 0
          },
          userBehavior: {
            sessionDuration: 0,
            screenViews: {},
            featureUsage: {},
            conversionFunnels: {},
            retentionRate: 0,
            engagementScore: 0
          },
          performance: {
            appLaunchTime: 0,
            screenLoadTimes: {},
            apiResponseTimes: {},
            errorRates: {},
            crashRate: 0,
            memoryUsage: []
          },
          business: {
            subscriptionConversions: 0,
            revenuePerUser: 0,
            churnRate: 0,
            lifetimeValue: 0,
            featureAdoption: {},
            supportTickets: 0
          },
          insights: {
            churnProbability: 0,
            nextBestAction: 'Continue training',
            recommendedFeatures: [],
            optimalEngagementTime: '18:00',
            skillProgressionPrediction: {
              currentLevel: 'beginner',
              predictedLevel: 'intermediate',
              timeToAchieve: 90,
              confidence: 0.7
            }
          },
          trends: {
            userGrowth: [],
            engagementTrend: [],
            revenueTrend: []
          }
        });
      });
      function getAnalyticsDashboard() {
        return _getAnalyticsDashboard.apply(this, arguments);
      }
      return getAnalyticsDashboard;
    }())
  }, {
    key: "exportAnalyticsData",
    value: (function () {
      var _exportAnalyticsData = _asyncToGenerator(function* (format) {
        var _this3 = this,
          _ref12;
        var timeRange = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_o2aoezpu8().b[19][0]++, '30d');
        cov_o2aoezpu8().f[16]++;
        var result = (cov_o2aoezpu8().s[47]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_o2aoezpu8().f[17]++;
          var dashboard = (cov_o2aoezpu8().s[48]++, yield _this3.getAnalyticsDashboard(timeRange));
          cov_o2aoezpu8().s[49]++;
          if (format === 'json') {
            cov_o2aoezpu8().b[20][0]++;
            cov_o2aoezpu8().s[50]++;
            return JSON.stringify(dashboard, null, 2);
          } else {
            cov_o2aoezpu8().b[20][1]++;
            cov_o2aoezpu8().s[51]++;
            return _this3.convertToCSV(dashboard);
          }
        }), {
          service: 'Analytics',
          action: 'exportAnalyticsData',
          format: format
        }, {
          showUserError: true
        }));
        cov_o2aoezpu8().s[52]++;
        return (_ref12 = (cov_o2aoezpu8().b[21][0]++, result)) != null ? _ref12 : (cov_o2aoezpu8().b[21][1]++, JSON.stringify({
          error: 'Failed to export data'
        }, null, 2));
      });
      function exportAnalyticsData(_x0) {
        return _exportAnalyticsData.apply(this, arguments);
      }
      return exportAnalyticsData;
    }())
  }, {
    key: "flushEvents",
    value: (function () {
      var _flushEvents = _asyncToGenerator(function* () {
        cov_o2aoezpu8().f[18]++;
        cov_o2aoezpu8().s[53]++;
        if ((cov_o2aoezpu8().b[23][0]++, this.eventQueue.length === 0) || (cov_o2aoezpu8().b[23][1]++, !this.isOnline)) {
          cov_o2aoezpu8().b[22][0]++;
          cov_o2aoezpu8().s[54]++;
          return;
        } else {
          cov_o2aoezpu8().b[22][1]++;
        }
        var eventsToFlush = (cov_o2aoezpu8().s[55]++, _toConsumableArray(this.eventQueue));
        cov_o2aoezpu8().s[56]++;
        this.eventQueue = [];
        cov_o2aoezpu8().s[57]++;
        try {
          var _ref13 = (cov_o2aoezpu8().s[58]++, yield supabase.from('analytics_events').insert(eventsToFlush)),
            error = _ref13.error;
          cov_o2aoezpu8().s[59]++;
          if (error) {
            cov_o2aoezpu8().b[24][0]++;
            cov_o2aoezpu8().s[60]++;
            throw error;
          } else {
            cov_o2aoezpu8().b[24][1]++;
          }
          cov_o2aoezpu8().s[61]++;
          yield this.removeStoredEvents(eventsToFlush.map(function (e) {
            cov_o2aoezpu8().f[19]++;
            cov_o2aoezpu8().s[62]++;
            return e.id;
          }));
        } catch (error) {
          var _this$eventQueue;
          cov_o2aoezpu8().s[63]++;
          (_this$eventQueue = this.eventQueue).unshift.apply(_this$eventQueue, _toConsumableArray(eventsToFlush));
          cov_o2aoezpu8().s[64]++;
          console.error('Failed to flush analytics events:', error);
        }
      });
      function flushEvents() {
        return _flushEvents.apply(this, arguments);
      }
      return flushEvents;
    }())
  }, {
    key: "startEventBatching",
    value: function startEventBatching() {
      var _this4 = this;
      cov_o2aoezpu8().f[20]++;
      cov_o2aoezpu8().s[65]++;
      setInterval(_asyncToGenerator(function* () {
        cov_o2aoezpu8().f[21]++;
        cov_o2aoezpu8().s[66]++;
        yield _this4.flushEvents();
      }), this.flushInterval);
    }
  }, {
    key: "setupNetworkListener",
    value: function setupNetworkListener() {
      cov_o2aoezpu8().f[22]++;
      cov_o2aoezpu8().s[67]++;
      this.isOnline = true;
    }
  }, {
    key: "storeEventLocally",
    value: (function () {
      var _storeEventLocally = _asyncToGenerator(function* (event) {
        cov_o2aoezpu8().f[23]++;
        cov_o2aoezpu8().s[68]++;
        try {
          var stored = (cov_o2aoezpu8().s[69]++, yield AsyncStorage.getItem('analytics_events'));
          var events = (cov_o2aoezpu8().s[70]++, stored ? (cov_o2aoezpu8().b[25][0]++, JSON.parse(stored)) : (cov_o2aoezpu8().b[25][1]++, []));
          cov_o2aoezpu8().s[71]++;
          events.push(event);
          cov_o2aoezpu8().s[72]++;
          if (events.length > 1000) {
            cov_o2aoezpu8().b[26][0]++;
            cov_o2aoezpu8().s[73]++;
            events.splice(0, events.length - 1000);
          } else {
            cov_o2aoezpu8().b[26][1]++;
          }
          cov_o2aoezpu8().s[74]++;
          yield AsyncStorage.setItem('analytics_events', JSON.stringify(events));
        } catch (error) {
          cov_o2aoezpu8().s[75]++;
          console.warn('Failed to store analytics event locally:', error);
        }
      });
      function storeEventLocally(_x1) {
        return _storeEventLocally.apply(this, arguments);
      }
      return storeEventLocally;
    }())
  }, {
    key: "removeStoredEvents",
    value: (function () {
      var _removeStoredEvents = _asyncToGenerator(function* (eventIds) {
        cov_o2aoezpu8().f[24]++;
        cov_o2aoezpu8().s[76]++;
        try {
          var stored = (cov_o2aoezpu8().s[77]++, yield AsyncStorage.getItem('analytics_events'));
          cov_o2aoezpu8().s[78]++;
          if (!stored) {
            cov_o2aoezpu8().b[27][0]++;
            cov_o2aoezpu8().s[79]++;
            return;
          } else {
            cov_o2aoezpu8().b[27][1]++;
          }
          var events = (cov_o2aoezpu8().s[80]++, JSON.parse(stored));
          var filteredEvents = (cov_o2aoezpu8().s[81]++, events.filter(function (e) {
            cov_o2aoezpu8().f[25]++;
            cov_o2aoezpu8().s[82]++;
            return !eventIds.includes(e.id);
          }));
          cov_o2aoezpu8().s[83]++;
          yield AsyncStorage.setItem('analytics_events', JSON.stringify(filteredEvents));
        } catch (error) {
          cov_o2aoezpu8().s[84]++;
          console.warn('Failed to remove stored analytics events:', error);
        }
      });
      function removeStoredEvents(_x10) {
        return _removeStoredEvents.apply(this, arguments);
      }
      return removeStoredEvents;
    }())
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      cov_o2aoezpu8().f[26]++;
      cov_o2aoezpu8().s[85]++;
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateEventId",
    value: function generateEventId() {
      cov_o2aoezpu8().f[27]++;
      cov_o2aoezpu8().s[86]++;
      return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "getCurrentUserId",
    value: function () {
      var _getCurrentUserId = _asyncToGenerator(function* () {
        cov_o2aoezpu8().f[28]++;
        var _ref15 = (cov_o2aoezpu8().s[87]++, yield supabase.auth.getUser()),
          user = _ref15.data.user;
        cov_o2aoezpu8().s[88]++;
        return (cov_o2aoezpu8().b[28][0]++, user == null ? void 0 : user.id) || (cov_o2aoezpu8().b[28][1]++, 'anonymous');
      });
      function getCurrentUserId() {
        return _getCurrentUserId.apply(this, arguments);
      }
      return getCurrentUserId;
    }()
  }, {
    key: "getPlatform",
    value: function getPlatform() {
      cov_o2aoezpu8().f[29]++;
      cov_o2aoezpu8().s[89]++;
      return 'react-native';
    }
  }, {
    key: "getAppVersion",
    value: function getAppVersion() {
      cov_o2aoezpu8().f[30]++;
      cov_o2aoezpu8().s[90]++;
      return '1.0.0';
    }
  }, {
    key: "getDefaultUserBehaviorMetrics",
    value: function getDefaultUserBehaviorMetrics() {
      cov_o2aoezpu8().f[31]++;
      cov_o2aoezpu8().s[91]++;
      return {
        sessionDuration: 0,
        screenViews: {},
        featureUsage: {},
        conversionFunnels: {},
        retentionRate: 0,
        engagementScore: 0
      };
    }
  }, {
    key: "getDefaultPerformanceMetrics",
    value: function getDefaultPerformanceMetrics() {
      cov_o2aoezpu8().f[32]++;
      cov_o2aoezpu8().s[92]++;
      return {
        appLaunchTime: 0,
        screenLoadTimes: {},
        apiResponseTimes: {},
        errorRates: {},
        crashRate: 0,
        memoryUsage: []
      };
    }
  }, {
    key: "getDefaultBusinessMetrics",
    value: function getDefaultBusinessMetrics() {
      cov_o2aoezpu8().f[33]++;
      cov_o2aoezpu8().s[93]++;
      return {
        subscriptionConversions: 0,
        revenuePerUser: 0,
        churnRate: 0,
        lifetimeValue: 0,
        featureAdoption: {},
        supportTickets: 0
      };
    }
  }, {
    key: "getDefaultPredictiveInsights",
    value: function getDefaultPredictiveInsights() {
      cov_o2aoezpu8().f[34]++;
      cov_o2aoezpu8().s[94]++;
      return {
        churnProbability: 0,
        nextBestAction: 'Continue using the app',
        recommendedFeatures: [],
        optimalEngagementTime: '9:00 AM',
        skillProgressionPrediction: {
          currentLevel: 'beginner',
          predictedLevel: 'intermediate',
          timeToAchieve: 90,
          confidence: 0.7
        }
      };
    }
  }, {
    key: "getDefaultDashboard",
    value: function getDefaultDashboard() {
      cov_o2aoezpu8().f[35]++;
      cov_o2aoezpu8().s[95]++;
      return {
        overview: {
          totalUsers: 0,
          activeUsers: 0,
          sessionCount: 0,
          averageSessionDuration: 0
        },
        userBehavior: this.getDefaultUserBehaviorMetrics(),
        performance: this.getDefaultPerformanceMetrics(),
        business: this.getDefaultBusinessMetrics(),
        insights: this.getDefaultPredictiveInsights(),
        trends: {
          userGrowth: [],
          engagementTrend: [],
          revenueTrend: []
        }
      };
    }
  }, {
    key: "calculateChurnProbability",
    value: function calculateChurnProbability(behavior, events) {
      cov_o2aoezpu8().f[36]++;
      cov_o2aoezpu8().s[96]++;
      return Math.random() * 0.3;
    }
  }, {
    key: "determineNextBestAction",
    value: function determineNextBestAction(behavior, events) {
      cov_o2aoezpu8().f[37]++;
      cov_o2aoezpu8().s[97]++;
      return 'Try the AI coaching feature';
    }
  }, {
    key: "recommendFeatures",
    value: function recommendFeatures(behavior, events) {
      cov_o2aoezpu8().f[38]++;
      cov_o2aoezpu8().s[98]++;
      return ['Video Analysis', 'Match Tracking', 'Social Features'];
    }
  }, {
    key: "calculateOptimalEngagementTime",
    value: function calculateOptimalEngagementTime(events) {
      cov_o2aoezpu8().f[39]++;
      cov_o2aoezpu8().s[99]++;
      return '7:00 PM';
    }
  }, {
    key: "predictSkillProgression",
    value: function () {
      var _predictSkillProgression = _asyncToGenerator(function* (userId) {
        cov_o2aoezpu8().f[40]++;
        cov_o2aoezpu8().s[100]++;
        return {
          currentLevel: 'intermediate',
          predictedLevel: 'advanced',
          timeToAchieve: 120,
          confidence: 0.8
        };
      });
      function predictSkillProgression(_x11) {
        return _predictSkillProgression.apply(this, arguments);
      }
      return predictSkillProgression;
    }()
  }, {
    key: "getUserEvents",
    value: function () {
      var _getUserEvents = _asyncToGenerator(function* (userId, timeRange) {
        cov_o2aoezpu8().f[41]++;
        cov_o2aoezpu8().s[101]++;
        return [];
      });
      function getUserEvents(_x12, _x13) {
        return _getUserEvents.apply(this, arguments);
      }
      return getUserEvents;
    }()
  }, {
    key: "getOverviewMetrics",
    value: function () {
      var _getOverviewMetrics = _asyncToGenerator(function* (timeRange) {
        cov_o2aoezpu8().f[42]++;
        cov_o2aoezpu8().s[102]++;
        return {
          totalUsers: 1000,
          activeUsers: 750,
          sessionCount: 5000,
          averageSessionDuration: 1200
        };
      });
      function getOverviewMetrics(_x14) {
        return _getOverviewMetrics.apply(this, arguments);
      }
      return getOverviewMetrics;
    }()
  }, {
    key: "getAggregatedUserBehavior",
    value: function () {
      var _getAggregatedUserBehavior = _asyncToGenerator(function* (timeRange) {
        cov_o2aoezpu8().f[43]++;
        cov_o2aoezpu8().s[103]++;
        return this.getDefaultUserBehaviorMetrics();
      });
      function getAggregatedUserBehavior(_x15) {
        return _getAggregatedUserBehavior.apply(this, arguments);
      }
      return getAggregatedUserBehavior;
    }()
  }, {
    key: "getTrends",
    value: function () {
      var _getTrends = _asyncToGenerator(function* (timeRange) {
        cov_o2aoezpu8().f[44]++;
        cov_o2aoezpu8().s[104]++;
        return {
          userGrowth: [100, 150, 200, 250, 300],
          engagementTrend: [70, 75, 80, 85, 90],
          revenueTrend: [1000, 1200, 1400, 1600, 1800]
        };
      });
      function getTrends(_x16) {
        return _getTrends.apply(this, arguments);
      }
      return getTrends;
    }()
  }, {
    key: "getAggregatedInsights",
    value: function () {
      var _getAggregatedInsights = _asyncToGenerator(function* () {
        cov_o2aoezpu8().f[45]++;
        cov_o2aoezpu8().s[105]++;
        return this.getDefaultPredictiveInsights();
      });
      function getAggregatedInsights() {
        return _getAggregatedInsights.apply(this, arguments);
      }
      return getAggregatedInsights;
    }()
  }, {
    key: "convertToCSV",
    value: function convertToCSV(data) {
      cov_o2aoezpu8().f[46]++;
      cov_o2aoezpu8().s[106]++;
      return 'CSV data placeholder';
    }
  }]);
}();
export var advancedAnalyticsService = (cov_o2aoezpu8().s[107]++, new AdvancedAnalyticsService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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