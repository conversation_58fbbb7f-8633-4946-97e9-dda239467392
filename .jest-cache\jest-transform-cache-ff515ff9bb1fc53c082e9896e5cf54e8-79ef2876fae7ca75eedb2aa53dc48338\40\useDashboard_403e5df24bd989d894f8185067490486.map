{"version": 3, "names": ["useState", "useEffect", "useCallback", "apiService", "useAuth", "useDashboard", "cov_1bt68itmx4", "f", "_ref", "s", "_ref2", "_slicedToArray", "data", "setData", "_ref3", "_ref4", "loading", "setLoading", "_ref5", "_ref6", "error", "setError", "_ref7", "_ref8", "refreshing", "setRefreshing", "_ref9", "user", "loadDashboardData", "_asyncToGenerator", "isRefresh", "arguments", "length", "undefined", "b", "dashboardData", "getDashboardData", "id", "err", "Error", "message", "console", "refreshData", "generateNewTip", "context", "recentSessions", "slice", "map", "title", "join", "newTip", "generateAITip", "prev", "Object", "assign", "dailyTip", "markNotificationRead", "_ref11", "notificationId", "markNotificationAsRead", "notifications", "notification", "read", "_x", "apply"], "sources": ["useDashboard.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { DashboardData, AITip } from '@/types/database';\nimport { apiService } from '@/services/api';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface UseDashboardReturn {\n  data: DashboardData | null;\n  loading: boolean;\n  error: string | null;\n  refreshing: boolean;\n  refreshData: () => Promise<void>;\n  generateNewTip: () => Promise<void>;\n  markNotificationRead: (notificationId: string) => Promise<void>;\n}\n\nexport function useDashboard(): UseDashboardReturn {\n  const [data, setData] = useState<DashboardData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [refreshing, setRefreshing] = useState(false);\n  const { user } = useAuth();\n\n  const loadDashboardData = useCallback(async (isRefresh = false) => {\n    if (!user) {\n      setLoading(false);\n      return;\n    }\n\n    try {\n      if (isRefresh) {\n        setRefreshing(true);\n      } else {\n        setLoading(true);\n      }\n      setError(null);\n\n      const dashboardData = await apiService.getDashboardData(user.id);\n      setData(dashboardData);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');\n      console.error('Dashboard data loading error:', err);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [user]);\n\n  const refreshData = useCallback(async () => {\n    await loadDashboardData(true);\n  }, [loadDashboardData]);\n\n  const generateNewTip = useCallback(async () => {\n    if (!data || !user) return;\n\n    try {\n      const context = `Recent sessions: ${data.recentSessions.slice(0, 2).map(s => s.title).join(', ')}`;\n      const newTip = await apiService.generateAITip(user.id, context);\n\n      setData(prev => prev ? {\n        ...prev,\n        dailyTip: newTip,\n      } : null);\n    } catch (err) {\n      console.error('Failed to generate new tip:', err);\n    }\n  }, [data, user]);\n\n  const markNotificationRead = useCallback(async (notificationId: string) => {\n    try {\n      await apiService.markNotificationAsRead(notificationId);\n      \n      setData(prev => prev ? {\n        ...prev,\n        notifications: prev.notifications.map(notification =>\n          notification.id === notificationId\n            ? { ...notification, read: true }\n            : notification\n        ),\n      } : null);\n    } catch (err) {\n      console.error('Failed to mark notification as read:', err);\n    }\n  }, []);\n\n  useEffect(() => {\n    loadDashboardData();\n  }, [loadDashboardData]);\n\n  return {\n    data,\n    loading,\n    error,\n    refreshing,\n    refreshData,\n    generateNewTip,\n    markNotificationRead,\n  };\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAExD,SAASC,UAAU;AACnB,SAASC,OAAO;AAYhB,OAAO,SAASC,YAAYA,CAAA,EAAuB;EAAAC,cAAA,GAAAC,CAAA;EACjD,IAAAC,IAAA,IAAAF,cAAA,GAAAG,CAAA,OAAwBT,QAAQ,CAAuB,IAAI,CAAC;IAAAU,KAAA,GAAAC,cAAA,CAAAH,IAAA;IAArDI,IAAI,GAAAF,KAAA;IAAEG,OAAO,GAAAH,KAAA;EACpB,IAAAI,KAAA,IAAAR,cAAA,GAAAG,CAAA,OAA8BT,QAAQ,CAAC,IAAI,CAAC;IAAAe,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAArCE,OAAO,GAAAD,KAAA;IAAEE,UAAU,GAAAF,KAAA;EAC1B,IAAAG,KAAA,IAAAZ,cAAA,GAAAG,CAAA,OAA0BT,QAAQ,CAAgB,IAAI,CAAC;IAAAmB,KAAA,GAAAR,cAAA,CAAAO,KAAA;IAAhDE,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EACtB,IAAAG,KAAA,IAAAhB,cAAA,GAAAG,CAAA,OAAoCT,QAAQ,CAAC,KAAK,CAAC;IAAAuB,KAAA,GAAAZ,cAAA,CAAAW,KAAA;IAA5CE,UAAU,GAAAD,KAAA;IAAEE,aAAa,GAAAF,KAAA;EAChC,IAAAG,KAAA,IAAApB,cAAA,GAAAG,CAAA,OAAiBL,OAAO,CAAC,CAAC;IAAlBuB,IAAI,GAAAD,KAAA,CAAJC,IAAI;EAEZ,IAAMC,iBAAiB,IAAAtB,cAAA,GAAAG,CAAA,OAAGP,WAAW,CAAA2B,iBAAA,CAAC,aAA6B;IAAA,IAAtBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAzB,cAAA,GAAA4B,CAAA,UAAG,KAAK;IAAA5B,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC5D,IAAI,CAACkB,IAAI,EAAE;MAAArB,cAAA,GAAA4B,CAAA;MAAA5B,cAAA,GAAAG,CAAA;MACTQ,UAAU,CAAC,KAAK,CAAC;MAACX,cAAA,GAAAG,CAAA;MAClB;IACF,CAAC;MAAAH,cAAA,GAAA4B,CAAA;IAAA;IAAA5B,cAAA,GAAAG,CAAA;IAED,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACF,IAAIqB,SAAS,EAAE;QAAAxB,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAG,CAAA;QACbgB,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QAAAnB,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAG,CAAA;QACLQ,UAAU,CAAC,IAAI,CAAC;MAClB;MAACX,cAAA,GAAAG,CAAA;MACDY,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAMc,aAAa,IAAA7B,cAAA,GAAAG,CAAA,cAASN,UAAU,CAACiC,gBAAgB,CAACT,IAAI,CAACU,EAAE,CAAC;MAAC/B,cAAA,GAAAG,CAAA;MACjEI,OAAO,CAACsB,aAAa,CAAC;IACxB,CAAC,CAAC,OAAOG,GAAG,EAAE;MAAAhC,cAAA,GAAAG,CAAA;MACZY,QAAQ,CAACiB,GAAG,YAAYC,KAAK,IAAAjC,cAAA,GAAA4B,CAAA,UAAGI,GAAG,CAACE,OAAO,KAAAlC,cAAA,GAAA4B,CAAA,UAAG,+BAA+B,EAAC;MAAC5B,cAAA,GAAAG,CAAA;MAC/EgC,OAAO,CAACrB,KAAK,CAAC,+BAA+B,EAAEkB,GAAG,CAAC;IACrD,CAAC,SAAS;MAAAhC,cAAA,GAAAG,CAAA;MACRQ,UAAU,CAAC,KAAK,CAAC;MAACX,cAAA,GAAAG,CAAA;MAClBgB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,GAAE,CAACE,IAAI,CAAC,CAAC;EAEV,IAAMe,WAAW,IAAApC,cAAA,GAAAG,CAAA,QAAGP,WAAW,CAAA2B,iBAAA,CAAC,aAAY;IAAAvB,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC1C,MAAMmB,iBAAiB,CAAC,IAAI,CAAC;EAC/B,CAAC,GAAE,CAACA,iBAAiB,CAAC,CAAC;EAEvB,IAAMe,cAAc,IAAArC,cAAA,GAAAG,CAAA,QAAGP,WAAW,CAAA2B,iBAAA,CAAC,aAAY;IAAAvB,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC7C,IAAI,CAAAH,cAAA,GAAA4B,CAAA,WAACtB,IAAI,MAAAN,cAAA,GAAA4B,CAAA,UAAI,CAACP,IAAI,GAAE;MAAArB,cAAA,GAAA4B,CAAA;MAAA5B,cAAA,GAAAG,CAAA;MAAA;IAAM,CAAC;MAAAH,cAAA,GAAA4B,CAAA;IAAA;IAAA5B,cAAA,GAAAG,CAAA;IAE3B,IAAI;MACF,IAAMmC,OAAO,IAAAtC,cAAA,GAAAG,CAAA,QAAG,oBAAoBG,IAAI,CAACiC,cAAc,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,UAAAtC,CAAC,EAAI;QAAAH,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAAA,CAAC,CAACuC,KAAK;MAAD,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE;MAClG,IAAMC,MAAM,IAAA5C,cAAA,GAAAG,CAAA,cAASN,UAAU,CAACgD,aAAa,CAACxB,IAAI,CAACU,EAAE,EAAEO,OAAO,CAAC;MAACtC,cAAA,GAAAG,CAAA;MAEhEI,OAAO,CAAC,UAAAuC,IAAI,EAAI;QAAA9C,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QAAA,OAAA2C,IAAI,IAAA9C,cAAA,GAAA4B,CAAA,UAAAmB,MAAA,CAAAC,MAAA,KACfF,IAAI;UACPG,QAAQ,EAAEL;QAAM,OAAA5C,cAAA,GAAA4B,CAAA,UACd,IAAI;MAAD,CAAC,CAAC;IACX,CAAC,CAAC,OAAOI,GAAG,EAAE;MAAAhC,cAAA,GAAAG,CAAA;MACZgC,OAAO,CAACrB,KAAK,CAAC,6BAA6B,EAAEkB,GAAG,CAAC;IACnD;EACF,CAAC,GAAE,CAAC1B,IAAI,EAAEe,IAAI,CAAC,CAAC;EAEhB,IAAM6B,oBAAoB,IAAAlD,cAAA,GAAAG,CAAA,QAAGP,WAAW;IAAA,IAAAuD,MAAA,GAAA5B,iBAAA,CAAC,WAAO6B,cAAsB,EAAK;MAAApD,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACzE,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACF,MAAMN,UAAU,CAACwD,sBAAsB,CAACD,cAAc,CAAC;QAACpD,cAAA,GAAAG,CAAA;QAExDI,OAAO,CAAC,UAAAuC,IAAI,EAAI;UAAA9C,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAG,CAAA;UAAA,OAAA2C,IAAI,IAAA9C,cAAA,GAAA4B,CAAA,UAAAmB,MAAA,CAAAC,MAAA,KACfF,IAAI;YACPQ,aAAa,EAAER,IAAI,CAACQ,aAAa,CAACb,GAAG,CAAC,UAAAc,YAAY,EAChD;cAAAvD,cAAA,GAAAC,CAAA;cAAAD,cAAA,GAAAG,CAAA;cAAA,OAAAoD,YAAY,CAACxB,EAAE,KAAKqB,cAAc,IAAApD,cAAA,GAAA4B,CAAA,UAAAmB,MAAA,CAAAC,MAAA,KACzBO,YAAY;gBAAEC,IAAI,EAAE;cAAI,OAAAxD,cAAA,GAAA4B,CAAA,UAC7B2B,YAAY;YAAD,CACjB;UAAC,OAAAvD,cAAA,GAAA4B,CAAA,UACC,IAAI;QAAD,CAAC,CAAC;MACX,CAAC,CAAC,OAAOI,GAAG,EAAE;QAAAhC,cAAA,GAAAG,CAAA;QACZgC,OAAO,CAACrB,KAAK,CAAC,sCAAsC,EAAEkB,GAAG,CAAC;MAC5D;IACF,CAAC;IAAA,iBAAAyB,EAAA;MAAA,OAAAN,MAAA,CAAAO,KAAA,OAAAjC,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAACzB,cAAA,GAAAG,CAAA;EAEPR,SAAS,CAAC,YAAM;IAAAK,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACdmB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACA,iBAAiB,CAAC,CAAC;EAACtB,cAAA,GAAAG,CAAA;EAExB,OAAO;IACLG,IAAI,EAAJA,IAAI;IACJI,OAAO,EAAPA,OAAO;IACPI,KAAK,EAALA,KAAK;IACLI,UAAU,EAAVA,UAAU;IACVkB,WAAW,EAAXA,WAAW;IACXC,cAAc,EAAdA,cAAc;IACda,oBAAoB,EAApBA;EACF,CAAC;AACH", "ignoreList": []}