{"name": "acemind-tennis-app", "main": "expo-router/entry", "version": "1.0.0", "private": true, "scripts": {"setup": "node scripts/setup-project.js", "dev": "expo start", "dev:clear": "expo start --clear", "dev:tunnel": "expo start --tunnel", "dev:no-telemetry": "cross-env EXPO_NO_TELEMETRY=1 expo start", "build:web": "expo export --platform web", "build:web:prod": "cross-env NODE_ENV=production expo export --platform web", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "type-check": "tsc --noEmit", "test": "jest", "test:unit": "jest --testPathPattern=__tests__ --testPathIgnorePatterns=integration,e2e", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:performance": "jest --testPathPattern=performance", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests", "test:smoke:staging": "jest --testPathPattern=smoke --testNamePattern=staging", "test:smoke:production": "jest --testPathPattern=smoke --testNamePattern=production", "test:security": "npm run security:audit && npm run validate:security", "test:technical-debt": "npm run lint && npm run type-check && npm run test:coverage", "type-check:strict": "tsc --noEmit --strict", "format:check": "prettier --check .", "format:fix": "prettier --write .", "prebuild": "expo prebuild --clean", "prebuild:ios": "expo prebuild --platform ios --clean", "prebuild:android": "expo prebuild --platform android --clean", "build:development": "eas build --profile development", "build:staging": "eas build --profile staging", "build:preview": "eas build --profile preview", "build:production": "eas build --profile production", "build:ios:dev": "eas build --platform ios --profile development", "build:ios:staging": "eas build --platform ios --profile staging", "build:ios:prod": "eas build --platform ios --profile production", "build:android:dev": "eas build --platform android --profile development", "build:android:staging": "eas build --platform android --profile staging", "build:android:prod": "eas build --platform android --profile production", "start:ci": "expo start --web --port 3000", "submit:staging": "eas submit --profile staging", "submit:production": "eas submit --profile production", "submit:ios:staging": "eas submit --platform ios --profile staging", "submit:ios:prod": "eas submit --platform ios --profile production", "submit:android:staging": "eas submit --platform android --profile staging", "submit:android:prod": "eas submit --platform android --profile production", "update:staging": "eas update --branch staging --message", "update:production": "eas update --branch production --message", "validate:config": "node scripts/validate-config.js", "validate:env": "node scripts/validate-environment.js", "validate:deployment": "node scripts/validate-deployment.js", "validate:security": "node scripts/validate-security.js", "security:audit": "npm audit && npx audit-ci --moderate", "security:scan": "npx trivy fs .", "setup:env": "node scripts/setup-environment.js", "db:migrate": "node scripts/migrate.js", "db:migrate:test": "NODE_ENV=test node scripts/migrate.js", "deploy:staging": "npm run validate:env && npm run test:ci && npm run build:staging", "deploy:production": "npm run validate:env && npm run test:ci && npm run build:production", "setup-demo": "node scripts/setup-demo.js", "test-phase1": "node scripts/test-phase1-foundation.js", "test-phase2": "node scripts/test-phase2-advanced.js", "test-phase3a": "node scripts/test-phase3a-ai-optimization.js", "test-phase3b": "node scripts/test-phase3b-edge-optimization.js", "test-phase3c": "node scripts/test-phase3c-native-optimization.js", "test-monitoring": "node scripts/test-comprehensive-monitoring.js", "test-all-phases": "npm run test-phase1 && npm run test-phase2 && npm run test-phase3a && npm run test-phase3b && npm run test-phase3c && npm run test-monitoring", "demo": "npm run setup-demo && npm run dev"}, "dependencies": {"@expo-google-fonts/inter": "^0.2.3", "@expo/vector-icons": "^14.1.0", "@lucide/lab": "^0.1.2", "@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/control_utils": "^0.6.1675466023", "@mediapipe/drawing_utils": "^0.3.1675466124", "@mediapipe/pose": "^0.5.1675469404", "@react-native-async-storage/async-storage": "^1.21.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@supabase/supabase-js": "^2.50.0", "base64-arraybuffer": "^1.0.2", "expo": "^53.0.0", "expo-battery": "^9.1.4", "expo-blur": "~14.1.3", "expo-camera": "~16.1.5", "expo-constants": "~17.1.3", "expo-document-picker": "~12.0.2", "expo-file-system": "~18.1.1", "expo-font": "~13.2.2", "expo-haptics": "~14.1.3", "expo-image": "~2.1.0", "expo-image-picker": "~15.0.7", "expo-linear-gradient": "~14.1.3", "expo-linking": "~7.1.3", "expo-local-authentication": "~14.0.1", "expo-localization": "~16.1.0", "expo-location": "^18.1.5", "expo-media-library": "~16.0.4", "expo-network": "^7.1.5", "expo-notifications": "~0.28.16", "expo-router": "~5.0.2", "expo-secure-store": "~13.0.2", "expo-splash-screen": "~0.30.6", "expo-status-bar": "~2.2.2", "expo-symbols": "~0.4.3", "expo-system-ui": "~5.0.5", "expo-web-browser": "~14.1.5", "i18n-js": "^4.3.2", "lucide-react-native": "^0.475.0", "openai": "^4.104.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.1", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.3.0", "react-native-screens": "~4.10.0", "react-native-share": "^10.0.2", "react-native-svg": "15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@expo/cli": "latest", "@react-native-community/netinfo": "^11.3.1", "@sentry/react-native": "^5.15.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^12.4.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dotenv": "^16.3.1", "eas-cli": "latest", "expo-build-properties": "~0.12.5", "expo-device": "~6.0.2", "expo-sharing": "~12.0.1", "jest": "^29.7.0", "jest-expo": "^51.0.0", "jest-junit": "^16.0.0", "nativewind": "^2.0.11", "react-test-renderer": "19.0.0", "tailwindcss": "^3.3.0", "typescript": "~5.8.3", "zod": "^3.22.4"}}