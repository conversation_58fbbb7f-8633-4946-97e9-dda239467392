08a071caee721dbf6d6e6fd3a756cd4f
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
exports.__esModule = true;
exports.default = void 0;
var EventEmitter = function () {
  function EventEmitter() {
    (0, _classCallCheck2.default)(this, EventEmitter);
    this._registry = {};
  }
  return (0, _createClass2.default)(EventEmitter, [{
    key: "addListener",
    value: function addListener(eventType, listener, context) {
      var registrations = allocate(this._registry, eventType);
      var registration = {
        context: context,
        listener: listener,
        remove: function remove() {
          registrations.delete(registration);
        }
      };
      registrations.add(registration);
      return registration;
    }
  }, {
    key: "emit",
    value: function emit(eventType) {
      var registrations = this._registry[eventType];
      if (registrations != null) {
        for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
          args[_key - 1] = arguments[_key];
        }
        for (var _i = 0, _arr = (0, _toConsumableArray2.default)(registrations); _i < _arr.length; _i++) {
          var registration = _arr[_i];
          registration.listener.apply(registration.context, args);
        }
      }
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners(eventType) {
      if (eventType == null) {
        this._registry = {};
      } else {
        delete this._registry[eventType];
      }
    }
  }, {
    key: "listenerCount",
    value: function listenerCount(eventType) {
      var registrations = this._registry[eventType];
      return registrations == null ? 0 : registrations.size;
    }
  }]);
}();
exports.default = EventEmitter;
function allocate(registry, eventType) {
  var registrations = registry[eventType];
  if (registrations == null) {
    registrations = new Set();
    registry[eventType] = registrations;
  }
  return registrations;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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