2f2df19605d76fc7d6462196f1016b44
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2cg6qb7uk2() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\storage\\FileUploadService.ts";
  var hash = "5408b4e56abbf03f5505110dd827b5c43016fdfc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\storage\\FileUploadService.ts",
    statementMap: {
      "0": {
        start: {
          line: 35,
          column: 36
        },
        end: {
          line: 35,
          column: 50
        }
      },
      "1": {
        start: {
          line: 36,
          column: 35
        },
        end: {
          line: 36,
          column: 52
        }
      },
      "2": {
        start: {
          line: 37,
          column: 43
        },
        end: {
          line: 37,
          column: 64
        }
      },
      "3": {
        start: {
          line: 38,
          column: 43
        },
        end: {
          line: 38,
          column: 73
        }
      },
      "4": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 181,
          column: 5
        }
      },
      "5": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 47
        }
      },
      "6": {
        start: {
          line: 50,
          column: 43
        },
        end: {
          line: 56,
          column: 7
        }
      },
      "7": {
        start: {
          line: 59,
          column: 25
        },
        end: {
          line: 59,
          column: 72
        }
      },
      "8": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 62,
          column: 7
        }
      },
      "9": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 55
        }
      },
      "10": {
        start: {
          line: 64,
          column: 23
        },
        end: {
          line: 64,
          column: 53
        }
      },
      "11": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 71,
          column: 9
        }
      },
      "12": {
        start: {
          line: 74,
          column: 23
        },
        end: {
          line: 74,
          column: 77
        }
      },
      "13": {
        start: {
          line: 75,
          column: 23
        },
        end: {
          line: 75,
          column: 94
        }
      },
      "14": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 78,
          column: 66
        }
      },
      "15": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 81,
          column: 7
        }
      },
      "16": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 80,
          column: 78
        }
      },
      "17": {
        start: {
          line: 84,
          column: 6
        },
        end: {
          line: 88,
          column: 9
        }
      },
      "18": {
        start: {
          line: 90,
          column: 25
        },
        end: {
          line: 92,
          column: 8
        }
      },
      "19": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 98,
          column: 9
        }
      },
      "20": {
        start: {
          line: 101,
          column: 30
        },
        end: {
          line: 101,
          column: 48
        }
      },
      "21": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 107,
          column: 9
        }
      },
      "22": {
        start: {
          line: 110,
          column: 27
        },
        end: {
          line: 110,
          column: 28
        }
      },
      "23": {
        start: {
          line: 111,
          column: 26
        },
        end: {
          line: 111,
          column: 27
        }
      },
      "24": {
        start: {
          line: 112,
          column: 29
        },
        end: {
          line: 112,
          column: 33
        }
      },
      "25": {
        start: {
          line: 113,
          column: 28
        },
        end: {
          line: 113,
          column: 32
        }
      },
      "26": {
        start: {
          line: 115,
          column: 6
        },
        end: {
          line: 145,
          column: 7
        }
      },
      "27": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 144,
          column: 9
        }
      },
      "28": {
        start: {
          line: 117,
          column: 34
        },
        end: {
          line: 122,
          column: 14
        }
      },
      "29": {
        start: {
          line: 124,
          column: 10
        },
        end: {
          line: 136,
          column: 11
        }
      },
      "30": {
        start: {
          line: 125,
          column: 12
        },
        end: {
          line: 125,
          column: 32
        }
      },
      "31": {
        start: {
          line: 126,
          column: 12
        },
        end: {
          line: 126,
          column: 29
        }
      },
      "32": {
        start: {
          line: 128,
          column: 12
        },
        end: {
          line: 132,
          column: 13
        }
      },
      "33": {
        start: {
          line: 129,
          column: 14
        },
        end: {
          line: 129,
          column: 83
        }
      },
      "34": {
        start: {
          line: 130,
          column: 14
        },
        end: {
          line: 130,
          column: 87
        }
      },
      "35": {
        start: {
          line: 130,
          column: 43
        },
        end: {
          line: 130,
          column: 85
        }
      },
      "36": {
        start: {
          line: 131,
          column: 14
        },
        end: {
          line: 131,
          column: 23
        }
      },
      "37": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 134,
          column: 30
        }
      },
      "38": {
        start: {
          line: 135,
          column: 12
        },
        end: {
          line: 135,
          column: 18
        }
      },
      "39": {
        start: {
          line: 138,
          column: 10
        },
        end: {
          line: 138,
          column: 30
        }
      },
      "40": {
        start: {
          line: 139,
          column: 10
        },
        end: {
          line: 139,
          column: 27
        }
      },
      "41": {
        start: {
          line: 141,
          column: 10
        },
        end: {
          line: 143,
          column: 11
        }
      },
      "42": {
        start: {
          line: 142,
          column: 12
        },
        end: {
          line: 142,
          column: 85
        }
      },
      "43": {
        start: {
          line: 142,
          column: 41
        },
        end: {
          line: 142,
          column: 83
        }
      },
      "44": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 150,
          column: 7
        }
      },
      "45": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 148,
          column: 75
        }
      },
      "46": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 149,
          column: 111
        }
      },
      "47": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 156,
          column: 9
        }
      },
      "48": {
        start: {
          line: 159,
          column: 32
        },
        end: {
          line: 161,
          column: 38
        }
      },
      "49": {
        start: {
          line: 163,
          column: 35
        },
        end: {
          line: 168,
          column: 7
        }
      },
      "50": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 174,
          column: 9
        }
      },
      "51": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 176,
          column: 45
        }
      },
      "52": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 43
        }
      },
      "53": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 179,
          column: 53
        }
      },
      "54": {
        start: {
          line: 180,
          column: 6
        },
        end: {
          line: 180,
          column: 61
        }
      },
      "55": {
        start: {
          line: 191,
          column: 4
        },
        end: {
          line: 250,
          column: 5
        }
      },
      "56": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 47
        }
      },
      "57": {
        start: {
          line: 194,
          column: 43
        },
        end: {
          line: 200,
          column: 7
        }
      },
      "58": {
        start: {
          line: 203,
          column: 25
        },
        end: {
          line: 203,
          column: 72
        }
      },
      "59": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 206,
          column: 7
        }
      },
      "60": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 205,
          column: 55
        }
      },
      "61": {
        start: {
          line: 209,
          column: 23
        },
        end: {
          line: 209,
          column: 77
        }
      },
      "62": {
        start: {
          line: 210,
          column: 23
        },
        end: {
          line: 210,
          column: 94
        }
      },
      "63": {
        start: {
          line: 213,
          column: 25
        },
        end: {
          line: 215,
          column: 8
        }
      },
      "64": {
        start: {
          line: 218,
          column: 30
        },
        end: {
          line: 218,
          column: 48
        }
      },
      "65": {
        start: {
          line: 221,
          column: 30
        },
        end: {
          line: 226,
          column: 10
        }
      },
      "66": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 231,
          column: 7
        }
      },
      "67": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 229,
          column: 55
        }
      },
      "68": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 52
        }
      },
      "69": {
        start: {
          line: 234,
          column: 32
        },
        end: {
          line: 236,
          column: 32
        }
      },
      "70": {
        start: {
          line: 238,
          column: 35
        },
        end: {
          line: 243,
          column: 7
        }
      },
      "71": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 45
        }
      },
      "72": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 246,
          column: 43
        }
      },
      "73": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 53
        }
      },
      "74": {
        start: {
          line: 249,
          column: 6
        },
        end: {
          line: 249,
          column: 61
        }
      },
      "75": {
        start: {
          line: 261,
          column: 4
        },
        end: {
          line: 275,
          column: 5
        }
      },
      "76": {
        start: {
          line: 262,
          column: 23
        },
        end: {
          line: 262,
          column: 51
        }
      },
      "77": {
        start: {
          line: 263,
          column: 43
        },
        end: {
          line: 269,
          column: 7
        }
      },
      "78": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 271,
          column: 65
        }
      },
      "79": {
        start: {
          line: 273,
          column: 6
        },
        end: {
          line: 273,
          column: 57
        }
      },
      "80": {
        start: {
          line: 274,
          column: 6
        },
        end: {
          line: 274,
          column: 65
        }
      },
      "81": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 299,
          column: 5
        }
      },
      "82": {
        start: {
          line: 286,
          column: 24
        },
        end: {
          line: 288,
          column: 27
        }
      },
      "83": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 293,
          column: 7
        }
      },
      "84": {
        start: {
          line: 291,
          column: 8
        },
        end: {
          line: 291,
          column: 53
        }
      },
      "85": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 292,
          column: 40
        }
      },
      "86": {
        start: {
          line: 295,
          column: 6
        },
        end: {
          line: 295,
          column: 29
        }
      },
      "87": {
        start: {
          line: 297,
          column: 6
        },
        end: {
          line: 297,
          column: 51
        }
      },
      "88": {
        start: {
          line: 298,
          column: 6
        },
        end: {
          line: 298,
          column: 48
        }
      },
      "89": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 323,
          column: 5
        }
      },
      "90": {
        start: {
          line: 310,
          column: 30
        },
        end: {
          line: 312,
          column: 23
        }
      },
      "91": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 317,
          column: 7
        }
      },
      "92": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 315,
          column: 57
        }
      },
      "93": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 316,
          column: 52
        }
      },
      "94": {
        start: {
          line: 319,
          column: 6
        },
        end: {
          line: 319,
          column: 35
        }
      },
      "95": {
        start: {
          line: 321,
          column: 6
        },
        end: {
          line: 321,
          column: 55
        }
      },
      "96": {
        start: {
          line: 322,
          column: 6
        },
        end: {
          line: 322,
          column: 62
        }
      },
      "97": {
        start: {
          line: 334,
          column: 4
        },
        end: {
          line: 348,
          column: 5
        }
      },
      "98": {
        start: {
          line: 335,
          column: 30
        },
        end: {
          line: 337,
          column: 45
        }
      },
      "99": {
        start: {
          line: 339,
          column: 6
        },
        end: {
          line: 342,
          column: 7
        }
      },
      "100": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 340,
          column: 59
        }
      },
      "101": {
        start: {
          line: 341,
          column: 8
        },
        end: {
          line: 341,
          column: 52
        }
      },
      "102": {
        start: {
          line: 344,
          column: 6
        },
        end: {
          line: 344,
          column: 51
        }
      },
      "103": {
        start: {
          line: 346,
          column: 6
        },
        end: {
          line: 346,
          column: 57
        }
      },
      "104": {
        start: {
          line: 347,
          column: 6
        },
        end: {
          line: 347,
          column: 66
        }
      },
      "105": {
        start: {
          line: 358,
          column: 4
        },
        end: {
          line: 384,
          column: 5
        }
      },
      "106": {
        start: {
          line: 360,
          column: 23
        },
        end: {
          line: 360,
          column: 61
        }
      },
      "107": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 363,
          column: 7
        }
      },
      "108": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 362,
          column: 48
        }
      },
      "109": {
        start: {
          line: 366,
          column: 6
        },
        end: {
          line: 369,
          column: 7
        }
      },
      "110": {
        start: {
          line: 367,
          column: 26
        },
        end: {
          line: 367,
          column: 74
        }
      },
      "111": {
        start: {
          line: 368,
          column: 8
        },
        end: {
          line: 368,
          column: 67
        }
      },
      "112": {
        start: {
          line: 372,
          column: 24
        },
        end: {
          line: 372,
          column: 54
        }
      },
      "113": {
        start: {
          line: 373,
          column: 22
        },
        end: {
          line: 373,
          column: 82
        }
      },
      "114": {
        start: {
          line: 374,
          column: 22
        },
        end: {
          line: 374,
          column: 82
        }
      },
      "115": {
        start: {
          line: 376,
          column: 6
        },
        end: {
          line: 378,
          column: 7
        }
      },
      "116": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 377,
          column: 50
        }
      },
      "117": {
        start: {
          line: 380,
          column: 6
        },
        end: {
          line: 380,
          column: 26
        }
      },
      "118": {
        start: {
          line: 382,
          column: 6
        },
        end: {
          line: 382,
          column: 53
        }
      },
      "119": {
        start: {
          line: 383,
          column: 6
        },
        end: {
          line: 383,
          column: 50
        }
      },
      "120": {
        start: {
          line: 391,
          column: 22
        },
        end: {
          line: 391,
          column: 32
        }
      },
      "121": {
        start: {
          line: 392,
          column: 19
        },
        end: {
          line: 392,
          column: 62
        }
      },
      "122": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 393,
          column: 49
        }
      },
      "123": {
        start: {
          line: 400,
          column: 18
        },
        end: {
          line: 400,
          column: 32
        }
      },
      "124": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 401,
          column: 41
        }
      },
      "125": {
        start: {
          line: 411,
          column: 4
        },
        end: {
          line: 419,
          column: 5
        }
      },
      "126": {
        start: {
          line: 414,
          column: 6
        },
        end: {
          line: 414,
          column: 74
        }
      },
      "127": {
        start: {
          line: 415,
          column: 6
        },
        end: {
          line: 415,
          column: 45
        }
      },
      "128": {
        start: {
          line: 417,
          column: 6
        },
        end: {
          line: 417,
          column: 55
        }
      },
      "129": {
        start: {
          line: 418,
          column: 6
        },
        end: {
          line: 418,
          column: 63
        }
      },
      "130": {
        start: {
          line: 429,
          column: 4
        },
        end: {
          line: 437,
          column: 5
        }
      },
      "131": {
        start: {
          line: 432,
          column: 6
        },
        end: {
          line: 432,
          column: 81
        }
      },
      "132": {
        start: {
          line: 433,
          column: 6
        },
        end: {
          line: 433,
          column: 45
        }
      },
      "133": {
        start: {
          line: 435,
          column: 6
        },
        end: {
          line: 435,
          column: 58
        }
      },
      "134": {
        start: {
          line: 436,
          column: 6
        },
        end: {
          line: 436,
          column: 67
        }
      },
      "135": {
        start: {
          line: 447,
          column: 4
        },
        end: {
          line: 447,
          column: 28
        }
      },
      "136": {
        start: {
          line: 447,
          column: 21
        },
        end: {
          line: 447,
          column: 28
        }
      },
      "137": {
        start: {
          line: 449,
          column: 17
        },
        end: {
          line: 449,
          column: 18
        }
      },
      "138": {
        start: {
          line: 450,
          column: 21
        },
        end: {
          line: 463,
          column: 11
        }
      },
      "139": {
        start: {
          line: 451,
          column: 6
        },
        end: {
          line: 451,
          column: 32
        }
      },
      "140": {
        start: {
          line: 452,
          column: 25
        },
        end: {
          line: 452,
          column: 66
        }
      },
      "141": {
        start: {
          line: 454,
          column: 6
        },
        end: {
          line: 458,
          column: 9
        }
      },
      "142": {
        start: {
          line: 460,
          column: 6
        },
        end: {
          line: 462,
          column: 7
        }
      },
      "143": {
        start: {
          line: 461,
          column: 8
        },
        end: {
          line: 461,
          column: 32
        }
      },
      "144": {
        start: {
          line: 468,
          column: 33
        },
        end: {
          line: 468,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 43,
            column: 2
          },
          end: {
            line: 43,
            column: 3
          }
        },
        loc: {
          start: {
            line: 46,
            column: 66
          },
          end: {
            line: 182,
            column: 3
          }
        },
        line: 46
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 130,
            column: 32
          },
          end: {
            line: 130,
            column: 33
          }
        },
        loc: {
          start: {
            line: 130,
            column: 43
          },
          end: {
            line: 130,
            column: 85
          }
        },
        line: 130
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 142,
            column: 30
          },
          end: {
            line: 142,
            column: 31
          }
        },
        loc: {
          start: {
            line: 142,
            column: 41
          },
          end: {
            line: 142,
            column: 83
          }
        },
        line: 142
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 187,
            column: 2
          },
          end: {
            line: 187,
            column: 3
          }
        },
        loc: {
          start: {
            line: 190,
            column: 66
          },
          end: {
            line: 251,
            column: 3
          }
        },
        line: 190
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 256,
            column: 2
          },
          end: {
            line: 256,
            column: 3
          }
        },
        loc: {
          start: {
            line: 260,
            column: 66
          },
          end: {
            line: 276,
            column: 3
          }
        },
        line: 260
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 281,
            column: 2
          },
          end: {
            line: 281,
            column: 3
          }
        },
        loc: {
          start: {
            line: 284,
            column: 39
          },
          end: {
            line: 300,
            column: 3
          }
        },
        line: 284
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 305,
            column: 2
          },
          end: {
            line: 305,
            column: 3
          }
        },
        loc: {
          start: {
            line: 308,
            column: 57
          },
          end: {
            line: 324,
            column: 3
          }
        },
        line: 308
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 329,
            column: 2
          },
          end: {
            line: 329,
            column: 3
          }
        },
        loc: {
          start: {
            line: 333,
            column: 60
          },
          end: {
            line: 349,
            column: 3
          }
        },
        line: 333
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 354,
            column: 2
          },
          end: {
            line: 354,
            column: 3
          }
        },
        loc: {
          start: {
            line: 357,
            column: 65
          },
          end: {
            line: 385,
            column: 3
          }
        },
        line: 357
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 390,
            column: 2
          },
          end: {
            line: 390,
            column: 3
          }
        },
        loc: {
          start: {
            line: 390,
            column: 54
          },
          end: {
            line: 394,
            column: 3
          }
        },
        line: 390
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 399,
            column: 2
          },
          end: {
            line: 399,
            column: 3
          }
        },
        loc: {
          start: {
            line: 399,
            column: 48
          },
          end: {
            line: 402,
            column: 3
          }
        },
        line: 399
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 407,
            column: 2
          },
          end: {
            line: 407,
            column: 3
          }
        },
        loc: {
          start: {
            line: 410,
            column: 60
          },
          end: {
            line: 420,
            column: 3
          }
        },
        line: 410
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 425,
            column: 2
          },
          end: {
            line: 425,
            column: 3
          }
        },
        loc: {
          start: {
            line: 428,
            column: 60
          },
          end: {
            line: 438,
            column: 3
          }
        },
        line: 428
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 443,
            column: 2
          },
          end: {
            line: 443,
            column: 3
          }
        },
        loc: {
          start: {
            line: 446,
            column: 10
          },
          end: {
            line: 464,
            column: 3
          }
        },
        line: 446
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 450,
            column: 33
          },
          end: {
            line: 450,
            column: 34
          }
        },
        loc: {
          start: {
            line: 450,
            column: 39
          },
          end: {
            line: 463,
            column: 5
          }
        },
        line: 450
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 45,
            column: 4
          },
          end: {
            line: 45,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 45,
            column: 38
          },
          end: {
            line: 45,
            column: 40
          }
        }],
        line: 45
      },
      "1": {
        loc: {
          start: {
            line: 60,
            column: 6
          },
          end: {
            line: 62,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 60,
            column: 6
          },
          end: {
            line: 62,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 60
      },
      "2": {
        loc: {
          start: {
            line: 64,
            column: 23
          },
          end: {
            line: 64,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 64,
            column: 23
          },
          end: {
            line: 64,
            column: 48
          }
        }, {
          start: {
            line: 64,
            column: 52
          },
          end: {
            line: 64,
            column: 53
          }
        }],
        line: 64
      },
      "3": {
        loc: {
          start: {
            line: 74,
            column: 23
          },
          end: {
            line: 74,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 23
          },
          end: {
            line: 74,
            column: 45
          }
        }, {
          start: {
            line: 74,
            column: 49
          },
          end: {
            line: 74,
            column: 77
          }
        }],
        line: 74
      },
      "4": {
        loc: {
          start: {
            line: 75,
            column: 23
          },
          end: {
            line: 75,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 75,
            column: 46
          },
          end: {
            line: 75,
            column: 83
          }
        }, {
          start: {
            line: 75,
            column: 86
          },
          end: {
            line: 75,
            column: 94
          }
        }],
        line: 75
      },
      "5": {
        loc: {
          start: {
            line: 79,
            column: 6
          },
          end: {
            line: 81,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 6
          },
          end: {
            line: 81,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "6": {
        loc: {
          start: {
            line: 124,
            column: 10
          },
          end: {
            line: 136,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 10
          },
          end: {
            line: 136,
            column: 11
          }
        }, {
          start: {
            line: 133,
            column: 17
          },
          end: {
            line: 136,
            column: 11
          }
        }],
        line: 124
      },
      "7": {
        loc: {
          start: {
            line: 128,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 128,
            column: 12
          },
          end: {
            line: 132,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 128
      },
      "8": {
        loc: {
          start: {
            line: 141,
            column: 10
          },
          end: {
            line: 143,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 141,
            column: 10
          },
          end: {
            line: 143,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 141
      },
      "9": {
        loc: {
          start: {
            line: 147,
            column: 6
          },
          end: {
            line: 150,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 6
          },
          end: {
            line: 150,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "10": {
        loc: {
          start: {
            line: 147,
            column: 10
          },
          end: {
            line: 147,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 147,
            column: 10
          },
          end: {
            line: 147,
            column: 21
          }
        }, {
          start: {
            line: 147,
            column: 25
          },
          end: {
            line: 147,
            column: 36
          }
        }],
        line: 147
      },
      "11": {
        loc: {
          start: {
            line: 149,
            column: 36
          },
          end: {
            line: 149,
            column: 108
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 149,
            column: 36
          },
          end: {
            line: 149,
            column: 56
          }
        }, {
          start: {
            line: 149,
            column: 60
          },
          end: {
            line: 149,
            column: 108
          }
        }],
        line: 149
      },
      "12": {
        loc: {
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 189,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 189,
            column: 38
          },
          end: {
            line: 189,
            column: 40
          }
        }],
        line: 189
      },
      "13": {
        loc: {
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 206,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 206,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "14": {
        loc: {
          start: {
            line: 209,
            column: 23
          },
          end: {
            line: 209,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 23
          },
          end: {
            line: 209,
            column: 45
          }
        }, {
          start: {
            line: 209,
            column: 49
          },
          end: {
            line: 209,
            column: 77
          }
        }],
        line: 209
      },
      "15": {
        loc: {
          start: {
            line: 210,
            column: 23
          },
          end: {
            line: 210,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 210,
            column: 46
          },
          end: {
            line: 210,
            column: 83
          }
        }, {
          start: {
            line: 210,
            column: 86
          },
          end: {
            line: 210,
            column: 94
          }
        }],
        line: 210
      },
      "16": {
        loc: {
          start: {
            line: 228,
            column: 6
          },
          end: {
            line: 231,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 6
          },
          end: {
            line: 231,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "17": {
        loc: {
          start: {
            line: 259,
            column: 4
          },
          end: {
            line: 259,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 259,
            column: 38
          },
          end: {
            line: 259,
            column: 40
          }
        }],
        line: 259
      },
      "18": {
        loc: {
          start: {
            line: 283,
            column: 4
          },
          end: {
            line: 283,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 283,
            column: 21
          },
          end: {
            line: 283,
            column: 40
          }
        }],
        line: 283
      },
      "19": {
        loc: {
          start: {
            line: 290,
            column: 6
          },
          end: {
            line: 293,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 6
          },
          end: {
            line: 293,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "20": {
        loc: {
          start: {
            line: 307,
            column: 4
          },
          end: {
            line: 307,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 307,
            column: 21
          },
          end: {
            line: 307,
            column: 40
          }
        }],
        line: 307
      },
      "21": {
        loc: {
          start: {
            line: 314,
            column: 6
          },
          end: {
            line: 317,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 314,
            column: 6
          },
          end: {
            line: 317,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 314
      },
      "22": {
        loc: {
          start: {
            line: 331,
            column: 4
          },
          end: {
            line: 331,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 331,
            column: 24
          },
          end: {
            line: 331,
            column: 28
          }
        }],
        line: 331
      },
      "23": {
        loc: {
          start: {
            line: 332,
            column: 4
          },
          end: {
            line: 332,
            column: 40
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 332,
            column: 21
          },
          end: {
            line: 332,
            column: 40
          }
        }],
        line: 332
      },
      "24": {
        loc: {
          start: {
            line: 339,
            column: 6
          },
          end: {
            line: 342,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 339,
            column: 6
          },
          end: {
            line: 342,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 339
      },
      "25": {
        loc: {
          start: {
            line: 361,
            column: 6
          },
          end: {
            line: 363,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 361,
            column: 6
          },
          end: {
            line: 363,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 361
      },
      "26": {
        loc: {
          start: {
            line: 366,
            column: 6
          },
          end: {
            line: 369,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 366,
            column: 6
          },
          end: {
            line: 369,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 366
      },
      "27": {
        loc: {
          start: {
            line: 366,
            column: 10
          },
          end: {
            line: 366,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 366,
            column: 10
          },
          end: {
            line: 366,
            column: 30
          }
        }, {
          start: {
            line: 366,
            column: 34
          },
          end: {
            line: 366,
            column: 47
          }
        }, {
          start: {
            line: 366,
            column: 51
          },
          end: {
            line: 366,
            column: 87
          }
        }],
        line: 366
      },
      "28": {
        loc: {
          start: {
            line: 376,
            column: 6
          },
          end: {
            line: 378,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 376,
            column: 6
          },
          end: {
            line: 378,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 376
      },
      "29": {
        loc: {
          start: {
            line: 376,
            column: 10
          },
          end: {
            line: 376,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 376,
            column: 10
          },
          end: {
            line: 376,
            column: 18
          }
        }, {
          start: {
            line: 376,
            column: 22
          },
          end: {
            line: 376,
            column: 30
          }
        }],
        line: 376
      },
      "30": {
        loc: {
          start: {
            line: 401,
            column: 11
          },
          end: {
            line: 401,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 401,
            column: 11
          },
          end: {
            line: 401,
            column: 34
          }
        }, {
          start: {
            line: 401,
            column: 38
          },
          end: {
            line: 401,
            column: 40
          }
        }],
        line: 401
      },
      "31": {
        loc: {
          start: {
            line: 409,
            column: 4
          },
          end: {
            line: 409,
            column: 49
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 409,
            column: 41
          },
          end: {
            line: 409,
            column: 49
          }
        }],
        line: 409
      },
      "32": {
        loc: {
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 427,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 427,
            column: 26
          },
          end: {
            line: 427,
            column: 27
          }
        }],
        line: 427
      },
      "33": {
        loc: {
          start: {
            line: 445,
            column: 4
          },
          end: {
            line: 445,
            column: 31
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 445,
            column: 24
          },
          end: {
            line: 445,
            column: 31
          }
        }],
        line: 445
      },
      "34": {
        loc: {
          start: {
            line: 447,
            column: 4
          },
          end: {
            line: 447,
            column: 28
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 447,
            column: 4
          },
          end: {
            line: 447,
            column: 28
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 447
      },
      "35": {
        loc: {
          start: {
            line: 460,
            column: 6
          },
          end: {
            line: 462,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 460,
            column: 6
          },
          end: {
            line: 462,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 460
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0],
      "18": [0],
      "19": [0, 0],
      "20": [0],
      "21": [0, 0],
      "22": [0],
      "23": [0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0],
      "32": [0],
      "33": [0],
      "34": [0, 0],
      "35": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5408b4e56abbf03f5505110dd827b5c43016fdfc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2cg6qb7uk2 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2cg6qb7uk2();
import { supabase } from "../../../lib/supabase";
import * as FileSystem from 'expo-file-system';
import { decode } from 'base64-arraybuffer';
import { performanceMonitor } from "../../../utils/performance";
var FileUploadService = function () {
  function FileUploadService() {
    _classCallCheck(this, FileUploadService);
    this.DEFAULT_BUCKET = (cov_2cg6qb7uk2().s[0]++, 'match-videos');
    this.MAX_FILE_SIZE = (cov_2cg6qb7uk2().s[1]++, 100 * 1024 * 1024);
    this.SUPPORTED_VIDEO_TYPES = (cov_2cg6qb7uk2().s[2]++, ['mp4', 'mov', 'avi']);
    this.SUPPORTED_IMAGE_TYPES = (cov_2cg6qb7uk2().s[3]++, ['jpg', 'jpeg', 'png', 'webp']);
  }
  return _createClass(FileUploadService, [{
    key: "uploadVideo",
    value: (function () {
      var _uploadVideo = _asyncToGenerator(function* (fileUri) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2cg6qb7uk2().b[0][0]++, {});
        cov_2cg6qb7uk2().f[0]++;
        cov_2cg6qb7uk2().s[4]++;
        try {
          cov_2cg6qb7uk2().s[5]++;
          performanceMonitor.start('video_upload');
          var uploadOptions = (cov_2cg6qb7uk2().s[6]++, Object.assign({
            bucket: this.DEFAULT_BUCKET,
            folder: 'videos',
            contentType: 'video/mp4',
            maxSizeBytes: this.MAX_FILE_SIZE
          }, options));
          var validation = (cov_2cg6qb7uk2().s[7]++, yield this.validateFile(fileUri, uploadOptions));
          cov_2cg6qb7uk2().s[8]++;
          if (validation.error) {
            cov_2cg6qb7uk2().b[1][0]++;
            cov_2cg6qb7uk2().s[9]++;
            return {
              data: null,
              error: validation.error
            };
          } else {
            cov_2cg6qb7uk2().b[1][1]++;
          }
          var fileSize = (cov_2cg6qb7uk2().s[10]++, (cov_2cg6qb7uk2().b[2][0]++, validation.fileInfo.size) || (cov_2cg6qb7uk2().b[2][1]++, 0));
          cov_2cg6qb7uk2().s[11]++;
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: 0,
            total: fileSize,
            percentage: 0
          });
          var fileName = (cov_2cg6qb7uk2().s[12]++, (cov_2cg6qb7uk2().b[3][0]++, uploadOptions.fileName) || (cov_2cg6qb7uk2().b[3][1]++, this.generateFileName('mp4')));
          var filePath = (cov_2cg6qb7uk2().s[13]++, uploadOptions.folder ? (cov_2cg6qb7uk2().b[4][0]++, `${uploadOptions.folder}/${fileName}`) : (cov_2cg6qb7uk2().b[4][1]++, fileName));
          var freeSpace = (cov_2cg6qb7uk2().s[14]++, yield FileSystem.getFreeDiskStorageAsync());
          cov_2cg6qb7uk2().s[15]++;
          if (freeSpace < fileSize * 2) {
            cov_2cg6qb7uk2().b[5][0]++;
            cov_2cg6qb7uk2().s[16]++;
            return {
              data: null,
              error: 'Insufficient storage space for upload'
            };
          } else {
            cov_2cg6qb7uk2().b[5][1]++;
          }
          cov_2cg6qb7uk2().s[17]++;
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize * 0.1,
            total: fileSize,
            percentage: 10
          });
          var fileBase64 = (cov_2cg6qb7uk2().s[18]++, yield FileSystem.readAsStringAsync(fileUri, {
            encoding: FileSystem.EncodingType.Base64
          }));
          cov_2cg6qb7uk2().s[19]++;
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize * 0.3,
            total: fileSize,
            percentage: 30
          });
          var fileArrayBuffer = (cov_2cg6qb7uk2().s[20]++, decode(fileBase64));
          cov_2cg6qb7uk2().s[21]++;
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize * 0.5,
            total: fileSize,
            percentage: 50
          });
          var uploadAttempts = (cov_2cg6qb7uk2().s[22]++, 0);
          var maxAttempts = (cov_2cg6qb7uk2().s[23]++, 3);
          var uploadError = (cov_2cg6qb7uk2().s[24]++, null);
          var uploadData = (cov_2cg6qb7uk2().s[25]++, null);
          cov_2cg6qb7uk2().s[26]++;
          while (uploadAttempts < maxAttempts) {
            cov_2cg6qb7uk2().s[27]++;
            try {
              var _ref = (cov_2cg6qb7uk2().s[28]++, yield supabase.storage.from(uploadOptions.bucket).upload(filePath, fileArrayBuffer, {
                  contentType: uploadOptions.contentType,
                  upsert: false
                })),
                data = _ref.data,
                error = _ref.error;
              cov_2cg6qb7uk2().s[29]++;
              if (error) {
                cov_2cg6qb7uk2().b[6][0]++;
                cov_2cg6qb7uk2().s[30]++;
                uploadError = error;
                cov_2cg6qb7uk2().s[31]++;
                uploadAttempts++;
                cov_2cg6qb7uk2().s[32]++;
                if (uploadAttempts < maxAttempts) {
                  cov_2cg6qb7uk2().b[7][0]++;
                  cov_2cg6qb7uk2().s[33]++;
                  console.warn(`Upload attempt ${uploadAttempts} failed, retrying...`);
                  cov_2cg6qb7uk2().s[34]++;
                  yield new Promise(function (resolve) {
                    cov_2cg6qb7uk2().f[1]++;
                    cov_2cg6qb7uk2().s[35]++;
                    return setTimeout(resolve, 1000 * uploadAttempts);
                  });
                  cov_2cg6qb7uk2().s[36]++;
                  continue;
                } else {
                  cov_2cg6qb7uk2().b[7][1]++;
                }
              } else {
                cov_2cg6qb7uk2().b[6][1]++;
                cov_2cg6qb7uk2().s[37]++;
                uploadData = data;
                cov_2cg6qb7uk2().s[38]++;
                break;
              }
            } catch (error) {
              cov_2cg6qb7uk2().s[39]++;
              uploadError = error;
              cov_2cg6qb7uk2().s[40]++;
              uploadAttempts++;
              cov_2cg6qb7uk2().s[41]++;
              if (uploadAttempts < maxAttempts) {
                cov_2cg6qb7uk2().b[8][0]++;
                cov_2cg6qb7uk2().s[42]++;
                yield new Promise(function (resolve) {
                  cov_2cg6qb7uk2().f[2]++;
                  cov_2cg6qb7uk2().s[43]++;
                  return setTimeout(resolve, 1000 * uploadAttempts);
                });
              } else {
                cov_2cg6qb7uk2().b[8][1]++;
              }
            }
          }
          cov_2cg6qb7uk2().s[44]++;
          if ((cov_2cg6qb7uk2().b[10][0]++, uploadError) || (cov_2cg6qb7uk2().b[10][1]++, !uploadData)) {
            var _uploadError;
            cov_2cg6qb7uk2().b[9][0]++;
            cov_2cg6qb7uk2().s[45]++;
            console.error('Error uploading video after retries:', uploadError);
            cov_2cg6qb7uk2().s[46]++;
            return {
              data: null,
              error: (cov_2cg6qb7uk2().b[11][0]++, (_uploadError = uploadError) == null ? void 0 : _uploadError.message) || (cov_2cg6qb7uk2().b[11][1]++, 'Failed to upload video after multiple attempts')
            };
          } else {
            cov_2cg6qb7uk2().b[9][1]++;
          }
          cov_2cg6qb7uk2().s[47]++;
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize * 0.9,
            total: fileSize,
            percentage: 90
          });
          var _ref2 = (cov_2cg6qb7uk2().s[48]++, supabase.storage.from(uploadOptions.bucket).getPublicUrl(uploadData.path)),
            urlData = _ref2.data;
          var result = (cov_2cg6qb7uk2().s[49]++, {
            url: urlData.publicUrl,
            path: uploadData.path,
            size: fileSize,
            type: 'video'
          });
          cov_2cg6qb7uk2().s[50]++;
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize,
            total: fileSize,
            percentage: 100
          });
          cov_2cg6qb7uk2().s[51]++;
          performanceMonitor.end('video_upload');
          cov_2cg6qb7uk2().s[52]++;
          return {
            data: result,
            error: null
          };
        } catch (error) {
          cov_2cg6qb7uk2().s[53]++;
          console.error('Error uploading video:', error);
          cov_2cg6qb7uk2().s[54]++;
          return {
            data: null,
            error: 'Failed to upload video'
          };
        }
      });
      function uploadVideo(_x) {
        return _uploadVideo.apply(this, arguments);
      }
      return uploadVideo;
    }())
  }, {
    key: "uploadImage",
    value: (function () {
      var _uploadImage = _asyncToGenerator(function* (fileUri) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2cg6qb7uk2().b[12][0]++, {});
        cov_2cg6qb7uk2().f[3]++;
        cov_2cg6qb7uk2().s[55]++;
        try {
          cov_2cg6qb7uk2().s[56]++;
          performanceMonitor.start('image_upload');
          var uploadOptions = (cov_2cg6qb7uk2().s[57]++, Object.assign({
            bucket: this.DEFAULT_BUCKET,
            folder: 'images',
            contentType: 'image/jpeg',
            maxSizeBytes: 10 * 1024 * 1024
          }, options));
          var validation = (cov_2cg6qb7uk2().s[58]++, yield this.validateFile(fileUri, uploadOptions));
          cov_2cg6qb7uk2().s[59]++;
          if (validation.error) {
            cov_2cg6qb7uk2().b[13][0]++;
            cov_2cg6qb7uk2().s[60]++;
            return {
              data: null,
              error: validation.error
            };
          } else {
            cov_2cg6qb7uk2().b[13][1]++;
          }
          var fileName = (cov_2cg6qb7uk2().s[61]++, (cov_2cg6qb7uk2().b[14][0]++, uploadOptions.fileName) || (cov_2cg6qb7uk2().b[14][1]++, this.generateFileName('jpg')));
          var filePath = (cov_2cg6qb7uk2().s[62]++, uploadOptions.folder ? (cov_2cg6qb7uk2().b[15][0]++, `${uploadOptions.folder}/${fileName}`) : (cov_2cg6qb7uk2().b[15][1]++, fileName));
          var fileBase64 = (cov_2cg6qb7uk2().s[63]++, yield FileSystem.readAsStringAsync(fileUri, {
            encoding: FileSystem.EncodingType.Base64
          }));
          var fileArrayBuffer = (cov_2cg6qb7uk2().s[64]++, decode(fileBase64));
          var _ref3 = (cov_2cg6qb7uk2().s[65]++, yield supabase.storage.from(uploadOptions.bucket).upload(filePath, fileArrayBuffer, {
              contentType: uploadOptions.contentType,
              upsert: false
            })),
            data = _ref3.data,
            error = _ref3.error;
          cov_2cg6qb7uk2().s[66]++;
          if (error) {
            cov_2cg6qb7uk2().b[16][0]++;
            cov_2cg6qb7uk2().s[67]++;
            console.error('Error uploading image:', error);
            cov_2cg6qb7uk2().s[68]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_2cg6qb7uk2().b[16][1]++;
          }
          var _ref4 = (cov_2cg6qb7uk2().s[69]++, supabase.storage.from(uploadOptions.bucket).getPublicUrl(data.path)),
            urlData = _ref4.data;
          var result = (cov_2cg6qb7uk2().s[70]++, {
            url: urlData.publicUrl,
            path: data.path,
            size: validation.fileInfo.size,
            type: 'image'
          });
          cov_2cg6qb7uk2().s[71]++;
          performanceMonitor.end('image_upload');
          cov_2cg6qb7uk2().s[72]++;
          return {
            data: result,
            error: null
          };
        } catch (error) {
          cov_2cg6qb7uk2().s[73]++;
          console.error('Error uploading image:', error);
          cov_2cg6qb7uk2().s[74]++;
          return {
            data: null,
            error: 'Failed to upload image'
          };
        }
      });
      function uploadImage(_x2) {
        return _uploadImage.apply(this, arguments);
      }
      return uploadImage;
    }())
  }, {
    key: "uploadThumbnail",
    value: (function () {
      var _uploadThumbnail = _asyncToGenerator(function* (videoUri, thumbnailUri) {
        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_2cg6qb7uk2().b[17][0]++, {});
        cov_2cg6qb7uk2().f[4]++;
        cov_2cg6qb7uk2().s[75]++;
        try {
          var fileName = (cov_2cg6qb7uk2().s[76]++, this.generateFileName('jpg'));
          var uploadOptions = (cov_2cg6qb7uk2().s[77]++, Object.assign({
            bucket: this.DEFAULT_BUCKET,
            folder: 'thumbnails',
            fileName: fileName,
            contentType: 'image/jpeg'
          }, options));
          cov_2cg6qb7uk2().s[78]++;
          return yield this.uploadImage(thumbnailUri, uploadOptions);
        } catch (error) {
          cov_2cg6qb7uk2().s[79]++;
          console.error('Error uploading thumbnail:', error);
          cov_2cg6qb7uk2().s[80]++;
          return {
            data: null,
            error: 'Failed to upload thumbnail'
          };
        }
      });
      function uploadThumbnail(_x3, _x4) {
        return _uploadThumbnail.apply(this, arguments);
      }
      return uploadThumbnail;
    }())
  }, {
    key: "deleteFile",
    value: (function () {
      var _deleteFile = _asyncToGenerator(function* (filePath) {
        var bucket = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2cg6qb7uk2().b[18][0]++, this.DEFAULT_BUCKET);
        cov_2cg6qb7uk2().f[5]++;
        cov_2cg6qb7uk2().s[81]++;
        try {
          var _ref5 = (cov_2cg6qb7uk2().s[82]++, yield supabase.storage.from(bucket).remove([filePath])),
            error = _ref5.error;
          cov_2cg6qb7uk2().s[83]++;
          if (error) {
            cov_2cg6qb7uk2().b[19][0]++;
            cov_2cg6qb7uk2().s[84]++;
            console.error('Error deleting file:', error);
            cov_2cg6qb7uk2().s[85]++;
            return {
              error: error.message
            };
          } else {
            cov_2cg6qb7uk2().b[19][1]++;
          }
          cov_2cg6qb7uk2().s[86]++;
          return {
            error: null
          };
        } catch (error) {
          cov_2cg6qb7uk2().s[87]++;
          console.error('Error deleting file:', error);
          cov_2cg6qb7uk2().s[88]++;
          return {
            error: 'Failed to delete file'
          };
        }
      });
      function deleteFile(_x5) {
        return _deleteFile.apply(this, arguments);
      }
      return deleteFile;
    }())
  }, {
    key: "getFileInfo",
    value: (function () {
      var _getFileInfo = _asyncToGenerator(function* (filePath) {
        var bucket = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2cg6qb7uk2().b[20][0]++, this.DEFAULT_BUCKET);
        cov_2cg6qb7uk2().f[6]++;
        cov_2cg6qb7uk2().s[89]++;
        try {
          var _ref6 = (cov_2cg6qb7uk2().s[90]++, yield supabase.storage.from(bucket).list(filePath)),
            data = _ref6.data,
            error = _ref6.error;
          cov_2cg6qb7uk2().s[91]++;
          if (error) {
            cov_2cg6qb7uk2().b[21][0]++;
            cov_2cg6qb7uk2().s[92]++;
            console.error('Error getting file info:', error);
            cov_2cg6qb7uk2().s[93]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_2cg6qb7uk2().b[21][1]++;
          }
          cov_2cg6qb7uk2().s[94]++;
          return {
            data: data,
            error: null
          };
        } catch (error) {
          cov_2cg6qb7uk2().s[95]++;
          console.error('Error getting file info:', error);
          cov_2cg6qb7uk2().s[96]++;
          return {
            data: null,
            error: 'Failed to get file info'
          };
        }
      });
      function getFileInfo(_x6) {
        return _getFileInfo.apply(this, arguments);
      }
      return getFileInfo;
    }())
  }, {
    key: "createSignedUrl",
    value: (function () {
      var _createSignedUrl = _asyncToGenerator(function* (filePath) {
        var expiresIn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2cg6qb7uk2().b[22][0]++, 3600);
        var bucket = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_2cg6qb7uk2().b[23][0]++, this.DEFAULT_BUCKET);
        cov_2cg6qb7uk2().f[7]++;
        cov_2cg6qb7uk2().s[97]++;
        try {
          var _ref7 = (cov_2cg6qb7uk2().s[98]++, yield supabase.storage.from(bucket).createSignedUrl(filePath, expiresIn)),
            data = _ref7.data,
            error = _ref7.error;
          cov_2cg6qb7uk2().s[99]++;
          if (error) {
            cov_2cg6qb7uk2().b[24][0]++;
            cov_2cg6qb7uk2().s[100]++;
            console.error('Error creating signed URL:', error);
            cov_2cg6qb7uk2().s[101]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_2cg6qb7uk2().b[24][1]++;
          }
          cov_2cg6qb7uk2().s[102]++;
          return {
            data: data.signedUrl,
            error: null
          };
        } catch (error) {
          cov_2cg6qb7uk2().s[103]++;
          console.error('Error creating signed URL:', error);
          cov_2cg6qb7uk2().s[104]++;
          return {
            data: null,
            error: 'Failed to create signed URL'
          };
        }
      });
      function createSignedUrl(_x7) {
        return _createSignedUrl.apply(this, arguments);
      }
      return createSignedUrl;
    }())
  }, {
    key: "validateFile",
    value: (function () {
      var _validateFile = _asyncToGenerator(function* (fileUri, options) {
        cov_2cg6qb7uk2().f[8]++;
        cov_2cg6qb7uk2().s[105]++;
        try {
          var fileInfo = (cov_2cg6qb7uk2().s[106]++, yield FileSystem.getInfoAsync(fileUri));
          cov_2cg6qb7uk2().s[107]++;
          if (!fileInfo.exists) {
            cov_2cg6qb7uk2().b[25][0]++;
            cov_2cg6qb7uk2().s[108]++;
            return {
              error: 'File does not exist'
            };
          } else {
            cov_2cg6qb7uk2().b[25][1]++;
          }
          cov_2cg6qb7uk2().s[109]++;
          if ((cov_2cg6qb7uk2().b[27][0]++, options.maxSizeBytes) && (cov_2cg6qb7uk2().b[27][1]++, fileInfo.size) && (cov_2cg6qb7uk2().b[27][2]++, fileInfo.size > options.maxSizeBytes)) {
            cov_2cg6qb7uk2().b[26][0]++;
            var maxSizeMB = (cov_2cg6qb7uk2().s[110]++, Math.round(options.maxSizeBytes / (1024 * 1024)));
            cov_2cg6qb7uk2().s[111]++;
            return {
              error: `File size exceeds ${maxSizeMB}MB limit`
            };
          } else {
            cov_2cg6qb7uk2().b[26][1]++;
          }
          var extension = (cov_2cg6qb7uk2().s[112]++, this.getFileExtension(fileUri));
          var isVideo = (cov_2cg6qb7uk2().s[113]++, this.SUPPORTED_VIDEO_TYPES.includes(extension.toLowerCase()));
          var isImage = (cov_2cg6qb7uk2().s[114]++, this.SUPPORTED_IMAGE_TYPES.includes(extension.toLowerCase()));
          cov_2cg6qb7uk2().s[115]++;
          if ((cov_2cg6qb7uk2().b[29][0]++, !isVideo) && (cov_2cg6qb7uk2().b[29][1]++, !isImage)) {
            cov_2cg6qb7uk2().b[28][0]++;
            cov_2cg6qb7uk2().s[116]++;
            return {
              error: 'Unsupported file type'
            };
          } else {
            cov_2cg6qb7uk2().b[28][1]++;
          }
          cov_2cg6qb7uk2().s[117]++;
          return {
            fileInfo: fileInfo
          };
        } catch (error) {
          cov_2cg6qb7uk2().s[118]++;
          console.error('Error validating file:', error);
          cov_2cg6qb7uk2().s[119]++;
          return {
            error: 'Failed to validate file'
          };
        }
      });
      function validateFile(_x8, _x9) {
        return _validateFile.apply(this, arguments);
      }
      return validateFile;
    }())
  }, {
    key: "generateFileName",
    value: function generateFileName(extension) {
      cov_2cg6qb7uk2().f[9]++;
      var timestamp = (cov_2cg6qb7uk2().s[120]++, Date.now());
      var random = (cov_2cg6qb7uk2().s[121]++, Math.random().toString(36).substring(2, 15));
      cov_2cg6qb7uk2().s[122]++;
      return `${timestamp}_${random}.${extension}`;
    }
  }, {
    key: "getFileExtension",
    value: function getFileExtension(uri) {
      cov_2cg6qb7uk2().f[10]++;
      var parts = (cov_2cg6qb7uk2().s[123]++, uri.split('.'));
      cov_2cg6qb7uk2().s[124]++;
      return (cov_2cg6qb7uk2().b[30][0]++, parts[parts.length - 1]) || (cov_2cg6qb7uk2().b[30][1]++, '');
    }
  }, {
    key: "compressVideo",
    value: (function () {
      var _compressVideo = _asyncToGenerator(function* (inputUri) {
        var quality = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2cg6qb7uk2().b[31][0]++, 'medium');
        cov_2cg6qb7uk2().f[11]++;
        cov_2cg6qb7uk2().s[125]++;
        try {
          cov_2cg6qb7uk2().s[126]++;
          console.log(`Video compression requested with quality: ${quality}`);
          cov_2cg6qb7uk2().s[127]++;
          return {
            data: inputUri,
            error: null
          };
        } catch (error) {
          cov_2cg6qb7uk2().s[128]++;
          console.error('Error compressing video:', error);
          cov_2cg6qb7uk2().s[129]++;
          return {
            data: null,
            error: 'Failed to compress video'
          };
        }
      });
      function compressVideo(_x0) {
        return _compressVideo.apply(this, arguments);
      }
      return compressVideo;
    }())
  }, {
    key: "generateVideoThumbnail",
    value: (function () {
      var _generateVideoThumbnail = _asyncToGenerator(function* (videoUri) {
        var timeSeconds = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2cg6qb7uk2().b[32][0]++, 1);
        cov_2cg6qb7uk2().f[12]++;
        cov_2cg6qb7uk2().s[130]++;
        try {
          cov_2cg6qb7uk2().s[131]++;
          console.log(`Thumbnail generation requested for video at ${timeSeconds}s`);
          cov_2cg6qb7uk2().s[132]++;
          return {
            data: videoUri,
            error: null
          };
        } catch (error) {
          cov_2cg6qb7uk2().s[133]++;
          console.error('Error generating thumbnail:', error);
          cov_2cg6qb7uk2().s[134]++;
          return {
            data: null,
            error: 'Failed to generate thumbnail'
          };
        }
      });
      function generateVideoThumbnail(_x1) {
        return _generateVideoThumbnail.apply(this, arguments);
      }
      return generateVideoThumbnail;
    }())
  }, {
    key: "simulateProgress",
    value: function simulateProgress(onProgress) {
      var totalSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2cg6qb7uk2().b[33][0]++, 1000000);
      cov_2cg6qb7uk2().f[13]++;
      cov_2cg6qb7uk2().s[135]++;
      if (!onProgress) {
        cov_2cg6qb7uk2().b[34][0]++;
        cov_2cg6qb7uk2().s[136]++;
        return;
      } else {
        cov_2cg6qb7uk2().b[34][1]++;
      }
      var loaded = (cov_2cg6qb7uk2().s[137]++, 0);
      var interval = (cov_2cg6qb7uk2().s[138]++, setInterval(function () {
        cov_2cg6qb7uk2().f[14]++;
        cov_2cg6qb7uk2().s[139]++;
        loaded += totalSize * 0.1;
        var percentage = (cov_2cg6qb7uk2().s[140]++, Math.min(loaded / totalSize * 100, 100));
        cov_2cg6qb7uk2().s[141]++;
        onProgress({
          loaded: loaded,
          total: totalSize,
          percentage: percentage
        });
        cov_2cg6qb7uk2().s[142]++;
        if (percentage >= 100) {
          cov_2cg6qb7uk2().b[35][0]++;
          cov_2cg6qb7uk2().s[143]++;
          clearInterval(interval);
        } else {
          cov_2cg6qb7uk2().b[35][1]++;
        }
      }, 200));
    }
  }]);
}();
export var fileUploadService = (cov_2cg6qb7uk2().s[144]++, new FileUploadService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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