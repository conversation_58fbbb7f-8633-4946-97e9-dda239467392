129b3367dec92e69df7bbc7e3d044f4e
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2blu1uqp5u() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useOptimizationControlCenter.ts";
  var hash = "34e8709f3ce36a501b4d321f65099a33e60755f4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useOptimizationControlCenter.ts",
    statementMap: {
      "0": {
        start: {
          line: 93,
          column: 20
        },
        end: {
          line: 93,
          column: 45
        }
      },
      "1": {
        start: {
          line: 94,
          column: 13
        },
        end: {
          line: 94,
          column: 32
        }
      },
      "2": {
        start: {
          line: 95,
          column: 15
        },
        end: {
          line: 95,
          column: 36
        }
      },
      "3": {
        start: {
          line: 96,
          column: 17
        },
        end: {
          line: 96,
          column: 40
        }
      },
      "4": {
        start: {
          line: 98,
          column: 30
        },
        end: {
          line: 106,
          column: 4
        }
      },
      "5": {
        start: {
          line: 108,
          column: 28
        },
        end: {
          line: 136,
          column: 4
        }
      },
      "6": {
        start: {
          line: 141,
          column: 21
        },
        end: {
          line: 143,
          column: 8
        }
      },
      "7": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 55
        }
      },
      "8": {
        start: {
          line: 142,
          column: 22
        },
        end: {
          line: 142,
          column: 52
        }
      },
      "9": {
        start: {
          line: 148,
          column: 29
        },
        end: {
          line: 165,
          column: 8
        }
      },
      "10": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 164,
          column: 5
        }
      },
      "11": {
        start: {
          line: 150,
          column: 22
        },
        end: {
          line: 150,
          column: 80
        }
      },
      "12": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 159,
          column: 7
        }
      },
      "13": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 158,
          column: 12
        }
      },
      "14": {
        start: {
          line: 152,
          column: 26
        },
        end: {
          line: 158,
          column: 9
        }
      },
      "15": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 21
        }
      },
      "16": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 62
        }
      },
      "17": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 19
        }
      },
      "18": {
        start: {
          line: 170,
          column: 22
        },
        end: {
          line: 182,
          column: 8
        }
      },
      "19": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 181,
          column: 5
        }
      },
      "20": {
        start: {
          line: 172,
          column: 22
        },
        end: {
          line: 176,
          column: 7
        }
      },
      "21": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 21
        }
      },
      "22": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 179,
          column: 56
        }
      },
      "23": {
        start: {
          line: 180,
          column: 6
        },
        end: {
          line: 180,
          column: 19
        }
      },
      "24": {
        start: {
          line: 187,
          column: 29
        },
        end: {
          line: 216,
          column: 32
        }
      },
      "25": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 215,
          column: 5
        }
      },
      "26": {
        start: {
          line: 189,
          column: 21
        },
        end: {
          line: 200,
          column: 7
        }
      },
      "27": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 211,
          column: 7
        }
      },
      "28": {
        start: {
          line: 204,
          column: 10
        },
        end: {
          line: 204,
          column: 49
        }
      },
      "29": {
        start: {
          line: 206,
          column: 10
        },
        end: {
          line: 206,
          column: 50
        }
      },
      "30": {
        start: {
          line: 208,
          column: 10
        },
        end: {
          line: 208,
          column: 52
        }
      },
      "31": {
        start: {
          line: 210,
          column: 10
        },
        end: {
          line: 210,
          column: 49
        }
      },
      "32": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 62
        }
      },
      "33": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 16
        }
      },
      "34": {
        start: {
          line: 221,
          column: 29
        },
        end: {
          line: 247,
          column: 43
        }
      },
      "35": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 246,
          column: 5
        }
      },
      "36": {
        start: {
          line: 223,
          column: 6
        },
        end: {
          line: 223,
          column: 67
        }
      },
      "37": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 228,
          column: 7
        }
      },
      "38": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 227,
          column: 47
        }
      },
      "39": {
        start: {
          line: 231,
          column: 6
        },
        end: {
          line: 233,
          column: 7
        }
      },
      "40": {
        start: {
          line: 232,
          column: 8
        },
        end: {
          line: 232,
          column: 65
        }
      },
      "41": {
        start: {
          line: 236,
          column: 6
        },
        end: {
          line: 238,
          column: 7
        }
      },
      "42": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 51
        }
      },
      "43": {
        start: {
          line: 241,
          column: 6
        },
        end: {
          line: 241,
          column: 47
        }
      },
      "44": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 243,
          column: 65
        }
      },
      "45": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 62
        }
      },
      "46": {
        start: {
          line: 252,
          column: 35
        },
        end: {
          line: 282,
          column: 25
        }
      },
      "47": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 281,
          column: 5
        }
      },
      "48": {
        start: {
          line: 254,
          column: 30
        },
        end: {
          line: 254,
          column: 32
        }
      },
      "49": {
        start: {
          line: 257,
          column: 32
        },
        end: {
          line: 257,
          column: 99
        }
      },
      "50": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 258,
          column: 49
        }
      },
      "51": {
        start: {
          line: 261,
          column: 28
        },
        end: {
          line: 261,
          column: 84
        }
      },
      "52": {
        start: {
          line: 262,
          column: 6
        },
        end: {
          line: 262,
          column: 45
        }
      },
      "53": {
        start: {
          line: 265,
          column: 28
        },
        end: {
          line: 265,
          column: 82
        }
      },
      "54": {
        start: {
          line: 266,
          column: 27
        },
        end: {
          line: 274,
          column: 11
        }
      },
      "55": {
        start: {
          line: 267,
          column: 23
        },
        end: {
          line: 267,
          column: 87
        }
      },
      "56": {
        start: {
          line: 268,
          column: 21
        },
        end: {
          line: 274,
          column: 9
        }
      },
      "57": {
        start: {
          line: 275,
          column: 6
        },
        end: {
          line: 275,
          column: 44
        }
      },
      "58": {
        start: {
          line: 277,
          column: 6
        },
        end: {
          line: 277,
          column: 29
        }
      },
      "59": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 68
        }
      },
      "60": {
        start: {
          line: 280,
          column: 6
        },
        end: {
          line: 280,
          column: 16
        }
      },
      "61": {
        start: {
          line: 287,
          column: 30
        },
        end: {
          line: 296,
          column: 8
        }
      },
      "62": {
        start: {
          line: 288,
          column: 4
        },
        end: {
          line: 295,
          column: 5
        }
      },
      "63": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 62
        }
      },
      "64": {
        start: {
          line: 291,
          column: 6
        },
        end: {
          line: 291,
          column: 18
        }
      },
      "65": {
        start: {
          line: 293,
          column: 6
        },
        end: {
          line: 293,
          column: 62
        }
      },
      "66": {
        start: {
          line: 294,
          column: 6
        },
        end: {
          line: 294,
          column: 19
        }
      },
      "67": {
        start: {
          line: 301,
          column: 32
        },
        end: {
          line: 303,
          column: 8
        }
      },
      "68": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 302,
          column: 61
        }
      },
      "69": {
        start: {
          line: 308,
          column: 29
        },
        end: {
          line: 313,
          column: 8
        }
      },
      "70": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 312,
          column: 8
        }
      },
      "71": {
        start: {
          line: 309,
          column: 22
        },
        end: {
          line: 312,
          column: 5
        }
      },
      "72": {
        start: {
          line: 311,
          column: 48
        },
        end: {
          line: 311,
          column: 78
        }
      },
      "73": {
        start: {
          line: 318,
          column: 35
        },
        end: {
          line: 377,
          column: 17
        }
      },
      "74": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 376,
          column: 5
        }
      },
      "75": {
        start: {
          line: 321,
          column: 27
        },
        end: {
          line: 332,
          column: 7
        }
      },
      "76": {
        start: {
          line: 331,
          column: 63
        },
        end: {
          line: 331,
          column: 92
        }
      },
      "77": {
        start: {
          line: 335,
          column: 27
        },
        end: {
          line: 340,
          column: 7
        }
      },
      "78": {
        start: {
          line: 343,
          column: 37
        },
        end: {
          line: 343,
          column: 93
        }
      },
      "79": {
        start: {
          line: 344,
          column: 28
        },
        end: {
          line: 350,
          column: 7
        }
      },
      "80": {
        start: {
          line: 348,
          column: 33
        },
        end: {
          line: 348,
          column: 58
        }
      },
      "81": {
        start: {
          line: 353,
          column: 22
        },
        end: {
          line: 353,
          column: 69
        }
      },
      "82": {
        start: {
          line: 354,
          column: 33
        },
        end: {
          line: 354,
          column: 82
        }
      },
      "83": {
        start: {
          line: 355,
          column: 23
        },
        end: {
          line: 360,
          column: 7
        }
      },
      "84": {
        start: {
          line: 363,
          column: 23
        },
        end: {
          line: 363,
          column: 104
        }
      },
      "85": {
        start: {
          line: 365,
          column: 6
        },
        end: {
          line: 372,
          column: 10
        }
      },
      "86": {
        start: {
          line: 365,
          column: 24
        },
        end: {
          line: 372,
          column: 7
        }
      },
      "87": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 375,
          column: 69
        }
      },
      "88": {
        start: {
          line: 380,
          column: 33
        },
        end: {
          line: 425,
          column: 3
        }
      },
      "89": {
        start: {
          line: 386,
          column: 21
        },
        end: {
          line: 386,
          column: 23
        }
      },
      "90": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 395,
          column: 5
        }
      },
      "91": {
        start: {
          line: 389,
          column: 6
        },
        end: {
          line: 394,
          column: 9
        }
      },
      "92": {
        start: {
          line: 397,
          column: 4
        },
        end: {
          line: 404,
          column: 5
        }
      },
      "93": {
        start: {
          line: 398,
          column: 6
        },
        end: {
          line: 403,
          column: 9
        }
      },
      "94": {
        start: {
          line: 406,
          column: 4
        },
        end: {
          line: 413,
          column: 5
        }
      },
      "95": {
        start: {
          line: 407,
          column: 6
        },
        end: {
          line: 412,
          column: 9
        }
      },
      "96": {
        start: {
          line: 415,
          column: 4
        },
        end: {
          line: 422,
          column: 5
        }
      },
      "97": {
        start: {
          line: 416,
          column: 6
        },
        end: {
          line: 421,
          column: 9
        }
      },
      "98": {
        start: {
          line: 424,
          column: 4
        },
        end: {
          line: 424,
          column: 20
        }
      },
      "99": {
        start: {
          line: 428,
          column: 2
        },
        end: {
          line: 436,
          column: 89
        }
      },
      "100": {
        start: {
          line: 429,
          column: 4
        },
        end: {
          line: 429,
          column: 49
        }
      },
      "101": {
        start: {
          line: 429,
          column: 42
        },
        end: {
          line: 429,
          column: 49
        }
      },
      "102": {
        start: {
          line: 431,
          column: 21
        },
        end: {
          line: 433,
          column: 29
        }
      },
      "103": {
        start: {
          line: 432,
          column: 6
        },
        end: {
          line: 432,
          column: 33
        }
      },
      "104": {
        start: {
          line: 435,
          column: 4
        },
        end: {
          line: 435,
          column: 41
        }
      },
      "105": {
        start: {
          line: 435,
          column: 17
        },
        end: {
          line: 435,
          column: 40
        }
      },
      "106": {
        start: {
          line: 439,
          column: 2
        },
        end: {
          line: 446,
          column: 9
        }
      },
      "107": {
        start: {
          line: 440,
          column: 23
        },
        end: {
          line: 443,
          column: 5
        }
      },
      "108": {
        start: {
          line: 441,
          column: 6
        },
        end: {
          line: 441,
          column: 39
        }
      },
      "109": {
        start: {
          line: 442,
          column: 6
        },
        end: {
          line: 442,
          column: 59
        }
      },
      "110": {
        start: {
          line: 442,
          column: 24
        },
        end: {
          line: 442,
          column: 56
        }
      },
      "111": {
        start: {
          line: 445,
          column: 4
        },
        end: {
          line: 445,
          column: 17
        }
      },
      "112": {
        start: {
          line: 449,
          column: 18
        },
        end: {
          line: 478,
          column: 13
        }
      },
      "113": {
        start: {
          line: 450,
          column: 26
        },
        end: {
          line: 452,
          column: 74
        }
      },
      "114": {
        start: {
          line: 454,
          column: 29
        },
        end: {
          line: 457,
          column: 81
        }
      },
      "115": {
        start: {
          line: 459,
          column: 30
        },
        end: {
          line: 461,
          column: 82
        }
      },
      "116": {
        start: {
          line: 463,
          column: 28
        },
        end: {
          line: 465,
          column: 77
        }
      },
      "117": {
        start: {
          line: 467,
          column: 24
        },
        end: {
          line: 469,
          column: 54
        }
      },
      "118": {
        start: {
          line: 467,
          column: 55
        },
        end: {
          line: 467,
          column: 86
        }
      },
      "119": {
        start: {
          line: 468,
          column: 54
        },
        end: {
          line: 468,
          column: 81
        }
      },
      "120": {
        start: {
          line: 471,
          column: 4
        },
        end: {
          line: 477,
          column: 6
        }
      },
      "121": {
        start: {
          line: 480,
          column: 2
        },
        end: {
          line: 516,
          column: 5
        }
      },
      "122": {
        start: {
          line: 480,
          column: 24
        },
        end: {
          line: 499,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "useOptimizationControlCenter",
        decl: {
          start: {
            line: 90,
            column: 16
          },
          end: {
            line: 90,
            column: 44
          }
        },
        loc: {
          start: {
            line: 92,
            column: 38
          },
          end: {
            line: 517,
            column: 1
          }
        },
        line: 92
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 141,
            column: 33
          },
          end: {
            line: 141,
            column: 34
          }
        },
        loc: {
          start: {
            line: 141,
            column: 78
          },
          end: {
            line: 143,
            column: 3
          }
        },
        line: 141
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 142,
            column: 13
          },
          end: {
            line: 142,
            column: 14
          }
        },
        loc: {
          start: {
            line: 142,
            column: 22
          },
          end: {
            line: 142,
            column: 52
          }
        },
        line: 142
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 148,
            column: 41
          },
          end: {
            line: 148,
            column: 42
          }
        },
        loc: {
          start: {
            line: 148,
            column: 70
          },
          end: {
            line: 165,
            column: 3
          }
        },
        line: 148
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 152,
            column: 17
          },
          end: {
            line: 152,
            column: 18
          }
        },
        loc: {
          start: {
            line: 152,
            column: 26
          },
          end: {
            line: 158,
            column: 9
          }
        },
        line: 152
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 170,
            column: 34
          },
          end: {
            line: 170,
            column: 35
          }
        },
        loc: {
          start: {
            line: 170,
            column: 61
          },
          end: {
            line: 182,
            column: 3
          }
        },
        line: 170
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 187,
            column: 41
          },
          end: {
            line: 187,
            column: 42
          }
        },
        loc: {
          start: {
            line: 187,
            column: 94
          },
          end: {
            line: 216,
            column: 3
          }
        },
        line: 187
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 221,
            column: 41
          },
          end: {
            line: 221,
            column: 42
          }
        },
        loc: {
          start: {
            line: 221,
            column: 53
          },
          end: {
            line: 247,
            column: 3
          }
        },
        line: 221
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 252,
            column: 47
          },
          end: {
            line: 252,
            column: 48
          }
        },
        loc: {
          start: {
            line: 252,
            column: 59
          },
          end: {
            line: 282,
            column: 3
          }
        },
        line: 252
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 267,
            column: 16
          },
          end: {
            line: 267,
            column: 17
          }
        },
        loc: {
          start: {
            line: 267,
            column: 23
          },
          end: {
            line: 267,
            column: 87
          }
        },
        line: 267
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 268,
            column: 13
          },
          end: {
            line: 268,
            column: 14
          }
        },
        loc: {
          start: {
            line: 268,
            column: 21
          },
          end: {
            line: 274,
            column: 9
          }
        },
        line: 268
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 287,
            column: 42
          },
          end: {
            line: 287,
            column: 43
          }
        },
        loc: {
          start: {
            line: 287,
            column: 75
          },
          end: {
            line: 296,
            column: 3
          }
        },
        line: 287
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 301,
            column: 44
          },
          end: {
            line: 301,
            column: 45
          }
        },
        loc: {
          start: {
            line: 301,
            column: 50
          },
          end: {
            line: 303,
            column: 3
          }
        },
        line: 301
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 308,
            column: 41
          },
          end: {
            line: 308,
            column: 42
          }
        },
        loc: {
          start: {
            line: 308,
            column: 64
          },
          end: {
            line: 313,
            column: 3
          }
        },
        line: 308
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 309,
            column: 13
          },
          end: {
            line: 309,
            column: 14
          }
        },
        loc: {
          start: {
            line: 309,
            column: 22
          },
          end: {
            line: 312,
            column: 5
          }
        },
        line: 309
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 311,
            column: 37
          },
          end: {
            line: 311,
            column: 38
          }
        },
        loc: {
          start: {
            line: 311,
            column: 48
          },
          end: {
            line: 311,
            column: 78
          }
        },
        line: 311
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 318,
            column: 47
          },
          end: {
            line: 318,
            column: 48
          }
        },
        loc: {
          start: {
            line: 318,
            column: 59
          },
          end: {
            line: 377,
            column: 3
          }
        },
        line: 318
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 331,
            column: 54
          },
          end: {
            line: 331,
            column: 55
          }
        },
        loc: {
          start: {
            line: 331,
            column: 63
          },
          end: {
            line: 331,
            column: 92
          }
        },
        line: 331
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 348,
            column: 18
          },
          end: {
            line: 348,
            column: 19
          }
        },
        loc: {
          start: {
            line: 348,
            column: 33
          },
          end: {
            line: 348,
            column: 58
          }
        },
        line: 348
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 365,
            column: 15
          },
          end: {
            line: 365,
            column: 16
          }
        },
        loc: {
          start: {
            line: 365,
            column: 24
          },
          end: {
            line: 372,
            column: 7
          }
        },
        line: 365
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 380,
            column: 33
          },
          end: {
            line: 380,
            column: 34
          }
        },
        loc: {
          start: {
            line: 385,
            column: 7
          },
          end: {
            line: 425,
            column: 3
          }
        },
        line: 385
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 428,
            column: 12
          },
          end: {
            line: 428,
            column: 13
          }
        },
        loc: {
          start: {
            line: 428,
            column: 18
          },
          end: {
            line: 436,
            column: 3
          }
        },
        line: 428
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 431,
            column: 33
          },
          end: {
            line: 431,
            column: 34
          }
        },
        loc: {
          start: {
            line: 431,
            column: 39
          },
          end: {
            line: 433,
            column: 5
          }
        },
        line: 431
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 435,
            column: 11
          },
          end: {
            line: 435,
            column: 12
          }
        },
        loc: {
          start: {
            line: 435,
            column: 17
          },
          end: {
            line: 435,
            column: 40
          }
        },
        line: 435
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 439,
            column: 12
          },
          end: {
            line: 439,
            column: 13
          }
        },
        loc: {
          start: {
            line: 439,
            column: 18
          },
          end: {
            line: 446,
            column: 3
          }
        },
        line: 439
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 440,
            column: 23
          },
          end: {
            line: 440,
            column: 24
          }
        },
        loc: {
          start: {
            line: 440,
            column: 35
          },
          end: {
            line: 443,
            column: 5
          }
        },
        line: 440
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 442,
            column: 15
          },
          end: {
            line: 442,
            column: 16
          }
        },
        loc: {
          start: {
            line: 442,
            column: 24
          },
          end: {
            line: 442,
            column: 56
          }
        },
        line: 442
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 449,
            column: 26
          },
          end: {
            line: 449,
            column: 27
          }
        },
        loc: {
          start: {
            line: 449,
            column: 32
          },
          end: {
            line: 478,
            column: 3
          }
        },
        line: 449
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 467,
            column: 44
          },
          end: {
            line: 467,
            column: 45
          }
        },
        loc: {
          start: {
            line: 467,
            column: 55
          },
          end: {
            line: 467,
            column: 86
          }
        },
        line: 467
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 468,
            column: 43
          },
          end: {
            line: 468,
            column: 44
          }
        },
        loc: {
          start: {
            line: 468,
            column: 54
          },
          end: {
            line: 468,
            column: 81
          }
        },
        line: 468
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 480,
            column: 17
          },
          end: {
            line: 480,
            column: 18
          }
        },
        loc: {
          start: {
            line: 480,
            column: 24
          },
          end: {
            line: 499,
            column: 3
          }
        },
        line: 480
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 91,
            column: 2
          },
          end: {
            line: 91,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 91,
            column: 48
          },
          end: {
            line: 91,
            column: 50
          }
        }],
        line: 91
      },
      "1": {
        loc: {
          start: {
            line: 151,
            column: 6
          },
          end: {
            line: 159,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 6
          },
          end: {
            line: 159,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "2": {
        loc: {
          start: {
            line: 187,
            column: 48
          },
          end: {
            line: 187,
            column: 89
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 187,
            column: 83
          },
          end: {
            line: 187,
            column: 89
          }
        }],
        line: 187
      },
      "3": {
        loc: {
          start: {
            line: 202,
            column: 6
          },
          end: {
            line: 211,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 203,
            column: 8
          },
          end: {
            line: 204,
            column: 49
          }
        }, {
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 206,
            column: 50
          }
        }, {
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 208,
            column: 52
          }
        }, {
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 210,
            column: 49
          }
        }],
        line: 202
      },
      "4": {
        loc: {
          start: {
            line: 226,
            column: 6
          },
          end: {
            line: 228,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 6
          },
          end: {
            line: 228,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "5": {
        loc: {
          start: {
            line: 226,
            column: 10
          },
          end: {
            line: 226,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 226,
            column: 10
          },
          end: {
            line: 226,
            column: 41
          }
        }, {
          start: {
            line: 226,
            column: 45
          },
          end: {
            line: 226,
            column: 67
          }
        }],
        line: 226
      },
      "6": {
        loc: {
          start: {
            line: 231,
            column: 6
          },
          end: {
            line: 233,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 231,
            column: 6
          },
          end: {
            line: 233,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 231
      },
      "7": {
        loc: {
          start: {
            line: 236,
            column: 6
          },
          end: {
            line: 238,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 236,
            column: 6
          },
          end: {
            line: 238,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 236
      },
      "8": {
        loc: {
          start: {
            line: 267,
            column: 23
          },
          end: {
            line: 267,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 267,
            column: 23
          },
          end: {
            line: 267,
            column: 53
          }
        }, {
          start: {
            line: 267,
            column: 57
          },
          end: {
            line: 267,
            column: 87
          }
        }],
        line: 267
      },
      "9": {
        loc: {
          start: {
            line: 388,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 388,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 388
      },
      "10": {
        loc: {
          start: {
            line: 397,
            column: 4
          },
          end: {
            line: 404,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 397,
            column: 4
          },
          end: {
            line: 404,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 397
      },
      "11": {
        loc: {
          start: {
            line: 406,
            column: 4
          },
          end: {
            line: 413,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 406,
            column: 4
          },
          end: {
            line: 413,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 406
      },
      "12": {
        loc: {
          start: {
            line: 415,
            column: 4
          },
          end: {
            line: 422,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 4
          },
          end: {
            line: 422,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "13": {
        loc: {
          start: {
            line: 429,
            column: 4
          },
          end: {
            line: 429,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 429,
            column: 4
          },
          end: {
            line: 429,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 429
      },
      "14": {
        loc: {
          start: {
            line: 450,
            column: 26
          },
          end: {
            line: 452,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 450,
            column: 60
          },
          end: {
            line: 450,
            column: 71
          }
        }, {
          start: {
            line: 451,
            column: 25
          },
          end: {
            line: 452,
            column: 74
          }
        }],
        line: 450
      },
      "15": {
        loc: {
          start: {
            line: 451,
            column: 25
          },
          end: {
            line: 452,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 451,
            column: 59
          },
          end: {
            line: 451,
            column: 65
          }
        }, {
          start: {
            line: 452,
            column: 25
          },
          end: {
            line: 452,
            column: 74
          }
        }],
        line: 451
      },
      "16": {
        loc: {
          start: {
            line: 452,
            column: 25
          },
          end: {
            line: 452,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 452,
            column: 59
          },
          end: {
            line: 452,
            column: 65
          }
        }, {
          start: {
            line: 452,
            column: 68
          },
          end: {
            line: 452,
            column: 74
          }
        }],
        line: 452
      },
      "17": {
        loc: {
          start: {
            line: 454,
            column: 29
          },
          end: {
            line: 457,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 454,
            column: 73
          },
          end: {
            line: 454,
            column: 77
          }
        }, {
          start: {
            line: 455,
            column: 28
          },
          end: {
            line: 457,
            column: 81
          }
        }],
        line: 454
      },
      "18": {
        loc: {
          start: {
            line: 455,
            column: 28
          },
          end: {
            line: 457,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 455,
            column: 72
          },
          end: {
            line: 455,
            column: 75
          }
        }, {
          start: {
            line: 456,
            column: 28
          },
          end: {
            line: 457,
            column: 81
          }
        }],
        line: 455
      },
      "19": {
        loc: {
          start: {
            line: 456,
            column: 28
          },
          end: {
            line: 457,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 456,
            column: 72
          },
          end: {
            line: 456,
            column: 76
          }
        }, {
          start: {
            line: 457,
            column: 28
          },
          end: {
            line: 457,
            column: 81
          }
        }],
        line: 456
      },
      "20": {
        loc: {
          start: {
            line: 457,
            column: 28
          },
          end: {
            line: 457,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 457,
            column: 72
          },
          end: {
            line: 457,
            column: 75
          }
        }, {
          start: {
            line: 457,
            column: 78
          },
          end: {
            line: 457,
            column: 81
          }
        }],
        line: 457
      },
      "21": {
        loc: {
          start: {
            line: 459,
            column: 30
          },
          end: {
            line: 461,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 459,
            column: 67
          },
          end: {
            line: 459,
            column: 76
          }
        }, {
          start: {
            line: 460,
            column: 29
          },
          end: {
            line: 461,
            column: 82
          }
        }],
        line: 459
      },
      "22": {
        loc: {
          start: {
            line: 460,
            column: 29
          },
          end: {
            line: 461,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 460,
            column: 66
          },
          end: {
            line: 460,
            column: 72
          }
        }, {
          start: {
            line: 461,
            column: 29
          },
          end: {
            line: 461,
            column: 82
          }
        }],
        line: 460
      },
      "23": {
        loc: {
          start: {
            line: 461,
            column: 29
          },
          end: {
            line: 461,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 461,
            column: 66
          },
          end: {
            line: 461,
            column: 74
          }
        }, {
          start: {
            line: 461,
            column: 77
          },
          end: {
            line: 461,
            column: 82
          }
        }],
        line: 461
      },
      "24": {
        loc: {
          start: {
            line: 463,
            column: 28
          },
          end: {
            line: 465,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 463,
            column: 63
          },
          end: {
            line: 463,
            column: 74
          }
        }, {
          start: {
            line: 464,
            column: 27
          },
          end: {
            line: 465,
            column: 77
          }
        }],
        line: 463
      },
      "25": {
        loc: {
          start: {
            line: 464,
            column: 27
          },
          end: {
            line: 465,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 464,
            column: 62
          },
          end: {
            line: 464,
            column: 68
          }
        }, {
          start: {
            line: 465,
            column: 27
          },
          end: {
            line: 465,
            column: 77
          }
        }],
        line: 464
      },
      "26": {
        loc: {
          start: {
            line: 465,
            column: 27
          },
          end: {
            line: 465,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 465,
            column: 62
          },
          end: {
            line: 465,
            column: 68
          }
        }, {
          start: {
            line: 465,
            column: 71
          },
          end: {
            line: 465,
            column: 77
          }
        }],
        line: 465
      },
      "27": {
        loc: {
          start: {
            line: 467,
            column: 24
          },
          end: {
            line: 469,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 467,
            column: 24
          },
          end: {
            line: 467,
            column: 96
          }
        }, {
          start: {
            line: 468,
            column: 23
          },
          end: {
            line: 468,
            column: 91
          }
        }, {
          start: {
            line: 469,
            column: 23
          },
          end: {
            line: 469,
            column: 54
          }
        }],
        line: 467
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "34e8709f3ce36a501b4d321f65099a33e60755f4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2blu1uqp5u = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2blu1uqp5u();
import { useState, useEffect, useCallback, useMemo } from 'react';
import { usePerformanceDashboard } from "./usePerformanceDashboard";
import { useAIOptimization } from "./useAIOptimization";
import { useEdgeOptimization } from "./useEdgeOptimization";
import { useNativeOptimization } from "./useNativeOptimization";
import { advancedCustomizationManager } from "../services/customization/AdvancedCustomizationManager";
import { futureEnhancementsManager } from "../services/planning/FutureEnhancementsManager";
export function useOptimizationControlCenter() {
  var initialConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2blu1uqp5u().b[0][0]++, {});
  cov_2blu1uqp5u().f[0]++;
  var dashboard = (cov_2blu1uqp5u().s[0]++, usePerformanceDashboard());
  var ai = (cov_2blu1uqp5u().s[1]++, useAIOptimization());
  var edge = (cov_2blu1uqp5u().s[2]++, useEdgeOptimization());
  var native = (cov_2blu1uqp5u().s[3]++, useNativeOptimization());
  var _ref = (cov_2blu1uqp5u().s[4]++, useState(Object.assign({
      enableRealTimeMonitoring: true,
      enableAdvancedCustomization: true,
      enableFuturePlanning: true,
      autoOptimization: true,
      alertThreshold: 80,
      updateInterval: 30000
    }, initialConfig))),
    _ref2 = _slicedToArray(_ref, 2),
    config = _ref2[0],
    setConfig = _ref2[1];
  var _ref3 = (cov_2blu1uqp5u().s[5]++, useState({
      isInitialized: false,
      currentMode: 'overview',
      systemHealth: {
        overall: 0,
        phases: {},
        alerts: 0,
        criticalIssues: 0
      },
      optimization: {
        totalImprovement: 0,
        activeOptimizations: 0,
        performanceScore: 0,
        efficiency: 0
      },
      customization: {
        activeProfile: null,
        availableProfiles: 0,
        abTestsRunning: 0,
        customizationScore: 0
      },
      planning: {
        roadmapPhases: 0,
        pipelineEnhancements: 0,
        investmentRequired: 0,
        expectedROI: 0
      },
      insights: []
    })),
    _ref4 = _slicedToArray(_ref3, 2),
    state = _ref4[0],
    setState = _ref4[1];
  var switchMode = (cov_2blu1uqp5u().s[6]++, useCallback(function (mode) {
    cov_2blu1uqp5u().f[1]++;
    cov_2blu1uqp5u().s[7]++;
    setState(function (prev) {
      cov_2blu1uqp5u().f[2]++;
      cov_2blu1uqp5u().s[8]++;
      return Object.assign({}, prev, {
        currentMode: mode
      });
    });
  }, []));
  var applyCustomProfile = (cov_2blu1uqp5u().s[9]++, useCallback(function () {
    var _ref5 = _asyncToGenerator(function* (profileId) {
      cov_2blu1uqp5u().f[3]++;
      cov_2blu1uqp5u().s[10]++;
      try {
        var success = (cov_2blu1uqp5u().s[11]++, yield advancedCustomizationManager.applyProfile(profileId));
        cov_2blu1uqp5u().s[12]++;
        if (success) {
          cov_2blu1uqp5u().b[1][0]++;
          cov_2blu1uqp5u().s[13]++;
          setState(function (prev) {
            cov_2blu1uqp5u().f[4]++;
            cov_2blu1uqp5u().s[14]++;
            return Object.assign({}, prev, {
              customization: Object.assign({}, prev.customization, {
                activeProfile: profileId
              })
            });
          });
        } else {
          cov_2blu1uqp5u().b[1][1]++;
        }
        cov_2blu1uqp5u().s[15]++;
        return success;
      } catch (error) {
        cov_2blu1uqp5u().s[16]++;
        console.error('Failed to apply custom profile:', error);
        cov_2blu1uqp5u().s[17]++;
        return false;
      }
    });
    return function (_x) {
      return _ref5.apply(this, arguments);
    };
  }(), []));
  var startABTest = (cov_2blu1uqp5u().s[18]++, useCallback(function () {
    var _ref6 = _asyncToGenerator(function* (testConfig) {
      cov_2blu1uqp5u().f[5]++;
      cov_2blu1uqp5u().s[19]++;
      try {
        var success = (cov_2blu1uqp5u().s[20]++, yield advancedCustomizationManager.startABTest(testConfig.id, testConfig.variants, testConfig.duration));
        cov_2blu1uqp5u().s[21]++;
        return success;
      } catch (error) {
        cov_2blu1uqp5u().s[22]++;
        console.error('Failed to start A/B test:', error);
        cov_2blu1uqp5u().s[23]++;
        return false;
      }
    });
    return function (_x2) {
      return _ref6.apply(this, arguments);
    };
  }(), []));
  var exportSystemReport = (cov_2blu1uqp5u().s[24]++, useCallback(_asyncToGenerator(function* () {
    var format = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2blu1uqp5u().b[2][0]++, 'json');
    cov_2blu1uqp5u().f[6]++;
    cov_2blu1uqp5u().s[25]++;
    try {
      var report = (cov_2blu1uqp5u().s[26]++, {
        timestamp: Date.now(),
        systemHealth: state.systemHealth,
        optimization: state.optimization,
        customization: state.customization,
        planning: state.planning,
        insights: state.insights,
        dashboardData: yield dashboard.actions.exportData(format),
        customizationAnalytics: advancedCustomizationManager.getCustomizationAnalytics(),
        roadmap: futureEnhancementsManager.getStrategicRoadmap(),
        technologyTrends: futureEnhancementsManager.getTechnologyTrendAnalysis()
      });
      cov_2blu1uqp5u().s[27]++;
      switch (format) {
        case 'json':
          cov_2blu1uqp5u().b[3][0]++;
          cov_2blu1uqp5u().s[28]++;
          return JSON.stringify(report, null, 2);
        case 'pdf':
          cov_2blu1uqp5u().b[3][1]++;
          cov_2blu1uqp5u().s[29]++;
          return 'PDF export not implemented yet';
        case 'excel':
          cov_2blu1uqp5u().b[3][2]++;
          cov_2blu1uqp5u().s[30]++;
          return 'Excel export not implemented yet';
        default:
          cov_2blu1uqp5u().b[3][3]++;
          cov_2blu1uqp5u().s[31]++;
          return JSON.stringify(report, null, 2);
      }
    } catch (error) {
      cov_2blu1uqp5u().s[32]++;
      console.error('Failed to export system report:', error);
      cov_2blu1uqp5u().s[33]++;
      return '';
    }
  }), [state, dashboard.actions]));
  var optimizeAllSystems = (cov_2blu1uqp5u().s[34]++, useCallback(_asyncToGenerator(function* () {
    cov_2blu1uqp5u().f[7]++;
    cov_2blu1uqp5u().s[35]++;
    try {
      cov_2blu1uqp5u().s[36]++;
      console.log('Starting comprehensive system optimization...');
      cov_2blu1uqp5u().s[37]++;
      if ((cov_2blu1uqp5u().b[5][0]++, config.enableRealTimeMonitoring) && (cov_2blu1uqp5u().b[5][1]++, ai.state.isInitialized)) {
        cov_2blu1uqp5u().b[4][0]++;
        cov_2blu1uqp5u().s[38]++;
        yield ai.actions.optimizePerformance();
      } else {
        cov_2blu1uqp5u().b[4][1]++;
      }
      cov_2blu1uqp5u().s[39]++;
      if (edge.state.isInitialized) {
        cov_2blu1uqp5u().b[6][0]++;
        cov_2blu1uqp5u().s[40]++;
        yield edge.actions.preloadGlobally(['critical_content']);
      } else {
        cov_2blu1uqp5u().b[6][1]++;
      }
      cov_2blu1uqp5u().s[41]++;
      if (native.state.isInitialized) {
        cov_2blu1uqp5u().b[7][0]++;
        cov_2blu1uqp5u().s[42]++;
        yield native.actions.optimizeMemory(false);
      } else {
        cov_2blu1uqp5u().b[7][1]++;
      }
      cov_2blu1uqp5u().s[43]++;
      yield dashboard.actions.refreshMetrics();
      cov_2blu1uqp5u().s[44]++;
      console.log('Comprehensive system optimization completed');
    } catch (error) {
      cov_2blu1uqp5u().s[45]++;
      console.error('Failed to optimize all systems:', error);
    }
  }), [config, ai, edge, native, dashboard]));
  var getSystemRecommendations = (cov_2blu1uqp5u().s[46]++, useCallback(_asyncToGenerator(function* () {
    cov_2blu1uqp5u().f[8]++;
    cov_2blu1uqp5u().s[47]++;
    try {
      var recommendations = (cov_2blu1uqp5u().s[48]++, []);
      var customizationRecs = (cov_2blu1uqp5u().s[49]++, yield advancedCustomizationManager.getOptimizationRecommendations());
      cov_2blu1uqp5u().s[50]++;
      recommendations.push.apply(recommendations, _toConsumableArray(customizationRecs));
      var dashboardRecs = (cov_2blu1uqp5u().s[51]++, yield dashboard.actions.getOptimizationRecommendations());
      cov_2blu1uqp5u().s[52]++;
      recommendations.push.apply(recommendations, _toConsumableArray(dashboardRecs));
      var trendAnalysis = (cov_2blu1uqp5u().s[53]++, futureEnhancementsManager.getTechnologyTrendAnalysis());
      var adoptionRecs = (cov_2blu1uqp5u().s[54]++, trendAnalysis.adoptionRecommendations.filter(function (rec) {
        cov_2blu1uqp5u().f[9]++;
        cov_2blu1uqp5u().s[55]++;
        return (cov_2blu1uqp5u().b[8][0]++, rec.recommendation === 'adopt') || (cov_2blu1uqp5u().b[8][1]++, rec.recommendation === 'trial');
      }).map(function (rec) {
        cov_2blu1uqp5u().f[10]++;
        cov_2blu1uqp5u().s[56]++;
        return {
          category: 'Technology Adoption',
          recommendation: `${rec.recommendation.toUpperCase()}: ${rec.technology}`,
          impact: 'high',
          effort: 'medium',
          expectedImprovement: 20
        };
      }));
      cov_2blu1uqp5u().s[57]++;
      recommendations.push.apply(recommendations, _toConsumableArray(adoptionRecs));
      cov_2blu1uqp5u().s[58]++;
      return recommendations;
    } catch (error) {
      cov_2blu1uqp5u().s[59]++;
      console.error('Failed to get system recommendations:', error);
      cov_2blu1uqp5u().s[60]++;
      return [];
    }
  }), [dashboard.actions]));
  var scheduleEnhancement = (cov_2blu1uqp5u().s[61]++, useCallback(function () {
    var _ref0 = _asyncToGenerator(function* (enhancementId) {
      cov_2blu1uqp5u().f[11]++;
      cov_2blu1uqp5u().s[62]++;
      try {
        cov_2blu1uqp5u().s[63]++;
        console.log(`Scheduling enhancement: ${enhancementId}`);
        cov_2blu1uqp5u().s[64]++;
        return true;
      } catch (error) {
        cov_2blu1uqp5u().s[65]++;
        console.error('Failed to schedule enhancement:', error);
        cov_2blu1uqp5u().s[66]++;
        return false;
      }
    });
    return function (_x3) {
      return _ref0.apply(this, arguments);
    };
  }(), []));
  var getInvestmentAnalysis = (cov_2blu1uqp5u().s[67]++, useCallback(function () {
    cov_2blu1uqp5u().f[12]++;
    cov_2blu1uqp5u().s[68]++;
    return futureEnhancementsManager.getInvestmentAnalysis();
  }, []));
  var acknowledgeInsight = (cov_2blu1uqp5u().s[69]++, useCallback(function (insightId) {
    cov_2blu1uqp5u().f[13]++;
    cov_2blu1uqp5u().s[70]++;
    setState(function (prev) {
      cov_2blu1uqp5u().f[14]++;
      cov_2blu1uqp5u().s[71]++;
      return Object.assign({}, prev, {
        insights: prev.insights.filter(function (insight) {
          cov_2blu1uqp5u().f[15]++;
          cov_2blu1uqp5u().s[72]++;
          return insight.category !== insightId;
        })
      });
    });
  }, []));
  var updateControlCenterState = (cov_2blu1uqp5u().s[73]++, useCallback(_asyncToGenerator(function* () {
    cov_2blu1uqp5u().f[16]++;
    cov_2blu1uqp5u().s[74]++;
    try {
      var systemHealth = (cov_2blu1uqp5u().s[75]++, {
        overall: dashboard.summary.healthScore,
        phases: {
          phase1: dashboard.state.phaseStatus.phase1.health,
          phase2: dashboard.state.phaseStatus.phase2.health,
          phase3a: dashboard.state.phaseStatus.phase3a.health,
          phase3b: dashboard.state.phaseStatus.phase3b.health,
          phase3c: dashboard.state.phaseStatus.phase3c.health
        },
        alerts: dashboard.summary.activeAlerts,
        criticalIssues: dashboard.state.alerts.filter(function (alert) {
          cov_2blu1uqp5u().f[17]++;
          cov_2blu1uqp5u().s[76]++;
          return alert.severity === 'critical';
        }).length
      });
      var optimization = (cov_2blu1uqp5u().s[77]++, {
        totalImprovement: dashboard.summary.totalImprovement,
        activeOptimizations: 5,
        performanceScore: dashboard.summary.overallScore,
        efficiency: Math.round(dashboard.summary.overallScore / 500 * 100)
      });
      var customizationAnalytics = (cov_2blu1uqp5u().s[78]++, advancedCustomizationManager.getCustomizationAnalytics());
      var customization = (cov_2blu1uqp5u().s[79]++, {
        activeProfile: customizationAnalytics.activeProfile,
        availableProfiles: Object.keys(customizationAnalytics.profilePerformance).length,
        abTestsRunning: Object.values(customizationAnalytics.abTestResults).filter(function (test) {
          cov_2blu1uqp5u().f[18]++;
          cov_2blu1uqp5u().s[80]++;
          return test.status === 'running';
        }).length,
        customizationScore: 85
      });
      var roadmap = (cov_2blu1uqp5u().s[81]++, futureEnhancementsManager.getStrategicRoadmap());
      var investmentAnalysis = (cov_2blu1uqp5u().s[82]++, futureEnhancementsManager.getInvestmentAnalysis());
      var planning = (cov_2blu1uqp5u().s[83]++, {
        roadmapPhases: roadmap.phases.length,
        pipelineEnhancements: futureEnhancementsManager.getInnovationPipeline().pipeline.length,
        investmentRequired: investmentAnalysis.totalInvestment,
        expectedROI: investmentAnalysis.roi.mediumTerm
      });
      var insights = (cov_2blu1uqp5u().s[84]++, yield generateSystemInsights(systemHealth, optimization, customization, planning));
      cov_2blu1uqp5u().s[85]++;
      setState(function (prev) {
        cov_2blu1uqp5u().f[19]++;
        cov_2blu1uqp5u().s[86]++;
        return Object.assign({}, prev, {
          systemHealth: systemHealth,
          optimization: optimization,
          customization: customization,
          planning: planning,
          insights: insights
        });
      });
    } catch (error) {
      cov_2blu1uqp5u().s[87]++;
      console.error('Failed to update control center state:', error);
    }
  }), [dashboard]));
  cov_2blu1uqp5u().s[88]++;
  var generateSystemInsights = function () {
    var _ref10 = _asyncToGenerator(function* (systemHealth, optimization, customization, planning) {
      cov_2blu1uqp5u().f[20]++;
      var insights = (cov_2blu1uqp5u().s[89]++, []);
      cov_2blu1uqp5u().s[90]++;
      if (systemHealth.overall > 95) {
        cov_2blu1uqp5u().b[9][0]++;
        cov_2blu1uqp5u().s[91]++;
        insights.push({
          category: 'System Health',
          message: 'All systems operating at peak performance',
          priority: 'low',
          actionable: false
        });
      } else {
        cov_2blu1uqp5u().b[9][1]++;
      }
      cov_2blu1uqp5u().s[92]++;
      if (optimization.totalImprovement > 300) {
        cov_2blu1uqp5u().b[10][0]++;
        cov_2blu1uqp5u().s[93]++;
        insights.push({
          category: 'Performance',
          message: 'Exceptional performance gains achieved across all phases',
          priority: 'medium',
          actionable: false
        });
      } else {
        cov_2blu1uqp5u().b[10][1]++;
      }
      cov_2blu1uqp5u().s[94]++;
      if (customization.abTestsRunning > 0) {
        cov_2blu1uqp5u().b[11][0]++;
        cov_2blu1uqp5u().s[95]++;
        insights.push({
          category: 'Customization',
          message: `${customization.abTestsRunning} A/B tests currently running`,
          priority: 'medium',
          actionable: true
        });
      } else {
        cov_2blu1uqp5u().b[11][1]++;
      }
      cov_2blu1uqp5u().s[96]++;
      if (planning.expectedROI > 200) {
        cov_2blu1uqp5u().b[12][0]++;
        cov_2blu1uqp5u().s[97]++;
        insights.push({
          category: 'Planning',
          message: 'High ROI potential identified in future enhancements',
          priority: 'high',
          actionable: true
        });
      } else {
        cov_2blu1uqp5u().b[12][1]++;
      }
      cov_2blu1uqp5u().s[98]++;
      return insights;
    });
    return function generateSystemInsights(_x4, _x5, _x6, _x7) {
      return _ref10.apply(this, arguments);
    };
  }();
  cov_2blu1uqp5u().s[99]++;
  useEffect(function () {
    cov_2blu1uqp5u().f[21]++;
    cov_2blu1uqp5u().s[100]++;
    if (!config.enableRealTimeMonitoring) {
      cov_2blu1uqp5u().b[13][0]++;
      cov_2blu1uqp5u().s[101]++;
      return;
    } else {
      cov_2blu1uqp5u().b[13][1]++;
    }
    var interval = (cov_2blu1uqp5u().s[102]++, setInterval(function () {
      cov_2blu1uqp5u().f[22]++;
      cov_2blu1uqp5u().s[103]++;
      updateControlCenterState();
    }, config.updateInterval));
    cov_2blu1uqp5u().s[104]++;
    return function () {
      cov_2blu1uqp5u().f[23]++;
      cov_2blu1uqp5u().s[105]++;
      return clearInterval(interval);
    };
  }, [config.enableRealTimeMonitoring, config.updateInterval, updateControlCenterState]);
  cov_2blu1uqp5u().s[106]++;
  useEffect(function () {
    cov_2blu1uqp5u().f[24]++;
    cov_2blu1uqp5u().s[107]++;
    var initialize = function () {
      var _ref11 = _asyncToGenerator(function* () {
        cov_2blu1uqp5u().f[25]++;
        cov_2blu1uqp5u().s[108]++;
        yield updateControlCenterState();
        cov_2blu1uqp5u().s[109]++;
        setState(function (prev) {
          cov_2blu1uqp5u().f[26]++;
          cov_2blu1uqp5u().s[110]++;
          return Object.assign({}, prev, {
            isInitialized: true
          });
        });
      });
      return function initialize() {
        return _ref11.apply(this, arguments);
      };
    }();
    cov_2blu1uqp5u().s[111]++;
    initialize();
  }, []);
  var summary = (cov_2blu1uqp5u().s[112]++, useMemo(function () {
    var _state$insights$find, _state$insights$find2;
    cov_2blu1uqp5u().f[27]++;
    var overallHealth = (cov_2blu1uqp5u().s[113]++, state.systemHealth.overall > 95 ? (cov_2blu1uqp5u().b[14][0]++, 'Excellent') : (cov_2blu1uqp5u().b[14][1]++, state.systemHealth.overall > 85 ? (cov_2blu1uqp5u().b[15][0]++, 'Good') : (cov_2blu1uqp5u().b[15][1]++, state.systemHealth.overall > 70 ? (cov_2blu1uqp5u().b[16][0]++, 'Fair') : (cov_2blu1uqp5u().b[16][1]++, 'Poor'))));
    var performanceGrade = (cov_2blu1uqp5u().s[114]++, state.optimization.performanceScore > 450 ? (cov_2blu1uqp5u().b[17][0]++, 'A+') : (cov_2blu1uqp5u().b[17][1]++, state.optimization.performanceScore > 400 ? (cov_2blu1uqp5u().b[18][0]++, 'A') : (cov_2blu1uqp5u().b[18][1]++, state.optimization.performanceScore > 350 ? (cov_2blu1uqp5u().b[19][0]++, 'B+') : (cov_2blu1uqp5u().b[19][1]++, state.optimization.performanceScore > 300 ? (cov_2blu1uqp5u().b[20][0]++, 'B') : (cov_2blu1uqp5u().b[20][1]++, 'C')))));
    var optimizationLevel = (cov_2blu1uqp5u().s[115]++, state.optimization.efficiency > 90 ? (cov_2blu1uqp5u().b[21][0]++, 'Maximum') : (cov_2blu1uqp5u().b[21][1]++, state.optimization.efficiency > 75 ? (cov_2blu1uqp5u().b[22][0]++, 'High') : (cov_2blu1uqp5u().b[22][1]++, state.optimization.efficiency > 60 ? (cov_2blu1uqp5u().b[23][0]++, 'Medium') : (cov_2blu1uqp5u().b[23][1]++, 'Low'))));
    var futureReadiness = (cov_2blu1uqp5u().s[116]++, state.planning.expectedROI > 200 ? (cov_2blu1uqp5u().b[24][0]++, 'Excellent') : (cov_2blu1uqp5u().b[24][1]++, state.planning.expectedROI > 150 ? (cov_2blu1uqp5u().b[25][0]++, 'Good') : (cov_2blu1uqp5u().b[25][1]++, state.planning.expectedROI > 100 ? (cov_2blu1uqp5u().b[26][0]++, 'Fair') : (cov_2blu1uqp5u().b[26][1]++, 'Poor'))));
    var topPriority = (cov_2blu1uqp5u().s[117]++, (cov_2blu1uqp5u().b[27][0]++, (_state$insights$find = state.insights.find(function (insight) {
      cov_2blu1uqp5u().f[28]++;
      cov_2blu1uqp5u().s[118]++;
      return insight.priority === 'critical';
    })) == null ? void 0 : _state$insights$find.message) || (cov_2blu1uqp5u().b[27][1]++, (_state$insights$find2 = state.insights.find(function (insight) {
      cov_2blu1uqp5u().f[29]++;
      cov_2blu1uqp5u().s[119]++;
      return insight.priority === 'high';
    })) == null ? void 0 : _state$insights$find2.message) || (cov_2blu1uqp5u().b[27][2]++, 'No critical issues identified'));
    cov_2blu1uqp5u().s[120]++;
    return {
      overallHealth: overallHealth,
      performanceGrade: performanceGrade,
      optimizationLevel: optimizationLevel,
      futureReadiness: futureReadiness,
      topPriority: topPriority
    };
  }, [state]));
  cov_2blu1uqp5u().s[121]++;
  return useMemo(function () {
    cov_2blu1uqp5u().f[30]++;
    cov_2blu1uqp5u().s[122]++;
    return {
      state: state,
      dashboard: dashboard,
      ai: ai,
      edge: edge,
      native: native,
      actions: {
        switchMode: switchMode,
        applyCustomProfile: applyCustomProfile,
        startABTest: startABTest,
        exportSystemReport: exportSystemReport,
        optimizeAllSystems: optimizeAllSystems,
        getSystemRecommendations: getSystemRecommendations,
        scheduleEnhancement: scheduleEnhancement,
        getInvestmentAnalysis: getInvestmentAnalysis,
        acknowledgeInsight: acknowledgeInsight
      },
      config: config,
      summary: summary
    };
  }, [state, dashboard, ai, edge, native, switchMode, applyCustomProfile, startABTest, exportSystemReport, optimizeAllSystems, getSystemRecommendations, scheduleEnhancement, getInvestmentAnalysis, acknowledgeInsight, config, summary]);
}
export default useOptimizationControlCenter;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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