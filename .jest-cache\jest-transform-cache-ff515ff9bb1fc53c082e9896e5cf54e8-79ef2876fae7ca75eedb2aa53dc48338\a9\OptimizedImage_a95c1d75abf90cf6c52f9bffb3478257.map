{"version": 3, "names": ["React", "useState", "useCallback", "memo", "View", "StyleSheet", "ActivityIndicator", "Text", "Image", "ExpoImage", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_294oa57f0x", "s", "primary", "white", "dark", "gray", "lightGray", "OptimizedImage", "_ref", "source", "style", "placeholder", "fallback", "_ref$resizeMode", "resizeMode", "b", "_ref$priority", "priority", "_ref$cachePolicy", "cachePolicy", "_ref$lazy", "lazy", "onLoad", "onError", "accessibilityLabel", "testID", "f", "_ref2", "_ref3", "_slicedToArray", "loading", "setLoading", "_ref4", "_ref5", "error", "setError", "_ref6", "_ref7", "imageLoaded", "setImageLoaded", "handleLoad", "handleError", "errorEvent", "getImageSource", "uri", "getOptimizedSource", "imageSource", "Array", "isArray", "startsWith", "optimizedUri", "includes", "Object", "assign", "renderPlaceholder", "styles", "image", "contentFit", "transition", "children", "size", "color", "renderError", "<PERSON><PERSON><PERSON><PERSON>", "errorText", "container", "hidden", "accessible", "placeholderContentFit", "displayName", "create", "position", "width", "height", "opacity", "top", "left", "right", "bottom", "backgroundColor", "justifyContent", "alignItems", "zIndex", "padding", "fontSize", "textAlign"], "sources": ["OptimizedImage.tsx"], "sourcesContent": ["import React, { useState, useCallback, memo } from 'react';\nimport {\n  View,\n  Image,\n  StyleSheet,\n  ActivityIndicator,\n  Text,\n  ImageStyle,\n  ViewStyle,\n  ImageSourcePropType,\n} from 'react-native';\nimport { Image as ExpoImage } from 'expo-image';\n\nconst colors = {\n  primary: '#23ba16',\n  white: '#ffffff',\n  dark: '#171717',\n  gray: '#6b7280',\n  lightGray: '#f9fafb',\n};\n\ninterface OptimizedImageProps {\n  source: ImageSourcePropType | string;\n  style?: ImageStyle | ViewStyle;\n  placeholder?: string;\n  fallback?: string;\n  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';\n  priority?: 'low' | 'normal' | 'high';\n  cachePolicy?: 'memory' | 'disk' | 'memory-disk' | 'none';\n  lazy?: boolean;\n  onLoad?: () => void;\n  onError?: (error: any) => void;\n  accessibilityLabel?: string;\n  testID?: string;\n}\n\nconst OptimizedImage: React.FC<OptimizedImageProps> = memo(({\n  source,\n  style,\n  placeholder,\n  fallback,\n  resizeMode = 'cover',\n  priority = 'normal',\n  cachePolicy = 'memory-disk',\n  lazy = true,\n  onLoad,\n  onError,\n  accessibilityLabel,\n  testID,\n}) => {\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(false);\n  const [imageLoaded, setImageLoaded] = useState(false);\n\n  const handleLoad = useCallback(() => {\n    setLoading(false);\n    setImageLoaded(true);\n    onLoad?.();\n  }, [onLoad]);\n\n  const handleError = useCallback((errorEvent: any) => {\n    setLoading(false);\n    setError(true);\n    onError?.(errorEvent);\n  }, [onError]);\n\n  const getImageSource = () => {\n    if (error && fallback) {\n      return { uri: fallback };\n    }\n    \n    if (typeof source === 'string') {\n      return { uri: source };\n    }\n    \n    return source;\n  };\n\n  const getOptimizedSource = () => {\n    const imageSource = getImageSource();\n    \n    if (typeof imageSource === 'object' && !Array.isArray(imageSource) && imageSource.uri) {\n      // Add optimization parameters for remote images\n      const uri = imageSource.uri;\n      \n      // Check if it's a remote URL that supports optimization\n      if (uri.startsWith('http')) {\n        // Add image optimization parameters\n        // This is a generic example - adjust based on your CDN/image service\n        const optimizedUri = `${uri}${uri.includes('?') ? '&' : '?'}auto=format&fit=crop&w=800&q=80`;\n        return { ...imageSource, uri: optimizedUri };\n      }\n    }\n    \n    return imageSource;\n  };\n\n  const renderPlaceholder = () => {\n    if (placeholder) {\n      return (\n        <ExpoImage\n          source={{ uri: placeholder }}\n          style={[styles.image, style as any]}\n          contentFit={resizeMode === 'repeat' ? 'cover' : resizeMode as any}\n          transition={200}\n        />\n      );\n    }\n    \n    return (\n      <View style={[styles.placeholder, style]}>\n        <ActivityIndicator size=\"small\" color={colors.primary} />\n      </View>\n    );\n  };\n\n  const renderError = () => (\n    <View style={[styles.errorContainer, style]}>\n      <Text style={styles.errorText}>Failed to load image</Text>\n    </View>\n  );\n\n  if (error && !fallback) {\n    return renderError();\n  }\n\n  return (\n    <View style={[styles.container, style]} testID={testID}>\n      {/* Show placeholder while loading */}\n      {loading && renderPlaceholder()}\n      \n      {/* Main image */}\n      <ExpoImage\n        source={getOptimizedSource()}\n        style={[\n          styles.image,\n          style as any,\n          loading && styles.hidden,\n        ]}\n        contentFit={resizeMode === 'repeat' ? 'cover' : resizeMode as any}\n        priority={priority}\n        cachePolicy={cachePolicy}\n        transition={300}\n        onLoad={handleLoad}\n        onError={handleError}\n        accessible={!!accessibilityLabel}\n        accessibilityLabel={accessibilityLabel}\n        placeholder={placeholder}\n        placeholderContentFit={resizeMode === 'repeat' ? 'cover' : resizeMode as any}\n      />\n    </View>\n  );\n});\n\nOptimizedImage.displayName = 'OptimizedImage';\n\nconst styles = StyleSheet.create({\n  container: {\n    position: 'relative',\n  },\n  image: {\n    width: '100%',\n    height: '100%',\n  },\n  hidden: {\n    opacity: 0,\n  },\n  placeholder: {\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    backgroundColor: colors.lightGray,\n    justifyContent: 'center',\n    alignItems: 'center',\n    zIndex: 1,\n  },\n  errorContainer: {\n    backgroundColor: colors.lightGray,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n  },\n  errorText: {\n    fontSize: 12,\n    color: colors.gray,\n    textAlign: 'center',\n  },\n});\n\nexport default OptimizedImage;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,EAAEC,IAAI,QAAQ,OAAO;AAC1D,SACEC,IAAI,EAEJC,UAAU,EACVC,iBAAiB,EACjBC,IAAI,QAIC,cAAc;AACrB,SAASC,KAAK,IAAIC,SAAS,QAAQ,YAAY;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEhD,IAAMC,MAAM,IAAAC,cAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE;AACb,CAAC;AAiBD,IAAMC,cAA6C,IAAAP,cAAA,GAAAC,CAAA,OAAGb,IAAI,CAAC,UAAAoB,IAAA,EAarD;EAAA,IAZJC,MAAM,GAAAD,IAAA,CAANC,MAAM;IACNC,KAAK,GAAAF,IAAA,CAALE,KAAK;IACLC,WAAW,GAAAH,IAAA,CAAXG,WAAW;IACXC,QAAQ,GAAAJ,IAAA,CAARI,QAAQ;IAAAC,eAAA,GAAAL,IAAA,CACRM,UAAU;IAAVA,UAAU,GAAAD,eAAA,eAAAb,cAAA,GAAAe,CAAA,UAAG,OAAO,IAAAF,eAAA;IAAAG,aAAA,GAAAR,IAAA,CACpBS,QAAQ;IAARA,QAAQ,GAAAD,aAAA,eAAAhB,cAAA,GAAAe,CAAA,UAAG,QAAQ,IAAAC,aAAA;IAAAE,gBAAA,GAAAV,IAAA,CACnBW,WAAW;IAAXA,WAAW,GAAAD,gBAAA,eAAAlB,cAAA,GAAAe,CAAA,UAAG,aAAa,IAAAG,gBAAA;IAAAE,SAAA,GAAAZ,IAAA,CAC3Ba,IAAI;IAAJA,IAAI,GAAAD,SAAA,eAAApB,cAAA,GAAAe,CAAA,UAAG,IAAI,IAAAK,SAAA;IACXE,MAAM,GAAAd,IAAA,CAANc,MAAM;IACNC,OAAO,GAAAf,IAAA,CAAPe,OAAO;IACPC,kBAAkB,GAAAhB,IAAA,CAAlBgB,kBAAkB;IAClBC,MAAM,GAAAjB,IAAA,CAANiB,MAAM;EAAAzB,cAAA,GAAA0B,CAAA;EAEN,IAAAC,KAAA,IAAA3B,cAAA,GAAAC,CAAA,OAA8Bf,QAAQ,CAAC,IAAI,CAAC;IAAA0C,KAAA,GAAAC,cAAA,CAAAF,KAAA;IAArCG,OAAO,GAAAF,KAAA;IAAEG,UAAU,GAAAH,KAAA;EAC1B,IAAAI,KAAA,IAAAhC,cAAA,GAAAC,CAAA,OAA0Bf,QAAQ,CAAC,KAAK,CAAC;IAAA+C,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAAlCE,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EACtB,IAAAG,KAAA,IAAApC,cAAA,GAAAC,CAAA,OAAsCf,QAAQ,CAAC,KAAK,CAAC;IAAAmD,KAAA,GAAAR,cAAA,CAAAO,KAAA;IAA9CE,WAAW,GAAAD,KAAA;IAAEE,cAAc,GAAAF,KAAA;EAElC,IAAMG,UAAU,IAAAxC,cAAA,GAAAC,CAAA,OAAGd,WAAW,CAAC,YAAM;IAAAa,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAC,CAAA;IACnC8B,UAAU,CAAC,KAAK,CAAC;IAAC/B,cAAA,GAAAC,CAAA;IAClBsC,cAAc,CAAC,IAAI,CAAC;IAACvC,cAAA,GAAAC,CAAA;IACrBqB,MAAM,YAANA,MAAM,CAAG,CAAC;EACZ,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EAEZ,IAAMmB,WAAW,IAAAzC,cAAA,GAAAC,CAAA,OAAGd,WAAW,CAAC,UAACuD,UAAe,EAAK;IAAA1C,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAC,CAAA;IACnD8B,UAAU,CAAC,KAAK,CAAC;IAAC/B,cAAA,GAAAC,CAAA;IAClBkC,QAAQ,CAAC,IAAI,CAAC;IAACnC,cAAA,GAAAC,CAAA;IACfsB,OAAO,YAAPA,OAAO,CAAGmB,UAAU,CAAC;EACvB,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EAACvB,cAAA,GAAAC,CAAA;EAEd,IAAM0C,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAAA3C,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAC,CAAA;IAC3B,IAAI,CAAAD,cAAA,GAAAe,CAAA,UAAAmB,KAAK,MAAAlC,cAAA,GAAAe,CAAA,UAAIH,QAAQ,GAAE;MAAAZ,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MACrB,OAAO;QAAE2C,GAAG,EAAEhC;MAAS,CAAC;IAC1B,CAAC;MAAAZ,cAAA,GAAAe,CAAA;IAAA;IAAAf,cAAA,GAAAC,CAAA;IAED,IAAI,OAAOQ,MAAM,KAAK,QAAQ,EAAE;MAAAT,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MAC9B,OAAO;QAAE2C,GAAG,EAAEnC;MAAO,CAAC;IACxB,CAAC;MAAAT,cAAA,GAAAe,CAAA;IAAA;IAAAf,cAAA,GAAAC,CAAA;IAED,OAAOQ,MAAM;EACf,CAAC;EAACT,cAAA,GAAAC,CAAA;EAEF,IAAM4C,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;IAAA7C,cAAA,GAAA0B,CAAA;IAC/B,IAAMoB,WAAW,IAAA9C,cAAA,GAAAC,CAAA,QAAG0C,cAAc,CAAC,CAAC;IAAC3C,cAAA,GAAAC,CAAA;IAErC,IAAI,CAAAD,cAAA,GAAAe,CAAA,iBAAO+B,WAAW,KAAK,QAAQ,MAAA9C,cAAA,GAAAe,CAAA,UAAI,CAACgC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,MAAA9C,cAAA,GAAAe,CAAA,UAAI+B,WAAW,CAACF,GAAG,GAAE;MAAA5C,cAAA,GAAAe,CAAA;MAErF,IAAM6B,GAAG,IAAA5C,cAAA,GAAAC,CAAA,QAAG6C,WAAW,CAACF,GAAG;MAAC5C,cAAA,GAAAC,CAAA;MAG5B,IAAI2C,GAAG,CAACK,UAAU,CAAC,MAAM,CAAC,EAAE;QAAAjD,cAAA,GAAAe,CAAA;QAG1B,IAAMmC,YAAY,IAAAlD,cAAA,GAAAC,CAAA,QAAG,GAAG2C,GAAG,GAAGA,GAAG,CAACO,QAAQ,CAAC,GAAG,CAAC,IAAAnD,cAAA,GAAAe,CAAA,WAAG,GAAG,KAAAf,cAAA,GAAAe,CAAA,WAAG,GAAG,kCAAiC;QAACf,cAAA,GAAAC,CAAA;QAC7F,OAAAmD,MAAA,CAAAC,MAAA,KAAYP,WAAW;UAAEF,GAAG,EAAEM;QAAY;MAC5C,CAAC;QAAAlD,cAAA,GAAAe,CAAA;MAAA;IACH,CAAC;MAAAf,cAAA,GAAAe,CAAA;IAAA;IAAAf,cAAA,GAAAC,CAAA;IAED,OAAO6C,WAAW;EACpB,CAAC;EAAC9C,cAAA,GAAAC,CAAA;EAEF,IAAMqD,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAAAtD,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAC,CAAA;IAC9B,IAAIU,WAAW,EAAE;MAAAX,cAAA,GAAAe,CAAA;MAAAf,cAAA,GAAAC,CAAA;MACf,OACEL,IAAA,CAACF,SAAS;QACRe,MAAM,EAAE;UAAEmC,GAAG,EAAEjC;QAAY,CAAE;QAC7BD,KAAK,EAAE,CAAC6C,MAAM,CAACC,KAAK,EAAE9C,KAAK,CAAS;QACpC+C,UAAU,EAAE3C,UAAU,KAAK,QAAQ,IAAAd,cAAA,GAAAe,CAAA,WAAG,OAAO,KAAAf,cAAA,GAAAe,CAAA,WAAGD,UAAU,CAAQ;QAClE4C,UAAU,EAAE;MAAI,CACjB,CAAC;IAEN,CAAC;MAAA1D,cAAA,GAAAe,CAAA;IAAA;IAAAf,cAAA,GAAAC,CAAA;IAED,OACEL,IAAA,CAACP,IAAI;MAACqB,KAAK,EAAE,CAAC6C,MAAM,CAAC5C,WAAW,EAAED,KAAK,CAAE;MAAAiD,QAAA,EACvC/D,IAAA,CAACL,iBAAiB;QAACqE,IAAI,EAAC,OAAO;QAACC,KAAK,EAAE9D,MAAM,CAACG;MAAQ,CAAE;IAAC,CACrD,CAAC;EAEX,CAAC;EAACF,cAAA,GAAAC,CAAA;EAEF,IAAM6D,WAAW,GAAG,SAAdA,WAAWA,CAAA,EACf;IAAA9D,cAAA,GAAA0B,CAAA;IAAA1B,cAAA,GAAAC,CAAA;IAAA,OAAAL,IAAA,CAACP,IAAI;MAACqB,KAAK,EAAE,CAAC6C,MAAM,CAACQ,cAAc,EAAErD,KAAK,CAAE;MAAAiD,QAAA,EAC1C/D,IAAA,CAACJ,IAAI;QAACkB,KAAK,EAAE6C,MAAM,CAACS,SAAU;QAAAL,QAAA,EAAC;MAAoB,CAAM;IAAC,CACtD,CAAC;EAAD,CACP;EAAC3D,cAAA,GAAAC,CAAA;EAEF,IAAI,CAAAD,cAAA,GAAAe,CAAA,WAAAmB,KAAK,MAAAlC,cAAA,GAAAe,CAAA,WAAI,CAACH,QAAQ,GAAE;IAAAZ,cAAA,GAAAe,CAAA;IAAAf,cAAA,GAAAC,CAAA;IACtB,OAAO6D,WAAW,CAAC,CAAC;EACtB,CAAC;IAAA9D,cAAA,GAAAe,CAAA;EAAA;EAAAf,cAAA,GAAAC,CAAA;EAED,OACEH,KAAA,CAACT,IAAI;IAACqB,KAAK,EAAE,CAAC6C,MAAM,CAACU,SAAS,EAAEvD,KAAK,CAAE;IAACe,MAAM,EAAEA,MAAO;IAAAkC,QAAA,GAEpD,CAAA3D,cAAA,GAAAe,CAAA,WAAAe,OAAO,MAAA9B,cAAA,GAAAe,CAAA,WAAIuC,iBAAiB,CAAC,CAAC,GAG/B1D,IAAA,CAACF,SAAS;MACRe,MAAM,EAAEoC,kBAAkB,CAAC,CAAE;MAC7BnC,KAAK,EAAE,CACL6C,MAAM,CAACC,KAAK,EACZ9C,KAAK,EACL,CAAAV,cAAA,GAAAe,CAAA,WAAAe,OAAO,MAAA9B,cAAA,GAAAe,CAAA,WAAIwC,MAAM,CAACW,MAAM,EACxB;MACFT,UAAU,EAAE3C,UAAU,KAAK,QAAQ,IAAAd,cAAA,GAAAe,CAAA,WAAG,OAAO,KAAAf,cAAA,GAAAe,CAAA,WAAGD,UAAU,CAAQ;MAClEG,QAAQ,EAAEA,QAAS;MACnBE,WAAW,EAAEA,WAAY;MACzBuC,UAAU,EAAE,GAAI;MAChBpC,MAAM,EAAEkB,UAAW;MACnBjB,OAAO,EAAEkB,WAAY;MACrB0B,UAAU,EAAE,CAAC,CAAC3C,kBAAmB;MACjCA,kBAAkB,EAAEA,kBAAmB;MACvCb,WAAW,EAAEA,WAAY;MACzByD,qBAAqB,EAAEtD,UAAU,KAAK,QAAQ,IAAAd,cAAA,GAAAe,CAAA,WAAG,OAAO,KAAAf,cAAA,GAAAe,CAAA,WAAGD,UAAU;IAAQ,CAC9E,CAAC;EAAA,CACE,CAAC;AAEX,CAAC,CAAC;AAACd,cAAA,GAAAC,CAAA;AAEHM,cAAc,CAAC8D,WAAW,GAAG,gBAAgB;AAE7C,IAAMd,MAAM,IAAAvD,cAAA,GAAAC,CAAA,QAAGX,UAAU,CAACgF,MAAM,CAAC;EAC/BL,SAAS,EAAE;IACTM,QAAQ,EAAE;EACZ,CAAC;EACDf,KAAK,EAAE;IACLgB,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE;EACV,CAAC;EACDP,MAAM,EAAE;IACNQ,OAAO,EAAE;EACX,CAAC;EACD/D,WAAW,EAAE;IACX4D,QAAQ,EAAE,UAAU;IACpBI,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,eAAe,EAAEhF,MAAM,CAACO,SAAS;IACjC0E,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,MAAM,EAAE;EACV,CAAC;EACDnB,cAAc,EAAE;IACdgB,eAAe,EAAEhF,MAAM,CAACO,SAAS;IACjC0E,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBE,OAAO,EAAE;EACX,CAAC;EACDnB,SAAS,EAAE;IACToB,QAAQ,EAAE,EAAE;IACZvB,KAAK,EAAE9D,MAAM,CAACM,IAAI;IAClBgF,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAe9E,cAAc", "ignoreList": []}