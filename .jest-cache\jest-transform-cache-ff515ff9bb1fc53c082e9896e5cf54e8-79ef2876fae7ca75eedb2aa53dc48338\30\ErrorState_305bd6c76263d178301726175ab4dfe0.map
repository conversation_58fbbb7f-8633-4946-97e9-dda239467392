{"version": 3, "names": ["React", "View", "Text", "StyleSheet", "Triangle<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_14vhv5qoaj", "s", "primary", "white", "dark", "gray", "lightGray", "red", "ErrorState", "_ref", "message", "onRetry", "_ref$retryText", "retryText", "b", "f", "style", "styles", "container", "children", "content", "iconContainer", "size", "color", "title", "onPress", "retryButton", "create", "flex", "backgroundColor", "alignItems", "justifyContent", "padding", "max<PERSON><PERSON><PERSON>", "width", "height", "borderRadius", "marginBottom", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "elevation", "fontSize", "fontFamily", "textAlign", "lineHeight", "min<PERSON><PERSON><PERSON>"], "sources": ["ErrorState.tsx"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet } from 'react-native';\nimport { TriangleAlert as AlertTriangle } from 'lucide-react-native';\nimport Button from './Button';\n\nconst colors = {\n  primary: '#23ba16',\n  white: '#ffffff',\n  dark: '#171717',\n  gray: '#6b7280',\n  lightGray: '#f9fafb',\n  red: '#ef4444',\n};\n\ninterface ErrorStateProps {\n  message: string;\n  onRetry?: () => void;\n  retryText?: string;\n}\n\nexport default function ErrorState({ \n  message, \n  onRetry, \n  retryText = 'Try Again' \n}: ErrorStateProps) {\n  return (\n    <View style={styles.container}>\n      <View style={styles.content}>\n        <View style={styles.iconContainer}>\n          <AlertTriangle size={48} color={colors.red} />\n        </View>\n        <Text style={styles.title}>Something went wrong</Text>\n        <Text style={styles.message}>{message}</Text>\n        {onRetry && (\n          <Button\n            title={retryText}\n            onPress={onRetry}\n            style={styles.retryButton}\n          />\n        )}\n      </View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: colors.lightGray,\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: 24,\n  },\n  content: {\n    alignItems: 'center',\n    maxWidth: 280,\n  },\n  iconContainer: {\n    width: 80,\n    height: 80,\n    borderRadius: 40,\n    backgroundColor: colors.white,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginBottom: 24,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 8,\n    elevation: 3,\n  },\n  title: {\n    fontSize: 20,\n    fontFamily: 'Inter-Bold',\n    color: colors.dark,\n    marginBottom: 8,\n    textAlign: 'center',\n  },\n  message: {\n    fontSize: 16,\n    fontFamily: 'Inter-Regular',\n    color: colors.gray,\n    textAlign: 'center',\n    lineHeight: 22,\n    marginBottom: 24,\n  },\n  retryButton: {\n    minWidth: 120,\n  },\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,cAAc;AACrD,SAASC,aAAa,IAAIC,aAAa,QAAQ,qBAAqB;AACpE,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,MAAM,IAAAC,cAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,SAAS;EACpBC,GAAG,EAAE;AACP,CAAC;AAQD,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAId;EAAA,IAHlBC,OAAO,GAAAD,IAAA,CAAPC,OAAO;IACPC,OAAO,GAAAF,IAAA,CAAPE,OAAO;IAAAC,cAAA,GAAAH,IAAA,CACPI,SAAS;IAATA,SAAS,GAAAD,cAAA,eAAAZ,cAAA,GAAAc,CAAA,UAAG,WAAW,IAAAF,cAAA;EAAAZ,cAAA,GAAAe,CAAA;EAAAf,cAAA,GAAAC,CAAA;EAEvB,OACEL,IAAA,CAACP,IAAI;IAAC2B,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EAC5BrB,KAAA,CAACT,IAAI;MAAC2B,KAAK,EAAEC,MAAM,CAACG,OAAQ;MAAAD,QAAA,GAC1BvB,IAAA,CAACP,IAAI;QAAC2B,KAAK,EAAEC,MAAM,CAACI,aAAc;QAAAF,QAAA,EAChCvB,IAAA,CAACH,aAAa;UAAC6B,IAAI,EAAE,EAAG;UAACC,KAAK,EAAExB,MAAM,CAACQ;QAAI,CAAE;MAAC,CAC1C,CAAC,EACPX,IAAA,CAACN,IAAI;QAAC0B,KAAK,EAAEC,MAAM,CAACO,KAAM;QAAAL,QAAA,EAAC;MAAoB,CAAM,CAAC,EACtDvB,IAAA,CAACN,IAAI;QAAC0B,KAAK,EAAEC,MAAM,CAACP,OAAQ;QAAAS,QAAA,EAAET;MAAO,CAAO,CAAC,EAC5C,CAAAV,cAAA,GAAAc,CAAA,UAAAH,OAAO,MAAAX,cAAA,GAAAc,CAAA,UACNlB,IAAA,CAACF,MAAM;QACL8B,KAAK,EAAEX,SAAU;QACjBY,OAAO,EAAEd,OAAQ;QACjBK,KAAK,EAAEC,MAAM,CAACS;MAAY,CAC3B,CAAC,CACH;IAAA,CACG;EAAC,CACH,CAAC;AAEX;AAEA,IAAMT,MAAM,IAAAjB,cAAA,GAAAC,CAAA,OAAGV,UAAU,CAACoC,MAAM,CAAC;EAC/BT,SAAS,EAAE;IACTU,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE9B,MAAM,CAACO,SAAS;IACjCwB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE;EACX,CAAC;EACDZ,OAAO,EAAE;IACPU,UAAU,EAAE,QAAQ;IACpBG,QAAQ,EAAE;EACZ,CAAC;EACDZ,aAAa,EAAE;IACba,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBP,eAAe,EAAE9B,MAAM,CAACI,KAAK;IAC7B2B,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBM,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MACZL,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDK,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDlB,KAAK,EAAE;IACLmB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,YAAY;IACxBrB,KAAK,EAAExB,MAAM,CAACK,IAAI;IAClBiC,YAAY,EAAE,CAAC;IACfQ,SAAS,EAAE;EACb,CAAC;EACDnC,OAAO,EAAE;IACPiC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3BrB,KAAK,EAAExB,MAAM,CAACM,IAAI;IAClBwC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,EAAE;IACdT,YAAY,EAAE;EAChB,CAAC;EACDX,WAAW,EAAE;IACXqB,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}