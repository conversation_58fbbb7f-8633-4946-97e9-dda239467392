a00bfe59ff76b1568f28f255d7007a00
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.videoRecordingService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _expoCamera = require("expo-camera");
var MediaLibrary = _interopRequireWildcard(require("expo-media-library"));
var FileSystem = _interopRequireWildcard(require("expo-file-system"));
var _performance = require("../../../utils/performance");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var VideoRecordingService = function () {
  function VideoRecordingService() {
    (0, _classCallCheck2.default)(this, VideoRecordingService);
    this.cameraRef = null;
    this.isRecording = false;
    this.isPaused = false;
    this.recordingStartTime = 0;
    this.pausedDuration = 0;
    this.currentRecordingUri = null;
    this.progressCallback = null;
    this.progressInterval = null;
  }
  return (0, _createClass2.default)(VideoRecordingService, [{
    key: "initialize",
    value: (function () {
      var _initialize = (0, _asyncToGenerator2.default)(function* () {
        try {
          var permissions = yield this.requestPermissions();
          if (!permissions.camera || !permissions.microphone) {
            throw new Error('Camera and microphone permissions are required for video recording');
          }
          _performance.performanceMonitor.start('video_service_init');
          _performance.performanceMonitor.end('video_service_init');
        } catch (error) {
          console.error('Failed to initialize video recording service:', error);
          throw error;
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "requestPermissions",
    value: (function () {
      var _requestPermissions = (0, _asyncToGenerator2.default)(function* () {
        try {
          var cameraPermission = yield _expoCamera.Camera.requestCameraPermissionsAsync();
          var microphonePermission = yield _expoCamera.Camera.requestMicrophonePermissionsAsync();
          var mediaLibraryPermission = yield MediaLibrary.requestPermissionsAsync();
          return {
            camera: cameraPermission.status === 'granted',
            microphone: microphonePermission.status === 'granted',
            mediaLibrary: mediaLibraryPermission.status === 'granted'
          };
        } catch (error) {
          console.error('Failed to request permissions:', error);
          return {
            camera: false,
            microphone: false,
            mediaLibrary: false
          };
        }
      });
      function requestPermissions() {
        return _requestPermissions.apply(this, arguments);
      }
      return requestPermissions;
    }())
  }, {
    key: "setCameraRef",
    value: function setCameraRef(ref) {
      this.cameraRef = ref;
    }
  }, {
    key: "startRecording",
    value: (function () {
      var _startRecording = (0, _asyncToGenerator2.default)(function* (config) {
        if (!this.cameraRef) {
          throw new Error('Camera reference not set');
        }
        if (this.isRecording) {
          throw new Error('Recording already in progress');
        }
        try {
          _performance.performanceMonitor.start('video_recording_start');
          var recordingOptions = this.getRecordingOptions(config);
          this.isRecording = true;
          this.isPaused = false;
          this.recordingStartTime = Date.now();
          this.pausedDuration = 0;
          var recordingPromise = this.cameraRef.recordAsync(recordingOptions);
          this.startProgressMonitoring();
          var result = yield recordingPromise;
          this.currentRecordingUri = result.uri;
          _performance.performanceMonitor.end('video_recording_start');
        } catch (error) {
          this.isRecording = false;
          console.error('Failed to start recording:', error);
          throw error;
        }
      });
      function startRecording(_x) {
        return _startRecording.apply(this, arguments);
      }
      return startRecording;
    }())
  }, {
    key: "stopRecording",
    value: (function () {
      var _stopRecording = (0, _asyncToGenerator2.default)(function* () {
        if (!this.cameraRef || !this.isRecording) {
          throw new Error('No active recording to stop');
        }
        try {
          _performance.performanceMonitor.start('video_recording_stop');
          this.cameraRef.stopRecording();
          this.isRecording = false;
          this.isPaused = false;
          this.stopProgressMonitoring();
          if (!this.currentRecordingUri) {
            throw new Error('Recording URI not available');
          }
          var fileInfo = yield FileSystem.getInfoAsync(this.currentRecordingUri);
          if (!fileInfo.exists) {
            throw new Error('Recording file not found');
          }
          var duration = (Date.now() - this.recordingStartTime - this.pausedDuration) / 1000;
          var thumbnail = yield this.generateThumbnail(this.currentRecordingUri);
          var result = {
            uri: this.currentRecordingUri,
            duration: duration,
            fileSize: fileInfo.size || 0,
            width: 1920,
            height: 1080,
            thumbnail: thumbnail
          };
          _performance.performanceMonitor.end('video_recording_stop');
          return result;
        } catch (error) {
          console.error('Failed to stop recording:', error);
          throw error;
        }
      });
      function stopRecording() {
        return _stopRecording.apply(this, arguments);
      }
      return stopRecording;
    }())
  }, {
    key: "pauseRecording",
    value: (function () {
      var _pauseRecording = (0, _asyncToGenerator2.default)(function* () {
        if (!this.isRecording || this.isPaused) {
          return;
        }
        try {
          this.isPaused = true;
          console.log('Recording paused (placeholder implementation)');
        } catch (error) {
          console.error('Failed to pause recording:', error);
          throw error;
        }
      });
      function pauseRecording() {
        return _pauseRecording.apply(this, arguments);
      }
      return pauseRecording;
    }())
  }, {
    key: "resumeRecording",
    value: (function () {
      var _resumeRecording = (0, _asyncToGenerator2.default)(function* () {
        if (!this.isRecording || !this.isPaused) {
          return;
        }
        try {
          this.isPaused = false;
          console.log('Recording resumed (placeholder implementation)');
        } catch (error) {
          console.error('Failed to resume recording:', error);
          throw error;
        }
      });
      function resumeRecording() {
        return _resumeRecording.apply(this, arguments);
      }
      return resumeRecording;
    }())
  }, {
    key: "getRecordingStatus",
    value: function getRecordingStatus() {
      var currentTime = Date.now();
      var duration = this.isRecording ? (currentTime - this.recordingStartTime - this.pausedDuration) / 1000 : 0;
      return {
        duration: duration,
        fileSize: 0,
        isRecording: this.isRecording,
        isPaused: this.isPaused
      };
    }
  }, {
    key: "setProgressCallback",
    value: function setProgressCallback(callback) {
      this.progressCallback = callback;
    }
  }, {
    key: "saveToGallery",
    value: (function () {
      var _saveToGallery = (0, _asyncToGenerator2.default)(function* (uri) {
        try {
          var permissions = yield MediaLibrary.requestPermissionsAsync();
          if (!permissions.granted) {
            throw new Error('Media library permission required to save video');
          }
          var asset = yield MediaLibrary.createAssetAsync(uri);
          return asset.uri;
        } catch (error) {
          console.error('Failed to save video to gallery:', error);
          throw error;
        }
      });
      function saveToGallery(_x2) {
        return _saveToGallery.apply(this, arguments);
      }
      return saveToGallery;
    }())
  }, {
    key: "compressVideo",
    value: (function () {
      var _compressVideo = (0, _asyncToGenerator2.default)(function* (uri) {
        var quality = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'medium';
        try {
          _performance.performanceMonitor.start('video_compression');
          var fileInfo = yield FileSystem.getInfoAsync(uri);
          if (!fileInfo.exists) {
            throw new Error('Video file not found for compression');
          }
          var originalSize = fileInfo.size || 0;
          console.log(`Compressing video: ${originalSize} bytes, quality: ${quality}`);
          var compressedUri = `${FileSystem.cacheDirectory}compressed_${Date.now()}.mp4`;
          var compressionSettings = {
            low: {
              bitrate: 500000,
              resolution: 480
            },
            medium: {
              bitrate: 1000000,
              resolution: 720
            },
            high: {
              bitrate: 2000000,
              resolution: 1080
            }
          };
          var settings = compressionSettings[quality];
          yield FileSystem.copyAsync({
            from: uri,
            to: compressedUri
          });
          var compressedInfo = yield FileSystem.getInfoAsync(compressedUri);
          var compressedSize = compressedInfo.size || 0;
          console.log(`Video compressed: ${originalSize} -> ${compressedSize} bytes`);
          _performance.performanceMonitor.end('video_compression');
          return compressedUri;
        } catch (error) {
          console.error('Failed to compress video:', error);
          throw error;
        }
      });
      function compressVideo(_x3) {
        return _compressVideo.apply(this, arguments);
      }
      return compressVideo;
    }())
  }, {
    key: "generateThumbnail",
    value: (function () {
      var _generateThumbnail = (0, _asyncToGenerator2.default)(function* (uri) {
        try {
          var thumbnailUri = `${FileSystem.cacheDirectory}thumbnail_${Date.now()}.jpg`;
          console.log('Generating thumbnail for video:', uri);
          return '';
        } catch (error) {
          console.error('Failed to generate thumbnail:', error);
          return '';
        }
      });
      function generateThumbnail(_x4) {
        return _generateThumbnail.apply(this, arguments);
      }
      return generateThumbnail;
    }())
  }, {
    key: "getRecordingOptions",
    value: function getRecordingOptions(config) {
      var qualityMap = {
        low: _expoCamera.VideoQuality['480p'],
        medium: _expoCamera.VideoQuality['720p'],
        high: _expoCamera.VideoQuality['1080p'],
        ultra: _expoCamera.VideoQuality['2160p']
      };
      return {
        quality: qualityMap[config.quality] || _expoCamera.VideoQuality['720p'],
        maxDuration: config.maxDurationMinutes * 60,
        mute: !config.enableAudio
      };
    }
  }, {
    key: "startProgressMonitoring",
    value: function startProgressMonitoring() {
      var _this = this;
      if (this.progressInterval) {
        clearInterval(this.progressInterval);
      }
      this.progressInterval = setInterval(function () {
        if (_this.progressCallback) {
          var progress = _this.getRecordingStatus();
          _this.progressCallback(progress);
        }
      }, 1000);
    }
  }, {
    key: "stopProgressMonitoring",
    value: function stopProgressMonitoring() {
      if (this.progressInterval) {
        clearInterval(this.progressInterval);
        this.progressInterval = null;
      }
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      this.stopProgressMonitoring();
      this.isRecording = false;
      this.isPaused = false;
      this.currentRecordingUri = null;
      this.progressCallback = null;
    }
  }]);
}();
var videoRecordingService = exports.videoRecordingService = new VideoRecordingService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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