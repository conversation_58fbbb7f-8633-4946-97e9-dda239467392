7385f86ca94f3a5c0273d03a6764c297
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_21z2r3zj0g() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\subscription.tsx";
  var hash = "623f79faab3ab4c3edd716d00e4aec7eac0fc146";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\subscription.tsx",
    statementMap: {
      "0": {
        start: {
          line: 28,
          column: 39
        },
        end: {
          line: 28,
          column: 48
        }
      },
      "1": {
        start: {
          line: 29,
          column: 42
        },
        end: {
          line: 29,
          column: 77
        }
      },
      "2": {
        start: {
          line: 30,
          column: 34
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "3": {
        start: {
          line: 31,
          column: 32
        },
        end: {
          line: 31,
          column: 46
        }
      },
      "4": {
        start: {
          line: 32,
          column: 38
        },
        end: {
          line: 32,
          column: 53
        }
      },
      "5": {
        start: {
          line: 33,
          column: 50
        },
        end: {
          line: 33,
          column: 65
        }
      },
      "6": {
        start: {
          line: 35,
          column: 22
        },
        end: {
          line: 35,
          column: 53
        }
      },
      "7": {
        start: {
          line: 36,
          column: 32
        },
        end: {
          line: 36,
          column: 70
        }
      },
      "8": {
        start: {
          line: 38,
          column: 2
        },
        end: {
          line: 44,
          column: 9
        }
      },
      "9": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 43,
          column: 5
        }
      },
      "10": {
        start: {
          line: 40,
          column: 6
        },
        end: {
          line: 40,
          column: 29
        }
      },
      "11": {
        start: {
          line: 42,
          column: 6
        },
        end: {
          line: 42,
          column: 24
        }
      },
      "12": {
        start: {
          line: 46,
          column: 31
        },
        end: {
          line: 65,
          column: 3
        }
      },
      "13": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 64,
          column: 5
        }
      },
      "14": {
        start: {
          line: 48,
          column: 42
        },
        end: {
          line: 51,
          column: 8
        }
      },
      "15": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 55,
          column: 7
        }
      },
      "16": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 54,
          column: 48
        }
      },
      "17": {
        start: {
          line: 57,
          column: 6
        },
        end: {
          line: 59,
          column: 7
        }
      },
      "18": {
        start: {
          line: 58,
          column: 8
        },
        end: {
          line: 58,
          column: 45
        }
      },
      "19": {
        start: {
          line: 61,
          column: 6
        },
        end: {
          line: 61,
          column: 63
        }
      },
      "20": {
        start: {
          line: 63,
          column: 6
        },
        end: {
          line: 63,
          column: 24
        }
      },
      "21": {
        start: {
          line: 67,
          column: 24
        },
        end: {
          line: 71,
          column: 3
        }
      },
      "22": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 68,
          column: 24
        }
      },
      "23": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 33
        }
      },
      "24": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 70,
          column: 25
        }
      },
      "25": {
        start: {
          line: 73,
          column: 35
        },
        end: {
          line: 100,
          column: 3
        }
      },
      "26": {
        start: {
          line: 74,
          column: 4
        },
        end: {
          line: 74,
          column: 30
        }
      },
      "27": {
        start: {
          line: 74,
          column: 23
        },
        end: {
          line: 74,
          column: 30
        }
      },
      "28": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 99,
          column: 6
        }
      },
      "29": {
        start: {
          line: 85,
          column: 12
        },
        end: {
          line: 95,
          column: 13
        }
      },
      "30": {
        start: {
          line: 86,
          column: 41
        },
        end: {
          line: 86,
          column: 97
        }
      },
      "31": {
        start: {
          line: 87,
          column: 14
        },
        end: {
          line: 92,
          column: 15
        }
      },
      "32": {
        start: {
          line: 88,
          column: 16
        },
        end: {
          line: 88,
          column: 93
        }
      },
      "33": {
        start: {
          line: 89,
          column: 16
        },
        end: {
          line: 89,
          column: 39
        }
      },
      "34": {
        start: {
          line: 91,
          column: 16
        },
        end: {
          line: 91,
          column: 79
        }
      },
      "35": {
        start: {
          line: 94,
          column: 14
        },
        end: {
          line: 94,
          column: 68
        }
      },
      "36": {
        start: {
          line: 102,
          column: 35
        },
        end: {
          line: 116,
          column: 3
        }
      },
      "37": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 103,
          column: 30
        }
      },
      "38": {
        start: {
          line: 103,
          column: 23
        },
        end: {
          line: 103,
          column: 30
        }
      },
      "39": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 115,
          column: 5
        }
      },
      "40": {
        start: {
          line: 106,
          column: 33
        },
        end: {
          line: 106,
          column: 89
        }
      },
      "41": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 112,
          column: 7
        }
      },
      "42": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 83
        }
      },
      "43": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 109,
          column: 31
        }
      },
      "44": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 111,
          column: 71
        }
      },
      "45": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 60
        }
      },
      "46": {
        start: {
          line: 118,
          column: 32
        },
        end: {
          line: 130,
          column: 3
        }
      },
      "47": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "48": {
        start: {
          line: 120,
          column: 29
        },
        end: {
          line: 120,
          column: 76
        }
      },
      "49": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 126,
          column: 7
        }
      },
      "50": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 69
        }
      },
      "51": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 68
        }
      },
      "52": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 57
        }
      },
      "53": {
        start: {
          line: 132,
          column: 21
        },
        end: {
          line: 138,
          column: 3
        }
      },
      "54": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 137,
          column: 7
        }
      },
      "55": {
        start: {
          line: 140,
          column: 25
        },
        end: {
          line: 153,
          column: 3
        }
      },
      "56": {
        start: {
          line: 141,
          column: 4
        },
        end: {
          line: 152,
          column: 5
        }
      },
      "57": {
        start: {
          line: 144,
          column: 8
        },
        end: {
          line: 144,
          column: 25
        }
      },
      "58": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 146,
          column: 25
        }
      },
      "59": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 149,
          column: 25
        }
      },
      "60": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 151,
          column: 25
        }
      },
      "61": {
        start: {
          line: 155,
          column: 24
        },
        end: {
          line: 170,
          column: 3
        }
      },
      "62": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 169,
          column: 5
        }
      },
      "63": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 158,
          column: 24
        }
      },
      "64": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 160,
          column: 28
        }
      },
      "65": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 162,
          column: 26
        }
      },
      "66": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 164,
          column: 26
        }
      },
      "67": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 166,
          column: 24
        }
      },
      "68": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 22
        }
      },
      "69": {
        start: {
          line: 172,
          column: 2
        },
        end: {
          line: 190,
          column: 3
        }
      },
      "70": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 189,
          column: 6
        }
      },
      "71": {
        start: {
          line: 183,
          column: 27
        },
        end: {
          line: 183,
          column: 53
        }
      },
      "72": {
        start: {
          line: 192,
          column: 2
        },
        end: {
          line: 199,
          column: 3
        }
      },
      "73": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 198,
          column: 6
        }
      },
      "74": {
        start: {
          line: 201,
          column: 2
        },
        end: {
          line: 252,
          column: 3
        }
      },
      "75": {
        start: {
          line: 202,
          column: 4
        },
        end: {
          line: 251,
          column: 6
        }
      },
      "76": {
        start: {
          line: 231,
          column: 29
        },
        end: {
          line: 231,
          column: 54
        }
      },
      "77": {
        start: {
          line: 240,
          column: 16
        },
        end: {
          line: 245,
          column: 23
        }
      },
      "78": {
        start: {
          line: 243,
          column: 72
        },
        end: {
          line: 243,
          column: 87
        }
      },
      "79": {
        start: {
          line: 254,
          column: 2
        },
        end: {
          line: 269,
          column: 3
        }
      },
      "80": {
        start: {
          line: 255,
          column: 4
        },
        end: {
          line: 268,
          column: 6
        }
      },
      "81": {
        start: {
          line: 260,
          column: 27
        },
        end: {
          line: 260,
          column: 53
        }
      },
      "82": {
        start: {
          line: 271,
          column: 2
        },
        end: {
          line: 403,
          column: 4
        }
      },
      "83": {
        start: {
          line: 315,
          column: 27
        },
        end: {
          line: 315,
          column: 52
        }
      },
      "84": {
        start: {
          line: 353,
          column: 14
        },
        end: {
          line: 384,
          column: 21
        }
      },
      "85": {
        start: {
          line: 378,
          column: 37
        },
        end: {
          line: 378,
          column: 70
        }
      },
      "86": {
        start: {
          line: 406,
          column: 15
        },
        end: {
          line: 730,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "SubscriptionScreen",
        decl: {
          start: {
            line: 27,
            column: 24
          },
          end: {
            line: 27,
            column: 42
          }
        },
        loc: {
          start: {
            line: 27,
            column: 45
          },
          end: {
            line: 404,
            column: 1
          }
        },
        line: 27
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 38,
            column: 13
          }
        },
        loc: {
          start: {
            line: 38,
            column: 18
          },
          end: {
            line: 44,
            column: 3
          }
        },
        line: 38
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 46,
            column: 31
          },
          end: {
            line: 46,
            column: 32
          }
        },
        loc: {
          start: {
            line: 46,
            column: 43
          },
          end: {
            line: 65,
            column: 3
          }
        },
        line: 46
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 67,
            column: 24
          },
          end: {
            line: 67,
            column: 25
          }
        },
        loc: {
          start: {
            line: 67,
            column: 36
          },
          end: {
            line: 71,
            column: 3
          }
        },
        line: 67
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 73,
            column: 35
          },
          end: {
            line: 73,
            column: 36
          }
        },
        loc: {
          start: {
            line: 73,
            column: 41
          },
          end: {
            line: 100,
            column: 3
          }
        },
        line: 73
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 84,
            column: 19
          },
          end: {
            line: 84,
            column: 20
          }
        },
        loc: {
          start: {
            line: 84,
            column: 31
          },
          end: {
            line: 96,
            column: 11
          }
        },
        line: 84
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 102,
            column: 35
          },
          end: {
            line: 102,
            column: 36
          }
        },
        loc: {
          start: {
            line: 102,
            column: 47
          },
          end: {
            line: 116,
            column: 3
          }
        },
        line: 102
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 118,
            column: 32
          },
          end: {
            line: 118,
            column: 33
          }
        },
        loc: {
          start: {
            line: 118,
            column: 61
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 118
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 132,
            column: 21
          },
          end: {
            line: 132,
            column: 22
          }
        },
        loc: {
          start: {
            line: 132,
            column: 45
          },
          end: {
            line: 138,
            column: 3
          }
        },
        line: 132
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 140,
            column: 25
          },
          end: {
            line: 140,
            column: 26
          }
        },
        loc: {
          start: {
            line: 140,
            column: 45
          },
          end: {
            line: 153,
            column: 3
          }
        },
        line: 140
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 155,
            column: 24
          },
          end: {
            line: 155,
            column: 25
          }
        },
        loc: {
          start: {
            line: 155,
            column: 44
          },
          end: {
            line: 170,
            column: 3
          }
        },
        line: 155
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 183,
            column: 21
          },
          end: {
            line: 183,
            column: 22
          }
        },
        loc: {
          start: {
            line: 183,
            column: 27
          },
          end: {
            line: 183,
            column: 53
          }
        },
        line: 183
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 231,
            column: 23
          },
          end: {
            line: 231,
            column: 24
          }
        },
        loc: {
          start: {
            line: 231,
            column: 29
          },
          end: {
            line: 231,
            column: 54
          }
        },
        line: 231
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 239,
            column: 40
          },
          end: {
            line: 239,
            column: 41
          }
        },
        loc: {
          start: {
            line: 240,
            column: 16
          },
          end: {
            line: 245,
            column: 23
          }
        },
        line: 240
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 243,
            column: 67
          },
          end: {
            line: 243,
            column: 68
          }
        },
        loc: {
          start: {
            line: 243,
            column: 72
          },
          end: {
            line: 243,
            column: 87
          }
        },
        line: 243
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 260,
            column: 21
          },
          end: {
            line: 260,
            column: 22
          }
        },
        loc: {
          start: {
            line: 260,
            column: 27
          },
          end: {
            line: 260,
            column: 53
          }
        },
        line: 260
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 315,
            column: 21
          },
          end: {
            line: 315,
            column: 22
          }
        },
        loc: {
          start: {
            line: 315,
            column: 27
          },
          end: {
            line: 315,
            column: 52
          }
        },
        line: 315
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 352,
            column: 26
          },
          end: {
            line: 352,
            column: 27
          }
        },
        loc: {
          start: {
            line: 353,
            column: 14
          },
          end: {
            line: 384,
            column: 21
          }
        },
        line: 353
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 378,
            column: 31
          },
          end: {
            line: 378,
            column: 32
          }
        },
        loc: {
          start: {
            line: 378,
            column: 37
          },
          end: {
            line: 378,
            column: 70
          }
        },
        line: 378
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 43,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 39,
            column: 4
          },
          end: {
            line: 43,
            column: 5
          }
        }, {
          start: {
            line: 41,
            column: 11
          },
          end: {
            line: 43,
            column: 5
          }
        }],
        line: 39
      },
      "1": {
        loc: {
          start: {
            line: 53,
            column: 6
          },
          end: {
            line: 55,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 6
          },
          end: {
            line: 55,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "2": {
        loc: {
          start: {
            line: 57,
            column: 6
          },
          end: {
            line: 59,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 57,
            column: 6
          },
          end: {
            line: 59,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 57
      },
      "3": {
        loc: {
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 74,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 4
          },
          end: {
            line: 74,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "4": {
        loc: {
          start: {
            line: 87,
            column: 14
          },
          end: {
            line: 92,
            column: 15
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 14
          },
          end: {
            line: 92,
            column: 15
          }
        }, {
          start: {
            line: 90,
            column: 21
          },
          end: {
            line: 92,
            column: 15
          }
        }],
        line: 87
      },
      "5": {
        loc: {
          start: {
            line: 91,
            column: 37
          },
          end: {
            line: 91,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 91,
            column: 37
          },
          end: {
            line: 91,
            column: 42
          }
        }, {
          start: {
            line: 91,
            column: 46
          },
          end: {
            line: 91,
            column: 77
          }
        }],
        line: 91
      },
      "6": {
        loc: {
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 103,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 103,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 103
      },
      "7": {
        loc: {
          start: {
            line: 107,
            column: 6
          },
          end: {
            line: 112,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 6
          },
          end: {
            line: 112,
            column: 7
          }
        }, {
          start: {
            line: 110,
            column: 13
          },
          end: {
            line: 112,
            column: 7
          }
        }],
        line: 107
      },
      "8": {
        loc: {
          start: {
            line: 111,
            column: 29
          },
          end: {
            line: 111,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 111,
            column: 29
          },
          end: {
            line: 111,
            column: 34
          }
        }, {
          start: {
            line: 111,
            column: 38
          },
          end: {
            line: 111,
            column: 69
          }
        }],
        line: 111
      },
      "9": {
        loc: {
          start: {
            line: 121,
            column: 6
          },
          end: {
            line: 126,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 6
          },
          end: {
            line: 126,
            column: 7
          }
        }, {
          start: {
            line: 124,
            column: 13
          },
          end: {
            line: 126,
            column: 7
          }
        }],
        line: 121
      },
      "10": {
        loc: {
          start: {
            line: 125,
            column: 29
          },
          end: {
            line: 125,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 125,
            column: 29
          },
          end: {
            line: 125,
            column: 34
          }
        }, {
          start: {
            line: 125,
            column: 38
          },
          end: {
            line: 125,
            column: 66
          }
        }],
        line: 125
      },
      "11": {
        loc: {
          start: {
            line: 141,
            column: 4
          },
          end: {
            line: 152,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 142,
            column: 6
          },
          end: {
            line: 142,
            column: 20
          }
        }, {
          start: {
            line: 143,
            column: 6
          },
          end: {
            line: 144,
            column: 25
          }
        }, {
          start: {
            line: 145,
            column: 6
          },
          end: {
            line: 146,
            column: 25
          }
        }, {
          start: {
            line: 147,
            column: 6
          },
          end: {
            line: 147,
            column: 22
          }
        }, {
          start: {
            line: 148,
            column: 6
          },
          end: {
            line: 149,
            column: 25
          }
        }, {
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 151,
            column: 25
          }
        }],
        line: 141
      },
      "12": {
        loc: {
          start: {
            line: 156,
            column: 4
          },
          end: {
            line: 169,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 157,
            column: 6
          },
          end: {
            line: 158,
            column: 24
          }
        }, {
          start: {
            line: 159,
            column: 6
          },
          end: {
            line: 160,
            column: 28
          }
        }, {
          start: {
            line: 161,
            column: 6
          },
          end: {
            line: 162,
            column: 26
          }
        }, {
          start: {
            line: 163,
            column: 6
          },
          end: {
            line: 164,
            column: 26
          }
        }, {
          start: {
            line: 165,
            column: 6
          },
          end: {
            line: 166,
            column: 24
          }
        }, {
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 168,
            column: 22
          }
        }],
        line: 156
      },
      "13": {
        loc: {
          start: {
            line: 172,
            column: 2
          },
          end: {
            line: 190,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 2
          },
          end: {
            line: 190,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "14": {
        loc: {
          start: {
            line: 192,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 192,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 192
      },
      "15": {
        loc: {
          start: {
            line: 201,
            column: 2
          },
          end: {
            line: 252,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 2
          },
          end: {
            line: 252,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "16": {
        loc: {
          start: {
            line: 201,
            column: 6
          },
          end: {
            line: 201,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 201,
            column: 6
          },
          end: {
            line: 201,
            column: 28
          }
        }, {
          start: {
            line: 201,
            column: 32
          },
          end: {
            line: 201,
            column: 49
          }
        }],
        line: 201
      },
      "17": {
        loc: {
          start: {
            line: 254,
            column: 2
          },
          end: {
            line: 269,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 2
          },
          end: {
            line: 269,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "18": {
        loc: {
          start: {
            line: 290,
            column: 52
          },
          end: {
            line: 290,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 52
          },
          end: {
            line: 290,
            column: 72
          }
        }, {
          start: {
            line: 290,
            column: 76
          },
          end: {
            line: 290,
            column: 84
          }
        }],
        line: 290
      },
      "19": {
        loc: {
          start: {
            line: 294,
            column: 33
          },
          end: {
            line: 294,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 294,
            column: 33
          },
          end: {
            line: 294,
            column: 53
          }
        }, {
          start: {
            line: 294,
            column: 57
          },
          end: {
            line: 294,
            column: 65
          }
        }],
        line: 294
      },
      "20": {
        loc: {
          start: {
            line: 300,
            column: 11
          },
          end: {
            line: 309,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 300,
            column: 11
          },
          end: {
            line: 300,
            column: 23
          }
        }, {
          start: {
            line: 301,
            column: 12
          },
          end: {
            line: 308,
            column: 19
          }
        }],
        line: 300
      },
      "21": {
        loc: {
          start: {
            line: 321,
            column: 11
          },
          end: {
            line: 337,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 322,
            column: 12
          },
          end: {
            line: 328,
            column: 31
          }
        }, {
          start: {
            line: 330,
            column: 12
          },
          end: {
            line: 336,
            column: 31
          }
        }],
        line: 321
      },
      "22": {
        loc: {
          start: {
            line: 345,
            column: 9
          },
          end: {
            line: 387,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 346,
            column: 10
          },
          end: {
            line: 349,
            column: 17
          }
        }, {
          start: {
            line: 351,
            column: 10
          },
          end: {
            line: 386,
            column: 17
          }
        }],
        line: 345
      },
      "23": {
        loc: {
          start: {
            line: 367,
            column: 41
          },
          end: {
            line: 367,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 367,
            column: 69
          },
          end: {
            line: 367,
            column: 78
          }
        }, {
          start: {
            line: 367,
            column: 81
          },
          end: {
            line: 367,
            column: 90
          }
        }],
        line: 367
      },
      "24": {
        loc: {
          start: {
            line: 375,
            column: 19
          },
          end: {
            line: 382,
            column: 19
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 375,
            column: 19
          },
          end: {
            line: 375,
            column: 44
          }
        }, {
          start: {
            line: 376,
            column: 20
          },
          end: {
            line: 381,
            column: 39
          }
        }],
        line: 375
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0, 0, 0, 0, 0],
      "12": [0, 0, 0, 0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "623f79faab3ab4c3edd716d00e4aec7eac0fc146"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_21z2r3zj0g = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_21z2r3zj0g();
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Alert, RefreshControl, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { useAuth } from "../../contexts/AuthContext";
import { paymentService } from "../../services/payment/PaymentService";
import { formatPrice } from "../../config/subscription.config";
import PricingPlans from "../../components/subscription/PricingPlans";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export default function SubscriptionScreen() {
  cov_21z2r3zj0g().f[0]++;
  var _ref = (cov_21z2r3zj0g().s[0]++, useAuth()),
    profile = _ref.profile,
    isAuthenticated = _ref.isAuthenticated;
  var _ref2 = (cov_21z2r3zj0g().s[1]++, useState(null)),
    _ref3 = _slicedToArray(_ref2, 2),
    subscription = _ref3[0],
    setSubscription = _ref3[1];
  var _ref4 = (cov_21z2r3zj0g().s[2]++, useState([])),
    _ref5 = _slicedToArray(_ref4, 2),
    invoices = _ref5[0],
    setInvoices = _ref5[1];
  var _ref6 = (cov_21z2r3zj0g().s[3]++, useState(true)),
    _ref7 = _slicedToArray(_ref6, 2),
    loading = _ref7[0],
    setLoading = _ref7[1];
  var _ref8 = (cov_21z2r3zj0g().s[4]++, useState(false)),
    _ref9 = _slicedToArray(_ref8, 2),
    refreshing = _ref9[0],
    setRefreshing = _ref9[1];
  var _ref0 = (cov_21z2r3zj0g().s[5]++, useState(false)),
    _ref1 = _slicedToArray(_ref0, 2),
    showPricingPlans = _ref1[0],
    setShowPricingPlans = _ref1[1];
  var currentTier = (cov_21z2r3zj0g().s[6]++, paymentService.getCurrentTier());
  var hasActiveSubscription = (cov_21z2r3zj0g().s[7]++, paymentService.hasActiveSubscription());
  cov_21z2r3zj0g().s[8]++;
  useEffect(function () {
    cov_21z2r3zj0g().f[1]++;
    cov_21z2r3zj0g().s[9]++;
    if (isAuthenticated()) {
      cov_21z2r3zj0g().b[0][0]++;
      cov_21z2r3zj0g().s[10]++;
      loadSubscriptionData();
    } else {
      cov_21z2r3zj0g().b[0][1]++;
      cov_21z2r3zj0g().s[11]++;
      setLoading(false);
    }
  }, []);
  cov_21z2r3zj0g().s[12]++;
  var loadSubscriptionData = function () {
    var _ref10 = _asyncToGenerator(function* () {
      cov_21z2r3zj0g().f[2]++;
      cov_21z2r3zj0g().s[13]++;
      try {
        var _ref11 = (cov_21z2r3zj0g().s[14]++, yield Promise.all([paymentService.getCurrentSubscription(), paymentService.getBillingHistory()])),
          _ref12 = _slicedToArray(_ref11, 2),
          subResult = _ref12[0],
          invoicesResult = _ref12[1];
        cov_21z2r3zj0g().s[15]++;
        if (subResult.subscription) {
          cov_21z2r3zj0g().b[1][0]++;
          cov_21z2r3zj0g().s[16]++;
          setSubscription(subResult.subscription);
        } else {
          cov_21z2r3zj0g().b[1][1]++;
        }
        cov_21z2r3zj0g().s[17]++;
        if (invoicesResult.invoices) {
          cov_21z2r3zj0g().b[2][0]++;
          cov_21z2r3zj0g().s[18]++;
          setInvoices(invoicesResult.invoices);
        } else {
          cov_21z2r3zj0g().b[2][1]++;
        }
      } catch (error) {
        cov_21z2r3zj0g().s[19]++;
        console.error('Error loading subscription data:', error);
      } finally {
        cov_21z2r3zj0g().s[20]++;
        setLoading(false);
      }
    });
    return function loadSubscriptionData() {
      return _ref10.apply(this, arguments);
    };
  }();
  cov_21z2r3zj0g().s[21]++;
  var handleRefresh = function () {
    var _ref13 = _asyncToGenerator(function* () {
      cov_21z2r3zj0g().f[3]++;
      cov_21z2r3zj0g().s[22]++;
      setRefreshing(true);
      cov_21z2r3zj0g().s[23]++;
      yield loadSubscriptionData();
      cov_21z2r3zj0g().s[24]++;
      setRefreshing(false);
    });
    return function handleRefresh() {
      return _ref13.apply(this, arguments);
    };
  }();
  cov_21z2r3zj0g().s[25]++;
  var handleCancelSubscription = function handleCancelSubscription() {
    cov_21z2r3zj0g().f[4]++;
    cov_21z2r3zj0g().s[26]++;
    if (!subscription) {
      cov_21z2r3zj0g().b[3][0]++;
      cov_21z2r3zj0g().s[27]++;
      return;
    } else {
      cov_21z2r3zj0g().b[3][1]++;
    }
    cov_21z2r3zj0g().s[28]++;
    Alert.alert('Cancel Subscription', 'Are you sure you want to cancel your subscription? You will lose access to premium features at the end of your billing period.', [{
      text: 'Keep Subscription',
      style: 'cancel'
    }, {
      text: 'Cancel',
      style: 'destructive',
      onPress: function () {
        var _onPress = _asyncToGenerator(function* () {
          cov_21z2r3zj0g().f[5]++;
          cov_21z2r3zj0g().s[29]++;
          try {
            var _ref14 = (cov_21z2r3zj0g().s[30]++, yield paymentService.cancelSubscription(subscription.id)),
              success = _ref14.success,
              error = _ref14.error;
            cov_21z2r3zj0g().s[31]++;
            if (success) {
              cov_21z2r3zj0g().b[4][0]++;
              cov_21z2r3zj0g().s[32]++;
              Alert.alert('Subscription Canceled', 'Your subscription has been canceled.');
              cov_21z2r3zj0g().s[33]++;
              loadSubscriptionData();
            } else {
              cov_21z2r3zj0g().b[4][1]++;
              cov_21z2r3zj0g().s[34]++;
              Alert.alert('Error', (cov_21z2r3zj0g().b[5][0]++, error) || (cov_21z2r3zj0g().b[5][1]++, 'Failed to cancel subscription'));
            }
          } catch (error) {
            cov_21z2r3zj0g().s[35]++;
            Alert.alert('Error', 'Failed to cancel subscription');
          }
        });
        function onPress() {
          return _onPress.apply(this, arguments);
        }
        return onPress;
      }()
    }]);
  };
  cov_21z2r3zj0g().s[36]++;
  var handleResumeSubscription = function () {
    var _ref15 = _asyncToGenerator(function* () {
      cov_21z2r3zj0g().f[6]++;
      cov_21z2r3zj0g().s[37]++;
      if (!subscription) {
        cov_21z2r3zj0g().b[6][0]++;
        cov_21z2r3zj0g().s[38]++;
        return;
      } else {
        cov_21z2r3zj0g().b[6][1]++;
      }
      cov_21z2r3zj0g().s[39]++;
      try {
        var _ref16 = (cov_21z2r3zj0g().s[40]++, yield paymentService.resumeSubscription(subscription.id)),
          success = _ref16.success,
          error = _ref16.error;
        cov_21z2r3zj0g().s[41]++;
        if (success) {
          cov_21z2r3zj0g().b[7][0]++;
          cov_21z2r3zj0g().s[42]++;
          Alert.alert('Subscription Resumed', 'Your subscription has been resumed.');
          cov_21z2r3zj0g().s[43]++;
          loadSubscriptionData();
        } else {
          cov_21z2r3zj0g().b[7][1]++;
          cov_21z2r3zj0g().s[44]++;
          Alert.alert('Error', (cov_21z2r3zj0g().b[8][0]++, error) || (cov_21z2r3zj0g().b[8][1]++, 'Failed to resume subscription'));
        }
      } catch (error) {
        cov_21z2r3zj0g().s[45]++;
        Alert.alert('Error', 'Failed to resume subscription');
      }
    });
    return function handleResumeSubscription() {
      return _ref15.apply(this, arguments);
    };
  }();
  cov_21z2r3zj0g().s[46]++;
  var handleDownloadInvoice = function () {
    var _ref17 = _asyncToGenerator(function* (invoiceId) {
      cov_21z2r3zj0g().f[7]++;
      cov_21z2r3zj0g().s[47]++;
      try {
        var _ref18 = (cov_21z2r3zj0g().s[48]++, yield paymentService.downloadInvoice(invoiceId)),
          url = _ref18.url,
          error = _ref18.error;
        cov_21z2r3zj0g().s[49]++;
        if (url) {
          cov_21z2r3zj0g().b[9][0]++;
          cov_21z2r3zj0g().s[50]++;
          Alert.alert('Download', 'Invoice download would start here');
        } else {
          cov_21z2r3zj0g().b[9][1]++;
          cov_21z2r3zj0g().s[51]++;
          Alert.alert('Error', (cov_21z2r3zj0g().b[10][0]++, error) || (cov_21z2r3zj0g().b[10][1]++, 'Failed to download invoice'));
        }
      } catch (error) {
        cov_21z2r3zj0g().s[52]++;
        Alert.alert('Error', 'Failed to download invoice');
      }
    });
    return function handleDownloadInvoice(_x) {
      return _ref17.apply(this, arguments);
    };
  }();
  cov_21z2r3zj0g().s[53]++;
  var formatDate = function formatDate(dateString) {
    cov_21z2r3zj0g().f[8]++;
    cov_21z2r3zj0g().s[54]++;
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  cov_21z2r3zj0g().s[55]++;
  var getStatusColor = function getStatusColor(status) {
    cov_21z2r3zj0g().f[9]++;
    cov_21z2r3zj0g().s[56]++;
    switch (status) {
      case 'active':
        cov_21z2r3zj0g().b[11][0]++;
      case 'trialing':
        cov_21z2r3zj0g().b[11][1]++;
        cov_21z2r3zj0g().s[57]++;
        return '#10B981';
      case 'canceled':
        cov_21z2r3zj0g().b[11][2]++;
        cov_21z2r3zj0g().s[58]++;
        return '#F59E0B';
      case 'past_due':
        cov_21z2r3zj0g().b[11][3]++;
      case 'unpaid':
        cov_21z2r3zj0g().b[11][4]++;
        cov_21z2r3zj0g().s[59]++;
        return '#EF4444';
      default:
        cov_21z2r3zj0g().b[11][5]++;
        cov_21z2r3zj0g().s[60]++;
        return '#6B7280';
    }
  };
  cov_21z2r3zj0g().s[61]++;
  var getStatusText = function getStatusText(status) {
    cov_21z2r3zj0g().f[10]++;
    cov_21z2r3zj0g().s[62]++;
    switch (status) {
      case 'active':
        cov_21z2r3zj0g().b[12][0]++;
        cov_21z2r3zj0g().s[63]++;
        return 'Active';
      case 'trialing':
        cov_21z2r3zj0g().b[12][1]++;
        cov_21z2r3zj0g().s[64]++;
        return 'Free Trial';
      case 'canceled':
        cov_21z2r3zj0g().b[12][2]++;
        cov_21z2r3zj0g().s[65]++;
        return 'Canceled';
      case 'past_due':
        cov_21z2r3zj0g().b[12][3]++;
        cov_21z2r3zj0g().s[66]++;
        return 'Past Due';
      case 'unpaid':
        cov_21z2r3zj0g().b[12][4]++;
        cov_21z2r3zj0g().s[67]++;
        return 'Unpaid';
      default:
        cov_21z2r3zj0g().b[12][5]++;
        cov_21z2r3zj0g().s[68]++;
        return status;
    }
  };
  cov_21z2r3zj0g().s[69]++;
  if (!isAuthenticated()) {
    cov_21z2r3zj0g().b[13][0]++;
    cov_21z2r3zj0g().s[70]++;
    return _jsx(View, {
      style: styles.container,
      children: _jsxs(View, {
        style: styles.notAuthenticatedContainer,
        children: [_jsx(Ionicons, {
          name: "person-circle-outline",
          size: 80,
          color: "#6B7280"
        }), _jsx(Text, {
          style: styles.notAuthenticatedTitle,
          children: "Sign In Required"
        }), _jsx(Text, {
          style: styles.notAuthenticatedText,
          children: "Please sign in to view your subscription details"
        }), _jsx(TouchableOpacity, {
          style: styles.signInButton,
          onPress: function onPress() {
            cov_21z2r3zj0g().f[11]++;
            cov_21z2r3zj0g().s[71]++;
            return router.push('/auth/login');
          },
          children: _jsx(Text, {
            style: styles.signInButtonText,
            children: "Sign In"
          })
        })]
      })
    });
  } else {
    cov_21z2r3zj0g().b[13][1]++;
  }
  cov_21z2r3zj0g().s[72]++;
  if (loading) {
    cov_21z2r3zj0g().b[14][0]++;
    cov_21z2r3zj0g().s[73]++;
    return _jsxs(View, {
      style: styles.loadingContainer,
      children: [_jsx(ActivityIndicator, {
        size: "large",
        color: "#3B82F6"
      }), _jsx(Text, {
        style: styles.loadingText,
        children: "Loading subscription details..."
      })]
    });
  } else {
    cov_21z2r3zj0g().b[14][1]++;
  }
  cov_21z2r3zj0g().s[74]++;
  if ((cov_21z2r3zj0g().b[16][0]++, !hasActiveSubscription) && (cov_21z2r3zj0g().b[16][1]++, !showPricingPlans)) {
    cov_21z2r3zj0g().b[15][0]++;
    cov_21z2r3zj0g().s[75]++;
    return _jsx(ScrollView, {
      style: styles.container,
      refreshControl: _jsx(RefreshControl, {
        refreshing: refreshing,
        onRefresh: handleRefresh
      }),
      children: _jsxs(View, {
        style: styles.freeUserContainer,
        children: [_jsxs(LinearGradient, {
          colors: ['#3B82F6', '#8B5CF6'],
          style: styles.upgradeHeader,
          start: {
            x: 0,
            y: 0
          },
          end: {
            x: 1,
            y: 1
          },
          children: [_jsx(Ionicons, {
            name: "star",
            size: 60,
            color: "#FFFFFF"
          }), _jsx(Text, {
            style: styles.upgradeTitle,
            children: "Unlock Premium Features"
          }), _jsx(Text, {
            style: styles.upgradeSubtitle,
            children: "Take your tennis training to the next level"
          })]
        }), _jsxs(View, {
          style: styles.freeUserContent,
          children: [_jsxs(View, {
            style: styles.currentPlanCard,
            children: [_jsx(Text, {
              style: styles.currentPlanTitle,
              children: "Current Plan: Free"
            }), _jsx(Text, {
              style: styles.currentPlanDescription,
              children: "You're currently on the free plan with limited features"
            })]
          }), _jsxs(TouchableOpacity, {
            style: styles.viewPlansButton,
            onPress: function onPress() {
              cov_21z2r3zj0g().f[12]++;
              cov_21z2r3zj0g().s[76]++;
              return setShowPricingPlans(true);
            },
            children: [_jsx(Text, {
              style: styles.viewPlansButtonText,
              children: "View Premium Plans"
            }), _jsx(Ionicons, {
              name: "arrow-forward",
              size: 20,
              color: "#FFFFFF"
            })]
          }), _jsxs(View, {
            style: styles.freeFeatures,
            children: [_jsx(Text, {
              style: styles.freeFeaturesTitle,
              children: "What you get with Free:"
            }), currentTier.features.map(function (featureId) {
              cov_21z2r3zj0g().f[13]++;
              cov_21z2r3zj0g().s[77]++;
              return _jsxs(View, {
                style: styles.featureItem,
                children: [_jsx(Ionicons, {
                  name: "checkmark-circle",
                  size: 16,
                  color: "#10B981"
                }), _jsx(Text, {
                  style: styles.featureText,
                  children: featureId.replace(/_/g, ' ').replace(/\b\w/g, function (l) {
                    cov_21z2r3zj0g().f[14]++;
                    cov_21z2r3zj0g().s[78]++;
                    return l.toUpperCase();
                  })
                })]
              }, featureId);
            })]
          })]
        })]
      })
    });
  } else {
    cov_21z2r3zj0g().b[15][1]++;
  }
  cov_21z2r3zj0g().s[79]++;
  if (showPricingPlans) {
    cov_21z2r3zj0g().b[17][0]++;
    cov_21z2r3zj0g().s[80]++;
    return _jsxs(View, {
      style: styles.container,
      children: [_jsxs(View, {
        style: styles.pricingHeader,
        children: [_jsx(TouchableOpacity, {
          style: styles.backButton,
          onPress: function onPress() {
            cov_21z2r3zj0g().f[15]++;
            cov_21z2r3zj0g().s[81]++;
            return setShowPricingPlans(false);
          },
          children: _jsx(Ionicons, {
            name: "arrow-back",
            size: 24,
            color: "#6B7280"
          })
        }), _jsx(Text, {
          style: styles.pricingTitle,
          children: "Choose Your Plan"
        })]
      }), _jsx(PricingPlans, {
        currentTier: currentTier.id
      })]
    });
  } else {
    cov_21z2r3zj0g().b[17][1]++;
  }
  cov_21z2r3zj0g().s[82]++;
  return _jsxs(ScrollView, {
    style: styles.container,
    refreshControl: _jsx(RefreshControl, {
      refreshing: refreshing,
      onRefresh: handleRefresh
    }),
    children: [_jsxs(View, {
      style: styles.subscriptionCard,
      children: [_jsxs(LinearGradient, {
        colors: currentTier.gradient,
        style: styles.subscriptionHeader,
        start: {
          x: 0,
          y: 0
        },
        end: {
          x: 1,
          y: 1
        },
        children: [_jsxs(View, {
          style: styles.subscriptionInfo,
          children: [_jsxs(Text, {
            style: styles.subscriptionTier,
            children: [currentTier.name, " Plan"]
          }), _jsx(View, {
            style: styles.statusContainer,
            children: _jsx(View, {
              style: [styles.statusBadge, {
                backgroundColor: getStatusColor((cov_21z2r3zj0g().b[18][0]++, subscription == null ? void 0 : subscription.status) || (cov_21z2r3zj0g().b[18][1]++, 'active'))
              }],
              children: _jsx(Text, {
                style: styles.statusText,
                children: getStatusText((cov_21z2r3zj0g().b[19][0]++, subscription == null ? void 0 : subscription.status) || (cov_21z2r3zj0g().b[19][1]++, 'active'))
              })
            })
          })]
        }), (cov_21z2r3zj0g().b[20][0]++, subscription) && (cov_21z2r3zj0g().b[20][1]++, _jsxs(View, {
          style: styles.billingInfo,
          children: [_jsxs(Text, {
            style: styles.billingText,
            children: ["Next billing: ", formatDate(subscription.current_period_end)]
          }), _jsxs(Text, {
            style: styles.billingAmount,
            children: [formatPrice(currentTier.price_monthly), "/month"]
          })]
        }))]
      }), _jsxs(View, {
        style: styles.subscriptionActions,
        children: [_jsxs(TouchableOpacity, {
          style: styles.actionButton,
          onPress: function onPress() {
            cov_21z2r3zj0g().f[16]++;
            cov_21z2r3zj0g().s[83]++;
            return setShowPricingPlans(true);
          },
          children: [_jsx(Ionicons, {
            name: "trending-up",
            size: 20,
            color: "#3B82F6"
          }), _jsx(Text, {
            style: styles.actionButtonText,
            children: "Upgrade Plan"
          })]
        }), subscription != null && subscription.cancel_at_period_end ? (cov_21z2r3zj0g().b[21][0]++, _jsxs(TouchableOpacity, {
          style: styles.actionButton,
          onPress: handleResumeSubscription,
          children: [_jsx(Ionicons, {
            name: "play",
            size: 20,
            color: "#10B981"
          }), _jsx(Text, {
            style: styles.actionButtonText,
            children: "Resume"
          })]
        })) : (cov_21z2r3zj0g().b[21][1]++, _jsxs(TouchableOpacity, {
          style: styles.actionButton,
          onPress: handleCancelSubscription,
          children: [_jsx(Ionicons, {
            name: "pause",
            size: 20,
            color: "#EF4444"
          }), _jsx(Text, {
            style: styles.actionButtonText,
            children: "Cancel"
          })]
        }))]
      })]
    }), _jsxs(View, {
      style: styles.billingHistoryCard,
      children: [_jsx(Text, {
        style: styles.sectionTitle,
        children: "Billing History"
      }), invoices.length === 0 ? (cov_21z2r3zj0g().b[22][0]++, _jsxs(View, {
        style: styles.emptyState,
        children: [_jsx(Ionicons, {
          name: "receipt-outline",
          size: 48,
          color: "#9CA3AF"
        }), _jsx(Text, {
          style: styles.emptyStateText,
          children: "No billing history yet"
        })]
      })) : (cov_21z2r3zj0g().b[22][1]++, _jsx(View, {
        style: styles.invoicesList,
        children: invoices.map(function (invoice) {
          cov_21z2r3zj0g().f[17]++;
          cov_21z2r3zj0g().s[84]++;
          return _jsxs(View, {
            style: styles.invoiceItem,
            children: [_jsxs(View, {
              style: styles.invoiceInfo,
              children: [_jsx(Text, {
                style: styles.invoiceDate,
                children: formatDate(invoice.invoice_date)
              }), _jsx(Text, {
                style: styles.invoiceAmount,
                children: formatPrice(invoice.amount, invoice.currency)
              })]
            }), _jsxs(View, {
              style: styles.invoiceActions,
              children: [_jsx(View, {
                style: [styles.invoiceStatus, {
                  backgroundColor: invoice.status === 'paid' ? (cov_21z2r3zj0g().b[23][0]++, '#10B981') : (cov_21z2r3zj0g().b[23][1]++, '#EF4444')
                }],
                children: _jsx(Text, {
                  style: styles.invoiceStatusText,
                  children: invoice.status.toUpperCase()
                })
              }), (cov_21z2r3zj0g().b[24][0]++, invoice.status === 'paid') && (cov_21z2r3zj0g().b[24][1]++, _jsx(TouchableOpacity, {
                style: styles.downloadButton,
                onPress: function onPress() {
                  cov_21z2r3zj0g().f[18]++;
                  cov_21z2r3zj0g().s[85]++;
                  return handleDownloadInvoice(invoice.id);
                },
                children: _jsx(Ionicons, {
                  name: "download-outline",
                  size: 16,
                  color: "#6B7280"
                })
              }))]
            })]
          }, invoice.id);
        })
      }))]
    }), _jsxs(View, {
      style: styles.supportCard,
      children: [_jsx(Text, {
        style: styles.sectionTitle,
        children: "Need Help?"
      }), _jsx(Text, {
        style: styles.supportText,
        children: "Have questions about your subscription? Our support team is here to help."
      }), _jsxs(TouchableOpacity, {
        style: styles.supportButton,
        children: [_jsx(Ionicons, {
          name: "chatbubble-outline",
          size: 20,
          color: "#3B82F6"
        }), _jsx(Text, {
          style: styles.supportButtonText,
          children: "Contact Support"
        })]
      })]
    })]
  });
}
var styles = (cov_21z2r3zj0g().s[86]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280'
  },
  notAuthenticatedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24
  },
  notAuthenticatedTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8
  },
  notAuthenticatedText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 24
  },
  signInButton: {
    backgroundColor: '#3B82F6',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8
  },
  signInButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  freeUserContainer: {
    flex: 1
  },
  upgradeHeader: {
    padding: 32,
    alignItems: 'center'
  },
  upgradeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center'
  },
  upgradeSubtitle: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.9,
    textAlign: 'center'
  },
  freeUserContent: {
    padding: 24
  },
  currentPlanCard: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  currentPlanTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8
  },
  currentPlanDescription: {
    fontSize: 14,
    color: '#6B7280'
  },
  viewPlansButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
    marginBottom: 24,
    gap: 8
  },
  viewPlansButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  freeFeatures: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  freeFeaturesTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  featureText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8
  },
  pricingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  backButton: {
    padding: 8,
    marginRight: 16
  },
  pricingTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827'
  },
  subscriptionCard: {
    margin: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4
  },
  subscriptionHeader: {
    padding: 24
  },
  subscriptionInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16
  },
  subscriptionTier: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF'
  },
  statusContainer: {
    alignItems: 'flex-end'
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  billingInfo: {
    alignItems: 'center'
  },
  billingText: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
    marginBottom: 4
  },
  billingAmount: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  subscriptionActions: {
    flexDirection: 'row',
    padding: 16,
    gap: 12
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    gap: 8
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151'
  },
  billingHistoryCard: {
    margin: 16,
    marginTop: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 32
  },
  emptyStateText: {
    fontSize: 16,
    color: '#9CA3AF',
    marginTop: 12
  },
  invoicesList: {
    gap: 12
  },
  invoiceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6'
  },
  invoiceInfo: {
    flex: 1
  },
  invoiceDate: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4
  },
  invoiceAmount: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827'
  },
  invoiceActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12
  },
  invoiceStatus: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8
  },
  invoiceStatusText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  downloadButton: {
    padding: 8
  },
  supportCard: {
    margin: 16,
    marginTop: 0,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  supportText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 16,
    lineHeight: 20
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#3B82F6',
    gap: 8
  },
  supportButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#3B82F6'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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