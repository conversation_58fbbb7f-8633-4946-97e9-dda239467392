54a7d46a051b997a2794ee9232dd3c69
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = useEvent;
var _addEventListener = require("../addEventListener");
var _useLayoutEffect = _interopRequireDefault(require("../useLayoutEffect"));
var _useStable = _interopRequireDefault(require("../useStable"));
function useEvent(eventType, options) {
  var targetListeners = (0, _useStable.default)(function () {
    return new Map();
  });
  var addListener = (0, _useStable.default)(function () {
    return function (target, callback) {
      var removeTargetListener = targetListeners.get(target);
      if (removeTargetListener != null) {
        removeTargetListener();
      }
      if (callback == null) {
        targetListeners.delete(target);
        callback = function callback() {};
      }
      var removeEventListener = (0, _addEventListener.addEventListener)(target, eventType, callback, options);
      targetListeners.set(target, removeEventListener);
      return removeEventListener;
    };
  });
  (0, _useLayoutEffect.default)(function () {
    return function () {
      targetListeners.forEach(function (removeListener) {
        removeListener();
      });
      targetListeners.clear();
    };
  }, [targetListeners]);
  return addListener;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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