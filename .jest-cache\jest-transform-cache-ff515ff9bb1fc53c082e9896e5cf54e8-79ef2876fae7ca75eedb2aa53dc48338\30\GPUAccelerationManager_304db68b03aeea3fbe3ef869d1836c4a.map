{"version": 3, "names": ["Platform", "performanceMonitor", "GPUAccelerationManager", "_classCallCheck", "gpuCapabilities", "cov_9gk90rihy", "s", "computeQueue", "activeComputations", "Map", "performanceHistory", "computeShaders", "MAX_CONCURRENT_TASKS", "MEMORY_POOL_SIZE", "PERFORMANCE_THRESHOLD", "f", "memoryPool", "GPUMemoryPool", "initializeGPUAcceleration", "_createClass", "key", "value", "_initializeGPUAcceleration", "_asyncToGenerator", "detectGPUCapabilities", "initializeComputeShaders", "startComputeQueueProcessor", "startPerformanceMonitoring", "console", "log", "error", "apply", "arguments", "_executeComputeTask", "task", "_this$gpuCapabilities", "startTime", "Date", "now", "validateComputeTask", "b", "Error", "id", "useGPU", "shouldUseGPU", "result", "supportsCompute", "executeOnGPU", "constraints", "fallbackToCPU", "executeOnCPU", "trackComputePerformance", "totalTime", "trackDatabaseQuery", "createErrorResult", "executeComputeTask", "_x", "_analyzeVideo", "computeTask", "type", "priority", "data", "input", "videoData", "parameters", "analysisType", "frameRate", "resolution", "outputFormat", "expectedOutputSize", "estimateVideoAnalysisOutputSize", "maxExecutionTime", "maxMemoryUsage", "requiresGPU", "analyzeVideo", "_x2", "_runMLInference", "inputTensor", "buffer", "modelId", "inputShape", "outputShape", "precision", "reduce", "a", "runMLInference", "_x3", "getGPUPerformanceMetrics", "recentTasks", "slice", "successfulTasks", "filter", "entry", "success", "averageExecutionTime", "length", "sum", "executionTime", "tasksPerSecond", "timestamp", "efficiency", "performance", "errorRate", "capabilities", "utilization", "calculateGPUUtilization", "memoryUsage", "getUsagePercentage", "_optimizeGPUPerformance", "metrics", "optimize", "optimizeComputeQueue", "updateComputeShaders", "optimizeGPUPerformance", "_detectGPUCapabilities", "mockCapabilities", "vendor", "OS", "model", "computeUnits", "memoryMB", "maxTextureSize", "supportedAPIs", "gflops", "bandwidth", "_initializeComputeShaders", "shaderConfigs", "name", "config", "shader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "initialize", "set", "supportedTypes", "includes", "_shouldUseGPU", "_this$gpuCapabilities2", "gpuBeneficialTypes", "dataSizeThreshold", "byteLength", "_x4", "_executeOnGPU", "inputBuffer", "allocate", "outputBuffer", "copyToGPU", "get", "gpuResult", "execute", "output", "copyFromGPU", "free", "taskId", "memoryUsed", "processingUnit", "throughput", "calculateThroughput", "accuracy", "_x5", "_executeOnCPU", "simulateCPUExecution", "_x6", "_simulateCPUExecution", "processingTime", "estimateProcessingTime", "Promise", "resolve", "setTimeout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_x7", "unit", "baseTime", "multiplier", "typeMultipliers", "_ref", "width", "height", "frameCount", "Math", "ceil", "dataSize", "activeTasks", "size", "_copyToGPU", "gpuBuffer", "_x8", "_x9", "_copyFromGPU", "_x0", "_x1", "code", "message", "fallbackUsed", "push", "shift", "_this", "setInterval", "processComputeQueue", "_processComputeQueue", "sort", "priorityOrder", "critical", "high", "medium", "low", "delete", "_optimizeComputeQueue", "_updateComputeShaders", "_this2", "monitorGPUPerformance", "toFixed", "usedSize", "allocations", "totalSize", "_allocate", "freeOldestAllocations", "random", "toString", "substr", "_x10", "_free", "allocation", "_x11", "_optimize", "fiveMinutesAgo", "_ref2", "entries", "_ref3", "_slicedToArray", "_freeOldestAllocations", "requiredSize", "sortedAllocations", "Array", "from", "freedSize", "_ref4", "_ref5", "_x12", "isInitialized", "_initialize", "_execute", "undefined", "_x13", "_x14", "_x15", "gpuAccelerationManager"], "sources": ["GPUAccelerationManager.ts"], "sourcesContent": ["/**\n * GPU Acceleration Manager\n * \n * Hardware-accelerated computing system for intensive operations including\n * video analysis, machine learning, and graphics processing using GPU compute.\n */\n\nimport { Platform } from 'react-native';\nimport { performanceMonitor } from '@/utils/performance';\nimport { smartResourceManager } from '@/services/ai/SmartResourceManager';\n\ninterface GPUCapabilities {\n  vendor: string;\n  model: string;\n  computeUnits: number;\n  memoryMB: number;\n  maxTextureSize: number;\n  supportsCompute: boolean;\n  supportedAPIs: string[];\n  performance: {\n    gflops: number; // Gigaflops\n    bandwidth: number; // GB/s\n    efficiency: number; // Performance per watt\n  };\n}\n\ninterface ComputeTask {\n  id: string;\n  type: 'video_analysis' | 'ml_inference' | 'image_processing' | 'physics_simulation' | 'data_processing';\n  priority: 'low' | 'medium' | 'high' | 'critical';\n  data: {\n    input: ArrayBuffer | Float32Array | Uint8Array;\n    parameters: Record<string, any>;\n    expectedOutputSize: number;\n  };\n  constraints: {\n    maxExecutionTime: number; // milliseconds\n    maxMemoryUsage: number; // bytes\n    requiresGPU: boolean;\n    fallbackToCPU: boolean;\n  };\n}\n\ninterface ComputeResult {\n  taskId: string;\n  success: boolean;\n  output: ArrayBuffer | Float32Array | Uint8Array | null;\n  executionTime: number;\n  memoryUsed: number;\n  processingUnit: 'gpu' | 'cpu' | 'hybrid';\n  performance: {\n    throughput: number; // operations per second\n    efficiency: number; // performance per watt\n    accuracy?: number; // for ML tasks\n  };\n  error?: {\n    code: string;\n    message: string;\n    fallbackUsed: boolean;\n  };\n}\n\ninterface VideoAnalysisTask {\n  videoData: ArrayBuffer;\n  analysisType: 'pose_detection' | 'motion_tracking' | 'technique_analysis' | 'ball_tracking';\n  frameRate: number;\n  resolution: { width: number; height: number };\n  outputFormat: 'keypoints' | 'annotations' | 'metrics' | 'highlights';\n}\n\ninterface MLInferenceTask {\n  modelId: string;\n  inputTensor: Float32Array;\n  inputShape: number[];\n  outputShape: number[];\n  precision: 'fp16' | 'fp32' | 'int8';\n}\n\n/**\n * GPU-Accelerated Computing Manager\n */\nclass GPUAccelerationManager {\n  private gpuCapabilities: GPUCapabilities | null = null;\n  private computeQueue: ComputeTask[] = [];\n  private activeComputations: Map<string, ComputeTask> = new Map();\n  private performanceHistory: Array<{ timestamp: number; task: ComputeTask; result: ComputeResult }> = [];\n  private memoryPool: GPUMemoryPool;\n  private computeShaders: Map<string, ComputeShader> = new Map();\n  \n  private readonly MAX_CONCURRENT_TASKS = 4;\n  private readonly MEMORY_POOL_SIZE = 256 * 1024 * 1024; // 256MB\n  private readonly PERFORMANCE_THRESHOLD = 0.8; // 80% efficiency threshold\n\n  constructor() {\n    this.memoryPool = new GPUMemoryPool(this.MEMORY_POOL_SIZE);\n    this.initializeGPUAcceleration();\n  }\n\n  /**\n   * Initialize GPU acceleration system\n   */\n  private async initializeGPUAcceleration(): Promise<void> {\n    try {\n      // Detect GPU capabilities\n      this.gpuCapabilities = await this.detectGPUCapabilities();\n      \n      // Initialize compute shaders\n      await this.initializeComputeShaders();\n      \n      // Start compute queue processor\n      this.startComputeQueueProcessor();\n      \n      // Initialize performance monitoring\n      this.startPerformanceMonitoring();\n      \n      console.log('GPU Acceleration Manager initialized successfully');\n      console.log('GPU Capabilities:', this.gpuCapabilities);\n    } catch (error) {\n      console.error('Failed to initialize GPU Acceleration Manager:', error);\n    }\n  }\n\n  /**\n   * Execute compute task with GPU acceleration\n   */\n  async executeComputeTask(task: ComputeTask): Promise<ComputeResult> {\n    try {\n      const startTime = Date.now();\n      \n      // Validate task\n      if (!this.validateComputeTask(task)) {\n        throw new Error(`Invalid compute task: ${task.id}`);\n      }\n\n      // Check if GPU acceleration is available and beneficial\n      const useGPU = await this.shouldUseGPU(task);\n      \n      let result: ComputeResult;\n      \n      if (useGPU && this.gpuCapabilities?.supportsCompute) {\n        result = await this.executeOnGPU(task);\n      } else if (task.constraints.fallbackToCPU) {\n        result = await this.executeOnCPU(task);\n      } else {\n        throw new Error('GPU not available and CPU fallback disabled');\n      }\n\n      // Track performance\n      this.trackComputePerformance(task, result);\n      \n      const totalTime = Date.now() - startTime;\n      performanceMonitor.trackDatabaseQuery('gpu_compute_task', totalTime);\n      \n      return result;\n\n    } catch (error) {\n      console.error('Failed to execute compute task:', error);\n      return this.createErrorResult(task, error);\n    }\n  }\n\n  /**\n   * Analyze video with GPU acceleration\n   */\n  async analyzeVideo(task: VideoAnalysisTask): Promise<ComputeResult> {\n    const computeTask: ComputeTask = {\n      id: `video_analysis_${Date.now()}`,\n      type: 'video_analysis',\n      priority: 'high',\n      data: {\n        input: task.videoData,\n        parameters: {\n          analysisType: task.analysisType,\n          frameRate: task.frameRate,\n          resolution: task.resolution,\n          outputFormat: task.outputFormat,\n        },\n        expectedOutputSize: this.estimateVideoAnalysisOutputSize(task),\n      },\n      constraints: {\n        maxExecutionTime: 30000, // 30 seconds\n        maxMemoryUsage: 512 * 1024 * 1024, // 512MB\n        requiresGPU: true,\n        fallbackToCPU: true,\n      },\n    };\n\n    return await this.executeComputeTask(computeTask);\n  }\n\n  /**\n   * Run ML inference with GPU acceleration\n   */\n  async runMLInference(task: MLInferenceTask): Promise<ComputeResult> {\n    const computeTask: ComputeTask = {\n      id: `ml_inference_${Date.now()}`,\n      type: 'ml_inference',\n      priority: 'medium',\n      data: {\n        input: task.inputTensor.buffer,\n        parameters: {\n          modelId: task.modelId,\n          inputShape: task.inputShape,\n          outputShape: task.outputShape,\n          precision: task.precision,\n        },\n        expectedOutputSize: task.outputShape.reduce((a, b) => a * b, 1) * 4, // 4 bytes per float32\n      },\n      constraints: {\n        maxExecutionTime: 5000, // 5 seconds\n        maxMemoryUsage: 128 * 1024 * 1024, // 128MB\n        requiresGPU: false,\n        fallbackToCPU: true,\n      },\n    };\n\n    return await this.executeComputeTask(computeTask);\n  }\n\n  /**\n   * Get GPU performance metrics\n   */\n  getGPUPerformanceMetrics(): {\n    capabilities: GPUCapabilities | null;\n    utilization: number;\n    memoryUsage: number;\n    averageExecutionTime: number;\n    tasksPerSecond: number;\n    efficiency: number;\n    errorRate: number;\n  } {\n    const recentTasks = this.performanceHistory.slice(-100); // Last 100 tasks\n    const successfulTasks = recentTasks.filter(entry => entry.result.success);\n    \n    const averageExecutionTime = successfulTasks.length > 0\n      ? successfulTasks.reduce((sum, entry) => sum + entry.result.executionTime, 0) / successfulTasks.length\n      : 0;\n\n    const tasksPerSecond = recentTasks.length > 0\n      ? recentTasks.length / ((Date.now() - recentTasks[0].timestamp) / 1000)\n      : 0;\n\n    const efficiency = successfulTasks.length > 0\n      ? successfulTasks.reduce((sum, entry) => sum + entry.result.performance.efficiency, 0) / successfulTasks.length\n      : 0;\n\n    const errorRate = recentTasks.length > 0\n      ? (recentTasks.length - successfulTasks.length) / recentTasks.length * 100\n      : 0;\n\n    return {\n      capabilities: this.gpuCapabilities,\n      utilization: this.calculateGPUUtilization(),\n      memoryUsage: this.memoryPool.getUsagePercentage(),\n      averageExecutionTime,\n      tasksPerSecond,\n      efficiency,\n      errorRate,\n    };\n  }\n\n  /**\n   * Optimize GPU performance based on usage patterns\n   */\n  async optimizeGPUPerformance(): Promise<void> {\n    try {\n      console.log('Optimizing GPU performance...');\n      \n      // Analyze recent performance\n      const metrics = this.getGPUPerformanceMetrics();\n      \n      // Optimize memory pool if needed\n      if (metrics.memoryUsage > 80) {\n        await this.memoryPool.optimize();\n      }\n      \n      // Adjust compute queue processing\n      if (metrics.efficiency < this.PERFORMANCE_THRESHOLD) {\n        await this.optimizeComputeQueue();\n      }\n      \n      // Update compute shaders if beneficial\n      await this.updateComputeShaders();\n      \n      console.log('GPU performance optimization completed');\n      \n    } catch (error) {\n      console.error('Failed to optimize GPU performance:', error);\n    }\n  }\n\n  // Private helper methods\n\n  private async detectGPUCapabilities(): Promise<GPUCapabilities> {\n    // In a real implementation, this would use native modules to detect actual GPU\n    const mockCapabilities: GPUCapabilities = {\n      vendor: Platform.OS === 'ios' ? 'Apple' : 'Qualcomm',\n      model: Platform.OS === 'ios' ? 'A15 Bionic GPU' : 'Adreno 660',\n      computeUnits: Platform.OS === 'ios' ? 5 : 8,\n      memoryMB: Platform.OS === 'ios' ? 6144 : 8192,\n      maxTextureSize: 16384,\n      supportsCompute: true,\n      supportedAPIs: Platform.OS === 'ios' ? ['Metal'] : ['Vulkan', 'OpenGL ES'],\n      performance: {\n        gflops: Platform.OS === 'ios' ? 2600 : 2100,\n        bandwidth: Platform.OS === 'ios' ? 238 : 204,\n        efficiency: Platform.OS === 'ios' ? 0.85 : 0.78,\n      },\n    };\n\n    return mockCapabilities;\n  }\n\n  private async initializeComputeShaders(): Promise<void> {\n    // Initialize compute shaders for different task types\n    const shaderConfigs = [\n      { name: 'video_analysis', type: 'video_analysis' },\n      { name: 'ml_inference', type: 'ml_inference' },\n      { name: 'image_processing', type: 'image_processing' },\n      { name: 'physics_simulation', type: 'physics_simulation' },\n    ];\n\n    for (const config of shaderConfigs) {\n      const shader = new ComputeShader(config.name, config.type);\n      await shader.initialize();\n      this.computeShaders.set(config.name, shader);\n    }\n  }\n\n  private validateComputeTask(task: ComputeTask): boolean {\n    // Validate task parameters\n    if (!task.id || !task.type || !task.data.input) {\n      return false;\n    }\n\n    // Check memory constraints\n    if (task.data.expectedOutputSize > this.MEMORY_POOL_SIZE) {\n      return false;\n    }\n\n    // Check if task type is supported\n    const supportedTypes = ['video_analysis', 'ml_inference', 'image_processing', 'physics_simulation', 'data_processing'];\n    if (!supportedTypes.includes(task.type)) {\n      return false;\n    }\n\n    return true;\n  }\n\n  private async shouldUseGPU(task: ComputeTask): Promise<boolean> {\n    if (!this.gpuCapabilities?.supportsCompute) {\n      return false;\n    }\n\n    // Check if task requires GPU\n    if (task.constraints.requiresGPU) {\n      return true;\n    }\n\n    // Check if GPU would be beneficial based on task type and size\n    const gpuBeneficialTypes = ['video_analysis', 'ml_inference', 'image_processing'];\n    if (!gpuBeneficialTypes.includes(task.type)) {\n      return false;\n    }\n\n    // Check data size threshold\n    const dataSizeThreshold = 1024 * 1024; // 1MB\n    if (task.data.input.byteLength < dataSizeThreshold) {\n      return false;\n    }\n\n    // Check current GPU utilization\n    const utilization = this.calculateGPUUtilization();\n    if (utilization > 90) {\n      return false;\n    }\n\n    return true;\n  }\n\n  private async executeOnGPU(task: ComputeTask): Promise<ComputeResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Allocate GPU memory\n      const inputBuffer = await this.memoryPool.allocate(task.data.input.byteLength);\n      const outputBuffer = await this.memoryPool.allocate(task.data.expectedOutputSize);\n      \n      // Copy input data to GPU\n      await this.copyToGPU(task.data.input, inputBuffer);\n      \n      // Get appropriate compute shader\n      const shader = this.computeShaders.get(task.type);\n      if (!shader) {\n        throw new Error(`No compute shader available for task type: ${task.type}`);\n      }\n      \n      // Execute on GPU\n      const gpuResult = await shader.execute(inputBuffer, outputBuffer, task.data.parameters);\n      \n      // Copy result back from GPU\n      const output = await this.copyFromGPU(outputBuffer, task.data.expectedOutputSize);\n      \n      // Free GPU memory\n      await this.memoryPool.free(inputBuffer);\n      await this.memoryPool.free(outputBuffer);\n      \n      const executionTime = Date.now() - startTime;\n      \n      return {\n        taskId: task.id,\n        success: true,\n        output,\n        executionTime,\n        memoryUsed: task.data.input.byteLength + task.data.expectedOutputSize,\n        processingUnit: 'gpu',\n        performance: {\n          throughput: this.calculateThroughput(task, executionTime),\n          efficiency: gpuResult.efficiency || 0.8,\n          accuracy: gpuResult.accuracy,\n        },\n      };\n      \n    } catch (error) {\n      console.error('GPU execution failed:', error);\n      \n      // Try CPU fallback if allowed\n      if (task.constraints.fallbackToCPU) {\n        return await this.executeOnCPU(task);\n      }\n      \n      throw error;\n    }\n  }\n\n  private async executeOnCPU(task: ComputeTask): Promise<ComputeResult> {\n    const startTime = Date.now();\n    \n    try {\n      // Simulate CPU execution (in real implementation, would use actual CPU processing)\n      const output = await this.simulateCPUExecution(task);\n      const executionTime = Date.now() - startTime;\n      \n      return {\n        taskId: task.id,\n        success: true,\n        output,\n        executionTime,\n        memoryUsed: task.data.input.byteLength + task.data.expectedOutputSize,\n        processingUnit: 'cpu',\n        performance: {\n          throughput: this.calculateThroughput(task, executionTime),\n          efficiency: 0.6, // CPU is less efficient for these tasks\n        },\n      };\n      \n    } catch (error) {\n      throw error;\n    }\n  }\n\n  private async simulateCPUExecution(task: ComputeTask): Promise<ArrayBuffer> {\n    // Simulate CPU processing time based on task complexity\n    const processingTime = this.estimateProcessingTime(task, 'cpu');\n    await new Promise(resolve => setTimeout(resolve, processingTime));\n    \n    // Return mock output data\n    return new ArrayBuffer(task.data.expectedOutputSize);\n  }\n\n  private estimateProcessingTime(task: ComputeTask, unit: 'gpu' | 'cpu'): number {\n    const baseTime = task.data.input.byteLength / (1024 * 1024) * 100; // 100ms per MB\n    const multiplier = unit === 'gpu' ? 0.3 : 1.0; // GPU is ~3x faster\n    \n    const typeMultipliers = {\n      'video_analysis': 3.0,\n      'ml_inference': 2.0,\n      'image_processing': 1.5,\n      'physics_simulation': 2.5,\n      'data_processing': 1.0,\n    };\n    \n    return baseTime * multiplier * (typeMultipliers[task.type] || 1.0);\n  }\n\n  private estimateVideoAnalysisOutputSize(task: VideoAnalysisTask): number {\n    const { width, height } = task.resolution;\n    const frameCount = Math.ceil(task.videoData.byteLength / (width * height * 3)); // Estimate frame count\n    \n    switch (task.outputFormat) {\n      case 'keypoints':\n        return frameCount * 33 * 3 * 4; // 33 keypoints, 3 coordinates, 4 bytes each\n      case 'annotations':\n        return frameCount * 1024; // 1KB per frame\n      case 'metrics':\n        return frameCount * 256; // 256 bytes per frame\n      case 'highlights':\n        return Math.ceil(frameCount * 0.1) * width * height * 3; // 10% of frames as highlights\n      default:\n        return frameCount * 512; // Default 512 bytes per frame\n    }\n  }\n\n  private calculateThroughput(task: ComputeTask, executionTime: number): number {\n    const dataSize = task.data.input.byteLength;\n    return (dataSize / 1024 / 1024) / (executionTime / 1000); // MB/s\n  }\n\n  private calculateGPUUtilization(): number {\n    // Calculate current GPU utilization based on active tasks\n    const activeTasks = this.activeComputations.size;\n    return (activeTasks / this.MAX_CONCURRENT_TASKS) * 100;\n  }\n\n  private async copyToGPU(data: ArrayBuffer, gpuBuffer: any): Promise<void> {\n    // Simulate GPU memory copy\n    await new Promise(resolve => setTimeout(resolve, 1));\n  }\n\n  private async copyFromGPU(gpuBuffer: any, size: number): Promise<ArrayBuffer> {\n    // Simulate GPU memory copy back\n    await new Promise(resolve => setTimeout(resolve, 1));\n    return new ArrayBuffer(size);\n  }\n\n  private createErrorResult(task: ComputeTask, error: any): ComputeResult {\n    return {\n      taskId: task.id,\n      success: false,\n      output: null,\n      executionTime: 0,\n      memoryUsed: 0,\n      processingUnit: 'cpu',\n      performance: { throughput: 0, efficiency: 0 },\n      error: {\n        code: 'EXECUTION_ERROR',\n        message: error.message || 'Unknown error',\n        fallbackUsed: false,\n      },\n    };\n  }\n\n  private trackComputePerformance(task: ComputeTask, result: ComputeResult): void {\n    this.performanceHistory.push({\n      timestamp: Date.now(),\n      task,\n      result,\n    });\n    \n    // Limit history size\n    if (this.performanceHistory.length > 1000) {\n      this.performanceHistory.shift();\n    }\n  }\n\n  private startComputeQueueProcessor(): void {\n    // Process compute queue every 100ms\n    setInterval(() => {\n      this.processComputeQueue();\n    }, 100);\n  }\n\n  private async processComputeQueue(): Promise<void> {\n    if (this.computeQueue.length === 0 || this.activeComputations.size >= this.MAX_CONCURRENT_TASKS) {\n      return;\n    }\n    \n    // Sort queue by priority\n    this.computeQueue.sort((a, b) => {\n      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };\n      return priorityOrder[b.priority] - priorityOrder[a.priority];\n    });\n    \n    // Process next task\n    const task = this.computeQueue.shift();\n    if (task) {\n      this.activeComputations.set(task.id, task);\n      \n      try {\n        await this.executeComputeTask(task);\n      } finally {\n        this.activeComputations.delete(task.id);\n      }\n    }\n  }\n\n  private async optimizeComputeQueue(): Promise<void> {\n    // Optimize compute queue processing based on performance metrics\n    console.log('Optimizing compute queue processing');\n  }\n\n  private async updateComputeShaders(): Promise<void> {\n    // Update compute shaders for better performance\n    console.log('Updating compute shaders');\n  }\n\n  private startPerformanceMonitoring(): void {\n    // Monitor GPU performance every 30 seconds\n    setInterval(() => {\n      this.monitorGPUPerformance();\n    }, 30000);\n  }\n\n  private monitorGPUPerformance(): void {\n    const metrics = this.getGPUPerformanceMetrics();\n    \n    // Log performance metrics\n    console.log('GPU Performance Metrics:', {\n      utilization: `${metrics.utilization.toFixed(1)}%`,\n      memoryUsage: `${metrics.memoryUsage.toFixed(1)}%`,\n      averageExecutionTime: `${metrics.averageExecutionTime.toFixed(1)}ms`,\n      tasksPerSecond: metrics.tasksPerSecond.toFixed(2),\n      efficiency: `${(metrics.efficiency * 100).toFixed(1)}%`,\n      errorRate: `${metrics.errorRate.toFixed(1)}%`,\n    });\n  }\n}\n\n/**\n * GPU Memory Pool Manager\n */\nclass GPUMemoryPool {\n  private totalSize: number;\n  private usedSize: number = 0;\n  private allocations: Map<string, { size: number; timestamp: number }> = new Map();\n\n  constructor(size: number) {\n    this.totalSize = size;\n  }\n\n  async allocate(size: number): Promise<string> {\n    if (this.usedSize + size > this.totalSize) {\n      await this.freeOldestAllocations(size);\n    }\n    \n    const id = `alloc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    this.allocations.set(id, { size, timestamp: Date.now() });\n    this.usedSize += size;\n    \n    return id;\n  }\n\n  async free(id: string): Promise<void> {\n    const allocation = this.allocations.get(id);\n    if (allocation) {\n      this.usedSize -= allocation.size;\n      this.allocations.delete(id);\n    }\n  }\n\n  getUsagePercentage(): number {\n    return (this.usedSize / this.totalSize) * 100;\n  }\n\n  async optimize(): Promise<void> {\n    // Free allocations older than 5 minutes\n    const fiveMinutesAgo = Date.now() - 300000;\n    \n    for (const [id, allocation] of this.allocations.entries()) {\n      if (allocation.timestamp < fiveMinutesAgo) {\n        await this.free(id);\n      }\n    }\n  }\n\n  private async freeOldestAllocations(requiredSize: number): Promise<void> {\n    const sortedAllocations = Array.from(this.allocations.entries())\n      .sort((a, b) => a[1].timestamp - b[1].timestamp);\n    \n    let freedSize = 0;\n    for (const [id, allocation] of sortedAllocations) {\n      await this.free(id);\n      freedSize += allocation.size;\n      \n      if (freedSize >= requiredSize) {\n        break;\n      }\n    }\n  }\n}\n\n/**\n * Compute Shader\n */\nclass ComputeShader {\n  private name: string;\n  private type: string;\n  private isInitialized = false;\n\n  constructor(name: string, type: string) {\n    this.name = name;\n    this.type = type;\n  }\n\n  async initialize(): Promise<void> {\n    // Initialize compute shader (would load actual shader code in real implementation)\n    this.isInitialized = true;\n    console.log(`Initialized compute shader: ${this.name}`);\n  }\n\n  async execute(\n    inputBuffer: string,\n    outputBuffer: string,\n    parameters: Record<string, any>\n  ): Promise<{ efficiency: number; accuracy?: number }> {\n    if (!this.isInitialized) {\n      throw new Error(`Compute shader not initialized: ${this.name}`);\n    }\n    \n    // Simulate shader execution\n    const executionTime = Math.random() * 50 + 10; // 10-60ms\n    await new Promise(resolve => setTimeout(resolve, executionTime));\n    \n    return {\n      efficiency: 0.8 + Math.random() * 0.15, // 80-95% efficiency\n      accuracy: this.type === 'ml_inference' ? 0.9 + Math.random() * 0.09 : undefined, // 90-99% accuracy for ML\n    };\n  }\n}\n\n// Export singleton instance\nexport const gpuAccelerationManager = new GPUAccelerationManager();\nexport default gpuAccelerationManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,kBAAkB;AAA8B,IAyEnDC,sBAAsB;EAY1B,SAAAA,uBAAA,EAAc;IAAAC,eAAA,OAAAD,sBAAA;IAAA,KAXNE,eAAe,IAAAC,aAAA,GAAAC,CAAA,OAA2B,IAAI;IAAA,KAC9CC,YAAY,IAAAF,aAAA,GAAAC,CAAA,OAAkB,EAAE;IAAA,KAChCE,kBAAkB,IAAAH,aAAA,GAAAC,CAAA,OAA6B,IAAIG,GAAG,CAAC,CAAC;IAAA,KACxDC,kBAAkB,IAAAL,aAAA,GAAAC,CAAA,OAA2E,EAAE;IAAA,KAE/FK,cAAc,IAAAN,aAAA,GAAAC,CAAA,OAA+B,IAAIG,GAAG,CAAC,CAAC;IAAA,KAE7CG,oBAAoB,IAAAP,aAAA,GAAAC,CAAA,OAAG,CAAC;IAAA,KACxBO,gBAAgB,IAAAR,aAAA,GAAAC,CAAA,OAAG,GAAG,GAAG,IAAI,GAAG,IAAI;IAAA,KACpCQ,qBAAqB,IAAAT,aAAA,GAAAC,CAAA,OAAG,GAAG;IAAAD,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAG1C,IAAI,CAACU,UAAU,GAAG,IAAIC,aAAa,CAAC,IAAI,CAACJ,gBAAgB,CAAC;IAACR,aAAA,GAAAC,CAAA;IAC3D,IAAI,CAACY,yBAAyB,CAAC,CAAC;EAClC;EAAC,OAAAC,YAAA,CAAAjB,sBAAA;IAAAkB,GAAA;IAAAC,KAAA;MAAA,IAAAC,0BAAA,GAAAC,iBAAA,CAKD,aAAyD;QAAAlB,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QACvD,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAEF,IAAI,CAACF,eAAe,SAAS,IAAI,CAACoB,qBAAqB,CAAC,CAAC;UAACnB,aAAA,GAAAC,CAAA;UAG1D,MAAM,IAAI,CAACmB,wBAAwB,CAAC,CAAC;UAACpB,aAAA,GAAAC,CAAA;UAGtC,IAAI,CAACoB,0BAA0B,CAAC,CAAC;UAACrB,aAAA,GAAAC,CAAA;UAGlC,IAAI,CAACqB,0BAA0B,CAAC,CAAC;UAACtB,aAAA,GAAAC,CAAA;UAElCsB,OAAO,CAACC,GAAG,CAAC,mDAAmD,CAAC;UAACxB,aAAA,GAAAC,CAAA;UACjEsB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACzB,eAAe,CAAC;QACxD,CAAC,CAAC,OAAO0B,KAAK,EAAE;UAAAzB,aAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;QACxE;MACF,CAAC;MAAA,SAnBaZ,yBAAyBA,CAAA;QAAA,OAAAI,0BAAA,CAAAS,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAzBd,yBAAyB;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA;MAAA,IAAAY,mBAAA,GAAAV,iBAAA,CAwBvC,WAAyBW,IAAiB,EAA0B;QAAA7B,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAClE,IAAI;UAAA,IAAA6B,qBAAA;UACF,IAAMC,SAAS,IAAA/B,aAAA,GAAAC,CAAA,QAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC;UAACjC,aAAA,GAAAC,CAAA;UAG7B,IAAI,CAAC,IAAI,CAACiC,mBAAmB,CAACL,IAAI,CAAC,EAAE;YAAA7B,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YACnC,MAAM,IAAImC,KAAK,CAAC,yBAAyBP,IAAI,CAACQ,EAAE,EAAE,CAAC;UACrD,CAAC;YAAArC,aAAA,GAAAmC,CAAA;UAAA;UAGD,IAAMG,MAAM,IAAAtC,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACsC,YAAY,CAACV,IAAI,CAAC;UAE5C,IAAIW,MAAqB;UAACxC,aAAA,GAAAC,CAAA;UAE1B,IAAI,CAAAD,aAAA,GAAAmC,CAAA,UAAAG,MAAM,MAAAtC,aAAA,GAAAmC,CAAA,WAAAL,qBAAA,GAAI,IAAI,CAAC/B,eAAe,aAApB+B,qBAAA,CAAsBW,eAAe,GAAE;YAAAzC,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YACnDuC,MAAM,SAAS,IAAI,CAACE,YAAY,CAACb,IAAI,CAAC;UACxC,CAAC,MAAM;YAAA7B,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YAAA,IAAI4B,IAAI,CAACc,WAAW,CAACC,aAAa,EAAE;cAAA5C,aAAA,GAAAmC,CAAA;cAAAnC,aAAA,GAAAC,CAAA;cACzCuC,MAAM,SAAS,IAAI,CAACK,YAAY,CAAChB,IAAI,CAAC;YACxC,CAAC,MAAM;cAAA7B,aAAA,GAAAmC,CAAA;cAAAnC,aAAA,GAAAC,CAAA;cACL,MAAM,IAAImC,KAAK,CAAC,6CAA6C,CAAC;YAChE;UAAA;UAACpC,aAAA,GAAAC,CAAA;UAGD,IAAI,CAAC6C,uBAAuB,CAACjB,IAAI,EAAEW,MAAM,CAAC;UAE1C,IAAMO,SAAS,IAAA/C,aAAA,GAAAC,CAAA,QAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;UAAC/B,aAAA,GAAAC,CAAA;UACzCL,kBAAkB,CAACoD,kBAAkB,CAAC,kBAAkB,EAAED,SAAS,CAAC;UAAC/C,aAAA,GAAAC,CAAA;UAErE,OAAOuC,MAAM;QAEf,CAAC,CAAC,OAAOf,KAAK,EAAE;UAAAzB,aAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UAACzB,aAAA,GAAAC,CAAA;UACxD,OAAO,IAAI,CAACgD,iBAAiB,CAACpB,IAAI,EAAEJ,KAAK,CAAC;QAC5C;MACF,CAAC;MAAA,SAlCKyB,kBAAkBA,CAAAC,EAAA;QAAA,OAAAvB,mBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBuB,kBAAkB;IAAA;EAAA;IAAAnC,GAAA;IAAAC,KAAA;MAAA,IAAAoC,aAAA,GAAAlC,iBAAA,CAuCxB,WAAmBW,IAAuB,EAA0B;QAAA7B,aAAA,GAAAU,CAAA;QAClE,IAAM2C,WAAwB,IAAArD,aAAA,GAAAC,CAAA,QAAG;UAC/BoC,EAAE,EAAE,kBAAkBL,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAClCqB,IAAI,EAAE,gBAAgB;UACtBC,QAAQ,EAAE,MAAM;UAChBC,IAAI,EAAE;YACJC,KAAK,EAAE5B,IAAI,CAAC6B,SAAS;YACrBC,UAAU,EAAE;cACVC,YAAY,EAAE/B,IAAI,CAAC+B,YAAY;cAC/BC,SAAS,EAAEhC,IAAI,CAACgC,SAAS;cACzBC,UAAU,EAAEjC,IAAI,CAACiC,UAAU;cAC3BC,YAAY,EAAElC,IAAI,CAACkC;YACrB,CAAC;YACDC,kBAAkB,EAAE,IAAI,CAACC,+BAA+B,CAACpC,IAAI;UAC/D,CAAC;UACDc,WAAW,EAAE;YACXuB,gBAAgB,EAAE,KAAK;YACvBC,cAAc,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;YACjCC,WAAW,EAAE,IAAI;YACjBxB,aAAa,EAAE;UACjB;QACF,CAAC;QAAC5C,aAAA,GAAAC,CAAA;QAEF,aAAa,IAAI,CAACiD,kBAAkB,CAACG,WAAW,CAAC;MACnD,CAAC;MAAA,SAxBKgB,YAAYA,CAAAC,GAAA;QAAA,OAAAlB,aAAA,CAAA1B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZ0C,YAAY;IAAA;EAAA;IAAAtD,GAAA;IAAAC,KAAA;MAAA,IAAAuD,eAAA,GAAArD,iBAAA,CA6BlB,WAAqBW,IAAqB,EAA0B;QAAA7B,aAAA,GAAAU,CAAA;QAClE,IAAM2C,WAAwB,IAAArD,aAAA,GAAAC,CAAA,QAAG;UAC/BoC,EAAE,EAAE,gBAAgBL,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UAChCqB,IAAI,EAAE,cAAc;UACpBC,QAAQ,EAAE,QAAQ;UAClBC,IAAI,EAAE;YACJC,KAAK,EAAE5B,IAAI,CAAC2C,WAAW,CAACC,MAAM;YAC9Bd,UAAU,EAAE;cACVe,OAAO,EAAE7C,IAAI,CAAC6C,OAAO;cACrBC,UAAU,EAAE9C,IAAI,CAAC8C,UAAU;cAC3BC,WAAW,EAAE/C,IAAI,CAAC+C,WAAW;cAC7BC,SAAS,EAAEhD,IAAI,CAACgD;YAClB,CAAC;YACDb,kBAAkB,EAAEnC,IAAI,CAAC+C,WAAW,CAACE,MAAM,CAAC,UAACC,CAAC,EAAE5C,CAAC,EAAK;cAAAnC,aAAA,GAAAU,CAAA;cAAAV,aAAA,GAAAC,CAAA;cAAA,OAAA8E,CAAC,GAAG5C,CAAC;YAAD,CAAC,EAAE,CAAC,CAAC,GAAG;UACpE,CAAC;UACDQ,WAAW,EAAE;YACXuB,gBAAgB,EAAE,IAAI;YACtBC,cAAc,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;YACjCC,WAAW,EAAE,KAAK;YAClBxB,aAAa,EAAE;UACjB;QACF,CAAC;QAAC5C,aAAA,GAAAC,CAAA;QAEF,aAAa,IAAI,CAACiD,kBAAkB,CAACG,WAAW,CAAC;MACnD,CAAC;MAAA,SAxBK2B,cAAcA,CAAAC,GAAA;QAAA,OAAAV,eAAA,CAAA7C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdqD,cAAc;IAAA;EAAA;IAAAjE,GAAA;IAAAC,KAAA,EA6BpB,SAAAkE,wBAAwBA,CAAA,EAQtB;MAAAlF,aAAA,GAAAU,CAAA;MACA,IAAMyE,WAAW,IAAAnF,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACI,kBAAkB,CAAC+E,KAAK,CAAC,CAAC,GAAG,CAAC;MACvD,IAAMC,eAAe,IAAArF,aAAA,GAAAC,CAAA,QAAGkF,WAAW,CAACG,MAAM,CAAC,UAAAC,KAAK,EAAI;QAAAvF,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAAA,OAAAsF,KAAK,CAAC/C,MAAM,CAACgD,OAAO;MAAD,CAAC,CAAC;MAEzE,IAAMC,oBAAoB,IAAAzF,aAAA,GAAAC,CAAA,QAAGoF,eAAe,CAACK,MAAM,GAAG,CAAC,IAAA1F,aAAA,GAAAmC,CAAA,UACnDkD,eAAe,CAACP,MAAM,CAAC,UAACa,GAAG,EAAEJ,KAAK,EAAK;QAAAvF,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAAA,OAAA0F,GAAG,GAAGJ,KAAK,CAAC/C,MAAM,CAACoD,aAAa;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGP,eAAe,CAACK,MAAM,KAAA1F,aAAA,GAAAmC,CAAA,UACpG,CAAC;MAEL,IAAM0D,cAAc,IAAA7F,aAAA,GAAAC,CAAA,QAAGkF,WAAW,CAACO,MAAM,GAAG,CAAC,IAAA1F,aAAA,GAAAmC,CAAA,UACzCgD,WAAW,CAACO,MAAM,IAAI,CAAC1D,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkD,WAAW,CAAC,CAAC,CAAC,CAACW,SAAS,IAAI,IAAI,CAAC,KAAA9F,aAAA,GAAAmC,CAAA,UACrE,CAAC;MAEL,IAAM4D,UAAU,IAAA/F,aAAA,GAAAC,CAAA,QAAGoF,eAAe,CAACK,MAAM,GAAG,CAAC,IAAA1F,aAAA,GAAAmC,CAAA,UACzCkD,eAAe,CAACP,MAAM,CAAC,UAACa,GAAG,EAAEJ,KAAK,EAAK;QAAAvF,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAAA,OAAA0F,GAAG,GAAGJ,KAAK,CAAC/C,MAAM,CAACwD,WAAW,CAACD,UAAU;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGV,eAAe,CAACK,MAAM,KAAA1F,aAAA,GAAAmC,CAAA,UAC7G,CAAC;MAEL,IAAM8D,SAAS,IAAAjG,aAAA,GAAAC,CAAA,QAAGkF,WAAW,CAACO,MAAM,GAAG,CAAC,IAAA1F,aAAA,GAAAmC,CAAA,UACpC,CAACgD,WAAW,CAACO,MAAM,GAAGL,eAAe,CAACK,MAAM,IAAIP,WAAW,CAACO,MAAM,GAAG,GAAG,KAAA1F,aAAA,GAAAmC,CAAA,UACxE,CAAC;MAACnC,aAAA,GAAAC,CAAA;MAEN,OAAO;QACLiG,YAAY,EAAE,IAAI,CAACnG,eAAe;QAClCoG,WAAW,EAAE,IAAI,CAACC,uBAAuB,CAAC,CAAC;QAC3CC,WAAW,EAAE,IAAI,CAAC1F,UAAU,CAAC2F,kBAAkB,CAAC,CAAC;QACjDb,oBAAoB,EAApBA,oBAAoB;QACpBI,cAAc,EAAdA,cAAc;QACdE,UAAU,EAAVA,UAAU;QACVE,SAAS,EAATA;MACF,CAAC;IACH;EAAC;IAAAlF,GAAA;IAAAC,KAAA;MAAA,IAAAuF,uBAAA,GAAArF,iBAAA,CAKD,aAA8C;QAAAlB,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAC5C,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFsB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;UAG5C,IAAMgF,OAAO,IAAAxG,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACiF,wBAAwB,CAAC,CAAC;UAAClF,aAAA,GAAAC,CAAA;UAGhD,IAAIuG,OAAO,CAACH,WAAW,GAAG,EAAE,EAAE;YAAArG,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YAC5B,MAAM,IAAI,CAACU,UAAU,CAAC8F,QAAQ,CAAC,CAAC;UAClC,CAAC;YAAAzG,aAAA,GAAAmC,CAAA;UAAA;UAAAnC,aAAA,GAAAC,CAAA;UAGD,IAAIuG,OAAO,CAACT,UAAU,GAAG,IAAI,CAACtF,qBAAqB,EAAE;YAAAT,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YACnD,MAAM,IAAI,CAACyG,oBAAoB,CAAC,CAAC;UACnC,CAAC;YAAA1G,aAAA,GAAAmC,CAAA;UAAA;UAAAnC,aAAA,GAAAC,CAAA;UAGD,MAAM,IAAI,CAAC0G,oBAAoB,CAAC,CAAC;UAAC3G,aAAA,GAAAC,CAAA;UAElCsB,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QAEvD,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAzB,aAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC7D;MACF,CAAC;MAAA,SAzBKmF,sBAAsBA,CAAA;QAAA,OAAAL,uBAAA,CAAA7E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBiF,sBAAsB;IAAA;EAAA;IAAA7F,GAAA;IAAAC,KAAA;MAAA,IAAA6F,sBAAA,GAAA3F,iBAAA,CA6B5B,aAAgE;QAAAlB,aAAA,GAAAU,CAAA;QAE9D,IAAMoG,gBAAiC,IAAA9G,aAAA,GAAAC,CAAA,QAAG;UACxC8G,MAAM,EAAEpH,QAAQ,CAACqH,EAAE,KAAK,KAAK,IAAAhH,aAAA,GAAAmC,CAAA,WAAG,OAAO,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,UAAU;UACpD8E,KAAK,EAAEtH,QAAQ,CAACqH,EAAE,KAAK,KAAK,IAAAhH,aAAA,GAAAmC,CAAA,WAAG,gBAAgB,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,YAAY;UAC9D+E,YAAY,EAAEvH,QAAQ,CAACqH,EAAE,KAAK,KAAK,IAAAhH,aAAA,GAAAmC,CAAA,WAAG,CAAC,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,CAAC;UAC3CgF,QAAQ,EAAExH,QAAQ,CAACqH,EAAE,KAAK,KAAK,IAAAhH,aAAA,GAAAmC,CAAA,WAAG,IAAI,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,IAAI;UAC7CiF,cAAc,EAAE,KAAK;UACrB3E,eAAe,EAAE,IAAI;UACrB4E,aAAa,EAAE1H,QAAQ,CAACqH,EAAE,KAAK,KAAK,IAAAhH,aAAA,GAAAmC,CAAA,WAAG,CAAC,OAAO,CAAC,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,CAAC,QAAQ,EAAE,WAAW,CAAC;UAC1E6D,WAAW,EAAE;YACXsB,MAAM,EAAE3H,QAAQ,CAACqH,EAAE,KAAK,KAAK,IAAAhH,aAAA,GAAAmC,CAAA,WAAG,IAAI,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,IAAI;YAC3CoF,SAAS,EAAE5H,QAAQ,CAACqH,EAAE,KAAK,KAAK,IAAAhH,aAAA,GAAAmC,CAAA,WAAG,GAAG,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,GAAG;YAC5C4D,UAAU,EAAEpG,QAAQ,CAACqH,EAAE,KAAK,KAAK,IAAAhH,aAAA,GAAAmC,CAAA,WAAG,IAAI,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,IAAI;UACjD;QACF,CAAC;QAACnC,aAAA,GAAAC,CAAA;QAEF,OAAO6G,gBAAgB;MACzB,CAAC;MAAA,SAlBa3F,qBAAqBA,CAAA;QAAA,OAAA0F,sBAAA,CAAAnF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBR,qBAAqB;IAAA;EAAA;IAAAJ,GAAA;IAAAC,KAAA;MAAA,IAAAwG,yBAAA,GAAAtG,iBAAA,CAoBnC,aAAwD;QAAAlB,aAAA,GAAAU,CAAA;QAEtD,IAAM+G,aAAa,IAAAzH,aAAA,GAAAC,CAAA,QAAG,CACpB;UAAEyH,IAAI,EAAE,gBAAgB;UAAEpE,IAAI,EAAE;QAAiB,CAAC,EAClD;UAAEoE,IAAI,EAAE,cAAc;UAAEpE,IAAI,EAAE;QAAe,CAAC,EAC9C;UAAEoE,IAAI,EAAE,kBAAkB;UAAEpE,IAAI,EAAE;QAAmB,CAAC,EACtD;UAAEoE,IAAI,EAAE,oBAAoB;UAAEpE,IAAI,EAAE;QAAqB,CAAC,CAC3D;QAACtD,aAAA,GAAAC,CAAA;QAEF,KAAK,IAAM0H,MAAM,IAAIF,aAAa,EAAE;UAClC,IAAMG,MAAM,IAAA5H,aAAA,GAAAC,CAAA,QAAG,IAAI4H,aAAa,CAACF,MAAM,CAACD,IAAI,EAAEC,MAAM,CAACrE,IAAI,CAAC;UAACtD,aAAA,GAAAC,CAAA;UAC3D,MAAM2H,MAAM,CAACE,UAAU,CAAC,CAAC;UAAC9H,aAAA,GAAAC,CAAA;UAC1B,IAAI,CAACK,cAAc,CAACyH,GAAG,CAACJ,MAAM,CAACD,IAAI,EAAEE,MAAM,CAAC;QAC9C;MACF,CAAC;MAAA,SAdaxG,wBAAwBA,CAAA;QAAA,OAAAoG,yBAAA,CAAA9F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAxBP,wBAAwB;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAgBtC,SAAQkB,mBAAmBA,CAACL,IAAiB,EAAW;MAAA7B,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAEtD,IAAI,CAAAD,aAAA,GAAAmC,CAAA,YAACN,IAAI,CAACQ,EAAE,MAAArC,aAAA,GAAAmC,CAAA,WAAI,CAACN,IAAI,CAACyB,IAAI,MAAAtD,aAAA,GAAAmC,CAAA,WAAI,CAACN,IAAI,CAAC2B,IAAI,CAACC,KAAK,GAAE;QAAAzD,aAAA,GAAAmC,CAAA;QAAAnC,aAAA,GAAAC,CAAA;QAC9C,OAAO,KAAK;MACd,CAAC;QAAAD,aAAA,GAAAmC,CAAA;MAAA;MAAAnC,aAAA,GAAAC,CAAA;MAGD,IAAI4B,IAAI,CAAC2B,IAAI,CAACQ,kBAAkB,GAAG,IAAI,CAACxD,gBAAgB,EAAE;QAAAR,aAAA,GAAAmC,CAAA;QAAAnC,aAAA,GAAAC,CAAA;QACxD,OAAO,KAAK;MACd,CAAC;QAAAD,aAAA,GAAAmC,CAAA;MAAA;MAGD,IAAM6F,cAAc,IAAAhI,aAAA,GAAAC,CAAA,QAAG,CAAC,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,iBAAiB,CAAC;MAACD,aAAA,GAAAC,CAAA;MACvH,IAAI,CAAC+H,cAAc,CAACC,QAAQ,CAACpG,IAAI,CAACyB,IAAI,CAAC,EAAE;QAAAtD,aAAA,GAAAmC,CAAA;QAAAnC,aAAA,GAAAC,CAAA;QACvC,OAAO,KAAK;MACd,CAAC;QAAAD,aAAA,GAAAmC,CAAA;MAAA;MAAAnC,aAAA,GAAAC,CAAA;MAED,OAAO,IAAI;IACb;EAAC;IAAAc,GAAA;IAAAC,KAAA;MAAA,IAAAkH,aAAA,GAAAhH,iBAAA,CAED,WAA2BW,IAAiB,EAAoB;QAAA,IAAAsG,sBAAA;QAAAnI,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAC9D,IAAI,GAAAkI,sBAAA,GAAC,IAAI,CAACpI,eAAe,aAApBoI,sBAAA,CAAsB1F,eAAe,GAAE;UAAAzC,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UAC1C,OAAO,KAAK;QACd,CAAC;UAAAD,aAAA,GAAAmC,CAAA;QAAA;QAAAnC,aAAA,GAAAC,CAAA;QAGD,IAAI4B,IAAI,CAACc,WAAW,CAACyB,WAAW,EAAE;UAAApE,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UAChC,OAAO,IAAI;QACb,CAAC;UAAAD,aAAA,GAAAmC,CAAA;QAAA;QAGD,IAAMiG,kBAAkB,IAAApI,aAAA,GAAAC,CAAA,QAAG,CAAC,gBAAgB,EAAE,cAAc,EAAE,kBAAkB,CAAC;QAACD,aAAA,GAAAC,CAAA;QAClF,IAAI,CAACmI,kBAAkB,CAACH,QAAQ,CAACpG,IAAI,CAACyB,IAAI,CAAC,EAAE;UAAAtD,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UAC3C,OAAO,KAAK;QACd,CAAC;UAAAD,aAAA,GAAAmC,CAAA;QAAA;QAGD,IAAMkG,iBAAiB,IAAArI,aAAA,GAAAC,CAAA,QAAG,IAAI,GAAG,IAAI;QAACD,aAAA,GAAAC,CAAA;QACtC,IAAI4B,IAAI,CAAC2B,IAAI,CAACC,KAAK,CAAC6E,UAAU,GAAGD,iBAAiB,EAAE;UAAArI,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UAClD,OAAO,KAAK;QACd,CAAC;UAAAD,aAAA,GAAAmC,CAAA;QAAA;QAGD,IAAMgE,WAAW,IAAAnG,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACmG,uBAAuB,CAAC,CAAC;QAACpG,aAAA,GAAAC,CAAA;QACnD,IAAIkG,WAAW,GAAG,EAAE,EAAE;UAAAnG,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACpB,OAAO,KAAK;QACd,CAAC;UAAAD,aAAA,GAAAmC,CAAA;QAAA;QAAAnC,aAAA,GAAAC,CAAA;QAED,OAAO,IAAI;MACb,CAAC;MAAA,SA7BasC,YAAYA,CAAAgG,GAAA;QAAA,OAAAL,aAAA,CAAAxG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZY,YAAY;IAAA;EAAA;IAAAxB,GAAA;IAAAC,KAAA;MAAA,IAAAwH,aAAA,GAAAtH,iBAAA,CA+B1B,WAA2BW,IAAiB,EAA0B;QAAA7B,aAAA,GAAAU,CAAA;QACpE,IAAMqB,SAAS,IAAA/B,aAAA,GAAAC,CAAA,QAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC;QAACjC,aAAA,GAAAC,CAAA;QAE7B,IAAI;UAEF,IAAMwI,WAAW,IAAAzI,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACU,UAAU,CAAC+H,QAAQ,CAAC7G,IAAI,CAAC2B,IAAI,CAACC,KAAK,CAAC6E,UAAU,CAAC;UAC9E,IAAMK,YAAY,IAAA3I,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACU,UAAU,CAAC+H,QAAQ,CAAC7G,IAAI,CAAC2B,IAAI,CAACQ,kBAAkB,CAAC;UAAChE,aAAA,GAAAC,CAAA;UAGlF,MAAM,IAAI,CAAC2I,SAAS,CAAC/G,IAAI,CAAC2B,IAAI,CAACC,KAAK,EAAEgF,WAAW,CAAC;UAGlD,IAAMb,MAAM,IAAA5H,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACK,cAAc,CAACuI,GAAG,CAAChH,IAAI,CAACyB,IAAI,CAAC;UAACtD,aAAA,GAAAC,CAAA;UAClD,IAAI,CAAC2H,MAAM,EAAE;YAAA5H,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YACX,MAAM,IAAImC,KAAK,CAAC,8CAA8CP,IAAI,CAACyB,IAAI,EAAE,CAAC;UAC5E,CAAC;YAAAtD,aAAA,GAAAmC,CAAA;UAAA;UAGD,IAAM2G,SAAS,IAAA9I,aAAA,GAAAC,CAAA,cAAS2H,MAAM,CAACmB,OAAO,CAACN,WAAW,EAAEE,YAAY,EAAE9G,IAAI,CAAC2B,IAAI,CAACG,UAAU,CAAC;UAGvF,IAAMqF,MAAM,IAAAhJ,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACgJ,WAAW,CAACN,YAAY,EAAE9G,IAAI,CAAC2B,IAAI,CAACQ,kBAAkB,CAAC;UAAChE,aAAA,GAAAC,CAAA;UAGlF,MAAM,IAAI,CAACU,UAAU,CAACuI,IAAI,CAACT,WAAW,CAAC;UAACzI,aAAA,GAAAC,CAAA;UACxC,MAAM,IAAI,CAACU,UAAU,CAACuI,IAAI,CAACP,YAAY,CAAC;UAExC,IAAM/C,aAAa,IAAA5F,aAAA,GAAAC,CAAA,SAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;UAAC/B,aAAA,GAAAC,CAAA;UAE7C,OAAO;YACLkJ,MAAM,EAAEtH,IAAI,CAACQ,EAAE;YACfmD,OAAO,EAAE,IAAI;YACbwD,MAAM,EAANA,MAAM;YACNpD,aAAa,EAAbA,aAAa;YACbwD,UAAU,EAAEvH,IAAI,CAAC2B,IAAI,CAACC,KAAK,CAAC6E,UAAU,GAAGzG,IAAI,CAAC2B,IAAI,CAACQ,kBAAkB;YACrEqF,cAAc,EAAE,KAAK;YACrBrD,WAAW,EAAE;cACXsD,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAAC1H,IAAI,EAAE+D,aAAa,CAAC;cACzDG,UAAU,EAAE,CAAA/F,aAAA,GAAAmC,CAAA,WAAA2G,SAAS,CAAC/C,UAAU,MAAA/F,aAAA,GAAAmC,CAAA,WAAI,GAAG;cACvCqH,QAAQ,EAAEV,SAAS,CAACU;YACtB;UACF,CAAC;QAEH,CAAC,CAAC,OAAO/H,KAAK,EAAE;UAAAzB,aAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAACzB,aAAA,GAAAC,CAAA;UAG9C,IAAI4B,IAAI,CAACc,WAAW,CAACC,aAAa,EAAE;YAAA5C,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YAClC,aAAa,IAAI,CAAC4C,YAAY,CAAChB,IAAI,CAAC;UACtC,CAAC;YAAA7B,aAAA,GAAAmC,CAAA;UAAA;UAAAnC,aAAA,GAAAC,CAAA;UAED,MAAMwB,KAAK;QACb;MACF,CAAC;MAAA,SArDaiB,YAAYA,CAAA+G,GAAA;QAAA,OAAAjB,aAAA,CAAA9G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZe,YAAY;IAAA;EAAA;IAAA3B,GAAA;IAAAC,KAAA;MAAA,IAAA0I,aAAA,GAAAxI,iBAAA,CAuD1B,WAA2BW,IAAiB,EAA0B;QAAA7B,aAAA,GAAAU,CAAA;QACpE,IAAMqB,SAAS,IAAA/B,aAAA,GAAAC,CAAA,SAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC;QAACjC,aAAA,GAAAC,CAAA;QAE7B,IAAI;UAEF,IAAM+I,MAAM,IAAAhJ,aAAA,GAAAC,CAAA,eAAS,IAAI,CAAC0J,oBAAoB,CAAC9H,IAAI,CAAC;UACpD,IAAM+D,aAAa,IAAA5F,aAAA,GAAAC,CAAA,SAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;UAAC/B,aAAA,GAAAC,CAAA;UAE7C,OAAO;YACLkJ,MAAM,EAAEtH,IAAI,CAACQ,EAAE;YACfmD,OAAO,EAAE,IAAI;YACbwD,MAAM,EAANA,MAAM;YACNpD,aAAa,EAAbA,aAAa;YACbwD,UAAU,EAAEvH,IAAI,CAAC2B,IAAI,CAACC,KAAK,CAAC6E,UAAU,GAAGzG,IAAI,CAAC2B,IAAI,CAACQ,kBAAkB;YACrEqF,cAAc,EAAE,KAAK;YACrBrD,WAAW,EAAE;cACXsD,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAAC1H,IAAI,EAAE+D,aAAa,CAAC;cACzDG,UAAU,EAAE;YACd;UACF,CAAC;QAEH,CAAC,CAAC,OAAOtE,KAAK,EAAE;UAAAzB,aAAA,GAAAC,CAAA;UACd,MAAMwB,KAAK;QACb;MACF,CAAC;MAAA,SAxBaoB,YAAYA,CAAA+G,GAAA;QAAA,OAAAF,aAAA,CAAAhI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZkB,YAAY;IAAA;EAAA;IAAA9B,GAAA;IAAAC,KAAA;MAAA,IAAA6I,qBAAA,GAAA3I,iBAAA,CA0B1B,WAAmCW,IAAiB,EAAwB;QAAA7B,aAAA,GAAAU,CAAA;QAE1E,IAAMoJ,cAAc,IAAA9J,aAAA,GAAAC,CAAA,SAAG,IAAI,CAAC8J,sBAAsB,CAAClI,IAAI,EAAE,KAAK,CAAC;QAAC7B,aAAA,GAAAC,CAAA;QAChE,MAAM,IAAI+J,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAjK,aAAA,GAAAU,CAAA;UAAAV,aAAA,GAAAC,CAAA;UAAA,OAAAiK,UAAU,CAACD,OAAO,EAAEH,cAAc,CAAC;QAAD,CAAC,CAAC;QAAC9J,aAAA,GAAAC,CAAA;QAGlE,OAAO,IAAIkK,WAAW,CAACtI,IAAI,CAAC2B,IAAI,CAACQ,kBAAkB,CAAC;MACtD,CAAC;MAAA,SAPa2F,oBAAoBA,CAAAS,GAAA;QAAA,OAAAP,qBAAA,CAAAnI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBgI,oBAAoB;IAAA;EAAA;IAAA5I,GAAA;IAAAC,KAAA,EASlC,SAAQ+I,sBAAsBA,CAAClI,IAAiB,EAAEwI,IAAmB,EAAU;MAAArK,aAAA,GAAAU,CAAA;MAC7E,IAAM4J,QAAQ,IAAAtK,aAAA,GAAAC,CAAA,SAAG4B,IAAI,CAAC2B,IAAI,CAACC,KAAK,CAAC6E,UAAU,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,GAAG;MACjE,IAAMiC,UAAU,IAAAvK,aAAA,GAAAC,CAAA,SAAGoK,IAAI,KAAK,KAAK,IAAArK,aAAA,GAAAmC,CAAA,WAAG,GAAG,KAAAnC,aAAA,GAAAmC,CAAA,WAAG,GAAG;MAE7C,IAAMqI,eAAe,IAAAxK,aAAA,GAAAC,CAAA,SAAG;QACtB,gBAAgB,EAAE,GAAG;QACrB,cAAc,EAAE,GAAG;QACnB,kBAAkB,EAAE,GAAG;QACvB,oBAAoB,EAAE,GAAG;QACzB,iBAAiB,EAAE;MACrB,CAAC;MAACD,aAAA,GAAAC,CAAA;MAEF,OAAOqK,QAAQ,GAAGC,UAAU,IAAI,CAAAvK,aAAA,GAAAmC,CAAA,WAAAqI,eAAe,CAAC3I,IAAI,CAACyB,IAAI,CAAC,MAAAtD,aAAA,GAAAmC,CAAA,WAAI,GAAG,EAAC;IACpE;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAQiD,+BAA+BA,CAACpC,IAAuB,EAAU;MAAA7B,aAAA,GAAAU,CAAA;MACvE,IAAA+J,IAAA,IAAAzK,aAAA,GAAAC,CAAA,SAA0B4B,IAAI,CAACiC,UAAU;QAAjC4G,KAAK,GAAAD,IAAA,CAALC,KAAK;QAAEC,MAAM,GAAAF,IAAA,CAANE,MAAM;MACrB,IAAMC,UAAU,IAAA5K,aAAA,GAAAC,CAAA,SAAG4K,IAAI,CAACC,IAAI,CAACjJ,IAAI,CAAC6B,SAAS,CAAC4E,UAAU,IAAIoC,KAAK,GAAGC,MAAM,GAAG,CAAC,CAAC,CAAC;MAAC3K,aAAA,GAAAC,CAAA;MAE/E,QAAQ4B,IAAI,CAACkC,YAAY;QACvB,KAAK,WAAW;UAAA/D,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACd,OAAO2K,UAAU,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC;QAChC,KAAK,aAAa;UAAA5K,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UAChB,OAAO2K,UAAU,GAAG,IAAI;QAC1B,KAAK,SAAS;UAAA5K,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACZ,OAAO2K,UAAU,GAAG,GAAG;QACzB,KAAK,YAAY;UAAA5K,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACf,OAAO4K,IAAI,CAACC,IAAI,CAACF,UAAU,GAAG,GAAG,CAAC,GAAGF,KAAK,GAAGC,MAAM,GAAG,CAAC;QACzD;UAAA3K,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACE,OAAO2K,UAAU,GAAG,GAAG;MAC3B;IACF;EAAC;IAAA7J,GAAA;IAAAC,KAAA,EAED,SAAQuI,mBAAmBA,CAAC1H,IAAiB,EAAE+D,aAAqB,EAAU;MAAA5F,aAAA,GAAAU,CAAA;MAC5E,IAAMqK,QAAQ,IAAA/K,aAAA,GAAAC,CAAA,SAAG4B,IAAI,CAAC2B,IAAI,CAACC,KAAK,CAAC6E,UAAU;MAACtI,aAAA,GAAAC,CAAA;MAC5C,OAAQ8K,QAAQ,GAAG,IAAI,GAAG,IAAI,IAAKnF,aAAa,GAAG,IAAI,CAAC;IAC1D;EAAC;IAAA7E,GAAA;IAAAC,KAAA,EAED,SAAQoF,uBAAuBA,CAAA,EAAW;MAAApG,aAAA,GAAAU,CAAA;MAExC,IAAMsK,WAAW,IAAAhL,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACE,kBAAkB,CAAC8K,IAAI;MAACjL,aAAA,GAAAC,CAAA;MACjD,OAAQ+K,WAAW,GAAG,IAAI,CAACzK,oBAAoB,GAAI,GAAG;IACxD;EAAC;IAAAQ,GAAA;IAAAC,KAAA;MAAA,IAAAkK,UAAA,GAAAhK,iBAAA,CAED,WAAwBsC,IAAiB,EAAE2H,SAAc,EAAiB;QAAAnL,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAExE,MAAM,IAAI+J,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAjK,aAAA,GAAAU,CAAA;UAAAV,aAAA,GAAAC,CAAA;UAAA,OAAAiK,UAAU,CAACD,OAAO,EAAE,CAAC,CAAC;QAAD,CAAC,CAAC;MACtD,CAAC;MAAA,SAHarB,SAASA,CAAAwC,GAAA,EAAAC,GAAA;QAAA,OAAAH,UAAA,CAAAxJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAATiH,SAAS;IAAA;EAAA;IAAA7H,GAAA;IAAAC,KAAA;MAAA,IAAAsK,YAAA,GAAApK,iBAAA,CAKvB,WAA0BiK,SAAc,EAAEF,IAAY,EAAwB;QAAAjL,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAE5E,MAAM,IAAI+J,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAjK,aAAA,GAAAU,CAAA;UAAAV,aAAA,GAAAC,CAAA;UAAA,OAAAiK,UAAU,CAACD,OAAO,EAAE,CAAC,CAAC;QAAD,CAAC,CAAC;QAACjK,aAAA,GAAAC,CAAA;QACrD,OAAO,IAAIkK,WAAW,CAACc,IAAI,CAAC;MAC9B,CAAC;MAAA,SAJahC,WAAWA,CAAAsC,GAAA,EAAAC,GAAA;QAAA,OAAAF,YAAA,CAAA5J,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXsH,WAAW;IAAA;EAAA;IAAAlI,GAAA;IAAAC,KAAA,EAMzB,SAAQiC,iBAAiBA,CAACpB,IAAiB,EAAEJ,KAAU,EAAiB;MAAAzB,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MACtE,OAAO;QACLkJ,MAAM,EAAEtH,IAAI,CAACQ,EAAE;QACfmD,OAAO,EAAE,KAAK;QACdwD,MAAM,EAAE,IAAI;QACZpD,aAAa,EAAE,CAAC;QAChBwD,UAAU,EAAE,CAAC;QACbC,cAAc,EAAE,KAAK;QACrBrD,WAAW,EAAE;UAAEsD,UAAU,EAAE,CAAC;UAAEvD,UAAU,EAAE;QAAE,CAAC;QAC7CtE,KAAK,EAAE;UACLgK,IAAI,EAAE,iBAAiB;UACvBC,OAAO,EAAE,CAAA1L,aAAA,GAAAmC,CAAA,WAAAV,KAAK,CAACiK,OAAO,MAAA1L,aAAA,GAAAmC,CAAA,WAAI,eAAe;UACzCwJ,YAAY,EAAE;QAChB;MACF,CAAC;IACH;EAAC;IAAA5K,GAAA;IAAAC,KAAA,EAED,SAAQ8B,uBAAuBA,CAACjB,IAAiB,EAAEW,MAAqB,EAAQ;MAAAxC,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAC9E,IAAI,CAACI,kBAAkB,CAACuL,IAAI,CAAC;QAC3B9F,SAAS,EAAE9D,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBJ,IAAI,EAAJA,IAAI;QACJW,MAAM,EAANA;MACF,CAAC,CAAC;MAACxC,aAAA,GAAAC,CAAA;MAGH,IAAI,IAAI,CAACI,kBAAkB,CAACqF,MAAM,GAAG,IAAI,EAAE;QAAA1F,aAAA,GAAAmC,CAAA;QAAAnC,aAAA,GAAAC,CAAA;QACzC,IAAI,CAACI,kBAAkB,CAACwL,KAAK,CAAC,CAAC;MACjC,CAAC;QAAA7L,aAAA,GAAAmC,CAAA;MAAA;IACH;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAQK,0BAA0BA,CAAA,EAAS;MAAA,IAAAyK,KAAA;MAAA9L,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAEzC8L,WAAW,CAAC,YAAM;QAAA/L,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAChB6L,KAAI,CAACE,mBAAmB,CAAC,CAAC;MAC5B,CAAC,EAAE,GAAG,CAAC;IACT;EAAC;IAAAjL,GAAA;IAAAC,KAAA;MAAA,IAAAiL,oBAAA,GAAA/K,iBAAA,CAED,aAAmD;QAAAlB,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QACjD,IAAI,CAAAD,aAAA,GAAAmC,CAAA,eAAI,CAACjC,YAAY,CAACwF,MAAM,KAAK,CAAC,MAAA1F,aAAA,GAAAmC,CAAA,WAAI,IAAI,CAAChC,kBAAkB,CAAC8K,IAAI,IAAI,IAAI,CAAC1K,oBAAoB,GAAE;UAAAP,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UAC/F;QACF,CAAC;UAAAD,aAAA,GAAAmC,CAAA;QAAA;QAAAnC,aAAA,GAAAC,CAAA;QAGD,IAAI,CAACC,YAAY,CAACgM,IAAI,CAAC,UAACnH,CAAC,EAAE5C,CAAC,EAAK;UAAAnC,aAAA,GAAAU,CAAA;UAC/B,IAAMyL,aAAa,IAAAnM,aAAA,GAAAC,CAAA,SAAG;YAAEmM,QAAQ,EAAE,CAAC;YAAEC,IAAI,EAAE,CAAC;YAAEC,MAAM,EAAE,CAAC;YAAEC,GAAG,EAAE;UAAE,CAAC;UAACvM,aAAA,GAAAC,CAAA;UAClE,OAAOkM,aAAa,CAAChK,CAAC,CAACoB,QAAQ,CAAC,GAAG4I,aAAa,CAACpH,CAAC,CAACxB,QAAQ,CAAC;QAC9D,CAAC,CAAC;QAGF,IAAM1B,IAAI,IAAA7B,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACC,YAAY,CAAC2L,KAAK,CAAC,CAAC;QAAC7L,aAAA,GAAAC,CAAA;QACvC,IAAI4B,IAAI,EAAE;UAAA7B,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACR,IAAI,CAACE,kBAAkB,CAAC4H,GAAG,CAAClG,IAAI,CAACQ,EAAE,EAAER,IAAI,CAAC;UAAC7B,aAAA,GAAAC,CAAA;UAE3C,IAAI;YAAAD,aAAA,GAAAC,CAAA;YACF,MAAM,IAAI,CAACiD,kBAAkB,CAACrB,IAAI,CAAC;UACrC,CAAC,SAAS;YAAA7B,aAAA,GAAAC,CAAA;YACR,IAAI,CAACE,kBAAkB,CAACqM,MAAM,CAAC3K,IAAI,CAACQ,EAAE,CAAC;UACzC;QACF,CAAC;UAAArC,aAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA,SAtBa6J,mBAAmBA,CAAA;QAAA,OAAAC,oBAAA,CAAAvK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBqK,mBAAmB;IAAA;EAAA;IAAAjL,GAAA;IAAAC,KAAA;MAAA,IAAAyL,qBAAA,GAAAvL,iBAAA,CAwBjC,aAAoD;QAAAlB,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAElDsB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MACpD,CAAC;MAAA,SAHakF,oBAAoBA,CAAA;QAAA,OAAA+F,qBAAA,CAAA/K,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApB+E,oBAAoB;IAAA;EAAA;IAAA3F,GAAA;IAAAC,KAAA;MAAA,IAAA0L,qBAAA,GAAAxL,iBAAA,CAKlC,aAAoD;QAAAlB,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAElDsB,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC,CAAC;MAAA,SAHamF,oBAAoBA,CAAA;QAAA,OAAA+F,qBAAA,CAAAhL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBgF,oBAAoB;IAAA;EAAA;IAAA5F,GAAA;IAAAC,KAAA,EAKlC,SAAQM,0BAA0BA,CAAA,EAAS;MAAA,IAAAqL,MAAA;MAAA3M,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAEzC8L,WAAW,CAAC,YAAM;QAAA/L,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAChB0M,MAAI,CAACC,qBAAqB,CAAC,CAAC;MAC9B,CAAC,EAAE,KAAK,CAAC;IACX;EAAC;IAAA7L,GAAA;IAAAC,KAAA,EAED,SAAQ4L,qBAAqBA,CAAA,EAAS;MAAA5M,aAAA,GAAAU,CAAA;MACpC,IAAM8F,OAAO,IAAAxG,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACiF,wBAAwB,CAAC,CAAC;MAAClF,aAAA,GAAAC,CAAA;MAGhDsB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;QACtC2E,WAAW,EAAE,GAAGK,OAAO,CAACL,WAAW,CAAC0G,OAAO,CAAC,CAAC,CAAC,GAAG;QACjDxG,WAAW,EAAE,GAAGG,OAAO,CAACH,WAAW,CAACwG,OAAO,CAAC,CAAC,CAAC,GAAG;QACjDpH,oBAAoB,EAAE,GAAGe,OAAO,CAACf,oBAAoB,CAACoH,OAAO,CAAC,CAAC,CAAC,IAAI;QACpEhH,cAAc,EAAEW,OAAO,CAACX,cAAc,CAACgH,OAAO,CAAC,CAAC,CAAC;QACjD9G,UAAU,EAAE,GAAG,CAACS,OAAO,CAACT,UAAU,GAAG,GAAG,EAAE8G,OAAO,CAAC,CAAC,CAAC,GAAG;QACvD5G,SAAS,EAAE,GAAGO,OAAO,CAACP,SAAS,CAAC4G,OAAO,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC;IACJ;EAAC;AAAA;AAAA,IAMGjM,aAAa;EAKjB,SAAAA,cAAYqK,IAAY,EAAE;IAAAnL,eAAA,OAAAc,aAAA;IAAA,KAHlBkM,QAAQ,IAAA9M,aAAA,GAAAC,CAAA,SAAW,CAAC;IAAA,KACpB8M,WAAW,IAAA/M,aAAA,GAAAC,CAAA,SAAqD,IAAIG,GAAG,CAAC,CAAC;IAAAJ,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAG/E,IAAI,CAAC+M,SAAS,GAAG/B,IAAI;EACvB;EAAC,OAAAnK,YAAA,CAAAF,aAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAAiM,SAAA,GAAA/L,iBAAA,CAED,WAAe+J,IAAY,EAAmB;QAAAjL,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAC5C,IAAI,IAAI,CAAC6M,QAAQ,GAAG7B,IAAI,GAAG,IAAI,CAAC+B,SAAS,EAAE;UAAAhN,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACzC,MAAM,IAAI,CAACiN,qBAAqB,CAACjC,IAAI,CAAC;QACxC,CAAC;UAAAjL,aAAA,GAAAmC,CAAA;QAAA;QAED,IAAME,EAAE,IAAArC,aAAA,GAAAC,CAAA,SAAG,SAAS+B,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI4I,IAAI,CAACsC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;QAACrN,aAAA,GAAAC,CAAA;QAC5E,IAAI,CAAC8M,WAAW,CAAChF,GAAG,CAAC1F,EAAE,EAAE;UAAE4I,IAAI,EAAJA,IAAI;UAAEnF,SAAS,EAAE9D,IAAI,CAACC,GAAG,CAAC;QAAE,CAAC,CAAC;QAACjC,aAAA,GAAAC,CAAA;QAC1D,IAAI,CAAC6M,QAAQ,IAAI7B,IAAI;QAACjL,aAAA,GAAAC,CAAA;QAEtB,OAAOoC,EAAE;MACX,CAAC;MAAA,SAVKqG,QAAQA,CAAA4E,IAAA;QAAA,OAAAL,SAAA,CAAAvL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAR+G,QAAQ;IAAA;EAAA;IAAA3H,GAAA;IAAAC,KAAA;MAAA,IAAAuM,KAAA,GAAArM,iBAAA,CAYd,WAAWmB,EAAU,EAAiB;QAAArC,aAAA,GAAAU,CAAA;QACpC,IAAM8M,UAAU,IAAAxN,aAAA,GAAAC,CAAA,SAAG,IAAI,CAAC8M,WAAW,CAAClE,GAAG,CAACxG,EAAE,CAAC;QAACrC,aAAA,GAAAC,CAAA;QAC5C,IAAIuN,UAAU,EAAE;UAAAxN,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACd,IAAI,CAAC6M,QAAQ,IAAIU,UAAU,CAACvC,IAAI;UAACjL,aAAA,GAAAC,CAAA;UACjC,IAAI,CAAC8M,WAAW,CAACP,MAAM,CAACnK,EAAE,CAAC;QAC7B,CAAC;UAAArC,aAAA,GAAAmC,CAAA;QAAA;MACH,CAAC;MAAA,SANK+G,IAAIA,CAAAuE,IAAA;QAAA,OAAAF,KAAA,CAAA7L,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAJuH,IAAI;IAAA;EAAA;IAAAnI,GAAA;IAAAC,KAAA,EAQV,SAAAsF,kBAAkBA,CAAA,EAAW;MAAAtG,aAAA,GAAAU,CAAA;MAAAV,aAAA,GAAAC,CAAA;MAC3B,OAAQ,IAAI,CAAC6M,QAAQ,GAAG,IAAI,CAACE,SAAS,GAAI,GAAG;IAC/C;EAAC;IAAAjM,GAAA;IAAAC,KAAA;MAAA,IAAA0M,SAAA,GAAAxM,iBAAA,CAED,aAAgC;QAAAlB,aAAA,GAAAU,CAAA;QAE9B,IAAMiN,cAAc,IAAA3N,aAAA,GAAAC,CAAA,SAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,MAAM;QAACjC,aAAA,GAAAC,CAAA;QAE3C,SAAA2N,KAAA,IAA+B,IAAI,CAACb,WAAW,CAACc,OAAO,CAAC,CAAC,EAAE;UAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAH,KAAA;UAAA,IAA/CvL,EAAE,GAAAyL,KAAA;UAAA,IAAEN,UAAU,GAAAM,KAAA;UAAA9N,aAAA,GAAAC,CAAA;UACxB,IAAIuN,UAAU,CAAC1H,SAAS,GAAG6H,cAAc,EAAE;YAAA3N,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YACzC,MAAM,IAAI,CAACiJ,IAAI,CAAC7G,EAAE,CAAC;UACrB,CAAC;YAAArC,aAAA,GAAAmC,CAAA;UAAA;QACH;MACF,CAAC;MAAA,SATKsE,QAAQA,CAAA;QAAA,OAAAiH,SAAA,CAAAhM,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAR8E,QAAQ;IAAA;EAAA;IAAA1F,GAAA;IAAAC,KAAA;MAAA,IAAAgN,sBAAA,GAAA9M,iBAAA,CAWd,WAAoC+M,YAAoB,EAAiB;QAAAjO,aAAA,GAAAU,CAAA;QACvE,IAAMwN,iBAAiB,IAAAlO,aAAA,GAAAC,CAAA,SAAGkO,KAAK,CAACC,IAAI,CAAC,IAAI,CAACrB,WAAW,CAACc,OAAO,CAAC,CAAC,CAAC,CAC7D3B,IAAI,CAAC,UAACnH,CAAC,EAAE5C,CAAC,EAAK;UAAAnC,aAAA,GAAAU,CAAA;UAAAV,aAAA,GAAAC,CAAA;UAAA,OAAA8E,CAAC,CAAC,CAAC,CAAC,CAACe,SAAS,GAAG3D,CAAC,CAAC,CAAC,CAAC,CAAC2D,SAAS;QAAD,CAAC,CAAC;QAElD,IAAIuI,SAAS,IAAArO,aAAA,GAAAC,CAAA,SAAG,CAAC;QAACD,aAAA,GAAAC,CAAA;QAClB,SAAAqO,KAAA,IAA+BJ,iBAAiB,EAAE;UAAA,IAAAK,KAAA,GAAAR,cAAA,CAAAO,KAAA;UAAA,IAAtCjM,EAAE,GAAAkM,KAAA;UAAA,IAAEf,UAAU,GAAAe,KAAA;UAAAvO,aAAA,GAAAC,CAAA;UACxB,MAAM,IAAI,CAACiJ,IAAI,CAAC7G,EAAE,CAAC;UAACrC,aAAA,GAAAC,CAAA;UACpBoO,SAAS,IAAIb,UAAU,CAACvC,IAAI;UAACjL,aAAA,GAAAC,CAAA;UAE7B,IAAIoO,SAAS,IAAIJ,YAAY,EAAE;YAAAjO,aAAA,GAAAmC,CAAA;YAAAnC,aAAA,GAAAC,CAAA;YAC7B;UACF,CAAC;YAAAD,aAAA,GAAAmC,CAAA;UAAA;QACH;MACF,CAAC;MAAA,SAba+K,qBAAqBA,CAAAsB,IAAA;QAAA,OAAAR,sBAAA,CAAAtM,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBuL,qBAAqB;IAAA;EAAA;AAAA;AAAA,IAmB/BrF,aAAa;EAKjB,SAAAA,cAAYH,IAAY,EAAEpE,IAAY,EAAE;IAAAxD,eAAA,OAAA+H,aAAA;IAAA,KAFhC4G,aAAa,IAAAzO,aAAA,GAAAC,CAAA,SAAG,KAAK;IAAAD,aAAA,GAAAU,CAAA;IAAAV,aAAA,GAAAC,CAAA;IAG3B,IAAI,CAACyH,IAAI,GAAGA,IAAI;IAAC1H,aAAA,GAAAC,CAAA;IACjB,IAAI,CAACqD,IAAI,GAAGA,IAAI;EAClB;EAAC,OAAAxC,YAAA,CAAA+G,aAAA;IAAA9G,GAAA;IAAAC,KAAA;MAAA,IAAA0N,WAAA,GAAAxN,iBAAA,CAED,aAAkC;QAAAlB,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QAEhC,IAAI,CAACwO,aAAa,GAAG,IAAI;QAACzO,aAAA,GAAAC,CAAA;QAC1BsB,OAAO,CAACC,GAAG,CAAC,+BAA+B,IAAI,CAACkG,IAAI,EAAE,CAAC;MACzD,CAAC;MAAA,SAJKI,UAAUA,CAAA;QAAA,OAAA4G,WAAA,CAAAhN,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVmG,UAAU;IAAA;EAAA;IAAA/G,GAAA;IAAAC,KAAA;MAAA,IAAA2N,QAAA,GAAAzN,iBAAA,CAMhB,WACEuH,WAAmB,EACnBE,YAAoB,EACpBhF,UAA+B,EACqB;QAAA3D,aAAA,GAAAU,CAAA;QAAAV,aAAA,GAAAC,CAAA;QACpD,IAAI,CAAC,IAAI,CAACwO,aAAa,EAAE;UAAAzO,aAAA,GAAAmC,CAAA;UAAAnC,aAAA,GAAAC,CAAA;UACvB,MAAM,IAAImC,KAAK,CAAC,mCAAmC,IAAI,CAACsF,IAAI,EAAE,CAAC;QACjE,CAAC;UAAA1H,aAAA,GAAAmC,CAAA;QAAA;QAGD,IAAMyD,aAAa,IAAA5F,aAAA,GAAAC,CAAA,SAAG4K,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;QAACnN,aAAA,GAAAC,CAAA;QAC9C,MAAM,IAAI+J,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAjK,aAAA,GAAAU,CAAA;UAAAV,aAAA,GAAAC,CAAA;UAAA,OAAAiK,UAAU,CAACD,OAAO,EAAErE,aAAa,CAAC;QAAD,CAAC,CAAC;QAAC5F,aAAA,GAAAC,CAAA;QAEjE,OAAO;UACL8F,UAAU,EAAE,GAAG,GAAG8E,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,IAAI;UACtC3D,QAAQ,EAAE,IAAI,CAAClG,IAAI,KAAK,cAAc,IAAAtD,aAAA,GAAAmC,CAAA,WAAG,GAAG,GAAG0I,IAAI,CAACsC,MAAM,CAAC,CAAC,GAAG,IAAI,KAAAnN,aAAA,GAAAmC,CAAA,WAAGyM,SAAS;QACjF,CAAC;MACH,CAAC;MAAA,SAjBK7F,OAAOA,CAAA8F,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAJ,QAAA,CAAAjN,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPoH,OAAO;IAAA;EAAA;AAAA;AAqBf,OAAO,IAAMiG,sBAAsB,IAAAhP,aAAA,GAAAC,CAAA,SAAG,IAAIJ,sBAAsB,CAAC,CAAC;AAClE,eAAemP,sBAAsB", "ignoreList": []}