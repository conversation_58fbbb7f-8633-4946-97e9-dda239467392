e2ceb5205401e191778749e95dbf0a9f
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _View = _interopRequireDefault(require("../../exports/View"));
var _react = _interopRequireDefault(require("react"));
var _excluded = ["style"];
function UnimplementedView(_ref) {
  var style = _ref.style,
    props = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded);
  return _react.default.createElement(_View.default, (0, _extends2.default)({}, props, {
    style: [unimplementedViewStyles, style]
  }));
}
var unimplementedViewStyles = process.env.NODE_ENV !== 'production' ? {
  alignSelf: 'flex-start',
  borderColor: 'red',
  borderWidth: 1
} : {};
var _default = exports.default = UnimplementedView;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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