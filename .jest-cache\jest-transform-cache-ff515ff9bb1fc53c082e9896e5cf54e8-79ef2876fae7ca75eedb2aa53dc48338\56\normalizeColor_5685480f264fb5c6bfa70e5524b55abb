04654efdfbd8e258311115ac1a5e134d
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _isWebColor = _interopRequireDefault(require("../../../modules/isWebColor"));
var _processColor = _interopRequireDefault(require("../../../exports/processColor"));
var normalizeColor = function normalizeColor(color, opacity) {
  if (opacity === void 0) {
    opacity = 1;
  }
  if (color == null) return;
  if (typeof color === 'string' && (0, _isWebColor.default)(color)) {
    return color;
  }
  var colorInt = (0, _processColor.default)(color);
  if (colorInt != null) {
    var r = colorInt >> 16 & 255;
    var g = colorInt >> 8 & 255;
    var b = colorInt & 255;
    var a = (colorInt >> 24 & 255) / 255;
    var alpha = (a * opacity).toFixed(2);
    return "rgba(" + r + "," + g + "," + b + "," + alpha + ")";
  }
};
var _default = exports.default = normalizeColor;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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