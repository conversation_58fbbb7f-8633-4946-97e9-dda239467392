{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_Platform", "_RCTDeviceEventEmitter", "_invariant", "NativeEventEmitter", "nativeModule", "OS", "_nativeModule", "key", "value", "addListener", "eventType", "listener", "context", "_this", "_this$_nativeModule", "subscription", "remove", "_this$_nativeModule2", "removeListeners", "removeListener", "_this$_nativeModule3", "emit", "_RCTDeviceEventEmitte", "_len", "arguments", "length", "args", "Array", "_key", "apply", "concat", "removeAllListeners", "_this$_nativeModule4", "listenerCount", "module"], "sources": ["NativeEventEmitter.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _Platform = _interopRequireDefault(require(\"../../../exports/Platform\"));\nvar _RCTDeviceEventEmitter = _interopRequireDefault(require(\"./RCTDeviceEventEmitter\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\n/**\n * `NativeEventEmitter` is intended for use by Native Modules to emit events to\n * JavaScript listeners. If a `NativeModule` is supplied to the constructor, it\n * will be notified (via `addListener` and `removeListeners`) when the listener\n * count changes to manage \"native memory\".\n *\n * Currently, all native events are fired via a global `RCTDeviceEventEmitter`.\n * This means event names must be globally unique, and it means that call sites\n * can theoretically listen to `RCTDeviceEventEmitter` (although discouraged).\n */\nclass NativeEventEmitter {\n  constructor(nativeModule) {\n    if (_Platform.default.OS === 'ios') {\n      (0, _invariant.default)(nativeModule != null, '`new NativeEventEmitter()` requires a non-null argument.');\n      this._nativeModule = nativeModule;\n    }\n  }\n  addListener(eventType, listener, context) {\n    var _this$_nativeModule;\n    (_this$_nativeModule = this._nativeModule) == null ? void 0 : _this$_nativeModule.addListener(eventType);\n    var subscription = _RCTDeviceEventEmitter.default.addListener(eventType, listener, context);\n    return {\n      remove: () => {\n        if (subscription != null) {\n          var _this$_nativeModule2;\n          (_this$_nativeModule2 = this._nativeModule) == null ? void 0 : _this$_nativeModule2.removeListeners(1);\n          // $FlowFixMe[incompatible-use]\n          subscription.remove();\n          subscription = null;\n        }\n      }\n    };\n  }\n\n  /**\n   * @deprecated Use `remove` on the EventSubscription from `addListener`.\n   */\n  removeListener(eventType, listener) {\n    var _this$_nativeModule3;\n    (_this$_nativeModule3 = this._nativeModule) == null ? void 0 : _this$_nativeModule3.removeListeners(1);\n    // NOTE: This will report a deprecation notice via `console.error`.\n    // $FlowFixMe[prop-missing] - `removeListener` exists but is deprecated.\n    _RCTDeviceEventEmitter.default.removeListener(eventType, listener);\n  }\n  emit(eventType) {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    // Generally, `RCTDeviceEventEmitter` is directly invoked. But this is\n    // included for completeness.\n    _RCTDeviceEventEmitter.default.emit(eventType, ...args);\n  }\n  removeAllListeners(eventType) {\n    var _this$_nativeModule4;\n    (0, _invariant.default)(eventType != null, '`NativeEventEmitter.removeAllListener()` requires a non-null argument.');\n    (_this$_nativeModule4 = this._nativeModule) == null ? void 0 : _this$_nativeModule4.removeListeners(this.listenerCount(eventType));\n    _RCTDeviceEventEmitter.default.removeAllListeners(eventType);\n  }\n  listenerCount(eventType) {\n    return _RCTDeviceEventEmitter.default.listenerCount(eventType);\n  }\n}\nexports.default = NativeEventEmitter;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,SAAS,GAAGJ,sBAAsB,CAACH,OAAO,4BAA4B,CAAC,CAAC;AAC5E,IAAIQ,sBAAsB,GAAGL,sBAAsB,CAACH,OAAO,0BAA0B,CAAC,CAAC;AACvF,IAAIS,UAAU,GAAGN,sBAAsB,CAACH,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAAC,IAWjEU,kBAAkB;EACtB,SAAAA,mBAAYC,YAAY,EAAE;IAAA,IAAAV,gBAAA,CAAAG,OAAA,QAAAM,kBAAA;IACxB,IAAIH,SAAS,CAACH,OAAO,CAACQ,EAAE,KAAK,KAAK,EAAE;MAClC,CAAC,CAAC,EAAEH,UAAU,CAACL,OAAO,EAAEO,YAAY,IAAI,IAAI,EAAE,0DAA0D,CAAC;MACzG,IAAI,CAACE,aAAa,GAAGF,YAAY;IACnC;EACF;EAAC,WAAAT,aAAA,CAAAE,OAAA,EAAAM,kBAAA;IAAAI,GAAA;IAAAC,KAAA,EACD,SAAAC,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MAAA,IAAAC,KAAA;MACxC,IAAIC,mBAAmB;MACvB,CAACA,mBAAmB,GAAG,IAAI,CAACR,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGQ,mBAAmB,CAACL,WAAW,CAACC,SAAS,CAAC;MACxG,IAAIK,YAAY,GAAGd,sBAAsB,CAACJ,OAAO,CAACY,WAAW,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,CAAC;MAC3F,OAAO;QACLI,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;UACZ,IAAID,YAAY,IAAI,IAAI,EAAE;YACxB,IAAIE,oBAAoB;YACxB,CAACA,oBAAoB,GAAGJ,KAAI,CAACP,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,oBAAoB,CAACC,eAAe,CAAC,CAAC,CAAC;YAEtGH,YAAY,CAACC,MAAM,CAAC,CAAC;YACrBD,YAAY,GAAG,IAAI;UACrB;QACF;MACF,CAAC;IACH;EAAC;IAAAR,GAAA;IAAAC,KAAA,EAKD,SAAAW,cAAcA,CAACT,SAAS,EAAEC,QAAQ,EAAE;MAClC,IAAIS,oBAAoB;MACxB,CAACA,oBAAoB,GAAG,IAAI,CAACd,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGc,oBAAoB,CAACF,eAAe,CAAC,CAAC,CAAC;MAGtGjB,sBAAsB,CAACJ,OAAO,CAACsB,cAAc,CAACT,SAAS,EAAEC,QAAQ,CAAC;IACpE;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAa,IAAIA,CAACX,SAAS,EAAE;MAAA,IAAAY,qBAAA;MACd,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAClC;MAGA,CAAAN,qBAAA,GAAArB,sBAAsB,CAACJ,OAAO,EAACwB,IAAI,CAAAQ,KAAA,CAAAP,qBAAA,GAACZ,SAAS,EAAAoB,MAAA,CAAKJ,IAAI,EAAC;IACzD;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EACD,SAAAuB,kBAAkBA,CAACrB,SAAS,EAAE;MAC5B,IAAIsB,oBAAoB;MACxB,CAAC,CAAC,EAAE9B,UAAU,CAACL,OAAO,EAAEa,SAAS,IAAI,IAAI,EAAE,wEAAwE,CAAC;MACpH,CAACsB,oBAAoB,GAAG,IAAI,CAAC1B,aAAa,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0B,oBAAoB,CAACd,eAAe,CAAC,IAAI,CAACe,aAAa,CAACvB,SAAS,CAAC,CAAC;MAClIT,sBAAsB,CAACJ,OAAO,CAACkC,kBAAkB,CAACrB,SAAS,CAAC;IAC9D;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAyB,aAAaA,CAACvB,SAAS,EAAE;MACvB,OAAOT,sBAAsB,CAACJ,OAAO,CAACoC,aAAa,CAACvB,SAAS,CAAC;IAChE;EAAC;AAAA;AAEHZ,OAAO,CAACD,OAAO,GAAGM,kBAAkB;AACpC+B,MAAM,CAACpC,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}