{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_isWebColor", "_processColor", "normalizeColor", "color", "opacity", "colorInt", "r", "g", "b", "a", "alpha", "toFixed", "_default", "module"], "sources": ["normalizeColor.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _isWebColor = _interopRequireDefault(require(\"../../../modules/isWebColor\"));\nvar _processColor = _interopRequireDefault(require(\"../../../exports/processColor\"));\n/**\n * Copyright (c) Nicolas Gallagher.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar normalizeColor = function normalizeColor(color, opacity) {\n  if (opacity === void 0) {\n    opacity = 1;\n  }\n  if (color == null) return;\n  if (typeof color === 'string' && (0, _isWebColor.default)(color)) {\n    return color;\n  }\n  var colorInt = (0, _processColor.default)(color);\n  if (colorInt != null) {\n    var r = colorInt >> 16 & 255;\n    var g = colorInt >> 8 & 255;\n    var b = colorInt & 255;\n    var a = (colorInt >> 24 & 255) / 255;\n    var alpha = (a * opacity).toFixed(2);\n    return \"rgba(\" + r + \",\" + g + \",\" + b + \",\" + alpha + \")\";\n  }\n};\nvar _default = exports.default = normalizeColor;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,WAAW,GAAGL,sBAAsB,CAACC,OAAO,8BAA8B,CAAC,CAAC;AAChF,IAAIK,aAAa,GAAGN,sBAAsB,CAACC,OAAO,gCAAgC,CAAC,CAAC;AAUpF,IAAIM,cAAc,GAAG,SAASA,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC3D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC;EACb;EACA,IAAID,KAAK,IAAI,IAAI,EAAE;EACnB,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC,CAAC,EAAEH,WAAW,CAACH,OAAO,EAAEM,KAAK,CAAC,EAAE;IAChE,OAAOA,KAAK;EACd;EACA,IAAIE,QAAQ,GAAG,CAAC,CAAC,EAAEJ,aAAa,CAACJ,OAAO,EAAEM,KAAK,CAAC;EAChD,IAAIE,QAAQ,IAAI,IAAI,EAAE;IACpB,IAAIC,CAAC,GAAGD,QAAQ,IAAI,EAAE,GAAG,GAAG;IAC5B,IAAIE,CAAC,GAAGF,QAAQ,IAAI,CAAC,GAAG,GAAG;IAC3B,IAAIG,CAAC,GAAGH,QAAQ,GAAG,GAAG;IACtB,IAAII,CAAC,GAAG,CAACJ,QAAQ,IAAI,EAAE,GAAG,GAAG,IAAI,GAAG;IACpC,IAAIK,KAAK,GAAG,CAACD,CAAC,GAAGL,OAAO,EAAEO,OAAO,CAAC,CAAC,CAAC;IACpC,OAAO,OAAO,GAAGL,CAAC,GAAG,GAAG,GAAGC,CAAC,GAAG,GAAG,GAAGC,CAAC,GAAG,GAAG,GAAGE,KAAK,GAAG,GAAG;EAC5D;AACF,CAAC;AACD,IAAIE,QAAQ,GAAGd,OAAO,CAACD,OAAO,GAAGK,cAAc;AAC/CW,MAAM,CAACf,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}