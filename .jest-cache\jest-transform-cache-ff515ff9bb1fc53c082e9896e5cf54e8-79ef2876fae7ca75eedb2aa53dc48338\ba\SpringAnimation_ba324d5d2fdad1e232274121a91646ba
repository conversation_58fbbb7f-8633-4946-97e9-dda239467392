11e607428b26e04bf8808572d52c6a41
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _Animation = _interopRequireDefault(require("./Animation"));
var _SpringConfig = _interopRequireDefault(require("../SpringConfig"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _NativeAnimatedHelper = require("../NativeAnimatedHelper");
var _AnimatedColor = _interopRequireDefault(require("../nodes/AnimatedColor"));
var SpringAnimation = function (_Animation$default) {
  function SpringAnimation(config) {
    var _this;
    (0, _classCallCheck2.default)(this, SpringAnimation);
    var _config$overshootClam, _config$restDisplacem, _config$restSpeedThre, _config$velocity, _config$velocity2, _config$delay, _config$isInteraction, _config$iterations;
    _this = _callSuper(this, SpringAnimation);
    _this._overshootClamping = (_config$overshootClam = config.overshootClamping) !== null && _config$overshootClam !== void 0 ? _config$overshootClam : false;
    _this._restDisplacementThreshold = (_config$restDisplacem = config.restDisplacementThreshold) !== null && _config$restDisplacem !== void 0 ? _config$restDisplacem : 0.001;
    _this._restSpeedThreshold = (_config$restSpeedThre = config.restSpeedThreshold) !== null && _config$restSpeedThre !== void 0 ? _config$restSpeedThre : 0.001;
    _this._initialVelocity = (_config$velocity = config.velocity) !== null && _config$velocity !== void 0 ? _config$velocity : 0;
    _this._lastVelocity = (_config$velocity2 = config.velocity) !== null && _config$velocity2 !== void 0 ? _config$velocity2 : 0;
    _this._toValue = config.toValue;
    _this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;
    _this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);
    _this._platformConfig = config.platformConfig;
    _this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !_this._useNativeDriver;
    _this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;
    if (config.stiffness !== undefined || config.damping !== undefined || config.mass !== undefined) {
      var _config$stiffness, _config$damping, _config$mass;
      (0, _invariant.default)(config.bounciness === undefined && config.speed === undefined && config.tension === undefined && config.friction === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');
      _this._stiffness = (_config$stiffness = config.stiffness) !== null && _config$stiffness !== void 0 ? _config$stiffness : 100;
      _this._damping = (_config$damping = config.damping) !== null && _config$damping !== void 0 ? _config$damping : 10;
      _this._mass = (_config$mass = config.mass) !== null && _config$mass !== void 0 ? _config$mass : 1;
    } else if (config.bounciness !== undefined || config.speed !== undefined) {
      var _config$bounciness, _config$speed;
      (0, _invariant.default)(config.tension === undefined && config.friction === undefined && config.stiffness === undefined && config.damping === undefined && config.mass === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');
      var springConfig = _SpringConfig.default.fromBouncinessAndSpeed((_config$bounciness = config.bounciness) !== null && _config$bounciness !== void 0 ? _config$bounciness : 8, (_config$speed = config.speed) !== null && _config$speed !== void 0 ? _config$speed : 12);
      _this._stiffness = springConfig.stiffness;
      _this._damping = springConfig.damping;
      _this._mass = 1;
    } else {
      var _config$tension, _config$friction;
      var _springConfig = _SpringConfig.default.fromOrigamiTensionAndFriction((_config$tension = config.tension) !== null && _config$tension !== void 0 ? _config$tension : 40, (_config$friction = config.friction) !== null && _config$friction !== void 0 ? _config$friction : 7);
      _this._stiffness = _springConfig.stiffness;
      _this._damping = _springConfig.damping;
      _this._mass = 1;
    }
    (0, _invariant.default)(_this._stiffness > 0, 'Stiffness value must be greater than 0');
    (0, _invariant.default)(_this._damping > 0, 'Damping value must be greater than 0');
    (0, _invariant.default)(_this._mass > 0, 'Mass value must be greater than 0');
    return _this;
  }
  (0, _inherits2.default)(SpringAnimation, _Animation$default);
  return (0, _createClass2.default)(SpringAnimation, [{
    key: "__getNativeAnimationConfig",
    value: function __getNativeAnimationConfig() {
      var _this$_initialVelocit;
      return {
        type: 'spring',
        overshootClamping: this._overshootClamping,
        restDisplacementThreshold: this._restDisplacementThreshold,
        restSpeedThreshold: this._restSpeedThreshold,
        stiffness: this._stiffness,
        damping: this._damping,
        mass: this._mass,
        initialVelocity: (_this$_initialVelocit = this._initialVelocity) !== null && _this$_initialVelocit !== void 0 ? _this$_initialVelocit : this._lastVelocity,
        toValue: this._toValue,
        iterations: this.__iterations,
        platformConfig: this._platformConfig
      };
    }
  }, {
    key: "start",
    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {
      var _this2 = this;
      this.__active = true;
      this._startPosition = fromValue;
      this._lastPosition = this._startPosition;
      this._onUpdate = onUpdate;
      this.__onEnd = onEnd;
      this._lastTime = Date.now();
      this._frameTime = 0.0;
      if (previousAnimation instanceof SpringAnimation) {
        var internalState = previousAnimation.getInternalState();
        this._lastPosition = internalState.lastPosition;
        this._lastVelocity = internalState.lastVelocity;
        this._initialVelocity = this._lastVelocity;
        this._lastTime = internalState.lastTime;
      }
      var start = function start() {
        if (_this2._useNativeDriver) {
          _this2.__startNativeAnimation(animatedValue);
        } else {
          _this2.onUpdate();
        }
      };
      if (this._delay) {
        this._timeout = setTimeout(start, this._delay);
      } else {
        start();
      }
    }
  }, {
    key: "getInternalState",
    value: function getInternalState() {
      return {
        lastPosition: this._lastPosition,
        lastVelocity: this._lastVelocity,
        lastTime: this._lastTime
      };
    }
  }, {
    key: "onUpdate",
    value: function onUpdate() {
      var MAX_STEPS = 64;
      var now = Date.now();
      if (now > this._lastTime + MAX_STEPS) {
        now = this._lastTime + MAX_STEPS;
      }
      var deltaTime = (now - this._lastTime) / 1000;
      this._frameTime += deltaTime;
      var c = this._damping;
      var m = this._mass;
      var k = this._stiffness;
      var v0 = -this._initialVelocity;
      var zeta = c / (2 * Math.sqrt(k * m));
      var omega0 = Math.sqrt(k / m);
      var omega1 = omega0 * Math.sqrt(1.0 - zeta * zeta);
      var x0 = this._toValue - this._startPosition;
      var position = 0.0;
      var velocity = 0.0;
      var t = this._frameTime;
      if (zeta < 1) {
        var envelope = Math.exp(-zeta * omega0 * t);
        position = this._toValue - envelope * ((v0 + zeta * omega0 * x0) / omega1 * Math.sin(omega1 * t) + x0 * Math.cos(omega1 * t));
        velocity = zeta * omega0 * envelope * (Math.sin(omega1 * t) * (v0 + zeta * omega0 * x0) / omega1 + x0 * Math.cos(omega1 * t)) - envelope * (Math.cos(omega1 * t) * (v0 + zeta * omega0 * x0) - omega1 * x0 * Math.sin(omega1 * t));
      } else {
        var _envelope = Math.exp(-omega0 * t);
        position = this._toValue - _envelope * (x0 + (v0 + omega0 * x0) * t);
        velocity = _envelope * (v0 * (t * omega0 - 1) + t * x0 * (omega0 * omega0));
      }
      this._lastTime = now;
      this._lastPosition = position;
      this._lastVelocity = velocity;
      this._onUpdate(position);
      if (!this.__active) {
        return;
      }
      var isOvershooting = false;
      if (this._overshootClamping && this._stiffness !== 0) {
        if (this._startPosition < this._toValue) {
          isOvershooting = position > this._toValue;
        } else {
          isOvershooting = position < this._toValue;
        }
      }
      var isVelocity = Math.abs(velocity) <= this._restSpeedThreshold;
      var isDisplacement = true;
      if (this._stiffness !== 0) {
        isDisplacement = Math.abs(this._toValue - position) <= this._restDisplacementThreshold;
      }
      if (isOvershooting || isVelocity && isDisplacement) {
        if (this._stiffness !== 0) {
          this._lastPosition = this._toValue;
          this._lastVelocity = 0;
          this._onUpdate(this._toValue);
        }
        this.__debouncedOnEnd({
          finished: true
        });
        return;
      }
      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));
    }
  }, {
    key: "stop",
    value: function stop() {
      _superPropGet(SpringAnimation, "stop", this, 3)([]);
      this.__active = false;
      clearTimeout(this._timeout);
      global.cancelAnimationFrame(this._animationFrame);
      this.__debouncedOnEnd({
        finished: false
      });
    }
  }]);
}(_Animation.default);
var _default = exports.default = SpringAnimation;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0MiIsInJlcXVpcmUiLCJfY2xhc3NDYWxsQ2hlY2syIiwiX2NyZWF0ZUNsYXNzMiIsIl9wb3NzaWJsZUNvbnN0cnVjdG9yUmV0dXJuMiIsIl9nZXRQcm90b3R5cGVPZjIiLCJfZ2V0MiIsIl9pbmhlcml0czIiLCJfY2FsbFN1cGVyIiwidCIsIm8iLCJlIiwiZGVmYXVsdCIsIl9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QiLCJSZWZsZWN0IiwiY29uc3RydWN0IiwiY29uc3RydWN0b3IiLCJhcHBseSIsIkJvb2xlYW4iLCJwcm90b3R5cGUiLCJ2YWx1ZU9mIiwiY2FsbCIsIl9zdXBlclByb3BHZXQiLCJyIiwicCIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJleHBvcnRzIiwiX19lc01vZHVsZSIsIl9BbmltYXRpb24iLCJfU3ByaW5nQ29uZmlnIiwiX2ludmFyaWFudCIsIl9OYXRpdmVBbmltYXRlZEhlbHBlciIsIl9BbmltYXRlZENvbG9yIiwiU3ByaW5nQW5pbWF0aW9uIiwiX0FuaW1hdGlvbiRkZWZhdWx0IiwiY29uZmlnIiwiX3RoaXMiLCJfY29uZmlnJG92ZXJzaG9vdENsYW0iLCJfY29uZmlnJHJlc3REaXNwbGFjZW0iLCJfY29uZmlnJHJlc3RTcGVlZFRocmUiLCJfY29uZmlnJHZlbG9jaXR5IiwiX2NvbmZpZyR2ZWxvY2l0eTIiLCJfY29uZmlnJGRlbGF5IiwiX2NvbmZpZyRpc0ludGVyYWN0aW9uIiwiX2NvbmZpZyRpdGVyYXRpb25zIiwiX292ZXJzaG9vdENsYW1waW5nIiwib3ZlcnNob290Q2xhbXBpbmciLCJfcmVzdERpc3BsYWNlbWVudFRocmVzaG9sZCIsInJlc3REaXNwbGFjZW1lbnRUaHJlc2hvbGQiLCJfcmVzdFNwZWVkVGhyZXNob2xkIiwicmVzdFNwZWVkVGhyZXNob2xkIiwiX2luaXRpYWxWZWxvY2l0eSIsInZlbG9jaXR5IiwiX2xhc3RWZWxvY2l0eSIsIl90b1ZhbHVlIiwidG9WYWx1ZSIsIl9kZWxheSIsImRlbGF5IiwiX3VzZU5hdGl2ZURyaXZlciIsInNob3VsZFVzZU5hdGl2ZURyaXZlciIsIl9wbGF0Zm9ybUNvbmZpZyIsInBsYXRmb3JtQ29uZmlnIiwiX19pc0ludGVyYWN0aW9uIiwiaXNJbnRlcmFjdGlvbiIsIl9faXRlcmF0aW9ucyIsIml0ZXJhdGlvbnMiLCJzdGlmZm5lc3MiLCJ1bmRlZmluZWQiLCJkYW1waW5nIiwibWFzcyIsIl9jb25maWckc3RpZmZuZXNzIiwiX2NvbmZpZyRkYW1waW5nIiwiX2NvbmZpZyRtYXNzIiwiYm91bmNpbmVzcyIsInNwZWVkIiwidGVuc2lvbiIsImZyaWN0aW9uIiwiX3N0aWZmbmVzcyIsIl9kYW1waW5nIiwiX21hc3MiLCJfY29uZmlnJGJvdW5jaW5lc3MiLCJfY29uZmlnJHNwZWVkIiwic3ByaW5nQ29uZmlnIiwiZnJvbUJvdW5jaW5lc3NBbmRTcGVlZCIsIl9jb25maWckdGVuc2lvbiIsIl9jb25maWckZnJpY3Rpb24iLCJfc3ByaW5nQ29uZmlnIiwiZnJvbU9yaWdhbWlUZW5zaW9uQW5kRnJpY3Rpb24iLCJrZXkiLCJ2YWx1ZSIsIl9fZ2V0TmF0aXZlQW5pbWF0aW9uQ29uZmlnIiwiX3RoaXMkX2luaXRpYWxWZWxvY2l0IiwidHlwZSIsImluaXRpYWxWZWxvY2l0eSIsInN0YXJ0IiwiZnJvbVZhbHVlIiwib25VcGRhdGUiLCJvbkVuZCIsInByZXZpb3VzQW5pbWF0aW9uIiwiYW5pbWF0ZWRWYWx1ZSIsIl90aGlzMiIsIl9fYWN0aXZlIiwiX3N0YXJ0UG9zaXRpb24iLCJfbGFzdFBvc2l0aW9uIiwiX29uVXBkYXRlIiwiX19vbkVuZCIsIl9sYXN0VGltZSIsIkRhdGUiLCJub3ciLCJfZnJhbWVUaW1lIiwiaW50ZXJuYWxTdGF0ZSIsImdldEludGVybmFsU3RhdGUiLCJsYXN0UG9zaXRpb24iLCJsYXN0VmVsb2NpdHkiLCJsYXN0VGltZSIsIl9fc3RhcnROYXRpdmVBbmltYXRpb24iLCJfdGltZW91dCIsInNldFRpbWVvdXQiLCJNQVhfU1RFUFMiLCJkZWx0YVRpbWUiLCJjIiwibSIsImsiLCJ2MCIsInpldGEiLCJNYXRoIiwic3FydCIsIm9tZWdhMCIsIm9tZWdhMSIsIngwIiwicG9zaXRpb24iLCJlbnZlbG9wZSIsImV4cCIsInNpbiIsImNvcyIsIl9lbnZlbG9wZSIsImlzT3ZlcnNob290aW5nIiwiaXNWZWxvY2l0eSIsImFicyIsImlzRGlzcGxhY2VtZW50IiwiX19kZWJvdW5jZWRPbkVuZCIsImZpbmlzaGVkIiwiX2FuaW1hdGlvbkZyYW1lIiwicmVxdWVzdEFuaW1hdGlvbkZyYW1lIiwiYmluZCIsInN0b3AiLCJjbGVhclRpbWVvdXQiLCJnbG9iYWwiLCJjYW5jZWxBbmltYXRpb25GcmFtZSIsIl9kZWZhdWx0IiwibW9kdWxlIl0sInNvdXJjZXMiOlsiU3ByaW5nQW5pbWF0aW9uLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBcbiAqIEBmb3JtYXRcbiAqL1xuXG4ndXNlIHN0cmljdCc7XG5cbnZhciBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0ID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0XCIpLmRlZmF1bHQ7XG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwO1xudmFyIF9BbmltYXRpb24gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuL0FuaW1hdGlvblwiKSk7XG52YXIgX1NwcmluZ0NvbmZpZyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uL1NwcmluZ0NvbmZpZ1wiKSk7XG52YXIgX2ludmFyaWFudCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcImZianMvbGliL2ludmFyaWFudFwiKSk7XG52YXIgX05hdGl2ZUFuaW1hdGVkSGVscGVyID0gcmVxdWlyZShcIi4uL05hdGl2ZUFuaW1hdGVkSGVscGVyXCIpO1xudmFyIF9BbmltYXRlZENvbG9yID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vbm9kZXMvQW5pbWF0ZWRDb2xvclwiKSk7XG5jbGFzcyBTcHJpbmdBbmltYXRpb24gZXh0ZW5kcyBfQW5pbWF0aW9uLmRlZmF1bHQge1xuICBjb25zdHJ1Y3Rvcihjb25maWcpIHtcbiAgICB2YXIgX2NvbmZpZyRvdmVyc2hvb3RDbGFtLCBfY29uZmlnJHJlc3REaXNwbGFjZW0sIF9jb25maWckcmVzdFNwZWVkVGhyZSwgX2NvbmZpZyR2ZWxvY2l0eSwgX2NvbmZpZyR2ZWxvY2l0eTIsIF9jb25maWckZGVsYXksIF9jb25maWckaXNJbnRlcmFjdGlvbiwgX2NvbmZpZyRpdGVyYXRpb25zO1xuICAgIHN1cGVyKCk7XG4gICAgdGhpcy5fb3ZlcnNob290Q2xhbXBpbmcgPSAoX2NvbmZpZyRvdmVyc2hvb3RDbGFtID0gY29uZmlnLm92ZXJzaG9vdENsYW1waW5nKSAhPT0gbnVsbCAmJiBfY29uZmlnJG92ZXJzaG9vdENsYW0gIT09IHZvaWQgMCA/IF9jb25maWckb3ZlcnNob290Q2xhbSA6IGZhbHNlO1xuICAgIHRoaXMuX3Jlc3REaXNwbGFjZW1lbnRUaHJlc2hvbGQgPSAoX2NvbmZpZyRyZXN0RGlzcGxhY2VtID0gY29uZmlnLnJlc3REaXNwbGFjZW1lbnRUaHJlc2hvbGQpICE9PSBudWxsICYmIF9jb25maWckcmVzdERpc3BsYWNlbSAhPT0gdm9pZCAwID8gX2NvbmZpZyRyZXN0RGlzcGxhY2VtIDogMC4wMDE7XG4gICAgdGhpcy5fcmVzdFNwZWVkVGhyZXNob2xkID0gKF9jb25maWckcmVzdFNwZWVkVGhyZSA9IGNvbmZpZy5yZXN0U3BlZWRUaHJlc2hvbGQpICE9PSBudWxsICYmIF9jb25maWckcmVzdFNwZWVkVGhyZSAhPT0gdm9pZCAwID8gX2NvbmZpZyRyZXN0U3BlZWRUaHJlIDogMC4wMDE7XG4gICAgdGhpcy5faW5pdGlhbFZlbG9jaXR5ID0gKF9jb25maWckdmVsb2NpdHkgPSBjb25maWcudmVsb2NpdHkpICE9PSBudWxsICYmIF9jb25maWckdmVsb2NpdHkgIT09IHZvaWQgMCA/IF9jb25maWckdmVsb2NpdHkgOiAwO1xuICAgIHRoaXMuX2xhc3RWZWxvY2l0eSA9IChfY29uZmlnJHZlbG9jaXR5MiA9IGNvbmZpZy52ZWxvY2l0eSkgIT09IG51bGwgJiYgX2NvbmZpZyR2ZWxvY2l0eTIgIT09IHZvaWQgMCA/IF9jb25maWckdmVsb2NpdHkyIDogMDtcbiAgICB0aGlzLl90b1ZhbHVlID0gY29uZmlnLnRvVmFsdWU7XG4gICAgdGhpcy5fZGVsYXkgPSAoX2NvbmZpZyRkZWxheSA9IGNvbmZpZy5kZWxheSkgIT09IG51bGwgJiYgX2NvbmZpZyRkZWxheSAhPT0gdm9pZCAwID8gX2NvbmZpZyRkZWxheSA6IDA7XG4gICAgdGhpcy5fdXNlTmF0aXZlRHJpdmVyID0gKDAsIF9OYXRpdmVBbmltYXRlZEhlbHBlci5zaG91bGRVc2VOYXRpdmVEcml2ZXIpKGNvbmZpZyk7XG4gICAgdGhpcy5fcGxhdGZvcm1Db25maWcgPSBjb25maWcucGxhdGZvcm1Db25maWc7XG4gICAgdGhpcy5fX2lzSW50ZXJhY3Rpb24gPSAoX2NvbmZpZyRpc0ludGVyYWN0aW9uID0gY29uZmlnLmlzSW50ZXJhY3Rpb24pICE9PSBudWxsICYmIF9jb25maWckaXNJbnRlcmFjdGlvbiAhPT0gdm9pZCAwID8gX2NvbmZpZyRpc0ludGVyYWN0aW9uIDogIXRoaXMuX3VzZU5hdGl2ZURyaXZlcjtcbiAgICB0aGlzLl9faXRlcmF0aW9ucyA9IChfY29uZmlnJGl0ZXJhdGlvbnMgPSBjb25maWcuaXRlcmF0aW9ucykgIT09IG51bGwgJiYgX2NvbmZpZyRpdGVyYXRpb25zICE9PSB2b2lkIDAgPyBfY29uZmlnJGl0ZXJhdGlvbnMgOiAxO1xuICAgIGlmIChjb25maWcuc3RpZmZuZXNzICE9PSB1bmRlZmluZWQgfHwgY29uZmlnLmRhbXBpbmcgIT09IHVuZGVmaW5lZCB8fCBjb25maWcubWFzcyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgICB2YXIgX2NvbmZpZyRzdGlmZm5lc3MsIF9jb25maWckZGFtcGluZywgX2NvbmZpZyRtYXNzO1xuICAgICAgKDAsIF9pbnZhcmlhbnQuZGVmYXVsdCkoY29uZmlnLmJvdW5jaW5lc3MgPT09IHVuZGVmaW5lZCAmJiBjb25maWcuc3BlZWQgPT09IHVuZGVmaW5lZCAmJiBjb25maWcudGVuc2lvbiA9PT0gdW5kZWZpbmVkICYmIGNvbmZpZy5mcmljdGlvbiA9PT0gdW5kZWZpbmVkLCAnWW91IGNhbiBkZWZpbmUgb25lIG9mIGJvdW5jaW5lc3Mvc3BlZWQsIHRlbnNpb24vZnJpY3Rpb24sIG9yIHN0aWZmbmVzcy9kYW1waW5nL21hc3MsIGJ1dCBub3QgbW9yZSB0aGFuIG9uZScpO1xuICAgICAgdGhpcy5fc3RpZmZuZXNzID0gKF9jb25maWckc3RpZmZuZXNzID0gY29uZmlnLnN0aWZmbmVzcykgIT09IG51bGwgJiYgX2NvbmZpZyRzdGlmZm5lc3MgIT09IHZvaWQgMCA/IF9jb25maWckc3RpZmZuZXNzIDogMTAwO1xuICAgICAgdGhpcy5fZGFtcGluZyA9IChfY29uZmlnJGRhbXBpbmcgPSBjb25maWcuZGFtcGluZykgIT09IG51bGwgJiYgX2NvbmZpZyRkYW1waW5nICE9PSB2b2lkIDAgPyBfY29uZmlnJGRhbXBpbmcgOiAxMDtcbiAgICAgIHRoaXMuX21hc3MgPSAoX2NvbmZpZyRtYXNzID0gY29uZmlnLm1hc3MpICE9PSBudWxsICYmIF9jb25maWckbWFzcyAhPT0gdm9pZCAwID8gX2NvbmZpZyRtYXNzIDogMTtcbiAgICB9IGVsc2UgaWYgKGNvbmZpZy5ib3VuY2luZXNzICE9PSB1bmRlZmluZWQgfHwgY29uZmlnLnNwZWVkICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIHZhciBfY29uZmlnJGJvdW5jaW5lc3MsIF9jb25maWckc3BlZWQ7XG4gICAgICAvLyBDb252ZXJ0IHRoZSBvcmlnYW1pIGJvdW5jaW5lc3Mvc3BlZWQgdmFsdWVzIHRvIHN0aWZmbmVzcy9kYW1waW5nXG4gICAgICAvLyBXZSBhc3N1bWUgbWFzcyBpcyAxLlxuICAgICAgKDAsIF9pbnZhcmlhbnQuZGVmYXVsdCkoY29uZmlnLnRlbnNpb24gPT09IHVuZGVmaW5lZCAmJiBjb25maWcuZnJpY3Rpb24gPT09IHVuZGVmaW5lZCAmJiBjb25maWcuc3RpZmZuZXNzID09PSB1bmRlZmluZWQgJiYgY29uZmlnLmRhbXBpbmcgPT09IHVuZGVmaW5lZCAmJiBjb25maWcubWFzcyA9PT0gdW5kZWZpbmVkLCAnWW91IGNhbiBkZWZpbmUgb25lIG9mIGJvdW5jaW5lc3Mvc3BlZWQsIHRlbnNpb24vZnJpY3Rpb24sIG9yIHN0aWZmbmVzcy9kYW1waW5nL21hc3MsIGJ1dCBub3QgbW9yZSB0aGFuIG9uZScpO1xuICAgICAgdmFyIHNwcmluZ0NvbmZpZyA9IF9TcHJpbmdDb25maWcuZGVmYXVsdC5mcm9tQm91bmNpbmVzc0FuZFNwZWVkKChfY29uZmlnJGJvdW5jaW5lc3MgPSBjb25maWcuYm91bmNpbmVzcykgIT09IG51bGwgJiYgX2NvbmZpZyRib3VuY2luZXNzICE9PSB2b2lkIDAgPyBfY29uZmlnJGJvdW5jaW5lc3MgOiA4LCAoX2NvbmZpZyRzcGVlZCA9IGNvbmZpZy5zcGVlZCkgIT09IG51bGwgJiYgX2NvbmZpZyRzcGVlZCAhPT0gdm9pZCAwID8gX2NvbmZpZyRzcGVlZCA6IDEyKTtcbiAgICAgIHRoaXMuX3N0aWZmbmVzcyA9IHNwcmluZ0NvbmZpZy5zdGlmZm5lc3M7XG4gICAgICB0aGlzLl9kYW1waW5nID0gc3ByaW5nQ29uZmlnLmRhbXBpbmc7XG4gICAgICB0aGlzLl9tYXNzID0gMTtcbiAgICB9IGVsc2Uge1xuICAgICAgdmFyIF9jb25maWckdGVuc2lvbiwgX2NvbmZpZyRmcmljdGlvbjtcbiAgICAgIC8vIENvbnZlcnQgdGhlIG9yaWdhbWkgdGVuc2lvbi9mcmljdGlvbiB2YWx1ZXMgdG8gc3RpZmZuZXNzL2RhbXBpbmdcbiAgICAgIC8vIFdlIGFzc3VtZSBtYXNzIGlzIDEuXG4gICAgICB2YXIgX3NwcmluZ0NvbmZpZyA9IF9TcHJpbmdDb25maWcuZGVmYXVsdC5mcm9tT3JpZ2FtaVRlbnNpb25BbmRGcmljdGlvbigoX2NvbmZpZyR0ZW5zaW9uID0gY29uZmlnLnRlbnNpb24pICE9PSBudWxsICYmIF9jb25maWckdGVuc2lvbiAhPT0gdm9pZCAwID8gX2NvbmZpZyR0ZW5zaW9uIDogNDAsIChfY29uZmlnJGZyaWN0aW9uID0gY29uZmlnLmZyaWN0aW9uKSAhPT0gbnVsbCAmJiBfY29uZmlnJGZyaWN0aW9uICE9PSB2b2lkIDAgPyBfY29uZmlnJGZyaWN0aW9uIDogNyk7XG4gICAgICB0aGlzLl9zdGlmZm5lc3MgPSBfc3ByaW5nQ29uZmlnLnN0aWZmbmVzcztcbiAgICAgIHRoaXMuX2RhbXBpbmcgPSBfc3ByaW5nQ29uZmlnLmRhbXBpbmc7XG4gICAgICB0aGlzLl9tYXNzID0gMTtcbiAgICB9XG4gICAgKDAsIF9pbnZhcmlhbnQuZGVmYXVsdCkodGhpcy5fc3RpZmZuZXNzID4gMCwgJ1N0aWZmbmVzcyB2YWx1ZSBtdXN0IGJlIGdyZWF0ZXIgdGhhbiAwJyk7XG4gICAgKDAsIF9pbnZhcmlhbnQuZGVmYXVsdCkodGhpcy5fZGFtcGluZyA+IDAsICdEYW1waW5nIHZhbHVlIG11c3QgYmUgZ3JlYXRlciB0aGFuIDAnKTtcbiAgICAoMCwgX2ludmFyaWFudC5kZWZhdWx0KSh0aGlzLl9tYXNzID4gMCwgJ01hc3MgdmFsdWUgbXVzdCBiZSBncmVhdGVyIHRoYW4gMCcpO1xuICB9XG4gIF9fZ2V0TmF0aXZlQW5pbWF0aW9uQ29uZmlnKCkge1xuICAgIHZhciBfdGhpcyRfaW5pdGlhbFZlbG9jaXQ7XG4gICAgcmV0dXJuIHtcbiAgICAgIHR5cGU6ICdzcHJpbmcnLFxuICAgICAgb3ZlcnNob290Q2xhbXBpbmc6IHRoaXMuX292ZXJzaG9vdENsYW1waW5nLFxuICAgICAgcmVzdERpc3BsYWNlbWVudFRocmVzaG9sZDogdGhpcy5fcmVzdERpc3BsYWNlbWVudFRocmVzaG9sZCxcbiAgICAgIHJlc3RTcGVlZFRocmVzaG9sZDogdGhpcy5fcmVzdFNwZWVkVGhyZXNob2xkLFxuICAgICAgc3RpZmZuZXNzOiB0aGlzLl9zdGlmZm5lc3MsXG4gICAgICBkYW1waW5nOiB0aGlzLl9kYW1waW5nLFxuICAgICAgbWFzczogdGhpcy5fbWFzcyxcbiAgICAgIGluaXRpYWxWZWxvY2l0eTogKF90aGlzJF9pbml0aWFsVmVsb2NpdCA9IHRoaXMuX2luaXRpYWxWZWxvY2l0eSkgIT09IG51bGwgJiYgX3RoaXMkX2luaXRpYWxWZWxvY2l0ICE9PSB2b2lkIDAgPyBfdGhpcyRfaW5pdGlhbFZlbG9jaXQgOiB0aGlzLl9sYXN0VmVsb2NpdHksXG4gICAgICB0b1ZhbHVlOiB0aGlzLl90b1ZhbHVlLFxuICAgICAgaXRlcmF0aW9uczogdGhpcy5fX2l0ZXJhdGlvbnMsXG4gICAgICBwbGF0Zm9ybUNvbmZpZzogdGhpcy5fcGxhdGZvcm1Db25maWdcbiAgICB9O1xuICB9XG4gIHN0YXJ0KGZyb21WYWx1ZSwgb25VcGRhdGUsIG9uRW5kLCBwcmV2aW91c0FuaW1hdGlvbiwgYW5pbWF0ZWRWYWx1ZSkge1xuICAgIHRoaXMuX19hY3RpdmUgPSB0cnVlO1xuICAgIHRoaXMuX3N0YXJ0UG9zaXRpb24gPSBmcm9tVmFsdWU7XG4gICAgdGhpcy5fbGFzdFBvc2l0aW9uID0gdGhpcy5fc3RhcnRQb3NpdGlvbjtcbiAgICB0aGlzLl9vblVwZGF0ZSA9IG9uVXBkYXRlO1xuICAgIHRoaXMuX19vbkVuZCA9IG9uRW5kO1xuICAgIHRoaXMuX2xhc3RUaW1lID0gRGF0ZS5ub3coKTtcbiAgICB0aGlzLl9mcmFtZVRpbWUgPSAwLjA7XG4gICAgaWYgKHByZXZpb3VzQW5pbWF0aW9uIGluc3RhbmNlb2YgU3ByaW5nQW5pbWF0aW9uKSB7XG4gICAgICB2YXIgaW50ZXJuYWxTdGF0ZSA9IHByZXZpb3VzQW5pbWF0aW9uLmdldEludGVybmFsU3RhdGUoKTtcbiAgICAgIHRoaXMuX2xhc3RQb3NpdGlvbiA9IGludGVybmFsU3RhdGUubGFzdFBvc2l0aW9uO1xuICAgICAgdGhpcy5fbGFzdFZlbG9jaXR5ID0gaW50ZXJuYWxTdGF0ZS5sYXN0VmVsb2NpdHk7XG4gICAgICAvLyBTZXQgdGhlIGluaXRpYWwgdmVsb2NpdHkgdG8gdGhlIGxhc3QgdmVsb2NpdHlcbiAgICAgIHRoaXMuX2luaXRpYWxWZWxvY2l0eSA9IHRoaXMuX2xhc3RWZWxvY2l0eTtcbiAgICAgIHRoaXMuX2xhc3RUaW1lID0gaW50ZXJuYWxTdGF0ZS5sYXN0VGltZTtcbiAgICB9XG4gICAgdmFyIHN0YXJ0ID0gKCkgPT4ge1xuICAgICAgaWYgKHRoaXMuX3VzZU5hdGl2ZURyaXZlcikge1xuICAgICAgICB0aGlzLl9fc3RhcnROYXRpdmVBbmltYXRpb24oYW5pbWF0ZWRWYWx1ZSk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aGlzLm9uVXBkYXRlKCk7XG4gICAgICB9XG4gICAgfTtcblxuICAgIC8vICBJZiB0aGlzLl9kZWxheSBpcyBtb3JlIHRoYW4gMCwgd2Ugc3RhcnQgYWZ0ZXIgdGhlIHRpbWVvdXQuXG4gICAgaWYgKHRoaXMuX2RlbGF5KSB7XG4gICAgICB0aGlzLl90aW1lb3V0ID0gc2V0VGltZW91dChzdGFydCwgdGhpcy5fZGVsYXkpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzdGFydCgpO1xuICAgIH1cbiAgfVxuICBnZXRJbnRlcm5hbFN0YXRlKCkge1xuICAgIHJldHVybiB7XG4gICAgICBsYXN0UG9zaXRpb246IHRoaXMuX2xhc3RQb3NpdGlvbixcbiAgICAgIGxhc3RWZWxvY2l0eTogdGhpcy5fbGFzdFZlbG9jaXR5LFxuICAgICAgbGFzdFRpbWU6IHRoaXMuX2xhc3RUaW1lXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBUaGlzIHNwcmluZyBtb2RlbCBpcyBiYXNlZCBvZmYgb2YgYSBkYW1wZWQgaGFybW9uaWMgb3NjaWxsYXRvclxuICAgKiAoaHR0cHM6Ly9lbi53aWtpcGVkaWEub3JnL3dpa2kvSGFybW9uaWNfb3NjaWxsYXRvciNEYW1wZWRfaGFybW9uaWNfb3NjaWxsYXRvcikuXG4gICAqXG4gICAqIFdlIHVzZSB0aGUgY2xvc2VkIGZvcm0gb2YgdGhlIHNlY29uZCBvcmRlciBkaWZmZXJlbnRpYWwgZXF1YXRpb246XG4gICAqXG4gICAqIHgnJyArICgyzrbijbVfMCl4JyArIOKNtV4yeCA9IDBcbiAgICpcbiAgICogd2hlcmVcbiAgICogICAg4o21XzAgPSDiiJooayAvIG0pICh1bmRhbXBlZCBhbmd1bGFyIGZyZXF1ZW5jeSBvZiB0aGUgb3NjaWxsYXRvciksXG4gICAqICAgIM62ID0gYyAvIDLiiJptayAoZGFtcGluZyByYXRpbyksXG4gICAqICAgIGMgPSBkYW1waW5nIGNvbnN0YW50XG4gICAqICAgIGsgPSBzdGlmZm5lc3NcbiAgICogICAgbSA9IG1hc3NcbiAgICpcbiAgICogVGhlIGRlcml2YXRpb24gb2YgdGhlIGNsb3NlZCBmb3JtIGlzIGRlc2NyaWJlZCBpbiBkZXRhaWwgaGVyZTpcbiAgICogaHR0cDovL3BsYW5ldG1hdGgub3JnL3NpdGVzL2RlZmF1bHQvZmlsZXMvdGV4cGRmLzM5NzQ1LnBkZlxuICAgKlxuICAgKiBUaGlzIGFsZ29yaXRobSBoYXBwZW5zIHRvIG1hdGNoIHRoZSBhbGdvcml0aG0gdXNlZCBieSBDQVNwcmluZ0FuaW1hdGlvbixcbiAgICogYSBRdWFydHpDb3JlIChpT1MpIEFQSSB0aGF0IGNyZWF0ZXMgc3ByaW5nIGFuaW1hdGlvbnMuXG4gICAqL1xuICBvblVwZGF0ZSgpIHtcbiAgICAvLyBJZiBmb3Igc29tZSByZWFzb24gd2UgbG9zdCBhIGxvdCBvZiBmcmFtZXMgKGUuZy4gcHJvY2VzcyBsYXJnZSBwYXlsb2FkIG9yXG4gICAgLy8gc3RvcHBlZCBpbiB0aGUgZGVidWdnZXIpLCB3ZSBvbmx5IGFkdmFuY2UgYnkgNCBmcmFtZXMgd29ydGggb2ZcbiAgICAvLyBjb21wdXRhdGlvbiBhbmQgd2lsbCBjb250aW51ZSBvbiB0aGUgbmV4dCBmcmFtZS4gSXQncyBiZXR0ZXIgdG8gaGF2ZSBpdFxuICAgIC8vIHJ1bm5pbmcgYXQgZmFzdGVyIHNwZWVkIHRoYW4ganVtcGluZyB0byB0aGUgZW5kLlxuICAgIHZhciBNQVhfU1RFUFMgPSA2NDtcbiAgICB2YXIgbm93ID0gRGF0ZS5ub3coKTtcbiAgICBpZiAobm93ID4gdGhpcy5fbGFzdFRpbWUgKyBNQVhfU1RFUFMpIHtcbiAgICAgIG5vdyA9IHRoaXMuX2xhc3RUaW1lICsgTUFYX1NURVBTO1xuICAgIH1cbiAgICB2YXIgZGVsdGFUaW1lID0gKG5vdyAtIHRoaXMuX2xhc3RUaW1lKSAvIDEwMDA7XG4gICAgdGhpcy5fZnJhbWVUaW1lICs9IGRlbHRhVGltZTtcbiAgICB2YXIgYyA9IHRoaXMuX2RhbXBpbmc7XG4gICAgdmFyIG0gPSB0aGlzLl9tYXNzO1xuICAgIHZhciBrID0gdGhpcy5fc3RpZmZuZXNzO1xuICAgIHZhciB2MCA9IC10aGlzLl9pbml0aWFsVmVsb2NpdHk7XG4gICAgdmFyIHpldGEgPSBjIC8gKDIgKiBNYXRoLnNxcnQoayAqIG0pKTsgLy8gZGFtcGluZyByYXRpb1xuICAgIHZhciBvbWVnYTAgPSBNYXRoLnNxcnQoayAvIG0pOyAvLyB1bmRhbXBlZCBhbmd1bGFyIGZyZXF1ZW5jeSBvZiB0aGUgb3NjaWxsYXRvciAocmFkL21zKVxuICAgIHZhciBvbWVnYTEgPSBvbWVnYTAgKiBNYXRoLnNxcnQoMS4wIC0gemV0YSAqIHpldGEpOyAvLyBleHBvbmVudGlhbCBkZWNheVxuICAgIHZhciB4MCA9IHRoaXMuX3RvVmFsdWUgLSB0aGlzLl9zdGFydFBvc2l0aW9uOyAvLyBjYWxjdWxhdGUgdGhlIG9zY2lsbGF0aW9uIGZyb20geDAgPSAxIHRvIHggPSAwXG5cbiAgICB2YXIgcG9zaXRpb24gPSAwLjA7XG4gICAgdmFyIHZlbG9jaXR5ID0gMC4wO1xuICAgIHZhciB0ID0gdGhpcy5fZnJhbWVUaW1lO1xuICAgIGlmICh6ZXRhIDwgMSkge1xuICAgICAgLy8gVW5kZXIgZGFtcGVkXG4gICAgICB2YXIgZW52ZWxvcGUgPSBNYXRoLmV4cCgtemV0YSAqIG9tZWdhMCAqIHQpO1xuICAgICAgcG9zaXRpb24gPSB0aGlzLl90b1ZhbHVlIC0gZW52ZWxvcGUgKiAoKHYwICsgemV0YSAqIG9tZWdhMCAqIHgwKSAvIG9tZWdhMSAqIE1hdGguc2luKG9tZWdhMSAqIHQpICsgeDAgKiBNYXRoLmNvcyhvbWVnYTEgKiB0KSk7XG4gICAgICAvLyBUaGlzIGxvb2tzIGNyYXp5IC0tIGl0J3MgYWN0dWFsbHkganVzdCB0aGUgZGVyaXZhdGl2ZSBvZiB0aGVcbiAgICAgIC8vIG9zY2lsbGF0aW9uIGZ1bmN0aW9uXG4gICAgICB2ZWxvY2l0eSA9IHpldGEgKiBvbWVnYTAgKiBlbnZlbG9wZSAqIChNYXRoLnNpbihvbWVnYTEgKiB0KSAqICh2MCArIHpldGEgKiBvbWVnYTAgKiB4MCkgLyBvbWVnYTEgKyB4MCAqIE1hdGguY29zKG9tZWdhMSAqIHQpKSAtIGVudmVsb3BlICogKE1hdGguY29zKG9tZWdhMSAqIHQpICogKHYwICsgemV0YSAqIG9tZWdhMCAqIHgwKSAtIG9tZWdhMSAqIHgwICogTWF0aC5zaW4ob21lZ2ExICogdCkpO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBDcml0aWNhbGx5IGRhbXBlZFxuICAgICAgdmFyIF9lbnZlbG9wZSA9IE1hdGguZXhwKC1vbWVnYTAgKiB0KTtcbiAgICAgIHBvc2l0aW9uID0gdGhpcy5fdG9WYWx1ZSAtIF9lbnZlbG9wZSAqICh4MCArICh2MCArIG9tZWdhMCAqIHgwKSAqIHQpO1xuICAgICAgdmVsb2NpdHkgPSBfZW52ZWxvcGUgKiAodjAgKiAodCAqIG9tZWdhMCAtIDEpICsgdCAqIHgwICogKG9tZWdhMCAqIG9tZWdhMCkpO1xuICAgIH1cbiAgICB0aGlzLl9sYXN0VGltZSA9IG5vdztcbiAgICB0aGlzLl9sYXN0UG9zaXRpb24gPSBwb3NpdGlvbjtcbiAgICB0aGlzLl9sYXN0VmVsb2NpdHkgPSB2ZWxvY2l0eTtcbiAgICB0aGlzLl9vblVwZGF0ZShwb3NpdGlvbik7XG4gICAgaWYgKCF0aGlzLl9fYWN0aXZlKSB7XG4gICAgICAvLyBhIGxpc3RlbmVyIG1pZ2h0IGhhdmUgc3RvcHBlZCB1cyBpbiBfb25VcGRhdGVcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBDb25kaXRpb25zIGZvciBzdG9wcGluZyB0aGUgc3ByaW5nIGFuaW1hdGlvblxuICAgIHZhciBpc092ZXJzaG9vdGluZyA9IGZhbHNlO1xuICAgIGlmICh0aGlzLl9vdmVyc2hvb3RDbGFtcGluZyAmJiB0aGlzLl9zdGlmZm5lc3MgIT09IDApIHtcbiAgICAgIGlmICh0aGlzLl9zdGFydFBvc2l0aW9uIDwgdGhpcy5fdG9WYWx1ZSkge1xuICAgICAgICBpc092ZXJzaG9vdGluZyA9IHBvc2l0aW9uID4gdGhpcy5fdG9WYWx1ZTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGlzT3ZlcnNob290aW5nID0gcG9zaXRpb24gPCB0aGlzLl90b1ZhbHVlO1xuICAgICAgfVxuICAgIH1cbiAgICB2YXIgaXNWZWxvY2l0eSA9IE1hdGguYWJzKHZlbG9jaXR5KSA8PSB0aGlzLl9yZXN0U3BlZWRUaHJlc2hvbGQ7XG4gICAgdmFyIGlzRGlzcGxhY2VtZW50ID0gdHJ1ZTtcbiAgICBpZiAodGhpcy5fc3RpZmZuZXNzICE9PSAwKSB7XG4gICAgICBpc0Rpc3BsYWNlbWVudCA9IE1hdGguYWJzKHRoaXMuX3RvVmFsdWUgLSBwb3NpdGlvbikgPD0gdGhpcy5fcmVzdERpc3BsYWNlbWVudFRocmVzaG9sZDtcbiAgICB9XG4gICAgaWYgKGlzT3ZlcnNob290aW5nIHx8IGlzVmVsb2NpdHkgJiYgaXNEaXNwbGFjZW1lbnQpIHtcbiAgICAgIGlmICh0aGlzLl9zdGlmZm5lc3MgIT09IDApIHtcbiAgICAgICAgLy8gRW5zdXJlIHRoYXQgd2UgZW5kIHVwIHdpdGggYSByb3VuZCB2YWx1ZVxuICAgICAgICB0aGlzLl9sYXN0UG9zaXRpb24gPSB0aGlzLl90b1ZhbHVlO1xuICAgICAgICB0aGlzLl9sYXN0VmVsb2NpdHkgPSAwO1xuICAgICAgICB0aGlzLl9vblVwZGF0ZSh0aGlzLl90b1ZhbHVlKTtcbiAgICAgIH1cbiAgICAgIHRoaXMuX19kZWJvdW5jZWRPbkVuZCh7XG4gICAgICAgIGZpbmlzaGVkOiB0cnVlXG4gICAgICB9KTtcbiAgICAgIHJldHVybjtcbiAgICB9XG4gICAgLy8gJEZsb3dGaXhNZVttZXRob2QtdW5iaW5kaW5nXSBhZGRlZCB3aGVuIGltcHJvdmluZyB0eXBpbmcgZm9yIHRoaXMgcGFyYW1ldGVyc1xuICAgIHRoaXMuX2FuaW1hdGlvbkZyYW1lID0gcmVxdWVzdEFuaW1hdGlvbkZyYW1lKHRoaXMub25VcGRhdGUuYmluZCh0aGlzKSk7XG4gIH1cbiAgc3RvcCgpIHtcbiAgICBzdXBlci5zdG9wKCk7XG4gICAgdGhpcy5fX2FjdGl2ZSA9IGZhbHNlO1xuICAgIGNsZWFyVGltZW91dCh0aGlzLl90aW1lb3V0KTtcbiAgICBnbG9iYWwuY2FuY2VsQW5pbWF0aW9uRnJhbWUodGhpcy5fYW5pbWF0aW9uRnJhbWUpO1xuICAgIHRoaXMuX19kZWJvdW5jZWRPbkVuZCh7XG4gICAgICBmaW5pc2hlZDogZmFsc2VcbiAgICB9KTtcbiAgfVxufVxudmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gU3ByaW5nQW5pbWF0aW9uO1xubW9kdWxlLmV4cG9ydHMgPSBleHBvcnRzLmRlZmF1bHQ7Il0sIm1hcHBpbmdzIjoiQUFVQSxZQUFZOztBQUFDLElBQUFBLHVCQUFBLEdBQUFDLE9BQUE7QUFBQSxJQUFBQyxnQkFBQSxHQUFBRix1QkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQUUsYUFBQSxHQUFBSCx1QkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQUcsMkJBQUEsR0FBQUosdUJBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFJLGdCQUFBLEdBQUFMLHVCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBSyxLQUFBLEdBQUFOLHVCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBTSxVQUFBLEdBQUFQLHVCQUFBLENBQUFDLE9BQUE7QUFBQSxTQUFBTyxXQUFBQyxDQUFBLEVBQUFDLENBQUEsRUFBQUMsQ0FBQSxXQUFBRCxDQUFBLE9BQUFMLGdCQUFBLENBQUFPLE9BQUEsRUFBQUYsQ0FBQSxPQUFBTiwyQkFBQSxDQUFBUSxPQUFBLEVBQUFILENBQUEsRUFBQUkseUJBQUEsS0FBQUMsT0FBQSxDQUFBQyxTQUFBLENBQUFMLENBQUEsRUFBQUMsQ0FBQSxZQUFBTixnQkFBQSxDQUFBTyxPQUFBLEVBQUFILENBQUEsRUFBQU8sV0FBQSxJQUFBTixDQUFBLENBQUFPLEtBQUEsQ0FBQVIsQ0FBQSxFQUFBRSxDQUFBO0FBQUEsU0FBQUUsMEJBQUEsY0FBQUosQ0FBQSxJQUFBUyxPQUFBLENBQUFDLFNBQUEsQ0FBQUMsT0FBQSxDQUFBQyxJQUFBLENBQUFQLE9BQUEsQ0FBQUMsU0FBQSxDQUFBRyxPQUFBLGlDQUFBVCxDQUFBLGFBQUFJLHlCQUFBLFlBQUFBLDBCQUFBLGFBQUFKLENBQUE7QUFBQSxTQUFBYSxjQUFBYixDQUFBLEVBQUFDLENBQUEsRUFBQUMsQ0FBQSxFQUFBWSxDQUFBLFFBQUFDLENBQUEsT0FBQWxCLEtBQUEsQ0FBQU0sT0FBQSxNQUFBUCxnQkFBQSxDQUFBTyxPQUFBLE1BQUFXLENBQUEsR0FBQWQsQ0FBQSxDQUFBVSxTQUFBLEdBQUFWLENBQUEsR0FBQUMsQ0FBQSxFQUFBQyxDQUFBLGNBQUFZLENBQUEseUJBQUFDLENBQUEsYUFBQWYsQ0FBQSxXQUFBZSxDQUFBLENBQUFQLEtBQUEsQ0FBQU4sQ0FBQSxFQUFBRixDQUFBLE9BQUFlLENBQUE7QUFFYixJQUFJQyxzQkFBc0IsR0FBR3hCLE9BQU8sQ0FBQyw4Q0FBOEMsQ0FBQyxDQUFDVyxPQUFPO0FBQzVGYyxPQUFPLENBQUNDLFVBQVUsR0FBRyxJQUFJO0FBQ3pCRCxPQUFPLENBQUNkLE9BQU8sR0FBRyxLQUFLLENBQUM7QUFDeEIsSUFBSWdCLFVBQVUsR0FBR0gsc0JBQXNCLENBQUN4QixPQUFPLGNBQWMsQ0FBQyxDQUFDO0FBQy9ELElBQUk0QixhQUFhLEdBQUdKLHNCQUFzQixDQUFDeEIsT0FBTyxrQkFBa0IsQ0FBQyxDQUFDO0FBQ3RFLElBQUk2QixVQUFVLEdBQUdMLHNCQUFzQixDQUFDeEIsT0FBTyxDQUFDLG9CQUFvQixDQUFDLENBQUM7QUFDdEUsSUFBSThCLHFCQUFxQixHQUFHOUIsT0FBTywwQkFBMEIsQ0FBQztBQUM5RCxJQUFJK0IsY0FBYyxHQUFHUCxzQkFBc0IsQ0FBQ3hCLE9BQU8seUJBQXlCLENBQUMsQ0FBQztBQUFDLElBQ3pFZ0MsZUFBZSxhQUFBQyxrQkFBQTtFQUNuQixTQUFBRCxnQkFBWUUsTUFBTSxFQUFFO0lBQUEsSUFBQUMsS0FBQTtJQUFBLElBQUFsQyxnQkFBQSxDQUFBVSxPQUFBLFFBQUFxQixlQUFBO0lBQ2xCLElBQUlJLHFCQUFxQixFQUFFQyxxQkFBcUIsRUFBRUMscUJBQXFCLEVBQUVDLGdCQUFnQixFQUFFQyxpQkFBaUIsRUFBRUMsYUFBYSxFQUFFQyxxQkFBcUIsRUFBRUMsa0JBQWtCO0lBQ3RLUixLQUFBLEdBQUE1QixVQUFBLE9BQUF5QixlQUFBO0lBQ0FHLEtBQUEsQ0FBS1Msa0JBQWtCLEdBQUcsQ0FBQ1IscUJBQXFCLEdBQUdGLE1BQU0sQ0FBQ1csaUJBQWlCLE1BQU0sSUFBSSxJQUFJVCxxQkFBcUIsS0FBSyxLQUFLLENBQUMsR0FBR0EscUJBQXFCLEdBQUcsS0FBSztJQUN6SkQsS0FBQSxDQUFLVywwQkFBMEIsR0FBRyxDQUFDVCxxQkFBcUIsR0FBR0gsTUFBTSxDQUFDYSx5QkFBeUIsTUFBTSxJQUFJLElBQUlWLHFCQUFxQixLQUFLLEtBQUssQ0FBQyxHQUFHQSxxQkFBcUIsR0FBRyxLQUFLO0lBQ3pLRixLQUFBLENBQUthLG1CQUFtQixHQUFHLENBQUNWLHFCQUFxQixHQUFHSixNQUFNLENBQUNlLGtCQUFrQixNQUFNLElBQUksSUFBSVgscUJBQXFCLEtBQUssS0FBSyxDQUFDLEdBQUdBLHFCQUFxQixHQUFHLEtBQUs7SUFDM0pILEtBQUEsQ0FBS2UsZ0JBQWdCLEdBQUcsQ0FBQ1gsZ0JBQWdCLEdBQUdMLE1BQU0sQ0FBQ2lCLFFBQVEsTUFBTSxJQUFJLElBQUlaLGdCQUFnQixLQUFLLEtBQUssQ0FBQyxHQUFHQSxnQkFBZ0IsR0FBRyxDQUFDO0lBQzNISixLQUFBLENBQUtpQixhQUFhLEdBQUcsQ0FBQ1osaUJBQWlCLEdBQUdOLE1BQU0sQ0FBQ2lCLFFBQVEsTUFBTSxJQUFJLElBQUlYLGlCQUFpQixLQUFLLEtBQUssQ0FBQyxHQUFHQSxpQkFBaUIsR0FBRyxDQUFDO0lBQzNITCxLQUFBLENBQUtrQixRQUFRLEdBQUduQixNQUFNLENBQUNvQixPQUFPO0lBQzlCbkIsS0FBQSxDQUFLb0IsTUFBTSxHQUFHLENBQUNkLGFBQWEsR0FBR1AsTUFBTSxDQUFDc0IsS0FBSyxNQUFNLElBQUksSUFBSWYsYUFBYSxLQUFLLEtBQUssQ0FBQyxHQUFHQSxhQUFhLEdBQUcsQ0FBQztJQUNyR04sS0FBQSxDQUFLc0IsZ0JBQWdCLEdBQUcsQ0FBQyxDQUFDLEVBQUUzQixxQkFBcUIsQ0FBQzRCLHFCQUFxQixFQUFFeEIsTUFBTSxDQUFDO0lBQ2hGQyxLQUFBLENBQUt3QixlQUFlLEdBQUd6QixNQUFNLENBQUMwQixjQUFjO0lBQzVDekIsS0FBQSxDQUFLMEIsZUFBZSxHQUFHLENBQUNuQixxQkFBcUIsR0FBR1IsTUFBTSxDQUFDNEIsYUFBYSxNQUFNLElBQUksSUFBSXBCLHFCQUFxQixLQUFLLEtBQUssQ0FBQyxHQUFHQSxxQkFBcUIsR0FBRyxDQUFDUCxLQUFBLENBQUtzQixnQkFBZ0I7SUFDbkt0QixLQUFBLENBQUs0QixZQUFZLEdBQUcsQ0FBQ3BCLGtCQUFrQixHQUFHVCxNQUFNLENBQUM4QixVQUFVLE1BQU0sSUFBSSxJQUFJckIsa0JBQWtCLEtBQUssS0FBSyxDQUFDLEdBQUdBLGtCQUFrQixHQUFHLENBQUM7SUFDL0gsSUFBSVQsTUFBTSxDQUFDK0IsU0FBUyxLQUFLQyxTQUFTLElBQUloQyxNQUFNLENBQUNpQyxPQUFPLEtBQUtELFNBQVMsSUFBSWhDLE1BQU0sQ0FBQ2tDLElBQUksS0FBS0YsU0FBUyxFQUFFO01BQy9GLElBQUlHLGlCQUFpQixFQUFFQyxlQUFlLEVBQUVDLFlBQVk7TUFDcEQsQ0FBQyxDQUFDLEVBQUUxQyxVQUFVLENBQUNsQixPQUFPLEVBQUV1QixNQUFNLENBQUNzQyxVQUFVLEtBQUtOLFNBQVMsSUFBSWhDLE1BQU0sQ0FBQ3VDLEtBQUssS0FBS1AsU0FBUyxJQUFJaEMsTUFBTSxDQUFDd0MsT0FBTyxLQUFLUixTQUFTLElBQUloQyxNQUFNLENBQUN5QyxRQUFRLEtBQUtULFNBQVMsRUFBRSw0R0FBNEcsQ0FBQztNQUNyUS9CLEtBQUEsQ0FBS3lDLFVBQVUsR0FBRyxDQUFDUCxpQkFBaUIsR0FBR25DLE1BQU0sQ0FBQytCLFNBQVMsTUFBTSxJQUFJLElBQUlJLGlCQUFpQixLQUFLLEtBQUssQ0FBQyxHQUFHQSxpQkFBaUIsR0FBRyxHQUFHO01BQzNIbEMsS0FBQSxDQUFLMEMsUUFBUSxHQUFHLENBQUNQLGVBQWUsR0FBR3BDLE1BQU0sQ0FBQ2lDLE9BQU8sTUFBTSxJQUFJLElBQUlHLGVBQWUsS0FBSyxLQUFLLENBQUMsR0FBR0EsZUFBZSxHQUFHLEVBQUU7TUFDaEhuQyxLQUFBLENBQUsyQyxLQUFLLEdBQUcsQ0FBQ1AsWUFBWSxHQUFHckMsTUFBTSxDQUFDa0MsSUFBSSxNQUFNLElBQUksSUFBSUcsWUFBWSxLQUFLLEtBQUssQ0FBQyxHQUFHQSxZQUFZLEdBQUcsQ0FBQztJQUNsRyxDQUFDLE1BQU0sSUFBSXJDLE1BQU0sQ0FBQ3NDLFVBQVUsS0FBS04sU0FBUyxJQUFJaEMsTUFBTSxDQUFDdUMsS0FBSyxLQUFLUCxTQUFTLEVBQUU7TUFDeEUsSUFBSWEsa0JBQWtCLEVBQUVDLGFBQWE7TUFHckMsQ0FBQyxDQUFDLEVBQUVuRCxVQUFVLENBQUNsQixPQUFPLEVBQUV1QixNQUFNLENBQUN3QyxPQUFPLEtBQUtSLFNBQVMsSUFBSWhDLE1BQU0sQ0FBQ3lDLFFBQVEsS0FBS1QsU0FBUyxJQUFJaEMsTUFBTSxDQUFDK0IsU0FBUyxLQUFLQyxTQUFTLElBQUloQyxNQUFNLENBQUNpQyxPQUFPLEtBQUtELFNBQVMsSUFBSWhDLE1BQU0sQ0FBQ2tDLElBQUksS0FBS0YsU0FBUyxFQUFFLDRHQUE0RyxDQUFDO01BQ25TLElBQUllLFlBQVksR0FBR3JELGFBQWEsQ0FBQ2pCLE9BQU8sQ0FBQ3VFLHNCQUFzQixDQUFDLENBQUNILGtCQUFrQixHQUFHN0MsTUFBTSxDQUFDc0MsVUFBVSxNQUFNLElBQUksSUFBSU8sa0JBQWtCLEtBQUssS0FBSyxDQUFDLEdBQUdBLGtCQUFrQixHQUFHLENBQUMsRUFBRSxDQUFDQyxhQUFhLEdBQUc5QyxNQUFNLENBQUN1QyxLQUFLLE1BQU0sSUFBSSxJQUFJTyxhQUFhLEtBQUssS0FBSyxDQUFDLEdBQUdBLGFBQWEsR0FBRyxFQUFFLENBQUM7TUFDdFE3QyxLQUFBLENBQUt5QyxVQUFVLEdBQUdLLFlBQVksQ0FBQ2hCLFNBQVM7TUFDeEM5QixLQUFBLENBQUswQyxRQUFRLEdBQUdJLFlBQVksQ0FBQ2QsT0FBTztNQUNwQ2hDLEtBQUEsQ0FBSzJDLEtBQUssR0FBRyxDQUFDO0lBQ2hCLENBQUMsTUFBTTtNQUNMLElBQUlLLGVBQWUsRUFBRUMsZ0JBQWdCO01BR3JDLElBQUlDLGFBQWEsR0FBR3pELGFBQWEsQ0FBQ2pCLE9BQU8sQ0FBQzJFLDZCQUE2QixDQUFDLENBQUNILGVBQWUsR0FBR2pELE1BQU0sQ0FBQ3dDLE9BQU8sTUFBTSxJQUFJLElBQUlTLGVBQWUsS0FBSyxLQUFLLENBQUMsR0FBR0EsZUFBZSxHQUFHLEVBQUUsRUFBRSxDQUFDQyxnQkFBZ0IsR0FBR2xELE1BQU0sQ0FBQ3lDLFFBQVEsTUFBTSxJQUFJLElBQUlTLGdCQUFnQixLQUFLLEtBQUssQ0FBQyxHQUFHQSxnQkFBZ0IsR0FBRyxDQUFDLENBQUM7TUFDOVFqRCxLQUFBLENBQUt5QyxVQUFVLEdBQUdTLGFBQWEsQ0FBQ3BCLFNBQVM7TUFDekM5QixLQUFBLENBQUswQyxRQUFRLEdBQUdRLGFBQWEsQ0FBQ2xCLE9BQU87TUFDckNoQyxLQUFBLENBQUsyQyxLQUFLLEdBQUcsQ0FBQztJQUNoQjtJQUNBLENBQUMsQ0FBQyxFQUFFakQsVUFBVSxDQUFDbEIsT0FBTyxFQUFFd0IsS0FBQSxDQUFLeUMsVUFBVSxHQUFHLENBQUMsRUFBRSx3Q0FBd0MsQ0FBQztJQUN0RixDQUFDLENBQUMsRUFBRS9DLFVBQVUsQ0FBQ2xCLE9BQU8sRUFBRXdCLEtBQUEsQ0FBSzBDLFFBQVEsR0FBRyxDQUFDLEVBQUUsc0NBQXNDLENBQUM7SUFDbEYsQ0FBQyxDQUFDLEVBQUVoRCxVQUFVLENBQUNsQixPQUFPLEVBQUV3QixLQUFBLENBQUsyQyxLQUFLLEdBQUcsQ0FBQyxFQUFFLG1DQUFtQyxDQUFDO0lBQUMsT0FBQTNDLEtBQUE7RUFDL0U7RUFBQyxJQUFBN0IsVUFBQSxDQUFBSyxPQUFBLEVBQUFxQixlQUFBLEVBQUFDLGtCQUFBO0VBQUEsV0FBQS9CLGFBQUEsQ0FBQVMsT0FBQSxFQUFBcUIsZUFBQTtJQUFBdUQsR0FBQTtJQUFBQyxLQUFBLEVBQ0QsU0FBQUMsMEJBQTBCQSxDQUFBLEVBQUc7TUFDM0IsSUFBSUMscUJBQXFCO01BQ3pCLE9BQU87UUFDTEMsSUFBSSxFQUFFLFFBQVE7UUFDZDlDLGlCQUFpQixFQUFFLElBQUksQ0FBQ0Qsa0JBQWtCO1FBQzFDRyx5QkFBeUIsRUFBRSxJQUFJLENBQUNELDBCQUEwQjtRQUMxREcsa0JBQWtCLEVBQUUsSUFBSSxDQUFDRCxtQkFBbUI7UUFDNUNpQixTQUFTLEVBQUUsSUFBSSxDQUFDVyxVQUFVO1FBQzFCVCxPQUFPLEVBQUUsSUFBSSxDQUFDVSxRQUFRO1FBQ3RCVCxJQUFJLEVBQUUsSUFBSSxDQUFDVSxLQUFLO1FBQ2hCYyxlQUFlLEVBQUUsQ0FBQ0YscUJBQXFCLEdBQUcsSUFBSSxDQUFDeEMsZ0JBQWdCLE1BQU0sSUFBSSxJQUFJd0MscUJBQXFCLEtBQUssS0FBSyxDQUFDLEdBQUdBLHFCQUFxQixHQUFHLElBQUksQ0FBQ3RDLGFBQWE7UUFDMUpFLE9BQU8sRUFBRSxJQUFJLENBQUNELFFBQVE7UUFDdEJXLFVBQVUsRUFBRSxJQUFJLENBQUNELFlBQVk7UUFDN0JILGNBQWMsRUFBRSxJQUFJLENBQUNEO01BQ3ZCLENBQUM7SUFDSDtFQUFDO0lBQUE0QixHQUFBO0lBQUFDLEtBQUEsRUFDRCxTQUFBSyxLQUFLQSxDQUFDQyxTQUFTLEVBQUVDLFFBQVEsRUFBRUMsS0FBSyxFQUFFQyxpQkFBaUIsRUFBRUMsYUFBYSxFQUFFO01BQUEsSUFBQUMsTUFBQTtNQUNsRSxJQUFJLENBQUNDLFFBQVEsR0FBRyxJQUFJO01BQ3BCLElBQUksQ0FBQ0MsY0FBYyxHQUFHUCxTQUFTO01BQy9CLElBQUksQ0FBQ1EsYUFBYSxHQUFHLElBQUksQ0FBQ0QsY0FBYztNQUN4QyxJQUFJLENBQUNFLFNBQVMsR0FBR1IsUUFBUTtNQUN6QixJQUFJLENBQUNTLE9BQU8sR0FBR1IsS0FBSztNQUNwQixJQUFJLENBQUNTLFNBQVMsR0FBR0MsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztNQUMzQixJQUFJLENBQUNDLFVBQVUsR0FBRyxHQUFHO01BQ3JCLElBQUlYLGlCQUFpQixZQUFZakUsZUFBZSxFQUFFO1FBQ2hELElBQUk2RSxhQUFhLEdBQUdaLGlCQUFpQixDQUFDYSxnQkFBZ0IsQ0FBQyxDQUFDO1FBQ3hELElBQUksQ0FBQ1IsYUFBYSxHQUFHTyxhQUFhLENBQUNFLFlBQVk7UUFDL0MsSUFBSSxDQUFDM0QsYUFBYSxHQUFHeUQsYUFBYSxDQUFDRyxZQUFZO1FBRS9DLElBQUksQ0FBQzlELGdCQUFnQixHQUFHLElBQUksQ0FBQ0UsYUFBYTtRQUMxQyxJQUFJLENBQUNxRCxTQUFTLEdBQUdJLGFBQWEsQ0FBQ0ksUUFBUTtNQUN6QztNQUNBLElBQUlwQixLQUFLLEdBQUcsU0FBUkEsS0FBS0EsQ0FBQSxFQUFTO1FBQ2hCLElBQUlNLE1BQUksQ0FBQzFDLGdCQUFnQixFQUFFO1VBQ3pCMEMsTUFBSSxDQUFDZSxzQkFBc0IsQ0FBQ2hCLGFBQWEsQ0FBQztRQUM1QyxDQUFDLE1BQU07VUFDTEMsTUFBSSxDQUFDSixRQUFRLENBQUMsQ0FBQztRQUNqQjtNQUNGLENBQUM7TUFHRCxJQUFJLElBQUksQ0FBQ3hDLE1BQU0sRUFBRTtRQUNmLElBQUksQ0FBQzRELFFBQVEsR0FBR0MsVUFBVSxDQUFDdkIsS0FBSyxFQUFFLElBQUksQ0FBQ3RDLE1BQU0sQ0FBQztNQUNoRCxDQUFDLE1BQU07UUFDTHNDLEtBQUssQ0FBQyxDQUFDO01BQ1Q7SUFDRjtFQUFDO0lBQUFOLEdBQUE7SUFBQUMsS0FBQSxFQUNELFNBQUFzQixnQkFBZ0JBLENBQUEsRUFBRztNQUNqQixPQUFPO1FBQ0xDLFlBQVksRUFBRSxJQUFJLENBQUNULGFBQWE7UUFDaENVLFlBQVksRUFBRSxJQUFJLENBQUM1RCxhQUFhO1FBQ2hDNkQsUUFBUSxFQUFFLElBQUksQ0FBQ1I7TUFDakIsQ0FBQztJQUNIO0VBQUM7SUFBQWxCLEdBQUE7SUFBQUMsS0FBQSxFQXVCRCxTQUFBTyxRQUFRQSxDQUFBLEVBQUc7TUFLVCxJQUFJc0IsU0FBUyxHQUFHLEVBQUU7TUFDbEIsSUFBSVYsR0FBRyxHQUFHRCxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDO01BQ3BCLElBQUlBLEdBQUcsR0FBRyxJQUFJLENBQUNGLFNBQVMsR0FBR1ksU0FBUyxFQUFFO1FBQ3BDVixHQUFHLEdBQUcsSUFBSSxDQUFDRixTQUFTLEdBQUdZLFNBQVM7TUFDbEM7TUFDQSxJQUFJQyxTQUFTLEdBQUcsQ0FBQ1gsR0FBRyxHQUFHLElBQUksQ0FBQ0YsU0FBUyxJQUFJLElBQUk7TUFDN0MsSUFBSSxDQUFDRyxVQUFVLElBQUlVLFNBQVM7TUFDNUIsSUFBSUMsQ0FBQyxHQUFHLElBQUksQ0FBQzFDLFFBQVE7TUFDckIsSUFBSTJDLENBQUMsR0FBRyxJQUFJLENBQUMxQyxLQUFLO01BQ2xCLElBQUkyQyxDQUFDLEdBQUcsSUFBSSxDQUFDN0MsVUFBVTtNQUN2QixJQUFJOEMsRUFBRSxHQUFHLENBQUMsSUFBSSxDQUFDeEUsZ0JBQWdCO01BQy9CLElBQUl5RSxJQUFJLEdBQUdKLENBQUMsSUFBSSxDQUFDLEdBQUdLLElBQUksQ0FBQ0MsSUFBSSxDQUFDSixDQUFDLEdBQUdELENBQUMsQ0FBQyxDQUFDO01BQ3JDLElBQUlNLE1BQU0sR0FBR0YsSUFBSSxDQUFDQyxJQUFJLENBQUNKLENBQUMsR0FBR0QsQ0FBQyxDQUFDO01BQzdCLElBQUlPLE1BQU0sR0FBR0QsTUFBTSxHQUFHRixJQUFJLENBQUNDLElBQUksQ0FBQyxHQUFHLEdBQUdGLElBQUksR0FBR0EsSUFBSSxDQUFDO01BQ2xELElBQUlLLEVBQUUsR0FBRyxJQUFJLENBQUMzRSxRQUFRLEdBQUcsSUFBSSxDQUFDZ0QsY0FBYztNQUU1QyxJQUFJNEIsUUFBUSxHQUFHLEdBQUc7TUFDbEIsSUFBSTlFLFFBQVEsR0FBRyxHQUFHO01BQ2xCLElBQUkzQyxDQUFDLEdBQUcsSUFBSSxDQUFDb0csVUFBVTtNQUN2QixJQUFJZSxJQUFJLEdBQUcsQ0FBQyxFQUFFO1FBRVosSUFBSU8sUUFBUSxHQUFHTixJQUFJLENBQUNPLEdBQUcsQ0FBQyxDQUFDUixJQUFJLEdBQUdHLE1BQU0sR0FBR3RILENBQUMsQ0FBQztRQUMzQ3lILFFBQVEsR0FBRyxJQUFJLENBQUM1RSxRQUFRLEdBQUc2RSxRQUFRLElBQUksQ0FBQ1IsRUFBRSxHQUFHQyxJQUFJLEdBQUdHLE1BQU0sR0FBR0UsRUFBRSxJQUFJRCxNQUFNLEdBQUdILElBQUksQ0FBQ1EsR0FBRyxDQUFDTCxNQUFNLEdBQUd2SCxDQUFDLENBQUMsR0FBR3dILEVBQUUsR0FBR0osSUFBSSxDQUFDUyxHQUFHLENBQUNOLE1BQU0sR0FBR3ZILENBQUMsQ0FBQyxDQUFDO1FBRzdIMkMsUUFBUSxHQUFHd0UsSUFBSSxHQUFHRyxNQUFNLEdBQUdJLFFBQVEsSUFBSU4sSUFBSSxDQUFDUSxHQUFHLENBQUNMLE1BQU0sR0FBR3ZILENBQUMsQ0FBQyxJQUFJa0gsRUFBRSxHQUFHQyxJQUFJLEdBQUdHLE1BQU0sR0FBR0UsRUFBRSxDQUFDLEdBQUdELE1BQU0sR0FBR0MsRUFBRSxHQUFHSixJQUFJLENBQUNTLEdBQUcsQ0FBQ04sTUFBTSxHQUFHdkgsQ0FBQyxDQUFDLENBQUMsR0FBRzBILFFBQVEsSUFBSU4sSUFBSSxDQUFDUyxHQUFHLENBQUNOLE1BQU0sR0FBR3ZILENBQUMsQ0FBQyxJQUFJa0gsRUFBRSxHQUFHQyxJQUFJLEdBQUdHLE1BQU0sR0FBR0UsRUFBRSxDQUFDLEdBQUdELE1BQU0sR0FBR0MsRUFBRSxHQUFHSixJQUFJLENBQUNRLEdBQUcsQ0FBQ0wsTUFBTSxHQUFHdkgsQ0FBQyxDQUFDLENBQUM7TUFDcE8sQ0FBQyxNQUFNO1FBRUwsSUFBSThILFNBQVMsR0FBR1YsSUFBSSxDQUFDTyxHQUFHLENBQUMsQ0FBQ0wsTUFBTSxHQUFHdEgsQ0FBQyxDQUFDO1FBQ3JDeUgsUUFBUSxHQUFHLElBQUksQ0FBQzVFLFFBQVEsR0FBR2lGLFNBQVMsSUFBSU4sRUFBRSxHQUFHLENBQUNOLEVBQUUsR0FBR0ksTUFBTSxHQUFHRSxFQUFFLElBQUl4SCxDQUFDLENBQUM7UUFDcEUyQyxRQUFRLEdBQUdtRixTQUFTLElBQUlaLEVBQUUsSUFBSWxILENBQUMsR0FBR3NILE1BQU0sR0FBRyxDQUFDLENBQUMsR0FBR3RILENBQUMsR0FBR3dILEVBQUUsSUFBSUYsTUFBTSxHQUFHQSxNQUFNLENBQUMsQ0FBQztNQUM3RTtNQUNBLElBQUksQ0FBQ3JCLFNBQVMsR0FBR0UsR0FBRztNQUNwQixJQUFJLENBQUNMLGFBQWEsR0FBRzJCLFFBQVE7TUFDN0IsSUFBSSxDQUFDN0UsYUFBYSxHQUFHRCxRQUFRO01BQzdCLElBQUksQ0FBQ29ELFNBQVMsQ0FBQzBCLFFBQVEsQ0FBQztNQUN4QixJQUFJLENBQUMsSUFBSSxDQUFDN0IsUUFBUSxFQUFFO1FBRWxCO01BQ0Y7TUFHQSxJQUFJbUMsY0FBYyxHQUFHLEtBQUs7TUFDMUIsSUFBSSxJQUFJLENBQUMzRixrQkFBa0IsSUFBSSxJQUFJLENBQUNnQyxVQUFVLEtBQUssQ0FBQyxFQUFFO1FBQ3BELElBQUksSUFBSSxDQUFDeUIsY0FBYyxHQUFHLElBQUksQ0FBQ2hELFFBQVEsRUFBRTtVQUN2Q2tGLGNBQWMsR0FBR04sUUFBUSxHQUFHLElBQUksQ0FBQzVFLFFBQVE7UUFDM0MsQ0FBQyxNQUFNO1VBQ0xrRixjQUFjLEdBQUdOLFFBQVEsR0FBRyxJQUFJLENBQUM1RSxRQUFRO1FBQzNDO01BQ0Y7TUFDQSxJQUFJbUYsVUFBVSxHQUFHWixJQUFJLENBQUNhLEdBQUcsQ0FBQ3RGLFFBQVEsQ0FBQyxJQUFJLElBQUksQ0FBQ0gsbUJBQW1CO01BQy9ELElBQUkwRixjQUFjLEdBQUcsSUFBSTtNQUN6QixJQUFJLElBQUksQ0FBQzlELFVBQVUsS0FBSyxDQUFDLEVBQUU7UUFDekI4RCxjQUFjLEdBQUdkLElBQUksQ0FBQ2EsR0FBRyxDQUFDLElBQUksQ0FBQ3BGLFFBQVEsR0FBRzRFLFFBQVEsQ0FBQyxJQUFJLElBQUksQ0FBQ25GLDBCQUEwQjtNQUN4RjtNQUNBLElBQUl5RixjQUFjLElBQUlDLFVBQVUsSUFBSUUsY0FBYyxFQUFFO1FBQ2xELElBQUksSUFBSSxDQUFDOUQsVUFBVSxLQUFLLENBQUMsRUFBRTtVQUV6QixJQUFJLENBQUMwQixhQUFhLEdBQUcsSUFBSSxDQUFDakQsUUFBUTtVQUNsQyxJQUFJLENBQUNELGFBQWEsR0FBRyxDQUFDO1VBQ3RCLElBQUksQ0FBQ21ELFNBQVMsQ0FBQyxJQUFJLENBQUNsRCxRQUFRLENBQUM7UUFDL0I7UUFDQSxJQUFJLENBQUNzRixnQkFBZ0IsQ0FBQztVQUNwQkMsUUFBUSxFQUFFO1FBQ1osQ0FBQyxDQUFDO1FBQ0Y7TUFDRjtNQUVBLElBQUksQ0FBQ0MsZUFBZSxHQUFHQyxxQkFBcUIsQ0FBQyxJQUFJLENBQUMvQyxRQUFRLENBQUNnRCxJQUFJLENBQUMsSUFBSSxDQUFDLENBQUM7SUFDeEU7RUFBQztJQUFBeEQsR0FBQTtJQUFBQyxLQUFBLEVBQ0QsU0FBQXdELElBQUlBLENBQUEsRUFBRztNQUNMM0gsYUFBQSxDQUFBVyxlQUFBO01BQ0EsSUFBSSxDQUFDb0UsUUFBUSxHQUFHLEtBQUs7TUFDckI2QyxZQUFZLENBQUMsSUFBSSxDQUFDOUIsUUFBUSxDQUFDO01BQzNCK0IsTUFBTSxDQUFDQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUNOLGVBQWUsQ0FBQztNQUNqRCxJQUFJLENBQUNGLGdCQUFnQixDQUFDO1FBQ3BCQyxRQUFRLEVBQUU7TUFDWixDQUFDLENBQUM7SUFDSjtFQUFDO0FBQUEsRUExTTJCakgsVUFBVSxDQUFDaEIsT0FBTztBQTRNaEQsSUFBSXlJLFFBQVEsR0FBRzNILE9BQU8sQ0FBQ2QsT0FBTyxHQUFHcUIsZUFBZTtBQUNoRHFILE1BQU0sQ0FBQzVILE9BQU8sR0FBR0EsT0FBTyxDQUFDZCxPQUFPIiwiaWdub3JlTGlzdCI6W119