{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_StyleSheet", "_View", "_excluded", "ProgressBar", "forwardRef", "props", "ref", "_props$color", "color", "_props$indeterminate", "indeterminate", "_props$progress", "progress", "_props$trackColor", "trackColor", "style", "other", "percentageProgress", "width", "createElement", "role", "styles", "track", "backgroundColor", "animation", "displayName", "create", "forcedColorAdjust", "height", "overflow", "userSelect", "zIndex", "animationDuration", "animationKeyframes", "transform", "animationTimingFunction", "animationIterationCount", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _excluded = [\"color\", \"indeterminate\", \"progress\", \"trackColor\", \"style\"];\nvar ProgressBar = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _props$color = props.color,\n    color = _props$color === void 0 ? '#1976D2' : _props$color,\n    _props$indeterminate = props.indeterminate,\n    indeterminate = _props$indeterminate === void 0 ? false : _props$indeterminate,\n    _props$progress = props.progress,\n    progress = _props$progress === void 0 ? 0 : _props$progress,\n    _props$trackColor = props.trackColor,\n    trackColor = _props$trackColor === void 0 ? 'transparent' : _props$trackColor,\n    style = props.style,\n    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  var percentageProgress = progress * 100;\n  var width = indeterminate ? '25%' : percentageProgress + \"%\";\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, other, {\n    \"aria-valuemax\": 100,\n    \"aria-valuemin\": 0,\n    \"aria-valuenow\": indeterminate ? null : percentageProgress,\n    ref: ref,\n    role: \"progressbar\",\n    style: [styles.track, style, {\n      backgroundColor: trackColor\n    }]\n  }), /*#__PURE__*/React.createElement(_View.default, {\n    style: [{\n      backgroundColor: color,\n      width\n    }, styles.progress, indeterminate && styles.animation]\n  }));\n});\nProgressBar.displayName = 'ProgressBar';\nvar styles = _StyleSheet.default.create({\n  track: {\n    forcedColorAdjust: 'none',\n    height: 5,\n    overflow: 'hidden',\n    userSelect: 'none',\n    zIndex: 0\n  },\n  progress: {\n    forcedColorAdjust: 'none',\n    height: '100%',\n    zIndex: -1\n  },\n  animation: {\n    animationDuration: '1s',\n    animationKeyframes: [{\n      '0%': {\n        transform: 'translateX(-100%)'\n      },\n      '100%': {\n        transform: 'translateX(400%)'\n      }\n    }],\n    animationTimingFunction: 'linear',\n    animationIterationCount: 'infinite'\n  }\n});\nvar _default = exports.default = ProgressBar;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAUZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,KAAK,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIS,KAAK,GAAGV,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIU,SAAS,GAAG,CAAC,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,YAAY,EAAE,OAAO,CAAC;AAC7E,IAAIC,WAAW,GAAgBJ,KAAK,CAACK,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG,EAAK;EAC9D,IAAIC,YAAY,GAAGF,KAAK,CAACG,KAAK;IAC5BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAG,SAAS,GAAGA,YAAY;IAC1DE,oBAAoB,GAAGJ,KAAK,CAACK,aAAa;IAC1CA,aAAa,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,oBAAoB;IAC9EE,eAAe,GAAGN,KAAK,CAACO,QAAQ;IAChCA,QAAQ,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,eAAe;IAC3DE,iBAAiB,GAAGR,KAAK,CAACS,UAAU;IACpCA,UAAU,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAG,aAAa,GAAGA,iBAAiB;IAC7EE,KAAK,GAAGV,KAAK,CAACU,KAAK;IACnBC,KAAK,GAAG,CAAC,CAAC,EAAElB,8BAA8B,CAACL,OAAO,EAAEY,KAAK,EAAEH,SAAS,CAAC;EACvE,IAAIe,kBAAkB,GAAGL,QAAQ,GAAG,GAAG;EACvC,IAAIM,KAAK,GAAGR,aAAa,GAAG,KAAK,GAAGO,kBAAkB,GAAG,GAAG;EAC5D,OAAoBlB,KAAK,CAACoB,aAAa,CAAClB,KAAK,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACvF,eAAe,EAAE,GAAG;IACpB,eAAe,EAAE,CAAC;IAClB,eAAe,EAAEN,aAAa,GAAG,IAAI,GAAGO,kBAAkB;IAC1DX,GAAG,EAAEA,GAAG;IACRc,IAAI,EAAE,aAAa;IACnBL,KAAK,EAAE,CAACM,MAAM,CAACC,KAAK,EAAEP,KAAK,EAAE;MAC3BQ,eAAe,EAAET;IACnB,CAAC;EACH,CAAC,CAAC,EAAef,KAAK,CAACoB,aAAa,CAAClB,KAAK,CAACR,OAAO,EAAE;IAClDsB,KAAK,EAAE,CAAC;MACNQ,eAAe,EAAEf,KAAK;MACtBU,KAAK,EAALA;IACF,CAAC,EAAEG,MAAM,CAACT,QAAQ,EAAEF,aAAa,IAAIW,MAAM,CAACG,SAAS;EACvD,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFrB,WAAW,CAACsB,WAAW,GAAG,aAAa;AACvC,IAAIJ,MAAM,GAAGrB,WAAW,CAACP,OAAO,CAACiC,MAAM,CAAC;EACtCJ,KAAK,EAAE;IACLK,iBAAiB,EAAE,MAAM;IACzBC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,MAAM;IAClBC,MAAM,EAAE;EACV,CAAC;EACDnB,QAAQ,EAAE;IACRe,iBAAiB,EAAE,MAAM;IACzBC,MAAM,EAAE,MAAM;IACdG,MAAM,EAAE,CAAC;EACX,CAAC;EACDP,SAAS,EAAE;IACTQ,iBAAiB,EAAE,IAAI;IACvBC,kBAAkB,EAAE,CAAC;MACnB,IAAI,EAAE;QACJC,SAAS,EAAE;MACb,CAAC;MACD,MAAM,EAAE;QACNA,SAAS,EAAE;MACb;IACF,CAAC,CAAC;IACFC,uBAAuB,EAAE,QAAQ;IACjCC,uBAAuB,EAAE;EAC3B;AACF,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAG1C,OAAO,CAACF,OAAO,GAAGU,WAAW;AAC5CmC,MAAM,CAAC3C,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}