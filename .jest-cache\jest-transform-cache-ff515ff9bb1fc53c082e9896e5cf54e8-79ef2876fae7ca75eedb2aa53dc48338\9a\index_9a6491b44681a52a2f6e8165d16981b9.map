{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "_react", "React", "_Image", "_StyleSheet", "_View", "_excluded", "emptyObject", "ImageBackground", "forwardRef", "props", "forwardedRef", "children", "_props$style", "style", "imageStyle", "imageRef", "rest", "_StyleSheet$flatten", "flatten", "height", "width", "createElement", "ref", "zIndex", "absoluteFill", "displayName", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _Image = _interopRequireDefault(require(\"../Image\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _excluded = [\"children\", \"style\", \"imageStyle\", \"imageRef\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nvar emptyObject = {};\n\n/**\n * Very simple drop-in replacement for <Image> which supports nesting views.\n */\nvar ImageBackground = /*#__PURE__*/(0, _react.forwardRef)((props, forwardedRef) => {\n  var children = props.children,\n    _props$style = props.style,\n    style = _props$style === void 0 ? emptyObject : _props$style,\n    imageStyle = props.imageStyle,\n    imageRef = props.imageRef,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  var _StyleSheet$flatten = _StyleSheet.default.flatten(style),\n    height = _StyleSheet$flatten.height,\n    width = _StyleSheet$flatten.width;\n  return /*#__PURE__*/React.createElement(_View.default, {\n    ref: forwardedRef,\n    style: style\n  }, /*#__PURE__*/React.createElement(_Image.default, (0, _extends2.default)({}, rest, {\n    ref: imageRef,\n    style: [{\n      // Temporary Workaround:\n      // Current (imperfect yet) implementation of <Image> overwrites width and height styles\n      // (which is not quite correct), and these styles conflict with explicitly set styles\n      // of <ImageBackground> and with our internal layout model here.\n      // So, we have to proxy/reapply these styles explicitly for actual <Image> component.\n      // This workaround should be removed after implementing proper support of\n      // intrinsic content size of the <Image>.\n      width,\n      height,\n      zIndex: -1\n    }, _StyleSheet.default.absoluteFill, imageStyle]\n  })), children);\n});\nImageBackground.displayName = 'ImageBackground';\nvar _default = exports.default = ImageBackground;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,MAAM,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,IAAIQ,KAAK,GAAGD,MAAM;AAClB,IAAIE,MAAM,GAAGV,sBAAsB,CAACC,OAAO,WAAW,CAAC,CAAC;AACxD,IAAIU,WAAW,GAAGX,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIW,KAAK,GAAGZ,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIY,SAAS,GAAG,CAAC,UAAU,EAAE,OAAO,EAAE,YAAY,EAAE,UAAU,CAAC;AAS/D,IAAIC,WAAW,GAAG,CAAC,CAAC;AAKpB,IAAIC,eAAe,GAAgB,CAAC,CAAC,EAAEP,MAAM,CAACQ,UAAU,EAAE,UAACC,KAAK,EAAEC,YAAY,EAAK;EACjF,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,YAAY,GAAGH,KAAK,CAACI,KAAK;IAC1BA,KAAK,GAAGD,YAAY,KAAK,KAAK,CAAC,GAAGN,WAAW,GAAGM,YAAY;IAC5DE,UAAU,GAAGL,KAAK,CAACK,UAAU;IAC7BC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,IAAI,GAAG,CAAC,CAAC,EAAEjB,8BAA8B,CAACL,OAAO,EAAEe,KAAK,EAAEJ,SAAS,CAAC;EACtE,IAAIY,mBAAmB,GAAGd,WAAW,CAACT,OAAO,CAACwB,OAAO,CAACL,KAAK,CAAC;IAC1DM,MAAM,GAAGF,mBAAmB,CAACE,MAAM;IACnCC,KAAK,GAAGH,mBAAmB,CAACG,KAAK;EACnC,OAAoBnB,KAAK,CAACoB,aAAa,CAACjB,KAAK,CAACV,OAAO,EAAE;IACrD4B,GAAG,EAAEZ,YAAY;IACjBG,KAAK,EAAEA;EACT,CAAC,EAAeZ,KAAK,CAACoB,aAAa,CAACnB,MAAM,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEsB,IAAI,EAAE;IACnFM,GAAG,EAAEP,QAAQ;IACbF,KAAK,EAAE,CAAC;MAQNO,KAAK,EAALA,KAAK;MACLD,MAAM,EAANA,MAAM;MACNI,MAAM,EAAE,CAAC;IACX,CAAC,EAAEpB,WAAW,CAACT,OAAO,CAAC8B,YAAY,EAAEV,UAAU;EACjD,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAAC;AAChB,CAAC,CAAC;AACFJ,eAAe,CAACkB,WAAW,GAAG,iBAAiB;AAC/C,IAAIC,QAAQ,GAAG9B,OAAO,CAACF,OAAO,GAAGa,eAAe;AAChDoB,MAAM,CAAC/B,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}