{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "normalizeValueWithProperty", "_unitlessNumbers", "_normalizeColor", "colorProps", "backgroundColor", "borderColor", "borderTopColor", "borderRightColor", "borderBottomColor", "borderLeftColor", "color", "shadowColor", "textDecorationColor", "textShadowColor", "value", "property", "returnValue", "module"], "sources": ["normalizeValueWithProperty.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = normalizeValueWithProperty;\nvar _unitlessNumbers = _interopRequireDefault(require(\"./unitlessNumbers\"));\nvar _normalizeColor = _interopRequireDefault(require(\"./normalizeColor\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar colorProps = {\n  backgroundColor: true,\n  borderColor: true,\n  borderTopColor: true,\n  borderRightColor: true,\n  borderBottomColor: true,\n  borderLeftColor: true,\n  color: true,\n  shadowColor: true,\n  textDecorationColor: true,\n  textShadowColor: true\n};\nfunction normalizeValueWithProperty(value, property) {\n  var returnValue = value;\n  if ((property == null || !_unitlessNumbers.default[property]) && typeof value === 'number') {\n    returnValue = value + \"px\";\n  } else if (property != null && colorProps[property]) {\n    returnValue = (0, _normalizeColor.default)(value);\n  }\n  return returnValue;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,0BAA0B;AAC5C,IAAIC,gBAAgB,GAAGN,sBAAsB,CAACC,OAAO,oBAAoB,CAAC,CAAC;AAC3E,IAAIM,eAAe,GAAGP,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AAUzE,IAAIO,UAAU,GAAG;EACfC,eAAe,EAAE,IAAI;EACrBC,WAAW,EAAE,IAAI;EACjBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,iBAAiB,EAAE,IAAI;EACvBC,eAAe,EAAE,IAAI;EACrBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAE,IAAI;EACjBC,mBAAmB,EAAE,IAAI;EACzBC,eAAe,EAAE;AACnB,CAAC;AACD,SAASb,0BAA0BA,CAACc,KAAK,EAAEC,QAAQ,EAAE;EACnD,IAAIC,WAAW,GAAGF,KAAK;EACvB,IAAI,CAACC,QAAQ,IAAI,IAAI,IAAI,CAACd,gBAAgB,CAACJ,OAAO,CAACkB,QAAQ,CAAC,KAAK,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC1FE,WAAW,GAAGF,KAAK,GAAG,IAAI;EAC5B,CAAC,MAAM,IAAIC,QAAQ,IAAI,IAAI,IAAIZ,UAAU,CAACY,QAAQ,CAAC,EAAE;IACnDC,WAAW,GAAG,CAAC,CAAC,EAAEd,eAAe,CAACL,OAAO,EAAEiB,KAAK,CAAC;EACnD;EACA,OAAOE,WAAW;AACpB;AACAC,MAAM,CAACnB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}