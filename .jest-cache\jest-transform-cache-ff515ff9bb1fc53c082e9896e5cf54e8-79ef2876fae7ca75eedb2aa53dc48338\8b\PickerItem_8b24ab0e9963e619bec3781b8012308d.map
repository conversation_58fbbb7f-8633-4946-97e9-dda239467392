{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "PickerItem", "_createElement", "props", "color", "label", "testID", "value", "style", "children", "module"], "sources": ["PickerItem.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = PickerItem;\nvar _createElement = _interopRequireDefault(require(\"../createElement\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction PickerItem(props) {\n  var color = props.color,\n    label = props.label,\n    testID = props.testID,\n    value = props.value;\n  var style = {\n    color\n  };\n  return (0, _createElement.default)('option', {\n    children: label,\n    style,\n    testID,\n    value\n  });\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,UAAU;AAC5B,IAAIC,cAAc,GAAGN,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AAWxE,SAASI,UAAUA,CAACE,KAAK,EAAE;EACzB,IAAIC,KAAK,GAAGD,KAAK,CAACC,KAAK;IACrBC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,KAAK,GAAGJ,KAAK,CAACI,KAAK;EACrB,IAAIC,KAAK,GAAG;IACVJ,KAAK,EAALA;EACF,CAAC;EACD,OAAO,CAAC,CAAC,EAAEF,cAAc,CAACJ,OAAO,EAAE,QAAQ,EAAE;IAC3CW,QAAQ,EAAEJ,KAAK;IACfG,KAAK,EAALA,KAAK;IACLF,MAAM,EAANA,MAAM;IACNC,KAAK,EAALA;EACF,CAAC,CAAC;AACJ;AACAG,MAAM,CAACX,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}