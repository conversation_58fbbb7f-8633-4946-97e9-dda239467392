4e0857b39ea87208ef1811047432b6e9
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _createElement = _interopRequireDefault(require("../createElement"));
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _usePlatformMethods = _interopRequireDefault(require("../../modules/usePlatformMethods"));
var _PickerItem = _interopRequireDefault(require("./PickerItem"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _excluded = ["children", "enabled", "onValueChange", "selectedValue", "style", "testID", "itemStyle", "mode", "prompt"];
var Picker = React.forwardRef(function (props, forwardedRef) {
  var children = props.children,
    enabled = props.enabled,
    onValueChange = props.onValueChange,
    selectedValue = props.selectedValue,
    style = props.style,
    testID = props.testID,
    itemStyle = props.itemStyle,
    mode = props.mode,
    prompt = props.prompt,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var hostRef = React.useRef(null);
  function handleChange(e) {
    var _e$target = e.target,
      selectedIndex = _e$target.selectedIndex,
      value = _e$target.value;
    if (onValueChange) {
      onValueChange(value, selectedIndex);
    }
  }
  var supportedProps = (0, _objectSpread2.default)({
    children: children,
    disabled: enabled === false ? true : undefined,
    onChange: handleChange,
    style: [styles.initial, style],
    testID: testID,
    value: selectedValue
  }, other);
  var platformMethodsRef = (0, _usePlatformMethods.default)(supportedProps);
  var setRef = (0, _useMergeRefs.default)(hostRef, platformMethodsRef, forwardedRef);
  supportedProps.ref = setRef;
  return (0, _createElement.default)('select', supportedProps);
});
Picker.Item = _PickerItem.default;
var styles = _StyleSheet.default.create({
  initial: {
    fontFamily: 'System',
    fontSize: 'inherit',
    margin: 0
  }
});
var _default = exports.default = Picker;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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