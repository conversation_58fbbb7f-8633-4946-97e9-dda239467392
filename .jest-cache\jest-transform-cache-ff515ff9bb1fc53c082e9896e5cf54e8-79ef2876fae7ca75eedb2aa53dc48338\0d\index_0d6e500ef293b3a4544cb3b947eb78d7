255f9c206d54073ccfded535851ff7b2
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _dismissKeyboard = _interopRequireDefault(require("../../modules/dismissKeyboard"));
var Keyboard = {
  isVisible: function isVisible() {
    return false;
  },
  addListener: function addListener() {
    return {
      remove: function remove() {}
    };
  },
  dismiss: function dismiss() {
    (0, _dismissKeyboard.default)();
  },
  removeAllListeners: function removeAllListeners() {},
  removeListener: function removeListener() {}
};
var _default = exports.default = Keyboard;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJleHBvcnRzIiwiX19lc01vZHVsZSIsIl9kaXNtaXNzS2V5Ym9hcmQiLCJLZXlib2FyZCIsImlzVmlzaWJsZSIsImFkZExpc3RlbmVyIiwicmVtb3ZlIiwiZGlzbWlzcyIsInJlbW92ZUFsbExpc3RlbmVycyIsInJlbW92ZUxpc3RlbmVyIiwiX2RlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyJpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIikuZGVmYXVsdDtcbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG52YXIgX2Rpc21pc3NLZXlib2FyZCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uLy4uL21vZHVsZXMvZGlzbWlzc0tleWJvYXJkXCIpKTtcbi8qKlxuICogQ29weXJpZ2h0IChjKSBOaWNvbGFzIEdhbGxhZ2hlci5cbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKi9cblxuLy8gaW4gdGhlIGZ1dHVyZSB3ZSBjYW4gdXNlIGh0dHBzOi8vZ2l0aHViLmNvbS93M2MvdmlydHVhbC1rZXlib2FyZFxudmFyIEtleWJvYXJkID0ge1xuICBpc1Zpc2libGUoKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9LFxuICBhZGRMaXN0ZW5lcigpIHtcbiAgICByZXR1cm4ge1xuICAgICAgcmVtb3ZlOiAoKSA9PiB7fVxuICAgIH07XG4gIH0sXG4gIGRpc21pc3MoKSB7XG4gICAgKDAsIF9kaXNtaXNzS2V5Ym9hcmQuZGVmYXVsdCkoKTtcbiAgfSxcbiAgcmVtb3ZlQWxsTGlzdGVuZXJzKCkge30sXG4gIHJlbW92ZUxpc3RlbmVyKCkge31cbn07XG52YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBLZXlib2FyZDtcbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFFWixJQUFJQSxzQkFBc0IsR0FBR0MsT0FBTyxDQUFDLDhDQUE4QyxDQUFDLENBQUNDLE9BQU87QUFDNUZDLE9BQU8sQ0FBQ0MsVUFBVSxHQUFHLElBQUk7QUFDekJELE9BQU8sQ0FBQ0QsT0FBTyxHQUFHLEtBQUssQ0FBQztBQUN4QixJQUFJRyxnQkFBZ0IsR0FBR0wsc0JBQXNCLENBQUNDLE9BQU8sZ0NBQWdDLENBQUMsQ0FBQztBQVl2RixJQUFJSyxRQUFRLEdBQUc7RUFDYkMsU0FBUyxXQUFUQSxTQUFTQSxDQUFBLEVBQUc7SUFDVixPQUFPLEtBQUs7RUFDZCxDQUFDO0VBQ0RDLFdBQVcsV0FBWEEsV0FBV0EsQ0FBQSxFQUFHO0lBQ1osT0FBTztNQUNMQyxNQUFNLEVBQUUsU0FBUkEsTUFBTUEsQ0FBQSxFQUFRLENBQUM7SUFDakIsQ0FBQztFQUNILENBQUM7RUFDREMsT0FBTyxXQUFQQSxPQUFPQSxDQUFBLEVBQUc7SUFDUixDQUFDLENBQUMsRUFBRUwsZ0JBQWdCLENBQUNILE9BQU8sRUFBRSxDQUFDO0VBQ2pDLENBQUM7RUFDRFMsa0JBQWtCLFdBQWxCQSxrQkFBa0JBLENBQUEsRUFBRyxDQUFDLENBQUM7RUFDdkJDLGNBQWMsV0FBZEEsY0FBY0EsQ0FBQSxFQUFHLENBQUM7QUFDcEIsQ0FBQztBQUNELElBQUlDLFFBQVEsR0FBR1YsT0FBTyxDQUFDRCxPQUFPLEdBQUdJLFFBQVE7QUFDekNRLE1BQU0sQ0FBQ1gsT0FBTyxHQUFHQSxPQUFPLENBQUNELE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=