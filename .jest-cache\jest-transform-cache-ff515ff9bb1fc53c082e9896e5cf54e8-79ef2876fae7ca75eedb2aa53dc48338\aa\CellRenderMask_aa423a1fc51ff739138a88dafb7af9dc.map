{"version": 3, "names": ["_interopRequireDefault2", "require", "_toConsumableArray2", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "CellRenderMask", "_objectSpread2", "_invariant", "num<PERSON>ells", "_numCells", "_regions", "first", "last", "isSpacer", "key", "value", "enumerateRegions", "add<PERSON>ells", "cells", "_this$_regions", "_this$_findRegion", "_findRegion", "firstIntersect", "firstIntersectIdx", "_this$_findRegion2", "lastIntersect", "lastIntersectIdx", "newLeadRegion", "newTailRegion", "newMainRegion", "push", "replacementRegions", "concat", "numRegionsToDelete", "splice", "apply", "equals", "other", "length", "every", "region", "i", "cellIdx", "firstIdx", "lastIdx", "middleIdx", "Math", "floor", "middleRegion"], "sources": ["CellRenderMask.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.CellRenderMask = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nclass CellRenderMask {\n  constructor(numCells) {\n    (0, _invariant.default)(numCells >= 0, 'CellRenderMask must contain a non-negative number os cells');\n    this._numCells = numCells;\n    if (numCells === 0) {\n      this._regions = [];\n    } else {\n      this._regions = [{\n        first: 0,\n        last: numCells - 1,\n        isSpacer: true\n      }];\n    }\n  }\n  enumerateRegions() {\n    return this._regions;\n  }\n  addCells(cells) {\n    (0, _invariant.default)(cells.first >= 0 && cells.first < this._numCells && cells.last >= -1 && cells.last < this._numCells && cells.last >= cells.first - 1, 'CellRenderMask.addCells called with invalid cell range');\n\n    // VirtualizedList uses inclusive ranges, where zero-count states are\n    // possible. E.g. [0, -1] for no cells, starting at 0.\n    if (cells.last < cells.first) {\n      return;\n    }\n    var _this$_findRegion = this._findRegion(cells.first),\n      firstIntersect = _this$_findRegion[0],\n      firstIntersectIdx = _this$_findRegion[1];\n    var _this$_findRegion2 = this._findRegion(cells.last),\n      lastIntersect = _this$_findRegion2[0],\n      lastIntersectIdx = _this$_findRegion2[1];\n\n    // Fast-path if the cells to add are already all present in the mask. We\n    // will otherwise need to do some mutation.\n    if (firstIntersectIdx === lastIntersectIdx && !firstIntersect.isSpacer) {\n      return;\n    }\n\n    // We need to replace the existing covered regions with 1-3 new regions\n    // depending whether we need to split spacers out of overlapping regions.\n    var newLeadRegion = [];\n    var newTailRegion = [];\n    var newMainRegion = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, cells), {}, {\n      isSpacer: false\n    });\n    if (firstIntersect.first < newMainRegion.first) {\n      if (firstIntersect.isSpacer) {\n        newLeadRegion.push({\n          first: firstIntersect.first,\n          last: newMainRegion.first - 1,\n          isSpacer: true\n        });\n      } else {\n        newMainRegion.first = firstIntersect.first;\n      }\n    }\n    if (lastIntersect.last > newMainRegion.last) {\n      if (lastIntersect.isSpacer) {\n        newTailRegion.push({\n          first: newMainRegion.last + 1,\n          last: lastIntersect.last,\n          isSpacer: true\n        });\n      } else {\n        newMainRegion.last = lastIntersect.last;\n      }\n    }\n    var replacementRegions = [...newLeadRegion, newMainRegion, ...newTailRegion];\n    var numRegionsToDelete = lastIntersectIdx - firstIntersectIdx + 1;\n    this._regions.splice(firstIntersectIdx, numRegionsToDelete, ...replacementRegions);\n  }\n  numCells() {\n    return this._numCells;\n  }\n  equals(other) {\n    return this._numCells === other._numCells && this._regions.length === other._regions.length && this._regions.every((region, i) => region.first === other._regions[i].first && region.last === other._regions[i].last && region.isSpacer === other._regions[i].isSpacer);\n  }\n  _findRegion(cellIdx) {\n    var firstIdx = 0;\n    var lastIdx = this._regions.length - 1;\n    while (firstIdx <= lastIdx) {\n      var middleIdx = Math.floor((firstIdx + lastIdx) / 2);\n      var middleRegion = this._regions[middleIdx];\n      if (cellIdx >= middleRegion.first && cellIdx <= middleRegion.last) {\n        return [middleRegion, middleIdx];\n      } else if (cellIdx < middleRegion.first) {\n        lastIdx = middleIdx - 1;\n      } else if (cellIdx > middleRegion.last) {\n        firstIdx = middleIdx + 1;\n      }\n    }\n    (0, _invariant.default)(false, \"A region was not found containing cellIdx \" + cellIdx);\n  }\n}\nexports.CellRenderMask = CellRenderMask;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,mBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,gBAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,aAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAEb,IAAII,sBAAsB,GAAGJ,OAAO,CAAC,8CAA8C,CAAC,CAACK,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,cAAc,GAAG,KAAK,CAAC;AAC/B,IAAIC,cAAc,GAAGL,sBAAsB,CAACJ,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIU,UAAU,GAAGN,sBAAsB,CAACJ,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAAC,IAWjEQ,cAAc;EAClB,SAAAA,eAAYG,QAAQ,EAAE;IAAA,IAAAT,gBAAA,CAAAG,OAAA,QAAAG,cAAA;IACpB,CAAC,CAAC,EAAEE,UAAU,CAACL,OAAO,EAAEM,QAAQ,IAAI,CAAC,EAAE,4DAA4D,CAAC;IACpG,IAAI,CAACC,SAAS,GAAGD,QAAQ;IACzB,IAAIA,QAAQ,KAAK,CAAC,EAAE;MAClB,IAAI,CAACE,QAAQ,GAAG,EAAE;IACpB,CAAC,MAAM;MACL,IAAI,CAACA,QAAQ,GAAG,CAAC;QACfC,KAAK,EAAE,CAAC;QACRC,IAAI,EAAEJ,QAAQ,GAAG,CAAC;QAClBK,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF;EAAC,WAAAb,aAAA,CAAAE,OAAA,EAAAG,cAAA;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAC,gBAAgBA,CAAA,EAAG;MACjB,OAAO,IAAI,CAACN,QAAQ;IACtB;EAAC;IAAAI,GAAA;IAAAC,KAAA,EACD,SAAAE,QAAQA,CAACC,KAAK,EAAE;MAAA,IAAAC,cAAA;MACd,CAAC,CAAC,EAAEZ,UAAU,CAACL,OAAO,EAAEgB,KAAK,CAACP,KAAK,IAAI,CAAC,IAAIO,KAAK,CAACP,KAAK,GAAG,IAAI,CAACF,SAAS,IAAIS,KAAK,CAACN,IAAI,IAAI,CAAC,CAAC,IAAIM,KAAK,CAACN,IAAI,GAAG,IAAI,CAACH,SAAS,IAAIS,KAAK,CAACN,IAAI,IAAIM,KAAK,CAACP,KAAK,GAAG,CAAC,EAAE,wDAAwD,CAAC;MAIvN,IAAIO,KAAK,CAACN,IAAI,GAAGM,KAAK,CAACP,KAAK,EAAE;QAC5B;MACF;MACA,IAAIS,iBAAiB,GAAG,IAAI,CAACC,WAAW,CAACH,KAAK,CAACP,KAAK,CAAC;QACnDW,cAAc,GAAGF,iBAAiB,CAAC,CAAC,CAAC;QACrCG,iBAAiB,GAAGH,iBAAiB,CAAC,CAAC,CAAC;MAC1C,IAAII,kBAAkB,GAAG,IAAI,CAACH,WAAW,CAACH,KAAK,CAACN,IAAI,CAAC;QACnDa,aAAa,GAAGD,kBAAkB,CAAC,CAAC,CAAC;QACrCE,gBAAgB,GAAGF,kBAAkB,CAAC,CAAC,CAAC;MAI1C,IAAID,iBAAiB,KAAKG,gBAAgB,IAAI,CAACJ,cAAc,CAACT,QAAQ,EAAE;QACtE;MACF;MAIA,IAAIc,aAAa,GAAG,EAAE;MACtB,IAAIC,aAAa,GAAG,EAAE;MACtB,IAAIC,aAAa,GAAG,CAAC,CAAC,EAAEvB,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEI,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEgB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC1FL,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAIS,cAAc,CAACX,KAAK,GAAGkB,aAAa,CAAClB,KAAK,EAAE;QAC9C,IAAIW,cAAc,CAACT,QAAQ,EAAE;UAC3Bc,aAAa,CAACG,IAAI,CAAC;YACjBnB,KAAK,EAAEW,cAAc,CAACX,KAAK;YAC3BC,IAAI,EAAEiB,aAAa,CAAClB,KAAK,GAAG,CAAC;YAC7BE,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLgB,aAAa,CAAClB,KAAK,GAAGW,cAAc,CAACX,KAAK;QAC5C;MACF;MACA,IAAIc,aAAa,CAACb,IAAI,GAAGiB,aAAa,CAACjB,IAAI,EAAE;QAC3C,IAAIa,aAAa,CAACZ,QAAQ,EAAE;UAC1Be,aAAa,CAACE,IAAI,CAAC;YACjBnB,KAAK,EAAEkB,aAAa,CAACjB,IAAI,GAAG,CAAC;YAC7BA,IAAI,EAAEa,aAAa,CAACb,IAAI;YACxBC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLgB,aAAa,CAACjB,IAAI,GAAGa,aAAa,CAACb,IAAI;QACzC;MACF;MACA,IAAImB,kBAAkB,MAAAC,MAAA,CAAOL,aAAa,GAAEE,aAAa,GAAKD,aAAa,CAAC;MAC5E,IAAIK,kBAAkB,GAAGP,gBAAgB,GAAGH,iBAAiB,GAAG,CAAC;MACjE,CAAAJ,cAAA,OAAI,CAACT,QAAQ,EAACwB,MAAM,CAAAC,KAAA,CAAAhB,cAAA,GAACI,iBAAiB,EAAEU,kBAAkB,EAAAD,MAAA,KAAAlC,mBAAA,CAAAI,OAAA,EAAK6B,kBAAkB,GAAC;IACpF;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EACD,SAAAP,QAAQA,CAAA,EAAG;MACT,OAAO,IAAI,CAACC,SAAS;IACvB;EAAC;IAAAK,GAAA;IAAAC,KAAA,EACD,SAAAqB,MAAMA,CAACC,KAAK,EAAE;MACZ,OAAO,IAAI,CAAC5B,SAAS,KAAK4B,KAAK,CAAC5B,SAAS,IAAI,IAAI,CAACC,QAAQ,CAAC4B,MAAM,KAAKD,KAAK,CAAC3B,QAAQ,CAAC4B,MAAM,IAAI,IAAI,CAAC5B,QAAQ,CAAC6B,KAAK,CAAC,UAACC,MAAM,EAAEC,CAAC;QAAA,OAAKD,MAAM,CAAC7B,KAAK,KAAK0B,KAAK,CAAC3B,QAAQ,CAAC+B,CAAC,CAAC,CAAC9B,KAAK,IAAI6B,MAAM,CAAC5B,IAAI,KAAKyB,KAAK,CAAC3B,QAAQ,CAAC+B,CAAC,CAAC,CAAC7B,IAAI,IAAI4B,MAAM,CAAC3B,QAAQ,KAAKwB,KAAK,CAAC3B,QAAQ,CAAC+B,CAAC,CAAC,CAAC5B,QAAQ;MAAA,EAAC;IACzQ;EAAC;IAAAC,GAAA;IAAAC,KAAA,EACD,SAAAM,WAAWA,CAACqB,OAAO,EAAE;MACnB,IAAIC,QAAQ,GAAG,CAAC;MAChB,IAAIC,OAAO,GAAG,IAAI,CAAClC,QAAQ,CAAC4B,MAAM,GAAG,CAAC;MACtC,OAAOK,QAAQ,IAAIC,OAAO,EAAE;QAC1B,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACJ,QAAQ,GAAGC,OAAO,IAAI,CAAC,CAAC;QACpD,IAAII,YAAY,GAAG,IAAI,CAACtC,QAAQ,CAACmC,SAAS,CAAC;QAC3C,IAAIH,OAAO,IAAIM,YAAY,CAACrC,KAAK,IAAI+B,OAAO,IAAIM,YAAY,CAACpC,IAAI,EAAE;UACjE,OAAO,CAACoC,YAAY,EAAEH,SAAS,CAAC;QAClC,CAAC,MAAM,IAAIH,OAAO,GAAGM,YAAY,CAACrC,KAAK,EAAE;UACvCiC,OAAO,GAAGC,SAAS,GAAG,CAAC;QACzB,CAAC,MAAM,IAAIH,OAAO,GAAGM,YAAY,CAACpC,IAAI,EAAE;UACtC+B,QAAQ,GAAGE,SAAS,GAAG,CAAC;QAC1B;MACF;MACA,CAAC,CAAC,EAAEtC,UAAU,CAACL,OAAO,EAAE,KAAK,EAAE,4CAA4C,GAAGwC,OAAO,CAAC;IACxF;EAAC;AAAA;AAEHvC,OAAO,CAACE,cAAc,GAAGA,cAAc", "ignoreList": []}