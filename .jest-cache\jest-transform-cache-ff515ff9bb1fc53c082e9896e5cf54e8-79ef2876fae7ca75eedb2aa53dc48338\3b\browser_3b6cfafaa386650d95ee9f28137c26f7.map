{"version": 3, "names": ["isDOMAvailable", "exports", "canUseEventListeners", "canUseViewport", "isAsyncDebugging", "__DEV__", "global", "nativeExtensions", "nativeCallSyncHook", "RN$Bridgeless"], "sources": ["browser.ts"], "sourcesContent": ["declare const global: any;\n\n// In standard node environments there is no DOM API\nexport const isDOMAvailable = false;\nexport const canUseEventListeners = false;\nexport const canUseViewport = false;\n\nexport let isAsyncDebugging: boolean = false;\n\nif (__DEV__) {\n  // These native globals are injected by native React runtimes and not standard browsers\n  // we can use them to determine if the JS is being executed in Chrome.\n  isAsyncDebugging =\n    !global.nativeExtensions && !global.nativeCallSyncHook && !global.RN$Bridgeless;\n}\n"], "mappings": ";;;;AAGO,IAAMA,cAAc,GAAAC,OAAA,CAAAD,cAAA,GAAG,KAAK;AAC5B,IAAME,oBAAoB,GAAAD,OAAA,CAAAC,oBAAA,GAAG,KAAK;AAClC,IAAMC,cAAc,GAAAF,OAAA,CAAAE,cAAA,GAAG,KAAK;AAE5B,IAAIC,gBAAyB,GAAAH,OAAA,CAAAG,gBAAA,GAAG,KAAK;AAE5C,IAAIC,OAAO,EAAE;EAGXJ,OAAA,CAAAG,gBAAA,GAAAA,gBAAgB,GACd,CAACE,MAAM,CAACC,gBAAgB,IAAI,CAACD,MAAM,CAACE,kBAAkB,IAAI,CAACF,MAAM,CAACG,aAAa;AACnF", "ignoreList": []}