7ecab5fec7e8158ccad74e6c9610c7b2
import _defineProperty from "@babel/runtime/helpers/defineProperty";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_82mwvlolh() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\native\\MobileFeatureService.ts";
  var hash = "52b3acdf77d84067f735e384718c7e478a4a9bf4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\native\\MobileFeatureService.ts",
    statementMap: {
      "0": {
        start: {
          line: 79,
          column: 71
        },
        end: {
          line: 79,
          column: 75
        }
      },
      "1": {
        start: {
          line: 80,
          column: 38
        },
        end: {
          line: 80,
          column: 42
        }
      },
      "2": {
        start: {
          line: 81,
          column: 34
        },
        end: {
          line: 81,
          column: 38
        }
      },
      "3": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 84,
          column: 35
        }
      },
      "4": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "5": {
        start: {
          line: 93,
          column: 6
        },
        end: {
          line: 99,
          column: 9
        }
      },
      "6": {
        start: {
          line: 94,
          column: 41
        },
        end: {
          line: 98,
          column: 9
        }
      },
      "7": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 113,
          column: 9
        }
      },
      "8": {
        start: {
          line: 115,
          column: 6
        },
        end: {
          line: 126,
          column: 9
        }
      },
      "9": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 65
        }
      },
      "10": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 165,
          column: 5
        }
      },
      "11": {
        start: {
          line: 137,
          column: 31
        },
        end: {
          line: 137,
          column: 75
        }
      },
      "12": {
        start: {
          line: 138,
          column: 35
        },
        end: {
          line: 138,
          column: 83
        }
      },
      "13": {
        start: {
          line: 139,
          column: 37
        },
        end: {
          line: 139,
          column: 81
        }
      },
      "14": {
        start: {
          line: 141,
          column: 45
        },
        end: {
          line: 145,
          column: 7
        }
      },
      "15": {
        start: {
          line: 147,
          column: 25
        },
        end: {
          line: 147,
          column: 77
        }
      },
      "16": {
        start: {
          line: 147,
          column: 69
        },
        end: {
          line: 147,
          column: 76
        }
      },
      "17": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 155,
          column: 7
        }
      },
      "18": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 154,
          column: 10
        }
      },
      "19": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 44
        }
      },
      "20": {
        start: {
          line: 159,
          column: 23
        },
        end: {
          line: 159,
          column: 63
        }
      },
      "21": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 164,
          column: 8
        }
      },
      "22": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 202,
          column: 5
        }
      },
      "23": {
        start: {
          line: 173,
          column: 25
        },
        end: {
          line: 173,
          column: 75
        }
      },
      "24": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 180,
          column: 7
        }
      },
      "25": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 179,
          column: 10
        }
      },
      "26": {
        start: {
          line: 182,
          column: 23
        },
        end: {
          line: 186,
          column: 8
        }
      },
      "27": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 198,
          column: 8
        }
      },
      "28": {
        start: {
          line: 200,
          column: 23
        },
        end: {
          line: 200,
          column: 63
        }
      },
      "29": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 61
        }
      },
      "30": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 249,
          column: 5
        }
      },
      "31": {
        start: {
          line: 217,
          column: 25
        },
        end: {
          line: 217,
          column: 75
        }
      },
      "32": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 224,
          column: 7
        }
      },
      "33": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 223,
          column: 10
        }
      },
      "34": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 243,
          column: 8
        }
      },
      "35": {
        start: {
          line: 233,
          column: 10
        },
        end: {
          line: 241,
          column: 13
        }
      },
      "36": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 31
        }
      },
      "37": {
        start: {
          line: 247,
          column: 23
        },
        end: {
          line: 247,
          column: 63
        }
      },
      "38": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 61
        }
      },
      "39": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 259,
          column: 5
        }
      },
      "40": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 257,
          column: 41
        }
      },
      "41": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 258,
          column: 39
        }
      },
      "42": {
        start: {
          line: 266,
          column: 4
        },
        end: {
          line: 294,
          column: 5
        }
      },
      "43": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 272,
          column: 7
        }
      },
      "44": {
        start: {
          line: 268,
          column: 8
        },
        end: {
          line: 271,
          column: 10
        }
      },
      "45": {
        start: {
          line: 274,
          column: 41
        },
        end: {
          line: 274,
          column: 82
        }
      },
      "46": {
        start: {
          line: 275,
          column: 24
        },
        end: {
          line: 275,
          column: 38
        }
      },
      "47": {
        start: {
          line: 277,
          column: 6
        },
        end: {
          line: 280,
          column: 7
        }
      },
      "48": {
        start: {
          line: 278,
          column: 27
        },
        end: {
          line: 278,
          column: 72
        }
      },
      "49": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 279,
          column: 29
        }
      },
      "50": {
        start: {
          line: 282,
          column: 6
        },
        end: {
          line: 287,
          column: 7
        }
      },
      "51": {
        start: {
          line: 283,
          column: 8
        },
        end: {
          line: 286,
          column: 10
        }
      },
      "52": {
        start: {
          line: 289,
          column: 20
        },
        end: {
          line: 289,
          column: 63
        }
      },
      "53": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 50
        }
      },
      "54": {
        start: {
          line: 292,
          column: 23
        },
        end: {
          line: 292,
          column: 63
        }
      },
      "55": {
        start: {
          line: 293,
          column: 6
        },
        end: {
          line: 293,
          column: 61
        }
      },
      "56": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 325,
          column: 5
        }
      },
      "57": {
        start: {
          line: 309,
          column: 29
        },
        end: {
          line: 319,
          column: 8
        }
      },
      "58": {
        start: {
          line: 321,
          column: 6
        },
        end: {
          line: 321,
          column: 47
        }
      },
      "59": {
        start: {
          line: 323,
          column: 23
        },
        end: {
          line: 323,
          column: 63
        }
      },
      "60": {
        start: {
          line: 324,
          column: 6
        },
        end: {
          line: 324,
          column: 61
        }
      },
      "61": {
        start: {
          line: 332,
          column: 4
        },
        end: {
          line: 338,
          column: 5
        }
      },
      "62": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 333,
          column: 75
        }
      },
      "63": {
        start: {
          line: 334,
          column: 6
        },
        end: {
          line: 334,
          column: 31
        }
      },
      "64": {
        start: {
          line: 336,
          column: 23
        },
        end: {
          line: 336,
          column: 63
        }
      },
      "65": {
        start: {
          line: 337,
          column: 6
        },
        end: {
          line: 337,
          column: 61
        }
      },
      "66": {
        start: {
          line: 349,
          column: 4
        },
        end: {
          line: 349,
          column: 102
        }
      },
      "67": {
        start: {
          line: 352,
          column: 4
        },
        end: {
          line: 352,
          column: 106
        }
      },
      "68": {
        start: {
          line: 359,
          column: 4
        },
        end: {
          line: 362,
          column: 5
        }
      },
      "69": {
        start: {
          line: 360,
          column: 6
        },
        end: {
          line: 360,
          column: 78
        }
      },
      "70": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 361,
          column: 39
        }
      },
      "71": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 367,
          column: 5
        }
      },
      "72": {
        start: {
          line: 365,
          column: 6
        },
        end: {
          line: 365,
          column: 74
        }
      },
      "73": {
        start: {
          line: 366,
          column: 6
        },
        end: {
          line: 366,
          column: 35
        }
      },
      "74": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 407,
          column: 5
        }
      },
      "75": {
        start: {
          line: 375,
          column: 26
        },
        end: {
          line: 375,
          column: 70
        }
      },
      "76": {
        start: {
          line: 376,
          column: 29
        },
        end: {
          line: 376,
          column: 90
        }
      },
      "77": {
        start: {
          line: 377,
          column: 25
        },
        end: {
          line: 377,
          column: 68
        }
      },
      "78": {
        start: {
          line: 378,
          column: 28
        },
        end: {
          line: 378,
          column: 77
        }
      },
      "79": {
        start: {
          line: 380,
          column: 24
        },
        end: {
          line: 391,
          column: 8
        }
      },
      "80": {
        start: {
          line: 381,
          column: 8
        },
        end: {
          line: 390,
          column: 9
        }
      },
      "81": {
        start: {
          line: 383,
          column: 12
        },
        end: {
          line: 383,
          column: 33
        }
      },
      "82": {
        start: {
          line: 385,
          column: 12
        },
        end: {
          line: 385,
          column: 26
        }
      },
      "83": {
        start: {
          line: 387,
          column: 12
        },
        end: {
          line: 387,
          column: 26
        }
      },
      "84": {
        start: {
          line: 389,
          column: 12
        },
        end: {
          line: 389,
          column: 29
        }
      },
      "85": {
        start: {
          line: 393,
          column: 6
        },
        end: {
          line: 399,
          column: 8
        }
      },
      "86": {
        start: {
          line: 401,
          column: 6
        },
        end: {
          line: 406,
          column: 8
        }
      },
      "87": {
        start: {
          line: 416,
          column: 4
        },
        end: {
          line: 444,
          column: 5
        }
      },
      "88": {
        start: {
          line: 417,
          column: 28
        },
        end: {
          line: 417,
          column: 57
        }
      },
      "89": {
        start: {
          line: 419,
          column: 6
        },
        end: {
          line: 424,
          column: 7
        }
      },
      "90": {
        start: {
          line: 420,
          column: 8
        },
        end: {
          line: 423,
          column: 10
        }
      },
      "91": {
        start: {
          line: 426,
          column: 21
        },
        end: {
          line: 431,
          column: 8
        }
      },
      "92": {
        start: {
          line: 433,
          column: 6
        },
        end: {
          line: 440,
          column: 7
        }
      },
      "93": {
        start: {
          line: 434,
          column: 8
        },
        end: {
          line: 434,
          column: 33
        }
      },
      "94": {
        start: {
          line: 436,
          column: 8
        },
        end: {
          line: 439,
          column: 10
        }
      },
      "95": {
        start: {
          line: 442,
          column: 23
        },
        end: {
          line: 442,
          column: 63
        }
      },
      "96": {
        start: {
          line: 443,
          column: 6
        },
        end: {
          line: 443,
          column: 61
        }
      },
      "97": {
        start: {
          line: 451,
          column: 4
        },
        end: {
          line: 468,
          column: 5
        }
      },
      "98": {
        start: {
          line: 452,
          column: 6
        },
        end: {
          line: 461,
          column: 8
        }
      },
      "99": {
        start: {
          line: 463,
          column: 6
        },
        end: {
          line: 467,
          column: 8
        }
      },
      "100": {
        start: {
          line: 475,
          column: 4
        },
        end: {
          line: 498,
          column: 5
        }
      },
      "101": {
        start: {
          line: 476,
          column: 27
        },
        end: {
          line: 476,
          column: 63
        }
      },
      "102": {
        start: {
          line: 477,
          column: 27
        },
        end: {
          line: 477,
          column: 63
        }
      },
      "103": {
        start: {
          line: 478,
          column: 27
        },
        end: {
          line: 478,
          column: 69
        }
      },
      "104": {
        start: {
          line: 480,
          column: 23
        },
        end: {
          line: 485,
          column: 7
        }
      },
      "105": {
        start: {
          line: 487,
          column: 6
        },
        end: {
          line: 491,
          column: 8
        }
      },
      "106": {
        start: {
          line: 493,
          column: 6
        },
        end: {
          line: 497,
          column: 8
        }
      },
      "107": {
        start: {
          line: 505,
          column: 4
        },
        end: {
          line: 528,
          column: 5
        }
      },
      "108": {
        start: {
          line: 506,
          column: 27
        },
        end: {
          line: 506,
          column: 63
        }
      },
      "109": {
        start: {
          line: 508,
          column: 22
        },
        end: {
          line: 515,
          column: 7
        }
      },
      "110": {
        start: {
          line: 517,
          column: 6
        },
        end: {
          line: 521,
          column: 8
        }
      },
      "111": {
        start: {
          line: 523,
          column: 6
        },
        end: {
          line: 527,
          column: 8
        }
      },
      "112": {
        start: {
          line: 535,
          column: 4
        },
        end: {
          line: 579,
          column: 5
        }
      },
      "113": {
        start: {
          line: 538,
          column: 6
        },
        end: {
          line: 567,
          column: 7
        }
      },
      "114": {
        start: {
          line: 539,
          column: 8
        },
        end: {
          line: 551,
          column: 9
        }
      },
      "115": {
        start: {
          line: 541,
          column: 12
        },
        end: {
          line: 541,
          column: 34
        }
      },
      "116": {
        start: {
          line: 542,
          column: 12
        },
        end: {
          line: 542,
          column: 18
        }
      },
      "117": {
        start: {
          line: 544,
          column: 12
        },
        end: {
          line: 544,
          column: 52
        }
      },
      "118": {
        start: {
          line: 545,
          column: 12
        },
        end: {
          line: 545,
          column: 18
        }
      },
      "119": {
        start: {
          line: 547,
          column: 12
        },
        end: {
          line: 547,
          column: 47
        }
      },
      "120": {
        start: {
          line: 548,
          column: 12
        },
        end: {
          line: 548,
          column: 18
        }
      },
      "121": {
        start: {
          line: 550,
          column: 12
        },
        end: {
          line: 550,
          column: 34
        }
      },
      "122": {
        start: {
          line: 554,
          column: 8
        },
        end: {
          line: 566,
          column: 9
        }
      },
      "123": {
        start: {
          line: 556,
          column: 12
        },
        end: {
          line: 556,
          column: 47
        }
      },
      "124": {
        start: {
          line: 557,
          column: 12
        },
        end: {
          line: 557,
          column: 18
        }
      },
      "125": {
        start: {
          line: 559,
          column: 12
        },
        end: {
          line: 559,
          column: 62
        }
      },
      "126": {
        start: {
          line: 560,
          column: 12
        },
        end: {
          line: 560,
          column: 18
        }
      },
      "127": {
        start: {
          line: 562,
          column: 12
        },
        end: {
          line: 562,
          column: 63
        }
      },
      "128": {
        start: {
          line: 563,
          column: 12
        },
        end: {
          line: 563,
          column: 18
        }
      },
      "129": {
        start: {
          line: 565,
          column: 12
        },
        end: {
          line: 565,
          column: 66
        }
      },
      "130": {
        start: {
          line: 569,
          column: 22
        },
        end: {
          line: 569,
          column: 51
        }
      },
      "131": {
        start: {
          line: 570,
          column: 6
        },
        end: {
          line: 575,
          column: 7
        }
      },
      "132": {
        start: {
          line: 571,
          column: 8
        },
        end: {
          line: 571,
          column: 35
        }
      },
      "133": {
        start: {
          line: 572,
          column: 8
        },
        end: {
          line: 572,
          column: 33
        }
      },
      "134": {
        start: {
          line: 574,
          column: 8
        },
        end: {
          line: 574,
          column: 65
        }
      },
      "135": {
        start: {
          line: 577,
          column: 23
        },
        end: {
          line: 577,
          column: 63
        }
      },
      "136": {
        start: {
          line: 578,
          column: 6
        },
        end: {
          line: 578,
          column: 61
        }
      },
      "137": {
        start: {
          line: 586,
          column: 4
        },
        end: {
          line: 586,
          column: 32
        }
      },
      "138": {
        start: {
          line: 587,
          column: 4
        },
        end: {
          line: 587,
          column: 39
        }
      },
      "139": {
        start: {
          line: 592,
          column: 36
        },
        end: {
          line: 592,
          column: 62
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 83,
            column: 2
          },
          end: {
            line: 83,
            column: 3
          }
        },
        loc: {
          start: {
            line: 83,
            column: 16
          },
          end: {
            line: 85,
            column: 3
          }
        },
        line: 83
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        },
        loc: {
          start: {
            line: 90,
            column: 57
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 90
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 94,
            column: 28
          },
          end: {
            line: 94,
            column: 29
          }
        },
        loc: {
          start: {
            line: 94,
            column: 41
          },
          end: {
            line: 98,
            column: 9
          }
        },
        line: 94
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 135,
            column: 2
          },
          end: {
            line: 135,
            column: 3
          }
        },
        loc: {
          start: {
            line: 135,
            column: 114
          },
          end: {
            line: 166,
            column: 3
          }
        },
        line: 135
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 147,
            column: 58
          },
          end: {
            line: 147,
            column: 59
          }
        },
        loc: {
          start: {
            line: 147,
            column: 69
          },
          end: {
            line: 147,
            column: 76
          }
        },
        line: 147
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 171,
            column: 2
          },
          end: {
            line: 171,
            column: 3
          }
        },
        loc: {
          start: {
            line: 171,
            column: 89
          },
          end: {
            line: 203,
            column: 3
          }
        },
        line: 171
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 208,
            column: 2
          },
          end: {
            line: 208,
            column: 3
          }
        },
        loc: {
          start: {
            line: 215,
            column: 51
          },
          end: {
            line: 250,
            column: 3
          }
        },
        line: 215
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 232,
            column: 8
          },
          end: {
            line: 232,
            column: 9
          }
        },
        loc: {
          start: {
            line: 232,
            column: 22
          },
          end: {
            line: 242,
            column: 9
          }
        },
        line: 232
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 255,
            column: 2
          },
          end: {
            line: 255,
            column: 3
          }
        },
        loc: {
          start: {
            line: 255,
            column: 31
          },
          end: {
            line: 260,
            column: 3
          }
        },
        line: 255
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 265,
            column: 2
          },
          end: {
            line: 265,
            column: 3
          }
        },
        loc: {
          start: {
            line: 265,
            column: 104
          },
          end: {
            line: 295,
            column: 3
          }
        },
        line: 265
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 300,
            column: 2
          },
          end: {
            line: 300,
            column: 3
          }
        },
        loc: {
          start: {
            line: 307,
            column: 76
          },
          end: {
            line: 326,
            column: 3
          }
        },
        line: 307
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 331,
            column: 2
          },
          end: {
            line: 331,
            column: 3
          }
        },
        loc: {
          start: {
            line: 331,
            column: 98
          },
          end: {
            line: 339,
            column: 3
          }
        },
        line: 331
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 344,
            column: 2
          },
          end: {
            line: 344,
            column: 3
          }
        },
        loc: {
          start: {
            line: 347,
            column: 10
          },
          end: {
            line: 353,
            column: 3
          }
        },
        line: 347
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 358,
            column: 2
          },
          end: {
            line: 358,
            column: 3
          }
        },
        loc: {
          start: {
            line: 358,
            column: 38
          },
          end: {
            line: 368,
            column: 3
          }
        },
        line: 358
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 373,
            column: 51
          },
          end: {
            line: 408,
            column: 3
          }
        },
        line: 373
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 380,
            column: 43
          },
          end: {
            line: 380,
            column: 44
          }
        },
        loc: {
          start: {
            line: 380,
            column: 51
          },
          end: {
            line: 391,
            column: 7
          }
        },
        line: 380
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 413,
            column: 2
          },
          end: {
            line: 413,
            column: 3
          }
        },
        loc: {
          start: {
            line: 415,
            column: 51
          },
          end: {
            line: 445,
            column: 3
          }
        },
        line: 415
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 450,
            column: 2
          },
          end: {
            line: 450,
            column: 3
          }
        },
        loc: {
          start: {
            line: 450,
            column: 45
          },
          end: {
            line: 469,
            column: 3
          }
        },
        line: 450
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 474,
            column: 2
          },
          end: {
            line: 474,
            column: 3
          }
        },
        loc: {
          start: {
            line: 474,
            column: 47
          },
          end: {
            line: 499,
            column: 3
          }
        },
        line: 474
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 504,
            column: 2
          },
          end: {
            line: 504,
            column: 3
          }
        },
        loc: {
          start: {
            line: 504,
            column: 47
          },
          end: {
            line: 529,
            column: 3
          }
        },
        line: 504
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 534,
            column: 2
          },
          end: {
            line: 534,
            column: 3
          }
        },
        loc: {
          start: {
            line: 534,
            column: 120
          },
          end: {
            line: 580,
            column: 3
          }
        },
        line: 534
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 585,
            column: 2
          },
          end: {
            line: 585,
            column: 3
          }
        },
        loc: {
          start: {
            line: 585,
            column: 18
          },
          end: {
            line: 588,
            column: 3
          }
        },
        line: 585
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 149,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "1": {
        loc: {
          start: {
            line: 175,
            column: 6
          },
          end: {
            line: 180,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 6
          },
          end: {
            line: 180,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "2": {
        loc: {
          start: {
            line: 192,
            column: 20
          },
          end: {
            line: 192,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 192,
            column: 20
          },
          end: {
            line: 192,
            column: 44
          }
        }, {
          start: {
            line: 192,
            column: 48
          },
          end: {
            line: 192,
            column: 57
          }
        }],
        line: 192
      },
      "3": {
        loc: {
          start: {
            line: 193,
            column: 20
          },
          end: {
            line: 193,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 193,
            column: 20
          },
          end: {
            line: 193,
            column: 44
          }
        }, {
          start: {
            line: 193,
            column: 48
          },
          end: {
            line: 193,
            column: 57
          }
        }],
        line: 193
      },
      "4": {
        loc: {
          start: {
            line: 194,
            column: 17
          },
          end: {
            line: 194,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 194,
            column: 17
          },
          end: {
            line: 194,
            column: 38
          }
        }, {
          start: {
            line: 194,
            column: 42
          },
          end: {
            line: 194,
            column: 51
          }
        }],
        line: 194
      },
      "5": {
        loc: {
          start: {
            line: 195,
            column: 19
          },
          end: {
            line: 195,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 195,
            column: 19
          },
          end: {
            line: 195,
            column: 42
          }
        }, {
          start: {
            line: 195,
            column: 46
          },
          end: {
            line: 195,
            column: 55
          }
        }],
        line: 195
      },
      "6": {
        loc: {
          start: {
            line: 210,
            column: 4
          },
          end: {
            line: 214,
            column: 10
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 214,
            column: 8
          },
          end: {
            line: 214,
            column: 10
          }
        }],
        line: 210
      },
      "7": {
        loc: {
          start: {
            line: 219,
            column: 6
          },
          end: {
            line: 224,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 6
          },
          end: {
            line: 224,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "8": {
        loc: {
          start: {
            line: 228,
            column: 20
          },
          end: {
            line: 228,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 20
          },
          end: {
            line: 228,
            column: 36
          }
        }, {
          start: {
            line: 228,
            column: 40
          },
          end: {
            line: 228,
            column: 62
          }
        }],
        line: 228
      },
      "9": {
        loc: {
          start: {
            line: 229,
            column: 24
          },
          end: {
            line: 229,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 24
          },
          end: {
            line: 229,
            column: 44
          }
        }, {
          start: {
            line: 229,
            column: 48
          },
          end: {
            line: 229,
            column: 52
          }
        }],
        line: 229
      },
      "10": {
        loc: {
          start: {
            line: 230,
            column: 28
          },
          end: {
            line: 230,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 230,
            column: 28
          },
          end: {
            line: 230,
            column: 52
          }
        }, {
          start: {
            line: 230,
            column: 56
          },
          end: {
            line: 230,
            column: 58
          }
        }],
        line: 230
      },
      "11": {
        loc: {
          start: {
            line: 236,
            column: 22
          },
          end: {
            line: 236,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 236,
            column: 22
          },
          end: {
            line: 236,
            column: 46
          }
        }, {
          start: {
            line: 236,
            column: 50
          },
          end: {
            line: 236,
            column: 59
          }
        }],
        line: 236
      },
      "12": {
        loc: {
          start: {
            line: 237,
            column: 22
          },
          end: {
            line: 237,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 22
          },
          end: {
            line: 237,
            column: 46
          }
        }, {
          start: {
            line: 237,
            column: 50
          },
          end: {
            line: 237,
            column: 59
          }
        }],
        line: 237
      },
      "13": {
        loc: {
          start: {
            line: 238,
            column: 19
          },
          end: {
            line: 238,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 238,
            column: 19
          },
          end: {
            line: 238,
            column: 40
          }
        }, {
          start: {
            line: 238,
            column: 44
          },
          end: {
            line: 238,
            column: 53
          }
        }],
        line: 238
      },
      "14": {
        loc: {
          start: {
            line: 239,
            column: 21
          },
          end: {
            line: 239,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 239,
            column: 21
          },
          end: {
            line: 239,
            column: 44
          }
        }, {
          start: {
            line: 239,
            column: 48
          },
          end: {
            line: 239,
            column: 57
          }
        }],
        line: 239
      },
      "15": {
        loc: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 259,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 259,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 256
      },
      "16": {
        loc: {
          start: {
            line: 267,
            column: 6
          },
          end: {
            line: 272,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 267,
            column: 6
          },
          end: {
            line: 272,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 267
      },
      "17": {
        loc: {
          start: {
            line: 277,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 277,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 277
      },
      "18": {
        loc: {
          start: {
            line: 282,
            column: 6
          },
          end: {
            line: 287,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 6
          },
          end: {
            line: 287,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "19": {
        loc: {
          start: {
            line: 313,
            column: 16
          },
          end: {
            line: 313,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 313,
            column: 16
          },
          end: {
            line: 313,
            column: 33
          }
        }, {
          start: {
            line: 313,
            column: 37
          },
          end: {
            line: 313,
            column: 39
          }
        }],
        line: 313
      },
      "20": {
        loc: {
          start: {
            line: 318,
            column: 17
          },
          end: {
            line: 318,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 318,
            column: 17
          },
          end: {
            line: 318,
            column: 24
          }
        }, {
          start: {
            line: 318,
            column: 28
          },
          end: {
            line: 318,
            column: 32
          }
        }],
        line: 318
      },
      "21": {
        loc: {
          start: {
            line: 359,
            column: 4
          },
          end: {
            line: 362,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 359,
            column: 4
          },
          end: {
            line: 362,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 359
      },
      "22": {
        loc: {
          start: {
            line: 364,
            column: 4
          },
          end: {
            line: 367,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 364,
            column: 4
          },
          end: {
            line: 367,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 364
      },
      "23": {
        loc: {
          start: {
            line: 381,
            column: 8
          },
          end: {
            line: 390,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 382,
            column: 10
          },
          end: {
            line: 383,
            column: 33
          }
        }, {
          start: {
            line: 384,
            column: 10
          },
          end: {
            line: 385,
            column: 26
          }
        }, {
          start: {
            line: 386,
            column: 10
          },
          end: {
            line: 387,
            column: 26
          }
        }, {
          start: {
            line: 388,
            column: 10
          },
          end: {
            line: 389,
            column: 29
          }
        }],
        line: 381
      },
      "24": {
        loc: {
          start: {
            line: 397,
            column: 23
          },
          end: {
            line: 398,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 397,
            column: 87
          },
          end: {
            line: 397,
            column: 98
          }
        }, {
          start: {
            line: 398,
            column: 22
          },
          end: {
            line: 398,
            column: 102
          }
        }],
        line: 397
      },
      "25": {
        loc: {
          start: {
            line: 398,
            column: 22
          },
          end: {
            line: 398,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 398,
            column: 83
          },
          end: {
            line: 398,
            column: 93
          }
        }, {
          start: {
            line: 398,
            column: 96
          },
          end: {
            line: 398,
            column: 102
          }
        }],
        line: 398
      },
      "26": {
        loc: {
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 414,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 414,
            column: 28
          },
          end: {
            line: 414,
            column: 54
          }
        }],
        line: 414
      },
      "27": {
        loc: {
          start: {
            line: 419,
            column: 6
          },
          end: {
            line: 424,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 419,
            column: 6
          },
          end: {
            line: 424,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 419
      },
      "28": {
        loc: {
          start: {
            line: 419,
            column: 10
          },
          end: {
            line: 419,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 419,
            column: 10
          },
          end: {
            line: 419,
            column: 36
          }
        }, {
          start: {
            line: 419,
            column: 40
          },
          end: {
            line: 419,
            column: 65
          }
        }],
        line: 419
      },
      "29": {
        loc: {
          start: {
            line: 433,
            column: 6
          },
          end: {
            line: 440,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 433,
            column: 6
          },
          end: {
            line: 440,
            column: 7
          }
        }, {
          start: {
            line: 435,
            column: 13
          },
          end: {
            line: 440,
            column: 7
          }
        }],
        line: 433
      },
      "30": {
        loc: {
          start: {
            line: 438,
            column: 17
          },
          end: {
            line: 438,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 438,
            column: 17
          },
          end: {
            line: 438,
            column: 29
          }
        }, {
          start: {
            line: 438,
            column: 33
          },
          end: {
            line: 438,
            column: 56
          }
        }],
        line: 438
      },
      "31": {
        loc: {
          start: {
            line: 455,
            column: 20
          },
          end: {
            line: 455,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 455,
            column: 20
          },
          end: {
            line: 455,
            column: 37
          }
        }, {
          start: {
            line: 455,
            column: 41
          },
          end: {
            line: 455,
            column: 50
          }
        }],
        line: 455
      },
      "32": {
        loc: {
          start: {
            line: 456,
            column: 19
          },
          end: {
            line: 456,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 456,
            column: 19
          },
          end: {
            line: 456,
            column: 35
          }
        }, {
          start: {
            line: 456,
            column: 39
          },
          end: {
            line: 456,
            column: 48
          }
        }],
        line: 456
      },
      "33": {
        loc: {
          start: {
            line: 457,
            column: 15
          },
          end: {
            line: 457,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 457,
            column: 15
          },
          end: {
            line: 457,
            column: 27
          }
        }, {
          start: {
            line: 457,
            column: 31
          },
          end: {
            line: 457,
            column: 40
          }
        }],
        line: 457
      },
      "34": {
        loc: {
          start: {
            line: 458,
            column: 22
          },
          end: {
            line: 458,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 458,
            column: 22
          },
          end: {
            line: 458,
            column: 41
          }
        }, {
          start: {
            line: 458,
            column: 45
          },
          end: {
            line: 458,
            column: 54
          }
        }],
        line: 458
      },
      "35": {
        loc: {
          start: {
            line: 460,
            column: 21
          },
          end: {
            line: 460,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 460,
            column: 21
          },
          end: {
            line: 460,
            column: 39
          }
        }, {
          start: {
            line: 460,
            column: 43
          },
          end: {
            line: 460,
            column: 52
          }
        }],
        line: 460
      },
      "36": {
        loc: {
          start: {
            line: 489,
            column: 22
          },
          end: {
            line: 489,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 489,
            column: 22
          },
          end: {
            line: 489,
            column: 44
          }
        }, {
          start: {
            line: 489,
            column: 48
          },
          end: {
            line: 489,
            column: 57
          }
        }],
        line: 489
      },
      "37": {
        loc: {
          start: {
            line: 518,
            column: 21
          },
          end: {
            line: 518,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 518,
            column: 21
          },
          end: {
            line: 518,
            column: 45
          }
        }, {
          start: {
            line: 518,
            column: 49
          },
          end: {
            line: 518,
            column: 54
          }
        }],
        line: 518
      },
      "38": {
        loc: {
          start: {
            line: 519,
            column: 14
          },
          end: {
            line: 519,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 519,
            column: 14
          },
          end: {
            line: 519,
            column: 40
          }
        }, {
          start: {
            line: 519,
            column: 44
          },
          end: {
            line: 519,
            column: 53
          }
        }],
        line: 519
      },
      "39": {
        loc: {
          start: {
            line: 520,
            column: 29
          },
          end: {
            line: 520,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 520,
            column: 29
          },
          end: {
            line: 520,
            column: 61
          }
        }, {
          start: {
            line: 520,
            column: 65
          },
          end: {
            line: 520,
            column: 70
          }
        }],
        line: 520
      },
      "40": {
        loc: {
          start: {
            line: 538,
            column: 6
          },
          end: {
            line: 567,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 538,
            column: 6
          },
          end: {
            line: 567,
            column: 7
          }
        }, {
          start: {
            line: 552,
            column: 13
          },
          end: {
            line: 567,
            column: 7
          }
        }],
        line: 538
      },
      "41": {
        loc: {
          start: {
            line: 539,
            column: 8
          },
          end: {
            line: 551,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 540,
            column: 10
          },
          end: {
            line: 542,
            column: 18
          }
        }, {
          start: {
            line: 543,
            column: 10
          },
          end: {
            line: 545,
            column: 18
          }
        }, {
          start: {
            line: 546,
            column: 10
          },
          end: {
            line: 548,
            column: 18
          }
        }, {
          start: {
            line: 549,
            column: 10
          },
          end: {
            line: 550,
            column: 34
          }
        }],
        line: 539
      },
      "42": {
        loc: {
          start: {
            line: 554,
            column: 8
          },
          end: {
            line: 566,
            column: 9
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 555,
            column: 10
          },
          end: {
            line: 557,
            column: 18
          }
        }, {
          start: {
            line: 558,
            column: 10
          },
          end: {
            line: 560,
            column: 18
          }
        }, {
          start: {
            line: 561,
            column: 10
          },
          end: {
            line: 563,
            column: 18
          }
        }, {
          start: {
            line: 564,
            column: 10
          },
          end: {
            line: 565,
            column: 66
          }
        }],
        line: 554
      },
      "43": {
        loc: {
          start: {
            line: 570,
            column: 6
          },
          end: {
            line: 575,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 570,
            column: 6
          },
          end: {
            line: 575,
            column: 7
          }
        }, {
          start: {
            line: 573,
            column: 13
          },
          end: {
            line: 575,
            column: 7
          }
        }],
        line: 570
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0, 0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0, 0, 0],
      "42": [0, 0, 0, 0],
      "43": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "52b3acdf77d84067f735e384718c7e478a4a9bf4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_82mwvlolh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_82mwvlolh();
import { Platform, Linking } from 'react-native';
import * as Location from 'expo-location';
import * as Camera from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';
import * as Notifications from 'expo-notifications';
import * as LocalAuthentication from 'expo-local-authentication';
import * as Device from 'expo-device';
import * as Battery from 'expo-battery';
import * as Network from 'expo-network';
import { handleError } from "../../utils/errorHandling";
var MobileFeatureService = function () {
  function MobileFeatureService() {
    _classCallCheck(this, MobileFeatureService);
    this.locationSubscription = (cov_82mwvlolh().s[0]++, null);
    this.notificationListener = (cov_82mwvlolh().s[1]++, null);
    this.responseListener = (cov_82mwvlolh().s[2]++, null);
    cov_82mwvlolh().f[0]++;
    cov_82mwvlolh().s[3]++;
    this.initializeNotifications();
  }
  return _createClass(MobileFeatureService, [{
    key: "initializeNotifications",
    value: (function () {
      var _initializeNotifications = _asyncToGenerator(function* () {
        cov_82mwvlolh().f[1]++;
        cov_82mwvlolh().s[4]++;
        try {
          cov_82mwvlolh().s[5]++;
          Notifications.setNotificationHandler({
            handleNotification: function () {
              var _handleNotification = _asyncToGenerator(function* () {
                cov_82mwvlolh().f[2]++;
                cov_82mwvlolh().s[6]++;
                return {
                  shouldShowAlert: true,
                  shouldPlaySound: true,
                  shouldSetBadge: true
                };
              });
              function handleNotification() {
                return _handleNotification.apply(this, arguments);
              }
              return handleNotification;
            }()
          });
          cov_82mwvlolh().s[7]++;
          yield Notifications.setNotificationCategoryAsync('tennis_match', [{
            identifier: 'accept',
            buttonTitle: 'Accept',
            options: {
              opensAppToForeground: true
            }
          }, {
            identifier: 'decline',
            buttonTitle: 'Decline',
            options: {
              opensAppToForeground: false
            }
          }]);
          cov_82mwvlolh().s[8]++;
          yield Notifications.setNotificationCategoryAsync('training_reminder', [{
            identifier: 'start_now',
            buttonTitle: 'Start Now',
            options: {
              opensAppToForeground: true
            }
          }, {
            identifier: 'remind_later',
            buttonTitle: 'Remind Later',
            options: {
              opensAppToForeground: false
            }
          }]);
        } catch (error) {
          cov_82mwvlolh().s[9]++;
          console.warn('Failed to initialize notifications:', error);
        }
      });
      function initializeNotifications() {
        return _initializeNotifications.apply(this, arguments);
      }
      return initializeNotifications;
    }())
  }, {
    key: "requestCameraPermissions",
    value: (function () {
      var _requestCameraPermissions = _asyncToGenerator(function* () {
        cov_82mwvlolh().f[3]++;
        cov_82mwvlolh().s[10]++;
        try {
          var cameraPermission = (cov_82mwvlolh().s[11]++, yield Camera.requestCameraPermissionsAsync());
          var microphonePermission = (cov_82mwvlolh().s[12]++, yield Camera.requestMicrophonePermissionsAsync());
          var mediaLibraryPermission = (cov_82mwvlolh().s[13]++, yield MediaLibrary.requestPermissionsAsync());
          var permissions = (cov_82mwvlolh().s[14]++, {
            camera: cameraPermission.status === 'granted',
            microphone: microphonePermission.status === 'granted',
            mediaLibrary: mediaLibraryPermission.status === 'granted'
          });
          var allGranted = (cov_82mwvlolh().s[15]++, Object.values(permissions).every(function (granted) {
            cov_82mwvlolh().f[4]++;
            cov_82mwvlolh().s[16]++;
            return granted;
          }));
          cov_82mwvlolh().s[17]++;
          if (!allGranted) {
            cov_82mwvlolh().b[0][0]++;
            cov_82mwvlolh().s[18]++;
            return {
              success: false,
              permissions: permissions,
              error: 'Some camera permissions were denied. Please enable them in settings.'
            };
          } else {
            cov_82mwvlolh().b[0][1]++;
          }
          cov_82mwvlolh().s[19]++;
          return {
            success: true,
            permissions: permissions
          };
        } catch (error) {
          var appError = (cov_82mwvlolh().s[20]++, handleError(error, {
            showAlert: false
          }));
          cov_82mwvlolh().s[21]++;
          return {
            success: false,
            permissions: {
              camera: false,
              microphone: false,
              mediaLibrary: false
            },
            error: appError.userMessage
          };
        }
      });
      function requestCameraPermissions() {
        return _requestCameraPermissions.apply(this, arguments);
      }
      return requestCameraPermissions;
    }())
  }, {
    key: "getCurrentLocation",
    value: (function () {
      var _getCurrentLocation = _asyncToGenerator(function* () {
        cov_82mwvlolh().f[5]++;
        cov_82mwvlolh().s[22]++;
        try {
          var _ref = (cov_82mwvlolh().s[23]++, yield Location.requestForegroundPermissionsAsync()),
            status = _ref.status;
          cov_82mwvlolh().s[24]++;
          if (status !== 'granted') {
            cov_82mwvlolh().b[1][0]++;
            cov_82mwvlolh().s[25]++;
            return {
              location: null,
              error: 'Location permission denied. Please enable location access in settings.'
            };
          } else {
            cov_82mwvlolh().b[1][1]++;
          }
          var location = (cov_82mwvlolh().s[26]++, yield Location.getCurrentPositionAsync({
            accuracy: Location.Accuracy.High,
            timeInterval: 5000,
            distanceInterval: 10
          }));
          cov_82mwvlolh().s[27]++;
          return {
            location: {
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              altitude: (cov_82mwvlolh().b[2][0]++, location.coords.altitude) || (cov_82mwvlolh().b[2][1]++, undefined),
              accuracy: (cov_82mwvlolh().b[3][0]++, location.coords.accuracy) || (cov_82mwvlolh().b[3][1]++, undefined),
              speed: (cov_82mwvlolh().b[4][0]++, location.coords.speed) || (cov_82mwvlolh().b[4][1]++, undefined),
              heading: (cov_82mwvlolh().b[5][0]++, location.coords.heading) || (cov_82mwvlolh().b[5][1]++, undefined),
              timestamp: location.timestamp
            }
          };
        } catch (error) {
          var appError = (cov_82mwvlolh().s[28]++, handleError(error, {
            showAlert: false
          }));
          cov_82mwvlolh().s[29]++;
          return {
            location: null,
            error: appError.userMessage
          };
        }
      });
      function getCurrentLocation() {
        return _getCurrentLocation.apply(this, arguments);
      }
      return getCurrentLocation;
    }())
  }, {
    key: "startLocationTracking",
    value: (function () {
      var _startLocationTracking = _asyncToGenerator(function* (onLocationUpdate) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_82mwvlolh().b[6][0]++, {});
        cov_82mwvlolh().f[6]++;
        cov_82mwvlolh().s[30]++;
        try {
          var _ref2 = (cov_82mwvlolh().s[31]++, yield Location.requestForegroundPermissionsAsync()),
            status = _ref2.status;
          cov_82mwvlolh().s[32]++;
          if (status !== 'granted') {
            cov_82mwvlolh().b[7][0]++;
            cov_82mwvlolh().s[33]++;
            return {
              success: false,
              error: 'Location permission denied'
            };
          } else {
            cov_82mwvlolh().b[7][1]++;
          }
          cov_82mwvlolh().s[34]++;
          this.locationSubscription = yield Location.watchPositionAsync({
            accuracy: (cov_82mwvlolh().b[8][0]++, options.accuracy) || (cov_82mwvlolh().b[8][1]++, Location.Accuracy.High),
            timeInterval: (cov_82mwvlolh().b[9][0]++, options.timeInterval) || (cov_82mwvlolh().b[9][1]++, 5000),
            distanceInterval: (cov_82mwvlolh().b[10][0]++, options.distanceInterval) || (cov_82mwvlolh().b[10][1]++, 10)
          }, function (location) {
            cov_82mwvlolh().f[7]++;
            cov_82mwvlolh().s[35]++;
            onLocationUpdate({
              latitude: location.coords.latitude,
              longitude: location.coords.longitude,
              altitude: (cov_82mwvlolh().b[11][0]++, location.coords.altitude) || (cov_82mwvlolh().b[11][1]++, undefined),
              accuracy: (cov_82mwvlolh().b[12][0]++, location.coords.accuracy) || (cov_82mwvlolh().b[12][1]++, undefined),
              speed: (cov_82mwvlolh().b[13][0]++, location.coords.speed) || (cov_82mwvlolh().b[13][1]++, undefined),
              heading: (cov_82mwvlolh().b[14][0]++, location.coords.heading) || (cov_82mwvlolh().b[14][1]++, undefined),
              timestamp: location.timestamp
            });
          });
          cov_82mwvlolh().s[36]++;
          return {
            success: true
          };
        } catch (error) {
          var appError = (cov_82mwvlolh().s[37]++, handleError(error, {
            showAlert: false
          }));
          cov_82mwvlolh().s[38]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function startLocationTracking(_x) {
        return _startLocationTracking.apply(this, arguments);
      }
      return startLocationTracking;
    }())
  }, {
    key: "stopLocationTracking",
    value: function stopLocationTracking() {
      cov_82mwvlolh().f[8]++;
      cov_82mwvlolh().s[39]++;
      if (this.locationSubscription) {
        cov_82mwvlolh().b[15][0]++;
        cov_82mwvlolh().s[40]++;
        this.locationSubscription.remove();
        cov_82mwvlolh().s[41]++;
        this.locationSubscription = null;
      } else {
        cov_82mwvlolh().b[15][1]++;
      }
    }
  }, {
    key: "requestNotificationPermissions",
    value: (function () {
      var _requestNotificationPermissions = _asyncToGenerator(function* () {
        cov_82mwvlolh().f[9]++;
        cov_82mwvlolh().s[42]++;
        try {
          cov_82mwvlolh().s[43]++;
          if (!Device.isDevice) {
            cov_82mwvlolh().b[16][0]++;
            cov_82mwvlolh().s[44]++;
            return {
              success: false,
              error: 'Push notifications are not supported on simulators'
            };
          } else {
            cov_82mwvlolh().b[16][1]++;
          }
          var _ref3 = (cov_82mwvlolh().s[45]++, yield Notifications.getPermissionsAsync()),
            existingStatus = _ref3.status;
          var finalStatus = (cov_82mwvlolh().s[46]++, existingStatus);
          cov_82mwvlolh().s[47]++;
          if (existingStatus !== 'granted') {
            cov_82mwvlolh().b[17][0]++;
            var _ref4 = (cov_82mwvlolh().s[48]++, yield Notifications.requestPermissionsAsync()),
              status = _ref4.status;
            cov_82mwvlolh().s[49]++;
            finalStatus = status;
          } else {
            cov_82mwvlolh().b[17][1]++;
          }
          cov_82mwvlolh().s[50]++;
          if (finalStatus !== 'granted') {
            cov_82mwvlolh().b[18][0]++;
            cov_82mwvlolh().s[51]++;
            return {
              success: false,
              error: 'Push notification permission denied'
            };
          } else {
            cov_82mwvlolh().b[18][1]++;
          }
          var token = (cov_82mwvlolh().s[52]++, yield Notifications.getExpoPushTokenAsync());
          cov_82mwvlolh().s[53]++;
          return {
            success: true,
            token: token.data
          };
        } catch (error) {
          var appError = (cov_82mwvlolh().s[54]++, handleError(error, {
            showAlert: false
          }));
          cov_82mwvlolh().s[55]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function requestNotificationPermissions() {
        return _requestNotificationPermissions.apply(this, arguments);
      }
      return requestNotificationPermissions;
    }())
  }, {
    key: "scheduleNotification",
    value: (function () {
      var _scheduleNotification = _asyncToGenerator(function* (notification, trigger) {
        cov_82mwvlolh().f[10]++;
        cov_82mwvlolh().s[56]++;
        try {
          var notificationId = (cov_82mwvlolh().s[57]++, yield Notifications.scheduleNotificationAsync({
            content: {
              title: notification.title,
              body: notification.body,
              data: (cov_82mwvlolh().b[19][0]++, notification.data) || (cov_82mwvlolh().b[19][1]++, {}),
              sound: notification.sound !== false,
              badge: notification.badge,
              categoryIdentifier: notification.categoryId
            },
            trigger: (cov_82mwvlolh().b[20][0]++, trigger) || (cov_82mwvlolh().b[20][1]++, null)
          }));
          cov_82mwvlolh().s[58]++;
          return {
            success: true,
            notificationId: notificationId
          };
        } catch (error) {
          var appError = (cov_82mwvlolh().s[59]++, handleError(error, {
            showAlert: false
          }));
          cov_82mwvlolh().s[60]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function scheduleNotification(_x2, _x3) {
        return _scheduleNotification.apply(this, arguments);
      }
      return scheduleNotification;
    }())
  }, {
    key: "cancelNotification",
    value: (function () {
      var _cancelNotification = _asyncToGenerator(function* (notificationId) {
        cov_82mwvlolh().f[11]++;
        cov_82mwvlolh().s[61]++;
        try {
          cov_82mwvlolh().s[62]++;
          yield Notifications.cancelScheduledNotificationAsync(notificationId);
          cov_82mwvlolh().s[63]++;
          return {
            success: true
          };
        } catch (error) {
          var appError = (cov_82mwvlolh().s[64]++, handleError(error, {
            showAlert: false
          }));
          cov_82mwvlolh().s[65]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function cancelNotification(_x4) {
        return _cancelNotification.apply(this, arguments);
      }
      return cancelNotification;
    }())
  }, {
    key: "setupNotificationListeners",
    value: function setupNotificationListeners(onNotificationReceived, onNotificationResponse) {
      cov_82mwvlolh().f[12]++;
      cov_82mwvlolh().s[66]++;
      this.notificationListener = Notifications.addNotificationReceivedListener(onNotificationReceived);
      cov_82mwvlolh().s[67]++;
      this.responseListener = Notifications.addNotificationResponseReceivedListener(onNotificationResponse);
    }
  }, {
    key: "removeNotificationListeners",
    value: function removeNotificationListeners() {
      cov_82mwvlolh().f[13]++;
      cov_82mwvlolh().s[68]++;
      if (this.notificationListener) {
        cov_82mwvlolh().b[21][0]++;
        cov_82mwvlolh().s[69]++;
        Notifications.removeNotificationSubscription(this.notificationListener);
        cov_82mwvlolh().s[70]++;
        this.notificationListener = null;
      } else {
        cov_82mwvlolh().b[21][1]++;
      }
      cov_82mwvlolh().s[71]++;
      if (this.responseListener) {
        cov_82mwvlolh().b[22][0]++;
        cov_82mwvlolh().s[72]++;
        Notifications.removeNotificationSubscription(this.responseListener);
        cov_82mwvlolh().s[73]++;
        this.responseListener = null;
      } else {
        cov_82mwvlolh().b[22][1]++;
      }
    }
  }, {
    key: "getBiometricInfo",
    value: (function () {
      var _getBiometricInfo = _asyncToGenerator(function* () {
        cov_82mwvlolh().f[14]++;
        cov_82mwvlolh().s[74]++;
        try {
          var isAvailable = (cov_82mwvlolh().s[75]++, yield LocalAuthentication.hasHardwareAsync());
          var supportedTypes = (cov_82mwvlolh().s[76]++, yield LocalAuthentication.supportedAuthenticationTypesAsync());
          var isEnrolled = (cov_82mwvlolh().s[77]++, yield LocalAuthentication.isEnrolledAsync());
          var securityLevel = (cov_82mwvlolh().s[78]++, yield LocalAuthentication.getEnrolledLevelAsync());
          var typeNames = (cov_82mwvlolh().s[79]++, supportedTypes.map(function (type) {
            cov_82mwvlolh().f[15]++;
            cov_82mwvlolh().s[80]++;
            switch (type) {
              case LocalAuthentication.AuthenticationType.FINGERPRINT:
                cov_82mwvlolh().b[23][0]++;
                cov_82mwvlolh().s[81]++;
                return 'fingerprint';
              case LocalAuthentication.AuthenticationType.FACIAL_RECOGNITION:
                cov_82mwvlolh().b[23][1]++;
                cov_82mwvlolh().s[82]++;
                return 'face';
              case LocalAuthentication.AuthenticationType.IRIS:
                cov_82mwvlolh().b[23][2]++;
                cov_82mwvlolh().s[83]++;
                return 'iris';
              default:
                cov_82mwvlolh().b[23][3]++;
                cov_82mwvlolh().s[84]++;
                return 'unknown';
            }
          }));
          cov_82mwvlolh().s[85]++;
          return {
            isAvailable: isAvailable,
            supportedTypes: typeNames,
            isEnrolled: isEnrolled,
            securityLevel: securityLevel === LocalAuthentication.SecurityLevel.BIOMETRIC ? (cov_82mwvlolh().b[24][0]++, 'biometric') : (cov_82mwvlolh().b[24][1]++, securityLevel === LocalAuthentication.SecurityLevel.SECRET ? (cov_82mwvlolh().b[25][0]++, 'passcode') : (cov_82mwvlolh().b[25][1]++, 'none'))
          };
        } catch (error) {
          cov_82mwvlolh().s[86]++;
          return {
            isAvailable: false,
            supportedTypes: [],
            isEnrolled: false,
            securityLevel: 'none'
          };
        }
      });
      function getBiometricInfo() {
        return _getBiometricInfo.apply(this, arguments);
      }
      return getBiometricInfo;
    }())
  }, {
    key: "authenticateWithBiometrics",
    value: (function () {
      var _authenticateWithBiometrics = _asyncToGenerator(function* () {
        var promptMessage = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_82mwvlolh().b[26][0]++, 'Authenticate to continue');
        cov_82mwvlolh().f[16]++;
        cov_82mwvlolh().s[87]++;
        try {
          var biometricInfo = (cov_82mwvlolh().s[88]++, yield this.getBiometricInfo());
          cov_82mwvlolh().s[89]++;
          if ((cov_82mwvlolh().b[28][0]++, !biometricInfo.isAvailable) || (cov_82mwvlolh().b[28][1]++, !biometricInfo.isEnrolled)) {
            cov_82mwvlolh().b[27][0]++;
            cov_82mwvlolh().s[90]++;
            return {
              success: false,
              error: 'Biometric authentication is not available or not set up'
            };
          } else {
            cov_82mwvlolh().b[27][1]++;
          }
          var result = (cov_82mwvlolh().s[91]++, yield LocalAuthentication.authenticateAsync({
            promptMessage: promptMessage,
            cancelLabel: 'Cancel',
            fallbackLabel: 'Use Passcode',
            disableDeviceFallback: false
          }));
          cov_82mwvlolh().s[92]++;
          if (result.success) {
            cov_82mwvlolh().b[29][0]++;
            cov_82mwvlolh().s[93]++;
            return {
              success: true
            };
          } else {
            cov_82mwvlolh().b[29][1]++;
            cov_82mwvlolh().s[94]++;
            return {
              success: false,
              error: (cov_82mwvlolh().b[30][0]++, result.error) || (cov_82mwvlolh().b[30][1]++, 'Authentication failed')
            };
          }
        } catch (error) {
          var appError = (cov_82mwvlolh().s[95]++, handleError(error, {
            showAlert: false
          }));
          cov_82mwvlolh().s[96]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function authenticateWithBiometrics() {
        return _authenticateWithBiometrics.apply(this, arguments);
      }
      return authenticateWithBiometrics;
    }())
  }, {
    key: "getDeviceInfo",
    value: (function () {
      var _getDeviceInfo = _asyncToGenerator(function* () {
        cov_82mwvlolh().f[17]++;
        cov_82mwvlolh().s[97]++;
        try {
          cov_82mwvlolh().s[98]++;
          return {
            platform: Platform.OS,
            osVersion: Platform.Version.toString(),
            deviceName: (cov_82mwvlolh().b[31][0]++, Device.deviceName) || (cov_82mwvlolh().b[31][1]++, undefined),
            modelName: (cov_82mwvlolh().b[32][0]++, Device.modelName) || (cov_82mwvlolh().b[32][1]++, undefined),
            brand: (cov_82mwvlolh().b[33][0]++, Device.brand) || (cov_82mwvlolh().b[33][1]++, undefined),
            manufacturer: (cov_82mwvlolh().b[34][0]++, Device.manufacturer) || (cov_82mwvlolh().b[34][1]++, undefined),
            isDevice: Device.isDevice,
            totalMemory: (cov_82mwvlolh().b[35][0]++, Device.totalMemory) || (cov_82mwvlolh().b[35][1]++, undefined)
          };
        } catch (error) {
          cov_82mwvlolh().s[99]++;
          return {
            platform: Platform.OS,
            osVersion: Platform.Version.toString(),
            isDevice: Device.isDevice
          };
        }
      });
      function getDeviceInfo() {
        return _getDeviceInfo.apply(this, arguments);
      }
      return getDeviceInfo;
    }())
  }, {
    key: "getBatteryInfo",
    value: (function () {
      var _getBatteryInfo = _asyncToGenerator(function* () {
        cov_82mwvlolh().f[18]++;
        cov_82mwvlolh().s[100]++;
        try {
          var batteryLevel = (cov_82mwvlolh().s[101]++, yield Battery.getBatteryLevelAsync());
          var batteryState = (cov_82mwvlolh().s[102]++, yield Battery.getBatteryStateAsync());
          var lowPowerMode = (cov_82mwvlolh().s[103]++, yield Battery.isLowPowerModeEnabledAsync());
          var stateMap = (cov_82mwvlolh().s[104]++, _defineProperty(_defineProperty(_defineProperty(_defineProperty({}, Battery.BatteryState.UNKNOWN, 'unknown'), Battery.BatteryState.UNPLUGGED, 'unplugged'), Battery.BatteryState.CHARGING, 'charging'), Battery.BatteryState.FULL, 'full'));
          cov_82mwvlolh().s[105]++;
          return {
            batteryLevel: batteryLevel,
            batteryState: (cov_82mwvlolh().b[36][0]++, stateMap[batteryState]) || (cov_82mwvlolh().b[36][1]++, 'unknown'),
            lowPowerMode: lowPowerMode
          };
        } catch (error) {
          cov_82mwvlolh().s[106]++;
          return {
            batteryLevel: 1,
            batteryState: 'unknown',
            lowPowerMode: false
          };
        }
      });
      function getBatteryInfo() {
        return _getBatteryInfo.apply(this, arguments);
      }
      return getBatteryInfo;
    }())
  }, {
    key: "getNetworkInfo",
    value: (function () {
      var _getNetworkInfo = _asyncToGenerator(function* () {
        cov_82mwvlolh().f[19]++;
        cov_82mwvlolh().s[107]++;
        try {
          var networkState = (cov_82mwvlolh().s[108]++, yield Network.getNetworkStateAsync());
          var typeMap = (cov_82mwvlolh().s[109]++, _defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty({}, Network.NetworkStateType.WIFI, 'wifi'), Network.NetworkStateType.CELLULAR, 'cellular'), Network.NetworkStateType.ETHERNET, 'ethernet'), Network.NetworkStateType.OTHER, 'other'), Network.NetworkStateType.UNKNOWN, 'unknown'), Network.NetworkStateType.NONE, 'unknown'));
          cov_82mwvlolh().s[110]++;
          return {
            isConnected: (cov_82mwvlolh().b[37][0]++, networkState.isConnected) || (cov_82mwvlolh().b[37][1]++, false),
            type: (cov_82mwvlolh().b[38][0]++, typeMap[networkState.type]) || (cov_82mwvlolh().b[38][1]++, 'unknown'),
            isInternetReachable: (cov_82mwvlolh().b[39][0]++, networkState.isInternetReachable) || (cov_82mwvlolh().b[39][1]++, false)
          };
        } catch (error) {
          cov_82mwvlolh().s[111]++;
          return {
            isConnected: false,
            type: 'unknown',
            isInternetReachable: false
          };
        }
      });
      function getNetworkInfo() {
        return _getNetworkInfo.apply(this, arguments);
      }
      return getNetworkInfo;
    }())
  }, {
    key: "openSettings",
    value: (function () {
      var _openSettings = _asyncToGenerator(function* (settingsType) {
        cov_82mwvlolh().f[20]++;
        cov_82mwvlolh().s[112]++;
        try {
          var url;
          cov_82mwvlolh().s[113]++;
          if (Platform.OS === 'ios') {
            cov_82mwvlolh().b[40][0]++;
            cov_82mwvlolh().s[114]++;
            switch (settingsType) {
              case 'app':
                cov_82mwvlolh().b[41][0]++;
                cov_82mwvlolh().s[115]++;
                url = 'app-settings:';
                cov_82mwvlolh().s[116]++;
                break;
              case 'location':
                cov_82mwvlolh().b[41][1]++;
                cov_82mwvlolh().s[117]++;
                url = 'App-Prefs:Privacy&path=LOCATION';
                cov_82mwvlolh().s[118]++;
                break;
              case 'notification':
                cov_82mwvlolh().b[41][2]++;
                cov_82mwvlolh().s[119]++;
                url = 'App-Prefs:NOTIFICATIONS_ID';
                cov_82mwvlolh().s[120]++;
                break;
              default:
                cov_82mwvlolh().b[41][3]++;
                cov_82mwvlolh().s[121]++;
                url = 'app-settings:';
            }
          } else {
            cov_82mwvlolh().b[40][1]++;
            cov_82mwvlolh().s[122]++;
            switch (settingsType) {
              case 'app':
                cov_82mwvlolh().b[42][0]++;
                cov_82mwvlolh().s[123]++;
                url = 'package:com.acemind.tennis';
                cov_82mwvlolh().s[124]++;
                break;
              case 'location':
                cov_82mwvlolh().b[42][1]++;
                cov_82mwvlolh().s[125]++;
                url = 'android.settings.LOCATION_SOURCE_SETTINGS';
                cov_82mwvlolh().s[126]++;
                break;
              case 'notification':
                cov_82mwvlolh().b[42][2]++;
                cov_82mwvlolh().s[127]++;
                url = 'android.settings.APP_NOTIFICATION_SETTINGS';
                cov_82mwvlolh().s[128]++;
                break;
              default:
                cov_82mwvlolh().b[42][3]++;
                cov_82mwvlolh().s[129]++;
                url = 'android.settings.APPLICATION_DETAILS_SETTINGS';
            }
          }
          var canOpen = (cov_82mwvlolh().s[130]++, yield Linking.canOpenURL(url));
          cov_82mwvlolh().s[131]++;
          if (canOpen) {
            cov_82mwvlolh().b[43][0]++;
            cov_82mwvlolh().s[132]++;
            yield Linking.openURL(url);
            cov_82mwvlolh().s[133]++;
            return {
              success: true
            };
          } else {
            cov_82mwvlolh().b[43][1]++;
            cov_82mwvlolh().s[134]++;
            return {
              success: false,
              error: 'Cannot open settings'
            };
          }
        } catch (error) {
          var appError = (cov_82mwvlolh().s[135]++, handleError(error, {
            showAlert: false
          }));
          cov_82mwvlolh().s[136]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function openSettings(_x5) {
        return _openSettings.apply(this, arguments);
      }
      return openSettings;
    }())
  }, {
    key: "cleanup",
    value: function cleanup() {
      cov_82mwvlolh().f[21]++;
      cov_82mwvlolh().s[137]++;
      this.stopLocationTracking();
      cov_82mwvlolh().s[138]++;
      this.removeNotificationListeners();
    }
  }]);
}();
export var mobileFeatureService = (cov_82mwvlolh().s[139]++, new MobileFeatureService());
export default mobileFeatureService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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