813d6a1031cde3eadd6993cbfa4ae77b
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.fileUploadService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _supabase = require("../../../lib/supabase");
var FileSystem = _interopRequireWildcard(require("expo-file-system"));
var _base64Arraybuffer = require("base64-arraybuffer");
var _performance = require("../../../utils/performance");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
var FileUploadService = function () {
  function FileUploadService() {
    (0, _classCallCheck2.default)(this, FileUploadService);
    this.DEFAULT_BUCKET = 'match-videos';
    this.MAX_FILE_SIZE = 100 * 1024 * 1024;
    this.SUPPORTED_VIDEO_TYPES = ['mp4', 'mov', 'avi'];
    this.SUPPORTED_IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'webp'];
  }
  return (0, _createClass2.default)(FileUploadService, [{
    key: "uploadVideo",
    value: (function () {
      var _uploadVideo = (0, _asyncToGenerator2.default)(function* (fileUri) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        try {
          _performance.performanceMonitor.start('video_upload');
          var uploadOptions = Object.assign({
            bucket: this.DEFAULT_BUCKET,
            folder: 'videos',
            contentType: 'video/mp4',
            maxSizeBytes: this.MAX_FILE_SIZE
          }, options);
          var validation = yield this.validateFile(fileUri, uploadOptions);
          if (validation.error) {
            return {
              data: null,
              error: validation.error
            };
          }
          var fileSize = validation.fileInfo.size || 0;
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: 0,
            total: fileSize,
            percentage: 0
          });
          var fileName = uploadOptions.fileName || this.generateFileName('mp4');
          var filePath = uploadOptions.folder ? `${uploadOptions.folder}/${fileName}` : fileName;
          var freeSpace = yield FileSystem.getFreeDiskStorageAsync();
          if (freeSpace < fileSize * 2) {
            return {
              data: null,
              error: 'Insufficient storage space for upload'
            };
          }
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize * 0.1,
            total: fileSize,
            percentage: 10
          });
          var fileBase64 = yield FileSystem.readAsStringAsync(fileUri, {
            encoding: FileSystem.EncodingType.Base64
          });
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize * 0.3,
            total: fileSize,
            percentage: 30
          });
          var fileArrayBuffer = (0, _base64Arraybuffer.decode)(fileBase64);
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize * 0.5,
            total: fileSize,
            percentage: 50
          });
          var uploadAttempts = 0;
          var maxAttempts = 3;
          var uploadError = null;
          var uploadData = null;
          while (uploadAttempts < maxAttempts) {
            try {
              var _yield$supabase$stora = yield _supabase.supabase.storage.from(uploadOptions.bucket).upload(filePath, fileArrayBuffer, {
                  contentType: uploadOptions.contentType,
                  upsert: false
                }),
                data = _yield$supabase$stora.data,
                error = _yield$supabase$stora.error;
              if (error) {
                uploadError = error;
                uploadAttempts++;
                if (uploadAttempts < maxAttempts) {
                  console.warn(`Upload attempt ${uploadAttempts} failed, retrying...`);
                  yield new Promise(function (resolve) {
                    return setTimeout(resolve, 1000 * uploadAttempts);
                  });
                  continue;
                }
              } else {
                uploadData = data;
                break;
              }
            } catch (error) {
              uploadError = error;
              uploadAttempts++;
              if (uploadAttempts < maxAttempts) {
                yield new Promise(function (resolve) {
                  return setTimeout(resolve, 1000 * uploadAttempts);
                });
              }
            }
          }
          if (uploadError || !uploadData) {
            var _uploadError;
            console.error('Error uploading video after retries:', uploadError);
            return {
              data: null,
              error: ((_uploadError = uploadError) == null ? void 0 : _uploadError.message) || 'Failed to upload video after multiple attempts'
            };
          }
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize * 0.9,
            total: fileSize,
            percentage: 90
          });
          var _supabase$storage$fro = _supabase.supabase.storage.from(uploadOptions.bucket).getPublicUrl(uploadData.path),
            urlData = _supabase$storage$fro.data;
          var result = {
            url: urlData.publicUrl,
            path: uploadData.path,
            size: fileSize,
            type: 'video'
          };
          uploadOptions.onProgress == null || uploadOptions.onProgress({
            loaded: fileSize,
            total: fileSize,
            percentage: 100
          });
          _performance.performanceMonitor.end('video_upload');
          return {
            data: result,
            error: null
          };
        } catch (error) {
          console.error('Error uploading video:', error);
          return {
            data: null,
            error: 'Failed to upload video'
          };
        }
      });
      function uploadVideo(_x) {
        return _uploadVideo.apply(this, arguments);
      }
      return uploadVideo;
    }())
  }, {
    key: "uploadImage",
    value: (function () {
      var _uploadImage = (0, _asyncToGenerator2.default)(function* (fileUri) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
        try {
          _performance.performanceMonitor.start('image_upload');
          var uploadOptions = Object.assign({
            bucket: this.DEFAULT_BUCKET,
            folder: 'images',
            contentType: 'image/jpeg',
            maxSizeBytes: 10 * 1024 * 1024
          }, options);
          var validation = yield this.validateFile(fileUri, uploadOptions);
          if (validation.error) {
            return {
              data: null,
              error: validation.error
            };
          }
          var fileName = uploadOptions.fileName || this.generateFileName('jpg');
          var filePath = uploadOptions.folder ? `${uploadOptions.folder}/${fileName}` : fileName;
          var fileBase64 = yield FileSystem.readAsStringAsync(fileUri, {
            encoding: FileSystem.EncodingType.Base64
          });
          var fileArrayBuffer = (0, _base64Arraybuffer.decode)(fileBase64);
          var _yield$supabase$stora2 = yield _supabase.supabase.storage.from(uploadOptions.bucket).upload(filePath, fileArrayBuffer, {
              contentType: uploadOptions.contentType,
              upsert: false
            }),
            data = _yield$supabase$stora2.data,
            error = _yield$supabase$stora2.error;
          if (error) {
            console.error('Error uploading image:', error);
            return {
              data: null,
              error: error.message
            };
          }
          var _supabase$storage$fro2 = _supabase.supabase.storage.from(uploadOptions.bucket).getPublicUrl(data.path),
            urlData = _supabase$storage$fro2.data;
          var result = {
            url: urlData.publicUrl,
            path: data.path,
            size: validation.fileInfo.size,
            type: 'image'
          };
          _performance.performanceMonitor.end('image_upload');
          return {
            data: result,
            error: null
          };
        } catch (error) {
          console.error('Error uploading image:', error);
          return {
            data: null,
            error: 'Failed to upload image'
          };
        }
      });
      function uploadImage(_x2) {
        return _uploadImage.apply(this, arguments);
      }
      return uploadImage;
    }())
  }, {
    key: "uploadThumbnail",
    value: (function () {
      var _uploadThumbnail = (0, _asyncToGenerator2.default)(function* (videoUri, thumbnailUri) {
        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
        try {
          var fileName = this.generateFileName('jpg');
          var uploadOptions = Object.assign({
            bucket: this.DEFAULT_BUCKET,
            folder: 'thumbnails',
            fileName: fileName,
            contentType: 'image/jpeg'
          }, options);
          return yield this.uploadImage(thumbnailUri, uploadOptions);
        } catch (error) {
          console.error('Error uploading thumbnail:', error);
          return {
            data: null,
            error: 'Failed to upload thumbnail'
          };
        }
      });
      function uploadThumbnail(_x3, _x4) {
        return _uploadThumbnail.apply(this, arguments);
      }
      return uploadThumbnail;
    }())
  }, {
    key: "deleteFile",
    value: (function () {
      var _deleteFile = (0, _asyncToGenerator2.default)(function* (filePath) {
        var bucket = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.DEFAULT_BUCKET;
        try {
          var _yield$supabase$stora3 = yield _supabase.supabase.storage.from(bucket).remove([filePath]),
            error = _yield$supabase$stora3.error;
          if (error) {
            console.error('Error deleting file:', error);
            return {
              error: error.message
            };
          }
          return {
            error: null
          };
        } catch (error) {
          console.error('Error deleting file:', error);
          return {
            error: 'Failed to delete file'
          };
        }
      });
      function deleteFile(_x5) {
        return _deleteFile.apply(this, arguments);
      }
      return deleteFile;
    }())
  }, {
    key: "getFileInfo",
    value: (function () {
      var _getFileInfo = (0, _asyncToGenerator2.default)(function* (filePath) {
        var bucket = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : this.DEFAULT_BUCKET;
        try {
          var _yield$supabase$stora4 = yield _supabase.supabase.storage.from(bucket).list(filePath),
            data = _yield$supabase$stora4.data,
            error = _yield$supabase$stora4.error;
          if (error) {
            console.error('Error getting file info:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error getting file info:', error);
          return {
            data: null,
            error: 'Failed to get file info'
          };
        }
      });
      function getFileInfo(_x6) {
        return _getFileInfo.apply(this, arguments);
      }
      return getFileInfo;
    }())
  }, {
    key: "createSignedUrl",
    value: (function () {
      var _createSignedUrl = (0, _asyncToGenerator2.default)(function* (filePath) {
        var expiresIn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 3600;
        var bucket = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : this.DEFAULT_BUCKET;
        try {
          var _yield$supabase$stora5 = yield _supabase.supabase.storage.from(bucket).createSignedUrl(filePath, expiresIn),
            data = _yield$supabase$stora5.data,
            error = _yield$supabase$stora5.error;
          if (error) {
            console.error('Error creating signed URL:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data.signedUrl,
            error: null
          };
        } catch (error) {
          console.error('Error creating signed URL:', error);
          return {
            data: null,
            error: 'Failed to create signed URL'
          };
        }
      });
      function createSignedUrl(_x7) {
        return _createSignedUrl.apply(this, arguments);
      }
      return createSignedUrl;
    }())
  }, {
    key: "validateFile",
    value: (function () {
      var _validateFile = (0, _asyncToGenerator2.default)(function* (fileUri, options) {
        try {
          var fileInfo = yield FileSystem.getInfoAsync(fileUri);
          if (!fileInfo.exists) {
            return {
              error: 'File does not exist'
            };
          }
          if (options.maxSizeBytes && fileInfo.size && fileInfo.size > options.maxSizeBytes) {
            var maxSizeMB = Math.round(options.maxSizeBytes / (1024 * 1024));
            return {
              error: `File size exceeds ${maxSizeMB}MB limit`
            };
          }
          var extension = this.getFileExtension(fileUri);
          var isVideo = this.SUPPORTED_VIDEO_TYPES.includes(extension.toLowerCase());
          var isImage = this.SUPPORTED_IMAGE_TYPES.includes(extension.toLowerCase());
          if (!isVideo && !isImage) {
            return {
              error: 'Unsupported file type'
            };
          }
          return {
            fileInfo: fileInfo
          };
        } catch (error) {
          console.error('Error validating file:', error);
          return {
            error: 'Failed to validate file'
          };
        }
      });
      function validateFile(_x8, _x9) {
        return _validateFile.apply(this, arguments);
      }
      return validateFile;
    }())
  }, {
    key: "generateFileName",
    value: function generateFileName(extension) {
      var timestamp = Date.now();
      var random = Math.random().toString(36).substring(2, 15);
      return `${timestamp}_${random}.${extension}`;
    }
  }, {
    key: "getFileExtension",
    value: function getFileExtension(uri) {
      var parts = uri.split('.');
      return parts[parts.length - 1] || '';
    }
  }, {
    key: "compressVideo",
    value: (function () {
      var _compressVideo = (0, _asyncToGenerator2.default)(function* (inputUri) {
        var quality = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'medium';
        try {
          console.log(`Video compression requested with quality: ${quality}`);
          return {
            data: inputUri,
            error: null
          };
        } catch (error) {
          console.error('Error compressing video:', error);
          return {
            data: null,
            error: 'Failed to compress video'
          };
        }
      });
      function compressVideo(_x0) {
        return _compressVideo.apply(this, arguments);
      }
      return compressVideo;
    }())
  }, {
    key: "generateVideoThumbnail",
    value: (function () {
      var _generateVideoThumbnail = (0, _asyncToGenerator2.default)(function* (videoUri) {
        var timeSeconds = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
        try {
          console.log(`Thumbnail generation requested for video at ${timeSeconds}s`);
          return {
            data: videoUri,
            error: null
          };
        } catch (error) {
          console.error('Error generating thumbnail:', error);
          return {
            data: null,
            error: 'Failed to generate thumbnail'
          };
        }
      });
      function generateVideoThumbnail(_x1) {
        return _generateVideoThumbnail.apply(this, arguments);
      }
      return generateVideoThumbnail;
    }())
  }, {
    key: "simulateProgress",
    value: function simulateProgress(onProgress) {
      var totalSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1000000;
      if (!onProgress) return;
      var loaded = 0;
      var interval = setInterval(function () {
        loaded += totalSize * 0.1;
        var percentage = Math.min(loaded / totalSize * 100, 100);
        onProgress({
          loaded: loaded,
          total: totalSize,
          percentage: percentage
        });
        if (percentage >= 100) {
          clearInterval(interval);
        }
      }, 200);
    }
  }]);
}();
var fileUploadService = exports.fileUploadService = new FileUploadService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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