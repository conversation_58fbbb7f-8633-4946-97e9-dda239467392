{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_UnimplementedView", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _UnimplementedView = _interopRequireDefault(require(\"../../modules/UnimplementedView\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nvar _default = exports.default = _UnimplementedView.default;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,kBAAkB,GAAGL,sBAAsB,CAACC,OAAO,kCAAkC,CAAC,CAAC;AAS3F,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,kBAAkB,CAACH,OAAO;AAC3DK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}