05d5b738d6e8110280b831b817e8bd0d
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_pgvyuyop5() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\components\\ai\\AIAnalysisResults.tsx";
  var hash = "b8ff227cc086b1896486642d40f6c2e6d3bfd1b8";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\components\\ai\\AIAnalysisResults.tsx",
    statementMap: {
      "0": {
        start: {
          line: 33,
          column: 18
        },
        end: {
          line: 33,
          column: 42
        }
      },
      "1": {
        start: {
          line: 35,
          column: 15
        },
        end: {
          line: 47,
          column: 1
        }
      },
      "2": {
        start: {
          line: 64,
          column: 50
        },
        end: {
          line: 64,
          column: 73
        }
      },
      "3": {
        start: {
          line: 65,
          column: 48
        },
        end: {
          line: 65,
          column: 63
        }
      },
      "4": {
        start: {
          line: 66,
          column: 50
        },
        end: {
          line: 66,
          column: 82
        }
      },
      "5": {
        start: {
          line: 68,
          column: 75
        },
        end: {
          line: 68,
          column: 89
        }
      },
      "6": {
        start: {
          line: 71,
          column: 27
        },
        end: {
          line: 73,
          column: 3
        }
      },
      "7": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 71
        }
      },
      "8": {
        start: {
          line: 75,
          column: 33
        },
        end: {
          line: 83,
          column: 3
        }
      },
      "9": {
        start: {
          line: 76,
          column: 24
        },
        end: {
          line: 76,
          column: 49
        }
      },
      "10": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 81,
          column: 5
        }
      },
      "11": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 36
        }
      },
      "12": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 80,
          column: 33
        }
      },
      "13": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 82,
          column: 37
        }
      },
      "14": {
        start: {
          line: 85,
          column: 26
        },
        end: {
          line: 93,
          column: 3
        }
      },
      "15": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 92,
          column: 5
        }
      },
      "16": {
        start: {
          line: 87,
          column: 24
        },
        end: {
          line: 87,
          column: 72
        }
      },
      "17": {
        start: {
          line: 88,
          column: 23
        },
        end: {
          line: 88,
          column: 72
        }
      },
      "18": {
        start: {
          line: 89,
          column: 22
        },
        end: {
          line: 89,
          column: 75
        }
      },
      "19": {
        start: {
          line: 90,
          column: 21
        },
        end: {
          line: 90,
          column: 70
        }
      },
      "20": {
        start: {
          line: 91,
          column: 15
        },
        end: {
          line: 91,
          column: 62
        }
      },
      "21": {
        start: {
          line: 95,
          column: 27
        },
        end: {
          line: 102,
          column: 3
        }
      },
      "22": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 101,
          column: 5
        }
      },
      "23": {
        start: {
          line: 97,
          column: 19
        },
        end: {
          line: 97,
          column: 37
        }
      },
      "24": {
        start: {
          line: 98,
          column: 21
        },
        end: {
          line: 98,
          column: 42
        }
      },
      "25": {
        start: {
          line: 99,
          column: 18
        },
        end: {
          line: 99,
          column: 38
        }
      },
      "26": {
        start: {
          line: 100,
          column: 15
        },
        end: {
          line: 100,
          column: 34
        }
      },
      "27": {
        start: {
          line: 104,
          column: 31
        },
        end: {
          line: 126,
          column: 3
        }
      },
      "28": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 125,
          column: 11
        }
      },
      "29": {
        start: {
          line: 128,
          column: 34
        },
        end: {
          line: 156,
          column: 3
        }
      },
      "30": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 155,
          column: 11
        }
      },
      "31": {
        start: {
          line: 158,
          column: 28
        },
        end: {
          line: 216,
          column: 3
        }
      },
      "32": {
        start: {
          line: 159,
          column: 23
        },
        end: {
          line: 159,
          column: 55
        }
      },
      "33": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 215,
          column: 6
        }
      },
      "34": {
        start: {
          line: 164,
          column: 25
        },
        end: {
          line: 164,
          column: 59
        }
      },
      "35": {
        start: {
          line: 189,
          column: 18
        },
        end: {
          line: 192,
          column: 25
        }
      },
      "36": {
        start: {
          line: 207,
          column: 31
        },
        end: {
          line: 207,
          column: 55
        }
      },
      "37": {
        start: {
          line: 218,
          column: 32
        },
        end: {
          line: 241,
          column: 3
        }
      },
      "38": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 240,
          column: 11
        }
      },
      "39": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 238,
          column: 27
        }
      },
      "40": {
        start: {
          line: 225,
          column: 25
        },
        end: {
          line: 225,
          column: 66
        }
      },
      "41": {
        start: {
          line: 243,
          column: 28
        },
        end: {
          line: 284,
          column: 3
        }
      },
      "42": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 283,
          column: 12
        }
      },
      "43": {
        start: {
          line: 248,
          column: 28
        },
        end: {
          line: 248,
          column: 53
        }
      },
      "44": {
        start: {
          line: 255,
          column: 12
        },
        end: {
          line: 272,
          column: 31
        }
      },
      "45": {
        start: {
          line: 262,
          column: 16
        },
        end: {
          line: 262,
          column: 46
        }
      },
      "46": {
        start: {
          line: 263,
          column: 16
        },
        end: {
          line: 263,
          column: 42
        }
      },
      "47": {
        start: {
          line: 277,
          column: 27
        },
        end: {
          line: 277,
          column: 52
        }
      },
      "48": {
        start: {
          line: 286,
          column: 2
        },
        end: {
          line: 361,
          column: 4
        }
      },
      "49": {
        start: {
          line: 292,
          column: 25
        },
        end: {
          line: 292,
          column: 49
        }
      },
      "50": {
        start: {
          line: 323,
          column: 10
        },
        end: {
          line: 325,
          column: 17
        }
      },
      "51": {
        start: {
          line: 347,
          column: 12
        },
        end: {
          line: 347,
          column: 75
        }
      },
      "52": {
        start: {
          line: 354,
          column: 12
        },
        end: {
          line: 354,
          column: 77
        }
      },
      "53": {
        start: {
          line: 364,
          column: 24
        },
        end: {
          line: 372,
          column: 1
        }
      },
      "54": {
        start: {
          line: 365,
          column: 2
        },
        end: {
          line: 371,
          column: 3
        }
      },
      "55": {
        start: {
          line: 366,
          column: 22
        },
        end: {
          line: 366,
          column: 42
        }
      },
      "56": {
        start: {
          line: 367,
          column: 17
        },
        end: {
          line: 367,
          column: 36
        }
      },
      "57": {
        start: {
          line: 368,
          column: 17
        },
        end: {
          line: 368,
          column: 38
        }
      },
      "58": {
        start: {
          line: 369,
          column: 17
        },
        end: {
          line: 369,
          column: 35
        }
      },
      "59": {
        start: {
          line: 370,
          column: 13
        },
        end: {
          line: 370,
          column: 32
        }
      },
      "60": {
        start: {
          line: 374,
          column: 15
        },
        end: {
          line: 662,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "AIAnalysisResults",
        decl: {
          start: {
            line: 57,
            column: 16
          },
          end: {
            line: 57,
            column: 33
          }
        },
        loc: {
          start: {
            line: 63,
            column: 27
          },
          end: {
            line: 362,
            column: 1
          }
        },
        line: 63
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 71,
            column: 59
          },
          end: {
            line: 71,
            column: 60
          }
        },
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 72,
            column: 71
          }
        },
        line: 72
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 75,
            column: 33
          },
          end: {
            line: 75,
            column: 34
          }
        },
        loc: {
          start: {
            line: 75,
            column: 56
          },
          end: {
            line: 83,
            column: 3
          }
        },
        line: 75
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 85,
            column: 26
          },
          end: {
            line: 85,
            column: 27
          }
        },
        loc: {
          start: {
            line: 85,
            column: 48
          },
          end: {
            line: 93,
            column: 3
          }
        },
        line: 85
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 95,
            column: 27
          },
          end: {
            line: 95,
            column: 28
          }
        },
        loc: {
          start: {
            line: 95,
            column: 49
          },
          end: {
            line: 102,
            column: 3
          }
        },
        line: 95
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 104,
            column: 31
          },
          end: {
            line: 104,
            column: 32
          }
        },
        loc: {
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 125,
            column: 11
          }
        },
        line: 105
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 128,
            column: 34
          },
          end: {
            line: 128,
            column: 35
          }
        },
        loc: {
          start: {
            line: 129,
            column: 4
          },
          end: {
            line: 155,
            column: 11
          }
        },
        line: 129
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 158,
            column: 28
          },
          end: {
            line: 158,
            column: 29
          }
        },
        loc: {
          start: {
            line: 158,
            column: 58
          },
          end: {
            line: 216,
            column: 3
          }
        },
        line: 158
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 164,
            column: 19
          },
          end: {
            line: 164,
            column: 20
          }
        },
        loc: {
          start: {
            line: 164,
            column: 25
          },
          end: {
            line: 164,
            column: 59
          }
        },
        line: 164
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 188,
            column: 45
          },
          end: {
            line: 188,
            column: 46
          }
        },
        loc: {
          start: {
            line: 189,
            column: 18
          },
          end: {
            line: 192,
            column: 25
          }
        },
        line: 189
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 207,
            column: 25
          },
          end: {
            line: 207,
            column: 26
          }
        },
        loc: {
          start: {
            line: 207,
            column: 31
          },
          end: {
            line: 207,
            column: 55
          }
        },
        line: 207
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 218,
            column: 32
          },
          end: {
            line: 218,
            column: 33
          }
        },
        loc: {
          start: {
            line: 219,
            column: 4
          },
          end: {
            line: 240,
            column: 11
          }
        },
        line: 219
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 221,
            column: 20
          },
          end: {
            line: 221,
            column: 21
          }
        },
        loc: {
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 238,
            column: 27
          }
        },
        line: 222
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 225,
            column: 19
          },
          end: {
            line: 225,
            column: 20
          }
        },
        loc: {
          start: {
            line: 225,
            column: 25
          },
          end: {
            line: 225,
            column: 66
          }
        },
        line: 225
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 243,
            column: 28
          },
          end: {
            line: 243,
            column: 29
          }
        },
        loc: {
          start: {
            line: 244,
            column: 4
          },
          end: {
            line: 283,
            column: 12
          }
        },
        line: 244
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 248,
            column: 22
          },
          end: {
            line: 248,
            column: 23
          }
        },
        loc: {
          start: {
            line: 248,
            column: 28
          },
          end: {
            line: 248,
            column: 53
          }
        },
        line: 248
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 254,
            column: 69
          },
          end: {
            line: 254,
            column: 70
          }
        },
        loc: {
          start: {
            line: 255,
            column: 12
          },
          end: {
            line: 272,
            column: 31
          }
        },
        line: 255
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 261,
            column: 23
          },
          end: {
            line: 261,
            column: 24
          }
        },
        loc: {
          start: {
            line: 261,
            column: 29
          },
          end: {
            line: 264,
            column: 15
          }
        },
        line: 261
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 277,
            column: 21
          },
          end: {
            line: 277,
            column: 22
          }
        },
        loc: {
          start: {
            line: 277,
            column: 27
          },
          end: {
            line: 277,
            column: 52
          }
        },
        line: 277
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 292,
            column: 19
          },
          end: {
            line: 292,
            column: 20
          }
        },
        loc: {
          start: {
            line: 292,
            column: 25
          },
          end: {
            line: 292,
            column: 49
          }
        },
        line: 292
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 322,
            column: 66
          },
          end: {
            line: 322,
            column: 67
          }
        },
        loc: {
          start: {
            line: 323,
            column: 10
          },
          end: {
            line: 325,
            column: 17
          }
        },
        line: 323
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 346,
            column: 58
          },
          end: {
            line: 346,
            column: 59
          }
        },
        loc: {
          start: {
            line: 347,
            column: 12
          },
          end: {
            line: 347,
            column: 75
          }
        },
        line: 347
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 353,
            column: 65
          },
          end: {
            line: 353,
            column: 66
          }
        },
        loc: {
          start: {
            line: 354,
            column: 12
          },
          end: {
            line: 354,
            column: 77
          }
        },
        line: 354
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 364,
            column: 24
          },
          end: {
            line: 364,
            column: 25
          }
        },
        loc: {
          start: {
            line: 364,
            column: 45
          },
          end: {
            line: 372,
            column: 1
          }
        },
        line: 364
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 72,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 72,
            column: 30
          }
        }, {
          start: {
            line: 72,
            column: 34
          },
          end: {
            line: 72,
            column: 71
          }
        }],
        line: 72
      },
      "1": {
        loc: {
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        }, {
          start: {
            line: 79,
            column: 11
          },
          end: {
            line: 81,
            column: 5
          }
        }],
        line: 77
      },
      "2": {
        loc: {
          start: {
            line: 86,
            column: 4
          },
          end: {
            line: 92,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 87,
            column: 6
          },
          end: {
            line: 87,
            column: 72
          }
        }, {
          start: {
            line: 88,
            column: 6
          },
          end: {
            line: 88,
            column: 72
          }
        }, {
          start: {
            line: 89,
            column: 6
          },
          end: {
            line: 89,
            column: 75
          }
        }, {
          start: {
            line: 90,
            column: 6
          },
          end: {
            line: 90,
            column: 70
          }
        }, {
          start: {
            line: 91,
            column: 6
          },
          end: {
            line: 91,
            column: 62
          }
        }],
        line: 86
      },
      "3": {
        loc: {
          start: {
            line: 96,
            column: 4
          },
          end: {
            line: 101,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 97,
            column: 6
          },
          end: {
            line: 97,
            column: 37
          }
        }, {
          start: {
            line: 98,
            column: 6
          },
          end: {
            line: 98,
            column: 42
          }
        }, {
          start: {
            line: 99,
            column: 6
          },
          end: {
            line: 99,
            column: 38
          }
        }, {
          start: {
            line: 100,
            column: 6
          },
          end: {
            line: 100,
            column: 34
          }
        }],
        line: 96
      },
      "4": {
        loc: {
          start: {
            line: 177,
            column: 36
          },
          end: {
            line: 177,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 36
          },
          end: {
            line: 177,
            column: 46
          }
        }, {
          start: {
            line: 177,
            column: 50
          },
          end: {
            line: 177,
            column: 72
          }
        }],
        line: 177
      },
      "5": {
        loc: {
          start: {
            line: 181,
            column: 9
          },
          end: {
            line: 213,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 181,
            column: 9
          },
          end: {
            line: 181,
            column: 19
          }
        }, {
          start: {
            line: 182,
            column: 10
          },
          end: {
            line: 212,
            column: 17
          }
        }],
        line: 181
      },
      "6": {
        loc: {
          start: {
            line: 185,
            column: 13
          },
          end: {
            line: 195,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 13
          },
          end: {
            line: 185,
            column: 47
          }
        }, {
          start: {
            line: 186,
            column: 14
          },
          end: {
            line: 194,
            column: 21
          }
        }],
        line: 185
      },
      "7": {
        loc: {
          start: {
            line: 204,
            column: 13
          },
          end: {
            line: 211,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 13
          },
          end: {
            line: 204,
            column: 28
          }
        }, {
          start: {
            line: 205,
            column: 14
          },
          end: {
            line: 210,
            column: 16
          }
        }],
        line: 204
      },
      "8": {
        loc: {
          start: {
            line: 259,
            column: 16
          },
          end: {
            line: 259,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 259,
            column: 16
          },
          end: {
            line: 259,
            column: 45
          }
        }, {
          start: {
            line: 259,
            column: 49
          },
          end: {
            line: 259,
            column: 76
          }
        }],
        line: 259
      },
      "9": {
        loc: {
          start: {
            line: 268,
            column: 16
          },
          end: {
            line: 268,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 268,
            column: 16
          },
          end: {
            line: 268,
            column: 45
          }
        }, {
          start: {
            line: 268,
            column: 49
          },
          end: {
            line: 268,
            column: 80
          }
        }],
        line: 268
      },
      "10": {
        loc: {
          start: {
            line: 298,
            column: 9
          },
          end: {
            line: 303,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 298,
            column: 9
          },
          end: {
            line: 298,
            column: 24
          }
        }, {
          start: {
            line: 299,
            column: 10
          },
          end: {
            line: 302,
            column: 29
          }
        }],
        line: 298
      },
      "11": {
        loc: {
          start: {
            line: 305,
            column: 9
          },
          end: {
            line: 310,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 305,
            column: 9
          },
          end: {
            line: 305,
            column: 25
          }
        }, {
          start: {
            line: 306,
            column: 10
          },
          end: {
            line: 309,
            column: 29
          }
        }],
        line: 305
      },
      "12": {
        loc: {
          start: {
            line: 365,
            column: 2
          },
          end: {
            line: 371,
            column: 3
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 366,
            column: 4
          },
          end: {
            line: 366,
            column: 42
          }
        }, {
          start: {
            line: 367,
            column: 4
          },
          end: {
            line: 367,
            column: 36
          }
        }, {
          start: {
            line: 368,
            column: 4
          },
          end: {
            line: 368,
            column: 38
          }
        }, {
          start: {
            line: 369,
            column: 4
          },
          end: {
            line: 369,
            column: 35
          }
        }, {
          start: {
            line: 370,
            column: 4
          },
          end: {
            line: 370,
            column: 32
          }
        }],
        line: 365
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0, 0, 0],
      "3": [0, 0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b8ff227cc086b1896486642d40f6c2e6d3bfd1b8"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_pgvyuyop5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_pgvyuyop5();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Dimensions, Modal } from 'react-native';
import { Brain, TrendingUp, Target, Award, PlayCircle, ChevronRight, Filter, Share, Download } from 'lucide-react-native';
import { Card } from "../../../components/ui/Card";
import { Button } from "../../../components/ui/Button";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var _ref = (cov_pgvyuyop5().s[0]++, Dimensions.get('window')),
  width = _ref.width;
var colors = (cov_pgvyuyop5().s[1]++, {
  primary: '#23ba16',
  secondary: '#1a5e1a',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  blue: '#3b82f6',
  yellow: '#eab308',
  green: '#10b981',
  purple: '#8b5cf6'
});
export function AIAnalysisResults(_ref2) {
  var analysisResult = _ref2.analysisResult,
    onSegmentSelect = _ref2.onSegmentSelect,
    onInsightSelect = _ref2.onInsightSelect,
    onShareAnalysis = _ref2.onShareAnalysis,
    onDownloadReport = _ref2.onDownloadReport;
  cov_pgvyuyop5().f[0]++;
  var _ref3 = (cov_pgvyuyop5().s[2]++, useState('all')),
    _ref4 = _slicedToArray(_ref3, 2),
    selectedCategory = _ref4[0],
    setSelectedCategory = _ref4[1];
  var _ref5 = (cov_pgvyuyop5().s[3]++, useState(false)),
    _ref6 = _slicedToArray(_ref5, 2),
    showFilterModal = _ref6[0],
    setShowFilterModal = _ref6[1];
  var _ref7 = (cov_pgvyuyop5().s[4]++, useState(new Set())),
    _ref8 = _slicedToArray(_ref7, 2),
    expandedInsights = _ref8[0],
    setExpandedInsights = _ref8[1];
  var _ref9 = (cov_pgvyuyop5().s[5]++, analysisResult),
    overallAnalysis = _ref9.overallAnalysis,
    segments = _ref9.segments,
    processingMetrics = _ref9.processingMetrics,
    qualityMetrics = _ref9.qualityMetrics;
  var filteredInsights = (cov_pgvyuyop5().s[6]++, overallAnalysis.insights.filter(function (insight) {
    cov_pgvyuyop5().f[1]++;
    cov_pgvyuyop5().s[7]++;
    return (cov_pgvyuyop5().b[0][0]++, selectedCategory === 'all') || (cov_pgvyuyop5().b[0][1]++, insight.category === selectedCategory);
  }));
  cov_pgvyuyop5().s[8]++;
  var toggleInsightExpansion = function toggleInsightExpansion(insightId) {
    cov_pgvyuyop5().f[2]++;
    var newExpanded = (cov_pgvyuyop5().s[9]++, new Set(expandedInsights));
    cov_pgvyuyop5().s[10]++;
    if (newExpanded.has(insightId)) {
      cov_pgvyuyop5().b[1][0]++;
      cov_pgvyuyop5().s[11]++;
      newExpanded.delete(insightId);
    } else {
      cov_pgvyuyop5().b[1][1]++;
      cov_pgvyuyop5().s[12]++;
      newExpanded.add(insightId);
    }
    cov_pgvyuyop5().s[13]++;
    setExpandedInsights(newExpanded);
  };
  cov_pgvyuyop5().s[14]++;
  var getCategoryIcon = function getCategoryIcon(category) {
    cov_pgvyuyop5().f[3]++;
    cov_pgvyuyop5().s[15]++;
    switch (category) {
      case 'technique':
        cov_pgvyuyop5().b[2][0]++;
        cov_pgvyuyop5().s[16]++;
        return _jsx(Target, {
          size: 16,
          color: colors.blue
        });
      case 'strategy':
        cov_pgvyuyop5().b[2][1]++;
        cov_pgvyuyop5().s[17]++;
        return _jsx(Brain, {
          size: 16,
          color: colors.purple
        });
      case 'fitness':
        cov_pgvyuyop5().b[2][2]++;
        cov_pgvyuyop5().s[18]++;
        return _jsx(TrendingUp, {
          size: 16,
          color: colors.green
        });
      case 'mental':
        cov_pgvyuyop5().b[2][3]++;
        cov_pgvyuyop5().s[19]++;
        return _jsx(Award, {
          size: 16,
          color: colors.yellow
        });
      default:
        cov_pgvyuyop5().b[2][4]++;
        cov_pgvyuyop5().s[20]++;
        return _jsx(Brain, {
          size: 16,
          color: colors.gray
        });
    }
  };
  cov_pgvyuyop5().s[21]++;
  var getPriorityColor = function getPriorityColor(priority) {
    cov_pgvyuyop5().f[4]++;
    cov_pgvyuyop5().s[22]++;
    switch (priority) {
      case 'high':
        cov_pgvyuyop5().b[3][0]++;
        cov_pgvyuyop5().s[23]++;
        return colors.red;
      case 'medium':
        cov_pgvyuyop5().b[3][1]++;
        cov_pgvyuyop5().s[24]++;
        return colors.yellow;
      case 'low':
        cov_pgvyuyop5().b[3][2]++;
        cov_pgvyuyop5().s[25]++;
        return colors.green;
      default:
        cov_pgvyuyop5().b[3][3]++;
        cov_pgvyuyop5().s[26]++;
        return colors.gray;
    }
  };
  cov_pgvyuyop5().s[27]++;
  var renderOverallMetrics = function renderOverallMetrics() {
    cov_pgvyuyop5().f[5]++;
    cov_pgvyuyop5().s[28]++;
    return _jsxs(Card, {
      style: styles.metricsCard,
      children: [_jsx(Text, {
        style: styles.sectionTitle,
        children: "Performance Overview"
      }), _jsxs(View, {
        style: styles.metricsGrid,
        children: [_jsxs(View, {
          style: styles.metricItem,
          children: [_jsx(Text, {
            style: styles.metricValue,
            children: overallAnalysis.technicalMetrics.techniqueScore
          }), _jsx(Text, {
            style: styles.metricLabel,
            children: "Technique"
          })]
        }), _jsxs(View, {
          style: styles.metricItem,
          children: [_jsx(Text, {
            style: styles.metricValue,
            children: overallAnalysis.technicalMetrics.consistencyScore
          }), _jsx(Text, {
            style: styles.metricLabel,
            children: "Consistency"
          })]
        }), _jsxs(View, {
          style: styles.metricItem,
          children: [_jsx(Text, {
            style: styles.metricValue,
            children: overallAnalysis.technicalMetrics.powerScore
          }), _jsx(Text, {
            style: styles.metricLabel,
            children: "Power"
          })]
        }), _jsxs(View, {
          style: styles.metricItem,
          children: [_jsx(Text, {
            style: styles.metricValue,
            children: overallAnalysis.technicalMetrics.accuracyScore
          }), _jsx(Text, {
            style: styles.metricLabel,
            children: "Accuracy"
          })]
        })]
      })]
    });
  };
  cov_pgvyuyop5().s[29]++;
  var renderProcessingMetrics = function renderProcessingMetrics() {
    cov_pgvyuyop5().f[6]++;
    cov_pgvyuyop5().s[30]++;
    return _jsxs(Card, {
      style: styles.processingCard,
      children: [_jsx(Text, {
        style: styles.sectionTitle,
        children: "Analysis Quality"
      }), _jsxs(View, {
        style: styles.qualityGrid,
        children: [_jsxs(View, {
          style: styles.qualityItem,
          children: [_jsx(Text, {
            style: styles.qualityLabel,
            children: "Video Quality"
          }), _jsx(Text, {
            style: [styles.qualityValue, {
              color: getQualityColor(qualityMetrics.videoQuality)
            }],
            children: qualityMetrics.videoQuality.toUpperCase()
          })]
        }), _jsxs(View, {
          style: styles.qualityItem,
          children: [_jsx(Text, {
            style: styles.qualityLabel,
            children: "Pose Detection"
          }), _jsxs(Text, {
            style: styles.qualityValue,
            children: [Math.round(processingMetrics.poseDetectionAccuracy * 100), "%"]
          })]
        }), _jsxs(View, {
          style: styles.qualityItem,
          children: [_jsx(Text, {
            style: styles.qualityLabel,
            children: "Frames Analyzed"
          }), _jsx(Text, {
            style: styles.qualityValue,
            children: processingMetrics.processedFrames
          })]
        }), _jsxs(View, {
          style: styles.qualityItem,
          children: [_jsx(Text, {
            style: styles.qualityLabel,
            children: "Processing Time"
          }), _jsxs(Text, {
            style: styles.qualityValue,
            children: [Math.round(processingMetrics.totalProcessingTime / 1000), "s"]
          })]
        })]
      })]
    });
  };
  cov_pgvyuyop5().s[31]++;
  var renderInsightCard = function renderInsightCard(insight) {
    cov_pgvyuyop5().f[7]++;
    var isExpanded = (cov_pgvyuyop5().s[32]++, expandedInsights.has(insight.id));
    cov_pgvyuyop5().s[33]++;
    return _jsxs(Card, {
      style: styles.insightCard,
      children: [_jsxs(TouchableOpacity, {
        onPress: function onPress() {
          cov_pgvyuyop5().f[8]++;
          cov_pgvyuyop5().s[34]++;
          return toggleInsightExpansion(insight.id);
        },
        style: styles.insightHeader,
        children: [_jsxs(View, {
          style: styles.insightTitleRow,
          children: [getCategoryIcon(insight.category), _jsx(Text, {
            style: styles.insightTitle,
            children: insight.title
          }), _jsx(View, {
            style: [styles.priorityBadge, {
              backgroundColor: getPriorityColor(insight.priority)
            }],
            children: _jsx(Text, {
              style: styles.priorityText,
              children: insight.priority.toUpperCase()
            })
          })]
        }), _jsx(ChevronRight, {
          size: 20,
          color: colors.gray,
          style: [styles.chevron, (cov_pgvyuyop5().b[4][0]++, isExpanded) && (cov_pgvyuyop5().b[4][1]++, styles.chevronExpanded)]
        })]
      }), (cov_pgvyuyop5().b[5][0]++, isExpanded) && (cov_pgvyuyop5().b[5][1]++, _jsxs(View, {
        style: styles.insightContent,
        children: [_jsx(Text, {
          style: styles.insightDescription,
          children: insight.description
        }), (cov_pgvyuyop5().b[6][0]++, insight.actionableSteps.length > 0) && (cov_pgvyuyop5().b[6][1]++, _jsxs(View, {
          style: styles.actionStepsSection,
          children: [_jsx(Text, {
            style: styles.subsectionTitle,
            children: "Action Steps:"
          }), insight.actionableSteps.map(function (step, index) {
            cov_pgvyuyop5().f[9]++;
            cov_pgvyuyop5().s[35]++;
            return _jsxs(View, {
              style: styles.actionStep,
              children: [_jsx(Text, {
                style: styles.stepNumber,
                children: index + 1
              }), _jsx(Text, {
                style: styles.stepText,
                children: step
              })]
            }, index);
          })]
        })), _jsxs(View, {
          style: styles.insightFooter,
          children: [_jsxs(Text, {
            style: styles.timeframe,
            children: ["Expected improvement: ", insight.timeframe]
          }), _jsxs(Text, {
            style: styles.confidence,
            children: ["Confidence: ", Math.round(insight.confidence * 100), "%"]
          })]
        }), (cov_pgvyuyop5().b[7][0]++, onInsightSelect) && (cov_pgvyuyop5().b[7][1]++, _jsx(Button, {
          title: "View Details",
          onPress: function onPress() {
            cov_pgvyuyop5().f[10]++;
            cov_pgvyuyop5().s[36]++;
            return onInsightSelect(insight);
          },
          variant: "outline",
          style: styles.detailsButton
        }))]
      }))]
    }, insight.id);
  };
  cov_pgvyuyop5().s[37]++;
  var renderSegmentAnalysis = function renderSegmentAnalysis() {
    cov_pgvyuyop5().f[11]++;
    cov_pgvyuyop5().s[38]++;
    return _jsxs(Card, {
      style: styles.segmentsCard,
      children: [_jsx(Text, {
        style: styles.sectionTitle,
        children: "Video Segments"
      }), segments.map(function (segmentData, index) {
        cov_pgvyuyop5().f[12]++;
        cov_pgvyuyop5().s[39]++;
        return _jsxs(TouchableOpacity, {
          style: styles.segmentItem,
          onPress: function onPress() {
            cov_pgvyuyop5().f[13]++;
            cov_pgvyuyop5().s[40]++;
            return onSegmentSelect == null ? void 0 : onSegmentSelect(segmentData.segment.id);
          },
          children: [_jsxs(View, {
            style: styles.segmentHeader,
            children: [_jsx(PlayCircle, {
              size: 20,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.segmentTitle,
              children: segmentData.segment.description
            }), _jsxs(Text, {
              style: styles.segmentDuration,
              children: [Math.round(segmentData.segment.endTime - segmentData.segment.startTime), "s"]
            })]
          }), _jsxs(Text, {
            style: styles.segmentSummary,
            children: [segmentData.movementAnalyses.length, " movements analyzed \u2022", segmentData.coachingAnalysis.insights.length, " insights generated"]
          })]
        }, segmentData.segment.id);
      })]
    });
  };
  cov_pgvyuyop5().s[41]++;
  var renderFilterModal = function renderFilterModal() {
    cov_pgvyuyop5().f[14]++;
    cov_pgvyuyop5().s[42]++;
    return _jsx(Modal, {
      visible: showFilterModal,
      transparent: true,
      animationType: "slide",
      onRequestClose: function onRequestClose() {
        cov_pgvyuyop5().f[15]++;
        cov_pgvyuyop5().s[43]++;
        return setShowFilterModal(false);
      },
      children: _jsx(View, {
        style: styles.modalOverlay,
        children: _jsxs(View, {
          style: styles.filterModal,
          children: [_jsx(Text, {
            style: styles.modalTitle,
            children: "Filter Insights"
          }), ['all', 'technique', 'strategy', 'fitness', 'mental'].map(function (category) {
            cov_pgvyuyop5().f[16]++;
            cov_pgvyuyop5().s[44]++;
            return _jsx(TouchableOpacity, {
              style: [styles.filterOption, (cov_pgvyuyop5().b[8][0]++, selectedCategory === category) && (cov_pgvyuyop5().b[8][1]++, styles.filterOptionSelected)],
              onPress: function onPress() {
                cov_pgvyuyop5().f[17]++;
                cov_pgvyuyop5().s[45]++;
                setSelectedCategory(category);
                cov_pgvyuyop5().s[46]++;
                setShowFilterModal(false);
              },
              children: _jsx(Text, {
                style: [styles.filterOptionText, (cov_pgvyuyop5().b[9][0]++, selectedCategory === category) && (cov_pgvyuyop5().b[9][1]++, styles.filterOptionTextSelected)],
                children: category.charAt(0).toUpperCase() + category.slice(1)
              })
            }, category);
          }), _jsx(Button, {
            title: "Close",
            onPress: function onPress() {
              cov_pgvyuyop5().f[18]++;
              cov_pgvyuyop5().s[47]++;
              return setShowFilterModal(false);
            },
            variant: "outline",
            style: styles.closeButton
          })]
        })
      })
    });
  };
  cov_pgvyuyop5().s[48]++;
  return _jsxs(ScrollView, {
    style: styles.container,
    showsVerticalScrollIndicator: false,
    children: [_jsxs(View, {
      style: styles.headerActions,
      children: [_jsxs(TouchableOpacity, {
        style: styles.actionButton,
        onPress: function onPress() {
          cov_pgvyuyop5().f[19]++;
          cov_pgvyuyop5().s[49]++;
          return setShowFilterModal(true);
        },
        children: [_jsx(Filter, {
          size: 20,
          color: colors.primary
        }), _jsx(Text, {
          style: styles.actionButtonText,
          children: "Filter"
        })]
      }), (cov_pgvyuyop5().b[10][0]++, onShareAnalysis) && (cov_pgvyuyop5().b[10][1]++, _jsxs(TouchableOpacity, {
        style: styles.actionButton,
        onPress: onShareAnalysis,
        children: [_jsx(Share, {
          size: 20,
          color: colors.primary
        }), _jsx(Text, {
          style: styles.actionButtonText,
          children: "Share"
        })]
      })), (cov_pgvyuyop5().b[11][0]++, onDownloadReport) && (cov_pgvyuyop5().b[11][1]++, _jsxs(TouchableOpacity, {
        style: styles.actionButton,
        onPress: onDownloadReport,
        children: [_jsx(Download, {
          size: 20,
          color: colors.primary
        }), _jsx(Text, {
          style: styles.actionButtonText,
          children: "Report"
        })]
      }))]
    }), renderOverallMetrics(), renderProcessingMetrics(), _jsxs(Card, {
      style: styles.recommendationsCard,
      children: [_jsx(Text, {
        style: styles.sectionTitle,
        children: "Key Recommendations"
      }), overallAnalysis.overallAssessment.keyRecommendations.map(function (recommendation, index) {
        cov_pgvyuyop5().f[20]++;
        cov_pgvyuyop5().s[50]++;
        return _jsx(View, {
          style: styles.recommendationItem,
          children: _jsxs(Text, {
            style: styles.recommendationText,
            children: ["\u2022 ", recommendation]
          })
        }, index);
      })]
    }), _jsxs(View, {
      style: styles.insightsSection,
      children: [_jsxs(Text, {
        style: styles.sectionTitle,
        children: ["Coaching Insights (", filteredInsights.length, ")"]
      }), filteredInsights.map(renderInsightCard)]
    }), renderSegmentAnalysis(), _jsxs(Card, {
      style: styles.nextStepsCard,
      children: [_jsx(Text, {
        style: styles.sectionTitle,
        children: "Next Steps"
      }), _jsxs(View, {
        style: styles.nextStepsSection,
        children: [_jsx(Text, {
          style: styles.subsectionTitle,
          children: "Immediate Actions:"
        }), overallAnalysis.nextSteps.immediateActions.map(function (action, index) {
          cov_pgvyuyop5().f[21]++;
          cov_pgvyuyop5().s[51]++;
          return _jsxs(Text, {
            style: styles.nextStepItem,
            children: ["\u2022 ", action]
          }, index);
        })]
      }), _jsxs(View, {
        style: styles.nextStepsSection,
        children: [_jsx(Text, {
          style: styles.subsectionTitle,
          children: "Practice Recommendations:"
        }), overallAnalysis.nextSteps.practiceRecommendations.map(function (practice, index) {
          cov_pgvyuyop5().f[22]++;
          cov_pgvyuyop5().s[52]++;
          return _jsxs(Text, {
            style: styles.nextStepItem,
            children: ["\u2022 ", practice]
          }, index);
        })]
      })]
    }), renderFilterModal()]
  });
}
cov_pgvyuyop5().s[53]++;
var getQualityColor = function getQualityColor(quality) {
  cov_pgvyuyop5().f[23]++;
  cov_pgvyuyop5().s[54]++;
  switch (quality) {
    case 'excellent':
      cov_pgvyuyop5().b[12][0]++;
      cov_pgvyuyop5().s[55]++;
      return colors.green;
    case 'good':
      cov_pgvyuyop5().b[12][1]++;
      cov_pgvyuyop5().s[56]++;
      return colors.blue;
    case 'fair':
      cov_pgvyuyop5().b[12][2]++;
      cov_pgvyuyop5().s[57]++;
      return colors.yellow;
    case 'poor':
      cov_pgvyuyop5().b[12][3]++;
      cov_pgvyuyop5().s[58]++;
      return colors.red;
    default:
      cov_pgvyuyop5().b[12][4]++;
      cov_pgvyuyop5().s[59]++;
      return colors.gray;
  }
};
var styles = (cov_pgvyuyop5().s[60]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray,
    padding: 16
  },
  headerActions: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 16
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: colors.white,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.primary,
    gap: 4
  },
  actionButtonText: {
    color: colors.primary,
    fontSize: 14,
    fontWeight: '500'
  },
  metricsCard: {
    marginBottom: 16,
    padding: 20
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 16
  },
  metricsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  metricItem: {
    alignItems: 'center',
    flex: 1
  },
  metricValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary,
    marginBottom: 4
  },
  metricLabel: {
    fontSize: 12,
    color: colors.gray,
    textAlign: 'center'
  },
  processingCard: {
    marginBottom: 16,
    padding: 16
  },
  qualityGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between'
  },
  qualityItem: {
    width: '48%',
    marginBottom: 12
  },
  qualityLabel: {
    fontSize: 12,
    color: colors.gray,
    marginBottom: 4
  },
  qualityValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.dark
  },
  recommendationsCard: {
    marginBottom: 16,
    padding: 16
  },
  recommendationItem: {
    marginBottom: 8
  },
  recommendationText: {
    fontSize: 14,
    color: colors.dark,
    lineHeight: 20
  },
  insightsSection: {
    marginBottom: 16
  },
  insightCard: {
    marginBottom: 12,
    overflow: 'hidden'
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16
  },
  insightTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 8
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.dark,
    flex: 1
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10
  },
  priorityText: {
    fontSize: 10,
    fontWeight: '600',
    color: colors.white
  },
  chevron: {
    transform: [{
      rotate: '0deg'
    }]
  },
  chevronExpanded: {
    transform: [{
      rotate: '90deg'
    }]
  },
  insightContent: {
    paddingHorizontal: 16,
    paddingBottom: 16
  },
  insightDescription: {
    fontSize: 14,
    color: colors.dark,
    lineHeight: 20,
    marginBottom: 12
  },
  actionStepsSection: {
    marginBottom: 12
  },
  subsectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 8
  },
  actionStep: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 6,
    gap: 8
  },
  stepNumber: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.primary,
    backgroundColor: colors.lightGray,
    borderRadius: 10,
    width: 20,
    height: 20,
    textAlign: 'center',
    lineHeight: 20
  },
  stepText: {
    fontSize: 14,
    color: colors.dark,
    flex: 1,
    lineHeight: 18
  },
  insightFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12
  },
  timeframe: {
    fontSize: 12,
    color: colors.gray
  },
  confidence: {
    fontSize: 12,
    color: colors.gray
  },
  detailsButton: {
    marginTop: 8
  },
  segmentsCard: {
    marginBottom: 16,
    padding: 16
  },
  segmentItem: {
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb'
  },
  segmentHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4
  },
  segmentTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.dark,
    flex: 1
  },
  segmentDuration: {
    fontSize: 12,
    color: colors.gray
  },
  segmentSummary: {
    fontSize: 12,
    color: colors.gray,
    marginLeft: 28
  },
  nextStepsCard: {
    marginBottom: 16,
    padding: 16
  },
  nextStepsSection: {
    marginBottom: 16
  },
  nextStepItem: {
    fontSize: 14,
    color: colors.dark,
    lineHeight: 20,
    marginBottom: 4
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  filterModal: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 20,
    width: width * 0.8,
    maxWidth: 300
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 16,
    textAlign: 'center'
  },
  filterOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8
  },
  filterOptionSelected: {
    backgroundColor: colors.primary
  },
  filterOptionText: {
    fontSize: 16,
    color: colors.dark,
    textAlign: 'center'
  },
  filterOptionTextSelected: {
    color: colors.white,
    fontWeight: '500'
  },
  closeButton: {
    marginTop: 16
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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