{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_interopRequireDefault", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_View", "_excluded", "KeyboardAvoidingView", "_React$Component", "_this", "arguments", "frame", "onLayout", "event", "nativeEvent", "layout", "key", "value", "relativeKeyboardHeight", "keyboardFrame", "keyboardY", "screenY", "props", "keyboardVerticalOffset", "Math", "max", "y", "height", "onKeyboardChange", "render", "_this$props", "behavior", "contentContainerStyle", "rest", "createElement", "Component", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _excluded = [\"behavior\", \"contentContainerStyle\", \"keyboardVerticalOffset\"];\nclass KeyboardAvoidingView extends React.Component {\n  constructor() {\n    super(...arguments);\n    this.frame = null;\n    this.onLayout = event => {\n      this.frame = event.nativeEvent.layout;\n    };\n  }\n  relativeKeyboardHeight(keyboardFrame) {\n    var frame = this.frame;\n    if (!frame || !keyboardFrame) {\n      return 0;\n    }\n    var keyboardY = keyboardFrame.screenY - (this.props.keyboardVerticalOffset || 0);\n    return Math.max(frame.y + frame.height - keyboardY, 0);\n  }\n  onKeyboardChange(event) {}\n  render() {\n    var _this$props = this.props,\n      behavior = _this$props.behavior,\n      contentContainerStyle = _this$props.contentContainerStyle,\n      keyboardVerticalOffset = _this$props.keyboardVerticalOffset,\n      rest = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);\n    return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({\n      onLayout: this.onLayout\n    }, rest));\n  }\n}\nvar _default = exports.default = KeyboardAvoidingView;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,SAAAM,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAM,OAAA,EAAAF,CAAA,OAAAL,2BAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAM,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEb,IAAIa,sBAAsB,GAAGpB,OAAO,CAAC,8CAA8C,CAAC,CAACU,OAAO;AAC5F,IAAIW,uBAAuB,GAAGrB,OAAO,CAAC,+CAA+C,CAAC,CAACU,OAAO;AAC9FY,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACZ,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIc,SAAS,GAAGJ,sBAAsB,CAACpB,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIyB,8BAA8B,GAAGL,sBAAsB,CAACpB,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAI0B,KAAK,GAAGL,uBAAuB,CAACrB,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAI2B,KAAK,GAAGP,sBAAsB,CAACpB,OAAO,UAAU,CAAC,CAAC;AACtD,IAAI4B,SAAS,GAAG,CAAC,UAAU,EAAE,uBAAuB,EAAE,wBAAwB,CAAC;AAAC,IAC1EC,oBAAoB,aAAAC,gBAAA;EACxB,SAAAD,qBAAA,EAAc;IAAA,IAAAE,KAAA;IAAA,IAAA9B,gBAAA,CAAAS,OAAA,QAAAmB,oBAAA;IACZE,KAAA,GAAAzB,UAAA,OAAAuB,oBAAA,EAASG,SAAS;IAClBD,KAAA,CAAKE,KAAK,GAAG,IAAI;IACjBF,KAAA,CAAKG,QAAQ,GAAG,UAAAC,KAAK,EAAI;MACvBJ,KAAA,CAAKE,KAAK,GAAGE,KAAK,CAACC,WAAW,CAACC,MAAM;IACvC,CAAC;IAAC,OAAAN,KAAA;EACJ;EAAC,IAAA1B,UAAA,CAAAK,OAAA,EAAAmB,oBAAA,EAAAC,gBAAA;EAAA,WAAA5B,aAAA,CAAAQ,OAAA,EAAAmB,oBAAA;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAC,sBAAsBA,CAACC,aAAa,EAAE;MACpC,IAAIR,KAAK,GAAG,IAAI,CAACA,KAAK;MACtB,IAAI,CAACA,KAAK,IAAI,CAACQ,aAAa,EAAE;QAC5B,OAAO,CAAC;MACV;MACA,IAAIC,SAAS,GAAGD,aAAa,CAACE,OAAO,IAAI,IAAI,CAACC,KAAK,CAACC,sBAAsB,IAAI,CAAC,CAAC;MAChF,OAAOC,IAAI,CAACC,GAAG,CAACd,KAAK,CAACe,CAAC,GAAGf,KAAK,CAACgB,MAAM,GAAGP,SAAS,EAAE,CAAC,CAAC;IACxD;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAW,gBAAgBA,CAACf,KAAK,EAAE,CAAC;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAC1B,SAAAY,MAAMA,CAAA,EAAG;MACP,IAAIC,WAAW,GAAG,IAAI,CAACR,KAAK;QAC1BS,QAAQ,GAAGD,WAAW,CAACC,QAAQ;QAC/BC,qBAAqB,GAAGF,WAAW,CAACE,qBAAqB;QACzDT,sBAAsB,GAAGO,WAAW,CAACP,sBAAsB;QAC3DU,IAAI,GAAG,CAAC,CAAC,EAAE9B,8BAA8B,CAACf,OAAO,EAAE0C,WAAW,EAAExB,SAAS,CAAC;MAC5E,OAAoBF,KAAK,CAAC8B,aAAa,CAAC7B,KAAK,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEc,SAAS,CAACd,OAAO,EAAE;QAC5EwB,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,EAAEqB,IAAI,CAAC,CAAC;IACX;EAAC;AAAA,EA1BgC7B,KAAK,CAAC+B,SAAS;AA4BlD,IAAIC,QAAQ,GAAGpC,OAAO,CAACZ,OAAO,GAAGmB,oBAAoB;AACrD8B,MAAM,CAACrC,OAAO,GAAGA,OAAO,CAACZ,OAAO", "ignoreList": []}