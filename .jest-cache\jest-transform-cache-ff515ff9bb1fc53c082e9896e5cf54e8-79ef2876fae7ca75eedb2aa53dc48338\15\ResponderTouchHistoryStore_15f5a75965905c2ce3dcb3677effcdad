b40cf124947f2b35796b06fe09d4a32c
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
exports.__esModule = true;
exports.ResponderTouchHistoryStore = void 0;
var _ResponderEventTypes = require("./ResponderEventTypes");
var __DEV__ = process.env.NODE_ENV !== 'production';
var MAX_TOUCH_BANK = 20;
function timestampForTouch(touch) {
  return touch.timeStamp || touch.timestamp;
}
function createTouchRecord(touch) {
  return {
    touchActive: true,
    startPageX: touch.pageX,
    startPageY: touch.pageY,
    startTimeStamp: timestampForTouch(touch),
    currentPageX: touch.pageX,
    currentPageY: touch.pageY,
    currentTimeStamp: timestampForTouch(touch),
    previousPageX: touch.pageX,
    previousPageY: touch.pageY,
    previousTimeStamp: timestampForTouch(touch)
  };
}
function resetTouchRecord(touchRecord, touch) {
  touchRecord.touchActive = true;
  touchRecord.startPageX = touch.pageX;
  touchRecord.startPageY = touch.pageY;
  touchRecord.startTimeStamp = timestampForTouch(touch);
  touchRecord.currentPageX = touch.pageX;
  touchRecord.currentPageY = touch.pageY;
  touchRecord.currentTimeStamp = timestampForTouch(touch);
  touchRecord.previousPageX = touch.pageX;
  touchRecord.previousPageY = touch.pageY;
  touchRecord.previousTimeStamp = timestampForTouch(touch);
}
function getTouchIdentifier(_ref) {
  var identifier = _ref.identifier;
  if (identifier == null) {
    console.error('Touch object is missing identifier.');
  }
  if (__DEV__) {
    if (identifier > MAX_TOUCH_BANK) {
      console.error('Touch identifier %s is greater than maximum supported %s which causes ' + 'performance issues backfilling array locations for all of the indices.', identifier, MAX_TOUCH_BANK);
    }
  }
  return identifier;
}
function recordTouchStart(touch, touchHistory) {
  var identifier = getTouchIdentifier(touch);
  var touchRecord = touchHistory.touchBank[identifier];
  if (touchRecord) {
    resetTouchRecord(touchRecord, touch);
  } else {
    touchHistory.touchBank[identifier] = createTouchRecord(touch);
  }
  touchHistory.mostRecentTimeStamp = timestampForTouch(touch);
}
function recordTouchMove(touch, touchHistory) {
  var touchRecord = touchHistory.touchBank[getTouchIdentifier(touch)];
  if (touchRecord) {
    touchRecord.touchActive = true;
    touchRecord.previousPageX = touchRecord.currentPageX;
    touchRecord.previousPageY = touchRecord.currentPageY;
    touchRecord.previousTimeStamp = touchRecord.currentTimeStamp;
    touchRecord.currentPageX = touch.pageX;
    touchRecord.currentPageY = touch.pageY;
    touchRecord.currentTimeStamp = timestampForTouch(touch);
    touchHistory.mostRecentTimeStamp = timestampForTouch(touch);
  } else {
    console.warn('Cannot record touch move without a touch start.\n', "Touch Move: " + printTouch(touch) + "\n", "Touch Bank: " + printTouchBank(touchHistory));
  }
}
function recordTouchEnd(touch, touchHistory) {
  var touchRecord = touchHistory.touchBank[getTouchIdentifier(touch)];
  if (touchRecord) {
    touchRecord.touchActive = false;
    touchRecord.previousPageX = touchRecord.currentPageX;
    touchRecord.previousPageY = touchRecord.currentPageY;
    touchRecord.previousTimeStamp = touchRecord.currentTimeStamp;
    touchRecord.currentPageX = touch.pageX;
    touchRecord.currentPageY = touch.pageY;
    touchRecord.currentTimeStamp = timestampForTouch(touch);
    touchHistory.mostRecentTimeStamp = timestampForTouch(touch);
  } else {
    console.warn('Cannot record touch end without a touch start.\n', "Touch End: " + printTouch(touch) + "\n", "Touch Bank: " + printTouchBank(touchHistory));
  }
}
function printTouch(touch) {
  return JSON.stringify({
    identifier: touch.identifier,
    pageX: touch.pageX,
    pageY: touch.pageY,
    timestamp: timestampForTouch(touch)
  });
}
function printTouchBank(touchHistory) {
  var touchBank = touchHistory.touchBank;
  var printed = JSON.stringify(touchBank.slice(0, MAX_TOUCH_BANK));
  if (touchBank.length > MAX_TOUCH_BANK) {
    printed += ' (original size: ' + touchBank.length + ')';
  }
  return printed;
}
var ResponderTouchHistoryStore = function () {
  function ResponderTouchHistoryStore() {
    (0, _classCallCheck2.default)(this, ResponderTouchHistoryStore);
    this._touchHistory = {
      touchBank: [],
      numberActiveTouches: 0,
      indexOfSingleActiveTouch: -1,
      mostRecentTimeStamp: 0
    };
  }
  return (0, _createClass2.default)(ResponderTouchHistoryStore, [{
    key: "recordTouchTrack",
    value: function recordTouchTrack(topLevelType, nativeEvent) {
      var touchHistory = this._touchHistory;
      if ((0, _ResponderEventTypes.isMoveish)(topLevelType)) {
        nativeEvent.changedTouches.forEach(function (touch) {
          return recordTouchMove(touch, touchHistory);
        });
      } else if ((0, _ResponderEventTypes.isStartish)(topLevelType)) {
        nativeEvent.changedTouches.forEach(function (touch) {
          return recordTouchStart(touch, touchHistory);
        });
        touchHistory.numberActiveTouches = nativeEvent.touches.length;
        if (touchHistory.numberActiveTouches === 1) {
          touchHistory.indexOfSingleActiveTouch = nativeEvent.touches[0].identifier;
        }
      } else if ((0, _ResponderEventTypes.isEndish)(topLevelType)) {
        nativeEvent.changedTouches.forEach(function (touch) {
          return recordTouchEnd(touch, touchHistory);
        });
        touchHistory.numberActiveTouches = nativeEvent.touches.length;
        if (touchHistory.numberActiveTouches === 1) {
          var touchBank = touchHistory.touchBank;
          for (var i = 0; i < touchBank.length; i++) {
            var touchTrackToCheck = touchBank[i];
            if (touchTrackToCheck != null && touchTrackToCheck.touchActive) {
              touchHistory.indexOfSingleActiveTouch = i;
              break;
            }
          }
          if (__DEV__) {
            var activeRecord = touchBank[touchHistory.indexOfSingleActiveTouch];
            if (!(activeRecord != null && activeRecord.touchActive)) {
              console.error('Cannot find single active touch.');
            }
          }
        }
      }
    }
  }, {
    key: "touchHistory",
    get: function get() {
      return this._touchHistory;
    }
  }]);
}();
exports.ResponderTouchHistoryStore = ResponderTouchHistoryStore;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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