3b41e10531715303a950925b862eb072
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _pick = _interopRequireDefault(require("../../modules/pick"));
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _usePressEvents = _interopRequireDefault(require("../../modules/usePressEvents"));
var _warnOnce = require("../../modules/warnOnce");
var forwardPropsList = {
  accessibilityDisabled: true,
  accessibilityLabel: true,
  accessibilityLiveRegion: true,
  accessibilityRole: true,
  accessibilityState: true,
  accessibilityValue: true,
  children: true,
  disabled: true,
  focusable: true,
  nativeID: true,
  onBlur: true,
  onFocus: true,
  onLayout: true,
  testID: true
};
var pickProps = function pickProps(props) {
  return (0, _pick.default)(props, forwardPropsList);
};
function TouchableWithoutFeedback(props, forwardedRef) {
  (0, _warnOnce.warnOnce)('TouchableWithoutFeedback', 'TouchableWithoutFeedback is deprecated. Please use Pressable.');
  var delayPressIn = props.delayPressIn,
    delayPressOut = props.delayPressOut,
    delayLongPress = props.delayLongPress,
    disabled = props.disabled,
    focusable = props.focusable,
    onLongPress = props.onLongPress,
    onPress = props.onPress,
    onPressIn = props.onPressIn,
    onPressOut = props.onPressOut,
    rejectResponderTermination = props.rejectResponderTermination;
  var hostRef = (0, _react.useRef)(null);
  var pressConfig = (0, _react.useMemo)(function () {
    return {
      cancelable: !rejectResponderTermination,
      disabled: disabled,
      delayLongPress: delayLongPress,
      delayPressStart: delayPressIn,
      delayPressEnd: delayPressOut,
      onLongPress: onLongPress,
      onPress: onPress,
      onPressStart: onPressIn,
      onPressEnd: onPressOut
    };
  }, [disabled, delayPressIn, delayPressOut, delayLongPress, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination]);
  var pressEventHandlers = (0, _usePressEvents.default)(hostRef, pressConfig);
  var element = React.Children.only(props.children);
  var children = [element.props.children];
  var supportedProps = pickProps(props);
  supportedProps.accessibilityDisabled = disabled;
  supportedProps.focusable = !disabled && focusable !== false;
  supportedProps.ref = (0, _useMergeRefs.default)(forwardedRef, hostRef, element.ref);
  var elementProps = Object.assign(supportedProps, pressEventHandlers);
  return React.cloneElement.apply(React, [element, elementProps].concat(children));
}
var MemoedTouchableWithoutFeedback = React.memo(React.forwardRef(TouchableWithoutFeedback));
MemoedTouchableWithoutFeedback.displayName = 'TouchableWithoutFeedback';
var _default = exports.default = MemoedTouchableWithoutFeedback;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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