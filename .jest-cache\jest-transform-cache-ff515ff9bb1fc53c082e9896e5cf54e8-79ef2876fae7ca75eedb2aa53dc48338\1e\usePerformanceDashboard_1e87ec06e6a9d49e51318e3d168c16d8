2b03aec702bc4d06a598764f53081941
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_27rx7fc5md() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\usePerformanceDashboard.ts";
  var hash = "651c744a4752ec6da2c08d8b527cd208b3700ac3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\usePerformanceDashboard.ts",
    statementMap: {
      "0": {
        start: {
          line: 79,
          column: 25
        },
        end: {
          line: 79,
          column: 44
        }
      },
      "1": {
        start: {
          line: 80,
          column: 27
        },
        end: {
          line: 80,
          column: 48
        }
      },
      "2": {
        start: {
          line: 81,
          column: 29
        },
        end: {
          line: 81,
          column: 52
        }
      },
      "3": {
        start: {
          line: 83,
          column: 30
        },
        end: {
          line: 91,
          column: 4
        }
      },
      "4": {
        start: {
          line: 93,
          column: 28
        },
        end: {
          line: 120,
          column: 4
        }
      },
      "5": {
        start: {
          line: 125,
          column: 25
        },
        end: {
          line: 213,
          column: 28
        }
      },
      "6": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 212,
          column: 5
        }
      },
      "7": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 55
        }
      },
      "8": {
        start: {
          line: 127,
          column: 24
        },
        end: {
          line: 127,
          column: 52
        }
      },
      "9": {
        start: {
          line: 130,
          column: 29
        },
        end: {
          line: 130,
          column: 80
        }
      },
      "10": {
        start: {
          line: 133,
          column: 23
        },
        end: {
          line: 135,
          column: 14
        }
      },
      "11": {
        start: {
          line: 138,
          column: 26
        },
        end: {
          line: 164,
          column: 7
        }
      },
      "12": {
        start: {
          line: 167,
          column: 18
        },
        end: {
          line: 167,
          column: 28
        }
      },
      "13": {
        start: {
          line: 168,
          column: 24
        },
        end: {
          line: 168,
          column: 43
        }
      },
      "14": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 195,
          column: 7
        }
      },
      "15": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 175,
          column: 11
        }
      },
      "16": {
        start: {
          line: 178,
          column: 8
        },
        end: {
          line: 181,
          column: 11
        }
      },
      "17": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 187,
          column: 11
        }
      },
      "18": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 194,
          column: 11
        }
      },
      "19": {
        start: {
          line: 191,
          column: 10
        },
        end: {
          line: 193,
          column: 11
        }
      },
      "20": {
        start: {
          line: 192,
          column: 12
        },
        end: {
          line: 192,
          column: 61
        }
      },
      "21": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 207,
          column: 10
        }
      },
      "22": {
        start: {
          line: 197,
          column: 24
        },
        end: {
          line: 207,
          column: 7
        }
      },
      "23": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 210,
          column: 57
        }
      },
      "24": {
        start: {
          line: 211,
          column: 6
        },
        end: {
          line: 211,
          column: 56
        }
      },
      "25": {
        start: {
          line: 211,
          column: 24
        },
        end: {
          line: 211,
          column: 53
        }
      },
      "26": {
        start: {
          line: 218,
          column: 21
        },
        end: {
          line: 226,
          column: 8
        }
      },
      "27": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 225,
          column: 5
        }
      },
      "28": {
        start: {
          line: 220,
          column: 25
        },
        end: {
          line: 220,
          column: 85
        }
      },
      "29": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 221,
          column: 24
        }
      },
      "30": {
        start: {
          line: 223,
          column: 6
        },
        end: {
          line: 223,
          column: 53
        }
      },
      "31": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 16
        }
      },
      "32": {
        start: {
          line: 231,
          column: 25
        },
        end: {
          line: 240,
          column: 8
        }
      },
      "33": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 239,
          column: 5
        }
      },
      "34": {
        start: {
          line: 233,
          column: 24
        },
        end: {
          line: 233,
          column: 90
        }
      },
      "35": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 61
        }
      },
      "36": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 235,
          column: 23
        }
      },
      "37": {
        start: {
          line: 237,
          column: 6
        },
        end: {
          line: 237,
          column: 57
        }
      },
      "38": {
        start: {
          line: 238,
          column: 6
        },
        end: {
          line: 238,
          column: 16
        }
      },
      "39": {
        start: {
          line: 245,
          column: 27
        },
        end: {
          line: 252,
          column: 8
        }
      },
      "40": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 251,
          column: 8
        }
      },
      "41": {
        start: {
          line: 246,
          column: 22
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "42": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 249,
          column: 67
        }
      },
      "43": {
        start: {
          line: 257,
          column: 26
        },
        end: {
          line: 268,
          column: 28
        }
      },
      "44": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 258,
          column: 43
        }
      },
      "45": {
        start: {
          line: 258,
          column: 31
        },
        end: {
          line: 258,
          column: 43
        }
      },
      "46": {
        start: {
          line: 260,
          column: 22
        },
        end: {
          line: 260,
          column: 56
        }
      },
      "47": {
        start: {
          line: 261,
          column: 4
        },
        end: {
          line: 261,
          column: 32
        }
      },
      "48": {
        start: {
          line: 261,
          column: 20
        },
        end: {
          line: 261,
          column: 32
        }
      },
      "49": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 267,
          column: 6
        }
      },
      "50": {
        start: {
          line: 273,
          column: 41
        },
        end: {
          line: 281,
          column: 22
        }
      },
      "51": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 280,
          column: 5
        }
      },
      "52": {
        start: {
          line: 275,
          column: 6
        },
        end: {
          line: 275,
          column: 37
        }
      },
      "53": {
        start: {
          line: 275,
          column: 27
        },
        end: {
          line: 275,
          column: 37
        }
      },
      "54": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 60
        }
      },
      "55": {
        start: {
          line: 278,
          column: 6
        },
        end: {
          line: 278,
          column: 61
        }
      },
      "56": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 16
        }
      },
      "57": {
        start: {
          line: 286,
          column: 28
        },
        end: {
          line: 288,
          column: 8
        }
      },
      "58": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 287,
          column: 69
        }
      },
      "59": {
        start: {
          line: 287,
          column: 23
        },
        end: {
          line: 287,
          column: 66
        }
      },
      "60": {
        start: {
          line: 291,
          column: 2
        },
        end: {
          line: 299,
          column: 67
        }
      },
      "61": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 292,
          column: 36
        }
      },
      "62": {
        start: {
          line: 292,
          column: 29
        },
        end: {
          line: 292,
          column: 36
        }
      },
      "63": {
        start: {
          line: 294,
          column: 21
        },
        end: {
          line: 296,
          column: 30
        }
      },
      "64": {
        start: {
          line: 295,
          column: 6
        },
        end: {
          line: 295,
          column: 23
        }
      },
      "65": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 298,
          column: 41
        }
      },
      "66": {
        start: {
          line: 298,
          column: 17
        },
        end: {
          line: 298,
          column: 40
        }
      },
      "67": {
        start: {
          line: 302,
          column: 2
        },
        end: {
          line: 304,
          column: 9
        }
      },
      "68": {
        start: {
          line: 303,
          column: 4
        },
        end: {
          line: 303,
          column: 21
        }
      },
      "69": {
        start: {
          line: 307,
          column: 18
        },
        end: {
          line: 328,
          column: 58
        }
      },
      "70": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 316,
          column: 5
        }
      },
      "71": {
        start: {
          line: 309,
          column: 6
        },
        end: {
          line: 315,
          column: 8
        }
      },
      "72": {
        start: {
          line: 318,
          column: 25
        },
        end: {
          line: 318,
          column: 77
        }
      },
      "73": {
        start: {
          line: 318,
          column: 54
        },
        end: {
          line: 318,
          column: 69
        }
      },
      "74": {
        start: {
          line: 319,
          column: 23
        },
        end: {
          line: 319,
          column: 88
        }
      },
      "75": {
        start: {
          line: 321,
          column: 4
        },
        end: {
          line: 327,
          column: 6
        }
      },
      "76": {
        start: {
          line: 330,
          column: 2
        },
        end: {
          line: 354,
          column: 5
        }
      },
      "77": {
        start: {
          line: 330,
          column: 24
        },
        end: {
          line: 343,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "usePerformanceDashboard",
        decl: {
          start: {
            line: 76,
            column: 16
          },
          end: {
            line: 76,
            column: 39
          }
        },
        loc: {
          start: {
            line: 78,
            column: 33
          },
          end: {
            line: 355,
            column: 1
          }
        },
        line: 78
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 125,
            column: 37
          },
          end: {
            line: 125,
            column: 38
          }
        },
        loc: {
          start: {
            line: 125,
            column: 49
          },
          end: {
            line: 213,
            column: 3
          }
        },
        line: 125
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 127,
            column: 15
          },
          end: {
            line: 127,
            column: 16
          }
        },
        loc: {
          start: {
            line: 127,
            column: 24
          },
          end: {
            line: 127,
            column: 52
          }
        },
        line: 127
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 190,
            column: 39
          },
          end: {
            line: 190,
            column: 40
          }
        },
        loc: {
          start: {
            line: 190,
            column: 46
          },
          end: {
            line: 194,
            column: 9
          }
        },
        line: 190
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 197,
            column: 15
          },
          end: {
            line: 197,
            column: 16
          }
        },
        loc: {
          start: {
            line: 197,
            column: 24
          },
          end: {
            line: 207,
            column: 7
          }
        },
        line: 197
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 211,
            column: 15
          },
          end: {
            line: 211,
            column: 16
          }
        },
        loc: {
          start: {
            line: 211,
            column: 24
          },
          end: {
            line: 211,
            column: 53
          }
        },
        line: 211
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 218,
            column: 33
          },
          end: {
            line: 218,
            column: 34
          }
        },
        loc: {
          start: {
            line: 218,
            column: 86
          },
          end: {
            line: 226,
            column: 3
          }
        },
        line: 218
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 231,
            column: 37
          },
          end: {
            line: 231,
            column: 38
          }
        },
        loc: {
          start: {
            line: 231,
            column: 68
          },
          end: {
            line: 240,
            column: 3
          }
        },
        line: 231
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 245,
            column: 39
          },
          end: {
            line: 245,
            column: 40
          }
        },
        loc: {
          start: {
            line: 245,
            column: 60
          },
          end: {
            line: 252,
            column: 3
          }
        },
        line: 245
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 246,
            column: 13
          },
          end: {
            line: 246,
            column: 14
          }
        },
        loc: {
          start: {
            line: 246,
            column: 22
          },
          end: {
            line: 251,
            column: 5
          }
        },
        line: 246
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 248,
            column: 30
          },
          end: {
            line: 248,
            column: 31
          }
        },
        loc: {
          start: {
            line: 249,
            column: 8
          },
          end: {
            line: 249,
            column: 67
          }
        },
        line: 249
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 257,
            column: 38
          },
          end: {
            line: 257,
            column: 39
          }
        },
        loc: {
          start: {
            line: 257,
            column: 57
          },
          end: {
            line: 268,
            column: 3
          }
        },
        line: 257
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 273,
            column: 53
          },
          end: {
            line: 273,
            column: 54
          }
        },
        loc: {
          start: {
            line: 273,
            column: 65
          },
          end: {
            line: 281,
            column: 3
          }
        },
        line: 273
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 286,
            column: 40
          },
          end: {
            line: 286,
            column: 41
          }
        },
        loc: {
          start: {
            line: 286,
            column: 46
          },
          end: {
            line: 288,
            column: 3
          }
        },
        line: 286
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 287,
            column: 14
          },
          end: {
            line: 287,
            column: 15
          }
        },
        loc: {
          start: {
            line: 287,
            column: 23
          },
          end: {
            line: 287,
            column: 66
          }
        },
        line: 287
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 291,
            column: 12
          },
          end: {
            line: 291,
            column: 13
          }
        },
        loc: {
          start: {
            line: 291,
            column: 18
          },
          end: {
            line: 299,
            column: 3
          }
        },
        line: 291
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 294,
            column: 33
          },
          end: {
            line: 294,
            column: 34
          }
        },
        loc: {
          start: {
            line: 294,
            column: 39
          },
          end: {
            line: 296,
            column: 5
          }
        },
        line: 294
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 298,
            column: 11
          },
          end: {
            line: 298,
            column: 12
          }
        },
        loc: {
          start: {
            line: 298,
            column: 17
          },
          end: {
            line: 298,
            column: 40
          }
        },
        line: 298
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 302,
            column: 12
          },
          end: {
            line: 302,
            column: 13
          }
        },
        loc: {
          start: {
            line: 302,
            column: 18
          },
          end: {
            line: 304,
            column: 3
          }
        },
        line: 302
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 307,
            column: 26
          },
          end: {
            line: 307,
            column: 27
          }
        },
        loc: {
          start: {
            line: 307,
            column: 32
          },
          end: {
            line: 328,
            column: 3
          }
        },
        line: 307
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 318,
            column: 45
          },
          end: {
            line: 318,
            column: 46
          }
        },
        loc: {
          start: {
            line: 318,
            column: 54
          },
          end: {
            line: 318,
            column: 69
          }
        },
        line: 318
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 330,
            column: 17
          },
          end: {
            line: 330,
            column: 18
          }
        },
        loc: {
          start: {
            line: 330,
            column: 24
          },
          end: {
            line: 343,
            column: 3
          }
        },
        line: 330
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 77,
            column: 2
          },
          end: {
            line: 77,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 77,
            column: 44
          },
          end: {
            line: 77,
            column: 46
          }
        }],
        line: 77
      },
      "1": {
        loc: {
          start: {
            line: 133,
            column: 23
          },
          end: {
            line: 135,
            column: 14
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 134,
            column: 10
          },
          end: {
            line: 134,
            column: 66
          }
        }, {
          start: {
            line: 135,
            column: 10
          },
          end: {
            line: 135,
            column: 14
          }
        }],
        line: 133
      },
      "2": {
        loc: {
          start: {
            line: 142,
            column: 18
          },
          end: {
            line: 142,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 142,
            column: 69
          },
          end: {
            line: 142,
            column: 72
          }
        }, {
          start: {
            line: 142,
            column: 75
          },
          end: {
            line: 142,
            column: 77
          }
        }],
        line: 142
      },
      "3": {
        loc: {
          start: {
            line: 147,
            column: 18
          },
          end: {
            line: 147,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 147,
            column: 69
          },
          end: {
            line: 147,
            column: 72
          }
        }, {
          start: {
            line: 147,
            column: 75
          },
          end: {
            line: 147,
            column: 77
          }
        }],
        line: 147
      },
      "4": {
        loc: {
          start: {
            line: 152,
            column: 18
          },
          end: {
            line: 152,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 152,
            column: 70
          },
          end: {
            line: 152,
            column: 73
          }
        }, {
          start: {
            line: 152,
            column: 76
          },
          end: {
            line: 152,
            column: 78
          }
        }],
        line: 152
      },
      "5": {
        loc: {
          start: {
            line: 157,
            column: 18
          },
          end: {
            line: 157,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 157,
            column: 70
          },
          end: {
            line: 157,
            column: 73
          }
        }, {
          start: {
            line: 157,
            column: 76
          },
          end: {
            line: 157,
            column: 78
          }
        }],
        line: 157
      },
      "6": {
        loc: {
          start: {
            line: 162,
            column: 18
          },
          end: {
            line: 162,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 162,
            column: 70
          },
          end: {
            line: 162,
            column: 73
          }
        }, {
          start: {
            line: 162,
            column: 76
          },
          end: {
            line: 162,
            column: 78
          }
        }],
        line: 162
      },
      "7": {
        loc: {
          start: {
            line: 170,
            column: 6
          },
          end: {
            line: 195,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 6
          },
          end: {
            line: 195,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "8": {
        loc: {
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 193,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 193,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "9": {
        loc: {
          start: {
            line: 203,
            column: 16
          },
          end: {
            line: 203,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 203,
            column: 38
          },
          end: {
            line: 203,
            column: 59
          }
        }, {
          start: {
            line: 203,
            column: 62
          },
          end: {
            line: 203,
            column: 64
          }
        }],
        line: 203
      },
      "10": {
        loc: {
          start: {
            line: 218,
            column: 40
          },
          end: {
            line: 218,
            column: 81
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 218,
            column: 75
          },
          end: {
            line: 218,
            column: 81
          }
        }],
        line: 218
      },
      "11": {
        loc: {
          start: {
            line: 249,
            column: 8
          },
          end: {
            line: 249,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 31
          },
          end: {
            line: 249,
            column: 59
          }
        }, {
          start: {
            line: 249,
            column: 62
          },
          end: {
            line: 249,
            column: 67
          }
        }],
        line: 249
      },
      "12": {
        loc: {
          start: {
            line: 258,
            column: 4
          },
          end: {
            line: 258,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 258,
            column: 4
          },
          end: {
            line: 258,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 258
      },
      "13": {
        loc: {
          start: {
            line: 261,
            column: 4
          },
          end: {
            line: 261,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 261,
            column: 4
          },
          end: {
            line: 261,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 261
      },
      "14": {
        loc: {
          start: {
            line: 275,
            column: 6
          },
          end: {
            line: 275,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 275,
            column: 6
          },
          end: {
            line: 275,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 275
      },
      "15": {
        loc: {
          start: {
            line: 276,
            column: 13
          },
          end: {
            line: 276,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 276,
            column: 13
          },
          end: {
            line: 276,
            column: 53
          }
        }, {
          start: {
            line: 276,
            column: 57
          },
          end: {
            line: 276,
            column: 59
          }
        }],
        line: 276
      },
      "16": {
        loc: {
          start: {
            line: 292,
            column: 4
          },
          end: {
            line: 292,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 292,
            column: 4
          },
          end: {
            line: 292,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 292
      },
      "17": {
        loc: {
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 316,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 316,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 308
      },
      "18": {
        loc: {
          start: {
            line: 319,
            column: 23
          },
          end: {
            line: 319,
            column: 88
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 319,
            column: 23
          },
          end: {
            line: 319,
            column: 61
          }
        }, {
          start: {
            line: 319,
            column: 65
          },
          end: {
            line: 319,
            column: 88
          }
        }],
        line: 319
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "651c744a4752ec6da2c08d8b527cd208b3700ac3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_27rx7fc5md = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_27rx7fc5md();
import { useState, useEffect, useCallback, useMemo } from 'react';
import { unifiedPerformanceMonitor } from "../services/monitoring/UnifiedPerformanceMonitor";
import { useAIOptimization } from "./useAIOptimization";
import { useEdgeOptimization } from "./useEdgeOptimization";
import { useNativeOptimization } from "./useNativeOptimization";
export function usePerformanceDashboard() {
  var initialConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_27rx7fc5md().b[0][0]++, {});
  cov_27rx7fc5md().f[0]++;
  var aiOptimization = (cov_27rx7fc5md().s[0]++, useAIOptimization());
  var edgeOptimization = (cov_27rx7fc5md().s[1]++, useEdgeOptimization());
  var nativeOptimization = (cov_27rx7fc5md().s[2]++, useNativeOptimization());
  var _ref = (cov_27rx7fc5md().s[3]++, useState(Object.assign({
      autoRefresh: true,
      refreshInterval: 10000,
      enableAlerts: true,
      enableTrends: true,
      enableInsights: true,
      maxDataPoints: 100
    }, initialConfig))),
    _ref2 = _slicedToArray(_ref, 2),
    config = _ref2[0],
    setConfig = _ref2[1];
  var _ref3 = (cov_27rx7fc5md().s[4]++, useState({
      isLoading: true,
      lastUpdated: 0,
      unifiedMetrics: null,
      insights: null,
      alerts: [],
      trends: {
        performance: [],
        memory: [],
        network: []
      },
      phaseStatus: {
        phase1: {
          status: 'unknown',
          score: 0,
          health: 0
        },
        phase2: {
          status: 'unknown',
          score: 0,
          health: 0
        },
        phase3a: {
          status: 'unknown',
          score: 0,
          health: 0
        },
        phase3b: {
          status: 'unknown',
          score: 0,
          health: 0
        },
        phase3c: {
          status: 'unknown',
          score: 0,
          health: 0
        }
      },
      realTimeMetrics: {
        bundleSize: 0,
        loadTime: 0,
        renderTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        networkLatency: 0,
        batteryDrain: 0
      }
    })),
    _ref4 = _slicedToArray(_ref3, 2),
    state = _ref4[0],
    setState = _ref4[1];
  var refreshMetrics = (cov_27rx7fc5md().s[5]++, useCallback(_asyncToGenerator(function* () {
    cov_27rx7fc5md().f[1]++;
    cov_27rx7fc5md().s[6]++;
    try {
      cov_27rx7fc5md().s[7]++;
      setState(function (prev) {
        cov_27rx7fc5md().f[2]++;
        cov_27rx7fc5md().s[8]++;
        return Object.assign({}, prev, {
          isLoading: true
        });
      });
      var unifiedMetrics = (cov_27rx7fc5md().s[9]++, yield unifiedPerformanceMonitor.getUnifiedMetrics());
      var insights = (cov_27rx7fc5md().s[10]++, config.enableInsights ? (cov_27rx7fc5md().b[1][0]++, yield unifiedPerformanceMonitor.getPerformanceInsights()) : (cov_27rx7fc5md().b[1][1]++, null));
      var phaseStatus = (cov_27rx7fc5md().s[11]++, {
        phase1: {
          status: unifiedMetrics.phases.phase1.status,
          score: unifiedMetrics.phases.phase1.performance.current,
          health: unifiedMetrics.phases.phase1.status === 'active' ? (cov_27rx7fc5md().b[2][0]++, 100) : (cov_27rx7fc5md().b[2][1]++, 50)
        },
        phase2: {
          status: unifiedMetrics.phases.phase2.status,
          score: unifiedMetrics.phases.phase2.performance.current,
          health: unifiedMetrics.phases.phase2.status === 'active' ? (cov_27rx7fc5md().b[3][0]++, 100) : (cov_27rx7fc5md().b[3][1]++, 50)
        },
        phase3a: {
          status: unifiedMetrics.phases.phase3a.status,
          score: unifiedMetrics.phases.phase3a.performance.current,
          health: unifiedMetrics.phases.phase3a.status === 'active' ? (cov_27rx7fc5md().b[4][0]++, 100) : (cov_27rx7fc5md().b[4][1]++, 50)
        },
        phase3b: {
          status: unifiedMetrics.phases.phase3b.status,
          score: unifiedMetrics.phases.phase3b.performance.current,
          health: unifiedMetrics.phases.phase3b.status === 'active' ? (cov_27rx7fc5md().b[5][0]++, 100) : (cov_27rx7fc5md().b[5][1]++, 50)
        },
        phase3c: {
          status: unifiedMetrics.phases.phase3c.status,
          score: unifiedMetrics.phases.phase3c.performance.current,
          health: unifiedMetrics.phases.phase3c.status === 'active' ? (cov_27rx7fc5md().b[6][0]++, 100) : (cov_27rx7fc5md().b[6][1]++, 50)
        }
      });
      var now = (cov_27rx7fc5md().s[12]++, Date.now());
      var newTrends = (cov_27rx7fc5md().s[13]++, Object.assign({}, state.trends));
      cov_27rx7fc5md().s[14]++;
      if (config.enableTrends) {
        cov_27rx7fc5md().b[7][0]++;
        cov_27rx7fc5md().s[15]++;
        newTrends.performance.push({
          timestamp: now,
          value: unifiedMetrics.overall.performanceScore
        });
        cov_27rx7fc5md().s[16]++;
        newTrends.memory.push({
          timestamp: now,
          value: unifiedMetrics.realTime.memoryUsage
        });
        cov_27rx7fc5md().s[17]++;
        newTrends.network.push({
          timestamp: now,
          value: unifiedMetrics.realTime.networkLatency
        });
        cov_27rx7fc5md().s[18]++;
        Object.keys(newTrends).forEach(function (key) {
          cov_27rx7fc5md().f[3]++;
          cov_27rx7fc5md().s[19]++;
          if (newTrends[key].length > config.maxDataPoints) {
            cov_27rx7fc5md().b[8][0]++;
            cov_27rx7fc5md().s[20]++;
            newTrends[key].shift();
          } else {
            cov_27rx7fc5md().b[8][1]++;
          }
        });
      } else {
        cov_27rx7fc5md().b[7][1]++;
      }
      cov_27rx7fc5md().s[21]++;
      setState(function (prev) {
        cov_27rx7fc5md().f[4]++;
        cov_27rx7fc5md().s[22]++;
        return Object.assign({}, prev, {
          isLoading: false,
          lastUpdated: now,
          unifiedMetrics: unifiedMetrics,
          insights: insights,
          alerts: config.enableAlerts ? (cov_27rx7fc5md().b[9][0]++, unifiedMetrics.alerts) : (cov_27rx7fc5md().b[9][1]++, []),
          trends: newTrends,
          phaseStatus: phaseStatus,
          realTimeMetrics: unifiedMetrics.realTime
        });
      });
    } catch (error) {
      cov_27rx7fc5md().s[23]++;
      console.error('Failed to refresh metrics:', error);
      cov_27rx7fc5md().s[24]++;
      setState(function (prev) {
        cov_27rx7fc5md().f[5]++;
        cov_27rx7fc5md().s[25]++;
        return Object.assign({}, prev, {
          isLoading: false
        });
      });
    }
  }), [config, state.trends]));
  var exportData = (cov_27rx7fc5md().s[26]++, useCallback(_asyncToGenerator(function* () {
    var format = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_27rx7fc5md().b[10][0]++, 'json');
    cov_27rx7fc5md().f[6]++;
    cov_27rx7fc5md().s[27]++;
    try {
      var _exportData = (cov_27rx7fc5md().s[28]++, yield unifiedPerformanceMonitor.exportMonitoringData(format));
      cov_27rx7fc5md().s[29]++;
      return _exportData;
    } catch (error) {
      cov_27rx7fc5md().s[30]++;
      console.error('Failed to export data:', error);
      cov_27rx7fc5md().s[31]++;
      return '';
    }
  }), []));
  var startProfiling = (cov_27rx7fc5md().s[32]++, useCallback(function () {
    var _ref7 = _asyncToGenerator(function* (sessionName) {
      cov_27rx7fc5md().f[7]++;
      cov_27rx7fc5md().s[33]++;
      try {
        var sessionId = (cov_27rx7fc5md().s[34]++, yield unifiedPerformanceMonitor.startProfilingSession(sessionName));
        cov_27rx7fc5md().s[35]++;
        console.log(`Started profiling session: ${sessionId}`);
        cov_27rx7fc5md().s[36]++;
        return sessionId;
      } catch (error) {
        cov_27rx7fc5md().s[37]++;
        console.error('Failed to start profiling:', error);
        cov_27rx7fc5md().s[38]++;
        return '';
      }
    });
    return function (_x) {
      return _ref7.apply(this, arguments);
    };
  }(), []));
  var acknowledgeAlert = (cov_27rx7fc5md().s[39]++, useCallback(function (alertId) {
    cov_27rx7fc5md().f[8]++;
    cov_27rx7fc5md().s[40]++;
    setState(function (prev) {
      cov_27rx7fc5md().f[9]++;
      cov_27rx7fc5md().s[41]++;
      return Object.assign({}, prev, {
        alerts: prev.alerts.map(function (alert) {
          cov_27rx7fc5md().f[10]++;
          cov_27rx7fc5md().s[42]++;
          return alert.id === alertId ? (cov_27rx7fc5md().b[11][0]++, Object.assign({}, alert, {
            resolved: true
          })) : (cov_27rx7fc5md().b[11][1]++, alert);
        })
      });
    });
  }, []));
  var getPhaseDetails = (cov_27rx7fc5md().s[43]++, useCallback(function (phase) {
    cov_27rx7fc5md().f[11]++;
    cov_27rx7fc5md().s[44]++;
    if (!state.unifiedMetrics) {
      cov_27rx7fc5md().b[12][0]++;
      cov_27rx7fc5md().s[45]++;
      return null;
    } else {
      cov_27rx7fc5md().b[12][1]++;
    }
    var phaseData = (cov_27rx7fc5md().s[46]++, state.unifiedMetrics.phases[phase]);
    cov_27rx7fc5md().s[47]++;
    if (!phaseData) {
      cov_27rx7fc5md().b[13][0]++;
      cov_27rx7fc5md().s[48]++;
      return null;
    } else {
      cov_27rx7fc5md().b[13][1]++;
    }
    cov_27rx7fc5md().s[49]++;
    return Object.assign({}, phaseData, {
      improvement: (phaseData.performance.current - phaseData.performance.baseline) / phaseData.performance.baseline * 100,
      targetProgress: (phaseData.performance.current - phaseData.performance.baseline) / (phaseData.performance.target - phaseData.performance.baseline) * 100
    });
  }, [state.unifiedMetrics]));
  var getOptimizationRecommendations = (cov_27rx7fc5md().s[50]++, useCallback(_asyncToGenerator(function* () {
    cov_27rx7fc5md().f[12]++;
    cov_27rx7fc5md().s[51]++;
    try {
      cov_27rx7fc5md().s[52]++;
      if (!state.insights) {
        cov_27rx7fc5md().b[14][0]++;
        cov_27rx7fc5md().s[53]++;
        return [];
      } else {
        cov_27rx7fc5md().b[14][1]++;
      }
      cov_27rx7fc5md().s[54]++;
      return (cov_27rx7fc5md().b[15][0]++, state.insights.optimizationOpportunities) || (cov_27rx7fc5md().b[15][1]++, []);
    } catch (error) {
      cov_27rx7fc5md().s[55]++;
      console.error('Failed to get recommendations:', error);
      cov_27rx7fc5md().s[56]++;
      return [];
    }
  }), [state.insights]));
  var toggleAutoRefresh = (cov_27rx7fc5md().s[57]++, useCallback(function () {
    cov_27rx7fc5md().f[13]++;
    cov_27rx7fc5md().s[58]++;
    setConfig(function (prev) {
      cov_27rx7fc5md().f[14]++;
      cov_27rx7fc5md().s[59]++;
      return Object.assign({}, prev, {
        autoRefresh: !prev.autoRefresh
      });
    });
  }, []));
  cov_27rx7fc5md().s[60]++;
  useEffect(function () {
    cov_27rx7fc5md().f[15]++;
    cov_27rx7fc5md().s[61]++;
    if (!config.autoRefresh) {
      cov_27rx7fc5md().b[16][0]++;
      cov_27rx7fc5md().s[62]++;
      return;
    } else {
      cov_27rx7fc5md().b[16][1]++;
    }
    var interval = (cov_27rx7fc5md().s[63]++, setInterval(function () {
      cov_27rx7fc5md().f[16]++;
      cov_27rx7fc5md().s[64]++;
      refreshMetrics();
    }, config.refreshInterval));
    cov_27rx7fc5md().s[65]++;
    return function () {
      cov_27rx7fc5md().f[17]++;
      cov_27rx7fc5md().s[66]++;
      return clearInterval(interval);
    };
  }, [config.autoRefresh, config.refreshInterval, refreshMetrics]);
  cov_27rx7fc5md().s[67]++;
  useEffect(function () {
    cov_27rx7fc5md().f[18]++;
    cov_27rx7fc5md().s[68]++;
    refreshMetrics();
  }, []);
  var summary = (cov_27rx7fc5md().s[69]++, useMemo(function () {
    var _state$insights;
    cov_27rx7fc5md().f[19]++;
    cov_27rx7fc5md().s[70]++;
    if (!state.unifiedMetrics) {
      cov_27rx7fc5md().b[17][0]++;
      cov_27rx7fc5md().s[71]++;
      return {
        overallScore: 0,
        totalImprovement: 0,
        healthScore: 0,
        activeAlerts: 0,
        topInsight: 'Loading...'
      };
    } else {
      cov_27rx7fc5md().b[17][1]++;
    }
    var activeAlerts = (cov_27rx7fc5md().s[72]++, state.alerts.filter(function (alert) {
      cov_27rx7fc5md().f[20]++;
      cov_27rx7fc5md().s[73]++;
      return !alert.resolved;
    }).length);
    var topInsight = (cov_27rx7fc5md().s[74]++, (cov_27rx7fc5md().b[18][0]++, (_state$insights = state.insights) == null || (_state$insights = _state$insights.insights) == null || (_state$insights = _state$insights[0]) == null ? void 0 : _state$insights.insight) || (cov_27rx7fc5md().b[18][1]++, 'No insights available'));
    cov_27rx7fc5md().s[75]++;
    return {
      overallScore: Math.round(state.unifiedMetrics.overall.performanceScore),
      totalImprovement: Math.round(state.unifiedMetrics.overall.totalImprovement),
      healthScore: Math.round(state.unifiedMetrics.overall.healthScore),
      activeAlerts: activeAlerts,
      topInsight: topInsight
    };
  }, [state.unifiedMetrics, state.alerts, state.insights]));
  cov_27rx7fc5md().s[76]++;
  return useMemo(function () {
    cov_27rx7fc5md().f[21]++;
    cov_27rx7fc5md().s[77]++;
    return {
      state: state,
      actions: {
        refreshMetrics: refreshMetrics,
        exportData: exportData,
        startProfiling: startProfiling,
        acknowledgeAlert: acknowledgeAlert,
        getPhaseDetails: getPhaseDetails,
        getOptimizationRecommendations: getOptimizationRecommendations,
        toggleAutoRefresh: toggleAutoRefresh
      },
      config: config,
      summary: summary
    };
  }, [state, refreshMetrics, exportData, startProfiling, acknowledgeAlert, getPhaseDetails, getOptimizationRecommendations, toggleAutoRefresh, config, summary]);
}
export default usePerformanceDashboard;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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