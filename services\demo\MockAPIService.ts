/**
 * Mock API Service for Demo Mode
 * 
 * Provides mock responses for all API calls when running in demo mode,
 * eliminating the need for real API keys.
 */

import { DEMO_USERS, DEMO_MATCHES, DEMO_ANALYTICS } from '@/config/demo.config';

interface MockResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: number;
}

/**
 * Mock API Service for Demo Mode
 */
class MockAPIService {
  private readonly isDemoMode: boolean;
  private readonly simulateDelay: boolean;
  private readonly defaultDelay: number;

  constructor() {
    this.isDemoMode = process.env.EXPO_PUBLIC_DEMO_MODE === 'true';
    this.simulateDelay = true;
    this.defaultDelay = 500; // 500ms delay to simulate real API calls
  }

  /**
   * Simulate API delay
   */
  private async simulateAPIDelay(customDelay?: number): Promise<void> {
    if (!this.simulateDelay) return;
    
    const delay = customDelay || this.defaultDelay;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  /**
   * Create mock response
   */
  private createMockResponse<T>(data: T, message?: string): MockResponse<T> {
    return {
      success: true,
      data,
      message,
      timestamp: Date.now(),
    };
  }

  /**
   * Mock Supabase Authentication
   */
  async mockAuth(action: 'login' | 'signup' | 'logout', credentials?: any): Promise<MockResponse> {
    await this.simulateAPIDelay();

    switch (action) {
      case 'login':
        return this.createMockResponse({
          user: {
            id: 'demo-user-123',
            email: credentials?.email || '<EMAIL>',
            name: 'Demo User',
            avatar: 'https://via.placeholder.com/100x100/4CAF50/white?text=DU',
            subscription: 'premium',
            createdAt: new Date().toISOString(),
          },
          session: {
            access_token: 'demo-access-token-123',
            refresh_token: 'demo-refresh-token-123',
            expires_at: Date.now() + 3600000, // 1 hour
          },
        }, 'Login successful');

      case 'signup':
        return this.createMockResponse({
          user: {
            id: 'demo-user-new-456',
            email: credentials?.email || '<EMAIL>',
            name: credentials?.name || 'New Demo User',
            avatar: 'https://via.placeholder.com/100x100/2196F3/white?text=NU',
            subscription: 'free',
            createdAt: new Date().toISOString(),
          },
        }, 'Account created successfully');

      case 'logout':
        return this.createMockResponse(null, 'Logout successful');

      default:
        return this.createMockResponse(null, 'Unknown auth action');
    }
  }

  /**
   * Mock OpenAI API calls
   */
  async mockOpenAI(prompt: string, type: 'coaching' | 'analysis' | 'chat' = 'coaching'): Promise<MockResponse> {
    await this.simulateAPIDelay(1000); // Longer delay for AI calls

    const mockResponses = {
      coaching: [
        "Great serve technique! Try to follow through more with your racket for increased power and spin.",
        "Your backhand form is improving. Focus on keeping your eye on the ball and rotating your hips.",
        "Excellent footwork in that rally! Remember to stay on your toes and be ready for the next shot.",
        "Your forehand has good power. Work on consistency by practicing your swing path.",
        "Nice volley technique! Try to get closer to the net for better angles.",
      ],
      analysis: [
        "Based on your match data, you're winning 78% of points when you serve to the opponent's backhand.",
        "Your unforced errors decrease by 40% when you take more time between points.",
        "You have a 65% win rate on break points - excellent mental toughness!",
        "Your first serve percentage is 68% - try to improve consistency for better results.",
        "You're most effective when playing aggressive baseline tennis with 72% point win rate.",
      ],
      chat: [
        "I'm here to help you improve your tennis game! What would you like to work on today?",
        "That's a great question about tennis strategy. Let me help you understand the best approach.",
        "Based on your playing style, I'd recommend focusing on these key areas for improvement.",
        "Tennis is all about consistency and smart shot selection. Let's work on your game plan.",
        "Remember, every professional player started as a beginner. Keep practicing and stay positive!",
      ],
    };

    const responses = mockResponses[type];
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];

    return this.createMockResponse({
      response: randomResponse,
      confidence: 0.85 + Math.random() * 0.15, // 85-100% confidence
      tokens_used: Math.floor(Math.random() * 100) + 50,
      model: 'gpt-4-demo',
    }, 'AI response generated');
  }

  /**
   * Mock MediaPipe video analysis (replaces Replicate)
   */
  async mockMediaPipe(videoData: any, analysisType: string): Promise<MockResponse> {
    await this.simulateAPIDelay(1500); // Shorter delay for local processing

    const mockAnalysis = {
      pose_detection: {
        keypoints: [
          { name: 'nose', x: 320, y: 180, confidence: 0.95 },
          { name: 'left_shoulder', x: 280, y: 220, confidence: 0.92 },
          { name: 'right_shoulder', x: 360, y: 220, confidence: 0.91 },
          { name: 'left_elbow', x: 250, y: 280, confidence: 0.88 },
          { name: 'right_elbow', x: 390, y: 280, confidence: 0.89 },
          { name: 'left_wrist', x: 220, y: 340, confidence: 0.85 },
          { name: 'right_wrist', x: 420, y: 340, confidence: 0.87 },
        ],
        technique_score: 85,
        recommendations: [
          'Keep your eye on the ball throughout the swing',
          'Rotate your hips more for increased power',
          'Follow through completely after contact',
        ],
      },
      swing_analysis: {
        swing_speed: 78, // mph
        contact_point: { x: 380, y: 300 },
        swing_path: 'inside-out',
        timing: 'good',
        power_rating: 82,
        consistency_rating: 76,
      },
      match_highlights: {
        total_shots: 156,
        winners: 24,
        unforced_errors: 18,
        aces: 8,
        double_faults: 3,
        best_shots: [
          { timestamp: 45.2, type: 'forehand_winner', score: 95 },
          { timestamp: 127.8, type: 'ace', score: 98 },
          { timestamp: 203.5, type: 'backhand_winner', score: 88 },
        ],
      },
    };

    return this.createMockResponse({
      analysis: mockAnalysis[analysisType as keyof typeof mockAnalysis] || mockAnalysis.pose_detection,
      processing_time: 1.8, // seconds
      video_duration: 45.6, // seconds
      frames_analyzed: 1368,
    }, 'Video analysis completed');
  }

  /**
   * Mock user data
   */
  async mockUserData(userId?: string): Promise<MockResponse> {
    await this.simulateAPIDelay();

    return this.createMockResponse({
      users: DEMO_USERS,
      currentUser: DEMO_USERS[0],
      stats: {
        totalMatches: 45,
        winRate: 78,
        averageMatchDuration: 105, // minutes
        favoriteShot: 'Forehand',
        improvementRate: 15, // % improvement over last month
      },
    }, 'User data retrieved');
  }

  /**
   * Mock match data
   */
  async mockMatchData(matchId?: string): Promise<MockResponse> {
    await this.simulateAPIDelay();

    return this.createMockResponse({
      matches: DEMO_MATCHES,
      currentMatch: DEMO_MATCHES[0],
      analytics: DEMO_ANALYTICS,
    }, 'Match data retrieved');
  }

  /**
   * Mock performance analytics
   */
  async mockAnalytics(): Promise<MockResponse> {
    await this.simulateAPIDelay();

    return this.createMockResponse(DEMO_ANALYTICS, 'Analytics data retrieved');
  }

  /**
   * Mock subscription data
   */
  async mockSubscription(): Promise<MockResponse> {
    await this.simulateAPIDelay();

    return this.createMockResponse({
      plan: 'premium',
      status: 'active',
      expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
      features: [
        'AI Coaching',
        'Video Analysis',
        'Advanced Analytics',
        'Unlimited Matches',
        'Priority Support',
      ],
      billing: {
        amount: 9.99,
        currency: 'USD',
        interval: 'month',
        next_billing_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      },
    }, 'Subscription data retrieved');
  }

  /**
   * Check if we should use mock data
   */
  shouldUseMockData(): boolean {
    return this.isDemoMode || process.env.EXPO_PUBLIC_USE_MOCK_DATA === 'true';
  }

  /**
   * Intercept API calls and return mock data if in demo mode
   */
  async interceptAPICall(
    service: 'supabase' | 'openai' | 'mediapipe' | 'analytics',
    method: string,
    params?: any
  ): Promise<MockResponse | null> {
    if (!this.shouldUseMockData()) return null;

    console.log(`🎭 Mock API Call: ${service}.${method}`, params);

    switch (service) {
      case 'supabase':
        if (method.includes('auth')) {
          return this.mockAuth(params?.action || 'login', params);
        }
        if (method.includes('user')) {
          return this.mockUserData(params?.userId);
        }
        if (method.includes('match')) {
          return this.mockMatchData(params?.matchId);
        }
        break;

      case 'openai':
        return this.mockOpenAI(params?.prompt, params?.type);

      case 'mediapipe':
        return this.mockMediaPipe(params?.videoData, params?.analysisType);

      case 'analytics':
        return this.mockAnalytics();
    }

    // Default mock response
    return this.createMockResponse(
      { message: 'Mock data not implemented for this endpoint' },
      `Mock response for ${service}.${method}`
    );
  }
}

// Export singleton instance
export const mockAPIService = new MockAPIService();
export default mockAPIService;
