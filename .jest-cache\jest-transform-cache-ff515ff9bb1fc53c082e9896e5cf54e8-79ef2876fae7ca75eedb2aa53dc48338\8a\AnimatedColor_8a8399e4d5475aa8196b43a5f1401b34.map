{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_AnimatedValue", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "_normalizeColors", "_NativeAnimatedHelper", "NativeAnimatedAPI", "API", "defaultColor", "g", "b", "a", "_uniqueId", "processColorObject", "color", "processColor", "undefined", "isRgbaValue", "normalizedColor", "processedColorObj", "value", "isRgbaAnimatedValue", "AnimatedColor", "_AnimatedWithChildren2", "valueIn", "config", "_this", "_listeners", "rgbaAnimatedValue", "_processColor", "processedColor", "initColor", "nativeColor", "useNativeDriver", "__makeNative", "key", "setValue", "_processColor2", "shouldUpdateNodeConfig", "__isNative", "nativeTag", "__getNativeTag", "setWaitingForIdentifier", "toString", "rgbaValue", "_nativeTag", "updateAnimatedNodeConfig", "__getNativeConfig", "unsetWaitingForIdentifier", "setOffset", "offset", "flattenOffset", "extractOffset", "addListener", "callback", "_this2", "id", "String", "jointCallback", "_ref", "number", "__getValue", "removeListener", "removeAllListeners", "stopAnimation", "resetAnimation", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "platformConfig", "type", "module"], "sources": ["AnimatedColor.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _AnimatedValue = _interopRequireDefault(require(\"./AnimatedValue\"));\nvar _AnimatedWithChildren = _interopRequireDefault(require(\"./AnimatedWithChildren\"));\nvar _normalizeColors = _interopRequireDefault(require(\"@react-native/normalize-colors\"));\nvar _NativeAnimatedHelper = _interopRequireDefault(require(\"../NativeAnimatedHelper\"));\nvar NativeAnimatedAPI = _NativeAnimatedHelper.default.API;\nvar defaultColor = {\n  r: 0,\n  g: 0,\n  b: 0,\n  a: 1.0\n};\nvar _uniqueId = 1;\nvar processColorObject = color => {\n  return color;\n};\n\n/* eslint no-bitwise: 0 */\nfunction processColor(color) {\n  if (color === undefined || color === null) {\n    return null;\n  }\n  if (isRgbaValue(color)) {\n    // $FlowIgnore[incompatible-cast] - Type is verified above\n    return color;\n  }\n  var normalizedColor = (0, _normalizeColors.default)(\n  // $FlowIgnore[incompatible-cast] - Type is verified above\n  color);\n  if (normalizedColor === undefined || normalizedColor === null) {\n    return null;\n  }\n  if (typeof normalizedColor === 'object') {\n    var processedColorObj = processColorObject(normalizedColor);\n    if (processedColorObj != null) {\n      return processedColorObj;\n    }\n  } else if (typeof normalizedColor === 'number') {\n    var r = (normalizedColor & 0xff000000) >>> 24;\n    var g = (normalizedColor & 0x00ff0000) >>> 16;\n    var b = (normalizedColor & 0x0000ff00) >>> 8;\n    var a = (normalizedColor & 0x000000ff) / 255;\n    return {\n      r,\n      g,\n      b,\n      a\n    };\n  }\n  return null;\n}\nfunction isRgbaValue(value) {\n  return value && typeof value.r === 'number' && typeof value.g === 'number' && typeof value.b === 'number' && typeof value.a === 'number';\n}\nfunction isRgbaAnimatedValue(value) {\n  return value && value.r instanceof _AnimatedValue.default && value.g instanceof _AnimatedValue.default && value.b instanceof _AnimatedValue.default && value.a instanceof _AnimatedValue.default;\n}\nclass AnimatedColor extends _AnimatedWithChildren.default {\n  constructor(valueIn, config) {\n    super();\n    this._listeners = {};\n    var value = valueIn !== null && valueIn !== void 0 ? valueIn : defaultColor;\n    if (isRgbaAnimatedValue(value)) {\n      // $FlowIgnore[incompatible-cast] - Type is verified above\n      var rgbaAnimatedValue = value;\n      this.r = rgbaAnimatedValue.r;\n      this.g = rgbaAnimatedValue.g;\n      this.b = rgbaAnimatedValue.b;\n      this.a = rgbaAnimatedValue.a;\n    } else {\n      var _processColor;\n      var processedColor = // $FlowIgnore[incompatible-cast] - Type is verified above\n      (_processColor = processColor(value)) !== null && _processColor !== void 0 ? _processColor : defaultColor;\n      var initColor = defaultColor;\n      if (isRgbaValue(processedColor)) {\n        // $FlowIgnore[incompatible-cast] - Type is verified above\n        initColor = processedColor;\n      } else {\n        // $FlowIgnore[incompatible-cast] - Type is verified above\n        this.nativeColor = processedColor;\n      }\n      this.r = new _AnimatedValue.default(initColor.r);\n      this.g = new _AnimatedValue.default(initColor.g);\n      this.b = new _AnimatedValue.default(initColor.b);\n      this.a = new _AnimatedValue.default(initColor.a);\n    }\n    if (this.nativeColor || config && config.useNativeDriver) {\n      this.__makeNative();\n    }\n  }\n\n  /**\n   * Directly set the value. This will stop any animations running on the value\n   * and update all the bound properties.\n   */\n  setValue(value) {\n    var _processColor2;\n    var shouldUpdateNodeConfig = false;\n    if (this.__isNative) {\n      var nativeTag = this.__getNativeTag();\n      NativeAnimatedAPI.setWaitingForIdentifier(nativeTag.toString());\n    }\n    var processedColor = (_processColor2 = processColor(value)) !== null && _processColor2 !== void 0 ? _processColor2 : defaultColor;\n    if (isRgbaValue(processedColor)) {\n      // $FlowIgnore[incompatible-type] - Type is verified above\n      var rgbaValue = processedColor;\n      this.r.setValue(rgbaValue.r);\n      this.g.setValue(rgbaValue.g);\n      this.b.setValue(rgbaValue.b);\n      this.a.setValue(rgbaValue.a);\n      if (this.nativeColor != null) {\n        this.nativeColor = null;\n        shouldUpdateNodeConfig = true;\n      }\n    } else {\n      // $FlowIgnore[incompatible-type] - Type is verified above\n      var nativeColor = processedColor;\n      if (this.nativeColor !== nativeColor) {\n        this.nativeColor = nativeColor;\n        shouldUpdateNodeConfig = true;\n      }\n    }\n    if (this.__isNative) {\n      var _nativeTag = this.__getNativeTag();\n      if (shouldUpdateNodeConfig) {\n        NativeAnimatedAPI.updateAnimatedNodeConfig(_nativeTag, this.__getNativeConfig());\n      }\n      NativeAnimatedAPI.unsetWaitingForIdentifier(_nativeTag.toString());\n    }\n  }\n\n  /**\n   * Sets an offset that is applied on top of whatever value is set, whether\n   * via `setValue`, an animation, or `Animated.event`. Useful for compensating\n   * things like the start of a pan gesture.\n   */\n  setOffset(offset) {\n    this.r.setOffset(offset.r);\n    this.g.setOffset(offset.g);\n    this.b.setOffset(offset.b);\n    this.a.setOffset(offset.a);\n  }\n\n  /**\n   * Merges the offset value into the base value and resets the offset to zero.\n   * The final output of the value is unchanged.\n   */\n  flattenOffset() {\n    this.r.flattenOffset();\n    this.g.flattenOffset();\n    this.b.flattenOffset();\n    this.a.flattenOffset();\n  }\n\n  /**\n   * Sets the offset value to the base value, and resets the base value to\n   * zero. The final output of the value is unchanged.\n   */\n  extractOffset() {\n    this.r.extractOffset();\n    this.g.extractOffset();\n    this.b.extractOffset();\n    this.a.extractOffset();\n  }\n\n  /**\n   * Adds an asynchronous listener to the value so you can observe updates from\n   * animations.  This is useful because there is no way to synchronously read\n   * the value because it might be driven natively.\n   *\n   * Returns a string that serves as an identifier for the listener.\n   */\n  addListener(callback) {\n    var id = String(_uniqueId++);\n    var jointCallback = _ref => {\n      var number = _ref.value;\n      callback(this.__getValue());\n    };\n    this._listeners[id] = {\n      r: this.r.addListener(jointCallback),\n      g: this.g.addListener(jointCallback),\n      b: this.b.addListener(jointCallback),\n      a: this.a.addListener(jointCallback)\n    };\n    return id;\n  }\n\n  /**\n   * Unregister a listener. The `id` param shall match the identifier\n   * previously returned by `addListener()`.\n   */\n  removeListener(id) {\n    this.r.removeListener(this._listeners[id].r);\n    this.g.removeListener(this._listeners[id].g);\n    this.b.removeListener(this._listeners[id].b);\n    this.a.removeListener(this._listeners[id].a);\n    delete this._listeners[id];\n  }\n\n  /**\n   * Remove all registered listeners.\n   */\n  removeAllListeners() {\n    this.r.removeAllListeners();\n    this.g.removeAllListeners();\n    this.b.removeAllListeners();\n    this.a.removeAllListeners();\n    this._listeners = {};\n  }\n\n  /**\n   * Stops any running animation or tracking. `callback` is invoked with the\n   * final value after stopping the animation, which is useful for updating\n   * state to match the animation position with layout.\n   */\n  stopAnimation(callback) {\n    this.r.stopAnimation();\n    this.g.stopAnimation();\n    this.b.stopAnimation();\n    this.a.stopAnimation();\n    callback && callback(this.__getValue());\n  }\n\n  /**\n   * Stops any animation and resets the value to its original.\n   */\n  resetAnimation(callback) {\n    this.r.resetAnimation();\n    this.g.resetAnimation();\n    this.b.resetAnimation();\n    this.a.resetAnimation();\n    callback && callback(this.__getValue());\n  }\n  __getValue() {\n    if (this.nativeColor != null) {\n      return this.nativeColor;\n    } else {\n      return \"rgba(\" + this.r.__getValue() + \", \" + this.g.__getValue() + \", \" + this.b.__getValue() + \", \" + this.a.__getValue() + \")\";\n    }\n  }\n  __attach() {\n    this.r.__addChild(this);\n    this.g.__addChild(this);\n    this.b.__addChild(this);\n    this.a.__addChild(this);\n    super.__attach();\n  }\n  __detach() {\n    this.r.__removeChild(this);\n    this.g.__removeChild(this);\n    this.b.__removeChild(this);\n    this.a.__removeChild(this);\n    super.__detach();\n  }\n  __makeNative(platformConfig) {\n    this.r.__makeNative(platformConfig);\n    this.g.__makeNative(platformConfig);\n    this.b.__makeNative(platformConfig);\n    this.a.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n  __getNativeConfig() {\n    return {\n      type: 'color',\n      r: this.r.__getNativeTag(),\n      g: this.g.__getNativeTag(),\n      b: this.b.__getNativeTag(),\n      a: this.a.__getNativeTag(),\n      nativeColor: this.nativeColor\n    };\n  }\n}\nexports.default = AnimatedColor;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,cAAc,GAAGH,sBAAsB,CAACxB,OAAO,kBAAkB,CAAC,CAAC;AACvE,IAAI4B,qBAAqB,GAAGJ,sBAAsB,CAACxB,OAAO,yBAAyB,CAAC,CAAC;AACrF,IAAI6B,gBAAgB,GAAGL,sBAAsB,CAACxB,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACxF,IAAI8B,qBAAqB,GAAGN,sBAAsB,CAACxB,OAAO,0BAA0B,CAAC,CAAC;AACtF,IAAI+B,iBAAiB,GAAGD,qBAAqB,CAACnB,OAAO,CAACqB,GAAG;AACzD,IAAIC,YAAY,GAAG;EACjBX,CAAC,EAAE,CAAC;EACJY,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE,CAAC;EACJC,CAAC,EAAE;AACL,CAAC;AACD,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAGC,KAAK,EAAI;EAChC,OAAOA,KAAK;AACd,CAAC;AAGD,SAASC,YAAYA,CAACD,KAAK,EAAE;EAC3B,IAAIA,KAAK,KAAKE,SAAS,IAAIF,KAAK,KAAK,IAAI,EAAE;IACzC,OAAO,IAAI;EACb;EACA,IAAIG,WAAW,CAACH,KAAK,CAAC,EAAE;IAEtB,OAAOA,KAAK;EACd;EACA,IAAII,eAAe,GAAG,CAAC,CAAC,EAAEd,gBAAgB,CAAClB,OAAO,EAElD4B,KAAK,CAAC;EACN,IAAII,eAAe,KAAKF,SAAS,IAAIE,eAAe,KAAK,IAAI,EAAE;IAC7D,OAAO,IAAI;EACb;EACA,IAAI,OAAOA,eAAe,KAAK,QAAQ,EAAE;IACvC,IAAIC,iBAAiB,GAAGN,kBAAkB,CAACK,eAAe,CAAC;IAC3D,IAAIC,iBAAiB,IAAI,IAAI,EAAE;MAC7B,OAAOA,iBAAiB;IAC1B;EACF,CAAC,MAAM,IAAI,OAAOD,eAAe,KAAK,QAAQ,EAAE;IAC9C,IAAIrB,CAAC,GAAG,CAACqB,eAAe,GAAG,UAAU,MAAM,EAAE;IAC7C,IAAIT,CAAC,GAAG,CAACS,eAAe,GAAG,UAAU,MAAM,EAAE;IAC7C,IAAIR,CAAC,GAAG,CAACQ,eAAe,GAAG,UAAU,MAAM,CAAC;IAC5C,IAAIP,CAAC,GAAG,CAACO,eAAe,GAAG,UAAU,IAAI,GAAG;IAC5C,OAAO;MACLrB,CAAC,EAADA,CAAC;MACDY,CAAC,EAADA,CAAC;MACDC,CAAC,EAADA,CAAC;MACDC,CAAC,EAADA;IACF,CAAC;EACH;EACA,OAAO,IAAI;AACb;AACA,SAASM,WAAWA,CAACG,KAAK,EAAE;EAC1B,OAAOA,KAAK,IAAI,OAAOA,KAAK,CAACvB,CAAC,KAAK,QAAQ,IAAI,OAAOuB,KAAK,CAACX,CAAC,KAAK,QAAQ,IAAI,OAAOW,KAAK,CAACV,CAAC,KAAK,QAAQ,IAAI,OAAOU,KAAK,CAACT,CAAC,KAAK,QAAQ;AAC1I;AACA,SAASU,mBAAmBA,CAACD,KAAK,EAAE;EAClC,OAAOA,KAAK,IAAIA,KAAK,CAACvB,CAAC,YAAYK,cAAc,CAAChB,OAAO,IAAIkC,KAAK,CAACX,CAAC,YAAYP,cAAc,CAAChB,OAAO,IAAIkC,KAAK,CAACV,CAAC,YAAYR,cAAc,CAAChB,OAAO,IAAIkC,KAAK,CAACT,CAAC,YAAYT,cAAc,CAAChB,OAAO;AAClM;AAAC,IACKoC,aAAa,aAAAC,sBAAA;EACjB,SAAAD,cAAYE,OAAO,EAAEC,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAlD,gBAAA,CAAAU,OAAA,QAAAoC,aAAA;IAC3BI,KAAA,GAAA5C,UAAA,OAAAwC,aAAA;IACAI,KAAA,CAAKC,UAAU,GAAG,CAAC,CAAC;IACpB,IAAIP,KAAK,GAAGI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAGhB,YAAY;IAC3E,IAAIa,mBAAmB,CAACD,KAAK,CAAC,EAAE;MAE9B,IAAIQ,iBAAiB,GAAGR,KAAK;MAC7BM,KAAA,CAAK7B,CAAC,GAAG+B,iBAAiB,CAAC/B,CAAC;MAC5B6B,KAAA,CAAKjB,CAAC,GAAGmB,iBAAiB,CAACnB,CAAC;MAC5BiB,KAAA,CAAKhB,CAAC,GAAGkB,iBAAiB,CAAClB,CAAC;MAC5BgB,KAAA,CAAKf,CAAC,GAAGiB,iBAAiB,CAACjB,CAAC;IAC9B,CAAC,MAAM;MACL,IAAIkB,aAAa;MACjB,IAAIC,cAAc,GAClB,CAACD,aAAa,GAAGd,YAAY,CAACK,KAAK,CAAC,MAAM,IAAI,IAAIS,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAGrB,YAAY;MACzG,IAAIuB,SAAS,GAAGvB,YAAY;MAC5B,IAAIS,WAAW,CAACa,cAAc,CAAC,EAAE;QAE/BC,SAAS,GAAGD,cAAc;MAC5B,CAAC,MAAM;QAELJ,KAAA,CAAKM,WAAW,GAAGF,cAAc;MACnC;MACAJ,KAAA,CAAK7B,CAAC,GAAG,IAAIK,cAAc,CAAChB,OAAO,CAAC6C,SAAS,CAAClC,CAAC,CAAC;MAChD6B,KAAA,CAAKjB,CAAC,GAAG,IAAIP,cAAc,CAAChB,OAAO,CAAC6C,SAAS,CAACtB,CAAC,CAAC;MAChDiB,KAAA,CAAKhB,CAAC,GAAG,IAAIR,cAAc,CAAChB,OAAO,CAAC6C,SAAS,CAACrB,CAAC,CAAC;MAChDgB,KAAA,CAAKf,CAAC,GAAG,IAAIT,cAAc,CAAChB,OAAO,CAAC6C,SAAS,CAACpB,CAAC,CAAC;IAClD;IACA,IAAIe,KAAA,CAAKM,WAAW,IAAIP,MAAM,IAAIA,MAAM,CAACQ,eAAe,EAAE;MACxDP,KAAA,CAAKQ,YAAY,CAAC,CAAC;IACrB;IAAC,OAAAR,KAAA;EACH;EAAC,IAAA7C,UAAA,CAAAK,OAAA,EAAAoC,aAAA,EAAAC,sBAAA;EAAA,WAAA9C,aAAA,CAAAS,OAAA,EAAAoC,aAAA;IAAAa,GAAA;IAAAf,KAAA,EAMD,SAAAgB,QAAQA,CAAChB,KAAK,EAAE;MACd,IAAIiB,cAAc;MAClB,IAAIC,sBAAsB,GAAG,KAAK;MAClC,IAAI,IAAI,CAACC,UAAU,EAAE;QACnB,IAAIC,SAAS,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;QACrCnC,iBAAiB,CAACoC,uBAAuB,CAACF,SAAS,CAACG,QAAQ,CAAC,CAAC,CAAC;MACjE;MACA,IAAIb,cAAc,GAAG,CAACO,cAAc,GAAGtB,YAAY,CAACK,KAAK,CAAC,MAAM,IAAI,IAAIiB,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAG7B,YAAY;MACjI,IAAIS,WAAW,CAACa,cAAc,CAAC,EAAE;QAE/B,IAAIc,SAAS,GAAGd,cAAc;QAC9B,IAAI,CAACjC,CAAC,CAACuC,QAAQ,CAACQ,SAAS,CAAC/C,CAAC,CAAC;QAC5B,IAAI,CAACY,CAAC,CAAC2B,QAAQ,CAACQ,SAAS,CAACnC,CAAC,CAAC;QAC5B,IAAI,CAACC,CAAC,CAAC0B,QAAQ,CAACQ,SAAS,CAAClC,CAAC,CAAC;QAC5B,IAAI,CAACC,CAAC,CAACyB,QAAQ,CAACQ,SAAS,CAACjC,CAAC,CAAC;QAC5B,IAAI,IAAI,CAACqB,WAAW,IAAI,IAAI,EAAE;UAC5B,IAAI,CAACA,WAAW,GAAG,IAAI;UACvBM,sBAAsB,GAAG,IAAI;QAC/B;MACF,CAAC,MAAM;QAEL,IAAIN,WAAW,GAAGF,cAAc;QAChC,IAAI,IAAI,CAACE,WAAW,KAAKA,WAAW,EAAE;UACpC,IAAI,CAACA,WAAW,GAAGA,WAAW;UAC9BM,sBAAsB,GAAG,IAAI;QAC/B;MACF;MACA,IAAI,IAAI,CAACC,UAAU,EAAE;QACnB,IAAIM,UAAU,GAAG,IAAI,CAACJ,cAAc,CAAC,CAAC;QACtC,IAAIH,sBAAsB,EAAE;UAC1BhC,iBAAiB,CAACwC,wBAAwB,CAACD,UAAU,EAAE,IAAI,CAACE,iBAAiB,CAAC,CAAC,CAAC;QAClF;QACAzC,iBAAiB,CAAC0C,yBAAyB,CAACH,UAAU,CAACF,QAAQ,CAAC,CAAC,CAAC;MACpE;IACF;EAAC;IAAAR,GAAA;IAAAf,KAAA,EAOD,SAAA6B,SAASA,CAACC,MAAM,EAAE;MAChB,IAAI,CAACrD,CAAC,CAACoD,SAAS,CAACC,MAAM,CAACrD,CAAC,CAAC;MAC1B,IAAI,CAACY,CAAC,CAACwC,SAAS,CAACC,MAAM,CAACzC,CAAC,CAAC;MAC1B,IAAI,CAACC,CAAC,CAACuC,SAAS,CAACC,MAAM,CAACxC,CAAC,CAAC;MAC1B,IAAI,CAACC,CAAC,CAACsC,SAAS,CAACC,MAAM,CAACvC,CAAC,CAAC;IAC5B;EAAC;IAAAwB,GAAA;IAAAf,KAAA,EAMD,SAAA+B,aAAaA,CAAA,EAAG;MACd,IAAI,CAACtD,CAAC,CAACsD,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC1C,CAAC,CAAC0C,aAAa,CAAC,CAAC;MACtB,IAAI,CAACzC,CAAC,CAACyC,aAAa,CAAC,CAAC;MACtB,IAAI,CAACxC,CAAC,CAACwC,aAAa,CAAC,CAAC;IACxB;EAAC;IAAAhB,GAAA;IAAAf,KAAA,EAMD,SAAAgC,aAAaA,CAAA,EAAG;MACd,IAAI,CAACvD,CAAC,CAACuD,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC3C,CAAC,CAAC2C,aAAa,CAAC,CAAC;MACtB,IAAI,CAAC1C,CAAC,CAAC0C,aAAa,CAAC,CAAC;MACtB,IAAI,CAACzC,CAAC,CAACyC,aAAa,CAAC,CAAC;IACxB;EAAC;IAAAjB,GAAA;IAAAf,KAAA,EASD,SAAAiC,WAAWA,CAACC,QAAQ,EAAE;MAAA,IAAAC,MAAA;MACpB,IAAIC,EAAE,GAAGC,MAAM,CAAC7C,SAAS,EAAE,CAAC;MAC5B,IAAI8C,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,IAAI,EAAI;QAC1B,IAAIC,MAAM,GAAGD,IAAI,CAACvC,KAAK;QACvBkC,QAAQ,CAACC,MAAI,CAACM,UAAU,CAAC,CAAC,CAAC;MAC7B,CAAC;MACD,IAAI,CAAClC,UAAU,CAAC6B,EAAE,CAAC,GAAG;QACpB3D,CAAC,EAAE,IAAI,CAACA,CAAC,CAACwD,WAAW,CAACK,aAAa,CAAC;QACpCjD,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC4C,WAAW,CAACK,aAAa,CAAC;QACpChD,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC2C,WAAW,CAACK,aAAa,CAAC;QACpC/C,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC0C,WAAW,CAACK,aAAa;MACrC,CAAC;MACD,OAAOF,EAAE;IACX;EAAC;IAAArB,GAAA;IAAAf,KAAA,EAMD,SAAA0C,cAAcA,CAACN,EAAE,EAAE;MACjB,IAAI,CAAC3D,CAAC,CAACiE,cAAc,CAAC,IAAI,CAACnC,UAAU,CAAC6B,EAAE,CAAC,CAAC3D,CAAC,CAAC;MAC5C,IAAI,CAACY,CAAC,CAACqD,cAAc,CAAC,IAAI,CAACnC,UAAU,CAAC6B,EAAE,CAAC,CAAC/C,CAAC,CAAC;MAC5C,IAAI,CAACC,CAAC,CAACoD,cAAc,CAAC,IAAI,CAACnC,UAAU,CAAC6B,EAAE,CAAC,CAAC9C,CAAC,CAAC;MAC5C,IAAI,CAACC,CAAC,CAACmD,cAAc,CAAC,IAAI,CAACnC,UAAU,CAAC6B,EAAE,CAAC,CAAC7C,CAAC,CAAC;MAC5C,OAAO,IAAI,CAACgB,UAAU,CAAC6B,EAAE,CAAC;IAC5B;EAAC;IAAArB,GAAA;IAAAf,KAAA,EAKD,SAAA2C,kBAAkBA,CAAA,EAAG;MACnB,IAAI,CAAClE,CAAC,CAACkE,kBAAkB,CAAC,CAAC;MAC3B,IAAI,CAACtD,CAAC,CAACsD,kBAAkB,CAAC,CAAC;MAC3B,IAAI,CAACrD,CAAC,CAACqD,kBAAkB,CAAC,CAAC;MAC3B,IAAI,CAACpD,CAAC,CAACoD,kBAAkB,CAAC,CAAC;MAC3B,IAAI,CAACpC,UAAU,GAAG,CAAC,CAAC;IACtB;EAAC;IAAAQ,GAAA;IAAAf,KAAA,EAOD,SAAA4C,aAAaA,CAACV,QAAQ,EAAE;MACtB,IAAI,CAACzD,CAAC,CAACmE,aAAa,CAAC,CAAC;MACtB,IAAI,CAACvD,CAAC,CAACuD,aAAa,CAAC,CAAC;MACtB,IAAI,CAACtD,CAAC,CAACsD,aAAa,CAAC,CAAC;MACtB,IAAI,CAACrD,CAAC,CAACqD,aAAa,CAAC,CAAC;MACtBV,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAACO,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAA1B,GAAA;IAAAf,KAAA,EAKD,SAAA6C,cAAcA,CAACX,QAAQ,EAAE;MACvB,IAAI,CAACzD,CAAC,CAACoE,cAAc,CAAC,CAAC;MACvB,IAAI,CAACxD,CAAC,CAACwD,cAAc,CAAC,CAAC;MACvB,IAAI,CAACvD,CAAC,CAACuD,cAAc,CAAC,CAAC;MACvB,IAAI,CAACtD,CAAC,CAACsD,cAAc,CAAC,CAAC;MACvBX,QAAQ,IAAIA,QAAQ,CAAC,IAAI,CAACO,UAAU,CAAC,CAAC,CAAC;IACzC;EAAC;IAAA1B,GAAA;IAAAf,KAAA,EACD,SAAAyC,UAAUA,CAAA,EAAG;MACX,IAAI,IAAI,CAAC7B,WAAW,IAAI,IAAI,EAAE;QAC5B,OAAO,IAAI,CAACA,WAAW;MACzB,CAAC,MAAM;QACL,OAAO,OAAO,GAAG,IAAI,CAACnC,CAAC,CAACgE,UAAU,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAACpD,CAAC,CAACoD,UAAU,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAACnD,CAAC,CAACmD,UAAU,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI,CAAClD,CAAC,CAACkD,UAAU,CAAC,CAAC,GAAG,GAAG;MACnI;IACF;EAAC;IAAA1B,GAAA;IAAAf,KAAA,EACD,SAAA8C,QAAQA,CAAA,EAAG;MACT,IAAI,CAACrE,CAAC,CAACsE,UAAU,CAAC,IAAI,CAAC;MACvB,IAAI,CAAC1D,CAAC,CAAC0D,UAAU,CAAC,IAAI,CAAC;MACvB,IAAI,CAACzD,CAAC,CAACyD,UAAU,CAAC,IAAI,CAAC;MACvB,IAAI,CAACxD,CAAC,CAACwD,UAAU,CAAC,IAAI,CAAC;MACvBvE,aAAA,CAAA0B,aAAA;IACF;EAAC;IAAAa,GAAA;IAAAf,KAAA,EACD,SAAAgD,QAAQA,CAAA,EAAG;MACT,IAAI,CAACvE,CAAC,CAACwE,aAAa,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAC5D,CAAC,CAAC4D,aAAa,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAC3D,CAAC,CAAC2D,aAAa,CAAC,IAAI,CAAC;MAC1B,IAAI,CAAC1D,CAAC,CAAC0D,aAAa,CAAC,IAAI,CAAC;MAC1BzE,aAAA,CAAA0B,aAAA;IACF;EAAC;IAAAa,GAAA;IAAAf,KAAA,EACD,SAAAc,YAAYA,CAACoC,cAAc,EAAE;MAC3B,IAAI,CAACzE,CAAC,CAACqC,YAAY,CAACoC,cAAc,CAAC;MACnC,IAAI,CAAC7D,CAAC,CAACyB,YAAY,CAACoC,cAAc,CAAC;MACnC,IAAI,CAAC5D,CAAC,CAACwB,YAAY,CAACoC,cAAc,CAAC;MACnC,IAAI,CAAC3D,CAAC,CAACuB,YAAY,CAACoC,cAAc,CAAC;MACnC1E,aAAA,CAAA0B,aAAA,4BAAmBgD,cAAc;IACnC;EAAC;IAAAnC,GAAA;IAAAf,KAAA,EACD,SAAA2B,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLwB,IAAI,EAAE,OAAO;QACb1E,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC4C,cAAc,CAAC,CAAC;QAC1BhC,CAAC,EAAE,IAAI,CAACA,CAAC,CAACgC,cAAc,CAAC,CAAC;QAC1B/B,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC+B,cAAc,CAAC,CAAC;QAC1B9B,CAAC,EAAE,IAAI,CAACA,CAAC,CAAC8B,cAAc,CAAC,CAAC;QAC1BT,WAAW,EAAE,IAAI,CAACA;MACpB,CAAC;IACH;EAAC;AAAA,EArNyB7B,qBAAqB,CAACjB,OAAO;AAuNzDc,OAAO,CAACd,OAAO,GAAGoC,aAAa;AAC/BkD,MAAM,CAACxE,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}