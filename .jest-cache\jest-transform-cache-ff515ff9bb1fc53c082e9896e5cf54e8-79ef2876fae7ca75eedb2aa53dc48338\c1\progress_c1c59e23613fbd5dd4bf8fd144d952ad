da5f41714f0f7ecb2cb38b9f1c3a8fe5
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_365l8trxc() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\progress.tsx";
  var hash = "d9cb419c23315be192955a70765adbeeffaf93ec";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\progress.tsx",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 15
        },
        end: {
          line: 17,
          column: 1
        }
      },
      "1": {
        start: {
          line: 20,
          column: 46
        },
        end: {
          line: 20,
          column: 63
        }
      },
      "2": {
        start: {
          line: 21,
          column: 60
        },
        end: {
          line: 21,
          column: 77
        }
      },
      "3": {
        start: {
          line: 23,
          column: 18
        },
        end: {
          line: 27,
          column: 3
        }
      },
      "4": {
        start: {
          line: 29,
          column: 2
        },
        end: {
          line: 31,
          column: 3
        }
      },
      "5": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 30,
          column: 68
        }
      },
      "6": {
        start: {
          line: 33,
          column: 2
        },
        end: {
          line: 40,
          column: 3
        }
      },
      "7": {
        start: {
          line: 34,
          column: 4
        },
        end: {
          line: 39,
          column: 6
        }
      },
      "8": {
        start: {
          line: 42,
          column: 2
        },
        end: {
          line: 49,
          column: 3
        }
      },
      "9": {
        start: {
          line: 43,
          column: 4
        },
        end: {
          line: 48,
          column: 6
        }
      },
      "10": {
        start: {
          line: 51,
          column: 22
        },
        end: {
          line: 51,
          column: 78
        }
      },
      "11": {
        start: {
          line: 51,
          column: 63
        },
        end: {
          line: 51,
          column: 76
        }
      },
      "12": {
        start: {
          line: 53,
          column: 2
        },
        end: {
          line: 334,
          column: 4
        }
      },
      "13": {
        start: {
          line: 76,
          column: 12
        },
        end: {
          line: 92,
          column: 31
        }
      },
      "14": {
        start: {
          line: 82,
          column: 29
        },
        end: {
          line: 82,
          column: 57
        }
      },
      "15": {
        start: {
          line: 136,
          column: 14
        },
        end: {
          line: 150,
          column: 21
        }
      },
      "16": {
        start: {
          line: 160,
          column: 14
        },
        end: {
          line: 174,
          column: 21
        }
      },
      "17": {
        start: {
          line: 180,
          column: 88
        },
        end: {
          line: 180,
          column: 107
        }
      },
      "18": {
        start: {
          line: 184,
          column: 88
        },
        end: {
          line: 184,
          column: 107
        }
      },
      "19": {
        start: {
          line: 199,
          column: 34
        },
        end: {
          line: 200,
          column: 74
        }
      },
      "20": {
        start: {
          line: 201,
          column: 31
        },
        end: {
          line: 201,
          column: 65
        }
      },
      "21": {
        start: {
          line: 203,
          column: 12
        },
        end: {
          line: 226,
          column: 14
        }
      },
      "22": {
        start: {
          line: 236,
          column: 12
        },
        end: {
          line: 250,
          column: 19
        }
      },
      "23": {
        start: {
          line: 264,
          column: 12
        },
        end: {
          line: 305,
          column: 31
        }
      },
      "24": {
        start: {
          line: 317,
          column: 12
        },
        end: {
          line: 321,
          column: 19
        }
      },
      "25": {
        start: {
          line: 337,
          column: 15
        },
        end: {
          line: 706,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "ProgressScreen",
        decl: {
          start: {
            line: 19,
            column: 24
          },
          end: {
            line: 19,
            column: 38
          }
        },
        loc: {
          start: {
            line: 19,
            column: 41
          },
          end: {
            line: 335,
            column: 1
          }
        },
        line: 19
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 51,
            column: 55
          },
          end: {
            line: 51,
            column: 56
          }
        },
        loc: {
          start: {
            line: 51,
            column: 63
          },
          end: {
            line: 51,
            column: 76
          }
        },
        line: 51
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 75,
            column: 23
          },
          end: {
            line: 75,
            column: 24
          }
        },
        loc: {
          start: {
            line: 76,
            column: 12
          },
          end: {
            line: 92,
            column: 31
          }
        },
        line: 76
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 82,
            column: 23
          },
          end: {
            line: 82,
            column: 24
          }
        },
        loc: {
          start: {
            line: 82,
            column: 29
          },
          end: {
            line: 82,
            column: 57
          }
        },
        line: 82
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 135,
            column: 52
          },
          end: {
            line: 135,
            column: 53
          }
        },
        loc: {
          start: {
            line: 136,
            column: 14
          },
          end: {
            line: 150,
            column: 21
          }
        },
        line: 136
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 159,
            column: 34
          },
          end: {
            line: 159,
            column: 35
          }
        },
        loc: {
          start: {
            line: 160,
            column: 14
          },
          end: {
            line: 174,
            column: 21
          }
        },
        line: 160
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 180,
            column: 73
          },
          end: {
            line: 180,
            column: 74
          }
        },
        loc: {
          start: {
            line: 180,
            column: 88
          },
          end: {
            line: 180,
            column: 107
          }
        },
        line: 180
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 184,
            column: 73
          },
          end: {
            line: 184,
            column: 74
          }
        },
        loc: {
          start: {
            line: 184,
            column: 88
          },
          end: {
            line: 184,
            column: 107
          }
        },
        line: 184
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 198,
            column: 33
          },
          end: {
            line: 198,
            column: 34
          }
        },
        loc: {
          start: {
            line: 198,
            column: 50
          },
          end: {
            line: 227,
            column: 11
          }
        },
        line: 198
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 235,
            column: 38
          },
          end: {
            line: 235,
            column: 39
          }
        },
        loc: {
          start: {
            line: 236,
            column: 12
          },
          end: {
            line: 250,
            column: 19
          }
        },
        line: 236
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 263,
            column: 33
          },
          end: {
            line: 263,
            column: 34
          }
        },
        loc: {
          start: {
            line: 264,
            column: 12
          },
          end: {
            line: 305,
            column: 31
          }
        },
        line: 264
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 316,
            column: 31
          },
          end: {
            line: 316,
            column: 32
          }
        },
        loc: {
          start: {
            line: 317,
            column: 12
          },
          end: {
            line: 321,
            column: 19
          }
        },
        line: 317
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 29,
            column: 2
          },
          end: {
            line: 31,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 29,
            column: 2
          },
          end: {
            line: 31,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 29
      },
      "1": {
        loc: {
          start: {
            line: 29,
            column: 6
          },
          end: {
            line: 29,
            column: 22
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 29,
            column: 6
          },
          end: {
            line: 29,
            column: 13
          }
        }, {
          start: {
            line: 29,
            column: 17
          },
          end: {
            line: 29,
            column: 22
          }
        }],
        line: 29
      },
      "2": {
        loc: {
          start: {
            line: 33,
            column: 2
          },
          end: {
            line: 40,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 2
          },
          end: {
            line: 40,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 33
      },
      "3": {
        loc: {
          start: {
            line: 33,
            column: 6
          },
          end: {
            line: 33,
            column: 20
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 33,
            column: 6
          },
          end: {
            line: 33,
            column: 11
          }
        }, {
          start: {
            line: 33,
            column: 15
          },
          end: {
            line: 33,
            column: 20
          }
        }],
        line: 33
      },
      "4": {
        loc: {
          start: {
            line: 42,
            column: 2
          },
          end: {
            line: 49,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 42,
            column: 2
          },
          end: {
            line: 49,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 42
      },
      "5": {
        loc: {
          start: {
            line: 80,
            column: 16
          },
          end: {
            line: 80,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 80,
            column: 16
          },
          end: {
            line: 80,
            column: 44
          }
        }, {
          start: {
            line: 80,
            column: 48
          },
          end: {
            line: 80,
            column: 73
          }
        }],
        line: 80
      },
      "6": {
        loc: {
          start: {
            line: 87,
            column: 18
          },
          end: {
            line: 87,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 18
          },
          end: {
            line: 87,
            column: 46
          }
        }, {
          start: {
            line: 87,
            column: 50
          },
          end: {
            line: 87,
            column: 79
          }
        }],
        line: 87
      },
      "7": {
        loc: {
          start: {
            line: 166,
            column: 32
          },
          end: {
            line: 166,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 166,
            column: 50
          },
          end: {
            line: 166,
            column: 84
          }
        }, {
          start: {
            line: 166,
            column: 87
          },
          end: {
            line: 166,
            column: 88
          }
        }],
        line: 166
      },
      "8": {
        loc: {
          start: {
            line: 167,
            column: 41
          },
          end: {
            line: 167,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 167,
            column: 61
          },
          end: {
            line: 167,
            column: 75
          }
        }, {
          start: {
            line: 167,
            column: 78
          },
          end: {
            line: 167,
            column: 94
          }
        }],
        line: 167
      },
      "9": {
        loc: {
          start: {
            line: 199,
            column: 34
          },
          end: {
            line: 200,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 199,
            column: 61
          },
          end: {
            line: 199,
            column: 69
          }
        }, {
          start: {
            line: 200,
            column: 31
          },
          end: {
            line: 200,
            column: 74
          }
        }],
        line: 199
      },
      "10": {
        loc: {
          start: {
            line: 200,
            column: 31
          },
          end: {
            line: 200,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 200,
            column: 56
          },
          end: {
            line: 200,
            column: 62
          }
        }, {
          start: {
            line: 200,
            column: 65
          },
          end: {
            line: 200,
            column: 74
          }
        }],
        line: 200
      },
      "11": {
        loc: {
          start: {
            line: 240,
            column: 47
          },
          end: {
            line: 240,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 240,
            column: 66
          },
          end: {
            line: 240,
            column: 80
          }
        }, {
          start: {
            line: 240,
            column: 83
          },
          end: {
            line: 240,
            column: 94
          }
        }],
        line: 240
      },
      "12": {
        loc: {
          start: {
            line: 243,
            column: 29
          },
          end: {
            line: 243,
            column: 76
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 243,
            column: 48
          },
          end: {
            line: 243,
            column: 62
          }
        }, {
          start: {
            line: 243,
            column: 65
          },
          end: {
            line: 243,
            column: 76
          }
        }],
        line: 243
      },
      "13": {
        loc: {
          start: {
            line: 245,
            column: 21
          },
          end: {
            line: 245,
            column: 48
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 245,
            column: 40
          },
          end: {
            line: 245,
            column: 43
          }
        }, {
          start: {
            line: 245,
            column: 46
          },
          end: {
            line: 245,
            column: 48
          }
        }],
        line: 245
      },
      "14": {
        loc: {
          start: {
            line: 269,
            column: 37
          },
          end: {
            line: 271,
            column: 40
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 270,
            column: 24
          },
          end: {
            line: 270,
            column: 41
          }
        }, {
          start: {
            line: 271,
            column: 24
          },
          end: {
            line: 271,
            column: 40
          }
        }],
        line: 269
      },
      "15": {
        loc: {
          start: {
            line: 277,
            column: 25
          },
          end: {
            line: 277,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 277,
            column: 48
          },
          end: {
            line: 277,
            column: 60
          }
        }, {
          start: {
            line: 277,
            column: 63
          },
          end: {
            line: 277,
            column: 74
          }
        }],
        line: 277
      },
      "16": {
        loc: {
          start: {
            line: 285,
            column: 20
          },
          end: {
            line: 285,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 20
          },
          end: {
            line: 285,
            column: 41
          }
        }, {
          start: {
            line: 285,
            column: 45
          },
          end: {
            line: 285,
            column: 74
          }
        }],
        line: 285
      },
      "17": {
        loc: {
          start: {
            line: 293,
            column: 17
          },
          end: {
            line: 301,
            column: 24
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 294,
            column: 18
          },
          end: {
            line: 296,
            column: 25
          }
        }, {
          start: {
            line: 297,
            column: 20
          },
          end: {
            line: 301,
            column: 24
          }
        }],
        line: 293
      },
      "18": {
        loc: {
          start: {
            line: 297,
            column: 20
          },
          end: {
            line: 301,
            column: 24
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 298,
            column: 18
          },
          end: {
            line: 300,
            column: 25
          }
        }, {
          start: {
            line: 301,
            column: 20
          },
          end: {
            line: 301,
            column: 24
          }
        }],
        line: 297
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d9cb419c23315be192955a70765adbeeffaf93ec"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_365l8trxc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_365l8trxc();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity, RefreshControl } from 'react-native';
import Card from "../../components/ui/Card";
import ProgressRing from "../../components/ui/ProgressRing";
import LoadingState from "../../components/ui/LoadingState";
import ErrorState from "../../components/ui/ErrorState";
import { useProgressData } from "../../hooks/useProgressData";
import { TrendingUp, Calendar, Award, Target, ChartBar as BarChart3, ChevronRight, Share2, Download, Zap } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_365l8trxc().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
export default function ProgressScreen() {
  cov_365l8trxc().f[0]++;
  var _ref = (cov_365l8trxc().s[1]++, useState('month')),
    _ref2 = _slicedToArray(_ref, 2),
    selectedPeriod = _ref2[0],
    setSelectedPeriod = _ref2[1];
  var _ref3 = (cov_365l8trxc().s[2]++, useProgressData()),
    data = _ref3.data,
    loading = _ref3.loading,
    error = _ref3.error,
    refreshing = _ref3.refreshing,
    refreshData = _ref3.refreshData;
  var periods = (cov_365l8trxc().s[3]++, [{
    id: 'week',
    label: 'Week'
  }, {
    id: 'month',
    label: 'Month'
  }, {
    id: 'year',
    label: 'Year'
  }]);
  cov_365l8trxc().s[4]++;
  if ((cov_365l8trxc().b[1][0]++, loading) && (cov_365l8trxc().b[1][1]++, !data)) {
    cov_365l8trxc().b[0][0]++;
    cov_365l8trxc().s[5]++;
    return _jsx(LoadingState, {
      message: "Loading your progress data..."
    });
  } else {
    cov_365l8trxc().b[0][1]++;
  }
  cov_365l8trxc().s[6]++;
  if ((cov_365l8trxc().b[3][0]++, error) && (cov_365l8trxc().b[3][1]++, !data)) {
    cov_365l8trxc().b[2][0]++;
    cov_365l8trxc().s[7]++;
    return _jsx(ErrorState, {
      message: error,
      onRetry: refreshData
    });
  } else {
    cov_365l8trxc().b[2][1]++;
  }
  cov_365l8trxc().s[8]++;
  if (!data) {
    cov_365l8trxc().b[4][0]++;
    cov_365l8trxc().s[9]++;
    return _jsx(ErrorState, {
      message: "No progress data available",
      onRetry: refreshData
    });
  } else {
    cov_365l8trxc().b[4][1]++;
  }
  var maxSessions = (cov_365l8trxc().s[10]++, Math.max.apply(Math, _toConsumableArray(data.weeklyStats.map(function (stat) {
    cov_365l8trxc().f[1]++;
    cov_365l8trxc().s[11]++;
    return stat.sessions;
  }))));
  cov_365l8trxc().s[12]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(ScrollView, {
      style: styles.scrollView,
      showsVerticalScrollIndicator: false,
      refreshControl: _jsx(RefreshControl, {
        refreshing: refreshing,
        onRefresh: refreshData,
        colors: [colors.primary],
        tintColor: colors.primary
      }),
      children: [_jsxs(View, {
        style: styles.header,
        children: [_jsx(Text, {
          style: styles.title,
          children: "Your Progress"
        }), _jsx(Text, {
          style: styles.subtitle,
          children: "Track your tennis improvement journey"
        })]
      }), _jsx(View, {
        style: styles.periodSelector,
        children: periods.map(function (period) {
          cov_365l8trxc().f[2]++;
          cov_365l8trxc().s[13]++;
          return _jsx(TouchableOpacity, {
            style: [styles.periodButton, (cov_365l8trxc().b[5][0]++, selectedPeriod === period.id) && (cov_365l8trxc().b[5][1]++, styles.periodButtonActive)],
            onPress: function onPress() {
              cov_365l8trxc().f[3]++;
              cov_365l8trxc().s[14]++;
              return setSelectedPeriod(period.id);
            },
            children: _jsx(Text, {
              style: [styles.periodButtonText, (cov_365l8trxc().b[6][0]++, selectedPeriod === period.id) && (cov_365l8trxc().b[6][1]++, styles.periodButtonTextActive)],
              children: period.label
            })
          }, period.id);
        })
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.summaryCard,
        children: [_jsxs(View, {
          style: styles.summaryHeader,
          children: [_jsx(Text, {
            style: styles.sectionTitle,
            children: "Progress Summary"
          }), _jsx(TouchableOpacity, {
            style: styles.shareButton,
            children: _jsx(Share2, {
              size: 20,
              color: colors.primary
            })
          })]
        }), _jsxs(View, {
          style: styles.summaryStats,
          children: [_jsxs(View, {
            style: styles.summaryItem,
            children: [_jsx(Text, {
              style: styles.summaryValue,
              children: data.overallProgress.totalSessions
            }), _jsx(Text, {
              style: styles.summaryLabel,
              children: "Total Sessions"
            })]
          }), _jsxs(View, {
            style: styles.summaryItem,
            children: [_jsxs(Text, {
              style: styles.summaryValue,
              children: [data.overallProgress.hoursPlayed, "h"]
            }), _jsx(Text, {
              style: styles.summaryLabel,
              children: "Hours Played"
            })]
          }), _jsxs(View, {
            style: styles.summaryItem,
            children: [_jsxs(Text, {
              style: styles.summaryValue,
              children: ["+", data.overallProgress.skillImprovement]
            }), _jsx(Text, {
              style: styles.summaryLabel,
              children: "Avg Improvement"
            })]
          }), _jsxs(View, {
            style: styles.summaryItem,
            children: [_jsx(Text, {
              style: styles.summaryValue,
              children: data.overallProgress.currentStreak
            }), _jsx(Text, {
              style: styles.summaryLabel,
              children: "Day Streak"
            })]
          })]
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.skillsCard,
        children: [_jsxs(View, {
          style: styles.sectionHeader,
          children: [_jsx(Text, {
            style: styles.sectionTitle,
            children: "Skill Development"
          }), _jsx(TouchableOpacity, {
            children: _jsx(Text, {
              style: styles.viewAllLink,
              children: "View Details"
            })
          })]
        }), _jsx(View, {
          style: styles.skillsGrid,
          children: Object.entries(data.skillProgress).map(function (_ref4) {
            var _ref5 = _slicedToArray(_ref4, 2),
              skill = _ref5[0],
              skillData = _ref5[1];
            cov_365l8trxc().f[4]++;
            cov_365l8trxc().s[15]++;
            return _jsxs(View, {
              style: styles.skillItem,
              children: [_jsx(ProgressRing, {
                progress: skillData.current,
                size: 70,
                strokeWidth: 6,
                value: `${skillData.current}%`
              }), _jsx(Text, {
                style: styles.skillName,
                children: skill.charAt(0).toUpperCase() + skill.slice(1)
              }), _jsxs(View, {
                style: styles.skillChange,
                children: [_jsx(TrendingUp, {
                  size: 12,
                  color: colors.primary
                }), _jsxs(Text, {
                  style: styles.skillChangeText,
                  children: ["+", skillData.change, "%"]
                })]
              })]
            }, skill);
          })
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.activityCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Weekly Activity"
        }), _jsx(View, {
          style: styles.weeklyChart,
          children: data.weeklyStats.map(function (stat, index) {
            cov_365l8trxc().f[5]++;
            cov_365l8trxc().s[16]++;
            return _jsxs(View, {
              style: styles.chartDay,
              children: [_jsx(View, {
                style: styles.chartBarContainer,
                children: _jsx(View, {
                  style: [styles.chartBar, {
                    height: maxSessions > 0 ? (cov_365l8trxc().b[7][0]++, stat.sessions / maxSessions * 60) : (cov_365l8trxc().b[7][1]++, 0),
                    backgroundColor: stat.sessions > 0 ? (cov_365l8trxc().b[8][0]++, colors.primary) : (cov_365l8trxc().b[8][1]++, colors.lightGray)
                  }]
                })
              }), _jsx(Text, {
                style: styles.chartDayLabel,
                children: stat.day
              }), _jsx(Text, {
                style: styles.chartSessions,
                children: stat.sessions
              })]
            }, index);
          })
        }), _jsxs(View, {
          style: styles.activitySummary,
          children: [_jsxs(View, {
            style: styles.summaryItem,
            children: [_jsx(Text, {
              style: styles.summaryValue,
              children: data.weeklyStats.reduce(function (sum, stat) {
                cov_365l8trxc().f[6]++;
                cov_365l8trxc().s[17]++;
                return sum + stat.sessions;
              }, 0)
            }), _jsx(Text, {
              style: styles.summaryLabel,
              children: "Sessions"
            })]
          }), _jsxs(View, {
            style: styles.summaryItem,
            children: [_jsx(Text, {
              style: styles.summaryValue,
              children: data.weeklyStats.reduce(function (sum, stat) {
                cov_365l8trxc().f[7]++;
                cov_365l8trxc().s[18]++;
                return sum + stat.duration;
              }, 0)
            }), _jsx(Text, {
              style: styles.summaryLabel,
              children: "Minutes"
            })]
          }), _jsxs(View, {
            style: styles.summaryItem,
            children: [_jsxs(Text, {
              style: styles.summaryValue,
              children: ["+", data.overallProgress.weeklyImprovement, "%"]
            }), _jsx(Text, {
              style: styles.summaryLabel,
              children: "vs Last Week"
            })]
          })]
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.goalsCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Monthly Goals"
        }), data.monthlyGoals.map(function (goal, index) {
          cov_365l8trxc().f[8]++;
          var IconComponent = (cov_365l8trxc().s[19]++, goal.icon === 'calendar' ? (cov_365l8trxc().b[9][0]++, Calendar) : (cov_365l8trxc().b[9][1]++, goal.icon === 'target' ? (cov_365l8trxc().b[10][0]++, Target) : (cov_365l8trxc().b[10][1]++, BarChart3)));
          var percentage = (cov_365l8trxc().s[20]++, goal.progress / goal.total * 100);
          cov_365l8trxc().s[21]++;
          return _jsxs(View, {
            style: styles.goalItem,
            children: [_jsxs(View, {
              style: styles.goalHeader,
              children: [_jsx(View, {
                style: styles.goalIcon,
                children: _jsx(IconComponent, {
                  size: 16,
                  color: colors.primary
                })
              }), _jsxs(View, {
                style: styles.goalContent,
                children: [_jsx(Text, {
                  style: styles.goalText,
                  children: goal.goal
                }), _jsxs(Text, {
                  style: styles.goalProgress,
                  children: [goal.progress, " / ", goal.total]
                })]
              }), _jsxs(Text, {
                style: styles.goalPercentage,
                children: [percentage.toFixed(0), "%"]
              })]
            }), _jsx(View, {
              style: styles.goalProgressBar,
              children: _jsx(View, {
                style: [styles.goalProgressFill, {
                  width: `${percentage}%`
                }]
              })
            })]
          }, index);
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.trendsCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Performance Trends"
        }), _jsx(Text, {
          style: styles.sectionSubtitle,
          children: "Your improvement over time"
        }), data.performanceTrends.map(function (trend, index) {
          cov_365l8trxc().f[9]++;
          cov_365l8trxc().s[22]++;
          return _jsxs(View, {
            style: styles.trendItem,
            children: [_jsxs(View, {
              style: styles.trendHeader,
              children: [_jsx(Text, {
                style: styles.trendMetric,
                children: trend.metric
              }), _jsxs(View, {
                style: styles.trendChange,
                children: [_jsx(TrendingUp, {
                  size: 14,
                  color: trend.change > 0 ? (cov_365l8trxc().b[11][0]++, colors.primary) : (cov_365l8trxc().b[11][1]++, colors.gray)
                }), _jsxs(Text, {
                  style: [styles.trendChangeText, {
                    color: trend.change > 0 ? (cov_365l8trxc().b[12][0]++, colors.primary) : (cov_365l8trxc().b[12][1]++, colors.gray)
                  }],
                  children: [trend.change > 0 ? (cov_365l8trxc().b[13][0]++, '+') : (cov_365l8trxc().b[13][1]++, ''), trend.change, "%"]
                })]
              })]
            }), _jsx(Text, {
              style: styles.trendDescription,
              children: trend.description
            })]
          }, index);
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.achievementsCard,
        children: [_jsxs(View, {
          style: styles.sectionHeader,
          children: [_jsx(Text, {
            style: styles.sectionTitle,
            children: "Achievements"
          }), _jsx(TouchableOpacity, {
            children: _jsx(Text, {
              style: styles.viewAllLink,
              children: "View All"
            })
          })]
        }), data.achievements.map(function (achievement) {
          cov_365l8trxc().f[10]++;
          cov_365l8trxc().s[23]++;
          return _jsxs(TouchableOpacity, {
            style: styles.achievementItem,
            children: [_jsx(View, {
              style: [styles.achievementIcon, {
                backgroundColor: achievement.unlocked ? (cov_365l8trxc().b[14][0]++, achievement.color) : (cov_365l8trxc().b[14][1]++, colors.lightGray)
              }],
              children: _jsx(Award, {
                size: 20,
                color: achievement.unlocked ? (cov_365l8trxc().b[15][0]++, colors.white) : (cov_365l8trxc().b[15][1]++, colors.gray)
              })
            }), _jsxs(View, {
              style: styles.achievementContent,
              children: [_jsx(Text, {
                style: [styles.achievementTitle, (cov_365l8trxc().b[16][0]++, !achievement.unlocked) && (cov_365l8trxc().b[16][1]++, styles.achievementTitleLocked)],
                children: achievement.title
              }), _jsx(Text, {
                style: styles.achievementDescription,
                children: achievement.description
              }), achievement.unlocked ? (cov_365l8trxc().b[17][0]++, _jsxs(Text, {
                style: styles.achievementDate,
                children: ["Unlocked ", new Date(achievement.unlocked_at).toLocaleDateString()]
              })) : (cov_365l8trxc().b[17][1]++, achievement.progress !== undefined ? (cov_365l8trxc().b[18][0]++, _jsxs(Text, {
                style: styles.achievementProgress,
                children: [achievement.progress, " / ", achievement.total]
              })) : (cov_365l8trxc().b[18][1]++, null))]
            }), _jsx(ChevronRight, {
              size: 16,
              color: colors.gray
            })]
          }, achievement.id);
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.insightsCard,
        children: [_jsxs(View, {
          style: styles.insightsHeader,
          children: [_jsx(Zap, {
            size: 20,
            color: colors.yellow
          }), _jsx(Text, {
            style: styles.sectionTitle,
            children: "AI Insights"
          })]
        }), data.aiInsights.map(function (insight, index) {
          cov_365l8trxc().f[11]++;
          cov_365l8trxc().s[24]++;
          return _jsxs(View, {
            style: styles.insightItem,
            children: [_jsx(Text, {
              style: styles.insightTitle,
              children: insight.title
            }), _jsx(Text, {
              style: styles.insightDescription,
              children: insight.description
            }), _jsx(Text, {
              style: styles.insightRecommendation,
              children: insight.recommendation
            })]
          }, index);
        })]
      }), _jsx(View, {
        style: styles.exportSection,
        children: _jsxs(TouchableOpacity, {
          style: styles.exportButton,
          children: [_jsx(Download, {
            size: 20,
            color: colors.primary
          }), _jsx(Text, {
            style: styles.exportButtonText,
            children: "Export Progress Report"
          })]
        })
      })]
    })
  });
}
var styles = (cov_365l8trxc().s[25]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray
  },
  scrollView: {
    flex: 1
  },
  header: {
    padding: 24,
    paddingBottom: 16
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: colors.dark
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4
  },
  periodSelector: {
    flexDirection: 'row',
    marginHorizontal: 24,
    marginBottom: 20,
    backgroundColor: colors.lightGray,
    borderRadius: 12,
    padding: 4
  },
  periodButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderRadius: 8
  },
  periodButtonActive: {
    backgroundColor: colors.white,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  periodButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.gray
  },
  periodButtonTextActive: {
    color: colors.dark
  },
  summaryCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  summaryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  shareButton: {
    padding: 8
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-between'
  },
  summaryItem: {
    alignItems: 'center',
    flex: 1
  },
  summaryValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: colors.dark
  },
  summaryLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4,
    textAlign: 'center'
  },
  skillsCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 20
  },
  viewAllLink: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.primary
  },
  skillsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between'
  },
  skillItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 20
  },
  skillName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.dark,
    marginTop: 8,
    textAlign: 'center'
  },
  skillChange: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4
  },
  skillChangeText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: colors.primary,
    marginLeft: 4
  },
  activityCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  weeklyChart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    paddingHorizontal: 8
  },
  chartDay: {
    alignItems: 'center',
    flex: 1
  },
  chartBarContainer: {
    height: 60,
    justifyContent: 'flex-end',
    marginBottom: 8
  },
  chartBar: {
    width: 20,
    backgroundColor: colors.primary,
    borderRadius: 2,
    minHeight: 2
  },
  chartDayLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: colors.gray,
    marginBottom: 2
  },
  chartSessions: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  activitySummary: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray
  },
  goalsCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  goalItem: {
    marginBottom: 20
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8
  },
  goalIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12
  },
  goalContent: {
    flex: 1
  },
  goalText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.dark
  },
  goalProgress: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 2
  },
  goalPercentage: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary
  },
  goalProgressBar: {
    height: 4,
    backgroundColor: colors.lightGray,
    borderRadius: 2,
    marginLeft: 44
  },
  goalProgressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2
  },
  trendsCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  trendItem: {
    marginBottom: 16
  },
  trendHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4
  },
  trendMetric: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  trendChange: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  trendChangeText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    marginLeft: 4
  },
  trendDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 18
  },
  achievementsCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  achievementIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16
  },
  achievementContent: {
    flex: 1
  },
  achievementTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  achievementTitleLocked: {
    opacity: 0.6
  },
  achievementDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4
  },
  achievementDate: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.primary,
    marginTop: 4
  },
  achievementProgress: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4
  },
  insightsCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  insightsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  insightItem: {
    marginBottom: 16
  },
  insightTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 4
  },
  insightDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 18,
    marginBottom: 4
  },
  insightRecommendation: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.primary
  },
  exportSection: {
    padding: 24,
    paddingTop: 8
  },
  exportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.primary,
    backgroundColor: colors.white
  },
  exportButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary,
    marginLeft: 8
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiVmlldyIsIlRleHQiLCJTdHlsZVNoZWV0IiwiU2Nyb2xsVmlldyIsIlNhZmVBcmVhVmlldyIsIlRvdWNoYWJsZU9wYWNpdHkiLCJSZWZyZXNoQ29udHJvbCIsIkNhcmQiLCJQcm9ncmVzc1JpbmciLCJMb2FkaW5nU3RhdGUiLCJFcnJvclN0YXRlIiwidXNlUHJvZ3Jlc3NEYXRhIiwiVHJlbmRpbmdVcCIsIkNhbGVuZGFyIiwiQXdhcmQiLCJUYXJnZXQiLCJDaGFydEJhciIsIkJhckNoYXJ0MyIsIkNoZXZyb25SaWdodCIsIlNoYXJlMiIsIkRvd25sb2FkIiwiWmFwIiwianN4IiwiX2pzeCIsImpzeHMiLCJfanN4cyIsImNvbG9ycyIsImNvdl8zNjVsOHRyeGMiLCJzIiwicHJpbWFyeSIsInllbGxvdyIsIndoaXRlIiwiZGFyayIsImdyYXkiLCJsaWdodEdyYXkiLCJQcm9ncmVzc1NjcmVlbiIsImYiLCJfcmVmIiwiX3JlZjIiLCJfc2xpY2VkVG9BcnJheSIsInNlbGVjdGVkUGVyaW9kIiwic2V0U2VsZWN0ZWRQZXJpb2QiLCJfcmVmMyIsImRhdGEiLCJsb2FkaW5nIiwiZXJyb3IiLCJyZWZyZXNoaW5nIiwicmVmcmVzaERhdGEiLCJwZXJpb2RzIiwiaWQiLCJsYWJlbCIsImIiLCJtZXNzYWdlIiwib25SZXRyeSIsIm1heFNlc3Npb25zIiwiTWF0aCIsIm1heCIsImFwcGx5IiwiX3RvQ29uc3VtYWJsZUFycmF5Iiwid2Vla2x5U3RhdHMiLCJtYXAiLCJzdGF0Iiwic2Vzc2lvbnMiLCJzdHlsZSIsInN0eWxlcyIsImNvbnRhaW5lciIsImNoaWxkcmVuIiwic2Nyb2xsVmlldyIsInNob3dzVmVydGljYWxTY3JvbGxJbmRpY2F0b3IiLCJyZWZyZXNoQ29udHJvbCIsIm9uUmVmcmVzaCIsInRpbnRDb2xvciIsImhlYWRlciIsInRpdGxlIiwic3VidGl0bGUiLCJwZXJpb2RTZWxlY3RvciIsInBlcmlvZCIsInBlcmlvZEJ1dHRvbiIsInBlcmlvZEJ1dHRvbkFjdGl2ZSIsIm9uUHJlc3MiLCJwZXJpb2RCdXR0b25UZXh0IiwicGVyaW9kQnV0dG9uVGV4dEFjdGl2ZSIsInZhcmlhbnQiLCJzdW1tYXJ5Q2FyZCIsInN1bW1hcnlIZWFkZXIiLCJzZWN0aW9uVGl0bGUiLCJzaGFyZUJ1dHRvbiIsInNpemUiLCJjb2xvciIsInN1bW1hcnlTdGF0cyIsInN1bW1hcnlJdGVtIiwic3VtbWFyeVZhbHVlIiwib3ZlcmFsbFByb2dyZXNzIiwidG90YWxTZXNzaW9ucyIsInN1bW1hcnlMYWJlbCIsImhvdXJzUGxheWVkIiwic2tpbGxJbXByb3ZlbWVudCIsImN1cnJlbnRTdHJlYWsiLCJza2lsbHNDYXJkIiwic2VjdGlvbkhlYWRlciIsInZpZXdBbGxMaW5rIiwic2tpbGxzR3JpZCIsIk9iamVjdCIsImVudHJpZXMiLCJza2lsbFByb2dyZXNzIiwiX3JlZjQiLCJfcmVmNSIsInNraWxsIiwic2tpbGxEYXRhIiwic2tpbGxJdGVtIiwicHJvZ3Jlc3MiLCJjdXJyZW50Iiwic3Ryb2tlV2lkdGgiLCJ2YWx1ZSIsInNraWxsTmFtZSIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJza2lsbENoYW5nZSIsInNraWxsQ2hhbmdlVGV4dCIsImNoYW5nZSIsImFjdGl2aXR5Q2FyZCIsIndlZWtseUNoYXJ0IiwiaW5kZXgiLCJjaGFydERheSIsImNoYXJ0QmFyQ29udGFpbmVyIiwiY2hhcnRCYXIiLCJoZWlnaHQiLCJiYWNrZ3JvdW5kQ29sb3IiLCJjaGFydERheUxhYmVsIiwiZGF5IiwiY2hhcnRTZXNzaW9ucyIsImFjdGl2aXR5U3VtbWFyeSIsInJlZHVjZSIsInN1bSIsImR1cmF0aW9uIiwid2Vla2x5SW1wcm92ZW1lbnQiLCJnb2Fsc0NhcmQiLCJtb250aGx5R29hbHMiLCJnb2FsIiwiSWNvbkNvbXBvbmVudCIsImljb24iLCJwZXJjZW50YWdlIiwidG90YWwiLCJnb2FsSXRlbSIsImdvYWxIZWFkZXIiLCJnb2FsSWNvbiIsImdvYWxDb250ZW50IiwiZ29hbFRleHQiLCJnb2FsUHJvZ3Jlc3MiLCJnb2FsUGVyY2VudGFnZSIsInRvRml4ZWQiLCJnb2FsUHJvZ3Jlc3NCYXIiLCJnb2FsUHJvZ3Jlc3NGaWxsIiwid2lkdGgiLCJ0cmVuZHNDYXJkIiwic2VjdGlvblN1YnRpdGxlIiwicGVyZm9ybWFuY2VUcmVuZHMiLCJ0cmVuZCIsInRyZW5kSXRlbSIsInRyZW5kSGVhZGVyIiwidHJlbmRNZXRyaWMiLCJtZXRyaWMiLCJ0cmVuZENoYW5nZSIsInRyZW5kQ2hhbmdlVGV4dCIsInRyZW5kRGVzY3JpcHRpb24iLCJkZXNjcmlwdGlvbiIsImFjaGlldmVtZW50c0NhcmQiLCJhY2hpZXZlbWVudHMiLCJhY2hpZXZlbWVudCIsImFjaGlldmVtZW50SXRlbSIsImFjaGlldmVtZW50SWNvbiIsInVubG9ja2VkIiwiYWNoaWV2ZW1lbnRDb250ZW50IiwiYWNoaWV2ZW1lbnRUaXRsZSIsImFjaGlldmVtZW50VGl0bGVMb2NrZWQiLCJhY2hpZXZlbWVudERlc2NyaXB0aW9uIiwiYWNoaWV2ZW1lbnREYXRlIiwiRGF0ZSIsInVubG9ja2VkX2F0IiwidG9Mb2NhbGVEYXRlU3RyaW5nIiwidW5kZWZpbmVkIiwiYWNoaWV2ZW1lbnRQcm9ncmVzcyIsImluc2lnaHRzQ2FyZCIsImluc2lnaHRzSGVhZGVyIiwiYWlJbnNpZ2h0cyIsImluc2lnaHQiLCJpbnNpZ2h0SXRlbSIsImluc2lnaHRUaXRsZSIsImluc2lnaHREZXNjcmlwdGlvbiIsImluc2lnaHRSZWNvbW1lbmRhdGlvbiIsInJlY29tbWVuZGF0aW9uIiwiZXhwb3J0U2VjdGlvbiIsImV4cG9ydEJ1dHRvbiIsImV4cG9ydEJ1dHRvblRleHQiLCJjcmVhdGUiLCJmbGV4IiwicGFkZGluZyIsInBhZGRpbmdCb3R0b20iLCJmb250U2l6ZSIsImZvbnRGYW1pbHkiLCJtYXJnaW5Ub3AiLCJmbGV4RGlyZWN0aW9uIiwibWFyZ2luSG9yaXpvbnRhbCIsIm1hcmdpbkJvdHRvbSIsImJvcmRlclJhZGl1cyIsInBhZGRpbmdWZXJ0aWNhbCIsImFsaWduSXRlbXMiLCJzaGFkb3dDb2xvciIsInNoYWRvd09mZnNldCIsInNoYWRvd09wYWNpdHkiLCJzaGFkb3dSYWRpdXMiLCJlbGV2YXRpb24iLCJqdXN0aWZ5Q29udGVudCIsInRleHRBbGlnbiIsImZsZXhXcmFwIiwibWFyZ2luTGVmdCIsInBhZGRpbmdIb3Jpem9udGFsIiwibWluSGVpZ2h0IiwicGFkZGluZ1RvcCIsImJvcmRlclRvcFdpZHRoIiwiYm9yZGVyVG9wQ29sb3IiLCJtYXJnaW5SaWdodCIsImxpbmVIZWlnaHQiLCJib3JkZXJCb3R0b21XaWR0aCIsImJvcmRlckJvdHRvbUNvbG9yIiwib3BhY2l0eSIsImJvcmRlcldpZHRoIiwiYm9yZGVyQ29sb3IiXSwic291cmNlcyI6WyJwcm9ncmVzcy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgVmlldywgVGV4dCwgU3R5bGVTaGVldCwgU2Nyb2xsVmlldywgU2FmZUFyZWFWaWV3LCBUb3VjaGFibGVPcGFjaXR5LCBSZWZyZXNoQ29udHJvbCB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQgQ2FyZCBmcm9tICdAL2NvbXBvbmVudHMvdWkvQ2FyZCc7XG5pbXBvcnQgUHJvZ3Jlc3NSaW5nIGZyb20gJ0AvY29tcG9uZW50cy91aS9Qcm9ncmVzc1JpbmcnO1xuaW1wb3J0IExvYWRpbmdTdGF0ZSBmcm9tICdAL2NvbXBvbmVudHMvdWkvTG9hZGluZ1N0YXRlJztcbmltcG9ydCBFcnJvclN0YXRlIGZyb20gJ0AvY29tcG9uZW50cy91aS9FcnJvclN0YXRlJztcbmltcG9ydCB7IHVzZVByb2dyZXNzRGF0YSB9IGZyb20gJ0AvaG9va3MvdXNlUHJvZ3Jlc3NEYXRhJztcbmltcG9ydCB7IFRyZW5kaW5nVXAsIENhbGVuZGFyLCBBd2FyZCwgVGFyZ2V0LCBDaGFydEJhciBhcyBCYXJDaGFydDMsIFRyb3BoeSwgU3RhciwgQ2hldnJvblJpZ2h0LCBTaGFyZTIsIERvd25sb2FkLCBaYXAgfSBmcm9tICdsdWNpZGUtcmVhY3QtbmF0aXZlJztcblxuY29uc3QgY29sb3JzID0ge1xuICBwcmltYXJ5OiAnIzIzYmExNicsXG4gIHllbGxvdzogJyNmZmU2MDAnLFxuICB3aGl0ZTogJyNmZmZmZmYnLFxuICBkYXJrOiAnIzE3MTcxNycsXG4gIGdyYXk6ICcjNmI3MjgwJyxcbiAgbGlnaHRHcmF5OiAnI2Y5ZmFmYicsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm9ncmVzc1NjcmVlbigpIHtcbiAgY29uc3QgW3NlbGVjdGVkUGVyaW9kLCBzZXRTZWxlY3RlZFBlcmlvZF0gPSB1c2VTdGF0ZSgnbW9udGgnKTtcbiAgY29uc3QgeyBkYXRhLCBsb2FkaW5nLCBlcnJvciwgcmVmcmVzaGluZywgcmVmcmVzaERhdGEgfSA9IHVzZVByb2dyZXNzRGF0YSgpO1xuXG4gIGNvbnN0IHBlcmlvZHMgPSBbXG4gICAgeyBpZDogJ3dlZWsnLCBsYWJlbDogJ1dlZWsnIH0sXG4gICAgeyBpZDogJ21vbnRoJywgbGFiZWw6ICdNb250aCcgfSxcbiAgICB7IGlkOiAneWVhcicsIGxhYmVsOiAnWWVhcicgfSxcbiAgXTtcblxuICBpZiAobG9hZGluZyAmJiAhZGF0YSkge1xuICAgIHJldHVybiA8TG9hZGluZ1N0YXRlIG1lc3NhZ2U9XCJMb2FkaW5nIHlvdXIgcHJvZ3Jlc3MgZGF0YS4uLlwiIC8+O1xuICB9XG5cbiAgaWYgKGVycm9yICYmICFkYXRhKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxFcnJvclN0YXRlIFxuICAgICAgICBtZXNzYWdlPXtlcnJvcn1cbiAgICAgICAgb25SZXRyeT17cmVmcmVzaERhdGF9XG4gICAgICAvPlxuICAgICk7XG4gIH1cblxuICBpZiAoIWRhdGEpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPEVycm9yU3RhdGUgXG4gICAgICAgIG1lc3NhZ2U9XCJObyBwcm9ncmVzcyBkYXRhIGF2YWlsYWJsZVwiXG4gICAgICAgIG9uUmV0cnk9e3JlZnJlc2hEYXRhfVxuICAgICAgLz5cbiAgICApO1xuICB9XG5cbiAgY29uc3QgbWF4U2Vzc2lvbnMgPSBNYXRoLm1heCguLi5kYXRhLndlZWtseVN0YXRzLm1hcChzdGF0ID0+IHN0YXQuc2Vzc2lvbnMpKTtcblxuICByZXR1cm4gKFxuICAgIDxTYWZlQXJlYVZpZXcgc3R5bGU9e3N0eWxlcy5jb250YWluZXJ9PlxuICAgICAgPFNjcm9sbFZpZXcgXG4gICAgICAgIHN0eWxlPXtzdHlsZXMuc2Nyb2xsVmlld30gXG4gICAgICAgIHNob3dzVmVydGljYWxTY3JvbGxJbmRpY2F0b3I9e2ZhbHNlfVxuICAgICAgICByZWZyZXNoQ29udHJvbD17XG4gICAgICAgICAgPFJlZnJlc2hDb250cm9sXG4gICAgICAgICAgICByZWZyZXNoaW5nPXtyZWZyZXNoaW5nfVxuICAgICAgICAgICAgb25SZWZyZXNoPXtyZWZyZXNoRGF0YX1cbiAgICAgICAgICAgIGNvbG9ycz17W2NvbG9ycy5wcmltYXJ5XX1cbiAgICAgICAgICAgIHRpbnRDb2xvcj17Y29sb3JzLnByaW1hcnl9XG4gICAgICAgICAgLz5cbiAgICAgICAgfVxuICAgICAgPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLmhlYWRlcn0+XG4gICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy50aXRsZX0+WW91ciBQcm9ncmVzczwvVGV4dD5cbiAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnN1YnRpdGxlfT5UcmFjayB5b3VyIHRlbm5pcyBpbXByb3ZlbWVudCBqb3VybmV5PC9UZXh0PlxuICAgICAgICA8L1ZpZXc+XG5cbiAgICAgICAgey8qIFBlcmlvZCBTZWxlY3RvciAqL31cbiAgICAgICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5wZXJpb2RTZWxlY3Rvcn0+XG4gICAgICAgICAge3BlcmlvZHMubWFwKChwZXJpb2QpID0+IChcbiAgICAgICAgICAgIDxUb3VjaGFibGVPcGFjaXR5XG4gICAgICAgICAgICAgIGtleT17cGVyaW9kLmlkfVxuICAgICAgICAgICAgICBzdHlsZT17W1xuICAgICAgICAgICAgICAgIHN0eWxlcy5wZXJpb2RCdXR0b24sXG4gICAgICAgICAgICAgICAgc2VsZWN0ZWRQZXJpb2QgPT09IHBlcmlvZC5pZCAmJiBzdHlsZXMucGVyaW9kQnV0dG9uQWN0aXZlLFxuICAgICAgICAgICAgICBdfVxuICAgICAgICAgICAgICBvblByZXNzPXsoKSA9PiBzZXRTZWxlY3RlZFBlcmlvZChwZXJpb2QuaWQpfVxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICA8VGV4dFxuICAgICAgICAgICAgICAgIHN0eWxlPXtbXG4gICAgICAgICAgICAgICAgICBzdHlsZXMucGVyaW9kQnV0dG9uVGV4dCxcbiAgICAgICAgICAgICAgICAgIHNlbGVjdGVkUGVyaW9kID09PSBwZXJpb2QuaWQgJiYgc3R5bGVzLnBlcmlvZEJ1dHRvblRleHRBY3RpdmUsXG4gICAgICAgICAgICAgICAgXX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIHtwZXJpb2QubGFiZWx9XG4gICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgIDwvVG91Y2hhYmxlT3BhY2l0eT5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9WaWV3PlxuXG4gICAgICAgIHsvKiBPdmVyYWxsIFByb2dyZXNzIFN1bW1hcnkgKi99XG4gICAgICAgIDxDYXJkIHZhcmlhbnQ9XCJlbGV2YXRlZFwiIHN0eWxlPXtzdHlsZXMuc3VtbWFyeUNhcmR9PlxuICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuc3VtbWFyeUhlYWRlcn0+XG4gICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnNlY3Rpb25UaXRsZX0+UHJvZ3Jlc3MgU3VtbWFyeTwvVGV4dD5cbiAgICAgICAgICAgIDxUb3VjaGFibGVPcGFjaXR5IHN0eWxlPXtzdHlsZXMuc2hhcmVCdXR0b259PlxuICAgICAgICAgICAgICA8U2hhcmUyIHNpemU9ezIwfSBjb2xvcj17Y29sb3JzLnByaW1hcnl9IC8+XG4gICAgICAgICAgICA8L1RvdWNoYWJsZU9wYWNpdHk+XG4gICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgIFxuICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuc3VtbWFyeVN0YXRzfT5cbiAgICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuc3VtbWFyeUl0ZW19PlxuICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnN1bW1hcnlWYWx1ZX0+e2RhdGEub3ZlcmFsbFByb2dyZXNzLnRvdGFsU2Vzc2lvbnN9PC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnN1bW1hcnlMYWJlbH0+VG90YWwgU2Vzc2lvbnM8L1RleHQ+XG4gICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLnN1bW1hcnlJdGVtfT5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5zdW1tYXJ5VmFsdWV9PntkYXRhLm92ZXJhbGxQcm9ncmVzcy5ob3Vyc1BsYXllZH1oPC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnN1bW1hcnlMYWJlbH0+SG91cnMgUGxheWVkPC9UZXh0PlxuICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5zdW1tYXJ5SXRlbX0+XG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuc3VtbWFyeVZhbHVlfT4re2RhdGEub3ZlcmFsbFByb2dyZXNzLnNraWxsSW1wcm92ZW1lbnR9PC9UZXh0PlxuICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnN1bW1hcnlMYWJlbH0+QXZnIEltcHJvdmVtZW50PC9UZXh0PlxuICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5zdW1tYXJ5SXRlbX0+XG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuc3VtbWFyeVZhbHVlfT57ZGF0YS5vdmVyYWxsUHJvZ3Jlc3MuY3VycmVudFN0cmVha308L1RleHQ+XG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuc3VtbWFyeUxhYmVsfT5EYXkgU3RyZWFrPC9UZXh0PlxuICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBTa2lsbCBEZXZlbG9wbWVudCAqL31cbiAgICAgICAgPENhcmQgdmFyaWFudD1cImVsZXZhdGVkXCIgc3R5bGU9e3N0eWxlcy5za2lsbHNDYXJkfT5cbiAgICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLnNlY3Rpb25IZWFkZXJ9PlxuICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5zZWN0aW9uVGl0bGV9PlNraWxsIERldmVsb3BtZW50PC9UZXh0PlxuICAgICAgICAgICAgPFRvdWNoYWJsZU9wYWNpdHk+XG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMudmlld0FsbExpbmt9PlZpZXcgRGV0YWlsczwvVGV4dD5cbiAgICAgICAgICAgIDwvVG91Y2hhYmxlT3BhY2l0eT5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgXG4gICAgICAgICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5za2lsbHNHcmlkfT5cbiAgICAgICAgICAgIHtPYmplY3QuZW50cmllcyhkYXRhLnNraWxsUHJvZ3Jlc3MpLm1hcCgoW3NraWxsLCBza2lsbERhdGFdKSA9PiAoXG4gICAgICAgICAgICAgIDxWaWV3IGtleT17c2tpbGx9IHN0eWxlPXtzdHlsZXMuc2tpbGxJdGVtfT5cbiAgICAgICAgICAgICAgICA8UHJvZ3Jlc3NSaW5nXG4gICAgICAgICAgICAgICAgICBwcm9ncmVzcz17c2tpbGxEYXRhLmN1cnJlbnR9XG4gICAgICAgICAgICAgICAgICBzaXplPXs3MH1cbiAgICAgICAgICAgICAgICAgIHN0cm9rZVdpZHRoPXs2fVxuICAgICAgICAgICAgICAgICAgdmFsdWU9e2Ake3NraWxsRGF0YS5jdXJyZW50fSVgfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5za2lsbE5hbWV9PlxuICAgICAgICAgICAgICAgICAge3NraWxsLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgc2tpbGwuc2xpY2UoMSl9XG4gICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuc2tpbGxDaGFuZ2V9PlxuICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgc2l6ZT17MTJ9IGNvbG9yPXtjb2xvcnMucHJpbWFyeX0gLz5cbiAgICAgICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuc2tpbGxDaGFuZ2VUZXh0fT4re3NraWxsRGF0YS5jaGFuZ2V9JTwvVGV4dD5cbiAgICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBXZWVrbHkgQWN0aXZpdHkgQ2hhcnQgKi99XG4gICAgICAgIDxDYXJkIHZhcmlhbnQ9XCJlbGV2YXRlZFwiIHN0eWxlPXtzdHlsZXMuYWN0aXZpdHlDYXJkfT5cbiAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnNlY3Rpb25UaXRsZX0+V2Vla2x5IEFjdGl2aXR5PC9UZXh0PlxuICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMud2Vla2x5Q2hhcnR9PlxuICAgICAgICAgICAge2RhdGEud2Vla2x5U3RhdHMubWFwKChzdGF0LCBpbmRleCkgPT4gKFxuICAgICAgICAgICAgICA8VmlldyBrZXk9e2luZGV4fSBzdHlsZT17c3R5bGVzLmNoYXJ0RGF5fT5cbiAgICAgICAgICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLmNoYXJ0QmFyQ29udGFpbmVyfT5cbiAgICAgICAgICAgICAgICAgIDxWaWV3XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXtbXG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGVzLmNoYXJ0QmFyLFxuICAgICAgICAgICAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGhlaWdodDogbWF4U2Vzc2lvbnMgPiAwID8gKHN0YXQuc2Vzc2lvbnMgLyBtYXhTZXNzaW9ucykgKiA2MCA6IDAsXG4gICAgICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IHN0YXQuc2Vzc2lvbnMgPiAwID8gY29sb3JzLnByaW1hcnkgOiBjb2xvcnMubGlnaHRHcmF5LFxuICAgICAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgICAgIF19XG4gICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLmNoYXJ0RGF5TGFiZWx9PntzdGF0LmRheX08L1RleHQ+XG4gICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5jaGFydFNlc3Npb25zfT57c3RhdC5zZXNzaW9uc308L1RleHQ+XG4gICAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICBcbiAgICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLmFjdGl2aXR5U3VtbWFyeX0+XG4gICAgICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLnN1bW1hcnlJdGVtfT5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5zdW1tYXJ5VmFsdWV9PntkYXRhLndlZWtseVN0YXRzLnJlZHVjZSgoc3VtLCBzdGF0KSA9PiBzdW0gKyBzdGF0LnNlc3Npb25zLCAwKX08L1RleHQ+XG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuc3VtbWFyeUxhYmVsfT5TZXNzaW9uczwvVGV4dD5cbiAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuc3VtbWFyeUl0ZW19PlxuICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnN1bW1hcnlWYWx1ZX0+e2RhdGEud2Vla2x5U3RhdHMucmVkdWNlKChzdW0sIHN0YXQpID0+IHN1bSArIHN0YXQuZHVyYXRpb24sIDApfTwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5zdW1tYXJ5TGFiZWx9Pk1pbnV0ZXM8L1RleHQ+XG4gICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLnN1bW1hcnlJdGVtfT5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5zdW1tYXJ5VmFsdWV9Pit7ZGF0YS5vdmVyYWxsUHJvZ3Jlc3Mud2Vla2x5SW1wcm92ZW1lbnR9JTwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5zdW1tYXJ5TGFiZWx9PnZzIExhc3QgV2VlazwvVGV4dD5cbiAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogTW9udGhseSBHb2FscyAqL31cbiAgICAgICAgPENhcmQgdmFyaWFudD1cImVsZXZhdGVkXCIgc3R5bGU9e3N0eWxlcy5nb2Fsc0NhcmR9PlxuICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuc2VjdGlvblRpdGxlfT5Nb250aGx5IEdvYWxzPC9UZXh0PlxuICAgICAgICAgIFxuICAgICAgICAgIHtkYXRhLm1vbnRobHlHb2Fscy5tYXAoKGdvYWwsIGluZGV4KSA9PiB7XG4gICAgICAgICAgICBjb25zdCBJY29uQ29tcG9uZW50ID0gZ29hbC5pY29uID09PSAnY2FsZW5kYXInID8gQ2FsZW5kYXIgOiBcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBnb2FsLmljb24gPT09ICd0YXJnZXQnID8gVGFyZ2V0IDogQmFyQ2hhcnQzO1xuICAgICAgICAgICAgY29uc3QgcGVyY2VudGFnZSA9IChnb2FsLnByb2dyZXNzIC8gZ29hbC50b3RhbCkgKiAxMDA7XG4gICAgICAgICAgICBcbiAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgIDxWaWV3IGtleT17aW5kZXh9IHN0eWxlPXtzdHlsZXMuZ29hbEl0ZW19PlxuICAgICAgICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuZ29hbEhlYWRlcn0+XG4gICAgICAgICAgICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLmdvYWxJY29ufT5cbiAgICAgICAgICAgICAgICAgICAgPEljb25Db21wb25lbnQgc2l6ZT17MTZ9IGNvbG9yPXtjb2xvcnMucHJpbWFyeX0gLz5cbiAgICAgICAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuZ29hbENvbnRlbnR9PlxuICAgICAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLmdvYWxUZXh0fT57Z29hbC5nb2FsfTwvVGV4dD5cbiAgICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5nb2FsUHJvZ3Jlc3N9PlxuICAgICAgICAgICAgICAgICAgICAgIHtnb2FsLnByb2dyZXNzfSAvIHtnb2FsLnRvdGFsfVxuICAgICAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLmdvYWxQZXJjZW50YWdlfT57cGVyY2VudGFnZS50b0ZpeGVkKDApfSU8L1RleHQ+XG4gICAgICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuZ29hbFByb2dyZXNzQmFyfT5cbiAgICAgICAgICAgICAgICAgIDxWaWV3XG4gICAgICAgICAgICAgICAgICAgIHN0eWxlPXtbXG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGVzLmdvYWxQcm9ncmVzc0ZpbGwsXG4gICAgICAgICAgICAgICAgICAgICAgeyB3aWR0aDogYCR7cGVyY2VudGFnZX0lYCB9LFxuICAgICAgICAgICAgICAgICAgICBdfVxuICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfSl9XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogUGVyZm9ybWFuY2UgVHJlbmRzICovfVxuICAgICAgICA8Q2FyZCB2YXJpYW50PVwiZWxldmF0ZWRcIiBzdHlsZT17c3R5bGVzLnRyZW5kc0NhcmR9PlxuICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuc2VjdGlvblRpdGxlfT5QZXJmb3JtYW5jZSBUcmVuZHM8L1RleHQ+XG4gICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5zZWN0aW9uU3VidGl0bGV9PllvdXIgaW1wcm92ZW1lbnQgb3ZlciB0aW1lPC9UZXh0PlxuICAgICAgICAgIFxuICAgICAgICAgIHtkYXRhLnBlcmZvcm1hbmNlVHJlbmRzLm1hcCgodHJlbmQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8VmlldyBrZXk9e2luZGV4fSBzdHlsZT17c3R5bGVzLnRyZW5kSXRlbX0+XG4gICAgICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMudHJlbmRIZWFkZXJ9PlxuICAgICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMudHJlbmRNZXRyaWN9Pnt0cmVuZC5tZXRyaWN9PC9UZXh0PlxuICAgICAgICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMudHJlbmRDaGFuZ2V9PlxuICAgICAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgc2l6ZT17MTR9IGNvbG9yPXt0cmVuZC5jaGFuZ2UgPiAwID8gY29sb3JzLnByaW1hcnkgOiBjb2xvcnMuZ3JheX0gLz5cbiAgICAgICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtbXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlcy50cmVuZENoYW5nZVRleHQsXG4gICAgICAgICAgICAgICAgICAgIHsgY29sb3I6IHRyZW5kLmNoYW5nZSA+IDAgPyBjb2xvcnMucHJpbWFyeSA6IGNvbG9ycy5ncmF5IH1cbiAgICAgICAgICAgICAgICAgIF19PlxuICAgICAgICAgICAgICAgICAgICB7dHJlbmQuY2hhbmdlID4gMCA/ICcrJyA6ICcnfXt0cmVuZC5jaGFuZ2V9JVxuICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnRyZW5kRGVzY3JpcHRpb259Pnt0cmVuZC5kZXNjcmlwdGlvbn08L1RleHQ+XG4gICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgKSl9XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogQWNoaWV2ZW1lbnRzICovfVxuICAgICAgICA8Q2FyZCB2YXJpYW50PVwiZWxldmF0ZWRcIiBzdHlsZT17c3R5bGVzLmFjaGlldmVtZW50c0NhcmR9PlxuICAgICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuc2VjdGlvbkhlYWRlcn0+XG4gICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLnNlY3Rpb25UaXRsZX0+QWNoaWV2ZW1lbnRzPC9UZXh0PlxuICAgICAgICAgICAgPFRvdWNoYWJsZU9wYWNpdHk+XG4gICAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMudmlld0FsbExpbmt9PlZpZXcgQWxsPC9UZXh0PlxuICAgICAgICAgICAgPC9Ub3VjaGFibGVPcGFjaXR5PlxuICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICBcbiAgICAgICAgICB7ZGF0YS5hY2hpZXZlbWVudHMubWFwKChhY2hpZXZlbWVudCkgPT4gKFxuICAgICAgICAgICAgPFRvdWNoYWJsZU9wYWNpdHkga2V5PXthY2hpZXZlbWVudC5pZH0gc3R5bGU9e3N0eWxlcy5hY2hpZXZlbWVudEl0ZW19PlxuICAgICAgICAgICAgICA8Vmlld1xuICAgICAgICAgICAgICAgIHN0eWxlPXtbXG4gICAgICAgICAgICAgICAgICBzdHlsZXMuYWNoaWV2ZW1lbnRJY29uLFxuICAgICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICBiYWNrZ3JvdW5kQ29sb3I6IGFjaGlldmVtZW50LnVubG9ja2VkXG4gICAgICAgICAgICAgICAgICAgICAgPyBhY2hpZXZlbWVudC5jb2xvclxuICAgICAgICAgICAgICAgICAgICAgIDogY29sb3JzLmxpZ2h0R3JheSxcbiAgICAgICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgICAgXX1cbiAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgIDxBd2FyZFxuICAgICAgICAgICAgICAgICAgc2l6ZT17MjB9XG4gICAgICAgICAgICAgICAgICBjb2xvcj17YWNoaWV2ZW1lbnQudW5sb2NrZWQgPyBjb2xvcnMud2hpdGUgOiBjb2xvcnMuZ3JheX1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgICAgIFxuICAgICAgICAgICAgICA8VmlldyBzdHlsZT17c3R5bGVzLmFjaGlldmVtZW50Q29udGVudH0+XG4gICAgICAgICAgICAgICAgPFRleHRcbiAgICAgICAgICAgICAgICAgIHN0eWxlPXtbXG4gICAgICAgICAgICAgICAgICAgIHN0eWxlcy5hY2hpZXZlbWVudFRpdGxlLFxuICAgICAgICAgICAgICAgICAgICAhYWNoaWV2ZW1lbnQudW5sb2NrZWQgJiYgc3R5bGVzLmFjaGlldmVtZW50VGl0bGVMb2NrZWQsXG4gICAgICAgICAgICAgICAgICBdfVxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIHthY2hpZXZlbWVudC50aXRsZX1cbiAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5hY2hpZXZlbWVudERlc2NyaXB0aW9ufT5cbiAgICAgICAgICAgICAgICAgIHthY2hpZXZlbWVudC5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgICA8L1RleHQ+XG4gICAgICAgICAgICAgICAge2FjaGlldmVtZW50LnVubG9ja2VkID8gKFxuICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5hY2hpZXZlbWVudERhdGV9PlxuICAgICAgICAgICAgICAgICAgICBVbmxvY2tlZCB7bmV3IERhdGUoYWNoaWV2ZW1lbnQudW5sb2NrZWRfYXQpLnRvTG9jYWxlRGF0ZVN0cmluZygpfVxuICAgICAgICAgICAgICAgICAgPC9UZXh0PlxuICAgICAgICAgICAgICAgICkgOiBhY2hpZXZlbWVudC5wcm9ncmVzcyAhPT0gdW5kZWZpbmVkID8gKFxuICAgICAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5hY2hpZXZlbWVudFByb2dyZXNzfT5cbiAgICAgICAgICAgICAgICAgICAge2FjaGlldmVtZW50LnByb2dyZXNzfSAvIHthY2hpZXZlbWVudC50b3RhbH1cbiAgICAgICAgICAgICAgICAgIDwvVGV4dD5cbiAgICAgICAgICAgICAgICApIDogbnVsbH1cbiAgICAgICAgICAgICAgPC9WaWV3PlxuICAgICAgICAgICAgICBcbiAgICAgICAgICAgICAgPENoZXZyb25SaWdodCBzaXplPXsxNn0gY29sb3I9e2NvbG9ycy5ncmF5fSAvPlxuICAgICAgICAgICAgPC9Ub3VjaGFibGVPcGFjaXR5PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIEFJIEluc2lnaHRzICovfVxuICAgICAgICA8Q2FyZCB2YXJpYW50PVwiZWxldmF0ZWRcIiBzdHlsZT17c3R5bGVzLmluc2lnaHRzQ2FyZH0+XG4gICAgICAgICAgPFZpZXcgc3R5bGU9e3N0eWxlcy5pbnNpZ2h0c0hlYWRlcn0+XG4gICAgICAgICAgICA8WmFwIHNpemU9ezIwfSBjb2xvcj17Y29sb3JzLnllbGxvd30gLz5cbiAgICAgICAgICAgIDxUZXh0IHN0eWxlPXtzdHlsZXMuc2VjdGlvblRpdGxlfT5BSSBJbnNpZ2h0czwvVGV4dD5cbiAgICAgICAgICA8L1ZpZXc+XG4gICAgICAgICAgXG4gICAgICAgICAge2RhdGEuYWlJbnNpZ2h0cy5tYXAoKGluc2lnaHQsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8VmlldyBrZXk9e2luZGV4fSBzdHlsZT17c3R5bGVzLmluc2lnaHRJdGVtfT5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5pbnNpZ2h0VGl0bGV9PntpbnNpZ2h0LnRpdGxlfTwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5pbnNpZ2h0RGVzY3JpcHRpb259PntpbnNpZ2h0LmRlc2NyaXB0aW9ufTwvVGV4dD5cbiAgICAgICAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy5pbnNpZ2h0UmVjb21tZW5kYXRpb259PntpbnNpZ2h0LnJlY29tbWVuZGF0aW9ufTwvVGV4dD5cbiAgICAgICAgICAgIDwvVmlldz5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9DYXJkPlxuXG4gICAgICAgIHsvKiBFeHBvcnQgUHJvZ3Jlc3MgKi99XG4gICAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuZXhwb3J0U2VjdGlvbn0+XG4gICAgICAgICAgPFRvdWNoYWJsZU9wYWNpdHkgc3R5bGU9e3N0eWxlcy5leHBvcnRCdXR0b259PlxuICAgICAgICAgICAgPERvd25sb2FkIHNpemU9ezIwfSBjb2xvcj17Y29sb3JzLnByaW1hcnl9IC8+XG4gICAgICAgICAgICA8VGV4dCBzdHlsZT17c3R5bGVzLmV4cG9ydEJ1dHRvblRleHR9PkV4cG9ydCBQcm9ncmVzcyBSZXBvcnQ8L1RleHQ+XG4gICAgICAgICAgPC9Ub3VjaGFibGVPcGFjaXR5PlxuICAgICAgICA8L1ZpZXc+XG4gICAgICA8L1Njcm9sbFZpZXc+XG4gICAgPC9TYWZlQXJlYVZpZXc+XG4gICk7XG59XG5cbmNvbnN0IHN0eWxlcyA9IFN0eWxlU2hlZXQuY3JlYXRlKHtcbiAgY29udGFpbmVyOiB7XG4gICAgZmxleDogMSxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5saWdodEdyYXksXG4gIH0sXG4gIHNjcm9sbFZpZXc6IHtcbiAgICBmbGV4OiAxLFxuICB9LFxuICBoZWFkZXI6IHtcbiAgICBwYWRkaW5nOiAyNCxcbiAgICBwYWRkaW5nQm90dG9tOiAxNixcbiAgfSxcbiAgdGl0bGU6IHtcbiAgICBmb250U2l6ZTogMjgsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLUJvbGQnLFxuICAgIGNvbG9yOiBjb2xvcnMuZGFyayxcbiAgfSxcbiAgc3VidGl0bGU6IHtcbiAgICBmb250U2l6ZTogMTYsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLVJlZ3VsYXInLFxuICAgIGNvbG9yOiBjb2xvcnMuZ3JheSxcbiAgICBtYXJnaW5Ub3A6IDQsXG4gIH0sXG4gIHBlcmlvZFNlbGVjdG9yOiB7XG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgbWFyZ2luSG9yaXpvbnRhbDogMjQsXG4gICAgbWFyZ2luQm90dG9tOiAyMCxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy5saWdodEdyYXksXG4gICAgYm9yZGVyUmFkaXVzOiAxMixcbiAgICBwYWRkaW5nOiA0LFxuICB9LFxuICBwZXJpb2RCdXR0b246IHtcbiAgICBmbGV4OiAxLFxuICAgIHBhZGRpbmdWZXJ0aWNhbDogMTIsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgYm9yZGVyUmFkaXVzOiA4LFxuICB9LFxuICBwZXJpb2RCdXR0b25BY3RpdmU6IHtcbiAgICBiYWNrZ3JvdW5kQ29sb3I6IGNvbG9ycy53aGl0ZSxcbiAgICBzaGFkb3dDb2xvcjogJyMwMDAnLFxuICAgIHNoYWRvd09mZnNldDoge1xuICAgICAgd2lkdGg6IDAsXG4gICAgICBoZWlnaHQ6IDEsXG4gICAgfSxcbiAgICBzaGFkb3dPcGFjaXR5OiAwLjEsXG4gICAgc2hhZG93UmFkaXVzOiAyLFxuICAgIGVsZXZhdGlvbjogMixcbiAgfSxcbiAgcGVyaW9kQnV0dG9uVGV4dDoge1xuICAgIGZvbnRTaXplOiAxNCxcbiAgICBmb250RmFtaWx5OiAnSW50ZXItTWVkaXVtJyxcbiAgICBjb2xvcjogY29sb3JzLmdyYXksXG4gIH0sXG4gIHBlcmlvZEJ1dHRvblRleHRBY3RpdmU6IHtcbiAgICBjb2xvcjogY29sb3JzLmRhcmssXG4gIH0sXG4gIHN1bW1hcnlDYXJkOiB7XG4gICAgbWFyZ2luSG9yaXpvbnRhbDogMjQsXG4gICAgbWFyZ2luQm90dG9tOiAyMCxcbiAgfSxcbiAgc3VtbWFyeUhlYWRlcjoge1xuICAgIGZsZXhEaXJlY3Rpb246ICdyb3cnLFxuICAgIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYmV0d2VlbicsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgbWFyZ2luQm90dG9tOiAyMCxcbiAgfSxcbiAgc2hhcmVCdXR0b246IHtcbiAgICBwYWRkaW5nOiA4LFxuICB9LFxuICBzdW1tYXJ5U3RhdHM6IHtcbiAgICBmbGV4RGlyZWN0aW9uOiAncm93JyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxuICB9LFxuICBzdW1tYXJ5SXRlbToge1xuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIGZsZXg6IDEsXG4gIH0sXG4gIHN1bW1hcnlWYWx1ZToge1xuICAgIGZvbnRTaXplOiAyMCxcbiAgICBmb250RmFtaWx5OiAnSW50ZXItQm9sZCcsXG4gICAgY29sb3I6IGNvbG9ycy5kYXJrLFxuICB9LFxuICBzdW1tYXJ5TGFiZWw6IHtcbiAgICBmb250U2l6ZTogMTIsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLVJlZ3VsYXInLFxuICAgIGNvbG9yOiBjb2xvcnMuZ3JheSxcbiAgICBtYXJnaW5Ub3A6IDQsXG4gICAgdGV4dEFsaWduOiAnY2VudGVyJyxcbiAgfSxcbiAgc2tpbGxzQ2FyZDoge1xuICAgIG1hcmdpbkhvcml6b250YWw6IDI0LFxuICAgIG1hcmdpbkJvdHRvbTogMjAsXG4gIH0sXG4gIHNlY3Rpb25IZWFkZXI6IHtcbiAgICBmbGV4RGlyZWN0aW9uOiAncm93JyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIG1hcmdpbkJvdHRvbTogMjAsXG4gIH0sXG4gIHNlY3Rpb25UaXRsZToge1xuICAgIGZvbnRTaXplOiAxOCxcbiAgICBmb250RmFtaWx5OiAnSW50ZXItU2VtaUJvbGQnLFxuICAgIGNvbG9yOiBjb2xvcnMuZGFyayxcbiAgfSxcbiAgc2VjdGlvblN1YnRpdGxlOiB7XG4gICAgZm9udFNpemU6IDE0LFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1SZWd1bGFyJyxcbiAgICBjb2xvcjogY29sb3JzLmdyYXksXG4gICAgbWFyZ2luQm90dG9tOiAyMCxcbiAgfSxcbiAgdmlld0FsbExpbms6IHtcbiAgICBmb250U2l6ZTogMTQsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLU1lZGl1bScsXG4gICAgY29sb3I6IGNvbG9ycy5wcmltYXJ5LFxuICB9LFxuICBza2lsbHNHcmlkOiB7XG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgZmxleFdyYXA6ICd3cmFwJyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxuICB9LFxuICBza2lsbEl0ZW06IHtcbiAgICB3aWR0aDogJzQ4JScsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgbWFyZ2luQm90dG9tOiAyMCxcbiAgfSxcbiAgc2tpbGxOYW1lOiB7XG4gICAgZm9udFNpemU6IDE0LFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1NZWRpdW0nLFxuICAgIGNvbG9yOiBjb2xvcnMuZGFyayxcbiAgICBtYXJnaW5Ub3A6IDgsXG4gICAgdGV4dEFsaWduOiAnY2VudGVyJyxcbiAgfSxcbiAgc2tpbGxDaGFuZ2U6IHtcbiAgICBmbGV4RGlyZWN0aW9uOiAncm93JyxcbiAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcbiAgICBtYXJnaW5Ub3A6IDQsXG4gIH0sXG4gIHNraWxsQ2hhbmdlVGV4dDoge1xuICAgIGZvbnRTaXplOiAxMixcbiAgICBmb250RmFtaWx5OiAnSW50ZXItTWVkaXVtJyxcbiAgICBjb2xvcjogY29sb3JzLnByaW1hcnksXG4gICAgbWFyZ2luTGVmdDogNCxcbiAgfSxcbiAgYWN0aXZpdHlDYXJkOiB7XG4gICAgbWFyZ2luSG9yaXpvbnRhbDogMjQsXG4gICAgbWFyZ2luQm90dG9tOiAyMCxcbiAgfSxcbiAgd2Vla2x5Q2hhcnQ6IHtcbiAgICBmbGV4RGlyZWN0aW9uOiAncm93JyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxuICAgIG1hcmdpbkJvdHRvbTogMjAsXG4gICAgcGFkZGluZ0hvcml6b250YWw6IDgsXG4gIH0sXG4gIGNoYXJ0RGF5OiB7XG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgZmxleDogMSxcbiAgfSxcbiAgY2hhcnRCYXJDb250YWluZXI6IHtcbiAgICBoZWlnaHQ6IDYwLFxuICAgIGp1c3RpZnlDb250ZW50OiAnZmxleC1lbmQnLFxuICAgIG1hcmdpbkJvdHRvbTogOCxcbiAgfSxcbiAgY2hhcnRCYXI6IHtcbiAgICB3aWR0aDogMjAsXG4gICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMucHJpbWFyeSxcbiAgICBib3JkZXJSYWRpdXM6IDIsXG4gICAgbWluSGVpZ2h0OiAyLFxuICB9LFxuICBjaGFydERheUxhYmVsOiB7XG4gICAgZm9udFNpemU6IDEyLFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1NZWRpdW0nLFxuICAgIGNvbG9yOiBjb2xvcnMuZ3JheSxcbiAgICBtYXJnaW5Cb3R0b206IDIsXG4gIH0sXG4gIGNoYXJ0U2Vzc2lvbnM6IHtcbiAgICBmb250U2l6ZTogMTIsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLVNlbWlCb2xkJyxcbiAgICBjb2xvcjogY29sb3JzLmRhcmssXG4gIH0sXG4gIGFjdGl2aXR5U3VtbWFyeToge1xuICAgIGZsZXhEaXJlY3Rpb246ICdyb3cnLFxuICAgIGp1c3RpZnlDb250ZW50OiAnc3BhY2UtYXJvdW5kJyxcbiAgICBwYWRkaW5nVG9wOiAyMCxcbiAgICBib3JkZXJUb3BXaWR0aDogMSxcbiAgICBib3JkZXJUb3BDb2xvcjogY29sb3JzLmxpZ2h0R3JheSxcbiAgfSxcbiAgZ29hbHNDYXJkOiB7XG4gICAgbWFyZ2luSG9yaXpvbnRhbDogMjQsXG4gICAgbWFyZ2luQm90dG9tOiAyMCxcbiAgfSxcbiAgZ29hbEl0ZW06IHtcbiAgICBtYXJnaW5Cb3R0b206IDIwLFxuICB9LFxuICBnb2FsSGVhZGVyOiB7XG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAgbWFyZ2luQm90dG9tOiA4LFxuICB9LFxuICBnb2FsSWNvbjoge1xuICAgIHdpZHRoOiAzMixcbiAgICBoZWlnaHQ6IDMyLFxuICAgIGJvcmRlclJhZGl1czogMTYsXG4gICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMubGlnaHRHcmF5LFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcbiAgICBtYXJnaW5SaWdodDogMTIsXG4gIH0sXG4gIGdvYWxDb250ZW50OiB7XG4gICAgZmxleDogMSxcbiAgfSxcbiAgZ29hbFRleHQ6IHtcbiAgICBmb250U2l6ZTogMTQsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLU1lZGl1bScsXG4gICAgY29sb3I6IGNvbG9ycy5kYXJrLFxuICB9LFxuICBnb2FsUHJvZ3Jlc3M6IHtcbiAgICBmb250U2l6ZTogMTIsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLVJlZ3VsYXInLFxuICAgIGNvbG9yOiBjb2xvcnMuZ3JheSxcbiAgICBtYXJnaW5Ub3A6IDIsXG4gIH0sXG4gIGdvYWxQZXJjZW50YWdlOiB7XG4gICAgZm9udFNpemU6IDE0LFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1TZW1pQm9sZCcsXG4gICAgY29sb3I6IGNvbG9ycy5wcmltYXJ5LFxuICB9LFxuICBnb2FsUHJvZ3Jlc3NCYXI6IHtcbiAgICBoZWlnaHQ6IDQsXG4gICAgYmFja2dyb3VuZENvbG9yOiBjb2xvcnMubGlnaHRHcmF5LFxuICAgIGJvcmRlclJhZGl1czogMixcbiAgICBtYXJnaW5MZWZ0OiA0NCxcbiAgfSxcbiAgZ29hbFByb2dyZXNzRmlsbDoge1xuICAgIGhlaWdodDogJzEwMCUnLFxuICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLnByaW1hcnksXG4gICAgYm9yZGVyUmFkaXVzOiAyLFxuICB9LFxuICB0cmVuZHNDYXJkOiB7XG4gICAgbWFyZ2luSG9yaXpvbnRhbDogMjQsXG4gICAgbWFyZ2luQm90dG9tOiAyMCxcbiAgfSxcbiAgdHJlbmRJdGVtOiB7XG4gICAgbWFyZ2luQm90dG9tOiAxNixcbiAgfSxcbiAgdHJlbmRIZWFkZXI6IHtcbiAgICBmbGV4RGlyZWN0aW9uOiAncm93JyxcbiAgICBqdXN0aWZ5Q29udGVudDogJ3NwYWNlLWJldHdlZW4nLFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIG1hcmdpbkJvdHRvbTogNCxcbiAgfSxcbiAgdHJlbmRNZXRyaWM6IHtcbiAgICBmb250U2l6ZTogMTYsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLVNlbWlCb2xkJyxcbiAgICBjb2xvcjogY29sb3JzLmRhcmssXG4gIH0sXG4gIHRyZW5kQ2hhbmdlOiB7XG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gIH0sXG4gIHRyZW5kQ2hhbmdlVGV4dDoge1xuICAgIGZvbnRTaXplOiAxNCxcbiAgICBmb250RmFtaWx5OiAnSW50ZXItU2VtaUJvbGQnLFxuICAgIG1hcmdpbkxlZnQ6IDQsXG4gIH0sXG4gIHRyZW5kRGVzY3JpcHRpb246IHtcbiAgICBmb250U2l6ZTogMTQsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLVJlZ3VsYXInLFxuICAgIGNvbG9yOiBjb2xvcnMuZ3JheSxcbiAgICBsaW5lSGVpZ2h0OiAxOCxcbiAgfSxcbiAgYWNoaWV2ZW1lbnRzQ2FyZDoge1xuICAgIG1hcmdpbkhvcml6b250YWw6IDI0LFxuICAgIG1hcmdpbkJvdHRvbTogMjAsXG4gIH0sXG4gIGFjaGlldmVtZW50SXRlbToge1xuICAgIGZsZXhEaXJlY3Rpb246ICdyb3cnLFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIHBhZGRpbmdWZXJ0aWNhbDogMTYsXG4gICAgYm9yZGVyQm90dG9tV2lkdGg6IDEsXG4gICAgYm9yZGVyQm90dG9tQ29sb3I6IGNvbG9ycy5saWdodEdyYXksXG4gIH0sXG4gIGFjaGlldmVtZW50SWNvbjoge1xuICAgIHdpZHRoOiA0MCxcbiAgICBoZWlnaHQ6IDQwLFxuICAgIGJvcmRlclJhZGl1czogMjAsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgIG1hcmdpblJpZ2h0OiAxNixcbiAgfSxcbiAgYWNoaWV2ZW1lbnRDb250ZW50OiB7XG4gICAgZmxleDogMSxcbiAgfSxcbiAgYWNoaWV2ZW1lbnRUaXRsZToge1xuICAgIGZvbnRTaXplOiAxNixcbiAgICBmb250RmFtaWx5OiAnSW50ZXItU2VtaUJvbGQnLFxuICAgIGNvbG9yOiBjb2xvcnMuZGFyayxcbiAgfSxcbiAgYWNoaWV2ZW1lbnRUaXRsZUxvY2tlZDoge1xuICAgIG9wYWNpdHk6IDAuNixcbiAgfSxcbiAgYWNoaWV2ZW1lbnREZXNjcmlwdGlvbjoge1xuICAgIGZvbnRTaXplOiAxNCxcbiAgICBmb250RmFtaWx5OiAnSW50ZXItUmVndWxhcicsXG4gICAgY29sb3I6IGNvbG9ycy5ncmF5LFxuICAgIG1hcmdpblRvcDogNCxcbiAgfSxcbiAgYWNoaWV2ZW1lbnREYXRlOiB7XG4gICAgZm9udFNpemU6IDEyLFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1SZWd1bGFyJyxcbiAgICBjb2xvcjogY29sb3JzLnByaW1hcnksXG4gICAgbWFyZ2luVG9wOiA0LFxuICB9LFxuICBhY2hpZXZlbWVudFByb2dyZXNzOiB7XG4gICAgZm9udFNpemU6IDEyLFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1SZWd1bGFyJyxcbiAgICBjb2xvcjogY29sb3JzLmdyYXksXG4gICAgbWFyZ2luVG9wOiA0LFxuICB9LFxuICBpbnNpZ2h0c0NhcmQ6IHtcbiAgICBtYXJnaW5Ib3Jpem9udGFsOiAyNCxcbiAgICBtYXJnaW5Cb3R0b206IDIwLFxuICB9LFxuICBpbnNpZ2h0c0hlYWRlcjoge1xuICAgIGZsZXhEaXJlY3Rpb246ICdyb3cnLFxuICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxuICAgIG1hcmdpbkJvdHRvbTogMTYsXG4gIH0sXG4gIGluc2lnaHRJdGVtOiB7XG4gICAgbWFyZ2luQm90dG9tOiAxNixcbiAgfSxcbiAgaW5zaWdodFRpdGxlOiB7XG4gICAgZm9udFNpemU6IDE2LFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1TZW1pQm9sZCcsXG4gICAgY29sb3I6IGNvbG9ycy5kYXJrLFxuICAgIG1hcmdpbkJvdHRvbTogNCxcbiAgfSxcbiAgaW5zaWdodERlc2NyaXB0aW9uOiB7XG4gICAgZm9udFNpemU6IDE0LFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1SZWd1bGFyJyxcbiAgICBjb2xvcjogY29sb3JzLmdyYXksXG4gICAgbGluZUhlaWdodDogMTgsXG4gICAgbWFyZ2luQm90dG9tOiA0LFxuICB9LFxuICBpbnNpZ2h0UmVjb21tZW5kYXRpb246IHtcbiAgICBmb250U2l6ZTogMTQsXG4gICAgZm9udEZhbWlseTogJ0ludGVyLU1lZGl1bScsXG4gICAgY29sb3I6IGNvbG9ycy5wcmltYXJ5LFxuICB9LFxuICBleHBvcnRTZWN0aW9uOiB7XG4gICAgcGFkZGluZzogMjQsXG4gICAgcGFkZGluZ1RvcDogOCxcbiAgfSxcbiAgZXhwb3J0QnV0dG9uOiB7XG4gICAgZmxleERpcmVjdGlvbjogJ3JvdycsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgIHBhZGRpbmdWZXJ0aWNhbDogMTYsXG4gICAgcGFkZGluZ0hvcml6b250YWw6IDI0LFxuICAgIGJvcmRlclJhZGl1czogMTIsXG4gICAgYm9yZGVyV2lkdGg6IDEsXG4gICAgYm9yZGVyQ29sb3I6IGNvbG9ycy5wcmltYXJ5LFxuICAgIGJhY2tncm91bmRDb2xvcjogY29sb3JzLndoaXRlLFxuICB9LFxuICBleHBvcnRCdXR0b25UZXh0OiB7XG4gICAgZm9udFNpemU6IDE2LFxuICAgIGZvbnRGYW1pbHk6ICdJbnRlci1TZW1pQm9sZCcsXG4gICAgY29sb3I6IGNvbG9ycy5wcmltYXJ5LFxuICAgIG1hcmdpbkxlZnQ6IDgsXG4gIH0sXG59KTsiXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLE9BQU9BLEtBQUssSUFBSUMsUUFBUSxRQUFRLE9BQU87QUFDdkMsU0FBU0MsSUFBSSxFQUFFQyxJQUFJLEVBQUVDLFVBQVUsRUFBRUMsVUFBVSxFQUFFQyxZQUFZLEVBQUVDLGdCQUFnQixFQUFFQyxjQUFjLFFBQVEsY0FBYztBQUNqSCxPQUFPQyxJQUFJO0FBQ1gsT0FBT0MsWUFBWTtBQUNuQixPQUFPQyxZQUFZO0FBQ25CLE9BQU9DLFVBQVU7QUFDakIsU0FBU0MsZUFBZTtBQUN4QixTQUFTQyxVQUFVLEVBQUVDLFFBQVEsRUFBRUMsS0FBSyxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsSUFBSUMsU0FBUyxFQUFnQkMsWUFBWSxFQUFFQyxNQUFNLEVBQUVDLFFBQVEsRUFBRUMsR0FBRyxRQUFRLHFCQUFxQjtBQUFDLFNBQUFDLEdBQUEsSUFBQUMsSUFBQSxFQUFBQyxJQUFBLElBQUFDLEtBQUE7QUFFcEosSUFBTUMsTUFBTSxJQUFBQyxhQUFBLEdBQUFDLENBQUEsT0FBRztFQUNiQyxPQUFPLEVBQUUsU0FBUztFQUNsQkMsTUFBTSxFQUFFLFNBQVM7RUFDakJDLEtBQUssRUFBRSxTQUFTO0VBQ2hCQyxJQUFJLEVBQUUsU0FBUztFQUNmQyxJQUFJLEVBQUUsU0FBUztFQUNmQyxTQUFTLEVBQUU7QUFDYixDQUFDO0FBRUQsZUFBZSxTQUFTQyxjQUFjQSxDQUFBLEVBQUc7RUFBQVIsYUFBQSxHQUFBUyxDQUFBO0VBQ3ZDLElBQUFDLElBQUEsSUFBQVYsYUFBQSxHQUFBQyxDQUFBLE9BQTRDN0IsUUFBUSxDQUFDLE9BQU8sQ0FBQztJQUFBdUMsS0FBQSxHQUFBQyxjQUFBLENBQUFGLElBQUE7SUFBdERHLGNBQWMsR0FBQUYsS0FBQTtJQUFFRyxpQkFBaUIsR0FBQUgsS0FBQTtFQUN4QyxJQUFBSSxLQUFBLElBQUFmLGFBQUEsR0FBQUMsQ0FBQSxPQUEwRGpCLGVBQWUsQ0FBQyxDQUFDO0lBQW5FZ0MsSUFBSSxHQUFBRCxLQUFBLENBQUpDLElBQUk7SUFBRUMsT0FBTyxHQUFBRixLQUFBLENBQVBFLE9BQU87SUFBRUMsS0FBSyxHQUFBSCxLQUFBLENBQUxHLEtBQUs7SUFBRUMsVUFBVSxHQUFBSixLQUFBLENBQVZJLFVBQVU7SUFBRUMsV0FBVyxHQUFBTCxLQUFBLENBQVhLLFdBQVc7RUFFckQsSUFBTUMsT0FBTyxJQUFBckIsYUFBQSxHQUFBQyxDQUFBLE9BQUcsQ0FDZDtJQUFFcUIsRUFBRSxFQUFFLE1BQU07SUFBRUMsS0FBSyxFQUFFO0VBQU8sQ0FBQyxFQUM3QjtJQUFFRCxFQUFFLEVBQUUsT0FBTztJQUFFQyxLQUFLLEVBQUU7RUFBUSxDQUFDLEVBQy9CO0lBQUVELEVBQUUsRUFBRSxNQUFNO0lBQUVDLEtBQUssRUFBRTtFQUFPLENBQUMsQ0FDOUI7RUFBQ3ZCLGFBQUEsR0FBQUMsQ0FBQTtFQUVGLElBQUksQ0FBQUQsYUFBQSxHQUFBd0IsQ0FBQSxVQUFBUCxPQUFPLE1BQUFqQixhQUFBLEdBQUF3QixDQUFBLFVBQUksQ0FBQ1IsSUFBSSxHQUFFO0lBQUFoQixhQUFBLEdBQUF3QixDQUFBO0lBQUF4QixhQUFBLEdBQUFDLENBQUE7SUFDcEIsT0FBT0wsSUFBQSxDQUFDZCxZQUFZO01BQUMyQyxPQUFPLEVBQUM7SUFBK0IsQ0FBRSxDQUFDO0VBQ2pFLENBQUM7SUFBQXpCLGFBQUEsR0FBQXdCLENBQUE7RUFBQTtFQUFBeEIsYUFBQSxHQUFBQyxDQUFBO0VBRUQsSUFBSSxDQUFBRCxhQUFBLEdBQUF3QixDQUFBLFVBQUFOLEtBQUssTUFBQWxCLGFBQUEsR0FBQXdCLENBQUEsVUFBSSxDQUFDUixJQUFJLEdBQUU7SUFBQWhCLGFBQUEsR0FBQXdCLENBQUE7SUFBQXhCLGFBQUEsR0FBQUMsQ0FBQTtJQUNsQixPQUNFTCxJQUFBLENBQUNiLFVBQVU7TUFDVDBDLE9BQU8sRUFBRVAsS0FBTTtNQUNmUSxPQUFPLEVBQUVOO0lBQVksQ0FDdEIsQ0FBQztFQUVOLENBQUM7SUFBQXBCLGFBQUEsR0FBQXdCLENBQUE7RUFBQTtFQUFBeEIsYUFBQSxHQUFBQyxDQUFBO0VBRUQsSUFBSSxDQUFDZSxJQUFJLEVBQUU7SUFBQWhCLGFBQUEsR0FBQXdCLENBQUE7SUFBQXhCLGFBQUEsR0FBQUMsQ0FBQTtJQUNULE9BQ0VMLElBQUEsQ0FBQ2IsVUFBVTtNQUNUMEMsT0FBTyxFQUFDLDRCQUE0QjtNQUNwQ0MsT0FBTyxFQUFFTjtJQUFZLENBQ3RCLENBQUM7RUFFTixDQUFDO0lBQUFwQixhQUFBLEdBQUF3QixDQUFBO0VBQUE7RUFFRCxJQUFNRyxXQUFXLElBQUEzQixhQUFBLEdBQUFDLENBQUEsUUFBRzJCLElBQUksQ0FBQ0MsR0FBRyxDQUFBQyxLQUFBLENBQVJGLElBQUksRUFBQUcsa0JBQUEsQ0FBUWYsSUFBSSxDQUFDZ0IsV0FBVyxDQUFDQyxHQUFHLENBQUMsVUFBQUMsSUFBSSxFQUFJO0lBQUFsQyxhQUFBLEdBQUFTLENBQUE7SUFBQVQsYUFBQSxHQUFBQyxDQUFBO0lBQUEsT0FBQWlDLElBQUksQ0FBQ0MsUUFBUTtFQUFELENBQUMsQ0FBQyxFQUFDO0VBQUNuQyxhQUFBLEdBQUFDLENBQUE7RUFFN0UsT0FDRUwsSUFBQSxDQUFDbkIsWUFBWTtJQUFDMkQsS0FBSyxFQUFFQyxNQUFNLENBQUNDLFNBQVU7SUFBQUMsUUFBQSxFQUNwQ3pDLEtBQUEsQ0FBQ3RCLFVBQVU7TUFDVDRELEtBQUssRUFBRUMsTUFBTSxDQUFDRyxVQUFXO01BQ3pCQyw0QkFBNEIsRUFBRSxLQUFNO01BQ3BDQyxjQUFjLEVBQ1o5QyxJQUFBLENBQUNqQixjQUFjO1FBQ2J3QyxVQUFVLEVBQUVBLFVBQVc7UUFDdkJ3QixTQUFTLEVBQUV2QixXQUFZO1FBQ3ZCckIsTUFBTSxFQUFFLENBQUNBLE1BQU0sQ0FBQ0csT0FBTyxDQUFFO1FBQ3pCMEMsU0FBUyxFQUFFN0MsTUFBTSxDQUFDRztNQUFRLENBQzNCLENBQ0Y7TUFBQXFDLFFBQUEsR0FHRHpDLEtBQUEsQ0FBQ3pCLElBQUk7UUFBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDUSxNQUFPO1FBQUFOLFFBQUEsR0FDekIzQyxJQUFBLENBQUN0QixJQUFJO1VBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ1MsS0FBTTtVQUFBUCxRQUFBLEVBQUM7UUFBYSxDQUFNLENBQUMsRUFDL0MzQyxJQUFBLENBQUN0QixJQUFJO1VBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ1UsUUFBUztVQUFBUixRQUFBLEVBQUM7UUFBcUMsQ0FBTSxDQUFDO01BQUEsQ0FDdEUsQ0FBQyxFQUdQM0MsSUFBQSxDQUFDdkIsSUFBSTtRQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUNXLGNBQWU7UUFBQVQsUUFBQSxFQUNoQ2xCLE9BQU8sQ0FBQ1ksR0FBRyxDQUFDLFVBQUNnQixNQUFNLEVBQ2xCO1VBQUFqRCxhQUFBLEdBQUFTLENBQUE7VUFBQVQsYUFBQSxHQUFBQyxDQUFBO1VBQUEsT0FBQUwsSUFBQSxDQUFDbEIsZ0JBQWdCO1lBRWYwRCxLQUFLLEVBQUUsQ0FDTEMsTUFBTSxDQUFDYSxZQUFZLEVBQ25CLENBQUFsRCxhQUFBLEdBQUF3QixDQUFBLFVBQUFYLGNBQWMsS0FBS29DLE1BQU0sQ0FBQzNCLEVBQUUsTUFBQXRCLGFBQUEsR0FBQXdCLENBQUEsVUFBSWEsTUFBTSxDQUFDYyxrQkFBa0IsRUFDekQ7WUFDRkMsT0FBTyxFQUFFLFNBQVRBLE9BQU9BLENBQUEsRUFBUTtjQUFBcEQsYUFBQSxHQUFBUyxDQUFBO2NBQUFULGFBQUEsR0FBQUMsQ0FBQTtjQUFBLE9BQUFhLGlCQUFpQixDQUFDbUMsTUFBTSxDQUFDM0IsRUFBRSxDQUFDO1lBQUQsQ0FBRTtZQUFBaUIsUUFBQSxFQUU1QzNDLElBQUEsQ0FBQ3RCLElBQUk7Y0FDSDhELEtBQUssRUFBRSxDQUNMQyxNQUFNLENBQUNnQixnQkFBZ0IsRUFDdkIsQ0FBQXJELGFBQUEsR0FBQXdCLENBQUEsVUFBQVgsY0FBYyxLQUFLb0MsTUFBTSxDQUFDM0IsRUFBRSxNQUFBdEIsYUFBQSxHQUFBd0IsQ0FBQSxVQUFJYSxNQUFNLENBQUNpQixzQkFBc0IsRUFDN0Q7Y0FBQWYsUUFBQSxFQUVEVSxNQUFNLENBQUMxQjtZQUFLLENBQ1Q7VUFBQyxHQWRGMEIsTUFBTSxDQUFDM0IsRUFlSSxDQUFDO1FBQUQsQ0FDbkI7TUFBQyxDQUNFLENBQUMsRUFHUHhCLEtBQUEsQ0FBQ2xCLElBQUk7UUFBQzJFLE9BQU8sRUFBQyxVQUFVO1FBQUNuQixLQUFLLEVBQUVDLE1BQU0sQ0FBQ21CLFdBQVk7UUFBQWpCLFFBQUEsR0FDakR6QyxLQUFBLENBQUN6QixJQUFJO1VBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ29CLGFBQWM7VUFBQWxCLFFBQUEsR0FDaEMzQyxJQUFBLENBQUN0QixJQUFJO1lBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ3FCLFlBQWE7WUFBQW5CLFFBQUEsRUFBQztVQUFnQixDQUFNLENBQUMsRUFDekQzQyxJQUFBLENBQUNsQixnQkFBZ0I7WUFBQzBELEtBQUssRUFBRUMsTUFBTSxDQUFDc0IsV0FBWTtZQUFBcEIsUUFBQSxFQUMxQzNDLElBQUEsQ0FBQ0osTUFBTTtjQUFDb0UsSUFBSSxFQUFFLEVBQUc7Y0FBQ0MsS0FBSyxFQUFFOUQsTUFBTSxDQUFDRztZQUFRLENBQUU7VUFBQyxDQUMzQixDQUFDO1FBQUEsQ0FDZixDQUFDLEVBRVBKLEtBQUEsQ0FBQ3pCLElBQUk7VUFBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDeUIsWUFBYTtVQUFBdkIsUUFBQSxHQUMvQnpDLEtBQUEsQ0FBQ3pCLElBQUk7WUFBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDMEIsV0FBWTtZQUFBeEIsUUFBQSxHQUM5QjNDLElBQUEsQ0FBQ3RCLElBQUk7Y0FBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDMkIsWUFBYTtjQUFBekIsUUFBQSxFQUFFdkIsSUFBSSxDQUFDaUQsZUFBZSxDQUFDQztZQUFhLENBQU8sQ0FBQyxFQUM3RXRFLElBQUEsQ0FBQ3RCLElBQUk7Y0FBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDOEIsWUFBYTtjQUFBNUIsUUFBQSxFQUFDO1lBQWMsQ0FBTSxDQUFDO1VBQUEsQ0FDbkQsQ0FBQyxFQUNQekMsS0FBQSxDQUFDekIsSUFBSTtZQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUMwQixXQUFZO1lBQUF4QixRQUFBLEdBQzlCekMsS0FBQSxDQUFDeEIsSUFBSTtjQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUMyQixZQUFhO2NBQUF6QixRQUFBLEdBQUV2QixJQUFJLENBQUNpRCxlQUFlLENBQUNHLFdBQVcsRUFBQyxHQUFDO1lBQUEsQ0FBTSxDQUFDLEVBQzVFeEUsSUFBQSxDQUFDdEIsSUFBSTtjQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUM4QixZQUFhO2NBQUE1QixRQUFBLEVBQUM7WUFBWSxDQUFNLENBQUM7VUFBQSxDQUNqRCxDQUFDLEVBQ1B6QyxLQUFBLENBQUN6QixJQUFJO1lBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzBCLFdBQVk7WUFBQXhCLFFBQUEsR0FDOUJ6QyxLQUFBLENBQUN4QixJQUFJO2NBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzJCLFlBQWE7Y0FBQXpCLFFBQUEsR0FBQyxHQUFDLEVBQUN2QixJQUFJLENBQUNpRCxlQUFlLENBQUNJLGdCQUFnQjtZQUFBLENBQU8sQ0FBQyxFQUNqRnpFLElBQUEsQ0FBQ3RCLElBQUk7Y0FBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDOEIsWUFBYTtjQUFBNUIsUUFBQSxFQUFDO1lBQWUsQ0FBTSxDQUFDO1VBQUEsQ0FDcEQsQ0FBQyxFQUNQekMsS0FBQSxDQUFDekIsSUFBSTtZQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUMwQixXQUFZO1lBQUF4QixRQUFBLEdBQzlCM0MsSUFBQSxDQUFDdEIsSUFBSTtjQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUMyQixZQUFhO2NBQUF6QixRQUFBLEVBQUV2QixJQUFJLENBQUNpRCxlQUFlLENBQUNLO1lBQWEsQ0FBTyxDQUFDLEVBQzdFMUUsSUFBQSxDQUFDdEIsSUFBSTtjQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUM4QixZQUFhO2NBQUE1QixRQUFBLEVBQUM7WUFBVSxDQUFNLENBQUM7VUFBQSxDQUMvQyxDQUFDO1FBQUEsQ0FDSCxDQUFDO01BQUEsQ0FDSCxDQUFDLEVBR1B6QyxLQUFBLENBQUNsQixJQUFJO1FBQUMyRSxPQUFPLEVBQUMsVUFBVTtRQUFDbkIsS0FBSyxFQUFFQyxNQUFNLENBQUNrQyxVQUFXO1FBQUFoQyxRQUFBLEdBQ2hEekMsS0FBQSxDQUFDekIsSUFBSTtVQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUNtQyxhQUFjO1VBQUFqQyxRQUFBLEdBQ2hDM0MsSUFBQSxDQUFDdEIsSUFBSTtZQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUNxQixZQUFhO1lBQUFuQixRQUFBLEVBQUM7VUFBaUIsQ0FBTSxDQUFDLEVBQzFEM0MsSUFBQSxDQUFDbEIsZ0JBQWdCO1lBQUE2RCxRQUFBLEVBQ2YzQyxJQUFBLENBQUN0QixJQUFJO2NBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ29DLFdBQVk7Y0FBQWxDLFFBQUEsRUFBQztZQUFZLENBQU07VUFBQyxDQUNwQyxDQUFDO1FBQUEsQ0FDZixDQUFDLEVBRVAzQyxJQUFBLENBQUN2QixJQUFJO1VBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ3FDLFVBQVc7VUFBQW5DLFFBQUEsRUFDNUJvQyxNQUFNLENBQUNDLE9BQU8sQ0FBQzVELElBQUksQ0FBQzZELGFBQWEsQ0FBQyxDQUFDNUMsR0FBRyxDQUFDLFVBQUE2QyxLQUFBLEVBQ3RDO1lBQUEsSUFBQUMsS0FBQSxHQUFBbkUsY0FBQSxDQUFBa0UsS0FBQTtjQUR3Q0UsS0FBSyxHQUFBRCxLQUFBO2NBQUVFLFNBQVMsR0FBQUYsS0FBQTtZQUFBL0UsYUFBQSxHQUFBUyxDQUFBO1lBQUFULGFBQUEsR0FBQUMsQ0FBQTtZQUN4RCxPQUFBSCxLQUFBLENBQUN6QixJQUFJO2NBQWErRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzZDLFNBQVU7Y0FBQTNDLFFBQUEsR0FDeEMzQyxJQUFBLENBQUNmLFlBQVk7Z0JBQ1hzRyxRQUFRLEVBQUVGLFNBQVMsQ0FBQ0csT0FBUTtnQkFDNUJ4QixJQUFJLEVBQUUsRUFBRztnQkFDVHlCLFdBQVcsRUFBRSxDQUFFO2dCQUNmQyxLQUFLLEVBQUUsR0FBR0wsU0FBUyxDQUFDRyxPQUFPO2NBQUksQ0FDaEMsQ0FBQyxFQUNGeEYsSUFBQSxDQUFDdEIsSUFBSTtnQkFBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDa0QsU0FBVTtnQkFBQWhELFFBQUEsRUFDM0J5QyxLQUFLLENBQUNRLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUMsR0FBR1QsS0FBSyxDQUFDVSxLQUFLLENBQUMsQ0FBQztjQUFDLENBQzNDLENBQUMsRUFDUDVGLEtBQUEsQ0FBQ3pCLElBQUk7Z0JBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ3NELFdBQVk7Z0JBQUFwRCxRQUFBLEdBQzlCM0MsSUFBQSxDQUFDWCxVQUFVO2tCQUFDMkUsSUFBSSxFQUFFLEVBQUc7a0JBQUNDLEtBQUssRUFBRTlELE1BQU0sQ0FBQ0c7Z0JBQVEsQ0FBRSxDQUFDLEVBQy9DSixLQUFBLENBQUN4QixJQUFJO2tCQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUN1RCxlQUFnQjtrQkFBQXJELFFBQUEsR0FBQyxHQUFDLEVBQUMwQyxTQUFTLENBQUNZLE1BQU0sRUFBQyxHQUFDO2dCQUFBLENBQU0sQ0FBQztjQUFBLENBQzVELENBQUM7WUFBQSxHQWJFYixLQWNMLENBQUM7VUFBRCxDQUNQO1FBQUMsQ0FDRSxDQUFDO01BQUEsQ0FDSCxDQUFDLEVBR1BsRixLQUFBLENBQUNsQixJQUFJO1FBQUMyRSxPQUFPLEVBQUMsVUFBVTtRQUFDbkIsS0FBSyxFQUFFQyxNQUFNLENBQUN5RCxZQUFhO1FBQUF2RCxRQUFBLEdBQ2xEM0MsSUFBQSxDQUFDdEIsSUFBSTtVQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUNxQixZQUFhO1VBQUFuQixRQUFBLEVBQUM7UUFBZSxDQUFNLENBQUMsRUFDeEQzQyxJQUFBLENBQUN2QixJQUFJO1VBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzBELFdBQVk7VUFBQXhELFFBQUEsRUFDN0J2QixJQUFJLENBQUNnQixXQUFXLENBQUNDLEdBQUcsQ0FBQyxVQUFDQyxJQUFJLEVBQUU4RCxLQUFLLEVBQ2hDO1lBQUFoRyxhQUFBLEdBQUFTLENBQUE7WUFBQVQsYUFBQSxHQUFBQyxDQUFBO1lBQUEsT0FBQUgsS0FBQSxDQUFDekIsSUFBSTtjQUFhK0QsS0FBSyxFQUFFQyxNQUFNLENBQUM0RCxRQUFTO2NBQUExRCxRQUFBLEdBQ3ZDM0MsSUFBQSxDQUFDdkIsSUFBSTtnQkFBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDNkQsaUJBQWtCO2dCQUFBM0QsUUFBQSxFQUNwQzNDLElBQUEsQ0FBQ3ZCLElBQUk7a0JBQ0grRCxLQUFLLEVBQUUsQ0FDTEMsTUFBTSxDQUFDOEQsUUFBUSxFQUNmO29CQUNFQyxNQUFNLEVBQUV6RSxXQUFXLEdBQUcsQ0FBQyxJQUFBM0IsYUFBQSxHQUFBd0IsQ0FBQSxVQUFJVSxJQUFJLENBQUNDLFFBQVEsR0FBR1IsV0FBVyxHQUFJLEVBQUUsS0FBQTNCLGFBQUEsR0FBQXdCLENBQUEsVUFBRyxDQUFDO29CQUNoRTZFLGVBQWUsRUFBRW5FLElBQUksQ0FBQ0MsUUFBUSxHQUFHLENBQUMsSUFBQW5DLGFBQUEsR0FBQXdCLENBQUEsVUFBR3pCLE1BQU0sQ0FBQ0csT0FBTyxLQUFBRixhQUFBLEdBQUF3QixDQUFBLFVBQUd6QixNQUFNLENBQUNRLFNBQVM7a0JBQ3hFLENBQUM7Z0JBQ0QsQ0FDSDtjQUFDLENBQ0UsQ0FBQyxFQUNQWCxJQUFBLENBQUN0QixJQUFJO2dCQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUNpRSxhQUFjO2dCQUFBL0QsUUFBQSxFQUFFTCxJQUFJLENBQUNxRTtjQUFHLENBQU8sQ0FBQyxFQUNwRDNHLElBQUEsQ0FBQ3RCLElBQUk7Z0JBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ21FLGFBQWM7Z0JBQUFqRSxRQUFBLEVBQUVMLElBQUksQ0FBQ0M7Y0FBUSxDQUFPLENBQUM7WUFBQSxHQWJoRDZELEtBY0wsQ0FBQztVQUFELENBQ1A7UUFBQyxDQUNFLENBQUMsRUFFUGxHLEtBQUEsQ0FBQ3pCLElBQUk7VUFBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDb0UsZUFBZ0I7VUFBQWxFLFFBQUEsR0FDbEN6QyxLQUFBLENBQUN6QixJQUFJO1lBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzBCLFdBQVk7WUFBQXhCLFFBQUEsR0FDOUIzQyxJQUFBLENBQUN0QixJQUFJO2NBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzJCLFlBQWE7Y0FBQXpCLFFBQUEsRUFBRXZCLElBQUksQ0FBQ2dCLFdBQVcsQ0FBQzBFLE1BQU0sQ0FBQyxVQUFDQyxHQUFHLEVBQUV6RSxJQUFJLEVBQUs7Z0JBQUFsQyxhQUFBLEdBQUFTLENBQUE7Z0JBQUFULGFBQUEsR0FBQUMsQ0FBQTtnQkFBQSxPQUFBMEcsR0FBRyxHQUFHekUsSUFBSSxDQUFDQyxRQUFRO2NBQUQsQ0FBQyxFQUFFLENBQUM7WUFBQyxDQUFPLENBQUMsRUFDekd2QyxJQUFBLENBQUN0QixJQUFJO2NBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzhCLFlBQWE7Y0FBQTVCLFFBQUEsRUFBQztZQUFRLENBQU0sQ0FBQztVQUFBLENBQzdDLENBQUMsRUFDUHpDLEtBQUEsQ0FBQ3pCLElBQUk7WUFBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDMEIsV0FBWTtZQUFBeEIsUUFBQSxHQUM5QjNDLElBQUEsQ0FBQ3RCLElBQUk7Y0FBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDMkIsWUFBYTtjQUFBekIsUUFBQSxFQUFFdkIsSUFBSSxDQUFDZ0IsV0FBVyxDQUFDMEUsTUFBTSxDQUFDLFVBQUNDLEdBQUcsRUFBRXpFLElBQUksRUFBSztnQkFBQWxDLGFBQUEsR0FBQVMsQ0FBQTtnQkFBQVQsYUFBQSxHQUFBQyxDQUFBO2dCQUFBLE9BQUEwRyxHQUFHLEdBQUd6RSxJQUFJLENBQUMwRSxRQUFRO2NBQUQsQ0FBQyxFQUFFLENBQUM7WUFBQyxDQUFPLENBQUMsRUFDekdoSCxJQUFBLENBQUN0QixJQUFJO2NBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzhCLFlBQWE7Y0FBQTVCLFFBQUEsRUFBQztZQUFPLENBQU0sQ0FBQztVQUFBLENBQzVDLENBQUMsRUFDUHpDLEtBQUEsQ0FBQ3pCLElBQUk7WUFBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDMEIsV0FBWTtZQUFBeEIsUUFBQSxHQUM5QnpDLEtBQUEsQ0FBQ3hCLElBQUk7Y0FBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDMkIsWUFBYTtjQUFBekIsUUFBQSxHQUFDLEdBQUMsRUFBQ3ZCLElBQUksQ0FBQ2lELGVBQWUsQ0FBQzRDLGlCQUFpQixFQUFDLEdBQUM7WUFBQSxDQUFNLENBQUMsRUFDbkZqSCxJQUFBLENBQUN0QixJQUFJO2NBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzhCLFlBQWE7Y0FBQTVCLFFBQUEsRUFBQztZQUFZLENBQU0sQ0FBQztVQUFBLENBQ2pELENBQUM7UUFBQSxDQUNILENBQUM7TUFBQSxDQUNILENBQUMsRUFHUHpDLEtBQUEsQ0FBQ2xCLElBQUk7UUFBQzJFLE9BQU8sRUFBQyxVQUFVO1FBQUNuQixLQUFLLEVBQUVDLE1BQU0sQ0FBQ3lFLFNBQVU7UUFBQXZFLFFBQUEsR0FDL0MzQyxJQUFBLENBQUN0QixJQUFJO1VBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ3FCLFlBQWE7VUFBQW5CLFFBQUEsRUFBQztRQUFhLENBQU0sQ0FBQyxFQUVyRHZCLElBQUksQ0FBQytGLFlBQVksQ0FBQzlFLEdBQUcsQ0FBQyxVQUFDK0UsSUFBSSxFQUFFaEIsS0FBSyxFQUFLO1VBQUFoRyxhQUFBLEdBQUFTLENBQUE7VUFDdEMsSUFBTXdHLGFBQWEsSUFBQWpILGFBQUEsR0FBQUMsQ0FBQSxRQUFHK0csSUFBSSxDQUFDRSxJQUFJLEtBQUssVUFBVSxJQUFBbEgsYUFBQSxHQUFBd0IsQ0FBQSxVQUFHdEMsUUFBUSxLQUFBYyxhQUFBLEdBQUF3QixDQUFBLFVBQ3RDd0YsSUFBSSxDQUFDRSxJQUFJLEtBQUssUUFBUSxJQUFBbEgsYUFBQSxHQUFBd0IsQ0FBQSxXQUFHcEMsTUFBTSxLQUFBWSxhQUFBLEdBQUF3QixDQUFBLFdBQUdsQyxTQUFTO1VBQzlELElBQU02SCxVQUFVLElBQUFuSCxhQUFBLEdBQUFDLENBQUEsUUFBSStHLElBQUksQ0FBQzdCLFFBQVEsR0FBRzZCLElBQUksQ0FBQ0ksS0FBSyxHQUFJLEdBQUc7VUFBQ3BILGFBQUEsR0FBQUMsQ0FBQTtVQUV0RCxPQUNFSCxLQUFBLENBQUN6QixJQUFJO1lBQWErRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ2dGLFFBQVM7WUFBQTlFLFFBQUEsR0FDdkN6QyxLQUFBLENBQUN6QixJQUFJO2NBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ2lGLFVBQVc7Y0FBQS9FLFFBQUEsR0FDN0IzQyxJQUFBLENBQUN2QixJQUFJO2dCQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUNrRixRQUFTO2dCQUFBaEYsUUFBQSxFQUMzQjNDLElBQUEsQ0FBQ3FILGFBQWE7a0JBQUNyRCxJQUFJLEVBQUUsRUFBRztrQkFBQ0MsS0FBSyxFQUFFOUQsTUFBTSxDQUFDRztnQkFBUSxDQUFFO2NBQUMsQ0FDOUMsQ0FBQyxFQUNQSixLQUFBLENBQUN6QixJQUFJO2dCQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUNtRixXQUFZO2dCQUFBakYsUUFBQSxHQUM5QjNDLElBQUEsQ0FBQ3RCLElBQUk7a0JBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ29GLFFBQVM7a0JBQUFsRixRQUFBLEVBQUV5RSxJQUFJLENBQUNBO2dCQUFJLENBQU8sQ0FBQyxFQUNoRGxILEtBQUEsQ0FBQ3hCLElBQUk7a0JBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ3FGLFlBQWE7a0JBQUFuRixRQUFBLEdBQzlCeUUsSUFBSSxDQUFDN0IsUUFBUSxFQUFDLEtBQUcsRUFBQzZCLElBQUksQ0FBQ0ksS0FBSztnQkFBQSxDQUN6QixDQUFDO2NBQUEsQ0FDSCxDQUFDLEVBQ1B0SCxLQUFBLENBQUN4QixJQUFJO2dCQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUNzRixjQUFlO2dCQUFBcEYsUUFBQSxHQUFFNEUsVUFBVSxDQUFDUyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUMsR0FBQztjQUFBLENBQU0sQ0FBQztZQUFBLENBQy9ELENBQUMsRUFDUGhJLElBQUEsQ0FBQ3ZCLElBQUk7Y0FBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDd0YsZUFBZ0I7Y0FBQXRGLFFBQUEsRUFDbEMzQyxJQUFBLENBQUN2QixJQUFJO2dCQUNIK0QsS0FBSyxFQUFFLENBQ0xDLE1BQU0sQ0FBQ3lGLGdCQUFnQixFQUN2QjtrQkFBRUMsS0FBSyxFQUFFLEdBQUdaLFVBQVU7Z0JBQUksQ0FBQztjQUMzQixDQUNIO1lBQUMsQ0FDRSxDQUFDO1VBQUEsR0FwQkVuQixLQXFCTCxDQUFDO1FBRVgsQ0FBQyxDQUFDO01BQUEsQ0FDRSxDQUFDLEVBR1BsRyxLQUFBLENBQUNsQixJQUFJO1FBQUMyRSxPQUFPLEVBQUMsVUFBVTtRQUFDbkIsS0FBSyxFQUFFQyxNQUFNLENBQUMyRixVQUFXO1FBQUF6RixRQUFBLEdBQ2hEM0MsSUFBQSxDQUFDdEIsSUFBSTtVQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUNxQixZQUFhO1VBQUFuQixRQUFBLEVBQUM7UUFBa0IsQ0FBTSxDQUFDLEVBQzNEM0MsSUFBQSxDQUFDdEIsSUFBSTtVQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUM0RixlQUFnQjtVQUFBMUYsUUFBQSxFQUFDO1FBQTBCLENBQU0sQ0FBQyxFQUVyRXZCLElBQUksQ0FBQ2tILGlCQUFpQixDQUFDakcsR0FBRyxDQUFDLFVBQUNrRyxLQUFLLEVBQUVuQyxLQUFLLEVBQ3ZDO1VBQUFoRyxhQUFBLEdBQUFTLENBQUE7VUFBQVQsYUFBQSxHQUFBQyxDQUFBO1VBQUEsT0FBQUgsS0FBQSxDQUFDekIsSUFBSTtZQUFhK0QsS0FBSyxFQUFFQyxNQUFNLENBQUMrRixTQUFVO1lBQUE3RixRQUFBLEdBQ3hDekMsS0FBQSxDQUFDekIsSUFBSTtjQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUNnRyxXQUFZO2NBQUE5RixRQUFBLEdBQzlCM0MsSUFBQSxDQUFDdEIsSUFBSTtnQkFBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDaUcsV0FBWTtnQkFBQS9GLFFBQUEsRUFBRTRGLEtBQUssQ0FBQ0k7Y0FBTSxDQUFPLENBQUMsRUFDdER6SSxLQUFBLENBQUN6QixJQUFJO2dCQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUNtRyxXQUFZO2dCQUFBakcsUUFBQSxHQUM5QjNDLElBQUEsQ0FBQ1gsVUFBVTtrQkFBQzJFLElBQUksRUFBRSxFQUFHO2tCQUFDQyxLQUFLLEVBQUVzRSxLQUFLLENBQUN0QyxNQUFNLEdBQUcsQ0FBQyxJQUFBN0YsYUFBQSxHQUFBd0IsQ0FBQSxXQUFHekIsTUFBTSxDQUFDRyxPQUFPLEtBQUFGLGFBQUEsR0FBQXdCLENBQUEsV0FBR3pCLE1BQU0sQ0FBQ08sSUFBSTtnQkFBQyxDQUFFLENBQUMsRUFDaEZSLEtBQUEsQ0FBQ3hCLElBQUk7a0JBQUM4RCxLQUFLLEVBQUUsQ0FDWEMsTUFBTSxDQUFDb0csZUFBZSxFQUN0QjtvQkFBRTVFLEtBQUssRUFBRXNFLEtBQUssQ0FBQ3RDLE1BQU0sR0FBRyxDQUFDLElBQUE3RixhQUFBLEdBQUF3QixDQUFBLFdBQUd6QixNQUFNLENBQUNHLE9BQU8sS0FBQUYsYUFBQSxHQUFBd0IsQ0FBQSxXQUFHekIsTUFBTSxDQUFDTyxJQUFJO2tCQUFDLENBQUMsQ0FDMUQ7a0JBQUFpQyxRQUFBLEdBQ0M0RixLQUFLLENBQUN0QyxNQUFNLEdBQUcsQ0FBQyxJQUFBN0YsYUFBQSxHQUFBd0IsQ0FBQSxXQUFHLEdBQUcsS0FBQXhCLGFBQUEsR0FBQXdCLENBQUEsV0FBRyxFQUFFLEdBQUUyRyxLQUFLLENBQUN0QyxNQUFNLEVBQUMsR0FDN0M7Z0JBQUEsQ0FBTSxDQUFDO2NBQUEsQ0FDSCxDQUFDO1lBQUEsQ0FDSCxDQUFDLEVBQ1BqRyxJQUFBLENBQUN0QixJQUFJO2NBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ3FHLGdCQUFpQjtjQUFBbkcsUUFBQSxFQUFFNEYsS0FBSyxDQUFDUTtZQUFXLENBQU8sQ0FBQztVQUFBLEdBYnZEM0MsS0FjTCxDQUFDO1FBQUQsQ0FDUCxDQUFDO01BQUEsQ0FDRSxDQUFDLEVBR1BsRyxLQUFBLENBQUNsQixJQUFJO1FBQUMyRSxPQUFPLEVBQUMsVUFBVTtRQUFDbkIsS0FBSyxFQUFFQyxNQUFNLENBQUN1RyxnQkFBaUI7UUFBQXJHLFFBQUEsR0FDdER6QyxLQUFBLENBQUN6QixJQUFJO1VBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ21DLGFBQWM7VUFBQWpDLFFBQUEsR0FDaEMzQyxJQUFBLENBQUN0QixJQUFJO1lBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ3FCLFlBQWE7WUFBQW5CLFFBQUEsRUFBQztVQUFZLENBQU0sQ0FBQyxFQUNyRDNDLElBQUEsQ0FBQ2xCLGdCQUFnQjtZQUFBNkQsUUFBQSxFQUNmM0MsSUFBQSxDQUFDdEIsSUFBSTtjQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUNvQyxXQUFZO2NBQUFsQyxRQUFBLEVBQUM7WUFBUSxDQUFNO1VBQUMsQ0FDaEMsQ0FBQztRQUFBLENBQ2YsQ0FBQyxFQUVOdkIsSUFBSSxDQUFDNkgsWUFBWSxDQUFDNUcsR0FBRyxDQUFDLFVBQUM2RyxXQUFXLEVBQ2pDO1VBQUE5SSxhQUFBLEdBQUFTLENBQUE7VUFBQVQsYUFBQSxHQUFBQyxDQUFBO1VBQUEsT0FBQUgsS0FBQSxDQUFDcEIsZ0JBQWdCO1lBQXNCMEQsS0FBSyxFQUFFQyxNQUFNLENBQUMwRyxlQUFnQjtZQUFBeEcsUUFBQSxHQUNuRTNDLElBQUEsQ0FBQ3ZCLElBQUk7Y0FDSCtELEtBQUssRUFBRSxDQUNMQyxNQUFNLENBQUMyRyxlQUFlLEVBQ3RCO2dCQUNFM0MsZUFBZSxFQUFFeUMsV0FBVyxDQUFDRyxRQUFRLElBQUFqSixhQUFBLEdBQUF3QixDQUFBLFdBQ2pDc0gsV0FBVyxDQUFDakYsS0FBSyxLQUFBN0QsYUFBQSxHQUFBd0IsQ0FBQSxXQUNqQnpCLE1BQU0sQ0FBQ1EsU0FBUztjQUN0QixDQUFDLENBQ0Q7Y0FBQWdDLFFBQUEsRUFFRjNDLElBQUEsQ0FBQ1QsS0FBSztnQkFDSnlFLElBQUksRUFBRSxFQUFHO2dCQUNUQyxLQUFLLEVBQUVpRixXQUFXLENBQUNHLFFBQVEsSUFBQWpKLGFBQUEsR0FBQXdCLENBQUEsV0FBR3pCLE1BQU0sQ0FBQ0ssS0FBSyxLQUFBSixhQUFBLEdBQUF3QixDQUFBLFdBQUd6QixNQUFNLENBQUNPLElBQUk7Y0FBQyxDQUMxRDtZQUFDLENBQ0UsQ0FBQyxFQUVQUixLQUFBLENBQUN6QixJQUFJO2NBQUMrRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzZHLGtCQUFtQjtjQUFBM0csUUFBQSxHQUNyQzNDLElBQUEsQ0FBQ3RCLElBQUk7Z0JBQ0g4RCxLQUFLLEVBQUUsQ0FDTEMsTUFBTSxDQUFDOEcsZ0JBQWdCLEVBQ3ZCLENBQUFuSixhQUFBLEdBQUF3QixDQUFBLFlBQUNzSCxXQUFXLENBQUNHLFFBQVEsTUFBQWpKLGFBQUEsR0FBQXdCLENBQUEsV0FBSWEsTUFBTSxDQUFDK0csc0JBQXNCLEVBQ3REO2dCQUFBN0csUUFBQSxFQUVEdUcsV0FBVyxDQUFDaEc7Y0FBSyxDQUNkLENBQUMsRUFDUGxELElBQUEsQ0FBQ3RCLElBQUk7Z0JBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ2dILHNCQUF1QjtnQkFBQTlHLFFBQUEsRUFDeEN1RyxXQUFXLENBQUNIO2NBQVcsQ0FDcEIsQ0FBQyxFQUNORyxXQUFXLENBQUNHLFFBQVEsSUFBQWpKLGFBQUEsR0FBQXdCLENBQUEsV0FDbkIxQixLQUFBLENBQUN4QixJQUFJO2dCQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUNpSCxlQUFnQjtnQkFBQS9HLFFBQUEsR0FBQyxXQUMxQixFQUFDLElBQUlnSCxJQUFJLENBQUNULFdBQVcsQ0FBQ1UsV0FBVyxDQUFDLENBQUNDLGtCQUFrQixDQUFDLENBQUM7Y0FBQSxDQUM1RCxDQUFDLEtBQUF6SixhQUFBLEdBQUF3QixDQUFBLFdBQ0xzSCxXQUFXLENBQUMzRCxRQUFRLEtBQUt1RSxTQUFTLElBQUExSixhQUFBLEdBQUF3QixDQUFBLFdBQ3BDMUIsS0FBQSxDQUFDeEIsSUFBSTtnQkFBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDc0gsbUJBQW9CO2dCQUFBcEgsUUFBQSxHQUNyQ3VHLFdBQVcsQ0FBQzNELFFBQVEsRUFBQyxLQUFHLEVBQUMyRCxXQUFXLENBQUMxQixLQUFLO2NBQUEsQ0FDdkMsQ0FBQyxLQUFBcEgsYUFBQSxHQUFBd0IsQ0FBQSxXQUNMLElBQUk7WUFBQSxDQUNKLENBQUMsRUFFUDVCLElBQUEsQ0FBQ0wsWUFBWTtjQUFDcUUsSUFBSSxFQUFFLEVBQUc7Y0FBQ0MsS0FBSyxFQUFFOUQsTUFBTSxDQUFDTztZQUFLLENBQUUsQ0FBQztVQUFBLEdBeEN6QndJLFdBQVcsQ0FBQ3hILEVBeUNqQixDQUFDO1FBQUQsQ0FDbkIsQ0FBQztNQUFBLENBQ0UsQ0FBQyxFQUdQeEIsS0FBQSxDQUFDbEIsSUFBSTtRQUFDMkUsT0FBTyxFQUFDLFVBQVU7UUFBQ25CLEtBQUssRUFBRUMsTUFBTSxDQUFDdUgsWUFBYTtRQUFBckgsUUFBQSxHQUNsRHpDLEtBQUEsQ0FBQ3pCLElBQUk7VUFBQytELEtBQUssRUFBRUMsTUFBTSxDQUFDd0gsY0FBZTtVQUFBdEgsUUFBQSxHQUNqQzNDLElBQUEsQ0FBQ0YsR0FBRztZQUFDa0UsSUFBSSxFQUFFLEVBQUc7WUFBQ0MsS0FBSyxFQUFFOUQsTUFBTSxDQUFDSTtVQUFPLENBQUUsQ0FBQyxFQUN2Q1AsSUFBQSxDQUFDdEIsSUFBSTtZQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUNxQixZQUFhO1lBQUFuQixRQUFBLEVBQUM7VUFBVyxDQUFNLENBQUM7UUFBQSxDQUNoRCxDQUFDLEVBRU52QixJQUFJLENBQUM4SSxVQUFVLENBQUM3SCxHQUFHLENBQUMsVUFBQzhILE9BQU8sRUFBRS9ELEtBQUssRUFDbEM7VUFBQWhHLGFBQUEsR0FBQVMsQ0FBQTtVQUFBVCxhQUFBLEdBQUFDLENBQUE7VUFBQSxPQUFBSCxLQUFBLENBQUN6QixJQUFJO1lBQWErRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzJILFdBQVk7WUFBQXpILFFBQUEsR0FDMUMzQyxJQUFBLENBQUN0QixJQUFJO2NBQUM4RCxLQUFLLEVBQUVDLE1BQU0sQ0FBQzRILFlBQWE7Y0FBQTFILFFBQUEsRUFBRXdILE9BQU8sQ0FBQ2pIO1lBQUssQ0FBTyxDQUFDLEVBQ3hEbEQsSUFBQSxDQUFDdEIsSUFBSTtjQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUM2SCxrQkFBbUI7Y0FBQTNILFFBQUEsRUFBRXdILE9BQU8sQ0FBQ3BCO1lBQVcsQ0FBTyxDQUFDLEVBQ3BFL0ksSUFBQSxDQUFDdEIsSUFBSTtjQUFDOEQsS0FBSyxFQUFFQyxNQUFNLENBQUM4SCxxQkFBc0I7Y0FBQTVILFFBQUEsRUFBRXdILE9BQU8sQ0FBQ0s7WUFBYyxDQUFPLENBQUM7VUFBQSxHQUhqRXBFLEtBSUwsQ0FBQztRQUFELENBQ1AsQ0FBQztNQUFBLENBQ0UsQ0FBQyxFQUdQcEcsSUFBQSxDQUFDdkIsSUFBSTtRQUFDK0QsS0FBSyxFQUFFQyxNQUFNLENBQUNnSSxhQUFjO1FBQUE5SCxRQUFBLEVBQ2hDekMsS0FBQSxDQUFDcEIsZ0JBQWdCO1VBQUMwRCxLQUFLLEVBQUVDLE1BQU0sQ0FBQ2lJLFlBQWE7VUFBQS9ILFFBQUEsR0FDM0MzQyxJQUFBLENBQUNILFFBQVE7WUFBQ21FLElBQUksRUFBRSxFQUFHO1lBQUNDLEtBQUssRUFBRTlELE1BQU0sQ0FBQ0c7VUFBUSxDQUFFLENBQUMsRUFDN0NOLElBQUEsQ0FBQ3RCLElBQUk7WUFBQzhELEtBQUssRUFBRUMsTUFBTSxDQUFDa0ksZ0JBQWlCO1lBQUFoSSxRQUFBLEVBQUM7VUFBc0IsQ0FBTSxDQUFDO1FBQUEsQ0FDbkQ7TUFBQyxDQUNmLENBQUM7SUFBQSxDQUNHO0VBQUMsQ0FDRCxDQUFDO0FBRW5CO0FBRUEsSUFBTUYsTUFBTSxJQUFBckMsYUFBQSxHQUFBQyxDQUFBLFFBQUcxQixVQUFVLENBQUNpTSxNQUFNLENBQUM7RUFDL0JsSSxTQUFTLEVBQUU7SUFDVG1JLElBQUksRUFBRSxDQUFDO0lBQ1BwRSxlQUFlLEVBQUV0RyxNQUFNLENBQUNRO0VBQzFCLENBQUM7RUFDRGlDLFVBQVUsRUFBRTtJQUNWaUksSUFBSSxFQUFFO0VBQ1IsQ0FBQztFQUNENUgsTUFBTSxFQUFFO0lBQ042SCxPQUFPLEVBQUUsRUFBRTtJQUNYQyxhQUFhLEVBQUU7RUFDakIsQ0FBQztFQUNEN0gsS0FBSyxFQUFFO0lBQ0w4SCxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsWUFBWTtJQUN4QmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ007RUFDaEIsQ0FBQztFQUNEMEMsUUFBUSxFQUFFO0lBQ1I2SCxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsZUFBZTtJQUMzQmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ08sSUFBSTtJQUNsQndLLFNBQVMsRUFBRTtFQUNiLENBQUM7RUFDRDlILGNBQWMsRUFBRTtJQUNkK0gsYUFBYSxFQUFFLEtBQUs7SUFDcEJDLGdCQUFnQixFQUFFLEVBQUU7SUFDcEJDLFlBQVksRUFBRSxFQUFFO0lBQ2hCNUUsZUFBZSxFQUFFdEcsTUFBTSxDQUFDUSxTQUFTO0lBQ2pDMkssWUFBWSxFQUFFLEVBQUU7SUFDaEJSLE9BQU8sRUFBRTtFQUNYLENBQUM7RUFDRHhILFlBQVksRUFBRTtJQUNadUgsSUFBSSxFQUFFLENBQUM7SUFDUFUsZUFBZSxFQUFFLEVBQUU7SUFDbkJDLFVBQVUsRUFBRSxRQUFRO0lBQ3BCRixZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNEL0gsa0JBQWtCLEVBQUU7SUFDbEJrRCxlQUFlLEVBQUV0RyxNQUFNLENBQUNLLEtBQUs7SUFDN0JpTCxXQUFXLEVBQUUsTUFBTTtJQUNuQkMsWUFBWSxFQUFFO01BQ1p2RCxLQUFLLEVBQUUsQ0FBQztNQUNSM0IsTUFBTSxFQUFFO0lBQ1YsQ0FBQztJQUNEbUYsYUFBYSxFQUFFLEdBQUc7SUFDbEJDLFlBQVksRUFBRSxDQUFDO0lBQ2ZDLFNBQVMsRUFBRTtFQUNiLENBQUM7RUFDRHBJLGdCQUFnQixFQUFFO0lBQ2hCdUgsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGNBQWM7SUFDMUJoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNPO0VBQ2hCLENBQUM7RUFDRGdELHNCQUFzQixFQUFFO0lBQ3RCTyxLQUFLLEVBQUU5RCxNQUFNLENBQUNNO0VBQ2hCLENBQUM7RUFDRG1ELFdBQVcsRUFBRTtJQUNYd0gsZ0JBQWdCLEVBQUUsRUFBRTtJQUNwQkMsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRHhILGFBQWEsRUFBRTtJQUNic0gsYUFBYSxFQUFFLEtBQUs7SUFDcEJXLGNBQWMsRUFBRSxlQUFlO0lBQy9CTixVQUFVLEVBQUUsUUFBUTtJQUNwQkgsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRHRILFdBQVcsRUFBRTtJQUNYK0csT0FBTyxFQUFFO0VBQ1gsQ0FBQztFQUNENUcsWUFBWSxFQUFFO0lBQ1ppSCxhQUFhLEVBQUUsS0FBSztJQUNwQlcsY0FBYyxFQUFFO0VBQ2xCLENBQUM7RUFDRDNILFdBQVcsRUFBRTtJQUNYcUgsVUFBVSxFQUFFLFFBQVE7SUFDcEJYLElBQUksRUFBRTtFQUNSLENBQUM7RUFDRHpHLFlBQVksRUFBRTtJQUNaNEcsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLFlBQVk7SUFDeEJoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNNO0VBQ2hCLENBQUM7RUFDRDhELFlBQVksRUFBRTtJQUNaeUcsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGVBQWU7SUFDM0JoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNPLElBQUk7SUFDbEJ3SyxTQUFTLEVBQUUsQ0FBQztJQUNaYSxTQUFTLEVBQUU7RUFDYixDQUFDO0VBQ0RwSCxVQUFVLEVBQUU7SUFDVnlHLGdCQUFnQixFQUFFLEVBQUU7SUFDcEJDLFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0R6RyxhQUFhLEVBQUU7SUFDYnVHLGFBQWEsRUFBRSxLQUFLO0lBQ3BCVyxjQUFjLEVBQUUsZUFBZTtJQUMvQk4sVUFBVSxFQUFFLFFBQVE7SUFDcEJILFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0R2SCxZQUFZLEVBQUU7SUFDWmtILFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRSxnQkFBZ0I7SUFDNUJoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNNO0VBQ2hCLENBQUM7RUFDRDRILGVBQWUsRUFBRTtJQUNmMkMsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGVBQWU7SUFDM0JoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNPLElBQUk7SUFDbEIySyxZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNEeEcsV0FBVyxFQUFFO0lBQ1htRyxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsY0FBYztJQUMxQmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ0c7RUFDaEIsQ0FBQztFQUNEd0UsVUFBVSxFQUFFO0lBQ1ZxRyxhQUFhLEVBQUUsS0FBSztJQUNwQmEsUUFBUSxFQUFFLE1BQU07SUFDaEJGLGNBQWMsRUFBRTtFQUNsQixDQUFDO0VBQ0R4RyxTQUFTLEVBQUU7SUFDVDZDLEtBQUssRUFBRSxLQUFLO0lBQ1pxRCxVQUFVLEVBQUUsUUFBUTtJQUNwQkgsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRDFGLFNBQVMsRUFBRTtJQUNUcUYsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGNBQWM7SUFDMUJoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNNLElBQUk7SUFDbEJ5SyxTQUFTLEVBQUUsQ0FBQztJQUNaYSxTQUFTLEVBQUU7RUFDYixDQUFDO0VBQ0RoRyxXQUFXLEVBQUU7SUFDWG9GLGFBQWEsRUFBRSxLQUFLO0lBQ3BCSyxVQUFVLEVBQUUsUUFBUTtJQUNwQk4sU0FBUyxFQUFFO0VBQ2IsQ0FBQztFQUNEbEYsZUFBZSxFQUFFO0lBQ2ZnRixRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsY0FBYztJQUMxQmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ0csT0FBTztJQUNyQjJMLFVBQVUsRUFBRTtFQUNkLENBQUM7RUFDRC9GLFlBQVksRUFBRTtJQUNaa0YsZ0JBQWdCLEVBQUUsRUFBRTtJQUNwQkMsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRGxGLFdBQVcsRUFBRTtJQUNYZ0YsYUFBYSxFQUFFLEtBQUs7SUFDcEJXLGNBQWMsRUFBRSxlQUFlO0lBQy9CVCxZQUFZLEVBQUUsRUFBRTtJQUNoQmEsaUJBQWlCLEVBQUU7RUFDckIsQ0FBQztFQUNEN0YsUUFBUSxFQUFFO0lBQ1JtRixVQUFVLEVBQUUsUUFBUTtJQUNwQlgsSUFBSSxFQUFFO0VBQ1IsQ0FBQztFQUNEdkUsaUJBQWlCLEVBQUU7SUFDakJFLE1BQU0sRUFBRSxFQUFFO0lBQ1ZzRixjQUFjLEVBQUUsVUFBVTtJQUMxQlQsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRDlFLFFBQVEsRUFBRTtJQUNSNEIsS0FBSyxFQUFFLEVBQUU7SUFDVDFCLGVBQWUsRUFBRXRHLE1BQU0sQ0FBQ0csT0FBTztJQUMvQmdMLFlBQVksRUFBRSxDQUFDO0lBQ2ZhLFNBQVMsRUFBRTtFQUNiLENBQUM7RUFDRHpGLGFBQWEsRUFBRTtJQUNic0UsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGNBQWM7SUFDMUJoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNPLElBQUk7SUFDbEIySyxZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNEekUsYUFBYSxFQUFFO0lBQ2JvRSxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsZ0JBQWdCO0lBQzVCaEgsS0FBSyxFQUFFOUQsTUFBTSxDQUFDTTtFQUNoQixDQUFDO0VBQ0RvRyxlQUFlLEVBQUU7SUFDZnNFLGFBQWEsRUFBRSxLQUFLO0lBQ3BCVyxjQUFjLEVBQUUsY0FBYztJQUM5Qk0sVUFBVSxFQUFFLEVBQUU7SUFDZEMsY0FBYyxFQUFFLENBQUM7SUFDakJDLGNBQWMsRUFBRW5NLE1BQU0sQ0FBQ1E7RUFDekIsQ0FBQztFQUNEdUcsU0FBUyxFQUFFO0lBQ1RrRSxnQkFBZ0IsRUFBRSxFQUFFO0lBQ3BCQyxZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNENUQsUUFBUSxFQUFFO0lBQ1I0RCxZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNEM0QsVUFBVSxFQUFFO0lBQ1Z5RCxhQUFhLEVBQUUsS0FBSztJQUNwQkssVUFBVSxFQUFFLFFBQVE7SUFDcEJILFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0QxRCxRQUFRLEVBQUU7SUFDUlEsS0FBSyxFQUFFLEVBQUU7SUFDVDNCLE1BQU0sRUFBRSxFQUFFO0lBQ1Y4RSxZQUFZLEVBQUUsRUFBRTtJQUNoQjdFLGVBQWUsRUFBRXRHLE1BQU0sQ0FBQ1EsU0FBUztJQUNqQzZLLFVBQVUsRUFBRSxRQUFRO0lBQ3BCTSxjQUFjLEVBQUUsUUFBUTtJQUN4QlMsV0FBVyxFQUFFO0VBQ2YsQ0FBQztFQUNEM0UsV0FBVyxFQUFFO0lBQ1hpRCxJQUFJLEVBQUU7RUFDUixDQUFDO0VBQ0RoRCxRQUFRLEVBQUU7SUFDUm1ELFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRSxjQUFjO0lBQzFCaEgsS0FBSyxFQUFFOUQsTUFBTSxDQUFDTTtFQUNoQixDQUFDO0VBQ0RxSCxZQUFZLEVBQUU7SUFDWmtELFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRSxlQUFlO0lBQzNCaEgsS0FBSyxFQUFFOUQsTUFBTSxDQUFDTyxJQUFJO0lBQ2xCd0ssU0FBUyxFQUFFO0VBQ2IsQ0FBQztFQUNEbkQsY0FBYyxFQUFFO0lBQ2RpRCxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsZ0JBQWdCO0lBQzVCaEgsS0FBSyxFQUFFOUQsTUFBTSxDQUFDRztFQUNoQixDQUFDO0VBQ0QySCxlQUFlLEVBQUU7SUFDZnpCLE1BQU0sRUFBRSxDQUFDO0lBQ1RDLGVBQWUsRUFBRXRHLE1BQU0sQ0FBQ1EsU0FBUztJQUNqQzJLLFlBQVksRUFBRSxDQUFDO0lBQ2ZXLFVBQVUsRUFBRTtFQUNkLENBQUM7RUFDRC9ELGdCQUFnQixFQUFFO0lBQ2hCMUIsTUFBTSxFQUFFLE1BQU07SUFDZEMsZUFBZSxFQUFFdEcsTUFBTSxDQUFDRyxPQUFPO0lBQy9CZ0wsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRGxELFVBQVUsRUFBRTtJQUNWZ0QsZ0JBQWdCLEVBQUUsRUFBRTtJQUNwQkMsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRDdDLFNBQVMsRUFBRTtJQUNUNkMsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRDVDLFdBQVcsRUFBRTtJQUNYMEMsYUFBYSxFQUFFLEtBQUs7SUFDcEJXLGNBQWMsRUFBRSxlQUFlO0lBQy9CTixVQUFVLEVBQUUsUUFBUTtJQUNwQkgsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRDNDLFdBQVcsRUFBRTtJQUNYc0MsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGdCQUFnQjtJQUM1QmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ007RUFDaEIsQ0FBQztFQUNEbUksV0FBVyxFQUFFO0lBQ1h1QyxhQUFhLEVBQUUsS0FBSztJQUNwQkssVUFBVSxFQUFFO0VBQ2QsQ0FBQztFQUNEM0MsZUFBZSxFQUFFO0lBQ2ZtQyxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsZ0JBQWdCO0lBQzVCZ0IsVUFBVSxFQUFFO0VBQ2QsQ0FBQztFQUNEbkQsZ0JBQWdCLEVBQUU7SUFDaEJrQyxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsZUFBZTtJQUMzQmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ08sSUFBSTtJQUNsQjhMLFVBQVUsRUFBRTtFQUNkLENBQUM7RUFDRHhELGdCQUFnQixFQUFFO0lBQ2hCb0MsZ0JBQWdCLEVBQUUsRUFBRTtJQUNwQkMsWUFBWSxFQUFFO0VBQ2hCLENBQUM7RUFDRGxDLGVBQWUsRUFBRTtJQUNmZ0MsYUFBYSxFQUFFLEtBQUs7SUFDcEJLLFVBQVUsRUFBRSxRQUFRO0lBQ3BCRCxlQUFlLEVBQUUsRUFBRTtJQUNuQmtCLGlCQUFpQixFQUFFLENBQUM7SUFDcEJDLGlCQUFpQixFQUFFdk0sTUFBTSxDQUFDUTtFQUM1QixDQUFDO0VBQ0R5SSxlQUFlLEVBQUU7SUFDZmpCLEtBQUssRUFBRSxFQUFFO0lBQ1QzQixNQUFNLEVBQUUsRUFBRTtJQUNWOEUsWUFBWSxFQUFFLEVBQUU7SUFDaEJFLFVBQVUsRUFBRSxRQUFRO0lBQ3BCTSxjQUFjLEVBQUUsUUFBUTtJQUN4QlMsV0FBVyxFQUFFO0VBQ2YsQ0FBQztFQUNEakQsa0JBQWtCLEVBQUU7SUFDbEJ1QixJQUFJLEVBQUU7RUFDUixDQUFDO0VBQ0R0QixnQkFBZ0IsRUFBRTtJQUNoQnlCLFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRSxnQkFBZ0I7SUFDNUJoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNNO0VBQ2hCLENBQUM7RUFDRCtJLHNCQUFzQixFQUFFO0lBQ3RCbUQsT0FBTyxFQUFFO0VBQ1gsQ0FBQztFQUNEbEQsc0JBQXNCLEVBQUU7SUFDdEJ1QixRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsZUFBZTtJQUMzQmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ08sSUFBSTtJQUNsQndLLFNBQVMsRUFBRTtFQUNiLENBQUM7RUFDRHhCLGVBQWUsRUFBRTtJQUNmc0IsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGVBQWU7SUFDM0JoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNHLE9BQU87SUFDckI0SyxTQUFTLEVBQUU7RUFDYixDQUFDO0VBQ0RuQixtQkFBbUIsRUFBRTtJQUNuQmlCLFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRSxlQUFlO0lBQzNCaEgsS0FBSyxFQUFFOUQsTUFBTSxDQUFDTyxJQUFJO0lBQ2xCd0ssU0FBUyxFQUFFO0VBQ2IsQ0FBQztFQUNEbEIsWUFBWSxFQUFFO0lBQ1pvQixnQkFBZ0IsRUFBRSxFQUFFO0lBQ3BCQyxZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNEcEIsY0FBYyxFQUFFO0lBQ2RrQixhQUFhLEVBQUUsS0FBSztJQUNwQkssVUFBVSxFQUFFLFFBQVE7SUFDcEJILFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0RqQixXQUFXLEVBQUU7SUFDWGlCLFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0RoQixZQUFZLEVBQUU7SUFDWlcsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGdCQUFnQjtJQUM1QmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ00sSUFBSTtJQUNsQjRLLFlBQVksRUFBRTtFQUNoQixDQUFDO0VBQ0RmLGtCQUFrQixFQUFFO0lBQ2xCVSxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUUsZUFBZTtJQUMzQmhILEtBQUssRUFBRTlELE1BQU0sQ0FBQ08sSUFBSTtJQUNsQjhMLFVBQVUsRUFBRSxFQUFFO0lBQ2RuQixZQUFZLEVBQUU7RUFDaEIsQ0FBQztFQUNEZCxxQkFBcUIsRUFBRTtJQUNyQlMsUUFBUSxFQUFFLEVBQUU7SUFDWkMsVUFBVSxFQUFFLGNBQWM7SUFDMUJoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNHO0VBQ2hCLENBQUM7RUFDRG1LLGFBQWEsRUFBRTtJQUNiSyxPQUFPLEVBQUUsRUFBRTtJQUNYc0IsVUFBVSxFQUFFO0VBQ2QsQ0FBQztFQUNEMUIsWUFBWSxFQUFFO0lBQ1pTLGFBQWEsRUFBRSxLQUFLO0lBQ3BCSyxVQUFVLEVBQUUsUUFBUTtJQUNwQk0sY0FBYyxFQUFFLFFBQVE7SUFDeEJQLGVBQWUsRUFBRSxFQUFFO0lBQ25CVyxpQkFBaUIsRUFBRSxFQUFFO0lBQ3JCWixZQUFZLEVBQUUsRUFBRTtJQUNoQnNCLFdBQVcsRUFBRSxDQUFDO0lBQ2RDLFdBQVcsRUFBRTFNLE1BQU0sQ0FBQ0csT0FBTztJQUMzQm1HLGVBQWUsRUFBRXRHLE1BQU0sQ0FBQ0s7RUFDMUIsQ0FBQztFQUNEbUssZ0JBQWdCLEVBQUU7SUFDaEJLLFFBQVEsRUFBRSxFQUFFO0lBQ1pDLFVBQVUsRUFBRSxnQkFBZ0I7SUFDNUJoSCxLQUFLLEVBQUU5RCxNQUFNLENBQUNHLE9BQU87SUFDckIyTCxVQUFVLEVBQUU7RUFDZDtBQUNGLENBQUMsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==