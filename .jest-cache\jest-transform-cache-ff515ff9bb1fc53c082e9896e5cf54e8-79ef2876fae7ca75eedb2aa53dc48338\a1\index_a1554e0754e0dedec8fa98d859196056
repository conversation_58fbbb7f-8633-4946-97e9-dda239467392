a30a45678fe99c28b4d76ee0d79a8a81
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
var _excluded = ["style"];
var cssFunction = function () {
  if (_canUseDom.default && window.CSS && window.CSS.supports && window.CSS.supports('top: constant(safe-area-inset-top)')) {
    return 'constant';
  }
  return 'env';
}();
var SafeAreaView = React.forwardRef(function (props, ref) {
  var style = props.style,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  return React.createElement(_View.default, (0, _extends2.default)({}, rest, {
    ref: ref,
    style: [styles.root, style]
  }));
});
SafeAreaView.displayName = 'SafeAreaView';
var styles = _StyleSheet.default.create({
  root: {
    paddingTop: cssFunction + "(safe-area-inset-top)",
    paddingRight: cssFunction + "(safe-area-inset-right)",
    paddingBottom: cssFunction + "(safe-area-inset-bottom)",
    paddingLeft: cssFunction + "(safe-area-inset-left)"
  }
});
var _default = exports.default = SafeAreaView;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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