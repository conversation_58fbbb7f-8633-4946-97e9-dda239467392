1cd623b4837d9d9dc3c9bf04ca9b4971
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.addNode = addNode;
exports.attachListeners = attachListeners;
exports.getResponderNode = getResponderNode;
exports.removeNode = removeNode;
exports.terminateResponder = terminateResponder;
var _createResponderEvent = _interopRequireDefault(require("./createResponderEvent"));
var _ResponderEventTypes = require("./ResponderEventTypes");
var _utils = require("./utils");
var _ResponderTouchHistoryStore = require("./ResponderTouchHistoryStore");
var _canUseDom = _interopRequireDefault(require("../canUseDom"));
var emptyObject = {};
var startRegistration = ['onStartShouldSetResponderCapture', 'onStartShouldSetResponder', {
  bubbles: true
}];
var moveRegistration = ['onMoveShouldSetResponderCapture', 'onMoveShouldSetResponder', {
  bubbles: true
}];
var scrollRegistration = ['onScrollShouldSetResponderCapture', 'onScrollShouldSetResponder', {
  bubbles: false
}];
var shouldSetResponderEvents = {
  touchstart: startRegistration,
  mousedown: startRegistration,
  touchmove: moveRegistration,
  mousemove: moveRegistration,
  scroll: scrollRegistration
};
var emptyResponder = {
  id: null,
  idPath: null,
  node: null
};
var responderListenersMap = new Map();
var isEmulatingMouseEvents = false;
var trackedTouchCount = 0;
var currentResponder = {
  id: null,
  node: null,
  idPath: null
};
var responderTouchHistoryStore = new _ResponderTouchHistoryStore.ResponderTouchHistoryStore();
function changeCurrentResponder(responder) {
  currentResponder = responder;
}
function getResponderConfig(id) {
  var config = responderListenersMap.get(id);
  return config != null ? config : emptyObject;
}
function eventListener(domEvent) {
  var eventType = domEvent.type;
  var eventTarget = domEvent.target;
  if (eventType === 'touchstart') {
    isEmulatingMouseEvents = true;
  }
  if (eventType === 'touchmove' || trackedTouchCount > 1) {
    isEmulatingMouseEvents = false;
  }
  if (eventType === 'mousedown' && isEmulatingMouseEvents || eventType === 'mousemove' && isEmulatingMouseEvents || eventType === 'mousemove' && trackedTouchCount < 1) {
    return;
  }
  if (isEmulatingMouseEvents && eventType === 'mouseup') {
    if (trackedTouchCount === 0) {
      isEmulatingMouseEvents = false;
    }
    return;
  }
  var isStartEvent = (0, _ResponderEventTypes.isStartish)(eventType) && (0, _utils.isPrimaryPointerDown)(domEvent);
  var isMoveEvent = (0, _ResponderEventTypes.isMoveish)(eventType);
  var isEndEvent = (0, _ResponderEventTypes.isEndish)(eventType);
  var isScrollEvent = (0, _ResponderEventTypes.isScroll)(eventType);
  var isSelectionChangeEvent = (0, _ResponderEventTypes.isSelectionChange)(eventType);
  var responderEvent = (0, _createResponderEvent.default)(domEvent, responderTouchHistoryStore);
  if (isStartEvent || isMoveEvent || isEndEvent) {
    if (domEvent.touches) {
      trackedTouchCount = domEvent.touches.length;
    } else {
      if (isStartEvent) {
        trackedTouchCount = 1;
      } else if (isEndEvent) {
        trackedTouchCount = 0;
      }
    }
    responderTouchHistoryStore.recordTouchTrack(eventType, responderEvent.nativeEvent);
  }
  var eventPaths = (0, _utils.getResponderPaths)(domEvent);
  var wasNegotiated = false;
  var wantsResponder;
  if (isStartEvent || isMoveEvent || isScrollEvent && trackedTouchCount > 0) {
    var currentResponderIdPath = currentResponder.idPath;
    var eventIdPath = eventPaths.idPath;
    if (currentResponderIdPath != null && eventIdPath != null) {
      var lowestCommonAncestor = (0, _utils.getLowestCommonAncestor)(currentResponderIdPath, eventIdPath);
      if (lowestCommonAncestor != null) {
        var indexOfLowestCommonAncestor = eventIdPath.indexOf(lowestCommonAncestor);
        var index = indexOfLowestCommonAncestor + (lowestCommonAncestor === currentResponder.id ? 1 : 0);
        eventPaths = {
          idPath: eventIdPath.slice(index),
          nodePath: eventPaths.nodePath.slice(index)
        };
      } else {
        eventPaths = null;
      }
    }
    if (eventPaths != null) {
      wantsResponder = findWantsResponder(eventPaths, domEvent, responderEvent);
      if (wantsResponder != null) {
        attemptTransfer(responderEvent, wantsResponder);
        wasNegotiated = true;
      }
    }
  }
  if (currentResponder.id != null && currentResponder.node != null) {
    var _currentResponder = currentResponder,
      id = _currentResponder.id,
      node = _currentResponder.node;
    var _getResponderConfig = getResponderConfig(id),
      onResponderStart = _getResponderConfig.onResponderStart,
      onResponderMove = _getResponderConfig.onResponderMove,
      onResponderEnd = _getResponderConfig.onResponderEnd,
      onResponderRelease = _getResponderConfig.onResponderRelease,
      onResponderTerminate = _getResponderConfig.onResponderTerminate,
      onResponderTerminationRequest = _getResponderConfig.onResponderTerminationRequest;
    responderEvent.bubbles = false;
    responderEvent.cancelable = false;
    responderEvent.currentTarget = node;
    if (isStartEvent) {
      if (onResponderStart != null) {
        responderEvent.dispatchConfig.registrationName = 'onResponderStart';
        onResponderStart(responderEvent);
      }
    } else if (isMoveEvent) {
      if (onResponderMove != null) {
        responderEvent.dispatchConfig.registrationName = 'onResponderMove';
        onResponderMove(responderEvent);
      }
    } else {
      var isTerminateEvent = (0, _ResponderEventTypes.isCancelish)(eventType) || eventType === 'contextmenu' || eventType === 'blur' && eventTarget === window || eventType === 'blur' && eventTarget.contains(node) && domEvent.relatedTarget !== node || isScrollEvent && trackedTouchCount === 0 || isScrollEvent && eventTarget.contains(node) && eventTarget !== node || isSelectionChangeEvent && (0, _utils.hasValidSelection)(domEvent);
      var isReleaseEvent = isEndEvent && !isTerminateEvent && !(0, _utils.hasTargetTouches)(node, domEvent.touches);
      if (isEndEvent) {
        if (onResponderEnd != null) {
          responderEvent.dispatchConfig.registrationName = 'onResponderEnd';
          onResponderEnd(responderEvent);
        }
      }
      if (isReleaseEvent) {
        if (onResponderRelease != null) {
          responderEvent.dispatchConfig.registrationName = 'onResponderRelease';
          onResponderRelease(responderEvent);
        }
        changeCurrentResponder(emptyResponder);
      }
      if (isTerminateEvent) {
        var shouldTerminate = true;
        if (eventType === 'contextmenu' || eventType === 'scroll' || eventType === 'selectionchange') {
          if (wasNegotiated) {
            shouldTerminate = false;
          } else if (onResponderTerminationRequest != null) {
            responderEvent.dispatchConfig.registrationName = 'onResponderTerminationRequest';
            if (onResponderTerminationRequest(responderEvent) === false) {
              shouldTerminate = false;
            }
          }
        }
        if (shouldTerminate) {
          if (onResponderTerminate != null) {
            responderEvent.dispatchConfig.registrationName = 'onResponderTerminate';
            onResponderTerminate(responderEvent);
          }
          changeCurrentResponder(emptyResponder);
          isEmulatingMouseEvents = false;
          trackedTouchCount = 0;
        }
      }
    }
  }
}
function findWantsResponder(eventPaths, domEvent, responderEvent) {
  var shouldSetCallbacks = shouldSetResponderEvents[domEvent.type];
  if (shouldSetCallbacks != null) {
    var idPath = eventPaths.idPath,
      nodePath = eventPaths.nodePath;
    var shouldSetCallbackCaptureName = shouldSetCallbacks[0];
    var shouldSetCallbackBubbleName = shouldSetCallbacks[1];
    var bubbles = shouldSetCallbacks[2].bubbles;
    var check = function check(id, node, callbackName) {
      var config = getResponderConfig(id);
      var shouldSetCallback = config[callbackName];
      if (shouldSetCallback != null) {
        responderEvent.currentTarget = node;
        if (shouldSetCallback(responderEvent) === true) {
          var prunedIdPath = idPath.slice(idPath.indexOf(id));
          return {
            id: id,
            node: node,
            idPath: prunedIdPath
          };
        }
      }
    };
    for (var i = idPath.length - 1; i >= 0; i--) {
      var id = idPath[i];
      var node = nodePath[i];
      var result = check(id, node, shouldSetCallbackCaptureName);
      if (result != null) {
        return result;
      }
      if (responderEvent.isPropagationStopped() === true) {
        return;
      }
    }
    if (bubbles) {
      for (var _i = 0; _i < idPath.length; _i++) {
        var _id = idPath[_i];
        var _node = nodePath[_i];
        var _result = check(_id, _node, shouldSetCallbackBubbleName);
        if (_result != null) {
          return _result;
        }
        if (responderEvent.isPropagationStopped() === true) {
          return;
        }
      }
    } else {
      var _id2 = idPath[0];
      var _node2 = nodePath[0];
      var target = domEvent.target;
      if (target === _node2) {
        return check(_id2, _node2, shouldSetCallbackBubbleName);
      }
    }
  }
}
function attemptTransfer(responderEvent, wantsResponder) {
  var _currentResponder2 = currentResponder,
    currentId = _currentResponder2.id,
    currentNode = _currentResponder2.node;
  var id = wantsResponder.id,
    node = wantsResponder.node;
  var _getResponderConfig2 = getResponderConfig(id),
    onResponderGrant = _getResponderConfig2.onResponderGrant,
    onResponderReject = _getResponderConfig2.onResponderReject;
  responderEvent.bubbles = false;
  responderEvent.cancelable = false;
  responderEvent.currentTarget = node;
  if (currentId == null) {
    if (onResponderGrant != null) {
      responderEvent.currentTarget = node;
      responderEvent.dispatchConfig.registrationName = 'onResponderGrant';
      onResponderGrant(responderEvent);
    }
    changeCurrentResponder(wantsResponder);
  } else {
    var _getResponderConfig3 = getResponderConfig(currentId),
      onResponderTerminate = _getResponderConfig3.onResponderTerminate,
      onResponderTerminationRequest = _getResponderConfig3.onResponderTerminationRequest;
    var allowTransfer = true;
    if (onResponderTerminationRequest != null) {
      responderEvent.currentTarget = currentNode;
      responderEvent.dispatchConfig.registrationName = 'onResponderTerminationRequest';
      if (onResponderTerminationRequest(responderEvent) === false) {
        allowTransfer = false;
      }
    }
    if (allowTransfer) {
      if (onResponderTerminate != null) {
        responderEvent.currentTarget = currentNode;
        responderEvent.dispatchConfig.registrationName = 'onResponderTerminate';
        onResponderTerminate(responderEvent);
      }
      if (onResponderGrant != null) {
        responderEvent.currentTarget = node;
        responderEvent.dispatchConfig.registrationName = 'onResponderGrant';
        onResponderGrant(responderEvent);
      }
      changeCurrentResponder(wantsResponder);
    } else {
      if (onResponderReject != null) {
        responderEvent.currentTarget = node;
        responderEvent.dispatchConfig.registrationName = 'onResponderReject';
        onResponderReject(responderEvent);
      }
    }
  }
}
var documentEventsCapturePhase = ['blur', 'scroll'];
var documentEventsBubblePhase = ['mousedown', 'mousemove', 'mouseup', 'dragstart', 'touchstart', 'touchmove', 'touchend', 'touchcancel', 'contextmenu', 'select', 'selectionchange'];
function attachListeners() {
  if (_canUseDom.default && window.__reactResponderSystemActive == null) {
    window.addEventListener('blur', eventListener);
    documentEventsBubblePhase.forEach(function (eventType) {
      document.addEventListener(eventType, eventListener);
    });
    documentEventsCapturePhase.forEach(function (eventType) {
      document.addEventListener(eventType, eventListener, true);
    });
    window.__reactResponderSystemActive = true;
  }
}
function addNode(id, node, config) {
  (0, _utils.setResponderId)(node, id);
  responderListenersMap.set(id, config);
}
function removeNode(id) {
  if (currentResponder.id === id) {
    terminateResponder();
  }
  if (responderListenersMap.has(id)) {
    responderListenersMap.delete(id);
  }
}
function terminateResponder() {
  var _currentResponder3 = currentResponder,
    id = _currentResponder3.id,
    node = _currentResponder3.node;
  if (id != null && node != null) {
    var _getResponderConfig4 = getResponderConfig(id),
      onResponderTerminate = _getResponderConfig4.onResponderTerminate;
    if (onResponderTerminate != null) {
      var event = (0, _createResponderEvent.default)({}, responderTouchHistoryStore);
      event.currentTarget = node;
      onResponderTerminate(event);
    }
    changeCurrentResponder(emptyResponder);
  }
  isEmulatingMouseEvents = false;
  trackedTouchCount = 0;
}
function getResponderNode() {
  return currentResponder.node;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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