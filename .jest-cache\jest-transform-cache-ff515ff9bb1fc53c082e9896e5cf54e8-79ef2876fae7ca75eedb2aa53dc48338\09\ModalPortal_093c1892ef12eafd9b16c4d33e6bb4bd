3780ac63615de67dd55d4b7099806031
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _reactDom = _interopRequireDefault(require("react-dom"));
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
function ModalPortal(props) {
  var children = props.children;
  var elementRef = React.useRef(null);
  if (_canUseDom.default && !elementRef.current) {
    var element = document.createElement('div');
    if (element && document.body) {
      document.body.appendChild(element);
      elementRef.current = element;
    }
  }
  React.useEffect(function () {
    if (_canUseDom.default) {
      return function () {
        if (document.body && elementRef.current) {
          document.body.removeChild(elementRef.current);
          elementRef.current = null;
        }
      };
    }
  }, []);
  return elementRef.current && _canUseDom.default ? _reactDom.default.createPortal(children, elementRef.current) : null;
}
var _default = exports.default = ModalPortal;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiUmVhY3QiLCJfcmVhY3REb20iLCJfY2FuVXNlRG9tIiwiTW9kYWxQb3J0YWwiLCJwcm9wcyIsImNoaWxkcmVuIiwiZWxlbWVudFJlZiIsInVzZVJlZiIsImN1cnJlbnQiLCJlbGVtZW50IiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiYm9keSIsImFwcGVuZENoaWxkIiwidXNlRWZmZWN0IiwicmVtb3ZlQ2hpbGQiLCJjcmVhdGVQb3J0YWwiLCJfZGVmYXVsdCIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbIk1vZGFsUG9ydGFsLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgX2ludGVyb3BSZXF1aXJlRGVmYXVsdCA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdFwiKS5kZWZhdWx0O1xudmFyIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVXaWxkY2FyZFwiKS5kZWZhdWx0O1xuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDtcbnZhciBSZWFjdCA9IF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkKHJlcXVpcmUoXCJyZWFjdFwiKSk7XG52YXIgX3JlYWN0RG9tID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwicmVhY3QtZG9tXCIpKTtcbnZhciBfY2FuVXNlRG9tID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vLi4vbW9kdWxlcy9jYW5Vc2VEb21cIikpO1xuLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE5pY29sYXMgR2FsbGFnaGVyLlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBcbiAqL1xuXG5mdW5jdGlvbiBNb2RhbFBvcnRhbChwcm9wcykge1xuICB2YXIgY2hpbGRyZW4gPSBwcm9wcy5jaGlsZHJlbjtcbiAgdmFyIGVsZW1lbnRSZWYgPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gIGlmIChfY2FuVXNlRG9tLmRlZmF1bHQgJiYgIWVsZW1lbnRSZWYuY3VycmVudCkge1xuICAgIHZhciBlbGVtZW50ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7XG4gICAgaWYgKGVsZW1lbnQgJiYgZG9jdW1lbnQuYm9keSkge1xuICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZChlbGVtZW50KTtcbiAgICAgIGVsZW1lbnRSZWYuY3VycmVudCA9IGVsZW1lbnQ7XG4gICAgfVxuICB9XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKF9jYW5Vc2VEb20uZGVmYXVsdCkge1xuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgaWYgKGRvY3VtZW50LmJvZHkgJiYgZWxlbWVudFJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChlbGVtZW50UmVmLmN1cnJlbnQpO1xuICAgICAgICAgIGVsZW1lbnRSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfVxuICB9LCBbXSk7XG4gIHJldHVybiBlbGVtZW50UmVmLmN1cnJlbnQgJiYgX2NhblVzZURvbS5kZWZhdWx0ID8gLyojX19QVVJFX18qL19yZWFjdERvbS5kZWZhdWx0LmNyZWF0ZVBvcnRhbChjaGlsZHJlbiwgZWxlbWVudFJlZi5jdXJyZW50KSA6IG51bGw7XG59XG52YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBNb2RhbFBvcnRhbDtcbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFFWixJQUFJQSxzQkFBc0IsR0FBR0MsT0FBTyxDQUFDLDhDQUE4QyxDQUFDLENBQUNDLE9BQU87QUFDNUYsSUFBSUMsdUJBQXVCLEdBQUdGLE9BQU8sQ0FBQywrQ0FBK0MsQ0FBQyxDQUFDQyxPQUFPO0FBQzlGRSxPQUFPLENBQUNDLFVBQVUsR0FBRyxJQUFJO0FBQ3pCRCxPQUFPLENBQUNGLE9BQU8sR0FBRyxLQUFLLENBQUM7QUFDeEIsSUFBSUksS0FBSyxHQUFHSCx1QkFBdUIsQ0FBQ0YsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDO0FBQ3JELElBQUlNLFNBQVMsR0FBR1Asc0JBQXNCLENBQUNDLE9BQU8sQ0FBQyxXQUFXLENBQUMsQ0FBQztBQUM1RCxJQUFJTyxVQUFVLEdBQUdSLHNCQUFzQixDQUFDQyxPQUFPLDBCQUEwQixDQUFDLENBQUM7QUFXM0UsU0FBU1EsV0FBV0EsQ0FBQ0MsS0FBSyxFQUFFO0VBQzFCLElBQUlDLFFBQVEsR0FBR0QsS0FBSyxDQUFDQyxRQUFRO0VBQzdCLElBQUlDLFVBQVUsR0FBR04sS0FBSyxDQUFDTyxNQUFNLENBQUMsSUFBSSxDQUFDO0VBQ25DLElBQUlMLFVBQVUsQ0FBQ04sT0FBTyxJQUFJLENBQUNVLFVBQVUsQ0FBQ0UsT0FBTyxFQUFFO0lBQzdDLElBQUlDLE9BQU8sR0FBR0MsUUFBUSxDQUFDQyxhQUFhLENBQUMsS0FBSyxDQUFDO0lBQzNDLElBQUlGLE9BQU8sSUFBSUMsUUFBUSxDQUFDRSxJQUFJLEVBQUU7TUFDNUJGLFFBQVEsQ0FBQ0UsSUFBSSxDQUFDQyxXQUFXLENBQUNKLE9BQU8sQ0FBQztNQUNsQ0gsVUFBVSxDQUFDRSxPQUFPLEdBQUdDLE9BQU87SUFDOUI7RUFDRjtFQUNBVCxLQUFLLENBQUNjLFNBQVMsQ0FBQyxZQUFNO0lBQ3BCLElBQUlaLFVBQVUsQ0FBQ04sT0FBTyxFQUFFO01BQ3RCLE9BQU8sWUFBTTtRQUNYLElBQUljLFFBQVEsQ0FBQ0UsSUFBSSxJQUFJTixVQUFVLENBQUNFLE9BQU8sRUFBRTtVQUN2Q0UsUUFBUSxDQUFDRSxJQUFJLENBQUNHLFdBQVcsQ0FBQ1QsVUFBVSxDQUFDRSxPQUFPLENBQUM7VUFDN0NGLFVBQVUsQ0FBQ0UsT0FBTyxHQUFHLElBQUk7UUFDM0I7TUFDRixDQUFDO0lBQ0g7RUFDRixDQUFDLEVBQUUsRUFBRSxDQUFDO0VBQ04sT0FBT0YsVUFBVSxDQUFDRSxPQUFPLElBQUlOLFVBQVUsQ0FBQ04sT0FBTyxHQUFnQkssU0FBUyxDQUFDTCxPQUFPLENBQUNvQixZQUFZLENBQUNYLFFBQVEsRUFBRUMsVUFBVSxDQUFDRSxPQUFPLENBQUMsR0FBRyxJQUFJO0FBQ3BJO0FBQ0EsSUFBSVMsUUFBUSxHQUFHbkIsT0FBTyxDQUFDRixPQUFPLEdBQUdPLFdBQVc7QUFDNUNlLE1BQU0sQ0FBQ3BCLE9BQU8sR0FBR0EsT0FBTyxDQUFDRixPQUFPIiwiaWdub3JlTGlzdCI6W119