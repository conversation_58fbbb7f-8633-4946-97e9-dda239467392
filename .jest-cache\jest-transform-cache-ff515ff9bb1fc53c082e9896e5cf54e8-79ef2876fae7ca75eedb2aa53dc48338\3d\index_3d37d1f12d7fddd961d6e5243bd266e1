40b00908f6560237c8cdfbf81b183216
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
var initialURL = _canUseDom.default ? window.location.href : '';
var Linking = function () {
  function Linking() {
    (0, _classCallCheck2.default)(this, Linking);
    this._eventCallbacks = {};
  }
  return (0, _createClass2.default)(Linking, [{
    key: "_dispatchEvent",
    value: function _dispatchEvent(event) {
      for (var _len = arguments.length, data = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        data[_key - 1] = arguments[_key];
      }
      var listeners = this._eventCallbacks[event];
      if (listeners != null && Array.isArray(listeners)) {
        listeners.map(function (listener) {
          listener.apply(void 0, data);
        });
      }
    }
  }, {
    key: "addEventListener",
    value: function addEventListener(eventType, callback) {
      var _this = this;
      if (!_this._eventCallbacks[eventType]) {
        _this._eventCallbacks[eventType] = [callback];
      }
      _this._eventCallbacks[eventType].push(callback);
      return {
        remove: function remove() {
          var callbacks = _this._eventCallbacks[eventType];
          var filteredCallbacks = callbacks.filter(function (c) {
            return c.toString() !== callback.toString();
          });
          _this._eventCallbacks[eventType] = filteredCallbacks;
        }
      };
    }
  }, {
    key: "removeEventListener",
    value: function removeEventListener(eventType, callback) {
      console.error("Linking.removeEventListener('" + eventType + "', ...): Method has been " + 'deprecated. Please instead use `remove()` on the subscription ' + 'returned by `Linking.addEventListener`.');
      var callbacks = this._eventCallbacks[eventType];
      var filteredCallbacks = callbacks.filter(function (c) {
        return c.toString() !== callback.toString();
      });
      this._eventCallbacks[eventType] = filteredCallbacks;
    }
  }, {
    key: "canOpenURL",
    value: function canOpenURL() {
      return Promise.resolve(true);
    }
  }, {
    key: "getInitialURL",
    value: function getInitialURL() {
      return Promise.resolve(initialURL);
    }
  }, {
    key: "openURL",
    value: function openURL(url, target) {
      if (arguments.length === 1) {
        target = '_blank';
      }
      try {
        open(url, target);
        this._dispatchEvent('onOpen', url);
        return Promise.resolve();
      } catch (e) {
        return Promise.reject(e);
      }
    }
  }, {
    key: "_validateURL",
    value: function _validateURL(url) {
      (0, _invariant.default)(typeof url === 'string', 'Invalid URL: should be a string. Was: ' + url);
      (0, _invariant.default)(url, 'Invalid URL: cannot be empty');
    }
  }]);
}();
var open = function open(url, target) {
  if (_canUseDom.default) {
    var urlToOpen = new URL(url, window.location).toString();
    if (urlToOpen.indexOf('tel:') === 0) {
      window.location = urlToOpen;
    } else {
      window.open(urlToOpen, target, 'noopener');
    }
  }
};
var _default = exports.default = new Linking();
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0MiIsInJlcXVpcmUiLCJfY2xhc3NDYWxsQ2hlY2syIiwiX2NyZWF0ZUNsYXNzMiIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJkZWZhdWx0IiwiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJfaW52YXJpYW50IiwiX2NhblVzZURvbSIsImluaXRpYWxVUkwiLCJ3aW5kb3ciLCJsb2NhdGlvbiIsImhyZWYiLCJMaW5raW5nIiwiX2V2ZW50Q2FsbGJhY2tzIiwia2V5IiwidmFsdWUiLCJfZGlzcGF0Y2hFdmVudCIsImV2ZW50IiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImRhdGEiLCJBcnJheSIsIl9rZXkiLCJsaXN0ZW5lcnMiLCJpc0FycmF5IiwibWFwIiwibGlzdGVuZXIiLCJhcHBseSIsImFkZEV2ZW50TGlzdGVuZXIiLCJldmVudFR5cGUiLCJjYWxsYmFjayIsIl90aGlzIiwicHVzaCIsInJlbW92ZSIsImNhbGxiYWNrcyIsImZpbHRlcmVkQ2FsbGJhY2tzIiwiZmlsdGVyIiwiYyIsInRvU3RyaW5nIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImNvbnNvbGUiLCJlcnJvciIsImNhbk9wZW5VUkwiLCJQcm9taXNlIiwicmVzb2x2ZSIsImdldEluaXRpYWxVUkwiLCJvcGVuVVJMIiwidXJsIiwidGFyZ2V0Iiwib3BlbiIsImUiLCJyZWplY3QiLCJfdmFsaWRhdGVVUkwiLCJ1cmxUb09wZW4iLCJVUkwiLCJpbmRleE9mIiwiX2RlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyJpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIikuZGVmYXVsdDtcbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG52YXIgX2ludmFyaWFudCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcImZianMvbGliL2ludmFyaWFudFwiKSk7XG52YXIgX2NhblVzZURvbSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uLy4uL21vZHVsZXMvY2FuVXNlRG9tXCIpKTtcbi8qKlxuICogQ29weXJpZ2h0IChjKSBOaWNvbGFzIEdhbGxhZ2hlci5cbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKi9cblxudmFyIGluaXRpYWxVUkwgPSBfY2FuVXNlRG9tLmRlZmF1bHQgPyB3aW5kb3cubG9jYXRpb24uaHJlZiA6ICcnO1xuY2xhc3MgTGlua2luZyB7XG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuX2V2ZW50Q2FsbGJhY2tzID0ge307XG4gIH1cbiAgLyoqXG4gICAqIEFuIG9iamVjdCBtYXBwaW5nIG9mIGV2ZW50IG5hbWVcbiAgICogYW5kIGFsbCB0aGUgY2FsbGJhY2tzIHN1YnNjcmliaW5nIHRvIGl0XG4gICAqL1xuICBfZGlzcGF0Y2hFdmVudChldmVudCkge1xuICAgIGZvciAodmFyIF9sZW4gPSBhcmd1bWVudHMubGVuZ3RoLCBkYXRhID0gbmV3IEFycmF5KF9sZW4gPiAxID8gX2xlbiAtIDEgOiAwKSwgX2tleSA9IDE7IF9rZXkgPCBfbGVuOyBfa2V5KyspIHtcbiAgICAgIGRhdGFbX2tleSAtIDFdID0gYXJndW1lbnRzW19rZXldO1xuICAgIH1cbiAgICB2YXIgbGlzdGVuZXJzID0gdGhpcy5fZXZlbnRDYWxsYmFja3NbZXZlbnRdO1xuICAgIGlmIChsaXN0ZW5lcnMgIT0gbnVsbCAmJiBBcnJheS5pc0FycmF5KGxpc3RlbmVycykpIHtcbiAgICAgIGxpc3RlbmVycy5tYXAobGlzdGVuZXIgPT4ge1xuICAgICAgICBsaXN0ZW5lciguLi5kYXRhKTtcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBBZGRzIGEgZXZlbnQgbGlzdGVuZXIgZm9yIHRoZSBzcGVjaWZpZWQgZXZlbnQuIFRoZSBjYWxsYmFjayB3aWxsIGJlIGNhbGxlZCB3aGVuIHRoZVxuICAgKiBzYWlkIGV2ZW50IGlzIGRpc3BhdGNoZWQuXG4gICAqL1xuICBhZGRFdmVudExpc3RlbmVyKGV2ZW50VHlwZSwgY2FsbGJhY2spIHtcbiAgICB2YXIgX3RoaXMgPSB0aGlzO1xuICAgIGlmICghX3RoaXMuX2V2ZW50Q2FsbGJhY2tzW2V2ZW50VHlwZV0pIHtcbiAgICAgIF90aGlzLl9ldmVudENhbGxiYWNrc1tldmVudFR5cGVdID0gW2NhbGxiYWNrXTtcbiAgICB9XG4gICAgX3RoaXMuX2V2ZW50Q2FsbGJhY2tzW2V2ZW50VHlwZV0ucHVzaChjYWxsYmFjayk7XG4gICAgcmV0dXJuIHtcbiAgICAgIHJlbW92ZSgpIHtcbiAgICAgICAgdmFyIGNhbGxiYWNrcyA9IF90aGlzLl9ldmVudENhbGxiYWNrc1tldmVudFR5cGVdO1xuICAgICAgICB2YXIgZmlsdGVyZWRDYWxsYmFja3MgPSBjYWxsYmFja3MuZmlsdGVyKGMgPT4gYy50b1N0cmluZygpICE9PSBjYWxsYmFjay50b1N0cmluZygpKTtcbiAgICAgICAgX3RoaXMuX2V2ZW50Q2FsbGJhY2tzW2V2ZW50VHlwZV0gPSBmaWx0ZXJlZENhbGxiYWNrcztcbiAgICAgIH1cbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFJlbW92ZXMgYSBwcmV2aW91c2x5IGFkZGVkIGV2ZW50IGxpc3RlbmVyIGZvciB0aGUgc3BlY2lmaWVkIGV2ZW50LiBUaGUgY2FsbGJhY2sgbXVzdFxuICAgKiBiZSB0aGUgc2FtZSBvYmplY3QgYXMgdGhlIG9uZSBwYXNzZWQgdG8gYGFkZEV2ZW50TGlzdGVuZXJgLlxuICAgKi9cbiAgcmVtb3ZlRXZlbnRMaXN0ZW5lcihldmVudFR5cGUsIGNhbGxiYWNrKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkxpbmtpbmcucmVtb3ZlRXZlbnRMaXN0ZW5lcignXCIgKyBldmVudFR5cGUgKyBcIicsIC4uLik6IE1ldGhvZCBoYXMgYmVlbiBcIiArICdkZXByZWNhdGVkLiBQbGVhc2UgaW5zdGVhZCB1c2UgYHJlbW92ZSgpYCBvbiB0aGUgc3Vic2NyaXB0aW9uICcgKyAncmV0dXJuZWQgYnkgYExpbmtpbmcuYWRkRXZlbnRMaXN0ZW5lcmAuJyk7XG4gICAgdmFyIGNhbGxiYWNrcyA9IHRoaXMuX2V2ZW50Q2FsbGJhY2tzW2V2ZW50VHlwZV07XG4gICAgdmFyIGZpbHRlcmVkQ2FsbGJhY2tzID0gY2FsbGJhY2tzLmZpbHRlcihjID0+IGMudG9TdHJpbmcoKSAhPT0gY2FsbGJhY2sudG9TdHJpbmcoKSk7XG4gICAgdGhpcy5fZXZlbnRDYWxsYmFja3NbZXZlbnRUeXBlXSA9IGZpbHRlcmVkQ2FsbGJhY2tzO1xuICB9XG4gIGNhbk9wZW5VUkwoKSB7XG4gICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSh0cnVlKTtcbiAgfVxuICBnZXRJbml0aWFsVVJMKCkge1xuICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoaW5pdGlhbFVSTCk7XG4gIH1cblxuICAvKipcbiAgICogVHJ5IHRvIG9wZW4gdGhlIGdpdmVuIHVybCBpbiBhIHNlY3VyZSBmYXNoaW9uLiBUaGUgbWV0aG9kIHJldHVybnMgYSBQcm9taXNlIG9iamVjdC5cbiAgICogSWYgYSB0YXJnZXQgaXMgcGFzc2VkIChpbmNsdWRpbmcgdW5kZWZpbmVkKSB0aGF0IHRhcmdldCB3aWxsIGJlIHVzZWQsIG90aGVyd2lzZSAnX2JsYW5rJy5cbiAgICogSWYgdGhlIHVybCBvcGVucywgdGhlIHByb21pc2UgaXMgcmVzb2x2ZWQuIElmIG5vdCwgdGhlIHByb21pc2UgaXMgcmVqZWN0ZWQuXG4gICAqIERpc3BhdGNoZXMgdGhlIGBvbk9wZW5gIGV2ZW50IGlmIGB1cmxgIGlzIG9wZW5lZCBzdWNjZXNzZnVsbHkuXG4gICAqL1xuICBvcGVuVVJMKHVybCwgdGFyZ2V0KSB7XG4gICAgaWYgKGFyZ3VtZW50cy5sZW5ndGggPT09IDEpIHtcbiAgICAgIHRhcmdldCA9ICdfYmxhbmsnO1xuICAgIH1cbiAgICB0cnkge1xuICAgICAgb3Blbih1cmwsIHRhcmdldCk7XG4gICAgICB0aGlzLl9kaXNwYXRjaEV2ZW50KCdvbk9wZW4nLCB1cmwpO1xuICAgICAgcmV0dXJuIFByb21pc2UucmVzb2x2ZSgpO1xuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgIHJldHVybiBQcm9taXNlLnJlamVjdChlKTtcbiAgICB9XG4gIH1cbiAgX3ZhbGlkYXRlVVJMKHVybCkge1xuICAgICgwLCBfaW52YXJpYW50LmRlZmF1bHQpKHR5cGVvZiB1cmwgPT09ICdzdHJpbmcnLCAnSW52YWxpZCBVUkw6IHNob3VsZCBiZSBhIHN0cmluZy4gV2FzOiAnICsgdXJsKTtcbiAgICAoMCwgX2ludmFyaWFudC5kZWZhdWx0KSh1cmwsICdJbnZhbGlkIFVSTDogY2Fubm90IGJlIGVtcHR5Jyk7XG4gIH1cbn1cbnZhciBvcGVuID0gKHVybCwgdGFyZ2V0KSA9PiB7XG4gIGlmIChfY2FuVXNlRG9tLmRlZmF1bHQpIHtcbiAgICB2YXIgdXJsVG9PcGVuID0gbmV3IFVSTCh1cmwsIHdpbmRvdy5sb2NhdGlvbikudG9TdHJpbmcoKTtcbiAgICBpZiAodXJsVG9PcGVuLmluZGV4T2YoJ3RlbDonKSA9PT0gMCkge1xuICAgICAgd2luZG93LmxvY2F0aW9uID0gdXJsVG9PcGVuO1xuICAgIH0gZWxzZSB7XG4gICAgICB3aW5kb3cub3Blbih1cmxUb09wZW4sIHRhcmdldCwgJ25vb3BlbmVyJyk7XG4gICAgfVxuICB9XG59O1xudmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gbmV3IExpbmtpbmcoKTtcbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFBQyxJQUFBQSx1QkFBQSxHQUFBQyxPQUFBO0FBQUEsSUFBQUMsZ0JBQUEsR0FBQUYsdUJBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFFLGFBQUEsR0FBQUgsdUJBQUEsQ0FBQUMsT0FBQTtBQUViLElBQUlHLHNCQUFzQixHQUFHSCxPQUFPLENBQUMsOENBQThDLENBQUMsQ0FBQ0ksT0FBTztBQUM1RkMsT0FBTyxDQUFDQyxVQUFVLEdBQUcsSUFBSTtBQUN6QkQsT0FBTyxDQUFDRCxPQUFPLEdBQUcsS0FBSyxDQUFDO0FBQ3hCLElBQUlHLFVBQVUsR0FBR0osc0JBQXNCLENBQUNILE9BQU8sQ0FBQyxvQkFBb0IsQ0FBQyxDQUFDO0FBQ3RFLElBQUlRLFVBQVUsR0FBR0wsc0JBQXNCLENBQUNILE9BQU8sMEJBQTBCLENBQUMsQ0FBQztBQVczRSxJQUFJUyxVQUFVLEdBQUdELFVBQVUsQ0FBQ0osT0FBTyxHQUFHTSxNQUFNLENBQUNDLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHLEVBQUU7QUFBQyxJQUMxREMsT0FBTztFQUNYLFNBQUFBLFFBQUEsRUFBYztJQUFBLElBQUFaLGdCQUFBLENBQUFHLE9BQUEsUUFBQVMsT0FBQTtJQUNaLElBQUksQ0FBQ0MsZUFBZSxHQUFHLENBQUMsQ0FBQztFQUMzQjtFQUFDLFdBQUFaLGFBQUEsQ0FBQUUsT0FBQSxFQUFBUyxPQUFBO0lBQUFFLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQUFDLGNBQWNBLENBQUNDLEtBQUssRUFBRTtNQUNwQixLQUFLLElBQUlDLElBQUksR0FBR0MsU0FBUyxDQUFDQyxNQUFNLEVBQUVDLElBQUksR0FBRyxJQUFJQyxLQUFLLENBQUNKLElBQUksR0FBRyxDQUFDLEdBQUdBLElBQUksR0FBRyxDQUFDLEdBQUcsQ0FBQyxDQUFDLEVBQUVLLElBQUksR0FBRyxDQUFDLEVBQUVBLElBQUksR0FBR0wsSUFBSSxFQUFFSyxJQUFJLEVBQUUsRUFBRTtRQUMxR0YsSUFBSSxDQUFDRSxJQUFJLEdBQUcsQ0FBQyxDQUFDLEdBQUdKLFNBQVMsQ0FBQ0ksSUFBSSxDQUFDO01BQ2xDO01BQ0EsSUFBSUMsU0FBUyxHQUFHLElBQUksQ0FBQ1gsZUFBZSxDQUFDSSxLQUFLLENBQUM7TUFDM0MsSUFBSU8sU0FBUyxJQUFJLElBQUksSUFBSUYsS0FBSyxDQUFDRyxPQUFPLENBQUNELFNBQVMsQ0FBQyxFQUFFO1FBQ2pEQSxTQUFTLENBQUNFLEdBQUcsQ0FBQyxVQUFBQyxRQUFRLEVBQUk7VUFDeEJBLFFBQVEsQ0FBQUMsS0FBQSxTQUFJUCxJQUFJLENBQUM7UUFDbkIsQ0FBQyxDQUFDO01BQ0o7SUFDRjtFQUFDO0lBQUFQLEdBQUE7SUFBQUMsS0FBQSxFQU1ELFNBQUFjLGdCQUFnQkEsQ0FBQ0MsU0FBUyxFQUFFQyxRQUFRLEVBQUU7TUFDcEMsSUFBSUMsS0FBSyxHQUFHLElBQUk7TUFDaEIsSUFBSSxDQUFDQSxLQUFLLENBQUNuQixlQUFlLENBQUNpQixTQUFTLENBQUMsRUFBRTtRQUNyQ0UsS0FBSyxDQUFDbkIsZUFBZSxDQUFDaUIsU0FBUyxDQUFDLEdBQUcsQ0FBQ0MsUUFBUSxDQUFDO01BQy9DO01BQ0FDLEtBQUssQ0FBQ25CLGVBQWUsQ0FBQ2lCLFNBQVMsQ0FBQyxDQUFDRyxJQUFJLENBQUNGLFFBQVEsQ0FBQztNQUMvQyxPQUFPO1FBQ0xHLE1BQU0sV0FBTkEsTUFBTUEsQ0FBQSxFQUFHO1VBQ1AsSUFBSUMsU0FBUyxHQUFHSCxLQUFLLENBQUNuQixlQUFlLENBQUNpQixTQUFTLENBQUM7VUFDaEQsSUFBSU0saUJBQWlCLEdBQUdELFNBQVMsQ0FBQ0UsTUFBTSxDQUFDLFVBQUFDLENBQUM7WUFBQSxPQUFJQSxDQUFDLENBQUNDLFFBQVEsQ0FBQyxDQUFDLEtBQUtSLFFBQVEsQ0FBQ1EsUUFBUSxDQUFDLENBQUM7VUFBQSxFQUFDO1VBQ25GUCxLQUFLLENBQUNuQixlQUFlLENBQUNpQixTQUFTLENBQUMsR0FBR00saUJBQWlCO1FBQ3REO01BQ0YsQ0FBQztJQUNIO0VBQUM7SUFBQXRCLEdBQUE7SUFBQUMsS0FBQSxFQU1ELFNBQUF5QixtQkFBbUJBLENBQUNWLFNBQVMsRUFBRUMsUUFBUSxFQUFFO01BQ3ZDVSxPQUFPLENBQUNDLEtBQUssQ0FBQywrQkFBK0IsR0FBR1osU0FBUyxHQUFHLDJCQUEyQixHQUFHLGdFQUFnRSxHQUFHLHlDQUF5QyxDQUFDO01BQ3ZNLElBQUlLLFNBQVMsR0FBRyxJQUFJLENBQUN0QixlQUFlLENBQUNpQixTQUFTLENBQUM7TUFDL0MsSUFBSU0saUJBQWlCLEdBQUdELFNBQVMsQ0FBQ0UsTUFBTSxDQUFDLFVBQUFDLENBQUM7UUFBQSxPQUFJQSxDQUFDLENBQUNDLFFBQVEsQ0FBQyxDQUFDLEtBQUtSLFFBQVEsQ0FBQ1EsUUFBUSxDQUFDLENBQUM7TUFBQSxFQUFDO01BQ25GLElBQUksQ0FBQzFCLGVBQWUsQ0FBQ2lCLFNBQVMsQ0FBQyxHQUFHTSxpQkFBaUI7SUFDckQ7RUFBQztJQUFBdEIsR0FBQTtJQUFBQyxLQUFBLEVBQ0QsU0FBQTRCLFVBQVVBLENBQUEsRUFBRztNQUNYLE9BQU9DLE9BQU8sQ0FBQ0MsT0FBTyxDQUFDLElBQUksQ0FBQztJQUM5QjtFQUFDO0lBQUEvQixHQUFBO0lBQUFDLEtBQUEsRUFDRCxTQUFBK0IsYUFBYUEsQ0FBQSxFQUFHO01BQ2QsT0FBT0YsT0FBTyxDQUFDQyxPQUFPLENBQUNyQyxVQUFVLENBQUM7SUFDcEM7RUFBQztJQUFBTSxHQUFBO0lBQUFDLEtBQUEsRUFRRCxTQUFBZ0MsT0FBT0EsQ0FBQ0MsR0FBRyxFQUFFQyxNQUFNLEVBQUU7TUFDbkIsSUFBSTlCLFNBQVMsQ0FBQ0MsTUFBTSxLQUFLLENBQUMsRUFBRTtRQUMxQjZCLE1BQU0sR0FBRyxRQUFRO01BQ25CO01BQ0EsSUFBSTtRQUNGQyxJQUFJLENBQUNGLEdBQUcsRUFBRUMsTUFBTSxDQUFDO1FBQ2pCLElBQUksQ0FBQ2pDLGNBQWMsQ0FBQyxRQUFRLEVBQUVnQyxHQUFHLENBQUM7UUFDbEMsT0FBT0osT0FBTyxDQUFDQyxPQUFPLENBQUMsQ0FBQztNQUMxQixDQUFDLENBQUMsT0FBT00sQ0FBQyxFQUFFO1FBQ1YsT0FBT1AsT0FBTyxDQUFDUSxNQUFNLENBQUNELENBQUMsQ0FBQztNQUMxQjtJQUNGO0VBQUM7SUFBQXJDLEdBQUE7SUFBQUMsS0FBQSxFQUNELFNBQUFzQyxZQUFZQSxDQUFDTCxHQUFHLEVBQUU7TUFDaEIsQ0FBQyxDQUFDLEVBQUUxQyxVQUFVLENBQUNILE9BQU8sRUFBRSxPQUFPNkMsR0FBRyxLQUFLLFFBQVEsRUFBRSx3Q0FBd0MsR0FBR0EsR0FBRyxDQUFDO01BQ2hHLENBQUMsQ0FBQyxFQUFFMUMsVUFBVSxDQUFDSCxPQUFPLEVBQUU2QyxHQUFHLEVBQUUsOEJBQThCLENBQUM7SUFDOUQ7RUFBQztBQUFBO0FBRUgsSUFBSUUsSUFBSSxHQUFHLFNBQVBBLElBQUlBLENBQUlGLEdBQUcsRUFBRUMsTUFBTSxFQUFLO0VBQzFCLElBQUkxQyxVQUFVLENBQUNKLE9BQU8sRUFBRTtJQUN0QixJQUFJbUQsU0FBUyxHQUFHLElBQUlDLEdBQUcsQ0FBQ1AsR0FBRyxFQUFFdkMsTUFBTSxDQUFDQyxRQUFRLENBQUMsQ0FBQzZCLFFBQVEsQ0FBQyxDQUFDO0lBQ3hELElBQUllLFNBQVMsQ0FBQ0UsT0FBTyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsRUFBRTtNQUNuQy9DLE1BQU0sQ0FBQ0MsUUFBUSxHQUFHNEMsU0FBUztJQUM3QixDQUFDLE1BQU07TUFDTDdDLE1BQU0sQ0FBQ3lDLElBQUksQ0FBQ0ksU0FBUyxFQUFFTCxNQUFNLEVBQUUsVUFBVSxDQUFDO0lBQzVDO0VBQ0Y7QUFDRixDQUFDO0FBQ0QsSUFBSVEsUUFBUSxHQUFHckQsT0FBTyxDQUFDRCxPQUFPLEdBQUcsSUFBSVMsT0FBTyxDQUFDLENBQUM7QUFDOUM4QyxNQUFNLENBQUN0RCxPQUFPLEdBQUdBLE9BQU8sQ0FBQ0QsT0FBTyIsImlnbm9yZUxpc3QiOltdfQ==