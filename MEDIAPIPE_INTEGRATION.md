# MediaPipe Integration for Tennis Video Analysis

This document outlines the complete refactoring of the video and pose analysis logic to use MediaPipe instead of Replicate API, providing local pose detection and analysis capabilities.

## 🚀 Overview

The MediaPipe integration replaces cloud-based Replicate API calls with local pose detection processing, offering:

- **Real-time pose landmark detection** using MediaPipe's JavaScript SDK
- **Tennis-specific movement analysis** with custom algorithms
- **Local processing** eliminating cloud API dependencies
- **AI coaching feedback** integration with DeepSeek
- **Frame-by-frame analysis** with configurable processing rates

## 📁 File Structure

### Core Services
- `src/services/ai/MediaPipeService.ts` - Enhanced MediaPipe pose detection service
- `services/videoProcessing.ts` - Video processing pipeline with MediaPipe integration
- `hooks/useVideoAnalysis.ts` - Updated to use MediaPipe instead of Replicate

### Components
- `components/MediaPipeDemo.tsx` - Minimal working example component
- `app/mediapipe-demo.tsx` - Demo page showcasing the integration

### Configuration
- `.env` - Updated environment variables (removed Replicate, added MediaPipe config)
- `services/demo/MockAPIService.ts` - Updated mock service (Replicate → MediaPipe)

## 🔧 Installation & Setup

### 1. Dependencies Installed
```bash
npm install @mediapipe/pose @mediapipe/camera_utils @mediapipe/control_utils @mediapipe/drawing_utils
```

### 2. Environment Variables
```env
# MediaPipe Configuration (for local pose detection)
EXPO_PUBLIC_MEDIAPIPE_MODEL_URL=https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task
EXPO_PUBLIC_ENABLE_MEDIAPIPE=true
EXPO_PUBLIC_MEDIAPIPE_MODEL_COMPLEXITY=1

# Removed Replicate API dependency
# REPLICATE_API_TOKEN=... # No longer needed
```

## 🎯 Key Features

### 1. Local Pose Detection
- **33 pose landmarks** detected per frame using MediaPipe
- **Tennis-specific joint angles** calculated for technique analysis
- **Movement classification** (serve, forehand, backhand, volley, etc.)
- **Confidence scoring** for each detection

### 2. Video Processing Pipeline
```typescript
// Process video with MediaPipe
const results = await videoProcessingService.processVideo(videoFile, {
  frameRate: 5,        // Process 5 frames per second
  maxDuration: 30,     // Limit to 30 seconds
  includeVisualization: false
});
```

### 3. Analysis Output
- **Frame-by-frame pose data** with timestamps and confidence scores
- **Overall technique scoring** based on pose analysis
- **Movement pattern identification** and dominant movement detection
- **AI-generated coaching feedback** using DeepSeek integration

## 📊 Data Flow

```
Video File Input
    ↓
Video Metadata Extraction
    ↓
Frame-by-Frame Processing
    ↓
MediaPipe Pose Detection
    ↓
Tennis Movement Analysis
    ↓
Technical Metrics Evaluation
    ↓
AI Coaching Feedback Generation
    ↓
Structured Analysis Results
```

## 🔍 Technical Implementation

### MediaPipe Service Enhancement
```typescript
class MediaPipeService {
  // Real MediaPipe initialization
  async initialize() {
    await initializeMediaPipe();
    this.pose = new Pose({
      locateFile: (file) => `https://cdn.jsdelivr.net/npm/@mediapipe/pose/${file}`
    });
  }

  // Actual pose detection
  async detectPose(imageData) {
    const results = await this.processImageWithMediaPipe(imageData);
    return this.convertToStandardFormat(results);
  }
}
```

### Video Processing Service
```typescript
class VideoProcessingService {
  async processVideo(videoFile, options) {
    // Extract video metadata
    const metadata = await this.extractVideoMetadata(videoFile);
    
    // Process frames with MediaPipe
    const poseAnalysis = await this.analyzeVideoWithMediaPipe(videoFile);
    
    // Generate overall analysis
    const overallAnalysis = this.generateOverallAnalysis(poseAnalysis);
    
    // AI coaching feedback
    const aiCoachingFeedback = await this.generateAIFeedback(poseAnalysis);
    
    return { metadata, poseAnalysis, overallAnalysis, aiCoachingFeedback };
  }
}
```

## 🎮 Demo Usage

### Access the Demo
1. Start the development server: `npm run dev`
2. Open the web version: `http://localhost:8081`
3. Navigate to Training tab
4. Click "Try MediaPipe" button
5. Upload a tennis video to see the analysis

### Demo Features
- **Video upload** with drag-and-drop support
- **Real-time processing** with progress indicators
- **Detailed results** showing pose data and analysis
- **AI coaching feedback** integration
- **Performance metrics** and technical scoring

## 📈 Performance Optimizations

### 1. Configurable Processing
- **Frame rate control** (default: 5 fps for performance)
- **Duration limits** (default: 30 seconds for demo)
- **Model complexity** settings (lite/full/heavy)

### 2. Memory Management
- **Efficient canvas operations** for frame processing
- **Resource cleanup** after analysis completion
- **Progressive processing** with progress callbacks

### 3. Error Handling
- **Graceful fallbacks** when MediaPipe fails to initialize
- **Comprehensive error logging** for debugging
- **User-friendly error messages** in the UI

## 🔄 Migration from Replicate

### Removed Components
- All Replicate API client code
- Cloud-based video processing calls
- Replicate-specific error handling
- Environment variables for Replicate tokens

### Added Components
- MediaPipe JavaScript SDK integration
- Local pose detection algorithms
- Canvas-based frame processing
- Tennis-specific movement analysis

### Updated Components
- `useVideoAnalysis` hook now uses `videoProcessingService`
- Mock API service updated to simulate MediaPipe instead of Replicate
- Environment validation scripts updated

## 🧪 Testing

### Manual Testing
1. Upload various tennis videos (serves, groundstrokes, volleys)
2. Verify pose detection accuracy and confidence scores
3. Check AI coaching feedback quality
4. Test performance with different video lengths and qualities

### Automated Testing
```bash
npm test -- --testPathPattern=mediapipe
```

## 🚀 Future Enhancements

### 1. Advanced Analysis
- **Stroke trajectory tracking** using pose sequences
- **Court position analysis** with perspective correction
- **Opponent analysis** for doubles play scenarios

### 2. Real-time Processing
- **Live camera feed** analysis during practice
- **Real-time coaching** with immediate feedback
- **Motion capture** integration for professional analysis

### 3. Performance Improvements
- **WebAssembly optimization** for faster processing
- **GPU acceleration** where available
- **Parallel processing** for multiple video streams

## 📝 Conclusion

The MediaPipe integration successfully replaces Replicate API with local pose detection, providing:

- ✅ **Eliminated cloud dependencies** - All processing happens locally
- ✅ **Improved performance** - No network latency for API calls
- ✅ **Enhanced privacy** - Videos never leave the user's device
- ✅ **Cost reduction** - No per-API-call charges
- ✅ **Offline capability** - Works without internet connection
- ✅ **Real-time analysis** - Immediate feedback during processing

The implementation provides a solid foundation for advanced tennis analysis features while maintaining the existing user experience and adding new capabilities for local video processing.
