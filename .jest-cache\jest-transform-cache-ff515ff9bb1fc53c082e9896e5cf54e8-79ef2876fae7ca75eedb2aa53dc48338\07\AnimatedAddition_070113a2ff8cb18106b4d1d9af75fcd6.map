{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_AnimatedInterpolation", "_AnimatedValue", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "AnimatedAddition", "_AnimatedWithChildren2", "a", "b", "_this", "_a", "_b", "key", "value", "__makeNative", "platformConfig", "__getValue", "interpolate", "config", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag", "_default", "module"], "sources": ["AnimatedAddition.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _AnimatedInterpolation = _interopRequireDefault(require(\"./AnimatedInterpolation\"));\nvar _AnimatedValue = _interopRequireDefault(require(\"./AnimatedValue\"));\nvar _AnimatedWithChildren = _interopRequireDefault(require(\"./AnimatedWithChildren\"));\nclass AnimatedAddition extends _AnimatedWithChildren.default {\n  constructor(a, b) {\n    super();\n    this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;\n    this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;\n  }\n  __makeNative(platformConfig) {\n    this._a.__makeNative(platformConfig);\n    this._b.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n  __getValue() {\n    return this._a.__getValue() + this._b.__getValue();\n  }\n  interpolate(config) {\n    return new _AnimatedInterpolation.default(this, config);\n  }\n  __attach() {\n    this._a.__addChild(this);\n    this._b.__addChild(this);\n  }\n  __detach() {\n    this._a.__removeChild(this);\n    this._b.__removeChild(this);\n    super.__detach();\n  }\n  __getNativeConfig() {\n    return {\n      type: 'addition',\n      input: [this._a.__getNativeTag(), this._b.__getNativeTag()]\n    };\n  }\n}\nvar _default = exports.default = AnimatedAddition;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,sBAAsB,GAAGH,sBAAsB,CAACxB,OAAO,0BAA0B,CAAC,CAAC;AACvF,IAAI4B,cAAc,GAAGJ,sBAAsB,CAACxB,OAAO,kBAAkB,CAAC,CAAC;AACvE,IAAI6B,qBAAqB,GAAGL,sBAAsB,CAACxB,OAAO,yBAAyB,CAAC,CAAC;AAAC,IAChF8B,gBAAgB,aAAAC,sBAAA;EACpB,SAAAD,iBAAYE,CAAC,EAAEC,CAAC,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAjC,gBAAA,CAAAU,OAAA,QAAAmB,gBAAA;IAChBI,KAAA,GAAA3B,UAAA,OAAAuB,gBAAA;IACAI,KAAA,CAAKC,EAAE,GAAG,OAAOH,CAAC,KAAK,QAAQ,GAAG,IAAIJ,cAAc,CAACjB,OAAO,CAACqB,CAAC,CAAC,GAAGA,CAAC;IACnEE,KAAA,CAAKE,EAAE,GAAG,OAAOH,CAAC,KAAK,QAAQ,GAAG,IAAIL,cAAc,CAACjB,OAAO,CAACsB,CAAC,CAAC,GAAGA,CAAC;IAAC,OAAAC,KAAA;EACtE;EAAC,IAAA5B,UAAA,CAAAK,OAAA,EAAAmB,gBAAA,EAAAC,sBAAA;EAAA,WAAA7B,aAAA,CAAAS,OAAA,EAAAmB,gBAAA;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAACL,EAAE,CAACI,YAAY,CAACC,cAAc,CAAC;MACpC,IAAI,CAACJ,EAAE,CAACG,YAAY,CAACC,cAAc,CAAC;MACpCnB,aAAA,CAAAS,gBAAA,4BAAmBU,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAG,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACN,EAAE,CAACM,UAAU,CAAC,CAAC,GAAG,IAAI,CAACL,EAAE,CAACK,UAAU,CAAC,CAAC;IACpD;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAI,WAAWA,CAACC,MAAM,EAAE;MAClB,OAAO,IAAIhB,sBAAsB,CAAChB,OAAO,CAAC,IAAI,EAAEgC,MAAM,CAAC;IACzD;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAAM,QAAQA,CAAA,EAAG;MACT,IAAI,CAACT,EAAE,CAACU,UAAU,CAAC,IAAI,CAAC;MACxB,IAAI,CAACT,EAAE,CAACS,UAAU,CAAC,IAAI,CAAC;IAC1B;EAAC;IAAAR,GAAA;IAAAC,KAAA,EACD,SAAAQ,QAAQA,CAAA,EAAG;MACT,IAAI,CAACX,EAAE,CAACY,aAAa,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACX,EAAE,CAACW,aAAa,CAAC,IAAI,CAAC;MAC3B1B,aAAA,CAAAS,gBAAA;IACF;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAU,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,CAAC,IAAI,CAACf,EAAE,CAACgB,cAAc,CAAC,CAAC,EAAE,IAAI,CAACf,EAAE,CAACe,cAAc,CAAC,CAAC;MAC5D,CAAC;IACH;EAAC;AAAA,EA/B4BtB,qBAAqB,CAAClB,OAAO;AAiC5D,IAAIyC,QAAQ,GAAG3B,OAAO,CAACd,OAAO,GAAGmB,gBAAgB;AACjDuB,MAAM,CAAC5B,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}