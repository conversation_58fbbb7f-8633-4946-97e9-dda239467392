57d732b698d56463d02e3551d0625bb2
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = useHover;
var _modality = require("../modality");
var _useEvent = _interopRequireDefault(require("../useEvent"));
var _useLayoutEffect = _interopRequireDefault(require("../useLayoutEffect"));
var emptyObject = {};
var opts = {
  passive: true
};
var lockEventType = 'react-gui:hover:lock';
var unlockEventType = 'react-gui:hover:unlock';
var supportsPointerEvent = function supportsPointerEvent() {
  return !!(typeof window !== 'undefined' && window.PointerEvent != null);
};
function dispatchCustomEvent(target, type, payload) {
  var event = document.createEvent('CustomEvent');
  var _ref = payload || emptyObject,
    _ref$bubbles = _ref.bubbles,
    bubbles = _ref$bubbles === void 0 ? true : _ref$bubbles,
    _ref$cancelable = _ref.cancelable,
    cancelable = _ref$cancelable === void 0 ? true : _ref$cancelable,
    detail = _ref.detail;
  event.initCustomEvent(type, bubbles, cancelable, detail);
  target.dispatchEvent(event);
}
function getPointerType(event) {
  var pointerType = event.pointerType;
  return pointerType != null ? pointerType : (0, _modality.getModality)();
}
function useHover(targetRef, config) {
  var contain = config.contain,
    disabled = config.disabled,
    onHoverStart = config.onHoverStart,
    onHoverChange = config.onHoverChange,
    onHoverUpdate = config.onHoverUpdate,
    onHoverEnd = config.onHoverEnd;
  var canUsePE = supportsPointerEvent();
  var addMoveListener = (0, _useEvent.default)(canUsePE ? 'pointermove' : 'mousemove', opts);
  var addEnterListener = (0, _useEvent.default)(canUsePE ? 'pointerenter' : 'mouseenter', opts);
  var addLeaveListener = (0, _useEvent.default)(canUsePE ? 'pointerleave' : 'mouseleave', opts);
  var addLockListener = (0, _useEvent.default)(lockEventType, opts);
  var addUnlockListener = (0, _useEvent.default)(unlockEventType, opts);
  (0, _useLayoutEffect.default)(function () {
    var target = targetRef.current;
    if (target !== null) {
      var hoverEnd = function hoverEnd(e) {
        if (onHoverEnd != null) {
          onHoverEnd(e);
        }
        if (onHoverChange != null) {
          onHoverChange(false);
        }
        addMoveListener(target, null);
        addLeaveListener(target, null);
      };
      var leaveListener = function leaveListener(e) {
        var target = targetRef.current;
        if (target != null && getPointerType(e) !== 'touch') {
          if (contain) {
            dispatchCustomEvent(target, unlockEventType);
          }
          hoverEnd(e);
        }
      };
      var moveListener = function moveListener(e) {
        if (getPointerType(e) !== 'touch') {
          if (onHoverUpdate != null) {
            if (e.x == null) {
              e.x = e.clientX;
            }
            if (e.y == null) {
              e.y = e.clientY;
            }
            onHoverUpdate(e);
          }
        }
      };
      var hoverStart = function hoverStart(e) {
        if (onHoverStart != null) {
          onHoverStart(e);
        }
        if (onHoverChange != null) {
          onHoverChange(true);
        }
        if (onHoverUpdate != null) {
          addMoveListener(target, !disabled ? moveListener : null);
        }
        addLeaveListener(target, !disabled ? leaveListener : null);
      };
      var enterListener = function enterListener(e) {
        var target = targetRef.current;
        if (target != null && getPointerType(e) !== 'touch') {
          if (contain) {
            dispatchCustomEvent(target, lockEventType);
          }
          hoverStart(e);
          var lockListener = function lockListener(lockEvent) {
            if (lockEvent.target !== target) {
              hoverEnd(e);
            }
          };
          var unlockListener = function unlockListener(lockEvent) {
            if (lockEvent.target !== target) {
              hoverStart(e);
            }
          };
          addLockListener(target, !disabled ? lockListener : null);
          addUnlockListener(target, !disabled ? unlockListener : null);
        }
      };
      addEnterListener(target, !disabled ? enterListener : null);
    }
  }, [addEnterListener, addMoveListener, addLeaveListener, addLockListener, addUnlockListener, contain, disabled, onHoverStart, onHoverChange, onHoverUpdate, onHoverEnd, targetRef]);
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJleHBvcnRzIiwiX19lc01vZHVsZSIsInVzZUhvdmVyIiwiX21vZGFsaXR5IiwiX3VzZUV2ZW50IiwiX3VzZUxheW91dEVmZmVjdCIsImVtcHR5T2JqZWN0Iiwib3B0cyIsInBhc3NpdmUiLCJsb2NrRXZlbnRUeXBlIiwidW5sb2NrRXZlbnRUeXBlIiwic3VwcG9ydHNQb2ludGVyRXZlbnQiLCJ3aW5kb3ciLCJQb2ludGVyRXZlbnQiLCJkaXNwYXRjaEN1c3RvbUV2ZW50IiwidGFyZ2V0IiwidHlwZSIsInBheWxvYWQiLCJldmVudCIsImRvY3VtZW50IiwiY3JlYXRlRXZlbnQiLCJfcmVmIiwiX3JlZiRidWJibGVzIiwiYnViYmxlcyIsIl9yZWYkY2FuY2VsYWJsZSIsImNhbmNlbGFibGUiLCJkZXRhaWwiLCJpbml0Q3VzdG9tRXZlbnQiLCJkaXNwYXRjaEV2ZW50IiwiZ2V0UG9pbnRlclR5cGUiLCJwb2ludGVyVHlwZSIsImdldE1vZGFsaXR5IiwidGFyZ2V0UmVmIiwiY29uZmlnIiwiY29udGFpbiIsImRpc2FibGVkIiwib25Ib3ZlclN0YXJ0Iiwib25Ib3ZlckNoYW5nZSIsIm9uSG92ZXJVcGRhdGUiLCJvbkhvdmVyRW5kIiwiY2FuVXNlUEUiLCJhZGRNb3ZlTGlzdGVuZXIiLCJhZGRFbnRlckxpc3RlbmVyIiwiYWRkTGVhdmVMaXN0ZW5lciIsImFkZExvY2tMaXN0ZW5lciIsImFkZFVubG9ja0xpc3RlbmVyIiwiY3VycmVudCIsImhvdmVyRW5kIiwiZSIsImxlYXZlTGlzdGVuZXIiLCJtb3ZlTGlzdGVuZXIiLCJ4IiwiY2xpZW50WCIsInkiLCJjbGllbnRZIiwiaG92ZXJTdGFydCIsImVudGVyTGlzdGVuZXIiLCJsb2NrTGlzdGVuZXIiLCJsb2NrRXZlbnQiLCJ1bmxvY2tMaXN0ZW5lciIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbImluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgX2ludGVyb3BSZXF1aXJlRGVmYXVsdCA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdFwiKS5kZWZhdWx0O1xuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHVzZUhvdmVyO1xudmFyIF9tb2RhbGl0eSA9IHJlcXVpcmUoXCIuLi9tb2RhbGl0eVwiKTtcbnZhciBfdXNlRXZlbnQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi91c2VFdmVudFwiKSk7XG52YXIgX3VzZUxheW91dEVmZmVjdCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uL3VzZUxheW91dEVmZmVjdFwiKSk7XG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKi9cblxuLyoqXG4gKiBUeXBlc1xuICovXG5cbi8qKlxuICogSW1wbGVtZW50YXRpb25cbiAqL1xuXG52YXIgZW1wdHlPYmplY3QgPSB7fTtcbnZhciBvcHRzID0ge1xuICBwYXNzaXZlOiB0cnVlXG59O1xudmFyIGxvY2tFdmVudFR5cGUgPSAncmVhY3QtZ3VpOmhvdmVyOmxvY2snO1xudmFyIHVubG9ja0V2ZW50VHlwZSA9ICdyZWFjdC1ndWk6aG92ZXI6dW5sb2NrJztcbnZhciBzdXBwb3J0c1BvaW50ZXJFdmVudCA9ICgpID0+ICEhKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHdpbmRvdy5Qb2ludGVyRXZlbnQgIT0gbnVsbCk7XG5mdW5jdGlvbiBkaXNwYXRjaEN1c3RvbUV2ZW50KHRhcmdldCwgdHlwZSwgcGF5bG9hZCkge1xuICB2YXIgZXZlbnQgPSBkb2N1bWVudC5jcmVhdGVFdmVudCgnQ3VzdG9tRXZlbnQnKTtcbiAgdmFyIF9yZWYgPSBwYXlsb2FkIHx8IGVtcHR5T2JqZWN0LFxuICAgIF9yZWYkYnViYmxlcyA9IF9yZWYuYnViYmxlcyxcbiAgICBidWJibGVzID0gX3JlZiRidWJibGVzID09PSB2b2lkIDAgPyB0cnVlIDogX3JlZiRidWJibGVzLFxuICAgIF9yZWYkY2FuY2VsYWJsZSA9IF9yZWYuY2FuY2VsYWJsZSxcbiAgICBjYW5jZWxhYmxlID0gX3JlZiRjYW5jZWxhYmxlID09PSB2b2lkIDAgPyB0cnVlIDogX3JlZiRjYW5jZWxhYmxlLFxuICAgIGRldGFpbCA9IF9yZWYuZGV0YWlsO1xuICBldmVudC5pbml0Q3VzdG9tRXZlbnQodHlwZSwgYnViYmxlcywgY2FuY2VsYWJsZSwgZGV0YWlsKTtcbiAgdGFyZ2V0LmRpc3BhdGNoRXZlbnQoZXZlbnQpO1xufVxuXG4vLyBUaGlzIGFjY291bnRzIGZvciB0aGUgbm9uLVBvaW50ZXJFdmVudCBmYWxsYmFjayBldmVudHMuXG5mdW5jdGlvbiBnZXRQb2ludGVyVHlwZShldmVudCkge1xuICB2YXIgcG9pbnRlclR5cGUgPSBldmVudC5wb2ludGVyVHlwZTtcbiAgcmV0dXJuIHBvaW50ZXJUeXBlICE9IG51bGwgPyBwb2ludGVyVHlwZSA6ICgwLCBfbW9kYWxpdHkuZ2V0TW9kYWxpdHkpKCk7XG59XG5mdW5jdGlvbiB1c2VIb3Zlcih0YXJnZXRSZWYsIGNvbmZpZykge1xuICB2YXIgY29udGFpbiA9IGNvbmZpZy5jb250YWluLFxuICAgIGRpc2FibGVkID0gY29uZmlnLmRpc2FibGVkLFxuICAgIG9uSG92ZXJTdGFydCA9IGNvbmZpZy5vbkhvdmVyU3RhcnQsXG4gICAgb25Ib3ZlckNoYW5nZSA9IGNvbmZpZy5vbkhvdmVyQ2hhbmdlLFxuICAgIG9uSG92ZXJVcGRhdGUgPSBjb25maWcub25Ib3ZlclVwZGF0ZSxcbiAgICBvbkhvdmVyRW5kID0gY29uZmlnLm9uSG92ZXJFbmQ7XG4gIHZhciBjYW5Vc2VQRSA9IHN1cHBvcnRzUG9pbnRlckV2ZW50KCk7XG4gIHZhciBhZGRNb3ZlTGlzdGVuZXIgPSAoMCwgX3VzZUV2ZW50LmRlZmF1bHQpKGNhblVzZVBFID8gJ3BvaW50ZXJtb3ZlJyA6ICdtb3VzZW1vdmUnLCBvcHRzKTtcbiAgdmFyIGFkZEVudGVyTGlzdGVuZXIgPSAoMCwgX3VzZUV2ZW50LmRlZmF1bHQpKGNhblVzZVBFID8gJ3BvaW50ZXJlbnRlcicgOiAnbW91c2VlbnRlcicsIG9wdHMpO1xuICB2YXIgYWRkTGVhdmVMaXN0ZW5lciA9ICgwLCBfdXNlRXZlbnQuZGVmYXVsdCkoY2FuVXNlUEUgPyAncG9pbnRlcmxlYXZlJyA6ICdtb3VzZWxlYXZlJywgb3B0cyk7XG4gIC8vIFRoZXNlIGN1c3RvbSBldmVudHMgYXJlIHVzZWQgdG8gaW1wbGVtZW50IHRoZSBcImNvbnRhaW5cIiBwcm9wLlxuICB2YXIgYWRkTG9ja0xpc3RlbmVyID0gKDAsIF91c2VFdmVudC5kZWZhdWx0KShsb2NrRXZlbnRUeXBlLCBvcHRzKTtcbiAgdmFyIGFkZFVubG9ja0xpc3RlbmVyID0gKDAsIF91c2VFdmVudC5kZWZhdWx0KSh1bmxvY2tFdmVudFR5cGUsIG9wdHMpO1xuICAoMCwgX3VzZUxheW91dEVmZmVjdC5kZWZhdWx0KSgoKSA9PiB7XG4gICAgdmFyIHRhcmdldCA9IHRhcmdldFJlZi5jdXJyZW50O1xuICAgIGlmICh0YXJnZXQgIT09IG51bGwpIHtcbiAgICAgIC8qKlxuICAgICAgICogRW5kIHRoZSBob3ZlciBnZXN0dXJlXG4gICAgICAgKi9cbiAgICAgIHZhciBob3ZlckVuZCA9IGZ1bmN0aW9uIGhvdmVyRW5kKGUpIHtcbiAgICAgICAgaWYgKG9uSG92ZXJFbmQgIT0gbnVsbCkge1xuICAgICAgICAgIG9uSG92ZXJFbmQoZSk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKG9uSG92ZXJDaGFuZ2UgIT0gbnVsbCkge1xuICAgICAgICAgIG9uSG92ZXJDaGFuZ2UoZmFsc2UpO1xuICAgICAgICB9XG4gICAgICAgIC8vIFJlbW92ZSB0aGUgbGlzdGVuZXJzIG9uY2UgZmluaXNoZWQuXG4gICAgICAgIGFkZE1vdmVMaXN0ZW5lcih0YXJnZXQsIG51bGwpO1xuICAgICAgICBhZGRMZWF2ZUxpc3RlbmVyKHRhcmdldCwgbnVsbCk7XG4gICAgICB9O1xuXG4gICAgICAvKipcbiAgICAgICAqIExlYXZlIGVsZW1lbnRcbiAgICAgICAqL1xuICAgICAgdmFyIGxlYXZlTGlzdGVuZXIgPSBmdW5jdGlvbiBsZWF2ZUxpc3RlbmVyKGUpIHtcbiAgICAgICAgdmFyIHRhcmdldCA9IHRhcmdldFJlZi5jdXJyZW50O1xuICAgICAgICBpZiAodGFyZ2V0ICE9IG51bGwgJiYgZ2V0UG9pbnRlclR5cGUoZSkgIT09ICd0b3VjaCcpIHtcbiAgICAgICAgICBpZiAoY29udGFpbikge1xuICAgICAgICAgICAgZGlzcGF0Y2hDdXN0b21FdmVudCh0YXJnZXQsIHVubG9ja0V2ZW50VHlwZSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGhvdmVyRW5kKGUpO1xuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgICAvKipcbiAgICAgICAqIE1vdmUgd2l0aGluIGVsZW1lbnRcbiAgICAgICAqL1xuICAgICAgdmFyIG1vdmVMaXN0ZW5lciA9IGZ1bmN0aW9uIG1vdmVMaXN0ZW5lcihlKSB7XG4gICAgICAgIGlmIChnZXRQb2ludGVyVHlwZShlKSAhPT0gJ3RvdWNoJykge1xuICAgICAgICAgIGlmIChvbkhvdmVyVXBkYXRlICE9IG51bGwpIHtcbiAgICAgICAgICAgIC8vIE5vdCBhbGwgYnJvd3NlcnMgaGF2ZSB0aGVzZSBwcm9wZXJ0aWVzXG4gICAgICAgICAgICBpZiAoZS54ID09IG51bGwpIHtcbiAgICAgICAgICAgICAgZS54ID0gZS5jbGllbnRYO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKGUueSA9PSBudWxsKSB7XG4gICAgICAgICAgICAgIGUueSA9IGUuY2xpZW50WTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIG9uSG92ZXJVcGRhdGUoZSk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgICAvKipcbiAgICAgICAqIFN0YXJ0IHRoZSBob3ZlciBnZXN0dXJlXG4gICAgICAgKi9cbiAgICAgIHZhciBob3ZlclN0YXJ0ID0gZnVuY3Rpb24gaG92ZXJTdGFydChlKSB7XG4gICAgICAgIGlmIChvbkhvdmVyU3RhcnQgIT0gbnVsbCkge1xuICAgICAgICAgIG9uSG92ZXJTdGFydChlKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAob25Ib3ZlckNoYW5nZSAhPSBudWxsKSB7XG4gICAgICAgICAgb25Ib3ZlckNoYW5nZSh0cnVlKTtcbiAgICAgICAgfVxuICAgICAgICAvLyBTZXQgdGhlIGxpc3RlbmVycyBuZWVkZWQgZm9yIHRoZSByZXN0IG9mIHRoZSBob3ZlciBnZXN0dXJlLlxuICAgICAgICBpZiAob25Ib3ZlclVwZGF0ZSAhPSBudWxsKSB7XG4gICAgICAgICAgYWRkTW92ZUxpc3RlbmVyKHRhcmdldCwgIWRpc2FibGVkID8gbW92ZUxpc3RlbmVyIDogbnVsbCk7XG4gICAgICAgIH1cbiAgICAgICAgYWRkTGVhdmVMaXN0ZW5lcih0YXJnZXQsICFkaXNhYmxlZCA/IGxlYXZlTGlzdGVuZXIgOiBudWxsKTtcbiAgICAgIH07XG5cbiAgICAgIC8qKlxuICAgICAgICogRW50ZXIgZWxlbWVudFxuICAgICAgICovXG4gICAgICB2YXIgZW50ZXJMaXN0ZW5lciA9IGZ1bmN0aW9uIGVudGVyTGlzdGVuZXIoZSkge1xuICAgICAgICB2YXIgdGFyZ2V0ID0gdGFyZ2V0UmVmLmN1cnJlbnQ7XG4gICAgICAgIGlmICh0YXJnZXQgIT0gbnVsbCAmJiBnZXRQb2ludGVyVHlwZShlKSAhPT0gJ3RvdWNoJykge1xuICAgICAgICAgIGlmIChjb250YWluKSB7XG4gICAgICAgICAgICBkaXNwYXRjaEN1c3RvbUV2ZW50KHRhcmdldCwgbG9ja0V2ZW50VHlwZSk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGhvdmVyU3RhcnQoZSk7XG4gICAgICAgICAgdmFyIGxvY2tMaXN0ZW5lciA9IGZ1bmN0aW9uIGxvY2tMaXN0ZW5lcihsb2NrRXZlbnQpIHtcbiAgICAgICAgICAgIGlmIChsb2NrRXZlbnQudGFyZ2V0ICE9PSB0YXJnZXQpIHtcbiAgICAgICAgICAgICAgaG92ZXJFbmQoZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfTtcbiAgICAgICAgICB2YXIgdW5sb2NrTGlzdGVuZXIgPSBmdW5jdGlvbiB1bmxvY2tMaXN0ZW5lcihsb2NrRXZlbnQpIHtcbiAgICAgICAgICAgIGlmIChsb2NrRXZlbnQudGFyZ2V0ICE9PSB0YXJnZXQpIHtcbiAgICAgICAgICAgICAgaG92ZXJTdGFydChlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9O1xuICAgICAgICAgIGFkZExvY2tMaXN0ZW5lcih0YXJnZXQsICFkaXNhYmxlZCA/IGxvY2tMaXN0ZW5lciA6IG51bGwpO1xuICAgICAgICAgIGFkZFVubG9ja0xpc3RlbmVyKHRhcmdldCwgIWRpc2FibGVkID8gdW5sb2NrTGlzdGVuZXIgOiBudWxsKTtcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICAgIGFkZEVudGVyTGlzdGVuZXIodGFyZ2V0LCAhZGlzYWJsZWQgPyBlbnRlckxpc3RlbmVyIDogbnVsbCk7XG4gICAgfVxuICB9LCBbYWRkRW50ZXJMaXN0ZW5lciwgYWRkTW92ZUxpc3RlbmVyLCBhZGRMZWF2ZUxpc3RlbmVyLCBhZGRMb2NrTGlzdGVuZXIsIGFkZFVubG9ja0xpc3RlbmVyLCBjb250YWluLCBkaXNhYmxlZCwgb25Ib3ZlclN0YXJ0LCBvbkhvdmVyQ2hhbmdlLCBvbkhvdmVyVXBkYXRlLCBvbkhvdmVyRW5kLCB0YXJnZXRSZWZdKTtcbn1cbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFFWixJQUFJQSxzQkFBc0IsR0FBR0MsT0FBTyxDQUFDLDhDQUE4QyxDQUFDLENBQUNDLE9BQU87QUFDNUZDLE9BQU8sQ0FBQ0MsVUFBVSxHQUFHLElBQUk7QUFDekJELE9BQU8sQ0FBQ0QsT0FBTyxHQUFHRyxRQUFRO0FBQzFCLElBQUlDLFNBQVMsR0FBR0wsT0FBTyxjQUFjLENBQUM7QUFDdEMsSUFBSU0sU0FBUyxHQUFHUCxzQkFBc0IsQ0FBQ0MsT0FBTyxjQUFjLENBQUMsQ0FBQztBQUM5RCxJQUFJTyxnQkFBZ0IsR0FBR1Isc0JBQXNCLENBQUNDLE9BQU8scUJBQXFCLENBQUMsQ0FBQztBQWtCNUUsSUFBSVEsV0FBVyxHQUFHLENBQUMsQ0FBQztBQUNwQixJQUFJQyxJQUFJLEdBQUc7RUFDVEMsT0FBTyxFQUFFO0FBQ1gsQ0FBQztBQUNELElBQUlDLGFBQWEsR0FBRyxzQkFBc0I7QUFDMUMsSUFBSUMsZUFBZSxHQUFHLHdCQUF3QjtBQUM5QyxJQUFJQyxvQkFBb0IsR0FBRyxTQUF2QkEsb0JBQW9CQSxDQUFBO0VBQUEsT0FBUyxDQUFDLEVBQUUsT0FBT0MsTUFBTSxLQUFLLFdBQVcsSUFBSUEsTUFBTSxDQUFDQyxZQUFZLElBQUksSUFBSSxDQUFDO0FBQUE7QUFDakcsU0FBU0MsbUJBQW1CQSxDQUFDQyxNQUFNLEVBQUVDLElBQUksRUFBRUMsT0FBTyxFQUFFO0VBQ2xELElBQUlDLEtBQUssR0FBR0MsUUFBUSxDQUFDQyxXQUFXLENBQUMsYUFBYSxDQUFDO0VBQy9DLElBQUlDLElBQUksR0FBR0osT0FBTyxJQUFJWCxXQUFXO0lBQy9CZ0IsWUFBWSxHQUFHRCxJQUFJLENBQUNFLE9BQU87SUFDM0JBLE9BQU8sR0FBR0QsWUFBWSxLQUFLLEtBQUssQ0FBQyxHQUFHLElBQUksR0FBR0EsWUFBWTtJQUN2REUsZUFBZSxHQUFHSCxJQUFJLENBQUNJLFVBQVU7SUFDakNBLFVBQVUsR0FBR0QsZUFBZSxLQUFLLEtBQUssQ0FBQyxHQUFHLElBQUksR0FBR0EsZUFBZTtJQUNoRUUsTUFBTSxHQUFHTCxJQUFJLENBQUNLLE1BQU07RUFDdEJSLEtBQUssQ0FBQ1MsZUFBZSxDQUFDWCxJQUFJLEVBQUVPLE9BQU8sRUFBRUUsVUFBVSxFQUFFQyxNQUFNLENBQUM7RUFDeERYLE1BQU0sQ0FBQ2EsYUFBYSxDQUFDVixLQUFLLENBQUM7QUFDN0I7QUFHQSxTQUFTVyxjQUFjQSxDQUFDWCxLQUFLLEVBQUU7RUFDN0IsSUFBSVksV0FBVyxHQUFHWixLQUFLLENBQUNZLFdBQVc7RUFDbkMsT0FBT0EsV0FBVyxJQUFJLElBQUksR0FBR0EsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFM0IsU0FBUyxDQUFDNEIsV0FBVyxFQUFFLENBQUM7QUFDekU7QUFDQSxTQUFTN0IsUUFBUUEsQ0FBQzhCLFNBQVMsRUFBRUMsTUFBTSxFQUFFO0VBQ25DLElBQUlDLE9BQU8sR0FBR0QsTUFBTSxDQUFDQyxPQUFPO0lBQzFCQyxRQUFRLEdBQUdGLE1BQU0sQ0FBQ0UsUUFBUTtJQUMxQkMsWUFBWSxHQUFHSCxNQUFNLENBQUNHLFlBQVk7SUFDbENDLGFBQWEsR0FBR0osTUFBTSxDQUFDSSxhQUFhO0lBQ3BDQyxhQUFhLEdBQUdMLE1BQU0sQ0FBQ0ssYUFBYTtJQUNwQ0MsVUFBVSxHQUFHTixNQUFNLENBQUNNLFVBQVU7RUFDaEMsSUFBSUMsUUFBUSxHQUFHN0Isb0JBQW9CLENBQUMsQ0FBQztFQUNyQyxJQUFJOEIsZUFBZSxHQUFHLENBQUMsQ0FBQyxFQUFFckMsU0FBUyxDQUFDTCxPQUFPLEVBQUV5QyxRQUFRLEdBQUcsYUFBYSxHQUFHLFdBQVcsRUFBRWpDLElBQUksQ0FBQztFQUMxRixJQUFJbUMsZ0JBQWdCLEdBQUcsQ0FBQyxDQUFDLEVBQUV0QyxTQUFTLENBQUNMLE9BQU8sRUFBRXlDLFFBQVEsR0FBRyxjQUFjLEdBQUcsWUFBWSxFQUFFakMsSUFBSSxDQUFDO0VBQzdGLElBQUlvQyxnQkFBZ0IsR0FBRyxDQUFDLENBQUMsRUFBRXZDLFNBQVMsQ0FBQ0wsT0FBTyxFQUFFeUMsUUFBUSxHQUFHLGNBQWMsR0FBRyxZQUFZLEVBQUVqQyxJQUFJLENBQUM7RUFFN0YsSUFBSXFDLGVBQWUsR0FBRyxDQUFDLENBQUMsRUFBRXhDLFNBQVMsQ0FBQ0wsT0FBTyxFQUFFVSxhQUFhLEVBQUVGLElBQUksQ0FBQztFQUNqRSxJQUFJc0MsaUJBQWlCLEdBQUcsQ0FBQyxDQUFDLEVBQUV6QyxTQUFTLENBQUNMLE9BQU8sRUFBRVcsZUFBZSxFQUFFSCxJQUFJLENBQUM7RUFDckUsQ0FBQyxDQUFDLEVBQUVGLGdCQUFnQixDQUFDTixPQUFPLEVBQUUsWUFBTTtJQUNsQyxJQUFJZ0IsTUFBTSxHQUFHaUIsU0FBUyxDQUFDYyxPQUFPO0lBQzlCLElBQUkvQixNQUFNLEtBQUssSUFBSSxFQUFFO01BSW5CLElBQUlnQyxRQUFRLEdBQUcsU0FBU0EsUUFBUUEsQ0FBQ0MsQ0FBQyxFQUFFO1FBQ2xDLElBQUlULFVBQVUsSUFBSSxJQUFJLEVBQUU7VUFDdEJBLFVBQVUsQ0FBQ1MsQ0FBQyxDQUFDO1FBQ2Y7UUFDQSxJQUFJWCxhQUFhLElBQUksSUFBSSxFQUFFO1VBQ3pCQSxhQUFhLENBQUMsS0FBSyxDQUFDO1FBQ3RCO1FBRUFJLGVBQWUsQ0FBQzFCLE1BQU0sRUFBRSxJQUFJLENBQUM7UUFDN0I0QixnQkFBZ0IsQ0FBQzVCLE1BQU0sRUFBRSxJQUFJLENBQUM7TUFDaEMsQ0FBQztNQUtELElBQUlrQyxhQUFhLEdBQUcsU0FBU0EsYUFBYUEsQ0FBQ0QsQ0FBQyxFQUFFO1FBQzVDLElBQUlqQyxNQUFNLEdBQUdpQixTQUFTLENBQUNjLE9BQU87UUFDOUIsSUFBSS9CLE1BQU0sSUFBSSxJQUFJLElBQUljLGNBQWMsQ0FBQ21CLENBQUMsQ0FBQyxLQUFLLE9BQU8sRUFBRTtVQUNuRCxJQUFJZCxPQUFPLEVBQUU7WUFDWHBCLG1CQUFtQixDQUFDQyxNQUFNLEVBQUVMLGVBQWUsQ0FBQztVQUM5QztVQUNBcUMsUUFBUSxDQUFDQyxDQUFDLENBQUM7UUFDYjtNQUNGLENBQUM7TUFLRCxJQUFJRSxZQUFZLEdBQUcsU0FBU0EsWUFBWUEsQ0FBQ0YsQ0FBQyxFQUFFO1FBQzFDLElBQUluQixjQUFjLENBQUNtQixDQUFDLENBQUMsS0FBSyxPQUFPLEVBQUU7VUFDakMsSUFBSVYsYUFBYSxJQUFJLElBQUksRUFBRTtZQUV6QixJQUFJVSxDQUFDLENBQUNHLENBQUMsSUFBSSxJQUFJLEVBQUU7Y0FDZkgsQ0FBQyxDQUFDRyxDQUFDLEdBQUdILENBQUMsQ0FBQ0ksT0FBTztZQUNqQjtZQUNBLElBQUlKLENBQUMsQ0FBQ0ssQ0FBQyxJQUFJLElBQUksRUFBRTtjQUNmTCxDQUFDLENBQUNLLENBQUMsR0FBR0wsQ0FBQyxDQUFDTSxPQUFPO1lBQ2pCO1lBQ0FoQixhQUFhLENBQUNVLENBQUMsQ0FBQztVQUNsQjtRQUNGO01BQ0YsQ0FBQztNQUtELElBQUlPLFVBQVUsR0FBRyxTQUFTQSxVQUFVQSxDQUFDUCxDQUFDLEVBQUU7UUFDdEMsSUFBSVosWUFBWSxJQUFJLElBQUksRUFBRTtVQUN4QkEsWUFBWSxDQUFDWSxDQUFDLENBQUM7UUFDakI7UUFDQSxJQUFJWCxhQUFhLElBQUksSUFBSSxFQUFFO1VBQ3pCQSxhQUFhLENBQUMsSUFBSSxDQUFDO1FBQ3JCO1FBRUEsSUFBSUMsYUFBYSxJQUFJLElBQUksRUFBRTtVQUN6QkcsZUFBZSxDQUFDMUIsTUFBTSxFQUFFLENBQUNvQixRQUFRLEdBQUdlLFlBQVksR0FBRyxJQUFJLENBQUM7UUFDMUQ7UUFDQVAsZ0JBQWdCLENBQUM1QixNQUFNLEVBQUUsQ0FBQ29CLFFBQVEsR0FBR2MsYUFBYSxHQUFHLElBQUksQ0FBQztNQUM1RCxDQUFDO01BS0QsSUFBSU8sYUFBYSxHQUFHLFNBQVNBLGFBQWFBLENBQUNSLENBQUMsRUFBRTtRQUM1QyxJQUFJakMsTUFBTSxHQUFHaUIsU0FBUyxDQUFDYyxPQUFPO1FBQzlCLElBQUkvQixNQUFNLElBQUksSUFBSSxJQUFJYyxjQUFjLENBQUNtQixDQUFDLENBQUMsS0FBSyxPQUFPLEVBQUU7VUFDbkQsSUFBSWQsT0FBTyxFQUFFO1lBQ1hwQixtQkFBbUIsQ0FBQ0MsTUFBTSxFQUFFTixhQUFhLENBQUM7VUFDNUM7VUFDQThDLFVBQVUsQ0FBQ1AsQ0FBQyxDQUFDO1VBQ2IsSUFBSVMsWUFBWSxHQUFHLFNBQVNBLFlBQVlBLENBQUNDLFNBQVMsRUFBRTtZQUNsRCxJQUFJQSxTQUFTLENBQUMzQyxNQUFNLEtBQUtBLE1BQU0sRUFBRTtjQUMvQmdDLFFBQVEsQ0FBQ0MsQ0FBQyxDQUFDO1lBQ2I7VUFDRixDQUFDO1VBQ0QsSUFBSVcsY0FBYyxHQUFHLFNBQVNBLGNBQWNBLENBQUNELFNBQVMsRUFBRTtZQUN0RCxJQUFJQSxTQUFTLENBQUMzQyxNQUFNLEtBQUtBLE1BQU0sRUFBRTtjQUMvQndDLFVBQVUsQ0FBQ1AsQ0FBQyxDQUFDO1lBQ2Y7VUFDRixDQUFDO1VBQ0RKLGVBQWUsQ0FBQzdCLE1BQU0sRUFBRSxDQUFDb0IsUUFBUSxHQUFHc0IsWUFBWSxHQUFHLElBQUksQ0FBQztVQUN4RFosaUJBQWlCLENBQUM5QixNQUFNLEVBQUUsQ0FBQ29CLFFBQVEsR0FBR3dCLGNBQWMsR0FBRyxJQUFJLENBQUM7UUFDOUQ7TUFDRixDQUFDO01BQ0RqQixnQkFBZ0IsQ0FBQzNCLE1BQU0sRUFBRSxDQUFDb0IsUUFBUSxHQUFHcUIsYUFBYSxHQUFHLElBQUksQ0FBQztJQUM1RDtFQUNGLENBQUMsRUFBRSxDQUFDZCxnQkFBZ0IsRUFBRUQsZUFBZSxFQUFFRSxnQkFBZ0IsRUFBRUMsZUFBZSxFQUFFQyxpQkFBaUIsRUFBRVgsT0FBTyxFQUFFQyxRQUFRLEVBQUVDLFlBQVksRUFBRUMsYUFBYSxFQUFFQyxhQUFhLEVBQUVDLFVBQVUsRUFBRVAsU0FBUyxDQUFDLENBQUM7QUFDckw7QUFDQTRCLE1BQU0sQ0FBQzVELE9BQU8sR0FBR0EsT0FBTyxDQUFDRCxPQUFPIiwiaWdub3JlTGlzdCI6W119