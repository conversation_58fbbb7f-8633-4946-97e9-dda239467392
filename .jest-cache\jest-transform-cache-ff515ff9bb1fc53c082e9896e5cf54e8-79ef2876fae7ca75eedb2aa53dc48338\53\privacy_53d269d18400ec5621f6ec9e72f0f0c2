e2ff361ea117a07a6f9fcc188efb65e8
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_13drk2ekyo() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\settings\\privacy.tsx";
  var hash = "25c1028395df1b7a999553b5b184815c0d1ae840";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\settings\\privacy.tsx",
    statementMap: {
      "0": {
        start: {
          line: 28,
          column: 15
        },
        end: {
          line: 36,
          column: 1
        }
      },
      "1": {
        start: {
          line: 49,
          column: 32
        },
        end: {
          line: 49,
          column: 47
        }
      },
      "2": {
        start: {
          line: 50,
          column: 34
        },
        end: {
          line: 108,
          column: 4
        }
      },
      "3": {
        start: {
          line: 110,
          column: 24
        },
        end: {
          line: 129,
          column: 3
        }
      },
      "4": {
        start: {
          line: 111,
          column: 20
        },
        end: {
          line: 111,
          column: 51
        }
      },
      "5": {
        start: {
          line: 111,
          column: 39
        },
        end: {
          line: 111,
          column: 50
        }
      },
      "6": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 128,
          column: 5
        }
      },
      "7": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 125,
          column: 8
        }
      },
      "8": {
        start: {
          line: 122,
          column: 27
        },
        end: {
          line: 122,
          column: 44
        }
      },
      "9": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 24
        }
      },
      "10": {
        start: {
          line: 131,
          column: 24
        },
        end: {
          line: 139,
          column: 3
        }
      },
      "11": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 138,
          column: 6
        }
      },
      "12": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 137,
          column: 7
        }
      },
      "13": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 136,
          column: 19
        }
      },
      "14": {
        start: {
          line: 141,
          column: 21
        },
        end: {
          line: 157,
          column: 3
        }
      },
      "15": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 21
        }
      },
      "16": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 156,
          column: 5
        }
      },
      "17": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 62
        }
      },
      "18": {
        start: {
          line: 145,
          column: 35
        },
        end: {
          line: 145,
          column: 60
        }
      },
      "19": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 151,
          column: 8
        }
      },
      "20": {
        start: {
          line: 150,
          column: 38
        },
        end: {
          line: 150,
          column: 51
        }
      },
      "21": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 153,
          column: 73
        }
      },
      "22": {
        start: {
          line: 155,
          column: 6
        },
        end: {
          line: 155,
          column: 24
        }
      },
      "23": {
        start: {
          line: 159,
          column: 27
        },
        end: {
          line: 170,
          column: 3
        }
      },
      "24": {
        start: {
          line: 160,
          column: 4
        },
        end: {
          line: 169,
          column: 6
        }
      },
      "25": {
        start: {
          line: 166,
          column: 10
        },
        end: {
          line: 166,
          column: 99
        }
      },
      "26": {
        start: {
          line: 172,
          column: 30
        },
        end: {
          line: 187,
          column: 3
        }
      },
      "27": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 186,
          column: 6
        }
      },
      "28": {
        start: {
          line: 182,
          column: 12
        },
        end: {
          line: 182,
          column: 100
        }
      },
      "29": {
        start: {
          line: 189,
          column: 32
        },
        end: {
          line: 232,
          column: 3
        }
      },
      "30": {
        start: {
          line: 190,
          column: 29
        },
        end: {
          line: 190,
          column: 74
        }
      },
      "31": {
        start: {
          line: 190,
          column: 50
        },
        end: {
          line: 190,
          column: 73
        }
      },
      "32": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 231,
          column: 6
        }
      },
      "33": {
        start: {
          line: 196,
          column: 32
        },
        end: {
          line: 196,
          column: 44
        }
      },
      "34": {
        start: {
          line: 197,
          column: 10
        },
        end: {
          line: 228,
          column: 12
        }
      },
      "35": {
        start: {
          line: 223,
          column: 37
        },
        end: {
          line: 223,
          column: 62
        }
      },
      "36": {
        start: {
          line: 234,
          column: 2
        },
        end: {
          line: 306,
          column: 4
        }
      },
      "37": {
        start: {
          line: 242,
          column: 43
        },
        end: {
          line: 242,
          column: 56
        }
      },
      "38": {
        start: {
          line: 309,
          column: 15
        },
        end: {
          line: 470,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "PrivacyScreen",
        decl: {
          start: {
            line: 48,
            column: 24
          },
          end: {
            line: 48,
            column: 37
          }
        },
        loc: {
          start: {
            line: 48,
            column: 40
          },
          end: {
            line: 307,
            column: 1
          }
        },
        line: 48
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 110,
            column: 24
          },
          end: {
            line: 110,
            column: 25
          }
        },
        loc: {
          start: {
            line: 110,
            column: 40
          },
          end: {
            line: 129,
            column: 3
          }
        },
        line: 110
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 111,
            column: 34
          },
          end: {
            line: 111,
            column: 35
          }
        },
        loc: {
          start: {
            line: 111,
            column: 39
          },
          end: {
            line: 111,
            column: 50
          }
        },
        line: 111
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 122,
            column: 21
          },
          end: {
            line: 122,
            column: 22
          }
        },
        loc: {
          start: {
            line: 122,
            column: 27
          },
          end: {
            line: 122,
            column: 44
          }
        },
        line: 122
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 131,
            column: 24
          },
          end: {
            line: 131,
            column: 25
          }
        },
        loc: {
          start: {
            line: 131,
            column: 40
          },
          end: {
            line: 139,
            column: 3
          }
        },
        line: 131
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 132,
            column: 16
          },
          end: {
            line: 132,
            column: 17
          }
        },
        loc: {
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 137,
            column: 7
          }
        },
        line: 133
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 133,
            column: 15
          },
          end: {
            line: 133,
            column: 16
          }
        },
        loc: {
          start: {
            line: 134,
            column: 8
          },
          end: {
            line: 136,
            column: 19
          }
        },
        line: 134
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 141,
            column: 21
          },
          end: {
            line: 141,
            column: 22
          }
        },
        loc: {
          start: {
            line: 141,
            column: 33
          },
          end: {
            line: 157,
            column: 3
          }
        },
        line: 141
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 145,
            column: 24
          },
          end: {
            line: 145,
            column: 25
          }
        },
        loc: {
          start: {
            line: 145,
            column: 35
          },
          end: {
            line: 145,
            column: 60
          }
        },
        line: 145
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 150,
            column: 32
          },
          end: {
            line: 150,
            column: 33
          }
        },
        loc: {
          start: {
            line: 150,
            column: 38
          },
          end: {
            line: 150,
            column: 51
          }
        },
        line: 150
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 159,
            column: 27
          },
          end: {
            line: 159,
            column: 28
          }
        },
        loc: {
          start: {
            line: 159,
            column: 33
          },
          end: {
            line: 170,
            column: 3
          }
        },
        line: 159
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 165,
            column: 43
          },
          end: {
            line: 165,
            column: 44
          }
        },
        loc: {
          start: {
            line: 165,
            column: 49
          },
          end: {
            line: 167,
            column: 9
          }
        },
        line: 165
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 172,
            column: 30
          },
          end: {
            line: 172,
            column: 31
          }
        },
        loc: {
          start: {
            line: 172,
            column: 36
          },
          end: {
            line: 187,
            column: 3
          }
        },
        line: 172
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 181,
            column: 19
          },
          end: {
            line: 181,
            column: 20
          }
        },
        loc: {
          start: {
            line: 181,
            column: 25
          },
          end: {
            line: 183,
            column: 11
          }
        },
        line: 181
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 189,
            column: 32
          },
          end: {
            line: 189,
            column: 33
          }
        },
        loc: {
          start: {
            line: 189,
            column: 69
          },
          end: {
            line: 232,
            column: 3
          }
        },
        line: 189
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 190,
            column: 45
          },
          end: {
            line: 190,
            column: 46
          }
        },
        loc: {
          start: {
            line: 190,
            column: 50
          },
          end: {
            line: 190,
            column: 73
          }
        },
        line: 190
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 195,
            column: 30
          },
          end: {
            line: 195,
            column: 31
          }
        },
        loc: {
          start: {
            line: 195,
            column: 43
          },
          end: {
            line: 229,
            column: 9
          }
        },
        line: 195
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 223,
            column: 31
          },
          end: {
            line: 223,
            column: 32
          }
        },
        loc: {
          start: {
            line: 223,
            column: 37
          },
          end: {
            line: 223,
            column: 62
          }
        },
        line: 223
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 242,
            column: 37
          },
          end: {
            line: 242,
            column: 38
          }
        },
        loc: {
          start: {
            line: 242,
            column: 43
          },
          end: {
            line: 242,
            column: 56
          }
        },
        line: 242
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        }, {
          start: {
            line: 126,
            column: 11
          },
          end: {
            line: 128,
            column: 5
          }
        }],
        line: 113
      },
      "1": {
        loc: {
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 113,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 113,
            column: 8
          },
          end: {
            line: 113,
            column: 25
          }
        }, {
          start: {
            line: 113,
            column: 29
          },
          end: {
            line: 113,
            column: 44
          }
        }],
        line: 113
      },
      "2": {
        loc: {
          start: {
            line: 134,
            column: 8
          },
          end: {
            line: 136,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 135,
            column: 12
          },
          end: {
            line: 135,
            column: 53
          }
        }, {
          start: {
            line: 136,
            column: 12
          },
          end: {
            line: 136,
            column: 19
          }
        }],
        line: 134
      },
      "3": {
        loc: {
          start: {
            line: 202,
            column: 18
          },
          end: {
            line: 202,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 202,
            column: 18
          },
          end: {
            line: 202,
            column: 34
          }
        }, {
          start: {
            line: 202,
            column: 38
          },
          end: {
            line: 202,
            column: 57
          }
        }],
        line: 202
      },
      "4": {
        loc: {
          start: {
            line: 206,
            column: 27
          },
          end: {
            line: 206,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 206,
            column: 46
          },
          end: {
            line: 206,
            column: 56
          }
        }, {
          start: {
            line: 206,
            column: 59
          },
          end: {
            line: 206,
            column: 73
          }
        }],
        line: 206
      },
      "5": {
        loc: {
          start: {
            line: 212,
            column: 21
          },
          end: {
            line: 216,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 212,
            column: 21
          },
          end: {
            line: 212,
            column: 37
          }
        }, {
          start: {
            line: 213,
            column: 22
          },
          end: {
            line: 215,
            column: 29
          }
        }],
        line: 212
      },
      "6": {
        loc: {
          start: {
            line: 225,
            column: 28
          },
          end: {
            line: 225,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 46
          },
          end: {
            line: 225,
            column: 58
          }
        }, {
          start: {
            line: 225,
            column: 61
          },
          end: {
            line: 225,
            column: 72
          }
        }],
        line: 225
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "25c1028395df1b7a999553b5b184815c0d1ae840"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_13drk2ekyo = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_13drk2ekyo();
import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView, Switch, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import Card from "../../components/ui/Card";
import Button from "../../components/ui/Button";
import { ArrowLeft, Shield, Eye, Users, Database, Lock, Globe, Camera, Mic } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_13drk2ekyo().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444'
});
export default function PrivacyScreen() {
  cov_13drk2ekyo().f[0]++;
  var _ref = (cov_13drk2ekyo().s[1]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    loading = _ref2[0],
    setLoading = _ref2[1];
  var _ref3 = (cov_13drk2ekyo().s[2]++, useState([{
      id: 'profile_visibility',
      title: 'Public Profile',
      description: 'Allow other users to find and view your profile',
      icon: Eye,
      enabled: true,
      category: 'profile'
    }, {
      id: 'match_history_public',
      title: 'Public Match History',
      description: 'Show your match results to other players',
      icon: Globe,
      enabled: false,
      category: 'profile'
    }, {
      id: 'social_interactions',
      title: 'Social Interactions',
      description: 'Allow friend requests and messages',
      icon: Users,
      enabled: true,
      category: 'profile'
    }, {
      id: 'data_analytics',
      title: 'Performance Analytics',
      description: 'Use your data to improve AI coaching recommendations',
      icon: Database,
      enabled: true,
      category: 'data'
    }, {
      id: 'data_sharing',
      title: 'Anonymous Data Sharing',
      description: 'Help improve the app with anonymous usage data',
      icon: Shield,
      enabled: false,
      category: 'data'
    }, {
      id: 'camera_access',
      title: 'Camera Access',
      description: 'Required for video analysis and photo uploads',
      icon: Camera,
      enabled: true,
      category: 'permissions',
      critical: true
    }, {
      id: 'microphone_access',
      title: 'Microphone Access',
      description: 'For voice coaching and audio feedback',
      icon: Mic,
      enabled: false,
      category: 'permissions'
    }])),
    _ref4 = _slicedToArray(_ref3, 2),
    settings = _ref4[0],
    setSettings = _ref4[1];
  cov_13drk2ekyo().s[3]++;
  var toggleSetting = function toggleSetting(id) {
    cov_13drk2ekyo().f[1]++;
    var setting = (cov_13drk2ekyo().s[4]++, settings.find(function (s) {
      cov_13drk2ekyo().f[2]++;
      cov_13drk2ekyo().s[5]++;
      return s.id === id;
    }));
    cov_13drk2ekyo().s[6]++;
    if ((cov_13drk2ekyo().b[1][0]++, setting != null && setting.critical) && (cov_13drk2ekyo().b[1][1]++, setting.enabled)) {
      cov_13drk2ekyo().b[0][0]++;
      cov_13drk2ekyo().s[7]++;
      Alert.alert('Critical Permission', 'This permission is required for core app functionality. Disabling it may limit your experience.', [{
        text: 'Cancel',
        style: 'cancel'
      }, {
        text: 'Disable Anyway',
        style: 'destructive',
        onPress: function onPress() {
          cov_13drk2ekyo().f[3]++;
          cov_13drk2ekyo().s[8]++;
          return updateSetting(id);
        }
      }]);
    } else {
      cov_13drk2ekyo().b[0][1]++;
      cov_13drk2ekyo().s[9]++;
      updateSetting(id);
    }
  };
  cov_13drk2ekyo().s[10]++;
  var updateSetting = function updateSetting(id) {
    cov_13drk2ekyo().f[4]++;
    cov_13drk2ekyo().s[11]++;
    setSettings(function (prev) {
      cov_13drk2ekyo().f[5]++;
      cov_13drk2ekyo().s[12]++;
      return prev.map(function (setting) {
        cov_13drk2ekyo().f[6]++;
        cov_13drk2ekyo().s[13]++;
        return setting.id === id ? (cov_13drk2ekyo().b[2][0]++, Object.assign({}, setting, {
          enabled: !setting.enabled
        })) : (cov_13drk2ekyo().b[2][1]++, setting);
      });
    });
  };
  cov_13drk2ekyo().s[14]++;
  var handleSave = function () {
    var _ref5 = _asyncToGenerator(function* () {
      cov_13drk2ekyo().f[7]++;
      cov_13drk2ekyo().s[15]++;
      setLoading(true);
      cov_13drk2ekyo().s[16]++;
      try {
        cov_13drk2ekyo().s[17]++;
        yield new Promise(function (resolve) {
          cov_13drk2ekyo().f[8]++;
          cov_13drk2ekyo().s[18]++;
          return setTimeout(resolve, 1000);
        });
        cov_13drk2ekyo().s[19]++;
        Alert.alert('Success', 'Privacy settings saved successfully!', [{
          text: 'OK',
          onPress: function onPress() {
            cov_13drk2ekyo().f[9]++;
            cov_13drk2ekyo().s[20]++;
            return router.back();
          }
        }]);
      } catch (error) {
        cov_13drk2ekyo().s[21]++;
        Alert.alert('Error', 'Failed to save settings. Please try again.');
      } finally {
        cov_13drk2ekyo().s[22]++;
        setLoading(false);
      }
    });
    return function handleSave() {
      return _ref5.apply(this, arguments);
    };
  }();
  cov_13drk2ekyo().s[23]++;
  var handleDataExport = function handleDataExport() {
    cov_13drk2ekyo().f[10]++;
    cov_13drk2ekyo().s[24]++;
    Alert.alert('Export Data', 'We will prepare your data export and send it to your email within 24 hours.', [{
      text: 'Cancel',
      style: 'cancel'
    }, {
      text: 'Request Export',
      onPress: function onPress() {
        cov_13drk2ekyo().f[11]++;
        cov_13drk2ekyo().s[25]++;
        Alert.alert('Export Requested', 'You will receive an email with your data export link.');
      }
    }]);
  };
  cov_13drk2ekyo().s[26]++;
  var handleDeleteAccount = function handleDeleteAccount() {
    cov_13drk2ekyo().f[12]++;
    cov_13drk2ekyo().s[27]++;
    Alert.alert('Delete Account', 'This action cannot be undone. All your data, including match history, training progress, and social connections will be permanently deleted.', [{
      text: 'Cancel',
      style: 'cancel'
    }, {
      text: 'Delete Account',
      style: 'destructive',
      onPress: function onPress() {
        cov_13drk2ekyo().f[13]++;
        cov_13drk2ekyo().s[28]++;
        Alert.alert('Account Deletion', 'Please contact support to complete account deletion.');
      }
    }]);
  };
  cov_13drk2ekyo().s[29]++;
  var renderPrivacyCategory = function renderPrivacyCategory(category, title) {
    cov_13drk2ekyo().f[14]++;
    var categorySettings = (cov_13drk2ekyo().s[30]++, settings.filter(function (s) {
      cov_13drk2ekyo().f[15]++;
      cov_13drk2ekyo().s[31]++;
      return s.category === category;
    }));
    cov_13drk2ekyo().s[32]++;
    return _jsxs(Card, {
      style: styles.categoryCard,
      children: [_jsx(Text, {
        style: styles.categoryTitle,
        children: title
      }), categorySettings.map(function (setting) {
        cov_13drk2ekyo().f[16]++;
        var IconComponent = (cov_13drk2ekyo().s[33]++, setting.icon);
        cov_13drk2ekyo().s[34]++;
        return _jsxs(View, {
          style: styles.settingRow,
          children: [_jsxs(View, {
            style: styles.settingInfo,
            children: [_jsx(View, {
              style: [styles.settingIcon, (cov_13drk2ekyo().b[3][0]++, setting.critical) && (cov_13drk2ekyo().b[3][1]++, styles.criticalIcon)],
              children: _jsx(IconComponent, {
                size: 20,
                color: setting.critical ? (cov_13drk2ekyo().b[4][0]++, colors.red) : (cov_13drk2ekyo().b[4][1]++, colors.primary)
              })
            }), _jsxs(View, {
              style: styles.settingText,
              children: [_jsxs(View, {
                style: styles.settingTitleRow,
                children: [_jsx(Text, {
                  style: styles.settingTitle,
                  children: setting.title
                }), (cov_13drk2ekyo().b[5][0]++, setting.critical) && (cov_13drk2ekyo().b[5][1]++, _jsx(View, {
                  style: styles.criticalBadge,
                  children: _jsx(Text, {
                    style: styles.criticalText,
                    children: "Required"
                  })
                }))]
              }), _jsx(Text, {
                style: styles.settingDescription,
                children: setting.description
              })]
            })]
          }), _jsx(Switch, {
            value: setting.enabled,
            onValueChange: function onValueChange() {
              cov_13drk2ekyo().f[17]++;
              cov_13drk2ekyo().s[35]++;
              return toggleSetting(setting.id);
            },
            trackColor: {
              false: colors.lightGray,
              true: colors.primary
            },
            thumbColor: setting.enabled ? (cov_13drk2ekyo().b[6][0]++, colors.white) : (cov_13drk2ekyo().b[6][1]++, colors.gray)
          })]
        }, setting.id);
      })]
    }, category);
  };
  cov_13drk2ekyo().s[36]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(LinearGradient, {
      colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
      style: styles.gradient,
      children: [_jsxs(View, {
        style: styles.header,
        children: [_jsx(TouchableOpacity, {
          onPress: function onPress() {
            cov_13drk2ekyo().f[18]++;
            cov_13drk2ekyo().s[37]++;
            return router.back();
          },
          style: styles.backButton,
          children: _jsx(ArrowLeft, {
            size: 24,
            color: "white"
          })
        }), _jsx(Text, {
          style: styles.title,
          children: "Privacy & Security"
        }), _jsx(View, {
          style: styles.placeholder
        })]
      }), _jsxs(ScrollView, {
        style: styles.content,
        showsVerticalScrollIndicator: false,
        children: [_jsxs(Card, {
          style: styles.overviewCard,
          children: [_jsxs(View, {
            style: styles.overviewHeader,
            children: [_jsx(Shield, {
              size: 24,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.overviewTitle,
              children: "Your Privacy Matters"
            })]
          }), _jsx(Text, {
            style: styles.overviewText,
            children: "Control how your data is used and shared. We're committed to protecting your privacy while providing the best tennis coaching experience."
          })]
        }), renderPrivacyCategory('profile', 'Profile & Visibility'), renderPrivacyCategory('data', 'Data Usage'), renderPrivacyCategory('permissions', 'App Permissions'), _jsxs(Card, {
          style: styles.dataCard,
          children: [_jsx(Text, {
            style: styles.dataTitle,
            children: "Data Management"
          }), _jsx(Text, {
            style: styles.dataDescription,
            children: "You have full control over your personal data."
          }), _jsxs(View, {
            style: styles.dataActions,
            children: [_jsxs(TouchableOpacity, {
              style: styles.dataButton,
              onPress: handleDataExport,
              children: [_jsx(Database, {
                size: 20,
                color: colors.primary
              }), _jsx(Text, {
                style: styles.dataButtonText,
                children: "Export My Data"
              })]
            }), _jsxs(TouchableOpacity, {
              style: [styles.dataButton, styles.deleteButton],
              onPress: handleDeleteAccount,
              children: [_jsx(Lock, {
                size: 20,
                color: colors.red
              }), _jsx(Text, {
                style: [styles.dataButtonText, styles.deleteButtonText],
                children: "Delete Account"
              })]
            })]
          })]
        }), _jsx(View, {
          style: styles.saveContainer,
          children: _jsx(Button, {
            title: "Save Privacy Settings",
            onPress: handleSave,
            loading: loading,
            style: styles.saveButton
          })
        })]
      })]
    })
  });
}
var styles = (cov_13drk2ekyo().s[38]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10
  },
  backButton: {
    padding: 8
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: colors.white
  },
  placeholder: {
    width: 40
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  overviewCard: {
    padding: 20,
    marginBottom: 16
  },
  overviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  overviewTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginLeft: 12
  },
  overviewText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 20
  },
  categoryCard: {
    padding: 20,
    marginBottom: 16
  },
  categoryTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 16
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12
  },
  criticalIcon: {
    backgroundColor: '#fef2f2'
  },
  settingText: {
    flex: 1
  },
  settingTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: colors.dark
  },
  criticalBadge: {
    marginLeft: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
    backgroundColor: colors.red,
    borderRadius: 4
  },
  criticalText: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: colors.white
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  dataCard: {
    padding: 20,
    marginBottom: 16
  },
  dataTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 8
  },
  dataDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 16
  },
  dataActions: {
    gap: 12
  },
  dataButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: colors.lightGray
  },
  deleteButton: {
    backgroundColor: '#fef2f2'
  },
  dataButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.dark,
    marginLeft: 8
  },
  deleteButtonText: {
    color: colors.red
  },
  saveContainer: {
    paddingVertical: 20
  },
  saveButton: {
    width: '100%'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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