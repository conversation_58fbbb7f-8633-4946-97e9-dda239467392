bdff2c6df91652e6a18fb6b59c2219<PERSON>
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var NativeAnimatedAPI = _NativeAnimatedHelper.default.API;
var _uniqueId = 1;
var AnimatedNode = function () {
  function AnimatedNode() {
    (0, _classCallCheck2.default)(this, AnimatedNode);
    this._listeners = {};
  }
  return (0, _createClass2.default)(AnimatedNode, [{
    key: "__attach",
    value: function __attach() {}
  }, {
    key: "__detach",
    value: function __detach() {
      if (this.__isNative && this.__nativeTag != null) {
        _NativeAnimatedHelper.default.API.dropAnimatedNode(this.__nativeTag);
        this.__nativeTag = undefined;
      }
    }
  }, {
    key: "__getValue",
    value: function __getValue() {}
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      return this.__getValue();
    }
  }, {
    key: "__addChild",
    value: function __addChild(child) {}
  }, {
    key: "__removeChild",
    value: function __removeChild(child) {}
  }, {
    key: "__getChildren",
    value: function __getChildren() {
      return [];
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      if (!this.__isNative) {
        throw new Error('This node cannot be made a "native" animated node');
      }
      this._platformConfig = platformConfig;
      if (this.hasListeners()) {
        this._startListeningToNativeValueUpdates();
      }
    }
  }, {
    key: "addListener",
    value: function addListener(callback) {
      var id = String(_uniqueId++);
      this._listeners[id] = callback;
      if (this.__isNative) {
        this._startListeningToNativeValueUpdates();
      }
      return id;
    }
  }, {
    key: "removeListener",
    value: function removeListener(id) {
      delete this._listeners[id];
      if (this.__isNative && !this.hasListeners()) {
        this._stopListeningForNativeValueUpdates();
      }
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners() {
      this._listeners = {};
      if (this.__isNative) {
        this._stopListeningForNativeValueUpdates();
      }
    }
  }, {
    key: "hasListeners",
    value: function hasListeners() {
      return !!Object.keys(this._listeners).length;
    }
  }, {
    key: "_startListeningToNativeValueUpdates",
    value: function _startListeningToNativeValueUpdates() {
      var _this = this;
      if (this.__nativeAnimatedValueListener && !this.__shouldUpdateListenersForNewNativeTag) {
        return;
      }
      if (this.__shouldUpdateListenersForNewNativeTag) {
        this.__shouldUpdateListenersForNewNativeTag = false;
        this._stopListeningForNativeValueUpdates();
      }
      NativeAnimatedAPI.startListeningToAnimatedNodeValue(this.__getNativeTag());
      this.__nativeAnimatedValueListener = _NativeAnimatedHelper.default.nativeEventEmitter.addListener('onAnimatedValueUpdate', function (data) {
        if (data.tag !== _this.__getNativeTag()) {
          return;
        }
        _this.__onAnimatedValueUpdateReceived(data.value);
      });
    }
  }, {
    key: "__onAnimatedValueUpdateReceived",
    value: function __onAnimatedValueUpdateReceived(value) {
      this.__callListeners(value);
    }
  }, {
    key: "__callListeners",
    value: function __callListeners(value) {
      for (var _key in this._listeners) {
        this._listeners[_key]({
          value: value
        });
      }
    }
  }, {
    key: "_stopListeningForNativeValueUpdates",
    value: function _stopListeningForNativeValueUpdates() {
      if (!this.__nativeAnimatedValueListener) {
        return;
      }
      this.__nativeAnimatedValueListener.remove();
      this.__nativeAnimatedValueListener = null;
      NativeAnimatedAPI.stopListeningToAnimatedNodeValue(this.__getNativeTag());
    }
  }, {
    key: "__getNativeTag",
    value: function __getNativeTag() {
      var _this$__nativeTag;
      _NativeAnimatedHelper.default.assertNativeAnimatedModule();
      (0, _invariant.default)(this.__isNative, 'Attempt to get native tag from node not marked as "native"');
      var nativeTag = (_this$__nativeTag = this.__nativeTag) !== null && _this$__nativeTag !== void 0 ? _this$__nativeTag : _NativeAnimatedHelper.default.generateNewNodeTag();
      if (this.__nativeTag == null) {
        this.__nativeTag = nativeTag;
        var config = this.__getNativeConfig();
        if (this._platformConfig) {
          config.platformConfig = this._platformConfig;
        }
        _NativeAnimatedHelper.default.API.createAnimatedNode(nativeTag, config);
        this.__shouldUpdateListenersForNewNativeTag = true;
      }
      return nativeTag;
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      throw new Error('This JS animated node type cannot be used as native animated node');
    }
  }, {
    key: "toJSON",
    value: function toJSON() {
      return this.__getValue();
    }
  }, {
    key: "__getPlatformConfig",
    value: function __getPlatformConfig() {
      return this._platformConfig;
    }
  }, {
    key: "__setPlatformConfig",
    value: function __setPlatformConfig(platformConfig) {
      this._platformConfig = platformConfig;
    }
  }]);
}();
var _default = exports.default = AnimatedNode;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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