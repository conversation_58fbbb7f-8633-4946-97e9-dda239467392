{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "React", "_reactDom", "_canUseDom", "ModalPortal", "props", "children", "elementRef", "useRef", "current", "element", "document", "createElement", "body", "append<PERSON><PERSON><PERSON>", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "createPortal", "_default", "module"], "sources": ["ModalPortal.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _canUseDom = _interopRequireDefault(require(\"../../modules/canUseDom\"));\n/**\n * Copyright (c) Nicolas <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction ModalPortal(props) {\n  var children = props.children;\n  var elementRef = React.useRef(null);\n  if (_canUseDom.default && !elementRef.current) {\n    var element = document.createElement('div');\n    if (element && document.body) {\n      document.body.appendChild(element);\n      elementRef.current = element;\n    }\n  }\n  React.useEffect(() => {\n    if (_canUseDom.default) {\n      return () => {\n        if (document.body && elementRef.current) {\n          document.body.removeChild(elementRef.current);\n          elementRef.current = null;\n        }\n      };\n    }\n  }, []);\n  return elementRef.current && _canUseDom.default ? /*#__PURE__*/_reactDom.default.createPortal(children, elementRef.current) : null;\n}\nvar _default = exports.default = ModalPortal;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,KAAK,GAAGH,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIM,SAAS,GAAGP,sBAAsB,CAACC,OAAO,CAAC,WAAW,CAAC,CAAC;AAC5D,IAAIO,UAAU,GAAGR,sBAAsB,CAACC,OAAO,0BAA0B,CAAC,CAAC;AAW3E,SAASQ,WAAWA,CAACC,KAAK,EAAE;EAC1B,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC7B,IAAIC,UAAU,GAAGN,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIL,UAAU,CAACN,OAAO,IAAI,CAACU,UAAU,CAACE,OAAO,EAAE;IAC7C,IAAIC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IAC3C,IAAIF,OAAO,IAAIC,QAAQ,CAACE,IAAI,EAAE;MAC5BF,QAAQ,CAACE,IAAI,CAACC,WAAW,CAACJ,OAAO,CAAC;MAClCH,UAAU,CAACE,OAAO,GAAGC,OAAO;IAC9B;EACF;EACAT,KAAK,CAACc,SAAS,CAAC,YAAM;IACpB,IAAIZ,UAAU,CAACN,OAAO,EAAE;MACtB,OAAO,YAAM;QACX,IAAIc,QAAQ,CAACE,IAAI,IAAIN,UAAU,CAACE,OAAO,EAAE;UACvCE,QAAQ,CAACE,IAAI,CAACG,WAAW,CAACT,UAAU,CAACE,OAAO,CAAC;UAC7CF,UAAU,CAACE,OAAO,GAAG,IAAI;QAC3B;MACF,CAAC;IACH;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,UAAU,CAACE,OAAO,IAAIN,UAAU,CAACN,OAAO,GAAgBK,SAAS,CAACL,OAAO,CAACoB,YAAY,CAACX,QAAQ,EAAEC,UAAU,CAACE,OAAO,CAAC,GAAG,IAAI;AACpI;AACA,IAAIS,QAAQ,GAAGnB,OAAO,CAACF,OAAO,GAAGO,WAAW;AAC5Ce,MAAM,CAACpB,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}