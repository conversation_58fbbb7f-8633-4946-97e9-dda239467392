7a59ba3db818b99c6d22b01cf292f744
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
exports.__esModule = true;
exports.default = exports.ImageUriCache = void 0;
var dataUriPattern = /^data:/;
var ImageUriCache = function () {
  function ImageUriCache() {
    (0, _classCallCheck2.default)(this, ImageUriCache);
  }
  return (0, _createClass2.default)(ImageUriCache, null, [{
    key: "has",
    value: function has(uri) {
      var entries = ImageUriCache._entries;
      var isDataUri = dataUriPattern.test(uri);
      return isDataUri || Boolean(entries[uri]);
    }
  }, {
    key: "add",
    value: function add(uri) {
      var entries = ImageUriCache._entries;
      var lastUsedTimestamp = Date.now();
      if (entries[uri]) {
        entries[uri].lastUsedTimestamp = lastUsedTimestamp;
        entries[uri].refCount += 1;
      } else {
        entries[uri] = {
          lastUsedTimestamp: lastUsedTimestamp,
          refCount: 1
        };
      }
    }
  }, {
    key: "remove",
    value: function remove(uri) {
      var entries = ImageUriCache._entries;
      if (entries[uri]) {
        entries[uri].refCount -= 1;
      }
      ImageUriCache._cleanUpIfNeeded();
    }
  }, {
    key: "_cleanUpIfNeeded",
    value: function _cleanUpIfNeeded() {
      var entries = ImageUriCache._entries;
      var imageUris = Object.keys(entries);
      if (imageUris.length + 1 > ImageUriCache._maximumEntries) {
        var leastRecentlyUsedKey;
        var leastRecentlyUsedEntry;
        imageUris.forEach(function (uri) {
          var entry = entries[uri];
          if ((!leastRecentlyUsedEntry || entry.lastUsedTimestamp < leastRecentlyUsedEntry.lastUsedTimestamp) && entry.refCount === 0) {
            leastRecentlyUsedKey = uri;
            leastRecentlyUsedEntry = entry;
          }
        });
        if (leastRecentlyUsedKey) {
          delete entries[leastRecentlyUsedKey];
        }
      }
    }
  }]);
}();
exports.ImageUriCache = ImageUriCache;
ImageUriCache._maximumEntries = 256;
ImageUriCache._entries = {};
var id = 0;
var requests = {};
var ImageLoader = {
  abort: function abort(requestId) {
    var image = requests["" + requestId];
    if (image) {
      image.onerror = null;
      image.onload = null;
      image = null;
      delete requests["" + requestId];
    }
  },
  getSize: function getSize(uri, success, failure) {
    var complete = false;
    var interval = setInterval(callback, 16);
    var requestId = ImageLoader.load(uri, callback, errorCallback);
    function callback() {
      var image = requests["" + requestId];
      if (image) {
        var naturalHeight = image.naturalHeight,
          naturalWidth = image.naturalWidth;
        if (naturalHeight && naturalWidth) {
          success(naturalWidth, naturalHeight);
          complete = true;
        }
      }
      if (complete) {
        ImageLoader.abort(requestId);
        clearInterval(interval);
      }
    }
    function errorCallback() {
      if (typeof failure === 'function') {
        failure();
      }
      ImageLoader.abort(requestId);
      clearInterval(interval);
    }
  },
  has: function has(uri) {
    return ImageUriCache.has(uri);
  },
  load: function load(uri, onLoad, onError) {
    id += 1;
    var image = new window.Image();
    image.onerror = onError;
    image.onload = function (e) {
      var onDecode = function onDecode() {
        return onLoad({
          nativeEvent: e
        });
      };
      if (typeof image.decode === 'function') {
        image.decode().then(onDecode, onDecode);
      } else {
        setTimeout(onDecode, 0);
      }
    };
    image.src = uri;
    requests["" + id] = image;
    return id;
  },
  prefetch: function prefetch(uri) {
    return new Promise(function (resolve, reject) {
      ImageLoader.load(uri, function () {
        ImageUriCache.add(uri);
        ImageUriCache.remove(uri);
        resolve();
      }, reject);
    });
  },
  queryCache: function queryCache(uris) {
    var result = {};
    uris.forEach(function (u) {
      if (ImageUriCache.has(u)) {
        result[u] = 'disk/memory';
      }
    });
    return Promise.resolve(result);
  }
};
var _default = exports.default = ImageLoader;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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