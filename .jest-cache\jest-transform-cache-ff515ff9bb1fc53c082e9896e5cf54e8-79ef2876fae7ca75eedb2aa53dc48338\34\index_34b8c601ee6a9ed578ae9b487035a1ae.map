{"version": 3, "names": ["exports", "__esModule", "default", "infoLog", "_console", "console", "log", "apply", "arguments", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use strict';\n\n/**\n * Intentional info-level logging for clear separation from ad-hoc console debug logging.\n */\nexports.__esModule = true;\nexports.default = void 0;\nfunction infoLog() {\n  return console.log(...arguments);\n}\nvar _default = exports.default = infoLog;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;AAKZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,SAASC,OAAOA,CAAA,EAAG;EAAA,IAAAC,QAAA;EACjB,OAAO,CAAAA,QAAA,GAAAC,OAAO,EAACC,GAAG,CAAAC,KAAA,CAAAH,QAAA,EAAII,SAAS,CAAC;AAClC;AACA,IAAIC,QAAQ,GAAGT,OAAO,CAACE,OAAO,GAAGC,OAAO;AACxCO,MAAM,CAACV,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}