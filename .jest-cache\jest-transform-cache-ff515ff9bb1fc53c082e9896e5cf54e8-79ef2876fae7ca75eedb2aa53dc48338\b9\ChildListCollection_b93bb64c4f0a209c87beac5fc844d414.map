{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_createForOfIteratorHelperLoose2", "_invariant", "ChildListCollection", "_cellKeyToChildren", "Map", "_children<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "value", "add", "list", "cellKey", "_this$_cellKeyToChild", "has", "cellLists", "get", "Set", "set", "remove", "delete", "size", "for<PERSON>ach", "fn", "_iterator", "values", "_step", "done", "listSet", "_iterator2", "_step2", "forEachInCell", "_this$_cellKeyToChild2", "_iterator3", "_step3", "anyInCell", "_this$_cellKeyToChild3", "_iterator4", "_step4", "module"], "sources": ["ChildListCollection.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _createForOfIteratorHelperLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createForOfIteratorHelperLoose\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nclass ChildListCollection {\n  constructor() {\n    this._cellKeyToChildren = new Map();\n    this._childrenToCellKey = new Map();\n  }\n  add(list, cellKey) {\n    var _this$_cellKeyToChild;\n    (0, _invariant.default)(!this._childrenToCellKey.has(list), 'Trying to add already present child list');\n    var cellLists = (_this$_cellKeyToChild = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild !== void 0 ? _this$_cellKeyToChild : new Set();\n    cellLists.add(list);\n    this._cellKeyToChildren.set(cellKey, cellLists);\n    this._childrenToCellKey.set(list, cellKey);\n  }\n  remove(list) {\n    var cellKey = this._childrenToCellKey.get(list);\n    (0, _invariant.default)(cellKey != null, 'Trying to remove non-present child list');\n    this._childrenToCellKey.delete(list);\n    var cellLists = this._cellKeyToChildren.get(cellKey);\n    (0, _invariant.default)(cellLists, '_cellKeyToChildren should contain cellKey');\n    cellLists.delete(list);\n    if (cellLists.size === 0) {\n      this._cellKeyToChildren.delete(cellKey);\n    }\n  }\n  forEach(fn) {\n    for (var _iterator = (0, _createForOfIteratorHelperLoose2.default)(this._cellKeyToChildren.values()), _step; !(_step = _iterator()).done;) {\n      var listSet = _step.value;\n      for (var _iterator2 = (0, _createForOfIteratorHelperLoose2.default)(listSet), _step2; !(_step2 = _iterator2()).done;) {\n        var list = _step2.value;\n        fn(list);\n      }\n    }\n  }\n  forEachInCell(cellKey, fn) {\n    var _this$_cellKeyToChild2;\n    var listSet = (_this$_cellKeyToChild2 = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild2 !== void 0 ? _this$_cellKeyToChild2 : [];\n    for (var _iterator3 = (0, _createForOfIteratorHelperLoose2.default)(listSet), _step3; !(_step3 = _iterator3()).done;) {\n      var list = _step3.value;\n      fn(list);\n    }\n  }\n  anyInCell(cellKey, fn) {\n    var _this$_cellKeyToChild3;\n    var listSet = (_this$_cellKeyToChild3 = this._cellKeyToChildren.get(cellKey)) !== null && _this$_cellKeyToChild3 !== void 0 ? _this$_cellKeyToChild3 : [];\n    for (var _iterator4 = (0, _createForOfIteratorHelperLoose2.default)(listSet), _step4; !(_step4 = _iterator4()).done;) {\n      var list = _step4.value;\n      if (fn(list)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  size() {\n    return this._childrenToCellKey.size;\n  }\n}\nexports.default = ChildListCollection;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,gCAAgC,GAAGJ,sBAAsB,CAACH,OAAO,CAAC,uDAAuD,CAAC,CAAC;AAC/H,IAAIQ,UAAU,GAAGL,sBAAsB,CAACH,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAAC,IAWjES,mBAAmB;EACvB,SAAAA,oBAAA,EAAc;IAAA,IAAAR,gBAAA,CAAAG,OAAA,QAAAK,mBAAA;IACZ,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnC,IAAI,CAACC,kBAAkB,GAAG,IAAID,GAAG,CAAC,CAAC;EACrC;EAAC,WAAAT,aAAA,CAAAE,OAAA,EAAAK,mBAAA;IAAAI,GAAA;IAAAC,KAAA,EACD,SAAAC,GAAGA,CAACC,IAAI,EAAEC,OAAO,EAAE;MACjB,IAAIC,qBAAqB;MACzB,CAAC,CAAC,EAAEV,UAAU,CAACJ,OAAO,EAAE,CAAC,IAAI,CAACQ,kBAAkB,CAACO,GAAG,CAACH,IAAI,CAAC,EAAE,0CAA0C,CAAC;MACvG,IAAII,SAAS,GAAG,CAACF,qBAAqB,GAAG,IAAI,CAACR,kBAAkB,CAACW,GAAG,CAACJ,OAAO,CAAC,MAAM,IAAI,IAAIC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,IAAII,GAAG,CAAC,CAAC;MAC/JF,SAAS,CAACL,GAAG,CAACC,IAAI,CAAC;MACnB,IAAI,CAACN,kBAAkB,CAACa,GAAG,CAACN,OAAO,EAAEG,SAAS,CAAC;MAC/C,IAAI,CAACR,kBAAkB,CAACW,GAAG,CAACP,IAAI,EAAEC,OAAO,CAAC;IAC5C;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAU,MAAMA,CAACR,IAAI,EAAE;MACX,IAAIC,OAAO,GAAG,IAAI,CAACL,kBAAkB,CAACS,GAAG,CAACL,IAAI,CAAC;MAC/C,CAAC,CAAC,EAAER,UAAU,CAACJ,OAAO,EAAEa,OAAO,IAAI,IAAI,EAAE,yCAAyC,CAAC;MACnF,IAAI,CAACL,kBAAkB,CAACa,MAAM,CAACT,IAAI,CAAC;MACpC,IAAII,SAAS,GAAG,IAAI,CAACV,kBAAkB,CAACW,GAAG,CAACJ,OAAO,CAAC;MACpD,CAAC,CAAC,EAAET,UAAU,CAACJ,OAAO,EAAEgB,SAAS,EAAE,2CAA2C,CAAC;MAC/EA,SAAS,CAACK,MAAM,CAACT,IAAI,CAAC;MACtB,IAAII,SAAS,CAACM,IAAI,KAAK,CAAC,EAAE;QACxB,IAAI,CAAChB,kBAAkB,CAACe,MAAM,CAACR,OAAO,CAAC;MACzC;IACF;EAAC;IAAAJ,GAAA;IAAAC,KAAA,EACD,SAAAa,OAAOA,CAACC,EAAE,EAAE;MACV,KAAK,IAAIC,SAAS,GAAG,CAAC,CAAC,EAAEtB,gCAAgC,CAACH,OAAO,EAAE,IAAI,CAACM,kBAAkB,CAACoB,MAAM,CAAC,CAAC,CAAC,EAAEC,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGF,SAAS,CAAC,CAAC,EAAEG,IAAI,GAAG;QACzI,IAAIC,OAAO,GAAGF,KAAK,CAACjB,KAAK;QACzB,KAAK,IAAIoB,UAAU,GAAG,CAAC,CAAC,EAAE3B,gCAAgC,CAACH,OAAO,EAAE6B,OAAO,CAAC,EAAEE,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEF,IAAI,GAAG;UACpH,IAAIhB,IAAI,GAAGmB,MAAM,CAACrB,KAAK;UACvBc,EAAE,CAACZ,IAAI,CAAC;QACV;MACF;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAsB,aAAaA,CAACnB,OAAO,EAAEW,EAAE,EAAE;MACzB,IAAIS,sBAAsB;MAC1B,IAAIJ,OAAO,GAAG,CAACI,sBAAsB,GAAG,IAAI,CAAC3B,kBAAkB,CAACW,GAAG,CAACJ,OAAO,CAAC,MAAM,IAAI,IAAIoB,sBAAsB,KAAK,KAAK,CAAC,GAAGA,sBAAsB,GAAG,EAAE;MACzJ,KAAK,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAE/B,gCAAgC,CAACH,OAAO,EAAE6B,OAAO,CAAC,EAAEM,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEN,IAAI,GAAG;QACpH,IAAIhB,IAAI,GAAGuB,MAAM,CAACzB,KAAK;QACvBc,EAAE,CAACZ,IAAI,CAAC;MACV;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAA0B,SAASA,CAACvB,OAAO,EAAEW,EAAE,EAAE;MACrB,IAAIa,sBAAsB;MAC1B,IAAIR,OAAO,GAAG,CAACQ,sBAAsB,GAAG,IAAI,CAAC/B,kBAAkB,CAACW,GAAG,CAACJ,OAAO,CAAC,MAAM,IAAI,IAAIwB,sBAAsB,KAAK,KAAK,CAAC,GAAGA,sBAAsB,GAAG,EAAE;MACzJ,KAAK,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAEnC,gCAAgC,CAACH,OAAO,EAAE6B,OAAO,CAAC,EAAEU,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEV,IAAI,GAAG;QACpH,IAAIhB,IAAI,GAAG2B,MAAM,CAAC7B,KAAK;QACvB,IAAIc,EAAE,CAACZ,IAAI,CAAC,EAAE;UACZ,OAAO,IAAI;QACb;MACF;MACA,OAAO,KAAK;IACd;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAY,IAAIA,CAAA,EAAG;MACL,OAAO,IAAI,CAACd,kBAAkB,CAACc,IAAI;IACrC;EAAC;AAAA;AAEHrB,OAAO,CAACD,OAAO,GAAGK,mBAAmB;AACrCmC,MAAM,CAACvC,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}