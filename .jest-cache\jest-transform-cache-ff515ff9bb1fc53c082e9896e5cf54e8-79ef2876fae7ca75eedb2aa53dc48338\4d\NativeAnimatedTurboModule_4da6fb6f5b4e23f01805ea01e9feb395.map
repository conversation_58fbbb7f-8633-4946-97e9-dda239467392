{"version": 3, "names": ["_interopRequireWildcard", "require", "default", "exports", "__esModule", "TurboModuleRegistry", "_default", "get", "module"], "sources": ["NativeAnimatedTurboModule.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar TurboModuleRegistry = _interopRequireWildcard(require(\"../TurboModule/TurboModuleRegistry\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n// The config has different keys depending on the type of the Node\n// TODO(*********): Make these types strict\nvar _default = exports.default = TurboModuleRegistry.get('NativeAnimatedTurboModule');\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,mBAAmB,GAAGL,uBAAuB,CAACC,OAAO,qCAAqC,CAAC,CAAC;AAYhG,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,mBAAmB,CAACE,GAAG,CAAC,2BAA2B,CAAC;AACrFC,MAAM,CAACL,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}