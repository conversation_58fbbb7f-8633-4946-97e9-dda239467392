{"version": 3, "names": ["exports", "__esModule", "default", "emptyFunction", "<PERSON><PERSON><PERSON><PERSON>", "exitApp", "addEventListener", "console", "error", "remove", "removeEventListener", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction emptyFunction() {}\nvar BackHandler = {\n  exitApp: emptyFunction,\n  addEventListener() {\n    console.error('BackHandler is not supported on web and should not be used.');\n    return {\n      remove: emptyFunction\n    };\n  },\n  removeEventListener: emptyFunction\n};\nvar _default = exports.default = BackHandler;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAWxB,SAASC,aAAaA,CAAA,EAAG,CAAC;AAC1B,IAAIC,WAAW,GAAG;EAChBC,OAAO,EAAEF,aAAa;EACtBG,gBAAgB,WAAhBA,gBAAgBA,CAAA,EAAG;IACjBC,OAAO,CAACC,KAAK,CAAC,6DAA6D,CAAC;IAC5E,OAAO;MACLC,MAAM,EAAEN;IACV,CAAC;EACH,CAAC;EACDO,mBAAmB,EAAEP;AACvB,CAAC;AACD,IAAIQ,QAAQ,GAAGX,OAAO,CAACE,OAAO,GAAGE,WAAW;AAC5CQ,MAAM,CAACZ,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}