0f2511af92bf52e9213dad8947087b28
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _TouchableOpacity = _interopRequireDefault(require("../TouchableOpacity"));
var _Text = _interopRequireDefault(require("../Text"));
var Button = React.forwardRef(function (props, forwardedRef) {
  var accessibilityLabel = props.accessibilityLabel,
    color = props.color,
    disabled = props.disabled,
    onPress = props.onPress,
    testID = props.testID,
    title = props.title;
  return React.createElement(_TouchableOpacity.default, {
    accessibilityLabel: accessibilityLabel,
    accessibilityRole: "button",
    disabled: disabled,
    focusable: !disabled,
    onPress: onPress,
    ref: forwardedRef,
    style: [styles.button, color && {
      backgroundColor: color
    }, disabled && styles.buttonDisabled],
    testID: testID
  }, React.createElement(_Text.default, {
    style: [styles.text, disabled && styles.textDisabled]
  }, title));
});
Button.displayName = 'Button';
var styles = _StyleSheet.default.create({
  button: {
    backgroundColor: '#2196F3',
    borderRadius: 2
  },
  text: {
    color: '#fff',
    fontWeight: '500',
    padding: 8,
    textAlign: 'center',
    textTransform: 'uppercase'
  },
  buttonDisabled: {
    backgroundColor: '#dfdfdf'
  },
  textDisabled: {
    color: '#a1a1a1'
  }
});
var _default = exports.default = Button;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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