{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_dangerousStyleValue", "setValueForStyles", "node", "styles", "style", "styleName", "hasOwnProperty", "isCustomProperty", "indexOf", "styleValue", "setProperty", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _dangerousStyleValue = _interopRequireDefault(require(\"./dangerousStyleValue\"));\n/* eslint-disable */\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * From React 16.3.0\n * \n */\n\n/**\n * Sets the value for multiple styles on a node.  If a value is specified as\n * '' (empty string), the corresponding style property will be unset.\n *\n * @param {DOMElement} node\n * @param {object} styles\n */\nfunction setValueForStyles(node, styles) {\n  var style = node.style;\n  for (var styleName in styles) {\n    if (!styles.hasOwnProperty(styleName)) {\n      continue;\n    }\n    var isCustomProperty = styleName.indexOf('--') === 0;\n    var styleValue = (0, _dangerousStyleValue.default)(styleName, styles[styleName], isCustomProperty);\n    if (styleName === 'float') {\n      styleName = 'cssFloat';\n    }\n    if (isCustomProperty) {\n      style.setProperty(styleName, styleValue);\n    } else {\n      style[styleName] = styleValue;\n    }\n  }\n}\nvar _default = exports.default = setValueForStyles;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,oBAAoB,GAAGL,sBAAsB,CAACC,OAAO,wBAAwB,CAAC,CAAC;AAoBnF,SAASK,iBAAiBA,CAACC,IAAI,EAAEC,MAAM,EAAE;EACvC,IAAIC,KAAK,GAAGF,IAAI,CAACE,KAAK;EACtB,KAAK,IAAIC,SAAS,IAAIF,MAAM,EAAE;IAC5B,IAAI,CAACA,MAAM,CAACG,cAAc,CAACD,SAAS,CAAC,EAAE;MACrC;IACF;IACA,IAAIE,gBAAgB,GAAGF,SAAS,CAACG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC;IACpD,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAET,oBAAoB,CAACH,OAAO,EAAEQ,SAAS,EAAEF,MAAM,CAACE,SAAS,CAAC,EAAEE,gBAAgB,CAAC;IAClG,IAAIF,SAAS,KAAK,OAAO,EAAE;MACzBA,SAAS,GAAG,UAAU;IACxB;IACA,IAAIE,gBAAgB,EAAE;MACpBH,KAAK,CAACM,WAAW,CAACL,SAAS,EAAEI,UAAU,CAAC;IAC1C,CAAC,MAAM;MACLL,KAAK,CAACC,SAAS,CAAC,GAAGI,UAAU;IAC/B;EACF;AACF;AACA,IAAIE,QAAQ,GAAGb,OAAO,CAACD,OAAO,GAAGI,iBAAiB;AAClDW,MAAM,CAACd,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}