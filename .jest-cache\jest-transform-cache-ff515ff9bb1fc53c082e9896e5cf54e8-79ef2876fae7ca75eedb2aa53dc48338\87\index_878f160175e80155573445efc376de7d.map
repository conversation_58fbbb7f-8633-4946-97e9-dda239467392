{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "React", "_StyleSheet", "_TouchableOpacity", "_Text", "<PERSON><PERSON>", "forwardRef", "props", "forwardedRef", "accessibilityLabel", "color", "disabled", "onPress", "testID", "title", "createElement", "accessibilityRole", "focusable", "ref", "style", "styles", "button", "backgroundColor", "buttonDisabled", "text", "textDisabled", "displayName", "create", "borderRadius", "fontWeight", "padding", "textAlign", "textTransform", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _TouchableOpacity = _interopRequireDefault(require(\"../TouchableOpacity\"));\nvar _Text = _interopRequireDefault(require(\"../Text\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n//import { warnOnce } from '../../modules/warnOnce';\n\nvar Button = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  // warnOnce('Button', 'Button is deprecated. Please use Pressable.');\n\n  var accessibilityLabel = props.accessibilityLabel,\n    color = props.color,\n    disabled = props.disabled,\n    onPress = props.onPress,\n    testID = props.testID,\n    title = props.title;\n  return /*#__PURE__*/React.createElement(_TouchableOpacity.default, {\n    accessibilityLabel: accessibilityLabel,\n    accessibilityRole: \"button\",\n    disabled: disabled,\n    focusable: !disabled,\n    onPress: onPress,\n    ref: forwardedRef,\n    style: [styles.button, color && {\n      backgroundColor: color\n    }, disabled && styles.buttonDisabled],\n    testID: testID\n  }, /*#__PURE__*/React.createElement(_Text.default, {\n    style: [styles.text, disabled && styles.textDisabled]\n  }, title));\n});\nButton.displayName = 'Button';\nvar styles = _StyleSheet.default.create({\n  button: {\n    backgroundColor: '#2196F3',\n    borderRadius: 2\n  },\n  text: {\n    color: '#fff',\n    fontWeight: '500',\n    padding: 8,\n    textAlign: 'center',\n    textTransform: 'uppercase'\n  },\n  buttonDisabled: {\n    backgroundColor: '#dfdfdf'\n  },\n  textDisabled: {\n    color: '#a1a1a1'\n  }\n});\nvar _default = exports.default = Button;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,KAAK,GAAGH,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIM,WAAW,GAAGP,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIO,iBAAiB,GAAGR,sBAAsB,CAACC,OAAO,sBAAsB,CAAC,CAAC;AAC9E,IAAIQ,KAAK,GAAGT,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AAatD,IAAIS,MAAM,GAAgBJ,KAAK,CAACK,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EAGlE,IAAIC,kBAAkB,GAAGF,KAAK,CAACE,kBAAkB;IAC/CC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,OAAO,GAAGL,KAAK,CAACK,OAAO;IACvBC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,KAAK,GAAGP,KAAK,CAACO,KAAK;EACrB,OAAoBb,KAAK,CAACc,aAAa,CAACZ,iBAAiB,CAACN,OAAO,EAAE;IACjEY,kBAAkB,EAAEA,kBAAkB;IACtCO,iBAAiB,EAAE,QAAQ;IAC3BL,QAAQ,EAAEA,QAAQ;IAClBM,SAAS,EAAE,CAACN,QAAQ;IACpBC,OAAO,EAAEA,OAAO;IAChBM,GAAG,EAAEV,YAAY;IACjBW,KAAK,EAAE,CAACC,MAAM,CAACC,MAAM,EAAEX,KAAK,IAAI;MAC9BY,eAAe,EAAEZ;IACnB,CAAC,EAAEC,QAAQ,IAAIS,MAAM,CAACG,cAAc,CAAC;IACrCV,MAAM,EAAEA;EACV,CAAC,EAAeZ,KAAK,CAACc,aAAa,CAACX,KAAK,CAACP,OAAO,EAAE;IACjDsB,KAAK,EAAE,CAACC,MAAM,CAACI,IAAI,EAAEb,QAAQ,IAAIS,MAAM,CAACK,YAAY;EACtD,CAAC,EAAEX,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFT,MAAM,CAACqB,WAAW,GAAG,QAAQ;AAC7B,IAAIN,MAAM,GAAGlB,WAAW,CAACL,OAAO,CAAC8B,MAAM,CAAC;EACtCN,MAAM,EAAE;IACNC,eAAe,EAAE,SAAS;IAC1BM,YAAY,EAAE;EAChB,CAAC;EACDJ,IAAI,EAAE;IACJd,KAAK,EAAE,MAAM;IACbmB,UAAU,EAAE,KAAK;IACjBC,OAAO,EAAE,CAAC;IACVC,SAAS,EAAE,QAAQ;IACnBC,aAAa,EAAE;EACjB,CAAC;EACDT,cAAc,EAAE;IACdD,eAAe,EAAE;EACnB,CAAC;EACDG,YAAY,EAAE;IACZf,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AACF,IAAIuB,QAAQ,GAAGlC,OAAO,CAACF,OAAO,GAAGQ,MAAM;AACvC6B,MAAM,CAACnC,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}