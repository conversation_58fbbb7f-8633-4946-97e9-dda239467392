a7923a95985c4d8a76593864638d440f
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.VirtualizedListCellContextProvider = VirtualizedListCellContextProvider;
exports.VirtualizedListContext = void 0;
exports.VirtualizedListContextProvider = VirtualizedListContextProvider;
exports.VirtualizedListContextResetter = VirtualizedListContextResetter;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var __DEV__ = process.env.NODE_ENV !== 'production';
var VirtualizedListContext = exports.VirtualizedListContext = React.createContext(null);
if (__DEV__) {
  VirtualizedListContext.displayName = 'VirtualizedListContext';
}
function VirtualizedListContextResetter(_ref) {
  var children = _ref.children;
  return React.createElement(VirtualizedListContext.Provider, {
    value: null
  }, children);
}
function VirtualizedListContextProvider(_ref2) {
  var children = _ref2.children,
    value = _ref2.value;
  var context = (0, _react.useMemo)(function () {
    return {
      cellKey: null,
      getScrollMetrics: value.getScrollMetrics,
      horizontal: value.horizontal,
      getOutermostParentListRef: value.getOutermostParentListRef,
      registerAsNestedChild: value.registerAsNestedChild,
      unregisterAsNestedChild: value.unregisterAsNestedChild
    };
  }, [value.getScrollMetrics, value.horizontal, value.getOutermostParentListRef, value.registerAsNestedChild, value.unregisterAsNestedChild]);
  return React.createElement(VirtualizedListContext.Provider, {
    value: context
  }, children);
}
function VirtualizedListCellContextProvider(_ref3) {
  var cellKey = _ref3.cellKey,
    children = _ref3.children;
  var currContext = (0, _react.useContext)(VirtualizedListContext);
  var context = (0, _react.useMemo)(function () {
    return currContext == null ? null : (0, _objectSpread2.default)((0, _objectSpread2.default)({}, currContext), {}, {
      cellKey: cellKey
    });
  }, [currContext, cellKey]);
  return React.createElement(VirtualizedListContext.Provider, {
    value: context
  }, children);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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