dacded1d8528104d791d48274a196d56
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _getBoundingClientRect = _interopRequireDefault(require("../../modules/getBoundingClientRect"));
var _setValueForStyles = _interopRequireDefault(require("../../modules/setValueForStyles"));
var getRect = function getRect(node) {
  var height = node.offsetHeight;
  var width = node.offsetWidth;
  var left = node.offsetLeft;
  var top = node.offsetTop;
  node = node.offsetParent;
  while (node && node.nodeType === 1) {
    left += node.offsetLeft + node.clientLeft - node.scrollLeft;
    top += node.offsetTop + node.clientTop - node.scrollTop;
    node = node.offsetParent;
  }
  top -= window.scrollY;
  left -= window.scrollX;
  return {
    width: width,
    height: height,
    top: top,
    left: left
  };
};
var _measureLayout = function measureLayout(node, relativeToNativeNode, callback) {
  var relativeNode = relativeToNativeNode || node && node.parentNode;
  if (node && relativeNode) {
    setTimeout(function () {
      if (node.isConnected && relativeNode.isConnected) {
        var relativeRect = getRect(relativeNode);
        var _getRect = getRect(node),
          height = _getRect.height,
          left = _getRect.left,
          top = _getRect.top,
          width = _getRect.width;
        var x = left - relativeRect.left;
        var y = top - relativeRect.top;
        callback(x, y, width, height, left, top);
      }
    }, 0);
  }
};
var elementsToIgnore = {
  A: true,
  BODY: true,
  INPUT: true,
  SELECT: true,
  TEXTAREA: true
};
var UIManager = {
  blur: function blur(node) {
    try {
      node.blur();
    } catch (err) {}
  },
  focus: function focus(node) {
    try {
      var name = node.nodeName;
      if (node.getAttribute('tabIndex') == null && node.isContentEditable !== true && elementsToIgnore[name] == null) {
        node.setAttribute('tabIndex', '-1');
      }
      node.focus();
    } catch (err) {}
  },
  measure: function measure(node, callback) {
    _measureLayout(node, null, callback);
  },
  measureInWindow: function measureInWindow(node, callback) {
    if (node) {
      setTimeout(function () {
        var _getBoundingClientRec = (0, _getBoundingClientRect.default)(node),
          height = _getBoundingClientRec.height,
          left = _getBoundingClientRec.left,
          top = _getBoundingClientRec.top,
          width = _getBoundingClientRec.width;
        callback(left, top, width, height);
      }, 0);
    }
  },
  measureLayout: function measureLayout(node, relativeToNativeNode, onFail, onSuccess) {
    _measureLayout(node, relativeToNativeNode, onSuccess);
  },
  updateView: function updateView(node, props) {
    for (var prop in props) {
      if (!Object.prototype.hasOwnProperty.call(props, prop)) {
        continue;
      }
      var value = props[prop];
      switch (prop) {
        case 'style':
          {
            (0, _setValueForStyles.default)(node, value);
            break;
          }
        case 'class':
        case 'className':
          {
            node.setAttribute('class', value);
            break;
          }
        case 'text':
        case 'value':
          node.value = value;
          break;
        default:
          node.setAttribute(prop, value);
      }
    }
  },
  configureNextLayoutAnimation: function configureNextLayoutAnimation(config, onAnimationDidEnd) {
    onAnimationDidEnd();
  },
  setLayoutAnimationEnabledExperimental: function setLayoutAnimationEnabledExperimental() {}
};
var _default = exports.default = UIManager;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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