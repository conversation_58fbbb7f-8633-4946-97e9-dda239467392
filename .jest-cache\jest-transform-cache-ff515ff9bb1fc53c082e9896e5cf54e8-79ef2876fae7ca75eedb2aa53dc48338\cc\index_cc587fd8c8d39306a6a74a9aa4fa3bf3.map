{"version": 3, "names": ["_interopRequireDefault2", "require", "_toConsumableArray2", "_defineProperty2", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_interopRequireWildcard", "_interopRequireDefault", "exports", "__esModule", "_createForOfIteratorHelperLoose2", "_extends2", "_objectSpread2", "_RefreshControl", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_View", "_StyleSheet", "_Batchinator", "_clamp", "_infoLog", "_CellRenderMask", "_ChildListCollection", "_FillRateHelper", "_StateSafePureComponent", "_ViewabilityHelper", "_VirtualizedListCellRenderer", "_VirtualizedListContext", "_VirtualizeUtils", "_invariant", "_nullthrows", "React", "__DEV__", "process", "env", "NODE_ENV", "ON_EDGE_REACHED_EPSILON", "_usedIndexForKey", "_keylessItemComponentName", "horizontalOrDefault", "horizontal", "initialNumToRenderOrDefault", "initialNumToRender", "maxToRenderPerBatchOrDefault", "maxToRenderPerBatch", "onStartReachedThresholdOrDefault", "onStartReachedThreshold", "onEndReachedThresholdOrDefault", "onEndReachedThreshold", "getScrollingThreshold", "threshold", "<PERSON><PERSON><PERSON><PERSON>", "scrollEventThrottleOrDefault", "scrollEventThrottle", "windowSizeOrDefault", "windowSize", "findLastWhere", "arr", "predicate", "i", "length", "VirtualizedList", "_StateSafePureCompone", "_props", "_this2", "_this$props$updateCel", "_getScrollMetrics", "_scrollMetrics", "_getOutermostParentListRef", "_isNestedWithSameOrientation", "context", "getOutermostParentListRef", "_registerAsNestedChild", "childList", "_nestedChildLists", "add", "ref", "cellKey", "_hasInteracted", "recordInteraction", "_unregisterAsNestedChild", "remove", "_onUpdateSeparators", "keys", "newProps", "for<PERSON>ach", "key", "_cellRefs", "updateSeparatorProps", "_getSpace<PERSON><PERSON><PERSON>", "isVertical", "_averageCellLength", "_frames", "_footer<PERSON><PERSON>th", "_hasTriggeredInitialScrollToIndex", "_hasMore", "_hasWarned", "_headerLength", "_hiPriInProgress", "_highestMeasuredFrameIndex", "_indicesToKeys", "Map", "_lastFocused<PERSON>ell<PERSON>ey", "_offsetFromParentVirtualizedList", "_prevParentOffset", "contentLength", "dOffset", "dt", "offset", "timestamp", "velocity", "zoomScale", "_scrollRef", "_sentStartForContentLength", "_sentEndForContentLength", "_totalCellLength", "_totalCellsMeasured", "_viewabilityTuples", "_captureScrollRef", "_defaultRenderScrollComponent", "props", "onRefresh", "createElement", "_props$refreshing", "refreshing", "JSON", "stringify", "refreshControl", "progressViewOffset", "_onCellLayout", "index", "layout", "nativeEvent", "next", "_selectOffset", "_selectLength", "inLayout", "curr", "Math", "max", "_scheduleCellsToRenderUpdate", "_triggerRemeasureForChildListsInCell", "_computeBlankness", "_updateViewableItems", "state", "cellsAroundViewport", "_onCellUnmount", "_onLayout", "measureLayoutRelativeToContainingList", "onLayout", "_maybe<PERSON>all<PERSON>n<PERSON>dgeReached", "_onLayoutEmpty", "_onLayoutFooter", "_getFooter<PERSON><PERSON><PERSON><PERSON>", "_onLayoutHeader", "_onContentSizeChange", "width", "height", "initialScrollIndex", "contentOffset", "getItemCount", "data", "scrollToIndex", "animated", "scrollToEnd", "onContentSizeChange", "_convertParentScrollMetrics", "metrics", "_onScroll", "onScroll", "timeStamp", "layoutMeasurement", "contentSize", "_this$_convertParentS", "perf", "prevDt", "_fillRate<PERSON><PERSON><PERSON>", "activate", "_onScrollBeginDrag", "tuple", "viewabilityHelper", "onScrollBeginDrag", "_onScrollEndDrag", "onScrollEndDrag", "_onMomentumScrollBegin", "onMomentumScrollBegin", "_onMomentumScrollEnd", "onMomentumScrollEnd", "_updateCellsToRender", "setState", "_adjustCellsAroundViewport", "renderMask", "_createRenderMask", "_getNonViewportRenderRegions", "first", "last", "equals", "_createViewToken", "isViewable", "getItem", "item", "_keyExtractor", "_getOffsetApprox", "Number", "isInteger", "__getFrameMetricsApprox", "frameMetrics", "floor", "remainder", "frame", "_getFrameMetrics", "getItemLayout", "lastFoc<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "focusedCellIndex", "itemCount", "heightOfCellsBeforeFocused", "heightOfCellsAfterFocused", "_i", "_checkProps", "_updateCellsToRenderBatcher", "updateCellsBatchingPeriod", "viewabilityConfigCallbackPairs", "map", "pair", "viewabilityConfig", "onViewableItemsChanged", "_this$props3", "push", "initialRenderRegion", "_initialRenderRegion", "invertedWheelEventHandler", "ev", "scrollOffset", "target", "scrollLeft", "scrollTop", "<PERSON><PERSON><PERSON><PERSON>", "scrollWidth", "scrollHeight", "clientLength", "clientWidth", "clientHeight", "isEventTargetScrollable", "delta", "deltaX", "wheelDeltaX", "deltaY", "wheelDeltaY", "leftoverDel<PERSON>", "min", "targetDel<PERSON>", "inverted", "getScrollableNode", "node", "nextScrollLeft", "nextScrollTop", "preventDefault", "value", "params", "veryLast", "scrollTo", "console", "warn", "x", "y", "_this$props", "onScrollToIndexFailed", "viewOffset", "viewPosition", "averageItemLength", "highestMeasuredFrameIndex", "scrollToItem", "_this$props2", "_index", "scrollToOffset", "flashScrollIndicators", "getScrollResponder", "getScrollRef", "_get<PERSON><PERSON><PERSON><PERSON>", "_this$context", "hasMore", "__isNative", "flexWrap", "flatStyles", "flatten", "contentContainerStyle", "_this$_scrollMetrics", "distanceFromEnd", "_constrainToItemCount", "newCellsAroundViewport", "disableVirtualization", "renderAhead", "abs", "EPSILON", "computeWindowedRenderLimits", "size", "childIdx", "_findFirstChildWithMore", "ii", "cellKeyForIndex", "get", "anyInCell", "componentDidMount", "registerAsNestedChild", "setupWebWheelHandler", "componentWillUnmount", "unregisterAsNestedChild", "dispose", "abort", "deactivateAndFlush", "teardownWebWheelHandler", "_this3", "addEventListener", "setTimeout", "removeEventListener", "_pushCells", "cells", "stickyHeaderIndices", "stickyIndicesFromProps", "inversionStyle", "_this", "_this$props4", "CellRendererComponent", "ItemSeparatorComponent", "ListHeaderComponent", "ListItemComponent", "debug", "renderItem", "stickyOffset", "end", "prevCell<PERSON>ey", "_loop", "set", "has", "shouldListenForLayout", "enabled", "undefined", "onUpdateSeparators", "onCellFocusCapture", "_onCellFocusCapture", "onUnmount", "_ref", "onCellLayout", "nested<PERSON><PERSON><PERSON><PERSON>", "keyExtractor", "String", "type", "displayName", "render", "_this4", "_this$props5", "ListEmptyComponent", "ListFooterComponent", "_this$props6", "styles", "horizontallyInverted", "verticallyInverted", "Set", "_element", "isValidElement", "VirtualizedListCellContextProvider", "style", "ListHeaderComponentStyle", "_element2", "cloneElement", "event", "<PERSON><PERSON><PERSON><PERSON>", "renderRegions", "enumerateRegions", "lastSpacer", "r", "isSpacer", "_iterator", "_step", "done", "section", "isLastSpacer", "constrainToMeasured", "firstMetrics", "lastMetrics", "spacerSize", "_element3", "ListFooterComponentStyle", "scrollProps", "invertStickyHeaders", "innerRet", "VirtualizedListContextProvider", "getScrollMetrics", "renderScrollComponent", "ret", "_renderDebugOverlay", "componentDidUpdate", "prevProps", "_this$props7", "extraData", "resetViewableIndices", "hiPriInProgress", "computeBlankness", "forEachInCell", "_this5", "measureLayout", "scrollMetrics", "metricsChanged", "error", "stack", "normalize", "framesInLayout", "windowTop", "frameLast", "windowLen", "visTop", "visLen", "debugOverlayBase", "debugOverlay", "f", "debugOverlayFrame", "top", "debugOverlayFrameLast", "debugOverlayFrameVis", "_this$props8", "onStartReached", "onEndReached", "_this$_scrollMetrics2", "distanceFromStart", "DEFAULT_THRESHOLD_PX", "startThreshold", "endThreshold", "isWithinStartThreshold", "isWithinEndThreshold", "_this$state$cellsArou", "_this$_scrollMetrics3", "<PERSON><PERSON><PERSON>", "distTop", "distBottom", "schedule", "_this6", "onUpdate", "additionalRegions", "CellRenderMask", "allRegions", "concat", "_i2", "_allRegions", "region", "add<PERSON>ells", "initialRegion", "stickyIndicesSet", "_ensureClosestStickyHeader", "_props$initialScrollI", "firstCellIndex", "lastCellIndex", "cellIdx", "itemIdx", "getDerivedStateFromProps", "prevState", "num<PERSON>ells", "constrained<PERSON><PERSON><PERSON>", "contextType", "VirtualizedListContext", "create", "transform", "flex", "position", "right", "bottom", "borderColor", "borderWidth", "left", "backgroundColor", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _createForOfIteratorHelperLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createForOfIteratorHelperLoose\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _RefreshControl = _interopRequireDefault(require(\"../../../exports/RefreshControl\"));\nvar _ScrollView = _interopRequireDefault(require(\"../../../exports/ScrollView\"));\nvar _View = _interopRequireDefault(require(\"../../../exports/View\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../../../exports/StyleSheet\"));\nvar _Batchinator = _interopRequireDefault(require(\"../Batchinator\"));\nvar _clamp = _interopRequireDefault(require(\"../Utilities/clamp\"));\nvar _infoLog = _interopRequireDefault(require(\"../infoLog\"));\nvar _CellRenderMask = require(\"./CellRenderMask\");\nvar _ChildListCollection = _interopRequireDefault(require(\"./ChildListCollection\"));\nvar _FillRateHelper = _interopRequireDefault(require(\"../FillRateHelper\"));\nvar _StateSafePureComponent = _interopRequireDefault(require(\"./StateSafePureComponent\"));\nvar _ViewabilityHelper = _interopRequireDefault(require(\"../ViewabilityHelper\"));\nvar _VirtualizedListCellRenderer = _interopRequireDefault(require(\"./VirtualizedListCellRenderer\"));\nvar _VirtualizedListContext = require(\"./VirtualizedListContext.js\");\nvar _VirtualizeUtils = require(\"../VirtualizeUtils\");\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _nullthrows = _interopRequireDefault(require(\"nullthrows\"));\nvar React = _interopRequireWildcard(require(\"react\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nvar ON_EDGE_REACHED_EPSILON = 0.001;\nvar _usedIndexForKey = false;\nvar _keylessItemComponentName = '';\n/**\n * Default Props Helper Functions\n * Use the following helper functions for default values\n */\n\n// horizontalOrDefault(this.props.horizontal)\nfunction horizontalOrDefault(horizontal) {\n  return horizontal !== null && horizontal !== void 0 ? horizontal : false;\n}\n\n// initialNumToRenderOrDefault(this.props.initialNumToRender)\nfunction initialNumToRenderOrDefault(initialNumToRender) {\n  return initialNumToRender !== null && initialNumToRender !== void 0 ? initialNumToRender : 10;\n}\n\n// maxToRenderPerBatchOrDefault(this.props.maxToRenderPerBatch)\nfunction maxToRenderPerBatchOrDefault(maxToRenderPerBatch) {\n  return maxToRenderPerBatch !== null && maxToRenderPerBatch !== void 0 ? maxToRenderPerBatch : 10;\n}\n\n// onStartReachedThresholdOrDefault(this.props.onStartReachedThreshold)\nfunction onStartReachedThresholdOrDefault(onStartReachedThreshold) {\n  return onStartReachedThreshold !== null && onStartReachedThreshold !== void 0 ? onStartReachedThreshold : 2;\n}\n\n// onEndReachedThresholdOrDefault(this.props.onEndReachedThreshold)\nfunction onEndReachedThresholdOrDefault(onEndReachedThreshold) {\n  return onEndReachedThreshold !== null && onEndReachedThreshold !== void 0 ? onEndReachedThreshold : 2;\n}\n\n// getScrollingThreshold(visibleLength, onEndReachedThreshold)\nfunction getScrollingThreshold(threshold, visibleLength) {\n  return threshold * visibleLength / 2;\n}\n\n// scrollEventThrottleOrDefault(this.props.scrollEventThrottle)\nfunction scrollEventThrottleOrDefault(scrollEventThrottle) {\n  return scrollEventThrottle !== null && scrollEventThrottle !== void 0 ? scrollEventThrottle : 50;\n}\n\n// windowSizeOrDefault(this.props.windowSize)\nfunction windowSizeOrDefault(windowSize) {\n  return windowSize !== null && windowSize !== void 0 ? windowSize : 21;\n}\nfunction findLastWhere(arr, predicate) {\n  for (var i = arr.length - 1; i >= 0; i--) {\n    if (predicate(arr[i])) {\n      return arr[i];\n    }\n  }\n  return null;\n}\n\n/**\n * Base implementation for the more convenient [`<FlatList>`](https://reactnative.dev/docs/flatlist)\n * and [`<SectionList>`](https://reactnative.dev/docs/sectionlist) components, which are also better\n * documented. In general, this should only really be used if you need more flexibility than\n * `FlatList` provides, e.g. for use with immutable data instead of plain arrays.\n *\n * Virtualization massively improves memory consumption and performance of large lists by\n * maintaining a finite render window of active items and replacing all items outside of the render\n * window with appropriately sized blank space. The window adapts to scrolling behavior, and items\n * are rendered incrementally with low-pri (after any running interactions) if they are far from the\n * visible area, or with hi-pri otherwise to minimize the potential of seeing blank space.\n *\n * Some caveats:\n *\n * - Internal state is not preserved when content scrolls out of the render window. Make sure all\n *   your data is captured in the item data or external stores like Flux, Redux, or Relay.\n * - This is a `PureComponent` which means that it will not re-render if `props` remain shallow-\n *   equal. Make sure that everything your `renderItem` function depends on is passed as a prop\n *   (e.g. `extraData`) that is not `===` after updates, otherwise your UI may not update on\n *   changes. This includes the `data` prop and parent component state.\n * - In order to constrain memory and enable smooth scrolling, content is rendered asynchronously\n *   offscreen. This means it's possible to scroll faster than the fill rate ands momentarily see\n *   blank content. This is a tradeoff that can be adjusted to suit the needs of each application,\n *   and we are working on improving it behind the scenes.\n * - By default, the list looks for a `key` or `id` prop on each item and uses that for the React key.\n *   Alternatively, you can provide a custom `keyExtractor` prop.\n * - As an effort to remove defaultProps, use helper functions when referencing certain props\n *\n */\nclass VirtualizedList extends _StateSafePureComponent.default {\n  // scrollToEnd may be janky without getItemLayout prop\n  scrollToEnd(params) {\n    var animated = params ? params.animated : true;\n    var veryLast = this.props.getItemCount(this.props.data) - 1;\n    if (veryLast < 0) {\n      return;\n    }\n    var frame = this.__getFrameMetricsApprox(veryLast, this.props);\n    var offset = Math.max(0, frame.offset + frame.length + this._footerLength - this._scrollMetrics.visibleLength);\n    if (this._scrollRef == null) {\n      return;\n    }\n    if (this._scrollRef.scrollTo == null) {\n      console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n      return;\n    }\n    this._scrollRef.scrollTo(horizontalOrDefault(this.props.horizontal) ? {\n      x: offset,\n      animated\n    } : {\n      y: offset,\n      animated\n    });\n  }\n\n  // scrollToIndex may be janky without getItemLayout prop\n  scrollToIndex(params) {\n    var _this$props = this.props,\n      data = _this$props.data,\n      horizontal = _this$props.horizontal,\n      getItemCount = _this$props.getItemCount,\n      getItemLayout = _this$props.getItemLayout,\n      onScrollToIndexFailed = _this$props.onScrollToIndexFailed;\n    var animated = params.animated,\n      index = params.index,\n      viewOffset = params.viewOffset,\n      viewPosition = params.viewPosition;\n    (0, _invariant.default)(index >= 0, \"scrollToIndex out of range: requested index \" + index + \" but minimum is 0\");\n    (0, _invariant.default)(getItemCount(data) >= 1, \"scrollToIndex out of range: item length \" + getItemCount(data) + \" but minimum is 1\");\n    (0, _invariant.default)(index < getItemCount(data), \"scrollToIndex out of range: requested index \" + index + \" is out of 0 to \" + (getItemCount(data) - 1));\n    if (!getItemLayout && index > this._highestMeasuredFrameIndex) {\n      (0, _invariant.default)(!!onScrollToIndexFailed, 'scrollToIndex should be used in conjunction with getItemLayout or onScrollToIndexFailed, ' + 'otherwise there is no way to know the location of offscreen indices or handle failures.');\n      onScrollToIndexFailed({\n        averageItemLength: this._averageCellLength,\n        highestMeasuredFrameIndex: this._highestMeasuredFrameIndex,\n        index\n      });\n      return;\n    }\n    var frame = this.__getFrameMetricsApprox(Math.floor(index), this.props);\n    var offset = Math.max(0, this._getOffsetApprox(index, this.props) - (viewPosition || 0) * (this._scrollMetrics.visibleLength - frame.length)) - (viewOffset || 0);\n    if (this._scrollRef == null) {\n      return;\n    }\n    if (this._scrollRef.scrollTo == null) {\n      console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n      return;\n    }\n    this._scrollRef.scrollTo(horizontal ? {\n      x: offset,\n      animated\n    } : {\n      y: offset,\n      animated\n    });\n  }\n\n  // scrollToItem may be janky without getItemLayout prop. Required linear scan through items -\n  // use scrollToIndex instead if possible.\n  scrollToItem(params) {\n    var item = params.item;\n    var _this$props2 = this.props,\n      data = _this$props2.data,\n      getItem = _this$props2.getItem,\n      getItemCount = _this$props2.getItemCount;\n    var itemCount = getItemCount(data);\n    for (var _index = 0; _index < itemCount; _index++) {\n      if (getItem(data, _index) === item) {\n        this.scrollToIndex((0, _objectSpread2.default)((0, _objectSpread2.default)({}, params), {}, {\n          index: _index\n        }));\n        break;\n      }\n    }\n  }\n\n  /**\n   * Scroll to a specific content pixel offset in the list.\n   *\n   * Param `offset` expects the offset to scroll to.\n   * In case of `horizontal` is true, the offset is the x-value,\n   * in any other case the offset is the y-value.\n   *\n   * Param `animated` (`true` by default) defines whether the list\n   * should do an animation while scrolling.\n   */\n  scrollToOffset(params) {\n    var animated = params.animated,\n      offset = params.offset;\n    if (this._scrollRef == null) {\n      return;\n    }\n    if (this._scrollRef.scrollTo == null) {\n      console.warn('No scrollTo method provided. This may be because you have two nested ' + 'VirtualizedLists with the same orientation, or because you are ' + 'using a custom component that does not implement scrollTo.');\n      return;\n    }\n    this._scrollRef.scrollTo(horizontalOrDefault(this.props.horizontal) ? {\n      x: offset,\n      animated\n    } : {\n      y: offset,\n      animated\n    });\n  }\n  recordInteraction() {\n    this._nestedChildLists.forEach(childList => {\n      childList.recordInteraction();\n    });\n    this._viewabilityTuples.forEach(t => {\n      t.viewabilityHelper.recordInteraction();\n    });\n    this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n  }\n  flashScrollIndicators() {\n    if (this._scrollRef == null) {\n      return;\n    }\n    this._scrollRef.flashScrollIndicators();\n  }\n\n  /**\n   * Provides a handle to the underlying scroll responder.\n   * Note that `this._scrollRef` might not be a `ScrollView`, so we\n   * need to check that it responds to `getScrollResponder` before calling it.\n   */\n  getScrollResponder() {\n    if (this._scrollRef && this._scrollRef.getScrollResponder) {\n      return this._scrollRef.getScrollResponder();\n    }\n  }\n  getScrollableNode() {\n    if (this._scrollRef && this._scrollRef.getScrollableNode) {\n      return this._scrollRef.getScrollableNode();\n    } else {\n      return this._scrollRef;\n    }\n  }\n  getScrollRef() {\n    if (this._scrollRef && this._scrollRef.getScrollRef) {\n      return this._scrollRef.getScrollRef();\n    } else {\n      return this._scrollRef;\n    }\n  }\n  _getCellKey() {\n    var _this$context;\n    return ((_this$context = this.context) == null ? void 0 : _this$context.cellKey) || 'rootList';\n  }\n\n  // $FlowFixMe[missing-local-annot]\n\n  hasMore() {\n    return this._hasMore;\n  }\n\n  // $FlowFixMe[missing-local-annot]\n\n  // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n\n  constructor(_props) {\n    var _this$props$updateCel;\n    super(_props);\n    this._getScrollMetrics = () => {\n      return this._scrollMetrics;\n    };\n    this._getOutermostParentListRef = () => {\n      if (this._isNestedWithSameOrientation()) {\n        return this.context.getOutermostParentListRef();\n      } else {\n        return this;\n      }\n    };\n    this._registerAsNestedChild = childList => {\n      this._nestedChildLists.add(childList.ref, childList.cellKey);\n      if (this._hasInteracted) {\n        childList.ref.recordInteraction();\n      }\n    };\n    this._unregisterAsNestedChild = childList => {\n      this._nestedChildLists.remove(childList.ref);\n    };\n    this._onUpdateSeparators = (keys, newProps) => {\n      keys.forEach(key => {\n        var ref = key != null && this._cellRefs[key];\n        ref && ref.updateSeparatorProps(newProps);\n      });\n    };\n    this._getSpacerKey = isVertical => isVertical ? 'height' : 'width';\n    this._averageCellLength = 0;\n    this._cellRefs = {};\n    this._frames = {};\n    this._footerLength = 0;\n    this._hasTriggeredInitialScrollToIndex = false;\n    this._hasInteracted = false;\n    this._hasMore = false;\n    this._hasWarned = {};\n    this._headerLength = 0;\n    this._hiPriInProgress = false;\n    this._highestMeasuredFrameIndex = 0;\n    this._indicesToKeys = new Map();\n    this._lastFocusedCellKey = null;\n    this._nestedChildLists = new _ChildListCollection.default();\n    this._offsetFromParentVirtualizedList = 0;\n    this._prevParentOffset = 0;\n    this._scrollMetrics = {\n      contentLength: 0,\n      dOffset: 0,\n      dt: 10,\n      offset: 0,\n      timestamp: 0,\n      velocity: 0,\n      visibleLength: 0,\n      zoomScale: 1\n    };\n    this._scrollRef = null;\n    this._sentStartForContentLength = 0;\n    this._sentEndForContentLength = 0;\n    this._totalCellLength = 0;\n    this._totalCellsMeasured = 0;\n    this._viewabilityTuples = [];\n    this._captureScrollRef = ref => {\n      this._scrollRef = ref;\n    };\n    this._defaultRenderScrollComponent = props => {\n      var onRefresh = props.onRefresh;\n      if (this._isNestedWithSameOrientation()) {\n        // $FlowFixMe[prop-missing] - Typing ReactNativeComponent revealed errors\n        return /*#__PURE__*/React.createElement(_View.default, props);\n      } else if (onRefresh) {\n        var _props$refreshing;\n        (0, _invariant.default)(typeof props.refreshing === 'boolean', '`refreshing` prop must be set as a boolean in order to use `onRefresh`, but got `' + JSON.stringify((_props$refreshing = props.refreshing) !== null && _props$refreshing !== void 0 ? _props$refreshing : 'undefined') + '`');\n        return (\n          /*#__PURE__*/\n          // $FlowFixMe[prop-missing] Invalid prop usage\n          // $FlowFixMe[incompatible-use]\n          React.createElement(_ScrollView.default, (0, _extends2.default)({}, props, {\n            refreshControl: props.refreshControl == null ? /*#__PURE__*/React.createElement(_RefreshControl.default\n            // $FlowFixMe[incompatible-type]\n            , {\n              refreshing: props.refreshing,\n              onRefresh: onRefresh,\n              progressViewOffset: props.progressViewOffset\n            }) : props.refreshControl\n          }))\n        );\n      } else {\n        // $FlowFixMe[prop-missing] Invalid prop usage\n        // $FlowFixMe[incompatible-use]\n        return /*#__PURE__*/React.createElement(_ScrollView.default, props);\n      }\n    };\n    this._onCellLayout = (e, cellKey, index) => {\n      var layout = e.nativeEvent.layout;\n      var next = {\n        offset: this._selectOffset(layout),\n        length: this._selectLength(layout),\n        index,\n        inLayout: true\n      };\n      var curr = this._frames[cellKey];\n      if (!curr || next.offset !== curr.offset || next.length !== curr.length || index !== curr.index) {\n        this._totalCellLength += next.length - (curr ? curr.length : 0);\n        this._totalCellsMeasured += curr ? 0 : 1;\n        this._averageCellLength = this._totalCellLength / this._totalCellsMeasured;\n        this._frames[cellKey] = next;\n        this._highestMeasuredFrameIndex = Math.max(this._highestMeasuredFrameIndex, index);\n        this._scheduleCellsToRenderUpdate();\n      } else {\n        this._frames[cellKey].inLayout = true;\n      }\n      this._triggerRemeasureForChildListsInCell(cellKey);\n      this._computeBlankness();\n      this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n    };\n    this._onCellUnmount = cellKey => {\n      delete this._cellRefs[cellKey];\n      var curr = this._frames[cellKey];\n      if (curr) {\n        this._frames[cellKey] = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, curr), {}, {\n          inLayout: false\n        });\n      }\n    };\n    this._onLayout = e => {\n      if (this._isNestedWithSameOrientation()) {\n        // Need to adjust our scroll metrics to be relative to our containing\n        // VirtualizedList before we can make claims about list item viewability\n        this.measureLayoutRelativeToContainingList();\n      } else {\n        this._scrollMetrics.visibleLength = this._selectLength(e.nativeEvent.layout);\n      }\n      this.props.onLayout && this.props.onLayout(e);\n      this._scheduleCellsToRenderUpdate();\n      this._maybeCallOnEdgeReached();\n    };\n    this._onLayoutEmpty = e => {\n      this.props.onLayout && this.props.onLayout(e);\n    };\n    this._onLayoutFooter = e => {\n      this._triggerRemeasureForChildListsInCell(this._getFooterCellKey());\n      this._footerLength = this._selectLength(e.nativeEvent.layout);\n    };\n    this._onLayoutHeader = e => {\n      this._headerLength = this._selectLength(e.nativeEvent.layout);\n    };\n    this._onContentSizeChange = (width, height) => {\n      if (width > 0 && height > 0 && this.props.initialScrollIndex != null && this.props.initialScrollIndex > 0 && !this._hasTriggeredInitialScrollToIndex) {\n        if (this.props.contentOffset == null) {\n          if (this.props.initialScrollIndex < this.props.getItemCount(this.props.data)) {\n            this.scrollToIndex({\n              animated: false,\n              index: (0, _nullthrows.default)(this.props.initialScrollIndex)\n            });\n          } else {\n            this.scrollToEnd({\n              animated: false\n            });\n          }\n        }\n        this._hasTriggeredInitialScrollToIndex = true;\n      }\n      if (this.props.onContentSizeChange) {\n        this.props.onContentSizeChange(width, height);\n      }\n      this._scrollMetrics.contentLength = this._selectLength({\n        height,\n        width\n      });\n      this._scheduleCellsToRenderUpdate();\n      this._maybeCallOnEdgeReached();\n    };\n    this._convertParentScrollMetrics = metrics => {\n      // Offset of the top of the nested list relative to the top of its parent's viewport\n      var offset = metrics.offset - this._offsetFromParentVirtualizedList;\n      // Child's visible length is the same as its parent's\n      var visibleLength = metrics.visibleLength;\n      var dOffset = offset - this._scrollMetrics.offset;\n      var contentLength = this._scrollMetrics.contentLength;\n      return {\n        visibleLength,\n        contentLength,\n        offset,\n        dOffset\n      };\n    };\n    this._onScroll = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onScroll(e);\n      });\n      if (this.props.onScroll) {\n        this.props.onScroll(e);\n      }\n      var timestamp = e.timeStamp;\n      var visibleLength = this._selectLength(e.nativeEvent.layoutMeasurement);\n      var contentLength = this._selectLength(e.nativeEvent.contentSize);\n      var offset = this._selectOffset(e.nativeEvent.contentOffset);\n      var dOffset = offset - this._scrollMetrics.offset;\n      if (this._isNestedWithSameOrientation()) {\n        if (this._scrollMetrics.contentLength === 0) {\n          // Ignore scroll events until onLayout has been called and we\n          // know our offset from our offset from our parent\n          return;\n        }\n        var _this$_convertParentS = this._convertParentScrollMetrics({\n          visibleLength,\n          offset\n        });\n        visibleLength = _this$_convertParentS.visibleLength;\n        contentLength = _this$_convertParentS.contentLength;\n        offset = _this$_convertParentS.offset;\n        dOffset = _this$_convertParentS.dOffset;\n      }\n      var dt = this._scrollMetrics.timestamp ? Math.max(1, timestamp - this._scrollMetrics.timestamp) : 1;\n      var velocity = dOffset / dt;\n      if (dt > 500 && this._scrollMetrics.dt > 500 && contentLength > 5 * visibleLength && !this._hasWarned.perf) {\n        (0, _infoLog.default)('VirtualizedList: You have a large list that is slow to update - make sure your ' + 'renderItem function renders components that follow React performance best practices ' + 'like PureComponent, shouldComponentUpdate, etc.', {\n          dt,\n          prevDt: this._scrollMetrics.dt,\n          contentLength\n        });\n        this._hasWarned.perf = true;\n      }\n\n      // For invalid negative values (w/ RTL), set this to 1.\n      var zoomScale = e.nativeEvent.zoomScale < 0 ? 1 : e.nativeEvent.zoomScale;\n      this._scrollMetrics = {\n        contentLength,\n        dt,\n        dOffset,\n        offset,\n        timestamp,\n        velocity,\n        visibleLength,\n        zoomScale\n      };\n      this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n      if (!this.props) {\n        return;\n      }\n      this._maybeCallOnEdgeReached();\n      if (velocity !== 0) {\n        this._fillRateHelper.activate();\n      }\n      this._computeBlankness();\n      this._scheduleCellsToRenderUpdate();\n    };\n    this._onScrollBeginDrag = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onScrollBeginDrag(e);\n      });\n      this._viewabilityTuples.forEach(tuple => {\n        tuple.viewabilityHelper.recordInteraction();\n      });\n      this._hasInteracted = true;\n      this.props.onScrollBeginDrag && this.props.onScrollBeginDrag(e);\n    };\n    this._onScrollEndDrag = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onScrollEndDrag(e);\n      });\n      var velocity = e.nativeEvent.velocity;\n      if (velocity) {\n        this._scrollMetrics.velocity = this._selectOffset(velocity);\n      }\n      this._computeBlankness();\n      this.props.onScrollEndDrag && this.props.onScrollEndDrag(e);\n    };\n    this._onMomentumScrollBegin = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onMomentumScrollBegin(e);\n      });\n      this.props.onMomentumScrollBegin && this.props.onMomentumScrollBegin(e);\n    };\n    this._onMomentumScrollEnd = e => {\n      this._nestedChildLists.forEach(childList => {\n        childList._onMomentumScrollEnd(e);\n      });\n      this._scrollMetrics.velocity = 0;\n      this._computeBlankness();\n      this.props.onMomentumScrollEnd && this.props.onMomentumScrollEnd(e);\n    };\n    this._updateCellsToRender = () => {\n      this._updateViewableItems(this.props, this.state.cellsAroundViewport);\n      this.setState((state, props) => {\n        var cellsAroundViewport = this._adjustCellsAroundViewport(props, state.cellsAroundViewport);\n        var renderMask = VirtualizedList._createRenderMask(props, cellsAroundViewport, this._getNonViewportRenderRegions(props));\n        if (cellsAroundViewport.first === state.cellsAroundViewport.first && cellsAroundViewport.last === state.cellsAroundViewport.last && renderMask.equals(state.renderMask)) {\n          return null;\n        }\n        return {\n          cellsAroundViewport,\n          renderMask\n        };\n      });\n    };\n    this._createViewToken = (index, isViewable, props\n    // $FlowFixMe[missing-local-annot]\n    ) => {\n      var data = props.data,\n        getItem = props.getItem;\n      var item = getItem(data, index);\n      return {\n        index,\n        item,\n        key: this._keyExtractor(item, index, props),\n        isViewable\n      };\n    };\n    this._getOffsetApprox = (index, props) => {\n      if (Number.isInteger(index)) {\n        return this.__getFrameMetricsApprox(index, props).offset;\n      } else {\n        var frameMetrics = this.__getFrameMetricsApprox(Math.floor(index), props);\n        var remainder = index - Math.floor(index);\n        return frameMetrics.offset + remainder * frameMetrics.length;\n      }\n    };\n    this.__getFrameMetricsApprox = (index, props) => {\n      var frame = this._getFrameMetrics(index, props);\n      if (frame && frame.index === index) {\n        // check for invalid frames due to row re-ordering\n        return frame;\n      } else {\n        var data = props.data,\n          getItemCount = props.getItemCount,\n          getItemLayout = props.getItemLayout;\n        (0, _invariant.default)(index >= 0 && index < getItemCount(data), 'Tried to get frame for out of range index ' + index);\n        (0, _invariant.default)(!getItemLayout, 'Should not have to estimate frames when a measurement metrics function is provided');\n        return {\n          length: this._averageCellLength,\n          offset: this._averageCellLength * index\n        };\n      }\n    };\n    this._getFrameMetrics = (index, props) => {\n      var data = props.data,\n        getItem = props.getItem,\n        getItemCount = props.getItemCount,\n        getItemLayout = props.getItemLayout;\n      (0, _invariant.default)(index >= 0 && index < getItemCount(data), 'Tried to get frame for out of range index ' + index);\n      var item = getItem(data, index);\n      var frame = this._frames[this._keyExtractor(item, index, props)];\n      if (!frame || frame.index !== index) {\n        if (getItemLayout) {\n          /* $FlowFixMe[prop-missing] (>=0.63.0 site=react_native_fb) This comment\n           * suppresses an error found when Flow v0.63 was deployed. To see the error\n           * delete this comment and run Flow. */\n          return getItemLayout(data, index);\n        }\n      }\n      return frame;\n    };\n    this._getNonViewportRenderRegions = props => {\n      // Keep a viewport's worth of content around the last focused cell to allow\n      // random navigation around it without any blanking. E.g. tabbing from one\n      // focused item out of viewport to another.\n      if (!(this._lastFocusedCellKey && this._cellRefs[this._lastFocusedCellKey])) {\n        return [];\n      }\n      var lastFocusedCellRenderer = this._cellRefs[this._lastFocusedCellKey];\n      var focusedCellIndex = lastFocusedCellRenderer.props.index;\n      var itemCount = props.getItemCount(props.data);\n\n      // The last cell we rendered may be at a new index. Bail if we don't know\n      // where it is.\n      if (focusedCellIndex >= itemCount || this._keyExtractor(props.getItem(props.data, focusedCellIndex), focusedCellIndex, props) !== this._lastFocusedCellKey) {\n        return [];\n      }\n      var first = focusedCellIndex;\n      var heightOfCellsBeforeFocused = 0;\n      for (var i = first - 1; i >= 0 && heightOfCellsBeforeFocused < this._scrollMetrics.visibleLength; i--) {\n        first--;\n        heightOfCellsBeforeFocused += this.__getFrameMetricsApprox(i, props).length;\n      }\n      var last = focusedCellIndex;\n      var heightOfCellsAfterFocused = 0;\n      for (var _i = last + 1; _i < itemCount && heightOfCellsAfterFocused < this._scrollMetrics.visibleLength; _i++) {\n        last++;\n        heightOfCellsAfterFocused += this.__getFrameMetricsApprox(_i, props).length;\n      }\n      return [{\n        first,\n        last\n      }];\n    };\n    this._checkProps(_props);\n    this._fillRateHelper = new _FillRateHelper.default(this._getFrameMetrics);\n    this._updateCellsToRenderBatcher = new _Batchinator.default(this._updateCellsToRender, (_this$props$updateCel = this.props.updateCellsBatchingPeriod) !== null && _this$props$updateCel !== void 0 ? _this$props$updateCel : 50);\n    if (this.props.viewabilityConfigCallbackPairs) {\n      this._viewabilityTuples = this.props.viewabilityConfigCallbackPairs.map(pair => ({\n        viewabilityHelper: new _ViewabilityHelper.default(pair.viewabilityConfig),\n        onViewableItemsChanged: pair.onViewableItemsChanged\n      }));\n    } else {\n      var _this$props3 = this.props,\n        onViewableItemsChanged = _this$props3.onViewableItemsChanged,\n        viewabilityConfig = _this$props3.viewabilityConfig;\n      if (onViewableItemsChanged) {\n        this._viewabilityTuples.push({\n          viewabilityHelper: new _ViewabilityHelper.default(viewabilityConfig),\n          onViewableItemsChanged: onViewableItemsChanged\n        });\n      }\n    }\n    var initialRenderRegion = VirtualizedList._initialRenderRegion(_props);\n    this.state = {\n      cellsAroundViewport: initialRenderRegion,\n      renderMask: VirtualizedList._createRenderMask(_props, initialRenderRegion)\n    };\n\n    // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n    // For issue https://github.com/necolas/react-native-web/issues/995\n    this.invertedWheelEventHandler = ev => {\n      var scrollOffset = this.props.horizontal ? ev.target.scrollLeft : ev.target.scrollTop;\n      var scrollLength = this.props.horizontal ? ev.target.scrollWidth : ev.target.scrollHeight;\n      var clientLength = this.props.horizontal ? ev.target.clientWidth : ev.target.clientHeight;\n      var isEventTargetScrollable = scrollLength > clientLength;\n      var delta = this.props.horizontal ? ev.deltaX || ev.wheelDeltaX : ev.deltaY || ev.wheelDeltaY;\n      var leftoverDelta = delta;\n      if (isEventTargetScrollable) {\n        leftoverDelta = delta < 0 ? Math.min(delta + scrollOffset, 0) : Math.max(delta - (scrollLength - clientLength - scrollOffset), 0);\n      }\n      var targetDelta = delta - leftoverDelta;\n      if (this.props.inverted && this._scrollRef && this._scrollRef.getScrollableNode) {\n        var node = this._scrollRef.getScrollableNode();\n        if (this.props.horizontal) {\n          ev.target.scrollLeft += targetDelta;\n          var nextScrollLeft = node.scrollLeft - leftoverDelta;\n          node.scrollLeft = !this.props.getItemLayout ? Math.min(nextScrollLeft, this._totalCellLength) : nextScrollLeft;\n        } else {\n          ev.target.scrollTop += targetDelta;\n          var nextScrollTop = node.scrollTop - leftoverDelta;\n          node.scrollTop = !this.props.getItemLayout ? Math.min(nextScrollTop, this._totalCellLength) : nextScrollTop;\n        }\n        ev.preventDefault();\n      }\n    };\n  }\n  _checkProps(props) {\n    var onScroll = props.onScroll,\n      windowSize = props.windowSize,\n      getItemCount = props.getItemCount,\n      data = props.data,\n      initialScrollIndex = props.initialScrollIndex;\n    (0, _invariant.default)(\n    // $FlowFixMe[prop-missing]\n    !onScroll || !onScroll.__isNative, 'Components based on VirtualizedList must be wrapped with Animated.createAnimatedComponent ' + 'to support native onScroll events with useNativeDriver');\n    (0, _invariant.default)(windowSizeOrDefault(windowSize) > 0, 'VirtualizedList: The windowSize prop must be present and set to a value greater than 0.');\n    (0, _invariant.default)(getItemCount, 'VirtualizedList: The \"getItemCount\" prop must be provided');\n    var itemCount = getItemCount(data);\n    if (initialScrollIndex != null && !this._hasTriggeredInitialScrollToIndex && (initialScrollIndex < 0 || itemCount > 0 && initialScrollIndex >= itemCount) && !this._hasWarned.initialScrollIndex) {\n      console.warn(\"initialScrollIndex \\\"\" + initialScrollIndex + \"\\\" is not valid (list has \" + itemCount + \" items)\");\n      this._hasWarned.initialScrollIndex = true;\n    }\n    if (__DEV__ && !this._hasWarned.flexWrap) {\n      // $FlowFixMe[underconstrained-implicit-instantiation]\n      var flatStyles = _StyleSheet.default.flatten(this.props.contentContainerStyle);\n      if (flatStyles != null && flatStyles.flexWrap === 'wrap') {\n        console.warn('`flexWrap: `wrap`` is not supported with the `VirtualizedList` components.' + 'Consider using `numColumns` with `FlatList` instead.');\n        this._hasWarned.flexWrap = true;\n      }\n    }\n  }\n  static _createRenderMask(props, cellsAroundViewport, additionalRegions) {\n    var itemCount = props.getItemCount(props.data);\n    (0, _invariant.default)(cellsAroundViewport.first >= 0 && cellsAroundViewport.last >= cellsAroundViewport.first - 1 && cellsAroundViewport.last < itemCount, \"Invalid cells around viewport \\\"[\" + cellsAroundViewport.first + \", \" + cellsAroundViewport.last + \"]\\\" was passed to VirtualizedList._createRenderMask\");\n    var renderMask = new _CellRenderMask.CellRenderMask(itemCount);\n    if (itemCount > 0) {\n      var allRegions = [cellsAroundViewport, ...(additionalRegions !== null && additionalRegions !== void 0 ? additionalRegions : [])];\n      for (var _i2 = 0, _allRegions = allRegions; _i2 < _allRegions.length; _i2++) {\n        var region = _allRegions[_i2];\n        renderMask.addCells(region);\n      }\n\n      // The initially rendered cells are retained as part of the\n      // \"scroll-to-top\" optimization\n      if (props.initialScrollIndex == null || props.initialScrollIndex <= 0) {\n        var initialRegion = VirtualizedList._initialRenderRegion(props);\n        renderMask.addCells(initialRegion);\n      }\n\n      // The layout coordinates of sticker headers may be off-screen while the\n      // actual header is on-screen. Keep the most recent before the viewport\n      // rendered, even if its layout coordinates are not in viewport.\n      var stickyIndicesSet = new Set(props.stickyHeaderIndices);\n      VirtualizedList._ensureClosestStickyHeader(props, stickyIndicesSet, renderMask, cellsAroundViewport.first);\n    }\n    return renderMask;\n  }\n  static _initialRenderRegion(props) {\n    var _props$initialScrollI;\n    var itemCount = props.getItemCount(props.data);\n    var firstCellIndex = Math.max(0, Math.min(itemCount - 1, Math.floor((_props$initialScrollI = props.initialScrollIndex) !== null && _props$initialScrollI !== void 0 ? _props$initialScrollI : 0)));\n    var lastCellIndex = Math.min(itemCount, firstCellIndex + initialNumToRenderOrDefault(props.initialNumToRender)) - 1;\n    return {\n      first: firstCellIndex,\n      last: lastCellIndex\n    };\n  }\n  static _ensureClosestStickyHeader(props, stickyIndicesSet, renderMask, cellIdx) {\n    var stickyOffset = props.ListHeaderComponent ? 1 : 0;\n    for (var itemIdx = cellIdx - 1; itemIdx >= 0; itemIdx--) {\n      if (stickyIndicesSet.has(itemIdx + stickyOffset)) {\n        renderMask.addCells({\n          first: itemIdx,\n          last: itemIdx\n        });\n        break;\n      }\n    }\n  }\n  _adjustCellsAroundViewport(props, cellsAroundViewport) {\n    var data = props.data,\n      getItemCount = props.getItemCount;\n    var onEndReachedThreshold = onEndReachedThresholdOrDefault(props.onEndReachedThreshold);\n    var _this$_scrollMetrics = this._scrollMetrics,\n      contentLength = _this$_scrollMetrics.contentLength,\n      offset = _this$_scrollMetrics.offset,\n      visibleLength = _this$_scrollMetrics.visibleLength;\n    var distanceFromEnd = contentLength - visibleLength - offset;\n\n    // Wait until the scroll view metrics have been set up. And until then,\n    // we will trust the initialNumToRender suggestion\n    if (visibleLength <= 0 || contentLength <= 0) {\n      return cellsAroundViewport.last >= getItemCount(data) ? VirtualizedList._constrainToItemCount(cellsAroundViewport, props) : cellsAroundViewport;\n    }\n    var newCellsAroundViewport;\n    if (props.disableVirtualization) {\n      var renderAhead = distanceFromEnd < onEndReachedThreshold * visibleLength ? maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch) : 0;\n      newCellsAroundViewport = {\n        first: 0,\n        last: Math.min(cellsAroundViewport.last + renderAhead, getItemCount(data) - 1)\n      };\n    } else {\n      // If we have a non-zero initialScrollIndex and run this before we've scrolled,\n      // we'll wipe out the initialNumToRender rendered elements starting at initialScrollIndex.\n      // So let's wait until we've scrolled the view to the right place. And until then,\n      // we will trust the initialScrollIndex suggestion.\n\n      // Thus, we want to recalculate the windowed render limits if any of the following hold:\n      // - initialScrollIndex is undefined or is 0\n      // - initialScrollIndex > 0 AND scrolling is complete\n      // - initialScrollIndex > 0 AND the end of the list is visible (this handles the case\n      //   where the list is shorter than the visible area)\n      if (props.initialScrollIndex && !this._scrollMetrics.offset && Math.abs(distanceFromEnd) >= Number.EPSILON) {\n        return cellsAroundViewport.last >= getItemCount(data) ? VirtualizedList._constrainToItemCount(cellsAroundViewport, props) : cellsAroundViewport;\n      }\n      newCellsAroundViewport = (0, _VirtualizeUtils.computeWindowedRenderLimits)(props, maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch), windowSizeOrDefault(props.windowSize), cellsAroundViewport, this.__getFrameMetricsApprox, this._scrollMetrics);\n      (0, _invariant.default)(newCellsAroundViewport.last < getItemCount(data), 'computeWindowedRenderLimits() should return range in-bounds');\n    }\n    if (this._nestedChildLists.size() > 0) {\n      // If some cell in the new state has a child list in it, we should only render\n      // up through that item, so that we give that list a chance to render.\n      // Otherwise there's churn from multiple child lists mounting and un-mounting\n      // their items.\n\n      // Will this prevent rendering if the nested list doesn't realize the end?\n      var childIdx = this._findFirstChildWithMore(newCellsAroundViewport.first, newCellsAroundViewport.last);\n      newCellsAroundViewport.last = childIdx !== null && childIdx !== void 0 ? childIdx : newCellsAroundViewport.last;\n    }\n    return newCellsAroundViewport;\n  }\n  _findFirstChildWithMore(first, last) {\n    for (var ii = first; ii <= last; ii++) {\n      var cellKeyForIndex = this._indicesToKeys.get(ii);\n      if (cellKeyForIndex != null && this._nestedChildLists.anyInCell(cellKeyForIndex, childList => childList.hasMore())) {\n        return ii;\n      }\n    }\n    return null;\n  }\n  componentDidMount() {\n    if (this._isNestedWithSameOrientation()) {\n      this.context.registerAsNestedChild({\n        ref: this,\n        cellKey: this.context.cellKey\n      });\n    }\n\n    // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n    this.setupWebWheelHandler();\n  }\n  componentWillUnmount() {\n    if (this._isNestedWithSameOrientation()) {\n      this.context.unregisterAsNestedChild({\n        ref: this\n      });\n    }\n    this._updateCellsToRenderBatcher.dispose({\n      abort: true\n    });\n    this._viewabilityTuples.forEach(tuple => {\n      tuple.viewabilityHelper.dispose();\n    });\n    this._fillRateHelper.deactivateAndFlush();\n\n    // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n    this.teardownWebWheelHandler();\n  }\n\n  // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n  setupWebWheelHandler() {\n    if (this._scrollRef && this._scrollRef.getScrollableNode) {\n      this._scrollRef.getScrollableNode().addEventListener('wheel', this.invertedWheelEventHandler);\n    } else {\n      setTimeout(() => this.setupWebWheelHandler(), 50);\n      return;\n    }\n  }\n\n  // REACT-NATIVE-WEB patch to preserve during future RN merges: Support inverted wheel scroller.\n  teardownWebWheelHandler() {\n    if (this._scrollRef && this._scrollRef.getScrollableNode) {\n      this._scrollRef.getScrollableNode().removeEventListener('wheel', this.invertedWheelEventHandler);\n    }\n  }\n  static getDerivedStateFromProps(newProps, prevState) {\n    // first and last could be stale (e.g. if a new, shorter items props is passed in), so we make\n    // sure we're rendering a reasonable range here.\n    var itemCount = newProps.getItemCount(newProps.data);\n    if (itemCount === prevState.renderMask.numCells()) {\n      return prevState;\n    }\n    var constrainedCells = VirtualizedList._constrainToItemCount(prevState.cellsAroundViewport, newProps);\n    return {\n      cellsAroundViewport: constrainedCells,\n      renderMask: VirtualizedList._createRenderMask(newProps, constrainedCells)\n    };\n  }\n  _pushCells(cells, stickyHeaderIndices, stickyIndicesFromProps, first, last, inversionStyle) {\n    var _this = this;\n    var _this$props4 = this.props,\n      CellRendererComponent = _this$props4.CellRendererComponent,\n      ItemSeparatorComponent = _this$props4.ItemSeparatorComponent,\n      ListHeaderComponent = _this$props4.ListHeaderComponent,\n      ListItemComponent = _this$props4.ListItemComponent,\n      data = _this$props4.data,\n      debug = _this$props4.debug,\n      getItem = _this$props4.getItem,\n      getItemCount = _this$props4.getItemCount,\n      getItemLayout = _this$props4.getItemLayout,\n      horizontal = _this$props4.horizontal,\n      renderItem = _this$props4.renderItem;\n    var stickyOffset = ListHeaderComponent ? 1 : 0;\n    var end = getItemCount(data) - 1;\n    var prevCellKey;\n    last = Math.min(end, last);\n    var _loop = function _loop() {\n      var item = getItem(data, ii);\n      var key = _this._keyExtractor(item, ii, _this.props);\n      _this._indicesToKeys.set(ii, key);\n      if (stickyIndicesFromProps.has(ii + stickyOffset)) {\n        stickyHeaderIndices.push(cells.length);\n      }\n      var shouldListenForLayout = getItemLayout == null || debug || _this._fillRateHelper.enabled();\n      cells.push(/*#__PURE__*/React.createElement(_VirtualizedListCellRenderer.default, (0, _extends2.default)({\n        CellRendererComponent: CellRendererComponent,\n        ItemSeparatorComponent: ii < end ? ItemSeparatorComponent : undefined,\n        ListItemComponent: ListItemComponent,\n        cellKey: key,\n        horizontal: horizontal,\n        index: ii,\n        inversionStyle: inversionStyle,\n        item: item,\n        key: key,\n        prevCellKey: prevCellKey,\n        onUpdateSeparators: _this._onUpdateSeparators,\n        onCellFocusCapture: e => _this._onCellFocusCapture(key),\n        onUnmount: _this._onCellUnmount,\n        ref: _ref => {\n          _this._cellRefs[key] = _ref;\n        },\n        renderItem: renderItem\n      }, shouldListenForLayout && {\n        onCellLayout: _this._onCellLayout\n      })));\n      prevCellKey = key;\n    };\n    for (var ii = first; ii <= last; ii++) {\n      _loop();\n    }\n  }\n  static _constrainToItemCount(cells, props) {\n    var itemCount = props.getItemCount(props.data);\n    var last = Math.min(itemCount - 1, cells.last);\n    var maxToRenderPerBatch = maxToRenderPerBatchOrDefault(props.maxToRenderPerBatch);\n    return {\n      first: (0, _clamp.default)(0, itemCount - 1 - maxToRenderPerBatch, cells.first),\n      last\n    };\n  }\n  _isNestedWithSameOrientation() {\n    var nestedContext = this.context;\n    return !!(nestedContext && !!nestedContext.horizontal === horizontalOrDefault(this.props.horizontal));\n  }\n  _keyExtractor(item, index, props\n  // $FlowFixMe[missing-local-annot]\n  ) {\n    if (props.keyExtractor != null) {\n      return props.keyExtractor(item, index);\n    }\n    var key = (0, _VirtualizeUtils.keyExtractor)(item, index);\n    if (key === String(index)) {\n      _usedIndexForKey = true;\n      if (item.type && item.type.displayName) {\n        _keylessItemComponentName = item.type.displayName;\n      }\n    }\n    return key;\n  }\n  render() {\n    this._checkProps(this.props);\n    var _this$props5 = this.props,\n      ListEmptyComponent = _this$props5.ListEmptyComponent,\n      ListFooterComponent = _this$props5.ListFooterComponent,\n      ListHeaderComponent = _this$props5.ListHeaderComponent;\n    var _this$props6 = this.props,\n      data = _this$props6.data,\n      horizontal = _this$props6.horizontal;\n    var inversionStyle = this.props.inverted ? horizontalOrDefault(this.props.horizontal) ? styles.horizontallyInverted : styles.verticallyInverted : null;\n    var cells = [];\n    var stickyIndicesFromProps = new Set(this.props.stickyHeaderIndices);\n    var stickyHeaderIndices = [];\n\n    // 1. Add cell for ListHeaderComponent\n    if (ListHeaderComponent) {\n      if (stickyIndicesFromProps.has(0)) {\n        stickyHeaderIndices.push(0);\n      }\n      var _element = /*#__PURE__*/React.isValidElement(ListHeaderComponent) ? ListHeaderComponent :\n      /*#__PURE__*/\n      // $FlowFixMe[not-a-component]\n      // $FlowFixMe[incompatible-type-arg]\n      React.createElement(ListHeaderComponent, null);\n      cells.push(/*#__PURE__*/React.createElement(_VirtualizedListContext.VirtualizedListCellContextProvider, {\n        cellKey: this._getCellKey() + '-header',\n        key: \"$header\"\n      }, /*#__PURE__*/React.createElement(_View.default, {\n        onLayout: this._onLayoutHeader,\n        style: [inversionStyle, this.props.ListHeaderComponentStyle]\n      },\n      // $FlowFixMe[incompatible-type] - Typing ReactNativeComponent revealed errors\n      _element)));\n    }\n\n    // 2a. Add a cell for ListEmptyComponent if applicable\n    var itemCount = this.props.getItemCount(data);\n    if (itemCount === 0 && ListEmptyComponent) {\n      var _element2 = /*#__PURE__*/React.isValidElement(ListEmptyComponent) ? ListEmptyComponent :\n      /*#__PURE__*/\n      // $FlowFixMe[not-a-component]\n      // $FlowFixMe[incompatible-type-arg]\n      React.createElement(ListEmptyComponent, null);\n      cells.push(/*#__PURE__*/React.createElement(_VirtualizedListContext.VirtualizedListCellContextProvider, {\n        cellKey: this._getCellKey() + '-empty',\n        key: \"$empty\"\n      }, /*#__PURE__*/React.cloneElement(_element2, {\n        onLayout: event => {\n          this._onLayoutEmpty(event);\n          if (_element2.props.onLayout) {\n            _element2.props.onLayout(event);\n          }\n        },\n        style: [inversionStyle, _element2.props.style]\n      })));\n    }\n\n    // 2b. Add cells and spacers for each item\n    if (itemCount > 0) {\n      _usedIndexForKey = false;\n      _keylessItemComponentName = '';\n      var spacerKey = this._getSpacerKey(!horizontal);\n      var renderRegions = this.state.renderMask.enumerateRegions();\n      var lastSpacer = findLastWhere(renderRegions, r => r.isSpacer);\n      for (var _iterator = (0, _createForOfIteratorHelperLoose2.default)(renderRegions), _step; !(_step = _iterator()).done;) {\n        var section = _step.value;\n        if (section.isSpacer) {\n          // Legacy behavior is to avoid spacers when virtualization is\n          // disabled (including head spacers on initial render).\n          if (this.props.disableVirtualization) {\n            continue;\n          }\n\n          // Without getItemLayout, we limit our tail spacer to the _highestMeasuredFrameIndex to\n          // prevent the user for hyperscrolling into un-measured area because otherwise content will\n          // likely jump around as it renders in above the viewport.\n          var isLastSpacer = section === lastSpacer;\n          var constrainToMeasured = isLastSpacer && !this.props.getItemLayout;\n          var last = constrainToMeasured ? (0, _clamp.default)(section.first - 1, section.last, this._highestMeasuredFrameIndex) : section.last;\n          var firstMetrics = this.__getFrameMetricsApprox(section.first, this.props);\n          var lastMetrics = this.__getFrameMetricsApprox(last, this.props);\n          var spacerSize = lastMetrics.offset + lastMetrics.length - firstMetrics.offset;\n          cells.push(/*#__PURE__*/React.createElement(_View.default, {\n            key: \"$spacer-\" + section.first,\n            style: {\n              [spacerKey]: spacerSize\n            }\n          }));\n        } else {\n          this._pushCells(cells, stickyHeaderIndices, stickyIndicesFromProps, section.first, section.last, inversionStyle);\n        }\n      }\n      if (!this._hasWarned.keys && _usedIndexForKey) {\n        console.warn('VirtualizedList: missing keys for items, make sure to specify a key or id property on each ' + 'item or provide a custom keyExtractor.', _keylessItemComponentName);\n        this._hasWarned.keys = true;\n      }\n    }\n\n    // 3. Add cell for ListFooterComponent\n    if (ListFooterComponent) {\n      var _element3 = /*#__PURE__*/React.isValidElement(ListFooterComponent) ? ListFooterComponent :\n      /*#__PURE__*/\n      // $FlowFixMe[not-a-component]\n      // $FlowFixMe[incompatible-type-arg]\n      React.createElement(ListFooterComponent, null);\n      cells.push(/*#__PURE__*/React.createElement(_VirtualizedListContext.VirtualizedListCellContextProvider, {\n        cellKey: this._getFooterCellKey(),\n        key: \"$footer\"\n      }, /*#__PURE__*/React.createElement(_View.default, {\n        onLayout: this._onLayoutFooter,\n        style: [inversionStyle, this.props.ListFooterComponentStyle]\n      },\n      // $FlowFixMe[incompatible-type] - Typing ReactNativeComponent revealed errors\n      _element3)));\n    }\n\n    // 4. Render the ScrollView\n    var scrollProps = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, this.props), {}, {\n      onContentSizeChange: this._onContentSizeChange,\n      onLayout: this._onLayout,\n      onScroll: this._onScroll,\n      onScrollBeginDrag: this._onScrollBeginDrag,\n      onScrollEndDrag: this._onScrollEndDrag,\n      onMomentumScrollBegin: this._onMomentumScrollBegin,\n      onMomentumScrollEnd: this._onMomentumScrollEnd,\n      scrollEventThrottle: scrollEventThrottleOrDefault(this.props.scrollEventThrottle),\n      // TODO: Android support\n      invertStickyHeaders: this.props.invertStickyHeaders !== undefined ? this.props.invertStickyHeaders : this.props.inverted,\n      stickyHeaderIndices,\n      style: inversionStyle ? [inversionStyle, this.props.style] : this.props.style\n    });\n    this._hasMore = this.state.cellsAroundViewport.last < itemCount - 1;\n    var innerRet = /*#__PURE__*/React.createElement(_VirtualizedListContext.VirtualizedListContextProvider, {\n      value: {\n        cellKey: null,\n        getScrollMetrics: this._getScrollMetrics,\n        horizontal: horizontalOrDefault(this.props.horizontal),\n        getOutermostParentListRef: this._getOutermostParentListRef,\n        registerAsNestedChild: this._registerAsNestedChild,\n        unregisterAsNestedChild: this._unregisterAsNestedChild\n      }\n    }, /*#__PURE__*/React.cloneElement((this.props.renderScrollComponent || this._defaultRenderScrollComponent)(scrollProps), {\n      ref: this._captureScrollRef\n    }, cells));\n    var ret = innerRet;\n    /* https://github.com/necolas/react-native-web/issues/2239: Re-enable when ScrollView.Context.Consumer is available.\n    if (__DEV__) {\n      ret = (\n        <ScrollView.Context.Consumer>\n          {scrollContext => {\n            if (\n              scrollContext != null &&\n              !scrollContext.horizontal ===\n                !horizontalOrDefault(this.props.horizontal) &&\n              !this._hasWarned.nesting &&\n              this.context == null &&\n              this.props.scrollEnabled !== false\n            ) {\n              // TODO (*********): use React.warn once 16.9 is sync'd: https://github.com/facebook/react/pull/15170\n              console.error(\n                'VirtualizedLists should never be nested inside plain ScrollViews with the same ' +\n                  'orientation because it can break windowing and other functionality - use another ' +\n                  'VirtualizedList-backed container instead.',\n              );\n              this._hasWarned.nesting = true;\n            }\n            return innerRet;\n          }}\n        </ScrollView.Context.Consumer>\n      );\n    }*/\n    if (this.props.debug) {\n      return /*#__PURE__*/React.createElement(_View.default, {\n        style: styles.debug\n      }, ret, this._renderDebugOverlay());\n    } else {\n      return ret;\n    }\n  }\n  componentDidUpdate(prevProps) {\n    var _this$props7 = this.props,\n      data = _this$props7.data,\n      extraData = _this$props7.extraData;\n    if (data !== prevProps.data || extraData !== prevProps.extraData) {\n      // clear the viewableIndices cache to also trigger\n      // the onViewableItemsChanged callback with the new data\n      this._viewabilityTuples.forEach(tuple => {\n        tuple.viewabilityHelper.resetViewableIndices();\n      });\n    }\n    // The `this._hiPriInProgress` is guaranteeing a hiPri cell update will only happen\n    // once per fiber update. The `_scheduleCellsToRenderUpdate` will set it to true\n    // if a hiPri update needs to perform. If `componentDidUpdate` is triggered with\n    // `this._hiPriInProgress=true`, means it's triggered by the hiPri update. The\n    // `_scheduleCellsToRenderUpdate` will check this condition and not perform\n    // another hiPri update.\n    var hiPriInProgress = this._hiPriInProgress;\n    this._scheduleCellsToRenderUpdate();\n    // Make sure setting `this._hiPriInProgress` back to false after `componentDidUpdate`\n    // is triggered with `this._hiPriInProgress = true`\n    if (hiPriInProgress) {\n      this._hiPriInProgress = false;\n    }\n  }\n\n  // Used for preventing scrollToIndex from being called multiple times for initialScrollIndex\n\n  // flag to prevent infinite hiPri cell limit update\n\n  // $FlowFixMe[missing-local-annot]\n\n  /* $FlowFixMe[missing-local-annot] The type annotation(s) required by Flow's\n   * LTI update could not be added via codemod */\n\n  _computeBlankness() {\n    this._fillRateHelper.computeBlankness(this.props, this.state.cellsAroundViewport, this._scrollMetrics);\n  }\n\n  /* $FlowFixMe[missing-local-annot] The type annotation(s) required by Flow's\n   * LTI update could not be added via codemod */\n\n  _onCellFocusCapture(cellKey) {\n    this._lastFocusedCellKey = cellKey;\n    this._updateCellsToRender();\n  }\n  _triggerRemeasureForChildListsInCell(cellKey) {\n    this._nestedChildLists.forEachInCell(cellKey, childList => {\n      childList.measureLayoutRelativeToContainingList();\n    });\n  }\n  measureLayoutRelativeToContainingList() {\n    // TODO (*********): findNodeHandle sometimes crashes with \"Unable to find\n    // node on an unmounted component\" during scrolling\n    try {\n      if (!this._scrollRef) {\n        return;\n      }\n      // We are assuming that getOutermostParentListRef().getScrollRef()\n      // is a non-null reference to a ScrollView\n      this._scrollRef.measureLayout(this.context.getOutermostParentListRef().getScrollRef(), (x, y, width, height) => {\n        this._offsetFromParentVirtualizedList = this._selectOffset({\n          x,\n          y\n        });\n        this._scrollMetrics.contentLength = this._selectLength({\n          width,\n          height\n        });\n        var scrollMetrics = this._convertParentScrollMetrics(this.context.getScrollMetrics());\n        var metricsChanged = this._scrollMetrics.visibleLength !== scrollMetrics.visibleLength || this._scrollMetrics.offset !== scrollMetrics.offset;\n        if (metricsChanged) {\n          this._scrollMetrics.visibleLength = scrollMetrics.visibleLength;\n          this._scrollMetrics.offset = scrollMetrics.offset;\n\n          // If metrics of the scrollView changed, then we triggered remeasure for child list\n          // to ensure VirtualizedList has the right information.\n          this._nestedChildLists.forEach(childList => {\n            childList.measureLayoutRelativeToContainingList();\n          });\n        }\n      }, error => {\n        console.warn(\"VirtualizedList: Encountered an error while measuring a list's\" + ' offset from its containing VirtualizedList.');\n      });\n    } catch (error) {\n      console.warn('measureLayoutRelativeToContainingList threw an error', error.stack);\n    }\n  }\n  _getFooterCellKey() {\n    return this._getCellKey() + '-footer';\n  }\n  // $FlowFixMe[missing-local-annot]\n  _renderDebugOverlay() {\n    var normalize = this._scrollMetrics.visibleLength / (this._scrollMetrics.contentLength || 1);\n    var framesInLayout = [];\n    var itemCount = this.props.getItemCount(this.props.data);\n    for (var ii = 0; ii < itemCount; ii++) {\n      var frame = this.__getFrameMetricsApprox(ii, this.props);\n      /* $FlowFixMe[prop-missing] (>=0.68.0 site=react_native_fb) This comment\n       * suppresses an error found when Flow v0.68 was deployed. To see the\n       * error delete this comment and run Flow. */\n      if (frame.inLayout) {\n        framesInLayout.push(frame);\n      }\n    }\n    var windowTop = this.__getFrameMetricsApprox(this.state.cellsAroundViewport.first, this.props).offset;\n    var frameLast = this.__getFrameMetricsApprox(this.state.cellsAroundViewport.last, this.props);\n    var windowLen = frameLast.offset + frameLast.length - windowTop;\n    var visTop = this._scrollMetrics.offset;\n    var visLen = this._scrollMetrics.visibleLength;\n    return /*#__PURE__*/React.createElement(_View.default, {\n      style: [styles.debugOverlayBase, styles.debugOverlay]\n    }, framesInLayout.map((f, ii) => /*#__PURE__*/React.createElement(_View.default, {\n      key: 'f' + ii,\n      style: [styles.debugOverlayBase, styles.debugOverlayFrame, {\n        top: f.offset * normalize,\n        height: f.length * normalize\n      }]\n    })), /*#__PURE__*/React.createElement(_View.default, {\n      style: [styles.debugOverlayBase, styles.debugOverlayFrameLast, {\n        top: windowTop * normalize,\n        height: windowLen * normalize\n      }]\n    }), /*#__PURE__*/React.createElement(_View.default, {\n      style: [styles.debugOverlayBase, styles.debugOverlayFrameVis, {\n        top: visTop * normalize,\n        height: visLen * normalize\n      }]\n    }));\n  }\n  _selectLength(metrics) {\n    return !horizontalOrDefault(this.props.horizontal) ? metrics.height : metrics.width;\n  }\n  _selectOffset(metrics) {\n    return !horizontalOrDefault(this.props.horizontal) ? metrics.y : metrics.x;\n  }\n  _maybeCallOnEdgeReached() {\n    var _this$props8 = this.props,\n      data = _this$props8.data,\n      getItemCount = _this$props8.getItemCount,\n      onStartReached = _this$props8.onStartReached,\n      onStartReachedThreshold = _this$props8.onStartReachedThreshold,\n      onEndReached = _this$props8.onEndReached,\n      onEndReachedThreshold = _this$props8.onEndReachedThreshold,\n      initialScrollIndex = _this$props8.initialScrollIndex;\n    var _this$_scrollMetrics2 = this._scrollMetrics,\n      contentLength = _this$_scrollMetrics2.contentLength,\n      visibleLength = _this$_scrollMetrics2.visibleLength,\n      offset = _this$_scrollMetrics2.offset;\n    var distanceFromStart = offset;\n    var distanceFromEnd = contentLength - visibleLength - offset;\n\n    // Especially when oERT is zero it's necessary to 'floor' very small distance values to be 0\n    // since debouncing causes us to not fire this event for every single \"pixel\" we scroll and can thus\n    // be at the edge of the list with a distance approximating 0 but not quite there.\n    if (distanceFromStart < ON_EDGE_REACHED_EPSILON) {\n      distanceFromStart = 0;\n    }\n    if (distanceFromEnd < ON_EDGE_REACHED_EPSILON) {\n      distanceFromEnd = 0;\n    }\n\n    // TODO: T121172172 Look into why we're \"defaulting\" to a threshold of 2px\n    // when oERT is not present (different from 2 viewports used elsewhere)\n    var DEFAULT_THRESHOLD_PX = 2;\n    var startThreshold = onStartReachedThreshold != null ? onStartReachedThreshold * visibleLength : DEFAULT_THRESHOLD_PX;\n    var endThreshold = onEndReachedThreshold != null ? onEndReachedThreshold * visibleLength : DEFAULT_THRESHOLD_PX;\n    var isWithinStartThreshold = distanceFromStart <= startThreshold;\n    var isWithinEndThreshold = distanceFromEnd <= endThreshold;\n\n    // First check if the user just scrolled within the end threshold\n    // and call onEndReached only once for a given content length,\n    // and only if onStartReached is not being executed\n    if (onEndReached && this.state.cellsAroundViewport.last === getItemCount(data) - 1 && isWithinEndThreshold && this._scrollMetrics.contentLength !== this._sentEndForContentLength) {\n      this._sentEndForContentLength = this._scrollMetrics.contentLength;\n      onEndReached({\n        distanceFromEnd\n      });\n    }\n\n    // Next check if the user just scrolled within the start threshold\n    // and call onStartReached only once for a given content length,\n    // and only if onEndReached is not being executed\n    else if (onStartReached != null && this.state.cellsAroundViewport.first === 0 && isWithinStartThreshold && this._scrollMetrics.contentLength !== this._sentStartForContentLength) {\n      // On initial mount when using initialScrollIndex the offset will be 0 initially\n      // and will trigger an unexpected onStartReached. To avoid this we can use\n      // timestamp to differentiate between the initial scroll metrics and when we actually\n      // received the first scroll event.\n      if (!initialScrollIndex || this._scrollMetrics.timestamp !== 0) {\n        this._sentStartForContentLength = this._scrollMetrics.contentLength;\n        onStartReached({\n          distanceFromStart\n        });\n      }\n    }\n\n    // If the user scrolls away from the start or end and back again,\n    // cause onStartReached or onEndReached to be triggered again\n    else {\n      this._sentStartForContentLength = isWithinStartThreshold ? this._sentStartForContentLength : 0;\n      this._sentEndForContentLength = isWithinEndThreshold ? this._sentEndForContentLength : 0;\n    }\n  }\n\n  /* Translates metrics from a scroll event in a parent VirtualizedList into\n   * coordinates relative to the child list.\n   */\n\n  _scheduleCellsToRenderUpdate() {\n    var _this$state$cellsArou = this.state.cellsAroundViewport,\n      first = _this$state$cellsArou.first,\n      last = _this$state$cellsArou.last;\n    var _this$_scrollMetrics3 = this._scrollMetrics,\n      offset = _this$_scrollMetrics3.offset,\n      visibleLength = _this$_scrollMetrics3.visibleLength,\n      velocity = _this$_scrollMetrics3.velocity;\n    var itemCount = this.props.getItemCount(this.props.data);\n    var hiPri = false;\n    var onStartReachedThreshold = onStartReachedThresholdOrDefault(this.props.onStartReachedThreshold);\n    var onEndReachedThreshold = onEndReachedThresholdOrDefault(this.props.onEndReachedThreshold);\n    // Mark as high priority if we're close to the start of the first item\n    // But only if there are items before the first rendered item\n    if (first > 0) {\n      var distTop = offset - this.__getFrameMetricsApprox(first, this.props).offset;\n      hiPri = distTop < 0 || velocity < -2 && distTop < getScrollingThreshold(onStartReachedThreshold, visibleLength);\n    }\n    // Mark as high priority if we're close to the end of the last item\n    // But only if there are items after the last rendered item\n    if (!hiPri && last >= 0 && last < itemCount - 1) {\n      var distBottom = this.__getFrameMetricsApprox(last, this.props).offset - (offset + visibleLength);\n      hiPri = distBottom < 0 || velocity > 2 && distBottom < getScrollingThreshold(onEndReachedThreshold, visibleLength);\n    }\n    // Only trigger high-priority updates if we've actually rendered cells,\n    // and with that size estimate, accurately compute how many cells we should render.\n    // Otherwise, it would just render as many cells as it can (of zero dimension),\n    // each time through attempting to render more (limited by maxToRenderPerBatch),\n    // starving the renderer from actually laying out the objects and computing _averageCellLength.\n    // If this is triggered in an `componentDidUpdate` followed by a hiPri cellToRenderUpdate\n    // We shouldn't do another hipri cellToRenderUpdate\n    if (hiPri && (this._averageCellLength || this.props.getItemLayout) && !this._hiPriInProgress) {\n      this._hiPriInProgress = true;\n      // Don't worry about interactions when scrolling quickly; focus on filling content as fast\n      // as possible.\n      this._updateCellsToRenderBatcher.dispose({\n        abort: true\n      });\n      this._updateCellsToRender();\n      return;\n    } else {\n      this._updateCellsToRenderBatcher.schedule();\n    }\n  }\n\n  /**\n   * Gets an approximate offset to an item at a given index. Supports\n   * fractional indices.\n   */\n\n  _updateViewableItems(props, cellsAroundViewport) {\n    this._viewabilityTuples.forEach(tuple => {\n      tuple.viewabilityHelper.onUpdate(props, this._scrollMetrics.offset, this._scrollMetrics.visibleLength, this._getFrameMetrics, this._createViewToken, tuple.onViewableItemsChanged, cellsAroundViewport);\n    });\n  }\n}\nVirtualizedList.contextType = _VirtualizedListContext.VirtualizedListContext;\nvar styles = _StyleSheet.default.create({\n  verticallyInverted: {\n    transform: 'scaleY(-1)'\n  },\n  horizontallyInverted: {\n    transform: 'scaleX(-1)'\n  },\n  debug: {\n    flex: 1\n  },\n  debugOverlayBase: {\n    position: 'absolute',\n    top: 0,\n    right: 0\n  },\n  debugOverlay: {\n    bottom: 0,\n    width: 20,\n    borderColor: 'blue',\n    borderWidth: 1\n  },\n  debugOverlayFrame: {\n    left: 0,\n    backgroundColor: 'orange'\n  },\n  debugOverlayFrameLast: {\n    left: 0,\n    borderColor: 'green',\n    borderWidth: 2\n  },\n  debugOverlayFrameVis: {\n    left: 0,\n    borderColor: 'red',\n    borderWidth: 2\n  }\n});\nvar _default = exports.default = VirtualizedList;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,mBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,gBAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,gBAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,aAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,2BAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,gBAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,IAAAO,UAAA,GAAAR,uBAAA,CAAAC,OAAA;AAAA,SAAAQ,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAM,OAAA,EAAAF,CAAA,OAAAL,2BAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAM,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEb,IAAIa,uBAAuB,GAAGtB,OAAO,CAAC,+CAA+C,CAAC,CAACY,OAAO;AAC9F,IAAIW,sBAAsB,GAAGvB,OAAO,CAAC,8CAA8C,CAAC,CAACY,OAAO;AAC5FY,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACZ,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIc,gCAAgC,GAAGH,sBAAsB,CAACvB,OAAO,CAAC,uDAAuD,CAAC,CAAC;AAC/H,IAAI2B,SAAS,GAAGJ,sBAAsB,CAACvB,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAI4B,cAAc,GAAGL,sBAAsB,CAACvB,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAI6B,eAAe,GAAGN,sBAAsB,CAACvB,OAAO,kCAAkC,CAAC,CAAC;AACxF,IAAI8B,WAAW,GAAGP,sBAAsB,CAACvB,OAAO,8BAA8B,CAAC,CAAC;AAChF,IAAI+B,KAAK,GAAGR,sBAAsB,CAACvB,OAAO,wBAAwB,CAAC,CAAC;AACpE,IAAIgC,WAAW,GAAGT,sBAAsB,CAACvB,OAAO,8BAA8B,CAAC,CAAC;AAChF,IAAIiC,YAAY,GAAGV,sBAAsB,CAACvB,OAAO,iBAAiB,CAAC,CAAC;AACpE,IAAIkC,MAAM,GAAGX,sBAAsB,CAACvB,OAAO,qBAAqB,CAAC,CAAC;AAClE,IAAImC,QAAQ,GAAGZ,sBAAsB,CAACvB,OAAO,aAAa,CAAC,CAAC;AAC5D,IAAIoC,eAAe,GAAGpC,OAAO,mBAAmB,CAAC;AACjD,IAAIqC,oBAAoB,GAAGd,sBAAsB,CAACvB,OAAO,wBAAwB,CAAC,CAAC;AACnF,IAAIsC,eAAe,GAAGf,sBAAsB,CAACvB,OAAO,oBAAoB,CAAC,CAAC;AAC1E,IAAIuC,uBAAuB,GAAGhB,sBAAsB,CAACvB,OAAO,2BAA2B,CAAC,CAAC;AACzF,IAAIwC,kBAAkB,GAAGjB,sBAAsB,CAACvB,OAAO,uBAAuB,CAAC,CAAC;AAChF,IAAIyC,4BAA4B,GAAGlB,sBAAsB,CAACvB,OAAO,gCAAgC,CAAC,CAAC;AACnG,IAAI0C,uBAAuB,GAAG1C,OAAO,8BAA8B,CAAC;AACpE,IAAI2C,gBAAgB,GAAG3C,OAAO,qBAAqB,CAAC;AACpD,IAAI4C,UAAU,GAAGrB,sBAAsB,CAACvB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAI6C,WAAW,GAAGtB,sBAAsB,CAACvB,OAAO,CAAC,YAAY,CAAC,CAAC;AAC/D,IAAI8C,KAAK,GAAGxB,uBAAuB,CAACtB,OAAO,CAAC,OAAO,CAAC,CAAC;AAWrD,IAAI+C,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACnD,IAAIC,uBAAuB,GAAG,KAAK;AACnC,IAAIC,gBAAgB,GAAG,KAAK;AAC5B,IAAIC,yBAAyB,GAAG,EAAE;AAOlC,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,OAAOA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,KAAK;AAC1E;AAGA,SAASC,2BAA2BA,CAACC,kBAAkB,EAAE;EACvD,OAAOA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,EAAE;AAC/F;AAGA,SAASC,4BAA4BA,CAACC,mBAAmB,EAAE;EACzD,OAAOA,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAG,EAAE;AAClG;AAGA,SAASC,gCAAgCA,CAACC,uBAAuB,EAAE;EACjE,OAAOA,uBAAuB,KAAK,IAAI,IAAIA,uBAAuB,KAAK,KAAK,CAAC,GAAGA,uBAAuB,GAAG,CAAC;AAC7G;AAGA,SAASC,8BAA8BA,CAACC,qBAAqB,EAAE;EAC7D,OAAOA,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC;AACvG;AAGA,SAASC,qBAAqBA,CAACC,SAAS,EAAEC,aAAa,EAAE;EACvD,OAAOD,SAAS,GAAGC,aAAa,GAAG,CAAC;AACtC;AAGA,SAASC,4BAA4BA,CAACC,mBAAmB,EAAE;EACzD,OAAOA,mBAAmB,KAAK,IAAI,IAAIA,mBAAmB,KAAK,KAAK,CAAC,GAAGA,mBAAmB,GAAG,EAAE;AAClG;AAGA,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,OAAOA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,EAAE;AACvE;AACA,SAASC,aAAaA,CAACC,GAAG,EAAEC,SAAS,EAAE;EACrC,KAAK,IAAIC,CAAC,GAAGF,GAAG,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACxC,IAAID,SAAS,CAACD,GAAG,CAACE,CAAC,CAAC,CAAC,EAAE;MACrB,OAAOF,GAAG,CAACE,CAAC,CAAC;IACf;EACF;EACA,OAAO,IAAI;AACb;AAAC,IA+BKE,eAAe,aAAAC,qBAAA;EA0KnB,SAAAD,gBAAYE,MAAM,EAAE;IAAA,IAAAC,MAAA;IAAA,IAAA5E,gBAAA,CAAAS,OAAA,QAAAgE,eAAA;IAClB,IAAII,qBAAqB;IACzBD,MAAA,GAAAvE,UAAA,OAAAoE,eAAA,GAAME,MAAM;IACZC,MAAA,CAAKE,iBAAiB,GAAG,YAAM;MAC7B,OAAOF,MAAA,CAAKG,cAAc;IAC5B,CAAC;IACDH,MAAA,CAAKI,0BAA0B,GAAG,YAAM;MACtC,IAAIJ,MAAA,CAAKK,4BAA4B,CAAC,CAAC,EAAE;QACvC,OAAOL,MAAA,CAAKM,OAAO,CAACC,yBAAyB,CAAC,CAAC;MACjD,CAAC,MAAM;QACL,OAAAP,MAAA;MACF;IACF,CAAC;IACDA,MAAA,CAAKQ,sBAAsB,GAAG,UAAAC,SAAS,EAAI;MACzCT,MAAA,CAAKU,iBAAiB,CAACC,GAAG,CAACF,SAAS,CAACG,GAAG,EAAEH,SAAS,CAACI,OAAO,CAAC;MAC5D,IAAIb,MAAA,CAAKc,cAAc,EAAE;QACvBL,SAAS,CAACG,GAAG,CAACG,iBAAiB,CAAC,CAAC;MACnC;IACF,CAAC;IACDf,MAAA,CAAKgB,wBAAwB,GAAG,UAAAP,SAAS,EAAI;MAC3CT,MAAA,CAAKU,iBAAiB,CAACO,MAAM,CAACR,SAAS,CAACG,GAAG,CAAC;IAC9C,CAAC;IACDZ,MAAA,CAAKkB,mBAAmB,GAAG,UAACC,IAAI,EAAEC,QAAQ,EAAK;MAC7CD,IAAI,CAACE,OAAO,CAAC,UAAAC,GAAG,EAAI;QAClB,IAAIV,GAAG,GAAGU,GAAG,IAAI,IAAI,IAAItB,MAAA,CAAKuB,SAAS,CAACD,GAAG,CAAC;QAC5CV,GAAG,IAAIA,GAAG,CAACY,oBAAoB,CAACJ,QAAQ,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC;IACDpB,MAAA,CAAKyB,aAAa,GAAG,UAAAC,UAAU;MAAA,OAAIA,UAAU,GAAG,QAAQ,GAAG,OAAO;IAAA;IAClE1B,MAAA,CAAK2B,kBAAkB,GAAG,CAAC;IAC3B3B,MAAA,CAAKuB,SAAS,GAAG,CAAC,CAAC;IACnBvB,MAAA,CAAK4B,OAAO,GAAG,CAAC,CAAC;IACjB5B,MAAA,CAAK6B,aAAa,GAAG,CAAC;IACtB7B,MAAA,CAAK8B,iCAAiC,GAAG,KAAK;IAC9C9B,MAAA,CAAKc,cAAc,GAAG,KAAK;IAC3Bd,MAAA,CAAK+B,QAAQ,GAAG,KAAK;IACrB/B,MAAA,CAAKgC,UAAU,GAAG,CAAC,CAAC;IACpBhC,MAAA,CAAKiC,aAAa,GAAG,CAAC;IACtBjC,MAAA,CAAKkC,gBAAgB,GAAG,KAAK;IAC7BlC,MAAA,CAAKmC,0BAA0B,GAAG,CAAC;IACnCnC,MAAA,CAAKoC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/BrC,MAAA,CAAKsC,mBAAmB,GAAG,IAAI;IAC/BtC,MAAA,CAAKU,iBAAiB,GAAG,IAAIpD,oBAAoB,CAACzB,OAAO,CAAC,CAAC;IAC3DmE,MAAA,CAAKuC,gCAAgC,GAAG,CAAC;IACzCvC,MAAA,CAAKwC,iBAAiB,GAAG,CAAC;IAC1BxC,MAAA,CAAKG,cAAc,GAAG;MACpBsC,aAAa,EAAE,CAAC;MAChBC,OAAO,EAAE,CAAC;MACVC,EAAE,EAAE,EAAE;MACNC,MAAM,EAAE,CAAC;MACTC,SAAS,EAAE,CAAC;MACZC,QAAQ,EAAE,CAAC;MACX3D,aAAa,EAAE,CAAC;MAChB4D,SAAS,EAAE;IACb,CAAC;IACD/C,MAAA,CAAKgD,UAAU,GAAG,IAAI;IACtBhD,MAAA,CAAKiD,0BAA0B,GAAG,CAAC;IACnCjD,MAAA,CAAKkD,wBAAwB,GAAG,CAAC;IACjClD,MAAA,CAAKmD,gBAAgB,GAAG,CAAC;IACzBnD,MAAA,CAAKoD,mBAAmB,GAAG,CAAC;IAC5BpD,MAAA,CAAKqD,kBAAkB,GAAG,EAAE;IAC5BrD,MAAA,CAAKsD,iBAAiB,GAAG,UAAA1C,GAAG,EAAI;MAC9BZ,MAAA,CAAKgD,UAAU,GAAGpC,GAAG;IACvB,CAAC;IACDZ,MAAA,CAAKuD,6BAA6B,GAAG,UAAAC,KAAK,EAAI;MAC5C,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;MAC/B,IAAIzD,MAAA,CAAKK,4BAA4B,CAAC,CAAC,EAAE;QAEvC,OAAoBtC,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE2H,KAAK,CAAC;MAC/D,CAAC,MAAM,IAAIC,SAAS,EAAE;QACpB,IAAIE,iBAAiB;QACrB,CAAC,CAAC,EAAE9F,UAAU,CAAChC,OAAO,EAAE,OAAO2H,KAAK,CAACI,UAAU,KAAK,SAAS,EAAE,mFAAmF,GAAGC,IAAI,CAACC,SAAS,CAAC,CAACH,iBAAiB,GAAGH,KAAK,CAACI,UAAU,MAAM,IAAI,IAAID,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,WAAW,CAAC,GAAG,GAAG,CAAC;QAC7R,QAIE5F,KAAK,CAAC2F,aAAa,CAAC3G,WAAW,CAAClB,OAAO,EAAE,CAAC,CAAC,EAAEe,SAAS,CAACf,OAAO,EAAE,CAAC,CAAC,EAAE2H,KAAK,EAAE;YACzEO,cAAc,EAAEP,KAAK,CAACO,cAAc,IAAI,IAAI,GAAgBhG,KAAK,CAAC2F,aAAa,CAAC5G,eAAe,CAACjB,OAAO,EAErG;cACA+H,UAAU,EAAEJ,KAAK,CAACI,UAAU;cAC5BH,SAAS,EAAEA,SAAS;cACpBO,kBAAkB,EAAER,KAAK,CAACQ;YAC5B,CAAC,CAAC,GAAGR,KAAK,CAACO;UACb,CAAC,CAAC;QAAC;MAEP,CAAC,MAAM;QAGL,OAAoBhG,KAAK,CAAC2F,aAAa,CAAC3G,WAAW,CAAClB,OAAO,EAAE2H,KAAK,CAAC;MACrE;IACF,CAAC;IACDxD,MAAA,CAAKiE,aAAa,GAAG,UAACrI,CAAC,EAAEiF,OAAO,EAAEqD,KAAK,EAAK;MAC1C,IAAIC,MAAM,GAAGvI,CAAC,CAACwI,WAAW,CAACD,MAAM;MACjC,IAAIE,IAAI,GAAG;QACTzB,MAAM,EAAE5C,MAAA,CAAKsE,aAAa,CAACH,MAAM,CAAC;QAClCvE,MAAM,EAAEI,MAAA,CAAKuE,aAAa,CAACJ,MAAM,CAAC;QAClCD,KAAK,EAALA,KAAK;QACLM,QAAQ,EAAE;MACZ,CAAC;MACD,IAAIC,IAAI,GAAGzE,MAAA,CAAK4B,OAAO,CAACf,OAAO,CAAC;MAChC,IAAI,CAAC4D,IAAI,IAAIJ,IAAI,CAACzB,MAAM,KAAK6B,IAAI,CAAC7B,MAAM,IAAIyB,IAAI,CAACzE,MAAM,KAAK6E,IAAI,CAAC7E,MAAM,IAAIsE,KAAK,KAAKO,IAAI,CAACP,KAAK,EAAE;QAC/FlE,MAAA,CAAKmD,gBAAgB,IAAIkB,IAAI,CAACzE,MAAM,IAAI6E,IAAI,GAAGA,IAAI,CAAC7E,MAAM,GAAG,CAAC,CAAC;QAC/DI,MAAA,CAAKoD,mBAAmB,IAAIqB,IAAI,GAAG,CAAC,GAAG,CAAC;QACxCzE,MAAA,CAAK2B,kBAAkB,GAAG3B,MAAA,CAAKmD,gBAAgB,GAAGnD,MAAA,CAAKoD,mBAAmB;QAC1EpD,MAAA,CAAK4B,OAAO,CAACf,OAAO,CAAC,GAAGwD,IAAI;QAC5BrE,MAAA,CAAKmC,0BAA0B,GAAGuC,IAAI,CAACC,GAAG,CAAC3E,MAAA,CAAKmC,0BAA0B,EAAE+B,KAAK,CAAC;QAClFlE,MAAA,CAAK4E,4BAA4B,CAAC,CAAC;MACrC,CAAC,MAAM;QACL5E,MAAA,CAAK4B,OAAO,CAACf,OAAO,CAAC,CAAC2D,QAAQ,GAAG,IAAI;MACvC;MACAxE,MAAA,CAAK6E,oCAAoC,CAAChE,OAAO,CAAC;MAClDb,MAAA,CAAK8E,iBAAiB,CAAC,CAAC;MACxB9E,MAAA,CAAK+E,oBAAoB,CAAC/E,MAAA,CAAKwD,KAAK,EAAExD,MAAA,CAAKgF,KAAK,CAACC,mBAAmB,CAAC;IACvE,CAAC;IACDjF,MAAA,CAAKkF,cAAc,GAAG,UAAArE,OAAO,EAAI;MAC/B,OAAOb,MAAA,CAAKuB,SAAS,CAACV,OAAO,CAAC;MAC9B,IAAI4D,IAAI,GAAGzE,MAAA,CAAK4B,OAAO,CAACf,OAAO,CAAC;MAChC,IAAI4D,IAAI,EAAE;QACRzE,MAAA,CAAK4B,OAAO,CAACf,OAAO,CAAC,GAAG,CAAC,CAAC,EAAEhE,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEgB,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAE4I,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7FD,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC;IACDxE,MAAA,CAAKmF,SAAS,GAAG,UAAAvJ,CAAC,EAAI;MACpB,IAAIoE,MAAA,CAAKK,4BAA4B,CAAC,CAAC,EAAE;QAGvCL,MAAA,CAAKoF,qCAAqC,CAAC,CAAC;MAC9C,CAAC,MAAM;QACLpF,MAAA,CAAKG,cAAc,CAAChB,aAAa,GAAGa,MAAA,CAAKuE,aAAa,CAAC3I,CAAC,CAACwI,WAAW,CAACD,MAAM,CAAC;MAC9E;MACAnE,MAAA,CAAKwD,KAAK,CAAC6B,QAAQ,IAAIrF,MAAA,CAAKwD,KAAK,CAAC6B,QAAQ,CAACzJ,CAAC,CAAC;MAC7CoE,MAAA,CAAK4E,4BAA4B,CAAC,CAAC;MACnC5E,MAAA,CAAKsF,uBAAuB,CAAC,CAAC;IAChC,CAAC;IACDtF,MAAA,CAAKuF,cAAc,GAAG,UAAA3J,CAAC,EAAI;MACzBoE,MAAA,CAAKwD,KAAK,CAAC6B,QAAQ,IAAIrF,MAAA,CAAKwD,KAAK,CAAC6B,QAAQ,CAACzJ,CAAC,CAAC;IAC/C,CAAC;IACDoE,MAAA,CAAKwF,eAAe,GAAG,UAAA5J,CAAC,EAAI;MAC1BoE,MAAA,CAAK6E,oCAAoC,CAAC7E,MAAA,CAAKyF,iBAAiB,CAAC,CAAC,CAAC;MACnEzF,MAAA,CAAK6B,aAAa,GAAG7B,MAAA,CAAKuE,aAAa,CAAC3I,CAAC,CAACwI,WAAW,CAACD,MAAM,CAAC;IAC/D,CAAC;IACDnE,MAAA,CAAK0F,eAAe,GAAG,UAAA9J,CAAC,EAAI;MAC1BoE,MAAA,CAAKiC,aAAa,GAAGjC,MAAA,CAAKuE,aAAa,CAAC3I,CAAC,CAACwI,WAAW,CAACD,MAAM,CAAC;IAC/D,CAAC;IACDnE,MAAA,CAAK2F,oBAAoB,GAAG,UAACC,KAAK,EAAEC,MAAM,EAAK;MAC7C,IAAID,KAAK,GAAG,CAAC,IAAIC,MAAM,GAAG,CAAC,IAAI7F,MAAA,CAAKwD,KAAK,CAACsC,kBAAkB,IAAI,IAAI,IAAI9F,MAAA,CAAKwD,KAAK,CAACsC,kBAAkB,GAAG,CAAC,IAAI,CAAC9F,MAAA,CAAK8B,iCAAiC,EAAE;QACpJ,IAAI9B,MAAA,CAAKwD,KAAK,CAACuC,aAAa,IAAI,IAAI,EAAE;UACpC,IAAI/F,MAAA,CAAKwD,KAAK,CAACsC,kBAAkB,GAAG9F,MAAA,CAAKwD,KAAK,CAACwC,YAAY,CAAChG,MAAA,CAAKwD,KAAK,CAACyC,IAAI,CAAC,EAAE;YAC5EjG,MAAA,CAAKkG,aAAa,CAAC;cACjBC,QAAQ,EAAE,KAAK;cACfjC,KAAK,EAAE,CAAC,CAAC,EAAEpG,WAAW,CAACjC,OAAO,EAAEmE,MAAA,CAAKwD,KAAK,CAACsC,kBAAkB;YAC/D,CAAC,CAAC;UACJ,CAAC,MAAM;YACL9F,MAAA,CAAKoG,WAAW,CAAC;cACfD,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ;QACF;QACAnG,MAAA,CAAK8B,iCAAiC,GAAG,IAAI;MAC/C;MACA,IAAI9B,MAAA,CAAKwD,KAAK,CAAC6C,mBAAmB,EAAE;QAClCrG,MAAA,CAAKwD,KAAK,CAAC6C,mBAAmB,CAACT,KAAK,EAAEC,MAAM,CAAC;MAC/C;MACA7F,MAAA,CAAKG,cAAc,CAACsC,aAAa,GAAGzC,MAAA,CAAKuE,aAAa,CAAC;QACrDsB,MAAM,EAANA,MAAM;QACND,KAAK,EAALA;MACF,CAAC,CAAC;MACF5F,MAAA,CAAK4E,4BAA4B,CAAC,CAAC;MACnC5E,MAAA,CAAKsF,uBAAuB,CAAC,CAAC;IAChC,CAAC;IACDtF,MAAA,CAAKsG,2BAA2B,GAAG,UAAAC,OAAO,EAAI;MAE5C,IAAI3D,MAAM,GAAG2D,OAAO,CAAC3D,MAAM,GAAG5C,MAAA,CAAKuC,gCAAgC;MAEnE,IAAIpD,aAAa,GAAGoH,OAAO,CAACpH,aAAa;MACzC,IAAIuD,OAAO,GAAGE,MAAM,GAAG5C,MAAA,CAAKG,cAAc,CAACyC,MAAM;MACjD,IAAIH,aAAa,GAAGzC,MAAA,CAAKG,cAAc,CAACsC,aAAa;MACrD,OAAO;QACLtD,aAAa,EAAbA,aAAa;QACbsD,aAAa,EAAbA,aAAa;QACbG,MAAM,EAANA,MAAM;QACNF,OAAO,EAAPA;MACF,CAAC;IACH,CAAC;IACD1C,MAAA,CAAKwG,SAAS,GAAG,UAAA5K,CAAC,EAAI;MACpBoE,MAAA,CAAKU,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAAC+F,SAAS,CAAC5K,CAAC,CAAC;MACxB,CAAC,CAAC;MACF,IAAIoE,MAAA,CAAKwD,KAAK,CAACiD,QAAQ,EAAE;QACvBzG,MAAA,CAAKwD,KAAK,CAACiD,QAAQ,CAAC7K,CAAC,CAAC;MACxB;MACA,IAAIiH,SAAS,GAAGjH,CAAC,CAAC8K,SAAS;MAC3B,IAAIvH,aAAa,GAAGa,MAAA,CAAKuE,aAAa,CAAC3I,CAAC,CAACwI,WAAW,CAACuC,iBAAiB,CAAC;MACvE,IAAIlE,aAAa,GAAGzC,MAAA,CAAKuE,aAAa,CAAC3I,CAAC,CAACwI,WAAW,CAACwC,WAAW,CAAC;MACjE,IAAIhE,MAAM,GAAG5C,MAAA,CAAKsE,aAAa,CAAC1I,CAAC,CAACwI,WAAW,CAAC2B,aAAa,CAAC;MAC5D,IAAIrD,OAAO,GAAGE,MAAM,GAAG5C,MAAA,CAAKG,cAAc,CAACyC,MAAM;MACjD,IAAI5C,MAAA,CAAKK,4BAA4B,CAAC,CAAC,EAAE;QACvC,IAAIL,MAAA,CAAKG,cAAc,CAACsC,aAAa,KAAK,CAAC,EAAE;UAG3C;QACF;QACA,IAAIoE,qBAAqB,GAAG7G,MAAA,CAAKsG,2BAA2B,CAAC;UAC3DnH,aAAa,EAAbA,aAAa;UACbyD,MAAM,EAANA;QACF,CAAC,CAAC;QACFzD,aAAa,GAAG0H,qBAAqB,CAAC1H,aAAa;QACnDsD,aAAa,GAAGoE,qBAAqB,CAACpE,aAAa;QACnDG,MAAM,GAAGiE,qBAAqB,CAACjE,MAAM;QACrCF,OAAO,GAAGmE,qBAAqB,CAACnE,OAAO;MACzC;MACA,IAAIC,EAAE,GAAG3C,MAAA,CAAKG,cAAc,CAAC0C,SAAS,GAAG6B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE9B,SAAS,GAAG7C,MAAA,CAAKG,cAAc,CAAC0C,SAAS,CAAC,GAAG,CAAC;MACnG,IAAIC,QAAQ,GAAGJ,OAAO,GAAGC,EAAE;MAC3B,IAAIA,EAAE,GAAG,GAAG,IAAI3C,MAAA,CAAKG,cAAc,CAACwC,EAAE,GAAG,GAAG,IAAIF,aAAa,GAAG,CAAC,GAAGtD,aAAa,IAAI,CAACa,MAAA,CAAKgC,UAAU,CAAC8E,IAAI,EAAE;QAC1G,CAAC,CAAC,EAAE1J,QAAQ,CAACvB,OAAO,EAAE,iFAAiF,GAAG,sFAAsF,GAAG,iDAAiD,EAAE;UACpP8G,EAAE,EAAFA,EAAE;UACFoE,MAAM,EAAE/G,MAAA,CAAKG,cAAc,CAACwC,EAAE;UAC9BF,aAAa,EAAbA;QACF,CAAC,CAAC;QACFzC,MAAA,CAAKgC,UAAU,CAAC8E,IAAI,GAAG,IAAI;MAC7B;MAGA,IAAI/D,SAAS,GAAGnH,CAAC,CAACwI,WAAW,CAACrB,SAAS,GAAG,CAAC,GAAG,CAAC,GAAGnH,CAAC,CAACwI,WAAW,CAACrB,SAAS;MACzE/C,MAAA,CAAKG,cAAc,GAAG;QACpBsC,aAAa,EAAbA,aAAa;QACbE,EAAE,EAAFA,EAAE;QACFD,OAAO,EAAPA,OAAO;QACPE,MAAM,EAANA,MAAM;QACNC,SAAS,EAATA,SAAS;QACTC,QAAQ,EAARA,QAAQ;QACR3D,aAAa,EAAbA,aAAa;QACb4D,SAAS,EAATA;MACF,CAAC;MACD/C,MAAA,CAAK+E,oBAAoB,CAAC/E,MAAA,CAAKwD,KAAK,EAAExD,MAAA,CAAKgF,KAAK,CAACC,mBAAmB,CAAC;MACrE,IAAI,CAACjF,MAAA,CAAKwD,KAAK,EAAE;QACf;MACF;MACAxD,MAAA,CAAKsF,uBAAuB,CAAC,CAAC;MAC9B,IAAIxC,QAAQ,KAAK,CAAC,EAAE;QAClB9C,MAAA,CAAKgH,eAAe,CAACC,QAAQ,CAAC,CAAC;MACjC;MACAjH,MAAA,CAAK8E,iBAAiB,CAAC,CAAC;MACxB9E,MAAA,CAAK4E,4BAA4B,CAAC,CAAC;IACrC,CAAC;IACD5E,MAAA,CAAKkH,kBAAkB,GAAG,UAAAtL,CAAC,EAAI;MAC7BoE,MAAA,CAAKU,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAACyG,kBAAkB,CAACtL,CAAC,CAAC;MACjC,CAAC,CAAC;MACFoE,MAAA,CAAKqD,kBAAkB,CAAChC,OAAO,CAAC,UAAA8F,KAAK,EAAI;QACvCA,KAAK,CAACC,iBAAiB,CAACrG,iBAAiB,CAAC,CAAC;MAC7C,CAAC,CAAC;MACFf,MAAA,CAAKc,cAAc,GAAG,IAAI;MAC1Bd,MAAA,CAAKwD,KAAK,CAAC6D,iBAAiB,IAAIrH,MAAA,CAAKwD,KAAK,CAAC6D,iBAAiB,CAACzL,CAAC,CAAC;IACjE,CAAC;IACDoE,MAAA,CAAKsH,gBAAgB,GAAG,UAAA1L,CAAC,EAAI;MAC3BoE,MAAA,CAAKU,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAAC6G,gBAAgB,CAAC1L,CAAC,CAAC;MAC/B,CAAC,CAAC;MACF,IAAIkH,QAAQ,GAAGlH,CAAC,CAACwI,WAAW,CAACtB,QAAQ;MACrC,IAAIA,QAAQ,EAAE;QACZ9C,MAAA,CAAKG,cAAc,CAAC2C,QAAQ,GAAG9C,MAAA,CAAKsE,aAAa,CAACxB,QAAQ,CAAC;MAC7D;MACA9C,MAAA,CAAK8E,iBAAiB,CAAC,CAAC;MACxB9E,MAAA,CAAKwD,KAAK,CAAC+D,eAAe,IAAIvH,MAAA,CAAKwD,KAAK,CAAC+D,eAAe,CAAC3L,CAAC,CAAC;IAC7D,CAAC;IACDoE,MAAA,CAAKwH,sBAAsB,GAAG,UAAA5L,CAAC,EAAI;MACjCoE,MAAA,CAAKU,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAAC+G,sBAAsB,CAAC5L,CAAC,CAAC;MACrC,CAAC,CAAC;MACFoE,MAAA,CAAKwD,KAAK,CAACiE,qBAAqB,IAAIzH,MAAA,CAAKwD,KAAK,CAACiE,qBAAqB,CAAC7L,CAAC,CAAC;IACzE,CAAC;IACDoE,MAAA,CAAK0H,oBAAoB,GAAG,UAAA9L,CAAC,EAAI;MAC/BoE,MAAA,CAAKU,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAACiH,oBAAoB,CAAC9L,CAAC,CAAC;MACnC,CAAC,CAAC;MACFoE,MAAA,CAAKG,cAAc,CAAC2C,QAAQ,GAAG,CAAC;MAChC9C,MAAA,CAAK8E,iBAAiB,CAAC,CAAC;MACxB9E,MAAA,CAAKwD,KAAK,CAACmE,mBAAmB,IAAI3H,MAAA,CAAKwD,KAAK,CAACmE,mBAAmB,CAAC/L,CAAC,CAAC;IACrE,CAAC;IACDoE,MAAA,CAAK4H,oBAAoB,GAAG,YAAM;MAChC5H,MAAA,CAAK+E,oBAAoB,CAAC/E,MAAA,CAAKwD,KAAK,EAAExD,MAAA,CAAKgF,KAAK,CAACC,mBAAmB,CAAC;MACrEjF,MAAA,CAAK6H,QAAQ,CAAC,UAAC7C,KAAK,EAAExB,KAAK,EAAK;QAC9B,IAAIyB,mBAAmB,GAAGjF,MAAA,CAAK8H,0BAA0B,CAACtE,KAAK,EAAEwB,KAAK,CAACC,mBAAmB,CAAC;QAC3F,IAAI8C,UAAU,GAAGlI,eAAe,CAACmI,iBAAiB,CAACxE,KAAK,EAAEyB,mBAAmB,EAAEjF,MAAA,CAAKiI,4BAA4B,CAACzE,KAAK,CAAC,CAAC;QACxH,IAAIyB,mBAAmB,CAACiD,KAAK,KAAKlD,KAAK,CAACC,mBAAmB,CAACiD,KAAK,IAAIjD,mBAAmB,CAACkD,IAAI,KAAKnD,KAAK,CAACC,mBAAmB,CAACkD,IAAI,IAAIJ,UAAU,CAACK,MAAM,CAACpD,KAAK,CAAC+C,UAAU,CAAC,EAAE;UACvK,OAAO,IAAI;QACb;QACA,OAAO;UACL9C,mBAAmB,EAAnBA,mBAAmB;UACnB8C,UAAU,EAAVA;QACF,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;IACD/H,MAAA,CAAKqI,gBAAgB,GAAG,UAACnE,KAAK,EAAEoE,UAAU,EAAE9E,KAAK,EAE5C;MACH,IAAIyC,IAAI,GAAGzC,KAAK,CAACyC,IAAI;QACnBsC,OAAO,GAAG/E,KAAK,CAAC+E,OAAO;MACzB,IAAIC,IAAI,GAAGD,OAAO,CAACtC,IAAI,EAAE/B,KAAK,CAAC;MAC/B,OAAO;QACLA,KAAK,EAALA,KAAK;QACLsE,IAAI,EAAJA,IAAI;QACJlH,GAAG,EAAEtB,MAAA,CAAKyI,aAAa,CAACD,IAAI,EAAEtE,KAAK,EAAEV,KAAK,CAAC;QAC3C8E,UAAU,EAAVA;MACF,CAAC;IACH,CAAC;IACDtI,MAAA,CAAK0I,gBAAgB,GAAG,UAACxE,KAAK,EAAEV,KAAK,EAAK;MACxC,IAAImF,MAAM,CAACC,SAAS,CAAC1E,KAAK,CAAC,EAAE;QAC3B,OAAOlE,MAAA,CAAK6I,uBAAuB,CAAC3E,KAAK,EAAEV,KAAK,CAAC,CAACZ,MAAM;MAC1D,CAAC,MAAM;QACL,IAAIkG,YAAY,GAAG9I,MAAA,CAAK6I,uBAAuB,CAACnE,IAAI,CAACqE,KAAK,CAAC7E,KAAK,CAAC,EAAEV,KAAK,CAAC;QACzE,IAAIwF,SAAS,GAAG9E,KAAK,GAAGQ,IAAI,CAACqE,KAAK,CAAC7E,KAAK,CAAC;QACzC,OAAO4E,YAAY,CAAClG,MAAM,GAAGoG,SAAS,GAAGF,YAAY,CAAClJ,MAAM;MAC9D;IACF,CAAC;IACDI,MAAA,CAAK6I,uBAAuB,GAAG,UAAC3E,KAAK,EAAEV,KAAK,EAAK;MAC/C,IAAIyF,KAAK,GAAGjJ,MAAA,CAAKkJ,gBAAgB,CAAChF,KAAK,EAAEV,KAAK,CAAC;MAC/C,IAAIyF,KAAK,IAAIA,KAAK,CAAC/E,KAAK,KAAKA,KAAK,EAAE;QAElC,OAAO+E,KAAK;MACd,CAAC,MAAM;QACL,IAAIhD,IAAI,GAAGzC,KAAK,CAACyC,IAAI;UACnBD,YAAY,GAAGxC,KAAK,CAACwC,YAAY;UACjCmD,aAAa,GAAG3F,KAAK,CAAC2F,aAAa;QACrC,CAAC,CAAC,EAAEtL,UAAU,CAAChC,OAAO,EAAEqI,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG8B,YAAY,CAACC,IAAI,CAAC,EAAE,4CAA4C,GAAG/B,KAAK,CAAC;QACvH,CAAC,CAAC,EAAErG,UAAU,CAAChC,OAAO,EAAE,CAACsN,aAAa,EAAE,oFAAoF,CAAC;QAC7H,OAAO;UACLvJ,MAAM,EAAEI,MAAA,CAAK2B,kBAAkB;UAC/BiB,MAAM,EAAE5C,MAAA,CAAK2B,kBAAkB,GAAGuC;QACpC,CAAC;MACH;IACF,CAAC;IACDlE,MAAA,CAAKkJ,gBAAgB,GAAG,UAAChF,KAAK,EAAEV,KAAK,EAAK;MACxC,IAAIyC,IAAI,GAAGzC,KAAK,CAACyC,IAAI;QACnBsC,OAAO,GAAG/E,KAAK,CAAC+E,OAAO;QACvBvC,YAAY,GAAGxC,KAAK,CAACwC,YAAY;QACjCmD,aAAa,GAAG3F,KAAK,CAAC2F,aAAa;MACrC,CAAC,CAAC,EAAEtL,UAAU,CAAChC,OAAO,EAAEqI,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG8B,YAAY,CAACC,IAAI,CAAC,EAAE,4CAA4C,GAAG/B,KAAK,CAAC;MACvH,IAAIsE,IAAI,GAAGD,OAAO,CAACtC,IAAI,EAAE/B,KAAK,CAAC;MAC/B,IAAI+E,KAAK,GAAGjJ,MAAA,CAAK4B,OAAO,CAAC5B,MAAA,CAAKyI,aAAa,CAACD,IAAI,EAAEtE,KAAK,EAAEV,KAAK,CAAC,CAAC;MAChE,IAAI,CAACyF,KAAK,IAAIA,KAAK,CAAC/E,KAAK,KAAKA,KAAK,EAAE;QACnC,IAAIiF,aAAa,EAAE;UAIjB,OAAOA,aAAa,CAAClD,IAAI,EAAE/B,KAAK,CAAC;QACnC;MACF;MACA,OAAO+E,KAAK;IACd,CAAC;IACDjJ,MAAA,CAAKiI,4BAA4B,GAAG,UAAAzE,KAAK,EAAI;MAI3C,IAAI,EAAExD,MAAA,CAAKsC,mBAAmB,IAAItC,MAAA,CAAKuB,SAAS,CAACvB,MAAA,CAAKsC,mBAAmB,CAAC,CAAC,EAAE;QAC3E,OAAO,EAAE;MACX;MACA,IAAI8G,uBAAuB,GAAGpJ,MAAA,CAAKuB,SAAS,CAACvB,MAAA,CAAKsC,mBAAmB,CAAC;MACtE,IAAI+G,gBAAgB,GAAGD,uBAAuB,CAAC5F,KAAK,CAACU,KAAK;MAC1D,IAAIoF,SAAS,GAAG9F,KAAK,CAACwC,YAAY,CAACxC,KAAK,CAACyC,IAAI,CAAC;MAI9C,IAAIoD,gBAAgB,IAAIC,SAAS,IAAItJ,MAAA,CAAKyI,aAAa,CAACjF,KAAK,CAAC+E,OAAO,CAAC/E,KAAK,CAACyC,IAAI,EAAEoD,gBAAgB,CAAC,EAAEA,gBAAgB,EAAE7F,KAAK,CAAC,KAAKxD,MAAA,CAAKsC,mBAAmB,EAAE;QAC1J,OAAO,EAAE;MACX;MACA,IAAI4F,KAAK,GAAGmB,gBAAgB;MAC5B,IAAIE,0BAA0B,GAAG,CAAC;MAClC,KAAK,IAAI5J,CAAC,GAAGuI,KAAK,GAAG,CAAC,EAAEvI,CAAC,IAAI,CAAC,IAAI4J,0BAA0B,GAAGvJ,MAAA,CAAKG,cAAc,CAAChB,aAAa,EAAEQ,CAAC,EAAE,EAAE;QACrGuI,KAAK,EAAE;QACPqB,0BAA0B,IAAIvJ,MAAA,CAAK6I,uBAAuB,CAAClJ,CAAC,EAAE6D,KAAK,CAAC,CAAC5D,MAAM;MAC7E;MACA,IAAIuI,IAAI,GAAGkB,gBAAgB;MAC3B,IAAIG,yBAAyB,GAAG,CAAC;MACjC,KAAK,IAAIC,EAAE,GAAGtB,IAAI,GAAG,CAAC,EAAEsB,EAAE,GAAGH,SAAS,IAAIE,yBAAyB,GAAGxJ,MAAA,CAAKG,cAAc,CAAChB,aAAa,EAAEsK,EAAE,EAAE,EAAE;QAC7GtB,IAAI,EAAE;QACNqB,yBAAyB,IAAIxJ,MAAA,CAAK6I,uBAAuB,CAACY,EAAE,EAAEjG,KAAK,CAAC,CAAC5D,MAAM;MAC7E;MACA,OAAO,CAAC;QACNsI,KAAK,EAALA,KAAK;QACLC,IAAI,EAAJA;MACF,CAAC,CAAC;IACJ,CAAC;IACDnI,MAAA,CAAK0J,WAAW,CAAC3J,MAAM,CAAC;IACxBC,MAAA,CAAKgH,eAAe,GAAG,IAAIzJ,eAAe,CAAC1B,OAAO,CAACmE,MAAA,CAAKkJ,gBAAgB,CAAC;IACzElJ,MAAA,CAAK2J,2BAA2B,GAAG,IAAIzM,YAAY,CAACrB,OAAO,CAACmE,MAAA,CAAK4H,oBAAoB,EAAE,CAAC3H,qBAAqB,GAAGD,MAAA,CAAKwD,KAAK,CAACoG,yBAAyB,MAAM,IAAI,IAAI3J,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,EAAE,CAAC;IAChO,IAAID,MAAA,CAAKwD,KAAK,CAACqG,8BAA8B,EAAE;MAC7C7J,MAAA,CAAKqD,kBAAkB,GAAGrD,MAAA,CAAKwD,KAAK,CAACqG,8BAA8B,CAACC,GAAG,CAAC,UAAAC,IAAI;QAAA,OAAK;UAC/E3C,iBAAiB,EAAE,IAAI3J,kBAAkB,CAAC5B,OAAO,CAACkO,IAAI,CAACC,iBAAiB,CAAC;UACzEC,sBAAsB,EAAEF,IAAI,CAACE;QAC/B,CAAC;MAAA,CAAC,CAAC;IACL,CAAC,MAAM;MACL,IAAIC,YAAY,GAAGlK,MAAA,CAAKwD,KAAK;QAC3ByG,sBAAsB,GAAGC,YAAY,CAACD,sBAAsB;QAC5DD,iBAAiB,GAAGE,YAAY,CAACF,iBAAiB;MACpD,IAAIC,sBAAsB,EAAE;QAC1BjK,MAAA,CAAKqD,kBAAkB,CAAC8G,IAAI,CAAC;UAC3B/C,iBAAiB,EAAE,IAAI3J,kBAAkB,CAAC5B,OAAO,CAACmO,iBAAiB,CAAC;UACpEC,sBAAsB,EAAEA;QAC1B,CAAC,CAAC;MACJ;IACF;IACA,IAAIG,mBAAmB,GAAGvK,eAAe,CAACwK,oBAAoB,CAACtK,MAAM,CAAC;IACtEC,MAAA,CAAKgF,KAAK,GAAG;MACXC,mBAAmB,EAAEmF,mBAAmB;MACxCrC,UAAU,EAAElI,eAAe,CAACmI,iBAAiB,CAACjI,MAAM,EAAEqK,mBAAmB;IAC3E,CAAC;IAIDpK,MAAA,CAAKsK,yBAAyB,GAAG,UAAAC,EAAE,EAAI;MACrC,IAAIC,YAAY,GAAGxK,MAAA,CAAKwD,KAAK,CAAChF,UAAU,GAAG+L,EAAE,CAACE,MAAM,CAACC,UAAU,GAAGH,EAAE,CAACE,MAAM,CAACE,SAAS;MACrF,IAAIC,YAAY,GAAG5K,MAAA,CAAKwD,KAAK,CAAChF,UAAU,GAAG+L,EAAE,CAACE,MAAM,CAACI,WAAW,GAAGN,EAAE,CAACE,MAAM,CAACK,YAAY;MACzF,IAAIC,YAAY,GAAG/K,MAAA,CAAKwD,KAAK,CAAChF,UAAU,GAAG+L,EAAE,CAACE,MAAM,CAACO,WAAW,GAAGT,EAAE,CAACE,MAAM,CAACQ,YAAY;MACzF,IAAIC,uBAAuB,GAAGN,YAAY,GAAGG,YAAY;MACzD,IAAII,KAAK,GAAGnL,MAAA,CAAKwD,KAAK,CAAChF,UAAU,GAAG+L,EAAE,CAACa,MAAM,IAAIb,EAAE,CAACc,WAAW,GAAGd,EAAE,CAACe,MAAM,IAAIf,EAAE,CAACgB,WAAW;MAC7F,IAAIC,aAAa,GAAGL,KAAK;MACzB,IAAID,uBAAuB,EAAE;QAC3BM,aAAa,GAAGL,KAAK,GAAG,CAAC,GAAGzG,IAAI,CAAC+G,GAAG,CAACN,KAAK,GAAGX,YAAY,EAAE,CAAC,CAAC,GAAG9F,IAAI,CAACC,GAAG,CAACwG,KAAK,IAAIP,YAAY,GAAGG,YAAY,GAAGP,YAAY,CAAC,EAAE,CAAC,CAAC;MACnI;MACA,IAAIkB,WAAW,GAAGP,KAAK,GAAGK,aAAa;MACvC,IAAIxL,MAAA,CAAKwD,KAAK,CAACmI,QAAQ,IAAI3L,MAAA,CAAKgD,UAAU,IAAIhD,MAAA,CAAKgD,UAAU,CAAC4I,iBAAiB,EAAE;QAC/E,IAAIC,IAAI,GAAG7L,MAAA,CAAKgD,UAAU,CAAC4I,iBAAiB,CAAC,CAAC;QAC9C,IAAI5L,MAAA,CAAKwD,KAAK,CAAChF,UAAU,EAAE;UACzB+L,EAAE,CAACE,MAAM,CAACC,UAAU,IAAIgB,WAAW;UACnC,IAAII,cAAc,GAAGD,IAAI,CAACnB,UAAU,GAAGc,aAAa;UACpDK,IAAI,CAACnB,UAAU,GAAG,CAAC1K,MAAA,CAAKwD,KAAK,CAAC2F,aAAa,GAAGzE,IAAI,CAAC+G,GAAG,CAACK,cAAc,EAAE9L,MAAA,CAAKmD,gBAAgB,CAAC,GAAG2I,cAAc;QAChH,CAAC,MAAM;UACLvB,EAAE,CAACE,MAAM,CAACE,SAAS,IAAIe,WAAW;UAClC,IAAIK,aAAa,GAAGF,IAAI,CAAClB,SAAS,GAAGa,aAAa;UAClDK,IAAI,CAAClB,SAAS,GAAG,CAAC3K,MAAA,CAAKwD,KAAK,CAAC2F,aAAa,GAAGzE,IAAI,CAAC+G,GAAG,CAACM,aAAa,EAAE/L,MAAA,CAAKmD,gBAAgB,CAAC,GAAG4I,aAAa;QAC7G;QACAxB,EAAE,CAACyB,cAAc,CAAC,CAAC;MACrB;IACF,CAAC;IAAC,OAAAhM,MAAA;EACJ;EAAC,IAAAxE,UAAA,CAAAK,OAAA,EAAAgE,eAAA,EAAAC,qBAAA;EAAA,WAAAzE,aAAA,CAAAQ,OAAA,EAAAgE,eAAA;IAAAyB,GAAA;IAAA2K,KAAA,EA9lBD,SAAA7F,WAAWA,CAAC8F,MAAM,EAAE;MAClB,IAAI/F,QAAQ,GAAG+F,MAAM,GAAGA,MAAM,CAAC/F,QAAQ,GAAG,IAAI;MAC9C,IAAIgG,QAAQ,GAAG,IAAI,CAAC3I,KAAK,CAACwC,YAAY,CAAC,IAAI,CAACxC,KAAK,CAACyC,IAAI,CAAC,GAAG,CAAC;MAC3D,IAAIkG,QAAQ,GAAG,CAAC,EAAE;QAChB;MACF;MACA,IAAIlD,KAAK,GAAG,IAAI,CAACJ,uBAAuB,CAACsD,QAAQ,EAAE,IAAI,CAAC3I,KAAK,CAAC;MAC9D,IAAIZ,MAAM,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEsE,KAAK,CAACrG,MAAM,GAAGqG,KAAK,CAACrJ,MAAM,GAAG,IAAI,CAACiC,aAAa,GAAG,IAAI,CAAC1B,cAAc,CAAChB,aAAa,CAAC;MAC9G,IAAI,IAAI,CAAC6D,UAAU,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,IAAI,CAACA,UAAU,CAACoJ,QAAQ,IAAI,IAAI,EAAE;QACpCC,OAAO,CAACC,IAAI,CAAC,uEAAuE,GAAG,iEAAiE,GAAG,4DAA4D,CAAC;QACxN;MACF;MACA,IAAI,CAACtJ,UAAU,CAACoJ,QAAQ,CAAC7N,mBAAmB,CAAC,IAAI,CAACiF,KAAK,CAAChF,UAAU,CAAC,GAAG;QACpE+N,CAAC,EAAE3J,MAAM;QACTuD,QAAQ,EAARA;MACF,CAAC,GAAG;QACFqG,CAAC,EAAE5J,MAAM;QACTuD,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ;EAAC;IAAA7E,GAAA;IAAA2K,KAAA,EAGD,SAAA/F,aAAaA,CAACgG,MAAM,EAAE;MACpB,IAAIO,WAAW,GAAG,IAAI,CAACjJ,KAAK;QAC1ByC,IAAI,GAAGwG,WAAW,CAACxG,IAAI;QACvBzH,UAAU,GAAGiO,WAAW,CAACjO,UAAU;QACnCwH,YAAY,GAAGyG,WAAW,CAACzG,YAAY;QACvCmD,aAAa,GAAGsD,WAAW,CAACtD,aAAa;QACzCuD,qBAAqB,GAAGD,WAAW,CAACC,qBAAqB;MAC3D,IAAIvG,QAAQ,GAAG+F,MAAM,CAAC/F,QAAQ;QAC5BjC,KAAK,GAAGgI,MAAM,CAAChI,KAAK;QACpByI,UAAU,GAAGT,MAAM,CAACS,UAAU;QAC9BC,YAAY,GAAGV,MAAM,CAACU,YAAY;MACpC,CAAC,CAAC,EAAE/O,UAAU,CAAChC,OAAO,EAAEqI,KAAK,IAAI,CAAC,EAAE,8CAA8C,GAAGA,KAAK,GAAG,mBAAmB,CAAC;MACjH,CAAC,CAAC,EAAErG,UAAU,CAAChC,OAAO,EAAEmK,YAAY,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,0CAA0C,GAAGD,YAAY,CAACC,IAAI,CAAC,GAAG,mBAAmB,CAAC;MACvI,CAAC,CAAC,EAAEpI,UAAU,CAAChC,OAAO,EAAEqI,KAAK,GAAG8B,YAAY,CAACC,IAAI,CAAC,EAAE,8CAA8C,GAAG/B,KAAK,GAAG,kBAAkB,IAAI8B,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;MAC3J,IAAI,CAACkD,aAAa,IAAIjF,KAAK,GAAG,IAAI,CAAC/B,0BAA0B,EAAE;QAC7D,CAAC,CAAC,EAAEtE,UAAU,CAAChC,OAAO,EAAE,CAAC,CAAC6Q,qBAAqB,EAAE,2FAA2F,GAAG,yFAAyF,CAAC;QACzOA,qBAAqB,CAAC;UACpBG,iBAAiB,EAAE,IAAI,CAAClL,kBAAkB;UAC1CmL,yBAAyB,EAAE,IAAI,CAAC3K,0BAA0B;UAC1D+B,KAAK,EAALA;QACF,CAAC,CAAC;QACF;MACF;MACA,IAAI+E,KAAK,GAAG,IAAI,CAACJ,uBAAuB,CAACnE,IAAI,CAACqE,KAAK,CAAC7E,KAAK,CAAC,EAAE,IAAI,CAACV,KAAK,CAAC;MACvE,IAAIZ,MAAM,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC+D,gBAAgB,CAACxE,KAAK,EAAE,IAAI,CAACV,KAAK,CAAC,GAAG,CAACoJ,YAAY,IAAI,CAAC,KAAK,IAAI,CAACzM,cAAc,CAAChB,aAAa,GAAG8J,KAAK,CAACrJ,MAAM,CAAC,CAAC,IAAI+M,UAAU,IAAI,CAAC,CAAC;MACjK,IAAI,IAAI,CAAC3J,UAAU,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,IAAI,CAACA,UAAU,CAACoJ,QAAQ,IAAI,IAAI,EAAE;QACpCC,OAAO,CAACC,IAAI,CAAC,uEAAuE,GAAG,iEAAiE,GAAG,4DAA4D,CAAC;QACxN;MACF;MACA,IAAI,CAACtJ,UAAU,CAACoJ,QAAQ,CAAC5N,UAAU,GAAG;QACpC+N,CAAC,EAAE3J,MAAM;QACTuD,QAAQ,EAARA;MACF,CAAC,GAAG;QACFqG,CAAC,EAAE5J,MAAM;QACTuD,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ;EAAC;IAAA7E,GAAA;IAAA2K,KAAA,EAID,SAAAc,YAAYA,CAACb,MAAM,EAAE;MACnB,IAAI1D,IAAI,GAAG0D,MAAM,CAAC1D,IAAI;MACtB,IAAIwE,YAAY,GAAG,IAAI,CAACxJ,KAAK;QAC3ByC,IAAI,GAAG+G,YAAY,CAAC/G,IAAI;QACxBsC,OAAO,GAAGyE,YAAY,CAACzE,OAAO;QAC9BvC,YAAY,GAAGgH,YAAY,CAAChH,YAAY;MAC1C,IAAIsD,SAAS,GAAGtD,YAAY,CAACC,IAAI,CAAC;MAClC,KAAK,IAAIgH,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAG3D,SAAS,EAAE2D,MAAM,EAAE,EAAE;QACjD,IAAI1E,OAAO,CAACtC,IAAI,EAAEgH,MAAM,CAAC,KAAKzE,IAAI,EAAE;UAClC,IAAI,CAACtC,aAAa,CAAC,CAAC,CAAC,EAAErJ,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEgB,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEqQ,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YAC1FhI,KAAK,EAAE+I;UACT,CAAC,CAAC,CAAC;UACH;QACF;MACF;IACF;EAAC;IAAA3L,GAAA;IAAA2K,KAAA,EAYD,SAAAiB,cAAcA,CAAChB,MAAM,EAAE;MACrB,IAAI/F,QAAQ,GAAG+F,MAAM,CAAC/F,QAAQ;QAC5BvD,MAAM,GAAGsJ,MAAM,CAACtJ,MAAM;MACxB,IAAI,IAAI,CAACI,UAAU,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,IAAI,CAACA,UAAU,CAACoJ,QAAQ,IAAI,IAAI,EAAE;QACpCC,OAAO,CAACC,IAAI,CAAC,uEAAuE,GAAG,iEAAiE,GAAG,4DAA4D,CAAC;QACxN;MACF;MACA,IAAI,CAACtJ,UAAU,CAACoJ,QAAQ,CAAC7N,mBAAmB,CAAC,IAAI,CAACiF,KAAK,CAAChF,UAAU,CAAC,GAAG;QACpE+N,CAAC,EAAE3J,MAAM;QACTuD,QAAQ,EAARA;MACF,CAAC,GAAG;QACFqG,CAAC,EAAE5J,MAAM;QACTuD,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ;EAAC;IAAA7E,GAAA;IAAA2K,KAAA,EACD,SAAAlL,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACL,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;QAC1CA,SAAS,CAACM,iBAAiB,CAAC,CAAC;MAC/B,CAAC,CAAC;MACF,IAAI,CAACsC,kBAAkB,CAAChC,OAAO,CAAC,UAAA3F,CAAC,EAAI;QACnCA,CAAC,CAAC0L,iBAAiB,CAACrG,iBAAiB,CAAC,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACgE,oBAAoB,CAAC,IAAI,CAACvB,KAAK,EAAE,IAAI,CAACwB,KAAK,CAACC,mBAAmB,CAAC;IACvE;EAAC;IAAA3D,GAAA;IAAA2K,KAAA,EACD,SAAAkB,qBAAqBA,CAAA,EAAG;MACtB,IAAI,IAAI,CAACnK,UAAU,IAAI,IAAI,EAAE;QAC3B;MACF;MACA,IAAI,CAACA,UAAU,CAACmK,qBAAqB,CAAC,CAAC;IACzC;EAAC;IAAA7L,GAAA;IAAA2K,KAAA,EAOD,SAAAmB,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACpK,UAAU,IAAI,IAAI,CAACA,UAAU,CAACoK,kBAAkB,EAAE;QACzD,OAAO,IAAI,CAACpK,UAAU,CAACoK,kBAAkB,CAAC,CAAC;MAC7C;IACF;EAAC;IAAA9L,GAAA;IAAA2K,KAAA,EACD,SAAAL,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAAC5I,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC4I,iBAAiB,EAAE;QACxD,OAAO,IAAI,CAAC5I,UAAU,CAAC4I,iBAAiB,CAAC,CAAC;MAC5C,CAAC,MAAM;QACL,OAAO,IAAI,CAAC5I,UAAU;MACxB;IACF;EAAC;IAAA1B,GAAA;IAAA2K,KAAA,EACD,SAAAoB,YAAYA,CAAA,EAAG;MACb,IAAI,IAAI,CAACrK,UAAU,IAAI,IAAI,CAACA,UAAU,CAACqK,YAAY,EAAE;QACnD,OAAO,IAAI,CAACrK,UAAU,CAACqK,YAAY,CAAC,CAAC;MACvC,CAAC,MAAM;QACL,OAAO,IAAI,CAACrK,UAAU;MACxB;IACF;EAAC;IAAA1B,GAAA;IAAA2K,KAAA,EACD,SAAAqB,WAAWA,CAAA,EAAG;MACZ,IAAIC,aAAa;MACjB,OAAO,CAAC,CAACA,aAAa,GAAG,IAAI,CAACjN,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiN,aAAa,CAAC1M,OAAO,KAAK,UAAU;IAChG;EAAC;IAAAS,GAAA;IAAA2K,KAAA,EAID,SAAAuB,OAAOA,CAAA,EAAG;MACR,OAAO,IAAI,CAACzL,QAAQ;IACtB;EAAC;IAAAT,GAAA;IAAA2K,KAAA,EA6bD,SAAAvC,WAAWA,CAAClG,KAAK,EAAE;MACjB,IAAIiD,QAAQ,GAAGjD,KAAK,CAACiD,QAAQ;QAC3BlH,UAAU,GAAGiE,KAAK,CAACjE,UAAU;QAC7ByG,YAAY,GAAGxC,KAAK,CAACwC,YAAY;QACjCC,IAAI,GAAGzC,KAAK,CAACyC,IAAI;QACjBH,kBAAkB,GAAGtC,KAAK,CAACsC,kBAAkB;MAC/C,CAAC,CAAC,EAAEjI,UAAU,CAAChC,OAAO,EAEtB,CAAC4K,QAAQ,IAAI,CAACA,QAAQ,CAACgH,UAAU,EAAE,4FAA4F,GAAG,wDAAwD,CAAC;MAC3L,CAAC,CAAC,EAAE5P,UAAU,CAAChC,OAAO,EAAEyD,mBAAmB,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE,yFAAyF,CAAC;MACvJ,CAAC,CAAC,EAAE1B,UAAU,CAAChC,OAAO,EAAEmK,YAAY,EAAE,2DAA2D,CAAC;MAClG,IAAIsD,SAAS,GAAGtD,YAAY,CAACC,IAAI,CAAC;MAClC,IAAIH,kBAAkB,IAAI,IAAI,IAAI,CAAC,IAAI,CAAChE,iCAAiC,KAAKgE,kBAAkB,GAAG,CAAC,IAAIwD,SAAS,GAAG,CAAC,IAAIxD,kBAAkB,IAAIwD,SAAS,CAAC,IAAI,CAAC,IAAI,CAACtH,UAAU,CAAC8D,kBAAkB,EAAE;QAChMuG,OAAO,CAACC,IAAI,CAAC,uBAAuB,GAAGxG,kBAAkB,GAAG,4BAA4B,GAAGwD,SAAS,GAAG,SAAS,CAAC;QACjH,IAAI,CAACtH,UAAU,CAAC8D,kBAAkB,GAAG,IAAI;MAC3C;MACA,IAAI9H,OAAO,IAAI,CAAC,IAAI,CAACgE,UAAU,CAAC0L,QAAQ,EAAE;QAExC,IAAIC,UAAU,GAAG1Q,WAAW,CAACpB,OAAO,CAAC+R,OAAO,CAAC,IAAI,CAACpK,KAAK,CAACqK,qBAAqB,CAAC;QAC9E,IAAIF,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACD,QAAQ,KAAK,MAAM,EAAE;UACxDrB,OAAO,CAACC,IAAI,CAAC,4EAA4E,GAAG,sDAAsD,CAAC;UACnJ,IAAI,CAACtK,UAAU,CAAC0L,QAAQ,GAAG,IAAI;QACjC;MACF;IACF;EAAC;IAAApM,GAAA;IAAA2K,KAAA,EAiDD,SAAAnE,0BAA0BA,CAACtE,KAAK,EAAEyB,mBAAmB,EAAE;MACrD,IAAIgB,IAAI,GAAGzC,KAAK,CAACyC,IAAI;QACnBD,YAAY,GAAGxC,KAAK,CAACwC,YAAY;MACnC,IAAIhH,qBAAqB,GAAGD,8BAA8B,CAACyE,KAAK,CAACxE,qBAAqB,CAAC;MACvF,IAAI8O,oBAAoB,GAAG,IAAI,CAAC3N,cAAc;QAC5CsC,aAAa,GAAGqL,oBAAoB,CAACrL,aAAa;QAClDG,MAAM,GAAGkL,oBAAoB,CAAClL,MAAM;QACpCzD,aAAa,GAAG2O,oBAAoB,CAAC3O,aAAa;MACpD,IAAI4O,eAAe,GAAGtL,aAAa,GAAGtD,aAAa,GAAGyD,MAAM;MAI5D,IAAIzD,aAAa,IAAI,CAAC,IAAIsD,aAAa,IAAI,CAAC,EAAE;QAC5C,OAAOwC,mBAAmB,CAACkD,IAAI,IAAInC,YAAY,CAACC,IAAI,CAAC,GAAGpG,eAAe,CAACmO,qBAAqB,CAAC/I,mBAAmB,EAAEzB,KAAK,CAAC,GAAGyB,mBAAmB;MACjJ;MACA,IAAIgJ,sBAAsB;MAC1B,IAAIzK,KAAK,CAAC0K,qBAAqB,EAAE;QAC/B,IAAIC,WAAW,GAAGJ,eAAe,GAAG/O,qBAAqB,GAAGG,aAAa,GAAGR,4BAA4B,CAAC6E,KAAK,CAAC5E,mBAAmB,CAAC,GAAG,CAAC;QACvIqP,sBAAsB,GAAG;UACvB/F,KAAK,EAAE,CAAC;UACRC,IAAI,EAAEzD,IAAI,CAAC+G,GAAG,CAACxG,mBAAmB,CAACkD,IAAI,GAAGgG,WAAW,EAAEnI,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC;QAC/E,CAAC;MACH,CAAC,MAAM;QAWL,IAAIzC,KAAK,CAACsC,kBAAkB,IAAI,CAAC,IAAI,CAAC3F,cAAc,CAACyC,MAAM,IAAI8B,IAAI,CAAC0J,GAAG,CAACL,eAAe,CAAC,IAAIpF,MAAM,CAAC0F,OAAO,EAAE;UAC1G,OAAOpJ,mBAAmB,CAACkD,IAAI,IAAInC,YAAY,CAACC,IAAI,CAAC,GAAGpG,eAAe,CAACmO,qBAAqB,CAAC/I,mBAAmB,EAAEzB,KAAK,CAAC,GAAGyB,mBAAmB;QACjJ;QACAgJ,sBAAsB,GAAG,CAAC,CAAC,EAAErQ,gBAAgB,CAAC0Q,2BAA2B,EAAE9K,KAAK,EAAE7E,4BAA4B,CAAC6E,KAAK,CAAC5E,mBAAmB,CAAC,EAAEU,mBAAmB,CAACkE,KAAK,CAACjE,UAAU,CAAC,EAAE0F,mBAAmB,EAAE,IAAI,CAAC4D,uBAAuB,EAAE,IAAI,CAAC1I,cAAc,CAAC;QACzP,CAAC,CAAC,EAAEtC,UAAU,CAAChC,OAAO,EAAEoS,sBAAsB,CAAC9F,IAAI,GAAGnC,YAAY,CAACC,IAAI,CAAC,EAAE,6DAA6D,CAAC;MAC1I;MACA,IAAI,IAAI,CAACvF,iBAAiB,CAAC6N,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE;QAOrC,IAAIC,QAAQ,GAAG,IAAI,CAACC,uBAAuB,CAACR,sBAAsB,CAAC/F,KAAK,EAAE+F,sBAAsB,CAAC9F,IAAI,CAAC;QACtG8F,sBAAsB,CAAC9F,IAAI,GAAGqG,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAGP,sBAAsB,CAAC9F,IAAI;MACjH;MACA,OAAO8F,sBAAsB;IAC/B;EAAC;IAAA3M,GAAA;IAAA2K,KAAA,EACD,SAAAwC,uBAAuBA,CAACvG,KAAK,EAAEC,IAAI,EAAE;MACnC,KAAK,IAAIuG,EAAE,GAAGxG,KAAK,EAAEwG,EAAE,IAAIvG,IAAI,EAAEuG,EAAE,EAAE,EAAE;QACrC,IAAIC,eAAe,GAAG,IAAI,CAACvM,cAAc,CAACwM,GAAG,CAACF,EAAE,CAAC;QACjD,IAAIC,eAAe,IAAI,IAAI,IAAI,IAAI,CAACjO,iBAAiB,CAACmO,SAAS,CAACF,eAAe,EAAE,UAAAlO,SAAS;UAAA,OAAIA,SAAS,CAAC+M,OAAO,CAAC,CAAC;QAAA,EAAC,EAAE;UAClH,OAAOkB,EAAE;QACX;MACF;MACA,OAAO,IAAI;IACb;EAAC;IAAApN,GAAA;IAAA2K,KAAA,EACD,SAAA6C,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAACzO,4BAA4B,CAAC,CAAC,EAAE;QACvC,IAAI,CAACC,OAAO,CAACyO,qBAAqB,CAAC;UACjCnO,GAAG,EAAE,IAAI;UACTC,OAAO,EAAE,IAAI,CAACP,OAAO,CAACO;QACxB,CAAC,CAAC;MACJ;MAGA,IAAI,CAACmO,oBAAoB,CAAC,CAAC;IAC7B;EAAC;IAAA1N,GAAA;IAAA2K,KAAA,EACD,SAAAgD,oBAAoBA,CAAA,EAAG;MACrB,IAAI,IAAI,CAAC5O,4BAA4B,CAAC,CAAC,EAAE;QACvC,IAAI,CAACC,OAAO,CAAC4O,uBAAuB,CAAC;UACnCtO,GAAG,EAAE;QACP,CAAC,CAAC;MACJ;MACA,IAAI,CAAC+I,2BAA2B,CAACwF,OAAO,CAAC;QACvCC,KAAK,EAAE;MACT,CAAC,CAAC;MACF,IAAI,CAAC/L,kBAAkB,CAAChC,OAAO,CAAC,UAAA8F,KAAK,EAAI;QACvCA,KAAK,CAACC,iBAAiB,CAAC+H,OAAO,CAAC,CAAC;MACnC,CAAC,CAAC;MACF,IAAI,CAACnI,eAAe,CAACqI,kBAAkB,CAAC,CAAC;MAGzC,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAChC;EAAC;IAAAhO,GAAA;IAAA2K,KAAA,EAGD,SAAA+C,oBAAoBA,CAAA,EAAG;MAAA,IAAAO,MAAA;MACrB,IAAI,IAAI,CAACvM,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC4I,iBAAiB,EAAE;QACxD,IAAI,CAAC5I,UAAU,CAAC4I,iBAAiB,CAAC,CAAC,CAAC4D,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAClF,yBAAyB,CAAC;MAC/F,CAAC,MAAM;QACLmF,UAAU,CAAC;UAAA,OAAMF,MAAI,CAACP,oBAAoB,CAAC,CAAC;QAAA,GAAE,EAAE,CAAC;QACjD;MACF;IACF;EAAC;IAAA1N,GAAA;IAAA2K,KAAA,EAGD,SAAAqD,uBAAuBA,CAAA,EAAG;MACxB,IAAI,IAAI,CAACtM,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC4I,iBAAiB,EAAE;QACxD,IAAI,CAAC5I,UAAU,CAAC4I,iBAAiB,CAAC,CAAC,CAAC8D,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACpF,yBAAyB,CAAC;MAClG;IACF;EAAC;IAAAhJ,GAAA;IAAA2K,KAAA,EAcD,SAAA0D,UAAUA,CAACC,KAAK,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAE5H,KAAK,EAAEC,IAAI,EAAE4H,cAAc,EAAE;MAC1F,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAIC,YAAY,GAAG,IAAI,CAACzM,KAAK;QAC3B0M,qBAAqB,GAAGD,YAAY,CAACC,qBAAqB;QAC1DC,sBAAsB,GAAGF,YAAY,CAACE,sBAAsB;QAC5DC,mBAAmB,GAAGH,YAAY,CAACG,mBAAmB;QACtDC,iBAAiB,GAAGJ,YAAY,CAACI,iBAAiB;QAClDpK,IAAI,GAAGgK,YAAY,CAAChK,IAAI;QACxBqK,KAAK,GAAGL,YAAY,CAACK,KAAK;QAC1B/H,OAAO,GAAG0H,YAAY,CAAC1H,OAAO;QAC9BvC,YAAY,GAAGiK,YAAY,CAACjK,YAAY;QACxCmD,aAAa,GAAG8G,YAAY,CAAC9G,aAAa;QAC1C3K,UAAU,GAAGyR,YAAY,CAACzR,UAAU;QACpC+R,UAAU,GAAGN,YAAY,CAACM,UAAU;MACtC,IAAIC,YAAY,GAAGJ,mBAAmB,GAAG,CAAC,GAAG,CAAC;MAC9C,IAAIK,GAAG,GAAGzK,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC;MAChC,IAAIyK,WAAW;MACfvI,IAAI,GAAGzD,IAAI,CAAC+G,GAAG,CAACgF,GAAG,EAAEtI,IAAI,CAAC;MAC1B,IAAIwI,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;QAC3B,IAAInI,IAAI,GAAGD,OAAO,CAACtC,IAAI,EAAEyI,EAAE,CAAC;QAC5B,IAAIpN,GAAG,GAAG0O,KAAK,CAACvH,aAAa,CAACD,IAAI,EAAEkG,EAAE,EAAEsB,KAAK,CAACxM,KAAK,CAAC;QACpDwM,KAAK,CAAC5N,cAAc,CAACwO,GAAG,CAAClC,EAAE,EAAEpN,GAAG,CAAC;QACjC,IAAIwO,sBAAsB,CAACe,GAAG,CAACnC,EAAE,GAAG8B,YAAY,CAAC,EAAE;UACjDX,mBAAmB,CAAC1F,IAAI,CAACyF,KAAK,CAAChQ,MAAM,CAAC;QACxC;QACA,IAAIkR,qBAAqB,GAAG3H,aAAa,IAAI,IAAI,IAAImH,KAAK,IAAIN,KAAK,CAAChJ,eAAe,CAAC+J,OAAO,CAAC,CAAC;QAC7FnB,KAAK,CAACzF,IAAI,CAAcpM,KAAK,CAAC2F,aAAa,CAAChG,4BAA4B,CAAC7B,OAAO,EAAE,CAAC,CAAC,EAAEe,SAAS,CAACf,OAAO,EAAE;UACvGqU,qBAAqB,EAAEA,qBAAqB;UAC5CC,sBAAsB,EAAEzB,EAAE,GAAG+B,GAAG,GAAGN,sBAAsB,GAAGa,SAAS;UACrEX,iBAAiB,EAAEA,iBAAiB;UACpCxP,OAAO,EAAES,GAAG;UACZ9C,UAAU,EAAEA,UAAU;UACtB0F,KAAK,EAAEwK,EAAE;UACTqB,cAAc,EAAEA,cAAc;UAC9BvH,IAAI,EAAEA,IAAI;UACVlH,GAAG,EAAEA,GAAG;UACRoP,WAAW,EAAEA,WAAW;UACxBO,kBAAkB,EAAEjB,KAAK,CAAC9O,mBAAmB;UAC7CgQ,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAEtV,CAAC;YAAA,OAAIoU,KAAK,CAACmB,mBAAmB,CAAC7P,GAAG,CAAC;UAAA;UACvD8P,SAAS,EAAEpB,KAAK,CAAC9K,cAAc;UAC/BtE,GAAG,EAAE,SAALA,GAAGA,CAAEyQ,IAAI,EAAI;YACXrB,KAAK,CAACzO,SAAS,CAACD,GAAG,CAAC,GAAG+P,IAAI;UAC7B,CAAC;UACDd,UAAU,EAAEA;QACd,CAAC,EAAEO,qBAAqB,IAAI;UAC1BQ,YAAY,EAAEtB,KAAK,CAAC/L;QACtB,CAAC,CAAC,CAAC,CAAC;QACJyM,WAAW,GAAGpP,GAAG;MACnB,CAAC;MACD,KAAK,IAAIoN,EAAE,GAAGxG,KAAK,EAAEwG,EAAE,IAAIvG,IAAI,EAAEuG,EAAE,EAAE,EAAE;QACrCiC,KAAK,CAAC,CAAC;MACT;IACF;EAAC;IAAArP,GAAA;IAAA2K,KAAA,EAUD,SAAA5L,4BAA4BA,CAAA,EAAG;MAC7B,IAAIkR,aAAa,GAAG,IAAI,CAACjR,OAAO;MAChC,OAAO,CAAC,EAAEiR,aAAa,IAAI,CAAC,CAACA,aAAa,CAAC/S,UAAU,KAAKD,mBAAmB,CAAC,IAAI,CAACiF,KAAK,CAAChF,UAAU,CAAC,CAAC;IACvG;EAAC;IAAA8C,GAAA;IAAA2K,KAAA,EACD,SAAAxD,aAAaA,CAACD,IAAI,EAAEtE,KAAK,EAAEV,KAAK,EAE9B;MACA,IAAIA,KAAK,CAACgO,YAAY,IAAI,IAAI,EAAE;QAC9B,OAAOhO,KAAK,CAACgO,YAAY,CAAChJ,IAAI,EAAEtE,KAAK,CAAC;MACxC;MACA,IAAI5C,GAAG,GAAG,CAAC,CAAC,EAAE1D,gBAAgB,CAAC4T,YAAY,EAAEhJ,IAAI,EAAEtE,KAAK,CAAC;MACzD,IAAI5C,GAAG,KAAKmQ,MAAM,CAACvN,KAAK,CAAC,EAAE;QACzB7F,gBAAgB,GAAG,IAAI;QACvB,IAAImK,IAAI,CAACkJ,IAAI,IAAIlJ,IAAI,CAACkJ,IAAI,CAACC,WAAW,EAAE;UACtCrT,yBAAyB,GAAGkK,IAAI,CAACkJ,IAAI,CAACC,WAAW;QACnD;MACF;MACA,OAAOrQ,GAAG;IACZ;EAAC;IAAAA,GAAA;IAAA2K,KAAA,EACD,SAAA2F,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP,IAAI,CAACnI,WAAW,CAAC,IAAI,CAAClG,KAAK,CAAC;MAC5B,IAAIsO,YAAY,GAAG,IAAI,CAACtO,KAAK;QAC3BuO,kBAAkB,GAAGD,YAAY,CAACC,kBAAkB;QACpDC,mBAAmB,GAAGF,YAAY,CAACE,mBAAmB;QACtD5B,mBAAmB,GAAG0B,YAAY,CAAC1B,mBAAmB;MACxD,IAAI6B,YAAY,GAAG,IAAI,CAACzO,KAAK;QAC3ByC,IAAI,GAAGgM,YAAY,CAAChM,IAAI;QACxBzH,UAAU,GAAGyT,YAAY,CAACzT,UAAU;MACtC,IAAIuR,cAAc,GAAG,IAAI,CAACvM,KAAK,CAACmI,QAAQ,GAAGpN,mBAAmB,CAAC,IAAI,CAACiF,KAAK,CAAChF,UAAU,CAAC,GAAG0T,MAAM,CAACC,oBAAoB,GAAGD,MAAM,CAACE,kBAAkB,GAAG,IAAI;MACtJ,IAAIxC,KAAK,GAAG,EAAE;MACd,IAAIE,sBAAsB,GAAG,IAAIuC,GAAG,CAAC,IAAI,CAAC7O,KAAK,CAACqM,mBAAmB,CAAC;MACpE,IAAIA,mBAAmB,GAAG,EAAE;MAG5B,IAAIO,mBAAmB,EAAE;QACvB,IAAIN,sBAAsB,CAACe,GAAG,CAAC,CAAC,CAAC,EAAE;UACjChB,mBAAmB,CAAC1F,IAAI,CAAC,CAAC,CAAC;QAC7B;QACA,IAAImI,QAAQ,GAAgBvU,KAAK,CAACwU,cAAc,CAACnC,mBAAmB,CAAC,GAAGA,mBAAmB,GAI3FrS,KAAK,CAAC2F,aAAa,CAAC0M,mBAAmB,EAAE,IAAI,CAAC;QAC9CR,KAAK,CAACzF,IAAI,CAAcpM,KAAK,CAAC2F,aAAa,CAAC/F,uBAAuB,CAAC6U,kCAAkC,EAAE;UACtG3R,OAAO,EAAE,IAAI,CAACyM,WAAW,CAAC,CAAC,GAAG,SAAS;UACvChM,GAAG,EAAE;QACP,CAAC,EAAevD,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE;UACjDwJ,QAAQ,EAAE,IAAI,CAACK,eAAe;UAC9B+M,KAAK,EAAE,CAAC1C,cAAc,EAAE,IAAI,CAACvM,KAAK,CAACkP,wBAAwB;QAC7D,CAAC,EAEDJ,QAAQ,CAAC,CAAC,CAAC;MACb;MAGA,IAAIhJ,SAAS,GAAG,IAAI,CAAC9F,KAAK,CAACwC,YAAY,CAACC,IAAI,CAAC;MAC7C,IAAIqD,SAAS,KAAK,CAAC,IAAIyI,kBAAkB,EAAE;QACzC,IAAIY,SAAS,GAAgB5U,KAAK,CAACwU,cAAc,CAACR,kBAAkB,CAAC,GAAGA,kBAAkB,GAI1FhU,KAAK,CAAC2F,aAAa,CAACqO,kBAAkB,EAAE,IAAI,CAAC;QAC7CnC,KAAK,CAACzF,IAAI,CAAcpM,KAAK,CAAC2F,aAAa,CAAC/F,uBAAuB,CAAC6U,kCAAkC,EAAE;UACtG3R,OAAO,EAAE,IAAI,CAACyM,WAAW,CAAC,CAAC,GAAG,QAAQ;UACtChM,GAAG,EAAE;QACP,CAAC,EAAevD,KAAK,CAAC6U,YAAY,CAACD,SAAS,EAAE;UAC5CtN,QAAQ,EAAE,SAAVA,QAAQA,CAAEwN,KAAK,EAAI;YACjBhB,MAAI,CAACtM,cAAc,CAACsN,KAAK,CAAC;YAC1B,IAAIF,SAAS,CAACnP,KAAK,CAAC6B,QAAQ,EAAE;cAC5BsN,SAAS,CAACnP,KAAK,CAAC6B,QAAQ,CAACwN,KAAK,CAAC;YACjC;UACF,CAAC;UACDJ,KAAK,EAAE,CAAC1C,cAAc,EAAE4C,SAAS,CAACnP,KAAK,CAACiP,KAAK;QAC/C,CAAC,CAAC,CAAC,CAAC;MACN;MAGA,IAAInJ,SAAS,GAAG,CAAC,EAAE;QACjBjL,gBAAgB,GAAG,KAAK;QACxBC,yBAAyB,GAAG,EAAE;QAC9B,IAAIwU,SAAS,GAAG,IAAI,CAACrR,aAAa,CAAC,CAACjD,UAAU,CAAC;QAC/C,IAAIuU,aAAa,GAAG,IAAI,CAAC/N,KAAK,CAAC+C,UAAU,CAACiL,gBAAgB,CAAC,CAAC;QAC5D,IAAIC,UAAU,GAAGzT,aAAa,CAACuT,aAAa,EAAE,UAAAG,CAAC;UAAA,OAAIA,CAAC,CAACC,QAAQ;QAAA,EAAC;QAC9D,KAAK,IAAIC,SAAS,GAAG,CAAC,CAAC,EAAEzW,gCAAgC,CAACd,OAAO,EAAEkX,aAAa,CAAC,EAAEM,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAEE,IAAI,GAAG;UACtH,IAAIC,OAAO,GAAGF,KAAK,CAACpH,KAAK;UACzB,IAAIsH,OAAO,CAACJ,QAAQ,EAAE;YAGpB,IAAI,IAAI,CAAC3P,KAAK,CAAC0K,qBAAqB,EAAE;cACpC;YACF;YAKA,IAAIsF,YAAY,GAAGD,OAAO,KAAKN,UAAU;YACzC,IAAIQ,mBAAmB,GAAGD,YAAY,IAAI,CAAC,IAAI,CAAChQ,KAAK,CAAC2F,aAAa;YACnE,IAAIhB,IAAI,GAAGsL,mBAAmB,GAAG,CAAC,CAAC,EAAEtW,MAAM,CAACtB,OAAO,EAAE0X,OAAO,CAACrL,KAAK,GAAG,CAAC,EAAEqL,OAAO,CAACpL,IAAI,EAAE,IAAI,CAAChG,0BAA0B,CAAC,GAAGoR,OAAO,CAACpL,IAAI;YACrI,IAAIuL,YAAY,GAAG,IAAI,CAAC7K,uBAAuB,CAAC0K,OAAO,CAACrL,KAAK,EAAE,IAAI,CAAC1E,KAAK,CAAC;YAC1E,IAAImQ,WAAW,GAAG,IAAI,CAAC9K,uBAAuB,CAACV,IAAI,EAAE,IAAI,CAAC3E,KAAK,CAAC;YAChE,IAAIoQ,UAAU,GAAGD,WAAW,CAAC/Q,MAAM,GAAG+Q,WAAW,CAAC/T,MAAM,GAAG8T,YAAY,CAAC9Q,MAAM;YAC9EgN,KAAK,CAACzF,IAAI,CAAcpM,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE;cACzDyF,GAAG,EAAE,UAAU,GAAGiS,OAAO,CAACrL,KAAK;cAC/BuK,KAAK,MAAAtX,gBAAA,CAAAU,OAAA,MACFiX,SAAS,EAAGc,UAAU;YAE3B,CAAC,CAAC,CAAC;UACL,CAAC,MAAM;YACL,IAAI,CAACjE,UAAU,CAACC,KAAK,EAAEC,mBAAmB,EAAEC,sBAAsB,EAAEyD,OAAO,CAACrL,KAAK,EAAEqL,OAAO,CAACpL,IAAI,EAAE4H,cAAc,CAAC;UAClH;QACF;QACA,IAAI,CAAC,IAAI,CAAC/N,UAAU,CAACb,IAAI,IAAI9C,gBAAgB,EAAE;UAC7CgO,OAAO,CAACC,IAAI,CAAC,6FAA6F,GAAG,wCAAwC,EAAEhO,yBAAyB,CAAC;UACjL,IAAI,CAAC0D,UAAU,CAACb,IAAI,GAAG,IAAI;QAC7B;MACF;MAGA,IAAI6Q,mBAAmB,EAAE;QACvB,IAAI6B,SAAS,GAAgB9V,KAAK,CAACwU,cAAc,CAACP,mBAAmB,CAAC,GAAGA,mBAAmB,GAI5FjU,KAAK,CAAC2F,aAAa,CAACsO,mBAAmB,EAAE,IAAI,CAAC;QAC9CpC,KAAK,CAACzF,IAAI,CAAcpM,KAAK,CAAC2F,aAAa,CAAC/F,uBAAuB,CAAC6U,kCAAkC,EAAE;UACtG3R,OAAO,EAAE,IAAI,CAAC4E,iBAAiB,CAAC,CAAC;UACjCnE,GAAG,EAAE;QACP,CAAC,EAAevD,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE;UACjDwJ,QAAQ,EAAE,IAAI,CAACG,eAAe;UAC9BiN,KAAK,EAAE,CAAC1C,cAAc,EAAE,IAAI,CAACvM,KAAK,CAACsQ,wBAAwB;QAC7D,CAAC,EAEDD,SAAS,CAAC,CAAC,CAAC;MACd;MAGA,IAAIE,WAAW,GAAG,CAAC,CAAC,EAAElX,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEgB,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC2H,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC7F6C,mBAAmB,EAAE,IAAI,CAACV,oBAAoB;QAC9CN,QAAQ,EAAE,IAAI,CAACF,SAAS;QACxBsB,QAAQ,EAAE,IAAI,CAACD,SAAS;QACxBa,iBAAiB,EAAE,IAAI,CAACH,kBAAkB;QAC1CK,eAAe,EAAE,IAAI,CAACD,gBAAgB;QACtCG,qBAAqB,EAAE,IAAI,CAACD,sBAAsB;QAClDG,mBAAmB,EAAE,IAAI,CAACD,oBAAoB;QAC9CrI,mBAAmB,EAAED,4BAA4B,CAAC,IAAI,CAACoE,KAAK,CAACnE,mBAAmB,CAAC;QAEjF2U,mBAAmB,EAAE,IAAI,CAACxQ,KAAK,CAACwQ,mBAAmB,KAAKhD,SAAS,GAAG,IAAI,CAACxN,KAAK,CAACwQ,mBAAmB,GAAG,IAAI,CAACxQ,KAAK,CAACmI,QAAQ;QACxHkE,mBAAmB,EAAnBA,mBAAmB;QACnB4C,KAAK,EAAE1C,cAAc,GAAG,CAACA,cAAc,EAAE,IAAI,CAACvM,KAAK,CAACiP,KAAK,CAAC,GAAG,IAAI,CAACjP,KAAK,CAACiP;MAC1E,CAAC,CAAC;MACF,IAAI,CAAC1Q,QAAQ,GAAG,IAAI,CAACiD,KAAK,CAACC,mBAAmB,CAACkD,IAAI,GAAGmB,SAAS,GAAG,CAAC;MACnE,IAAI2K,QAAQ,GAAgBlW,KAAK,CAAC2F,aAAa,CAAC/F,uBAAuB,CAACuW,8BAA8B,EAAE;QACtGjI,KAAK,EAAE;UACLpL,OAAO,EAAE,IAAI;UACbsT,gBAAgB,EAAE,IAAI,CAACjU,iBAAiB;UACxC1B,UAAU,EAAED,mBAAmB,CAAC,IAAI,CAACiF,KAAK,CAAChF,UAAU,CAAC;UACtD+B,yBAAyB,EAAE,IAAI,CAACH,0BAA0B;UAC1D2O,qBAAqB,EAAE,IAAI,CAACvO,sBAAsB;UAClD0O,uBAAuB,EAAE,IAAI,CAAClO;QAChC;MACF,CAAC,EAAejD,KAAK,CAAC6U,YAAY,CAAC,CAAC,IAAI,CAACpP,KAAK,CAAC4Q,qBAAqB,IAAI,IAAI,CAAC7Q,6BAA6B,EAAEwQ,WAAW,CAAC,EAAE;QACxHnT,GAAG,EAAE,IAAI,CAAC0C;MACZ,CAAC,EAAEsM,KAAK,CAAC,CAAC;MACV,IAAIyE,GAAG,GAAGJ,QAAQ;MA2BlB,IAAI,IAAI,CAACzQ,KAAK,CAAC8M,KAAK,EAAE;QACpB,OAAoBvS,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE;UACrD4W,KAAK,EAAEP,MAAM,CAAC5B;QAChB,CAAC,EAAE+D,GAAG,EAAE,IAAI,CAACC,mBAAmB,CAAC,CAAC,CAAC;MACrC,CAAC,MAAM;QACL,OAAOD,GAAG;MACZ;IACF;EAAC;IAAA/S,GAAA;IAAA2K,KAAA,EACD,SAAAsI,kBAAkBA,CAACC,SAAS,EAAE;MAC5B,IAAIC,YAAY,GAAG,IAAI,CAACjR,KAAK;QAC3ByC,IAAI,GAAGwO,YAAY,CAACxO,IAAI;QACxByO,SAAS,GAAGD,YAAY,CAACC,SAAS;MACpC,IAAIzO,IAAI,KAAKuO,SAAS,CAACvO,IAAI,IAAIyO,SAAS,KAAKF,SAAS,CAACE,SAAS,EAAE;QAGhE,IAAI,CAACrR,kBAAkB,CAAChC,OAAO,CAAC,UAAA8F,KAAK,EAAI;UACvCA,KAAK,CAACC,iBAAiB,CAACuN,oBAAoB,CAAC,CAAC;QAChD,CAAC,CAAC;MACJ;MAOA,IAAIC,eAAe,GAAG,IAAI,CAAC1S,gBAAgB;MAC3C,IAAI,CAAC0C,4BAA4B,CAAC,CAAC;MAGnC,IAAIgQ,eAAe,EAAE;QACnB,IAAI,CAAC1S,gBAAgB,GAAG,KAAK;MAC/B;IACF;EAAC;IAAAZ,GAAA;IAAA2K,KAAA,EAWD,SAAAnH,iBAAiBA,CAAA,EAAG;MAClB,IAAI,CAACkC,eAAe,CAAC6N,gBAAgB,CAAC,IAAI,CAACrR,KAAK,EAAE,IAAI,CAACwB,KAAK,CAACC,mBAAmB,EAAE,IAAI,CAAC9E,cAAc,CAAC;IACxG;EAAC;IAAAmB,GAAA;IAAA2K,KAAA,EAKD,SAAAkF,mBAAmBA,CAACtQ,OAAO,EAAE;MAC3B,IAAI,CAACyB,mBAAmB,GAAGzB,OAAO;MAClC,IAAI,CAAC+G,oBAAoB,CAAC,CAAC;IAC7B;EAAC;IAAAtG,GAAA;IAAA2K,KAAA,EACD,SAAApH,oCAAoCA,CAAChE,OAAO,EAAE;MAC5C,IAAI,CAACH,iBAAiB,CAACoU,aAAa,CAACjU,OAAO,EAAE,UAAAJ,SAAS,EAAI;QACzDA,SAAS,CAAC2E,qCAAqC,CAAC,CAAC;MACnD,CAAC,CAAC;IACJ;EAAC;IAAA9D,GAAA;IAAA2K,KAAA,EACD,SAAA7G,qCAAqCA,CAAA,EAAG;MAAA,IAAA2P,MAAA;MAGtC,IAAI;QACF,IAAI,CAAC,IAAI,CAAC/R,UAAU,EAAE;UACpB;QACF;QAGA,IAAI,CAACA,UAAU,CAACgS,aAAa,CAAC,IAAI,CAAC1U,OAAO,CAACC,yBAAyB,CAAC,CAAC,CAAC8M,YAAY,CAAC,CAAC,EAAE,UAACd,CAAC,EAAEC,CAAC,EAAE5G,KAAK,EAAEC,MAAM,EAAK;UAC9GkP,MAAI,CAACxS,gCAAgC,GAAGwS,MAAI,CAACzQ,aAAa,CAAC;YACzDiI,CAAC,EAADA,CAAC;YACDC,CAAC,EAADA;UACF,CAAC,CAAC;UACFuI,MAAI,CAAC5U,cAAc,CAACsC,aAAa,GAAGsS,MAAI,CAACxQ,aAAa,CAAC;YACrDqB,KAAK,EAALA,KAAK;YACLC,MAAM,EAANA;UACF,CAAC,CAAC;UACF,IAAIoP,aAAa,GAAGF,MAAI,CAACzO,2BAA2B,CAACyO,MAAI,CAACzU,OAAO,CAAC6T,gBAAgB,CAAC,CAAC,CAAC;UACrF,IAAIe,cAAc,GAAGH,MAAI,CAAC5U,cAAc,CAAChB,aAAa,KAAK8V,aAAa,CAAC9V,aAAa,IAAI4V,MAAI,CAAC5U,cAAc,CAACyC,MAAM,KAAKqS,aAAa,CAACrS,MAAM;UAC7I,IAAIsS,cAAc,EAAE;YAClBH,MAAI,CAAC5U,cAAc,CAAChB,aAAa,GAAG8V,aAAa,CAAC9V,aAAa;YAC/D4V,MAAI,CAAC5U,cAAc,CAACyC,MAAM,GAAGqS,aAAa,CAACrS,MAAM;YAIjDmS,MAAI,CAACrU,iBAAiB,CAACW,OAAO,CAAC,UAAAZ,SAAS,EAAI;cAC1CA,SAAS,CAAC2E,qCAAqC,CAAC,CAAC;YACnD,CAAC,CAAC;UACJ;QACF,CAAC,EAAE,UAAA+P,KAAK,EAAI;UACV9I,OAAO,CAACC,IAAI,CAAC,gEAAgE,GAAG,8CAA8C,CAAC;QACjI,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO6I,KAAK,EAAE;QACd9I,OAAO,CAACC,IAAI,CAAC,sDAAsD,EAAE6I,KAAK,CAACC,KAAK,CAAC;MACnF;IACF;EAAC;IAAA9T,GAAA;IAAA2K,KAAA,EACD,SAAAxG,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAAC6H,WAAW,CAAC,CAAC,GAAG,SAAS;IACvC;EAAC;IAAAhM,GAAA;IAAA2K,KAAA,EAED,SAAAqI,mBAAmBA,CAAA,EAAG;MACpB,IAAIe,SAAS,GAAG,IAAI,CAAClV,cAAc,CAAChB,aAAa,IAAI,IAAI,CAACgB,cAAc,CAACsC,aAAa,IAAI,CAAC,CAAC;MAC5F,IAAI6S,cAAc,GAAG,EAAE;MACvB,IAAIhM,SAAS,GAAG,IAAI,CAAC9F,KAAK,CAACwC,YAAY,CAAC,IAAI,CAACxC,KAAK,CAACyC,IAAI,CAAC;MACxD,KAAK,IAAIyI,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGpF,SAAS,EAAEoF,EAAE,EAAE,EAAE;QACrC,IAAIzF,KAAK,GAAG,IAAI,CAACJ,uBAAuB,CAAC6F,EAAE,EAAE,IAAI,CAAClL,KAAK,CAAC;QAIxD,IAAIyF,KAAK,CAACzE,QAAQ,EAAE;UAClB8Q,cAAc,CAACnL,IAAI,CAAClB,KAAK,CAAC;QAC5B;MACF;MACA,IAAIsM,SAAS,GAAG,IAAI,CAAC1M,uBAAuB,CAAC,IAAI,CAAC7D,KAAK,CAACC,mBAAmB,CAACiD,KAAK,EAAE,IAAI,CAAC1E,KAAK,CAAC,CAACZ,MAAM;MACrG,IAAI4S,SAAS,GAAG,IAAI,CAAC3M,uBAAuB,CAAC,IAAI,CAAC7D,KAAK,CAACC,mBAAmB,CAACkD,IAAI,EAAE,IAAI,CAAC3E,KAAK,CAAC;MAC7F,IAAIiS,SAAS,GAAGD,SAAS,CAAC5S,MAAM,GAAG4S,SAAS,CAAC5V,MAAM,GAAG2V,SAAS;MAC/D,IAAIG,MAAM,GAAG,IAAI,CAACvV,cAAc,CAACyC,MAAM;MACvC,IAAI+S,MAAM,GAAG,IAAI,CAACxV,cAAc,CAAChB,aAAa;MAC9C,OAAoBpB,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE;QACrD4W,KAAK,EAAE,CAACP,MAAM,CAAC0D,gBAAgB,EAAE1D,MAAM,CAAC2D,YAAY;MACtD,CAAC,EAAEP,cAAc,CAACxL,GAAG,CAAC,UAACgM,CAAC,EAAEpH,EAAE;QAAA,OAAkB3Q,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE;UAC/EyF,GAAG,EAAE,GAAG,GAAGoN,EAAE;UACb+D,KAAK,EAAE,CAACP,MAAM,CAAC0D,gBAAgB,EAAE1D,MAAM,CAAC6D,iBAAiB,EAAE;YACzDC,GAAG,EAAEF,CAAC,CAAClT,MAAM,GAAGyS,SAAS;YACzBxP,MAAM,EAAEiQ,CAAC,CAAClW,MAAM,GAAGyV;UACrB,CAAC;QACH,CAAC,CAAC;MAAA,EAAC,EAAetX,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE;QACnD4W,KAAK,EAAE,CAACP,MAAM,CAAC0D,gBAAgB,EAAE1D,MAAM,CAAC+D,qBAAqB,EAAE;UAC7DD,GAAG,EAAET,SAAS,GAAGF,SAAS;UAC1BxP,MAAM,EAAE4P,SAAS,GAAGJ;QACtB,CAAC;MACH,CAAC,CAAC,EAAetX,KAAK,CAAC2F,aAAa,CAAC1G,KAAK,CAACnB,OAAO,EAAE;QAClD4W,KAAK,EAAE,CAACP,MAAM,CAAC0D,gBAAgB,EAAE1D,MAAM,CAACgE,oBAAoB,EAAE;UAC5DF,GAAG,EAAEN,MAAM,GAAGL,SAAS;UACvBxP,MAAM,EAAE8P,MAAM,GAAGN;QACnB,CAAC;MACH,CAAC,CAAC,CAAC;IACL;EAAC;IAAA/T,GAAA;IAAA2K,KAAA,EACD,SAAA1H,aAAaA,CAACgC,OAAO,EAAE;MACrB,OAAO,CAAChI,mBAAmB,CAAC,IAAI,CAACiF,KAAK,CAAChF,UAAU,CAAC,GAAG+H,OAAO,CAACV,MAAM,GAAGU,OAAO,CAACX,KAAK;IACrF;EAAC;IAAAtE,GAAA;IAAA2K,KAAA,EACD,SAAA3H,aAAaA,CAACiC,OAAO,EAAE;MACrB,OAAO,CAAChI,mBAAmB,CAAC,IAAI,CAACiF,KAAK,CAAChF,UAAU,CAAC,GAAG+H,OAAO,CAACiG,CAAC,GAAGjG,OAAO,CAACgG,CAAC;IAC5E;EAAC;IAAAjL,GAAA;IAAA2K,KAAA,EACD,SAAA3G,uBAAuBA,CAAA,EAAG;MACxB,IAAI6Q,YAAY,GAAG,IAAI,CAAC3S,KAAK;QAC3ByC,IAAI,GAAGkQ,YAAY,CAAClQ,IAAI;QACxBD,YAAY,GAAGmQ,YAAY,CAACnQ,YAAY;QACxCoQ,cAAc,GAAGD,YAAY,CAACC,cAAc;QAC5CtX,uBAAuB,GAAGqX,YAAY,CAACrX,uBAAuB;QAC9DuX,YAAY,GAAGF,YAAY,CAACE,YAAY;QACxCrX,qBAAqB,GAAGmX,YAAY,CAACnX,qBAAqB;QAC1D8G,kBAAkB,GAAGqQ,YAAY,CAACrQ,kBAAkB;MACtD,IAAIwQ,qBAAqB,GAAG,IAAI,CAACnW,cAAc;QAC7CsC,aAAa,GAAG6T,qBAAqB,CAAC7T,aAAa;QACnDtD,aAAa,GAAGmX,qBAAqB,CAACnX,aAAa;QACnDyD,MAAM,GAAG0T,qBAAqB,CAAC1T,MAAM;MACvC,IAAI2T,iBAAiB,GAAG3T,MAAM;MAC9B,IAAImL,eAAe,GAAGtL,aAAa,GAAGtD,aAAa,GAAGyD,MAAM;MAK5D,IAAI2T,iBAAiB,GAAGnY,uBAAuB,EAAE;QAC/CmY,iBAAiB,GAAG,CAAC;MACvB;MACA,IAAIxI,eAAe,GAAG3P,uBAAuB,EAAE;QAC7C2P,eAAe,GAAG,CAAC;MACrB;MAIA,IAAIyI,oBAAoB,GAAG,CAAC;MAC5B,IAAIC,cAAc,GAAG3X,uBAAuB,IAAI,IAAI,GAAGA,uBAAuB,GAAGK,aAAa,GAAGqX,oBAAoB;MACrH,IAAIE,YAAY,GAAG1X,qBAAqB,IAAI,IAAI,GAAGA,qBAAqB,GAAGG,aAAa,GAAGqX,oBAAoB;MAC/G,IAAIG,sBAAsB,GAAGJ,iBAAiB,IAAIE,cAAc;MAChE,IAAIG,oBAAoB,GAAG7I,eAAe,IAAI2I,YAAY;MAK1D,IAAIL,YAAY,IAAI,IAAI,CAACrR,KAAK,CAACC,mBAAmB,CAACkD,IAAI,KAAKnC,YAAY,CAACC,IAAI,CAAC,GAAG,CAAC,IAAI2Q,oBAAoB,IAAI,IAAI,CAACzW,cAAc,CAACsC,aAAa,KAAK,IAAI,CAACS,wBAAwB,EAAE;QACjL,IAAI,CAACA,wBAAwB,GAAG,IAAI,CAAC/C,cAAc,CAACsC,aAAa;QACjE4T,YAAY,CAAC;UACXtI,eAAe,EAAfA;QACF,CAAC,CAAC;MACJ,CAAC,MAKI,IAAIqI,cAAc,IAAI,IAAI,IAAI,IAAI,CAACpR,KAAK,CAACC,mBAAmB,CAACiD,KAAK,KAAK,CAAC,IAAIyO,sBAAsB,IAAI,IAAI,CAACxW,cAAc,CAACsC,aAAa,KAAK,IAAI,CAACQ,0BAA0B,EAAE;QAKhL,IAAI,CAAC6C,kBAAkB,IAAI,IAAI,CAAC3F,cAAc,CAAC0C,SAAS,KAAK,CAAC,EAAE;UAC9D,IAAI,CAACI,0BAA0B,GAAG,IAAI,CAAC9C,cAAc,CAACsC,aAAa;UACnE2T,cAAc,CAAC;YACbG,iBAAiB,EAAjBA;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAII;QACH,IAAI,CAACtT,0BAA0B,GAAG0T,sBAAsB,GAAG,IAAI,CAAC1T,0BAA0B,GAAG,CAAC;QAC9F,IAAI,CAACC,wBAAwB,GAAG0T,oBAAoB,GAAG,IAAI,CAAC1T,wBAAwB,GAAG,CAAC;MAC1F;IACF;EAAC;IAAA5B,GAAA;IAAA2K,KAAA,EAMD,SAAArH,4BAA4BA,CAAA,EAAG;MAC7B,IAAIiS,qBAAqB,GAAG,IAAI,CAAC7R,KAAK,CAACC,mBAAmB;QACxDiD,KAAK,GAAG2O,qBAAqB,CAAC3O,KAAK;QACnCC,IAAI,GAAG0O,qBAAqB,CAAC1O,IAAI;MACnC,IAAI2O,qBAAqB,GAAG,IAAI,CAAC3W,cAAc;QAC7CyC,MAAM,GAAGkU,qBAAqB,CAAClU,MAAM;QACrCzD,aAAa,GAAG2X,qBAAqB,CAAC3X,aAAa;QACnD2D,QAAQ,GAAGgU,qBAAqB,CAAChU,QAAQ;MAC3C,IAAIwG,SAAS,GAAG,IAAI,CAAC9F,KAAK,CAACwC,YAAY,CAAC,IAAI,CAACxC,KAAK,CAACyC,IAAI,CAAC;MACxD,IAAI8Q,KAAK,GAAG,KAAK;MACjB,IAAIjY,uBAAuB,GAAGD,gCAAgC,CAAC,IAAI,CAAC2E,KAAK,CAAC1E,uBAAuB,CAAC;MAClG,IAAIE,qBAAqB,GAAGD,8BAA8B,CAAC,IAAI,CAACyE,KAAK,CAACxE,qBAAqB,CAAC;MAG5F,IAAIkJ,KAAK,GAAG,CAAC,EAAE;QACb,IAAI8O,OAAO,GAAGpU,MAAM,GAAG,IAAI,CAACiG,uBAAuB,CAACX,KAAK,EAAE,IAAI,CAAC1E,KAAK,CAAC,CAACZ,MAAM;QAC7EmU,KAAK,GAAGC,OAAO,GAAG,CAAC,IAAIlU,QAAQ,GAAG,CAAC,CAAC,IAAIkU,OAAO,GAAG/X,qBAAqB,CAACH,uBAAuB,EAAEK,aAAa,CAAC;MACjH;MAGA,IAAI,CAAC4X,KAAK,IAAI5O,IAAI,IAAI,CAAC,IAAIA,IAAI,GAAGmB,SAAS,GAAG,CAAC,EAAE;QAC/C,IAAI2N,UAAU,GAAG,IAAI,CAACpO,uBAAuB,CAACV,IAAI,EAAE,IAAI,CAAC3E,KAAK,CAAC,CAACZ,MAAM,IAAIA,MAAM,GAAGzD,aAAa,CAAC;QACjG4X,KAAK,GAAGE,UAAU,GAAG,CAAC,IAAInU,QAAQ,GAAG,CAAC,IAAImU,UAAU,GAAGhY,qBAAqB,CAACD,qBAAqB,EAAEG,aAAa,CAAC;MACpH;MAQA,IAAI4X,KAAK,KAAK,IAAI,CAACpV,kBAAkB,IAAI,IAAI,CAAC6B,KAAK,CAAC2F,aAAa,CAAC,IAAI,CAAC,IAAI,CAACjH,gBAAgB,EAAE;QAC5F,IAAI,CAACA,gBAAgB,GAAG,IAAI;QAG5B,IAAI,CAACyH,2BAA2B,CAACwF,OAAO,CAAC;UACvCC,KAAK,EAAE;QACT,CAAC,CAAC;QACF,IAAI,CAACxH,oBAAoB,CAAC,CAAC;QAC3B;MACF,CAAC,MAAM;QACL,IAAI,CAAC+B,2BAA2B,CAACuN,QAAQ,CAAC,CAAC;MAC7C;IACF;EAAC;IAAA5V,GAAA;IAAA2K,KAAA,EAOD,SAAAlH,oBAAoBA,CAACvB,KAAK,EAAEyB,mBAAmB,EAAE;MAAA,IAAAkS,MAAA;MAC/C,IAAI,CAAC9T,kBAAkB,CAAChC,OAAO,CAAC,UAAA8F,KAAK,EAAI;QACvCA,KAAK,CAACC,iBAAiB,CAACgQ,QAAQ,CAAC5T,KAAK,EAAE2T,MAAI,CAAChX,cAAc,CAACyC,MAAM,EAAEuU,MAAI,CAAChX,cAAc,CAAChB,aAAa,EAAEgY,MAAI,CAACjO,gBAAgB,EAAEiO,MAAI,CAAC9O,gBAAgB,EAAElB,KAAK,CAAC8C,sBAAsB,EAAEhF,mBAAmB,CAAC;MACzM,CAAC,CAAC;IACJ;EAAC;IAAA3D,GAAA;IAAA2K,KAAA,EAhrBD,SAAOjE,iBAAiBA,CAACxE,KAAK,EAAEyB,mBAAmB,EAAEoS,iBAAiB,EAAE;MACtE,IAAI/N,SAAS,GAAG9F,KAAK,CAACwC,YAAY,CAACxC,KAAK,CAACyC,IAAI,CAAC;MAC9C,CAAC,CAAC,EAAEpI,UAAU,CAAChC,OAAO,EAAEoJ,mBAAmB,CAACiD,KAAK,IAAI,CAAC,IAAIjD,mBAAmB,CAACkD,IAAI,IAAIlD,mBAAmB,CAACiD,KAAK,GAAG,CAAC,IAAIjD,mBAAmB,CAACkD,IAAI,GAAGmB,SAAS,EAAE,mCAAmC,GAAGrE,mBAAmB,CAACiD,KAAK,GAAG,IAAI,GAAGjD,mBAAmB,CAACkD,IAAI,GAAG,qDAAqD,CAAC;MACvT,IAAIJ,UAAU,GAAG,IAAI1K,eAAe,CAACia,cAAc,CAAChO,SAAS,CAAC;MAC9D,IAAIA,SAAS,GAAG,CAAC,EAAE;QACjB,IAAIiO,UAAU,IAAItS,mBAAmB,EAAAuS,MAAA,KAAAtc,mBAAA,CAAAW,OAAA,EAAMwb,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,EAAE,EAAE;QAChI,KAAK,IAAII,GAAG,GAAG,CAAC,EAAEC,WAAW,GAAGH,UAAU,EAAEE,GAAG,GAAGC,WAAW,CAAC9X,MAAM,EAAE6X,GAAG,EAAE,EAAE;UAC3E,IAAIE,MAAM,GAAGD,WAAW,CAACD,GAAG,CAAC;UAC7B1P,UAAU,CAAC6P,QAAQ,CAACD,MAAM,CAAC;QAC7B;QAIA,IAAInU,KAAK,CAACsC,kBAAkB,IAAI,IAAI,IAAItC,KAAK,CAACsC,kBAAkB,IAAI,CAAC,EAAE;UACrE,IAAI+R,aAAa,GAAGhY,eAAe,CAACwK,oBAAoB,CAAC7G,KAAK,CAAC;UAC/DuE,UAAU,CAAC6P,QAAQ,CAACC,aAAa,CAAC;QACpC;QAKA,IAAIC,gBAAgB,GAAG,IAAIzF,GAAG,CAAC7O,KAAK,CAACqM,mBAAmB,CAAC;QACzDhQ,eAAe,CAACkY,0BAA0B,CAACvU,KAAK,EAAEsU,gBAAgB,EAAE/P,UAAU,EAAE9C,mBAAmB,CAACiD,KAAK,CAAC;MAC5G;MACA,OAAOH,UAAU;IACnB;EAAC;IAAAzG,GAAA;IAAA2K,KAAA,EACD,SAAO5B,oBAAoBA,CAAC7G,KAAK,EAAE;MACjC,IAAIwU,qBAAqB;MACzB,IAAI1O,SAAS,GAAG9F,KAAK,CAACwC,YAAY,CAACxC,KAAK,CAACyC,IAAI,CAAC;MAC9C,IAAIgS,cAAc,GAAGvT,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAAC+G,GAAG,CAACnC,SAAS,GAAG,CAAC,EAAE5E,IAAI,CAACqE,KAAK,CAAC,CAACiP,qBAAqB,GAAGxU,KAAK,CAACsC,kBAAkB,MAAM,IAAI,IAAIkS,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAAC,CAAC,CAAC,CAAC;MAClM,IAAIE,aAAa,GAAGxT,IAAI,CAAC+G,GAAG,CAACnC,SAAS,EAAE2O,cAAc,GAAGxZ,2BAA2B,CAAC+E,KAAK,CAAC9E,kBAAkB,CAAC,CAAC,GAAG,CAAC;MACnH,OAAO;QACLwJ,KAAK,EAAE+P,cAAc;QACrB9P,IAAI,EAAE+P;MACR,CAAC;IACH;EAAC;IAAA5W,GAAA;IAAA2K,KAAA,EACD,SAAO8L,0BAA0BA,CAACvU,KAAK,EAAEsU,gBAAgB,EAAE/P,UAAU,EAAEoQ,OAAO,EAAE;MAC9E,IAAI3H,YAAY,GAAGhN,KAAK,CAAC4M,mBAAmB,GAAG,CAAC,GAAG,CAAC;MACpD,KAAK,IAAIgI,OAAO,GAAGD,OAAO,GAAG,CAAC,EAAEC,OAAO,IAAI,CAAC,EAAEA,OAAO,EAAE,EAAE;QACvD,IAAIN,gBAAgB,CAACjH,GAAG,CAACuH,OAAO,GAAG5H,YAAY,CAAC,EAAE;UAChDzI,UAAU,CAAC6P,QAAQ,CAAC;YAClB1P,KAAK,EAAEkQ,OAAO;YACdjQ,IAAI,EAAEiQ;UACR,CAAC,CAAC;UACF;QACF;MACF;IACF;EAAC;IAAA9W,GAAA;IAAA2K,KAAA,EA0GD,SAAOoM,wBAAwBA,CAACjX,QAAQ,EAAEkX,SAAS,EAAE;MAGnD,IAAIhP,SAAS,GAAGlI,QAAQ,CAAC4E,YAAY,CAAC5E,QAAQ,CAAC6E,IAAI,CAAC;MACpD,IAAIqD,SAAS,KAAKgP,SAAS,CAACvQ,UAAU,CAACwQ,QAAQ,CAAC,CAAC,EAAE;QACjD,OAAOD,SAAS;MAClB;MACA,IAAIE,gBAAgB,GAAG3Y,eAAe,CAACmO,qBAAqB,CAACsK,SAAS,CAACrT,mBAAmB,EAAE7D,QAAQ,CAAC;MACrG,OAAO;QACL6D,mBAAmB,EAAEuT,gBAAgB;QACrCzQ,UAAU,EAAElI,eAAe,CAACmI,iBAAiB,CAAC5G,QAAQ,EAAEoX,gBAAgB;MAC1E,CAAC;IACH;EAAC;IAAAlX,GAAA;IAAA2K,KAAA,EAsDD,SAAO+B,qBAAqBA,CAAC4B,KAAK,EAAEpM,KAAK,EAAE;MACzC,IAAI8F,SAAS,GAAG9F,KAAK,CAACwC,YAAY,CAACxC,KAAK,CAACyC,IAAI,CAAC;MAC9C,IAAIkC,IAAI,GAAGzD,IAAI,CAAC+G,GAAG,CAACnC,SAAS,GAAG,CAAC,EAAEsG,KAAK,CAACzH,IAAI,CAAC;MAC9C,IAAIvJ,mBAAmB,GAAGD,4BAA4B,CAAC6E,KAAK,CAAC5E,mBAAmB,CAAC;MACjF,OAAO;QACLsJ,KAAK,EAAE,CAAC,CAAC,EAAE/K,MAAM,CAACtB,OAAO,EAAE,CAAC,EAAEyN,SAAS,GAAG,CAAC,GAAG1K,mBAAmB,EAAEgR,KAAK,CAAC1H,KAAK,CAAC;QAC/EC,IAAI,EAAJA;MACF,CAAC;IACH;EAAC;AAAA,EA71B2B3K,uBAAuB,CAAC3B,OAAO;AA4yC7DgE,eAAe,CAAC4Y,WAAW,GAAG9a,uBAAuB,CAAC+a,sBAAsB;AAC5E,IAAIxG,MAAM,GAAGjV,WAAW,CAACpB,OAAO,CAAC8c,MAAM,CAAC;EACtCvG,kBAAkB,EAAE;IAClBwG,SAAS,EAAE;EACb,CAAC;EACDzG,oBAAoB,EAAE;IACpByG,SAAS,EAAE;EACb,CAAC;EACDtI,KAAK,EAAE;IACLuI,IAAI,EAAE;EACR,CAAC;EACDjD,gBAAgB,EAAE;IAChBkD,QAAQ,EAAE,UAAU;IACpB9C,GAAG,EAAE,CAAC;IACN+C,KAAK,EAAE;EACT,CAAC;EACDlD,YAAY,EAAE;IACZmD,MAAM,EAAE,CAAC;IACTpT,KAAK,EAAE,EAAE;IACTqT,WAAW,EAAE,MAAM;IACnBC,WAAW,EAAE;EACf,CAAC;EACDnD,iBAAiB,EAAE;IACjBoD,IAAI,EAAE,CAAC;IACPC,eAAe,EAAE;EACnB,CAAC;EACDnD,qBAAqB,EAAE;IACrBkD,IAAI,EAAE,CAAC;IACPF,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE;EACf,CAAC;EACDhD,oBAAoB,EAAE;IACpBiD,IAAI,EAAE,CAAC;IACPF,WAAW,EAAE,KAAK;IAClBC,WAAW,EAAE;EACf;AACF,CAAC,CAAC;AACF,IAAIG,QAAQ,GAAG5c,OAAO,CAACZ,OAAO,GAAGgE,eAAe;AAChDyZ,MAAM,CAAC7c,OAAO,GAAGA,OAAO,CAACZ,OAAO", "ignoreList": []}