a8256d17227caa94f904d9e33b47fa59
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.LocaleProvider = LocaleProvider;
exports.getLocaleDirection = getLocaleDirection;
exports.useLocaleContext = useLocaleContext;
var _react = _interopRequireWildcard(require("react"));
var _isLocaleRTL = require("./isLocaleRTL");
var defaultLocale = {
  direction: 'ltr',
  locale: 'en-US'
};
var LocaleContext = (0, _react.createContext)(defaultLocale);
function getLocaleDirection(locale) {
  return (0, _isLocaleRTL.isLocaleRTL)(locale) ? 'rtl' : 'ltr';
}
function LocaleProvider(props) {
  var direction = props.direction,
    locale = props.locale,
    children = props.children;
  var needsContext = direction || locale;
  return needsContext ? _react.default.createElement(LocaleContext.Provider, {
    children: children,
    value: {
      direction: locale ? getLocaleDirection(locale) : direction,
      locale: locale
    }
  }) : children;
}
function useLocaleContext() {
  return (0, _react.useContext)(LocaleContext);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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