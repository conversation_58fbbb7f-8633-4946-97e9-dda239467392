b4ff9261608c52f35fbf80e89c0348ef
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
import _possibleConstructorReturn from "@babel/runtime/helpers/possibleConstructorReturn";
import _getPrototypeOf from "@babel/runtime/helpers/getPrototypeOf";
import _inherits from "@babel/runtime/helpers/inherits";
function cov_zjqk3t1k9() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\ErrorBoundary.tsx";
  var hash = "d48fb74844437a56aa2880ca9dd66d6a5e9c69ba";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\ErrorBoundary.tsx",
    statementMap: {
      "0": {
        start: {
          line: 28,
          column: 15
        },
        end: {
          line: 36,
          column: 1
        }
      },
      "1": {
        start: {
          line: 40,
          column: 4
        },
        end: {
          line: 40,
          column: 17
        }
      },
      "2": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 46,
          column: 6
        }
      },
      "3": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 55,
          column: 6
        }
      },
      "4": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 70
        }
      },
      "5": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 65,
          column: 7
        }
      },
      "6": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "7": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 69,
          column: 43
        }
      },
      "8": {
        start: {
          line: 73,
          column: 4
        },
        end: {
          line: 73,
          column: 45
        }
      },
      "9": {
        start: {
          line: 76,
          column: 30
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "10": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 96,
          column: 5
        }
      },
      "11": {
        start: {
          line: 79,
          column: 26
        },
        end: {
          line: 87,
          column: 7
        }
      },
      "12": {
        start: {
          line: 89,
          column: 6
        },
        end: {
          line: 89,
          column: 48
        }
      },
      "13": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 95,
          column: 58
        }
      },
      "14": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 106,
          column: 3
        }
      },
      "15": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 105,
          column: 7
        }
      },
      "16": {
        start: {
          line: 108,
          column: 25
        },
        end: {
          line: 112,
          column: 3
        }
      },
      "17": {
        start: {
          line: 111,
          column: 4
        },
        end: {
          line: 111,
          column: 23
        }
      },
      "18": {
        start: {
          line: 114,
          column: 28
        },
        end: {
          line: 140,
          column: 3
        }
      },
      "19": {
        start: {
          line: 115,
          column: 42
        },
        end: {
          line: 115,
          column: 52
        }
      },
      "20": {
        start: {
          line: 117,
          column: 22
        },
        end: {
          line: 123,
          column: 5
        }
      },
      "21": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 139,
          column: 6
        }
      },
      "22": {
        start: {
          line: 133,
          column: 12
        },
        end: {
          line: 133,
          column: 50
        }
      },
      "23": {
        start: {
          line: 134,
          column: 12
        },
        end: {
          line: 134,
          column: 68
        }
      },
      "24": {
        start: {
          line: 142,
          column: 31
        },
        end: {
          line: 176,
          column: 3
        }
      },
      "25": {
        start: {
          line: 143,
          column: 33
        },
        end: {
          line: 143,
          column: 43
        }
      },
      "26": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 145,
          column: 45
        }
      },
      "27": {
        start: {
          line: 145,
          column: 33
        },
        end: {
          line: 145,
          column: 45
        }
      },
      "28": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 175,
          column: 6
        }
      },
      "29": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 245,
          column: 5
        }
      },
      "30": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 183,
          column: 7
        }
      },
      "31": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 35
        }
      },
      "32": {
        start: {
          line: 186,
          column: 6
        },
        end: {
          line: 244,
          column: 8
        }
      },
      "33": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 31
        }
      },
      "34": {
        start: {
          line: 251,
          column: 15
        },
        end: {
          line: 362,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 39,
            column: 2
          },
          end: {
            line: 39,
            column: 3
          }
        },
        loc: {
          start: {
            line: 39,
            column: 28
          },
          end: {
            line: 47,
            column: 3
          }
        },
        line: 39
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 49,
            column: 2
          },
          end: {
            line: 49,
            column: 3
          }
        },
        loc: {
          start: {
            line: 49,
            column: 64
          },
          end: {
            line: 56,
            column: 3
          }
        },
        line: 49
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 58,
            column: 2
          },
          end: {
            line: 58,
            column: 3
          }
        },
        loc: {
          start: {
            line: 58,
            column: 56
          },
          end: {
            line: 74,
            column: 3
          }
        },
        line: 58
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 76,
            column: 30
          },
          end: {
            line: 76,
            column: 31
          }
        },
        loc: {
          start: {
            line: 76,
            column: 70
          },
          end: {
            line: 97,
            column: 3
          }
        },
        line: 76
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 99,
            column: 24
          },
          end: {
            line: 99,
            column: 25
          }
        },
        loc: {
          start: {
            line: 99,
            column: 30
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 99
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 108,
            column: 25
          },
          end: {
            line: 108,
            column: 26
          }
        },
        loc: {
          start: {
            line: 108,
            column: 31
          },
          end: {
            line: 112,
            column: 3
          }
        },
        line: 108
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 114,
            column: 28
          },
          end: {
            line: 114,
            column: 29
          }
        },
        loc: {
          start: {
            line: 114,
            column: 34
          },
          end: {
            line: 140,
            column: 3
          }
        },
        line: 114
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 131,
            column: 19
          },
          end: {
            line: 131,
            column: 20
          }
        },
        loc: {
          start: {
            line: 131,
            column: 25
          },
          end: {
            line: 135,
            column: 11
          }
        },
        line: 131
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 142,
            column: 31
          },
          end: {
            line: 142,
            column: 32
          }
        },
        loc: {
          start: {
            line: 142,
            column: 37
          },
          end: {
            line: 176,
            column: 3
          }
        },
        line: 142
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 178,
            column: 2
          },
          end: {
            line: 178,
            column: 3
          }
        },
        loc: {
          start: {
            line: 178,
            column: 11
          },
          end: {
            line: 248,
            column: 3
          }
        },
        line: 178
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "1": {
        loc: {
          start: {
            line: 86,
            column: 13
          },
          end: {
            line: 86,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 86,
            column: 13
          },
          end: {
            line: 86,
            column: 34
          }
        }, {
          start: {
            line: 86,
            column: 38
          },
          end: {
            line: 86,
            column: 50
          }
        }],
        line: 86
      },
      "2": {
        loc: {
          start: {
            line: 119,
            column: 15
          },
          end: {
            line: 119,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 119,
            column: 15
          },
          end: {
            line: 119,
            column: 29
          }
        }, {
          start: {
            line: 119,
            column: 33
          },
          end: {
            line: 119,
            column: 48
          }
        }],
        line: 119
      },
      "3": {
        loc: {
          start: {
            line: 120,
            column: 13
          },
          end: {
            line: 120,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 120,
            column: 13
          },
          end: {
            line: 120,
            column: 25
          }
        }, {
          start: {
            line: 120,
            column: 29
          },
          end: {
            line: 120,
            column: 45
          }
        }],
        line: 120
      },
      "4": {
        loc: {
          start: {
            line: 121,
            column: 22
          },
          end: {
            line: 121,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 121,
            column: 22
          },
          end: {
            line: 121,
            column: 47
          }
        }, {
          start: {
            line: 121,
            column: 51
          },
          end: {
            line: 121,
            column: 71
          }
        }],
        line: 121
      },
      "5": {
        loc: {
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 145,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 4
          },
          end: {
            line: 145,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 145
      },
      "6": {
        loc: {
          start: {
            line: 157,
            column: 44
          },
          end: {
            line: 157,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 157,
            column: 44
          },
          end: {
            line: 157,
            column: 58
          }
        }, {
          start: {
            line: 157,
            column: 62
          },
          end: {
            line: 157,
            column: 77
          }
        }],
        line: 157
      },
      "7": {
        loc: {
          start: {
            line: 160,
            column: 11
          },
          end: {
            line: 165,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 160,
            column: 11
          },
          end: {
            line: 160,
            column: 23
          }
        }, {
          start: {
            line: 161,
            column: 12
          },
          end: {
            line: 164,
            column: 19
          }
        }],
        line: 160
      },
      "8": {
        loc: {
          start: {
            line: 167,
            column: 11
          },
          end: {
            line: 172,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 11
          },
          end: {
            line: 167,
            column: 36
          }
        }, {
          start: {
            line: 168,
            column: 12
          },
          end: {
            line: 171,
            column: 19
          }
        }],
        line: 167
      },
      "9": {
        loc: {
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 245,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "10": {
        loc: {
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 183,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 183,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "11": {
        loc: {
          start: {
            line: 205,
            column: 19
          },
          end: {
            line: 205,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 205,
            column: 19
          },
          end: {
            line: 205,
            column: 44
          }
        }, {
          start: {
            line: 205,
            column: 48
          },
          end: {
            line: 205,
            column: 72
          }
        }],
        line: 205
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d48fb74844437a56aa2880ca9dd66d6a5e9c69ba"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_zjqk3t1k9 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_zjqk3t1k9();
function _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
import React, { Component } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react-native';
import Button from "./ui/Button";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_zjqk3t1k9().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  yellow: '#f59e0b'
});
var ErrorBoundary = function (_Component) {
  function ErrorBoundary(props) {
    var _this;
    _classCallCheck(this, ErrorBoundary);
    cov_zjqk3t1k9().f[0]++;
    cov_zjqk3t1k9().s[1]++;
    _this = _callSuper(this, ErrorBoundary, [props]);
    _this.logErrorToService = (cov_zjqk3t1k9().s[9]++, function (error, errorInfo) {
      cov_zjqk3t1k9().f[3]++;
      cov_zjqk3t1k9().s[10]++;
      try {
        var _window$location;
        var errorReport = (cov_zjqk3t1k9().s[11]++, {
          errorId: _this.state.errorId,
          message: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack,
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          url: (cov_zjqk3t1k9().b[1][0]++, (_window$location = window.location) == null ? void 0 : _window$location.href) || (cov_zjqk3t1k9().b[1][1]++, 'mobile-app')
        });
        cov_zjqk3t1k9().s[12]++;
        console.log('Error Report:', errorReport);
      } catch (loggingError) {
        cov_zjqk3t1k9().s[13]++;
        console.error('Failed to log error:', loggingError);
      }
    });
    _this.handleRetry = (cov_zjqk3t1k9().s[14]++, function () {
      cov_zjqk3t1k9().f[4]++;
      cov_zjqk3t1k9().s[15]++;
      _this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: ''
      });
    });
    _this.handleGoHome = (cov_zjqk3t1k9().s[16]++, function () {
      cov_zjqk3t1k9().f[5]++;
      cov_zjqk3t1k9().s[17]++;
      _this.handleRetry();
    });
    _this.handleReportBug = (cov_zjqk3t1k9().s[18]++, function () {
      cov_zjqk3t1k9().f[6]++;
      var _ref = (cov_zjqk3t1k9().s[19]++, _this.state),
        error = _ref.error,
        errorInfo = _ref.errorInfo,
        errorId = _ref.errorId;
      var bugReport = (cov_zjqk3t1k9().s[20]++, {
        errorId: errorId,
        message: (cov_zjqk3t1k9().b[2][0]++, error == null ? void 0 : error.message) || (cov_zjqk3t1k9().b[2][1]++, 'Unknown error'),
        stack: (cov_zjqk3t1k9().b[3][0]++, error == null ? void 0 : error.stack) || (cov_zjqk3t1k9().b[3][1]++, 'No stack trace'),
        componentStack: (cov_zjqk3t1k9().b[4][0]++, errorInfo == null ? void 0 : errorInfo.componentStack) || (cov_zjqk3t1k9().b[4][1]++, 'No component stack'),
        timestamp: new Date().toISOString()
      });
      cov_zjqk3t1k9().s[21]++;
      Alert.alert('Report Bug', 'Thank you for helping us improve AceMind! The error details have been collected.', [{
        text: 'Send Report',
        onPress: function onPress() {
          cov_zjqk3t1k9().f[7]++;
          cov_zjqk3t1k9().s[22]++;
          console.log('Bug Report:', bugReport);
          cov_zjqk3t1k9().s[23]++;
          Alert.alert('Success', 'Bug report sent successfully!');
        }
      }, {
        text: 'Cancel',
        style: 'cancel'
      }]);
    });
    _this.renderErrorDetails = (cov_zjqk3t1k9().s[24]++, function () {
      cov_zjqk3t1k9().f[8]++;
      var _ref2 = (cov_zjqk3t1k9().s[25]++, _this.state),
        error = _ref2.error,
        errorInfo = _ref2.errorInfo;
      cov_zjqk3t1k9().s[26]++;
      if (!_this.props.showDetails) {
        cov_zjqk3t1k9().b[5][0]++;
        cov_zjqk3t1k9().s[27]++;
        return null;
      } else {
        cov_zjqk3t1k9().b[5][1]++;
      }
      cov_zjqk3t1k9().s[28]++;
      return _jsxs(View, {
        style: styles.detailsContainer,
        children: [_jsxs(TouchableOpacity, {
          style: styles.detailsHeader,
          children: [_jsx(Text, {
            style: styles.detailsTitle,
            children: "Error Details"
          }), _jsx(Bug, {
            size: 16,
            color: colors.gray
          })]
        }), _jsxs(ScrollView, {
          style: styles.detailsScroll,
          showsVerticalScrollIndicator: false,
          children: [_jsxs(View, {
            style: styles.errorSection,
            children: [_jsx(Text, {
              style: styles.errorSectionTitle,
              children: "Error Message:"
            }), _jsx(Text, {
              style: styles.errorText,
              children: (cov_zjqk3t1k9().b[6][0]++, error == null ? void 0 : error.message) || (cov_zjqk3t1k9().b[6][1]++, 'Unknown error')
            })]
          }), (cov_zjqk3t1k9().b[7][0]++, error == null ? void 0 : error.stack) && (cov_zjqk3t1k9().b[7][1]++, _jsxs(View, {
            style: styles.errorSection,
            children: [_jsx(Text, {
              style: styles.errorSectionTitle,
              children: "Stack Trace:"
            }), _jsx(Text, {
              style: styles.errorText,
              children: error.stack
            })]
          })), (cov_zjqk3t1k9().b[8][0]++, errorInfo == null ? void 0 : errorInfo.componentStack) && (cov_zjqk3t1k9().b[8][1]++, _jsxs(View, {
            style: styles.errorSection,
            children: [_jsx(Text, {
              style: styles.errorSectionTitle,
              children: "Component Stack:"
            }), _jsx(Text, {
              style: styles.errorText,
              children: errorInfo.componentStack
            })]
          }))]
        })]
      });
    });
    cov_zjqk3t1k9().s[2]++;
    _this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
    return _this;
  }
  _inherits(ErrorBoundary, _Component);
  return _createClass(ErrorBoundary, [{
    key: "componentDidCatch",
    value: function componentDidCatch(error, errorInfo) {
      cov_zjqk3t1k9().f[2]++;
      cov_zjqk3t1k9().s[4]++;
      console.error('ErrorBoundary caught an error:', error, errorInfo);
      cov_zjqk3t1k9().s[5]++;
      this.setState({
        error: error,
        errorInfo: errorInfo
      });
      cov_zjqk3t1k9().s[6]++;
      if (this.props.onError) {
        cov_zjqk3t1k9().b[0][0]++;
        cov_zjqk3t1k9().s[7]++;
        this.props.onError(error, errorInfo);
      } else {
        cov_zjqk3t1k9().b[0][1]++;
      }
      cov_zjqk3t1k9().s[8]++;
      this.logErrorToService(error, errorInfo);
    }
  }, {
    key: "render",
    value: function render() {
      cov_zjqk3t1k9().f[9]++;
      cov_zjqk3t1k9().s[29]++;
      if (this.state.hasError) {
        var _this$state$error;
        cov_zjqk3t1k9().b[9][0]++;
        cov_zjqk3t1k9().s[30]++;
        if (this.props.fallback) {
          cov_zjqk3t1k9().b[10][0]++;
          cov_zjqk3t1k9().s[31]++;
          return this.props.fallback;
        } else {
          cov_zjqk3t1k9().b[10][1]++;
        }
        cov_zjqk3t1k9().s[32]++;
        return _jsx(View, {
          style: styles.container,
          children: _jsx(LinearGradient, {
            colors: ['#ef4444', '#dc2626', '#b91c1c'],
            style: styles.gradient,
            children: _jsxs(View, {
              style: styles.content,
              children: [_jsx(View, {
                style: styles.iconContainer,
                children: _jsx(AlertTriangle, {
                  size: 64,
                  color: colors.white
                })
              }), _jsx(Text, {
                style: styles.title,
                children: "Oops! Something went wrong"
              }), _jsx(Text, {
                style: styles.subtitle,
                children: "We're sorry for the inconvenience. The app encountered an unexpected error."
              }), _jsxs(View, {
                style: styles.errorInfo,
                children: [_jsxs(Text, {
                  style: styles.errorId,
                  children: ["Error ID: ", this.state.errorId]
                }), _jsx(Text, {
                  style: styles.errorMessage,
                  children: (cov_zjqk3t1k9().b[11][0]++, (_this$state$error = this.state.error) == null ? void 0 : _this$state$error.message) || (cov_zjqk3t1k9().b[11][1]++, 'Unknown error occurred')
                })]
              }), _jsxs(View, {
                style: styles.actions,
                children: [_jsx(Button, {
                  title: "Try Again",
                  onPress: this.handleRetry,
                  style: styles.retryButton,
                  icon: _jsx(RefreshCw, {
                    size: 20,
                    color: colors.white
                  })
                }), _jsx(Button, {
                  title: "Go to Home",
                  onPress: this.handleGoHome,
                  style: styles.homeButton,
                  variant: "outline",
                  icon: _jsx(Home, {
                    size: 20,
                    color: colors.white
                  })
                }), _jsx(Button, {
                  title: "Report Bug",
                  onPress: this.handleReportBug,
                  style: styles.reportButton,
                  variant: "outline",
                  icon: _jsx(Bug, {
                    size: 20,
                    color: colors.white
                  })
                })]
              }), this.renderErrorDetails(), _jsx(View, {
                style: styles.footer,
                children: _jsx(Text, {
                  style: styles.footerText,
                  children: "If this problem persists, please contact our support team."
                })
              })]
            })
          })
        });
      } else {
        cov_zjqk3t1k9().b[9][1]++;
      }
      cov_zjqk3t1k9().s[33]++;
      return this.props.children;
    }
  }], [{
    key: "getDerivedStateFromError",
    value: function getDerivedStateFromError(error) {
      cov_zjqk3t1k9().f[1]++;
      cov_zjqk3t1k9().s[3]++;
      return {
        hasError: true,
        error: error,
        errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      };
    }
  }]);
}(Component);
var styles = (cov_zjqk3t1k9().s[34]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  iconContainer: {
    marginBottom: 24
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.white,
    textAlign: 'center',
    marginBottom: 12
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24
  },
  errorInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 16,
    borderRadius: 8,
    marginBottom: 24,
    width: '100%'
  },
  errorId: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 8,
    fontFamily: 'monospace'
  },
  errorMessage: {
    fontSize: 14,
    color: colors.white,
    fontWeight: '500'
  },
  actions: {
    width: '100%',
    gap: 12
  },
  retryButton: {
    backgroundColor: colors.white
  },
  homeButton: {
    borderColor: colors.white
  },
  reportButton: {
    borderColor: colors.white
  },
  detailsContainer: {
    width: '100%',
    marginTop: 24,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    maxHeight: 200
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)'
  },
  detailsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.white
  },
  detailsScroll: {
    flex: 1,
    padding: 12
  },
  errorSection: {
    marginBottom: 16
  },
  errorSectionTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 4
  },
  errorText: {
    fontSize: 11,
    color: 'rgba(255, 255, 255, 0.9)',
    fontFamily: 'monospace',
    lineHeight: 16
  },
  footer: {
    marginTop: 24,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: 'rgba(255, 255, 255, 0.2)'
  },
  footerText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center'
  }
}));
export default ErrorBoundary;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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