{"version": 3, "names": ["ComputerVisionService", "_classCallCheck", "_createClass", "key", "value", "isWebEnvironment", "cov_15w3oqugc4", "f", "s", "window", "_analyzeVideoFrames", "_asyncToGenerator", "videoUrl", "console", "log", "mockFrames", "generateMockFrames", "analysis", "analyzeTechnique", "movements", "analyzeMovements", "highlights", "detectHighlights", "frames", "error", "getFallbackAnalysis", "analyzeVideoFrames", "_x", "apply", "arguments", "detectShotType", "keypoints", "rightWrist", "find", "kp", "name", "leftWrist", "rightShoulder", "leftShoulder", "b", "armExtension", "Math", "abs", "x", "bodyRotation", "armHeight", "y", "analyzeBiomechanics", "shotType", "<PERSON><PERSON><PERSON>", "leftKnee", "rightHip", "leftHip", "kneeBend", "calculateKneeBend", "shoulderRotation", "calculateShoulderRotation", "hipRotation", "calculateHipRotation", "follow<PERSON><PERSON><PERSON>", "calculateFollowThrough", "contactPoint", "calculateContactPoint", "scoreTechnique", "biomechanics", "timing", "biomechanicsScore", "timingScore", "preparation", "contact", "recovery", "round", "analyzeFootwork", "i", "length", "prevFrame", "currentFrame", "movement", "detectMovementType", "push", "generateInsights", "strengths", "improvements", "recommendations", "avgBiomechanics", "reduce", "sum", "a", "overallScore", "avgKneeBend", "avgFollowThrough", "footworkQuality", "m", "quality", "timestamp", "generateMockKeypoints", "confidence", "_this", "map", "frame", "random", "consistency", "powerGeneration", "accuracy", "type", "efficiency", "description", "prevKeypoints", "currentKeypoints", "computerVisionService"], "sources": ["computerVision.ts"], "sourcesContent": ["// Computer Vision Service for Tennis Technique Analysis\n// This service handles pose detection, movement analysis, and technique scoring\n\nexport interface PoseKeypoint {\n  x: number;\n  y: number;\n  confidence: number;\n  name: string;\n}\n\nexport interface TennisFrame {\n  timestamp: number;\n  keypoints: PoseKeypoint[];\n  shotType?: 'forehand' | 'backhand' | 'serve' | 'volley' | 'overhead';\n  phase?: 'preparation' | 'contact' | 'follow_through';\n}\n\nexport interface TechniqueAnalysis {\n  shotType: string;\n  overallScore: number;\n  biomechanics: {\n    kneeBend: number;\n    shoulderRotation: number;\n    hipRotation: number;\n    followThrough: number;\n    contactPoint: number;\n  };\n  timing: {\n    preparation: number;\n    contact: number;\n    recovery: number;\n  };\n  consistency: number;\n  powerGeneration: number;\n  accuracy: number;\n}\n\nexport interface MovementPattern {\n  type: 'lateral' | 'forward' | 'backward' | 'split_step';\n  quality: number;\n  timing: number;\n  efficiency: number;\n}\n\nclass ComputerVisionService {\n  private isWebEnvironment(): boolean {\n    return typeof window !== 'undefined';\n  }\n\n  /**\n   * Analyze video frames for tennis technique\n   * In a real implementation, this would use MediaPipe or similar\n   */\n  async analyzeVideoFrames(videoUrl: string): Promise<{\n    frames: TennisFrame[];\n    analysis: TechniqueAnalysis[];\n    movements: MovementPattern[];\n    highlights: { timestamp: number; type: string; description: string }[];\n  }> {\n    try {\n      // Simulate video processing\n      console.log('Processing video:', videoUrl);\n      \n      // In a real implementation, this would:\n      // 1. Extract frames from video\n      // 2. Run pose detection on each frame\n      // 3. Analyze movement patterns\n      // 4. Score technique elements\n      \n      const mockFrames = this.generateMockFrames();\n      const analysis = this.analyzeTechnique(mockFrames);\n      const movements = this.analyzeMovements(mockFrames);\n      const highlights = this.detectHighlights(mockFrames, analysis);\n\n      return {\n        frames: mockFrames,\n        analysis,\n        movements,\n        highlights,\n      };\n    } catch (error) {\n      console.error('Video analysis error:', error);\n      return this.getFallbackAnalysis();\n    }\n  }\n\n  /**\n   * Detect tennis shot type from pose keypoints\n   */\n  detectShotType(keypoints: PoseKeypoint[]): 'forehand' | 'backhand' | 'serve' | 'volley' | 'overhead' {\n    // Analyze arm positions, body rotation, and racquet position\n    const rightWrist = keypoints.find(kp => kp.name === 'right_wrist');\n    const leftWrist = keypoints.find(kp => kp.name === 'left_wrist');\n    const rightShoulder = keypoints.find(kp => kp.name === 'right_shoulder');\n    const leftShoulder = keypoints.find(kp => kp.name === 'left_shoulder');\n\n    if (!rightWrist || !leftWrist || !rightShoulder || !leftShoulder) {\n      return 'forehand'; // Default\n    }\n\n    // Simple heuristics for shot detection\n    const armExtension = Math.abs(rightWrist.x - rightShoulder.x);\n    const bodyRotation = Math.abs(rightShoulder.x - leftShoulder.x);\n    const armHeight = rightWrist.y;\n\n    // Serve detection (high arm position)\n    if (armHeight < rightShoulder.y - 50) {\n      return 'serve';\n    }\n\n    // Overhead detection (very high arm position)\n    if (armHeight < rightShoulder.y - 100) {\n      return 'overhead';\n    }\n\n    // Volley detection (close to net, compact swing)\n    if (armExtension < 30) {\n      return 'volley';\n    }\n\n    // Forehand vs Backhand based on body rotation\n    if (rightWrist.x > rightShoulder.x + 20) {\n      return 'forehand';\n    } else {\n      return 'backhand';\n    }\n  }\n\n  /**\n   * Analyze biomechanics of tennis technique\n   */\n  analyzeBiomechanics(keypoints: PoseKeypoint[], shotType: string): TechniqueAnalysis['biomechanics'] {\n    const rightKnee = keypoints.find(kp => kp.name === 'right_knee');\n    const leftKnee = keypoints.find(kp => kp.name === 'left_knee');\n    const rightHip = keypoints.find(kp => kp.name === 'right_hip');\n    const leftHip = keypoints.find(kp => kp.name === 'left_hip');\n    const rightShoulder = keypoints.find(kp => kp.name === 'right_shoulder');\n    const leftShoulder = keypoints.find(kp => kp.name === 'left_shoulder');\n\n    // Calculate biomechanical scores (0-100)\n    const kneeBend = this.calculateKneeBend(rightKnee, leftKnee, rightHip, leftHip);\n    const shoulderRotation = this.calculateShoulderRotation(rightShoulder, leftShoulder);\n    const hipRotation = this.calculateHipRotation(rightHip, leftHip);\n    const followThrough = this.calculateFollowThrough(keypoints, shotType);\n    const contactPoint = this.calculateContactPoint(keypoints, shotType);\n\n    return {\n      kneeBend,\n      shoulderRotation,\n      hipRotation,\n      followThrough,\n      contactPoint,\n    };\n  }\n\n  /**\n   * Score overall technique quality\n   */\n  scoreTechnique(biomechanics: TechniqueAnalysis['biomechanics'], timing: TechniqueAnalysis['timing']): number {\n    const biomechanicsScore = (\n      biomechanics.kneeBend +\n      biomechanics.shoulderRotation +\n      biomechanics.hipRotation +\n      biomechanics.followThrough +\n      biomechanics.contactPoint\n    ) / 5;\n\n    const timingScore = (\n      timing.preparation +\n      timing.contact +\n      timing.recovery\n    ) / 3;\n\n    return Math.round((biomechanicsScore * 0.7 + timingScore * 0.3));\n  }\n\n  /**\n   * Detect movement patterns and footwork\n   */\n  analyzeFootwork(frames: TennisFrame[]): MovementPattern[] {\n    const movements: MovementPattern[] = [];\n\n    for (let i = 1; i < frames.length; i++) {\n      const prevFrame = frames[i - 1];\n      const currentFrame = frames[i];\n\n      const movement = this.detectMovementType(prevFrame.keypoints, currentFrame.keypoints);\n      if (movement) {\n        movements.push(movement);\n      }\n    }\n\n    return movements;\n  }\n\n  /**\n   * Generate performance insights from analysis\n   */\n  generateInsights(analysis: TechniqueAnalysis[], movements: MovementPattern[]): {\n    strengths: string[];\n    improvements: string[];\n    recommendations: string[];\n  } {\n    const strengths: string[] = [];\n    const improvements: string[] = [];\n    const recommendations: string[] = [];\n\n    // Analyze technique scores\n    const avgBiomechanics = analysis.reduce((sum, a) => sum + a.overallScore, 0) / analysis.length;\n\n    if (avgBiomechanics > 80) {\n      strengths.push('Excellent overall technique');\n    } else if (avgBiomechanics < 60) {\n      improvements.push('Focus on fundamental technique');\n      recommendations.push('Work with a coach on basic mechanics');\n    }\n\n    // Analyze specific biomechanics\n    const avgKneeBend = analysis.reduce((sum, a) => sum + a.biomechanics.kneeBend, 0) / analysis.length;\n    if (avgKneeBend < 60) {\n      improvements.push('Improve knee bend for more power');\n      recommendations.push('Practice shadow swings with deeper knee bend');\n    }\n\n    const avgFollowThrough = analysis.reduce((sum, a) => sum + a.biomechanics.followThrough, 0) / analysis.length;\n    if (avgFollowThrough > 80) {\n      strengths.push('Excellent follow-through');\n    }\n\n    // Analyze movement patterns\n    const footworkQuality = movements.reduce((sum, m) => sum + m.quality, 0) / movements.length;\n    if (footworkQuality < 70) {\n      improvements.push('Work on footwork and court positioning');\n      recommendations.push('Practice ladder drills and split-step timing');\n    }\n\n    return { strengths, improvements, recommendations };\n  }\n\n  // Private helper methods\n\n  private generateMockFrames(): TennisFrame[] {\n    const frames: TennisFrame[] = [];\n    \n    for (let i = 0; i < 30; i++) {\n      frames.push({\n        timestamp: i * 100, // 100ms intervals\n        keypoints: this.generateMockKeypoints(),\n        shotType: i < 10 ? 'forehand' : i < 20 ? 'backhand' : 'serve' as const,\n      });\n    }\n\n    return frames;\n  }\n\n  private generateMockKeypoints(): PoseKeypoint[] {\n    // Generate realistic tennis pose keypoints\n    return [\n      { x: 320, y: 100, confidence: 0.9, name: 'nose' },\n      { x: 300, y: 150, confidence: 0.8, name: 'left_shoulder' },\n      { x: 340, y: 150, confidence: 0.8, name: 'right_shoulder' },\n      { x: 280, y: 200, confidence: 0.7, name: 'left_elbow' },\n      { x: 360, y: 200, confidence: 0.7, name: 'right_elbow' },\n      { x: 260, y: 250, confidence: 0.6, name: 'left_wrist' },\n      { x: 380, y: 250, confidence: 0.6, name: 'right_wrist' },\n      { x: 310, y: 300, confidence: 0.8, name: 'left_hip' },\n      { x: 330, y: 300, confidence: 0.8, name: 'right_hip' },\n      { x: 300, y: 400, confidence: 0.7, name: 'left_knee' },\n      { x: 340, y: 400, confidence: 0.7, name: 'right_knee' },\n      { x: 290, y: 500, confidence: 0.6, name: 'left_ankle' },\n      { x: 350, y: 500, confidence: 0.6, name: 'right_ankle' },\n    ];\n  }\n\n  private analyzeTechnique(frames: TennisFrame[]): TechniqueAnalysis[] {\n    return frames.map(frame => ({\n      shotType: this.detectShotType(frame.keypoints),\n      overallScore: 78 + Math.random() * 20, // Mock score\n      biomechanics: this.analyzeBiomechanics(frame.keypoints, 'forehand'),\n      timing: {\n        preparation: 75 + Math.random() * 20,\n        contact: 80 + Math.random() * 15,\n        recovery: 70 + Math.random() * 25,\n      },\n      consistency: 75,\n      powerGeneration: 80,\n      accuracy: 85,\n    }));\n  }\n\n  private analyzeMovements(frames: TennisFrame[]): MovementPattern[] {\n    return [\n      { type: 'lateral', quality: 80, timing: 85, efficiency: 75 },\n      { type: 'split_step', quality: 90, timing: 95, efficiency: 88 },\n      { type: 'forward', quality: 75, timing: 70, efficiency: 80 },\n    ];\n  }\n\n  private detectHighlights(frames: TennisFrame[], analysis: TechniqueAnalysis[]) {\n    return [\n      { timestamp: 1200, type: 'excellent_contact', description: 'Perfect contact point on forehand' },\n      { timestamp: 2500, type: 'good_footwork', description: 'Excellent split-step timing' },\n      { timestamp: 4100, type: 'improvement_needed', description: 'Late preparation on backhand' },\n    ];\n  }\n\n  private calculateKneeBend(rightKnee?: PoseKeypoint, leftKnee?: PoseKeypoint, rightHip?: PoseKeypoint, leftHip?: PoseKeypoint): number {\n    // Calculate knee bend angle and score\n    return 75 + Math.random() * 20; // Mock calculation\n  }\n\n  private calculateShoulderRotation(rightShoulder?: PoseKeypoint, leftShoulder?: PoseKeypoint): number {\n    return 80 + Math.random() * 15; // Mock calculation\n  }\n\n  private calculateHipRotation(rightHip?: PoseKeypoint, leftHip?: PoseKeypoint): number {\n    return 78 + Math.random() * 18; // Mock calculation\n  }\n\n  private calculateFollowThrough(keypoints: PoseKeypoint[], shotType: string): number {\n    return 82 + Math.random() * 15; // Mock calculation\n  }\n\n  private calculateContactPoint(keypoints: PoseKeypoint[], shotType: string): number {\n    return 85 + Math.random() * 12; // Mock calculation\n  }\n\n  private detectMovementType(prevKeypoints: PoseKeypoint[], currentKeypoints: PoseKeypoint[]): MovementPattern | null {\n    // Analyze movement between frames\n    return {\n      type: 'lateral',\n      quality: 80,\n      timing: 85,\n      efficiency: 75,\n    };\n  }\n\n  private getFallbackAnalysis() {\n    return {\n      frames: this.generateMockFrames(),\n      analysis: this.analyzeTechnique(this.generateMockFrames()),\n      movements: [\n        { type: 'lateral' as const, quality: 75, timing: 80, efficiency: 70 }\n      ],\n      highlights: [\n        { timestamp: 1000, type: 'good_technique', description: 'Solid overall form' }\n      ],\n    };\n  }\n}\n\nexport const computerVisionService = new ComputerVisionService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IA4CMA,qBAAqB;EAAA,SAAAA,sBAAA;IAAAC,eAAA,OAAAD,qBAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,qBAAA;IAAAG,GAAA;IAAAC,KAAA,EACzB,SAAQC,gBAAgBA,CAAA,EAAY;MAAAC,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MAClC,OAAO,OAAOC,MAAM,KAAK,WAAW;IACtC;EAAC;IAAAN,GAAA;IAAAC,KAAA;MAAA,IAAAM,mBAAA,GAAAC,iBAAA,CAMD,WAAyBC,QAAgB,EAKtC;QAAAN,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QACD,IAAI;UAAAF,cAAA,GAAAE,CAAA;UAEFK,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,QAAQ,CAAC;UAQ1C,IAAMG,UAAU,IAAAT,cAAA,GAAAE,CAAA,OAAG,IAAI,CAACQ,kBAAkB,CAAC,CAAC;UAC5C,IAAMC,QAAQ,IAAAX,cAAA,GAAAE,CAAA,OAAG,IAAI,CAACU,gBAAgB,CAACH,UAAU,CAAC;UAClD,IAAMI,SAAS,IAAAb,cAAA,GAAAE,CAAA,OAAG,IAAI,CAACY,gBAAgB,CAACL,UAAU,CAAC;UACnD,IAAMM,UAAU,IAAAf,cAAA,GAAAE,CAAA,OAAG,IAAI,CAACc,gBAAgB,CAACP,UAAU,EAAEE,QAAQ,CAAC;UAACX,cAAA,GAAAE,CAAA;UAE/D,OAAO;YACLe,MAAM,EAAER,UAAU;YAClBE,QAAQ,EAARA,QAAQ;YACRE,SAAS,EAATA,SAAS;YACTE,UAAU,EAAVA;UACF,CAAC;QACH,CAAC,CAAC,OAAOG,KAAK,EAAE;UAAAlB,cAAA,GAAAE,CAAA;UACdK,OAAO,CAACW,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAAClB,cAAA,GAAAE,CAAA;UAC9C,OAAO,IAAI,CAACiB,mBAAmB,CAAC,CAAC;QACnC;MACF,CAAC;MAAA,SA/BKC,kBAAkBA,CAAAC,EAAA;QAAA,OAAAjB,mBAAA,CAAAkB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBH,kBAAkB;IAAA;EAAA;IAAAvB,GAAA;IAAAC,KAAA,EAoCxB,SAAA0B,cAAcA,CAACC,SAAyB,EAA6D;MAAAzB,cAAA,GAAAC,CAAA;MAEnG,IAAMyB,UAAU,IAAA1B,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,aAAa;MAAD,CAAC,CAAC;MAClE,IAAMC,SAAS,IAAA9B,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,YAAY;MAAD,CAAC,CAAC;MAChE,IAAME,aAAa,IAAA/B,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,gBAAgB;MAAD,CAAC,CAAC;MACxE,IAAMG,YAAY,IAAAhC,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,eAAe;MAAD,CAAC,CAAC;MAAC7B,cAAA,GAAAE,CAAA;MAEvE,IAAI,CAAAF,cAAA,GAAAiC,CAAA,WAACP,UAAU,MAAA1B,cAAA,GAAAiC,CAAA,UAAI,CAACH,SAAS,MAAA9B,cAAA,GAAAiC,CAAA,UAAI,CAACF,aAAa,MAAA/B,cAAA,GAAAiC,CAAA,UAAI,CAACD,YAAY,GAAE;QAAAhC,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QAChE,OAAO,UAAU;MACnB,CAAC;QAAAF,cAAA,GAAAiC,CAAA;MAAA;MAGD,IAAMC,YAAY,IAAAlC,cAAA,GAAAE,CAAA,QAAGiC,IAAI,CAACC,GAAG,CAACV,UAAU,CAACW,CAAC,GAAGN,aAAa,CAACM,CAAC,CAAC;MAC7D,IAAMC,YAAY,IAAAtC,cAAA,GAAAE,CAAA,QAAGiC,IAAI,CAACC,GAAG,CAACL,aAAa,CAACM,CAAC,GAAGL,YAAY,CAACK,CAAC,CAAC;MAC/D,IAAME,SAAS,IAAAvC,cAAA,GAAAE,CAAA,QAAGwB,UAAU,CAACc,CAAC;MAACxC,cAAA,GAAAE,CAAA;MAG/B,IAAIqC,SAAS,GAAGR,aAAa,CAACS,CAAC,GAAG,EAAE,EAAE;QAAAxC,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACpC,OAAO,OAAO;MAChB,CAAC;QAAAF,cAAA,GAAAiC,CAAA;MAAA;MAAAjC,cAAA,GAAAE,CAAA;MAGD,IAAIqC,SAAS,GAAGR,aAAa,CAACS,CAAC,GAAG,GAAG,EAAE;QAAAxC,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACrC,OAAO,UAAU;MACnB,CAAC;QAAAF,cAAA,GAAAiC,CAAA;MAAA;MAAAjC,cAAA,GAAAE,CAAA;MAGD,IAAIgC,YAAY,GAAG,EAAE,EAAE;QAAAlC,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACrB,OAAO,QAAQ;MACjB,CAAC;QAAAF,cAAA,GAAAiC,CAAA;MAAA;MAAAjC,cAAA,GAAAE,CAAA;MAGD,IAAIwB,UAAU,CAACW,CAAC,GAAGN,aAAa,CAACM,CAAC,GAAG,EAAE,EAAE;QAAArC,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACvC,OAAO,UAAU;MACnB,CAAC,MAAM;QAAAF,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACL,OAAO,UAAU;MACnB;IACF;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAA2C,mBAAmBA,CAAChB,SAAyB,EAAEiB,QAAgB,EAAqC;MAAA1C,cAAA,GAAAC,CAAA;MAClG,IAAM0C,SAAS,IAAA3C,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,YAAY;MAAD,CAAC,CAAC;MAChE,IAAMe,QAAQ,IAAA5C,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,WAAW;MAAD,CAAC,CAAC;MAC9D,IAAMgB,QAAQ,IAAA7C,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,WAAW;MAAD,CAAC,CAAC;MAC9D,IAAMiB,OAAO,IAAA9C,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,UAAU;MAAD,CAAC,CAAC;MAC5D,IAAME,aAAa,IAAA/B,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,gBAAgB;MAAD,CAAC,CAAC;MACxE,IAAMG,YAAY,IAAAhC,cAAA,GAAAE,CAAA,QAAGuB,SAAS,CAACE,IAAI,CAAC,UAAAC,EAAE,EAAI;QAAA5B,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA0B,EAAE,CAACC,IAAI,KAAK,eAAe;MAAD,CAAC,CAAC;MAGtE,IAAMkB,QAAQ,IAAA/C,cAAA,GAAAE,CAAA,QAAG,IAAI,CAAC8C,iBAAiB,CAACL,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,CAAC;MAC/E,IAAMG,gBAAgB,IAAAjD,cAAA,GAAAE,CAAA,QAAG,IAAI,CAACgD,yBAAyB,CAACnB,aAAa,EAAEC,YAAY,CAAC;MACpF,IAAMmB,WAAW,IAAAnD,cAAA,GAAAE,CAAA,QAAG,IAAI,CAACkD,oBAAoB,CAACP,QAAQ,EAAEC,OAAO,CAAC;MAChE,IAAMO,aAAa,IAAArD,cAAA,GAAAE,CAAA,QAAG,IAAI,CAACoD,sBAAsB,CAAC7B,SAAS,EAAEiB,QAAQ,CAAC;MACtE,IAAMa,YAAY,IAAAvD,cAAA,GAAAE,CAAA,QAAG,IAAI,CAACsD,qBAAqB,CAAC/B,SAAS,EAAEiB,QAAQ,CAAC;MAAC1C,cAAA,GAAAE,CAAA;MAErE,OAAO;QACL6C,QAAQ,EAARA,QAAQ;QACRE,gBAAgB,EAAhBA,gBAAgB;QAChBE,WAAW,EAAXA,WAAW;QACXE,aAAa,EAAbA,aAAa;QACbE,YAAY,EAAZA;MACF,CAAC;IACH;EAAC;IAAA1D,GAAA;IAAAC,KAAA,EAKD,SAAA2D,cAAcA,CAACC,YAA+C,EAAEC,MAAmC,EAAU;MAAA3D,cAAA,GAAAC,CAAA;MAC3G,IAAM2D,iBAAiB,IAAA5D,cAAA,GAAAE,CAAA,QAAG,CACxBwD,YAAY,CAACX,QAAQ,GACrBW,YAAY,CAACT,gBAAgB,GAC7BS,YAAY,CAACP,WAAW,GACxBO,YAAY,CAACL,aAAa,GAC1BK,YAAY,CAACH,YAAY,IACvB,CAAC;MAEL,IAAMM,WAAW,IAAA7D,cAAA,GAAAE,CAAA,QAAG,CAClByD,MAAM,CAACG,WAAW,GAClBH,MAAM,CAACI,OAAO,GACdJ,MAAM,CAACK,QAAQ,IACb,CAAC;MAAChE,cAAA,GAAAE,CAAA;MAEN,OAAOiC,IAAI,CAAC8B,KAAK,CAAEL,iBAAiB,GAAG,GAAG,GAAGC,WAAW,GAAG,GAAI,CAAC;IAClE;EAAC;IAAAhE,GAAA;IAAAC,KAAA,EAKD,SAAAoE,eAAeA,CAACjD,MAAqB,EAAqB;MAAAjB,cAAA,GAAAC,CAAA;MACxD,IAAMY,SAA4B,IAAAb,cAAA,GAAAE,CAAA,QAAG,EAAE;MAACF,cAAA,GAAAE,CAAA;MAExC,KAAK,IAAIiE,CAAC,IAAAnE,cAAA,GAAAE,CAAA,QAAG,CAAC,GAAEiE,CAAC,GAAGlD,MAAM,CAACmD,MAAM,EAAED,CAAC,EAAE,EAAE;QACtC,IAAME,SAAS,IAAArE,cAAA,GAAAE,CAAA,QAAGe,MAAM,CAACkD,CAAC,GAAG,CAAC,CAAC;QAC/B,IAAMG,YAAY,IAAAtE,cAAA,GAAAE,CAAA,QAAGe,MAAM,CAACkD,CAAC,CAAC;QAE9B,IAAMI,QAAQ,IAAAvE,cAAA,GAAAE,CAAA,QAAG,IAAI,CAACsE,kBAAkB,CAACH,SAAS,CAAC5C,SAAS,EAAE6C,YAAY,CAAC7C,SAAS,CAAC;QAACzB,cAAA,GAAAE,CAAA;QACtF,IAAIqE,QAAQ,EAAE;UAAAvE,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAE,CAAA;UACZW,SAAS,CAAC4D,IAAI,CAACF,QAAQ,CAAC;QAC1B,CAAC;UAAAvE,cAAA,GAAAiC,CAAA;QAAA;MACH;MAACjC,cAAA,GAAAE,CAAA;MAED,OAAOW,SAAS;IAClB;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAKD,SAAA4E,gBAAgBA,CAAC/D,QAA6B,EAAEE,SAA4B,EAI1E;MAAAb,cAAA,GAAAC,CAAA;MACA,IAAM0E,SAAmB,IAAA3E,cAAA,GAAAE,CAAA,QAAG,EAAE;MAC9B,IAAM0E,YAAsB,IAAA5E,cAAA,GAAAE,CAAA,QAAG,EAAE;MACjC,IAAM2E,eAAyB,IAAA7E,cAAA,GAAAE,CAAA,QAAG,EAAE;MAGpC,IAAM4E,eAAe,IAAA9E,cAAA,GAAAE,CAAA,QAAGS,QAAQ,CAACoE,MAAM,CAAC,UAACC,GAAG,EAAEC,CAAC,EAAK;QAAAjF,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA8E,GAAG,GAAGC,CAAC,CAACC,YAAY;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGvE,QAAQ,CAACyD,MAAM;MAACpE,cAAA,GAAAE,CAAA;MAE/F,IAAI4E,eAAe,GAAG,EAAE,EAAE;QAAA9E,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACxByE,SAAS,CAACF,IAAI,CAAC,6BAA6B,CAAC;MAC/C,CAAC,MAAM;QAAAzE,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QAAA,IAAI4E,eAAe,GAAG,EAAE,EAAE;UAAA9E,cAAA,GAAAiC,CAAA;UAAAjC,cAAA,GAAAE,CAAA;UAC/B0E,YAAY,CAACH,IAAI,CAAC,gCAAgC,CAAC;UAACzE,cAAA,GAAAE,CAAA;UACpD2E,eAAe,CAACJ,IAAI,CAAC,sCAAsC,CAAC;QAC9D,CAAC;UAAAzE,cAAA,GAAAiC,CAAA;QAAA;MAAD;MAGA,IAAMkD,WAAW,IAAAnF,cAAA,GAAAE,CAAA,QAAGS,QAAQ,CAACoE,MAAM,CAAC,UAACC,GAAG,EAAEC,CAAC,EAAK;QAAAjF,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA8E,GAAG,GAAGC,CAAC,CAACvB,YAAY,CAACX,QAAQ;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGpC,QAAQ,CAACyD,MAAM;MAACpE,cAAA,GAAAE,CAAA;MACpG,IAAIiF,WAAW,GAAG,EAAE,EAAE;QAAAnF,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACpB0E,YAAY,CAACH,IAAI,CAAC,kCAAkC,CAAC;QAACzE,cAAA,GAAAE,CAAA;QACtD2E,eAAe,CAACJ,IAAI,CAAC,8CAA8C,CAAC;MACtE,CAAC;QAAAzE,cAAA,GAAAiC,CAAA;MAAA;MAED,IAAMmD,gBAAgB,IAAApF,cAAA,GAAAE,CAAA,QAAGS,QAAQ,CAACoE,MAAM,CAAC,UAACC,GAAG,EAAEC,CAAC,EAAK;QAAAjF,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA8E,GAAG,GAAGC,CAAC,CAACvB,YAAY,CAACL,aAAa;MAAD,CAAC,EAAE,CAAC,CAAC,GAAG1C,QAAQ,CAACyD,MAAM;MAACpE,cAAA,GAAAE,CAAA;MAC9G,IAAIkF,gBAAgB,GAAG,EAAE,EAAE;QAAApF,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACzByE,SAAS,CAACF,IAAI,CAAC,0BAA0B,CAAC;MAC5C,CAAC;QAAAzE,cAAA,GAAAiC,CAAA;MAAA;MAGD,IAAMoD,eAAe,IAAArF,cAAA,GAAAE,CAAA,QAAGW,SAAS,CAACkE,MAAM,CAAC,UAACC,GAAG,EAAEM,CAAC,EAAK;QAAAtF,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA,OAAA8E,GAAG,GAAGM,CAAC,CAACC,OAAO;MAAD,CAAC,EAAE,CAAC,CAAC,GAAG1E,SAAS,CAACuD,MAAM;MAACpE,cAAA,GAAAE,CAAA;MAC5F,IAAImF,eAAe,GAAG,EAAE,EAAE;QAAArF,cAAA,GAAAiC,CAAA;QAAAjC,cAAA,GAAAE,CAAA;QACxB0E,YAAY,CAACH,IAAI,CAAC,wCAAwC,CAAC;QAACzE,cAAA,GAAAE,CAAA;QAC5D2E,eAAe,CAACJ,IAAI,CAAC,8CAA8C,CAAC;MACtE,CAAC;QAAAzE,cAAA,GAAAiC,CAAA;MAAA;MAAAjC,cAAA,GAAAE,CAAA;MAED,OAAO;QAAEyE,SAAS,EAATA,SAAS;QAAEC,YAAY,EAAZA,YAAY;QAAEC,eAAe,EAAfA;MAAgB,CAAC;IACrD;EAAC;IAAAhF,GAAA;IAAAC,KAAA,EAID,SAAQY,kBAAkBA,CAAA,EAAkB;MAAAV,cAAA,GAAAC,CAAA;MAC1C,IAAMgB,MAAqB,IAAAjB,cAAA,GAAAE,CAAA,QAAG,EAAE;MAACF,cAAA,GAAAE,CAAA;MAEjC,KAAK,IAAIiE,CAAC,IAAAnE,cAAA,GAAAE,CAAA,QAAG,CAAC,GAAEiE,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QAAAnE,cAAA,GAAAE,CAAA;QAC3Be,MAAM,CAACwD,IAAI,CAAC;UACVe,SAAS,EAAErB,CAAC,GAAG,GAAG;UAClB1C,SAAS,EAAE,IAAI,CAACgE,qBAAqB,CAAC,CAAC;UACvC/C,QAAQ,EAAEyB,CAAC,GAAG,EAAE,IAAAnE,cAAA,GAAAiC,CAAA,WAAG,UAAU,KAAAjC,cAAA,GAAAiC,CAAA,WAAGkC,CAAC,GAAG,EAAE,IAAAnE,cAAA,GAAAiC,CAAA,WAAG,UAAU,KAAAjC,cAAA,GAAAiC,CAAA,WAAG,OAAO,CAAS;QACxE,CAAC,CAAC;MACJ;MAACjC,cAAA,GAAAE,CAAA;MAED,OAAOe,MAAM;IACf;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAQ2F,qBAAqBA,CAAA,EAAmB;MAAAzF,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MAE9C,OAAO,CACL;QAAEmC,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAO,CAAC,EACjD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAgB,CAAC,EAC1D;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAiB,CAAC,EAC3D;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAa,CAAC,EACvD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAc,CAAC,EACxD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAa,CAAC,EACvD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAc,CAAC,EACxD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAW,CAAC,EACrD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAY,CAAC,EACtD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAY,CAAC,EACtD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAa,CAAC,EACvD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAa,CAAC,EACvD;QAAEQ,CAAC,EAAE,GAAG;QAAEG,CAAC,EAAE,GAAG;QAAEkD,UAAU,EAAE,GAAG;QAAE7D,IAAI,EAAE;MAAc,CAAC,CACzD;IACH;EAAC;IAAAhC,GAAA;IAAAC,KAAA,EAED,SAAQc,gBAAgBA,CAACK,MAAqB,EAAuB;MAAA,IAAA0E,KAAA;MAAA3F,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MACnE,OAAOe,MAAM,CAAC2E,GAAG,CAAC,UAAAC,KAAK,EAAK;QAAA7F,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAE,CAAA;QAAA;UAC1BwC,QAAQ,EAAEiD,KAAI,CAACnE,cAAc,CAACqE,KAAK,CAACpE,SAAS,CAAC;UAC9CyD,YAAY,EAAE,EAAE,GAAG/C,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,EAAE;UACrCpC,YAAY,EAAEiC,KAAI,CAAClD,mBAAmB,CAACoD,KAAK,CAACpE,SAAS,EAAE,UAAU,CAAC;UACnEkC,MAAM,EAAE;YACNG,WAAW,EAAE,EAAE,GAAG3B,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,EAAE;YACpC/B,OAAO,EAAE,EAAE,GAAG5B,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,EAAE;YAChC9B,QAAQ,EAAE,EAAE,GAAG7B,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG;UACjC,CAAC;UACDC,WAAW,EAAE,EAAE;UACfC,eAAe,EAAE,EAAE;UACnBC,QAAQ,EAAE;QACZ,CAAC;MAAD,CAAE,CAAC;IACL;EAAC;IAAApG,GAAA;IAAAC,KAAA,EAED,SAAQgB,gBAAgBA,CAACG,MAAqB,EAAqB;MAAAjB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MACjE,OAAO,CACL;QAAEgG,IAAI,EAAE,SAAS;QAAEX,OAAO,EAAE,EAAE;QAAE5B,MAAM,EAAE,EAAE;QAAEwC,UAAU,EAAE;MAAG,CAAC,EAC5D;QAAED,IAAI,EAAE,YAAY;QAAEX,OAAO,EAAE,EAAE;QAAE5B,MAAM,EAAE,EAAE;QAAEwC,UAAU,EAAE;MAAG,CAAC,EAC/D;QAAED,IAAI,EAAE,SAAS;QAAEX,OAAO,EAAE,EAAE;QAAE5B,MAAM,EAAE,EAAE;QAAEwC,UAAU,EAAE;MAAG,CAAC,CAC7D;IACH;EAAC;IAAAtG,GAAA;IAAAC,KAAA,EAED,SAAQkB,gBAAgBA,CAACC,MAAqB,EAAEN,QAA6B,EAAE;MAAAX,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MAC7E,OAAO,CACL;QAAEsF,SAAS,EAAE,IAAI;QAAEU,IAAI,EAAE,mBAAmB;QAAEE,WAAW,EAAE;MAAoC,CAAC,EAChG;QAAEZ,SAAS,EAAE,IAAI;QAAEU,IAAI,EAAE,eAAe;QAAEE,WAAW,EAAE;MAA8B,CAAC,EACtF;QAAEZ,SAAS,EAAE,IAAI;QAAEU,IAAI,EAAE,oBAAoB;QAAEE,WAAW,EAAE;MAA+B,CAAC,CAC7F;IACH;EAAC;IAAAvG,GAAA;IAAAC,KAAA,EAED,SAAQkD,iBAAiBA,CAACL,SAAwB,EAAEC,QAAuB,EAAEC,QAAuB,EAAEC,OAAsB,EAAU;MAAA9C,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MAEpI,OAAO,EAAE,GAAGiC,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,EAAE;IAChC;EAAC;IAAAjG,GAAA;IAAAC,KAAA,EAED,SAAQoD,yBAAyBA,CAACnB,aAA4B,EAAEC,YAA2B,EAAU;MAAAhC,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MACnG,OAAO,EAAE,GAAGiC,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,EAAE;IAChC;EAAC;IAAAjG,GAAA;IAAAC,KAAA,EAED,SAAQsD,oBAAoBA,CAACP,QAAuB,EAAEC,OAAsB,EAAU;MAAA9C,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MACpF,OAAO,EAAE,GAAGiC,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,EAAE;IAChC;EAAC;IAAAjG,GAAA;IAAAC,KAAA,EAED,SAAQwD,sBAAsBA,CAAC7B,SAAyB,EAAEiB,QAAgB,EAAU;MAAA1C,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MAClF,OAAO,EAAE,GAAGiC,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,EAAE;IAChC;EAAC;IAAAjG,GAAA;IAAAC,KAAA,EAED,SAAQ0D,qBAAqBA,CAAC/B,SAAyB,EAAEiB,QAAgB,EAAU;MAAA1C,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MACjF,OAAO,EAAE,GAAGiC,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG,EAAE;IAChC;EAAC;IAAAjG,GAAA;IAAAC,KAAA,EAED,SAAQ0E,kBAAkBA,CAAC6B,aAA6B,EAAEC,gBAAgC,EAA0B;MAAAtG,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MAElH,OAAO;QACLgG,IAAI,EAAE,SAAS;QACfX,OAAO,EAAE,EAAE;QACX5B,MAAM,EAAE,EAAE;QACVwC,UAAU,EAAE;MACd,CAAC;IACH;EAAC;IAAAtG,GAAA;IAAAC,KAAA,EAED,SAAQqB,mBAAmBA,CAAA,EAAG;MAAAnB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAE,CAAA;MAC5B,OAAO;QACLe,MAAM,EAAE,IAAI,CAACP,kBAAkB,CAAC,CAAC;QACjCC,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACF,kBAAkB,CAAC,CAAC,CAAC;QAC1DG,SAAS,EAAE,CACT;UAAEqF,IAAI,EAAE,SAAkB;UAAEX,OAAO,EAAE,EAAE;UAAE5B,MAAM,EAAE,EAAE;UAAEwC,UAAU,EAAE;QAAG,CAAC,CACtE;QACDpF,UAAU,EAAE,CACV;UAAEyE,SAAS,EAAE,IAAI;UAAEU,IAAI,EAAE,gBAAgB;UAAEE,WAAW,EAAE;QAAqB,CAAC;MAElF,CAAC;IACH;EAAC;AAAA;AAGH,OAAO,IAAMG,qBAAqB,IAAAvG,cAAA,GAAAE,CAAA,SAAG,IAAIR,qBAAqB,CAAC,CAAC", "ignoreList": []}