{"version": 3, "names": ["useEffect", "useRef", "useState", "useCallback", "useMemo", "InteractionManager", "AppState", "performanceMonitor", "ComponentPerformanceTracker", "NetworkPerformanceMonitor", "advancedCacheManager", "cov_1jekbn2f1", "s", "useRenderPerformance", "componentName", "f", "renderStartTime", "tracker", "getInstance", "current", "Date", "now", "trackMount", "renderTime", "trackRender", "getStats", "getComponentStats", "usePerformanceMonitor", "operationName", "dependencies", "arguments", "length", "undefined", "b", "_ref", "_ref2", "_slicedToArray", "isMonitoring", "setIsMonitoring", "_ref3", "_ref4", "lastDuration", "setLastDuration", "startMonitoring", "start", "timestamp", "concat", "_toConsumableArray", "stopMonitoring", "metric", "end", "duration", "measureAsync", "_ref5", "_asyncToGenerator", "operation", "result", "_x", "apply", "useAdvancedCache", "key", "fetcher", "options", "_ref6", "_ref6$ttl", "ttl", "_ref6$dependencies", "_ref6$enabled", "enabled", "_ref6$tags", "tags", "_ref6$priority", "priority", "_ref6$staleWhileReval", "staleWhileRevalidate", "_ref6$retryOnError", "retryOnError", "_ref6$maxRetries", "maxRetries", "_ref7", "_ref8", "data", "setData", "_ref9", "_ref0", "loading", "setLoading", "_ref1", "_ref10", "error", "setError", "_ref11", "_ref12", "lastFetched", "setLastFetched", "retryCountRef", "abortControllerRef", "cache<PERSON>ey", "depHash", "JSON", "stringify", "slice", "isStale", "fetchData", "force", "abort", "AbortController", "startTime", "_abortControllerRef$c", "cachedData", "get", "setTimeout", "freshData", "signal", "aborted", "set", "fetchTime", "trackDatabaseQuery", "err", "_abortControllerRef$c2", "Error", "console", "warn", "invalidate", "refresh", "useNetworkPerformance", "_ref15", "averageResponseTime", "totalRequests", "failedRequests", "slowRequests", "_ref16", "stats", "setStats", "networkMonitor", "updateStats", "getNetworkStats", "interval", "setInterval", "clearInterval", "trackRequest", "url", "method", "requestId", "Math", "random", "toString", "substr", "startRequest", "status", "size", "endRequest", "useMemoryMonitoring", "_ref17", "_ref18", "memoryUsage", "setMemoryUsage", "checkMemory", "used", "total", "percentage", "intervalId", "useAppStatePerformance", "_ref19", "currentState", "_ref20", "appState", "setAppState", "_ref21", "_ref22", "backgroundTime", "setBackgroundTime", "_ref23", "_ref24", "foregroundTime", "setForegroundTime", "handleAppStateChange", "nextAppState", "timeInBackground", "timeInForeground", "subscription", "addEventListener", "remove", "useInteractionOptimization", "_ref25", "_ref26", "isInteracting", "setIsInteracting", "runAfterInteractions", "callback", "Promise", "resolve", "deferredUpdate", "delay", "usePerformanceAnalytics", "_ref27", "renderCount", "averageRenderTime", "slowRenders", "memoryLeaks", "_ref28", "analytics", "setAnalytics", "globalManager", "GlobalPerformanceManager", "collectAnalytics", "reports", "getStoredReports", "latest", "Object", "keys", "components", "exportAnalytics", "toISOString", "useAutoOptimization", "_ref31", "_ref31$enableCaching", "enableCaching", "_ref31$enableRenderOp", "enableRenderOptimization", "_ref31$enableNetworkO", "enableNetworkOptimization", "_ref32", "cacheHitRate", "renderOptimizations", "networkOptimizations", "_ref33", "optimizations", "setOptimizations", "isOptimized"], "sources": ["usePerformanceOptimization.ts"], "sourcesContent": ["/**\n * Advanced Performance Optimization Hooks\n * \n * Provides React hooks for performance monitoring, optimization,\n * and real-time performance analytics.\n */\n\nimport { useEffect, useRef, useState, useCallback, useMemo } from 'react';\nimport { InteractionManager, AppState, AppStateStatus } from 'react-native';\nimport {\n  performanceMonitor,\n  ComponentPerformanceTracker,\n  NetworkPerformanceMonitor,\n} from '@/utils/performance';\nimport { advancedCacheManager } from '@/services/caching/AdvancedCacheManager';\n\n/**\n * Hook for tracking component render performance\n */\nexport const useRenderPerformance = (componentName: string) => {\n  const renderStartTime = useRef<number>(0);\n  const tracker = ComponentPerformanceTracker.getInstance();\n\n  useEffect(() => {\n    renderStartTime.current = Date.now();\n    tracker.trackMount(componentName);\n  }, [componentName]);\n\n  useEffect(() => {\n    const renderTime = Date.now() - renderStartTime.current;\n    tracker.trackRender(componentName, renderTime);\n  });\n\n  const getStats = useCallback(() => {\n    return tracker.getComponentStats(componentName);\n  }, [componentName]);\n\n  return { getStats };\n};\n\n/**\n * Hook for performance monitoring with automatic cleanup\n */\nexport const usePerformanceMonitor = (operationName: string, dependencies: any[] = []) => {\n  const [isMonitoring, setIsMonitoring] = useState(false);\n  const [lastDuration, setLastDuration] = useState<number | null>(null);\n\n  const startMonitoring = useCallback(() => {\n    setIsMonitoring(true);\n    performanceMonitor.start(operationName, { \n      timestamp: Date.now(),\n      dependencies: dependencies.length \n    });\n  }, [operationName, ...dependencies]);\n\n  const stopMonitoring = useCallback(() => {\n    const metric = performanceMonitor.end(operationName);\n    setIsMonitoring(false);\n    if (metric) {\n      setLastDuration(metric.duration || 0);\n    }\n    return metric;\n  }, [operationName]);\n\n  const measureAsync = useCallback(async <T>(operation: () => Promise<T>): Promise<T> => {\n    startMonitoring();\n    try {\n      const result = await operation();\n      return result;\n    } finally {\n      stopMonitoring();\n    }\n  }, [startMonitoring, stopMonitoring]);\n\n  return {\n    isMonitoring,\n    lastDuration,\n    startMonitoring,\n    stopMonitoring,\n    measureAsync,\n  };\n};\n\n/**\n * Hook for advanced caching with intelligent strategies\n */\nexport const useAdvancedCache = <T>(\n  key: string,\n  fetcher: () => Promise<T>,\n  options: {\n    ttl?: number;\n    dependencies?: any[];\n    enabled?: boolean;\n    tags?: string[];\n    priority?: 'high' | 'medium' | 'low';\n    staleWhileRevalidate?: boolean;\n    retryOnError?: boolean;\n    maxRetries?: number;\n  } = {}\n) => {\n  const {\n    ttl = 300000,\n    dependencies = [],\n    enabled = true,\n    tags = [],\n    priority = 'medium',\n    staleWhileRevalidate = true,\n    retryOnError = true,\n    maxRetries = 3,\n  } = options;\n\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<Error | null>(null);\n  const [lastFetched, setLastFetched] = useState<number | null>(null);\n  const retryCountRef = useRef(0);\n  const abortControllerRef = useRef<AbortController | null>(null);\n\n  const cacheKey = useMemo(() => {\n    const depHash = dependencies.length > 0 ? JSON.stringify(dependencies).slice(0, 50) : '';\n    return `${key}_${depHash}`;\n  }, [key, ...dependencies]);\n\n  const isStale = useMemo(() => {\n    if (!lastFetched) return true;\n    return Date.now() - lastFetched > ttl;\n  }, [lastFetched, ttl]);\n\n  const fetchData = useCallback(async (force = false) => {\n    if (!enabled) return;\n\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n    }\n\n    abortControllerRef.current = new AbortController();\n    const startTime = Date.now();\n\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Try cache first unless forced\n      if (!force) {\n        const cachedData = await advancedCacheManager.get<T>(cacheKey);\n        if (cachedData !== null) {\n          setData(cachedData);\n          setLastFetched(Date.now());\n          setLoading(false);\n\n          // Background revalidation if stale-while-revalidate is enabled\n          if (staleWhileRevalidate && isStale) {\n            setTimeout(() => fetchData(true), 0);\n          }\n\n          return cachedData;\n        }\n      }\n\n      // Fetch fresh data\n      const freshData = await fetcher();\n\n      if (abortControllerRef.current?.signal.aborted) {\n        return;\n      }\n\n      // Cache the fresh data\n      await advancedCacheManager.set(cacheKey, freshData, {\n        ttl,\n        tags,\n        priority,\n      });\n\n      setData(freshData);\n      setLastFetched(Date.now());\n      retryCountRef.current = 0;\n\n      const fetchTime = Date.now() - startTime;\n      performanceMonitor.trackDatabaseQuery(`cache_fetch_${key}`, fetchTime);\n\n      return freshData;\n\n    } catch (err) {\n      if (abortControllerRef.current?.signal.aborted) {\n        return;\n      }\n\n      const error = err instanceof Error ? err : new Error('Fetch failed');\n\n      // Retry logic\n      if (retryOnError && retryCountRef.current < maxRetries) {\n        retryCountRef.current++;\n        console.warn(`Retrying fetch for ${key} (attempt ${retryCountRef.current})`);\n        setTimeout(() => fetchData(force), 1000 * retryCountRef.current);\n        return;\n      }\n\n      setError(error);\n      console.error(`Cache fetch error for ${key}:`, error);\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  }, [cacheKey, fetcher, enabled, ttl, tags, priority, staleWhileRevalidate, retryOnError, maxRetries, isStale]);\n\n  const invalidate = useCallback(async () => {\n    await advancedCacheManager.invalidate(cacheKey);\n    setData(null);\n    setLastFetched(null);\n  }, [cacheKey]);\n\n  const refresh = useCallback(() => {\n    return fetchData(true);\n  }, [fetchData]);\n\n  useEffect(() => {\n    if (enabled) {\n      fetchData();\n    }\n\n    return () => {\n      if (abortControllerRef.current) {\n        abortControllerRef.current.abort();\n      }\n    };\n  }, [enabled, ...dependencies]);\n\n  return {\n    data,\n    loading,\n    error,\n    refresh,\n    invalidate,\n    fetchData,\n    isStale,\n    lastFetched,\n  };\n};\n\n/**\n * Hook for network performance monitoring\n */\nexport const useNetworkPerformance = () => {\n  const [stats, setStats] = useState({\n    averageResponseTime: 0,\n    totalRequests: 0,\n    failedRequests: 0,\n    slowRequests: 0,\n  });\n\n  const networkMonitor = NetworkPerformanceMonitor.getInstance();\n\n  const updateStats = useCallback(() => {\n    setStats(networkMonitor.getNetworkStats());\n  }, []);\n\n  useEffect(() => {\n    updateStats();\n    const interval = setInterval(updateStats, 5000); // Update every 5 seconds\n    return () => clearInterval(interval);\n  }, [updateStats]);\n\n  const trackRequest = useCallback((url: string, method: string = 'GET') => {\n    const requestId = `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    networkMonitor.startRequest(requestId, url, method);\n    \n    return {\n      end: (status: number, size: number = 0) => {\n        networkMonitor.endRequest(requestId, status, size);\n        updateStats();\n      },\n    };\n  }, [updateStats]);\n\n  return {\n    stats,\n    trackRequest,\n    updateStats,\n  };\n};\n\n/**\n * Hook for memory usage monitoring\n */\nexport const useMemoryMonitoring = (interval = 10000) => {\n  const [memoryUsage, setMemoryUsage] = useState<{\n    used: number;\n    total: number;\n    percentage: number;\n  } | null>(null);\n\n  useEffect(() => {\n    const checkMemory = () => {\n      // In a real React Native app, you'd use a native module\n      // For now, simulate memory usage\n      const used = Math.random() * 100;\n      const total = 512; // MB\n      setMemoryUsage({\n        used,\n        total,\n        percentage: (used / total) * 100,\n      });\n    };\n\n    checkMemory();\n    const intervalId = setInterval(checkMemory, interval);\n    return () => clearInterval(intervalId);\n  }, [interval]);\n\n  return memoryUsage;\n};\n\n/**\n * Hook for app state performance tracking\n */\nexport const useAppStatePerformance = () => {\n  const [appState, setAppState] = useState(AppState.currentState);\n  const [backgroundTime, setBackgroundTime] = useState<number | null>(null);\n  const [foregroundTime, setForegroundTime] = useState<number | null>(null);\n\n  useEffect(() => {\n    const handleAppStateChange = (nextAppState: AppStateStatus) => {\n      const now = Date.now();\n      \n      if (appState === 'background' && nextAppState === 'active') {\n        // App came to foreground\n        setForegroundTime(now);\n        if (backgroundTime) {\n          const timeInBackground = now - backgroundTime;\n          performanceMonitor.start('app_background_duration');\n          performanceMonitor.end('app_background_duration');\n        }\n      } else if (appState === 'active' && nextAppState === 'background') {\n        // App went to background\n        setBackgroundTime(now);\n        if (foregroundTime) {\n          const timeInForeground = now - foregroundTime;\n          performanceMonitor.start('app_foreground_duration');\n          performanceMonitor.end('app_foreground_duration');\n        }\n      }\n      \n      setAppState(nextAppState);\n    };\n\n    const subscription = AppState.addEventListener('change', handleAppStateChange);\n    return () => subscription?.remove();\n  }, [appState, backgroundTime, foregroundTime]);\n\n  return {\n    appState,\n    backgroundTime,\n    foregroundTime,\n  };\n};\n\n/**\n * Hook for interaction-based performance optimization\n */\nexport const useInteractionOptimization = () => {\n  const [isInteracting, setIsInteracting] = useState(false);\n\n  const runAfterInteractions = useCallback(<T>(callback: () => T): Promise<T> => {\n    return new Promise((resolve) => {\n      InteractionManager.runAfterInteractions(() => {\n        const result = callback();\n        resolve(result);\n      });\n    });\n  }, []);\n\n  const deferredUpdate = useCallback((callback: () => void, delay = 0) => {\n    setIsInteracting(true);\n    setTimeout(() => {\n      runAfterInteractions(() => {\n        callback();\n        setIsInteracting(false);\n      });\n    }, delay);\n  }, [runAfterInteractions]);\n\n  return {\n    isInteracting,\n    runAfterInteractions,\n    deferredUpdate,\n  };\n};\n\n/**\n * Hook for performance analytics collection\n */\nexport const usePerformanceAnalytics = () => {\n  const [analytics, setAnalytics] = useState<{\n    renderCount: number;\n    averageRenderTime: number;\n    slowRenders: number;\n    memoryLeaks: number;\n  }>({\n    renderCount: 0,\n    averageRenderTime: 0,\n    slowRenders: 0,\n    memoryLeaks: 0,\n  });\n\n  const globalManager = GlobalPerformanceManager.getInstance();\n\n  const collectAnalytics = useCallback(async () => {\n    try {\n      const reports = await globalManager.getStoredReports();\n      if (reports.length > 0) {\n        const latest = reports[0];\n        setAnalytics({\n          renderCount: Object.keys(latest.components || {}).length,\n          averageRenderTime: 0, // Calculate from component stats\n          slowRenders: 0, // Calculate from component stats\n          memoryLeaks: 0, // Detect from memory patterns\n        });\n      }\n    } catch (error) {\n      console.warn('Failed to collect performance analytics:', error);\n    }\n  }, []);\n\n  useEffect(() => {\n    collectAnalytics();\n    const interval = setInterval(collectAnalytics, 30000); // Every 30 seconds\n    return () => clearInterval(interval);\n  }, [collectAnalytics]);\n\n  const exportAnalytics = useCallback(async () => {\n    const reports = await globalManager.getStoredReports();\n    return {\n      timestamp: new Date().toISOString(),\n      analytics,\n      reports,\n    };\n  }, [analytics]);\n\n  return {\n    analytics,\n    collectAnalytics,\n    exportAnalytics,\n  };\n};\n\n/**\n * Hook for automatic performance optimization\n */\nexport const useAutoOptimization = (options: {\n  enableCaching?: boolean;\n  enableRenderOptimization?: boolean;\n  enableNetworkOptimization?: boolean;\n} = {}) => {\n  const {\n    enableCaching = true,\n    enableRenderOptimization = true,\n    enableNetworkOptimization = true,\n  } = options;\n\n  const [optimizations, setOptimizations] = useState({\n    cacheHitRate: 0,\n    renderOptimizations: 0,\n    networkOptimizations: 0,\n  });\n\n  useEffect(() => {\n    if (enableCaching) {\n      // Enable automatic caching optimizations\n    }\n    \n    if (enableRenderOptimization) {\n      // Enable render optimizations\n    }\n    \n    if (enableNetworkOptimization) {\n      // Enable network optimizations\n    }\n  }, [enableCaching, enableRenderOptimization, enableNetworkOptimization]);\n\n  return {\n    optimizations,\n    isOptimized: optimizations.cacheHitRate > 0.8,\n  };\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACzE,SAASC,kBAAkB,EAAEC,QAAQ,QAAwB,cAAc;AAC3E,SACEC,kBAAkB,EAClBC,2BAA2B,EAC3BC,yBAAyB;AAE3B,SAASC,oBAAoB;AAAkDC,aAAA,GAAAC,CAAA;AAK/E,OAAO,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,aAAqB,EAAK;EAAAH,aAAA,GAAAI,CAAA;EAC7D,IAAMC,eAAe,IAAAL,aAAA,GAAAC,CAAA,OAAGX,MAAM,CAAS,CAAC,CAAC;EACzC,IAAMgB,OAAO,IAAAN,aAAA,GAAAC,CAAA,OAAGJ,2BAA2B,CAACU,WAAW,CAAC,CAAC;EAACP,aAAA,GAAAC,CAAA;EAE1DZ,SAAS,CAAC,YAAM;IAAAW,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACdI,eAAe,CAACG,OAAO,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IAACV,aAAA,GAAAC,CAAA;IACrCK,OAAO,CAACK,UAAU,CAACR,aAAa,CAAC;EACnC,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAACH,aAAA,GAAAC,CAAA;EAEpBZ,SAAS,CAAC,YAAM;IAAAW,aAAA,GAAAI,CAAA;IACd,IAAMQ,UAAU,IAAAZ,aAAA,GAAAC,CAAA,OAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGL,eAAe,CAACG,OAAO;IAACR,aAAA,GAAAC,CAAA;IACxDK,OAAO,CAACO,WAAW,CAACV,aAAa,EAAES,UAAU,CAAC;EAChD,CAAC,CAAC;EAEF,IAAME,QAAQ,IAAAd,aAAA,GAAAC,CAAA,OAAGT,WAAW,CAAC,YAAM;IAAAQ,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACjC,OAAOK,OAAO,CAACS,iBAAiB,CAACZ,aAAa,CAAC;EACjD,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EAACH,aAAA,GAAAC,CAAA;EAEpB,OAAO;IAAEa,QAAQ,EAARA;EAAS,CAAC;AACrB,CAAC;AAACd,aAAA,GAAAC,CAAA;AAKF,OAAO,IAAMe,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,aAAqB,EAA+B;EAAA,IAA7BC,YAAmB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,aAAA,GAAAsB,CAAA,UAAG,EAAE;EAAAtB,aAAA,GAAAI,CAAA;EACnF,IAAAmB,IAAA,IAAAvB,aAAA,GAAAC,CAAA,QAAwCV,QAAQ,CAAC,KAAK,CAAC;IAAAiC,KAAA,GAAAC,cAAA,CAAAF,IAAA;IAAhDG,YAAY,GAAAF,KAAA;IAAEG,eAAe,GAAAH,KAAA;EACpC,IAAAI,KAAA,IAAA5B,aAAA,GAAAC,CAAA,QAAwCV,QAAQ,CAAgB,IAAI,CAAC;IAAAsC,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAA9DE,YAAY,GAAAD,KAAA;IAAEE,eAAe,GAAAF,KAAA;EAEpC,IAAMG,eAAe,IAAAhC,aAAA,GAAAC,CAAA,QAAGT,WAAW,CAAC,YAAM;IAAAQ,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACxC0B,eAAe,CAAC,IAAI,CAAC;IAAC3B,aAAA,GAAAC,CAAA;IACtBL,kBAAkB,CAACqC,KAAK,CAAChB,aAAa,EAAE;MACtCiB,SAAS,EAAEzB,IAAI,CAACC,GAAG,CAAC,CAAC;MACrBQ,YAAY,EAAEA,YAAY,CAACE;IAC7B,CAAC,CAAC;EACJ,CAAC,GAAGH,aAAa,EAAAkB,MAAA,CAAAC,kBAAA,CAAKlB,YAAY,EAAC,CAAC;EAEpC,IAAMmB,cAAc,IAAArC,aAAA,GAAAC,CAAA,QAAGT,WAAW,CAAC,YAAM;IAAAQ,aAAA,GAAAI,CAAA;IACvC,IAAMkC,MAAM,IAAAtC,aAAA,GAAAC,CAAA,QAAGL,kBAAkB,CAAC2C,GAAG,CAACtB,aAAa,CAAC;IAACjB,aAAA,GAAAC,CAAA;IACrD0B,eAAe,CAAC,KAAK,CAAC;IAAC3B,aAAA,GAAAC,CAAA;IACvB,IAAIqC,MAAM,EAAE;MAAAtC,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAC,CAAA;MACV8B,eAAe,CAAC,CAAA/B,aAAA,GAAAsB,CAAA,UAAAgB,MAAM,CAACE,QAAQ,MAAAxC,aAAA,GAAAsB,CAAA,UAAI,CAAC,EAAC;IACvC,CAAC;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAC,CAAA;IACD,OAAOqC,MAAM;EACf,CAAC,EAAE,CAACrB,aAAa,CAAC,CAAC;EAEnB,IAAMwB,YAAY,IAAAzC,aAAA,GAAAC,CAAA,QAAGT,WAAW;IAAA,IAAAkD,KAAA,GAAAC,iBAAA,CAAC,WAAUC,SAA2B,EAAiB;MAAA5C,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAC,CAAA;MACrF+B,eAAe,CAAC,CAAC;MAAChC,aAAA,GAAAC,CAAA;MAClB,IAAI;QACF,IAAM4C,MAAM,IAAA7C,aAAA,GAAAC,CAAA,cAAS2C,SAAS,CAAC,CAAC;QAAC5C,aAAA,GAAAC,CAAA;QACjC,OAAO4C,MAAM;MACf,CAAC,SAAS;QAAA7C,aAAA,GAAAC,CAAA;QACRoC,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IAAA,iBAAAS,EAAA;MAAA,OAAAJ,KAAA,CAAAK,KAAA,OAAA5B,SAAA;IAAA;EAAA,KAAE,CAACa,eAAe,EAAEK,cAAc,CAAC,CAAC;EAACrC,aAAA,GAAAC,CAAA;EAEtC,OAAO;IACLyB,YAAY,EAAZA,YAAY;IACZI,YAAY,EAAZA,YAAY;IACZE,eAAe,EAAfA,eAAe;IACfK,cAAc,EAAdA,cAAc;IACdI,YAAY,EAAZA;EACF,CAAC;AACH,CAAC;AAACzC,aAAA,GAAAC,CAAA;AAKF,OAAO,IAAM+C,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAC3BC,GAAW,EACXC,OAAyB,EAWtB;EAAA,IAVHC,OASC,GAAAhC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,aAAA,GAAAsB,CAAA,UAAG,CAAC,CAAC;EAAAtB,aAAA,GAAAI,CAAA;EAEN,IAAAgD,KAAA,IAAApD,aAAA,GAAAC,CAAA,QASIkD,OAAO;IAAAE,SAAA,GAAAD,KAAA,CARTE,GAAG;IAAHA,GAAG,GAAAD,SAAA,eAAArD,aAAA,GAAAsB,CAAA,UAAG,MAAM,IAAA+B,SAAA;IAAAE,kBAAA,GAAAH,KAAA,CACZlC,YAAY;IAAZA,YAAY,GAAAqC,kBAAA,eAAAvD,aAAA,GAAAsB,CAAA,UAAG,EAAE,IAAAiC,kBAAA;IAAAC,aAAA,GAAAJ,KAAA,CACjBK,OAAO;IAAPA,OAAO,GAAAD,aAAA,eAAAxD,aAAA,GAAAsB,CAAA,UAAG,IAAI,IAAAkC,aAAA;IAAAE,UAAA,GAAAN,KAAA,CACdO,IAAI;IAAJA,IAAI,GAAAD,UAAA,eAAA1D,aAAA,GAAAsB,CAAA,UAAG,EAAE,IAAAoC,UAAA;IAAAE,cAAA,GAAAR,KAAA,CACTS,QAAQ;IAARA,QAAQ,GAAAD,cAAA,eAAA5D,aAAA,GAAAsB,CAAA,UAAG,QAAQ,IAAAsC,cAAA;IAAAE,qBAAA,GAAAV,KAAA,CACnBW,oBAAoB;IAApBA,oBAAoB,GAAAD,qBAAA,eAAA9D,aAAA,GAAAsB,CAAA,UAAG,IAAI,IAAAwC,qBAAA;IAAAE,kBAAA,GAAAZ,KAAA,CAC3Ba,YAAY;IAAZA,YAAY,GAAAD,kBAAA,eAAAhE,aAAA,GAAAsB,CAAA,WAAG,IAAI,IAAA0C,kBAAA;IAAAE,gBAAA,GAAAd,KAAA,CACnBe,UAAU;IAAVA,UAAU,GAAAD,gBAAA,eAAAlE,aAAA,GAAAsB,CAAA,WAAG,CAAC,IAAA4C,gBAAA;EAGhB,IAAAE,KAAA,IAAApE,aAAA,GAAAC,CAAA,QAAwBV,QAAQ,CAAW,IAAI,CAAC;IAAA8E,KAAA,GAAA5C,cAAA,CAAA2C,KAAA;IAAzCE,IAAI,GAAAD,KAAA;IAAEE,OAAO,GAAAF,KAAA;EACpB,IAAAG,KAAA,IAAAxE,aAAA,GAAAC,CAAA,QAA8BV,QAAQ,CAAC,KAAK,CAAC;IAAAkF,KAAA,GAAAhD,cAAA,CAAA+C,KAAA;IAAtCE,OAAO,GAAAD,KAAA;IAAEE,UAAU,GAAAF,KAAA;EAC1B,IAAAG,KAAA,IAAA5E,aAAA,GAAAC,CAAA,QAA0BV,QAAQ,CAAe,IAAI,CAAC;IAAAsF,MAAA,GAAApD,cAAA,CAAAmD,KAAA;IAA/CE,KAAK,GAAAD,MAAA;IAAEE,QAAQ,GAAAF,MAAA;EACtB,IAAAG,MAAA,IAAAhF,aAAA,GAAAC,CAAA,QAAsCV,QAAQ,CAAgB,IAAI,CAAC;IAAA0F,MAAA,GAAAxD,cAAA,CAAAuD,MAAA;IAA5DE,WAAW,GAAAD,MAAA;IAAEE,cAAc,GAAAF,MAAA;EAClC,IAAMG,aAAa,IAAApF,aAAA,GAAAC,CAAA,QAAGX,MAAM,CAAC,CAAC,CAAC;EAC/B,IAAM+F,kBAAkB,IAAArF,aAAA,GAAAC,CAAA,QAAGX,MAAM,CAAyB,IAAI,CAAC;EAE/D,IAAMgG,QAAQ,IAAAtF,aAAA,GAAAC,CAAA,QAAGR,OAAO,CAAC,YAAM;IAAAO,aAAA,GAAAI,CAAA;IAC7B,IAAMmF,OAAO,IAAAvF,aAAA,GAAAC,CAAA,QAAGiB,YAAY,CAACE,MAAM,GAAG,CAAC,IAAApB,aAAA,GAAAsB,CAAA,WAAGkE,IAAI,CAACC,SAAS,CAACvE,YAAY,CAAC,CAACwE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,KAAA1F,aAAA,GAAAsB,CAAA,WAAG,EAAE;IAACtB,aAAA,GAAAC,CAAA;IACzF,OAAO,GAAGgD,GAAG,IAAIsC,OAAO,EAAE;EAC5B,CAAC,GAAGtC,GAAG,EAAAd,MAAA,CAAAC,kBAAA,CAAKlB,YAAY,EAAC,CAAC;EAE1B,IAAMyE,OAAO,IAAA3F,aAAA,GAAAC,CAAA,QAAGR,OAAO,CAAC,YAAM;IAAAO,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IAC5B,IAAI,CAACiF,WAAW,EAAE;MAAAlF,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAC,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAD,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAC,CAAA;IAC9B,OAAOQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGwE,WAAW,GAAG5B,GAAG;EACvC,CAAC,EAAE,CAAC4B,WAAW,EAAE5B,GAAG,CAAC,CAAC;EAEtB,IAAMsC,SAAS,IAAA5F,aAAA,GAAAC,CAAA,QAAGT,WAAW,CAAAmD,iBAAA,CAAC,aAAyB;IAAA,IAAlBkD,KAAK,GAAA1E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,aAAA,GAAAsB,CAAA,WAAG,KAAK;IAAAtB,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IAChD,IAAI,CAACwD,OAAO,EAAE;MAAAzD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAC,CAAA;MAAA;IAAM,CAAC;MAAAD,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAC,CAAA;IAErB,IAAIoF,kBAAkB,CAAC7E,OAAO,EAAE;MAAAR,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAC,CAAA;MAC9BoF,kBAAkB,CAAC7E,OAAO,CAACsF,KAAK,CAAC,CAAC;IACpC,CAAC;MAAA9F,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAC,CAAA;IAEDoF,kBAAkB,CAAC7E,OAAO,GAAG,IAAIuF,eAAe,CAAC,CAAC;IAClD,IAAMC,SAAS,IAAAhG,aAAA,GAAAC,CAAA,QAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC;IAACV,aAAA,GAAAC,CAAA;IAE7B,IAAI;MAAA,IAAAgG,qBAAA;MAAAjG,aAAA,GAAAC,CAAA;MACF0E,UAAU,CAAC,IAAI,CAAC;MAAC3E,aAAA,GAAAC,CAAA;MACjB8E,QAAQ,CAAC,IAAI,CAAC;MAAC/E,aAAA,GAAAC,CAAA;MAGf,IAAI,CAAC4F,KAAK,EAAE;QAAA7F,aAAA,GAAAsB,CAAA;QACV,IAAM4E,UAAU,IAAAlG,aAAA,GAAAC,CAAA,cAASF,oBAAoB,CAACoG,GAAG,CAAIb,QAAQ,CAAC;QAACtF,aAAA,GAAAC,CAAA;QAC/D,IAAIiG,UAAU,KAAK,IAAI,EAAE;UAAAlG,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAC,CAAA;UACvBsE,OAAO,CAAC2B,UAAU,CAAC;UAAClG,aAAA,GAAAC,CAAA;UACpBkF,cAAc,CAAC1E,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;UAACV,aAAA,GAAAC,CAAA;UAC3B0E,UAAU,CAAC,KAAK,CAAC;UAAC3E,aAAA,GAAAC,CAAA;UAGlB,IAAI,CAAAD,aAAA,GAAAsB,CAAA,WAAAyC,oBAAoB,MAAA/D,aAAA,GAAAsB,CAAA,WAAIqE,OAAO,GAAE;YAAA3F,aAAA,GAAAsB,CAAA;YAAAtB,aAAA,GAAAC,CAAA;YACnCmG,UAAU,CAAC,YAAM;cAAApG,aAAA,GAAAI,CAAA;cAAAJ,aAAA,GAAAC,CAAA;cAAA,OAAA2F,SAAS,CAAC,IAAI,CAAC;YAAD,CAAC,EAAE,CAAC,CAAC;UACtC,CAAC;YAAA5F,aAAA,GAAAsB,CAAA;UAAA;UAAAtB,aAAA,GAAAC,CAAA;UAED,OAAOiG,UAAU;QACnB,CAAC;UAAAlG,aAAA,GAAAsB,CAAA;QAAA;MACH,CAAC;QAAAtB,aAAA,GAAAsB,CAAA;MAAA;MAGD,IAAM+E,SAAS,IAAArG,aAAA,GAAAC,CAAA,cAASiD,OAAO,CAAC,CAAC;MAAClD,aAAA,GAAAC,CAAA;MAElC,KAAAgG,qBAAA,GAAIZ,kBAAkB,CAAC7E,OAAO,aAA1ByF,qBAAA,CAA4BK,MAAM,CAACC,OAAO,EAAE;QAAAvG,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAC,CAAA;QAC9C;MACF,CAAC;QAAAD,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAC,CAAA;MAGD,MAAMF,oBAAoB,CAACyG,GAAG,CAAClB,QAAQ,EAAEe,SAAS,EAAE;QAClD/C,GAAG,EAAHA,GAAG;QACHK,IAAI,EAAJA,IAAI;QACJE,QAAQ,EAARA;MACF,CAAC,CAAC;MAAC7D,aAAA,GAAAC,CAAA;MAEHsE,OAAO,CAAC8B,SAAS,CAAC;MAACrG,aAAA,GAAAC,CAAA;MACnBkF,cAAc,CAAC1E,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;MAACV,aAAA,GAAAC,CAAA;MAC3BmF,aAAa,CAAC5E,OAAO,GAAG,CAAC;MAEzB,IAAMiG,SAAS,IAAAzG,aAAA,GAAAC,CAAA,QAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGsF,SAAS;MAAChG,aAAA,GAAAC,CAAA;MACzCL,kBAAkB,CAAC8G,kBAAkB,CAAC,eAAezD,GAAG,EAAE,EAAEwD,SAAS,CAAC;MAACzG,aAAA,GAAAC,CAAA;MAEvE,OAAOoG,SAAS;IAElB,CAAC,CAAC,OAAOM,GAAG,EAAE;MAAA,IAAAC,sBAAA;MAAA5G,aAAA,GAAAC,CAAA;MACZ,KAAA2G,sBAAA,GAAIvB,kBAAkB,CAAC7E,OAAO,aAA1BoG,sBAAA,CAA4BN,MAAM,CAACC,OAAO,EAAE;QAAAvG,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAC,CAAA;QAC9C;MACF,CAAC;QAAAD,aAAA,GAAAsB,CAAA;MAAA;MAED,IAAMwD,MAAK,IAAA9E,aAAA,GAAAC,CAAA,QAAG0G,GAAG,YAAYE,KAAK,IAAA7G,aAAA,GAAAsB,CAAA,WAAGqF,GAAG,KAAA3G,aAAA,GAAAsB,CAAA,WAAG,IAAIuF,KAAK,CAAC,cAAc,CAAC;MAAC7G,aAAA,GAAAC,CAAA;MAGrE,IAAI,CAAAD,aAAA,GAAAsB,CAAA,WAAA2C,YAAY,MAAAjE,aAAA,GAAAsB,CAAA,WAAI8D,aAAa,CAAC5E,OAAO,GAAG2D,UAAU,GAAE;QAAAnE,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAC,CAAA;QACtDmF,aAAa,CAAC5E,OAAO,EAAE;QAACR,aAAA,GAAAC,CAAA;QACxB6G,OAAO,CAACC,IAAI,CAAC,sBAAsB9D,GAAG,aAAamC,aAAa,CAAC5E,OAAO,GAAG,CAAC;QAACR,aAAA,GAAAC,CAAA;QAC7EmG,UAAU,CAAC,YAAM;UAAApG,aAAA,GAAAI,CAAA;UAAAJ,aAAA,GAAAC,CAAA;UAAA,OAAA2F,SAAS,CAACC,KAAK,CAAC;QAAD,CAAC,EAAE,IAAI,GAAGT,aAAa,CAAC5E,OAAO,CAAC;QAACR,aAAA,GAAAC,CAAA;QACjE;MACF,CAAC;QAAAD,aAAA,GAAAsB,CAAA;MAAA;MAAAtB,aAAA,GAAAC,CAAA;MAED8E,QAAQ,CAACD,MAAK,CAAC;MAAC9E,aAAA,GAAAC,CAAA;MAChB6G,OAAO,CAAChC,KAAK,CAAC,yBAAyB7B,GAAG,GAAG,EAAE6B,MAAK,CAAC;MAAC9E,aAAA,GAAAC,CAAA;MACtD,MAAM6E,MAAK;IACb,CAAC,SAAS;MAAA9E,aAAA,GAAAC,CAAA;MACR0E,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,GAAE,CAACW,QAAQ,EAAEpC,OAAO,EAAEO,OAAO,EAAEH,GAAG,EAAEK,IAAI,EAAEE,QAAQ,EAAEE,oBAAoB,EAAEE,YAAY,EAAEE,UAAU,EAAEwB,OAAO,CAAC,CAAC;EAE9G,IAAMqB,UAAU,IAAAhH,aAAA,GAAAC,CAAA,QAAGT,WAAW,CAAAmD,iBAAA,CAAC,aAAY;IAAA3C,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACzC,MAAMF,oBAAoB,CAACiH,UAAU,CAAC1B,QAAQ,CAAC;IAACtF,aAAA,GAAAC,CAAA;IAChDsE,OAAO,CAAC,IAAI,CAAC;IAACvE,aAAA,GAAAC,CAAA;IACdkF,cAAc,CAAC,IAAI,CAAC;EACtB,CAAC,GAAE,CAACG,QAAQ,CAAC,CAAC;EAEd,IAAM2B,OAAO,IAAAjH,aAAA,GAAAC,CAAA,QAAGT,WAAW,CAAC,YAAM;IAAAQ,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IAChC,OAAO2F,SAAS,CAAC,IAAI,CAAC;EACxB,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EAAC5F,aAAA,GAAAC,CAAA;EAEhBZ,SAAS,CAAC,YAAM;IAAAW,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACd,IAAIwD,OAAO,EAAE;MAAAzD,aAAA,GAAAsB,CAAA;MAAAtB,aAAA,GAAAC,CAAA;MACX2F,SAAS,CAAC,CAAC;IACb,CAAC;MAAA5F,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAC,CAAA;IAED,OAAO,YAAM;MAAAD,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAC,CAAA;MACX,IAAIoF,kBAAkB,CAAC7E,OAAO,EAAE;QAAAR,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAC,CAAA;QAC9BoF,kBAAkB,CAAC7E,OAAO,CAACsF,KAAK,CAAC,CAAC;MACpC,CAAC;QAAA9F,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC;EACH,CAAC,GAAGmC,OAAO,EAAAtB,MAAA,CAAAC,kBAAA,CAAKlB,YAAY,EAAC,CAAC;EAAClB,aAAA,GAAAC,CAAA;EAE/B,OAAO;IACLqE,IAAI,EAAJA,IAAI;IACJI,OAAO,EAAPA,OAAO;IACPI,KAAK,EAALA,KAAK;IACLmC,OAAO,EAAPA,OAAO;IACPD,UAAU,EAAVA,UAAU;IACVpB,SAAS,EAATA,SAAS;IACTD,OAAO,EAAPA,OAAO;IACPT,WAAW,EAAXA;EACF,CAAC;AACH,CAAC;AAAClF,aAAA,GAAAC,CAAA;AAKF,OAAO,IAAMiH,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAA,EAAS;EAAAlH,aAAA,GAAAI,CAAA;EACzC,IAAA+G,MAAA,IAAAnH,aAAA,GAAAC,CAAA,SAA0BV,QAAQ,CAAC;MACjC6H,mBAAmB,EAAE,CAAC;MACtBC,aAAa,EAAE,CAAC;MAChBC,cAAc,EAAE,CAAC;MACjBC,YAAY,EAAE;IAChB,CAAC,CAAC;IAAAC,MAAA,GAAA/F,cAAA,CAAA0F,MAAA;IALKM,KAAK,GAAAD,MAAA;IAAEE,QAAQ,GAAAF,MAAA;EAOtB,IAAMG,cAAc,IAAA3H,aAAA,GAAAC,CAAA,SAAGH,yBAAyB,CAACS,WAAW,CAAC,CAAC;EAE9D,IAAMqH,WAAW,IAAA5H,aAAA,GAAAC,CAAA,SAAGT,WAAW,CAAC,YAAM;IAAAQ,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACpCyH,QAAQ,CAACC,cAAc,CAACE,eAAe,CAAC,CAAC,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;EAAC7H,aAAA,GAAAC,CAAA;EAEPZ,SAAS,CAAC,YAAM;IAAAW,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACd2H,WAAW,CAAC,CAAC;IACb,IAAME,QAAQ,IAAA9H,aAAA,GAAAC,CAAA,SAAG8H,WAAW,CAACH,WAAW,EAAE,IAAI,CAAC;IAAC5H,aAAA,GAAAC,CAAA;IAChD,OAAO,YAAM;MAAAD,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAC,CAAA;MAAA,OAAA+H,aAAa,CAACF,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAACF,WAAW,CAAC,CAAC;EAEjB,IAAMK,YAAY,IAAAjI,aAAA,GAAAC,CAAA,SAAGT,WAAW,CAAC,UAAC0I,GAAW,EAA6B;IAAA,IAA3BC,MAAc,GAAAhH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,aAAA,GAAAsB,CAAA,WAAG,KAAK;IAAAtB,aAAA,GAAAI,CAAA;IACnE,IAAMgI,SAAS,IAAApI,aAAA,GAAAC,CAAA,SAAG,GAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI2H,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAACxI,aAAA,GAAAC,CAAA;IAC7E0H,cAAc,CAACc,YAAY,CAACL,SAAS,EAAEF,GAAG,EAAEC,MAAM,CAAC;IAACnI,aAAA,GAAAC,CAAA;IAEpD,OAAO;MACLsC,GAAG,EAAE,SAALA,GAAGA,CAAGmG,MAAc,EAAuB;QAAA,IAArBC,IAAY,GAAAxH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,aAAA,GAAAsB,CAAA,WAAG,CAAC;QAAAtB,aAAA,GAAAI,CAAA;QAAAJ,aAAA,GAAAC,CAAA;QACpC0H,cAAc,CAACiB,UAAU,CAACR,SAAS,EAAEM,MAAM,EAAEC,IAAI,CAAC;QAAC3I,aAAA,GAAAC,CAAA;QACnD2H,WAAW,CAAC,CAAC;MACf;IACF,CAAC;EACH,CAAC,EAAE,CAACA,WAAW,CAAC,CAAC;EAAC5H,aAAA,GAAAC,CAAA;EAElB,OAAO;IACLwH,KAAK,EAALA,KAAK;IACLQ,YAAY,EAAZA,YAAY;IACZL,WAAW,EAAXA;EACF,CAAC;AACH,CAAC;AAAC5H,aAAA,GAAAC,CAAA;AAKF,OAAO,IAAM4I,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAyB;EAAA,IAArBf,QAAQ,GAAA3G,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,aAAA,GAAAsB,CAAA,WAAG,KAAK;EAAAtB,aAAA,GAAAI,CAAA;EAClD,IAAA0I,MAAA,IAAA9I,aAAA,GAAAC,CAAA,SAAsCV,QAAQ,CAIpC,IAAI,CAAC;IAAAwJ,MAAA,GAAAtH,cAAA,CAAAqH,MAAA;IAJRE,WAAW,GAAAD,MAAA;IAAEE,cAAc,GAAAF,MAAA;EAIlB/I,aAAA,GAAAC,CAAA;EAEhBZ,SAAS,CAAC,YAAM;IAAAW,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACd,IAAMiJ,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MAAAlJ,aAAA,GAAAI,CAAA;MAGxB,IAAM+I,IAAI,IAAAnJ,aAAA,GAAAC,CAAA,SAAGoI,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;MAChC,IAAMc,KAAK,IAAApJ,aAAA,GAAAC,CAAA,SAAG,GAAG;MAACD,aAAA,GAAAC,CAAA;MAClBgJ,cAAc,CAAC;QACbE,IAAI,EAAJA,IAAI;QACJC,KAAK,EAALA,KAAK;QACLC,UAAU,EAAGF,IAAI,GAAGC,KAAK,GAAI;MAC/B,CAAC,CAAC;IACJ,CAAC;IAACpJ,aAAA,GAAAC,CAAA;IAEFiJ,WAAW,CAAC,CAAC;IACb,IAAMI,UAAU,IAAAtJ,aAAA,GAAAC,CAAA,SAAG8H,WAAW,CAACmB,WAAW,EAAEpB,QAAQ,CAAC;IAAC9H,aAAA,GAAAC,CAAA;IACtD,OAAO,YAAM;MAAAD,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAC,CAAA;MAAA,OAAA+H,aAAa,CAACsB,UAAU,CAAC;IAAD,CAAC;EACxC,CAAC,EAAE,CAACxB,QAAQ,CAAC,CAAC;EAAC9H,aAAA,GAAAC,CAAA;EAEf,OAAO+I,WAAW;AACpB,CAAC;AAAChJ,aAAA,GAAAC,CAAA;AAKF,OAAO,IAAMsJ,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA,EAAS;EAAAvJ,aAAA,GAAAI,CAAA;EAC1C,IAAAoJ,MAAA,IAAAxJ,aAAA,GAAAC,CAAA,SAAgCV,QAAQ,CAACI,QAAQ,CAAC8J,YAAY,CAAC;IAAAC,MAAA,GAAAjI,cAAA,CAAA+H,MAAA;IAAxDG,QAAQ,GAAAD,MAAA;IAAEE,WAAW,GAAAF,MAAA;EAC5B,IAAAG,MAAA,IAAA7J,aAAA,GAAAC,CAAA,SAA4CV,QAAQ,CAAgB,IAAI,CAAC;IAAAuK,MAAA,GAAArI,cAAA,CAAAoI,MAAA;IAAlEE,cAAc,GAAAD,MAAA;IAAEE,iBAAiB,GAAAF,MAAA;EACxC,IAAAG,MAAA,IAAAjK,aAAA,GAAAC,CAAA,SAA4CV,QAAQ,CAAgB,IAAI,CAAC;IAAA2K,MAAA,GAAAzI,cAAA,CAAAwI,MAAA;IAAlEE,cAAc,GAAAD,MAAA;IAAEE,iBAAiB,GAAAF,MAAA;EAAkClK,aAAA,GAAAC,CAAA;EAE1EZ,SAAS,CAAC,YAAM;IAAAW,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACd,IAAMoK,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIC,YAA4B,EAAK;MAAAtK,aAAA,GAAAI,CAAA;MAC7D,IAAMM,GAAG,IAAAV,aAAA,GAAAC,CAAA,SAAGQ,IAAI,CAACC,GAAG,CAAC,CAAC;MAACV,aAAA,GAAAC,CAAA;MAEvB,IAAI,CAAAD,aAAA,GAAAsB,CAAA,WAAAqI,QAAQ,KAAK,YAAY,MAAA3J,aAAA,GAAAsB,CAAA,WAAIgJ,YAAY,KAAK,QAAQ,GAAE;QAAAtK,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAC,CAAA;QAE1DmK,iBAAiB,CAAC1J,GAAG,CAAC;QAACV,aAAA,GAAAC,CAAA;QACvB,IAAI8J,cAAc,EAAE;UAAA/J,aAAA,GAAAsB,CAAA;UAClB,IAAMiJ,gBAAgB,IAAAvK,aAAA,GAAAC,CAAA,SAAGS,GAAG,GAAGqJ,cAAc;UAAC/J,aAAA,GAAAC,CAAA;UAC9CL,kBAAkB,CAACqC,KAAK,CAAC,yBAAyB,CAAC;UAACjC,aAAA,GAAAC,CAAA;UACpDL,kBAAkB,CAAC2C,GAAG,CAAC,yBAAyB,CAAC;QACnD,CAAC;UAAAvC,aAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,MAAM;QAAAtB,aAAA,GAAAsB,CAAA;QAAAtB,aAAA,GAAAC,CAAA;QAAA,IAAI,CAAAD,aAAA,GAAAsB,CAAA,WAAAqI,QAAQ,KAAK,QAAQ,MAAA3J,aAAA,GAAAsB,CAAA,WAAIgJ,YAAY,KAAK,YAAY,GAAE;UAAAtK,aAAA,GAAAsB,CAAA;UAAAtB,aAAA,GAAAC,CAAA;UAEjE+J,iBAAiB,CAACtJ,GAAG,CAAC;UAACV,aAAA,GAAAC,CAAA;UACvB,IAAIkK,cAAc,EAAE;YAAAnK,aAAA,GAAAsB,CAAA;YAClB,IAAMkJ,gBAAgB,IAAAxK,aAAA,GAAAC,CAAA,SAAGS,GAAG,GAAGyJ,cAAc;YAACnK,aAAA,GAAAC,CAAA;YAC9CL,kBAAkB,CAACqC,KAAK,CAAC,yBAAyB,CAAC;YAACjC,aAAA,GAAAC,CAAA;YACpDL,kBAAkB,CAAC2C,GAAG,CAAC,yBAAyB,CAAC;UACnD,CAAC;YAAAvC,aAAA,GAAAsB,CAAA;UAAA;QACH,CAAC;UAAAtB,aAAA,GAAAsB,CAAA;QAAA;MAAD;MAACtB,aAAA,GAAAC,CAAA;MAED2J,WAAW,CAACU,YAAY,CAAC;IAC3B,CAAC;IAED,IAAMG,YAAY,IAAAzK,aAAA,GAAAC,CAAA,SAAGN,QAAQ,CAAC+K,gBAAgB,CAAC,QAAQ,EAAEL,oBAAoB,CAAC;IAACrK,aAAA,GAAAC,CAAA;IAC/E,OAAO,YAAM;MAAAD,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAC,CAAA;MAAA,OAAAwK,YAAY,oBAAZA,YAAY,CAAEE,MAAM,CAAC,CAAC;IAAD,CAAC;EACrC,CAAC,EAAE,CAAChB,QAAQ,EAAEI,cAAc,EAAEI,cAAc,CAAC,CAAC;EAACnK,aAAA,GAAAC,CAAA;EAE/C,OAAO;IACL0J,QAAQ,EAARA,QAAQ;IACRI,cAAc,EAAdA,cAAc;IACdI,cAAc,EAAdA;EACF,CAAC;AACH,CAAC;AAACnK,aAAA,GAAAC,CAAA;AAKF,OAAO,IAAM2K,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA,EAAS;EAAA5K,aAAA,GAAAI,CAAA;EAC9C,IAAAyK,MAAA,IAAA7K,aAAA,GAAAC,CAAA,SAA0CV,QAAQ,CAAC,KAAK,CAAC;IAAAuL,MAAA,GAAArJ,cAAA,CAAAoJ,MAAA;IAAlDE,aAAa,GAAAD,MAAA;IAAEE,gBAAgB,GAAAF,MAAA;EAEtC,IAAMG,oBAAoB,IAAAjL,aAAA,GAAAC,CAAA,SAAGT,WAAW,CAAC,UAAI0L,QAAiB,EAAiB;IAAAlL,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IAC7E,OAAO,IAAIkL,OAAO,CAAC,UAACC,OAAO,EAAK;MAAApL,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAC,CAAA;MAC9BP,kBAAkB,CAACuL,oBAAoB,CAAC,YAAM;QAAAjL,aAAA,GAAAI,CAAA;QAC5C,IAAMyC,MAAM,IAAA7C,aAAA,GAAAC,CAAA,SAAGiL,QAAQ,CAAC,CAAC;QAAClL,aAAA,GAAAC,CAAA;QAC1BmL,OAAO,CAACvI,MAAM,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMwI,cAAc,IAAArL,aAAA,GAAAC,CAAA,SAAGT,WAAW,CAAC,UAAC0L,QAAoB,EAAgB;IAAA,IAAdI,KAAK,GAAAnK,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,aAAA,GAAAsB,CAAA,WAAG,CAAC;IAAAtB,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACjE+K,gBAAgB,CAAC,IAAI,CAAC;IAAChL,aAAA,GAAAC,CAAA;IACvBmG,UAAU,CAAC,YAAM;MAAApG,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAC,CAAA;MACfgL,oBAAoB,CAAC,YAAM;QAAAjL,aAAA,GAAAI,CAAA;QAAAJ,aAAA,GAAAC,CAAA;QACzBiL,QAAQ,CAAC,CAAC;QAAClL,aAAA,GAAAC,CAAA;QACX+K,gBAAgB,CAAC,KAAK,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC,EAAEM,KAAK,CAAC;EACX,CAAC,EAAE,CAACL,oBAAoB,CAAC,CAAC;EAACjL,aAAA,GAAAC,CAAA;EAE3B,OAAO;IACL8K,aAAa,EAAbA,aAAa;IACbE,oBAAoB,EAApBA,oBAAoB;IACpBI,cAAc,EAAdA;EACF,CAAC;AACH,CAAC;AAACrL,aAAA,GAAAC,CAAA;AAKF,OAAO,IAAMsL,uBAAuB,GAAG,SAA1BA,uBAAuBA,CAAA,EAAS;EAAAvL,aAAA,GAAAI,CAAA;EAC3C,IAAAoL,MAAA,IAAAxL,aAAA,GAAAC,CAAA,SAAkCV,QAAQ,CAKvC;MACDkM,WAAW,EAAE,CAAC;MACdC,iBAAiB,EAAE,CAAC;MACpBC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE;IACf,CAAC,CAAC;IAAAC,MAAA,GAAApK,cAAA,CAAA+J,MAAA;IAVKM,SAAS,GAAAD,MAAA;IAAEE,YAAY,GAAAF,MAAA;EAY9B,IAAMG,aAAa,IAAAhM,aAAA,GAAAC,CAAA,SAAGgM,wBAAwB,CAAC1L,WAAW,CAAC,CAAC;EAE5D,IAAM2L,gBAAgB,IAAAlM,aAAA,GAAAC,CAAA,SAAGT,WAAW,CAAAmD,iBAAA,CAAC,aAAY;IAAA3C,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IAC/C,IAAI;MACF,IAAMkM,OAAO,IAAAnM,aAAA,GAAAC,CAAA,eAAS+L,aAAa,CAACI,gBAAgB,CAAC,CAAC;MAACpM,aAAA,GAAAC,CAAA;MACvD,IAAIkM,OAAO,CAAC/K,MAAM,GAAG,CAAC,EAAE;QAAApB,aAAA,GAAAsB,CAAA;QACtB,IAAM+K,MAAM,IAAArM,aAAA,GAAAC,CAAA,SAAGkM,OAAO,CAAC,CAAC,CAAC;QAACnM,aAAA,GAAAC,CAAA;QAC1B8L,YAAY,CAAC;UACXN,WAAW,EAAEa,MAAM,CAACC,IAAI,CAAC,CAAAvM,aAAA,GAAAsB,CAAA,WAAA+K,MAAM,CAACG,UAAU,MAAAxM,aAAA,GAAAsB,CAAA,WAAI,CAAC,CAAC,EAAC,CAACF,MAAM;UACxDsK,iBAAiB,EAAE,CAAC;UACpBC,WAAW,EAAE,CAAC;UACdC,WAAW,EAAE;QACf,CAAC,CAAC;MACJ,CAAC;QAAA5L,aAAA,GAAAsB,CAAA;MAAA;IACH,CAAC,CAAC,OAAOwD,KAAK,EAAE;MAAA9E,aAAA,GAAAC,CAAA;MACd6G,OAAO,CAACC,IAAI,CAAC,0CAA0C,EAAEjC,KAAK,CAAC;IACjE;EACF,CAAC,GAAE,EAAE,CAAC;EAAC9E,aAAA,GAAAC,CAAA;EAEPZ,SAAS,CAAC,YAAM;IAAAW,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACdiM,gBAAgB,CAAC,CAAC;IAClB,IAAMpE,QAAQ,IAAA9H,aAAA,GAAAC,CAAA,SAAG8H,WAAW,CAACmE,gBAAgB,EAAE,KAAK,CAAC;IAAClM,aAAA,GAAAC,CAAA;IACtD,OAAO,YAAM;MAAAD,aAAA,GAAAI,CAAA;MAAAJ,aAAA,GAAAC,CAAA;MAAA,OAAA+H,aAAa,CAACF,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAACoE,gBAAgB,CAAC,CAAC;EAEtB,IAAMO,eAAe,IAAAzM,aAAA,GAAAC,CAAA,SAAGT,WAAW,CAAAmD,iBAAA,CAAC,aAAY;IAAA3C,aAAA,GAAAI,CAAA;IAC9C,IAAM+L,OAAO,IAAAnM,aAAA,GAAAC,CAAA,eAAS+L,aAAa,CAACI,gBAAgB,CAAC,CAAC;IAACpM,aAAA,GAAAC,CAAA;IACvD,OAAO;MACLiC,SAAS,EAAE,IAAIzB,IAAI,CAAC,CAAC,CAACiM,WAAW,CAAC,CAAC;MACnCZ,SAAS,EAATA,SAAS;MACTK,OAAO,EAAPA;IACF,CAAC;EACH,CAAC,GAAE,CAACL,SAAS,CAAC,CAAC;EAAC9L,aAAA,GAAAC,CAAA;EAEhB,OAAO;IACL6L,SAAS,EAATA,SAAS;IACTI,gBAAgB,EAAhBA,gBAAgB;IAChBO,eAAe,EAAfA;EACF,CAAC;AACH,CAAC;AAACzM,aAAA,GAAAC,CAAA;AAKF,OAAO,IAAM0M,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAIrB;EAAA,IAJyBxJ,OAInC,GAAAhC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAnB,aAAA,GAAAsB,CAAA,WAAG,CAAC,CAAC;EAAAtB,aAAA,GAAAI,CAAA;EACJ,IAAAwM,MAAA,IAAA5M,aAAA,GAAAC,CAAA,SAIIkD,OAAO;IAAA0J,oBAAA,GAAAD,MAAA,CAHTE,aAAa;IAAbA,aAAa,GAAAD,oBAAA,eAAA7M,aAAA,GAAAsB,CAAA,WAAG,IAAI,IAAAuL,oBAAA;IAAAE,qBAAA,GAAAH,MAAA,CACpBI,wBAAwB;IAAxBA,wBAAwB,GAAAD,qBAAA,eAAA/M,aAAA,GAAAsB,CAAA,WAAG,IAAI,IAAAyL,qBAAA;IAAAE,qBAAA,GAAAL,MAAA,CAC/BM,yBAAyB;IAAzBA,yBAAyB,GAAAD,qBAAA,eAAAjN,aAAA,GAAAsB,CAAA,WAAG,IAAI,IAAA2L,qBAAA;EAGlC,IAAAE,MAAA,IAAAnN,aAAA,GAAAC,CAAA,SAA0CV,QAAQ,CAAC;MACjD6N,YAAY,EAAE,CAAC;MACfC,mBAAmB,EAAE,CAAC;MACtBC,oBAAoB,EAAE;IACxB,CAAC,CAAC;IAAAC,MAAA,GAAA9L,cAAA,CAAA0L,MAAA;IAJKK,aAAa,GAAAD,MAAA;IAAEE,gBAAgB,GAAAF,MAAA;EAInCvN,aAAA,GAAAC,CAAA;EAEHZ,SAAS,CAAC,YAAM;IAAAW,aAAA,GAAAI,CAAA;IAAAJ,aAAA,GAAAC,CAAA;IACd,IAAI6M,aAAa,EAAE;MAAA9M,aAAA,GAAAsB,CAAA;IAEnB,CAAC;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAC,CAAA;IAED,IAAI+M,wBAAwB,EAAE;MAAAhN,aAAA,GAAAsB,CAAA;IAE9B,CAAC;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;IAAAtB,aAAA,GAAAC,CAAA;IAED,IAAIiN,yBAAyB,EAAE;MAAAlN,aAAA,GAAAsB,CAAA;IAE/B,CAAC;MAAAtB,aAAA,GAAAsB,CAAA;IAAA;EACH,CAAC,EAAE,CAACwL,aAAa,EAAEE,wBAAwB,EAAEE,yBAAyB,CAAC,CAAC;EAAClN,aAAA,GAAAC,CAAA;EAEzE,OAAO;IACLuN,aAAa,EAAbA,aAAa;IACbE,WAAW,EAAEF,aAAa,CAACJ,YAAY,GAAG;EAC5C,CAAC;AACH,CAAC", "ignoreList": []}