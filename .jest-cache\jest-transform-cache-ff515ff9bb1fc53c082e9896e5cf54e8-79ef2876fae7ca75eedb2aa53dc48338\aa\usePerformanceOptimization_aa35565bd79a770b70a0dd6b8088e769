6f1ee2e2230678e8014a374e7feeb12b
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1jekbn2f1() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\usePerformanceOptimization.ts";
  var hash = "4a136446c58586d481c2e03ab9af84f416fdb1d4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\usePerformanceOptimization.ts",
    statementMap: {
      "0": {
        start: {
          line: 20,
          column: 36
        },
        end: {
          line: 39,
          column: 1
        }
      },
      "1": {
        start: {
          line: 21,
          column: 26
        },
        end: {
          line: 21,
          column: 43
        }
      },
      "2": {
        start: {
          line: 22,
          column: 18
        },
        end: {
          line: 22,
          column: 59
        }
      },
      "3": {
        start: {
          line: 24,
          column: 2
        },
        end: {
          line: 27,
          column: 22
        }
      },
      "4": {
        start: {
          line: 25,
          column: 4
        },
        end: {
          line: 25,
          column: 41
        }
      },
      "5": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 38
        }
      },
      "6": {
        start: {
          line: 29,
          column: 2
        },
        end: {
          line: 32,
          column: 5
        }
      },
      "7": {
        start: {
          line: 30,
          column: 23
        },
        end: {
          line: 30,
          column: 59
        }
      },
      "8": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 51
        }
      },
      "9": {
        start: {
          line: 34,
          column: 19
        },
        end: {
          line: 36,
          column: 21
        }
      },
      "10": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 35,
          column: 52
        }
      },
      "11": {
        start: {
          line: 38,
          column: 2
        },
        end: {
          line: 38,
          column: 22
        }
      },
      "12": {
        start: {
          line: 44,
          column: 37
        },
        end: {
          line: 82,
          column: 1
        }
      },
      "13": {
        start: {
          line: 45,
          column: 42
        },
        end: {
          line: 45,
          column: 57
        }
      },
      "14": {
        start: {
          line: 46,
          column: 42
        },
        end: {
          line: 46,
          column: 71
        }
      },
      "15": {
        start: {
          line: 48,
          column: 26
        },
        end: {
          line: 54,
          column: 38
        }
      },
      "16": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 26
        }
      },
      "17": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 53,
          column: 7
        }
      },
      "18": {
        start: {
          line: 56,
          column: 25
        },
        end: {
          line: 63,
          column: 21
        }
      },
      "19": {
        start: {
          line: 57,
          column: 19
        },
        end: {
          line: 57,
          column: 56
        }
      },
      "20": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 27
        }
      },
      "21": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 61,
          column: 5
        }
      },
      "22": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 44
        }
      },
      "23": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 18
        }
      },
      "24": {
        start: {
          line: 65,
          column: 23
        },
        end: {
          line: 73,
          column: 39
        }
      },
      "25": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 66,
          column: 22
        }
      },
      "26": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 72,
          column: 5
        }
      },
      "27": {
        start: {
          line: 68,
          column: 21
        },
        end: {
          line: 68,
          column: 38
        }
      },
      "28": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 69,
          column: 20
        }
      },
      "29": {
        start: {
          line: 71,
          column: 6
        },
        end: {
          line: 71,
          column: 23
        }
      },
      "30": {
        start: {
          line: 75,
          column: 2
        },
        end: {
          line: 81,
          column: 4
        }
      },
      "31": {
        start: {
          line: 87,
          column: 32
        },
        end: {
          line: 238,
          column: 1
        }
      },
      "32": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 110,
          column: 13
        }
      },
      "33": {
        start: {
          line: 112,
          column: 26
        },
        end: {
          line: 112,
          column: 50
        }
      },
      "34": {
        start: {
          line: 113,
          column: 32
        },
        end: {
          line: 113,
          column: 47
        }
      },
      "35": {
        start: {
          line: 114,
          column: 28
        },
        end: {
          line: 114,
          column: 56
        }
      },
      "36": {
        start: {
          line: 115,
          column: 40
        },
        end: {
          line: 115,
          column: 69
        }
      },
      "37": {
        start: {
          line: 116,
          column: 24
        },
        end: {
          line: 116,
          column: 33
        }
      },
      "38": {
        start: {
          line: 117,
          column: 29
        },
        end: {
          line: 117,
          column: 65
        }
      },
      "39": {
        start: {
          line: 119,
          column: 19
        },
        end: {
          line: 122,
          column: 28
        }
      },
      "40": {
        start: {
          line: 120,
          column: 20
        },
        end: {
          line: 120,
          column: 92
        }
      },
      "41": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 121,
          column: 31
        }
      },
      "42": {
        start: {
          line: 124,
          column: 18
        },
        end: {
          line: 127,
          column: 24
        }
      },
      "43": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 125,
          column: 34
        }
      },
      "44": {
        start: {
          line: 125,
          column: 22
        },
        end: {
          line: 125,
          column: 34
        }
      },
      "45": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 126,
          column: 42
        }
      },
      "46": {
        start: {
          line: 129,
          column: 20
        },
        end: {
          line: 204,
          column: 112
        }
      },
      "47": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 130,
          column: 25
        }
      },
      "48": {
        start: {
          line: 130,
          column: 18
        },
        end: {
          line: 130,
          column: 25
        }
      },
      "49": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 134,
          column: 5
        }
      },
      "50": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 133,
          column: 41
        }
      },
      "51": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 136,
          column: 55
        }
      },
      "52": {
        start: {
          line: 137,
          column: 22
        },
        end: {
          line: 137,
          column: 32
        }
      },
      "53": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 203,
          column: 5
        }
      },
      "54": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 23
        }
      },
      "55": {
        start: {
          line: 141,
          column: 6
        },
        end: {
          line: 141,
          column: 21
        }
      },
      "56": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 158,
          column: 7
        }
      },
      "57": {
        start: {
          line: 145,
          column: 27
        },
        end: {
          line: 145,
          column: 70
        }
      },
      "58": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 157,
          column: 9
        }
      },
      "59": {
        start: {
          line: 147,
          column: 10
        },
        end: {
          line: 147,
          column: 30
        }
      },
      "60": {
        start: {
          line: 148,
          column: 10
        },
        end: {
          line: 148,
          column: 37
        }
      },
      "61": {
        start: {
          line: 149,
          column: 10
        },
        end: {
          line: 149,
          column: 28
        }
      },
      "62": {
        start: {
          line: 152,
          column: 10
        },
        end: {
          line: 154,
          column: 11
        }
      },
      "63": {
        start: {
          line: 153,
          column: 12
        },
        end: {
          line: 153,
          column: 49
        }
      },
      "64": {
        start: {
          line: 153,
          column: 29
        },
        end: {
          line: 153,
          column: 44
        }
      },
      "65": {
        start: {
          line: 156,
          column: 10
        },
        end: {
          line: 156,
          column: 28
        }
      },
      "66": {
        start: {
          line: 161,
          column: 24
        },
        end: {
          line: 161,
          column: 39
        }
      },
      "67": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 165,
          column: 7
        }
      },
      "68": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 164,
          column: 15
        }
      },
      "69": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 172,
          column: 9
        }
      },
      "70": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 25
        }
      },
      "71": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 33
        }
      },
      "72": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 176,
          column: 32
        }
      },
      "73": {
        start: {
          line: 178,
          column: 24
        },
        end: {
          line: 178,
          column: 46
        }
      },
      "74": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 179,
          column: 77
        }
      },
      "75": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 23
        }
      },
      "76": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 186,
          column: 7
        }
      },
      "77": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 15
        }
      },
      "78": {
        start: {
          line: 188,
          column: 20
        },
        end: {
          line: 188,
          column: 74
        }
      },
      "79": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 196,
          column: 7
        }
      },
      "80": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 192,
          column: 32
        }
      },
      "81": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 193,
          column: 85
        }
      },
      "82": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 194,
          column: 73
        }
      },
      "83": {
        start: {
          line: 194,
          column: 25
        },
        end: {
          line: 194,
          column: 41
        }
      },
      "84": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 195,
          column: 15
        }
      },
      "85": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 198,
          column: 22
        }
      },
      "86": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 199,
          column: 60
        }
      },
      "87": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 200,
          column: 18
        }
      },
      "88": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 202,
          column: 24
        }
      },
      "89": {
        start: {
          line: 206,
          column: 21
        },
        end: {
          line: 210,
          column: 16
        }
      },
      "90": {
        start: {
          line: 207,
          column: 4
        },
        end: {
          line: 207,
          column: 52
        }
      },
      "91": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 208,
          column: 18
        }
      },
      "92": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 209,
          column: 25
        }
      },
      "93": {
        start: {
          line: 212,
          column: 18
        },
        end: {
          line: 214,
          column: 17
        }
      },
      "94": {
        start: {
          line: 213,
          column: 4
        },
        end: {
          line: 213,
          column: 27
        }
      },
      "95": {
        start: {
          line: 216,
          column: 2
        },
        end: {
          line: 226,
          column: 33
        }
      },
      "96": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "97": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 18
        }
      },
      "98": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 225,
          column: 6
        }
      },
      "99": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 224,
          column: 7
        }
      },
      "100": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 223,
          column: 43
        }
      },
      "101": {
        start: {
          line: 228,
          column: 2
        },
        end: {
          line: 237,
          column: 4
        }
      },
      "102": {
        start: {
          line: 243,
          column: 37
        },
        end: {
          line: 280,
          column: 1
        }
      },
      "103": {
        start: {
          line: 244,
          column: 28
        },
        end: {
          line: 249,
          column: 4
        }
      },
      "104": {
        start: {
          line: 251,
          column: 25
        },
        end: {
          line: 251,
          column: 64
        }
      },
      "105": {
        start: {
          line: 253,
          column: 22
        },
        end: {
          line: 255,
          column: 8
        }
      },
      "106": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 254,
          column: 47
        }
      },
      "107": {
        start: {
          line: 257,
          column: 2
        },
        end: {
          line: 261,
          column: 20
        }
      },
      "108": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 258,
          column: 18
        }
      },
      "109": {
        start: {
          line: 259,
          column: 21
        },
        end: {
          line: 259,
          column: 51
        }
      },
      "110": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 260,
          column: 41
        }
      },
      "111": {
        start: {
          line: 260,
          column: 17
        },
        end: {
          line: 260,
          column: 40
        }
      },
      "112": {
        start: {
          line: 263,
          column: 23
        },
        end: {
          line: 273,
          column: 19
        }
      },
      "113": {
        start: {
          line: 264,
          column: 22
        },
        end: {
          line: 264,
          column: 80
        }
      },
      "114": {
        start: {
          line: 265,
          column: 4
        },
        end: {
          line: 265,
          column: 56
        }
      },
      "115": {
        start: {
          line: 267,
          column: 4
        },
        end: {
          line: 272,
          column: 6
        }
      },
      "116": {
        start: {
          line: 269,
          column: 8
        },
        end: {
          line: 269,
          column: 59
        }
      },
      "117": {
        start: {
          line: 270,
          column: 8
        },
        end: {
          line: 270,
          column: 22
        }
      },
      "118": {
        start: {
          line: 275,
          column: 2
        },
        end: {
          line: 279,
          column: 4
        }
      },
      "119": {
        start: {
          line: 285,
          column: 35
        },
        end: {
          line: 311,
          column: 1
        }
      },
      "120": {
        start: {
          line: 286,
          column: 40
        },
        end: {
          line: 290,
          column: 17
        }
      },
      "121": {
        start: {
          line: 292,
          column: 2
        },
        end: {
          line: 308,
          column: 17
        }
      },
      "122": {
        start: {
          line: 293,
          column: 24
        },
        end: {
          line: 303,
          column: 5
        }
      },
      "123": {
        start: {
          line: 296,
          column: 19
        },
        end: {
          line: 296,
          column: 38
        }
      },
      "124": {
        start: {
          line: 297,
          column: 20
        },
        end: {
          line: 297,
          column: 23
        }
      },
      "125": {
        start: {
          line: 298,
          column: 6
        },
        end: {
          line: 302,
          column: 9
        }
      },
      "126": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 305,
          column: 18
        }
      },
      "127": {
        start: {
          line: 306,
          column: 23
        },
        end: {
          line: 306,
          column: 57
        }
      },
      "128": {
        start: {
          line: 307,
          column: 4
        },
        end: {
          line: 307,
          column: 43
        }
      },
      "129": {
        start: {
          line: 307,
          column: 17
        },
        end: {
          line: 307,
          column: 42
        }
      },
      "130": {
        start: {
          line: 310,
          column: 2
        },
        end: {
          line: 310,
          column: 21
        }
      },
      "131": {
        start: {
          line: 316,
          column: 38
        },
        end: {
          line: 355,
          column: 1
        }
      },
      "132": {
        start: {
          line: 317,
          column: 34
        },
        end: {
          line: 317,
          column: 65
        }
      },
      "133": {
        start: {
          line: 318,
          column: 46
        },
        end: {
          line: 318,
          column: 75
        }
      },
      "134": {
        start: {
          line: 319,
          column: 46
        },
        end: {
          line: 319,
          column: 75
        }
      },
      "135": {
        start: {
          line: 321,
          column: 2
        },
        end: {
          line: 348,
          column: 49
        }
      },
      "136": {
        start: {
          line: 322,
          column: 33
        },
        end: {
          line: 344,
          column: 5
        }
      },
      "137": {
        start: {
          line: 323,
          column: 18
        },
        end: {
          line: 323,
          column: 28
        }
      },
      "138": {
        start: {
          line: 325,
          column: 6
        },
        end: {
          line: 341,
          column: 7
        }
      },
      "139": {
        start: {
          line: 327,
          column: 8
        },
        end: {
          line: 327,
          column: 31
        }
      },
      "140": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 332,
          column: 9
        }
      },
      "141": {
        start: {
          line: 329,
          column: 35
        },
        end: {
          line: 329,
          column: 55
        }
      },
      "142": {
        start: {
          line: 330,
          column: 10
        },
        end: {
          line: 330,
          column: 62
        }
      },
      "143": {
        start: {
          line: 331,
          column: 10
        },
        end: {
          line: 331,
          column: 60
        }
      },
      "144": {
        start: {
          line: 333,
          column: 13
        },
        end: {
          line: 341,
          column: 7
        }
      },
      "145": {
        start: {
          line: 335,
          column: 8
        },
        end: {
          line: 335,
          column: 31
        }
      },
      "146": {
        start: {
          line: 336,
          column: 8
        },
        end: {
          line: 340,
          column: 9
        }
      },
      "147": {
        start: {
          line: 337,
          column: 35
        },
        end: {
          line: 337,
          column: 55
        }
      },
      "148": {
        start: {
          line: 338,
          column: 10
        },
        end: {
          line: 338,
          column: 62
        }
      },
      "149": {
        start: {
          line: 339,
          column: 10
        },
        end: {
          line: 339,
          column: 60
        }
      },
      "150": {
        start: {
          line: 343,
          column: 6
        },
        end: {
          line: 343,
          column: 32
        }
      },
      "151": {
        start: {
          line: 346,
          column: 25
        },
        end: {
          line: 346,
          column: 82
        }
      },
      "152": {
        start: {
          line: 347,
          column: 4
        },
        end: {
          line: 347,
          column: 40
        }
      },
      "153": {
        start: {
          line: 347,
          column: 17
        },
        end: {
          line: 347,
          column: 39
        }
      },
      "154": {
        start: {
          line: 350,
          column: 2
        },
        end: {
          line: 354,
          column: 4
        }
      },
      "155": {
        start: {
          line: 360,
          column: 42
        },
        end: {
          line: 387,
          column: 1
        }
      },
      "156": {
        start: {
          line: 361,
          column: 44
        },
        end: {
          line: 361,
          column: 59
        }
      },
      "157": {
        start: {
          line: 363,
          column: 31
        },
        end: {
          line: 370,
          column: 8
        }
      },
      "158": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 369,
          column: 7
        }
      },
      "159": {
        start: {
          line: 365,
          column: 6
        },
        end: {
          line: 368,
          column: 9
        }
      },
      "160": {
        start: {
          line: 366,
          column: 23
        },
        end: {
          line: 366,
          column: 33
        }
      },
      "161": {
        start: {
          line: 367,
          column: 8
        },
        end: {
          line: 367,
          column: 24
        }
      },
      "162": {
        start: {
          line: 372,
          column: 25
        },
        end: {
          line: 380,
          column: 28
        }
      },
      "163": {
        start: {
          line: 373,
          column: 4
        },
        end: {
          line: 373,
          column: 27
        }
      },
      "164": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 379,
          column: 14
        }
      },
      "165": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 378,
          column: 9
        }
      },
      "166": {
        start: {
          line: 376,
          column: 8
        },
        end: {
          line: 376,
          column: 19
        }
      },
      "167": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 377,
          column: 32
        }
      },
      "168": {
        start: {
          line: 382,
          column: 2
        },
        end: {
          line: 386,
          column: 4
        }
      },
      "169": {
        start: {
          line: 392,
          column: 39
        },
        end: {
          line: 444,
          column: 1
        }
      },
      "170": {
        start: {
          line: 393,
          column: 36
        },
        end: {
          line: 403,
          column: 4
        }
      },
      "171": {
        start: {
          line: 405,
          column: 24
        },
        end: {
          line: 405,
          column: 62
        }
      },
      "172": {
        start: {
          line: 407,
          column: 27
        },
        end: {
          line: 422,
          column: 8
        }
      },
      "173": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 421,
          column: 5
        }
      },
      "174": {
        start: {
          line: 409,
          column: 22
        },
        end: {
          line: 409,
          column: 60
        }
      },
      "175": {
        start: {
          line: 410,
          column: 6
        },
        end: {
          line: 418,
          column: 7
        }
      },
      "176": {
        start: {
          line: 411,
          column: 23
        },
        end: {
          line: 411,
          column: 33
        }
      },
      "177": {
        start: {
          line: 412,
          column: 8
        },
        end: {
          line: 417,
          column: 11
        }
      },
      "178": {
        start: {
          line: 420,
          column: 6
        },
        end: {
          line: 420,
          column: 70
        }
      },
      "179": {
        start: {
          line: 424,
          column: 2
        },
        end: {
          line: 428,
          column: 25
        }
      },
      "180": {
        start: {
          line: 425,
          column: 4
        },
        end: {
          line: 425,
          column: 23
        }
      },
      "181": {
        start: {
          line: 426,
          column: 21
        },
        end: {
          line: 426,
          column: 57
        }
      },
      "182": {
        start: {
          line: 427,
          column: 4
        },
        end: {
          line: 427,
          column: 41
        }
      },
      "183": {
        start: {
          line: 427,
          column: 17
        },
        end: {
          line: 427,
          column: 40
        }
      },
      "184": {
        start: {
          line: 430,
          column: 26
        },
        end: {
          line: 437,
          column: 17
        }
      },
      "185": {
        start: {
          line: 431,
          column: 20
        },
        end: {
          line: 431,
          column: 58
        }
      },
      "186": {
        start: {
          line: 432,
          column: 4
        },
        end: {
          line: 436,
          column: 6
        }
      },
      "187": {
        start: {
          line: 439,
          column: 2
        },
        end: {
          line: 443,
          column: 4
        }
      },
      "188": {
        start: {
          line: 449,
          column: 35
        },
        end: {
          line: 484,
          column: 1
        }
      },
      "189": {
        start: {
          line: 458,
          column: 6
        },
        end: {
          line: 458,
          column: 13
        }
      },
      "190": {
        start: {
          line: 460,
          column: 44
        },
        end: {
          line: 464,
          column: 4
        }
      },
      "191": {
        start: {
          line: 466,
          column: 2
        },
        end: {
          line: 478,
          column: 75
        }
      },
      "192": {
        start: {
          line: 467,
          column: 4
        },
        end: {
          line: 469,
          column: 5
        }
      },
      "193": {
        start: {
          line: 471,
          column: 4
        },
        end: {
          line: 473,
          column: 5
        }
      },
      "194": {
        start: {
          line: 475,
          column: 4
        },
        end: {
          line: 477,
          column: 5
        }
      },
      "195": {
        start: {
          line: 480,
          column: 2
        },
        end: {
          line: 483,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 20,
            column: 36
          },
          end: {
            line: 20,
            column: 37
          }
        },
        loc: {
          start: {
            line: 20,
            column: 63
          },
          end: {
            line: 39,
            column: 1
          }
        },
        line: 20
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 24,
            column: 12
          },
          end: {
            line: 24,
            column: 13
          }
        },
        loc: {
          start: {
            line: 24,
            column: 18
          },
          end: {
            line: 27,
            column: 3
          }
        },
        line: 24
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 13
          }
        },
        loc: {
          start: {
            line: 29,
            column: 18
          },
          end: {
            line: 32,
            column: 3
          }
        },
        line: 29
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 34,
            column: 31
          },
          end: {
            line: 34,
            column: 32
          }
        },
        loc: {
          start: {
            line: 34,
            column: 37
          },
          end: {
            line: 36,
            column: 3
          }
        },
        line: 34
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 44,
            column: 37
          },
          end: {
            line: 44,
            column: 38
          }
        },
        loc: {
          start: {
            line: 44,
            column: 90
          },
          end: {
            line: 82,
            column: 1
          }
        },
        line: 44
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 48,
            column: 38
          },
          end: {
            line: 48,
            column: 39
          }
        },
        loc: {
          start: {
            line: 48,
            column: 44
          },
          end: {
            line: 54,
            column: 3
          }
        },
        line: 48
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 56,
            column: 37
          },
          end: {
            line: 56,
            column: 38
          }
        },
        loc: {
          start: {
            line: 56,
            column: 43
          },
          end: {
            line: 63,
            column: 3
          }
        },
        line: 56
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 65,
            column: 35
          },
          end: {
            line: 65,
            column: 36
          }
        },
        loc: {
          start: {
            line: 65,
            column: 89
          },
          end: {
            line: 73,
            column: 3
          }
        },
        line: 65
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 87,
            column: 32
          },
          end: {
            line: 87,
            column: 33
          }
        },
        loc: {
          start: {
            line: 100,
            column: 5
          },
          end: {
            line: 238,
            column: 1
          }
        },
        line: 100
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 119,
            column: 27
          },
          end: {
            line: 119,
            column: 28
          }
        },
        loc: {
          start: {
            line: 119,
            column: 33
          },
          end: {
            line: 122,
            column: 3
          }
        },
        line: 119
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 124,
            column: 26
          },
          end: {
            line: 124,
            column: 27
          }
        },
        loc: {
          start: {
            line: 124,
            column: 32
          },
          end: {
            line: 127,
            column: 3
          }
        },
        line: 124
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 129,
            column: 32
          },
          end: {
            line: 129,
            column: 33
          }
        },
        loc: {
          start: {
            line: 129,
            column: 57
          },
          end: {
            line: 204,
            column: 3
          }
        },
        line: 129
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 153,
            column: 23
          },
          end: {
            line: 153,
            column: 24
          }
        },
        loc: {
          start: {
            line: 153,
            column: 29
          },
          end: {
            line: 153,
            column: 44
          }
        },
        line: 153
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 194,
            column: 19
          },
          end: {
            line: 194,
            column: 20
          }
        },
        loc: {
          start: {
            line: 194,
            column: 25
          },
          end: {
            line: 194,
            column: 41
          }
        },
        line: 194
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 206,
            column: 33
          },
          end: {
            line: 206,
            column: 34
          }
        },
        loc: {
          start: {
            line: 206,
            column: 45
          },
          end: {
            line: 210,
            column: 3
          }
        },
        line: 206
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 212,
            column: 30
          },
          end: {
            line: 212,
            column: 31
          }
        },
        loc: {
          start: {
            line: 212,
            column: 36
          },
          end: {
            line: 214,
            column: 3
          }
        },
        line: 212
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 216,
            column: 12
          },
          end: {
            line: 216,
            column: 13
          }
        },
        loc: {
          start: {
            line: 216,
            column: 18
          },
          end: {
            line: 226,
            column: 3
          }
        },
        line: 216
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 221,
            column: 11
          },
          end: {
            line: 221,
            column: 12
          }
        },
        loc: {
          start: {
            line: 221,
            column: 17
          },
          end: {
            line: 225,
            column: 5
          }
        },
        line: 221
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 243,
            column: 37
          },
          end: {
            line: 243,
            column: 38
          }
        },
        loc: {
          start: {
            line: 243,
            column: 43
          },
          end: {
            line: 280,
            column: 1
          }
        },
        line: 243
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 253,
            column: 34
          },
          end: {
            line: 253,
            column: 35
          }
        },
        loc: {
          start: {
            line: 253,
            column: 40
          },
          end: {
            line: 255,
            column: 3
          }
        },
        line: 253
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 257,
            column: 12
          },
          end: {
            line: 257,
            column: 13
          }
        },
        loc: {
          start: {
            line: 257,
            column: 18
          },
          end: {
            line: 261,
            column: 3
          }
        },
        line: 257
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 260,
            column: 11
          },
          end: {
            line: 260,
            column: 12
          }
        },
        loc: {
          start: {
            line: 260,
            column: 17
          },
          end: {
            line: 260,
            column: 40
          }
        },
        line: 260
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 263,
            column: 35
          },
          end: {
            line: 263,
            column: 36
          }
        },
        loc: {
          start: {
            line: 263,
            column: 76
          },
          end: {
            line: 273,
            column: 3
          }
        },
        line: 263
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 268,
            column: 11
          },
          end: {
            line: 268,
            column: 12
          }
        },
        loc: {
          start: {
            line: 268,
            column: 49
          },
          end: {
            line: 271,
            column: 7
          }
        },
        line: 268
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 285,
            column: 35
          },
          end: {
            line: 285,
            column: 36
          }
        },
        loc: {
          start: {
            line: 285,
            column: 57
          },
          end: {
            line: 311,
            column: 1
          }
        },
        line: 285
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 292,
            column: 12
          },
          end: {
            line: 292,
            column: 13
          }
        },
        loc: {
          start: {
            line: 292,
            column: 18
          },
          end: {
            line: 308,
            column: 3
          }
        },
        line: 292
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 293,
            column: 24
          },
          end: {
            line: 293,
            column: 25
          }
        },
        loc: {
          start: {
            line: 293,
            column: 30
          },
          end: {
            line: 303,
            column: 5
          }
        },
        line: 293
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 307,
            column: 11
          },
          end: {
            line: 307,
            column: 12
          }
        },
        loc: {
          start: {
            line: 307,
            column: 17
          },
          end: {
            line: 307,
            column: 42
          }
        },
        line: 307
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 316,
            column: 38
          },
          end: {
            line: 316,
            column: 39
          }
        },
        loc: {
          start: {
            line: 316,
            column: 44
          },
          end: {
            line: 355,
            column: 1
          }
        },
        line: 316
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 321,
            column: 12
          },
          end: {
            line: 321,
            column: 13
          }
        },
        loc: {
          start: {
            line: 321,
            column: 18
          },
          end: {
            line: 348,
            column: 3
          }
        },
        line: 321
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 322,
            column: 33
          },
          end: {
            line: 322,
            column: 34
          }
        },
        loc: {
          start: {
            line: 322,
            column: 67
          },
          end: {
            line: 344,
            column: 5
          }
        },
        line: 322
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 347,
            column: 11
          },
          end: {
            line: 347,
            column: 12
          }
        },
        loc: {
          start: {
            line: 347,
            column: 17
          },
          end: {
            line: 347,
            column: 39
          }
        },
        line: 347
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 360,
            column: 42
          },
          end: {
            line: 360,
            column: 43
          }
        },
        loc: {
          start: {
            line: 360,
            column: 48
          },
          end: {
            line: 387,
            column: 1
          }
        },
        line: 360
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 363,
            column: 43
          },
          end: {
            line: 363,
            column: 44
          }
        },
        loc: {
          start: {
            line: 363,
            column: 81
          },
          end: {
            line: 370,
            column: 3
          }
        },
        line: 363
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 364,
            column: 23
          },
          end: {
            line: 364,
            column: 24
          }
        },
        loc: {
          start: {
            line: 364,
            column: 36
          },
          end: {
            line: 369,
            column: 5
          }
        },
        line: 364
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 365,
            column: 46
          },
          end: {
            line: 365,
            column: 47
          }
        },
        loc: {
          start: {
            line: 365,
            column: 52
          },
          end: {
            line: 368,
            column: 7
          }
        },
        line: 365
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 372,
            column: 37
          },
          end: {
            line: 372,
            column: 38
          }
        },
        loc: {
          start: {
            line: 372,
            column: 74
          },
          end: {
            line: 380,
            column: 3
          }
        },
        line: 372
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 374,
            column: 15
          },
          end: {
            line: 374,
            column: 16
          }
        },
        loc: {
          start: {
            line: 374,
            column: 21
          },
          end: {
            line: 379,
            column: 5
          }
        },
        line: 374
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 375,
            column: 27
          },
          end: {
            line: 375,
            column: 28
          }
        },
        loc: {
          start: {
            line: 375,
            column: 33
          },
          end: {
            line: 378,
            column: 7
          }
        },
        line: 375
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 392,
            column: 39
          },
          end: {
            line: 392,
            column: 40
          }
        },
        loc: {
          start: {
            line: 392,
            column: 45
          },
          end: {
            line: 444,
            column: 1
          }
        },
        line: 392
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 407,
            column: 39
          },
          end: {
            line: 407,
            column: 40
          }
        },
        loc: {
          start: {
            line: 407,
            column: 51
          },
          end: {
            line: 422,
            column: 3
          }
        },
        line: 407
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 424,
            column: 12
          },
          end: {
            line: 424,
            column: 13
          }
        },
        loc: {
          start: {
            line: 424,
            column: 18
          },
          end: {
            line: 428,
            column: 3
          }
        },
        line: 424
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 427,
            column: 11
          },
          end: {
            line: 427,
            column: 12
          }
        },
        loc: {
          start: {
            line: 427,
            column: 17
          },
          end: {
            line: 427,
            column: 40
          }
        },
        line: 427
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 430,
            column: 38
          },
          end: {
            line: 430,
            column: 39
          }
        },
        loc: {
          start: {
            line: 430,
            column: 50
          },
          end: {
            line: 437,
            column: 3
          }
        },
        line: 430
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 449,
            column: 35
          },
          end: {
            line: 449,
            column: 36
          }
        },
        loc: {
          start: {
            line: 453,
            column: 11
          },
          end: {
            line: 484,
            column: 1
          }
        },
        line: 453
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 466,
            column: 12
          },
          end: {
            line: 466,
            column: 13
          }
        },
        loc: {
          start: {
            line: 466,
            column: 18
          },
          end: {
            line: 478,
            column: 3
          }
        },
        line: 466
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 44,
            column: 61
          },
          end: {
            line: 44,
            column: 85
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 44,
            column: 83
          },
          end: {
            line: 44,
            column: 85
          }
        }],
        line: 44
      },
      "1": {
        loc: {
          start: {
            line: 59,
            column: 4
          },
          end: {
            line: 61,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 59,
            column: 4
          },
          end: {
            line: 61,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 59
      },
      "2": {
        loc: {
          start: {
            line: 60,
            column: 22
          },
          end: {
            line: 60,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 22
          },
          end: {
            line: 60,
            column: 37
          }
        }, {
          start: {
            line: 60,
            column: 41
          },
          end: {
            line: 60,
            column: 42
          }
        }],
        line: 60
      },
      "3": {
        loc: {
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 99,
            column: 8
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 99,
            column: 6
          },
          end: {
            line: 99,
            column: 8
          }
        }],
        line: 90
      },
      "4": {
        loc: {
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 102,
            column: 16
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 102,
            column: 10
          },
          end: {
            line: 102,
            column: 16
          }
        }],
        line: 102
      },
      "5": {
        loc: {
          start: {
            line: 103,
            column: 4
          },
          end: {
            line: 103,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 103,
            column: 19
          },
          end: {
            line: 103,
            column: 21
          }
        }],
        line: 103
      },
      "6": {
        loc: {
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 104,
            column: 18
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 104,
            column: 14
          },
          end: {
            line: 104,
            column: 18
          }
        }],
        line: 104
      },
      "7": {
        loc: {
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 105,
            column: 13
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 105,
            column: 11
          },
          end: {
            line: 105,
            column: 13
          }
        }],
        line: 105
      },
      "8": {
        loc: {
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 106,
            column: 23
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 106,
            column: 15
          },
          end: {
            line: 106,
            column: 23
          }
        }],
        line: 106
      },
      "9": {
        loc: {
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 107,
            column: 31
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 107,
            column: 27
          },
          end: {
            line: 107,
            column: 31
          }
        }],
        line: 107
      },
      "10": {
        loc: {
          start: {
            line: 108,
            column: 4
          },
          end: {
            line: 108,
            column: 23
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 108,
            column: 19
          },
          end: {
            line: 108,
            column: 23
          }
        }],
        line: 108
      },
      "11": {
        loc: {
          start: {
            line: 109,
            column: 4
          },
          end: {
            line: 109,
            column: 18
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 109,
            column: 17
          },
          end: {
            line: 109,
            column: 18
          }
        }],
        line: 109
      },
      "12": {
        loc: {
          start: {
            line: 120,
            column: 20
          },
          end: {
            line: 120,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 120,
            column: 46
          },
          end: {
            line: 120,
            column: 87
          }
        }, {
          start: {
            line: 120,
            column: 90
          },
          end: {
            line: 120,
            column: 92
          }
        }],
        line: 120
      },
      "13": {
        loc: {
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 125,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 125,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "14": {
        loc: {
          start: {
            line: 129,
            column: 39
          },
          end: {
            line: 129,
            column: 52
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 129,
            column: 47
          },
          end: {
            line: 129,
            column: 52
          }
        }],
        line: 129
      },
      "15": {
        loc: {
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 130,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 130,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "16": {
        loc: {
          start: {
            line: 132,
            column: 4
          },
          end: {
            line: 134,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 132,
            column: 4
          },
          end: {
            line: 134,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 132
      },
      "17": {
        loc: {
          start: {
            line: 144,
            column: 6
          },
          end: {
            line: 158,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 144,
            column: 6
          },
          end: {
            line: 158,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 144
      },
      "18": {
        loc: {
          start: {
            line: 146,
            column: 8
          },
          end: {
            line: 157,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 146,
            column: 8
          },
          end: {
            line: 157,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 146
      },
      "19": {
        loc: {
          start: {
            line: 152,
            column: 10
          },
          end: {
            line: 154,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 152,
            column: 10
          },
          end: {
            line: 154,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 152
      },
      "20": {
        loc: {
          start: {
            line: 152,
            column: 14
          },
          end: {
            line: 152,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 152,
            column: 14
          },
          end: {
            line: 152,
            column: 34
          }
        }, {
          start: {
            line: 152,
            column: 38
          },
          end: {
            line: 152,
            column: 45
          }
        }],
        line: 152
      },
      "21": {
        loc: {
          start: {
            line: 163,
            column: 6
          },
          end: {
            line: 165,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 6
          },
          end: {
            line: 165,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "22": {
        loc: {
          start: {
            line: 184,
            column: 6
          },
          end: {
            line: 186,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 6
          },
          end: {
            line: 186,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "23": {
        loc: {
          start: {
            line: 188,
            column: 20
          },
          end: {
            line: 188,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 188,
            column: 43
          },
          end: {
            line: 188,
            column: 46
          }
        }, {
          start: {
            line: 188,
            column: 49
          },
          end: {
            line: 188,
            column: 74
          }
        }],
        line: 188
      },
      "24": {
        loc: {
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 196,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 196,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "25": {
        loc: {
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 191,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 191,
            column: 22
          }
        }, {
          start: {
            line: 191,
            column: 26
          },
          end: {
            line: 191,
            column: 60
          }
        }],
        line: 191
      },
      "26": {
        loc: {
          start: {
            line: 217,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      },
      "27": {
        loc: {
          start: {
            line: 222,
            column: 6
          },
          end: {
            line: 224,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 6
          },
          end: {
            line: 224,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "28": {
        loc: {
          start: {
            line: 263,
            column: 49
          },
          end: {
            line: 263,
            column: 71
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 263,
            column: 66
          },
          end: {
            line: 263,
            column: 71
          }
        }],
        line: 263
      },
      "29": {
        loc: {
          start: {
            line: 268,
            column: 28
          },
          end: {
            line: 268,
            column: 44
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 268,
            column: 43
          },
          end: {
            line: 268,
            column: 44
          }
        }],
        line: 268
      },
      "30": {
        loc: {
          start: {
            line: 285,
            column: 36
          },
          end: {
            line: 285,
            column: 52
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 285,
            column: 47
          },
          end: {
            line: 285,
            column: 52
          }
        }],
        line: 285
      },
      "31": {
        loc: {
          start: {
            line: 325,
            column: 6
          },
          end: {
            line: 341,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 325,
            column: 6
          },
          end: {
            line: 341,
            column: 7
          }
        }, {
          start: {
            line: 333,
            column: 13
          },
          end: {
            line: 341,
            column: 7
          }
        }],
        line: 325
      },
      "32": {
        loc: {
          start: {
            line: 325,
            column: 10
          },
          end: {
            line: 325,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 325,
            column: 10
          },
          end: {
            line: 325,
            column: 35
          }
        }, {
          start: {
            line: 325,
            column: 39
          },
          end: {
            line: 325,
            column: 64
          }
        }],
        line: 325
      },
      "33": {
        loc: {
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 332,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 332,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "34": {
        loc: {
          start: {
            line: 333,
            column: 13
          },
          end: {
            line: 341,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 333,
            column: 13
          },
          end: {
            line: 341,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 333
      },
      "35": {
        loc: {
          start: {
            line: 333,
            column: 17
          },
          end: {
            line: 333,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 333,
            column: 17
          },
          end: {
            line: 333,
            column: 38
          }
        }, {
          start: {
            line: 333,
            column: 42
          },
          end: {
            line: 333,
            column: 71
          }
        }],
        line: 333
      },
      "36": {
        loc: {
          start: {
            line: 336,
            column: 8
          },
          end: {
            line: 340,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 336,
            column: 8
          },
          end: {
            line: 340,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 336
      },
      "37": {
        loc: {
          start: {
            line: 372,
            column: 60
          },
          end: {
            line: 372,
            column: 69
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 372,
            column: 68
          },
          end: {
            line: 372,
            column: 69
          }
        }],
        line: 372
      },
      "38": {
        loc: {
          start: {
            line: 410,
            column: 6
          },
          end: {
            line: 418,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 410,
            column: 6
          },
          end: {
            line: 418,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 410
      },
      "39": {
        loc: {
          start: {
            line: 413,
            column: 35
          },
          end: {
            line: 413,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 413,
            column: 35
          },
          end: {
            line: 413,
            column: 52
          }
        }, {
          start: {
            line: 413,
            column: 56
          },
          end: {
            line: 413,
            column: 58
          }
        }],
        line: 413
      },
      "40": {
        loc: {
          start: {
            line: 449,
            column: 36
          },
          end: {
            line: 453,
            column: 6
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 453,
            column: 4
          },
          end: {
            line: 453,
            column: 6
          }
        }],
        line: 449
      },
      "41": {
        loc: {
          start: {
            line: 455,
            column: 4
          },
          end: {
            line: 455,
            column: 24
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 455,
            column: 20
          },
          end: {
            line: 455,
            column: 24
          }
        }],
        line: 455
      },
      "42": {
        loc: {
          start: {
            line: 456,
            column: 4
          },
          end: {
            line: 456,
            column: 35
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 456,
            column: 31
          },
          end: {
            line: 456,
            column: 35
          }
        }],
        line: 456
      },
      "43": {
        loc: {
          start: {
            line: 457,
            column: 4
          },
          end: {
            line: 457,
            column: 36
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 457,
            column: 32
          },
          end: {
            line: 457,
            column: 36
          }
        }],
        line: 457
      },
      "44": {
        loc: {
          start: {
            line: 467,
            column: 4
          },
          end: {
            line: 469,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 467,
            column: 4
          },
          end: {
            line: 469,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 467
      },
      "45": {
        loc: {
          start: {
            line: 471,
            column: 4
          },
          end: {
            line: 473,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 471,
            column: 4
          },
          end: {
            line: 473,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 471
      },
      "46": {
        loc: {
          start: {
            line: 475,
            column: 4
          },
          end: {
            line: 477,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 475,
            column: 4
          },
          end: {
            line: 477,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 475
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0],
      "7": [0],
      "8": [0],
      "9": [0],
      "10": [0],
      "11": [0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0],
      "29": [0],
      "30": [0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0],
      "41": [0],
      "42": [0],
      "43": [0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4a136446c58586d481c2e03ab9af84f416fdb1d4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1jekbn2f1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1jekbn2f1();
import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { InteractionManager, AppState } from 'react-native';
import { performanceMonitor, ComponentPerformanceTracker, NetworkPerformanceMonitor } from "../utils/performance";
import { advancedCacheManager } from "../services/caching/AdvancedCacheManager";
cov_1jekbn2f1().s[0]++;
export var useRenderPerformance = function useRenderPerformance(componentName) {
  cov_1jekbn2f1().f[0]++;
  var renderStartTime = (cov_1jekbn2f1().s[1]++, useRef(0));
  var tracker = (cov_1jekbn2f1().s[2]++, ComponentPerformanceTracker.getInstance());
  cov_1jekbn2f1().s[3]++;
  useEffect(function () {
    cov_1jekbn2f1().f[1]++;
    cov_1jekbn2f1().s[4]++;
    renderStartTime.current = Date.now();
    cov_1jekbn2f1().s[5]++;
    tracker.trackMount(componentName);
  }, [componentName]);
  cov_1jekbn2f1().s[6]++;
  useEffect(function () {
    cov_1jekbn2f1().f[2]++;
    var renderTime = (cov_1jekbn2f1().s[7]++, Date.now() - renderStartTime.current);
    cov_1jekbn2f1().s[8]++;
    tracker.trackRender(componentName, renderTime);
  });
  var getStats = (cov_1jekbn2f1().s[9]++, useCallback(function () {
    cov_1jekbn2f1().f[3]++;
    cov_1jekbn2f1().s[10]++;
    return tracker.getComponentStats(componentName);
  }, [componentName]));
  cov_1jekbn2f1().s[11]++;
  return {
    getStats: getStats
  };
};
cov_1jekbn2f1().s[12]++;
export var usePerformanceMonitor = function usePerformanceMonitor(operationName) {
  var dependencies = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1jekbn2f1().b[0][0]++, []);
  cov_1jekbn2f1().f[4]++;
  var _ref = (cov_1jekbn2f1().s[13]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    isMonitoring = _ref2[0],
    setIsMonitoring = _ref2[1];
  var _ref3 = (cov_1jekbn2f1().s[14]++, useState(null)),
    _ref4 = _slicedToArray(_ref3, 2),
    lastDuration = _ref4[0],
    setLastDuration = _ref4[1];
  var startMonitoring = (cov_1jekbn2f1().s[15]++, useCallback(function () {
    cov_1jekbn2f1().f[5]++;
    cov_1jekbn2f1().s[16]++;
    setIsMonitoring(true);
    cov_1jekbn2f1().s[17]++;
    performanceMonitor.start(operationName, {
      timestamp: Date.now(),
      dependencies: dependencies.length
    });
  }, [operationName].concat(_toConsumableArray(dependencies))));
  var stopMonitoring = (cov_1jekbn2f1().s[18]++, useCallback(function () {
    cov_1jekbn2f1().f[6]++;
    var metric = (cov_1jekbn2f1().s[19]++, performanceMonitor.end(operationName));
    cov_1jekbn2f1().s[20]++;
    setIsMonitoring(false);
    cov_1jekbn2f1().s[21]++;
    if (metric) {
      cov_1jekbn2f1().b[1][0]++;
      cov_1jekbn2f1().s[22]++;
      setLastDuration((cov_1jekbn2f1().b[2][0]++, metric.duration) || (cov_1jekbn2f1().b[2][1]++, 0));
    } else {
      cov_1jekbn2f1().b[1][1]++;
    }
    cov_1jekbn2f1().s[23]++;
    return metric;
  }, [operationName]));
  var measureAsync = (cov_1jekbn2f1().s[24]++, useCallback(function () {
    var _ref5 = _asyncToGenerator(function* (operation) {
      cov_1jekbn2f1().f[7]++;
      cov_1jekbn2f1().s[25]++;
      startMonitoring();
      cov_1jekbn2f1().s[26]++;
      try {
        var result = (cov_1jekbn2f1().s[27]++, yield operation());
        cov_1jekbn2f1().s[28]++;
        return result;
      } finally {
        cov_1jekbn2f1().s[29]++;
        stopMonitoring();
      }
    });
    return function (_x) {
      return _ref5.apply(this, arguments);
    };
  }(), [startMonitoring, stopMonitoring]));
  cov_1jekbn2f1().s[30]++;
  return {
    isMonitoring: isMonitoring,
    lastDuration: lastDuration,
    startMonitoring: startMonitoring,
    stopMonitoring: stopMonitoring,
    measureAsync: measureAsync
  };
};
cov_1jekbn2f1().s[31]++;
export var useAdvancedCache = function useAdvancedCache(key, fetcher) {
  var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_1jekbn2f1().b[3][0]++, {});
  cov_1jekbn2f1().f[8]++;
  var _ref6 = (cov_1jekbn2f1().s[32]++, options),
    _ref6$ttl = _ref6.ttl,
    ttl = _ref6$ttl === void 0 ? (cov_1jekbn2f1().b[4][0]++, 300000) : _ref6$ttl,
    _ref6$dependencies = _ref6.dependencies,
    dependencies = _ref6$dependencies === void 0 ? (cov_1jekbn2f1().b[5][0]++, []) : _ref6$dependencies,
    _ref6$enabled = _ref6.enabled,
    enabled = _ref6$enabled === void 0 ? (cov_1jekbn2f1().b[6][0]++, true) : _ref6$enabled,
    _ref6$tags = _ref6.tags,
    tags = _ref6$tags === void 0 ? (cov_1jekbn2f1().b[7][0]++, []) : _ref6$tags,
    _ref6$priority = _ref6.priority,
    priority = _ref6$priority === void 0 ? (cov_1jekbn2f1().b[8][0]++, 'medium') : _ref6$priority,
    _ref6$staleWhileReval = _ref6.staleWhileRevalidate,
    staleWhileRevalidate = _ref6$staleWhileReval === void 0 ? (cov_1jekbn2f1().b[9][0]++, true) : _ref6$staleWhileReval,
    _ref6$retryOnError = _ref6.retryOnError,
    retryOnError = _ref6$retryOnError === void 0 ? (cov_1jekbn2f1().b[10][0]++, true) : _ref6$retryOnError,
    _ref6$maxRetries = _ref6.maxRetries,
    maxRetries = _ref6$maxRetries === void 0 ? (cov_1jekbn2f1().b[11][0]++, 3) : _ref6$maxRetries;
  var _ref7 = (cov_1jekbn2f1().s[33]++, useState(null)),
    _ref8 = _slicedToArray(_ref7, 2),
    data = _ref8[0],
    setData = _ref8[1];
  var _ref9 = (cov_1jekbn2f1().s[34]++, useState(false)),
    _ref0 = _slicedToArray(_ref9, 2),
    loading = _ref0[0],
    setLoading = _ref0[1];
  var _ref1 = (cov_1jekbn2f1().s[35]++, useState(null)),
    _ref10 = _slicedToArray(_ref1, 2),
    error = _ref10[0],
    setError = _ref10[1];
  var _ref11 = (cov_1jekbn2f1().s[36]++, useState(null)),
    _ref12 = _slicedToArray(_ref11, 2),
    lastFetched = _ref12[0],
    setLastFetched = _ref12[1];
  var retryCountRef = (cov_1jekbn2f1().s[37]++, useRef(0));
  var abortControllerRef = (cov_1jekbn2f1().s[38]++, useRef(null));
  var cacheKey = (cov_1jekbn2f1().s[39]++, useMemo(function () {
    cov_1jekbn2f1().f[9]++;
    var depHash = (cov_1jekbn2f1().s[40]++, dependencies.length > 0 ? (cov_1jekbn2f1().b[12][0]++, JSON.stringify(dependencies).slice(0, 50)) : (cov_1jekbn2f1().b[12][1]++, ''));
    cov_1jekbn2f1().s[41]++;
    return `${key}_${depHash}`;
  }, [key].concat(_toConsumableArray(dependencies))));
  var isStale = (cov_1jekbn2f1().s[42]++, useMemo(function () {
    cov_1jekbn2f1().f[10]++;
    cov_1jekbn2f1().s[43]++;
    if (!lastFetched) {
      cov_1jekbn2f1().b[13][0]++;
      cov_1jekbn2f1().s[44]++;
      return true;
    } else {
      cov_1jekbn2f1().b[13][1]++;
    }
    cov_1jekbn2f1().s[45]++;
    return Date.now() - lastFetched > ttl;
  }, [lastFetched, ttl]));
  var fetchData = (cov_1jekbn2f1().s[46]++, useCallback(_asyncToGenerator(function* () {
    var force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1jekbn2f1().b[14][0]++, false);
    cov_1jekbn2f1().f[11]++;
    cov_1jekbn2f1().s[47]++;
    if (!enabled) {
      cov_1jekbn2f1().b[15][0]++;
      cov_1jekbn2f1().s[48]++;
      return;
    } else {
      cov_1jekbn2f1().b[15][1]++;
    }
    cov_1jekbn2f1().s[49]++;
    if (abortControllerRef.current) {
      cov_1jekbn2f1().b[16][0]++;
      cov_1jekbn2f1().s[50]++;
      abortControllerRef.current.abort();
    } else {
      cov_1jekbn2f1().b[16][1]++;
    }
    cov_1jekbn2f1().s[51]++;
    abortControllerRef.current = new AbortController();
    var startTime = (cov_1jekbn2f1().s[52]++, Date.now());
    cov_1jekbn2f1().s[53]++;
    try {
      var _abortControllerRef$c;
      cov_1jekbn2f1().s[54]++;
      setLoading(true);
      cov_1jekbn2f1().s[55]++;
      setError(null);
      cov_1jekbn2f1().s[56]++;
      if (!force) {
        cov_1jekbn2f1().b[17][0]++;
        var cachedData = (cov_1jekbn2f1().s[57]++, yield advancedCacheManager.get(cacheKey));
        cov_1jekbn2f1().s[58]++;
        if (cachedData !== null) {
          cov_1jekbn2f1().b[18][0]++;
          cov_1jekbn2f1().s[59]++;
          setData(cachedData);
          cov_1jekbn2f1().s[60]++;
          setLastFetched(Date.now());
          cov_1jekbn2f1().s[61]++;
          setLoading(false);
          cov_1jekbn2f1().s[62]++;
          if ((cov_1jekbn2f1().b[20][0]++, staleWhileRevalidate) && (cov_1jekbn2f1().b[20][1]++, isStale)) {
            cov_1jekbn2f1().b[19][0]++;
            cov_1jekbn2f1().s[63]++;
            setTimeout(function () {
              cov_1jekbn2f1().f[12]++;
              cov_1jekbn2f1().s[64]++;
              return fetchData(true);
            }, 0);
          } else {
            cov_1jekbn2f1().b[19][1]++;
          }
          cov_1jekbn2f1().s[65]++;
          return cachedData;
        } else {
          cov_1jekbn2f1().b[18][1]++;
        }
      } else {
        cov_1jekbn2f1().b[17][1]++;
      }
      var freshData = (cov_1jekbn2f1().s[66]++, yield fetcher());
      cov_1jekbn2f1().s[67]++;
      if ((_abortControllerRef$c = abortControllerRef.current) != null && _abortControllerRef$c.signal.aborted) {
        cov_1jekbn2f1().b[21][0]++;
        cov_1jekbn2f1().s[68]++;
        return;
      } else {
        cov_1jekbn2f1().b[21][1]++;
      }
      cov_1jekbn2f1().s[69]++;
      yield advancedCacheManager.set(cacheKey, freshData, {
        ttl: ttl,
        tags: tags,
        priority: priority
      });
      cov_1jekbn2f1().s[70]++;
      setData(freshData);
      cov_1jekbn2f1().s[71]++;
      setLastFetched(Date.now());
      cov_1jekbn2f1().s[72]++;
      retryCountRef.current = 0;
      var fetchTime = (cov_1jekbn2f1().s[73]++, Date.now() - startTime);
      cov_1jekbn2f1().s[74]++;
      performanceMonitor.trackDatabaseQuery(`cache_fetch_${key}`, fetchTime);
      cov_1jekbn2f1().s[75]++;
      return freshData;
    } catch (err) {
      var _abortControllerRef$c2;
      cov_1jekbn2f1().s[76]++;
      if ((_abortControllerRef$c2 = abortControllerRef.current) != null && _abortControllerRef$c2.signal.aborted) {
        cov_1jekbn2f1().b[22][0]++;
        cov_1jekbn2f1().s[77]++;
        return;
      } else {
        cov_1jekbn2f1().b[22][1]++;
      }
      var _error = (cov_1jekbn2f1().s[78]++, err instanceof Error ? (cov_1jekbn2f1().b[23][0]++, err) : (cov_1jekbn2f1().b[23][1]++, new Error('Fetch failed')));
      cov_1jekbn2f1().s[79]++;
      if ((cov_1jekbn2f1().b[25][0]++, retryOnError) && (cov_1jekbn2f1().b[25][1]++, retryCountRef.current < maxRetries)) {
        cov_1jekbn2f1().b[24][0]++;
        cov_1jekbn2f1().s[80]++;
        retryCountRef.current++;
        cov_1jekbn2f1().s[81]++;
        console.warn(`Retrying fetch for ${key} (attempt ${retryCountRef.current})`);
        cov_1jekbn2f1().s[82]++;
        setTimeout(function () {
          cov_1jekbn2f1().f[13]++;
          cov_1jekbn2f1().s[83]++;
          return fetchData(force);
        }, 1000 * retryCountRef.current);
        cov_1jekbn2f1().s[84]++;
        return;
      } else {
        cov_1jekbn2f1().b[24][1]++;
      }
      cov_1jekbn2f1().s[85]++;
      setError(_error);
      cov_1jekbn2f1().s[86]++;
      console.error(`Cache fetch error for ${key}:`, _error);
      cov_1jekbn2f1().s[87]++;
      throw _error;
    } finally {
      cov_1jekbn2f1().s[88]++;
      setLoading(false);
    }
  }), [cacheKey, fetcher, enabled, ttl, tags, priority, staleWhileRevalidate, retryOnError, maxRetries, isStale]));
  var invalidate = (cov_1jekbn2f1().s[89]++, useCallback(_asyncToGenerator(function* () {
    cov_1jekbn2f1().f[14]++;
    cov_1jekbn2f1().s[90]++;
    yield advancedCacheManager.invalidate(cacheKey);
    cov_1jekbn2f1().s[91]++;
    setData(null);
    cov_1jekbn2f1().s[92]++;
    setLastFetched(null);
  }), [cacheKey]));
  var refresh = (cov_1jekbn2f1().s[93]++, useCallback(function () {
    cov_1jekbn2f1().f[15]++;
    cov_1jekbn2f1().s[94]++;
    return fetchData(true);
  }, [fetchData]));
  cov_1jekbn2f1().s[95]++;
  useEffect(function () {
    cov_1jekbn2f1().f[16]++;
    cov_1jekbn2f1().s[96]++;
    if (enabled) {
      cov_1jekbn2f1().b[26][0]++;
      cov_1jekbn2f1().s[97]++;
      fetchData();
    } else {
      cov_1jekbn2f1().b[26][1]++;
    }
    cov_1jekbn2f1().s[98]++;
    return function () {
      cov_1jekbn2f1().f[17]++;
      cov_1jekbn2f1().s[99]++;
      if (abortControllerRef.current) {
        cov_1jekbn2f1().b[27][0]++;
        cov_1jekbn2f1().s[100]++;
        abortControllerRef.current.abort();
      } else {
        cov_1jekbn2f1().b[27][1]++;
      }
    };
  }, [enabled].concat(_toConsumableArray(dependencies)));
  cov_1jekbn2f1().s[101]++;
  return {
    data: data,
    loading: loading,
    error: error,
    refresh: refresh,
    invalidate: invalidate,
    fetchData: fetchData,
    isStale: isStale,
    lastFetched: lastFetched
  };
};
cov_1jekbn2f1().s[102]++;
export var useNetworkPerformance = function useNetworkPerformance() {
  cov_1jekbn2f1().f[18]++;
  var _ref15 = (cov_1jekbn2f1().s[103]++, useState({
      averageResponseTime: 0,
      totalRequests: 0,
      failedRequests: 0,
      slowRequests: 0
    })),
    _ref16 = _slicedToArray(_ref15, 2),
    stats = _ref16[0],
    setStats = _ref16[1];
  var networkMonitor = (cov_1jekbn2f1().s[104]++, NetworkPerformanceMonitor.getInstance());
  var updateStats = (cov_1jekbn2f1().s[105]++, useCallback(function () {
    cov_1jekbn2f1().f[19]++;
    cov_1jekbn2f1().s[106]++;
    setStats(networkMonitor.getNetworkStats());
  }, []));
  cov_1jekbn2f1().s[107]++;
  useEffect(function () {
    cov_1jekbn2f1().f[20]++;
    cov_1jekbn2f1().s[108]++;
    updateStats();
    var interval = (cov_1jekbn2f1().s[109]++, setInterval(updateStats, 5000));
    cov_1jekbn2f1().s[110]++;
    return function () {
      cov_1jekbn2f1().f[21]++;
      cov_1jekbn2f1().s[111]++;
      return clearInterval(interval);
    };
  }, [updateStats]);
  var trackRequest = (cov_1jekbn2f1().s[112]++, useCallback(function (url) {
    var method = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1jekbn2f1().b[28][0]++, 'GET');
    cov_1jekbn2f1().f[22]++;
    var requestId = (cov_1jekbn2f1().s[113]++, `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
    cov_1jekbn2f1().s[114]++;
    networkMonitor.startRequest(requestId, url, method);
    cov_1jekbn2f1().s[115]++;
    return {
      end: function end(status) {
        var size = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1jekbn2f1().b[29][0]++, 0);
        cov_1jekbn2f1().f[23]++;
        cov_1jekbn2f1().s[116]++;
        networkMonitor.endRequest(requestId, status, size);
        cov_1jekbn2f1().s[117]++;
        updateStats();
      }
    };
  }, [updateStats]));
  cov_1jekbn2f1().s[118]++;
  return {
    stats: stats,
    trackRequest: trackRequest,
    updateStats: updateStats
  };
};
cov_1jekbn2f1().s[119]++;
export var useMemoryMonitoring = function useMemoryMonitoring() {
  var interval = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1jekbn2f1().b[30][0]++, 10000);
  cov_1jekbn2f1().f[24]++;
  var _ref17 = (cov_1jekbn2f1().s[120]++, useState(null)),
    _ref18 = _slicedToArray(_ref17, 2),
    memoryUsage = _ref18[0],
    setMemoryUsage = _ref18[1];
  cov_1jekbn2f1().s[121]++;
  useEffect(function () {
    cov_1jekbn2f1().f[25]++;
    cov_1jekbn2f1().s[122]++;
    var checkMemory = function checkMemory() {
      cov_1jekbn2f1().f[26]++;
      var used = (cov_1jekbn2f1().s[123]++, Math.random() * 100);
      var total = (cov_1jekbn2f1().s[124]++, 512);
      cov_1jekbn2f1().s[125]++;
      setMemoryUsage({
        used: used,
        total: total,
        percentage: used / total * 100
      });
    };
    cov_1jekbn2f1().s[126]++;
    checkMemory();
    var intervalId = (cov_1jekbn2f1().s[127]++, setInterval(checkMemory, interval));
    cov_1jekbn2f1().s[128]++;
    return function () {
      cov_1jekbn2f1().f[27]++;
      cov_1jekbn2f1().s[129]++;
      return clearInterval(intervalId);
    };
  }, [interval]);
  cov_1jekbn2f1().s[130]++;
  return memoryUsage;
};
cov_1jekbn2f1().s[131]++;
export var useAppStatePerformance = function useAppStatePerformance() {
  cov_1jekbn2f1().f[28]++;
  var _ref19 = (cov_1jekbn2f1().s[132]++, useState(AppState.currentState)),
    _ref20 = _slicedToArray(_ref19, 2),
    appState = _ref20[0],
    setAppState = _ref20[1];
  var _ref21 = (cov_1jekbn2f1().s[133]++, useState(null)),
    _ref22 = _slicedToArray(_ref21, 2),
    backgroundTime = _ref22[0],
    setBackgroundTime = _ref22[1];
  var _ref23 = (cov_1jekbn2f1().s[134]++, useState(null)),
    _ref24 = _slicedToArray(_ref23, 2),
    foregroundTime = _ref24[0],
    setForegroundTime = _ref24[1];
  cov_1jekbn2f1().s[135]++;
  useEffect(function () {
    cov_1jekbn2f1().f[29]++;
    cov_1jekbn2f1().s[136]++;
    var handleAppStateChange = function handleAppStateChange(nextAppState) {
      cov_1jekbn2f1().f[30]++;
      var now = (cov_1jekbn2f1().s[137]++, Date.now());
      cov_1jekbn2f1().s[138]++;
      if ((cov_1jekbn2f1().b[32][0]++, appState === 'background') && (cov_1jekbn2f1().b[32][1]++, nextAppState === 'active')) {
        cov_1jekbn2f1().b[31][0]++;
        cov_1jekbn2f1().s[139]++;
        setForegroundTime(now);
        cov_1jekbn2f1().s[140]++;
        if (backgroundTime) {
          cov_1jekbn2f1().b[33][0]++;
          var timeInBackground = (cov_1jekbn2f1().s[141]++, now - backgroundTime);
          cov_1jekbn2f1().s[142]++;
          performanceMonitor.start('app_background_duration');
          cov_1jekbn2f1().s[143]++;
          performanceMonitor.end('app_background_duration');
        } else {
          cov_1jekbn2f1().b[33][1]++;
        }
      } else {
        cov_1jekbn2f1().b[31][1]++;
        cov_1jekbn2f1().s[144]++;
        if ((cov_1jekbn2f1().b[35][0]++, appState === 'active') && (cov_1jekbn2f1().b[35][1]++, nextAppState === 'background')) {
          cov_1jekbn2f1().b[34][0]++;
          cov_1jekbn2f1().s[145]++;
          setBackgroundTime(now);
          cov_1jekbn2f1().s[146]++;
          if (foregroundTime) {
            cov_1jekbn2f1().b[36][0]++;
            var timeInForeground = (cov_1jekbn2f1().s[147]++, now - foregroundTime);
            cov_1jekbn2f1().s[148]++;
            performanceMonitor.start('app_foreground_duration');
            cov_1jekbn2f1().s[149]++;
            performanceMonitor.end('app_foreground_duration');
          } else {
            cov_1jekbn2f1().b[36][1]++;
          }
        } else {
          cov_1jekbn2f1().b[34][1]++;
        }
      }
      cov_1jekbn2f1().s[150]++;
      setAppState(nextAppState);
    };
    var subscription = (cov_1jekbn2f1().s[151]++, AppState.addEventListener('change', handleAppStateChange));
    cov_1jekbn2f1().s[152]++;
    return function () {
      cov_1jekbn2f1().f[31]++;
      cov_1jekbn2f1().s[153]++;
      return subscription == null ? void 0 : subscription.remove();
    };
  }, [appState, backgroundTime, foregroundTime]);
  cov_1jekbn2f1().s[154]++;
  return {
    appState: appState,
    backgroundTime: backgroundTime,
    foregroundTime: foregroundTime
  };
};
cov_1jekbn2f1().s[155]++;
export var useInteractionOptimization = function useInteractionOptimization() {
  cov_1jekbn2f1().f[32]++;
  var _ref25 = (cov_1jekbn2f1().s[156]++, useState(false)),
    _ref26 = _slicedToArray(_ref25, 2),
    isInteracting = _ref26[0],
    setIsInteracting = _ref26[1];
  var runAfterInteractions = (cov_1jekbn2f1().s[157]++, useCallback(function (callback) {
    cov_1jekbn2f1().f[33]++;
    cov_1jekbn2f1().s[158]++;
    return new Promise(function (resolve) {
      cov_1jekbn2f1().f[34]++;
      cov_1jekbn2f1().s[159]++;
      InteractionManager.runAfterInteractions(function () {
        cov_1jekbn2f1().f[35]++;
        var result = (cov_1jekbn2f1().s[160]++, callback());
        cov_1jekbn2f1().s[161]++;
        resolve(result);
      });
    });
  }, []));
  var deferredUpdate = (cov_1jekbn2f1().s[162]++, useCallback(function (callback) {
    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1jekbn2f1().b[37][0]++, 0);
    cov_1jekbn2f1().f[36]++;
    cov_1jekbn2f1().s[163]++;
    setIsInteracting(true);
    cov_1jekbn2f1().s[164]++;
    setTimeout(function () {
      cov_1jekbn2f1().f[37]++;
      cov_1jekbn2f1().s[165]++;
      runAfterInteractions(function () {
        cov_1jekbn2f1().f[38]++;
        cov_1jekbn2f1().s[166]++;
        callback();
        cov_1jekbn2f1().s[167]++;
        setIsInteracting(false);
      });
    }, delay);
  }, [runAfterInteractions]));
  cov_1jekbn2f1().s[168]++;
  return {
    isInteracting: isInteracting,
    runAfterInteractions: runAfterInteractions,
    deferredUpdate: deferredUpdate
  };
};
cov_1jekbn2f1().s[169]++;
export var usePerformanceAnalytics = function usePerformanceAnalytics() {
  cov_1jekbn2f1().f[39]++;
  var _ref27 = (cov_1jekbn2f1().s[170]++, useState({
      renderCount: 0,
      averageRenderTime: 0,
      slowRenders: 0,
      memoryLeaks: 0
    })),
    _ref28 = _slicedToArray(_ref27, 2),
    analytics = _ref28[0],
    setAnalytics = _ref28[1];
  var globalManager = (cov_1jekbn2f1().s[171]++, GlobalPerformanceManager.getInstance());
  var collectAnalytics = (cov_1jekbn2f1().s[172]++, useCallback(_asyncToGenerator(function* () {
    cov_1jekbn2f1().f[40]++;
    cov_1jekbn2f1().s[173]++;
    try {
      var reports = (cov_1jekbn2f1().s[174]++, yield globalManager.getStoredReports());
      cov_1jekbn2f1().s[175]++;
      if (reports.length > 0) {
        cov_1jekbn2f1().b[38][0]++;
        var latest = (cov_1jekbn2f1().s[176]++, reports[0]);
        cov_1jekbn2f1().s[177]++;
        setAnalytics({
          renderCount: Object.keys((cov_1jekbn2f1().b[39][0]++, latest.components) || (cov_1jekbn2f1().b[39][1]++, {})).length,
          averageRenderTime: 0,
          slowRenders: 0,
          memoryLeaks: 0
        });
      } else {
        cov_1jekbn2f1().b[38][1]++;
      }
    } catch (error) {
      cov_1jekbn2f1().s[178]++;
      console.warn('Failed to collect performance analytics:', error);
    }
  }), []));
  cov_1jekbn2f1().s[179]++;
  useEffect(function () {
    cov_1jekbn2f1().f[41]++;
    cov_1jekbn2f1().s[180]++;
    collectAnalytics();
    var interval = (cov_1jekbn2f1().s[181]++, setInterval(collectAnalytics, 30000));
    cov_1jekbn2f1().s[182]++;
    return function () {
      cov_1jekbn2f1().f[42]++;
      cov_1jekbn2f1().s[183]++;
      return clearInterval(interval);
    };
  }, [collectAnalytics]);
  var exportAnalytics = (cov_1jekbn2f1().s[184]++, useCallback(_asyncToGenerator(function* () {
    cov_1jekbn2f1().f[43]++;
    var reports = (cov_1jekbn2f1().s[185]++, yield globalManager.getStoredReports());
    cov_1jekbn2f1().s[186]++;
    return {
      timestamp: new Date().toISOString(),
      analytics: analytics,
      reports: reports
    };
  }), [analytics]));
  cov_1jekbn2f1().s[187]++;
  return {
    analytics: analytics,
    collectAnalytics: collectAnalytics,
    exportAnalytics: exportAnalytics
  };
};
cov_1jekbn2f1().s[188]++;
export var useAutoOptimization = function useAutoOptimization() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1jekbn2f1().b[40][0]++, {});
  cov_1jekbn2f1().f[44]++;
  var _ref31 = (cov_1jekbn2f1().s[189]++, options),
    _ref31$enableCaching = _ref31.enableCaching,
    enableCaching = _ref31$enableCaching === void 0 ? (cov_1jekbn2f1().b[41][0]++, true) : _ref31$enableCaching,
    _ref31$enableRenderOp = _ref31.enableRenderOptimization,
    enableRenderOptimization = _ref31$enableRenderOp === void 0 ? (cov_1jekbn2f1().b[42][0]++, true) : _ref31$enableRenderOp,
    _ref31$enableNetworkO = _ref31.enableNetworkOptimization,
    enableNetworkOptimization = _ref31$enableNetworkO === void 0 ? (cov_1jekbn2f1().b[43][0]++, true) : _ref31$enableNetworkO;
  var _ref32 = (cov_1jekbn2f1().s[190]++, useState({
      cacheHitRate: 0,
      renderOptimizations: 0,
      networkOptimizations: 0
    })),
    _ref33 = _slicedToArray(_ref32, 2),
    optimizations = _ref33[0],
    setOptimizations = _ref33[1];
  cov_1jekbn2f1().s[191]++;
  useEffect(function () {
    cov_1jekbn2f1().f[45]++;
    cov_1jekbn2f1().s[192]++;
    if (enableCaching) {
      cov_1jekbn2f1().b[44][0]++;
    } else {
      cov_1jekbn2f1().b[44][1]++;
    }
    cov_1jekbn2f1().s[193]++;
    if (enableRenderOptimization) {
      cov_1jekbn2f1().b[45][0]++;
    } else {
      cov_1jekbn2f1().b[45][1]++;
    }
    cov_1jekbn2f1().s[194]++;
    if (enableNetworkOptimization) {
      cov_1jekbn2f1().b[46][0]++;
    } else {
      cov_1jekbn2f1().b[46][1]++;
    }
  }, [enableCaching, enableRenderOptimization, enableNetworkOptimization]);
  cov_1jekbn2f1().s[195]++;
  return {
    optimizations: optimizations,
    isOptimized: optimizations.cacheHitRate > 0.8
  };
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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