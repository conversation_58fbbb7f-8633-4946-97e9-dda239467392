{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_AnimatedValue", "_AnimatedValueXY", "_AnimatedInterpolation", "_Easing", "_Animation", "_NativeAnimatedHelper", "_AnimatedColor", "_easeInOut", "easeInOut", "inOut", "ease", "TimingAnimation", "_Animation$default", "config", "_this", "_config$easing", "_config$duration", "_config$delay", "_config$iterations", "_config$isInteraction", "_toValue", "toValue", "_easing", "easing", "_duration", "duration", "_delay", "delay", "__iterations", "iterations", "_useNativeDriver", "shouldUseNativeDriver", "_platformConfig", "platformConfig", "__isInteraction", "isInteraction", "key", "value", "__getNativeAnimationConfig", "frameDuration", "frames", "numFrames", "Math", "round", "frame", "push", "type", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "_this2", "__active", "_fromValue", "_onUpdate", "__onEnd", "__debouncedOnEnd", "finished", "_startTime", "Date", "now", "__startNativeAnimation", "_animationFrame", "requestAnimationFrame", "bind", "_timeout", "setTimeout", "stop", "clearTimeout", "global", "cancelAnimationFrame", "_default", "module"], "sources": ["TimingAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _AnimatedValue = _interopRequireDefault(require(\"../nodes/AnimatedValue\"));\nvar _AnimatedValueXY = _interopRequireDefault(require(\"../nodes/AnimatedValueXY\"));\nvar _AnimatedInterpolation = _interopRequireDefault(require(\"../nodes/AnimatedInterpolation\"));\nvar _Easing = _interopRequireDefault(require(\"../../../../exports/Easing\"));\nvar _Animation = _interopRequireDefault(require(\"./Animation\"));\nvar _NativeAnimatedHelper = require(\"../NativeAnimatedHelper\");\nvar _AnimatedColor = _interopRequireDefault(require(\"../nodes/AnimatedColor\"));\nvar _easeInOut;\nfunction easeInOut() {\n  if (!_easeInOut) {\n    _easeInOut = _Easing.default.inOut(_Easing.default.ease);\n  }\n  return _easeInOut;\n}\nclass TimingAnimation extends _Animation.default {\n  constructor(config) {\n    var _config$easing, _config$duration, _config$delay, _config$iterations, _config$isInteraction;\n    super();\n    this._toValue = config.toValue;\n    this._easing = (_config$easing = config.easing) !== null && _config$easing !== void 0 ? _config$easing : easeInOut();\n    this._duration = (_config$duration = config.duration) !== null && _config$duration !== void 0 ? _config$duration : 500;\n    this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;\n    this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n    this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);\n    this._platformConfig = config.platformConfig;\n    this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !this._useNativeDriver;\n  }\n  __getNativeAnimationConfig() {\n    var frameDuration = 1000.0 / 60.0;\n    var frames = [];\n    var numFrames = Math.round(this._duration / frameDuration);\n    for (var frame = 0; frame < numFrames; frame++) {\n      frames.push(this._easing(frame / numFrames));\n    }\n    frames.push(this._easing(1));\n    return {\n      type: 'frames',\n      frames,\n      toValue: this._toValue,\n      iterations: this.__iterations,\n      platformConfig: this._platformConfig\n    };\n  }\n  start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n    this.__active = true;\n    this._fromValue = fromValue;\n    this._onUpdate = onUpdate;\n    this.__onEnd = onEnd;\n    var start = () => {\n      // Animations that sometimes have 0 duration and sometimes do not\n      // still need to use the native driver when duration is 0 so as to\n      // not cause intermixed JS and native animations.\n      if (this._duration === 0 && !this._useNativeDriver) {\n        this._onUpdate(this._toValue);\n        this.__debouncedOnEnd({\n          finished: true\n        });\n      } else {\n        this._startTime = Date.now();\n        if (this._useNativeDriver) {\n          this.__startNativeAnimation(animatedValue);\n        } else {\n          this._animationFrame = requestAnimationFrame(\n          // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n          this.onUpdate.bind(this));\n        }\n      }\n    };\n    if (this._delay) {\n      this._timeout = setTimeout(start, this._delay);\n    } else {\n      start();\n    }\n  }\n  onUpdate() {\n    var now = Date.now();\n    if (now >= this._startTime + this._duration) {\n      if (this._duration === 0) {\n        this._onUpdate(this._toValue);\n      } else {\n        this._onUpdate(this._fromValue + this._easing(1) * (this._toValue - this._fromValue));\n      }\n      this.__debouncedOnEnd({\n        finished: true\n      });\n      return;\n    }\n    this._onUpdate(this._fromValue + this._easing((now - this._startTime) / this._duration) * (this._toValue - this._fromValue));\n    if (this.__active) {\n      // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n      this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n    }\n  }\n  stop() {\n    super.stop();\n    this.__active = false;\n    clearTimeout(this._timeout);\n    global.cancelAnimationFrame(this._animationFrame);\n    this.__debouncedOnEnd({\n      finished: false\n    });\n  }\n}\nvar _default = exports.default = TimingAnimation;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,cAAc,GAAGH,sBAAsB,CAACxB,OAAO,yBAAyB,CAAC,CAAC;AAC9E,IAAI4B,gBAAgB,GAAGJ,sBAAsB,CAACxB,OAAO,2BAA2B,CAAC,CAAC;AAClF,IAAI6B,sBAAsB,GAAGL,sBAAsB,CAACxB,OAAO,iCAAiC,CAAC,CAAC;AAC9F,IAAI8B,OAAO,GAAGN,sBAAsB,CAACxB,OAAO,6BAA6B,CAAC,CAAC;AAC3E,IAAI+B,UAAU,GAAGP,sBAAsB,CAACxB,OAAO,cAAc,CAAC,CAAC;AAC/D,IAAIgC,qBAAqB,GAAGhC,OAAO,0BAA0B,CAAC;AAC9D,IAAIiC,cAAc,GAAGT,sBAAsB,CAACxB,OAAO,yBAAyB,CAAC,CAAC;AAC9E,IAAIkC,UAAU;AACd,SAASC,SAASA,CAAA,EAAG;EACnB,IAAI,CAACD,UAAU,EAAE;IACfA,UAAU,GAAGJ,OAAO,CAACnB,OAAO,CAACyB,KAAK,CAACN,OAAO,CAACnB,OAAO,CAAC0B,IAAI,CAAC;EAC1D;EACA,OAAOH,UAAU;AACnB;AAAC,IACKI,eAAe,aAAAC,kBAAA;EACnB,SAAAD,gBAAYE,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAxC,gBAAA,CAAAU,OAAA,QAAA2B,eAAA;IAClB,IAAII,cAAc,EAAEC,gBAAgB,EAAEC,aAAa,EAAEC,kBAAkB,EAAEC,qBAAqB;IAC9FL,KAAA,GAAAlC,UAAA,OAAA+B,eAAA;IACAG,KAAA,CAAKM,QAAQ,GAAGP,MAAM,CAACQ,OAAO;IAC9BP,KAAA,CAAKQ,OAAO,GAAG,CAACP,cAAc,GAAGF,MAAM,CAACU,MAAM,MAAM,IAAI,IAAIR,cAAc,KAAK,KAAK,CAAC,GAAGA,cAAc,GAAGP,SAAS,CAAC,CAAC;IACpHM,KAAA,CAAKU,SAAS,GAAG,CAACR,gBAAgB,GAAGH,MAAM,CAACY,QAAQ,MAAM,IAAI,IAAIT,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,GAAG;IACtHF,KAAA,CAAKY,MAAM,GAAG,CAACT,aAAa,GAAGJ,MAAM,CAACc,KAAK,MAAM,IAAI,IAAIV,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC;IACrGH,KAAA,CAAKc,YAAY,GAAG,CAACV,kBAAkB,GAAGL,MAAM,CAACgB,UAAU,MAAM,IAAI,IAAIX,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC;IAC/HJ,KAAA,CAAKgB,gBAAgB,GAAG,CAAC,CAAC,EAAEzB,qBAAqB,CAAC0B,qBAAqB,EAAElB,MAAM,CAAC;IAChFC,KAAA,CAAKkB,eAAe,GAAGnB,MAAM,CAACoB,cAAc;IAC5CnB,KAAA,CAAKoB,eAAe,GAAG,CAACf,qBAAqB,GAAGN,MAAM,CAACsB,aAAa,MAAM,IAAI,IAAIhB,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAACL,KAAA,CAAKgB,gBAAgB;IAAC,OAAAhB,KAAA;EACtK;EAAC,IAAAnC,UAAA,CAAAK,OAAA,EAAA2B,eAAA,EAAAC,kBAAA;EAAA,WAAArC,aAAA,CAAAS,OAAA,EAAA2B,eAAA;IAAAyB,GAAA;IAAAC,KAAA,EACD,SAAAC,0BAA0BA,CAAA,EAAG;MAC3B,IAAIC,aAAa,GAAG,MAAM,GAAG,IAAI;MACjC,IAAIC,MAAM,GAAG,EAAE;MACf,IAAIC,SAAS,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACnB,SAAS,GAAGe,aAAa,CAAC;MAC1D,KAAK,IAAIK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,SAAS,EAAEG,KAAK,EAAE,EAAE;QAC9CJ,MAAM,CAACK,IAAI,CAAC,IAAI,CAACvB,OAAO,CAACsB,KAAK,GAAGH,SAAS,CAAC,CAAC;MAC9C;MACAD,MAAM,CAACK,IAAI,CAAC,IAAI,CAACvB,OAAO,CAAC,CAAC,CAAC,CAAC;MAC5B,OAAO;QACLwB,IAAI,EAAE,QAAQ;QACdN,MAAM,EAANA,MAAM;QACNnB,OAAO,EAAE,IAAI,CAACD,QAAQ;QACtBS,UAAU,EAAE,IAAI,CAACD,YAAY;QAC7BK,cAAc,EAAE,IAAI,CAACD;MACvB,CAAC;IACH;EAAC;IAAAI,GAAA;IAAAC,KAAA,EACD,SAAAU,KAAKA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa,EAAE;MAAA,IAAAC,MAAA;MAClE,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,UAAU,GAAGP,SAAS;MAC3B,IAAI,CAACQ,SAAS,GAAGP,QAAQ;MACzB,IAAI,CAACQ,OAAO,GAAGP,KAAK;MACpB,IAAIH,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;QAIhB,IAAIM,MAAI,CAAC7B,SAAS,KAAK,CAAC,IAAI,CAAC6B,MAAI,CAACvB,gBAAgB,EAAE;UAClDuB,MAAI,CAACG,SAAS,CAACH,MAAI,CAACjC,QAAQ,CAAC;UAC7BiC,MAAI,CAACK,gBAAgB,CAAC;YACpBC,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ,CAAC,MAAM;UACLN,MAAI,CAACO,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;UAC5B,IAAIT,MAAI,CAACvB,gBAAgB,EAAE;YACzBuB,MAAI,CAACU,sBAAsB,CAACX,aAAa,CAAC;UAC5C,CAAC,MAAM;YACLC,MAAI,CAACW,eAAe,GAAGC,qBAAqB,CAE5CZ,MAAI,CAACJ,QAAQ,CAACiB,IAAI,CAACb,MAAI,CAAC,CAAC;UAC3B;QACF;MACF,CAAC;MACD,IAAI,IAAI,CAAC3B,MAAM,EAAE;QACf,IAAI,CAACyC,QAAQ,GAAGC,UAAU,CAACrB,KAAK,EAAE,IAAI,CAACrB,MAAM,CAAC;MAChD,CAAC,MAAM;QACLqB,KAAK,CAAC,CAAC;MACT;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA,EACD,SAAAY,QAAQA,CAAA,EAAG;MACT,IAAIa,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIA,GAAG,IAAI,IAAI,CAACF,UAAU,GAAG,IAAI,CAACpC,SAAS,EAAE;QAC3C,IAAI,IAAI,CAACA,SAAS,KAAK,CAAC,EAAE;UACxB,IAAI,CAACgC,SAAS,CAAC,IAAI,CAACpC,QAAQ,CAAC;QAC/B,CAAC,MAAM;UACL,IAAI,CAACoC,SAAS,CAAC,IAAI,CAACD,UAAU,GAAG,IAAI,CAACjC,OAAO,CAAC,CAAC,CAAC,IAAI,IAAI,CAACF,QAAQ,GAAG,IAAI,CAACmC,UAAU,CAAC,CAAC;QACvF;QACA,IAAI,CAACG,gBAAgB,CAAC;UACpBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MACF;MACA,IAAI,CAACH,SAAS,CAAC,IAAI,CAACD,UAAU,GAAG,IAAI,CAACjC,OAAO,CAAC,CAACwC,GAAG,GAAG,IAAI,CAACF,UAAU,IAAI,IAAI,CAACpC,SAAS,CAAC,IAAI,IAAI,CAACJ,QAAQ,GAAG,IAAI,CAACmC,UAAU,CAAC,CAAC;MAC5H,IAAI,IAAI,CAACD,QAAQ,EAAE;QAEjB,IAAI,CAACU,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAAChB,QAAQ,CAACiB,IAAI,CAAC,IAAI,CAAC,CAAC;MACxE;IACF;EAAC;IAAA9B,GAAA;IAAAC,KAAA,EACD,SAAAgC,IAAIA,CAAA,EAAG;MACL3E,aAAA,CAAAiB,eAAA;MACA,IAAI,CAAC2C,QAAQ,GAAG,KAAK;MACrBgB,YAAY,CAAC,IAAI,CAACH,QAAQ,CAAC;MAC3BI,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACR,eAAe,CAAC;MACjD,IAAI,CAACN,gBAAgB,CAAC;QACpBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EAAC;AAAA,EAvF2BvD,UAAU,CAACpB,OAAO;AAyFhD,IAAIyF,QAAQ,GAAG3E,OAAO,CAACd,OAAO,GAAG2B,eAAe;AAChD+D,MAAM,CAAC5E,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}