65eef12a4b330fc2afcc064cbba0306f
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
function cov_p8qgbk0si() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\optimized\\usePerformanceAnalysis.ts";
  var hash = "c16200bb798b9dd496f68bc905a46c366f346790";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\optimized\\usePerformanceAnalysis.ts",
    statementMap: {
      "0": {
        start: {
          line: 47,
          column: 6
        },
        end: {
          line: 47,
          column: 13
        }
      },
      "1": {
        start: {
          line: 50,
          column: 24
        },
        end: {
          line: 74,
          column: 56
        }
      },
      "2": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 51,
          column: 56
        }
      },
      "3": {
        start: {
          line: 51,
          column: 44
        },
        end: {
          line: 51,
          column: 56
        }
      },
      "4": {
        start: {
          line: 53,
          column: 46
        },
        end: {
          line: 53,
          column: 50
        }
      },
      "5": {
        start: {
          line: 56,
          column: 17
        },
        end: {
          line: 56,
          column: 63
        }
      },
      "6": {
        start: {
          line: 56,
          column: 37
        },
        end: {
          line: 56,
          column: 55
        }
      },
      "7": {
        start: {
          line: 57,
          column: 20
        },
        end: {
          line: 57,
          column: 49
        }
      },
      "8": {
        start: {
          line: 60,
          column: 28
        },
        end: {
          line: 62,
          column: 9
        }
      },
      "9": {
        start: {
          line: 61,
          column: 36
        },
        end: {
          line: 61,
          column: 64
        }
      },
      "10": {
        start: {
          line: 65,
          column: 26
        },
        end: {
          line: 65,
          column: 79
        }
      },
      "11": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 73,
          column: 6
        }
      },
      "12": {
        start: {
          line: 77,
          column: 30
        },
        end: {
          line: 117,
          column: 39
        }
      },
      "13": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 74
        }
      },
      "14": {
        start: {
          line: 78,
          column: 33
        },
        end: {
          line: 78,
          column: 74
        }
      },
      "15": {
        start: {
          line: 80,
          column: 32
        },
        end: {
          line: 80,
          column: 34
        }
      },
      "16": {
        start: {
          line: 81,
          column: 33
        },
        end: {
          line: 81,
          column: 35
        }
      },
      "17": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 88,
          column: 5
        }
      },
      "18": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 85,
          column: 49
        }
      },
      "19": {
        start: {
          line: 86,
          column: 11
        },
        end: {
          line: 88,
          column: 5
        }
      },
      "20": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 87,
          column: 47
        }
      },
      "21": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 95,
          column: 5
        }
      },
      "22": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 56
        }
      },
      "23": {
        start: {
          line: 93,
          column: 11
        },
        end: {
          line: 95,
          column: 5
        }
      },
      "24": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 94,
          column: 64
        }
      },
      "25": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 114,
          column: 5
        }
      },
      "26": {
        start: {
          line: 99,
          column: 21
        },
        end: {
          line: 99,
          column: 39
        }
      },
      "27": {
        start: {
          line: 100,
          column: 23
        },
        end: {
          line: 100,
          column: 41
        }
      },
      "28": {
        start: {
          line: 101,
          column: 21
        },
        end: {
          line: 101,
          column: 103
        }
      },
      "29": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 113,
          column: 9
        }
      },
      "30": {
        start: {
          line: 104,
          column: 24
        },
        end: {
          line: 104,
          column: 44
        }
      },
      "31": {
        start: {
          line: 105,
          column: 21
        },
        end: {
          line: 105,
          column: 43
        }
      },
      "32": {
        start: {
          line: 106,
          column: 28
        },
        end: {
          line: 106,
          column: 42
        }
      },
      "33": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 112,
          column: 9
        }
      },
      "34": {
        start: {
          line: 109,
          column: 10
        },
        end: {
          line: 109,
          column: 47
        }
      },
      "35": {
        start: {
          line: 110,
          column: 15
        },
        end: {
          line: 112,
          column: 9
        }
      },
      "36": {
        start: {
          line: 111,
          column: 10
        },
        end: {
          line: 111,
          column: 48
        }
      },
      "37": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 116,
          column: 37
        }
      },
      "38": {
        start: {
          line: 120,
          column: 26
        },
        end: {
          line: 148,
          column: 65
        }
      },
      "39": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 121,
          column: 84
        }
      },
      "40": {
        start: {
          line: 121,
          column: 74
        },
        end: {
          line: 121,
          column: 84
        }
      },
      "41": {
        start: {
          line: 123,
          column: 27
        },
        end: {
          line: 123,
          column: 29
        }
      },
      "42": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 128,
          column: 5
        }
      },
      "43": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 65
        }
      },
      "44": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 133,
          column: 5
        }
      },
      "45": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 132,
          column: 63
        }
      },
      "46": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 144,
          column: 7
        }
      },
      "47": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 143,
          column: 7
        }
      },
      "48": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 138,
          column: 69
        }
      },
      "49": {
        start: {
          line: 139,
          column: 13
        },
        end: {
          line: 143,
          column: 7
        }
      },
      "50": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 140,
          column: 57
        }
      },
      "51": {
        start: {
          line: 141,
          column: 13
        },
        end: {
          line: 143,
          column: 7
        }
      },
      "52": {
        start: {
          line: 142,
          column: 8
        },
        end: {
          line: 142,
          column: 63
        }
      },
      "53": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 147,
          column: 28
        }
      },
      "54": {
        start: {
          line: 151,
          column: 17
        },
        end: {
          line: 226,
          column: 88
        }
      },
      "55": {
        start: {
          line: 152,
          column: 4
        },
        end: {
          line: 158,
          column: 5
        }
      },
      "56": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 157,
          column: 8
        }
      },
      "57": {
        start: {
          line: 161,
          column: 29
        },
        end: {
          line: 188,
          column: 13
        }
      },
      "58": {
        start: {
          line: 162,
          column: 21
        },
        end: {
          line: 162,
          column: 103
        }
      },
      "59": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 187,
          column: 9
        }
      },
      "60": {
        start: {
          line: 165,
          column: 23
        },
        end: {
          line: 165,
          column: 80
        }
      },
      "61": {
        start: {
          line: 165,
          column: 48
        },
        end: {
          line: 165,
          column: 56
        }
      },
      "62": {
        start: {
          line: 165,
          column: 70
        },
        end: {
          line: 165,
          column: 79
        }
      },
      "63": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 174,
          column: 9
        }
      },
      "64": {
        start: {
          line: 167,
          column: 10
        },
        end: {
          line: 173,
          column: 12
        }
      },
      "65": {
        start: {
          line: 176,
          column: 31
        },
        end: {
          line: 176,
          column: 70
        }
      },
      "66": {
        start: {
          line: 177,
          column: 30
        },
        end: {
          line: 177,
          column: 69
        }
      },
      "67": {
        start: {
          line: 178,
          column: 27
        },
        end: {
          line: 178,
          column: 57
        }
      },
      "68": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 186,
          column: 10
        }
      },
      "69": {
        start: {
          line: 191,
          column: 29
        },
        end: {
          line: 197,
          column: 7
        }
      },
      "70": {
        start: {
          line: 191,
          column: 69
        },
        end: {
          line: 197,
          column: 5
        }
      },
      "71": {
        start: {
          line: 200,
          column: 27
        },
        end: {
          line: 219,
          column: 7
        }
      },
      "72": {
        start: {
          line: 201,
          column: 19
        },
        end: {
          line: 201,
          column: 74
        }
      },
      "73": {
        start: {
          line: 202,
          column: 23
        },
        end: {
          line: 202,
          column: 55
        }
      },
      "74": {
        start: {
          line: 202,
          column: 39
        },
        end: {
          line: 202,
          column: 54
        }
      },
      "75": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 213,
          column: 7
        }
      },
      "76": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 205,
          column: 37
        }
      },
      "77": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 206,
          column: 58
        }
      },
      "78": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 212,
          column: 11
        }
      },
      "79": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 19
        }
      },
      "80": {
        start: {
          line: 216,
          column: 24
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "81": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 225,
          column: 6
        }
      },
      "82": {
        start: {
          line: 229,
          column: 22
        },
        end: {
          line: 258,
          column: 50
        }
      },
      "83": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 235,
          column: 5
        }
      },
      "84": {
        start: {
          line: 231,
          column: 6
        },
        end: {
          line: 234,
          column: 8
        }
      },
      "85": {
        start: {
          line: 237,
          column: 29
        },
        end: {
          line: 245,
          column: 9
        }
      },
      "86": {
        start: {
          line: 238,
          column: 19
        },
        end: {
          line: 238,
          column: 42
        }
      },
      "87": {
        start: {
          line: 239,
          column: 17
        },
        end: {
          line: 245,
          column: 7
        }
      },
      "88": {
        start: {
          line: 247,
          column: 27
        },
        end: {
          line: 252,
          column: 5
        }
      },
      "89": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 257,
          column: 6
        }
      },
      "90": {
        start: {
          line: 261,
          column: 2
        },
        end: {
          line: 272,
          column: 81
        }
      },
      "91": {
        start: {
          line: 262,
          column: 4
        },
        end: {
          line: 262,
          column: 36
        }
      },
      "92": {
        start: {
          line: 262,
          column: 24
        },
        end: {
          line: 262,
          column: 36
        }
      },
      "93": {
        start: {
          line: 264,
          column: 4
        },
        end: {
          line: 271,
          column: 6
        }
      },
      "94": {
        start: {
          line: 279,
          column: 2
        },
        end: {
          line: 331,
          column: 9
        }
      },
      "95": {
        start: {
          line: 280,
          column: 22
        },
        end: {
          line: 280,
          column: 32
        }
      },
      "96": {
        start: {
          line: 282,
          column: 4
        },
        end: {
          line: 330,
          column: 5
        }
      },
      "97": {
        start: {
          line: 284,
          column: 28
        },
        end: {
          line: 284,
          column: 91
        }
      },
      "98": {
        start: {
          line: 287,
          column: 29
        },
        end: {
          line: 287,
          column: 33
        }
      },
      "99": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 294,
          column: 7
        }
      },
      "100": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 293,
          column: 10
        }
      },
      "101": {
        start: {
          line: 297,
          column: 31
        },
        end: {
          line: 302,
          column: 9
        }
      },
      "102": {
        start: {
          line: 297,
          column: 68
        },
        end: {
          line: 302,
          column: 7
        }
      },
      "103": {
        start: {
          line: 304,
          column: 30
        },
        end: {
          line: 307,
          column: 7
        }
      },
      "104": {
        start: {
          line: 309,
          column: 27
        },
        end: {
          line: 309,
          column: 49
        }
      },
      "105": {
        start: {
          line: 311,
          column: 6
        },
        end: {
          line: 313,
          column: 7
        }
      },
      "106": {
        start: {
          line: 312,
          column: 8
        },
        end: {
          line: 312,
          column: 63
        }
      },
      "107": {
        start: {
          line: 315,
          column: 6
        },
        end: {
          line: 326,
          column: 8
        }
      },
      "108": {
        start: {
          line: 328,
          column: 6
        },
        end: {
          line: 328,
          column: 52
        }
      },
      "109": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 329,
          column: 18
        }
      }
    },
    fnMap: {
      "0": {
        name: "usePerformanceAnalysis",
        decl: {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 38
          }
        },
        loc: {
          start: {
            line: 41,
            column: 25
          },
          end: {
            line: 273,
            column: 1
          }
        },
        line: 41
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 50,
            column: 32
          },
          end: {
            line: 50,
            column: 33
          }
        },
        loc: {
          start: {
            line: 50,
            column: 38
          },
          end: {
            line: 74,
            column: 3
          }
        },
        line: 50
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 56,
            column: 32
          },
          end: {
            line: 56,
            column: 33
          }
        },
        loc: {
          start: {
            line: 56,
            column: 37
          },
          end: {
            line: 56,
            column: 55
          }
        },
        line: 56
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 61,
            column: 24
          },
          end: {
            line: 61,
            column: 25
          }
        },
        loc: {
          start: {
            line: 61,
            column: 36
          },
          end: {
            line: 61,
            column: 64
          }
        },
        line: 61
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 77,
            column: 38
          },
          end: {
            line: 77,
            column: 39
          }
        },
        loc: {
          start: {
            line: 77,
            column: 44
          },
          end: {
            line: 117,
            column: 3
          }
        },
        line: 77
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 103,
            column: 21
          },
          end: {
            line: 103,
            column: 22
          }
        },
        loc: {
          start: {
            line: 103,
            column: 30
          },
          end: {
            line: 113,
            column: 7
          }
        },
        line: 103
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 120,
            column: 34
          },
          end: {
            line: 120,
            column: 35
          }
        },
        loc: {
          start: {
            line: 120,
            column: 40
          },
          end: {
            line: 148,
            column: 3
          }
        },
        line: 120
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 136,
            column: 43
          },
          end: {
            line: 136,
            column: 44
          }
        },
        loc: {
          start: {
            line: 136,
            column: 55
          },
          end: {
            line: 144,
            column: 5
          }
        },
        line: 136
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 151,
            column: 25
          },
          end: {
            line: 151,
            column: 26
          }
        },
        loc: {
          start: {
            line: 151,
            column: 31
          },
          end: {
            line: 226,
            column: 3
          }
        },
        line: 151
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 161,
            column: 59
          },
          end: {
            line: 161,
            column: 60
          }
        },
        loc: {
          start: {
            line: 161,
            column: 65
          },
          end: {
            line: 188,
            column: 5
          }
        },
        line: 161
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 164,
            column: 24
          },
          end: {
            line: 164,
            column: 25
          }
        },
        loc: {
          start: {
            line: 164,
            column: 33
          },
          end: {
            line: 187,
            column: 7
          }
        },
        line: 164
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 165,
            column: 43
          },
          end: {
            line: 165,
            column: 44
          }
        },
        loc: {
          start: {
            line: 165,
            column: 48
          },
          end: {
            line: 165,
            column: 56
          }
        },
        line: 165
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 165,
            column: 65
          },
          end: {
            line: 165,
            column: 66
          }
        },
        loc: {
          start: {
            line: 165,
            column: 70
          },
          end: {
            line: 165,
            column: 79
          }
        },
        line: 165
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 191,
            column: 59
          },
          end: {
            line: 191,
            column: 60
          }
        },
        loc: {
          start: {
            line: 191,
            column: 69
          },
          end: {
            line: 197,
            column: 5
          }
        },
        line: 191
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 200,
            column: 48
          },
          end: {
            line: 200,
            column: 49
          }
        },
        loc: {
          start: {
            line: 200,
            column: 75
          },
          end: {
            line: 216,
            column: 5
          }
        },
        line: 200
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 202,
            column: 34
          },
          end: {
            line: 202,
            column: 35
          }
        },
        loc: {
          start: {
            line: 202,
            column: 39
          },
          end: {
            line: 202,
            column: 54
          }
        },
        line: 202
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 216,
            column: 15
          },
          end: {
            line: 216,
            column: 16
          }
        },
        loc: {
          start: {
            line: 216,
            column: 24
          },
          end: {
            line: 219,
            column: 5
          }
        },
        line: 216
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 229,
            column: 30
          },
          end: {
            line: 229,
            column: 31
          }
        },
        loc: {
          start: {
            line: 229,
            column: 36
          },
          end: {
            line: 258,
            column: 3
          }
        },
        line: 229
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 238,
            column: 14
          },
          end: {
            line: 238,
            column: 15
          }
        },
        loc: {
          start: {
            line: 238,
            column: 19
          },
          end: {
            line: 238,
            column: 42
          }
        },
        line: 238
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 239,
            column: 11
          },
          end: {
            line: 239,
            column: 12
          }
        },
        loc: {
          start: {
            line: 239,
            column: 17
          },
          end: {
            line: 245,
            column: 7
          }
        },
        line: 239
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 261,
            column: 17
          },
          end: {
            line: 261,
            column: 18
          }
        },
        loc: {
          start: {
            line: 261,
            column: 23
          },
          end: {
            line: 272,
            column: 3
          }
        },
        line: 261
      },
      "21": {
        name: "useMatchAnalysis",
        decl: {
          start: {
            line: 278,
            column: 16
          },
          end: {
            line: 278,
            column: 32
          }
        },
        loc: {
          start: {
            line: 278,
            column: 35
          },
          end: {
            line: 332,
            column: 1
          }
        },
        line: 278
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 279,
            column: 21
          },
          end: {
            line: 279,
            column: 22
          }
        },
        loc: {
          start: {
            line: 279,
            column: 80
          },
          end: {
            line: 331,
            column: 3
          }
        },
        line: 279
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 297,
            column: 57
          },
          end: {
            line: 297,
            column: 58
          }
        },
        loc: {
          start: {
            line: 297,
            column: 68
          },
          end: {
            line: 302,
            column: 7
          }
        },
        line: 297
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 40,
            column: 2
          },
          end: {
            line: 40,
            column: 45
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 40,
            column: 43
          },
          end: {
            line: 40,
            column: 45
          }
        }],
        line: 40
      },
      "1": {
        loc: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 43,
            column: 23
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 43,
            column: 19
          },
          end: {
            line: 43,
            column: 23
          }
        }],
        line: 43
      },
      "2": {
        loc: {
          start: {
            line: 44,
            column: 4
          },
          end: {
            line: 44,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 44,
            column: 24
          },
          end: {
            line: 44,
            column: 28
          }
        }],
        line: 44
      },
      "3": {
        loc: {
          start: {
            line: 45,
            column: 4
          },
          end: {
            line: 45,
            column: 32
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 45,
            column: 28
          },
          end: {
            line: 45,
            column: 32
          }
        }],
        line: 45
      },
      "4": {
        loc: {
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 46,
            column: 30
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 30
          }
        }],
        line: 46
      },
      "5": {
        loc: {
          start: {
            line: 51,
            column: 4
          },
          end: {
            line: 51,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 4
          },
          end: {
            line: 51,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "6": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 13
          }
        }, {
          start: {
            line: 51,
            column: 17
          },
          end: {
            line: 51,
            column: 42
          }
        }],
        line: 51
      },
      "7": {
        loc: {
          start: {
            line: 60,
            column: 28
          },
          end: {
            line: 62,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 61,
            column: 8
          },
          end: {
            line: 61,
            column: 86
          }
        }, {
          start: {
            line: 62,
            column: 8
          },
          end: {
            line: 62,
            column: 9
          }
        }],
        line: 60
      },
      "8": {
        loc: {
          start: {
            line: 61,
            column: 43
          },
          end: {
            line: 61,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 43
          },
          end: {
            line: 61,
            column: 58
          }
        }, {
          start: {
            line: 61,
            column: 62
          },
          end: {
            line: 61,
            column: 63
          }
        }],
        line: 61
      },
      "9": {
        loc: {
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 78,
            column: 74
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 4
          },
          end: {
            line: 78,
            column: 74
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 78
      },
      "10": {
        loc: {
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 78,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 78,
            column: 8
          },
          end: {
            line: 78,
            column: 13
          }
        }, {
          start: {
            line: 78,
            column: 17
          },
          end: {
            line: 78,
            column: 31
          }
        }],
        line: 78
      },
      "11": {
        loc: {
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 84,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        }, {
          start: {
            line: 86,
            column: 11
          },
          end: {
            line: 88,
            column: 5
          }
        }],
        line: 84
      },
      "12": {
        loc: {
          start: {
            line: 86,
            column: 11
          },
          end: {
            line: 88,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 11
          },
          end: {
            line: 88,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "13": {
        loc: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 95,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 95,
            column: 5
          }
        }, {
          start: {
            line: 93,
            column: 11
          },
          end: {
            line: 95,
            column: 5
          }
        }],
        line: 91
      },
      "14": {
        loc: {
          start: {
            line: 93,
            column: 11
          },
          end: {
            line: 95,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 11
          },
          end: {
            line: 95,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "15": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 114,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 114,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "16": {
        loc: {
          start: {
            line: 104,
            column: 24
          },
          end: {
            line: 104,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 104,
            column: 24
          },
          end: {
            line: 104,
            column: 39
          }
        }, {
          start: {
            line: 104,
            column: 43
          },
          end: {
            line: 104,
            column: 44
          }
        }],
        line: 104
      },
      "17": {
        loc: {
          start: {
            line: 105,
            column: 21
          },
          end: {
            line: 105,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 21
          },
          end: {
            line: 105,
            column: 38
          }
        }, {
          start: {
            line: 105,
            column: 42
          },
          end: {
            line: 105,
            column: 43
          }
        }],
        line: 105
      },
      "18": {
        loc: {
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 112,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 108,
            column: 8
          },
          end: {
            line: 112,
            column: 9
          }
        }, {
          start: {
            line: 110,
            column: 15
          },
          end: {
            line: 112,
            column: 9
          }
        }],
        line: 108
      },
      "19": {
        loc: {
          start: {
            line: 110,
            column: 15
          },
          end: {
            line: 112,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 15
          },
          end: {
            line: 112,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "20": {
        loc: {
          start: {
            line: 121,
            column: 4
          },
          end: {
            line: 121,
            column: 84
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 121,
            column: 4
          },
          end: {
            line: 121,
            column: 84
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 121
      },
      "21": {
        loc: {
          start: {
            line: 121,
            column: 8
          },
          end: {
            line: 121,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 121,
            column: 8
          },
          end: {
            line: 121,
            column: 30
          }
        }, {
          start: {
            line: 121,
            column: 34
          },
          end: {
            line: 121,
            column: 48
          }
        }, {
          start: {
            line: 121,
            column: 52
          },
          end: {
            line: 121,
            column: 72
          }
        }],
        line: 121
      },
      "22": {
        loc: {
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 128,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 126
      },
      "23": {
        loc: {
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 133,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 133,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "24": {
        loc: {
          start: {
            line: 137,
            column: 6
          },
          end: {
            line: 143,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 6
          },
          end: {
            line: 143,
            column: 7
          }
        }, {
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 143,
            column: 7
          }
        }],
        line: 137
      },
      "25": {
        loc: {
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 143,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 13
          },
          end: {
            line: 143,
            column: 7
          }
        }, {
          start: {
            line: 141,
            column: 13
          },
          end: {
            line: 143,
            column: 7
          }
        }],
        line: 139
      },
      "26": {
        loc: {
          start: {
            line: 141,
            column: 13
          },
          end: {
            line: 143,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 141,
            column: 13
          },
          end: {
            line: 143,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 141
      },
      "27": {
        loc: {
          start: {
            line: 152,
            column: 4
          },
          end: {
            line: 158,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 152,
            column: 4
          },
          end: {
            line: 158,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 152
      },
      "28": {
        loc: {
          start: {
            line: 152,
            column: 8
          },
          end: {
            line: 152,
            column: 30
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 152,
            column: 8
          },
          end: {
            line: 152,
            column: 21
          }
        }, {
          start: {
            line: 152,
            column: 25
          },
          end: {
            line: 152,
            column: 30
          }
        }],
        line: 152
      },
      "29": {
        loc: {
          start: {
            line: 161,
            column: 29
          },
          end: {
            line: 188,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 161,
            column: 58
          },
          end: {
            line: 188,
            column: 8
          }
        }, {
          start: {
            line: 188,
            column: 11
          },
          end: {
            line: 188,
            column: 13
          }
        }],
        line: 161
      },
      "30": {
        loc: {
          start: {
            line: 166,
            column: 8
          },
          end: {
            line: 174,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 8
          },
          end: {
            line: 174,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "31": {
        loc: {
          start: {
            line: 169,
            column: 28
          },
          end: {
            line: 169,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 28
          },
          end: {
            line: 169,
            column: 37
          }
        }, {
          start: {
            line: 169,
            column: 41
          },
          end: {
            line: 169,
            column: 43
          }
        }],
        line: 169
      },
      "32": {
        loc: {
          start: {
            line: 170,
            column: 27
          },
          end: {
            line: 170,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 170,
            column: 27
          },
          end: {
            line: 170,
            column: 36
          }
        }, {
          start: {
            line: 170,
            column: 40
          },
          end: {
            line: 170,
            column: 42
          }
        }],
        line: 170
      },
      "33": {
        loc: {
          start: {
            line: 176,
            column: 31
          },
          end: {
            line: 176,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 176,
            column: 31
          },
          end: {
            line: 176,
            column: 64
          }
        }, {
          start: {
            line: 176,
            column: 68
          },
          end: {
            line: 176,
            column: 70
          }
        }],
        line: 176
      },
      "34": {
        loc: {
          start: {
            line: 177,
            column: 30
          },
          end: {
            line: 177,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 177,
            column: 30
          },
          end: {
            line: 177,
            column: 63
          }
        }, {
          start: {
            line: 177,
            column: 67
          },
          end: {
            line: 177,
            column: 69
          }
        }],
        line: 177
      },
      "35": {
        loc: {
          start: {
            line: 184,
            column: 17
          },
          end: {
            line: 184,
            column: 121
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 35
          },
          end: {
            line: 184,
            column: 55
          }
        }, {
          start: {
            line: 184,
            column: 59
          },
          end: {
            line: 184,
            column: 121
          }
        }],
        line: 184
      },
      "36": {
        loc: {
          start: {
            line: 184,
            column: 59
          },
          end: {
            line: 184,
            column: 121
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 184,
            column: 78
          },
          end: {
            line: 184,
            column: 98
          }
        }, {
          start: {
            line: 184,
            column: 103
          },
          end: {
            line: 184,
            column: 120
          }
        }],
        line: 184
      },
      "37": {
        loc: {
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 213,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 213,
            column: 7
          }
        }, {
          start: {
            line: 207,
            column: 13
          },
          end: {
            line: 213,
            column: 7
          }
        }],
        line: 204
      },
      "38": {
        loc: {
          start: {
            line: 206,
            column: 31
          },
          end: {
            line: 206,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 206,
            column: 31
          },
          end: {
            line: 206,
            column: 52
          }
        }, {
          start: {
            line: 206,
            column: 56
          },
          end: {
            line: 206,
            column: 57
          }
        }],
        line: 206
      },
      "39": {
        loc: {
          start: {
            line: 211,
            column: 22
          },
          end: {
            line: 211,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 211,
            column: 22
          },
          end: {
            line: 211,
            column: 43
          }
        }, {
          start: {
            line: 211,
            column: 47
          },
          end: {
            line: 211,
            column: 48
          }
        }],
        line: 211
      },
      "40": {
        loc: {
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 235,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 235,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "41": {
        loc: {
          start: {
            line: 230,
            column: 8
          },
          end: {
            line: 230,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 230,
            column: 8
          },
          end: {
            line: 230,
            column: 26
          }
        }, {
          start: {
            line: 230,
            column: 30
          },
          end: {
            line: 230,
            column: 61
          }
        }],
        line: 230
      },
      "42": {
        loc: {
          start: {
            line: 244,
            column: 20
          },
          end: {
            line: 244,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 244,
            column: 39
          },
          end: {
            line: 244,
            column: 45
          }
        }, {
          start: {
            line: 244,
            column: 48
          },
          end: {
            line: 244,
            column: 83
          }
        }],
        line: 244
      },
      "43": {
        loc: {
          start: {
            line: 244,
            column: 48
          },
          end: {
            line: 244,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 244,
            column: 67
          },
          end: {
            line: 244,
            column: 75
          }
        }, {
          start: {
            line: 244,
            column: 78
          },
          end: {
            line: 244,
            column: 83
          }
        }],
        line: 244
      },
      "44": {
        loc: {
          start: {
            line: 262,
            column: 4
          },
          end: {
            line: 262,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 262,
            column: 4
          },
          end: {
            line: 262,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 262
      },
      "45": {
        loc: {
          start: {
            line: 288,
            column: 6
          },
          end: {
            line: 294,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 288,
            column: 6
          },
          end: {
            line: 294,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 288
      },
      "46": {
        loc: {
          start: {
            line: 291,
            column: 10
          },
          end: {
            line: 291,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 10
          },
          end: {
            line: 291,
            column: 28
          }
        }, {
          start: {
            line: 291,
            column: 32
          },
          end: {
            line: 291,
            column: 42
          }
        }],
        line: 291
      },
      "47": {
        loc: {
          start: {
            line: 292,
            column: 10
          },
          end: {
            line: 292,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 10
          },
          end: {
            line: 292,
            column: 30
          }
        }, {
          start: {
            line: 292,
            column: 34
          },
          end: {
            line: 292,
            column: 40
          }
        }],
        line: 292
      },
      "48": {
        loc: {
          start: {
            line: 311,
            column: 6
          },
          end: {
            line: 313,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 311,
            column: 6
          },
          end: {
            line: 313,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 311
      },
      "49": {
        loc: {
          start: {
            line: 323,
            column: 26
          },
          end: {
            line: 323,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 323,
            column: 26
          },
          end: {
            line: 323,
            column: 61
          }
        }, {
          start: {
            line: 323,
            column: 65
          },
          end: {
            line: 323,
            column: 67
          }
        }],
        line: 323
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c16200bb798b9dd496f68bc905a46c366f346790"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_p8qgbk0si = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_p8qgbk0si();
import { useMemo, useCallback } from 'react';
import { performanceAnalyticsService } from "../../services/performanceAnalytics";
export function usePerformanceAnalysis(data) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_p8qgbk0si().b[0][0]++, {});
  cov_p8qgbk0si().f[0]++;
  var _ref = (cov_p8qgbk0si().s[0]++, options),
    _ref$enableTrends = _ref.enableTrends,
    enableTrends = _ref$enableTrends === void 0 ? (cov_p8qgbk0si().b[1][0]++, true) : _ref$enableTrends,
    _ref$enableProjection = _ref.enableProjections,
    enableProjections = _ref$enableProjection === void 0 ? (cov_p8qgbk0si().b[2][0]++, true) : _ref$enableProjection,
    _ref$enableRecommenda = _ref.enableRecommendations,
    enableRecommendations = _ref$enableRecommenda === void 0 ? (cov_p8qgbk0si().b[3][0]++, true) : _ref$enableRecommenda,
    _ref$analysisDepth = _ref.analysisDepth,
    analysisDepth = _ref$analysisDepth === void 0 ? (cov_p8qgbk0si().b[4][0]++, 'detailed') : _ref$analysisDepth;
  var basicAnalysis = (cov_p8qgbk0si().s[1]++, useMemo(function () {
    cov_p8qgbk0si().f[1]++;
    cov_p8qgbk0si().s[2]++;
    if ((cov_p8qgbk0si().b[6][0]++, !data) || (cov_p8qgbk0si().b[6][1]++, data.matches.length === 0)) {
      cov_p8qgbk0si().b[5][0]++;
      cov_p8qgbk0si().s[3]++;
      return null;
    } else {
      cov_p8qgbk0si().b[5][1]++;
    }
    var _ref2 = (cov_p8qgbk0si().s[4]++, data),
      matches = _ref2.matches,
      sessions = _ref2.sessions,
      skillStats = _ref2.skillStats;
    var wins = (cov_p8qgbk0si().s[5]++, matches.filter(function (m) {
      cov_p8qgbk0si().f[2]++;
      cov_p8qgbk0si().s[6]++;
      return m.result === 'win';
    }).length);
    var winRate = (cov_p8qgbk0si().s[7]++, wins / matches.length * 100);
    var avgSessionScore = (cov_p8qgbk0si().s[8]++, sessions.length > 0 ? (cov_p8qgbk0si().b[7][0]++, sessions.reduce(function (sum, s) {
      cov_p8qgbk0si().f[3]++;
      cov_p8qgbk0si().s[9]++;
      return sum + ((cov_p8qgbk0si().b[8][0]++, s.overall_score) || (cov_p8qgbk0si().b[8][1]++, 0));
    }, 0) / sessions.length) : (cov_p8qgbk0si().b[7][1]++, 0));
    var overallRating = (cov_p8qgbk0si().s[10]++, Math.round(winRate * 0.4 + avgSessionScore * 0.6));
    cov_p8qgbk0si().s[11]++;
    return {
      overallRating: overallRating,
      winRate: winRate,
      avgSessionScore: avgSessionScore,
      totalMatches: matches.length,
      totalSessions: sessions.length
    };
  }, [data == null ? void 0 : data.matches, data == null ? void 0 : data.sessions, data == null ? void 0 : data.lastFetched]));
  var strengthsWeaknesses = (cov_p8qgbk0si().s[12]++, useMemo(function () {
    cov_p8qgbk0si().f[4]++;
    cov_p8qgbk0si().s[13]++;
    if ((cov_p8qgbk0si().b[10][0]++, !data) || (cov_p8qgbk0si().b[10][1]++, !basicAnalysis)) {
      cov_p8qgbk0si().b[9][0]++;
      cov_p8qgbk0si().s[14]++;
      return {
        strengths: [],
        weaknesses: []
      };
    } else {
      cov_p8qgbk0si().b[9][1]++;
    }
    var strengths = (cov_p8qgbk0si().s[15]++, []);
    var weaknesses = (cov_p8qgbk0si().s[16]++, []);
    cov_p8qgbk0si().s[17]++;
    if (basicAnalysis.winRate > 60) {
      cov_p8qgbk0si().b[11][0]++;
      cov_p8qgbk0si().s[18]++;
      strengths.push('Strong match performance');
    } else {
      cov_p8qgbk0si().b[11][1]++;
      cov_p8qgbk0si().s[19]++;
      if (basicAnalysis.winRate < 40) {
        cov_p8qgbk0si().b[12][0]++;
        cov_p8qgbk0si().s[20]++;
        weaknesses.push('Struggling in matches');
      } else {
        cov_p8qgbk0si().b[12][1]++;
      }
    }
    cov_p8qgbk0si().s[21]++;
    if (basicAnalysis.avgSessionScore > 75) {
      cov_p8qgbk0si().b[13][0]++;
      cov_p8qgbk0si().s[22]++;
      strengths.push('Consistent training performance');
    } else {
      cov_p8qgbk0si().b[13][1]++;
      cov_p8qgbk0si().s[23]++;
      if (basicAnalysis.avgSessionScore < 60) {
        cov_p8qgbk0si().b[14][0]++;
        cov_p8qgbk0si().s[24]++;
        weaknesses.push('Training consistency needs improvement');
      } else {
        cov_p8qgbk0si().b[14][1]++;
      }
    }
    cov_p8qgbk0si().s[25]++;
    if (data.skillStats.length >= 2) {
      cov_p8qgbk0si().b[15][0]++;
      var latest = (cov_p8qgbk0si().s[26]++, data.skillStats[0]);
      var previous = (cov_p8qgbk0si().s[27]++, data.skillStats[1]);
      var skills = (cov_p8qgbk0si().s[28]++, ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game']);
      cov_p8qgbk0si().s[29]++;
      skills.forEach(function (skill) {
        cov_p8qgbk0si().f[5]++;
        var current = (cov_p8qgbk0si().s[30]++, (cov_p8qgbk0si().b[16][0]++, latest == null ? void 0 : latest[skill]) || (cov_p8qgbk0si().b[16][1]++, 0));
        var prev = (cov_p8qgbk0si().s[31]++, (cov_p8qgbk0si().b[17][0]++, previous == null ? void 0 : previous[skill]) || (cov_p8qgbk0si().b[17][1]++, 0));
        var improvement = (cov_p8qgbk0si().s[32]++, current - prev);
        cov_p8qgbk0si().s[33]++;
        if (improvement > 5) {
          cov_p8qgbk0si().b[18][0]++;
          cov_p8qgbk0si().s[34]++;
          strengths.push(`Improving ${skill}`);
        } else {
          cov_p8qgbk0si().b[18][1]++;
          cov_p8qgbk0si().s[35]++;
          if (improvement < -5) {
            cov_p8qgbk0si().b[19][0]++;
            cov_p8qgbk0si().s[36]++;
            weaknesses.push(`Declining ${skill}`);
          } else {
            cov_p8qgbk0si().b[19][1]++;
          }
        }
      });
    } else {
      cov_p8qgbk0si().b[15][1]++;
    }
    cov_p8qgbk0si().s[37]++;
    return {
      strengths: strengths,
      weaknesses: weaknesses
    };
  }, [data == null ? void 0 : data.skillStats, basicAnalysis]));
  var recommendations = (cov_p8qgbk0si().s[38]++, useMemo(function () {
    cov_p8qgbk0si().f[6]++;
    cov_p8qgbk0si().s[39]++;
    if ((cov_p8qgbk0si().b[21][0]++, !enableRecommendations) || (cov_p8qgbk0si().b[21][1]++, !basicAnalysis) || (cov_p8qgbk0si().b[21][2]++, !strengthsWeaknesses)) {
      cov_p8qgbk0si().b[20][0]++;
      cov_p8qgbk0si().s[40]++;
      return [];
    } else {
      cov_p8qgbk0si().b[20][1]++;
    }
    var recs = (cov_p8qgbk0si().s[41]++, []);
    cov_p8qgbk0si().s[42]++;
    if (basicAnalysis.winRate < 50) {
      cov_p8qgbk0si().b[22][0]++;
      cov_p8qgbk0si().s[43]++;
      recs.push('Focus on match-specific training and strategy');
    } else {
      cov_p8qgbk0si().b[22][1]++;
    }
    cov_p8qgbk0si().s[44]++;
    if (basicAnalysis.avgSessionScore < 70) {
      cov_p8qgbk0si().b[23][0]++;
      cov_p8qgbk0si().s[45]++;
      recs.push('Increase training intensity and consistency');
    } else {
      cov_p8qgbk0si().b[23][1]++;
    }
    cov_p8qgbk0si().s[46]++;
    strengthsWeaknesses.weaknesses.forEach(function (weakness) {
      cov_p8qgbk0si().f[7]++;
      cov_p8qgbk0si().s[47]++;
      if (weakness.includes('forehand')) {
        cov_p8qgbk0si().b[24][0]++;
        cov_p8qgbk0si().s[48]++;
        recs.push('Practice forehand technique with video analysis');
      } else {
        cov_p8qgbk0si().b[24][1]++;
        cov_p8qgbk0si().s[49]++;
        if (weakness.includes('serve')) {
          cov_p8qgbk0si().b[25][0]++;
          cov_p8qgbk0si().s[50]++;
          recs.push('Work on serve consistency and power');
        } else {
          cov_p8qgbk0si().b[25][1]++;
          cov_p8qgbk0si().s[51]++;
          if (weakness.includes('strategy')) {
            cov_p8qgbk0si().b[26][0]++;
            cov_p8qgbk0si().s[52]++;
            recs.push('Study match tactics and court positioning');
          } else {
            cov_p8qgbk0si().b[26][1]++;
          }
        }
      }
    });
    cov_p8qgbk0si().s[53]++;
    return recs.slice(0, 5);
  }, [enableRecommendations, basicAnalysis, strengthsWeaknesses]));
  var trends = (cov_p8qgbk0si().s[54]++, useMemo(function () {
    cov_p8qgbk0si().f[8]++;
    cov_p8qgbk0si().s[55]++;
    if ((cov_p8qgbk0si().b[28][0]++, !enableTrends) || (cov_p8qgbk0si().b[28][1]++, !data)) {
      cov_p8qgbk0si().b[27][0]++;
      cov_p8qgbk0si().s[56]++;
      return {
        skillProgression: [],
        matchPerformance: [],
        weeklyProgress: []
      };
    } else {
      cov_p8qgbk0si().b[27][1]++;
    }
    var skillProgression = (cov_p8qgbk0si().s[57]++, data.skillStats.length > 1 ? (cov_p8qgbk0si().b[29][0]++, function () {
      cov_p8qgbk0si().f[9]++;
      var skills = (cov_p8qgbk0si().s[58]++, ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game']);
      cov_p8qgbk0si().s[59]++;
      return skills.map(function (skill) {
        cov_p8qgbk0si().f[10]++;
        var values = (cov_p8qgbk0si().s[60]++, data.skillStats.map(function (s) {
          cov_p8qgbk0si().f[11]++;
          cov_p8qgbk0si().s[61]++;
          return s[skill];
        }).filter(function (v) {
          cov_p8qgbk0si().f[12]++;
          cov_p8qgbk0si().s[62]++;
          return v != null;
        }));
        cov_p8qgbk0si().s[63]++;
        if (values.length < 2) {
          cov_p8qgbk0si().b[30][0]++;
          cov_p8qgbk0si().s[64]++;
          return {
            skill: skill,
            previousRating: (cov_p8qgbk0si().b[31][0]++, values[0]) || (cov_p8qgbk0si().b[31][1]++, 50),
            currentRating: (cov_p8qgbk0si().b[32][0]++, values[0]) || (cov_p8qgbk0si().b[32][1]++, 50),
            trend: 'stable',
            changeRate: 0
          };
        } else {
          cov_p8qgbk0si().b[30][1]++;
        }
        var previousRating = (cov_p8qgbk0si().s[65]++, (cov_p8qgbk0si().b[33][0]++, Number(values[values.length - 2])) || (cov_p8qgbk0si().b[33][1]++, 50));
        var currentRating = (cov_p8qgbk0si().s[66]++, (cov_p8qgbk0si().b[34][0]++, Number(values[values.length - 1])) || (cov_p8qgbk0si().b[34][1]++, 50));
        var changeRate = (cov_p8qgbk0si().s[67]++, currentRating - previousRating);
        cov_p8qgbk0si().s[68]++;
        return {
          skill: skill,
          previousRating: previousRating,
          currentRating: currentRating,
          trend: changeRate > 2 ? (cov_p8qgbk0si().b[35][0]++, 'improving') : (cov_p8qgbk0si().b[35][1]++, changeRate < -2 ? (cov_p8qgbk0si().b[36][0]++, 'declining') : (cov_p8qgbk0si().b[36][1]++, 'stable')),
          changeRate: changeRate
        };
      });
    }()) : (cov_p8qgbk0si().b[29][1]++, []));
    var matchPerformance = (cov_p8qgbk0si().s[69]++, data.matches.slice(0, 10).map(function (match) {
      cov_p8qgbk0si().f[13]++;
      cov_p8qgbk0si().s[70]++;
      return {
        date: match.created_at,
        opponent: match.opponent_name,
        result: match.result,
        score: match.match_score,
        duration: match.duration_minutes
      };
    }));
    var weeklyProgress = (cov_p8qgbk0si().s[71]++, data.sessions.reduce(function (weeks, session) {
      cov_p8qgbk0si().f[14]++;
      var week = (cov_p8qgbk0si().s[72]++, new Date(session.created_at).toISOString().slice(0, 10));
      var existing = (cov_p8qgbk0si().s[73]++, weeks.find(function (w) {
        cov_p8qgbk0si().f[15]++;
        cov_p8qgbk0si().s[74]++;
        return w.week === week;
      }));
      cov_p8qgbk0si().s[75]++;
      if (existing) {
        cov_p8qgbk0si().b[37][0]++;
        cov_p8qgbk0si().s[76]++;
        existing.sessionsCompleted++;
        cov_p8qgbk0si().s[77]++;
        existing.totalScore += (cov_p8qgbk0si().b[38][0]++, session.overall_score) || (cov_p8qgbk0si().b[38][1]++, 0);
      } else {
        cov_p8qgbk0si().b[37][1]++;
        cov_p8qgbk0si().s[78]++;
        weeks.push({
          week: week,
          sessionsCompleted: 1,
          totalScore: (cov_p8qgbk0si().b[39][0]++, session.overall_score) || (cov_p8qgbk0si().b[39][1]++, 0)
        });
      }
      cov_p8qgbk0si().s[79]++;
      return weeks;
    }, []).map(function (week) {
      cov_p8qgbk0si().f[16]++;
      cov_p8qgbk0si().s[80]++;
      return Object.assign({}, week, {
        averageScore: Math.round(week.totalScore / week.sessionsCompleted)
      });
    }));
    cov_p8qgbk0si().s[81]++;
    return {
      skillProgression: skillProgression,
      matchPerformance: matchPerformance,
      weeklyProgress: weeklyProgress
    };
  }, [enableTrends, data == null ? void 0 : data.skillStats, data == null ? void 0 : data.matches, data == null ? void 0 : data.sessions, data == null ? void 0 : data.lastFetched]));
  var projections = (cov_p8qgbk0si().s[82]++, useMemo(function () {
    cov_p8qgbk0si().f[17]++;
    cov_p8qgbk0si().s[83]++;
    if ((cov_p8qgbk0si().b[41][0]++, !enableProjections) || (cov_p8qgbk0si().b[41][1]++, !trends.skillProgression.length)) {
      cov_p8qgbk0si().b[40][0]++;
      cov_p8qgbk0si().s[84]++;
      return {
        skillProgression: [],
        nextMilestones: []
      };
    } else {
      cov_p8qgbk0si().b[40][1]++;
    }
    var skillProgression = (cov_p8qgbk0si().s[85]++, trends.skillProgression.filter(function (s) {
      cov_p8qgbk0si().f[18]++;
      cov_p8qgbk0si().s[86]++;
      return s.trend === 'improving';
    }).map(function (s) {
      cov_p8qgbk0si().f[19]++;
      cov_p8qgbk0si().s[87]++;
      return {
        skill: s.skill,
        currentRating: s.currentRating,
        projectedRating: Math.min(100, s.currentRating + s.changeRate * 4),
        timeframe: '4 weeks',
        confidence: s.changeRate > 5 ? (cov_p8qgbk0si().b[42][0]++, 'high') : (cov_p8qgbk0si().b[42][1]++, s.changeRate > 2 ? (cov_p8qgbk0si().b[43][0]++, 'medium') : (cov_p8qgbk0si().b[43][1]++, 'low'))
      };
    }));
    var nextMilestones = (cov_p8qgbk0si().s[88]++, ['Reach 80% consistency in strongest skill', 'Improve weakest skill by 10 points', 'Win next 3 out of 5 matches', 'Complete 10 training sessions this month']);
    cov_p8qgbk0si().s[89]++;
    return {
      skillProgression: skillProgression,
      nextMilestones: nextMilestones
    };
  }, [enableProjections, trends.skillProgression]));
  cov_p8qgbk0si().s[90]++;
  return useMemo(function () {
    cov_p8qgbk0si().f[20]++;
    cov_p8qgbk0si().s[91]++;
    if (!basicAnalysis) {
      cov_p8qgbk0si().b[44][0]++;
      cov_p8qgbk0si().s[92]++;
      return null;
    } else {
      cov_p8qgbk0si().b[44][1]++;
    }
    cov_p8qgbk0si().s[93]++;
    return {
      overallRating: basicAnalysis.overallRating,
      strengths: strengthsWeaknesses.strengths,
      weaknesses: strengthsWeaknesses.weaknesses,
      recommendations: recommendations,
      trends: trends,
      projections: projections
    };
  }, [basicAnalysis, strengthsWeaknesses, recommendations, trends, projections]);
}
export function useMatchAnalysis() {
  cov_p8qgbk0si().f[21]++;
  cov_p8qgbk0si().s[94]++;
  return useCallback(function () {
    var _ref3 = _asyncToGenerator(function* (matchStats, opponentInfo) {
      cov_p8qgbk0si().f[22]++;
      var startTime = (cov_p8qgbk0si().s[95]++, Date.now());
      cov_p8qgbk0si().s[96]++;
      try {
        var _tacticalAnalysis;
        var basicAnalysis = (cov_p8qgbk0si().s[97]++, performanceAnalyticsService.analyzeMatchPerformance(matchStats));
        var tacticalAnalysis = (cov_p8qgbk0si().s[98]++, null);
        cov_p8qgbk0si().s[99]++;
        if (opponentInfo) {
          cov_p8qgbk0si().b[45][0]++;
          cov_p8qgbk0si().s[100]++;
          tacticalAnalysis = performanceAnalyticsService.analyzeTacticalPerformance(matchStats, (cov_p8qgbk0si().b[46][0]++, opponentInfo.style) || (cov_p8qgbk0si().b[46][1]++, 'baseline'), (cov_p8qgbk0si().b[47][0]++, opponentInfo.surface) || (cov_p8qgbk0si().b[47][1]++, 'hard'));
        } else {
          cov_p8qgbk0si().b[45][1]++;
        }
        var performanceBySet = (cov_p8qgbk0si().s[101]++, Array.from({
          length: 3
        }, function (_, i) {
          cov_p8qgbk0si().f[23]++;
          cov_p8qgbk0si().s[102]++;
          return Object.assign({}, matchStats, {
            totalPoints: Math.floor(matchStats.totalPoints / 3),
            pointsWon: Math.floor(matchStats.pointsWon / 3) - i * 2,
            unforcedErrors: Math.floor(matchStats.unforcedErrors / 3) + i
          });
        }));
        var fitnessAnalysis = (cov_p8qgbk0si().s[103]++, performanceAnalyticsService.analyzeFitnessMetrics(performanceBySet, matchStats.totalGameTime));
        var analysisTime = (cov_p8qgbk0si().s[104]++, Date.now() - startTime);
        cov_p8qgbk0si().s[105]++;
        if (analysisTime > 1000) {
          cov_p8qgbk0si().b[48][0]++;
          cov_p8qgbk0si().s[106]++;
          console.warn(`Slow match analysis: ${analysisTime}ms`);
        } else {
          cov_p8qgbk0si().b[48][1]++;
        }
        cov_p8qgbk0si().s[107]++;
        return {
          overallRating: basicAnalysis.overallRating,
          detailedMetrics: Object.assign({}, basicAnalysis.advancedMetrics, {
            winPercentage: matchStats.pointsWon / matchStats.totalPoints * 100,
            errorRate: (matchStats.unforcedErrors + matchStats.doubleFaults) / matchStats.totalPoints * 100,
            winnerRate: matchStats.winners / matchStats.totalPoints * 100
          }),
          tacticalInsights: (cov_p8qgbk0si().b[49][0]++, (_tacticalAnalysis = tacticalAnalysis) == null ? void 0 : _tacticalAnalysis.successfulTactics) || (cov_p8qgbk0si().b[49][1]++, []),
          fitnessAnalysis: fitnessAnalysis,
          analysisTime: analysisTime
        };
      } catch (error) {
        cov_p8qgbk0si().s[108]++;
        console.error('Match analysis error:', error);
        cov_p8qgbk0si().s[109]++;
        throw error;
      }
    });
    return function (_x, _x2) {
      return _ref3.apply(this, arguments);
    };
  }(), []);
}
export default {
  usePerformanceAnalysis: usePerformanceAnalysis,
  useMatchAnalysis: useMatchAnalysis
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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