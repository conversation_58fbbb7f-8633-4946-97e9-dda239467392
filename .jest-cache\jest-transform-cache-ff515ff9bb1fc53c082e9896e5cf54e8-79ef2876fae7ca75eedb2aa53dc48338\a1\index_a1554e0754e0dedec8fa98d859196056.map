{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_StyleSheet", "_View", "_canUseDom", "_excluded", "cssFunction", "window", "CSS", "supports", "SafeAreaView", "forwardRef", "props", "ref", "style", "rest", "createElement", "styles", "root", "displayName", "create", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _canUseDom = _interopRequireDefault(require(\"../../modules/canUseDom\"));\nvar _excluded = [\"style\"];\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nvar cssFunction = function () {\n  if (_canUseDom.default && window.CSS && window.CSS.supports && window.CSS.supports('top: constant(safe-area-inset-top)')) {\n    return 'constant';\n  }\n  return 'env';\n}();\nvar SafeAreaView = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var style = props.style,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, {\n    ref: ref,\n    style: [styles.root, style]\n  }));\n});\nSafeAreaView.displayName = 'SafeAreaView';\nvar styles = _StyleSheet.default.create({\n  root: {\n    paddingTop: cssFunction + \"(safe-area-inset-top)\",\n    paddingRight: cssFunction + \"(safe-area-inset-right)\",\n    paddingBottom: cssFunction + \"(safe-area-inset-bottom)\",\n    paddingLeft: cssFunction + \"(safe-area-inset-left)\"\n  }\n});\nvar _default = exports.default = SafeAreaView;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,KAAK,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,WAAW,GAAGT,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIS,KAAK,GAAGV,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIU,UAAU,GAAGX,sBAAsB,CAACC,OAAO,0BAA0B,CAAC,CAAC;AAC3E,IAAIW,SAAS,GAAG,CAAC,OAAO,CAAC;AAUzB,IAAIC,WAAW,GAAG,YAAY;EAC5B,IAAIF,UAAU,CAACT,OAAO,IAAIY,MAAM,CAACC,GAAG,IAAID,MAAM,CAACC,GAAG,CAACC,QAAQ,IAAIF,MAAM,CAACC,GAAG,CAACC,QAAQ,CAAC,oCAAoC,CAAC,EAAE;IACxH,OAAO,UAAU;EACnB;EACA,OAAO,KAAK;AACd,CAAC,CAAC,CAAC;AACH,IAAIC,YAAY,GAAgBT,KAAK,CAACU,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG,EAAK;EAC/D,IAAIC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACrBC,IAAI,GAAG,CAAC,CAAC,EAAEf,8BAA8B,CAACL,OAAO,EAAEiB,KAAK,EAAEP,SAAS,CAAC;EACtE,OAAoBJ,KAAK,CAACe,aAAa,CAACb,KAAK,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEoB,IAAI,EAAE;IACtFF,GAAG,EAAEA,GAAG;IACRC,KAAK,EAAE,CAACG,MAAM,CAACC,IAAI,EAAEJ,KAAK;EAC5B,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFJ,YAAY,CAACS,WAAW,GAAG,cAAc;AACzC,IAAIF,MAAM,GAAGf,WAAW,CAACP,OAAO,CAACyB,MAAM,CAAC;EACtCF,IAAI,EAAE;IACJG,UAAU,EAAEf,WAAW,GAAG,uBAAuB;IACjDgB,YAAY,EAAEhB,WAAW,GAAG,yBAAyB;IACrDiB,aAAa,EAAEjB,WAAW,GAAG,0BAA0B;IACvDkB,WAAW,EAAElB,WAAW,GAAG;EAC7B;AACF,CAAC,CAAC;AACF,IAAImB,QAAQ,GAAG5B,OAAO,CAACF,OAAO,GAAGe,YAAY;AAC7CgB,MAAM,CAAC7B,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}