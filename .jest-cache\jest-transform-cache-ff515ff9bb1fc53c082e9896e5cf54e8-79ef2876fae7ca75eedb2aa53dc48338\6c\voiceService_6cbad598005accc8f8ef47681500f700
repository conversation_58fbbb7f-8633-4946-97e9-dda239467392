8a428a96772c964a4d3d1982efcb5c3d
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
function cov_xnrgkz6vn() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\voiceService.ts";
  var hash = "4af3f3b1a33f377f2c6aecfa7e2b6142ca7a97fb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\voiceService.ts",
    statementMap: {
      "0": {
        start: {
          line: 3,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 5,
          column: 4
        },
        end: {
          line: 5,
          column: 51
        }
      },
      "2": {
        start: {
          line: 6,
          column: 4
        },
        end: {
          line: 6,
          column: 61
        }
      },
      "3": {
        start: {
          line: 6,
          column: 34
        },
        end: {
          line: 6,
          column: 59
        }
      },
      "4": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 9,
          column: 40
        }
      },
      "5": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 29
        }
      },
      "6": {
        start: {
          line: 34,
          column: 24
        },
        end: {
          line: 34,
          column: 29
        }
      },
      "7": {
        start: {
          line: 35,
          column: 23
        },
        end: {
          line: 35,
          column: 28
        }
      },
      "8": {
        start: {
          line: 36,
          column: 79
        },
        end: {
          line: 36,
          column: 81
        }
      },
      "9": {
        start: {
          line: 39,
          column: 42
        },
        end: {
          line: 56,
          column: 3
        }
      },
      "10": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 79,
          column: 5
        }
      },
      "11": {
        start: {
          line: 64,
          column: 6
        },
        end: {
          line: 64,
          column: 55
        }
      },
      "12": {
        start: {
          line: 67,
          column: 26
        },
        end: {
          line: 67,
          column: 72
        }
      },
      "13": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 75,
          column: 7
        }
      },
      "14": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 70,
          column: 66
        }
      },
      "15": {
        start: {
          line: 71,
          column: 8
        },
        end: {
          line: 71,
          column: 20
        }
      },
      "16": {
        start: {
          line: 73,
          column: 8
        },
        end: {
          line: 73,
          column: 70
        }
      },
      "17": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 74,
          column: 21
        }
      },
      "18": {
        start: {
          line: 77,
          column: 6
        },
        end: {
          line: 77,
          column: 68
        }
      },
      "19": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 19
        }
      },
      "20": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 88,
          column: 5
        }
      },
      "21": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 87,
          column: 13
        }
      },
      "22": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 104,
          column: 5
        }
      },
      "23": {
        start: {
          line: 91,
          column: 6
        },
        end: {
          line: 91,
          column: 30
        }
      },
      "24": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 47
        }
      },
      "25": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 95,
          column: 58
        }
      },
      "26": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 98,
          column: 38
        }
      },
      "27": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 31
        }
      },
      "28": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 64
        }
      },
      "29": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 18
        }
      },
      "30": {
        start: {
          line: 111,
          column: 4
        },
        end: {
          line: 113,
          column: 5
        }
      },
      "31": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 112,
          column: 13
        }
      },
      "32": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 122,
          column: 5
        }
      },
      "33": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 31
        }
      },
      "34": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 37
        }
      },
      "35": {
        start: {
          line: 118,
          column: 6
        },
        end: {
          line: 118,
          column: 55
        }
      },
      "36": {
        start: {
          line: 120,
          column: 6
        },
        end: {
          line: 120,
          column: 64
        }
      },
      "37": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 18
        }
      },
      "38": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 131,
          column: 5
        }
      },
      "39": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 32
        }
      },
      "40": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 149,
          column: 5
        }
      },
      "41": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 29
        }
      },
      "42": {
        start: {
          line: 136,
          column: 28
        },
        end: {
          line: 141,
          column: 7
        }
      },
      "43": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 46
        }
      },
      "44": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 30
        }
      },
      "45": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 30
        }
      },
      "46": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 147,
          column: 51
        }
      },
      "47": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 18
        }
      },
      "48": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 161,
          column: 5
        }
      },
      "49": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 26
        }
      },
      "50": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 30
        }
      },
      "51": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 53
        }
      },
      "52": {
        start: {
          line: 168,
          column: 33
        },
        end: {
          line: 168,
          column: 64
        }
      },
      "53": {
        start: {
          line: 171,
          column: 27
        },
        end: {
          line: 173,
          column: 5
        }
      },
      "54": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 62
        }
      },
      "55": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 180,
          column: 5
        }
      },
      "56": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 179,
          column: 8
        }
      },
      "57": {
        start: {
          line: 183,
          column: 25
        },
        end: {
          line: 183,
          column: 68
        }
      },
      "58": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 186,
          column: 5
        }
      },
      "59": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 185,
          column: 26
        }
      },
      "60": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 188,
          column: 16
        }
      },
      "61": {
        start: {
          line: 195,
          column: 4
        },
        end: {
          line: 195,
          column: 54
        }
      },
      "62": {
        start: {
          line: 195,
          column: 41
        },
        end: {
          line: 195,
          column: 52
        }
      },
      "63": {
        start: {
          line: 202,
          column: 4
        },
        end: {
          line: 202,
          column: 28
        }
      },
      "64": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 209,
          column: 27
        }
      },
      "65": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 245,
          column: 6
        }
      },
      "66": {
        start: {
          line: 252,
          column: 20
        },
        end: {
          line: 252,
          column: 45
        }
      },
      "67": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 261,
          column: 5
        }
      },
      "68": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 257,
          column: 31
        }
      },
      "69": {
        start: {
          line: 259,
          column: 29
        },
        end: {
          line: 259,
          column: 67
        }
      },
      "70": {
        start: {
          line: 260,
          column: 6
        },
        end: {
          line: 260,
          column: 86
        }
      },
      "71": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 267,
          column: 7
        }
      },
      "72": {
        start: {
          line: 273,
          column: 4
        },
        end: {
          line: 279,
          column: 5
        }
      },
      "73": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 18
        }
      },
      "74": {
        start: {
          line: 278,
          column: 6
        },
        end: {
          line: 278,
          column: 19
        }
      },
      "75": {
        start: {
          line: 284,
          column: 25
        },
        end: {
          line: 290,
          column: 5
        }
      },
      "76": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 303,
          column: 13
        }
      },
      "77": {
        start: {
          line: 293,
          column: 6
        },
        end: {
          line: 302,
          column: 7
        }
      },
      "78": {
        start: {
          line: 294,
          column: 30
        },
        end: {
          line: 294,
          column: 91
        }
      },
      "79": {
        start: {
          line: 295,
          column: 47
        },
        end: {
          line: 299,
          column: 9
        }
      },
      "80": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 301,
          column: 72
        }
      },
      "81": {
        start: {
          line: 301,
          column: 54
        },
        end: {
          line: 301,
          column: 70
        }
      },
      "82": {
        start: {
          line: 308,
          column: 28
        },
        end: {
          line: 308,
          column: 30
        }
      },
      "83": {
        start: {
          line: 310,
          column: 4
        },
        end: {
          line: 327,
          column: 5
        }
      },
      "84": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 315,
          column: 73
        }
      },
      "85": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 316,
          column: 14
        }
      },
      "86": {
        start: {
          line: 320,
          column: 30
        },
        end: {
          line: 320,
          column: 78
        }
      },
      "87": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 325,
          column: 9
        }
      },
      "88": {
        start: {
          line: 322,
          column: 24
        },
        end: {
          line: 322,
          column: 50
        }
      },
      "89": {
        start: {
          line: 323,
          column: 23
        },
        end: {
          line: 323,
          column: 53
        }
      },
      "90": {
        start: {
          line: 324,
          column: 10
        },
        end: {
          line: 324,
          column: 76
        }
      },
      "91": {
        start: {
          line: 326,
          column: 8
        },
        end: {
          line: 326,
          column: 14
        }
      },
      "92": {
        start: {
          line: 329,
          column: 4
        },
        end: {
          line: 329,
          column: 22
        }
      },
      "93": {
        start: {
          line: 334,
          column: 4
        },
        end: {
          line: 349,
          column: 5
        }
      },
      "94": {
        start: {
          line: 335,
          column: 27
        },
        end: {
          line: 335,
          column: 53
        }
      },
      "95": {
        start: {
          line: 336,
          column: 30
        },
        end: {
          line: 336,
          column: 51
        }
      },
      "96": {
        start: {
          line: 338,
          column: 23
        },
        end: {
          line: 338,
          column: 24
        }
      },
      "97": {
        start: {
          line: 339,
          column: 6
        },
        end: {
          line: 343,
          column: 7
        }
      },
      "98": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 342,
          column: 9
        }
      },
      "99": {
        start: {
          line: 340,
          column: 39
        },
        end: {
          line: 340,
          column: 77
        }
      },
      "100": {
        start: {
          line: 341,
          column: 10
        },
        end: {
          line: 341,
          column: 23
        }
      },
      "101": {
        start: {
          line: 346,
          column: 6
        },
        end: {
          line: 348,
          column: 7
        }
      },
      "102": {
        start: {
          line: 347,
          column: 8
        },
        end: {
          line: 347,
          column: 23
        }
      },
      "103": {
        start: {
          line: 351,
          column: 4
        },
        end: {
          line: 351,
          column: 16
        }
      },
      "104": {
        start: {
          line: 355,
          column: 28
        },
        end: {
          line: 355,
          column: 46
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 4,
            column: 9
          },
          end: {
            line: 4,
            column: 10
          }
        },
        loc: {
          start: {
            line: 4,
            column: 48
          },
          end: {
            line: 7,
            column: 3
          }
        },
        line: 4
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 6,
            column: 23
          },
          end: {
            line: 6,
            column: 24
          }
        },
        loc: {
          start: {
            line: 6,
            column: 34
          },
          end: {
            line: 6,
            column: 59
          }
        },
        line: 6
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 8,
            column: 8
          },
          end: {
            line: 8,
            column: 9
          }
        },
        loc: {
          start: {
            line: 8,
            column: 20
          },
          end: {
            line: 11,
            column: 3
          }
        },
        line: 8
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 61,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        },
        loc: {
          start: {
            line: 61,
            column: 55
          },
          end: {
            line: 80,
            column: 3
          }
        },
        line: 61
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 85,
            column: 2
          },
          end: {
            line: 85,
            column: 3
          }
        },
        loc: {
          start: {
            line: 85,
            column: 90
          },
          end: {
            line: 105,
            column: 3
          }
        },
        line: 85
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 110,
            column: 2
          },
          end: {
            line: 110,
            column: 3
          }
        },
        loc: {
          start: {
            line: 110,
            column: 39
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 110
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 128,
            column: 2
          },
          end: {
            line: 128,
            column: 3
          }
        },
        loc: {
          start: {
            line: 128,
            column: 72
          },
          end: {
            line: 150,
            column: 3
          }
        },
        line: 128
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 155,
            column: 2
          },
          end: {
            line: 155,
            column: 3
          }
        },
        loc: {
          start: {
            line: 155,
            column: 38
          },
          end: {
            line: 162,
            column: 3
          }
        },
        line: 155
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 167,
            column: 2
          },
          end: {
            line: 167,
            column: 3
          }
        },
        loc: {
          start: {
            line: 167,
            column: 63
          },
          end: {
            line: 189,
            column: 3
          }
        },
        line: 167
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 171,
            column: 51
          },
          end: {
            line: 171,
            column: 52
          }
        },
        loc: {
          start: {
            line: 172,
            column: 6
          },
          end: {
            line: 172,
            column: 62
          }
        },
        line: 172
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 194,
            column: 2
          },
          end: {
            line: 194,
            column: 3
          }
        },
        loc: {
          start: {
            line: 194,
            column: 35
          },
          end: {
            line: 196,
            column: 3
          }
        },
        line: 194
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 195,
            column: 34
          },
          end: {
            line: 195,
            column: 35
          }
        },
        loc: {
          start: {
            line: 195,
            column: 41
          },
          end: {
            line: 195,
            column: 52
          }
        },
        line: 195
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 201,
            column: 2
          },
          end: {
            line: 201,
            column: 3
          }
        },
        loc: {
          start: {
            line: 201,
            column: 34
          },
          end: {
            line: 203,
            column: 3
          }
        },
        line: 201
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 208,
            column: 2
          },
          end: {
            line: 208,
            column: 3
          }
        },
        loc: {
          start: {
            line: 208,
            column: 33
          },
          end: {
            line: 210,
            column: 3
          }
        },
        line: 208
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 215,
            column: 2
          },
          end: {
            line: 215,
            column: 3
          }
        },
        loc: {
          start: {
            line: 215,
            column: 52
          },
          end: {
            line: 246,
            column: 3
          }
        },
        line: 215
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 251,
            column: 2
          },
          end: {
            line: 251,
            column: 3
          }
        },
        loc: {
          start: {
            line: 251,
            column: 128
          },
          end: {
            line: 268,
            column: 3
          }
        },
        line: 251
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 272,
            column: 2
          },
          end: {
            line: 272,
            column: 3
          }
        },
        loc: {
          start: {
            line: 272,
            column: 70
          },
          end: {
            line: 280,
            column: 3
          }
        },
        line: 272
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 282,
            column: 2
          },
          end: {
            line: 282,
            column: 3
          }
        },
        loc: {
          start: {
            line: 282,
            column: 43
          },
          end: {
            line: 304,
            column: 3
          }
        },
        line: 282
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 292,
            column: 15
          },
          end: {
            line: 292,
            column: 16
          }
        },
        loc: {
          start: {
            line: 292,
            column: 21
          },
          end: {
            line: 303,
            column: 5
          }
        },
        line: 292
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 301,
            column: 42
          },
          end: {
            line: 301,
            column: 43
          }
        },
        loc: {
          start: {
            line: 301,
            column: 54
          },
          end: {
            line: 301,
            column: 70
          }
        },
        line: 301
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 306,
            column: 2
          },
          end: {
            line: 306,
            column: 3
          }
        },
        loc: {
          start: {
            line: 306,
            column: 83
          },
          end: {
            line: 330,
            column: 3
          }
        },
        line: 306
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 332,
            column: 2
          },
          end: {
            line: 332,
            column: 3
          }
        },
        loc: {
          start: {
            line: 332,
            column: 68
          },
          end: {
            line: 352,
            column: 3
          }
        },
        line: 332
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 340,
            column: 33
          },
          end: {
            line: 340,
            column: 34
          }
        },
        loc: {
          start: {
            line: 340,
            column: 39
          },
          end: {
            line: 340,
            column: 77
          }
        },
        line: 340
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 69,
            column: 6
          },
          end: {
            line: 75,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 6
          },
          end: {
            line: 75,
            column: 7
          }
        }, {
          start: {
            line: 72,
            column: 13
          },
          end: {
            line: 75,
            column: 7
          }
        }],
        line: 69
      },
      "1": {
        loc: {
          start: {
            line: 86,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 86,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 86
      },
      "2": {
        loc: {
          start: {
            line: 111,
            column: 4
          },
          end: {
            line: 113,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 4
          },
          end: {
            line: 113,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "3": {
        loc: {
          start: {
            line: 128,
            column: 28
          },
          end: {
            line: 128,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 128,
            column: 53
          },
          end: {
            line: 128,
            column: 55
          }
        }],
        line: 128
      },
      "4": {
        loc: {
          start: {
            line: 129,
            column: 4
          },
          end: {
            line: 131,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 129,
            column: 4
          },
          end: {
            line: 131,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 129
      },
      "5": {
        loc: {
          start: {
            line: 137,
            column: 18
          },
          end: {
            line: 137,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 137,
            column: 18
          },
          end: {
            line: 137,
            column: 34
          }
        }, {
          start: {
            line: 137,
            column: 38
          },
          end: {
            line: 137,
            column: 45
          }
        }],
        line: 137
      },
      "6": {
        loc: {
          start: {
            line: 138,
            column: 15
          },
          end: {
            line: 138,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 15
          },
          end: {
            line: 138,
            column: 28
          }
        }, {
          start: {
            line: 138,
            column: 32
          },
          end: {
            line: 138,
            column: 35
          }
        }],
        line: 138
      },
      "7": {
        loc: {
          start: {
            line: 139,
            column: 14
          },
          end: {
            line: 139,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 139,
            column: 14
          },
          end: {
            line: 139,
            column: 26
          }
        }, {
          start: {
            line: 139,
            column: 30
          },
          end: {
            line: 139,
            column: 33
          }
        }],
        line: 139
      },
      "8": {
        loc: {
          start: {
            line: 175,
            column: 4
          },
          end: {
            line: 180,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 4
          },
          end: {
            line: 180,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "9": {
        loc: {
          start: {
            line: 184,
            column: 4
          },
          end: {
            line: 186,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 184,
            column: 4
          },
          end: {
            line: 186,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 184
      },
      "10": {
        loc: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 261,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 261,
            column: 5
          }
        }, {
          start: {
            line: 258,
            column: 11
          },
          end: {
            line: 261,
            column: 5
          }
        }],
        line: 256
      },
      "11": {
        loc: {
          start: {
            line: 259,
            column: 29
          },
          end: {
            line: 259,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 259,
            column: 29
          },
          end: {
            line: 259,
            column: 42
          }
        }, {
          start: {
            line: 259,
            column: 46
          },
          end: {
            line: 259,
            column: 67
          }
        }],
        line: 259
      },
      "12": {
        loc: {
          start: {
            line: 293,
            column: 6
          },
          end: {
            line: 302,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 6
          },
          end: {
            line: 302,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 293
      },
      "13": {
        loc: {
          start: {
            line: 293,
            column: 10
          },
          end: {
            line: 293,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 293,
            column: 10
          },
          end: {
            line: 293,
            column: 26
          }
        }, {
          start: {
            line: 293,
            column: 30
          },
          end: {
            line: 293,
            column: 66
          }
        }],
        line: 293
      },
      "14": {
        loc: {
          start: {
            line: 310,
            column: 4
          },
          end: {
            line: 327,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 311,
            column: 6
          },
          end: {
            line: 311,
            column: 27
          }
        }, {
          start: {
            line: 312,
            column: 6
          },
          end: {
            line: 312,
            column: 30
          }
        }, {
          start: {
            line: 313,
            column: 6
          },
          end: {
            line: 316,
            column: 14
          }
        }, {
          start: {
            line: 318,
            column: 6
          },
          end: {
            line: 326,
            column: 14
          }
        }],
        line: 310
      },
      "15": {
        loc: {
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 325,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 321,
            column: 8
          },
          end: {
            line: 325,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 321
      },
      "16": {
        loc: {
          start: {
            line: 324,
            column: 32
          },
          end: {
            line: 324,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 324,
            column: 57
          },
          end: {
            line: 324,
            column: 67
          }
        }, {
          start: {
            line: 324,
            column: 70
          },
          end: {
            line: 324,
            column: 75
          }
        }],
        line: 324
      },
      "17": {
        loc: {
          start: {
            line: 340,
            column: 8
          },
          end: {
            line: 342,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 340,
            column: 8
          },
          end: {
            line: 342,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 340
      },
      "18": {
        loc: {
          start: {
            line: 340,
            column: 39
          },
          end: {
            line: 340,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 340,
            column: 39
          },
          end: {
            line: 340,
            column: 56
          }
        }, {
          start: {
            line: 340,
            column: 60
          },
          end: {
            line: 340,
            column: 77
          }
        }],
        line: 340
      },
      "19": {
        loc: {
          start: {
            line: 346,
            column: 6
          },
          end: {
            line: 348,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 346,
            column: 6
          },
          end: {
            line: 348,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 346
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0, 0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4af3f3b1a33f377f2c6aecfa7e2b6142ca7a97fb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_xnrgkz6vn = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xnrgkz6vn();
var Speech = (cov_xnrgkz6vn().s[0]++, {
  speak: function () {
    var _speak = _asyncToGenerator(function* (text, options) {
      cov_xnrgkz6vn().f[0]++;
      cov_xnrgkz6vn().s[1]++;
      console.log(`🎤 Mock TTS: "${text}"`, options);
      cov_xnrgkz6vn().s[2]++;
      return new Promise(function (resolve) {
        cov_xnrgkz6vn().f[1]++;
        cov_xnrgkz6vn().s[3]++;
        return setTimeout(resolve, 1000);
      });
    });
    function speak(_x, _x2) {
      return _speak.apply(this, arguments);
    }
    return speak;
  }(),
  stop: function () {
    var _stop = _asyncToGenerator(function* () {
      cov_xnrgkz6vn().f[2]++;
      cov_xnrgkz6vn().s[4]++;
      console.log('🎤 Mock TTS: Stopped');
      cov_xnrgkz6vn().s[5]++;
      return Promise.resolve();
    });
    function stop() {
      return _stop.apply(this, arguments);
    }
    return stop;
  }()
});
var VoiceService = function () {
  function VoiceService() {
    _classCallCheck(this, VoiceService);
    this.isListening = (cov_xnrgkz6vn().s[6]++, false);
    this.isSpeaking = (cov_xnrgkz6vn().s[7]++, false);
    this.recognitionCallbacks = (cov_xnrgkz6vn().s[8]++, []);
    this.voiceCommands = (cov_xnrgkz6vn().s[9]++, [{
      command: 'start recording',
      action: 'START_RECORDING'
    }, {
      command: 'stop recording',
      action: 'STOP_RECORDING'
    }, {
      command: 'take photo',
      action: 'TAKE_PHOTO'
    }, {
      command: 'start training',
      action: 'START_TRAINING'
    }, {
      command: 'end session',
      action: 'END_SESSION'
    }, {
      command: 'show stats',
      action: 'SHOW_STATS'
    }, {
      command: 'give me a tip',
      action: 'GET_TIP'
    }, {
      command: 'analyze my serve',
      action: 'ANALYZE_SERVE'
    }, {
      command: 'analyze my forehand',
      action: 'ANALYZE_FOREHAND'
    }, {
      command: 'analyze my backhand',
      action: 'ANALYZE_BACKHAND'
    }, {
      command: 'start match',
      action: 'START_MATCH'
    }, {
      command: 'pause',
      action: 'PAUSE'
    }, {
      command: 'resume',
      action: 'RESUME'
    }, {
      command: 'save session',
      action: 'SAVE_SESSION'
    }, {
      command: 'go back',
      action: 'NAVIGATE_BACK'
    }, {
      command: 'go home',
      action: 'NAVIGATE_HOME'
    }]);
  }
  return _createClass(VoiceService, [{
    key: "initializeVoiceRecognition",
    value: (function () {
      var _initializeVoiceRecognition = _asyncToGenerator(function* () {
        cov_xnrgkz6vn().f[3]++;
        cov_xnrgkz6vn().s[10]++;
        try {
          cov_xnrgkz6vn().s[11]++;
          console.log('Initializing voice recognition...');
          var isAvailable = (cov_xnrgkz6vn().s[12]++, yield this.checkVoiceRecognitionAvailability());
          cov_xnrgkz6vn().s[13]++;
          if (isAvailable) {
            cov_xnrgkz6vn().b[0][0]++;
            cov_xnrgkz6vn().s[14]++;
            console.log('Voice recognition initialized successfully');
            cov_xnrgkz6vn().s[15]++;
            return true;
          } else {
            cov_xnrgkz6vn().b[0][1]++;
            cov_xnrgkz6vn().s[16]++;
            console.log('Voice recognition not available on this device');
            cov_xnrgkz6vn().s[17]++;
            return false;
          }
        } catch (error) {
          cov_xnrgkz6vn().s[18]++;
          console.error('Error initializing voice recognition:', error);
          cov_xnrgkz6vn().s[19]++;
          return false;
        }
      });
      function initializeVoiceRecognition() {
        return _initializeVoiceRecognition.apply(this, arguments);
      }
      return initializeVoiceRecognition;
    }())
  }, {
    key: "startListening",
    value: (function () {
      var _startListening = _asyncToGenerator(function* (callback) {
        cov_xnrgkz6vn().f[4]++;
        cov_xnrgkz6vn().s[20]++;
        if (this.isListening) {
          cov_xnrgkz6vn().b[1][0]++;
          cov_xnrgkz6vn().s[21]++;
          return;
        } else {
          cov_xnrgkz6vn().b[1][1]++;
        }
        cov_xnrgkz6vn().s[22]++;
        try {
          cov_xnrgkz6vn().s[23]++;
          this.isListening = true;
          cov_xnrgkz6vn().s[24]++;
          this.recognitionCallbacks.push(callback);
          cov_xnrgkz6vn().s[25]++;
          console.log('Started listening for voice input...');
          cov_xnrgkz6vn().s[26]++;
          this.simulateVoiceRecognition();
        } catch (error) {
          cov_xnrgkz6vn().s[27]++;
          this.isListening = false;
          cov_xnrgkz6vn().s[28]++;
          console.error('Error starting voice recognition:', error);
          cov_xnrgkz6vn().s[29]++;
          throw error;
        }
      });
      function startListening(_x3) {
        return _startListening.apply(this, arguments);
      }
      return startListening;
    }())
  }, {
    key: "stopListening",
    value: (function () {
      var _stopListening = _asyncToGenerator(function* () {
        cov_xnrgkz6vn().f[5]++;
        cov_xnrgkz6vn().s[30]++;
        if (!this.isListening) {
          cov_xnrgkz6vn().b[2][0]++;
          cov_xnrgkz6vn().s[31]++;
          return;
        } else {
          cov_xnrgkz6vn().b[2][1]++;
        }
        cov_xnrgkz6vn().s[32]++;
        try {
          cov_xnrgkz6vn().s[33]++;
          this.isListening = false;
          cov_xnrgkz6vn().s[34]++;
          this.recognitionCallbacks = [];
          cov_xnrgkz6vn().s[35]++;
          console.log('Stopped listening for voice input');
        } catch (error) {
          cov_xnrgkz6vn().s[36]++;
          console.error('Error stopping voice recognition:', error);
          cov_xnrgkz6vn().s[37]++;
          throw error;
        }
      });
      function stopListening() {
        return _stopListening.apply(this, arguments);
      }
      return stopListening;
    }())
  }, {
    key: "speak",
    value: (function () {
      var _speak2 = _asyncToGenerator(function* (text) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_xnrgkz6vn().b[3][0]++, {});
        cov_xnrgkz6vn().f[6]++;
        cov_xnrgkz6vn().s[38]++;
        if (this.isSpeaking) {
          cov_xnrgkz6vn().b[4][0]++;
          cov_xnrgkz6vn().s[39]++;
          yield this.stopSpeaking();
        } else {
          cov_xnrgkz6vn().b[4][1]++;
        }
        cov_xnrgkz6vn().s[40]++;
        try {
          cov_xnrgkz6vn().s[41]++;
          this.isSpeaking = true;
          var speechOptions = (cov_xnrgkz6vn().s[42]++, {
            language: (cov_xnrgkz6vn().b[5][0]++, options.language) || (cov_xnrgkz6vn().b[5][1]++, 'en-US'),
            pitch: (cov_xnrgkz6vn().b[6][0]++, options.pitch) || (cov_xnrgkz6vn().b[6][1]++, 1.0),
            rate: (cov_xnrgkz6vn().b[7][0]++, options.rate) || (cov_xnrgkz6vn().b[7][1]++, 1.0),
            voice: options.voice
          });
          cov_xnrgkz6vn().s[43]++;
          yield Speech.speak(text, speechOptions);
          cov_xnrgkz6vn().s[44]++;
          this.isSpeaking = false;
        } catch (error) {
          cov_xnrgkz6vn().s[45]++;
          this.isSpeaking = false;
          cov_xnrgkz6vn().s[46]++;
          console.error('Error speaking text:', error);
          cov_xnrgkz6vn().s[47]++;
          throw error;
        }
      });
      function speak(_x4) {
        return _speak2.apply(this, arguments);
      }
      return speak;
    }())
  }, {
    key: "stopSpeaking",
    value: (function () {
      var _stopSpeaking = _asyncToGenerator(function* () {
        cov_xnrgkz6vn().f[7]++;
        cov_xnrgkz6vn().s[48]++;
        try {
          cov_xnrgkz6vn().s[49]++;
          yield Speech.stop();
          cov_xnrgkz6vn().s[50]++;
          this.isSpeaking = false;
        } catch (error) {
          cov_xnrgkz6vn().s[51]++;
          console.error('Error stopping speech:', error);
        }
      });
      function stopSpeaking() {
        return _stopSpeaking.apply(this, arguments);
      }
      return stopSpeaking;
    }())
  }, {
    key: "processVoiceCommand",
    value: function processVoiceCommand(transcript) {
      cov_xnrgkz6vn().f[8]++;
      var normalizedTranscript = (cov_xnrgkz6vn().s[52]++, transcript.toLowerCase().trim());
      var matchedCommand = (cov_xnrgkz6vn().s[53]++, this.voiceCommands.find(function (cmd) {
        cov_xnrgkz6vn().f[9]++;
        cov_xnrgkz6vn().s[54]++;
        return normalizedTranscript.includes(cmd.command.toLowerCase());
      }));
      cov_xnrgkz6vn().s[55]++;
      if (matchedCommand) {
        cov_xnrgkz6vn().b[8][0]++;
        cov_xnrgkz6vn().s[56]++;
        return Object.assign({}, matchedCommand, {
          parameters: this.extractCommandParameters(normalizedTranscript, matchedCommand)
        });
      } else {
        cov_xnrgkz6vn().b[8][1]++;
      }
      var partialMatch = (cov_xnrgkz6vn().s[57]++, this.findPartialMatch(normalizedTranscript));
      cov_xnrgkz6vn().s[58]++;
      if (partialMatch) {
        cov_xnrgkz6vn().b[9][0]++;
        cov_xnrgkz6vn().s[59]++;
        return partialMatch;
      } else {
        cov_xnrgkz6vn().b[9][1]++;
      }
      cov_xnrgkz6vn().s[60]++;
      return null;
    }
  }, {
    key: "getAvailableCommands",
    value: function getAvailableCommands() {
      cov_xnrgkz6vn().f[10]++;
      cov_xnrgkz6vn().s[61]++;
      return this.voiceCommands.map(function (cmd) {
        cov_xnrgkz6vn().f[11]++;
        cov_xnrgkz6vn().s[62]++;
        return cmd.command;
      });
    }
  }, {
    key: "isCurrentlyListening",
    value: function isCurrentlyListening() {
      cov_xnrgkz6vn().f[12]++;
      cov_xnrgkz6vn().s[63]++;
      return this.isListening;
    }
  }, {
    key: "isCurrentlySpeaking",
    value: function isCurrentlySpeaking() {
      cov_xnrgkz6vn().f[13]++;
      cov_xnrgkz6vn().s[64]++;
      return this.isSpeaking;
    }
  }, {
    key: "getCoachingPhrases",
    value: function getCoachingPhrases() {
      cov_xnrgkz6vn().f[14]++;
      cov_xnrgkz6vn().s[65]++;
      return {
        encouragement: ["Great shot! Keep it up!", "Excellent form on that forehand!", "Perfect timing on that serve!", "Your footwork is improving!", "Nice consistency in that rally!"],
        corrections: ["Try to keep your eye on the ball longer", "Remember to follow through completely", "Focus on your split step timing", "Keep your racquet head up on the volley", "Bend your knees more for better balance"],
        tips: ["Remember to prepare early for each shot", "Use your legs to generate power", "Keep your head still during contact", "Follow through across your body", "Stay light on your feet between shots"],
        instructions: ["Let's work on your serve technique", "Time for some forehand practice", "Let's focus on net play today", "Ready for some footwork drills?", "Let's analyze your last rally"]
      };
    }
  }, {
    key: "speakCoachingFeedback",
    value: (function () {
      var _speakCoachingFeedback = _asyncToGenerator(function* (type, customText) {
        cov_xnrgkz6vn().f[15]++;
        var phrases = (cov_xnrgkz6vn().s[66]++, this.getCoachingPhrases());
        var textToSpeak;
        cov_xnrgkz6vn().s[67]++;
        if (customText) {
          cov_xnrgkz6vn().b[10][0]++;
          cov_xnrgkz6vn().s[68]++;
          textToSpeak = customText;
        } else {
          cov_xnrgkz6vn().b[10][1]++;
          var phrasesForType = (cov_xnrgkz6vn().s[69]++, (cov_xnrgkz6vn().b[11][0]++, phrases[type]) || (cov_xnrgkz6vn().b[11][1]++, phrases.encouragement));
          cov_xnrgkz6vn().s[70]++;
          textToSpeak = phrasesForType[Math.floor(Math.random() * phrasesForType.length)];
        }
        cov_xnrgkz6vn().s[71]++;
        yield this.speak(textToSpeak, {
          language: 'en-US',
          rate: 0.9,
          pitch: 1.1
        });
      });
      function speakCoachingFeedback(_x5, _x6) {
        return _speakCoachingFeedback.apply(this, arguments);
      }
      return speakCoachingFeedback;
    }())
  }, {
    key: "checkVoiceRecognitionAvailability",
    value: function () {
      var _checkVoiceRecognitionAvailability = _asyncToGenerator(function* () {
        cov_xnrgkz6vn().f[16]++;
        cov_xnrgkz6vn().s[72]++;
        try {
          cov_xnrgkz6vn().s[73]++;
          return true;
        } catch (error) {
          cov_xnrgkz6vn().s[74]++;
          return false;
        }
      });
      function checkVoiceRecognitionAvailability() {
        return _checkVoiceRecognitionAvailability.apply(this, arguments);
      }
      return checkVoiceRecognitionAvailability;
    }()
  }, {
    key: "simulateVoiceRecognition",
    value: function simulateVoiceRecognition() {
      var _this = this;
      cov_xnrgkz6vn().f[17]++;
      var mockCommands = (cov_xnrgkz6vn().s[75]++, ['start recording', 'stop recording', 'take photo', 'give me a tip', 'show stats']);
      cov_xnrgkz6vn().s[76]++;
      setTimeout(function () {
        cov_xnrgkz6vn().f[18]++;
        cov_xnrgkz6vn().s[77]++;
        if ((cov_xnrgkz6vn().b[13][0]++, _this.isListening) && (cov_xnrgkz6vn().b[13][1]++, _this.recognitionCallbacks.length > 0)) {
          cov_xnrgkz6vn().b[12][0]++;
          var randomCommand = (cov_xnrgkz6vn().s[78]++, mockCommands[Math.floor(Math.random() * mockCommands.length)]);
          var result = (cov_xnrgkz6vn().s[79]++, {
            transcript: randomCommand,
            confidence: 0.85 + Math.random() * 0.15,
            isFinal: true
          });
          cov_xnrgkz6vn().s[80]++;
          _this.recognitionCallbacks.forEach(function (callback) {
            cov_xnrgkz6vn().f[19]++;
            cov_xnrgkz6vn().s[81]++;
            return callback(result);
          });
        } else {
          cov_xnrgkz6vn().b[12][1]++;
        }
      }, 3000);
    }
  }, {
    key: "extractCommandParameters",
    value: function extractCommandParameters(transcript, command) {
      cov_xnrgkz6vn().f[20]++;
      var parameters = (cov_xnrgkz6vn().s[82]++, {});
      cov_xnrgkz6vn().s[83]++;
      switch (command.action) {
        case 'ANALYZE_SERVE':
          cov_xnrgkz6vn().b[14][0]++;
        case 'ANALYZE_FOREHAND':
          cov_xnrgkz6vn().b[14][1]++;
        case 'ANALYZE_BACKHAND':
          cov_xnrgkz6vn().b[14][2]++;
          cov_xnrgkz6vn().s[84]++;
          parameters.shotType = command.action.split('_')[1].toLowerCase();
          cov_xnrgkz6vn().s[85]++;
          break;
        case 'START_RECORDING':
          cov_xnrgkz6vn().b[14][3]++;
          var durationMatch = (cov_xnrgkz6vn().s[86]++, transcript.match(/(\d+)\s*(second|minute|min)/i));
          cov_xnrgkz6vn().s[87]++;
          if (durationMatch) {
            cov_xnrgkz6vn().b[15][0]++;
            var value = (cov_xnrgkz6vn().s[88]++, parseInt(durationMatch[1]));
            var unit = (cov_xnrgkz6vn().s[89]++, durationMatch[2].toLowerCase());
            cov_xnrgkz6vn().s[90]++;
            parameters.duration = unit.startsWith('min') ? (cov_xnrgkz6vn().b[16][0]++, value * 60) : (cov_xnrgkz6vn().b[16][1]++, value);
          } else {
            cov_xnrgkz6vn().b[15][1]++;
          }
          cov_xnrgkz6vn().s[91]++;
          break;
      }
      cov_xnrgkz6vn().s[92]++;
      return parameters;
    }
  }, {
    key: "findPartialMatch",
    value: function findPartialMatch(transcript) {
      cov_xnrgkz6vn().f[21]++;
      cov_xnrgkz6vn().s[93]++;
      for (var command of this.voiceCommands) {
        var commandWords = (cov_xnrgkz6vn().s[94]++, command.command.split(' '));
        var transcriptWords = (cov_xnrgkz6vn().s[95]++, transcript.split(' '));
        var matchCount = (cov_xnrgkz6vn().s[96]++, 0);
        cov_xnrgkz6vn().s[97]++;
        var _loop = function _loop(word) {
          cov_xnrgkz6vn().s[98]++;
          if (transcriptWords.some(function (tw) {
            cov_xnrgkz6vn().f[22]++;
            cov_xnrgkz6vn().s[99]++;
            return (cov_xnrgkz6vn().b[18][0]++, tw.includes(word)) || (cov_xnrgkz6vn().b[18][1]++, word.includes(tw));
          })) {
            cov_xnrgkz6vn().b[17][0]++;
            cov_xnrgkz6vn().s[100]++;
            matchCount++;
          } else {
            cov_xnrgkz6vn().b[17][1]++;
          }
        };
        for (var word of commandWords) {
          _loop(word);
        }
        cov_xnrgkz6vn().s[101]++;
        if (matchCount >= Math.ceil(commandWords.length / 2)) {
          cov_xnrgkz6vn().b[19][0]++;
          cov_xnrgkz6vn().s[102]++;
          return command;
        } else {
          cov_xnrgkz6vn().b[19][1]++;
        }
      }
      cov_xnrgkz6vn().s[103]++;
      return null;
    }
  }]);
}();
export var voiceService = (cov_xnrgkz6vn().s[104]++, new VoiceService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJTcGVlY2giLCJjb3ZfeG5yZ2t6NnZuIiwicyIsInNwZWFrIiwiX3NwZWFrIiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJ0ZXh0Iiwib3B0aW9ucyIsImYiLCJjb25zb2xlIiwibG9nIiwiUHJvbWlzZSIsInJlc29sdmUiLCJzZXRUaW1lb3V0IiwiX3giLCJfeDIiLCJhcHBseSIsImFyZ3VtZW50cyIsInN0b3AiLCJfc3RvcCIsIlZvaWNlU2VydmljZSIsIl9jbGFzc0NhbGxDaGVjayIsImlzTGlzdGVuaW5nIiwiaXNTcGVha2luZyIsInJlY29nbml0aW9uQ2FsbGJhY2tzIiwidm9pY2VDb21tYW5kcyIsImNvbW1hbmQiLCJhY3Rpb24iLCJfY3JlYXRlQ2xhc3MiLCJrZXkiLCJ2YWx1ZSIsIl9pbml0aWFsaXplVm9pY2VSZWNvZ25pdGlvbiIsImlzQXZhaWxhYmxlIiwiY2hlY2tWb2ljZVJlY29nbml0aW9uQXZhaWxhYmlsaXR5IiwiYiIsImVycm9yIiwiaW5pdGlhbGl6ZVZvaWNlUmVjb2duaXRpb24iLCJfc3RhcnRMaXN0ZW5pbmciLCJjYWxsYmFjayIsInB1c2giLCJzaW11bGF0ZVZvaWNlUmVjb2duaXRpb24iLCJzdGFydExpc3RlbmluZyIsIl94MyIsIl9zdG9wTGlzdGVuaW5nIiwic3RvcExpc3RlbmluZyIsIl9zcGVhazIiLCJsZW5ndGgiLCJ1bmRlZmluZWQiLCJzdG9wU3BlYWtpbmciLCJzcGVlY2hPcHRpb25zIiwibGFuZ3VhZ2UiLCJwaXRjaCIsInJhdGUiLCJ2b2ljZSIsIl94NCIsIl9zdG9wU3BlYWtpbmciLCJwcm9jZXNzVm9pY2VDb21tYW5kIiwidHJhbnNjcmlwdCIsIm5vcm1hbGl6ZWRUcmFuc2NyaXB0IiwidG9Mb3dlckNhc2UiLCJ0cmltIiwibWF0Y2hlZENvbW1hbmQiLCJmaW5kIiwiY21kIiwiaW5jbHVkZXMiLCJPYmplY3QiLCJhc3NpZ24iLCJwYXJhbWV0ZXJzIiwiZXh0cmFjdENvbW1hbmRQYXJhbWV0ZXJzIiwicGFydGlhbE1hdGNoIiwiZmluZFBhcnRpYWxNYXRjaCIsImdldEF2YWlsYWJsZUNvbW1hbmRzIiwibWFwIiwiaXNDdXJyZW50bHlMaXN0ZW5pbmciLCJpc0N1cnJlbnRseVNwZWFraW5nIiwiZ2V0Q29hY2hpbmdQaHJhc2VzIiwiZW5jb3VyYWdlbWVudCIsImNvcnJlY3Rpb25zIiwidGlwcyIsImluc3RydWN0aW9ucyIsIl9zcGVha0NvYWNoaW5nRmVlZGJhY2siLCJ0eXBlIiwiY3VzdG9tVGV4dCIsInBocmFzZXMiLCJ0ZXh0VG9TcGVhayIsInBocmFzZXNGb3JUeXBlIiwiTWF0aCIsImZsb29yIiwicmFuZG9tIiwic3BlYWtDb2FjaGluZ0ZlZWRiYWNrIiwiX3g1IiwiX3g2IiwiX2NoZWNrVm9pY2VSZWNvZ25pdGlvbkF2YWlsYWJpbGl0eSIsIl90aGlzIiwibW9ja0NvbW1hbmRzIiwicmFuZG9tQ29tbWFuZCIsInJlc3VsdCIsImNvbmZpZGVuY2UiLCJpc0ZpbmFsIiwiZm9yRWFjaCIsInNob3RUeXBlIiwic3BsaXQiLCJkdXJhdGlvbk1hdGNoIiwibWF0Y2giLCJwYXJzZUludCIsInVuaXQiLCJkdXJhdGlvbiIsInN0YXJ0c1dpdGgiLCJjb21tYW5kV29yZHMiLCJ0cmFuc2NyaXB0V29yZHMiLCJtYXRjaENvdW50IiwiX2xvb3AiLCJ3b3JkIiwic29tZSIsInR3IiwiY2VpbCIsInZvaWNlU2VydmljZSJdLCJzb3VyY2VzIjpbInZvaWNlU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBWb2ljZSBJbnB1dCBTZXJ2aWNlIGZvciBTcGVlY2ggUmVjb2duaXRpb24gYW5kIFRleHQtdG8tU3BlZWNoXG4vLyBNb2NrIFNwZWVjaCBpbXBsZW1lbnRhdGlvbiBmb3IgZGVtbyBtb2RlXG5jb25zdCBTcGVlY2ggPSB7XG4gIHNwZWFrOiBhc3luYyAodGV4dDogc3RyaW5nLCBvcHRpb25zPzogYW55KSA9PiB7XG4gICAgY29uc29sZS5sb2coYPCfjqQgTW9jayBUVFM6IFwiJHt0ZXh0fVwiYCwgb3B0aW9ucyk7XG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSk7XG4gIH0sXG4gIHN0b3A6IGFzeW5jICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygn8J+OpCBNb2NrIFRUUzogU3RvcHBlZCcpO1xuICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUoKTtcbiAgfSxcbn07XG5cbmV4cG9ydCBpbnRlcmZhY2UgVm9pY2VSZWNvZ25pdGlvblJlc3VsdCB7XG4gIHRyYW5zY3JpcHQ6IHN0cmluZztcbiAgY29uZmlkZW5jZTogbnVtYmVyO1xuICBpc0ZpbmFsOiBib29sZWFuO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFZvaWNlQ29tbWFuZCB7XG4gIGNvbW1hbmQ6IHN0cmluZztcbiAgYWN0aW9uOiBzdHJpbmc7XG4gIHBhcmFtZXRlcnM/OiBhbnk7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU3BlZWNoT3B0aW9ucyB7XG4gIGxhbmd1YWdlPzogc3RyaW5nO1xuICBwaXRjaD86IG51bWJlcjtcbiAgcmF0ZT86IG51bWJlcjtcbiAgdm9pY2U/OiBzdHJpbmc7XG59XG5cbmNsYXNzIFZvaWNlU2VydmljZSB7XG4gIHByaXZhdGUgaXNMaXN0ZW5pbmcgPSBmYWxzZTtcbiAgcHJpdmF0ZSBpc1NwZWFraW5nID0gZmFsc2U7XG4gIHByaXZhdGUgcmVjb2duaXRpb25DYWxsYmFja3M6ICgocmVzdWx0OiBWb2ljZVJlY29nbml0aW9uUmVzdWx0KSA9PiB2b2lkKVtdID0gW107XG5cbiAgLy8gVGVubmlzLXNwZWNpZmljIHZvaWNlIGNvbW1hbmRzXG4gIHByaXZhdGUgdm9pY2VDb21tYW5kczogVm9pY2VDb21tYW5kW10gPSBbXG4gICAgeyBjb21tYW5kOiAnc3RhcnQgcmVjb3JkaW5nJywgYWN0aW9uOiAnU1RBUlRfUkVDT1JESU5HJyB9LFxuICAgIHsgY29tbWFuZDogJ3N0b3AgcmVjb3JkaW5nJywgYWN0aW9uOiAnU1RPUF9SRUNPUkRJTkcnIH0sXG4gICAgeyBjb21tYW5kOiAndGFrZSBwaG90bycsIGFjdGlvbjogJ1RBS0VfUEhPVE8nIH0sXG4gICAgeyBjb21tYW5kOiAnc3RhcnQgdHJhaW5pbmcnLCBhY3Rpb246ICdTVEFSVF9UUkFJTklORycgfSxcbiAgICB7IGNvbW1hbmQ6ICdlbmQgc2Vzc2lvbicsIGFjdGlvbjogJ0VORF9TRVNTSU9OJyB9LFxuICAgIHsgY29tbWFuZDogJ3Nob3cgc3RhdHMnLCBhY3Rpb246ICdTSE9XX1NUQVRTJyB9LFxuICAgIHsgY29tbWFuZDogJ2dpdmUgbWUgYSB0aXAnLCBhY3Rpb246ICdHRVRfVElQJyB9LFxuICAgIHsgY29tbWFuZDogJ2FuYWx5emUgbXkgc2VydmUnLCBhY3Rpb246ICdBTkFMWVpFX1NFUlZFJyB9LFxuICAgIHsgY29tbWFuZDogJ2FuYWx5emUgbXkgZm9yZWhhbmQnLCBhY3Rpb246ICdBTkFMWVpFX0ZPUkVIQU5EJyB9LFxuICAgIHsgY29tbWFuZDogJ2FuYWx5emUgbXkgYmFja2hhbmQnLCBhY3Rpb246ICdBTkFMWVpFX0JBQ0tIQU5EJyB9LFxuICAgIHsgY29tbWFuZDogJ3N0YXJ0IG1hdGNoJywgYWN0aW9uOiAnU1RBUlRfTUFUQ0gnIH0sXG4gICAgeyBjb21tYW5kOiAncGF1c2UnLCBhY3Rpb246ICdQQVVTRScgfSxcbiAgICB7IGNvbW1hbmQ6ICdyZXN1bWUnLCBhY3Rpb246ICdSRVNVTUUnIH0sXG4gICAgeyBjb21tYW5kOiAnc2F2ZSBzZXNzaW9uJywgYWN0aW9uOiAnU0FWRV9TRVNTSU9OJyB9LFxuICAgIHsgY29tbWFuZDogJ2dvIGJhY2snLCBhY3Rpb246ICdOQVZJR0FURV9CQUNLJyB9LFxuICAgIHsgY29tbWFuZDogJ2dvIGhvbWUnLCBhY3Rpb246ICdOQVZJR0FURV9IT01FJyB9LFxuICBdO1xuXG4gIC8qKlxuICAgKiBJbml0aWFsaXplIHZvaWNlIHJlY29nbml0aW9uIChtb2NrIGltcGxlbWVudGF0aW9uKVxuICAgKi9cbiAgYXN5bmMgaW5pdGlhbGl6ZVZvaWNlUmVjb2duaXRpb24oKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIEluIGEgcmVhbCBpbXBsZW1lbnRhdGlvbiwgeW91IHdvdWxkIGluaXRpYWxpemUgc3BlZWNoIHJlY29nbml0aW9uXG4gICAgICBjb25zb2xlLmxvZygnSW5pdGlhbGl6aW5nIHZvaWNlIHJlY29nbml0aW9uLi4uJyk7XG4gICAgICBcbiAgICAgIC8vIENoZWNrIGlmIHNwZWVjaCByZWNvZ25pdGlvbiBpcyBhdmFpbGFibGVcbiAgICAgIGNvbnN0IGlzQXZhaWxhYmxlID0gYXdhaXQgdGhpcy5jaGVja1ZvaWNlUmVjb2duaXRpb25BdmFpbGFiaWxpdHkoKTtcbiAgICAgIFxuICAgICAgaWYgKGlzQXZhaWxhYmxlKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdWb2ljZSByZWNvZ25pdGlvbiBpbml0aWFsaXplZCBzdWNjZXNzZnVsbHknKTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmxvZygnVm9pY2UgcmVjb2duaXRpb24gbm90IGF2YWlsYWJsZSBvbiB0aGlzIGRldmljZScpO1xuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluaXRpYWxpemluZyB2b2ljZSByZWNvZ25pdGlvbjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFN0YXJ0IGxpc3RlbmluZyBmb3Igdm9pY2UgaW5wdXRcbiAgICovXG4gIGFzeW5jIHN0YXJ0TGlzdGVuaW5nKGNhbGxiYWNrOiAocmVzdWx0OiBWb2ljZVJlY29nbml0aW9uUmVzdWx0KSA9PiB2b2lkKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgaWYgKHRoaXMuaXNMaXN0ZW5pbmcpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgdGhpcy5pc0xpc3RlbmluZyA9IHRydWU7XG4gICAgICB0aGlzLnJlY29nbml0aW9uQ2FsbGJhY2tzLnB1c2goY2FsbGJhY2spO1xuXG4gICAgICAvLyBNb2NrIHZvaWNlIHJlY29nbml0aW9uIC0gaW4gcmVhbCBpbXBsZW1lbnRhdGlvbiwgdXNlIGV4cG8tc3BlZWNoIG9yIHJlYWN0LW5hdGl2ZS12b2ljZVxuICAgICAgY29uc29sZS5sb2coJ1N0YXJ0ZWQgbGlzdGVuaW5nIGZvciB2b2ljZSBpbnB1dC4uLicpO1xuICAgICAgXG4gICAgICAvLyBTaW11bGF0ZSB2b2ljZSByZWNvZ25pdGlvbiB3aXRoIG1vY2sgZGF0YVxuICAgICAgdGhpcy5zaW11bGF0ZVZvaWNlUmVjb2duaXRpb24oKTtcbiAgICAgIFxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aGlzLmlzTGlzdGVuaW5nID0gZmFsc2U7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzdGFydGluZyB2b2ljZSByZWNvZ25pdGlvbjonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU3RvcCBsaXN0ZW5pbmcgZm9yIHZvaWNlIGlucHV0XG4gICAqL1xuICBhc3luYyBzdG9wTGlzdGVuaW5nKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIGlmICghdGhpcy5pc0xpc3RlbmluZykge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICB0aGlzLmlzTGlzdGVuaW5nID0gZmFsc2U7XG4gICAgICB0aGlzLnJlY29nbml0aW9uQ2FsbGJhY2tzID0gW107XG4gICAgICBjb25zb2xlLmxvZygnU3RvcHBlZCBsaXN0ZW5pbmcgZm9yIHZvaWNlIGlucHV0Jyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHN0b3BwaW5nIHZvaWNlIHJlY29nbml0aW9uOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTcGVhayB0ZXh0IHVzaW5nIHRleHQtdG8tc3BlZWNoXG4gICAqL1xuICBhc3luYyBzcGVhayh0ZXh0OiBzdHJpbmcsIG9wdGlvbnM6IFNwZWVjaE9wdGlvbnMgPSB7fSk6IFByb21pc2U8dm9pZD4ge1xuICAgIGlmICh0aGlzLmlzU3BlYWtpbmcpIHtcbiAgICAgIGF3YWl0IHRoaXMuc3RvcFNwZWFraW5nKCk7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIHRoaXMuaXNTcGVha2luZyA9IHRydWU7XG4gICAgICBcbiAgICAgIGNvbnN0IHNwZWVjaE9wdGlvbnMgPSB7XG4gICAgICAgIGxhbmd1YWdlOiBvcHRpb25zLmxhbmd1YWdlIHx8ICdlbi1VUycsXG4gICAgICAgIHBpdGNoOiBvcHRpb25zLnBpdGNoIHx8IDEuMCxcbiAgICAgICAgcmF0ZTogb3B0aW9ucy5yYXRlIHx8IDEuMCxcbiAgICAgICAgdm9pY2U6IG9wdGlvbnMudm9pY2UsXG4gICAgICB9O1xuXG4gICAgICBhd2FpdCBTcGVlY2guc3BlYWsodGV4dCwgc3BlZWNoT3B0aW9ucyk7XG4gICAgICB0aGlzLmlzU3BlYWtpbmcgPSBmYWxzZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgdGhpcy5pc1NwZWFraW5nID0gZmFsc2U7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBzcGVha2luZyB0ZXh0OicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTdG9wIGN1cnJlbnQgc3BlZWNoXG4gICAqL1xuICBhc3luYyBzdG9wU3BlYWtpbmcoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IFNwZWVjaC5zdG9wKCk7XG4gICAgICB0aGlzLmlzU3BlYWtpbmcgPSBmYWxzZTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3RvcHBpbmcgc3BlZWNoOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogUHJvY2VzcyB2b2ljZSBjb21tYW5kXG4gICAqL1xuICBwcm9jZXNzVm9pY2VDb21tYW5kKHRyYW5zY3JpcHQ6IHN0cmluZyk6IFZvaWNlQ29tbWFuZCB8IG51bGwge1xuICAgIGNvbnN0IG5vcm1hbGl6ZWRUcmFuc2NyaXB0ID0gdHJhbnNjcmlwdC50b0xvd2VyQ2FzZSgpLnRyaW0oKTtcbiAgICBcbiAgICAvLyBGaW5kIG1hdGNoaW5nIGNvbW1hbmRcbiAgICBjb25zdCBtYXRjaGVkQ29tbWFuZCA9IHRoaXMudm9pY2VDb21tYW5kcy5maW5kKGNtZCA9PiBcbiAgICAgIG5vcm1hbGl6ZWRUcmFuc2NyaXB0LmluY2x1ZGVzKGNtZC5jb21tYW5kLnRvTG93ZXJDYXNlKCkpXG4gICAgKTtcblxuICAgIGlmIChtYXRjaGVkQ29tbWFuZCkge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgLi4ubWF0Y2hlZENvbW1hbmQsXG4gICAgICAgIHBhcmFtZXRlcnM6IHRoaXMuZXh0cmFjdENvbW1hbmRQYXJhbWV0ZXJzKG5vcm1hbGl6ZWRUcmFuc2NyaXB0LCBtYXRjaGVkQ29tbWFuZCksXG4gICAgICB9O1xuICAgIH1cblxuICAgIC8vIENoZWNrIGZvciBwYXJ0aWFsIG1hdGNoZXMgb3Igc2ltaWxhciBjb21tYW5kc1xuICAgIGNvbnN0IHBhcnRpYWxNYXRjaCA9IHRoaXMuZmluZFBhcnRpYWxNYXRjaChub3JtYWxpemVkVHJhbnNjcmlwdCk7XG4gICAgaWYgKHBhcnRpYWxNYXRjaCkge1xuICAgICAgcmV0dXJuIHBhcnRpYWxNYXRjaDtcbiAgICB9XG5cbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYXZhaWxhYmxlIHZvaWNlIGNvbW1hbmRzXG4gICAqL1xuICBnZXRBdmFpbGFibGVDb21tYW5kcygpOiBzdHJpbmdbXSB7XG4gICAgcmV0dXJuIHRoaXMudm9pY2VDb21tYW5kcy5tYXAoY21kID0+IGNtZC5jb21tYW5kKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBDaGVjayBpZiBjdXJyZW50bHkgbGlzdGVuaW5nXG4gICAqL1xuICBpc0N1cnJlbnRseUxpc3RlbmluZygpOiBib29sZWFuIHtcbiAgICByZXR1cm4gdGhpcy5pc0xpc3RlbmluZztcbiAgfVxuXG4gIC8qKlxuICAgKiBDaGVjayBpZiBjdXJyZW50bHkgc3BlYWtpbmdcbiAgICovXG4gIGlzQ3VycmVudGx5U3BlYWtpbmcoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMuaXNTcGVha2luZztcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgdGVubmlzLXNwZWNpZmljIGNvYWNoaW5nIHBocmFzZXNcbiAgICovXG4gIGdldENvYWNoaW5nUGhyYXNlcygpOiB7IFtrZXk6IHN0cmluZ106IHN0cmluZ1tdIH0ge1xuICAgIHJldHVybiB7XG4gICAgICBlbmNvdXJhZ2VtZW50OiBbXG4gICAgICAgIFwiR3JlYXQgc2hvdCEgS2VlcCBpdCB1cCFcIixcbiAgICAgICAgXCJFeGNlbGxlbnQgZm9ybSBvbiB0aGF0IGZvcmVoYW5kIVwiLFxuICAgICAgICBcIlBlcmZlY3QgdGltaW5nIG9uIHRoYXQgc2VydmUhXCIsXG4gICAgICAgIFwiWW91ciBmb290d29yayBpcyBpbXByb3ZpbmchXCIsXG4gICAgICAgIFwiTmljZSBjb25zaXN0ZW5jeSBpbiB0aGF0IHJhbGx5IVwiLFxuICAgICAgXSxcbiAgICAgIGNvcnJlY3Rpb25zOiBbXG4gICAgICAgIFwiVHJ5IHRvIGtlZXAgeW91ciBleWUgb24gdGhlIGJhbGwgbG9uZ2VyXCIsXG4gICAgICAgIFwiUmVtZW1iZXIgdG8gZm9sbG93IHRocm91Z2ggY29tcGxldGVseVwiLFxuICAgICAgICBcIkZvY3VzIG9uIHlvdXIgc3BsaXQgc3RlcCB0aW1pbmdcIixcbiAgICAgICAgXCJLZWVwIHlvdXIgcmFjcXVldCBoZWFkIHVwIG9uIHRoZSB2b2xsZXlcIixcbiAgICAgICAgXCJCZW5kIHlvdXIga25lZXMgbW9yZSBmb3IgYmV0dGVyIGJhbGFuY2VcIixcbiAgICAgIF0sXG4gICAgICB0aXBzOiBbXG4gICAgICAgIFwiUmVtZW1iZXIgdG8gcHJlcGFyZSBlYXJseSBmb3IgZWFjaCBzaG90XCIsXG4gICAgICAgIFwiVXNlIHlvdXIgbGVncyB0byBnZW5lcmF0ZSBwb3dlclwiLFxuICAgICAgICBcIktlZXAgeW91ciBoZWFkIHN0aWxsIGR1cmluZyBjb250YWN0XCIsXG4gICAgICAgIFwiRm9sbG93IHRocm91Z2ggYWNyb3NzIHlvdXIgYm9keVwiLFxuICAgICAgICBcIlN0YXkgbGlnaHQgb24geW91ciBmZWV0IGJldHdlZW4gc2hvdHNcIixcbiAgICAgIF0sXG4gICAgICBpbnN0cnVjdGlvbnM6IFtcbiAgICAgICAgXCJMZXQncyB3b3JrIG9uIHlvdXIgc2VydmUgdGVjaG5pcXVlXCIsXG4gICAgICAgIFwiVGltZSBmb3Igc29tZSBmb3JlaGFuZCBwcmFjdGljZVwiLFxuICAgICAgICBcIkxldCdzIGZvY3VzIG9uIG5ldCBwbGF5IHRvZGF5XCIsXG4gICAgICAgIFwiUmVhZHkgZm9yIHNvbWUgZm9vdHdvcmsgZHJpbGxzP1wiLFxuICAgICAgICBcIkxldCdzIGFuYWx5emUgeW91ciBsYXN0IHJhbGx5XCIsXG4gICAgICBdLFxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogU3BlYWsgY29hY2hpbmcgZmVlZGJhY2tcbiAgICovXG4gIGFzeW5jIHNwZWFrQ29hY2hpbmdGZWVkYmFjayh0eXBlOiAnZW5jb3VyYWdlbWVudCcgfCAnY29ycmVjdGlvbicgfCAndGlwJyB8ICdpbnN0cnVjdGlvbicsIGN1c3RvbVRleHQ/OiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zdCBwaHJhc2VzID0gdGhpcy5nZXRDb2FjaGluZ1BocmFzZXMoKTtcbiAgICBcbiAgICBsZXQgdGV4dFRvU3BlYWs6IHN0cmluZztcbiAgICBcbiAgICBpZiAoY3VzdG9tVGV4dCkge1xuICAgICAgdGV4dFRvU3BlYWsgPSBjdXN0b21UZXh0O1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBwaHJhc2VzRm9yVHlwZSA9IHBocmFzZXNbdHlwZV0gfHwgcGhyYXNlcy5lbmNvdXJhZ2VtZW50O1xuICAgICAgdGV4dFRvU3BlYWsgPSBwaHJhc2VzRm9yVHlwZVtNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkgKiBwaHJhc2VzRm9yVHlwZS5sZW5ndGgpXTtcbiAgICB9XG5cbiAgICBhd2FpdCB0aGlzLnNwZWFrKHRleHRUb1NwZWFrLCB7XG4gICAgICBsYW5ndWFnZTogJ2VuLVVTJyxcbiAgICAgIHJhdGU6IDAuOSwgLy8gU2xpZ2h0bHkgc2xvd2VyIGZvciBjb2FjaGluZ1xuICAgICAgcGl0Y2g6IDEuMSwgLy8gU2xpZ2h0bHkgaGlnaGVyIHBpdGNoIGZvciBlbmNvdXJhZ2VtZW50XG4gICAgfSk7XG4gIH1cblxuICAvLyBQcml2YXRlIGhlbHBlciBtZXRob2RzXG5cbiAgcHJpdmF0ZSBhc3luYyBjaGVja1ZvaWNlUmVjb2duaXRpb25BdmFpbGFiaWxpdHkoKTogUHJvbWlzZTxib29sZWFuPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIENoZWNrIGlmIHRoZSBkZXZpY2Ugc3VwcG9ydHMgc3BlZWNoIHJlY29nbml0aW9uXG4gICAgICAvLyBUaGlzIGlzIGEgbW9jayBpbXBsZW1lbnRhdGlvblxuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIHNpbXVsYXRlVm9pY2VSZWNvZ25pdGlvbigpOiB2b2lkIHtcbiAgICAvLyBTaW11bGF0ZSB2b2ljZSByZWNvZ25pdGlvbiByZXN1bHRzIGZvciBkZW1vIHB1cnBvc2VzXG4gICAgY29uc3QgbW9ja0NvbW1hbmRzID0gW1xuICAgICAgJ3N0YXJ0IHJlY29yZGluZycsXG4gICAgICAnc3RvcCByZWNvcmRpbmcnLFxuICAgICAgJ3Rha2UgcGhvdG8nLFxuICAgICAgJ2dpdmUgbWUgYSB0aXAnLFxuICAgICAgJ3Nob3cgc3RhdHMnLFxuICAgIF07XG5cbiAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgIGlmICh0aGlzLmlzTGlzdGVuaW5nICYmIHRoaXMucmVjb2duaXRpb25DYWxsYmFja3MubGVuZ3RoID4gMCkge1xuICAgICAgICBjb25zdCByYW5kb21Db21tYW5kID0gbW9ja0NvbW1hbmRzW01hdGguZmxvb3IoTWF0aC5yYW5kb20oKSAqIG1vY2tDb21tYW5kcy5sZW5ndGgpXTtcbiAgICAgICAgY29uc3QgcmVzdWx0OiBWb2ljZVJlY29nbml0aW9uUmVzdWx0ID0ge1xuICAgICAgICAgIHRyYW5zY3JpcHQ6IHJhbmRvbUNvbW1hbmQsXG4gICAgICAgICAgY29uZmlkZW5jZTogMC44NSArIE1hdGgucmFuZG9tKCkgKiAwLjE1LFxuICAgICAgICAgIGlzRmluYWw6IHRydWUsXG4gICAgICAgIH07XG5cbiAgICAgICAgdGhpcy5yZWNvZ25pdGlvbkNhbGxiYWNrcy5mb3JFYWNoKGNhbGxiYWNrID0+IGNhbGxiYWNrKHJlc3VsdCkpO1xuICAgICAgfVxuICAgIH0sIDMwMDApOyAvLyBTaW11bGF0ZSAzLXNlY29uZCBkZWxheVxuICB9XG5cbiAgcHJpdmF0ZSBleHRyYWN0Q29tbWFuZFBhcmFtZXRlcnModHJhbnNjcmlwdDogc3RyaW5nLCBjb21tYW5kOiBWb2ljZUNvbW1hbmQpOiBhbnkge1xuICAgIC8vIEV4dHJhY3QgcGFyYW1ldGVycyBmcm9tIHZvaWNlIGNvbW1hbmRzXG4gICAgY29uc3QgcGFyYW1ldGVyczogYW55ID0ge307XG5cbiAgICBzd2l0Y2ggKGNvbW1hbmQuYWN0aW9uKSB7XG4gICAgICBjYXNlICdBTkFMWVpFX1NFUlZFJzpcbiAgICAgIGNhc2UgJ0FOQUxZWkVfRk9SRUhBTkQnOlxuICAgICAgY2FzZSAnQU5BTFlaRV9CQUNLSEFORCc6XG4gICAgICAgIC8vIEV4dHJhY3Qgc2hvdCB0eXBlIGZyb20gY29tbWFuZFxuICAgICAgICBwYXJhbWV0ZXJzLnNob3RUeXBlID0gY29tbWFuZC5hY3Rpb24uc3BsaXQoJ18nKVsxXS50b0xvd2VyQ2FzZSgpO1xuICAgICAgICBicmVhaztcbiAgICAgIFxuICAgICAgY2FzZSAnU1RBUlRfUkVDT1JESU5HJzpcbiAgICAgICAgLy8gQ2hlY2sgZm9yIGR1cmF0aW9uIG1lbnRpb25zXG4gICAgICAgIGNvbnN0IGR1cmF0aW9uTWF0Y2ggPSB0cmFuc2NyaXB0Lm1hdGNoKC8oXFxkKylcXHMqKHNlY29uZHxtaW51dGV8bWluKS9pKTtcbiAgICAgICAgaWYgKGR1cmF0aW9uTWF0Y2gpIHtcbiAgICAgICAgICBjb25zdCB2YWx1ZSA9IHBhcnNlSW50KGR1cmF0aW9uTWF0Y2hbMV0pO1xuICAgICAgICAgIGNvbnN0IHVuaXQgPSBkdXJhdGlvbk1hdGNoWzJdLnRvTG93ZXJDYXNlKCk7XG4gICAgICAgICAgcGFyYW1ldGVycy5kdXJhdGlvbiA9IHVuaXQuc3RhcnRzV2l0aCgnbWluJykgPyB2YWx1ZSAqIDYwIDogdmFsdWU7XG4gICAgICAgIH1cbiAgICAgICAgYnJlYWs7XG4gICAgfVxuXG4gICAgcmV0dXJuIHBhcmFtZXRlcnM7XG4gIH1cblxuICBwcml2YXRlIGZpbmRQYXJ0aWFsTWF0Y2godHJhbnNjcmlwdDogc3RyaW5nKTogVm9pY2VDb21tYW5kIHwgbnVsbCB7XG4gICAgLy8gRmluZCBjb21tYW5kcyB3aXRoIHBhcnRpYWwgbWF0Y2hlc1xuICAgIGZvciAoY29uc3QgY29tbWFuZCBvZiB0aGlzLnZvaWNlQ29tbWFuZHMpIHtcbiAgICAgIGNvbnN0IGNvbW1hbmRXb3JkcyA9IGNvbW1hbmQuY29tbWFuZC5zcGxpdCgnICcpO1xuICAgICAgY29uc3QgdHJhbnNjcmlwdFdvcmRzID0gdHJhbnNjcmlwdC5zcGxpdCgnICcpO1xuICAgICAgXG4gICAgICBsZXQgbWF0Y2hDb3VudCA9IDA7XG4gICAgICBmb3IgKGNvbnN0IHdvcmQgb2YgY29tbWFuZFdvcmRzKSB7XG4gICAgICAgIGlmICh0cmFuc2NyaXB0V29yZHMuc29tZSh0dyA9PiB0dy5pbmNsdWRlcyh3b3JkKSB8fCB3b3JkLmluY2x1ZGVzKHR3KSkpIHtcbiAgICAgICAgICBtYXRjaENvdW50Kys7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIFxuICAgICAgLy8gSWYgbW9yZSB0aGFuIGhhbGYgdGhlIHdvcmRzIG1hdGNoLCBjb25zaWRlciBpdCBhIHBhcnRpYWwgbWF0Y2hcbiAgICAgIGlmIChtYXRjaENvdW50ID49IE1hdGguY2VpbChjb21tYW5kV29yZHMubGVuZ3RoIC8gMikpIHtcbiAgICAgICAgcmV0dXJuIGNvbW1hbmQ7XG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IHZvaWNlU2VydmljZSA9IG5ldyBWb2ljZVNlcnZpY2UoKTtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRUEsSUFBTUEsTUFBTSxJQUFBQyxhQUFBLEdBQUFDLENBQUEsT0FBRztFQUNiQyxLQUFLO0lBQUEsSUFBQUMsTUFBQSxHQUFBQyxpQkFBQSxDQUFFLFdBQU9DLElBQVksRUFBRUMsT0FBYSxFQUFLO01BQUFOLGFBQUEsR0FBQU8sQ0FBQTtNQUFBUCxhQUFBLEdBQUFDLENBQUE7TUFDNUNPLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLGlCQUFpQkosSUFBSSxHQUFHLEVBQUVDLE9BQU8sQ0FBQztNQUFDTixhQUFBLEdBQUFDLENBQUE7TUFDL0MsT0FBTyxJQUFJUyxPQUFPLENBQUMsVUFBQUMsT0FBTyxFQUFJO1FBQUFYLGFBQUEsR0FBQU8sQ0FBQTtRQUFBUCxhQUFBLEdBQUFDLENBQUE7UUFBQSxPQUFBVyxVQUFVLENBQUNELE9BQU8sRUFBRSxJQUFJLENBQUM7TUFBRCxDQUFDLENBQUM7SUFDMUQsQ0FBQztJQUFBLFNBSERULEtBQUtBLENBQUFXLEVBQUEsRUFBQUMsR0FBQTtNQUFBLE9BQUFYLE1BQUEsQ0FBQVksS0FBQSxPQUFBQyxTQUFBO0lBQUE7SUFBQSxPQUFMZCxLQUFLO0VBQUEsR0FHSjtFQUNEZSxJQUFJO0lBQUEsSUFBQUMsS0FBQSxHQUFBZCxpQkFBQSxDQUFFLGFBQVk7TUFBQUosYUFBQSxHQUFBTyxDQUFBO01BQUFQLGFBQUEsR0FBQUMsQ0FBQTtNQUNoQk8sT0FBTyxDQUFDQyxHQUFHLENBQUMsc0JBQXNCLENBQUM7TUFBQ1QsYUFBQSxHQUFBQyxDQUFBO01BQ3BDLE9BQU9TLE9BQU8sQ0FBQ0MsT0FBTyxDQUFDLENBQUM7SUFDMUIsQ0FBQztJQUFBLFNBSERNLElBQUlBLENBQUE7TUFBQSxPQUFBQyxLQUFBLENBQUFILEtBQUEsT0FBQUMsU0FBQTtJQUFBO0lBQUEsT0FBSkMsSUFBSTtFQUFBO0FBSU4sQ0FBQztBQUFDLElBcUJJRSxZQUFZO0VBQUEsU0FBQUEsYUFBQTtJQUFBQyxlQUFBLE9BQUFELFlBQUE7SUFBQSxLQUNSRSxXQUFXLElBQUFyQixhQUFBLEdBQUFDLENBQUEsT0FBRyxLQUFLO0lBQUEsS0FDbkJxQixVQUFVLElBQUF0QixhQUFBLEdBQUFDLENBQUEsT0FBRyxLQUFLO0lBQUEsS0FDbEJzQixvQkFBb0IsSUFBQXZCLGFBQUEsR0FBQUMsQ0FBQSxPQUFpRCxFQUFFO0lBQUEsS0FHdkV1QixhQUFhLElBQUF4QixhQUFBLEdBQUFDLENBQUEsT0FBbUIsQ0FDdEM7TUFBRXdCLE9BQU8sRUFBRSxpQkFBaUI7TUFBRUMsTUFBTSxFQUFFO0lBQWtCLENBQUMsRUFDekQ7TUFBRUQsT0FBTyxFQUFFLGdCQUFnQjtNQUFFQyxNQUFNLEVBQUU7SUFBaUIsQ0FBQyxFQUN2RDtNQUFFRCxPQUFPLEVBQUUsWUFBWTtNQUFFQyxNQUFNLEVBQUU7SUFBYSxDQUFDLEVBQy9DO01BQUVELE9BQU8sRUFBRSxnQkFBZ0I7TUFBRUMsTUFBTSxFQUFFO0lBQWlCLENBQUMsRUFDdkQ7TUFBRUQsT0FBTyxFQUFFLGFBQWE7TUFBRUMsTUFBTSxFQUFFO0lBQWMsQ0FBQyxFQUNqRDtNQUFFRCxPQUFPLEVBQUUsWUFBWTtNQUFFQyxNQUFNLEVBQUU7SUFBYSxDQUFDLEVBQy9DO01BQUVELE9BQU8sRUFBRSxlQUFlO01BQUVDLE1BQU0sRUFBRTtJQUFVLENBQUMsRUFDL0M7TUFBRUQsT0FBTyxFQUFFLGtCQUFrQjtNQUFFQyxNQUFNLEVBQUU7SUFBZ0IsQ0FBQyxFQUN4RDtNQUFFRCxPQUFPLEVBQUUscUJBQXFCO01BQUVDLE1BQU0sRUFBRTtJQUFtQixDQUFDLEVBQzlEO01BQUVELE9BQU8sRUFBRSxxQkFBcUI7TUFBRUMsTUFBTSxFQUFFO0lBQW1CLENBQUMsRUFDOUQ7TUFBRUQsT0FBTyxFQUFFLGFBQWE7TUFBRUMsTUFBTSxFQUFFO0lBQWMsQ0FBQyxFQUNqRDtNQUFFRCxPQUFPLEVBQUUsT0FBTztNQUFFQyxNQUFNLEVBQUU7SUFBUSxDQUFDLEVBQ3JDO01BQUVELE9BQU8sRUFBRSxRQUFRO01BQUVDLE1BQU0sRUFBRTtJQUFTLENBQUMsRUFDdkM7TUFBRUQsT0FBTyxFQUFFLGNBQWM7TUFBRUMsTUFBTSxFQUFFO0lBQWUsQ0FBQyxFQUNuRDtNQUFFRCxPQUFPLEVBQUUsU0FBUztNQUFFQyxNQUFNLEVBQUU7SUFBZ0IsQ0FBQyxFQUMvQztNQUFFRCxPQUFPLEVBQUUsU0FBUztNQUFFQyxNQUFNLEVBQUU7SUFBZ0IsQ0FBQyxDQUNoRDtFQUFBO0VBQUEsT0FBQUMsWUFBQSxDQUFBUixZQUFBO0lBQUFTLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFDLDJCQUFBLEdBQUExQixpQkFBQSxDQUtELGFBQXFEO1FBQUFKLGFBQUEsR0FBQU8sQ0FBQTtRQUFBUCxhQUFBLEdBQUFDLENBQUE7UUFDbkQsSUFBSTtVQUFBRCxhQUFBLEdBQUFDLENBQUE7VUFFRk8sT0FBTyxDQUFDQyxHQUFHLENBQUMsbUNBQW1DLENBQUM7VUFHaEQsSUFBTXNCLFdBQVcsSUFBQS9CLGFBQUEsR0FBQUMsQ0FBQSxjQUFTLElBQUksQ0FBQytCLGlDQUFpQyxDQUFDLENBQUM7VUFBQ2hDLGFBQUEsR0FBQUMsQ0FBQTtVQUVuRSxJQUFJOEIsV0FBVyxFQUFFO1lBQUEvQixhQUFBLEdBQUFpQyxDQUFBO1lBQUFqQyxhQUFBLEdBQUFDLENBQUE7WUFDZk8sT0FBTyxDQUFDQyxHQUFHLENBQUMsNENBQTRDLENBQUM7WUFBQ1QsYUFBQSxHQUFBQyxDQUFBO1lBQzFELE9BQU8sSUFBSTtVQUNiLENBQUMsTUFBTTtZQUFBRCxhQUFBLEdBQUFpQyxDQUFBO1lBQUFqQyxhQUFBLEdBQUFDLENBQUE7WUFDTE8sT0FBTyxDQUFDQyxHQUFHLENBQUMsZ0RBQWdELENBQUM7WUFBQ1QsYUFBQSxHQUFBQyxDQUFBO1lBQzlELE9BQU8sS0FBSztVQUNkO1FBQ0YsQ0FBQyxDQUFDLE9BQU9pQyxLQUFLLEVBQUU7VUFBQWxDLGFBQUEsR0FBQUMsQ0FBQTtVQUNkTyxPQUFPLENBQUMwQixLQUFLLENBQUMsdUNBQXVDLEVBQUVBLEtBQUssQ0FBQztVQUFDbEMsYUFBQSxHQUFBQyxDQUFBO1VBQzlELE9BQU8sS0FBSztRQUNkO01BQ0YsQ0FBQztNQUFBLFNBbkJLa0MsMEJBQTBCQSxDQUFBO1FBQUEsT0FBQUwsMkJBQUEsQ0FBQWYsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUExQm1CLDBCQUEwQjtJQUFBO0VBQUE7SUFBQVAsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQU8sZUFBQSxHQUFBaEMsaUJBQUEsQ0F3QmhDLFdBQXFCaUMsUUFBa0QsRUFBaUI7UUFBQXJDLGFBQUEsR0FBQU8sQ0FBQTtRQUFBUCxhQUFBLEdBQUFDLENBQUE7UUFDdEYsSUFBSSxJQUFJLENBQUNvQixXQUFXLEVBQUU7VUFBQXJCLGFBQUEsR0FBQWlDLENBQUE7VUFBQWpDLGFBQUEsR0FBQUMsQ0FBQTtVQUNwQjtRQUNGLENBQUM7VUFBQUQsYUFBQSxHQUFBaUMsQ0FBQTtRQUFBO1FBQUFqQyxhQUFBLEdBQUFDLENBQUE7UUFFRCxJQUFJO1VBQUFELGFBQUEsR0FBQUMsQ0FBQTtVQUNGLElBQUksQ0FBQ29CLFdBQVcsR0FBRyxJQUFJO1VBQUNyQixhQUFBLEdBQUFDLENBQUE7VUFDeEIsSUFBSSxDQUFDc0Isb0JBQW9CLENBQUNlLElBQUksQ0FBQ0QsUUFBUSxDQUFDO1VBQUNyQyxhQUFBLEdBQUFDLENBQUE7VUFHekNPLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNDQUFzQyxDQUFDO1VBQUNULGFBQUEsR0FBQUMsQ0FBQTtVQUdwRCxJQUFJLENBQUNzQyx3QkFBd0IsQ0FBQyxDQUFDO1FBRWpDLENBQUMsQ0FBQyxPQUFPTCxLQUFLLEVBQUU7VUFBQWxDLGFBQUEsR0FBQUMsQ0FBQTtVQUNkLElBQUksQ0FBQ29CLFdBQVcsR0FBRyxLQUFLO1VBQUNyQixhQUFBLEdBQUFDLENBQUE7VUFDekJPLE9BQU8sQ0FBQzBCLEtBQUssQ0FBQyxtQ0FBbUMsRUFBRUEsS0FBSyxDQUFDO1VBQUNsQyxhQUFBLEdBQUFDLENBQUE7VUFDMUQsTUFBTWlDLEtBQUs7UUFDYjtNQUNGLENBQUM7TUFBQSxTQXBCS00sY0FBY0EsQ0FBQUMsR0FBQTtRQUFBLE9BQUFMLGVBQUEsQ0FBQXJCLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBZHdCLGNBQWM7SUFBQTtFQUFBO0lBQUFaLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFhLGNBQUEsR0FBQXRDLGlCQUFBLENBeUJwQixhQUFxQztRQUFBSixhQUFBLEdBQUFPLENBQUE7UUFBQVAsYUFBQSxHQUFBQyxDQUFBO1FBQ25DLElBQUksQ0FBQyxJQUFJLENBQUNvQixXQUFXLEVBQUU7VUFBQXJCLGFBQUEsR0FBQWlDLENBQUE7VUFBQWpDLGFBQUEsR0FBQUMsQ0FBQTtVQUNyQjtRQUNGLENBQUM7VUFBQUQsYUFBQSxHQUFBaUMsQ0FBQTtRQUFBO1FBQUFqQyxhQUFBLEdBQUFDLENBQUE7UUFFRCxJQUFJO1VBQUFELGFBQUEsR0FBQUMsQ0FBQTtVQUNGLElBQUksQ0FBQ29CLFdBQVcsR0FBRyxLQUFLO1VBQUNyQixhQUFBLEdBQUFDLENBQUE7VUFDekIsSUFBSSxDQUFDc0Isb0JBQW9CLEdBQUcsRUFBRTtVQUFDdkIsYUFBQSxHQUFBQyxDQUFBO1VBQy9CTyxPQUFPLENBQUNDLEdBQUcsQ0FBQyxtQ0FBbUMsQ0FBQztRQUNsRCxDQUFDLENBQUMsT0FBT3lCLEtBQUssRUFBRTtVQUFBbEMsYUFBQSxHQUFBQyxDQUFBO1VBQ2RPLE9BQU8sQ0FBQzBCLEtBQUssQ0FBQyxtQ0FBbUMsRUFBRUEsS0FBSyxDQUFDO1VBQUNsQyxhQUFBLEdBQUFDLENBQUE7VUFDMUQsTUFBTWlDLEtBQUs7UUFDYjtNQUNGLENBQUM7TUFBQSxTQWJLUyxhQUFhQSxDQUFBO1FBQUEsT0FBQUQsY0FBQSxDQUFBM0IsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFiMkIsYUFBYTtJQUFBO0VBQUE7SUFBQWYsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWUsT0FBQSxHQUFBeEMsaUJBQUEsQ0FrQm5CLFdBQVlDLElBQVksRUFBOEM7UUFBQSxJQUE1Q0MsT0FBc0IsR0FBQVUsU0FBQSxDQUFBNkIsTUFBQSxRQUFBN0IsU0FBQSxRQUFBOEIsU0FBQSxHQUFBOUIsU0FBQSxPQUFBaEIsYUFBQSxHQUFBaUMsQ0FBQSxVQUFHLENBQUMsQ0FBQztRQUFBakMsYUFBQSxHQUFBTyxDQUFBO1FBQUFQLGFBQUEsR0FBQUMsQ0FBQTtRQUNuRCxJQUFJLElBQUksQ0FBQ3FCLFVBQVUsRUFBRTtVQUFBdEIsYUFBQSxHQUFBaUMsQ0FBQTtVQUFBakMsYUFBQSxHQUFBQyxDQUFBO1VBQ25CLE1BQU0sSUFBSSxDQUFDOEMsWUFBWSxDQUFDLENBQUM7UUFDM0IsQ0FBQztVQUFBL0MsYUFBQSxHQUFBaUMsQ0FBQTtRQUFBO1FBQUFqQyxhQUFBLEdBQUFDLENBQUE7UUFFRCxJQUFJO1VBQUFELGFBQUEsR0FBQUMsQ0FBQTtVQUNGLElBQUksQ0FBQ3FCLFVBQVUsR0FBRyxJQUFJO1VBRXRCLElBQU0wQixhQUFhLElBQUFoRCxhQUFBLEdBQUFDLENBQUEsUUFBRztZQUNwQmdELFFBQVEsRUFBRSxDQUFBakQsYUFBQSxHQUFBaUMsQ0FBQSxVQUFBM0IsT0FBTyxDQUFDMkMsUUFBUSxNQUFBakQsYUFBQSxHQUFBaUMsQ0FBQSxVQUFJLE9BQU87WUFDckNpQixLQUFLLEVBQUUsQ0FBQWxELGFBQUEsR0FBQWlDLENBQUEsVUFBQTNCLE9BQU8sQ0FBQzRDLEtBQUssTUFBQWxELGFBQUEsR0FBQWlDLENBQUEsVUFBSSxHQUFHO1lBQzNCa0IsSUFBSSxFQUFFLENBQUFuRCxhQUFBLEdBQUFpQyxDQUFBLFVBQUEzQixPQUFPLENBQUM2QyxJQUFJLE1BQUFuRCxhQUFBLEdBQUFpQyxDQUFBLFVBQUksR0FBRztZQUN6Qm1CLEtBQUssRUFBRTlDLE9BQU8sQ0FBQzhDO1VBQ2pCLENBQUM7VUFBQ3BELGFBQUEsR0FBQUMsQ0FBQTtVQUVGLE1BQU1GLE1BQU0sQ0FBQ0csS0FBSyxDQUFDRyxJQUFJLEVBQUUyQyxhQUFhLENBQUM7VUFBQ2hELGFBQUEsR0FBQUMsQ0FBQTtVQUN4QyxJQUFJLENBQUNxQixVQUFVLEdBQUcsS0FBSztRQUN6QixDQUFDLENBQUMsT0FBT1ksS0FBSyxFQUFFO1VBQUFsQyxhQUFBLEdBQUFDLENBQUE7VUFDZCxJQUFJLENBQUNxQixVQUFVLEdBQUcsS0FBSztVQUFDdEIsYUFBQSxHQUFBQyxDQUFBO1VBQ3hCTyxPQUFPLENBQUMwQixLQUFLLENBQUMsc0JBQXNCLEVBQUVBLEtBQUssQ0FBQztVQUFDbEMsYUFBQSxHQUFBQyxDQUFBO1VBQzdDLE1BQU1pQyxLQUFLO1FBQ2I7TUFDRixDQUFDO01BQUEsU0F0QktoQyxLQUFLQSxDQUFBbUQsR0FBQTtRQUFBLE9BQUFULE9BQUEsQ0FBQTdCLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBTGQsS0FBSztJQUFBO0VBQUE7SUFBQTBCLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUF5QixhQUFBLEdBQUFsRCxpQkFBQSxDQTJCWCxhQUFvQztRQUFBSixhQUFBLEdBQUFPLENBQUE7UUFBQVAsYUFBQSxHQUFBQyxDQUFBO1FBQ2xDLElBQUk7VUFBQUQsYUFBQSxHQUFBQyxDQUFBO1VBQ0YsTUFBTUYsTUFBTSxDQUFDa0IsSUFBSSxDQUFDLENBQUM7VUFBQ2pCLGFBQUEsR0FBQUMsQ0FBQTtVQUNwQixJQUFJLENBQUNxQixVQUFVLEdBQUcsS0FBSztRQUN6QixDQUFDLENBQUMsT0FBT1ksS0FBSyxFQUFFO1VBQUFsQyxhQUFBLEdBQUFDLENBQUE7VUFDZE8sT0FBTyxDQUFDMEIsS0FBSyxDQUFDLHdCQUF3QixFQUFFQSxLQUFLLENBQUM7UUFDaEQ7TUFDRixDQUFDO01BQUEsU0FQS2EsWUFBWUEsQ0FBQTtRQUFBLE9BQUFPLGFBQUEsQ0FBQXZDLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBWitCLFlBQVk7SUFBQTtFQUFBO0lBQUFuQixHQUFBO0lBQUFDLEtBQUEsRUFZbEIsU0FBQTBCLG1CQUFtQkEsQ0FBQ0MsVUFBa0IsRUFBdUI7TUFBQXhELGFBQUEsR0FBQU8sQ0FBQTtNQUMzRCxJQUFNa0Qsb0JBQW9CLElBQUF6RCxhQUFBLEdBQUFDLENBQUEsUUFBR3VELFVBQVUsQ0FBQ0UsV0FBVyxDQUFDLENBQUMsQ0FBQ0MsSUFBSSxDQUFDLENBQUM7TUFHNUQsSUFBTUMsY0FBYyxJQUFBNUQsYUFBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSSxDQUFDdUIsYUFBYSxDQUFDcUMsSUFBSSxDQUFDLFVBQUFDLEdBQUcsRUFDaEQ7UUFBQTlELGFBQUEsR0FBQU8sQ0FBQTtRQUFBUCxhQUFBLEdBQUFDLENBQUE7UUFBQSxPQUFBd0Qsb0JBQW9CLENBQUNNLFFBQVEsQ0FBQ0QsR0FBRyxDQUFDckMsT0FBTyxDQUFDaUMsV0FBVyxDQUFDLENBQUMsQ0FBQztNQUFELENBQ3pELENBQUM7TUFBQzFELGFBQUEsR0FBQUMsQ0FBQTtNQUVGLElBQUkyRCxjQUFjLEVBQUU7UUFBQTVELGFBQUEsR0FBQWlDLENBQUE7UUFBQWpDLGFBQUEsR0FBQUMsQ0FBQTtRQUNsQixPQUFBK0QsTUFBQSxDQUFBQyxNQUFBLEtBQ0tMLGNBQWM7VUFDakJNLFVBQVUsRUFBRSxJQUFJLENBQUNDLHdCQUF3QixDQUFDVixvQkFBb0IsRUFBRUcsY0FBYztRQUFDO01BRW5GLENBQUM7UUFBQTVELGFBQUEsR0FBQWlDLENBQUE7TUFBQTtNQUdELElBQU1tQyxZQUFZLElBQUFwRSxhQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNvRSxnQkFBZ0IsQ0FBQ1osb0JBQW9CLENBQUM7TUFBQ3pELGFBQUEsR0FBQUMsQ0FBQTtNQUNqRSxJQUFJbUUsWUFBWSxFQUFFO1FBQUFwRSxhQUFBLEdBQUFpQyxDQUFBO1FBQUFqQyxhQUFBLEdBQUFDLENBQUE7UUFDaEIsT0FBT21FLFlBQVk7TUFDckIsQ0FBQztRQUFBcEUsYUFBQSxHQUFBaUMsQ0FBQTtNQUFBO01BQUFqQyxhQUFBLEdBQUFDLENBQUE7TUFFRCxPQUFPLElBQUk7SUFDYjtFQUFDO0lBQUEyQixHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBeUMsb0JBQW9CQSxDQUFBLEVBQWE7TUFBQXRFLGFBQUEsR0FBQU8sQ0FBQTtNQUFBUCxhQUFBLEdBQUFDLENBQUE7TUFDL0IsT0FBTyxJQUFJLENBQUN1QixhQUFhLENBQUMrQyxHQUFHLENBQUMsVUFBQVQsR0FBRyxFQUFJO1FBQUE5RCxhQUFBLEdBQUFPLENBQUE7UUFBQVAsYUFBQSxHQUFBQyxDQUFBO1FBQUEsT0FBQTZELEdBQUcsQ0FBQ3JDLE9BQU87TUFBRCxDQUFDLENBQUM7SUFDbkQ7RUFBQztJQUFBRyxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBMkMsb0JBQW9CQSxDQUFBLEVBQVk7TUFBQXhFLGFBQUEsR0FBQU8sQ0FBQTtNQUFBUCxhQUFBLEdBQUFDLENBQUE7TUFDOUIsT0FBTyxJQUFJLENBQUNvQixXQUFXO0lBQ3pCO0VBQUM7SUFBQU8sR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQTRDLG1CQUFtQkEsQ0FBQSxFQUFZO01BQUF6RSxhQUFBLEdBQUFPLENBQUE7TUFBQVAsYUFBQSxHQUFBQyxDQUFBO01BQzdCLE9BQU8sSUFBSSxDQUFDcUIsVUFBVTtJQUN4QjtFQUFDO0lBQUFNLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQUE2QyxrQkFBa0JBLENBQUEsRUFBZ0M7TUFBQTFFLGFBQUEsR0FBQU8sQ0FBQTtNQUFBUCxhQUFBLEdBQUFDLENBQUE7TUFDaEQsT0FBTztRQUNMMEUsYUFBYSxFQUFFLENBQ2IseUJBQXlCLEVBQ3pCLGtDQUFrQyxFQUNsQywrQkFBK0IsRUFDL0IsNkJBQTZCLEVBQzdCLGlDQUFpQyxDQUNsQztRQUNEQyxXQUFXLEVBQUUsQ0FDWCx5Q0FBeUMsRUFDekMsdUNBQXVDLEVBQ3ZDLGlDQUFpQyxFQUNqQyx5Q0FBeUMsRUFDekMseUNBQXlDLENBQzFDO1FBQ0RDLElBQUksRUFBRSxDQUNKLHlDQUF5QyxFQUN6QyxpQ0FBaUMsRUFDakMscUNBQXFDLEVBQ3JDLGlDQUFpQyxFQUNqQyx1Q0FBdUMsQ0FDeEM7UUFDREMsWUFBWSxFQUFFLENBQ1osb0NBQW9DLEVBQ3BDLGlDQUFpQyxFQUNqQywrQkFBK0IsRUFDL0IsaUNBQWlDLEVBQ2pDLCtCQUErQjtNQUVuQyxDQUFDO0lBQ0g7RUFBQztJQUFBbEQsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWtELHNCQUFBLEdBQUEzRSxpQkFBQSxDQUtELFdBQTRCNEUsSUFBNEQsRUFBRUMsVUFBbUIsRUFBaUI7UUFBQWpGLGFBQUEsR0FBQU8sQ0FBQTtRQUM1SCxJQUFNMkUsT0FBTyxJQUFBbEYsYUFBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSSxDQUFDeUUsa0JBQWtCLENBQUMsQ0FBQztRQUV6QyxJQUFJUyxXQUFtQjtRQUFDbkYsYUFBQSxHQUFBQyxDQUFBO1FBRXhCLElBQUlnRixVQUFVLEVBQUU7VUFBQWpGLGFBQUEsR0FBQWlDLENBQUE7VUFBQWpDLGFBQUEsR0FBQUMsQ0FBQTtVQUNka0YsV0FBVyxHQUFHRixVQUFVO1FBQzFCLENBQUMsTUFBTTtVQUFBakYsYUFBQSxHQUFBaUMsQ0FBQTtVQUNMLElBQU1tRCxjQUFjLElBQUFwRixhQUFBLEdBQUFDLENBQUEsUUFBRyxDQUFBRCxhQUFBLEdBQUFpQyxDQUFBLFdBQUFpRCxPQUFPLENBQUNGLElBQUksQ0FBQyxNQUFBaEYsYUFBQSxHQUFBaUMsQ0FBQSxXQUFJaUQsT0FBTyxDQUFDUCxhQUFhO1VBQUMzRSxhQUFBLEdBQUFDLENBQUE7VUFDOURrRixXQUFXLEdBQUdDLGNBQWMsQ0FBQ0MsSUFBSSxDQUFDQyxLQUFLLENBQUNELElBQUksQ0FBQ0UsTUFBTSxDQUFDLENBQUMsR0FBR0gsY0FBYyxDQUFDdkMsTUFBTSxDQUFDLENBQUM7UUFDakY7UUFBQzdDLGFBQUEsR0FBQUMsQ0FBQTtRQUVELE1BQU0sSUFBSSxDQUFDQyxLQUFLLENBQUNpRixXQUFXLEVBQUU7VUFDNUJsQyxRQUFRLEVBQUUsT0FBTztVQUNqQkUsSUFBSSxFQUFFLEdBQUc7VUFDVEQsS0FBSyxFQUFFO1FBQ1QsQ0FBQyxDQUFDO01BQ0osQ0FBQztNQUFBLFNBakJLc0MscUJBQXFCQSxDQUFBQyxHQUFBLEVBQUFDLEdBQUE7UUFBQSxPQUFBWCxzQkFBQSxDQUFBaEUsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFyQndFLHFCQUFxQjtJQUFBO0VBQUE7SUFBQTVELEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE4RCxrQ0FBQSxHQUFBdkYsaUJBQUEsQ0FxQjNCLGFBQW9FO1FBQUFKLGFBQUEsR0FBQU8sQ0FBQTtRQUFBUCxhQUFBLEdBQUFDLENBQUE7UUFDbEUsSUFBSTtVQUFBRCxhQUFBLEdBQUFDLENBQUE7VUFHRixPQUFPLElBQUk7UUFDYixDQUFDLENBQUMsT0FBT2lDLEtBQUssRUFBRTtVQUFBbEMsYUFBQSxHQUFBQyxDQUFBO1VBQ2QsT0FBTyxLQUFLO1FBQ2Q7TUFDRixDQUFDO01BQUEsU0FSYStCLGlDQUFpQ0EsQ0FBQTtRQUFBLE9BQUEyRCxrQ0FBQSxDQUFBNUUsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFqQ2dCLGlDQUFpQztJQUFBO0VBQUE7SUFBQUosR0FBQTtJQUFBQyxLQUFBLEVBVS9DLFNBQVFVLHdCQUF3QkEsQ0FBQSxFQUFTO01BQUEsSUFBQXFELEtBQUE7TUFBQTVGLGFBQUEsR0FBQU8sQ0FBQTtNQUV2QyxJQUFNc0YsWUFBWSxJQUFBN0YsYUFBQSxHQUFBQyxDQUFBLFFBQUcsQ0FDbkIsaUJBQWlCLEVBQ2pCLGdCQUFnQixFQUNoQixZQUFZLEVBQ1osZUFBZSxFQUNmLFlBQVksQ0FDYjtNQUFDRCxhQUFBLEdBQUFDLENBQUE7TUFFRlcsVUFBVSxDQUFDLFlBQU07UUFBQVosYUFBQSxHQUFBTyxDQUFBO1FBQUFQLGFBQUEsR0FBQUMsQ0FBQTtRQUNmLElBQUksQ0FBQUQsYUFBQSxHQUFBaUMsQ0FBQSxXQUFBMkQsS0FBSSxDQUFDdkUsV0FBVyxNQUFBckIsYUFBQSxHQUFBaUMsQ0FBQSxXQUFJMkQsS0FBSSxDQUFDckUsb0JBQW9CLENBQUNzQixNQUFNLEdBQUcsQ0FBQyxHQUFFO1VBQUE3QyxhQUFBLEdBQUFpQyxDQUFBO1VBQzVELElBQU02RCxhQUFhLElBQUE5RixhQUFBLEdBQUFDLENBQUEsUUFBRzRGLFlBQVksQ0FBQ1IsSUFBSSxDQUFDQyxLQUFLLENBQUNELElBQUksQ0FBQ0UsTUFBTSxDQUFDLENBQUMsR0FBR00sWUFBWSxDQUFDaEQsTUFBTSxDQUFDLENBQUM7VUFDbkYsSUFBTWtELE1BQThCLElBQUEvRixhQUFBLEdBQUFDLENBQUEsUUFBRztZQUNyQ3VELFVBQVUsRUFBRXNDLGFBQWE7WUFDekJFLFVBQVUsRUFBRSxJQUFJLEdBQUdYLElBQUksQ0FBQ0UsTUFBTSxDQUFDLENBQUMsR0FBRyxJQUFJO1lBQ3ZDVSxPQUFPLEVBQUU7VUFDWCxDQUFDO1VBQUNqRyxhQUFBLEdBQUFDLENBQUE7VUFFRjJGLEtBQUksQ0FBQ3JFLG9CQUFvQixDQUFDMkUsT0FBTyxDQUFDLFVBQUE3RCxRQUFRLEVBQUk7WUFBQXJDLGFBQUEsR0FBQU8sQ0FBQTtZQUFBUCxhQUFBLEdBQUFDLENBQUE7WUFBQSxPQUFBb0MsUUFBUSxDQUFDMEQsTUFBTSxDQUFDO1VBQUQsQ0FBQyxDQUFDO1FBQ2pFLENBQUM7VUFBQS9GLGFBQUEsR0FBQWlDLENBQUE7UUFBQTtNQUNILENBQUMsRUFBRSxJQUFJLENBQUM7SUFDVjtFQUFDO0lBQUFMLEdBQUE7SUFBQUMsS0FBQSxFQUVELFNBQVFzQyx3QkFBd0JBLENBQUNYLFVBQWtCLEVBQUUvQixPQUFxQixFQUFPO01BQUF6QixhQUFBLEdBQUFPLENBQUE7TUFFL0UsSUFBTTJELFVBQWUsSUFBQWxFLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLENBQUMsQ0FBQztNQUFDRCxhQUFBLEdBQUFDLENBQUE7TUFFM0IsUUFBUXdCLE9BQU8sQ0FBQ0MsTUFBTTtRQUNwQixLQUFLLGVBQWU7VUFBQTFCLGFBQUEsR0FBQWlDLENBQUE7UUFDcEIsS0FBSyxrQkFBa0I7VUFBQWpDLGFBQUEsR0FBQWlDLENBQUE7UUFDdkIsS0FBSyxrQkFBa0I7VUFBQWpDLGFBQUEsR0FBQWlDLENBQUE7VUFBQWpDLGFBQUEsR0FBQUMsQ0FBQTtVQUVyQmlFLFVBQVUsQ0FBQ2lDLFFBQVEsR0FBRzFFLE9BQU8sQ0FBQ0MsTUFBTSxDQUFDMEUsS0FBSyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDMUMsV0FBVyxDQUFDLENBQUM7VUFBQzFELGFBQUEsR0FBQUMsQ0FBQTtVQUNqRTtRQUVGLEtBQUssaUJBQWlCO1VBQUFELGFBQUEsR0FBQWlDLENBQUE7VUFFcEIsSUFBTW9FLGFBQWEsSUFBQXJHLGFBQUEsR0FBQUMsQ0FBQSxRQUFHdUQsVUFBVSxDQUFDOEMsS0FBSyxDQUFDLDhCQUE4QixDQUFDO1VBQUN0RyxhQUFBLEdBQUFDLENBQUE7VUFDdkUsSUFBSW9HLGFBQWEsRUFBRTtZQUFBckcsYUFBQSxHQUFBaUMsQ0FBQTtZQUNqQixJQUFNSixLQUFLLElBQUE3QixhQUFBLEdBQUFDLENBQUEsUUFBR3NHLFFBQVEsQ0FBQ0YsYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ3hDLElBQU1HLElBQUksSUFBQXhHLGFBQUEsR0FBQUMsQ0FBQSxRQUFHb0csYUFBYSxDQUFDLENBQUMsQ0FBQyxDQUFDM0MsV0FBVyxDQUFDLENBQUM7WUFBQzFELGFBQUEsR0FBQUMsQ0FBQTtZQUM1Q2lFLFVBQVUsQ0FBQ3VDLFFBQVEsR0FBR0QsSUFBSSxDQUFDRSxVQUFVLENBQUMsS0FBSyxDQUFDLElBQUExRyxhQUFBLEdBQUFpQyxDQUFBLFdBQUdKLEtBQUssR0FBRyxFQUFFLEtBQUE3QixhQUFBLEdBQUFpQyxDQUFBLFdBQUdKLEtBQUs7VUFDbkUsQ0FBQztZQUFBN0IsYUFBQSxHQUFBaUMsQ0FBQTtVQUFBO1VBQUFqQyxhQUFBLEdBQUFDLENBQUE7VUFDRDtNQUNKO01BQUNELGFBQUEsR0FBQUMsQ0FBQTtNQUVELE9BQU9pRSxVQUFVO0lBQ25CO0VBQUM7SUFBQXRDLEdBQUE7SUFBQUMsS0FBQSxFQUVELFNBQVF3QyxnQkFBZ0JBLENBQUNiLFVBQWtCLEVBQXVCO01BQUF4RCxhQUFBLEdBQUFPLENBQUE7TUFBQVAsYUFBQSxHQUFBQyxDQUFBO01BRWhFLEtBQUssSUFBTXdCLE9BQU8sSUFBSSxJQUFJLENBQUNELGFBQWEsRUFBRTtRQUN4QyxJQUFNbUYsWUFBWSxJQUFBM0csYUFBQSxHQUFBQyxDQUFBLFFBQUd3QixPQUFPLENBQUNBLE9BQU8sQ0FBQzJFLEtBQUssQ0FBQyxHQUFHLENBQUM7UUFDL0MsSUFBTVEsZUFBZSxJQUFBNUcsYUFBQSxHQUFBQyxDQUFBLFFBQUd1RCxVQUFVLENBQUM0QyxLQUFLLENBQUMsR0FBRyxDQUFDO1FBRTdDLElBQUlTLFVBQVUsSUFBQTdHLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLENBQUM7UUFBQ0QsYUFBQSxHQUFBQyxDQUFBO1FBQUEsSUFBQTZHLEtBQUEsWUFBQUEsTUFBQUMsSUFBQSxFQUNjO1VBQUEvRyxhQUFBLEdBQUFDLENBQUE7VUFDL0IsSUFBSTJHLGVBQWUsQ0FBQ0ksSUFBSSxDQUFDLFVBQUFDLEVBQUUsRUFBSTtZQUFBakgsYUFBQSxHQUFBTyxDQUFBO1lBQUFQLGFBQUEsR0FBQUMsQ0FBQTtZQUFBLFFBQUFELGFBQUEsR0FBQWlDLENBQUEsV0FBQWdGLEVBQUUsQ0FBQ2xELFFBQVEsQ0FBQ2dELElBQUksQ0FBQyxNQUFBL0csYUFBQSxHQUFBaUMsQ0FBQSxXQUFJOEUsSUFBSSxDQUFDaEQsUUFBUSxDQUFDa0QsRUFBRSxDQUFDO1VBQUQsQ0FBQyxDQUFDLEVBQUU7WUFBQWpILGFBQUEsR0FBQWlDLENBQUE7WUFBQWpDLGFBQUEsR0FBQUMsQ0FBQTtZQUN0RTRHLFVBQVUsRUFBRTtVQUNkLENBQUM7WUFBQTdHLGFBQUEsR0FBQWlDLENBQUE7VUFBQTtRQUNILENBQUM7UUFKRCxLQUFLLElBQU04RSxJQUFJLElBQUlKLFlBQVk7VUFBQUcsS0FBQSxDQUFBQyxJQUFBO1FBQUE7UUFJOUIvRyxhQUFBLEdBQUFDLENBQUE7UUFHRCxJQUFJNEcsVUFBVSxJQUFJeEIsSUFBSSxDQUFDNkIsSUFBSSxDQUFDUCxZQUFZLENBQUM5RCxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUU7VUFBQTdDLGFBQUEsR0FBQWlDLENBQUE7VUFBQWpDLGFBQUEsR0FBQUMsQ0FBQTtVQUNwRCxPQUFPd0IsT0FBTztRQUNoQixDQUFDO1VBQUF6QixhQUFBLEdBQUFpQyxDQUFBO1FBQUE7TUFDSDtNQUFDakMsYUFBQSxHQUFBQyxDQUFBO01BRUQsT0FBTyxJQUFJO0lBQ2I7RUFBQztBQUFBO0FBR0gsT0FBTyxJQUFNa0gsWUFBWSxJQUFBbkgsYUFBQSxHQUFBQyxDQUFBLFNBQUcsSUFBSWtCLFlBQVksQ0FBQyxDQUFDIiwiaWdub3JlTGlzdCI6W119