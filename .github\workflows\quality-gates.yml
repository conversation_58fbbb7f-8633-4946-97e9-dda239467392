name: 🛡️ Quality Gates

on:
  pull_request:
    branches: [ main, develop ]
    types: [opened, synchronize, reopened]
  push:
    branches: [ main, develop ]
  schedule:
    # Run quality checks daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  NODE_VERSION: '18'

jobs:
  # Code Quality Analysis
  code-quality:
    name: 📊 Code Quality Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 20
    
    outputs:
      quality_score: ${{ steps.quality.outputs.score }}
      coverage_percentage: ${{ steps.coverage.outputs.percentage }}
      technical_debt: ${{ steps.debt.outputs.minutes }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 ESLint analysis
        run: |
          npm run lint -- --format=json --output-file=eslint-report.json
          npm run lint -- --format=stylish
        continue-on-error: true

      - name: 🎨 Prettier check
        run: npm run format:check
        continue-on-error: true

      - name: 📝 TypeScript strict check
        run: npm run type-check:strict
        continue-on-error: true

      - name: 🧪 Run tests with coverage
        run: npm run test:coverage
        env:
          CI: true

      - name: 📊 Calculate quality metrics
        id: quality
        run: |
          # Calculate ESLint score
          ESLINT_ERRORS=$(jq '[.[] | select(.errorCount > 0)] | length' eslint-report.json || echo "0")
          ESLINT_WARNINGS=$(jq '[.[] | select(.warningCount > 0)] | length' eslint-report.json || echo "0")
          
          # Calculate quality score (0-100)
          QUALITY_SCORE=$((100 - (ESLINT_ERRORS * 10) - (ESLINT_WARNINGS * 2)))
          QUALITY_SCORE=$((QUALITY_SCORE < 0 ? 0 : QUALITY_SCORE))
          
          echo "score=$QUALITY_SCORE" >> $GITHUB_OUTPUT
          echo "📊 Quality Score: $QUALITY_SCORE/100"

      - name: 📈 Extract coverage
        id: coverage
        run: |
          if [ -f "coverage/coverage-summary.json" ]; then
            COVERAGE=$(jq '.total.lines.pct' coverage/coverage-summary.json)
            echo "percentage=$COVERAGE" >> $GITHUB_OUTPUT
            echo "📈 Coverage: $COVERAGE%"
          else
            echo "percentage=0" >> $GITHUB_OUTPUT
            echo "📈 Coverage: No data"
          fi

      - name: 🔧 Technical debt analysis
        id: debt
        run: |
          # Analyze technical debt using ESLint complexity rules
          COMPLEXITY=$(jq '[.[] | .messages[] | select(.ruleId == "complexity")] | length' eslint-report.json || echo "0")
          DEBT_MINUTES=$((COMPLEXITY * 5))
          
          echo "minutes=$DEBT_MINUTES" >> $GITHUB_OUTPUT
          echo "🔧 Technical Debt: $DEBT_MINUTES minutes"

      - name: 📤 Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: quality-gates
          name: quality-gates-coverage

      - name: 📊 SonarCloud analysis
        uses: SonarSource/sonarcloud-github-action@master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

  # Security Analysis
  security-analysis:
    name: 🔒 Security Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      security_score: ${{ steps.security.outputs.score }}
      vulnerabilities: ${{ steps.security.outputs.vulnerabilities }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 npm audit
        id: audit
        run: |
          npm audit --audit-level=low --json > audit-report.json || true
          
          # Count vulnerabilities by severity
          HIGH=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "high")) | length' audit-report.json || echo "0")
          MODERATE=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "moderate")) | length' audit-report.json || echo "0")
          LOW=$(jq '.vulnerabilities | to_entries | map(select(.value.severity == "low")) | length' audit-report.json || echo "0")
          
          echo "High: $HIGH, Moderate: $MODERATE, Low: $LOW"
          echo "high_vulns=$HIGH" >> $GITHUB_OUTPUT
          echo "moderate_vulns=$MODERATE" >> $GITHUB_OUTPUT
          echo "low_vulns=$LOW" >> $GITHUB_OUTPUT

      - name: 🛡️ Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --json --file=package.json > snyk-report.json
        continue-on-error: true

      - name: 🔐 CodeQL analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript

      - name: 🔍 Perform CodeQL analysis
        uses: github/codeql-action/analyze@v2

      - name: 📊 Calculate security score
        id: security
        run: |
          HIGH_VULNS="${{ steps.audit.outputs.high_vulns }}"
          MODERATE_VULNS="${{ steps.audit.outputs.moderate_vulns }}"
          LOW_VULNS="${{ steps.audit.outputs.low_vulns }}"
          
          # Calculate security score (0-100)
          SECURITY_SCORE=$((100 - (HIGH_VULNS * 20) - (MODERATE_VULNS * 5) - (LOW_VULNS * 1)))
          SECURITY_SCORE=$((SECURITY_SCORE < 0 ? 0 : SECURITY_SCORE))
          
          TOTAL_VULNS=$((HIGH_VULNS + MODERATE_VULNS + LOW_VULNS))
          
          echo "score=$SECURITY_SCORE" >> $GITHUB_OUTPUT
          echo "vulnerabilities=$TOTAL_VULNS" >> $GITHUB_OUTPUT
          echo "🔒 Security Score: $SECURITY_SCORE/100"
          echo "🚨 Total Vulnerabilities: $TOTAL_VULNS"

  # Performance Analysis
  performance-analysis:
    name: ⚡ Performance Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 25
    
    outputs:
      performance_score: ${{ steps.performance.outputs.score }}
      bundle_size: ${{ steps.bundle.outputs.size }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔨 Build for performance analysis
        run: npm run build:web:prod

      - name: 📦 Bundle size analysis
        id: bundle
        run: |
          # Calculate bundle size
          BUNDLE_SIZE=$(du -sh dist/ | cut -f1)
          BUNDLE_SIZE_BYTES=$(du -sb dist/ | cut -f1)
          
          echo "size=$BUNDLE_SIZE" >> $GITHUB_OUTPUT
          echo "size_bytes=$BUNDLE_SIZE_BYTES" >> $GITHUB_OUTPUT
          echo "📦 Bundle Size: $BUNDLE_SIZE"

      - name: 🚀 Start test server
        run: |
          npm install -g serve
          serve -s dist -l 3000 &
          sleep 10

      - name: ⚡ Lighthouse performance audit
        id: lighthouse
        run: |
          npm install -g lighthouse
          
          lighthouse http://localhost:3000 \
            --chrome-flags="--headless --no-sandbox" \
            --output=json \
            --output-path=lighthouse-report.json \
            --quiet
          
          # Extract scores
          PERFORMANCE=$(jq '.categories.performance.score * 100' lighthouse-report.json)
          ACCESSIBILITY=$(jq '.categories.accessibility.score * 100' lighthouse-report.json)
          BEST_PRACTICES=$(jq '.categories["best-practices"].score * 100' lighthouse-report.json)
          SEO=$(jq '.categories.seo.score * 100' lighthouse-report.json)
          
          echo "performance=$PERFORMANCE" >> $GITHUB_OUTPUT
          echo "accessibility=$ACCESSIBILITY" >> $GITHUB_OUTPUT
          echo "best_practices=$BEST_PRACTICES" >> $GITHUB_OUTPUT
          echo "seo=$SEO" >> $GITHUB_OUTPUT

      - name: 📊 Calculate performance score
        id: performance
        run: |
          PERF_SCORE="${{ steps.lighthouse.outputs.performance }}"
          BUNDLE_SIZE_MB=$(echo "scale=2; ${{ steps.bundle.outputs.size_bytes }} / 1024 / 1024" | bc)
          
          # Penalize large bundle sizes
          if (( $(echo "$BUNDLE_SIZE_MB > 5" | bc -l) )); then
            PERF_SCORE=$(echo "scale=0; $PERF_SCORE - 10" | bc)
          elif (( $(echo "$BUNDLE_SIZE_MB > 3" | bc -l) )); then
            PERF_SCORE=$(echo "scale=0; $PERF_SCORE - 5" | bc)
          fi
          
          PERF_SCORE=$(echo "scale=0; if ($PERF_SCORE < 0) 0 else $PERF_SCORE" | bc)
          
          echo "score=$PERF_SCORE" >> $GITHUB_OUTPUT
          echo "⚡ Performance Score: $PERF_SCORE/100"

      - name: 📤 Upload Lighthouse report
        uses: actions/upload-artifact@v3
        with:
          name: lighthouse-report
          path: lighthouse-report.json
          retention-days: 30

  # Accessibility Analysis
  accessibility-analysis:
    name: ♿ Accessibility Analysis
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      accessibility_score: ${{ steps.a11y.outputs.score }}
      violations: ${{ steps.a11y.outputs.violations }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔨 Build application
        run: npm run build:web

      - name: 🚀 Start test server
        run: |
          npm install -g serve
          serve -s dist -l 3000 &
          sleep 10

      - name: ♿ Run accessibility tests
        id: a11y
        run: |
          npm install -g @axe-core/cli
          
          # Run axe accessibility tests
          axe http://localhost:3000 \
            --format=json \
            --output=axe-report.json \
            --exit || true
          
          # Count violations
          VIOLATIONS=$(jq '.violations | length' axe-report.json || echo "0")
          CRITICAL=$(jq '[.violations[] | select(.impact == "critical")] | length' axe-report.json || echo "0")
          SERIOUS=$(jq '[.violations[] | select(.impact == "serious")] | length' axe-report.json || echo "0")
          
          # Calculate accessibility score
          A11Y_SCORE=$((100 - (CRITICAL * 15) - (SERIOUS * 5) - (VIOLATIONS * 1)))
          A11Y_SCORE=$((A11Y_SCORE < 0 ? 0 : A11Y_SCORE))
          
          echo "score=$A11Y_SCORE" >> $GITHUB_OUTPUT
          echo "violations=$VIOLATIONS" >> $GITHUB_OUTPUT
          echo "♿ Accessibility Score: $A11Y_SCORE/100"
          echo "🚨 Violations: $VIOLATIONS"

      - name: 📤 Upload accessibility report
        uses: actions/upload-artifact@v3
        with:
          name: accessibility-report
          path: axe-report.json
          retention-days: 30

  # Quality Gate Evaluation
  quality-gate-evaluation:
    name: 🚪 Quality Gate Evaluation
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [code-quality, security-analysis, performance-analysis, accessibility-analysis]
    
    outputs:
      gate_passed: ${{ steps.evaluation.outputs.passed }}
      overall_score: ${{ steps.evaluation.outputs.score }}
    
    steps:
      - name: 📊 Evaluate quality gates
        id: evaluation
        run: |
          # Extract scores
          QUALITY_SCORE="${{ needs.code-quality.outputs.quality_score }}"
          SECURITY_SCORE="${{ needs.security-analysis.outputs.security_score }}"
          PERFORMANCE_SCORE="${{ needs.performance-analysis.outputs.performance_score }}"
          ACCESSIBILITY_SCORE="${{ needs.accessibility-analysis.outputs.accessibility_score }}"
          
          # Calculate overall score (weighted average)
          OVERALL_SCORE=$(echo "scale=0; ($QUALITY_SCORE * 0.3 + $SECURITY_SCORE * 0.3 + $PERFORMANCE_SCORE * 0.25 + $ACCESSIBILITY_SCORE * 0.15)" | bc)
          
          echo "score=$OVERALL_SCORE" >> $GITHUB_OUTPUT
          
          # Define quality gates
          QUALITY_GATE=75
          SECURITY_GATE=80
          PERFORMANCE_GATE=70
          ACCESSIBILITY_GATE=85
          OVERALL_GATE=75
          
          # Check if all gates pass
          GATES_PASSED=true
          
          if (( $(echo "$QUALITY_SCORE < $QUALITY_GATE" | bc -l) )); then
            echo "❌ Quality gate failed: $QUALITY_SCORE < $QUALITY_GATE"
            GATES_PASSED=false
          fi
          
          if (( $(echo "$SECURITY_SCORE < $SECURITY_GATE" | bc -l) )); then
            echo "❌ Security gate failed: $SECURITY_SCORE < $SECURITY_GATE"
            GATES_PASSED=false
          fi
          
          if (( $(echo "$PERFORMANCE_SCORE < $PERFORMANCE_GATE" | bc -l) )); then
            echo "❌ Performance gate failed: $PERFORMANCE_SCORE < $PERFORMANCE_GATE"
            GATES_PASSED=false
          fi
          
          if (( $(echo "$ACCESSIBILITY_SCORE < $ACCESSIBILITY_GATE" | bc -l) )); then
            echo "❌ Accessibility gate failed: $ACCESSIBILITY_SCORE < $ACCESSIBILITY_GATE"
            GATES_PASSED=false
          fi
          
          if (( $(echo "$OVERALL_SCORE < $OVERALL_GATE" | bc -l) )); then
            echo "❌ Overall gate failed: $OVERALL_SCORE < $OVERALL_GATE"
            GATES_PASSED=false
          fi
          
          echo "passed=$GATES_PASSED" >> $GITHUB_OUTPUT
          
          if [ "$GATES_PASSED" = "true" ]; then
            echo "✅ All quality gates passed!"
          else
            echo "❌ Quality gates failed!"
          fi

      - name: 📊 Generate quality report
        run: |
          echo "## 🛡️ Quality Gates Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Metric | Score | Threshold | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|--------|-------|-----------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Code Quality | ${{ needs.code-quality.outputs.quality_score }}/100 | 75 | ${{ needs.code-quality.outputs.quality_score >= 75 && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security | ${{ needs.security-analysis.outputs.security_score }}/100 | 80 | ${{ needs.security-analysis.outputs.security_score >= 80 && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Performance | ${{ needs.performance-analysis.outputs.performance_score }}/100 | 70 | ${{ needs.performance-analysis.outputs.performance_score >= 70 && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Accessibility | ${{ needs.accessibility-analysis.outputs.accessibility_score }}/100 | 85 | ${{ needs.accessibility-analysis.outputs.accessibility_score >= 85 && '✅' || '❌' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| **Overall** | **${{ steps.evaluation.outputs.score }}/100** | **75** | **${{ steps.evaluation.outputs.passed == 'true' && '✅' || '❌' }}** |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📈 Additional Metrics" >> $GITHUB_STEP_SUMMARY
          echo "- 📊 Test Coverage: ${{ needs.code-quality.outputs.coverage_percentage }}%" >> $GITHUB_STEP_SUMMARY
          echo "- 🔧 Technical Debt: ${{ needs.code-quality.outputs.technical_debt }} minutes" >> $GITHUB_STEP_SUMMARY
          echo "- 🚨 Security Vulnerabilities: ${{ needs.security-analysis.outputs.vulnerabilities }}" >> $GITHUB_STEP_SUMMARY
          echo "- 📦 Bundle Size: ${{ needs.performance-analysis.outputs.bundle_size }}" >> $GITHUB_STEP_SUMMARY
          echo "- ♿ Accessibility Violations: ${{ needs.accessibility-analysis.outputs.violations }}" >> $GITHUB_STEP_SUMMARY

      - name: 🚨 Fail if quality gates don't pass
        if: steps.evaluation.outputs.passed != 'true'
        run: |
          echo "❌ Quality gates failed. Please fix the issues before merging."
          exit 1

  # Update PR status
  update-pr-status:
    name: 📝 Update PR Status
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: quality-gate-evaluation
    if: github.event_name == 'pull_request'
    
    steps:
      - name: 📝 Update PR status
        uses: actions/github-script@v7
        with:
          script: |
            const { owner, repo } = context.repo;
            const pull_number = context.payload.pull_request.number;
            const sha = context.payload.pull_request.head.sha;
            
            const passed = '${{ needs.quality-gate-evaluation.outputs.gate_passed }}' === 'true';
            const score = '${{ needs.quality-gate-evaluation.outputs.overall_score }}';
            
            const state = passed ? 'success' : 'failure';
            const description = passed 
              ? `✅ Quality gates passed (${score}/100)` 
              : `❌ Quality gates failed (${score}/100)`;
            
            await github.rest.repos.createCommitStatus({
              owner,
              repo,
              sha,
              state,
              target_url: `${context.payload.repository.html_url}/actions/runs/${context.runId}`,
              description,
              context: 'quality-gates'
            });
            
            // Add comment to PR
            const comment = passed 
              ? `🎉 Quality gates passed! Overall score: ${score}/100`
              : `❌ Quality gates failed. Overall score: ${score}/100. Please check the quality report for details.`;
            
            await github.rest.issues.createComment({
              owner,
              repo,
              issue_number: pull_number,
              body: comment
            });
