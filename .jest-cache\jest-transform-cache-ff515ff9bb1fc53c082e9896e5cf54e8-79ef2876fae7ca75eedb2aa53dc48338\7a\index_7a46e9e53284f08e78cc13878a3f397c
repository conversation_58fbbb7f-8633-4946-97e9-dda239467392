90dd5438854cb125622afd4d7526c8fe
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _createForOfIteratorHelperLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/createForOfIteratorHelperLoose"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _View = _interopRequireDefault(require("../../../exports/View"));
var _VirtualizedList = _interopRequireDefault(require("../VirtualizedList"));
var _VirtualizeUtils = require("../VirtualizeUtils");
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var React = _interopRequireWildcard(require("react"));
var _excluded = ["ItemSeparatorComponent", "SectionSeparatorComponent", "renderItem", "renderSectionFooter", "renderSectionHeader", "sections", "stickySectionHeadersEnabled"];
var VirtualizedSectionList = function (_React$PureComponent) {
  function VirtualizedSectionList() {
    var _this;
    (0, _classCallCheck2.default)(this, VirtualizedSectionList);
    _this = _callSuper(this, VirtualizedSectionList, arguments);
    _this._keyExtractor = function (item, index) {
      var info = _this._subExtractor(index);
      return info && info.key || String(index);
    };
    _this._convertViewable = function (viewable) {
      var _info$index;
      (0, _invariant.default)(viewable.index != null, 'Received a broken ViewToken');
      var info = _this._subExtractor(viewable.index);
      if (!info) {
        return null;
      }
      var keyExtractorWithNullableIndex = info.section.keyExtractor;
      var keyExtractorWithNonNullableIndex = _this.props.keyExtractor || _VirtualizeUtils.keyExtractor;
      var key = keyExtractorWithNullableIndex != null ? keyExtractorWithNullableIndex(viewable.item, info.index) : keyExtractorWithNonNullableIndex(viewable.item, (_info$index = info.index) !== null && _info$index !== void 0 ? _info$index : 0);
      return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, viewable), {}, {
        index: info.index,
        key: key,
        section: info.section
      });
    };
    _this._onViewableItemsChanged = function (_ref) {
      var viewableItems = _ref.viewableItems,
        changed = _ref.changed;
      var onViewableItemsChanged = _this.props.onViewableItemsChanged;
      if (onViewableItemsChanged != null) {
        onViewableItemsChanged({
          viewableItems: viewableItems.map(_this._convertViewable, _this).filter(Boolean),
          changed: changed.map(_this._convertViewable, _this).filter(Boolean)
        });
      }
    };
    _this._renderItem = function (listItemCount) {
      return (function (_ref2) {
          var item = _ref2.item,
            index = _ref2.index;
          var info = _this._subExtractor(index);
          if (!info) {
            return null;
          }
          var infoIndex = info.index;
          if (infoIndex == null) {
            var section = info.section;
            if (info.header === true) {
              var renderSectionHeader = _this.props.renderSectionHeader;
              return renderSectionHeader ? renderSectionHeader({
                section: section
              }) : null;
            } else {
              var renderSectionFooter = _this.props.renderSectionFooter;
              return renderSectionFooter ? renderSectionFooter({
                section: section
              }) : null;
            }
          } else {
            var renderItem = info.section.renderItem || _this.props.renderItem;
            var SeparatorComponent = _this._getSeparatorComponent(index, info, listItemCount);
            (0, _invariant.default)(renderItem, 'no renderItem!');
            return React.createElement(ItemWithSeparator, {
              SeparatorComponent: SeparatorComponent,
              LeadingSeparatorComponent: infoIndex === 0 ? _this.props.SectionSeparatorComponent : undefined,
              cellKey: info.key,
              index: infoIndex,
              item: item,
              leadingItem: info.leadingItem,
              leadingSection: info.leadingSection,
              prevCellKey: (_this._subExtractor(index - 1) || {}).key,
              setSelfHighlightCallback: _this._setUpdateHighlightFor,
              setSelfUpdatePropsCallback: _this._setUpdatePropsFor,
              updateHighlightFor: _this._updateHighlightFor,
              updatePropsFor: _this._updatePropsFor,
              renderItem: renderItem,
              section: info.section,
              trailingItem: info.trailingItem,
              trailingSection: info.trailingSection,
              inverted: !!_this.props.inverted
            });
          }
        }
      );
    };
    _this._updatePropsFor = function (cellKey, value) {
      var updateProps = _this._updatePropsMap[cellKey];
      if (updateProps != null) {
        updateProps(value);
      }
    };
    _this._updateHighlightFor = function (cellKey, value) {
      var updateHighlight = _this._updateHighlightMap[cellKey];
      if (updateHighlight != null) {
        updateHighlight(value);
      }
    };
    _this._setUpdateHighlightFor = function (cellKey, updateHighlightFn) {
      if (updateHighlightFn != null) {
        _this._updateHighlightMap[cellKey] = updateHighlightFn;
      } else {
        delete _this._updateHighlightFor[cellKey];
      }
    };
    _this._setUpdatePropsFor = function (cellKey, updatePropsFn) {
      if (updatePropsFn != null) {
        _this._updatePropsMap[cellKey] = updatePropsFn;
      } else {
        delete _this._updatePropsMap[cellKey];
      }
    };
    _this._updateHighlightMap = {};
    _this._updatePropsMap = {};
    _this._captureRef = function (ref) {
      _this._listRef = ref;
    };
    return _this;
  }
  (0, _inherits2.default)(VirtualizedSectionList, _React$PureComponent);
  return (0, _createClass2.default)(VirtualizedSectionList, [{
    key: "scrollToLocation",
    value: function scrollToLocation(params) {
      var index = params.itemIndex;
      for (var i = 0; i < params.sectionIndex; i++) {
        index += this.props.getItemCount(this.props.sections[i].data) + 2;
      }
      var viewOffset = params.viewOffset || 0;
      if (this._listRef == null) {
        return;
      }
      if (params.itemIndex > 0 && this.props.stickySectionHeadersEnabled) {
        var frame = this._listRef.__getFrameMetricsApprox(index - params.itemIndex, this._listRef.props);
        viewOffset += frame.length;
      }
      var toIndexParams = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, params), {}, {
        viewOffset: viewOffset,
        index: index
      });
      this._listRef.scrollToIndex(toIndexParams);
    }
  }, {
    key: "getListRef",
    value: function getListRef() {
      return this._listRef;
    }
  }, {
    key: "render",
    value: function render() {
      var _this2 = this;
      var _this$props = this.props,
        ItemSeparatorComponent = _this$props.ItemSeparatorComponent,
        SectionSeparatorComponent = _this$props.SectionSeparatorComponent,
        _renderItem = _this$props.renderItem,
        renderSectionFooter = _this$props.renderSectionFooter,
        renderSectionHeader = _this$props.renderSectionHeader,
        _sections = _this$props.sections,
        stickySectionHeadersEnabled = _this$props.stickySectionHeadersEnabled,
        passThroughProps = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);
      var listHeaderOffset = this.props.ListHeaderComponent ? 1 : 0;
      var stickyHeaderIndices = this.props.stickySectionHeadersEnabled ? [] : undefined;
      var itemCount = 0;
      for (var _iterator = (0, _createForOfIteratorHelperLoose2.default)(this.props.sections), _step; !(_step = _iterator()).done;) {
        var section = _step.value;
        if (stickyHeaderIndices != null) {
          stickyHeaderIndices.push(itemCount + listHeaderOffset);
        }
        itemCount += 2;
        itemCount += this.props.getItemCount(section.data);
      }
      var renderItem = this._renderItem(itemCount);
      return React.createElement(_VirtualizedList.default, (0, _extends2.default)({}, passThroughProps, {
        keyExtractor: this._keyExtractor,
        stickyHeaderIndices: stickyHeaderIndices,
        renderItem: renderItem,
        data: this.props.sections,
        getItem: function getItem(sections, index) {
          return _this2._getItem(_this2.props, sections, index);
        },
        getItemCount: function getItemCount() {
          return itemCount;
        },
        onViewableItemsChanged: this.props.onViewableItemsChanged ? this._onViewableItemsChanged : undefined,
        ref: this._captureRef
      }));
    }
  }, {
    key: "_getItem",
    value: function _getItem(props, sections, index) {
      if (!sections) {
        return null;
      }
      var itemIdx = index - 1;
      for (var i = 0; i < sections.length; i++) {
        var section = sections[i];
        var sectionData = section.data;
        var itemCount = props.getItemCount(sectionData);
        if (itemIdx === -1 || itemIdx === itemCount) {
          return section;
        } else if (itemIdx < itemCount) {
          return props.getItem(sectionData, itemIdx);
        } else {
          itemIdx -= itemCount + 2;
        }
      }
      return null;
    }
  }, {
    key: "_subExtractor",
    value: function _subExtractor(index) {
      var itemIndex = index;
      var _this$props2 = this.props,
        getItem = _this$props2.getItem,
        getItemCount = _this$props2.getItemCount,
        keyExtractor = _this$props2.keyExtractor,
        sections = _this$props2.sections;
      for (var i = 0; i < sections.length; i++) {
        var section = sections[i];
        var sectionData = section.data;
        var key = section.key || String(i);
        itemIndex -= 1;
        if (itemIndex >= getItemCount(sectionData) + 1) {
          itemIndex -= getItemCount(sectionData) + 1;
        } else if (itemIndex === -1) {
          return {
            section: section,
            key: key + ':header',
            index: null,
            header: true,
            trailingSection: sections[i + 1]
          };
        } else if (itemIndex === getItemCount(sectionData)) {
          return {
            section: section,
            key: key + ':footer',
            index: null,
            header: false,
            trailingSection: sections[i + 1]
          };
        } else {
          var extractor = section.keyExtractor || keyExtractor || _VirtualizeUtils.keyExtractor;
          return {
            section: section,
            key: key + ':' + extractor(getItem(sectionData, itemIndex), itemIndex),
            index: itemIndex,
            leadingItem: getItem(sectionData, itemIndex - 1),
            leadingSection: sections[i - 1],
            trailingItem: getItem(sectionData, itemIndex + 1),
            trailingSection: sections[i + 1]
          };
        }
      }
    }
  }, {
    key: "_getSeparatorComponent",
    value: function _getSeparatorComponent(index, info, listItemCount) {
      info = info || this._subExtractor(index);
      if (!info) {
        return null;
      }
      var ItemSeparatorComponent = info.section.ItemSeparatorComponent || this.props.ItemSeparatorComponent;
      var SectionSeparatorComponent = this.props.SectionSeparatorComponent;
      var isLastItemInList = index === listItemCount - 1;
      var isLastItemInSection = info.index === this.props.getItemCount(info.section.data) - 1;
      if (SectionSeparatorComponent && isLastItemInSection) {
        return SectionSeparatorComponent;
      }
      if (ItemSeparatorComponent && !isLastItemInSection && !isLastItemInList) {
        return ItemSeparatorComponent;
      }
      return null;
    }
  }]);
}(React.PureComponent);
function ItemWithSeparator(props) {
  var LeadingSeparatorComponent = props.LeadingSeparatorComponent,
    SeparatorComponent = props.SeparatorComponent,
    cellKey = props.cellKey,
    prevCellKey = props.prevCellKey,
    setSelfHighlightCallback = props.setSelfHighlightCallback,
    updateHighlightFor = props.updateHighlightFor,
    setSelfUpdatePropsCallback = props.setSelfUpdatePropsCallback,
    updatePropsFor = props.updatePropsFor,
    item = props.item,
    index = props.index,
    section = props.section,
    inverted = props.inverted;
  var _React$useState = React.useState(false),
    leadingSeparatorHiglighted = _React$useState[0],
    setLeadingSeparatorHighlighted = _React$useState[1];
  var _React$useState2 = React.useState(false),
    separatorHighlighted = _React$useState2[0],
    setSeparatorHighlighted = _React$useState2[1];
  var _React$useState3 = React.useState({
      leadingItem: props.leadingItem,
      leadingSection: props.leadingSection,
      section: props.section,
      trailingItem: props.item,
      trailingSection: props.trailingSection
    }),
    leadingSeparatorProps = _React$useState3[0],
    setLeadingSeparatorProps = _React$useState3[1];
  var _React$useState4 = React.useState({
      leadingItem: props.item,
      leadingSection: props.leadingSection,
      section: props.section,
      trailingItem: props.trailingItem,
      trailingSection: props.trailingSection
    }),
    separatorProps = _React$useState4[0],
    setSeparatorProps = _React$useState4[1];
  React.useEffect(function () {
    setSelfHighlightCallback(cellKey, setSeparatorHighlighted);
    setSelfUpdatePropsCallback(cellKey, setSeparatorProps);
    return function () {
      setSelfUpdatePropsCallback(cellKey, null);
      setSelfHighlightCallback(cellKey, null);
    };
  }, [cellKey, setSelfHighlightCallback, setSeparatorProps, setSelfUpdatePropsCallback]);
  var separators = {
    highlight: function highlight() {
      setLeadingSeparatorHighlighted(true);
      setSeparatorHighlighted(true);
      if (prevCellKey != null) {
        updateHighlightFor(prevCellKey, true);
      }
    },
    unhighlight: function unhighlight() {
      setLeadingSeparatorHighlighted(false);
      setSeparatorHighlighted(false);
      if (prevCellKey != null) {
        updateHighlightFor(prevCellKey, false);
      }
    },
    updateProps: function updateProps(select, newProps) {
      if (select === 'leading') {
        if (LeadingSeparatorComponent != null) {
          setLeadingSeparatorProps((0, _objectSpread2.default)((0, _objectSpread2.default)({}, leadingSeparatorProps), newProps));
        } else if (prevCellKey != null) {
          updatePropsFor(prevCellKey, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, leadingSeparatorProps), newProps));
        }
      } else if (select === 'trailing' && SeparatorComponent != null) {
        setSeparatorProps((0, _objectSpread2.default)((0, _objectSpread2.default)({}, separatorProps), newProps));
      }
    }
  };
  var element = props.renderItem({
    item: item,
    index: index,
    section: section,
    separators: separators
  });
  var leadingSeparator = LeadingSeparatorComponent != null && React.createElement(LeadingSeparatorComponent, (0, _extends2.default)({
    highlighted: leadingSeparatorHiglighted
  }, leadingSeparatorProps));
  var separator = SeparatorComponent != null && React.createElement(SeparatorComponent, (0, _extends2.default)({
    highlighted: separatorHighlighted
  }, separatorProps));
  return leadingSeparator || separator ? React.createElement(_View.default, null, inverted === false ? leadingSeparator : separator, element, inverted === false ? separator : leadingSeparator) : element;
}
var _default = exports.default = VirtualizedSectionList;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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