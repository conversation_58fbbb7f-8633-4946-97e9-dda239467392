88264803ef980f894e29586dc157e60e
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _ScrollView = _interopRequireDefault(require("../../../../exports/ScrollView"));
var _createAnimatedComponent = _interopRequireDefault(require("../createAnimatedComponent"));
var ScrollViewWithEventThrottle = React.forwardRef(function (props, ref) {
  return React.createElement(_ScrollView.default, (0, _extends2.default)({
    scrollEventThrottle: 0.0001
  }, props, {
    ref: ref
  }));
});
var _default = exports.default = (0, _createAnimatedComponent.default)(ScrollViewWithEventThrottle);
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsInJlcXVpcmUiLCJkZWZhdWx0IiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiX2V4dGVuZHMyIiwiUmVhY3QiLCJfU2Nyb2xsVmlldyIsIl9jcmVhdGVBbmltYXRlZENvbXBvbmVudCIsIlNjcm9sbFZpZXdXaXRoRXZlbnRUaHJvdHRsZSIsImZvcndhcmRSZWYiLCJwcm9wcyIsInJlZiIsImNyZWF0ZUVsZW1lbnQiLCJzY3JvbGxFdmVudFRocm90dGxlIiwiX2RlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyJBbmltYXRlZFNjcm9sbFZpZXcuanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlV2lsZGNhcmRcIikuZGVmYXVsdDtcbnZhciBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0ID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0XCIpLmRlZmF1bHQ7XG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwO1xudmFyIF9leHRlbmRzMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXh0ZW5kc1wiKSk7XG52YXIgUmVhY3QgPSBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKFwicmVhY3RcIikpO1xudmFyIF9TY3JvbGxWaWV3ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vLi4vLi4vLi4vZXhwb3J0cy9TY3JvbGxWaWV3XCIpKTtcbnZhciBfY3JlYXRlQW5pbWF0ZWRDb21wb25lbnQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi9jcmVhdGVBbmltYXRlZENvbXBvbmVudFwiKSk7XG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKiBAZm9ybWF0XG4gKi9cblxuLyoqXG4gKiBAc2VlIGh0dHBzOi8vZ2l0aHViLmNvbS9mYWNlYm9vay9yZWFjdC1uYXRpdmUvY29tbWl0L2I4Yzg1NjJcbiAqL1xudmFyIFNjcm9sbFZpZXdXaXRoRXZlbnRUaHJvdHRsZSA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgcmVmKSA9PiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChfU2Nyb2xsVmlldy5kZWZhdWx0LCAoMCwgX2V4dGVuZHMyLmRlZmF1bHQpKHtcbiAgc2Nyb2xsRXZlbnRUaHJvdHRsZTogMC4wMDAxXG59LCBwcm9wcywge1xuICByZWY6IHJlZlxufSkpKTtcbnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9ICgwLCBfY3JlYXRlQW5pbWF0ZWRDb21wb25lbnQuZGVmYXVsdCkoU2Nyb2xsVmlld1dpdGhFdmVudFRocm90dGxlKTtcbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFFWixJQUFJQSx1QkFBdUIsR0FBR0MsT0FBTyxDQUFDLCtDQUErQyxDQUFDLENBQUNDLE9BQU87QUFDOUYsSUFBSUMsc0JBQXNCLEdBQUdGLE9BQU8sQ0FBQyw4Q0FBOEMsQ0FBQyxDQUFDQyxPQUFPO0FBQzVGRSxPQUFPLENBQUNDLFVBQVUsR0FBRyxJQUFJO0FBQ3pCRCxPQUFPLENBQUNGLE9BQU8sR0FBRyxLQUFLLENBQUM7QUFDeEIsSUFBSUksU0FBUyxHQUFHSCxzQkFBc0IsQ0FBQ0YsT0FBTyxDQUFDLGdDQUFnQyxDQUFDLENBQUM7QUFDakYsSUFBSU0sS0FBSyxHQUFHUCx1QkFBdUIsQ0FBQ0MsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDO0FBQ3JELElBQUlPLFdBQVcsR0FBR0wsc0JBQXNCLENBQUNGLE9BQU8saUNBQWlDLENBQUMsQ0FBQztBQUNuRixJQUFJUSx3QkFBd0IsR0FBR04sc0JBQXNCLENBQUNGLE9BQU8sNkJBQTZCLENBQUMsQ0FBQztBQWM1RixJQUFJUywyQkFBMkIsR0FBZ0JILEtBQUssQ0FBQ0ksVUFBVSxDQUFDLFVBQUNDLEtBQUssRUFBRUMsR0FBRztFQUFBLE9BQWtCTixLQUFLLENBQUNPLGFBQWEsQ0FBQ04sV0FBVyxDQUFDTixPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVJLFNBQVMsQ0FBQ0osT0FBTyxFQUFFO0lBQzNKYSxtQkFBbUIsRUFBRTtFQUN2QixDQUFDLEVBQUVILEtBQUssRUFBRTtJQUNSQyxHQUFHLEVBQUVBO0VBQ1AsQ0FBQyxDQUFDLENBQUM7QUFBQSxFQUFDO0FBQ0osSUFBSUcsUUFBUSxHQUFHWixPQUFPLENBQUNGLE9BQU8sR0FBRyxDQUFDLENBQUMsRUFBRU8sd0JBQXdCLENBQUNQLE9BQU8sRUFBRVEsMkJBQTJCLENBQUM7QUFDbkdPLE1BQU0sQ0FBQ2IsT0FBTyxHQUFHQSxPQUFPLENBQUNGLE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=