2d06fcfa1ff551de1287f191796cae06
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1vw0dzcqro() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\optimized\\ImageOptimizationService.ts";
  var hash = "1229d805c1b2f0bca15553fbe476685df34615dc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\optimized\\ImageOptimizationService.ts",
    statementMap: {
      "0": {
        start: {
          line: 58,
          column: 32
        },
        end: {
          line: 58,
          column: 56
        }
      },
      "1": {
        start: {
          line: 59,
          column: 38
        },
        end: {
          line: 59,
          column: 64
        }
      },
      "2": {
        start: {
          line: 60,
          column: 35
        },
        end: {
          line: 60,
          column: 61
        }
      },
      "3": {
        start: {
          line: 63,
          column: 33
        },
        end: {
          line: 68,
          column: 3
        }
      },
      "4": {
        start: {
          line: 77,
          column: 22
        },
        end: {
          line: 77,
          column: 32
        }
      },
      "5": {
        start: {
          line: 79,
          column: 4
        },
        end: {
          line: 175,
          column: 5
        }
      },
      "6": {
        start: {
          line: 93,
          column: 10
        },
        end: {
          line: 93,
          column: 17
        }
      },
      "7": {
        start: {
          line: 96,
          column: 23
        },
        end: {
          line: 96,
          column: 63
        }
      },
      "8": {
        start: {
          line: 99,
          column: 21
        },
        end: {
          line: 99,
          column: 81
        }
      },
      "9": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 103,
          column: 7
        }
      },
      "10": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 101,
          column: 102
        }
      },
      "11": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 22
        }
      },
      "12": {
        start: {
          line: 106,
          column: 28
        },
        end: {
          line: 106,
          column: 73
        }
      },
      "13": {
        start: {
          line: 109,
          column: 32
        },
        end: {
          line: 109,
          column: 78
        }
      },
      "14": {
        start: {
          line: 112,
          column: 21
        },
        end: {
          line: 124,
          column: 8
        }
      },
      "15": {
        start: {
          line: 127,
          column: 27
        },
        end: {
          line: 127,
          column: 68
        }
      },
      "16": {
        start: {
          line: 130,
          column: 23
        },
        end: {
          line: 130,
          column: 64
        }
      },
      "17": {
        start: {
          line: 133,
          column: 31
        },
        end: {
          line: 133,
          column: 71
        }
      },
      "18": {
        start: {
          line: 135,
          column: 41
        },
        end: {
          line: 142,
          column: 7
        }
      },
      "19": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 149,
          column: 9
        }
      },
      "20": {
        start: {
          line: 151,
          column: 31
        },
        end: {
          line: 151,
          column: 53
        }
      },
      "21": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 152,
          column: 84
        }
      },
      "22": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 154,
          column: 20
        }
      },
      "23": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 57
        }
      },
      "24": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 82
        }
      },
      "25": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 174,
          column: 8
        }
      },
      "26": {
        start: {
          line: 185,
          column: 21
        },
        end: {
          line: 193,
          column: 6
        }
      },
      "27": {
        start: {
          line: 186,
          column: 24
        },
        end: {
          line: 190,
          column: 8
        }
      },
      "28": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 44
        }
      },
      "29": {
        start: {
          line: 195,
          column: 20
        },
        end: {
          line: 195,
          column: 47
        }
      },
      "30": {
        start: {
          line: 196,
          column: 4
        },
        end: {
          line: 196,
          column: 61
        }
      },
      "31": {
        start: {
          line: 206,
          column: 28
        },
        end: {
          line: 218,
          column: 6
        }
      },
      "32": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 217,
          column: 7
        }
      },
      "33": {
        start: {
          line: 208,
          column: 26
        },
        end: {
          line: 211,
          column: 10
        }
      },
      "34": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 214,
          column: 62
        }
      },
      "35": {
        start: {
          line: 216,
          column: 8
        },
        end: {
          line: 216,
          column: 63
        }
      },
      "36": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 220,
          column: 39
        }
      },
      "37": {
        start: {
          line: 234,
          column: 21
        },
        end: {
          line: 234,
          column: 58
        }
      },
      "38": {
        start: {
          line: 235,
          column: 38
        },
        end: {
          line: 235,
          column: 40
        }
      },
      "39": {
        start: {
          line: 236,
          column: 27
        },
        end: {
          line: 236,
          column: 28
        }
      },
      "40": {
        start: {
          line: 239,
          column: 26
        },
        end: {
          line: 239,
          column: 71
        }
      },
      "41": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 243,
          column: 5
        }
      },
      "42": {
        start: {
          line: 241,
          column: 6
        },
        end: {
          line: 241,
          column: 65
        }
      },
      "43": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 29
        }
      },
      "44": {
        start: {
          line: 246,
          column: 30
        },
        end: {
          line: 246,
          column: 63
        }
      },
      "45": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 250,
          column: 5
        }
      },
      "46": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 65
        }
      },
      "47": {
        start: {
          line: 249,
          column: 6
        },
        end: {
          line: 249,
          column: 29
        }
      },
      "48": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 256,
          column: 5
        }
      },
      "49": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 254,
          column: 75
        }
      },
      "50": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 29
        }
      },
      "51": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 259,
          column: 55
        }
      },
      "52": {
        start: {
          line: 261,
          column: 4
        },
        end: {
          line: 266,
          column: 6
        }
      },
      "53": {
        start: {
          line: 273,
          column: 4
        },
        end: {
          line: 273,
          column: 66
        }
      },
      "54": {
        start: {
          line: 286,
          column: 4
        },
        end: {
          line: 291,
          column: 6
        }
      },
      "55": {
        start: {
          line: 297,
          column: 24
        },
        end: {
          line: 297,
          column: 47
        }
      },
      "56": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 298,
          column: 81
        }
      },
      "57": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 302,
          column: 41
        }
      },
      "58": {
        start: {
          line: 302,
          column: 27
        },
        end: {
          line: 302,
          column: 41
        }
      },
      "59": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 307,
          column: 5
        }
      },
      "60": {
        start: {
          line: 306,
          column: 6
        },
        end: {
          line: 306,
          column: 20
        }
      },
      "61": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 312,
          column: 5
        }
      },
      "62": {
        start: {
          line: 310,
          column: 25
        },
        end: {
          line: 310,
          column: 65
        }
      },
      "63": {
        start: {
          line: 311,
          column: 6
        },
        end: {
          line: 311,
          column: 48
        }
      },
      "64": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 317,
          column: 5
        }
      },
      "65": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 316,
          column: 19
        }
      },
      "66": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 319,
          column: 18
        }
      },
      "67": {
        start: {
          line: 326,
          column: 21
        },
        end: {
          line: 326,
          column: 66
        }
      },
      "68": {
        start: {
          line: 327,
          column: 22
        },
        end: {
          line: 327,
          column: 68
        }
      },
      "69": {
        start: {
          line: 329,
          column: 4
        },
        end: {
          line: 332,
          column: 6
        }
      },
      "70": {
        start: {
          line: 337,
          column: 4
        },
        end: {
          line: 339,
          column: 5
        }
      },
      "71": {
        start: {
          line: 338,
          column: 6
        },
        end: {
          line: 338,
          column: 40
        }
      },
      "72": {
        start: {
          line: 341,
          column: 4
        },
        end: {
          line: 341,
          column: 19
        }
      },
      "73": {
        start: {
          line: 345,
          column: 40
        },
        end: {
          line: 345,
          column: 42
        }
      },
      "74": {
        start: {
          line: 347,
          column: 4
        },
        end: {
          line: 347,
          column: 48
        }
      },
      "75": {
        start: {
          line: 347,
          column: 23
        },
        end: {
          line: 347,
          column: 48
        }
      },
      "76": {
        start: {
          line: 348,
          column: 4
        },
        end: {
          line: 348,
          column: 50
        }
      },
      "77": {
        start: {
          line: 348,
          column: 24
        },
        end: {
          line: 348,
          column: 50
        }
      },
      "78": {
        start: {
          line: 349,
          column: 4
        },
        end: {
          line: 349,
          column: 52
        }
      },
      "79": {
        start: {
          line: 349,
          column: 25
        },
        end: {
          line: 349,
          column: 52
        }
      },
      "80": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 350,
          column: 50
        }
      },
      "81": {
        start: {
          line: 350,
          column: 24
        },
        end: {
          line: 350,
          column: 50
        }
      },
      "82": {
        start: {
          line: 351,
          column: 4
        },
        end: {
          line: 351,
          column: 46
        }
      },
      "83": {
        start: {
          line: 351,
          column: 21
        },
        end: {
          line: 351,
          column: 46
        }
      },
      "84": {
        start: {
          line: 352,
          column: 4
        },
        end: {
          line: 352,
          column: 57
        }
      },
      "85": {
        start: {
          line: 352,
          column: 29
        },
        end: {
          line: 352,
          column: 57
        }
      },
      "86": {
        start: {
          line: 353,
          column: 4
        },
        end: {
          line: 353,
          column: 49
        }
      },
      "87": {
        start: {
          line: 353,
          column: 22
        },
        end: {
          line: 353,
          column: 49
        }
      },
      "88": {
        start: {
          line: 354,
          column: 4
        },
        end: {
          line: 354,
          column: 49
        }
      },
      "89": {
        start: {
          line: 354,
          column: 25
        },
        end: {
          line: 354,
          column: 49
        }
      },
      "90": {
        start: {
          line: 355,
          column: 4
        },
        end: {
          line: 355,
          column: 67
        }
      },
      "91": {
        start: {
          line: 355,
          column: 28
        },
        end: {
          line: 355,
          column: 67
        }
      },
      "92": {
        start: {
          line: 356,
          column: 4
        },
        end: {
          line: 356,
          column: 61
        }
      },
      "93": {
        start: {
          line: 356,
          column: 26
        },
        end: {
          line: 356,
          column: 61
        }
      },
      "94": {
        start: {
          line: 357,
          column: 4
        },
        end: {
          line: 357,
          column: 67
        }
      },
      "95": {
        start: {
          line: 357,
          column: 28
        },
        end: {
          line: 357,
          column: 67
        }
      },
      "96": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 360,
          column: 39
        }
      },
      "97": {
        start: {
          line: 363,
          column: 4
        },
        end: {
          line: 363,
          column: 36
        }
      },
      "98": {
        start: {
          line: 365,
          column: 4
        },
        end: {
          line: 365,
          column: 18
        }
      },
      "99": {
        start: {
          line: 369,
          column: 4
        },
        end: {
          line: 385,
          column: 5
        }
      },
      "100": {
        start: {
          line: 370,
          column: 21
        },
        end: {
          line: 370,
          column: 33
        }
      },
      "101": {
        start: {
          line: 372,
          column: 6
        },
        end: {
          line: 374,
          column: 9
        }
      },
      "102": {
        start: {
          line: 373,
          column: 8
        },
        end: {
          line: 373,
          column: 52
        }
      },
      "103": {
        start: {
          line: 376,
          column: 6
        },
        end: {
          line: 376,
          column: 31
        }
      },
      "104": {
        start: {
          line: 379,
          column: 24
        },
        end: {
          line: 379,
          column: 53
        }
      },
      "105": {
        start: {
          line: 380,
          column: 26
        },
        end: {
          line: 382,
          column: 18
        }
      },
      "106": {
        start: {
          line: 381,
          column: 31
        },
        end: {
          line: 381,
          column: 68
        }
      },
      "107": {
        start: {
          line: 384,
          column: 6
        },
        end: {
          line: 384,
          column: 48
        }
      },
      "108": {
        start: {
          line: 391,
          column: 4
        },
        end: {
          line: 399,
          column: 6
        }
      },
      "109": {
        start: {
          line: 403,
          column: 18
        },
        end: {
          line: 403,
          column: 19
        }
      },
      "110": {
        start: {
          line: 406,
          column: 4
        },
        end: {
          line: 406,
          column: 49
        }
      },
      "111": {
        start: {
          line: 406,
          column: 35
        },
        end: {
          line: 406,
          column: 49
        }
      },
      "112": {
        start: {
          line: 409,
          column: 4
        },
        end: {
          line: 409,
          column: 63
        }
      },
      "113": {
        start: {
          line: 409,
          column: 49
        },
        end: {
          line: 409,
          column: 63
        }
      },
      "114": {
        start: {
          line: 412,
          column: 4
        },
        end: {
          line: 412,
          column: 71
        }
      },
      "115": {
        start: {
          line: 412,
          column: 57
        },
        end: {
          line: 412,
          column: 71
        }
      },
      "116": {
        start: {
          line: 414,
          column: 4
        },
        end: {
          line: 414,
          column: 33
        }
      },
      "117": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 423,
          column: 7
        }
      },
      "118": {
        start: {
          line: 419,
          column: 20
        },
        end: {
          line: 419,
          column: 31
        }
      },
      "119": {
        start: {
          line: 420,
          column: 6
        },
        end: {
          line: 420,
          column: 37
        }
      },
      "120": {
        start: {
          line: 420,
          column: 27
        },
        end: {
          line: 420,
          column: 36
        }
      },
      "121": {
        start: {
          line: 421,
          column: 6
        },
        end: {
          line: 421,
          column: 29
        }
      },
      "122": {
        start: {
          line: 422,
          column: 6
        },
        end: {
          line: 422,
          column: 22
        }
      },
      "123": {
        start: {
          line: 428,
          column: 40
        },
        end: {
          line: 428,
          column: 70
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 73,
            column: 2
          },
          end: {
            line: 73,
            column: 3
          }
        },
        loc: {
          start: {
            line: 76,
            column: 33
          },
          end: {
            line: 176,
            column: 3
          }
        },
        line: 76
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 181,
            column: 2
          },
          end: {
            line: 181,
            column: 3
          }
        },
        loc: {
          start: {
            line: 184,
            column: 33
          },
          end: {
            line: 197,
            column: 3
          }
        },
        line: 184
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 185,
            column: 58
          },
          end: {
            line: 185,
            column: 59
          }
        },
        loc: {
          start: {
            line: 185,
            column: 83
          },
          end: {
            line: 193,
            column: 5
          }
        },
        line: 185
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 202,
            column: 2
          },
          end: {
            line: 202,
            column: 3
          }
        },
        loc: {
          start: {
            line: 205,
            column: 19
          },
          end: {
            line: 221,
            column: 3
          }
        },
        line: 205
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 206,
            column: 42
          },
          end: {
            line: 206,
            column: 43
          }
        },
        loc: {
          start: {
            line: 206,
            column: 57
          },
          end: {
            line: 218,
            column: 5
          }
        },
        line: 206
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 226,
            column: 2
          },
          end: {
            line: 226,
            column: 3
          }
        },
        loc: {
          start: {
            line: 233,
            column: 5
          },
          end: {
            line: 267,
            column: 3
          }
        },
        line: 233
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 272,
            column: 2
          },
          end: {
            line: 272,
            column: 3
          }
        },
        loc: {
          start: {
            line: 272,
            column: 48
          },
          end: {
            line: 274,
            column: 3
          }
        },
        line: 272
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 279,
            column: 2
          },
          end: {
            line: 279,
            column: 3
          }
        },
        loc: {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 292,
            column: 3
          }
        },
        line: 284
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 296,
            column: 2
          },
          end: {
            line: 296,
            column: 3
          }
        },
        loc: {
          start: {
            line: 296,
            column: 83
          },
          end: {
            line: 299,
            column: 3
          }
        },
        line: 296
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 301,
            column: 2
          },
          end: {
            line: 301,
            column: 3
          }
        },
        loc: {
          start: {
            line: 301,
            column: 75
          },
          end: {
            line: 320,
            column: 3
          }
        },
        line: 301
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 322,
            column: 2
          },
          end: {
            line: 322,
            column: 3
          }
        },
        loc: {
          start: {
            line: 325,
            column: 39
          },
          end: {
            line: 333,
            column: 3
          }
        },
        line: 325
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 335,
            column: 2
          },
          end: {
            line: 335,
            column: 3
          }
        },
        loc: {
          start: {
            line: 335,
            column: 58
          },
          end: {
            line: 342,
            column: 3
          }
        },
        line: 335
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 344,
            column: 2
          },
          end: {
            line: 344,
            column: 3
          }
        },
        loc: {
          start: {
            line: 344,
            column: 90
          },
          end: {
            line: 366,
            column: 3
          }
        },
        line: 344
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 368,
            column: 2
          },
          end: {
            line: 368,
            column: 3
          }
        },
        loc: {
          start: {
            line: 368,
            column: 79
          },
          end: {
            line: 386,
            column: 3
          }
        },
        line: 368
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 372,
            column: 37
          },
          end: {
            line: 372,
            column: 38
          }
        },
        loc: {
          start: {
            line: 372,
            column: 55
          },
          end: {
            line: 374,
            column: 7
          }
        },
        line: 372
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 381,
            column: 13
          },
          end: {
            line: 381,
            column: 14
          }
        },
        loc: {
          start: {
            line: 381,
            column: 31
          },
          end: {
            line: 381,
            column: 68
          }
        },
        line: 381
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 388,
            column: 2
          },
          end: {
            line: 388,
            column: 3
          }
        },
        loc: {
          start: {
            line: 388,
            column: 70
          },
          end: {
            line: 400,
            column: 3
          }
        },
        line: 388
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 402,
            column: 2
          },
          end: {
            line: 402,
            column: 3
          }
        },
        loc: {
          start: {
            line: 402,
            column: 95
          },
          end: {
            line: 415,
            column: 3
          }
        },
        line: 402
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 417,
            column: 2
          },
          end: {
            line: 417,
            column: 3
          }
        },
        loc: {
          start: {
            line: 417,
            column: 63
          },
          end: {
            line: 424,
            column: 3
          }
        },
        line: 417
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 418,
            column: 23
          },
          end: {
            line: 418,
            column: 24
          }
        },
        loc: {
          start: {
            line: 418,
            column: 44
          },
          end: {
            line: 423,
            column: 5
          }
        },
        line: 418
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 420,
            column: 21
          },
          end: {
            line: 420,
            column: 22
          }
        },
        loc: {
          start: {
            line: 420,
            column: 27
          },
          end: {
            line: 420,
            column: 36
          }
        },
        line: 420
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 59,
            column: 38
          },
          end: {
            line: 59,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 59,
            column: 38
          },
          end: {
            line: 59,
            column: 59
          }
        }, {
          start: {
            line: 59,
            column: 63
          },
          end: {
            line: 59,
            column: 64
          }
        }],
        line: 59
      },
      "1": {
        loc: {
          start: {
            line: 75,
            column: 4
          },
          end: {
            line: 75,
            column: 42
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 75,
            column: 40
          },
          end: {
            line: 75,
            column: 42
          }
        }],
        line: 75
      },
      "2": {
        loc: {
          start: {
            line: 83,
            column: 8
          },
          end: {
            line: 83,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 83,
            column: 18
          },
          end: {
            line: 83,
            column: 20
          }
        }],
        line: 83
      },
      "3": {
        loc: {
          start: {
            line: 84,
            column: 8
          },
          end: {
            line: 84,
            column: 23
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 84,
            column: 17
          },
          end: {
            line: 84,
            column: 23
          }
        }],
        line: 84
      },
      "4": {
        loc: {
          start: {
            line: 85,
            column: 8
          },
          end: {
            line: 85,
            column: 20
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 85,
            column: 14
          },
          end: {
            line: 85,
            column: 20
          }
        }],
        line: 85
      },
      "5": {
        loc: {
          start: {
            line: 86,
            column: 8
          },
          end: {
            line: 86,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 86,
            column: 19
          },
          end: {
            line: 86,
            column: 27
          }
        }],
        line: 86
      },
      "6": {
        loc: {
          start: {
            line: 87,
            column: 8
          },
          end: {
            line: 87,
            column: 26
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 87,
            column: 22
          },
          end: {
            line: 87,
            column: 26
          }
        }],
        line: 87
      },
      "7": {
        loc: {
          start: {
            line: 89,
            column: 8
          },
          end: {
            line: 89,
            column: 23
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 89,
            column: 18
          },
          end: {
            line: 89,
            column: 23
          }
        }],
        line: 89
      },
      "8": {
        loc: {
          start: {
            line: 100,
            column: 6
          },
          end: {
            line: 103,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 100,
            column: 6
          },
          end: {
            line: 103,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 100
      },
      "9": {
        loc: {
          start: {
            line: 183,
            column: 4
          },
          end: {
            line: 183,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 183,
            column: 44
          },
          end: {
            line: 183,
            column: 46
          }
        }],
        line: 183
      },
      "10": {
        loc: {
          start: {
            line: 189,
            column: 18
          },
          end: {
            line: 189,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 189,
            column: 38
          },
          end: {
            line: 189,
            column: 44
          }
        }, {
          start: {
            line: 189,
            column: 47
          },
          end: {
            line: 189,
            column: 55
          }
        }],
        line: 189
      },
      "11": {
        loc: {
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 204,
            column: 42
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 204,
            column: 40
          },
          end: {
            line: 204,
            column: 42
          }
        }],
        line: 204
      },
      "12": {
        loc: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "13": {
        loc: {
          start: {
            line: 247,
            column: 4
          },
          end: {
            line: 250,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 247,
            column: 4
          },
          end: {
            line: 250,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 247
      },
      "14": {
        loc: {
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 253
      },
      "15": {
        loc: {
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 302,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 302,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "16": {
        loc: {
          start: {
            line: 305,
            column: 4
          },
          end: {
            line: 307,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 305,
            column: 4
          },
          end: {
            line: 307,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 305
      },
      "17": {
        loc: {
          start: {
            line: 309,
            column: 4
          },
          end: {
            line: 312,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 309,
            column: 4
          },
          end: {
            line: 312,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 309
      },
      "18": {
        loc: {
          start: {
            line: 311,
            column: 13
          },
          end: {
            line: 311,
            column: 47
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 311,
            column: 32
          },
          end: {
            line: 311,
            column: 38
          }
        }, {
          start: {
            line: 311,
            column: 41
          },
          end: {
            line: 311,
            column: 47
          }
        }],
        line: 311
      },
      "19": {
        loc: {
          start: {
            line: 315,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 315
      },
      "20": {
        loc: {
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 315,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 315,
            column: 33
          }
        }, {
          start: {
            line: 315,
            column: 37
          },
          end: {
            line: 315,
            column: 63
          }
        }],
        line: 315
      },
      "21": {
        loc: {
          start: {
            line: 330,
            column: 13
          },
          end: {
            line: 330,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 330,
            column: 30
          },
          end: {
            line: 330,
            column: 64
          }
        }, {
          start: {
            line: 330,
            column: 67
          },
          end: {
            line: 330,
            column: 75
          }
        }],
        line: 330
      },
      "22": {
        loc: {
          start: {
            line: 331,
            column: 14
          },
          end: {
            line: 331,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 331,
            column: 32
          },
          end: {
            line: 331,
            column: 68
          }
        }, {
          start: {
            line: 331,
            column: 71
          },
          end: {
            line: 331,
            column: 80
          }
        }],
        line: 331
      },
      "23": {
        loc: {
          start: {
            line: 337,
            column: 4
          },
          end: {
            line: 339,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 337,
            column: 4
          },
          end: {
            line: 339,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 337
      },
      "24": {
        loc: {
          start: {
            line: 347,
            column: 4
          },
          end: {
            line: 347,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 347,
            column: 4
          },
          end: {
            line: 347,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 347
      },
      "25": {
        loc: {
          start: {
            line: 348,
            column: 4
          },
          end: {
            line: 348,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 348,
            column: 4
          },
          end: {
            line: 348,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 348
      },
      "26": {
        loc: {
          start: {
            line: 349,
            column: 4
          },
          end: {
            line: 349,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 349,
            column: 4
          },
          end: {
            line: 349,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 349
      },
      "27": {
        loc: {
          start: {
            line: 350,
            column: 4
          },
          end: {
            line: 350,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 350,
            column: 4
          },
          end: {
            line: 350,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 350
      },
      "28": {
        loc: {
          start: {
            line: 351,
            column: 4
          },
          end: {
            line: 351,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 351,
            column: 4
          },
          end: {
            line: 351,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 351
      },
      "29": {
        loc: {
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 352,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 352,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 352
      },
      "30": {
        loc: {
          start: {
            line: 353,
            column: 4
          },
          end: {
            line: 353,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 353,
            column: 4
          },
          end: {
            line: 353,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 353
      },
      "31": {
        loc: {
          start: {
            line: 354,
            column: 4
          },
          end: {
            line: 354,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 354,
            column: 4
          },
          end: {
            line: 354,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 354
      },
      "32": {
        loc: {
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 355,
            column: 67
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 355,
            column: 67
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 355
      },
      "33": {
        loc: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 356,
            column: 61
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 356,
            column: 61
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 356
      },
      "34": {
        loc: {
          start: {
            line: 357,
            column: 4
          },
          end: {
            line: 357,
            column: 67
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 357,
            column: 4
          },
          end: {
            line: 357,
            column: 67
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 357
      },
      "35": {
        loc: {
          start: {
            line: 379,
            column: 24
          },
          end: {
            line: 379,
            column: 53
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 379,
            column: 44
          },
          end: {
            line: 379,
            column: 47
          }
        }, {
          start: {
            line: 379,
            column: 50
          },
          end: {
            line: 379,
            column: 53
          }
        }],
        line: 379
      },
      "36": {
        loc: {
          start: {
            line: 406,
            column: 4
          },
          end: {
            line: 406,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 406,
            column: 4
          },
          end: {
            line: 406,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 406
      },
      "37": {
        loc: {
          start: {
            line: 409,
            column: 4
          },
          end: {
            line: 409,
            column: 63
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 409,
            column: 4
          },
          end: {
            line: 409,
            column: 63
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 409
      },
      "38": {
        loc: {
          start: {
            line: 409,
            column: 8
          },
          end: {
            line: 409,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 409,
            column: 8
          },
          end: {
            line: 409,
            column: 23
          }
        }, {
          start: {
            line: 409,
            column: 27
          },
          end: {
            line: 409,
            column: 47
          }
        }],
        line: 409
      },
      "39": {
        loc: {
          start: {
            line: 412,
            column: 4
          },
          end: {
            line: 412,
            column: 71
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 412,
            column: 4
          },
          end: {
            line: 412,
            column: 71
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 412
      },
      "40": {
        loc: {
          start: {
            line: 412,
            column: 8
          },
          end: {
            line: 412,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 412,
            column: 8
          },
          end: {
            line: 412,
            column: 21
          }
        }, {
          start: {
            line: 412,
            column: 25
          },
          end: {
            line: 412,
            column: 55
          }
        }],
        line: 412
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0],
      "7": [0],
      "8": [0, 0],
      "9": [0],
      "10": [0, 0],
      "11": [0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1229d805c1b2f0bca15553fbe476685df34615dc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1vw0dzcqro = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1vw0dzcqro();
import { Dimensions, Platform } from 'react-native';
import { advancedCacheManager } from "../caching/AdvancedCacheManager";
import { performanceMonitor } from "../../utils/performance";
var ImageOptimizationService = function () {
  function ImageOptimizationService() {
    _classCallCheck(this, ImageOptimizationService);
    this.screenData = (cov_1vw0dzcqro().s[0]++, Dimensions.get('window'));
    this.devicePixelRatio = (cov_1vw0dzcqro().s[1]++, (cov_1vw0dzcqro().b[0][0]++, this.screenData.scale) || (cov_1vw0dzcqro().b[0][1]++, 1));
    this.isHighDensity = (cov_1vw0dzcqro().s[2]++, this.devicePixelRatio >= 2);
    this.breakpoints = (cov_1vw0dzcqro().s[3]++, {
      small: 480,
      medium: 768,
      large: 1024,
      xlarge: 1440
    });
  }
  return _createClass(ImageOptimizationService, [{
    key: "optimizeImage",
    value: (function () {
      var _optimizeImage = _asyncToGenerator(function* (imageUrl) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1vw0dzcqro().b[1][0]++, {});
        cov_1vw0dzcqro().f[0]++;
        var startTime = (cov_1vw0dzcqro().s[4]++, Date.now());
        cov_1vw0dzcqro().s[5]++;
        try {
          var _ref = (cov_1vw0dzcqro().s[6]++, options),
            width = _ref.width,
            height = _ref.height,
            _ref$quality = _ref.quality,
            quality = _ref$quality === void 0 ? (cov_1vw0dzcqro().b[2][0]++, 80) : _ref$quality,
            _ref$format = _ref.format,
            format = _ref$format === void 0 ? (cov_1vw0dzcqro().b[3][0]++, 'auto') : _ref$format,
            _ref$fit = _ref.fit,
            fit = _ref$fit === void 0 ? (cov_1vw0dzcqro().b[4][0]++, 'crop') : _ref$fit,
            _ref$priority = _ref.priority,
            priority = _ref$priority === void 0 ? (cov_1vw0dzcqro().b[5][0]++, 'medium') : _ref$priority,
            _ref$progressive = _ref.progressive,
            progressive = _ref$progressive === void 0 ? (cov_1vw0dzcqro().b[6][0]++, true) : _ref$progressive,
            blur = _ref.blur,
            _ref$sharpen = _ref.sharpen,
            sharpen = _ref$sharpen === void 0 ? (cov_1vw0dzcqro().b[7][0]++, false) : _ref$sharpen,
            brightness = _ref.brightness,
            contrast = _ref.contrast,
            saturation = _ref.saturation;
          var cacheKey = (cov_1vw0dzcqro().s[7]++, this.generateCacheKey(imageUrl, options));
          var cached = (cov_1vw0dzcqro().s[8]++, yield advancedCacheManager.get(cacheKey));
          cov_1vw0dzcqro().s[9]++;
          if (cached) {
            cov_1vw0dzcqro().b[8][0]++;
            cov_1vw0dzcqro().s[10]++;
            performanceMonitor.trackDatabaseQuery('image_optimization_cache_hit', Date.now() - startTime);
            cov_1vw0dzcqro().s[11]++;
            return cached;
          } else {
            cov_1vw0dzcqro().b[8][1]++;
          }
          var optimalFormat = (cov_1vw0dzcqro().s[12]++, this.determineOptimalFormat(format, imageUrl));
          var optimalDimensions = (cov_1vw0dzcqro().s[13]++, this.calculateOptimalDimensions(width, height));
          var params = (cov_1vw0dzcqro().s[14]++, this.buildOptimizationParams(Object.assign({}, options, {
            width: optimalDimensions.width,
            height: optimalDimensions.height,
            format: optimalFormat,
            quality: this.adjustQualityForDevice(quality),
            progressive: progressive,
            blur: blur,
            sharpen: sharpen,
            brightness: brightness,
            contrast: contrast,
            saturation: saturation
          })));
          var optimizedUrl = (cov_1vw0dzcqro().s[15]++, this.applyOptimizations(imageUrl, params));
          var metadata = (cov_1vw0dzcqro().s[16]++, yield this.getImageMetadata(optimizedUrl));
          var estimatedSavings = (cov_1vw0dzcqro().s[17]++, this.calculateSavings(metadata, options));
          var result = (cov_1vw0dzcqro().s[18]++, {
            optimizedUrl: optimizedUrl,
            originalUrl: imageUrl,
            metadata: metadata,
            optimizationApplied: Object.keys(params),
            estimatedSavings: estimatedSavings,
            cacheKey: cacheKey
          });
          cov_1vw0dzcqro().s[19]++;
          yield advancedCacheManager.set(cacheKey, result, {
            ttl: 86400000,
            priority: priority,
            tags: ['image_optimization']
          });
          var optimizationTime = (cov_1vw0dzcqro().s[20]++, Date.now() - startTime);
          cov_1vw0dzcqro().s[21]++;
          performanceMonitor.trackDatabaseQuery('image_optimization', optimizationTime);
          cov_1vw0dzcqro().s[22]++;
          return result;
        } catch (error) {
          cov_1vw0dzcqro().s[23]++;
          console.error('Image optimization failed:', error);
          cov_1vw0dzcqro().s[24]++;
          performanceMonitor.trackDatabaseError('image_optimization', error);
          cov_1vw0dzcqro().s[25]++;
          return {
            optimizedUrl: imageUrl,
            originalUrl: imageUrl,
            metadata: {
              width: 0,
              height: 0,
              format: 'unknown',
              size: 0,
              aspectRatio: 1
            },
            optimizationApplied: [],
            estimatedSavings: 0,
            cacheKey: ''
          };
        }
      });
      function optimizeImage(_x) {
        return _optimizeImage.apply(this, arguments);
      }
      return optimizeImage;
    }())
  }, {
    key: "generateResponsiveImageSet",
    value: (function () {
      var _generateResponsiveImageSet = _asyncToGenerator(function* (imageUrl) {
        var _this = this;
        var baseOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1vw0dzcqro().b[9][0]++, {});
        cov_1vw0dzcqro().f[1]++;
        var promises = (cov_1vw0dzcqro().s[26]++, Object.entries(this.breakpoints).map(function () {
          var _ref3 = _asyncToGenerator(function* (_ref2) {
            var _ref4 = _slicedToArray(_ref2, 2),
              size = _ref4[0],
              width = _ref4[1];
            cov_1vw0dzcqro().f[2]++;
            var optimized = (cov_1vw0dzcqro().s[27]++, yield _this.optimizeImage(imageUrl, Object.assign({}, baseOptions, {
              width: width * _this.devicePixelRatio,
              priority: size === 'medium' ? (cov_1vw0dzcqro().b[10][0]++, 'high') : (cov_1vw0dzcqro().b[10][1]++, 'medium')
            })));
            cov_1vw0dzcqro().s[28]++;
            return [size, optimized.optimizedUrl];
          });
          return function (_x3) {
            return _ref3.apply(this, arguments);
          };
        }()));
        var results = (cov_1vw0dzcqro().s[29]++, yield Promise.all(promises));
        cov_1vw0dzcqro().s[30]++;
        return Object.fromEntries(results);
      });
      function generateResponsiveImageSet(_x2) {
        return _generateResponsiveImageSet.apply(this, arguments);
      }
      return generateResponsiveImageSet;
    }())
  }, {
    key: "preloadImages",
    value: (function () {
      var _preloadImages = _asyncToGenerator(function* (imageUrls) {
        var _this2 = this;
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1vw0dzcqro().b[11][0]++, {});
        cov_1vw0dzcqro().f[3]++;
        var preloadPromises = (cov_1vw0dzcqro().s[31]++, imageUrls.map(function () {
          var _ref5 = _asyncToGenerator(function* (url) {
            cov_1vw0dzcqro().f[4]++;
            cov_1vw0dzcqro().s[32]++;
            try {
              var optimized = (cov_1vw0dzcqro().s[33]++, yield _this2.optimizeImage(url, Object.assign({}, options, {
                priority: 'high'
              })));
              cov_1vw0dzcqro().s[34]++;
              yield _this2.preloadSingleImage(optimized.optimizedUrl);
            } catch (error) {
              cov_1vw0dzcqro().s[35]++;
              console.warn(`Failed to preload image: ${url}`, error);
            }
          });
          return function (_x5) {
            return _ref5.apply(this, arguments);
          };
        }()));
        cov_1vw0dzcqro().s[36]++;
        yield Promise.all(preloadPromises);
      });
      function preloadImages(_x4) {
        return _preloadImages.apply(this, arguments);
      }
      return preloadImages;
    }())
  }, {
    key: "getOptimizationRecommendations",
    value: (function () {
      var _getOptimizationRecommendations = _asyncToGenerator(function* (imageUrl) {
        cov_1vw0dzcqro().f[5]++;
        var metadata = (cov_1vw0dzcqro().s[37]++, yield this.getImageMetadata(imageUrl));
        var recommendations = (cov_1vw0dzcqro().s[38]++, []);
        var potentialSavings = (cov_1vw0dzcqro().s[39]++, 0);
        var optimalFormat = (cov_1vw0dzcqro().s[40]++, this.determineOptimalFormat('auto', imageUrl));
        cov_1vw0dzcqro().s[41]++;
        if (metadata.format !== optimalFormat) {
          cov_1vw0dzcqro().b[12][0]++;
          cov_1vw0dzcqro().s[42]++;
          recommendations.push(`Convert to ${optimalFormat} format`);
          cov_1vw0dzcqro().s[43]++;
          potentialSavings += 30;
        } else {
          cov_1vw0dzcqro().b[12][1]++;
        }
        var optimalDimensions = (cov_1vw0dzcqro().s[44]++, this.calculateOptimalDimensions());
        cov_1vw0dzcqro().s[45]++;
        if (metadata.width > optimalDimensions.width * 2) {
          cov_1vw0dzcqro().b[13][0]++;
          cov_1vw0dzcqro().s[46]++;
          recommendations.push('Reduce image dimensions for mobile');
          cov_1vw0dzcqro().s[47]++;
          potentialSavings += 50;
        } else {
          cov_1vw0dzcqro().b[13][1]++;
        }
        cov_1vw0dzcqro().s[48]++;
        if (metadata.size > 500000) {
          cov_1vw0dzcqro().b[14][0]++;
          cov_1vw0dzcqro().s[49]++;
          recommendations.push('Reduce quality to 80% for better performance');
          cov_1vw0dzcqro().s[50]++;
          potentialSavings += 20;
        } else {
          cov_1vw0dzcqro().b[14][1]++;
        }
        cov_1vw0dzcqro().s[51]++;
        recommendations.push('Enable progressive loading');
        cov_1vw0dzcqro().s[52]++;
        return {
          recommendations: recommendations,
          potentialSavings: Math.min(potentialSavings, 80),
          optimalFormat: optimalFormat,
          optimalDimensions: optimalDimensions
        };
      });
      function getOptimizationRecommendations(_x6) {
        return _getOptimizationRecommendations.apply(this, arguments);
      }
      return getOptimizationRecommendations;
    }())
  }, {
    key: "clearOptimizationCache",
    value: (function () {
      var _clearOptimizationCache = _asyncToGenerator(function* () {
        cov_1vw0dzcqro().f[6]++;
        cov_1vw0dzcqro().s[53]++;
        yield advancedCacheManager.invalidate(['image_optimization']);
      });
      function clearOptimizationCache() {
        return _clearOptimizationCache.apply(this, arguments);
      }
      return clearOptimizationCache;
    }())
  }, {
    key: "getOptimizationStats",
    value: function getOptimizationStats() {
      cov_1vw0dzcqro().f[7]++;
      cov_1vw0dzcqro().s[54]++;
      return {
        totalOptimizations: 0,
        averageSavings: 0,
        cacheHitRate: 0,
        formatDistribution: {}
      };
    }
  }, {
    key: "generateCacheKey",
    value: function generateCacheKey(url, options) {
      cov_1vw0dzcqro().f[8]++;
      var optionsHash = (cov_1vw0dzcqro().s[55]++, JSON.stringify(options));
      cov_1vw0dzcqro().s[56]++;
      return `img_opt_${btoa(url).slice(0, 20)}_${btoa(optionsHash).slice(0, 20)}`;
    }
  }, {
    key: "determineOptimalFormat",
    value: function determineOptimalFormat(format, imageUrl) {
      cov_1vw0dzcqro().f[9]++;
      cov_1vw0dzcqro().s[57]++;
      if (format !== 'auto') {
        cov_1vw0dzcqro().b[15][0]++;
        cov_1vw0dzcqro().s[58]++;
        return format;
      } else {
        cov_1vw0dzcqro().b[15][1]++;
      }
      cov_1vw0dzcqro().s[59]++;
      if (Platform.OS === 'android') {
        cov_1vw0dzcqro().b[16][0]++;
        cov_1vw0dzcqro().s[60]++;
        return 'webp';
      } else {
        cov_1vw0dzcqro().b[16][1]++;
      }
      cov_1vw0dzcqro().s[61]++;
      if (Platform.OS === 'ios') {
        cov_1vw0dzcqro().b[17][0]++;
        var iosVersion = (cov_1vw0dzcqro().s[62]++, parseInt(Platform.Version, 10));
        cov_1vw0dzcqro().s[63]++;
        return iosVersion >= 14 ? (cov_1vw0dzcqro().b[18][0]++, 'webp') : (cov_1vw0dzcqro().b[18][1]++, 'jpeg');
      } else {
        cov_1vw0dzcqro().b[17][1]++;
      }
      cov_1vw0dzcqro().s[64]++;
      if ((cov_1vw0dzcqro().b[20][0]++, imageUrl.includes('.png')) || (cov_1vw0dzcqro().b[20][1]++, imageUrl.includes('alpha'))) {
        cov_1vw0dzcqro().b[19][0]++;
        cov_1vw0dzcqro().s[65]++;
        return 'png';
      } else {
        cov_1vw0dzcqro().b[19][1]++;
      }
      cov_1vw0dzcqro().s[66]++;
      return 'jpeg';
    }
  }, {
    key: "calculateOptimalDimensions",
    value: function calculateOptimalDimensions(requestedWidth, requestedHeight) {
      cov_1vw0dzcqro().f[10]++;
      var maxWidth = (cov_1vw0dzcqro().s[67]++, this.screenData.width * this.devicePixelRatio);
      var maxHeight = (cov_1vw0dzcqro().s[68]++, this.screenData.height * this.devicePixelRatio);
      cov_1vw0dzcqro().s[69]++;
      return {
        width: requestedWidth ? (cov_1vw0dzcqro().b[21][0]++, Math.min(requestedWidth, maxWidth)) : (cov_1vw0dzcqro().b[21][1]++, maxWidth),
        height: requestedHeight ? (cov_1vw0dzcqro().b[22][0]++, Math.min(requestedHeight, maxHeight)) : (cov_1vw0dzcqro().b[22][1]++, maxHeight)
      };
    }
  }, {
    key: "adjustQualityForDevice",
    value: function adjustQualityForDevice(quality) {
      cov_1vw0dzcqro().f[11]++;
      cov_1vw0dzcqro().s[70]++;
      if (this.devicePixelRatio < 2) {
        cov_1vw0dzcqro().b[23][0]++;
        cov_1vw0dzcqro().s[71]++;
        return Math.max(quality - 10, 60);
      } else {
        cov_1vw0dzcqro().b[23][1]++;
      }
      cov_1vw0dzcqro().s[72]++;
      return quality;
    }
  }, {
    key: "buildOptimizationParams",
    value: function buildOptimizationParams(options) {
      cov_1vw0dzcqro().f[12]++;
      var params = (cov_1vw0dzcqro().s[73]++, {});
      cov_1vw0dzcqro().s[74]++;
      if (options.width) {
        cov_1vw0dzcqro().b[24][0]++;
        cov_1vw0dzcqro().s[75]++;
        params.w = options.width;
      } else {
        cov_1vw0dzcqro().b[24][1]++;
      }
      cov_1vw0dzcqro().s[76]++;
      if (options.height) {
        cov_1vw0dzcqro().b[25][0]++;
        cov_1vw0dzcqro().s[77]++;
        params.h = options.height;
      } else {
        cov_1vw0dzcqro().b[25][1]++;
      }
      cov_1vw0dzcqro().s[78]++;
      if (options.quality) {
        cov_1vw0dzcqro().b[26][0]++;
        cov_1vw0dzcqro().s[79]++;
        params.q = options.quality;
      } else {
        cov_1vw0dzcqro().b[26][1]++;
      }
      cov_1vw0dzcqro().s[80]++;
      if (options.format) {
        cov_1vw0dzcqro().b[27][0]++;
        cov_1vw0dzcqro().s[81]++;
        params.f = options.format;
      } else {
        cov_1vw0dzcqro().b[27][1]++;
      }
      cov_1vw0dzcqro().s[82]++;
      if (options.fit) {
        cov_1vw0dzcqro().b[28][0]++;
        cov_1vw0dzcqro().s[83]++;
        params.fit = options.fit;
      } else {
        cov_1vw0dzcqro().b[28][1]++;
      }
      cov_1vw0dzcqro().s[84]++;
      if (options.progressive) {
        cov_1vw0dzcqro().b[29][0]++;
        cov_1vw0dzcqro().s[85]++;
        params.progressive = 'true';
      } else {
        cov_1vw0dzcqro().b[29][1]++;
      }
      cov_1vw0dzcqro().s[86]++;
      if (options.blur) {
        cov_1vw0dzcqro().b[30][0]++;
        cov_1vw0dzcqro().s[87]++;
        params.blur = options.blur;
      } else {
        cov_1vw0dzcqro().b[30][1]++;
      }
      cov_1vw0dzcqro().s[88]++;
      if (options.sharpen) {
        cov_1vw0dzcqro().b[31][0]++;
        cov_1vw0dzcqro().s[89]++;
        params.sharpen = 'true';
      } else {
        cov_1vw0dzcqro().b[31][1]++;
      }
      cov_1vw0dzcqro().s[90]++;
      if (options.brightness) {
        cov_1vw0dzcqro().b[32][0]++;
        cov_1vw0dzcqro().s[91]++;
        params.brightness = options.brightness;
      } else {
        cov_1vw0dzcqro().b[32][1]++;
      }
      cov_1vw0dzcqro().s[92]++;
      if (options.contrast) {
        cov_1vw0dzcqro().b[33][0]++;
        cov_1vw0dzcqro().s[93]++;
        params.contrast = options.contrast;
      } else {
        cov_1vw0dzcqro().b[33][1]++;
      }
      cov_1vw0dzcqro().s[94]++;
      if (options.saturation) {
        cov_1vw0dzcqro().b[34][0]++;
        cov_1vw0dzcqro().s[95]++;
        params.saturation = options.saturation;
      } else {
        cov_1vw0dzcqro().b[34][1]++;
      }
      cov_1vw0dzcqro().s[96]++;
      params.dpr = this.devicePixelRatio;
      cov_1vw0dzcqro().s[97]++;
      params.auto = 'format,compress';
      cov_1vw0dzcqro().s[98]++;
      return params;
    }
  }, {
    key: "applyOptimizations",
    value: function applyOptimizations(url, params) {
      cov_1vw0dzcqro().f[13]++;
      cov_1vw0dzcqro().s[99]++;
      try {
        var urlObj = (cov_1vw0dzcqro().s[100]++, new URL(url));
        cov_1vw0dzcqro().s[101]++;
        Object.entries(params).forEach(function (_ref6) {
          var _ref7 = _slicedToArray(_ref6, 2),
            key = _ref7[0],
            value = _ref7[1];
          cov_1vw0dzcqro().f[14]++;
          cov_1vw0dzcqro().s[102]++;
          urlObj.searchParams.set(key, String(value));
        });
        cov_1vw0dzcqro().s[103]++;
        return urlObj.toString();
      } catch (error) {
        var separator = (cov_1vw0dzcqro().s[104]++, url.includes('?') ? (cov_1vw0dzcqro().b[35][0]++, '&') : (cov_1vw0dzcqro().b[35][1]++, '?'));
        var paramString = (cov_1vw0dzcqro().s[105]++, Object.entries(params).map(function (_ref8) {
          var _ref9 = _slicedToArray(_ref8, 2),
            key = _ref9[0],
            value = _ref9[1];
          cov_1vw0dzcqro().f[15]++;
          cov_1vw0dzcqro().s[106]++;
          return `${key}=${encodeURIComponent(value)}`;
        }).join('&'));
        cov_1vw0dzcqro().s[107]++;
        return `${url}${separator}${paramString}`;
      }
    }
  }, {
    key: "getImageMetadata",
    value: function () {
      var _getImageMetadata = _asyncToGenerator(function* (url) {
        cov_1vw0dzcqro().f[16]++;
        cov_1vw0dzcqro().s[108]++;
        return {
          width: 1920,
          height: 1080,
          format: 'jpeg',
          size: 250000,
          aspectRatio: 1920 / 1080,
          colorSpace: 'sRGB',
          hasAlpha: false
        };
      });
      function getImageMetadata(_x7) {
        return _getImageMetadata.apply(this, arguments);
      }
      return getImageMetadata;
    }()
  }, {
    key: "calculateSavings",
    value: function calculateSavings(metadata, options) {
      cov_1vw0dzcqro().f[17]++;
      var savings = (cov_1vw0dzcqro().s[109]++, 0);
      cov_1vw0dzcqro().s[110]++;
      if (options.format === 'webp') {
        cov_1vw0dzcqro().b[36][0]++;
        cov_1vw0dzcqro().s[111]++;
        savings += 25;
      } else {
        cov_1vw0dzcqro().b[36][1]++;
      }
      cov_1vw0dzcqro().s[112]++;
      if ((cov_1vw0dzcqro().b[38][0]++, options.quality) && (cov_1vw0dzcqro().b[38][1]++, options.quality < 90)) {
        cov_1vw0dzcqro().b[37][0]++;
        cov_1vw0dzcqro().s[113]++;
        savings += 15;
      } else {
        cov_1vw0dzcqro().b[37][1]++;
      }
      cov_1vw0dzcqro().s[114]++;
      if ((cov_1vw0dzcqro().b[40][0]++, options.width) && (cov_1vw0dzcqro().b[40][1]++, options.width < metadata.width)) {
        cov_1vw0dzcqro().b[39][0]++;
        cov_1vw0dzcqro().s[115]++;
        savings += 30;
      } else {
        cov_1vw0dzcqro().b[39][1]++;
      }
      cov_1vw0dzcqro().s[116]++;
      return Math.min(savings, 70);
    }
  }, {
    key: "preloadSingleImage",
    value: function () {
      var _preloadSingleImage = _asyncToGenerator(function* (url) {
        cov_1vw0dzcqro().f[18]++;
        cov_1vw0dzcqro().s[117]++;
        return new Promise(function (resolve, reject) {
          cov_1vw0dzcqro().f[19]++;
          var image = (cov_1vw0dzcqro().s[118]++, new Image());
          cov_1vw0dzcqro().s[119]++;
          image.onload = function () {
            cov_1vw0dzcqro().f[20]++;
            cov_1vw0dzcqro().s[120]++;
            return resolve();
          };
          cov_1vw0dzcqro().s[121]++;
          image.onerror = reject;
          cov_1vw0dzcqro().s[122]++;
          image.src = url;
        });
      });
      function preloadSingleImage(_x8) {
        return _preloadSingleImage.apply(this, arguments);
      }
      return preloadSingleImage;
    }()
  }]);
}();
export var imageOptimizationService = (cov_1vw0dzcqro().s[123]++, new ImageOptimizationService());
export default imageOptimizationService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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