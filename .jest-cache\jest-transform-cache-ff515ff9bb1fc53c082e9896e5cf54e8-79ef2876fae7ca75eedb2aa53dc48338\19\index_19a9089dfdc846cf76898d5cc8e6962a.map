{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_interopRequireDefault", "exports", "__esModule", "_objectSpread2", "_extends2", "_objectWithoutPropertiesLoose2", "_Dimensions", "_dismissKeyboard", "_invariant", "_mergeRefs", "_Platform", "_ScrollViewBase", "_StyleSheet", "_TextInputState", "_UIManager", "_View", "_react", "_warning", "_excluded", "emptyObject", "IS_ANIMATING_TOUCH_START_THRESHOLD_MS", "ScrollView", "_react$default$Compon", "_this", "arguments", "_scrollNodeRef", "_innerViewRef", "isTouching", "lastMomentumScrollBeginTime", "lastMomentumScrollEndTime", "observedScrollSinceBecomingResponder", "becameResponderWhileAnimating", "scrollResponderHandleScrollShouldSetResponder", "scrollResponderHandleStartShouldSetResponderCapture", "scrollResponderIsAnimating", "scrollResponderHandleTerminationRequest", "scrollResponderHandleTouchEnd", "nativeEvent", "touches", "length", "props", "onTouchEnd", "scrollResponderHandleResponderRelease", "onResponderRelease", "currentlyFocusedTextInput", "currentlyFocusedField", "keyboardShouldPersistTaps", "target", "onScrollResponderKeyboardDismissed", "blurTextInput", "scrollResponderHandleScroll", "onScroll", "scrollResponderHandleResponderGrant", "onResponderGrant", "scrollResponderHandleScrollBeginDrag", "onScrollBeginDrag", "scrollResponderHandleScrollEndDrag", "onScrollEndDrag", "scrollResponderHandleMomentumScrollBegin", "Date", "now", "onMomentumScrollBegin", "scrollResponderHandleMomentumScrollEnd", "onMomentumScrollEnd", "scrollResponderHandleTouchStart", "onTouchStart", "scrollResponderHandleTouchMove", "onTouchMove", "timeSinceLastMomentumScrollEnd", "isAnimating", "scrollResponderScrollTo", "x", "y", "animated", "console", "warn", "_ref", "node", "getScrollableNode", "left", "top", "scroll", "behavior", "scrollLeft", "scrollTop", "scrollResponderZoomTo", "rect", "OS", "scrollResponderScrollNativeHandleToKeyboard", "nodeHandle", "additionalOffset", "preventNegativeScrollOffset", "additionalScrollOffset", "measureLayout", "getInnerViewNode", "scrollResponderTextInputFocusError", "scrollResponderInputMeasureAndScrollToKeyboard", "width", "height", "keyboardScreenY", "get", "keyboardWillOpenTo", "endCoordinates", "screenY", "scrollOffsetY", "Math", "max", "scrollResponderKeyboardWillShow", "onKeyboardWillShow", "scrollResponderKeyboardWillHide", "onKeyboardWillHide", "scrollResponderKeyboardDidShow", "onKeyboardDidShow", "scrollResponderKeyboardDidHide", "onKeyboardDidHide", "flashScrollIndicators", "scrollResponderFlashScrollIndicators", "getScrollResponder", "getInnerViewRef", "getNativeScrollRef", "scrollTo", "_ref2", "scrollToEnd", "options", "horizontal", "scrollResponderNode", "scrollWidth", "scrollHeight", "_handleContentOnLayout", "_e$nativeEvent$layout", "layout", "onContentSizeChange", "_handleScroll", "process", "env", "NODE_ENV", "scrollEventThrottle", "log", "keyboardDismissMode", "_setInnerViewRef", "_setScrollNodeRef", "ref", "forwardedRef", "key", "value", "scrollResponderHandleStartShouldSetResponder", "scrollResponderHandleResponderReject", "error", "render", "_this$props", "contentContainerStyle", "refreshControl", "stickyHeaderIndices", "pagingEnabled", "centerContent", "other", "style", "flatten", "childLayoutProps", "filter", "prop", "undefined", "JSON", "stringify", "contentSizeChangeProps", "onLayout", "hasStickyHeaderIndices", "Array", "isArray", "children", "Children", "map", "child", "i", "isSticky", "indexOf", "createElement", "styles", "<PERSON><PERSON><PERSON><PERSON>", "paging<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contentContainer", "collapsable", "contentContainerHorizontal", "contentContainerCenterContent", "baseStyle", "baseHorizontal", "baseVertical", "pagingEnabledStyle", "pagingEnabledHorizontal", "pagingEnabledVertical", "onStartShouldSetResponder", "onStartShouldSetResponderCapture", "onScrollShouldSetResponder", "onResponderTerminationRequest", "onResponderTerminate", "scrollResponderHandleTerminate", "onResponderReject", "ScrollViewClass", "scrollView", "cloneElement", "Component", "commonStyle", "flexGrow", "flexShrink", "transform", "WebkitOverflowScrolling", "create", "flexDirection", "overflowX", "overflowY", "justifyContent", "position", "zIndex", "scrollSnapType", "scrollSnapAlign", "ForwardedScrollView", "forwardRef", "displayName", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _Dimensions = _interopRequireDefault(require(\"../Dimensions\"));\nvar _dismissKeyboard = _interopRequireDefault(require(\"../../modules/dismissKeyboard\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _mergeRefs = _interopRequireDefault(require(\"../../modules/mergeRefs\"));\nvar _Platform = _interopRequireDefault(require(\"../Platform\"));\nvar _ScrollViewBase = _interopRequireDefault(require(\"./ScrollViewBase\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _TextInputState = _interopRequireDefault(require(\"../../modules/TextInputState\"));\nvar _UIManager = _interopRequireDefault(require(\"../UIManager\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _warning = _interopRequireDefault(require(\"fbjs/lib/warning\"));\nvar _excluded = [\"contentContainerStyle\", \"horizontal\", \"onContentSizeChange\", \"refreshControl\", \"stickyHeaderIndices\", \"pagingEnabled\", \"forwardedRef\", \"keyboardDismissMode\", \"onScroll\", \"centerContent\"];\nvar emptyObject = {};\nvar IS_ANIMATING_TOUCH_START_THRESHOLD_MS = 16;\nclass ScrollView extends _react.default.Component {\n  constructor() {\n    super(...arguments);\n    this._scrollNodeRef = null;\n    this._innerViewRef = null;\n    this.isTouching = false;\n    this.lastMomentumScrollBeginTime = 0;\n    this.lastMomentumScrollEndTime = 0;\n    this.observedScrollSinceBecomingResponder = false;\n    this.becameResponderWhileAnimating = false;\n    this.scrollResponderHandleScrollShouldSetResponder = () => {\n      return this.isTouching;\n    };\n    this.scrollResponderHandleStartShouldSetResponderCapture = e => {\n      // First see if we want to eat taps while the keyboard is up\n      // var currentlyFocusedTextInput = TextInputState.currentlyFocusedField();\n      // if (!this.props.keyboardShouldPersistTaps &&\n      //   currentlyFocusedTextInput != null &&\n      //   e.target !== currentlyFocusedTextInput) {\n      //   return true;\n      // }\n      return this.scrollResponderIsAnimating();\n    };\n    this.scrollResponderHandleTerminationRequest = () => {\n      return !this.observedScrollSinceBecomingResponder;\n    };\n    this.scrollResponderHandleTouchEnd = e => {\n      var nativeEvent = e.nativeEvent;\n      this.isTouching = nativeEvent.touches.length !== 0;\n      this.props.onTouchEnd && this.props.onTouchEnd(e);\n    };\n    this.scrollResponderHandleResponderRelease = e => {\n      this.props.onResponderRelease && this.props.onResponderRelease(e);\n\n      // By default scroll views will unfocus a textField\n      // if another touch occurs outside of it\n      var currentlyFocusedTextInput = _TextInputState.default.currentlyFocusedField();\n      if (!this.props.keyboardShouldPersistTaps && currentlyFocusedTextInput != null && e.target !== currentlyFocusedTextInput && !this.observedScrollSinceBecomingResponder && !this.becameResponderWhileAnimating) {\n        this.props.onScrollResponderKeyboardDismissed && this.props.onScrollResponderKeyboardDismissed(e);\n        _TextInputState.default.blurTextInput(currentlyFocusedTextInput);\n      }\n    };\n    this.scrollResponderHandleScroll = e => {\n      this.observedScrollSinceBecomingResponder = true;\n      this.props.onScroll && this.props.onScroll(e);\n    };\n    this.scrollResponderHandleResponderGrant = e => {\n      this.observedScrollSinceBecomingResponder = false;\n      this.props.onResponderGrant && this.props.onResponderGrant(e);\n      this.becameResponderWhileAnimating = this.scrollResponderIsAnimating();\n    };\n    this.scrollResponderHandleScrollBeginDrag = e => {\n      this.props.onScrollBeginDrag && this.props.onScrollBeginDrag(e);\n    };\n    this.scrollResponderHandleScrollEndDrag = e => {\n      this.props.onScrollEndDrag && this.props.onScrollEndDrag(e);\n    };\n    this.scrollResponderHandleMomentumScrollBegin = e => {\n      this.lastMomentumScrollBeginTime = Date.now();\n      this.props.onMomentumScrollBegin && this.props.onMomentumScrollBegin(e);\n    };\n    this.scrollResponderHandleMomentumScrollEnd = e => {\n      this.lastMomentumScrollEndTime = Date.now();\n      this.props.onMomentumScrollEnd && this.props.onMomentumScrollEnd(e);\n    };\n    this.scrollResponderHandleTouchStart = e => {\n      this.isTouching = true;\n      this.props.onTouchStart && this.props.onTouchStart(e);\n    };\n    this.scrollResponderHandleTouchMove = e => {\n      this.props.onTouchMove && this.props.onTouchMove(e);\n    };\n    this.scrollResponderIsAnimating = () => {\n      var now = Date.now();\n      var timeSinceLastMomentumScrollEnd = now - this.lastMomentumScrollEndTime;\n      var isAnimating = timeSinceLastMomentumScrollEnd < IS_ANIMATING_TOUCH_START_THRESHOLD_MS || this.lastMomentumScrollEndTime < this.lastMomentumScrollBeginTime;\n      return isAnimating;\n    };\n    this.scrollResponderScrollTo = (x, y, animated) => {\n      if (typeof x === 'number') {\n        console.warn('`scrollResponderScrollTo(x, y, animated)` is deprecated. Use `scrollResponderScrollTo({x: 5, y: 5, animated: true})` instead.');\n      } else {\n        var _ref = x || emptyObject;\n        x = _ref.x;\n        y = _ref.y;\n        animated = _ref.animated;\n      }\n      var node = this.getScrollableNode();\n      var left = x || 0;\n      var top = y || 0;\n      if (node != null) {\n        if (typeof node.scroll === 'function') {\n          node.scroll({\n            top,\n            left,\n            behavior: !animated ? 'auto' : 'smooth'\n          });\n        } else {\n          node.scrollLeft = left;\n          node.scrollTop = top;\n        }\n      }\n    };\n    this.scrollResponderZoomTo = (rect, animated) => {\n      if (_Platform.default.OS !== 'ios') {\n        (0, _invariant.default)('zoomToRect is not implemented');\n      }\n    };\n    this.scrollResponderScrollNativeHandleToKeyboard = (nodeHandle, additionalOffset, preventNegativeScrollOffset) => {\n      this.additionalScrollOffset = additionalOffset || 0;\n      this.preventNegativeScrollOffset = !!preventNegativeScrollOffset;\n      _UIManager.default.measureLayout(nodeHandle, this.getInnerViewNode(), this.scrollResponderTextInputFocusError, this.scrollResponderInputMeasureAndScrollToKeyboard);\n    };\n    this.scrollResponderInputMeasureAndScrollToKeyboard = (left, top, width, height) => {\n      var keyboardScreenY = _Dimensions.default.get('window').height;\n      if (this.keyboardWillOpenTo) {\n        keyboardScreenY = this.keyboardWillOpenTo.endCoordinates.screenY;\n      }\n      var scrollOffsetY = top - keyboardScreenY + height + this.additionalScrollOffset;\n\n      // By default, this can scroll with negative offset, pulling the content\n      // down so that the target component's bottom meets the keyboard's top.\n      // If requested otherwise, cap the offset at 0 minimum to avoid content\n      // shifting down.\n      if (this.preventNegativeScrollOffset) {\n        scrollOffsetY = Math.max(0, scrollOffsetY);\n      }\n      this.scrollResponderScrollTo({\n        x: 0,\n        y: scrollOffsetY,\n        animated: true\n      });\n      this.additionalOffset = 0;\n      this.preventNegativeScrollOffset = false;\n    };\n    this.scrollResponderKeyboardWillShow = e => {\n      this.keyboardWillOpenTo = e;\n      this.props.onKeyboardWillShow && this.props.onKeyboardWillShow(e);\n    };\n    this.scrollResponderKeyboardWillHide = e => {\n      this.keyboardWillOpenTo = null;\n      this.props.onKeyboardWillHide && this.props.onKeyboardWillHide(e);\n    };\n    this.scrollResponderKeyboardDidShow = e => {\n      // TODO(7693961): The event for DidShow is not available on iOS yet.\n      // Use the one from WillShow and do not assign.\n      if (e) {\n        this.keyboardWillOpenTo = e;\n      }\n      this.props.onKeyboardDidShow && this.props.onKeyboardDidShow(e);\n    };\n    this.scrollResponderKeyboardDidHide = e => {\n      this.keyboardWillOpenTo = null;\n      this.props.onKeyboardDidHide && this.props.onKeyboardDidHide(e);\n    };\n    this.flashScrollIndicators = () => {\n      this.scrollResponderFlashScrollIndicators();\n    };\n    this.getScrollResponder = () => {\n      return this;\n    };\n    this.getScrollableNode = () => {\n      return this._scrollNodeRef;\n    };\n    this.getInnerViewRef = () => {\n      return this._innerViewRef;\n    };\n    this.getInnerViewNode = () => {\n      return this._innerViewRef;\n    };\n    this.getNativeScrollRef = () => {\n      return this._scrollNodeRef;\n    };\n    this.scrollTo = (y, x, animated) => {\n      if (typeof y === 'number') {\n        console.warn('`scrollTo(y, x, animated)` is deprecated. Use `scrollTo({x: 5, y: 5, animated: true})` instead.');\n      } else {\n        var _ref2 = y || emptyObject;\n        x = _ref2.x;\n        y = _ref2.y;\n        animated = _ref2.animated;\n      }\n      this.scrollResponderScrollTo({\n        x: x || 0,\n        y: y || 0,\n        animated: animated !== false\n      });\n    };\n    this.scrollToEnd = options => {\n      // Default to true\n      var animated = (options && options.animated) !== false;\n      var horizontal = this.props.horizontal;\n      var scrollResponderNode = this.getScrollableNode();\n      var x = horizontal ? scrollResponderNode.scrollWidth : 0;\n      var y = horizontal ? 0 : scrollResponderNode.scrollHeight;\n      this.scrollResponderScrollTo({\n        x,\n        y,\n        animated\n      });\n    };\n    this._handleContentOnLayout = e => {\n      var _e$nativeEvent$layout = e.nativeEvent.layout,\n        width = _e$nativeEvent$layout.width,\n        height = _e$nativeEvent$layout.height;\n      this.props.onContentSizeChange(width, height);\n    };\n    this._handleScroll = e => {\n      if (process.env.NODE_ENV !== 'production') {\n        if (this.props.onScroll && this.props.scrollEventThrottle == null) {\n          console.log('You specified `onScroll` on a <ScrollView> but not ' + '`scrollEventThrottle`. You will only receive one event. ' + 'Using `16` you get all the events but be aware that it may ' + \"cause frame drops, use a bigger number if you don't need as \" + 'much precision.');\n        }\n      }\n      if (this.props.keyboardDismissMode === 'on-drag') {\n        (0, _dismissKeyboard.default)();\n      }\n      this.scrollResponderHandleScroll(e);\n    };\n    this._setInnerViewRef = node => {\n      this._innerViewRef = node;\n    };\n    this._setScrollNodeRef = node => {\n      this._scrollNodeRef = node;\n      // ScrollView needs to add more methods to the hostNode in addition to those\n      // added by `usePlatformMethods`. This is temporarily until an API like\n      // `ScrollView.scrollTo(hostNode, { x, y })` is added to React Native.\n      if (node != null) {\n        node.getScrollResponder = this.getScrollResponder;\n        node.getInnerViewNode = this.getInnerViewNode;\n        node.getInnerViewRef = this.getInnerViewRef;\n        node.getNativeScrollRef = this.getNativeScrollRef;\n        node.getScrollableNode = this.getScrollableNode;\n        node.scrollTo = this.scrollTo;\n        node.scrollToEnd = this.scrollToEnd;\n        node.flashScrollIndicators = this.flashScrollIndicators;\n        node.scrollResponderZoomTo = this.scrollResponderZoomTo;\n        node.scrollResponderScrollNativeHandleToKeyboard = this.scrollResponderScrollNativeHandleToKeyboard;\n      }\n      var ref = (0, _mergeRefs.default)(this.props.forwardedRef);\n      ref(node);\n    };\n  }\n  /**\n   * ------------------------------------------------------\n   * START SCROLLRESPONDER\n   * ------------------------------------------------------\n   */\n  // Reset to false every time becomes responder. This is used to:\n  // - Determine if the scroll view has been scrolled and therefore should\n  // refuse to give up its responder lock.\n  // - Determine if releasing should dismiss the keyboard when we are in\n  // tap-to-dismiss mode (!this.props.keyboardShouldPersistTaps).\n  /**\n   * Invoke this from an `onScroll` event.\n   */\n  /**\n   * Merely touch starting is not sufficient for a scroll view to become the\n   * responder. Being the \"responder\" means that the very next touch move/end\n   * event will result in an action/movement.\n   *\n   * Invoke this from an `onStartShouldSetResponder` event.\n   *\n   * `onStartShouldSetResponder` is used when the next move/end will trigger\n   * some UI movement/action, but when you want to yield priority to views\n   * nested inside of the view.\n   *\n   * There may be some cases where scroll views actually should return `true`\n   * from `onStartShouldSetResponder`: Any time we are detecting a standard tap\n   * that gives priority to nested views.\n   *\n   * - If a single tap on the scroll view triggers an action such as\n   *   recentering a map style view yet wants to give priority to interaction\n   *   views inside (such as dropped pins or labels), then we would return true\n   *   from this method when there is a single touch.\n   *\n   * - Similar to the previous case, if a two finger \"tap\" should trigger a\n   *   zoom, we would check the `touches` count, and if `>= 2`, we would return\n   *   true.\n   *\n   */\n  scrollResponderHandleStartShouldSetResponder() {\n    return false;\n  }\n\n  /**\n   * There are times when the scroll view wants to become the responder\n   * (meaning respond to the next immediate `touchStart/touchEnd`), in a way\n   * that *doesn't* give priority to nested views (hence the capture phase):\n   *\n   * - Currently animating.\n   * - Tapping anywhere that is not the focused input, while the keyboard is\n   *   up (which should dismiss the keyboard).\n   *\n   * Invoke this from an `onStartShouldSetResponderCapture` event.\n   */\n\n  /**\n   * Invoke this from an `onResponderReject` event.\n   *\n   * Some other element is not yielding its role as responder. Normally, we'd\n   * just disable the `UIScrollView`, but a touch has already began on it, the\n   * `UIScrollView` will not accept being disabled after that. The easiest\n   * solution for now is to accept the limitation of disallowing this\n   * altogether. To improve this, find a way to disable the `UIScrollView` after\n   * a touch has already started.\n   */\n  scrollResponderHandleResponderReject() {\n    (0, _warning.default)(false, \"ScrollView doesn't take rejection well - scrolls anyway\");\n  }\n\n  /**\n   * We will allow the scroll view to give up its lock iff it acquired the lock\n   * during an animation. This is a very useful default that happens to satisfy\n   * many common user experiences.\n   *\n   * - Stop a scroll on the left edge, then turn that into an outer view's\n   *   backswipe.\n   * - Stop a scroll mid-bounce at the top, continue pulling to have the outer\n   *   view dismiss.\n   * - However, without catching the scroll view mid-bounce (while it is\n   *   motionless), if you drag far enough for the scroll view to become\n   *   responder (and therefore drag the scroll view a bit), any backswipe\n   *   navigation of a swipe gesture higher in the view hierarchy, should be\n   *   rejected.\n   */\n\n  /**\n   * Invoke this from an `onTouchEnd` event.\n   *\n   * @param {SyntheticEvent} e Event.\n   */\n\n  /**\n   * Invoke this from an `onResponderRelease` event.\n   */\n\n  /**\n   * Invoke this from an `onResponderGrant` event.\n   */\n\n  /**\n   * Unfortunately, `onScrollBeginDrag` also fires when *stopping* the scroll\n   * animation, and there's not an easy way to distinguish a drag vs. stopping\n   * momentum.\n   *\n   * Invoke this from an `onScrollBeginDrag` event.\n   */\n\n  /**\n   * Invoke this from an `onScrollEndDrag` event.\n   */\n\n  /**\n   * Invoke this from an `onMomentumScrollBegin` event.\n   */\n\n  /**\n   * Invoke this from an `onMomentumScrollEnd` event.\n   */\n\n  /**\n   * Invoke this from an `onTouchStart` event.\n   *\n   * Since we know that the `SimpleEventPlugin` occurs later in the plugin\n   * order, after `ResponderEventPlugin`, we can detect that we were *not*\n   * permitted to be the responder (presumably because a contained view became\n   * responder). The `onResponderReject` won't fire in that case - it only\n   * fires when a *current* responder rejects our request.\n   *\n   * @param {SyntheticEvent} e Touch Start event.\n   */\n\n  /**\n   * Invoke this from an `onTouchMove` event.\n   *\n   * Since we know that the `SimpleEventPlugin` occurs later in the plugin\n   * order, after `ResponderEventPlugin`, we can detect that we were *not*\n   * permitted to be the responder (presumably because a contained view became\n   * responder). The `onResponderReject` won't fire in that case - it only\n   * fires when a *current* responder rejects our request.\n   *\n   * @param {SyntheticEvent} e Touch Start event.\n   */\n\n  /**\n   * A helper function for this class that lets us quickly determine if the\n   * view is currently animating. This is particularly useful to know when\n   * a touch has just started or ended.\n   */\n\n  /**\n   * A helper function to scroll to a specific point in the scrollview.\n   * This is currently used to help focus on child textviews, but can also\n   * be used to quickly scroll to any element we want to focus. Syntax:\n   *\n   * scrollResponderScrollTo(options: {x: number = 0; y: number = 0; animated: boolean = true})\n   *\n   * Note: The weird argument signature is due to the fact that, for historical reasons,\n   * the function also accepts separate arguments as as alternative to the options object.\n   * This is deprecated due to ambiguity (y before x), and SHOULD NOT BE USED.\n   */\n\n  /**\n   * A helper function to zoom to a specific rect in the scrollview. The argument has the shape\n   * {x: number; y: number; width: number; height: number; animated: boolean = true}\n   *\n   * @platform ios\n   */\n\n  /**\n   * Displays the scroll indicators momentarily.\n   */\n  scrollResponderFlashScrollIndicators() {}\n\n  /**\n   * This method should be used as the callback to onFocus in a TextInputs'\n   * parent view. Note that any module using this mixin needs to return\n   * the parent view's ref in getScrollViewRef() in order to use this method.\n   * @param {any} nodeHandle The TextInput node handle\n   * @param {number} additionalOffset The scroll view's top \"contentInset\".\n   *        Default is 0.\n   * @param {bool} preventNegativeScrolling Whether to allow pulling the content\n   *        down to make it meet the keyboard's top. Default is false.\n   */\n\n  /**\n   * The calculations performed here assume the scroll view takes up the entire\n   * screen - even if has some content inset. We then measure the offsets of the\n   * keyboard, and compensate both for the scroll view's \"contentInset\".\n   *\n   * @param {number} left Position of input w.r.t. table view.\n   * @param {number} top Position of input w.r.t. table view.\n   * @param {number} width Width of the text input.\n   * @param {number} height Height of the text input.\n   */\n\n  scrollResponderTextInputFocusError(e) {\n    console.error('Error measuring text field: ', e);\n  }\n\n  /**\n   * Warning, this may be called several times for a single keyboard opening.\n   * It's best to store the information in this method and then take any action\n   * at a later point (either in `keyboardDidShow` or other).\n   *\n   * Here's the order that events occur in:\n   * - focus\n   * - willShow {startCoordinates, endCoordinates} several times\n   * - didShow several times\n   * - blur\n   * - willHide {startCoordinates, endCoordinates} several times\n   * - didHide several times\n   *\n   * The `ScrollResponder` providesModule callbacks for each of these events.\n   * Even though any user could have easily listened to keyboard events\n   * themselves, using these `props` callbacks ensures that ordering of events\n   * is consistent - and not dependent on the order that the keyboard events are\n   * subscribed to. This matters when telling the scroll view to scroll to where\n   * the keyboard is headed - the scroll responder better have been notified of\n   * the keyboard destination before being instructed to scroll to where the\n   * keyboard will be. Stick to the `ScrollResponder` callbacks, and everything\n   * will work.\n   *\n   * WARNING: These callbacks will fire even if a keyboard is displayed in a\n   * different navigation pane. Filter out the events to determine if they are\n   * relevant to you. (For example, only if you receive these callbacks after\n   * you had explicitly focused a node etc).\n   */\n\n  /**\n   * ------------------------------------------------------\n   * END SCROLLRESPONDER\n   * ------------------------------------------------------\n   */\n\n  /**\n   * Returns a reference to the underlying scroll responder, which supports\n   * operations like `scrollTo`. All ScrollView-like components should\n   * implement this method so that they can be composed while providing access\n   * to the underlying scroll responder's methods.\n   */\n\n  /**\n   * Scrolls to a given x, y offset, either immediately or with a smooth animation.\n   * Syntax:\n   *\n   * scrollTo(options: {x: number = 0; y: number = 0; animated: boolean = true})\n   *\n   * Note: The weird argument signature is due to the fact that, for historical reasons,\n   * the function also accepts separate arguments as as alternative to the options object.\n   * This is deprecated due to ambiguity (y before x), and SHOULD NOT BE USED.\n   */\n\n  /**\n   * If this is a vertical ScrollView scrolls to the bottom.\n   * If this is a horizontal ScrollView scrolls to the right.\n   *\n   * Use `scrollToEnd({ animated: true })` for smooth animated scrolling,\n   * `scrollToEnd({ animated: false })` for immediate scrolling.\n   * If no options are passed, `animated` defaults to true.\n   */\n\n  render() {\n    var _this$props = this.props,\n      contentContainerStyle = _this$props.contentContainerStyle,\n      horizontal = _this$props.horizontal,\n      onContentSizeChange = _this$props.onContentSizeChange,\n      refreshControl = _this$props.refreshControl,\n      stickyHeaderIndices = _this$props.stickyHeaderIndices,\n      pagingEnabled = _this$props.pagingEnabled,\n      forwardedRef = _this$props.forwardedRef,\n      keyboardDismissMode = _this$props.keyboardDismissMode,\n      onScroll = _this$props.onScroll,\n      centerContent = _this$props.centerContent,\n      other = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);\n    if (process.env.NODE_ENV !== 'production' && this.props.style) {\n      var style = _StyleSheet.default.flatten(this.props.style);\n      var childLayoutProps = ['alignItems', 'justifyContent'].filter(prop => style && style[prop] !== undefined);\n      (0, _invariant.default)(childLayoutProps.length === 0, \"ScrollView child layout (\" + JSON.stringify(childLayoutProps) + \") \" + 'must be applied through the contentContainerStyle prop.');\n    }\n    var contentSizeChangeProps = {};\n    if (onContentSizeChange) {\n      contentSizeChangeProps = {\n        onLayout: this._handleContentOnLayout\n      };\n    }\n    var hasStickyHeaderIndices = !horizontal && Array.isArray(stickyHeaderIndices);\n    var children = hasStickyHeaderIndices || pagingEnabled ? _react.default.Children.map(this.props.children, (child, i) => {\n      var isSticky = hasStickyHeaderIndices && stickyHeaderIndices.indexOf(i) > -1;\n      if (child != null && (isSticky || pagingEnabled)) {\n        return /*#__PURE__*/_react.default.createElement(_View.default, {\n          style: [isSticky && styles.stickyHeader, pagingEnabled && styles.pagingEnabledChild]\n        }, child);\n      } else {\n        return child;\n      }\n    }) : this.props.children;\n    var contentContainer = /*#__PURE__*/_react.default.createElement(_View.default, (0, _extends2.default)({}, contentSizeChangeProps, {\n      children: children,\n      collapsable: false,\n      ref: this._setInnerViewRef,\n      style: [horizontal && styles.contentContainerHorizontal, centerContent && styles.contentContainerCenterContent, contentContainerStyle]\n    }));\n    var baseStyle = horizontal ? styles.baseHorizontal : styles.baseVertical;\n    var pagingEnabledStyle = horizontal ? styles.pagingEnabledHorizontal : styles.pagingEnabledVertical;\n    var props = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, other), {}, {\n      style: [baseStyle, pagingEnabled && pagingEnabledStyle, this.props.style],\n      onTouchStart: this.scrollResponderHandleTouchStart,\n      onTouchMove: this.scrollResponderHandleTouchMove,\n      onTouchEnd: this.scrollResponderHandleTouchEnd,\n      onScrollBeginDrag: this.scrollResponderHandleScrollBeginDrag,\n      onScrollEndDrag: this.scrollResponderHandleScrollEndDrag,\n      onMomentumScrollBegin: this.scrollResponderHandleMomentumScrollBegin,\n      onMomentumScrollEnd: this.scrollResponderHandleMomentumScrollEnd,\n      onStartShouldSetResponder: this.scrollResponderHandleStartShouldSetResponder,\n      onStartShouldSetResponderCapture: this.scrollResponderHandleStartShouldSetResponderCapture,\n      onScrollShouldSetResponder: this.scrollResponderHandleScrollShouldSetResponder,\n      onScroll: this._handleScroll,\n      onResponderGrant: this.scrollResponderHandleResponderGrant,\n      onResponderTerminationRequest: this.scrollResponderHandleTerminationRequest,\n      onResponderTerminate: this.scrollResponderHandleTerminate,\n      onResponderRelease: this.scrollResponderHandleResponderRelease,\n      onResponderReject: this.scrollResponderHandleResponderReject\n    });\n    var ScrollViewClass = _ScrollViewBase.default;\n    (0, _invariant.default)(ScrollViewClass !== undefined, 'ScrollViewClass must not be undefined');\n    var scrollView = /*#__PURE__*/_react.default.createElement(ScrollViewClass, (0, _extends2.default)({}, props, {\n      ref: this._setScrollNodeRef\n    }), contentContainer);\n    if (refreshControl) {\n      return /*#__PURE__*/_react.default.cloneElement(refreshControl, {\n        style: props.style\n      }, scrollView);\n    }\n    return scrollView;\n  }\n}\nvar commonStyle = {\n  flexGrow: 1,\n  flexShrink: 1,\n  // Enable hardware compositing in modern browsers.\n  // Creates a new layer with its own backing surface that can significantly\n  // improve scroll performance.\n  transform: 'translateZ(0)',\n  // iOS native scrolling\n  WebkitOverflowScrolling: 'touch'\n};\nvar styles = _StyleSheet.default.create({\n  baseVertical: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, commonStyle), {}, {\n    flexDirection: 'column',\n    overflowX: 'hidden',\n    overflowY: 'auto'\n  }),\n  baseHorizontal: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, commonStyle), {}, {\n    flexDirection: 'row',\n    overflowX: 'auto',\n    overflowY: 'hidden'\n  }),\n  contentContainerHorizontal: {\n    flexDirection: 'row'\n  },\n  contentContainerCenterContent: {\n    justifyContent: 'center',\n    flexGrow: 1\n  },\n  stickyHeader: {\n    position: 'sticky',\n    top: 0,\n    zIndex: 10\n  },\n  pagingEnabledHorizontal: {\n    scrollSnapType: 'x mandatory'\n  },\n  pagingEnabledVertical: {\n    scrollSnapType: 'y mandatory'\n  },\n  pagingEnabledChild: {\n    scrollSnapAlign: 'start'\n  }\n});\nvar ForwardedScrollView = /*#__PURE__*/_react.default.forwardRef((props, forwardedRef) => {\n  return /*#__PURE__*/_react.default.createElement(ScrollView, (0, _extends2.default)({}, props, {\n    forwardedRef: forwardedRef\n  }));\n});\nForwardedScrollView.displayName = 'ScrollView';\nvar _default = exports.default = ForwardedScrollView;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,SAAAM,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAM,OAAA,EAAAF,CAAA,OAAAL,2BAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAM,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEb,IAAIa,sBAAsB,GAAGpB,OAAO,CAAC,8CAA8C,CAAC,CAACU,OAAO;AAC5FW,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACX,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIa,cAAc,GAAGH,sBAAsB,CAACpB,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIwB,SAAS,GAAGJ,sBAAsB,CAACpB,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIyB,8BAA8B,GAAGL,sBAAsB,CAACpB,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAI0B,WAAW,GAAGN,sBAAsB,CAACpB,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAI2B,gBAAgB,GAAGP,sBAAsB,CAACpB,OAAO,gCAAgC,CAAC,CAAC;AACvF,IAAI4B,UAAU,GAAGR,sBAAsB,CAACpB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAI6B,UAAU,GAAGT,sBAAsB,CAACpB,OAAO,0BAA0B,CAAC,CAAC;AAC3E,IAAI8B,SAAS,GAAGV,sBAAsB,CAACpB,OAAO,cAAc,CAAC,CAAC;AAC9D,IAAI+B,eAAe,GAAGX,sBAAsB,CAACpB,OAAO,mBAAmB,CAAC,CAAC;AACzE,IAAIgC,WAAW,GAAGZ,sBAAsB,CAACpB,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIiC,eAAe,GAAGb,sBAAsB,CAACpB,OAAO,+BAA+B,CAAC,CAAC;AACrF,IAAIkC,UAAU,GAAGd,sBAAsB,CAACpB,OAAO,eAAe,CAAC,CAAC;AAChE,IAAImC,KAAK,GAAGf,sBAAsB,CAACpB,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIoC,MAAM,GAAGhB,sBAAsB,CAACpB,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIqC,QAAQ,GAAGjB,sBAAsB,CAACpB,OAAO,CAAC,kBAAkB,CAAC,CAAC;AAClE,IAAIsC,SAAS,GAAG,CAAC,uBAAuB,EAAE,YAAY,EAAE,qBAAqB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,eAAe,EAAE,cAAc,EAAE,qBAAqB,EAAE,UAAU,EAAE,eAAe,CAAC;AAC5M,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,qCAAqC,GAAG,EAAE;AAAC,IACzCC,UAAU,aAAAC,qBAAA;EACd,SAAAD,WAAA,EAAc;IAAA,IAAAE,KAAA;IAAA,IAAA1C,gBAAA,CAAAS,OAAA,QAAA+B,UAAA;IACZE,KAAA,GAAArC,UAAA,OAAAmC,UAAA,EAASG,SAAS;IAClBD,KAAA,CAAKE,cAAc,GAAG,IAAI;IAC1BF,KAAA,CAAKG,aAAa,GAAG,IAAI;IACzBH,KAAA,CAAKI,UAAU,GAAG,KAAK;IACvBJ,KAAA,CAAKK,2BAA2B,GAAG,CAAC;IACpCL,KAAA,CAAKM,yBAAyB,GAAG,CAAC;IAClCN,KAAA,CAAKO,oCAAoC,GAAG,KAAK;IACjDP,KAAA,CAAKQ,6BAA6B,GAAG,KAAK;IAC1CR,KAAA,CAAKS,6CAA6C,GAAG,YAAM;MACzD,OAAOT,KAAA,CAAKI,UAAU;IACxB,CAAC;IACDJ,KAAA,CAAKU,mDAAmD,GAAG,UAAA5C,CAAC,EAAI;MAQ9D,OAAOkC,KAAA,CAAKW,0BAA0B,CAAC,CAAC;IAC1C,CAAC;IACDX,KAAA,CAAKY,uCAAuC,GAAG,YAAM;MACnD,OAAO,CAACZ,KAAA,CAAKO,oCAAoC;IACnD,CAAC;IACDP,KAAA,CAAKa,6BAA6B,GAAG,UAAA/C,CAAC,EAAI;MACxC,IAAIgD,WAAW,GAAGhD,CAAC,CAACgD,WAAW;MAC/Bd,KAAA,CAAKI,UAAU,GAAGU,WAAW,CAACC,OAAO,CAACC,MAAM,KAAK,CAAC;MAClDhB,KAAA,CAAKiB,KAAK,CAACC,UAAU,IAAIlB,KAAA,CAAKiB,KAAK,CAACC,UAAU,CAACpD,CAAC,CAAC;IACnD,CAAC;IACDkC,KAAA,CAAKmB,qCAAqC,GAAG,UAAArD,CAAC,EAAI;MAChDkC,KAAA,CAAKiB,KAAK,CAACG,kBAAkB,IAAIpB,KAAA,CAAKiB,KAAK,CAACG,kBAAkB,CAACtD,CAAC,CAAC;MAIjE,IAAIuD,yBAAyB,GAAG/B,eAAe,CAACvB,OAAO,CAACuD,qBAAqB,CAAC,CAAC;MAC/E,IAAI,CAACtB,KAAA,CAAKiB,KAAK,CAACM,yBAAyB,IAAIF,yBAAyB,IAAI,IAAI,IAAIvD,CAAC,CAAC0D,MAAM,KAAKH,yBAAyB,IAAI,CAACrB,KAAA,CAAKO,oCAAoC,IAAI,CAACP,KAAA,CAAKQ,6BAA6B,EAAE;QAC7MR,KAAA,CAAKiB,KAAK,CAACQ,kCAAkC,IAAIzB,KAAA,CAAKiB,KAAK,CAACQ,kCAAkC,CAAC3D,CAAC,CAAC;QACjGwB,eAAe,CAACvB,OAAO,CAAC2D,aAAa,CAACL,yBAAyB,CAAC;MAClE;IACF,CAAC;IACDrB,KAAA,CAAK2B,2BAA2B,GAAG,UAAA7D,CAAC,EAAI;MACtCkC,KAAA,CAAKO,oCAAoC,GAAG,IAAI;MAChDP,KAAA,CAAKiB,KAAK,CAACW,QAAQ,IAAI5B,KAAA,CAAKiB,KAAK,CAACW,QAAQ,CAAC9D,CAAC,CAAC;IAC/C,CAAC;IACDkC,KAAA,CAAK6B,mCAAmC,GAAG,UAAA/D,CAAC,EAAI;MAC9CkC,KAAA,CAAKO,oCAAoC,GAAG,KAAK;MACjDP,KAAA,CAAKiB,KAAK,CAACa,gBAAgB,IAAI9B,KAAA,CAAKiB,KAAK,CAACa,gBAAgB,CAAChE,CAAC,CAAC;MAC7DkC,KAAA,CAAKQ,6BAA6B,GAAGR,KAAA,CAAKW,0BAA0B,CAAC,CAAC;IACxE,CAAC;IACDX,KAAA,CAAK+B,oCAAoC,GAAG,UAAAjE,CAAC,EAAI;MAC/CkC,KAAA,CAAKiB,KAAK,CAACe,iBAAiB,IAAIhC,KAAA,CAAKiB,KAAK,CAACe,iBAAiB,CAAClE,CAAC,CAAC;IACjE,CAAC;IACDkC,KAAA,CAAKiC,kCAAkC,GAAG,UAAAnE,CAAC,EAAI;MAC7CkC,KAAA,CAAKiB,KAAK,CAACiB,eAAe,IAAIlC,KAAA,CAAKiB,KAAK,CAACiB,eAAe,CAACpE,CAAC,CAAC;IAC7D,CAAC;IACDkC,KAAA,CAAKmC,wCAAwC,GAAG,UAAArE,CAAC,EAAI;MACnDkC,KAAA,CAAKK,2BAA2B,GAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC7CrC,KAAA,CAAKiB,KAAK,CAACqB,qBAAqB,IAAItC,KAAA,CAAKiB,KAAK,CAACqB,qBAAqB,CAACxE,CAAC,CAAC;IACzE,CAAC;IACDkC,KAAA,CAAKuC,sCAAsC,GAAG,UAAAzE,CAAC,EAAI;MACjDkC,KAAA,CAAKM,yBAAyB,GAAG8B,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3CrC,KAAA,CAAKiB,KAAK,CAACuB,mBAAmB,IAAIxC,KAAA,CAAKiB,KAAK,CAACuB,mBAAmB,CAAC1E,CAAC,CAAC;IACrE,CAAC;IACDkC,KAAA,CAAKyC,+BAA+B,GAAG,UAAA3E,CAAC,EAAI;MAC1CkC,KAAA,CAAKI,UAAU,GAAG,IAAI;MACtBJ,KAAA,CAAKiB,KAAK,CAACyB,YAAY,IAAI1C,KAAA,CAAKiB,KAAK,CAACyB,YAAY,CAAC5E,CAAC,CAAC;IACvD,CAAC;IACDkC,KAAA,CAAK2C,8BAA8B,GAAG,UAAA7E,CAAC,EAAI;MACzCkC,KAAA,CAAKiB,KAAK,CAAC2B,WAAW,IAAI5C,KAAA,CAAKiB,KAAK,CAAC2B,WAAW,CAAC9E,CAAC,CAAC;IACrD,CAAC;IACDkC,KAAA,CAAKW,0BAA0B,GAAG,YAAM;MACtC,IAAI0B,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIQ,8BAA8B,GAAGR,GAAG,GAAGrC,KAAA,CAAKM,yBAAyB;MACzE,IAAIwC,WAAW,GAAGD,8BAA8B,GAAGhD,qCAAqC,IAAIG,KAAA,CAAKM,yBAAyB,GAAGN,KAAA,CAAKK,2BAA2B;MAC7J,OAAOyC,WAAW;IACpB,CAAC;IACD9C,KAAA,CAAK+C,uBAAuB,GAAG,UAACC,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAK;MACjD,IAAI,OAAOF,CAAC,KAAK,QAAQ,EAAE;QACzBG,OAAO,CAACC,IAAI,CAAC,+HAA+H,CAAC;MAC/I,CAAC,MAAM;QACL,IAAIC,IAAI,GAAGL,CAAC,IAAIpD,WAAW;QAC3BoD,CAAC,GAAGK,IAAI,CAACL,CAAC;QACVC,CAAC,GAAGI,IAAI,CAACJ,CAAC;QACVC,QAAQ,GAAGG,IAAI,CAACH,QAAQ;MAC1B;MACA,IAAII,IAAI,GAAGtD,KAAA,CAAKuD,iBAAiB,CAAC,CAAC;MACnC,IAAIC,IAAI,GAAGR,CAAC,IAAI,CAAC;MACjB,IAAIS,GAAG,GAAGR,CAAC,IAAI,CAAC;MAChB,IAAIK,IAAI,IAAI,IAAI,EAAE;QAChB,IAAI,OAAOA,IAAI,CAACI,MAAM,KAAK,UAAU,EAAE;UACrCJ,IAAI,CAACI,MAAM,CAAC;YACVD,GAAG,EAAHA,GAAG;YACHD,IAAI,EAAJA,IAAI;YACJG,QAAQ,EAAE,CAACT,QAAQ,GAAG,MAAM,GAAG;UACjC,CAAC,CAAC;QACJ,CAAC,MAAM;UACLI,IAAI,CAACM,UAAU,GAAGJ,IAAI;UACtBF,IAAI,CAACO,SAAS,GAAGJ,GAAG;QACtB;MACF;IACF,CAAC;IACDzD,KAAA,CAAK8D,qBAAqB,GAAG,UAACC,IAAI,EAAEb,QAAQ,EAAK;MAC/C,IAAI/D,SAAS,CAACpB,OAAO,CAACiG,EAAE,KAAK,KAAK,EAAE;QAClC,CAAC,CAAC,EAAE/E,UAAU,CAAClB,OAAO,EAAE,+BAA+B,CAAC;MAC1D;IACF,CAAC;IACDiC,KAAA,CAAKiE,2CAA2C,GAAG,UAACC,UAAU,EAAEC,gBAAgB,EAAEC,2BAA2B,EAAK;MAChHpE,KAAA,CAAKqE,sBAAsB,GAAGF,gBAAgB,IAAI,CAAC;MACnDnE,KAAA,CAAKoE,2BAA2B,GAAG,CAAC,CAACA,2BAA2B;MAChE7E,UAAU,CAACxB,OAAO,CAACuG,aAAa,CAACJ,UAAU,EAAElE,KAAA,CAAKuE,gBAAgB,CAAC,CAAC,EAAEvE,KAAA,CAAKwE,kCAAkC,EAAExE,KAAA,CAAKyE,8CAA8C,CAAC;IACrK,CAAC;IACDzE,KAAA,CAAKyE,8CAA8C,GAAG,UAACjB,IAAI,EAAEC,GAAG,EAAEiB,KAAK,EAAEC,MAAM,EAAK;MAClF,IAAIC,eAAe,GAAG7F,WAAW,CAAChB,OAAO,CAAC8G,GAAG,CAAC,QAAQ,CAAC,CAACF,MAAM;MAC9D,IAAI3E,KAAA,CAAK8E,kBAAkB,EAAE;QAC3BF,eAAe,GAAG5E,KAAA,CAAK8E,kBAAkB,CAACC,cAAc,CAACC,OAAO;MAClE;MACA,IAAIC,aAAa,GAAGxB,GAAG,GAAGmB,eAAe,GAAGD,MAAM,GAAG3E,KAAA,CAAKqE,sBAAsB;MAMhF,IAAIrE,KAAA,CAAKoE,2BAA2B,EAAE;QACpCa,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,aAAa,CAAC;MAC5C;MACAjF,KAAA,CAAK+C,uBAAuB,CAAC;QAC3BC,CAAC,EAAE,CAAC;QACJC,CAAC,EAAEgC,aAAa;QAChB/B,QAAQ,EAAE;MACZ,CAAC,CAAC;MACFlD,KAAA,CAAKmE,gBAAgB,GAAG,CAAC;MACzBnE,KAAA,CAAKoE,2BAA2B,GAAG,KAAK;IAC1C,CAAC;IACDpE,KAAA,CAAKoF,+BAA+B,GAAG,UAAAtH,CAAC,EAAI;MAC1CkC,KAAA,CAAK8E,kBAAkB,GAAGhH,CAAC;MAC3BkC,KAAA,CAAKiB,KAAK,CAACoE,kBAAkB,IAAIrF,KAAA,CAAKiB,KAAK,CAACoE,kBAAkB,CAACvH,CAAC,CAAC;IACnE,CAAC;IACDkC,KAAA,CAAKsF,+BAA+B,GAAG,UAAAxH,CAAC,EAAI;MAC1CkC,KAAA,CAAK8E,kBAAkB,GAAG,IAAI;MAC9B9E,KAAA,CAAKiB,KAAK,CAACsE,kBAAkB,IAAIvF,KAAA,CAAKiB,KAAK,CAACsE,kBAAkB,CAACzH,CAAC,CAAC;IACnE,CAAC;IACDkC,KAAA,CAAKwF,8BAA8B,GAAG,UAAA1H,CAAC,EAAI;MAGzC,IAAIA,CAAC,EAAE;QACLkC,KAAA,CAAK8E,kBAAkB,GAAGhH,CAAC;MAC7B;MACAkC,KAAA,CAAKiB,KAAK,CAACwE,iBAAiB,IAAIzF,KAAA,CAAKiB,KAAK,CAACwE,iBAAiB,CAAC3H,CAAC,CAAC;IACjE,CAAC;IACDkC,KAAA,CAAK0F,8BAA8B,GAAG,UAAA5H,CAAC,EAAI;MACzCkC,KAAA,CAAK8E,kBAAkB,GAAG,IAAI;MAC9B9E,KAAA,CAAKiB,KAAK,CAAC0E,iBAAiB,IAAI3F,KAAA,CAAKiB,KAAK,CAAC0E,iBAAiB,CAAC7H,CAAC,CAAC;IACjE,CAAC;IACDkC,KAAA,CAAK4F,qBAAqB,GAAG,YAAM;MACjC5F,KAAA,CAAK6F,oCAAoC,CAAC,CAAC;IAC7C,CAAC;IACD7F,KAAA,CAAK8F,kBAAkB,GAAG,YAAM;MAC9B,OAAA9F,KAAA;IACF,CAAC;IACDA,KAAA,CAAKuD,iBAAiB,GAAG,YAAM;MAC7B,OAAOvD,KAAA,CAAKE,cAAc;IAC5B,CAAC;IACDF,KAAA,CAAK+F,eAAe,GAAG,YAAM;MAC3B,OAAO/F,KAAA,CAAKG,aAAa;IAC3B,CAAC;IACDH,KAAA,CAAKuE,gBAAgB,GAAG,YAAM;MAC5B,OAAOvE,KAAA,CAAKG,aAAa;IAC3B,CAAC;IACDH,KAAA,CAAKgG,kBAAkB,GAAG,YAAM;MAC9B,OAAOhG,KAAA,CAAKE,cAAc;IAC5B,CAAC;IACDF,KAAA,CAAKiG,QAAQ,GAAG,UAAChD,CAAC,EAAED,CAAC,EAAEE,QAAQ,EAAK;MAClC,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;QACzBE,OAAO,CAACC,IAAI,CAAC,iGAAiG,CAAC;MACjH,CAAC,MAAM;QACL,IAAI8C,KAAK,GAAGjD,CAAC,IAAIrD,WAAW;QAC5BoD,CAAC,GAAGkD,KAAK,CAAClD,CAAC;QACXC,CAAC,GAAGiD,KAAK,CAACjD,CAAC;QACXC,QAAQ,GAAGgD,KAAK,CAAChD,QAAQ;MAC3B;MACAlD,KAAA,CAAK+C,uBAAuB,CAAC;QAC3BC,CAAC,EAAEA,CAAC,IAAI,CAAC;QACTC,CAAC,EAAEA,CAAC,IAAI,CAAC;QACTC,QAAQ,EAAEA,QAAQ,KAAK;MACzB,CAAC,CAAC;IACJ,CAAC;IACDlD,KAAA,CAAKmG,WAAW,GAAG,UAAAC,OAAO,EAAI;MAE5B,IAAIlD,QAAQ,GAAG,CAACkD,OAAO,IAAIA,OAAO,CAAClD,QAAQ,MAAM,KAAK;MACtD,IAAImD,UAAU,GAAGrG,KAAA,CAAKiB,KAAK,CAACoF,UAAU;MACtC,IAAIC,mBAAmB,GAAGtG,KAAA,CAAKuD,iBAAiB,CAAC,CAAC;MAClD,IAAIP,CAAC,GAAGqD,UAAU,GAAGC,mBAAmB,CAACC,WAAW,GAAG,CAAC;MACxD,IAAItD,CAAC,GAAGoD,UAAU,GAAG,CAAC,GAAGC,mBAAmB,CAACE,YAAY;MACzDxG,KAAA,CAAK+C,uBAAuB,CAAC;QAC3BC,CAAC,EAADA,CAAC;QACDC,CAAC,EAADA,CAAC;QACDC,QAAQ,EAARA;MACF,CAAC,CAAC;IACJ,CAAC;IACDlD,KAAA,CAAKyG,sBAAsB,GAAG,UAAA3I,CAAC,EAAI;MACjC,IAAI4I,qBAAqB,GAAG5I,CAAC,CAACgD,WAAW,CAAC6F,MAAM;QAC9CjC,KAAK,GAAGgC,qBAAqB,CAAChC,KAAK;QACnCC,MAAM,GAAG+B,qBAAqB,CAAC/B,MAAM;MACvC3E,KAAA,CAAKiB,KAAK,CAAC2F,mBAAmB,CAAClC,KAAK,EAAEC,MAAM,CAAC;IAC/C,CAAC;IACD3E,KAAA,CAAK6G,aAAa,GAAG,UAAA/I,CAAC,EAAI;MACxB,IAAIgJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;QACzC,IAAIhH,KAAA,CAAKiB,KAAK,CAACW,QAAQ,IAAI5B,KAAA,CAAKiB,KAAK,CAACgG,mBAAmB,IAAI,IAAI,EAAE;UACjE9D,OAAO,CAAC+D,GAAG,CAAC,qDAAqD,GAAG,0DAA0D,GAAG,6DAA6D,GAAG,8DAA8D,GAAG,iBAAiB,CAAC;QACtR;MACF;MACA,IAAIlH,KAAA,CAAKiB,KAAK,CAACkG,mBAAmB,KAAK,SAAS,EAAE;QAChD,CAAC,CAAC,EAAEnI,gBAAgB,CAACjB,OAAO,EAAE,CAAC;MACjC;MACAiC,KAAA,CAAK2B,2BAA2B,CAAC7D,CAAC,CAAC;IACrC,CAAC;IACDkC,KAAA,CAAKoH,gBAAgB,GAAG,UAAA9D,IAAI,EAAI;MAC9BtD,KAAA,CAAKG,aAAa,GAAGmD,IAAI;IAC3B,CAAC;IACDtD,KAAA,CAAKqH,iBAAiB,GAAG,UAAA/D,IAAI,EAAI;MAC/BtD,KAAA,CAAKE,cAAc,GAAGoD,IAAI;MAI1B,IAAIA,IAAI,IAAI,IAAI,EAAE;QAChBA,IAAI,CAACwC,kBAAkB,GAAG9F,KAAA,CAAK8F,kBAAkB;QACjDxC,IAAI,CAACiB,gBAAgB,GAAGvE,KAAA,CAAKuE,gBAAgB;QAC7CjB,IAAI,CAACyC,eAAe,GAAG/F,KAAA,CAAK+F,eAAe;QAC3CzC,IAAI,CAAC0C,kBAAkB,GAAGhG,KAAA,CAAKgG,kBAAkB;QACjD1C,IAAI,CAACC,iBAAiB,GAAGvD,KAAA,CAAKuD,iBAAiB;QAC/CD,IAAI,CAAC2C,QAAQ,GAAGjG,KAAA,CAAKiG,QAAQ;QAC7B3C,IAAI,CAAC6C,WAAW,GAAGnG,KAAA,CAAKmG,WAAW;QACnC7C,IAAI,CAACsC,qBAAqB,GAAG5F,KAAA,CAAK4F,qBAAqB;QACvDtC,IAAI,CAACQ,qBAAqB,GAAG9D,KAAA,CAAK8D,qBAAqB;QACvDR,IAAI,CAACW,2CAA2C,GAAGjE,KAAA,CAAKiE,2CAA2C;MACrG;MACA,IAAIqD,GAAG,GAAG,CAAC,CAAC,EAAEpI,UAAU,CAACnB,OAAO,EAAEiC,KAAA,CAAKiB,KAAK,CAACsG,YAAY,CAAC;MAC1DD,GAAG,CAAChE,IAAI,CAAC;IACX,CAAC;IAAC,OAAAtD,KAAA;EACJ;EAAC,IAAAtC,UAAA,CAAAK,OAAA,EAAA+B,UAAA,EAAAC,qBAAA;EAAA,WAAAxC,aAAA,CAAAQ,OAAA,EAAA+B,UAAA;IAAA0H,GAAA;IAAAC,KAAA,EAuCD,SAAAC,4CAA4CA,CAAA,EAAG;MAC7C,OAAO,KAAK;IACd;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAwBD,SAAAE,oCAAoCA,CAAA,EAAG;MACrC,CAAC,CAAC,EAAEjI,QAAQ,CAAC3B,OAAO,EAAE,KAAK,EAAE,yDAAyD,CAAC;IACzF;EAAC;IAAAyJ,GAAA;IAAAC,KAAA,EAwGD,SAAA5B,oCAAoCA,CAAA,EAAG,CAAC;EAAC;IAAA2B,GAAA;IAAAC,KAAA,EAwBzC,SAAAjD,kCAAkCA,CAAC1G,CAAC,EAAE;MACpCqF,OAAO,CAACyE,KAAK,CAAC,8BAA8B,EAAE9J,CAAC,CAAC;IAClD;EAAC;IAAA0J,GAAA;IAAAC,KAAA,EAgED,SAAAI,MAAMA,CAAA,EAAG;MACP,IAAIC,WAAW,GAAG,IAAI,CAAC7G,KAAK;QAC1B8G,qBAAqB,GAAGD,WAAW,CAACC,qBAAqB;QACzD1B,UAAU,GAAGyB,WAAW,CAACzB,UAAU;QACnCO,mBAAmB,GAAGkB,WAAW,CAAClB,mBAAmB;QACrDoB,cAAc,GAAGF,WAAW,CAACE,cAAc;QAC3CC,mBAAmB,GAAGH,WAAW,CAACG,mBAAmB;QACrDC,aAAa,GAAGJ,WAAW,CAACI,aAAa;QACzCX,YAAY,GAAGO,WAAW,CAACP,YAAY;QACvCJ,mBAAmB,GAAGW,WAAW,CAACX,mBAAmB;QACrDvF,QAAQ,GAAGkG,WAAW,CAAClG,QAAQ;QAC/BuG,aAAa,GAAGL,WAAW,CAACK,aAAa;QACzCC,KAAK,GAAG,CAAC,CAAC,EAAEtJ,8BAA8B,CAACf,OAAO,EAAE+J,WAAW,EAAEnI,SAAS,CAAC;MAC7E,IAAImH,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,IAAI,CAAC/F,KAAK,CAACoH,KAAK,EAAE;QAC7D,IAAIA,KAAK,GAAGhJ,WAAW,CAACtB,OAAO,CAACuK,OAAO,CAAC,IAAI,CAACrH,KAAK,CAACoH,KAAK,CAAC;QACzD,IAAIE,gBAAgB,GAAG,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAACC,MAAM,CAAC,UAAAC,IAAI;UAAA,OAAIJ,KAAK,IAAIA,KAAK,CAACI,IAAI,CAAC,KAAKC,SAAS;QAAA,EAAC;QAC1G,CAAC,CAAC,EAAEzJ,UAAU,CAAClB,OAAO,EAAEwK,gBAAgB,CAACvH,MAAM,KAAK,CAAC,EAAE,2BAA2B,GAAG2H,IAAI,CAACC,SAAS,CAACL,gBAAgB,CAAC,GAAG,IAAI,GAAG,yDAAyD,CAAC;MAC3L;MACA,IAAIM,sBAAsB,GAAG,CAAC,CAAC;MAC/B,IAAIjC,mBAAmB,EAAE;QACvBiC,sBAAsB,GAAG;UACvBC,QAAQ,EAAE,IAAI,CAACrC;QACjB,CAAC;MACH;MACA,IAAIsC,sBAAsB,GAAG,CAAC1C,UAAU,IAAI2C,KAAK,CAACC,OAAO,CAAChB,mBAAmB,CAAC;MAC9E,IAAIiB,QAAQ,GAAGH,sBAAsB,IAAIb,aAAa,GAAGzI,MAAM,CAAC1B,OAAO,CAACoL,QAAQ,CAACC,GAAG,CAAC,IAAI,CAACnI,KAAK,CAACiI,QAAQ,EAAE,UAACG,KAAK,EAAEC,CAAC,EAAK;QACtH,IAAIC,QAAQ,GAAGR,sBAAsB,IAAId,mBAAmB,CAACuB,OAAO,CAACF,CAAC,CAAC,GAAG,CAAC,CAAC;QAC5E,IAAID,KAAK,IAAI,IAAI,KAAKE,QAAQ,IAAIrB,aAAa,CAAC,EAAE;UAChD,OAAoBzI,MAAM,CAAC1B,OAAO,CAAC0L,aAAa,CAACjK,KAAK,CAACzB,OAAO,EAAE;YAC9DsK,KAAK,EAAE,CAACkB,QAAQ,IAAIG,MAAM,CAACC,YAAY,EAAEzB,aAAa,IAAIwB,MAAM,CAACE,kBAAkB;UACrF,CAAC,EAAEP,KAAK,CAAC;QACX,CAAC,MAAM;UACL,OAAOA,KAAK;QACd;MACF,CAAC,CAAC,GAAG,IAAI,CAACpI,KAAK,CAACiI,QAAQ;MACxB,IAAIW,gBAAgB,GAAgBpK,MAAM,CAAC1B,OAAO,CAAC0L,aAAa,CAACjK,KAAK,CAACzB,OAAO,EAAE,CAAC,CAAC,EAAEc,SAAS,CAACd,OAAO,EAAE,CAAC,CAAC,EAAE8K,sBAAsB,EAAE;QACjIK,QAAQ,EAAEA,QAAQ;QAClBY,WAAW,EAAE,KAAK;QAClBxC,GAAG,EAAE,IAAI,CAACF,gBAAgB;QAC1BiB,KAAK,EAAE,CAAChC,UAAU,IAAIqD,MAAM,CAACK,0BAA0B,EAAE5B,aAAa,IAAIuB,MAAM,CAACM,6BAA6B,EAAEjC,qBAAqB;MACvI,CAAC,CAAC,CAAC;MACH,IAAIkC,SAAS,GAAG5D,UAAU,GAAGqD,MAAM,CAACQ,cAAc,GAAGR,MAAM,CAACS,YAAY;MACxE,IAAIC,kBAAkB,GAAG/D,UAAU,GAAGqD,MAAM,CAACW,uBAAuB,GAAGX,MAAM,CAACY,qBAAqB;MACnG,IAAIrJ,KAAK,GAAG,CAAC,CAAC,EAAErC,cAAc,CAACb,OAAO,EAAE,CAAC,CAAC,EAAEa,cAAc,CAACb,OAAO,EAAE,CAAC,CAAC,EAAEqK,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAClFC,KAAK,EAAE,CAAC4B,SAAS,EAAE/B,aAAa,IAAIkC,kBAAkB,EAAE,IAAI,CAACnJ,KAAK,CAACoH,KAAK,CAAC;QACzE3F,YAAY,EAAE,IAAI,CAACD,+BAA+B;QAClDG,WAAW,EAAE,IAAI,CAACD,8BAA8B;QAChDzB,UAAU,EAAE,IAAI,CAACL,6BAA6B;QAC9CmB,iBAAiB,EAAE,IAAI,CAACD,oCAAoC;QAC5DG,eAAe,EAAE,IAAI,CAACD,kCAAkC;QACxDK,qBAAqB,EAAE,IAAI,CAACH,wCAAwC;QACpEK,mBAAmB,EAAE,IAAI,CAACD,sCAAsC;QAChEgI,yBAAyB,EAAE,IAAI,CAAC7C,4CAA4C;QAC5E8C,gCAAgC,EAAE,IAAI,CAAC9J,mDAAmD;QAC1F+J,0BAA0B,EAAE,IAAI,CAAChK,6CAA6C;QAC9EmB,QAAQ,EAAE,IAAI,CAACiF,aAAa;QAC5B/E,gBAAgB,EAAE,IAAI,CAACD,mCAAmC;QAC1D6I,6BAA6B,EAAE,IAAI,CAAC9J,uCAAuC;QAC3E+J,oBAAoB,EAAE,IAAI,CAACC,8BAA8B;QACzDxJ,kBAAkB,EAAE,IAAI,CAACD,qCAAqC;QAC9D0J,iBAAiB,EAAE,IAAI,CAAClD;MAC1B,CAAC,CAAC;MACF,IAAImD,eAAe,GAAG1L,eAAe,CAACrB,OAAO;MAC7C,CAAC,CAAC,EAAEkB,UAAU,CAAClB,OAAO,EAAE+M,eAAe,KAAKpC,SAAS,EAAE,uCAAuC,CAAC;MAC/F,IAAIqC,UAAU,GAAgBtL,MAAM,CAAC1B,OAAO,CAAC0L,aAAa,CAACqB,eAAe,EAAE,CAAC,CAAC,EAAEjM,SAAS,CAACd,OAAO,EAAE,CAAC,CAAC,EAAEkD,KAAK,EAAE;QAC5GqG,GAAG,EAAE,IAAI,CAACD;MACZ,CAAC,CAAC,EAAEwC,gBAAgB,CAAC;MACrB,IAAI7B,cAAc,EAAE;QAClB,OAAoBvI,MAAM,CAAC1B,OAAO,CAACiN,YAAY,CAAChD,cAAc,EAAE;UAC9DK,KAAK,EAAEpH,KAAK,CAACoH;QACf,CAAC,EAAE0C,UAAU,CAAC;MAChB;MACA,OAAOA,UAAU;IACnB;EAAC;AAAA,EA/jBsBtL,MAAM,CAAC1B,OAAO,CAACkN,SAAS;AAikBjD,IAAIC,WAAW,GAAG;EAChBC,QAAQ,EAAE,CAAC;EACXC,UAAU,EAAE,CAAC;EAIbC,SAAS,EAAE,eAAe;EAE1BC,uBAAuB,EAAE;AAC3B,CAAC;AACD,IAAI5B,MAAM,GAAGrK,WAAW,CAACtB,OAAO,CAACwN,MAAM,CAAC;EACtCpB,YAAY,EAAE,CAAC,CAAC,EAAEvL,cAAc,CAACb,OAAO,EAAE,CAAC,CAAC,EAAEa,cAAc,CAACb,OAAO,EAAE,CAAC,CAAC,EAAEmN,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1FM,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAE,QAAQ;IACnBC,SAAS,EAAE;EACb,CAAC,CAAC;EACFxB,cAAc,EAAE,CAAC,CAAC,EAAEtL,cAAc,CAACb,OAAO,EAAE,CAAC,CAAC,EAAEa,cAAc,CAACb,OAAO,EAAE,CAAC,CAAC,EAAEmN,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;IAC5FM,aAAa,EAAE,KAAK;IACpBC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE;EACb,CAAC,CAAC;EACF3B,0BAA0B,EAAE;IAC1ByB,aAAa,EAAE;EACjB,CAAC;EACDxB,6BAA6B,EAAE;IAC7B2B,cAAc,EAAE,QAAQ;IACxBR,QAAQ,EAAE;EACZ,CAAC;EACDxB,YAAY,EAAE;IACZiC,QAAQ,EAAE,QAAQ;IAClBnI,GAAG,EAAE,CAAC;IACNoI,MAAM,EAAE;EACV,CAAC;EACDxB,uBAAuB,EAAE;IACvByB,cAAc,EAAE;EAClB,CAAC;EACDxB,qBAAqB,EAAE;IACrBwB,cAAc,EAAE;EAClB,CAAC;EACDlC,kBAAkB,EAAE;IAClBmC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AACF,IAAIC,mBAAmB,GAAgBvM,MAAM,CAAC1B,OAAO,CAACkO,UAAU,CAAC,UAAChL,KAAK,EAAEsG,YAAY,EAAK;EACxF,OAAoB9H,MAAM,CAAC1B,OAAO,CAAC0L,aAAa,CAAC3J,UAAU,EAAE,CAAC,CAAC,EAAEjB,SAAS,CAACd,OAAO,EAAE,CAAC,CAAC,EAAEkD,KAAK,EAAE;IAC7FsG,YAAY,EAAEA;EAChB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFyE,mBAAmB,CAACE,WAAW,GAAG,YAAY;AAC9C,IAAIC,QAAQ,GAAGzN,OAAO,CAACX,OAAO,GAAGiO,mBAAmB;AACpDI,MAAM,CAAC1N,OAAO,GAAGA,OAAO,CAACX,OAAO", "ignoreList": []}