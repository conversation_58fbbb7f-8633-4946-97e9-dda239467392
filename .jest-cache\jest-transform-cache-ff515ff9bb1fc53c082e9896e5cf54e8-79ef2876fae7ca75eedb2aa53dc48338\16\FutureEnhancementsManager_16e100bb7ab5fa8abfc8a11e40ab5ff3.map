{"version": 3, "names": ["FutureEnhancementsManager", "_classCallCheck", "enhancements", "cov_2ehhv10rb3", "s", "Map", "technologyTrends", "roadmapPhases", "innovationPipeline", "FUTURE_ENHANCEMENTS", "id", "name", "description", "category", "phase", "priority", "complexity", "dependencies", "prerequisites", "technology", "infrastructure", "expertise", "impact", "performance", "userExperience", "technicalDebt", "marketAdvantage", "resources", "developmentTime", "teamSize", "budget", "f", "initializeFutureEnhancements", "_createClass", "key", "value", "_initializeFutureEnhancements", "_asyncToGenerator", "loadFutureEnhancements", "initializeTechnologyTrends", "createRoadmapPhases", "buildInnovationPipeline", "console", "log", "error", "apply", "arguments", "getStrategicRoadmap", "phases", "Array", "from", "values", "timeline", "for<PERSON>ach", "milestones", "milestone", "push", "date", "sort", "a", "b", "totalInvestment", "reduce", "sum", "expectedROI", "allRisks", "flatMap", "risks", "riskAssessment", "high", "filter", "risk", "probability", "length", "medium", "low", "getInnovationPipeline", "priorityDistribution", "phaseDistribution", "categoryDistribution", "enhancement", "readinessScore", "calculateInnovationReadiness", "pipeline", "getTechnologyTrendAnalysis", "trends", "emergingTechnologies", "trend", "maturity", "adoptionRecommendations", "map", "recommendation", "reasoning", "relevanceScore", "competitiveAdvantage", "calculateCompetitiveAdvantage", "prioritizeEnhancements", "_this", "scoreA", "calculatePriorityScore", "scoreB", "getInvestmentAnalysis", "categoryBreakdown", "phaseBreakdown", "roi", "shortTerm", "mediumTerm", "longTerm", "paybackPeriod", "riskAdjustedReturn", "_this2", "fullEnhancement", "Object", "assign", "estimatedStart", "Date", "now", "Math", "random", "estimatedCompletion", "feasibility", "calculateFeasibility", "set", "technical", "resource", "overall", "_this3", "adoptionRate", "timeToMainstream", "opportunities", "fullTrend", "potentialImpact", "cost", "marketPosition", "_this4", "startDate", "endDate", "objectives", "criteria", "status", "team", "mitigation", "readyEnhancements", "highImpactTrends", "size", "priorityWeights", "critical", "priorityScore", "impactScore", "feasibilityScore", "futureEnhancementsManager"], "sources": ["FutureEnhancementsManager.ts"], "sourcesContent": ["/**\n * Future Enhancements Manager\n * \n * Strategic planning system for future optimization enhancements,\n * technology roadmap, and innovation pipeline management.\n */\n\ninterface Enhancement {\n  id: string;\n  name: string;\n  description: string;\n  category: 'performance' | 'ai' | 'hardware' | 'network' | 'ui' | 'security' | 'analytics';\n  phase: 'research' | 'design' | 'development' | 'testing' | 'deployment' | 'completed';\n  priority: 'low' | 'medium' | 'high' | 'critical';\n  complexity: 'simple' | 'moderate' | 'complex' | 'revolutionary';\n  timeline: {\n    estimatedStart: number;\n    estimatedCompletion: number;\n    actualStart?: number;\n    actualCompletion?: number;\n  };\n  dependencies: string[];\n  prerequisites: {\n    technology: string[];\n    infrastructure: string[];\n    expertise: string[];\n  };\n  impact: {\n    performance: number; // 0-100\n    userExperience: number; // 0-100\n    technicalDebt: number; // -100 to 100 (negative is reduction)\n    marketAdvantage: number; // 0-100\n  };\n  feasibility: {\n    technical: number; // 0-100\n    resource: number; // 0-100\n    timeline: number; // 0-100\n    overall: number; // 0-100\n  };\n  resources: {\n    developmentTime: number; // hours\n    teamSize: number;\n    budget: number;\n    infrastructure: string[];\n  };\n}\n\ninterface TechnologyTrend {\n  id: string;\n  name: string;\n  description: string;\n  category: string;\n  maturity: 'emerging' | 'developing' | 'mature' | 'declining';\n  adoptionRate: number; // 0-100\n  relevanceScore: number; // 0-100\n  timeToMainstream: number; // months\n  potentialImpact: {\n    performance: number;\n    cost: number;\n    complexity: number;\n    marketPosition: number;\n  };\n  risks: string[];\n  opportunities: string[];\n}\n\ninterface RoadmapPhase {\n  id: string;\n  name: string;\n  description: string;\n  startDate: number;\n  endDate: number;\n  objectives: string[];\n  enhancements: string[];\n  milestones: Array<{\n    id: string;\n    name: string;\n    date: number;\n    criteria: string[];\n    status: 'pending' | 'in_progress' | 'completed' | 'delayed';\n  }>;\n  resources: {\n    budget: number;\n    team: string[];\n    infrastructure: string[];\n  };\n  risks: Array<{\n    description: string;\n    probability: number;\n    impact: number;\n    mitigation: string;\n  }>;\n}\n\n/**\n * Future Enhancements Planning System\n */\nclass FutureEnhancementsManager {\n  private enhancements: Map<string, Enhancement> = new Map();\n  private technologyTrends: Map<string, TechnologyTrend> = new Map();\n  private roadmapPhases: Map<string, RoadmapPhase> = new Map();\n  private innovationPipeline: Enhancement[] = [];\n  \n  private readonly FUTURE_ENHANCEMENTS: Omit<Enhancement, 'timeline' | 'feasibility'>[] = [\n    {\n      id: 'quantum_optimization',\n      name: 'Quantum Computing Optimization',\n      description: 'Leverage quantum computing for complex optimization problems',\n      category: 'performance',\n      phase: 'research',\n      priority: 'medium',\n      complexity: 'revolutionary',\n      dependencies: [],\n      prerequisites: {\n        technology: ['quantum_computing', 'quantum_algorithms'],\n        infrastructure: ['quantum_cloud_access'],\n        expertise: ['quantum_programming', 'optimization_theory'],\n      },\n      impact: {\n        performance: 95,\n        userExperience: 70,\n        technicalDebt: -20,\n        marketAdvantage: 90,\n      },\n      resources: {\n        developmentTime: 2000,\n        teamSize: 3,\n        budget: 500000,\n        infrastructure: ['quantum_simulator', 'cloud_quantum_access'],\n      },\n    },\n    {\n      id: 'neural_processing_units',\n      name: 'Neural Processing Unit Integration',\n      description: 'Integrate dedicated NPU chips for AI acceleration',\n      category: 'ai',\n      phase: 'design',\n      priority: 'high',\n      complexity: 'complex',\n      dependencies: ['phase3c_native'],\n      prerequisites: {\n        technology: ['npu_apis', 'neural_frameworks'],\n        infrastructure: ['npu_devices'],\n        expertise: ['neural_optimization', 'hardware_integration'],\n      },\n      impact: {\n        performance: 85,\n        userExperience: 80,\n        technicalDebt: -10,\n        marketAdvantage: 75,\n      },\n      resources: {\n        developmentTime: 800,\n        teamSize: 4,\n        budget: 200000,\n        infrastructure: ['npu_dev_kits', 'testing_devices'],\n      },\n    },\n    {\n      id: 'real_time_ray_tracing',\n      name: 'Real-time Ray Tracing Graphics',\n      description: 'Implement real-time ray tracing for 3D tennis visualization',\n      category: 'hardware',\n      phase: 'research',\n      priority: 'medium',\n      complexity: 'complex',\n      dependencies: ['phase3c_gpu'],\n      prerequisites: {\n        technology: ['ray_tracing_apis', 'gpu_compute'],\n        infrastructure: ['rt_capable_gpus'],\n        expertise: ['graphics_programming', 'ray_tracing'],\n      },\n      impact: {\n        performance: 60,\n        userExperience: 95,\n        technicalDebt: 10,\n        marketAdvantage: 85,\n      },\n      resources: {\n        developmentTime: 1200,\n        teamSize: 5,\n        budget: 300000,\n        infrastructure: ['rt_gpus', 'graphics_workstations'],\n      },\n    },\n    {\n      id: 'edge_ai_inference',\n      name: 'Edge AI Inference Optimization',\n      description: 'Deploy AI models directly to edge locations for ultra-low latency',\n      category: 'ai',\n      phase: 'development',\n      priority: 'high',\n      complexity: 'complex',\n      dependencies: ['phase3b_edge', 'phase3a_ai'],\n      prerequisites: {\n        technology: ['edge_ai_frameworks', 'model_optimization'],\n        infrastructure: ['edge_compute_nodes'],\n        expertise: ['edge_computing', 'ai_optimization'],\n      },\n      impact: {\n        performance: 80,\n        userExperience: 85,\n        technicalDebt: -5,\n        marketAdvantage: 80,\n      },\n      resources: {\n        developmentTime: 600,\n        teamSize: 3,\n        budget: 150000,\n        infrastructure: ['edge_servers', 'ai_accelerators'],\n      },\n    },\n    {\n      id: 'blockchain_performance_tracking',\n      name: 'Blockchain Performance Analytics',\n      description: 'Immutable performance tracking and verification system',\n      category: 'analytics',\n      phase: 'research',\n      priority: 'low',\n      complexity: 'complex',\n      dependencies: [],\n      prerequisites: {\n        technology: ['blockchain_platforms', 'smart_contracts'],\n        infrastructure: ['blockchain_nodes'],\n        expertise: ['blockchain_development', 'cryptography'],\n      },\n      impact: {\n        performance: 20,\n        userExperience: 40,\n        technicalDebt: 15,\n        marketAdvantage: 60,\n      },\n      resources: {\n        developmentTime: 400,\n        teamSize: 2,\n        budget: 100000,\n        infrastructure: ['blockchain_testnet'],\n      },\n    },\n    {\n      id: 'augmented_reality_coaching',\n      name: 'AR-Powered Tennis Coaching',\n      description: 'Real-time AR overlays for tennis technique improvement',\n      category: 'ui',\n      phase: 'design',\n      priority: 'high',\n      complexity: 'complex',\n      dependencies: ['phase3c_gpu', 'phase3a_ai'],\n      prerequisites: {\n        technology: ['ar_frameworks', 'computer_vision'],\n        infrastructure: ['ar_devices'],\n        expertise: ['ar_development', 'sports_analytics'],\n      },\n      impact: {\n        performance: 70,\n        userExperience: 95,\n        technicalDebt: 20,\n        marketAdvantage: 90,\n      },\n      resources: {\n        developmentTime: 1000,\n        teamSize: 6,\n        budget: 400000,\n        infrastructure: ['ar_headsets', 'motion_capture'],\n      },\n    },\n    {\n      id: 'predictive_maintenance',\n      name: 'Predictive System Maintenance',\n      description: 'AI-powered predictive maintenance for all optimization systems',\n      category: 'ai',\n      phase: 'development',\n      priority: 'medium',\n      complexity: 'moderate',\n      dependencies: ['phase3a_ai'],\n      prerequisites: {\n        technology: ['predictive_analytics', 'anomaly_detection'],\n        infrastructure: ['monitoring_systems'],\n        expertise: ['machine_learning', 'system_monitoring'],\n      },\n      impact: {\n        performance: 75,\n        userExperience: 60,\n        technicalDebt: -30,\n        marketAdvantage: 50,\n      },\n      resources: {\n        developmentTime: 300,\n        teamSize: 2,\n        budget: 75000,\n        infrastructure: ['monitoring_tools'],\n      },\n    },\n    {\n      id: 'zero_latency_networking',\n      name: 'Zero-Latency Networking',\n      description: 'Ultra-low latency networking with 5G and edge computing',\n      category: 'network',\n      phase: 'research',\n      priority: 'high',\n      complexity: 'complex',\n      dependencies: ['phase3b_edge'],\n      prerequisites: {\n        technology: ['5g_networks', 'edge_computing'],\n        infrastructure: ['5g_infrastructure'],\n        expertise: ['network_optimization', 'edge_computing'],\n      },\n      impact: {\n        performance: 90,\n        userExperience: 85,\n        technicalDebt: -10,\n        marketAdvantage: 85,\n      },\n      resources: {\n        developmentTime: 800,\n        teamSize: 4,\n        budget: 250000,\n        infrastructure: ['5g_testbeds', 'edge_nodes'],\n      },\n    },\n  ];\n\n  constructor() {\n    this.initializeFutureEnhancements();\n  }\n\n  /**\n   * Initialize future enhancements planning\n   */\n  private async initializeFutureEnhancements(): Promise<void> {\n    try {\n      // Load future enhancements\n      this.loadFutureEnhancements();\n      \n      // Initialize technology trends\n      this.initializeTechnologyTrends();\n      \n      // Create roadmap phases\n      this.createRoadmapPhases();\n      \n      // Build innovation pipeline\n      this.buildInnovationPipeline();\n      \n      console.log('Future Enhancements Manager initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Future Enhancements Manager:', error);\n    }\n  }\n\n  /**\n   * Get strategic roadmap\n   */\n  getStrategicRoadmap(): {\n    phases: RoadmapPhase[];\n    timeline: Array<{ date: number; milestone: string; phase: string }>;\n    totalInvestment: number;\n    expectedROI: number;\n    riskAssessment: {\n      high: number;\n      medium: number;\n      low: number;\n    };\n  } {\n    const phases = Array.from(this.roadmapPhases.values());\n    const timeline: Array<{ date: number; milestone: string; phase: string }> = [];\n    \n    // Build timeline from milestones\n    phases.forEach(phase => {\n      phase.milestones.forEach(milestone => {\n        timeline.push({\n          date: milestone.date,\n          milestone: milestone.name,\n          phase: phase.name,\n        });\n      });\n    });\n    \n    // Sort timeline by date\n    timeline.sort((a, b) => a.date - b.date);\n    \n    // Calculate total investment\n    const totalInvestment = phases.reduce((sum, phase) => sum + phase.resources.budget, 0);\n    \n    // Calculate expected ROI (simplified)\n    const expectedROI = 250; // 250% ROI over 3 years\n    \n    // Risk assessment\n    const allRisks = phases.flatMap(phase => phase.risks);\n    const riskAssessment = {\n      high: allRisks.filter(risk => risk.probability * risk.impact > 70).length,\n      medium: allRisks.filter(risk => risk.probability * risk.impact > 30 && risk.probability * risk.impact <= 70).length,\n      low: allRisks.filter(risk => risk.probability * risk.impact <= 30).length,\n    };\n\n    return {\n      phases,\n      timeline,\n      totalInvestment,\n      expectedROI,\n      riskAssessment,\n    };\n  }\n\n  /**\n   * Get innovation pipeline\n   */\n  getInnovationPipeline(): {\n    pipeline: Enhancement[];\n    priorityDistribution: Record<string, number>;\n    phaseDistribution: Record<string, number>;\n    categoryDistribution: Record<string, number>;\n    readinessScore: number;\n  } {\n    const priorityDistribution: Record<string, number> = {};\n    const phaseDistribution: Record<string, number> = {};\n    const categoryDistribution: Record<string, number> = {};\n    \n    this.innovationPipeline.forEach(enhancement => {\n      priorityDistribution[enhancement.priority] = (priorityDistribution[enhancement.priority] || 0) + 1;\n      phaseDistribution[enhancement.phase] = (phaseDistribution[enhancement.phase] || 0) + 1;\n      categoryDistribution[enhancement.category] = (categoryDistribution[enhancement.category] || 0) + 1;\n    });\n    \n    // Calculate readiness score\n    const readinessScore = this.calculateInnovationReadiness();\n\n    return {\n      pipeline: this.innovationPipeline,\n      priorityDistribution,\n      phaseDistribution,\n      categoryDistribution,\n      readinessScore,\n    };\n  }\n\n  /**\n   * Get technology trend analysis\n   */\n  getTechnologyTrendAnalysis(): {\n    trends: TechnologyTrend[];\n    emergingTechnologies: TechnologyTrend[];\n    adoptionRecommendations: Array<{\n      technology: string;\n      recommendation: 'adopt' | 'trial' | 'assess' | 'hold';\n      reasoning: string;\n      timeline: string;\n    }>;\n    competitiveAdvantage: number;\n  } {\n    const trends = Array.from(this.technologyTrends.values());\n    const emergingTechnologies = trends.filter(trend => trend.maturity === 'emerging');\n    \n    const adoptionRecommendations = trends.map(trend => {\n      let recommendation: 'adopt' | 'trial' | 'assess' | 'hold';\n      let reasoning: string;\n      let timeline: string;\n      \n      if (trend.relevanceScore > 80 && trend.maturity === 'mature') {\n        recommendation = 'adopt';\n        reasoning = 'High relevance and mature technology';\n        timeline = 'Immediate';\n      } else if (trend.relevanceScore > 60 && trend.maturity === 'developing') {\n        recommendation = 'trial';\n        reasoning = 'Good potential with developing maturity';\n        timeline = '3-6 months';\n      } else if (trend.relevanceScore > 40) {\n        recommendation = 'assess';\n        reasoning = 'Moderate potential, needs evaluation';\n        timeline = '6-12 months';\n      } else {\n        recommendation = 'hold';\n        reasoning = 'Low relevance or high risk';\n        timeline = '12+ months';\n      }\n      \n      return {\n        technology: trend.name,\n        recommendation,\n        reasoning,\n        timeline,\n      };\n    });\n    \n    // Calculate competitive advantage\n    const competitiveAdvantage = this.calculateCompetitiveAdvantage();\n\n    return {\n      trends,\n      emergingTechnologies,\n      adoptionRecommendations,\n      competitiveAdvantage,\n    };\n  }\n\n  /**\n   * Prioritize enhancements\n   */\n  prioritizeEnhancements(): Enhancement[] {\n    return this.innovationPipeline.sort((a, b) => {\n      // Calculate priority score\n      const scoreA = this.calculatePriorityScore(a);\n      const scoreB = this.calculatePriorityScore(b);\n      return scoreB - scoreA;\n    });\n  }\n\n  /**\n   * Get investment analysis\n   */\n  getInvestmentAnalysis(): {\n    totalInvestment: number;\n    categoryBreakdown: Record<string, number>;\n    phaseBreakdown: Record<string, number>;\n    roi: {\n      shortTerm: number; // 1 year\n      mediumTerm: number; // 3 years\n      longTerm: number; // 5 years\n    };\n    paybackPeriod: number; // months\n    riskAdjustedReturn: number;\n  } {\n    const totalInvestment = this.innovationPipeline.reduce((sum, enhancement) => \n      sum + enhancement.resources.budget, 0\n    );\n    \n    const categoryBreakdown: Record<string, number> = {};\n    const phaseBreakdown: Record<string, number> = {};\n    \n    this.innovationPipeline.forEach(enhancement => {\n      categoryBreakdown[enhancement.category] = \n        (categoryBreakdown[enhancement.category] || 0) + enhancement.resources.budget;\n      phaseBreakdown[enhancement.phase] = \n        (phaseBreakdown[enhancement.phase] || 0) + enhancement.resources.budget;\n    });\n\n    return {\n      totalInvestment,\n      categoryBreakdown,\n      phaseBreakdown,\n      roi: {\n        shortTerm: 25, // 25% ROI in 1 year\n        mediumTerm: 150, // 150% ROI in 3 years\n        longTerm: 300, // 300% ROI in 5 years\n      },\n      paybackPeriod: 18, // 18 months\n      riskAdjustedReturn: 180, // 180% risk-adjusted return\n    };\n  }\n\n  // Private helper methods\n\n  private loadFutureEnhancements(): void {\n    this.FUTURE_ENHANCEMENTS.forEach(enhancement => {\n      const fullEnhancement: Enhancement = {\n        ...enhancement,\n        timeline: {\n          estimatedStart: Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000, // Random start within 1 year\n          estimatedCompletion: Date.now() + (Math.random() * 2 + 1) * 365 * 24 * 60 * 60 * 1000, // 1-3 years\n        },\n        feasibility: this.calculateFeasibility(enhancement),\n      };\n      \n      this.enhancements.set(enhancement.id, fullEnhancement);\n    });\n  }\n\n  private calculateFeasibility(enhancement: any): Enhancement['feasibility'] {\n    // Calculate feasibility scores based on complexity and prerequisites\n    const technical = enhancement.complexity === 'simple' ? 90 : \n                     enhancement.complexity === 'moderate' ? 70 :\n                     enhancement.complexity === 'complex' ? 50 : 30;\n    \n    const resource = enhancement.resources.budget < 100000 ? 90 :\n                    enhancement.resources.budget < 300000 ? 70 : 50;\n    \n    const timeline = enhancement.phase === 'development' ? 80 :\n                    enhancement.phase === 'design' ? 60 :\n                    enhancement.phase === 'research' ? 40 : 20;\n    \n    const overall = (technical + resource + timeline) / 3;\n    \n    return { technical, resource, timeline, overall };\n  }\n\n  private initializeTechnologyTrends(): void {\n    const trends: Omit<TechnologyTrend, 'potentialImpact'>[] = [\n      {\n        id: 'quantum_computing',\n        name: 'Quantum Computing',\n        description: 'Quantum computers for complex optimization',\n        category: 'computing',\n        maturity: 'emerging',\n        adoptionRate: 5,\n        relevanceScore: 70,\n        timeToMainstream: 60,\n        risks: ['Technical complexity', 'Limited availability'],\n        opportunities: ['Revolutionary performance gains', 'Competitive advantage'],\n      },\n      {\n        id: 'neural_processing',\n        name: 'Neural Processing Units',\n        description: 'Dedicated AI acceleration chips',\n        category: 'hardware',\n        maturity: 'developing',\n        adoptionRate: 25,\n        relevanceScore: 85,\n        timeToMainstream: 24,\n        risks: ['Hardware dependency', 'Cost'],\n        opportunities: ['AI acceleration', 'Energy efficiency'],\n      },\n      {\n        id: 'edge_ai',\n        name: 'Edge AI Computing',\n        description: 'AI processing at network edge',\n        category: 'ai',\n        maturity: 'developing',\n        adoptionRate: 40,\n        relevanceScore: 90,\n        timeToMainstream: 18,\n        risks: ['Infrastructure requirements', 'Complexity'],\n        opportunities: ['Ultra-low latency', 'Privacy'],\n      },\n    ];\n\n    trends.forEach(trend => {\n      const fullTrend: TechnologyTrend = {\n        ...trend,\n        potentialImpact: {\n          performance: 70 + Math.random() * 30,\n          cost: 50 + Math.random() * 50,\n          complexity: 30 + Math.random() * 40,\n          marketPosition: 60 + Math.random() * 40,\n        },\n      };\n      \n      this.technologyTrends.set(trend.id, fullTrend);\n    });\n  }\n\n  private createRoadmapPhases(): void {\n    const phases: RoadmapPhase[] = [\n      {\n        id: 'phase_4a',\n        name: 'Phase 4A: Next-Gen AI Integration',\n        description: 'Advanced AI and neural processing integration',\n        startDate: Date.now() + 90 * 24 * 60 * 60 * 1000, // 3 months\n        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000, // 1 year\n        objectives: [\n          'Integrate Neural Processing Units',\n          'Deploy Edge AI Inference',\n          'Implement Predictive Maintenance',\n        ],\n        enhancements: ['neural_processing_units', 'edge_ai_inference', 'predictive_maintenance'],\n        milestones: [\n          {\n            id: 'npu_integration',\n            name: 'NPU Integration Complete',\n            date: Date.now() + 180 * 24 * 60 * 60 * 1000,\n            criteria: ['NPU APIs integrated', 'Performance benchmarks met'],\n            status: 'pending',\n          },\n        ],\n        resources: {\n          budget: 425000,\n          team: ['ai_engineers', 'hardware_specialists', 'performance_engineers'],\n          infrastructure: ['npu_devices', 'edge_servers', 'monitoring_tools'],\n        },\n        risks: [\n          {\n            description: 'NPU hardware availability',\n            probability: 30,\n            impact: 70,\n            mitigation: 'Multiple vendor partnerships',\n          },\n        ],\n      },\n      {\n        id: 'phase_4b',\n        name: 'Phase 4B: Immersive Technologies',\n        description: 'AR/VR and advanced visualization',\n        startDate: Date.now() + 365 * 24 * 60 * 60 * 1000, // 1 year\n        endDate: Date.now() + 2 * 365 * 24 * 60 * 60 * 1000, // 2 years\n        objectives: [\n          'Implement AR Coaching',\n          'Deploy Real-time Ray Tracing',\n          'Create Immersive Training',\n        ],\n        enhancements: ['augmented_reality_coaching', 'real_time_ray_tracing'],\n        milestones: [\n          {\n            id: 'ar_prototype',\n            name: 'AR Coaching Prototype',\n            date: Date.now() + 450 * 24 * 60 * 60 * 1000,\n            criteria: ['AR prototype functional', 'User testing complete'],\n            status: 'pending',\n          },\n        ],\n        resources: {\n          budget: 700000,\n          team: ['ar_developers', 'graphics_engineers', 'ux_designers'],\n          infrastructure: ['ar_devices', 'graphics_workstations', 'motion_capture'],\n        },\n        risks: [\n          {\n            description: 'AR technology maturity',\n            probability: 40,\n            impact: 60,\n            mitigation: 'Phased implementation approach',\n          },\n        ],\n      },\n    ];\n\n    phases.forEach(phase => {\n      this.roadmapPhases.set(phase.id, phase);\n    });\n  }\n\n  private buildInnovationPipeline(): void {\n    this.innovationPipeline = Array.from(this.enhancements.values());\n  }\n\n  private calculateInnovationReadiness(): number {\n    const readyEnhancements = this.innovationPipeline.filter(\n      enhancement => enhancement.feasibility.overall > 70\n    );\n    \n    return (readyEnhancements.length / this.innovationPipeline.length) * 100;\n  }\n\n  private calculateCompetitiveAdvantage(): number {\n    const highImpactTrends = Array.from(this.technologyTrends.values())\n      .filter(trend => trend.potentialImpact.marketPosition > 70);\n    \n    return (highImpactTrends.length / this.technologyTrends.size) * 100;\n  }\n\n  private calculatePriorityScore(enhancement: Enhancement): number {\n    const priorityWeights = { critical: 4, high: 3, medium: 2, low: 1 };\n    const priorityScore = priorityWeights[enhancement.priority] * 25;\n    \n    const impactScore = (\n      enhancement.impact.performance +\n      enhancement.impact.userExperience +\n      enhancement.impact.marketAdvantage\n    ) / 3;\n    \n    const feasibilityScore = enhancement.feasibility.overall;\n    \n    return (priorityScore + impactScore + feasibilityScore) / 3;\n  }\n}\n\n// Export singleton instance\nexport const futureEnhancementsManager = new FutureEnhancementsManager();\nexport default futureEnhancementsManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAiGMA,yBAAyB;EAiO7B,SAAAA,0BAAA,EAAc;IAAAC,eAAA,OAAAD,yBAAA;IAAA,KAhONE,YAAY,IAAAC,cAAA,GAAAC,CAAA,OAA6B,IAAIC,GAAG,CAAC,CAAC;IAAA,KAClDC,gBAAgB,IAAAH,cAAA,GAAAC,CAAA,OAAiC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAC1DE,aAAa,IAAAJ,cAAA,GAAAC,CAAA,OAA8B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACpDG,kBAAkB,IAAAL,cAAA,GAAAC,CAAA,OAAkB,EAAE;IAAA,KAE7BK,mBAAmB,IAAAN,cAAA,GAAAC,CAAA,OAAoD,CACtF;MACEM,EAAE,EAAE,sBAAsB;MAC1BC,IAAI,EAAE,gCAAgC;MACtCC,WAAW,EAAE,8DAA8D;MAC3EC,QAAQ,EAAE,aAAa;MACvBC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,eAAe;MAC3BC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE;QACbC,UAAU,EAAE,CAAC,mBAAmB,EAAE,oBAAoB,CAAC;QACvDC,cAAc,EAAE,CAAC,sBAAsB,CAAC;QACxCC,SAAS,EAAE,CAAC,qBAAqB,EAAE,qBAAqB;MAC1D,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,CAAC,EAAE;QAClBC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,eAAe,EAAE,IAAI;QACrBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,MAAM;QACdV,cAAc,EAAE,CAAC,mBAAmB,EAAE,sBAAsB;MAC9D;IACF,CAAC,EACD;MACEV,EAAE,EAAE,yBAAyB;MAC7BC,IAAI,EAAE,oCAAoC;MAC1CC,WAAW,EAAE,mDAAmD;MAChEC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,QAAQ;MACfC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,SAAS;MACrBC,YAAY,EAAE,CAAC,gBAAgB,CAAC;MAChCC,aAAa,EAAE;QACbC,UAAU,EAAE,CAAC,UAAU,EAAE,mBAAmB,CAAC;QAC7CC,cAAc,EAAE,CAAC,aAAa,CAAC;QAC/BC,SAAS,EAAE,CAAC,qBAAqB,EAAE,sBAAsB;MAC3D,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,CAAC,EAAE;QAClBC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,eAAe,EAAE,GAAG;QACpBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,MAAM;QACdV,cAAc,EAAE,CAAC,cAAc,EAAE,iBAAiB;MACpD;IACF,CAAC,EACD;MACEV,EAAE,EAAE,uBAAuB;MAC3BC,IAAI,EAAE,gCAAgC;MACtCC,WAAW,EAAE,6DAA6D;MAC1EC,QAAQ,EAAE,UAAU;MACpBC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,SAAS;MACrBC,YAAY,EAAE,CAAC,aAAa,CAAC;MAC7BC,aAAa,EAAE;QACbC,UAAU,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAC;QAC/CC,cAAc,EAAE,CAAC,iBAAiB,CAAC;QACnCC,SAAS,EAAE,CAAC,sBAAsB,EAAE,aAAa;MACnD,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,eAAe,EAAE,IAAI;QACrBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,MAAM;QACdV,cAAc,EAAE,CAAC,SAAS,EAAE,uBAAuB;MACrD;IACF,CAAC,EACD;MACEV,EAAE,EAAE,mBAAmB;MACvBC,IAAI,EAAE,gCAAgC;MACtCC,WAAW,EAAE,mEAAmE;MAChFC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,aAAa;MACpBC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,SAAS;MACrBC,YAAY,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC;MAC5CC,aAAa,EAAE;QACbC,UAAU,EAAE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;QACxDC,cAAc,EAAE,CAAC,oBAAoB,CAAC;QACtCC,SAAS,EAAE,CAAC,gBAAgB,EAAE,iBAAiB;MACjD,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,CAAC,CAAC;QACjBC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,eAAe,EAAE,GAAG;QACpBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,MAAM;QACdV,cAAc,EAAE,CAAC,cAAc,EAAE,iBAAiB;MACpD;IACF,CAAC,EACD;MACEV,EAAE,EAAE,iCAAiC;MACrCC,IAAI,EAAE,kCAAkC;MACxCC,WAAW,EAAE,wDAAwD;MACrEC,QAAQ,EAAE,WAAW;MACrBC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE,SAAS;MACrBC,YAAY,EAAE,EAAE;MAChBC,aAAa,EAAE;QACbC,UAAU,EAAE,CAAC,sBAAsB,EAAE,iBAAiB,CAAC;QACvDC,cAAc,EAAE,CAAC,kBAAkB,CAAC;QACpCC,SAAS,EAAE,CAAC,wBAAwB,EAAE,cAAc;MACtD,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,eAAe,EAAE,GAAG;QACpBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,MAAM;QACdV,cAAc,EAAE,CAAC,oBAAoB;MACvC;IACF,CAAC,EACD;MACEV,EAAE,EAAE,4BAA4B;MAChCC,IAAI,EAAE,4BAA4B;MAClCC,WAAW,EAAE,wDAAwD;MACrEC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,QAAQ;MACfC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,SAAS;MACrBC,YAAY,EAAE,CAAC,aAAa,EAAE,YAAY,CAAC;MAC3CC,aAAa,EAAE;QACbC,UAAU,EAAE,CAAC,eAAe,EAAE,iBAAiB,CAAC;QAChDC,cAAc,EAAE,CAAC,YAAY,CAAC;QAC9BC,SAAS,EAAE,CAAC,gBAAgB,EAAE,kBAAkB;MAClD,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,EAAE;QACjBC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,eAAe,EAAE,IAAI;QACrBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,MAAM;QACdV,cAAc,EAAE,CAAC,aAAa,EAAE,gBAAgB;MAClD;IACF,CAAC,EACD;MACEV,EAAE,EAAE,wBAAwB;MAC5BC,IAAI,EAAE,+BAA+B;MACrCC,WAAW,EAAE,gEAAgE;MAC7EC,QAAQ,EAAE,IAAI;MACdC,KAAK,EAAE,aAAa;MACpBC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,UAAU;MACtBC,YAAY,EAAE,CAAC,YAAY,CAAC;MAC5BC,aAAa,EAAE;QACbC,UAAU,EAAE,CAAC,sBAAsB,EAAE,mBAAmB,CAAC;QACzDC,cAAc,EAAE,CAAC,oBAAoB,CAAC;QACtCC,SAAS,EAAE,CAAC,kBAAkB,EAAE,mBAAmB;MACrD,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,CAAC,EAAE;QAClBC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,eAAe,EAAE,GAAG;QACpBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,KAAK;QACbV,cAAc,EAAE,CAAC,kBAAkB;MACrC;IACF,CAAC,EACD;MACEV,EAAE,EAAE,yBAAyB;MAC7BC,IAAI,EAAE,yBAAyB;MAC/BC,WAAW,EAAE,yDAAyD;MACtEC,QAAQ,EAAE,SAAS;MACnBC,KAAK,EAAE,UAAU;MACjBC,QAAQ,EAAE,MAAM;MAChBC,UAAU,EAAE,SAAS;MACrBC,YAAY,EAAE,CAAC,cAAc,CAAC;MAC9BC,aAAa,EAAE;QACbC,UAAU,EAAE,CAAC,aAAa,EAAE,gBAAgB,CAAC;QAC7CC,cAAc,EAAE,CAAC,mBAAmB,CAAC;QACrCC,SAAS,EAAE,CAAC,sBAAsB,EAAE,gBAAgB;MACtD,CAAC;MACDC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,cAAc,EAAE,EAAE;QAClBC,aAAa,EAAE,CAAC,EAAE;QAClBC,eAAe,EAAE;MACnB,CAAC;MACDC,SAAS,EAAE;QACTC,eAAe,EAAE,GAAG;QACpBC,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,MAAM;QACdV,cAAc,EAAE,CAAC,aAAa,EAAE,YAAY;MAC9C;IACF,CAAC,CACF;IAAAjB,cAAA,GAAA4B,CAAA;IAAA5B,cAAA,GAAAC,CAAA;IAGC,IAAI,CAAC4B,4BAA4B,CAAC,CAAC;EACrC;EAAC,OAAAC,YAAA,CAAAjC,yBAAA;IAAAkC,GAAA;IAAAC,KAAA;MAAA,IAAAC,6BAAA,GAAAC,iBAAA,CAKD,aAA4D;QAAAlC,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAC1D,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF,IAAI,CAACkC,sBAAsB,CAAC,CAAC;UAACnC,cAAA,GAAAC,CAAA;UAG9B,IAAI,CAACmC,0BAA0B,CAAC,CAAC;UAACpC,cAAA,GAAAC,CAAA;UAGlC,IAAI,CAACoC,mBAAmB,CAAC,CAAC;UAACrC,cAAA,GAAAC,CAAA;UAG3B,IAAI,CAACqC,uBAAuB,CAAC,CAAC;UAACtC,cAAA,GAAAC,CAAA;UAE/BsC,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACrE,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAzC,cAAA,GAAAC,CAAA;UACdsC,OAAO,CAACE,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QAC3E;MACF,CAAC;MAAA,SAlBaZ,4BAA4BA,CAAA;QAAA,OAAAI,6BAAA,CAAAS,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA5Bd,4BAA4B;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA,EAuB1C,SAAAY,mBAAmBA,CAAA,EAUjB;MAAA5C,cAAA,GAAA4B,CAAA;MACA,IAAMiB,MAAM,IAAA7C,cAAA,GAAAC,CAAA,QAAG6C,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC3C,aAAa,CAAC4C,MAAM,CAAC,CAAC,CAAC;MACtD,IAAMC,QAAmE,IAAAjD,cAAA,GAAAC,CAAA,QAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAG/E4C,MAAM,CAACK,OAAO,CAAC,UAAAvC,KAAK,EAAI;QAAAX,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QACtBU,KAAK,CAACwC,UAAU,CAACD,OAAO,CAAC,UAAAE,SAAS,EAAI;UAAApD,cAAA,GAAA4B,CAAA;UAAA5B,cAAA,GAAAC,CAAA;UACpCgD,QAAQ,CAACI,IAAI,CAAC;YACZC,IAAI,EAAEF,SAAS,CAACE,IAAI;YACpBF,SAAS,EAAEA,SAAS,CAAC5C,IAAI;YACzBG,KAAK,EAAEA,KAAK,CAACH;UACf,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;MAACR,cAAA,GAAAC,CAAA;MAGHgD,QAAQ,CAACM,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;QAAAzD,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAAuD,CAAC,CAACF,IAAI,GAAGG,CAAC,CAACH,IAAI;MAAD,CAAC,CAAC;MAGxC,IAAMI,eAAe,IAAA1D,cAAA,GAAAC,CAAA,QAAG4C,MAAM,CAACc,MAAM,CAAC,UAACC,GAAG,EAAEjD,KAAK,EAAK;QAAAX,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAA2D,GAAG,GAAGjD,KAAK,CAACa,SAAS,CAACG,MAAM;MAAD,CAAC,EAAE,CAAC,CAAC;MAGtF,IAAMkC,WAAW,IAAA7D,cAAA,GAAAC,CAAA,QAAG,GAAG;MAGvB,IAAM6D,QAAQ,IAAA9D,cAAA,GAAAC,CAAA,QAAG4C,MAAM,CAACkB,OAAO,CAAC,UAAApD,KAAK,EAAI;QAAAX,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAAU,KAAK,CAACqD,KAAK;MAAD,CAAC,CAAC;MACrD,IAAMC,cAAc,IAAAjE,cAAA,GAAAC,CAAA,QAAG;QACrBiE,IAAI,EAAEJ,QAAQ,CAACK,MAAM,CAAC,UAAAC,IAAI,EAAI;UAAApE,cAAA,GAAA4B,CAAA;UAAA5B,cAAA,GAAAC,CAAA;UAAA,OAAAmE,IAAI,CAACC,WAAW,GAAGD,IAAI,CAACjD,MAAM,GAAG,EAAE;QAAD,CAAC,CAAC,CAACmD,MAAM;QACzEC,MAAM,EAAET,QAAQ,CAACK,MAAM,CAAC,UAAAC,IAAI,EAAI;UAAApE,cAAA,GAAA4B,CAAA;UAAA5B,cAAA,GAAAC,CAAA;UAAA,QAAAD,cAAA,GAAAyD,CAAA,UAAAW,IAAI,CAACC,WAAW,GAAGD,IAAI,CAACjD,MAAM,GAAG,EAAE,MAAAnB,cAAA,GAAAyD,CAAA,UAAIW,IAAI,CAACC,WAAW,GAAGD,IAAI,CAACjD,MAAM,IAAI,EAAE;QAAD,CAAC,CAAC,CAACmD,MAAM;QACnHE,GAAG,EAAEV,QAAQ,CAACK,MAAM,CAAC,UAAAC,IAAI,EAAI;UAAApE,cAAA,GAAA4B,CAAA;UAAA5B,cAAA,GAAAC,CAAA;UAAA,OAAAmE,IAAI,CAACC,WAAW,GAAGD,IAAI,CAACjD,MAAM,IAAI,EAAE;QAAD,CAAC,CAAC,CAACmD;MACrE,CAAC;MAACtE,cAAA,GAAAC,CAAA;MAEF,OAAO;QACL4C,MAAM,EAANA,MAAM;QACNI,QAAQ,EAARA,QAAQ;QACRS,eAAe,EAAfA,eAAe;QACfG,WAAW,EAAXA,WAAW;QACXI,cAAc,EAAdA;MACF,CAAC;IACH;EAAC;IAAAlC,GAAA;IAAAC,KAAA,EAKD,SAAAyC,qBAAqBA,CAAA,EAMnB;MAAAzE,cAAA,GAAA4B,CAAA;MACA,IAAM8C,oBAA4C,IAAA1E,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MACvD,IAAM0E,iBAAyC,IAAA3E,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MACpD,IAAM2E,oBAA4C,IAAA5E,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAACD,cAAA,GAAAC,CAAA;MAExD,IAAI,CAACI,kBAAkB,CAAC6C,OAAO,CAAC,UAAA2B,WAAW,EAAI;QAAA7E,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAC7CyE,oBAAoB,CAACG,WAAW,CAACjE,QAAQ,CAAC,GAAG,CAAC,CAAAZ,cAAA,GAAAyD,CAAA,UAAAiB,oBAAoB,CAACG,WAAW,CAACjE,QAAQ,CAAC,MAAAZ,cAAA,GAAAyD,CAAA,UAAI,CAAC,KAAI,CAAC;QAACzD,cAAA,GAAAC,CAAA;QACnG0E,iBAAiB,CAACE,WAAW,CAAClE,KAAK,CAAC,GAAG,CAAC,CAAAX,cAAA,GAAAyD,CAAA,UAAAkB,iBAAiB,CAACE,WAAW,CAAClE,KAAK,CAAC,MAAAX,cAAA,GAAAyD,CAAA,UAAI,CAAC,KAAI,CAAC;QAACzD,cAAA,GAAAC,CAAA;QACvF2E,oBAAoB,CAACC,WAAW,CAACnE,QAAQ,CAAC,GAAG,CAAC,CAAAV,cAAA,GAAAyD,CAAA,UAAAmB,oBAAoB,CAACC,WAAW,CAACnE,QAAQ,CAAC,MAAAV,cAAA,GAAAyD,CAAA,UAAI,CAAC,KAAI,CAAC;MACpG,CAAC,CAAC;MAGF,IAAMqB,cAAc,IAAA9E,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC8E,4BAA4B,CAAC,CAAC;MAAC/E,cAAA,GAAAC,CAAA;MAE3D,OAAO;QACL+E,QAAQ,EAAE,IAAI,CAAC3E,kBAAkB;QACjCqE,oBAAoB,EAApBA,oBAAoB;QACpBC,iBAAiB,EAAjBA,iBAAiB;QACjBC,oBAAoB,EAApBA,oBAAoB;QACpBE,cAAc,EAAdA;MACF,CAAC;IACH;EAAC;IAAA/C,GAAA;IAAAC,KAAA,EAKD,SAAAiD,0BAA0BA,CAAA,EAUxB;MAAAjF,cAAA,GAAA4B,CAAA;MACA,IAAMsD,MAAM,IAAAlF,cAAA,GAAAC,CAAA,QAAG6C,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5C,gBAAgB,CAAC6C,MAAM,CAAC,CAAC,CAAC;MACzD,IAAMmC,oBAAoB,IAAAnF,cAAA,GAAAC,CAAA,QAAGiF,MAAM,CAACf,MAAM,CAAC,UAAAiB,KAAK,EAAI;QAAApF,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAAmF,KAAK,CAACC,QAAQ,KAAK,UAAU;MAAD,CAAC,CAAC;MAElF,IAAMC,uBAAuB,IAAAtF,cAAA,GAAAC,CAAA,QAAGiF,MAAM,CAACK,GAAG,CAAC,UAAAH,KAAK,EAAI;QAAApF,cAAA,GAAA4B,CAAA;QAClD,IAAI4D,cAAqD;QACzD,IAAIC,SAAiB;QACrB,IAAIxC,QAAgB;QAACjD,cAAA,GAAAC,CAAA;QAErB,IAAI,CAAAD,cAAA,GAAAyD,CAAA,UAAA2B,KAAK,CAACM,cAAc,GAAG,EAAE,MAAA1F,cAAA,GAAAyD,CAAA,UAAI2B,KAAK,CAACC,QAAQ,KAAK,QAAQ,GAAE;UAAArF,cAAA,GAAAyD,CAAA;UAAAzD,cAAA,GAAAC,CAAA;UAC5DuF,cAAc,GAAG,OAAO;UAACxF,cAAA,GAAAC,CAAA;UACzBwF,SAAS,GAAG,sCAAsC;UAACzF,cAAA,GAAAC,CAAA;UACnDgD,QAAQ,GAAG,WAAW;QACxB,CAAC,MAAM;UAAAjD,cAAA,GAAAyD,CAAA;UAAAzD,cAAA,GAAAC,CAAA;UAAA,IAAI,CAAAD,cAAA,GAAAyD,CAAA,UAAA2B,KAAK,CAACM,cAAc,GAAG,EAAE,MAAA1F,cAAA,GAAAyD,CAAA,UAAI2B,KAAK,CAACC,QAAQ,KAAK,YAAY,GAAE;YAAArF,cAAA,GAAAyD,CAAA;YAAAzD,cAAA,GAAAC,CAAA;YACvEuF,cAAc,GAAG,OAAO;YAACxF,cAAA,GAAAC,CAAA;YACzBwF,SAAS,GAAG,yCAAyC;YAACzF,cAAA,GAAAC,CAAA;YACtDgD,QAAQ,GAAG,YAAY;UACzB,CAAC,MAAM;YAAAjD,cAAA,GAAAyD,CAAA;YAAAzD,cAAA,GAAAC,CAAA;YAAA,IAAImF,KAAK,CAACM,cAAc,GAAG,EAAE,EAAE;cAAA1F,cAAA,GAAAyD,CAAA;cAAAzD,cAAA,GAAAC,CAAA;cACpCuF,cAAc,GAAG,QAAQ;cAACxF,cAAA,GAAAC,CAAA;cAC1BwF,SAAS,GAAG,sCAAsC;cAACzF,cAAA,GAAAC,CAAA;cACnDgD,QAAQ,GAAG,aAAa;YAC1B,CAAC,MAAM;cAAAjD,cAAA,GAAAyD,CAAA;cAAAzD,cAAA,GAAAC,CAAA;cACLuF,cAAc,GAAG,MAAM;cAACxF,cAAA,GAAAC,CAAA;cACxBwF,SAAS,GAAG,4BAA4B;cAACzF,cAAA,GAAAC,CAAA;cACzCgD,QAAQ,GAAG,YAAY;YACzB;UAAA;QAAA;QAACjD,cAAA,GAAAC,CAAA;QAED,OAAO;UACLe,UAAU,EAAEoE,KAAK,CAAC5E,IAAI;UACtBgF,cAAc,EAAdA,cAAc;UACdC,SAAS,EAATA,SAAS;UACTxC,QAAQ,EAARA;QACF,CAAC;MACH,CAAC,CAAC;MAGF,IAAM0C,oBAAoB,IAAA3F,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC2F,6BAA6B,CAAC,CAAC;MAAC5F,cAAA,GAAAC,CAAA;MAElE,OAAO;QACLiF,MAAM,EAANA,MAAM;QACNC,oBAAoB,EAApBA,oBAAoB;QACpBG,uBAAuB,EAAvBA,uBAAuB;QACvBK,oBAAoB,EAApBA;MACF,CAAC;IACH;EAAC;IAAA5D,GAAA;IAAAC,KAAA,EAKD,SAAA6D,sBAAsBA,CAAA,EAAkB;MAAA,IAAAC,KAAA;MAAA9F,cAAA,GAAA4B,CAAA;MAAA5B,cAAA,GAAAC,CAAA;MACtC,OAAO,IAAI,CAACI,kBAAkB,CAACkD,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;QAAAzD,cAAA,GAAA4B,CAAA;QAE5C,IAAMmE,MAAM,IAAA/F,cAAA,GAAAC,CAAA,QAAG6F,KAAI,CAACE,sBAAsB,CAACxC,CAAC,CAAC;QAC7C,IAAMyC,MAAM,IAAAjG,cAAA,GAAAC,CAAA,QAAG6F,KAAI,CAACE,sBAAsB,CAACvC,CAAC,CAAC;QAACzD,cAAA,GAAAC,CAAA;QAC9C,OAAOgG,MAAM,GAAGF,MAAM;MACxB,CAAC,CAAC;IACJ;EAAC;IAAAhE,GAAA;IAAAC,KAAA,EAKD,SAAAkE,qBAAqBA,CAAA,EAWnB;MAAAlG,cAAA,GAAA4B,CAAA;MACA,IAAM8B,eAAe,IAAA1D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACI,kBAAkB,CAACsD,MAAM,CAAC,UAACC,GAAG,EAAEiB,WAAW,EACtE;QAAA7E,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAA2D,GAAG,GAAGiB,WAAW,CAACrD,SAAS,CAACG,MAAM;MAAD,CAAC,EAAE,CACtC,CAAC;MAED,IAAMwE,iBAAyC,IAAAnG,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MACpD,IAAMmG,cAAsC,IAAApG,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAACD,cAAA,GAAAC,CAAA;MAElD,IAAI,CAACI,kBAAkB,CAAC6C,OAAO,CAAC,UAAA2B,WAAW,EAAI;QAAA7E,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAC7CkG,iBAAiB,CAACtB,WAAW,CAACnE,QAAQ,CAAC,GACrC,CAAC,CAAAV,cAAA,GAAAyD,CAAA,UAAA0C,iBAAiB,CAACtB,WAAW,CAACnE,QAAQ,CAAC,MAAAV,cAAA,GAAAyD,CAAA,UAAI,CAAC,KAAIoB,WAAW,CAACrD,SAAS,CAACG,MAAM;QAAC3B,cAAA,GAAAC,CAAA;QAChFmG,cAAc,CAACvB,WAAW,CAAClE,KAAK,CAAC,GAC/B,CAAC,CAAAX,cAAA,GAAAyD,CAAA,WAAA2C,cAAc,CAACvB,WAAW,CAAClE,KAAK,CAAC,MAAAX,cAAA,GAAAyD,CAAA,WAAI,CAAC,KAAIoB,WAAW,CAACrD,SAAS,CAACG,MAAM;MAC3E,CAAC,CAAC;MAAC3B,cAAA,GAAAC,CAAA;MAEH,OAAO;QACLyD,eAAe,EAAfA,eAAe;QACfyC,iBAAiB,EAAjBA,iBAAiB;QACjBC,cAAc,EAAdA,cAAc;QACdC,GAAG,EAAE;UACHC,SAAS,EAAE,EAAE;UACbC,UAAU,EAAE,GAAG;UACfC,QAAQ,EAAE;QACZ,CAAC;QACDC,aAAa,EAAE,EAAE;QACjBC,kBAAkB,EAAE;MACtB,CAAC;IACH;EAAC;IAAA3E,GAAA;IAAAC,KAAA,EAID,SAAQG,sBAAsBA,CAAA,EAAS;MAAA,IAAAwE,MAAA;MAAA3G,cAAA,GAAA4B,CAAA;MAAA5B,cAAA,GAAAC,CAAA;MACrC,IAAI,CAACK,mBAAmB,CAAC4C,OAAO,CAAC,UAAA2B,WAAW,EAAI;QAAA7E,cAAA,GAAA4B,CAAA;QAC9C,IAAMgF,eAA4B,IAAA5G,cAAA,GAAAC,CAAA,QAAA4G,MAAA,CAAAC,MAAA,KAC7BjC,WAAW;UACd5B,QAAQ,EAAE;YACR8D,cAAc,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;YACtEC,mBAAmB,EAAEJ,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;UACnF,CAAC;UACDE,WAAW,EAAEV,MAAI,CAACW,oBAAoB,CAACzC,WAAW;QAAC,GACpD;QAAC7E,cAAA,GAAAC,CAAA;QAEF0G,MAAI,CAAC5G,YAAY,CAACwH,GAAG,CAAC1C,WAAW,CAACtE,EAAE,EAAEqG,eAAe,CAAC;MACxD,CAAC,CAAC;IACJ;EAAC;IAAA7E,GAAA;IAAAC,KAAA,EAED,SAAQsF,oBAAoBA,CAACzC,WAAgB,EAA8B;MAAA7E,cAAA,GAAA4B,CAAA;MAEzE,IAAM4F,SAAS,IAAAxH,cAAA,GAAAC,CAAA,QAAG4E,WAAW,CAAChE,UAAU,KAAK,QAAQ,IAAAb,cAAA,GAAAyD,CAAA,WAAG,EAAE,KAAAzD,cAAA,GAAAyD,CAAA,WACzCoB,WAAW,CAAChE,UAAU,KAAK,UAAU,IAAAb,cAAA,GAAAyD,CAAA,WAAG,EAAE,KAAAzD,cAAA,GAAAyD,CAAA,WAC1CoB,WAAW,CAAChE,UAAU,KAAK,SAAS,IAAAb,cAAA,GAAAyD,CAAA,WAAG,EAAE,KAAAzD,cAAA,GAAAyD,CAAA,WAAG,EAAE;MAE/D,IAAMgE,QAAQ,IAAAzH,cAAA,GAAAC,CAAA,QAAG4E,WAAW,CAACrD,SAAS,CAACG,MAAM,GAAG,MAAM,IAAA3B,cAAA,GAAAyD,CAAA,WAAG,EAAE,KAAAzD,cAAA,GAAAyD,CAAA,WAC3CoB,WAAW,CAACrD,SAAS,CAACG,MAAM,GAAG,MAAM,IAAA3B,cAAA,GAAAyD,CAAA,WAAG,EAAE,KAAAzD,cAAA,GAAAyD,CAAA,WAAG,EAAE;MAE/D,IAAMR,QAAQ,IAAAjD,cAAA,GAAAC,CAAA,QAAG4E,WAAW,CAAClE,KAAK,KAAK,aAAa,IAAAX,cAAA,GAAAyD,CAAA,WAAG,EAAE,KAAAzD,cAAA,GAAAyD,CAAA,WACzCoB,WAAW,CAAClE,KAAK,KAAK,QAAQ,IAAAX,cAAA,GAAAyD,CAAA,WAAG,EAAE,KAAAzD,cAAA,GAAAyD,CAAA,WACnCoB,WAAW,CAAClE,KAAK,KAAK,UAAU,IAAAX,cAAA,GAAAyD,CAAA,WAAG,EAAE,KAAAzD,cAAA,GAAAyD,CAAA,WAAG,EAAE;MAE1D,IAAMiE,OAAO,IAAA1H,cAAA,GAAAC,CAAA,QAAG,CAACuH,SAAS,GAAGC,QAAQ,GAAGxE,QAAQ,IAAI,CAAC;MAACjD,cAAA,GAAAC,CAAA;MAEtD,OAAO;QAAEuH,SAAS,EAATA,SAAS;QAAEC,QAAQ,EAARA,QAAQ;QAAExE,QAAQ,EAARA,QAAQ;QAAEyE,OAAO,EAAPA;MAAQ,CAAC;IACnD;EAAC;IAAA3F,GAAA;IAAAC,KAAA,EAED,SAAQI,0BAA0BA,CAAA,EAAS;MAAA,IAAAuF,MAAA;MAAA3H,cAAA,GAAA4B,CAAA;MACzC,IAAMsD,MAAkD,IAAAlF,cAAA,GAAAC,CAAA,QAAG,CACzD;QACEM,EAAE,EAAE,mBAAmB;QACvBC,IAAI,EAAE,mBAAmB;QACzBC,WAAW,EAAE,4CAA4C;QACzDC,QAAQ,EAAE,WAAW;QACrB2E,QAAQ,EAAE,UAAU;QACpBuC,YAAY,EAAE,CAAC;QACflC,cAAc,EAAE,EAAE;QAClBmC,gBAAgB,EAAE,EAAE;QACpB7D,KAAK,EAAE,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;QACvD8D,aAAa,EAAE,CAAC,iCAAiC,EAAE,uBAAuB;MAC5E,CAAC,EACD;QACEvH,EAAE,EAAE,mBAAmB;QACvBC,IAAI,EAAE,yBAAyB;QAC/BC,WAAW,EAAE,iCAAiC;QAC9CC,QAAQ,EAAE,UAAU;QACpB2E,QAAQ,EAAE,YAAY;QACtBuC,YAAY,EAAE,EAAE;QAChBlC,cAAc,EAAE,EAAE;QAClBmC,gBAAgB,EAAE,EAAE;QACpB7D,KAAK,EAAE,CAAC,qBAAqB,EAAE,MAAM,CAAC;QACtC8D,aAAa,EAAE,CAAC,iBAAiB,EAAE,mBAAmB;MACxD,CAAC,EACD;QACEvH,EAAE,EAAE,SAAS;QACbC,IAAI,EAAE,mBAAmB;QACzBC,WAAW,EAAE,+BAA+B;QAC5CC,QAAQ,EAAE,IAAI;QACd2E,QAAQ,EAAE,YAAY;QACtBuC,YAAY,EAAE,EAAE;QAChBlC,cAAc,EAAE,EAAE;QAClBmC,gBAAgB,EAAE,EAAE;QACpB7D,KAAK,EAAE,CAAC,6BAA6B,EAAE,YAAY,CAAC;QACpD8D,aAAa,EAAE,CAAC,mBAAmB,EAAE,SAAS;MAChD,CAAC,CACF;MAAC9H,cAAA,GAAAC,CAAA;MAEFiF,MAAM,CAAChC,OAAO,CAAC,UAAAkC,KAAK,EAAI;QAAApF,cAAA,GAAA4B,CAAA;QACtB,IAAMmG,SAA0B,IAAA/H,cAAA,GAAAC,CAAA,QAAA4G,MAAA,CAAAC,MAAA,KAC3B1B,KAAK;UACR4C,eAAe,EAAE;YACf5G,WAAW,EAAE,EAAE,GAAG8F,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YACpCc,IAAI,EAAE,EAAE,GAAGf,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YAC7BtG,UAAU,EAAE,EAAE,GAAGqG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;YACnCe,cAAc,EAAE,EAAE,GAAGhB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;UACvC;QAAC,GACF;QAACnH,cAAA,GAAAC,CAAA;QAEF0H,MAAI,CAACxH,gBAAgB,CAACoH,GAAG,CAACnC,KAAK,CAAC7E,EAAE,EAAEwH,SAAS,CAAC;MAChD,CAAC,CAAC;IACJ;EAAC;IAAAhG,GAAA;IAAAC,KAAA,EAED,SAAQK,mBAAmBA,CAAA,EAAS;MAAA,IAAA8F,MAAA;MAAAnI,cAAA,GAAA4B,CAAA;MAClC,IAAMiB,MAAsB,IAAA7C,cAAA,GAAAC,CAAA,QAAG,CAC7B;QACEM,EAAE,EAAE,UAAU;QACdC,IAAI,EAAE,mCAAmC;QACzCC,WAAW,EAAE,+CAA+C;QAC5D2H,SAAS,EAAEpB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAChDoB,OAAO,EAAErB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QAC/CqB,UAAU,EAAE,CACV,mCAAmC,EACnC,0BAA0B,EAC1B,kCAAkC,CACnC;QACDvI,YAAY,EAAE,CAAC,yBAAyB,EAAE,mBAAmB,EAAE,wBAAwB,CAAC;QACxFoD,UAAU,EAAE,CACV;UACE5C,EAAE,EAAE,iBAAiB;UACrBC,IAAI,EAAE,0BAA0B;UAChC8C,IAAI,EAAE0D,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;UAC5CsB,QAAQ,EAAE,CAAC,qBAAqB,EAAE,4BAA4B,CAAC;UAC/DC,MAAM,EAAE;QACV,CAAC,CACF;QACDhH,SAAS,EAAE;UACTG,MAAM,EAAE,MAAM;UACd8G,IAAI,EAAE,CAAC,cAAc,EAAE,sBAAsB,EAAE,uBAAuB,CAAC;UACvExH,cAAc,EAAE,CAAC,aAAa,EAAE,cAAc,EAAE,kBAAkB;QACpE,CAAC;QACD+C,KAAK,EAAE,CACL;UACEvD,WAAW,EAAE,2BAA2B;UACxC4D,WAAW,EAAE,EAAE;UACflD,MAAM,EAAE,EAAE;UACVuH,UAAU,EAAE;QACd,CAAC;MAEL,CAAC,EACD;QACEnI,EAAE,EAAE,UAAU;QACdC,IAAI,EAAE,kCAAkC;QACxCC,WAAW,EAAE,kCAAkC;QAC/C2H,SAAS,EAAEpB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QACjDoB,OAAO,EAAErB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;QACnDqB,UAAU,EAAE,CACV,uBAAuB,EACvB,8BAA8B,EAC9B,2BAA2B,CAC5B;QACDvI,YAAY,EAAE,CAAC,4BAA4B,EAAE,uBAAuB,CAAC;QACrEoD,UAAU,EAAE,CACV;UACE5C,EAAE,EAAE,cAAc;UAClBC,IAAI,EAAE,uBAAuB;UAC7B8C,IAAI,EAAE0D,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;UAC5CsB,QAAQ,EAAE,CAAC,yBAAyB,EAAE,uBAAuB,CAAC;UAC9DC,MAAM,EAAE;QACV,CAAC,CACF;QACDhH,SAAS,EAAE;UACTG,MAAM,EAAE,MAAM;UACd8G,IAAI,EAAE,CAAC,eAAe,EAAE,oBAAoB,EAAE,cAAc,CAAC;UAC7DxH,cAAc,EAAE,CAAC,YAAY,EAAE,uBAAuB,EAAE,gBAAgB;QAC1E,CAAC;QACD+C,KAAK,EAAE,CACL;UACEvD,WAAW,EAAE,wBAAwB;UACrC4D,WAAW,EAAE,EAAE;UACflD,MAAM,EAAE,EAAE;UACVuH,UAAU,EAAE;QACd,CAAC;MAEL,CAAC,CACF;MAAC1I,cAAA,GAAAC,CAAA;MAEF4C,MAAM,CAACK,OAAO,CAAC,UAAAvC,KAAK,EAAI;QAAAX,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QACtBkI,MAAI,CAAC/H,aAAa,CAACmH,GAAG,CAAC5G,KAAK,CAACJ,EAAE,EAAEI,KAAK,CAAC;MACzC,CAAC,CAAC;IACJ;EAAC;IAAAoB,GAAA;IAAAC,KAAA,EAED,SAAQM,uBAAuBA,CAAA,EAAS;MAAAtC,cAAA,GAAA4B,CAAA;MAAA5B,cAAA,GAAAC,CAAA;MACtC,IAAI,CAACI,kBAAkB,GAAGyC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAChD,YAAY,CAACiD,MAAM,CAAC,CAAC,CAAC;IAClE;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAED,SAAQ+C,4BAA4BA,CAAA,EAAW;MAAA/E,cAAA,GAAA4B,CAAA;MAC7C,IAAM+G,iBAAiB,IAAA3I,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACI,kBAAkB,CAAC8D,MAAM,CACtD,UAAAU,WAAW,EAAI;QAAA7E,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAA4E,WAAW,CAACwC,WAAW,CAACK,OAAO,GAAG,EAAE;MAAD,CACpD,CAAC;MAAC1H,cAAA,GAAAC,CAAA;MAEF,OAAQ0I,iBAAiB,CAACrE,MAAM,GAAG,IAAI,CAACjE,kBAAkB,CAACiE,MAAM,GAAI,GAAG;IAC1E;EAAC;IAAAvC,GAAA;IAAAC,KAAA,EAED,SAAQ4D,6BAA6BA,CAAA,EAAW;MAAA5F,cAAA,GAAA4B,CAAA;MAC9C,IAAMgH,gBAAgB,IAAA5I,cAAA,GAAAC,CAAA,QAAG6C,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC5C,gBAAgB,CAAC6C,MAAM,CAAC,CAAC,CAAC,CAChEmB,MAAM,CAAC,UAAAiB,KAAK,EAAI;QAAApF,cAAA,GAAA4B,CAAA;QAAA5B,cAAA,GAAAC,CAAA;QAAA,OAAAmF,KAAK,CAAC4C,eAAe,CAACE,cAAc,GAAG,EAAE;MAAD,CAAC,CAAC;MAAClI,cAAA,GAAAC,CAAA;MAE9D,OAAQ2I,gBAAgB,CAACtE,MAAM,GAAG,IAAI,CAACnE,gBAAgB,CAAC0I,IAAI,GAAI,GAAG;IACrE;EAAC;IAAA9G,GAAA;IAAAC,KAAA,EAED,SAAQgE,sBAAsBA,CAACnB,WAAwB,EAAU;MAAA7E,cAAA,GAAA4B,CAAA;MAC/D,IAAMkH,eAAe,IAAA9I,cAAA,GAAAC,CAAA,QAAG;QAAE8I,QAAQ,EAAE,CAAC;QAAE7E,IAAI,EAAE,CAAC;QAAEK,MAAM,EAAE,CAAC;QAAEC,GAAG,EAAE;MAAE,CAAC;MACnE,IAAMwE,aAAa,IAAAhJ,cAAA,GAAAC,CAAA,QAAG6I,eAAe,CAACjE,WAAW,CAACjE,QAAQ,CAAC,GAAG,EAAE;MAEhE,IAAMqI,WAAW,IAAAjJ,cAAA,GAAAC,CAAA,QAAG,CAClB4E,WAAW,CAAC1D,MAAM,CAACC,WAAW,GAC9ByD,WAAW,CAAC1D,MAAM,CAACE,cAAc,GACjCwD,WAAW,CAAC1D,MAAM,CAACI,eAAe,IAChC,CAAC;MAEL,IAAM2H,gBAAgB,IAAAlJ,cAAA,GAAAC,CAAA,QAAG4E,WAAW,CAACwC,WAAW,CAACK,OAAO;MAAC1H,cAAA,GAAAC,CAAA;MAEzD,OAAO,CAAC+I,aAAa,GAAGC,WAAW,GAAGC,gBAAgB,IAAI,CAAC;IAC7D;EAAC;AAAA;AAIH,OAAO,IAAMC,yBAAyB,IAAAnJ,cAAA,GAAAC,CAAA,SAAG,IAAIJ,yBAAyB,CAAC,CAAC;AACxE,eAAesJ,yBAAyB", "ignoreList": []}