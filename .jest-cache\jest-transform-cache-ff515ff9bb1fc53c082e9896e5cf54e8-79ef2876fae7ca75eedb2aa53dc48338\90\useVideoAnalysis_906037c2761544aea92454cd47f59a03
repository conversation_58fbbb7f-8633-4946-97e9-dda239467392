7b71b3609feb1c07b21379065ff3794b
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_v3kg9l3uj() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useVideoAnalysis.ts";
  var hash = "0b4fa563c7f59b9fa90dc0773f0e21f0fc5df83c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useVideoAnalysis.ts",
    statementMap: {
      "0": {
        start: {
          line: 64,
          column: 46
        },
        end: {
          line: 64,
          column: 57
        }
      },
      "1": {
        start: {
          line: 65,
          column: 40
        },
        end: {
          line: 65,
          column: 55
        }
      },
      "2": {
        start: {
          line: 66,
          column: 50
        },
        end: {
          line: 66,
          column: 65
        }
      },
      "3": {
        start: {
          line: 67,
          column: 48
        },
        end: {
          line: 67,
          column: 86
        }
      },
      "4": {
        start: {
          line: 68,
          column: 28
        },
        end: {
          line: 68,
          column: 57
        }
      },
      "5": {
        start: {
          line: 69,
          column: 19
        },
        end: {
          line: 69,
          column: 28
        }
      },
      "6": {
        start: {
          line: 72,
          column: 47
        },
        end: {
          line: 190,
          column: 3
        }
      },
      "7": {
        start: {
          line: 192,
          column: 22
        },
        end: {
          line: 264,
          column: 12
        }
      },
      "8": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 196,
          column: 5
        }
      },
      "9": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 194,
          column: 41
        }
      },
      "10": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 195,
          column: 13
        }
      },
      "11": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 263,
          column: 5
        }
      },
      "12": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 199,
          column: 21
        }
      },
      "13": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 200,
          column: 27
        }
      },
      "14": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 28
        }
      },
      "15": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 202,
          column: 33
        }
      },
      "16": {
        start: {
          line: 203,
          column: 6
        },
        end: {
          line: 203,
          column: 31
        }
      },
      "17": {
        start: {
          line: 206,
          column: 27
        },
        end: {
          line: 213,
          column: 7
        }
      },
      "18": {
        start: {
          line: 211,
          column: 10
        },
        end: {
          line: 211,
          column: 49
        }
      },
      "19": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 217,
          column: 7
        }
      },
      "20": {
        start: {
          line: 216,
          column: 8
        },
        end: {
          line: 216,
          column: 44
        }
      },
      "21": {
        start: {
          line: 220,
          column: 6
        },
        end: {
          line: 220,
          column: 29
        }
      },
      "22": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 221,
          column: 27
        }
      },
      "23": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 257,
          column: 7
        }
      },
      "24": {
        start: {
          line: 226,
          column: 28
        },
        end: {
          line: 226,
          column: 57
        }
      },
      "25": {
        start: {
          line: 229,
          column: 38
        },
        end: {
          line: 232,
          column: 9
        }
      },
      "26": {
        start: {
          line: 235,
          column: 32
        },
        end: {
          line: 238,
          column: 9
        }
      },
      "27": {
        start: {
          line: 240,
          column: 8
        },
        end: {
          line: 240,
          column: 30
        }
      },
      "28": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 241,
          column: 34
        }
      },
      "29": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 242,
          column: 44
        }
      },
      "30": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 245,
          column: 70
        }
      },
      "31": {
        start: {
          line: 248,
          column: 31
        },
        end: {
          line: 252,
          column: 9
        }
      },
      "32": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 254,
          column: 30
        }
      },
      "33": {
        start: {
          line: 255,
          column: 8
        },
        end: {
          line: 255,
          column: 34
        }
      },
      "34": {
        start: {
          line: 256,
          column: 8
        },
        end: {
          line: 256,
          column: 43
        }
      },
      "35": {
        start: {
          line: 260,
          column: 6
        },
        end: {
          line: 260,
          column: 69
        }
      },
      "36": {
        start: {
          line: 261,
          column: 6
        },
        end: {
          line: 261,
          column: 27
        }
      },
      "37": {
        start: {
          line: 262,
          column: 6
        },
        end: {
          line: 262,
          column: 28
        }
      },
      "38": {
        start: {
          line: 266,
          column: 24
        },
        end: {
          line: 272,
          column: 8
        }
      },
      "39": {
        start: {
          line: 267,
          column: 4
        },
        end: {
          line: 267,
          column: 25
        }
      },
      "40": {
        start: {
          line: 268,
          column: 4
        },
        end: {
          line: 268,
          column: 26
        }
      },
      "41": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 269,
          column: 31
        }
      },
      "42": {
        start: {
          line: 270,
          column: 4
        },
        end: {
          line: 270,
          column: 29
        }
      },
      "43": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 271,
          column: 19
        }
      },
      "44": {
        start: {
          line: 274,
          column: 22
        },
        end: {
          line: 317,
          column: 29
        }
      },
      "45": {
        start: {
          line: 275,
          column: 4
        },
        end: {
          line: 275,
          column: 42
        }
      },
      "46": {
        start: {
          line: 275,
          column: 35
        },
        end: {
          line: 275,
          column: 42
        }
      },
      "47": {
        start: {
          line: 277,
          column: 4
        },
        end: {
          line: 316,
          column: 5
        }
      },
      "48": {
        start: {
          line: 279,
          column: 30
        },
        end: {
          line: 301,
          column: 17
        }
      },
      "49": {
        start: {
          line: 289,
          column: 32
        },
        end: {
          line: 289,
          column: 63
        }
      },
      "50": {
        start: {
          line: 290,
          column: 29
        },
        end: {
          line: 290,
          column: 42
        }
      },
      "51": {
        start: {
          line: 292,
          column: 62
        },
        end: {
          line: 292,
          column: 91
        }
      },
      "52": {
        start: {
          line: 293,
          column: 65
        },
        end: {
          line: 293,
          column: 87
        }
      },
      "53": {
        start: {
          line: 294,
          column: 65
        },
        end: {
          line: 294,
          column: 87
        }
      },
      "54": {
        start: {
          line: 295,
          column: 65
        },
        end: {
          line: 295,
          column: 87
        }
      },
      "55": {
        start: {
          line: 303,
          column: 6
        },
        end: {
          line: 306,
          column: 7
        }
      },
      "56": {
        start: {
          line: 304,
          column: 8
        },
        end: {
          line: 304,
          column: 54
        }
      },
      "57": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 305,
          column: 62
        }
      },
      "58": {
        start: {
          line: 308,
          column: 6
        },
        end: {
          line: 308,
          column: 58
        }
      },
      "59": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 314,
          column: 48
        }
      },
      "60": {
        start: {
          line: 315,
          column: 6
        },
        end: {
          line: 315,
          column: 48
        }
      },
      "61": {
        start: {
          line: 320,
          column: 25
        },
        end: {
          line: 362,
          column: 8
        }
      },
      "62": {
        start: {
          line: 321,
          column: 4
        },
        end: {
          line: 361,
          column: 5
        }
      },
      "63": {
        start: {
          line: 322,
          column: 33
        },
        end: {
          line: 326,
          column: 17
        }
      },
      "64": {
        start: {
          line: 328,
          column: 35
        },
        end: {
          line: 332,
          column: 17
        }
      },
      "65": {
        start: {
          line: 334,
          column: 39
        },
        end: {
          line: 339,
          column: 17
        }
      },
      "66": {
        start: {
          line: 341,
          column: 6
        },
        end: {
          line: 349,
          column: 8
        }
      },
      "67": {
        start: {
          line: 347,
          column: 49
        },
        end: {
          line: 347,
          column: 56
        }
      },
      "68": {
        start: {
          line: 351,
          column: 6
        },
        end: {
          line: 351,
          column: 59
        }
      },
      "69": {
        start: {
          line: 352,
          column: 6
        },
        end: {
          line: 360,
          column: 8
        }
      },
      "70": {
        start: {
          line: 365,
          column: 35
        },
        end: {
          line: 391,
          column: 8
        }
      },
      "71": {
        start: {
          line: 366,
          column: 4
        },
        end: {
          line: 390,
          column: 6
        }
      },
      "72": {
        start: {
          line: 368,
          column: 87
        },
        end: {
          line: 375,
          column: 7
        }
      },
      "73": {
        start: {
          line: 378,
          column: 97
        },
        end: {
          line: 384,
          column: 7
        }
      },
      "74": {
        start: {
          line: 394,
          column: 26
        },
        end: {
          line: 399,
          column: 8
        }
      },
      "75": {
        start: {
          line: 395,
          column: 20
        },
        end: {
          line: 395,
          column: 41
        }
      },
      "76": {
        start: {
          line: 396,
          column: 20
        },
        end: {
          line: 396,
          column: 44
        }
      },
      "77": {
        start: {
          line: 397,
          column: 29
        },
        end: {
          line: 397,
          column: 41
        }
      },
      "78": {
        start: {
          line: 398,
          column: 4
        },
        end: {
          line: 398,
          column: 72
        }
      },
      "79": {
        start: {
          line: 402,
          column: 34
        },
        end: {
          line: 412,
          column: 8
        }
      },
      "80": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 405,
          column: 5
        }
      },
      "81": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 404,
          column: 50
        }
      },
      "82": {
        start: {
          line: 407,
          column: 4
        },
        end: {
          line: 411,
          column: 8
        }
      },
      "83": {
        start: {
          line: 407,
          column: 67
        },
        end: {
          line: 411,
          column: 5
        }
      },
      "84": {
        start: {
          line: 415,
          column: 28
        },
        end: {
          line: 438,
          column: 8
        }
      },
      "85": {
        start: {
          line: 416,
          column: 35
        },
        end: {
          line: 416,
          column: 37
        }
      },
      "86": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 424,
          column: 5
        }
      },
      "87": {
        start: {
          line: 419,
          column: 6
        },
        end: {
          line: 423,
          column: 9
        }
      },
      "88": {
        start: {
          line: 426,
          column: 4
        },
        end: {
          line: 435,
          column: 5
        }
      },
      "89": {
        start: {
          line: 427,
          column: 6
        },
        end: {
          line: 434,
          column: 9
        }
      },
      "90": {
        start: {
          line: 428,
          column: 8
        },
        end: {
          line: 433,
          column: 11
        }
      },
      "91": {
        start: {
          line: 437,
          column: 4
        },
        end: {
          line: 437,
          column: 75
        }
      },
      "92": {
        start: {
          line: 440,
          column: 2
        },
        end: {
          line: 449,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useVideoAnalysis",
        decl: {
          start: {
            line: 63,
            column: 16
          },
          end: {
            line: 63,
            column: 32
          }
        },
        loc: {
          start: {
            line: 63,
            column: 59
          },
          end: {
            line: 450,
            column: 1
          }
        },
        line: 63
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 192,
            column: 34
          },
          end: {
            line: 192,
            column: 35
          }
        },
        loc: {
          start: {
            line: 192,
            column: 115
          },
          end: {
            line: 264,
            column: 3
          }
        },
        line: 192
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 210,
            column: 9
          }
        },
        loc: {
          start: {
            line: 210,
            column: 22
          },
          end: {
            line: 212,
            column: 9
          }
        },
        line: 210
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 266,
            column: 36
          },
          end: {
            line: 266,
            column: 37
          }
        },
        loc: {
          start: {
            line: 266,
            column: 42
          },
          end: {
            line: 272,
            column: 3
          }
        },
        line: 266
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 274,
            column: 34
          },
          end: {
            line: 274,
            column: 35
          }
        },
        loc: {
          start: {
            line: 274,
            column: 46
          },
          end: {
            line: 317,
            column: 3
          }
        },
        line: 274
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 289,
            column: 20
          },
          end: {
            line: 289,
            column: 21
          }
        },
        loc: {
          start: {
            line: 289,
            column: 32
          },
          end: {
            line: 289,
            column: 63
          }
        },
        line: 289
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 290,
            column: 17
          },
          end: {
            line: 290,
            column: 18
          }
        },
        loc: {
          start: {
            line: 290,
            column: 29
          },
          end: {
            line: 290,
            column: 42
          }
        },
        line: 290
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 292,
            column: 57
          },
          end: {
            line: 292,
            column: 58
          }
        },
        loc: {
          start: {
            line: 292,
            column: 62
          },
          end: {
            line: 292,
            column: 91
          }
        },
        line: 292
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 293,
            column: 60
          },
          end: {
            line: 293,
            column: 61
          }
        },
        loc: {
          start: {
            line: 293,
            column: 65
          },
          end: {
            line: 293,
            column: 87
          }
        },
        line: 293
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 294,
            column: 60
          },
          end: {
            line: 294,
            column: 61
          }
        },
        loc: {
          start: {
            line: 294,
            column: 65
          },
          end: {
            line: 294,
            column: 87
          }
        },
        line: 294
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 295,
            column: 60
          },
          end: {
            line: 295,
            column: 61
          }
        },
        loc: {
          start: {
            line: 295,
            column: 65
          },
          end: {
            line: 295,
            column: 87
          }
        },
        line: 295
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 320,
            column: 37
          },
          end: {
            line: 320,
            column: 38
          }
        },
        loc: {
          start: {
            line: 320,
            column: 63
          },
          end: {
            line: 362,
            column: 3
          }
        },
        line: 320
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 347,
            column: 44
          },
          end: {
            line: 347,
            column: 45
          }
        },
        loc: {
          start: {
            line: 347,
            column: 49
          },
          end: {
            line: 347,
            column: 56
          }
        },
        line: 347
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 365,
            column: 47
          },
          end: {
            line: 365,
            column: 48
          }
        },
        loc: {
          start: {
            line: 365,
            column: 114
          },
          end: {
            line: 391,
            column: 3
          }
        },
        line: 365
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 368,
            column: 74
          },
          end: {
            line: 368,
            column: 75
          }
        },
        loc: {
          start: {
            line: 368,
            column: 87
          },
          end: {
            line: 375,
            column: 7
          }
        },
        line: 368
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 378,
            column: 80
          },
          end: {
            line: 378,
            column: 81
          }
        },
        loc: {
          start: {
            line: 378,
            column: 97
          },
          end: {
            line: 384,
            column: 7
          }
        },
        line: 378
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 394,
            column: 38
          },
          end: {
            line: 394,
            column: 39
          }
        },
        loc: {
          start: {
            line: 394,
            column: 62
          },
          end: {
            line: 399,
            column: 3
          }
        },
        line: 394
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 402,
            column: 46
          },
          end: {
            line: 402,
            column: 47
          }
        },
        loc: {
          start: {
            line: 402,
            column: 96
          },
          end: {
            line: 412,
            column: 3
          }
        },
        line: 402
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 407,
            column: 46
          },
          end: {
            line: 407,
            column: 47
          }
        },
        loc: {
          start: {
            line: 407,
            column: 67
          },
          end: {
            line: 411,
            column: 5
          }
        },
        line: 407
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 415,
            column: 40
          },
          end: {
            line: 415,
            column: 41
          }
        },
        loc: {
          start: {
            line: 415,
            column: 75
          },
          end: {
            line: 438,
            column: 3
          }
        },
        line: 415
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 427,
            column: 43
          },
          end: {
            line: 427,
            column: 44
          }
        },
        loc: {
          start: {
            line: 427,
            column: 76
          },
          end: {
            line: 434,
            column: 7
          }
        },
        line: 427
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 193,
            column: 4
          },
          end: {
            line: 196,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 193,
            column: 4
          },
          end: {
            line: 196,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 193
      },
      "1": {
        loc: {
          start: {
            line: 215,
            column: 6
          },
          end: {
            line: 217,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 215,
            column: 6
          },
          end: {
            line: 217,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 215
      },
      "2": {
        loc: {
          start: {
            line: 260,
            column: 15
          },
          end: {
            line: 260,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 260,
            column: 38
          },
          end: {
            line: 260,
            column: 49
          }
        }, {
          start: {
            line: 260,
            column: 52
          },
          end: {
            line: 260,
            column: 67
          }
        }],
        line: 260
      },
      "3": {
        loc: {
          start: {
            line: 275,
            column: 4
          },
          end: {
            line: 275,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 275,
            column: 4
          },
          end: {
            line: 275,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 275
      },
      "4": {
        loc: {
          start: {
            line: 275,
            column: 8
          },
          end: {
            line: 275,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 275,
            column: 8
          },
          end: {
            line: 275,
            column: 24
          }
        }, {
          start: {
            line: 275,
            column: 28
          },
          end: {
            line: 275,
            column: 33
          }
        }],
        line: 275
      },
      "5": {
        loc: {
          start: {
            line: 287,
            column: 31
          },
          end: {
            line: 287,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 287,
            column: 31
          },
          end: {
            line: 287,
            column: 70
          }
        }, {
          start: {
            line: 287,
            column: 74
          },
          end: {
            line: 287,
            column: 97
          }
        }],
        line: 287
      },
      "6": {
        loc: {
          start: {
            line: 292,
            column: 19
          },
          end: {
            line: 292,
            column: 104
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 19
          },
          end: {
            line: 292,
            column: 99
          }
        }, {
          start: {
            line: 292,
            column: 103
          },
          end: {
            line: 292,
            column: 104
          }
        }],
        line: 292
      },
      "7": {
        loc: {
          start: {
            line: 293,
            column: 22
          },
          end: {
            line: 293,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 293,
            column: 22
          },
          end: {
            line: 293,
            column: 95
          }
        }, {
          start: {
            line: 293,
            column: 99
          },
          end: {
            line: 293,
            column: 100
          }
        }],
        line: 293
      },
      "8": {
        loc: {
          start: {
            line: 294,
            column: 22
          },
          end: {
            line: 294,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 294,
            column: 22
          },
          end: {
            line: 294,
            column: 95
          }
        }, {
          start: {
            line: 294,
            column: 99
          },
          end: {
            line: 294,
            column: 100
          }
        }],
        line: 294
      },
      "9": {
        loc: {
          start: {
            line: 295,
            column: 22
          },
          end: {
            line: 295,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 295,
            column: 22
          },
          end: {
            line: 295,
            column: 95
          }
        }, {
          start: {
            line: 295,
            column: 99
          },
          end: {
            line: 295,
            column: 100
          }
        }],
        line: 295
      },
      "10": {
        loc: {
          start: {
            line: 297,
            column: 21
          },
          end: {
            line: 297,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 297,
            column: 21
          },
          end: {
            line: 297,
            column: 54
          }
        }, {
          start: {
            line: 297,
            column: 58
          },
          end: {
            line: 297,
            column: 62
          }
        }],
        line: 297
      },
      "11": {
        loc: {
          start: {
            line: 303,
            column: 6
          },
          end: {
            line: 306,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 303,
            column: 6
          },
          end: {
            line: 306,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 303
      },
      "12": {
        loc: {
          start: {
            line: 342,
            column: 20
          },
          end: {
            line: 342,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 342,
            column: 20
          },
          end: {
            line: 342,
            column: 41
          }
        }, {
          start: {
            line: 342,
            column: 45
          },
          end: {
            line: 342,
            column: 59
          }
        }],
        line: 342
      },
      "13": {
        loc: {
          start: {
            line: 343,
            column: 22
          },
          end: {
            line: 346,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 343,
            column: 22
          },
          end: {
            line: 343,
            column: 32
          }
        }, {
          start: {
            line: 343,
            column: 36
          },
          end: {
            line: 346,
            column: 9
          }
        }],
        line: 343
      },
      "14": {
        loc: {
          start: {
            line: 347,
            column: 24
          },
          end: {
            line: 347,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 347,
            column: 24
          },
          end: {
            line: 347,
            column: 57
          }
        }, {
          start: {
            line: 347,
            column: 61
          },
          end: {
            line: 347,
            column: 63
          }
        }],
        line: 347
      },
      "15": {
        loc: {
          start: {
            line: 348,
            column: 15
          },
          end: {
            line: 348,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 348,
            column: 15
          },
          end: {
            line: 348,
            column: 30
          }
        }, {
          start: {
            line: 348,
            column: 34
          },
          end: {
            line: 348,
            column: 36
          }
        }],
        line: 348
      },
      "16": {
        loc: {
          start: {
            line: 374,
            column: 14
          },
          end: {
            line: 374,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 374,
            column: 72
          },
          end: {
            line: 374,
            column: 82
          }
        }, {
          start: {
            line: 374,
            column: 85
          },
          end: {
            line: 374,
            column: 98
          }
        }],
        line: 374
      },
      "17": {
        loc: {
          start: {
            line: 374,
            column: 14
          },
          end: {
            line: 374,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 374,
            column: 14
          },
          end: {
            line: 374,
            column: 42
          }
        }, {
          start: {
            line: 374,
            column: 46
          },
          end: {
            line: 374,
            column: 69
          }
        }],
        line: 374
      },
      "18": {
        loc: {
          start: {
            line: 380,
            column: 15
          },
          end: {
            line: 380,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 380,
            column: 15
          },
          end: {
            line: 380,
            column: 25
          }
        }, {
          start: {
            line: 380,
            column: 29
          },
          end: {
            line: 380,
            column: 40
          }
        }],
        line: 380
      },
      "19": {
        loc: {
          start: {
            line: 403,
            column: 4
          },
          end: {
            line: 405,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 403,
            column: 4
          },
          end: {
            line: 405,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 403
      },
      "20": {
        loc: {
          start: {
            line: 403,
            column: 8
          },
          end: {
            line: 403,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 403,
            column: 8
          },
          end: {
            line: 403,
            column: 27
          }
        }, {
          start: {
            line: 403,
            column: 31
          },
          end: {
            line: 403,
            column: 62
          }
        }],
        line: 403
      },
      "21": {
        loc: {
          start: {
            line: 408,
            column: 13
          },
          end: {
            line: 408,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 408,
            column: 13
          },
          end: {
            line: 408,
            column: 31
          }
        }, {
          start: {
            line: 408,
            column: 35
          },
          end: {
            line: 408,
            column: 54
          }
        }],
        line: 408
      },
      "22": {
        loc: {
          start: {
            line: 409,
            column: 24
          },
          end: {
            line: 409,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 409,
            column: 24
          },
          end: {
            line: 409,
            column: 46
          }
        }, {
          start: {
            line: 409,
            column: 50
          },
          end: {
            line: 409,
            column: 52
          }
        }],
        line: 409
      },
      "23": {
        loc: {
          start: {
            line: 418,
            column: 4
          },
          end: {
            line: 424,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 418,
            column: 4
          },
          end: {
            line: 424,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 418
      },
      "24": {
        loc: {
          start: {
            line: 426,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 426,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 426
      },
      "25": {
        loc: {
          start: {
            line: 437,
            column: 11
          },
          end: {
            line: 437,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 437,
            column: 33
          },
          end: {
            line: 437,
            column: 41
          }
        }, {
          start: {
            line: 437,
            column: 44
          },
          end: {
            line: 437,
            column: 74
          }
        }],
        line: 437
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0b4fa563c7f59b9fa90dc0773f0e21f0fc5df83c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_v3kg9l3uj = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_v3kg9l3uj();
import { useState, useCallback } from 'react';
import { storageService } from "../services/storage";
import { supabase } from "../lib/supabase";
import { useAuth } from "../contexts/AuthContext";
import { aiAnalysisService } from "../services/aiAnalysis";
export function useVideoAnalysis() {
  cov_v3kg9l3uj().f[0]++;
  var _ref = (cov_v3kg9l3uj().s[0]++, useState(0)),
    _ref2 = _slicedToArray(_ref, 2),
    uploadProgress = _ref2[0],
    setUploadProgress = _ref2[1];
  var _ref3 = (cov_v3kg9l3uj().s[1]++, useState(false)),
    _ref4 = _slicedToArray(_ref3, 2),
    isAnalyzing = _ref4[0],
    setIsAnalyzing = _ref4[1];
  var _ref5 = (cov_v3kg9l3uj().s[2]++, useState(false)),
    _ref6 = _slicedToArray(_ref5, 2),
    analysisComplete = _ref6[0],
    setAnalysisComplete = _ref6[1];
  var _ref7 = (cov_v3kg9l3uj().s[3]++, useState(null)),
    _ref8 = _slicedToArray(_ref7, 2),
    analysisResults = _ref8[0],
    setAnalysisResults = _ref8[1];
  var _ref9 = (cov_v3kg9l3uj().s[4]++, useState(null)),
    _ref0 = _slicedToArray(_ref9, 2),
    error = _ref0[0],
    setError = _ref0[1];
  var _ref1 = (cov_v3kg9l3uj().s[5]++, useAuth()),
    user = _ref1.user;
  var mockAnalysisResults = (cov_v3kg9l3uj().s[6]++, {
    overallScore: 78,
    videoHighlights: [{
      id: 'highlight-1',
      title: 'Excellent Toss Placement',
      description: 'Perfect serve toss height and positioning',
      timestamp: '0:23',
      thumbnail: 'https://images.pexels.com/photos/209977/pexels-photo-209977.jpeg?auto=compress&cs=tinysrgb&w=120&h=80',
      type: 'positive'
    }, {
      id: 'highlight-2',
      title: 'Improve Knee Bend',
      description: 'More knee bend needed for power generation',
      timestamp: '1:15',
      thumbnail: 'https://images.pexels.com/photos/1263426/pexels-photo-1263426.jpeg?auto=compress&cs=tinysrgb&w=120&h=80',
      type: 'improvement'
    }, {
      id: 'highlight-3',
      title: 'Great Follow-through',
      description: 'Consistent follow-through on forehand shots',
      timestamp: '2:08',
      thumbnail: 'https://images.pexels.com/photos/1432039/pexels-photo-1432039.jpeg?auto=compress&cs=tinysrgb&w=120&h=80',
      type: 'positive'
    }, {
      id: 'highlight-4',
      title: 'Footwork Timing',
      description: 'Late preparation on backhand side',
      timestamp: '3:42',
      thumbnail: 'https://images.pexels.com/photos/1263426/pexels-photo-1263426.jpeg?auto=compress&cs=tinysrgb&w=120&h=80',
      type: 'improvement'
    }],
    techniqueRatings: [{
      skill: 'Serve Technique',
      score: 85,
      feedback: 'Excellent toss consistency and contact point. Work on leg drive for more power.'
    }, {
      skill: 'Forehand',
      score: 78,
      feedback: 'Good follow-through and topspin generation. Improve preparation timing.'
    }, {
      skill: 'Backhand',
      score: 65,
      feedback: 'Solid technique but late preparation. Focus on earlier shoulder turn.'
    }, {
      skill: 'Footwork',
      score: 72,
      feedback: 'Good court coverage. Work on split-step timing and recovery.'
    }, {
      skill: 'Court Positioning',
      score: 80,
      feedback: 'Excellent baseline positioning. Practice approach shot positioning.'
    }],
    aiFeedback: [{
      area: 'Serve Toss',
      feedback: 'Your serve toss is consistently placed in the optimal position. This is a major strength that allows for powerful and accurate serves.',
      type: 'positive'
    }, {
      area: 'Knee Bend',
      feedback: 'Increasing your knee bend during the serve motion will help generate more upward power and improve your serve speed.',
      type: 'improvement',
      improvement: 'Practice shadow serves focusing on deeper knee bend'
    }, {
      area: 'Forehand Follow-through',
      feedback: 'Your follow-through is excellent, creating good topspin and control. This technique will serve you well in matches.',
      type: 'positive'
    }, {
      area: 'Backhand Preparation',
      feedback: 'Your backhand preparation is slightly late. Earlier shoulder turn will give you more time and power.',
      type: 'improvement',
      improvement: 'Practice early preparation drills'
    }],
    recommendedDrills: [{
      id: 'drill-1',
      title: 'Deep Knee Bend Serves',
      focus: 'Serve power generation',
      duration: '15 min',
      difficulty: 'Intermediate'
    }, {
      id: 'drill-2',
      title: 'Early Preparation Drill',
      focus: 'Backhand timing',
      duration: '20 min',
      difficulty: 'Beginner'
    }, {
      id: 'drill-3',
      title: 'Split-Step Timing',
      focus: 'Footwork improvement',
      duration: '10 min',
      difficulty: 'Intermediate'
    }, {
      id: 'drill-4',
      title: 'Approach Shot Practice',
      focus: 'Net positioning',
      duration: '25 min',
      difficulty: 'Advanced'
    }],
    sessionId: `session-${Date.now()}`
  });
  var uploadVideo = (cov_v3kg9l3uj().s[7]++, useCallback(function () {
    var _ref10 = _asyncToGenerator(function* (file, filename, source) {
      cov_v3kg9l3uj().f[1]++;
      cov_v3kg9l3uj().s[8]++;
      if (!user) {
        cov_v3kg9l3uj().b[0][0]++;
        cov_v3kg9l3uj().s[9]++;
        setError('User not authenticated');
        cov_v3kg9l3uj().s[10]++;
        return;
      } else {
        cov_v3kg9l3uj().b[0][1]++;
      }
      cov_v3kg9l3uj().s[11]++;
      try {
        cov_v3kg9l3uj().s[12]++;
        setError(null);
        cov_v3kg9l3uj().s[13]++;
        setUploadProgress(0);
        cov_v3kg9l3uj().s[14]++;
        setIsAnalyzing(false);
        cov_v3kg9l3uj().s[15]++;
        setAnalysisComplete(false);
        cov_v3kg9l3uj().s[16]++;
        setAnalysisResults(null);
        var uploadResult = (cov_v3kg9l3uj().s[17]++, yield storageService.uploadVideo(file, user.id, filename, function (progress) {
          cov_v3kg9l3uj().f[2]++;
          cov_v3kg9l3uj().s[18]++;
          setUploadProgress(progress.percentage);
        }));
        cov_v3kg9l3uj().s[19]++;
        if (uploadResult.error) {
          cov_v3kg9l3uj().b[1][0]++;
          cov_v3kg9l3uj().s[20]++;
          throw new Error(uploadResult.error);
        } else {
          cov_v3kg9l3uj().b[1][1]++;
        }
        cov_v3kg9l3uj().s[21]++;
        setUploadProgress(100);
        cov_v3kg9l3uj().s[22]++;
        setIsAnalyzing(true);
        cov_v3kg9l3uj().s[23]++;
        try {
          var userProfile = (cov_v3kg9l3uj().s[24]++, yield getUserProfile(user.id));
          var comprehensiveAnalysis = (cov_v3kg9l3uj().s[25]++, yield aiAnalysisService.analyzeTrainingVideo(uploadResult.url, userProfile));
          var _analysisResults = (cov_v3kg9l3uj().s[26]++, convertToAnalysisResults(comprehensiveAnalysis, uploadResult.url));
          cov_v3kg9l3uj().s[27]++;
          setIsAnalyzing(false);
          cov_v3kg9l3uj().s[28]++;
          setAnalysisComplete(true);
          cov_v3kg9l3uj().s[29]++;
          setAnalysisResults(_analysisResults);
        } catch (aiError) {
          cov_v3kg9l3uj().s[30]++;
          console.error('AI analysis failed, using fallback:', aiError);
          var updatedResults = (cov_v3kg9l3uj().s[31]++, Object.assign({}, mockAnalysisResults, {
            sessionId: `session-${Date.now()}`,
            videoUrl: uploadResult.url
          }));
          cov_v3kg9l3uj().s[32]++;
          setIsAnalyzing(false);
          cov_v3kg9l3uj().s[33]++;
          setAnalysisComplete(true);
          cov_v3kg9l3uj().s[34]++;
          setAnalysisResults(updatedResults);
        }
      } catch (err) {
        cov_v3kg9l3uj().s[35]++;
        setError(err instanceof Error ? (cov_v3kg9l3uj().b[2][0]++, err.message) : (cov_v3kg9l3uj().b[2][1]++, 'Upload failed'));
        cov_v3kg9l3uj().s[36]++;
        setUploadProgress(0);
        cov_v3kg9l3uj().s[37]++;
        setIsAnalyzing(false);
      }
    });
    return function (_x, _x2, _x3) {
      return _ref10.apply(this, arguments);
    };
  }(), [user]));
  var resetAnalysis = (cov_v3kg9l3uj().s[38]++, useCallback(function () {
    cov_v3kg9l3uj().f[3]++;
    cov_v3kg9l3uj().s[39]++;
    setUploadProgress(0);
    cov_v3kg9l3uj().s[40]++;
    setIsAnalyzing(false);
    cov_v3kg9l3uj().s[41]++;
    setAnalysisComplete(false);
    cov_v3kg9l3uj().s[42]++;
    setAnalysisResults(null);
    cov_v3kg9l3uj().s[43]++;
    setError(null);
  }, []));
  var saveSession = (cov_v3kg9l3uj().s[44]++, useCallback(_asyncToGenerator(function* () {
    cov_v3kg9l3uj().f[4]++;
    cov_v3kg9l3uj().s[45]++;
    if ((cov_v3kg9l3uj().b[4][0]++, !analysisResults) || (cov_v3kg9l3uj().b[4][1]++, !user)) {
      cov_v3kg9l3uj().b[3][0]++;
      cov_v3kg9l3uj().s[46]++;
      return;
    } else {
      cov_v3kg9l3uj().b[3][1]++;
    }
    cov_v3kg9l3uj().s[47]++;
    try {
      var _analysisResults$aiFe, _analysisResults$tech, _analysisResults$tech2, _analysisResults$tech3, _analysisResults$tech4;
      var _ref12 = (cov_v3kg9l3uj().s[48]++, yield supabase.from('training_sessions').insert({
          user_id: user.id,
          session_type: 'video_analysis',
          title: 'Video Analysis Session',
          description: 'AI-powered video analysis of tennis technique',
          duration_minutes: 45,
          ai_feedback_summary: (cov_v3kg9l3uj().b[5][0]++, (_analysisResults$aiFe = analysisResults.aiFeedback[0]) == null ? void 0 : _analysisResults$aiFe.feedback) || (cov_v3kg9l3uj().b[5][1]++, 'AI analysis completed'),
          improvement_areas: analysisResults.aiFeedback.filter(function (feedback) {
            cov_v3kg9l3uj().f[5]++;
            cov_v3kg9l3uj().s[49]++;
            return feedback.type === 'improvement';
          }).map(function (feedback) {
            cov_v3kg9l3uj().f[6]++;
            cov_v3kg9l3uj().s[50]++;
            return feedback.area;
          }),
          skill_improvements: {
            serve: (cov_v3kg9l3uj().b[6][0]++, (_analysisResults$tech = analysisResults.techniqueRatings.find(function (r) {
              cov_v3kg9l3uj().f[7]++;
              cov_v3kg9l3uj().s[51]++;
              return r.skill === 'Serve Technique';
            })) == null ? void 0 : _analysisResults$tech.score) || (cov_v3kg9l3uj().b[6][1]++, 0),
            forehand: (cov_v3kg9l3uj().b[7][0]++, (_analysisResults$tech2 = analysisResults.techniqueRatings.find(function (r) {
              cov_v3kg9l3uj().f[8]++;
              cov_v3kg9l3uj().s[52]++;
              return r.skill === 'Forehand';
            })) == null ? void 0 : _analysisResults$tech2.score) || (cov_v3kg9l3uj().b[7][1]++, 0),
            backhand: (cov_v3kg9l3uj().b[8][0]++, (_analysisResults$tech3 = analysisResults.techniqueRatings.find(function (r) {
              cov_v3kg9l3uj().f[9]++;
              cov_v3kg9l3uj().s[53]++;
              return r.skill === 'Backhand';
            })) == null ? void 0 : _analysisResults$tech3.score) || (cov_v3kg9l3uj().b[8][1]++, 0),
            footwork: (cov_v3kg9l3uj().b[9][0]++, (_analysisResults$tech4 = analysisResults.techniqueRatings.find(function (r) {
              cov_v3kg9l3uj().f[10]++;
              cov_v3kg9l3uj().s[54]++;
              return r.skill === 'Footwork';
            })) == null ? void 0 : _analysisResults$tech4.score) || (cov_v3kg9l3uj().b[9][1]++, 0)
          },
          video_url: (cov_v3kg9l3uj().b[10][0]++, analysisResults.videoUrl) || (cov_v3kg9l3uj().b[10][1]++, null),
          overall_score: analysisResults.overallScore
        }).select().single()),
        data = _ref12.data,
        _error = _ref12.error;
      cov_v3kg9l3uj().s[55]++;
      if (_error) {
        cov_v3kg9l3uj().b[11][0]++;
        cov_v3kg9l3uj().s[56]++;
        console.error('Error saving session:', _error);
        cov_v3kg9l3uj().s[57]++;
        throw new Error('Failed to save session to database');
      } else {
        cov_v3kg9l3uj().b[11][1]++;
      }
      cov_v3kg9l3uj().s[58]++;
      console.log('Session saved successfully:', data.id);
    } catch (err) {
      cov_v3kg9l3uj().s[59]++;
      console.error('Save session error:', err);
      cov_v3kg9l3uj().s[60]++;
      throw new Error('Failed to save session');
    }
  }), [analysisResults, user]));
  var getUserProfile = (cov_v3kg9l3uj().s[61]++, useCallback(function () {
    var _ref13 = _asyncToGenerator(function* (userId) {
      cov_v3kg9l3uj().f[11]++;
      cov_v3kg9l3uj().s[62]++;
      try {
        var _ref14 = (cov_v3kg9l3uj().s[63]++, yield supabase.from('users').select('*').eq('id', userId).single()),
          userData = _ref14.data;
        var _ref15 = (cov_v3kg9l3uj().s[64]++, yield supabase.from('skill_stats').select('*').eq('user_id', userId).single()),
          skillStats = _ref15.data;
        var _ref16 = (cov_v3kg9l3uj().s[65]++, yield supabase.from('training_sessions').select('title').eq('user_id', userId).order('created_at', {
            ascending: false
          }).limit(5)),
          recentSessions = _ref16.data;
        cov_v3kg9l3uj().s[66]++;
        return {
          skillLevel: (cov_v3kg9l3uj().b[12][0]++, userData == null ? void 0 : userData.skill_level) || (cov_v3kg9l3uj().b[12][1]++, 'intermediate'),
          currentStats: (cov_v3kg9l3uj().b[13][0]++, skillStats) || (cov_v3kg9l3uj().b[13][1]++, {
            forehand: 50,
            backhand: 50,
            serve: 50,
            volley: 50,
            footwork: 50,
            strategy: 50,
            mental_game: 50
          }),
          recentSessions: (cov_v3kg9l3uj().b[14][0]++, recentSessions == null ? void 0 : recentSessions.map(function (s) {
            cov_v3kg9l3uj().f[12]++;
            cov_v3kg9l3uj().s[67]++;
            return s.title;
          })) || (cov_v3kg9l3uj().b[14][1]++, []),
          goals: (cov_v3kg9l3uj().b[15][0]++, userData == null ? void 0 : userData.goals) || (cov_v3kg9l3uj().b[15][1]++, [])
        };
      } catch (error) {
        cov_v3kg9l3uj().s[68]++;
        console.error('Error fetching user profile:', error);
        cov_v3kg9l3uj().s[69]++;
        return {
          skillLevel: 'intermediate',
          currentStats: {
            forehand: 50,
            backhand: 50,
            serve: 50,
            volley: 50,
            footwork: 50,
            strategy: 50,
            mental_game: 50
          },
          recentSessions: [],
          goals: []
        };
      }
    });
    return function (_x4) {
      return _ref13.apply(this, arguments);
    };
  }(), []));
  var convertToAnalysisResults = (cov_v3kg9l3uj().s[70]++, useCallback(function (comprehensiveAnalysis, videoUrl) {
    cov_v3kg9l3uj().f[13]++;
    cov_v3kg9l3uj().s[71]++;
    return {
      overallScore: comprehensiveAnalysis.videoAnalysis.overallScore,
      videoHighlights: comprehensiveAnalysis.videoAnalysis.highlights.map(function (h) {
        cov_v3kg9l3uj().f[14]++;
        cov_v3kg9l3uj().s[72]++;
        return {
          id: `highlight-${h.timestamp}`,
          title: h.type.replace('_', ' ').toUpperCase(),
          description: h.description,
          timestamp: formatTimestamp(h.timestamp),
          thumbnail: 'https://images.pexels.com/photos/209977/pexels-photo-209977.jpeg?auto=compress&cs=tinysrgb&w=120&h=80',
          type: (cov_v3kg9l3uj().b[17][0]++, h.type.includes('excellent')) || (cov_v3kg9l3uj().b[17][1]++, h.type.includes('good')) ? (cov_v3kg9l3uj().b[16][0]++, 'positive') : (cov_v3kg9l3uj().b[16][1]++, 'improvement')
        };
      }),
      techniqueRatings: convertTechniqueRatings(comprehensiveAnalysis.videoAnalysis.techniqueBreakdown),
      aiFeedback: convertAIFeedback(comprehensiveAnalysis.aiCoaching),
      recommendedDrills: comprehensiveAnalysis.aiCoaching.recommendedDrills.map(function (drill) {
        cov_v3kg9l3uj().f[15]++;
        cov_v3kg9l3uj().s[73]++;
        return {
          id: `drill-${Date.now()}-${Math.random()}`,
          title: (cov_v3kg9l3uj().b[18][0]++, drill.name) || (cov_v3kg9l3uj().b[18][1]++, drill.title),
          focus: drill.focus,
          duration: drill.duration,
          difficulty: drill.difficulty
        };
      }),
      sessionId: `session-${Date.now()}`,
      comprehensiveAnalysis: comprehensiveAnalysis,
      realTimeInsights: comprehensiveAnalysis.recommendations.immediate,
      performanceMetrics: comprehensiveAnalysis.performanceInsights
    };
  }, []));
  var formatTimestamp = (cov_v3kg9l3uj().s[74]++, useCallback(function (ms) {
    cov_v3kg9l3uj().f[16]++;
    var seconds = (cov_v3kg9l3uj().s[75]++, Math.floor(ms / 1000));
    var minutes = (cov_v3kg9l3uj().s[76]++, Math.floor(seconds / 60));
    var remainingSeconds = (cov_v3kg9l3uj().s[77]++, seconds % 60);
    cov_v3kg9l3uj().s[78]++;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []));
  var convertTechniqueRatings = (cov_v3kg9l3uj().s[79]++, useCallback(function (techniqueBreakdown) {
    cov_v3kg9l3uj().f[17]++;
    cov_v3kg9l3uj().s[80]++;
    if ((cov_v3kg9l3uj().b[20][0]++, !techniqueBreakdown) || (cov_v3kg9l3uj().b[20][1]++, techniqueBreakdown.length === 0)) {
      cov_v3kg9l3uj().b[19][0]++;
      cov_v3kg9l3uj().s[81]++;
      return mockAnalysisResults.techniqueRatings;
    } else {
      cov_v3kg9l3uj().b[19][1]++;
    }
    cov_v3kg9l3uj().s[82]++;
    return techniqueBreakdown.slice(0, 5).map(function (technique) {
      cov_v3kg9l3uj().f[18]++;
      cov_v3kg9l3uj().s[83]++;
      return {
        skill: (cov_v3kg9l3uj().b[21][0]++, technique.shotType) || (cov_v3kg9l3uj().b[21][1]++, 'Overall Technique'),
        score: Math.round((cov_v3kg9l3uj().b[22][0]++, technique.overallScore) || (cov_v3kg9l3uj().b[22][1]++, 75)),
        feedback: `${technique.shotType} analysis: ${technique.overallScore}% technique score`
      };
    });
  }, []));
  var convertAIFeedback = (cov_v3kg9l3uj().s[84]++, useCallback(function (aiCoaching) {
    cov_v3kg9l3uj().f[19]++;
    var feedback = (cov_v3kg9l3uj().s[85]++, []);
    cov_v3kg9l3uj().s[86]++;
    if (aiCoaching.personalizedTip) {
      cov_v3kg9l3uj().b[23][0]++;
      cov_v3kg9l3uj().s[87]++;
      feedback.push({
        area: 'Personalized Tip',
        feedback: aiCoaching.personalizedTip,
        type: 'positive'
      });
    } else {
      cov_v3kg9l3uj().b[23][1]++;
    }
    cov_v3kg9l3uj().s[88]++;
    if (aiCoaching.technicalFeedback) {
      cov_v3kg9l3uj().b[24][0]++;
      cov_v3kg9l3uj().s[89]++;
      aiCoaching.technicalFeedback.forEach(function (tech, index) {
        cov_v3kg9l3uj().f[20]++;
        cov_v3kg9l3uj().s[90]++;
        feedback.push({
          area: `Technical Point ${index + 1}`,
          feedback: tech,
          type: 'improvement',
          improvement: 'Focus on this area in practice'
        });
      });
    } else {
      cov_v3kg9l3uj().b[24][1]++;
    }
    cov_v3kg9l3uj().s[91]++;
    return feedback.length > 0 ? (cov_v3kg9l3uj().b[25][0]++, feedback) : (cov_v3kg9l3uj().b[25][1]++, mockAnalysisResults.aiFeedback);
  }, []));
  cov_v3kg9l3uj().s[92]++;
  return {
    uploadProgress: uploadProgress,
    isAnalyzing: isAnalyzing,
    analysisComplete: analysisComplete,
    analysisResults: analysisResults,
    error: error,
    uploadVideo: uploadVideo,
    resetAnalysis: resetAnalysis,
    saveSession: saveSession
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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