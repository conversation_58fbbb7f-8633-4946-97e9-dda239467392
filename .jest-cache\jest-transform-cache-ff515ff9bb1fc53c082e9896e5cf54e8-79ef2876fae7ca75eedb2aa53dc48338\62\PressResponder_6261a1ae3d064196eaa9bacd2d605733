32ab5828d36e9b761b034f157ce35db4
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
exports.__esModule = true;
exports.default = void 0;
var DELAY = 'DELAY';
var ERROR = 'ERROR';
var LONG_PRESS_DETECTED = 'LONG_PRESS_DETECTED';
var NOT_RESPONDER = 'NOT_RESPONDER';
var RESPONDER_ACTIVE_LONG_PRESS_START = 'RESPONDER_ACTIVE_LONG_PRESS_START';
var RESPONDER_ACTIVE_PRESS_START = 'RESPONDER_ACTIVE_PRESS_START';
var RESPONDER_INACTIVE_PRESS_START = 'RESPONDER_INACTIVE_PRESS_START';
var RESPONDER_GRANT = 'RESPONDER_GRANT';
var RESPONDER_RELEASE = 'RESPONDER_RELEASE';
var RESPONDER_TERMINATED = 'RESPONDER_TERMINATED';
var Transitions = Object.freeze({
  NOT_RESPONDER: {
    DELAY: ERROR,
    RESPONDER_GRANT: RESPONDER_INACTIVE_PRESS_START,
    RESPONDER_RELEASE: ERROR,
    RESPONDER_TERMINATED: ERROR,
    LONG_PRESS_DETECTED: ERROR
  },
  RESPONDER_INACTIVE_PRESS_START: {
    DELAY: RESPONDER_ACTIVE_PRESS_START,
    RESPONDER_GRANT: ERROR,
    RESPONDER_RELEASE: NOT_RESPONDER,
    RESPONDER_TERMINATED: NOT_RESPONDER,
    LONG_PRESS_DETECTED: ERROR
  },
  RESPONDER_ACTIVE_PRESS_START: {
    DELAY: ERROR,
    RESPONDER_GRANT: ERROR,
    RESPONDER_RELEASE: NOT_RESPONDER,
    RESPONDER_TERMINATED: NOT_RESPONDER,
    LONG_PRESS_DETECTED: RESPONDER_ACTIVE_LONG_PRESS_START
  },
  RESPONDER_ACTIVE_LONG_PRESS_START: {
    DELAY: ERROR,
    RESPONDER_GRANT: ERROR,
    RESPONDER_RELEASE: NOT_RESPONDER,
    RESPONDER_TERMINATED: NOT_RESPONDER,
    LONG_PRESS_DETECTED: RESPONDER_ACTIVE_LONG_PRESS_START
  },
  ERROR: {
    DELAY: NOT_RESPONDER,
    RESPONDER_GRANT: RESPONDER_INACTIVE_PRESS_START,
    RESPONDER_RELEASE: NOT_RESPONDER,
    RESPONDER_TERMINATED: NOT_RESPONDER,
    LONG_PRESS_DETECTED: NOT_RESPONDER
  }
});
var getElementRole = function getElementRole(element) {
  return element.getAttribute('role');
};
var getElementType = function getElementType(element) {
  return element.tagName.toLowerCase();
};
var isActiveSignal = function isActiveSignal(signal) {
  return signal === RESPONDER_ACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_LONG_PRESS_START;
};
var isButtonRole = function isButtonRole(element) {
  return getElementRole(element) === 'button';
};
var isPressStartSignal = function isPressStartSignal(signal) {
  return signal === RESPONDER_INACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_PRESS_START || signal === RESPONDER_ACTIVE_LONG_PRESS_START;
};
var isTerminalSignal = function isTerminalSignal(signal) {
  return signal === RESPONDER_TERMINATED || signal === RESPONDER_RELEASE;
};
var isValidKeyPress = function isValidKeyPress(event) {
  var key = event.key,
    target = event.target;
  var isSpacebar = key === ' ' || key === 'Spacebar';
  var isButtonish = getElementType(target) === 'button' || isButtonRole(target);
  return key === 'Enter' || isSpacebar && isButtonish;
};
var DEFAULT_LONG_PRESS_DELAY_MS = 450;
var DEFAULT_PRESS_DELAY_MS = 50;
var PressResponder = function () {
  function PressResponder(config) {
    (0, _classCallCheck2.default)(this, PressResponder);
    this._eventHandlers = null;
    this._isPointerTouch = false;
    this._longPressDelayTimeout = null;
    this._longPressDispatched = false;
    this._pressDelayTimeout = null;
    this._pressOutDelayTimeout = null;
    this._touchState = NOT_RESPONDER;
    this._responderElement = null;
    this.configure(config);
  }
  return (0, _createClass2.default)(PressResponder, [{
    key: "configure",
    value: function configure(config) {
      this._config = config;
    }
  }, {
    key: "reset",
    value: function reset() {
      this._cancelLongPressDelayTimeout();
      this._cancelPressDelayTimeout();
      this._cancelPressOutDelayTimeout();
    }
  }, {
    key: "getEventHandlers",
    value: function getEventHandlers() {
      if (this._eventHandlers == null) {
        this._eventHandlers = this._createEventHandlers();
      }
      return this._eventHandlers;
    }
  }, {
    key: "_createEventHandlers",
    value: function _createEventHandlers() {
      var _this = this;
      var start = function start(event, shouldDelay) {
        event.persist();
        _this._cancelPressOutDelayTimeout();
        _this._longPressDispatched = false;
        _this._selectionTerminated = false;
        _this._touchState = NOT_RESPONDER;
        _this._isPointerTouch = event.nativeEvent.type === 'touchstart';
        _this._receiveSignal(RESPONDER_GRANT, event);
        var delayPressStart = normalizeDelay(_this._config.delayPressStart, 0, DEFAULT_PRESS_DELAY_MS);
        if (shouldDelay !== false && delayPressStart > 0) {
          _this._pressDelayTimeout = setTimeout(function () {
            _this._receiveSignal(DELAY, event);
          }, delayPressStart);
        } else {
          _this._receiveSignal(DELAY, event);
        }
        var delayLongPress = normalizeDelay(_this._config.delayLongPress, 10, DEFAULT_LONG_PRESS_DELAY_MS);
        _this._longPressDelayTimeout = setTimeout(function () {
          _this._handleLongPress(event);
        }, delayLongPress + delayPressStart);
      };
      var end = function end(event) {
        _this._receiveSignal(RESPONDER_RELEASE, event);
      };
      var _keyupHandler = function keyupHandler(event) {
        var onPress = _this._config.onPress;
        var target = event.target;
        if (_this._touchState !== NOT_RESPONDER && isValidKeyPress(event)) {
          end(event);
          document.removeEventListener('keyup', _keyupHandler);
          var role = target.getAttribute('role');
          var elementType = getElementType(target);
          var isNativeInteractiveElement = role === 'link' || elementType === 'a' || elementType === 'button' || elementType === 'input' || elementType === 'select' || elementType === 'textarea';
          var isActiveElement = _this._responderElement === target;
          if (onPress != null && !isNativeInteractiveElement && isActiveElement) {
            onPress(event);
          }
          _this._responderElement = null;
        }
      };
      return {
        onStartShouldSetResponder: function onStartShouldSetResponder(event) {
          var disabled = _this._config.disabled;
          if (disabled && isButtonRole(event.currentTarget)) {
            event.stopPropagation();
          }
          if (disabled == null) {
            return true;
          }
          return !disabled;
        },
        onKeyDown: function onKeyDown(event) {
          var disabled = _this._config.disabled;
          var key = event.key,
            target = event.target;
          if (!disabled && isValidKeyPress(event)) {
            if (_this._touchState === NOT_RESPONDER) {
              start(event, false);
              _this._responderElement = target;
              document.addEventListener('keyup', _keyupHandler);
            }
            var isSpacebarKey = key === ' ' || key === 'Spacebar';
            var role = getElementRole(target);
            var isButtonLikeRole = role === 'button' || role === 'menuitem';
            if (isSpacebarKey && isButtonLikeRole && getElementType(target) !== 'button') {
              event.preventDefault();
            }
            event.stopPropagation();
          }
        },
        onResponderGrant: function onResponderGrant(event) {
          return start(event);
        },
        onResponderMove: function onResponderMove(event) {
          if (_this._config.onPressMove != null) {
            _this._config.onPressMove(event);
          }
          var touch = getTouchFromResponderEvent(event);
          if (_this._touchActivatePosition != null) {
            var deltaX = _this._touchActivatePosition.pageX - touch.pageX;
            var deltaY = _this._touchActivatePosition.pageY - touch.pageY;
            if (Math.hypot(deltaX, deltaY) > 10) {
              _this._cancelLongPressDelayTimeout();
            }
          }
        },
        onResponderRelease: function onResponderRelease(event) {
          return end(event);
        },
        onResponderTerminate: function onResponderTerminate(event) {
          if (event.nativeEvent.type === 'selectionchange') {
            _this._selectionTerminated = true;
          }
          _this._receiveSignal(RESPONDER_TERMINATED, event);
        },
        onResponderTerminationRequest: function onResponderTerminationRequest(event) {
          var _this$_config = _this._config,
            cancelable = _this$_config.cancelable,
            disabled = _this$_config.disabled,
            onLongPress = _this$_config.onLongPress;
          if (!disabled && onLongPress != null && _this._isPointerTouch && event.nativeEvent.type === 'contextmenu') {
            return false;
          }
          if (cancelable == null) {
            return true;
          }
          return cancelable;
        },
        onClick: function onClick(event) {
          var _this$_config2 = _this._config,
            disabled = _this$_config2.disabled,
            onPress = _this$_config2.onPress;
          if (!disabled) {
            event.stopPropagation();
            if (_this._longPressDispatched || _this._selectionTerminated) {
              event.preventDefault();
            } else if (onPress != null && event.altKey === false) {
              onPress(event);
            }
          } else {
            if (isButtonRole(event.currentTarget)) {
              event.stopPropagation();
            }
          }
        },
        onContextMenu: function onContextMenu(event) {
          var _this$_config3 = _this._config,
            disabled = _this$_config3.disabled,
            onLongPress = _this$_config3.onLongPress;
          if (!disabled) {
            if (onLongPress != null && _this._isPointerTouch && !event.defaultPrevented) {
              event.preventDefault();
              event.stopPropagation();
            }
          } else {
            if (isButtonRole(event.currentTarget)) {
              event.stopPropagation();
            }
          }
        }
      };
    }
  }, {
    key: "_receiveSignal",
    value: function _receiveSignal(signal, event) {
      var prevState = this._touchState;
      var nextState = null;
      if (Transitions[prevState] != null) {
        nextState = Transitions[prevState][signal];
      }
      if (this._touchState === NOT_RESPONDER && signal === RESPONDER_RELEASE) {
        return;
      }
      if (nextState == null || nextState === ERROR) {
        console.error("PressResponder: Invalid signal " + signal + " for state " + prevState + " on responder");
      } else if (prevState !== nextState) {
        this._performTransitionSideEffects(prevState, nextState, signal, event);
        this._touchState = nextState;
      }
    }
  }, {
    key: "_performTransitionSideEffects",
    value: function _performTransitionSideEffects(prevState, nextState, signal, event) {
      var _this2 = this;
      if (isTerminalSignal(signal)) {
        setTimeout(function () {
          _this2._isPointerTouch = false;
        }, 0);
        this._touchActivatePosition = null;
        this._cancelLongPressDelayTimeout();
      }
      if (isPressStartSignal(prevState) && signal === LONG_PRESS_DETECTED) {
        var onLongPress = this._config.onLongPress;
        if (onLongPress != null && event.nativeEvent.key == null) {
          onLongPress(event);
          this._longPressDispatched = true;
        }
      }
      var isPrevActive = isActiveSignal(prevState);
      var isNextActive = isActiveSignal(nextState);
      if (!isPrevActive && isNextActive) {
        this._activate(event);
      } else if (isPrevActive && !isNextActive) {
        this._deactivate(event);
      }
      if (isPressStartSignal(prevState) && signal === RESPONDER_RELEASE) {
        var _this$_config4 = this._config,
          _onLongPress = _this$_config4.onLongPress,
          onPress = _this$_config4.onPress;
        if (onPress != null) {
          var isPressCanceledByLongPress = _onLongPress != null && prevState === RESPONDER_ACTIVE_LONG_PRESS_START;
          if (!isPressCanceledByLongPress) {
            if (!isNextActive && !isPrevActive) {
              this._activate(event);
              this._deactivate(event);
            }
          }
        }
      }
      this._cancelPressDelayTimeout();
    }
  }, {
    key: "_activate",
    value: function _activate(event) {
      var _this$_config5 = this._config,
        onPressChange = _this$_config5.onPressChange,
        onPressStart = _this$_config5.onPressStart;
      var touch = getTouchFromResponderEvent(event);
      this._touchActivatePosition = {
        pageX: touch.pageX,
        pageY: touch.pageY
      };
      if (onPressStart != null) {
        onPressStart(event);
      }
      if (onPressChange != null) {
        onPressChange(true);
      }
    }
  }, {
    key: "_deactivate",
    value: function _deactivate(event) {
      var _this$_config6 = this._config,
        onPressChange = _this$_config6.onPressChange,
        onPressEnd = _this$_config6.onPressEnd;
      function end() {
        if (onPressEnd != null) {
          onPressEnd(event);
        }
        if (onPressChange != null) {
          onPressChange(false);
        }
      }
      var delayPressEnd = normalizeDelay(this._config.delayPressEnd);
      if (delayPressEnd > 0) {
        this._pressOutDelayTimeout = setTimeout(function () {
          end();
        }, delayPressEnd);
      } else {
        end();
      }
    }
  }, {
    key: "_handleLongPress",
    value: function _handleLongPress(event) {
      if (this._touchState === RESPONDER_ACTIVE_PRESS_START || this._touchState === RESPONDER_ACTIVE_LONG_PRESS_START) {
        this._receiveSignal(LONG_PRESS_DETECTED, event);
      }
    }
  }, {
    key: "_cancelLongPressDelayTimeout",
    value: function _cancelLongPressDelayTimeout() {
      if (this._longPressDelayTimeout != null) {
        clearTimeout(this._longPressDelayTimeout);
        this._longPressDelayTimeout = null;
      }
    }
  }, {
    key: "_cancelPressDelayTimeout",
    value: function _cancelPressDelayTimeout() {
      if (this._pressDelayTimeout != null) {
        clearTimeout(this._pressDelayTimeout);
        this._pressDelayTimeout = null;
      }
    }
  }, {
    key: "_cancelPressOutDelayTimeout",
    value: function _cancelPressOutDelayTimeout() {
      if (this._pressOutDelayTimeout != null) {
        clearTimeout(this._pressOutDelayTimeout);
        this._pressOutDelayTimeout = null;
      }
    }
  }]);
}();
exports.default = PressResponder;
function normalizeDelay(delay, min, fallback) {
  if (min === void 0) {
    min = 0;
  }
  if (fallback === void 0) {
    fallback = 0;
  }
  return Math.max(min, delay !== null && delay !== void 0 ? delay : fallback);
}
function getTouchFromResponderEvent(event) {
  var _event$nativeEvent = event.nativeEvent,
    changedTouches = _event$nativeEvent.changedTouches,
    touches = _event$nativeEvent.touches;
  if (touches != null && touches.length > 0) {
    return touches[0];
  }
  if (changedTouches != null && changedTouches.length > 0) {
    return changedTouches[0];
  }
  return event.nativeEvent;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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