{"version": 3, "names": ["React", "View", "StyleSheet", "jsx", "_jsx", "colors", "cov_xrq0811ty", "s", "white", "lightGray", "Card", "_ref", "children", "style", "_ref$variant", "variant", "b", "f", "styles", "base", "create", "borderRadius", "padding", "default", "backgroundColor", "elevated", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "bordered", "borderWidth", "borderColor"], "sources": ["Card.tsx"], "sourcesContent": ["import React from 'react';\nimport { View, StyleSheet, ViewStyle } from 'react-native';\n\nconst colors = {\n  white: '#ffffff',\n  lightGray: '#f9fafb',\n};\n\ninterface CardProps {\n  children: React.ReactNode;\n  style?: ViewStyle;\n  variant?: 'default' | 'elevated' | 'bordered';\n}\n\nexport default function Card({ children, style, variant = 'default' }: CardProps) {\n  return (\n    <View style={[styles.base, styles[variant], style]}>\n      {children}\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  base: {\n    borderRadius: 16,\n    padding: 20,\n  },\n  default: {\n    backgroundColor: colors.white,\n  },\n  elevated: {\n    backgroundColor: colors.white,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 8,\n    elevation: 3,\n  },\n  bordered: {\n    backgroundColor: colors.white,\n    borderWidth: 1,\n    borderColor: colors.lightGray,\n  },\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,UAAU,QAAmB,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAE3D,IAAMC,MAAM,IAAAC,aAAA,GAAAC,CAAA,OAAG;EACbC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE;AACb,CAAC;AAQD,eAAe,SAASC,IAAIA,CAAAC,IAAA,EAAsD;EAAA,IAAnDC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAEC,KAAK,GAAAF,IAAA,CAALE,KAAK;IAAAC,YAAA,GAAAH,IAAA,CAAEI,OAAO;IAAPA,OAAO,GAAAD,YAAA,eAAAR,aAAA,GAAAU,CAAA,UAAG,SAAS,IAAAF,YAAA;EAAAR,aAAA,GAAAW,CAAA;EAAAX,aAAA,GAAAC,CAAA;EACjE,OACEH,IAAA,CAACH,IAAI;IAACY,KAAK,EAAE,CAACK,MAAM,CAACC,IAAI,EAAED,MAAM,CAACH,OAAO,CAAC,EAAEF,KAAK,CAAE;IAAAD,QAAA,EAChDA;EAAQ,CACL,CAAC;AAEX;AAEA,IAAMM,MAAM,IAAAZ,aAAA,GAAAC,CAAA,OAAGL,UAAU,CAACkB,MAAM,CAAC;EAC/BD,IAAI,EAAE;IACJE,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE;EACX,CAAC;EACDC,OAAO,EAAE;IACPC,eAAe,EAAEnB,MAAM,CAACG;EAC1B,CAAC;EACDiB,QAAQ,EAAE;IACRD,eAAe,EAAEnB,MAAM,CAACG,KAAK;IAC7BkB,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDC,QAAQ,EAAE;IACRT,eAAe,EAAEnB,MAAM,CAACG,KAAK;IAC7B0B,WAAW,EAAE,CAAC;IACdC,WAAW,EAAE9B,MAAM,CAACI;EACtB;AACF,CAAC,CAAC", "ignoreList": []}