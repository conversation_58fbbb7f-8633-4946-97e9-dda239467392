{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_invariant", "Share", "key", "value", "share", "content", "options", "url", "message", "title", "window", "navigator", "undefined", "text", "Promise", "reject", "Error", "get", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nclass Share {\n  static share(content, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    (0, _invariant.default)(typeof content === 'object' && content !== null, 'Content to share must be a valid object');\n    (0, _invariant.default)(typeof content.url === 'string' || typeof content.message === 'string', 'At least one of URL and message is required');\n    (0, _invariant.default)(typeof options === 'object' && options !== null, 'Options must be a valid object');\n    (0, _invariant.default)(!content.title || typeof content.title === 'string', 'Invalid title: title should be a string.');\n    if (window.navigator.share !== undefined) {\n      return window.navigator.share({\n        title: content.title,\n        text: content.message,\n        url: content.url\n      });\n    } else {\n      return Promise.reject(new Error('Share is not supported in this browser'));\n    }\n  }\n\n  /**\n   * The content was successfully shared.\n   */\n  static get sharedAction() {\n    return 'sharedAction';\n  }\n\n  /**\n   * The dialog has been dismissed.\n   * @platform ios\n   */\n  static get dismissedAction() {\n    return 'dismissedAction';\n  }\n}\nvar _default = exports.default = Share;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,UAAU,GAAGJ,sBAAsB,CAACH,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAAC,IAWjEQ,KAAK;EAAA,SAAAA,MAAA;IAAA,IAAAP,gBAAA,CAAAG,OAAA,QAAAI,KAAA;EAAA;EAAA,WAAAN,aAAA,CAAAE,OAAA,EAAAI,KAAA;IAAAC,GAAA;IAAAC,KAAA,EACT,SAAOC,KAAKA,CAACC,OAAO,EAAEC,OAAO,EAAE;MAC7B,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;QACtBA,OAAO,GAAG,CAAC,CAAC;MACd;MACA,CAAC,CAAC,EAAEN,UAAU,CAACH,OAAO,EAAE,OAAOQ,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE,yCAAyC,CAAC;MACnH,CAAC,CAAC,EAAEL,UAAU,CAACH,OAAO,EAAE,OAAOQ,OAAO,CAACE,GAAG,KAAK,QAAQ,IAAI,OAAOF,OAAO,CAACG,OAAO,KAAK,QAAQ,EAAE,6CAA6C,CAAC;MAC9I,CAAC,CAAC,EAAER,UAAU,CAACH,OAAO,EAAE,OAAOS,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE,gCAAgC,CAAC;MAC1G,CAAC,CAAC,EAAEN,UAAU,CAACH,OAAO,EAAE,CAACQ,OAAO,CAACI,KAAK,IAAI,OAAOJ,OAAO,CAACI,KAAK,KAAK,QAAQ,EAAE,0CAA0C,CAAC;MACxH,IAAIC,MAAM,CAACC,SAAS,CAACP,KAAK,KAAKQ,SAAS,EAAE;QACxC,OAAOF,MAAM,CAACC,SAAS,CAACP,KAAK,CAAC;UAC5BK,KAAK,EAAEJ,OAAO,CAACI,KAAK;UACpBI,IAAI,EAAER,OAAO,CAACG,OAAO;UACrBD,GAAG,EAAEF,OAAO,CAACE;QACf,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOO,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAAC,wCAAwC,CAAC,CAAC;MAC5E;IACF;EAAC;IAAAd,GAAA;IAAAe,GAAA,EAKD,SAAAA,IAAA,EAA0B;MACxB,OAAO,cAAc;IACvB;EAAC;IAAAf,GAAA;IAAAe,GAAA,EAMD,SAAAA,IAAA,EAA6B;MAC3B,OAAO,iBAAiB;IAC1B;EAAC;AAAA;AAEH,IAAIC,QAAQ,GAAGpB,OAAO,CAACD,OAAO,GAAGI,KAAK;AACtCkB,MAAM,CAACrB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}