d5416c2ec2f01e225f71fa21b106ae73
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1i07i0i58r() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\offline\\OfflineManager.ts";
  var hash = "f61d070ee3db9f0c60606a8cee10462eda93470d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\offline\\OfflineManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 56,
          column: 21
        },
        end: {
          line: 56,
          column: 25
        }
      },
      "1": {
        start: {
          line: 57,
          column: 27
        },
        end: {
          line: 57,
          column: 32
        }
      },
      "2": {
        start: {
          line: 58,
          column: 47
        },
        end: {
          line: 58,
          column: 49
        }
      },
      "3": {
        start: {
          line: 59,
          column: 60
        },
        end: {
          line: 59,
          column: 62
        }
      },
      "4": {
        start: {
          line: 60,
          column: 91
        },
        end: {
          line: 60,
          column: 100
        }
      },
      "5": {
        start: {
          line: 62,
          column: 34
        },
        end: {
          line: 69,
          column: 3
        }
      },
      "6": {
        start: {
          line: 71,
          column: 34
        },
        end: {
          line: 75,
          column: 3
        }
      },
      "7": {
        start: {
          line: 78,
          column: 4
        },
        end: {
          line: 78,
          column: 36
        }
      },
      "8": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 105,
          column: 5
        }
      },
      "9": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 87,
          column: 43
        }
      },
      "10": {
        start: {
          line: 90,
          column: 6
        },
        end: {
          line: 90,
          column: 36
        }
      },
      "11": {
        start: {
          line: 93,
          column: 6
        },
        end: {
          line: 95,
          column: 7
        }
      },
      "12": {
        start: {
          line: 94,
          column: 8
        },
        end: {
          line: 94,
          column: 29
        }
      },
      "13": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 100,
          column: 7
        }
      },
      "14": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 35
        }
      },
      "15": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 62
        }
      },
      "16": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 68
        }
      },
      "17": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 15
        }
      },
      "18": {
        start: {
          line: 127,
          column: 40
        },
        end: {
          line: 137,
          column: 5
        }
      },
      "19": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 140,
          column: 40
        }
      },
      "20": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 143,
          column: 30
        }
      },
      "21": {
        start: {
          line: 146,
          column: 4
        },
        end: {
          line: 146,
          column: 35
        }
      },
      "22": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 151,
          column: 5
        }
      },
      "23": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 28
        }
      },
      "24": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 153,
          column: 72
        }
      },
      "25": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 24
        }
      },
      "26": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 164,
          column: 5
        }
      },
      "27": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 46
        }
      },
      "28": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 42
        }
      },
      "29": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 169,
          column: 5
        }
      },
      "30": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 167,
          column: 47
        }
      },
      "31": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 42
        }
      },
      "32": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 171,
          column: 31
        }
      },
      "33": {
        start: {
          line: 172,
          column: 22
        },
        end: {
          line: 172,
          column: 32
        }
      },
      "34": {
        start: {
          line: 173,
          column: 31
        },
        end: {
          line: 179,
          column: 5
        }
      },
      "35": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 242,
          column: 5
        }
      },
      "36": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 79
        }
      },
      "37": {
        start: {
          line: 185,
          column: 36
        },
        end: {
          line: 185,
          column: 68
        }
      },
      "38": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 213,
          column: 7
        }
      },
      "39": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 188,
          column: 84
        }
      },
      "40": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 212,
          column: 9
        }
      },
      "41": {
        start: {
          line: 191,
          column: 10
        },
        end: {
          line: 211,
          column: 11
        }
      },
      "42": {
        start: {
          line: 192,
          column: 31
        },
        end: {
          line: 192,
          column: 72
        }
      },
      "43": {
        start: {
          line: 194,
          column: 12
        },
        end: {
          line: 207,
          column: 13
        }
      },
      "44": {
        start: {
          line: 196,
          column: 14
        },
        end: {
          line: 196,
          column: 58
        }
      },
      "45": {
        start: {
          line: 197,
          column: 14
        },
        end: {
          line: 197,
          column: 48
        }
      },
      "46": {
        start: {
          line: 198,
          column: 19
        },
        end: {
          line: 207,
          column: 13
        }
      },
      "47": {
        start: {
          line: 199,
          column: 14
        },
        end: {
          line: 199,
          column: 57
        }
      },
      "48": {
        start: {
          line: 202,
          column: 14
        },
        end: {
          line: 202,
          column: 37
        }
      },
      "49": {
        start: {
          line: 203,
          column: 14
        },
        end: {
          line: 206,
          column: 15
        }
      },
      "50": {
        start: {
          line: 204,
          column: 16
        },
        end: {
          line: 204,
          column: 89
        }
      },
      "51": {
        start: {
          line: 205,
          column: 16
        },
        end: {
          line: 205,
          column: 60
        }
      },
      "52": {
        start: {
          line: 209,
          column: 12
        },
        end: {
          line: 209,
          column: 85
        }
      },
      "53": {
        start: {
          line: 210,
          column: 12
        },
        end: {
          line: 210,
          column: 35
        }
      },
      "54": {
        start: {
          line: 216,
          column: 6
        },
        end: {
          line: 216,
          column: 37
        }
      },
      "55": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 85
        }
      },
      "56": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 221,
          column: 47
        }
      },
      "57": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 50
        }
      },
      "58": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 228,
          column: 9
        }
      },
      "59": {
        start: {
          line: 231,
          column: 6
        },
        end: {
          line: 231,
          column: 77
        }
      },
      "60": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 29
        }
      },
      "61": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 235,
          column: 50
        }
      },
      "62": {
        start: {
          line: 236,
          column: 6
        },
        end: {
          line: 236,
          column: 53
        }
      },
      "63": {
        start: {
          line: 238,
          column: 6
        },
        end: {
          line: 238,
          column: 34
        }
      },
      "64": {
        start: {
          line: 241,
          column: 6
        },
        end: {
          line: 241,
          column: 39
        }
      },
      "65": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 244,
          column: 18
        }
      },
      "66": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 263,
          column: 15
        }
      },
      "67": {
        start: {
          line: 265,
          column: 21
        },
        end: {
          line: 265,
          column: 69
        }
      },
      "68": {
        start: {
          line: 267,
          column: 4
        },
        end: {
          line: 322,
          column: 5
        }
      },
      "69": {
        start: {
          line: 269,
          column: 6
        },
        end: {
          line: 275,
          column: 7
        }
      },
      "70": {
        start: {
          line: 270,
          column: 27
        },
        end: {
          line: 270,
          column: 70
        }
      },
      "71": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 274,
          column: 9
        }
      },
      "72": {
        start: {
          line: 272,
          column: 10
        },
        end: {
          line: 272,
          column: 48
        }
      },
      "73": {
        start: {
          line: 273,
          column: 10
        },
        end: {
          line: 273,
          column: 28
        }
      },
      "74": {
        start: {
          line: 278,
          column: 6
        },
        end: {
          line: 296,
          column: 7
        }
      },
      "75": {
        start: {
          line: 279,
          column: 32
        },
        end: {
          line: 282,
          column: 23
        }
      },
      "76": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 284,
          column: 31
        }
      },
      "77": {
        start: {
          line: 284,
          column: 19
        },
        end: {
          line: 284,
          column: 31
        }
      },
      "78": {
        start: {
          line: 287,
          column: 8
        },
        end: {
          line: 293,
          column: 9
        }
      },
      "79": {
        start: {
          line: 288,
          column: 10
        },
        end: {
          line: 292,
          column: 13
        }
      },
      "80": {
        start: {
          line: 295,
          column: 8
        },
        end: {
          line: 295,
          column: 25
        }
      },
      "81": {
        start: {
          line: 299,
          column: 6
        },
        end: {
          line: 305,
          column: 7
        }
      },
      "82": {
        start: {
          line: 300,
          column: 27
        },
        end: {
          line: 300,
          column: 70
        }
      },
      "83": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 304,
          column: 9
        }
      },
      "84": {
        start: {
          line: 302,
          column: 10
        },
        end: {
          line: 302,
          column: 64
        }
      },
      "85": {
        start: {
          line: 303,
          column: 10
        },
        end: {
          line: 303,
          column: 28
        }
      },
      "86": {
        start: {
          line: 307,
          column: 6
        },
        end: {
          line: 307,
          column: 70
        }
      },
      "87": {
        start: {
          line: 310,
          column: 6
        },
        end: {
          line: 310,
          column: 63
        }
      },
      "88": {
        start: {
          line: 313,
          column: 6
        },
        end: {
          line: 319,
          column: 7
        }
      },
      "89": {
        start: {
          line: 314,
          column: 27
        },
        end: {
          line: 314,
          column: 70
        }
      },
      "90": {
        start: {
          line: 315,
          column: 8
        },
        end: {
          line: 318,
          column: 9
        }
      },
      "91": {
        start: {
          line: 316,
          column: 10
        },
        end: {
          line: 316,
          column: 62
        }
      },
      "92": {
        start: {
          line: 317,
          column: 10
        },
        end: {
          line: 317,
          column: 28
        }
      },
      "93": {
        start: {
          line: 321,
          column: 6
        },
        end: {
          line: 321,
          column: 18
        }
      },
      "94": {
        start: {
          line: 329,
          column: 4
        },
        end: {
          line: 329,
          column: 25
        }
      },
      "95": {
        start: {
          line: 336,
          column: 4
        },
        end: {
          line: 336,
          column: 38
        }
      },
      "96": {
        start: {
          line: 348,
          column: 4
        },
        end: {
          line: 353,
          column: 6
        }
      },
      "97": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 360,
          column: 38
        }
      },
      "98": {
        start: {
          line: 367,
          column: 18
        },
        end: {
          line: 367,
          column: 54
        }
      },
      "99": {
        start: {
          line: 368,
          column: 4
        },
        end: {
          line: 370,
          column: 5
        }
      },
      "100": {
        start: {
          line: 369,
          column: 6
        },
        end: {
          line: 369,
          column: 42
        }
      },
      "101": {
        start: {
          line: 380,
          column: 4
        },
        end: {
          line: 380,
          column: 48
        }
      },
      "102": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 387,
          column: 29
        }
      },
      "103": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 392,
          column: 7
        }
      },
      "104": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 393,
          column: 60
        }
      },
      "105": {
        start: {
          line: 394,
          column: 4
        },
        end: {
          line: 394,
          column: 40
        }
      },
      "106": {
        start: {
          line: 400,
          column: 4
        },
        end: {
          line: 411,
          column: 7
        }
      },
      "107": {
        start: {
          line: 401,
          column: 24
        },
        end: {
          line: 401,
          column: 37
        }
      },
      "108": {
        start: {
          line: 402,
          column: 6
        },
        end: {
          line: 402,
          column: 49
        }
      },
      "109": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 404,
          column: 85
        }
      },
      "110": {
        start: {
          line: 407,
          column: 6
        },
        end: {
          line: 410,
          column: 7
        }
      },
      "111": {
        start: {
          line: 408,
          column: 8
        },
        end: {
          line: 408,
          column: 59
        }
      },
      "112": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 54
        }
      },
      "113": {
        start: {
          line: 409,
          column: 25
        },
        end: {
          line: 409,
          column: 46
        }
      },
      "114": {
        start: {
          line: 414,
          column: 18
        },
        end: {
          line: 414,
          column: 39
        }
      },
      "115": {
        start: {
          line: 415,
          column: 4
        },
        end: {
          line: 415,
          column: 47
        }
      },
      "116": {
        start: {
          line: 419,
          column: 4
        },
        end: {
          line: 423,
          column: 33
        }
      },
      "117": {
        start: {
          line: 420,
          column: 6
        },
        end: {
          line: 422,
          column: 7
        }
      },
      "118": {
        start: {
          line: 421,
          column: 8
        },
        end: {
          line: 421,
          column: 30
        }
      },
      "119": {
        start: {
          line: 429,
          column: 4
        },
        end: {
          line: 429,
          column: 84
        }
      },
      "120": {
        start: {
          line: 433,
          column: 4
        },
        end: {
          line: 441,
          column: 5
        }
      },
      "121": {
        start: {
          line: 434,
          column: 21
        },
        end: {
          line: 434,
          column: 77
        }
      },
      "122": {
        start: {
          line: 435,
          column: 6
        },
        end: {
          line: 438,
          column: 7
        }
      },
      "123": {
        start: {
          line: 436,
          column: 8
        },
        end: {
          line: 436,
          column: 49
        }
      },
      "124": {
        start: {
          line: 437,
          column: 8
        },
        end: {
          line: 437,
          column: 81
        }
      },
      "125": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 440,
          column: 67
        }
      },
      "126": {
        start: {
          line: 445,
          column: 4
        },
        end: {
          line: 452,
          column: 5
        }
      },
      "127": {
        start: {
          line: 446,
          column: 6
        },
        end: {
          line: 449,
          column: 8
        }
      },
      "128": {
        start: {
          line: 451,
          column: 6
        },
        end: {
          line: 451,
          column: 60
        }
      },
      "129": {
        start: {
          line: 456,
          column: 4
        },
        end: {
          line: 464,
          column: 7
        }
      },
      "130": {
        start: {
          line: 458,
          column: 28
        },
        end: {
          line: 458,
          column: 58
        }
      },
      "131": {
        start: {
          line: 459,
          column: 27
        },
        end: {
          line: 459,
          column: 80
        }
      },
      "132": {
        start: {
          line: 460,
          column: 6
        },
        end: {
          line: 460,
          column: 50
        }
      },
      "133": {
        start: {
          line: 460,
          column: 30
        },
        end: {
          line: 460,
          column: 50
        }
      },
      "134": {
        start: {
          line: 463,
          column: 6
        },
        end: {
          line: 463,
          column: 39
        }
      },
      "135": {
        start: {
          line: 468,
          column: 19
        },
        end: {
          line: 468,
          column: 56
        }
      },
      "136": {
        start: {
          line: 470,
          column: 4
        },
        end: {
          line: 475,
          column: 5
        }
      },
      "137": {
        start: {
          line: 471,
          column: 6
        },
        end: {
          line: 473,
          column: 7
        }
      },
      "138": {
        start: {
          line: 472,
          column: 8
        },
        end: {
          line: 472,
          column: 43
        }
      },
      "139": {
        start: {
          line: 474,
          column: 6
        },
        end: {
          line: 474,
          column: 54
        }
      },
      "140": {
        start: {
          line: 477,
          column: 4
        },
        end: {
          line: 477,
          column: 18
        }
      },
      "141": {
        start: {
          line: 484,
          column: 4
        },
        end: {
          line: 512,
          column: 5
        }
      },
      "142": {
        start: {
          line: 487,
          column: 6
        },
        end: {
          line: 497,
          column: 7
        }
      },
      "143": {
        start: {
          line: 489,
          column: 10
        },
        end: {
          line: 489,
          column: 79
        }
      },
      "144": {
        start: {
          line: 490,
          column: 10
        },
        end: {
          line: 490,
          column: 16
        }
      },
      "145": {
        start: {
          line: 492,
          column: 10
        },
        end: {
          line: 492,
          column: 107
        }
      },
      "146": {
        start: {
          line: 493,
          column: 10
        },
        end: {
          line: 493,
          column: 16
        }
      },
      "147": {
        start: {
          line: 495,
          column: 10
        },
        end: {
          line: 495,
          column: 93
        }
      },
      "148": {
        start: {
          line: 496,
          column: 10
        },
        end: {
          line: 496,
          column: 16
        }
      },
      "149": {
        start: {
          line: 499,
          column: 6
        },
        end: {
          line: 506,
          column: 7
        }
      },
      "150": {
        start: {
          line: 501,
          column: 8
        },
        end: {
          line: 504,
          column: 9
        }
      },
      "151": {
        start: {
          line: 502,
          column: 27
        },
        end: {
          line: 502,
          column: 77
        }
      },
      "152": {
        start: {
          line: 503,
          column: 10
        },
        end: {
          line: 503,
          column: 46
        }
      },
      "153": {
        start: {
          line: 505,
          column: 8
        },
        end: {
          line: 505,
          column: 27
        }
      },
      "154": {
        start: {
          line: 508,
          column: 6
        },
        end: {
          line: 508,
          column: 31
        }
      },
      "155": {
        start: {
          line: 510,
          column: 6
        },
        end: {
          line: 510,
          column: 72
        }
      },
      "156": {
        start: {
          line: 511,
          column: 6
        },
        end: {
          line: 511,
          column: 32
        }
      },
      "157": {
        start: {
          line: 519,
          column: 41
        },
        end: {
          line: 525,
          column: 5
        }
      },
      "158": {
        start: {
          line: 528,
          column: 21
        },
        end: {
          line: 528,
          column: 64
        }
      },
      "159": {
        start: {
          line: 529,
          column: 4
        },
        end: {
          line: 536,
          column: 5
        }
      },
      "160": {
        start: {
          line: 530,
          column: 6
        },
        end: {
          line: 535,
          column: 7
        }
      },
      "161": {
        start: {
          line: 531,
          column: 8
        },
        end: {
          line: 531,
          column: 57
        }
      },
      "162": {
        start: {
          line: 532,
          column: 8
        },
        end: {
          line: 532,
          column: 39
        }
      },
      "163": {
        start: {
          line: 534,
          column: 8
        },
        end: {
          line: 534,
          column: 66
        }
      },
      "164": {
        start: {
          line: 539,
          column: 4
        },
        end: {
          line: 551,
          column: 5
        }
      },
      "165": {
        start: {
          line: 540,
          column: 6
        },
        end: {
          line: 550,
          column: 7
        }
      },
      "166": {
        start: {
          line: 542,
          column: 10
        },
        end: {
          line: 542,
          column: 53
        }
      },
      "167": {
        start: {
          line: 543,
          column: 10
        },
        end: {
          line: 543,
          column: 16
        }
      },
      "168": {
        start: {
          line: 545,
          column: 10
        },
        end: {
          line: 545,
          column: 54
        }
      },
      "169": {
        start: {
          line: 546,
          column: 10
        },
        end: {
          line: 546,
          column: 16
        }
      },
      "170": {
        start: {
          line: 548,
          column: 10
        },
        end: {
          line: 548,
          column: 84
        }
      },
      "171": {
        start: {
          line: 549,
          column: 10
        },
        end: {
          line: 549,
          column: 16
        }
      },
      "172": {
        start: {
          line: 553,
          column: 4
        },
        end: {
          line: 553,
          column: 20
        }
      },
      "173": {
        start: {
          line: 557,
          column: 4
        },
        end: {
          line: 557,
          column: 82
        }
      },
      "174": {
        start: {
          line: 557,
          column: 59
        },
        end: {
          line: 557,
          column: 80
        }
      },
      "175": {
        start: {
          line: 561,
          column: 4
        },
        end: {
          line: 561,
          column: 73
        }
      },
      "176": {
        start: {
          line: 565,
          column: 4
        },
        end: {
          line: 571,
          column: 6
        }
      },
      "177": {
        start: {
          line: 575,
          column: 4
        },
        end: {
          line: 581,
          column: 7
        }
      },
      "178": {
        start: {
          line: 576,
          column: 6
        },
        end: {
          line: 580,
          column: 7
        }
      },
      "179": {
        start: {
          line: 577,
          column: 8
        },
        end: {
          line: 577,
          column: 25
        }
      },
      "180": {
        start: {
          line: 579,
          column: 8
        },
        end: {
          line: 579,
          column: 53
        }
      },
      "181": {
        start: {
          line: 586,
          column: 30
        },
        end: {
          line: 586,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 77,
            column: 2
          },
          end: {
            line: 77,
            column: 3
          }
        },
        loc: {
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 79,
            column: 3
          }
        },
        line: 77
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 84,
            column: 2
          },
          end: {
            line: 84,
            column: 3
          }
        },
        loc: {
          start: {
            line: 84,
            column: 58
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 84
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 111,
            column: 2
          },
          end: {
            line: 111,
            column: 3
          }
        },
        loc: {
          start: {
            line: 120,
            column: 21
          },
          end: {
            line: 155,
            column: 3
          }
        },
        line: 120
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 160,
            column: 2
          },
          end: {
            line: 160,
            column: 3
          }
        },
        loc: {
          start: {
            line: 160,
            column: 46
          },
          end: {
            line: 245,
            column: 3
          }
        },
        line: 160
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 250,
            column: 2
          },
          end: {
            line: 250,
            column: 3
          }
        },
        loc: {
          start: {
            line: 258,
            column: 23
          },
          end: {
            line: 323,
            column: 3
          }
        },
        line: 258
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 328,
            column: 2
          },
          end: {
            line: 328,
            column: 3
          }
        },
        loc: {
          start: {
            line: 328,
            column: 28
          },
          end: {
            line: 330,
            column: 3
          }
        },
        line: 328
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 335,
            column: 2
          },
          end: {
            line: 335,
            column: 3
          }
        },
        loc: {
          start: {
            line: 335,
            column: 38
          },
          end: {
            line: 337,
            column: 3
          }
        },
        line: 335
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 342,
            column: 2
          },
          end: {
            line: 342,
            column: 3
          }
        },
        loc: {
          start: {
            line: 347,
            column: 4
          },
          end: {
            line: 354,
            column: 3
          }
        },
        line: 347
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 359,
            column: 2
          },
          end: {
            line: 359,
            column: 3
          }
        },
        loc: {
          start: {
            line: 359,
            column: 64
          },
          end: {
            line: 361,
            column: 3
          }
        },
        line: 359
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 366,
            column: 2
          },
          end: {
            line: 366,
            column: 3
          }
        },
        loc: {
          start: {
            line: 366,
            column: 67
          },
          end: {
            line: 371,
            column: 3
          }
        },
        line: 366
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 376,
            column: 2
          },
          end: {
            line: 376,
            column: 3
          }
        },
        loc: {
          start: {
            line: 379,
            column: 10
          },
          end: {
            line: 381,
            column: 3
          }
        },
        line: 379
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 386,
            column: 2
          },
          end: {
            line: 386,
            column: 3
          }
        },
        loc: {
          start: {
            line: 386,
            column: 42
          },
          end: {
            line: 395,
            column: 3
          }
        },
        line: 386
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 399,
            column: 2
          },
          end: {
            line: 399,
            column: 3
          }
        },
        loc: {
          start: {
            line: 399,
            column: 56
          },
          end: {
            line: 416,
            column: 3
          }
        },
        line: 399
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 400,
            column: 29
          },
          end: {
            line: 400,
            column: 30
          }
        },
        loc: {
          start: {
            line: 400,
            column: 54
          },
          end: {
            line: 411,
            column: 5
          }
        },
        line: 400
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 409,
            column: 19
          },
          end: {
            line: 409,
            column: 20
          }
        },
        loc: {
          start: {
            line: 409,
            column: 25
          },
          end: {
            line: 409,
            column: 46
          }
        },
        line: 409
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 418,
            column: 2
          },
          end: {
            line: 418,
            column: 3
          }
        },
        loc: {
          start: {
            line: 418,
            column: 32
          },
          end: {
            line: 424,
            column: 3
          }
        },
        line: 418
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 419,
            column: 16
          },
          end: {
            line: 419,
            column: 17
          }
        },
        loc: {
          start: {
            line: 419,
            column: 22
          },
          end: {
            line: 423,
            column: 5
          }
        },
        line: 419
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 426,
            column: 2
          },
          end: {
            line: 426,
            column: 3
          }
        },
        loc: {
          start: {
            line: 426,
            column: 38
          },
          end: {
            line: 430,
            column: 3
          }
        },
        line: 426
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 432,
            column: 2
          },
          end: {
            line: 432,
            column: 3
          }
        },
        loc: {
          start: {
            line: 432,
            column: 57
          },
          end: {
            line: 442,
            column: 3
          }
        },
        line: 432
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 444,
            column: 2
          },
          end: {
            line: 444,
            column: 3
          }
        },
        loc: {
          start: {
            line: 444,
            column: 51
          },
          end: {
            line: 453,
            column: 3
          }
        },
        line: 444
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 455,
            column: 2
          },
          end: {
            line: 455,
            column: 3
          }
        },
        loc: {
          start: {
            line: 455,
            column: 37
          },
          end: {
            line: 465,
            column: 3
          }
        },
        line: 455
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 456,
            column: 29
          },
          end: {
            line: 456,
            column: 30
          }
        },
        loc: {
          start: {
            line: 456,
            column: 39
          },
          end: {
            line: 464,
            column: 5
          }
        },
        line: 456
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 467,
            column: 2
          },
          end: {
            line: 467,
            column: 3
          }
        },
        loc: {
          start: {
            line: 467,
            column: 71
          },
          end: {
            line: 478,
            column: 3
          }
        },
        line: 467
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 480,
            column: 2
          },
          end: {
            line: 480,
            column: 3
          }
        },
        loc: {
          start: {
            line: 483,
            column: 5
          },
          end: {
            line: 513,
            column: 3
          }
        },
        line: 483
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 515,
            column: 2
          },
          end: {
            line: 515,
            column: 3
          }
        },
        loc: {
          start: {
            line: 518,
            column: 33
          },
          end: {
            line: 554,
            column: 3
          }
        },
        line: 518
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 556,
            column: 2
          },
          end: {
            line: 556,
            column: 3
          }
        },
        loc: {
          start: {
            line: 556,
            column: 62
          },
          end: {
            line: 558,
            column: 3
          }
        },
        line: 556
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 557,
            column: 53
          },
          end: {
            line: 557,
            column: 54
          }
        },
        loc: {
          start: {
            line: 557,
            column: 59
          },
          end: {
            line: 557,
            column: 80
          }
        },
        line: 557
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 560,
            column: 2
          },
          end: {
            line: 560,
            column: 3
          }
        },
        loc: {
          start: {
            line: 560,
            column: 40
          },
          end: {
            line: 562,
            column: 3
          }
        },
        line: 560
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 564,
            column: 2
          },
          end: {
            line: 564,
            column: 3
          }
        },
        loc: {
          start: {
            line: 564,
            column: 46
          },
          end: {
            line: 572,
            column: 3
          }
        },
        line: 564
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 574,
            column: 2
          },
          end: {
            line: 574,
            column: 3
          }
        },
        loc: {
          start: {
            line: 574,
            column: 56
          },
          end: {
            line: 582,
            column: 3
          }
        },
        line: 574
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 575,
            column: 31
          },
          end: {
            line: 575,
            column: 32
          }
        },
        loc: {
          start: {
            line: 575,
            column: 43
          },
          end: {
            line: 581,
            column: 5
          }
        },
        line: 575
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 93,
            column: 6
          },
          end: {
            line: 95,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 6
          },
          end: {
            line: 95,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "1": {
        loc: {
          start: {
            line: 98,
            column: 6
          },
          end: {
            line: 100,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 6
          },
          end: {
            line: 100,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "2": {
        loc: {
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 119,
            column: 10
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 119,
            column: 8
          },
          end: {
            line: 119,
            column: 10
          }
        }],
        line: 115
      },
      "3": {
        loc: {
          start: {
            line: 122,
            column: 6
          },
          end: {
            line: 122,
            column: 25
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 122,
            column: 17
          },
          end: {
            line: 122,
            column: 25
          }
        }],
        line: 122
      },
      "4": {
        loc: {
          start: {
            line: 123,
            column: 6
          },
          end: {
            line: 123,
            column: 41
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 123,
            column: 19
          },
          end: {
            line: 123,
            column: 41
          }
        }],
        line: 123
      },
      "5": {
        loc: {
          start: {
            line: 124,
            column: 6
          },
          end: {
            line: 124,
            column: 23
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 124,
            column: 21
          },
          end: {
            line: 124,
            column: 23
          }
        }],
        line: 124
      },
      "6": {
        loc: {
          start: {
            line: 149,
            column: 4
          },
          end: {
            line: 151,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 4
          },
          end: {
            line: 151,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "7": {
        loc: {
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 149,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 149,
            column: 21
          }
        }, {
          start: {
            line: 149,
            column: 25
          },
          end: {
            line: 149,
            column: 45
          }
        }],
        line: 149
      },
      "8": {
        loc: {
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "9": {
        loc: {
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 169,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 169,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "10": {
        loc: {
          start: {
            line: 194,
            column: 12
          },
          end: {
            line: 207,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 12
          },
          end: {
            line: 207,
            column: 13
          }
        }, {
          start: {
            line: 198,
            column: 19
          },
          end: {
            line: 207,
            column: 13
          }
        }],
        line: 194
      },
      "11": {
        loc: {
          start: {
            line: 198,
            column: 19
          },
          end: {
            line: 207,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 19
          },
          end: {
            line: 207,
            column: 13
          }
        }, {
          start: {
            line: 200,
            column: 19
          },
          end: {
            line: 207,
            column: 13
          }
        }],
        line: 198
      },
      "12": {
        loc: {
          start: {
            line: 203,
            column: 14
          },
          end: {
            line: 206,
            column: 15
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 203,
            column: 14
          },
          end: {
            line: 206,
            column: 15
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 203
      },
      "13": {
        loc: {
          start: {
            line: 252,
            column: 4
          },
          end: {
            line: 252,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 252,
            column: 17
          },
          end: {
            line: 252,
            column: 19
          }
        }],
        line: 252
      },
      "14": {
        loc: {
          start: {
            line: 253,
            column: 4
          },
          end: {
            line: 257,
            column: 10
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 257,
            column: 8
          },
          end: {
            line: 257,
            column: 10
          }
        }],
        line: 253
      },
      "15": {
        loc: {
          start: {
            line: 260,
            column: 6
          },
          end: {
            line: 260,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 260,
            column: 17
          },
          end: {
            line: 260,
            column: 21
          }
        }],
        line: 260
      },
      "16": {
        loc: {
          start: {
            line: 261,
            column: 6
          },
          end: {
            line: 261,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 261,
            column: 24
          },
          end: {
            line: 261,
            column: 28
          }
        }],
        line: 261
      },
      "17": {
        loc: {
          start: {
            line: 262,
            column: 6
          },
          end: {
            line: 262,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 262,
            column: 21
          },
          end: {
            line: 262,
            column: 27
          }
        }],
        line: 262
      },
      "18": {
        loc: {
          start: {
            line: 269,
            column: 6
          },
          end: {
            line: 275,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 269,
            column: 6
          },
          end: {
            line: 275,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 269
      },
      "19": {
        loc: {
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 274,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 274,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "20": {
        loc: {
          start: {
            line: 278,
            column: 6
          },
          end: {
            line: 296,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 278,
            column: 6
          },
          end: {
            line: 296,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 278
      },
      "21": {
        loc: {
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 284,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 284,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "22": {
        loc: {
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 293,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 8
          },
          end: {
            line: 293,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 287
      },
      "23": {
        loc: {
          start: {
            line: 287,
            column: 12
          },
          end: {
            line: 287,
            column: 28
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 287,
            column: 12
          },
          end: {
            line: 287,
            column: 20
          }
        }, {
          start: {
            line: 287,
            column: 24
          },
          end: {
            line: 287,
            column: 28
          }
        }],
        line: 287
      },
      "24": {
        loc: {
          start: {
            line: 290,
            column: 22
          },
          end: {
            line: 290,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 290,
            column: 67
          },
          end: {
            line: 290,
            column: 73
          }
        }, {
          start: {
            line: 290,
            column: 76
          },
          end: {
            line: 290,
            column: 84
          }
        }],
        line: 290
      },
      "25": {
        loc: {
          start: {
            line: 299,
            column: 6
          },
          end: {
            line: 305,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 299,
            column: 6
          },
          end: {
            line: 305,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 299
      },
      "26": {
        loc: {
          start: {
            line: 301,
            column: 8
          },
          end: {
            line: 304,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 301,
            column: 8
          },
          end: {
            line: 304,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 301
      },
      "27": {
        loc: {
          start: {
            line: 313,
            column: 6
          },
          end: {
            line: 319,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 313,
            column: 6
          },
          end: {
            line: 319,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 313
      },
      "28": {
        loc: {
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 318,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 8
          },
          end: {
            line: 318,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 315
      },
      "29": {
        loc: {
          start: {
            line: 368,
            column: 4
          },
          end: {
            line: 370,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 368,
            column: 4
          },
          end: {
            line: 370,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 368
      },
      "30": {
        loc: {
          start: {
            line: 402,
            column: 22
          },
          end: {
            line: 402,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 402,
            column: 22
          },
          end: {
            line: 402,
            column: 39
          }
        }, {
          start: {
            line: 402,
            column: 43
          },
          end: {
            line: 402,
            column: 48
          }
        }],
        line: 402
      },
      "31": {
        loc: {
          start: {
            line: 404,
            column: 45
          },
          end: {
            line: 404,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 404,
            column: 61
          },
          end: {
            line: 404,
            column: 69
          }
        }, {
          start: {
            line: 404,
            column: 72
          },
          end: {
            line: 404,
            column: 81
          }
        }],
        line: 404
      },
      "32": {
        loc: {
          start: {
            line: 407,
            column: 6
          },
          end: {
            line: 410,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 407,
            column: 6
          },
          end: {
            line: 410,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 407
      },
      "33": {
        loc: {
          start: {
            line: 407,
            column: 10
          },
          end: {
            line: 407,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 407,
            column: 10
          },
          end: {
            line: 407,
            column: 20
          }
        }, {
          start: {
            line: 407,
            column: 24
          },
          end: {
            line: 407,
            column: 37
          }
        }, {
          start: {
            line: 407,
            column: 41
          },
          end: {
            line: 407,
            column: 71
          }
        }],
        line: 407
      },
      "34": {
        loc: {
          start: {
            line: 415,
            column: 20
          },
          end: {
            line: 415,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 415,
            column: 20
          },
          end: {
            line: 415,
            column: 37
          }
        }, {
          start: {
            line: 415,
            column: 41
          },
          end: {
            line: 415,
            column: 46
          }
        }],
        line: 415
      },
      "35": {
        loc: {
          start: {
            line: 420,
            column: 6
          },
          end: {
            line: 422,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 420,
            column: 6
          },
          end: {
            line: 422,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 420
      },
      "36": {
        loc: {
          start: {
            line: 420,
            column: 10
          },
          end: {
            line: 420,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 420,
            column: 10
          },
          end: {
            line: 420,
            column: 23
          }
        }, {
          start: {
            line: 420,
            column: 27
          },
          end: {
            line: 420,
            column: 47
          }
        }, {
          start: {
            line: 420,
            column: 51
          },
          end: {
            line: 420,
            column: 81
          }
        }],
        line: 420
      },
      "37": {
        loc: {
          start: {
            line: 435,
            column: 6
          },
          end: {
            line: 438,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 435,
            column: 6
          },
          end: {
            line: 438,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 435
      },
      "38": {
        loc: {
          start: {
            line: 460,
            column: 6
          },
          end: {
            line: 460,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 460,
            column: 6
          },
          end: {
            line: 460,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 460
      },
      "39": {
        loc: {
          start: {
            line: 471,
            column: 6
          },
          end: {
            line: 473,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 471,
            column: 6
          },
          end: {
            line: 473,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 471
      },
      "40": {
        loc: {
          start: {
            line: 487,
            column: 6
          },
          end: {
            line: 497,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 488,
            column: 8
          },
          end: {
            line: 490,
            column: 16
          }
        }, {
          start: {
            line: 491,
            column: 8
          },
          end: {
            line: 493,
            column: 16
          }
        }, {
          start: {
            line: 494,
            column: 8
          },
          end: {
            line: 496,
            column: 16
          }
        }],
        line: 487
      },
      "41": {
        loc: {
          start: {
            line: 499,
            column: 6
          },
          end: {
            line: 506,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 499,
            column: 6
          },
          end: {
            line: 506,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 499
      },
      "42": {
        loc: {
          start: {
            line: 501,
            column: 8
          },
          end: {
            line: 504,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 501,
            column: 8
          },
          end: {
            line: 504,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 501
      },
      "43": {
        loc: {
          start: {
            line: 529,
            column: 4
          },
          end: {
            line: 536,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 529,
            column: 4
          },
          end: {
            line: 536,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 529
      },
      "44": {
        loc: {
          start: {
            line: 539,
            column: 4
          },
          end: {
            line: 551,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 539,
            column: 4
          },
          end: {
            line: 551,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 539
      },
      "45": {
        loc: {
          start: {
            line: 540,
            column: 6
          },
          end: {
            line: 550,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 541,
            column: 8
          },
          end: {
            line: 543,
            column: 16
          }
        }, {
          start: {
            line: 544,
            column: 8
          },
          end: {
            line: 546,
            column: 16
          }
        }, {
          start: {
            line: 547,
            column: 8
          },
          end: {
            line: 549,
            column: 16
          }
        }],
        line: 540
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0],
      "14": [0],
      "15": [0],
      "16": [0],
      "17": [0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f61d070ee3db9f0c60606a8cee10462eda93470d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1i07i0i58r = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1i07i0i58r();
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-netinfo/netinfo';
import { supabase } from "../../lib/supabase";
import { advancedCacheManager } from "../caching/AdvancedCacheManager";
import { performanceMonitor } from "../../utils/performance";
var OfflineManager = function () {
  function OfflineManager() {
    _classCallCheck(this, OfflineManager);
    this.isOnline = (cov_1i07i0i58r().s[0]++, true);
    this.syncInProgress = (cov_1i07i0i58r().s[1]++, false);
    this.operationQueue = (cov_1i07i0i58r().s[2]++, []);
    this.syncListeners = (cov_1i07i0i58r().s[3]++, []);
    this.conflictResolvers = (cov_1i07i0i58r().s[4]++, new Map());
    this.config = (cov_1i07i0i58r().s[5]++, {
      enableAutoSync: true,
      syncInterval: 30000,
      maxRetries: 3,
      conflictResolution: 'merge',
      priorityTables: ['user_profile', 'training_sessions', 'match_results'],
      backgroundSync: true
    });
    this.STORAGE_KEYS = (cov_1i07i0i58r().s[6]++, {
      OPERATIONS: 'offline_operations',
      LAST_SYNC: 'last_sync_timestamp',
      CONFLICT_LOG: 'conflict_resolution_log'
    });
    cov_1i07i0i58r().f[0]++;
    cov_1i07i0i58r().s[7]++;
    this.initializeOfflineManager();
  }
  return _createClass(OfflineManager, [{
    key: "initializeOfflineManager",
    value: (function () {
      var _initializeOfflineManager = _asyncToGenerator(function* () {
        cov_1i07i0i58r().f[1]++;
        cov_1i07i0i58r().s[8]++;
        try {
          cov_1i07i0i58r().s[9]++;
          yield this.loadPersistedOperations();
          cov_1i07i0i58r().s[10]++;
          this.setupNetworkMonitoring();
          cov_1i07i0i58r().s[11]++;
          if (this.config.enableAutoSync) {
            cov_1i07i0i58r().b[0][0]++;
            cov_1i07i0i58r().s[12]++;
            this.setupAutoSync();
          } else {
            cov_1i07i0i58r().b[0][1]++;
          }
          cov_1i07i0i58r().s[13]++;
          if (this.config.backgroundSync) {
            cov_1i07i0i58r().b[1][0]++;
            cov_1i07i0i58r().s[14]++;
            this.setupBackgroundSync();
          } else {
            cov_1i07i0i58r().b[1][1]++;
          }
          cov_1i07i0i58r().s[15]++;
          console.log('Offline Manager initialized successfully');
        } catch (error) {
          cov_1i07i0i58r().s[16]++;
          console.error('Failed to initialize Offline Manager:', error);
        }
      });
      function initializeOfflineManager() {
        return _initializeOfflineManager.apply(this, arguments);
      }
      return initializeOfflineManager;
    }())
  }, {
    key: "queueOperation",
    value: (function () {
      var _queueOperation = _asyncToGenerator(function* (type, table, data) {
        var options = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : (cov_1i07i0i58r().b[2][0]++, {});
        cov_1i07i0i58r().f[2]++;
        var _ref = (cov_1i07i0i58r().s[17]++, options),
          _ref$priority = _ref.priority,
          priority = _ref$priority === void 0 ? (cov_1i07i0i58r().b[3][0]++, 'medium') : _ref$priority,
          _ref$maxRetries = _ref.maxRetries,
          maxRetries = _ref$maxRetries === void 0 ? (cov_1i07i0i58r().b[4][0]++, this.config.maxRetries) : _ref$maxRetries,
          _ref$dependencies = _ref.dependencies,
          dependencies = _ref$dependencies === void 0 ? (cov_1i07i0i58r().b[5][0]++, []) : _ref$dependencies;
        var operation = (cov_1i07i0i58r().s[18]++, {
          id: this.generateOperationId(),
          type: type,
          table: table,
          data: data,
          timestamp: Date.now(),
          retryCount: 0,
          maxRetries: maxRetries,
          priority: priority,
          dependencies: dependencies
        });
        cov_1i07i0i58r().s[19]++;
        this.operationQueue.push(operation);
        cov_1i07i0i58r().s[20]++;
        this.sortOperationQueue();
        cov_1i07i0i58r().s[21]++;
        yield this.persistOperations();
        cov_1i07i0i58r().s[22]++;
        if ((cov_1i07i0i58r().b[7][0]++, this.isOnline) && (cov_1i07i0i58r().b[7][1]++, !this.syncInProgress)) {
          cov_1i07i0i58r().b[6][0]++;
          cov_1i07i0i58r().s[23]++;
          this.syncOperations();
        } else {
          cov_1i07i0i58r().b[6][1]++;
        }
        cov_1i07i0i58r().s[24]++;
        console.log(`Queued ${type} operation for ${table}:`, operation.id);
        cov_1i07i0i58r().s[25]++;
        return operation.id;
      });
      function queueOperation(_x, _x2, _x3) {
        return _queueOperation.apply(this, arguments);
      }
      return queueOperation;
    }())
  }, {
    key: "syncOperations",
    value: (function () {
      var _syncOperations = _asyncToGenerator(function* () {
        cov_1i07i0i58r().f[3]++;
        cov_1i07i0i58r().s[26]++;
        if (this.syncInProgress) {
          cov_1i07i0i58r().b[8][0]++;
          cov_1i07i0i58r().s[27]++;
          console.log('Sync already in progress');
          cov_1i07i0i58r().s[28]++;
          return this.createEmptySyncResult();
        } else {
          cov_1i07i0i58r().b[8][1]++;
        }
        cov_1i07i0i58r().s[29]++;
        if (!this.isOnline) {
          cov_1i07i0i58r().b[9][0]++;
          cov_1i07i0i58r().s[30]++;
          console.log('Cannot sync while offline');
          cov_1i07i0i58r().s[31]++;
          return this.createEmptySyncResult();
        } else {
          cov_1i07i0i58r().b[9][1]++;
        }
        cov_1i07i0i58r().s[32]++;
        this.syncInProgress = true;
        var startTime = (cov_1i07i0i58r().s[33]++, Date.now());
        var result = (cov_1i07i0i58r().s[34]++, {
          success: true,
          operations: [],
          conflicts: [],
          errors: [],
          syncTime: 0
        });
        cov_1i07i0i58r().s[35]++;
        try {
          cov_1i07i0i58r().s[36]++;
          console.log(`Starting sync of ${this.operationQueue.length} operations`);
          var prioritizedOperations = (cov_1i07i0i58r().s[37]++, this.groupOperationsByPriority());
          cov_1i07i0i58r().s[38]++;
          for (var _ref2 of prioritizedOperations) {
            var _ref3 = _slicedToArray(_ref2, 2);
            var priority = _ref3[0];
            var operations = _ref3[1];
            cov_1i07i0i58r().s[39]++;
            console.log(`Syncing ${operations.length} ${priority} priority operations`);
            cov_1i07i0i58r().s[40]++;
            for (var operation of operations) {
              cov_1i07i0i58r().s[41]++;
              try {
                var syncResult = (cov_1i07i0i58r().s[42]++, yield this.syncSingleOperation(operation));
                cov_1i07i0i58r().s[43]++;
                if (syncResult.success) {
                  cov_1i07i0i58r().b[10][0]++;
                  cov_1i07i0i58r().s[44]++;
                  this.removeOperationFromQueue(operation.id);
                  cov_1i07i0i58r().s[45]++;
                  result.operations.push(operation);
                } else {
                  cov_1i07i0i58r().b[10][1]++;
                  cov_1i07i0i58r().s[46]++;
                  if (syncResult.conflict) {
                    cov_1i07i0i58r().b[11][0]++;
                    cov_1i07i0i58r().s[47]++;
                    result.conflicts.push(syncResult.conflict);
                  } else {
                    cov_1i07i0i58r().b[11][1]++;
                    cov_1i07i0i58r().s[48]++;
                    operation.retryCount++;
                    cov_1i07i0i58r().s[49]++;
                    if (operation.retryCount >= operation.maxRetries) {
                      cov_1i07i0i58r().b[12][0]++;
                      cov_1i07i0i58r().s[50]++;
                      result.errors.push(`Max retries exceeded for operation ${operation.id}`);
                      cov_1i07i0i58r().s[51]++;
                      this.removeOperationFromQueue(operation.id);
                    } else {
                      cov_1i07i0i58r().b[12][1]++;
                    }
                  }
                }
              } catch (error) {
                cov_1i07i0i58r().s[52]++;
                result.errors.push(`Sync error for operation ${operation.id}: ${error}`);
                cov_1i07i0i58r().s[53]++;
                operation.retryCount++;
              }
            }
          }
          cov_1i07i0i58r().s[54]++;
          yield this.persistOperations();
          cov_1i07i0i58r().s[55]++;
          yield AsyncStorage.setItem(this.STORAGE_KEYS.LAST_SYNC, Date.now().toString());
          cov_1i07i0i58r().s[56]++;
          result.syncTime = Date.now() - startTime;
          cov_1i07i0i58r().s[57]++;
          result.success = result.errors.length === 0;
          cov_1i07i0i58r().s[58]++;
          console.log(`Sync completed in ${result.syncTime}ms:`, {
            synced: result.operations.length,
            conflicts: result.conflicts.length,
            errors: result.errors.length
          });
          cov_1i07i0i58r().s[59]++;
          performanceMonitor.trackDatabaseQuery('offline_sync', result.syncTime);
        } catch (error) {
          cov_1i07i0i58r().s[60]++;
          result.success = false;
          cov_1i07i0i58r().s[61]++;
          result.errors.push(`Sync failed: ${error}`);
          cov_1i07i0i58r().s[62]++;
          console.error('Sync operation failed:', error);
        } finally {
          cov_1i07i0i58r().s[63]++;
          this.syncInProgress = false;
          cov_1i07i0i58r().s[64]++;
          this.notifySyncListeners(result);
        }
        cov_1i07i0i58r().s[65]++;
        return result;
      });
      function syncOperations() {
        return _syncOperations.apply(this, arguments);
      }
      return syncOperations;
    }())
  }, {
    key: "getData",
    value: (function () {
      var _getData = _asyncToGenerator(function* (table) {
        var query = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1i07i0i58r().b[13][0]++, {});
        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_1i07i0i58r().b[14][0]++, {});
        cov_1i07i0i58r().f[4]++;
        var _ref4 = (cov_1i07i0i58r().s[66]++, options),
          _ref4$useCache = _ref4.useCache,
          useCache = _ref4$useCache === void 0 ? (cov_1i07i0i58r().b[15][0]++, true) : _ref4$useCache,
          _ref4$fallbackToCache = _ref4.fallbackToCache,
          fallbackToCache = _ref4$fallbackToCache === void 0 ? (cov_1i07i0i58r().b[16][0]++, true) : _ref4$fallbackToCache,
          _ref4$cacheTimeout = _ref4.cacheTimeout,
          cacheTimeout = _ref4$cacheTimeout === void 0 ? (cov_1i07i0i58r().b[17][0]++, 300000) : _ref4$cacheTimeout;
        var cacheKey = (cov_1i07i0i58r().s[67]++, `offline_data_${table}_${JSON.stringify(query)}`);
        cov_1i07i0i58r().s[68]++;
        try {
          cov_1i07i0i58r().s[69]++;
          if (useCache) {
            cov_1i07i0i58r().b[18][0]++;
            var cachedData = (cov_1i07i0i58r().s[70]++, yield advancedCacheManager.get(cacheKey));
            cov_1i07i0i58r().s[71]++;
            if (cachedData) {
              cov_1i07i0i58r().b[19][0]++;
              cov_1i07i0i58r().s[72]++;
              console.log(`Cache hit for ${table}`);
              cov_1i07i0i58r().s[73]++;
              return cachedData;
            } else {
              cov_1i07i0i58r().b[19][1]++;
            }
          } else {
            cov_1i07i0i58r().b[18][1]++;
          }
          cov_1i07i0i58r().s[74]++;
          if (this.isOnline) {
            cov_1i07i0i58r().b[20][0]++;
            var _ref5 = (cov_1i07i0i58r().s[75]++, yield supabase.from(table).select('*').match(query)),
              data = _ref5.data,
              error = _ref5.error;
            cov_1i07i0i58r().s[76]++;
            if (error) {
              cov_1i07i0i58r().b[21][0]++;
              cov_1i07i0i58r().s[77]++;
              throw error;
            } else {
              cov_1i07i0i58r().b[21][1]++;
            }
            cov_1i07i0i58r().s[78]++;
            if ((cov_1i07i0i58r().b[23][0]++, useCache) && (cov_1i07i0i58r().b[23][1]++, data)) {
              cov_1i07i0i58r().b[22][0]++;
              cov_1i07i0i58r().s[79]++;
              yield advancedCacheManager.set(cacheKey, data, {
                ttl: cacheTimeout,
                priority: this.config.priorityTables.includes(table) ? (cov_1i07i0i58r().b[24][0]++, 'high') : (cov_1i07i0i58r().b[24][1]++, 'medium'),
                tags: ['offline_data', table]
              });
            } else {
              cov_1i07i0i58r().b[22][1]++;
            }
            cov_1i07i0i58r().s[80]++;
            return data;
          } else {
            cov_1i07i0i58r().b[20][1]++;
          }
          cov_1i07i0i58r().s[81]++;
          if (fallbackToCache) {
            cov_1i07i0i58r().b[25][0]++;
            var _cachedData = (cov_1i07i0i58r().s[82]++, yield advancedCacheManager.get(cacheKey));
            cov_1i07i0i58r().s[83]++;
            if (_cachedData) {
              cov_1i07i0i58r().b[26][0]++;
              cov_1i07i0i58r().s[84]++;
              console.log(`Offline fallback to cache for ${table}`);
              cov_1i07i0i58r().s[85]++;
              return _cachedData;
            } else {
              cov_1i07i0i58r().b[26][1]++;
            }
          } else {
            cov_1i07i0i58r().b[25][1]++;
          }
          cov_1i07i0i58r().s[86]++;
          throw new Error(`No data available for ${table} while offline`);
        } catch (error) {
          cov_1i07i0i58r().s[87]++;
          console.error(`Failed to get data for ${table}:`, error);
          cov_1i07i0i58r().s[88]++;
          if (fallbackToCache) {
            cov_1i07i0i58r().b[27][0]++;
            var _cachedData2 = (cov_1i07i0i58r().s[89]++, yield advancedCacheManager.get(cacheKey));
            cov_1i07i0i58r().s[90]++;
            if (_cachedData2) {
              cov_1i07i0i58r().b[28][0]++;
              cov_1i07i0i58r().s[91]++;
              console.log(`Error fallback to cache for ${table}`);
              cov_1i07i0i58r().s[92]++;
              return _cachedData2;
            } else {
              cov_1i07i0i58r().b[28][1]++;
            }
          } else {
            cov_1i07i0i58r().b[27][1]++;
          }
          cov_1i07i0i58r().s[93]++;
          throw error;
        }
      });
      function getData(_x4) {
        return _getData.apply(this, arguments);
      }
      return getData;
    }())
  }, {
    key: "isDeviceOnline",
    value: function isDeviceOnline() {
      cov_1i07i0i58r().f[5]++;
      cov_1i07i0i58r().s[94]++;
      return this.isOnline;
    }
  }, {
    key: "getPendingOperationsCount",
    value: function getPendingOperationsCount() {
      cov_1i07i0i58r().f[6]++;
      cov_1i07i0i58r().s[95]++;
      return this.operationQueue.length;
    }
  }, {
    key: "getSyncStatus",
    value: function getSyncStatus() {
      cov_1i07i0i58r().f[7]++;
      cov_1i07i0i58r().s[96]++;
      return {
        isOnline: this.isOnline,
        syncInProgress: this.syncInProgress,
        pendingOperations: this.operationQueue.length,
        lastSync: null
      };
    }
  }, {
    key: "addSyncListener",
    value: function addSyncListener(listener) {
      cov_1i07i0i58r().f[8]++;
      cov_1i07i0i58r().s[97]++;
      this.syncListeners.push(listener);
    }
  }, {
    key: "removeSyncListener",
    value: function removeSyncListener(listener) {
      cov_1i07i0i58r().f[9]++;
      var index = (cov_1i07i0i58r().s[98]++, this.syncListeners.indexOf(listener));
      cov_1i07i0i58r().s[99]++;
      if (index > -1) {
        cov_1i07i0i58r().b[29][0]++;
        cov_1i07i0i58r().s[100]++;
        this.syncListeners.splice(index, 1);
      } else {
        cov_1i07i0i58r().b[29][1]++;
      }
    }
  }, {
    key: "registerConflictResolver",
    value: function registerConflictResolver(table, resolver) {
      cov_1i07i0i58r().f[10]++;
      cov_1i07i0i58r().s[101]++;
      this.conflictResolvers.set(table, resolver);
    }
  }, {
    key: "clearOfflineData",
    value: (function () {
      var _clearOfflineData = _asyncToGenerator(function* () {
        cov_1i07i0i58r().f[11]++;
        cov_1i07i0i58r().s[102]++;
        this.operationQueue = [];
        cov_1i07i0i58r().s[103]++;
        yield AsyncStorage.multiRemove([this.STORAGE_KEYS.OPERATIONS, this.STORAGE_KEYS.LAST_SYNC, this.STORAGE_KEYS.CONFLICT_LOG]);
        cov_1i07i0i58r().s[104]++;
        yield advancedCacheManager.invalidate(['offline_data']);
        cov_1i07i0i58r().s[105]++;
        console.log('Offline data cleared');
      });
      function clearOfflineData() {
        return _clearOfflineData.apply(this, arguments);
      }
      return clearOfflineData;
    }())
  }, {
    key: "setupNetworkMonitoring",
    value: function () {
      var _setupNetworkMonitoring = _asyncToGenerator(function* () {
        var _this = this,
          _ref7;
        cov_1i07i0i58r().f[12]++;
        cov_1i07i0i58r().s[106]++;
        NetInfo.addEventListener(function (state) {
          var _ref6;
          cov_1i07i0i58r().f[13]++;
          var wasOnline = (cov_1i07i0i58r().s[107]++, _this.isOnline);
          cov_1i07i0i58r().s[108]++;
          _this.isOnline = (_ref6 = (cov_1i07i0i58r().b[30][0]++, state.isConnected)) != null ? _ref6 : (cov_1i07i0i58r().b[30][1]++, false);
          cov_1i07i0i58r().s[109]++;
          console.log(`Network status changed: ${_this.isOnline ? (cov_1i07i0i58r().b[31][0]++, 'online') : (cov_1i07i0i58r().b[31][1]++, 'offline')}`);
          cov_1i07i0i58r().s[110]++;
          if ((cov_1i07i0i58r().b[33][0]++, !wasOnline) && (cov_1i07i0i58r().b[33][1]++, _this.isOnline) && (cov_1i07i0i58r().b[33][2]++, _this.operationQueue.length > 0)) {
            cov_1i07i0i58r().b[32][0]++;
            cov_1i07i0i58r().s[111]++;
            console.log('Device back online, triggering sync');
            cov_1i07i0i58r().s[112]++;
            setTimeout(function () {
              cov_1i07i0i58r().f[14]++;
              cov_1i07i0i58r().s[113]++;
              return _this.syncOperations();
            }, 1000);
          } else {
            cov_1i07i0i58r().b[32][1]++;
          }
        });
        var state = (cov_1i07i0i58r().s[114]++, yield NetInfo.fetch());
        cov_1i07i0i58r().s[115]++;
        this.isOnline = (_ref7 = (cov_1i07i0i58r().b[34][0]++, state.isConnected)) != null ? _ref7 : (cov_1i07i0i58r().b[34][1]++, false);
      });
      function setupNetworkMonitoring() {
        return _setupNetworkMonitoring.apply(this, arguments);
      }
      return setupNetworkMonitoring;
    }()
  }, {
    key: "setupAutoSync",
    value: function setupAutoSync() {
      var _this2 = this;
      cov_1i07i0i58r().f[15]++;
      cov_1i07i0i58r().s[116]++;
      setInterval(function () {
        cov_1i07i0i58r().f[16]++;
        cov_1i07i0i58r().s[117]++;
        if ((cov_1i07i0i58r().b[36][0]++, _this2.isOnline) && (cov_1i07i0i58r().b[36][1]++, !_this2.syncInProgress) && (cov_1i07i0i58r().b[36][2]++, _this2.operationQueue.length > 0)) {
          cov_1i07i0i58r().b[35][0]++;
          cov_1i07i0i58r().s[118]++;
          _this2.syncOperations();
        } else {
          cov_1i07i0i58r().b[35][1]++;
        }
      }, this.config.syncInterval);
    }
  }, {
    key: "setupBackgroundSync",
    value: function setupBackgroundSync() {
      cov_1i07i0i58r().f[17]++;
      cov_1i07i0i58r().s[119]++;
      console.log('Background sync setup (would use background tasks in production)');
    }
  }, {
    key: "loadPersistedOperations",
    value: function () {
      var _loadPersistedOperations = _asyncToGenerator(function* () {
        cov_1i07i0i58r().f[18]++;
        cov_1i07i0i58r().s[120]++;
        try {
          var stored = (cov_1i07i0i58r().s[121]++, yield AsyncStorage.getItem(this.STORAGE_KEYS.OPERATIONS));
          cov_1i07i0i58r().s[122]++;
          if (stored) {
            cov_1i07i0i58r().b[37][0]++;
            cov_1i07i0i58r().s[123]++;
            this.operationQueue = JSON.parse(stored);
            cov_1i07i0i58r().s[124]++;
            console.log(`Loaded ${this.operationQueue.length} persisted operations`);
          } else {
            cov_1i07i0i58r().b[37][1]++;
          }
        } catch (error) {
          cov_1i07i0i58r().s[125]++;
          console.error('Failed to load persisted operations:', error);
        }
      });
      function loadPersistedOperations() {
        return _loadPersistedOperations.apply(this, arguments);
      }
      return loadPersistedOperations;
    }()
  }, {
    key: "persistOperations",
    value: function () {
      var _persistOperations = _asyncToGenerator(function* () {
        cov_1i07i0i58r().f[19]++;
        cov_1i07i0i58r().s[126]++;
        try {
          cov_1i07i0i58r().s[127]++;
          yield AsyncStorage.setItem(this.STORAGE_KEYS.OPERATIONS, JSON.stringify(this.operationQueue));
        } catch (error) {
          cov_1i07i0i58r().s[128]++;
          console.error('Failed to persist operations:', error);
        }
      });
      function persistOperations() {
        return _persistOperations.apply(this, arguments);
      }
      return persistOperations;
    }()
  }, {
    key: "sortOperationQueue",
    value: function sortOperationQueue() {
      cov_1i07i0i58r().f[20]++;
      cov_1i07i0i58r().s[129]++;
      this.operationQueue.sort(function (a, b) {
        cov_1i07i0i58r().f[21]++;
        var priorityOrder = (cov_1i07i0i58r().s[130]++, {
          high: 0,
          medium: 1,
          low: 2
        });
        var priorityDiff = (cov_1i07i0i58r().s[131]++, priorityOrder[a.priority] - priorityOrder[b.priority]);
        cov_1i07i0i58r().s[132]++;
        if (priorityDiff !== 0) {
          cov_1i07i0i58r().b[38][0]++;
          cov_1i07i0i58r().s[133]++;
          return priorityDiff;
        } else {
          cov_1i07i0i58r().b[38][1]++;
        }
        cov_1i07i0i58r().s[134]++;
        return a.timestamp - b.timestamp;
      });
    }
  }, {
    key: "groupOperationsByPriority",
    value: function groupOperationsByPriority() {
      cov_1i07i0i58r().f[22]++;
      var groups = (cov_1i07i0i58r().s[135]++, new Map());
      cov_1i07i0i58r().s[136]++;
      for (var operation of this.operationQueue) {
        cov_1i07i0i58r().s[137]++;
        if (!groups.has(operation.priority)) {
          cov_1i07i0i58r().b[39][0]++;
          cov_1i07i0i58r().s[138]++;
          groups.set(operation.priority, []);
        } else {
          cov_1i07i0i58r().b[39][1]++;
        }
        cov_1i07i0i58r().s[139]++;
        groups.get(operation.priority).push(operation);
      }
      cov_1i07i0i58r().s[140]++;
      return groups;
    }
  }, {
    key: "syncSingleOperation",
    value: function () {
      var _syncSingleOperation = _asyncToGenerator(function* (operation) {
        cov_1i07i0i58r().f[23]++;
        cov_1i07i0i58r().s[141]++;
        try {
          var result;
          cov_1i07i0i58r().s[142]++;
          switch (operation.type) {
            case 'create':
              cov_1i07i0i58r().b[40][0]++;
              cov_1i07i0i58r().s[143]++;
              result = yield supabase.from(operation.table).insert(operation.data);
              cov_1i07i0i58r().s[144]++;
              break;
            case 'update':
              cov_1i07i0i58r().b[40][1]++;
              cov_1i07i0i58r().s[145]++;
              result = yield supabase.from(operation.table).update(operation.data).eq('id', operation.data.id);
              cov_1i07i0i58r().s[146]++;
              break;
            case 'delete':
              cov_1i07i0i58r().b[40][2]++;
              cov_1i07i0i58r().s[147]++;
              result = yield supabase.from(operation.table).delete().eq('id', operation.data.id);
              cov_1i07i0i58r().s[148]++;
              break;
          }
          cov_1i07i0i58r().s[149]++;
          if (result.error) {
            cov_1i07i0i58r().b[41][0]++;
            cov_1i07i0i58r().s[150]++;
            if (result.error.code === 'PGRST116') {
              cov_1i07i0i58r().b[42][0]++;
              var conflict = (cov_1i07i0i58r().s[151]++, yield this.handleConflict(operation, result.error));
              cov_1i07i0i58r().s[152]++;
              return {
                success: false,
                conflict: conflict
              };
            } else {
              cov_1i07i0i58r().b[42][1]++;
            }
            cov_1i07i0i58r().s[153]++;
            throw result.error;
          } else {
            cov_1i07i0i58r().b[41][1]++;
          }
          cov_1i07i0i58r().s[154]++;
          return {
            success: true
          };
        } catch (error) {
          cov_1i07i0i58r().s[155]++;
          console.error(`Failed to sync operation ${operation.id}:`, error);
          cov_1i07i0i58r().s[156]++;
          return {
            success: false
          };
        }
      });
      function syncSingleOperation(_x5) {
        return _syncSingleOperation.apply(this, arguments);
      }
      return syncSingleOperation;
    }()
  }, {
    key: "handleConflict",
    value: function () {
      var _handleConflict = _asyncToGenerator(function* (operation, error) {
        cov_1i07i0i58r().f[24]++;
        var conflict = (cov_1i07i0i58r().s[157]++, {
          operationId: operation.id,
          conflictType: 'version',
          resolution: this.config.conflictResolution,
          localData: operation.data,
          remoteData: null
        });
        var resolver = (cov_1i07i0i58r().s[158]++, this.conflictResolvers.get(operation.table));
        cov_1i07i0i58r().s[159]++;
        if (resolver) {
          cov_1i07i0i58r().b[43][0]++;
          cov_1i07i0i58r().s[160]++;
          try {
            cov_1i07i0i58r().s[161]++;
            conflict.resolvedData = yield resolver(conflict);
            cov_1i07i0i58r().s[162]++;
            conflict.resolution = 'manual';
          } catch (resolverError) {
            cov_1i07i0i58r().s[163]++;
            console.error('Conflict resolver failed:', resolverError);
          }
        } else {
          cov_1i07i0i58r().b[43][1]++;
        }
        cov_1i07i0i58r().s[164]++;
        if (!conflict.resolvedData) {
          cov_1i07i0i58r().b[44][0]++;
          cov_1i07i0i58r().s[165]++;
          switch (this.config.conflictResolution) {
            case 'local':
              cov_1i07i0i58r().b[45][0]++;
              cov_1i07i0i58r().s[166]++;
              conflict.resolvedData = conflict.localData;
              cov_1i07i0i58r().s[167]++;
              break;
            case 'remote':
              cov_1i07i0i58r().b[45][1]++;
              cov_1i07i0i58r().s[168]++;
              conflict.resolvedData = conflict.remoteData;
              cov_1i07i0i58r().s[169]++;
              break;
            case 'merge':
              cov_1i07i0i58r().b[45][2]++;
              cov_1i07i0i58r().s[170]++;
              conflict.resolvedData = Object.assign({}, conflict.remoteData, conflict.localData);
              cov_1i07i0i58r().s[171]++;
              break;
          }
        } else {
          cov_1i07i0i58r().b[44][1]++;
        }
        cov_1i07i0i58r().s[172]++;
        return conflict;
      });
      function handleConflict(_x6, _x7) {
        return _handleConflict.apply(this, arguments);
      }
      return handleConflict;
    }()
  }, {
    key: "removeOperationFromQueue",
    value: function removeOperationFromQueue(operationId) {
      cov_1i07i0i58r().f[25]++;
      cov_1i07i0i58r().s[173]++;
      this.operationQueue = this.operationQueue.filter(function (op) {
        cov_1i07i0i58r().f[26]++;
        cov_1i07i0i58r().s[174]++;
        return op.id !== operationId;
      });
    }
  }, {
    key: "generateOperationId",
    value: function generateOperationId() {
      cov_1i07i0i58r().f[27]++;
      cov_1i07i0i58r().s[175]++;
      return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "createEmptySyncResult",
    value: function createEmptySyncResult() {
      cov_1i07i0i58r().f[28]++;
      cov_1i07i0i58r().s[176]++;
      return {
        success: false,
        operations: [],
        conflicts: [],
        errors: [],
        syncTime: 0
      };
    }
  }, {
    key: "notifySyncListeners",
    value: function notifySyncListeners(result) {
      cov_1i07i0i58r().f[29]++;
      cov_1i07i0i58r().s[177]++;
      this.syncListeners.forEach(function (listener) {
        cov_1i07i0i58r().f[30]++;
        cov_1i07i0i58r().s[178]++;
        try {
          cov_1i07i0i58r().s[179]++;
          listener(result);
        } catch (error) {
          cov_1i07i0i58r().s[180]++;
          console.error('Sync listener error:', error);
        }
      });
    }
  }]);
}();
export var offlineManager = (cov_1i07i0i58r().s[181]++, new OfflineManager());
export default offlineManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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