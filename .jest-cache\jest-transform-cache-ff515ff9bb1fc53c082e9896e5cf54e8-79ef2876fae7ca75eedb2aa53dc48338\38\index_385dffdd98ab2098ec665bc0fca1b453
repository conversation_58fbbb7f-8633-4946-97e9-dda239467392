39eb54961a0ce73c346291e2c3ca5d62
"use strict";
'use client';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _EventEmitter = _interopRequireDefault(require("../../vendor/react-native/vendor/emitter/EventEmitter"));
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
var isPrefixed = _canUseDom.default && !document.hasOwnProperty('hidden') && document.hasOwnProperty('webkitHidden');
var EVENT_TYPES = ['change', 'memoryWarning'];
var VISIBILITY_CHANGE_EVENT = isPrefixed ? 'webkitvisibilitychange' : 'visibilitychange';
var VISIBILITY_STATE_PROPERTY = isPrefixed ? 'webkitVisibilityState' : 'visibilityState';
var AppStates = {
  BACKGROUND: 'background',
  ACTIVE: 'active'
};
var changeEmitter = null;
var AppState = function () {
  function AppState() {
    (0, _classCallCheck2.default)(this, AppState);
  }
  return (0, _createClass2.default)(AppState, null, [{
    key: "currentState",
    get: function get() {
      if (!AppState.isAvailable) {
        return AppStates.ACTIVE;
      }
      switch (document[VISIBILITY_STATE_PROPERTY]) {
        case 'hidden':
        case 'prerender':
        case 'unloaded':
          return AppStates.BACKGROUND;
        default:
          return AppStates.ACTIVE;
      }
    }
  }, {
    key: "addEventListener",
    value: function addEventListener(type, handler) {
      if (AppState.isAvailable) {
        (0, _invariant.default)(EVENT_TYPES.indexOf(type) !== -1, 'Trying to subscribe to unknown event: "%s"', type);
        if (type === 'change') {
          if (!changeEmitter) {
            changeEmitter = new _EventEmitter.default();
            document.addEventListener(VISIBILITY_CHANGE_EVENT, function () {
              if (changeEmitter) {
                changeEmitter.emit('change', AppState.currentState);
              }
            }, false);
          }
          return changeEmitter.addListener(type, handler);
        }
      }
    }
  }]);
}();
exports.default = AppState;
AppState.isAvailable = _canUseDom.default && !!document[VISIBILITY_STATE_PROPERTY];
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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