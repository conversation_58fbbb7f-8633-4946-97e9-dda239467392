abe8a1eb5a1a76ea2e55c04d6aa10bbf
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import { env as _env } from "expo/virtual/env";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2ala20ag7y() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\ai\\CoachingAnalysisService.ts";
  var hash = "25de4fa9d4d6e39245b038132bc66bf21ce50b2f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\ai\\CoachingAnalysisService.ts",
    statementMap: {
      "0": {
        start: {
          line: 91,
          column: 33
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "1": {
        start: {
          line: 99,
          column: 25
        },
        end: {
          line: 99,
          column: 26
        }
      },
      "2": {
        start: {
          line: 100,
          column: 26
        },
        end: {
          line: 100,
          column: 36
        }
      },
      "3": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 141,
          column: 5
        }
      },
      "4": {
        start: {
          line: 109,
          column: 6
        },
        end: {
          line: 109,
          column: 52
        }
      },
      "5": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 112,
          column: 34
        }
      },
      "6": {
        start: {
          line: 115,
          column: 23
        },
        end: {
          line: 115,
          column: 59
        }
      },
      "7": {
        start: {
          line: 118,
          column: 32
        },
        end: {
          line: 121,
          column: 7
        }
      },
      "8": {
        start: {
          line: 124,
          column: 31
        },
        end: {
          line: 124,
          column: 87
        }
      },
      "9": {
        start: {
          line: 127,
          column: 24
        },
        end: {
          line: 127,
          column: 71
        }
      },
      "10": {
        start: {
          line: 129,
          column: 45
        },
        end: {
          line: 134,
          column: 7
        }
      },
      "11": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 50
        }
      },
      "12": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 20
        }
      },
      "13": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 139,
          column: 56
        }
      },
      "14": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 62
        }
      },
      "15": {
        start: {
          line: 150,
          column: 40
        },
        end: {
          line: 150,
          column: 42
        }
      },
      "16": {
        start: {
          line: 153,
          column: 27
        },
        end: {
          line: 153,
          column: 78
        }
      },
      "17": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 168,
          column: 5
        }
      },
      "18": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 42
        }
      },
      "19": {
        start: {
          line: 156,
          column: 33
        },
        end: {
          line: 156,
          column: 42
        }
      },
      "20": {
        start: {
          line: 158,
          column: 22
        },
        end: {
          line: 163,
          column: 7
        }
      },
      "21": {
        start: {
          line: 165,
          column: 6
        },
        end: {
          line: 167,
          column: 7
        }
      },
      "22": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 166,
          column: 31
        }
      },
      "23": {
        start: {
          line: 171,
          column: 29
        },
        end: {
          line: 171,
          column: 73
        }
      },
      "24": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 174,
          column: 5
        }
      },
      "25": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 173,
          column: 38
        }
      },
      "26": {
        start: {
          line: 176,
          column: 4
        },
        end: {
          line: 176,
          column: 106
        }
      },
      "27": {
        start: {
          line: 176,
          column: 35
        },
        end: {
          line: 176,
          column: 104
        }
      },
      "28": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 207,
          column: 5
        }
      },
      "29": {
        start: {
          line: 190,
          column: 21
        },
        end: {
          line: 195,
          column: 7
        }
      },
      "30": {
        start: {
          line: 198,
          column: 23
        },
        end: {
          line: 198,
          column: 52
        }
      },
      "31": {
        start: {
          line: 201,
          column: 22
        },
        end: {
          line: 201,
          column: 72
        }
      },
      "32": {
        start: {
          line: 203,
          column: 6
        },
        end: {
          line: 203,
          column: 21
        }
      },
      "33": {
        start: {
          line: 205,
          column: 6
        },
        end: {
          line: 205,
          column: 65
        }
      },
      "34": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 206,
          column: 18
        }
      },
      "35": {
        start: {
          line: 219,
          column: 26
        },
        end: {
          line: 219,
          column: 94
        }
      },
      "36": {
        start: {
          line: 219,
          column: 54
        },
        end: {
          line: 219,
          column: 72
        }
      },
      "37": {
        start: {
          line: 220,
          column: 25
        },
        end: {
          line: 220,
          column: 60
        }
      },
      "38": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 251,
          column: 2
        }
      },
      "39": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 281,
          column: 5
        }
      },
      "40": {
        start: {
          line: 261,
          column: 21
        },
        end: {
          line: 261,
          column: 64
        }
      },
      "41": {
        start: {
          line: 262,
          column: 23
        },
        end: {
          line: 262,
          column: 52
        }
      },
      "42": {
        start: {
          line: 264,
          column: 6
        },
        end: {
          line: 277,
          column: 8
        }
      },
      "43": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 68
        }
      },
      "44": {
        start: {
          line: 280,
          column: 6
        },
        end: {
          line: 280,
          column: 18
        }
      },
      "45": {
        start: {
          line: 288,
          column: 26
        },
        end: {
          line: 288,
          column: 89
        }
      },
      "46": {
        start: {
          line: 288,
          column: 72
        },
        end: {
          line: 288,
          column: 86
        }
      },
      "47": {
        start: {
          line: 290,
          column: 4
        },
        end: {
          line: 308,
          column: 2
        }
      },
      "48": {
        start: {
          line: 316,
          column: 4
        },
        end: {
          line: 316,
          column: 60
        }
      },
      "49": {
        start: {
          line: 316,
          column: 33
        },
        end: {
          line: 316,
          column: 58
        }
      },
      "50": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 330,
          column: 6
        }
      },
      "51": {
        start: {
          line: 337,
          column: 4
        },
        end: {
          line: 350,
          column: 6
        }
      },
      "52": {
        start: {
          line: 360,
          column: 26
        },
        end: {
          line: 360,
          column: 94
        }
      },
      "53": {
        start: {
          line: 360,
          column: 54
        },
        end: {
          line: 360,
          column: 72
        }
      },
      "54": {
        start: {
          line: 361,
          column: 26
        },
        end: {
          line: 361,
          column: 73
        }
      },
      "55": {
        start: {
          line: 361,
          column: 56
        },
        end: {
          line: 361,
          column: 70
        }
      },
      "56": {
        start: {
          line: 363,
          column: 4
        },
        end: {
          line: 368,
          column: 6
        }
      },
      "57": {
        start: {
          line: 377,
          column: 26
        },
        end: {
          line: 377,
          column: 94
        }
      },
      "58": {
        start: {
          line: 377,
          column: 54
        },
        end: {
          line: 377,
          column: 72
        }
      },
      "59": {
        start: {
          line: 378,
          column: 23
        },
        end: {
          line: 378,
          column: 101
        }
      },
      "60": {
        start: {
          line: 378,
          column: 51
        },
        end: {
          line: 378,
          column: 79
        }
      },
      "61": {
        start: {
          line: 380,
          column: 4
        },
        end: {
          line: 385,
          column: 6
        }
      },
      "62": {
        start: {
          line: 395,
          column: 33
        },
        end: {
          line: 395,
          column: 76
        }
      },
      "63": {
        start: {
          line: 395,
          column: 54
        },
        end: {
          line: 395,
          column: 75
        }
      },
      "64": {
        start: {
          line: 397,
          column: 4
        },
        end: {
          line: 401,
          column: 6
        }
      },
      "65": {
        start: {
          line: 398,
          column: 58
        },
        end: {
          line: 398,
          column: 87
        }
      },
      "66": {
        start: {
          line: 406,
          column: 4
        },
        end: {
          line: 411,
          column: 55
        }
      },
      "67": {
        start: {
          line: 407,
          column: 19
        },
        end: {
          line: 407,
          column: 40
        }
      },
      "68": {
        start: {
          line: 408,
          column: 6
        },
        end: {
          line: 408,
          column: 43
        }
      },
      "69": {
        start: {
          line: 408,
          column: 25
        },
        end: {
          line: 408,
          column: 43
        }
      },
      "70": {
        start: {
          line: 409,
          column: 6
        },
        end: {
          line: 409,
          column: 34
        }
      },
      "71": {
        start: {
          line: 410,
          column: 6
        },
        end: {
          line: 410,
          column: 20
        }
      },
      "72": {
        start: {
          line: 415,
          column: 29
        },
        end: {
          line: 415,
          column: 31
        }
      },
      "73": {
        start: {
          line: 417,
          column: 23
        },
        end: {
          line: 417,
          column: 101
        }
      },
      "74": {
        start: {
          line: 417,
          column: 51
        },
        end: {
          line: 417,
          column: 79
        }
      },
      "75": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 418,
          column: 56
        }
      },
      "76": {
        start: {
          line: 418,
          column: 26
        },
        end: {
          line: 418,
          column: 56
        }
      },
      "77": {
        start: {
          line: 420,
          column: 36
        },
        end: {
          line: 420,
          column: 112
        }
      },
      "78": {
        start: {
          line: 420,
          column: 57
        },
        end: {
          line: 420,
          column: 104
        }
      },
      "79": {
        start: {
          line: 421,
          column: 4
        },
        end: {
          line: 421,
          column: 98
        }
      },
      "80": {
        start: {
          line: 421,
          column: 57
        },
        end: {
          line: 421,
          column: 98
        }
      },
      "81": {
        start: {
          line: 423,
          column: 4
        },
        end: {
          line: 423,
          column: 18
        }
      },
      "82": {
        start: {
          line: 427,
          column: 20
        },
        end: {
          line: 427,
          column: 60
        }
      },
      "83": {
        start: {
          line: 427,
          column: 38
        },
        end: {
          line: 427,
          column: 59
        }
      },
      "84": {
        start: {
          line: 428,
          column: 23
        },
        end: {
          line: 428,
          column: 50
        }
      },
      "85": {
        start: {
          line: 429,
          column: 4
        },
        end: {
          line: 429,
          column: 48
        }
      },
      "86": {
        start: {
          line: 433,
          column: 26
        },
        end: {
          line: 433,
          column: 102
        }
      },
      "87": {
        start: {
          line: 433,
          column: 47
        },
        end: {
          line: 433,
          column: 94
        }
      },
      "88": {
        start: {
          line: 434,
          column: 23
        },
        end: {
          line: 434,
          column: 74
        }
      },
      "89": {
        start: {
          line: 435,
          column: 4
        },
        end: {
          line: 435,
          column: 52
        }
      },
      "90": {
        start: {
          line: 439,
          column: 4
        },
        end: {
          line: 441,
          column: 13
        }
      },
      "91": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 440,
          column: 71
        }
      },
      "92": {
        start: {
          line: 440,
          column: 22
        },
        end: {
          line: 440,
          column: 29
        }
      },
      "93": {
        start: {
          line: 440,
          column: 56
        },
        end: {
          line: 440,
          column: 63
        }
      },
      "94": {
        start: {
          line: 445,
          column: 32
        },
        end: {
          line: 445,
          column: 34
        }
      },
      "95": {
        start: {
          line: 447,
          column: 26
        },
        end: {
          line: 447,
          column: 94
        }
      },
      "96": {
        start: {
          line: 447,
          column: 54
        },
        end: {
          line: 447,
          column: 72
        }
      },
      "97": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 448,
          column: 78
        }
      },
      "98": {
        start: {
          line: 448,
          column: 29
        },
        end: {
          line: 448,
          column: 78
        }
      },
      "99": {
        start: {
          line: 450,
          column: 24
        },
        end: {
          line: 450,
          column: 81
        }
      },
      "100": {
        start: {
          line: 450,
          column: 45
        },
        end: {
          line: 450,
          column: 73
        }
      },
      "101": {
        start: {
          line: 451,
          column: 4
        },
        end: {
          line: 451,
          column: 95
        }
      },
      "102": {
        start: {
          line: 451,
          column: 45
        },
        end: {
          line: 451,
          column: 95
        }
      },
      "103": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 453,
          column: 21
        }
      },
      "104": {
        start: {
          line: 457,
          column: 33
        },
        end: {
          line: 457,
          column: 35
        }
      },
      "105": {
        start: {
          line: 459,
          column: 30
        },
        end: {
          line: 459,
          column: 102
        }
      },
      "106": {
        start: {
          line: 459,
          column: 51
        },
        end: {
          line: 459,
          column: 94
        }
      },
      "107": {
        start: {
          line: 460,
          column: 4
        },
        end: {
          line: 460,
          column: 98
        }
      },
      "108": {
        start: {
          line: 460,
          column: 51
        },
        end: {
          line: 460,
          column: 98
        }
      },
      "109": {
        start: {
          line: 462,
          column: 25
        },
        end: {
          line: 462,
          column: 105
        }
      },
      "110": {
        start: {
          line: 462,
          column: 46
        },
        end: {
          line: 462,
          column: 97
        }
      },
      "111": {
        start: {
          line: 463,
          column: 4
        },
        end: {
          line: 463,
          column: 92
        }
      },
      "112": {
        start: {
          line: 463,
          column: 46
        },
        end: {
          line: 463,
          column: 92
        }
      },
      "113": {
        start: {
          line: 465,
          column: 4
        },
        end: {
          line: 465,
          column: 22
        }
      },
      "114": {
        start: {
          line: 472,
          column: 38
        },
        end: {
          line: 472,
          column: 40
        }
      },
      "115": {
        start: {
          line: 474,
          column: 4
        },
        end: {
          line: 480,
          column: 5
        }
      },
      "116": {
        start: {
          line: 475,
          column: 6
        },
        end: {
          line: 475,
          column: 68
        }
      },
      "117": {
        start: {
          line: 476,
          column: 6
        },
        end: {
          line: 476,
          column: 63
        }
      },
      "118": {
        start: {
          line: 478,
          column: 6
        },
        end: {
          line: 478,
          column: 55
        }
      },
      "119": {
        start: {
          line: 479,
          column: 6
        },
        end: {
          line: 479,
          column: 67
        }
      },
      "120": {
        start: {
          line: 482,
          column: 4
        },
        end: {
          line: 482,
          column: 27
        }
      },
      "121": {
        start: {
          line: 486,
          column: 4
        },
        end: {
          line: 488,
          column: 5
        }
      },
      "122": {
        start: {
          line: 487,
          column: 6
        },
        end: {
          line: 487,
          column: 23
        }
      },
      "123": {
        start: {
          line: 490,
          column: 4
        },
        end: {
          line: 490,
          column: 78
        }
      },
      "124": {
        start: {
          line: 495,
          column: 34
        },
        end: {
          line: 495,
          column: 110
        }
      },
      "125": {
        start: {
          line: 495,
          column: 55
        },
        end: {
          line: 495,
          column: 102
        }
      },
      "126": {
        start: {
          line: 496,
          column: 4
        },
        end: {
          line: 496,
          column: 71
        }
      },
      "127": {
        start: {
          line: 501,
          column: 26
        },
        end: {
          line: 501,
          column: 94
        }
      },
      "128": {
        start: {
          line: 501,
          column: 54
        },
        end: {
          line: 501,
          column: 72
        }
      },
      "129": {
        start: {
          line: 502,
          column: 4
        },
        end: {
          line: 502,
          column: 43
        }
      },
      "130": {
        start: {
          line: 506,
          column: 28
        },
        end: {
          line: 510,
          column: 5
        }
      },
      "131": {
        start: {
          line: 512,
          column: 4
        },
        end: {
          line: 516,
          column: 5
        }
      },
      "132": {
        start: {
          line: 513,
          column: 6
        },
        end: {
          line: 513,
          column: 65
        }
      },
      "133": {
        start: {
          line: 515,
          column: 6
        },
        end: {
          line: 515,
          column: 75
        }
      },
      "134": {
        start: {
          line: 518,
          column: 4
        },
        end: {
          line: 518,
          column: 27
        }
      },
      "135": {
        start: {
          line: 522,
          column: 18
        },
        end: {
          line: 525,
          column: 5
        }
      },
      "136": {
        start: {
          line: 527,
          column: 4
        },
        end: {
          line: 529,
          column: 5
        }
      },
      "137": {
        start: {
          line: 528,
          column: 6
        },
        end: {
          line: 528,
          column: 49
        }
      },
      "138": {
        start: {
          line: 531,
          column: 4
        },
        end: {
          line: 531,
          column: 17
        }
      },
      "139": {
        start: {
          line: 536,
          column: 4
        },
        end: {
          line: 540,
          column: 5
        }
      },
      "140": {
        start: {
          line: 537,
          column: 6
        },
        end: {
          line: 537,
          column: 20
        }
      },
      "141": {
        start: {
          line: 538,
          column: 11
        },
        end: {
          line: 540,
          column: 5
        }
      },
      "142": {
        start: {
          line: 539,
          column: 6
        },
        end: {
          line: 539,
          column: 22
        }
      },
      "143": {
        start: {
          line: 541,
          column: 4
        },
        end: {
          line: 541,
          column: 17
        }
      },
      "144": {
        start: {
          line: 545,
          column: 4
        },
        end: {
          line: 549,
          column: 5
        }
      },
      "145": {
        start: {
          line: 546,
          column: 19
        },
        end: {
          line: 546,
          column: 28
        }
      },
      "146": {
        start: {
          line: 547,
          column: 21
        },
        end: {
          line: 547,
          column: 30
        }
      },
      "147": {
        start: {
          line: 548,
          column: 18
        },
        end: {
          line: 548,
          column: 27
        }
      },
      "148": {
        start: {
          line: 553,
          column: 4
        },
        end: {
          line: 553,
          column: 78
        }
      },
      "149": {
        start: {
          line: 557,
          column: 16
        },
        end: {
          line: 557,
          column: 26
        }
      },
      "150": {
        start: {
          line: 558,
          column: 27
        },
        end: {
          line: 558,
          column: 51
        }
      },
      "151": {
        start: {
          line: 560,
          column: 4
        },
        end: {
          line: 563,
          column: 5
        }
      },
      "152": {
        start: {
          line: 561,
          column: 6
        },
        end: {
          line: 561,
          column: 28
        }
      },
      "153": {
        start: {
          line: 562,
          column: 6
        },
        end: {
          line: 562,
          column: 31
        }
      },
      "154": {
        start: {
          line: 565,
          column: 4
        },
        end: {
          line: 570,
          column: 5
        }
      },
      "155": {
        start: {
          line: 566,
          column: 23
        },
        end: {
          line: 566,
          column: 45
        }
      },
      "156": {
        start: {
          line: 567,
          column: 6
        },
        end: {
          line: 567,
          column: 66
        }
      },
      "157": {
        start: {
          line: 567,
          column: 35
        },
        end: {
          line: 567,
          column: 64
        }
      },
      "158": {
        start: {
          line: 568,
          column: 6
        },
        end: {
          line: 568,
          column: 28
        }
      },
      "159": {
        start: {
          line: 569,
          column: 6
        },
        end: {
          line: 569,
          column: 38
        }
      },
      "160": {
        start: {
          line: 572,
          column: 4
        },
        end: {
          line: 572,
          column: 24
        }
      },
      "161": {
        start: {
          line: 577,
          column: 39
        },
        end: {
          line: 577,
          column: 68
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 105,
            column: 2
          },
          end: {
            line: 105,
            column: 3
          }
        },
        loc: {
          start: {
            line: 107,
            column: 37
          },
          end: {
            line: 142,
            column: 3
          }
        },
        line: 107
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 147,
            column: 2
          },
          end: {
            line: 147,
            column: 3
          }
        },
        loc: {
          start: {
            line: 149,
            column: 32
          },
          end: {
            line: 177,
            column: 3
          }
        },
        line: 149
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 176,
            column: 25
          },
          end: {
            line: 176,
            column: 26
          }
        },
        loc: {
          start: {
            line: 176,
            column: 35
          },
          end: {
            line: 176,
            column: 104
          }
        },
        line: 176
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 182,
            column: 2
          },
          end: {
            line: 182,
            column: 3
          }
        },
        loc: {
          start: {
            line: 187,
            column: 37
          },
          end: {
            line: 208,
            column: 3
          }
        },
        line: 187
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 213,
            column: 2
          },
          end: {
            line: 213,
            column: 3
          }
        },
        loc: {
          start: {
            line: 218,
            column: 12
          },
          end: {
            line: 252,
            column: 3
          }
        },
        line: 218
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 219,
            column: 42
          },
          end: {
            line: 219,
            column: 43
          }
        },
        loc: {
          start: {
            line: 219,
            column: 54
          },
          end: {
            line: 219,
            column: 72
          }
        },
        line: 219
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 257,
            column: 2
          },
          end: {
            line: 257,
            column: 3
          }
        },
        loc: {
          start: {
            line: 259,
            column: 37
          },
          end: {
            line: 282,
            column: 3
          }
        },
        line: 259
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 287,
            column: 2
          },
          end: {
            line: 287,
            column: 3
          }
        },
        loc: {
          start: {
            line: 287,
            column: 82
          },
          end: {
            line: 309,
            column: 3
          }
        },
        line: 287
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 288,
            column: 67
          },
          end: {
            line: 288,
            column: 68
          }
        },
        loc: {
          start: {
            line: 288,
            column: 72
          },
          end: {
            line: 288,
            column: 86
          }
        },
        line: 288
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 314,
            column: 2
          },
          end: {
            line: 314,
            column: 3
          }
        },
        loc: {
          start: {
            line: 314,
            column: 57
          },
          end: {
            line: 331,
            column: 3
          }
        },
        line: 314
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 316,
            column: 22
          },
          end: {
            line: 316,
            column: 23
          }
        },
        loc: {
          start: {
            line: 316,
            column: 33
          },
          end: {
            line: 316,
            column: 58
          }
        },
        line: 316
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 336,
            column: 2
          },
          end: {
            line: 336,
            column: 3
          }
        },
        loc: {
          start: {
            line: 336,
            column: 86
          },
          end: {
            line: 351,
            column: 3
          }
        },
        line: 336
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 356,
            column: 2
          },
          end: {
            line: 356,
            column: 3
          }
        },
        loc: {
          start: {
            line: 359,
            column: 49
          },
          end: {
            line: 369,
            column: 3
          }
        },
        line: 359
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 360,
            column: 42
          },
          end: {
            line: 360,
            column: 43
          }
        },
        loc: {
          start: {
            line: 360,
            column: 54
          },
          end: {
            line: 360,
            column: 72
          }
        },
        line: 360
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 361,
            column: 51
          },
          end: {
            line: 361,
            column: 52
          }
        },
        loc: {
          start: {
            line: 361,
            column: 56
          },
          end: {
            line: 361,
            column: 70
          }
        },
        line: 361
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 374,
            column: 2
          },
          end: {
            line: 374,
            column: 3
          }
        },
        loc: {
          start: {
            line: 376,
            column: 48
          },
          end: {
            line: 386,
            column: 3
          }
        },
        line: 376
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 377,
            column: 42
          },
          end: {
            line: 377,
            column: 43
          }
        },
        loc: {
          start: {
            line: 377,
            column: 54
          },
          end: {
            line: 377,
            column: 72
          }
        },
        line: 377
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 378,
            column: 39
          },
          end: {
            line: 378,
            column: 40
          }
        },
        loc: {
          start: {
            line: 378,
            column: 51
          },
          end: {
            line: 378,
            column: 79
          }
        },
        line: 378
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 391,
            column: 2
          },
          end: {
            line: 391,
            column: 3
          }
        },
        loc: {
          start: {
            line: 394,
            column: 50
          },
          end: {
            line: 402,
            column: 3
          }
        },
        line: 394
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 395,
            column: 49
          },
          end: {
            line: 395,
            column: 50
          }
        },
        loc: {
          start: {
            line: 395,
            column: 54
          },
          end: {
            line: 395,
            column: 75
          }
        },
        line: 395
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 398,
            column: 53
          },
          end: {
            line: 398,
            column: 54
          }
        },
        loc: {
          start: {
            line: 398,
            column: 58
          },
          end: {
            line: 398,
            column: 87
          }
        },
        line: 398
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 405,
            column: 2
          },
          end: {
            line: 405,
            column: 3
          }
        },
        loc: {
          start: {
            line: 405,
            column: 109
          },
          end: {
            line: 412,
            column: 3
          }
        },
        line: 405
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 406,
            column: 27
          },
          end: {
            line: 406,
            column: 28
          }
        },
        loc: {
          start: {
            line: 406,
            column: 49
          },
          end: {
            line: 411,
            column: 5
          }
        },
        line: 406
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 414,
            column: 2
          },
          end: {
            line: 414,
            column: 3
          }
        },
        loc: {
          start: {
            line: 414,
            column: 77
          },
          end: {
            line: 424,
            column: 3
          }
        },
        line: 414
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 417,
            column: 39
          },
          end: {
            line: 417,
            column: 40
          }
        },
        loc: {
          start: {
            line: 417,
            column: 51
          },
          end: {
            line: 417,
            column: 79
          }
        },
        line: 417
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 420,
            column: 52
          },
          end: {
            line: 420,
            column: 53
          }
        },
        loc: {
          start: {
            line: 420,
            column: 57
          },
          end: {
            line: 420,
            column: 104
          }
        },
        line: 420
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 426,
            column: 2
          },
          end: {
            line: 426,
            column: 3
          }
        },
        loc: {
          start: {
            line: 426,
            column: 77
          },
          end: {
            line: 430,
            column: 3
          }
        },
        line: 426
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 427,
            column: 33
          },
          end: {
            line: 427,
            column: 34
          }
        },
        loc: {
          start: {
            line: 427,
            column: 38
          },
          end: {
            line: 427,
            column: 59
          }
        },
        line: 427
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 432,
            column: 2
          },
          end: {
            line: 432,
            column: 3
          }
        },
        loc: {
          start: {
            line: 432,
            column: 80
          },
          end: {
            line: 436,
            column: 3
          }
        },
        line: 432
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 433,
            column: 42
          },
          end: {
            line: 433,
            column: 43
          }
        },
        loc: {
          start: {
            line: 433,
            column: 47
          },
          end: {
            line: 433,
            column: 94
          }
        },
        line: 433
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 438,
            column: 2
          },
          end: {
            line: 438,
            column: 3
          }
        },
        loc: {
          start: {
            line: 438,
            column: 40
          },
          end: {
            line: 442,
            column: 3
          }
        },
        line: 438
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 439,
            column: 20
          },
          end: {
            line: 439,
            column: 21
          }
        },
        loc: {
          start: {
            line: 440,
            column: 6
          },
          end: {
            line: 440,
            column: 71
          }
        },
        line: 440
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 440,
            column: 17
          },
          end: {
            line: 440,
            column: 18
          }
        },
        loc: {
          start: {
            line: 440,
            column: 22
          },
          end: {
            line: 440,
            column: 29
          }
        },
        line: 440
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 440,
            column: 51
          },
          end: {
            line: 440,
            column: 52
          }
        },
        loc: {
          start: {
            line: 440,
            column: 56
          },
          end: {
            line: 440,
            column: 63
          }
        },
        line: 440
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 444,
            column: 2
          },
          end: {
            line: 444,
            column: 3
          }
        },
        loc: {
          start: {
            line: 444,
            column: 74
          },
          end: {
            line: 454,
            column: 3
          }
        },
        line: 444
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 447,
            column: 42
          },
          end: {
            line: 447,
            column: 43
          }
        },
        loc: {
          start: {
            line: 447,
            column: 54
          },
          end: {
            line: 447,
            column: 72
          }
        },
        line: 447
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 450,
            column: 40
          },
          end: {
            line: 450,
            column: 41
          }
        },
        loc: {
          start: {
            line: 450,
            column: 45
          },
          end: {
            line: 450,
            column: 73
          }
        },
        line: 450
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 456,
            column: 2
          },
          end: {
            line: 456,
            column: 3
          }
        },
        loc: {
          start: {
            line: 456,
            column: 75
          },
          end: {
            line: 466,
            column: 3
          }
        },
        line: 456
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 459,
            column: 46
          },
          end: {
            line: 459,
            column: 47
          }
        },
        loc: {
          start: {
            line: 459,
            column: 51
          },
          end: {
            line: 459,
            column: 94
          }
        },
        line: 459
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 462,
            column: 41
          },
          end: {
            line: 462,
            column: 42
          }
        },
        loc: {
          start: {
            line: 462,
            column: 46
          },
          end: {
            line: 462,
            column: 97
          }
        },
        line: 462
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 468,
            column: 2
          },
          end: {
            line: 468,
            column: 3
          }
        },
        loc: {
          start: {
            line: 471,
            column: 14
          },
          end: {
            line: 483,
            column: 3
          }
        },
        line: 471
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 485,
            column: 2
          },
          end: {
            line: 485,
            column: 3
          }
        },
        loc: {
          start: {
            line: 485,
            column: 88
          },
          end: {
            line: 491,
            column: 3
          }
        },
        line: 485
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 493,
            column: 2
          },
          end: {
            line: 493,
            column: 3
          }
        },
        loc: {
          start: {
            line: 493,
            column: 74
          },
          end: {
            line: 497,
            column: 3
          }
        },
        line: 493
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 495,
            column: 50
          },
          end: {
            line: 495,
            column: 51
          }
        },
        loc: {
          start: {
            line: 495,
            column: 55
          },
          end: {
            line: 495,
            column: 102
          }
        },
        line: 495
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 499,
            column: 2
          },
          end: {
            line: 499,
            column: 3
          }
        },
        loc: {
          start: {
            line: 499,
            column: 77
          },
          end: {
            line: 503,
            column: 3
          }
        },
        line: 499
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 501,
            column: 42
          },
          end: {
            line: 501,
            column: 43
          }
        },
        loc: {
          start: {
            line: 501,
            column: 54
          },
          end: {
            line: 501,
            column: 72
          }
        },
        line: 501
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 505,
            column: 2
          },
          end: {
            line: 505,
            column: 3
          }
        },
        loc: {
          start: {
            line: 505,
            column: 86
          },
          end: {
            line: 519,
            column: 3
          }
        },
        line: 505
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 521,
            column: 2
          },
          end: {
            line: 521,
            column: 3
          }
        },
        loc: {
          start: {
            line: 521,
            column: 101
          },
          end: {
            line: 532,
            column: 3
          }
        },
        line: 521
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 534,
            column: 2
          },
          end: {
            line: 534,
            column: 3
          }
        },
        loc: {
          start: {
            line: 534,
            column: 70
          },
          end: {
            line: 542,
            column: 3
          }
        },
        line: 534
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 544,
            column: 2
          },
          end: {
            line: 544,
            column: 3
          }
        },
        loc: {
          start: {
            line: 544,
            column: 72
          },
          end: {
            line: 550,
            column: 3
          }
        },
        line: 544
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 552,
            column: 2
          },
          end: {
            line: 552,
            column: 3
          }
        },
        loc: {
          start: {
            line: 552,
            column: 38
          },
          end: {
            line: 554,
            column: 3
          }
        },
        line: 552
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 556,
            column: 2
          },
          end: {
            line: 556,
            column: 3
          }
        },
        loc: {
          start: {
            line: 556,
            column: 48
          },
          end: {
            line: 573,
            column: 3
          }
        },
        line: 556
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 567,
            column: 24
          },
          end: {
            line: 567,
            column: 25
          }
        },
        loc: {
          start: {
            line: 567,
            column: 35
          },
          end: {
            line: 567,
            column: 64
          }
        },
        line: 567
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 92,
            column: 12
          },
          end: {
            line: 92,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 12
          },
          end: {
            line: 92,
            column: 50
          }
        }, {
          start: {
            line: 92,
            column: 54
          },
          end: {
            line: 92,
            column: 56
          }
        }],
        line: 92
      },
      "1": {
        loc: {
          start: {
            line: 156,
            column: 6
          },
          end: {
            line: 156,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 6
          },
          end: {
            line: 156,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "2": {
        loc: {
          start: {
            line: 165,
            column: 6
          },
          end: {
            line: 167,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 6
          },
          end: {
            line: 167,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 165
      },
      "3": {
        loc: {
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 174,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 174,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "4": {
        loc: {
          start: {
            line: 269,
            column: 21
          },
          end: {
            line: 269,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 269,
            column: 21
          },
          end: {
            line: 269,
            column: 41
          }
        }, {
          start: {
            line: 269,
            column: 45
          },
          end: {
            line: 269,
            column: 101
          }
        }],
        line: 269
      },
      "5": {
        loc: {
          start: {
            line: 270,
            column: 25
          },
          end: {
            line: 270,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 270,
            column: 25
          },
          end: {
            line: 270,
            column: 49
          }
        }, {
          start: {
            line: 270,
            column: 53
          },
          end: {
            line: 270,
            column: 55
          }
        }],
        line: 270
      },
      "6": {
        loc: {
          start: {
            line: 342,
            column: 19
          },
          end: {
            line: 342,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 342,
            column: 19
          },
          end: {
            line: 342,
            column: 39
          }
        }, {
          start: {
            line: 342,
            column: 43
          },
          end: {
            line: 342,
            column: 83
          }
        }],
        line: 342
      },
      "7": {
        loc: {
          start: {
            line: 343,
            column: 23
          },
          end: {
            line: 343,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 343,
            column: 23
          },
          end: {
            line: 343,
            column: 47
          }
        }, {
          start: {
            line: 343,
            column: 51
          },
          end: {
            line: 343,
            column: 53
          }
        }],
        line: 343
      },
      "8": {
        loc: {
          start: {
            line: 344,
            column: 22
          },
          end: {
            line: 344,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 344,
            column: 22
          },
          end: {
            line: 344,
            column: 45
          }
        }, {
          start: {
            line: 344,
            column: 49
          },
          end: {
            line: 344,
            column: 51
          }
        }],
        line: 344
      },
      "9": {
        loc: {
          start: {
            line: 345,
            column: 27
          },
          end: {
            line: 345,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 345,
            column: 27
          },
          end: {
            line: 345,
            column: 55
          }
        }, {
          start: {
            line: 345,
            column: 59
          },
          end: {
            line: 345,
            column: 89
          }
        }],
        line: 345
      },
      "10": {
        loc: {
          start: {
            line: 346,
            column: 17
          },
          end: {
            line: 346,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 346,
            column: 17
          },
          end: {
            line: 346,
            column: 35
          }
        }, {
          start: {
            line: 346,
            column: 39
          },
          end: {
            line: 346,
            column: 50
          }
        }],
        line: 346
      },
      "11": {
        loc: {
          start: {
            line: 408,
            column: 6
          },
          end: {
            line: 408,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 408,
            column: 6
          },
          end: {
            line: 408,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 408
      },
      "12": {
        loc: {
          start: {
            line: 418,
            column: 4
          },
          end: {
            line: 418,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 418,
            column: 4
          },
          end: {
            line: 418,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 418
      },
      "13": {
        loc: {
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 421,
            column: 98
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 421,
            column: 98
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 421
      },
      "14": {
        loc: {
          start: {
            line: 448,
            column: 4
          },
          end: {
            line: 448,
            column: 78
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 448,
            column: 4
          },
          end: {
            line: 448,
            column: 78
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 448
      },
      "15": {
        loc: {
          start: {
            line: 451,
            column: 4
          },
          end: {
            line: 451,
            column: 95
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 451,
            column: 4
          },
          end: {
            line: 451,
            column: 95
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 451
      },
      "16": {
        loc: {
          start: {
            line: 460,
            column: 4
          },
          end: {
            line: 460,
            column: 98
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 460,
            column: 4
          },
          end: {
            line: 460,
            column: 98
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 460
      },
      "17": {
        loc: {
          start: {
            line: 463,
            column: 4
          },
          end: {
            line: 463,
            column: 92
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 463,
            column: 4
          },
          end: {
            line: 463,
            column: 92
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 463
      },
      "18": {
        loc: {
          start: {
            line: 474,
            column: 4
          },
          end: {
            line: 480,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 474,
            column: 4
          },
          end: {
            line: 480,
            column: 5
          }
        }, {
          start: {
            line: 477,
            column: 11
          },
          end: {
            line: 480,
            column: 5
          }
        }],
        line: 474
      },
      "19": {
        loc: {
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 488,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 488,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 486
      },
      "20": {
        loc: {
          start: {
            line: 512,
            column: 4
          },
          end: {
            line: 516,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 512,
            column: 4
          },
          end: {
            line: 516,
            column: 5
          }
        }, {
          start: {
            line: 514,
            column: 11
          },
          end: {
            line: 516,
            column: 5
          }
        }],
        line: 512
      },
      "21": {
        loc: {
          start: {
            line: 527,
            column: 4
          },
          end: {
            line: 529,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 527,
            column: 4
          },
          end: {
            line: 529,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 527
      },
      "22": {
        loc: {
          start: {
            line: 536,
            column: 4
          },
          end: {
            line: 540,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 536,
            column: 4
          },
          end: {
            line: 540,
            column: 5
          }
        }, {
          start: {
            line: 538,
            column: 11
          },
          end: {
            line: 540,
            column: 5
          }
        }],
        line: 536
      },
      "23": {
        loc: {
          start: {
            line: 536,
            column: 8
          },
          end: {
            line: 536,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 536,
            column: 8
          },
          end: {
            line: 536,
            column: 50
          }
        }, {
          start: {
            line: 536,
            column: 54
          },
          end: {
            line: 536,
            column: 99
          }
        }],
        line: 536
      },
      "24": {
        loc: {
          start: {
            line: 538,
            column: 11
          },
          end: {
            line: 540,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 538,
            column: 11
          },
          end: {
            line: 540,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 538
      },
      "25": {
        loc: {
          start: {
            line: 538,
            column: 15
          },
          end: {
            line: 538,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 538,
            column: 15
          },
          end: {
            line: 538,
            column: 58
          }
        }, {
          start: {
            line: 538,
            column: 62
          },
          end: {
            line: 538,
            column: 107
          }
        }],
        line: 538
      },
      "26": {
        loc: {
          start: {
            line: 545,
            column: 4
          },
          end: {
            line: 549,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 546,
            column: 6
          },
          end: {
            line: 546,
            column: 28
          }
        }, {
          start: {
            line: 547,
            column: 6
          },
          end: {
            line: 547,
            column: 30
          }
        }, {
          start: {
            line: 548,
            column: 6
          },
          end: {
            line: 548,
            column: 27
          }
        }],
        line: 545
      },
      "27": {
        loc: {
          start: {
            line: 560,
            column: 4
          },
          end: {
            line: 563,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 560,
            column: 4
          },
          end: {
            line: 563,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 560
      },
      "28": {
        loc: {
          start: {
            line: 565,
            column: 4
          },
          end: {
            line: 570,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 565,
            column: 4
          },
          end: {
            line: 570,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 565
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "25de4fa9d4d6e39245b038132bc66bf21ce50b2f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2ala20ag7y = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2ala20ag7y();
import { performanceMonitor } from "../../../utils/performance";
var CoachingAnalysisService = function () {
  function CoachingAnalysisService() {
    _classCallCheck(this, CoachingAnalysisService);
    this.config = (cov_2ala20ag7y().s[0]++, {
      apiKey: (cov_2ala20ag7y().b[0][0]++, _env.EXPO_PUBLIC_OPENAI_API_KEY) || (cov_2ala20ag7y().b[0][1]++, ''),
      model: 'gpt-4',
      maxTokens: 2000,
      temperature: 0.7,
      rateLimitPerMinute: 20
    });
    this.requestCount = (cov_2ala20ag7y().s[1]++, 0);
    this.lastResetTime = (cov_2ala20ag7y().s[2]++, Date.now());
  }
  return _createClass(CoachingAnalysisService, [{
    key: "generateCoachingAnalysis",
    value: (function () {
      var _generateCoachingAnalysis = _asyncToGenerator(function* (request) {
        cov_2ala20ag7y().f[0]++;
        cov_2ala20ag7y().s[3]++;
        try {
          cov_2ala20ag7y().s[4]++;
          performanceMonitor.start('coaching_analysis');
          cov_2ala20ag7y().s[5]++;
          yield this.checkRateLimit();
          var insights = (cov_2ala20ag7y().s[6]++, yield this.generateInsights(request));
          var overallAssessment = (cov_2ala20ag7y().s[7]++, this.createOverallAssessment(request.movementAnalyses, request.playerProfile));
          var technicalMetrics = (cov_2ala20ag7y().s[8]++, this.calculateTechnicalMetrics(request.movementAnalyses));
          var nextSteps = (cov_2ala20ag7y().s[9]++, yield this.generateNextSteps(request, insights));
          var result = (cov_2ala20ag7y().s[10]++, {
            insights: insights,
            overallAssessment: overallAssessment,
            technicalMetrics: technicalMetrics,
            nextSteps: nextSteps
          });
          cov_2ala20ag7y().s[11]++;
          performanceMonitor.end('coaching_analysis');
          cov_2ala20ag7y().s[12]++;
          return result;
        } catch (error) {
          cov_2ala20ag7y().s[13]++;
          console.error('Coaching analysis failed:', error);
          cov_2ala20ag7y().s[14]++;
          throw new Error('Failed to generate coaching analysis');
        }
      });
      function generateCoachingAnalysis(_x) {
        return _generateCoachingAnalysis.apply(this, arguments);
      }
      return generateCoachingAnalysis;
    }())
  }, {
    key: "generateInsights",
    value: (function () {
      var _generateInsights = _asyncToGenerator(function* (request) {
        var _this = this;
        cov_2ala20ag7y().f[1]++;
        var insights = (cov_2ala20ag7y().s[15]++, []);
        var movementGroups = (cov_2ala20ag7y().s[16]++, this.groupMovementsByType(request.movementAnalyses));
        cov_2ala20ag7y().s[17]++;
        for (var _ref of Object.entries(movementGroups)) {
          var _ref2 = _slicedToArray(_ref, 2);
          var movementType = _ref2[0];
          var analyses = _ref2[1];
          cov_2ala20ag7y().s[18]++;
          if (analyses.length === 0) {
            cov_2ala20ag7y().b[1][0]++;
            cov_2ala20ag7y().s[19]++;
            continue;
          } else {
            cov_2ala20ag7y().b[1][1]++;
          }
          var insight = (cov_2ala20ag7y().s[20]++, yield this.analyzeMovementType(movementType, analyses, request.playerProfile, request.matchContext));
          cov_2ala20ag7y().s[21]++;
          if (insight) {
            cov_2ala20ag7y().b[2][0]++;
            cov_2ala20ag7y().s[22]++;
            insights.push(insight);
          } else {
            cov_2ala20ag7y().b[2][1]++;
          }
        }
        var strategicInsight = (cov_2ala20ag7y().s[23]++, yield this.generateStrategicInsight(request));
        cov_2ala20ag7y().s[24]++;
        if (strategicInsight) {
          cov_2ala20ag7y().b[3][0]++;
          cov_2ala20ag7y().s[25]++;
          insights.push(strategicInsight);
        } else {
          cov_2ala20ag7y().b[3][1]++;
        }
        cov_2ala20ag7y().s[26]++;
        return insights.sort(function (a, b) {
          cov_2ala20ag7y().f[2]++;
          cov_2ala20ag7y().s[27]++;
          return _this.getPriorityScore(b.priority) - _this.getPriorityScore(a.priority);
        });
      });
      function generateInsights(_x2) {
        return _generateInsights.apply(this, arguments);
      }
      return generateInsights;
    }())
  }, {
    key: "analyzeMovementType",
    value: (function () {
      var _analyzeMovementType = _asyncToGenerator(function* (movementType, analyses, playerProfile, matchContext) {
        cov_2ala20ag7y().f[3]++;
        cov_2ala20ag7y().s[28]++;
        try {
          var prompt = (cov_2ala20ag7y().s[29]++, this.createTechnicalAnalysisPrompt(movementType, analyses, playerProfile, matchContext));
          var response = (cov_2ala20ag7y().s[30]++, yield this.callOpenAI(prompt));
          var insight = (cov_2ala20ag7y().s[31]++, this.parseCoachingResponse(response, movementType));
          cov_2ala20ag7y().s[32]++;
          return insight;
        } catch (error) {
          cov_2ala20ag7y().s[33]++;
          console.error(`Failed to analyze ${movementType}:`, error);
          cov_2ala20ag7y().s[34]++;
          return null;
        }
      });
      function analyzeMovementType(_x3, _x4, _x5, _x6) {
        return _analyzeMovementType.apply(this, arguments);
      }
      return analyzeMovementType;
    }())
  }, {
    key: "createTechnicalAnalysisPrompt",
    value: function createTechnicalAnalysisPrompt(movementType, analyses, playerProfile, matchContext) {
      cov_2ala20ag7y().f[4]++;
      var avgConfidence = (cov_2ala20ag7y().s[35]++, analyses.reduce(function (sum, a) {
        cov_2ala20ag7y().f[5]++;
        cov_2ala20ag7y().s[36]++;
        return sum + a.confidence;
      }, 0) / analyses.length);
      var commonIssues = (cov_2ala20ag7y().s[37]++, this.identifyCommonIssues(analyses));
      cov_2ala20ag7y().s[38]++;
      return `
As a professional tennis coach, analyze this ${movementType} technique data:

Player Profile:
- Skill Level: ${playerProfile.skillLevel}
- Dominant Hand: ${playerProfile.dominantHand}
- Playing Style: ${playerProfile.playingStyle}
- Known Weaknesses: ${playerProfile.weaknesses.join(', ')}
- Goals: ${playerProfile.goals.join(', ')}

Movement Analysis (${analyses.length} samples):
- Average Confidence: ${(avgConfidence * 100).toFixed(1)}%
- Common Technical Issues: ${commonIssues.join(', ')}
- Body Position Patterns: ${this.summarizeBodyPositions(analyses)}
- Technical Metrics: ${this.summarizeTechnicalMetrics(analyses)}

Match Context:
- Surface: ${matchContext.surface}
- Match Type: ${matchContext.matchType}
- Current Situation: ${matchContext.matchSituation}

Please provide:
1. Primary technical issue to address
2. 3-4 specific actionable steps for improvement
3. Expected timeline for improvement
4. Practice drills recommendation
5. Mental/strategic considerations

Focus on practical, implementable advice suitable for ${playerProfile.skillLevel} level.
`;
    }
  }, {
    key: "generateStrategicInsight",
    value: (function () {
      var _generateStrategicInsight = _asyncToGenerator(function* (request) {
        cov_2ala20ag7y().f[6]++;
        cov_2ala20ag7y().s[39]++;
        try {
          var prompt = (cov_2ala20ag7y().s[40]++, this.createStrategicAnalysisPrompt(request));
          var response = (cov_2ala20ag7y().s[41]++, yield this.callOpenAI(prompt));
          cov_2ala20ag7y().s[42]++;
          return {
            id: this.generateInsightId(),
            category: 'strategy',
            priority: 'medium',
            title: 'Match Strategy Recommendations',
            description: (cov_2ala20ag7y().b[4][0]++, response.description) || (cov_2ala20ag7y().b[4][1]++, 'Strategic recommendations based on current performance'),
            actionableSteps: (cov_2ala20ag7y().b[5][0]++, response.actionableSteps) || (cov_2ala20ag7y().b[5][1]++, []),
            technicalFocus: ['match_tactics', 'point_construction'],
            expectedImprovement: 'Improved match performance and tactical awareness',
            timeframe: '2-4 weeks',
            confidence: 0.8,
            relatedMovements: ['all'],
            createdAt: new Date().toISOString()
          };
        } catch (error) {
          cov_2ala20ag7y().s[43]++;
          console.error('Failed to generate strategic insight:', error);
          cov_2ala20ag7y().s[44]++;
          return null;
        }
      });
      function generateStrategicInsight(_x7) {
        return _generateStrategicInsight.apply(this, arguments);
      }
      return generateStrategicInsight;
    }())
  }, {
    key: "createStrategicAnalysisPrompt",
    value: function createStrategicAnalysisPrompt(request) {
      cov_2ala20ag7y().f[7]++;
      var movementTypes = (cov_2ala20ag7y().s[45]++, _toConsumableArray(new Set(request.movementAnalyses.map(function (a) {
        cov_2ala20ag7y().f[8]++;
        cov_2ala20ag7y().s[46]++;
        return a.movementType;
      }))));
      cov_2ala20ag7y().s[47]++;
      return `
As a tennis strategy coach, analyze this match performance:

Player: ${request.playerProfile.skillLevel} level, ${request.playerProfile.playingStyle} style
Match Context: ${request.matchContext.matchType} on ${request.matchContext.surface}
Current Score: ${request.matchContext.score.userGames}-${request.matchContext.score.opponent}

Observed Movements: ${movementTypes.join(', ')}
Match Situation: ${request.matchContext.matchSituation}

Provide strategic recommendations for:
1. Point construction patterns
2. Opponent exploitation opportunities  
3. Risk/reward shot selection
4. Court positioning tactics
5. Mental approach adjustments

Keep advice practical and match-situation specific.
`;
    }
  }, {
    key: "callOpenAI",
    value: (function () {
      var _callOpenAI = _asyncToGenerator(function* (prompt) {
        cov_2ala20ag7y().f[9]++;
        cov_2ala20ag7y().s[48]++;
        yield new Promise(function (resolve) {
          cov_2ala20ag7y().f[10]++;
          cov_2ala20ag7y().s[49]++;
          return setTimeout(resolve, 1000);
        });
        cov_2ala20ag7y().s[50]++;
        return {
          description: 'Focus on improving follow-through consistency and body rotation timing.',
          actionableSteps: ['Practice shadow swings with emphasis on complete follow-through', 'Work on hip rotation timing during groundstrokes', 'Use video feedback to monitor racket path consistency', 'Incorporate balance drills into practice routine'],
          technicalFocus: ['follow_through', 'body_rotation', 'balance'],
          expectedImprovement: 'Increased shot consistency and power generation',
          timeframe: '3-4 weeks with regular practice'
        };
      });
      function callOpenAI(_x8) {
        return _callOpenAI.apply(this, arguments);
      }
      return callOpenAI;
    }())
  }, {
    key: "parseCoachingResponse",
    value: function parseCoachingResponse(response, movementType) {
      cov_2ala20ag7y().f[11]++;
      cov_2ala20ag7y().s[51]++;
      return {
        id: this.generateInsightId(),
        category: 'technique',
        priority: this.determinePriority(response),
        title: `${movementType.charAt(0).toUpperCase() + movementType.slice(1)} Technique Analysis`,
        description: (cov_2ala20ag7y().b[6][0]++, response.description) || (cov_2ala20ag7y().b[6][1]++, 'Technical analysis and recommendations'),
        actionableSteps: (cov_2ala20ag7y().b[7][0]++, response.actionableSteps) || (cov_2ala20ag7y().b[7][1]++, []),
        technicalFocus: (cov_2ala20ag7y().b[8][0]++, response.technicalFocus) || (cov_2ala20ag7y().b[8][1]++, []),
        expectedImprovement: (cov_2ala20ag7y().b[9][0]++, response.expectedImprovement) || (cov_2ala20ag7y().b[9][1]++, 'Improved technique execution'),
        timeframe: (cov_2ala20ag7y().b[10][0]++, response.timeframe) || (cov_2ala20ag7y().b[10][1]++, '2-3 weeks'),
        confidence: 0.85,
        relatedMovements: [movementType],
        createdAt: new Date().toISOString()
      };
    }
  }, {
    key: "createOverallAssessment",
    value: function createOverallAssessment(analyses, playerProfile) {
      cov_2ala20ag7y().f[12]++;
      var avgConfidence = (cov_2ala20ag7y().s[52]++, analyses.reduce(function (sum, a) {
        cov_2ala20ag7y().f[13]++;
        cov_2ala20ag7y().s[53]++;
        return sum + a.confidence;
      }, 0) / analyses.length);
      var movementTypes = (cov_2ala20ag7y().s[54]++, _toConsumableArray(new Set(analyses.map(function (a) {
        cov_2ala20ag7y().f[14]++;
        cov_2ala20ag7y().s[55]++;
        return a.movementType;
      }))));
      cov_2ala20ag7y().s[56]++;
      return {
        strengths: this.identifyStrengths(analyses),
        areasForImprovement: this.identifyWeaknesses(analyses),
        keyRecommendations: this.generateKeyRecommendations(analyses, playerProfile),
        progressFromLastAnalysis: this.compareWithPreviousAnalysis(playerProfile)
      };
    }
  }, {
    key: "calculateTechnicalMetrics",
    value: function calculateTechnicalMetrics(analyses) {
      cov_2ala20ag7y().f[15]++;
      var avgConfidence = (cov_2ala20ag7y().s[57]++, analyses.reduce(function (sum, a) {
        cov_2ala20ag7y().f[16]++;
        cov_2ala20ag7y().s[58]++;
        return sum + a.confidence;
      }, 0) / analyses.length);
      var avgBalance = (cov_2ala20ag7y().s[59]++, analyses.reduce(function (sum, a) {
        cov_2ala20ag7y().f[17]++;
        cov_2ala20ag7y().s[60]++;
        return sum + a.bodyPosition.balance;
      }, 0) / analyses.length);
      cov_2ala20ag7y().s[61]++;
      return {
        techniqueScore: Math.round(avgConfidence * 100),
        consistencyScore: Math.round(avgBalance * 100),
        powerScore: this.calculatePowerScore(analyses),
        accuracyScore: this.calculateAccuracyScore(analyses)
      };
    }
  }, {
    key: "generateNextSteps",
    value: (function () {
      var _generateNextSteps = _asyncToGenerator(function* (request, insights) {
        cov_2ala20ag7y().f[18]++;
        var highPriorityInsights = (cov_2ala20ag7y().s[62]++, insights.filter(function (i) {
          cov_2ala20ag7y().f[19]++;
          cov_2ala20ag7y().s[63]++;
          return i.priority === 'high';
        }));
        cov_2ala20ag7y().s[64]++;
        return {
          immediateActions: highPriorityInsights.flatMap(function (i) {
            cov_2ala20ag7y().f[20]++;
            cov_2ala20ag7y().s[65]++;
            return i.actionableSteps.slice(0, 2);
          }),
          practiceRecommendations: this.generatePracticeRecommendations(request),
          longTermGoals: this.generateLongTermGoals(request.playerProfile, insights)
        };
      });
      function generateNextSteps(_x9, _x0) {
        return _generateNextSteps.apply(this, arguments);
      }
      return generateNextSteps;
    }())
  }, {
    key: "groupMovementsByType",
    value: function groupMovementsByType(analyses) {
      cov_2ala20ag7y().f[21]++;
      cov_2ala20ag7y().s[66]++;
      return analyses.reduce(function (groups, analysis) {
        cov_2ala20ag7y().f[22]++;
        var type = (cov_2ala20ag7y().s[67]++, analysis.movementType);
        cov_2ala20ag7y().s[68]++;
        if (!groups[type]) {
          cov_2ala20ag7y().b[11][0]++;
          cov_2ala20ag7y().s[69]++;
          groups[type] = [];
        } else {
          cov_2ala20ag7y().b[11][1]++;
        }
        cov_2ala20ag7y().s[70]++;
        groups[type].push(analysis);
        cov_2ala20ag7y().s[71]++;
        return groups;
      }, {});
    }
  }, {
    key: "identifyCommonIssues",
    value: function identifyCommonIssues(analyses) {
      cov_2ala20ag7y().f[23]++;
      var issues = (cov_2ala20ag7y().s[72]++, []);
      var avgBalance = (cov_2ala20ag7y().s[73]++, analyses.reduce(function (sum, a) {
        cov_2ala20ag7y().f[24]++;
        cov_2ala20ag7y().s[74]++;
        return sum + a.bodyPosition.balance;
      }, 0) / analyses.length);
      cov_2ala20ag7y().s[75]++;
      if (avgBalance < 0.7) {
        cov_2ala20ag7y().b[12][0]++;
        cov_2ala20ag7y().s[76]++;
        issues.push('balance_issues');
      } else {
        cov_2ala20ag7y().b[12][1]++;
      }
      var incompleteFollowThrough = (cov_2ala20ag7y().s[77]++, analyses.filter(function (a) {
        cov_2ala20ag7y().f[25]++;
        cov_2ala20ag7y().s[78]++;
        return a.technicalMetrics.followThrough !== 'complete';
      }).length);
      cov_2ala20ag7y().s[79]++;
      if (incompleteFollowThrough > analyses.length * 0.5) {
        cov_2ala20ag7y().b[13][0]++;
        cov_2ala20ag7y().s[80]++;
        issues.push('incomplete_follow_through');
      } else {
        cov_2ala20ag7y().b[13][1]++;
      }
      cov_2ala20ag7y().s[81]++;
      return issues;
    }
  }, {
    key: "summarizeBodyPositions",
    value: function summarizeBodyPositions(analyses) {
      cov_2ala20ag7y().f[26]++;
      var stances = (cov_2ala20ag7y().s[82]++, analyses.map(function (a) {
        cov_2ala20ag7y().f[27]++;
        cov_2ala20ag7y().s[83]++;
        return a.bodyPosition.stance;
      }));
      var mostCommon = (cov_2ala20ag7y().s[84]++, this.getMostCommon(stances));
      cov_2ala20ag7y().s[85]++;
      return `Predominantly ${mostCommon} stance`;
    }
  }, {
    key: "summarizeTechnicalMetrics",
    value: function summarizeTechnicalMetrics(analyses) {
      cov_2ala20ag7y().f[28]++;
      var followThrough = (cov_2ala20ag7y().s[86]++, analyses.filter(function (a) {
        cov_2ala20ag7y().f[29]++;
        cov_2ala20ag7y().s[87]++;
        return a.technicalMetrics.followThrough === 'complete';
      }).length);
      var percentage = (cov_2ala20ag7y().s[88]++, Math.round(followThrough / analyses.length * 100));
      cov_2ala20ag7y().s[89]++;
      return `${percentage}% complete follow-through`;
    }
  }, {
    key: "getMostCommon",
    value: function getMostCommon(arr) {
      cov_2ala20ag7y().f[30]++;
      cov_2ala20ag7y().s[90]++;
      return arr.sort(function (a, b) {
        cov_2ala20ag7y().f[31]++;
        cov_2ala20ag7y().s[91]++;
        return arr.filter(function (v) {
          cov_2ala20ag7y().f[32]++;
          cov_2ala20ag7y().s[92]++;
          return v === a;
        }).length - arr.filter(function (v) {
          cov_2ala20ag7y().f[33]++;
          cov_2ala20ag7y().s[93]++;
          return v === b;
        }).length;
      }).pop();
    }
  }, {
    key: "identifyStrengths",
    value: function identifyStrengths(analyses) {
      cov_2ala20ag7y().f[34]++;
      var strengths = (cov_2ala20ag7y().s[94]++, []);
      var avgConfidence = (cov_2ala20ag7y().s[95]++, analyses.reduce(function (sum, a) {
        cov_2ala20ag7y().f[35]++;
        cov_2ala20ag7y().s[96]++;
        return sum + a.confidence;
      }, 0) / analyses.length);
      cov_2ala20ag7y().s[97]++;
      if (avgConfidence > 0.8) {
        cov_2ala20ag7y().b[14][0]++;
        cov_2ala20ag7y().s[98]++;
        strengths.push('Consistent technique execution');
      } else {
        cov_2ala20ag7y().b[14][1]++;
      }
      var goodBalance = (cov_2ala20ag7y().s[99]++, analyses.filter(function (a) {
        cov_2ala20ag7y().f[36]++;
        cov_2ala20ag7y().s[100]++;
        return a.bodyPosition.balance > 0.8;
      }).length);
      cov_2ala20ag7y().s[101]++;
      if (goodBalance > analyses.length * 0.7) {
        cov_2ala20ag7y().b[15][0]++;
        cov_2ala20ag7y().s[102]++;
        strengths.push('Excellent balance and stability');
      } else {
        cov_2ala20ag7y().b[15][1]++;
      }
      cov_2ala20ag7y().s[103]++;
      return strengths;
    }
  }, {
    key: "identifyWeaknesses",
    value: function identifyWeaknesses(analyses) {
      cov_2ala20ag7y().f[37]++;
      var weaknesses = (cov_2ala20ag7y().s[104]++, []);
      var poorFollowThrough = (cov_2ala20ag7y().s[105]++, analyses.filter(function (a) {
        cov_2ala20ag7y().f[38]++;
        cov_2ala20ag7y().s[106]++;
        return a.technicalMetrics.followThrough === 'none';
      }).length);
      cov_2ala20ag7y().s[107]++;
      if (poorFollowThrough > analyses.length * 0.3) {
        cov_2ala20ag7y().b[16][0]++;
        cov_2ala20ag7y().s[108]++;
        weaknesses.push('Inconsistent follow-through');
      } else {
        cov_2ala20ag7y().b[16][1]++;
      }
      var poorFootwork = (cov_2ala20ag7y().s[109]++, analyses.filter(function (a) {
        cov_2ala20ag7y().f[39]++;
        cov_2ala20ag7y().s[110]++;
        return a.technicalMetrics.footwork === 'needs_improvement';
      }).length);
      cov_2ala20ag7y().s[111]++;
      if (poorFootwork > analyses.length * 0.4) {
        cov_2ala20ag7y().b[17][0]++;
        cov_2ala20ag7y().s[112]++;
        weaknesses.push('Footwork needs improvement');
      } else {
        cov_2ala20ag7y().b[17][1]++;
      }
      cov_2ala20ag7y().s[113]++;
      return weaknesses;
    }
  }, {
    key: "generateKeyRecommendations",
    value: function generateKeyRecommendations(analyses, playerProfile) {
      cov_2ala20ag7y().f[40]++;
      var recommendations = (cov_2ala20ag7y().s[114]++, []);
      cov_2ala20ag7y().s[115]++;
      if (playerProfile.skillLevel === 'beginner') {
        cov_2ala20ag7y().b[18][0]++;
        cov_2ala20ag7y().s[116]++;
        recommendations.push('Focus on fundamental stroke mechanics');
        cov_2ala20ag7y().s[117]++;
        recommendations.push('Develop consistent contact point');
      } else {
        cov_2ala20ag7y().b[18][1]++;
        cov_2ala20ag7y().s[118]++;
        recommendations.push('Refine timing and rhythm');
        cov_2ala20ag7y().s[119]++;
        recommendations.push('Work on shot selection and placement');
      }
      cov_2ala20ag7y().s[120]++;
      return recommendations;
    }
  }, {
    key: "compareWithPreviousAnalysis",
    value: function compareWithPreviousAnalysis(playerProfile) {
      cov_2ala20ag7y().f[41]++;
      cov_2ala20ag7y().s[121]++;
      if (playerProfile.previousAnalyses.length === 0) {
        cov_2ala20ag7y().b[19][0]++;
        cov_2ala20ag7y().s[122]++;
        return undefined;
      } else {
        cov_2ala20ag7y().b[19][1]++;
      }
      cov_2ala20ag7y().s[123]++;
      return 'Showing improvement in consistency compared to previous analysis';
    }
  }, {
    key: "calculatePowerScore",
    value: function calculatePowerScore(analyses) {
      cov_2ala20ag7y().f[42]++;
      var completeFollowThrough = (cov_2ala20ag7y().s[124]++, analyses.filter(function (a) {
        cov_2ala20ag7y().f[43]++;
        cov_2ala20ag7y().s[125]++;
        return a.technicalMetrics.followThrough === 'complete';
      }).length);
      cov_2ala20ag7y().s[126]++;
      return Math.round(completeFollowThrough / analyses.length * 100);
    }
  }, {
    key: "calculateAccuracyScore",
    value: function calculateAccuracyScore(analyses) {
      cov_2ala20ag7y().f[44]++;
      var avgConfidence = (cov_2ala20ag7y().s[127]++, analyses.reduce(function (sum, a) {
        cov_2ala20ag7y().f[45]++;
        cov_2ala20ag7y().s[128]++;
        return sum + a.confidence;
      }, 0) / analyses.length);
      cov_2ala20ag7y().s[129]++;
      return Math.round(avgConfidence * 100);
    }
  }, {
    key: "generatePracticeRecommendations",
    value: function generatePracticeRecommendations(request) {
      cov_2ala20ag7y().f[46]++;
      var recommendations = (cov_2ala20ag7y().s[130]++, ['Practice shadow swings focusing on identified technical issues', 'Use video analysis for immediate feedback', 'Work on specific movement patterns during warm-up']);
      cov_2ala20ag7y().s[131]++;
      if (request.playerProfile.skillLevel === 'beginner') {
        cov_2ala20ag7y().b[20][0]++;
        cov_2ala20ag7y().s[132]++;
        recommendations.push('Focus on basic stroke fundamentals');
      } else {
        cov_2ala20ag7y().b[20][1]++;
        cov_2ala20ag7y().s[133]++;
        recommendations.push('Incorporate match-play scenarios in practice');
      }
      cov_2ala20ag7y().s[134]++;
      return recommendations;
    }
  }, {
    key: "generateLongTermGoals",
    value: function generateLongTermGoals(playerProfile, insights) {
      cov_2ala20ag7y().f[47]++;
      var goals = (cov_2ala20ag7y().s[135]++, ['Achieve consistent technique execution across all strokes', 'Develop tactical awareness and shot selection']);
      cov_2ala20ag7y().s[136]++;
      if (playerProfile.skillLevel !== 'professional') {
        cov_2ala20ag7y().b[21][0]++;
        cov_2ala20ag7y().s[137]++;
        goals.push('Progress to next skill level');
      } else {
        cov_2ala20ag7y().b[21][1]++;
      }
      cov_2ala20ag7y().s[138]++;
      return goals;
    }
  }, {
    key: "determinePriority",
    value: function determinePriority(response) {
      var _response$description, _response$description2;
      cov_2ala20ag7y().f[48]++;
      cov_2ala20ag7y().s[139]++;
      if ((cov_2ala20ag7y().b[23][0]++, (_response$description = response.description) != null && _response$description.includes('critical')) || (cov_2ala20ag7y().b[23][1]++, (_response$description2 = response.description) != null && _response$description2.includes('fundamental'))) {
        cov_2ala20ag7y().b[22][0]++;
        cov_2ala20ag7y().s[140]++;
        return 'high';
      } else {
        var _response$description3, _response$description4;
        cov_2ala20ag7y().b[22][1]++;
        cov_2ala20ag7y().s[141]++;
        if ((cov_2ala20ag7y().b[25][0]++, (_response$description3 = response.description) != null && _response$description3.includes('important')) || (cov_2ala20ag7y().b[25][1]++, (_response$description4 = response.description) != null && _response$description4.includes('significant'))) {
          cov_2ala20ag7y().b[24][0]++;
          cov_2ala20ag7y().s[142]++;
          return 'medium';
        } else {
          cov_2ala20ag7y().b[24][1]++;
        }
      }
      cov_2ala20ag7y().s[143]++;
      return 'low';
    }
  }, {
    key: "getPriorityScore",
    value: function getPriorityScore(priority) {
      cov_2ala20ag7y().f[49]++;
      cov_2ala20ag7y().s[144]++;
      switch (priority) {
        case 'high':
          cov_2ala20ag7y().b[26][0]++;
          cov_2ala20ag7y().s[145]++;
          return 3;
        case 'medium':
          cov_2ala20ag7y().b[26][1]++;
          cov_2ala20ag7y().s[146]++;
          return 2;
        case 'low':
          cov_2ala20ag7y().b[26][2]++;
          cov_2ala20ag7y().s[147]++;
          return 1;
      }
    }
  }, {
    key: "generateInsightId",
    value: function generateInsightId() {
      cov_2ala20ag7y().f[50]++;
      cov_2ala20ag7y().s[148]++;
      return `insight_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "checkRateLimit",
    value: function () {
      var _checkRateLimit = _asyncToGenerator(function* () {
        cov_2ala20ag7y().f[51]++;
        var now = (cov_2ala20ag7y().s[149]++, Date.now());
        var timeSinceReset = (cov_2ala20ag7y().s[150]++, now - this.lastResetTime);
        cov_2ala20ag7y().s[151]++;
        if (timeSinceReset > 60000) {
          cov_2ala20ag7y().b[27][0]++;
          cov_2ala20ag7y().s[152]++;
          this.requestCount = 0;
          cov_2ala20ag7y().s[153]++;
          this.lastResetTime = now;
        } else {
          cov_2ala20ag7y().b[27][1]++;
        }
        cov_2ala20ag7y().s[154]++;
        if (this.requestCount >= this.config.rateLimitPerMinute) {
          cov_2ala20ag7y().b[28][0]++;
          var waitTime = (cov_2ala20ag7y().s[155]++, 60000 - timeSinceReset);
          cov_2ala20ag7y().s[156]++;
          yield new Promise(function (resolve) {
            cov_2ala20ag7y().f[52]++;
            cov_2ala20ag7y().s[157]++;
            return setTimeout(resolve, waitTime);
          });
          cov_2ala20ag7y().s[158]++;
          this.requestCount = 0;
          cov_2ala20ag7y().s[159]++;
          this.lastResetTime = Date.now();
        } else {
          cov_2ala20ag7y().b[28][1]++;
        }
        cov_2ala20ag7y().s[160]++;
        this.requestCount++;
      });
      function checkRateLimit() {
        return _checkRateLimit.apply(this, arguments);
      }
      return checkRateLimit;
    }()
  }]);
}();
export var coachingAnalysisService = (cov_2ala20ag7y().s[161]++, new CoachingAnalysisService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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