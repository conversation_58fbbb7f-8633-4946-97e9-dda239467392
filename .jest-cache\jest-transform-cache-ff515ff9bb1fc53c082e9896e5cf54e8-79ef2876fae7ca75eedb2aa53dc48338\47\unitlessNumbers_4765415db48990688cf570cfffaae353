0ed4673f49b43ca387aa71783fbdefdd
"use strict";

exports.__esModule = true;
exports.default = void 0;
var unitlessNumbers = {
  animationIterationCount: true,
  aspectRatio: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  flex: true,
  flexGrow: true,
  flexOrder: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  fontWeight: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowGap: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnGap: true,
  gridColumnStart: true,
  lineClamp: true,
  opacity: true,
  order: true,
  orphans: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true,
  scale: true,
  scaleX: true,
  scaleY: true,
  scaleZ: true,
  shadowOpacity: true
};
var prefixes = ['ms', 'Moz', 'O', 'Webkit'];
var prefixKey = function prefixKey(prefix, key) {
  return prefix + key.charAt(0).toUpperCase() + key.substring(1);
};
Object.keys(unitlessNumbers).forEach(function (prop) {
  prefixes.forEach(function (prefix) {
    unitlessNumbers[prefixKey(prefix, prop)] = unitlessNumbers[prop];
  });
});
var _default = exports.default = unitlessNumbers;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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