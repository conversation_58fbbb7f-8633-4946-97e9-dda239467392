{"version": 3, "names": ["performanceMonitor", "smartResourceManager", "GlobalCDNManager", "_classCallCheck", "cdnProviders", "cov_4hnbjnpn2", "s", "Map", "regionalEndpoints", "performanceCache", "routingTable", "CDN_PROVIDERS", "id", "name", "baseUrl", "regions", "capabilities", "imageOptimization", "videoStreaming", "edgeFunctions", "realTimeAnalytics", "ddosProtection", "pricing", "bandwidth", "requests", "storage", "performance", "averageLatency", "uptime", "throughput", "f", "initializeCDNManager", "_createClass", "key", "value", "_initializeCDNManager", "_asyncToGenerator", "_this", "for<PERSON>ach", "provider", "set", "discoverRegionalEndpoints", "startPerformanceMonitoring", "optimizeRoutingTable", "console", "log", "error", "apply", "arguments", "_getOptimizedDeliveryUrl", "request", "startTime", "Date", "now", "optimalEndpoint", "selectOptimalEndpoint", "optimizations", "applyContentOptimizations", "optimizedUrl", "generateOptimizedUrl", "fallbackUrls", "generateFallbackUrls", "result", "url", "region", "estimatedLatency", "latency", "cacheStatus", "predictCacheStatus", "processingTime", "trackDatabaseQuery", "getFallbackDeliveryResult", "getOptimizedDeliveryUrl", "_x", "_preloadContentGlobally", "paths", "length", "undefined", "b", "priority", "preloadPromises", "path", "endpoints", "includes", "Array", "from", "values", "flat", "get", "endpoint", "push", "preloadToEndpoint", "Promise", "allSettled", "preloadContentGlobally", "_x2", "_getGlobalPerformanceMetrics", "metrics", "globalUptime", "regionalPerformance", "cdnComparison", "allEndpoints", "reduce", "sum", "ep", "providers", "p", "_ref", "entries", "_ref2", "_slicedToArray", "regionLatency", "regionReliability", "reliability", "_ref3", "_ref4", "cost", "getGlobalPerformanceMetrics", "_optimizeGlobalRouting", "_this2", "testPromises", "map", "testEndpointPerformance", "testResults", "updateRoutingTable", "resourceMetrics", "getCurrentMetrics", "adaptRoutingToResourceConditions", "optimizeGlobalRouting", "_discoverRegionalEndpoints", "Math", "random", "lastTested", "has", "_selectOptimalEndpoint", "_this3", "_scoredEndpoints$", "userRegion", "determineUserRegion", "userLocation", "availableEndpoints", "scoredEndpoints", "score", "type", "recentPerf", "sort", "a", "_x3", "_applyContentOptimizations", "deviceInfo", "connection", "cacheStrategy", "_x4", "_x5", "params", "URLSearchParams", "opt", "toString", "_generateFallbackUrls", "primaryEndpoint", "alternatives", "filter", "slice", "_x6", "_x7", "_predictCacheStatus", "cache<PERSON>ey", "lastAccess", "timeSinceAccess", "_x8", "_x9", "location", "regionMap", "country", "_preloadToEndpoint", "_x0", "_x1", "_x10", "_this4", "setInterval", "monitorCDNPerformance", "_monitorCDNPerformance", "measureEndpointLatency", "_measureEndpointLatency", "_x11", "_testEndpointPerformance", "success", "_x12", "_updateRoutingTable", "_this5", "status", "_ref5", "_x13", "_adaptRoutingToResourceConditions", "network", "battery", "level", "_x14", "_optimizeRoutingTable", "globalCDNManager"], "sources": ["GlobalCDNManager.ts"], "sourcesContent": ["/**\n * Global CDN Manager\n * \n * Manages multiple CDN providers with intelligent routing, global caching,\n * and performance optimization for worldwide content delivery.\n */\n\nimport { performanceMonitor } from '@/utils/performance';\nimport { smartResourceManager } from '@/services/ai/SmartResourceManager';\n\ninterface CDNProvider {\n  id: string;\n  name: string;\n  baseUrl: string;\n  regions: string[];\n  capabilities: {\n    imageOptimization: boolean;\n    videoStreaming: boolean;\n    edgeFunctions: boolean;\n    realTimeAnalytics: boolean;\n    ddosProtection: boolean;\n  };\n  pricing: {\n    bandwidth: number; // per GB\n    requests: number; // per million\n    storage: number; // per GB/month\n  };\n  performance: {\n    averageLatency: number;\n    uptime: number;\n    throughput: number;\n  };\n}\n\ninterface CDNEndpoint {\n  url: string;\n  provider: string;\n  region: string;\n  latency: number;\n  reliability: number;\n  lastTested: number;\n}\n\ninterface ContentDeliveryRequest {\n  path: string;\n  type: 'image' | 'video' | 'api' | 'static' | 'dynamic';\n  priority: 'high' | 'medium' | 'low';\n  userLocation?: {\n    country: string;\n    region: string;\n    city: string;\n    coordinates: { lat: number; lng: number };\n  };\n  deviceInfo?: {\n    type: 'mobile' | 'tablet' | 'desktop';\n    connection: '5g' | '4g' | 'wifi' | 'slow';\n    capabilities: string[];\n  };\n  cacheStrategy?: 'aggressive' | 'normal' | 'minimal' | 'bypass';\n}\n\ninterface DeliveryResult {\n  url: string;\n  provider: string;\n  region: string;\n  estimatedLatency: number;\n  cacheStatus: 'hit' | 'miss' | 'stale';\n  optimizations: string[];\n  fallbackUrls: string[];\n}\n\n/**\n * Global CDN Management System\n */\nclass GlobalCDNManager {\n  private cdnProviders: Map<string, CDNProvider> = new Map();\n  private regionalEndpoints: Map<string, CDNEndpoint[]> = new Map();\n  private performanceCache: Map<string, number> = new Map();\n  private routingTable: Map<string, string> = new Map();\n  \n  private readonly CDN_PROVIDERS: CDNProvider[] = [\n    {\n      id: 'cloudflare',\n      name: 'Cloudflare',\n      baseUrl: 'https://cdn.acemind.app',\n      regions: ['us-east', 'us-west', 'eu-west', 'eu-central', 'asia-pacific', 'asia-southeast'],\n      capabilities: {\n        imageOptimization: true,\n        videoStreaming: true,\n        edgeFunctions: true,\n        realTimeAnalytics: true,\n        ddosProtection: true,\n      },\n      pricing: {\n        bandwidth: 0.085, // $0.085 per GB\n        requests: 0.50, // $0.50 per million\n        storage: 0.015, // $0.015 per GB/month\n      },\n      performance: {\n        averageLatency: 45, // ms\n        uptime: 99.99,\n        throughput: 100, // Gbps\n      },\n    },\n    {\n      id: 'aws-cloudfront',\n      name: 'AWS CloudFront',\n      baseUrl: 'https://d1234567890.cloudfront.net',\n      regions: ['us-east-1', 'us-west-2', 'eu-west-1', 'ap-southeast-1', 'ap-northeast-1'],\n      capabilities: {\n        imageOptimization: true,\n        videoStreaming: true,\n        edgeFunctions: true,\n        realTimeAnalytics: true,\n        ddosProtection: true,\n      },\n      pricing: {\n        bandwidth: 0.085,\n        requests: 0.75,\n        storage: 0.023,\n      },\n      performance: {\n        averageLatency: 55,\n        uptime: 99.95,\n        throughput: 80,\n      },\n    },\n    {\n      id: 'fastly',\n      name: 'Fastly',\n      baseUrl: 'https://acemind.global.ssl.fastly.net',\n      regions: ['us-east', 'us-west', 'eu-west', 'asia-pacific'],\n      capabilities: {\n        imageOptimization: true,\n        videoStreaming: false,\n        edgeFunctions: true,\n        realTimeAnalytics: true,\n        ddosProtection: true,\n      },\n      pricing: {\n        bandwidth: 0.12,\n        requests: 0.40,\n        storage: 0.020,\n      },\n      performance: {\n        averageLatency: 40,\n        uptime: 99.98,\n        throughput: 60,\n      },\n    },\n  ];\n\n  constructor() {\n    this.initializeCDNManager();\n  }\n\n  /**\n   * Initialize CDN management system\n   */\n  private async initializeCDNManager(): Promise<void> {\n    try {\n      // Initialize CDN providers\n      this.CDN_PROVIDERS.forEach(provider => {\n        this.cdnProviders.set(provider.id, provider);\n      });\n\n      // Discover regional endpoints\n      await this.discoverRegionalEndpoints();\n      \n      // Start performance monitoring\n      this.startPerformanceMonitoring();\n      \n      // Initialize routing optimization\n      await this.optimizeRoutingTable();\n      \n      console.log('Global CDN Manager initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Global CDN Manager:', error);\n    }\n  }\n\n  /**\n   * Get optimized content delivery URL\n   */\n  async getOptimizedDeliveryUrl(request: ContentDeliveryRequest): Promise<DeliveryResult> {\n    try {\n      const startTime = Date.now();\n      \n      // Determine optimal CDN provider and region\n      const optimalEndpoint = await this.selectOptimalEndpoint(request);\n      \n      // Apply content optimizations\n      const optimizations = await this.applyContentOptimizations(request, optimalEndpoint);\n      \n      // Generate optimized URL\n      const optimizedUrl = this.generateOptimizedUrl(request, optimalEndpoint, optimizations);\n      \n      // Generate fallback URLs\n      const fallbackUrls = await this.generateFallbackUrls(request, optimalEndpoint);\n      \n      const result: DeliveryResult = {\n        url: optimizedUrl,\n        provider: optimalEndpoint.provider,\n        region: optimalEndpoint.region,\n        estimatedLatency: optimalEndpoint.latency,\n        cacheStatus: await this.predictCacheStatus(request, optimalEndpoint),\n        optimizations,\n        fallbackUrls,\n      };\n\n      // Track performance\n      const processingTime = Date.now() - startTime;\n      performanceMonitor.trackDatabaseQuery('cdn_optimization', processingTime);\n      \n      return result;\n\n    } catch (error) {\n      console.error('Failed to get optimized delivery URL:', error);\n      return this.getFallbackDeliveryResult(request);\n    }\n  }\n\n  /**\n   * Preload content globally\n   */\n  async preloadContentGlobally(\n    paths: string[],\n    regions: string[] = ['all'],\n    priority: 'high' | 'medium' | 'low' = 'medium'\n  ): Promise<void> {\n    try {\n      const preloadPromises: Promise<void>[] = [];\n      \n      for (const path of paths) {\n        for (const region of regions) {\n          const endpoints = regions.includes('all') \n            ? Array.from(this.regionalEndpoints.values()).flat()\n            : this.regionalEndpoints.get(region) || [];\n          \n          for (const endpoint of endpoints) {\n            preloadPromises.push(this.preloadToEndpoint(path, endpoint, priority));\n          }\n        }\n      }\n      \n      await Promise.allSettled(preloadPromises);\n      console.log(`Preloaded ${paths.length} assets to ${preloadPromises.length} endpoints`);\n      \n    } catch (error) {\n      console.error('Failed to preload content globally:', error);\n    }\n  }\n\n  /**\n   * Get global performance metrics\n   */\n  async getGlobalPerformanceMetrics(): Promise<{\n    averageLatency: number;\n    globalUptime: number;\n    regionalPerformance: Record<string, {\n      latency: number;\n      uptime: number;\n      throughput: number;\n    }>;\n    cdnComparison: Record<string, {\n      performance: number;\n      cost: number;\n      reliability: number;\n    }>;\n  }> {\n    const metrics = {\n      averageLatency: 0,\n      globalUptime: 0,\n      regionalPerformance: {} as any,\n      cdnComparison: {} as any,\n    };\n\n    try {\n      // Calculate average latency across all endpoints\n      const allEndpoints = Array.from(this.regionalEndpoints.values()).flat();\n      metrics.averageLatency = allEndpoints.reduce((sum, ep) => sum + ep.latency, 0) / allEndpoints.length;\n\n      // Calculate global uptime\n      const providers = Array.from(this.cdnProviders.values());\n      metrics.globalUptime = providers.reduce((sum, p) => sum + p.performance.uptime, 0) / providers.length;\n\n      // Regional performance breakdown\n      for (const [region, endpoints] of this.regionalEndpoints.entries()) {\n        const regionLatency = endpoints.reduce((sum, ep) => sum + ep.latency, 0) / endpoints.length;\n        const regionReliability = endpoints.reduce((sum, ep) => sum + ep.reliability, 0) / endpoints.length;\n        \n        metrics.regionalPerformance[region] = {\n          latency: regionLatency,\n          uptime: regionReliability * 100,\n          throughput: 100, // Would calculate from actual data\n        };\n      }\n\n      // CDN provider comparison\n      for (const [id, provider] of this.cdnProviders.entries()) {\n        metrics.cdnComparison[id] = {\n          performance: 100 - provider.performance.averageLatency, // Higher is better\n          cost: 100 - (provider.pricing.bandwidth * 100), // Lower cost is better\n          reliability: provider.performance.uptime,\n        };\n      }\n\n      return metrics;\n    } catch (error) {\n      console.error('Failed to get global performance metrics:', error);\n      return metrics;\n    }\n  }\n\n  /**\n   * Optimize CDN routing based on real-time performance\n   */\n  async optimizeGlobalRouting(): Promise<void> {\n    try {\n      console.log('Optimizing global CDN routing...');\n      \n      // Test all endpoints\n      const testPromises = Array.from(this.regionalEndpoints.values())\n        .flat()\n        .map(endpoint => this.testEndpointPerformance(endpoint));\n      \n      const testResults = await Promise.allSettled(testPromises);\n      \n      // Update routing table based on results\n      await this.updateRoutingTable(testResults);\n      \n      // Optimize based on current resource conditions\n      const resourceMetrics = smartResourceManager.getCurrentMetrics();\n      if (resourceMetrics) {\n        await this.adaptRoutingToResourceConditions(resourceMetrics);\n      }\n      \n      console.log('Global CDN routing optimization completed');\n      \n    } catch (error) {\n      console.error('Failed to optimize global routing:', error);\n    }\n  }\n\n  // Private helper methods\n\n  private async discoverRegionalEndpoints(): Promise<void> {\n    for (const provider of this.CDN_PROVIDERS) {\n      for (const region of provider.regions) {\n        const endpoint: CDNEndpoint = {\n          url: `${provider.baseUrl}/${region}`,\n          provider: provider.id,\n          region,\n          latency: provider.performance.averageLatency + Math.random() * 20, // Add variance\n          reliability: provider.performance.uptime / 100,\n          lastTested: Date.now(),\n        };\n\n        if (!this.regionalEndpoints.has(region)) {\n          this.regionalEndpoints.set(region, []);\n        }\n        this.regionalEndpoints.get(region)!.push(endpoint);\n      }\n    }\n  }\n\n  private async selectOptimalEndpoint(request: ContentDeliveryRequest): Promise<CDNEndpoint> {\n    const userRegion = this.determineUserRegion(request.userLocation);\n    const availableEndpoints = this.regionalEndpoints.get(userRegion) || \n                              Array.from(this.regionalEndpoints.values()).flat();\n\n    // Score endpoints based on multiple factors\n    const scoredEndpoints = availableEndpoints.map(endpoint => {\n      let score = 0;\n      \n      // Latency score (lower is better)\n      score += (200 - endpoint.latency) / 200 * 40;\n      \n      // Reliability score\n      score += endpoint.reliability * 30;\n      \n      // Provider capabilities score\n      const provider = this.cdnProviders.get(endpoint.provider);\n      if (provider) {\n        if (request.type === 'image' && provider.capabilities.imageOptimization) score += 15;\n        if (request.type === 'video' && provider.capabilities.videoStreaming) score += 15;\n        if (request.priority === 'high' && provider.capabilities.ddosProtection) score += 10;\n      }\n      \n      // Recent performance score\n      const recentPerf = this.performanceCache.get(`${endpoint.provider}_${endpoint.region}`);\n      if (recentPerf) {\n        score += (200 - recentPerf) / 200 * 5;\n      }\n      \n      return { endpoint, score };\n    });\n\n    // Return the highest scoring endpoint\n    scoredEndpoints.sort((a, b) => b.score - a.score);\n    return scoredEndpoints[0]?.endpoint || availableEndpoints[0];\n  }\n\n  private async applyContentOptimizations(\n    request: ContentDeliveryRequest,\n    endpoint: CDNEndpoint\n  ): Promise<string[]> {\n    const optimizations: string[] = [];\n    const provider = this.cdnProviders.get(endpoint.provider);\n    \n    if (!provider) return optimizations;\n\n    // Image optimizations\n    if (request.type === 'image' && provider.capabilities.imageOptimization) {\n      optimizations.push('webp_conversion');\n      optimizations.push('responsive_sizing');\n      optimizations.push('quality_optimization');\n    }\n\n    // Video optimizations\n    if (request.type === 'video' && provider.capabilities.videoStreaming) {\n      optimizations.push('adaptive_bitrate');\n      optimizations.push('format_optimization');\n    }\n\n    // Device-specific optimizations\n    if (request.deviceInfo) {\n      if (request.deviceInfo.connection === 'slow') {\n        optimizations.push('compression_boost');\n        optimizations.push('quality_reduction');\n      }\n      if (request.deviceInfo.type === 'mobile') {\n        optimizations.push('mobile_optimization');\n      }\n    }\n\n    // Cache optimizations\n    if (request.cacheStrategy === 'aggressive') {\n      optimizations.push('extended_cache_headers');\n    }\n\n    return optimizations;\n  }\n\n  private generateOptimizedUrl(\n    request: ContentDeliveryRequest,\n    endpoint: CDNEndpoint,\n    optimizations: string[]\n  ): string {\n    let url = `${endpoint.url}${request.path}`;\n    \n    // Add optimization parameters\n    const params = new URLSearchParams();\n    \n    optimizations.forEach(opt => {\n      switch (opt) {\n        case 'webp_conversion':\n          params.set('format', 'webp');\n          break;\n        case 'responsive_sizing':\n          params.set('width', 'auto');\n          break;\n        case 'quality_optimization':\n          params.set('quality', '85');\n          break;\n        case 'compression_boost':\n          params.set('compress', 'true');\n          break;\n        case 'mobile_optimization':\n          params.set('mobile', 'true');\n          break;\n      }\n    });\n\n    if (params.toString()) {\n      url += `?${params.toString()}`;\n    }\n\n    return url;\n  }\n\n  private async generateFallbackUrls(\n    request: ContentDeliveryRequest,\n    primaryEndpoint: CDNEndpoint\n  ): Promise<string[]> {\n    const fallbackUrls: string[] = [];\n    const allEndpoints = Array.from(this.regionalEndpoints.values()).flat();\n    \n    // Get alternative endpoints (excluding primary)\n    const alternatives = allEndpoints\n      .filter(ep => ep.provider !== primaryEndpoint.provider || ep.region !== primaryEndpoint.region)\n      .sort((a, b) => a.latency - b.latency)\n      .slice(0, 3); // Top 3 alternatives\n\n    for (const endpoint of alternatives) {\n      const optimizations = await this.applyContentOptimizations(request, endpoint);\n      const url = this.generateOptimizedUrl(request, endpoint, optimizations);\n      fallbackUrls.push(url);\n    }\n\n    return fallbackUrls;\n  }\n\n  private async predictCacheStatus(\n    request: ContentDeliveryRequest,\n    endpoint: CDNEndpoint\n  ): Promise<'hit' | 'miss' | 'stale'> {\n    // Simple cache prediction logic (would be more sophisticated in real implementation)\n    const cacheKey = `${endpoint.provider}_${request.path}`;\n    const lastAccess = this.performanceCache.get(cacheKey);\n    \n    if (!lastAccess) return 'miss';\n    \n    const timeSinceAccess = Date.now() - lastAccess;\n    if (timeSinceAccess < 300000) return 'hit'; // 5 minutes\n    if (timeSinceAccess < 3600000) return 'stale'; // 1 hour\n    return 'miss';\n  }\n\n  private getFallbackDeliveryResult(request: ContentDeliveryRequest): DeliveryResult {\n    return {\n      url: `https://fallback.acemind.app${request.path}`,\n      provider: 'fallback',\n      region: 'global',\n      estimatedLatency: 200,\n      cacheStatus: 'miss',\n      optimizations: [],\n      fallbackUrls: [],\n    };\n  }\n\n  private determineUserRegion(location?: ContentDeliveryRequest['userLocation']): string {\n    if (!location) return 'us-east'; // Default region\n    \n    // Simple region mapping (would be more sophisticated in real implementation)\n    const regionMap: Record<string, string> = {\n      'US': 'us-east',\n      'CA': 'us-east',\n      'GB': 'eu-west',\n      'DE': 'eu-central',\n      'FR': 'eu-west',\n      'JP': 'asia-pacific',\n      'AU': 'asia-pacific',\n      'SG': 'asia-southeast',\n    };\n    \n    return regionMap[location.country] || 'us-east';\n  }\n\n  private async preloadToEndpoint(\n    path: string,\n    endpoint: CDNEndpoint,\n    priority: 'high' | 'medium' | 'low'\n  ): Promise<void> {\n    try {\n      // Simulate preloading (in real implementation, would make actual requests)\n      console.log(`Preloading ${path} to ${endpoint.provider}/${endpoint.region}`);\n    } catch (error) {\n      console.error(`Failed to preload ${path} to ${endpoint.provider}:`, error);\n    }\n  }\n\n  private startPerformanceMonitoring(): void {\n    // Monitor CDN performance every 5 minutes\n    setInterval(() => {\n      this.monitorCDNPerformance();\n    }, 300000);\n  }\n\n  private async monitorCDNPerformance(): Promise<void> {\n    const allEndpoints = Array.from(this.regionalEndpoints.values()).flat();\n    \n    for (const endpoint of allEndpoints) {\n      try {\n        const latency = await this.measureEndpointLatency(endpoint);\n        this.performanceCache.set(`${endpoint.provider}_${endpoint.region}`, latency);\n        endpoint.latency = latency;\n        endpoint.lastTested = Date.now();\n      } catch (error) {\n        console.error(`Failed to monitor ${endpoint.provider}/${endpoint.region}:`, error);\n      }\n    }\n  }\n\n  private async measureEndpointLatency(endpoint: CDNEndpoint): Promise<number> {\n    // Simulate latency measurement (in real implementation, would ping endpoint)\n    return endpoint.latency + (Math.random() - 0.5) * 20; // Add some variance\n  }\n\n  private async testEndpointPerformance(endpoint: CDNEndpoint): Promise<{\n    endpoint: CDNEndpoint;\n    latency: number;\n    success: boolean;\n  }> {\n    try {\n      const latency = await this.measureEndpointLatency(endpoint);\n      return { endpoint, latency, success: true };\n    } catch (error) {\n      return { endpoint, latency: 9999, success: false };\n    }\n  }\n\n  private async updateRoutingTable(testResults: PromiseSettledResult<any>[]): Promise<void> {\n    // Update routing based on test results\n    testResults.forEach(result => {\n      if (result.status === 'fulfilled' && result.value.success) {\n        const { endpoint, latency } = result.value;\n        this.routingTable.set(`${endpoint.provider}_${endpoint.region}`, latency.toString());\n      }\n    });\n  }\n\n  private async adaptRoutingToResourceConditions(resourceMetrics: any): Promise<void> {\n    // Adapt routing based on current resource conditions\n    if (resourceMetrics.network.latency > 200) {\n      // Prefer faster CDNs when network is slow\n      console.log('Adapting routing for slow network conditions');\n    }\n    \n    if (resourceMetrics.battery.level < 20) {\n      // Prefer more efficient CDNs when battery is low\n      console.log('Adapting routing for low battery conditions');\n    }\n  }\n\n  private async optimizeRoutingTable(): Promise<void> {\n    // Initial routing table optimization\n    console.log('Optimizing initial routing table');\n  }\n}\n\n// Export singleton instance\nexport const globalCDNManager = new GlobalCDNManager();\nexport default globalCDNManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,kBAAkB;AAC3B,SAASC,oBAAoB;AAA6C,IAkEpEC,gBAAgB;EA8EpB,SAAAA,iBAAA,EAAc;IAAAC,eAAA,OAAAD,gBAAA;IAAA,KA7ENE,YAAY,IAAAC,aAAA,GAAAC,CAAA,OAA6B,IAAIC,GAAG,CAAC,CAAC;IAAA,KAClDC,iBAAiB,IAAAH,aAAA,GAAAC,CAAA,OAA+B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACzDE,gBAAgB,IAAAJ,aAAA,GAAAC,CAAA,OAAwB,IAAIC,GAAG,CAAC,CAAC;IAAA,KACjDG,YAAY,IAAAL,aAAA,GAAAC,CAAA,OAAwB,IAAIC,GAAG,CAAC,CAAC;IAAA,KAEpCI,aAAa,IAAAN,aAAA,GAAAC,CAAA,OAAkB,CAC9C;MACEM,EAAE,EAAE,YAAY;MAChBC,IAAI,EAAE,YAAY;MAClBC,OAAO,EAAE,yBAAyB;MAClCC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,EAAE,gBAAgB,CAAC;MAC1FC,YAAY,EAAE;QACZC,iBAAiB,EAAE,IAAI;QACvBC,cAAc,EAAE,IAAI;QACpBC,aAAa,EAAE,IAAI;QACnBC,iBAAiB,EAAE,IAAI;QACvBC,cAAc,EAAE;MAClB,CAAC;MACDC,OAAO,EAAE;QACPC,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;MACX,CAAC;MACDC,WAAW,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE;MACd;IACF,CAAC,EACD;MACEjB,EAAE,EAAE,gBAAgB;MACpBC,IAAI,EAAE,gBAAgB;MACtBC,OAAO,EAAE,oCAAoC;MAC7CC,OAAO,EAAE,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;MACpFC,YAAY,EAAE;QACZC,iBAAiB,EAAE,IAAI;QACvBC,cAAc,EAAE,IAAI;QACpBC,aAAa,EAAE,IAAI;QACnBC,iBAAiB,EAAE,IAAI;QACvBC,cAAc,EAAE;MAClB,CAAC;MACDC,OAAO,EAAE;QACPC,SAAS,EAAE,KAAK;QAChBC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;MACX,CAAC;MACDC,WAAW,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE;MACd;IACF,CAAC,EACD;MACEjB,EAAE,EAAE,QAAQ;MACZC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,uCAAuC;MAChDC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,cAAc,CAAC;MAC1DC,YAAY,EAAE;QACZC,iBAAiB,EAAE,IAAI;QACvBC,cAAc,EAAE,KAAK;QACrBC,aAAa,EAAE,IAAI;QACnBC,iBAAiB,EAAE,IAAI;QACvBC,cAAc,EAAE;MAClB,CAAC;MACDC,OAAO,EAAE;QACPC,SAAS,EAAE,IAAI;QACfC,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;MACX,CAAC;MACDC,WAAW,EAAE;QACXC,cAAc,EAAE,EAAE;QAClBC,MAAM,EAAE,KAAK;QACbC,UAAU,EAAE;MACd;IACF,CAAC,CACF;IAAAxB,aAAA,GAAAyB,CAAA;IAAAzB,aAAA,GAAAC,CAAA;IAGC,IAAI,CAACyB,oBAAoB,CAAC,CAAC;EAC7B;EAAC,OAAAC,YAAA,CAAA9B,gBAAA;IAAA+B,GAAA;IAAAC,KAAA;MAAA,IAAAC,qBAAA,GAAAC,iBAAA,CAKD,aAAoD;QAAA,IAAAC,KAAA;QAAAhC,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAClD,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAEF,IAAI,CAACK,aAAa,CAAC2B,OAAO,CAAC,UAAAC,QAAQ,EAAI;YAAAlC,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YACrC+B,KAAI,CAACjC,YAAY,CAACoC,GAAG,CAACD,QAAQ,CAAC3B,EAAE,EAAE2B,QAAQ,CAAC;UAC9C,CAAC,CAAC;UAAClC,aAAA,GAAAC,CAAA;UAGH,MAAM,IAAI,CAACmC,yBAAyB,CAAC,CAAC;UAACpC,aAAA,GAAAC,CAAA;UAGvC,IAAI,CAACoC,0BAA0B,CAAC,CAAC;UAACrC,aAAA,GAAAC,CAAA;UAGlC,MAAM,IAAI,CAACqC,oBAAoB,CAAC,CAAC;UAACtC,aAAA,GAAAC,CAAA;UAElCsC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC5D,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAzC,aAAA,GAAAC,CAAA;UACdsC,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAClE;MACF,CAAC;MAAA,SApBaf,oBAAoBA,CAAA;QAAA,OAAAI,qBAAA,CAAAY,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBjB,oBAAoB;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA;MAAA,IAAAe,wBAAA,GAAAb,iBAAA,CAyBlC,WAA8Bc,OAA+B,EAA2B;QAAA7C,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QACtF,IAAI;UACF,IAAM6C,SAAS,IAAA9C,aAAA,GAAAC,CAAA,QAAG8C,IAAI,CAACC,GAAG,CAAC,CAAC;UAG5B,IAAMC,eAAe,IAAAjD,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACiD,qBAAqB,CAACL,OAAO,CAAC;UAGjE,IAAMM,aAAa,IAAAnD,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACmD,yBAAyB,CAACP,OAAO,EAAEI,eAAe,CAAC;UAGpF,IAAMI,YAAY,IAAArD,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACqD,oBAAoB,CAACT,OAAO,EAAEI,eAAe,EAAEE,aAAa,CAAC;UAGvF,IAAMI,YAAY,IAAAvD,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACuD,oBAAoB,CAACX,OAAO,EAAEI,eAAe,CAAC;UAE9E,IAAMQ,MAAsB,IAAAzD,aAAA,GAAAC,CAAA,QAAG;YAC7ByD,GAAG,EAAEL,YAAY;YACjBnB,QAAQ,EAAEe,eAAe,CAACf,QAAQ;YAClCyB,MAAM,EAAEV,eAAe,CAACU,MAAM;YAC9BC,gBAAgB,EAAEX,eAAe,CAACY,OAAO;YACzCC,WAAW,QAAQ,IAAI,CAACC,kBAAkB,CAAClB,OAAO,EAAEI,eAAe,CAAC;YACpEE,aAAa,EAAbA,aAAa;YACbI,YAAY,EAAZA;UACF,CAAC;UAGD,IAAMS,cAAc,IAAAhE,aAAA,GAAAC,CAAA,QAAG8C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;UAAC9C,aAAA,GAAAC,CAAA;UAC9CN,kBAAkB,CAACsE,kBAAkB,CAAC,kBAAkB,EAAED,cAAc,CAAC;UAAChE,aAAA,GAAAC,CAAA;UAE1E,OAAOwD,MAAM;QAEf,CAAC,CAAC,OAAOhB,KAAK,EAAE;UAAAzC,aAAA,GAAAC,CAAA;UACdsC,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAACzC,aAAA,GAAAC,CAAA;UAC9D,OAAO,IAAI,CAACiE,yBAAyB,CAACrB,OAAO,CAAC;QAChD;MACF,CAAC;MAAA,SApCKsB,uBAAuBA,CAAAC,EAAA;QAAA,OAAAxB,wBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAvBwB,uBAAuB;IAAA;EAAA;IAAAvC,GAAA;IAAAC,KAAA;MAAA,IAAAwC,uBAAA,GAAAtC,iBAAA,CAyC7B,WACEuC,KAAe,EAGA;QAAA,IAFf5D,OAAiB,GAAAiC,SAAA,CAAA4B,MAAA,QAAA5B,SAAA,QAAA6B,SAAA,GAAA7B,SAAA,OAAA3C,aAAA,GAAAyE,CAAA,UAAG,CAAC,KAAK,CAAC;QAAA,IAC3BC,QAAmC,GAAA/B,SAAA,CAAA4B,MAAA,QAAA5B,SAAA,QAAA6B,SAAA,GAAA7B,SAAA,OAAA3C,aAAA,GAAAyE,CAAA,UAAG,QAAQ;QAAAzE,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAE9C,IAAI;UACF,IAAM0E,eAAgC,IAAA3E,aAAA,GAAAC,CAAA,QAAG,EAAE;UAACD,aAAA,GAAAC,CAAA;UAE5C,KAAK,IAAM2E,IAAI,IAAIN,KAAK,EAAE;YAAAtE,aAAA,GAAAC,CAAA;YACxB,KAAK,IAAM0D,MAAM,IAAIjD,OAAO,EAAE;cAC5B,IAAMmE,SAAS,IAAA7E,aAAA,GAAAC,CAAA,QAAGS,OAAO,CAACoE,QAAQ,CAAC,KAAK,CAAC,IAAA9E,aAAA,GAAAyE,CAAA,UACrCM,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,iBAAiB,CAAC8E,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,KAAAlF,aAAA,GAAAyE,CAAA,UAClD,CAAAzE,aAAA,GAAAyE,CAAA,cAAI,CAACtE,iBAAiB,CAACgF,GAAG,CAACxB,MAAM,CAAC,MAAA3D,aAAA,GAAAyE,CAAA,UAAI,EAAE;cAACzE,aAAA,GAAAC,CAAA;cAE7C,KAAK,IAAMmF,QAAQ,IAAIP,SAAS,EAAE;gBAAA7E,aAAA,GAAAC,CAAA;gBAChC0E,eAAe,CAACU,IAAI,CAAC,IAAI,CAACC,iBAAiB,CAACV,IAAI,EAAEQ,QAAQ,EAAEV,QAAQ,CAAC,CAAC;cACxE;YACF;UACF;UAAC1E,aAAA,GAAAC,CAAA;UAED,MAAMsF,OAAO,CAACC,UAAU,CAACb,eAAe,CAAC;UAAC3E,aAAA,GAAAC,CAAA;UAC1CsC,OAAO,CAACC,GAAG,CAAC,aAAa8B,KAAK,CAACC,MAAM,cAAcI,eAAe,CAACJ,MAAM,YAAY,CAAC;QAExF,CAAC,CAAC,OAAO9B,KAAK,EAAE;UAAAzC,aAAA,GAAAC,CAAA;UACdsC,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC7D;MACF,CAAC;MAAA,SA1BKgD,sBAAsBA,CAAAC,GAAA;QAAA,OAAArB,uBAAA,CAAA3B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtB8C,sBAAsB;IAAA;EAAA;IAAA7D,GAAA;IAAAC,KAAA;MAAA,IAAA8D,4BAAA,GAAA5D,iBAAA,CA+B5B,aAaG;QAAA/B,aAAA,GAAAyB,CAAA;QACD,IAAMmE,OAAO,IAAA5F,aAAA,GAAAC,CAAA,QAAG;UACdqB,cAAc,EAAE,CAAC;UACjBuE,YAAY,EAAE,CAAC;UACfC,mBAAmB,EAAE,CAAC,CAAQ;UAC9BC,aAAa,EAAE,CAAC;QAClB,CAAC;QAAC/F,aAAA,GAAAC,CAAA;QAEF,IAAI;UAEF,IAAM+F,YAAY,IAAAhG,aAAA,GAAAC,CAAA,QAAG8E,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,iBAAiB,CAAC8E,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;UAAClF,aAAA,GAAAC,CAAA;UACxE2F,OAAO,CAACtE,cAAc,GAAG0E,YAAY,CAACC,MAAM,CAAC,UAACC,GAAG,EAAEC,EAAE,EAAK;YAAAnG,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA,OAAAiG,GAAG,GAAGC,EAAE,CAACtC,OAAO;UAAD,CAAC,EAAE,CAAC,CAAC,GAAGmC,YAAY,CAACzB,MAAM;UAGpG,IAAM6B,SAAS,IAAApG,aAAA,GAAAC,CAAA,QAAG8E,KAAK,CAACC,IAAI,CAAC,IAAI,CAACjF,YAAY,CAACkF,MAAM,CAAC,CAAC,CAAC;UAACjF,aAAA,GAAAC,CAAA;UACzD2F,OAAO,CAACC,YAAY,GAAGO,SAAS,CAACH,MAAM,CAAC,UAACC,GAAG,EAAEG,CAAC,EAAK;YAAArG,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA,OAAAiG,GAAG,GAAGG,CAAC,CAAChF,WAAW,CAACE,MAAM;UAAD,CAAC,EAAE,CAAC,CAAC,GAAG6E,SAAS,CAAC7B,MAAM;UAACvE,aAAA,GAAAC,CAAA;UAGtG,SAAAqG,IAAA,IAAkC,IAAI,CAACnG,iBAAiB,CAACoG,OAAO,CAAC,CAAC,EAAE;YAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAH,IAAA;YAAA,IAAxD3C,MAAM,GAAA6C,KAAA;YAAA,IAAE3B,SAAS,GAAA2B,KAAA;YAC3B,IAAME,aAAa,IAAA1G,aAAA,GAAAC,CAAA,QAAG4E,SAAS,CAACoB,MAAM,CAAC,UAACC,GAAG,EAAEC,EAAE,EAAK;cAAAnG,aAAA,GAAAyB,CAAA;cAAAzB,aAAA,GAAAC,CAAA;cAAA,OAAAiG,GAAG,GAAGC,EAAE,CAACtC,OAAO;YAAD,CAAC,EAAE,CAAC,CAAC,GAAGgB,SAAS,CAACN,MAAM;YAC3F,IAAMoC,iBAAiB,IAAA3G,aAAA,GAAAC,CAAA,QAAG4E,SAAS,CAACoB,MAAM,CAAC,UAACC,GAAG,EAAEC,EAAE,EAAK;cAAAnG,aAAA,GAAAyB,CAAA;cAAAzB,aAAA,GAAAC,CAAA;cAAA,OAAAiG,GAAG,GAAGC,EAAE,CAACS,WAAW;YAAD,CAAC,EAAE,CAAC,CAAC,GAAG/B,SAAS,CAACN,MAAM;YAACvE,aAAA,GAAAC,CAAA;YAEpG2F,OAAO,CAACE,mBAAmB,CAACnC,MAAM,CAAC,GAAG;cACpCE,OAAO,EAAE6C,aAAa;cACtBnF,MAAM,EAAEoF,iBAAiB,GAAG,GAAG;cAC/BnF,UAAU,EAAE;YACd,CAAC;UACH;UAACxB,aAAA,GAAAC,CAAA;UAGD,SAAA4G,KAAA,IAA6B,IAAI,CAAC9G,YAAY,CAACwG,OAAO,CAAC,CAAC,EAAE;YAAA,IAAAO,KAAA,GAAAL,cAAA,CAAAI,KAAA;YAAA,IAA9CtG,EAAE,GAAAuG,KAAA;YAAA,IAAE5E,QAAQ,GAAA4E,KAAA;YAAA9G,aAAA,GAAAC,CAAA;YACtB2F,OAAO,CAACG,aAAa,CAACxF,EAAE,CAAC,GAAG;cAC1Bc,WAAW,EAAE,GAAG,GAAGa,QAAQ,CAACb,WAAW,CAACC,cAAc;cACtDyF,IAAI,EAAE,GAAG,GAAI7E,QAAQ,CAACjB,OAAO,CAACC,SAAS,GAAG,GAAI;cAC9C0F,WAAW,EAAE1E,QAAQ,CAACb,WAAW,CAACE;YACpC,CAAC;UACH;UAACvB,aAAA,GAAAC,CAAA;UAED,OAAO2F,OAAO;QAChB,CAAC,CAAC,OAAOnD,KAAK,EAAE;UAAAzC,aAAA,GAAAC,CAAA;UACdsC,OAAO,CAACE,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;UAACzC,aAAA,GAAAC,CAAA;UAClE,OAAO2F,OAAO;QAChB;MACF,CAAC;MAAA,SAxDKoB,2BAA2BA,CAAA;QAAA,OAAArB,4BAAA,CAAAjD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA3BqE,2BAA2B;IAAA;EAAA;IAAApF,GAAA;IAAAC,KAAA;MAAA,IAAAoF,sBAAA,GAAAlF,iBAAA,CA6DjC,aAA6C;QAAA,IAAAmF,MAAA;QAAAlH,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAC3C,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFsC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAG/C,IAAM2E,YAAY,IAAAnH,aAAA,GAAAC,CAAA,QAAG8E,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,iBAAiB,CAAC8E,MAAM,CAAC,CAAC,CAAC,CAC7DC,IAAI,CAAC,CAAC,CACNkC,GAAG,CAAC,UAAAhC,QAAQ,EAAI;YAAApF,aAAA,GAAAyB,CAAA;YAAAzB,aAAA,GAAAC,CAAA;YAAA,OAAAiH,MAAI,CAACG,uBAAuB,CAACjC,QAAQ,CAAC;UAAD,CAAC,CAAC;UAE1D,IAAMkC,WAAW,IAAAtH,aAAA,GAAAC,CAAA,cAASsF,OAAO,CAACC,UAAU,CAAC2B,YAAY,CAAC;UAACnH,aAAA,GAAAC,CAAA;UAG3D,MAAM,IAAI,CAACsH,kBAAkB,CAACD,WAAW,CAAC;UAG1C,IAAME,eAAe,IAAAxH,aAAA,GAAAC,CAAA,QAAGL,oBAAoB,CAAC6H,iBAAiB,CAAC,CAAC;UAACzH,aAAA,GAAAC,CAAA;UACjE,IAAIuH,eAAe,EAAE;YAAAxH,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACnB,MAAM,IAAI,CAACyH,gCAAgC,CAACF,eAAe,CAAC;UAC9D,CAAC;YAAAxH,aAAA,GAAAyE,CAAA;UAAA;UAAAzE,aAAA,GAAAC,CAAA;UAEDsC,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAE1D,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAzC,aAAA,GAAAC,CAAA;UACdsC,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAC5D;MACF,CAAC;MAAA,SAzBKkF,qBAAqBA,CAAA;QAAA,OAAAV,sBAAA,CAAAvE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBgF,qBAAqB;IAAA;EAAA;IAAA/F,GAAA;IAAAC,KAAA;MAAA,IAAA+F,0BAAA,GAAA7F,iBAAA,CA6B3B,aAAyD;QAAA/B,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QACvD,KAAK,IAAMiC,QAAQ,IAAI,IAAI,CAAC5B,aAAa,EAAE;UAAAN,aAAA,GAAAC,CAAA;UACzC,KAAK,IAAM0D,MAAM,IAAIzB,QAAQ,CAACxB,OAAO,EAAE;YACrC,IAAM0E,QAAqB,IAAApF,aAAA,GAAAC,CAAA,QAAG;cAC5ByD,GAAG,EAAE,GAAGxB,QAAQ,CAACzB,OAAO,IAAIkD,MAAM,EAAE;cACpCzB,QAAQ,EAAEA,QAAQ,CAAC3B,EAAE;cACrBoD,MAAM,EAANA,MAAM;cACNE,OAAO,EAAE3B,QAAQ,CAACb,WAAW,CAACC,cAAc,GAAGuG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;cACjElB,WAAW,EAAE1E,QAAQ,CAACb,WAAW,CAACE,MAAM,GAAG,GAAG;cAC9CwG,UAAU,EAAEhF,IAAI,CAACC,GAAG,CAAC;YACvB,CAAC;YAAChD,aAAA,GAAAC,CAAA;YAEF,IAAI,CAAC,IAAI,CAACE,iBAAiB,CAAC6H,GAAG,CAACrE,MAAM,CAAC,EAAE;cAAA3D,aAAA,GAAAyE,CAAA;cAAAzE,aAAA,GAAAC,CAAA;cACvC,IAAI,CAACE,iBAAiB,CAACgC,GAAG,CAACwB,MAAM,EAAE,EAAE,CAAC;YACxC,CAAC;cAAA3D,aAAA,GAAAyE,CAAA;YAAA;YAAAzE,aAAA,GAAAC,CAAA;YACD,IAAI,CAACE,iBAAiB,CAACgF,GAAG,CAACxB,MAAM,CAAC,CAAE0B,IAAI,CAACD,QAAQ,CAAC;UACpD;QACF;MACF,CAAC;MAAA,SAlBahD,yBAAyBA,CAAA;QAAA,OAAAwF,0BAAA,CAAAlF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAzBP,yBAAyB;IAAA;EAAA;IAAAR,GAAA;IAAAC,KAAA;MAAA,IAAAoG,sBAAA,GAAAlG,iBAAA,CAoBvC,WAAoCc,OAA+B,EAAwB;QAAA,IAAAqF,MAAA;UAAAC,iBAAA;QAAAnI,aAAA,GAAAyB,CAAA;QACzF,IAAM2G,UAAU,IAAApI,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACoI,mBAAmB,CAACxF,OAAO,CAACyF,YAAY,CAAC;QACjE,IAAMC,kBAAkB,IAAAvI,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAAyE,CAAA,cAAI,CAACtE,iBAAiB,CAACgF,GAAG,CAACiD,UAAU,CAAC,MAAApI,aAAA,GAAAyE,CAAA,UACvCM,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,iBAAiB,CAAC8E,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;QAG5E,IAAMsD,eAAe,IAAAxI,aAAA,GAAAC,CAAA,QAAGsI,kBAAkB,CAACnB,GAAG,CAAC,UAAAhC,QAAQ,EAAI;UAAApF,aAAA,GAAAyB,CAAA;UACzD,IAAIgH,KAAK,IAAAzI,aAAA,GAAAC,CAAA,QAAG,CAAC;UAACD,aAAA,GAAAC,CAAA;UAGdwI,KAAK,IAAI,CAAC,GAAG,GAAGrD,QAAQ,CAACvB,OAAO,IAAI,GAAG,GAAG,EAAE;UAAC7D,aAAA,GAAAC,CAAA;UAG7CwI,KAAK,IAAIrD,QAAQ,CAACwB,WAAW,GAAG,EAAE;UAGlC,IAAM1E,QAAQ,IAAAlC,aAAA,GAAAC,CAAA,QAAGiI,MAAI,CAACnI,YAAY,CAACoF,GAAG,CAACC,QAAQ,CAAClD,QAAQ,CAAC;UAAClC,aAAA,GAAAC,CAAA;UAC1D,IAAIiC,QAAQ,EAAE;YAAAlC,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACZ,IAAI,CAAAD,aAAA,GAAAyE,CAAA,UAAA5B,OAAO,CAAC6F,IAAI,KAAK,OAAO,MAAA1I,aAAA,GAAAyE,CAAA,UAAIvC,QAAQ,CAACvB,YAAY,CAACC,iBAAiB,GAAE;cAAAZ,aAAA,GAAAyE,CAAA;cAAAzE,aAAA,GAAAC,CAAA;cAAAwI,KAAK,IAAI,EAAE;YAAA,CAAC;cAAAzI,aAAA,GAAAyE,CAAA;YAAA;YAAAzE,aAAA,GAAAC,CAAA;YACrF,IAAI,CAAAD,aAAA,GAAAyE,CAAA,WAAA5B,OAAO,CAAC6F,IAAI,KAAK,OAAO,MAAA1I,aAAA,GAAAyE,CAAA,WAAIvC,QAAQ,CAACvB,YAAY,CAACE,cAAc,GAAE;cAAAb,aAAA,GAAAyE,CAAA;cAAAzE,aAAA,GAAAC,CAAA;cAAAwI,KAAK,IAAI,EAAE;YAAA,CAAC;cAAAzI,aAAA,GAAAyE,CAAA;YAAA;YAAAzE,aAAA,GAAAC,CAAA;YAClF,IAAI,CAAAD,aAAA,GAAAyE,CAAA,WAAA5B,OAAO,CAAC6B,QAAQ,KAAK,MAAM,MAAA1E,aAAA,GAAAyE,CAAA,WAAIvC,QAAQ,CAACvB,YAAY,CAACK,cAAc,GAAE;cAAAhB,aAAA,GAAAyE,CAAA;cAAAzE,aAAA,GAAAC,CAAA;cAAAwI,KAAK,IAAI,EAAE;YAAA,CAAC;cAAAzI,aAAA,GAAAyE,CAAA;YAAA;UACvF,CAAC;YAAAzE,aAAA,GAAAyE,CAAA;UAAA;UAGD,IAAMkE,UAAU,IAAA3I,aAAA,GAAAC,CAAA,QAAGiI,MAAI,CAAC9H,gBAAgB,CAAC+E,GAAG,CAAC,GAAGC,QAAQ,CAAClD,QAAQ,IAAIkD,QAAQ,CAACzB,MAAM,EAAE,CAAC;UAAC3D,aAAA,GAAAC,CAAA;UACxF,IAAI0I,UAAU,EAAE;YAAA3I,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACdwI,KAAK,IAAI,CAAC,GAAG,GAAGE,UAAU,IAAI,GAAG,GAAG,CAAC;UACvC,CAAC;YAAA3I,aAAA,GAAAyE,CAAA;UAAA;UAAAzE,aAAA,GAAAC,CAAA;UAED,OAAO;YAAEmF,QAAQ,EAARA,QAAQ;YAAEqD,KAAK,EAALA;UAAM,CAAC;QAC5B,CAAC,CAAC;QAACzI,aAAA,GAAAC,CAAA;QAGHuI,eAAe,CAACI,IAAI,CAAC,UAACC,CAAC,EAAEpE,CAAC,EAAK;UAAAzE,aAAA,GAAAyB,CAAA;UAAAzB,aAAA,GAAAC,CAAA;UAAA,OAAAwE,CAAC,CAACgE,KAAK,GAAGI,CAAC,CAACJ,KAAK;QAAD,CAAC,CAAC;QAACzI,aAAA,GAAAC,CAAA;QAClD,OAAO,CAAAD,aAAA,GAAAyE,CAAA,YAAA0D,iBAAA,GAAAK,eAAe,CAAC,CAAC,CAAC,qBAAlBL,iBAAA,CAAoB/C,QAAQ,MAAApF,aAAA,GAAAyE,CAAA,WAAI8D,kBAAkB,CAAC,CAAC,CAAC;MAC9D,CAAC;MAAA,SAnCarF,qBAAqBA,CAAA4F,GAAA;QAAA,OAAAb,sBAAA,CAAAvF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBO,qBAAqB;IAAA;EAAA;IAAAtB,GAAA;IAAAC,KAAA;MAAA,IAAAkH,0BAAA,GAAAhH,iBAAA,CAqCnC,WACEc,OAA+B,EAC/BuC,QAAqB,EACF;QAAApF,aAAA,GAAAyB,CAAA;QACnB,IAAM0B,aAAuB,IAAAnD,aAAA,GAAAC,CAAA,QAAG,EAAE;QAClC,IAAMiC,QAAQ,IAAAlC,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,YAAY,CAACoF,GAAG,CAACC,QAAQ,CAAClD,QAAQ,CAAC;QAAClC,aAAA,GAAAC,CAAA;QAE1D,IAAI,CAACiC,QAAQ,EAAE;UAAAlC,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UAAA,OAAOkD,aAAa;QAAA,CAAC;UAAAnD,aAAA,GAAAyE,CAAA;QAAA;QAAAzE,aAAA,GAAAC,CAAA;QAGpC,IAAI,CAAAD,aAAA,GAAAyE,CAAA,WAAA5B,OAAO,CAAC6F,IAAI,KAAK,OAAO,MAAA1I,aAAA,GAAAyE,CAAA,WAAIvC,QAAQ,CAACvB,YAAY,CAACC,iBAAiB,GAAE;UAAAZ,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UACvEkD,aAAa,CAACkC,IAAI,CAAC,iBAAiB,CAAC;UAACrF,aAAA,GAAAC,CAAA;UACtCkD,aAAa,CAACkC,IAAI,CAAC,mBAAmB,CAAC;UAACrF,aAAA,GAAAC,CAAA;UACxCkD,aAAa,CAACkC,IAAI,CAAC,sBAAsB,CAAC;QAC5C,CAAC;UAAArF,aAAA,GAAAyE,CAAA;QAAA;QAAAzE,aAAA,GAAAC,CAAA;QAGD,IAAI,CAAAD,aAAA,GAAAyE,CAAA,WAAA5B,OAAO,CAAC6F,IAAI,KAAK,OAAO,MAAA1I,aAAA,GAAAyE,CAAA,WAAIvC,QAAQ,CAACvB,YAAY,CAACE,cAAc,GAAE;UAAAb,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UACpEkD,aAAa,CAACkC,IAAI,CAAC,kBAAkB,CAAC;UAACrF,aAAA,GAAAC,CAAA;UACvCkD,aAAa,CAACkC,IAAI,CAAC,qBAAqB,CAAC;QAC3C,CAAC;UAAArF,aAAA,GAAAyE,CAAA;QAAA;QAAAzE,aAAA,GAAAC,CAAA;QAGD,IAAI4C,OAAO,CAACmG,UAAU,EAAE;UAAAhJ,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UACtB,IAAI4C,OAAO,CAACmG,UAAU,CAACC,UAAU,KAAK,MAAM,EAAE;YAAAjJ,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YAC5CkD,aAAa,CAACkC,IAAI,CAAC,mBAAmB,CAAC;YAACrF,aAAA,GAAAC,CAAA;YACxCkD,aAAa,CAACkC,IAAI,CAAC,mBAAmB,CAAC;UACzC,CAAC;YAAArF,aAAA,GAAAyE,CAAA;UAAA;UAAAzE,aAAA,GAAAC,CAAA;UACD,IAAI4C,OAAO,CAACmG,UAAU,CAACN,IAAI,KAAK,QAAQ,EAAE;YAAA1I,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACxCkD,aAAa,CAACkC,IAAI,CAAC,qBAAqB,CAAC;UAC3C,CAAC;YAAArF,aAAA,GAAAyE,CAAA;UAAA;QACH,CAAC;UAAAzE,aAAA,GAAAyE,CAAA;QAAA;QAAAzE,aAAA,GAAAC,CAAA;QAGD,IAAI4C,OAAO,CAACqG,aAAa,KAAK,YAAY,EAAE;UAAAlJ,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UAC1CkD,aAAa,CAACkC,IAAI,CAAC,wBAAwB,CAAC;QAC9C,CAAC;UAAArF,aAAA,GAAAyE,CAAA;QAAA;QAAAzE,aAAA,GAAAC,CAAA;QAED,OAAOkD,aAAa;MACtB,CAAC;MAAA,SAvCaC,yBAAyBA,CAAA+F,GAAA,EAAAC,GAAA;QAAA,OAAAL,0BAAA,CAAArG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAzBS,yBAAyB;IAAA;EAAA;IAAAxB,GAAA;IAAAC,KAAA,EAyCvC,SAAQyB,oBAAoBA,CAC1BT,OAA+B,EAC/BuC,QAAqB,EACrBjC,aAAuB,EACf;MAAAnD,aAAA,GAAAyB,CAAA;MACR,IAAIiC,GAAG,IAAA1D,aAAA,GAAAC,CAAA,SAAG,GAAGmF,QAAQ,CAAC1B,GAAG,GAAGb,OAAO,CAAC+B,IAAI,EAAE;MAG1C,IAAMyE,MAAM,IAAArJ,aAAA,GAAAC,CAAA,SAAG,IAAIqJ,eAAe,CAAC,CAAC;MAACtJ,aAAA,GAAAC,CAAA;MAErCkD,aAAa,CAAClB,OAAO,CAAC,UAAAsH,GAAG,EAAI;QAAAvJ,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAC3B,QAAQsJ,GAAG;UACT,KAAK,iBAAiB;YAAAvJ,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACpBoJ,MAAM,CAAClH,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;YAACnC,aAAA,GAAAC,CAAA;YAC7B;UACF,KAAK,mBAAmB;YAAAD,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACtBoJ,MAAM,CAAClH,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC;YAACnC,aAAA,GAAAC,CAAA;YAC5B;UACF,KAAK,sBAAsB;YAAAD,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACzBoJ,MAAM,CAAClH,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC;YAACnC,aAAA,GAAAC,CAAA;YAC5B;UACF,KAAK,mBAAmB;YAAAD,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACtBoJ,MAAM,CAAClH,GAAG,CAAC,UAAU,EAAE,MAAM,CAAC;YAACnC,aAAA,GAAAC,CAAA;YAC/B;UACF,KAAK,qBAAqB;YAAAD,aAAA,GAAAyE,CAAA;YAAAzE,aAAA,GAAAC,CAAA;YACxBoJ,MAAM,CAAClH,GAAG,CAAC,QAAQ,EAAE,MAAM,CAAC;YAACnC,aAAA,GAAAC,CAAA;YAC7B;QACJ;MACF,CAAC,CAAC;MAACD,aAAA,GAAAC,CAAA;MAEH,IAAIoJ,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE;QAAAxJ,aAAA,GAAAyE,CAAA;QAAAzE,aAAA,GAAAC,CAAA;QACrByD,GAAG,IAAI,IAAI2F,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE;MAChC,CAAC;QAAAxJ,aAAA,GAAAyE,CAAA;MAAA;MAAAzE,aAAA,GAAAC,CAAA;MAED,OAAOyD,GAAG;IACZ;EAAC;IAAA9B,GAAA;IAAAC,KAAA;MAAA,IAAA4H,qBAAA,GAAA1H,iBAAA,CAED,WACEc,OAA+B,EAC/B6G,eAA4B,EACT;QAAA1J,aAAA,GAAAyB,CAAA;QACnB,IAAM8B,YAAsB,IAAAvD,aAAA,GAAAC,CAAA,SAAG,EAAE;QACjC,IAAM+F,YAAY,IAAAhG,aAAA,GAAAC,CAAA,SAAG8E,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,iBAAiB,CAAC8E,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;QAGvE,IAAMyE,YAAY,IAAA3J,aAAA,GAAAC,CAAA,SAAG+F,YAAY,CAC9B4D,MAAM,CAAC,UAAAzD,EAAE,EAAI;UAAAnG,aAAA,GAAAyB,CAAA;UAAAzB,aAAA,GAAAC,CAAA;UAAA,QAAAD,aAAA,GAAAyE,CAAA,WAAA0B,EAAE,CAACjE,QAAQ,KAAKwH,eAAe,CAACxH,QAAQ,MAAAlC,aAAA,GAAAyE,CAAA,WAAI0B,EAAE,CAACxC,MAAM,KAAK+F,eAAe,CAAC/F,MAAM;QAAD,CAAC,CAAC,CAC9FiF,IAAI,CAAC,UAACC,CAAC,EAAEpE,CAAC,EAAK;UAAAzE,aAAA,GAAAyB,CAAA;UAAAzB,aAAA,GAAAC,CAAA;UAAA,OAAA4I,CAAC,CAAChF,OAAO,GAAGY,CAAC,CAACZ,OAAO;QAAD,CAAC,CAAC,CACrCgG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAAC7J,aAAA,GAAAC,CAAA;QAEf,KAAK,IAAMmF,QAAQ,IAAIuE,YAAY,EAAE;UACnC,IAAMxG,aAAa,IAAAnD,aAAA,GAAAC,CAAA,eAAS,IAAI,CAACmD,yBAAyB,CAACP,OAAO,EAAEuC,QAAQ,CAAC;UAC7E,IAAM1B,GAAG,IAAA1D,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACqD,oBAAoB,CAACT,OAAO,EAAEuC,QAAQ,EAAEjC,aAAa,CAAC;UAACnD,aAAA,GAAAC,CAAA;UACxEsD,YAAY,CAAC8B,IAAI,CAAC3B,GAAG,CAAC;QACxB;QAAC1D,aAAA,GAAAC,CAAA;QAED,OAAOsD,YAAY;MACrB,CAAC;MAAA,SApBaC,oBAAoBA,CAAAsG,GAAA,EAAAC,GAAA;QAAA,OAAAN,qBAAA,CAAA/G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBa,oBAAoB;IAAA;EAAA;IAAA5B,GAAA;IAAAC,KAAA;MAAA,IAAAmI,mBAAA,GAAAjI,iBAAA,CAsBlC,WACEc,OAA+B,EAC/BuC,QAAqB,EACc;QAAApF,aAAA,GAAAyB,CAAA;QAEnC,IAAMwI,QAAQ,IAAAjK,aAAA,GAAAC,CAAA,SAAG,GAAGmF,QAAQ,CAAClD,QAAQ,IAAIW,OAAO,CAAC+B,IAAI,EAAE;QACvD,IAAMsF,UAAU,IAAAlK,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACG,gBAAgB,CAAC+E,GAAG,CAAC8E,QAAQ,CAAC;QAACjK,aAAA,GAAAC,CAAA;QAEvD,IAAI,CAACiK,UAAU,EAAE;UAAAlK,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UAAA,OAAO,MAAM;QAAA,CAAC;UAAAD,aAAA,GAAAyE,CAAA;QAAA;QAE/B,IAAM0F,eAAe,IAAAnK,aAAA,GAAAC,CAAA,SAAG8C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGkH,UAAU;QAAClK,aAAA,GAAAC,CAAA;QAChD,IAAIkK,eAAe,GAAG,MAAM,EAAE;UAAAnK,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UAAA,OAAO,KAAK;QAAA,CAAC;UAAAD,aAAA,GAAAyE,CAAA;QAAA;QAAAzE,aAAA,GAAAC,CAAA;QAC3C,IAAIkK,eAAe,GAAG,OAAO,EAAE;UAAAnK,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UAAA,OAAO,OAAO;QAAA,CAAC;UAAAD,aAAA,GAAAyE,CAAA;QAAA;QAAAzE,aAAA,GAAAC,CAAA;QAC9C,OAAO,MAAM;MACf,CAAC;MAAA,SAda8D,kBAAkBA,CAAAqG,GAAA,EAAAC,GAAA;QAAA,OAAAL,mBAAA,CAAAtH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBoB,kBAAkB;IAAA;EAAA;IAAAnC,GAAA;IAAAC,KAAA,EAgBhC,SAAQqC,yBAAyBA,CAACrB,OAA+B,EAAkB;MAAA7C,aAAA,GAAAyB,CAAA;MAAAzB,aAAA,GAAAC,CAAA;MACjF,OAAO;QACLyD,GAAG,EAAE,+BAA+Bb,OAAO,CAAC+B,IAAI,EAAE;QAClD1C,QAAQ,EAAE,UAAU;QACpByB,MAAM,EAAE,QAAQ;QAChBC,gBAAgB,EAAE,GAAG;QACrBE,WAAW,EAAE,MAAM;QACnBX,aAAa,EAAE,EAAE;QACjBI,YAAY,EAAE;MAChB,CAAC;IACH;EAAC;IAAA3B,GAAA;IAAAC,KAAA,EAED,SAAQwG,mBAAmBA,CAACiC,QAAiD,EAAU;MAAAtK,aAAA,GAAAyB,CAAA;MAAAzB,aAAA,GAAAC,CAAA;MACrF,IAAI,CAACqK,QAAQ,EAAE;QAAAtK,aAAA,GAAAyE,CAAA;QAAAzE,aAAA,GAAAC,CAAA;QAAA,OAAO,SAAS;MAAA,CAAC;QAAAD,aAAA,GAAAyE,CAAA;MAAA;MAGhC,IAAM8F,SAAiC,IAAAvK,aAAA,GAAAC,CAAA,SAAG;QACxC,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,cAAc;QACpB,IAAI,EAAE,cAAc;QACpB,IAAI,EAAE;MACR,CAAC;MAACD,aAAA,GAAAC,CAAA;MAEF,OAAO,CAAAD,aAAA,GAAAyE,CAAA,WAAA8F,SAAS,CAACD,QAAQ,CAACE,OAAO,CAAC,MAAAxK,aAAA,GAAAyE,CAAA,WAAI,SAAS;IACjD;EAAC;IAAA7C,GAAA;IAAAC,KAAA;MAAA,IAAA4I,kBAAA,GAAA1I,iBAAA,CAED,WACE6C,IAAY,EACZQ,QAAqB,EACrBV,QAAmC,EACpB;QAAA1E,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QACf,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAEFsC,OAAO,CAACC,GAAG,CAAC,cAAcoC,IAAI,OAAOQ,QAAQ,CAAClD,QAAQ,IAAIkD,QAAQ,CAACzB,MAAM,EAAE,CAAC;QAC9E,CAAC,CAAC,OAAOlB,KAAK,EAAE;UAAAzC,aAAA,GAAAC,CAAA;UACdsC,OAAO,CAACE,KAAK,CAAC,qBAAqBmC,IAAI,OAAOQ,QAAQ,CAAClD,QAAQ,GAAG,EAAEO,KAAK,CAAC;QAC5E;MACF,CAAC;MAAA,SAXa6C,iBAAiBA,CAAAoF,GAAA,EAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAH,kBAAA,CAAA/H,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjB2C,iBAAiB;IAAA;EAAA;IAAA1D,GAAA;IAAAC,KAAA,EAa/B,SAAQQ,0BAA0BA,CAAA,EAAS;MAAA,IAAAwI,MAAA;MAAA7K,aAAA,GAAAyB,CAAA;MAAAzB,aAAA,GAAAC,CAAA;MAEzC6K,WAAW,CAAC,YAAM;QAAA9K,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAChB4K,MAAI,CAACE,qBAAqB,CAAC,CAAC;MAC9B,CAAC,EAAE,MAAM,CAAC;IACZ;EAAC;IAAAnJ,GAAA;IAAAC,KAAA;MAAA,IAAAmJ,sBAAA,GAAAjJ,iBAAA,CAED,aAAqD;QAAA/B,aAAA,GAAAyB,CAAA;QACnD,IAAMuE,YAAY,IAAAhG,aAAA,GAAAC,CAAA,SAAG8E,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC7E,iBAAiB,CAAC8E,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;QAAClF,aAAA,GAAAC,CAAA;QAExE,KAAK,IAAMmF,QAAQ,IAAIY,YAAY,EAAE;UAAAhG,aAAA,GAAAC,CAAA;UACnC,IAAI;YACF,IAAM4D,OAAO,IAAA7D,aAAA,GAAAC,CAAA,eAAS,IAAI,CAACgL,sBAAsB,CAAC7F,QAAQ,CAAC;YAACpF,aAAA,GAAAC,CAAA;YAC5D,IAAI,CAACG,gBAAgB,CAAC+B,GAAG,CAAC,GAAGiD,QAAQ,CAAClD,QAAQ,IAAIkD,QAAQ,CAACzB,MAAM,EAAE,EAAEE,OAAO,CAAC;YAAC7D,aAAA,GAAAC,CAAA;YAC9EmF,QAAQ,CAACvB,OAAO,GAAGA,OAAO;YAAC7D,aAAA,GAAAC,CAAA;YAC3BmF,QAAQ,CAAC2C,UAAU,GAAGhF,IAAI,CAACC,GAAG,CAAC,CAAC;UAClC,CAAC,CAAC,OAAOP,KAAK,EAAE;YAAAzC,aAAA,GAAAC,CAAA;YACdsC,OAAO,CAACE,KAAK,CAAC,qBAAqB2C,QAAQ,CAAClD,QAAQ,IAAIkD,QAAQ,CAACzB,MAAM,GAAG,EAAElB,KAAK,CAAC;UACpF;QACF;MACF,CAAC;MAAA,SAbasI,qBAAqBA,CAAA;QAAA,OAAAC,sBAAA,CAAAtI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBoI,qBAAqB;IAAA;EAAA;IAAAnJ,GAAA;IAAAC,KAAA;MAAA,IAAAqJ,uBAAA,GAAAnJ,iBAAA,CAenC,WAAqCqD,QAAqB,EAAmB;QAAApF,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAE3E,OAAOmF,QAAQ,CAACvB,OAAO,GAAG,CAACgE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;MACtD,CAAC;MAAA,SAHamD,sBAAsBA,CAAAE,IAAA;QAAA,OAAAD,uBAAA,CAAAxI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBsI,sBAAsB;IAAA;EAAA;IAAArJ,GAAA;IAAAC,KAAA;MAAA,IAAAuJ,wBAAA,GAAArJ,iBAAA,CAKpC,WAAsCqD,QAAqB,EAIxD;QAAApF,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QACD,IAAI;UACF,IAAM4D,OAAO,IAAA7D,aAAA,GAAAC,CAAA,eAAS,IAAI,CAACgL,sBAAsB,CAAC7F,QAAQ,CAAC;UAACpF,aAAA,GAAAC,CAAA;UAC5D,OAAO;YAAEmF,QAAQ,EAARA,QAAQ;YAAEvB,OAAO,EAAPA,OAAO;YAAEwH,OAAO,EAAE;UAAK,CAAC;QAC7C,CAAC,CAAC,OAAO5I,KAAK,EAAE;UAAAzC,aAAA,GAAAC,CAAA;UACd,OAAO;YAAEmF,QAAQ,EAARA,QAAQ;YAAEvB,OAAO,EAAE,IAAI;YAAEwH,OAAO,EAAE;UAAM,CAAC;QACpD;MACF,CAAC;MAAA,SAXahE,uBAAuBA,CAAAiE,IAAA;QAAA,OAAAF,wBAAA,CAAA1I,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAvB0E,uBAAuB;IAAA;EAAA;IAAAzF,GAAA;IAAAC,KAAA;MAAA,IAAA0J,mBAAA,GAAAxJ,iBAAA,CAarC,WAAiCuF,WAAwC,EAAiB;QAAA,IAAAkE,MAAA;QAAAxL,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAExFqH,WAAW,CAACrF,OAAO,CAAC,UAAAwB,MAAM,EAAI;UAAAzD,aAAA,GAAAyB,CAAA;UAAAzB,aAAA,GAAAC,CAAA;UAC5B,IAAI,CAAAD,aAAA,GAAAyE,CAAA,WAAAhB,MAAM,CAACgI,MAAM,KAAK,WAAW,MAAAzL,aAAA,GAAAyE,CAAA,WAAIhB,MAAM,CAAC5B,KAAK,CAACwJ,OAAO,GAAE;YAAArL,aAAA,GAAAyE,CAAA;YACzD,IAAAiH,KAAA,IAAA1L,aAAA,GAAAC,CAAA,SAA8BwD,MAAM,CAAC5B,KAAK;cAAlCuD,QAAQ,GAAAsG,KAAA,CAARtG,QAAQ;cAAEvB,OAAO,GAAA6H,KAAA,CAAP7H,OAAO;YAAkB7D,aAAA,GAAAC,CAAA;YAC3CuL,MAAI,CAACnL,YAAY,CAAC8B,GAAG,CAAC,GAAGiD,QAAQ,CAAClD,QAAQ,IAAIkD,QAAQ,CAACzB,MAAM,EAAE,EAAEE,OAAO,CAAC2F,QAAQ,CAAC,CAAC,CAAC;UACtF,CAAC;YAAAxJ,aAAA,GAAAyE,CAAA;UAAA;QACH,CAAC,CAAC;MACJ,CAAC;MAAA,SARa8C,kBAAkBA,CAAAoE,IAAA;QAAA,OAAAJ,mBAAA,CAAA7I,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlB4E,kBAAkB;IAAA;EAAA;IAAA3F,GAAA;IAAAC,KAAA;MAAA,IAAA+J,iCAAA,GAAA7J,iBAAA,CAUhC,WAA+CyF,eAAoB,EAAiB;QAAAxH,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAElF,IAAIuH,eAAe,CAACqE,OAAO,CAAChI,OAAO,GAAG,GAAG,EAAE;UAAA7D,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UAEzCsC,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAC;QAC7D,CAAC;UAAAxC,aAAA,GAAAyE,CAAA;QAAA;QAAAzE,aAAA,GAAAC,CAAA;QAED,IAAIuH,eAAe,CAACsE,OAAO,CAACC,KAAK,GAAG,EAAE,EAAE;UAAA/L,aAAA,GAAAyE,CAAA;UAAAzE,aAAA,GAAAC,CAAA;UAEtCsC,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC5D,CAAC;UAAAxC,aAAA,GAAAyE,CAAA;QAAA;MACH,CAAC;MAAA,SAXaiD,gCAAgCA,CAAAsE,IAAA;QAAA,OAAAJ,iCAAA,CAAAlJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhC+E,gCAAgC;IAAA;EAAA;IAAA9F,GAAA;IAAAC,KAAA;MAAA,IAAAoK,qBAAA,GAAAlK,iBAAA,CAa9C,aAAoD;QAAA/B,aAAA,GAAAyB,CAAA;QAAAzB,aAAA,GAAAC,CAAA;QAElDsC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD,CAAC;MAAA,SAHaF,oBAAoBA,CAAA;QAAA,OAAA2J,qBAAA,CAAAvJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBL,oBAAoB;IAAA;EAAA;AAAA;AAOpC,OAAO,IAAM4J,gBAAgB,IAAAlM,aAAA,GAAAC,CAAA,SAAG,IAAIJ,gBAAgB,CAAC,CAAC;AACtD,eAAeqM,gBAAgB", "ignoreList": []}