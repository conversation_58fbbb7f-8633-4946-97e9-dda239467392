{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "preprocess", "createTransformValue", "createTransformOriginValue", "createTextShadowValue", "createBoxShadowValue", "createBoxShadowArrayValue", "_normalizeColor", "_normalizeValueWithProperty", "_warnOnce", "emptyObject", "defaultOffset", "height", "width", "style", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "_ref", "offsetX", "offsetY", "blurRadius", "color", "textShadowColor", "textShadowOffset", "textShadowRadius", "_ref2", "radius", "mapBoxShadow", "boxShadow", "spreadDistance", "position", "inset", "value", "map", "join", "mapTransform", "transform", "type", "Object", "keys", "normalizedValue", "v", "PROPERTIES_STANDARD", "borderBottomEndRadius", "borderBottomStartRadius", "borderTopEndRadius", "borderTopStartRadius", "borderEndColor", "borderEndStyle", "borderEndWidth", "borderStartColor", "borderStartStyle", "borderStartWidth", "end", "marginEnd", "marginHorizontal", "marginStart", "marginVertical", "paddingEnd", "paddingHorizontal", "paddingStart", "paddingVertical", "start", "ignoredProps", "elevation", "overlayColor", "resizeMode", "tintColor", "originalStyle", "options", "nextStyle", "shadow", "warnOnce", "boxShadowValue", "textShadow", "textShadowValue", "originalProp", "originalValue", "prop", "_value", "prototype", "hasOwnProperty", "call", "toString", "Array", "isArray", "length", "verticalAlign", "transform<PERSON><PERSON>in", "_default"], "sources": ["preprocess.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.preprocess = exports.default = exports.createTransformValue = exports.createTransformOriginValue = exports.createTextShadowValue = exports.createBoxShadowValue = exports.createBoxShadowArrayValue = void 0;\nvar _normalizeColor = _interopRequireDefault(require(\"./compiler/normalizeColor\"));\nvar _normalizeValueWithProperty = _interopRequireDefault(require(\"./compiler/normalizeValueWithProperty\"));\nvar _warnOnce = require(\"../../modules/warnOnce\");\n/**\n * Copyright (c) Nicolas <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar emptyObject = {};\n\n/**\n * Shadows\n */\n\nvar defaultOffset = {\n  height: 0,\n  width: 0\n};\nvar createBoxShadowValue = style => {\n  var shadowColor = style.shadowColor,\n    shadowOffset = style.shadowOffset,\n    shadowOpacity = style.shadowOpacity,\n    shadowRadius = style.shadowRadius;\n  var _ref = shadowOffset || defaultOffset,\n    height = _ref.height,\n    width = _ref.width;\n  var offsetX = (0, _normalizeValueWithProperty.default)(width);\n  var offsetY = (0, _normalizeValueWithProperty.default)(height);\n  var blurRadius = (0, _normalizeValueWithProperty.default)(shadowRadius || 0);\n  var color = (0, _normalizeColor.default)(shadowColor || 'black', shadowOpacity);\n  if (color != null && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\nexports.createBoxShadowValue = createBoxShadowValue;\nvar createTextShadowValue = style => {\n  var textShadowColor = style.textShadowColor,\n    textShadowOffset = style.textShadowOffset,\n    textShadowRadius = style.textShadowRadius;\n  var _ref2 = textShadowOffset || defaultOffset,\n    height = _ref2.height,\n    width = _ref2.width;\n  var radius = textShadowRadius || 0;\n  var offsetX = (0, _normalizeValueWithProperty.default)(width);\n  var offsetY = (0, _normalizeValueWithProperty.default)(height);\n  var blurRadius = (0, _normalizeValueWithProperty.default)(radius);\n  var color = (0, _normalizeValueWithProperty.default)(textShadowColor, 'textShadowColor');\n  if (color && (height !== 0 || width !== 0 || radius !== 0) && offsetX != null && offsetY != null && blurRadius != null) {\n    return offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + color;\n  }\n};\n\n// { offsetX: 1, offsetY: 2, blurRadius: 3, spreadDistance: 4, color: 'rgba(255, 0, 0)', inset: true }\n// => 'rgba(255, 0, 0) 1px 2px 3px 4px inset'\nexports.createTextShadowValue = createTextShadowValue;\nvar mapBoxShadow = boxShadow => {\n  if (typeof boxShadow === 'string') {\n    return boxShadow;\n  }\n  var offsetX = (0, _normalizeValueWithProperty.default)(boxShadow.offsetX) || 0;\n  var offsetY = (0, _normalizeValueWithProperty.default)(boxShadow.offsetY) || 0;\n  var blurRadius = (0, _normalizeValueWithProperty.default)(boxShadow.blurRadius) || 0;\n  var spreadDistance = (0, _normalizeValueWithProperty.default)(boxShadow.spreadDistance) || 0;\n  var color = (0, _normalizeColor.default)(boxShadow.color) || 'black';\n  var position = boxShadow.inset ? 'inset ' : '';\n  return \"\" + position + offsetX + \" \" + offsetY + \" \" + blurRadius + \" \" + spreadDistance + \" \" + color;\n};\nvar createBoxShadowArrayValue = value => {\n  return value.map(mapBoxShadow).join(', ');\n};\n\n// { scale: 2 } => 'scale(2)'\n// { translateX: 20 } => 'translateX(20px)'\n// { matrix: [1,2,3,4,5,6] } => 'matrix(1,2,3,4,5,6)'\nexports.createBoxShadowArrayValue = createBoxShadowArrayValue;\nvar mapTransform = transform => {\n  var type = Object.keys(transform)[0];\n  var value = transform[type];\n  if (type === 'matrix' || type === 'matrix3d') {\n    return type + \"(\" + value.join(',') + \")\";\n  } else {\n    var normalizedValue = (0, _normalizeValueWithProperty.default)(value, type);\n    return type + \"(\" + normalizedValue + \")\";\n  }\n};\nvar createTransformValue = value => {\n  return value.map(mapTransform).join(' ');\n};\n\n// [2, '30%', 10] => '2px 30% 10px'\nexports.createTransformValue = createTransformValue;\nvar createTransformOriginValue = value => {\n  return value.map(v => (0, _normalizeValueWithProperty.default)(v)).join(' ');\n};\nexports.createTransformOriginValue = createTransformOriginValue;\nvar PROPERTIES_STANDARD = {\n  borderBottomEndRadius: 'borderEndEndRadius',\n  borderBottomStartRadius: 'borderEndStartRadius',\n  borderTopEndRadius: 'borderStartEndRadius',\n  borderTopStartRadius: 'borderStartStartRadius',\n  borderEndColor: 'borderInlineEndColor',\n  borderEndStyle: 'borderInlineEndStyle',\n  borderEndWidth: 'borderInlineEndWidth',\n  borderStartColor: 'borderInlineStartColor',\n  borderStartStyle: 'borderInlineStartStyle',\n  borderStartWidth: 'borderInlineStartWidth',\n  end: 'insetInlineEnd',\n  marginEnd: 'marginInlineEnd',\n  marginHorizontal: 'marginInline',\n  marginStart: 'marginInlineStart',\n  marginVertical: 'marginBlock',\n  paddingEnd: 'paddingInlineEnd',\n  paddingHorizontal: 'paddingInline',\n  paddingStart: 'paddingInlineStart',\n  paddingVertical: 'paddingBlock',\n  start: 'insetInlineStart'\n};\nvar ignoredProps = {\n  elevation: true,\n  overlayColor: true,\n  resizeMode: true,\n  tintColor: true\n};\n\n/**\n * Preprocess styles\n */\nvar preprocess = exports.preprocess = function preprocess(originalStyle, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var style = originalStyle || emptyObject;\n  var nextStyle = {};\n\n  // Convert shadow styles\n  if (options.shadow === true, style.shadowColor != null || style.shadowOffset != null || style.shadowOpacity != null || style.shadowRadius != null) {\n    (0, _warnOnce.warnOnce)('shadowStyles', \"\\\"shadow*\\\" style props are deprecated. Use \\\"boxShadow\\\".\");\n    var boxShadowValue = createBoxShadowValue(style);\n    if (boxShadowValue != null) {\n      nextStyle.boxShadow = boxShadowValue;\n    }\n  }\n\n  // Convert text shadow styles\n  if (options.textShadow === true, style.textShadowColor != null || style.textShadowOffset != null || style.textShadowRadius != null) {\n    (0, _warnOnce.warnOnce)('textShadowStyles', \"\\\"textShadow*\\\" style props are deprecated. Use \\\"textShadow\\\".\");\n    var textShadowValue = createTextShadowValue(style);\n    if (textShadowValue != null && nextStyle.textShadow == null) {\n      var textShadow = style.textShadow;\n      var value = textShadow ? textShadow + \", \" + textShadowValue : textShadowValue;\n      nextStyle.textShadow = value;\n    }\n  }\n  for (var originalProp in style) {\n    if (\n    // Ignore some React Native styles\n    ignoredProps[originalProp] != null || originalProp === 'shadowColor' || originalProp === 'shadowOffset' || originalProp === 'shadowOpacity' || originalProp === 'shadowRadius' || originalProp === 'textShadowColor' || originalProp === 'textShadowOffset' || originalProp === 'textShadowRadius') {\n      continue;\n    }\n    var originalValue = style[originalProp];\n    var prop = PROPERTIES_STANDARD[originalProp] || originalProp;\n    var _value = originalValue;\n    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || prop !== originalProp && style[prop] != null) {\n      continue;\n    }\n    if (prop === 'aspectRatio' && typeof _value === 'number') {\n      nextStyle[prop] = _value.toString();\n    } else if (prop === 'boxShadow') {\n      if (Array.isArray(_value)) {\n        _value = createBoxShadowArrayValue(_value);\n      }\n      var boxShadow = nextStyle.boxShadow;\n      nextStyle.boxShadow = boxShadow ? _value + \", \" + boxShadow : _value;\n    } else if (prop === 'fontVariant') {\n      if (Array.isArray(_value) && _value.length > 0) {\n        /*\n        warnOnce(\n          'fontVariant',\n          '\"fontVariant\" style array value is deprecated. Use space-separated values.'\n        );\n        */\n        _value = _value.join(' ');\n      }\n      nextStyle[prop] = _value;\n    } else if (prop === 'textAlignVertical') {\n      /*\n      warnOnce(\n        'textAlignVertical',\n        '\"textAlignVertical\" style is deprecated. Use \"verticalAlign\".'\n      );\n      */\n      if (style.verticalAlign == null) {\n        nextStyle.verticalAlign = _value === 'center' ? 'middle' : _value;\n      }\n    } else if (prop === 'transform') {\n      if (Array.isArray(_value)) {\n        _value = createTransformValue(_value);\n      }\n      nextStyle.transform = _value;\n    } else if (prop === 'transformOrigin') {\n      if (Array.isArray(_value)) {\n        _value = createTransformOriginValue(_value);\n      }\n      nextStyle.transformOrigin = _value;\n    } else {\n      nextStyle[prop] = _value;\n    }\n  }\n\n  // $FlowIgnore\n  return nextStyle;\n};\nvar _default = exports.default = preprocess;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,UAAU,GAAGF,OAAO,CAACD,OAAO,GAAGC,OAAO,CAACG,oBAAoB,GAAGH,OAAO,CAACI,0BAA0B,GAAGJ,OAAO,CAACK,qBAAqB,GAAGL,OAAO,CAACM,oBAAoB,GAAGN,OAAO,CAACO,yBAAyB,GAAG,KAAK,CAAC;AACpN,IAAIC,eAAe,GAAGX,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAClF,IAAIW,2BAA2B,GAAGZ,sBAAsB,CAACC,OAAO,wCAAwC,CAAC,CAAC;AAC1G,IAAIY,SAAS,GAAGZ,OAAO,yBAAyB,CAAC;AAUjD,IAAIa,WAAW,GAAG,CAAC,CAAC;AAMpB,IAAIC,aAAa,GAAG;EAClBC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE;AACT,CAAC;AACD,IAAIR,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAGS,KAAK,EAAI;EAClC,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;IACjCC,YAAY,GAAGF,KAAK,CAACE,YAAY;IACjCC,aAAa,GAAGH,KAAK,CAACG,aAAa;IACnCC,YAAY,GAAGJ,KAAK,CAACI,YAAY;EACnC,IAAIC,IAAI,GAAGH,YAAY,IAAIL,aAAa;IACtCC,MAAM,GAAGO,IAAI,CAACP,MAAM;IACpBC,KAAK,GAAGM,IAAI,CAACN,KAAK;EACpB,IAAIO,OAAO,GAAG,CAAC,CAAC,EAAEZ,2BAA2B,CAACV,OAAO,EAAEe,KAAK,CAAC;EAC7D,IAAIQ,OAAO,GAAG,CAAC,CAAC,EAAEb,2BAA2B,CAACV,OAAO,EAAEc,MAAM,CAAC;EAC9D,IAAIU,UAAU,GAAG,CAAC,CAAC,EAAEd,2BAA2B,CAACV,OAAO,EAAEoB,YAAY,IAAI,CAAC,CAAC;EAC5E,IAAIK,KAAK,GAAG,CAAC,CAAC,EAAEhB,eAAe,CAACT,OAAO,EAAEiB,WAAW,IAAI,OAAO,EAAEE,aAAa,CAAC;EAC/E,IAAIM,KAAK,IAAI,IAAI,IAAIH,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;IAC7E,OAAOF,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGC,KAAK;EACjE;AACF,CAAC;AACDxB,OAAO,CAACM,oBAAoB,GAAGA,oBAAoB;AACnD,IAAID,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAGU,KAAK,EAAI;EACnC,IAAIU,eAAe,GAAGV,KAAK,CAACU,eAAe;IACzCC,gBAAgB,GAAGX,KAAK,CAACW,gBAAgB;IACzCC,gBAAgB,GAAGZ,KAAK,CAACY,gBAAgB;EAC3C,IAAIC,KAAK,GAAGF,gBAAgB,IAAId,aAAa;IAC3CC,MAAM,GAAGe,KAAK,CAACf,MAAM;IACrBC,KAAK,GAAGc,KAAK,CAACd,KAAK;EACrB,IAAIe,MAAM,GAAGF,gBAAgB,IAAI,CAAC;EAClC,IAAIN,OAAO,GAAG,CAAC,CAAC,EAAEZ,2BAA2B,CAACV,OAAO,EAAEe,KAAK,CAAC;EAC7D,IAAIQ,OAAO,GAAG,CAAC,CAAC,EAAEb,2BAA2B,CAACV,OAAO,EAAEc,MAAM,CAAC;EAC9D,IAAIU,UAAU,GAAG,CAAC,CAAC,EAAEd,2BAA2B,CAACV,OAAO,EAAE8B,MAAM,CAAC;EACjE,IAAIL,KAAK,GAAG,CAAC,CAAC,EAAEf,2BAA2B,CAACV,OAAO,EAAE0B,eAAe,EAAE,iBAAiB,CAAC;EACxF,IAAID,KAAK,KAAKX,MAAM,KAAK,CAAC,IAAIC,KAAK,KAAK,CAAC,IAAIe,MAAM,KAAK,CAAC,CAAC,IAAIR,OAAO,IAAI,IAAI,IAAIC,OAAO,IAAI,IAAI,IAAIC,UAAU,IAAI,IAAI,EAAE;IACtH,OAAOF,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGC,KAAK;EACjE;AACF,CAAC;AAIDxB,OAAO,CAACK,qBAAqB,GAAGA,qBAAqB;AACrD,IAAIyB,YAAY,GAAG,SAAfA,YAAYA,CAAGC,SAAS,EAAI;EAC9B,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;IACjC,OAAOA,SAAS;EAClB;EACA,IAAIV,OAAO,GAAG,CAAC,CAAC,EAAEZ,2BAA2B,CAACV,OAAO,EAAEgC,SAAS,CAACV,OAAO,CAAC,IAAI,CAAC;EAC9E,IAAIC,OAAO,GAAG,CAAC,CAAC,EAAEb,2BAA2B,CAACV,OAAO,EAAEgC,SAAS,CAACT,OAAO,CAAC,IAAI,CAAC;EAC9E,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAEd,2BAA2B,CAACV,OAAO,EAAEgC,SAAS,CAACR,UAAU,CAAC,IAAI,CAAC;EACpF,IAAIS,cAAc,GAAG,CAAC,CAAC,EAAEvB,2BAA2B,CAACV,OAAO,EAAEgC,SAAS,CAACC,cAAc,CAAC,IAAI,CAAC;EAC5F,IAAIR,KAAK,GAAG,CAAC,CAAC,EAAEhB,eAAe,CAACT,OAAO,EAAEgC,SAAS,CAACP,KAAK,CAAC,IAAI,OAAO;EACpE,IAAIS,QAAQ,GAAGF,SAAS,CAACG,KAAK,GAAG,QAAQ,GAAG,EAAE;EAC9C,OAAO,EAAE,GAAGD,QAAQ,GAAGZ,OAAO,GAAG,GAAG,GAAGC,OAAO,GAAG,GAAG,GAAGC,UAAU,GAAG,GAAG,GAAGS,cAAc,GAAG,GAAG,GAAGR,KAAK;AACxG,CAAC;AACD,IAAIjB,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAG4B,KAAK,EAAI;EACvC,OAAOA,KAAK,CAACC,GAAG,CAACN,YAAY,CAAC,CAACO,IAAI,CAAC,IAAI,CAAC;AAC3C,CAAC;AAKDrC,OAAO,CAACO,yBAAyB,GAAGA,yBAAyB;AAC7D,IAAI+B,YAAY,GAAG,SAAfA,YAAYA,CAAGC,SAAS,EAAI;EAC9B,IAAIC,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC;EACpC,IAAIJ,KAAK,GAAGI,SAAS,CAACC,IAAI,CAAC;EAC3B,IAAIA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,EAAE;IAC5C,OAAOA,IAAI,GAAG,GAAG,GAAGL,KAAK,CAACE,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC3C,CAAC,MAAM;IACL,IAAIM,eAAe,GAAG,CAAC,CAAC,EAAElC,2BAA2B,CAACV,OAAO,EAAEoC,KAAK,EAAEK,IAAI,CAAC;IAC3E,OAAOA,IAAI,GAAG,GAAG,GAAGG,eAAe,GAAG,GAAG;EAC3C;AACF,CAAC;AACD,IAAIxC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAGgC,KAAK,EAAI;EAClC,OAAOA,KAAK,CAACC,GAAG,CAACE,YAAY,CAAC,CAACD,IAAI,CAAC,GAAG,CAAC;AAC1C,CAAC;AAGDrC,OAAO,CAACG,oBAAoB,GAAGA,oBAAoB;AACnD,IAAIC,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAG+B,KAAK,EAAI;EACxC,OAAOA,KAAK,CAACC,GAAG,CAAC,UAAAQ,CAAC;IAAA,OAAI,CAAC,CAAC,EAAEnC,2BAA2B,CAACV,OAAO,EAAE6C,CAAC,CAAC;EAAA,EAAC,CAACP,IAAI,CAAC,GAAG,CAAC;AAC9E,CAAC;AACDrC,OAAO,CAACI,0BAA0B,GAAGA,0BAA0B;AAC/D,IAAIyC,mBAAmB,GAAG;EACxBC,qBAAqB,EAAE,oBAAoB;EAC3CC,uBAAuB,EAAE,sBAAsB;EAC/CC,kBAAkB,EAAE,sBAAsB;EAC1CC,oBAAoB,EAAE,wBAAwB;EAC9CC,cAAc,EAAE,sBAAsB;EACtCC,cAAc,EAAE,sBAAsB;EACtCC,cAAc,EAAE,sBAAsB;EACtCC,gBAAgB,EAAE,wBAAwB;EAC1CC,gBAAgB,EAAE,wBAAwB;EAC1CC,gBAAgB,EAAE,wBAAwB;EAC1CC,GAAG,EAAE,gBAAgB;EACrBC,SAAS,EAAE,iBAAiB;EAC5BC,gBAAgB,EAAE,cAAc;EAChCC,WAAW,EAAE,mBAAmB;EAChCC,cAAc,EAAE,aAAa;EAC7BC,UAAU,EAAE,kBAAkB;EAC9BC,iBAAiB,EAAE,eAAe;EAClCC,YAAY,EAAE,oBAAoB;EAClCC,eAAe,EAAE,cAAc;EAC/BC,KAAK,EAAE;AACT,CAAC;AACD,IAAIC,YAAY,GAAG;EACjBC,SAAS,EAAE,IAAI;EACfC,YAAY,EAAE,IAAI;EAClBC,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE;AACb,CAAC;AAKD,IAAIpE,UAAU,GAAGF,OAAO,CAACE,UAAU,GAAG,SAASA,UAAUA,CAACqE,aAAa,EAAEC,OAAO,EAAE;EAChF,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIzD,KAAK,GAAGwD,aAAa,IAAI5D,WAAW;EACxC,IAAI8D,SAAS,GAAG,CAAC,CAAC;EAGlB,IAAID,OAAO,CAACE,MAAM,KAAK,IAAI,EAAE3D,KAAK,CAACC,WAAW,IAAI,IAAI,IAAID,KAAK,CAACE,YAAY,IAAI,IAAI,IAAIF,KAAK,CAACG,aAAa,IAAI,IAAI,IAAIH,KAAK,CAACI,YAAY,IAAI,IAAI,EAAE;IACjJ,CAAC,CAAC,EAAET,SAAS,CAACiE,QAAQ,EAAE,cAAc,EAAE,4DAA4D,CAAC;IACrG,IAAIC,cAAc,GAAGtE,oBAAoB,CAACS,KAAK,CAAC;IAChD,IAAI6D,cAAc,IAAI,IAAI,EAAE;MAC1BH,SAAS,CAAC1C,SAAS,GAAG6C,cAAc;IACtC;EACF;EAGA,IAAIJ,OAAO,CAACK,UAAU,KAAK,IAAI,EAAE9D,KAAK,CAACU,eAAe,IAAI,IAAI,IAAIV,KAAK,CAACW,gBAAgB,IAAI,IAAI,IAAIX,KAAK,CAACY,gBAAgB,IAAI,IAAI,EAAE;IAClI,CAAC,CAAC,EAAEjB,SAAS,CAACiE,QAAQ,EAAE,kBAAkB,EAAE,iEAAiE,CAAC;IAC9G,IAAIG,eAAe,GAAGzE,qBAAqB,CAACU,KAAK,CAAC;IAClD,IAAI+D,eAAe,IAAI,IAAI,IAAIL,SAAS,CAACI,UAAU,IAAI,IAAI,EAAE;MAC3D,IAAIA,UAAU,GAAG9D,KAAK,CAAC8D,UAAU;MACjC,IAAI1C,KAAK,GAAG0C,UAAU,GAAGA,UAAU,GAAG,IAAI,GAAGC,eAAe,GAAGA,eAAe;MAC9EL,SAAS,CAACI,UAAU,GAAG1C,KAAK;IAC9B;EACF;EACA,KAAK,IAAI4C,YAAY,IAAIhE,KAAK,EAAE;IAC9B,IAEAmD,YAAY,CAACa,YAAY,CAAC,IAAI,IAAI,IAAIA,YAAY,KAAK,aAAa,IAAIA,YAAY,KAAK,cAAc,IAAIA,YAAY,KAAK,eAAe,IAAIA,YAAY,KAAK,cAAc,IAAIA,YAAY,KAAK,iBAAiB,IAAIA,YAAY,KAAK,kBAAkB,IAAIA,YAAY,KAAK,kBAAkB,EAAE;MAClS;IACF;IACA,IAAIC,aAAa,GAAGjE,KAAK,CAACgE,YAAY,CAAC;IACvC,IAAIE,IAAI,GAAGpC,mBAAmB,CAACkC,YAAY,CAAC,IAAIA,YAAY;IAC5D,IAAIG,MAAM,GAAGF,aAAa;IAC1B,IAAI,CAACvC,MAAM,CAAC0C,SAAS,CAACC,cAAc,CAACC,IAAI,CAACtE,KAAK,EAAEgE,YAAY,CAAC,IAAIE,IAAI,KAAKF,YAAY,IAAIhE,KAAK,CAACkE,IAAI,CAAC,IAAI,IAAI,EAAE;MAC9G;IACF;IACA,IAAIA,IAAI,KAAK,aAAa,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;MACxDT,SAAS,CAACQ,IAAI,CAAC,GAAGC,MAAM,CAACI,QAAQ,CAAC,CAAC;IACrC,CAAC,MAAM,IAAIL,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;QACzBA,MAAM,GAAG3E,yBAAyB,CAAC2E,MAAM,CAAC;MAC5C;MACA,IAAInD,SAAS,GAAG0C,SAAS,CAAC1C,SAAS;MACnC0C,SAAS,CAAC1C,SAAS,GAAGA,SAAS,GAAGmD,MAAM,GAAG,IAAI,GAAGnD,SAAS,GAAGmD,MAAM;IACtE,CAAC,MAAM,IAAID,IAAI,KAAK,aAAa,EAAE;MACjC,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,IAAIA,MAAM,CAACO,MAAM,GAAG,CAAC,EAAE;QAO9CP,MAAM,GAAGA,MAAM,CAAC7C,IAAI,CAAC,GAAG,CAAC;MAC3B;MACAoC,SAAS,CAACQ,IAAI,CAAC,GAAGC,MAAM;IAC1B,CAAC,MAAM,IAAID,IAAI,KAAK,mBAAmB,EAAE;MAOvC,IAAIlE,KAAK,CAAC2E,aAAa,IAAI,IAAI,EAAE;QAC/BjB,SAAS,CAACiB,aAAa,GAAGR,MAAM,KAAK,QAAQ,GAAG,QAAQ,GAAGA,MAAM;MACnE;IACF,CAAC,MAAM,IAAID,IAAI,KAAK,WAAW,EAAE;MAC/B,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;QACzBA,MAAM,GAAG/E,oBAAoB,CAAC+E,MAAM,CAAC;MACvC;MACAT,SAAS,CAAClC,SAAS,GAAG2C,MAAM;IAC9B,CAAC,MAAM,IAAID,IAAI,KAAK,iBAAiB,EAAE;MACrC,IAAIM,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,EAAE;QACzBA,MAAM,GAAG9E,0BAA0B,CAAC8E,MAAM,CAAC;MAC7C;MACAT,SAAS,CAACkB,eAAe,GAAGT,MAAM;IACpC,CAAC,MAAM;MACLT,SAAS,CAACQ,IAAI,CAAC,GAAGC,MAAM;IAC1B;EACF;EAGA,OAAOT,SAAS;AAClB,CAAC;AACD,IAAImB,QAAQ,GAAG5F,OAAO,CAACD,OAAO,GAAGG,UAAU", "ignoreList": []}