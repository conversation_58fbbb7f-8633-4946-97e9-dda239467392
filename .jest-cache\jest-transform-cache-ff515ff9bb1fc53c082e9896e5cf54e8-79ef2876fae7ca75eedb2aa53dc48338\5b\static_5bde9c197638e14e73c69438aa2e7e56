12da3fb32d1fc2df3943b51fbfcab351
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _crossFade = _interopRequireDefault(require("inline-style-prefixer/lib/plugins/crossFade"));
var _imageSet = _interopRequireDefault(require("inline-style-prefixer/lib/plugins/imageSet"));
var _logical = _interopRequireDefault(require("inline-style-prefixer/lib/plugins/logical"));
var _position = _interopRequireDefault(require("inline-style-prefixer/lib/plugins/position"));
var _sizing = _interopRequireDefault(require("inline-style-prefixer/lib/plugins/sizing"));
var _transition = _interopRequireDefault(require("inline-style-prefixer/lib/plugins/transition"));
var w = ['Webkit'];
var m = ['Moz'];
var wm = ['Webkit', 'Moz'];
var wms = ['Webkit', 'ms'];
var wmms = ['Webkit', 'Moz', 'ms'];
var _default = exports.default = {
  plugins: [_crossFade.default, _imageSet.default, _logical.default, _position.default, _sizing.default, _transition.default],
  prefixMap: {
    appearance: wmms,
    userSelect: wm,
    textEmphasisPosition: wms,
    textEmphasis: wms,
    textEmphasisStyle: wms,
    textEmphasisColor: wms,
    boxDecorationBreak: wms,
    clipPath: w,
    maskImage: wms,
    maskMode: wms,
    maskRepeat: wms,
    maskPosition: wms,
    maskClip: wms,
    maskOrigin: wms,
    maskSize: wms,
    maskComposite: wms,
    mask: wms,
    maskBorderSource: wms,
    maskBorderMode: wms,
    maskBorderSlice: wms,
    maskBorderWidth: wms,
    maskBorderOutset: wms,
    maskBorderRepeat: wms,
    maskBorder: wms,
    maskType: wms,
    textDecorationStyle: w,
    textDecorationSkip: w,
    textDecorationLine: w,
    textDecorationColor: w,
    filter: w,
    breakAfter: w,
    breakBefore: w,
    breakInside: w,
    columnCount: w,
    columnFill: w,
    columnGap: w,
    columnRule: w,
    columnRuleColor: w,
    columnRuleStyle: w,
    columnRuleWidth: w,
    columns: w,
    columnSpan: w,
    columnWidth: w,
    backdropFilter: w,
    hyphens: w,
    flowInto: w,
    flowFrom: w,
    regionFragment: w,
    textOrientation: w,
    tabSize: m,
    fontKerning: w,
    textSizeAdjust: w
  }
};
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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