3a38c562c177e8f67c57dea7bdcecc3a
function cov_b7a5bk17a() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\AuthGuard.tsx";
  var hash = "52cc9653a8d8d3f4fd9a41ab07000e301e87a3f6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\AuthGuard.tsx",
    statementMap: {
      "0": {
        start: {
          line: 11,
          column: 28
        },
        end: {
          line: 11,
          column: 37
        }
      },
      "1": {
        start: {
          line: 12,
          column: 19
        },
        end: {
          line: 12,
          column: 32
        }
      },
      "2": {
        start: {
          line: 14,
          column: 2
        },
        end: {
          line: 30,
          column: 32
        }
      },
      "3": {
        start: {
          line: 15,
          column: 4
        },
        end: {
          line: 15,
          column: 24
        }
      },
      "4": {
        start: {
          line: 15,
          column: 17
        },
        end: {
          line: 15,
          column: 24
        }
      },
      "5": {
        start: {
          line: 18,
          column: 21
        },
        end: {
          line: 18,
          column: 39
        }
      },
      "6": {
        start: {
          line: 19,
          column: 24
        },
        end: {
          line: 19,
          column: 112
        }
      },
      "7": {
        start: {
          line: 20,
          column: 24
        },
        end: {
          line: 20,
          column: 48
        }
      },
      "8": {
        start: {
          line: 21,
          column: 25
        },
        end: {
          line: 21,
          column: 53
        }
      },
      "9": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 29,
          column: 5
        }
      },
      "10": {
        start: {
          line: 25,
          column: 6
        },
        end: {
          line: 25,
          column: 43
        }
      },
      "11": {
        start: {
          line: 26,
          column: 11
        },
        end: {
          line: 29,
          column: 5
        }
      },
      "12": {
        start: {
          line: 28,
          column: 6
        },
        end: {
          line: 28,
          column: 32
        }
      },
      "13": {
        start: {
          line: 32,
          column: 2
        },
        end: {
          line: 38,
          column: 3
        }
      },
      "14": {
        start: {
          line: 33,
          column: 4
        },
        end: {
          line: 37,
          column: 6
        }
      },
      "15": {
        start: {
          line: 40,
          column: 2
        },
        end: {
          line: 40,
          column: 25
        }
      },
      "16": {
        start: {
          line: 43,
          column: 15
        },
        end: {
          line: 50,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "AuthGuard",
        decl: {
          start: {
            line: 10,
            column: 16
          },
          end: {
            line: 10,
            column: 25
          }
        },
        loc: {
          start: {
            line: 10,
            column: 56
          },
          end: {
            line: 41,
            column: 1
          }
        },
        line: 10
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 14,
            column: 12
          },
          end: {
            line: 14,
            column: 13
          }
        },
        loc: {
          start: {
            line: 14,
            column: 18
          },
          end: {
            line: 30,
            column: 3
          }
        },
        line: 14
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 15,
            column: 24
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 15,
            column: 4
          },
          end: {
            line: 15,
            column: 24
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 15
      },
      "1": {
        loc: {
          start: {
            line: 19,
            column: 24
          },
          end: {
            line: 19,
            column: 112
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 19,
            column: 24
          },
          end: {
            line: 19,
            column: 49
          }
        }, {
          start: {
            line: 19,
            column: 53
          },
          end: {
            line: 19,
            column: 79
          }
        }, {
          start: {
            line: 19,
            column: 83
          },
          end: {
            line: 19,
            column: 112
          }
        }],
        line: 19
      },
      "2": {
        loc: {
          start: {
            line: 23,
            column: 4
          },
          end: {
            line: 29,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 23,
            column: 4
          },
          end: {
            line: 29,
            column: 5
          }
        }, {
          start: {
            line: 26,
            column: 11
          },
          end: {
            line: 29,
            column: 5
          }
        }],
        line: 23
      },
      "3": {
        loc: {
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 23,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 8
          },
          end: {
            line: 23,
            column: 13
          }
        }, {
          start: {
            line: 23,
            column: 17
          },
          end: {
            line: 23,
            column: 29
          }
        }, {
          start: {
            line: 23,
            column: 33
          },
          end: {
            line: 23,
            column: 46
          }
        }],
        line: 23
      },
      "4": {
        loc: {
          start: {
            line: 26,
            column: 11
          },
          end: {
            line: 29,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 26,
            column: 11
          },
          end: {
            line: 29,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 26
      },
      "5": {
        loc: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 19
          }
        }, {
          start: {
            line: 26,
            column: 23
          },
          end: {
            line: 26,
            column: 34
          }
        }],
        line: 26
      },
      "6": {
        loc: {
          start: {
            line: 32,
            column: 2
          },
          end: {
            line: 38,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 32,
            column: 2
          },
          end: {
            line: 38,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 32
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0, 0],
      "2": [0, 0],
      "3": [0, 0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "52cc9653a8d8d3f4fd9a41ab07000e301e87a3f6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_b7a5bk17a = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_b7a5bk17a();
import React, { useEffect } from 'react';
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import { router, useSegments } from 'expo-router';
import { useAuth } from "../contexts/AuthContext";
import { jsx as _jsx, Fragment as _Fragment } from "react/jsx-runtime";
export function AuthGuard(_ref) {
  var children = _ref.children;
  cov_b7a5bk17a().f[0]++;
  var _ref2 = (cov_b7a5bk17a().s[0]++, useAuth()),
    user = _ref2.user,
    loading = _ref2.loading;
  var segments = (cov_b7a5bk17a().s[1]++, useSegments());
  cov_b7a5bk17a().s[2]++;
  useEffect(function () {
    cov_b7a5bk17a().f[1]++;
    cov_b7a5bk17a().s[3]++;
    if (loading) {
      cov_b7a5bk17a().b[0][0]++;
      cov_b7a5bk17a().s[4]++;
      return;
    } else {
      cov_b7a5bk17a().b[0][1]++;
    }
    var pathname = (cov_b7a5bk17a().s[5]++, segments.join('/'));
    var inAuthGroup = (cov_b7a5bk17a().s[6]++, (cov_b7a5bk17a().b[1][0]++, pathname.includes('auth')) || (cov_b7a5bk17a().b[1][1]++, pathname.includes('login')) || (cov_b7a5bk17a().b[1][2]++, pathname.includes('register')));
    var inTabsGroup = (cov_b7a5bk17a().s[7]++, segments[0] === '(tabs)');
    var inOnboarding = (cov_b7a5bk17a().s[8]++, segments[0] === 'onboarding');
    cov_b7a5bk17a().s[9]++;
    if ((cov_b7a5bk17a().b[3][0]++, !user) && (cov_b7a5bk17a().b[3][1]++, !inAuthGroup) && (cov_b7a5bk17a().b[3][2]++, !inOnboarding)) {
      cov_b7a5bk17a().b[2][0]++;
      cov_b7a5bk17a().s[10]++;
      router.replace('/auth/login');
    } else {
      cov_b7a5bk17a().b[2][1]++;
      cov_b7a5bk17a().s[11]++;
      if ((cov_b7a5bk17a().b[5][0]++, user) && (cov_b7a5bk17a().b[5][1]++, inAuthGroup)) {
        cov_b7a5bk17a().b[4][0]++;
        cov_b7a5bk17a().s[12]++;
        router.replace('/(tabs)');
      } else {
        cov_b7a5bk17a().b[4][1]++;
      }
    }
  }, [user, loading, segments]);
  cov_b7a5bk17a().s[13]++;
  if (loading) {
    cov_b7a5bk17a().b[6][0]++;
    cov_b7a5bk17a().s[14]++;
    return _jsx(View, {
      style: styles.loadingContainer,
      children: _jsx(ActivityIndicator, {
        size: "large",
        color: "#3b82f6"
      })
    });
  } else {
    cov_b7a5bk17a().b[6][1]++;
  }
  cov_b7a5bk17a().s[15]++;
  return _jsx(_Fragment, {
    children: children
  });
}
var styles = (cov_b7a5bk17a().s[16]++, StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f9fafb'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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