{"version": 3, "names": ["AppState", "advancedMemoryManager", "BackgroundProcessingManager", "_classCallCheck", "backgroundTasks", "cov_1z72a6923t", "s", "Map", "taskQueue", "activeTasks", "executionHistory", "PROCESSING_STRATEGIES", "name", "description", "conditions", "batteryLevel", "networkType", "appState", "limits", "maxConcurrentTasks", "maxCPUUsage", "maxMemoryUsage", "maxNetworkUsage", "prioritization", "<PERSON><PERSON><PERSON><PERSON>", "batteryAware", "networkAware", "timeAware", "f", "currentStrategy", "taskScheduler", "TaskScheduler", "resourceMonitor", "ResourceMonitor", "initializeBackgroundProcessing", "_createClass", "key", "value", "_initializeBackgroundProcessing", "_asyncToGenerator", "initialize", "start", "startTaskProcessing", "setupAppStateMonitoring", "registerDefaultTasks", "console", "log", "error", "apply", "arguments", "registerTask", "task", "fullTask", "Object", "assign", "execution", "attempts", "maxAttempts", "averageExecutionTime", "estimatedDuration", "successRate", "set", "id", "schedule", "immediate", "b", "scheduleTask", "taskId", "_this", "delay", "length", "undefined", "get", "warn", "setTimeout", "addToQueue", "_executeTaskImmediately", "executeTask", "executeTaskImmediately", "_x", "getProcessingMetrics", "_this2", "totalExecutions", "successfulExecutions", "filter", "e", "success", "reduce", "sum", "endTime", "startTime", "taskTypeDistribution", "for<PERSON>ach", "type", "activeTaskCount", "size", "queuedTaskCount", "totalTasksExecuted", "resourceUsage", "getCurrentUsage", "_optimizeProcessingStrategy", "currentConditions", "getCurrentConditions", "optimalStrategy", "selectOptimalStrategy", "adjustActiveTasksForStrategy", "optimizeProcessingStrategy", "includes", "push", "sortTaskQueue", "_this3", "sort", "a", "taskA", "taskB", "priorityOrder", "critical", "high", "medium", "low", "priorityDiff", "priority", "_executeTask", "Date", "now", "cpu", "memory", "network", "battery", "canExecuteTask", "Error", "initialUsage", "result", "callback", "finalUsage", "lastSuccess", "message", "lastAttempt", "delete", "shift", "_x2", "constraints", "allowedStates", "maxBatteryDrain", "requiresCharging", "isCharging", "requiresNetwork", "currentUsage", "estimatedCPU", "estimatedM<PERSON>ory", "_getCurrentConditions", "strategyScores", "map", "strategy", "score", "_adjustActiveTasksForStrategy", "maxConcurrent", "tasksToSuspend", "Array", "from", "keys", "slice", "_this4", "setInterval", "processTaskQueue", "_processTaskQueue", "availableSlots", "tasksToProcess", "splice", "_this5", "addEventListener", "nextAppState", "_this6", "defaultTasks", "estimatedNetwork", "requiresIdle", "recurring", "interval", "preferredTime", "dependencies", "_callback", "optimizeMemory", "cleaned", "_callback2", "synced", "events", "_initialize", "_start", "_this7", "updateResourceUsage", "Math", "random", "currentState", "backgroundProcessingManager"], "sources": ["BackgroundProcessingManager.ts"], "sourcesContent": ["/**\n * Background Processing Manager\n * \n * Intelligent background task management system with priority scheduling,\n * battery-aware processing, and predictive task execution.\n */\n\nimport { Platform, AppState } from 'react-native';\nimport { performanceMonitor } from '@/utils/performance';\nimport { smartResourceManager } from '@/services/ai/SmartResourceManager';\nimport { advancedMemoryManager } from './AdvancedMemoryManager';\n\ninterface BackgroundTask {\n  id: string;\n  name: string;\n  type: 'sync' | 'analytics' | 'cache' | 'ml_training' | 'data_processing' | 'maintenance';\n  priority: 'low' | 'medium' | 'high' | 'critical';\n  estimatedDuration: number; // milliseconds\n  estimatedCPU: number; // 0-1\n  estimatedMemory: number; // bytes\n  estimatedNetwork: number; // bytes\n  constraints: {\n    requiresNetwork: boolean;\n    requiresCharging: boolean;\n    requiresIdle: boolean;\n    maxBatteryDrain: number; // percentage\n    allowedStates: ('active' | 'background' | 'inactive')[];\n  };\n  schedule: {\n    immediate: boolean;\n    recurring: boolean;\n    interval?: number; // milliseconds\n    preferredTime?: 'idle' | 'charging' | 'wifi' | 'anytime';\n  };\n  execution: {\n    attempts: number;\n    maxAttempts: number;\n    lastAttempt?: number;\n    lastSuccess?: number;\n    averageExecutionTime: number;\n    successRate: number;\n  };\n  dependencies: string[]; // task IDs that must complete first\n  callback: () => Promise<any>;\n}\n\ninterface TaskExecution {\n  taskId: string;\n  startTime: number;\n  endTime?: number;\n  success: boolean;\n  result?: any;\n  error?: string;\n  resourceUsage: {\n    cpu: number;\n    memory: number;\n    network: number;\n    battery: number;\n  };\n  conditions: {\n    appState: string;\n    batteryLevel: number;\n    networkType: string;\n    isCharging: boolean;\n  };\n}\n\ninterface ProcessingStrategy {\n  name: string;\n  description: string;\n  conditions: {\n    batteryLevel: number;\n    networkType: string[];\n    appState: string[];\n    timeOfDay?: { start: number; end: number };\n  };\n  limits: {\n    maxConcurrentTasks: number;\n    maxCPUUsage: number;\n    maxMemoryUsage: number;\n    maxNetworkUsage: number;\n  };\n  prioritization: {\n    criticalFirst: boolean;\n    batteryAware: boolean;\n    networkAware: boolean;\n    timeAware: boolean;\n  };\n}\n\n/**\n * Intelligent Background Processing Manager\n */\nclass BackgroundProcessingManager {\n  private backgroundTasks: Map<string, BackgroundTask> = new Map();\n  private taskQueue: string[] = [];\n  private activeTasks: Map<string, TaskExecution> = new Map();\n  private executionHistory: TaskExecution[] = [];\n  private currentStrategy: ProcessingStrategy;\n  private taskScheduler: TaskScheduler;\n  private resourceMonitor: ResourceMonitor;\n  \n  private readonly PROCESSING_STRATEGIES: ProcessingStrategy[] = [\n    {\n      name: 'battery_saver',\n      description: 'Minimal processing to preserve battery',\n      conditions: {\n        batteryLevel: 20,\n        networkType: ['wifi'],\n        appState: ['background'],\n      },\n      limits: {\n        maxConcurrentTasks: 1,\n        maxCPUUsage: 0.1,\n        maxMemoryUsage: 32 * 1024 * 1024, // 32MB\n        maxNetworkUsage: 1024 * 1024, // 1MB\n      },\n      prioritization: {\n        criticalFirst: true,\n        batteryAware: true,\n        networkAware: true,\n        timeAware: false,\n      },\n    },\n    {\n      name: 'balanced',\n      description: 'Balanced processing for normal conditions',\n      conditions: {\n        batteryLevel: 50,\n        networkType: ['wifi', '4g', '5g'],\n        appState: ['active', 'background'],\n      },\n      limits: {\n        maxConcurrentTasks: 3,\n        maxCPUUsage: 0.3,\n        maxMemoryUsage: 128 * 1024 * 1024, // 128MB\n        maxNetworkUsage: 10 * 1024 * 1024, // 10MB\n      },\n      prioritization: {\n        criticalFirst: true,\n        batteryAware: true,\n        networkAware: true,\n        timeAware: true,\n      },\n    },\n    {\n      name: 'performance',\n      description: 'High performance processing when charging',\n      conditions: {\n        batteryLevel: 80,\n        networkType: ['wifi', '5g'],\n        appState: ['active', 'background'],\n      },\n      limits: {\n        maxConcurrentTasks: 5,\n        maxCPUUsage: 0.6,\n        maxMemoryUsage: 256 * 1024 * 1024, // 256MB\n        maxNetworkUsage: 50 * 1024 * 1024, // 50MB\n      },\n      prioritization: {\n        criticalFirst: false,\n        batteryAware: false,\n        networkAware: false,\n        timeAware: true,\n      },\n    },\n  ];\n\n  constructor() {\n    this.currentStrategy = this.PROCESSING_STRATEGIES[1]; // Start with balanced\n    this.taskScheduler = new TaskScheduler();\n    this.resourceMonitor = new ResourceMonitor();\n    \n    this.initializeBackgroundProcessing();\n  }\n\n  /**\n   * Initialize background processing system\n   */\n  private async initializeBackgroundProcessing(): Promise<void> {\n    try {\n      // Initialize task scheduler\n      await this.taskScheduler.initialize();\n      \n      // Start resource monitoring\n      await this.resourceMonitor.start();\n      \n      // Start task processing\n      this.startTaskProcessing();\n      \n      // Monitor app state changes\n      this.setupAppStateMonitoring();\n      \n      // Register default background tasks\n      this.registerDefaultTasks();\n      \n      console.log('Background Processing Manager initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Background Processing Manager:', error);\n    }\n  }\n\n  /**\n   * Register background task\n   */\n  registerTask(task: Omit<BackgroundTask, 'execution'>): string {\n    const fullTask: BackgroundTask = {\n      ...task,\n      execution: {\n        attempts: 0,\n        maxAttempts: 3,\n        averageExecutionTime: task.estimatedDuration,\n        successRate: 100,\n      },\n    };\n\n    this.backgroundTasks.set(task.id, fullTask);\n    \n    // Schedule task if immediate\n    if (task.schedule.immediate) {\n      this.scheduleTask(task.id);\n    }\n    \n    console.log(`Registered background task: ${task.id}`);\n    return task.id;\n  }\n\n  /**\n   * Schedule task for execution\n   */\n  scheduleTask(taskId: string, delay: number = 0): void {\n    const task = this.backgroundTasks.get(taskId);\n    if (!task) {\n      console.warn(`Task not found: ${taskId}`);\n      return;\n    }\n\n    if (delay > 0) {\n      setTimeout(() => {\n        this.addToQueue(taskId);\n      }, delay);\n    } else {\n      this.addToQueue(taskId);\n    }\n  }\n\n  /**\n   * Execute task immediately (bypassing queue)\n   */\n  async executeTaskImmediately(taskId: string): Promise<TaskExecution | null> {\n    const task = this.backgroundTasks.get(taskId);\n    if (!task) {\n      console.warn(`Task not found: ${taskId}`);\n      return null;\n    }\n\n    return await this.executeTask(task);\n  }\n\n  /**\n   * Get background processing metrics\n   */\n  getProcessingMetrics(): {\n    activeTaskCount: number;\n    queuedTaskCount: number;\n    totalTasksExecuted: number;\n    averageExecutionTime: number;\n    successRate: number;\n    currentStrategy: string;\n    resourceUsage: {\n      cpu: number;\n      memory: number;\n      network: number;\n      battery: number;\n    };\n    taskTypeDistribution: Record<string, number>;\n  } {\n    const totalExecutions = this.executionHistory.length;\n    const successfulExecutions = this.executionHistory.filter(e => e.success).length;\n    \n    const averageExecutionTime = totalExecutions > 0\n      ? this.executionHistory.reduce((sum, e) => sum + (e.endTime! - e.startTime), 0) / totalExecutions\n      : 0;\n\n    const successRate = totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0;\n\n    // Calculate task type distribution\n    const taskTypeDistribution: Record<string, number> = {};\n    this.executionHistory.forEach(execution => {\n      const task = this.backgroundTasks.get(execution.taskId);\n      if (task) {\n        taskTypeDistribution[task.type] = (taskTypeDistribution[task.type] || 0) + 1;\n      }\n    });\n\n    return {\n      activeTaskCount: this.activeTasks.size,\n      queuedTaskCount: this.taskQueue.length,\n      totalTasksExecuted: totalExecutions,\n      averageExecutionTime,\n      successRate,\n      currentStrategy: this.currentStrategy.name,\n      resourceUsage: this.resourceMonitor.getCurrentUsage(),\n      taskTypeDistribution,\n    };\n  }\n\n  /**\n   * Optimize background processing strategy\n   */\n  async optimizeProcessingStrategy(): Promise<void> {\n    try {\n      const currentConditions = await this.getCurrentConditions();\n      const optimalStrategy = this.selectOptimalStrategy(currentConditions);\n      \n      if (optimalStrategy.name !== this.currentStrategy.name) {\n        console.log(`Switching to ${optimalStrategy.name} processing strategy`);\n        this.currentStrategy = optimalStrategy;\n        \n        // Adjust active tasks if needed\n        await this.adjustActiveTasksForStrategy();\n      }\n    } catch (error) {\n      console.error('Failed to optimize processing strategy:', error);\n    }\n  }\n\n  // Private helper methods\n\n  private addToQueue(taskId: string): void {\n    if (!this.taskQueue.includes(taskId)) {\n      this.taskQueue.push(taskId);\n      this.sortTaskQueue();\n    }\n  }\n\n  private sortTaskQueue(): void {\n    this.taskQueue.sort((a, b) => {\n      const taskA = this.backgroundTasks.get(a);\n      const taskB = this.backgroundTasks.get(b);\n      \n      if (!taskA || !taskB) return 0;\n      \n      // Sort by priority first\n      const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };\n      const priorityDiff = priorityOrder[taskB.priority] - priorityOrder[taskA.priority];\n      \n      if (priorityDiff !== 0) return priorityDiff;\n      \n      // Then by estimated duration (shorter first)\n      return taskA.estimatedDuration - taskB.estimatedDuration;\n    });\n  }\n\n  private async executeTask(task: BackgroundTask): Promise<TaskExecution> {\n    const execution: TaskExecution = {\n      taskId: task.id,\n      startTime: Date.now(),\n      success: false,\n      resourceUsage: { cpu: 0, memory: 0, network: 0, battery: 0 },\n      conditions: await this.getCurrentConditions(),\n    };\n\n    try {\n      // Check if task can be executed\n      if (!this.canExecuteTask(task)) {\n        throw new Error('Task execution conditions not met');\n      }\n\n      // Track resource usage before execution\n      const initialUsage = this.resourceMonitor.getCurrentUsage();\n      \n      // Execute task\n      this.activeTasks.set(task.id, execution);\n      const result = await task.callback();\n      \n      // Calculate resource usage\n      const finalUsage = this.resourceMonitor.getCurrentUsage();\n      execution.resourceUsage = {\n        cpu: finalUsage.cpu - initialUsage.cpu,\n        memory: finalUsage.memory - initialUsage.memory,\n        network: finalUsage.network - initialUsage.network,\n        battery: finalUsage.battery - initialUsage.battery,\n      };\n\n      execution.success = true;\n      execution.result = result;\n      execution.endTime = Date.now();\n\n      // Update task execution metrics\n      task.execution.attempts++;\n      task.execution.lastSuccess = Date.now();\n      task.execution.averageExecutionTime = \n        (task.execution.averageExecutionTime + (execution.endTime - execution.startTime)) / 2;\n\n      console.log(`Task executed successfully: ${task.id}`);\n\n    } catch (error) {\n      execution.success = false;\n      execution.error = error.message;\n      execution.endTime = Date.now();\n\n      task.execution.attempts++;\n      task.execution.lastAttempt = Date.now();\n\n      console.error(`Task execution failed: ${task.id}`, error);\n    } finally {\n      this.activeTasks.delete(task.id);\n      this.executionHistory.push(execution);\n      \n      // Limit history size\n      if (this.executionHistory.length > 1000) {\n        this.executionHistory.shift();\n      }\n    }\n\n    return execution;\n  }\n\n  private canExecuteTask(task: BackgroundTask): boolean {\n    // Check current conditions against task constraints\n    const conditions = this.resourceMonitor.getCurrentConditions();\n    \n    // Check app state\n    if (!task.constraints.allowedStates.includes(conditions.appState as any)) {\n      return false;\n    }\n    \n    // Check battery level\n    if (conditions.batteryLevel < task.constraints.maxBatteryDrain) {\n      return false;\n    }\n    \n    // Check charging requirement\n    if (task.constraints.requiresCharging && !conditions.isCharging) {\n      return false;\n    }\n    \n    // Check network requirement\n    if (task.constraints.requiresNetwork && conditions.networkType === 'none') {\n      return false;\n    }\n    \n    // Check resource limits\n    const currentUsage = this.resourceMonitor.getCurrentUsage();\n    if (currentUsage.cpu + task.estimatedCPU > this.currentStrategy.limits.maxCPUUsage) {\n      return false;\n    }\n    \n    if (currentUsage.memory + task.estimatedMemory > this.currentStrategy.limits.maxMemoryUsage) {\n      return false;\n    }\n    \n    return true;\n  }\n\n  private async getCurrentConditions(): Promise<any> {\n    return this.resourceMonitor.getCurrentConditions();\n  }\n\n  private selectOptimalStrategy(conditions: any): ProcessingStrategy {\n    // Score each strategy based on current conditions\n    const strategyScores = this.PROCESSING_STRATEGIES.map(strategy => {\n      let score = 0;\n      \n      // Battery level score\n      if (conditions.batteryLevel >= strategy.conditions.batteryLevel) {\n        score += 30;\n      }\n      \n      // Network type score\n      if (strategy.conditions.networkType.includes(conditions.networkType)) {\n        score += 25;\n      }\n      \n      // App state score\n      if (strategy.conditions.appState.includes(conditions.appState)) {\n        score += 20;\n      }\n      \n      // Charging bonus\n      if (conditions.isCharging && strategy.name === 'performance') {\n        score += 25;\n      }\n      \n      return { strategy, score };\n    });\n\n    // Return strategy with highest score\n    strategyScores.sort((a, b) => b.score - a.score);\n    return strategyScores[0].strategy;\n  }\n\n  private async adjustActiveTasksForStrategy(): Promise<void> {\n    // If we have too many active tasks for the new strategy, pause some\n    const maxConcurrent = this.currentStrategy.limits.maxConcurrentTasks;\n    \n    if (this.activeTasks.size > maxConcurrent) {\n      const tasksToSuspend = Array.from(this.activeTasks.keys()).slice(maxConcurrent);\n      \n      for (const taskId of tasksToSuspend) {\n        // In a real implementation, we would pause/suspend these tasks\n        console.log(`Suspending task due to strategy change: ${taskId}`);\n      }\n    }\n  }\n\n  private startTaskProcessing(): void {\n    // Process task queue every 5 seconds\n    setInterval(() => {\n      this.processTaskQueue();\n    }, 5000);\n    \n    // Optimize strategy every minute\n    setInterval(() => {\n      this.optimizeProcessingStrategy();\n    }, 60000);\n  }\n\n  private async processTaskQueue(): Promise<void> {\n    if (this.taskQueue.length === 0) return;\n    \n    const maxConcurrent = this.currentStrategy.limits.maxConcurrentTasks;\n    const availableSlots = maxConcurrent - this.activeTasks.size;\n    \n    if (availableSlots <= 0) return;\n    \n    // Process up to available slots\n    const tasksToProcess = this.taskQueue.splice(0, availableSlots);\n    \n    for (const taskId of tasksToProcess) {\n      const task = this.backgroundTasks.get(taskId);\n      if (task && this.canExecuteTask(task)) {\n        this.executeTask(task);\n      } else {\n        // Re-queue task for later\n        this.taskQueue.push(taskId);\n      }\n    }\n  }\n\n  private setupAppStateMonitoring(): void {\n    AppState.addEventListener('change', (nextAppState) => {\n      console.log(`App state changed to: ${nextAppState}`);\n      // Trigger strategy optimization on app state change\n      this.optimizeProcessingStrategy();\n    });\n  }\n\n  private registerDefaultTasks(): void {\n    // Register common background tasks\n    const defaultTasks = [\n      {\n        id: 'cache_cleanup',\n        name: 'Cache Cleanup',\n        type: 'maintenance' as const,\n        priority: 'low' as const,\n        estimatedDuration: 30000,\n        estimatedCPU: 0.1,\n        estimatedMemory: 16 * 1024 * 1024,\n        estimatedNetwork: 0,\n        constraints: {\n          requiresNetwork: false,\n          requiresCharging: false,\n          requiresIdle: true,\n          maxBatteryDrain: 10,\n          allowedStates: ['background' as const],\n        },\n        schedule: {\n          immediate: false,\n          recurring: true,\n          interval: 3600000, // 1 hour\n          preferredTime: 'idle' as const,\n        },\n        dependencies: [],\n        callback: async () => {\n          // Cleanup cache\n          await advancedMemoryManager.optimizeMemory(false);\n          return { cleaned: true };\n        },\n      },\n      {\n        id: 'analytics_sync',\n        name: 'Analytics Sync',\n        type: 'analytics' as const,\n        priority: 'medium' as const,\n        estimatedDuration: 15000,\n        estimatedCPU: 0.05,\n        estimatedMemory: 8 * 1024 * 1024,\n        estimatedNetwork: 1024 * 1024,\n        constraints: {\n          requiresNetwork: true,\n          requiresCharging: false,\n          requiresIdle: false,\n          maxBatteryDrain: 5,\n          allowedStates: ['active' as const, 'background' as const],\n        },\n        schedule: {\n          immediate: false,\n          recurring: true,\n          interval: 1800000, // 30 minutes\n          preferredTime: 'wifi' as const,\n        },\n        dependencies: [],\n        callback: async () => {\n          // Sync analytics data\n          return { synced: true, events: 42 };\n        },\n      },\n    ];\n\n    defaultTasks.forEach(task => this.registerTask(task));\n  }\n}\n\n/**\n * Task Scheduler\n */\nclass TaskScheduler {\n  async initialize(): Promise<void> {\n    console.log('Task Scheduler initialized');\n  }\n}\n\n/**\n * Resource Monitor\n */\nclass ResourceMonitor {\n  private currentUsage = { cpu: 0, memory: 0, network: 0, battery: 0 };\n  private currentConditions = {\n    appState: 'active',\n    batteryLevel: 100,\n    networkType: 'wifi',\n    isCharging: false,\n  };\n\n  async start(): Promise<void> {\n    // Start monitoring resources\n    setInterval(() => {\n      this.updateResourceUsage();\n    }, 10000); // Update every 10 seconds\n    \n    console.log('Resource Monitor started');\n  }\n\n  getCurrentUsage() {\n    return { ...this.currentUsage };\n  }\n\n  getCurrentConditions() {\n    return { ...this.currentConditions };\n  }\n\n  private updateResourceUsage(): void {\n    // Simulate resource usage monitoring\n    this.currentUsage = {\n      cpu: Math.random() * 0.5, // 0-50% CPU\n      memory: Math.random() * 128 * 1024 * 1024, // 0-128MB\n      network: Math.random() * 1024 * 1024, // 0-1MB network\n      battery: Math.random() * 5, // 0-5% battery drain\n    };\n    \n    // Update conditions\n    this.currentConditions = {\n      appState: AppState.currentState,\n      batteryLevel: 75 + Math.random() * 25, // 75-100%\n      networkType: 'wifi',\n      isCharging: Math.random() > 0.7, // 30% chance of charging\n    };\n  }\n}\n\n// Export singleton instance\nexport const backgroundProcessingManager = new BackgroundProcessingManager();\nexport default backgroundProcessingManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAAmBA,QAAQ,QAAQ,cAAc;AAGjD,SAASC,qBAAqB;AAAkC,IAmF1DC,2BAA2B;EA2E/B,SAAAA,4BAAA,EAAc;IAAAC,eAAA,OAAAD,2BAAA;IAAA,KA1ENE,eAAe,IAAAC,cAAA,GAAAC,CAAA,OAAgC,IAAIC,GAAG,CAAC,CAAC;IAAA,KACxDC,SAAS,IAAAH,cAAA,GAAAC,CAAA,OAAa,EAAE;IAAA,KACxBG,WAAW,IAAAJ,cAAA,GAAAC,CAAA,OAA+B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACnDG,gBAAgB,IAAAL,cAAA,GAAAC,CAAA,OAAoB,EAAE;IAAA,KAK7BK,qBAAqB,IAAAN,cAAA,GAAAC,CAAA,OAAyB,CAC7D;MACEM,IAAI,EAAE,eAAe;MACrBC,WAAW,EAAE,wCAAwC;MACrDC,UAAU,EAAE;QACVC,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE,CAAC,MAAM,CAAC;QACrBC,QAAQ,EAAE,CAAC,YAAY;MACzB,CAAC;MACDC,MAAM,EAAE;QACNC,kBAAkB,EAAE,CAAC;QACrBC,WAAW,EAAE,GAAG;QAChBC,cAAc,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;QAChCC,eAAe,EAAE,IAAI,GAAG;MAC1B,CAAC;MACDC,cAAc,EAAE;QACdC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE;MACb;IACF,CAAC,EACD;MACEf,IAAI,EAAE,UAAU;MAChBC,WAAW,EAAE,2CAA2C;MACxDC,UAAU,EAAE;QACVC,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;QACjCC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY;MACnC,CAAC;MACDC,MAAM,EAAE;QACNC,kBAAkB,EAAE,CAAC;QACrBC,WAAW,EAAE,GAAG;QAChBC,cAAc,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;QACjCC,eAAe,EAAE,EAAE,GAAG,IAAI,GAAG;MAC/B,CAAC;MACDC,cAAc,EAAE;QACdC,aAAa,EAAE,IAAI;QACnBC,YAAY,EAAE,IAAI;QAClBC,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE;MACb;IACF,CAAC,EACD;MACEf,IAAI,EAAE,aAAa;MACnBC,WAAW,EAAE,2CAA2C;MACxDC,UAAU,EAAE;QACVC,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC;QAC3BC,QAAQ,EAAE,CAAC,QAAQ,EAAE,YAAY;MACnC,CAAC;MACDC,MAAM,EAAE;QACNC,kBAAkB,EAAE,CAAC;QACrBC,WAAW,EAAE,GAAG;QAChBC,cAAc,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;QACjCC,eAAe,EAAE,EAAE,GAAG,IAAI,GAAG;MAC/B,CAAC;MACDC,cAAc,EAAE;QACdC,aAAa,EAAE,KAAK;QACpBC,YAAY,EAAE,KAAK;QACnBC,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb;IACF,CAAC,CACF;IAAAtB,cAAA,GAAAuB,CAAA;IAAAvB,cAAA,GAAAC,CAAA;IAGC,IAAI,CAACuB,eAAe,GAAG,IAAI,CAAClB,qBAAqB,CAAC,CAAC,CAAC;IAACN,cAAA,GAAAC,CAAA;IACrD,IAAI,CAACwB,aAAa,GAAG,IAAIC,aAAa,CAAC,CAAC;IAAC1B,cAAA,GAAAC,CAAA;IACzC,IAAI,CAAC0B,eAAe,GAAG,IAAIC,eAAe,CAAC,CAAC;IAAC5B,cAAA,GAAAC,CAAA;IAE7C,IAAI,CAAC4B,8BAA8B,CAAC,CAAC;EACvC;EAAC,OAAAC,YAAA,CAAAjC,2BAAA;IAAAkC,GAAA;IAAAC,KAAA;MAAA,IAAAC,+BAAA,GAAAC,iBAAA,CAKD,aAA8D;QAAAlC,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAC5D,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF,MAAM,IAAI,CAACwB,aAAa,CAACU,UAAU,CAAC,CAAC;UAACnC,cAAA,GAAAC,CAAA;UAGtC,MAAM,IAAI,CAAC0B,eAAe,CAACS,KAAK,CAAC,CAAC;UAACpC,cAAA,GAAAC,CAAA;UAGnC,IAAI,CAACoC,mBAAmB,CAAC,CAAC;UAACrC,cAAA,GAAAC,CAAA;UAG3B,IAAI,CAACqC,uBAAuB,CAAC,CAAC;UAACtC,cAAA,GAAAC,CAAA;UAG/B,IAAI,CAACsC,oBAAoB,CAAC,CAAC;UAACvC,cAAA,GAAAC,CAAA;UAE5BuC,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACvE,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAA1C,cAAA,GAAAC,CAAA;UACduC,OAAO,CAACE,KAAK,CAAC,qDAAqD,EAAEA,KAAK,CAAC;QAC7E;MACF,CAAC;MAAA,SArBab,8BAA8BA,CAAA;QAAA,OAAAI,+BAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA9Bf,8BAA8B;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA,EA0B5C,SAAAa,YAAYA,CAACC,IAAuC,EAAU;MAAA9C,cAAA,GAAAuB,CAAA;MAC5D,IAAMwB,QAAwB,IAAA/C,cAAA,GAAAC,CAAA,QAAA+C,MAAA,CAAAC,MAAA,KACzBH,IAAI;QACPI,SAAS,EAAE;UACTC,QAAQ,EAAE,CAAC;UACXC,WAAW,EAAE,CAAC;UACdC,oBAAoB,EAAEP,IAAI,CAACQ,iBAAiB;UAC5CC,WAAW,EAAE;QACf;MAAC,GACF;MAACvD,cAAA,GAAAC,CAAA;MAEF,IAAI,CAACF,eAAe,CAACyD,GAAG,CAACV,IAAI,CAACW,EAAE,EAAEV,QAAQ,CAAC;MAAC/C,cAAA,GAAAC,CAAA;MAG5C,IAAI6C,IAAI,CAACY,QAAQ,CAACC,SAAS,EAAE;QAAA3D,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QAC3B,IAAI,CAAC4D,YAAY,CAACf,IAAI,CAACW,EAAE,CAAC;MAC5B,CAAC;QAAAzD,cAAA,GAAA4D,CAAA;MAAA;MAAA5D,cAAA,GAAAC,CAAA;MAEDuC,OAAO,CAACC,GAAG,CAAC,+BAA+BK,IAAI,CAACW,EAAE,EAAE,CAAC;MAACzD,cAAA,GAAAC,CAAA;MACtD,OAAO6C,IAAI,CAACW,EAAE;IAChB;EAAC;IAAA1B,GAAA;IAAAC,KAAA,EAKD,SAAA6B,YAAYA,CAACC,MAAc,EAA2B;MAAA,IAAAC,KAAA;MAAA,IAAzBC,KAAa,GAAApB,SAAA,CAAAqB,MAAA,QAAArB,SAAA,QAAAsB,SAAA,GAAAtB,SAAA,OAAA5C,cAAA,GAAA4D,CAAA,UAAG,CAAC;MAAA5D,cAAA,GAAAuB,CAAA;MAC5C,IAAMuB,IAAI,IAAA9C,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,eAAe,CAACoE,GAAG,CAACL,MAAM,CAAC;MAAC9D,cAAA,GAAAC,CAAA;MAC9C,IAAI,CAAC6C,IAAI,EAAE;QAAA9C,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QACTuC,OAAO,CAAC4B,IAAI,CAAC,mBAAmBN,MAAM,EAAE,CAAC;QAAC9D,cAAA,GAAAC,CAAA;QAC1C;MACF,CAAC;QAAAD,cAAA,GAAA4D,CAAA;MAAA;MAAA5D,cAAA,GAAAC,CAAA;MAED,IAAI+D,KAAK,GAAG,CAAC,EAAE;QAAAhE,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QACboE,UAAU,CAAC,YAAM;UAAArE,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAC,CAAA;UACf8D,KAAI,CAACO,UAAU,CAACR,MAAM,CAAC;QACzB,CAAC,EAAEE,KAAK,CAAC;MACX,CAAC,MAAM;QAAAhE,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QACL,IAAI,CAACqE,UAAU,CAACR,MAAM,CAAC;MACzB;IACF;EAAC;IAAA/B,GAAA;IAAAC,KAAA;MAAA,IAAAuC,uBAAA,GAAArC,iBAAA,CAKD,WAA6B4B,MAAc,EAAiC;QAAA9D,cAAA,GAAAuB,CAAA;QAC1E,IAAMuB,IAAI,IAAA9C,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,eAAe,CAACoE,GAAG,CAACL,MAAM,CAAC;QAAC9D,cAAA,GAAAC,CAAA;QAC9C,IAAI,CAAC6C,IAAI,EAAE;UAAA9C,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UACTuC,OAAO,CAAC4B,IAAI,CAAC,mBAAmBN,MAAM,EAAE,CAAC;UAAC9D,cAAA,GAAAC,CAAA;UAC1C,OAAO,IAAI;QACb,CAAC;UAAAD,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAAC,CAAA;QAED,aAAa,IAAI,CAACuE,WAAW,CAAC1B,IAAI,CAAC;MACrC,CAAC;MAAA,SARK2B,sBAAsBA,CAAAC,EAAA;QAAA,OAAAH,uBAAA,CAAA5B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtB6B,sBAAsB;IAAA;EAAA;IAAA1C,GAAA;IAAAC,KAAA,EAa5B,SAAA2C,oBAAoBA,CAAA,EAclB;MAAA,IAAAC,MAAA;MAAA5E,cAAA,GAAAuB,CAAA;MACA,IAAMsD,eAAe,IAAA7E,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACI,gBAAgB,CAAC4D,MAAM;MACpD,IAAMa,oBAAoB,IAAA9E,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACI,gBAAgB,CAAC0E,MAAM,CAAC,UAAAC,CAAC,EAAI;QAAAhF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAAA,OAAA+E,CAAC,CAACC,OAAO;MAAD,CAAC,CAAC,CAAChB,MAAM;MAEhF,IAAMZ,oBAAoB,IAAArD,cAAA,GAAAC,CAAA,QAAG4E,eAAe,GAAG,CAAC,IAAA7E,cAAA,GAAA4D,CAAA,UAC5C,IAAI,CAACvD,gBAAgB,CAAC6E,MAAM,CAAC,UAACC,GAAG,EAAEH,CAAC,EAAK;QAAAhF,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAAA,OAAAkF,GAAG,IAAIH,CAAC,CAACI,OAAO,GAAIJ,CAAC,CAACK,SAAS,CAAC;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGR,eAAe,KAAA7E,cAAA,GAAA4D,CAAA,UAC/F,CAAC;MAEL,IAAML,WAAW,IAAAvD,cAAA,GAAAC,CAAA,QAAG4E,eAAe,GAAG,CAAC,IAAA7E,cAAA,GAAA4D,CAAA,UAAIkB,oBAAoB,GAAGD,eAAe,GAAI,GAAG,KAAA7E,cAAA,GAAA4D,CAAA,UAAG,CAAC;MAG5F,IAAM0B,oBAA4C,IAAAtF,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAACD,cAAA,GAAAC,CAAA;MACxD,IAAI,CAACI,gBAAgB,CAACkF,OAAO,CAAC,UAAArC,SAAS,EAAI;QAAAlD,cAAA,GAAAuB,CAAA;QACzC,IAAMuB,IAAI,IAAA9C,cAAA,GAAAC,CAAA,QAAG2E,MAAI,CAAC7E,eAAe,CAACoE,GAAG,CAACjB,SAAS,CAACY,MAAM,CAAC;QAAC9D,cAAA,GAAAC,CAAA;QACxD,IAAI6C,IAAI,EAAE;UAAA9C,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UACRqF,oBAAoB,CAACxC,IAAI,CAAC0C,IAAI,CAAC,GAAG,CAAC,CAAAxF,cAAA,GAAA4D,CAAA,UAAA0B,oBAAoB,CAACxC,IAAI,CAAC0C,IAAI,CAAC,MAAAxF,cAAA,GAAA4D,CAAA,UAAI,CAAC,KAAI,CAAC;QAC9E,CAAC;UAAA5D,cAAA,GAAA4D,CAAA;QAAA;MACH,CAAC,CAAC;MAAC5D,cAAA,GAAAC,CAAA;MAEH,OAAO;QACLwF,eAAe,EAAE,IAAI,CAACrF,WAAW,CAACsF,IAAI;QACtCC,eAAe,EAAE,IAAI,CAACxF,SAAS,CAAC8D,MAAM;QACtC2B,kBAAkB,EAAEf,eAAe;QACnCxB,oBAAoB,EAApBA,oBAAoB;QACpBE,WAAW,EAAXA,WAAW;QACX/B,eAAe,EAAE,IAAI,CAACA,eAAe,CAACjB,IAAI;QAC1CsF,aAAa,EAAE,IAAI,CAAClE,eAAe,CAACmE,eAAe,CAAC,CAAC;QACrDR,oBAAoB,EAApBA;MACF,CAAC;IACH;EAAC;IAAAvD,GAAA;IAAAC,KAAA;MAAA,IAAA+D,2BAAA,GAAA7D,iBAAA,CAKD,aAAkD;QAAAlC,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAChD,IAAI;UACF,IAAM+F,iBAAiB,IAAAhG,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACgG,oBAAoB,CAAC,CAAC;UAC3D,IAAMC,eAAe,IAAAlG,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkG,qBAAqB,CAACH,iBAAiB,CAAC;UAAChG,cAAA,GAAAC,CAAA;UAEtE,IAAIiG,eAAe,CAAC3F,IAAI,KAAK,IAAI,CAACiB,eAAe,CAACjB,IAAI,EAAE;YAAAP,cAAA,GAAA4D,CAAA;YAAA5D,cAAA,GAAAC,CAAA;YACtDuC,OAAO,CAACC,GAAG,CAAC,gBAAgByD,eAAe,CAAC3F,IAAI,sBAAsB,CAAC;YAACP,cAAA,GAAAC,CAAA;YACxE,IAAI,CAACuB,eAAe,GAAG0E,eAAe;YAAClG,cAAA,GAAAC,CAAA;YAGvC,MAAM,IAAI,CAACmG,4BAA4B,CAAC,CAAC;UAC3C,CAAC;YAAApG,cAAA,GAAA4D,CAAA;UAAA;QACH,CAAC,CAAC,OAAOlB,KAAK,EAAE;UAAA1C,cAAA,GAAAC,CAAA;UACduC,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;QACjE;MACF,CAAC;MAAA,SAfK2D,0BAA0BA,CAAA;QAAA,OAAAN,2BAAA,CAAApD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA1ByD,0BAA0B;IAAA;EAAA;IAAAtE,GAAA;IAAAC,KAAA,EAmBhC,SAAQsC,UAAUA,CAACR,MAAc,EAAQ;MAAA9D,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACvC,IAAI,CAAC,IAAI,CAACE,SAAS,CAACmG,QAAQ,CAACxC,MAAM,CAAC,EAAE;QAAA9D,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QACpC,IAAI,CAACE,SAAS,CAACoG,IAAI,CAACzC,MAAM,CAAC;QAAC9D,cAAA,GAAAC,CAAA;QAC5B,IAAI,CAACuG,aAAa,CAAC,CAAC;MACtB,CAAC;QAAAxG,cAAA,GAAA4D,CAAA;MAAA;IACH;EAAC;IAAA7B,GAAA;IAAAC,KAAA,EAED,SAAQwE,aAAaA,CAAA,EAAS;MAAA,IAAAC,MAAA;MAAAzG,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MAC5B,IAAI,CAACE,SAAS,CAACuG,IAAI,CAAC,UAACC,CAAC,EAAE/C,CAAC,EAAK;QAAA5D,cAAA,GAAAuB,CAAA;QAC5B,IAAMqF,KAAK,IAAA5G,cAAA,GAAAC,CAAA,QAAGwG,MAAI,CAAC1G,eAAe,CAACoE,GAAG,CAACwC,CAAC,CAAC;QACzC,IAAME,KAAK,IAAA7G,cAAA,GAAAC,CAAA,QAAGwG,MAAI,CAAC1G,eAAe,CAACoE,GAAG,CAACP,CAAC,CAAC;QAAC5D,cAAA,GAAAC,CAAA;QAE1C,IAAI,CAAAD,cAAA,GAAA4D,CAAA,YAACgD,KAAK,MAAA5G,cAAA,GAAA4D,CAAA,WAAI,CAACiD,KAAK,GAAE;UAAA7G,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UAAA,OAAO,CAAC;QAAA,CAAC;UAAAD,cAAA,GAAA4D,CAAA;QAAA;QAG/B,IAAMkD,aAAa,IAAA9G,cAAA,GAAAC,CAAA,QAAG;UAAE8G,QAAQ,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,MAAM,EAAE,CAAC;UAAEC,GAAG,EAAE;QAAE,CAAC;QACjE,IAAMC,YAAY,IAAAnH,cAAA,GAAAC,CAAA,QAAG6G,aAAa,CAACD,KAAK,CAACO,QAAQ,CAAC,GAAGN,aAAa,CAACF,KAAK,CAACQ,QAAQ,CAAC;QAACpH,cAAA,GAAAC,CAAA;QAEnF,IAAIkH,YAAY,KAAK,CAAC,EAAE;UAAAnH,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UAAA,OAAOkH,YAAY;QAAA,CAAC;UAAAnH,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAAC,CAAA;QAG5C,OAAO2G,KAAK,CAACtD,iBAAiB,GAAGuD,KAAK,CAACvD,iBAAiB;MAC1D,CAAC,CAAC;IACJ;EAAC;IAAAvB,GAAA;IAAAC,KAAA;MAAA,IAAAqF,YAAA,GAAAnF,iBAAA,CAED,WAA0BY,IAAoB,EAA0B;QAAA9C,cAAA,GAAAuB,CAAA;QACtE,IAAM2B,SAAwB,IAAAlD,cAAA,GAAAC,CAAA,QAAG;UAC/B6D,MAAM,EAAEhB,IAAI,CAACW,EAAE;UACf4B,SAAS,EAAEiC,IAAI,CAACC,GAAG,CAAC,CAAC;UACrBtC,OAAO,EAAE,KAAK;UACdY,aAAa,EAAE;YAAE2B,GAAG,EAAE,CAAC;YAAEC,MAAM,EAAE,CAAC;YAAEC,OAAO,EAAE,CAAC;YAAEC,OAAO,EAAE;UAAE,CAAC;UAC5DlH,UAAU,QAAQ,IAAI,CAACwF,oBAAoB,CAAC;QAC9C,CAAC;QAACjG,cAAA,GAAAC,CAAA;QAEF,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF,IAAI,CAAC,IAAI,CAAC2H,cAAc,CAAC9E,IAAI,CAAC,EAAE;YAAA9C,cAAA,GAAA4D,CAAA;YAAA5D,cAAA,GAAAC,CAAA;YAC9B,MAAM,IAAI4H,KAAK,CAAC,mCAAmC,CAAC;UACtD,CAAC;YAAA7H,cAAA,GAAA4D,CAAA;UAAA;UAGD,IAAMkE,YAAY,IAAA9H,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC0B,eAAe,CAACmE,eAAe,CAAC,CAAC;UAAC9F,cAAA,GAAAC,CAAA;UAG5D,IAAI,CAACG,WAAW,CAACoD,GAAG,CAACV,IAAI,CAACW,EAAE,EAAEP,SAAS,CAAC;UACxC,IAAM6E,MAAM,IAAA/H,cAAA,GAAAC,CAAA,cAAS6C,IAAI,CAACkF,QAAQ,CAAC,CAAC;UAGpC,IAAMC,UAAU,IAAAjI,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC0B,eAAe,CAACmE,eAAe,CAAC,CAAC;UAAC9F,cAAA,GAAAC,CAAA;UAC1DiD,SAAS,CAAC2C,aAAa,GAAG;YACxB2B,GAAG,EAAES,UAAU,CAACT,GAAG,GAAGM,YAAY,CAACN,GAAG;YACtCC,MAAM,EAAEQ,UAAU,CAACR,MAAM,GAAGK,YAAY,CAACL,MAAM;YAC/CC,OAAO,EAAEO,UAAU,CAACP,OAAO,GAAGI,YAAY,CAACJ,OAAO;YAClDC,OAAO,EAAEM,UAAU,CAACN,OAAO,GAAGG,YAAY,CAACH;UAC7C,CAAC;UAAC3H,cAAA,GAAAC,CAAA;UAEFiD,SAAS,CAAC+B,OAAO,GAAG,IAAI;UAACjF,cAAA,GAAAC,CAAA;UACzBiD,SAAS,CAAC6E,MAAM,GAAGA,MAAM;UAAC/H,cAAA,GAAAC,CAAA;UAC1BiD,SAAS,CAACkC,OAAO,GAAGkC,IAAI,CAACC,GAAG,CAAC,CAAC;UAACvH,cAAA,GAAAC,CAAA;UAG/B6C,IAAI,CAACI,SAAS,CAACC,QAAQ,EAAE;UAACnD,cAAA,GAAAC,CAAA;UAC1B6C,IAAI,CAACI,SAAS,CAACgF,WAAW,GAAGZ,IAAI,CAACC,GAAG,CAAC,CAAC;UAACvH,cAAA,GAAAC,CAAA;UACxC6C,IAAI,CAACI,SAAS,CAACG,oBAAoB,GACjC,CAACP,IAAI,CAACI,SAAS,CAACG,oBAAoB,IAAIH,SAAS,CAACkC,OAAO,GAAGlC,SAAS,CAACmC,SAAS,CAAC,IAAI,CAAC;UAACrF,cAAA,GAAAC,CAAA;UAExFuC,OAAO,CAACC,GAAG,CAAC,+BAA+BK,IAAI,CAACW,EAAE,EAAE,CAAC;QAEvD,CAAC,CAAC,OAAOf,KAAK,EAAE;UAAA1C,cAAA,GAAAC,CAAA;UACdiD,SAAS,CAAC+B,OAAO,GAAG,KAAK;UAACjF,cAAA,GAAAC,CAAA;UAC1BiD,SAAS,CAACR,KAAK,GAAGA,KAAK,CAACyF,OAAO;UAACnI,cAAA,GAAAC,CAAA;UAChCiD,SAAS,CAACkC,OAAO,GAAGkC,IAAI,CAACC,GAAG,CAAC,CAAC;UAACvH,cAAA,GAAAC,CAAA;UAE/B6C,IAAI,CAACI,SAAS,CAACC,QAAQ,EAAE;UAACnD,cAAA,GAAAC,CAAA;UAC1B6C,IAAI,CAACI,SAAS,CAACkF,WAAW,GAAGd,IAAI,CAACC,GAAG,CAAC,CAAC;UAACvH,cAAA,GAAAC,CAAA;UAExCuC,OAAO,CAACE,KAAK,CAAC,0BAA0BI,IAAI,CAACW,EAAE,EAAE,EAAEf,KAAK,CAAC;QAC3D,CAAC,SAAS;UAAA1C,cAAA,GAAAC,CAAA;UACR,IAAI,CAACG,WAAW,CAACiI,MAAM,CAACvF,IAAI,CAACW,EAAE,CAAC;UAACzD,cAAA,GAAAC,CAAA;UACjC,IAAI,CAACI,gBAAgB,CAACkG,IAAI,CAACrD,SAAS,CAAC;UAAClD,cAAA,GAAAC,CAAA;UAGtC,IAAI,IAAI,CAACI,gBAAgB,CAAC4D,MAAM,GAAG,IAAI,EAAE;YAAAjE,cAAA,GAAA4D,CAAA;YAAA5D,cAAA,GAAAC,CAAA;YACvC,IAAI,CAACI,gBAAgB,CAACiI,KAAK,CAAC,CAAC;UAC/B,CAAC;YAAAtI,cAAA,GAAA4D,CAAA;UAAA;QACH;QAAC5D,cAAA,GAAAC,CAAA;QAED,OAAOiD,SAAS;MAClB,CAAC;MAAA,SA/DasB,WAAWA,CAAA+D,GAAA;QAAA,OAAAlB,YAAA,CAAA1E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAX4B,WAAW;IAAA;EAAA;IAAAzC,GAAA;IAAAC,KAAA,EAiEzB,SAAQ4F,cAAcA,CAAC9E,IAAoB,EAAW;MAAA9C,cAAA,GAAAuB,CAAA;MAEpD,IAAMd,UAAU,IAAAT,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC0B,eAAe,CAACsE,oBAAoB,CAAC,CAAC;MAACjG,cAAA,GAAAC,CAAA;MAG/D,IAAI,CAAC6C,IAAI,CAAC0F,WAAW,CAACC,aAAa,CAACnC,QAAQ,CAAC7F,UAAU,CAACG,QAAe,CAAC,EAAE;QAAAZ,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QACxE,OAAO,KAAK;MACd,CAAC;QAAAD,cAAA,GAAA4D,CAAA;MAAA;MAAA5D,cAAA,GAAAC,CAAA;MAGD,IAAIQ,UAAU,CAACC,YAAY,GAAGoC,IAAI,CAAC0F,WAAW,CAACE,eAAe,EAAE;QAAA1I,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QAC9D,OAAO,KAAK;MACd,CAAC;QAAAD,cAAA,GAAA4D,CAAA;MAAA;MAAA5D,cAAA,GAAAC,CAAA;MAGD,IAAI,CAAAD,cAAA,GAAA4D,CAAA,WAAAd,IAAI,CAAC0F,WAAW,CAACG,gBAAgB,MAAA3I,cAAA,GAAA4D,CAAA,WAAI,CAACnD,UAAU,CAACmI,UAAU,GAAE;QAAA5I,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QAC/D,OAAO,KAAK;MACd,CAAC;QAAAD,cAAA,GAAA4D,CAAA;MAAA;MAAA5D,cAAA,GAAAC,CAAA;MAGD,IAAI,CAAAD,cAAA,GAAA4D,CAAA,WAAAd,IAAI,CAAC0F,WAAW,CAACK,eAAe,MAAA7I,cAAA,GAAA4D,CAAA,WAAInD,UAAU,CAACE,WAAW,KAAK,MAAM,GAAE;QAAAX,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QACzE,OAAO,KAAK;MACd,CAAC;QAAAD,cAAA,GAAA4D,CAAA;MAAA;MAGD,IAAMkF,YAAY,IAAA9I,cAAA,GAAAC,CAAA,SAAG,IAAI,CAAC0B,eAAe,CAACmE,eAAe,CAAC,CAAC;MAAC9F,cAAA,GAAAC,CAAA;MAC5D,IAAI6I,YAAY,CAACtB,GAAG,GAAG1E,IAAI,CAACiG,YAAY,GAAG,IAAI,CAACvH,eAAe,CAACX,MAAM,CAACE,WAAW,EAAE;QAAAf,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QAClF,OAAO,KAAK;MACd,CAAC;QAAAD,cAAA,GAAA4D,CAAA;MAAA;MAAA5D,cAAA,GAAAC,CAAA;MAED,IAAI6I,YAAY,CAACrB,MAAM,GAAG3E,IAAI,CAACkG,eAAe,GAAG,IAAI,CAACxH,eAAe,CAACX,MAAM,CAACG,cAAc,EAAE;QAAAhB,cAAA,GAAA4D,CAAA;QAAA5D,cAAA,GAAAC,CAAA;QAC3F,OAAO,KAAK;MACd,CAAC;QAAAD,cAAA,GAAA4D,CAAA;MAAA;MAAA5D,cAAA,GAAAC,CAAA;MAED,OAAO,IAAI;IACb;EAAC;IAAA8B,GAAA;IAAAC,KAAA;MAAA,IAAAiH,qBAAA,GAAA/G,iBAAA,CAED,aAAmD;QAAAlC,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QACjD,OAAO,IAAI,CAAC0B,eAAe,CAACsE,oBAAoB,CAAC,CAAC;MACpD,CAAC;MAAA,SAFaA,oBAAoBA,CAAA;QAAA,OAAAgD,qBAAA,CAAAtG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBqD,oBAAoB;IAAA;EAAA;IAAAlE,GAAA;IAAAC,KAAA,EAIlC,SAAQmE,qBAAqBA,CAAC1F,UAAe,EAAsB;MAAAT,cAAA,GAAAuB,CAAA;MAEjE,IAAM2H,cAAc,IAAAlJ,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACK,qBAAqB,CAAC6I,GAAG,CAAC,UAAAC,QAAQ,EAAI;QAAApJ,cAAA,GAAAuB,CAAA;QAChE,IAAI8H,KAAK,IAAArJ,cAAA,GAAAC,CAAA,SAAG,CAAC;QAACD,cAAA,GAAAC,CAAA;QAGd,IAAIQ,UAAU,CAACC,YAAY,IAAI0I,QAAQ,CAAC3I,UAAU,CAACC,YAAY,EAAE;UAAAV,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UAC/DoJ,KAAK,IAAI,EAAE;QACb,CAAC;UAAArJ,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAAC,CAAA;QAGD,IAAImJ,QAAQ,CAAC3I,UAAU,CAACE,WAAW,CAAC2F,QAAQ,CAAC7F,UAAU,CAACE,WAAW,CAAC,EAAE;UAAAX,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UACpEoJ,KAAK,IAAI,EAAE;QACb,CAAC;UAAArJ,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAAC,CAAA;QAGD,IAAImJ,QAAQ,CAAC3I,UAAU,CAACG,QAAQ,CAAC0F,QAAQ,CAAC7F,UAAU,CAACG,QAAQ,CAAC,EAAE;UAAAZ,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UAC9DoJ,KAAK,IAAI,EAAE;QACb,CAAC;UAAArJ,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAAC,CAAA;QAGD,IAAI,CAAAD,cAAA,GAAA4D,CAAA,WAAAnD,UAAU,CAACmI,UAAU,MAAA5I,cAAA,GAAA4D,CAAA,WAAIwF,QAAQ,CAAC7I,IAAI,KAAK,aAAa,GAAE;UAAAP,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UAC5DoJ,KAAK,IAAI,EAAE;QACb,CAAC;UAAArJ,cAAA,GAAA4D,CAAA;QAAA;QAAA5D,cAAA,GAAAC,CAAA;QAED,OAAO;UAAEmJ,QAAQ,EAARA,QAAQ;UAAEC,KAAK,EAALA;QAAM,CAAC;MAC5B,CAAC,CAAC;MAACrJ,cAAA,GAAAC,CAAA;MAGHiJ,cAAc,CAACxC,IAAI,CAAC,UAACC,CAAC,EAAE/C,CAAC,EAAK;QAAA5D,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAAA,OAAA2D,CAAC,CAACyF,KAAK,GAAG1C,CAAC,CAAC0C,KAAK;MAAD,CAAC,CAAC;MAACrJ,cAAA,GAAAC,CAAA;MACjD,OAAOiJ,cAAc,CAAC,CAAC,CAAC,CAACE,QAAQ;IACnC;EAAC;IAAArH,GAAA;IAAAC,KAAA;MAAA,IAAAsH,6BAAA,GAAApH,iBAAA,CAED,aAA4D;QAAAlC,cAAA,GAAAuB,CAAA;QAE1D,IAAMgI,aAAa,IAAAvJ,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACuB,eAAe,CAACX,MAAM,CAACC,kBAAkB;QAACd,cAAA,GAAAC,CAAA;QAErE,IAAI,IAAI,CAACG,WAAW,CAACsF,IAAI,GAAG6D,aAAa,EAAE;UAAAvJ,cAAA,GAAA4D,CAAA;UACzC,IAAM4F,cAAc,IAAAxJ,cAAA,GAAAC,CAAA,SAAGwJ,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtJ,WAAW,CAACuJ,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,CAACL,aAAa,CAAC;UAACvJ,cAAA,GAAAC,CAAA;UAEhF,KAAK,IAAM6D,MAAM,IAAI0F,cAAc,EAAE;YAAAxJ,cAAA,GAAAC,CAAA;YAEnCuC,OAAO,CAACC,GAAG,CAAC,2CAA2CqB,MAAM,EAAE,CAAC;UAClE;QACF,CAAC;UAAA9D,cAAA,GAAA4D,CAAA;QAAA;MACH,CAAC;MAAA,SAZawC,4BAA4BA,CAAA;QAAA,OAAAkD,6BAAA,CAAA3G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA5BwD,4BAA4B;IAAA;EAAA;IAAArE,GAAA;IAAAC,KAAA,EAc1C,SAAQK,mBAAmBA,CAAA,EAAS;MAAA,IAAAwH,MAAA;MAAA7J,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MAElC6J,WAAW,CAAC,YAAM;QAAA9J,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAChB4J,MAAI,CAACE,gBAAgB,CAAC,CAAC;MACzB,CAAC,EAAE,IAAI,CAAC;MAAC/J,cAAA,GAAAC,CAAA;MAGT6J,WAAW,CAAC,YAAM;QAAA9J,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAChB4J,MAAI,CAACxD,0BAA0B,CAAC,CAAC;MACnC,CAAC,EAAE,KAAK,CAAC;IACX;EAAC;IAAAtE,GAAA;IAAAC,KAAA;MAAA,IAAAgI,iBAAA,GAAA9H,iBAAA,CAED,aAAgD;QAAAlC,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAC9C,IAAI,IAAI,CAACE,SAAS,CAAC8D,MAAM,KAAK,CAAC,EAAE;UAAAjE,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UAAA;QAAM,CAAC;UAAAD,cAAA,GAAA4D,CAAA;QAAA;QAExC,IAAM2F,aAAa,IAAAvJ,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACuB,eAAe,CAACX,MAAM,CAACC,kBAAkB;QACpE,IAAMmJ,cAAc,IAAAjK,cAAA,GAAAC,CAAA,SAAGsJ,aAAa,GAAG,IAAI,CAACnJ,WAAW,CAACsF,IAAI;QAAC1F,cAAA,GAAAC,CAAA;QAE7D,IAAIgK,cAAc,IAAI,CAAC,EAAE;UAAAjK,cAAA,GAAA4D,CAAA;UAAA5D,cAAA,GAAAC,CAAA;UAAA;QAAM,CAAC;UAAAD,cAAA,GAAA4D,CAAA;QAAA;QAGhC,IAAMsG,cAAc,IAAAlK,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACE,SAAS,CAACgK,MAAM,CAAC,CAAC,EAAEF,cAAc,CAAC;QAACjK,cAAA,GAAAC,CAAA;QAEhE,KAAK,IAAM6D,MAAM,IAAIoG,cAAc,EAAE;UACnC,IAAMpH,IAAI,IAAA9C,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACF,eAAe,CAACoE,GAAG,CAACL,MAAM,CAAC;UAAC9D,cAAA,GAAAC,CAAA;UAC9C,IAAI,CAAAD,cAAA,GAAA4D,CAAA,WAAAd,IAAI,MAAA9C,cAAA,GAAA4D,CAAA,WAAI,IAAI,CAACgE,cAAc,CAAC9E,IAAI,CAAC,GAAE;YAAA9C,cAAA,GAAA4D,CAAA;YAAA5D,cAAA,GAAAC,CAAA;YACrC,IAAI,CAACuE,WAAW,CAAC1B,IAAI,CAAC;UACxB,CAAC,MAAM;YAAA9C,cAAA,GAAA4D,CAAA;YAAA5D,cAAA,GAAAC,CAAA;YAEL,IAAI,CAACE,SAAS,CAACoG,IAAI,CAACzC,MAAM,CAAC;UAC7B;QACF;MACF,CAAC;MAAA,SApBaiG,gBAAgBA,CAAA;QAAA,OAAAC,iBAAA,CAAArH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBmH,gBAAgB;IAAA;EAAA;IAAAhI,GAAA;IAAAC,KAAA,EAsB9B,SAAQM,uBAAuBA,CAAA,EAAS;MAAA,IAAA8H,MAAA;MAAApK,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACtCN,QAAQ,CAAC0K,gBAAgB,CAAC,QAAQ,EAAE,UAACC,YAAY,EAAK;QAAAtK,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QACpDuC,OAAO,CAACC,GAAG,CAAC,yBAAyB6H,YAAY,EAAE,CAAC;QAACtK,cAAA,GAAAC,CAAA;QAErDmK,MAAI,CAAC/D,0BAA0B,CAAC,CAAC;MACnC,CAAC,CAAC;IACJ;EAAC;IAAAtE,GAAA;IAAAC,KAAA,EAED,SAAQO,oBAAoBA,CAAA,EAAS;MAAA,IAAAgI,MAAA;MAAAvK,cAAA,GAAAuB,CAAA;MAEnC,IAAMiJ,YAAY,IAAAxK,cAAA,GAAAC,CAAA,SAAG,CACnB;QACEwD,EAAE,EAAE,eAAe;QACnBlD,IAAI,EAAE,eAAe;QACrBiF,IAAI,EAAE,aAAsB;QAC5B4B,QAAQ,EAAE,KAAc;QACxB9D,iBAAiB,EAAE,KAAK;QACxByF,YAAY,EAAE,GAAG;QACjBC,eAAe,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;QACjCyB,gBAAgB,EAAE,CAAC;QACnBjC,WAAW,EAAE;UACXK,eAAe,EAAE,KAAK;UACtBF,gBAAgB,EAAE,KAAK;UACvB+B,YAAY,EAAE,IAAI;UAClBhC,eAAe,EAAE,EAAE;UACnBD,aAAa,EAAE,CAAC,YAAY;QAC9B,CAAC;QACD/E,QAAQ,EAAE;UACRC,SAAS,EAAE,KAAK;UAChBgH,SAAS,EAAE,IAAI;UACfC,QAAQ,EAAE,OAAO;UACjBC,aAAa,EAAE;QACjB,CAAC;QACDC,YAAY,EAAE,EAAE;QAChB9C,QAAQ;UAAA,IAAA+C,SAAA,GAAA7I,iBAAA,CAAE,aAAY;YAAAlC,cAAA,GAAAuB,CAAA;YAAAvB,cAAA,GAAAC,CAAA;YAEpB,MAAML,qBAAqB,CAACoL,cAAc,CAAC,KAAK,CAAC;YAAChL,cAAA,GAAAC,CAAA;YAClD,OAAO;cAAEgL,OAAO,EAAE;YAAK,CAAC;UAC1B,CAAC;UAAA,SAJDjD,QAAQA,CAAA;YAAA,OAAA+C,SAAA,CAAApI,KAAA,OAAAC,SAAA;UAAA;UAAA,OAARoF,QAAQ;QAAA;MAKV,CAAC,EACD;QACEvE,EAAE,EAAE,gBAAgB;QACpBlD,IAAI,EAAE,gBAAgB;QACtBiF,IAAI,EAAE,WAAoB;QAC1B4B,QAAQ,EAAE,QAAiB;QAC3B9D,iBAAiB,EAAE,KAAK;QACxByF,YAAY,EAAE,IAAI;QAClBC,eAAe,EAAE,CAAC,GAAG,IAAI,GAAG,IAAI;QAChCyB,gBAAgB,EAAE,IAAI,GAAG,IAAI;QAC7BjC,WAAW,EAAE;UACXK,eAAe,EAAE,IAAI;UACrBF,gBAAgB,EAAE,KAAK;UACvB+B,YAAY,EAAE,KAAK;UACnBhC,eAAe,EAAE,CAAC;UAClBD,aAAa,EAAE,CAAC,QAAQ,EAAW,YAAY;QACjD,CAAC;QACD/E,QAAQ,EAAE;UACRC,SAAS,EAAE,KAAK;UAChBgH,SAAS,EAAE,IAAI;UACfC,QAAQ,EAAE,OAAO;UACjBC,aAAa,EAAE;QACjB,CAAC;QACDC,YAAY,EAAE,EAAE;QAChB9C,QAAQ;UAAA,IAAAkD,UAAA,GAAAhJ,iBAAA,CAAE,aAAY;YAAAlC,cAAA,GAAAuB,CAAA;YAAAvB,cAAA,GAAAC,CAAA;YAEpB,OAAO;cAAEkL,MAAM,EAAE,IAAI;cAAEC,MAAM,EAAE;YAAG,CAAC;UACrC,CAAC;UAAA,SAHDpD,QAAQA,CAAA;YAAA,OAAAkD,UAAA,CAAAvI,KAAA,OAAAC,SAAA;UAAA;UAAA,OAARoF,QAAQ;QAAA;MAIV,CAAC,CACF;MAAChI,cAAA,GAAAC,CAAA;MAEFuK,YAAY,CAACjF,OAAO,CAAC,UAAAzC,IAAI,EAAI;QAAA9C,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAAA,OAAAsK,MAAI,CAAC1H,YAAY,CAACC,IAAI,CAAC;MAAD,CAAC,CAAC;IACvD;EAAC;AAAA;AAAA,IAMGpB,aAAa;EAAA,SAAAA,cAAA;IAAA5B,eAAA,OAAA4B,aAAA;EAAA;EAAA,OAAAI,YAAA,CAAAJ,aAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAAqJ,WAAA,GAAAnJ,iBAAA,CACjB,aAAkC;QAAAlC,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAChCuC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MAC3C,CAAC;MAAA,SAFKN,UAAUA,CAAA;QAAA,OAAAkJ,WAAA,CAAA1I,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVT,UAAU;IAAA;EAAA;AAAA;AAAA,IAQZP,eAAe;EAAA,SAAAA,gBAAA;IAAA9B,eAAA,OAAA8B,eAAA;IAAA,KACXkH,YAAY,IAAA9I,cAAA,GAAAC,CAAA,SAAG;MAAEuH,GAAG,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,OAAO,EAAE,CAAC;MAAEC,OAAO,EAAE;IAAE,CAAC;IAAA,KAC5D3B,iBAAiB,IAAAhG,cAAA,GAAAC,CAAA,SAAG;MAC1BW,QAAQ,EAAE,QAAQ;MAClBF,YAAY,EAAE,GAAG;MACjBC,WAAW,EAAE,MAAM;MACnBiI,UAAU,EAAE;IACd,CAAC;EAAA;EAAA,OAAA9G,YAAA,CAAAF,eAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAAsJ,MAAA,GAAApJ,iBAAA,CAED,aAA6B;QAAA,IAAAqJ,MAAA;QAAAvL,cAAA,GAAAuB,CAAA;QAAAvB,cAAA,GAAAC,CAAA;QAE3B6J,WAAW,CAAC,YAAM;UAAA9J,cAAA,GAAAuB,CAAA;UAAAvB,cAAA,GAAAC,CAAA;UAChBsL,MAAI,CAACC,mBAAmB,CAAC,CAAC;QAC5B,CAAC,EAAE,KAAK,CAAC;QAACxL,cAAA,GAAAC,CAAA;QAEVuC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC,CAAC;MAAA,SAPKL,KAAKA,CAAA;QAAA,OAAAkJ,MAAA,CAAA3I,KAAA,OAAAC,SAAA;MAAA;MAAA,OAALR,KAAK;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA,EASX,SAAA8D,eAAeA,CAAA,EAAG;MAAA9F,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MAChB,OAAA+C,MAAA,CAAAC,MAAA,KAAY,IAAI,CAAC6F,YAAY;IAC/B;EAAC;IAAA/G,GAAA;IAAAC,KAAA,EAED,SAAAiE,oBAAoBA,CAAA,EAAG;MAAAjG,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MACrB,OAAA+C,MAAA,CAAAC,MAAA,KAAY,IAAI,CAAC+C,iBAAiB;IACpC;EAAC;IAAAjE,GAAA;IAAAC,KAAA,EAED,SAAQwJ,mBAAmBA,CAAA,EAAS;MAAAxL,cAAA,GAAAuB,CAAA;MAAAvB,cAAA,GAAAC,CAAA;MAElC,IAAI,CAAC6I,YAAY,GAAG;QAClBtB,GAAG,EAAEiE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QACxBjE,MAAM,EAAEgE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,IAAI;QACzChE,OAAO,EAAE+D,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;QACpC/D,OAAO,EAAE8D,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MAC3B,CAAC;MAAC1L,cAAA,GAAAC,CAAA;MAGF,IAAI,CAAC+F,iBAAiB,GAAG;QACvBpF,QAAQ,EAAEjB,QAAQ,CAACgM,YAAY;QAC/BjL,YAAY,EAAE,EAAE,GAAG+K,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE;QACrC/K,WAAW,EAAE,MAAM;QACnBiI,UAAU,EAAE6C,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;MAC9B,CAAC;IACH;EAAC;AAAA;AAIH,OAAO,IAAME,2BAA2B,IAAA5L,cAAA,GAAAC,CAAA,SAAG,IAAIJ,2BAA2B,CAAC,CAAC;AAC5E,eAAe+L,2BAA2B", "ignoreList": []}