/**
 * Backend Push Notification Service
 * Handles sending push notifications via FCM and Expo Push API
 */

import { Expo, ExpoPushMessage, ExpoPushTicket, ExpoPushReceiptId } from 'expo-server-sdk';

export interface PushNotificationPayload {
  to: string | string[]; // Push token(s)
  title: string;
  body: string;
  data?: Record<string, any>;
  sound?: string;
  badge?: number;
  priority?: 'default' | 'normal' | 'high';
  channelId?: string;
  categoryId?: string;
  ttl?: number; // Time to live in seconds
}

export interface FCMPayload {
  token: string | string[];
  notification: {
    title: string;
    body: string;
    image?: string;
  };
  data?: Record<string, string>;
  android?: {
    priority: 'normal' | 'high';
    notification: {
      channel_id?: string;
      sound?: string;
      badge?: number;
    };
  };
  apns?: {
    payload: {
      aps: {
        alert: {
          title: string;
          body: string;
        };
        sound?: string;
        badge?: number;
        category?: string;
      };
    };
  };
  webpush?: {
    notification: {
      title: string;
      body: string;
      icon?: string;
      badge?: string;
    };
  };
}

export interface NotificationResult {
  success: boolean;
  messageId?: string;
  error?: string;
  details?: any;
}

class PushNotificationBackend {
  private expo: Expo;
  private fcmServerKey: string;

  constructor() {
    this.expo = new Expo();
    this.fcmServerKey = process.env.FCM_SERVER_KEY || '';
  }

  /**
   * Send notification via Expo Push API
   */
  async sendExpoPushNotification(payload: PushNotificationPayload): Promise<NotificationResult[]> {
    try {
      // Validate tokens
      const tokens = Array.isArray(payload.to) ? payload.to : [payload.to];
      const validTokens = tokens.filter(token => Expo.isExpoPushToken(token));
      
      if (validTokens.length === 0) {
        return [{ success: false, error: 'No valid Expo push tokens provided' }];
      }

      // Create messages
      const messages: ExpoPushMessage[] = validTokens.map(token => ({
        to: token,
        title: payload.title,
        body: payload.body,
        data: payload.data || {},
        sound: payload.sound || 'default',
        badge: payload.badge,
        priority: payload.priority || 'default',
        channelId: payload.channelId,
        categoryId: payload.categoryId,
        ttl: payload.ttl,
      }));

      // Send notifications
      const tickets = await this.expo.sendPushNotificationsAsync(messages);
      
      // Process results
      const results: NotificationResult[] = tickets.map((ticket, index) => {
        if (ticket.status === 'ok') {
          return {
            success: true,
            messageId: ticket.id,
          };
        } else {
          return {
            success: false,
            error: ticket.message || 'Unknown error',
            details: ticket.details,
          };
        }
      });

      console.log('Expo push notifications sent:', results);
      return results;
    } catch (error) {
      console.error('Failed to send Expo push notifications:', error);
      return [{ success: false, error: error instanceof Error ? error.message : 'Unknown error' }];
    }
  }

  /**
   * Send notification via Firebase Cloud Messaging
   */
  async sendFCMNotification(payload: FCMPayload): Promise<NotificationResult[]> {
    try {
      if (!this.fcmServerKey) {
        return [{ success: false, error: 'FCM server key not configured' }];
      }

      const tokens = Array.isArray(payload.token) ? payload.token : [payload.token];
      const results: NotificationResult[] = [];

      for (const token of tokens) {
        try {
          const response = await fetch('https://fcm.googleapis.com/fcm/send', {
            method: 'POST',
            headers: {
              'Authorization': `key=${this.fcmServerKey}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              to: token,
              notification: payload.notification,
              data: payload.data,
              android: payload.android,
              apns: payload.apns,
              webpush: payload.webpush,
            }),
          });

          const result = await response.json();
          
          if (response.ok && result.success === 1) {
            results.push({
              success: true,
              messageId: result.multicast_id,
            });
          } else {
            results.push({
              success: false,
              error: result.results?.[0]?.error || 'FCM send failed',
              details: result,
            });
          }
        } catch (error) {
          results.push({
            success: false,
            error: error instanceof Error ? error.message : 'Network error',
          });
        }
      }

      console.log('FCM notifications sent:', results);
      return results;
    } catch (error) {
      console.error('Failed to send FCM notifications:', error);
      return [{ success: false, error: error instanceof Error ? error.message : 'Unknown error' }];
    }
  }

  /**
   * Send notification to user (automatically chooses FCM or Expo based on token)
   */
  async sendNotificationToUser(
    userId: string,
    notification: {
      title: string;
      body: string;
      data?: Record<string, any>;
      type?: string;
    }
  ): Promise<NotificationResult[]> {
    try {
      // Get user's push tokens from database
      const userTokens = await this.getUserPushTokens(userId);
      
      if (userTokens.length === 0) {
        return [{ success: false, error: 'No push tokens found for user' }];
      }

      const results: NotificationResult[] = [];

      // Send to Expo tokens
      const expoTokens = userTokens.filter(t => t.type === 'expo').map(t => t.token);
      if (expoTokens.length > 0) {
        const expoResults = await this.sendExpoPushNotification({
          to: expoTokens,
          title: notification.title,
          body: notification.body,
          data: { ...notification.data, type: notification.type },
        });
        results.push(...expoResults);
      }

      // Send to FCM tokens
      const fcmTokens = userTokens.filter(t => t.type === 'fcm').map(t => t.token);
      if (fcmTokens.length > 0) {
        const fcmResults = await this.sendFCMNotification({
          token: fcmTokens,
          notification: {
            title: notification.title,
            body: notification.body,
          },
          data: {
            ...notification.data,
            type: notification.type || 'general',
          },
        });
        results.push(...fcmResults);
      }

      return results;
    } catch (error) {
      console.error('Failed to send notification to user:', error);
      return [{ success: false, error: error instanceof Error ? error.message : 'Unknown error' }];
    }
  }

  /**
   * Send bulk notifications to multiple users
   */
  async sendBulkNotifications(
    userIds: string[],
    notification: {
      title: string;
      body: string;
      data?: Record<string, any>;
      type?: string;
    }
  ): Promise<{ userId: string; results: NotificationResult[] }[]> {
    const bulkResults = await Promise.all(
      userIds.map(async (userId) => ({
        userId,
        results: await this.sendNotificationToUser(userId, notification),
      }))
    );

    return bulkResults;
  }

  /**
   * Get user's push tokens from database
   */
  private async getUserPushTokens(userId: string): Promise<{ token: string; type: 'expo' | 'fcm'; platform: string }[]> {
    try {
      // This would typically query your database
      // For now, return mock data
      return [
        {
          token: 'ExponentPushToken[mock-token]',
          type: 'expo',
          platform: 'ios',
        },
      ];
    } catch (error) {
      console.error('Failed to get user push tokens:', error);
      return [];
    }
  }

  /**
   * Check Expo push notification receipts
   */
  async checkExpoPushReceipts(receiptIds: ExpoPushReceiptId[]): Promise<any> {
    try {
      const receipts = await this.expo.getPushNotificationReceiptsAsync(receiptIds);
      console.log('Expo push receipts:', receipts);
      return receipts;
    } catch (error) {
      console.error('Failed to check Expo push receipts:', error);
      return null;
    }
  }

  /**
   * Send training reminder notification
   */
  async sendTrainingReminder(userId: string, trainingType: string): Promise<NotificationResult[]> {
    return this.sendNotificationToUser(userId, {
      title: '🎾 Training Reminder',
      body: `Time for your ${trainingType} training session!`,
      data: {
        screen: 'training',
        trainingType,
      },
      type: 'training_reminder',
    });
  }

  /**
   * Send match result notification
   */
  async sendMatchResult(userId: string, matchResult: any): Promise<NotificationResult[]> {
    const title = matchResult.won ? '🏆 Match Won!' : '💪 Good Match!';
    const body = matchResult.won 
      ? `Congratulations! You won ${matchResult.score}`
      : `Great effort! Final score: ${matchResult.score}`;

    return this.sendNotificationToUser(userId, {
      title,
      body,
      data: {
        screen: 'match-details',
        matchId: matchResult.id,
      },
      type: 'match_result',
    });
  }

  /**
   * Send coaching feedback notification
   */
  async sendCoachingFeedback(userId: string, feedbackType: string): Promise<NotificationResult[]> {
    return this.sendNotificationToUser(userId, {
      title: '🤖 New AI Coaching Feedback',
      body: `Your ${feedbackType} analysis is ready!`,
      data: {
        screen: 'coaching',
        feedbackType,
      },
      type: 'coaching_feedback',
    });
  }
}

// Export singleton instance
export const pushNotificationBackend = new PushNotificationBackend();
