e5d1d43c7e719e01c60ef75b694ca7c0
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _SectionList = _interopRequireDefault(require("../../../../exports/SectionList"));
var _createAnimatedComponent = _interopRequireDefault(require("../createAnimatedComponent"));
var SectionListWithEventThrottle = React.forwardRef(function (props, ref) {
  return React.createElement(_SectionList.default, (0, _extends2.default)({
    scrollEventThrottle: 0.0001
  }, props, {
    ref: ref
  }));
});
var _default = exports.default = (0, _createAnimatedComponent.default)(SectionListWithEventThrottle);
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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