{"version": 3, "names": ["_VideoRecordingService", "require", "_MatchRepository", "_FileUploadService", "_performance", "cov_2k9ta5zj0b", "path", "hash", "global", "Function", "gcv", "coverageData", "statementMap", "start", "line", "column", "end", "fnMap", "name", "decl", "loc", "branchMap", "type", "locations", "undefined", "s", "f", "b", "_coverageSchema", "coverage", "actualCoverage", "MatchRecordingService", "_classCallCheck2", "default", "currentSession", "sessionListeners", "scoreListeners", "offlineSyncQueue", "syncInterval", "autoSaveInterval", "_createClass2", "key", "value", "_startMatch", "_asyncToGenerator2", "metadata", "options", "performanceMonitor", "validateMatchMetadata", "Error", "matchRecording", "id", "Date", "now", "Math", "random", "toString", "substr", "Object", "assign", "startTime", "toISOString", "score", "initializeScore", "matchFormat", "statistics", "initializeStatistics", "userId", "status", "createdAt", "updatedAt", "session", "generateSessionId", "match", "currentSet", "currentGame", "isRecording", "isPaused", "pausedTime", "totalPausedDuration", "videoRecordingActive", "enableVideoRecording", "autoScoreDetection", "enableAutoScoreDetection", "videoRecordingService", "startRecording", "videoConfig", "savedMatch", "saveMatchToDatabase", "success", "error", "data", "databaseId", "setupOfflineSync", "notifySessionListeners", "startAutoSave", "console", "cleanupFailedSession", "startMatch", "_x", "_x2", "apply", "arguments", "_addPoint", "winner", "eventType", "length", "shotType", "courtPosition", "gameEvent", "generateEventId", "timestamp", "player", "description", "updatedScore", "updateScore", "updateStatistics", "setComplete", "isSetComplete", "sets", "matchComplete", "isMatchComplete", "gameComplete", "isGameComplete", "endMatch", "updateMatchInDatabase", "notifyScoreListeners", "addPoint", "_x3", "_pauseMatch", "pauseRecording", "pauseMatch", "_resumeMatch", "pauseDuration", "resumeRecording", "resumeMatch", "_endMatch", "endTime", "totalDuration", "durationMinutes", "round", "videoResult", "stopRecording", "uploadResult", "fileUploadService", "uploadVideo", "uri", "folder", "videoUrl", "url", "videoDurationSeconds", "duration", "videoFileSizeBytes", "size", "thumbnail", "thumbnailResult", "uploadThumbnail", "videoThumbnailUrl", "calculateFinalStatistics", "finalMatch", "_cancelMatch", "cancelMatch", "getCurrentSession", "addSessionListener", "listener", "push", "removeSessionListener", "filter", "l", "addScoreListener", "removeScoreListener", "_metadata$opponentNam", "<PERSON><PERSON><PERSON>", "trim", "matchType", "surface", "format", "maxSets", "finalScore", "result", "setsWon", "setsLost", "matchId", "aces", "doubleFaults", "firstServesIn", "firstServesAttempted", "firstServePointsWon", "secondServePointsWon", "firstServeReturnPointsWon", "secondServeReturnPointsWon", "breakPointsConverted", "breakPointsFaced", "winners", "unforcedErrors", "forcedErrors", "totalPointsWon", "totalPointsPlayed", "netPointsAttempted", "netPointsWon", "forehandWinners", "backhandWinners", "forehandErrors", "backhandErrors", "currentScore", "setNumber", "gameNumber", "event", "userGames", "<PERSON><PERSON><PERSON><PERSON>", "is<PERSON><PERSON><PERSON>", "isCompleted", "_this$currentSession", "firstServePercentage", "breakPointsSaved", "pointWinPercentage", "set", "setsToWin", "_saveMatchToDatabase", "matchData", "user_id", "opponent_name", "match_type", "match_format", "location", "court_name", "<PERSON><PERSON><PERSON>", "weather_conditions", "weather", "temperature", "match_date", "split", "start_time", "toTimeString", "current_score", "JSON", "stringify", "created_at", "updated_at", "attempts", "maxAttempts", "_result$data", "matchRepository", "createMatch", "Promise", "resolve", "setTimeout", "_x4", "_updateMatchInDatabase", "updateData", "end_time", "duration_minutes", "getTime", "final_score", "generateFinalScoreString", "determineMatchResult", "sets_won", "sets_lost", "updateMatch", "_x5", "map", "join", "_this", "for<PERSON>ach", "_this2", "Map", "startOfflineSync", "_this3", "clearInterval", "setInterval", "syncOfflineData", "_syncOfflineData", "_this$offlineSyncQueu", "_this$offlineSyncQueu2", "queue", "get", "updates", "_toConsumableArray2", "update", "processOfflineUpdate", "_this$offlineSyncQueu3", "_x6", "_processOfflineUpdate", "warn", "_x7", "_this4", "_cleanupFailedSession", "matchRecordingService", "exports"], "sources": ["MatchRecordingService.ts"], "sourcesContent": ["/**\n * Match Recording Service\n * Core service for recording tennis matches with real-time score tracking\n */\n\nimport { \n  MatchRecording, \n  MatchSession, \n  MatchMetadata, \n  MatchScore, \n  SetScore, \n  GameScore, \n  GameEvent, \n  MatchStatistics,\n  VideoRecordingConfig \n} from '@/src/types/match';\nimport { videoRecordingService } from '@/src/services/video/VideoRecordingService';\nimport { matchRepository } from '@/src/services/database/MatchRepository';\nimport { fileUploadService } from '@/src/services/storage/FileUploadService';\nimport { performanceMonitor } from '@/utils/performance';\n\nexport interface MatchRecordingOptions {\n  enableVideoRecording: boolean;\n  enableAutoScoreDetection: boolean;\n  videoConfig: VideoRecordingConfig;\n  enableStatisticsTracking: boolean;\n}\n\nclass MatchRecordingService {\n  private currentSession: MatchSession | null = null;\n  private sessionListeners: ((session: MatchSession | null) => void)[] = [];\n  private scoreListeners: ((score: MatchScore) => void)[] = [];\n  private offlineSyncQueue: Map<string, any[]> | null = null;\n  private syncInterval: NodeJS.Timeout | null = null;\n  private autoSaveInterval: NodeJS.Timeout | null = null;\n\n  /**\n   * Start a new match recording session with real database integration\n   */\n  async startMatch(\n    metadata: MatchMetadata,\n    options: MatchRecordingOptions\n  ): Promise<MatchSession> {\n    try {\n      performanceMonitor.start('match_recording_start');\n\n      // Validate metadata\n      this.validateMatchMetadata(metadata);\n\n      // Check for existing active session\n      if (this.currentSession) {\n        throw new Error('Another match recording is already in progress');\n      }\n\n      // Initialize match recording\n      const matchRecording: MatchRecording = {\n        id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        metadata: {\n          ...metadata,\n          startTime: new Date().toISOString(),\n        },\n        score: this.initializeScore(metadata.matchFormat),\n        statistics: this.initializeStatistics(metadata.userId),\n        status: 'recording',\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n\n      // Create match session\n      const session: MatchSession = {\n        id: this.generateSessionId(),\n        match: matchRecording,\n        currentSet: 1,\n        currentGame: 1,\n        isRecording: true,\n        isPaused: false,\n        startTime: Date.now(),\n        pausedTime: 0,\n        totalPausedDuration: 0,\n        videoRecordingActive: options.enableVideoRecording,\n        autoScoreDetection: options.enableAutoScoreDetection,\n      };\n\n      // Start video recording if enabled\n      if (options.enableVideoRecording) {\n        await videoRecordingService.startRecording(options.videoConfig);\n      }\n\n      // Save initial match to database with real implementation\n      const savedMatch = await this.saveMatchToDatabase(matchRecording);\n      if (!savedMatch.success) {\n        throw new Error(savedMatch.error || 'Failed to save match to database');\n      }\n\n      session.match.id = savedMatch.data!.id;\n      session.match.databaseId = savedMatch.data!.databaseId;\n\n      // Set up offline sync queue\n      this.setupOfflineSync(session.match.id);\n\n      this.currentSession = session;\n      this.notifySessionListeners();\n\n      // Start auto-save interval\n      this.startAutoSave();\n\n      performanceMonitor.end('match_recording_start');\n      return session;\n    } catch (error) {\n      console.error('Failed to start match recording:', error);\n\n      // Clean up any partial state\n      if (this.currentSession) {\n        await this.cleanupFailedSession();\n      }\n\n      throw error;\n    }\n  }\n\n  /**\n   * Add a point to the current game\n   */\n  async addPoint(\n    winner: 'user' | 'opponent', \n    eventType: 'ace' | 'winner' | 'unforced_error' | 'forced_error' | 'normal' = 'normal',\n    shotType?: string,\n    courtPosition?: string\n  ): Promise<void> {\n    if (!this.currentSession) {\n      throw new Error('No active match session');\n    }\n\n    try {\n      const session = this.currentSession;\n      const currentSet = session.currentSet;\n      const currentGame = session.currentGame;\n\n      // Create game event\n      const gameEvent: GameEvent = {\n        id: this.generateEventId(),\n        timestamp: Date.now(),\n        eventType: eventType === 'normal' ? 'point_won' : eventType,\n        player: winner,\n        shotType: shotType as any,\n        courtPosition: courtPosition as any,\n        description: `Point won by ${winner}`,\n      };\n\n      // Update score\n      const updatedScore = this.updateScore(\n        session.match.score,\n        currentSet,\n        currentGame,\n        winner,\n        gameEvent\n      );\n\n      // Update statistics\n      this.updateStatistics(session.match.statistics, gameEvent);\n\n      // Update session\n      session.match.score = updatedScore;\n      session.match.updatedAt = new Date().toISOString();\n\n      // Check if set or match is complete\n      const setComplete = this.isSetComplete(updatedScore.sets[currentSet - 1]);\n      const matchComplete = this.isMatchComplete(updatedScore, session.match.metadata.matchFormat);\n\n      if (setComplete && !matchComplete) {\n        session.currentSet++;\n        session.currentGame = 1;\n      } else if (!setComplete) {\n        // Check if game is complete\n        const gameComplete = this.isGameComplete(\n          updatedScore.sets[currentSet - 1],\n          currentGame\n        );\n        if (gameComplete) {\n          session.currentGame++;\n        }\n      }\n\n      if (matchComplete) {\n        await this.endMatch();\n      } else {\n        // Save updated match to database\n        try {\n          await this.updateMatchInDatabase(session.match);\n        } catch (error) {\n          console.error('Failed to update match in database:', error);\n          // Continue execution - don't fail the point recording for database issues\n        }\n      }\n\n      this.notifyScoreListeners();\n      this.notifySessionListeners();\n    } catch (error) {\n      console.error('Failed to add point:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Pause the current match\n   */\n  async pauseMatch(): Promise<void> {\n    if (!this.currentSession || this.currentSession.isPaused) {\n      return;\n    }\n\n    try {\n      this.currentSession.isPaused = true;\n      this.currentSession.pausedTime = Date.now();\n      this.currentSession.match.status = 'paused';\n\n      // Pause video recording if active\n      if (this.currentSession.videoRecordingActive) {\n        await videoRecordingService.pauseRecording();\n      }\n\n      try {\n        await this.updateMatchInDatabase(this.currentSession.match);\n      } catch (error) {\n        console.error('Failed to update match in database:', error);\n        // Continue execution - don't fail pause for database issues\n      }\n      this.notifySessionListeners();\n    } catch (error) {\n      console.error('Failed to pause match:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Resume the current match\n   */\n  async resumeMatch(): Promise<void> {\n    if (!this.currentSession || !this.currentSession.isPaused) {\n      return;\n    }\n\n    try {\n      const pauseDuration = Date.now() - this.currentSession.pausedTime;\n      this.currentSession.totalPausedDuration += pauseDuration;\n      this.currentSession.isPaused = false;\n      this.currentSession.pausedTime = 0;\n      this.currentSession.match.status = 'recording';\n\n      // Resume video recording if active\n      if (this.currentSession.videoRecordingActive) {\n        await videoRecordingService.resumeRecording();\n      }\n\n      try {\n        await this.updateMatchInDatabase(this.currentSession.match);\n      } catch (error) {\n        console.error('Failed to update match in database:', error);\n        // Continue execution - don't fail resume for database issues\n      }\n      this.notifySessionListeners();\n    } catch (error) {\n      console.error('Failed to resume match:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * End the current match\n   */\n  async endMatch(): Promise<MatchRecording> {\n    if (!this.currentSession) {\n      throw new Error('No active match session');\n    }\n\n    try {\n      performanceMonitor.start('match_recording_end');\n\n      const session = this.currentSession;\n      const endTime = Date.now();\n      const totalDuration = (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60;\n\n      // Update match metadata\n      session.match.metadata.endTime = new Date().toISOString();\n      session.match.metadata.durationMinutes = Math.round(totalDuration);\n      session.match.status = 'completed';\n\n      // Stop video recording if active\n      if (session.videoRecordingActive) {\n        const videoResult = await videoRecordingService.stopRecording();\n\n        // Upload video to storage\n        const uploadResult = await fileUploadService.uploadVideo(videoResult.uri, {\n          folder: `matches/${session.match.id || 'temp'}`,\n        });\n\n        if (uploadResult.data) {\n          session.match.videoUrl = uploadResult.data.url;\n          session.match.videoDurationSeconds = videoResult.duration;\n          session.match.videoFileSizeBytes = uploadResult.data.size;\n\n          // Upload thumbnail if available\n          if (videoResult.thumbnail) {\n            const thumbnailResult = await fileUploadService.uploadThumbnail(\n              videoResult.uri,\n              videoResult.thumbnail,\n              {\n                folder: `matches/${session.match.id || 'temp'}/thumbnails`,\n              }\n            );\n\n            if (thumbnailResult.data) {\n              session.match.videoThumbnailUrl = thumbnailResult.data.url;\n            }\n          }\n        }\n      }\n\n      // Calculate final statistics\n      this.calculateFinalStatistics(session.match.statistics, session.match.score);\n\n      // Save final match to database\n      await this.updateMatchInDatabase(session.match);\n\n      // Store the final match before clearing session\n      const finalMatch = { ...session.match };\n\n      // Clear current session\n      this.currentSession = null;\n      this.notifySessionListeners();\n\n      performanceMonitor.end('match_recording_end');\n      return finalMatch;\n    } catch (error) {\n      console.error('Failed to end match:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Cancel the current match\n   */\n  async cancelMatch(): Promise<void> {\n    if (!this.currentSession) {\n      return;\n    }\n\n    try {\n      // Stop video recording if active\n      if (this.currentSession.videoRecordingActive) {\n        await videoRecordingService.stopRecording();\n      }\n\n      // Update match status\n      this.currentSession.match.status = 'cancelled';\n\n      // Update in database\n      await this.updateMatchInDatabase(this.currentSession.match);\n\n      // Clear session\n      this.currentSession = null;\n      this.notifySessionListeners();\n    } catch (error) {\n      console.error('Failed to cancel match:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current session\n   */\n  getCurrentSession(): MatchSession | null {\n    return this.currentSession;\n  }\n\n  /**\n   * Add session listener\n   */\n  addSessionListener(listener: (session: MatchSession | null) => void): void {\n    this.sessionListeners.push(listener);\n  }\n\n  /**\n   * Remove session listener\n   */\n  removeSessionListener(listener: (session: MatchSession | null) => void): void {\n    this.sessionListeners = this.sessionListeners.filter(l => l !== listener);\n  }\n\n  /**\n   * Add score listener\n   */\n  addScoreListener(listener: (score: MatchScore) => void): void {\n    this.scoreListeners.push(listener);\n  }\n\n  /**\n   * Remove score listener\n   */\n  removeScoreListener(listener: (score: MatchScore) => void): void {\n    this.scoreListeners = this.scoreListeners.filter(l => l !== listener);\n  }\n\n  // Private helper methods\n\n  private validateMatchMetadata(metadata: MatchMetadata): void {\n    if (!metadata.opponentName?.trim()) {\n      throw new Error('Opponent name is required');\n    }\n    if (!metadata.userId) {\n      throw new Error('User ID is required');\n    }\n    if (!metadata.matchType) {\n      throw new Error('Match type is required');\n    }\n    if (!metadata.matchFormat) {\n      throw new Error('Match format is required');\n    }\n    if (!metadata.surface) {\n      throw new Error('Court surface is required');\n    }\n  }\n\n  private initializeScore(format: string): MatchScore {\n    const maxSets = format === 'best_of_5' ? 5 : 3;\n    return {\n      sets: [],\n      finalScore: '',\n      result: 'win', // Will be determined at match end\n      setsWon: 0,\n      setsLost: 0,\n    };\n  }\n\n  private initializeStatistics(userId: string): MatchStatistics {\n    return {\n      matchId: '', // Will be set when match is saved\n      userId,\n      aces: 0,\n      doubleFaults: 0,\n      firstServesIn: 0,\n      firstServesAttempted: 0,\n      firstServePointsWon: 0,\n      secondServePointsWon: 0,\n      firstServeReturnPointsWon: 0,\n      secondServeReturnPointsWon: 0,\n      breakPointsConverted: 0,\n      breakPointsFaced: 0,\n      winners: 0,\n      unforcedErrors: 0,\n      forcedErrors: 0,\n      totalPointsWon: 0,\n      totalPointsPlayed: 0,\n      netPointsAttempted: 0,\n      netPointsWon: 0,\n      forehandWinners: 0,\n      backhandWinners: 0,\n      forehandErrors: 0,\n      backhandErrors: 0,\n    };\n  }\n\n  private updateScore(\n    currentScore: MatchScore,\n    setNumber: number,\n    gameNumber: number,\n    winner: 'user' | 'opponent',\n    event: GameEvent\n  ): MatchScore {\n    // Implementation for updating tennis score\n    // This is a simplified version - full tennis scoring logic would be more complex\n    const updatedScore = { ...currentScore };\n    \n    // Ensure we have the current set\n    while (updatedScore.sets.length < setNumber) {\n      updatedScore.sets.push({\n        setNumber: updatedScore.sets.length + 1,\n        userGames: 0,\n        opponentGames: 0,\n        isTiebreak: false,\n        isCompleted: false,\n      });\n    }\n\n    const currentSet = updatedScore.sets[setNumber - 1];\n    \n    // Add point logic here (simplified)\n    if (winner === 'user') {\n      // User wins point - implement tennis scoring logic\n      // This would include 15, 30, 40, game logic\n    } else {\n      // Opponent wins point\n    }\n\n    return updatedScore;\n  }\n\n  private updateStatistics(statistics: MatchStatistics, event: GameEvent): void {\n    statistics.totalPointsPlayed++;\n\n    if (event.player === 'user') {\n      statistics.totalPointsWon++;\n    }\n\n    switch (event.eventType) {\n      case 'ace':\n        statistics.aces++;\n        break;\n      case 'double_fault':\n        statistics.doubleFaults++;\n        break;\n      case 'winner':\n        statistics.winners++;\n        break;\n      case 'unforced_error':\n        statistics.unforcedErrors++;\n        break;\n      case 'forced_error':\n        statistics.forcedErrors++;\n        break;\n    }\n  }\n\n  /**\n   * Calculate final statistics for completed match\n   */\n  private calculateFinalStatistics(statistics: MatchStatistics, score: MatchScore): void {\n    // Calculate percentages and derived statistics\n    if (statistics.firstServesAttempted > 0) {\n      statistics.firstServePercentage = Math.round(\n        (statistics.firstServesIn / statistics.firstServesAttempted) * 100\n      );\n    }\n\n    if (statistics.breakPointsFaced > 0) {\n      statistics.breakPointsSaved = statistics.breakPointsFaced - statistics.breakPointsConverted;\n    }\n\n    // Calculate point win percentages\n    if (statistics.totalPointsPlayed > 0) {\n      statistics.pointWinPercentage = Math.round(\n        (statistics.totalPointsWon / statistics.totalPointsPlayed) * 100\n      );\n    }\n\n    // Update match ID in statistics\n    statistics.matchId = this.currentSession?.match.id || '';\n  }\n\n  /**\n   * Check if a set is complete\n   */\n  private isSetComplete(set: SetScore): boolean {\n    const userGames = set.userGames;\n    const opponentGames = set.opponentGames;\n\n    // Standard set: first to 6 games with 2-game lead, or 7-6 with tiebreak\n    if (userGames >= 6 && userGames - opponentGames >= 2) {\n      return true;\n    }\n    if (opponentGames >= 6 && opponentGames - userGames >= 2) {\n      return true;\n    }\n    if ((userGames === 7 && opponentGames === 6) || (opponentGames === 7 && userGames === 6)) {\n      return true;\n    }\n\n    return false;\n  }\n\n  /**\n   * Check if a game is complete\n   */\n  private isGameComplete(set: SetScore, gameNumber: number): boolean {\n    // Simplified game completion logic\n    // In a real implementation, this would track individual game scores (15, 30, 40, game)\n    return false; // For now, games are completed manually\n  }\n\n  /**\n   * Check if the match is complete\n   */\n  private isMatchComplete(score: MatchScore, format: string): boolean {\n    const setsToWin = format === 'best_of_5' ? 3 : 2;\n    return score.setsWon >= setsToWin || score.setsLost >= setsToWin;\n  }\n\n\n\n  private async saveMatchToDatabase(match: MatchRecording): Promise<{ success: boolean; data?: any; error?: string }> {\n    try {\n      // Prepare match data for database with comprehensive mapping\n      const matchData = {\n        id: match.id,\n        user_id: match.metadata.userId,\n        opponent_name: match.metadata.opponentName,\n        match_type: match.metadata.matchType || 'friendly',\n        match_format: match.metadata.matchFormat,\n        surface: match.metadata.surface,\n        location: match.metadata.location,\n        court_name: match.metadata.courtName,\n        weather_conditions: match.metadata.weather,\n        temperature: match.metadata.temperature,\n        match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],\n        start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],\n        status: match.status,\n        current_score: JSON.stringify(match.score),\n        statistics: JSON.stringify(match.statistics),\n        created_at: match.createdAt,\n        updated_at: match.updatedAt,\n      };\n\n      // Save to database with retry logic\n      let attempts = 0;\n      const maxAttempts = 3;\n\n      while (attempts < maxAttempts) {\n        try {\n          const result = await matchRepository.createMatch(matchData);\n\n          if (result.error) {\n            if (attempts === maxAttempts - 1) {\n              return { success: false, error: result.error };\n            }\n            attempts++;\n            await new Promise(resolve => setTimeout(resolve, 1000 * attempts));\n            continue;\n          }\n\n          return { success: true, data: { id: match.id, databaseId: result.data?.id } };\n        } catch (error) {\n          attempts++;\n          if (attempts === maxAttempts) {\n            throw error;\n          }\n          await new Promise(resolve => setTimeout(resolve, 1000 * attempts));\n        }\n      }\n\n      return { success: false, error: 'Failed to save after multiple attempts' };\n    } catch (error) {\n      console.error('Error saving match to database:', error);\n      return { success: false, error: 'Database connection failed' };\n    }\n  }\n\n  private async updateMatchInDatabase(match: MatchRecording): Promise<MatchRecording> {\n    try {\n      if (!match.id) {\n        throw new Error('Match ID is required for update');\n      }\n\n      const updateData = {\n        current_score: JSON.stringify(match.score),\n        statistics: JSON.stringify(match.statistics),\n        status: match.status,\n        updated_at: new Date().toISOString(),\n      };\n\n      // Add completion data if match is finished\n      if (match.status === 'completed' && match.metadata.endTime) {\n        updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];\n        updateData.duration_minutes = Math.round(\n          (new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60)\n        );\n        updateData.final_score = this.generateFinalScoreString(match.score);\n        updateData.result = this.determineMatchResult(match.score, match.metadata.userId);\n        updateData.sets_won = match.score.setsWon;\n        updateData.sets_lost = match.score.setsLost;\n      }\n\n      const result = await matchRepository.updateMatch(match.id, updateData);\n\n      if (result && result.error) {\n        throw new Error(result.error);\n      }\n\n      // Return the updated match\n      return {\n        ...match,\n        updatedAt: new Date().toISOString(),\n      };\n    } catch (error) {\n      console.error('Error updating match in database:', error);\n      throw new Error('Database connection failed');\n    }\n  }\n\n  /**\n   * Generate final score string for display\n   */\n  private generateFinalScoreString(score: MatchScore): string {\n    if (!score.sets || score.sets.length === 0) {\n      return '0-0';\n    }\n\n    return score.sets\n      .map(set => `${set.userGames}-${set.opponentGames}`)\n      .join(', ');\n  }\n\n  /**\n   * Determine match result from score\n   */\n  private determineMatchResult(score: MatchScore, userId: string): 'win' | 'loss' | 'draw' {\n    if (score.setsWon > score.setsLost) {\n      return 'win';\n    } else if (score.setsLost > score.setsWon) {\n      return 'loss';\n    }\n    return 'draw';\n  }\n\n  private generateSessionId(): string {\n    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private generateEventId(): string {\n    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private notifySessionListeners(): void {\n    this.sessionListeners.forEach(listener => listener(this.currentSession));\n  }\n\n  private notifyScoreListeners(): void {\n    if (this.currentSession) {\n      this.scoreListeners.forEach(listener => listener(this.currentSession!.match.score));\n    }\n  }\n\n  /**\n   * Set up offline synchronization for the match\n   */\n  private setupOfflineSync(matchId: string): void {\n    try {\n      // Initialize offline sync queue for this match\n      if (!this.offlineSyncQueue) {\n        this.offlineSyncQueue = new Map();\n      }\n\n      // Create sync queue for this match\n      this.offlineSyncQueue.set(matchId, []);\n\n      // Set up periodic sync if online\n      this.startOfflineSync(matchId);\n    } catch (error) {\n      console.error('Failed to setup offline sync:', error);\n    }\n  }\n\n  /**\n   * Start offline synchronization process\n   */\n  private startOfflineSync(matchId: string): void {\n    // Clear any existing sync interval\n    if (this.syncInterval) {\n      clearInterval(this.syncInterval);\n    }\n\n    // Set up sync interval (every 30 seconds)\n    this.syncInterval = setInterval(async () => {\n      await this.syncOfflineData(matchId);\n    }, 30000);\n  }\n\n  /**\n   * Synchronize offline data with server\n   */\n  private async syncOfflineData(matchId: string): Promise<void> {\n    try {\n      const queue = this.offlineSyncQueue?.get(matchId);\n      if (!queue || queue.length === 0) {\n        return;\n      }\n\n      // Process queued updates\n      const updates = [...queue];\n      this.offlineSyncQueue?.set(matchId, []); // Clear queue\n\n      for (const update of updates) {\n        try {\n          await this.processOfflineUpdate(update);\n        } catch (error) {\n          console.error('Failed to sync update:', error);\n          // Re-queue failed update\n          this.offlineSyncQueue?.get(matchId)?.push(update);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to sync offline data:', error);\n    }\n  }\n\n  /**\n   * Process individual offline update\n   */\n  private async processOfflineUpdate(update: any): Promise<void> {\n    switch (update.type) {\n      case 'match_update':\n        await this.updateMatchInDatabase(update.data);\n        break;\n      case 'score_update':\n        // Handle score updates\n        break;\n      case 'statistics_update':\n        // Handle statistics updates\n        break;\n      default:\n        console.warn('Unknown update type:', update.type);\n    }\n  }\n\n  /**\n   * Start auto-save functionality\n   */\n  private startAutoSave(): void {\n    // Clear any existing auto-save interval\n    if (this.autoSaveInterval) {\n      clearInterval(this.autoSaveInterval);\n    }\n\n    // Set up auto-save interval (every 2 minutes)\n    this.autoSaveInterval = setInterval(async () => {\n      if (this.currentSession) {\n        try {\n          await this.updateMatchInDatabase(this.currentSession.match);\n        } catch (error) {\n          console.error('Auto-save failed:', error);\n        }\n      }\n    }, 120000); // 2 minutes\n  }\n\n  /**\n   * Clean up failed session\n   */\n  private async cleanupFailedSession(): Promise<void> {\n    try {\n      if (this.currentSession) {\n        // Stop video recording if active\n        if (this.currentSession.videoRecordingActive) {\n          await videoRecordingService.stopRecording();\n        }\n\n        // Clear intervals\n        if (this.autoSaveInterval) {\n          clearInterval(this.autoSaveInterval);\n          this.autoSaveInterval = null;\n        }\n\n        if (this.syncInterval) {\n          clearInterval(this.syncInterval);\n          this.syncInterval = null;\n        }\n\n        // Clear session\n        this.currentSession = null;\n      }\n    } catch (error) {\n      console.error('Failed to cleanup session:', error);\n    }\n  }\n}\n\n// Export singleton instance\nexport const matchRecordingService = new MatchRecordingService();\n"], "mappings": ";;;;;;;;;AAgBA,IAAAA,sBAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,kBAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAAyD,SAAAI,eAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,IAAA;EAAA,IAAAC,MAAA,OAAAC,QAAA;EAAA,IAAAC,GAAA;EAAA,IAAAC,YAAA;IAAAL,IAAA;IAAAM,YAAA;MAAA;QAAAC,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;MAAA;QAAAF,KAAA;UAAAC,IAAA;UAAAC,MAAA;QAAA;QAAAC,GAAA;UAAAF,IAAA;UAAAC,MAAA;QAAA;MAAA;IAAA;IAAAE,KAAA;MAAA;QAAAC,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAI,IAAA;QAAAC,IAAA;UAAAN,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAK,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;IAAA;IAAAO,SAAA;MAAA;QAAAD,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAD,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;MAAA;QAAAM,GAAA;UAAAP,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;QAAAO,IAAA;QAAAC,SAAA;UAAAV,KAAA;YAAAC,IAAA;YAAAC,MAAA;UAAA;UAAAC,GAAA;YAAAF,IAAA;YAAAC,MAAA;UAAA;QAAA;UAAAF,KAAA;YAAAC,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;UAAAR,GAAA;YAAAF,IAAA,EAAAU,SAAA;YAAAT,MAAA,EAAAS;UAAA;QAAA;QAAAV,IAAA;MAAA;IAAA;IAAAW,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,CAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;MAAA;IAAA;IAAAC,eAAA;IAAArB,IAAA;EAAA;EAAA,IAAAsB,QAAA,GAAArB,MAAA,CAAAE,GAAA,MAAAF,MAAA,CAAAE,GAAA;EAAA,KAAAmB,QAAA,CAAAvB,IAAA,KAAAuB,QAAA,CAAAvB,IAAA,EAAAC,IAAA,KAAAA,IAAA;IAAAsB,QAAA,CAAAvB,IAAA,IAAAK,YAAA;EAAA;EAAA,IAAAmB,cAAA,GAAAD,QAAA,CAAAvB,IAAA;EAAA;IAAAD,cAAA,YAAAA,CAAA;MAAA,OAAAyB,cAAA;IAAA;EAAA;EAAA,OAAAA,cAAA;AAAA;AAAAzB,cAAA;AAAA,IASnD0B,qBAAqB;EAAA,SAAAA,sBAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,qBAAA;IAAA,KACjBG,cAAc,IAAA7B,cAAA,GAAAoB,CAAA,OAAwB,IAAI;IAAA,KAC1CU,gBAAgB,IAAA9B,cAAA,GAAAoB,CAAA,OAA+C,EAAE;IAAA,KACjEW,cAAc,IAAA/B,cAAA,GAAAoB,CAAA,OAAoC,EAAE;IAAA,KACpDY,gBAAgB,IAAAhC,cAAA,GAAAoB,CAAA,OAA8B,IAAI;IAAA,KAClDa,YAAY,IAAAjC,cAAA,GAAAoB,CAAA,OAA0B,IAAI;IAAA,KAC1Cc,gBAAgB,IAAAlC,cAAA,GAAAoB,CAAA,OAA0B,IAAI;EAAA;EAAA,WAAAe,aAAA,CAAAP,OAAA,EAAAF,qBAAA;IAAAU,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,OAAAC,kBAAA,CAAAX,OAAA,EAKtD,WACEY,QAAuB,EACvBC,OAA8B,EACP;QAAAzC,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QACvB,IAAI;UAAApB,cAAA,GAAAoB,CAAA;UACFsB,+BAAkB,CAAClC,KAAK,CAAC,uBAAuB,CAAC;UAACR,cAAA,GAAAoB,CAAA;UAGlD,IAAI,CAACuB,qBAAqB,CAACH,QAAQ,CAAC;UAACxC,cAAA,GAAAoB,CAAA;UAGrC,IAAI,IAAI,CAACS,cAAc,EAAE;YAAA7B,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACvB,MAAM,IAAIwB,KAAK,CAAC,gDAAgD,CAAC;UACnE,CAAC;YAAA5C,cAAA,GAAAsB,CAAA;UAAA;UAGD,IAAMuB,cAA8B,IAAA7C,cAAA,GAAAoB,CAAA,QAAG;YACrC0B,EAAE,EAAE,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACpEZ,QAAQ,EAAAa,MAAA,CAAAC,MAAA,KACHd,QAAQ;cACXe,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;YAAC,EACpC;YACDC,KAAK,EAAE,IAAI,CAACC,eAAe,CAAClB,QAAQ,CAACmB,WAAW,CAAC;YACjDC,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACrB,QAAQ,CAACsB,MAAM,CAAC;YACtDC,MAAM,EAAE,WAAW;YACnBC,SAAS,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;YACnCS,SAAS,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UACpC,CAAC;UAGD,IAAMU,OAAqB,IAAAlE,cAAA,GAAAoB,CAAA,QAAG;YAC5B0B,EAAE,EAAE,IAAI,CAACqB,iBAAiB,CAAC,CAAC;YAC5BC,KAAK,EAAEvB,cAAc;YACrBwB,UAAU,EAAE,CAAC;YACbC,WAAW,EAAE,CAAC;YACdC,WAAW,EAAE,IAAI;YACjBC,QAAQ,EAAE,KAAK;YACfjB,SAAS,EAAER,IAAI,CAACC,GAAG,CAAC,CAAC;YACrByB,UAAU,EAAE,CAAC;YACbC,mBAAmB,EAAE,CAAC;YACtBC,oBAAoB,EAAElC,OAAO,CAACmC,oBAAoB;YAClDC,kBAAkB,EAAEpC,OAAO,CAACqC;UAC9B,CAAC;UAAC9E,cAAA,GAAAoB,CAAA;UAGF,IAAIqB,OAAO,CAACmC,oBAAoB,EAAE;YAAA5E,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAChC,MAAM2D,4CAAqB,CAACC,cAAc,CAACvC,OAAO,CAACwC,WAAW,CAAC;UACjE,CAAC;YAAAjF,cAAA,GAAAsB,CAAA;UAAA;UAGD,IAAM4D,UAAU,IAAAlF,cAAA,GAAAoB,CAAA,cAAS,IAAI,CAAC+D,mBAAmB,CAACtC,cAAc,CAAC;UAAC7C,cAAA,GAAAoB,CAAA;UAClE,IAAI,CAAC8D,UAAU,CAACE,OAAO,EAAE;YAAApF,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACvB,MAAM,IAAIwB,KAAK,CAAC,CAAA5C,cAAA,GAAAsB,CAAA,UAAA4D,UAAU,CAACG,KAAK,MAAArF,cAAA,GAAAsB,CAAA,UAAI,kCAAkC,EAAC;UACzE,CAAC;YAAAtB,cAAA,GAAAsB,CAAA;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAED8C,OAAO,CAACE,KAAK,CAACtB,EAAE,GAAGoC,UAAU,CAACI,IAAI,CAAExC,EAAE;UAAC9C,cAAA,GAAAoB,CAAA;UACvC8C,OAAO,CAACE,KAAK,CAACmB,UAAU,GAAGL,UAAU,CAACI,IAAI,CAAEC,UAAU;UAACvF,cAAA,GAAAoB,CAAA;UAGvD,IAAI,CAACoE,gBAAgB,CAACtB,OAAO,CAACE,KAAK,CAACtB,EAAE,CAAC;UAAC9C,cAAA,GAAAoB,CAAA;UAExC,IAAI,CAACS,cAAc,GAAGqC,OAAO;UAAClE,cAAA,GAAAoB,CAAA;UAC9B,IAAI,CAACqE,sBAAsB,CAAC,CAAC;UAACzF,cAAA,GAAAoB,CAAA;UAG9B,IAAI,CAACsE,aAAa,CAAC,CAAC;UAAC1F,cAAA,GAAAoB,CAAA;UAErBsB,+BAAkB,CAAC/B,GAAG,CAAC,uBAAuB,CAAC;UAACX,cAAA,GAAAoB,CAAA;UAChD,OAAO8C,OAAO;QAChB,CAAC,CAAC,OAAOmB,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAACrF,cAAA,GAAAoB,CAAA;UAGzD,IAAI,IAAI,CAACS,cAAc,EAAE;YAAA7B,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACvB,MAAM,IAAI,CAACwE,oBAAoB,CAAC,CAAC;UACnC,CAAC;YAAA5F,cAAA,GAAAsB,CAAA;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAED,MAAMiE,KAAK;QACb;MACF,CAAC;MAAA,SA/EKQ,UAAUA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAzD,WAAA,CAAA0D,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVJ,UAAU;IAAA;EAAA;IAAAzD,GAAA;IAAAC,KAAA;MAAA,IAAA6D,SAAA,OAAA3D,kBAAA,CAAAX,OAAA,EAoFhB,WACEuE,MAA2B,EAIZ;QAAA,IAHfC,SAA0E,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAA9E,SAAA,GAAA8E,SAAA,OAAAjG,cAAA,GAAAsB,CAAA,UAAG,QAAQ;QAAA,IACrFgF,QAAiB,GAAAL,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAA9E,SAAA;QAAA,IACjBoF,aAAsB,GAAAN,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAA9E,SAAA;QAAAnB,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAEtB,IAAI,CAAC,IAAI,CAACS,cAAc,EAAE;UAAA7B,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACxB,MAAM,IAAIwB,KAAK,CAAC,yBAAyB,CAAC;QAC5C,CAAC;UAAA5C,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAED,IAAI;UACF,IAAM8C,OAAO,IAAAlE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACS,cAAc;UACnC,IAAMwC,UAAU,IAAArE,cAAA,GAAAoB,CAAA,QAAG8C,OAAO,CAACG,UAAU;UACrC,IAAMC,WAAW,IAAAtE,cAAA,GAAAoB,CAAA,QAAG8C,OAAO,CAACI,WAAW;UAGvC,IAAMkC,SAAoB,IAAAxG,cAAA,GAAAoB,CAAA,QAAG;YAC3B0B,EAAE,EAAE,IAAI,CAAC2D,eAAe,CAAC,CAAC;YAC1BC,SAAS,EAAE3D,IAAI,CAACC,GAAG,CAAC,CAAC;YACrBoD,SAAS,EAAEA,SAAS,KAAK,QAAQ,IAAApG,cAAA,GAAAsB,CAAA,UAAG,WAAW,KAAAtB,cAAA,GAAAsB,CAAA,UAAG8E,SAAS;YAC3DO,MAAM,EAAER,MAAM;YACdG,QAAQ,EAAEA,QAAe;YACzBC,aAAa,EAAEA,aAAoB;YACnCK,WAAW,EAAE,gBAAgBT,MAAM;UACrC,CAAC;UAGD,IAAMU,YAAY,IAAA7G,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC0F,WAAW,CACnC5C,OAAO,CAACE,KAAK,CAACX,KAAK,EACnBY,UAAU,EACVC,WAAW,EACX6B,MAAM,EACNK,SACF,CAAC;UAACxG,cAAA,GAAAoB,CAAA;UAGF,IAAI,CAAC2F,gBAAgB,CAAC7C,OAAO,CAACE,KAAK,CAACR,UAAU,EAAE4C,SAAS,CAAC;UAACxG,cAAA,GAAAoB,CAAA;UAG3D8C,OAAO,CAACE,KAAK,CAACX,KAAK,GAAGoD,YAAY;UAAC7G,cAAA,GAAAoB,CAAA;UACnC8C,OAAO,CAACE,KAAK,CAACH,SAAS,GAAG,IAAIlB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UAGlD,IAAMwD,WAAW,IAAAhH,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAAC6F,aAAa,CAACJ,YAAY,CAACK,IAAI,CAAC7C,UAAU,GAAG,CAAC,CAAC,CAAC;UACzE,IAAM8C,aAAa,IAAAnH,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACgG,eAAe,CAACP,YAAY,EAAE3C,OAAO,CAACE,KAAK,CAAC5B,QAAQ,CAACmB,WAAW,CAAC;UAAC3D,cAAA,GAAAoB,CAAA;UAE7F,IAAI,CAAApB,cAAA,GAAAsB,CAAA,UAAA0F,WAAW,MAAAhH,cAAA,GAAAsB,CAAA,UAAI,CAAC6F,aAAa,GAAE;YAAAnH,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACjC8C,OAAO,CAACG,UAAU,EAAE;YAACrE,cAAA,GAAAoB,CAAA;YACrB8C,OAAO,CAACI,WAAW,GAAG,CAAC;UACzB,CAAC,MAAM;YAAAtE,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAAA,IAAI,CAAC4F,WAAW,EAAE;cAAAhH,cAAA,GAAAsB,CAAA;cAEvB,IAAM+F,YAAY,IAAArH,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACkG,cAAc,CACtCT,YAAY,CAACK,IAAI,CAAC7C,UAAU,GAAG,CAAC,CAAC,EACjCC,WACF,CAAC;cAACtE,cAAA,GAAAoB,CAAA;cACF,IAAIiG,YAAY,EAAE;gBAAArH,cAAA,GAAAsB,CAAA;gBAAAtB,cAAA,GAAAoB,CAAA;gBAChB8C,OAAO,CAACI,WAAW,EAAE;cACvB,CAAC;gBAAAtE,cAAA,GAAAsB,CAAA;cAAA;YACH,CAAC;cAAAtB,cAAA,GAAAsB,CAAA;YAAA;UAAD;UAACtB,cAAA,GAAAoB,CAAA;UAED,IAAI+F,aAAa,EAAE;YAAAnH,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACjB,MAAM,IAAI,CAACmG,QAAQ,CAAC,CAAC;UACvB,CAAC,MAAM;YAAAvH,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAEL,IAAI;cAAApB,cAAA,GAAAoB,CAAA;cACF,MAAM,IAAI,CAACoG,qBAAqB,CAACtD,OAAO,CAACE,KAAK,CAAC;YACjD,CAAC,CAAC,OAAOiB,KAAK,EAAE;cAAArF,cAAA,GAAAoB,CAAA;cACduE,OAAO,CAACN,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;YAE7D;UACF;UAACrF,cAAA,GAAAoB,CAAA;UAED,IAAI,CAACqG,oBAAoB,CAAC,CAAC;UAACzH,cAAA,GAAAoB,CAAA;UAC5B,IAAI,CAACqE,sBAAsB,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOJ,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAACrF,cAAA,GAAAoB,CAAA;UAC7C,MAAMiE,KAAK;QACb;MACF,CAAC;MAAA,SA9EKqC,QAAQA,CAAAC,GAAA;QAAA,OAAAzB,SAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAARyB,QAAQ;IAAA;EAAA;IAAAtF,GAAA;IAAAC,KAAA;MAAA,IAAAuF,WAAA,OAAArF,kBAAA,CAAAX,OAAA,EAmFd,aAAkC;QAAA5B,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAChC,IAAI,CAAApB,cAAA,GAAAsB,CAAA,YAAC,IAAI,CAACO,cAAc,MAAA7B,cAAA,GAAAsB,CAAA,WAAI,IAAI,CAACO,cAAc,CAAC2C,QAAQ,GAAE;UAAAxE,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACxD;QACF,CAAC;UAAApB,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAED,IAAI;UAAApB,cAAA,GAAAoB,CAAA;UACF,IAAI,CAACS,cAAc,CAAC2C,QAAQ,GAAG,IAAI;UAACxE,cAAA,GAAAoB,CAAA;UACpC,IAAI,CAACS,cAAc,CAAC4C,UAAU,GAAG1B,IAAI,CAACC,GAAG,CAAC,CAAC;UAAChD,cAAA,GAAAoB,CAAA;UAC5C,IAAI,CAACS,cAAc,CAACuC,KAAK,CAACL,MAAM,GAAG,QAAQ;UAAC/D,cAAA,GAAAoB,CAAA;UAG5C,IAAI,IAAI,CAACS,cAAc,CAAC8C,oBAAoB,EAAE;YAAA3E,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAC5C,MAAM2D,4CAAqB,CAAC8C,cAAc,CAAC,CAAC;UAC9C,CAAC;YAAA7H,cAAA,GAAAsB,CAAA;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAED,IAAI;YAAApB,cAAA,GAAAoB,CAAA;YACF,MAAM,IAAI,CAACoG,qBAAqB,CAAC,IAAI,CAAC3F,cAAc,CAACuC,KAAK,CAAC;UAC7D,CAAC,CAAC,OAAOiB,KAAK,EAAE;YAAArF,cAAA,GAAAoB,CAAA;YACduE,OAAO,CAACN,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAE7D;UAACrF,cAAA,GAAAoB,CAAA;UACD,IAAI,CAACqE,sBAAsB,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOJ,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAACrF,cAAA,GAAAoB,CAAA;UAC/C,MAAMiE,KAAK;QACb;MACF,CAAC;MAAA,SA1BKyC,UAAUA,CAAA;QAAA,OAAAF,WAAA,CAAA5B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAV6B,UAAU;IAAA;EAAA;IAAA1F,GAAA;IAAAC,KAAA;MAAA,IAAA0F,YAAA,OAAAxF,kBAAA,CAAAX,OAAA,EA+BhB,aAAmC;QAAA5B,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QACjC,IAAI,CAAApB,cAAA,GAAAsB,CAAA,YAAC,IAAI,CAACO,cAAc,MAAA7B,cAAA,GAAAsB,CAAA,WAAI,CAAC,IAAI,CAACO,cAAc,CAAC2C,QAAQ,GAAE;UAAAxE,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACzD;QACF,CAAC;UAAApB,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAED,IAAI;UACF,IAAM4G,aAAa,IAAAhI,cAAA,GAAAoB,CAAA,QAAG2B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACnB,cAAc,CAAC4C,UAAU;UAACzE,cAAA,GAAAoB,CAAA;UAClE,IAAI,CAACS,cAAc,CAAC6C,mBAAmB,IAAIsD,aAAa;UAAChI,cAAA,GAAAoB,CAAA;UACzD,IAAI,CAACS,cAAc,CAAC2C,QAAQ,GAAG,KAAK;UAACxE,cAAA,GAAAoB,CAAA;UACrC,IAAI,CAACS,cAAc,CAAC4C,UAAU,GAAG,CAAC;UAACzE,cAAA,GAAAoB,CAAA;UACnC,IAAI,CAACS,cAAc,CAACuC,KAAK,CAACL,MAAM,GAAG,WAAW;UAAC/D,cAAA,GAAAoB,CAAA;UAG/C,IAAI,IAAI,CAACS,cAAc,CAAC8C,oBAAoB,EAAE;YAAA3E,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAC5C,MAAM2D,4CAAqB,CAACkD,eAAe,CAAC,CAAC;UAC/C,CAAC;YAAAjI,cAAA,GAAAsB,CAAA;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAED,IAAI;YAAApB,cAAA,GAAAoB,CAAA;YACF,MAAM,IAAI,CAACoG,qBAAqB,CAAC,IAAI,CAAC3F,cAAc,CAACuC,KAAK,CAAC;UAC7D,CAAC,CAAC,OAAOiB,KAAK,EAAE;YAAArF,cAAA,GAAAoB,CAAA;YACduE,OAAO,CAACN,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;UAE7D;UAACrF,cAAA,GAAAoB,CAAA;UACD,IAAI,CAACqE,sBAAsB,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOJ,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAACrF,cAAA,GAAAoB,CAAA;UAChD,MAAMiE,KAAK;QACb;MACF,CAAC;MAAA,SA5BK6C,WAAWA,CAAA;QAAA,OAAAH,YAAA,CAAA/B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXiC,WAAW;IAAA;EAAA;IAAA9F,GAAA;IAAAC,KAAA;MAAA,IAAA8F,SAAA,OAAA5F,kBAAA,CAAAX,OAAA,EAiCjB,aAA0C;QAAA5B,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QACxC,IAAI,CAAC,IAAI,CAACS,cAAc,EAAE;UAAA7B,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACxB,MAAM,IAAIwB,KAAK,CAAC,yBAAyB,CAAC;QAC5C,CAAC;UAAA5C,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAED,IAAI;UAAApB,cAAA,GAAAoB,CAAA;UACFsB,+BAAkB,CAAClC,KAAK,CAAC,qBAAqB,CAAC;UAE/C,IAAM0D,OAAO,IAAAlE,cAAA,GAAAoB,CAAA,QAAG,IAAI,CAACS,cAAc;UACnC,IAAMuG,OAAO,IAAApI,cAAA,GAAAoB,CAAA,QAAG2B,IAAI,CAACC,GAAG,CAAC,CAAC;UAC1B,IAAMqF,aAAa,IAAArI,cAAA,GAAAoB,CAAA,QAAG,CAACgH,OAAO,GAAGlE,OAAO,CAACX,SAAS,GAAGW,OAAO,CAACQ,mBAAmB,IAAI,IAAI,GAAG,EAAE;UAAC1E,cAAA,GAAAoB,CAAA;UAG9F8C,OAAO,CAACE,KAAK,CAAC5B,QAAQ,CAAC4F,OAAO,GAAG,IAAIrF,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UAACxD,cAAA,GAAAoB,CAAA;UAC1D8C,OAAO,CAACE,KAAK,CAAC5B,QAAQ,CAAC8F,eAAe,GAAGrF,IAAI,CAACsF,KAAK,CAACF,aAAa,CAAC;UAACrI,cAAA,GAAAoB,CAAA;UACnE8C,OAAO,CAACE,KAAK,CAACL,MAAM,GAAG,WAAW;UAAC/D,cAAA,GAAAoB,CAAA;UAGnC,IAAI8C,OAAO,CAACS,oBAAoB,EAAE;YAAA3E,cAAA,GAAAsB,CAAA;YAChC,IAAMkH,WAAW,IAAAxI,cAAA,GAAAoB,CAAA,eAAS2D,4CAAqB,CAAC0D,aAAa,CAAC,CAAC;YAG/D,IAAMC,YAAY,IAAA1I,cAAA,GAAAoB,CAAA,eAASuH,oCAAiB,CAACC,WAAW,CAACJ,WAAW,CAACK,GAAG,EAAE;cACxEC,MAAM,EAAE,WAAW,CAAA9I,cAAA,GAAAsB,CAAA,WAAA4C,OAAO,CAACE,KAAK,CAACtB,EAAE,MAAA9C,cAAA,GAAAsB,CAAA,WAAI,MAAM;YAC/C,CAAC,CAAC;YAACtB,cAAA,GAAAoB,CAAA;YAEH,IAAIsH,YAAY,CAACpD,IAAI,EAAE;cAAAtF,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cACrB8C,OAAO,CAACE,KAAK,CAAC2E,QAAQ,GAAGL,YAAY,CAACpD,IAAI,CAAC0D,GAAG;cAAChJ,cAAA,GAAAoB,CAAA;cAC/C8C,OAAO,CAACE,KAAK,CAAC6E,oBAAoB,GAAGT,WAAW,CAACU,QAAQ;cAAClJ,cAAA,GAAAoB,CAAA;cAC1D8C,OAAO,CAACE,KAAK,CAAC+E,kBAAkB,GAAGT,YAAY,CAACpD,IAAI,CAAC8D,IAAI;cAACpJ,cAAA,GAAAoB,CAAA;cAG1D,IAAIoH,WAAW,CAACa,SAAS,EAAE;gBAAArJ,cAAA,GAAAsB,CAAA;gBACzB,IAAMgI,eAAe,IAAAtJ,cAAA,GAAAoB,CAAA,eAASuH,oCAAiB,CAACY,eAAe,CAC7Df,WAAW,CAACK,GAAG,EACfL,WAAW,CAACa,SAAS,EACrB;kBACEP,MAAM,EAAE,WAAW,CAAA9I,cAAA,GAAAsB,CAAA,WAAA4C,OAAO,CAACE,KAAK,CAACtB,EAAE,MAAA9C,cAAA,GAAAsB,CAAA,WAAI,MAAM;gBAC/C,CACF,CAAC;gBAACtB,cAAA,GAAAoB,CAAA;gBAEF,IAAIkI,eAAe,CAAChE,IAAI,EAAE;kBAAAtF,cAAA,GAAAsB,CAAA;kBAAAtB,cAAA,GAAAoB,CAAA;kBACxB8C,OAAO,CAACE,KAAK,CAACoF,iBAAiB,GAAGF,eAAe,CAAChE,IAAI,CAAC0D,GAAG;gBAC5D,CAAC;kBAAAhJ,cAAA,GAAAsB,CAAA;gBAAA;cACH,CAAC;gBAAAtB,cAAA,GAAAsB,CAAA;cAAA;YACH,CAAC;cAAAtB,cAAA,GAAAsB,CAAA;YAAA;UACH,CAAC;YAAAtB,cAAA,GAAAsB,CAAA;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAGD,IAAI,CAACqI,wBAAwB,CAACvF,OAAO,CAACE,KAAK,CAACR,UAAU,EAAEM,OAAO,CAACE,KAAK,CAACX,KAAK,CAAC;UAACzD,cAAA,GAAAoB,CAAA;UAG7E,MAAM,IAAI,CAACoG,qBAAqB,CAACtD,OAAO,CAACE,KAAK,CAAC;UAG/C,IAAMsF,UAAU,IAAA1J,cAAA,GAAAoB,CAAA,SAAAiC,MAAA,CAAAC,MAAA,KAAQY,OAAO,CAACE,KAAK,EAAE;UAACpE,cAAA,GAAAoB,CAAA;UAGxC,IAAI,CAACS,cAAc,GAAG,IAAI;UAAC7B,cAAA,GAAAoB,CAAA;UAC3B,IAAI,CAACqE,sBAAsB,CAAC,CAAC;UAACzF,cAAA,GAAAoB,CAAA;UAE9BsB,+BAAkB,CAAC/B,GAAG,CAAC,qBAAqB,CAAC;UAACX,cAAA,GAAAoB,CAAA;UAC9C,OAAOsI,UAAU;QACnB,CAAC,CAAC,OAAOrE,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAACrF,cAAA,GAAAoB,CAAA;UAC7C,MAAMiE,KAAK;QACb;MACF,CAAC;MAAA,SAnEKkC,QAAQA,CAAA;QAAA,OAAAY,SAAA,CAAAnC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAARsB,QAAQ;IAAA;EAAA;IAAAnF,GAAA;IAAAC,KAAA;MAAA,IAAAsH,YAAA,OAAApH,kBAAA,CAAAX,OAAA,EAwEd,aAAmC;QAAA5B,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QACjC,IAAI,CAAC,IAAI,CAACS,cAAc,EAAE;UAAA7B,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACxB;QACF,CAAC;UAAApB,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAED,IAAI;UAAApB,cAAA,GAAAoB,CAAA;UAEF,IAAI,IAAI,CAACS,cAAc,CAAC8C,oBAAoB,EAAE;YAAA3E,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAC5C,MAAM2D,4CAAqB,CAAC0D,aAAa,CAAC,CAAC;UAC7C,CAAC;YAAAzI,cAAA,GAAAsB,CAAA;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAGD,IAAI,CAACS,cAAc,CAACuC,KAAK,CAACL,MAAM,GAAG,WAAW;UAAC/D,cAAA,GAAAoB,CAAA;UAG/C,MAAM,IAAI,CAACoG,qBAAqB,CAAC,IAAI,CAAC3F,cAAc,CAACuC,KAAK,CAAC;UAACpE,cAAA,GAAAoB,CAAA;UAG5D,IAAI,CAACS,cAAc,GAAG,IAAI;UAAC7B,cAAA,GAAAoB,CAAA;UAC3B,IAAI,CAACqE,sBAAsB,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOJ,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAACrF,cAAA,GAAAoB,CAAA;UAChD,MAAMiE,KAAK;QACb;MACF,CAAC;MAAA,SAxBKuE,WAAWA,CAAA;QAAA,OAAAD,YAAA,CAAA3D,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAX2D,WAAW;IAAA;EAAA;IAAAxH,GAAA;IAAAC,KAAA,EA6BjB,SAAAwH,iBAAiBA,CAAA,EAAwB;MAAA7J,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACvC,OAAO,IAAI,CAACS,cAAc;IAC5B;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAKD,SAAAyH,kBAAkBA,CAACC,QAAgD,EAAQ;MAAA/J,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACzE,IAAI,CAACU,gBAAgB,CAACkI,IAAI,CAACD,QAAQ,CAAC;IACtC;EAAC;IAAA3H,GAAA;IAAAC,KAAA,EAKD,SAAA4H,qBAAqBA,CAACF,QAAgD,EAAQ;MAAA/J,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC5E,IAAI,CAACU,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACoI,MAAM,CAAC,UAAAC,CAAC,EAAI;QAAAnK,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAA+I,CAAC,KAAKJ,QAAQ;MAAD,CAAC,CAAC;IAC3E;EAAC;IAAA3H,GAAA;IAAAC,KAAA,EAKD,SAAA+H,gBAAgBA,CAACL,QAAqC,EAAQ;MAAA/J,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC5D,IAAI,CAACW,cAAc,CAACiI,IAAI,CAACD,QAAQ,CAAC;IACpC;EAAC;IAAA3H,GAAA;IAAAC,KAAA,EAKD,SAAAgI,mBAAmBA,CAACN,QAAqC,EAAQ;MAAA/J,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC/D,IAAI,CAACW,cAAc,GAAG,IAAI,CAACA,cAAc,CAACmI,MAAM,CAAC,UAAAC,CAAC,EAAI;QAAAnK,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAA+I,CAAC,KAAKJ,QAAQ;MAAD,CAAC,CAAC;IACvE;EAAC;IAAA3H,GAAA;IAAAC,KAAA,EAID,SAAQM,qBAAqBA,CAACH,QAAuB,EAAQ;MAAA,IAAA8H,qBAAA;MAAAtK,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC3D,IAAI,GAAAkJ,qBAAA,GAAC9H,QAAQ,CAAC+H,YAAY,aAArBD,qBAAA,CAAuBE,IAAI,CAAC,CAAC,GAAE;QAAAxK,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAClC,MAAM,IAAIwB,KAAK,CAAC,2BAA2B,CAAC;MAC9C,CAAC;QAAA5C,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACD,IAAI,CAACoB,QAAQ,CAACsB,MAAM,EAAE;QAAA9D,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACpB,MAAM,IAAIwB,KAAK,CAAC,qBAAqB,CAAC;MACxC,CAAC;QAAA5C,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACD,IAAI,CAACoB,QAAQ,CAACiI,SAAS,EAAE;QAAAzK,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACvB,MAAM,IAAIwB,KAAK,CAAC,wBAAwB,CAAC;MAC3C,CAAC;QAAA5C,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACD,IAAI,CAACoB,QAAQ,CAACmB,WAAW,EAAE;QAAA3D,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACzB,MAAM,IAAIwB,KAAK,CAAC,0BAA0B,CAAC;MAC7C,CAAC;QAAA5C,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACD,IAAI,CAACoB,QAAQ,CAACkI,OAAO,EAAE;QAAA1K,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACrB,MAAM,IAAIwB,KAAK,CAAC,2BAA2B,CAAC;MAC9C,CAAC;QAAA5C,cAAA,GAAAsB,CAAA;MAAA;IACH;EAAC;IAAAc,GAAA;IAAAC,KAAA,EAED,SAAQqB,eAAeA,CAACiH,MAAc,EAAc;MAAA3K,cAAA,GAAAqB,CAAA;MAClD,IAAMuJ,OAAO,IAAA5K,cAAA,GAAAoB,CAAA,SAAGuJ,MAAM,KAAK,WAAW,IAAA3K,cAAA,GAAAsB,CAAA,WAAG,CAAC,KAAAtB,cAAA,GAAAsB,CAAA,WAAG,CAAC;MAACtB,cAAA,GAAAoB,CAAA;MAC/C,OAAO;QACL8F,IAAI,EAAE,EAAE;QACR2D,UAAU,EAAE,EAAE;QACdC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE;MACZ,CAAC;IACH;EAAC;IAAA5I,GAAA;IAAAC,KAAA,EAED,SAAQwB,oBAAoBA,CAACC,MAAc,EAAmB;MAAA9D,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC5D,OAAO;QACL6J,OAAO,EAAE,EAAE;QACXnH,MAAM,EAANA,MAAM;QACNoH,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,oBAAoB,EAAE,CAAC;QACvBC,mBAAmB,EAAE,CAAC;QACtBC,oBAAoB,EAAE,CAAC;QACvBC,yBAAyB,EAAE,CAAC;QAC5BC,0BAA0B,EAAE,CAAC;QAC7BC,oBAAoB,EAAE,CAAC;QACvBC,gBAAgB,EAAE,CAAC;QACnBC,OAAO,EAAE,CAAC;QACVC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE,CAAC;QACjBC,iBAAiB,EAAE,CAAC;QACpBC,kBAAkB,EAAE,CAAC;QACrBC,YAAY,EAAE,CAAC;QACfC,eAAe,EAAE,CAAC;QAClBC,eAAe,EAAE,CAAC;QAClBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;MAClB,CAAC;IACH;EAAC;IAAAlK,GAAA;IAAAC,KAAA,EAED,SAAQyE,WAAWA,CACjByF,YAAwB,EACxBC,SAAiB,EACjBC,UAAkB,EAClBtG,MAA2B,EAC3BuG,KAAgB,EACJ;MAAA1M,cAAA,GAAAqB,CAAA;MAGZ,IAAMwF,YAAY,IAAA7G,cAAA,GAAAoB,CAAA,SAAAiC,MAAA,CAAAC,MAAA,KAAQiJ,YAAY,EAAE;MAACvM,cAAA,GAAAoB,CAAA;MAGzC,OAAOyF,YAAY,CAACK,IAAI,CAACb,MAAM,GAAGmG,SAAS,EAAE;QAAAxM,cAAA,GAAAoB,CAAA;QAC3CyF,YAAY,CAACK,IAAI,CAAC8C,IAAI,CAAC;UACrBwC,SAAS,EAAE3F,YAAY,CAACK,IAAI,CAACb,MAAM,GAAG,CAAC;UACvCsG,SAAS,EAAE,CAAC;UACZC,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE,KAAK;UACjBC,WAAW,EAAE;QACf,CAAC,CAAC;MACJ;MAEA,IAAMzI,UAAU,IAAArE,cAAA,GAAAoB,CAAA,SAAGyF,YAAY,CAACK,IAAI,CAACsF,SAAS,GAAG,CAAC,CAAC;MAACxM,cAAA,GAAAoB,CAAA;MAGpD,IAAI+E,MAAM,KAAK,MAAM,EAAE;QAAAnG,cAAA,GAAAsB,CAAA;MAGvB,CAAC,MAAM;QAAAtB,cAAA,GAAAsB,CAAA;MAEP;MAACtB,cAAA,GAAAoB,CAAA;MAED,OAAOyF,YAAY;IACrB;EAAC;IAAAzE,GAAA;IAAAC,KAAA,EAED,SAAQ0E,gBAAgBA,CAACnD,UAA2B,EAAE8I,KAAgB,EAAQ;MAAA1M,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC5EwC,UAAU,CAACoI,iBAAiB,EAAE;MAAChM,cAAA,GAAAoB,CAAA;MAE/B,IAAIsL,KAAK,CAAC/F,MAAM,KAAK,MAAM,EAAE;QAAA3G,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC3BwC,UAAU,CAACmI,cAAc,EAAE;MAC7B,CAAC;QAAA/L,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,QAAQsL,KAAK,CAACtG,SAAS;QACrB,KAAK,KAAK;UAAApG,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACRwC,UAAU,CAACsH,IAAI,EAAE;UAAClL,cAAA,GAAAoB,CAAA;UAClB;QACF,KAAK,cAAc;UAAApB,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACjBwC,UAAU,CAACuH,YAAY,EAAE;UAACnL,cAAA,GAAAoB,CAAA;UAC1B;QACF,KAAK,QAAQ;UAAApB,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACXwC,UAAU,CAACgI,OAAO,EAAE;UAAC5L,cAAA,GAAAoB,CAAA;UACrB;QACF,KAAK,gBAAgB;UAAApB,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACnBwC,UAAU,CAACiI,cAAc,EAAE;UAAC7L,cAAA,GAAAoB,CAAA;UAC5B;QACF,KAAK,cAAc;UAAApB,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACjBwC,UAAU,CAACkI,YAAY,EAAE;UAAC9L,cAAA,GAAAoB,CAAA;UAC1B;MACJ;IACF;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAKD,SAAQoH,wBAAwBA,CAAC7F,UAA2B,EAAEH,KAAiB,EAAQ;MAAA,IAAAsJ,oBAAA;MAAA/M,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAErF,IAAIwC,UAAU,CAACyH,oBAAoB,GAAG,CAAC,EAAE;QAAArL,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACvCwC,UAAU,CAACoJ,oBAAoB,GAAG/J,IAAI,CAACsF,KAAK,CACzC3E,UAAU,CAACwH,aAAa,GAAGxH,UAAU,CAACyH,oBAAoB,GAAI,GACjE,CAAC;MACH,CAAC;QAAArL,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,IAAIwC,UAAU,CAAC+H,gBAAgB,GAAG,CAAC,EAAE;QAAA3L,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACnCwC,UAAU,CAACqJ,gBAAgB,GAAGrJ,UAAU,CAAC+H,gBAAgB,GAAG/H,UAAU,CAAC8H,oBAAoB;MAC7F,CAAC;QAAA1L,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAGD,IAAIwC,UAAU,CAACoI,iBAAiB,GAAG,CAAC,EAAE;QAAAhM,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACpCwC,UAAU,CAACsJ,kBAAkB,GAAGjK,IAAI,CAACsF,KAAK,CACvC3E,UAAU,CAACmI,cAAc,GAAGnI,UAAU,CAACoI,iBAAiB,GAAI,GAC/D,CAAC;MACH,CAAC;QAAAhM,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAGDwC,UAAU,CAACqH,OAAO,GAAG,CAAAjL,cAAA,GAAAsB,CAAA,YAAAyL,oBAAA,OAAI,CAAClL,cAAc,qBAAnBkL,oBAAA,CAAqB3I,KAAK,CAACtB,EAAE,MAAA9C,cAAA,GAAAsB,CAAA,WAAI,EAAE;IAC1D;EAAC;IAAAc,GAAA;IAAAC,KAAA,EAKD,SAAQ4E,aAAaA,CAACkG,GAAa,EAAW;MAAAnN,cAAA,GAAAqB,CAAA;MAC5C,IAAMsL,SAAS,IAAA3M,cAAA,GAAAoB,CAAA,SAAG+L,GAAG,CAACR,SAAS;MAC/B,IAAMC,aAAa,IAAA5M,cAAA,GAAAoB,CAAA,SAAG+L,GAAG,CAACP,aAAa;MAAC5M,cAAA,GAAAoB,CAAA;MAGxC,IAAI,CAAApB,cAAA,GAAAsB,CAAA,WAAAqL,SAAS,IAAI,CAAC,MAAA3M,cAAA,GAAAsB,CAAA,WAAIqL,SAAS,GAAGC,aAAa,IAAI,CAAC,GAAE;QAAA5M,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACpD,OAAO,IAAI;MACb,CAAC;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACD,IAAI,CAAApB,cAAA,GAAAsB,CAAA,WAAAsL,aAAa,IAAI,CAAC,MAAA5M,cAAA,GAAAsB,CAAA,WAAIsL,aAAa,GAAGD,SAAS,IAAI,CAAC,GAAE;QAAA3M,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACxD,OAAO,IAAI;MACb,CAAC;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MACD,IAAK,CAAApB,cAAA,GAAAsB,CAAA,WAAAqL,SAAS,KAAK,CAAC,MAAA3M,cAAA,GAAAsB,CAAA,WAAIsL,aAAa,KAAK,CAAC,KAAM,CAAA5M,cAAA,GAAAsB,CAAA,WAAAsL,aAAa,KAAK,CAAC,MAAA5M,cAAA,GAAAsB,CAAA,WAAIqL,SAAS,KAAK,CAAC,CAAC,EAAE;QAAA3M,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACxF,OAAO,IAAI;MACb,CAAC;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAO,KAAK;IACd;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAKD,SAAQiF,cAAcA,CAAC6F,GAAa,EAAEV,UAAkB,EAAW;MAAAzM,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAGjE,OAAO,KAAK;IACd;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAKD,SAAQ+E,eAAeA,CAAC3D,KAAiB,EAAEkH,MAAc,EAAW;MAAA3K,cAAA,GAAAqB,CAAA;MAClE,IAAM+L,SAAS,IAAApN,cAAA,GAAAoB,CAAA,SAAGuJ,MAAM,KAAK,WAAW,IAAA3K,cAAA,GAAAsB,CAAA,WAAG,CAAC,KAAAtB,cAAA,GAAAsB,CAAA,WAAG,CAAC;MAACtB,cAAA,GAAAoB,CAAA;MACjD,OAAO,CAAApB,cAAA,GAAAsB,CAAA,WAAAmC,KAAK,CAACsH,OAAO,IAAIqC,SAAS,MAAApN,cAAA,GAAAsB,CAAA,WAAImC,KAAK,CAACuH,QAAQ,IAAIoC,SAAS;IAClE;EAAC;IAAAhL,GAAA;IAAAC,KAAA;MAAA,IAAAgL,oBAAA,OAAA9K,kBAAA,CAAAX,OAAA,EAID,WAAkCwC,KAAqB,EAA6D;QAAApE,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAClH,IAAI;UAEF,IAAMkM,SAAS,IAAAtN,cAAA,GAAAoB,CAAA,SAAG;YAChB0B,EAAE,EAAEsB,KAAK,CAACtB,EAAE;YACZyK,OAAO,EAAEnJ,KAAK,CAAC5B,QAAQ,CAACsB,MAAM;YAC9B0J,aAAa,EAAEpJ,KAAK,CAAC5B,QAAQ,CAAC+H,YAAY;YAC1CkD,UAAU,EAAE,CAAAzN,cAAA,GAAAsB,CAAA,WAAA8C,KAAK,CAAC5B,QAAQ,CAACiI,SAAS,MAAAzK,cAAA,GAAAsB,CAAA,WAAI,UAAU;YAClDoM,YAAY,EAAEtJ,KAAK,CAAC5B,QAAQ,CAACmB,WAAW;YACxC+G,OAAO,EAAEtG,KAAK,CAAC5B,QAAQ,CAACkI,OAAO;YAC/BiD,QAAQ,EAAEvJ,KAAK,CAAC5B,QAAQ,CAACmL,QAAQ;YACjCC,UAAU,EAAExJ,KAAK,CAAC5B,QAAQ,CAACqL,SAAS;YACpCC,kBAAkB,EAAE1J,KAAK,CAAC5B,QAAQ,CAACuL,OAAO;YAC1CC,WAAW,EAAE5J,KAAK,CAAC5B,QAAQ,CAACwL,WAAW;YACvCC,UAAU,EAAE,IAAIlL,IAAI,CAACqB,KAAK,CAAC5B,QAAQ,CAACe,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC0K,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC1EC,UAAU,EAAE,IAAIpL,IAAI,CAACqB,KAAK,CAAC5B,QAAQ,CAACe,SAAS,CAAC,CAAC6K,YAAY,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3EnK,MAAM,EAAEK,KAAK,CAACL,MAAM;YACpBsK,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACnK,KAAK,CAACX,KAAK,CAAC;YAC1CG,UAAU,EAAE0K,IAAI,CAACC,SAAS,CAACnK,KAAK,CAACR,UAAU,CAAC;YAC5C4K,UAAU,EAAEpK,KAAK,CAACJ,SAAS;YAC3ByK,UAAU,EAAErK,KAAK,CAACH;UACpB,CAAC;UAGD,IAAIyK,QAAQ,IAAA1O,cAAA,GAAAoB,CAAA,SAAG,CAAC;UAChB,IAAMuN,WAAW,IAAA3O,cAAA,GAAAoB,CAAA,SAAG,CAAC;UAACpB,cAAA,GAAAoB,CAAA;UAEtB,OAAOsN,QAAQ,GAAGC,WAAW,EAAE;YAAA3O,cAAA,GAAAoB,CAAA;YAC7B,IAAI;cAAA,IAAAwN,YAAA;cACF,IAAM9D,MAAM,IAAA9K,cAAA,GAAAoB,CAAA,eAASyN,gCAAe,CAACC,WAAW,CAACxB,SAAS,CAAC;cAACtN,cAAA,GAAAoB,CAAA;cAE5D,IAAI0J,MAAM,CAACzF,KAAK,EAAE;gBAAArF,cAAA,GAAAsB,CAAA;gBAAAtB,cAAA,GAAAoB,CAAA;gBAChB,IAAIsN,QAAQ,KAAKC,WAAW,GAAG,CAAC,EAAE;kBAAA3O,cAAA,GAAAsB,CAAA;kBAAAtB,cAAA,GAAAoB,CAAA;kBAChC,OAAO;oBAAEgE,OAAO,EAAE,KAAK;oBAAEC,KAAK,EAAEyF,MAAM,CAACzF;kBAAM,CAAC;gBAChD,CAAC;kBAAArF,cAAA,GAAAsB,CAAA;gBAAA;gBAAAtB,cAAA,GAAAoB,CAAA;gBACDsN,QAAQ,EAAE;gBAAC1O,cAAA,GAAAoB,CAAA;gBACX,MAAM,IAAI2N,OAAO,CAAC,UAAAC,OAAO,EAAI;kBAAAhP,cAAA,GAAAqB,CAAA;kBAAArB,cAAA,GAAAoB,CAAA;kBAAA,OAAA6N,UAAU,CAACD,OAAO,EAAE,IAAI,GAAGN,QAAQ,CAAC;gBAAD,CAAC,CAAC;gBAAC1O,cAAA,GAAAoB,CAAA;gBACnE;cACF,CAAC;gBAAApB,cAAA,GAAAsB,CAAA;cAAA;cAAAtB,cAAA,GAAAoB,CAAA;cAED,OAAO;gBAAEgE,OAAO,EAAE,IAAI;gBAAEE,IAAI,EAAE;kBAAExC,EAAE,EAAEsB,KAAK,CAACtB,EAAE;kBAAEyC,UAAU,GAAAqJ,YAAA,GAAE9D,MAAM,CAACxF,IAAI,qBAAXsJ,YAAA,CAAa9L;gBAAG;cAAE,CAAC;YAC/E,CAAC,CAAC,OAAOuC,KAAK,EAAE;cAAArF,cAAA,GAAAoB,CAAA;cACdsN,QAAQ,EAAE;cAAC1O,cAAA,GAAAoB,CAAA;cACX,IAAIsN,QAAQ,KAAKC,WAAW,EAAE;gBAAA3O,cAAA,GAAAsB,CAAA;gBAAAtB,cAAA,GAAAoB,CAAA;gBAC5B,MAAMiE,KAAK;cACb,CAAC;gBAAArF,cAAA,GAAAsB,CAAA;cAAA;cAAAtB,cAAA,GAAAoB,CAAA;cACD,MAAM,IAAI2N,OAAO,CAAC,UAAAC,OAAO,EAAI;gBAAAhP,cAAA,GAAAqB,CAAA;gBAAArB,cAAA,GAAAoB,CAAA;gBAAA,OAAA6N,UAAU,CAACD,OAAO,EAAE,IAAI,GAAGN,QAAQ,CAAC;cAAD,CAAC,CAAC;YACpE;UACF;UAAC1O,cAAA,GAAAoB,CAAA;UAED,OAAO;YAAEgE,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAyC,CAAC;QAC5E,CAAC,CAAC,OAAOA,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UAACrF,cAAA,GAAAoB,CAAA;UACxD,OAAO;YAAEgE,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAE;UAA6B,CAAC;QAChE;MACF,CAAC;MAAA,SAvDaF,mBAAmBA,CAAA+J,GAAA;QAAA,OAAA7B,oBAAA,CAAArH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBd,mBAAmB;IAAA;EAAA;IAAA/C,GAAA;IAAAC,KAAA;MAAA,IAAA8M,sBAAA,OAAA5M,kBAAA,CAAAX,OAAA,EAyDjC,WAAoCwC,KAAqB,EAA2B;QAAApE,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAClF,IAAI;UAAApB,cAAA,GAAAoB,CAAA;UACF,IAAI,CAACgD,KAAK,CAACtB,EAAE,EAAE;YAAA9C,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACb,MAAM,IAAIwB,KAAK,CAAC,iCAAiC,CAAC;UACpD,CAAC;YAAA5C,cAAA,GAAAsB,CAAA;UAAA;UAED,IAAM8N,UAAU,IAAApP,cAAA,GAAAoB,CAAA,SAAG;YACjBiN,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACnK,KAAK,CAACX,KAAK,CAAC;YAC1CG,UAAU,EAAE0K,IAAI,CAACC,SAAS,CAACnK,KAAK,CAACR,UAAU,CAAC;YAC5CG,MAAM,EAAEK,KAAK,CAACL,MAAM;YACpB0K,UAAU,EAAE,IAAI1L,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UACrC,CAAC;UAACxD,cAAA,GAAAoB,CAAA;UAGF,IAAI,CAAApB,cAAA,GAAAsB,CAAA,WAAA8C,KAAK,CAACL,MAAM,KAAK,WAAW,MAAA/D,cAAA,GAAAsB,CAAA,WAAI8C,KAAK,CAAC5B,QAAQ,CAAC4F,OAAO,GAAE;YAAApI,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAC1DgO,UAAU,CAACC,QAAQ,GAAG,IAAItM,IAAI,CAACqB,KAAK,CAAC5B,QAAQ,CAAC4F,OAAO,CAAC,CAACgG,YAAY,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAAClO,cAAA,GAAAoB,CAAA;YACpFgO,UAAU,CAACE,gBAAgB,GAAGrM,IAAI,CAACsF,KAAK,CACtC,CAAC,IAAIxF,IAAI,CAACqB,KAAK,CAAC5B,QAAQ,CAAC4F,OAAO,CAAC,CAACmH,OAAO,CAAC,CAAC,GAAG,IAAIxM,IAAI,CAACqB,KAAK,CAAC5B,QAAQ,CAACe,SAAS,CAAC,CAACgM,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAC1G,CAAC;YAACvP,cAAA,GAAAoB,CAAA;YACFgO,UAAU,CAACI,WAAW,GAAG,IAAI,CAACC,wBAAwB,CAACrL,KAAK,CAACX,KAAK,CAAC;YAACzD,cAAA,GAAAoB,CAAA;YACpEgO,UAAU,CAACtE,MAAM,GAAG,IAAI,CAAC4E,oBAAoB,CAACtL,KAAK,CAACX,KAAK,EAAEW,KAAK,CAAC5B,QAAQ,CAACsB,MAAM,CAAC;YAAC9D,cAAA,GAAAoB,CAAA;YAClFgO,UAAU,CAACO,QAAQ,GAAGvL,KAAK,CAACX,KAAK,CAACsH,OAAO;YAAC/K,cAAA,GAAAoB,CAAA;YAC1CgO,UAAU,CAACQ,SAAS,GAAGxL,KAAK,CAACX,KAAK,CAACuH,QAAQ;UAC7C,CAAC;YAAAhL,cAAA,GAAAsB,CAAA;UAAA;UAED,IAAMwJ,MAAM,IAAA9K,cAAA,GAAAoB,CAAA,eAASyN,gCAAe,CAACgB,WAAW,CAACzL,KAAK,CAACtB,EAAE,EAAEsM,UAAU,CAAC;UAACpP,cAAA,GAAAoB,CAAA;UAEvE,IAAI,CAAApB,cAAA,GAAAsB,CAAA,WAAAwJ,MAAM,MAAA9K,cAAA,GAAAsB,CAAA,WAAIwJ,MAAM,CAACzF,KAAK,GAAE;YAAArF,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAC1B,MAAM,IAAIwB,KAAK,CAACkI,MAAM,CAACzF,KAAK,CAAC;UAC/B,CAAC;YAAArF,cAAA,GAAAsB,CAAA;UAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAGD,OAAAiC,MAAA,CAAAC,MAAA,KACKc,KAAK;YACRH,SAAS,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UAAC;QAEvC,CAAC,CAAC,OAAO6B,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UAACrF,cAAA,GAAAoB,CAAA;UAC1D,MAAM,IAAIwB,KAAK,CAAC,4BAA4B,CAAC;QAC/C;MACF,CAAC;MAAA,SAxCa4E,qBAAqBA,CAAAsI,GAAA;QAAA,OAAAX,sBAAA,CAAAnJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBuB,qBAAqB;IAAA;EAAA;IAAApF,GAAA;IAAAC,KAAA,EA6CnC,SAAQoN,wBAAwBA,CAAChM,KAAiB,EAAU;MAAAzD,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC1D,IAAI,CAAApB,cAAA,GAAAsB,CAAA,YAACmC,KAAK,CAACyD,IAAI,MAAAlH,cAAA,GAAAsB,CAAA,WAAImC,KAAK,CAACyD,IAAI,CAACb,MAAM,KAAK,CAAC,GAAE;QAAArG,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAC1C,OAAO,KAAK;MACd,CAAC;QAAApB,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAED,OAAOqC,KAAK,CAACyD,IAAI,CACd6I,GAAG,CAAC,UAAA5C,GAAG,EAAI;QAAAnN,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,UAAG+L,GAAG,CAACR,SAAS,IAAIQ,GAAG,CAACP,aAAa,EAAE;MAAD,CAAC,CAAC,CACnDoD,IAAI,CAAC,IAAI,CAAC;IACf;EAAC;IAAA5N,GAAA;IAAAC,KAAA,EAKD,SAAQqN,oBAAoBA,CAACjM,KAAiB,EAAEK,MAAc,EAA2B;MAAA9D,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACvF,IAAIqC,KAAK,CAACsH,OAAO,GAAGtH,KAAK,CAACuH,QAAQ,EAAE;QAAAhL,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAClC,OAAO,KAAK;MACd,CAAC,MAAM;QAAApB,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAAA,IAAIqC,KAAK,CAACuH,QAAQ,GAAGvH,KAAK,CAACsH,OAAO,EAAE;UAAA/K,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACzC,OAAO,MAAM;QACf,CAAC;UAAApB,cAAA,GAAAsB,CAAA;QAAA;MAAD;MAACtB,cAAA,GAAAoB,CAAA;MACD,OAAO,MAAM;IACf;EAAC;IAAAgB,GAAA;IAAAC,KAAA,EAED,SAAQ8B,iBAAiBA,CAAA,EAAW;MAAAnE,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAClC,OAAO,WAAW2B,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3E;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAED,SAAQoE,eAAeA,CAAA,EAAW;MAAAzG,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAChC,OAAO,SAAS2B,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACzE;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAED,SAAQoD,sBAAsBA,CAAA,EAAS;MAAA,IAAAwK,KAAA;MAAAjQ,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACrC,IAAI,CAACU,gBAAgB,CAACoO,OAAO,CAAC,UAAAnG,QAAQ,EAAI;QAAA/J,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAAA,OAAA2I,QAAQ,CAACkG,KAAI,CAACpO,cAAc,CAAC;MAAD,CAAC,CAAC;IAC1E;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAED,SAAQoF,oBAAoBA,CAAA,EAAS;MAAA,IAAA0I,MAAA;MAAAnQ,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MACnC,IAAI,IAAI,CAACS,cAAc,EAAE;QAAA7B,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACvB,IAAI,CAACW,cAAc,CAACmO,OAAO,CAAC,UAAAnG,QAAQ,EAAI;UAAA/J,cAAA,GAAAqB,CAAA;UAAArB,cAAA,GAAAoB,CAAA;UAAA,OAAA2I,QAAQ,CAACoG,MAAI,CAACtO,cAAc,CAAEuC,KAAK,CAACX,KAAK,CAAC;QAAD,CAAC,CAAC;MACrF,CAAC;QAAAzD,cAAA,GAAAsB,CAAA;MAAA;IACH;EAAC;IAAAc,GAAA;IAAAC,KAAA,EAKD,SAAQmD,gBAAgBA,CAACyF,OAAe,EAAQ;MAAAjL,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAC9C,IAAI;QAAApB,cAAA,GAAAoB,CAAA;QAEF,IAAI,CAAC,IAAI,CAACY,gBAAgB,EAAE;UAAAhC,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UAC1B,IAAI,CAACY,gBAAgB,GAAG,IAAIoO,GAAG,CAAC,CAAC;QACnC,CAAC;UAAApQ,cAAA,GAAAsB,CAAA;QAAA;QAAAtB,cAAA,GAAAoB,CAAA;QAGD,IAAI,CAACY,gBAAgB,CAACmL,GAAG,CAAClC,OAAO,EAAE,EAAE,CAAC;QAACjL,cAAA,GAAAoB,CAAA;QAGvC,IAAI,CAACiP,gBAAgB,CAACpF,OAAO,CAAC;MAChC,CAAC,CAAC,OAAO5F,KAAK,EAAE;QAAArF,cAAA,GAAAoB,CAAA;QACduE,OAAO,CAACN,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF;EAAC;IAAAjD,GAAA;IAAAC,KAAA,EAKD,SAAQgO,gBAAgBA,CAACpF,OAAe,EAAQ;MAAA,IAAAqF,MAAA;MAAAtQ,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAE9C,IAAI,IAAI,CAACa,YAAY,EAAE;QAAAjC,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACrBmP,aAAa,CAAC,IAAI,CAACtO,YAAY,CAAC;MAClC,CAAC;QAAAjC,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAGD,IAAI,CAACa,YAAY,GAAGuO,WAAW,KAAAjO,kBAAA,CAAAX,OAAA,EAAC,aAAY;QAAA5B,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAC1C,MAAMkP,MAAI,CAACG,eAAe,CAACxF,OAAO,CAAC;MACrC,CAAC,GAAE,KAAK,CAAC;IACX;EAAC;IAAA7I,GAAA;IAAAC,KAAA;MAAA,IAAAqO,gBAAA,OAAAnO,kBAAA,CAAAX,OAAA,EAKD,WAA8BqJ,OAAe,EAAiB;QAAAjL,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAC5D,IAAI;UAAA,IAAAuP,qBAAA,EAAAC,sBAAA;UACF,IAAMC,KAAK,IAAA7Q,cAAA,GAAAoB,CAAA,UAAAuP,qBAAA,GAAG,IAAI,CAAC3O,gBAAgB,qBAArB2O,qBAAA,CAAuBG,GAAG,CAAC7F,OAAO,CAAC;UAACjL,cAAA,GAAAoB,CAAA;UAClD,IAAI,CAAApB,cAAA,GAAAsB,CAAA,YAACuP,KAAK,MAAA7Q,cAAA,GAAAsB,CAAA,WAAIuP,KAAK,CAACxK,MAAM,KAAK,CAAC,GAAE;YAAArG,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAChC;UACF,CAAC;YAAApB,cAAA,GAAAsB,CAAA;UAAA;UAGD,IAAMyP,OAAO,IAAA/Q,cAAA,GAAAoB,CAAA,aAAA4P,mBAAA,CAAApP,OAAA,EAAOiP,KAAK,EAAC;UAAC7Q,cAAA,GAAAoB,CAAA;UAC3B,CAAAwP,sBAAA,OAAI,CAAC5O,gBAAgB,aAArB4O,sBAAA,CAAuBzD,GAAG,CAAClC,OAAO,EAAE,EAAE,CAAC;UAACjL,cAAA,GAAAoB,CAAA;UAExC,KAAK,IAAM6P,MAAM,IAAIF,OAAO,EAAE;YAAA/Q,cAAA,GAAAoB,CAAA;YAC5B,IAAI;cAAApB,cAAA,GAAAoB,CAAA;cACF,MAAM,IAAI,CAAC8P,oBAAoB,CAACD,MAAM,CAAC;YACzC,CAAC,CAAC,OAAO5L,KAAK,EAAE;cAAA,IAAA8L,sBAAA;cAAAnR,cAAA,GAAAoB,CAAA;cACduE,OAAO,CAACN,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;cAACrF,cAAA,GAAAoB,CAAA;cAE/C,CAAA+P,sBAAA,OAAI,CAACnP,gBAAgB,cAAAmP,sBAAA,GAArBA,sBAAA,CAAuBL,GAAG,CAAC7F,OAAO,CAAC,aAAnCkG,sBAAA,CAAqCnH,IAAI,CAACiH,MAAM,CAAC;YACnD;UACF;QACF,CAAC,CAAC,OAAO5L,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;MACF,CAAC;MAAA,SAvBaoL,eAAeA,CAAAW,GAAA;QAAA,OAAAV,gBAAA,CAAA1K,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfwK,eAAe;IAAA;EAAA;IAAArO,GAAA;IAAAC,KAAA;MAAA,IAAAgP,qBAAA,OAAA9O,kBAAA,CAAAX,OAAA,EA4B7B,WAAmCqP,MAAW,EAAiB;QAAAjR,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAC7D,QAAQ6P,MAAM,CAAChQ,IAAI;UACjB,KAAK,cAAc;YAAAjB,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACjB,MAAM,IAAI,CAACoG,qBAAqB,CAACyJ,MAAM,CAAC3L,IAAI,CAAC;YAACtF,cAAA,GAAAoB,CAAA;YAC9C;UACF,KAAK,cAAc;YAAApB,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAEjB;UACF,KAAK,mBAAmB;YAAApB,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAEtB;UACF;YAAApB,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YACEuE,OAAO,CAAC2L,IAAI,CAAC,sBAAsB,EAAEL,MAAM,CAAChQ,IAAI,CAAC;QACrD;MACF,CAAC;MAAA,SAdaiQ,oBAAoBA,CAAAK,GAAA;QAAA,OAAAF,qBAAA,CAAArL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBiL,oBAAoB;IAAA;EAAA;IAAA9O,GAAA;IAAAC,KAAA,EAmBlC,SAAQqD,aAAaA,CAAA,EAAS;MAAA,IAAA8L,MAAA;MAAAxR,cAAA,GAAAqB,CAAA;MAAArB,cAAA,GAAAoB,CAAA;MAE5B,IAAI,IAAI,CAACc,gBAAgB,EAAE;QAAAlC,cAAA,GAAAsB,CAAA;QAAAtB,cAAA,GAAAoB,CAAA;QACzBmP,aAAa,CAAC,IAAI,CAACrO,gBAAgB,CAAC;MACtC,CAAC;QAAAlC,cAAA,GAAAsB,CAAA;MAAA;MAAAtB,cAAA,GAAAoB,CAAA;MAGD,IAAI,CAACc,gBAAgB,GAAGsO,WAAW,KAAAjO,kBAAA,CAAAX,OAAA,EAAC,aAAY;QAAA5B,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAC9C,IAAIoQ,MAAI,CAAC3P,cAAc,EAAE;UAAA7B,cAAA,GAAAsB,CAAA;UAAAtB,cAAA,GAAAoB,CAAA;UACvB,IAAI;YAAApB,cAAA,GAAAoB,CAAA;YACF,MAAMoQ,MAAI,CAAChK,qBAAqB,CAACgK,MAAI,CAAC3P,cAAc,CAACuC,KAAK,CAAC;UAC7D,CAAC,CAAC,OAAOiB,KAAK,EAAE;YAAArF,cAAA,GAAAoB,CAAA;YACduE,OAAO,CAACN,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;UAC3C;QACF,CAAC;UAAArF,cAAA,GAAAsB,CAAA;QAAA;MACH,CAAC,GAAE,MAAM,CAAC;IACZ;EAAC;IAAAc,GAAA;IAAAC,KAAA;MAAA,IAAAoP,qBAAA,OAAAlP,kBAAA,CAAAX,OAAA,EAKD,aAAoD;QAAA5B,cAAA,GAAAqB,CAAA;QAAArB,cAAA,GAAAoB,CAAA;QAClD,IAAI;UAAApB,cAAA,GAAAoB,CAAA;UACF,IAAI,IAAI,CAACS,cAAc,EAAE;YAAA7B,cAAA,GAAAsB,CAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAEvB,IAAI,IAAI,CAACS,cAAc,CAAC8C,oBAAoB,EAAE;cAAA3E,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cAC5C,MAAM2D,4CAAqB,CAAC0D,aAAa,CAAC,CAAC;YAC7C,CAAC;cAAAzI,cAAA,GAAAsB,CAAA;YAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAGD,IAAI,IAAI,CAACc,gBAAgB,EAAE;cAAAlC,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cACzBmP,aAAa,CAAC,IAAI,CAACrO,gBAAgB,CAAC;cAAClC,cAAA,GAAAoB,CAAA;cACrC,IAAI,CAACc,gBAAgB,GAAG,IAAI;YAC9B,CAAC;cAAAlC,cAAA,GAAAsB,CAAA;YAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAED,IAAI,IAAI,CAACa,YAAY,EAAE;cAAAjC,cAAA,GAAAsB,CAAA;cAAAtB,cAAA,GAAAoB,CAAA;cACrBmP,aAAa,CAAC,IAAI,CAACtO,YAAY,CAAC;cAACjC,cAAA,GAAAoB,CAAA;cACjC,IAAI,CAACa,YAAY,GAAG,IAAI;YAC1B,CAAC;cAAAjC,cAAA,GAAAsB,CAAA;YAAA;YAAAtB,cAAA,GAAAoB,CAAA;YAGD,IAAI,CAACS,cAAc,GAAG,IAAI;UAC5B,CAAC;YAAA7B,cAAA,GAAAsB,CAAA;UAAA;QACH,CAAC,CAAC,OAAO+D,KAAK,EAAE;UAAArF,cAAA,GAAAoB,CAAA;UACduE,OAAO,CAACN,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC;MAAA,SAzBaO,oBAAoBA,CAAA;QAAA,OAAA6L,qBAAA,CAAAzL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBL,oBAAoB;IAAA;EAAA;AAAA;AA6B7B,IAAM8L,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,IAAA1R,cAAA,GAAAoB,CAAA,SAAG,IAAIM,qBAAqB,CAAC,CAAC", "ignoreList": []}