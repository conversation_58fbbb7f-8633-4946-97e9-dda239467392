f7371b5e0e508591101b41622860a405
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
function getQuery() {
  return _canUseDom.default && window.matchMedia != null ? window.matchMedia('(prefers-color-scheme: dark)') : null;
}
var query = getQuery();
var listenerMapping = new WeakMap();
var Appearance = {
  getColorScheme: function getColorScheme() {
    return query && query.matches ? 'dark' : 'light';
  },
  addChangeListener: function addChangeListener(listener) {
    var mappedListener = listenerMapping.get(listener);
    if (!mappedListener) {
      mappedListener = function mappedListener(_ref) {
        var matches = _ref.matches;
        listener({
          colorScheme: matches ? 'dark' : 'light'
        });
      };
      listenerMapping.set(listener, mappedListener);
    }
    if (query) {
      query.addListener(mappedListener);
    }
    function remove() {
      var mappedListener = listenerMapping.get(listener);
      if (query && mappedListener) {
        query.removeListener(mappedListener);
      }
      listenerMapping.delete(listener);
    }
    return {
      remove: remove
    };
  }
};
var _default = exports.default = Appearance;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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