{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_Animation", "_SpringConfig", "_invariant", "_NativeAnimatedHelper", "_AnimatedColor", "SpringAnimation", "_Animation$default", "config", "_this", "_config$overshootClam", "_config$restDisplacem", "_config$restSpeedThre", "_config$velocity", "_config$velocity2", "_config$delay", "_config$isInteraction", "_config$iterations", "_overshootClamping", "overshootClamping", "_restDisplacementThreshold", "restDisplacementThreshold", "_restSpeedThreshold", "restSpeedThreshold", "_initialVelocity", "velocity", "_lastVelocity", "_toValue", "toValue", "_delay", "delay", "_useNativeDriver", "shouldUseNativeDriver", "_platformConfig", "platformConfig", "__isInteraction", "isInteraction", "__iterations", "iterations", "stiffness", "undefined", "damping", "mass", "_config$stiffness", "_config$damping", "_config$mass", "bounciness", "speed", "tension", "friction", "_stiffness", "_damping", "_mass", "_config$bounciness", "_config$speed", "springConfig", "fromBouncinessAndSpeed", "_config$tension", "_config$friction", "_springConfig", "fromOrigamiTensionAndFriction", "key", "value", "__getNativeAnimationConfig", "_this$_initialVelocit", "type", "initialVelocity", "start", "fromValue", "onUpdate", "onEnd", "previousAnimation", "animatedValue", "_this2", "__active", "_startPosition", "_lastPosition", "_onUpdate", "__onEnd", "_lastTime", "Date", "now", "_frameTime", "internalState", "getInternalState", "lastPosition", "lastVelocity", "lastTime", "__startNativeAnimation", "_timeout", "setTimeout", "MAX_STEPS", "deltaTime", "c", "m", "k", "v0", "zeta", "Math", "sqrt", "omega0", "omega1", "x0", "position", "envelope", "exp", "sin", "cos", "_envelope", "isOvershooting", "isVelocity", "abs", "isDisplacement", "__debouncedOnEnd", "finished", "_animationFrame", "requestAnimationFrame", "bind", "stop", "clearTimeout", "global", "cancelAnimationFrame", "_default", "module"], "sources": ["SpringAnimation.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _Animation = _interopRequireDefault(require(\"./Animation\"));\nvar _SpringConfig = _interopRequireDefault(require(\"../SpringConfig\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _NativeAnimatedHelper = require(\"../NativeAnimatedHelper\");\nvar _AnimatedColor = _interopRequireDefault(require(\"../nodes/AnimatedColor\"));\nclass SpringAnimation extends _Animation.default {\n  constructor(config) {\n    var _config$overshootClam, _config$restDisplacem, _config$restSpeedThre, _config$velocity, _config$velocity2, _config$delay, _config$isInteraction, _config$iterations;\n    super();\n    this._overshootClamping = (_config$overshootClam = config.overshootClamping) !== null && _config$overshootClam !== void 0 ? _config$overshootClam : false;\n    this._restDisplacementThreshold = (_config$restDisplacem = config.restDisplacementThreshold) !== null && _config$restDisplacem !== void 0 ? _config$restDisplacem : 0.001;\n    this._restSpeedThreshold = (_config$restSpeedThre = config.restSpeedThreshold) !== null && _config$restSpeedThre !== void 0 ? _config$restSpeedThre : 0.001;\n    this._initialVelocity = (_config$velocity = config.velocity) !== null && _config$velocity !== void 0 ? _config$velocity : 0;\n    this._lastVelocity = (_config$velocity2 = config.velocity) !== null && _config$velocity2 !== void 0 ? _config$velocity2 : 0;\n    this._toValue = config.toValue;\n    this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;\n    this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);\n    this._platformConfig = config.platformConfig;\n    this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !this._useNativeDriver;\n    this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;\n    if (config.stiffness !== undefined || config.damping !== undefined || config.mass !== undefined) {\n      var _config$stiffness, _config$damping, _config$mass;\n      (0, _invariant.default)(config.bounciness === undefined && config.speed === undefined && config.tension === undefined && config.friction === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');\n      this._stiffness = (_config$stiffness = config.stiffness) !== null && _config$stiffness !== void 0 ? _config$stiffness : 100;\n      this._damping = (_config$damping = config.damping) !== null && _config$damping !== void 0 ? _config$damping : 10;\n      this._mass = (_config$mass = config.mass) !== null && _config$mass !== void 0 ? _config$mass : 1;\n    } else if (config.bounciness !== undefined || config.speed !== undefined) {\n      var _config$bounciness, _config$speed;\n      // Convert the origami bounciness/speed values to stiffness/damping\n      // We assume mass is 1.\n      (0, _invariant.default)(config.tension === undefined && config.friction === undefined && config.stiffness === undefined && config.damping === undefined && config.mass === undefined, 'You can define one of bounciness/speed, tension/friction, or stiffness/damping/mass, but not more than one');\n      var springConfig = _SpringConfig.default.fromBouncinessAndSpeed((_config$bounciness = config.bounciness) !== null && _config$bounciness !== void 0 ? _config$bounciness : 8, (_config$speed = config.speed) !== null && _config$speed !== void 0 ? _config$speed : 12);\n      this._stiffness = springConfig.stiffness;\n      this._damping = springConfig.damping;\n      this._mass = 1;\n    } else {\n      var _config$tension, _config$friction;\n      // Convert the origami tension/friction values to stiffness/damping\n      // We assume mass is 1.\n      var _springConfig = _SpringConfig.default.fromOrigamiTensionAndFriction((_config$tension = config.tension) !== null && _config$tension !== void 0 ? _config$tension : 40, (_config$friction = config.friction) !== null && _config$friction !== void 0 ? _config$friction : 7);\n      this._stiffness = _springConfig.stiffness;\n      this._damping = _springConfig.damping;\n      this._mass = 1;\n    }\n    (0, _invariant.default)(this._stiffness > 0, 'Stiffness value must be greater than 0');\n    (0, _invariant.default)(this._damping > 0, 'Damping value must be greater than 0');\n    (0, _invariant.default)(this._mass > 0, 'Mass value must be greater than 0');\n  }\n  __getNativeAnimationConfig() {\n    var _this$_initialVelocit;\n    return {\n      type: 'spring',\n      overshootClamping: this._overshootClamping,\n      restDisplacementThreshold: this._restDisplacementThreshold,\n      restSpeedThreshold: this._restSpeedThreshold,\n      stiffness: this._stiffness,\n      damping: this._damping,\n      mass: this._mass,\n      initialVelocity: (_this$_initialVelocit = this._initialVelocity) !== null && _this$_initialVelocit !== void 0 ? _this$_initialVelocit : this._lastVelocity,\n      toValue: this._toValue,\n      iterations: this.__iterations,\n      platformConfig: this._platformConfig\n    };\n  }\n  start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {\n    this.__active = true;\n    this._startPosition = fromValue;\n    this._lastPosition = this._startPosition;\n    this._onUpdate = onUpdate;\n    this.__onEnd = onEnd;\n    this._lastTime = Date.now();\n    this._frameTime = 0.0;\n    if (previousAnimation instanceof SpringAnimation) {\n      var internalState = previousAnimation.getInternalState();\n      this._lastPosition = internalState.lastPosition;\n      this._lastVelocity = internalState.lastVelocity;\n      // Set the initial velocity to the last velocity\n      this._initialVelocity = this._lastVelocity;\n      this._lastTime = internalState.lastTime;\n    }\n    var start = () => {\n      if (this._useNativeDriver) {\n        this.__startNativeAnimation(animatedValue);\n      } else {\n        this.onUpdate();\n      }\n    };\n\n    //  If this._delay is more than 0, we start after the timeout.\n    if (this._delay) {\n      this._timeout = setTimeout(start, this._delay);\n    } else {\n      start();\n    }\n  }\n  getInternalState() {\n    return {\n      lastPosition: this._lastPosition,\n      lastVelocity: this._lastVelocity,\n      lastTime: this._lastTime\n    };\n  }\n\n  /**\n   * This spring model is based off of a damped harmonic oscillator\n   * (https://en.wikipedia.org/wiki/Harmonic_oscillator#Damped_harmonic_oscillator).\n   *\n   * We use the closed form of the second order differential equation:\n   *\n   * x'' + (2ζ⍵_0)x' + ⍵^2x = 0\n   *\n   * where\n   *    ⍵_0 = √(k / m) (undamped angular frequency of the oscillator),\n   *    ζ = c / 2√mk (damping ratio),\n   *    c = damping constant\n   *    k = stiffness\n   *    m = mass\n   *\n   * The derivation of the closed form is described in detail here:\n   * http://planetmath.org/sites/default/files/texpdf/39745.pdf\n   *\n   * This algorithm happens to match the algorithm used by CASpringAnimation,\n   * a QuartzCore (iOS) API that creates spring animations.\n   */\n  onUpdate() {\n    // If for some reason we lost a lot of frames (e.g. process large payload or\n    // stopped in the debugger), we only advance by 4 frames worth of\n    // computation and will continue on the next frame. It's better to have it\n    // running at faster speed than jumping to the end.\n    var MAX_STEPS = 64;\n    var now = Date.now();\n    if (now > this._lastTime + MAX_STEPS) {\n      now = this._lastTime + MAX_STEPS;\n    }\n    var deltaTime = (now - this._lastTime) / 1000;\n    this._frameTime += deltaTime;\n    var c = this._damping;\n    var m = this._mass;\n    var k = this._stiffness;\n    var v0 = -this._initialVelocity;\n    var zeta = c / (2 * Math.sqrt(k * m)); // damping ratio\n    var omega0 = Math.sqrt(k / m); // undamped angular frequency of the oscillator (rad/ms)\n    var omega1 = omega0 * Math.sqrt(1.0 - zeta * zeta); // exponential decay\n    var x0 = this._toValue - this._startPosition; // calculate the oscillation from x0 = 1 to x = 0\n\n    var position = 0.0;\n    var velocity = 0.0;\n    var t = this._frameTime;\n    if (zeta < 1) {\n      // Under damped\n      var envelope = Math.exp(-zeta * omega0 * t);\n      position = this._toValue - envelope * ((v0 + zeta * omega0 * x0) / omega1 * Math.sin(omega1 * t) + x0 * Math.cos(omega1 * t));\n      // This looks crazy -- it's actually just the derivative of the\n      // oscillation function\n      velocity = zeta * omega0 * envelope * (Math.sin(omega1 * t) * (v0 + zeta * omega0 * x0) / omega1 + x0 * Math.cos(omega1 * t)) - envelope * (Math.cos(omega1 * t) * (v0 + zeta * omega0 * x0) - omega1 * x0 * Math.sin(omega1 * t));\n    } else {\n      // Critically damped\n      var _envelope = Math.exp(-omega0 * t);\n      position = this._toValue - _envelope * (x0 + (v0 + omega0 * x0) * t);\n      velocity = _envelope * (v0 * (t * omega0 - 1) + t * x0 * (omega0 * omega0));\n    }\n    this._lastTime = now;\n    this._lastPosition = position;\n    this._lastVelocity = velocity;\n    this._onUpdate(position);\n    if (!this.__active) {\n      // a listener might have stopped us in _onUpdate\n      return;\n    }\n\n    // Conditions for stopping the spring animation\n    var isOvershooting = false;\n    if (this._overshootClamping && this._stiffness !== 0) {\n      if (this._startPosition < this._toValue) {\n        isOvershooting = position > this._toValue;\n      } else {\n        isOvershooting = position < this._toValue;\n      }\n    }\n    var isVelocity = Math.abs(velocity) <= this._restSpeedThreshold;\n    var isDisplacement = true;\n    if (this._stiffness !== 0) {\n      isDisplacement = Math.abs(this._toValue - position) <= this._restDisplacementThreshold;\n    }\n    if (isOvershooting || isVelocity && isDisplacement) {\n      if (this._stiffness !== 0) {\n        // Ensure that we end up with a round value\n        this._lastPosition = this._toValue;\n        this._lastVelocity = 0;\n        this._onUpdate(this._toValue);\n      }\n      this.__debouncedOnEnd({\n        finished: true\n      });\n      return;\n    }\n    // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n    this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));\n  }\n  stop() {\n    super.stop();\n    this.__active = false;\n    clearTimeout(this._timeout);\n    global.cancelAnimationFrame(this._animationFrame);\n    this.__debouncedOnEnd({\n      finished: false\n    });\n  }\n}\nvar _default = exports.default = SpringAnimation;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,UAAU,GAAGH,sBAAsB,CAACxB,OAAO,cAAc,CAAC,CAAC;AAC/D,IAAI4B,aAAa,GAAGJ,sBAAsB,CAACxB,OAAO,kBAAkB,CAAC,CAAC;AACtE,IAAI6B,UAAU,GAAGL,sBAAsB,CAACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAI8B,qBAAqB,GAAG9B,OAAO,0BAA0B,CAAC;AAC9D,IAAI+B,cAAc,GAAGP,sBAAsB,CAACxB,OAAO,yBAAyB,CAAC,CAAC;AAAC,IACzEgC,eAAe,aAAAC,kBAAA;EACnB,SAAAD,gBAAYE,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAlC,gBAAA,CAAAU,OAAA,QAAAqB,eAAA;IAClB,IAAII,qBAAqB,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,aAAa,EAAEC,qBAAqB,EAAEC,kBAAkB;IACtKR,KAAA,GAAA5B,UAAA,OAAAyB,eAAA;IACAG,KAAA,CAAKS,kBAAkB,GAAG,CAACR,qBAAqB,GAAGF,MAAM,CAACW,iBAAiB,MAAM,IAAI,IAAIT,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,KAAK;IACzJD,KAAA,CAAKW,0BAA0B,GAAG,CAACT,qBAAqB,GAAGH,MAAM,CAACa,yBAAyB,MAAM,IAAI,IAAIV,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,KAAK;IACzKF,KAAA,CAAKa,mBAAmB,GAAG,CAACV,qBAAqB,GAAGJ,MAAM,CAACe,kBAAkB,MAAM,IAAI,IAAIX,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,KAAK;IAC3JH,KAAA,CAAKe,gBAAgB,GAAG,CAACX,gBAAgB,GAAGL,MAAM,CAACiB,QAAQ,MAAM,IAAI,IAAIZ,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,CAAC;IAC3HJ,KAAA,CAAKiB,aAAa,GAAG,CAACZ,iBAAiB,GAAGN,MAAM,CAACiB,QAAQ,MAAM,IAAI,IAAIX,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,CAAC;IAC3HL,KAAA,CAAKkB,QAAQ,GAAGnB,MAAM,CAACoB,OAAO;IAC9BnB,KAAA,CAAKoB,MAAM,GAAG,CAACd,aAAa,GAAGP,MAAM,CAACsB,KAAK,MAAM,IAAI,IAAIf,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,CAAC;IACrGN,KAAA,CAAKsB,gBAAgB,GAAG,CAAC,CAAC,EAAE3B,qBAAqB,CAAC4B,qBAAqB,EAAExB,MAAM,CAAC;IAChFC,KAAA,CAAKwB,eAAe,GAAGzB,MAAM,CAAC0B,cAAc;IAC5CzB,KAAA,CAAK0B,eAAe,GAAG,CAACnB,qBAAqB,GAAGR,MAAM,CAAC4B,aAAa,MAAM,IAAI,IAAIpB,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,CAACP,KAAA,CAAKsB,gBAAgB;IACnKtB,KAAA,CAAK4B,YAAY,GAAG,CAACpB,kBAAkB,GAAGT,MAAM,CAAC8B,UAAU,MAAM,IAAI,IAAIrB,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC;IAC/H,IAAIT,MAAM,CAAC+B,SAAS,KAAKC,SAAS,IAAIhC,MAAM,CAACiC,OAAO,KAAKD,SAAS,IAAIhC,MAAM,CAACkC,IAAI,KAAKF,SAAS,EAAE;MAC/F,IAAIG,iBAAiB,EAAEC,eAAe,EAAEC,YAAY;MACpD,CAAC,CAAC,EAAE1C,UAAU,CAAClB,OAAO,EAAEuB,MAAM,CAACsC,UAAU,KAAKN,SAAS,IAAIhC,MAAM,CAACuC,KAAK,KAAKP,SAAS,IAAIhC,MAAM,CAACwC,OAAO,KAAKR,SAAS,IAAIhC,MAAM,CAACyC,QAAQ,KAAKT,SAAS,EAAE,4GAA4G,CAAC;MACrQ/B,KAAA,CAAKyC,UAAU,GAAG,CAACP,iBAAiB,GAAGnC,MAAM,CAAC+B,SAAS,MAAM,IAAI,IAAII,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,GAAG;MAC3HlC,KAAA,CAAK0C,QAAQ,GAAG,CAACP,eAAe,GAAGpC,MAAM,CAACiC,OAAO,MAAM,IAAI,IAAIG,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,EAAE;MAChHnC,KAAA,CAAK2C,KAAK,GAAG,CAACP,YAAY,GAAGrC,MAAM,CAACkC,IAAI,MAAM,IAAI,IAAIG,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC;IAClG,CAAC,MAAM,IAAIrC,MAAM,CAACsC,UAAU,KAAKN,SAAS,IAAIhC,MAAM,CAACuC,KAAK,KAAKP,SAAS,EAAE;MACxE,IAAIa,kBAAkB,EAAEC,aAAa;MAGrC,CAAC,CAAC,EAAEnD,UAAU,CAAClB,OAAO,EAAEuB,MAAM,CAACwC,OAAO,KAAKR,SAAS,IAAIhC,MAAM,CAACyC,QAAQ,KAAKT,SAAS,IAAIhC,MAAM,CAAC+B,SAAS,KAAKC,SAAS,IAAIhC,MAAM,CAACiC,OAAO,KAAKD,SAAS,IAAIhC,MAAM,CAACkC,IAAI,KAAKF,SAAS,EAAE,4GAA4G,CAAC;MACnS,IAAIe,YAAY,GAAGrD,aAAa,CAACjB,OAAO,CAACuE,sBAAsB,CAAC,CAACH,kBAAkB,GAAG7C,MAAM,CAACsC,UAAU,MAAM,IAAI,IAAIO,kBAAkB,KAAK,KAAK,CAAC,GAAGA,kBAAkB,GAAG,CAAC,EAAE,CAACC,aAAa,GAAG9C,MAAM,CAACuC,KAAK,MAAM,IAAI,IAAIO,aAAa,KAAK,KAAK,CAAC,GAAGA,aAAa,GAAG,EAAE,CAAC;MACtQ7C,KAAA,CAAKyC,UAAU,GAAGK,YAAY,CAAChB,SAAS;MACxC9B,KAAA,CAAK0C,QAAQ,GAAGI,YAAY,CAACd,OAAO;MACpChC,KAAA,CAAK2C,KAAK,GAAG,CAAC;IAChB,CAAC,MAAM;MACL,IAAIK,eAAe,EAAEC,gBAAgB;MAGrC,IAAIC,aAAa,GAAGzD,aAAa,CAACjB,OAAO,CAAC2E,6BAA6B,CAAC,CAACH,eAAe,GAAGjD,MAAM,CAACwC,OAAO,MAAM,IAAI,IAAIS,eAAe,KAAK,KAAK,CAAC,GAAGA,eAAe,GAAG,EAAE,EAAE,CAACC,gBAAgB,GAAGlD,MAAM,CAACyC,QAAQ,MAAM,IAAI,IAAIS,gBAAgB,KAAK,KAAK,CAAC,GAAGA,gBAAgB,GAAG,CAAC,CAAC;MAC9QjD,KAAA,CAAKyC,UAAU,GAAGS,aAAa,CAACpB,SAAS;MACzC9B,KAAA,CAAK0C,QAAQ,GAAGQ,aAAa,CAAClB,OAAO;MACrChC,KAAA,CAAK2C,KAAK,GAAG,CAAC;IAChB;IACA,CAAC,CAAC,EAAEjD,UAAU,CAAClB,OAAO,EAAEwB,KAAA,CAAKyC,UAAU,GAAG,CAAC,EAAE,wCAAwC,CAAC;IACtF,CAAC,CAAC,EAAE/C,UAAU,CAAClB,OAAO,EAAEwB,KAAA,CAAK0C,QAAQ,GAAG,CAAC,EAAE,sCAAsC,CAAC;IAClF,CAAC,CAAC,EAAEhD,UAAU,CAAClB,OAAO,EAAEwB,KAAA,CAAK2C,KAAK,GAAG,CAAC,EAAE,mCAAmC,CAAC;IAAC,OAAA3C,KAAA;EAC/E;EAAC,IAAA7B,UAAA,CAAAK,OAAA,EAAAqB,eAAA,EAAAC,kBAAA;EAAA,WAAA/B,aAAA,CAAAS,OAAA,EAAAqB,eAAA;IAAAuD,GAAA;IAAAC,KAAA,EACD,SAAAC,0BAA0BA,CAAA,EAAG;MAC3B,IAAIC,qBAAqB;MACzB,OAAO;QACLC,IAAI,EAAE,QAAQ;QACd9C,iBAAiB,EAAE,IAAI,CAACD,kBAAkB;QAC1CG,yBAAyB,EAAE,IAAI,CAACD,0BAA0B;QAC1DG,kBAAkB,EAAE,IAAI,CAACD,mBAAmB;QAC5CiB,SAAS,EAAE,IAAI,CAACW,UAAU;QAC1BT,OAAO,EAAE,IAAI,CAACU,QAAQ;QACtBT,IAAI,EAAE,IAAI,CAACU,KAAK;QAChBc,eAAe,EAAE,CAACF,qBAAqB,GAAG,IAAI,CAACxC,gBAAgB,MAAM,IAAI,IAAIwC,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAG,IAAI,CAACtC,aAAa;QAC1JE,OAAO,EAAE,IAAI,CAACD,QAAQ;QACtBW,UAAU,EAAE,IAAI,CAACD,YAAY;QAC7BH,cAAc,EAAE,IAAI,CAACD;MACvB,CAAC;IACH;EAAC;IAAA4B,GAAA;IAAAC,KAAA,EACD,SAAAK,KAAKA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,aAAa,EAAE;MAAA,IAAAC,MAAA;MAClE,IAAI,CAACC,QAAQ,GAAG,IAAI;MACpB,IAAI,CAACC,cAAc,GAAGP,SAAS;MAC/B,IAAI,CAACQ,aAAa,GAAG,IAAI,CAACD,cAAc;MACxC,IAAI,CAACE,SAAS,GAAGR,QAAQ;MACzB,IAAI,CAACS,OAAO,GAAGR,KAAK;MACpB,IAAI,CAACS,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAC3B,IAAI,CAACC,UAAU,GAAG,GAAG;MACrB,IAAIX,iBAAiB,YAAYjE,eAAe,EAAE;QAChD,IAAI6E,aAAa,GAAGZ,iBAAiB,CAACa,gBAAgB,CAAC,CAAC;QACxD,IAAI,CAACR,aAAa,GAAGO,aAAa,CAACE,YAAY;QAC/C,IAAI,CAAC3D,aAAa,GAAGyD,aAAa,CAACG,YAAY;QAE/C,IAAI,CAAC9D,gBAAgB,GAAG,IAAI,CAACE,aAAa;QAC1C,IAAI,CAACqD,SAAS,GAAGI,aAAa,CAACI,QAAQ;MACzC;MACA,IAAIpB,KAAK,GAAG,SAARA,KAAKA,CAAA,EAAS;QAChB,IAAIM,MAAI,CAAC1C,gBAAgB,EAAE;UACzB0C,MAAI,CAACe,sBAAsB,CAAChB,aAAa,CAAC;QAC5C,CAAC,MAAM;UACLC,MAAI,CAACJ,QAAQ,CAAC,CAAC;QACjB;MACF,CAAC;MAGD,IAAI,IAAI,CAACxC,MAAM,EAAE;QACf,IAAI,CAAC4D,QAAQ,GAAGC,UAAU,CAACvB,KAAK,EAAE,IAAI,CAACtC,MAAM,CAAC;MAChD,CAAC,MAAM;QACLsC,KAAK,CAAC,CAAC;MACT;IACF;EAAC;IAAAN,GAAA;IAAAC,KAAA,EACD,SAAAsB,gBAAgBA,CAAA,EAAG;MACjB,OAAO;QACLC,YAAY,EAAE,IAAI,CAACT,aAAa;QAChCU,YAAY,EAAE,IAAI,CAAC5D,aAAa;QAChC6D,QAAQ,EAAE,IAAI,CAACR;MACjB,CAAC;IACH;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAuBD,SAAAO,QAAQA,CAAA,EAAG;MAKT,IAAIsB,SAAS,GAAG,EAAE;MAClB,IAAIV,GAAG,GAAGD,IAAI,CAACC,GAAG,CAAC,CAAC;MACpB,IAAIA,GAAG,GAAG,IAAI,CAACF,SAAS,GAAGY,SAAS,EAAE;QACpCV,GAAG,GAAG,IAAI,CAACF,SAAS,GAAGY,SAAS;MAClC;MACA,IAAIC,SAAS,GAAG,CAACX,GAAG,GAAG,IAAI,CAACF,SAAS,IAAI,IAAI;MAC7C,IAAI,CAACG,UAAU,IAAIU,SAAS;MAC5B,IAAIC,CAAC,GAAG,IAAI,CAAC1C,QAAQ;MACrB,IAAI2C,CAAC,GAAG,IAAI,CAAC1C,KAAK;MAClB,IAAI2C,CAAC,GAAG,IAAI,CAAC7C,UAAU;MACvB,IAAI8C,EAAE,GAAG,CAAC,IAAI,CAACxE,gBAAgB;MAC/B,IAAIyE,IAAI,GAAGJ,CAAC,IAAI,CAAC,GAAGK,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGD,CAAC,CAAC,CAAC;MACrC,IAAIM,MAAM,GAAGF,IAAI,CAACC,IAAI,CAACJ,CAAC,GAAGD,CAAC,CAAC;MAC7B,IAAIO,MAAM,GAAGD,MAAM,GAAGF,IAAI,CAACC,IAAI,CAAC,GAAG,GAAGF,IAAI,GAAGA,IAAI,CAAC;MAClD,IAAIK,EAAE,GAAG,IAAI,CAAC3E,QAAQ,GAAG,IAAI,CAACgD,cAAc;MAE5C,IAAI4B,QAAQ,GAAG,GAAG;MAClB,IAAI9E,QAAQ,GAAG,GAAG;MAClB,IAAI3C,CAAC,GAAG,IAAI,CAACoG,UAAU;MACvB,IAAIe,IAAI,GAAG,CAAC,EAAE;QAEZ,IAAIO,QAAQ,GAAGN,IAAI,CAACO,GAAG,CAAC,CAACR,IAAI,GAAGG,MAAM,GAAGtH,CAAC,CAAC;QAC3CyH,QAAQ,GAAG,IAAI,CAAC5E,QAAQ,GAAG6E,QAAQ,IAAI,CAACR,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,IAAID,MAAM,GAAGH,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAGvH,CAAC,CAAC,GAAGwH,EAAE,GAAGJ,IAAI,CAACS,GAAG,CAACN,MAAM,GAAGvH,CAAC,CAAC,CAAC;QAG7H2C,QAAQ,GAAGwE,IAAI,GAAGG,MAAM,GAAGI,QAAQ,IAAIN,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAGvH,CAAC,CAAC,IAAIkH,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,CAAC,GAAGD,MAAM,GAAGC,EAAE,GAAGJ,IAAI,CAACS,GAAG,CAACN,MAAM,GAAGvH,CAAC,CAAC,CAAC,GAAG0H,QAAQ,IAAIN,IAAI,CAACS,GAAG,CAACN,MAAM,GAAGvH,CAAC,CAAC,IAAIkH,EAAE,GAAGC,IAAI,GAAGG,MAAM,GAAGE,EAAE,CAAC,GAAGD,MAAM,GAAGC,EAAE,GAAGJ,IAAI,CAACQ,GAAG,CAACL,MAAM,GAAGvH,CAAC,CAAC,CAAC;MACpO,CAAC,MAAM;QAEL,IAAI8H,SAAS,GAAGV,IAAI,CAACO,GAAG,CAAC,CAACL,MAAM,GAAGtH,CAAC,CAAC;QACrCyH,QAAQ,GAAG,IAAI,CAAC5E,QAAQ,GAAGiF,SAAS,IAAIN,EAAE,GAAG,CAACN,EAAE,GAAGI,MAAM,GAAGE,EAAE,IAAIxH,CAAC,CAAC;QACpE2C,QAAQ,GAAGmF,SAAS,IAAIZ,EAAE,IAAIlH,CAAC,GAAGsH,MAAM,GAAG,CAAC,CAAC,GAAGtH,CAAC,GAAGwH,EAAE,IAAIF,MAAM,GAAGA,MAAM,CAAC,CAAC;MAC7E;MACA,IAAI,CAACrB,SAAS,GAAGE,GAAG;MACpB,IAAI,CAACL,aAAa,GAAG2B,QAAQ;MAC7B,IAAI,CAAC7E,aAAa,GAAGD,QAAQ;MAC7B,IAAI,CAACoD,SAAS,CAAC0B,QAAQ,CAAC;MACxB,IAAI,CAAC,IAAI,CAAC7B,QAAQ,EAAE;QAElB;MACF;MAGA,IAAImC,cAAc,GAAG,KAAK;MAC1B,IAAI,IAAI,CAAC3F,kBAAkB,IAAI,IAAI,CAACgC,UAAU,KAAK,CAAC,EAAE;QACpD,IAAI,IAAI,CAACyB,cAAc,GAAG,IAAI,CAAChD,QAAQ,EAAE;UACvCkF,cAAc,GAAGN,QAAQ,GAAG,IAAI,CAAC5E,QAAQ;QAC3C,CAAC,MAAM;UACLkF,cAAc,GAAGN,QAAQ,GAAG,IAAI,CAAC5E,QAAQ;QAC3C;MACF;MACA,IAAImF,UAAU,GAAGZ,IAAI,CAACa,GAAG,CAACtF,QAAQ,CAAC,IAAI,IAAI,CAACH,mBAAmB;MAC/D,IAAI0F,cAAc,GAAG,IAAI;MACzB,IAAI,IAAI,CAAC9D,UAAU,KAAK,CAAC,EAAE;QACzB8D,cAAc,GAAGd,IAAI,CAACa,GAAG,CAAC,IAAI,CAACpF,QAAQ,GAAG4E,QAAQ,CAAC,IAAI,IAAI,CAACnF,0BAA0B;MACxF;MACA,IAAIyF,cAAc,IAAIC,UAAU,IAAIE,cAAc,EAAE;QAClD,IAAI,IAAI,CAAC9D,UAAU,KAAK,CAAC,EAAE;UAEzB,IAAI,CAAC0B,aAAa,GAAG,IAAI,CAACjD,QAAQ;UAClC,IAAI,CAACD,aAAa,GAAG,CAAC;UACtB,IAAI,CAACmD,SAAS,CAAC,IAAI,CAAClD,QAAQ,CAAC;QAC/B;QACA,IAAI,CAACsF,gBAAgB,CAAC;UACpBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;MACF;MAEA,IAAI,CAACC,eAAe,GAAGC,qBAAqB,CAAC,IAAI,CAAC/C,QAAQ,CAACgD,IAAI,CAAC,IAAI,CAAC,CAAC;IACxE;EAAC;IAAAxD,GAAA;IAAAC,KAAA,EACD,SAAAwD,IAAIA,CAAA,EAAG;MACL3H,aAAA,CAAAW,eAAA;MACA,IAAI,CAACoE,QAAQ,GAAG,KAAK;MACrB6C,YAAY,CAAC,IAAI,CAAC9B,QAAQ,CAAC;MAC3B+B,MAAM,CAACC,oBAAoB,CAAC,IAAI,CAACN,eAAe,CAAC;MACjD,IAAI,CAACF,gBAAgB,CAAC;QACpBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EAAC;AAAA,EA1M2BjH,UAAU,CAAChB,OAAO;AA4MhD,IAAIyI,QAAQ,GAAG3H,OAAO,CAACd,OAAO,GAAGqB,eAAe;AAChDqH,MAAM,CAAC5H,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}