aeb3f796e448c318b01867af322bb4fb
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_5hqetizdh() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\privacy.ts";
  var hash = "ccf4760dd6ba939a9b142ff3bfb663e2722e439c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\privacy.ts",
    statementMap: {
      "0": {
        start: {
          line: 53,
          column: 61
        },
        end: {
          line: 66,
          column: 3
        }
      },
      "1": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 105,
          column: 5
        }
      },
      "2": {
        start: {
          line: 73,
          column: 30
        },
        end: {
          line: 77,
          column: 17
        }
      },
      "3": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 81,
          column: 7
        }
      },
      "4": {
        start: {
          line: 80,
          column: 8
        },
        end: {
          line: 80,
          column: 20
        }
      },
      "5": {
        start: {
          line: 83,
          column: 6
        },
        end: {
          line: 86,
          column: 7
        }
      },
      "6": {
        start: {
          line: 85,
          column: 8
        },
        end: {
          line: 85,
          column: 63
        }
      },
      "7": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 101,
          column: 8
        }
      },
      "8": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 62
        }
      },
      "9": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 41
        }
      },
      "10": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 152,
          column: 5
        }
      },
      "11": {
        start: {
          line: 116,
          column: 30
        },
        end: {
          line: 116,
          column: 67
        }
      },
      "12": {
        start: {
          line: 117,
          column: 30
        },
        end: {
          line: 117,
          column: 65
        }
      },
      "13": {
        start: {
          line: 119,
          column: 30
        },
        end: {
          line: 138,
          column: 17
        }
      },
      "14": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 29
        }
      },
      "15": {
        start: {
          line: 140,
          column: 17
        },
        end: {
          line: 140,
          column: 29
        }
      },
      "16": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 146,
          column: 9
        }
      },
      "17": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 29
        }
      },
      "18": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 65
        }
      },
      "19": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 151,
          column: 59
        }
      },
      "20": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 203,
          column: 5
        }
      },
      "21": {
        start: {
          line: 161,
          column: 40
        },
        end: {
          line: 166,
          column: 17
        }
      },
      "22": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 170,
          column: 7
        }
      },
      "23": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 63
        }
      },
      "24": {
        start: {
          line: 172,
          column: 41
        },
        end: {
          line: 176,
          column: 7
        }
      },
      "25": {
        start: {
          line: 178,
          column: 30
        },
        end: {
          line: 186,
          column: 17
        }
      },
      "26": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 29
        }
      },
      "27": {
        start: {
          line: 188,
          column: 17
        },
        end: {
          line: 188,
          column: 29
        }
      },
      "28": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 191,
          column: 46
        }
      },
      "29": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 197,
          column: 9
        }
      },
      "30": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 199,
          column: 21
        }
      },
      "31": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 61
        }
      },
      "32": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 202,
          column: 55
        }
      },
      "33": {
        start: {
          line: 214,
          column: 4
        },
        end: {
          line: 267,
          column: 5
        }
      },
      "34": {
        start: {
          line: 216,
          column: 40
        },
        end: {
          line: 221,
          column: 17
        }
      },
      "35": {
        start: {
          line: 223,
          column: 6
        },
        end: {
          line: 225,
          column: 7
        }
      },
      "36": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 224,
          column: 65
        }
      },
      "37": {
        start: {
          line: 228,
          column: 28
        },
        end: {
          line: 228,
          column: 38
        }
      },
      "38": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 229,
          column: 58
        }
      },
      "39": {
        start: {
          line: 231,
          column: 43
        },
        end: {
          line: 238,
          column: 7
        }
      },
      "40": {
        start: {
          line: 240,
          column: 30
        },
        end: {
          line: 251,
          column: 17
        }
      },
      "41": {
        start: {
          line: 253,
          column: 6
        },
        end: {
          line: 253,
          column: 29
        }
      },
      "42": {
        start: {
          line: 253,
          column: 17
        },
        end: {
          line: 253,
          column: 29
        }
      },
      "43": {
        start: {
          line: 256,
          column: 6
        },
        end: {
          line: 261,
          column: 9
        }
      },
      "44": {
        start: {
          line: 263,
          column: 6
        },
        end: {
          line: 263,
          column: 21
        }
      },
      "45": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 63
        }
      },
      "46": {
        start: {
          line: 266,
          column: 6
        },
        end: {
          line: 266,
          column: 57
        }
      },
      "47": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 293,
          column: 5
        }
      },
      "48": {
        start: {
          line: 275,
          column: 24
        },
        end: {
          line: 282,
          column: 48
        }
      },
      "49": {
        start: {
          line: 284,
          column: 6
        },
        end: {
          line: 284,
          column: 29
        }
      },
      "50": {
        start: {
          line: 284,
          column: 17
        },
        end: {
          line: 284,
          column: 29
        }
      },
      "51": {
        start: {
          line: 287,
          column: 6
        },
        end: {
          line: 289,
          column: 9
        }
      },
      "52": {
        start: {
          line: 291,
          column: 6
        },
        end: {
          line: 291,
          column: 62
        }
      },
      "53": {
        start: {
          line: 292,
          column: 6
        },
        end: {
          line: 292,
          column: 56
        }
      },
      "54": {
        start: {
          line: 306,
          column: 4
        },
        end: {
          line: 342,
          column: 5
        }
      },
      "55": {
        start: {
          line: 307,
          column: 37
        },
        end: {
          line: 315,
          column: 7
        }
      },
      "56": {
        start: {
          line: 317,
          column: 24
        },
        end: {
          line: 328,
          column: 10
        }
      },
      "57": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 29
        }
      },
      "58": {
        start: {
          line: 330,
          column: 17
        },
        end: {
          line: 330,
          column: 29
        }
      },
      "59": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 338,
          column: 9
        }
      },
      "60": {
        start: {
          line: 340,
          column: 6
        },
        end: {
          line: 340,
          column: 56
        }
      },
      "61": {
        start: {
          line: 341,
          column: 6
        },
        end: {
          line: 341,
          column: 50
        }
      },
      "62": {
        start: {
          line: 349,
          column: 4
        },
        end: {
          line: 370,
          column: 5
        }
      },
      "63": {
        start: {
          line: 350,
          column: 30
        },
        end: {
          line: 354,
          column: 49
        }
      },
      "64": {
        start: {
          line: 356,
          column: 6
        },
        end: {
          line: 356,
          column: 29
        }
      },
      "65": {
        start: {
          line: 356,
          column: 17
        },
        end: {
          line: 356,
          column: 29
        }
      },
      "66": {
        start: {
          line: 358,
          column: 6
        },
        end: {
          line: 366,
          column: 10
        }
      },
      "67": {
        start: {
          line: 358,
          column: 33
        },
        end: {
          line: 366,
          column: 7
        }
      },
      "68": {
        start: {
          line: 368,
          column: 6
        },
        end: {
          line: 368,
          column: 61
        }
      },
      "69": {
        start: {
          line: 369,
          column: 6
        },
        end: {
          line: 369,
          column: 16
        }
      },
      "70": {
        start: {
          line: 377,
          column: 4
        },
        end: {
          line: 419,
          column: 5
        }
      },
      "71": {
        start: {
          line: 379,
          column: 26
        },
        end: {
          line: 379,
          column: 69
        }
      },
      "72": {
        start: {
          line: 382,
          column: 35
        },
        end: {
          line: 395,
          column: 25
        }
      },
      "73": {
        start: {
          line: 397,
          column: 6
        },
        end: {
          line: 397,
          column: 37
        }
      },
      "74": {
        start: {
          line: 397,
          column: 21
        },
        end: {
          line: 397,
          column: 37
        }
      },
      "75": {
        start: {
          line: 400,
          column: 39
        },
        end: {
          line: 407,
          column: 30
        }
      },
      "76": {
        start: {
          line: 409,
          column: 6
        },
        end: {
          line: 409,
          column: 45
        }
      },
      "77": {
        start: {
          line: 409,
          column: 25
        },
        end: {
          line: 409,
          column: 45
        }
      },
      "78": {
        start: {
          line: 412,
          column: 6
        },
        end: {
          line: 415,
          column: 9
        }
      },
      "79": {
        start: {
          line: 417,
          column: 6
        },
        end: {
          line: 417,
          column: 61
        }
      },
      "80": {
        start: {
          line: 418,
          column: 6
        },
        end: {
          line: 418,
          column: 55
        }
      },
      "81": {
        start: {
          line: 426,
          column: 4
        },
        end: {
          line: 467,
          column: 5
        }
      },
      "82": {
        start: {
          line: 428,
          column: 6
        },
        end: {
          line: 431,
          column: 29
        }
      },
      "83": {
        start: {
          line: 434,
          column: 23
        },
        end: {
          line: 434,
          column: 57
        }
      },
      "84": {
        start: {
          line: 437,
          column: 28
        },
        end: {
          line: 437,
          column: 79
        }
      },
      "85": {
        start: {
          line: 440,
          column: 26
        },
        end: {
          line: 440,
          column: 77
        }
      },
      "86": {
        start: {
          line: 443,
          column: 24
        },
        end: {
          line: 443,
          column: 34
        }
      },
      "87": {
        start: {
          line: 444,
          column: 6
        },
        end: {
          line: 444,
          column: 49
        }
      },
      "88": {
        start: {
          line: 447,
          column: 6
        },
        end: {
          line: 454,
          column: 29
        }
      },
      "89": {
        start: {
          line: 460,
          column: 6
        },
        end: {
          line: 460,
          column: 61
        }
      },
      "90": {
        start: {
          line: 463,
          column: 6
        },
        end: {
          line: 466,
          column: 29
        }
      },
      "91": {
        start: {
          line: 474,
          column: 4
        },
        end: {
          line: 509,
          column: 5
        }
      },
      "92": {
        start: {
          line: 484,
          column: 10
        },
        end: {
          line: 493,
          column: 8
        }
      },
      "93": {
        start: {
          line: 495,
          column: 6
        },
        end: {
          line: 505,
          column: 8
        }
      },
      "94": {
        start: {
          line: 507,
          column: 6
        },
        end: {
          line: 507,
          column: 59
        }
      },
      "95": {
        start: {
          line: 508,
          column: 6
        },
        end: {
          line: 508,
          column: 53
        }
      },
      "96": {
        start: {
          line: 518,
          column: 19
        },
        end: {
          line: 518,
          column: 52
        }
      },
      "97": {
        start: {
          line: 519,
          column: 4
        },
        end: {
          line: 519,
          column: 67
        }
      },
      "98": {
        start: {
          line: 526,
          column: 4
        },
        end: {
          line: 541,
          column: 5
        }
      },
      "99": {
        start: {
          line: 527,
          column: 24
        },
        end: {
          line: 533,
          column: 10
        }
      },
      "100": {
        start: {
          line: 535,
          column: 6
        },
        end: {
          line: 535,
          column: 29
        }
      },
      "101": {
        start: {
          line: 535,
          column: 17
        },
        end: {
          line: 535,
          column: 29
        }
      },
      "102": {
        start: {
          line: 537,
          column: 6
        },
        end: {
          line: 537,
          column: 41
        }
      },
      "103": {
        start: {
          line: 539,
          column: 6
        },
        end: {
          line: 539,
          column: 73
        }
      },
      "104": {
        start: {
          line: 540,
          column: 6
        },
        end: {
          line: 540,
          column: 41
        }
      },
      "105": {
        start: {
          line: 548,
          column: 4
        },
        end: {
          line: 561,
          column: 6
        }
      },
      "106": {
        start: {
          line: 572,
          column: 4
        },
        end: {
          line: 584,
          column: 5
        }
      },
      "107": {
        start: {
          line: 573,
          column: 6
        },
        end: {
          line: 580,
          column: 11
        }
      },
      "108": {
        start: {
          line: 582,
          column: 6
        },
        end: {
          line: 582,
          column: 59
        }
      },
      "109": {
        start: {
          line: 589,
          column: 30
        },
        end: {
          line: 589,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 71,
            column: 2
          },
          end: {
            line: 71,
            column: 3
          }
        },
        loc: {
          start: {
            line: 71,
            column: 69
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 71
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 111,
            column: 2
          },
          end: {
            line: 111,
            column: 3
          }
        },
        loc: {
          start: {
            line: 114,
            column: 30
          },
          end: {
            line: 153,
            column: 3
          }
        },
        line: 114
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 158,
            column: 2
          },
          end: {
            line: 158,
            column: 3
          }
        },
        loc: {
          start: {
            line: 158,
            column: 70
          },
          end: {
            line: 204,
            column: 3
          }
        },
        line: 158
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 209,
            column: 2
          },
          end: {
            line: 209,
            column: 3
          }
        },
        loc: {
          start: {
            line: 213,
            column: 34
          },
          end: {
            line: 268,
            column: 3
          }
        },
        line: 213
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 273,
            column: 2
          },
          end: {
            line: 273,
            column: 3
          }
        },
        loc: {
          start: {
            line: 273,
            column: 58
          },
          end: {
            line: 294,
            column: 3
          }
        },
        line: 273
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 299,
            column: 2
          },
          end: {
            line: 299,
            column: 3
          }
        },
        loc: {
          start: {
            line: 305,
            column: 19
          },
          end: {
            line: 343,
            column: 3
          }
        },
        line: 305
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 348,
            column: 2
          },
          end: {
            line: 348,
            column: 3
          }
        },
        loc: {
          start: {
            line: 348,
            column: 68
          },
          end: {
            line: 371,
            column: 3
          }
        },
        line: 348
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 358,
            column: 22
          },
          end: {
            line: 358,
            column: 23
          }
        },
        loc: {
          start: {
            line: 358,
            column: 33
          },
          end: {
            line: 366,
            column: 7
          }
        },
        line: 358
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 376,
            column: 2
          },
          end: {
            line: 376,
            column: 3
          }
        },
        loc: {
          start: {
            line: 376,
            column: 57
          },
          end: {
            line: 420,
            column: 3
          }
        },
        line: 376
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 425,
            column: 2
          },
          end: {
            line: 425,
            column: 3
          }
        },
        loc: {
          start: {
            line: 425,
            column: 84
          },
          end: {
            line: 468,
            column: 3
          }
        },
        line: 425
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 473,
            column: 2
          },
          end: {
            line: 473,
            column: 3
          }
        },
        loc: {
          start: {
            line: 473,
            column: 62
          },
          end: {
            line: 510,
            column: 3
          }
        },
        line: 473
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 515,
            column: 2
          },
          end: {
            line: 515,
            column: 3
          }
        },
        loc: {
          start: {
            line: 515,
            column: 79
          },
          end: {
            line: 520,
            column: 3
          }
        },
        line: 515
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 525,
            column: 2
          },
          end: {
            line: 525,
            column: 3
          }
        },
        loc: {
          start: {
            line: 525,
            column: 87
          },
          end: {
            line: 542,
            column: 3
          }
        },
        line: 525
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 547,
            column: 2
          },
          end: {
            line: 547,
            column: 3
          }
        },
        loc: {
          start: {
            line: 547,
            column: 62
          },
          end: {
            line: 562,
            column: 3
          }
        },
        line: 547
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 567,
            column: 2
          },
          end: {
            line: 567,
            column: 3
          }
        },
        loc: {
          start: {
            line: 571,
            column: 19
          },
          end: {
            line: 585,
            column: 3
          }
        },
        line: 571
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 79,
            column: 6
          },
          end: {
            line: 81,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 79,
            column: 6
          },
          end: {
            line: 81,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 79
      },
      "1": {
        loc: {
          start: {
            line: 79,
            column: 10
          },
          end: {
            line: 79,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 79,
            column: 10
          },
          end: {
            line: 79,
            column: 15
          }
        }, {
          start: {
            line: 79,
            column: 19
          },
          end: {
            line: 79,
            column: 44
          }
        }],
        line: 79
      },
      "2": {
        loc: {
          start: {
            line: 83,
            column: 6
          },
          end: {
            line: 86,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 83,
            column: 6
          },
          end: {
            line: 86,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 83
      },
      "3": {
        loc: {
          start: {
            line: 140,
            column: 6
          },
          end: {
            line: 140,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 6
          },
          end: {
            line: 140,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "4": {
        loc: {
          start: {
            line: 168,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "5": {
        loc: {
          start: {
            line: 188,
            column: 6
          },
          end: {
            line: 188,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 188,
            column: 6
          },
          end: {
            line: 188,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 188
      },
      "6": {
        loc: {
          start: {
            line: 211,
            column: 4
          },
          end: {
            line: 211,
            column: 53
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 211,
            column: 43
          },
          end: {
            line: 211,
            column: 53
          }
        }],
        line: 211
      },
      "7": {
        loc: {
          start: {
            line: 212,
            column: 4
          },
          end: {
            line: 212,
            column: 31
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 212,
            column: 29
          },
          end: {
            line: 212,
            column: 31
          }
        }],
        line: 212
      },
      "8": {
        loc: {
          start: {
            line: 223,
            column: 6
          },
          end: {
            line: 225,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 6
          },
          end: {
            line: 225,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "9": {
        loc: {
          start: {
            line: 237,
            column: 22
          },
          end: {
            line: 237,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 237,
            column: 51
          },
          end: {
            line: 237,
            column: 63
          }
        }, {
          start: {
            line: 237,
            column: 66
          },
          end: {
            line: 237,
            column: 75
          }
        }],
        line: 237
      },
      "10": {
        loc: {
          start: {
            line: 253,
            column: 6
          },
          end: {
            line: 253,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 253,
            column: 6
          },
          end: {
            line: 253,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 253
      },
      "11": {
        loc: {
          start: {
            line: 284,
            column: 6
          },
          end: {
            line: 284,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 6
          },
          end: {
            line: 284,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "12": {
        loc: {
          start: {
            line: 330,
            column: 6
          },
          end: {
            line: 330,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 330,
            column: 6
          },
          end: {
            line: 330,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 330
      },
      "13": {
        loc: {
          start: {
            line: 356,
            column: 6
          },
          end: {
            line: 356,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 356,
            column: 6
          },
          end: {
            line: 356,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 356
      },
      "14": {
        loc: {
          start: {
            line: 397,
            column: 6
          },
          end: {
            line: 397,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 397,
            column: 6
          },
          end: {
            line: 397,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 397
      },
      "15": {
        loc: {
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 409,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 409,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 409
      },
      "16": {
        loc: {
          start: {
            line: 535,
            column: 6
          },
          end: {
            line: 535,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 535,
            column: 6
          },
          end: {
            line: 535,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 535
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ccf4760dd6ba939a9b142ff3bfb663e2722e439c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_5hqetizdh = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_5hqetizdh();
import { supabase } from "../lib/supabase";
import { encryptionService } from "./encryption";
var PrivacyService = function () {
  function PrivacyService() {
    _classCallCheck(this, PrivacyService);
    this.defaultPrivacySettings = (cov_5hqetizdh().s[0]++, {
      dataCollection: true,
      analytics: false,
      marketing: false,
      socialFeatures: true,
      locationTracking: false,
      videoAnalysis: true,
      aiCoaching: true,
      dataSharing: false,
      notifications: true,
      profileVisibility: 'private',
      activityVisibility: 'private',
      dataRetentionDays: 365
    });
  }
  return _createClass(PrivacyService, [{
    key: "getPrivacySettings",
    value: (function () {
      var _getPrivacySettings = _asyncToGenerator(function* (userId) {
        cov_5hqetizdh().f[0]++;
        cov_5hqetizdh().s[1]++;
        try {
          var _ref = (cov_5hqetizdh().s[2]++, yield supabase.from('privacy_settings').select('*').eq('user_id', userId).single()),
            data = _ref.data,
            error = _ref.error;
          cov_5hqetizdh().s[3]++;
          if ((cov_5hqetizdh().b[1][0]++, error) && (cov_5hqetizdh().b[1][1]++, error.code !== 'PGRST116')) {
            cov_5hqetizdh().b[0][0]++;
            cov_5hqetizdh().s[4]++;
            throw error;
          } else {
            cov_5hqetizdh().b[0][1]++;
          }
          cov_5hqetizdh().s[5]++;
          if (!data) {
            cov_5hqetizdh().b[2][0]++;
            cov_5hqetizdh().s[6]++;
            return yield this.createDefaultPrivacySettings(userId);
          } else {
            cov_5hqetizdh().b[2][1]++;
          }
          cov_5hqetizdh().s[7]++;
          return {
            dataCollection: data.data_collection,
            analytics: data.analytics,
            marketing: data.marketing,
            socialFeatures: data.social_features,
            locationTracking: data.location_tracking,
            videoAnalysis: data.video_analysis,
            aiCoaching: data.ai_coaching,
            dataSharing: data.data_sharing,
            notifications: data.notifications,
            profileVisibility: data.profile_visibility,
            activityVisibility: data.activity_visibility,
            dataRetentionDays: data.data_retention_days
          };
        } catch (error) {
          cov_5hqetizdh().s[8]++;
          console.error('Failed to get privacy settings:', error);
          cov_5hqetizdh().s[9]++;
          return this.defaultPrivacySettings;
        }
      });
      function getPrivacySettings(_x) {
        return _getPrivacySettings.apply(this, arguments);
      }
      return getPrivacySettings;
    }())
  }, {
    key: "updatePrivacySettings",
    value: (function () {
      var _updatePrivacySettings = _asyncToGenerator(function* (userId, settings) {
        cov_5hqetizdh().f[1]++;
        cov_5hqetizdh().s[10]++;
        try {
          var currentSettings = (cov_5hqetizdh().s[11]++, yield this.getPrivacySettings(userId));
          var updatedSettings = (cov_5hqetizdh().s[12]++, Object.assign({}, currentSettings, settings));
          var _ref2 = (cov_5hqetizdh().s[13]++, yield supabase.from('privacy_settings').upsert({
              user_id: userId,
              data_collection: updatedSettings.dataCollection,
              analytics: updatedSettings.analytics,
              marketing: updatedSettings.marketing,
              social_features: updatedSettings.socialFeatures,
              location_tracking: updatedSettings.locationTracking,
              video_analysis: updatedSettings.videoAnalysis,
              ai_coaching: updatedSettings.aiCoaching,
              data_sharing: updatedSettings.dataSharing,
              notifications: updatedSettings.notifications,
              profile_visibility: updatedSettings.profileVisibility,
              activity_visibility: updatedSettings.activityVisibility,
              data_retention_days: updatedSettings.dataRetentionDays,
              updated_at: new Date().toISOString()
            }).select().single()),
            data = _ref2.data,
            error = _ref2.error;
          cov_5hqetizdh().s[14]++;
          if (error) {
            cov_5hqetizdh().b[3][0]++;
            cov_5hqetizdh().s[15]++;
            throw error;
          } else {
            cov_5hqetizdh().b[3][1]++;
          }
          cov_5hqetizdh().s[16]++;
          yield this.logPrivacyEvent(userId, 'settings_updated', {
            changes: settings,
            timestamp: new Date().toISOString()
          });
          cov_5hqetizdh().s[17]++;
          return updatedSettings;
        } catch (error) {
          cov_5hqetizdh().s[18]++;
          console.error('Failed to update privacy settings:', error);
          cov_5hqetizdh().s[19]++;
          throw new Error('Failed to update privacy settings');
        }
      });
      function updatePrivacySettings(_x2, _x3) {
        return _updatePrivacySettings.apply(this, arguments);
      }
      return updatePrivacySettings;
    }())
  }, {
    key: "requestDataExport",
    value: (function () {
      var _requestDataExport = _asyncToGenerator(function* (userId) {
        cov_5hqetizdh().f[2]++;
        cov_5hqetizdh().s[20]++;
        try {
          var _ref3 = (cov_5hqetizdh().s[21]++, yield supabase.from('data_export_requests').select('*').eq('user_id', userId).eq('status', 'pending').single()),
            existingRequest = _ref3.data;
          cov_5hqetizdh().s[22]++;
          if (existingRequest) {
            cov_5hqetizdh().b[4][0]++;
            cov_5hqetizdh().s[23]++;
            throw new Error('Data export request already pending');
          } else {
            cov_5hqetizdh().b[4][1]++;
          }
          var request = (cov_5hqetizdh().s[24]++, {
            userId: userId,
            requestDate: new Date(),
            status: 'pending'
          });
          var _ref4 = (cov_5hqetizdh().s[25]++, yield supabase.from('data_export_requests').insert({
              user_id: userId,
              request_date: request.requestDate.toISOString(),
              status: request.status
            }).select().single()),
            data = _ref4.data,
            error = _ref4.error;
          cov_5hqetizdh().s[26]++;
          if (error) {
            cov_5hqetizdh().b[5][0]++;
            cov_5hqetizdh().s[27]++;
            throw error;
          } else {
            cov_5hqetizdh().b[5][1]++;
          }
          cov_5hqetizdh().s[28]++;
          this.processDataExport(userId, data.id);
          cov_5hqetizdh().s[29]++;
          yield this.logPrivacyEvent(userId, 'data_export_requested', {
            requestId: data.id,
            timestamp: new Date().toISOString()
          });
          cov_5hqetizdh().s[30]++;
          return request;
        } catch (error) {
          cov_5hqetizdh().s[31]++;
          console.error('Failed to request data export:', error);
          cov_5hqetizdh().s[32]++;
          throw new Error('Failed to request data export');
        }
      });
      function requestDataExport(_x4) {
        return _requestDataExport.apply(this, arguments);
      }
      return requestDataExport;
    }())
  }, {
    key: "requestDataDeletion",
    value: (function () {
      var _requestDataDeletion = _asyncToGenerator(function* (userId) {
        var deletionType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_5hqetizdh().b[6][0]++, 'complete');
        var retainedData = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_5hqetizdh().b[7][0]++, []);
        cov_5hqetizdh().f[3]++;
        cov_5hqetizdh().s[33]++;
        try {
          var _ref5 = (cov_5hqetizdh().s[34]++, yield supabase.from('data_deletion_requests').select('*').eq('user_id', userId).in('status', ['pending', 'processing']).single()),
            existingRequest = _ref5.data;
          cov_5hqetizdh().s[35]++;
          if (existingRequest) {
            cov_5hqetizdh().b[8][0]++;
            cov_5hqetizdh().s[36]++;
            throw new Error('Data deletion request already pending');
          } else {
            cov_5hqetizdh().b[8][1]++;
          }
          var scheduledDate = (cov_5hqetizdh().s[37]++, new Date());
          cov_5hqetizdh().s[38]++;
          scheduledDate.setDate(scheduledDate.getDate() + 30);
          var request = (cov_5hqetizdh().s[39]++, {
            userId: userId,
            requestDate: new Date(),
            scheduledDate: scheduledDate,
            status: 'pending',
            deletionType: deletionType,
            retainedData: deletionType === 'partial' ? (cov_5hqetizdh().b[9][0]++, retainedData) : (cov_5hqetizdh().b[9][1]++, undefined)
          });
          var _ref6 = (cov_5hqetizdh().s[40]++, yield supabase.from('data_deletion_requests').insert({
              user_id: userId,
              request_date: request.requestDate.toISOString(),
              scheduled_date: request.scheduledDate.toISOString(),
              status: request.status,
              deletion_type: request.deletionType,
              retained_data: request.retainedData
            }).select().single()),
            data = _ref6.data,
            error = _ref6.error;
          cov_5hqetizdh().s[41]++;
          if (error) {
            cov_5hqetizdh().b[10][0]++;
            cov_5hqetizdh().s[42]++;
            throw error;
          } else {
            cov_5hqetizdh().b[10][1]++;
          }
          cov_5hqetizdh().s[43]++;
          yield this.logPrivacyEvent(userId, 'data_deletion_requested', {
            requestId: data.id,
            deletionType: deletionType,
            scheduledDate: scheduledDate.toISOString(),
            timestamp: new Date().toISOString()
          });
          cov_5hqetizdh().s[44]++;
          return request;
        } catch (error) {
          cov_5hqetizdh().s[45]++;
          console.error('Failed to request data deletion:', error);
          cov_5hqetizdh().s[46]++;
          throw new Error('Failed to request data deletion');
        }
      });
      function requestDataDeletion(_x5) {
        return _requestDataDeletion.apply(this, arguments);
      }
      return requestDataDeletion;
    }())
  }, {
    key: "cancelDataDeletion",
    value: (function () {
      var _cancelDataDeletion = _asyncToGenerator(function* (userId) {
        cov_5hqetizdh().f[4]++;
        cov_5hqetizdh().s[47]++;
        try {
          var _ref7 = (cov_5hqetizdh().s[48]++, yield supabase.from('data_deletion_requests').update({
              status: 'cancelled',
              updated_at: new Date().toISOString()
            }).eq('user_id', userId).in('status', ['pending', 'processing'])),
            error = _ref7.error;
          cov_5hqetizdh().s[49]++;
          if (error) {
            cov_5hqetizdh().b[11][0]++;
            cov_5hqetizdh().s[50]++;
            throw error;
          } else {
            cov_5hqetizdh().b[11][1]++;
          }
          cov_5hqetizdh().s[51]++;
          yield this.logPrivacyEvent(userId, 'data_deletion_cancelled', {
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          cov_5hqetizdh().s[52]++;
          console.error('Failed to cancel data deletion:', error);
          cov_5hqetizdh().s[53]++;
          throw new Error('Failed to cancel data deletion');
        }
      });
      function cancelDataDeletion(_x6) {
        return _cancelDataDeletion.apply(this, arguments);
      }
      return cancelDataDeletion;
    }())
  }, {
    key: "recordConsent",
    value: (function () {
      var _recordConsent = _asyncToGenerator(function* (userId, consentType, granted, version, metadata) {
        cov_5hqetizdh().f[5]++;
        cov_5hqetizdh().s[54]++;
        try {
          var consent = (cov_5hqetizdh().s[55]++, {
            userId: userId,
            consentType: consentType,
            granted: granted,
            timestamp: new Date(),
            version: version,
            ipAddress: metadata == null ? void 0 : metadata.ipAddress,
            userAgent: metadata == null ? void 0 : metadata.userAgent
          });
          var _ref8 = (cov_5hqetizdh().s[56]++, yield supabase.from('consent_records').insert({
              user_id: consent.userId,
              consent_type: consent.consentType,
              granted: consent.granted,
              timestamp: consent.timestamp.toISOString(),
              version: consent.version,
              ip_address: consent.ipAddress,
              user_agent: consent.userAgent,
              metadata: metadata
            })),
            error = _ref8.error;
          cov_5hqetizdh().s[57]++;
          if (error) {
            cov_5hqetizdh().b[12][0]++;
            cov_5hqetizdh().s[58]++;
            throw error;
          } else {
            cov_5hqetizdh().b[12][1]++;
          }
          cov_5hqetizdh().s[59]++;
          yield this.logPrivacyEvent(userId, 'consent_recorded', {
            consentType: consentType,
            granted: granted,
            version: version,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          cov_5hqetizdh().s[60]++;
          console.error('Failed to record consent:', error);
          cov_5hqetizdh().s[61]++;
          throw new Error('Failed to record consent');
        }
      });
      function recordConsent(_x7, _x8, _x9, _x0, _x1) {
        return _recordConsent.apply(this, arguments);
      }
      return recordConsent;
    }())
  }, {
    key: "getConsentHistory",
    value: (function () {
      var _getConsentHistory = _asyncToGenerator(function* (userId) {
        cov_5hqetizdh().f[6]++;
        cov_5hqetizdh().s[62]++;
        try {
          var _ref9 = (cov_5hqetizdh().s[63]++, yield supabase.from('consent_records').select('*').eq('user_id', userId).order('timestamp', {
              ascending: false
            })),
            data = _ref9.data,
            error = _ref9.error;
          cov_5hqetizdh().s[64]++;
          if (error) {
            cov_5hqetizdh().b[13][0]++;
            cov_5hqetizdh().s[65]++;
            throw error;
          } else {
            cov_5hqetizdh().b[13][1]++;
          }
          cov_5hqetizdh().s[66]++;
          return data.map(function (record) {
            cov_5hqetizdh().f[7]++;
            cov_5hqetizdh().s[67]++;
            return {
              userId: record.user_id,
              consentType: record.consent_type,
              granted: record.granted,
              timestamp: new Date(record.timestamp),
              version: record.version,
              ipAddress: record.ip_address,
              userAgent: record.user_agent
            };
          });
        } catch (error) {
          cov_5hqetizdh().s[68]++;
          console.error('Failed to get consent history:', error);
          cov_5hqetizdh().s[69]++;
          return [];
        }
      });
      function getConsentHistory(_x10) {
        return _getConsentHistory.apply(this, arguments);
      }
      return getConsentHistory;
    }())
  }, {
    key: "anonymizeUserData",
    value: (function () {
      var _anonymizeUserData = _asyncToGenerator(function* (userId) {
        cov_5hqetizdh().f[8]++;
        cov_5hqetizdh().s[70]++;
        try {
          var anonymousId = (cov_5hqetizdh().s[71]++, `anon_${encryptionService.generateKey(16)}`);
          var _ref0 = (cov_5hqetizdh().s[72]++, yield supabase.from('users').update({
              email: `${anonymousId}@anonymized.local`,
              full_name: 'Anonymous User',
              phone: null,
              birth_date: null,
              location: null,
              bio: null,
              avatar_url: null,
              anonymized: true,
              anonymized_at: new Date().toISOString()
            }).eq('id', userId)),
            userError = _ref0.error;
          cov_5hqetizdh().s[73]++;
          if (userError) {
            cov_5hqetizdh().b[14][0]++;
            cov_5hqetizdh().s[74]++;
            throw userError;
          } else {
            cov_5hqetizdh().b[14][1]++;
          }
          var _ref1 = (cov_5hqetizdh().s[75]++, yield supabase.from('training_sessions').update({
              title: 'Anonymous Training Session',
              notes: null,
              video_url: null
            }).eq('user_id', userId)),
            sessionsError = _ref1.error;
          cov_5hqetizdh().s[76]++;
          if (sessionsError) {
            cov_5hqetizdh().b[15][0]++;
            cov_5hqetizdh().s[77]++;
            throw sessionsError;
          } else {
            cov_5hqetizdh().b[15][1]++;
          }
          cov_5hqetizdh().s[78]++;
          yield this.logPrivacyEvent(userId, 'data_anonymized', {
            anonymousId: anonymousId,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          cov_5hqetizdh().s[79]++;
          console.error('Failed to anonymize user data:', error);
          cov_5hqetizdh().s[80]++;
          throw new Error('Failed to anonymize user data');
        }
      });
      function anonymizeUserData(_x11) {
        return _anonymizeUserData.apply(this, arguments);
      }
      return anonymizeUserData;
    }())
  }, {
    key: "processDataExport",
    value: (function () {
      var _processDataExport = _asyncToGenerator(function* (userId, requestId) {
        cov_5hqetizdh().f[9]++;
        cov_5hqetizdh().s[81]++;
        try {
          cov_5hqetizdh().s[82]++;
          yield supabase.from('data_export_requests').update({
            status: 'processing'
          }).eq('id', requestId);
          var userData = (cov_5hqetizdh().s[83]++, yield this.collectUserData(userId));
          var encryptedData = (cov_5hqetizdh().s[84]++, encryptionService.encrypt(JSON.stringify(userData)));
          var downloadUrl = (cov_5hqetizdh().s[85]++, yield this.generateSecureDownloadUrl(encryptedData));
          var expiresAt = (cov_5hqetizdh().s[86]++, new Date());
          cov_5hqetizdh().s[87]++;
          expiresAt.setDate(expiresAt.getDate() + 7);
          cov_5hqetizdh().s[88]++;
          yield supabase.from('data_export_requests').update({
            status: 'completed',
            download_url: downloadUrl,
            expires_at: expiresAt.toISOString()
          }).eq('id', requestId);
        } catch (error) {
          cov_5hqetizdh().s[89]++;
          console.error('Data export processing failed:', error);
          cov_5hqetizdh().s[90]++;
          yield supabase.from('data_export_requests').update({
            status: 'failed'
          }).eq('id', requestId);
        }
      });
      function processDataExport(_x12, _x13) {
        return _processDataExport.apply(this, arguments);
      }
      return processDataExport;
    }())
  }, {
    key: "collectUserData",
    value: (function () {
      var _collectUserData = _asyncToGenerator(function* (userId) {
        cov_5hqetizdh().f[10]++;
        cov_5hqetizdh().s[91]++;
        try {
          var _ref10 = (cov_5hqetizdh().s[92]++, yield Promise.all([supabase.from('users').select('*').eq('id', userId).single(), supabase.from('skill_stats').select('*').eq('user_id', userId), supabase.from('training_sessions').select('*').eq('user_id', userId), supabase.from('match_results').select('*').eq('user_id', userId), supabase.from('achievements').select('*').eq('user_id', userId), supabase.from('ai_tips').select('*').eq('user_id', userId), this.getPrivacySettings(userId), this.getConsentHistory(userId)])),
            _ref11 = _slicedToArray(_ref10, 8),
            user = _ref11[0],
            skillStats = _ref11[1],
            trainingSessions = _ref11[2],
            matchResults = _ref11[3],
            achievements = _ref11[4],
            aiTips = _ref11[5],
            privacySettings = _ref11[6],
            consentHistory = _ref11[7];
          cov_5hqetizdh().s[93]++;
          return {
            exportDate: new Date().toISOString(),
            user: user.data,
            skillStats: skillStats.data,
            trainingSessions: trainingSessions.data,
            matchResults: matchResults.data,
            achievements: achievements.data,
            aiTips: aiTips.data,
            privacySettings: privacySettings,
            consentHistory: consentHistory
          };
        } catch (error) {
          cov_5hqetizdh().s[94]++;
          console.error('Failed to collect user data:', error);
          cov_5hqetizdh().s[95]++;
          throw new Error('Failed to collect user data');
        }
      });
      function collectUserData(_x14) {
        return _collectUserData.apply(this, arguments);
      }
      return collectUserData;
    }())
  }, {
    key: "generateSecureDownloadUrl",
    value: (function () {
      var _generateSecureDownloadUrl = _asyncToGenerator(function* (encryptedData) {
        cov_5hqetizdh().f[11]++;
        var dataId = (cov_5hqetizdh().s[96]++, encryptionService.generateKey(16));
        cov_5hqetizdh().s[97]++;
        return `https://secure-exports.acemind.app/download/${dataId}`;
      });
      function generateSecureDownloadUrl(_x15) {
        return _generateSecureDownloadUrl.apply(this, arguments);
      }
      return generateSecureDownloadUrl;
    }())
  }, {
    key: "createDefaultPrivacySettings",
    value: (function () {
      var _createDefaultPrivacySettings = _asyncToGenerator(function* (userId) {
        cov_5hqetizdh().f[12]++;
        cov_5hqetizdh().s[98]++;
        try {
          var _ref12 = (cov_5hqetizdh().s[99]++, yield supabase.from('privacy_settings').insert(Object.assign({
              user_id: userId
            }, this.convertSettingsToDb(this.defaultPrivacySettings), {
              created_at: new Date().toISOString()
            }))),
            error = _ref12.error;
          cov_5hqetizdh().s[100]++;
          if (error) {
            cov_5hqetizdh().b[16][0]++;
            cov_5hqetizdh().s[101]++;
            throw error;
          } else {
            cov_5hqetizdh().b[16][1]++;
          }
          cov_5hqetizdh().s[102]++;
          return this.defaultPrivacySettings;
        } catch (error) {
          cov_5hqetizdh().s[103]++;
          console.error('Failed to create default privacy settings:', error);
          cov_5hqetizdh().s[104]++;
          return this.defaultPrivacySettings;
        }
      });
      function createDefaultPrivacySettings(_x16) {
        return _createDefaultPrivacySettings.apply(this, arguments);
      }
      return createDefaultPrivacySettings;
    }())
  }, {
    key: "convertSettingsToDb",
    value: function convertSettingsToDb(settings) {
      cov_5hqetizdh().f[13]++;
      cov_5hqetizdh().s[105]++;
      return {
        data_collection: settings.dataCollection,
        analytics: settings.analytics,
        marketing: settings.marketing,
        social_features: settings.socialFeatures,
        location_tracking: settings.locationTracking,
        video_analysis: settings.videoAnalysis,
        ai_coaching: settings.aiCoaching,
        data_sharing: settings.dataSharing,
        notifications: settings.notifications,
        profile_visibility: settings.profileVisibility,
        activity_visibility: settings.activityVisibility,
        data_retention_days: settings.dataRetentionDays
      };
    }
  }, {
    key: "logPrivacyEvent",
    value: (function () {
      var _logPrivacyEvent = _asyncToGenerator(function* (userId, eventType, metadata) {
        cov_5hqetizdh().f[14]++;
        cov_5hqetizdh().s[106]++;
        try {
          cov_5hqetizdh().s[107]++;
          yield supabase.from('privacy_events').insert({
            user_id: userId,
            event_type: eventType,
            metadata: metadata,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          cov_5hqetizdh().s[108]++;
          console.error('Failed to log privacy event:', error);
        }
      });
      function logPrivacyEvent(_x17, _x18, _x19) {
        return _logPrivacyEvent.apply(this, arguments);
      }
      return logPrivacyEvent;
    }())
  }]);
}();
export var privacyService = (cov_5hqetizdh().s[109]++, new PrivacyService());
export default privacyService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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