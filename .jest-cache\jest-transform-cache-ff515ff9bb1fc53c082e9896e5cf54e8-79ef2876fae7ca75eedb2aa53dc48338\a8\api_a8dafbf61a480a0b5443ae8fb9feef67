d7e7a465adb84fc2565e2bbe2ed1a51d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.apiService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _env2 = require("expo/virtual/env");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _supabase = require("../lib/supabase");
var _openai = require("./openai");
var ApiService = function () {
  function ApiService() {
    (0, _classCallCheck2.default)(this, ApiService);
    this.baseUrl = _env2.env.EXPO_PUBLIC_SUPABASE_URL || 'https://your-project.supabase.co';
    this.apiKey = _env2.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'your-anon-key';
    this.mockUser = {
      id: 'user-sara-lee-123',
      email: '<EMAIL>',
      full_name: 'Sara Lee',
      skill_level: 'club',
      preferred_surface: 'hard',
      goals: ['serve', 'mental'],
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-12-20T15:30:00Z'
    };
    this.mockSkillStats = {
      id: 'stats-sara-123',
      user_id: 'user-sara-lee-123',
      forehand: 78,
      backhand: 65,
      serve: 82,
      volley: 70,
      footwork: 75,
      strategy: 68,
      mental_game: 73,
      updated_at: '2024-12-20T15:30:00Z'
    };
    this.mockRecentSessions = [{
      id: 'session-1',
      user_id: 'user-sara-lee-123',
      session_type: 'video_analysis',
      title: 'Forehand Technique Analysis',
      description: 'Analyzed crosscourt forehand consistency',
      duration_minutes: 45,
      ai_feedback_summary: 'Excellent toss placement and follow-through. Work on knee bend for more power.',
      improvement_areas: ['Follow-through', 'Knee bend', 'Contact point'],
      skill_improvements: {
        forehand: 5,
        serve: 2
      },
      video_url: 'https://example.com/video1.mp4',
      created_at: '2024-12-20T14:00:00Z'
    }, {
      id: 'session-2',
      user_id: 'user-sara-lee-123',
      session_type: 'match_simulation',
      title: 'vs AI Aggressive Baseliner',
      description: 'Simulated match against aggressive playing style',
      duration_minutes: 90,
      ai_feedback_summary: 'Great court positioning! Focus on varying shot placement to keep opponent guessing.',
      improvement_areas: ['Shot variety', 'Net approaches', 'Defensive positioning'],
      skill_improvements: {
        strategy: 3,
        mental_game: 4
      },
      created_at: '2024-12-19T16:30:00Z'
    }, {
      id: 'session-3',
      user_id: 'user-sara-lee-123',
      session_type: 'drill_practice',
      title: 'Serve Placement Drill',
      description: 'Practiced targeting different service boxes',
      duration_minutes: 30,
      ai_feedback_summary: 'Improved accuracy by 15%! Keep working on second serve consistency.',
      improvement_areas: ['Second serve', 'Placement accuracy'],
      skill_improvements: {
        serve: 3
      },
      created_at: '2024-12-18T11:00:00Z'
    }];
    this.mockLatestMatch = {
      id: 'match-1',
      user_id: 'user-sara-lee-123',
      opponent_name: 'AI: Aggressive Baseliner',
      opponent_type: 'ai',
      match_score: '6-3, 3-6, 6-4',
      sets: [6, 3, 6],
      opponent_sets: [3, 6, 4],
      surface: 'Hard Court',
      duration_minutes: 134,
      result: 'win',
      match_stats: {
        winners: 23,
        unforced_errors: 18,
        aces: 7,
        double_faults: 4,
        first_serve_percentage: 68,
        break_points_converted: '4/7'
      },
      created_at: '2024-12-19T18:45:00Z'
    };
    this.mockAchievements = [{
      id: 'achievement-1',
      user_id: 'user-sara-lee-123',
      badge_type: 'serve_master',
      title: 'Serve Master',
      description: 'Achieved 80%+ serve accuracy',
      icon: 'trophy',
      color: '#ffe600',
      unlocked_at: '2024-12-18T20:00:00Z'
    }, {
      id: 'achievement-2',
      user_id: 'user-sara-lee-123',
      badge_type: 'consistency_king',
      title: 'Consistency King',
      description: 'Complete 7 days of training',
      icon: 'target',
      color: '#23ba16',
      unlocked_at: '2024-12-15T09:00:00Z'
    }, {
      id: 'achievement-3',
      user_id: 'user-sara-lee-123',
      badge_type: 'video_analyst',
      title: 'Video Analyst',
      description: 'Upload 10 training videos',
      icon: 'bar-chart',
      color: '#23ba16',
      unlocked_at: '2024-12-20T16:00:00Z',
      progress: 7,
      total: 10
    }];
    this.mockNotifications = [{
      id: 'notif-1',
      user_id: 'user-sara-lee-123',
      type: 'achievement',
      title: 'New Badge Unlocked!',
      message: 'You earned the "Serve Master" badge for achieving 80%+ serve accuracy!',
      read: false,
      created_at: '2024-12-18T20:00:00Z'
    }, {
      id: 'notif-2',
      user_id: 'user-sara-lee-123',
      type: 'tip',
      title: 'Daily AI Tip',
      message: 'Focus on keeping your wrist loose during serves for more power.',
      read: false,
      created_at: '2024-12-20T08:00:00Z'
    }, {
      id: 'notif-3',
      user_id: 'user-sara-lee-123',
      type: 'match_result',
      title: 'Match Complete',
      message: 'Great win against AI Aggressive Baseliner! Check your analysis.',
      read: true,
      created_at: '2024-12-19T18:50:00Z'
    }];
    this.mockDailyTip = {
      id: 'tip-daily-1',
      user_id: 'user-sara-lee-123',
      tip_text: 'Focus on your follow-through when hitting forehands. Keep your racquet head up and finish with your elbow pointing towards your target for better topspin and control.',
      category: 'technique',
      personalized: true,
      created_at: '2024-12-20T08:00:00Z'
    };
  }
  return (0, _createClass2.default)(ApiService, [{
    key: "delay",
    value: function () {
      var _delay = (0, _asyncToGenerator2.default)(function* () {
        var ms = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 800;
        return new Promise(function (resolve) {
          return setTimeout(resolve, ms);
        });
      });
      function delay() {
        return _delay.apply(this, arguments);
      }
      return delay;
    }()
  }, {
    key: "getDashboardData",
    value: function () {
      var _getDashboardData = (0, _asyncToGenerator2.default)(function* (userId) {
        try {
          var _yield$supabase$from$ = yield _supabase.supabase.from('users').select('*').eq('id', userId).single(),
            user = _yield$supabase$from$.data,
            userError = _yield$supabase$from$.error;
          if (userError) {
            console.error('Error fetching user:', userError);
            throw new Error('Failed to fetch user data');
          }
          var _yield$supabase$from$2 = yield _supabase.supabase.from('skill_stats').select('*').eq('user_id', userId).single(),
            skillStats = _yield$supabase$from$2.data,
            skillStatsError = _yield$supabase$from$2.error;
          if (skillStatsError) {
            console.error('Error fetching skill stats:', skillStatsError);
            var _yield$supabase$from$3 = yield _supabase.supabase.from('skill_stats').insert({
                user_id: userId,
                forehand: 50,
                backhand: 50,
                serve: 50,
                volley: 50,
                footwork: 50,
                strategy: 50,
                mental_game: 50
              }).select().single(),
              newSkillStats = _yield$supabase$from$3.data,
              createError = _yield$supabase$from$3.error;
            if (createError) {
              throw new Error('Failed to create skill stats');
            }
            var finalSkillStats = newSkillStats || this.mockSkillStats;
          }
          var _yield$supabase$from$4 = yield _supabase.supabase.from('training_sessions').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(3),
            recentSessions = _yield$supabase$from$4.data,
            sessionsError = _yield$supabase$from$4.error;
          if (sessionsError) {
            console.error('Error fetching training sessions:', sessionsError);
          }
          var _yield$supabase$from$5 = yield _supabase.supabase.from('match_results').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(1),
            latestMatchArray = _yield$supabase$from$5.data,
            matchError = _yield$supabase$from$5.error;
          if (matchError) {
            console.error('Error fetching match results:', matchError);
          }
          var latestMatch = latestMatchArray && latestMatchArray.length > 0 ? latestMatchArray[0] : null;
          var _yield$supabase$from$6 = yield _supabase.supabase.from('achievements').select('*').eq('user_id', userId).order('unlocked_at', {
              ascending: false
            }),
            achievements = _yield$supabase$from$6.data,
            achievementsError = _yield$supabase$from$6.error;
          if (achievementsError) {
            console.error('Error fetching achievements:', achievementsError);
          }
          var _yield$supabase$from$7 = yield _supabase.supabase.from('notifications').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(10),
            notifications = _yield$supabase$from$7.data,
            notificationsError = _yield$supabase$from$7.error;
          if (notificationsError) {
            console.error('Error fetching notifications:', notificationsError);
          }
          var _yield$supabase$from$8 = yield _supabase.supabase.from('ai_tips').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(1),
            dailyTipArray = _yield$supabase$from$8.data,
            tipError = _yield$supabase$from$8.error;
          if (tipError) {
            console.error('Error fetching daily tip:', tipError);
          }
          var dailyTip = dailyTipArray && dailyTipArray.length > 0 ? dailyTipArray[0] : this.mockDailyTip;
          return {
            user: user || this.mockUser,
            skillStats: skillStats || this.mockSkillStats,
            recentSessions: recentSessions || this.mockRecentSessions,
            latestMatch: latestMatch,
            achievements: achievements || this.mockAchievements,
            notifications: notifications || this.mockNotifications,
            dailyTip: dailyTip
          };
        } catch (error) {
          console.error('Error in getDashboardData:', error);
          return {
            user: this.mockUser,
            skillStats: this.mockSkillStats,
            recentSessions: this.mockRecentSessions,
            latestMatch: this.mockLatestMatch,
            achievements: this.mockAchievements,
            notifications: this.mockNotifications,
            dailyTip: this.mockDailyTip
          };
        }
      });
      function getDashboardData(_x) {
        return _getDashboardData.apply(this, arguments);
      }
      return getDashboardData;
    }()
  }, {
    key: "generateAITip",
    value: function () {
      var _generateAITip = (0, _asyncToGenerator2.default)(function* (userId, context) {
        try {
          var _yield$supabase$from$9 = yield _supabase.supabase.from('users').select('*').eq('id', userId).single(),
            user = _yield$supabase$from$9.data;
          var _yield$supabase$from$0 = yield _supabase.supabase.from('skill_stats').select('*').eq('user_id', userId).single(),
            skillStats = _yield$supabase$from$0.data;
          var _yield$supabase$from$1 = yield _supabase.supabase.from('training_sessions').select('title').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(3),
            recentSessions = _yield$supabase$from$1.data;
          var coachingRequest = {
            skillLevel: (user == null ? void 0 : user.skill_level) || 'intermediate',
            recentSessions: (recentSessions == null ? void 0 : recentSessions.map(function (s) {
              return s.title;
            })) || [],
            currentStats: skillStats || {
              forehand: 50,
              backhand: 50,
              serve: 50,
              volley: 50,
              footwork: 50,
              strategy: 50,
              mental_game: 50
            },
            context: context || 'daily tip generation'
          };
          var aiCoaching = yield _openai.openAIService.generateCoachingAdvice(coachingRequest);
          var tipText = aiCoaching.personalizedTip;
          var newTip = {
            user_id: userId,
            tip_text: tipText,
            category: 'technique',
            personalized: true
          };
          var _yield$supabase$from$10 = yield _supabase.supabase.from('ai_tips').insert(newTip).select().single(),
            data = _yield$supabase$from$10.data,
            error = _yield$supabase$from$10.error;
          if (error) {
            console.error('Error saving AI tip:', error);
            return {
              id: `tip-${Date.now()}`,
              user_id: userId,
              tip_text: tipText,
              category: 'technique',
              personalized: true,
              created_at: new Date().toISOString()
            };
          }
          return data;
        } catch (error) {
          console.error('Error generating AI tip:', error);
          var fallbackTips = ['Focus on your split step timing - it should happen just as your opponent makes contact with the ball.', 'Practice your serve toss consistency by catching 10 tosses in a row at the same height.', 'When approaching the net, aim your approach shot deep and to your opponent\'s weaker side.', 'Work on your recovery step after each shot to maintain better court positioning.', 'Use the continental grip for all volleys to improve your net game consistency.'];
          var _tipText = fallbackTips[Math.floor(Math.random() * fallbackTips.length)];
          return {
            id: `tip-${Date.now()}`,
            user_id: userId,
            tip_text: _tipText,
            category: 'technique',
            personalized: false,
            created_at: new Date().toISOString()
          };
        }
      });
      function generateAITip(_x2, _x3) {
        return _generateAITip.apply(this, arguments);
      }
      return generateAITip;
    }()
  }, {
    key: "markNotificationAsRead",
    value: function () {
      var _markNotificationAsRead = (0, _asyncToGenerator2.default)(function* (notificationId) {
        try {
          var _yield$supabase$from$11 = yield _supabase.supabase.from('notifications').update({
              read: true
            }).eq('id', notificationId),
            error = _yield$supabase$from$11.error;
          if (error) {
            console.error('Error marking notification as read:', error);
            throw new Error('Failed to mark notification as read');
          }
        } catch (error) {
          console.error('Error in markNotificationAsRead:', error);
          var notification = this.mockNotifications.find(function (n) {
            return n.id === notificationId;
          });
          if (notification) {
            notification.read = true;
          }
        }
      });
      function markNotificationAsRead(_x4) {
        return _markNotificationAsRead.apply(this, arguments);
      }
      return markNotificationAsRead;
    }()
  }, {
    key: "refreshUserStats",
    value: function () {
      var _refreshUserStats = (0, _asyncToGenerator2.default)(function* (userId) {
        try {
          var _yield$supabase$from$12 = yield _supabase.supabase.from('skill_stats').select('*').eq('user_id', userId).single(),
            currentStats = _yield$supabase$from$12.data,
            fetchError = _yield$supabase$from$12.error;
          if (fetchError) {
            console.error('Error fetching current stats:', fetchError);
            throw new Error('Failed to fetch current stats');
          }
          var updatedStats = Object.assign({}, currentStats);
          var skillKeys = ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game'];
          skillKeys.forEach(function (key) {
            var currentValue = updatedStats[key];
            updatedStats[key] = Math.min(100, currentValue + Math.floor(Math.random() * 3));
          });
          var _yield$supabase$from$13 = yield _supabase.supabase.from('skill_stats').update(updatedStats).eq('user_id', userId).select().single(),
            newStats = _yield$supabase$from$13.data,
            updateError = _yield$supabase$from$13.error;
          if (updateError) {
            console.error('Error updating stats:', updateError);
            throw new Error('Failed to update stats');
          }
          return newStats;
        } catch (error) {
          console.error('Error in refreshUserStats:', error);
          var _updatedStats = Object.assign({}, this.mockSkillStats);
          Object.keys(_updatedStats).forEach(function (key) {
            if (typeof _updatedStats[key] === 'number' && key !== 'id') {
              var currentValue = _updatedStats[key];
              _updatedStats[key] = Math.min(100, currentValue + Math.floor(Math.random() * 3));
            }
          });
          _updatedStats.updated_at = new Date().toISOString();
          this.mockSkillStats = _updatedStats;
          return _updatedStats;
        }
      });
      function refreshUserStats(_x5) {
        return _refreshUserStats.apply(this, arguments);
      }
      return refreshUserStats;
    }()
  }, {
    key: "getPerformanceMetrics",
    value: (function () {
      var _getPerformanceMetrics = (0, _asyncToGenerator2.default)(function* (userId) {
        var _this = this;
        try {
          var _yield$supabase$from$14 = yield _supabase.supabase.from('matches').select(`
          id,
          statistics,
          result,
          match_date,
          created_at
        `).eq('user_id', userId).order('match_date', {
              ascending: false
            }).limit(10),
            matches = _yield$supabase$from$14.data,
            error = _yield$supabase$from$14.error;
          if (error || !matches || matches.length === 0) {
            return this.getDefaultPerformanceMetrics();
          }
          var totalServe = 0,
            totalForehand = 0,
            totalBackhand = 0;
          var totalVolley = 0,
            totalMovement = 0;
          var validMatches = 0;
          matches.forEach(function (match) {
            if (match.statistics) {
              var stats = typeof match.statistics === 'string' ? JSON.parse(match.statistics) : match.statistics;
              var _serveRating = _this.calculateServeRating(stats);
              var _forehandRating = _this.calculateStrokeRating(stats, 'forehand');
              var _backhandRating = _this.calculateStrokeRating(stats, 'backhand');
              var _volleyRating = _this.calculateVolleyRating(stats);
              var _movementRating = _this.calculateMovementRating(stats);
              totalServe += _serveRating;
              totalForehand += _forehandRating;
              totalBackhand += _backhandRating;
              totalVolley += _volleyRating;
              totalMovement += _movementRating;
              validMatches++;
            }
          });
          if (validMatches === 0) {
            return this.getDefaultPerformanceMetrics();
          }
          var serveRating = Math.round(totalServe / validMatches);
          var forehandRating = Math.round(totalForehand / validMatches);
          var backhandRating = Math.round(totalBackhand / validMatches);
          var volleyRating = Math.round(totalVolley / validMatches);
          var movementRating = Math.round(totalMovement / validMatches);
          var overallRating = Math.round((serveRating + forehandRating + backhandRating + volleyRating + movementRating) / 5);
          var improvementTrend = yield this.calculateImprovementTrend(userId, overallRating);
          return {
            overallRating: overallRating,
            serveRating: serveRating,
            forehandRating: forehandRating,
            backhandRating: backhandRating,
            volleyRating: volleyRating,
            movementRating: movementRating,
            improvementTrend: improvementTrend,
            lastUpdated: new Date().toISOString()
          };
        } catch (error) {
          console.error('Error getting performance metrics:', error);
          return this.getDefaultPerformanceMetrics();
        }
      });
      function getPerformanceMetrics(_x6) {
        return _getPerformanceMetrics.apply(this, arguments);
      }
      return getPerformanceMetrics;
    }())
  }, {
    key: "getWeeklyStatistics",
    value: (function () {
      var _getWeeklyStatistics = (0, _asyncToGenerator2.default)(function* (userId) {
        try {
          var oneWeekAgo = new Date();
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
          var _yield$supabase$from$15 = yield _supabase.supabase.from('training_sessions').select('*').eq('user_id', userId).gte('session_date', oneWeekAgo.toISOString().split('T')[0]),
            sessions = _yield$supabase$from$15.data,
            error = _yield$supabase$from$15.error;
          if (error) {
            console.error('Error fetching weekly statistics:', error);
            return this.getDefaultWeeklyStats();
          }
          var sessionsData = sessions || [];
          var sessionsCompleted = sessionsData.length;
          var totalPracticeTime = sessionsData.reduce(function (sum, session) {
            return sum + (session.duration_minutes || 0);
          }, 0);
          var averageScore = sessionsCompleted > 0 ? Math.round(sessionsData.reduce(function (sum, session) {
            return sum + (session.overall_score || 0);
          }, 0) / sessionsCompleted) : 0;
          var twoWeeksAgo = new Date();
          twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
          var _yield$supabase$from$16 = yield _supabase.supabase.from('training_sessions').select('overall_score').eq('user_id', userId).gte('session_date', twoWeeksAgo.toISOString().split('T')[0]).lt('session_date', oneWeekAgo.toISOString().split('T')[0]),
            previousWeekSessions = _yield$supabase$from$16.data;
          var previousWeekAverage = previousWeekSessions && previousWeekSessions.length > 0 ? previousWeekSessions.reduce(function (sum, session) {
            return sum + (session.overall_score || 0);
          }, 0) / previousWeekSessions.length : 0;
          var improvement = previousWeekAverage > 0 ? Math.round((averageScore - previousWeekAverage) / previousWeekAverage * 100) : 0;
          var sessionMetrics = sessionsData.map(function (session) {
            return {
              improvementScore: session.improvement_score || Math.floor(Math.random() * 20) + 70,
              consistencyRating: session.consistency_rating || Math.floor(Math.random() * 15) + 75
            };
          });
          return {
            sessionsCompleted: sessionsCompleted,
            totalPracticeTime: totalPracticeTime,
            averageScore: averageScore,
            improvement: improvement,
            sessionMetrics: sessionMetrics
          };
        } catch (error) {
          console.error('Error getting weekly statistics:', error);
          return this.getDefaultWeeklyStats();
        }
      });
      function getWeeklyStatistics(_x7) {
        return _getWeeklyStatistics.apply(this, arguments);
      }
      return getWeeklyStatistics;
    }())
  }, {
    key: "calculateServeRating",
    value: function calculateServeRating(stats) {
      var firstServePercentage = stats.firstServePercentage || 0;
      var aces = stats.aces || 0;
      var doubleFaults = stats.doubleFaults || 0;
      return Math.max(0, Math.min(100, firstServePercentage + aces * 2 - doubleFaults * 3));
    }
  }, {
    key: "calculateStrokeRating",
    value: function calculateStrokeRating(stats, strokeType) {
      var winners = stats[`${strokeType}Winners`] || 0;
      var errors = stats[`${strokeType}Errors`] || 0;
      return Math.max(0, Math.min(100, 70 + winners * 2 - errors));
    }
  }, {
    key: "calculateVolleyRating",
    value: function calculateVolleyRating(stats) {
      var netPointsAttempted = stats.netPointsAttempted || 0;
      var netPointsWon = stats.netPointsWon || 0;
      if (netPointsAttempted === 0) return 70;
      return Math.max(0, Math.min(100, netPointsWon / netPointsAttempted * 100));
    }
  }, {
    key: "calculateMovementRating",
    value: function calculateMovementRating(stats) {
      var totalPointsWon = stats.totalPointsWon || 0;
      var totalPointsPlayed = stats.totalPointsPlayed || 1;
      return Math.max(0, Math.min(100, 70 + totalPointsWon / totalPointsPlayed * 30));
    }
  }, {
    key: "calculateImprovementTrend",
    value: (function () {
      var _calculateImprovementTrend = (0, _asyncToGenerator2.default)(function* (userId, currentRating) {
        var _this2 = this;
        try {
          var oneMonthAgo = new Date();
          oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
          var _yield$supabase$from$17 = yield _supabase.supabase.from('matches').select('statistics').eq('user_id', userId).lt('match_date', oneMonthAgo.toISOString().split('T')[0]).order('match_date', {
              ascending: false
            }).limit(5),
            oldMatches = _yield$supabase$from$17.data;
          if (!oldMatches || oldMatches.length === 0) {
            return 0;
          }
          var totalOldRating = 0;
          var validOldMatches = 0;
          oldMatches.forEach(function (match) {
            if (match.statistics) {
              var stats = typeof match.statistics === 'string' ? JSON.parse(match.statistics) : match.statistics;
              var oldRating = (_this2.calculateServeRating(stats) + _this2.calculateStrokeRating(stats, 'forehand') + _this2.calculateStrokeRating(stats, 'backhand') + _this2.calculateVolleyRating(stats) + _this2.calculateMovementRating(stats)) / 5;
              totalOldRating += oldRating;
              validOldMatches++;
            }
          });
          if (validOldMatches === 0) return 0;
          var oldAverageRating = totalOldRating / validOldMatches;
          return Math.round(currentRating - oldAverageRating);
        } catch (error) {
          console.error('Error calculating improvement trend:', error);
          return 0;
        }
      });
      function calculateImprovementTrend(_x8, _x9) {
        return _calculateImprovementTrend.apply(this, arguments);
      }
      return calculateImprovementTrend;
    }())
  }, {
    key: "getDefaultPerformanceMetrics",
    value: function getDefaultPerformanceMetrics() {
      return {
        overallRating: 75,
        serveRating: 70,
        forehandRating: 80,
        backhandRating: 70,
        volleyRating: 65,
        movementRating: 75,
        improvementTrend: 0,
        lastUpdated: new Date().toISOString()
      };
    }
  }, {
    key: "getDefaultWeeklyStats",
    value: function getDefaultWeeklyStats() {
      return {
        sessionsCompleted: 0,
        totalPracticeTime: 0,
        averageScore: 0,
        improvement: 0,
        sessionMetrics: []
      };
    }
  }]);
}();
var apiService = exports.apiService = new ApiService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************