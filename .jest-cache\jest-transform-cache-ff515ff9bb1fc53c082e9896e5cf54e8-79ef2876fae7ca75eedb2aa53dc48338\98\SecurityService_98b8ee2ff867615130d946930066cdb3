5f14f772cc7e48389ad1e8572073dc00
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_m56dx3rw1() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\security\\SecurityService.ts";
  var hash = "ad94c963f7e1107a8835753ba4ce5e724bd1fa0d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\security\\SecurityService.ts",
    statementMap: {
      "0": {
        start: {
          line: 82,
          column: 56
        },
        end: {
          line: 82,
          column: 65
        }
      },
      "1": {
        start: {
          line: 83,
          column: 34
        },
        end: {
          line: 83,
          column: 36
        }
      },
      "2": {
        start: {
          line: 84,
          column: 37
        },
        end: {
          line: 91,
          column: 3
        }
      },
      "3": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 128,
          column: 6
        }
      },
      "4": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 130,
          column: 32
        }
      },
      "5": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 131,
          column: 29
        }
      },
      "6": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 155,
          column: 5
        }
      },
      "7": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 139,
          column: 48
        }
      },
      "8": {
        start: {
          line: 142,
          column: 6
        },
        end: {
          line: 142,
          column: 40
        }
      },
      "9": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 37
        }
      },
      "10": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 37
        }
      },
      "11": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 46
        }
      },
      "12": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 151,
          column: 63
        }
      },
      "13": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 153,
          column: 70
        }
      },
      "14": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 154,
          column: 18
        }
      },
      "15": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 164,
          column: 5
        }
      },
      "16": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 31
        }
      },
      "17": {
        start: {
          line: 166,
          column: 16
        },
        end: {
          line: 166,
          column: 35
        }
      },
      "18": {
        start: {
          line: 167,
          column: 16
        },
        end: {
          line: 167,
          column: 26
        }
      },
      "19": {
        start: {
          line: 168,
          column: 24
        },
        end: {
          line: 168,
          column: 63
        }
      },
      "20": {
        start: {
          line: 170,
          column: 16
        },
        end: {
          line: 170,
          column: 44
        }
      },
      "21": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 183,
          column: 5
        }
      },
      "22": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 180,
          column: 8
        }
      },
      "23": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 42
        }
      },
      "24": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 31
        }
      },
      "25": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 185,
          column: 18
        }
      },
      "26": {
        start: {
          line: 187,
          column: 4
        },
        end: {
          line: 203,
          column: 5
        }
      },
      "27": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 27
        }
      },
      "28": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 41
        }
      },
      "29": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 199,
          column: 9
        }
      },
      "30": {
        start: {
          line: 201,
          column: 25
        },
        end: {
          line: 201,
          column: 104
        }
      },
      "31": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 202,
          column: 44
        }
      },
      "32": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 205,
          column: 29
        }
      },
      "33": {
        start: {
          line: 212,
          column: 4
        },
        end: {
          line: 214,
          column: 5
        }
      },
      "34": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 41
        }
      },
      "35": {
        start: {
          line: 216,
          column: 29
        },
        end: {
          line: 216,
          column: 31
        }
      },
      "36": {
        start: {
          line: 218,
          column: 4
        },
        end: {
          line: 265,
          column: 5
        }
      },
      "37": {
        start: {
          line: 219,
          column: 20
        },
        end: {
          line: 219,
          column: 36
        }
      },
      "38": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 225,
          column: 7
        }
      },
      "39": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 223,
          column: 57
        }
      },
      "40": {
        start: {
          line: 224,
          column: 8
        },
        end: {
          line: 224,
          column: 17
        }
      },
      "41": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 230,
          column: 7
        }
      },
      "42": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 229,
          column: 17
        }
      },
      "43": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 236,
          column: 7
        }
      },
      "44": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 74
        }
      },
      "45": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 235,
          column: 17
        }
      },
      "46": {
        start: {
          line: 239,
          column: 6
        },
        end: {
          line: 249,
          column: 7
        }
      },
      "47": {
        start: {
          line: 240,
          column: 8
        },
        end: {
          line: 242,
          column: 9
        }
      },
      "48": {
        start: {
          line: 241,
          column: 10
        },
        end: {
          line: 241,
          column: 93
        }
      },
      "49": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 245,
          column: 9
        }
      },
      "50": {
        start: {
          line: 244,
          column: 10
        },
        end: {
          line: 244,
          column: 92
        }
      },
      "51": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 248,
          column: 9
        }
      },
      "52": {
        start: {
          line: 247,
          column: 10
        },
        end: {
          line: 247,
          column: 78
        }
      },
      "53": {
        start: {
          line: 252,
          column: 6
        },
        end: {
          line: 254,
          column: 7
        }
      },
      "54": {
        start: {
          line: 253,
          column: 8
        },
        end: {
          line: 253,
          column: 63
        }
      },
      "55": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 259,
          column: 7
        }
      },
      "56": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 258,
          column: 94
        }
      },
      "57": {
        start: {
          line: 262,
          column: 6
        },
        end: {
          line: 264,
          column: 7
        }
      },
      "58": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 263,
          column: 70
        }
      },
      "59": {
        start: {
          line: 267,
          column: 4
        },
        end: {
          line: 269,
          column: 5
        }
      },
      "60": {
        start: {
          line: 268,
          column: 6
        },
        end: {
          line: 268,
          column: 40
        }
      },
      "61": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 271,
          column: 50
        }
      },
      "62": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 280,
          column: 5
        }
      },
      "63": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 19
        }
      },
      "64": {
        start: {
          line: 282,
          column: 4
        },
        end: {
          line: 286,
          column: 14
        }
      },
      "65": {
        start: {
          line: 293,
          column: 4
        },
        end: {
          line: 295,
          column: 5
        }
      },
      "66": {
        start: {
          line: 294,
          column: 6
        },
        end: {
          line: 294,
          column: 18
        }
      },
      "67": {
        start: {
          line: 297,
          column: 4
        },
        end: {
          line: 306,
          column: 5
        }
      },
      "68": {
        start: {
          line: 300,
          column: 24
        },
        end: {
          line: 300,
          column: 60
        }
      },
      "69": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 42
        }
      },
      "70": {
        start: {
          line: 302,
          column: 6
        },
        end: {
          line: 302,
          column: 23
        }
      },
      "71": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 304,
          column: 49
        }
      },
      "72": {
        start: {
          line: 305,
          column: 6
        },
        end: {
          line: 305,
          column: 48
        }
      },
      "73": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 315,
          column: 5
        }
      },
      "74": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 314,
          column: 27
        }
      },
      "75": {
        start: {
          line: 317,
          column: 4
        },
        end: {
          line: 325,
          column: 5
        }
      },
      "76": {
        start: {
          line: 319,
          column: 24
        },
        end: {
          line: 319,
          column: 71
        }
      },
      "77": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 320,
          column: 42
        }
      },
      "78": {
        start: {
          line: 321,
          column: 6
        },
        end: {
          line: 321,
          column: 23
        }
      },
      "79": {
        start: {
          line: 323,
          column: 6
        },
        end: {
          line: 323,
          column: 49
        }
      },
      "80": {
        start: {
          line: 324,
          column: 6
        },
        end: {
          line: 324,
          column: 48
        }
      },
      "81": {
        start: {
          line: 332,
          column: 4
        },
        end: {
          line: 334,
          column: 5
        }
      },
      "82": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 333,
          column: 13
        }
      },
      "83": {
        start: {
          line: 336,
          column: 4
        },
        end: {
          line: 360,
          column: 5
        }
      },
      "84": {
        start: {
          line: 337,
          column: 38
        },
        end: {
          line: 341,
          column: 7
        }
      },
      "85": {
        start: {
          line: 344,
          column: 6
        },
        end: {
          line: 346,
          column: 9
        }
      },
      "86": {
        start: {
          line: 348,
          column: 6
        },
        end: {
          line: 348,
          column: 40
        }
      },
      "87": {
        start: {
          line: 351,
          column: 6
        },
        end: {
          line: 357,
          column: 7
        }
      },
      "88": {
        start: {
          line: 352,
          column: 8
        },
        end: {
          line: 352,
          column: 51
        }
      },
      "89": {
        start: {
          line: 353,
          column: 13
        },
        end: {
          line: 357,
          column: 7
        }
      },
      "90": {
        start: {
          line: 354,
          column: 8
        },
        end: {
          line: 354,
          column: 50
        }
      },
      "91": {
        start: {
          line: 356,
          column: 8
        },
        end: {
          line: 356,
          column: 49
        }
      },
      "92": {
        start: {
          line: 359,
          column: 6
        },
        end: {
          line: 359,
          column: 57
        }
      },
      "93": {
        start: {
          line: 367,
          column: 4
        },
        end: {
          line: 369,
          column: 5
        }
      },
      "94": {
        start: {
          line: 368,
          column: 6
        },
        end: {
          line: 368,
          column: 29
        }
      },
      "95": {
        start: {
          line: 372,
          column: 4
        },
        end: {
          line: 374,
          column: 5
        }
      },
      "96": {
        start: {
          line: 373,
          column: 6
        },
        end: {
          line: 373,
          column: 79
        }
      },
      "97": {
        start: {
          line: 377,
          column: 22
        },
        end: {
          line: 377,
          column: 63
        }
      },
      "98": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 380,
          column: 5
        }
      },
      "99": {
        start: {
          line: 379,
          column: 6
        },
        end: {
          line: 379,
          column: 62
        }
      },
      "100": {
        start: {
          line: 383,
          column: 29
        },
        end: {
          line: 389,
          column: 5
        }
      },
      "101": {
        start: {
          line: 391,
          column: 29
        },
        end: {
          line: 391,
          column: 89
        }
      },
      "102": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 394,
          column: 5
        }
      },
      "103": {
        start: {
          line: 393,
          column: 6
        },
        end: {
          line: 393,
          column: 59
        }
      },
      "104": {
        start: {
          line: 396,
          column: 4
        },
        end: {
          line: 396,
          column: 27
        }
      },
      "105": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 405,
          column: 5
        }
      },
      "106": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 404,
          column: 16
        }
      },
      "107": {
        start: {
          line: 407,
          column: 44
        },
        end: {
          line: 411,
          column: 5
        }
      },
      "108": {
        start: {
          line: 413,
          column: 4
        },
        end: {
          line: 416,
          column: 5
        }
      },
      "109": {
        start: {
          line: 414,
          column: 6
        },
        end: {
          line: 414,
          column: 54
        }
      },
      "110": {
        start: {
          line: 415,
          column: 6
        },
        end: {
          line: 415,
          column: 59
        }
      },
      "111": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 418,
          column: 19
        }
      },
      "112": {
        start: {
          line: 425,
          column: 4
        },
        end: {
          line: 425,
          column: 31
        }
      },
      "113": {
        start: {
          line: 436,
          column: 29
        },
        end: {
          line: 436,
          column: 31
        }
      },
      "114": {
        start: {
          line: 437,
          column: 38
        },
        end: {
          line: 437,
          column: 40
        }
      },
      "115": {
        start: {
          line: 440,
          column: 4
        },
        end: {
          line: 443,
          column: 5
        }
      },
      "116": {
        start: {
          line: 441,
          column: 6
        },
        end: {
          line: 441,
          column: 70
        }
      },
      "117": {
        start: {
          line: 442,
          column: 6
        },
        end: {
          line: 442,
          column: 99
        }
      },
      "118": {
        start: {
          line: 446,
          column: 4
        },
        end: {
          line: 449,
          column: 5
        }
      },
      "119": {
        start: {
          line: 447,
          column: 6
        },
        end: {
          line: 447,
          column: 62
        }
      },
      "120": {
        start: {
          line: 448,
          column: 6
        },
        end: {
          line: 448,
          column: 79
        }
      },
      "121": {
        start: {
          line: 452,
          column: 19
        },
        end: {
          line: 452,
          column: 51
        }
      },
      "122": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 456,
          column: 5
        }
      },
      "123": {
        start: {
          line: 454,
          column: 6
        },
        end: {
          line: 454,
          column: 54
        }
      },
      "124": {
        start: {
          line: 455,
          column: 6
        },
        end: {
          line: 455,
          column: 81
        }
      },
      "125": {
        start: {
          line: 459,
          column: 4
        },
        end: {
          line: 459,
          column: 40
        }
      },
      "126": {
        start: {
          line: 461,
          column: 52
        },
        end: {
          line: 461,
          column: 60
        }
      },
      "127": {
        start: {
          line: 462,
          column: 4
        },
        end: {
          line: 464,
          column: 5
        }
      },
      "128": {
        start: {
          line: 463,
          column: 6
        },
        end: {
          line: 463,
          column: 58
        }
      },
      "129": {
        start: {
          line: 466,
          column: 4
        },
        end: {
          line: 466,
          column: 61
        }
      },
      "130": {
        start: {
          line: 468,
          column: 4
        },
        end: {
          line: 468,
          column: 47
        }
      },
      "131": {
        start: {
          line: 476,
          column: 16
        },
        end: {
          line: 476,
          column: 26
        }
      },
      "132": {
        start: {
          line: 477,
          column: 4
        },
        end: {
          line: 481,
          column: 5
        }
      },
      "133": {
        start: {
          line: 478,
          column: 6
        },
        end: {
          line: 480,
          column: 7
        }
      },
      "134": {
        start: {
          line: 479,
          column: 8
        },
        end: {
          line: 479,
          column: 40
        }
      },
      "135": {
        start: {
          line: 484,
          column: 4
        },
        end: {
          line: 493,
          column: 5
        }
      },
      "136": {
        start: {
          line: 485,
          column: 25
        },
        end: {
          line: 485,
          column: 35
        }
      },
      "137": {
        start: {
          line: 486,
          column: 6
        },
        end: {
          line: 486,
          column: 88
        }
      },
      "138": {
        start: {
          line: 488,
          column: 6
        },
        end: {
          line: 492,
          column: 9
        }
      },
      "139": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 514,
          column: 5
        }
      },
      "140": {
        start: {
          line: 501,
          column: 8
        },
        end: {
          line: 501,
          column: 41
        }
      },
      "141": {
        start: {
          line: 503,
          column: 8
        },
        end: {
          line: 503,
          column: 58
        }
      },
      "142": {
        start: {
          line: 505,
          column: 8
        },
        end: {
          line: 505,
          column: 85
        }
      },
      "143": {
        start: {
          line: 507,
          column: 8
        },
        end: {
          line: 507,
          column: 73
        }
      },
      "144": {
        start: {
          line: 509,
          column: 8
        },
        end: {
          line: 509,
          column: 122
        }
      },
      "145": {
        start: {
          line: 511,
          column: 8
        },
        end: {
          line: 511,
          column: 41
        }
      },
      "146": {
        start: {
          line: 513,
          column: 8
        },
        end: {
          line: 513,
          column: 20
        }
      },
      "147": {
        start: {
          line: 519,
          column: 4
        },
        end: {
          line: 519,
          column: 52
        }
      },
      "148": {
        start: {
          line: 524,
          column: 4
        },
        end: {
          line: 524,
          column: 69
        }
      },
      "149": {
        start: {
          line: 529,
          column: 4
        },
        end: {
          line: 529,
          column: 41
        }
      },
      "150": {
        start: {
          line: 534,
          column: 4
        },
        end: {
          line: 536,
          column: 14
        }
      },
      "151": {
        start: {
          line: 535,
          column: 6
        },
        end: {
          line: 535,
          column: 61
        }
      },
      "152": {
        start: {
          line: 541,
          column: 4
        },
        end: {
          line: 543,
          column: 15
        }
      },
      "153": {
        start: {
          line: 542,
          column: 6
        },
        end: {
          line: 542,
          column: 42
        }
      },
      "154": {
        start: {
          line: 548,
          column: 4
        },
        end: {
          line: 548,
          column: 14
        }
      },
      "155": {
        start: {
          line: 553,
          column: 4
        },
        end: {
          line: 553,
          column: 47
        }
      },
      "156": {
        start: {
          line: 557,
          column: 4
        },
        end: {
          line: 557,
          column: 70
        }
      },
      "157": {
        start: {
          line: 562,
          column: 31
        },
        end: {
          line: 562,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 93,
            column: 2
          },
          end: {
            line: 93,
            column: 3
          }
        },
        loc: {
          start: {
            line: 93,
            column: 52
          },
          end: {
            line: 132,
            column: 3
          }
        },
        line: 93
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 137,
            column: 2
          },
          end: {
            line: 137,
            column: 3
          }
        },
        loc: {
          start: {
            line: 137,
            column: 36
          },
          end: {
            line: 156,
            column: 3
          }
        },
        line: 137
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 161,
            column: 2
          },
          end: {
            line: 161,
            column: 3
          }
        },
        loc: {
          start: {
            line: 161,
            column: 90
          },
          end: {
            line: 206,
            column: 3
          }
        },
        line: 161
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 211,
            column: 2
          },
          end: {
            line: 211,
            column: 3
          }
        },
        loc: {
          start: {
            line: 211,
            column: 106
          },
          end: {
            line: 272,
            column: 3
          }
        },
        line: 211
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 277,
            column: 2
          },
          end: {
            line: 277,
            column: 3
          }
        },
        loc: {
          start: {
            line: 277,
            column: 39
          },
          end: {
            line: 287,
            column: 3
          }
        },
        line: 277
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 292,
            column: 2
          },
          end: {
            line: 292,
            column: 3
          }
        },
        loc: {
          start: {
            line: 292,
            column: 51
          },
          end: {
            line: 307,
            column: 3
          }
        },
        line: 292
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 312,
            column: 2
          },
          end: {
            line: 312,
            column: 3
          }
        },
        loc: {
          start: {
            line: 312,
            column: 60
          },
          end: {
            line: 326,
            column: 3
          }
        },
        line: 312
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 331,
            column: 2
          },
          end: {
            line: 331,
            column: 3
          }
        },
        loc: {
          start: {
            line: 331,
            column: 85
          },
          end: {
            line: 361,
            column: 3
          }
        },
        line: 331
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 366,
            column: 2
          },
          end: {
            line: 366,
            column: 3
          }
        },
        loc: {
          start: {
            line: 366,
            column: 109
          },
          end: {
            line: 397,
            column: 3
          }
        },
        line: 366
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 402,
            column: 2
          },
          end: {
            line: 402,
            column: 3
          }
        },
        loc: {
          start: {
            line: 402,
            column: 58
          },
          end: {
            line: 419,
            column: 3
          }
        },
        line: 402
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 424,
            column: 2
          },
          end: {
            line: 424,
            column: 3
          }
        },
        loc: {
          start: {
            line: 424,
            column: 40
          },
          end: {
            line: 426,
            column: 3
          }
        },
        line: 424
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 431,
            column: 2
          },
          end: {
            line: 431,
            column: 3
          }
        },
        loc: {
          start: {
            line: 435,
            column: 5
          },
          end: {
            line: 469,
            column: 3
          }
        },
        line: 435
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 474,
            column: 2
          },
          end: {
            line: 474,
            column: 3
          }
        },
        loc: {
          start: {
            line: 474,
            column: 33
          },
          end: {
            line: 494,
            column: 3
          }
        },
        line: 474
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 498,
            column: 2
          },
          end: {
            line: 498,
            column: 3
          }
        },
        loc: {
          start: {
            line: 498,
            column: 58
          },
          end: {
            line: 515,
            column: 3
          }
        },
        line: 498
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 517,
            column: 2
          },
          end: {
            line: 517,
            column: 3
          }
        },
        loc: {
          start: {
            line: 517,
            column: 54
          },
          end: {
            line: 520,
            column: 3
          }
        },
        line: 517
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 522,
            column: 2
          },
          end: {
            line: 522,
            column: 3
          }
        },
        loc: {
          start: {
            line: 522,
            column: 39
          },
          end: {
            line: 525,
            column: 3
          }
        },
        line: 522
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 527,
            column: 2
          },
          end: {
            line: 527,
            column: 3
          }
        },
        loc: {
          start: {
            line: 527,
            column: 51
          },
          end: {
            line: 530,
            column: 3
          }
        },
        line: 527
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 532,
            column: 2
          },
          end: {
            line: 532,
            column: 3
          }
        },
        loc: {
          start: {
            line: 532,
            column: 42
          },
          end: {
            line: 537,
            column: 3
          }
        },
        line: 532
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 534,
            column: 16
          },
          end: {
            line: 534,
            column: 17
          }
        },
        loc: {
          start: {
            line: 534,
            column: 22
          },
          end: {
            line: 536,
            column: 5
          }
        },
        line: 534
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 539,
            column: 2
          },
          end: {
            line: 539,
            column: 3
          }
        },
        loc: {
          start: {
            line: 539,
            column: 36
          },
          end: {
            line: 544,
            column: 3
          }
        },
        line: 539
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 541,
            column: 16
          },
          end: {
            line: 541,
            column: 17
          }
        },
        loc: {
          start: {
            line: 541,
            column: 22
          },
          end: {
            line: 543,
            column: 5
          }
        },
        line: 541
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 546,
            column: 2
          },
          end: {
            line: 546,
            column: 3
          }
        },
        loc: {
          start: {
            line: 546,
            column: 55
          },
          end: {
            line: 549,
            column: 3
          }
        },
        line: 546
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 551,
            column: 2
          },
          end: {
            line: 551,
            column: 3
          }
        },
        loc: {
          start: {
            line: 551,
            column: 56
          },
          end: {
            line: 554,
            column: 3
          }
        },
        line: 551
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 556,
            column: 2
          },
          end: {
            line: 556,
            column: 3
          }
        },
        loc: {
          start: {
            line: 556,
            column: 31
          },
          end: {
            line: 558,
            column: 3
          }
        },
        line: 556
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 93,
            column: 14
          },
          end: {
            line: 93,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 93,
            column: 48
          },
          end: {
            line: 93,
            column: 50
          }
        }],
        line: 93
      },
      "1": {
        loc: {
          start: {
            line: 162,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 162,
            column: 4
          },
          end: {
            line: 164,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 162
      },
      "2": {
        loc: {
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 183,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 183,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "3": {
        loc: {
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 172,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 172,
            column: 14
          }
        }, {
          start: {
            line: 172,
            column: 18
          },
          end: {
            line: 172,
            column: 49
          }
        }],
        line: 172
      },
      "4": {
        loc: {
          start: {
            line: 187,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 187,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 187
      },
      "5": {
        loc: {
          start: {
            line: 212,
            column: 4
          },
          end: {
            line: 214,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 212,
            column: 4
          },
          end: {
            line: 214,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 212
      },
      "6": {
        loc: {
          start: {
            line: 222,
            column: 6
          },
          end: {
            line: 225,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 6
          },
          end: {
            line: 225,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "7": {
        loc: {
          start: {
            line: 222,
            column: 10
          },
          end: {
            line: 222,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 222,
            column: 10
          },
          end: {
            line: 222,
            column: 23
          }
        }, {
          start: {
            line: 222,
            column: 28
          },
          end: {
            line: 222,
            column: 47
          }
        }, {
          start: {
            line: 222,
            column: 51
          },
          end: {
            line: 222,
            column: 65
          }
        }, {
          start: {
            line: 222,
            column: 69
          },
          end: {
            line: 222,
            column: 81
          }
        }],
        line: 222
      },
      "8": {
        loc: {
          start: {
            line: 228,
            column: 6
          },
          end: {
            line: 230,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 6
          },
          end: {
            line: 230,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "9": {
        loc: {
          start: {
            line: 228,
            column: 10
          },
          end: {
            line: 228,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 228,
            column: 10
          },
          end: {
            line: 228,
            column: 24
          }
        }, {
          start: {
            line: 228,
            column: 29
          },
          end: {
            line: 228,
            column: 48
          }
        }, {
          start: {
            line: 228,
            column: 52
          },
          end: {
            line: 228,
            column: 66
          }
        }, {
          start: {
            line: 228,
            column: 70
          },
          end: {
            line: 228,
            column: 82
          }
        }],
        line: 228
      },
      "10": {
        loc: {
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 236,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 236,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "11": {
        loc: {
          start: {
            line: 239,
            column: 6
          },
          end: {
            line: 249,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 239,
            column: 6
          },
          end: {
            line: 249,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 239
      },
      "12": {
        loc: {
          start: {
            line: 239,
            column: 10
          },
          end: {
            line: 239,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 239,
            column: 10
          },
          end: {
            line: 239,
            column: 32
          }
        }, {
          start: {
            line: 239,
            column: 36
          },
          end: {
            line: 239,
            column: 61
          }
        }],
        line: 239
      },
      "13": {
        loc: {
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 242,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 8
          },
          end: {
            line: 242,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "14": {
        loc: {
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 240,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 240,
            column: 12
          },
          end: {
            line: 240,
            column: 26
          }
        }, {
          start: {
            line: 240,
            column: 30
          },
          end: {
            line: 240,
            column: 59
          }
        }],
        line: 240
      },
      "15": {
        loc: {
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 245,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 245,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "16": {
        loc: {
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 243,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 12
          },
          end: {
            line: 243,
            column: 26
          }
        }, {
          start: {
            line: 243,
            column: 30
          },
          end: {
            line: 243,
            column: 59
          }
        }],
        line: 243
      },
      "17": {
        loc: {
          start: {
            line: 246,
            column: 8
          },
          end: {
            line: 248,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 8
          },
          end: {
            line: 248,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "18": {
        loc: {
          start: {
            line: 252,
            column: 6
          },
          end: {
            line: 254,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 252,
            column: 6
          },
          end: {
            line: 254,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 252
      },
      "19": {
        loc: {
          start: {
            line: 252,
            column: 10
          },
          end: {
            line: 252,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 10
          },
          end: {
            line: 252,
            column: 22
          }
        }, {
          start: {
            line: 252,
            column: 26
          },
          end: {
            line: 252,
            column: 51
          }
        }, {
          start: {
            line: 252,
            column: 55
          },
          end: {
            line: 252,
            column: 80
          }
        }],
        line: 252
      },
      "20": {
        loc: {
          start: {
            line: 257,
            column: 6
          },
          end: {
            line: 259,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 6
          },
          end: {
            line: 259,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "21": {
        loc: {
          start: {
            line: 257,
            column: 10
          },
          end: {
            line: 257,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 257,
            column: 10
          },
          end: {
            line: 257,
            column: 28
          }
        }, {
          start: {
            line: 257,
            column: 32
          },
          end: {
            line: 257,
            column: 67
          }
        }],
        line: 257
      },
      "22": {
        loc: {
          start: {
            line: 262,
            column: 6
          },
          end: {
            line: 264,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 262,
            column: 6
          },
          end: {
            line: 264,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 262
      },
      "23": {
        loc: {
          start: {
            line: 262,
            column: 10
          },
          end: {
            line: 262,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 262,
            column: 10
          },
          end: {
            line: 262,
            column: 30
          }
        }, {
          start: {
            line: 262,
            column: 34
          },
          end: {
            line: 262,
            column: 62
          }
        }],
        line: 262
      },
      "24": {
        loc: {
          start: {
            line: 267,
            column: 4
          },
          end: {
            line: 269,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 267,
            column: 4
          },
          end: {
            line: 269,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 267
      },
      "25": {
        loc: {
          start: {
            line: 278,
            column: 4
          },
          end: {
            line: 280,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 278,
            column: 4
          },
          end: {
            line: 280,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 278
      },
      "26": {
        loc: {
          start: {
            line: 293,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 293
      },
      "27": {
        loc: {
          start: {
            line: 313,
            column: 4
          },
          end: {
            line: 315,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 313,
            column: 4
          },
          end: {
            line: 315,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 313
      },
      "28": {
        loc: {
          start: {
            line: 332,
            column: 4
          },
          end: {
            line: 334,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 4
          },
          end: {
            line: 334,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "29": {
        loc: {
          start: {
            line: 351,
            column: 6
          },
          end: {
            line: 357,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 351,
            column: 6
          },
          end: {
            line: 357,
            column: 7
          }
        }, {
          start: {
            line: 353,
            column: 13
          },
          end: {
            line: 357,
            column: 7
          }
        }],
        line: 351
      },
      "30": {
        loc: {
          start: {
            line: 353,
            column: 13
          },
          end: {
            line: 357,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 353,
            column: 13
          },
          end: {
            line: 357,
            column: 7
          }
        }, {
          start: {
            line: 355,
            column: 13
          },
          end: {
            line: 357,
            column: 7
          }
        }],
        line: 353
      },
      "31": {
        loc: {
          start: {
            line: 367,
            column: 4
          },
          end: {
            line: 369,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 367,
            column: 4
          },
          end: {
            line: 369,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 367
      },
      "32": {
        loc: {
          start: {
            line: 372,
            column: 4
          },
          end: {
            line: 374,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 372,
            column: 4
          },
          end: {
            line: 374,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 372
      },
      "33": {
        loc: {
          start: {
            line: 378,
            column: 4
          },
          end: {
            line: 380,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 378,
            column: 4
          },
          end: {
            line: 380,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 378
      },
      "34": {
        loc: {
          start: {
            line: 378,
            column: 8
          },
          end: {
            line: 378,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 378,
            column: 8
          },
          end: {
            line: 378,
            column: 18
          }
        }, {
          start: {
            line: 378,
            column: 22
          },
          end: {
            line: 378,
            column: 87
          }
        }],
        line: 378
      },
      "35": {
        loc: {
          start: {
            line: 392,
            column: 4
          },
          end: {
            line: 394,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 392,
            column: 4
          },
          end: {
            line: 394,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 392
      },
      "36": {
        loc: {
          start: {
            line: 392,
            column: 8
          },
          end: {
            line: 392,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 392,
            column: 8
          },
          end: {
            line: 392,
            column: 24
          }
        }, {
          start: {
            line: 392,
            column: 28
          },
          end: {
            line: 392,
            column: 58
          }
        }],
        line: 392
      },
      "37": {
        loc: {
          start: {
            line: 403,
            column: 4
          },
          end: {
            line: 405,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 403,
            column: 4
          },
          end: {
            line: 405,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 403
      },
      "38": {
        loc: {
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 416,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 416,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 413
      },
      "39": {
        loc: {
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 413,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 413,
            column: 8
          },
          end: {
            line: 413,
            column: 14
          }
        }, {
          start: {
            line: 413,
            column: 18
          },
          end: {
            line: 413,
            column: 66
          }
        }],
        line: 413
      },
      "40": {
        loc: {
          start: {
            line: 440,
            column: 4
          },
          end: {
            line: 443,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 440,
            column: 4
          },
          end: {
            line: 443,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 440
      },
      "41": {
        loc: {
          start: {
            line: 446,
            column: 4
          },
          end: {
            line: 449,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 446,
            column: 4
          },
          end: {
            line: 449,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 446
      },
      "42": {
        loc: {
          start: {
            line: 453,
            column: 4
          },
          end: {
            line: 456,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 453,
            column: 4
          },
          end: {
            line: 456,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 453
      },
      "43": {
        loc: {
          start: {
            line: 462,
            column: 4
          },
          end: {
            line: 464,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 462,
            column: 4
          },
          end: {
            line: 464,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 462
      },
      "44": {
        loc: {
          start: {
            line: 463,
            column: 15
          },
          end: {
            line: 463,
            column: 57
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 463,
            column: 35
          },
          end: {
            line: 463,
            column: 45
          }
        }, {
          start: {
            line: 463,
            column: 48
          },
          end: {
            line: 463,
            column: 57
          }
        }],
        line: 463
      },
      "45": {
        loc: {
          start: {
            line: 478,
            column: 6
          },
          end: {
            line: 480,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 478,
            column: 6
          },
          end: {
            line: 480,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 478
      },
      "46": {
        loc: {
          start: {
            line: 484,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 484,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 484
      },
      "47": {
        loc: {
          start: {
            line: 499,
            column: 4
          },
          end: {
            line: 514,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 500,
            column: 6
          },
          end: {
            line: 501,
            column: 41
          }
        }, {
          start: {
            line: 502,
            column: 6
          },
          end: {
            line: 503,
            column: 58
          }
        }, {
          start: {
            line: 504,
            column: 6
          },
          end: {
            line: 505,
            column: 85
          }
        }, {
          start: {
            line: 506,
            column: 6
          },
          end: {
            line: 507,
            column: 73
          }
        }, {
          start: {
            line: 508,
            column: 6
          },
          end: {
            line: 509,
            column: 122
          }
        }, {
          start: {
            line: 510,
            column: 6
          },
          end: {
            line: 511,
            column: 41
          }
        }, {
          start: {
            line: 512,
            column: 6
          },
          end: {
            line: 513,
            column: 20
          }
        }],
        line: 499
      },
      "48": {
        loc: {
          start: {
            line: 503,
            column: 15
          },
          end: {
            line: 503,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 503,
            column: 15
          },
          end: {
            line: 503,
            column: 40
          }
        }, {
          start: {
            line: 503,
            column: 44
          },
          end: {
            line: 503,
            column: 57
          }
        }],
        line: 503
      },
      "49": {
        loc: {
          start: {
            line: 505,
            column: 15
          },
          end: {
            line: 505,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 505,
            column: 15
          },
          end: {
            line: 505,
            column: 40
          }
        }, {
          start: {
            line: 505,
            column: 44
          },
          end: {
            line: 505,
            column: 84
          }
        }],
        line: 505
      },
      "50": {
        loc: {
          start: {
            line: 507,
            column: 15
          },
          end: {
            line: 507,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 507,
            column: 15
          },
          end: {
            line: 507,
            column: 40
          }
        }, {
          start: {
            line: 507,
            column: 44
          },
          end: {
            line: 507,
            column: 72
          }
        }],
        line: 507
      },
      "51": {
        loc: {
          start: {
            line: 509,
            column: 15
          },
          end: {
            line: 509,
            column: 121
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 509,
            column: 15
          },
          end: {
            line: 509,
            column: 40
          }
        }, {
          start: {
            line: 509,
            column: 44
          },
          end: {
            line: 509,
            column: 121
          }
        }],
        line: 509
      },
      "52": {
        loc: {
          start: {
            line: 524,
            column: 25
          },
          end: {
            line: 524,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 524,
            column: 25
          },
          end: {
            line: 524,
            column: 51
          }
        }, {
          start: {
            line: 524,
            column: 55
          },
          end: {
            line: 524,
            column: 68
          }
        }],
        line: 524
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0, 0, 0],
      "8": [0, 0],
      "9": [0, 0, 0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0, 0, 0, 0, 0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ad94c963f7e1107a8835753ba4ce5e724bd1fa0d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_m56dx3rw1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_m56dx3rw1();
import { databaseService } from "../database/DatabaseService";
import { performanceMonitor } from "../../../utils/performance";
var SecurityService = function () {
  function SecurityService() {
    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_m56dx3rw1().b[0][0]++, {});
    _classCallCheck(this, SecurityService);
    this.rateLimitStore = (cov_m56dx3rw1().s[0]++, new Map());
    this.encryptionKey = (cov_m56dx3rw1().s[1]++, '');
    this.metrics = (cov_m56dx3rw1().s[2]++, {
      rateLimitViolations: 0,
      validationFailures: 0,
      auditLogsGenerated: 0,
      encryptionOperations: 0,
      securityIncidents: 0,
      lastSecurityScan: new Date().toISOString()
    });
    cov_m56dx3rw1().f[0]++;
    cov_m56dx3rw1().s[3]++;
    this.config = {
      rateLimiting: Object.assign({
        enabled: true,
        windowMs: 15 * 60 * 1000,
        maxRequests: 100,
        skipSuccessfulRequests: false
      }, config.rateLimiting),
      inputValidation: Object.assign({
        enabled: true,
        maxStringLength: 10000,
        allowedFileTypes: ['jpg', 'jpeg', 'png', 'mp4', 'mov'],
        maxFileSize: 100 * 1024 * 1024
      }, config.inputValidation),
      encryption: Object.assign({
        enabled: true,
        algorithm: 'AES-256-GCM',
        keyRotationDays: 90
      }, config.encryption),
      auditLogging: Object.assign({
        enabled: true,
        logLevel: 'info',
        retentionDays: 365
      }, config.auditLogging),
      cors: Object.assign({
        enabled: true,
        allowedOrigins: ['https://acemind.app', 'https://app.acemind.com'],
        allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
      }, config.cors)
    };
    cov_m56dx3rw1().s[4]++;
    this.initializeEncryption();
    cov_m56dx3rw1().s[5]++;
    this.startCleanupTasks();
  }
  return _createClass(SecurityService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_m56dx3rw1().f[1]++;
        cov_m56dx3rw1().s[6]++;
        try {
          cov_m56dx3rw1().s[7]++;
          performanceMonitor.start('security_init');
          cov_m56dx3rw1().s[8]++;
          yield this.createSecurityTables();
          cov_m56dx3rw1().s[9]++;
          yield this.loadEncryptionKey();
          cov_m56dx3rw1().s[10]++;
          this.startSecurityMonitoring();
          cov_m56dx3rw1().s[11]++;
          performanceMonitor.end('security_init');
          cov_m56dx3rw1().s[12]++;
          console.log('Security service initialized successfully');
        } catch (error) {
          cov_m56dx3rw1().s[13]++;
          console.error('Security service initialization failed:', error);
          cov_m56dx3rw1().s[14]++;
          throw error;
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "checkRateLimit",
    value: function checkRateLimit(ip, endpoint) {
      cov_m56dx3rw1().f[2]++;
      cov_m56dx3rw1().s[15]++;
      if (!this.config.rateLimiting.enabled) {
        cov_m56dx3rw1().b[1][0]++;
        cov_m56dx3rw1().s[16]++;
        return {
          allowed: true
        };
      } else {
        cov_m56dx3rw1().b[1][1]++;
      }
      var key = (cov_m56dx3rw1().s[17]++, `${ip}:${endpoint}`);
      var now = (cov_m56dx3rw1().s[18]++, Date.now());
      var windowStart = (cov_m56dx3rw1().s[19]++, now - this.config.rateLimiting.windowMs);
      var entry = (cov_m56dx3rw1().s[20]++, this.rateLimitStore.get(key));
      cov_m56dx3rw1().s[21]++;
      if ((cov_m56dx3rw1().b[3][0]++, !entry) || (cov_m56dx3rw1().b[3][1]++, entry.windowStart < windowStart)) {
        cov_m56dx3rw1().b[2][0]++;
        cov_m56dx3rw1().s[22]++;
        entry = {
          ip: ip,
          endpoint: endpoint,
          count: 1,
          windowStart: now,
          blocked: false
        };
        cov_m56dx3rw1().s[23]++;
        this.rateLimitStore.set(key, entry);
        cov_m56dx3rw1().s[24]++;
        return {
          allowed: true
        };
      } else {
        cov_m56dx3rw1().b[2][1]++;
      }
      cov_m56dx3rw1().s[25]++;
      entry.count++;
      cov_m56dx3rw1().s[26]++;
      if (entry.count > this.config.rateLimiting.maxRequests) {
        cov_m56dx3rw1().b[4][0]++;
        cov_m56dx3rw1().s[27]++;
        entry.blocked = true;
        cov_m56dx3rw1().s[28]++;
        this.metrics.rateLimitViolations++;
        cov_m56dx3rw1().s[29]++;
        this.logAuditEvent({
          action: 'rate_limit_exceeded',
          resource: endpoint,
          details: {
            ip: ip,
            count: entry.count,
            limit: this.config.rateLimiting.maxRequests
          },
          ipAddress: ip,
          userAgent: '',
          severity: 'warn'
        });
        var retryAfter = (cov_m56dx3rw1().s[30]++, Math.ceil((entry.windowStart + this.config.rateLimiting.windowMs - now) / 1000));
        cov_m56dx3rw1().s[31]++;
        return {
          allowed: false,
          retryAfter: retryAfter
        };
      } else {
        cov_m56dx3rw1().b[4][1]++;
      }
      cov_m56dx3rw1().s[32]++;
      return {
        allowed: true
      };
    }
  }, {
    key: "validateInput",
    value: function validateInput(data, rules) {
      cov_m56dx3rw1().f[3]++;
      cov_m56dx3rw1().s[33]++;
      if (!this.config.inputValidation.enabled) {
        cov_m56dx3rw1().b[5][0]++;
        cov_m56dx3rw1().s[34]++;
        return {
          valid: true,
          errors: []
        };
      } else {
        cov_m56dx3rw1().b[5][1]++;
      }
      var errors = (cov_m56dx3rw1().s[35]++, []);
      cov_m56dx3rw1().s[36]++;
      for (var rule of rules) {
        var value = (cov_m56dx3rw1().s[37]++, data[rule.field]);
        cov_m56dx3rw1().s[38]++;
        if ((cov_m56dx3rw1().b[7][0]++, rule.required) && ((cov_m56dx3rw1().b[7][1]++, value === undefined) || (cov_m56dx3rw1().b[7][2]++, value === null) || (cov_m56dx3rw1().b[7][3]++, value === ''))) {
          cov_m56dx3rw1().b[6][0]++;
          cov_m56dx3rw1().s[39]++;
          errors.push(`Field '${rule.field}' is required`);
          cov_m56dx3rw1().s[40]++;
          continue;
        } else {
          cov_m56dx3rw1().b[6][1]++;
        }
        cov_m56dx3rw1().s[41]++;
        if ((cov_m56dx3rw1().b[9][0]++, !rule.required) && ((cov_m56dx3rw1().b[9][1]++, value === undefined) || (cov_m56dx3rw1().b[9][2]++, value === null) || (cov_m56dx3rw1().b[9][3]++, value === ''))) {
          cov_m56dx3rw1().b[8][0]++;
          cov_m56dx3rw1().s[42]++;
          continue;
        } else {
          cov_m56dx3rw1().b[8][1]++;
        }
        cov_m56dx3rw1().s[43]++;
        if (!this.validateType(value, rule.type)) {
          cov_m56dx3rw1().b[10][0]++;
          cov_m56dx3rw1().s[44]++;
          errors.push(`Field '${rule.field}' must be of type ${rule.type}`);
          cov_m56dx3rw1().s[45]++;
          continue;
        } else {
          cov_m56dx3rw1().b[10][1]++;
        }
        cov_m56dx3rw1().s[46]++;
        if ((cov_m56dx3rw1().b[12][0]++, rule.type === 'string') && (cov_m56dx3rw1().b[12][1]++, typeof value === 'string')) {
          cov_m56dx3rw1().b[11][0]++;
          cov_m56dx3rw1().s[47]++;
          if ((cov_m56dx3rw1().b[14][0]++, rule.minLength) && (cov_m56dx3rw1().b[14][1]++, value.length < rule.minLength)) {
            cov_m56dx3rw1().b[13][0]++;
            cov_m56dx3rw1().s[48]++;
            errors.push(`Field '${rule.field}' must be at least ${rule.minLength} characters`);
          } else {
            cov_m56dx3rw1().b[13][1]++;
          }
          cov_m56dx3rw1().s[49]++;
          if ((cov_m56dx3rw1().b[16][0]++, rule.maxLength) && (cov_m56dx3rw1().b[16][1]++, value.length > rule.maxLength)) {
            cov_m56dx3rw1().b[15][0]++;
            cov_m56dx3rw1().s[50]++;
            errors.push(`Field '${rule.field}' must be at most ${rule.maxLength} characters`);
          } else {
            cov_m56dx3rw1().b[15][1]++;
          }
          cov_m56dx3rw1().s[51]++;
          if (value.length > this.config.inputValidation.maxStringLength) {
            cov_m56dx3rw1().b[17][0]++;
            cov_m56dx3rw1().s[52]++;
            errors.push(`Field '${rule.field}' exceeds maximum allowed length`);
          } else {
            cov_m56dx3rw1().b[17][1]++;
          }
        } else {
          cov_m56dx3rw1().b[11][1]++;
        }
        cov_m56dx3rw1().s[53]++;
        if ((cov_m56dx3rw1().b[19][0]++, rule.pattern) && (cov_m56dx3rw1().b[19][1]++, typeof value === 'string') && (cov_m56dx3rw1().b[19][2]++, !rule.pattern.test(value))) {
          cov_m56dx3rw1().b[18][0]++;
          cov_m56dx3rw1().s[54]++;
          errors.push(`Field '${rule.field}' format is invalid`);
        } else {
          cov_m56dx3rw1().b[18][1]++;
        }
        cov_m56dx3rw1().s[55]++;
        if ((cov_m56dx3rw1().b[21][0]++, rule.allowedValues) && (cov_m56dx3rw1().b[21][1]++, !rule.allowedValues.includes(value))) {
          cov_m56dx3rw1().b[20][0]++;
          cov_m56dx3rw1().s[56]++;
          errors.push(`Field '${rule.field}' must be one of: ${rule.allowedValues.join(', ')}`);
        } else {
          cov_m56dx3rw1().b[20][1]++;
        }
        cov_m56dx3rw1().s[57]++;
        if ((cov_m56dx3rw1().b[23][0]++, rule.customValidator) && (cov_m56dx3rw1().b[23][1]++, !rule.customValidator(value))) {
          cov_m56dx3rw1().b[22][0]++;
          cov_m56dx3rw1().s[58]++;
          errors.push(`Field '${rule.field}' failed custom validation`);
        } else {
          cov_m56dx3rw1().b[22][1]++;
        }
      }
      cov_m56dx3rw1().s[59]++;
      if (errors.length > 0) {
        cov_m56dx3rw1().b[24][0]++;
        cov_m56dx3rw1().s[60]++;
        this.metrics.validationFailures++;
      } else {
        cov_m56dx3rw1().b[24][1]++;
      }
      cov_m56dx3rw1().s[61]++;
      return {
        valid: errors.length === 0,
        errors: errors
      };
    }
  }, {
    key: "sanitizeInput",
    value: function sanitizeInput(input) {
      cov_m56dx3rw1().f[4]++;
      cov_m56dx3rw1().s[62]++;
      if (!this.config.inputValidation.enabled) {
        cov_m56dx3rw1().b[25][0]++;
        cov_m56dx3rw1().s[63]++;
        return input;
      } else {
        cov_m56dx3rw1().b[25][1]++;
      }
      cov_m56dx3rw1().s[64]++;
      return input.replace(/[<>]/g, '').replace(/['"]/g, '').replace(/[;&|`$]/g, '').trim();
    }
  }, {
    key: "encryptData",
    value: (function () {
      var _encryptData = _asyncToGenerator(function* (data) {
        cov_m56dx3rw1().f[5]++;
        cov_m56dx3rw1().s[65]++;
        if (!this.config.encryption.enabled) {
          cov_m56dx3rw1().b[26][0]++;
          cov_m56dx3rw1().s[66]++;
          return data;
        } else {
          cov_m56dx3rw1().b[26][1]++;
        }
        cov_m56dx3rw1().s[67]++;
        try {
          var encrypted = (cov_m56dx3rw1().s[68]++, Buffer.from(data).toString('base64'));
          cov_m56dx3rw1().s[69]++;
          this.metrics.encryptionOperations++;
          cov_m56dx3rw1().s[70]++;
          return encrypted;
        } catch (error) {
          cov_m56dx3rw1().s[71]++;
          console.error('Encryption failed:', error);
          cov_m56dx3rw1().s[72]++;
          throw new Error('Data encryption failed');
        }
      });
      function encryptData(_x) {
        return _encryptData.apply(this, arguments);
      }
      return encryptData;
    }())
  }, {
    key: "decryptData",
    value: (function () {
      var _decryptData = _asyncToGenerator(function* (encryptedData) {
        cov_m56dx3rw1().f[6]++;
        cov_m56dx3rw1().s[73]++;
        if (!this.config.encryption.enabled) {
          cov_m56dx3rw1().b[27][0]++;
          cov_m56dx3rw1().s[74]++;
          return encryptedData;
        } else {
          cov_m56dx3rw1().b[27][1]++;
        }
        cov_m56dx3rw1().s[75]++;
        try {
          var decrypted = (cov_m56dx3rw1().s[76]++, Buffer.from(encryptedData, 'base64').toString());
          cov_m56dx3rw1().s[77]++;
          this.metrics.encryptionOperations++;
          cov_m56dx3rw1().s[78]++;
          return decrypted;
        } catch (error) {
          cov_m56dx3rw1().s[79]++;
          console.error('Decryption failed:', error);
          cov_m56dx3rw1().s[80]++;
          throw new Error('Data decryption failed');
        }
      });
      function decryptData(_x2) {
        return _decryptData.apply(this, arguments);
      }
      return decryptData;
    }())
  }, {
    key: "logAuditEvent",
    value: (function () {
      var _logAuditEvent = _asyncToGenerator(function* (event) {
        cov_m56dx3rw1().f[7]++;
        cov_m56dx3rw1().s[81]++;
        if (!this.config.auditLogging.enabled) {
          cov_m56dx3rw1().b[28][0]++;
          cov_m56dx3rw1().s[82]++;
          return;
        } else {
          cov_m56dx3rw1().b[28][1]++;
        }
        cov_m56dx3rw1().s[83]++;
        try {
          var auditLog = (cov_m56dx3rw1().s[84]++, Object.assign({
            id: this.generateId(),
            timestamp: new Date().toISOString()
          }, event));
          cov_m56dx3rw1().s[85]++;
          yield databaseService.query('audit_logs', 'insert', {
            data: auditLog
          });
          cov_m56dx3rw1().s[86]++;
          this.metrics.auditLogsGenerated++;
          cov_m56dx3rw1().s[87]++;
          if (event.severity === 'error') {
            cov_m56dx3rw1().b[29][0]++;
            cov_m56dx3rw1().s[88]++;
            console.error('Security Audit:', auditLog);
          } else {
            cov_m56dx3rw1().b[29][1]++;
            cov_m56dx3rw1().s[89]++;
            if (event.severity === 'warn') {
              cov_m56dx3rw1().b[30][0]++;
              cov_m56dx3rw1().s[90]++;
              console.warn('Security Audit:', auditLog);
            } else {
              cov_m56dx3rw1().b[30][1]++;
              cov_m56dx3rw1().s[91]++;
              console.log('Security Audit:', auditLog);
            }
          }
        } catch (error) {
          cov_m56dx3rw1().s[92]++;
          console.error('Failed to log audit event:', error);
        }
      });
      function logAuditEvent(_x3) {
        return _logAuditEvent.apply(this, arguments);
      }
      return logAuditEvent;
    }())
  }, {
    key: "validateFileUpload",
    value: function validateFileUpload(file) {
      var _file$name$split$pop;
      cov_m56dx3rw1().f[8]++;
      cov_m56dx3rw1().s[93]++;
      if (!this.config.inputValidation.enabled) {
        cov_m56dx3rw1().b[31][0]++;
        cov_m56dx3rw1().s[94]++;
        return {
          valid: true
        };
      } else {
        cov_m56dx3rw1().b[31][1]++;
      }
      cov_m56dx3rw1().s[95]++;
      if (file.size > this.config.inputValidation.maxFileSize) {
        cov_m56dx3rw1().b[32][0]++;
        cov_m56dx3rw1().s[96]++;
        return {
          valid: false,
          error: 'File size exceeds maximum allowed size'
        };
      } else {
        cov_m56dx3rw1().b[32][1]++;
      }
      var extension = (cov_m56dx3rw1().s[97]++, (_file$name$split$pop = file.name.split('.').pop()) == null ? void 0 : _file$name$split$pop.toLowerCase());
      cov_m56dx3rw1().s[98]++;
      if ((cov_m56dx3rw1().b[34][0]++, !extension) || (cov_m56dx3rw1().b[34][1]++, !this.config.inputValidation.allowedFileTypes.includes(extension))) {
        cov_m56dx3rw1().b[33][0]++;
        cov_m56dx3rw1().s[99]++;
        return {
          valid: false,
          error: 'File type not allowed'
        };
      } else {
        cov_m56dx3rw1().b[33][1]++;
      }
      var allowedMimeTypes = (cov_m56dx3rw1().s[100]++, {
        jpg: 'image/jpeg',
        jpeg: 'image/jpeg',
        png: 'image/png',
        mp4: 'video/mp4',
        mov: 'video/quicktime'
      });
      var expectedMimeType = (cov_m56dx3rw1().s[101]++, allowedMimeTypes[extension]);
      cov_m56dx3rw1().s[102]++;
      if ((cov_m56dx3rw1().b[36][0]++, expectedMimeType) && (cov_m56dx3rw1().b[36][1]++, file.type !== expectedMimeType)) {
        cov_m56dx3rw1().b[35][0]++;
        cov_m56dx3rw1().s[103]++;
        return {
          valid: false,
          error: 'File type mismatch'
        };
      } else {
        cov_m56dx3rw1().b[35][1]++;
      }
      cov_m56dx3rw1().s[104]++;
      return {
        valid: true
      };
    }
  }, {
    key: "getCorsHeaders",
    value: function getCorsHeaders(origin) {
      cov_m56dx3rw1().f[9]++;
      cov_m56dx3rw1().s[105]++;
      if (!this.config.cors.enabled) {
        cov_m56dx3rw1().b[37][0]++;
        cov_m56dx3rw1().s[106]++;
        return {};
      } else {
        cov_m56dx3rw1().b[37][1]++;
      }
      var headers = (cov_m56dx3rw1().s[107]++, {
        'Access-Control-Allow-Methods': this.config.cors.allowedMethods.join(', '),
        'Access-Control-Allow-Headers': this.config.cors.allowedHeaders.join(', '),
        'Access-Control-Max-Age': '86400'
      });
      cov_m56dx3rw1().s[108]++;
      if ((cov_m56dx3rw1().b[39][0]++, origin) && (cov_m56dx3rw1().b[39][1]++, this.config.cors.allowedOrigins.includes(origin))) {
        cov_m56dx3rw1().b[38][0]++;
        cov_m56dx3rw1().s[109]++;
        headers['Access-Control-Allow-Origin'] = origin;
        cov_m56dx3rw1().s[110]++;
        headers['Access-Control-Allow-Credentials'] = 'true';
      } else {
        cov_m56dx3rw1().b[38][1]++;
      }
      cov_m56dx3rw1().s[111]++;
      return headers;
    }
  }, {
    key: "getSecurityMetrics",
    value: function getSecurityMetrics() {
      cov_m56dx3rw1().f[10]++;
      cov_m56dx3rw1().s[112]++;
      return Object.assign({}, this.metrics);
    }
  }, {
    key: "performSecurityHealthCheck",
    value: (function () {
      var _performSecurityHealthCheck = _asyncToGenerator(function* () {
        cov_m56dx3rw1().f[11]++;
        var issues = (cov_m56dx3rw1().s[113]++, []);
        var recommendations = (cov_m56dx3rw1().s[114]++, []);
        cov_m56dx3rw1().s[115]++;
        if (this.metrics.rateLimitViolations > 100) {
          cov_m56dx3rw1().b[40][0]++;
          cov_m56dx3rw1().s[116]++;
          issues.push('High number of rate limiting violations detected');
          cov_m56dx3rw1().s[117]++;
          recommendations.push('Review rate limiting configuration and monitor for potential attacks');
        } else {
          cov_m56dx3rw1().b[40][1]++;
        }
        cov_m56dx3rw1().s[118]++;
        if (this.metrics.validationFailures > 50) {
          cov_m56dx3rw1().b[41][0]++;
          cov_m56dx3rw1().s[119]++;
          issues.push('High number of input validation failures');
          cov_m56dx3rw1().s[120]++;
          recommendations.push('Review input validation rules and user education');
        } else {
          cov_m56dx3rw1().b[41][1]++;
        }
        var keyAge = (cov_m56dx3rw1().s[121]++, yield this.getEncryptionKeyAge());
        cov_m56dx3rw1().s[122]++;
        if (keyAge > this.config.encryption.keyRotationDays) {
          cov_m56dx3rw1().b[42][0]++;
          cov_m56dx3rw1().s[123]++;
          issues.push('Encryption key requires rotation');
          cov_m56dx3rw1().s[124]++;
          recommendations.push('Rotate encryption key according to security policy');
        } else {
          cov_m56dx3rw1().b[42][1]++;
        }
        cov_m56dx3rw1().s[125]++;
        yield this.checkAuditLogRetention();
        var status = (cov_m56dx3rw1().s[126]++, 'secure');
        cov_m56dx3rw1().s[127]++;
        if (issues.length > 0) {
          cov_m56dx3rw1().b[43][0]++;
          cov_m56dx3rw1().s[128]++;
          status = issues.length > 3 ? (cov_m56dx3rw1().b[44][0]++, 'critical') : (cov_m56dx3rw1().b[44][1]++, 'warning');
        } else {
          cov_m56dx3rw1().b[43][1]++;
        }
        cov_m56dx3rw1().s[129]++;
        this.metrics.lastSecurityScan = new Date().toISOString();
        cov_m56dx3rw1().s[130]++;
        return {
          status: status,
          issues: issues,
          recommendations: recommendations
        };
      });
      function performSecurityHealthCheck() {
        return _performSecurityHealthCheck.apply(this, arguments);
      }
      return performSecurityHealthCheck;
    }())
  }, {
    key: "cleanup",
    value: (function () {
      var _cleanup = _asyncToGenerator(function* () {
        cov_m56dx3rw1().f[12]++;
        var now = (cov_m56dx3rw1().s[131]++, Date.now());
        cov_m56dx3rw1().s[132]++;
        for (var _ref of this.rateLimitStore.entries()) {
          var _ref2 = _slicedToArray(_ref, 2);
          var key = _ref2[0];
          var entry = _ref2[1];
          cov_m56dx3rw1().s[133]++;
          if (entry.windowStart < now - this.config.rateLimiting.windowMs) {
            cov_m56dx3rw1().b[45][0]++;
            cov_m56dx3rw1().s[134]++;
            this.rateLimitStore.delete(key);
          } else {
            cov_m56dx3rw1().b[45][1]++;
          }
        }
        cov_m56dx3rw1().s[135]++;
        if (this.config.auditLogging.enabled) {
          cov_m56dx3rw1().b[46][0]++;
          var cutoffDate = (cov_m56dx3rw1().s[136]++, new Date());
          cov_m56dx3rw1().s[137]++;
          cutoffDate.setDate(cutoffDate.getDate() - this.config.auditLogging.retentionDays);
          cov_m56dx3rw1().s[138]++;
          yield databaseService.query('audit_logs', 'delete', {
            filter: {
              timestamp: {
                lt: cutoffDate.toISOString()
              }
            }
          });
        } else {
          cov_m56dx3rw1().b[46][1]++;
        }
      });
      function cleanup() {
        return _cleanup.apply(this, arguments);
      }
      return cleanup;
    }())
  }, {
    key: "validateType",
    value: function validateType(value, type) {
      cov_m56dx3rw1().f[13]++;
      cov_m56dx3rw1().s[139]++;
      switch (type) {
        case 'string':
          cov_m56dx3rw1().b[47][0]++;
          cov_m56dx3rw1().s[140]++;
          return typeof value === 'string';
        case 'number':
          cov_m56dx3rw1().b[47][1]++;
          cov_m56dx3rw1().s[141]++;
          return (cov_m56dx3rw1().b[48][0]++, typeof value === 'number') && (cov_m56dx3rw1().b[48][1]++, !isNaN(value));
        case 'email':
          cov_m56dx3rw1().b[47][2]++;
          cov_m56dx3rw1().s[142]++;
          return (cov_m56dx3rw1().b[49][0]++, typeof value === 'string') && (cov_m56dx3rw1().b[49][1]++, /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value));
        case 'url':
          cov_m56dx3rw1().b[47][3]++;
          cov_m56dx3rw1().s[143]++;
          return (cov_m56dx3rw1().b[50][0]++, typeof value === 'string') && (cov_m56dx3rw1().b[50][1]++, /^https?:\/\/.+/.test(value));
        case 'uuid':
          cov_m56dx3rw1().b[47][4]++;
          cov_m56dx3rw1().s[144]++;
          return (cov_m56dx3rw1().b[51][0]++, typeof value === 'string') && (cov_m56dx3rw1().b[51][1]++, /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value));
        case 'date':
          cov_m56dx3rw1().b[47][5]++;
          cov_m56dx3rw1().s[145]++;
          return !isNaN(Date.parse(value));
        default:
          cov_m56dx3rw1().b[47][6]++;
          cov_m56dx3rw1().s[146]++;
          return true;
      }
    }
  }, {
    key: "createSecurityTables",
    value: function () {
      var _createSecurityTables = _asyncToGenerator(function* () {
        cov_m56dx3rw1().f[14]++;
        cov_m56dx3rw1().s[147]++;
        console.log('Security tables created/verified');
      });
      function createSecurityTables() {
        return _createSecurityTables.apply(this, arguments);
      }
      return createSecurityTables;
    }()
  }, {
    key: "initializeEncryption",
    value: function initializeEncryption() {
      cov_m56dx3rw1().f[15]++;
      cov_m56dx3rw1().s[148]++;
      this.encryptionKey = (cov_m56dx3rw1().b[52][0]++, process.env.ENCRYPTION_KEY) || (cov_m56dx3rw1().b[52][1]++, 'default-key');
    }
  }, {
    key: "loadEncryptionKey",
    value: function () {
      var _loadEncryptionKey = _asyncToGenerator(function* () {
        cov_m56dx3rw1().f[16]++;
        cov_m56dx3rw1().s[149]++;
        console.log('Encryption key loaded');
      });
      function loadEncryptionKey() {
        return _loadEncryptionKey.apply(this, arguments);
      }
      return loadEncryptionKey;
    }()
  }, {
    key: "startSecurityMonitoring",
    value: function startSecurityMonitoring() {
      var _this = this;
      cov_m56dx3rw1().f[17]++;
      cov_m56dx3rw1().s[150]++;
      setInterval(function () {
        cov_m56dx3rw1().f[18]++;
        cov_m56dx3rw1().s[151]++;
        _this.performSecurityHealthCheck().catch(console.error);
      }, 60000);
    }
  }, {
    key: "startCleanupTasks",
    value: function startCleanupTasks() {
      var _this2 = this;
      cov_m56dx3rw1().f[19]++;
      cov_m56dx3rw1().s[152]++;
      setInterval(function () {
        cov_m56dx3rw1().f[20]++;
        cov_m56dx3rw1().s[153]++;
        _this2.cleanup().catch(console.error);
      }, 300000);
    }
  }, {
    key: "getEncryptionKeyAge",
    value: function () {
      var _getEncryptionKeyAge = _asyncToGenerator(function* () {
        cov_m56dx3rw1().f[21]++;
        cov_m56dx3rw1().s[154]++;
        return 30;
      });
      function getEncryptionKeyAge() {
        return _getEncryptionKeyAge.apply(this, arguments);
      }
      return getEncryptionKeyAge;
    }()
  }, {
    key: "checkAuditLogRetention",
    value: function () {
      var _checkAuditLogRetention = _asyncToGenerator(function* () {
        cov_m56dx3rw1().f[22]++;
        cov_m56dx3rw1().s[155]++;
        console.log('Audit log retention checked');
      });
      function checkAuditLogRetention() {
        return _checkAuditLogRetention.apply(this, arguments);
      }
      return checkAuditLogRetention;
    }()
  }, {
    key: "generateId",
    value: function generateId() {
      cov_m56dx3rw1().f[23]++;
      cov_m56dx3rw1().s[156]++;
      return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }]);
}();
export var securityService = (cov_m56dx3rw1().s[157]++, new SecurityService());
export default SecurityService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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