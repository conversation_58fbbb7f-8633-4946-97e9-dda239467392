dba45bf6b864d2bf7e14722fea80db02
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1fyxso30pt() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\coach.tsx";
  var hash = "23ad3694e840152288a4101a5e11edf67c348c6c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\coach.tsx",
    statementMap: {
      "0": {
        start: {
          line: 16,
          column: 15
        },
        end: {
          line: 23,
          column: 1
        }
      },
      "1": {
        start: {
          line: 33,
          column: 34
        },
        end: {
          line: 40,
          column: 4
        }
      },
      "2": {
        start: {
          line: 41,
          column: 36
        },
        end: {
          line: 41,
          column: 48
        }
      },
      "3": {
        start: {
          line: 42,
          column: 34
        },
        end: {
          line: 42,
          column: 49
        }
      },
      "4": {
        start: {
          line: 43,
          column: 24
        },
        end: {
          line: 43,
          column: 48
        }
      },
      "5": {
        start: {
          line: 45,
          column: 28
        },
        end: {
          line: 61,
          column: 3
        }
      },
      "6": {
        start: {
          line: 63,
          column: 2
        },
        end: {
          line: 65,
          column: 17
        }
      },
      "7": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 64,
          column: 21
        }
      },
      "8": {
        start: {
          line: 67,
          column: 25
        },
        end: {
          line: 71,
          column: 3
        }
      },
      "9": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 70,
          column: 12
        }
      },
      "10": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 69,
          column: 61
        }
      },
      "11": {
        start: {
          line: 73,
          column: 22
        },
        end: {
          line: 101,
          column: 3
        }
      },
      "12": {
        start: {
          line: 74,
          column: 24
        },
        end: {
          line: 74,
          column: 48
        }
      },
      "13": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 75,
          column: 29
        }
      },
      "14": {
        start: {
          line: 75,
          column: 22
        },
        end: {
          line: 75,
          column: 29
        }
      },
      "15": {
        start: {
          line: 77,
          column: 33
        },
        end: {
          line: 82,
          column: 5
        }
      },
      "16": {
        start: {
          line: 84,
          column: 4
        },
        end: {
          line: 84,
          column: 48
        }
      },
      "17": {
        start: {
          line: 84,
          column: 24
        },
        end: {
          line: 84,
          column: 46
        }
      },
      "18": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 85,
          column: 21
        }
      },
      "19": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 22
        }
      },
      "20": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 100,
          column: 13
        }
      },
      "21": {
        start: {
          line: 90,
          column: 25
        },
        end: {
          line: 90,
          column: 56
        }
      },
      "22": {
        start: {
          line: 91,
          column: 33
        },
        end: {
          line: 96,
          column: 7
        }
      },
      "23": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 98,
          column: 48
        }
      },
      "24": {
        start: {
          line: 98,
          column: 26
        },
        end: {
          line: 98,
          column: 46
        }
      },
      "25": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 25
        }
      },
      "26": {
        start: {
          line: 103,
          column: 29
        },
        end: {
          line: 119,
          column: 3
        }
      },
      "27": {
        start: {
          line: 104,
          column: 25
        },
        end: {
          line: 104,
          column: 50
        }
      },
      "28": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 108,
          column: 5
        }
      },
      "29": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 497
        }
      },
      "30": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 112,
          column: 5
        }
      },
      "31": {
        start: {
          line: 111,
          column: 6
        },
        end: {
          line: 111,
          column: 454
        }
      },
      "32": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 116,
          column: 5
        }
      },
      "33": {
        start: {
          line: 115,
          column: 6
        },
        end: {
          line: 115,
          column: 418
        }
      },
      "34": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 118,
          column: 443
        }
      },
      "35": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 158,
          column: 3
        }
      },
      "36": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 157,
          column: 11
        }
      },
      "37": {
        start: {
          line: 160,
          column: 2
        },
        end: {
          line: 259,
          column: 4
        }
      },
      "38": {
        start: {
          line: 204,
          column: 38
        },
        end: {
          line: 204,
          column: 53
        }
      },
      "39": {
        start: {
          line: 205,
          column: 16
        },
        end: {
          line: 214,
          column: 18
        }
      },
      "40": {
        start: {
          line: 209,
          column: 35
        },
        end: {
          line: 209,
          column: 63
        }
      },
      "41": {
        start: {
          line: 237,
          column: 16
        },
        end: {
          line: 237,
          column: 50
        }
      },
      "42": {
        start: {
          line: 248,
          column: 27
        },
        end: {
          line: 248,
          column: 40
        }
      },
      "43": {
        start: {
          line: 262,
          column: 15
        },
        end: {
          line: 435,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "CoachScreen",
        decl: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 35
          }
        },
        loc: {
          start: {
            line: 32,
            column: 38
          },
          end: {
            line: 260,
            column: 1
          }
        },
        line: 32
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 63,
            column: 12
          },
          end: {
            line: 63,
            column: 13
          }
        },
        loc: {
          start: {
            line: 63,
            column: 18
          },
          end: {
            line: 65,
            column: 3
          }
        },
        line: 63
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 26
          }
        },
        loc: {
          start: {
            line: 67,
            column: 31
          },
          end: {
            line: 71,
            column: 3
          }
        },
        line: 67
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 68,
            column: 15
          },
          end: {
            line: 68,
            column: 16
          }
        },
        loc: {
          start: {
            line: 68,
            column: 21
          },
          end: {
            line: 70,
            column: 5
          }
        },
        line: 68
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 73,
            column: 22
          },
          end: {
            line: 73,
            column: 23
          }
        },
        loc: {
          start: {
            line: 73,
            column: 47
          },
          end: {
            line: 101,
            column: 3
          }
        },
        line: 73
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 84,
            column: 16
          },
          end: {
            line: 84,
            column: 17
          }
        },
        loc: {
          start: {
            line: 84,
            column: 24
          },
          end: {
            line: 84,
            column: 46
          }
        },
        line: 84
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 89,
            column: 15
          },
          end: {
            line: 89,
            column: 16
          }
        },
        loc: {
          start: {
            line: 89,
            column: 21
          },
          end: {
            line: 100,
            column: 5
          }
        },
        line: 89
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 98,
            column: 18
          },
          end: {
            line: 98,
            column: 19
          }
        },
        loc: {
          start: {
            line: 98,
            column: 26
          },
          end: {
            line: 98,
            column: 46
          }
        },
        line: 98
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 103,
            column: 29
          },
          end: {
            line: 103,
            column: 30
          }
        },
        loc: {
          start: {
            line: 103,
            column: 62
          },
          end: {
            line: 119,
            column: 3
          }
        },
        line: 103
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 121,
            column: 24
          },
          end: {
            line: 121,
            column: 25
          }
        },
        loc: {
          start: {
            line: 122,
            column: 4
          },
          end: {
            line: 157,
            column: 11
          }
        },
        line: 122
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 203,
            column: 37
          },
          end: {
            line: 203,
            column: 38
          }
        },
        loc: {
          start: {
            line: 203,
            column: 53
          },
          end: {
            line: 215,
            column: 15
          }
        },
        line: 203
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 209,
            column: 29
          },
          end: {
            line: 209,
            column: 30
          }
        },
        loc: {
          start: {
            line: 209,
            column: 35
          },
          end: {
            line: 209,
            column: 63
          }
        },
        line: 209
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 234,
            column: 23
          },
          end: {
            line: 234,
            column: 24
          }
        },
        loc: {
          start: {
            line: 234,
            column: 29
          },
          end: {
            line: 238,
            column: 15
          }
        },
        line: 234
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 248,
            column: 21
          },
          end: {
            line: 248,
            column: 22
          }
        },
        loc: {
          start: {
            line: 248,
            column: 27
          },
          end: {
            line: 248,
            column: 40
          }
        },
        line: 248
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 74,
            column: 24
          },
          end: {
            line: 74,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 24
          },
          end: {
            line: 74,
            column: 28
          }
        }, {
          start: {
            line: 74,
            column: 32
          },
          end: {
            line: 74,
            column: 48
          }
        }],
        line: 74
      },
      "1": {
        loc: {
          start: {
            line: 75,
            column: 4
          },
          end: {
            line: 75,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 75,
            column: 4
          },
          end: {
            line: 75,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 75
      },
      "2": {
        loc: {
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 106,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 106
      },
      "3": {
        loc: {
          start: {
            line: 110,
            column: 4
          },
          end: {
            line: 112,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 4
          },
          end: {
            line: 112,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "4": {
        loc: {
          start: {
            line: 114,
            column: 4
          },
          end: {
            line: 116,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 114,
            column: 4
          },
          end: {
            line: 116,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 114
      },
      "5": {
        loc: {
          start: {
            line: 126,
            column: 8
          },
          end: {
            line: 126,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 126,
            column: 25
          },
          end: {
            line: 126,
            column: 43
          }
        }, {
          start: {
            line: 126,
            column: 46
          },
          end: {
            line: 126,
            column: 62
          }
        }],
        line: 126
      },
      "6": {
        loc: {
          start: {
            line: 130,
            column: 9
          },
          end: {
            line: 134,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 131,
            column: 10
          },
          end: {
            line: 131,
            column: 49
          }
        }, {
          start: {
            line: 133,
            column: 10
          },
          end: {
            line: 133,
            column: 50
          }
        }],
        line: 130
      },
      "7": {
        loc: {
          start: {
            line: 139,
            column: 10
          },
          end: {
            line: 139,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 139,
            column: 27
          },
          end: {
            line: 139,
            column: 44
          }
        }, {
          start: {
            line: 139,
            column: 47
          },
          end: {
            line: 139,
            column: 62
          }
        }],
        line: 139
      },
      "8": {
        loc: {
          start: {
            line: 145,
            column: 12
          },
          end: {
            line: 145,
            column: 60
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 145,
            column: 29
          },
          end: {
            line: 145,
            column: 44
          }
        }, {
          start: {
            line: 145,
            column: 47
          },
          end: {
            line: 145,
            column: 60
          }
        }],
        line: 145
      },
      "9": {
        loc: {
          start: {
            line: 164,
            column: 18
          },
          end: {
            line: 164,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 164,
            column: 42
          },
          end: {
            line: 164,
            column: 51
          }
        }, {
          start: {
            line: 164,
            column: 54
          },
          end: {
            line: 164,
            column: 62
          }
        }],
        line: 164
      },
      "10": {
        loc: {
          start: {
            line: 188,
            column: 11
          },
          end: {
            line: 197,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 188,
            column: 11
          },
          end: {
            line: 188,
            column: 19
          }
        }, {
          start: {
            line: 189,
            column: 12
          },
          end: {
            line: 196,
            column: 19
          }
        }],
        line: 188
      },
      "11": {
        loc: {
          start: {
            line: 200,
            column: 11
          },
          end: {
            line: 217,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 200,
            column: 11
          },
          end: {
            line: 200,
            column: 31
          }
        }, {
          start: {
            line: 201,
            column: 12
          },
          end: {
            line: 216,
            column: 19
          }
        }],
        line: 200
      },
      "12": {
        loc: {
          start: {
            line: 246,
            column: 14
          },
          end: {
            line: 246,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 246,
            column: 33
          },
          end: {
            line: 246,
            column: 56
          }
        }, {
          start: {
            line: 246,
            column: 59
          },
          end: {
            line: 246,
            column: 84
          }
        }],
        line: 246
      },
      "13": {
        loc: {
          start: {
            line: 253,
            column: 21
          },
          end: {
            line: 253,
            column: 66
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 253,
            column: 40
          },
          end: {
            line: 253,
            column: 52
          }
        }, {
          start: {
            line: 253,
            column: 55
          },
          end: {
            line: 253,
            column: 66
          }
        }],
        line: 253
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "23ad3694e840152288a4101a5e11edf67c348c6c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1fyxso30pt = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1fyxso30pt();
import React, { useState, useRef, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TextInput, TouchableOpacity, KeyboardAvoidingView, Platform } from 'react-native';
import { Send, Mic, User, Bot, Lightbulb, Target, TrendingUp } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_1fyxso30pt().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
export default function CoachScreen() {
  cov_1fyxso30pt().f[0]++;
  var _ref = (cov_1fyxso30pt().s[1]++, useState([{
      id: '1',
      text: "Hi! I'm your AI tennis coach. I'm here to help you improve your technique, strategy, and mental game. What would you like to work on today?",
      isUser: false,
      timestamp: new Date()
    }])),
    _ref2 = _slicedToArray(_ref, 2),
    messages = _ref2[0],
    setMessages = _ref2[1];
  var _ref3 = (cov_1fyxso30pt().s[2]++, useState('')),
    _ref4 = _slicedToArray(_ref3, 2),
    inputText = _ref4[0],
    setInputText = _ref4[1];
  var _ref5 = (cov_1fyxso30pt().s[3]++, useState(false)),
    _ref6 = _slicedToArray(_ref5, 2),
    isTyping = _ref6[0],
    setIsTyping = _ref6[1];
  var scrollViewRef = (cov_1fyxso30pt().s[4]++, useRef(null));
  var promptSuggestions = (cov_1fyxso30pt().s[5]++, [{
    id: 1,
    text: "How can I improve my serve?",
    icon: Target
  }, {
    id: 2,
    text: "Tennis match strategy tips",
    icon: Lightbulb
  }, {
    id: 3,
    text: "Analyze my forehand technique",
    icon: TrendingUp
  }]);
  cov_1fyxso30pt().s[6]++;
  useEffect(function () {
    cov_1fyxso30pt().f[1]++;
    cov_1fyxso30pt().s[7]++;
    scrollToBottom();
  }, [messages]);
  cov_1fyxso30pt().s[8]++;
  var scrollToBottom = function scrollToBottom() {
    cov_1fyxso30pt().f[2]++;
    cov_1fyxso30pt().s[9]++;
    setTimeout(function () {
      var _scrollViewRef$curren;
      cov_1fyxso30pt().f[3]++;
      cov_1fyxso30pt().s[10]++;
      (_scrollViewRef$curren = scrollViewRef.current) == null || _scrollViewRef$curren.scrollToEnd({
        animated: true
      });
    }, 100);
  };
  cov_1fyxso30pt().s[11]++;
  var sendMessage = function () {
    var _ref7 = _asyncToGenerator(function* (text) {
      cov_1fyxso30pt().f[4]++;
      var messageText = (cov_1fyxso30pt().s[12]++, (cov_1fyxso30pt().b[0][0]++, text) || (cov_1fyxso30pt().b[0][1]++, inputText.trim()));
      cov_1fyxso30pt().s[13]++;
      if (!messageText) {
        cov_1fyxso30pt().b[1][0]++;
        cov_1fyxso30pt().s[14]++;
        return;
      } else {
        cov_1fyxso30pt().b[1][1]++;
      }
      var userMessage = (cov_1fyxso30pt().s[15]++, {
        id: Date.now().toString(),
        text: messageText,
        isUser: true,
        timestamp: new Date()
      });
      cov_1fyxso30pt().s[16]++;
      setMessages(function (prev) {
        cov_1fyxso30pt().f[5]++;
        cov_1fyxso30pt().s[17]++;
        return [].concat(_toConsumableArray(prev), [userMessage]);
      });
      cov_1fyxso30pt().s[18]++;
      setInputText('');
      cov_1fyxso30pt().s[19]++;
      setIsTyping(true);
      cov_1fyxso30pt().s[20]++;
      setTimeout(function () {
        cov_1fyxso30pt().f[6]++;
        var aiResponse = (cov_1fyxso30pt().s[21]++, generateAIResponse(messageText));
        var aiMessage = (cov_1fyxso30pt().s[22]++, {
          id: (Date.now() + 1).toString(),
          text: aiResponse,
          isUser: false,
          timestamp: new Date()
        });
        cov_1fyxso30pt().s[23]++;
        setMessages(function (prev) {
          cov_1fyxso30pt().f[7]++;
          cov_1fyxso30pt().s[24]++;
          return [].concat(_toConsumableArray(prev), [aiMessage]);
        });
        cov_1fyxso30pt().s[25]++;
        setIsTyping(false);
      }, 1500);
    });
    return function sendMessage(_x) {
      return _ref7.apply(this, arguments);
    };
  }();
  cov_1fyxso30pt().s[26]++;
  var generateAIResponse = function generateAIResponse(userMessage) {
    cov_1fyxso30pt().f[8]++;
    var lowerMessage = (cov_1fyxso30pt().s[27]++, userMessage.toLowerCase());
    cov_1fyxso30pt().s[28]++;
    if (lowerMessage.includes('serve')) {
      cov_1fyxso30pt().b[2][0]++;
      cov_1fyxso30pt().s[29]++;
      return "Great question about serving! Here are key tips for a powerful serve:\n\n1. **Toss placement**: Toss the ball slightly in front and to the right (for right-handed players)\n2. **Trophy position**: Get your racquet arm up high with elbow pointing up\n3. **Leg drive**: Use your legs to push up into the ball\n4. **Follow through**: Snap your wrist and finish across your body\n\nPractice these fundamentals 10-15 minutes daily. Would you like specific drills for any of these areas?";
    } else {
      cov_1fyxso30pt().b[2][1]++;
    }
    cov_1fyxso30pt().s[30]++;
    if (lowerMessage.includes('forehand')) {
      cov_1fyxso30pt().b[3][0]++;
      cov_1fyxso30pt().s[31]++;
      return "Let's work on your forehand technique! Key elements:\n\n1. **Preparation**: Turn your shoulders early and take the racquet back\n2. **Contact point**: Hit the ball out in front of your body\n3. **Follow through**: Finish high and across your body for topspin\n4. **Footwork**: Step into the shot with your opposite foot\n\nCommon mistake: hitting the ball too late or too close to your body. Try shadow swings to groove the proper motion!";
    } else {
      cov_1fyxso30pt().b[3][1]++;
    }
    cov_1fyxso30pt().s[32]++;
    if (lowerMessage.includes('strategy')) {
      cov_1fyxso30pt().b[4][0]++;
      cov_1fyxso30pt().s[33]++;
      return "Smart tennis strategy wins matches! Here's my tactical advice:\n\n**Singles Strategy:**\n• Play to your opponent's weaknesses\n• Use cross-court shots to open up the court\n• Approach the net on short balls\n• Stay patient and construct points\n\n**Mental game:**\n• Focus on one point at a time\n• Control what you can control\n• Use positive self-talk\n\nWhat's your biggest challenge during matches?";
    } else {
      cov_1fyxso30pt().b[4][1]++;
    }
    cov_1fyxso30pt().s[34]++;
    return "That's an interesting question! As your AI coach, I'm here to help with all aspects of tennis - technique, strategy, fitness, and mental game. Could you be more specific about what you'd like to improve? I can provide detailed guidance on:\n\n• Stroke technique (forehand, backhand, serve, volley)\n• Match strategy and tactics\n• Physical conditioning\n• Mental toughness\n• Court positioning\n\nWhat would you like to focus on?";
  };
  cov_1fyxso30pt().s[35]++;
  var renderMessage = function renderMessage(message) {
    cov_1fyxso30pt().f[9]++;
    cov_1fyxso30pt().s[36]++;
    return _jsxs(View, {
      style: [styles.messageContainer, message.isUser ? (cov_1fyxso30pt().b[5][0]++, styles.userMessage) : (cov_1fyxso30pt().b[5][1]++, styles.aiMessage)],
      children: [_jsx(View, {
        style: styles.messageAvatar,
        children: message.isUser ? (cov_1fyxso30pt().b[6][0]++, _jsx(User, {
          size: 16,
          color: colors.white
        })) : (cov_1fyxso30pt().b[6][1]++, _jsx(Bot, {
          size: 16,
          color: colors.primary
        }))
      }), _jsxs(View, {
        style: [styles.messageBubble, message.isUser ? (cov_1fyxso30pt().b[7][0]++, styles.userBubble) : (cov_1fyxso30pt().b[7][1]++, styles.aiBubble)],
        children: [_jsx(Text, {
          style: [styles.messageText, message.isUser ? (cov_1fyxso30pt().b[8][0]++, styles.userText) : (cov_1fyxso30pt().b[8][1]++, styles.aiText)],
          children: message.text
        }), _jsx(Text, {
          style: styles.messageTime,
          children: message.timestamp.toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
          })
        })]
      })]
    }, message.id);
  };
  cov_1fyxso30pt().s[37]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(KeyboardAvoidingView, {
      style: styles.keyboardContainer,
      behavior: Platform.OS === 'ios' ? (cov_1fyxso30pt().b[9][0]++, 'padding') : (cov_1fyxso30pt().b[9][1]++, 'height'),
      children: [_jsx(View, {
        style: styles.header,
        children: _jsxs(View, {
          style: styles.coachInfo,
          children: [_jsx(View, {
            style: styles.coachAvatar,
            children: _jsx(Bot, {
              size: 24,
              color: colors.primary
            })
          }), _jsxs(View, {
            children: [_jsx(Text, {
              style: styles.coachName,
              children: "AI Tennis Coach"
            }), _jsx(Text, {
              style: styles.coachStatus,
              children: "Online \u2022 Ready to help"
            })]
          })]
        })
      }), _jsxs(ScrollView, {
        ref: scrollViewRef,
        style: styles.messagesContainer,
        contentContainerStyle: styles.messagesContent,
        showsVerticalScrollIndicator: false,
        children: [messages.map(renderMessage), (cov_1fyxso30pt().b[10][0]++, isTyping) && (cov_1fyxso30pt().b[10][1]++, _jsxs(View, {
          style: [styles.messageContainer, styles.aiMessage],
          children: [_jsx(View, {
            style: styles.messageAvatar,
            children: _jsx(Bot, {
              size: 16,
              color: colors.primary
            })
          }), _jsx(View, {
            style: [styles.messageBubble, styles.aiBubble],
            children: _jsx(Text, {
              style: styles.typingText,
              children: "Coach is typing..."
            })
          })]
        })), (cov_1fyxso30pt().b[11][0]++, messages.length <= 3) && (cov_1fyxso30pt().b[11][1]++, _jsxs(View, {
          style: styles.suggestionsContainer,
          children: [_jsx(Text, {
            style: styles.suggestionsTitle,
            children: "Try asking:"
          }), promptSuggestions.map(function (suggestion) {
            cov_1fyxso30pt().f[10]++;
            var IconComponent = (cov_1fyxso30pt().s[38]++, suggestion.icon);
            cov_1fyxso30pt().s[39]++;
            return _jsxs(TouchableOpacity, {
              style: styles.suggestionCard,
              onPress: function onPress() {
                cov_1fyxso30pt().f[11]++;
                cov_1fyxso30pt().s[40]++;
                return sendMessage(suggestion.text);
              },
              children: [_jsx(IconComponent, {
                size: 16,
                color: colors.primary
              }), _jsx(Text, {
                style: styles.suggestionText,
                children: suggestion.text
              })]
            }, suggestion.id);
          })]
        }))]
      }), _jsxs(View, {
        style: styles.inputContainer,
        children: [_jsxs(View, {
          style: styles.inputRow,
          children: [_jsx(TextInput, {
            style: styles.textInput,
            value: inputText,
            onChangeText: setInputText,
            placeholder: "Ask your tennis coach anything...",
            placeholderTextColor: colors.gray,
            multiline: true,
            maxLength: 500
          }), _jsx(TouchableOpacity, {
            style: styles.micButton,
            onPress: function onPress() {
              cov_1fyxso30pt().f[12]++;
              cov_1fyxso30pt().s[41]++;
              alert('Voice input coming soon!');
            },
            children: _jsx(Mic, {
              size: 20,
              color: colors.gray
            })
          })]
        }), _jsx(TouchableOpacity, {
          style: [styles.sendButton, inputText.trim() ? (cov_1fyxso30pt().b[12][0]++, styles.sendButtonActive) : (cov_1fyxso30pt().b[12][1]++, styles.sendButtonInactive)],
          onPress: function onPress() {
            cov_1fyxso30pt().f[13]++;
            cov_1fyxso30pt().s[42]++;
            return sendMessage();
          },
          disabled: !inputText.trim(),
          children: _jsx(Send, {
            size: 20,
            color: inputText.trim() ? (cov_1fyxso30pt().b[13][0]++, colors.white) : (cov_1fyxso30pt().b[13][1]++, colors.gray)
          })
        })]
      })]
    })
  });
}
var styles = (cov_1fyxso30pt().s[43]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white
  },
  keyboardContainer: {
    flex: 1
  },
  header: {
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  coachInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  coachAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12
  },
  coachName: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  coachStatus: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.primary,
    marginTop: 2
  },
  messagesContainer: {
    flex: 1
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 20
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 16
  },
  userMessage: {
    justifyContent: 'flex-end'
  },
  aiMessage: {
    justifyContent: 'flex-start'
  },
  messageAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 8
  },
  messageBubble: {
    maxWidth: '70%',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 20
  },
  userBubble: {
    backgroundColor: colors.primary,
    borderBottomRightRadius: 4
  },
  aiBubble: {
    backgroundColor: colors.lightGray,
    borderBottomLeftRadius: 4
  },
  messageText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    lineHeight: 22
  },
  userText: {
    color: colors.white
  },
  aiText: {
    color: colors.dark
  },
  messageTime: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    opacity: 0.7,
    marginTop: 4
  },
  typingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    fontStyle: 'italic'
  },
  suggestionsContainer: {
    marginTop: 20,
    paddingHorizontal: 8
  },
  suggestionsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 12
  },
  suggestionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: colors.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.lightGray,
    marginBottom: 8
  },
  suggestionText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.dark,
    marginLeft: 12
  },
  inputContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray,
    backgroundColor: colors.white
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 8
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.lightGray,
    borderRadius: 24,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    maxHeight: 100,
    marginRight: 8
  },
  micButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    alignSelf: 'flex-end'
  },
  sendButtonActive: {
    backgroundColor: colors.primary
  },
  sendButtonInactive: {
    backgroundColor: colors.lightGray
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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