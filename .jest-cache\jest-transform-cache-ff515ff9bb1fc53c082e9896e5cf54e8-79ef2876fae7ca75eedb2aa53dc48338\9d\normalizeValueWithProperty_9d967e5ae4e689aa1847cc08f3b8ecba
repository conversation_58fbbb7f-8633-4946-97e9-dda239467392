727a01ce4c61886538cf862159bb542c
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = normalizeValueWithProperty;
var _unitlessNumbers = _interopRequireDefault(require("./unitlessNumbers"));
var _normalizeColor = _interopRequireDefault(require("./normalizeColor"));
var colorProps = {
  backgroundColor: true,
  borderColor: true,
  borderTopColor: true,
  borderRightColor: true,
  borderBottomColor: true,
  borderLeftColor: true,
  color: true,
  shadowColor: true,
  textDecorationColor: true,
  textShadowColor: true
};
function normalizeValueWithProperty(value, property) {
  var returnValue = value;
  if ((property == null || !_unitlessNumbers.default[property]) && typeof value === 'number') {
    returnValue = value + "px";
  } else if (property != null && colorProps[property]) {
    returnValue = (0, _normalizeColor.default)(value);
  }
  return returnValue;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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