{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_UIManager", "NativeModules", "UIManager", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _UIManager = _interopRequireDefault(require(\"../UIManager\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n// NativeModules shim\nvar NativeModules = {\n  UIManager: _UIManager.default\n};\nvar _default = exports.default = NativeModules;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,UAAU,GAAGL,sBAAsB,CAACC,OAAO,eAAe,CAAC,CAAC;AAWhE,IAAIK,aAAa,GAAG;EAClBC,SAAS,EAAEF,UAAU,CAACH;AACxB,CAAC;AACD,IAAIM,QAAQ,GAAGL,OAAO,CAACD,OAAO,GAAGI,aAAa;AAC9CG,MAAM,CAACN,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}