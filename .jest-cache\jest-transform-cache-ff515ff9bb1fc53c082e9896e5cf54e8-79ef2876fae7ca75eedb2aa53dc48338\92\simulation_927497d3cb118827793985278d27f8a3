971ef9cbfd72702f1759ec3723acf321
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1pml4p74yc() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\simulation.tsx";
  var hash = "cd73e00d3c742f9d95d2ade8c4a7f41a73541216";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\simulation.tsx",
    statementMap: {
      "0": {
        start: {
          line: 17,
          column: 15
        },
        end: {
          line: 26,
          column: 1
        }
      },
      "1": {
        start: {
          line: 36,
          column: 50
        },
        end: {
          line: 36,
          column: 79
        }
      },
      "2": {
        start: {
          line: 37,
          column: 42
        },
        end: {
          line: 37,
          column: 57
        }
      },
      "3": {
        start: {
          line: 40,
          column: 20
        },
        end: {
          line: 57,
          column: 3
        }
      },
      "4": {
        start: {
          line: 59,
          column: 21
        },
        end: {
          line: 67,
          column: 3
        }
      },
      "5": {
        start: {
          line: 70,
          column: 37
        },
        end: {
          line: 90,
          column: 3
        }
      },
      "6": {
        start: {
          line: 92,
          column: 30
        },
        end: {
          line: 114,
          column: 3
        }
      },
      "7": {
        start: {
          line: 116,
          column: 26
        },
        end: {
          line: 141,
          column: 3
        }
      },
      "8": {
        start: {
          line: 143,
          column: 29
        },
        end: {
          line: 154,
          column: 3
        }
      },
      "9": {
        start: {
          line: 156,
          column: 31
        },
        end: {
          line: 164,
          column: 3
        }
      },
      "10": {
        start: {
          line: 157,
          column: 4
        },
        end: {
          line: 157,
          column: 36
        }
      },
      "11": {
        start: {
          line: 158,
          column: 4
        },
        end: {
          line: 158,
          column: 26
        }
      },
      "12": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 163,
          column: 13
        }
      },
      "13": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 29
        }
      },
      "14": {
        start: {
          line: 166,
          column: 25
        },
        end: {
          line: 177,
          column: 3
        }
      },
      "15": {
        start: {
          line: 167,
          column: 4
        },
        end: {
          line: 176,
          column: 6
        }
      },
      "16": {
        start: {
          line: 179,
          column: 29
        },
        end: {
          line: 181,
          column: 3
        }
      },
      "17": {
        start: {
          line: 180,
          column: 4
        },
        end: {
          line: 180,
          column: 83
        }
      },
      "18": {
        start: {
          line: 183,
          column: 29
        },
        end: {
          line: 235,
          column: 3
        }
      },
      "19": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 234,
          column: 11
        }
      },
      "20": {
        start: {
          line: 200,
          column: 10
        },
        end: {
          line: 215,
          column: 12
        }
      },
      "21": {
        start: {
          line: 237,
          column: 2
        },
        end: {
          line: 467,
          column: 4
        }
      },
      "22": {
        start: {
          line: 263,
          column: 18
        },
        end: {
          line: 271,
          column: 25
        }
      },
      "23": {
        start: {
          line: 288,
          column: 18
        },
        end: {
          line: 296,
          column: 25
        }
      },
      "24": {
        start: {
          line: 339,
          column: 34
        },
        end: {
          line: 339,
          column: 49
        }
      },
      "25": {
        start: {
          line: 340,
          column: 12
        },
        end: {
          line: 352,
          column: 14
        }
      },
      "26": {
        start: {
          line: 364,
          column: 12
        },
        end: {
          line: 397,
          column: 31
        }
      },
      "27": {
        start: {
          line: 370,
          column: 29
        },
        end: {
          line: 370,
          column: 62
        }
      },
      "28": {
        start: {
          line: 411,
          column: 14
        },
        end: {
          line: 411,
          column: 79
        }
      },
      "29": {
        start: {
          line: 421,
          column: 14
        },
        end: {
          line: 421,
          column: 75
        }
      },
      "30": {
        start: {
          line: 470,
          column: 15
        },
        end: {
          line: 881,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "MatchSimulationScreen",
        decl: {
          start: {
            line: 35,
            column: 24
          },
          end: {
            line: 35,
            column: 45
          }
        },
        loc: {
          start: {
            line: 35,
            column: 48
          },
          end: {
            line: 468,
            column: 1
          }
        },
        line: 35
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 156,
            column: 31
          },
          end: {
            line: 156,
            column: 32
          }
        },
        loc: {
          start: {
            line: 156,
            column: 55
          },
          end: {
            line: 164,
            column: 3
          }
        },
        line: 156
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 161,
            column: 15
          },
          end: {
            line: 161,
            column: 16
          }
        },
        loc: {
          start: {
            line: 161,
            column: 21
          },
          end: {
            line: 163,
            column: 5
          }
        },
        line: 161
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 166,
            column: 25
          },
          end: {
            line: 166,
            column: 26
          }
        },
        loc: {
          start: {
            line: 166,
            column: 31
          },
          end: {
            line: 177,
            column: 3
          }
        },
        line: 166
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 171,
            column: 49
          },
          end: {
            line: 171,
            column: 50
          }
        },
        loc: {
          start: {
            line: 171,
            column: 55
          },
          end: {
            line: 171,
            column: 57
          }
        },
        line: 171
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 172,
            column: 50
          },
          end: {
            line: 172,
            column: 51
          }
        },
        loc: {
          start: {
            line: 172,
            column: 56
          },
          end: {
            line: 172,
            column: 58
          }
        },
        line: 172
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 173,
            column: 45
          },
          end: {
            line: 173,
            column: 46
          }
        },
        loc: {
          start: {
            line: 173,
            column: 51
          },
          end: {
            line: 173,
            column: 53
          }
        },
        line: 173
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 179,
            column: 29
          },
          end: {
            line: 179,
            column: 30
          }
        },
        loc: {
          start: {
            line: 179,
            column: 35
          },
          end: {
            line: 181,
            column: 3
          }
        },
        line: 179
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 183,
            column: 29
          },
          end: {
            line: 183,
            column: 30
          }
        },
        loc: {
          start: {
            line: 184,
            column: 4
          },
          end: {
            line: 234,
            column: 11
          }
        },
        line: 184
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 199,
            column: 23
          },
          end: {
            line: 199,
            column: 24
          }
        },
        loc: {
          start: {
            line: 200,
            column: 10
          },
          end: {
            line: 215,
            column: 12
          }
        },
        line: 200
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 262,
            column: 43
          },
          end: {
            line: 262,
            column: 44
          }
        },
        loc: {
          start: {
            line: 263,
            column: 18
          },
          end: {
            line: 271,
            column: 25
          }
        },
        line: 263
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 287,
            column: 45
          },
          end: {
            line: 287,
            column: 46
          }
        },
        loc: {
          start: {
            line: 288,
            column: 18
          },
          end: {
            line: 296,
            column: 25
          }
        },
        line: 288
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 338,
            column: 35
          },
          end: {
            line: 338,
            column: 36
          }
        },
        loc: {
          start: {
            line: 338,
            column: 51
          },
          end: {
            line: 353,
            column: 11
          }
        },
        line: 338
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 363,
            column: 31
          },
          end: {
            line: 363,
            column: 32
          }
        },
        loc: {
          start: {
            line: 364,
            column: 12
          },
          end: {
            line: 397,
            column: 31
          }
        },
        line: 364
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 370,
            column: 23
          },
          end: {
            line: 370,
            column: 24
          }
        },
        loc: {
          start: {
            line: 370,
            column: 29
          },
          end: {
            line: 370,
            column: 62
          }
        },
        line: 370
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 410,
            column: 46
          },
          end: {
            line: 410,
            column: 47
          }
        },
        loc: {
          start: {
            line: 411,
            column: 14
          },
          end: {
            line: 411,
            column: 79
          }
        },
        line: 411
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 420,
            column: 48
          },
          end: {
            line: 420,
            column: 49
          }
        },
        loc: {
          start: {
            line: 421,
            column: 14
          },
          end: {
            line: 421,
            column: 75
          }
        },
        line: 421
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 459,
            column: 70
          },
          end: {
            line: 459,
            column: 71
          }
        },
        loc: {
          start: {
            line: 459,
            column: 76
          },
          end: {
            line: 459,
            column: 78
          }
        },
        line: 459
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 208,
            column: 18
          },
          end: {
            line: 210,
            column: 29
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 208,
            column: 47
          },
          end: {
            line: 208,
            column: 61
          }
        }, {
          start: {
            line: 209,
            column: 18
          },
          end: {
            line: 210,
            column: 29
          }
        }],
        line: 208
      },
      "1": {
        loc: {
          start: {
            line: 209,
            column: 18
          },
          end: {
            line: 210,
            column: 29
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 209,
            column: 46
          },
          end: {
            line: 209,
            column: 56
          }
        }, {
          start: {
            line: 210,
            column: 18
          },
          end: {
            line: 210,
            column: 29
          }
        }],
        line: 209
      },
      "2": {
        loc: {
          start: {
            line: 267,
            column: 22
          },
          end: {
            line: 267,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 267,
            column: 22
          },
          end: {
            line: 267,
            column: 58
          }
        }, {
          start: {
            line: 267,
            column: 62
          },
          end: {
            line: 267,
            column: 79
          }
        }],
        line: 267
      },
      "3": {
        loc: {
          start: {
            line: 292,
            column: 22
          },
          end: {
            line: 292,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 22
          },
          end: {
            line: 292,
            column: 56
          }
        }, {
          start: {
            line: 292,
            column: 60
          },
          end: {
            line: 292,
            column: 77
          }
        }],
        line: 292
      },
      "4": {
        loc: {
          start: {
            line: 368,
            column: 16
          },
          end: {
            line: 368,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 368,
            column: 16
          },
          end: {
            line: 368,
            column: 48
          }
        }, {
          start: {
            line: 368,
            column: 52
          },
          end: {
            line: 368,
            column: 75
          }
        }],
        line: 368
      },
      "5": {
        loc: {
          start: {
            line: 374,
            column: 17
          },
          end: {
            line: 388,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 374,
            column: 17
          },
          end: {
            line: 374,
            column: 49
          }
        }, {
          start: {
            line: 375,
            column: 18
          },
          end: {
            line: 387,
            column: 25
          }
        }],
        line: 374
      },
      "6": {
        loc: {
          start: {
            line: 376,
            column: 21
          },
          end: {
            line: 386,
            column: 21
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 377,
            column: 22
          },
          end: {
            line: 380,
            column: 29
          }
        }, {
          start: {
            line: 382,
            column: 22
          },
          end: {
            line: 385,
            column: 29
          }
        }],
        line: 376
      },
      "7": {
        loc: {
          start: {
            line: 391,
            column: 17
          },
          end: {
            line: 395,
            column: 17
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 392,
            column: 18
          },
          end: {
            line: 392,
            column: 66
          }
        }, {
          start: {
            line: 394,
            column: 18
          },
          end: {
            line: 394,
            column: 60
          }
        }],
        line: 391
      },
      "8": {
        loc: {
          start: {
            line: 391,
            column: 17
          },
          end: {
            line: 391,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 391,
            column: 17
          },
          end: {
            line: 391,
            column: 49
          }
        }, {
          start: {
            line: 391,
            column: 53
          },
          end: {
            line: 391,
            column: 66
          }
        }],
        line: 391
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "cd73e00d3c742f9d95d2ade8c4a7f41a73541216"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1pml4p74yc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1pml4p74yc();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity, Image, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Card from "../../components/ui/Card";
import Button from "../../components/ui/Button";
import { Bot, Trophy, Target, TrendingUp, Share2, RotateCcw, Zap, ArrowRight, CircleCheck as CheckCircle, TriangleAlert as AlertTriangle, Play as PlayIcon } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_1pml4p74yc().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  blue: '#3b82f6'
});
export default function MatchSimulationScreen() {
  cov_1pml4p74yc().f[0]++;
  var _ref = (cov_1pml4p74yc().s[1]++, useState(null)),
    _ref2 = _slicedToArray(_ref, 2),
    selectedScenario = _ref2[0],
    setSelectedScenario = _ref2[1];
  var _ref3 = (cov_1pml4p74yc().s[2]++, useState(false)),
    _ref4 = _slicedToArray(_ref3, 2),
    isSimulating = _ref4[0],
    setIsSimulating = _ref4[1];
  var matchData = (cov_1pml4p74yc().s[3]++, {
    player: {
      name: 'Sara Lee',
      avatar: 'https://images.pexels.com/photos/91227/pexels-photo-91227.jpeg?auto=compress&cs=tinysrgb&w=150',
      sets: [6, 3, 6],
      games: 15,
      points: 89
    },
    opponent: {
      name: 'AI: Aggressive Baseliner',
      avatar: 'https://images.pexels.com/photos/6253919/pexels-photo-6253919.jpeg?auto=compress&cs=tinysrgb&w=150',
      sets: [3, 6, 4],
      games: 13,
      points: 82
    },
    duration: '2h 14m',
    surface: 'Hard Court'
  });
  var matchStats = (cov_1pml4p74yc().s[4]++, {
    firstServePercentage: 68,
    acesCount: 7,
    doubleFaults: 4,
    winnersCount: 23,
    unforcedErrors: 18,
    netApproaches: 12,
    breakPointsConverted: '4/7'
  });
  var courtData = (cov_1pml4p74yc().s[5]++, [{
    x: 25,
    y: 20,
    type: 'winner',
    intensity: 0.8
  }, {
    x: 75,
    y: 25,
    type: 'winner',
    intensity: 0.9
  }, {
    x: 30,
    y: 70,
    type: 'winner',
    intensity: 0.7
  }, {
    x: 80,
    y: 65,
    type: 'winner',
    intensity: 0.8
  }, {
    x: 15,
    y: 45,
    type: 'winner',
    intensity: 0.6
  }, {
    x: 85,
    y: 50,
    type: 'winner',
    intensity: 0.7
  }, {
    x: 50,
    y: 15,
    type: 'error',
    intensity: 0.6
  }, {
    x: 90,
    y: 30,
    type: 'error',
    intensity: 0.5
  }, {
    x: 10,
    y: 75,
    type: 'error',
    intensity: 0.7
  }, {
    x: 60,
    y: 85,
    type: 'error',
    intensity: 0.6
  }, {
    x: 35,
    y: 5,
    type: 'serve',
    intensity: 0.8
  }, {
    x: 65,
    y: 5,
    type: 'serve',
    intensity: 0.9
  }, {
    x: 40,
    y: 8,
    type: 'serve',
    intensity: 0.7
  }, {
    x: 60,
    y: 8,
    type: 'serve',
    intensity: 0.8
  }]);
  var tacticalSuggestions = (cov_1pml4p74yc().s[6]++, [{
    id: 1,
    title: 'Attack Opponent\'s Backhand',
    description: 'Target the AI\'s weaker backhand side more frequently',
    impact: '+12% win probability',
    icon: Target
  }, {
    id: 2,
    title: 'Increase Net Approaches',
    description: 'Move forward after deep shots to pressure opponent',
    impact: '+8% point conversion',
    icon: TrendingUp
  }, {
    id: 3,
    title: 'Vary Serve Placement',
    description: 'Mix up serve directions to keep opponent guessing',
    impact: '+15% first serve points',
    icon: Zap
  }]);
  var whatIfScenarios = (cov_1pml4p74yc().s[7]++, [{
    id: 'backhand-returns',
    question: 'What if I play more backhand returns?',
    prediction: 'Win probability: 72% → 78%',
    details: 'Better court positioning, fewer unforced errors'
  }, {
    id: 'serve-speed',
    question: 'What if I increase first serve speed?',
    prediction: 'Ace count: 7 → 11, but double faults: 4 → 7',
    details: 'Higher risk/reward ratio'
  }, {
    id: 'net-play',
    question: 'What if I approach the net more often?',
    prediction: 'Point conversion: 65% → 79%',
    details: 'Pressure opponent into more errors'
  }, {
    id: 'defensive-play',
    question: 'What if I play more defensively?',
    prediction: 'Match duration: +23 minutes, win probability: 68% → 71%',
    details: 'Fewer winners but also fewer errors'
  }]);
  var strengthsAndGrowth = (cov_1pml4p74yc().s[8]++, {
    strengths: ['Strong forehand crosscourt (89% success rate)', 'Excellent court coverage and movement', 'Consistent first serve placement'],
    growthAreas: ['Improve consistency under pressure (3rd set)', 'Develop more variety in backhand shots', 'Better decision-making at the net']
  });
  cov_1pml4p74yc().s[9]++;
  var handleScenarioSelect = function handleScenarioSelect(scenarioId) {
    cov_1pml4p74yc().f[1]++;
    cov_1pml4p74yc().s[10]++;
    setSelectedScenario(scenarioId);
    cov_1pml4p74yc().s[11]++;
    setIsSimulating(true);
    cov_1pml4p74yc().s[12]++;
    setTimeout(function () {
      cov_1pml4p74yc().f[2]++;
      cov_1pml4p74yc().s[13]++;
      setIsSimulating(false);
    }, 2000);
  };
  cov_1pml4p74yc().s[14]++;
  var handleNewMatch = function handleNewMatch() {
    cov_1pml4p74yc().f[3]++;
    cov_1pml4p74yc().s[15]++;
    Alert.alert('New Match Simulation', 'Choose your opponent:', [{
      text: 'Defensive Specialist',
      onPress: function onPress() {
        cov_1pml4p74yc().f[4]++;
      }
    }, {
      text: 'Serve & Volley Expert',
      onPress: function onPress() {
        cov_1pml4p74yc().f[5]++;
      }
    }, {
      text: 'All-Court Player',
      onPress: function onPress() {
        cov_1pml4p74yc().f[6]++;
      }
    }, {
      text: 'Cancel',
      style: 'cancel'
    }]);
  };
  cov_1pml4p74yc().s[16]++;
  var handleShareResults = function handleShareResults() {
    cov_1pml4p74yc().f[7]++;
    cov_1pml4p74yc().s[17]++;
    Alert.alert('Share Results', 'Match results shared to your tennis community!');
  };
  cov_1pml4p74yc().s[18]++;
  var renderCourtHeatmap = function renderCourtHeatmap() {
    cov_1pml4p74yc().f[8]++;
    cov_1pml4p74yc().s[19]++;
    return _jsxs(View, {
      style: styles.courtContainer,
      children: [_jsxs(View, {
        style: styles.court,
        children: [_jsxs(View, {
          style: styles.courtLines,
          children: [_jsx(View, {
            style: styles.baseline
          }), _jsx(View, {
            style: styles.serviceLine
          }), _jsx(View, {
            style: styles.centerLine
          }), _jsx(View, {
            style: styles.sideLines
          })]
        }), _jsx(Text, {
          style: styles.courtLabel,
          children: "Deuce Court"
        }), _jsx(Text, {
          style: [styles.courtLabel, styles.adCourtLabel],
          children: "Ad Court"
        }), courtData.map(function (position, index) {
          cov_1pml4p74yc().f[9]++;
          cov_1pml4p74yc().s[20]++;
          return _jsx(View, {
            style: [styles.shotDot, {
              left: `${position.x}%`,
              top: `${position.y}%`,
              backgroundColor: position.type === 'winner' ? (cov_1pml4p74yc().b[0][0]++, colors.primary) : (cov_1pml4p74yc().b[0][1]++, position.type === 'error' ? (cov_1pml4p74yc().b[1][0]++, colors.red) : (cov_1pml4p74yc().b[1][1]++, colors.blue)),
              opacity: position.intensity,
              transform: [{
                scale: position.intensity
              }]
            }]
          }, index);
        })]
      }), _jsxs(View, {
        style: styles.courtLegend,
        children: [_jsxs(View, {
          style: styles.legendItem,
          children: [_jsx(View, {
            style: [styles.legendDot, {
              backgroundColor: colors.primary
            }]
          }), _jsx(Text, {
            style: styles.legendText,
            children: "Winners"
          })]
        }), _jsxs(View, {
          style: styles.legendItem,
          children: [_jsx(View, {
            style: [styles.legendDot, {
              backgroundColor: colors.red
            }]
          }), _jsx(Text, {
            style: styles.legendText,
            children: "Errors"
          })]
        }), _jsxs(View, {
          style: styles.legendItem,
          children: [_jsx(View, {
            style: [styles.legendDot, {
              backgroundColor: colors.blue
            }]
          }), _jsx(Text, {
            style: styles.legendText,
            children: "Serves"
          })]
        })]
      })]
    });
  };
  cov_1pml4p74yc().s[21]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(ScrollView, {
      style: styles.scrollView,
      showsVerticalScrollIndicator: false,
      children: [_jsxs(View, {
        style: styles.header,
        children: [_jsx(Text, {
          style: styles.title,
          children: "Match Simulation"
        }), _jsx(Text, {
          style: styles.subtitle,
          children: "Digital Twin Analysis"
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.matchCard,
        children: [_jsxs(View, {
          style: styles.matchHeader,
          children: [_jsx(Text, {
            style: styles.matchTitle,
            children: "Latest Match Result"
          }), _jsxs(View, {
            style: styles.matchMeta,
            children: [_jsx(Text, {
              style: styles.matchDuration,
              children: matchData.duration
            }), _jsx(Text, {
              style: styles.matchSurface,
              children: matchData.surface
            })]
          })]
        }), _jsxs(View, {
          style: styles.playersContainer,
          children: [_jsxs(View, {
            style: styles.playerSection,
            children: [_jsx(Image, {
              source: {
                uri: matchData.player.avatar
              },
              style: styles.playerAvatar
            }), _jsx(Text, {
              style: styles.playerName,
              children: matchData.player.name
            }), _jsx(View, {
              style: styles.scoreContainer,
              children: matchData.player.sets.map(function (set, index) {
                cov_1pml4p74yc().f[10]++;
                cov_1pml4p74yc().s[22]++;
                return _jsx(Text, {
                  style: [styles.setScore, (cov_1pml4p74yc().b[2][0]++, set > matchData.opponent.sets[index]) && (cov_1pml4p74yc().b[2][1]++, styles.winningSet)],
                  children: set
                }, index);
              })
            })]
          }), _jsxs(View, {
            style: styles.vsContainer,
            children: [_jsx(Text, {
              style: styles.vsText,
              children: "VS"
            }), _jsx(Trophy, {
              size: 20,
              color: colors.yellow
            })]
          }), _jsxs(View, {
            style: styles.playerSection,
            children: [_jsx(Image, {
              source: {
                uri: matchData.opponent.avatar
              },
              style: styles.playerAvatar
            }), _jsx(Text, {
              style: styles.playerName,
              children: matchData.opponent.name
            }), _jsx(View, {
              style: styles.scoreContainer,
              children: matchData.opponent.sets.map(function (set, index) {
                cov_1pml4p74yc().f[11]++;
                cov_1pml4p74yc().s[23]++;
                return _jsx(Text, {
                  style: [styles.setScore, (cov_1pml4p74yc().b[3][0]++, set > matchData.player.sets[index]) && (cov_1pml4p74yc().b[3][1]++, styles.winningSet)],
                  children: set
                }, index);
              })
            })]
          })]
        }), _jsxs(View, {
          style: styles.matchStatsRow,
          children: [_jsxs(View, {
            style: styles.statItem,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: matchStats.winnersCount
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Winners"
            })]
          }), _jsxs(View, {
            style: styles.statItem,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: matchStats.acesCount
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Aces"
            })]
          }), _jsxs(View, {
            style: styles.statItem,
            children: [_jsxs(Text, {
              style: styles.statValue,
              children: [matchStats.firstServePercentage, "%"]
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "1st Serve"
            })]
          }), _jsxs(View, {
            style: styles.statItem,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: matchStats.breakPointsConverted
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Break Points"
            })]
          })]
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.heatmapCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Shot Placement Analysis"
        }), _jsx(Text, {
          style: styles.sectionSubtitle,
          children: "Visualizing your winning shots, errors, and serve patterns"
        }), renderCourtHeatmap()]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.tacticsCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "AI Tactical Suggestions"
        }), _jsx(Text, {
          style: styles.sectionSubtitle,
          children: "Strategic improvements for your next match"
        }), tacticalSuggestions.map(function (suggestion) {
          cov_1pml4p74yc().f[12]++;
          var IconComponent = (cov_1pml4p74yc().s[24]++, suggestion.icon);
          cov_1pml4p74yc().s[25]++;
          return _jsxs(View, {
            style: styles.suggestionItem,
            children: [_jsx(View, {
              style: styles.suggestionIcon,
              children: _jsx(IconComponent, {
                size: 20,
                color: colors.primary
              })
            }), _jsxs(View, {
              style: styles.suggestionContent,
              children: [_jsx(Text, {
                style: styles.suggestionTitle,
                children: suggestion.title
              }), _jsx(Text, {
                style: styles.suggestionDescription,
                children: suggestion.description
              }), _jsx(Text, {
                style: styles.suggestionImpact,
                children: suggestion.impact
              })]
            }), _jsx(ArrowRight, {
              size: 16,
              color: colors.gray
            })]
          }, suggestion.id);
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.scenariosCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "What-If Scenarios"
        }), _jsx(Text, {
          style: styles.sectionSubtitle,
          children: "Explore different tactical approaches and their outcomes"
        }), whatIfScenarios.map(function (scenario) {
          cov_1pml4p74yc().f[13]++;
          cov_1pml4p74yc().s[26]++;
          return _jsxs(TouchableOpacity, {
            style: [styles.scenarioItem, (cov_1pml4p74yc().b[4][0]++, selectedScenario === scenario.id) && (cov_1pml4p74yc().b[4][1]++, styles.selectedScenario)],
            onPress: function onPress() {
              cov_1pml4p74yc().f[14]++;
              cov_1pml4p74yc().s[27]++;
              return handleScenarioSelect(scenario.id);
            },
            children: [_jsxs(View, {
              style: styles.scenarioContent,
              children: [_jsx(Text, {
                style: styles.scenarioQuestion,
                children: scenario.question
              }), (cov_1pml4p74yc().b[5][0]++, selectedScenario === scenario.id) && (cov_1pml4p74yc().b[5][1]++, _jsx(View, {
                style: styles.scenarioResult,
                children: isSimulating ? (cov_1pml4p74yc().b[6][0]++, _jsxs(View, {
                  style: styles.simulatingContainer,
                  children: [_jsx(Bot, {
                    size: 16,
                    color: colors.primary
                  }), _jsx(Text, {
                    style: styles.simulatingText,
                    children: "AI analyzing..."
                  })]
                })) : (cov_1pml4p74yc().b[6][1]++, _jsxs(View, {
                  style: styles.predictionContainer,
                  children: [_jsx(Text, {
                    style: styles.predictionText,
                    children: scenario.prediction
                  }), _jsx(Text, {
                    style: styles.predictionDetails,
                    children: scenario.details
                  })]
                }))
              }))]
            }), _jsx(View, {
              style: styles.scenarioIcon,
              children: (cov_1pml4p74yc().b[8][0]++, selectedScenario === scenario.id) && (cov_1pml4p74yc().b[8][1]++, !isSimulating) ? (cov_1pml4p74yc().b[7][0]++, _jsx(CheckCircle, {
                size: 20,
                color: colors.primary
              })) : (cov_1pml4p74yc().b[7][1]++, _jsx(PlayIcon, {
                size: 16,
                color: colors.gray
              }))
            })]
          }, scenario.id);
        })]
      }), _jsxs(Card, {
        variant: "elevated",
        style: styles.analysisCard,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Performance Analysis"
        }), _jsxs(View, {
          style: styles.analysisSection,
          children: [_jsxs(View, {
            style: styles.analysisHeader,
            children: [_jsx(CheckCircle, {
              size: 20,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.analysisTitle,
              children: "Strengths"
            })]
          }), strengthsAndGrowth.strengths.map(function (strength, index) {
            cov_1pml4p74yc().f[15]++;
            cov_1pml4p74yc().s[28]++;
            return _jsxs(Text, {
              style: styles.analysisItem,
              children: ["\u2022 ", strength]
            }, index);
          })]
        }), _jsxs(View, {
          style: styles.analysisSection,
          children: [_jsxs(View, {
            style: styles.analysisHeader,
            children: [_jsx(AlertTriangle, {
              size: 20,
              color: colors.yellow
            }), _jsx(Text, {
              style: styles.analysisTitle,
              children: "Growth Areas"
            })]
          }), strengthsAndGrowth.growthAreas.map(function (area, index) {
            cov_1pml4p74yc().f[16]++;
            cov_1pml4p74yc().s[29]++;
            return _jsxs(Text, {
              style: styles.analysisItem,
              children: ["\u2022 ", area]
            }, index);
          })]
        })]
      }), _jsx(Card, {
        variant: "elevated",
        style: styles.motivationCard,
        children: _jsx(LinearGradient, {
          colors: [colors.primary, '#1ea012'],
          style: styles.motivationGradient,
          children: _jsxs(View, {
            style: styles.motivationContent,
            children: [_jsx(Bot, {
              size: 32,
              color: colors.white
            }), _jsxs(View, {
              style: styles.motivationText,
              children: [_jsx(Text, {
                style: styles.motivationTitle,
                children: "Great Performance, Sara!"
              }), _jsx(Text, {
                style: styles.motivationMessage,
                children: "With more aggressive net play, your win probability increases by 14%. Keep working on those approach shots!"
              })]
            })]
          })
        })
      }), _jsxs(View, {
        style: styles.actionButtons,
        children: [_jsx(Button, {
          title: "New Match Simulation",
          onPress: handleNewMatch,
          style: styles.primaryButton
        }), _jsxs(View, {
          style: styles.secondaryButtons,
          children: [_jsxs(TouchableOpacity, {
            style: styles.secondaryButton,
            onPress: handleShareResults,
            children: [_jsx(Share2, {
              size: 20,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.secondaryButtonText,
              children: "Share Results"
            })]
          }), _jsxs(TouchableOpacity, {
            style: styles.secondaryButton,
            onPress: function onPress() {
              cov_1pml4p74yc().f[17]++;
            },
            children: [_jsx(RotateCcw, {
              size: 20,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.secondaryButtonText,
              children: "Replay Match"
            })]
          })]
        })]
      })]
    })
  });
}
var styles = (cov_1pml4p74yc().s[30]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray
  },
  scrollView: {
    flex: 1
  },
  header: {
    padding: 24,
    paddingBottom: 16
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: colors.dark
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4
  },
  matchCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20
  },
  matchTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  matchMeta: {
    alignItems: 'flex-end'
  },
  matchDuration: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.primary
  },
  matchSurface: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 2
  },
  playersContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20
  },
  playerSection: {
    flex: 1,
    alignItems: 'center'
  },
  playerAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginBottom: 8
  },
  playerName: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    textAlign: 'center',
    marginBottom: 8
  },
  scoreContainer: {
    flexDirection: 'row',
    gap: 8
  },
  setScore: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: colors.gray,
    minWidth: 24,
    textAlign: 'center'
  },
  winningSet: {
    color: colors.primary
  },
  vsContainer: {
    alignItems: 'center',
    paddingHorizontal: 20
  },
  vsText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: colors.gray,
    marginBottom: 4
  },
  matchStatsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray
  },
  statItem: {
    alignItems: 'center'
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: colors.dark
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4
  },
  heatmapCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 8
  },
  sectionSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 20
  },
  courtContainer: {
    alignItems: 'center'
  },
  court: {
    width: 280,
    height: 140,
    backgroundColor: colors.blue,
    borderRadius: 8,
    position: 'relative',
    marginBottom: 16
  },
  courtLines: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0
  },
  baseline: {
    position: 'absolute',
    top: 10,
    left: 20,
    right: 20,
    height: 2,
    backgroundColor: colors.white
  },
  serviceLine: {
    position: 'absolute',
    top: 50,
    left: 20,
    right: 20,
    height: 1,
    backgroundColor: colors.white
  },
  centerLine: {
    position: 'absolute',
    top: 10,
    bottom: 50,
    left: '50%',
    width: 1,
    backgroundColor: colors.white,
    marginLeft: -0.5
  },
  sideLines: {
    position: 'absolute',
    top: 10,
    bottom: 10,
    left: 20,
    right: 20,
    borderLeftWidth: 2,
    borderRightWidth: 2,
    borderColor: colors.white
  },
  courtLabel: {
    position: 'absolute',
    top: 25,
    left: 30,
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: colors.white
  },
  adCourtLabel: {
    left: 'auto',
    right: 30
  },
  shotDot: {
    position: 'absolute',
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: -4,
    marginTop: -4
  },
  courtLegend: {
    flexDirection: 'row',
    gap: 20
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  legendDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6
  },
  legendText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  tacticsCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  suggestionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16
  },
  suggestionContent: {
    flex: 1
  },
  suggestionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 4
  },
  suggestionDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 4
  },
  suggestionImpact: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: colors.primary
  },
  scenariosCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  scenarioItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.lightGray,
    marginBottom: 12
  },
  selectedScenario: {
    borderColor: colors.primary,
    backgroundColor: colors.lightGray
  },
  scenarioContent: {
    flex: 1
  },
  scenarioQuestion: {
    fontSize: 15,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 8
  },
  scenarioResult: {
    marginTop: 8
  },
  simulatingContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  simulatingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.primary,
    marginLeft: 8
  },
  predictionContainer: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray
  },
  predictionText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary,
    marginBottom: 4
  },
  predictionDetails: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  scenarioIcon: {
    marginLeft: 12
  },
  analysisCard: {
    marginHorizontal: 24,
    marginBottom: 20
  },
  analysisSection: {
    marginBottom: 20
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  analysisTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginLeft: 8
  },
  analysisItem: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 20,
    marginBottom: 4
  },
  motivationCard: {
    marginHorizontal: 24,
    marginBottom: 20,
    padding: 0,
    overflow: 'hidden'
  },
  motivationGradient: {
    padding: 20,
    borderRadius: 16
  },
  motivationContent: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  motivationText: {
    flex: 1,
    marginLeft: 16
  },
  motivationTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: colors.white,
    marginBottom: 8
  },
  motivationMessage: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.white,
    lineHeight: 20,
    opacity: 0.9
  },
  actionButtons: {
    padding: 24,
    paddingTop: 8
  },
  primaryButton: {
    marginBottom: 16
  },
  secondaryButtons: {
    flexDirection: 'row',
    gap: 12
  },
  secondaryButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.primary,
    backgroundColor: colors.white
  },
  secondaryButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary,
    marginLeft: 8
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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