{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_objectSpread2", "_AnimatedValue", "_AnimatedNode", "_NativeAnimatedHelper", "AnimatedTracking", "_AnimatedNode$default", "value", "parent", "animationClass", "animationConfig", "callback", "_this", "_value", "_parent", "_animationClass", "_animationConfig", "_useNativeDriver", "shouldUseNativeDriver", "_callback", "__attach", "key", "__makeNative", "__isNative", "__getValue", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "update", "animate", "toValue", "__getNativeConfig", "animation", "undefined", "__getNativeAnimationConfig", "type", "animationId", "generateNewAnimationId", "__getNativeTag", "_default", "module"], "sources": ["AnimatedTracking.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _AnimatedValue = _interopRequireDefault(require(\"./AnimatedValue\"));\nvar _AnimatedNode = _interopRequireDefault(require(\"./AnimatedNode\"));\nvar _NativeAnimatedHelper = require(\"../NativeAnimatedHelper\");\nclass AnimatedTracking extends _AnimatedNode.default {\n  constructor(value, parent, animationClass, animationConfig, callback) {\n    super();\n    this._value = value;\n    this._parent = parent;\n    this._animationClass = animationClass;\n    this._animationConfig = animationConfig;\n    this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(animationConfig);\n    this._callback = callback;\n    this.__attach();\n  }\n  __makeNative() {\n    this.__isNative = true;\n    this._parent.__makeNative();\n    super.__makeNative();\n    this._value.__makeNative();\n  }\n  __getValue() {\n    return this._parent.__getValue();\n  }\n  __attach() {\n    this._parent.__addChild(this);\n    if (this._useNativeDriver) {\n      // when the tracking starts we need to convert this node to a \"native node\"\n      // so that the parent node will be made \"native\" too. This is necessary as\n      // if we don't do this `update` method will get called. At that point it\n      // may be too late as it would mean the JS driver has already started\n      // updating node values\n      this.__makeNative();\n    }\n  }\n  __detach() {\n    this._parent.__removeChild(this);\n    super.__detach();\n  }\n  update() {\n    this._value.animate(new this._animationClass((0, _objectSpread2.default)((0, _objectSpread2.default)({}, this._animationConfig), {}, {\n      toValue: this._animationConfig.toValue.__getValue()\n    })), this._callback);\n  }\n  __getNativeConfig() {\n    var animation = new this._animationClass((0, _objectSpread2.default)((0, _objectSpread2.default)({}, this._animationConfig), {}, {\n      // remove toValue from the config as it's a ref to Animated.Value\n      toValue: undefined\n    }));\n    var animationConfig = animation.__getNativeAnimationConfig();\n    return {\n      type: 'tracking',\n      animationId: (0, _NativeAnimatedHelper.generateNewAnimationId)(),\n      animationConfig,\n      toValue: this._parent.__getNativeTag(),\n      value: this._value.__getNativeTag()\n    };\n  }\n}\nvar _default = exports.default = AnimatedTracking;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,cAAc,GAAGH,sBAAsB,CAACxB,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAI4B,cAAc,GAAGJ,sBAAsB,CAACxB,OAAO,kBAAkB,CAAC,CAAC;AACvE,IAAI6B,aAAa,GAAGL,sBAAsB,CAACxB,OAAO,iBAAiB,CAAC,CAAC;AACrE,IAAI8B,qBAAqB,GAAG9B,OAAO,0BAA0B,CAAC;AAAC,IACzD+B,gBAAgB,aAAAC,qBAAA;EACpB,SAAAD,iBAAYE,KAAK,EAAEC,MAAM,EAAEC,cAAc,EAAEC,eAAe,EAAEC,QAAQ,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAArC,gBAAA,CAAAU,OAAA,QAAAoB,gBAAA;IACpEO,KAAA,GAAA/B,UAAA,OAAAwB,gBAAA;IACAO,KAAA,CAAKC,MAAM,GAAGN,KAAK;IACnBK,KAAA,CAAKE,OAAO,GAAGN,MAAM;IACrBI,KAAA,CAAKG,eAAe,GAAGN,cAAc;IACrCG,KAAA,CAAKI,gBAAgB,GAAGN,eAAe;IACvCE,KAAA,CAAKK,gBAAgB,GAAG,CAAC,CAAC,EAAEb,qBAAqB,CAACc,qBAAqB,EAAER,eAAe,CAAC;IACzFE,KAAA,CAAKO,SAAS,GAAGR,QAAQ;IACzBC,KAAA,CAAKQ,QAAQ,CAAC,CAAC;IAAC,OAAAR,KAAA;EAClB;EAAC,IAAAhC,UAAA,CAAAK,OAAA,EAAAoB,gBAAA,EAAAC,qBAAA;EAAA,WAAA9B,aAAA,CAAAS,OAAA,EAAAoB,gBAAA;IAAAgB,GAAA;IAAAd,KAAA,EACD,SAAAe,YAAYA,CAAA,EAAG;MACb,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAI,CAACT,OAAO,CAACQ,YAAY,CAAC,CAAC;MAC3B3B,aAAA,CAAAU,gBAAA;MACA,IAAI,CAACQ,MAAM,CAACS,YAAY,CAAC,CAAC;IAC5B;EAAC;IAAAD,GAAA;IAAAd,KAAA,EACD,SAAAiB,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAACV,OAAO,CAACU,UAAU,CAAC,CAAC;IAClC;EAAC;IAAAH,GAAA;IAAAd,KAAA,EACD,SAAAa,QAAQA,CAAA,EAAG;MACT,IAAI,CAACN,OAAO,CAACW,UAAU,CAAC,IAAI,CAAC;MAC7B,IAAI,IAAI,CAACR,gBAAgB,EAAE;QAMzB,IAAI,CAACK,YAAY,CAAC,CAAC;MACrB;IACF;EAAC;IAAAD,GAAA;IAAAd,KAAA,EACD,SAAAmB,QAAQA,CAAA,EAAG;MACT,IAAI,CAACZ,OAAO,CAACa,aAAa,CAAC,IAAI,CAAC;MAChChC,aAAA,CAAAU,gBAAA;IACF;EAAC;IAAAgB,GAAA;IAAAd,KAAA,EACD,SAAAqB,MAAMA,CAAA,EAAG;MACP,IAAI,CAACf,MAAM,CAACgB,OAAO,CAAC,IAAI,IAAI,CAACd,eAAe,CAAC,CAAC,CAAC,EAAEd,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEgB,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC+B,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QACnIc,OAAO,EAAE,IAAI,CAACd,gBAAgB,CAACc,OAAO,CAACN,UAAU,CAAC;MACpD,CAAC,CAAC,CAAC,EAAE,IAAI,CAACL,SAAS,CAAC;IACtB;EAAC;IAAAE,GAAA;IAAAd,KAAA,EACD,SAAAwB,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,SAAS,GAAG,IAAI,IAAI,CAACjB,eAAe,CAAC,CAAC,CAAC,EAAEd,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEgB,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC+B,gBAAgB,CAAC,EAAE,CAAC,CAAC,EAAE;QAE/Hc,OAAO,EAAEG;MACX,CAAC,CAAC,CAAC;MACH,IAAIvB,eAAe,GAAGsB,SAAS,CAACE,0BAA0B,CAAC,CAAC;MAC5D,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,WAAW,EAAE,CAAC,CAAC,EAAEhC,qBAAqB,CAACiC,sBAAsB,EAAE,CAAC;QAChE3B,eAAe,EAAfA,eAAe;QACfoB,OAAO,EAAE,IAAI,CAAChB,OAAO,CAACwB,cAAc,CAAC,CAAC;QACtC/B,KAAK,EAAE,IAAI,CAACM,MAAM,CAACyB,cAAc,CAAC;MACpC,CAAC;IACH;EAAC;AAAA,EArD4BnC,aAAa,CAAClB,OAAO;AAuDpD,IAAIsD,QAAQ,GAAGxC,OAAO,CAACd,OAAO,GAAGoB,gBAAgB;AACjDmC,MAAM,CAACzC,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}