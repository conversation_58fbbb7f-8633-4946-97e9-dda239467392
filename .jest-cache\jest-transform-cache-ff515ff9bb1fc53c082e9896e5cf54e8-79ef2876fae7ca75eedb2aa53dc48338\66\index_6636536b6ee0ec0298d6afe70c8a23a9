895f1c154c028efffdbd0fe67e51911b
"use strict";

exports.__esModule = true;
exports.default = void 0;
var unitlessNumbers = {
  animationIterationCount: true,
  aspectRatio: true,
  borderImageOutset: true,
  borderImageSlice: true,
  borderImageWidth: true,
  boxFlex: true,
  boxFlexGroup: true,
  boxOrdinalGroup: true,
  columnCount: true,
  flex: true,
  flexGrow: true,
  flexOrder: true,
  flexPositive: true,
  flexShrink: true,
  flexNegative: true,
  fontWeight: true,
  gridRow: true,
  gridRowEnd: true,
  gridRowGap: true,
  gridRowStart: true,
  gridColumn: true,
  gridColumnEnd: true,
  gridColumnGap: true,
  gridColumnStart: true,
  lineClamp: true,
  opacity: true,
  order: true,
  orphans: true,
  tabSize: true,
  widows: true,
  zIndex: true,
  zoom: true,
  fillOpacity: true,
  floodOpacity: true,
  stopOpacity: true,
  strokeDasharray: true,
  strokeDashoffset: true,
  strokeMiterlimit: true,
  strokeOpacity: true,
  strokeWidth: true,
  scale: true,
  scaleX: true,
  scaleY: true,
  scaleZ: true,
  shadowOpacity: true
};
var prefixes = ['ms', 'Moz', 'O', 'Webkit'];
var prefixKey = function prefixKey(prefix, key) {
  return prefix + key.charAt(0).toUpperCase() + key.substring(1);
};
Object.keys(unitlessNumbers).forEach(function (prop) {
  prefixes.forEach(function (prefix) {
    unitlessNumbers[prefixKey(prefix, prop)] = unitlessNumbers[prop];
  });
});
var _default = exports.default = unitlessNumbers;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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