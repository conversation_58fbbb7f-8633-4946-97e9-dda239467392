{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_createPrefixer", "_static", "prefixAll", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _createPrefixer = _interopRequireDefault(require(\"inline-style-prefixer/lib/createPrefixer\"));\nvar _static = _interopRequireDefault(require(\"./static\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar prefixAll = (0, _createPrefixer.default)(_static.default);\nvar _default = exports.default = prefixAll;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,eAAe,GAAGL,sBAAsB,CAACC,OAAO,CAAC,0CAA0C,CAAC,CAAC;AACjG,IAAIK,OAAO,GAAGN,sBAAsB,CAACC,OAAO,WAAW,CAAC,CAAC;AAUzD,IAAIM,SAAS,GAAG,CAAC,CAAC,EAAEF,eAAe,CAACH,OAAO,EAAEI,OAAO,CAACJ,OAAO,CAAC;AAC7D,IAAIM,QAAQ,GAAGL,OAAO,CAACD,OAAO,GAAGK,SAAS;AAC1CE,MAAM,CAACN,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}