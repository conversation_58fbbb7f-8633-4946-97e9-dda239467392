{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_NativeEventEmitter", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _NativeEventEmitter = _interopRequireDefault(require(\"../../vendor/react-native/EventEmitter/NativeEventEmitter\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nvar _default = exports.default = _NativeEventEmitter.default;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,mBAAmB,GAAGL,sBAAsB,CAACC,OAAO,4DAA4D,CAAC,CAAC;AAStH,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,mBAAmB,CAACH,OAAO;AAC5DK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}