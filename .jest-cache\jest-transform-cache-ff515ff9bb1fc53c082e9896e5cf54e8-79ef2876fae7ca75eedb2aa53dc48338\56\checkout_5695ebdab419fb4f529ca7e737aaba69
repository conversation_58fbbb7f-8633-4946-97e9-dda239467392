90cd079ce368947acf6103dc5113ca30
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_9qc468kxs() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\payment\\checkout.tsx";
  var hash = "2ea36e1f7caa3f95c9a51e8ae8de1701a4cf54e2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\payment\\checkout.tsx",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 15
        },
        end: {
          line: 16,
          column: 1
        }
      },
      "1": {
        start: {
          line: 19,
          column: 17
        },
        end: {
          line: 19,
          column: 39
        }
      },
      "2": {
        start: {
          line: 20,
          column: 32
        },
        end: {
          line: 20,
          column: 47
        }
      },
      "3": {
        start: {
          line: 22,
          column: 22
        },
        end: {
          line: 26,
          column: 3
        }
      },
      "4": {
        start: {
          line: 28,
          column: 24
        },
        end: {
          line: 49,
          column: 3
        }
      },
      "5": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "6": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 48,
          column: 5
        }
      },
      "7": {
        start: {
          line: 32,
          column: 6
        },
        end: {
          line: 32,
          column: 62
        }
      },
      "8": {
        start: {
          line: 32,
          column: 35
        },
        end: {
          line: 32,
          column: 60
        }
      },
      "9": {
        start: {
          line: 34,
          column: 6
        },
        end: {
          line: 43,
          column: 8
        }
      },
      "10": {
        start: {
          line: 40,
          column: 27
        },
        end: {
          line: 40,
          column: 60
        }
      },
      "11": {
        start: {
          line: 45,
          column: 6
        },
        end: {
          line: 45,
          column: 57
        }
      },
      "12": {
        start: {
          line: 47,
          column: 6
        },
        end: {
          line: 47,
          column: 24
        }
      },
      "13": {
        start: {
          line: 51,
          column: 2
        },
        end: {
          line: 106,
          column: 4
        }
      },
      "14": {
        start: {
          line: 56,
          column: 25
        },
        end: {
          line: 56,
          column: 38
        }
      },
      "15": {
        start: {
          line: 109,
          column: 15
        },
        end: {
          line: 218,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "CheckoutScreen",
        decl: {
          start: {
            line: 18,
            column: 24
          },
          end: {
            line: 18,
            column: 38
          }
        },
        loc: {
          start: {
            line: 18,
            column: 41
          },
          end: {
            line: 107,
            column: 1
          }
        },
        line: 18
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 25
          }
        },
        loc: {
          start: {
            line: 28,
            column: 36
          },
          end: {
            line: 49,
            column: 3
          }
        },
        line: 28
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 32,
            column: 24
          },
          end: {
            line: 32,
            column: 25
          }
        },
        loc: {
          start: {
            line: 32,
            column: 35
          },
          end: {
            line: 32,
            column: 60
          }
        },
        line: 32
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 40,
            column: 21
          },
          end: {
            line: 40,
            column: 22
          }
        },
        loc: {
          start: {
            line: 40,
            column: 27
          },
          end: {
            line: 40,
            column: 60
          }
        },
        line: 40
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 56,
            column: 19
          },
          end: {
            line: 56,
            column: 20
          }
        },
        loc: {
          start: {
            line: 56,
            column: 25
          },
          end: {
            line: 56,
            column: 38
          }
        },
        line: 56
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 10
          },
          end: {
            line: 23,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 23,
            column: 10
          },
          end: {
            line: 23,
            column: 21
          }
        }, {
          start: {
            line: 23,
            column: 25
          },
          end: {
            line: 23,
            column: 39
          }
        }],
        line: 23
      },
      "1": {
        loc: {
          start: {
            line: 24,
            column: 11
          },
          end: {
            line: 24,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 24,
            column: 11
          },
          end: {
            line: 24,
            column: 23
          }
        }, {
          start: {
            line: 24,
            column: 27
          },
          end: {
            line: 24,
            column: 34
          }
        }],
        line: 24
      },
      "2": {
        loc: {
          start: {
            line: 25,
            column: 12
          },
          end: {
            line: 25,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 25,
            column: 12
          },
          end: {
            line: 25,
            column: 25
          }
        }, {
          start: {
            line: 25,
            column: 29
          },
          end: {
            line: 25,
            column: 36
          }
        }],
        line: 25
      },
      "3": {
        loc: {
          start: {
            line: 99,
            column: 17
          },
          end: {
            line: 99,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 99,
            column: 27
          },
          end: {
            line: 99,
            column: 42
          }
        }, {
          start: {
            line: 99,
            column: 45
          },
          end: {
            line: 99,
            column: 71
          }
        }],
        line: 99
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2ea36e1f7caa3f95c9a51e8ae8de1701a4cf54e2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_9qc468kxs = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_9qc468kxs();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router, useLocalSearchParams } from 'expo-router';
import { ArrowLeft, CreditCard, Shield } from 'lucide-react-native';
import Button from "../../components/ui/Button";
import Card from "../../components/ui/Card";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_9qc468kxs().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
export default function CheckoutScreen() {
  cov_9qc468kxs().f[0]++;
  var params = (cov_9qc468kxs().s[1]++, useLocalSearchParams());
  var _ref = (cov_9qc468kxs().s[2]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    loading = _ref2[0],
    setLoading = _ref2[1];
  var planDetails = (cov_9qc468kxs().s[3]++, {
    name: (cov_9qc468kxs().b[0][0]++, params.plan) || (cov_9qc468kxs().b[0][1]++, 'Premium Plan'),
    price: (cov_9qc468kxs().b[1][0]++, params.price) || (cov_9qc468kxs().b[1][1]++, '$9.99'),
    period: (cov_9qc468kxs().b[2][0]++, params.period) || (cov_9qc468kxs().b[2][1]++, 'month')
  });
  cov_9qc468kxs().s[4]++;
  var handlePayment = function () {
    var _ref3 = _asyncToGenerator(function* () {
      cov_9qc468kxs().f[1]++;
      cov_9qc468kxs().s[5]++;
      setLoading(true);
      cov_9qc468kxs().s[6]++;
      try {
        cov_9qc468kxs().s[7]++;
        yield new Promise(function (resolve) {
          cov_9qc468kxs().f[2]++;
          cov_9qc468kxs().s[8]++;
          return setTimeout(resolve, 2000);
        });
        cov_9qc468kxs().s[9]++;
        Alert.alert('Payment Successful', 'Your subscription has been activated!', [{
          text: 'OK',
          onPress: function onPress() {
            cov_9qc468kxs().f[3]++;
            cov_9qc468kxs().s[10]++;
            return router.replace('/(tabs)/profile');
          }
        }]);
      } catch (error) {
        cov_9qc468kxs().s[11]++;
        Alert.alert('Payment Failed', 'Please try again.');
      } finally {
        cov_9qc468kxs().s[12]++;
        setLoading(false);
      }
    });
    return function handlePayment() {
      return _ref3.apply(this, arguments);
    };
  }();
  cov_9qc468kxs().s[13]++;
  return _jsxs(SafeAreaView, {
    style: styles.container,
    children: [_jsxs(View, {
      style: styles.header,
      children: [_jsx(Button, {
        title: "",
        onPress: function onPress() {
          cov_9qc468kxs().f[4]++;
          cov_9qc468kxs().s[14]++;
          return router.back();
        },
        variant: "ghost",
        style: styles.backButton,
        children: _jsx(ArrowLeft, {
          size: 24,
          color: colors.dark
        })
      }), _jsx(Text, {
        style: styles.headerTitle,
        children: "Checkout"
      }), _jsx(View, {
        style: styles.placeholder
      })]
    }), _jsxs(ScrollView, {
      style: styles.content,
      children: [_jsxs(Card, {
        style: styles.planCard,
        children: [_jsx(Text, {
          style: styles.planName,
          children: planDetails.name
        }), _jsxs(Text, {
          style: styles.planPrice,
          children: [planDetails.price, "/", planDetails.period]
        }), _jsx(Text, {
          style: styles.planDescription,
          children: "Full access to all premium features"
        })]
      }), _jsxs(Card, {
        style: styles.paymentCard,
        children: [_jsxs(View, {
          style: styles.cardHeader,
          children: [_jsx(CreditCard, {
            size: 24,
            color: colors.primary
          }), _jsx(Text, {
            style: styles.cardTitle,
            children: "Payment Method"
          })]
        }), _jsx(Text, {
          style: styles.cardInfo,
          children: "**** **** **** 1234"
        }), _jsx(Text, {
          style: styles.cardExpiry,
          children: "Expires 12/25"
        })]
      }), _jsxs(Card, {
        style: styles.securityCard,
        children: [_jsxs(View, {
          style: styles.securityHeader,
          children: [_jsx(Shield, {
            size: 24,
            color: colors.primary
          }), _jsx(Text, {
            style: styles.securityTitle,
            children: "Secure Payment"
          })]
        }), _jsx(Text, {
          style: styles.securityText,
          children: "Your payment information is encrypted and secure"
        })]
      })]
    }), _jsx(View, {
      style: styles.footer,
      children: _jsx(Button, {
        title: loading ? (cov_9qc468kxs().b[3][0]++, 'Processing...') : (cov_9qc468kxs().b[3][1]++, `Pay ${planDetails.price}`),
        onPress: handlePayment,
        disabled: loading,
        style: styles.payButton
      })
    })]
  });
}
var styles = (cov_9qc468kxs().s[15]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  backButton: {
    width: 40,
    height: 40
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  placeholder: {
    width: 40
  },
  content: {
    flex: 1,
    padding: 20
  },
  planCard: {
    marginBottom: 20,
    alignItems: 'center',
    padding: 24
  },
  planName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: colors.dark,
    marginBottom: 8
  },
  planPrice: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: colors.primary,
    marginBottom: 8
  },
  planDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    textAlign: 'center'
  },
  paymentCard: {
    marginBottom: 20,
    padding: 20
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  cardTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginLeft: 12
  },
  cardInfo: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: colors.dark,
    marginBottom: 4
  },
  cardExpiry: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  securityCard: {
    marginBottom: 20,
    padding: 20
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  securityTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginLeft: 12
  },
  securityText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  footer: {
    padding: 20,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray
  },
  payButton: {
    width: '100%'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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