dfb0fc31e7a740dcba95c6805a4027d1
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var twoArgumentPooler = function twoArgumentPooler(a1, a2) {
  var Klass = this;
  if (Klass.instancePool.length) {
    var instance = Klass.instancePool.pop();
    Klass.call(instance, a1, a2);
    return instance;
  } else {
    return new Klass(a1, a2);
  }
};
var standardReleaser = function standardReleaser(instance) {
  var Klass = this;
  instance.destructor();
  if (Klass.instancePool.length < Klass.poolSize) {
    Klass.instancePool.push(instance);
  }
};
var DEFAULT_POOL_SIZE = 10;
var DEFAULT_POOLER = twoArgumentPooler;
var addPoolingTo = function addPoolingTo(CopyConstructor, pooler) {
  var NewKlass = CopyConstructor;
  NewKlass.instancePool = [];
  NewKlass.getPooled = pooler || DEFAULT_POOLER;
  if (!NewKlass.poolSize) {
    NewKlass.poolSize = DEFAULT_POOL_SIZE;
  }
  NewKlass.release = standardReleaser;
  return NewKlass;
};
var PooledClass = {
  addPoolingTo: addPoolingTo,
  twoArgumentPooler: twoArgumentPooler
};
var _default = exports.default = PooledClass;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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