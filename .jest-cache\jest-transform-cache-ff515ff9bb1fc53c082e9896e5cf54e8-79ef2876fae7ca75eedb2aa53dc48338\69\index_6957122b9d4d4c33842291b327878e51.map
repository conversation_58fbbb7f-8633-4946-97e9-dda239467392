{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_TextInputState", "dismissKeyboard", "blurTextInput", "currentlyFocusedField", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _TextInputState = _interopRequireDefault(require(\"../TextInputState\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar dismissKeyboard = () => {\n  _TextInputState.default.blurTextInput(_TextInputState.default.currentlyFocusedField());\n};\nvar _default = exports.default = dismissKeyboard;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,eAAe,GAAGL,sBAAsB,CAACC,OAAO,oBAAoB,CAAC,CAAC;AAU1E,IAAIK,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EAC1BD,eAAe,CAACH,OAAO,CAACK,aAAa,CAACF,eAAe,CAACH,OAAO,CAACM,qBAAqB,CAAC,CAAC,CAAC;AACxF,CAAC;AACD,IAAIC,QAAQ,GAAGN,OAAO,CAACD,OAAO,GAAGI,eAAe;AAChDI,MAAM,CAACP,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}