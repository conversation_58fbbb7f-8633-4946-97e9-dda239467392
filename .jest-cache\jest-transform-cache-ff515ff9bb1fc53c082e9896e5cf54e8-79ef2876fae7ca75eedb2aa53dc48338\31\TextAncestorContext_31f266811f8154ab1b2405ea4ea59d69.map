{"version": 3, "names": ["exports", "__esModule", "default", "_react", "require", "TextAncestorContext", "createContext", "_default", "module"], "sources": ["TextAncestorContext.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nexports.__esModule = true;\nexports.default = void 0;\nvar _react = require(\"react\");\nvar TextAncestorContext = /*#__PURE__*/(0, _react.createContext)(false);\nvar _default = exports.default = TextAncestorContext;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAUZ,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC7B,IAAIC,mBAAmB,GAAgB,CAAC,CAAC,EAAEF,MAAM,CAACG,aAAa,EAAE,KAAK,CAAC;AACvE,IAAIC,QAAQ,GAAGP,OAAO,CAACE,OAAO,GAAGG,mBAAmB;AACpDG,MAAM,CAACR,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}