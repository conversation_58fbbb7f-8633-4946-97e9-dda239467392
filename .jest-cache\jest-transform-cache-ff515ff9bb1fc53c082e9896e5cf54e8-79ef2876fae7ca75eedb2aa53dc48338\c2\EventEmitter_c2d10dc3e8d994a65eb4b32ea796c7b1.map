{"version": 3, "names": ["_interopRequireDefault", "require", "_toConsumableArray2", "_classCallCheck2", "_createClass2", "exports", "__esModule", "default", "EventEmitter", "_registry", "key", "value", "addListener", "eventType", "listener", "context", "registrations", "allocate", "registration", "remove", "delete", "add", "emit", "_len", "arguments", "length", "args", "Array", "_key", "_i", "_arr", "apply", "removeAllListeners", "listenerCount", "size", "registry", "Set", "module"], "sources": ["EventEmitter.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n/**\n * EventEmitter manages listeners and publishes events to them.\n *\n * EventEmitter accepts a single type parameter that defines the valid events\n * and associated listener argument(s).\n *\n * @example\n *\n *   const emitter = new EventEmitter<{\n *     success: [number, string],\n *     error: [Error],\n *   }>();\n *\n *   emitter.on('success', (statusCode, responseText) => {...});\n *   emitter.emit('success', 200, '...');\n *\n *   emitter.on('error', error => {...});\n *   emitter.emit('error', new Error('Resource not found'));\n *\n */\nclass EventEmitter {\n  constructor() {\n    this._registry = {};\n  }\n  /**\n   * Registers a listener that is called when the supplied event is emitted.\n   * Returns a subscription that has a `remove` method to undo registration.\n   */\n  addListener(eventType, listener, context) {\n    var registrations = allocate(this._registry, eventType);\n    var registration = {\n      context,\n      listener,\n      remove() {\n        registrations.delete(registration);\n      }\n    };\n    registrations.add(registration);\n    return registration;\n  }\n\n  /**\n   * Emits the supplied event. Additional arguments supplied to `emit` will be\n   * passed through to each of the registered listeners.\n   *\n   * If a listener modifies the listeners registered for the same event, those\n   * changes will not be reflected in the current invocation of `emit`.\n   */\n  emit(eventType) {\n    var registrations = this._registry[eventType];\n    if (registrations != null) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n      for (var _i = 0, _arr = [...registrations]; _i < _arr.length; _i++) {\n        var registration = _arr[_i];\n        registration.listener.apply(registration.context, args);\n      }\n    }\n  }\n\n  /**\n   * Removes all registered listeners.\n   */\n  removeAllListeners(eventType) {\n    if (eventType == null) {\n      this._registry = {};\n    } else {\n      delete this._registry[eventType];\n    }\n  }\n\n  /**\n   * Returns the number of registered listeners for the supplied event.\n   */\n  listenerCount(eventType) {\n    var registrations = this._registry[eventType];\n    return registrations == null ? 0 : registrations.size;\n  }\n}\nexports.default = EventEmitter;\nfunction allocate(registry, eventType) {\n  var registrations = registry[eventType];\n  if (registrations == null) {\n    registrations = new Set();\n    registry[eventType] = registrations;\n  }\n  return registrations;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAA,IAAAC,mBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAA,IAAAE,gBAAA,GAAAH,sBAAA,CAAAC,OAAA;AAAA,IAAAG,aAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAEbI,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAAC,IA+BnBC,YAAY;EAChB,SAAAA,aAAA,EAAc;IAAA,IAAAL,gBAAA,CAAAI,OAAA,QAAAC,YAAA;IACZ,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACrB;EAAC,WAAAL,aAAA,CAAAG,OAAA,EAAAC,YAAA;IAAAE,GAAA;IAAAC,KAAA,EAKD,SAAAC,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,EAAE;MACxC,IAAIC,aAAa,GAAGC,QAAQ,CAAC,IAAI,CAACR,SAAS,EAAEI,SAAS,CAAC;MACvD,IAAIK,YAAY,GAAG;QACjBH,OAAO,EAAPA,OAAO;QACPD,QAAQ,EAARA,QAAQ;QACRK,MAAM,WAANA,MAAMA,CAAA,EAAG;UACPH,aAAa,CAACI,MAAM,CAACF,YAAY,CAAC;QACpC;MACF,CAAC;MACDF,aAAa,CAACK,GAAG,CAACH,YAAY,CAAC;MAC/B,OAAOA,YAAY;IACrB;EAAC;IAAAR,GAAA;IAAAC,KAAA,EASD,SAAAW,IAAIA,CAACT,SAAS,EAAE;MACd,IAAIG,aAAa,GAAG,IAAI,CAACP,SAAS,CAACI,SAAS,CAAC;MAC7C,IAAIG,aAAa,IAAI,IAAI,EAAE;QACzB,KAAK,IAAIO,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;UAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;QAClC;QACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,IAAI,OAAA5B,mBAAA,CAAAK,OAAA,EAAOS,aAAa,CAAC,EAAEa,EAAE,GAAGC,IAAI,CAACL,MAAM,EAAEI,EAAE,EAAE,EAAE;UAClE,IAAIX,YAAY,GAAGY,IAAI,CAACD,EAAE,CAAC;UAC3BX,YAAY,CAACJ,QAAQ,CAACiB,KAAK,CAACb,YAAY,CAACH,OAAO,EAAEW,IAAI,CAAC;QACzD;MACF;IACF;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAKD,SAAAqB,kBAAkBA,CAACnB,SAAS,EAAE;MAC5B,IAAIA,SAAS,IAAI,IAAI,EAAE;QACrB,IAAI,CAACJ,SAAS,GAAG,CAAC,CAAC;MACrB,CAAC,MAAM;QACL,OAAO,IAAI,CAACA,SAAS,CAACI,SAAS,CAAC;MAClC;IACF;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAKD,SAAAsB,aAAaA,CAACpB,SAAS,EAAE;MACvB,IAAIG,aAAa,GAAG,IAAI,CAACP,SAAS,CAACI,SAAS,CAAC;MAC7C,OAAOG,aAAa,IAAI,IAAI,GAAG,CAAC,GAAGA,aAAa,CAACkB,IAAI;IACvD;EAAC;AAAA;AAEH7B,OAAO,CAACE,OAAO,GAAGC,YAAY;AAC9B,SAASS,QAAQA,CAACkB,QAAQ,EAAEtB,SAAS,EAAE;EACrC,IAAIG,aAAa,GAAGmB,QAAQ,CAACtB,SAAS,CAAC;EACvC,IAAIG,aAAa,IAAI,IAAI,EAAE;IACzBA,aAAa,GAAG,IAAIoB,GAAG,CAAC,CAAC;IACzBD,QAAQ,CAACtB,SAAS,CAAC,GAAGG,aAAa;EACrC;EACA,OAAOA,aAAa;AACtB;AACAqB,MAAM,CAAChC,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}