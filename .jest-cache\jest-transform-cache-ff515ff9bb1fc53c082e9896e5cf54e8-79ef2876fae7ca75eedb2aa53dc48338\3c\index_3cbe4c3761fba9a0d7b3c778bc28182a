f80597395b7055709c4184653039d446
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _usePressEvents = _interopRequireDefault(require("../../modules/usePressEvents"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["activeOpacity", "delayPressIn", "delayPressOut", "delayLongPress", "disabled", "focusable", "onLongPress", "onPress", "onPressIn", "onPressOut", "rejectResponderTermination", "style"];
function TouchableOpacity(props, forwardedRef) {
  var activeOpacity = props.activeOpacity,
    delayPressIn = props.delayPressIn,
    delayPressOut = props.delayPressOut,
    delayLongPress = props.delayLongPress,
    disabled = props.disabled,
    focusable = props.focusable,
    onLongPress = props.onLongPress,
    onPress = props.onPress,
    onPressIn = props.onPressIn,
    onPressOut = props.onPressOut,
    rejectResponderTermination = props.rejectResponderTermination,
    style = props.style,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var hostRef = (0, _react.useRef)(null);
  var setRef = (0, _useMergeRefs.default)(forwardedRef, hostRef);
  var _useState = (0, _react.useState)('0s'),
    duration = _useState[0],
    setDuration = _useState[1];
  var _useState2 = (0, _react.useState)(null),
    opacityOverride = _useState2[0],
    setOpacityOverride = _useState2[1];
  var setOpacityTo = (0, _react.useCallback)(function (value, duration) {
    setOpacityOverride(value);
    setDuration(duration ? duration / 1000 + "s" : '0s');
  }, [setOpacityOverride, setDuration]);
  var setOpacityActive = (0, _react.useCallback)(function (duration) {
    setOpacityTo(activeOpacity !== null && activeOpacity !== void 0 ? activeOpacity : 0.2, duration);
  }, [activeOpacity, setOpacityTo]);
  var setOpacityInactive = (0, _react.useCallback)(function (duration) {
    setOpacityTo(null, duration);
  }, [setOpacityTo]);
  var pressConfig = (0, _react.useMemo)(function () {
    return {
      cancelable: !rejectResponderTermination,
      disabled: disabled,
      delayLongPress: delayLongPress,
      delayPressStart: delayPressIn,
      delayPressEnd: delayPressOut,
      onLongPress: onLongPress,
      onPress: onPress,
      onPressStart: function onPressStart(event) {
        var isGrant = event.dispatchConfig != null ? event.dispatchConfig.registrationName === 'onResponderGrant' : event.type === 'keydown';
        setOpacityActive(isGrant ? 0 : 150);
        if (onPressIn != null) {
          onPressIn(event);
        }
      },
      onPressEnd: function onPressEnd(event) {
        setOpacityInactive(250);
        if (onPressOut != null) {
          onPressOut(event);
        }
      }
    };
  }, [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination, setOpacityActive, setOpacityInactive]);
  var pressEventHandlers = (0, _usePressEvents.default)(hostRef, pressConfig);
  return React.createElement(_View.default, (0, _extends2.default)({}, rest, pressEventHandlers, {
    accessibilityDisabled: disabled,
    focusable: !disabled && focusable !== false,
    pointerEvents: disabled ? 'box-none' : undefined,
    ref: setRef,
    style: [styles.root, !disabled && styles.actionable, style, opacityOverride != null && {
      opacity: opacityOverride
    }, {
      transitionDuration: duration
    }]
  }));
}
var styles = _StyleSheet.default.create({
  root: {
    transitionProperty: 'opacity',
    transitionDuration: '0.15s',
    userSelect: 'none'
  },
  actionable: {
    cursor: 'pointer',
    touchAction: 'manipulation'
  }
});
var MemoedTouchableOpacity = React.memo(React.forwardRef(TouchableOpacity));
MemoedTouchableOpacity.displayName = 'TouchableOpacity';
var _default = exports.default = MemoedTouchableOpacity;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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