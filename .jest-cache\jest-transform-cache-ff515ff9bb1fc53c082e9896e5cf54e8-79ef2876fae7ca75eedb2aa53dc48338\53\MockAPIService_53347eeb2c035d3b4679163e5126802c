0dd60e42d448cbe1c35fcfd582ab481e
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import { env as _env } from "expo/virtual/env";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_24zsjsoctq() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\demo\\MockAPIService.ts";
  var hash = "8daa1b839ca83c89685127ec612928e64a5b6b66";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\demo\\MockAPIService.ts",
    statementMap: {
      "0": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 26,
          column: 67
        }
      },
      "1": {
        start: {
          line: 27,
          column: 4
        },
        end: {
          line: 27,
          column: 30
        }
      },
      "2": {
        start: {
          line: 28,
          column: 4
        },
        end: {
          line: 28,
          column: 28
        }
      },
      "3": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 35,
          column: 36
        }
      },
      "4": {
        start: {
          line: 35,
          column: 29
        },
        end: {
          line: 35,
          column: 36
        }
      },
      "5": {
        start: {
          line: 37,
          column: 18
        },
        end: {
          line: 37,
          column: 50
        }
      },
      "6": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 38,
          column: 61
        }
      },
      "7": {
        start: {
          line: 38,
          column: 33
        },
        end: {
          line: 38,
          column: 59
        }
      },
      "8": {
        start: {
          line: 45,
          column: 4
        },
        end: {
          line: 50,
          column: 6
        }
      },
      "9": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 57,
          column: 34
        }
      },
      "10": {
        start: {
          line: 59,
          column: 4
        },
        end: {
          line: 94,
          column: 5
        }
      },
      "11": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 75,
          column: 31
        }
      },
      "12": {
        start: {
          line: 78,
          column: 8
        },
        end: {
          line: 87,
          column: 43
        }
      },
      "13": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 66
        }
      },
      "14": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 93,
          column: 68
        }
      },
      "15": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 101,
          column: 38
        }
      },
      "16": {
        start: {
          line: 103,
          column: 26
        },
        end: {
          line: 125,
          column: 5
        }
      },
      "17": {
        start: {
          line: 127,
          column: 22
        },
        end: {
          line: 127,
          column: 41
        }
      },
      "18": {
        start: {
          line: 128,
          column: 27
        },
        end: {
          line: 128,
          column: 82
        }
      },
      "19": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 135,
          column: 32
        }
      },
      "20": {
        start: {
          line: 142,
          column: 4
        },
        end: {
          line: 142,
          column: 38
        }
      },
      "21": {
        start: {
          line: 144,
          column: 25
        },
        end: {
          line: 182,
          column: 5
        }
      },
      "22": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 189,
          column: 35
        }
      },
      "23": {
        start: {
          line: 196,
          column: 4
        },
        end: {
          line: 196,
          column: 34
        }
      },
      "24": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 208,
          column: 30
        }
      },
      "25": {
        start: {
          line: 215,
          column: 4
        },
        end: {
          line: 215,
          column: 34
        }
      },
      "26": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 221,
          column: 31
        }
      },
      "27": {
        start: {
          line: 228,
          column: 4
        },
        end: {
          line: 228,
          column: 34
        }
      },
      "28": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 230,
          column: 79
        }
      },
      "29": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 34
        }
      },
      "30": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 256,
          column: 38
        }
      },
      "31": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 263,
          column: 79
        }
      },
      "32": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 274,
          column: 47
        }
      },
      "33": {
        start: {
          line: 274,
          column: 35
        },
        end: {
          line: 274,
          column: 47
        }
      },
      "34": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 276,
          column: 66
        }
      },
      "35": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 299,
          column: 5
        }
      },
      "36": {
        start: {
          line: 280,
          column: 8
        },
        end: {
          line: 282,
          column: 9
        }
      },
      "37": {
        start: {
          line: 281,
          column: 10
        },
        end: {
          line: 281,
          column: 66
        }
      },
      "38": {
        start: {
          line: 283,
          column: 8
        },
        end: {
          line: 285,
          column: 9
        }
      },
      "39": {
        start: {
          line: 284,
          column: 10
        },
        end: {
          line: 284,
          column: 51
        }
      },
      "40": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 288,
          column: 9
        }
      },
      "41": {
        start: {
          line: 287,
          column: 10
        },
        end: {
          line: 287,
          column: 53
        }
      },
      "42": {
        start: {
          line: 289,
          column: 8
        },
        end: {
          line: 289,
          column: 14
        }
      },
      "43": {
        start: {
          line: 292,
          column: 8
        },
        end: {
          line: 292,
          column: 61
        }
      },
      "44": {
        start: {
          line: 295,
          column: 8
        },
        end: {
          line: 295,
          column: 75
        }
      },
      "45": {
        start: {
          line: 298,
          column: 8
        },
        end: {
          line: 298,
          column: 36
        }
      },
      "46": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 305,
          column: 6
        }
      },
      "47": {
        start: {
          line: 310,
          column: 30
        },
        end: {
          line: 310,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 25,
            column: 2
          },
          end: {
            line: 25,
            column: 3
          }
        },
        loc: {
          start: {
            line: 25,
            column: 16
          },
          end: {
            line: 29,
            column: 3
          }
        },
        line: 25
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 34,
            column: 2
          },
          end: {
            line: 34,
            column: 3
          }
        },
        loc: {
          start: {
            line: 34,
            column: 70
          },
          end: {
            line: 39,
            column: 3
          }
        },
        line: 34
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 38,
            column: 22
          },
          end: {
            line: 38,
            column: 23
          }
        },
        loc: {
          start: {
            line: 38,
            column: 33
          },
          end: {
            line: 38,
            column: 59
          }
        },
        line: 38
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 44,
            column: 2
          },
          end: {
            line: 44,
            column: 3
          }
        },
        loc: {
          start: {
            line: 44,
            column: 76
          },
          end: {
            line: 51,
            column: 3
          }
        },
        line: 44
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 56,
            column: 2
          },
          end: {
            line: 56,
            column: 3
          }
        },
        loc: {
          start: {
            line: 56,
            column: 98
          },
          end: {
            line: 95,
            column: 3
          }
        },
        line: 56
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 100,
            column: 2
          },
          end: {
            line: 100,
            column: 3
          }
        },
        loc: {
          start: {
            line: 100,
            column: 111
          },
          end: {
            line: 136,
            column: 3
          }
        },
        line: 100
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 141,
            column: 2
          },
          end: {
            line: 141,
            column: 3
          }
        },
        loc: {
          start: {
            line: 141,
            column: 83
          },
          end: {
            line: 190,
            column: 3
          }
        },
        line: 141
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 195,
            column: 2
          },
          end: {
            line: 195,
            column: 3
          }
        },
        loc: {
          start: {
            line: 195,
            column: 61
          },
          end: {
            line: 209,
            column: 3
          }
        },
        line: 195
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 214,
            column: 2
          },
          end: {
            line: 214,
            column: 3
          }
        },
        loc: {
          start: {
            line: 214,
            column: 63
          },
          end: {
            line: 222,
            column: 3
          }
        },
        line: 214
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 227,
            column: 2
          },
          end: {
            line: 227,
            column: 3
          }
        },
        loc: {
          start: {
            line: 227,
            column: 47
          },
          end: {
            line: 231,
            column: 3
          }
        },
        line: 227
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 236,
            column: 2
          },
          end: {
            line: 236,
            column: 3
          }
        },
        loc: {
          start: {
            line: 236,
            column: 50
          },
          end: {
            line: 257,
            column: 3
          }
        },
        line: 236
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 262,
            column: 2
          },
          end: {
            line: 262,
            column: 3
          }
        },
        loc: {
          start: {
            line: 262,
            column: 31
          },
          end: {
            line: 264,
            column: 3
          }
        },
        line: 262
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 269,
            column: 2
          },
          end: {
            line: 269,
            column: 3
          }
        },
        loc: {
          start: {
            line: 273,
            column: 34
          },
          end: {
            line: 306,
            column: 3
          }
        },
        line: 273
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 35,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 35,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "1": {
        loc: {
          start: {
            line: 37,
            column: 18
          },
          end: {
            line: 37,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 37,
            column: 18
          },
          end: {
            line: 37,
            column: 29
          }
        }, {
          start: {
            line: 37,
            column: 33
          },
          end: {
            line: 37,
            column: 50
          }
        }],
        line: 37
      },
      "2": {
        loc: {
          start: {
            line: 59,
            column: 4
          },
          end: {
            line: 94,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 60,
            column: 6
          },
          end: {
            line: 75,
            column: 31
          }
        }, {
          start: {
            line: 77,
            column: 6
          },
          end: {
            line: 87,
            column: 43
          }
        }, {
          start: {
            line: 89,
            column: 6
          },
          end: {
            line: 90,
            column: 66
          }
        }, {
          start: {
            line: 92,
            column: 6
          },
          end: {
            line: 93,
            column: 68
          }
        }],
        line: 59
      },
      "3": {
        loc: {
          start: {
            line: 64,
            column: 19
          },
          end: {
            line: 64,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 64,
            column: 19
          },
          end: {
            line: 64,
            column: 37
          }
        }, {
          start: {
            line: 64,
            column: 41
          },
          end: {
            line: 64,
            column: 59
          }
        }],
        line: 64
      },
      "4": {
        loc: {
          start: {
            line: 81,
            column: 19
          },
          end: {
            line: 81,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 81,
            column: 19
          },
          end: {
            line: 81,
            column: 37
          }
        }, {
          start: {
            line: 81,
            column: 41
          },
          end: {
            line: 81,
            column: 62
          }
        }],
        line: 81
      },
      "5": {
        loc: {
          start: {
            line: 82,
            column: 18
          },
          end: {
            line: 82,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 18
          },
          end: {
            line: 82,
            column: 35
          }
        }, {
          start: {
            line: 82,
            column: 39
          },
          end: {
            line: 82,
            column: 54
          }
        }],
        line: 82
      },
      "6": {
        loc: {
          start: {
            line: 100,
            column: 35
          },
          end: {
            line: 100,
            column: 86
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 100,
            column: 76
          },
          end: {
            line: 100,
            column: 86
          }
        }],
        line: 100
      },
      "7": {
        loc: {
          start: {
            line: 185,
            column: 16
          },
          end: {
            line: 185,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 16
          },
          end: {
            line: 185,
            column: 71
          }
        }, {
          start: {
            line: 185,
            column: 75
          },
          end: {
            line: 185,
            column: 102
          }
        }],
        line: 185
      },
      "8": {
        loc: {
          start: {
            line: 263,
            column: 11
          },
          end: {
            line: 263,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 11
          },
          end: {
            line: 263,
            column: 26
          }
        }, {
          start: {
            line: 263,
            column: 30
          },
          end: {
            line: 263,
            column: 78
          }
        }],
        line: 263
      },
      "9": {
        loc: {
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 274,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 274,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "10": {
        loc: {
          start: {
            line: 278,
            column: 4
          },
          end: {
            line: 299,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 279,
            column: 6
          },
          end: {
            line: 289,
            column: 14
          }
        }, {
          start: {
            line: 291,
            column: 6
          },
          end: {
            line: 292,
            column: 61
          }
        }, {
          start: {
            line: 294,
            column: 6
          },
          end: {
            line: 295,
            column: 75
          }
        }, {
          start: {
            line: 297,
            column: 6
          },
          end: {
            line: 298,
            column: 36
          }
        }],
        line: 278
      },
      "11": {
        loc: {
          start: {
            line: 280,
            column: 8
          },
          end: {
            line: 282,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 280,
            column: 8
          },
          end: {
            line: 282,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 280
      },
      "12": {
        loc: {
          start: {
            line: 281,
            column: 31
          },
          end: {
            line: 281,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 281,
            column: 31
          },
          end: {
            line: 281,
            column: 45
          }
        }, {
          start: {
            line: 281,
            column: 49
          },
          end: {
            line: 281,
            column: 56
          }
        }],
        line: 281
      },
      "13": {
        loc: {
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 285,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 285,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 283
      },
      "14": {
        loc: {
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 288,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 288,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 286
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0, 0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8daa1b839ca83c89685127ec612928e64a5b6b66"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_24zsjsoctq = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_24zsjsoctq();
import { DEMO_USERS, DEMO_MATCHES, DEMO_ANALYTICS } from "../../config/demo.config";
var MockAPIService = function () {
  function MockAPIService() {
    _classCallCheck(this, MockAPIService);
    cov_24zsjsoctq().f[0]++;
    cov_24zsjsoctq().s[0]++;
    this.isDemoMode = _env.EXPO_PUBLIC_DEMO_MODE === 'true';
    cov_24zsjsoctq().s[1]++;
    this.simulateDelay = true;
    cov_24zsjsoctq().s[2]++;
    this.defaultDelay = 500;
  }
  return _createClass(MockAPIService, [{
    key: "simulateAPIDelay",
    value: (function () {
      var _simulateAPIDelay = _asyncToGenerator(function* (customDelay) {
        cov_24zsjsoctq().f[1]++;
        cov_24zsjsoctq().s[3]++;
        if (!this.simulateDelay) {
          cov_24zsjsoctq().b[0][0]++;
          cov_24zsjsoctq().s[4]++;
          return;
        } else {
          cov_24zsjsoctq().b[0][1]++;
        }
        var delay = (cov_24zsjsoctq().s[5]++, (cov_24zsjsoctq().b[1][0]++, customDelay) || (cov_24zsjsoctq().b[1][1]++, this.defaultDelay));
        cov_24zsjsoctq().s[6]++;
        yield new Promise(function (resolve) {
          cov_24zsjsoctq().f[2]++;
          cov_24zsjsoctq().s[7]++;
          return setTimeout(resolve, delay);
        });
      });
      function simulateAPIDelay(_x) {
        return _simulateAPIDelay.apply(this, arguments);
      }
      return simulateAPIDelay;
    }())
  }, {
    key: "createMockResponse",
    value: function createMockResponse(data, message) {
      cov_24zsjsoctq().f[3]++;
      cov_24zsjsoctq().s[8]++;
      return {
        success: true,
        data: data,
        message: message,
        timestamp: Date.now()
      };
    }
  }, {
    key: "mockAuth",
    value: (function () {
      var _mockAuth = _asyncToGenerator(function* (action, credentials) {
        cov_24zsjsoctq().f[4]++;
        cov_24zsjsoctq().s[9]++;
        yield this.simulateAPIDelay();
        cov_24zsjsoctq().s[10]++;
        switch (action) {
          case 'login':
            cov_24zsjsoctq().b[2][0]++;
            cov_24zsjsoctq().s[11]++;
            return this.createMockResponse({
              user: {
                id: 'demo-user-123',
                email: (cov_24zsjsoctq().b[3][0]++, credentials == null ? void 0 : credentials.email) || (cov_24zsjsoctq().b[3][1]++, '<EMAIL>'),
                name: 'Demo User',
                avatar: 'https://via.placeholder.com/100x100/4CAF50/white?text=DU',
                subscription: 'premium',
                createdAt: new Date().toISOString()
              },
              session: {
                access_token: 'demo-access-token-123',
                refresh_token: 'demo-refresh-token-123',
                expires_at: Date.now() + 3600000
              }
            }, 'Login successful');
          case 'signup':
            cov_24zsjsoctq().b[2][1]++;
            cov_24zsjsoctq().s[12]++;
            return this.createMockResponse({
              user: {
                id: 'demo-user-new-456',
                email: (cov_24zsjsoctq().b[4][0]++, credentials == null ? void 0 : credentials.email) || (cov_24zsjsoctq().b[4][1]++, '<EMAIL>'),
                name: (cov_24zsjsoctq().b[5][0]++, credentials == null ? void 0 : credentials.name) || (cov_24zsjsoctq().b[5][1]++, 'New Demo User'),
                avatar: 'https://via.placeholder.com/100x100/2196F3/white?text=NU',
                subscription: 'free',
                createdAt: new Date().toISOString()
              }
            }, 'Account created successfully');
          case 'logout':
            cov_24zsjsoctq().b[2][2]++;
            cov_24zsjsoctq().s[13]++;
            return this.createMockResponse(null, 'Logout successful');
          default:
            cov_24zsjsoctq().b[2][3]++;
            cov_24zsjsoctq().s[14]++;
            return this.createMockResponse(null, 'Unknown auth action');
        }
      });
      function mockAuth(_x2, _x3) {
        return _mockAuth.apply(this, arguments);
      }
      return mockAuth;
    }())
  }, {
    key: "mockOpenAI",
    value: (function () {
      var _mockOpenAI = _asyncToGenerator(function* (prompt) {
        var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_24zsjsoctq().b[6][0]++, 'coaching');
        cov_24zsjsoctq().f[5]++;
        cov_24zsjsoctq().s[15]++;
        yield this.simulateAPIDelay(1000);
        var mockResponses = (cov_24zsjsoctq().s[16]++, {
          coaching: ["Great serve technique! Try to follow through more with your racket for increased power and spin.", "Your backhand form is improving. Focus on keeping your eye on the ball and rotating your hips.", "Excellent footwork in that rally! Remember to stay on your toes and be ready for the next shot.", "Your forehand has good power. Work on consistency by practicing your swing path.", "Nice volley technique! Try to get closer to the net for better angles."],
          analysis: ["Based on your match data, you're winning 78% of points when you serve to the opponent's backhand.", "Your unforced errors decrease by 40% when you take more time between points.", "You have a 65% win rate on break points - excellent mental toughness!", "Your first serve percentage is 68% - try to improve consistency for better results.", "You're most effective when playing aggressive baseline tennis with 72% point win rate."],
          chat: ["I'm here to help you improve your tennis game! What would you like to work on today?", "That's a great question about tennis strategy. Let me help you understand the best approach.", "Based on your playing style, I'd recommend focusing on these key areas for improvement.", "Tennis is all about consistency and smart shot selection. Let's work on your game plan.", "Remember, every professional player started as a beginner. Keep practicing and stay positive!"]
        });
        var responses = (cov_24zsjsoctq().s[17]++, mockResponses[type]);
        var randomResponse = (cov_24zsjsoctq().s[18]++, responses[Math.floor(Math.random() * responses.length)]);
        cov_24zsjsoctq().s[19]++;
        return this.createMockResponse({
          response: randomResponse,
          confidence: 0.85 + Math.random() * 0.15,
          tokens_used: Math.floor(Math.random() * 100) + 50,
          model: 'gpt-4-demo'
        }, 'AI response generated');
      });
      function mockOpenAI(_x4) {
        return _mockOpenAI.apply(this, arguments);
      }
      return mockOpenAI;
    }())
  }, {
    key: "mockReplicate",
    value: (function () {
      var _mockReplicate = _asyncToGenerator(function* (videoData, analysisType) {
        cov_24zsjsoctq().f[6]++;
        cov_24zsjsoctq().s[20]++;
        yield this.simulateAPIDelay(2000);
        var mockAnalysis = (cov_24zsjsoctq().s[21]++, {
          pose_detection: {
            keypoints: [{
              name: 'nose',
              x: 320,
              y: 180,
              confidence: 0.95
            }, {
              name: 'left_shoulder',
              x: 280,
              y: 220,
              confidence: 0.92
            }, {
              name: 'right_shoulder',
              x: 360,
              y: 220,
              confidence: 0.91
            }, {
              name: 'left_elbow',
              x: 250,
              y: 280,
              confidence: 0.88
            }, {
              name: 'right_elbow',
              x: 390,
              y: 280,
              confidence: 0.89
            }, {
              name: 'left_wrist',
              x: 220,
              y: 340,
              confidence: 0.85
            }, {
              name: 'right_wrist',
              x: 420,
              y: 340,
              confidence: 0.87
            }],
            technique_score: 85,
            recommendations: ['Keep your eye on the ball throughout the swing', 'Rotate your hips more for increased power', 'Follow through completely after contact']
          },
          swing_analysis: {
            swing_speed: 78,
            contact_point: {
              x: 380,
              y: 300
            },
            swing_path: 'inside-out',
            timing: 'good',
            power_rating: 82,
            consistency_rating: 76
          },
          match_highlights: {
            total_shots: 156,
            winners: 24,
            unforced_errors: 18,
            aces: 8,
            double_faults: 3,
            best_shots: [{
              timestamp: 45.2,
              type: 'forehand_winner',
              score: 95
            }, {
              timestamp: 127.8,
              type: 'ace',
              score: 98
            }, {
              timestamp: 203.5,
              type: 'backhand_winner',
              score: 88
            }]
          }
        });
        cov_24zsjsoctq().s[22]++;
        return this.createMockResponse({
          analysis: (cov_24zsjsoctq().b[7][0]++, mockAnalysis[analysisType]) || (cov_24zsjsoctq().b[7][1]++, mockAnalysis.pose_detection),
          processing_time: 1.8,
          video_duration: 45.6,
          frames_analyzed: 1368
        }, 'Video analysis completed');
      });
      function mockReplicate(_x5, _x6) {
        return _mockReplicate.apply(this, arguments);
      }
      return mockReplicate;
    }())
  }, {
    key: "mockUserData",
    value: (function () {
      var _mockUserData = _asyncToGenerator(function* (userId) {
        cov_24zsjsoctq().f[7]++;
        cov_24zsjsoctq().s[23]++;
        yield this.simulateAPIDelay();
        cov_24zsjsoctq().s[24]++;
        return this.createMockResponse({
          users: DEMO_USERS,
          currentUser: DEMO_USERS[0],
          stats: {
            totalMatches: 45,
            winRate: 78,
            averageMatchDuration: 105,
            favoriteShot: 'Forehand',
            improvementRate: 15
          }
        }, 'User data retrieved');
      });
      function mockUserData(_x7) {
        return _mockUserData.apply(this, arguments);
      }
      return mockUserData;
    }())
  }, {
    key: "mockMatchData",
    value: (function () {
      var _mockMatchData = _asyncToGenerator(function* (matchId) {
        cov_24zsjsoctq().f[8]++;
        cov_24zsjsoctq().s[25]++;
        yield this.simulateAPIDelay();
        cov_24zsjsoctq().s[26]++;
        return this.createMockResponse({
          matches: DEMO_MATCHES,
          currentMatch: DEMO_MATCHES[0],
          analytics: DEMO_ANALYTICS
        }, 'Match data retrieved');
      });
      function mockMatchData(_x8) {
        return _mockMatchData.apply(this, arguments);
      }
      return mockMatchData;
    }())
  }, {
    key: "mockAnalytics",
    value: (function () {
      var _mockAnalytics = _asyncToGenerator(function* () {
        cov_24zsjsoctq().f[9]++;
        cov_24zsjsoctq().s[27]++;
        yield this.simulateAPIDelay();
        cov_24zsjsoctq().s[28]++;
        return this.createMockResponse(DEMO_ANALYTICS, 'Analytics data retrieved');
      });
      function mockAnalytics() {
        return _mockAnalytics.apply(this, arguments);
      }
      return mockAnalytics;
    }())
  }, {
    key: "mockSubscription",
    value: (function () {
      var _mockSubscription = _asyncToGenerator(function* () {
        cov_24zsjsoctq().f[10]++;
        cov_24zsjsoctq().s[29]++;
        yield this.simulateAPIDelay();
        cov_24zsjsoctq().s[30]++;
        return this.createMockResponse({
          plan: 'premium',
          status: 'active',
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          features: ['AI Coaching', 'Video Analysis', 'Advanced Analytics', 'Unlimited Matches', 'Priority Support'],
          billing: {
            amount: 9.99,
            currency: 'USD',
            interval: 'month',
            next_billing_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
          }
        }, 'Subscription data retrieved');
      });
      function mockSubscription() {
        return _mockSubscription.apply(this, arguments);
      }
      return mockSubscription;
    }())
  }, {
    key: "shouldUseMockData",
    value: function shouldUseMockData() {
      cov_24zsjsoctq().f[11]++;
      cov_24zsjsoctq().s[31]++;
      return (cov_24zsjsoctq().b[8][0]++, this.isDemoMode) || (cov_24zsjsoctq().b[8][1]++, _env.EXPO_PUBLIC_USE_MOCK_DATA === 'true');
    }
  }, {
    key: "interceptAPICall",
    value: (function () {
      var _interceptAPICall = _asyncToGenerator(function* (service, method, params) {
        cov_24zsjsoctq().f[12]++;
        cov_24zsjsoctq().s[32]++;
        if (!this.shouldUseMockData()) {
          cov_24zsjsoctq().b[9][0]++;
          cov_24zsjsoctq().s[33]++;
          return null;
        } else {
          cov_24zsjsoctq().b[9][1]++;
        }
        cov_24zsjsoctq().s[34]++;
        console.log(`🎭 Mock API Call: ${service}.${method}`, params);
        cov_24zsjsoctq().s[35]++;
        switch (service) {
          case 'supabase':
            cov_24zsjsoctq().b[10][0]++;
            cov_24zsjsoctq().s[36]++;
            if (method.includes('auth')) {
              cov_24zsjsoctq().b[11][0]++;
              cov_24zsjsoctq().s[37]++;
              return this.mockAuth((cov_24zsjsoctq().b[12][0]++, params == null ? void 0 : params.action) || (cov_24zsjsoctq().b[12][1]++, 'login'), params);
            } else {
              cov_24zsjsoctq().b[11][1]++;
            }
            cov_24zsjsoctq().s[38]++;
            if (method.includes('user')) {
              cov_24zsjsoctq().b[13][0]++;
              cov_24zsjsoctq().s[39]++;
              return this.mockUserData(params == null ? void 0 : params.userId);
            } else {
              cov_24zsjsoctq().b[13][1]++;
            }
            cov_24zsjsoctq().s[40]++;
            if (method.includes('match')) {
              cov_24zsjsoctq().b[14][0]++;
              cov_24zsjsoctq().s[41]++;
              return this.mockMatchData(params == null ? void 0 : params.matchId);
            } else {
              cov_24zsjsoctq().b[14][1]++;
            }
            cov_24zsjsoctq().s[42]++;
            break;
          case 'openai':
            cov_24zsjsoctq().b[10][1]++;
            cov_24zsjsoctq().s[43]++;
            return this.mockOpenAI(params == null ? void 0 : params.prompt, params == null ? void 0 : params.type);
          case 'replicate':
            cov_24zsjsoctq().b[10][2]++;
            cov_24zsjsoctq().s[44]++;
            return this.mockReplicate(params == null ? void 0 : params.videoData, params == null ? void 0 : params.analysisType);
          case 'analytics':
            cov_24zsjsoctq().b[10][3]++;
            cov_24zsjsoctq().s[45]++;
            return this.mockAnalytics();
        }
        cov_24zsjsoctq().s[46]++;
        return this.createMockResponse({
          message: 'Mock data not implemented for this endpoint'
        }, `Mock response for ${service}.${method}`);
      });
      function interceptAPICall(_x9, _x0, _x1) {
        return _interceptAPICall.apply(this, arguments);
      }
      return interceptAPICall;
    }())
  }]);
}();
export var mockAPIService = (cov_24zsjsoctq().s[47]++, new MockAPIService());
export default mockAPIService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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