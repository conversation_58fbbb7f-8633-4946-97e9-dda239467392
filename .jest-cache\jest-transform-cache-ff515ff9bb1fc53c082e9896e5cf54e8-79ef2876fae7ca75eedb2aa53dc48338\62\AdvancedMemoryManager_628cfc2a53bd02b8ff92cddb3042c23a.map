{"version": 3, "names": ["AdvancedMemoryManager", "_classCallCheck", "memoryPools", "cov_2gb3samthe", "s", "Map", "memoryLeaks", "allocationHistory", "DEFAULT_POOL_CONFIGS", "image", "maxSize", "type", "video", "audio", "data", "compute", "cache", "f", "gcOptimization", "strategy", "frequency", "threshold", "batchSize", "pauseTime", "memoryPressure", "level", "availableMemory", "usedMemory", "totalMemory", "pressureRatio", "recommendations", "leakDetector", "MemoryLeakDetector", "gcScheduler", "GCScheduler", "initializeMemoryManager", "_createClass", "key", "value", "_initializeMemoryManager", "_asyncToGenerator", "createDefaultMemoryPools", "startMemoryMonitoring", "initialize", "start", "startPressureMonitoring", "console", "log", "error", "apply", "arguments", "_allocateMemory", "poolType", "size", "metadata", "length", "undefined", "b", "pool", "get", "Error", "canAllocate", "freeMemoryInPool", "allocationId", "generateAllocationId", "allocation", "id", "poolId", "timestamp", "Date", "now", "lastAccessed", "accessCount", "Object", "assign", "source", "priority", "compressed", "locked", "allocations", "set", "used", "available", "push", "updatePoolPerformance", "allocateMemory", "_x", "_x2", "_freeMemory", "targetPool", "values", "has", "warn", "delete", "freeMemory", "_x3", "_optimizeMemory", "aggressive", "<PERSON><PERSON><PERSON><PERSON>", "optimizedPools", "leaksResolved", "gcOptimizations", "poolFreed", "optimizeMemoryPool", "resolvedLeaks", "resolveMemoryLeaks", "optimizeGarbageCollection", "updateMemoryPressure", "optimizeMemory", "getMemoryMetrics", "totalAllocated", "totalAvailable", "poolUtilization", "totalFragmentation", "_ref", "entries", "_ref2", "_slicedToArray", "performance", "fragmentationRatio", "leakCount", "gcEfficiency", "getEfficiency", "_detectMemoryLeaks", "detectLeaks", "detectMemoryLeaks", "_createDefaultMemoryPools", "_ref3", "_ref4", "config", "createMemoryPool", "_createMemoryPool", "name", "char<PERSON>t", "toUpperCase", "slice", "configuration", "growthStrategy", "evictionPolicy", "compressionEnabled", "allocationSpeed", "hitRate", "compressionRatio", "_x4", "_x5", "_x6", "_freeMemoryInPool", "requiredSize", "freedSize", "allocationsToFree", "sortedAllocations", "Array", "from", "sort", "a", "_x7", "_x8", "_optimizeMemoryPool", "maxAge", "oldAllocations", "filter", "alloc", "defragmentPool", "_x9", "_x0", "_defragmentPool", "_x1", "_resolveMemoryLeaks", "leak", "resolved", "shouldResolve", "severity", "resolveLeak", "_x10", "_resolveLeak", "_x11", "_optimizeGarbageCollection", "optimizations", "updateConfiguration", "Math", "random", "toString", "substr", "allocCount", "avgAllocSize", "idealAllocCount", "min", "_updateMemoryPressure", "metrics", "usedRatio", "_this", "setInterval", "_this2", "suspiciousAllocations", "_initialize", "_detectLeaks", "leaks", "sourceGroups", "for<PERSON>ach", "entry", "_ref6", "_ref7", "totalSize", "reduce", "sum", "avgAge", "growthRate", "duration", "detectionTime", "_x12", "efficiency", "isRunning", "_start", "scheduleGC", "_updateConfiguration", "_x13", "_this3", "setTimeout", "performGC", "_performGC", "startTime", "aggressiveGC", "balancedGC", "conservativeGC", "adaptiveGC", "gcTime", "max", "_aggressiveGC", "Promise", "resolve", "_balancedGC", "_conservativeGC", "_adaptiveGC", "advancedMemoryManager"], "sources": ["AdvancedMemoryManager.ts"], "sourcesContent": ["/**\n * Advanced Memory Manager\n * \n * Intelligent memory optimization system with memory pools, leak detection,\n * garbage collection optimization, and pressure-aware memory management.\n */\n\nimport { Platform } from 'react-native';\nimport { performanceMonitor } from '@/utils/performance';\nimport { smartResourceManager } from '@/services/ai/SmartResourceManager';\nimport { nativeModuleManager } from './NativeModuleManager';\n\ninterface MemoryPool {\n  id: string;\n  name: string;\n  type: 'image' | 'video' | 'audio' | 'data' | 'compute' | 'cache';\n  size: number; // bytes\n  used: number; // bytes\n  available: number; // bytes\n  allocations: Map<string, MemoryAllocation>;\n  configuration: {\n    maxSize: number;\n    growthStrategy: 'fixed' | 'dynamic' | 'adaptive';\n    evictionPolicy: 'lru' | 'lfu' | 'fifo' | 'adaptive';\n    compressionEnabled: boolean;\n  };\n  performance: {\n    allocationSpeed: number; // allocations per second\n    fragmentationRatio: number; // 0-1\n    hitRate: number; // 0-1\n    compressionRatio: number; // 0-1\n  };\n}\n\ninterface MemoryAllocation {\n  id: string;\n  poolId: string;\n  size: number;\n  timestamp: number;\n  lastAccessed: number;\n  accessCount: number;\n  type: string;\n  metadata: {\n    source: string;\n    priority: 'low' | 'medium' | 'high' | 'critical';\n    compressed: boolean;\n    locked: boolean;\n  };\n}\n\ninterface MemoryLeak {\n  id: string;\n  source: string;\n  size: number;\n  duration: number; // milliseconds\n  growthRate: number; // bytes per second\n  severity: 'minor' | 'moderate' | 'severe' | 'critical';\n  stackTrace?: string;\n  detectionTime: number;\n  resolved: boolean;\n}\n\ninterface GCOptimization {\n  strategy: 'aggressive' | 'balanced' | 'conservative' | 'adaptive';\n  frequency: number; // milliseconds\n  threshold: number; // memory pressure threshold\n  batchSize: number; // objects to collect per cycle\n  pauseTime: number; // maximum GC pause time\n}\n\ninterface MemoryPressureLevel {\n  level: 'normal' | 'warning' | 'critical' | 'emergency';\n  availableMemory: number;\n  usedMemory: number;\n  totalMemory: number;\n  pressureRatio: number; // 0-1\n  recommendations: string[];\n}\n\n/**\n * Advanced Memory Management System\n */\nclass AdvancedMemoryManager {\n  private memoryPools: Map<string, MemoryPool> = new Map();\n  private memoryLeaks: Map<string, MemoryLeak> = new Map();\n  private gcOptimization: GCOptimization;\n  private memoryPressure: MemoryPressureLevel;\n  private allocationHistory: Array<{ timestamp: number; allocation: MemoryAllocation }> = [];\n  private leakDetector: MemoryLeakDetector;\n  private gcScheduler: GCScheduler;\n  \n  private readonly DEFAULT_POOL_CONFIGS = {\n    image: { maxSize: 128 * 1024 * 1024, type: 'image' as const }, // 128MB\n    video: { maxSize: 256 * 1024 * 1024, type: 'video' as const }, // 256MB\n    audio: { maxSize: 64 * 1024 * 1024, type: 'audio' as const }, // 64MB\n    data: { maxSize: 64 * 1024 * 1024, type: 'data' as const }, // 64MB\n    compute: { maxSize: 128 * 1024 * 1024, type: 'compute' as const }, // 128MB\n    cache: { maxSize: 32 * 1024 * 1024, type: 'cache' as const }, // 32MB\n  };\n\n  constructor() {\n    this.gcOptimization = {\n      strategy: 'adaptive',\n      frequency: 30000, // 30 seconds\n      threshold: 0.8, // 80% memory pressure\n      batchSize: 100,\n      pauseTime: 16, // 16ms max pause\n    };\n    \n    this.memoryPressure = {\n      level: 'normal',\n      availableMemory: 0,\n      usedMemory: 0,\n      totalMemory: 0,\n      pressureRatio: 0,\n      recommendations: [],\n    };\n    \n    this.leakDetector = new MemoryLeakDetector();\n    this.gcScheduler = new GCScheduler(this.gcOptimization);\n    \n    this.initializeMemoryManager();\n  }\n\n  /**\n   * Initialize advanced memory management\n   */\n  private async initializeMemoryManager(): Promise<void> {\n    try {\n      // Create default memory pools\n      await this.createDefaultMemoryPools();\n      \n      // Start memory monitoring\n      this.startMemoryMonitoring();\n      \n      // Initialize leak detection\n      await this.leakDetector.initialize();\n      \n      // Start GC optimization\n      await this.gcScheduler.start();\n      \n      // Start pressure monitoring\n      this.startPressureMonitoring();\n      \n      console.log('Advanced Memory Manager initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Advanced Memory Manager:', error);\n    }\n  }\n\n  /**\n   * Allocate memory from pool\n   */\n  async allocateMemory(\n    poolType: string,\n    size: number,\n    metadata: Partial<MemoryAllocation['metadata']> = {}\n  ): Promise<string> {\n    try {\n      const pool = this.memoryPools.get(poolType);\n      if (!pool) {\n        throw new Error(`Memory pool not found: ${poolType}`);\n      }\n\n      // Check if allocation is possible\n      if (!this.canAllocate(pool, size)) {\n        // Try to free memory\n        await this.freeMemoryInPool(pool, size);\n        \n        // Check again\n        if (!this.canAllocate(pool, size)) {\n          throw new Error(`Insufficient memory in pool: ${poolType}`);\n        }\n      }\n\n      // Create allocation\n      const allocationId = this.generateAllocationId();\n      const allocation: MemoryAllocation = {\n        id: allocationId,\n        poolId: pool.id,\n        size,\n        timestamp: Date.now(),\n        lastAccessed: Date.now(),\n        accessCount: 1,\n        type: poolType,\n        metadata: {\n          source: 'unknown',\n          priority: 'medium',\n          compressed: false,\n          locked: false,\n          ...metadata,\n        },\n      };\n\n      // Add to pool\n      pool.allocations.set(allocationId, allocation);\n      pool.used += size;\n      pool.available -= size;\n\n      // Track allocation\n      this.allocationHistory.push({ timestamp: Date.now(), allocation });\n      \n      // Update performance metrics\n      this.updatePoolPerformance(pool);\n      \n      console.log(`Allocated ${size} bytes in pool ${poolType}`);\n      return allocationId;\n\n    } catch (error) {\n      console.error('Failed to allocate memory:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Free memory allocation\n   */\n  async freeMemory(allocationId: string): Promise<void> {\n    try {\n      // Find allocation across all pools\n      let targetPool: MemoryPool | null = null;\n      let allocation: MemoryAllocation | null = null;\n\n      for (const pool of this.memoryPools.values()) {\n        if (pool.allocations.has(allocationId)) {\n          targetPool = pool;\n          allocation = pool.allocations.get(allocationId)!;\n          break;\n        }\n      }\n\n      if (!targetPool || !allocation) {\n        console.warn(`Memory allocation not found: ${allocationId}`);\n        return;\n      }\n\n      // Remove allocation\n      targetPool.allocations.delete(allocationId);\n      targetPool.used -= allocation.size;\n      targetPool.available += allocation.size;\n\n      // Update performance metrics\n      this.updatePoolPerformance(targetPool);\n      \n      console.log(`Freed ${allocation.size} bytes from pool ${targetPool.id}`);\n\n    } catch (error) {\n      console.error('Failed to free memory:', error);\n    }\n  }\n\n  /**\n   * Optimize memory usage\n   */\n  async optimizeMemory(aggressive: boolean = false): Promise<{\n    freedMemory: number;\n    optimizedPools: number;\n    leaksResolved: number;\n    gcOptimizations: number;\n  }> {\n    try {\n      console.log(`Starting memory optimization (aggressive: ${aggressive})`);\n      \n      let freedMemory = 0;\n      let optimizedPools = 0;\n      let leaksResolved = 0;\n      let gcOptimizations = 0;\n\n      // Optimize memory pools\n      for (const pool of this.memoryPools.values()) {\n        const poolFreed = await this.optimizeMemoryPool(pool, aggressive);\n        if (poolFreed > 0) {\n          freedMemory += poolFreed;\n          optimizedPools++;\n        }\n      }\n\n      // Resolve memory leaks\n      const resolvedLeaks = await this.resolveMemoryLeaks(aggressive);\n      leaksResolved = resolvedLeaks.length;\n\n      // Optimize garbage collection\n      if (aggressive) {\n        gcOptimizations = await this.optimizeGarbageCollection();\n      }\n\n      // Update memory pressure\n      await this.updateMemoryPressure();\n\n      console.log(`Memory optimization completed: ${freedMemory} bytes freed`);\n      \n      return { freedMemory, optimizedPools, leaksResolved, gcOptimizations };\n\n    } catch (error) {\n      console.error('Failed to optimize memory:', error);\n      return { freedMemory: 0, optimizedPools: 0, leaksResolved: 0, gcOptimizations: 0 };\n    }\n  }\n\n  /**\n   * Get memory usage metrics\n   */\n  getMemoryMetrics(): {\n    totalAllocated: number;\n    totalAvailable: number;\n    poolUtilization: Record<string, number>;\n    memoryPressure: MemoryPressureLevel;\n    leakCount: number;\n    gcEfficiency: number;\n    fragmentationRatio: number;\n  } {\n    let totalAllocated = 0;\n    let totalAvailable = 0;\n    const poolUtilization: Record<string, number> = {};\n    let totalFragmentation = 0;\n\n    for (const [poolId, pool] of this.memoryPools.entries()) {\n      totalAllocated += pool.used;\n      totalAvailable += pool.available;\n      poolUtilization[poolId] = (pool.used / pool.size) * 100;\n      totalFragmentation += pool.performance.fragmentationRatio;\n    }\n\n    const fragmentationRatio = this.memoryPools.size > 0\n      ? totalFragmentation / this.memoryPools.size\n      : 0;\n\n    return {\n      totalAllocated,\n      totalAvailable,\n      poolUtilization,\n      memoryPressure: this.memoryPressure,\n      leakCount: this.memoryLeaks.size,\n      gcEfficiency: this.gcScheduler.getEfficiency(),\n      fragmentationRatio,\n    };\n  }\n\n  /**\n   * Detect and report memory leaks\n   */\n  async detectMemoryLeaks(): Promise<MemoryLeak[]> {\n    return await this.leakDetector.detectLeaks(this.allocationHistory);\n  }\n\n  // Private helper methods\n\n  private async createDefaultMemoryPools(): Promise<void> {\n    for (const [poolType, config] of Object.entries(this.DEFAULT_POOL_CONFIGS)) {\n      await this.createMemoryPool(poolType, config.maxSize, config.type);\n    }\n  }\n\n  private async createMemoryPool(\n    poolId: string,\n    maxSize: number,\n    type: MemoryPool['type']\n  ): Promise<void> {\n    const pool: MemoryPool = {\n      id: poolId,\n      name: `${type.charAt(0).toUpperCase() + type.slice(1)} Pool`,\n      type,\n      size: maxSize,\n      used: 0,\n      available: maxSize,\n      allocations: new Map(),\n      configuration: {\n        maxSize,\n        growthStrategy: 'adaptive',\n        evictionPolicy: 'lru',\n        compressionEnabled: type === 'image' || type === 'video',\n      },\n      performance: {\n        allocationSpeed: 0,\n        fragmentationRatio: 0,\n        hitRate: 0,\n        compressionRatio: 0,\n      },\n    };\n\n    this.memoryPools.set(poolId, pool);\n    console.log(`Created memory pool: ${poolId} (${maxSize} bytes)`);\n  }\n\n  private canAllocate(pool: MemoryPool, size: number): boolean {\n    return pool.available >= size;\n  }\n\n  private async freeMemoryInPool(pool: MemoryPool, requiredSize: number): Promise<number> {\n    let freedSize = 0;\n    const allocationsToFree: string[] = [];\n\n    // Sort allocations by eviction policy\n    const sortedAllocations = Array.from(pool.allocations.values()).sort((a, b) => {\n      switch (pool.configuration.evictionPolicy) {\n        case 'lru':\n          return a.lastAccessed - b.lastAccessed;\n        case 'lfu':\n          return a.accessCount - b.accessCount;\n        case 'fifo':\n          return a.timestamp - b.timestamp;\n        default:\n          return a.lastAccessed - b.lastAccessed;\n      }\n    });\n\n    // Free allocations until we have enough space\n    for (const allocation of sortedAllocations) {\n      if (allocation.metadata.locked) continue;\n      \n      allocationsToFree.push(allocation.id);\n      freedSize += allocation.size;\n      \n      if (freedSize >= requiredSize) break;\n    }\n\n    // Free the selected allocations\n    for (const allocationId of allocationsToFree) {\n      await this.freeMemory(allocationId);\n    }\n\n    return freedSize;\n  }\n\n  private async optimizeMemoryPool(pool: MemoryPool, aggressive: boolean): Promise<number> {\n    let freedMemory = 0;\n\n    // Remove old allocations\n    const now = Date.now();\n    const maxAge = aggressive ? 300000 : 600000; // 5 or 10 minutes\n    \n    const oldAllocations = Array.from(pool.allocations.values())\n      .filter(alloc => \n        !alloc.metadata.locked && \n        (now - alloc.lastAccessed) > maxAge\n      );\n\n    for (const allocation of oldAllocations) {\n      await this.freeMemory(allocation.id);\n      freedMemory += allocation.size;\n    }\n\n    // Defragment pool if needed\n    if (pool.performance.fragmentationRatio > 0.3) {\n      await this.defragmentPool(pool);\n    }\n\n    return freedMemory;\n  }\n\n  private async defragmentPool(pool: MemoryPool): Promise<void> {\n    // Simulate pool defragmentation\n    console.log(`Defragmenting pool: ${pool.id}`);\n    pool.performance.fragmentationRatio *= 0.5; // Reduce fragmentation by 50%\n  }\n\n  private async resolveMemoryLeaks(aggressive: boolean): Promise<MemoryLeak[]> {\n    const resolvedLeaks: MemoryLeak[] = [];\n    \n    for (const leak of this.memoryLeaks.values()) {\n      if (leak.resolved) continue;\n      \n      const shouldResolve = aggressive || leak.severity === 'critical' || leak.severity === 'severe';\n      \n      if (shouldResolve) {\n        // Attempt to resolve the leak\n        const resolved = await this.resolveLeak(leak);\n        if (resolved) {\n          leak.resolved = true;\n          resolvedLeaks.push(leak);\n        }\n      }\n    }\n\n    return resolvedLeaks;\n  }\n\n  private async resolveLeak(leak: MemoryLeak): Promise<boolean> {\n    try {\n      // Simulate leak resolution\n      console.log(`Resolving memory leak: ${leak.id} (${leak.size} bytes)`);\n      \n      // In a real implementation, this would:\n      // 1. Identify the source of the leak\n      // 2. Force cleanup of leaked objects\n      // 3. Update references to prevent future leaks\n      \n      return true;\n    } catch (error) {\n      console.error(`Failed to resolve leak ${leak.id}:`, error);\n      return false;\n    }\n  }\n\n  private async optimizeGarbageCollection(): Promise<number> {\n    // Optimize GC settings based on current memory pressure\n    let optimizations = 0;\n    \n    if (this.memoryPressure.level === 'critical' || this.memoryPressure.level === 'emergency') {\n      // Aggressive GC\n      this.gcOptimization.strategy = 'aggressive';\n      this.gcOptimization.frequency = 10000; // 10 seconds\n      optimizations++;\n    } else if (this.memoryPressure.level === 'warning') {\n      // Balanced GC\n      this.gcOptimization.strategy = 'balanced';\n      this.gcOptimization.frequency = 20000; // 20 seconds\n      optimizations++;\n    }\n    \n    // Update GC scheduler\n    await this.gcScheduler.updateConfiguration(this.gcOptimization);\n    \n    return optimizations;\n  }\n\n  private generateAllocationId(): string {\n    return `alloc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private updatePoolPerformance(pool: MemoryPool): void {\n    // Update fragmentation ratio\n    const allocCount = pool.allocations.size;\n    const avgAllocSize = allocCount > 0 ? pool.used / allocCount : 0;\n    const idealAllocCount = avgAllocSize > 0 ? pool.size / avgAllocSize : 0;\n    pool.performance.fragmentationRatio = idealAllocCount > 0 ? 1 - (allocCount / idealAllocCount) : 0;\n    \n    // Update hit rate (simplified)\n    pool.performance.hitRate = Math.min(pool.used / pool.size, 1);\n  }\n\n  private async updateMemoryPressure(): Promise<void> {\n    // Get current memory usage\n    const metrics = this.getMemoryMetrics();\n    const totalMemory = metrics.totalAllocated + metrics.totalAvailable;\n    const usedRatio = totalMemory > 0 ? metrics.totalAllocated / totalMemory : 0;\n\n    // Determine pressure level\n    let level: MemoryPressureLevel['level'];\n    if (usedRatio < 0.6) level = 'normal';\n    else if (usedRatio < 0.8) level = 'warning';\n    else if (usedRatio < 0.95) level = 'critical';\n    else level = 'emergency';\n\n    // Generate recommendations\n    const recommendations: string[] = [];\n    if (level === 'warning') {\n      recommendations.push('Consider freeing unused allocations');\n    } else if (level === 'critical') {\n      recommendations.push('Free memory immediately');\n      recommendations.push('Reduce image/video quality');\n    } else if (level === 'emergency') {\n      recommendations.push('Emergency memory cleanup required');\n      recommendations.push('Close non-essential features');\n    }\n\n    this.memoryPressure = {\n      level,\n      availableMemory: metrics.totalAvailable,\n      usedMemory: metrics.totalAllocated,\n      totalMemory,\n      pressureRatio: usedRatio,\n      recommendations,\n    };\n  }\n\n  private startMemoryMonitoring(): void {\n    // Monitor memory every 10 seconds\n    setInterval(() => {\n      this.updateMemoryPressure();\n    }, 10000);\n  }\n\n  private startPressureMonitoring(): void {\n    // Check for memory pressure every 5 seconds\n    setInterval(async () => {\n      if (this.memoryPressure.level === 'critical' || this.memoryPressure.level === 'emergency') {\n        await this.optimizeMemory(true);\n      }\n    }, 5000);\n  }\n}\n\n/**\n * Memory Leak Detector\n */\nclass MemoryLeakDetector {\n  private suspiciousAllocations: Map<string, number> = new Map();\n\n  async initialize(): Promise<void> {\n    console.log('Memory Leak Detector initialized');\n  }\n\n  async detectLeaks(\n    allocationHistory: Array<{ timestamp: number; allocation: MemoryAllocation }>\n  ): Promise<MemoryLeak[]> {\n    const leaks: MemoryLeak[] = [];\n    const now = Date.now();\n    \n    // Analyze allocation patterns\n    const sourceGroups = new Map<string, MemoryAllocation[]>();\n    \n    allocationHistory.forEach(entry => {\n      const source = entry.allocation.metadata.source;\n      if (!sourceGroups.has(source)) {\n        sourceGroups.set(source, []);\n      }\n      sourceGroups.get(source)!.push(entry.allocation);\n    });\n\n    // Check for potential leaks\n    for (const [source, allocations] of sourceGroups.entries()) {\n      const totalSize = allocations.reduce((sum, alloc) => sum + alloc.size, 0);\n      const avgAge = allocations.reduce((sum, alloc) => sum + (now - alloc.timestamp), 0) / allocations.length;\n      \n      // Detect leak patterns\n      if (allocations.length > 100 && avgAge > 600000) { // More than 100 allocations older than 10 minutes\n        const growthRate = totalSize / (avgAge / 1000); // bytes per second\n        \n        let severity: MemoryLeak['severity'];\n        if (growthRate > 1024 * 1024) severity = 'critical'; // 1MB/s\n        else if (growthRate > 512 * 1024) severity = 'severe'; // 512KB/s\n        else if (growthRate > 256 * 1024) severity = 'moderate'; // 256KB/s\n        else severity = 'minor';\n\n        leaks.push({\n          id: `leak_${source}_${Date.now()}`,\n          source,\n          size: totalSize,\n          duration: avgAge,\n          growthRate,\n          severity,\n          detectionTime: now,\n          resolved: false,\n        });\n      }\n    }\n\n    return leaks;\n  }\n}\n\n/**\n * Garbage Collection Scheduler\n */\nclass GCScheduler {\n  private configuration: GCOptimization;\n  private efficiency = 0.8;\n  private isRunning = false;\n\n  constructor(config: GCOptimization) {\n    this.configuration = config;\n  }\n\n  async start(): Promise<void> {\n    if (this.isRunning) return;\n    \n    this.isRunning = true;\n    this.scheduleGC();\n    console.log('GC Scheduler started');\n  }\n\n  async updateConfiguration(config: GCOptimization): Promise<void> {\n    this.configuration = config;\n    console.log('GC configuration updated');\n  }\n\n  getEfficiency(): number {\n    return this.efficiency;\n  }\n\n  private scheduleGC(): void {\n    if (!this.isRunning) return;\n    \n    setTimeout(() => {\n      this.performGC();\n      this.scheduleGC(); // Schedule next GC\n    }, this.configuration.frequency);\n  }\n\n  private async performGC(): Promise<void> {\n    try {\n      // Simulate garbage collection\n      const startTime = Date.now();\n      \n      // Perform GC based on strategy\n      switch (this.configuration.strategy) {\n        case 'aggressive':\n          await this.aggressiveGC();\n          break;\n        case 'balanced':\n          await this.balancedGC();\n          break;\n        case 'conservative':\n          await this.conservativeGC();\n          break;\n        case 'adaptive':\n          await this.adaptiveGC();\n          break;\n      }\n      \n      const gcTime = Date.now() - startTime;\n      this.efficiency = Math.max(0, 1 - (gcTime / this.configuration.pauseTime));\n      \n    } catch (error) {\n      console.error('GC execution failed:', error);\n    }\n  }\n\n  private async aggressiveGC(): Promise<void> {\n    // Simulate aggressive garbage collection\n    await new Promise(resolve => setTimeout(resolve, 8));\n  }\n\n  private async balancedGC(): Promise<void> {\n    // Simulate balanced garbage collection\n    await new Promise(resolve => setTimeout(resolve, 12));\n  }\n\n  private async conservativeGC(): Promise<void> {\n    // Simulate conservative garbage collection\n    await new Promise(resolve => setTimeout(resolve, 16));\n  }\n\n  private async adaptiveGC(): Promise<void> {\n    // Simulate adaptive garbage collection\n    await new Promise(resolve => setTimeout(resolve, 10));\n  }\n}\n\n// Export singleton instance\nexport const advancedMemoryManager = new AdvancedMemoryManager();\nexport default advancedMemoryManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAkFMA,qBAAqB;EAkBzB,SAAAA,sBAAA,EAAc;IAAAC,eAAA,OAAAD,qBAAA;IAAA,KAjBNE,WAAW,IAAAC,cAAA,GAAAC,CAAA,OAA4B,IAAIC,GAAG,CAAC,CAAC;IAAA,KAChDC,WAAW,IAAAH,cAAA,GAAAC,CAAA,OAA4B,IAAIC,GAAG,CAAC,CAAC;IAAA,KAGhDE,iBAAiB,IAAAJ,cAAA,GAAAC,CAAA,OAA+D,EAAE;IAAA,KAIzEI,oBAAoB,IAAAL,cAAA,GAAAC,CAAA,OAAG;MACtCK,KAAK,EAAE;QAAEC,OAAO,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;QAAEC,IAAI,EAAE;MAAiB,CAAC;MAC7DC,KAAK,EAAE;QAAEF,OAAO,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;QAAEC,IAAI,EAAE;MAAiB,CAAC;MAC7DE,KAAK,EAAE;QAAEH,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;QAAEC,IAAI,EAAE;MAAiB,CAAC;MAC5DG,IAAI,EAAE;QAAEJ,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;QAAEC,IAAI,EAAE;MAAgB,CAAC;MAC1DI,OAAO,EAAE;QAAEL,OAAO,EAAE,GAAG,GAAG,IAAI,GAAG,IAAI;QAAEC,IAAI,EAAE;MAAmB,CAAC;MACjEK,KAAK,EAAE;QAAEN,OAAO,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;QAAEC,IAAI,EAAE;MAAiB;IAC7D,CAAC;IAAAR,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAC,CAAA;IAGC,IAAI,CAACc,cAAc,GAAG;MACpBC,QAAQ,EAAE,UAAU;MACpBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE,GAAG;MACdC,SAAS,EAAE;IACb,CAAC;IAACpB,cAAA,GAAAC,CAAA;IAEF,IAAI,CAACoB,cAAc,GAAG;MACpBC,KAAK,EAAE,QAAQ;MACfC,eAAe,EAAE,CAAC;MAClBC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,aAAa,EAAE,CAAC;MAChBC,eAAe,EAAE;IACnB,CAAC;IAAC3B,cAAA,GAAAC,CAAA;IAEF,IAAI,CAAC2B,YAAY,GAAG,IAAIC,kBAAkB,CAAC,CAAC;IAAC7B,cAAA,GAAAC,CAAA;IAC7C,IAAI,CAAC6B,WAAW,GAAG,IAAIC,WAAW,CAAC,IAAI,CAAChB,cAAc,CAAC;IAACf,cAAA,GAAAC,CAAA;IAExD,IAAI,CAAC+B,uBAAuB,CAAC,CAAC;EAChC;EAAC,OAAAC,YAAA,CAAApC,qBAAA;IAAAqC,GAAA;IAAAC,KAAA;MAAA,IAAAC,wBAAA,GAAAC,iBAAA,CAKD,aAAuD;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QACrD,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF,MAAM,IAAI,CAACqC,wBAAwB,CAAC,CAAC;UAACtC,cAAA,GAAAC,CAAA;UAGtC,IAAI,CAACsC,qBAAqB,CAAC,CAAC;UAACvC,cAAA,GAAAC,CAAA;UAG7B,MAAM,IAAI,CAAC2B,YAAY,CAACY,UAAU,CAAC,CAAC;UAACxC,cAAA,GAAAC,CAAA;UAGrC,MAAM,IAAI,CAAC6B,WAAW,CAACW,KAAK,CAAC,CAAC;UAACzC,cAAA,GAAAC,CAAA;UAG/B,IAAI,CAACyC,uBAAuB,CAAC,CAAC;UAAC1C,cAAA,GAAAC,CAAA;UAE/B0C,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QACjE,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAA7C,cAAA,GAAAC,CAAA;UACd0C,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACvE;MACF,CAAC;MAAA,SArBab,uBAAuBA,CAAA;QAAA,OAAAI,wBAAA,CAAAU,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAvBf,uBAAuB;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA;MAAA,IAAAa,eAAA,GAAAX,iBAAA,CA0BrC,WACEY,QAAgB,EAChBC,IAAY,EAEK;QAAA,IADjBC,QAA+C,GAAAJ,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,OAAA/C,cAAA,GAAAsD,CAAA,UAAG,CAAC,CAAC;QAAAtD,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAEpD,IAAI;UACF,IAAMsD,IAAI,IAAAvD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,WAAW,CAACyD,GAAG,CAACP,QAAQ,CAAC;UAACjD,cAAA,GAAAC,CAAA;UAC5C,IAAI,CAACsD,IAAI,EAAE;YAAAvD,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YACT,MAAM,IAAIwD,KAAK,CAAC,0BAA0BR,QAAQ,EAAE,CAAC;UACvD,CAAC;YAAAjD,cAAA,GAAAsD,CAAA;UAAA;UAAAtD,cAAA,GAAAC,CAAA;UAGD,IAAI,CAAC,IAAI,CAACyD,WAAW,CAACH,IAAI,EAAEL,IAAI,CAAC,EAAE;YAAAlD,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAEjC,MAAM,IAAI,CAAC0D,gBAAgB,CAACJ,IAAI,EAAEL,IAAI,CAAC;YAAClD,cAAA,GAAAC,CAAA;YAGxC,IAAI,CAAC,IAAI,CAACyD,WAAW,CAACH,IAAI,EAAEL,IAAI,CAAC,EAAE;cAAAlD,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACjC,MAAM,IAAIwD,KAAK,CAAC,gCAAgCR,QAAQ,EAAE,CAAC;YAC7D,CAAC;cAAAjD,cAAA,GAAAsD,CAAA;YAAA;UACH,CAAC;YAAAtD,cAAA,GAAAsD,CAAA;UAAA;UAGD,IAAMM,YAAY,IAAA5D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC4D,oBAAoB,CAAC,CAAC;UAChD,IAAMC,UAA4B,IAAA9D,cAAA,GAAAC,CAAA,QAAG;YACnC8D,EAAE,EAAEH,YAAY;YAChBI,MAAM,EAAET,IAAI,CAACQ,EAAE;YACfb,IAAI,EAAJA,IAAI;YACJe,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YACrBC,YAAY,EAAEF,IAAI,CAACC,GAAG,CAAC,CAAC;YACxBE,WAAW,EAAE,CAAC;YACd7D,IAAI,EAAEyC,QAAQ;YACdE,QAAQ,EAAAmB,MAAA,CAAAC,MAAA;cACNC,MAAM,EAAE,SAAS;cACjBC,QAAQ,EAAE,QAAQ;cAClBC,UAAU,EAAE,KAAK;cACjBC,MAAM,EAAE;YAAK,GACVxB,QAAQ;UAEf,CAAC;UAACnD,cAAA,GAAAC,CAAA;UAGFsD,IAAI,CAACqB,WAAW,CAACC,GAAG,CAACjB,YAAY,EAAEE,UAAU,CAAC;UAAC9D,cAAA,GAAAC,CAAA;UAC/CsD,IAAI,CAACuB,IAAI,IAAI5B,IAAI;UAAClD,cAAA,GAAAC,CAAA;UAClBsD,IAAI,CAACwB,SAAS,IAAI7B,IAAI;UAAClD,cAAA,GAAAC,CAAA;UAGvB,IAAI,CAACG,iBAAiB,CAAC4E,IAAI,CAAC;YAAEf,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YAAEL,UAAU,EAAVA;UAAW,CAAC,CAAC;UAAC9D,cAAA,GAAAC,CAAA;UAGnE,IAAI,CAACgF,qBAAqB,CAAC1B,IAAI,CAAC;UAACvD,cAAA,GAAAC,CAAA;UAEjC0C,OAAO,CAACC,GAAG,CAAC,aAAaM,IAAI,kBAAkBD,QAAQ,EAAE,CAAC;UAACjD,cAAA,GAAAC,CAAA;UAC3D,OAAO2D,YAAY;QAErB,CAAC,CAAC,OAAOf,KAAK,EAAE;UAAA7C,cAAA,GAAAC,CAAA;UACd0C,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAAC7C,cAAA,GAAAC,CAAA;UACnD,MAAM4C,KAAK;QACb;MACF,CAAC;MAAA,SA3DKqC,cAAcA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAApC,eAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdmC,cAAc;IAAA;EAAA;IAAAhD,GAAA;IAAAC,KAAA;MAAA,IAAAkD,WAAA,GAAAhD,iBAAA,CAgEpB,WAAiBuB,YAAoB,EAAiB;QAAA5D,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QACpD,IAAI;UAEF,IAAIqF,UAA6B,IAAAtF,cAAA,GAAAC,CAAA,QAAG,IAAI;UACxC,IAAI6D,UAAmC,IAAA9D,cAAA,GAAAC,CAAA,QAAG,IAAI;UAACD,cAAA,GAAAC,CAAA;UAE/C,KAAK,IAAMsD,IAAI,IAAI,IAAI,CAACxD,WAAW,CAACwF,MAAM,CAAC,CAAC,EAAE;YAAAvF,cAAA,GAAAC,CAAA;YAC5C,IAAIsD,IAAI,CAACqB,WAAW,CAACY,GAAG,CAAC5B,YAAY,CAAC,EAAE;cAAA5D,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACtCqF,UAAU,GAAG/B,IAAI;cAACvD,cAAA,GAAAC,CAAA;cAClB6D,UAAU,GAAGP,IAAI,CAACqB,WAAW,CAACpB,GAAG,CAACI,YAAY,CAAE;cAAC5D,cAAA,GAAAC,CAAA;cACjD;YACF,CAAC;cAAAD,cAAA,GAAAsD,CAAA;YAAA;UACH;UAACtD,cAAA,GAAAC,CAAA;UAED,IAAI,CAAAD,cAAA,GAAAsD,CAAA,WAACgC,UAAU,MAAAtF,cAAA,GAAAsD,CAAA,UAAI,CAACQ,UAAU,GAAE;YAAA9D,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAC9B0C,OAAO,CAAC8C,IAAI,CAAC,gCAAgC7B,YAAY,EAAE,CAAC;YAAC5D,cAAA,GAAAC,CAAA;YAC7D;UACF,CAAC;YAAAD,cAAA,GAAAsD,CAAA;UAAA;UAAAtD,cAAA,GAAAC,CAAA;UAGDqF,UAAU,CAACV,WAAW,CAACc,MAAM,CAAC9B,YAAY,CAAC;UAAC5D,cAAA,GAAAC,CAAA;UAC5CqF,UAAU,CAACR,IAAI,IAAIhB,UAAU,CAACZ,IAAI;UAAClD,cAAA,GAAAC,CAAA;UACnCqF,UAAU,CAACP,SAAS,IAAIjB,UAAU,CAACZ,IAAI;UAAClD,cAAA,GAAAC,CAAA;UAGxC,IAAI,CAACgF,qBAAqB,CAACK,UAAU,CAAC;UAACtF,cAAA,GAAAC,CAAA;UAEvC0C,OAAO,CAACC,GAAG,CAAC,SAASkB,UAAU,CAACZ,IAAI,oBAAoBoC,UAAU,CAACvB,EAAE,EAAE,CAAC;QAE1E,CAAC,CAAC,OAAOlB,KAAK,EAAE;UAAA7C,cAAA,GAAAC,CAAA;UACd0C,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MAAA,SAhCK8C,UAAUA,CAAAC,GAAA;QAAA,OAAAP,WAAA,CAAAvC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAV4C,UAAU;IAAA;EAAA;IAAAzD,GAAA;IAAAC,KAAA;MAAA,IAAA0D,eAAA,GAAAxD,iBAAA,CAqChB,aAKG;QAAA,IALkByD,UAAmB,GAAA/C,SAAA,CAAAK,MAAA,QAAAL,SAAA,QAAAM,SAAA,GAAAN,SAAA,OAAA/C,cAAA,GAAAsD,CAAA,UAAG,KAAK;QAAAtD,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAM9C,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACF0C,OAAO,CAACC,GAAG,CAAC,6CAA6CkD,UAAU,GAAG,CAAC;UAEvE,IAAIC,WAAW,IAAA/F,cAAA,GAAAC,CAAA,QAAG,CAAC;UACnB,IAAI+F,cAAc,IAAAhG,cAAA,GAAAC,CAAA,QAAG,CAAC;UACtB,IAAIgG,aAAa,IAAAjG,cAAA,GAAAC,CAAA,QAAG,CAAC;UACrB,IAAIiG,eAAe,IAAAlG,cAAA,GAAAC,CAAA,QAAG,CAAC;UAACD,cAAA,GAAAC,CAAA;UAGxB,KAAK,IAAMsD,IAAI,IAAI,IAAI,CAACxD,WAAW,CAACwF,MAAM,CAAC,CAAC,EAAE;YAC5C,IAAMY,SAAS,IAAAnG,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACmG,kBAAkB,CAAC7C,IAAI,EAAEuC,UAAU,CAAC;YAAC9F,cAAA,GAAAC,CAAA;YAClE,IAAIkG,SAAS,GAAG,CAAC,EAAE;cAAAnG,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACjB8F,WAAW,IAAII,SAAS;cAACnG,cAAA,GAAAC,CAAA;cACzB+F,cAAc,EAAE;YAClB,CAAC;cAAAhG,cAAA,GAAAsD,CAAA;YAAA;UACH;UAGA,IAAM+C,aAAa,IAAArG,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACqG,kBAAkB,CAACR,UAAU,CAAC;UAAC9F,cAAA,GAAAC,CAAA;UAChEgG,aAAa,GAAGI,aAAa,CAACjD,MAAM;UAACpD,cAAA,GAAAC,CAAA;UAGrC,IAAI6F,UAAU,EAAE;YAAA9F,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YACdiG,eAAe,SAAS,IAAI,CAACK,yBAAyB,CAAC,CAAC;UAC1D,CAAC;YAAAvG,cAAA,GAAAsD,CAAA;UAAA;UAAAtD,cAAA,GAAAC,CAAA;UAGD,MAAM,IAAI,CAACuG,oBAAoB,CAAC,CAAC;UAACxG,cAAA,GAAAC,CAAA;UAElC0C,OAAO,CAACC,GAAG,CAAC,kCAAkCmD,WAAW,cAAc,CAAC;UAAC/F,cAAA,GAAAC,CAAA;UAEzE,OAAO;YAAE8F,WAAW,EAAXA,WAAW;YAAEC,cAAc,EAAdA,cAAc;YAAEC,aAAa,EAAbA,aAAa;YAAEC,eAAe,EAAfA;UAAgB,CAAC;QAExE,CAAC,CAAC,OAAOrD,KAAK,EAAE;UAAA7C,cAAA,GAAAC,CAAA;UACd0C,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAAC7C,cAAA,GAAAC,CAAA;UACnD,OAAO;YAAE8F,WAAW,EAAE,CAAC;YAAEC,cAAc,EAAE,CAAC;YAAEC,aAAa,EAAE,CAAC;YAAEC,eAAe,EAAE;UAAE,CAAC;QACpF;MACF,CAAC;MAAA,SA3CKO,cAAcA,CAAA;QAAA,OAAAZ,eAAA,CAAA/C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAd0D,cAAc;IAAA;EAAA;IAAAvE,GAAA;IAAAC,KAAA,EAgDpB,SAAAuE,gBAAgBA,CAAA,EAQd;MAAA1G,cAAA,GAAAc,CAAA;MACA,IAAI6F,cAAc,IAAA3G,cAAA,GAAAC,CAAA,QAAG,CAAC;MACtB,IAAI2G,cAAc,IAAA5G,cAAA,GAAAC,CAAA,QAAG,CAAC;MACtB,IAAM4G,eAAuC,IAAA7G,cAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAClD,IAAI6G,kBAAkB,IAAA9G,cAAA,GAAAC,CAAA,QAAG,CAAC;MAACD,cAAA,GAAAC,CAAA;MAE3B,SAAA8G,IAAA,IAA6B,IAAI,CAAChH,WAAW,CAACiH,OAAO,CAAC,CAAC,EAAE;QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAH,IAAA;QAAA,IAA7C/C,MAAM,GAAAiD,KAAA;QAAA,IAAE1D,IAAI,GAAA0D,KAAA;QAAAjH,cAAA,GAAAC,CAAA;QACtB0G,cAAc,IAAIpD,IAAI,CAACuB,IAAI;QAAC9E,cAAA,GAAAC,CAAA;QAC5B2G,cAAc,IAAIrD,IAAI,CAACwB,SAAS;QAAC/E,cAAA,GAAAC,CAAA;QACjC4G,eAAe,CAAC7C,MAAM,CAAC,GAAIT,IAAI,CAACuB,IAAI,GAAGvB,IAAI,CAACL,IAAI,GAAI,GAAG;QAAClD,cAAA,GAAAC,CAAA;QACxD6G,kBAAkB,IAAIvD,IAAI,CAAC4D,WAAW,CAACC,kBAAkB;MAC3D;MAEA,IAAMA,kBAAkB,IAAApH,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,WAAW,CAACmD,IAAI,GAAG,CAAC,IAAAlD,cAAA,GAAAsD,CAAA,WAChDwD,kBAAkB,GAAG,IAAI,CAAC/G,WAAW,CAACmD,IAAI,KAAAlD,cAAA,GAAAsD,CAAA,WAC1C,CAAC;MAACtD,cAAA,GAAAC,CAAA;MAEN,OAAO;QACL0G,cAAc,EAAdA,cAAc;QACdC,cAAc,EAAdA,cAAc;QACdC,eAAe,EAAfA,eAAe;QACfxF,cAAc,EAAE,IAAI,CAACA,cAAc;QACnCgG,SAAS,EAAE,IAAI,CAAClH,WAAW,CAAC+C,IAAI;QAChCoE,YAAY,EAAE,IAAI,CAACxF,WAAW,CAACyF,aAAa,CAAC,CAAC;QAC9CH,kBAAkB,EAAlBA;MACF,CAAC;IACH;EAAC;IAAAlF,GAAA;IAAAC,KAAA;MAAA,IAAAqF,kBAAA,GAAAnF,iBAAA,CAKD,aAAiD;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAC/C,aAAa,IAAI,CAAC2B,YAAY,CAAC6F,WAAW,CAAC,IAAI,CAACrH,iBAAiB,CAAC;MACpE,CAAC;MAAA,SAFKsH,iBAAiBA,CAAA;QAAA,OAAAF,kBAAA,CAAA1E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjB2E,iBAAiB;IAAA;EAAA;IAAAxF,GAAA;IAAAC,KAAA;MAAA,IAAAwF,yBAAA,GAAAtF,iBAAA,CAMvB,aAAwD;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QACtD,SAAA2H,KAAA,IAAiCtD,MAAM,CAAC0C,OAAO,CAAC,IAAI,CAAC3G,oBAAoB,CAAC,EAAE;UAAA,IAAAwH,KAAA,GAAAX,cAAA,CAAAU,KAAA;UAAA,IAAhE3E,QAAQ,GAAA4E,KAAA;UAAA,IAAEC,MAAM,GAAAD,KAAA;UAAA7H,cAAA,GAAAC,CAAA;UAC1B,MAAM,IAAI,CAAC8H,gBAAgB,CAAC9E,QAAQ,EAAE6E,MAAM,CAACvH,OAAO,EAAEuH,MAAM,CAACtH,IAAI,CAAC;QACpE;MACF,CAAC;MAAA,SAJa8B,wBAAwBA,CAAA;QAAA,OAAAqF,yBAAA,CAAA7E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAxBT,wBAAwB;IAAA;EAAA;IAAAJ,GAAA;IAAAC,KAAA;MAAA,IAAA6F,iBAAA,GAAA3F,iBAAA,CAMtC,WACE2B,MAAc,EACdzD,OAAe,EACfC,IAAwB,EACT;QAAAR,cAAA,GAAAc,CAAA;QACf,IAAMyC,IAAgB,IAAAvD,cAAA,GAAAC,CAAA,QAAG;UACvB8D,EAAE,EAAEC,MAAM;UACViE,IAAI,EAAE,GAAGzH,IAAI,CAAC0H,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAG3H,IAAI,CAAC4H,KAAK,CAAC,CAAC,CAAC,OAAO;UAC5D5H,IAAI,EAAJA,IAAI;UACJ0C,IAAI,EAAE3C,OAAO;UACbuE,IAAI,EAAE,CAAC;UACPC,SAAS,EAAExE,OAAO;UAClBqE,WAAW,EAAE,IAAI1E,GAAG,CAAC,CAAC;UACtBmI,aAAa,EAAE;YACb9H,OAAO,EAAPA,OAAO;YACP+H,cAAc,EAAE,UAAU;YAC1BC,cAAc,EAAE,KAAK;YACrBC,kBAAkB,EAAE,CAAAxI,cAAA,GAAAsD,CAAA,WAAA9C,IAAI,KAAK,OAAO,MAAAR,cAAA,GAAAsD,CAAA,WAAI9C,IAAI,KAAK,OAAO;UAC1D,CAAC;UACD2G,WAAW,EAAE;YACXsB,eAAe,EAAE,CAAC;YAClBrB,kBAAkB,EAAE,CAAC;YACrBsB,OAAO,EAAE,CAAC;YACVC,gBAAgB,EAAE;UACpB;QACF,CAAC;QAAC3I,cAAA,GAAAC,CAAA;QAEF,IAAI,CAACF,WAAW,CAAC8E,GAAG,CAACb,MAAM,EAAET,IAAI,CAAC;QAACvD,cAAA,GAAAC,CAAA;QACnC0C,OAAO,CAACC,GAAG,CAAC,wBAAwBoB,MAAM,KAAKzD,OAAO,SAAS,CAAC;MAClE,CAAC;MAAA,SA7BawH,gBAAgBA,CAAAa,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAd,iBAAA,CAAAlF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBgF,gBAAgB;IAAA;EAAA;IAAA7F,GAAA;IAAAC,KAAA,EA+B9B,SAAQuB,WAAWA,CAACH,IAAgB,EAAEL,IAAY,EAAW;MAAAlD,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAC,CAAA;MAC3D,OAAOsD,IAAI,CAACwB,SAAS,IAAI7B,IAAI;IAC/B;EAAC;IAAAhB,GAAA;IAAAC,KAAA;MAAA,IAAA4G,iBAAA,GAAA1G,iBAAA,CAED,WAA+BkB,IAAgB,EAAEyF,YAAoB,EAAmB;QAAAhJ,cAAA,GAAAc,CAAA;QACtF,IAAImI,SAAS,IAAAjJ,cAAA,GAAAC,CAAA,QAAG,CAAC;QACjB,IAAMiJ,iBAA2B,IAAAlJ,cAAA,GAAAC,CAAA,QAAG,EAAE;QAGtC,IAAMkJ,iBAAiB,IAAAnJ,cAAA,GAAAC,CAAA,QAAGmJ,KAAK,CAACC,IAAI,CAAC9F,IAAI,CAACqB,WAAW,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC+D,IAAI,CAAC,UAACC,CAAC,EAAEjG,CAAC,EAAK;UAAAtD,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAC,CAAA;UAC7E,QAAQsD,IAAI,CAAC8E,aAAa,CAACE,cAAc;YACvC,KAAK,KAAK;cAAAvI,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACR,OAAOsJ,CAAC,CAACnF,YAAY,GAAGd,CAAC,CAACc,YAAY;YACxC,KAAK,KAAK;cAAApE,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACR,OAAOsJ,CAAC,CAAClF,WAAW,GAAGf,CAAC,CAACe,WAAW;YACtC,KAAK,MAAM;cAAArE,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACT,OAAOsJ,CAAC,CAACtF,SAAS,GAAGX,CAAC,CAACW,SAAS;YAClC;cAAAjE,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACE,OAAOsJ,CAAC,CAACnF,YAAY,GAAGd,CAAC,CAACc,YAAY;UAC1C;QACF,CAAC,CAAC;QAACpE,cAAA,GAAAC,CAAA;QAGH,KAAK,IAAM6D,UAAU,IAAIqF,iBAAiB,EAAE;UAAAnJ,cAAA,GAAAC,CAAA;UAC1C,IAAI6D,UAAU,CAACX,QAAQ,CAACwB,MAAM,EAAE;YAAA3E,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAAA;UAAQ,CAAC;YAAAD,cAAA,GAAAsD,CAAA;UAAA;UAAAtD,cAAA,GAAAC,CAAA;UAEzCiJ,iBAAiB,CAAClE,IAAI,CAAClB,UAAU,CAACC,EAAE,CAAC;UAAC/D,cAAA,GAAAC,CAAA;UACtCgJ,SAAS,IAAInF,UAAU,CAACZ,IAAI;UAAClD,cAAA,GAAAC,CAAA;UAE7B,IAAIgJ,SAAS,IAAID,YAAY,EAAE;YAAAhJ,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAAA;UAAK,CAAC;YAAAD,cAAA,GAAAsD,CAAA;UAAA;QACvC;QAACtD,cAAA,GAAAC,CAAA;QAGD,KAAK,IAAM2D,YAAY,IAAIsF,iBAAiB,EAAE;UAAAlJ,cAAA,GAAAC,CAAA;UAC5C,MAAM,IAAI,CAAC0F,UAAU,CAAC/B,YAAY,CAAC;QACrC;QAAC5D,cAAA,GAAAC,CAAA;QAED,OAAOgJ,SAAS;MAClB,CAAC;MAAA,SAlCatF,gBAAgBA,CAAA6F,GAAA,EAAAC,GAAA;QAAA,OAAAV,iBAAA,CAAAjG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBY,gBAAgB;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA;MAAA,IAAAuH,mBAAA,GAAArH,iBAAA,CAoC9B,WAAiCkB,IAAgB,EAAEuC,UAAmB,EAAmB;QAAA9F,cAAA,GAAAc,CAAA;QACvF,IAAIiF,WAAW,IAAA/F,cAAA,GAAAC,CAAA,SAAG,CAAC;QAGnB,IAAMkE,GAAG,IAAAnE,cAAA,GAAAC,CAAA,SAAGiE,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,IAAMwF,MAAM,IAAA3J,cAAA,GAAAC,CAAA,SAAG6F,UAAU,IAAA9F,cAAA,GAAAsD,CAAA,WAAG,MAAM,KAAAtD,cAAA,GAAAsD,CAAA,WAAG,MAAM;QAE3C,IAAMsG,cAAc,IAAA5J,cAAA,GAAAC,CAAA,SAAGmJ,KAAK,CAACC,IAAI,CAAC9F,IAAI,CAACqB,WAAW,CAACW,MAAM,CAAC,CAAC,CAAC,CACzDsE,MAAM,CAAC,UAAAC,KAAK,EACX;UAAA9J,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAC,CAAA;UAAA,QAAAD,cAAA,GAAAsD,CAAA,YAACwG,KAAK,CAAC3G,QAAQ,CAACwB,MAAM,MAAA3E,cAAA,GAAAsD,CAAA,WACrBa,GAAG,GAAG2F,KAAK,CAAC1F,YAAY,GAAIuF,MAAM;QAAD,CACpC,CAAC;QAAC3J,cAAA,GAAAC,CAAA;QAEJ,KAAK,IAAM6D,UAAU,IAAI8F,cAAc,EAAE;UAAA5J,cAAA,GAAAC,CAAA;UACvC,MAAM,IAAI,CAAC0F,UAAU,CAAC7B,UAAU,CAACC,EAAE,CAAC;UAAC/D,cAAA,GAAAC,CAAA;UACrC8F,WAAW,IAAIjC,UAAU,CAACZ,IAAI;QAChC;QAAClD,cAAA,GAAAC,CAAA;QAGD,IAAIsD,IAAI,CAAC4D,WAAW,CAACC,kBAAkB,GAAG,GAAG,EAAE;UAAApH,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UAC7C,MAAM,IAAI,CAAC8J,cAAc,CAACxG,IAAI,CAAC;QACjC,CAAC;UAAAvD,cAAA,GAAAsD,CAAA;QAAA;QAAAtD,cAAA,GAAAC,CAAA;QAED,OAAO8F,WAAW;MACpB,CAAC;MAAA,SAxBaK,kBAAkBA,CAAA4D,GAAA,EAAAC,GAAA;QAAA,OAAAP,mBAAA,CAAA5G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBqD,kBAAkB;IAAA;EAAA;IAAAlE,GAAA;IAAAC,KAAA;MAAA,IAAA+H,eAAA,GAAA7H,iBAAA,CA0BhC,WAA6BkB,IAAgB,EAAiB;QAAAvD,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAE5D0C,OAAO,CAACC,GAAG,CAAC,uBAAuBW,IAAI,CAACQ,EAAE,EAAE,CAAC;QAAC/D,cAAA,GAAAC,CAAA;QAC9CsD,IAAI,CAAC4D,WAAW,CAACC,kBAAkB,IAAI,GAAG;MAC5C,CAAC;MAAA,SAJa2C,cAAcA,CAAAI,GAAA;QAAA,OAAAD,eAAA,CAAApH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdgH,cAAc;IAAA;EAAA;IAAA7H,GAAA;IAAAC,KAAA;MAAA,IAAAiI,mBAAA,GAAA/H,iBAAA,CAM5B,WAAiCyD,UAAmB,EAAyB;QAAA9F,cAAA,GAAAc,CAAA;QAC3E,IAAMuF,aAA2B,IAAArG,cAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAEvC,KAAK,IAAMoK,IAAI,IAAI,IAAI,CAAClK,WAAW,CAACoF,MAAM,CAAC,CAAC,EAAE;UAAAvF,cAAA,GAAAC,CAAA;UAC5C,IAAIoK,IAAI,CAACC,QAAQ,EAAE;YAAAtK,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAAA;UAAQ,CAAC;YAAAD,cAAA,GAAAsD,CAAA;UAAA;UAE5B,IAAMiH,aAAa,IAAAvK,cAAA,GAAAC,CAAA,SAAG,CAAAD,cAAA,GAAAsD,CAAA,WAAAwC,UAAU,MAAA9F,cAAA,GAAAsD,CAAA,WAAI+G,IAAI,CAACG,QAAQ,KAAK,UAAU,MAAAxK,cAAA,GAAAsD,CAAA,WAAI+G,IAAI,CAACG,QAAQ,KAAK,QAAQ;UAACxK,cAAA,GAAAC,CAAA;UAE/F,IAAIsK,aAAa,EAAE;YAAAvK,cAAA,GAAAsD,CAAA;YAEjB,IAAMgH,QAAQ,IAAAtK,cAAA,GAAAC,CAAA,eAAS,IAAI,CAACwK,WAAW,CAACJ,IAAI,CAAC;YAACrK,cAAA,GAAAC,CAAA;YAC9C,IAAIqK,QAAQ,EAAE;cAAAtK,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACZoK,IAAI,CAACC,QAAQ,GAAG,IAAI;cAACtK,cAAA,GAAAC,CAAA;cACrBoG,aAAa,CAACrB,IAAI,CAACqF,IAAI,CAAC;YAC1B,CAAC;cAAArK,cAAA,GAAAsD,CAAA;YAAA;UACH,CAAC;YAAAtD,cAAA,GAAAsD,CAAA;UAAA;QACH;QAACtD,cAAA,GAAAC,CAAA;QAED,OAAOoG,aAAa;MACtB,CAAC;MAAA,SAnBaC,kBAAkBA,CAAAoE,IAAA;QAAA,OAAAN,mBAAA,CAAAtH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBuD,kBAAkB;IAAA;EAAA;IAAApE,GAAA;IAAAC,KAAA;MAAA,IAAAwI,YAAA,GAAAtI,iBAAA,CAqBhC,WAA0BgI,IAAgB,EAAoB;QAAArK,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAC5D,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF0C,OAAO,CAACC,GAAG,CAAC,0BAA0ByH,IAAI,CAACtG,EAAE,KAAKsG,IAAI,CAACnH,IAAI,SAAS,CAAC;UAAClD,cAAA,GAAAC,CAAA;UAOtE,OAAO,IAAI;QACb,CAAC,CAAC,OAAO4C,KAAK,EAAE;UAAA7C,cAAA,GAAAC,CAAA;UACd0C,OAAO,CAACE,KAAK,CAAC,0BAA0BwH,IAAI,CAACtG,EAAE,GAAG,EAAElB,KAAK,CAAC;UAAC7C,cAAA,GAAAC,CAAA;UAC3D,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SAfawK,WAAWA,CAAAG,IAAA;QAAA,OAAAD,YAAA,CAAA7H,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAX0H,WAAW;IAAA;EAAA;IAAAvI,GAAA;IAAAC,KAAA;MAAA,IAAA0I,0BAAA,GAAAxI,iBAAA,CAiBzB,aAA2D;QAAArC,cAAA,GAAAc,CAAA;QAEzD,IAAIgK,aAAa,IAAA9K,cAAA,GAAAC,CAAA,SAAG,CAAC;QAACD,cAAA,GAAAC,CAAA;QAEtB,IAAI,CAAAD,cAAA,GAAAsD,CAAA,eAAI,CAACjC,cAAc,CAACC,KAAK,KAAK,UAAU,MAAAtB,cAAA,GAAAsD,CAAA,WAAI,IAAI,CAACjC,cAAc,CAACC,KAAK,KAAK,WAAW,GAAE;UAAAtB,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UAEzF,IAAI,CAACc,cAAc,CAACC,QAAQ,GAAG,YAAY;UAAChB,cAAA,GAAAC,CAAA;UAC5C,IAAI,CAACc,cAAc,CAACE,SAAS,GAAG,KAAK;UAACjB,cAAA,GAAAC,CAAA;UACtC6K,aAAa,EAAE;QACjB,CAAC,MAAM;UAAA9K,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UAAA,IAAI,IAAI,CAACoB,cAAc,CAACC,KAAK,KAAK,SAAS,EAAE;YAAAtB,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAElD,IAAI,CAACc,cAAc,CAACC,QAAQ,GAAG,UAAU;YAAChB,cAAA,GAAAC,CAAA;YAC1C,IAAI,CAACc,cAAc,CAACE,SAAS,GAAG,KAAK;YAACjB,cAAA,GAAAC,CAAA;YACtC6K,aAAa,EAAE;UACjB,CAAC;YAAA9K,cAAA,GAAAsD,CAAA;UAAA;QAAD;QAACtD,cAAA,GAAAC,CAAA;QAGD,MAAM,IAAI,CAAC6B,WAAW,CAACiJ,mBAAmB,CAAC,IAAI,CAAChK,cAAc,CAAC;QAACf,cAAA,GAAAC,CAAA;QAEhE,OAAO6K,aAAa;MACtB,CAAC;MAAA,SApBavE,yBAAyBA,CAAA;QAAA,OAAAsE,0BAAA,CAAA/H,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAzBwD,yBAAyB;IAAA;EAAA;IAAArE,GAAA;IAAAC,KAAA,EAsBvC,SAAQ0B,oBAAoBA,CAAA,EAAW;MAAA7D,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAC,CAAA;MACrC,OAAO,SAASiE,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI6G,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACzE;EAAC;IAAAjJ,GAAA;IAAAC,KAAA,EAED,SAAQ8C,qBAAqBA,CAAC1B,IAAgB,EAAQ;MAAAvD,cAAA,GAAAc,CAAA;MAEpD,IAAMsK,UAAU,IAAApL,cAAA,GAAAC,CAAA,SAAGsD,IAAI,CAACqB,WAAW,CAAC1B,IAAI;MACxC,IAAMmI,YAAY,IAAArL,cAAA,GAAAC,CAAA,SAAGmL,UAAU,GAAG,CAAC,IAAApL,cAAA,GAAAsD,CAAA,WAAGC,IAAI,CAACuB,IAAI,GAAGsG,UAAU,KAAApL,cAAA,GAAAsD,CAAA,WAAG,CAAC;MAChE,IAAMgI,eAAe,IAAAtL,cAAA,GAAAC,CAAA,SAAGoL,YAAY,GAAG,CAAC,IAAArL,cAAA,GAAAsD,CAAA,WAAGC,IAAI,CAACL,IAAI,GAAGmI,YAAY,KAAArL,cAAA,GAAAsD,CAAA,WAAG,CAAC;MAACtD,cAAA,GAAAC,CAAA;MACxEsD,IAAI,CAAC4D,WAAW,CAACC,kBAAkB,GAAGkE,eAAe,GAAG,CAAC,IAAAtL,cAAA,GAAAsD,CAAA,WAAG,CAAC,GAAI8H,UAAU,GAAGE,eAAgB,KAAAtL,cAAA,GAAAsD,CAAA,WAAG,CAAC;MAACtD,cAAA,GAAAC,CAAA;MAGnGsD,IAAI,CAAC4D,WAAW,CAACuB,OAAO,GAAGsC,IAAI,CAACO,GAAG,CAAChI,IAAI,CAACuB,IAAI,GAAGvB,IAAI,CAACL,IAAI,EAAE,CAAC,CAAC;IAC/D;EAAC;IAAAhB,GAAA;IAAAC,KAAA;MAAA,IAAAqJ,qBAAA,GAAAnJ,iBAAA,CAED,aAAoD;QAAArC,cAAA,GAAAc,CAAA;QAElD,IAAM2K,OAAO,IAAAzL,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACyG,gBAAgB,CAAC,CAAC;QACvC,IAAMjF,WAAW,IAAAzB,cAAA,GAAAC,CAAA,SAAGwL,OAAO,CAAC9E,cAAc,GAAG8E,OAAO,CAAC7E,cAAc;QACnE,IAAM8E,SAAS,IAAA1L,cAAA,GAAAC,CAAA,SAAGwB,WAAW,GAAG,CAAC,IAAAzB,cAAA,GAAAsD,CAAA,WAAGmI,OAAO,CAAC9E,cAAc,GAAGlF,WAAW,KAAAzB,cAAA,GAAAsD,CAAA,WAAG,CAAC;QAG5E,IAAIhC,KAAmC;QAACtB,cAAA,GAAAC,CAAA;QACxC,IAAIyL,SAAS,GAAG,GAAG,EAAE;UAAA1L,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UAAAqB,KAAK,GAAG,QAAQ;QAAA,CAAC,MACjC;UAAAtB,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UAAA,IAAIyL,SAAS,GAAG,GAAG,EAAE;YAAA1L,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAAAqB,KAAK,GAAG,SAAS;UAAA,CAAC,MACvC;YAAAtB,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAAA,IAAIyL,SAAS,GAAG,IAAI,EAAE;cAAA1L,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cAAAqB,KAAK,GAAG,UAAU;YAAA,CAAC,MACzC;cAAAtB,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cAAAqB,KAAK,GAAG,WAAW;YAAA;UAAA;QAAA;QAGxB,IAAMK,eAAyB,IAAA3B,cAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QACrC,IAAIqB,KAAK,KAAK,SAAS,EAAE;UAAAtB,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UACvB0B,eAAe,CAACqD,IAAI,CAAC,qCAAqC,CAAC;QAC7D,CAAC,MAAM;UAAAhF,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UAAA,IAAIqB,KAAK,KAAK,UAAU,EAAE;YAAAtB,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAC/B0B,eAAe,CAACqD,IAAI,CAAC,yBAAyB,CAAC;YAAChF,cAAA,GAAAC,CAAA;YAChD0B,eAAe,CAACqD,IAAI,CAAC,4BAA4B,CAAC;UACpD,CAAC,MAAM;YAAAhF,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAAA,IAAIqB,KAAK,KAAK,WAAW,EAAE;cAAAtB,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cAChC0B,eAAe,CAACqD,IAAI,CAAC,mCAAmC,CAAC;cAAChF,cAAA,GAAAC,CAAA;cAC1D0B,eAAe,CAACqD,IAAI,CAAC,8BAA8B,CAAC;YACtD,CAAC;cAAAhF,cAAA,GAAAsD,CAAA;YAAA;UAAD;QAAA;QAACtD,cAAA,GAAAC,CAAA;QAED,IAAI,CAACoB,cAAc,GAAG;UACpBC,KAAK,EAALA,KAAK;UACLC,eAAe,EAAEkK,OAAO,CAAC7E,cAAc;UACvCpF,UAAU,EAAEiK,OAAO,CAAC9E,cAAc;UAClClF,WAAW,EAAXA,WAAW;UACXC,aAAa,EAAEgK,SAAS;UACxB/J,eAAe,EAAfA;QACF,CAAC;MACH,CAAC;MAAA,SAjCa6E,oBAAoBA,CAAA;QAAA,OAAAgF,qBAAA,CAAA1I,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApByD,oBAAoB;IAAA;EAAA;IAAAtE,GAAA;IAAAC,KAAA,EAmClC,SAAQI,qBAAqBA,CAAA,EAAS;MAAA,IAAAoJ,KAAA;MAAA3L,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAC,CAAA;MAEpC2L,WAAW,CAAC,YAAM;QAAA5L,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAChB0L,KAAI,CAACnF,oBAAoB,CAAC,CAAC;MAC7B,CAAC,EAAE,KAAK,CAAC;IACX;EAAC;IAAAtE,GAAA;IAAAC,KAAA,EAED,SAAQO,uBAAuBA,CAAA,EAAS;MAAA,IAAAmJ,MAAA;MAAA7L,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAC,CAAA;MAEtC2L,WAAW,CAAAvJ,iBAAA,CAAC,aAAY;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QACtB,IAAI,CAAAD,cAAA,GAAAsD,CAAA,WAAAuI,MAAI,CAACxK,cAAc,CAACC,KAAK,KAAK,UAAU,MAAAtB,cAAA,GAAAsD,CAAA,WAAIuI,MAAI,CAACxK,cAAc,CAACC,KAAK,KAAK,WAAW,GAAE;UAAAtB,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UACzF,MAAM4L,MAAI,CAACpF,cAAc,CAAC,IAAI,CAAC;QACjC,CAAC;UAAAzG,cAAA,GAAAsD,CAAA;QAAA;MACH,CAAC,GAAE,IAAI,CAAC;IACV;EAAC;AAAA;AAAA,IAMGzB,kBAAkB;EAAA,SAAAA,mBAAA;IAAA/B,eAAA,OAAA+B,kBAAA;IAAA,KACdiK,qBAAqB,IAAA9L,cAAA,GAAAC,CAAA,SAAwB,IAAIC,GAAG,CAAC,CAAC;EAAA;EAAA,OAAA+B,YAAA,CAAAJ,kBAAA;IAAAK,GAAA;IAAAC,KAAA;MAAA,IAAA4J,WAAA,GAAA1J,iBAAA,CAE9D,aAAkC;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAChC0C,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD,CAAC;MAAA,SAFKJ,UAAUA,CAAA;QAAA,OAAAuJ,WAAA,CAAAjJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVP,UAAU;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA;MAAA,IAAA6J,YAAA,GAAA3J,iBAAA,CAIhB,WACEjC,iBAA6E,EACtD;QAAAJ,cAAA,GAAAc,CAAA;QACvB,IAAMmL,KAAmB,IAAAjM,cAAA,GAAAC,CAAA,SAAG,EAAE;QAC9B,IAAMkE,GAAG,IAAAnE,cAAA,GAAAC,CAAA,SAAGiE,IAAI,CAACC,GAAG,CAAC,CAAC;QAGtB,IAAM+H,YAAY,IAAAlM,cAAA,GAAAC,CAAA,SAAG,IAAIC,GAAG,CAA6B,CAAC;QAACF,cAAA,GAAAC,CAAA;QAE3DG,iBAAiB,CAAC+L,OAAO,CAAC,UAAAC,KAAK,EAAI;UAAApM,cAAA,GAAAc,CAAA;UACjC,IAAM0D,MAAM,IAAAxE,cAAA,GAAAC,CAAA,SAAGmM,KAAK,CAACtI,UAAU,CAACX,QAAQ,CAACqB,MAAM;UAACxE,cAAA,GAAAC,CAAA;UAChD,IAAI,CAACiM,YAAY,CAAC1G,GAAG,CAAChB,MAAM,CAAC,EAAE;YAAAxE,cAAA,GAAAsD,CAAA;YAAAtD,cAAA,GAAAC,CAAA;YAC7BiM,YAAY,CAACrH,GAAG,CAACL,MAAM,EAAE,EAAE,CAAC;UAC9B,CAAC;YAAAxE,cAAA,GAAAsD,CAAA;UAAA;UAAAtD,cAAA,GAAAC,CAAA;UACDiM,YAAY,CAAC1I,GAAG,CAACgB,MAAM,CAAC,CAAEQ,IAAI,CAACoH,KAAK,CAACtI,UAAU,CAAC;QAClD,CAAC,CAAC;QAAC9D,cAAA,GAAAC,CAAA;QAGH,SAAAoM,KAAA,IAAoCH,YAAY,CAAClF,OAAO,CAAC,CAAC,EAAE;UAAA,IAAAsF,KAAA,GAAApF,cAAA,CAAAmF,KAAA;UAAA,IAAhD7H,MAAM,GAAA8H,KAAA;UAAA,IAAE1H,WAAW,GAAA0H,KAAA;UAC7B,IAAMC,SAAS,IAAAvM,cAAA,GAAAC,CAAA,SAAG2E,WAAW,CAAC4H,MAAM,CAAC,UAACC,GAAG,EAAE3C,KAAK,EAAK;YAAA9J,cAAA,GAAAc,CAAA;YAAAd,cAAA,GAAAC,CAAA;YAAA,OAAAwM,GAAG,GAAG3C,KAAK,CAAC5G,IAAI;UAAD,CAAC,EAAE,CAAC,CAAC;UACzE,IAAMwJ,MAAM,IAAA1M,cAAA,GAAAC,CAAA,SAAG2E,WAAW,CAAC4H,MAAM,CAAC,UAACC,GAAG,EAAE3C,KAAK,EAAK;YAAA9J,cAAA,GAAAc,CAAA;YAAAd,cAAA,GAAAC,CAAA;YAAA,OAAAwM,GAAG,IAAItI,GAAG,GAAG2F,KAAK,CAAC7F,SAAS,CAAC;UAAD,CAAC,EAAE,CAAC,CAAC,GAAGW,WAAW,CAACxB,MAAM;UAACpD,cAAA,GAAAC,CAAA;UAGzG,IAAI,CAAAD,cAAA,GAAAsD,CAAA,WAAAsB,WAAW,CAACxB,MAAM,GAAG,GAAG,MAAApD,cAAA,GAAAsD,CAAA,WAAIoJ,MAAM,GAAG,MAAM,GAAE;YAAA1M,cAAA,GAAAsD,CAAA;YAC/C,IAAMqJ,UAAU,IAAA3M,cAAA,GAAAC,CAAA,SAAGsM,SAAS,IAAIG,MAAM,GAAG,IAAI,CAAC;YAE9C,IAAIlC,QAAgC;YAACxK,cAAA,GAAAC,CAAA;YACrC,IAAI0M,UAAU,GAAG,IAAI,GAAG,IAAI,EAAE;cAAA3M,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cAAAuK,QAAQ,GAAG,UAAU;YAAA,CAAC,MAC/C;cAAAxK,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cAAA,IAAI0M,UAAU,GAAG,GAAG,GAAG,IAAI,EAAE;gBAAA3M,cAAA,GAAAsD,CAAA;gBAAAtD,cAAA,GAAAC,CAAA;gBAAAuK,QAAQ,GAAG,QAAQ;cAAA,CAAC,MACjD;gBAAAxK,cAAA,GAAAsD,CAAA;gBAAAtD,cAAA,GAAAC,CAAA;gBAAA,IAAI0M,UAAU,GAAG,GAAG,GAAG,IAAI,EAAE;kBAAA3M,cAAA,GAAAsD,CAAA;kBAAAtD,cAAA,GAAAC,CAAA;kBAAAuK,QAAQ,GAAG,UAAU;gBAAA,CAAC,MACnD;kBAAAxK,cAAA,GAAAsD,CAAA;kBAAAtD,cAAA,GAAAC,CAAA;kBAAAuK,QAAQ,GAAG,OAAO;gBAAA;cAAA;YAAA;YAACxK,cAAA,GAAAC,CAAA;YAExBgM,KAAK,CAACjH,IAAI,CAAC;cACTjB,EAAE,EAAE,QAAQS,MAAM,IAAIN,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;cAClCK,MAAM,EAANA,MAAM;cACNtB,IAAI,EAAEqJ,SAAS;cACfK,QAAQ,EAAEF,MAAM;cAChBC,UAAU,EAAVA,UAAU;cACVnC,QAAQ,EAARA,QAAQ;cACRqC,aAAa,EAAE1I,GAAG;cAClBmG,QAAQ,EAAE;YACZ,CAAC,CAAC;UACJ,CAAC;YAAAtK,cAAA,GAAAsD,CAAA;UAAA;QACH;QAACtD,cAAA,GAAAC,CAAA;QAED,OAAOgM,KAAK;MACd,CAAC;MAAA,SA9CKxE,WAAWA,CAAAqF,IAAA;QAAA,OAAAd,YAAA,CAAAlJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAX0E,WAAW;IAAA;EAAA;AAAA;AAAA,IAoDb1F,WAAW;EAKf,SAAAA,YAAY+F,MAAsB,EAAE;IAAAhI,eAAA,OAAAiC,WAAA;IAAA,KAH5BgL,UAAU,IAAA/M,cAAA,GAAAC,CAAA,SAAG,GAAG;IAAA,KAChB+M,SAAS,IAAAhN,cAAA,GAAAC,CAAA,SAAG,KAAK;IAAAD,cAAA,GAAAc,CAAA;IAAAd,cAAA,GAAAC,CAAA;IAGvB,IAAI,CAACoI,aAAa,GAAGP,MAAM;EAC7B;EAAC,OAAA7F,YAAA,CAAAF,WAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAA8K,MAAA,GAAA5K,iBAAA,CAED,aAA6B;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAC3B,IAAI,IAAI,CAAC+M,SAAS,EAAE;UAAAhN,cAAA,GAAAsD,CAAA;UAAAtD,cAAA,GAAAC,CAAA;UAAA;QAAM,CAAC;UAAAD,cAAA,GAAAsD,CAAA;QAAA;QAAAtD,cAAA,GAAAC,CAAA;QAE3B,IAAI,CAAC+M,SAAS,GAAG,IAAI;QAAChN,cAAA,GAAAC,CAAA;QACtB,IAAI,CAACiN,UAAU,CAAC,CAAC;QAAClN,cAAA,GAAAC,CAAA;QAClB0C,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MACrC,CAAC;MAAA,SANKH,KAAKA,CAAA;QAAA,OAAAwK,MAAA,CAAAnK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAALN,KAAK;IAAA;EAAA;IAAAP,GAAA;IAAAC,KAAA;MAAA,IAAAgL,oBAAA,GAAA9K,iBAAA,CAQX,WAA0ByF,MAAsB,EAAiB;QAAA9H,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAC/D,IAAI,CAACoI,aAAa,GAAGP,MAAM;QAAC9H,cAAA,GAAAC,CAAA;QAC5B0C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;MACzC,CAAC;MAAA,SAHKmI,mBAAmBA,CAAAqC,IAAA;QAAA,OAAAD,oBAAA,CAAArK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBgI,mBAAmB;IAAA;EAAA;IAAA7I,GAAA;IAAAC,KAAA,EAKzB,SAAAoF,aAAaA,CAAA,EAAW;MAAAvH,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAC,CAAA;MACtB,OAAO,IAAI,CAAC8M,UAAU;IACxB;EAAC;IAAA7K,GAAA;IAAAC,KAAA,EAED,SAAQ+K,UAAUA,CAAA,EAAS;MAAA,IAAAG,MAAA;MAAArN,cAAA,GAAAc,CAAA;MAAAd,cAAA,GAAAC,CAAA;MACzB,IAAI,CAAC,IAAI,CAAC+M,SAAS,EAAE;QAAAhN,cAAA,GAAAsD,CAAA;QAAAtD,cAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,cAAA,GAAAsD,CAAA;MAAA;MAAAtD,cAAA,GAAAC,CAAA;MAE5BqN,UAAU,CAAC,YAAM;QAAAtN,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QACfoN,MAAI,CAACE,SAAS,CAAC,CAAC;QAACvN,cAAA,GAAAC,CAAA;QACjBoN,MAAI,CAACH,UAAU,CAAC,CAAC;MACnB,CAAC,EAAE,IAAI,CAAC7E,aAAa,CAACpH,SAAS,CAAC;IAClC;EAAC;IAAAiB,GAAA;IAAAC,KAAA;MAAA,IAAAqL,UAAA,GAAAnL,iBAAA,CAED,aAAyC;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QACvC,IAAI;UAEF,IAAMwN,SAAS,IAAAzN,cAAA,GAAAC,CAAA,SAAGiE,IAAI,CAACC,GAAG,CAAC,CAAC;UAACnE,cAAA,GAAAC,CAAA;UAG7B,QAAQ,IAAI,CAACoI,aAAa,CAACrH,QAAQ;YACjC,KAAK,YAAY;cAAAhB,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACf,MAAM,IAAI,CAACyN,YAAY,CAAC,CAAC;cAAC1N,cAAA,GAAAC,CAAA;cAC1B;YACF,KAAK,UAAU;cAAAD,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACb,MAAM,IAAI,CAAC0N,UAAU,CAAC,CAAC;cAAC3N,cAAA,GAAAC,CAAA;cACxB;YACF,KAAK,cAAc;cAAAD,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACjB,MAAM,IAAI,CAAC2N,cAAc,CAAC,CAAC;cAAC5N,cAAA,GAAAC,CAAA;cAC5B;YACF,KAAK,UAAU;cAAAD,cAAA,GAAAsD,CAAA;cAAAtD,cAAA,GAAAC,CAAA;cACb,MAAM,IAAI,CAAC4N,UAAU,CAAC,CAAC;cAAC7N,cAAA,GAAAC,CAAA;cACxB;UACJ;UAEA,IAAM6N,MAAM,IAAA9N,cAAA,GAAAC,CAAA,SAAGiE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGsJ,SAAS;UAACzN,cAAA,GAAAC,CAAA;UACtC,IAAI,CAAC8M,UAAU,GAAG/B,IAAI,CAAC+C,GAAG,CAAC,CAAC,EAAE,CAAC,GAAID,MAAM,GAAG,IAAI,CAACzF,aAAa,CAACjH,SAAU,CAAC;QAE5E,CAAC,CAAC,OAAOyB,KAAK,EAAE;UAAA7C,cAAA,GAAAC,CAAA;UACd0C,OAAO,CAACE,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC9C;MACF,CAAC;MAAA,SA3Ba0K,SAASA,CAAA;QAAA,OAAAC,UAAA,CAAA1K,KAAA,OAAAC,SAAA;MAAA;MAAA,OAATwK,SAAS;IAAA;EAAA;IAAArL,GAAA;IAAAC,KAAA;MAAA,IAAA6L,aAAA,GAAA3L,iBAAA,CA6BvB,aAA4C;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAE1C,MAAM,IAAIgO,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAlO,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAC,CAAA;UAAA,OAAAqN,UAAU,CAACY,OAAO,EAAE,CAAC,CAAC;QAAD,CAAC,CAAC;MACtD,CAAC;MAAA,SAHaR,YAAYA,CAAA;QAAA,OAAAM,aAAA,CAAAlL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZ2K,YAAY;IAAA;EAAA;IAAAxL,GAAA;IAAAC,KAAA;MAAA,IAAAgM,WAAA,GAAA9L,iBAAA,CAK1B,aAA0C;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAExC,MAAM,IAAIgO,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAlO,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAC,CAAA;UAAA,OAAAqN,UAAU,CAACY,OAAO,EAAE,EAAE,CAAC;QAAD,CAAC,CAAC;MACvD,CAAC;MAAA,SAHaP,UAAUA,CAAA;QAAA,OAAAQ,WAAA,CAAArL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAV4K,UAAU;IAAA;EAAA;IAAAzL,GAAA;IAAAC,KAAA;MAAA,IAAAiM,eAAA,GAAA/L,iBAAA,CAKxB,aAA8C;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAE5C,MAAM,IAAIgO,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAlO,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAC,CAAA;UAAA,OAAAqN,UAAU,CAACY,OAAO,EAAE,EAAE,CAAC;QAAD,CAAC,CAAC;MACvD,CAAC;MAAA,SAHaN,cAAcA,CAAA;QAAA,OAAAQ,eAAA,CAAAtL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAd6K,cAAc;IAAA;EAAA;IAAA1L,GAAA;IAAAC,KAAA;MAAA,IAAAkM,WAAA,GAAAhM,iBAAA,CAK5B,aAA0C;QAAArC,cAAA,GAAAc,CAAA;QAAAd,cAAA,GAAAC,CAAA;QAExC,MAAM,IAAIgO,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAlO,cAAA,GAAAc,CAAA;UAAAd,cAAA,GAAAC,CAAA;UAAA,OAAAqN,UAAU,CAACY,OAAO,EAAE,EAAE,CAAC;QAAD,CAAC,CAAC;MACvD,CAAC;MAAA,SAHaL,UAAUA,CAAA;QAAA,OAAAQ,WAAA,CAAAvL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAV8K,UAAU;IAAA;EAAA;AAAA;AAO1B,OAAO,IAAMS,qBAAqB,IAAAtO,cAAA,GAAAC,CAAA,SAAG,IAAIJ,qBAAqB,CAAC,CAAC;AAChE,eAAeyO,qBAAqB", "ignoreList": []}