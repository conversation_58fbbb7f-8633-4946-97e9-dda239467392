f6a0208aed0b68412413a71cd5ab7ceb
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_29jqgxid58() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useExport.ts";
  var hash = "05b2f31a061dbf14ee80ac01bcdd94d08088ed0f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useExport.ts",
    statementMap: {
      "0": {
        start: {
          line: 27,
          column: 42
        },
        end: {
          line: 27,
          column: 57
        }
      },
      "1": {
        start: {
          line: 28,
          column: 40
        },
        end: {
          line: 28,
          column: 55
        }
      },
      "2": {
        start: {
          line: 29,
          column: 34
        },
        end: {
          line: 29,
          column: 45
        }
      },
      "3": {
        start: {
          line: 30,
          column: 28
        },
        end: {
          line: 30,
          column: 57
        }
      },
      "4": {
        start: {
          line: 35,
          column: 33
        },
        end: {
          line: 63,
          column: 8
        }
      },
      "5": {
        start: {
          line: 39,
          column: 4
        },
        end: {
          line: 62,
          column: 5
        }
      },
      "6": {
        start: {
          line: 40,
          column: 6
        },
        end: {
          line: 40,
          column: 21
        }
      },
      "7": {
        start: {
          line: 41,
          column: 6
        },
        end: {
          line: 41,
          column: 28
        }
      },
      "8": {
        start: {
          line: 42,
          column: 6
        },
        end: {
          line: 42,
          column: 21
        }
      },
      "9": {
        start: {
          line: 45,
          column: 31
        },
        end: {
          line: 47,
          column: 13
        }
      },
      "10": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 53
        }
      },
      "11": {
        start: {
          line: 46,
          column: 28
        },
        end: {
          line: 46,
          column: 51
        }
      },
      "12": {
        start: {
          line: 49,
          column: 21
        },
        end: {
          line: 49,
          column: 80
        }
      },
      "13": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 51,
          column: 38
        }
      },
      "14": {
        start: {
          line: 52,
          column: 6
        },
        end: {
          line: 52,
          column: 23
        }
      },
      "15": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 29
        }
      },
      "16": {
        start: {
          line: 55,
          column: 6
        },
        end: {
          line: 55,
          column: 20
        }
      },
      "17": {
        start: {
          line: 57,
          column: 27
        },
        end: {
          line: 57,
          column: 100
        }
      },
      "18": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 29
        }
      },
      "19": {
        start: {
          line: 59,
          column: 6
        },
        end: {
          line: 59,
          column: 29
        }
      },
      "20": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 21
        }
      },
      "21": {
        start: {
          line: 61,
          column: 6
        },
        end: {
          line: 61,
          column: 16
        }
      },
      "22": {
        start: {
          line: 68,
          column: 31
        },
        end: {
          line: 96,
          column: 8
        }
      },
      "23": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 95,
          column: 5
        }
      },
      "24": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 21
        }
      },
      "25": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 74,
          column: 27
        }
      },
      "26": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 75,
          column: 21
        }
      },
      "27": {
        start: {
          line: 78,
          column: 31
        },
        end: {
          line: 80,
          column: 13
        }
      },
      "28": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 79,
          column: 53
        }
      },
      "29": {
        start: {
          line: 79,
          column: 28
        },
        end: {
          line: 79,
          column: 51
        }
      },
      "30": {
        start: {
          line: 82,
          column: 22
        },
        end: {
          line: 82,
          column: 79
        }
      },
      "31": {
        start: {
          line: 84,
          column: 6
        },
        end: {
          line: 84,
          column: 38
        }
      },
      "32": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 85,
          column: 23
        }
      },
      "33": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 86,
          column: 28
        }
      },
      "34": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 21
        }
      },
      "35": {
        start: {
          line: 90,
          column: 27
        },
        end: {
          line: 90,
          column: 98
        }
      },
      "36": {
        start: {
          line: 91,
          column: 6
        },
        end: {
          line: 91,
          column: 29
        }
      },
      "37": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 28
        }
      },
      "38": {
        start: {
          line: 93,
          column: 6
        },
        end: {
          line: 93,
          column: 21
        }
      },
      "39": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 94,
          column: 16
        }
      },
      "40": {
        start: {
          line: 101,
          column: 28
        },
        end: {
          line: 110,
          column: 8
        }
      },
      "41": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "42": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 21
        }
      },
      "43": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 53
        }
      },
      "44": {
        start: {
          line: 106,
          column: 27
        },
        end: {
          line: 106,
          column: 86
        }
      },
      "45": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 29
        }
      },
      "46": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 16
        }
      },
      "47": {
        start: {
          line: 115,
          column: 29
        },
        end: {
          line: 144,
          column: 8
        }
      },
      "48": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 143,
          column: 5
        }
      },
      "49": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 21
        }
      },
      "50": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 122,
          column: 27
        }
      },
      "51": {
        start: {
          line: 123,
          column: 6
        },
        end: {
          line: 123,
          column: 21
        }
      },
      "52": {
        start: {
          line: 126,
          column: 31
        },
        end: {
          line: 128,
          column: 13
        }
      },
      "53": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 127,
          column: 53
        }
      },
      "54": {
        start: {
          line: 127,
          column: 28
        },
        end: {
          line: 127,
          column: 51
        }
      },
      "55": {
        start: {
          line: 130,
          column: 22
        },
        end: {
          line: 130,
          column: 88
        }
      },
      "56": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 132,
          column: 38
        }
      },
      "57": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 133,
          column: 23
        }
      },
      "58": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 28
        }
      },
      "59": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 21
        }
      },
      "60": {
        start: {
          line: 138,
          column: 27
        },
        end: {
          line: 138,
          column: 96
        }
      },
      "61": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 139,
          column: 29
        }
      },
      "62": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 28
        }
      },
      "63": {
        start: {
          line: 141,
          column: 6
        },
        end: {
          line: 141,
          column: 21
        }
      },
      "64": {
        start: {
          line: 142,
          column: 6
        },
        end: {
          line: 142,
          column: 16
        }
      },
      "65": {
        start: {
          line: 149,
          column: 26
        },
        end: {
          line: 178,
          column: 8
        }
      },
      "66": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 177,
          column: 5
        }
      },
      "67": {
        start: {
          line: 155,
          column: 6
        },
        end: {
          line: 155,
          column: 21
        }
      },
      "68": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 27
        }
      },
      "69": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 21
        }
      },
      "70": {
        start: {
          line: 160,
          column: 31
        },
        end: {
          line: 162,
          column: 13
        }
      },
      "71": {
        start: {
          line: 161,
          column: 8
        },
        end: {
          line: 161,
          column: 53
        }
      },
      "72": {
        start: {
          line: 161,
          column: 28
        },
        end: {
          line: 161,
          column: 51
        }
      },
      "73": {
        start: {
          line: 164,
          column: 22
        },
        end: {
          line: 164,
          column: 83
        }
      },
      "74": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 38
        }
      },
      "75": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 167,
          column: 23
        }
      },
      "76": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 28
        }
      },
      "77": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 21
        }
      },
      "78": {
        start: {
          line: 172,
          column: 27
        },
        end: {
          line: 172,
          column: 93
        }
      },
      "79": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 173,
          column: 29
        }
      },
      "80": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 28
        }
      },
      "81": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 21
        }
      },
      "82": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 176,
          column: 16
        }
      },
      "83": {
        start: {
          line: 183,
          column: 21
        },
        end: {
          line: 192,
          column: 8
        }
      },
      "84": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 191,
          column: 5
        }
      },
      "85": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 185,
          column: 21
        }
      },
      "86": {
        start: {
          line: 186,
          column: 6
        },
        end: {
          line: 186,
          column: 46
        }
      },
      "87": {
        start: {
          line: 188,
          column: 27
        },
        end: {
          line: 188,
          column: 87
        }
      },
      "88": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 29
        }
      },
      "89": {
        start: {
          line: 190,
          column: 6
        },
        end: {
          line: 190,
          column: 16
        }
      },
      "90": {
        start: {
          line: 194,
          column: 2
        },
        end: {
          line: 214,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useExport",
        decl: {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 25
          }
        },
        loc: {
          start: {
            line: 26,
            column: 45
          },
          end: {
            line: 215,
            column: 1
          }
        },
        line: 26
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 35,
            column: 45
          },
          end: {
            line: 35,
            column: 46
          }
        },
        loc: {
          start: {
            line: 38,
            column: 32
          },
          end: {
            line: 63,
            column: 3
          }
        },
        line: 38
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 45,
            column: 43
          },
          end: {
            line: 45,
            column: 44
          }
        },
        loc: {
          start: {
            line: 45,
            column: 49
          },
          end: {
            line: 47,
            column: 7
          }
        },
        line: 45
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 21
          }
        },
        loc: {
          start: {
            line: 46,
            column: 28
          },
          end: {
            line: 46,
            column: 51
          }
        },
        line: 46
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 68,
            column: 43
          },
          end: {
            line: 68,
            column: 44
          }
        },
        loc: {
          start: {
            line: 71,
            column: 24
          },
          end: {
            line: 96,
            column: 3
          }
        },
        line: 71
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 78,
            column: 43
          },
          end: {
            line: 78,
            column: 44
          }
        },
        loc: {
          start: {
            line: 78,
            column: 49
          },
          end: {
            line: 80,
            column: 7
          }
        },
        line: 78
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 79,
            column: 20
          },
          end: {
            line: 79,
            column: 21
          }
        },
        loc: {
          start: {
            line: 79,
            column: 28
          },
          end: {
            line: 79,
            column: 51
          }
        },
        line: 79
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 101,
            column: 40
          },
          end: {
            line: 101,
            column: 41
          }
        },
        loc: {
          start: {
            line: 101,
            column: 82
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 101
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 115,
            column: 41
          },
          end: {
            line: 115,
            column: 42
          }
        },
        loc: {
          start: {
            line: 119,
            column: 24
          },
          end: {
            line: 144,
            column: 3
          }
        },
        line: 119
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 126,
            column: 43
          },
          end: {
            line: 126,
            column: 44
          }
        },
        loc: {
          start: {
            line: 126,
            column: 49
          },
          end: {
            line: 128,
            column: 7
          }
        },
        line: 126
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 127,
            column: 20
          },
          end: {
            line: 127,
            column: 21
          }
        },
        loc: {
          start: {
            line: 127,
            column: 28
          },
          end: {
            line: 127,
            column: 51
          }
        },
        line: 127
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 149,
            column: 38
          },
          end: {
            line: 149,
            column: 39
          }
        },
        loc: {
          start: {
            line: 153,
            column: 24
          },
          end: {
            line: 178,
            column: 3
          }
        },
        line: 153
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 160,
            column: 43
          },
          end: {
            line: 160,
            column: 44
          }
        },
        loc: {
          start: {
            line: 160,
            column: 49
          },
          end: {
            line: 162,
            column: 7
          }
        },
        line: 160
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 161,
            column: 20
          },
          end: {
            line: 161,
            column: 21
          }
        },
        loc: {
          start: {
            line: 161,
            column: 28
          },
          end: {
            line: 161,
            column: 51
          }
        },
        line: 161
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 183,
            column: 33
          },
          end: {
            line: 183,
            column: 34
          }
        },
        loc: {
          start: {
            line: 183,
            column: 59
          },
          end: {
            line: 192,
            column: 3
          }
        },
        line: 183
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 37,
            column: 4
          },
          end: {
            line: 37,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 37,
            column: 29
          },
          end: {
            line: 37,
            column: 46
          }
        }],
        line: 37
      },
      "1": {
        loc: {
          start: {
            line: 57,
            column: 27
          },
          end: {
            line: 57,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 57,
            column: 50
          },
          end: {
            line: 57,
            column: 61
          }
        }, {
          start: {
            line: 57,
            column: 64
          },
          end: {
            line: 57,
            column: 100
          }
        }],
        line: 57
      },
      "2": {
        loc: {
          start: {
            line: 90,
            column: 27
          },
          end: {
            line: 90,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 90,
            column: 50
          },
          end: {
            line: 90,
            column: 61
          }
        }, {
          start: {
            line: 90,
            column: 64
          },
          end: {
            line: 90,
            column: 98
          }
        }],
        line: 90
      },
      "3": {
        loc: {
          start: {
            line: 106,
            column: 27
          },
          end: {
            line: 106,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 106,
            column: 50
          },
          end: {
            line: 106,
            column: 61
          }
        }, {
          start: {
            line: 106,
            column: 64
          },
          end: {
            line: 106,
            column: 86
          }
        }],
        line: 106
      },
      "4": {
        loc: {
          start: {
            line: 138,
            column: 27
          },
          end: {
            line: 138,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 138,
            column: 50
          },
          end: {
            line: 138,
            column: 61
          }
        }, {
          start: {
            line: 138,
            column: 64
          },
          end: {
            line: 138,
            column: 96
          }
        }],
        line: 138
      },
      "5": {
        loc: {
          start: {
            line: 172,
            column: 27
          },
          end: {
            line: 172,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 172,
            column: 50
          },
          end: {
            line: 172,
            column: 61
          }
        }, {
          start: {
            line: 172,
            column: 64
          },
          end: {
            line: 172,
            column: 93
          }
        }],
        line: 172
      },
      "6": {
        loc: {
          start: {
            line: 188,
            column: 27
          },
          end: {
            line: 188,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 188,
            column: 50
          },
          end: {
            line: 188,
            column: 61
          }
        }, {
          start: {
            line: 188,
            column: 64
          },
          end: {
            line: 188,
            column: 87
          }
        }],
        line: 188
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "05b2f31a061dbf14ee80ac01bcdd94d08088ed0f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_29jqgxid58 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_29jqgxid58();
import { useState, useCallback } from 'react';
import { exportService } from "../services/exportService";
export function useExport() {
  cov_29jqgxid58().f[0]++;
  var _ref = (cov_29jqgxid58().s[0]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    isGenerating = _ref2[0],
    setIsGenerating = _ref2[1];
  var _ref3 = (cov_29jqgxid58().s[1]++, useState(false)),
    _ref4 = _slicedToArray(_ref3, 2),
    isExporting = _ref4[0],
    setIsExporting = _ref4[1];
  var _ref5 = (cov_29jqgxid58().s[2]++, useState(0)),
    _ref6 = _slicedToArray(_ref5, 2),
    progress = _ref6[0],
    setProgress = _ref6[1];
  var _ref7 = (cov_29jqgxid58().s[3]++, useState(null)),
    _ref8 = _slicedToArray(_ref7, 2),
    error = _ref8[0],
    setError = _ref8[1];
  var generateProgressReport = (cov_29jqgxid58().s[4]++, useCallback(function () {
    var _ref9 = _asyncToGenerator(function* (userId) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_29jqgxid58().b[0][0]++, {
        format: 'pdf'
      });
      cov_29jqgxid58().f[1]++;
      cov_29jqgxid58().s[5]++;
      try {
        cov_29jqgxid58().s[6]++;
        setError(null);
        cov_29jqgxid58().s[7]++;
        setIsGenerating(true);
        cov_29jqgxid58().s[8]++;
        setProgress(0);
        var progressInterval = (cov_29jqgxid58().s[9]++, setInterval(function () {
          cov_29jqgxid58().f[2]++;
          cov_29jqgxid58().s[10]++;
          setProgress(function (prev) {
            cov_29jqgxid58().f[3]++;
            cov_29jqgxid58().s[11]++;
            return Math.min(prev + 10, 90);
          });
        }, 200));
        var report = (cov_29jqgxid58().s[12]++, yield exportService.generateProgressReport(userId, options));
        cov_29jqgxid58().s[13]++;
        clearInterval(progressInterval);
        cov_29jqgxid58().s[14]++;
        setProgress(100);
        cov_29jqgxid58().s[15]++;
        setIsGenerating(false);
        cov_29jqgxid58().s[16]++;
        return report;
      } catch (err) {
        var errorMessage = (cov_29jqgxid58().s[17]++, err instanceof Error ? (cov_29jqgxid58().b[1][0]++, err.message) : (cov_29jqgxid58().b[1][1]++, 'Failed to generate progress report'));
        cov_29jqgxid58().s[18]++;
        setError(errorMessage);
        cov_29jqgxid58().s[19]++;
        setIsGenerating(false);
        cov_29jqgxid58().s[20]++;
        setProgress(0);
        cov_29jqgxid58().s[21]++;
        throw err;
      }
    });
    return function (_x) {
      return _ref9.apply(this, arguments);
    };
  }(), []));
  var exportProgressReport = (cov_29jqgxid58().s[22]++, useCallback(function () {
    var _ref0 = _asyncToGenerator(function* (report, options) {
      cov_29jqgxid58().f[4]++;
      cov_29jqgxid58().s[23]++;
      try {
        cov_29jqgxid58().s[24]++;
        setError(null);
        cov_29jqgxid58().s[25]++;
        setIsExporting(true);
        cov_29jqgxid58().s[26]++;
        setProgress(0);
        var progressInterval = (cov_29jqgxid58().s[27]++, setInterval(function () {
          cov_29jqgxid58().f[5]++;
          cov_29jqgxid58().s[28]++;
          setProgress(function (prev) {
            cov_29jqgxid58().f[6]++;
            cov_29jqgxid58().s[29]++;
            return Math.min(prev + 15, 90);
          });
        }, 300));
        var fileUri = (cov_29jqgxid58().s[30]++, yield exportService.exportProgressReport(report, options));
        cov_29jqgxid58().s[31]++;
        clearInterval(progressInterval);
        cov_29jqgxid58().s[32]++;
        setProgress(100);
        cov_29jqgxid58().s[33]++;
        setIsExporting(false);
        cov_29jqgxid58().s[34]++;
        return fileUri;
      } catch (err) {
        var errorMessage = (cov_29jqgxid58().s[35]++, err instanceof Error ? (cov_29jqgxid58().b[2][0]++, err.message) : (cov_29jqgxid58().b[2][1]++, 'Failed to export progress report'));
        cov_29jqgxid58().s[36]++;
        setError(errorMessage);
        cov_29jqgxid58().s[37]++;
        setIsExporting(false);
        cov_29jqgxid58().s[38]++;
        setProgress(0);
        cov_29jqgxid58().s[39]++;
        throw err;
      }
    });
    return function (_x2, _x3) {
      return _ref0.apply(this, arguments);
    };
  }(), []));
  var shareExportedFile = (cov_29jqgxid58().s[40]++, useCallback(function () {
    var _ref1 = _asyncToGenerator(function* (fileUri) {
      cov_29jqgxid58().f[7]++;
      cov_29jqgxid58().s[41]++;
      try {
        cov_29jqgxid58().s[42]++;
        setError(null);
        cov_29jqgxid58().s[43]++;
        yield exportService.shareExportedFile(fileUri);
      } catch (err) {
        var errorMessage = (cov_29jqgxid58().s[44]++, err instanceof Error ? (cov_29jqgxid58().b[3][0]++, err.message) : (cov_29jqgxid58().b[3][1]++, 'Failed to share file'));
        cov_29jqgxid58().s[45]++;
        setError(errorMessage);
        cov_29jqgxid58().s[46]++;
        throw err;
      }
    });
    return function (_x4) {
      return _ref1.apply(this, arguments);
    };
  }(), []));
  var exportTrainingData = (cov_29jqgxid58().s[47]++, useCallback(function () {
    var _ref10 = _asyncToGenerator(function* (userId, sessionIds, format) {
      cov_29jqgxid58().f[8]++;
      cov_29jqgxid58().s[48]++;
      try {
        cov_29jqgxid58().s[49]++;
        setError(null);
        cov_29jqgxid58().s[50]++;
        setIsExporting(true);
        cov_29jqgxid58().s[51]++;
        setProgress(0);
        var progressInterval = (cov_29jqgxid58().s[52]++, setInterval(function () {
          cov_29jqgxid58().f[9]++;
          cov_29jqgxid58().s[53]++;
          setProgress(function (prev) {
            cov_29jqgxid58().f[10]++;
            cov_29jqgxid58().s[54]++;
            return Math.min(prev + 20, 90);
          });
        }, 200));
        var fileUri = (cov_29jqgxid58().s[55]++, yield exportService.exportTrainingData(userId, sessionIds, format));
        cov_29jqgxid58().s[56]++;
        clearInterval(progressInterval);
        cov_29jqgxid58().s[57]++;
        setProgress(100);
        cov_29jqgxid58().s[58]++;
        setIsExporting(false);
        cov_29jqgxid58().s[59]++;
        return fileUri;
      } catch (err) {
        var errorMessage = (cov_29jqgxid58().s[60]++, err instanceof Error ? (cov_29jqgxid58().b[4][0]++, err.message) : (cov_29jqgxid58().b[4][1]++, 'Failed to export training data'));
        cov_29jqgxid58().s[61]++;
        setError(errorMessage);
        cov_29jqgxid58().s[62]++;
        setIsExporting(false);
        cov_29jqgxid58().s[63]++;
        setProgress(0);
        cov_29jqgxid58().s[64]++;
        throw err;
      }
    });
    return function (_x5, _x6, _x7) {
      return _ref10.apply(this, arguments);
    };
  }(), []));
  var exportMatchData = (cov_29jqgxid58().s[65]++, useCallback(function () {
    var _ref11 = _asyncToGenerator(function* (userId, matchIds, format) {
      cov_29jqgxid58().f[11]++;
      cov_29jqgxid58().s[66]++;
      try {
        cov_29jqgxid58().s[67]++;
        setError(null);
        cov_29jqgxid58().s[68]++;
        setIsExporting(true);
        cov_29jqgxid58().s[69]++;
        setProgress(0);
        var progressInterval = (cov_29jqgxid58().s[70]++, setInterval(function () {
          cov_29jqgxid58().f[12]++;
          cov_29jqgxid58().s[71]++;
          setProgress(function (prev) {
            cov_29jqgxid58().f[13]++;
            cov_29jqgxid58().s[72]++;
            return Math.min(prev + 20, 90);
          });
        }, 200));
        var fileUri = (cov_29jqgxid58().s[73]++, yield exportService.exportMatchData(userId, matchIds, format));
        cov_29jqgxid58().s[74]++;
        clearInterval(progressInterval);
        cov_29jqgxid58().s[75]++;
        setProgress(100);
        cov_29jqgxid58().s[76]++;
        setIsExporting(false);
        cov_29jqgxid58().s[77]++;
        return fileUri;
      } catch (err) {
        var errorMessage = (cov_29jqgxid58().s[78]++, err instanceof Error ? (cov_29jqgxid58().b[5][0]++, err.message) : (cov_29jqgxid58().b[5][1]++, 'Failed to export match data'));
        cov_29jqgxid58().s[79]++;
        setError(errorMessage);
        cov_29jqgxid58().s[80]++;
        setIsExporting(false);
        cov_29jqgxid58().s[81]++;
        setProgress(0);
        cov_29jqgxid58().s[82]++;
        throw err;
      }
    });
    return function (_x8, _x9, _x0) {
      return _ref11.apply(this, arguments);
    };
  }(), []));
  var importData = (cov_29jqgxid58().s[83]++, useCallback(_asyncToGenerator(function* () {
    cov_29jqgxid58().f[14]++;
    cov_29jqgxid58().s[84]++;
    try {
      cov_29jqgxid58().s[85]++;
      setError(null);
      cov_29jqgxid58().s[86]++;
      return yield exportService.importData();
    } catch (err) {
      var errorMessage = (cov_29jqgxid58().s[87]++, err instanceof Error ? (cov_29jqgxid58().b[6][0]++, err.message) : (cov_29jqgxid58().b[6][1]++, 'Failed to import data'));
      cov_29jqgxid58().s[88]++;
      setError(errorMessage);
      cov_29jqgxid58().s[89]++;
      throw err;
    }
  }), []));
  cov_29jqgxid58().s[90]++;
  return {
    isGenerating: isGenerating,
    isExporting: isExporting,
    progress: progress,
    generateProgressReport: generateProgressReport,
    exportProgressReport: exportProgressReport,
    shareExportedFile: shareExportedFile,
    exportTrainingData: exportTrainingData,
    exportMatchData: exportMatchData,
    importData: importData,
    error: error
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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