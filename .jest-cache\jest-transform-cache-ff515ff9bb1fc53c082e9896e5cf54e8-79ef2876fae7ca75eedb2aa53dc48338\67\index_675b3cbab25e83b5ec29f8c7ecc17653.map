{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "useColorScheme", "React", "_Appearance", "_React$useState", "useState", "getColorScheme", "colorScheme", "setColorScheme", "useEffect", "listener", "appearance", "_Appearance$addChange", "addChangeListener", "remove", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = useColorScheme;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _Appearance = _interopRequireDefault(require(\"../Appearance\"));\nfunction useColorScheme() {\n  var _React$useState = React.useState(_Appearance.default.getColorScheme()),\n    colorScheme = _React$useState[0],\n    setColorScheme = _React$useState[1];\n  React.useEffect(() => {\n    function listener(appearance) {\n      setColorScheme(appearance.colorScheme);\n    }\n    var _Appearance$addChange = _Appearance.default.addChangeListener(listener),\n      remove = _Appearance$addChange.remove;\n    return remove;\n  });\n  return colorScheme;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAGI,cAAc;AAChC,IAAIC,KAAK,GAAGJ,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIO,WAAW,GAAGR,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,SAASK,cAAcA,CAAA,EAAG;EACxB,IAAIG,eAAe,GAAGF,KAAK,CAACG,QAAQ,CAACF,WAAW,CAACN,OAAO,CAACS,cAAc,CAAC,CAAC,CAAC;IACxEC,WAAW,GAAGH,eAAe,CAAC,CAAC,CAAC;IAChCI,cAAc,GAAGJ,eAAe,CAAC,CAAC,CAAC;EACrCF,KAAK,CAACO,SAAS,CAAC,YAAM;IACpB,SAASC,QAAQA,CAACC,UAAU,EAAE;MAC5BH,cAAc,CAACG,UAAU,CAACJ,WAAW,CAAC;IACxC;IACA,IAAIK,qBAAqB,GAAGT,WAAW,CAACN,OAAO,CAACgB,iBAAiB,CAACH,QAAQ,CAAC;MACzEI,MAAM,GAAGF,qBAAqB,CAACE,MAAM;IACvC,OAAOA,MAAM;EACf,CAAC,CAAC;EACF,OAAOP,WAAW;AACpB;AACAQ,MAAM,CAAChB,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}