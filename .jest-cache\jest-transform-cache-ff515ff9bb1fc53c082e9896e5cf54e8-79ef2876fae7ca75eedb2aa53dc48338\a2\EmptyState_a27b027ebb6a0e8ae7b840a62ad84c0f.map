{"version": 3, "names": ["React", "View", "Text", "StyleSheet", "Target", "<PERSON><PERSON>", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_26b5a2ib5r", "s", "primary", "white", "dark", "gray", "lightGray", "EmptyState", "_ref", "title", "message", "onAction", "_ref$actionText", "actionText", "b", "_ref$compact", "compact", "f", "style", "styles", "container", "compactContainer", "children", "content", "iconContainer", "compactIcon", "size", "color", "compactTitle", "compactMessage", "onPress", "actionButton", "compactButton", "filter", "Boolean", "create", "flex", "alignItems", "justifyContent", "padding", "paddingVertical", "max<PERSON><PERSON><PERSON>", "width", "height", "borderRadius", "backgroundColor", "marginBottom", "fontSize", "fontFamily", "textAlign", "lineHeight", "min<PERSON><PERSON><PERSON>"], "sources": ["EmptyState.tsx"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet } from 'react-native';\nimport { Target } from 'lucide-react-native';\nimport Button from './Button';\n\nconst colors = {\n  primary: '#23ba16',\n  white: '#ffffff',\n  dark: '#171717',\n  gray: '#6b7280',\n  lightGray: '#f9fafb',\n};\n\ninterface EmptyStateProps {\n  title: string;\n  message: string;\n  onAction?: () => void;\n  actionText?: string;\n  compact?: boolean;\n}\n\nexport default function EmptyState({ \n  title, \n  message, \n  onAction, \n  actionText = 'Get Started',\n  compact = false\n}: EmptyStateProps) {\n  return (\n    <View style={[styles.container, compact && styles.compactContainer]}>\n      <View style={styles.content}>\n        <View style={[styles.iconContainer, compact && styles.compactIcon]}>\n          <Target size={compact ? 32 : 48} color={colors.primary} />\n        </View>\n        <Text style={[styles.title, compact && styles.compactTitle]}>{title}</Text>\n        <Text style={[styles.message, compact && styles.compactMessage]}>{message}</Text>\n        {onAction && (\n          <Button\n            title={actionText}\n            onPress={onAction}\n            style={[styles.actionButton, compact && styles.compactButton].filter(Boolean) as any}\n            size={compact ? 'small' : 'medium'}\n          />\n        )}\n      </View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: 24,\n  },\n  compactContainer: {\n    flex: 0,\n    paddingVertical: 32,\n  },\n  content: {\n    alignItems: 'center',\n    maxWidth: 280,\n  },\n  iconContainer: {\n    width: 80,\n    height: 80,\n    borderRadius: 40,\n    backgroundColor: colors.lightGray,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginBottom: 24,\n  },\n  compactIcon: {\n    width: 60,\n    height: 60,\n    borderRadius: 30,\n    marginBottom: 16,\n  },\n  title: {\n    fontSize: 20,\n    fontFamily: 'Inter-Bold',\n    color: colors.dark,\n    marginBottom: 8,\n    textAlign: 'center',\n  },\n  compactTitle: {\n    fontSize: 16,\n    marginBottom: 6,\n  },\n  message: {\n    fontSize: 16,\n    fontFamily: 'Inter-Regular',\n    color: colors.gray,\n    textAlign: 'center',\n    lineHeight: 22,\n    marginBottom: 24,\n  },\n  compactMessage: {\n    fontSize: 14,\n    marginBottom: 16,\n  },\n  actionButton: {\n    minWidth: 120,\n  },\n  compactButton: {\n    minWidth: 100,\n  },\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,cAAc;AACrD,SAASC,MAAM,QAAQ,qBAAqB;AAC5C,OAAOC,MAAM;AAAiB,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE9B,IAAMC,MAAM,IAAAC,cAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE;AACb,CAAC;AAUD,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAMd;EAAA,IALlBC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACLC,OAAO,GAAAF,IAAA,CAAPE,OAAO;IACPC,QAAQ,GAAAH,IAAA,CAARG,QAAQ;IAAAC,eAAA,GAAAJ,IAAA,CACRK,UAAU;IAAVA,UAAU,GAAAD,eAAA,eAAAZ,cAAA,GAAAc,CAAA,UAAG,aAAa,IAAAF,eAAA;IAAAG,YAAA,GAAAP,IAAA,CAC1BQ,OAAO;IAAPA,OAAO,GAAAD,YAAA,eAAAf,cAAA,GAAAc,CAAA,UAAG,KAAK,IAAAC,YAAA;EAAAf,cAAA,GAAAiB,CAAA;EAAAjB,cAAA,GAAAC,CAAA;EAEf,OACEL,IAAA,CAACN,IAAI;IAAC4B,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE,CAAApB,cAAA,GAAAc,CAAA,UAAAE,OAAO,MAAAhB,cAAA,GAAAc,CAAA,UAAIK,MAAM,CAACE,gBAAgB,EAAE;IAAAC,QAAA,EAClExB,KAAA,CAACR,IAAI;MAAC4B,KAAK,EAAEC,MAAM,CAACI,OAAQ;MAAAD,QAAA,GAC1B1B,IAAA,CAACN,IAAI;QAAC4B,KAAK,EAAE,CAACC,MAAM,CAACK,aAAa,EAAE,CAAAxB,cAAA,GAAAc,CAAA,UAAAE,OAAO,MAAAhB,cAAA,GAAAc,CAAA,UAAIK,MAAM,CAACM,WAAW,EAAE;QAAAH,QAAA,EACjE1B,IAAA,CAACH,MAAM;UAACiC,IAAI,EAAEV,OAAO,IAAAhB,cAAA,GAAAc,CAAA,UAAG,EAAE,KAAAd,cAAA,GAAAc,CAAA,UAAG,EAAE,CAAC;UAACa,KAAK,EAAE5B,MAAM,CAACG;QAAQ,CAAE;MAAC,CACtD,CAAC,EACPN,IAAA,CAACL,IAAI;QAAC2B,KAAK,EAAE,CAACC,MAAM,CAACV,KAAK,EAAE,CAAAT,cAAA,GAAAc,CAAA,UAAAE,OAAO,MAAAhB,cAAA,GAAAc,CAAA,UAAIK,MAAM,CAACS,YAAY,EAAE;QAAAN,QAAA,EAAEb;MAAK,CAAO,CAAC,EAC3Eb,IAAA,CAACL,IAAI;QAAC2B,KAAK,EAAE,CAACC,MAAM,CAACT,OAAO,EAAE,CAAAV,cAAA,GAAAc,CAAA,UAAAE,OAAO,MAAAhB,cAAA,GAAAc,CAAA,UAAIK,MAAM,CAACU,cAAc,EAAE;QAAAP,QAAA,EAAEZ;MAAO,CAAO,CAAC,EAChF,CAAAV,cAAA,GAAAc,CAAA,UAAAH,QAAQ,MAAAX,cAAA,GAAAc,CAAA,UACPlB,IAAA,CAACF,MAAM;QACLe,KAAK,EAAEI,UAAW;QAClBiB,OAAO,EAAEnB,QAAS;QAClBO,KAAK,EAAE,CAACC,MAAM,CAACY,YAAY,EAAE,CAAA/B,cAAA,GAAAc,CAAA,UAAAE,OAAO,MAAAhB,cAAA,GAAAc,CAAA,UAAIK,MAAM,CAACa,aAAa,EAAC,CAACC,MAAM,CAACC,OAAO,CAAS;QACrFR,IAAI,EAAEV,OAAO,IAAAhB,cAAA,GAAAc,CAAA,UAAG,OAAO,KAAAd,cAAA,GAAAc,CAAA,UAAG,QAAQ;MAAC,CACpC,CAAC,CACH;IAAA,CACG;EAAC,CACH,CAAC;AAEX;AAEA,IAAMK,MAAM,IAAAnB,cAAA,GAAAC,CAAA,OAAGT,UAAU,CAAC2C,MAAM,CAAC;EAC/Bf,SAAS,EAAE;IACTgB,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE;EACX,CAAC;EACDlB,gBAAgB,EAAE;IAChBe,IAAI,EAAE,CAAC;IACPI,eAAe,EAAE;EACnB,CAAC;EACDjB,OAAO,EAAE;IACPc,UAAU,EAAE,QAAQ;IACpBI,QAAQ,EAAE;EACZ,CAAC;EACDjB,aAAa,EAAE;IACbkB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAE9C,MAAM,CAACO,SAAS;IACjC+B,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBQ,YAAY,EAAE;EAChB,CAAC;EACDrB,WAAW,EAAE;IACXiB,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBE,YAAY,EAAE;EAChB,CAAC;EACDrC,KAAK,EAAE;IACLsC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,YAAY;IACxBrB,KAAK,EAAE5B,MAAM,CAACK,IAAI;IAClB0C,YAAY,EAAE,CAAC;IACfG,SAAS,EAAE;EACb,CAAC;EACDrB,YAAY,EAAE;IACZmB,QAAQ,EAAE,EAAE;IACZD,YAAY,EAAE;EAChB,CAAC;EACDpC,OAAO,EAAE;IACPqC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3BrB,KAAK,EAAE5B,MAAM,CAACM,IAAI;IAClB4C,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,EAAE;IACdJ,YAAY,EAAE;EAChB,CAAC;EACDjB,cAAc,EAAE;IACdkB,QAAQ,EAAE,EAAE;IACZD,YAAY,EAAE;EAChB,CAAC;EACDf,YAAY,EAAE;IACZoB,QAAQ,EAAE;EACZ,CAAC;EACDnB,aAAa,EAAE;IACbmB,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}