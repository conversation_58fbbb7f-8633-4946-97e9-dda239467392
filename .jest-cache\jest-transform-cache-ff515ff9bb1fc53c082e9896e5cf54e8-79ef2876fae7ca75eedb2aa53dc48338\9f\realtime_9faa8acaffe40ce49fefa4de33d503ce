5f2fcac358f8529d699d9d1d644a89f1
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_coj58o3mv() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\realtime.ts";
  var hash = "df8e35958ce06db705fa287d3c6a94fae4409de2";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\realtime.ts",
    statementMap: {
      "0": {
        start: {
          line: 23,
          column: 51
        },
        end: {
          line: 23,
          column: 60
        }
      },
      "1": {
        start: {
          line: 24,
          column: 85
        },
        end: {
          line: 24,
          column: 94
        }
      },
      "2": {
        start: {
          line: 25,
          column: 79
        },
        end: {
          line: 25,
          column: 88
        }
      },
      "3": {
        start: {
          line: 31,
          column: 4
        },
        end: {
          line: 31,
          column: 45
        }
      },
      "4": {
        start: {
          line: 33,
          column: 24
        },
        end: {
          line: 33,
          column: 49
        }
      },
      "5": {
        start: {
          line: 35,
          column: 4
        },
        end: {
          line: 67,
          column: 5
        }
      },
      "6": {
        start: {
          line: 36,
          column: 22
        },
        end: {
          line: 64,
          column: 20
        }
      },
      "7": {
        start: {
          line: 47,
          column: 33
        },
        end: {
          line: 47,
          column: 68
        }
      },
      "8": {
        start: {
          line: 48,
          column: 12
        },
        end: {
          line: 48,
          column: 71
        }
      },
      "9": {
        start: {
          line: 48,
          column: 53
        },
        end: {
          line: 48,
          column: 69
        }
      },
      "10": {
        start: {
          line: 60,
          column: 33
        },
        end: {
          line: 60,
          column: 68
        }
      },
      "11": {
        start: {
          line: 61,
          column: 12
        },
        end: {
          line: 61,
          column: 71
        }
      },
      "12": {
        start: {
          line: 61,
          column: 53
        },
        end: {
          line: 61,
          column: 69
        }
      },
      "13": {
        start: {
          line: 66,
          column: 6
        },
        end: {
          line: 66,
          column: 46
        }
      },
      "14": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 79,
          column: 6
        }
      },
      "15": {
        start: {
          line: 71,
          column: 6
        },
        end: {
          line: 71,
          column: 50
        }
      },
      "16": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 78,
          column: 7
        }
      },
      "17": {
        start: {
          line: 73,
          column: 24
        },
        end: {
          line: 73,
          column: 54
        }
      },
      "18": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 77,
          column: 9
        }
      },
      "19": {
        start: {
          line: 75,
          column: 10
        },
        end: {
          line: 75,
          column: 42
        }
      },
      "20": {
        start: {
          line: 76,
          column: 10
        },
        end: {
          line: 76,
          column: 44
        }
      },
      "21": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 41
        }
      },
      "22": {
        start: {
          line: 88,
          column: 24
        },
        end: {
          line: 88,
          column: 44
        }
      },
      "23": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 102,
          column: 5
        }
      },
      "24": {
        start: {
          line: 91,
          column: 22
        },
        end: {
          line: 99,
          column: 20
        }
      },
      "25": {
        start: {
          line: 94,
          column: 26
        },
        end: {
          line: 94,
          column: 68
        }
      },
      "26": {
        start: {
          line: 95,
          column: 10
        },
        end: {
          line: 97,
          column: 11
        }
      },
      "27": {
        start: {
          line: 96,
          column: 12
        },
        end: {
          line: 96,
          column: 62
        }
      },
      "28": {
        start: {
          line: 96,
          column: 49
        },
        end: {
          line: 96,
          column: 60
        }
      },
      "29": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 46
        }
      },
      "30": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 114,
          column: 6
        }
      },
      "31": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 106,
          column: 46
        }
      },
      "32": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 113,
          column: 7
        }
      },
      "33": {
        start: {
          line: 108,
          column: 24
        },
        end: {
          line: 108,
          column: 54
        }
      },
      "34": {
        start: {
          line: 109,
          column: 8
        },
        end: {
          line: 112,
          column: 9
        }
      },
      "35": {
        start: {
          line: 110,
          column: 10
        },
        end: {
          line: 110,
          column: 42
        }
      },
      "36": {
        start: {
          line: 111,
          column: 10
        },
        end: {
          line: 111,
          column: 44
        }
      },
      "37": {
        start: {
          line: 121,
          column: 24
        },
        end: {
          line: 121,
          column: 44
        }
      },
      "38": {
        start: {
          line: 122,
          column: 20
        },
        end: {
          line: 122,
          column: 50
        }
      },
      "39": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "40": {
        start: {
          line: 125,
          column: 55
        },
        end: {
          line: 131,
          column: 7
        }
      },
      "41": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 137,
          column: 9
        }
      },
      "42": {
        start: {
          line: 145,
          column: 24
        },
        end: {
          line: 145,
          column: 47
        }
      },
      "43": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 165,
          column: 5
        }
      },
      "44": {
        start: {
          line: 148,
          column: 22
        },
        end: {
          line: 162,
          column: 20
        }
      },
      "45": {
        start: {
          line: 159,
          column: 12
        },
        end: {
          line: 159,
          column: 34
        }
      },
      "46": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 46
        }
      },
      "47": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 174,
          column: 6
        }
      },
      "48": {
        start: {
          line: 169,
          column: 22
        },
        end: {
          line: 169,
          column: 52
        }
      },
      "49": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 173,
          column: 7
        }
      },
      "50": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 171,
          column: 40
        }
      },
      "51": {
        start: {
          line: 172,
          column: 8
        },
        end: {
          line: 172,
          column: 42
        }
      },
      "52": {
        start: {
          line: 181,
          column: 24
        },
        end: {
          line: 181,
          column: 53
        }
      },
      "53": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 201,
          column: 5
        }
      },
      "54": {
        start: {
          line: 184,
          column: 22
        },
        end: {
          line: 198,
          column: 20
        }
      },
      "55": {
        start: {
          line: 195,
          column: 12
        },
        end: {
          line: 195,
          column: 34
        }
      },
      "56": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 200,
          column: 46
        }
      },
      "57": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 210,
          column: 6
        }
      },
      "58": {
        start: {
          line: 205,
          column: 22
        },
        end: {
          line: 205,
          column: 52
        }
      },
      "59": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 209,
          column: 7
        }
      },
      "60": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 207,
          column: 40
        }
      },
      "61": {
        start: {
          line: 208,
          column: 8
        },
        end: {
          line: 208,
          column: 42
        }
      },
      "62": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 247,
          column: 5
        }
      },
      "63": {
        start: {
          line: 223,
          column: 30
        },
        end: {
          line: 233,
          column: 17
        }
      },
      "64": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 238,
          column: 7
        }
      },
      "65": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 236,
          column: 61
        }
      },
      "66": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 56
        }
      },
      "67": {
        start: {
          line: 240,
          column: 6
        },
        end: {
          line: 240,
          column: 37
        }
      },
      "68": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 54
        }
      },
      "69": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 246,
          column: 8
        }
      },
      "70": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 256,
          column: 7
        }
      },
      "71": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 38
        }
      },
      "72": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 257,
          column: 26
        }
      },
      "73": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 258,
          column: 39
        }
      },
      "74": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 259,
          column: 35
        }
      },
      "75": {
        start: {
          line: 266,
          column: 4
        },
        end: {
          line: 269,
          column: 6
        }
      },
      "76": {
        start: {
          line: 273,
          column: 31
        },
        end: {
          line: 273,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 30,
            column: 2
          },
          end: {
            line: 30,
            column: 3
          }
        },
        loc: {
          start: {
            line: 30,
            column: 99
          },
          end: {
            line: 80,
            column: 3
          }
        },
        line: 30
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 46,
            column: 10
          },
          end: {
            line: 46,
            column: 11
          }
        },
        loc: {
          start: {
            line: 46,
            column: 23
          },
          end: {
            line: 49,
            column: 11
          }
        },
        line: 46
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 48,
            column: 47
          },
          end: {
            line: 48,
            column: 48
          }
        },
        loc: {
          start: {
            line: 48,
            column: 53
          },
          end: {
            line: 48,
            column: 69
          }
        },
        line: 48
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 59,
            column: 10
          },
          end: {
            line: 59,
            column: 11
          }
        },
        loc: {
          start: {
            line: 59,
            column: 23
          },
          end: {
            line: 62,
            column: 11
          }
        },
        line: 59
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 61,
            column: 47
          },
          end: {
            line: 61,
            column: 48
          }
        },
        loc: {
          start: {
            line: 61,
            column: 53
          },
          end: {
            line: 61,
            column: 69
          }
        },
        line: 61
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 70,
            column: 11
          },
          end: {
            line: 70,
            column: 12
          }
        },
        loc: {
          start: {
            line: 70,
            column: 17
          },
          end: {
            line: 79,
            column: 5
          }
        },
        line: 70
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 85,
            column: 2
          },
          end: {
            line: 85,
            column: 3
          }
        },
        loc: {
          start: {
            line: 85,
            column: 92
          },
          end: {
            line: 115,
            column: 3
          }
        },
        line: 85
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 93,
            column: 56
          },
          end: {
            line: 93,
            column: 57
          }
        },
        loc: {
          start: {
            line: 93,
            column: 69
          },
          end: {
            line: 98,
            column: 9
          }
        },
        line: 93
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 96,
            column: 43
          },
          end: {
            line: 96,
            column: 44
          }
        },
        loc: {
          start: {
            line: 96,
            column: 49
          },
          end: {
            line: 96,
            column: 60
          }
        },
        line: 96
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 105,
            column: 11
          },
          end: {
            line: 105,
            column: 12
          }
        },
        loc: {
          start: {
            line: 105,
            column: 17
          },
          end: {
            line: 114,
            column: 5
          }
        },
        line: 105
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 120,
            column: 2
          },
          end: {
            line: 120,
            column: 3
          }
        },
        loc: {
          start: {
            line: 120,
            column: 107
          },
          end: {
            line: 139,
            column: 3
          }
        },
        line: 120
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 144,
            column: 2
          },
          end: {
            line: 144,
            column: 3
          }
        },
        loc: {
          start: {
            line: 144,
            column: 74
          },
          end: {
            line: 175,
            column: 3
          }
        },
        line: 144
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 158,
            column: 10
          },
          end: {
            line: 158,
            column: 11
          }
        },
        loc: {
          start: {
            line: 158,
            column: 23
          },
          end: {
            line: 160,
            column: 11
          }
        },
        line: 158
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 168,
            column: 11
          },
          end: {
            line: 168,
            column: 12
          }
        },
        loc: {
          start: {
            line: 168,
            column: 17
          },
          end: {
            line: 174,
            column: 5
          }
        },
        line: 168
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 180,
            column: 2
          },
          end: {
            line: 180,
            column: 3
          }
        },
        loc: {
          start: {
            line: 180,
            column: 80
          },
          end: {
            line: 211,
            column: 3
          }
        },
        line: 180
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 194,
            column: 10
          },
          end: {
            line: 194,
            column: 11
          }
        },
        loc: {
          start: {
            line: 194,
            column: 23
          },
          end: {
            line: 196,
            column: 11
          }
        },
        line: 194
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 204,
            column: 11
          },
          end: {
            line: 204,
            column: 12
          }
        },
        loc: {
          start: {
            line: 204,
            column: 17
          },
          end: {
            line: 210,
            column: 5
          }
        },
        line: 204
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 216,
            column: 2
          },
          end: {
            line: 216,
            column: 3
          }
        },
        loc: {
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 248,
            column: 3
          }
        },
        line: 221
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 253,
            column: 2
          },
          end: {
            line: 253,
            column: 3
          }
        },
        loc: {
          start: {
            line: 253,
            column: 15
          },
          end: {
            line: 260,
            column: 3
          }
        },
        line: 253
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 254,
            column: 26
          },
          end: {
            line: 254,
            column: 27
          }
        },
        loc: {
          start: {
            line: 254,
            column: 39
          },
          end: {
            line: 256,
            column: 5
          }
        },
        line: 254
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 265,
            column: 2
          },
          end: {
            line: 265,
            column: 3
          }
        },
        loc: {
          start: {
            line: 265,
            column: 24
          },
          end: {
            line: 270,
            column: 3
          }
        },
        line: 265
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 35,
            column: 4
          },
          end: {
            line: 67,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 35
      },
      "1": {
        loc: {
          start: {
            line: 72,
            column: 6
          },
          end: {
            line: 78,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 6
          },
          end: {
            line: 78,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "2": {
        loc: {
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 77,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 8
          },
          end: {
            line: 77,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "3": {
        loc: {
          start: {
            line: 90,
            column: 4
          },
          end: {
            line: 102,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 90,
            column: 4
          },
          end: {
            line: 102,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 90
      },
      "4": {
        loc: {
          start: {
            line: 95,
            column: 10
          },
          end: {
            line: 97,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 10
          },
          end: {
            line: 97,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "5": {
        loc: {
          start: {
            line: 107,
            column: 6
          },
          end: {
            line: 113,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 6
          },
          end: {
            line: 113,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "6": {
        loc: {
          start: {
            line: 109,
            column: 8
          },
          end: {
            line: 112,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 109,
            column: 8
          },
          end: {
            line: 112,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 109
      },
      "7": {
        loc: {
          start: {
            line: 124,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 124,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 124
      },
      "8": {
        loc: {
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 165,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 165,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "9": {
        loc: {
          start: {
            line: 170,
            column: 6
          },
          end: {
            line: 173,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 6
          },
          end: {
            line: 173,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "10": {
        loc: {
          start: {
            line: 183,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 183,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 183
      },
      "11": {
        loc: {
          start: {
            line: 206,
            column: 6
          },
          end: {
            line: 209,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 206,
            column: 6
          },
          end: {
            line: 209,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 206
      },
      "12": {
        loc: {
          start: {
            line: 235,
            column: 6
          },
          end: {
            line: 238,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 235,
            column: 6
          },
          end: {
            line: 238,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 235
      },
      "13": {
        loc: {
          start: {
            line: 245,
            column: 15
          },
          end: {
            line: 245,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 245,
            column: 40
          },
          end: {
            line: 245,
            column: 53
          }
        }, {
          start: {
            line: 245,
            column: 56
          },
          end: {
            line: 245,
            column: 71
          }
        }],
        line: 245
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "df8e35958ce06db705fa287d3c6a94fae4409de2"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_coj58o3mv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_coj58o3mv();
import { supabase } from "../lib/supabase";
var RealtimeService = function () {
  function RealtimeService() {
    _classCallCheck(this, RealtimeService);
    this.channels = (cov_coj58o3mv().s[0]++, new Map());
    this.notificationCallbacks = (cov_coj58o3mv().s[1]++, new Set());
    this.coachingCallbacks = (cov_coj58o3mv().s[2]++, new Set());
  }
  return _createClass(RealtimeService, [{
    key: "subscribeToNotifications",
    value: function subscribeToNotifications(userId, callback) {
      var _this = this;
      cov_coj58o3mv().f[0]++;
      cov_coj58o3mv().s[3]++;
      this.notificationCallbacks.add(callback);
      var channelName = (cov_coj58o3mv().s[4]++, `notifications:${userId}`);
      cov_coj58o3mv().s[5]++;
      if (!this.channels.has(channelName)) {
        cov_coj58o3mv().b[0][0]++;
        var channel = (cov_coj58o3mv().s[6]++, supabase.channel(channelName).on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        }, function (payload) {
          cov_coj58o3mv().f[1]++;
          var notification = (cov_coj58o3mv().s[7]++, payload.new);
          cov_coj58o3mv().s[8]++;
          _this.notificationCallbacks.forEach(function (cb) {
            cov_coj58o3mv().f[2]++;
            cov_coj58o3mv().s[9]++;
            return cb(notification);
          });
        }).on('postgres_changes', {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`
        }, function (payload) {
          cov_coj58o3mv().f[3]++;
          var notification = (cov_coj58o3mv().s[10]++, payload.new);
          cov_coj58o3mv().s[11]++;
          _this.notificationCallbacks.forEach(function (cb) {
            cov_coj58o3mv().f[4]++;
            cov_coj58o3mv().s[12]++;
            return cb(notification);
          });
        }).subscribe());
        cov_coj58o3mv().s[13]++;
        this.channels.set(channelName, channel);
      } else {
        cov_coj58o3mv().b[0][1]++;
      }
      cov_coj58o3mv().s[14]++;
      return function () {
        cov_coj58o3mv().f[5]++;
        cov_coj58o3mv().s[15]++;
        _this.notificationCallbacks.delete(callback);
        cov_coj58o3mv().s[16]++;
        if (_this.notificationCallbacks.size === 0) {
          cov_coj58o3mv().b[1][0]++;
          var _channel = (cov_coj58o3mv().s[17]++, _this.channels.get(channelName));
          cov_coj58o3mv().s[18]++;
          if (_channel) {
            cov_coj58o3mv().b[2][0]++;
            cov_coj58o3mv().s[19]++;
            supabase.removeChannel(_channel);
            cov_coj58o3mv().s[20]++;
            _this.channels.delete(channelName);
          } else {
            cov_coj58o3mv().b[2][1]++;
          }
        } else {
          cov_coj58o3mv().b[1][1]++;
        }
      };
    }
  }, {
    key: "subscribeToCoaching",
    value: function subscribeToCoaching(userId, callback) {
      var _this2 = this;
      cov_coj58o3mv().f[6]++;
      cov_coj58o3mv().s[21]++;
      this.coachingCallbacks.add(callback);
      var channelName = (cov_coj58o3mv().s[22]++, `coaching:${userId}`);
      cov_coj58o3mv().s[23]++;
      if (!this.channels.has(channelName)) {
        cov_coj58o3mv().b[3][0]++;
        var channel = (cov_coj58o3mv().s[24]++, supabase.channel(channelName).on('broadcast', {
          event: 'coaching_message'
        }, function (payload) {
          cov_coj58o3mv().f[7]++;
          var message = (cov_coj58o3mv().s[25]++, payload.payload);
          cov_coj58o3mv().s[26]++;
          if (message.user_id === userId) {
            cov_coj58o3mv().b[4][0]++;
            cov_coj58o3mv().s[27]++;
            _this2.coachingCallbacks.forEach(function (cb) {
              cov_coj58o3mv().f[8]++;
              cov_coj58o3mv().s[28]++;
              return cb(message);
            });
          } else {
            cov_coj58o3mv().b[4][1]++;
          }
        }).subscribe());
        cov_coj58o3mv().s[29]++;
        this.channels.set(channelName, channel);
      } else {
        cov_coj58o3mv().b[3][1]++;
      }
      cov_coj58o3mv().s[30]++;
      return function () {
        cov_coj58o3mv().f[9]++;
        cov_coj58o3mv().s[31]++;
        _this2.coachingCallbacks.delete(callback);
        cov_coj58o3mv().s[32]++;
        if (_this2.coachingCallbacks.size === 0) {
          cov_coj58o3mv().b[5][0]++;
          var _channel2 = (cov_coj58o3mv().s[33]++, _this2.channels.get(channelName));
          cov_coj58o3mv().s[34]++;
          if (_channel2) {
            cov_coj58o3mv().b[6][0]++;
            cov_coj58o3mv().s[35]++;
            supabase.removeChannel(_channel2);
            cov_coj58o3mv().s[36]++;
            _this2.channels.delete(channelName);
          } else {
            cov_coj58o3mv().b[6][1]++;
          }
        } else {
          cov_coj58o3mv().b[5][1]++;
        }
      };
    }
  }, {
    key: "sendCoachingMessage",
    value: (function () {
      var _sendCoachingMessage = _asyncToGenerator(function* (userId, message, type) {
        cov_coj58o3mv().f[10]++;
        var channelName = (cov_coj58o3mv().s[37]++, `coaching:${userId}`);
        var channel = (cov_coj58o3mv().s[38]++, this.channels.get(channelName));
        cov_coj58o3mv().s[39]++;
        if (channel) {
          cov_coj58o3mv().b[7][0]++;
          var coachingMessage = (cov_coj58o3mv().s[40]++, {
            id: `msg-${Date.now()}`,
            user_id: userId,
            message: message,
            type: type,
            timestamp: new Date().toISOString()
          });
          cov_coj58o3mv().s[41]++;
          yield channel.send({
            type: 'broadcast',
            event: 'coaching_message',
            payload: coachingMessage
          });
        } else {
          cov_coj58o3mv().b[7][1]++;
        }
      });
      function sendCoachingMessage(_x, _x2, _x3) {
        return _sendCoachingMessage.apply(this, arguments);
      }
      return sendCoachingMessage;
    }())
  }, {
    key: "subscribeToSkillUpdates",
    value: function subscribeToSkillUpdates(userId, callback) {
      var _this3 = this;
      cov_coj58o3mv().f[11]++;
      var channelName = (cov_coj58o3mv().s[42]++, `skill_stats:${userId}`);
      cov_coj58o3mv().s[43]++;
      if (!this.channels.has(channelName)) {
        cov_coj58o3mv().b[8][0]++;
        var channel = (cov_coj58o3mv().s[44]++, supabase.channel(channelName).on('postgres_changes', {
          event: 'UPDATE',
          schema: 'public',
          table: 'skill_stats',
          filter: `user_id=eq.${userId}`
        }, function (payload) {
          cov_coj58o3mv().f[12]++;
          cov_coj58o3mv().s[45]++;
          callback(payload.new);
        }).subscribe());
        cov_coj58o3mv().s[46]++;
        this.channels.set(channelName, channel);
      } else {
        cov_coj58o3mv().b[8][1]++;
      }
      cov_coj58o3mv().s[47]++;
      return function () {
        cov_coj58o3mv().f[13]++;
        var channel = (cov_coj58o3mv().s[48]++, _this3.channels.get(channelName));
        cov_coj58o3mv().s[49]++;
        if (channel) {
          cov_coj58o3mv().b[9][0]++;
          cov_coj58o3mv().s[50]++;
          supabase.removeChannel(channel);
          cov_coj58o3mv().s[51]++;
          _this3.channels.delete(channelName);
        } else {
          cov_coj58o3mv().b[9][1]++;
        }
      };
    }
  }, {
    key: "subscribeToTrainingSessions",
    value: function subscribeToTrainingSessions(userId, callback) {
      var _this4 = this;
      cov_coj58o3mv().f[14]++;
      var channelName = (cov_coj58o3mv().s[52]++, `training_sessions:${userId}`);
      cov_coj58o3mv().s[53]++;
      if (!this.channels.has(channelName)) {
        cov_coj58o3mv().b[10][0]++;
        var channel = (cov_coj58o3mv().s[54]++, supabase.channel(channelName).on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'training_sessions',
          filter: `user_id=eq.${userId}`
        }, function (payload) {
          cov_coj58o3mv().f[15]++;
          cov_coj58o3mv().s[55]++;
          callback(payload.new);
        }).subscribe());
        cov_coj58o3mv().s[56]++;
        this.channels.set(channelName, channel);
      } else {
        cov_coj58o3mv().b[10][1]++;
      }
      cov_coj58o3mv().s[57]++;
      return function () {
        cov_coj58o3mv().f[16]++;
        var channel = (cov_coj58o3mv().s[58]++, _this4.channels.get(channelName));
        cov_coj58o3mv().s[59]++;
        if (channel) {
          cov_coj58o3mv().b[11][0]++;
          cov_coj58o3mv().s[60]++;
          supabase.removeChannel(channel);
          cov_coj58o3mv().s[61]++;
          _this4.channels.delete(channelName);
        } else {
          cov_coj58o3mv().b[11][1]++;
        }
      };
    }
  }, {
    key: "createNotification",
    value: (function () {
      var _createNotification = _asyncToGenerator(function* (userId, type, title, message) {
        cov_coj58o3mv().f[17]++;
        cov_coj58o3mv().s[62]++;
        try {
          var _ref = (cov_coj58o3mv().s[63]++, yield supabase.from('notifications').insert({
              user_id: userId,
              type: type,
              title: title,
              message: message,
              read: false
            }).select().single()),
            data = _ref.data,
            error = _ref.error;
          cov_coj58o3mv().s[64]++;
          if (error) {
            cov_coj58o3mv().b[12][0]++;
            cov_coj58o3mv().s[65]++;
            console.error('Error creating notification:', error);
            cov_coj58o3mv().s[66]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_coj58o3mv().b[12][1]++;
          }
          cov_coj58o3mv().s[67]++;
          return {
            success: true,
            data: data
          };
        } catch (error) {
          cov_coj58o3mv().s[68]++;
          console.error('Realtime service error:', error);
          cov_coj58o3mv().s[69]++;
          return {
            success: false,
            error: error instanceof Error ? (cov_coj58o3mv().b[13][0]++, error.message) : (cov_coj58o3mv().b[13][1]++, 'Unknown error')
          };
        }
      });
      function createNotification(_x4, _x5, _x6, _x7) {
        return _createNotification.apply(this, arguments);
      }
      return createNotification;
    }())
  }, {
    key: "disconnect",
    value: function disconnect() {
      cov_coj58o3mv().f[18]++;
      cov_coj58o3mv().s[70]++;
      this.channels.forEach(function (channel) {
        cov_coj58o3mv().f[19]++;
        cov_coj58o3mv().s[71]++;
        supabase.removeChannel(channel);
      });
      cov_coj58o3mv().s[72]++;
      this.channels.clear();
      cov_coj58o3mv().s[73]++;
      this.notificationCallbacks.clear();
      cov_coj58o3mv().s[74]++;
      this.coachingCallbacks.clear();
    }
  }, {
    key: "getConnectionStatus",
    value: function getConnectionStatus() {
      cov_coj58o3mv().f[20]++;
      cov_coj58o3mv().s[75]++;
      return {
        connected: this.channels.size > 0,
        activeChannels: Array.from(this.channels.keys())
      };
    }
  }]);
}();
export var realtimeService = (cov_coj58o3mv().s[76]++, new RealtimeService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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