{"version": 3, "names": ["supabase", "encryptionService", "PrivacyService", "_classCallCheck", "defaultPrivacySettings", "cov_5hqetizdh", "s", "dataCollection", "analytics", "marketing", "socialFeatures", "locationTracking", "videoAnalysis", "aiCoaching", "dataSharing", "notifications", "profileVisibility", "activityVisibility", "dataRetentionDays", "_createClass", "key", "value", "_getPrivacySettings", "_asyncToGenerator", "userId", "f", "_ref", "from", "select", "eq", "single", "data", "error", "b", "code", "createDefaultPrivacySettings", "data_collection", "social_features", "location_tracking", "video_analysis", "ai_coaching", "data_sharing", "profile_visibility", "activity_visibility", "data_retention_days", "console", "getPrivacySettings", "_x", "apply", "arguments", "_updatePrivacySettings", "settings", "currentSettings", "updatedSettings", "Object", "assign", "_ref2", "upsert", "user_id", "updated_at", "Date", "toISOString", "logPrivacyEvent", "changes", "timestamp", "Error", "updatePrivacySettings", "_x2", "_x3", "_requestDataExport", "_ref3", "existingRequest", "request", "requestDate", "status", "_ref4", "insert", "request_date", "processDataExport", "id", "requestId", "requestDataExport", "_x4", "_requestDataDeletion", "deletionType", "length", "undefined", "retainedData", "_ref5", "in", "scheduledDate", "setDate", "getDate", "_ref6", "scheduled_date", "deletion_type", "retained_data", "requestDataDeletion", "_x5", "_cancelDataDeletion", "_ref7", "update", "cancelDataDeletion", "_x6", "_recordConsent", "consentType", "granted", "version", "metadata", "consent", "ip<PERSON><PERSON><PERSON>", "userAgent", "_ref8", "consent_type", "ip_address", "user_agent", "recordConsent", "_x7", "_x8", "_x9", "_x0", "_x1", "_getConsentHistory", "_ref9", "order", "ascending", "map", "record", "getConsentHistory", "_x10", "_anonymizeUserData", "anonymousId", "<PERSON><PERSON>ey", "_ref0", "email", "full_name", "phone", "birth_date", "location", "bio", "avatar_url", "anonymized", "anonymized_at", "userError", "_ref1", "title", "notes", "video_url", "sessionsError", "anonymizeUserData", "_x11", "_processDataExport", "userData", "collectUserData", "encryptedData", "encrypt", "JSON", "stringify", "downloadUrl", "generateSecureDownloadUrl", "expiresAt", "download_url", "expires_at", "_x12", "_x13", "_collectUserData", "_ref10", "Promise", "all", "_ref11", "_slicedToArray", "user", "skillStats", "trainingSessions", "matchResults", "achievements", "aiTips", "privacySettings", "consentHistory", "exportDate", "_x14", "_generateSecureDownloadUrl", "dataId", "_x15", "_createDefaultPrivacySettings", "_ref12", "convertSettingsToDb", "created_at", "_x16", "_logPrivacyEvent", "eventType", "event_type", "_x17", "_x18", "_x19", "privacyService"], "sources": ["privacy.ts"], "sourcesContent": ["import { supabase } from '@/lib/supabase';\nimport { encryptionService } from './encryption';\nimport env from '@/config/environment';\n\n/**\n * Comprehensive Privacy Controls Service\n * Handles data deletion, privacy settings, and GDPR compliance\n */\n\nexport interface PrivacySettings {\n  dataCollection: boolean;\n  analytics: boolean;\n  marketing: boolean;\n  socialFeatures: boolean;\n  locationTracking: boolean;\n  videoAnalysis: boolean;\n  aiCoaching: boolean;\n  dataSharing: boolean;\n  notifications: boolean;\n  profileVisibility: 'private' | 'friends' | 'public';\n  activityVisibility: 'private' | 'friends' | 'public';\n  dataRetentionDays: number;\n}\n\nexport interface DataExportRequest {\n  userId: string;\n  requestDate: Date;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  downloadUrl?: string;\n  expiresAt?: Date;\n}\n\nexport interface DataDeletionRequest {\n  userId: string;\n  requestDate: Date;\n  scheduledDate: Date;\n  status: 'pending' | 'processing' | 'completed' | 'cancelled';\n  deletionType: 'partial' | 'complete';\n  retainedData?: string[];\n}\n\nexport interface ConsentRecord {\n  userId: string;\n  consentType: string;\n  granted: boolean;\n  timestamp: Date;\n  version: string;\n  ipAddress?: string;\n  userAgent?: string;\n}\n\nclass PrivacyService {\n  private readonly defaultPrivacySettings: PrivacySettings = {\n    dataCollection: true,\n    analytics: false,\n    marketing: false,\n    socialFeatures: true,\n    locationTracking: false,\n    videoAnalysis: true,\n    aiCoaching: true,\n    dataSharing: false,\n    notifications: true,\n    profileVisibility: 'private',\n    activityVisibility: 'private',\n    dataRetentionDays: 365,\n  };\n\n  /**\n   * Get user's privacy settings\n   */\n  async getPrivacySettings(userId: string): Promise<PrivacySettings> {\n    try {\n      const { data, error } = await supabase\n        .from('privacy_settings')\n        .select('*')\n        .eq('user_id', userId)\n        .single();\n\n      if (error && error.code !== 'PGRST116') { // Not found error\n        throw error;\n      }\n\n      if (!data) {\n        // Create default privacy settings\n        return await this.createDefaultPrivacySettings(userId);\n      }\n\n      return {\n        dataCollection: data.data_collection,\n        analytics: data.analytics,\n        marketing: data.marketing,\n        socialFeatures: data.social_features,\n        locationTracking: data.location_tracking,\n        videoAnalysis: data.video_analysis,\n        aiCoaching: data.ai_coaching,\n        dataSharing: data.data_sharing,\n        notifications: data.notifications,\n        profileVisibility: data.profile_visibility,\n        activityVisibility: data.activity_visibility,\n        dataRetentionDays: data.data_retention_days,\n      };\n    } catch (error) {\n      console.error('Failed to get privacy settings:', error);\n      return this.defaultPrivacySettings;\n    }\n  }\n\n  /**\n   * Update user's privacy settings\n   */\n  async updatePrivacySettings(\n    userId: string, \n    settings: Partial<PrivacySettings>\n  ): Promise<PrivacySettings> {\n    try {\n      const currentSettings = await this.getPrivacySettings(userId);\n      const updatedSettings = { ...currentSettings, ...settings };\n\n      const { data, error } = await supabase\n        .from('privacy_settings')\n        .upsert({\n          user_id: userId,\n          data_collection: updatedSettings.dataCollection,\n          analytics: updatedSettings.analytics,\n          marketing: updatedSettings.marketing,\n          social_features: updatedSettings.socialFeatures,\n          location_tracking: updatedSettings.locationTracking,\n          video_analysis: updatedSettings.videoAnalysis,\n          ai_coaching: updatedSettings.aiCoaching,\n          data_sharing: updatedSettings.dataSharing,\n          notifications: updatedSettings.notifications,\n          profile_visibility: updatedSettings.profileVisibility,\n          activity_visibility: updatedSettings.activityVisibility,\n          data_retention_days: updatedSettings.dataRetentionDays,\n          updated_at: new Date().toISOString(),\n        })\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      // Log privacy setting change\n      await this.logPrivacyEvent(userId, 'settings_updated', {\n        changes: settings,\n        timestamp: new Date().toISOString(),\n      });\n\n      return updatedSettings;\n    } catch (error) {\n      console.error('Failed to update privacy settings:', error);\n      throw new Error('Failed to update privacy settings');\n    }\n  }\n\n  /**\n   * Request data export (GDPR Article 20)\n   */\n  async requestDataExport(userId: string): Promise<DataExportRequest> {\n    try {\n      // Check for existing pending requests\n      const { data: existingRequest } = await supabase\n        .from('data_export_requests')\n        .select('*')\n        .eq('user_id', userId)\n        .eq('status', 'pending')\n        .single();\n\n      if (existingRequest) {\n        throw new Error('Data export request already pending');\n      }\n\n      const request: DataExportRequest = {\n        userId,\n        requestDate: new Date(),\n        status: 'pending',\n      };\n\n      const { data, error } = await supabase\n        .from('data_export_requests')\n        .insert({\n          user_id: userId,\n          request_date: request.requestDate.toISOString(),\n          status: request.status,\n        })\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      // Start export process asynchronously\n      this.processDataExport(userId, data.id);\n\n      // Log privacy event\n      await this.logPrivacyEvent(userId, 'data_export_requested', {\n        requestId: data.id,\n        timestamp: new Date().toISOString(),\n      });\n\n      return request;\n    } catch (error) {\n      console.error('Failed to request data export:', error);\n      throw new Error('Failed to request data export');\n    }\n  }\n\n  /**\n   * Request data deletion (GDPR Article 17)\n   */\n  async requestDataDeletion(\n    userId: string, \n    deletionType: 'partial' | 'complete' = 'complete',\n    retainedData: string[] = []\n  ): Promise<DataDeletionRequest> {\n    try {\n      // Check for existing pending requests\n      const { data: existingRequest } = await supabase\n        .from('data_deletion_requests')\n        .select('*')\n        .eq('user_id', userId)\n        .in('status', ['pending', 'processing'])\n        .single();\n\n      if (existingRequest) {\n        throw new Error('Data deletion request already pending');\n      }\n\n      // Schedule deletion for 30 days from now (grace period)\n      const scheduledDate = new Date();\n      scheduledDate.setDate(scheduledDate.getDate() + 30);\n\n      const request: DataDeletionRequest = {\n        userId,\n        requestDate: new Date(),\n        scheduledDate,\n        status: 'pending',\n        deletionType,\n        retainedData: deletionType === 'partial' ? retainedData : undefined,\n      };\n\n      const { data, error } = await supabase\n        .from('data_deletion_requests')\n        .insert({\n          user_id: userId,\n          request_date: request.requestDate.toISOString(),\n          scheduled_date: request.scheduledDate.toISOString(),\n          status: request.status,\n          deletion_type: request.deletionType,\n          retained_data: request.retainedData,\n        })\n        .select()\n        .single();\n\n      if (error) throw error;\n\n      // Log privacy event\n      await this.logPrivacyEvent(userId, 'data_deletion_requested', {\n        requestId: data.id,\n        deletionType,\n        scheduledDate: scheduledDate.toISOString(),\n        timestamp: new Date().toISOString(),\n      });\n\n      return request;\n    } catch (error) {\n      console.error('Failed to request data deletion:', error);\n      throw new Error('Failed to request data deletion');\n    }\n  }\n\n  /**\n   * Cancel data deletion request\n   */\n  async cancelDataDeletion(userId: string): Promise<void> {\n    try {\n      const { error } = await supabase\n        .from('data_deletion_requests')\n        .update({\n          status: 'cancelled',\n          updated_at: new Date().toISOString(),\n        })\n        .eq('user_id', userId)\n        .in('status', ['pending', 'processing']);\n\n      if (error) throw error;\n\n      // Log privacy event\n      await this.logPrivacyEvent(userId, 'data_deletion_cancelled', {\n        timestamp: new Date().toISOString(),\n      });\n    } catch (error) {\n      console.error('Failed to cancel data deletion:', error);\n      throw new Error('Failed to cancel data deletion');\n    }\n  }\n\n  /**\n   * Record user consent\n   */\n  async recordConsent(\n    userId: string,\n    consentType: string,\n    granted: boolean,\n    version: string,\n    metadata?: any\n  ): Promise<void> {\n    try {\n      const consent: ConsentRecord = {\n        userId,\n        consentType,\n        granted,\n        timestamp: new Date(),\n        version,\n        ipAddress: metadata?.ipAddress,\n        userAgent: metadata?.userAgent,\n      };\n\n      const { error } = await supabase\n        .from('consent_records')\n        .insert({\n          user_id: consent.userId,\n          consent_type: consent.consentType,\n          granted: consent.granted,\n          timestamp: consent.timestamp.toISOString(),\n          version: consent.version,\n          ip_address: consent.ipAddress,\n          user_agent: consent.userAgent,\n          metadata: metadata,\n        });\n\n      if (error) throw error;\n\n      // Log privacy event\n      await this.logPrivacyEvent(userId, 'consent_recorded', {\n        consentType,\n        granted,\n        version,\n        timestamp: new Date().toISOString(),\n      });\n    } catch (error) {\n      console.error('Failed to record consent:', error);\n      throw new Error('Failed to record consent');\n    }\n  }\n\n  /**\n   * Get user's consent history\n   */\n  async getConsentHistory(userId: string): Promise<ConsentRecord[]> {\n    try {\n      const { data, error } = await supabase\n        .from('consent_records')\n        .select('*')\n        .eq('user_id', userId)\n        .order('timestamp', { ascending: false });\n\n      if (error) throw error;\n\n      return data.map(record => ({\n        userId: record.user_id,\n        consentType: record.consent_type,\n        granted: record.granted,\n        timestamp: new Date(record.timestamp),\n        version: record.version,\n        ipAddress: record.ip_address,\n        userAgent: record.user_agent,\n      }));\n    } catch (error) {\n      console.error('Failed to get consent history:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Anonymize user data\n   */\n  async anonymizeUserData(userId: string): Promise<void> {\n    try {\n      // Generate anonymous ID\n      const anonymousId = `anon_${encryptionService.generateKey(16)}`;\n\n      // Update user record with anonymized data\n      const { error: userError } = await supabase\n        .from('users')\n        .update({\n          email: `${anonymousId}@anonymized.local`,\n          full_name: 'Anonymous User',\n          phone: null,\n          birth_date: null,\n          location: null,\n          bio: null,\n          avatar_url: null,\n          anonymized: true,\n          anonymized_at: new Date().toISOString(),\n        })\n        .eq('id', userId);\n\n      if (userError) throw userError;\n\n      // Anonymize training sessions\n      const { error: sessionsError } = await supabase\n        .from('training_sessions')\n        .update({\n          title: 'Anonymous Training Session',\n          notes: null,\n          video_url: null, // Remove video references\n        })\n        .eq('user_id', userId);\n\n      if (sessionsError) throw sessionsError;\n\n      // Log privacy event\n      await this.logPrivacyEvent(userId, 'data_anonymized', {\n        anonymousId,\n        timestamp: new Date().toISOString(),\n      });\n    } catch (error) {\n      console.error('Failed to anonymize user data:', error);\n      throw new Error('Failed to anonymize user data');\n    }\n  }\n\n  /**\n   * Process data export (background task)\n   */\n  private async processDataExport(userId: string, requestId: string): Promise<void> {\n    try {\n      // Update status to processing\n      await supabase\n        .from('data_export_requests')\n        .update({ status: 'processing' })\n        .eq('id', requestId);\n\n      // Collect all user data\n      const userData = await this.collectUserData(userId);\n\n      // Encrypt the export data\n      const encryptedData = encryptionService.encrypt(JSON.stringify(userData));\n\n      // Generate download URL (in real implementation, upload to secure storage)\n      const downloadUrl = await this.generateSecureDownloadUrl(encryptedData);\n\n      // Set expiration date (7 days from now)\n      const expiresAt = new Date();\n      expiresAt.setDate(expiresAt.getDate() + 7);\n\n      // Update request with download URL\n      await supabase\n        .from('data_export_requests')\n        .update({\n          status: 'completed',\n          download_url: downloadUrl,\n          expires_at: expiresAt.toISOString(),\n        })\n        .eq('id', requestId);\n\n      // Send notification to user (implement notification service)\n      // await notificationService.sendDataExportReady(userId, downloadUrl);\n\n    } catch (error) {\n      console.error('Data export processing failed:', error);\n      \n      // Update status to failed\n      await supabase\n        .from('data_export_requests')\n        .update({ status: 'failed' })\n        .eq('id', requestId);\n    }\n  }\n\n  /**\n   * Collect all user data for export\n   */\n  private async collectUserData(userId: string): Promise<any> {\n    try {\n      const [\n        user,\n        skillStats,\n        trainingSessions,\n        matchResults,\n        achievements,\n        aiTips,\n        privacySettings,\n        consentHistory,\n      ] = await Promise.all([\n        supabase.from('users').select('*').eq('id', userId).single(),\n        supabase.from('skill_stats').select('*').eq('user_id', userId),\n        supabase.from('training_sessions').select('*').eq('user_id', userId),\n        supabase.from('match_results').select('*').eq('user_id', userId),\n        supabase.from('achievements').select('*').eq('user_id', userId),\n        supabase.from('ai_tips').select('*').eq('user_id', userId),\n        this.getPrivacySettings(userId),\n        this.getConsentHistory(userId),\n      ]);\n\n      return {\n        exportDate: new Date().toISOString(),\n        user: user.data,\n        skillStats: skillStats.data,\n        trainingSessions: trainingSessions.data,\n        matchResults: matchResults.data,\n        achievements: achievements.data,\n        aiTips: aiTips.data,\n        privacySettings,\n        consentHistory,\n      };\n    } catch (error) {\n      console.error('Failed to collect user data:', error);\n      throw new Error('Failed to collect user data');\n    }\n  }\n\n  /**\n   * Generate secure download URL\n   */\n  private async generateSecureDownloadUrl(encryptedData: any): Promise<string> {\n    // In a real implementation, this would upload to secure cloud storage\n    // and return a signed URL with expiration\n    const dataId = encryptionService.generateKey(16);\n    return `https://secure-exports.acemind.app/download/${dataId}`;\n  }\n\n  /**\n   * Create default privacy settings\n   */\n  private async createDefaultPrivacySettings(userId: string): Promise<PrivacySettings> {\n    try {\n      const { error } = await supabase\n        .from('privacy_settings')\n        .insert({\n          user_id: userId,\n          ...this.convertSettingsToDb(this.defaultPrivacySettings),\n          created_at: new Date().toISOString(),\n        });\n\n      if (error) throw error;\n\n      return this.defaultPrivacySettings;\n    } catch (error) {\n      console.error('Failed to create default privacy settings:', error);\n      return this.defaultPrivacySettings;\n    }\n  }\n\n  /**\n   * Convert privacy settings to database format\n   */\n  private convertSettingsToDb(settings: PrivacySettings): any {\n    return {\n      data_collection: settings.dataCollection,\n      analytics: settings.analytics,\n      marketing: settings.marketing,\n      social_features: settings.socialFeatures,\n      location_tracking: settings.locationTracking,\n      video_analysis: settings.videoAnalysis,\n      ai_coaching: settings.aiCoaching,\n      data_sharing: settings.dataSharing,\n      notifications: settings.notifications,\n      profile_visibility: settings.profileVisibility,\n      activity_visibility: settings.activityVisibility,\n      data_retention_days: settings.dataRetentionDays,\n    };\n  }\n\n  /**\n   * Log privacy-related events\n   */\n  private async logPrivacyEvent(\n    userId: string, \n    eventType: string, \n    metadata: any\n  ): Promise<void> {\n    try {\n      await supabase\n        .from('privacy_events')\n        .insert({\n          user_id: userId,\n          event_type: eventType,\n          metadata,\n          timestamp: new Date().toISOString(),\n        });\n    } catch (error) {\n      console.error('Failed to log privacy event:', error);\n      // Don't throw error for logging failures\n    }\n  }\n}\n\n// Create singleton instance\nexport const privacyService = new PrivacyService();\n\nexport default privacyService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ;AACjB,SAASC,iBAAiB;AAAuB,IAkD3CC,cAAc;EAAA,SAAAA,eAAA;IAAAC,eAAA,OAAAD,cAAA;IAAA,KACDE,sBAAsB,IAAAC,aAAA,GAAAC,CAAA,OAAoB;MACzDC,cAAc,EAAE,IAAI;MACpBC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,KAAK;MAChBC,cAAc,EAAE,IAAI;MACpBC,gBAAgB,EAAE,KAAK;MACvBC,aAAa,EAAE,IAAI;MACnBC,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,KAAK;MAClBC,aAAa,EAAE,IAAI;MACnBC,iBAAiB,EAAE,SAAS;MAC5BC,kBAAkB,EAAE,SAAS;MAC7BC,iBAAiB,EAAE;IACrB,CAAC;EAAA;EAAA,OAAAC,YAAA,CAAAjB,cAAA;IAAAkB,GAAA;IAAAC,KAAA;MAAA,IAAAC,mBAAA,GAAAC,iBAAA,CAKD,WAAyBC,MAAc,EAA4B;QAAAnB,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QACjE,IAAI;UACF,IAAAoB,IAAA,IAAArB,aAAA,GAAAC,CAAA,aAA8BN,QAAQ,CACnC2B,IAAI,CAAC,kBAAkB,CAAC,CACxBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,CACrBM,MAAM,CAAC,CAAC;YAJHC,IAAI,GAAAL,IAAA,CAAJK,IAAI;YAAEC,KAAK,GAAAN,IAAA,CAALM,KAAK;UAIP3B,aAAA,GAAAC,CAAA;UAEZ,IAAI,CAAAD,aAAA,GAAA4B,CAAA,UAAAD,KAAK,MAAA3B,aAAA,GAAA4B,CAAA,UAAID,KAAK,CAACE,IAAI,KAAK,UAAU,GAAE;YAAA7B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YACtC,MAAM0B,KAAK;UACb,CAAC;YAAA3B,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAED,IAAI,CAACyB,IAAI,EAAE;YAAA1B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAET,aAAa,IAAI,CAAC6B,4BAA4B,CAACX,MAAM,CAAC;UACxD,CAAC;YAAAnB,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAED,OAAO;YACLC,cAAc,EAAEwB,IAAI,CAACK,eAAe;YACpC5B,SAAS,EAAEuB,IAAI,CAACvB,SAAS;YACzBC,SAAS,EAAEsB,IAAI,CAACtB,SAAS;YACzBC,cAAc,EAAEqB,IAAI,CAACM,eAAe;YACpC1B,gBAAgB,EAAEoB,IAAI,CAACO,iBAAiB;YACxC1B,aAAa,EAAEmB,IAAI,CAACQ,cAAc;YAClC1B,UAAU,EAAEkB,IAAI,CAACS,WAAW;YAC5B1B,WAAW,EAAEiB,IAAI,CAACU,YAAY;YAC9B1B,aAAa,EAAEgB,IAAI,CAAChB,aAAa;YACjCC,iBAAiB,EAAEe,IAAI,CAACW,kBAAkB;YAC1CzB,kBAAkB,EAAEc,IAAI,CAACY,mBAAmB;YAC5CzB,iBAAiB,EAAEa,IAAI,CAACa;UAC1B,CAAC;QACH,CAAC,CAAC,OAAOZ,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UACxD,OAAO,IAAI,CAACF,sBAAsB;QACpC;MACF,CAAC;MAAA,SAnCK0C,kBAAkBA,CAAAC,EAAA;QAAA,OAAAzB,mBAAA,CAAA0B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBH,kBAAkB;IAAA;EAAA;IAAA1B,GAAA;IAAAC,KAAA;MAAA,IAAA6B,sBAAA,GAAA3B,iBAAA,CAwCxB,WACEC,MAAc,EACd2B,QAAkC,EACR;QAAA9C,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QAC1B,IAAI;UACF,IAAM8C,eAAe,IAAA/C,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACwC,kBAAkB,CAACtB,MAAM,CAAC;UAC7D,IAAM6B,eAAe,IAAAhD,aAAA,GAAAC,CAAA,QAAAgD,MAAA,CAAAC,MAAA,KAAQH,eAAe,EAAKD,QAAQ,EAAE;UAE3D,IAAAK,KAAA,IAAAnD,aAAA,GAAAC,CAAA,cAA8BN,QAAQ,CACnC2B,IAAI,CAAC,kBAAkB,CAAC,CACxB8B,MAAM,CAAC;cACNC,OAAO,EAAElC,MAAM;cACfY,eAAe,EAAEiB,eAAe,CAAC9C,cAAc;cAC/CC,SAAS,EAAE6C,eAAe,CAAC7C,SAAS;cACpCC,SAAS,EAAE4C,eAAe,CAAC5C,SAAS;cACpC4B,eAAe,EAAEgB,eAAe,CAAC3C,cAAc;cAC/C4B,iBAAiB,EAAEe,eAAe,CAAC1C,gBAAgB;cACnD4B,cAAc,EAAEc,eAAe,CAACzC,aAAa;cAC7C4B,WAAW,EAAEa,eAAe,CAACxC,UAAU;cACvC4B,YAAY,EAAEY,eAAe,CAACvC,WAAW;cACzCC,aAAa,EAAEsC,eAAe,CAACtC,aAAa;cAC5C2B,kBAAkB,EAAEW,eAAe,CAACrC,iBAAiB;cACrD2B,mBAAmB,EAAEU,eAAe,CAACpC,kBAAkB;cACvD2B,mBAAmB,EAAES,eAAe,CAACnC,iBAAiB;cACtDyC,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACrC,CAAC,CAAC,CACDjC,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;YAnBHC,IAAI,GAAAyB,KAAA,CAAJzB,IAAI;YAAEC,KAAK,GAAAwB,KAAA,CAALxB,KAAK;UAmBP3B,aAAA,GAAAC,CAAA;UAEZ,IAAI0B,KAAK,EAAE;YAAA3B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAM0B,KAAK;UAAA,CAAC;YAAA3B,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAGvB,MAAM,IAAI,CAACwD,eAAe,CAACtC,MAAM,EAAE,kBAAkB,EAAE;YACrDuC,OAAO,EAAEZ,QAAQ;YACjBa,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC,CAAC;UAACxD,aAAA,GAAAC,CAAA;UAEH,OAAO+C,eAAe;QACxB,CAAC,CAAC,OAAOrB,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UAC3D,MAAM,IAAI2D,KAAK,CAAC,mCAAmC,CAAC;QACtD;MACF,CAAC;MAAA,SA1CKC,qBAAqBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAlB,sBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBiB,qBAAqB;IAAA;EAAA;IAAA9C,GAAA;IAAAC,KAAA;MAAA,IAAAgD,kBAAA,GAAA9C,iBAAA,CA+C3B,WAAwBC,MAAc,EAA8B;QAAAnB,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QAClE,IAAI;UAEF,IAAAgE,KAAA,IAAAjE,aAAA,GAAAC,CAAA,cAAwCN,QAAQ,CAC7C2B,IAAI,CAAC,sBAAsB,CAAC,CAC5BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,CACrBK,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC,CACvBC,MAAM,CAAC,CAAC;YALGyC,eAAe,GAAAD,KAAA,CAArBvC,IAAI;UAKA1B,aAAA,GAAAC,CAAA;UAEZ,IAAIiE,eAAe,EAAE;YAAAlE,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YACnB,MAAM,IAAI2D,KAAK,CAAC,qCAAqC,CAAC;UACxD,CAAC;YAAA5D,aAAA,GAAA4B,CAAA;UAAA;UAED,IAAMuC,OAA0B,IAAAnE,aAAA,GAAAC,CAAA,QAAG;YACjCkB,MAAM,EAANA,MAAM;YACNiD,WAAW,EAAE,IAAIb,IAAI,CAAC,CAAC;YACvBc,MAAM,EAAE;UACV,CAAC;UAED,IAAAC,KAAA,IAAAtE,aAAA,GAAAC,CAAA,cAA8BN,QAAQ,CACnC2B,IAAI,CAAC,sBAAsB,CAAC,CAC5BiD,MAAM,CAAC;cACNlB,OAAO,EAAElC,MAAM;cACfqD,YAAY,EAAEL,OAAO,CAACC,WAAW,CAACZ,WAAW,CAAC,CAAC;cAC/Ca,MAAM,EAAEF,OAAO,CAACE;YAClB,CAAC,CAAC,CACD9C,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;YARHC,IAAI,GAAA4C,KAAA,CAAJ5C,IAAI;YAAEC,KAAK,GAAA2C,KAAA,CAAL3C,KAAK;UAQP3B,aAAA,GAAAC,CAAA;UAEZ,IAAI0B,KAAK,EAAE;YAAA3B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAM0B,KAAK;UAAA,CAAC;YAAA3B,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAGvB,IAAI,CAACwE,iBAAiB,CAACtD,MAAM,EAAEO,IAAI,CAACgD,EAAE,CAAC;UAAC1E,aAAA,GAAAC,CAAA;UAGxC,MAAM,IAAI,CAACwD,eAAe,CAACtC,MAAM,EAAE,uBAAuB,EAAE;YAC1DwD,SAAS,EAAEjD,IAAI,CAACgD,EAAE;YAClBf,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC,CAAC;UAACxD,aAAA,GAAAC,CAAA;UAEH,OAAOkE,OAAO;QAChB,CAAC,CAAC,OAAOxC,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UACvD,MAAM,IAAI2D,KAAK,CAAC,+BAA+B,CAAC;QAClD;MACF,CAAC;MAAA,SA9CKgB,iBAAiBA,CAAAC,GAAA;QAAA,OAAAb,kBAAA,CAAArB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBgC,iBAAiB;IAAA;EAAA;IAAA7D,GAAA;IAAAC,KAAA;MAAA,IAAA8D,oBAAA,GAAA5D,iBAAA,CAmDvB,WACEC,MAAc,EAGgB;QAAA,IAF9B4D,YAAoC,GAAAnC,SAAA,CAAAoC,MAAA,QAAApC,SAAA,QAAAqC,SAAA,GAAArC,SAAA,OAAA5C,aAAA,GAAA4B,CAAA,UAAG,UAAU;QAAA,IACjDsD,YAAsB,GAAAtC,SAAA,CAAAoC,MAAA,QAAApC,SAAA,QAAAqC,SAAA,GAAArC,SAAA,OAAA5C,aAAA,GAAA4B,CAAA,UAAG,EAAE;QAAA5B,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QAE3B,IAAI;UAEF,IAAAkF,KAAA,IAAAnF,aAAA,GAAAC,CAAA,cAAwCN,QAAQ,CAC7C2B,IAAI,CAAC,wBAAwB,CAAC,CAC9BC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,CACrBiE,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC,CACvC3D,MAAM,CAAC,CAAC;YALGyC,eAAe,GAAAiB,KAAA,CAArBzD,IAAI;UAKA1B,aAAA,GAAAC,CAAA;UAEZ,IAAIiE,eAAe,EAAE;YAAAlE,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YACnB,MAAM,IAAI2D,KAAK,CAAC,uCAAuC,CAAC;UAC1D,CAAC;YAAA5D,aAAA,GAAA4B,CAAA;UAAA;UAGD,IAAMyD,aAAa,IAAArF,aAAA,GAAAC,CAAA,QAAG,IAAIsD,IAAI,CAAC,CAAC;UAACvD,aAAA,GAAAC,CAAA;UACjCoF,aAAa,CAACC,OAAO,CAACD,aAAa,CAACE,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;UAEnD,IAAMpB,OAA4B,IAAAnE,aAAA,GAAAC,CAAA,QAAG;YACnCkB,MAAM,EAANA,MAAM;YACNiD,WAAW,EAAE,IAAIb,IAAI,CAAC,CAAC;YACvB8B,aAAa,EAAbA,aAAa;YACbhB,MAAM,EAAE,SAAS;YACjBU,YAAY,EAAZA,YAAY;YACZG,YAAY,EAAEH,YAAY,KAAK,SAAS,IAAA/E,aAAA,GAAA4B,CAAA,UAAGsD,YAAY,KAAAlF,aAAA,GAAA4B,CAAA,UAAGqD,SAAS;UACrE,CAAC;UAED,IAAAO,KAAA,IAAAxF,aAAA,GAAAC,CAAA,cAA8BN,QAAQ,CACnC2B,IAAI,CAAC,wBAAwB,CAAC,CAC9BiD,MAAM,CAAC;cACNlB,OAAO,EAAElC,MAAM;cACfqD,YAAY,EAAEL,OAAO,CAACC,WAAW,CAACZ,WAAW,CAAC,CAAC;cAC/CiC,cAAc,EAAEtB,OAAO,CAACkB,aAAa,CAAC7B,WAAW,CAAC,CAAC;cACnDa,MAAM,EAAEF,OAAO,CAACE,MAAM;cACtBqB,aAAa,EAAEvB,OAAO,CAACY,YAAY;cACnCY,aAAa,EAAExB,OAAO,CAACe;YACzB,CAAC,CAAC,CACD3D,MAAM,CAAC,CAAC,CACRE,MAAM,CAAC,CAAC;YAXHC,IAAI,GAAA8D,KAAA,CAAJ9D,IAAI;YAAEC,KAAK,GAAA6D,KAAA,CAAL7D,KAAK;UAWP3B,aAAA,GAAAC,CAAA;UAEZ,IAAI0B,KAAK,EAAE;YAAA3B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAM0B,KAAK;UAAA,CAAC;YAAA3B,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAGvB,MAAM,IAAI,CAACwD,eAAe,CAACtC,MAAM,EAAE,yBAAyB,EAAE;YAC5DwD,SAAS,EAAEjD,IAAI,CAACgD,EAAE;YAClBK,YAAY,EAAZA,YAAY;YACZM,aAAa,EAAEA,aAAa,CAAC7B,WAAW,CAAC,CAAC;YAC1CG,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC,CAAC;UAACxD,aAAA,GAAAC,CAAA;UAEH,OAAOkE,OAAO;QAChB,CAAC,CAAC,OAAOxC,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UACzD,MAAM,IAAI2D,KAAK,CAAC,iCAAiC,CAAC;QACpD;MACF,CAAC;MAAA,SA3DKgC,mBAAmBA,CAAAC,GAAA;QAAA,OAAAf,oBAAA,CAAAnC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBgD,mBAAmB;IAAA;EAAA;IAAA7E,GAAA;IAAAC,KAAA;MAAA,IAAA8E,mBAAA,GAAA5E,iBAAA,CAgEzB,WAAyBC,MAAc,EAAiB;QAAAnB,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QACtD,IAAI;UACF,IAAA8F,KAAA,IAAA/F,aAAA,GAAAC,CAAA,cAAwBN,QAAQ,CAC7B2B,IAAI,CAAC,wBAAwB,CAAC,CAC9B0E,MAAM,CAAC;cACN3B,MAAM,EAAE,WAAW;cACnBf,UAAU,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACrC,CAAC,CAAC,CACDhC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,CACrBiE,EAAE,CAAC,QAAQ,EAAE,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAPlCzD,KAAK,GAAAoE,KAAA,CAALpE,KAAK;UAO8B3B,aAAA,GAAAC,CAAA;UAE3C,IAAI0B,KAAK,EAAE;YAAA3B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAM0B,KAAK;UAAA,CAAC;YAAA3B,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAGvB,MAAM,IAAI,CAACwD,eAAe,CAACtC,MAAM,EAAE,yBAAyB,EAAE;YAC5DwC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC,CAAC;QACJ,CAAC,CAAC,OAAO7B,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UACxD,MAAM,IAAI2D,KAAK,CAAC,gCAAgC,CAAC;QACnD;MACF,CAAC;MAAA,SArBKqC,kBAAkBA,CAAAC,GAAA;QAAA,OAAAJ,mBAAA,CAAAnD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBqD,kBAAkB;IAAA;EAAA;IAAAlF,GAAA;IAAAC,KAAA;MAAA,IAAAmF,cAAA,GAAAjF,iBAAA,CA0BxB,WACEC,MAAc,EACdiF,WAAmB,EACnBC,OAAgB,EAChBC,OAAe,EACfC,QAAc,EACC;QAAAvG,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QACf,IAAI;UACF,IAAMuG,OAAsB,IAAAxG,aAAA,GAAAC,CAAA,QAAG;YAC7BkB,MAAM,EAANA,MAAM;YACNiF,WAAW,EAAXA,WAAW;YACXC,OAAO,EAAPA,OAAO;YACP1C,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC;YACrB+C,OAAO,EAAPA,OAAO;YACPG,SAAS,EAAEF,QAAQ,oBAARA,QAAQ,CAAEE,SAAS;YAC9BC,SAAS,EAAEH,QAAQ,oBAARA,QAAQ,CAAEG;UACvB,CAAC;UAED,IAAAC,KAAA,IAAA3G,aAAA,GAAAC,CAAA,cAAwBN,QAAQ,CAC7B2B,IAAI,CAAC,iBAAiB,CAAC,CACvBiD,MAAM,CAAC;cACNlB,OAAO,EAAEmD,OAAO,CAACrF,MAAM;cACvByF,YAAY,EAAEJ,OAAO,CAACJ,WAAW;cACjCC,OAAO,EAAEG,OAAO,CAACH,OAAO;cACxB1C,SAAS,EAAE6C,OAAO,CAAC7C,SAAS,CAACH,WAAW,CAAC,CAAC;cAC1C8C,OAAO,EAAEE,OAAO,CAACF,OAAO;cACxBO,UAAU,EAAEL,OAAO,CAACC,SAAS;cAC7BK,UAAU,EAAEN,OAAO,CAACE,SAAS;cAC7BH,QAAQ,EAAEA;YACZ,CAAC,CAAC;YAXI5E,KAAK,GAAAgF,KAAA,CAALhF,KAAK;UAWR3B,aAAA,GAAAC,CAAA;UAEL,IAAI0B,KAAK,EAAE;YAAA3B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAM0B,KAAK;UAAA,CAAC;YAAA3B,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAGvB,MAAM,IAAI,CAACwD,eAAe,CAACtC,MAAM,EAAE,kBAAkB,EAAE;YACrDiF,WAAW,EAAXA,WAAW;YACXC,OAAO,EAAPA,OAAO;YACPC,OAAO,EAAPA,OAAO;YACP3C,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC,CAAC;QACJ,CAAC,CAAC,OAAO7B,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UAClD,MAAM,IAAI2D,KAAK,CAAC,0BAA0B,CAAC;QAC7C;MACF,CAAC;MAAA,SA5CKmD,aAAaA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAjB,cAAA,CAAAxD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbmE,aAAa;IAAA;EAAA;IAAAhG,GAAA;IAAAC,KAAA;MAAA,IAAAqG,kBAAA,GAAAnG,iBAAA,CAiDnB,WAAwBC,MAAc,EAA4B;QAAAnB,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QAChE,IAAI;UACF,IAAAqH,KAAA,IAAAtH,aAAA,GAAAC,CAAA,cAA8BN,QAAQ,CACnC2B,IAAI,CAAC,iBAAiB,CAAC,CACvBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,CACrBoG,KAAK,CAAC,WAAW,EAAE;cAAEC,SAAS,EAAE;YAAM,CAAC,CAAC;YAJnC9F,IAAI,GAAA4F,KAAA,CAAJ5F,IAAI;YAAEC,KAAK,GAAA2F,KAAA,CAAL3F,KAAK;UAIyB3B,aAAA,GAAAC,CAAA;UAE5C,IAAI0B,KAAK,EAAE;YAAA3B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAM0B,KAAK;UAAA,CAAC;YAAA3B,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAEvB,OAAOyB,IAAI,CAAC+F,GAAG,CAAC,UAAAC,MAAM,EAAK;YAAA1H,aAAA,GAAAoB,CAAA;YAAApB,aAAA,GAAAC,CAAA;YAAA;cACzBkB,MAAM,EAAEuG,MAAM,CAACrE,OAAO;cACtB+C,WAAW,EAAEsB,MAAM,CAACd,YAAY;cAChCP,OAAO,EAAEqB,MAAM,CAACrB,OAAO;cACvB1C,SAAS,EAAE,IAAIJ,IAAI,CAACmE,MAAM,CAAC/D,SAAS,CAAC;cACrC2C,OAAO,EAAEoB,MAAM,CAACpB,OAAO;cACvBG,SAAS,EAAEiB,MAAM,CAACb,UAAU;cAC5BH,SAAS,EAAEgB,MAAM,CAACZ;YACpB,CAAC;UAAD,CAAE,CAAC;QACL,CAAC,CAAC,OAAOnF,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UACvD,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SAvBK0H,iBAAiBA,CAAAC,IAAA;QAAA,OAAAP,kBAAA,CAAA1E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjB+E,iBAAiB;IAAA;EAAA;IAAA5G,GAAA;IAAAC,KAAA;MAAA,IAAA6G,kBAAA,GAAA3G,iBAAA,CA4BvB,WAAwBC,MAAc,EAAiB;QAAAnB,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QACrD,IAAI;UAEF,IAAM6H,WAAW,IAAA9H,aAAA,GAAAC,CAAA,QAAG,QAAQL,iBAAiB,CAACmI,WAAW,CAAC,EAAE,CAAC,EAAE;UAG/D,IAAAC,KAAA,IAAAhI,aAAA,GAAAC,CAAA,cAAmCN,QAAQ,CACxC2B,IAAI,CAAC,OAAO,CAAC,CACb0E,MAAM,CAAC;cACNiC,KAAK,EAAE,GAAGH,WAAW,mBAAmB;cACxCI,SAAS,EAAE,gBAAgB;cAC3BC,KAAK,EAAE,IAAI;cACXC,UAAU,EAAE,IAAI;cAChBC,QAAQ,EAAE,IAAI;cACdC,GAAG,EAAE,IAAI;cACTC,UAAU,EAAE,IAAI;cAChBC,UAAU,EAAE,IAAI;cAChBC,aAAa,EAAE,IAAIlF,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YACxC,CAAC,CAAC,CACDhC,EAAE,CAAC,IAAI,EAAEL,MAAM,CAAC;YAbJuH,SAAS,GAAAV,KAAA,CAAhBrG,KAAK;UAaO3B,aAAA,GAAAC,CAAA;UAEpB,IAAIyI,SAAS,EAAE;YAAA1I,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAMyI,SAAS;UAAA,CAAC;YAAA1I,aAAA,GAAA4B,CAAA;UAAA;UAG/B,IAAA+G,KAAA,IAAA3I,aAAA,GAAAC,CAAA,cAAuCN,QAAQ,CAC5C2B,IAAI,CAAC,mBAAmB,CAAC,CACzB0E,MAAM,CAAC;cACN4C,KAAK,EAAE,4BAA4B;cACnCC,KAAK,EAAE,IAAI;cACXC,SAAS,EAAE;YACb,CAAC,CAAC,CACDtH,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC;YAPT4H,aAAa,GAAAJ,KAAA,CAApBhH,KAAK;UAOY3B,aAAA,GAAAC,CAAA;UAEzB,IAAI8I,aAAa,EAAE;YAAA/I,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAM8I,aAAa;UAAA,CAAC;YAAA/I,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAGvC,MAAM,IAAI,CAACwD,eAAe,CAACtC,MAAM,EAAE,iBAAiB,EAAE;YACpD2G,WAAW,EAAXA,WAAW;YACXnE,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC,CAAC;QACJ,CAAC,CAAC,OAAO7B,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UACvD,MAAM,IAAI2D,KAAK,CAAC,+BAA+B,CAAC;QAClD;MACF,CAAC;MAAA,SA5CKoF,iBAAiBA,CAAAC,IAAA;QAAA,OAAApB,kBAAA,CAAAlF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBoG,iBAAiB;IAAA;EAAA;IAAAjI,GAAA;IAAAC,KAAA;MAAA,IAAAkI,kBAAA,GAAAhI,iBAAA,CAiDvB,WAAgCC,MAAc,EAAEwD,SAAiB,EAAiB;QAAA3E,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QAChF,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAEF,MAAMN,QAAQ,CACX2B,IAAI,CAAC,sBAAsB,CAAC,CAC5B0E,MAAM,CAAC;YAAE3B,MAAM,EAAE;UAAa,CAAC,CAAC,CAChC7C,EAAE,CAAC,IAAI,EAAEmD,SAAS,CAAC;UAGtB,IAAMwE,QAAQ,IAAAnJ,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACmJ,eAAe,CAACjI,MAAM,CAAC;UAGnD,IAAMkI,aAAa,IAAArJ,aAAA,GAAAC,CAAA,QAAGL,iBAAiB,CAAC0J,OAAO,CAACC,IAAI,CAACC,SAAS,CAACL,QAAQ,CAAC,CAAC;UAGzE,IAAMM,WAAW,IAAAzJ,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACyJ,yBAAyB,CAACL,aAAa,CAAC;UAGvE,IAAMM,SAAS,IAAA3J,aAAA,GAAAC,CAAA,QAAG,IAAIsD,IAAI,CAAC,CAAC;UAACvD,aAAA,GAAAC,CAAA;UAC7B0J,SAAS,CAACrE,OAAO,CAACqE,SAAS,CAACpE,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;UAACvF,aAAA,GAAAC,CAAA;UAG3C,MAAMN,QAAQ,CACX2B,IAAI,CAAC,sBAAsB,CAAC,CAC5B0E,MAAM,CAAC;YACN3B,MAAM,EAAE,WAAW;YACnBuF,YAAY,EAAEH,WAAW;YACzBI,UAAU,EAAEF,SAAS,CAACnG,WAAW,CAAC;UACpC,CAAC,CAAC,CACDhC,EAAE,CAAC,IAAI,EAAEmD,SAAS,CAAC;QAKxB,CAAC,CAAC,OAAOhD,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UAGvD,MAAMN,QAAQ,CACX2B,IAAI,CAAC,sBAAsB,CAAC,CAC5B0E,MAAM,CAAC;YAAE3B,MAAM,EAAE;UAAS,CAAC,CAAC,CAC5B7C,EAAE,CAAC,IAAI,EAAEmD,SAAS,CAAC;QACxB;MACF,CAAC;MAAA,SA3CaF,iBAAiBA,CAAAqF,IAAA,EAAAC,IAAA;QAAA,OAAAb,kBAAA,CAAAvG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjB6B,iBAAiB;IAAA;EAAA;IAAA1D,GAAA;IAAAC,KAAA;MAAA,IAAAgJ,gBAAA,GAAA9I,iBAAA,CAgD/B,WAA8BC,MAAc,EAAgB;QAAAnB,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QAC1D,IAAI;UACF,IAAAgK,MAAA,IAAAjK,aAAA,GAAAC,CAAA,cASUiK,OAAO,CAACC,GAAG,CAAC,CACpBxK,QAAQ,CAAC2B,IAAI,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAACC,EAAE,CAAC,IAAI,EAAEL,MAAM,CAAC,CAACM,MAAM,CAAC,CAAC,EAC5D9B,QAAQ,CAAC2B,IAAI,CAAC,aAAa,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAACC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,EAC9DxB,QAAQ,CAAC2B,IAAI,CAAC,mBAAmB,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAACC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,EACpExB,QAAQ,CAAC2B,IAAI,CAAC,eAAe,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAACC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,EAChExB,QAAQ,CAAC2B,IAAI,CAAC,cAAc,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAACC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,EAC/DxB,QAAQ,CAAC2B,IAAI,CAAC,SAAS,CAAC,CAACC,MAAM,CAAC,GAAG,CAAC,CAACC,EAAE,CAAC,SAAS,EAAEL,MAAM,CAAC,EAC1D,IAAI,CAACsB,kBAAkB,CAACtB,MAAM,CAAC,EAC/B,IAAI,CAACwG,iBAAiB,CAACxG,MAAM,CAAC,CAC/B,CAAC;YAAAiJ,MAAA,GAAAC,cAAA,CAAAJ,MAAA;YAjBAK,IAAI,GAAAF,MAAA;YACJG,UAAU,GAAAH,MAAA;YACVI,gBAAgB,GAAAJ,MAAA;YAChBK,YAAY,GAAAL,MAAA;YACZM,YAAY,GAAAN,MAAA;YACZO,MAAM,GAAAP,MAAA;YACNQ,eAAe,GAAAR,MAAA;YACfS,cAAc,GAAAT,MAAA;UAUbpK,aAAA,GAAAC,CAAA;UAEH,OAAO;YACL6K,UAAU,EAAE,IAAIvH,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;YACpC8G,IAAI,EAAEA,IAAI,CAAC5I,IAAI;YACf6I,UAAU,EAAEA,UAAU,CAAC7I,IAAI;YAC3B8I,gBAAgB,EAAEA,gBAAgB,CAAC9I,IAAI;YACvC+I,YAAY,EAAEA,YAAY,CAAC/I,IAAI;YAC/BgJ,YAAY,EAAEA,YAAY,CAAChJ,IAAI;YAC/BiJ,MAAM,EAAEA,MAAM,CAACjJ,IAAI;YACnBkJ,eAAe,EAAfA,eAAe;YACfC,cAAc,EAAdA;UACF,CAAC;QACH,CAAC,CAAC,OAAOlJ,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UACrD,MAAM,IAAI2D,KAAK,CAAC,6BAA6B,CAAC;QAChD;MACF,CAAC;MAAA,SArCawF,eAAeA,CAAA2B,IAAA;QAAA,OAAAf,gBAAA,CAAArH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfwG,eAAe;IAAA;EAAA;IAAArI,GAAA;IAAAC,KAAA;MAAA,IAAAgK,0BAAA,GAAA9J,iBAAA,CA0C7B,WAAwCmI,aAAkB,EAAmB;QAAArJ,aAAA,GAAAoB,CAAA;QAG3E,IAAM6J,MAAM,IAAAjL,aAAA,GAAAC,CAAA,QAAGL,iBAAiB,CAACmI,WAAW,CAAC,EAAE,CAAC;QAAC/H,aAAA,GAAAC,CAAA;QACjD,OAAO,+CAA+CgL,MAAM,EAAE;MAChE,CAAC;MAAA,SALavB,yBAAyBA,CAAAwB,IAAA;QAAA,OAAAF,0BAAA,CAAArI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAzB8G,yBAAyB;IAAA;EAAA;IAAA3I,GAAA;IAAAC,KAAA;MAAA,IAAAmK,6BAAA,GAAAjK,iBAAA,CAUvC,WAA2CC,MAAc,EAA4B;QAAAnB,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QACnF,IAAI;UACF,IAAAmL,MAAA,IAAApL,aAAA,GAAAC,CAAA,cAAwBN,QAAQ,CAC7B2B,IAAI,CAAC,kBAAkB,CAAC,CACxBiD,MAAM,CAAAtB,MAAA,CAAAC,MAAA;cACLG,OAAO,EAAElC;YAAM,GACZ,IAAI,CAACkK,mBAAmB,CAAC,IAAI,CAACtL,sBAAsB,CAAC;cACxDuL,UAAU,EAAE,IAAI/H,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;YAAC,EACrC,CAAC;YANI7B,KAAK,GAAAyJ,MAAA,CAALzJ,KAAK;UAMR3B,aAAA,GAAAC,CAAA;UAEL,IAAI0B,KAAK,EAAE;YAAA3B,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,MAAM0B,KAAK;UAAA,CAAC;YAAA3B,aAAA,GAAA4B,CAAA;UAAA;UAAA5B,aAAA,GAAAC,CAAA;UAEvB,OAAO,IAAI,CAACF,sBAAsB;QACpC,CAAC,CAAC,OAAO4B,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;UAAC3B,aAAA,GAAAC,CAAA;UACnE,OAAO,IAAI,CAACF,sBAAsB;QACpC;MACF,CAAC;MAAA,SAjBa+B,4BAA4BA,CAAAyJ,IAAA;QAAA,OAAAJ,6BAAA,CAAAxI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA5Bd,4BAA4B;IAAA;EAAA;IAAAf,GAAA;IAAAC,KAAA,EAsB1C,SAAQqK,mBAAmBA,CAACvI,QAAyB,EAAO;MAAA9C,aAAA,GAAAoB,CAAA;MAAApB,aAAA,GAAAC,CAAA;MAC1D,OAAO;QACL8B,eAAe,EAAEe,QAAQ,CAAC5C,cAAc;QACxCC,SAAS,EAAE2C,QAAQ,CAAC3C,SAAS;QAC7BC,SAAS,EAAE0C,QAAQ,CAAC1C,SAAS;QAC7B4B,eAAe,EAAEc,QAAQ,CAACzC,cAAc;QACxC4B,iBAAiB,EAAEa,QAAQ,CAACxC,gBAAgB;QAC5C4B,cAAc,EAAEY,QAAQ,CAACvC,aAAa;QACtC4B,WAAW,EAAEW,QAAQ,CAACtC,UAAU;QAChC4B,YAAY,EAAEU,QAAQ,CAACrC,WAAW;QAClCC,aAAa,EAAEoC,QAAQ,CAACpC,aAAa;QACrC2B,kBAAkB,EAAES,QAAQ,CAACnC,iBAAiB;QAC9C2B,mBAAmB,EAAEQ,QAAQ,CAAClC,kBAAkB;QAChD2B,mBAAmB,EAAEO,QAAQ,CAACjC;MAChC,CAAC;IACH;EAAC;IAAAE,GAAA;IAAAC,KAAA;MAAA,IAAAwK,gBAAA,GAAAtK,iBAAA,CAKD,WACEC,MAAc,EACdsK,SAAiB,EACjBlF,QAAa,EACE;QAAAvG,aAAA,GAAAoB,CAAA;QAAApB,aAAA,GAAAC,CAAA;QACf,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACF,MAAMN,QAAQ,CACX2B,IAAI,CAAC,gBAAgB,CAAC,CACtBiD,MAAM,CAAC;YACNlB,OAAO,EAAElC,MAAM;YACfuK,UAAU,EAAED,SAAS;YACrBlF,QAAQ,EAARA,QAAQ;YACR5C,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UACpC,CAAC,CAAC;QACN,CAAC,CAAC,OAAO7B,KAAK,EAAE;UAAA3B,aAAA,GAAAC,CAAA;UACduC,OAAO,CAACb,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QAEtD;MACF,CAAC;MAAA,SAlBa8B,eAAeA,CAAAkI,IAAA,EAAAC,IAAA,EAAAC,IAAA;QAAA,OAAAL,gBAAA,CAAA7I,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfa,eAAe;IAAA;EAAA;AAAA;AAsB/B,OAAO,IAAMqI,cAAc,IAAA9L,aAAA,GAAAC,CAAA,SAAG,IAAIJ,cAAc,CAAC,CAAC;AAElD,eAAeiM,cAAc", "ignoreList": []}