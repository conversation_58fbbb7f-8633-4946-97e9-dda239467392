{"version": 3, "names": ["React", "Suspense", "lazy", "View", "Text", "ActivityIndicator", "performanceMonitor", "jsx", "_jsx", "jsxs", "_jsxs", "createLazyComponent", "importFunc", "componentName", "options", "arguments", "length", "undefined", "cov_wdxz0xwkb", "b", "f", "_ref", "s", "CustomFallback", "fallback", "CustomErrorBoundary", "errorBoundary", "_ref$preload", "preload", "_ref$timeout", "timeout", "LazyComponent", "_asyncToGenerator", "startTime", "Date", "now", "componentPromise", "timeoutPromise", "Promise", "_", "reject", "setTimeout", "Error", "component", "race", "loadTime", "trackComponentLoad", "console", "warn", "error", "trackComponentLoadError", "requestIdleCallback", "catch", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "style", "flex", "justifyContent", "alignItems", "padding", "children", "size", "color", "marginTop", "fontSize", "DefaultErrorBoundary", "_ref3", "retry", "fontWeight", "marginBottom", "textAlign", "message", "textDecorationLine", "onPress", "WrappedComponent", "props", "_ref4", "useState", "_ref5", "_slicedToArray", "setError", "_ref6", "_ref7", "retry<PERSON><PERSON>", "setRetryKey", "useCallback", "prev", "ErrorComponent", "Error<PERSON>ou<PERSON><PERSON>", "onError", "Object", "assign", "displayName", "preloadComponents", "components", "sortedComponents", "sort", "a", "priorityOrder", "high", "medium", "low", "priority", "for<PERSON>ach", "index", "delay", "bundleSplitting", "createFeatureBundle", "featureName", "lazyComponents", "entries", "_ref8", "_ref9", "createVendorChunk", "vendors", "map", "vendor", "name", "test", "RegExp", "chunks", "ComponentLoader", "_classCallCheck", "loadedComponents", "Set", "loadingComponents", "Map", "_createClass", "key", "value", "_loadComponent", "_this", "has", "default", "get", "loadPromise", "then", "module", "add", "delete", "set", "loadComponent", "_x", "_x2", "apply", "getLoadedComponents", "Array", "from", "getLoadingComponents", "keys", "getInstance", "instance"], "sources": ["lazyLoading.tsx"], "sourcesContent": ["/**\n * Lazy Loading Utilities for Bundle Splitting\n * \n * Provides optimized lazy loading with performance tracking,\n * error boundaries, and loading states.\n */\n\nimport React, { Suspense, lazy, ComponentType } from 'react';\nimport { View, Text, ActivityIndicator } from 'react-native';\nimport { performanceMonitor } from '@/utils/performance';\n\ninterface LazyLoadOptions {\n  fallback?: React.ComponentType;\n  errorBoundary?: React.ComponentType<{ error: Error; retry: () => void }>;\n  preload?: boolean;\n  timeout?: number;\n}\n\n/**\n * Enhanced lazy loading with performance tracking\n */\nexport function createLazyComponent<T extends ComponentType<any>>(\n  importFunc: () => Promise<{ default: T }>,\n  componentName: string,\n  options: LazyLoadOptions = {}\n): React.ComponentType<React.ComponentProps<T>> {\n  const {\n    fallback: CustomFallback,\n    errorBoundary: CustomErrorBoundary,\n    preload = false,\n    timeout = 10000\n  } = options;\n\n  // Create lazy component with performance tracking\n  const LazyComponent = lazy(async () => {\n    const startTime = Date.now();\n    \n    try {\n      // Add timeout to prevent hanging\n      const componentPromise = importFunc();\n      const timeoutPromise = new Promise<never>((_, reject) => {\n        setTimeout(() => reject(new Error(`Component ${componentName} load timeout`)), timeout);\n      });\n\n      const component = await Promise.race([componentPromise, timeoutPromise]);\n      const loadTime = Date.now() - startTime;\n\n      // Track performance\n      performanceMonitor.trackComponentLoad(componentName, loadTime);\n\n      if (loadTime > 2000) {\n        console.warn(`Slow component load: ${componentName} took ${loadTime}ms`);\n      }\n\n      return component;\n    } catch (error) {\n      performanceMonitor.trackComponentLoadError(componentName, error as Error);\n      throw error;\n    }\n  });\n\n  // Preload if requested\n  if (preload) {\n    // Preload during idle time\n    if (typeof requestIdleCallback !== 'undefined') {\n      requestIdleCallback(() => {\n        importFunc().catch(() => {\n          // Ignore preload errors\n        });\n      });\n    }\n  }\n\n  // Default loading fallback\n  const DefaultFallback = () => (\n    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>\n      <ActivityIndicator size=\"large\" color=\"#007AFF\" />\n      <Text style={{ marginTop: 10, fontSize: 16, color: '#666' }}>\n        Loading {componentName}...\n      </Text>\n    </View>\n  );\n\n  // Default error boundary\n  const DefaultErrorBoundary: React.FC<{ error: Error; retry: () => void }> = ({ error, retry }) => (\n    <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', padding: 20 }}>\n      <Text style={{ fontSize: 18, fontWeight: 'bold', color: '#FF3B30', marginBottom: 10 }}>\n        Failed to load {componentName}\n      </Text>\n      <Text style={{ fontSize: 14, color: '#666', textAlign: 'center', marginBottom: 20 }}>\n        {error.message}\n      </Text>\n      <Text \n        style={{ fontSize: 16, color: '#007AFF', textDecorationLine: 'underline' }}\n        onPress={retry}\n      >\n        Retry\n      </Text>\n    </View>\n  );\n\n  // Wrapper component with error boundary and suspense\n  const WrappedComponent: React.FC<React.ComponentProps<T>> = (props) => {\n    const [error, setError] = React.useState<Error | null>(null);\n    const [retryKey, setRetryKey] = React.useState(0);\n\n    const retry = React.useCallback(() => {\n      setError(null);\n      setRetryKey(prev => prev + 1);\n    }, []);\n\n    if (error) {\n      const ErrorComponent = CustomErrorBoundary || DefaultErrorBoundary;\n      return <ErrorComponent error={error} retry={retry} />;\n    }\n\n    return (\n      <React.ErrorBoundary\n        onError={(error) => setError(error)}\n        fallback={<DefaultErrorBoundary error={error!} retry={retry} />}\n      >\n        <Suspense fallback={<CustomFallback /> || <DefaultFallback />}>\n          <LazyComponent key={retryKey} {...props} />\n        </Suspense>\n      </React.ErrorBoundary>\n    );\n  };\n\n  WrappedComponent.displayName = `Lazy(${componentName})`;\n  return WrappedComponent;\n}\n\n/**\n * Preload multiple components during idle time\n */\nexport function preloadComponents(components: Array<{\n  name: string;\n  importFunc: () => Promise<any>;\n  priority?: 'high' | 'medium' | 'low';\n}>) {\n  const sortedComponents = components.sort((a, b) => {\n    const priorityOrder = { high: 0, medium: 1, low: 2 };\n    return priorityOrder[a.priority || 'medium'] - priorityOrder[b.priority || 'medium'];\n  });\n\n  sortedComponents.forEach((component, index) => {\n    const delay = index * 100; // Stagger preloading\n    \n    if (typeof requestIdleCallback !== 'undefined') {\n      requestIdleCallback(() => {\n        setTimeout(() => {\n          component.importFunc().catch(() => {\n            // Ignore preload errors\n          });\n        }, delay);\n      });\n    }\n  });\n}\n\n/**\n * Bundle splitting utilities\n */\nexport const bundleSplitting = {\n  /**\n   * Create feature-based lazy components\n   */\n  createFeatureBundle: <T extends ComponentType<any>>(\n    featureName: string,\n    components: Record<string, () => Promise<{ default: T }>>\n  ) => {\n    const lazyComponents: Record<string, React.ComponentType<any>> = {};\n    \n    Object.entries(components).forEach(([componentName, importFunc]) => {\n      lazyComponents[componentName] = createLazyComponent(\n        importFunc,\n        `${featureName}.${componentName}`,\n        { preload: false }\n      );\n    });\n\n    return lazyComponents;\n  },\n\n  /**\n   * Create vendor chunk separation\n   */\n  createVendorChunk: (vendors: string[]) => {\n    return vendors.map(vendor => ({\n      name: vendor,\n      test: new RegExp(`[\\\\/]node_modules[\\\\/]${vendor}[\\\\/]`),\n      chunks: 'all',\n      priority: 10,\n    }));\n  },\n};\n\n/**\n * Performance-aware component loader\n */\nexport class ComponentLoader {\n  private static instance: ComponentLoader;\n  private loadedComponents = new Set<string>();\n  private loadingComponents = new Map<string, Promise<any>>();\n\n  static getInstance(): ComponentLoader {\n    if (!ComponentLoader.instance) {\n      ComponentLoader.instance = new ComponentLoader();\n    }\n    return ComponentLoader.instance;\n  }\n\n  async loadComponent<T>(\n    name: string,\n    importFunc: () => Promise<{ default: T }>\n  ): Promise<T> {\n    // Return immediately if already loaded\n    if (this.loadedComponents.has(name)) {\n      return (await importFunc()).default;\n    }\n\n    // Return existing promise if currently loading\n    if (this.loadingComponents.has(name)) {\n      return this.loadingComponents.get(name)!;\n    }\n\n    // Start loading\n    const loadPromise = importFunc().then(module => {\n      this.loadedComponents.add(name);\n      this.loadingComponents.delete(name);\n      return module.default;\n    }).catch(error => {\n      this.loadingComponents.delete(name);\n      throw error;\n    });\n\n    this.loadingComponents.set(name, loadPromise);\n    return loadPromise;\n  }\n\n  getLoadedComponents(): string[] {\n    return Array.from(this.loadedComponents);\n  }\n\n  getLoadingComponents(): string[] {\n    return Array.from(this.loadingComponents.keys());\n  }\n}\n\nexport default {\n  createLazyComponent,\n  preloadComponents,\n  bundleSplitting,\n  ComponentLoader,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,IAAI,QAAuB,OAAO;AAC5D,SAASC,IAAI,EAAEC,IAAI,EAAEC,iBAAiB,QAAQ,cAAc;AAC5D,SAASC,kBAAkB;AAA8B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAYzD,OAAO,SAASC,mBAAmBA,CACjCC,UAAyC,EACzCC,aAAqB,EAEyB;EAAA,IAD9CC,OAAwB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,aAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;EAAAD,aAAA,GAAAE,CAAA;EAE7B,IAAAC,IAAA,IAAAH,aAAA,GAAAI,CAAA,OAKIR,OAAO;IAJCS,cAAc,GAAAF,IAAA,CAAxBG,QAAQ;IACOC,mBAAmB,GAAAJ,IAAA,CAAlCK,aAAa;IAAAC,YAAA,GAAAN,IAAA,CACbO,OAAO;IAAPA,OAAO,GAAAD,YAAA,eAAAT,aAAA,GAAAC,CAAA,UAAG,KAAK,IAAAQ,YAAA;IAAAE,YAAA,GAAAR,IAAA,CACfS,OAAO;IAAPA,OAAO,GAAAD,YAAA,eAAAX,aAAA,GAAAC,CAAA,UAAG,KAAK,IAAAU,YAAA;EAIjB,IAAME,aAAa,IAAAb,aAAA,GAAAI,CAAA,OAAGpB,IAAI,CAAA8B,iBAAA,CAAC,aAAY;IAAAd,aAAA,GAAAE,CAAA;IACrC,IAAMa,SAAS,IAAAf,aAAA,GAAAI,CAAA,OAAGY,IAAI,CAACC,GAAG,CAAC,CAAC;IAACjB,aAAA,GAAAI,CAAA;IAE7B,IAAI;MAEF,IAAMc,gBAAgB,IAAAlB,aAAA,GAAAI,CAAA,OAAGV,UAAU,CAAC,CAAC;MACrC,IAAMyB,cAAc,IAAAnB,aAAA,GAAAI,CAAA,OAAG,IAAIgB,OAAO,CAAQ,UAACC,CAAC,EAAEC,MAAM,EAAK;QAAAtB,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QACvDmB,UAAU,CAAC,YAAM;UAAAvB,aAAA,GAAAE,CAAA;UAAAF,aAAA,GAAAI,CAAA;UAAA,OAAAkB,MAAM,CAAC,IAAIE,KAAK,CAAC,aAAa7B,aAAa,eAAe,CAAC,CAAC;QAAD,CAAC,EAAEiB,OAAO,CAAC;MACzF,CAAC,CAAC;MAEF,IAAMa,SAAS,IAAAzB,aAAA,GAAAI,CAAA,aAASgB,OAAO,CAACM,IAAI,CAAC,CAACR,gBAAgB,EAAEC,cAAc,CAAC,CAAC;MACxE,IAAMQ,QAAQ,IAAA3B,aAAA,GAAAI,CAAA,OAAGY,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;MAACf,aAAA,GAAAI,CAAA;MAGxChB,kBAAkB,CAACwC,kBAAkB,CAACjC,aAAa,EAAEgC,QAAQ,CAAC;MAAC3B,aAAA,GAAAI,CAAA;MAE/D,IAAIuB,QAAQ,GAAG,IAAI,EAAE;QAAA3B,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACnByB,OAAO,CAACC,IAAI,CAAC,wBAAwBnC,aAAa,SAASgC,QAAQ,IAAI,CAAC;MAC1E,CAAC;QAAA3B,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,OAAOqB,SAAS;IAClB,CAAC,CAAC,OAAOM,KAAK,EAAE;MAAA/B,aAAA,GAAAI,CAAA;MACdhB,kBAAkB,CAAC4C,uBAAuB,CAACrC,aAAa,EAAEoC,KAAc,CAAC;MAAC/B,aAAA,GAAAI,CAAA;MAC1E,MAAM2B,KAAK;IACb;EACF,CAAC,EAAC;EAAC/B,aAAA,GAAAI,CAAA;EAGH,IAAIM,OAAO,EAAE;IAAAV,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAI,CAAA;IAEX,IAAI,OAAO6B,mBAAmB,KAAK,WAAW,EAAE;MAAAjC,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC9C6B,mBAAmB,CAAC,YAAM;QAAAjC,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QACxBV,UAAU,CAAC,CAAC,CAACwC,KAAK,CAAC,YAAM;UAAAlC,aAAA,GAAAE,CAAA;QAEzB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;MAAAF,aAAA,GAAAC,CAAA;IAAA;EACH,CAAC;IAAAD,aAAA,GAAAC,CAAA;EAAA;EAAAD,aAAA,GAAAI,CAAA;EAGD,IAAM+B,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EACnB;IAAAnC,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IAAA,OAAAZ,KAAA,CAACP,IAAI;MAACmD,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAG,CAAE;MAAAC,QAAA,GACpFnD,IAAA,CAACH,iBAAiB;QAACuD,IAAI,EAAC,OAAO;QAACC,KAAK,EAAC;MAAS,CAAE,CAAC,EAClDnD,KAAA,CAACN,IAAI;QAACkD,KAAK,EAAE;UAAEQ,SAAS,EAAE,EAAE;UAAEC,QAAQ,EAAE,EAAE;UAAEF,KAAK,EAAE;QAAO,CAAE;QAAAF,QAAA,GAAC,UACnD,EAAC9C,aAAa,EAAC,KACzB;MAAA,CAAM,CAAC;IAAA,CACH,CAAC;EAAD,CACP;EAACK,aAAA,GAAAI,CAAA;EAGF,IAAM0C,oBAAmE,GAAG,SAAtEA,oBAAmEA,CAAAC,KAAA,EACvE;IAAA,IAD6EhB,KAAK,GAAAgB,KAAA,CAALhB,KAAK;MAAEiB,KAAK,GAAAD,KAAA,CAALC,KAAK;IAAAhD,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACzF,OAAAZ,KAAA,CAACP,IAAI;MAACmD,KAAK,EAAE;QAAEC,IAAI,EAAE,CAAC;QAAEC,cAAc,EAAE,QAAQ;QAAEC,UAAU,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAG,CAAE;MAAAC,QAAA,GACpFjD,KAAA,CAACN,IAAI;QAACkD,KAAK,EAAE;UAAES,QAAQ,EAAE,EAAE;UAAEI,UAAU,EAAE,MAAM;UAAEN,KAAK,EAAE,SAAS;UAAEO,YAAY,EAAE;QAAG,CAAE;QAAAT,QAAA,GAAC,iBACtE,EAAC9C,aAAa;MAAA,CACzB,CAAC,EACPL,IAAA,CAACJ,IAAI;QAACkD,KAAK,EAAE;UAAES,QAAQ,EAAE,EAAE;UAAEF,KAAK,EAAE,MAAM;UAAEQ,SAAS,EAAE,QAAQ;UAAED,YAAY,EAAE;QAAG,CAAE;QAAAT,QAAA,EACjFV,KAAK,CAACqB;MAAO,CACV,CAAC,EACP9D,IAAA,CAACJ,IAAI;QACHkD,KAAK,EAAE;UAAES,QAAQ,EAAE,EAAE;UAAEF,KAAK,EAAE,SAAS;UAAEU,kBAAkB,EAAE;QAAY,CAAE;QAC3EC,OAAO,EAAEN,KAAM;QAAAP,QAAA,EAChB;MAED,CAAM,CAAC;IAAA,CACH,CAAC;EAAD,CACP;EAACzC,aAAA,GAAAI,CAAA;EAGF,IAAMmD,gBAAmD,GAAG,SAAtDA,gBAAmDA,CAAIC,KAAK,EAAK;IAAAxD,aAAA,GAAAE,CAAA;IACrE,IAAAuD,KAAA,IAAAzD,aAAA,GAAAI,CAAA,QAA0BtB,KAAK,CAAC4E,QAAQ,CAAe,IAAI,CAAC;MAAAC,KAAA,GAAAC,cAAA,CAAAH,KAAA;MAArD1B,KAAK,GAAA4B,KAAA;MAAEE,QAAQ,GAAAF,KAAA;IACtB,IAAAG,KAAA,IAAA9D,aAAA,GAAAI,CAAA,QAAgCtB,KAAK,CAAC4E,QAAQ,CAAC,CAAC,CAAC;MAAAK,KAAA,GAAAH,cAAA,CAAAE,KAAA;MAA1CE,QAAQ,GAAAD,KAAA;MAAEE,WAAW,GAAAF,KAAA;IAE5B,IAAMf,KAAK,IAAAhD,aAAA,GAAAI,CAAA,QAAGtB,KAAK,CAACoF,WAAW,CAAC,YAAM;MAAAlE,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MACpCyD,QAAQ,CAAC,IAAI,CAAC;MAAC7D,aAAA,GAAAI,CAAA;MACf6D,WAAW,CAAC,UAAAE,IAAI,EAAI;QAAAnE,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QAAA,OAAA+D,IAAI,GAAG,CAAC;MAAD,CAAC,CAAC;IAC/B,CAAC,EAAE,EAAE,CAAC;IAACnE,aAAA,GAAAI,CAAA;IAEP,IAAI2B,KAAK,EAAE;MAAA/B,aAAA,GAAAC,CAAA;MACT,IAAMmE,cAAc,IAAApE,aAAA,GAAAI,CAAA,QAAG,CAAAJ,aAAA,GAAAC,CAAA,UAAAM,mBAAmB,MAAAP,aAAA,GAAAC,CAAA,UAAI6C,oBAAoB;MAAC9C,aAAA,GAAAI,CAAA;MACnE,OAAOd,IAAA,CAAC8E,cAAc;QAACrC,KAAK,EAAEA,KAAM;QAACiB,KAAK,EAAEA;MAAM,CAAE,CAAC;IACvD,CAAC;MAAAhD,aAAA,GAAAC,CAAA;IAAA;IAAAD,aAAA,GAAAI,CAAA;IAED,OACEd,IAAA,CAACR,KAAK,CAACuF,aAAa;MAClBC,OAAO,EAAE,SAATA,OAAOA,CAAGvC,KAAK,EAAK;QAAA/B,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QAAA,OAAAyD,QAAQ,CAAC9B,KAAK,CAAC;MAAD,CAAE;MACpCzB,QAAQ,EAAEhB,IAAA,CAACwD,oBAAoB;QAACf,KAAK,EAAEA,KAAO;QAACiB,KAAK,EAAEA;MAAM,CAAE,CAAE;MAAAP,QAAA,EAEhEnD,IAAA,CAACP,QAAQ;QAACuB,QAAQ,EAAE,CAAAN,aAAA,GAAAC,CAAA,UAAAX,IAAA,CAACe,cAAc,IAAE,CAAC,MAAAL,aAAA,GAAAC,CAAA,UAAIX,IAAA,CAAC6C,eAAe,IAAE,CAAC,CAAC;QAAAM,QAAA,EAC5DnD,IAAA,CAACuB,aAAa,EAAA0D,MAAA,CAAAC,MAAA,KAAoBhB,KAAK,GAAnBQ,QAAsB;MAAC,CACnC;IAAC,CACQ,CAAC;EAE1B,CAAC;EAAChE,aAAA,GAAAI,CAAA;EAEFmD,gBAAgB,CAACkB,WAAW,GAAG,QAAQ9E,aAAa,GAAG;EAACK,aAAA,GAAAI,CAAA;EACxD,OAAOmD,gBAAgB;AACzB;AAKA,OAAO,SAASmB,iBAAiBA,CAACC,UAIhC,EAAE;EAAA3E,aAAA,GAAAE,CAAA;EACF,IAAM0E,gBAAgB,IAAA5E,aAAA,GAAAI,CAAA,QAAGuE,UAAU,CAACE,IAAI,CAAC,UAACC,CAAC,EAAE7E,CAAC,EAAK;IAAAD,aAAA,GAAAE,CAAA;IACjD,IAAM6E,aAAa,IAAA/E,aAAA,GAAAI,CAAA,QAAG;MAAE4E,IAAI,EAAE,CAAC;MAAEC,MAAM,EAAE,CAAC;MAAEC,GAAG,EAAE;IAAE,CAAC;IAAClF,aAAA,GAAAI,CAAA;IACrD,OAAO2E,aAAa,CAAC,CAAA/E,aAAA,GAAAC,CAAA,UAAA6E,CAAC,CAACK,QAAQ,MAAAnF,aAAA,GAAAC,CAAA,UAAI,QAAQ,EAAC,GAAG8E,aAAa,CAAC,CAAA/E,aAAA,GAAAC,CAAA,WAAAA,CAAC,CAACkF,QAAQ,MAAAnF,aAAA,GAAAC,CAAA,WAAI,QAAQ,EAAC;EACtF,CAAC,CAAC;EAACD,aAAA,GAAAI,CAAA;EAEHwE,gBAAgB,CAACQ,OAAO,CAAC,UAAC3D,SAAS,EAAE4D,KAAK,EAAK;IAAArF,aAAA,GAAAE,CAAA;IAC7C,IAAMoF,KAAK,IAAAtF,aAAA,GAAAI,CAAA,QAAGiF,KAAK,GAAG,GAAG;IAACrF,aAAA,GAAAI,CAAA;IAE1B,IAAI,OAAO6B,mBAAmB,KAAK,WAAW,EAAE;MAAAjC,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAI,CAAA;MAC9C6B,mBAAmB,CAAC,YAAM;QAAAjC,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QACxBmB,UAAU,CAAC,YAAM;UAAAvB,aAAA,GAAAE,CAAA;UAAAF,aAAA,GAAAI,CAAA;UACfqB,SAAS,CAAC/B,UAAU,CAAC,CAAC,CAACwC,KAAK,CAAC,YAAM;YAAAlC,aAAA,GAAAE,CAAA;UAEnC,CAAC,CAAC;QACJ,CAAC,EAAEoF,KAAK,CAAC;MACX,CAAC,CAAC;IACJ,CAAC;MAAAtF,aAAA,GAAAC,CAAA;IAAA;EACH,CAAC,CAAC;AACJ;AAKA,OAAO,IAAMsF,eAAe,IAAAvF,aAAA,GAAAI,CAAA,QAAG;EAI7BoF,mBAAmB,EAAE,SAArBA,mBAAmBA,CACjBC,WAAmB,EACnBd,UAAyD,EACtD;IAAA3E,aAAA,GAAAE,CAAA;IACH,IAAMwF,cAAwD,IAAA1F,aAAA,GAAAI,CAAA,QAAG,CAAC,CAAC;IAACJ,aAAA,GAAAI,CAAA;IAEpEmE,MAAM,CAACoB,OAAO,CAAChB,UAAU,CAAC,CAACS,OAAO,CAAC,UAAAQ,KAAA,EAAiC;MAAA,IAAAC,KAAA,GAAAjC,cAAA,CAAAgC,KAAA;QAA/BjG,aAAa,GAAAkG,KAAA;QAAEnG,UAAU,GAAAmG,KAAA;MAAA7F,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAC5DsF,cAAc,CAAC/F,aAAa,CAAC,GAAGF,mBAAmB,CACjDC,UAAU,EACV,GAAG+F,WAAW,IAAI9F,aAAa,EAAE,EACjC;QAAEe,OAAO,EAAE;MAAM,CACnB,CAAC;IACH,CAAC,CAAC;IAACV,aAAA,GAAAI,CAAA;IAEH,OAAOsF,cAAc;EACvB,CAAC;EAKDI,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGC,OAAiB,EAAK;IAAA/F,aAAA,GAAAE,CAAA;IAAAF,aAAA,GAAAI,CAAA;IACxC,OAAO2F,OAAO,CAACC,GAAG,CAAC,UAAAC,MAAM,EAAK;MAAAjG,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAAA;QAC5B8F,IAAI,EAAED,MAAM;QACZE,IAAI,EAAE,IAAIC,MAAM,CAAC,yBAAyBH,MAAM,OAAO,CAAC;QACxDI,MAAM,EAAE,KAAK;QACblB,QAAQ,EAAE;MACZ,CAAC;IAAD,CAAE,CAAC;EACL;AACF,CAAC;AAKD,WAAamB,eAAe;EAAA,SAAAA,gBAAA;IAAAC,eAAA,OAAAD,eAAA;IAAA,KAElBE,gBAAgB,IAAAxG,aAAA,GAAAI,CAAA,QAAG,IAAIqG,GAAG,CAAS,CAAC;IAAA,KACpCC,iBAAiB,IAAA1G,aAAA,GAAAI,CAAA,QAAG,IAAIuG,GAAG,CAAuB,CAAC;EAAA;EAAA,OAAAC,YAAA,CAAAN,eAAA;IAAAO,GAAA;IAAAC,KAAA;MAAA,IAAAC,cAAA,GAAAjG,iBAAA,CAS3D,WACEoF,IAAY,EACZxG,UAAyC,EAC7B;QAAA,IAAAsH,KAAA;QAAAhH,aAAA,GAAAE,CAAA;QAAAF,aAAA,GAAAI,CAAA;QAEZ,IAAI,IAAI,CAACoG,gBAAgB,CAACS,GAAG,CAACf,IAAI,CAAC,EAAE;UAAAlG,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACnC,OAAO,OAAOV,UAAU,CAAC,CAAC,EAAEwH,OAAO;QACrC,CAAC;UAAAlH,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,IAAI,IAAI,CAACsG,iBAAiB,CAACO,GAAG,CAACf,IAAI,CAAC,EAAE;UAAAlG,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACpC,OAAO,IAAI,CAACsG,iBAAiB,CAACS,GAAG,CAACjB,IAAI,CAAC;QACzC,CAAC;UAAAlG,aAAA,GAAAC,CAAA;QAAA;QAGD,IAAMmH,WAAW,IAAApH,aAAA,GAAAI,CAAA,QAAGV,UAAU,CAAC,CAAC,CAAC2H,IAAI,CAAC,UAAAC,MAAM,EAAI;UAAAtH,aAAA,GAAAE,CAAA;UAAAF,aAAA,GAAAI,CAAA;UAC9C4G,KAAI,CAACR,gBAAgB,CAACe,GAAG,CAACrB,IAAI,CAAC;UAAClG,aAAA,GAAAI,CAAA;UAChC4G,KAAI,CAACN,iBAAiB,CAACc,MAAM,CAACtB,IAAI,CAAC;UAAClG,aAAA,GAAAI,CAAA;UACpC,OAAOkH,MAAM,CAACJ,OAAO;QACvB,CAAC,CAAC,CAAChF,KAAK,CAAC,UAAAH,KAAK,EAAI;UAAA/B,aAAA,GAAAE,CAAA;UAAAF,aAAA,GAAAI,CAAA;UAChB4G,KAAI,CAACN,iBAAiB,CAACc,MAAM,CAACtB,IAAI,CAAC;UAAClG,aAAA,GAAAI,CAAA;UACpC,MAAM2B,KAAK;QACb,CAAC,CAAC;QAAC/B,aAAA,GAAAI,CAAA;QAEH,IAAI,CAACsG,iBAAiB,CAACe,GAAG,CAACvB,IAAI,EAAEkB,WAAW,CAAC;QAACpH,aAAA,GAAAI,CAAA;QAC9C,OAAOgH,WAAW;MACpB,CAAC;MAAA,SA1BKM,aAAaA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAb,cAAA,CAAAc,KAAA,OAAAhI,SAAA;MAAA;MAAA,OAAb6H,aAAa;IAAA;EAAA;IAAAb,GAAA;IAAAC,KAAA,EA4BnB,SAAAgB,mBAAmBA,CAAA,EAAa;MAAA9H,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAC9B,OAAO2H,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxB,gBAAgB,CAAC;IAC1C;EAAC;IAAAK,GAAA;IAAAC,KAAA,EAED,SAAAmB,oBAAoBA,CAAA,EAAa;MAAAjI,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MAC/B,OAAO2H,KAAK,CAACC,IAAI,CAAC,IAAI,CAACtB,iBAAiB,CAACwB,IAAI,CAAC,CAAC,CAAC;IAClD;EAAC;IAAArB,GAAA;IAAAC,KAAA,EAzCD,SAAOqB,WAAWA,CAAA,EAAoB;MAAAnI,aAAA,GAAAE,CAAA;MAAAF,aAAA,GAAAI,CAAA;MACpC,IAAI,CAACkG,eAAe,CAAC8B,QAAQ,EAAE;QAAApI,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAC7BkG,eAAe,CAAC8B,QAAQ,GAAG,IAAI9B,eAAe,CAAC,CAAC;MAClD,CAAC;QAAAtG,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MACD,OAAOkG,eAAe,CAAC8B,QAAQ;IACjC;EAAC;AAAA;AAuCH,eAAe;EACb3I,mBAAmB,EAAnBA,mBAAmB;EACnBiF,iBAAiB,EAAjBA,iBAAiB;EACjBa,eAAe,EAAfA,eAAe;EACfe,eAAe,EAAfA;AACF,CAAC", "ignoreList": []}