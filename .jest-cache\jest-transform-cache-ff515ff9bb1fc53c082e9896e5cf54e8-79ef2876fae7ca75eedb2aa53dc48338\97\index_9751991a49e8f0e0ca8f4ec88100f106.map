{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "useWindowDimensions", "_Dimensions", "_react", "_useState", "useState", "get", "dims", "setDims", "useEffect", "handleChange", "_ref", "window", "addEventListener", "removeEventListener", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = useWindowDimensions;\nvar _Dimensions = _interopRequireDefault(require(\"../Dimensions\"));\nvar _react = require(\"react\");\nfunction useWindowDimensions() {\n  var _useState = (0, _react.useState)(() => _Dimensions.default.get('window')),\n    dims = _useState[0],\n    setDims = _useState[1];\n  (0, _react.useEffect)(() => {\n    function handleChange(_ref) {\n      var window = _ref.window;\n      if (window != null) {\n        setDims(window);\n      }\n    }\n    _Dimensions.default.addEventListener('change', handleChange);\n    // We might have missed an update between calling `get` in render and\n    // `addEventListener` in this handler, so we set it here. If there was\n    // no change, React will filter out this update as a no-op.\n    setDims(_Dimensions.default.get('window'));\n    return () => {\n      _Dimensions.default.removeEventListener('change', handleChange);\n    };\n  }, []);\n  return dims;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,mBAAmB;AACrC,IAAIC,WAAW,GAAGN,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIM,MAAM,GAAGN,OAAO,CAAC,OAAO,CAAC;AAC7B,SAASI,mBAAmBA,CAAA,EAAG;EAC7B,IAAIG,SAAS,GAAG,CAAC,CAAC,EAAED,MAAM,CAACE,QAAQ,EAAE;MAAA,OAAMH,WAAW,CAACJ,OAAO,CAACQ,GAAG,CAAC,QAAQ,CAAC;IAAA,EAAC;IAC3EC,IAAI,GAAGH,SAAS,CAAC,CAAC,CAAC;IACnBI,OAAO,GAAGJ,SAAS,CAAC,CAAC,CAAC;EACxB,CAAC,CAAC,EAAED,MAAM,CAACM,SAAS,EAAE,YAAM;IAC1B,SAASC,YAAYA,CAACC,IAAI,EAAE;MAC1B,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;MACxB,IAAIA,MAAM,IAAI,IAAI,EAAE;QAClBJ,OAAO,CAACI,MAAM,CAAC;MACjB;IACF;IACAV,WAAW,CAACJ,OAAO,CAACe,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAI5DF,OAAO,CAACN,WAAW,CAACJ,OAAO,CAACQ,GAAG,CAAC,QAAQ,CAAC,CAAC;IAC1C,OAAO,YAAM;MACXJ,WAAW,CAACJ,OAAO,CAACgB,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;IACjE,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOH,IAAI;AACb;AACAQ,MAAM,CAAChB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}