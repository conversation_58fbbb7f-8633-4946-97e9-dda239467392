f577850932a0d3f98994ba3e4974d765
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_bxwhcoz4r() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\ai\\VideoProcessingService.ts";
  var hash = "2d836db71feb2e28b6c0a020e23e11d81740d2fd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\ai\\VideoProcessingService.ts",
    statementMap: {
      "0": {
        start: {
          line: 74,
          column: 45
        },
        end: {
          line: 74,
          column: 47
        }
      },
      "1": {
        start: {
          line: 75,
          column: 51
        },
        end: {
          line: 75,
          column: 60
        }
      },
      "2": {
        start: {
          line: 76,
          column: 68
        },
        end: {
          line: 76,
          column: 77
        }
      },
      "3": {
        start: {
          line: 78,
          column: 37
        },
        end: {
          line: 87,
          column: 3
        }
      },
      "4": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 131,
          column: 5
        }
      },
      "5": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 51
        }
      },
      "6": {
        start: {
          line: 103,
          column: 33
        },
        end: {
          line: 115,
          column: 7
        }
      },
      "7": {
        start: {
          line: 118,
          column: 6
        },
        end: {
          line: 118,
          column: 78
        }
      },
      "8": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 27
        }
      },
      "9": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 124,
          column: 26
        }
      },
      "10": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 126,
          column: 49
        }
      },
      "11": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 20
        }
      },
      "12": {
        start: {
          line: 129,
          column: 6
        },
        end: {
          line: 129,
          column: 69
        }
      },
      "13": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 49
        }
      },
      "14": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 140,
          column: 16
        }
      },
      "15": {
        start: {
          line: 139,
          column: 44
        },
        end: {
          line: 139,
          column: 60
        }
      },
      "16": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 167,
          column: 5
        }
      },
      "17": {
        start: {
          line: 148,
          column: 18
        },
        end: {
          line: 148,
          column: 44
        }
      },
      "18": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 154,
          column: 7
        }
      },
      "19": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 150,
          column: 33
        }
      },
      "20": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 151,
          column: 38
        }
      },
      "21": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 152,
          column: 34
        }
      },
      "22": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 153,
          column: 20
        }
      },
      "23": {
        start: {
          line: 156,
          column: 25
        },
        end: {
          line: 156,
          column: 76
        }
      },
      "24": {
        start: {
          line: 156,
          column: 61
        },
        end: {
          line: 156,
          column: 75
        }
      },
      "25": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 161,
          column: 7
        }
      },
      "26": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 158,
          column: 62
        }
      },
      "27": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 159,
          column: 51
        }
      },
      "28": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 160,
          column: 20
        }
      },
      "29": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 19
        }
      },
      "30": {
        start: {
          line: 165,
          column: 6
        },
        end: {
          line: 165,
          column: 52
        }
      },
      "31": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 19
        }
      },
      "32": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 43
        }
      },
      "33": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 181,
          column: 36
        }
      },
      "34": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 252,
          column: 5
        }
      },
      "35": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 61
        }
      },
      "36": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 191,
          column: 32
        }
      },
      "37": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 47
        }
      },
      "38": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 193,
          column: 23
        }
      },
      "39": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 194,
          column: 32
        }
      },
      "40": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 24
        }
      },
      "41": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 198,
          column: 32
        }
      },
      "42": {
        start: {
          line: 199,
          column: 29
        },
        end: {
          line: 199,
          column: 72
        }
      },
      "43": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 203,
          column: 7
        }
      },
      "44": {
        start: {
          line: 202,
          column: 8
        },
        end: {
          line: 202,
          column: 71
        }
      },
      "45": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 206,
          column: 24
        }
      },
      "46": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 207,
          column: 32
        }
      },
      "47": {
        start: {
          line: 208,
          column: 23
        },
        end: {
          line: 208,
          column: 78
        }
      },
      "48": {
        start: {
          line: 209,
          column: 6
        },
        end: {
          line: 209,
          column: 30
        }
      },
      "49": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 24
        }
      },
      "50": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 32
        }
      },
      "51": {
        start: {
          line: 214,
          column: 30
        },
        end: {
          line: 214,
          column: 71
        }
      },
      "52": {
        start: {
          line: 217,
          column: 6
        },
        end: {
          line: 217,
          column: 24
        }
      },
      "53": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 32
        }
      },
      "54": {
        start: {
          line: 219,
          column: 30
        },
        end: {
          line: 223,
          column: 7
        }
      },
      "55": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 24
        }
      },
      "56": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 32
        }
      },
      "57": {
        start: {
          line: 228,
          column: 21
        },
        end: {
          line: 233,
          column: 7
        }
      },
      "58": {
        start: {
          line: 236,
          column: 6
        },
        end: {
          line: 236,
          column: 31
        }
      },
      "59": {
        start: {
          line: 237,
          column: 6
        },
        end: {
          line: 237,
          column: 25
        }
      },
      "60": {
        start: {
          line: 238,
          column: 6
        },
        end: {
          line: 238,
          column: 49
        }
      },
      "61": {
        start: {
          line: 239,
          column: 6
        },
        end: {
          line: 239,
          column: 32
        }
      },
      "62": {
        start: {
          line: 241,
          column: 6
        },
        end: {
          line: 241,
          column: 59
        }
      },
      "63": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 20
        }
      },
      "64": {
        start: {
          line: 244,
          column: 6
        },
        end: {
          line: 244,
          column: 52
        }
      },
      "65": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 28
        }
      },
      "66": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 246,
          column: 82
        }
      },
      "67": {
        start: {
          line: 247,
          column: 6
        },
        end: {
          line: 247,
          column: 32
        }
      },
      "68": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 18
        }
      },
      "69": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 250,
          column: 37
        }
      },
      "70": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 251,
          column: 26
        }
      },
      "71": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 260,
          column: 59
        }
      },
      "72": {
        start: {
          line: 260,
          column: 33
        },
        end: {
          line: 260,
          column: 57
        }
      },
      "73": {
        start: {
          line: 268,
          column: 4
        },
        end: {
          line: 273,
          column: 6
        }
      },
      "74": {
        start: {
          line: 284,
          column: 4
        },
        end: {
          line: 284,
          column: 60
        }
      },
      "75": {
        start: {
          line: 284,
          column: 33
        },
        end: {
          line: 284,
          column: 58
        }
      },
      "76": {
        start: {
          line: 291,
          column: 37
        },
        end: {
          line: 316,
          column: 5
        }
      },
      "77": {
        start: {
          line: 318,
          column: 4
        },
        end: {
          line: 318,
          column: 20
        }
      },
      "78": {
        start: {
          line: 333,
          column: 20
        },
        end: {
          line: 333,
          column: 22
        }
      },
      "79": {
        start: {
          line: 334,
          column: 31
        },
        end: {
          line: 334,
          column: 51
        }
      },
      "80": {
        start: {
          line: 336,
          column: 4
        },
        end: {
          line: 373,
          column: 5
        }
      },
      "81": {
        start: {
          line: 336,
          column: 17
        },
        end: {
          line: 336,
          column: 18
        }
      },
      "82": {
        start: {
          line: 337,
          column: 22
        },
        end: {
          line: 337,
          column: 33
        }
      },
      "83": {
        start: {
          line: 338,
          column: 24
        },
        end: {
          line: 338,
          column: 34
        }
      },
      "84": {
        start: {
          line: 341,
          column: 21
        },
        end: {
          line: 341,
          column: 68
        }
      },
      "85": {
        start: {
          line: 344,
          column: 31
        },
        end: {
          line: 351,
          column: 7
        }
      },
      "86": {
        start: {
          line: 347,
          column: 34
        },
        end: {
          line: 347,
          column: 92
        }
      },
      "87": {
        start: {
          line: 348,
          column: 10
        },
        end: {
          line: 348,
          column: 46
        }
      },
      "88": {
        start: {
          line: 349,
          column: 10
        },
        end: {
          line: 349,
          column: 36
        }
      },
      "89": {
        start: {
          line: 354,
          column: 31
        },
        end: {
          line: 363,
          column: 8
        }
      },
      "90": {
        start: {
          line: 365,
          column: 29
        },
        end: {
          line: 365,
          column: 51
        }
      },
      "91": {
        start: {
          line: 367,
          column: 6
        },
        end: {
          line: 372,
          column: 9
        }
      },
      "92": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 375,
          column: 19
        }
      },
      "93": {
        start: {
          line: 386,
          column: 4
        },
        end: {
          line: 386,
          column: 59
        }
      },
      "94": {
        start: {
          line: 386,
          column: 33
        },
        end: {
          line: 386,
          column: 57
        }
      },
      "95": {
        start: {
          line: 393,
          column: 23
        },
        end: {
          line: 393,
          column: 105
        }
      },
      "96": {
        start: {
          line: 394,
          column: 40
        },
        end: {
          line: 394,
          column: 42
        }
      },
      "97": {
        start: {
          line: 396,
          column: 4
        },
        end: {
          line: 401,
          column: 5
        }
      },
      "98": {
        start: {
          line: 396,
          column: 17
        },
        end: {
          line: 396,
          column: 18
        }
      },
      "99": {
        start: {
          line: 397,
          column: 21
        },
        end: {
          line: 397,
          column: 53
        }
      },
      "100": {
        start: {
          line: 398,
          column: 6
        },
        end: {
          line: 398,
          column: 25
        }
      },
      "101": {
        start: {
          line: 399,
          column: 6
        },
        end: {
          line: 399,
          column: 26
        }
      },
      "102": {
        start: {
          line: 400,
          column: 6
        },
        end: {
          line: 400,
          column: 26
        }
      },
      "103": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 403,
          column: 18
        }
      },
      "104": {
        start: {
          line: 419,
          column: 32
        },
        end: {
          line: 419,
          column: 82
        }
      },
      "105": {
        start: {
          line: 419,
          column: 62
        },
        end: {
          line: 419,
          column: 81
        }
      },
      "106": {
        start: {
          line: 422,
          column: 4
        },
        end: {
          line: 431,
          column: 7
        }
      },
      "107": {
        start: {
          line: 448,
          column: 24
        },
        end: {
          line: 448,
          column: 96
        }
      },
      "108": {
        start: {
          line: 448,
          column: 60
        },
        end: {
          line: 448,
          column: 92
        }
      },
      "109": {
        start: {
          line: 449,
          column: 32
        },
        end: {
          line: 449,
          column: 95
        }
      },
      "110": {
        start: {
          line: 449,
          column: 68
        },
        end: {
          line: 449,
          column: 91
        }
      },
      "111": {
        start: {
          line: 450,
          column: 34
        },
        end: {
          line: 450,
          column: 78
        }
      },
      "112": {
        start: {
          line: 453,
          column: 24
        },
        end: {
          line: 453,
          column: 74
        }
      },
      "113": {
        start: {
          line: 453,
          column: 54
        },
        end: {
          line: 453,
          column: 73
        }
      },
      "114": {
        start: {
          line: 454,
          column: 26
        },
        end: {
          line: 454,
          column: 100
        }
      },
      "115": {
        start: {
          line: 454,
          column: 57
        },
        end: {
          line: 454,
          column: 75
        }
      },
      "116": {
        start: {
          line: 456,
          column: 4
        },
        end: {
          line: 469,
          column: 6
        }
      },
      "117": {
        start: {
          line: 477,
          column: 4
        },
        end: {
          line: 477,
          column: 59
        }
      },
      "118": {
        start: {
          line: 477,
          column: 33
        },
        end: {
          line: 477,
          column: 57
        }
      },
      "119": {
        start: {
          line: 484,
          column: 33
        },
        end: {
          line: 484,
          column: 34
        }
      },
      "120": {
        start: {
          line: 485,
          column: 28
        },
        end: {
          line: 485,
          column: 31
        }
      },
      "121": {
        start: {
          line: 487,
          column: 4
        },
        end: {
          line: 487,
          column: 55
        }
      },
      "122": {
        start: {
          line: 495,
          column: 24
        },
        end: {
          line: 497,
          column: 5
        }
      },
      "123": {
        start: {
          line: 496,
          column: 19
        },
        end: {
          line: 496,
          column: 98
        }
      },
      "124": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 503,
          column: 5
        }
      },
      "125": {
        start: {
          line: 500,
          column: 6
        },
        end: {
          line: 500,
          column: 37
        }
      },
      "126": {
        start: {
          line: 502,
          column: 6
        },
        end: {
          line: 502,
          column: 55
        }
      },
      "127": {
        start: {
          line: 510,
          column: 4
        },
        end: {
          line: 512,
          column: 5
        }
      },
      "128": {
        start: {
          line: 511,
          column: 6
        },
        end: {
          line: 511,
          column: 13
        }
      },
      "129": {
        start: {
          line: 514,
          column: 20
        },
        end: {
          line: 514,
          column: 48
        }
      },
      "130": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 517,
          column: 5
        }
      },
      "131": {
        start: {
          line: 516,
          column: 6
        },
        end: {
          line: 516,
          column: 13
        }
      },
      "132": {
        start: {
          line: 519,
          column: 4
        },
        end: {
          line: 522,
          column: 5
        }
      },
      "133": {
        start: {
          line: 520,
          column: 6
        },
        end: {
          line: 520,
          column: 26
        }
      },
      "134": {
        start: {
          line: 521,
          column: 6
        },
        end: {
          line: 521,
          column: 13
        }
      },
      "135": {
        start: {
          line: 524,
          column: 4
        },
        end: {
          line: 524,
          column: 45
        }
      },
      "136": {
        start: {
          line: 526,
          column: 4
        },
        end: {
          line: 530,
          column: 5
        }
      },
      "137": {
        start: {
          line: 527,
          column: 6
        },
        end: {
          line: 527,
          column: 37
        }
      },
      "138": {
        start: {
          line: 529,
          column: 6
        },
        end: {
          line: 529,
          column: 67
        }
      },
      "139": {
        start: {
          line: 537,
          column: 21
        },
        end: {
          line: 537,
          column: 50
        }
      },
      "140": {
        start: {
          line: 538,
          column: 4
        },
        end: {
          line: 540,
          column: 5
        }
      },
      "141": {
        start: {
          line: 539,
          column: 6
        },
        end: {
          line: 539,
          column: 20
        }
      },
      "142": {
        start: {
          line: 547,
          column: 4
        },
        end: {
          line: 551,
          column: 5
        }
      },
      "143": {
        start: {
          line: 548,
          column: 19
        },
        end: {
          line: 548,
          column: 28
        }
      },
      "144": {
        start: {
          line: 549,
          column: 21
        },
        end: {
          line: 549,
          column: 30
        }
      },
      "145": {
        start: {
          line: 550,
          column: 18
        },
        end: {
          line: 550,
          column: 27
        }
      },
      "146": {
        start: {
          line: 558,
          column: 4
        },
        end: {
          line: 558,
          column: 74
        }
      },
      "147": {
        start: {
          line: 565,
          column: 4
        },
        end: {
          line: 565,
          column: 48
        }
      },
      "148": {
        start: {
          line: 572,
          column: 4
        },
        end: {
          line: 572,
          column: 30
        }
      },
      "149": {
        start: {
          line: 583,
          column: 4
        },
        end: {
          line: 587,
          column: 6
        }
      },
      "150": {
        start: {
          line: 594,
          column: 4
        },
        end: {
          line: 594,
          column: 36
        }
      },
      "151": {
        start: {
          line: 595,
          column: 4
        },
        end: {
          line: 595,
          column: 28
        }
      },
      "152": {
        start: {
          line: 596,
          column: 4
        },
        end: {
          line: 596,
          column: 30
        }
      },
      "153": {
        start: {
          line: 601,
          column: 38
        },
        end: {
          line: 601,
          column: 66
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 92,
            column: 2
          },
          end: {
            line: 92,
            column: 3
          }
        },
        loc: {
          start: {
            line: 99,
            column: 21
          },
          end: {
            line: 132,
            column: 3
          }
        },
        line: 99
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 137,
            column: 2
          },
          end: {
            line: 137,
            column: 3
          }
        },
        loc: {
          start: {
            line: 137,
            column: 52
          },
          end: {
            line: 141,
            column: 3
          }
        },
        line: 137
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 139,
            column: 37
          },
          end: {
            line: 139,
            column: 38
          }
        },
        loc: {
          start: {
            line: 139,
            column: 44
          },
          end: {
            line: 139,
            column: 60
          }
        },
        line: 139
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 146,
            column: 2
          },
          end: {
            line: 146,
            column: 3
          }
        },
        loc: {
          start: {
            line: 146,
            column: 51
          },
          end: {
            line: 168,
            column: 3
          }
        },
        line: 146
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 156,
            column: 56
          },
          end: {
            line: 156,
            column: 57
          }
        },
        loc: {
          start: {
            line: 156,
            column: 61
          },
          end: {
            line: 156,
            column: 75
          }
        },
        line: 156
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 173,
            column: 2
          },
          end: {
            line: 173,
            column: 3
          }
        },
        loc: {
          start: {
            line: 173,
            column: 78
          },
          end: {
            line: 175,
            column: 3
          }
        },
        line: 173
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 180,
            column: 2
          },
          end: {
            line: 180,
            column: 3
          }
        },
        loc: {
          start: {
            line: 180,
            column: 41
          },
          end: {
            line: 182,
            column: 3
          }
        },
        line: 180
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 187,
            column: 2
          },
          end: {
            line: 187,
            column: 3
          }
        },
        loc: {
          start: {
            line: 187,
            column: 74
          },
          end: {
            line: 253,
            column: 3
          }
        },
        line: 187
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 258,
            column: 2
          },
          end: {
            line: 258,
            column: 3
          }
        },
        loc: {
          start: {
            line: 258,
            column: 98
          },
          end: {
            line: 274,
            column: 3
          }
        },
        line: 258
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 260,
            column: 22
          },
          end: {
            line: 260,
            column: 23
          }
        },
        loc: {
          start: {
            line: 260,
            column: 33
          },
          end: {
            line: 260,
            column: 57
          }
        },
        line: 260
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 279,
            column: 2
          },
          end: {
            line: 279,
            column: 3
          }
        },
        loc: {
          start: {
            line: 282,
            column: 29
          },
          end: {
            line: 319,
            column: 3
          }
        },
        line: 282
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 284,
            column: 22
          },
          end: {
            line: 284,
            column: 23
          }
        },
        loc: {
          start: {
            line: 284,
            column: 33
          },
          end: {
            line: 284,
            column: 58
          }
        },
        line: 284
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 324,
            column: 2
          },
          end: {
            line: 324,
            column: 3
          }
        },
        loc: {
          start: {
            line: 332,
            column: 6
          },
          end: {
            line: 376,
            column: 3
          }
        },
        line: 332
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 346,
            column: 8
          },
          end: {
            line: 346,
            column: 9
          }
        },
        loc: {
          start: {
            line: 346,
            column: 22
          },
          end: {
            line: 350,
            column: 9
          }
        },
        line: 346
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 381,
            column: 2
          },
          end: {
            line: 381,
            column: 3
          }
        },
        loc: {
          start: {
            line: 384,
            column: 34
          },
          end: {
            line: 404,
            column: 3
          }
        },
        line: 384
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 386,
            column: 22
          },
          end: {
            line: 386,
            column: 23
          }
        },
        loc: {
          start: {
            line: 386,
            column: 33
          },
          end: {
            line: 386,
            column: 57
          }
        },
        line: 386
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 409,
            column: 2
          },
          end: {
            line: 409,
            column: 3
          }
        },
        loc: {
          start: {
            line: 417,
            column: 37
          },
          end: {
            line: 432,
            column: 3
          }
        },
        line: 417
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 419,
            column: 56
          },
          end: {
            line: 419,
            column: 57
          }
        },
        loc: {
          start: {
            line: 419,
            column: 62
          },
          end: {
            line: 419,
            column: 81
          }
        },
        line: 419
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 437,
            column: 2
          },
          end: {
            line: 437,
            column: 3
          }
        },
        loc: {
          start: {
            line: 447,
            column: 31
          },
          end: {
            line: 470,
            column: 3
          }
        },
        line: 447
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 448,
            column: 47
          },
          end: {
            line: 448,
            column: 48
          }
        },
        loc: {
          start: {
            line: 448,
            column: 60
          },
          end: {
            line: 448,
            column: 92
          }
        },
        line: 448
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 449,
            column: 55
          },
          end: {
            line: 449,
            column: 56
          }
        },
        loc: {
          start: {
            line: 449,
            column: 68
          },
          end: {
            line: 449,
            column: 91
          }
        },
        line: 449
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 453,
            column: 48
          },
          end: {
            line: 453,
            column: 49
          }
        },
        loc: {
          start: {
            line: 453,
            column: 54
          },
          end: {
            line: 453,
            column: 73
          }
        },
        line: 453
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 454,
            column: 45
          },
          end: {
            line: 454,
            column: 46
          }
        },
        loc: {
          start: {
            line: 454,
            column: 57
          },
          end: {
            line: 454,
            column: 75
          }
        },
        line: 454
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 475,
            column: 2
          },
          end: {
            line: 475,
            column: 3
          }
        },
        loc: {
          start: {
            line: 475,
            column: 78
          },
          end: {
            line: 488,
            column: 3
          }
        },
        line: 475
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 477,
            column: 22
          },
          end: {
            line: 477,
            column: 23
          }
        },
        loc: {
          start: {
            line: 477,
            column: 33
          },
          end: {
            line: 477,
            column: 57
          }
        },
        line: 477
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 493,
            column: 2
          },
          end: {
            line: 493,
            column: 3
          }
        },
        loc: {
          start: {
            line: 493,
            column: 47
          },
          end: {
            line: 504,
            column: 3
          }
        },
        line: 493
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 496,
            column: 6
          },
          end: {
            line: 496,
            column: 7
          }
        },
        loc: {
          start: {
            line: 496,
            column: 19
          },
          end: {
            line: 496,
            column: 98
          }
        },
        line: 496
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 509,
            column: 2
          },
          end: {
            line: 509,
            column: 3
          }
        },
        loc: {
          start: {
            line: 509,
            column: 46
          },
          end: {
            line: 531,
            column: 3
          }
        },
        line: 509
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 536,
            column: 2
          },
          end: {
            line: 536,
            column: 3
          }
        },
        loc: {
          start: {
            line: 536,
            column: 52
          },
          end: {
            line: 541,
            column: 3
          }
        },
        line: 536
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 546,
            column: 2
          },
          end: {
            line: 546,
            column: 3
          }
        },
        loc: {
          start: {
            line: 546,
            column: 72
          },
          end: {
            line: 552,
            column: 3
          }
        },
        line: 546
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 557,
            column: 2
          },
          end: {
            line: 557,
            column: 3
          }
        },
        loc: {
          start: {
            line: 557,
            column: 34
          },
          end: {
            line: 559,
            column: 3
          }
        },
        line: 557
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 564,
            column: 2
          },
          end: {
            line: 564,
            column: 3
          }
        },
        loc: {
          start: {
            line: 564,
            column: 56
          },
          end: {
            line: 566,
            column: 3
          }
        },
        line: 564
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 571,
            column: 2
          },
          end: {
            line: 571,
            column: 3
          }
        },
        loc: {
          start: {
            line: 571,
            column: 32
          },
          end: {
            line: 573,
            column: 3
          }
        },
        line: 571
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 578,
            column: 2
          },
          end: {
            line: 578,
            column: 3
          }
        },
        loc: {
          start: {
            line: 582,
            column: 4
          },
          end: {
            line: 588,
            column: 3
          }
        },
        line: 582
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 593,
            column: 2
          },
          end: {
            line: 593,
            column: 3
          }
        },
        loc: {
          start: {
            line: 593,
            column: 18
          },
          end: {
            line: 597,
            column: 3
          }
        },
        line: 593
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 98,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 98,
            column: 42
          },
          end: {
            line: 98,
            column: 50
          }
        }],
        line: 98
      },
      "1": {
        loc: {
          start: {
            line: 138,
            column: 11
          },
          end: {
            line: 140,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 11
          },
          end: {
            line: 138,
            column: 37
          }
        }, {
          start: {
            line: 139,
            column: 11
          },
          end: {
            line: 139,
            column: 61
          }
        }, {
          start: {
            line: 140,
            column: 11
          },
          end: {
            line: 140,
            column: 15
          }
        }],
        line: 138
      },
      "2": {
        loc: {
          start: {
            line: 149,
            column: 6
          },
          end: {
            line: 154,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 6
          },
          end: {
            line: 154,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "3": {
        loc: {
          start: {
            line: 157,
            column: 6
          },
          end: {
            line: 161,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 6
          },
          end: {
            line: 161,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 157
      },
      "4": {
        loc: {
          start: {
            line: 201,
            column: 6
          },
          end: {
            line: 203,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 201,
            column: 6
          },
          end: {
            line: 203,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 201
      },
      "5": {
        loc: {
          start: {
            line: 246,
            column: 25
          },
          end: {
            line: 246,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 246,
            column: 50
          },
          end: {
            line: 246,
            column: 63
          }
        }, {
          start: {
            line: 246,
            column: 66
          },
          end: {
            line: 246,
            column: 81
          }
        }],
        line: 246
      },
      "6": {
        loc: {
          start: {
            line: 428,
            column: 17
          },
          end: {
            line: 428,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 428,
            column: 17
          },
          end: {
            line: 428,
            column: 77
          }
        }, {
          start: {
            line: 428,
            column: 81
          },
          end: {
            line: 428,
            column: 82
          }
        }],
        line: 428
      },
      "7": {
        loc: {
          start: {
            line: 499,
            column: 4
          },
          end: {
            line: 503,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 499,
            column: 4
          },
          end: {
            line: 503,
            column: 5
          }
        }, {
          start: {
            line: 501,
            column: 11
          },
          end: {
            line: 503,
            column: 5
          }
        }],
        line: 499
      },
      "8": {
        loc: {
          start: {
            line: 510,
            column: 4
          },
          end: {
            line: 512,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 510,
            column: 4
          },
          end: {
            line: 512,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 510
      },
      "9": {
        loc: {
          start: {
            line: 515,
            column: 4
          },
          end: {
            line: 517,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 515,
            column: 4
          },
          end: {
            line: 517,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 515
      },
      "10": {
        loc: {
          start: {
            line: 519,
            column: 4
          },
          end: {
            line: 522,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 519,
            column: 4
          },
          end: {
            line: 522,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 519
      },
      "11": {
        loc: {
          start: {
            line: 538,
            column: 4
          },
          end: {
            line: 540,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 538,
            column: 4
          },
          end: {
            line: 540,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 538
      },
      "12": {
        loc: {
          start: {
            line: 547,
            column: 4
          },
          end: {
            line: 551,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 548,
            column: 6
          },
          end: {
            line: 548,
            column: 28
          }
        }, {
          start: {
            line: 549,
            column: 6
          },
          end: {
            line: 549,
            column: 30
          }
        }, {
          start: {
            line: 550,
            column: 6
          },
          end: {
            line: 550,
            column: 27
          }
        }],
        line: 547
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0],
      "1": [0, 0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "2d836db71feb2e28b6c0a020e23e11d81740d2fd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_bxwhcoz4r = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bxwhcoz4r();
import { mediaPipeService } from "./MediaPipeService";
import { coachingAnalysisService } from "./CoachingAnalysisService";
import { performanceMonitor } from "../../../utils/performance";
var VideoProcessingService = function () {
  function VideoProcessingService() {
    _classCallCheck(this, VideoProcessingService);
    this.processingQueue = (cov_bxwhcoz4r().s[0]++, []);
    this.activeJobs = (cov_bxwhcoz4r().s[1]++, new Map());
    this.jobListeners = (cov_bxwhcoz4r().s[2]++, new Map());
    this.config = (cov_bxwhcoz4r().s[3]++, {
      maxConcurrentJobs: 2,
      frameExtractionRate: 2,
      segmentationThreshold: 0.7,
      minSegmentDuration: 5,
      maxSegmentDuration: 30,
      qualityThreshold: 0.6,
      enableAutoSegmentation: true,
      enableRealTimeProcessing: false
    });
  }
  return _createClass(VideoProcessingService, [{
    key: "submitVideoForProcessing",
    value: (function () {
      var _submitVideoForProcessing = _asyncToGenerator(function* (videoUrl, matchId, userId, playerProfile, matchContext) {
        var priority = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : (cov_bxwhcoz4r().b[0][0]++, 'medium');
        cov_bxwhcoz4r().f[0]++;
        cov_bxwhcoz4r().s[4]++;
        try {
          cov_bxwhcoz4r().s[5]++;
          performanceMonitor.start('video_submission');
          var job = (cov_bxwhcoz4r().s[6]++, {
            id: this.generateJobId(),
            videoUrl: videoUrl,
            matchId: matchId,
            userId: userId,
            status: 'queued',
            progress: 0,
            segments: [],
            playerProfile: playerProfile,
            matchContext: matchContext,
            priority: priority,
            createdAt: new Date().toISOString()
          });
          cov_bxwhcoz4r().s[7]++;
          job.estimatedDuration = yield this.estimateProcessingDuration(videoUrl);
          cov_bxwhcoz4r().s[8]++;
          this.addToQueue(job);
          cov_bxwhcoz4r().s[9]++;
          this.processQueue();
          cov_bxwhcoz4r().s[10]++;
          performanceMonitor.end('video_submission');
          cov_bxwhcoz4r().s[11]++;
          return job.id;
        } catch (error) {
          cov_bxwhcoz4r().s[12]++;
          console.error('Failed to submit video for processing:', error);
          cov_bxwhcoz4r().s[13]++;
          throw new Error('Video submission failed');
        }
      });
      function submitVideoForProcessing(_x, _x2, _x3, _x4, _x5) {
        return _submitVideoForProcessing.apply(this, arguments);
      }
      return submitVideoForProcessing;
    }())
  }, {
    key: "getJobStatus",
    value: function getJobStatus(jobId) {
      cov_bxwhcoz4r().f[1]++;
      cov_bxwhcoz4r().s[14]++;
      return (cov_bxwhcoz4r().b[1][0]++, this.activeJobs.get(jobId)) || (cov_bxwhcoz4r().b[1][1]++, this.processingQueue.find(function (job) {
        cov_bxwhcoz4r().f[2]++;
        cov_bxwhcoz4r().s[15]++;
        return job.id === jobId;
      })) || (cov_bxwhcoz4r().b[1][2]++, null);
    }
  }, {
    key: "cancelJob",
    value: (function () {
      var _cancelJob = _asyncToGenerator(function* (jobId) {
        cov_bxwhcoz4r().f[3]++;
        cov_bxwhcoz4r().s[16]++;
        try {
          var job = (cov_bxwhcoz4r().s[17]++, this.activeJobs.get(jobId));
          cov_bxwhcoz4r().s[18]++;
          if (job) {
            cov_bxwhcoz4r().b[2][0]++;
            cov_bxwhcoz4r().s[19]++;
            job.status = 'cancelled';
            cov_bxwhcoz4r().s[20]++;
            this.activeJobs.delete(jobId);
            cov_bxwhcoz4r().s[21]++;
            this.notifyJobUpdate(job);
            cov_bxwhcoz4r().s[22]++;
            return true;
          } else {
            cov_bxwhcoz4r().b[2][1]++;
          }
          var queueIndex = (cov_bxwhcoz4r().s[23]++, this.processingQueue.findIndex(function (j) {
            cov_bxwhcoz4r().f[4]++;
            cov_bxwhcoz4r().s[24]++;
            return j.id === jobId;
          }));
          cov_bxwhcoz4r().s[25]++;
          if (queueIndex !== -1) {
            cov_bxwhcoz4r().b[3][0]++;
            cov_bxwhcoz4r().s[26]++;
            this.processingQueue[queueIndex].status = 'cancelled';
            cov_bxwhcoz4r().s[27]++;
            this.processingQueue.splice(queueIndex, 1);
            cov_bxwhcoz4r().s[28]++;
            return true;
          } else {
            cov_bxwhcoz4r().b[3][1]++;
          }
          cov_bxwhcoz4r().s[29]++;
          return false;
        } catch (error) {
          cov_bxwhcoz4r().s[30]++;
          console.error('Failed to cancel job:', error);
          cov_bxwhcoz4r().s[31]++;
          return false;
        }
      });
      function cancelJob(_x6) {
        return _cancelJob.apply(this, arguments);
      }
      return cancelJob;
    }())
  }, {
    key: "addJobListener",
    value: function addJobListener(jobId, listener) {
      cov_bxwhcoz4r().f[5]++;
      cov_bxwhcoz4r().s[32]++;
      this.jobListeners.set(jobId, listener);
    }
  }, {
    key: "removeJobListener",
    value: function removeJobListener(jobId) {
      cov_bxwhcoz4r().f[6]++;
      cov_bxwhcoz4r().s[33]++;
      this.jobListeners.delete(jobId);
    }
  }, {
    key: "processJob",
    value: (function () {
      var _processJob = _asyncToGenerator(function* (job) {
        cov_bxwhcoz4r().f[7]++;
        cov_bxwhcoz4r().s[34]++;
        try {
          cov_bxwhcoz4r().s[35]++;
          performanceMonitor.start(`video_processing_${job.id}`);
          cov_bxwhcoz4r().s[36]++;
          job.status = 'processing';
          cov_bxwhcoz4r().s[37]++;
          job.startedAt = new Date().toISOString();
          cov_bxwhcoz4r().s[38]++;
          job.progress = 0;
          cov_bxwhcoz4r().s[39]++;
          this.notifyJobUpdate(job);
          cov_bxwhcoz4r().s[40]++;
          job.progress = 10;
          cov_bxwhcoz4r().s[41]++;
          this.notifyJobUpdate(job);
          var qualityMetrics = (cov_bxwhcoz4r().s[42]++, yield this.assessVideoQuality(job.videoUrl));
          cov_bxwhcoz4r().s[43]++;
          if (qualityMetrics.videoQuality === 'poor') {
            cov_bxwhcoz4r().b[4][0]++;
            cov_bxwhcoz4r().s[44]++;
            throw new Error('Video quality too low for reliable analysis');
          } else {
            cov_bxwhcoz4r().b[4][1]++;
          }
          cov_bxwhcoz4r().s[45]++;
          job.progress = 20;
          cov_bxwhcoz4r().s[46]++;
          this.notifyJobUpdate(job);
          var segments = (cov_bxwhcoz4r().s[47]++, yield this.segmentVideo(job.videoUrl, job.matchContext));
          cov_bxwhcoz4r().s[48]++;
          job.segments = segments;
          cov_bxwhcoz4r().s[49]++;
          job.progress = 30;
          cov_bxwhcoz4r().s[50]++;
          this.notifyJobUpdate(job);
          var segmentAnalyses = (cov_bxwhcoz4r().s[51]++, yield this.processSegments(job, segments));
          cov_bxwhcoz4r().s[52]++;
          job.progress = 80;
          cov_bxwhcoz4r().s[53]++;
          this.notifyJobUpdate(job);
          var overallAnalysis = (cov_bxwhcoz4r().s[54]++, yield this.generateOverallAnalysis(segmentAnalyses, job.playerProfile, job.matchContext));
          cov_bxwhcoz4r().s[55]++;
          job.progress = 95;
          cov_bxwhcoz4r().s[56]++;
          this.notifyJobUpdate(job);
          var result = (cov_bxwhcoz4r().s[57]++, yield this.compileResults(job, segmentAnalyses, overallAnalysis, qualityMetrics));
          cov_bxwhcoz4r().s[58]++;
          job.status = 'completed';
          cov_bxwhcoz4r().s[59]++;
          job.progress = 100;
          cov_bxwhcoz4r().s[60]++;
          job.completedAt = new Date().toISOString();
          cov_bxwhcoz4r().s[61]++;
          this.notifyJobUpdate(job);
          cov_bxwhcoz4r().s[62]++;
          performanceMonitor.end(`video_processing_${job.id}`);
          cov_bxwhcoz4r().s[63]++;
          return result;
        } catch (error) {
          cov_bxwhcoz4r().s[64]++;
          console.error(`Job ${job.id} failed:`, error);
          cov_bxwhcoz4r().s[65]++;
          job.status = 'failed';
          cov_bxwhcoz4r().s[66]++;
          job.errorMessage = error instanceof Error ? (cov_bxwhcoz4r().b[5][0]++, error.message) : (cov_bxwhcoz4r().b[5][1]++, 'Unknown error');
          cov_bxwhcoz4r().s[67]++;
          this.notifyJobUpdate(job);
          cov_bxwhcoz4r().s[68]++;
          throw error;
        } finally {
          cov_bxwhcoz4r().s[69]++;
          this.activeJobs.delete(job.id);
          cov_bxwhcoz4r().s[70]++;
          this.processQueue();
        }
      });
      function processJob(_x7) {
        return _processJob.apply(this, arguments);
      }
      return processJob;
    }())
  }, {
    key: "assessVideoQuality",
    value: (function () {
      var _assessVideoQuality = _asyncToGenerator(function* (videoUrl) {
        cov_bxwhcoz4r().f[8]++;
        cov_bxwhcoz4r().s[71]++;
        yield new Promise(function (resolve) {
          cov_bxwhcoz4r().f[9]++;
          cov_bxwhcoz4r().s[72]++;
          return setTimeout(resolve, 500);
        });
        cov_bxwhcoz4r().s[73]++;
        return {
          videoQuality: 'good',
          lightingConditions: 'good',
          cameraStability: 'excellent',
          playerVisibility: 'good'
        };
      });
      function assessVideoQuality(_x8) {
        return _assessVideoQuality.apply(this, arguments);
      }
      return assessVideoQuality;
    }())
  }, {
    key: "segmentVideo",
    value: (function () {
      var _segmentVideo = _asyncToGenerator(function* (videoUrl, matchContext) {
        cov_bxwhcoz4r().f[10]++;
        cov_bxwhcoz4r().s[74]++;
        yield new Promise(function (resolve) {
          cov_bxwhcoz4r().f[11]++;
          cov_bxwhcoz4r().s[75]++;
          return setTimeout(resolve, 1000);
        });
        var segments = (cov_bxwhcoz4r().s[76]++, [{
          id: 'segment_1',
          startTime: 0,
          endTime: 15,
          type: 'serve',
          description: 'Service games analysis',
          importance: 'high'
        }, {
          id: 'segment_2',
          startTime: 15,
          endTime: 45,
          type: 'rally',
          description: 'Baseline rally analysis',
          importance: 'high'
        }, {
          id: 'segment_3',
          startTime: 45,
          endTime: 60,
          type: 'point',
          description: 'Net play and volleys',
          importance: 'medium'
        }]);
        cov_bxwhcoz4r().s[77]++;
        return segments;
      });
      function segmentVideo(_x9, _x0) {
        return _segmentVideo.apply(this, arguments);
      }
      return segmentVideo;
    }())
  }, {
    key: "processSegments",
    value: (function () {
      var _processSegments = _asyncToGenerator(function* (job, segments) {
        var _this = this;
        cov_bxwhcoz4r().f[12]++;
        var results = (cov_bxwhcoz4r().s[78]++, []);
        var progressPerSegment = (cov_bxwhcoz4r().s[79]++, 50 / segments.length);
        cov_bxwhcoz4r().s[80]++;
        var _loop = function* _loop(i) {
          var segment = (cov_bxwhcoz4r().s[82]++, segments[i]);
          var startTime = (cov_bxwhcoz4r().s[83]++, Date.now());
          var frames = (cov_bxwhcoz4r().s[84]++, yield _this.extractFrames(job.videoUrl, segment));
          var movementAnalyses = (cov_bxwhcoz4r().s[85]++, yield mediaPipeService.processVideoFrames(frames, function (progress) {
            cov_bxwhcoz4r().f[13]++;
            var segmentProgress = (cov_bxwhcoz4r().s[86]++, i * progressPerSegment + progress * progressPerSegment);
            cov_bxwhcoz4r().s[87]++;
            job.progress = 30 + segmentProgress;
            cov_bxwhcoz4r().s[88]++;
            _this.notifyJobUpdate(job);
          }));
          var coachingAnalysis = (cov_bxwhcoz4r().s[89]++, yield coachingAnalysisService.generateCoachingAnalysis({
            movementAnalyses: movementAnalyses,
            playerProfile: job.playerProfile,
            matchContext: job.matchContext,
            videoSegment: {
              startTime: segment.startTime,
              endTime: segment.endTime,
              description: segment.description
            }
          }));
          var processingTime = (cov_bxwhcoz4r().s[90]++, Date.now() - startTime);
          cov_bxwhcoz4r().s[91]++;
          results.push({
            segment: segment,
            movementAnalyses: movementAnalyses,
            coachingAnalysis: coachingAnalysis,
            processingTime: processingTime
          });
        };
        for (var i = (cov_bxwhcoz4r().s[81]++, 0); i < segments.length; i++) {
          yield* _loop(i);
        }
        cov_bxwhcoz4r().s[92]++;
        return results;
      });
      function processSegments(_x1, _x10) {
        return _processSegments.apply(this, arguments);
      }
      return processSegments;
    }())
  }, {
    key: "extractFrames",
    value: (function () {
      var _extractFrames = _asyncToGenerator(function* (videoUrl, segment) {
        cov_bxwhcoz4r().f[14]++;
        cov_bxwhcoz4r().s[93]++;
        yield new Promise(function (resolve) {
          cov_bxwhcoz4r().f[15]++;
          cov_bxwhcoz4r().s[94]++;
          return setTimeout(resolve, 200);
        });
        var frameCount = (cov_bxwhcoz4r().s[95]++, Math.ceil((segment.endTime - segment.startTime) * this.config.frameExtractionRate));
        var frames = (cov_bxwhcoz4r().s[96]++, []);
        cov_bxwhcoz4r().s[97]++;
        for (var i = (cov_bxwhcoz4r().s[98]++, 0); i < frameCount; i++) {
          var canvas = (cov_bxwhcoz4r().s[99]++, document.createElement('canvas'));
          cov_bxwhcoz4r().s[100]++;
          canvas.width = 640;
          cov_bxwhcoz4r().s[101]++;
          canvas.height = 480;
          cov_bxwhcoz4r().s[102]++;
          frames.push(canvas);
        }
        cov_bxwhcoz4r().s[103]++;
        return frames;
      });
      function extractFrames(_x11, _x12) {
        return _extractFrames.apply(this, arguments);
      }
      return extractFrames;
    }())
  }, {
    key: "generateOverallAnalysis",
    value: (function () {
      var _generateOverallAnalysis = _asyncToGenerator(function* (segmentAnalyses, playerProfile, matchContext) {
        var _segmentAnalyses;
        cov_bxwhcoz4r().f[16]++;
        var allMovementAnalyses = (cov_bxwhcoz4r().s[104]++, segmentAnalyses.flatMap(function (sa) {
          cov_bxwhcoz4r().f[17]++;
          cov_bxwhcoz4r().s[105]++;
          return sa.movementAnalyses;
        }));
        cov_bxwhcoz4r().s[106]++;
        return yield coachingAnalysisService.generateCoachingAnalysis({
          movementAnalyses: allMovementAnalyses,
          playerProfile: playerProfile,
          matchContext: matchContext,
          videoSegment: {
            startTime: 0,
            endTime: (cov_bxwhcoz4r().b[6][0]++, (_segmentAnalyses = segmentAnalyses[segmentAnalyses.length - 1]) == null ? void 0 : _segmentAnalyses.segment.endTime) || (cov_bxwhcoz4r().b[6][1]++, 0),
            description: 'Complete match analysis'
          }
        });
      });
      function generateOverallAnalysis(_x13, _x14, _x15) {
        return _generateOverallAnalysis.apply(this, arguments);
      }
      return generateOverallAnalysis;
    }())
  }, {
    key: "compileResults",
    value: (function () {
      var _compileResults = _asyncToGenerator(function* (job, segmentAnalyses, overallAnalysis, qualityMetrics) {
        cov_bxwhcoz4r().f[18]++;
        var totalFrames = (cov_bxwhcoz4r().s[107]++, segmentAnalyses.reduce(function (sum, sa) {
          cov_bxwhcoz4r().f[19]++;
          cov_bxwhcoz4r().s[108]++;
          return sum + sa.movementAnalyses.length;
        }, 0));
        var totalProcessingTime = (cov_bxwhcoz4r().s[109]++, segmentAnalyses.reduce(function (sum, sa) {
          cov_bxwhcoz4r().f[20]++;
          cov_bxwhcoz4r().s[110]++;
          return sum + sa.processingTime;
        }, 0));
        var averageProcessingTime = (cov_bxwhcoz4r().s[111]++, totalProcessingTime / segmentAnalyses.length);
        var allAnalyses = (cov_bxwhcoz4r().s[112]++, segmentAnalyses.flatMap(function (sa) {
          cov_bxwhcoz4r().f[21]++;
          cov_bxwhcoz4r().s[113]++;
          return sa.movementAnalyses;
        }));
        var avgConfidence = (cov_bxwhcoz4r().s[114]++, allAnalyses.reduce(function (sum, a) {
          cov_bxwhcoz4r().f[22]++;
          cov_bxwhcoz4r().s[115]++;
          return sum + a.confidence;
        }, 0) / allAnalyses.length);
        cov_bxwhcoz4r().s[116]++;
        return {
          jobId: job.id,
          matchId: job.matchId,
          segments: segmentAnalyses,
          overallAnalysis: overallAnalysis,
          processingMetrics: {
            totalFrames: totalFrames,
            processedFrames: totalFrames,
            averageProcessingTime: averageProcessingTime,
            poseDetectionAccuracy: avgConfidence,
            totalProcessingTime: totalProcessingTime
          },
          qualityMetrics: qualityMetrics
        };
      });
      function compileResults(_x16, _x17, _x18, _x19) {
        return _compileResults.apply(this, arguments);
      }
      return compileResults;
    }())
  }, {
    key: "estimateProcessingDuration",
    value: (function () {
      var _estimateProcessingDuration = _asyncToGenerator(function* (videoUrl) {
        cov_bxwhcoz4r().f[23]++;
        cov_bxwhcoz4r().s[117]++;
        yield new Promise(function (resolve) {
          cov_bxwhcoz4r().f[24]++;
          cov_bxwhcoz4r().s[118]++;
          return setTimeout(resolve, 100);
        });
        var videoDurationMinutes = (cov_bxwhcoz4r().s[119]++, 5);
        var processingRatio = (cov_bxwhcoz4r().s[120]++, 0.3);
        cov_bxwhcoz4r().s[121]++;
        return videoDurationMinutes * 60 * processingRatio;
      });
      function estimateProcessingDuration(_x20) {
        return _estimateProcessingDuration.apply(this, arguments);
      }
      return estimateProcessingDuration;
    }())
  }, {
    key: "addToQueue",
    value: function addToQueue(job) {
      var _this2 = this;
      cov_bxwhcoz4r().f[25]++;
      var insertIndex = (cov_bxwhcoz4r().s[122]++, this.processingQueue.findIndex(function (queuedJob) {
        cov_bxwhcoz4r().f[26]++;
        cov_bxwhcoz4r().s[123]++;
        return _this2.getPriorityScore(queuedJob.priority) < _this2.getPriorityScore(job.priority);
      }));
      cov_bxwhcoz4r().s[124]++;
      if (insertIndex === -1) {
        cov_bxwhcoz4r().b[7][0]++;
        cov_bxwhcoz4r().s[125]++;
        this.processingQueue.push(job);
      } else {
        cov_bxwhcoz4r().b[7][1]++;
        cov_bxwhcoz4r().s[126]++;
        this.processingQueue.splice(insertIndex, 0, job);
      }
    }
  }, {
    key: "processQueue",
    value: (function () {
      var _processQueue = _asyncToGenerator(function* () {
        cov_bxwhcoz4r().f[27]++;
        cov_bxwhcoz4r().s[127]++;
        if (this.activeJobs.size >= this.config.maxConcurrentJobs) {
          cov_bxwhcoz4r().b[8][0]++;
          cov_bxwhcoz4r().s[128]++;
          return;
        } else {
          cov_bxwhcoz4r().b[8][1]++;
        }
        var nextJob = (cov_bxwhcoz4r().s[129]++, this.processingQueue.shift());
        cov_bxwhcoz4r().s[130]++;
        if (!nextJob) {
          cov_bxwhcoz4r().b[9][0]++;
          cov_bxwhcoz4r().s[131]++;
          return;
        } else {
          cov_bxwhcoz4r().b[9][1]++;
        }
        cov_bxwhcoz4r().s[132]++;
        if (nextJob.status === 'cancelled') {
          cov_bxwhcoz4r().b[10][0]++;
          cov_bxwhcoz4r().s[133]++;
          this.processQueue();
          cov_bxwhcoz4r().s[134]++;
          return;
        } else {
          cov_bxwhcoz4r().b[10][1]++;
        }
        cov_bxwhcoz4r().s[135]++;
        this.activeJobs.set(nextJob.id, nextJob);
        cov_bxwhcoz4r().s[136]++;
        try {
          cov_bxwhcoz4r().s[137]++;
          yield this.processJob(nextJob);
        } catch (error) {
          cov_bxwhcoz4r().s[138]++;
          console.error(`Failed to process job ${nextJob.id}:`, error);
        }
      });
      function processQueue() {
        return _processQueue.apply(this, arguments);
      }
      return processQueue;
    }())
  }, {
    key: "notifyJobUpdate",
    value: function notifyJobUpdate(job) {
      cov_bxwhcoz4r().f[28]++;
      var listener = (cov_bxwhcoz4r().s[139]++, this.jobListeners.get(job.id));
      cov_bxwhcoz4r().s[140]++;
      if (listener) {
        cov_bxwhcoz4r().b[11][0]++;
        cov_bxwhcoz4r().s[141]++;
        listener(job);
      } else {
        cov_bxwhcoz4r().b[11][1]++;
      }
    }
  }, {
    key: "getPriorityScore",
    value: function getPriorityScore(priority) {
      cov_bxwhcoz4r().f[29]++;
      cov_bxwhcoz4r().s[142]++;
      switch (priority) {
        case 'high':
          cov_bxwhcoz4r().b[12][0]++;
          cov_bxwhcoz4r().s[143]++;
          return 3;
        case 'medium':
          cov_bxwhcoz4r().b[12][1]++;
          cov_bxwhcoz4r().s[144]++;
          return 2;
        case 'low':
          cov_bxwhcoz4r().b[12][2]++;
          cov_bxwhcoz4r().s[145]++;
          return 1;
      }
    }
  }, {
    key: "generateJobId",
    value: function generateJobId() {
      cov_bxwhcoz4r().f[30]++;
      cov_bxwhcoz4r().s[146]++;
      return `job_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "updateConfig",
    value: function updateConfig(config) {
      cov_bxwhcoz4r().f[31]++;
      cov_bxwhcoz4r().s[147]++;
      this.config = Object.assign({}, this.config, config);
    }
  }, {
    key: "getConfig",
    value: function getConfig() {
      cov_bxwhcoz4r().f[32]++;
      cov_bxwhcoz4r().s[148]++;
      return Object.assign({}, this.config);
    }
  }, {
    key: "getQueueStatus",
    value: function getQueueStatus() {
      cov_bxwhcoz4r().f[33]++;
      cov_bxwhcoz4r().s[149]++;
      return {
        queueLength: this.processingQueue.length,
        activeJobs: this.activeJobs.size,
        totalCapacity: this.config.maxConcurrentJobs
      };
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      cov_bxwhcoz4r().f[34]++;
      cov_bxwhcoz4r().s[150]++;
      this.processingQueue.length = 0;
      cov_bxwhcoz4r().s[151]++;
      this.activeJobs.clear();
      cov_bxwhcoz4r().s[152]++;
      this.jobListeners.clear();
    }
  }]);
}();
export var videoProcessingService = (cov_bxwhcoz4r().s[153]++, new VideoProcessingService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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