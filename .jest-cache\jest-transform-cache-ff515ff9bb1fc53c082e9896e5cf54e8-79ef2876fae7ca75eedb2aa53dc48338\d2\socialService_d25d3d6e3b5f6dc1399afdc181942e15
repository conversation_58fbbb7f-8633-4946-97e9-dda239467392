d8c3c67e6cf48c2a53b262983b21dcb2
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_vub985s5j() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\socialService.ts";
  var hash = "7c244bc7f3c317d95be9da7c313f8bf1378e65e5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\socialService.ts",
    statementMap: {
      "0": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 132,
          column: 6
        }
      },
      "1": {
        start: {
          line: 121,
          column: 32
        },
        end: {
          line: 125,
          column: 19
        }
      },
      "2": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 127,
          column: 31
        }
      },
      "3": {
        start: {
          line: 127,
          column: 19
        },
        end: {
          line: 127,
          column: 31
        }
      },
      "4": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 128,
          column: 20
        }
      },
      "5": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 154,
          column: 6
        }
      },
      "6": {
        start: {
          line: 141,
          column: 26
        },
        end: {
          line: 147,
          column: 27
        }
      },
      "7": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 149,
          column: 31
        }
      },
      "8": {
        start: {
          line: 149,
          column: 19
        },
        end: {
          line: 149,
          column: 31
        }
      },
      "9": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 150,
          column: 15
        }
      },
      "10": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 155,
          column: 11
        }
      },
      "11": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 193,
          column: 6
        }
      },
      "12": {
        start: {
          line: 164,
          column: 24
        },
        end: {
          line: 172,
          column: 9
        }
      },
      "13": {
        start: {
          line: 174,
          column: 32
        },
        end: {
          line: 178,
          column: 19
        }
      },
      "14": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 31
        }
      },
      "15": {
        start: {
          line: 180,
          column: 19
        },
        end: {
          line: 180,
          column: 31
        }
      },
      "16": {
        start: {
          line: 183,
          column: 8
        },
        end: {
          line: 187,
          column: 11
        }
      },
      "17": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 20
        }
      },
      "18": {
        start: {
          line: 200,
          column: 19
        },
        end: {
          line: 214,
          column: 5
        }
      },
      "19": {
        start: {
          line: 202,
          column: 32
        },
        end: {
          line: 207,
          column: 23
        }
      },
      "20": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 209,
          column: 31
        }
      },
      "21": {
        start: {
          line: 209,
          column: 19
        },
        end: {
          line: 209,
          column: 31
        }
      },
      "22": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 210,
          column: 26
        }
      },
      "23": {
        start: {
          line: 215,
          column: 4
        },
        end: {
          line: 215,
          column: 24
        }
      },
      "24": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 235,
          column: 6
        }
      },
      "25": {
        start: {
          line: 224,
          column: 26
        },
        end: {
          line: 228,
          column: 36
        }
      },
      "26": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 31
        }
      },
      "27": {
        start: {
          line: 230,
          column: 19
        },
        end: {
          line: 230,
          column: 31
        }
      },
      "28": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 15
        }
      },
      "29": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 236,
          column: 11
        }
      },
      "30": {
        start: {
          line: 243,
          column: 19
        },
        end: {
          line: 264,
          column: 5
        }
      },
      "31": {
        start: {
          line: 245,
          column: 30
        },
        end: {
          line: 251,
          column: 9
        }
      },
      "32": {
        start: {
          line: 253,
          column: 32
        },
        end: {
          line: 257,
          column: 19
        }
      },
      "33": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 259,
          column: 31
        }
      },
      "34": {
        start: {
          line: 259,
          column: 19
        },
        end: {
          line: 259,
          column: 31
        }
      },
      "35": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 260,
          column: 20
        }
      },
      "36": {
        start: {
          line: 265,
          column: 4
        },
        end: {
          line: 265,
          column: 26
        }
      },
      "37": {
        start: {
          line: 272,
          column: 19
        },
        end: {
          line: 307,
          column: 5
        }
      },
      "38": {
        start: {
          line: 275,
          column: 35
        },
        end: {
          line: 280,
          column: 19
        }
      },
      "39": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 284,
          column: 9
        }
      },
      "40": {
        start: {
          line: 283,
          column: 10
        },
        end: {
          line: 283,
          column: 69
        }
      },
      "41": {
        start: {
          line: 287,
          column: 44
        },
        end: {
          line: 293,
          column: 12
        }
      },
      "42": {
        start: {
          line: 295,
          column: 8
        },
        end: {
          line: 295,
          column: 53
        }
      },
      "43": {
        start: {
          line: 295,
          column: 30
        },
        end: {
          line: 295,
          column: 53
        }
      },
      "44": {
        start: {
          line: 298,
          column: 39
        },
        end: {
          line: 299,
          column: 81
        }
      },
      "45": {
        start: {
          line: 301,
          column: 8
        },
        end: {
          line: 301,
          column: 43
        }
      },
      "46": {
        start: {
          line: 301,
          column: 25
        },
        end: {
          line: 301,
          column: 43
        }
      },
      "47": {
        start: {
          line: 303,
          column: 8
        },
        end: {
          line: 303,
          column: 20
        }
      },
      "48": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 308,
          column: 27
        }
      },
      "49": {
        start: {
          line: 315,
          column: 19
        },
        end: {
          line: 338,
          column: 5
        }
      },
      "50": {
        start: {
          line: 317,
          column: 22
        },
        end: {
          line: 323,
          column: 9
        }
      },
      "51": {
        start: {
          line: 325,
          column: 26
        },
        end: {
          line: 327,
          column: 24
        }
      },
      "52": {
        start: {
          line: 329,
          column: 8
        },
        end: {
          line: 329,
          column: 31
        }
      },
      "53": {
        start: {
          line: 329,
          column: 19
        },
        end: {
          line: 329,
          column: 31
        }
      },
      "54": {
        start: {
          line: 332,
          column: 8
        },
        end: {
          line: 332,
          column: 59
        }
      },
      "55": {
        start: {
          line: 334,
          column: 8
        },
        end: {
          line: 334,
          column: 20
        }
      },
      "56": {
        start: {
          line: 339,
          column: 4
        },
        end: {
          line: 339,
          column: 27
        }
      },
      "57": {
        start: {
          line: 346,
          column: 19
        },
        end: {
          line: 361,
          column: 5
        }
      },
      "58": {
        start: {
          line: 348,
          column: 32
        },
        end: {
          line: 354,
          column: 23
        }
      },
      "59": {
        start: {
          line: 356,
          column: 8
        },
        end: {
          line: 356,
          column: 31
        }
      },
      "60": {
        start: {
          line: 356,
          column: 19
        },
        end: {
          line: 356,
          column: 31
        }
      },
      "61": {
        start: {
          line: 357,
          column: 8
        },
        end: {
          line: 357,
          column: 26
        }
      },
      "62": {
        start: {
          line: 362,
          column: 4
        },
        end: {
          line: 362,
          column: 24
        }
      },
      "63": {
        start: {
          line: 369,
          column: 19
        },
        end: {
          line: 391,
          column: 5
        }
      },
      "64": {
        start: {
          line: 371,
          column: 26
        },
        end: {
          line: 378,
          column: 12
        }
      },
      "65": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 380,
          column: 31
        }
      },
      "66": {
        start: {
          line: 380,
          column: 19
        },
        end: {
          line: 380,
          column: 31
        }
      },
      "67": {
        start: {
          line: 383,
          column: 8
        },
        end: {
          line: 385,
          column: 11
        }
      },
      "68": {
        start: {
          line: 387,
          column: 8
        },
        end: {
          line: 387,
          column: 20
        }
      },
      "69": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 392,
          column: 27
        }
      },
      "70": {
        start: {
          line: 399,
          column: 19
        },
        end: {
          line: 414,
          column: 5
        }
      },
      "71": {
        start: {
          line: 401,
          column: 26
        },
        end: {
          line: 407,
          column: 33
        }
      },
      "72": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 31
        }
      },
      "73": {
        start: {
          line: 409,
          column: 19
        },
        end: {
          line: 409,
          column: 31
        }
      },
      "74": {
        start: {
          line: 410,
          column: 8
        },
        end: {
          line: 410,
          column: 20
        }
      },
      "75": {
        start: {
          line: 415,
          column: 4
        },
        end: {
          line: 415,
          column: 27
        }
      },
      "76": {
        start: {
          line: 422,
          column: 19
        },
        end: {
          line: 437,
          column: 5
        }
      },
      "77": {
        start: {
          line: 424,
          column: 32
        },
        end: {
          line: 430,
          column: 35
        }
      },
      "78": {
        start: {
          line: 432,
          column: 8
        },
        end: {
          line: 432,
          column: 31
        }
      },
      "79": {
        start: {
          line: 432,
          column: 19
        },
        end: {
          line: 432,
          column: 31
        }
      },
      "80": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 433,
          column: 59
        }
      },
      "81": {
        start: {
          line: 433,
          column: 40
        },
        end: {
          line: 433,
          column: 51
        }
      },
      "82": {
        start: {
          line: 438,
          column: 4
        },
        end: {
          line: 438,
          column: 24
        }
      },
      "83": {
        start: {
          line: 445,
          column: 19
        },
        end: {
          line: 459,
          column: 5
        }
      },
      "84": {
        start: {
          line: 447,
          column: 32
        },
        end: {
          line: 452,
          column: 23
        }
      },
      "85": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 454,
          column: 31
        }
      },
      "86": {
        start: {
          line: 454,
          column: 19
        },
        end: {
          line: 454,
          column: 31
        }
      },
      "87": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 455,
          column: 26
        }
      },
      "88": {
        start: {
          line: 460,
          column: 4
        },
        end: {
          line: 460,
          column: 24
        }
      },
      "89": {
        start: {
          line: 467,
          column: 4
        },
        end: {
          line: 477,
          column: 5
        }
      },
      "90": {
        start: {
          line: 468,
          column: 6
        },
        end: {
          line: 474,
          column: 11
        }
      },
      "91": {
        start: {
          line: 476,
          column: 6
        },
        end: {
          line: 476,
          column: 68
        }
      },
      "92": {
        start: {
          line: 484,
          column: 4
        },
        end: {
          line: 516,
          column: 5
        }
      },
      "93": {
        start: {
          line: 485,
          column: 32
        },
        end: {
          line: 496,
          column: 19
        }
      },
      "94": {
        start: {
          line: 498,
          column: 6
        },
        end: {
          line: 513,
          column: 7
        }
      },
      "95": {
        start: {
          line: 499,
          column: 28
        },
        end: {
          line: 507,
          column: 11
        }
      },
      "96": {
        start: {
          line: 499,
          column: 72
        },
        end: {
          line: 507,
          column: 9
        }
      },
      "97": {
        start: {
          line: 509,
          column: 8
        },
        end: {
          line: 512,
          column: 33
        }
      },
      "98": {
        start: {
          line: 515,
          column: 6
        },
        end: {
          line: 515,
          column: 70
        }
      },
      "99": {
        start: {
          line: 520,
          column: 29
        },
        end: {
          line: 520,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 118,
            column: 2
          },
          end: {
            line: 118,
            column: 3
          }
        },
        loc: {
          start: {
            line: 118,
            column: 67
          },
          end: {
            line: 133,
            column: 3
          }
        },
        line: 118
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 120,
            column: 6
          },
          end: {
            line: 120,
            column: 7
          }
        },
        loc: {
          start: {
            line: 120,
            column: 18
          },
          end: {
            line: 129,
            column: 7
          }
        },
        line: 120
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 138,
            column: 2
          },
          end: {
            line: 138,
            column: 3
          }
        },
        loc: {
          start: {
            line: 138,
            column: 77
          },
          end: {
            line: 156,
            column: 3
          }
        },
        line: 138
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 140,
            column: 6
          },
          end: {
            line: 140,
            column: 7
          }
        },
        loc: {
          start: {
            line: 140,
            column: 18
          },
          end: {
            line: 151,
            column: 7
          }
        },
        line: 140
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 161,
            column: 2
          },
          end: {
            line: 161,
            column: 3
          }
        },
        loc: {
          start: {
            line: 161,
            column: 148
          },
          end: {
            line: 194,
            column: 3
          }
        },
        line: 161
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 163,
            column: 6
          },
          end: {
            line: 163,
            column: 7
          }
        },
        loc: {
          start: {
            line: 163,
            column: 18
          },
          end: {
            line: 190,
            column: 7
          }
        },
        line: 163
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 199,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        },
        loc: {
          start: {
            line: 199,
            column: 90
          },
          end: {
            line: 216,
            column: 3
          }
        },
        line: 199
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 201,
            column: 6
          },
          end: {
            line: 201,
            column: 7
          }
        },
        loc: {
          start: {
            line: 201,
            column: 18
          },
          end: {
            line: 211,
            column: 7
          }
        },
        line: 201
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 221,
            column: 2
          },
          end: {
            line: 221,
            column: 3
          }
        },
        loc: {
          start: {
            line: 221,
            column: 76
          },
          end: {
            line: 237,
            column: 3
          }
        },
        line: 221
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 223,
            column: 6
          },
          end: {
            line: 223,
            column: 7
          }
        },
        loc: {
          start: {
            line: 223,
            column: 18
          },
          end: {
            line: 232,
            column: 7
          }
        },
        line: 223
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 242,
            column: 2
          },
          end: {
            line: 242,
            column: 3
          }
        },
        loc: {
          start: {
            line: 242,
            column: 131
          },
          end: {
            line: 266,
            column: 3
          }
        },
        line: 242
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 244,
            column: 6
          },
          end: {
            line: 244,
            column: 7
          }
        },
        loc: {
          start: {
            line: 244,
            column: 18
          },
          end: {
            line: 261,
            column: 7
          }
        },
        line: 244
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 271,
            column: 2
          },
          end: {
            line: 271,
            column: 3
          }
        },
        loc: {
          start: {
            line: 271,
            column: 77
          },
          end: {
            line: 309,
            column: 3
          }
        },
        line: 271
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 273,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        },
        loc: {
          start: {
            line: 273,
            column: 18
          },
          end: {
            line: 304,
            column: 7
          }
        },
        line: 273
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 314,
            column: 2
          },
          end: {
            line: 314,
            column: 3
          }
        },
        loc: {
          start: {
            line: 314,
            column: 138
          },
          end: {
            line: 340,
            column: 3
          }
        },
        line: 314
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 316,
            column: 6
          },
          end: {
            line: 316,
            column: 7
          }
        },
        loc: {
          start: {
            line: 316,
            column: 18
          },
          end: {
            line: 335,
            column: 7
          }
        },
        line: 316
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 345,
            column: 2
          },
          end: {
            line: 345,
            column: 3
          }
        },
        loc: {
          start: {
            line: 345,
            column: 62
          },
          end: {
            line: 363,
            column: 3
          }
        },
        line: 345
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 347,
            column: 6
          },
          end: {
            line: 347,
            column: 7
          }
        },
        loc: {
          start: {
            line: 347,
            column: 18
          },
          end: {
            line: 358,
            column: 7
          }
        },
        line: 347
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 368,
            column: 2
          },
          end: {
            line: 368,
            column: 3
          }
        },
        loc: {
          start: {
            line: 368,
            column: 78
          },
          end: {
            line: 393,
            column: 3
          }
        },
        line: 368
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 370,
            column: 6
          },
          end: {
            line: 370,
            column: 7
          }
        },
        loc: {
          start: {
            line: 370,
            column: 18
          },
          end: {
            line: 388,
            column: 7
          }
        },
        line: 370
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 398,
            column: 2
          },
          end: {
            line: 398,
            column: 3
          }
        },
        loc: {
          start: {
            line: 398,
            column: 68
          },
          end: {
            line: 416,
            column: 3
          }
        },
        line: 398
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 400,
            column: 6
          },
          end: {
            line: 400,
            column: 7
          }
        },
        loc: {
          start: {
            line: 400,
            column: 18
          },
          end: {
            line: 411,
            column: 7
          }
        },
        line: 400
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 421,
            column: 2
          },
          end: {
            line: 421,
            column: 3
          }
        },
        loc: {
          start: {
            line: 421,
            column: 58
          },
          end: {
            line: 439,
            column: 3
          }
        },
        line: 421
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 423,
            column: 6
          },
          end: {
            line: 423,
            column: 7
          }
        },
        loc: {
          start: {
            line: 423,
            column: 18
          },
          end: {
            line: 434,
            column: 7
          }
        },
        line: 423
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 433,
            column: 25
          },
          end: {
            line: 433,
            column: 26
          }
        },
        loc: {
          start: {
            line: 433,
            column: 40
          },
          end: {
            line: 433,
            column: 51
          }
        },
        line: 433
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 444,
            column: 2
          },
          end: {
            line: 444,
            column: 3
          }
        },
        loc: {
          start: {
            line: 444,
            column: 70
          },
          end: {
            line: 461,
            column: 3
          }
        },
        line: 444
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 446,
            column: 6
          },
          end: {
            line: 446,
            column: 7
          }
        },
        loc: {
          start: {
            line: 446,
            column: 18
          },
          end: {
            line: 456,
            column: 7
          }
        },
        line: 446
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 466,
            column: 2
          },
          end: {
            line: 466,
            column: 3
          }
        },
        loc: {
          start: {
            line: 466,
            column: 97
          },
          end: {
            line: 478,
            column: 3
          }
        },
        line: 466
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 483,
            column: 2
          },
          end: {
            line: 483,
            column: 3
          }
        },
        loc: {
          start: {
            line: 483,
            column: 79
          },
          end: {
            line: 517,
            column: 3
          }
        },
        line: 483
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 499,
            column: 40
          },
          end: {
            line: 499,
            column: 41
          }
        },
        loc: {
          start: {
            line: 499,
            column: 72
          },
          end: {
            line: 507,
            column: 9
          }
        },
        line: 499
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 127,
            column: 8
          },
          end: {
            line: 127,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 8
          },
          end: {
            line: 127,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "1": {
        loc: {
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 149,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 8
          },
          end: {
            line: 149,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "2": {
        loc: {
          start: {
            line: 161,
            column: 75
          },
          end: {
            line: 161,
            column: 105
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 161,
            column: 99
          },
          end: {
            line: 161,
            column: 105
          }
        }],
        line: 161
      },
      "3": {
        loc: {
          start: {
            line: 180,
            column: 8
          },
          end: {
            line: 180,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 180,
            column: 8
          },
          end: {
            line: 180,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 180
      },
      "4": {
        loc: {
          start: {
            line: 199,
            column: 58
          },
          end: {
            line: 199,
            column: 68
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 199,
            column: 66
          },
          end: {
            line: 199,
            column: 68
          }
        }],
        line: 199
      },
      "5": {
        loc: {
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 209,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 209,
            column: 8
          },
          end: {
            line: 209,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 209
      },
      "6": {
        loc: {
          start: {
            line: 210,
            column: 15
          },
          end: {
            line: 210,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 210,
            column: 15
          },
          end: {
            line: 210,
            column: 19
          }
        }, {
          start: {
            line: 210,
            column: 23
          },
          end: {
            line: 210,
            column: 25
          }
        }],
        line: 210
      },
      "7": {
        loc: {
          start: {
            line: 215,
            column: 11
          },
          end: {
            line: 215,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 215,
            column: 11
          },
          end: {
            line: 215,
            column: 17
          }
        }, {
          start: {
            line: 215,
            column: 21
          },
          end: {
            line: 215,
            column: 23
          }
        }],
        line: 215
      },
      "8": {
        loc: {
          start: {
            line: 230,
            column: 8
          },
          end: {
            line: 230,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 8
          },
          end: {
            line: 230,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 230
      },
      "9": {
        loc: {
          start: {
            line: 259,
            column: 8
          },
          end: {
            line: 259,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 8
          },
          end: {
            line: 259,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 259
      },
      "10": {
        loc: {
          start: {
            line: 265,
            column: 11
          },
          end: {
            line: 265,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 265,
            column: 11
          },
          end: {
            line: 265,
            column: 17
          }
        }, {
          start: {
            line: 265,
            column: 21
          },
          end: {
            line: 265,
            column: 25
          }
        }],
        line: 265
      },
      "11": {
        loc: {
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 284,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 8
          },
          end: {
            line: 284,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "12": {
        loc: {
          start: {
            line: 295,
            column: 8
          },
          end: {
            line: 295,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 295,
            column: 8
          },
          end: {
            line: 295,
            column: 53
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 295
      },
      "13": {
        loc: {
          start: {
            line: 301,
            column: 8
          },
          end: {
            line: 301,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 301,
            column: 8
          },
          end: {
            line: 301,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 301
      },
      "14": {
        loc: {
          start: {
            line: 308,
            column: 11
          },
          end: {
            line: 308,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 308,
            column: 11
          },
          end: {
            line: 308,
            column: 17
          }
        }, {
          start: {
            line: 308,
            column: 21
          },
          end: {
            line: 308,
            column: 26
          }
        }],
        line: 308
      },
      "15": {
        loc: {
          start: {
            line: 329,
            column: 8
          },
          end: {
            line: 329,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 329,
            column: 8
          },
          end: {
            line: 329,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 329
      },
      "16": {
        loc: {
          start: {
            line: 339,
            column: 11
          },
          end: {
            line: 339,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 339,
            column: 11
          },
          end: {
            line: 339,
            column: 17
          }
        }, {
          start: {
            line: 339,
            column: 21
          },
          end: {
            line: 339,
            column: 26
          }
        }],
        line: 339
      },
      "17": {
        loc: {
          start: {
            line: 345,
            column: 28
          },
          end: {
            line: 345,
            column: 38
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 345,
            column: 36
          },
          end: {
            line: 345,
            column: 38
          }
        }],
        line: 345
      },
      "18": {
        loc: {
          start: {
            line: 356,
            column: 8
          },
          end: {
            line: 356,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 356,
            column: 8
          },
          end: {
            line: 356,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 356
      },
      "19": {
        loc: {
          start: {
            line: 357,
            column: 15
          },
          end: {
            line: 357,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 357,
            column: 15
          },
          end: {
            line: 357,
            column: 19
          }
        }, {
          start: {
            line: 357,
            column: 23
          },
          end: {
            line: 357,
            column: 25
          }
        }],
        line: 357
      },
      "20": {
        loc: {
          start: {
            line: 362,
            column: 11
          },
          end: {
            line: 362,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 362,
            column: 11
          },
          end: {
            line: 362,
            column: 17
          }
        }, {
          start: {
            line: 362,
            column: 21
          },
          end: {
            line: 362,
            column: 23
          }
        }],
        line: 362
      },
      "21": {
        loc: {
          start: {
            line: 380,
            column: 8
          },
          end: {
            line: 380,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 380,
            column: 8
          },
          end: {
            line: 380,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 380
      },
      "22": {
        loc: {
          start: {
            line: 392,
            column: 11
          },
          end: {
            line: 392,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 392,
            column: 11
          },
          end: {
            line: 392,
            column: 17
          }
        }, {
          start: {
            line: 392,
            column: 21
          },
          end: {
            line: 392,
            column: 26
          }
        }],
        line: 392
      },
      "23": {
        loc: {
          start: {
            line: 409,
            column: 8
          },
          end: {
            line: 409,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 409,
            column: 8
          },
          end: {
            line: 409,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 409
      },
      "24": {
        loc: {
          start: {
            line: 415,
            column: 11
          },
          end: {
            line: 415,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 415,
            column: 11
          },
          end: {
            line: 415,
            column: 17
          }
        }, {
          start: {
            line: 415,
            column: 21
          },
          end: {
            line: 415,
            column: 26
          }
        }],
        line: 415
      },
      "25": {
        loc: {
          start: {
            line: 432,
            column: 8
          },
          end: {
            line: 432,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 432,
            column: 8
          },
          end: {
            line: 432,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 432
      },
      "26": {
        loc: {
          start: {
            line: 433,
            column: 15
          },
          end: {
            line: 433,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 433,
            column: 15
          },
          end: {
            line: 433,
            column: 52
          }
        }, {
          start: {
            line: 433,
            column: 56
          },
          end: {
            line: 433,
            column: 58
          }
        }],
        line: 433
      },
      "27": {
        loc: {
          start: {
            line: 438,
            column: 11
          },
          end: {
            line: 438,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 438,
            column: 11
          },
          end: {
            line: 438,
            column: 17
          }
        }, {
          start: {
            line: 438,
            column: 21
          },
          end: {
            line: 438,
            column: 23
          }
        }],
        line: 438
      },
      "28": {
        loc: {
          start: {
            line: 444,
            column: 35
          },
          end: {
            line: 444,
            column: 45
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 444,
            column: 43
          },
          end: {
            line: 444,
            column: 45
          }
        }],
        line: 444
      },
      "29": {
        loc: {
          start: {
            line: 454,
            column: 8
          },
          end: {
            line: 454,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 454,
            column: 8
          },
          end: {
            line: 454,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 454
      },
      "30": {
        loc: {
          start: {
            line: 455,
            column: 15
          },
          end: {
            line: 455,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 455,
            column: 15
          },
          end: {
            line: 455,
            column: 19
          }
        }, {
          start: {
            line: 455,
            column: 23
          },
          end: {
            line: 455,
            column: 25
          }
        }],
        line: 455
      },
      "31": {
        loc: {
          start: {
            line: 460,
            column: 11
          },
          end: {
            line: 460,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 460,
            column: 11
          },
          end: {
            line: 460,
            column: 17
          }
        }, {
          start: {
            line: 460,
            column: 21
          },
          end: {
            line: 460,
            column: 23
          }
        }],
        line: 460
      },
      "32": {
        loc: {
          start: {
            line: 498,
            column: 6
          },
          end: {
            line: 513,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 498,
            column: 6
          },
          end: {
            line: 513,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 498
      },
      "33": {
        loc: {
          start: {
            line: 501,
            column: 20
          },
          end: {
            line: 501,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 501,
            column: 20
          },
          end: {
            line: 501,
            column: 40
          }
        }, {
          start: {
            line: 501,
            column: 44
          },
          end: {
            line: 501,
            column: 53
          }
        }],
        line: 501
      },
      "34": {
        loc: {
          start: {
            line: 502,
            column: 18
          },
          end: {
            line: 502,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 502,
            column: 18
          },
          end: {
            line: 502,
            column: 36
          }
        }, {
          start: {
            line: 502,
            column: 40
          },
          end: {
            line: 502,
            column: 44
          }
        }],
        line: 502
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7c244bc7f3c317d95be9da7c313f8bf1378e65e5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_vub985s5j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_vub985s5j();
import { supabase } from "../lib/supabase";
import { withErrorHandling } from "../utils/errorHandler";
var SocialService = function () {
  function SocialService() {
    _classCallCheck(this, SocialService);
  }
  return _createClass(SocialService, [{
    key: "getUserProfile",
    value: (function () {
      var _getUserProfile = _asyncToGenerator(function* (userId) {
        cov_vub985s5j().f[0]++;
        cov_vub985s5j().s[0]++;
        return withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[1]++;
          var _ref2 = (cov_vub985s5j().s[1]++, yield supabase.from('social_profiles').select('*').eq('id', userId).single()),
            data = _ref2.data,
            error = _ref2.error;
          cov_vub985s5j().s[2]++;
          if (error) {
            cov_vub985s5j().b[0][0]++;
            cov_vub985s5j().s[3]++;
            throw error;
          } else {
            cov_vub985s5j().b[0][1]++;
          }
          cov_vub985s5j().s[4]++;
          return data;
        }), {
          service: 'Social',
          action: 'getUserProfile',
          userId: userId
        }, {
          showUserError: false
        });
      });
      function getUserProfile(_x) {
        return _getUserProfile.apply(this, arguments);
      }
      return getUserProfile;
    }())
  }, {
    key: "updateOnlineStatus",
    value: (function () {
      var _updateOnlineStatus = _asyncToGenerator(function* (userId, isOnline) {
        cov_vub985s5j().f[2]++;
        cov_vub985s5j().s[5]++;
        yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[3]++;
          var _ref4 = (cov_vub985s5j().s[6]++, yield supabase.from('social_profiles').update({
              is_online: isOnline,
              last_seen: new Date().toISOString()
            }).eq('id', userId)),
            error = _ref4.error;
          cov_vub985s5j().s[7]++;
          if (error) {
            cov_vub985s5j().b[1][0]++;
            cov_vub985s5j().s[8]++;
            throw error;
          } else {
            cov_vub985s5j().b[1][1]++;
          }
          cov_vub985s5j().s[9]++;
          return;
        }), {
          service: 'Social',
          action: 'updateOnlineStatus',
          userId: userId
        }, {
          showUserError: false
        });
        cov_vub985s5j().s[10]++;
        return;
      });
      function updateOnlineStatus(_x2, _x3) {
        return _updateOnlineStatus.apply(this, arguments);
      }
      return updateOnlineStatus;
    }())
  }, {
    key: "sendMessage",
    value: (function () {
      var _sendMessage = _asyncToGenerator(function* (senderId, receiverId, content) {
        var _this = this;
        var type = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : (cov_vub985s5j().b[2][0]++, 'text');
        var metadata = arguments.length > 4 ? arguments[4] : undefined;
        cov_vub985s5j().f[4]++;
        cov_vub985s5j().s[11]++;
        return withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[5]++;
          var message = (cov_vub985s5j().s[12]++, {
            sender_id: senderId,
            receiver_id: receiverId,
            content: content,
            type: type,
            metadata: metadata,
            timestamp: new Date().toISOString(),
            is_read: false
          });
          var _ref6 = (cov_vub985s5j().s[13]++, yield supabase.from('messages').insert(message).select().single()),
            data = _ref6.data,
            error = _ref6.error;
          cov_vub985s5j().s[14]++;
          if (error) {
            cov_vub985s5j().b[3][0]++;
            cov_vub985s5j().s[15]++;
            throw error;
          } else {
            cov_vub985s5j().b[3][1]++;
          }
          cov_vub985s5j().s[16]++;
          yield _this.sendRealtimeNotification(receiverId, 'new_message', {
            senderId: senderId,
            content: content.substring(0, 100),
            type: type
          });
          cov_vub985s5j().s[17]++;
          return data;
        }), {
          service: 'Social',
          action: 'sendMessage',
          senderId: senderId,
          receiverId: receiverId
        }, {
          showUserError: true
        });
      });
      function sendMessage(_x4, _x5, _x6) {
        return _sendMessage.apply(this, arguments);
      }
      return sendMessage;
    }())
  }, {
    key: "getConversation",
    value: (function () {
      var _getConversation = _asyncToGenerator(function* (userId1, userId2) {
        var _ref9;
        var limit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_vub985s5j().b[4][0]++, 50);
        cov_vub985s5j().f[6]++;
        var result = (cov_vub985s5j().s[18]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[7]++;
          var _ref8 = (cov_vub985s5j().s[19]++, yield supabase.from('messages').select('*').or(`and(sender_id.eq.${userId1},receiver_id.eq.${userId2}),and(sender_id.eq.${userId2},receiver_id.eq.${userId1})`).order('timestamp', {
              ascending: false
            }).limit(limit)),
            data = _ref8.data,
            error = _ref8.error;
          cov_vub985s5j().s[20]++;
          if (error) {
            cov_vub985s5j().b[5][0]++;
            cov_vub985s5j().s[21]++;
            throw error;
          } else {
            cov_vub985s5j().b[5][1]++;
          }
          cov_vub985s5j().s[22]++;
          return (cov_vub985s5j().b[6][0]++, data) || (cov_vub985s5j().b[6][1]++, []);
        }), {
          service: 'Social',
          action: 'getConversation',
          userId1: userId1,
          userId2: userId2
        }, {
          showUserError: false
        }));
        cov_vub985s5j().s[23]++;
        return (_ref9 = (cov_vub985s5j().b[7][0]++, result)) != null ? _ref9 : (cov_vub985s5j().b[7][1]++, []);
      });
      function getConversation(_x7, _x8) {
        return _getConversation.apply(this, arguments);
      }
      return getConversation;
    }())
  }, {
    key: "markMessagesAsRead",
    value: (function () {
      var _markMessagesAsRead = _asyncToGenerator(function* (userId, senderId) {
        cov_vub985s5j().f[8]++;
        cov_vub985s5j().s[24]++;
        yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[9]++;
          var _ref1 = (cov_vub985s5j().s[25]++, yield supabase.from('messages').update({
              is_read: true
            }).eq('receiver_id', userId).eq('sender_id', senderId)),
            error = _ref1.error;
          cov_vub985s5j().s[26]++;
          if (error) {
            cov_vub985s5j().b[8][0]++;
            cov_vub985s5j().s[27]++;
            throw error;
          } else {
            cov_vub985s5j().b[8][1]++;
          }
          cov_vub985s5j().s[28]++;
          return;
        }), {
          service: 'Social',
          action: 'markMessagesAsRead',
          userId: userId,
          senderId: senderId
        }, {
          showUserError: false
        });
        cov_vub985s5j().s[29]++;
        return;
      });
      function markMessagesAsRead(_x9, _x0) {
        return _markMessagesAsRead.apply(this, arguments);
      }
      return markMessagesAsRead;
    }())
  }, {
    key: "createChallenge",
    value: (function () {
      var _createChallenge = _asyncToGenerator(function* (challenge) {
        var _ref12;
        cov_vub985s5j().f[10]++;
        var result = (cov_vub985s5j().s[30]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[11]++;
          var challengeData = (cov_vub985s5j().s[31]++, Object.assign({}, challenge, {
            participants: 0,
            leaderboard: [],
            is_active: true,
            created_at: new Date().toISOString()
          }));
          var _ref11 = (cov_vub985s5j().s[32]++, yield supabase.from('challenges').insert(challengeData).select().single()),
            data = _ref11.data,
            error = _ref11.error;
          cov_vub985s5j().s[33]++;
          if (error) {
            cov_vub985s5j().b[9][0]++;
            cov_vub985s5j().s[34]++;
            throw error;
          } else {
            cov_vub985s5j().b[9][1]++;
          }
          cov_vub985s5j().s[35]++;
          return data;
        }), {
          service: 'Social',
          action: 'createChallenge'
        }, {
          showUserError: true
        }));
        cov_vub985s5j().s[36]++;
        return (_ref12 = (cov_vub985s5j().b[10][0]++, result)) != null ? _ref12 : (cov_vub985s5j().b[10][1]++, null);
      });
      function createChallenge(_x1) {
        return _createChallenge.apply(this, arguments);
      }
      return createChallenge;
    }())
  }, {
    key: "joinChallenge",
    value: (function () {
      var _joinChallenge = _asyncToGenerator(function* (challengeId, userId) {
        var _ref17;
        cov_vub985s5j().f[12]++;
        var result = (cov_vub985s5j().s[37]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[13]++;
          var _ref14 = (cov_vub985s5j().s[38]++, yield supabase.from('challenge_participants').select('id').eq('challenge_id', challengeId).eq('user_id', userId).single()),
            existing = _ref14.data;
          cov_vub985s5j().s[39]++;
          if (existing) {
            cov_vub985s5j().b[11][0]++;
            cov_vub985s5j().s[40]++;
            throw new Error('Already participating in this challenge');
          } else {
            cov_vub985s5j().b[11][1]++;
          }
          var _ref15 = (cov_vub985s5j().s[41]++, yield supabase.from('challenge_participants').insert({
              challenge_id: challengeId,
              user_id: userId,
              joined_at: new Date().toISOString()
            })),
            participantError = _ref15.error;
          cov_vub985s5j().s[42]++;
          if (participantError) {
            cov_vub985s5j().b[12][0]++;
            cov_vub985s5j().s[43]++;
            throw participantError;
          } else {
            cov_vub985s5j().b[12][1]++;
          }
          var _ref16 = (cov_vub985s5j().s[44]++, yield supabase.rpc('increment_challenge_participants', {
              challenge_id: challengeId
            })),
            updateError = _ref16.error;
          cov_vub985s5j().s[45]++;
          if (updateError) {
            cov_vub985s5j().b[13][0]++;
            cov_vub985s5j().s[46]++;
            throw updateError;
          } else {
            cov_vub985s5j().b[13][1]++;
          }
          cov_vub985s5j().s[47]++;
          return true;
        }), {
          service: 'Social',
          action: 'joinChallenge',
          challengeId: challengeId,
          userId: userId
        }, {
          showUserError: true
        }));
        cov_vub985s5j().s[48]++;
        return (_ref17 = (cov_vub985s5j().b[14][0]++, result)) != null ? _ref17 : (cov_vub985s5j().b[14][1]++, false);
      });
      function joinChallenge(_x10, _x11) {
        return _joinChallenge.apply(this, arguments);
      }
      return joinChallenge;
    }())
  }, {
    key: "submitChallengeEntry",
    value: (function () {
      var _submitChallengeEntry = _asyncToGenerator(function* (challengeId, userId, score, evidence) {
        var _this2 = this,
          _ref20;
        cov_vub985s5j().f[14]++;
        var result = (cov_vub985s5j().s[49]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[15]++;
          var entry = (cov_vub985s5j().s[50]++, {
            challenge_id: challengeId,
            user_id: userId,
            score: score,
            evidence: evidence,
            completed_at: new Date().toISOString()
          });
          var _ref19 = (cov_vub985s5j().s[51]++, yield supabase.from('challenge_entries').upsert(entry)),
            error = _ref19.error;
          cov_vub985s5j().s[52]++;
          if (error) {
            cov_vub985s5j().b[15][0]++;
            cov_vub985s5j().s[53]++;
            throw error;
          } else {
            cov_vub985s5j().b[15][1]++;
          }
          cov_vub985s5j().s[54]++;
          yield _this2.updateChallengeLeaderboard(challengeId);
          cov_vub985s5j().s[55]++;
          return true;
        }), {
          service: 'Social',
          action: 'submitChallengeEntry',
          challengeId: challengeId,
          userId: userId
        }, {
          showUserError: true
        }));
        cov_vub985s5j().s[56]++;
        return (_ref20 = (cov_vub985s5j().b[16][0]++, result)) != null ? _ref20 : (cov_vub985s5j().b[16][1]++, false);
      });
      function submitChallengeEntry(_x12, _x13, _x14, _x15) {
        return _submitChallengeEntry.apply(this, arguments);
      }
      return submitChallengeEntry;
    }())
  }, {
    key: "getActiveChallenges",
    value: (function () {
      var _getActiveChallenges = _asyncToGenerator(function* () {
        var _ref23;
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_vub985s5j().b[17][0]++, 20);
        cov_vub985s5j().f[16]++;
        var result = (cov_vub985s5j().s[57]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[17]++;
          var _ref22 = (cov_vub985s5j().s[58]++, yield supabase.from('challenges').select('*').eq('is_active', true).gte('end_date', new Date().toISOString()).order('created_at', {
              ascending: false
            }).limit(limit)),
            data = _ref22.data,
            error = _ref22.error;
          cov_vub985s5j().s[59]++;
          if (error) {
            cov_vub985s5j().b[18][0]++;
            cov_vub985s5j().s[60]++;
            throw error;
          } else {
            cov_vub985s5j().b[18][1]++;
          }
          cov_vub985s5j().s[61]++;
          return (cov_vub985s5j().b[19][0]++, data) || (cov_vub985s5j().b[19][1]++, []);
        }), {
          service: 'Social',
          action: 'getActiveChallenges'
        }, {
          showUserError: false
        }));
        cov_vub985s5j().s[62]++;
        return (_ref23 = (cov_vub985s5j().b[20][0]++, result)) != null ? _ref23 : (cov_vub985s5j().b[20][1]++, []);
      });
      function getActiveChallenges() {
        return _getActiveChallenges.apply(this, arguments);
      }
      return getActiveChallenges;
    }())
  }, {
    key: "sendFriendRequest",
    value: (function () {
      var _sendFriendRequest = _asyncToGenerator(function* (userId, friendId) {
        var _this3 = this,
          _ref26;
        cov_vub985s5j().f[18]++;
        var result = (cov_vub985s5j().s[63]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[19]++;
          var _ref25 = (cov_vub985s5j().s[64]++, yield supabase.from('friendships').insert({
              user_id: userId,
              friend_id: friendId,
              status: 'pending',
              created_at: new Date().toISOString()
            })),
            error = _ref25.error;
          cov_vub985s5j().s[65]++;
          if (error) {
            cov_vub985s5j().b[21][0]++;
            cov_vub985s5j().s[66]++;
            throw error;
          } else {
            cov_vub985s5j().b[21][1]++;
          }
          cov_vub985s5j().s[67]++;
          yield _this3.sendRealtimeNotification(friendId, 'friend_request', {
            fromUserId: userId
          });
          cov_vub985s5j().s[68]++;
          return true;
        }), {
          service: 'Social',
          action: 'sendFriendRequest',
          userId: userId,
          friendId: friendId
        }, {
          showUserError: true
        }));
        cov_vub985s5j().s[69]++;
        return (_ref26 = (cov_vub985s5j().b[22][0]++, result)) != null ? _ref26 : (cov_vub985s5j().b[22][1]++, false);
      });
      function sendFriendRequest(_x16, _x17) {
        return _sendFriendRequest.apply(this, arguments);
      }
      return sendFriendRequest;
    }())
  }, {
    key: "acceptFriendRequest",
    value: (function () {
      var _acceptFriendRequest = _asyncToGenerator(function* (friendshipId) {
        var _ref29;
        cov_vub985s5j().f[20]++;
        var result = (cov_vub985s5j().s[70]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[21]++;
          var _ref28 = (cov_vub985s5j().s[71]++, yield supabase.from('friendships').update({
              status: 'accepted',
              accepted_at: new Date().toISOString()
            }).eq('id', friendshipId)),
            error = _ref28.error;
          cov_vub985s5j().s[72]++;
          if (error) {
            cov_vub985s5j().b[23][0]++;
            cov_vub985s5j().s[73]++;
            throw error;
          } else {
            cov_vub985s5j().b[23][1]++;
          }
          cov_vub985s5j().s[74]++;
          return true;
        }), {
          service: 'Social',
          action: 'acceptFriendRequest',
          friendshipId: friendshipId
        }, {
          showUserError: true
        }));
        cov_vub985s5j().s[75]++;
        return (_ref29 = (cov_vub985s5j().b[24][0]++, result)) != null ? _ref29 : (cov_vub985s5j().b[24][1]++, false);
      });
      function acceptFriendRequest(_x18) {
        return _acceptFriendRequest.apply(this, arguments);
      }
      return acceptFriendRequest;
    }())
  }, {
    key: "getFriends",
    value: (function () {
      var _getFriends = _asyncToGenerator(function* (userId) {
        var _ref32;
        cov_vub985s5j().f[22]++;
        var result = (cov_vub985s5j().s[76]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[23]++;
          var _ref31 = (cov_vub985s5j().s[77]++, yield supabase.from('friendships').select(`
            friend:social_profiles!friendships_friend_id_fkey(*)
          `).eq('user_id', userId).eq('status', 'accepted')),
            data = _ref31.data,
            error = _ref31.error;
          cov_vub985s5j().s[78]++;
          if (error) {
            cov_vub985s5j().b[25][0]++;
            cov_vub985s5j().s[79]++;
            throw error;
          } else {
            cov_vub985s5j().b[25][1]++;
          }
          cov_vub985s5j().s[80]++;
          return (cov_vub985s5j().b[26][0]++, data == null ? void 0 : data.map(function (item) {
            cov_vub985s5j().f[24]++;
            cov_vub985s5j().s[81]++;
            return item.friend;
          })) || (cov_vub985s5j().b[26][1]++, []);
        }), {
          service: 'Social',
          action: 'getFriends',
          userId: userId
        }, {
          showUserError: false
        }));
        cov_vub985s5j().s[82]++;
        return (_ref32 = (cov_vub985s5j().b[27][0]++, result)) != null ? _ref32 : (cov_vub985s5j().b[27][1]++, []);
      });
      function getFriends(_x19) {
        return _getFriends.apply(this, arguments);
      }
      return getFriends;
    }())
  }, {
    key: "searchUsers",
    value: (function () {
      var _searchUsers = _asyncToGenerator(function* (query) {
        var _ref35;
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_vub985s5j().b[28][0]++, 20);
        cov_vub985s5j().f[25]++;
        var result = (cov_vub985s5j().s[83]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_vub985s5j().f[26]++;
          var _ref34 = (cov_vub985s5j().s[84]++, yield supabase.from('social_profiles').select('*').or(`username.ilike.%${query}%,full_name.ilike.%${query}%`).eq('visibility', 'public').limit(limit)),
            data = _ref34.data,
            error = _ref34.error;
          cov_vub985s5j().s[85]++;
          if (error) {
            cov_vub985s5j().b[29][0]++;
            cov_vub985s5j().s[86]++;
            throw error;
          } else {
            cov_vub985s5j().b[29][1]++;
          }
          cov_vub985s5j().s[87]++;
          return (cov_vub985s5j().b[30][0]++, data) || (cov_vub985s5j().b[30][1]++, []);
        }), {
          service: 'Social',
          action: 'searchUsers',
          query: query
        }, {
          showUserError: false
        }));
        cov_vub985s5j().s[88]++;
        return (_ref35 = (cov_vub985s5j().b[31][0]++, result)) != null ? _ref35 : (cov_vub985s5j().b[31][1]++, []);
      });
      function searchUsers(_x20) {
        return _searchUsers.apply(this, arguments);
      }
      return searchUsers;
    }())
  }, {
    key: "sendRealtimeNotification",
    value: (function () {
      var _sendRealtimeNotification = _asyncToGenerator(function* (userId, type, data) {
        cov_vub985s5j().f[27]++;
        cov_vub985s5j().s[89]++;
        try {
          cov_vub985s5j().s[90]++;
          yield supabase.channel(`user:${userId}`).send({
            type: 'broadcast',
            event: type,
            payload: data
          });
        } catch (error) {
          cov_vub985s5j().s[91]++;
          console.warn('Failed to send real-time notification:', error);
        }
      });
      function sendRealtimeNotification(_x21, _x22, _x23) {
        return _sendRealtimeNotification.apply(this, arguments);
      }
      return sendRealtimeNotification;
    }())
  }, {
    key: "updateChallengeLeaderboard",
    value: (function () {
      var _updateChallengeLeaderboard = _asyncToGenerator(function* (challengeId) {
        cov_vub985s5j().f[28]++;
        cov_vub985s5j().s[92]++;
        try {
          var _ref36 = (cov_vub985s5j().s[93]++, yield supabase.from('challenge_entries').select(`
          user_id,
          score,
          completed_at,
          evidence,
          user:social_profiles(username, avatar)
        `).eq('challenge_id', challengeId).order('score', {
              ascending: false
            }).limit(100)),
            entries = _ref36.data;
          cov_vub985s5j().s[94]++;
          if (entries) {
            cov_vub985s5j().b[32][0]++;
            var leaderboard = (cov_vub985s5j().s[95]++, entries.map(function (entry, index) {
              var _entry$user, _entry$user2;
              cov_vub985s5j().f[29]++;
              cov_vub985s5j().s[96]++;
              return {
                userId: entry.user_id,
                username: (cov_vub985s5j().b[33][0]++, (_entry$user = entry.user) == null ? void 0 : _entry$user.username) || (cov_vub985s5j().b[33][1]++, 'Unknown'),
                avatar: (cov_vub985s5j().b[34][0]++, (_entry$user2 = entry.user) == null ? void 0 : _entry$user2.avatar) || (cov_vub985s5j().b[34][1]++, null),
                score: entry.score,
                completedAt: entry.completed_at,
                rank: index + 1,
                evidence: entry.evidence
              };
            }));
            cov_vub985s5j().s[97]++;
            yield supabase.from('challenges').update({
              leaderboard: leaderboard
            }).eq('id', challengeId);
          } else {
            cov_vub985s5j().b[32][1]++;
          }
        } catch (error) {
          cov_vub985s5j().s[98]++;
          console.error('Failed to update challenge leaderboard:', error);
        }
      });
      function updateChallengeLeaderboard(_x24) {
        return _updateChallengeLeaderboard.apply(this, arguments);
      }
      return updateChallengeLeaderboard;
    }())
  }]);
}();
export var socialService = (cov_vub985s5j().s[99]++, new SocialService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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