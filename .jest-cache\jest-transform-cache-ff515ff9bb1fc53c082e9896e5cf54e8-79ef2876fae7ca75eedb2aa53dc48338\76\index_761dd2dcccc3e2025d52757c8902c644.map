{"version": 3, "names": ["exports", "__esModule", "default", "unmountComponentAtNode", "rootTag", "unmount", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = unmountComponentAtNode;\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction unmountComponentAtNode(rootTag) {\n  rootTag.unmount();\n  return true;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,sBAAsB;AAUxC,SAASA,sBAAsBA,CAACC,OAAO,EAAE;EACvCA,OAAO,CAACC,OAAO,CAAC,CAAC;EACjB,OAAO,IAAI;AACb;AACAC,MAAM,CAACN,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}