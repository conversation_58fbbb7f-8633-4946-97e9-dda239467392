{"version": 3, "names": ["useEffect", "useState", "useCallback", "realtimeService", "useAuth", "useRealtime", "cov_tl12vljt0", "f", "_ref", "s", "user", "_ref2", "_ref3", "_slicedToArray", "notifications", "setNotifications", "_ref4", "_ref5", "coachingMessages", "setCoachingMessages", "_ref6", "_ref7", "isConnected", "setIsConnected", "b", "notificationUnsubscribe", "coachingUnsubscribe", "subscribeToNotifications", "id", "notification", "prev", "concat", "_toConsumableArray", "subscribeToCoaching", "message", "sendCoachingMessage", "_ref8", "_asyncToGenerator", "type", "error", "console", "_x", "_x2", "apply", "arguments", "createNotification", "_ref9", "title", "_x3", "_x4", "_x5", "clearNotifications", "clearCoachingMessages", "useRealtimeSkillStats", "_ref0", "_ref1", "_ref10", "skillStats", "setSkillStats", "unsubscribe", "subscribeToSkillUpdates", "stats", "useRealtimeTrainingSessions", "_ref11", "_ref12", "_ref13", "newSessions", "setNewSessions", "subscribeToTrainingSessions", "session", "clearNewSessions"], "sources": ["useRealtime.ts"], "sourcesContent": ["import { useEffect, useState, useCallback } from 'react';\nimport { realtimeService, RealtimeNotification, RealtimeCoachingMessage } from '@/services/realtime';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface UseRealtimeReturn {\n  notifications: RealtimeNotification[];\n  coachingMessages: RealtimeCoachingMessage[];\n  isConnected: boolean;\n  sendCoachingMessage: (message: string, type: 'encouragement' | 'correction' | 'tip') => Promise<void>;\n  createNotification: (type: 'achievement' | 'tip' | 'reminder' | 'match_result', title: string, message: string) => Promise<void>;\n  clearNotifications: () => void;\n  clearCoachingMessages: () => void;\n}\n\nexport function useRealtime(): UseRealtimeReturn {\n  const { user } = useAuth();\n  const [notifications, setNotifications] = useState<RealtimeNotification[]>([]);\n  const [coachingMessages, setCoachingMessages] = useState<RealtimeCoachingMessage[]>([]);\n  const [isConnected, setIsConnected] = useState(false);\n\n  useEffect(() => {\n    if (!user) {\n      setIsConnected(false);\n      return;\n    }\n\n    let notificationUnsubscribe: (() => void) | undefined;\n    let coachingUnsubscribe: (() => void) | undefined;\n\n    // Subscribe to notifications\n    notificationUnsubscribe = realtimeService.subscribeToNotifications(\n      user.id,\n      (notification) => {\n        setNotifications(prev => [notification, ...prev]);\n      }\n    );\n\n    // Subscribe to coaching messages\n    coachingUnsubscribe = realtimeService.subscribeToCoaching(\n      user.id,\n      (message) => {\n        setCoachingMessages(prev => [message, ...prev]);\n      }\n    );\n\n    setIsConnected(true);\n\n    // Cleanup function\n    return () => {\n      if (notificationUnsubscribe) {\n        notificationUnsubscribe();\n      }\n      if (coachingUnsubscribe) {\n        coachingUnsubscribe();\n      }\n      setIsConnected(false);\n    };\n  }, [user]);\n\n  const sendCoachingMessage = useCallback(async (\n    message: string,\n    type: 'encouragement' | 'correction' | 'tip'\n  ) => {\n    if (!user) return;\n    \n    try {\n      await realtimeService.sendCoachingMessage(user.id, message, type);\n    } catch (error) {\n      console.error('Error sending coaching message:', error);\n    }\n  }, [user]);\n\n  const createNotification = useCallback(async (\n    type: 'achievement' | 'tip' | 'reminder' | 'match_result',\n    title: string,\n    message: string\n  ) => {\n    if (!user) return;\n    \n    try {\n      await realtimeService.createNotification(user.id, type, title, message);\n    } catch (error) {\n      console.error('Error creating notification:', error);\n    }\n  }, [user]);\n\n  const clearNotifications = useCallback(() => {\n    setNotifications([]);\n  }, []);\n\n  const clearCoachingMessages = useCallback(() => {\n    setCoachingMessages([]);\n  }, []);\n\n  return {\n    notifications,\n    coachingMessages,\n    isConnected,\n    sendCoachingMessage,\n    createNotification,\n    clearNotifications,\n    clearCoachingMessages,\n  };\n}\n\n// Hook for skill stats real-time updates\nexport function useRealtimeSkillStats() {\n  const { user } = useAuth();\n  const [skillStats, setSkillStats] = useState<any>(null);\n\n  useEffect(() => {\n    if (!user) return;\n\n    const unsubscribe = realtimeService.subscribeToSkillUpdates(\n      user.id,\n      (stats) => {\n        setSkillStats(stats);\n      }\n    );\n\n    return unsubscribe;\n  }, [user]);\n\n  return skillStats;\n}\n\n// Hook for training session real-time updates\nexport function useRealtimeTrainingSessions() {\n  const { user } = useAuth();\n  const [newSessions, setNewSessions] = useState<any[]>([]);\n\n  useEffect(() => {\n    if (!user) return;\n\n    const unsubscribe = realtimeService.subscribeToTrainingSessions(\n      user.id,\n      (session) => {\n        setNewSessions(prev => [session, ...prev]);\n      }\n    );\n\n    return unsubscribe;\n  }, [user]);\n\n  const clearNewSessions = useCallback(() => {\n    setNewSessions([]);\n  }, []);\n\n  return {\n    newSessions,\n    clearNewSessions,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,eAAe;AACxB,SAASC,OAAO;AAYhB,OAAO,SAASC,WAAWA,CAAA,EAAsB;EAAAC,aAAA,GAAAC,CAAA;EAC/C,IAAAC,IAAA,IAAAF,aAAA,GAAAG,CAAA,OAAiBL,OAAO,CAAC,CAAC;IAAlBM,IAAI,GAAAF,IAAA,CAAJE,IAAI;EACZ,IAAAC,KAAA,IAAAL,aAAA,GAAAG,CAAA,OAA0CR,QAAQ,CAAyB,EAAE,CAAC;IAAAW,KAAA,GAAAC,cAAA,CAAAF,KAAA;IAAvEG,aAAa,GAAAF,KAAA;IAAEG,gBAAgB,GAAAH,KAAA;EACtC,IAAAI,KAAA,IAAAV,aAAA,GAAAG,CAAA,OAAgDR,QAAQ,CAA4B,EAAE,CAAC;IAAAgB,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAAhFE,gBAAgB,GAAAD,KAAA;IAAEE,mBAAmB,GAAAF,KAAA;EAC5C,IAAAG,KAAA,IAAAd,aAAA,GAAAG,CAAA,OAAsCR,QAAQ,CAAC,KAAK,CAAC;IAAAoB,KAAA,GAAAR,cAAA,CAAAO,KAAA;IAA9CE,WAAW,GAAAD,KAAA;IAAEE,cAAc,GAAAF,KAAA;EAAoBf,aAAA,GAAAG,CAAA;EAEtDT,SAAS,CAAC,YAAM;IAAAM,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IACd,IAAI,CAACC,IAAI,EAAE;MAAAJ,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAG,CAAA;MACTc,cAAc,CAAC,KAAK,CAAC;MAACjB,aAAA,GAAAG,CAAA;MACtB;IACF,CAAC;MAAAH,aAAA,GAAAkB,CAAA;IAAA;IAED,IAAIC,uBAAiD;IACrD,IAAIC,mBAA6C;IAACpB,aAAA,GAAAG,CAAA;IAGlDgB,uBAAuB,GAAGtB,eAAe,CAACwB,wBAAwB,CAChEjB,IAAI,CAACkB,EAAE,EACP,UAACC,YAAY,EAAK;MAAAvB,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MAChBM,gBAAgB,CAAC,UAAAe,IAAI,EAAI;QAAAxB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAG,CAAA;QAAA,QAACoB,YAAY,EAAAE,MAAA,CAAAC,kBAAA,CAAKF,IAAI;MAAA,CAAC,CAAC;IACnD,CACF,CAAC;IAACxB,aAAA,GAAAG,CAAA;IAGFiB,mBAAmB,GAAGvB,eAAe,CAAC8B,mBAAmB,CACvDvB,IAAI,CAACkB,EAAE,EACP,UAACM,OAAO,EAAK;MAAA5B,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACXU,mBAAmB,CAAC,UAAAW,IAAI,EAAI;QAAAxB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAG,CAAA;QAAA,QAACyB,OAAO,EAAAH,MAAA,CAAAC,kBAAA,CAAKF,IAAI;MAAA,CAAC,CAAC;IACjD,CACF,CAAC;IAACxB,aAAA,GAAAG,CAAA;IAEFc,cAAc,CAAC,IAAI,CAAC;IAACjB,aAAA,GAAAG,CAAA;IAGrB,OAAO,YAAM;MAAAH,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACX,IAAIgB,uBAAuB,EAAE;QAAAnB,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAG,CAAA;QAC3BgB,uBAAuB,CAAC,CAAC;MAC3B,CAAC;QAAAnB,aAAA,GAAAkB,CAAA;MAAA;MAAAlB,aAAA,GAAAG,CAAA;MACD,IAAIiB,mBAAmB,EAAE;QAAApB,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAG,CAAA;QACvBiB,mBAAmB,CAAC,CAAC;MACvB,CAAC;QAAApB,aAAA,GAAAkB,CAAA;MAAA;MAAAlB,aAAA,GAAAG,CAAA;MACDc,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACb,IAAI,CAAC,CAAC;EAEV,IAAMyB,mBAAmB,IAAA7B,aAAA,GAAAG,CAAA,QAAGP,WAAW;IAAA,IAAAkC,KAAA,GAAAC,iBAAA,CAAC,WACtCH,OAAe,EACfI,IAA4C,EACzC;MAAAhC,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACH,IAAI,CAACC,IAAI,EAAE;QAAAJ,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAG,CAAA;QAAA;MAAM,CAAC;QAAAH,aAAA,GAAAkB,CAAA;MAAA;MAAAlB,aAAA,GAAAG,CAAA;MAElB,IAAI;QAAAH,aAAA,GAAAG,CAAA;QACF,MAAMN,eAAe,CAACgC,mBAAmB,CAACzB,IAAI,CAACkB,EAAE,EAAEM,OAAO,EAAEI,IAAI,CAAC;MACnE,CAAC,CAAC,OAAOC,KAAK,EAAE;QAAAjC,aAAA,GAAAG,CAAA;QACd+B,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACzD;IACF,CAAC;IAAA,iBAAAE,EAAA,EAAAC,GAAA;MAAA,OAAAN,KAAA,CAAAO,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAAClC,IAAI,CAAC,CAAC;EAEV,IAAMmC,kBAAkB,IAAAvC,aAAA,GAAAG,CAAA,QAAGP,WAAW;IAAA,IAAA4C,KAAA,GAAAT,iBAAA,CAAC,WACrCC,IAAyD,EACzDS,KAAa,EACbb,OAAe,EACZ;MAAA5B,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACH,IAAI,CAACC,IAAI,EAAE;QAAAJ,aAAA,GAAAkB,CAAA;QAAAlB,aAAA,GAAAG,CAAA;QAAA;MAAM,CAAC;QAAAH,aAAA,GAAAkB,CAAA;MAAA;MAAAlB,aAAA,GAAAG,CAAA;MAElB,IAAI;QAAAH,aAAA,GAAAG,CAAA;QACF,MAAMN,eAAe,CAAC0C,kBAAkB,CAACnC,IAAI,CAACkB,EAAE,EAAEU,IAAI,EAAES,KAAK,EAAEb,OAAO,CAAC;MACzE,CAAC,CAAC,OAAOK,KAAK,EAAE;QAAAjC,aAAA,GAAAG,CAAA;QACd+B,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACtD;IACF,CAAC;IAAA,iBAAAS,GAAA,EAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAJ,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAAClC,IAAI,CAAC,CAAC;EAEV,IAAMyC,kBAAkB,IAAA7C,aAAA,GAAAG,CAAA,QAAGP,WAAW,CAAC,YAAM;IAAAI,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IAC3CM,gBAAgB,CAAC,EAAE,CAAC;EACtB,CAAC,EAAE,EAAE,CAAC;EAEN,IAAMqC,qBAAqB,IAAA9C,aAAA,GAAAG,CAAA,QAAGP,WAAW,CAAC,YAAM;IAAAI,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IAC9CU,mBAAmB,CAAC,EAAE,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EAACb,aAAA,GAAAG,CAAA;EAEP,OAAO;IACLK,aAAa,EAAbA,aAAa;IACbI,gBAAgB,EAAhBA,gBAAgB;IAChBI,WAAW,EAAXA,WAAW;IACXa,mBAAmB,EAAnBA,mBAAmB;IACnBU,kBAAkB,EAAlBA,kBAAkB;IAClBM,kBAAkB,EAAlBA,kBAAkB;IAClBC,qBAAqB,EAArBA;EACF,CAAC;AACH;AAGA,OAAO,SAASC,qBAAqBA,CAAA,EAAG;EAAA/C,aAAA,GAAAC,CAAA;EACtC,IAAA+C,KAAA,IAAAhD,aAAA,GAAAG,CAAA,QAAiBL,OAAO,CAAC,CAAC;IAAlBM,IAAI,GAAA4C,KAAA,CAAJ5C,IAAI;EACZ,IAAA6C,KAAA,IAAAjD,aAAA,GAAAG,CAAA,QAAoCR,QAAQ,CAAM,IAAI,CAAC;IAAAuD,MAAA,GAAA3C,cAAA,CAAA0C,KAAA;IAAhDE,UAAU,GAAAD,MAAA;IAAEE,aAAa,GAAAF,MAAA;EAAwBlD,aAAA,GAAAG,CAAA;EAExDT,SAAS,CAAC,YAAM;IAAAM,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IACd,IAAI,CAACC,IAAI,EAAE;MAAAJ,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAG,CAAA;MAAA;IAAM,CAAC;MAAAH,aAAA,GAAAkB,CAAA;IAAA;IAElB,IAAMmC,WAAW,IAAArD,aAAA,GAAAG,CAAA,QAAGN,eAAe,CAACyD,uBAAuB,CACzDlD,IAAI,CAACkB,EAAE,EACP,UAACiC,KAAK,EAAK;MAAAvD,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACTiD,aAAa,CAACG,KAAK,CAAC;IACtB,CACF,CAAC;IAACvD,aAAA,GAAAG,CAAA;IAEF,OAAOkD,WAAW;EACpB,CAAC,EAAE,CAACjD,IAAI,CAAC,CAAC;EAACJ,aAAA,GAAAG,CAAA;EAEX,OAAOgD,UAAU;AACnB;AAGA,OAAO,SAASK,2BAA2BA,CAAA,EAAG;EAAAxD,aAAA,GAAAC,CAAA;EAC5C,IAAAwD,MAAA,IAAAzD,aAAA,GAAAG,CAAA,QAAiBL,OAAO,CAAC,CAAC;IAAlBM,IAAI,GAAAqD,MAAA,CAAJrD,IAAI;EACZ,IAAAsD,MAAA,IAAA1D,aAAA,GAAAG,CAAA,QAAsCR,QAAQ,CAAQ,EAAE,CAAC;IAAAgE,MAAA,GAAApD,cAAA,CAAAmD,MAAA;IAAlDE,WAAW,GAAAD,MAAA;IAAEE,cAAc,GAAAF,MAAA;EAAwB3D,aAAA,GAAAG,CAAA;EAE1DT,SAAS,CAAC,YAAM;IAAAM,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IACd,IAAI,CAACC,IAAI,EAAE;MAAAJ,aAAA,GAAAkB,CAAA;MAAAlB,aAAA,GAAAG,CAAA;MAAA;IAAM,CAAC;MAAAH,aAAA,GAAAkB,CAAA;IAAA;IAElB,IAAMmC,WAAW,IAAArD,aAAA,GAAAG,CAAA,QAAGN,eAAe,CAACiE,2BAA2B,CAC7D1D,IAAI,CAACkB,EAAE,EACP,UAACyC,OAAO,EAAK;MAAA/D,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAG,CAAA;MACX0D,cAAc,CAAC,UAAArC,IAAI,EAAI;QAAAxB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAG,CAAA;QAAA,QAAC4D,OAAO,EAAAtC,MAAA,CAAAC,kBAAA,CAAKF,IAAI;MAAA,CAAC,CAAC;IAC5C,CACF,CAAC;IAACxB,aAAA,GAAAG,CAAA;IAEF,OAAOkD,WAAW;EACpB,CAAC,EAAE,CAACjD,IAAI,CAAC,CAAC;EAEV,IAAM4D,gBAAgB,IAAAhE,aAAA,GAAAG,CAAA,QAAGP,WAAW,CAAC,YAAM;IAAAI,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IACzC0D,cAAc,CAAC,EAAE,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAAC7D,aAAA,GAAAG,CAAA;EAEP,OAAO;IACLyD,WAAW,EAAXA,WAAW;IACXI,gBAAgB,EAAhBA;EACF,CAAC;AACH", "ignoreList": []}