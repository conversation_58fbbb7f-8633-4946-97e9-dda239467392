{"version": 3, "names": ["Speech", "cov_xnrgkz6vn", "s", "speak", "_speak", "_asyncToGenerator", "text", "options", "f", "console", "log", "Promise", "resolve", "setTimeout", "_x", "_x2", "apply", "arguments", "stop", "_stop", "VoiceService", "_classCallCheck", "isListening", "isSpeaking", "recognitionCallbacks", "voiceCommands", "command", "action", "_createClass", "key", "value", "_initializeVoiceRecognition", "isAvailable", "checkVoiceRecognitionAvailability", "b", "error", "initializeVoiceRecognition", "_startListening", "callback", "push", "simulateVoiceRecognition", "startListening", "_x3", "_stopListening", "stopListening", "_speak2", "length", "undefined", "stopSpeaking", "speechOptions", "language", "pitch", "rate", "voice", "_x4", "_stopSpeaking", "processVoiceCommand", "transcript", "normalizedTranscript", "toLowerCase", "trim", "matchedCommand", "find", "cmd", "includes", "Object", "assign", "parameters", "extractCommandParameters", "partialMatch", "findPartialMatch", "getAvailableCommands", "map", "isCurrentlyListening", "isCurrentlySpeaking", "getCoachingPhrases", "encouragement", "corrections", "tips", "instructions", "_speakCoachingFeedback", "type", "customText", "phrases", "textToSpeak", "phrasesForType", "Math", "floor", "random", "speakCoachingFeedback", "_x5", "_x6", "_checkVoiceRecognitionAvailability", "_this", "mockCommands", "randomCommand", "result", "confidence", "isFinal", "for<PERSON>ach", "shotType", "split", "durationMatch", "match", "parseInt", "unit", "duration", "startsWith", "commandWords", "transcriptWords", "matchCount", "_loop", "word", "some", "tw", "ceil", "voiceService"], "sources": ["voiceService.ts"], "sourcesContent": ["// Voice Input Service for Speech Recognition and Text-to-Speech\n// Mock Speech implementation for demo mode\nconst Speech = {\n  speak: async (text: string, options?: any) => {\n    console.log(`🎤 Mock TTS: \"${text}\"`, options);\n    return new Promise(resolve => setTimeout(resolve, 1000));\n  },\n  stop: async () => {\n    console.log('🎤 Mock TTS: Stopped');\n    return Promise.resolve();\n  },\n};\n\nexport interface VoiceRecognitionResult {\n  transcript: string;\n  confidence: number;\n  isFinal: boolean;\n}\n\nexport interface VoiceCommand {\n  command: string;\n  action: string;\n  parameters?: any;\n}\n\nexport interface SpeechOptions {\n  language?: string;\n  pitch?: number;\n  rate?: number;\n  voice?: string;\n}\n\nclass VoiceService {\n  private isListening = false;\n  private isSpeaking = false;\n  private recognitionCallbacks: ((result: VoiceRecognitionResult) => void)[] = [];\n\n  // Tennis-specific voice commands\n  private voiceCommands: VoiceCommand[] = [\n    { command: 'start recording', action: 'START_RECORDING' },\n    { command: 'stop recording', action: 'STOP_RECORDING' },\n    { command: 'take photo', action: 'TAKE_PHOTO' },\n    { command: 'start training', action: 'START_TRAINING' },\n    { command: 'end session', action: 'END_SESSION' },\n    { command: 'show stats', action: 'SHOW_STATS' },\n    { command: 'give me a tip', action: 'GET_TIP' },\n    { command: 'analyze my serve', action: 'ANALYZE_SERVE' },\n    { command: 'analyze my forehand', action: 'ANALYZE_FOREHAND' },\n    { command: 'analyze my backhand', action: 'ANALYZE_BACKHAND' },\n    { command: 'start match', action: 'START_MATCH' },\n    { command: 'pause', action: 'PAUSE' },\n    { command: 'resume', action: 'RESUME' },\n    { command: 'save session', action: 'SAVE_SESSION' },\n    { command: 'go back', action: 'NAVIGATE_BACK' },\n    { command: 'go home', action: 'NAVIGATE_HOME' },\n  ];\n\n  /**\n   * Initialize voice recognition (mock implementation)\n   */\n  async initializeVoiceRecognition(): Promise<boolean> {\n    try {\n      // In a real implementation, you would initialize speech recognition\n      console.log('Initializing voice recognition...');\n      \n      // Check if speech recognition is available\n      const isAvailable = await this.checkVoiceRecognitionAvailability();\n      \n      if (isAvailable) {\n        console.log('Voice recognition initialized successfully');\n        return true;\n      } else {\n        console.log('Voice recognition not available on this device');\n        return false;\n      }\n    } catch (error) {\n      console.error('Error initializing voice recognition:', error);\n      return false;\n    }\n  }\n\n  /**\n   * Start listening for voice input\n   */\n  async startListening(callback: (result: VoiceRecognitionResult) => void): Promise<void> {\n    if (this.isListening) {\n      return;\n    }\n\n    try {\n      this.isListening = true;\n      this.recognitionCallbacks.push(callback);\n\n      // Mock voice recognition - in real implementation, use expo-speech or react-native-voice\n      console.log('Started listening for voice input...');\n      \n      // Simulate voice recognition with mock data\n      this.simulateVoiceRecognition();\n      \n    } catch (error) {\n      this.isListening = false;\n      console.error('Error starting voice recognition:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Stop listening for voice input\n   */\n  async stopListening(): Promise<void> {\n    if (!this.isListening) {\n      return;\n    }\n\n    try {\n      this.isListening = false;\n      this.recognitionCallbacks = [];\n      console.log('Stopped listening for voice input');\n    } catch (error) {\n      console.error('Error stopping voice recognition:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Speak text using text-to-speech\n   */\n  async speak(text: string, options: SpeechOptions = {}): Promise<void> {\n    if (this.isSpeaking) {\n      await this.stopSpeaking();\n    }\n\n    try {\n      this.isSpeaking = true;\n      \n      const speechOptions = {\n        language: options.language || 'en-US',\n        pitch: options.pitch || 1.0,\n        rate: options.rate || 1.0,\n        voice: options.voice,\n      };\n\n      await Speech.speak(text, speechOptions);\n      this.isSpeaking = false;\n    } catch (error) {\n      this.isSpeaking = false;\n      console.error('Error speaking text:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Stop current speech\n   */\n  async stopSpeaking(): Promise<void> {\n    try {\n      await Speech.stop();\n      this.isSpeaking = false;\n    } catch (error) {\n      console.error('Error stopping speech:', error);\n    }\n  }\n\n  /**\n   * Process voice command\n   */\n  processVoiceCommand(transcript: string): VoiceCommand | null {\n    const normalizedTranscript = transcript.toLowerCase().trim();\n    \n    // Find matching command\n    const matchedCommand = this.voiceCommands.find(cmd => \n      normalizedTranscript.includes(cmd.command.toLowerCase())\n    );\n\n    if (matchedCommand) {\n      return {\n        ...matchedCommand,\n        parameters: this.extractCommandParameters(normalizedTranscript, matchedCommand),\n      };\n    }\n\n    // Check for partial matches or similar commands\n    const partialMatch = this.findPartialMatch(normalizedTranscript);\n    if (partialMatch) {\n      return partialMatch;\n    }\n\n    return null;\n  }\n\n  /**\n   * Get available voice commands\n   */\n  getAvailableCommands(): string[] {\n    return this.voiceCommands.map(cmd => cmd.command);\n  }\n\n  /**\n   * Check if currently listening\n   */\n  isCurrentlyListening(): boolean {\n    return this.isListening;\n  }\n\n  /**\n   * Check if currently speaking\n   */\n  isCurrentlySpeaking(): boolean {\n    return this.isSpeaking;\n  }\n\n  /**\n   * Get tennis-specific coaching phrases\n   */\n  getCoachingPhrases(): { [key: string]: string[] } {\n    return {\n      encouragement: [\n        \"Great shot! Keep it up!\",\n        \"Excellent form on that forehand!\",\n        \"Perfect timing on that serve!\",\n        \"Your footwork is improving!\",\n        \"Nice consistency in that rally!\",\n      ],\n      corrections: [\n        \"Try to keep your eye on the ball longer\",\n        \"Remember to follow through completely\",\n        \"Focus on your split step timing\",\n        \"Keep your racquet head up on the volley\",\n        \"Bend your knees more for better balance\",\n      ],\n      tips: [\n        \"Remember to prepare early for each shot\",\n        \"Use your legs to generate power\",\n        \"Keep your head still during contact\",\n        \"Follow through across your body\",\n        \"Stay light on your feet between shots\",\n      ],\n      instructions: [\n        \"Let's work on your serve technique\",\n        \"Time for some forehand practice\",\n        \"Let's focus on net play today\",\n        \"Ready for some footwork drills?\",\n        \"Let's analyze your last rally\",\n      ],\n    };\n  }\n\n  /**\n   * Speak coaching feedback\n   */\n  async speakCoachingFeedback(type: 'encouragement' | 'correction' | 'tip' | 'instruction', customText?: string): Promise<void> {\n    const phrases = this.getCoachingPhrases();\n    \n    let textToSpeak: string;\n    \n    if (customText) {\n      textToSpeak = customText;\n    } else {\n      const phrasesForType = phrases[type] || phrases.encouragement;\n      textToSpeak = phrasesForType[Math.floor(Math.random() * phrasesForType.length)];\n    }\n\n    await this.speak(textToSpeak, {\n      language: 'en-US',\n      rate: 0.9, // Slightly slower for coaching\n      pitch: 1.1, // Slightly higher pitch for encouragement\n    });\n  }\n\n  // Private helper methods\n\n  private async checkVoiceRecognitionAvailability(): Promise<boolean> {\n    try {\n      // Check if the device supports speech recognition\n      // This is a mock implementation\n      return true;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  private simulateVoiceRecognition(): void {\n    // Simulate voice recognition results for demo purposes\n    const mockCommands = [\n      'start recording',\n      'stop recording',\n      'take photo',\n      'give me a tip',\n      'show stats',\n    ];\n\n    setTimeout(() => {\n      if (this.isListening && this.recognitionCallbacks.length > 0) {\n        const randomCommand = mockCommands[Math.floor(Math.random() * mockCommands.length)];\n        const result: VoiceRecognitionResult = {\n          transcript: randomCommand,\n          confidence: 0.85 + Math.random() * 0.15,\n          isFinal: true,\n        };\n\n        this.recognitionCallbacks.forEach(callback => callback(result));\n      }\n    }, 3000); // Simulate 3-second delay\n  }\n\n  private extractCommandParameters(transcript: string, command: VoiceCommand): any {\n    // Extract parameters from voice commands\n    const parameters: any = {};\n\n    switch (command.action) {\n      case 'ANALYZE_SERVE':\n      case 'ANALYZE_FOREHAND':\n      case 'ANALYZE_BACKHAND':\n        // Extract shot type from command\n        parameters.shotType = command.action.split('_')[1].toLowerCase();\n        break;\n      \n      case 'START_RECORDING':\n        // Check for duration mentions\n        const durationMatch = transcript.match(/(\\d+)\\s*(second|minute|min)/i);\n        if (durationMatch) {\n          const value = parseInt(durationMatch[1]);\n          const unit = durationMatch[2].toLowerCase();\n          parameters.duration = unit.startsWith('min') ? value * 60 : value;\n        }\n        break;\n    }\n\n    return parameters;\n  }\n\n  private findPartialMatch(transcript: string): VoiceCommand | null {\n    // Find commands with partial matches\n    for (const command of this.voiceCommands) {\n      const commandWords = command.command.split(' ');\n      const transcriptWords = transcript.split(' ');\n      \n      let matchCount = 0;\n      for (const word of commandWords) {\n        if (transcriptWords.some(tw => tw.includes(word) || word.includes(tw))) {\n          matchCount++;\n        }\n      }\n      \n      // If more than half the words match, consider it a partial match\n      if (matchCount >= Math.ceil(commandWords.length / 2)) {\n        return command;\n      }\n    }\n\n    return null;\n  }\n}\n\nexport const voiceService = new VoiceService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAMA,MAAM,IAAAC,aAAA,GAAAC,CAAA,OAAG;EACbC,KAAK;IAAA,IAAAC,MAAA,GAAAC,iBAAA,CAAE,WAAOC,IAAY,EAAEC,OAAa,EAAK;MAAAN,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAC5CO,OAAO,CAACC,GAAG,CAAC,iBAAiBJ,IAAI,GAAG,EAAEC,OAAO,CAAC;MAACN,aAAA,GAAAC,CAAA;MAC/C,OAAO,IAAIS,OAAO,CAAC,UAAAC,OAAO,EAAI;QAAAX,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QAAA,OAAAW,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;MAAD,CAAC,CAAC;IAC1D,CAAC;IAAA,SAHDT,KAAKA,CAAAW,EAAA,EAAAC,GAAA;MAAA,OAAAX,MAAA,CAAAY,KAAA,OAAAC,SAAA;IAAA;IAAA,OAALd,KAAK;EAAA,GAGJ;EACDe,IAAI;IAAA,IAAAC,KAAA,GAAAd,iBAAA,CAAE,aAAY;MAAAJ,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAChBO,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;MAACT,aAAA,GAAAC,CAAA;MACpC,OAAOS,OAAO,CAACC,OAAO,CAAC,CAAC;IAC1B,CAAC;IAAA,SAHDM,IAAIA,CAAA;MAAA,OAAAC,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;IAAA,OAAJC,IAAI;EAAA;AAIN,CAAC;AAAC,IAqBIE,YAAY;EAAA,SAAAA,aAAA;IAAAC,eAAA,OAAAD,YAAA;IAAA,KACRE,WAAW,IAAArB,aAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KACnBqB,UAAU,IAAAtB,aAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KAClBsB,oBAAoB,IAAAvB,aAAA,GAAAC,CAAA,OAAiD,EAAE;IAAA,KAGvEuB,aAAa,IAAAxB,aAAA,GAAAC,CAAA,OAAmB,CACtC;MAAEwB,OAAO,EAAE,iBAAiB;MAAEC,MAAM,EAAE;IAAkB,CAAC,EACzD;MAAED,OAAO,EAAE,gBAAgB;MAAEC,MAAM,EAAE;IAAiB,CAAC,EACvD;MAAED,OAAO,EAAE,YAAY;MAAEC,MAAM,EAAE;IAAa,CAAC,EAC/C;MAAED,OAAO,EAAE,gBAAgB;MAAEC,MAAM,EAAE;IAAiB,CAAC,EACvD;MAAED,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE;IAAc,CAAC,EACjD;MAAED,OAAO,EAAE,YAAY;MAAEC,MAAM,EAAE;IAAa,CAAC,EAC/C;MAAED,OAAO,EAAE,eAAe;MAAEC,MAAM,EAAE;IAAU,CAAC,EAC/C;MAAED,OAAO,EAAE,kBAAkB;MAAEC,MAAM,EAAE;IAAgB,CAAC,EACxD;MAAED,OAAO,EAAE,qBAAqB;MAAEC,MAAM,EAAE;IAAmB,CAAC,EAC9D;MAAED,OAAO,EAAE,qBAAqB;MAAEC,MAAM,EAAE;IAAmB,CAAC,EAC9D;MAAED,OAAO,EAAE,aAAa;MAAEC,MAAM,EAAE;IAAc,CAAC,EACjD;MAAED,OAAO,EAAE,OAAO;MAAEC,MAAM,EAAE;IAAQ,CAAC,EACrC;MAAED,OAAO,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAC,EACvC;MAAED,OAAO,EAAE,cAAc;MAAEC,MAAM,EAAE;IAAe,CAAC,EACnD;MAAED,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAgB,CAAC,EAC/C;MAAED,OAAO,EAAE,SAAS;MAAEC,MAAM,EAAE;IAAgB,CAAC,CAChD;EAAA;EAAA,OAAAC,YAAA,CAAAR,YAAA;IAAAS,GAAA;IAAAC,KAAA;MAAA,IAAAC,2BAAA,GAAA1B,iBAAA,CAKD,aAAqD;QAAAJ,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACnD,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAEFO,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;UAGhD,IAAMsB,WAAW,IAAA/B,aAAA,GAAAC,CAAA,cAAS,IAAI,CAAC+B,iCAAiC,CAAC,CAAC;UAAChC,aAAA,GAAAC,CAAA;UAEnE,IAAI8B,WAAW,EAAE;YAAA/B,aAAA,GAAAiC,CAAA;YAAAjC,aAAA,GAAAC,CAAA;YACfO,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;YAACT,aAAA,GAAAC,CAAA;YAC1D,OAAO,IAAI;UACb,CAAC,MAAM;YAAAD,aAAA,GAAAiC,CAAA;YAAAjC,aAAA,GAAAC,CAAA;YACLO,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAACT,aAAA,GAAAC,CAAA;YAC9D,OAAO,KAAK;UACd;QACF,CAAC,CAAC,OAAOiC,KAAK,EAAE;UAAAlC,aAAA,GAAAC,CAAA;UACdO,OAAO,CAAC0B,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAAClC,aAAA,GAAAC,CAAA;UAC9D,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SAnBKkC,0BAA0BA,CAAA;QAAA,OAAAL,2BAAA,CAAAf,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA1BmB,0BAA0B;IAAA;EAAA;IAAAP,GAAA;IAAAC,KAAA;MAAA,IAAAO,eAAA,GAAAhC,iBAAA,CAwBhC,WAAqBiC,QAAkD,EAAiB;QAAArC,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACtF,IAAI,IAAI,CAACoB,WAAW,EAAE;UAAArB,aAAA,GAAAiC,CAAA;UAAAjC,aAAA,GAAAC,CAAA;UACpB;QACF,CAAC;UAAAD,aAAA,GAAAiC,CAAA;QAAA;QAAAjC,aAAA,GAAAC,CAAA;QAED,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACF,IAAI,CAACoB,WAAW,GAAG,IAAI;UAACrB,aAAA,GAAAC,CAAA;UACxB,IAAI,CAACsB,oBAAoB,CAACe,IAAI,CAACD,QAAQ,CAAC;UAACrC,aAAA,GAAAC,CAAA;UAGzCO,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;UAACT,aAAA,GAAAC,CAAA;UAGpD,IAAI,CAACsC,wBAAwB,CAAC,CAAC;QAEjC,CAAC,CAAC,OAAOL,KAAK,EAAE;UAAAlC,aAAA,GAAAC,CAAA;UACd,IAAI,CAACoB,WAAW,GAAG,KAAK;UAACrB,aAAA,GAAAC,CAAA;UACzBO,OAAO,CAAC0B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UAAClC,aAAA,GAAAC,CAAA;UAC1D,MAAMiC,KAAK;QACb;MACF,CAAC;MAAA,SApBKM,cAAcA,CAAAC,GAAA;QAAA,OAAAL,eAAA,CAAArB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdwB,cAAc;IAAA;EAAA;IAAAZ,GAAA;IAAAC,KAAA;MAAA,IAAAa,cAAA,GAAAtC,iBAAA,CAyBpB,aAAqC;QAAAJ,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACnC,IAAI,CAAC,IAAI,CAACoB,WAAW,EAAE;UAAArB,aAAA,GAAAiC,CAAA;UAAAjC,aAAA,GAAAC,CAAA;UACrB;QACF,CAAC;UAAAD,aAAA,GAAAiC,CAAA;QAAA;QAAAjC,aAAA,GAAAC,CAAA;QAED,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACF,IAAI,CAACoB,WAAW,GAAG,KAAK;UAACrB,aAAA,GAAAC,CAAA;UACzB,IAAI,CAACsB,oBAAoB,GAAG,EAAE;UAACvB,aAAA,GAAAC,CAAA;UAC/BO,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAClD,CAAC,CAAC,OAAOyB,KAAK,EAAE;UAAAlC,aAAA,GAAAC,CAAA;UACdO,OAAO,CAAC0B,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UAAClC,aAAA,GAAAC,CAAA;UAC1D,MAAMiC,KAAK;QACb;MACF,CAAC;MAAA,SAbKS,aAAaA,CAAA;QAAA,OAAAD,cAAA,CAAA3B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAb2B,aAAa;IAAA;EAAA;IAAAf,GAAA;IAAAC,KAAA;MAAA,IAAAe,OAAA,GAAAxC,iBAAA,CAkBnB,WAAYC,IAAY,EAA8C;QAAA,IAA5CC,OAAsB,GAAAU,SAAA,CAAA6B,MAAA,QAAA7B,SAAA,QAAA8B,SAAA,GAAA9B,SAAA,OAAAhB,aAAA,GAAAiC,CAAA,UAAG,CAAC,CAAC;QAAAjC,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACnD,IAAI,IAAI,CAACqB,UAAU,EAAE;UAAAtB,aAAA,GAAAiC,CAAA;UAAAjC,aAAA,GAAAC,CAAA;UACnB,MAAM,IAAI,CAAC8C,YAAY,CAAC,CAAC;QAC3B,CAAC;UAAA/C,aAAA,GAAAiC,CAAA;QAAA;QAAAjC,aAAA,GAAAC,CAAA;QAED,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACF,IAAI,CAACqB,UAAU,GAAG,IAAI;UAEtB,IAAM0B,aAAa,IAAAhD,aAAA,GAAAC,CAAA,QAAG;YACpBgD,QAAQ,EAAE,CAAAjD,aAAA,GAAAiC,CAAA,UAAA3B,OAAO,CAAC2C,QAAQ,MAAAjD,aAAA,GAAAiC,CAAA,UAAI,OAAO;YACrCiB,KAAK,EAAE,CAAAlD,aAAA,GAAAiC,CAAA,UAAA3B,OAAO,CAAC4C,KAAK,MAAAlD,aAAA,GAAAiC,CAAA,UAAI,GAAG;YAC3BkB,IAAI,EAAE,CAAAnD,aAAA,GAAAiC,CAAA,UAAA3B,OAAO,CAAC6C,IAAI,MAAAnD,aAAA,GAAAiC,CAAA,UAAI,GAAG;YACzBmB,KAAK,EAAE9C,OAAO,CAAC8C;UACjB,CAAC;UAACpD,aAAA,GAAAC,CAAA;UAEF,MAAMF,MAAM,CAACG,KAAK,CAACG,IAAI,EAAE2C,aAAa,CAAC;UAAChD,aAAA,GAAAC,CAAA;UACxC,IAAI,CAACqB,UAAU,GAAG,KAAK;QACzB,CAAC,CAAC,OAAOY,KAAK,EAAE;UAAAlC,aAAA,GAAAC,CAAA;UACd,IAAI,CAACqB,UAAU,GAAG,KAAK;UAACtB,aAAA,GAAAC,CAAA;UACxBO,OAAO,CAAC0B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAAClC,aAAA,GAAAC,CAAA;UAC7C,MAAMiC,KAAK;QACb;MACF,CAAC;MAAA,SAtBKhC,KAAKA,CAAAmD,GAAA;QAAA,OAAAT,OAAA,CAAA7B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAALd,KAAK;IAAA;EAAA;IAAA0B,GAAA;IAAAC,KAAA;MAAA,IAAAyB,aAAA,GAAAlD,iBAAA,CA2BX,aAAoC;QAAAJ,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QAClC,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACF,MAAMF,MAAM,CAACkB,IAAI,CAAC,CAAC;UAACjB,aAAA,GAAAC,CAAA;UACpB,IAAI,CAACqB,UAAU,GAAG,KAAK;QACzB,CAAC,CAAC,OAAOY,KAAK,EAAE;UAAAlC,aAAA,GAAAC,CAAA;UACdO,OAAO,CAAC0B,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAChD;MACF,CAAC;MAAA,SAPKa,YAAYA,CAAA;QAAA,OAAAO,aAAA,CAAAvC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZ+B,YAAY;IAAA;EAAA;IAAAnB,GAAA;IAAAC,KAAA,EAYlB,SAAA0B,mBAAmBA,CAACC,UAAkB,EAAuB;MAAAxD,aAAA,GAAAO,CAAA;MAC3D,IAAMkD,oBAAoB,IAAAzD,aAAA,GAAAC,CAAA,QAAGuD,UAAU,CAACE,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;MAG5D,IAAMC,cAAc,IAAA5D,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACuB,aAAa,CAACqC,IAAI,CAAC,UAAAC,GAAG,EAChD;QAAA9D,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QAAA,OAAAwD,oBAAoB,CAACM,QAAQ,CAACD,GAAG,CAACrC,OAAO,CAACiC,WAAW,CAAC,CAAC,CAAC;MAAD,CACzD,CAAC;MAAC1D,aAAA,GAAAC,CAAA;MAEF,IAAI2D,cAAc,EAAE;QAAA5D,aAAA,GAAAiC,CAAA;QAAAjC,aAAA,GAAAC,CAAA;QAClB,OAAA+D,MAAA,CAAAC,MAAA,KACKL,cAAc;UACjBM,UAAU,EAAE,IAAI,CAACC,wBAAwB,CAACV,oBAAoB,EAAEG,cAAc;QAAC;MAEnF,CAAC;QAAA5D,aAAA,GAAAiC,CAAA;MAAA;MAGD,IAAMmC,YAAY,IAAApE,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACoE,gBAAgB,CAACZ,oBAAoB,CAAC;MAACzD,aAAA,GAAAC,CAAA;MACjE,IAAImE,YAAY,EAAE;QAAApE,aAAA,GAAAiC,CAAA;QAAAjC,aAAA,GAAAC,CAAA;QAChB,OAAOmE,YAAY;MACrB,CAAC;QAAApE,aAAA,GAAAiC,CAAA;MAAA;MAAAjC,aAAA,GAAAC,CAAA;MAED,OAAO,IAAI;IACb;EAAC;IAAA2B,GAAA;IAAAC,KAAA,EAKD,SAAAyC,oBAAoBA,CAAA,EAAa;MAAAtE,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAC/B,OAAO,IAAI,CAACuB,aAAa,CAAC+C,GAAG,CAAC,UAAAT,GAAG,EAAI;QAAA9D,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QAAA,OAAA6D,GAAG,CAACrC,OAAO;MAAD,CAAC,CAAC;IACnD;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAKD,SAAA2C,oBAAoBA,CAAA,EAAY;MAAAxE,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAC9B,OAAO,IAAI,CAACoB,WAAW;IACzB;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAKD,SAAA4C,mBAAmBA,CAAA,EAAY;MAAAzE,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAC7B,OAAO,IAAI,CAACqB,UAAU;IACxB;EAAC;IAAAM,GAAA;IAAAC,KAAA,EAKD,SAAA6C,kBAAkBA,CAAA,EAAgC;MAAA1E,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAChD,OAAO;QACL0E,aAAa,EAAE,CACb,yBAAyB,EACzB,kCAAkC,EAClC,+BAA+B,EAC/B,6BAA6B,EAC7B,iCAAiC,CAClC;QACDC,WAAW,EAAE,CACX,yCAAyC,EACzC,uCAAuC,EACvC,iCAAiC,EACjC,yCAAyC,EACzC,yCAAyC,CAC1C;QACDC,IAAI,EAAE,CACJ,yCAAyC,EACzC,iCAAiC,EACjC,qCAAqC,EACrC,iCAAiC,EACjC,uCAAuC,CACxC;QACDC,YAAY,EAAE,CACZ,oCAAoC,EACpC,iCAAiC,EACjC,+BAA+B,EAC/B,iCAAiC,EACjC,+BAA+B;MAEnC,CAAC;IACH;EAAC;IAAAlD,GAAA;IAAAC,KAAA;MAAA,IAAAkD,sBAAA,GAAA3E,iBAAA,CAKD,WAA4B4E,IAA4D,EAAEC,UAAmB,EAAiB;QAAAjF,aAAA,GAAAO,CAAA;QAC5H,IAAM2E,OAAO,IAAAlF,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACyE,kBAAkB,CAAC,CAAC;QAEzC,IAAIS,WAAmB;QAACnF,aAAA,GAAAC,CAAA;QAExB,IAAIgF,UAAU,EAAE;UAAAjF,aAAA,GAAAiC,CAAA;UAAAjC,aAAA,GAAAC,CAAA;UACdkF,WAAW,GAAGF,UAAU;QAC1B,CAAC,MAAM;UAAAjF,aAAA,GAAAiC,CAAA;UACL,IAAMmD,cAAc,IAAApF,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAAiC,CAAA,WAAAiD,OAAO,CAACF,IAAI,CAAC,MAAAhF,aAAA,GAAAiC,CAAA,WAAIiD,OAAO,CAACP,aAAa;UAAC3E,aAAA,GAAAC,CAAA;UAC9DkF,WAAW,GAAGC,cAAc,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGH,cAAc,CAACvC,MAAM,CAAC,CAAC;QACjF;QAAC7C,aAAA,GAAAC,CAAA;QAED,MAAM,IAAI,CAACC,KAAK,CAACiF,WAAW,EAAE;UAC5BlC,QAAQ,EAAE,OAAO;UACjBE,IAAI,EAAE,GAAG;UACTD,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC;MAAA,SAjBKsC,qBAAqBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAX,sBAAA,CAAAhE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBwE,qBAAqB;IAAA;EAAA;IAAA5D,GAAA;IAAAC,KAAA;MAAA,IAAA8D,kCAAA,GAAAvF,iBAAA,CAqB3B,aAAoE;QAAAJ,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QAClE,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAGF,OAAO,IAAI;QACb,CAAC,CAAC,OAAOiC,KAAK,EAAE;UAAAlC,aAAA,GAAAC,CAAA;UACd,OAAO,KAAK;QACd;MACF,CAAC;MAAA,SARa+B,iCAAiCA,CAAA;QAAA,OAAA2D,kCAAA,CAAA5E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjCgB,iCAAiC;IAAA;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAU/C,SAAQU,wBAAwBA,CAAA,EAAS;MAAA,IAAAqD,KAAA;MAAA5F,aAAA,GAAAO,CAAA;MAEvC,IAAMsF,YAAY,IAAA7F,aAAA,GAAAC,CAAA,QAAG,CACnB,iBAAiB,EACjB,gBAAgB,EAChB,YAAY,EACZ,eAAe,EACf,YAAY,CACb;MAACD,aAAA,GAAAC,CAAA;MAEFW,UAAU,CAAC,YAAM;QAAAZ,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAC,CAAA;QACf,IAAI,CAAAD,aAAA,GAAAiC,CAAA,WAAA2D,KAAI,CAACvE,WAAW,MAAArB,aAAA,GAAAiC,CAAA,WAAI2D,KAAI,CAACrE,oBAAoB,CAACsB,MAAM,GAAG,CAAC,GAAE;UAAA7C,aAAA,GAAAiC,CAAA;UAC5D,IAAM6D,aAAa,IAAA9F,aAAA,GAAAC,CAAA,QAAG4F,YAAY,CAACR,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGM,YAAY,CAAChD,MAAM,CAAC,CAAC;UACnF,IAAMkD,MAA8B,IAAA/F,aAAA,GAAAC,CAAA,QAAG;YACrCuD,UAAU,EAAEsC,aAAa;YACzBE,UAAU,EAAE,IAAI,GAAGX,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,IAAI;YACvCU,OAAO,EAAE;UACX,CAAC;UAACjG,aAAA,GAAAC,CAAA;UAEF2F,KAAI,CAACrE,oBAAoB,CAAC2E,OAAO,CAAC,UAAA7D,QAAQ,EAAI;YAAArC,aAAA,GAAAO,CAAA;YAAAP,aAAA,GAAAC,CAAA;YAAA,OAAAoC,QAAQ,CAAC0D,MAAM,CAAC;UAAD,CAAC,CAAC;QACjE,CAAC;UAAA/F,aAAA,GAAAiC,CAAA;QAAA;MACH,CAAC,EAAE,IAAI,CAAC;IACV;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAQsC,wBAAwBA,CAACX,UAAkB,EAAE/B,OAAqB,EAAO;MAAAzB,aAAA,GAAAO,CAAA;MAE/E,IAAM2D,UAAe,IAAAlE,aAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;MAACD,aAAA,GAAAC,CAAA;MAE3B,QAAQwB,OAAO,CAACC,MAAM;QACpB,KAAK,eAAe;UAAA1B,aAAA,GAAAiC,CAAA;QACpB,KAAK,kBAAkB;UAAAjC,aAAA,GAAAiC,CAAA;QACvB,KAAK,kBAAkB;UAAAjC,aAAA,GAAAiC,CAAA;UAAAjC,aAAA,GAAAC,CAAA;UAErBiE,UAAU,CAACiC,QAAQ,GAAG1E,OAAO,CAACC,MAAM,CAAC0E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC1C,WAAW,CAAC,CAAC;UAAC1D,aAAA,GAAAC,CAAA;UACjE;QAEF,KAAK,iBAAiB;UAAAD,aAAA,GAAAiC,CAAA;UAEpB,IAAMoE,aAAa,IAAArG,aAAA,GAAAC,CAAA,QAAGuD,UAAU,CAAC8C,KAAK,CAAC,8BAA8B,CAAC;UAACtG,aAAA,GAAAC,CAAA;UACvE,IAAIoG,aAAa,EAAE;YAAArG,aAAA,GAAAiC,CAAA;YACjB,IAAMJ,KAAK,IAAA7B,aAAA,GAAAC,CAAA,QAAGsG,QAAQ,CAACF,aAAa,CAAC,CAAC,CAAC,CAAC;YACxC,IAAMG,IAAI,IAAAxG,aAAA,GAAAC,CAAA,QAAGoG,aAAa,CAAC,CAAC,CAAC,CAAC3C,WAAW,CAAC,CAAC;YAAC1D,aAAA,GAAAC,CAAA;YAC5CiE,UAAU,CAACuC,QAAQ,GAAGD,IAAI,CAACE,UAAU,CAAC,KAAK,CAAC,IAAA1G,aAAA,GAAAiC,CAAA,WAAGJ,KAAK,GAAG,EAAE,KAAA7B,aAAA,GAAAiC,CAAA,WAAGJ,KAAK;UACnE,CAAC;YAAA7B,aAAA,GAAAiC,CAAA;UAAA;UAAAjC,aAAA,GAAAC,CAAA;UACD;MACJ;MAACD,aAAA,GAAAC,CAAA;MAED,OAAOiE,UAAU;IACnB;EAAC;IAAAtC,GAAA;IAAAC,KAAA,EAED,SAAQwC,gBAAgBA,CAACb,UAAkB,EAAuB;MAAAxD,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAC,CAAA;MAEhE,KAAK,IAAMwB,OAAO,IAAI,IAAI,CAACD,aAAa,EAAE;QACxC,IAAMmF,YAAY,IAAA3G,aAAA,GAAAC,CAAA,QAAGwB,OAAO,CAACA,OAAO,CAAC2E,KAAK,CAAC,GAAG,CAAC;QAC/C,IAAMQ,eAAe,IAAA5G,aAAA,GAAAC,CAAA,QAAGuD,UAAU,CAAC4C,KAAK,CAAC,GAAG,CAAC;QAE7C,IAAIS,UAAU,IAAA7G,aAAA,GAAAC,CAAA,QAAG,CAAC;QAACD,aAAA,GAAAC,CAAA;QAAA,IAAA6G,KAAA,YAAAA,MAAAC,IAAA,EACc;UAAA/G,aAAA,GAAAC,CAAA;UAC/B,IAAI2G,eAAe,CAACI,IAAI,CAAC,UAAAC,EAAE,EAAI;YAAAjH,aAAA,GAAAO,CAAA;YAAAP,aAAA,GAAAC,CAAA;YAAA,QAAAD,aAAA,GAAAiC,CAAA,WAAAgF,EAAE,CAAClD,QAAQ,CAACgD,IAAI,CAAC,MAAA/G,aAAA,GAAAiC,CAAA,WAAI8E,IAAI,CAAChD,QAAQ,CAACkD,EAAE,CAAC;UAAD,CAAC,CAAC,EAAE;YAAAjH,aAAA,GAAAiC,CAAA;YAAAjC,aAAA,GAAAC,CAAA;YACtE4G,UAAU,EAAE;UACd,CAAC;YAAA7G,aAAA,GAAAiC,CAAA;UAAA;QACH,CAAC;QAJD,KAAK,IAAM8E,IAAI,IAAIJ,YAAY;UAAAG,KAAA,CAAAC,IAAA;QAAA;QAI9B/G,aAAA,GAAAC,CAAA;QAGD,IAAI4G,UAAU,IAAIxB,IAAI,CAAC6B,IAAI,CAACP,YAAY,CAAC9D,MAAM,GAAG,CAAC,CAAC,EAAE;UAAA7C,aAAA,GAAAiC,CAAA;UAAAjC,aAAA,GAAAC,CAAA;UACpD,OAAOwB,OAAO;QAChB,CAAC;UAAAzB,aAAA,GAAAiC,CAAA;QAAA;MACH;MAACjC,aAAA,GAAAC,CAAA;MAED,OAAO,IAAI;IACb;EAAC;AAAA;AAGH,OAAO,IAAMkH,YAAY,IAAAnH,aAAA,GAAAC,CAAA,SAAG,IAAIkB,YAAY,CAAC,CAAC", "ignoreList": []}