b654c4427d1aa2fc39a9876ae88843a6
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import { env as _env } from "expo/virtual/env";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1v4i4qu2r1() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\auth\\AuthService.ts";
  var hash = "6eb7f0164f83d9a80b9b337ee6769fad175782cc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\auth\\AuthService.ts",
    statementMap: {
      "0": {
        start: {
          line: 90,
          column: 63
        },
        end: {
          line: 90,
          column: 65
        }
      },
      "1": {
        start: {
          line: 91,
          column: 36
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "2": {
        start: {
          line: 101,
          column: 24
        },
        end: {
          line: 101,
          column: 60
        }
      },
      "3": {
        start: {
          line: 102,
          column: 28
        },
        end: {
          line: 102,
          column: 69
        }
      },
      "4": {
        start: {
          line: 104,
          column: 4
        },
        end: {
          line: 108,
          column: 5
        }
      },
      "5": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 54
        }
      },
      "6": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 106,
          column: 72
        }
      },
      "7": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 13
        }
      },
      "8": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 117,
          column: 7
        }
      },
      "9": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 119,
          column: 26
        }
      },
      "10": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 177,
          column: 5
        }
      },
      "11": {
        start: {
          line: 128,
          column: 43
        },
        end: {
          line: 128,
          column: 80
        }
      },
      "12": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 134,
          column: 7
        }
      },
      "13": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 55
        }
      },
      "14": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 132,
          column: 67
        }
      },
      "15": {
        start: {
          line: 133,
          column: 8
        },
        end: {
          line: 133,
          column: 15
        }
      },
      "16": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 146,
          column: 7
        }
      },
      "17": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 52
        }
      },
      "18": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 143,
          column: 11
        }
      },
      "19": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 145,
          column: 45
        }
      },
      "20": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 169,
          column: 9
        }
      },
      "21": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 150,
          column: 50
        }
      },
      "22": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 168,
          column: 9
        }
      },
      "23": {
        start: {
          line: 153,
          column: 10
        },
        end: {
          line: 153,
          column: 54
        }
      },
      "24": {
        start: {
          line: 154,
          column: 10
        },
        end: {
          line: 158,
          column: 13
        }
      },
      "25": {
        start: {
          line: 159,
          column: 15
        },
        end: {
          line: 168,
          column: 9
        }
      },
      "26": {
        start: {
          line: 160,
          column: 10
        },
        end: {
          line: 165,
          column: 13
        }
      },
      "27": {
        start: {
          line: 166,
          column: 15
        },
        end: {
          line: 168,
          column: 9
        }
      },
      "28": {
        start: {
          line: 167,
          column: 10
        },
        end: {
          line: 167,
          column: 40
        }
      },
      "29": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 55
        }
      },
      "30": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 176,
          column: 9
        }
      },
      "31": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 199,
          column: 5
        }
      },
      "32": {
        start: {
          line: 185,
          column: 39
        },
        end: {
          line: 189,
          column: 17
        }
      },
      "33": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 194,
          column: 7
        }
      },
      "34": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 192,
          column: 55
        }
      },
      "35": {
        start: {
          line: 193,
          column: 8
        },
        end: {
          line: 193,
          column: 15
        }
      },
      "36": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 36
        }
      },
      "37": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 198,
          column: 58
        }
      },
      "38": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 206,
          column: 61
        }
      },
      "39": {
        start: {
          line: 207,
          column: 4
        },
        end: {
          line: 207,
          column: 77
        }
      },
      "40": {
        start: {
          line: 207,
          column: 48
        },
        end: {
          line: 207,
          column: 75
        }
      },
      "41": {
        start: {
          line: 214,
          column: 4
        },
        end: {
          line: 214,
          column: 43
        }
      },
      "42": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 217,
          column: 32
        }
      },
      "43": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 225,
          column: 6
        }
      },
      "44": {
        start: {
          line: 221,
          column: 20
        },
        end: {
          line: 221,
          column: 61
        }
      },
      "45": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 224,
          column: 7
        }
      },
      "46": {
        start: {
          line: 223,
          column: 8
        },
        end: {
          line: 223,
          column: 49
        }
      },
      "47": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 232,
          column: 29
        }
      },
      "48": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 281,
          column: 5
        }
      },
      "49": {
        start: {
          line: 240,
          column: 6
        },
        end: {
          line: 240,
          column: 55
        }
      },
      "50": {
        start: {
          line: 242,
          column: 51
        },
        end: {
          line: 255,
          column: 8
        }
      },
      "51": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 262,
          column: 7
        }
      },
      "52": {
        start: {
          line: 258,
          column: 25
        },
        end: {
          line: 258,
          column: 51
        }
      },
      "53": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 259,
          column: 69
        }
      },
      "54": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 260,
          column: 74
        }
      },
      "55": {
        start: {
          line: 261,
          column: 8
        },
        end: {
          line: 261,
          column: 63
        }
      },
      "56": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 271,
          column: 7
        }
      },
      "57": {
        start: {
          line: 266,
          column: 8
        },
        end: {
          line: 266,
          column: 45
        }
      },
      "58": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 270,
          column: 10
        }
      },
      "59": {
        start: {
          line: 273,
          column: 6
        },
        end: {
          line: 273,
          column: 43
        }
      },
      "60": {
        start: {
          line: 274,
          column: 6
        },
        end: {
          line: 274,
          column: 31
        }
      },
      "61": {
        start: {
          line: 277,
          column: 23
        },
        end: {
          line: 277,
          column: 45
        }
      },
      "62": {
        start: {
          line: 278,
          column: 6
        },
        end: {
          line: 278,
          column: 67
        }
      },
      "63": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 72
        }
      },
      "64": {
        start: {
          line: 280,
          column: 6
        },
        end: {
          line: 280,
          column: 61
        }
      },
      "65": {
        start: {
          line: 288,
          column: 4
        },
        end: {
          line: 308,
          column: 5
        }
      },
      "66": {
        start: {
          line: 289,
          column: 6
        },
        end: {
          line: 289,
          column: 55
        }
      },
      "67": {
        start: {
          line: 291,
          column: 51
        },
        end: {
          line: 294,
          column: 8
        }
      },
      "68": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 299,
          column: 7
        }
      },
      "69": {
        start: {
          line: 297,
          column: 8
        },
        end: {
          line: 297,
          column: 71
        }
      },
      "70": {
        start: {
          line: 298,
          column: 8
        },
        end: {
          line: 298,
          column: 60
        }
      },
      "71": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 43
        }
      },
      "72": {
        start: {
          line: 302,
          column: 6
        },
        end: {
          line: 302,
          column: 31
        }
      },
      "73": {
        start: {
          line: 305,
          column: 27
        },
        end: {
          line: 305,
          column: 84
        }
      },
      "74": {
        start: {
          line: 306,
          column: 6
        },
        end: {
          line: 306,
          column: 64
        }
      },
      "75": {
        start: {
          line: 307,
          column: 6
        },
        end: {
          line: 307,
          column: 53
        }
      },
      "76": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 332,
          column: 5
        }
      },
      "77": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 316,
          column: 55
        }
      },
      "78": {
        start: {
          line: 318,
          column: 24
        },
        end: {
          line: 318,
          column: 58
        }
      },
      "79": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 323,
          column: 7
        }
      },
      "80": {
        start: {
          line: 321,
          column: 8
        },
        end: {
          line: 321,
          column: 67
        }
      },
      "81": {
        start: {
          line: 322,
          column: 8
        },
        end: {
          line: 322,
          column: 56
        }
      },
      "82": {
        start: {
          line: 325,
          column: 6
        },
        end: {
          line: 325,
          column: 43
        }
      },
      "83": {
        start: {
          line: 326,
          column: 6
        },
        end: {
          line: 326,
          column: 31
        }
      },
      "84": {
        start: {
          line: 329,
          column: 27
        },
        end: {
          line: 329,
          column: 85
        }
      },
      "85": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 64
        }
      },
      "86": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 331,
          column: 53
        }
      },
      "87": {
        start: {
          line: 339,
          column: 4
        },
        end: {
          line: 353,
          column: 5
        }
      },
      "88": {
        start: {
          line: 340,
          column: 24
        },
        end: {
          line: 342,
          column: 8
        }
      },
      "89": {
        start: {
          line: 344,
          column: 6
        },
        end: {
          line: 346,
          column: 7
        }
      },
      "90": {
        start: {
          line: 345,
          column: 8
        },
        end: {
          line: 345,
          column: 56
        }
      },
      "91": {
        start: {
          line: 348,
          column: 6
        },
        end: {
          line: 348,
          column: 31
        }
      },
      "92": {
        start: {
          line: 351,
          column: 27
        },
        end: {
          line: 351,
          column: 91
        }
      },
      "93": {
        start: {
          line: 352,
          column: 6
        },
        end: {
          line: 352,
          column: 53
        }
      },
      "94": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 385,
          column: 5
        }
      },
      "95": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 363,
          column: 7
        }
      },
      "96": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 362,
          column: 67
        }
      },
      "97": {
        start: {
          line: 365,
          column: 30
        },
        end: {
          line: 373,
          column: 17
        }
      },
      "98": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 377,
          column: 7
        }
      },
      "99": {
        start: {
          line: 376,
          column: 8
        },
        end: {
          line: 376,
          column: 56
        }
      },
      "100": {
        start: {
          line: 379,
          column: 6
        },
        end: {
          line: 379,
          column: 42
        }
      },
      "101": {
        start: {
          line: 380,
          column: 6
        },
        end: {
          line: 380,
          column: 31
        }
      },
      "102": {
        start: {
          line: 383,
          column: 27
        },
        end: {
          line: 383,
          column: 91
        }
      },
      "103": {
        start: {
          line: 384,
          column: 6
        },
        end: {
          line: 384,
          column: 53
        }
      },
      "104": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 392,
          column: 40
        }
      },
      "105": {
        start: {
          line: 392,
          column: 33
        },
        end: {
          line: 392,
          column: 40
        }
      },
      "106": {
        start: {
          line: 394,
          column: 4
        },
        end: {
          line: 401,
          column: 5
        }
      },
      "107": {
        start: {
          line: 395,
          column: 6
        },
        end: {
          line: 398,
          column: 45
        }
      },
      "108": {
        start: {
          line: 400,
          column: 6
        },
        end: {
          line: 400,
          column: 58
        }
      },
      "109": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 444,
          column: 5
        }
      },
      "110": {
        start: {
          line: 409,
          column: 6
        },
        end: {
          line: 411,
          column: 7
        }
      },
      "111": {
        start: {
          line: 410,
          column: 8
        },
        end: {
          line: 410,
          column: 67
        }
      },
      "112": {
        start: {
          line: 414,
          column: 23
        },
        end: {
          line: 414,
          column: 44
        }
      },
      "113": {
        start: {
          line: 415,
          column: 19
        },
        end: {
          line: 415,
          column: 40
        }
      },
      "114": {
        start: {
          line: 417,
          column: 22
        },
        end: {
          line: 417,
          column: 47
        }
      },
      "115": {
        start: {
          line: 418,
          column: 23
        },
        end: {
          line: 418,
          column: 71
        }
      },
      "116": {
        start: {
          line: 420,
          column: 30
        },
        end: {
          line: 425,
          column: 10
        }
      },
      "117": {
        start: {
          line: 427,
          column: 6
        },
        end: {
          line: 429,
          column: 7
        }
      },
      "118": {
        start: {
          line: 428,
          column: 8
        },
        end: {
          line: 428,
          column: 56
        }
      },
      "119": {
        start: {
          line: 432,
          column: 38
        },
        end: {
          line: 434,
          column: 31
        }
      },
      "120": {
        start: {
          line: 437,
          column: 6
        },
        end: {
          line: 437,
          column: 58
        }
      },
      "121": {
        start: {
          line: 439,
          column: 6
        },
        end: {
          line: 439,
          column: 47
        }
      },
      "122": {
        start: {
          line: 442,
          column: 27
        },
        end: {
          line: 442,
          column: 90
        }
      },
      "123": {
        start: {
          line: 443,
          column: 6
        },
        end: {
          line: 443,
          column: 53
        }
      },
      "124": {
        start: {
          line: 451,
          column: 4
        },
        end: {
          line: 469,
          column: 5
        }
      },
      "125": {
        start: {
          line: 452,
          column: 6
        },
        end: {
          line: 454,
          column: 7
        }
      },
      "126": {
        start: {
          line: 453,
          column: 8
        },
        end: {
          line: 453,
          column: 67
        }
      },
      "127": {
        start: {
          line: 458,
          column: 6
        },
        end: {
          line: 462,
          column: 8
        }
      },
      "128": {
        start: {
          line: 464,
          column: 6
        },
        end: {
          line: 464,
          column: 88
        }
      },
      "129": {
        start: {
          line: 467,
          column: 27
        },
        end: {
          line: 467,
          column: 93
        }
      },
      "130": {
        start: {
          line: 468,
          column: 6
        },
        end: {
          line: 468,
          column: 53
        }
      },
      "131": {
        start: {
          line: 476,
          column: 4
        },
        end: {
          line: 476,
          column: 25
        }
      },
      "132": {
        start: {
          line: 483,
          column: 4
        },
        end: {
          line: 483,
          column: 67
        }
      },
      "133": {
        start: {
          line: 490,
          column: 4
        },
        end: {
          line: 491,
          column: 66
        }
      },
      "134": {
        start: {
          line: 498,
          column: 4
        },
        end: {
          line: 498,
          column: 66
        }
      },
      "135": {
        start: {
          line: 503,
          column: 27
        },
        end: {
          line: 503,
          column: 44
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 99,
            column: 2
          },
          end: {
            line: 99,
            column: 3
          }
        },
        loc: {
          start: {
            line: 99,
            column: 16
          },
          end: {
            line: 120,
            column: 3
          }
        },
        line: 99
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 125,
            column: 2
          },
          end: {
            line: 125,
            column: 3
          }
        },
        loc: {
          start: {
            line: 125,
            column: 33
          },
          end: {
            line: 178,
            column: 3
          }
        },
        line: 125
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 149,
            column: 43
          },
          end: {
            line: 149,
            column: 44
          }
        },
        loc: {
          start: {
            line: 149,
            column: 69
          },
          end: {
            line: 169,
            column: 7
          }
        },
        line: 149
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 183,
            column: 2
          },
          end: {
            line: 183,
            column: 3
          }
        },
        loc: {
          start: {
            line: 183,
            column: 63
          },
          end: {
            line: 200,
            column: 3
          }
        },
        line: 183
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 205,
            column: 2
          },
          end: {
            line: 205,
            column: 3
          }
        },
        loc: {
          start: {
            line: 205,
            column: 51
          },
          end: {
            line: 208,
            column: 3
          }
        },
        line: 205
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 207,
            column: 36
          },
          end: {
            line: 207,
            column: 37
          }
        },
        loc: {
          start: {
            line: 207,
            column: 48
          },
          end: {
            line: 207,
            column: 75
          }
        },
        line: 207
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 213,
            column: 2
          },
          end: {
            line: 213,
            column: 3
          }
        },
        loc: {
          start: {
            line: 213,
            column: 70
          },
          end: {
            line: 226,
            column: 3
          }
        },
        line: 213
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 220,
            column: 11
          },
          end: {
            line: 220,
            column: 12
          }
        },
        loc: {
          start: {
            line: 220,
            column: 17
          },
          end: {
            line: 225,
            column: 5
          }
        },
        line: 220
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 231,
            column: 2
          },
          end: {
            line: 231,
            column: 3
          }
        },
        loc: {
          start: {
            line: 231,
            column: 31
          },
          end: {
            line: 233,
            column: 3
          }
        },
        line: 231
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 238,
            column: 2
          },
          end: {
            line: 238,
            column: 3
          }
        },
        loc: {
          start: {
            line: 238,
            column: 80
          },
          end: {
            line: 282,
            column: 3
          }
        },
        line: 238
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 287,
            column: 2
          },
          end: {
            line: 287,
            column: 3
          }
        },
        loc: {
          start: {
            line: 287,
            column: 80
          },
          end: {
            line: 309,
            column: 3
          }
        },
        line: 287
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 314,
            column: 2
          },
          end: {
            line: 314,
            column: 3
          }
        },
        loc: {
          start: {
            line: 314,
            column: 65
          },
          end: {
            line: 333,
            column: 3
          }
        },
        line: 314
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 338,
            column: 2
          },
          end: {
            line: 338,
            column: 3
          }
        },
        loc: {
          start: {
            line: 338,
            column: 84
          },
          end: {
            line: 354,
            column: 3
          }
        },
        line: 338
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 359,
            column: 2
          },
          end: {
            line: 359,
            column: 3
          }
        },
        loc: {
          start: {
            line: 359,
            column: 97
          },
          end: {
            line: 386,
            column: 3
          }
        },
        line: 359
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 391,
            column: 2
          },
          end: {
            line: 391,
            column: 3
          }
        },
        loc: {
          start: {
            line: 391,
            column: 42
          },
          end: {
            line: 402,
            column: 3
          }
        },
        line: 391
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 407,
            column: 2
          },
          end: {
            line: 407,
            column: 3
          }
        },
        loc: {
          start: {
            line: 407,
            column: 100
          },
          end: {
            line: 445,
            column: 3
          }
        },
        line: 407
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 450,
            column: 2
          },
          end: {
            line: 450,
            column: 3
          }
        },
        loc: {
          start: {
            line: 450,
            column: 71
          },
          end: {
            line: 470,
            column: 3
          }
        },
        line: 450
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 475,
            column: 2
          },
          end: {
            line: 475,
            column: 3
          }
        },
        loc: {
          start: {
            line: 475,
            column: 38
          },
          end: {
            line: 477,
            column: 3
          }
        },
        line: 475
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 482,
            column: 2
          },
          end: {
            line: 482,
            column: 3
          }
        },
        loc: {
          start: {
            line: 482,
            column: 29
          },
          end: {
            line: 484,
            column: 3
          }
        },
        line: 482
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 489,
            column: 2
          },
          end: {
            line: 489,
            column: 3
          }
        },
        loc: {
          start: {
            line: 489,
            column: 36
          },
          end: {
            line: 492,
            column: 3
          }
        },
        line: 489
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 497,
            column: 2
          },
          end: {
            line: 497,
            column: 3
          }
        },
        loc: {
          start: {
            line: 497,
            column: 32
          },
          end: {
            line: 499,
            column: 3
          }
        },
        line: 497
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 104,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 104
      },
      "1": {
        loc: {
          start: {
            line: 104,
            column: 8
          },
          end: {
            line: 104,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 104,
            column: 8
          },
          end: {
            line: 104,
            column: 20
          }
        }, {
          start: {
            line: 104,
            column: 24
          },
          end: {
            line: 104,
            column: 40
          }
        }],
        line: 104
      },
      "2": {
        loc: {
          start: {
            line: 130,
            column: 6
          },
          end: {
            line: 134,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 6
          },
          end: {
            line: 134,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "3": {
        loc: {
          start: {
            line: 136,
            column: 6
          },
          end: {
            line: 146,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 6
          },
          end: {
            line: 146,
            column: 7
          }
        }, {
          start: {
            line: 144,
            column: 13
          },
          end: {
            line: 146,
            column: 7
          }
        }],
        line: 136
      },
      "4": {
        loc: {
          start: {
            line: 152,
            column: 8
          },
          end: {
            line: 168,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 152,
            column: 8
          },
          end: {
            line: 168,
            column: 9
          }
        }, {
          start: {
            line: 159,
            column: 15
          },
          end: {
            line: 168,
            column: 9
          }
        }],
        line: 152
      },
      "5": {
        loc: {
          start: {
            line: 152,
            column: 12
          },
          end: {
            line: 152,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 152,
            column: 12
          },
          end: {
            line: 152,
            column: 33
          }
        }, {
          start: {
            line: 152,
            column: 37
          },
          end: {
            line: 152,
            column: 50
          }
        }],
        line: 152
      },
      "6": {
        loc: {
          start: {
            line: 159,
            column: 15
          },
          end: {
            line: 168,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 15
          },
          end: {
            line: 168,
            column: 9
          }
        }, {
          start: {
            line: 166,
            column: 15
          },
          end: {
            line: 168,
            column: 9
          }
        }],
        line: 159
      },
      "7": {
        loc: {
          start: {
            line: 166,
            column: 15
          },
          end: {
            line: 168,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 15
          },
          end: {
            line: 168,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "8": {
        loc: {
          start: {
            line: 166,
            column: 19
          },
          end: {
            line: 166,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 19
          },
          end: {
            line: 166,
            column: 46
          }
        }, {
          start: {
            line: 166,
            column: 50
          },
          end: {
            line: 166,
            column: 57
          }
        }],
        line: 166
      },
      "9": {
        loc: {
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 194,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 194,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "10": {
        loc: {
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 191,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 191,
            column: 15
          }
        }, {
          start: {
            line: 191,
            column: 19
          },
          end: {
            line: 191,
            column: 44
          }
        }],
        line: 191
      },
      "11": {
        loc: {
          start: {
            line: 222,
            column: 6
          },
          end: {
            line: 224,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 6
          },
          end: {
            line: 224,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "12": {
        loc: {
          start: {
            line: 248,
            column: 26
          },
          end: {
            line: 248,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 248,
            column: 26
          },
          end: {
            line: 248,
            column: 42
          }
        }, {
          start: {
            line: 248,
            column: 46
          },
          end: {
            line: 248,
            column: 56
          }
        }],
        line: 248
      },
      "13": {
        loc: {
          start: {
            line: 249,
            column: 27
          },
          end: {
            line: 249,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 249,
            column: 27
          },
          end: {
            line: 249,
            column: 44
          }
        }, {
          start: {
            line: 249,
            column: 48
          },
          end: {
            line: 249,
            column: 59
          }
        }],
        line: 249
      },
      "14": {
        loc: {
          start: {
            line: 250,
            column: 27
          },
          end: {
            line: 250,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 250,
            column: 27
          },
          end: {
            line: 250,
            column: 44
          }
        }, {
          start: {
            line: 250,
            column: 48
          },
          end: {
            line: 250,
            column: 55
          }
        }],
        line: 250
      },
      "15": {
        loc: {
          start: {
            line: 251,
            column: 30
          },
          end: {
            line: 251,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 251,
            column: 30
          },
          end: {
            line: 251,
            column: 50
          }
        }, {
          start: {
            line: 251,
            column: 54
          },
          end: {
            line: 251,
            column: 60
          }
        }],
        line: 251
      },
      "16": {
        loc: {
          start: {
            line: 252,
            column: 27
          },
          end: {
            line: 252,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 27
          },
          end: {
            line: 252,
            column: 44
          }
        }, {
          start: {
            line: 252,
            column: 48
          },
          end: {
            line: 252,
            column: 49
          }
        }],
        line: 252
      },
      "17": {
        loc: {
          start: {
            line: 257,
            column: 6
          },
          end: {
            line: 262,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 6
          },
          end: {
            line: 262,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "18": {
        loc: {
          start: {
            line: 265,
            column: 6
          },
          end: {
            line: 271,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 265,
            column: 6
          },
          end: {
            line: 271,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 265
      },
      "19": {
        loc: {
          start: {
            line: 265,
            column: 10
          },
          end: {
            line: 265,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 265,
            column: 10
          },
          end: {
            line: 265,
            column: 23
          }
        }, {
          start: {
            line: 265,
            column: 27
          },
          end: {
            line: 265,
            column: 44
          }
        }],
        line: 265
      },
      "20": {
        loc: {
          start: {
            line: 296,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "21": {
        loc: {
          start: {
            line: 305,
            column: 27
          },
          end: {
            line: 305,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 305,
            column: 52
          },
          end: {
            line: 305,
            column: 65
          }
        }, {
          start: {
            line: 305,
            column: 68
          },
          end: {
            line: 305,
            column: 84
          }
        }],
        line: 305
      },
      "22": {
        loc: {
          start: {
            line: 320,
            column: 6
          },
          end: {
            line: 323,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 320,
            column: 6
          },
          end: {
            line: 323,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 320
      },
      "23": {
        loc: {
          start: {
            line: 329,
            column: 27
          },
          end: {
            line: 329,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 329,
            column: 52
          },
          end: {
            line: 329,
            column: 65
          }
        }, {
          start: {
            line: 329,
            column: 68
          },
          end: {
            line: 329,
            column: 85
          }
        }],
        line: 329
      },
      "24": {
        loc: {
          start: {
            line: 344,
            column: 6
          },
          end: {
            line: 346,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 344,
            column: 6
          },
          end: {
            line: 346,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 344
      },
      "25": {
        loc: {
          start: {
            line: 351,
            column: 27
          },
          end: {
            line: 351,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 351,
            column: 52
          },
          end: {
            line: 351,
            column: 65
          }
        }, {
          start: {
            line: 351,
            column: 68
          },
          end: {
            line: 351,
            column: 91
          }
        }],
        line: 351
      },
      "26": {
        loc: {
          start: {
            line: 361,
            column: 6
          },
          end: {
            line: 363,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 361,
            column: 6
          },
          end: {
            line: 363,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 361
      },
      "27": {
        loc: {
          start: {
            line: 375,
            column: 6
          },
          end: {
            line: 377,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 375,
            column: 6
          },
          end: {
            line: 377,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 375
      },
      "28": {
        loc: {
          start: {
            line: 383,
            column: 27
          },
          end: {
            line: 383,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 383,
            column: 52
          },
          end: {
            line: 383,
            column: 65
          }
        }, {
          start: {
            line: 383,
            column: 68
          },
          end: {
            line: 383,
            column: 91
          }
        }],
        line: 383
      },
      "29": {
        loc: {
          start: {
            line: 392,
            column: 4
          },
          end: {
            line: 392,
            column: 40
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 392,
            column: 4
          },
          end: {
            line: 392,
            column: 40
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 392
      },
      "30": {
        loc: {
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 411,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 411,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 409
      },
      "31": {
        loc: {
          start: {
            line: 427,
            column: 6
          },
          end: {
            line: 429,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 427,
            column: 6
          },
          end: {
            line: 429,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 427
      },
      "32": {
        loc: {
          start: {
            line: 442,
            column: 27
          },
          end: {
            line: 442,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 442,
            column: 52
          },
          end: {
            line: 442,
            column: 65
          }
        }, {
          start: {
            line: 442,
            column: 68
          },
          end: {
            line: 442,
            column: 90
          }
        }],
        line: 442
      },
      "33": {
        loc: {
          start: {
            line: 452,
            column: 6
          },
          end: {
            line: 454,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 452,
            column: 6
          },
          end: {
            line: 454,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 452
      },
      "34": {
        loc: {
          start: {
            line: 467,
            column: 27
          },
          end: {
            line: 467,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 467,
            column: 52
          },
          end: {
            line: 467,
            column: 65
          }
        }, {
          start: {
            line: 467,
            column: 68
          },
          end: {
            line: 467,
            column: 93
          }
        }],
        line: 467
      },
      "35": {
        loc: {
          start: {
            line: 483,
            column: 11
          },
          end: {
            line: 483,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 483,
            column: 11
          },
          end: {
            line: 483,
            column: 35
          }
        }, {
          start: {
            line: 483,
            column: 39
          },
          end: {
            line: 483,
            column: 66
          }
        }],
        line: 483
      },
      "36": {
        loc: {
          start: {
            line: 490,
            column: 11
          },
          end: {
            line: 491,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 490,
            column: 11
          },
          end: {
            line: 490,
            column: 69
          }
        }, {
          start: {
            line: 491,
            column: 11
          },
          end: {
            line: 491,
            column: 65
          }
        }],
        line: 490
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6eb7f0164f83d9a80b9b337ee6769fad175782cc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1v4i4qu2r1 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1v4i4qu2r1();
import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { handleAuthError, logError } from "../../utils/errorHandling";
var AuthService = function () {
  function AuthService() {
    _classCallCheck(this, AuthService);
    this.authStateListeners = (cov_1v4i4qu2r1().s[0]++, []);
    this.currentState = (cov_1v4i4qu2r1().s[1]++, {
      user: null,
      profile: null,
      session: null,
      loading: true,
      error: null
    });
    cov_1v4i4qu2r1().f[0]++;
    var supabaseUrl = (cov_1v4i4qu2r1().s[2]++, _env.EXPO_PUBLIC_SUPABASE_URL);
    var supabaseAnonKey = (cov_1v4i4qu2r1().s[3]++, _env.EXPO_PUBLIC_SUPABASE_ANON_KEY);
    cov_1v4i4qu2r1().s[4]++;
    if ((cov_1v4i4qu2r1().b[1][0]++, !supabaseUrl) || (cov_1v4i4qu2r1().b[1][1]++, !supabaseAnonKey)) {
      cov_1v4i4qu2r1().b[0][0]++;
      cov_1v4i4qu2r1().s[5]++;
      console.error('Missing Supabase configuration');
      cov_1v4i4qu2r1().s[6]++;
      this.currentState.error = 'Authentication service not configured';
      cov_1v4i4qu2r1().s[7]++;
      return;
    } else {
      cov_1v4i4qu2r1().b[0][1]++;
    }
    cov_1v4i4qu2r1().s[8]++;
    this.supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        storage: AsyncStorage,
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: false
      }
    });
    cov_1v4i4qu2r1().s[9]++;
    this.initializeAuth();
  }
  return _createClass(AuthService, [{
    key: "initializeAuth",
    value: (function () {
      var _initializeAuth = _asyncToGenerator(function* () {
        var _this = this;
        cov_1v4i4qu2r1().f[1]++;
        cov_1v4i4qu2r1().s[10]++;
        try {
          var _ref = (cov_1v4i4qu2r1().s[11]++, yield this.supabase.auth.getSession()),
            session = _ref.data.session,
            error = _ref.error;
          cov_1v4i4qu2r1().s[12]++;
          if (error) {
            cov_1v4i4qu2r1().b[2][0]++;
            cov_1v4i4qu2r1().s[13]++;
            console.error('Error getting session:', error);
            cov_1v4i4qu2r1().s[14]++;
            this.updateState({
              error: error.message,
              loading: false
            });
            cov_1v4i4qu2r1().s[15]++;
            return;
          } else {
            cov_1v4i4qu2r1().b[2][1]++;
          }
          cov_1v4i4qu2r1().s[16]++;
          if (session != null && session.user) {
            cov_1v4i4qu2r1().b[3][0]++;
            cov_1v4i4qu2r1().s[17]++;
            yield this.loadUserProfile(session.user.id);
            cov_1v4i4qu2r1().s[18]++;
            this.updateState({
              user: session.user,
              session: session,
              loading: false,
              error: null
            });
          } else {
            cov_1v4i4qu2r1().b[3][1]++;
            cov_1v4i4qu2r1().s[19]++;
            this.updateState({
              loading: false
            });
          }
          cov_1v4i4qu2r1().s[20]++;
          this.supabase.auth.onAuthStateChange(function () {
            var _ref2 = _asyncToGenerator(function* (event, session) {
              cov_1v4i4qu2r1().f[2]++;
              cov_1v4i4qu2r1().s[21]++;
              console.log('Auth state changed:', event);
              cov_1v4i4qu2r1().s[22]++;
              if ((cov_1v4i4qu2r1().b[5][0]++, event === 'SIGNED_IN') && (cov_1v4i4qu2r1().b[5][1]++, session != null && session.user)) {
                cov_1v4i4qu2r1().b[4][0]++;
                cov_1v4i4qu2r1().s[23]++;
                yield _this.loadUserProfile(session.user.id);
                cov_1v4i4qu2r1().s[24]++;
                _this.updateState({
                  user: session.user,
                  session: session,
                  error: null
                });
              } else {
                cov_1v4i4qu2r1().b[4][1]++;
                cov_1v4i4qu2r1().s[25]++;
                if (event === 'SIGNED_OUT') {
                  cov_1v4i4qu2r1().b[6][0]++;
                  cov_1v4i4qu2r1().s[26]++;
                  _this.updateState({
                    user: null,
                    profile: null,
                    session: null,
                    error: null
                  });
                } else {
                  cov_1v4i4qu2r1().b[6][1]++;
                  cov_1v4i4qu2r1().s[27]++;
                  if ((cov_1v4i4qu2r1().b[8][0]++, event === 'TOKEN_REFRESHED') && (cov_1v4i4qu2r1().b[8][1]++, session)) {
                    cov_1v4i4qu2r1().b[7][0]++;
                    cov_1v4i4qu2r1().s[28]++;
                    _this.updateState({
                      session: session
                    });
                  } else {
                    cov_1v4i4qu2r1().b[7][1]++;
                  }
                }
              }
            });
            return function (_x, _x2) {
              return _ref2.apply(this, arguments);
            };
          }());
        } catch (error) {
          cov_1v4i4qu2r1().s[29]++;
          console.error('Error initializing auth:', error);
          cov_1v4i4qu2r1().s[30]++;
          this.updateState({
            error: 'Failed to initialize authentication',
            loading: false
          });
        }
      });
      function initializeAuth() {
        return _initializeAuth.apply(this, arguments);
      }
      return initializeAuth;
    }())
  }, {
    key: "loadUserProfile",
    value: (function () {
      var _loadUserProfile = _asyncToGenerator(function* (userId) {
        cov_1v4i4qu2r1().f[3]++;
        cov_1v4i4qu2r1().s[31]++;
        try {
          var _ref3 = (cov_1v4i4qu2r1().s[32]++, yield this.supabase.from('user_profiles').select('*').eq('id', userId).single()),
            profile = _ref3.data,
            error = _ref3.error;
          cov_1v4i4qu2r1().s[33]++;
          if ((cov_1v4i4qu2r1().b[10][0]++, error) && (cov_1v4i4qu2r1().b[10][1]++, error.code !== 'PGRST116')) {
            cov_1v4i4qu2r1().b[9][0]++;
            cov_1v4i4qu2r1().s[34]++;
            console.error('Error loading profile:', error);
            cov_1v4i4qu2r1().s[35]++;
            return;
          } else {
            cov_1v4i4qu2r1().b[9][1]++;
          }
          cov_1v4i4qu2r1().s[36]++;
          this.updateState({
            profile: profile
          });
        } catch (error) {
          cov_1v4i4qu2r1().s[37]++;
          console.error('Error loading user profile:', error);
        }
      });
      function loadUserProfile(_x3) {
        return _loadUserProfile.apply(this, arguments);
      }
      return loadUserProfile;
    }())
  }, {
    key: "updateState",
    value: function updateState(updates) {
      var _this2 = this;
      cov_1v4i4qu2r1().f[4]++;
      cov_1v4i4qu2r1().s[38]++;
      this.currentState = Object.assign({}, this.currentState, updates);
      cov_1v4i4qu2r1().s[39]++;
      this.authStateListeners.forEach(function (listener) {
        cov_1v4i4qu2r1().f[5]++;
        cov_1v4i4qu2r1().s[40]++;
        return listener(_this2.currentState);
      });
    }
  }, {
    key: "onAuthStateChange",
    value: function onAuthStateChange(callback) {
      var _this3 = this;
      cov_1v4i4qu2r1().f[6]++;
      cov_1v4i4qu2r1().s[41]++;
      this.authStateListeners.push(callback);
      cov_1v4i4qu2r1().s[42]++;
      callback(this.currentState);
      cov_1v4i4qu2r1().s[43]++;
      return function () {
        cov_1v4i4qu2r1().f[7]++;
        var index = (cov_1v4i4qu2r1().s[44]++, _this3.authStateListeners.indexOf(callback));
        cov_1v4i4qu2r1().s[45]++;
        if (index > -1) {
          cov_1v4i4qu2r1().b[11][0]++;
          cov_1v4i4qu2r1().s[46]++;
          _this3.authStateListeners.splice(index, 1);
        } else {
          cov_1v4i4qu2r1().b[11][1]++;
        }
      };
    }
  }, {
    key: "getCurrentState",
    value: function getCurrentState() {
      cov_1v4i4qu2r1().f[8]++;
      cov_1v4i4qu2r1().s[47]++;
      return this.currentState;
    }
  }, {
    key: "signUp",
    value: (function () {
      var _signUp = _asyncToGenerator(function* (data) {
        cov_1v4i4qu2r1().f[9]++;
        cov_1v4i4qu2r1().s[48]++;
        try {
          cov_1v4i4qu2r1().s[49]++;
          this.updateState({
            loading: true,
            error: null
          });
          var _ref4 = (cov_1v4i4qu2r1().s[50]++, yield this.supabase.auth.signUp({
              email: data.email,
              password: data.password,
              options: {
                data: {
                  full_name: data.fullName,
                  tennis_level: (cov_1v4i4qu2r1().b[12][0]++, data.tennisLevel) || (cov_1v4i4qu2r1().b[12][1]++, 'beginner'),
                  playing_style: (cov_1v4i4qu2r1().b[13][0]++, data.playingStyle) || (cov_1v4i4qu2r1().b[13][1]++, 'all_court'),
                  dominant_hand: (cov_1v4i4qu2r1().b[14][0]++, data.dominantHand) || (cov_1v4i4qu2r1().b[14][1]++, 'right'),
                  favorite_surface: (cov_1v4i4qu2r1().b[15][0]++, data.favoriteSurface) || (cov_1v4i4qu2r1().b[15][1]++, 'hard'),
                  years_playing: (cov_1v4i4qu2r1().b[16][0]++, data.yearsPlaying) || (cov_1v4i4qu2r1().b[16][1]++, 0)
                }
              }
            })),
            authData = _ref4.data,
            authError = _ref4.error;
          cov_1v4i4qu2r1().s[51]++;
          if (authError) {
            cov_1v4i4qu2r1().b[17][0]++;
            var appError = (cov_1v4i4qu2r1().s[52]++, handleAuthError(authError));
            cov_1v4i4qu2r1().s[53]++;
            logError(appError, {
              context: 'signUp',
              email: data.email
            });
            cov_1v4i4qu2r1().s[54]++;
            this.updateState({
              error: appError.userMessage,
              loading: false
            });
            cov_1v4i4qu2r1().s[55]++;
            return {
              success: false,
              error: appError.userMessage
            };
          } else {
            cov_1v4i4qu2r1().b[17][1]++;
          }
          cov_1v4i4qu2r1().s[56]++;
          if ((cov_1v4i4qu2r1().b[19][0]++, authData.user) && (cov_1v4i4qu2r1().b[19][1]++, !authData.session)) {
            cov_1v4i4qu2r1().b[18][0]++;
            cov_1v4i4qu2r1().s[57]++;
            this.updateState({
              loading: false
            });
            cov_1v4i4qu2r1().s[58]++;
            return {
              success: true,
              error: 'Please check your email to confirm your account'
            };
          } else {
            cov_1v4i4qu2r1().b[18][1]++;
          }
          cov_1v4i4qu2r1().s[59]++;
          this.updateState({
            loading: false
          });
          cov_1v4i4qu2r1().s[60]++;
          return {
            success: true
          };
        } catch (error) {
          var _appError = (cov_1v4i4qu2r1().s[61]++, handleAuthError(error));
          cov_1v4i4qu2r1().s[62]++;
          logError(_appError, {
            context: 'signUp',
            email: data.email
          });
          cov_1v4i4qu2r1().s[63]++;
          this.updateState({
            error: _appError.userMessage,
            loading: false
          });
          cov_1v4i4qu2r1().s[64]++;
          return {
            success: false,
            error: _appError.userMessage
          };
        }
      });
      function signUp(_x4) {
        return _signUp.apply(this, arguments);
      }
      return signUp;
    }())
  }, {
    key: "signIn",
    value: (function () {
      var _signIn = _asyncToGenerator(function* (data) {
        cov_1v4i4qu2r1().f[10]++;
        cov_1v4i4qu2r1().s[65]++;
        try {
          cov_1v4i4qu2r1().s[66]++;
          this.updateState({
            loading: true,
            error: null
          });
          var _ref5 = (cov_1v4i4qu2r1().s[67]++, yield this.supabase.auth.signInWithPassword({
              email: data.email,
              password: data.password
            })),
            authData = _ref5.data,
            authError = _ref5.error;
          cov_1v4i4qu2r1().s[68]++;
          if (authError) {
            cov_1v4i4qu2r1().b[20][0]++;
            cov_1v4i4qu2r1().s[69]++;
            this.updateState({
              error: authError.message,
              loading: false
            });
            cov_1v4i4qu2r1().s[70]++;
            return {
              success: false,
              error: authError.message
            };
          } else {
            cov_1v4i4qu2r1().b[20][1]++;
          }
          cov_1v4i4qu2r1().s[71]++;
          this.updateState({
            loading: false
          });
          cov_1v4i4qu2r1().s[72]++;
          return {
            success: true
          };
        } catch (error) {
          var errorMessage = (cov_1v4i4qu2r1().s[73]++, error instanceof Error ? (cov_1v4i4qu2r1().b[21][0]++, error.message) : (cov_1v4i4qu2r1().b[21][1]++, 'Sign in failed'));
          cov_1v4i4qu2r1().s[74]++;
          this.updateState({
            error: errorMessage,
            loading: false
          });
          cov_1v4i4qu2r1().s[75]++;
          return {
            success: false,
            error: errorMessage
          };
        }
      });
      function signIn(_x5) {
        return _signIn.apply(this, arguments);
      }
      return signIn;
    }())
  }, {
    key: "signOut",
    value: (function () {
      var _signOut = _asyncToGenerator(function* () {
        cov_1v4i4qu2r1().f[11]++;
        cov_1v4i4qu2r1().s[76]++;
        try {
          cov_1v4i4qu2r1().s[77]++;
          this.updateState({
            loading: true,
            error: null
          });
          var _ref6 = (cov_1v4i4qu2r1().s[78]++, yield this.supabase.auth.signOut()),
            error = _ref6.error;
          cov_1v4i4qu2r1().s[79]++;
          if (error) {
            cov_1v4i4qu2r1().b[22][0]++;
            cov_1v4i4qu2r1().s[80]++;
            this.updateState({
              error: error.message,
              loading: false
            });
            cov_1v4i4qu2r1().s[81]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_1v4i4qu2r1().b[22][1]++;
          }
          cov_1v4i4qu2r1().s[82]++;
          this.updateState({
            loading: false
          });
          cov_1v4i4qu2r1().s[83]++;
          return {
            success: true
          };
        } catch (error) {
          var errorMessage = (cov_1v4i4qu2r1().s[84]++, error instanceof Error ? (cov_1v4i4qu2r1().b[23][0]++, error.message) : (cov_1v4i4qu2r1().b[23][1]++, 'Sign out failed'));
          cov_1v4i4qu2r1().s[85]++;
          this.updateState({
            error: errorMessage,
            loading: false
          });
          cov_1v4i4qu2r1().s[86]++;
          return {
            success: false,
            error: errorMessage
          };
        }
      });
      function signOut() {
        return _signOut.apply(this, arguments);
      }
      return signOut;
    }())
  }, {
    key: "resetPassword",
    value: (function () {
      var _resetPassword = _asyncToGenerator(function* (email) {
        cov_1v4i4qu2r1().f[12]++;
        cov_1v4i4qu2r1().s[87]++;
        try {
          var _ref7 = (cov_1v4i4qu2r1().s[88]++, yield this.supabase.auth.resetPasswordForEmail(email, {
              redirectTo: 'acemind://reset-password'
            })),
            error = _ref7.error;
          cov_1v4i4qu2r1().s[89]++;
          if (error) {
            cov_1v4i4qu2r1().b[24][0]++;
            cov_1v4i4qu2r1().s[90]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_1v4i4qu2r1().b[24][1]++;
          }
          cov_1v4i4qu2r1().s[91]++;
          return {
            success: true
          };
        } catch (error) {
          var errorMessage = (cov_1v4i4qu2r1().s[92]++, error instanceof Error ? (cov_1v4i4qu2r1().b[25][0]++, error.message) : (cov_1v4i4qu2r1().b[25][1]++, 'Password reset failed'));
          cov_1v4i4qu2r1().s[93]++;
          return {
            success: false,
            error: errorMessage
          };
        }
      });
      function resetPassword(_x6) {
        return _resetPassword.apply(this, arguments);
      }
      return resetPassword;
    }())
  }, {
    key: "updateProfile",
    value: (function () {
      var _updateProfile = _asyncToGenerator(function* (updates) {
        cov_1v4i4qu2r1().f[13]++;
        cov_1v4i4qu2r1().s[94]++;
        try {
          cov_1v4i4qu2r1().s[95]++;
          if (!this.currentState.user) {
            cov_1v4i4qu2r1().b[26][0]++;
            cov_1v4i4qu2r1().s[96]++;
            return {
              success: false,
              error: 'User not authenticated'
            };
          } else {
            cov_1v4i4qu2r1().b[26][1]++;
          }
          var _ref8 = (cov_1v4i4qu2r1().s[97]++, yield this.supabase.from('user_profiles').update(Object.assign({}, updates, {
              updated_at: new Date().toISOString()
            })).eq('id', this.currentState.user.id).select().single()),
            data = _ref8.data,
            error = _ref8.error;
          cov_1v4i4qu2r1().s[98]++;
          if (error) {
            cov_1v4i4qu2r1().b[27][0]++;
            cov_1v4i4qu2r1().s[99]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_1v4i4qu2r1().b[27][1]++;
          }
          cov_1v4i4qu2r1().s[100]++;
          this.updateState({
            profile: data
          });
          cov_1v4i4qu2r1().s[101]++;
          return {
            success: true
          };
        } catch (error) {
          var errorMessage = (cov_1v4i4qu2r1().s[102]++, error instanceof Error ? (cov_1v4i4qu2r1().b[28][0]++, error.message) : (cov_1v4i4qu2r1().b[28][1]++, 'Profile update failed'));
          cov_1v4i4qu2r1().s[103]++;
          return {
            success: false,
            error: errorMessage
          };
        }
      });
      function updateProfile(_x7) {
        return _updateProfile.apply(this, arguments);
      }
      return updateProfile;
    }())
  }, {
    key: "updateLastActive",
    value: (function () {
      var _updateLastActive = _asyncToGenerator(function* () {
        cov_1v4i4qu2r1().f[14]++;
        cov_1v4i4qu2r1().s[104]++;
        if (!this.currentState.user) {
          cov_1v4i4qu2r1().b[29][0]++;
          cov_1v4i4qu2r1().s[105]++;
          return;
        } else {
          cov_1v4i4qu2r1().b[29][1]++;
        }
        cov_1v4i4qu2r1().s[106]++;
        try {
          cov_1v4i4qu2r1().s[107]++;
          yield this.supabase.from('user_profiles').update({
            last_active_at: new Date().toISOString()
          }).eq('id', this.currentState.user.id);
        } catch (error) {
          cov_1v4i4qu2r1().s[108]++;
          console.error('Error updating last active:', error);
        }
      });
      function updateLastActive() {
        return _updateLastActive.apply(this, arguments);
      }
      return updateLastActive;
    }())
  }, {
    key: "uploadAvatar",
    value: (function () {
      var _uploadAvatar = _asyncToGenerator(function* (imageUri) {
        cov_1v4i4qu2r1().f[15]++;
        cov_1v4i4qu2r1().s[109]++;
        try {
          cov_1v4i4qu2r1().s[110]++;
          if (!this.currentState.user) {
            cov_1v4i4qu2r1().b[30][0]++;
            cov_1v4i4qu2r1().s[111]++;
            return {
              success: false,
              error: 'User not authenticated'
            };
          } else {
            cov_1v4i4qu2r1().b[30][1]++;
          }
          var response = (cov_1v4i4qu2r1().s[112]++, yield fetch(imageUri));
          var blob = (cov_1v4i4qu2r1().s[113]++, yield response.blob());
          var fileExt = (cov_1v4i4qu2r1().s[114]++, imageUri.split('.').pop());
          var fileName = (cov_1v4i4qu2r1().s[115]++, `${this.currentState.user.id}/avatar.${fileExt}`);
          var _ref9 = (cov_1v4i4qu2r1().s[116]++, yield this.supabase.storage.from('avatars').upload(fileName, blob, {
              cacheControl: '3600',
              upsert: true
            })),
            data = _ref9.data,
            error = _ref9.error;
          cov_1v4i4qu2r1().s[117]++;
          if (error) {
            cov_1v4i4qu2r1().b[31][0]++;
            cov_1v4i4qu2r1().s[118]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_1v4i4qu2r1().b[31][1]++;
          }
          var _ref0 = (cov_1v4i4qu2r1().s[119]++, this.supabase.storage.from('avatars').getPublicUrl(fileName)),
            publicUrl = _ref0.data.publicUrl;
          cov_1v4i4qu2r1().s[120]++;
          yield this.updateProfile({
            avatar_url: publicUrl
          });
          cov_1v4i4qu2r1().s[121]++;
          return {
            success: true,
            url: publicUrl
          };
        } catch (error) {
          var errorMessage = (cov_1v4i4qu2r1().s[122]++, error instanceof Error ? (cov_1v4i4qu2r1().b[32][0]++, error.message) : (cov_1v4i4qu2r1().b[32][1]++, 'Avatar upload failed'));
          cov_1v4i4qu2r1().s[123]++;
          return {
            success: false,
            error: errorMessage
          };
        }
      });
      function uploadAvatar(_x8) {
        return _uploadAvatar.apply(this, arguments);
      }
      return uploadAvatar;
    }())
  }, {
    key: "deleteAccount",
    value: (function () {
      var _deleteAccount = _asyncToGenerator(function* () {
        cov_1v4i4qu2r1().f[16]++;
        cov_1v4i4qu2r1().s[124]++;
        try {
          cov_1v4i4qu2r1().s[125]++;
          if (!this.currentState.user) {
            cov_1v4i4qu2r1().b[33][0]++;
            cov_1v4i4qu2r1().s[126]++;
            return {
              success: false,
              error: 'User not authenticated'
            };
          } else {
            cov_1v4i4qu2r1().b[33][1]++;
          }
          cov_1v4i4qu2r1().s[127]++;
          Alert.alert('Delete Account', 'Please contact support to delete your account.', [{
            text: 'OK'
          }]);
          cov_1v4i4qu2r1().s[128]++;
          return {
            success: false,
            error: 'Please contact support to delete your account'
          };
        } catch (error) {
          var errorMessage = (cov_1v4i4qu2r1().s[129]++, error instanceof Error ? (cov_1v4i4qu2r1().b[34][0]++, error.message) : (cov_1v4i4qu2r1().b[34][1]++, 'Account deletion failed'));
          cov_1v4i4qu2r1().s[130]++;
          return {
            success: false,
            error: errorMessage
          };
        }
      });
      function deleteAccount() {
        return _deleteAccount.apply(this, arguments);
      }
      return deleteAccount;
    }())
  }, {
    key: "getSupabaseClient",
    value: function getSupabaseClient() {
      cov_1v4i4qu2r1().f[17]++;
      cov_1v4i4qu2r1().s[131]++;
      return this.supabase;
    }
  }, {
    key: "isAuthenticated",
    value: function isAuthenticated() {
      cov_1v4i4qu2r1().f[18]++;
      cov_1v4i4qu2r1().s[132]++;
      return (cov_1v4i4qu2r1().b[35][0]++, !!this.currentState.user) && (cov_1v4i4qu2r1().b[35][1]++, !!this.currentState.session);
    }
  }, {
    key: "hasPremiumSubscription",
    value: function hasPremiumSubscription() {
      var _this$currentState$pr, _this$currentState$pr2;
      cov_1v4i4qu2r1().f[19]++;
      cov_1v4i4qu2r1().s[133]++;
      return (cov_1v4i4qu2r1().b[36][0]++, ((_this$currentState$pr = this.currentState.profile) == null ? void 0 : _this$currentState$pr.subscription_tier) === 'premium') || (cov_1v4i4qu2r1().b[36][1]++, ((_this$currentState$pr2 = this.currentState.profile) == null ? void 0 : _this$currentState$pr2.subscription_tier) === 'pro');
    }
  }, {
    key: "hasProSubscription",
    value: function hasProSubscription() {
      var _this$currentState$pr3;
      cov_1v4i4qu2r1().f[20]++;
      cov_1v4i4qu2r1().s[134]++;
      return ((_this$currentState$pr3 = this.currentState.profile) == null ? void 0 : _this$currentState$pr3.subscription_tier) === 'pro';
    }
  }]);
}();
export var authService = (cov_1v4i4qu2r1().s[135]++, new AuthService());
export default authService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJBc3luY1N0b3JhZ2UiLCJBbGVydCIsImhhbmRsZUF1dGhFcnJvciIsImxvZ0Vycm9yIiwiQXV0aFNlcnZpY2UiLCJfY2xhc3NDYWxsQ2hlY2siLCJhdXRoU3RhdGVMaXN0ZW5lcnMiLCJjb3ZfMXY0aTRxdTJyMSIsInMiLCJjdXJyZW50U3RhdGUiLCJ1c2VyIiwicHJvZmlsZSIsInNlc3Npb24iLCJsb2FkaW5nIiwiZXJyb3IiLCJmIiwic3VwYWJhc2VVcmwiLCJfZW52IiwiRVhQT19QVUJMSUNfU1VQQUJBU0VfVVJMIiwic3VwYWJhc2VBbm9uS2V5IiwiRVhQT19QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkiLCJiIiwiY29uc29sZSIsInN1cGFiYXNlIiwiYXV0aCIsInN0b3JhZ2UiLCJhdXRvUmVmcmVzaFRva2VuIiwicGVyc2lzdFNlc3Npb24iLCJkZXRlY3RTZXNzaW9uSW5VcmwiLCJpbml0aWFsaXplQXV0aCIsIl9jcmVhdGVDbGFzcyIsImtleSIsInZhbHVlIiwiX2luaXRpYWxpemVBdXRoIiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJfdGhpcyIsIl9yZWYiLCJnZXRTZXNzaW9uIiwiZGF0YSIsInVwZGF0ZVN0YXRlIiwibWVzc2FnZSIsImxvYWRVc2VyUHJvZmlsZSIsImlkIiwib25BdXRoU3RhdGVDaGFuZ2UiLCJfcmVmMiIsImV2ZW50IiwibG9nIiwiX3giLCJfeDIiLCJhcHBseSIsImFyZ3VtZW50cyIsIl9sb2FkVXNlclByb2ZpbGUiLCJ1c2VySWQiLCJfcmVmMyIsImZyb20iLCJzZWxlY3QiLCJlcSIsInNpbmdsZSIsImNvZGUiLCJfeDMiLCJ1cGRhdGVzIiwiX3RoaXMyIiwiT2JqZWN0IiwiYXNzaWduIiwiZm9yRWFjaCIsImxpc3RlbmVyIiwiY2FsbGJhY2siLCJfdGhpczMiLCJwdXNoIiwiaW5kZXgiLCJpbmRleE9mIiwic3BsaWNlIiwiZ2V0Q3VycmVudFN0YXRlIiwiX3NpZ25VcCIsIl9yZWY0Iiwic2lnblVwIiwiZW1haWwiLCJwYXNzd29yZCIsIm9wdGlvbnMiLCJmdWxsX25hbWUiLCJmdWxsTmFtZSIsInRlbm5pc19sZXZlbCIsInRlbm5pc0xldmVsIiwicGxheWluZ19zdHlsZSIsInBsYXlpbmdTdHlsZSIsImRvbWluYW50X2hhbmQiLCJkb21pbmFudEhhbmQiLCJmYXZvcml0ZV9zdXJmYWNlIiwiZmF2b3JpdGVTdXJmYWNlIiwieWVhcnNfcGxheWluZyIsInllYXJzUGxheWluZyIsImF1dGhEYXRhIiwiYXV0aEVycm9yIiwiYXBwRXJyb3IiLCJjb250ZXh0IiwidXNlck1lc3NhZ2UiLCJzdWNjZXNzIiwiX3g0IiwiX3NpZ25JbiIsIl9yZWY1Iiwic2lnbkluV2l0aFBhc3N3b3JkIiwiZXJyb3JNZXNzYWdlIiwiRXJyb3IiLCJzaWduSW4iLCJfeDUiLCJfc2lnbk91dCIsIl9yZWY2Iiwic2lnbk91dCIsIl9yZXNldFBhc3N3b3JkIiwiX3JlZjciLCJyZXNldFBhc3N3b3JkRm9yRW1haWwiLCJyZWRpcmVjdFRvIiwicmVzZXRQYXNzd29yZCIsIl94NiIsIl91cGRhdGVQcm9maWxlIiwiX3JlZjgiLCJ1cGRhdGUiLCJ1cGRhdGVkX2F0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwidXBkYXRlUHJvZmlsZSIsIl94NyIsIl91cGRhdGVMYXN0QWN0aXZlIiwibGFzdF9hY3RpdmVfYXQiLCJ1cGRhdGVMYXN0QWN0aXZlIiwiX3VwbG9hZEF2YXRhciIsImltYWdlVXJpIiwicmVzcG9uc2UiLCJmZXRjaCIsImJsb2IiLCJmaWxlRXh0Iiwic3BsaXQiLCJwb3AiLCJmaWxlTmFtZSIsIl9yZWY5IiwidXBsb2FkIiwiY2FjaGVDb250cm9sIiwidXBzZXJ0IiwiX3JlZjAiLCJnZXRQdWJsaWNVcmwiLCJwdWJsaWNVcmwiLCJhdmF0YXJfdXJsIiwidXJsIiwidXBsb2FkQXZhdGFyIiwiX3g4IiwiX2RlbGV0ZUFjY291bnQiLCJhbGVydCIsInRleHQiLCJkZWxldGVBY2NvdW50IiwiZ2V0U3VwYWJhc2VDbGllbnQiLCJpc0F1dGhlbnRpY2F0ZWQiLCJoYXNQcmVtaXVtU3Vic2NyaXB0aW9uIiwiX3RoaXMkY3VycmVudFN0YXRlJHByIiwiX3RoaXMkY3VycmVudFN0YXRlJHByMiIsInN1YnNjcmlwdGlvbl90aWVyIiwiaGFzUHJvU3Vic2NyaXB0aW9uIiwiX3RoaXMkY3VycmVudFN0YXRlJHByMyIsImF1dGhTZXJ2aWNlIl0sInNvdXJjZXMiOlsiQXV0aFNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZWFsIEF1dGhlbnRpY2F0aW9uIFNlcnZpY2VcbiAqIFxuICogUmVwbGFjZXMgbW9jayBhdXRoZW50aWNhdGlvbiB3aXRoIHJlYWwgU3VwYWJhc2UgYXV0aFxuICogSGFuZGxlcyB1c2VyIHJlZ2lzdHJhdGlvbiwgbG9naW4sIGxvZ291dCwgYW5kIHNlc3Npb24gbWFuYWdlbWVudFxuICovXG5cbmltcG9ydCB7IGNyZWF0ZUNsaWVudCwgU3VwYWJhc2VDbGllbnQsIFVzZXIsIFNlc3Npb24gfSBmcm9tICdAc3VwYWJhc2Uvc3VwYWJhc2UtanMnO1xuaW1wb3J0IEFzeW5jU3RvcmFnZSBmcm9tICdAcmVhY3QtbmF0aXZlLWFzeW5jLXN0b3JhZ2UvYXN5bmMtc3RvcmFnZSc7XG5pbXBvcnQgeyBBbGVydCB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQgeyBoYW5kbGVBdXRoRXJyb3IsIGhhbmRsZURhdGFiYXNlRXJyb3IsIGxvZ0Vycm9yIH0gZnJvbSAnQC91dGlscy9lcnJvckhhbmRsaW5nJztcblxuLy8gVHlwZXNcbmV4cG9ydCBpbnRlcmZhY2UgVXNlclByb2ZpbGUge1xuICBpZDogc3RyaW5nO1xuICBlbWFpbDogc3RyaW5nO1xuICBmdWxsX25hbWU/OiBzdHJpbmc7XG4gIHVzZXJuYW1lPzogc3RyaW5nO1xuICBhdmF0YXJfdXJsPzogc3RyaW5nO1xuICBwaG9uZT86IHN0cmluZztcbiAgZGF0ZV9vZl9iaXJ0aD86IHN0cmluZztcbiAgdGVubmlzX2xldmVsOiAnYmVnaW5uZXInIHwgJ2ludGVybWVkaWF0ZScgfCAnYWR2YW5jZWQnIHwgJ3Byb2Zlc3Npb25hbCc7XG4gIHBsYXlpbmdfc3R5bGU6ICdhZ2dyZXNzaXZlJyB8ICdkZWZlbnNpdmUnIHwgJ2FsbF9jb3VydCcgfCAnc2VydmVfdm9sbGV5JztcbiAgZG9taW5hbnRfaGFuZDogJ3JpZ2h0JyB8ICdsZWZ0JyB8ICdhbWJpZGV4dHJvdXMnO1xuICBmYXZvcml0ZV9zdXJmYWNlOiAnaGFyZCcgfCAnY2xheScgfCAnZ3Jhc3MnIHwgJ2luZG9vcic7XG4gIHllYXJzX3BsYXlpbmc6IG51bWJlcjtcbiAgcHJpbWFyeV9nb2FsPzogc3RyaW5nO1xuICB0cmFpbmluZ19mcmVxdWVuY3k6IG51bWJlcjtcbiAgcHJlZmVycmVkX3Nlc3Npb25fZHVyYXRpb246IG51bWJlcjtcbiAgY291bnRyeT86IHN0cmluZztcbiAgY2l0eT86IHN0cmluZztcbiAgdGltZXpvbmU6IHN0cmluZztcbiAgc3Vic2NyaXB0aW9uX3RpZXI6ICdmcmVlJyB8ICdwcmVtaXVtJyB8ICdwcm8nO1xuICBzdWJzY3JpcHRpb25fZXhwaXJlc19hdD86IHN0cmluZztcbiAgcHJvZmlsZV92aXNpYmlsaXR5OiAncHVibGljJyB8ICdmcmllbmRzJyB8ICdwcml2YXRlJztcbiAgYWxsb3dfZnJpZW5kX3JlcXVlc3RzOiBib29sZWFuO1xuICBzaG93X29ubGluZV9zdGF0dXM6IGJvb2xlYW47XG4gIGNyZWF0ZWRfYXQ6IHN0cmluZztcbiAgdXBkYXRlZF9hdDogc3RyaW5nO1xuICBsYXN0X2FjdGl2ZV9hdDogc3RyaW5nO1xuICBpc19hY3RpdmU6IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQXV0aFN0YXRlIHtcbiAgdXNlcjogVXNlciB8IG51bGw7XG4gIHByb2ZpbGU6IFVzZXJQcm9maWxlIHwgbnVsbDtcbiAgc2Vzc2lvbjogU2Vzc2lvbiB8IG51bGw7XG4gIGxvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFNpZ25VcERhdGEge1xuICBlbWFpbDogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xuICBmdWxsTmFtZTogc3RyaW5nO1xuICB0ZW5uaXNMZXZlbD86ICdiZWdpbm5lcicgfCAnaW50ZXJtZWRpYXRlJyB8ICdhZHZhbmNlZCcgfCAncHJvZmVzc2lvbmFsJztcbiAgcGxheWluZ1N0eWxlPzogJ2FnZ3Jlc3NpdmUnIHwgJ2RlZmVuc2l2ZScgfCAnYWxsX2NvdXJ0JyB8ICdzZXJ2ZV92b2xsZXknO1xuICBkb21pbmFudEhhbmQ/OiAncmlnaHQnIHwgJ2xlZnQnIHwgJ2FtYmlkZXh0cm91cyc7XG4gIGZhdm9yaXRlU3VyZmFjZT86ICdoYXJkJyB8ICdjbGF5JyB8ICdncmFzcycgfCAnaW5kb29yJztcbiAgeWVhcnNQbGF5aW5nPzogbnVtYmVyO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFNpZ25JbkRhdGEge1xuICBlbWFpbDogc3RyaW5nO1xuICBwYXNzd29yZDogc3RyaW5nO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFVwZGF0ZVByb2ZpbGVEYXRhIHtcbiAgZnVsbF9uYW1lPzogc3RyaW5nO1xuICB1c2VybmFtZT86IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG4gIGRhdGVfb2ZfYmlydGg/OiBzdHJpbmc7XG4gIHRlbm5pc19sZXZlbD86ICdiZWdpbm5lcicgfCAnaW50ZXJtZWRpYXRlJyB8ICdhZHZhbmNlZCcgfCAncHJvZmVzc2lvbmFsJztcbiAgcGxheWluZ19zdHlsZT86ICdhZ2dyZXNzaXZlJyB8ICdkZWZlbnNpdmUnIHwgJ2FsbF9jb3VydCcgfCAnc2VydmVfdm9sbGV5JztcbiAgZG9taW5hbnRfaGFuZD86ICdyaWdodCcgfCAnbGVmdCcgfCAnYW1iaWRleHRyb3VzJztcbiAgZmF2b3JpdGVfc3VyZmFjZT86ICdoYXJkJyB8ICdjbGF5JyB8ICdncmFzcycgfCAnaW5kb29yJztcbiAgeWVhcnNfcGxheWluZz86IG51bWJlcjtcbiAgcHJpbWFyeV9nb2FsPzogc3RyaW5nO1xuICB0cmFpbmluZ19mcmVxdWVuY3k/OiBudW1iZXI7XG4gIHByZWZlcnJlZF9zZXNzaW9uX2R1cmF0aW9uPzogbnVtYmVyO1xuICBjb3VudHJ5Pzogc3RyaW5nO1xuICBjaXR5Pzogc3RyaW5nO1xuICBwcm9maWxlX3Zpc2liaWxpdHk/OiAncHVibGljJyB8ICdmcmllbmRzJyB8ICdwcml2YXRlJztcbiAgYWxsb3dfZnJpZW5kX3JlcXVlc3RzPzogYm9vbGVhbjtcbiAgc2hvd19vbmxpbmVfc3RhdHVzPzogYm9vbGVhbjtcbn1cblxuY2xhc3MgQXV0aFNlcnZpY2Uge1xuICBwcml2YXRlIHN1cGFiYXNlOiBTdXBhYmFzZUNsaWVudDtcbiAgcHJpdmF0ZSBhdXRoU3RhdGVMaXN0ZW5lcnM6ICgoc3RhdGU6IEF1dGhTdGF0ZSkgPT4gdm9pZClbXSA9IFtdO1xuICBwcml2YXRlIGN1cnJlbnRTdGF0ZTogQXV0aFN0YXRlID0ge1xuICAgIHVzZXI6IG51bGwsXG4gICAgcHJvZmlsZTogbnVsbCxcbiAgICBzZXNzaW9uOiBudWxsLFxuICAgIGxvYWRpbmc6IHRydWUsXG4gICAgZXJyb3I6IG51bGwsXG4gIH07XG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgLy8gSW5pdGlhbGl6ZSBTdXBhYmFzZSBjbGllbnRcbiAgICBjb25zdCBzdXBhYmFzZVVybCA9IHByb2Nlc3MuZW52LkVYUE9fUFVCTElDX1NVUEFCQVNFX1VSTDtcbiAgICBjb25zdCBzdXBhYmFzZUFub25LZXkgPSBwcm9jZXNzLmVudi5FWFBPX1BVQkxJQ19TVVBBQkFTRV9BTk9OX0tFWTtcblxuICAgIGlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlQW5vbktleSkge1xuICAgICAgY29uc29sZS5lcnJvcignTWlzc2luZyBTdXBhYmFzZSBjb25maWd1cmF0aW9uJyk7XG4gICAgICB0aGlzLmN1cnJlbnRTdGF0ZS5lcnJvciA9ICdBdXRoZW50aWNhdGlvbiBzZXJ2aWNlIG5vdCBjb25maWd1cmVkJztcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0aGlzLnN1cGFiYXNlID0gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUFub25LZXksIHtcbiAgICAgIGF1dGg6IHtcbiAgICAgICAgc3RvcmFnZTogQXN5bmNTdG9yYWdlLFxuICAgICAgICBhdXRvUmVmcmVzaFRva2VuOiB0cnVlLFxuICAgICAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcbiAgICAgICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiBmYWxzZSxcbiAgICAgIH0sXG4gICAgfSk7XG5cbiAgICB0aGlzLmluaXRpYWxpemVBdXRoKCk7XG4gIH1cblxuICAvKipcbiAgICogSW5pdGlhbGl6ZSBhdXRoZW50aWNhdGlvbiBzdGF0ZVxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBpbml0aWFsaXplQXV0aCgpIHtcbiAgICB0cnkge1xuICAgICAgLy8gR2V0IGluaXRpYWwgc2Vzc2lvblxuICAgICAgY29uc3QgeyBkYXRhOiB7IHNlc3Npb24gfSwgZXJyb3IgfSA9IGF3YWl0IHRoaXMuc3VwYWJhc2UuYXV0aC5nZXRTZXNzaW9uKCk7XG4gICAgICBcbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBnZXR0aW5nIHNlc3Npb246JywgZXJyb3IpO1xuICAgICAgICB0aGlzLnVwZGF0ZVN0YXRlKHsgZXJyb3I6IGVycm9yLm1lc3NhZ2UsIGxvYWRpbmc6IGZhbHNlIH0pO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIGlmIChzZXNzaW9uPy51c2VyKSB7XG4gICAgICAgIGF3YWl0IHRoaXMubG9hZFVzZXJQcm9maWxlKHNlc3Npb24udXNlci5pZCk7XG4gICAgICAgIHRoaXMudXBkYXRlU3RhdGUoe1xuICAgICAgICAgIHVzZXI6IHNlc3Npb24udXNlcixcbiAgICAgICAgICBzZXNzaW9uLFxuICAgICAgICAgIGxvYWRpbmc6IGZhbHNlLFxuICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICB9KTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBsb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgIH1cblxuICAgICAgLy8gTGlzdGVuIGZvciBhdXRoIGNoYW5nZXNcbiAgICAgIHRoaXMuc3VwYWJhc2UuYXV0aC5vbkF1dGhTdGF0ZUNoYW5nZShhc3luYyAoZXZlbnQsIHNlc3Npb24pID0+IHtcbiAgICAgICAgY29uc29sZS5sb2coJ0F1dGggc3RhdGUgY2hhbmdlZDonLCBldmVudCk7XG4gICAgICAgIFxuICAgICAgICBpZiAoZXZlbnQgPT09ICdTSUdORURfSU4nICYmIHNlc3Npb24/LnVzZXIpIHtcbiAgICAgICAgICBhd2FpdCB0aGlzLmxvYWRVc2VyUHJvZmlsZShzZXNzaW9uLnVzZXIuaWQpO1xuICAgICAgICAgIHRoaXMudXBkYXRlU3RhdGUoe1xuICAgICAgICAgICAgdXNlcjogc2Vzc2lvbi51c2VyLFxuICAgICAgICAgICAgc2Vzc2lvbixcbiAgICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2UgaWYgKGV2ZW50ID09PSAnU0lHTkVEX09VVCcpIHtcbiAgICAgICAgICB0aGlzLnVwZGF0ZVN0YXRlKHtcbiAgICAgICAgICAgIHVzZXI6IG51bGwsXG4gICAgICAgICAgICBwcm9maWxlOiBudWxsLFxuICAgICAgICAgICAgc2Vzc2lvbjogbnVsbCxcbiAgICAgICAgICAgIGVycm9yOiBudWxsLFxuICAgICAgICAgIH0pO1xuICAgICAgICB9IGVsc2UgaWYgKGV2ZW50ID09PSAnVE9LRU5fUkVGUkVTSEVEJyAmJiBzZXNzaW9uKSB7XG4gICAgICAgICAgdGhpcy51cGRhdGVTdGF0ZSh7IHNlc3Npb24gfSk7XG4gICAgICAgIH1cbiAgICAgIH0pO1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluaXRpYWxpemluZyBhdXRoOicsIGVycm9yKTtcbiAgICAgIHRoaXMudXBkYXRlU3RhdGUoe1xuICAgICAgICBlcnJvcjogJ0ZhaWxlZCB0byBpbml0aWFsaXplIGF1dGhlbnRpY2F0aW9uJyxcbiAgICAgICAgbG9hZGluZzogZmFsc2UsXG4gICAgICB9KTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogTG9hZCB1c2VyIHByb2ZpbGUgZnJvbSBkYXRhYmFzZVxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBsb2FkVXNlclByb2ZpbGUodXNlcklkOiBzdHJpbmcpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgeyBkYXRhOiBwcm9maWxlLCBlcnJvciB9ID0gYXdhaXQgdGhpcy5zdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcl9wcm9maWxlcycpXG4gICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAuZXEoJ2lkJywgdXNlcklkKVxuICAgICAgICAuc2luZ2xlKCk7XG5cbiAgICAgIGlmIChlcnJvciAmJiBlcnJvci5jb2RlICE9PSAnUEdSU1QxMTYnKSB7IC8vIFBHUlNUMTE2ID0gbm8gcm93cyByZXR1cm5lZFxuICAgICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHByb2ZpbGU6JywgZXJyb3IpO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG5cbiAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBwcm9maWxlIH0pO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBsb2FkaW5nIHVzZXIgcHJvZmlsZTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSBhdXRoZW50aWNhdGlvbiBzdGF0ZSBhbmQgbm90aWZ5IGxpc3RlbmVyc1xuICAgKi9cbiAgcHJpdmF0ZSB1cGRhdGVTdGF0ZSh1cGRhdGVzOiBQYXJ0aWFsPEF1dGhTdGF0ZT4pIHtcbiAgICB0aGlzLmN1cnJlbnRTdGF0ZSA9IHsgLi4udGhpcy5jdXJyZW50U3RhdGUsIC4uLnVwZGF0ZXMgfTtcbiAgICB0aGlzLmF1dGhTdGF0ZUxpc3RlbmVycy5mb3JFYWNoKGxpc3RlbmVyID0+IGxpc3RlbmVyKHRoaXMuY3VycmVudFN0YXRlKSk7XG4gIH1cblxuICAvKipcbiAgICogU3Vic2NyaWJlIHRvIGF1dGhlbnRpY2F0aW9uIHN0YXRlIGNoYW5nZXNcbiAgICovXG4gIG9uQXV0aFN0YXRlQ2hhbmdlKGNhbGxiYWNrOiAoc3RhdGU6IEF1dGhTdGF0ZSkgPT4gdm9pZCk6ICgpID0+IHZvaWQge1xuICAgIHRoaXMuYXV0aFN0YXRlTGlzdGVuZXJzLnB1c2goY2FsbGJhY2spO1xuICAgIFxuICAgIC8vIEltbWVkaWF0ZWx5IGNhbGwgd2l0aCBjdXJyZW50IHN0YXRlXG4gICAgY2FsbGJhY2sodGhpcy5jdXJyZW50U3RhdGUpO1xuICAgIFxuICAgIC8vIFJldHVybiB1bnN1YnNjcmliZSBmdW5jdGlvblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBjb25zdCBpbmRleCA9IHRoaXMuYXV0aFN0YXRlTGlzdGVuZXJzLmluZGV4T2YoY2FsbGJhY2spO1xuICAgICAgaWYgKGluZGV4ID4gLTEpIHtcbiAgICAgICAgdGhpcy5hdXRoU3RhdGVMaXN0ZW5lcnMuc3BsaWNlKGluZGV4LCAxKTtcbiAgICAgIH1cbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIEdldCBjdXJyZW50IGF1dGhlbnRpY2F0aW9uIHN0YXRlXG4gICAqL1xuICBnZXRDdXJyZW50U3RhdGUoKTogQXV0aFN0YXRlIHtcbiAgICByZXR1cm4gdGhpcy5jdXJyZW50U3RhdGU7XG4gIH1cblxuICAvKipcbiAgICogU2lnbiB1cCBuZXcgdXNlclxuICAgKi9cbiAgYXN5bmMgc2lnblVwKGRhdGE6IFNpZ25VcERhdGEpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT4ge1xuICAgIHRyeSB7XG4gICAgICB0aGlzLnVwZGF0ZVN0YXRlKHsgbG9hZGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSk7XG5cbiAgICAgIGNvbnN0IHsgZGF0YTogYXV0aERhdGEsIGVycm9yOiBhdXRoRXJyb3IgfSA9IGF3YWl0IHRoaXMuc3VwYWJhc2UuYXV0aC5zaWduVXAoe1xuICAgICAgICBlbWFpbDogZGF0YS5lbWFpbCxcbiAgICAgICAgcGFzc3dvcmQ6IGRhdGEucGFzc3dvcmQsXG4gICAgICAgIG9wdGlvbnM6IHtcbiAgICAgICAgICBkYXRhOiB7XG4gICAgICAgICAgICBmdWxsX25hbWU6IGRhdGEuZnVsbE5hbWUsXG4gICAgICAgICAgICB0ZW5uaXNfbGV2ZWw6IGRhdGEudGVubmlzTGV2ZWwgfHwgJ2JlZ2lubmVyJyxcbiAgICAgICAgICAgIHBsYXlpbmdfc3R5bGU6IGRhdGEucGxheWluZ1N0eWxlIHx8ICdhbGxfY291cnQnLFxuICAgICAgICAgICAgZG9taW5hbnRfaGFuZDogZGF0YS5kb21pbmFudEhhbmQgfHwgJ3JpZ2h0JyxcbiAgICAgICAgICAgIGZhdm9yaXRlX3N1cmZhY2U6IGRhdGEuZmF2b3JpdGVTdXJmYWNlIHx8ICdoYXJkJyxcbiAgICAgICAgICAgIHllYXJzX3BsYXlpbmc6IGRhdGEueWVhcnNQbGF5aW5nIHx8IDAsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoYXV0aEVycm9yKSB7XG4gICAgICAgIGNvbnN0IGFwcEVycm9yID0gaGFuZGxlQXV0aEVycm9yKGF1dGhFcnJvcik7XG4gICAgICAgIGxvZ0Vycm9yKGFwcEVycm9yLCB7IGNvbnRleHQ6ICdzaWduVXAnLCBlbWFpbDogZGF0YS5lbWFpbCB9KTtcbiAgICAgICAgdGhpcy51cGRhdGVTdGF0ZSh7IGVycm9yOiBhcHBFcnJvci51c2VyTWVzc2FnZSwgbG9hZGluZzogZmFsc2UgfSk7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogYXBwRXJyb3IudXNlck1lc3NhZ2UgfTtcbiAgICAgIH1cblxuICAgICAgLy8gSWYgdXNlciBpcyBjcmVhdGVkIGJ1dCBuZWVkcyBlbWFpbCBjb25maXJtYXRpb25cbiAgICAgIGlmIChhdXRoRGF0YS51c2VyICYmICFhdXRoRGF0YS5zZXNzaW9uKSB7XG4gICAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBsb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICBzdWNjZXNzOiB0cnVlLFxuICAgICAgICAgIGVycm9yOiAnUGxlYXNlIGNoZWNrIHlvdXIgZW1haWwgdG8gY29uZmlybSB5b3VyIGFjY291bnQnXG4gICAgICAgIH07XG4gICAgICB9XG5cbiAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBsb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBhcHBFcnJvciA9IGhhbmRsZUF1dGhFcnJvcihlcnJvcik7XG4gICAgICBsb2dFcnJvcihhcHBFcnJvciwgeyBjb250ZXh0OiAnc2lnblVwJywgZW1haWw6IGRhdGEuZW1haWwgfSk7XG4gICAgICB0aGlzLnVwZGF0ZVN0YXRlKHsgZXJyb3I6IGFwcEVycm9yLnVzZXJNZXNzYWdlLCBsb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogYXBwRXJyb3IudXNlck1lc3NhZ2UgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2lnbiBpbiB1c2VyXG4gICAqL1xuICBhc3luYyBzaWduSW4oZGF0YTogU2lnbkluRGF0YSk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBsb2FkaW5nOiB0cnVlLCBlcnJvcjogbnVsbCB9KTtcblxuICAgICAgY29uc3QgeyBkYXRhOiBhdXRoRGF0YSwgZXJyb3I6IGF1dGhFcnJvciB9ID0gYXdhaXQgdGhpcy5zdXBhYmFzZS5hdXRoLnNpZ25JbldpdGhQYXNzd29yZCh7XG4gICAgICAgIGVtYWlsOiBkYXRhLmVtYWlsLFxuICAgICAgICBwYXNzd29yZDogZGF0YS5wYXNzd29yZCxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoYXV0aEVycm9yKSB7XG4gICAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBlcnJvcjogYXV0aEVycm9yLm1lc3NhZ2UsIGxvYWRpbmc6IGZhbHNlIH0pO1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGF1dGhFcnJvci5tZXNzYWdlIH07XG4gICAgICB9XG5cbiAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBsb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdTaWduIGluIGZhaWxlZCc7XG4gICAgICB0aGlzLnVwZGF0ZVN0YXRlKHsgZXJyb3I6IGVycm9yTWVzc2FnZSwgbG9hZGluZzogZmFsc2UgfSk7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTaWduIG91dCB1c2VyXG4gICAqL1xuICBhc3luYyBzaWduT3V0KCk6IFByb21pc2U8eyBzdWNjZXNzOiBib29sZWFuOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBsb2FkaW5nOiB0cnVlLCBlcnJvcjogbnVsbCB9KTtcblxuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgdGhpcy5zdXBhYmFzZS5hdXRoLnNpZ25PdXQoKTtcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBlcnJvcjogZXJyb3IubWVzc2FnZSwgbG9hZGluZzogZmFsc2UgfSk7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3IubWVzc2FnZSB9O1xuICAgICAgfVxuXG4gICAgICB0aGlzLnVwZGF0ZVN0YXRlKHsgbG9hZGluZzogZmFsc2UgfSk7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnU2lnbiBvdXQgZmFpbGVkJztcbiAgICAgIHRoaXMudXBkYXRlU3RhdGUoeyBlcnJvcjogZXJyb3JNZXNzYWdlLCBsb2FkaW5nOiBmYWxzZSB9KTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFJlc2V0IHBhc3N3b3JkXG4gICAqL1xuICBhc3luYyByZXNldFBhc3N3b3JkKGVtYWlsOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCB7IGVycm9yIH0gPSBhd2FpdCB0aGlzLnN1cGFiYXNlLmF1dGgucmVzZXRQYXNzd29yZEZvckVtYWlsKGVtYWlsLCB7XG4gICAgICAgIHJlZGlyZWN0VG86ICdhY2VtaW5kOi8vcmVzZXQtcGFzc3dvcmQnLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Bhc3N3b3JkIHJlc2V0IGZhaWxlZCc7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBVcGRhdGUgdXNlciBwcm9maWxlXG4gICAqL1xuICBhc3luYyB1cGRhdGVQcm9maWxlKHVwZGF0ZXM6IFVwZGF0ZVByb2ZpbGVEYXRhKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKCF0aGlzLmN1cnJlbnRTdGF0ZS51c2VyKSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHRoaXMuc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgICAudXBkYXRlKHtcbiAgICAgICAgICAuLi51cGRhdGVzLFxuICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICAgICAgfSlcbiAgICAgICAgLmVxKCdpZCcsIHRoaXMuY3VycmVudFN0YXRlLnVzZXIuaWQpXG4gICAgICAgIC5zZWxlY3QoKVxuICAgICAgICAuc2luZ2xlKCk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfTtcbiAgICAgIH1cblxuICAgICAgdGhpcy51cGRhdGVTdGF0ZSh7IHByb2ZpbGU6IGRhdGEgfSk7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnUHJvZmlsZSB1cGRhdGUgZmFpbGVkJztcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFVwZGF0ZSB1c2VyJ3MgbGFzdCBhY3RpdmUgdGltZXN0YW1wXG4gICAqL1xuICBhc3luYyB1cGRhdGVMYXN0QWN0aXZlKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIGlmICghdGhpcy5jdXJyZW50U3RhdGUudXNlcikgcmV0dXJuO1xuXG4gICAgdHJ5IHtcbiAgICAgIGF3YWl0IHRoaXMuc3VwYWJhc2VcbiAgICAgICAgLmZyb20oJ3VzZXJfcHJvZmlsZXMnKVxuICAgICAgICAudXBkYXRlKHsgbGFzdF9hY3RpdmVfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSB9KVxuICAgICAgICAuZXEoJ2lkJywgdGhpcy5jdXJyZW50U3RhdGUudXNlci5pZCk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHVwZGF0aW5nIGxhc3QgYWN0aXZlOicsIGVycm9yKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogVXBsb2FkIGF2YXRhciBpbWFnZVxuICAgKi9cbiAgYXN5bmMgdXBsb2FkQXZhdGFyKGltYWdlVXJpOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgdXJsPzogc3RyaW5nOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGlmICghdGhpcy5jdXJyZW50U3RhdGUudXNlcikge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJyB9O1xuICAgICAgfVxuXG4gICAgICAvLyBDb252ZXJ0IGltYWdlIFVSSSB0byBibG9iIChpbXBsZW1lbnRhdGlvbiBkZXBlbmRzIG9uIHBsYXRmb3JtKVxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChpbWFnZVVyaSk7XG4gICAgICBjb25zdCBibG9iID0gYXdhaXQgcmVzcG9uc2UuYmxvYigpO1xuICAgICAgXG4gICAgICBjb25zdCBmaWxlRXh0ID0gaW1hZ2VVcmkuc3BsaXQoJy4nKS5wb3AoKTtcbiAgICAgIGNvbnN0IGZpbGVOYW1lID0gYCR7dGhpcy5jdXJyZW50U3RhdGUudXNlci5pZH0vYXZhdGFyLiR7ZmlsZUV4dH1gO1xuXG4gICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCB0aGlzLnN1cGFiYXNlLnN0b3JhZ2VcbiAgICAgICAgLmZyb20oJ2F2YXRhcnMnKVxuICAgICAgICAudXBsb2FkKGZpbGVOYW1lLCBibG9iLCB7XG4gICAgICAgICAgY2FjaGVDb250cm9sOiAnMzYwMCcsXG4gICAgICAgICAgdXBzZXJ0OiB0cnVlLFxuICAgICAgICB9KTtcblxuICAgICAgaWYgKGVycm9yKSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3IubWVzc2FnZSB9O1xuICAgICAgfVxuXG4gICAgICAvLyBHZXQgcHVibGljIFVSTFxuICAgICAgY29uc3QgeyBkYXRhOiB7IHB1YmxpY1VybCB9IH0gPSB0aGlzLnN1cGFiYXNlLnN0b3JhZ2VcbiAgICAgICAgLmZyb20oJ2F2YXRhcnMnKVxuICAgICAgICAuZ2V0UHVibGljVXJsKGZpbGVOYW1lKTtcblxuICAgICAgLy8gVXBkYXRlIHByb2ZpbGUgd2l0aCBuZXcgYXZhdGFyIFVSTFxuICAgICAgYXdhaXQgdGhpcy51cGRhdGVQcm9maWxlKHsgYXZhdGFyX3VybDogcHVibGljVXJsIH0pO1xuXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCB1cmw6IHB1YmxpY1VybCB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0F2YXRhciB1cGxvYWQgZmFpbGVkJztcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIERlbGV0ZSB1c2VyIGFjY291bnRcbiAgICovXG4gIGFzeW5jIGRlbGV0ZUFjY291bnQoKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKCF0aGlzLmN1cnJlbnRTdGF0ZS51c2VyKSB7XG4gICAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogJ1VzZXIgbm90IGF1dGhlbnRpY2F0ZWQnIH07XG4gICAgICB9XG5cbiAgICAgIC8vIE5vdGU6IFN1cGFiYXNlIGRvZXNuJ3QgaGF2ZSBhIGRpcmVjdCBkZWxldGUgdXNlciBtZXRob2QgaW4gdGhlIGNsaWVudFxuICAgICAgLy8gVGhpcyB3b3VsZCB0eXBpY2FsbHkgYmUgaGFuZGxlZCBieSBhIHNlcnZlciBmdW5jdGlvblxuICAgICAgQWxlcnQuYWxlcnQoXG4gICAgICAgICdEZWxldGUgQWNjb3VudCcsXG4gICAgICAgICdQbGVhc2UgY29udGFjdCBzdXBwb3J0IHRvIGRlbGV0ZSB5b3VyIGFjY291bnQuJyxcbiAgICAgICAgW3sgdGV4dDogJ09LJyB9XVxuICAgICAgKTtcblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiAnUGxlYXNlIGNvbnRhY3Qgc3VwcG9ydCB0byBkZWxldGUgeW91ciBhY2NvdW50JyB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ0FjY291bnQgZGVsZXRpb24gZmFpbGVkJztcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogZXJyb3JNZXNzYWdlIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBTdXBhYmFzZSBjbGllbnQgZm9yIGRpcmVjdCBkYXRhYmFzZSBvcGVyYXRpb25zXG4gICAqL1xuICBnZXRTdXBhYmFzZUNsaWVudCgpOiBTdXBhYmFzZUNsaWVudCB7XG4gICAgcmV0dXJuIHRoaXMuc3VwYWJhc2U7XG4gIH1cblxuICAvKipcbiAgICogQ2hlY2sgaWYgdXNlciBpcyBhdXRoZW50aWNhdGVkXG4gICAqL1xuICBpc0F1dGhlbnRpY2F0ZWQoKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuICEhdGhpcy5jdXJyZW50U3RhdGUudXNlciAmJiAhIXRoaXMuY3VycmVudFN0YXRlLnNlc3Npb247XG4gIH1cblxuICAvKipcbiAgICogQ2hlY2sgaWYgdXNlciBoYXMgcHJlbWl1bSBzdWJzY3JpcHRpb25cbiAgICovXG4gIGhhc1ByZW1pdW1TdWJzY3JpcHRpb24oKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMuY3VycmVudFN0YXRlLnByb2ZpbGU/LnN1YnNjcmlwdGlvbl90aWVyID09PSAncHJlbWl1bScgfHwgXG4gICAgICAgICAgIHRoaXMuY3VycmVudFN0YXRlLnByb2ZpbGU/LnN1YnNjcmlwdGlvbl90aWVyID09PSAncHJvJztcbiAgfVxuXG4gIC8qKlxuICAgKiBDaGVjayBpZiB1c2VyIGhhcyBwcm8gc3Vic2NyaXB0aW9uXG4gICAqL1xuICBoYXNQcm9TdWJzY3JpcHRpb24oKTogYm9vbGVhbiB7XG4gICAgcmV0dXJuIHRoaXMuY3VycmVudFN0YXRlLnByb2ZpbGU/LnN1YnNjcmlwdGlvbl90aWVyID09PSAncHJvJztcbiAgfVxufVxuXG4vLyBFeHBvcnQgc2luZ2xldG9uIGluc3RhbmNlXG5leHBvcnQgY29uc3QgYXV0aFNlcnZpY2UgPSBuZXcgQXV0aFNlcnZpY2UoKTtcbmV4cG9ydCBkZWZhdWx0IGF1dGhTZXJ2aWNlO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPQSxTQUFTQSxZQUFZLFFBQXVDLHVCQUF1QjtBQUNuRixPQUFPQyxZQUFZLE1BQU0sMkNBQTJDO0FBQ3BFLFNBQVNDLEtBQUssUUFBUSxjQUFjO0FBQ3BDLFNBQVNDLGVBQWUsRUFBdUJDLFFBQVE7QUFBZ0MsSUE2RWpGQyxXQUFXO0VBV2YsU0FBQUEsWUFBQSxFQUFjO0lBQUFDLGVBQUEsT0FBQUQsV0FBQTtJQUFBLEtBVE5FLGtCQUFrQixJQUFBQyxjQUFBLEdBQUFDLENBQUEsT0FBbUMsRUFBRTtJQUFBLEtBQ3ZEQyxZQUFZLElBQUFGLGNBQUEsR0FBQUMsQ0FBQSxPQUFjO01BQ2hDRSxJQUFJLEVBQUUsSUFBSTtNQUNWQyxPQUFPLEVBQUUsSUFBSTtNQUNiQyxPQUFPLEVBQUUsSUFBSTtNQUNiQyxPQUFPLEVBQUUsSUFBSTtNQUNiQyxLQUFLLEVBQUU7SUFDVCxDQUFDO0lBQUFQLGNBQUEsR0FBQVEsQ0FBQTtJQUlDLElBQU1DLFdBQVcsSUFBQVQsY0FBQSxHQUFBQyxDQUFBLE9BQUFTLElBQUEsQ0FBQUMsd0JBQUEsQ0FBdUM7SUFDeEQsSUFBTUMsZUFBZSxJQUFBWixjQUFBLEdBQUFDLENBQUEsT0FBQVMsSUFBQSxDQUFBRyw2QkFBQSxDQUE0QztJQUFDYixjQUFBLEdBQUFDLENBQUE7SUFFbEUsSUFBSSxDQUFBRCxjQUFBLEdBQUFjLENBQUEsV0FBQ0wsV0FBVyxNQUFBVCxjQUFBLEdBQUFjLENBQUEsVUFBSSxDQUFDRixlQUFlLEdBQUU7TUFBQVosY0FBQSxHQUFBYyxDQUFBO01BQUFkLGNBQUEsR0FBQUMsQ0FBQTtNQUNwQ2MsT0FBTyxDQUFDUixLQUFLLENBQUMsZ0NBQWdDLENBQUM7TUFBQ1AsY0FBQSxHQUFBQyxDQUFBO01BQ2hELElBQUksQ0FBQ0MsWUFBWSxDQUFDSyxLQUFLLEdBQUcsdUNBQXVDO01BQUNQLGNBQUEsR0FBQUMsQ0FBQTtNQUNsRTtJQUNGLENBQUM7TUFBQUQsY0FBQSxHQUFBYyxDQUFBO0lBQUE7SUFBQWQsY0FBQSxHQUFBQyxDQUFBO0lBRUQsSUFBSSxDQUFDZSxRQUFRLEdBQUd4QixZQUFZLENBQUNpQixXQUFXLEVBQUVHLGVBQWUsRUFBRTtNQUN6REssSUFBSSxFQUFFO1FBQ0pDLE9BQU8sRUFBRXpCLFlBQVk7UUFDckIwQixnQkFBZ0IsRUFBRSxJQUFJO1FBQ3RCQyxjQUFjLEVBQUUsSUFBSTtRQUNwQkMsa0JBQWtCLEVBQUU7TUFDdEI7SUFDRixDQUFDLENBQUM7SUFBQ3JCLGNBQUEsR0FBQUMsQ0FBQTtJQUVILElBQUksQ0FBQ3FCLGNBQWMsQ0FBQyxDQUFDO0VBQ3ZCO0VBQUMsT0FBQUMsWUFBQSxDQUFBMUIsV0FBQTtJQUFBMkIsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQUMsZUFBQSxHQUFBQyxpQkFBQSxDQUtELGFBQStCO1FBQUEsSUFBQUMsS0FBQTtRQUFBNUIsY0FBQSxHQUFBUSxDQUFBO1FBQUFSLGNBQUEsR0FBQUMsQ0FBQTtRQUM3QixJQUFJO1VBRUYsSUFBQTRCLElBQUEsSUFBQTdCLGNBQUEsR0FBQUMsQ0FBQSxjQUEyQyxJQUFJLENBQUNlLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDYSxVQUFVLENBQUMsQ0FBQztZQUExRHpCLE9BQU8sR0FBQXdCLElBQUEsQ0FBZkUsSUFBSSxDQUFJMUIsT0FBTztZQUFJRSxLQUFLLEdBQUFzQixJQUFBLENBQUx0QixLQUFLO1VBQTJDUCxjQUFBLEdBQUFDLENBQUE7VUFFM0UsSUFBSU0sS0FBSyxFQUFFO1lBQUFQLGNBQUEsR0FBQWMsQ0FBQTtZQUFBZCxjQUFBLEdBQUFDLENBQUE7WUFDVGMsT0FBTyxDQUFDUixLQUFLLENBQUMsd0JBQXdCLEVBQUVBLEtBQUssQ0FBQztZQUFDUCxjQUFBLEdBQUFDLENBQUE7WUFDL0MsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO2NBQUV6QixLQUFLLEVBQUVBLEtBQUssQ0FBQzBCLE9BQU87Y0FBRTNCLE9BQU8sRUFBRTtZQUFNLENBQUMsQ0FBQztZQUFDTixjQUFBLEdBQUFDLENBQUE7WUFDM0Q7VUFDRixDQUFDO1lBQUFELGNBQUEsR0FBQWMsQ0FBQTtVQUFBO1VBQUFkLGNBQUEsR0FBQUMsQ0FBQTtVQUVELElBQUlJLE9BQU8sWUFBUEEsT0FBTyxDQUFFRixJQUFJLEVBQUU7WUFBQUgsY0FBQSxHQUFBYyxDQUFBO1lBQUFkLGNBQUEsR0FBQUMsQ0FBQTtZQUNqQixNQUFNLElBQUksQ0FBQ2lDLGVBQWUsQ0FBQzdCLE9BQU8sQ0FBQ0YsSUFBSSxDQUFDZ0MsRUFBRSxDQUFDO1lBQUNuQyxjQUFBLEdBQUFDLENBQUE7WUFDNUMsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO2NBQ2Y3QixJQUFJLEVBQUVFLE9BQU8sQ0FBQ0YsSUFBSTtjQUNsQkUsT0FBTyxFQUFQQSxPQUFPO2NBQ1BDLE9BQU8sRUFBRSxLQUFLO2NBQ2RDLEtBQUssRUFBRTtZQUNULENBQUMsQ0FBQztVQUNKLENBQUMsTUFBTTtZQUFBUCxjQUFBLEdBQUFjLENBQUE7WUFBQWQsY0FBQSxHQUFBQyxDQUFBO1lBQ0wsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO2NBQUUxQixPQUFPLEVBQUU7WUFBTSxDQUFDLENBQUM7VUFDdEM7VUFBQ04sY0FBQSxHQUFBQyxDQUFBO1VBR0QsSUFBSSxDQUFDZSxRQUFRLENBQUNDLElBQUksQ0FBQ21CLGlCQUFpQjtZQUFBLElBQUFDLEtBQUEsR0FBQVYsaUJBQUEsQ0FBQyxXQUFPVyxLQUFLLEVBQUVqQyxPQUFPLEVBQUs7Y0FBQUwsY0FBQSxHQUFBUSxDQUFBO2NBQUFSLGNBQUEsR0FBQUMsQ0FBQTtjQUM3RGMsT0FBTyxDQUFDd0IsR0FBRyxDQUFDLHFCQUFxQixFQUFFRCxLQUFLLENBQUM7Y0FBQ3RDLGNBQUEsR0FBQUMsQ0FBQTtjQUUxQyxJQUFJLENBQUFELGNBQUEsR0FBQWMsQ0FBQSxVQUFBd0IsS0FBSyxLQUFLLFdBQVcsTUFBQXRDLGNBQUEsR0FBQWMsQ0FBQSxVQUFJVCxPQUFPLFlBQVBBLE9BQU8sQ0FBRUYsSUFBSSxHQUFFO2dCQUFBSCxjQUFBLEdBQUFjLENBQUE7Z0JBQUFkLGNBQUEsR0FBQUMsQ0FBQTtnQkFDMUMsTUFBTTJCLEtBQUksQ0FBQ00sZUFBZSxDQUFDN0IsT0FBTyxDQUFDRixJQUFJLENBQUNnQyxFQUFFLENBQUM7Z0JBQUNuQyxjQUFBLEdBQUFDLENBQUE7Z0JBQzVDMkIsS0FBSSxDQUFDSSxXQUFXLENBQUM7a0JBQ2Y3QixJQUFJLEVBQUVFLE9BQU8sQ0FBQ0YsSUFBSTtrQkFDbEJFLE9BQU8sRUFBUEEsT0FBTztrQkFDUEUsS0FBSyxFQUFFO2dCQUNULENBQUMsQ0FBQztjQUNKLENBQUMsTUFBTTtnQkFBQVAsY0FBQSxHQUFBYyxDQUFBO2dCQUFBZCxjQUFBLEdBQUFDLENBQUE7Z0JBQUEsSUFBSXFDLEtBQUssS0FBSyxZQUFZLEVBQUU7a0JBQUF0QyxjQUFBLEdBQUFjLENBQUE7a0JBQUFkLGNBQUEsR0FBQUMsQ0FBQTtrQkFDakMyQixLQUFJLENBQUNJLFdBQVcsQ0FBQztvQkFDZjdCLElBQUksRUFBRSxJQUFJO29CQUNWQyxPQUFPLEVBQUUsSUFBSTtvQkFDYkMsT0FBTyxFQUFFLElBQUk7b0JBQ2JFLEtBQUssRUFBRTtrQkFDVCxDQUFDLENBQUM7Z0JBQ0osQ0FBQyxNQUFNO2tCQUFBUCxjQUFBLEdBQUFjLENBQUE7a0JBQUFkLGNBQUEsR0FBQUMsQ0FBQTtrQkFBQSxJQUFJLENBQUFELGNBQUEsR0FBQWMsQ0FBQSxVQUFBd0IsS0FBSyxLQUFLLGlCQUFpQixNQUFBdEMsY0FBQSxHQUFBYyxDQUFBLFVBQUlULE9BQU8sR0FBRTtvQkFBQUwsY0FBQSxHQUFBYyxDQUFBO29CQUFBZCxjQUFBLEdBQUFDLENBQUE7b0JBQ2pEMkIsS0FBSSxDQUFDSSxXQUFXLENBQUM7c0JBQUUzQixPQUFPLEVBQVBBO29CQUFRLENBQUMsQ0FBQztrQkFDL0IsQ0FBQztvQkFBQUwsY0FBQSxHQUFBYyxDQUFBO2tCQUFBO2dCQUFEO2NBQUE7WUFDRixDQUFDO1lBQUEsaUJBQUEwQixFQUFBLEVBQUFDLEdBQUE7Y0FBQSxPQUFBSixLQUFBLENBQUFLLEtBQUEsT0FBQUMsU0FBQTtZQUFBO1VBQUEsSUFBQztRQUVKLENBQUMsQ0FBQyxPQUFPcEMsS0FBSyxFQUFFO1VBQUFQLGNBQUEsR0FBQUMsQ0FBQTtVQUNkYyxPQUFPLENBQUNSLEtBQUssQ0FBQywwQkFBMEIsRUFBRUEsS0FBSyxDQUFDO1VBQUNQLGNBQUEsR0FBQUMsQ0FBQTtVQUNqRCxJQUFJLENBQUMrQixXQUFXLENBQUM7WUFDZnpCLEtBQUssRUFBRSxxQ0FBcUM7WUFDNUNELE9BQU8sRUFBRTtVQUNYLENBQUMsQ0FBQztRQUNKO01BQ0YsQ0FBQztNQUFBLFNBckRhZ0IsY0FBY0EsQ0FBQTtRQUFBLE9BQUFJLGVBQUEsQ0FBQWdCLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBZHJCLGNBQWM7SUFBQTtFQUFBO0lBQUFFLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFtQixnQkFBQSxHQUFBakIsaUJBQUEsQ0EwRDVCLFdBQThCa0IsTUFBYyxFQUFpQjtRQUFBN0MsY0FBQSxHQUFBUSxDQUFBO1FBQUFSLGNBQUEsR0FBQUMsQ0FBQTtRQUMzRCxJQUFJO1VBQ0YsSUFBQTZDLEtBQUEsSUFBQTlDLGNBQUEsR0FBQUMsQ0FBQSxjQUF1QyxJQUFJLENBQUNlLFFBQVEsQ0FDakQrQixJQUFJLENBQUMsZUFBZSxDQUFDLENBQ3JCQyxNQUFNLENBQUMsR0FBRyxDQUFDLENBQ1hDLEVBQUUsQ0FBQyxJQUFJLEVBQUVKLE1BQU0sQ0FBQyxDQUNoQkssTUFBTSxDQUFDLENBQUM7WUFKRzlDLE9BQU8sR0FBQTBDLEtBQUEsQ0FBYmYsSUFBSTtZQUFXeEIsS0FBSyxHQUFBdUMsS0FBQSxDQUFMdkMsS0FBSztVQUloQlAsY0FBQSxHQUFBQyxDQUFBO1VBRVosSUFBSSxDQUFBRCxjQUFBLEdBQUFjLENBQUEsV0FBQVAsS0FBSyxNQUFBUCxjQUFBLEdBQUFjLENBQUEsV0FBSVAsS0FBSyxDQUFDNEMsSUFBSSxLQUFLLFVBQVUsR0FBRTtZQUFBbkQsY0FBQSxHQUFBYyxDQUFBO1lBQUFkLGNBQUEsR0FBQUMsQ0FBQTtZQUN0Q2MsT0FBTyxDQUFDUixLQUFLLENBQUMsd0JBQXdCLEVBQUVBLEtBQUssQ0FBQztZQUFDUCxjQUFBLEdBQUFDLENBQUE7WUFDL0M7VUFDRixDQUFDO1lBQUFELGNBQUEsR0FBQWMsQ0FBQTtVQUFBO1VBQUFkLGNBQUEsR0FBQUMsQ0FBQTtVQUVELElBQUksQ0FBQytCLFdBQVcsQ0FBQztZQUFFNUIsT0FBTyxFQUFQQTtVQUFRLENBQUMsQ0FBQztRQUMvQixDQUFDLENBQUMsT0FBT0csS0FBSyxFQUFFO1VBQUFQLGNBQUEsR0FBQUMsQ0FBQTtVQUNkYyxPQUFPLENBQUNSLEtBQUssQ0FBQyw2QkFBNkIsRUFBRUEsS0FBSyxDQUFDO1FBQ3JEO01BQ0YsQ0FBQztNQUFBLFNBakJhMkIsZUFBZUEsQ0FBQWtCLEdBQUE7UUFBQSxPQUFBUixnQkFBQSxDQUFBRixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWZULGVBQWU7SUFBQTtFQUFBO0lBQUFWLEdBQUE7SUFBQUMsS0FBQSxFQXNCN0IsU0FBUU8sV0FBV0EsQ0FBQ3FCLE9BQTJCLEVBQUU7TUFBQSxJQUFBQyxNQUFBO01BQUF0RCxjQUFBLEdBQUFRLENBQUE7TUFBQVIsY0FBQSxHQUFBQyxDQUFBO01BQy9DLElBQUksQ0FBQ0MsWUFBWSxHQUFBcUQsTUFBQSxDQUFBQyxNQUFBLEtBQVEsSUFBSSxDQUFDdEQsWUFBWSxFQUFLbUQsT0FBTyxDQUFFO01BQUNyRCxjQUFBLEdBQUFDLENBQUE7TUFDekQsSUFBSSxDQUFDRixrQkFBa0IsQ0FBQzBELE9BQU8sQ0FBQyxVQUFBQyxRQUFRLEVBQUk7UUFBQTFELGNBQUEsR0FBQVEsQ0FBQTtRQUFBUixjQUFBLEdBQUFDLENBQUE7UUFBQSxPQUFBeUQsUUFBUSxDQUFDSixNQUFJLENBQUNwRCxZQUFZLENBQUM7TUFBRCxDQUFDLENBQUM7SUFDMUU7RUFBQztJQUFBc0IsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQVcsaUJBQWlCQSxDQUFDdUIsUUFBb0MsRUFBYztNQUFBLElBQUFDLE1BQUE7TUFBQTVELGNBQUEsR0FBQVEsQ0FBQTtNQUFBUixjQUFBLEdBQUFDLENBQUE7TUFDbEUsSUFBSSxDQUFDRixrQkFBa0IsQ0FBQzhELElBQUksQ0FBQ0YsUUFBUSxDQUFDO01BQUMzRCxjQUFBLEdBQUFDLENBQUE7TUFHdkMwRCxRQUFRLENBQUMsSUFBSSxDQUFDekQsWUFBWSxDQUFDO01BQUNGLGNBQUEsR0FBQUMsQ0FBQTtNQUc1QixPQUFPLFlBQU07UUFBQUQsY0FBQSxHQUFBUSxDQUFBO1FBQ1gsSUFBTXNELEtBQUssSUFBQTlELGNBQUEsR0FBQUMsQ0FBQSxRQUFHMkQsTUFBSSxDQUFDN0Qsa0JBQWtCLENBQUNnRSxPQUFPLENBQUNKLFFBQVEsQ0FBQztRQUFDM0QsY0FBQSxHQUFBQyxDQUFBO1FBQ3hELElBQUk2RCxLQUFLLEdBQUcsQ0FBQyxDQUFDLEVBQUU7VUFBQTlELGNBQUEsR0FBQWMsQ0FBQTtVQUFBZCxjQUFBLEdBQUFDLENBQUE7VUFDZDJELE1BQUksQ0FBQzdELGtCQUFrQixDQUFDaUUsTUFBTSxDQUFDRixLQUFLLEVBQUUsQ0FBQyxDQUFDO1FBQzFDLENBQUM7VUFBQTlELGNBQUEsR0FBQWMsQ0FBQTtRQUFBO01BQ0gsQ0FBQztJQUNIO0VBQUM7SUFBQVUsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQXdDLGVBQWVBLENBQUEsRUFBYztNQUFBakUsY0FBQSxHQUFBUSxDQUFBO01BQUFSLGNBQUEsR0FBQUMsQ0FBQTtNQUMzQixPQUFPLElBQUksQ0FBQ0MsWUFBWTtJQUMxQjtFQUFDO0lBQUFzQixHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBeUMsT0FBQSxHQUFBdkMsaUJBQUEsQ0FLRCxXQUFhSSxJQUFnQixFQUFpRDtRQUFBL0IsY0FBQSxHQUFBUSxDQUFBO1FBQUFSLGNBQUEsR0FBQUMsQ0FBQTtRQUM1RSxJQUFJO1VBQUFELGNBQUEsR0FBQUMsQ0FBQTtVQUNGLElBQUksQ0FBQytCLFdBQVcsQ0FBQztZQUFFMUIsT0FBTyxFQUFFLElBQUk7WUFBRUMsS0FBSyxFQUFFO1VBQUssQ0FBQyxDQUFDO1VBRWhELElBQUE0RCxLQUFBLElBQUFuRSxjQUFBLEdBQUFDLENBQUEsY0FBbUQsSUFBSSxDQUFDZSxRQUFRLENBQUNDLElBQUksQ0FBQ21ELE1BQU0sQ0FBQztjQUMzRUMsS0FBSyxFQUFFdEMsSUFBSSxDQUFDc0MsS0FBSztjQUNqQkMsUUFBUSxFQUFFdkMsSUFBSSxDQUFDdUMsUUFBUTtjQUN2QkMsT0FBTyxFQUFFO2dCQUNQeEMsSUFBSSxFQUFFO2tCQUNKeUMsU0FBUyxFQUFFekMsSUFBSSxDQUFDMEMsUUFBUTtrQkFDeEJDLFlBQVksRUFBRSxDQUFBMUUsY0FBQSxHQUFBYyxDQUFBLFdBQUFpQixJQUFJLENBQUM0QyxXQUFXLE1BQUEzRSxjQUFBLEdBQUFjLENBQUEsV0FBSSxVQUFVO2tCQUM1QzhELGFBQWEsRUFBRSxDQUFBNUUsY0FBQSxHQUFBYyxDQUFBLFdBQUFpQixJQUFJLENBQUM4QyxZQUFZLE1BQUE3RSxjQUFBLEdBQUFjLENBQUEsV0FBSSxXQUFXO2tCQUMvQ2dFLGFBQWEsRUFBRSxDQUFBOUUsY0FBQSxHQUFBYyxDQUFBLFdBQUFpQixJQUFJLENBQUNnRCxZQUFZLE1BQUEvRSxjQUFBLEdBQUFjLENBQUEsV0FBSSxPQUFPO2tCQUMzQ2tFLGdCQUFnQixFQUFFLENBQUFoRixjQUFBLEdBQUFjLENBQUEsV0FBQWlCLElBQUksQ0FBQ2tELGVBQWUsTUFBQWpGLGNBQUEsR0FBQWMsQ0FBQSxXQUFJLE1BQU07a0JBQ2hEb0UsYUFBYSxFQUFFLENBQUFsRixjQUFBLEdBQUFjLENBQUEsV0FBQWlCLElBQUksQ0FBQ29ELFlBQVksTUFBQW5GLGNBQUEsR0FBQWMsQ0FBQSxXQUFJLENBQUM7Z0JBQ3ZDO2NBQ0Y7WUFDRixDQUFDLENBQUM7WUFiWXNFLFFBQVEsR0FBQWpCLEtBQUEsQ0FBZHBDLElBQUk7WUFBbUJzRCxTQUFTLEdBQUFsQixLQUFBLENBQWhCNUQsS0FBSztVQWExQlAsY0FBQSxHQUFBQyxDQUFBO1VBRUgsSUFBSW9GLFNBQVMsRUFBRTtZQUFBckYsY0FBQSxHQUFBYyxDQUFBO1lBQ2IsSUFBTXdFLFFBQVEsSUFBQXRGLGNBQUEsR0FBQUMsQ0FBQSxRQUFHTixlQUFlLENBQUMwRixTQUFTLENBQUM7WUFBQ3JGLGNBQUEsR0FBQUMsQ0FBQTtZQUM1Q0wsUUFBUSxDQUFDMEYsUUFBUSxFQUFFO2NBQUVDLE9BQU8sRUFBRSxRQUFRO2NBQUVsQixLQUFLLEVBQUV0QyxJQUFJLENBQUNzQztZQUFNLENBQUMsQ0FBQztZQUFDckUsY0FBQSxHQUFBQyxDQUFBO1lBQzdELElBQUksQ0FBQytCLFdBQVcsQ0FBQztjQUFFekIsS0FBSyxFQUFFK0UsUUFBUSxDQUFDRSxXQUFXO2NBQUVsRixPQUFPLEVBQUU7WUFBTSxDQUFDLENBQUM7WUFBQ04sY0FBQSxHQUFBQyxDQUFBO1lBQ2xFLE9BQU87Y0FBRXdGLE9BQU8sRUFBRSxLQUFLO2NBQUVsRixLQUFLLEVBQUUrRSxRQUFRLENBQUNFO1lBQVksQ0FBQztVQUN4RCxDQUFDO1lBQUF4RixjQUFBLEdBQUFjLENBQUE7VUFBQTtVQUFBZCxjQUFBLEdBQUFDLENBQUE7VUFHRCxJQUFJLENBQUFELGNBQUEsR0FBQWMsQ0FBQSxXQUFBc0UsUUFBUSxDQUFDakYsSUFBSSxNQUFBSCxjQUFBLEdBQUFjLENBQUEsV0FBSSxDQUFDc0UsUUFBUSxDQUFDL0UsT0FBTyxHQUFFO1lBQUFMLGNBQUEsR0FBQWMsQ0FBQTtZQUFBZCxjQUFBLEdBQUFDLENBQUE7WUFDdEMsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO2NBQUUxQixPQUFPLEVBQUU7WUFBTSxDQUFDLENBQUM7WUFBQ04sY0FBQSxHQUFBQyxDQUFBO1lBQ3JDLE9BQU87Y0FDTHdGLE9BQU8sRUFBRSxJQUFJO2NBQ2JsRixLQUFLLEVBQUU7WUFDVCxDQUFDO1VBQ0gsQ0FBQztZQUFBUCxjQUFBLEdBQUFjLENBQUE7VUFBQTtVQUFBZCxjQUFBLEdBQUFDLENBQUE7VUFFRCxJQUFJLENBQUMrQixXQUFXLENBQUM7WUFBRTFCLE9BQU8sRUFBRTtVQUFNLENBQUMsQ0FBQztVQUFDTixjQUFBLEdBQUFDLENBQUE7VUFDckMsT0FBTztZQUFFd0YsT0FBTyxFQUFFO1VBQUssQ0FBQztRQUUxQixDQUFDLENBQUMsT0FBT2xGLEtBQUssRUFBRTtVQUNkLElBQU0rRSxTQUFRLElBQUF0RixjQUFBLEdBQUFDLENBQUEsUUFBR04sZUFBZSxDQUFDWSxLQUFLLENBQUM7VUFBQ1AsY0FBQSxHQUFBQyxDQUFBO1VBQ3hDTCxRQUFRLENBQUMwRixTQUFRLEVBQUU7WUFBRUMsT0FBTyxFQUFFLFFBQVE7WUFBRWxCLEtBQUssRUFBRXRDLElBQUksQ0FBQ3NDO1VBQU0sQ0FBQyxDQUFDO1VBQUNyRSxjQUFBLEdBQUFDLENBQUE7VUFDN0QsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO1lBQUV6QixLQUFLLEVBQUUrRSxTQUFRLENBQUNFLFdBQVc7WUFBRWxGLE9BQU8sRUFBRTtVQUFNLENBQUMsQ0FBQztVQUFDTixjQUFBLEdBQUFDLENBQUE7VUFDbEUsT0FBTztZQUFFd0YsT0FBTyxFQUFFLEtBQUs7WUFBRWxGLEtBQUssRUFBRStFLFNBQVEsQ0FBQ0U7VUFBWSxDQUFDO1FBQ3hEO01BQ0YsQ0FBQztNQUFBLFNBNUNLcEIsTUFBTUEsQ0FBQXNCLEdBQUE7UUFBQSxPQUFBeEIsT0FBQSxDQUFBeEIsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFOeUIsTUFBTTtJQUFBO0VBQUE7SUFBQTVDLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFrRSxPQUFBLEdBQUFoRSxpQkFBQSxDQWlEWixXQUFhSSxJQUFnQixFQUFpRDtRQUFBL0IsY0FBQSxHQUFBUSxDQUFBO1FBQUFSLGNBQUEsR0FBQUMsQ0FBQTtRQUM1RSxJQUFJO1VBQUFELGNBQUEsR0FBQUMsQ0FBQTtVQUNGLElBQUksQ0FBQytCLFdBQVcsQ0FBQztZQUFFMUIsT0FBTyxFQUFFLElBQUk7WUFBRUMsS0FBSyxFQUFFO1VBQUssQ0FBQyxDQUFDO1VBRWhELElBQUFxRixLQUFBLElBQUE1RixjQUFBLEdBQUFDLENBQUEsY0FBbUQsSUFBSSxDQUFDZSxRQUFRLENBQUNDLElBQUksQ0FBQzRFLGtCQUFrQixDQUFDO2NBQ3ZGeEIsS0FBSyxFQUFFdEMsSUFBSSxDQUFDc0MsS0FBSztjQUNqQkMsUUFBUSxFQUFFdkMsSUFBSSxDQUFDdUM7WUFDakIsQ0FBQyxDQUFDO1lBSFljLFFBQVEsR0FBQVEsS0FBQSxDQUFkN0QsSUFBSTtZQUFtQnNELFNBQVMsR0FBQU8sS0FBQSxDQUFoQnJGLEtBQUs7VUFHMUJQLGNBQUEsR0FBQUMsQ0FBQTtVQUVILElBQUlvRixTQUFTLEVBQUU7WUFBQXJGLGNBQUEsR0FBQWMsQ0FBQTtZQUFBZCxjQUFBLEdBQUFDLENBQUE7WUFDYixJQUFJLENBQUMrQixXQUFXLENBQUM7Y0FBRXpCLEtBQUssRUFBRThFLFNBQVMsQ0FBQ3BELE9BQU87Y0FBRTNCLE9BQU8sRUFBRTtZQUFNLENBQUMsQ0FBQztZQUFDTixjQUFBLEdBQUFDLENBQUE7WUFDL0QsT0FBTztjQUFFd0YsT0FBTyxFQUFFLEtBQUs7Y0FBRWxGLEtBQUssRUFBRThFLFNBQVMsQ0FBQ3BEO1lBQVEsQ0FBQztVQUNyRCxDQUFDO1lBQUFqQyxjQUFBLEdBQUFjLENBQUE7VUFBQTtVQUFBZCxjQUFBLEdBQUFDLENBQUE7VUFFRCxJQUFJLENBQUMrQixXQUFXLENBQUM7WUFBRTFCLE9BQU8sRUFBRTtVQUFNLENBQUMsQ0FBQztVQUFDTixjQUFBLEdBQUFDLENBQUE7VUFDckMsT0FBTztZQUFFd0YsT0FBTyxFQUFFO1VBQUssQ0FBQztRQUUxQixDQUFDLENBQUMsT0FBT2xGLEtBQUssRUFBRTtVQUNkLElBQU11RixZQUFZLElBQUE5RixjQUFBLEdBQUFDLENBQUEsUUFBR00sS0FBSyxZQUFZd0YsS0FBSyxJQUFBL0YsY0FBQSxHQUFBYyxDQUFBLFdBQUdQLEtBQUssQ0FBQzBCLE9BQU8sS0FBQWpDLGNBQUEsR0FBQWMsQ0FBQSxXQUFHLGdCQUFnQjtVQUFDZCxjQUFBLEdBQUFDLENBQUE7VUFDL0UsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO1lBQUV6QixLQUFLLEVBQUV1RixZQUFZO1lBQUV4RixPQUFPLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ04sY0FBQSxHQUFBQyxDQUFBO1VBQzFELE9BQU87WUFBRXdGLE9BQU8sRUFBRSxLQUFLO1lBQUVsRixLQUFLLEVBQUV1RjtVQUFhLENBQUM7UUFDaEQ7TUFDRixDQUFDO01BQUEsU0F0QktFLE1BQU1BLENBQUFDLEdBQUE7UUFBQSxPQUFBTixPQUFBLENBQUFqRCxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQU5xRCxNQUFNO0lBQUE7RUFBQTtJQUFBeEUsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQXlFLFFBQUEsR0FBQXZFLGlCQUFBLENBMkJaLGFBQStEO1FBQUEzQixjQUFBLEdBQUFRLENBQUE7UUFBQVIsY0FBQSxHQUFBQyxDQUFBO1FBQzdELElBQUk7VUFBQUQsY0FBQSxHQUFBQyxDQUFBO1VBQ0YsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO1lBQUUxQixPQUFPLEVBQUUsSUFBSTtZQUFFQyxLQUFLLEVBQUU7VUFBSyxDQUFDLENBQUM7VUFFaEQsSUFBQTRGLEtBQUEsSUFBQW5HLGNBQUEsR0FBQUMsQ0FBQSxjQUF3QixJQUFJLENBQUNlLFFBQVEsQ0FBQ0MsSUFBSSxDQUFDbUYsT0FBTyxDQUFDLENBQUM7WUFBNUM3RixLQUFLLEdBQUE0RixLQUFBLENBQUw1RixLQUFLO1VBQXdDUCxjQUFBLEdBQUFDLENBQUE7VUFFckQsSUFBSU0sS0FBSyxFQUFFO1lBQUFQLGNBQUEsR0FBQWMsQ0FBQTtZQUFBZCxjQUFBLEdBQUFDLENBQUE7WUFDVCxJQUFJLENBQUMrQixXQUFXLENBQUM7Y0FBRXpCLEtBQUssRUFBRUEsS0FBSyxDQUFDMEIsT0FBTztjQUFFM0IsT0FBTyxFQUFFO1lBQU0sQ0FBQyxDQUFDO1lBQUNOLGNBQUEsR0FBQUMsQ0FBQTtZQUMzRCxPQUFPO2NBQUV3RixPQUFPLEVBQUUsS0FBSztjQUFFbEYsS0FBSyxFQUFFQSxLQUFLLENBQUMwQjtZQUFRLENBQUM7VUFDakQsQ0FBQztZQUFBakMsY0FBQSxHQUFBYyxDQUFBO1VBQUE7VUFBQWQsY0FBQSxHQUFBQyxDQUFBO1VBRUQsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO1lBQUUxQixPQUFPLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ04sY0FBQSxHQUFBQyxDQUFBO1VBQ3JDLE9BQU87WUFBRXdGLE9BQU8sRUFBRTtVQUFLLENBQUM7UUFFMUIsQ0FBQyxDQUFDLE9BQU9sRixLQUFLLEVBQUU7VUFDZCxJQUFNdUYsWUFBWSxJQUFBOUYsY0FBQSxHQUFBQyxDQUFBLFFBQUdNLEtBQUssWUFBWXdGLEtBQUssSUFBQS9GLGNBQUEsR0FBQWMsQ0FBQSxXQUFHUCxLQUFLLENBQUMwQixPQUFPLEtBQUFqQyxjQUFBLEdBQUFjLENBQUEsV0FBRyxpQkFBaUI7VUFBQ2QsY0FBQSxHQUFBQyxDQUFBO1VBQ2hGLElBQUksQ0FBQytCLFdBQVcsQ0FBQztZQUFFekIsS0FBSyxFQUFFdUYsWUFBWTtZQUFFeEYsT0FBTyxFQUFFO1VBQU0sQ0FBQyxDQUFDO1VBQUNOLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUV3RixPQUFPLEVBQUUsS0FBSztZQUFFbEYsS0FBSyxFQUFFdUY7VUFBYSxDQUFDO1FBQ2hEO01BQ0YsQ0FBQztNQUFBLFNBbkJLTSxPQUFPQSxDQUFBO1FBQUEsT0FBQUYsUUFBQSxDQUFBeEQsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFQeUQsT0FBTztJQUFBO0VBQUE7SUFBQTVFLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE0RSxjQUFBLEdBQUExRSxpQkFBQSxDQXdCYixXQUFvQjBDLEtBQWEsRUFBaUQ7UUFBQXJFLGNBQUEsR0FBQVEsQ0FBQTtRQUFBUixjQUFBLEdBQUFDLENBQUE7UUFDaEYsSUFBSTtVQUNGLElBQUFxRyxLQUFBLElBQUF0RyxjQUFBLEdBQUFDLENBQUEsY0FBd0IsSUFBSSxDQUFDZSxRQUFRLENBQUNDLElBQUksQ0FBQ3NGLHFCQUFxQixDQUFDbEMsS0FBSyxFQUFFO2NBQ3RFbUMsVUFBVSxFQUFFO1lBQ2QsQ0FBQyxDQUFDO1lBRk1qRyxLQUFLLEdBQUErRixLQUFBLENBQUwvRixLQUFLO1VBRVZQLGNBQUEsR0FBQUMsQ0FBQTtVQUVILElBQUlNLEtBQUssRUFBRTtZQUFBUCxjQUFBLEdBQUFjLENBQUE7WUFBQWQsY0FBQSxHQUFBQyxDQUFBO1lBQ1QsT0FBTztjQUFFd0YsT0FBTyxFQUFFLEtBQUs7Y0FBRWxGLEtBQUssRUFBRUEsS0FBSyxDQUFDMEI7WUFBUSxDQUFDO1VBQ2pELENBQUM7WUFBQWpDLGNBQUEsR0FBQWMsQ0FBQTtVQUFBO1VBQUFkLGNBQUEsR0FBQUMsQ0FBQTtVQUVELE9BQU87WUFBRXdGLE9BQU8sRUFBRTtVQUFLLENBQUM7UUFFMUIsQ0FBQyxDQUFDLE9BQU9sRixLQUFLLEVBQUU7VUFDZCxJQUFNdUYsWUFBWSxJQUFBOUYsY0FBQSxHQUFBQyxDQUFBLFFBQUdNLEtBQUssWUFBWXdGLEtBQUssSUFBQS9GLGNBQUEsR0FBQWMsQ0FBQSxXQUFHUCxLQUFLLENBQUMwQixPQUFPLEtBQUFqQyxjQUFBLEdBQUFjLENBQUEsV0FBRyx1QkFBdUI7VUFBQ2QsY0FBQSxHQUFBQyxDQUFBO1VBQ3RGLE9BQU87WUFBRXdGLE9BQU8sRUFBRSxLQUFLO1lBQUVsRixLQUFLLEVBQUV1RjtVQUFhLENBQUM7UUFDaEQ7TUFDRixDQUFDO01BQUEsU0FoQktXLGFBQWFBLENBQUFDLEdBQUE7UUFBQSxPQUFBTCxjQUFBLENBQUEzRCxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWI4RCxhQUFhO0lBQUE7RUFBQTtJQUFBakYsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWtGLGNBQUEsR0FBQWhGLGlCQUFBLENBcUJuQixXQUFvQjBCLE9BQTBCLEVBQWlEO1FBQUFyRCxjQUFBLEdBQUFRLENBQUE7UUFBQVIsY0FBQSxHQUFBQyxDQUFBO1FBQzdGLElBQUk7VUFBQUQsY0FBQSxHQUFBQyxDQUFBO1VBQ0YsSUFBSSxDQUFDLElBQUksQ0FBQ0MsWUFBWSxDQUFDQyxJQUFJLEVBQUU7WUFBQUgsY0FBQSxHQUFBYyxDQUFBO1lBQUFkLGNBQUEsR0FBQUMsQ0FBQTtZQUMzQixPQUFPO2NBQUV3RixPQUFPLEVBQUUsS0FBSztjQUFFbEYsS0FBSyxFQUFFO1lBQXlCLENBQUM7VUFDNUQsQ0FBQztZQUFBUCxjQUFBLEdBQUFjLENBQUE7VUFBQTtVQUVELElBQUE4RixLQUFBLElBQUE1RyxjQUFBLEdBQUFDLENBQUEsY0FBOEIsSUFBSSxDQUFDZSxRQUFRLENBQ3hDK0IsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUNyQjhELE1BQU0sQ0FBQXRELE1BQUEsQ0FBQUMsTUFBQSxLQUNGSCxPQUFPO2NBQ1Z5RCxVQUFVLEVBQUUsSUFBSUMsSUFBSSxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDO1lBQUMsRUFDckMsQ0FBQyxDQUNEL0QsRUFBRSxDQUFDLElBQUksRUFBRSxJQUFJLENBQUMvQyxZQUFZLENBQUNDLElBQUksQ0FBQ2dDLEVBQUUsQ0FBQyxDQUNuQ2EsTUFBTSxDQUFDLENBQUMsQ0FDUkUsTUFBTSxDQUFDLENBQUM7WUFSSG5CLElBQUksR0FBQTZFLEtBQUEsQ0FBSjdFLElBQUk7WUFBRXhCLEtBQUssR0FBQXFHLEtBQUEsQ0FBTHJHLEtBQUs7VUFRUFAsY0FBQSxHQUFBQyxDQUFBO1VBRVosSUFBSU0sS0FBSyxFQUFFO1lBQUFQLGNBQUEsR0FBQWMsQ0FBQTtZQUFBZCxjQUFBLEdBQUFDLENBQUE7WUFDVCxPQUFPO2NBQUV3RixPQUFPLEVBQUUsS0FBSztjQUFFbEYsS0FBSyxFQUFFQSxLQUFLLENBQUMwQjtZQUFRLENBQUM7VUFDakQsQ0FBQztZQUFBakMsY0FBQSxHQUFBYyxDQUFBO1VBQUE7VUFBQWQsY0FBQSxHQUFBQyxDQUFBO1VBRUQsSUFBSSxDQUFDK0IsV0FBVyxDQUFDO1lBQUU1QixPQUFPLEVBQUUyQjtVQUFLLENBQUMsQ0FBQztVQUFDL0IsY0FBQSxHQUFBQyxDQUFBO1VBQ3BDLE9BQU87WUFBRXdGLE9BQU8sRUFBRTtVQUFLLENBQUM7UUFFMUIsQ0FBQyxDQUFDLE9BQU9sRixLQUFLLEVBQUU7VUFDZCxJQUFNdUYsWUFBWSxJQUFBOUYsY0FBQSxHQUFBQyxDQUFBLFNBQUdNLEtBQUssWUFBWXdGLEtBQUssSUFBQS9GLGNBQUEsR0FBQWMsQ0FBQSxXQUFHUCxLQUFLLENBQUMwQixPQUFPLEtBQUFqQyxjQUFBLEdBQUFjLENBQUEsV0FBRyx1QkFBdUI7VUFBQ2QsY0FBQSxHQUFBQyxDQUFBO1VBQ3RGLE9BQU87WUFBRXdGLE9BQU8sRUFBRSxLQUFLO1lBQUVsRixLQUFLLEVBQUV1RjtVQUFhLENBQUM7UUFDaEQ7TUFDRixDQUFDO01BQUEsU0EzQkttQixhQUFhQSxDQUFBQyxHQUFBO1FBQUEsT0FBQVAsY0FBQSxDQUFBakUsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFic0UsYUFBYTtJQUFBO0VBQUE7SUFBQXpGLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUEwRixpQkFBQSxHQUFBeEYsaUJBQUEsQ0FnQ25CLGFBQXdDO1FBQUEzQixjQUFBLEdBQUFRLENBQUE7UUFBQVIsY0FBQSxHQUFBQyxDQUFBO1FBQ3RDLElBQUksQ0FBQyxJQUFJLENBQUNDLFlBQVksQ0FBQ0MsSUFBSSxFQUFFO1VBQUFILGNBQUEsR0FBQWMsQ0FBQTtVQUFBZCxjQUFBLEdBQUFDLENBQUE7VUFBQTtRQUFNLENBQUM7VUFBQUQsY0FBQSxHQUFBYyxDQUFBO1FBQUE7UUFBQWQsY0FBQSxHQUFBQyxDQUFBO1FBRXBDLElBQUk7VUFBQUQsY0FBQSxHQUFBQyxDQUFBO1VBQ0YsTUFBTSxJQUFJLENBQUNlLFFBQVEsQ0FDaEIrQixJQUFJLENBQUMsZUFBZSxDQUFDLENBQ3JCOEQsTUFBTSxDQUFDO1lBQUVPLGNBQWMsRUFBRSxJQUFJTCxJQUFJLENBQUMsQ0FBQyxDQUFDQyxXQUFXLENBQUM7VUFBRSxDQUFDLENBQUMsQ0FDcEQvRCxFQUFFLENBQUMsSUFBSSxFQUFFLElBQUksQ0FBQy9DLFlBQVksQ0FBQ0MsSUFBSSxDQUFDZ0MsRUFBRSxDQUFDO1FBQ3hDLENBQUMsQ0FBQyxPQUFPNUIsS0FBSyxFQUFFO1VBQUFQLGNBQUEsR0FBQUMsQ0FBQTtVQUNkYyxPQUFPLENBQUNSLEtBQUssQ0FBQyw2QkFBNkIsRUFBRUEsS0FBSyxDQUFDO1FBQ3JEO01BQ0YsQ0FBQztNQUFBLFNBWEs4RyxnQkFBZ0JBLENBQUE7UUFBQSxPQUFBRixpQkFBQSxDQUFBekUsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFoQjBFLGdCQUFnQjtJQUFBO0VBQUE7SUFBQTdGLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE2RixhQUFBLEdBQUEzRixpQkFBQSxDQWdCdEIsV0FBbUI0RixRQUFnQixFQUErRDtRQUFBdkgsY0FBQSxHQUFBUSxDQUFBO1FBQUFSLGNBQUEsR0FBQUMsQ0FBQTtRQUNoRyxJQUFJO1VBQUFELGNBQUEsR0FBQUMsQ0FBQTtVQUNGLElBQUksQ0FBQyxJQUFJLENBQUNDLFlBQVksQ0FBQ0MsSUFBSSxFQUFFO1lBQUFILGNBQUEsR0FBQWMsQ0FBQTtZQUFBZCxjQUFBLEdBQUFDLENBQUE7WUFDM0IsT0FBTztjQUFFd0YsT0FBTyxFQUFFLEtBQUs7Y0FBRWxGLEtBQUssRUFBRTtZQUF5QixDQUFDO1VBQzVELENBQUM7WUFBQVAsY0FBQSxHQUFBYyxDQUFBO1VBQUE7VUFHRCxJQUFNMEcsUUFBUSxJQUFBeEgsY0FBQSxHQUFBQyxDQUFBLGVBQVN3SCxLQUFLLENBQUNGLFFBQVEsQ0FBQztVQUN0QyxJQUFNRyxJQUFJLElBQUExSCxjQUFBLEdBQUFDLENBQUEsZUFBU3VILFFBQVEsQ0FBQ0UsSUFBSSxDQUFDLENBQUM7VUFFbEMsSUFBTUMsT0FBTyxJQUFBM0gsY0FBQSxHQUFBQyxDQUFBLFNBQUdzSCxRQUFRLENBQUNLLEtBQUssQ0FBQyxHQUFHLENBQUMsQ0FBQ0MsR0FBRyxDQUFDLENBQUM7VUFDekMsSUFBTUMsUUFBUSxJQUFBOUgsY0FBQSxHQUFBQyxDQUFBLFNBQUcsR0FBRyxJQUFJLENBQUNDLFlBQVksQ0FBQ0MsSUFBSSxDQUFDZ0MsRUFBRSxXQUFXd0YsT0FBTyxFQUFFO1VBRWpFLElBQUFJLEtBQUEsSUFBQS9ILGNBQUEsR0FBQUMsQ0FBQSxlQUE4QixJQUFJLENBQUNlLFFBQVEsQ0FBQ0UsT0FBTyxDQUNoRDZCLElBQUksQ0FBQyxTQUFTLENBQUMsQ0FDZmlGLE1BQU0sQ0FBQ0YsUUFBUSxFQUFFSixJQUFJLEVBQUU7Y0FDdEJPLFlBQVksRUFBRSxNQUFNO2NBQ3BCQyxNQUFNLEVBQUU7WUFDVixDQUFDLENBQUM7WUFMSW5HLElBQUksR0FBQWdHLEtBQUEsQ0FBSmhHLElBQUk7WUFBRXhCLEtBQUssR0FBQXdILEtBQUEsQ0FBTHhILEtBQUs7VUFLZFAsY0FBQSxHQUFBQyxDQUFBO1VBRUwsSUFBSU0sS0FBSyxFQUFFO1lBQUFQLGNBQUEsR0FBQWMsQ0FBQTtZQUFBZCxjQUFBLEdBQUFDLENBQUE7WUFDVCxPQUFPO2NBQUV3RixPQUFPLEVBQUUsS0FBSztjQUFFbEYsS0FBSyxFQUFFQSxLQUFLLENBQUMwQjtZQUFRLENBQUM7VUFDakQsQ0FBQztZQUFBakMsY0FBQSxHQUFBYyxDQUFBO1VBQUE7VUFHRCxJQUFBcUgsS0FBQSxJQUFBbkksY0FBQSxHQUFBQyxDQUFBLFNBQWdDLElBQUksQ0FBQ2UsUUFBUSxDQUFDRSxPQUFPLENBQ2xENkIsSUFBSSxDQUFDLFNBQVMsQ0FBQyxDQUNmcUYsWUFBWSxDQUFDTixRQUFRLENBQUM7WUFGVE8sU0FBUyxHQUFBRixLQUFBLENBQWpCcEcsSUFBSSxDQUFJc0csU0FBUztVQUVDckksY0FBQSxHQUFBQyxDQUFBO1VBRzFCLE1BQU0sSUFBSSxDQUFDZ0gsYUFBYSxDQUFDO1lBQUVxQixVQUFVLEVBQUVEO1VBQVUsQ0FBQyxDQUFDO1VBQUNySSxjQUFBLEdBQUFDLENBQUE7VUFFcEQsT0FBTztZQUFFd0YsT0FBTyxFQUFFLElBQUk7WUFBRThDLEdBQUcsRUFBRUY7VUFBVSxDQUFDO1FBRTFDLENBQUMsQ0FBQyxPQUFPOUgsS0FBSyxFQUFFO1VBQ2QsSUFBTXVGLFlBQVksSUFBQTlGLGNBQUEsR0FBQUMsQ0FBQSxTQUFHTSxLQUFLLFlBQVl3RixLQUFLLElBQUEvRixjQUFBLEdBQUFjLENBQUEsV0FBR1AsS0FBSyxDQUFDMEIsT0FBTyxLQUFBakMsY0FBQSxHQUFBYyxDQUFBLFdBQUcsc0JBQXNCO1VBQUNkLGNBQUEsR0FBQUMsQ0FBQTtVQUNyRixPQUFPO1lBQUV3RixPQUFPLEVBQUUsS0FBSztZQUFFbEYsS0FBSyxFQUFFdUY7VUFBYSxDQUFDO1FBQ2hEO01BQ0YsQ0FBQztNQUFBLFNBdENLMEMsWUFBWUEsQ0FBQUMsR0FBQTtRQUFBLE9BQUFuQixhQUFBLENBQUE1RSxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQVo2RixZQUFZO0lBQUE7RUFBQTtJQUFBaEgsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWlILGNBQUEsR0FBQS9HLGlCQUFBLENBMkNsQixhQUFxRTtRQUFBM0IsY0FBQSxHQUFBUSxDQUFBO1FBQUFSLGNBQUEsR0FBQUMsQ0FBQTtRQUNuRSxJQUFJO1VBQUFELGNBQUEsR0FBQUMsQ0FBQTtVQUNGLElBQUksQ0FBQyxJQUFJLENBQUNDLFlBQVksQ0FBQ0MsSUFBSSxFQUFFO1lBQUFILGNBQUEsR0FBQWMsQ0FBQTtZQUFBZCxjQUFBLEdBQUFDLENBQUE7WUFDM0IsT0FBTztjQUFFd0YsT0FBTyxFQUFFLEtBQUs7Y0FBRWxGLEtBQUssRUFBRTtZQUF5QixDQUFDO1VBQzVELENBQUM7WUFBQVAsY0FBQSxHQUFBYyxDQUFBO1VBQUE7VUFBQWQsY0FBQSxHQUFBQyxDQUFBO1VBSURQLEtBQUssQ0FBQ2lKLEtBQUssQ0FDVCxnQkFBZ0IsRUFDaEIsZ0RBQWdELEVBQ2hELENBQUM7WUFBRUMsSUFBSSxFQUFFO1VBQUssQ0FBQyxDQUNqQixDQUFDO1VBQUM1SSxjQUFBLEdBQUFDLENBQUE7VUFFRixPQUFPO1lBQUV3RixPQUFPLEVBQUUsS0FBSztZQUFFbEYsS0FBSyxFQUFFO1VBQWdELENBQUM7UUFFbkYsQ0FBQyxDQUFDLE9BQU9BLEtBQUssRUFBRTtVQUNkLElBQU11RixZQUFZLElBQUE5RixjQUFBLEdBQUFDLENBQUEsU0FBR00sS0FBSyxZQUFZd0YsS0FBSyxJQUFBL0YsY0FBQSxHQUFBYyxDQUFBLFdBQUdQLEtBQUssQ0FBQzBCLE9BQU8sS0FBQWpDLGNBQUEsR0FBQWMsQ0FBQSxXQUFHLHlCQUF5QjtVQUFDZCxjQUFBLEdBQUFDLENBQUE7VUFDeEYsT0FBTztZQUFFd0YsT0FBTyxFQUFFLEtBQUs7WUFBRWxGLEtBQUssRUFBRXVGO1VBQWEsQ0FBQztRQUNoRDtNQUNGLENBQUM7TUFBQSxTQXBCSytDLGFBQWFBLENBQUE7UUFBQSxPQUFBSCxjQUFBLENBQUFoRyxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWJrRyxhQUFhO0lBQUE7RUFBQTtJQUFBckgsR0FBQTtJQUFBQyxLQUFBLEVBeUJuQixTQUFBcUgsaUJBQWlCQSxDQUFBLEVBQW1CO01BQUE5SSxjQUFBLEdBQUFRLENBQUE7TUFBQVIsY0FBQSxHQUFBQyxDQUFBO01BQ2xDLE9BQU8sSUFBSSxDQUFDZSxRQUFRO0lBQ3RCO0VBQUM7SUFBQVEsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQXNILGVBQWVBLENBQUEsRUFBWTtNQUFBL0ksY0FBQSxHQUFBUSxDQUFBO01BQUFSLGNBQUEsR0FBQUMsQ0FBQTtNQUN6QixPQUFPLENBQUFELGNBQUEsR0FBQWMsQ0FBQSxZQUFDLENBQUMsSUFBSSxDQUFDWixZQUFZLENBQUNDLElBQUksTUFBQUgsY0FBQSxHQUFBYyxDQUFBLFdBQUksQ0FBQyxDQUFDLElBQUksQ0FBQ1osWUFBWSxDQUFDRyxPQUFPO0lBQ2hFO0VBQUM7SUFBQW1CLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQUF1SCxzQkFBc0JBLENBQUEsRUFBWTtNQUFBLElBQUFDLHFCQUFBLEVBQUFDLHNCQUFBO01BQUFsSixjQUFBLEdBQUFRLENBQUE7TUFBQVIsY0FBQSxHQUFBQyxDQUFBO01BQ2hDLE9BQU8sQ0FBQUQsY0FBQSxHQUFBYyxDQUFBLGFBQUFtSSxxQkFBQSxPQUFJLENBQUMvSSxZQUFZLENBQUNFLE9BQU8scUJBQXpCNkkscUJBQUEsQ0FBMkJFLGlCQUFpQixNQUFLLFNBQVMsTUFBQW5KLGNBQUEsR0FBQWMsQ0FBQSxXQUMxRCxFQUFBb0ksc0JBQUEsT0FBSSxDQUFDaEosWUFBWSxDQUFDRSxPQUFPLHFCQUF6QjhJLHNCQUFBLENBQTJCQyxpQkFBaUIsTUFBSyxLQUFLO0lBQy9EO0VBQUM7SUFBQTNILEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQUEySCxrQkFBa0JBLENBQUEsRUFBWTtNQUFBLElBQUFDLHNCQUFBO01BQUFySixjQUFBLEdBQUFRLENBQUE7TUFBQVIsY0FBQSxHQUFBQyxDQUFBO01BQzVCLE9BQU8sRUFBQW9KLHNCQUFBLE9BQUksQ0FBQ25KLFlBQVksQ0FBQ0UsT0FBTyxxQkFBekJpSixzQkFBQSxDQUEyQkYsaUJBQWlCLE1BQUssS0FBSztJQUMvRDtFQUFDO0FBQUE7QUFJSCxPQUFPLElBQU1HLFdBQVcsSUFBQXRKLGNBQUEsR0FBQUMsQ0FBQSxTQUFHLElBQUlKLFdBQVcsQ0FBQyxDQUFDO0FBQzVDLGVBQWV5SixXQUFXIiwiaWdub3JlTGlzdCI6W119