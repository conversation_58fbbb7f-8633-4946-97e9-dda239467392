{"version": 3, "names": ["_interopRequireWildcard", "require", "default", "exports", "__esModule", "useResponderEvents", "React", "ResponderSystem", "emptyObject", "idCounter", "useStable", "getInitialValue", "ref", "useRef", "current", "hostRef", "config", "id", "isAttachedRef", "useEffect", "attachListeners", "removeNode", "_config", "onMoveShouldSetResponder", "onMoveShouldSetResponderCapture", "onScrollShouldSetResponder", "onScrollShouldSetResponderCapture", "onSelectionChangeShouldSetResponder", "onSelectionChangeShouldSetResponderCapture", "onStartShouldSetResponder", "onStartShouldSetResponderCapture", "requiresResponderSystem", "node", "addNode", "useDebugValue", "isResponder", "getResponderNode", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = useResponderEvents;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar ResponderSystem = _interopRequireWildcard(require(\"./ResponderSystem\"));\n/**\n * Copyright (c) <PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n/**\n * Hook for integrating the Responder System into React\n *\n *   function SomeComponent({ onStartShouldSetResponder }) {\n *     const ref = useRef(null);\n *     useResponderEvents(ref, { onStartShouldSetResponder });\n *     return <div ref={ref} />\n *   }\n */\n\nvar emptyObject = {};\nvar idCounter = 0;\nfunction useStable(getInitialValue) {\n  var ref = React.useRef(null);\n  if (ref.current == null) {\n    ref.current = getInitialValue();\n  }\n  return ref.current;\n}\nfunction useResponderEvents(hostRef, config) {\n  if (config === void 0) {\n    config = emptyObject;\n  }\n  var id = useStable(() => idCounter++);\n  var isAttachedRef = React.useRef(false);\n\n  // This is a separate effects so it doesn't run when the config changes.\n  // On initial mount, attach global listeners if needed.\n  // On unmount, remove node potentially attached to the Responder System.\n  React.useEffect(() => {\n    ResponderSystem.attachListeners();\n    return () => {\n      ResponderSystem.removeNode(id);\n    };\n  }, [id]);\n\n  // Register and unregister with the Responder System as necessary\n  React.useEffect(() => {\n    var _config = config,\n      onMoveShouldSetResponder = _config.onMoveShouldSetResponder,\n      onMoveShouldSetResponderCapture = _config.onMoveShouldSetResponderCapture,\n      onScrollShouldSetResponder = _config.onScrollShouldSetResponder,\n      onScrollShouldSetResponderCapture = _config.onScrollShouldSetResponderCapture,\n      onSelectionChangeShouldSetResponder = _config.onSelectionChangeShouldSetResponder,\n      onSelectionChangeShouldSetResponderCapture = _config.onSelectionChangeShouldSetResponderCapture,\n      onStartShouldSetResponder = _config.onStartShouldSetResponder,\n      onStartShouldSetResponderCapture = _config.onStartShouldSetResponderCapture;\n    var requiresResponderSystem = onMoveShouldSetResponder != null || onMoveShouldSetResponderCapture != null || onScrollShouldSetResponder != null || onScrollShouldSetResponderCapture != null || onSelectionChangeShouldSetResponder != null || onSelectionChangeShouldSetResponderCapture != null || onStartShouldSetResponder != null || onStartShouldSetResponderCapture != null;\n    var node = hostRef.current;\n    if (requiresResponderSystem) {\n      ResponderSystem.addNode(id, node, config);\n      isAttachedRef.current = true;\n    } else if (isAttachedRef.current) {\n      ResponderSystem.removeNode(id);\n      isAttachedRef.current = false;\n    }\n  }, [config, hostRef, id]);\n  React.useDebugValue({\n    isResponder: hostRef.current === ResponderSystem.getResponderNode()\n  });\n  React.useDebugValue(config);\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,kBAAkB;AACpC,IAAIC,KAAK,GAAGN,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIM,eAAe,GAAGP,uBAAuB,CAACC,OAAO,oBAAoB,CAAC,CAAC;AAoB3E,IAAIO,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,SAAS,GAAG,CAAC;AACjB,SAASC,SAASA,CAACC,eAAe,EAAE;EAClC,IAAIC,GAAG,GAAGN,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC;EAC5B,IAAID,GAAG,CAACE,OAAO,IAAI,IAAI,EAAE;IACvBF,GAAG,CAACE,OAAO,GAAGH,eAAe,CAAC,CAAC;EACjC;EACA,OAAOC,GAAG,CAACE,OAAO;AACpB;AACA,SAAST,kBAAkBA,CAACU,OAAO,EAAEC,MAAM,EAAE;EAC3C,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;IACrBA,MAAM,GAAGR,WAAW;EACtB;EACA,IAAIS,EAAE,GAAGP,SAAS,CAAC;IAAA,OAAMD,SAAS,EAAE;EAAA,EAAC;EACrC,IAAIS,aAAa,GAAGZ,KAAK,CAACO,MAAM,CAAC,KAAK,CAAC;EAKvCP,KAAK,CAACa,SAAS,CAAC,YAAM;IACpBZ,eAAe,CAACa,eAAe,CAAC,CAAC;IACjC,OAAO,YAAM;MACXb,eAAe,CAACc,UAAU,CAACJ,EAAE,CAAC;IAChC,CAAC;EACH,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EAGRX,KAAK,CAACa,SAAS,CAAC,YAAM;IACpB,IAAIG,OAAO,GAAGN,MAAM;MAClBO,wBAAwB,GAAGD,OAAO,CAACC,wBAAwB;MAC3DC,+BAA+B,GAAGF,OAAO,CAACE,+BAA+B;MACzEC,0BAA0B,GAAGH,OAAO,CAACG,0BAA0B;MAC/DC,iCAAiC,GAAGJ,OAAO,CAACI,iCAAiC;MAC7EC,mCAAmC,GAAGL,OAAO,CAACK,mCAAmC;MACjFC,0CAA0C,GAAGN,OAAO,CAACM,0CAA0C;MAC/FC,yBAAyB,GAAGP,OAAO,CAACO,yBAAyB;MAC7DC,gCAAgC,GAAGR,OAAO,CAACQ,gCAAgC;IAC7E,IAAIC,uBAAuB,GAAGR,wBAAwB,IAAI,IAAI,IAAIC,+BAA+B,IAAI,IAAI,IAAIC,0BAA0B,IAAI,IAAI,IAAIC,iCAAiC,IAAI,IAAI,IAAIC,mCAAmC,IAAI,IAAI,IAAIC,0CAA0C,IAAI,IAAI,IAAIC,yBAAyB,IAAI,IAAI,IAAIC,gCAAgC,IAAI,IAAI;IAClX,IAAIE,IAAI,GAAGjB,OAAO,CAACD,OAAO;IAC1B,IAAIiB,uBAAuB,EAAE;MAC3BxB,eAAe,CAAC0B,OAAO,CAAChB,EAAE,EAAEe,IAAI,EAAEhB,MAAM,CAAC;MACzCE,aAAa,CAACJ,OAAO,GAAG,IAAI;IAC9B,CAAC,MAAM,IAAII,aAAa,CAACJ,OAAO,EAAE;MAChCP,eAAe,CAACc,UAAU,CAACJ,EAAE,CAAC;MAC9BC,aAAa,CAACJ,OAAO,GAAG,KAAK;IAC/B;EACF,CAAC,EAAE,CAACE,MAAM,EAAED,OAAO,EAAEE,EAAE,CAAC,CAAC;EACzBX,KAAK,CAAC4B,aAAa,CAAC;IAClBC,WAAW,EAAEpB,OAAO,CAACD,OAAO,KAAKP,eAAe,CAAC6B,gBAAgB,CAAC;EACpE,CAAC,CAAC;EACF9B,KAAK,CAAC4B,aAAa,CAAClB,MAAM,CAAC;AAC7B;AACAqB,MAAM,CAAClC,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}