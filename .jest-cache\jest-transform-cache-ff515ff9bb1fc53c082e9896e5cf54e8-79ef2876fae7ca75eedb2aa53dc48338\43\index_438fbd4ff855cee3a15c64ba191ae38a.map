{"version": 3, "names": ["exports", "__esModule", "default", "pick", "obj", "list", "nextObj", "key", "hasOwnProperty", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = pick;\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction pick(obj, list) {\n  var nextObj = {};\n  for (var key in obj) {\n    if (obj.hasOwnProperty(key)) {\n      if (list[key] === true) {\n        nextObj[key] = obj[key];\n      }\n    }\n  }\n  return nextObj;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,IAAI;AAUtB,SAASA,IAAIA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACvB,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,KAAK,IAAIC,GAAG,IAAIH,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACI,cAAc,CAACD,GAAG,CAAC,EAAE;MAC3B,IAAIF,IAAI,CAACE,GAAG,CAAC,KAAK,IAAI,EAAE;QACtBD,OAAO,CAACC,GAAG,CAAC,GAAGH,GAAG,CAACG,GAAG,CAAC;MACzB;IACF;EACF;EACA,OAAOD,OAAO;AAChB;AACAG,MAAM,CAACT,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}