{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_propsToAriaRole", "roleComponents", "article", "banner", "blockquote", "button", "code", "complementary", "contentinfo", "deletion", "emphasis", "figure", "insertion", "form", "list", "listitem", "main", "navigation", "paragraph", "region", "strong", "emptyObject", "propsToAccessibilityComponent", "props", "roleProp", "role", "accessibilityRole", "level", "accessibilityLevel", "_default", "module"], "sources": ["propsToAccessibilityComponent.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _propsToAriaRole = _interopRequireDefault(require(\"./propsToAriaRole\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar roleComponents = {\n  article: 'article',\n  banner: 'header',\n  blockquote: 'blockquote',\n  button: 'button',\n  code: 'code',\n  complementary: 'aside',\n  contentinfo: 'footer',\n  deletion: 'del',\n  emphasis: 'em',\n  figure: 'figure',\n  insertion: 'ins',\n  form: 'form',\n  list: 'ul',\n  listitem: 'li',\n  main: 'main',\n  navigation: 'nav',\n  paragraph: 'p',\n  region: 'section',\n  strong: 'strong'\n};\nvar emptyObject = {};\nvar propsToAccessibilityComponent = function propsToAccessibilityComponent(props) {\n  if (props === void 0) {\n    props = emptyObject;\n  }\n  var roleProp = props.role || props.accessibilityRole;\n  // special-case for \"label\" role which doesn't map to an ARIA role\n  if (roleProp === 'label') {\n    return 'label';\n  }\n  var role = (0, _propsToAriaRole.default)(props);\n  if (role) {\n    if (role === 'heading') {\n      var level = props.accessibilityLevel || props['aria-level'];\n      if (level != null) {\n        return \"h\" + level;\n      }\n      return 'h1';\n    }\n    return roleComponents[role];\n  }\n};\nvar _default = exports.default = propsToAccessibilityComponent;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,gBAAgB,GAAGL,sBAAsB,CAACC,OAAO,oBAAoB,CAAC,CAAC;AAU3E,IAAIK,cAAc,GAAG;EACnBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,QAAQ;EAChBC,UAAU,EAAE,YAAY;EACxBC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,aAAa,EAAE,OAAO;EACtBC,WAAW,EAAE,QAAQ;EACrBC,QAAQ,EAAE,KAAK;EACfC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,QAAQ;EAChBC,SAAS,EAAE,KAAK;EAChBC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,KAAK;EACjBC,SAAS,EAAE,GAAG;EACdC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,6BAA6B,GAAG,SAASA,6BAA6BA,CAACC,KAAK,EAAE;EAChF,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAGF,WAAW;EACrB;EACA,IAAIG,QAAQ,GAAGD,KAAK,CAACE,IAAI,IAAIF,KAAK,CAACG,iBAAiB;EAEpD,IAAIF,QAAQ,KAAK,OAAO,EAAE;IACxB,OAAO,OAAO;EAChB;EACA,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAEzB,gBAAgB,CAACH,OAAO,EAAE0B,KAAK,CAAC;EAC/C,IAAIE,IAAI,EAAE;IACR,IAAIA,IAAI,KAAK,SAAS,EAAE;MACtB,IAAIE,KAAK,GAAGJ,KAAK,CAACK,kBAAkB,IAAIL,KAAK,CAAC,YAAY,CAAC;MAC3D,IAAII,KAAK,IAAI,IAAI,EAAE;QACjB,OAAO,GAAG,GAAGA,KAAK;MACpB;MACA,OAAO,IAAI;IACb;IACA,OAAO1B,cAAc,CAACwB,IAAI,CAAC;EAC7B;AACF,CAAC;AACD,IAAII,QAAQ,GAAG/B,OAAO,CAACD,OAAO,GAAGyB,6BAA6B;AAC9DQ,MAAM,CAAChC,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}