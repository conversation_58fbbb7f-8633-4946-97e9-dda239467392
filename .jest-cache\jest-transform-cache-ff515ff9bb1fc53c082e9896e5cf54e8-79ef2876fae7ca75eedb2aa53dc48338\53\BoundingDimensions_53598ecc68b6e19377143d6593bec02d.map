{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_PooledClass", "twoArgumentPooler", "BoundingDimensions", "width", "height", "prototype", "destructor", "getPooledFromElement", "element", "getPooled", "offsetWidth", "offsetHeight", "addPoolingTo", "_default", "module"], "sources": ["BoundingDimensions.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _PooledClass = _interopRequireDefault(require(\"../../vendor/react-native/PooledClass\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar twoArgumentPooler = _PooledClass.default.twoArgumentPooler;\n\n/**\n * PooledClass representing the bounding rectangle of a region.\n */\nfunction BoundingDimensions(width, height) {\n  this.width = width;\n  this.height = height;\n}\nBoundingDimensions.prototype.destructor = function () {\n  this.width = null;\n  this.height = null;\n};\nBoundingDimensions.getPooledFromElement = function (element) {\n  return BoundingDimensions.getPooled(element.offsetWidth, element.offsetHeight);\n};\n_PooledClass.default.addPoolingTo(BoundingDimensions, twoArgumentPooler);\nvar _default = exports.default = BoundingDimensions;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,YAAY,GAAGL,sBAAsB,CAACC,OAAO,wCAAwC,CAAC,CAAC;AAW3F,IAAIK,iBAAiB,GAAGD,YAAY,CAACH,OAAO,CAACI,iBAAiB;AAK9D,SAASC,kBAAkBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACzC,IAAI,CAACD,KAAK,GAAGA,KAAK;EAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;AACtB;AACAF,kBAAkB,CAACG,SAAS,CAACC,UAAU,GAAG,YAAY;EACpD,IAAI,CAACH,KAAK,GAAG,IAAI;EACjB,IAAI,CAACC,MAAM,GAAG,IAAI;AACpB,CAAC;AACDF,kBAAkB,CAACK,oBAAoB,GAAG,UAAUC,OAAO,EAAE;EAC3D,OAAON,kBAAkB,CAACO,SAAS,CAACD,OAAO,CAACE,WAAW,EAAEF,OAAO,CAACG,YAAY,CAAC;AAChF,CAAC;AACDX,YAAY,CAACH,OAAO,CAACe,YAAY,CAACV,kBAAkB,EAAED,iBAAiB,CAAC;AACxE,IAAIY,QAAQ,GAAGf,OAAO,CAACD,OAAO,GAAGK,kBAAkB;AACnDY,MAAM,CAAChB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}