{"version": 3, "names": ["performanceMonitor", "CoachingAnalysisService", "_classCallCheck", "config", "cov_2ala20ag7y", "s", "<PERSON><PERSON><PERSON><PERSON>", "b", "_env", "EXPO_PUBLIC_OPENAI_API_KEY", "model", "maxTokens", "temperature", "rateLimitPerMinute", "requestCount", "lastResetTime", "Date", "now", "_createClass", "key", "value", "_generateCoachingAnalysis", "_asyncToGenerator", "request", "f", "start", "checkRateLimit", "insights", "generateInsights", "overallAssessment", "createOverallAssessment", "movementAnalyses", "player<PERSON><PERSON><PERSON><PERSON>", "technicalMetrics", "calculateTechnicalMetrics", "nextSteps", "generateNextSteps", "result", "end", "error", "console", "Error", "generateCoachingAnalysis", "_x", "apply", "arguments", "_generateInsights", "_this", "movementGroups", "groupMovementsByType", "_ref", "Object", "entries", "_ref2", "_slicedToArray", "movementType", "analyses", "length", "insight", "analyzeMovementType", "matchContext", "push", "strategicInsight", "generateStrategicInsight", "sort", "a", "getPriorityScore", "priority", "_x2", "_analyzeMovementType", "prompt", "createTechnicalAnalysisPrompt", "response", "callOpenAI", "parseCoachingResponse", "_x3", "_x4", "_x5", "_x6", "avgConfidence", "reduce", "sum", "confidence", "commonIssues", "identifyCommonIssues", "skillLevel", "dominantHand", "playingStyle", "weaknesses", "join", "goals", "toFixed", "summarizeBodyPositions", "summarizeTechnicalMetrics", "surface", "matchType", "matchSituation", "_generateStrategicInsight", "createStrategicAnalysisPrompt", "id", "generateInsightId", "category", "title", "description", "actionableSteps", "technicalFocus", "expectedImprovement", "timeframe", "relatedMovements", "createdAt", "toISOString", "_x7", "movementTypes", "_toConsumableArray", "Set", "map", "score", "userGames", "opponent", "_callOpenAI", "Promise", "resolve", "setTimeout", "_x8", "determinePriority", "char<PERSON>t", "toUpperCase", "slice", "strengths", "identifyStrengths", "areasForImprovement", "identifyWeaknesses", "keyRecommendations", "generateKeyRecommendations", "progressFromLastAnalysis", "compareWithPreviousAnalysis", "avgBalance", "bodyPosition", "balance", "techniqueScore", "Math", "round", "consistencyScore", "powerScore", "calculatePowerScore", "accuracyScore", "calculateAccuracyScore", "_generateNextSteps", "highPriorityInsights", "filter", "i", "immediateActions", "flatMap", "practiceRecommendations", "generatePracticeRecommendations", "longTermGoals", "generateLongTermGoals", "_x9", "_x0", "groups", "analysis", "type", "issues", "incompleteFollowThrough", "follow<PERSON><PERSON><PERSON>", "stances", "stance", "mostCommon", "getMostCommon", "percentage", "arr", "v", "pop", "goodBalance", "poorFollowThrough", "poorFootwork", "footwork", "recommendations", "previousAnalyses", "undefined", "completeFollowThrough", "_response$description", "_response$description2", "includes", "_response$description3", "_response$description4", "random", "toString", "substr", "_checkRateLimit", "timeSinceReset", "waitTime", "coachingAnalysisService"], "sources": ["CoachingAnalysisService.ts"], "sourcesContent": ["/**\n * OpenAI Coaching Analysis Service\n * Generates personalized tennis coaching insights using GPT-4\n */\n\nimport { performanceMonitor } from '@/utils/performance';\nimport { TennisMovementAnalysis } from './MediaPipeService';\n\nexport interface PlayerProfile {\n  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'professional';\n  dominantHand: 'left' | 'right';\n  playingStyle: 'aggressive' | 'defensive' | 'all_court' | 'serve_volley';\n  weaknesses: string[];\n  goals: string[];\n  previousAnalyses: CoachingInsight[];\n}\n\nexport interface MatchContext {\n  matchType: 'practice' | 'tournament' | 'friendly' | 'lesson';\n  surface: 'hard' | 'clay' | 'grass' | 'indoor';\n  opponent: {\n    skillLevel?: string;\n    playingStyle?: string;\n  };\n  score: {\n    currentSet: number;\n    userGames: number;\n    opponentGames: number;\n  };\n  matchSituation: 'serving' | 'returning' | 'rally' | 'break_point' | 'match_point';\n}\n\nexport interface CoachingInsight {\n  id: string;\n  category: 'technique' | 'strategy' | 'fitness' | 'mental';\n  priority: 'high' | 'medium' | 'low';\n  title: string;\n  description: string;\n  actionableSteps: string[];\n  technicalFocus: string[];\n  expectedImprovement: string;\n  timeframe: string;\n  confidence: number;\n  relatedMovements: string[];\n  videoTimestamp?: number;\n  createdAt: string;\n}\n\nexport interface CoachingAnalysisRequest {\n  movementAnalyses: TennisMovementAnalysis[];\n  playerProfile: PlayerProfile;\n  matchContext: MatchContext;\n  videoSegment: {\n    startTime: number;\n    endTime: number;\n    description: string;\n  };\n  specificFocus?: string[];\n}\n\nexport interface CoachingAnalysisResult {\n  insights: CoachingInsight[];\n  overallAssessment: {\n    strengths: string[];\n    areasForImprovement: string[];\n    keyRecommendations: string[];\n    progressFromLastAnalysis?: string;\n  };\n  technicalMetrics: {\n    techniqueScore: number;\n    consistencyScore: number;\n    powerScore: number;\n    accuracyScore: number;\n  };\n  nextSteps: {\n    immediateActions: string[];\n    practiceRecommendations: string[];\n    longTermGoals: string[];\n  };\n}\n\ninterface OpenAIConfig {\n  apiKey: string;\n  model: string;\n  maxTokens: number;\n  temperature: number;\n  rateLimitPerMinute: number;\n}\n\nclass CoachingAnalysisService {\n  private config: OpenAIConfig = {\n    apiKey: process.env.EXPO_PUBLIC_OPENAI_API_KEY || '',\n    model: 'gpt-4',\n    maxTokens: 2000,\n    temperature: 0.7,\n    rateLimitPerMinute: 20,\n  };\n\n  private requestCount = 0;\n  private lastResetTime = Date.now();\n\n  /**\n   * Generate comprehensive coaching analysis\n   */\n  async generateCoachingAnalysis(\n    request: CoachingAnalysisRequest\n  ): Promise<CoachingAnalysisResult> {\n    try {\n      performanceMonitor.start('coaching_analysis');\n\n      // Check rate limiting\n      await this.checkRateLimit();\n\n      // Generate coaching insights\n      const insights = await this.generateInsights(request);\n\n      // Create overall assessment\n      const overallAssessment = this.createOverallAssessment(\n        request.movementAnalyses,\n        request.playerProfile\n      );\n\n      // Calculate technical metrics\n      const technicalMetrics = this.calculateTechnicalMetrics(request.movementAnalyses);\n\n      // Generate next steps\n      const nextSteps = await this.generateNextSteps(request, insights);\n\n      const result: CoachingAnalysisResult = {\n        insights,\n        overallAssessment,\n        technicalMetrics,\n        nextSteps,\n      };\n\n      performanceMonitor.end('coaching_analysis');\n      return result;\n    } catch (error) {\n      console.error('Coaching analysis failed:', error);\n      throw new Error('Failed to generate coaching analysis');\n    }\n  }\n\n  /**\n   * Generate specific coaching insights using OpenAI\n   */\n  private async generateInsights(\n    request: CoachingAnalysisRequest\n  ): Promise<CoachingInsight[]> {\n    const insights: CoachingInsight[] = [];\n\n    // Group movements by type for analysis\n    const movementGroups = this.groupMovementsByType(request.movementAnalyses);\n\n    for (const [movementType, analyses] of Object.entries(movementGroups)) {\n      if (analyses.length === 0) continue;\n\n      const insight = await this.analyzeMovementType(\n        movementType,\n        analyses,\n        request.playerProfile,\n        request.matchContext\n      );\n\n      if (insight) {\n        insights.push(insight);\n      }\n    }\n\n    // Generate strategic insights\n    const strategicInsight = await this.generateStrategicInsight(request);\n    if (strategicInsight) {\n      insights.push(strategicInsight);\n    }\n\n    return insights.sort((a, b) => this.getPriorityScore(b.priority) - this.getPriorityScore(a.priority));\n  }\n\n  /**\n   * Analyze specific movement type\n   */\n  private async analyzeMovementType(\n    movementType: string,\n    analyses: TennisMovementAnalysis[],\n    playerProfile: PlayerProfile,\n    matchContext: MatchContext\n  ): Promise<CoachingInsight | null> {\n    try {\n      // Create tennis-specific prompt\n      const prompt = this.createTechnicalAnalysisPrompt(\n        movementType,\n        analyses,\n        playerProfile,\n        matchContext\n      );\n\n      // Call OpenAI API (simulated for now)\n      const response = await this.callOpenAI(prompt);\n\n      // Parse response into structured insight\n      const insight = this.parseCoachingResponse(response, movementType);\n\n      return insight;\n    } catch (error) {\n      console.error(`Failed to analyze ${movementType}:`, error);\n      return null;\n    }\n  }\n\n  /**\n   * Create technical analysis prompt for OpenAI\n   */\n  private createTechnicalAnalysisPrompt(\n    movementType: string,\n    analyses: TennisMovementAnalysis[],\n    playerProfile: PlayerProfile,\n    matchContext: MatchContext\n  ): string {\n    const avgConfidence = analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length;\n    const commonIssues = this.identifyCommonIssues(analyses);\n\n    return `\nAs a professional tennis coach, analyze this ${movementType} technique data:\n\nPlayer Profile:\n- Skill Level: ${playerProfile.skillLevel}\n- Dominant Hand: ${playerProfile.dominantHand}\n- Playing Style: ${playerProfile.playingStyle}\n- Known Weaknesses: ${playerProfile.weaknesses.join(', ')}\n- Goals: ${playerProfile.goals.join(', ')}\n\nMovement Analysis (${analyses.length} samples):\n- Average Confidence: ${(avgConfidence * 100).toFixed(1)}%\n- Common Technical Issues: ${commonIssues.join(', ')}\n- Body Position Patterns: ${this.summarizeBodyPositions(analyses)}\n- Technical Metrics: ${this.summarizeTechnicalMetrics(analyses)}\n\nMatch Context:\n- Surface: ${matchContext.surface}\n- Match Type: ${matchContext.matchType}\n- Current Situation: ${matchContext.matchSituation}\n\nPlease provide:\n1. Primary technical issue to address\n2. 3-4 specific actionable steps for improvement\n3. Expected timeline for improvement\n4. Practice drills recommendation\n5. Mental/strategic considerations\n\nFocus on practical, implementable advice suitable for ${playerProfile.skillLevel} level.\n`;\n  }\n\n  /**\n   * Generate strategic coaching insight\n   */\n  private async generateStrategicInsight(\n    request: CoachingAnalysisRequest\n  ): Promise<CoachingInsight | null> {\n    try {\n      const prompt = this.createStrategicAnalysisPrompt(request);\n      const response = await this.callOpenAI(prompt);\n      \n      return {\n        id: this.generateInsightId(),\n        category: 'strategy',\n        priority: 'medium',\n        title: 'Match Strategy Recommendations',\n        description: response.description || 'Strategic recommendations based on current performance',\n        actionableSteps: response.actionableSteps || [],\n        technicalFocus: ['match_tactics', 'point_construction'],\n        expectedImprovement: 'Improved match performance and tactical awareness',\n        timeframe: '2-4 weeks',\n        confidence: 0.8,\n        relatedMovements: ['all'],\n        createdAt: new Date().toISOString(),\n      };\n    } catch (error) {\n      console.error('Failed to generate strategic insight:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Create strategic analysis prompt\n   */\n  private createStrategicAnalysisPrompt(request: CoachingAnalysisRequest): string {\n    const movementTypes = [...new Set(request.movementAnalyses.map(a => a.movementType))];\n    \n    return `\nAs a tennis strategy coach, analyze this match performance:\n\nPlayer: ${request.playerProfile.skillLevel} level, ${request.playerProfile.playingStyle} style\nMatch Context: ${request.matchContext.matchType} on ${request.matchContext.surface}\nCurrent Score: ${request.matchContext.score.userGames}-${request.matchContext.score.opponent}\n\nObserved Movements: ${movementTypes.join(', ')}\nMatch Situation: ${request.matchContext.matchSituation}\n\nProvide strategic recommendations for:\n1. Point construction patterns\n2. Opponent exploitation opportunities  \n3. Risk/reward shot selection\n4. Court positioning tactics\n5. Mental approach adjustments\n\nKeep advice practical and match-situation specific.\n`;\n  }\n\n  /**\n   * Call OpenAI API (simulated implementation)\n   */\n  private async callOpenAI(prompt: string): Promise<any> {\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Simulate OpenAI response\n    return {\n      description: 'Focus on improving follow-through consistency and body rotation timing.',\n      actionableSteps: [\n        'Practice shadow swings with emphasis on complete follow-through',\n        'Work on hip rotation timing during groundstrokes',\n        'Use video feedback to monitor racket path consistency',\n        'Incorporate balance drills into practice routine'\n      ],\n      technicalFocus: ['follow_through', 'body_rotation', 'balance'],\n      expectedImprovement: 'Increased shot consistency and power generation',\n      timeframe: '3-4 weeks with regular practice'\n    };\n  }\n\n  /**\n   * Parse OpenAI response into structured insight\n   */\n  private parseCoachingResponse(response: any, movementType: string): CoachingInsight {\n    return {\n      id: this.generateInsightId(),\n      category: 'technique',\n      priority: this.determinePriority(response),\n      title: `${movementType.charAt(0).toUpperCase() + movementType.slice(1)} Technique Analysis`,\n      description: response.description || 'Technical analysis and recommendations',\n      actionableSteps: response.actionableSteps || [],\n      technicalFocus: response.technicalFocus || [],\n      expectedImprovement: response.expectedImprovement || 'Improved technique execution',\n      timeframe: response.timeframe || '2-3 weeks',\n      confidence: 0.85,\n      relatedMovements: [movementType],\n      createdAt: new Date().toISOString(),\n    };\n  }\n\n  /**\n   * Create overall assessment\n   */\n  private createOverallAssessment(\n    analyses: TennisMovementAnalysis[],\n    playerProfile: PlayerProfile\n  ): CoachingAnalysisResult['overallAssessment'] {\n    const avgConfidence = analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length;\n    const movementTypes = [...new Set(analyses.map(a => a.movementType))];\n\n    return {\n      strengths: this.identifyStrengths(analyses),\n      areasForImprovement: this.identifyWeaknesses(analyses),\n      keyRecommendations: this.generateKeyRecommendations(analyses, playerProfile),\n      progressFromLastAnalysis: this.compareWithPreviousAnalysis(playerProfile),\n    };\n  }\n\n  /**\n   * Calculate technical metrics\n   */\n  private calculateTechnicalMetrics(\n    analyses: TennisMovementAnalysis[]\n  ): CoachingAnalysisResult['technicalMetrics'] {\n    const avgConfidence = analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length;\n    const avgBalance = analyses.reduce((sum, a) => sum + a.bodyPosition.balance, 0) / analyses.length;\n\n    return {\n      techniqueScore: Math.round(avgConfidence * 100),\n      consistencyScore: Math.round(avgBalance * 100),\n      powerScore: this.calculatePowerScore(analyses),\n      accuracyScore: this.calculateAccuracyScore(analyses),\n    };\n  }\n\n  /**\n   * Generate next steps recommendations\n   */\n  private async generateNextSteps(\n    request: CoachingAnalysisRequest,\n    insights: CoachingInsight[]\n  ): Promise<CoachingAnalysisResult['nextSteps']> {\n    const highPriorityInsights = insights.filter(i => i.priority === 'high');\n    \n    return {\n      immediateActions: highPriorityInsights.flatMap(i => i.actionableSteps.slice(0, 2)),\n      practiceRecommendations: this.generatePracticeRecommendations(request),\n      longTermGoals: this.generateLongTermGoals(request.playerProfile, insights),\n    };\n  }\n\n  // Helper methods\n  private groupMovementsByType(analyses: TennisMovementAnalysis[]): Record<string, TennisMovementAnalysis[]> {\n    return analyses.reduce((groups, analysis) => {\n      const type = analysis.movementType;\n      if (!groups[type]) groups[type] = [];\n      groups[type].push(analysis);\n      return groups;\n    }, {} as Record<string, TennisMovementAnalysis[]>);\n  }\n\n  private identifyCommonIssues(analyses: TennisMovementAnalysis[]): string[] {\n    const issues: string[] = [];\n    \n    const avgBalance = analyses.reduce((sum, a) => sum + a.bodyPosition.balance, 0) / analyses.length;\n    if (avgBalance < 0.7) issues.push('balance_issues');\n    \n    const incompleteFollowThrough = analyses.filter(a => a.technicalMetrics.followThrough !== 'complete').length;\n    if (incompleteFollowThrough > analyses.length * 0.5) issues.push('incomplete_follow_through');\n    \n    return issues;\n  }\n\n  private summarizeBodyPositions(analyses: TennisMovementAnalysis[]): string {\n    const stances = analyses.map(a => a.bodyPosition.stance);\n    const mostCommon = this.getMostCommon(stances);\n    return `Predominantly ${mostCommon} stance`;\n  }\n\n  private summarizeTechnicalMetrics(analyses: TennisMovementAnalysis[]): string {\n    const followThrough = analyses.filter(a => a.technicalMetrics.followThrough === 'complete').length;\n    const percentage = Math.round((followThrough / analyses.length) * 100);\n    return `${percentage}% complete follow-through`;\n  }\n\n  private getMostCommon<T>(arr: T[]): T {\n    return arr.sort((a, b) =>\n      arr.filter(v => v === a).length - arr.filter(v => v === b).length\n    ).pop()!;\n  }\n\n  private identifyStrengths(analyses: TennisMovementAnalysis[]): string[] {\n    const strengths: string[] = [];\n    \n    const avgConfidence = analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length;\n    if (avgConfidence > 0.8) strengths.push('Consistent technique execution');\n    \n    const goodBalance = analyses.filter(a => a.bodyPosition.balance > 0.8).length;\n    if (goodBalance > analyses.length * 0.7) strengths.push('Excellent balance and stability');\n    \n    return strengths;\n  }\n\n  private identifyWeaknesses(analyses: TennisMovementAnalysis[]): string[] {\n    const weaknesses: string[] = [];\n    \n    const poorFollowThrough = analyses.filter(a => a.technicalMetrics.followThrough === 'none').length;\n    if (poorFollowThrough > analyses.length * 0.3) weaknesses.push('Inconsistent follow-through');\n    \n    const poorFootwork = analyses.filter(a => a.technicalMetrics.footwork === 'needs_improvement').length;\n    if (poorFootwork > analyses.length * 0.4) weaknesses.push('Footwork needs improvement');\n    \n    return weaknesses;\n  }\n\n  private generateKeyRecommendations(\n    analyses: TennisMovementAnalysis[],\n    playerProfile: PlayerProfile\n  ): string[] {\n    const recommendations: string[] = [];\n    \n    if (playerProfile.skillLevel === 'beginner') {\n      recommendations.push('Focus on fundamental stroke mechanics');\n      recommendations.push('Develop consistent contact point');\n    } else {\n      recommendations.push('Refine timing and rhythm');\n      recommendations.push('Work on shot selection and placement');\n    }\n    \n    return recommendations;\n  }\n\n  private compareWithPreviousAnalysis(playerProfile: PlayerProfile): string | undefined {\n    if (playerProfile.previousAnalyses.length === 0) {\n      return undefined;\n    }\n    \n    return 'Showing improvement in consistency compared to previous analysis';\n  }\n\n  private calculatePowerScore(analyses: TennisMovementAnalysis[]): number {\n    // Simplified power calculation based on follow-through and body rotation\n    const completeFollowThrough = analyses.filter(a => a.technicalMetrics.followThrough === 'complete').length;\n    return Math.round((completeFollowThrough / analyses.length) * 100);\n  }\n\n  private calculateAccuracyScore(analyses: TennisMovementAnalysis[]): number {\n    // Simplified accuracy calculation based on consistency\n    const avgConfidence = analyses.reduce((sum, a) => sum + a.confidence, 0) / analyses.length;\n    return Math.round(avgConfidence * 100);\n  }\n\n  private generatePracticeRecommendations(request: CoachingAnalysisRequest): string[] {\n    const recommendations = [\n      'Practice shadow swings focusing on identified technical issues',\n      'Use video analysis for immediate feedback',\n      'Work on specific movement patterns during warm-up',\n    ];\n    \n    if (request.playerProfile.skillLevel === 'beginner') {\n      recommendations.push('Focus on basic stroke fundamentals');\n    } else {\n      recommendations.push('Incorporate match-play scenarios in practice');\n    }\n    \n    return recommendations;\n  }\n\n  private generateLongTermGoals(playerProfile: PlayerProfile, insights: CoachingInsight[]): string[] {\n    const goals = [\n      'Achieve consistent technique execution across all strokes',\n      'Develop tactical awareness and shot selection',\n    ];\n    \n    if (playerProfile.skillLevel !== 'professional') {\n      goals.push('Progress to next skill level');\n    }\n    \n    return goals;\n  }\n\n  private determinePriority(response: any): 'high' | 'medium' | 'low' {\n    // Simplified priority determination\n    if (response.description?.includes('critical') || response.description?.includes('fundamental')) {\n      return 'high';\n    } else if (response.description?.includes('important') || response.description?.includes('significant')) {\n      return 'medium';\n    }\n    return 'low';\n  }\n\n  private getPriorityScore(priority: 'high' | 'medium' | 'low'): number {\n    switch (priority) {\n      case 'high': return 3;\n      case 'medium': return 2;\n      case 'low': return 1;\n    }\n  }\n\n  private generateInsightId(): string {\n    return `insight_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private async checkRateLimit(): Promise<void> {\n    const now = Date.now();\n    const timeSinceReset = now - this.lastResetTime;\n    \n    if (timeSinceReset > 60000) { // Reset every minute\n      this.requestCount = 0;\n      this.lastResetTime = now;\n    }\n    \n    if (this.requestCount >= this.config.rateLimitPerMinute) {\n      const waitTime = 60000 - timeSinceReset;\n      await new Promise(resolve => setTimeout(resolve, waitTime));\n      this.requestCount = 0;\n      this.lastResetTime = Date.now();\n    }\n    \n    this.requestCount++;\n  }\n}\n\n// Export singleton instance\nexport const coachingAnalysisService = new CoachingAnalysisService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAASA,kBAAkB;AAA8B,IAoFnDC,uBAAuB;EAAA,SAAAA,wBAAA;IAAAC,eAAA,OAAAD,uBAAA;IAAA,KACnBE,MAAM,IAAAC,cAAA,GAAAC,CAAA,OAAiB;MAC7BC,MAAM,EAAE,CAAAF,cAAA,GAAAG,CAAA,UAAAC,IAAA,CAAAC,0BAAA,MAAAL,cAAA,GAAAG,CAAA,UAA0C,EAAE;MACpDG,KAAK,EAAE,OAAO;MACdC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,GAAG;MAChBC,kBAAkB,EAAE;IACtB,CAAC;IAAA,KAEOC,YAAY,IAAAV,cAAA,GAAAC,CAAA,OAAG,CAAC;IAAA,KAChBU,aAAa,IAAAX,cAAA,GAAAC,CAAA,OAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;EAAA;EAAA,OAAAC,YAAA,CAAAjB,uBAAA;IAAAkB,GAAA;IAAAC,KAAA;MAAA,IAAAC,yBAAA,GAAAC,iBAAA,CAKlC,WACEC,OAAgC,EACC;QAAAnB,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACjC,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACFL,kBAAkB,CAACyB,KAAK,CAAC,mBAAmB,CAAC;UAACrB,cAAA,GAAAC,CAAA;UAG9C,MAAM,IAAI,CAACqB,cAAc,CAAC,CAAC;UAG3B,IAAMC,QAAQ,IAAAvB,cAAA,GAAAC,CAAA,aAAS,IAAI,CAACuB,gBAAgB,CAACL,OAAO,CAAC;UAGrD,IAAMM,iBAAiB,IAAAzB,cAAA,GAAAC,CAAA,OAAG,IAAI,CAACyB,uBAAuB,CACpDP,OAAO,CAACQ,gBAAgB,EACxBR,OAAO,CAACS,aACV,CAAC;UAGD,IAAMC,gBAAgB,IAAA7B,cAAA,GAAAC,CAAA,OAAG,IAAI,CAAC6B,yBAAyB,CAACX,OAAO,CAACQ,gBAAgB,CAAC;UAGjF,IAAMI,SAAS,IAAA/B,cAAA,GAAAC,CAAA,aAAS,IAAI,CAAC+B,iBAAiB,CAACb,OAAO,EAAEI,QAAQ,CAAC;UAEjE,IAAMU,MAA8B,IAAAjC,cAAA,GAAAC,CAAA,QAAG;YACrCsB,QAAQ,EAARA,QAAQ;YACRE,iBAAiB,EAAjBA,iBAAiB;YACjBI,gBAAgB,EAAhBA,gBAAgB;YAChBE,SAAS,EAATA;UACF,CAAC;UAAC/B,cAAA,GAAAC,CAAA;UAEFL,kBAAkB,CAACsC,GAAG,CAAC,mBAAmB,CAAC;UAAClC,cAAA,GAAAC,CAAA;UAC5C,OAAOgC,MAAM;QACf,CAAC,CAAC,OAAOE,KAAK,EAAE;UAAAnC,cAAA,GAAAC,CAAA;UACdmC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UAACnC,cAAA,GAAAC,CAAA;UAClD,MAAM,IAAIoC,KAAK,CAAC,sCAAsC,CAAC;QACzD;MACF,CAAC;MAAA,SArCKC,wBAAwBA,CAAAC,EAAA;QAAA,OAAAtB,yBAAA,CAAAuB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAxBH,wBAAwB;IAAA;EAAA;IAAAvB,GAAA;IAAAC,KAAA;MAAA,IAAA0B,iBAAA,GAAAxB,iBAAA,CA0C9B,WACEC,OAAgC,EACJ;QAAA,IAAAwB,KAAA;QAAA3C,cAAA,GAAAoB,CAAA;QAC5B,IAAMG,QAA2B,IAAAvB,cAAA,GAAAC,CAAA,QAAG,EAAE;QAGtC,IAAM2C,cAAc,IAAA5C,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC4C,oBAAoB,CAAC1B,OAAO,CAACQ,gBAAgB,CAAC;QAAC3B,cAAA,GAAAC,CAAA;QAE3E,SAAA6C,IAAA,IAAuCC,MAAM,CAACC,OAAO,CAACJ,cAAc,CAAC,EAAE;UAAA,IAAAK,KAAA,GAAAC,cAAA,CAAAJ,IAAA;UAAA,IAA3DK,YAAY,GAAAF,KAAA;UAAA,IAAEG,QAAQ,GAAAH,KAAA;UAAAjD,cAAA,GAAAC,CAAA;UAChC,IAAImD,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;YAAArD,cAAA,GAAAG,CAAA;YAAAH,cAAA,GAAAC,CAAA;YAAA;UAAQ,CAAC;YAAAD,cAAA,GAAAG,CAAA;UAAA;UAEpC,IAAMmD,OAAO,IAAAtD,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACsD,mBAAmB,CAC5CJ,YAAY,EACZC,QAAQ,EACRjC,OAAO,CAACS,aAAa,EACrBT,OAAO,CAACqC,YACV,CAAC;UAACxD,cAAA,GAAAC,CAAA;UAEF,IAAIqD,OAAO,EAAE;YAAAtD,cAAA,GAAAG,CAAA;YAAAH,cAAA,GAAAC,CAAA;YACXsB,QAAQ,CAACkC,IAAI,CAACH,OAAO,CAAC;UACxB,CAAC;YAAAtD,cAAA,GAAAG,CAAA;UAAA;QACH;QAGA,IAAMuD,gBAAgB,IAAA1D,cAAA,GAAAC,CAAA,cAAS,IAAI,CAAC0D,wBAAwB,CAACxC,OAAO,CAAC;QAACnB,cAAA,GAAAC,CAAA;QACtE,IAAIyD,gBAAgB,EAAE;UAAA1D,cAAA,GAAAG,CAAA;UAAAH,cAAA,GAAAC,CAAA;UACpBsB,QAAQ,CAACkC,IAAI,CAACC,gBAAgB,CAAC;QACjC,CAAC;UAAA1D,cAAA,GAAAG,CAAA;QAAA;QAAAH,cAAA,GAAAC,CAAA;QAED,OAAOsB,QAAQ,CAACqC,IAAI,CAAC,UAACC,CAAC,EAAE1D,CAAC,EAAK;UAAAH,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAAA,OAAA0C,KAAI,CAACmB,gBAAgB,CAAC3D,CAAC,CAAC4D,QAAQ,CAAC,GAAGpB,KAAI,CAACmB,gBAAgB,CAACD,CAAC,CAACE,QAAQ,CAAC;QAAD,CAAC,CAAC;MACvG,CAAC;MAAA,SA9BavC,gBAAgBA,CAAAwC,GAAA;QAAA,OAAAtB,iBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBjB,gBAAgB;IAAA;EAAA;IAAAT,GAAA;IAAAC,KAAA;MAAA,IAAAiD,oBAAA,GAAA/C,iBAAA,CAmC9B,WACEiC,YAAoB,EACpBC,QAAkC,EAClCxB,aAA4B,EAC5B4B,YAA0B,EACO;QAAAxD,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACjC,IAAI;UAEF,IAAMiE,MAAM,IAAAlE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACkE,6BAA6B,CAC/ChB,YAAY,EACZC,QAAQ,EACRxB,aAAa,EACb4B,YACF,CAAC;UAGD,IAAMY,QAAQ,IAAApE,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACoE,UAAU,CAACH,MAAM,CAAC;UAG9C,IAAMZ,OAAO,IAAAtD,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACqE,qBAAqB,CAACF,QAAQ,EAAEjB,YAAY,CAAC;UAACnD,cAAA,GAAAC,CAAA;UAEnE,OAAOqD,OAAO;QAChB,CAAC,CAAC,OAAOnB,KAAK,EAAE;UAAAnC,cAAA,GAAAC,CAAA;UACdmC,OAAO,CAACD,KAAK,CAAC,qBAAqBgB,YAAY,GAAG,EAAEhB,KAAK,CAAC;UAACnC,cAAA,GAAAC,CAAA;UAC3D,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SA1BasD,mBAAmBA,CAAAgB,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAT,oBAAA,CAAAzB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBc,mBAAmB;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA,EA+BjC,SAAQmD,6BAA6BA,CACnChB,YAAoB,EACpBC,QAAkC,EAClCxB,aAA4B,EAC5B4B,YAA0B,EAClB;MAAAxD,cAAA,GAAAoB,CAAA;MACR,IAAMuD,aAAa,IAAA3E,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAACwB,MAAM,CAAC,UAACC,GAAG,EAAEhB,CAAC,EAAK;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4E,GAAG,GAAGhB,CAAC,CAACiB,UAAU;MAAD,CAAC,EAAE,CAAC,CAAC,GAAG1B,QAAQ,CAACC,MAAM;MAC1F,IAAM0B,YAAY,IAAA/E,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC+E,oBAAoB,CAAC5B,QAAQ,CAAC;MAACpD,cAAA,GAAAC,CAAA;MAEzD,OAAO;AACX,+CAA+CkD,YAAY;AAC3D;AACA;AACA,iBAAiBvB,aAAa,CAACqD,UAAU;AACzC,mBAAmBrD,aAAa,CAACsD,YAAY;AAC7C,mBAAmBtD,aAAa,CAACuD,YAAY;AAC7C,sBAAsBvD,aAAa,CAACwD,UAAU,CAACC,IAAI,CAAC,IAAI,CAAC;AACzD,WAAWzD,aAAa,CAAC0D,KAAK,CAACD,IAAI,CAAC,IAAI,CAAC;AACzC;AACA,qBAAqBjC,QAAQ,CAACC,MAAM;AACpC,wBAAwB,CAACsB,aAAa,GAAG,GAAG,EAAEY,OAAO,CAAC,CAAC,CAAC;AACxD,6BAA6BR,YAAY,CAACM,IAAI,CAAC,IAAI,CAAC;AACpD,4BAA4B,IAAI,CAACG,sBAAsB,CAACpC,QAAQ,CAAC;AACjE,uBAAuB,IAAI,CAACqC,yBAAyB,CAACrC,QAAQ,CAAC;AAC/D;AACA;AACA,aAAaI,YAAY,CAACkC,OAAO;AACjC,gBAAgBlC,YAAY,CAACmC,SAAS;AACtC,uBAAuBnC,YAAY,CAACoC,cAAc;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wDAAwDhE,aAAa,CAACqD,UAAU;AAChF,CAAC;IACC;EAAC;IAAAlE,GAAA;IAAAC,KAAA;MAAA,IAAA6E,yBAAA,GAAA3E,iBAAA,CAKD,WACEC,OAAgC,EACC;QAAAnB,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QACjC,IAAI;UACF,IAAMiE,MAAM,IAAAlE,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC6F,6BAA6B,CAAC3E,OAAO,CAAC;UAC1D,IAAMiD,QAAQ,IAAApE,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACoE,UAAU,CAACH,MAAM,CAAC;UAAClE,cAAA,GAAAC,CAAA;UAE/C,OAAO;YACL8F,EAAE,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;YAC5BC,QAAQ,EAAE,UAAU;YACpBlC,QAAQ,EAAE,QAAQ;YAClBmC,KAAK,EAAE,gCAAgC;YACvCC,WAAW,EAAE,CAAAnG,cAAA,GAAAG,CAAA,UAAAiE,QAAQ,CAAC+B,WAAW,MAAAnG,cAAA,GAAAG,CAAA,UAAI,wDAAwD;YAC7FiG,eAAe,EAAE,CAAApG,cAAA,GAAAG,CAAA,UAAAiE,QAAQ,CAACgC,eAAe,MAAApG,cAAA,GAAAG,CAAA,UAAI,EAAE;YAC/CkG,cAAc,EAAE,CAAC,eAAe,EAAE,oBAAoB,CAAC;YACvDC,mBAAmB,EAAE,mDAAmD;YACxEC,SAAS,EAAE,WAAW;YACtBzB,UAAU,EAAE,GAAG;YACf0B,gBAAgB,EAAE,CAAC,KAAK,CAAC;YACzBC,SAAS,EAAE,IAAI7F,IAAI,CAAC,CAAC,CAAC8F,WAAW,CAAC;UACpC,CAAC;QACH,CAAC,CAAC,OAAOvE,KAAK,EAAE;UAAAnC,cAAA,GAAAC,CAAA;UACdmC,OAAO,CAACD,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;UAACnC,cAAA,GAAAC,CAAA;UAC9D,OAAO,IAAI;QACb;MACF,CAAC;MAAA,SAzBa0D,wBAAwBA,CAAAgD,GAAA;QAAA,OAAAd,yBAAA,CAAArD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAxBkB,wBAAwB;IAAA;EAAA;IAAA5C,GAAA;IAAAC,KAAA,EA8BtC,SAAQ8E,6BAA6BA,CAAC3E,OAAgC,EAAU;MAAAnB,cAAA,GAAAoB,CAAA;MAC9E,IAAMwF,aAAa,IAAA5G,cAAA,GAAAC,CAAA,QAAA4G,kBAAA,CAAO,IAAIC,GAAG,CAAC3F,OAAO,CAACQ,gBAAgB,CAACoF,GAAG,CAAC,UAAAlD,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAACV,YAAY;MAAD,CAAC,CAAC,CAAC,EAAC;MAACnD,cAAA,GAAAC,CAAA;MAEtF,OAAO;AACX;AACA;AACA,UAAUkB,OAAO,CAACS,aAAa,CAACqD,UAAU,WAAW9D,OAAO,CAACS,aAAa,CAACuD,YAAY;AACvF,iBAAiBhE,OAAO,CAACqC,YAAY,CAACmC,SAAS,OAAOxE,OAAO,CAACqC,YAAY,CAACkC,OAAO;AAClF,iBAAiBvE,OAAO,CAACqC,YAAY,CAACwD,KAAK,CAACC,SAAS,IAAI9F,OAAO,CAACqC,YAAY,CAACwD,KAAK,CAACE,QAAQ;AAC5F;AACA,sBAAsBN,aAAa,CAACvB,IAAI,CAAC,IAAI,CAAC;AAC9C,mBAAmBlE,OAAO,CAACqC,YAAY,CAACoC,cAAc;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;IACC;EAAC;IAAA7E,GAAA;IAAAC,KAAA;MAAA,IAAAmG,WAAA,GAAAjG,iBAAA,CAKD,WAAyBgD,MAAc,EAAgB;QAAAlE,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAErD,MAAM,IAAImH,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAArH,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAAA,OAAAqH,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QAAD,CAAC,CAAC;QAACrH,cAAA,GAAAC,CAAA;QAGxD,OAAO;UACLkG,WAAW,EAAE,yEAAyE;UACtFC,eAAe,EAAE,CACf,iEAAiE,EACjE,kDAAkD,EAClD,uDAAuD,EACvD,kDAAkD,CACnD;UACDC,cAAc,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,SAAS,CAAC;UAC9DC,mBAAmB,EAAE,iDAAiD;UACtEC,SAAS,EAAE;QACb,CAAC;MACH,CAAC;MAAA,SAjBalC,UAAUA,CAAAkD,GAAA;QAAA,OAAAJ,WAAA,CAAA3E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAV4B,UAAU;IAAA;EAAA;IAAAtD,GAAA;IAAAC,KAAA,EAsBxB,SAAQsD,qBAAqBA,CAACF,QAAa,EAAEjB,YAAoB,EAAmB;MAAAnD,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAClF,OAAO;QACL8F,EAAE,EAAE,IAAI,CAACC,iBAAiB,CAAC,CAAC;QAC5BC,QAAQ,EAAE,WAAW;QACrBlC,QAAQ,EAAE,IAAI,CAACyD,iBAAiB,CAACpD,QAAQ,CAAC;QAC1C8B,KAAK,EAAE,GAAG/C,YAAY,CAACsE,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGvE,YAAY,CAACwE,KAAK,CAAC,CAAC,CAAC,qBAAqB;QAC3FxB,WAAW,EAAE,CAAAnG,cAAA,GAAAG,CAAA,UAAAiE,QAAQ,CAAC+B,WAAW,MAAAnG,cAAA,GAAAG,CAAA,UAAI,wCAAwC;QAC7EiG,eAAe,EAAE,CAAApG,cAAA,GAAAG,CAAA,UAAAiE,QAAQ,CAACgC,eAAe,MAAApG,cAAA,GAAAG,CAAA,UAAI,EAAE;QAC/CkG,cAAc,EAAE,CAAArG,cAAA,GAAAG,CAAA,UAAAiE,QAAQ,CAACiC,cAAc,MAAArG,cAAA,GAAAG,CAAA,UAAI,EAAE;QAC7CmG,mBAAmB,EAAE,CAAAtG,cAAA,GAAAG,CAAA,UAAAiE,QAAQ,CAACkC,mBAAmB,MAAAtG,cAAA,GAAAG,CAAA,UAAI,8BAA8B;QACnFoG,SAAS,EAAE,CAAAvG,cAAA,GAAAG,CAAA,WAAAiE,QAAQ,CAACmC,SAAS,MAAAvG,cAAA,GAAAG,CAAA,WAAI,WAAW;QAC5C2E,UAAU,EAAE,IAAI;QAChB0B,gBAAgB,EAAE,CAACrD,YAAY,CAAC;QAChCsD,SAAS,EAAE,IAAI7F,IAAI,CAAC,CAAC,CAAC8F,WAAW,CAAC;MACpC,CAAC;IACH;EAAC;IAAA3F,GAAA;IAAAC,KAAA,EAKD,SAAQU,uBAAuBA,CAC7B0B,QAAkC,EAClCxB,aAA4B,EACiB;MAAA5B,cAAA,GAAAoB,CAAA;MAC7C,IAAMuD,aAAa,IAAA3E,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAACwB,MAAM,CAAC,UAACC,GAAG,EAAEhB,CAAC,EAAK;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4E,GAAG,GAAGhB,CAAC,CAACiB,UAAU;MAAD,CAAC,EAAE,CAAC,CAAC,GAAG1B,QAAQ,CAACC,MAAM;MAC1F,IAAMuD,aAAa,IAAA5G,cAAA,GAAAC,CAAA,QAAA4G,kBAAA,CAAO,IAAIC,GAAG,CAAC1D,QAAQ,CAAC2D,GAAG,CAAC,UAAAlD,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAACV,YAAY;MAAD,CAAC,CAAC,CAAC,EAAC;MAACnD,cAAA,GAAAC,CAAA;MAEtE,OAAO;QACL2H,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAACzE,QAAQ,CAAC;QAC3C0E,mBAAmB,EAAE,IAAI,CAACC,kBAAkB,CAAC3E,QAAQ,CAAC;QACtD4E,kBAAkB,EAAE,IAAI,CAACC,0BAA0B,CAAC7E,QAAQ,EAAExB,aAAa,CAAC;QAC5EsG,wBAAwB,EAAE,IAAI,CAACC,2BAA2B,CAACvG,aAAa;MAC1E,CAAC;IACH;EAAC;IAAAb,GAAA;IAAAC,KAAA,EAKD,SAAQc,yBAAyBA,CAC/BsB,QAAkC,EACU;MAAApD,cAAA,GAAAoB,CAAA;MAC5C,IAAMuD,aAAa,IAAA3E,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAACwB,MAAM,CAAC,UAACC,GAAG,EAAEhB,CAAC,EAAK;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4E,GAAG,GAAGhB,CAAC,CAACiB,UAAU;MAAD,CAAC,EAAE,CAAC,CAAC,GAAG1B,QAAQ,CAACC,MAAM;MAC1F,IAAM+E,UAAU,IAAApI,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAACwB,MAAM,CAAC,UAACC,GAAG,EAAEhB,CAAC,EAAK;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4E,GAAG,GAAGhB,CAAC,CAACwE,YAAY,CAACC,OAAO;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGlF,QAAQ,CAACC,MAAM;MAACrD,cAAA,GAAAC,CAAA;MAElG,OAAO;QACLsI,cAAc,EAAEC,IAAI,CAACC,KAAK,CAAC9D,aAAa,GAAG,GAAG,CAAC;QAC/C+D,gBAAgB,EAAEF,IAAI,CAACC,KAAK,CAACL,UAAU,GAAG,GAAG,CAAC;QAC9CO,UAAU,EAAE,IAAI,CAACC,mBAAmB,CAACxF,QAAQ,CAAC;QAC9CyF,aAAa,EAAE,IAAI,CAACC,sBAAsB,CAAC1F,QAAQ;MACrD,CAAC;IACH;EAAC;IAAArC,GAAA;IAAAC,KAAA;MAAA,IAAA+H,kBAAA,GAAA7H,iBAAA,CAKD,WACEC,OAAgC,EAChCI,QAA2B,EACmB;QAAAvB,cAAA,GAAAoB,CAAA;QAC9C,IAAM4H,oBAAoB,IAAAhJ,cAAA,GAAAC,CAAA,QAAGsB,QAAQ,CAAC0H,MAAM,CAAC,UAAAC,CAAC,EAAI;UAAAlJ,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAAA,OAAAiJ,CAAC,CAACnF,QAAQ,KAAK,MAAM;QAAD,CAAC,CAAC;QAAC/D,cAAA,GAAAC,CAAA;QAEzE,OAAO;UACLkJ,gBAAgB,EAAEH,oBAAoB,CAACI,OAAO,CAAC,UAAAF,CAAC,EAAI;YAAAlJ,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YAAA,OAAAiJ,CAAC,CAAC9C,eAAe,CAACuB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;UAAD,CAAC,CAAC;UAClF0B,uBAAuB,EAAE,IAAI,CAACC,+BAA+B,CAACnI,OAAO,CAAC;UACtEoI,aAAa,EAAE,IAAI,CAACC,qBAAqB,CAACrI,OAAO,CAACS,aAAa,EAAEL,QAAQ;QAC3E,CAAC;MACH,CAAC;MAAA,SAXaS,iBAAiBA,CAAAyH,GAAA,EAAAC,GAAA;QAAA,OAAAX,kBAAA,CAAAvG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBT,iBAAiB;IAAA;EAAA;IAAAjB,GAAA;IAAAC,KAAA,EAc/B,SAAQ6B,oBAAoBA,CAACO,QAAkC,EAA4C;MAAApD,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACzG,OAAOmD,QAAQ,CAACwB,MAAM,CAAC,UAAC+E,MAAM,EAAEC,QAAQ,EAAK;QAAA5J,cAAA,GAAAoB,CAAA;QAC3C,IAAMyI,IAAI,IAAA7J,cAAA,GAAAC,CAAA,QAAG2J,QAAQ,CAACzG,YAAY;QAACnD,cAAA,GAAAC,CAAA;QACnC,IAAI,CAAC0J,MAAM,CAACE,IAAI,CAAC,EAAE;UAAA7J,cAAA,GAAAG,CAAA;UAAAH,cAAA,GAAAC,CAAA;UAAA0J,MAAM,CAACE,IAAI,CAAC,GAAG,EAAE;QAAA,CAAC;UAAA7J,cAAA,GAAAG,CAAA;QAAA;QAAAH,cAAA,GAAAC,CAAA;QACrC0J,MAAM,CAACE,IAAI,CAAC,CAACpG,IAAI,CAACmG,QAAQ,CAAC;QAAC5J,cAAA,GAAAC,CAAA;QAC5B,OAAO0J,MAAM;MACf,CAAC,EAAE,CAAC,CAA6C,CAAC;IACpD;EAAC;IAAA5I,GAAA;IAAAC,KAAA,EAED,SAAQgE,oBAAoBA,CAAC5B,QAAkC,EAAY;MAAApD,cAAA,GAAAoB,CAAA;MACzE,IAAM0I,MAAgB,IAAA9J,cAAA,GAAAC,CAAA,QAAG,EAAE;MAE3B,IAAMmI,UAAU,IAAApI,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAACwB,MAAM,CAAC,UAACC,GAAG,EAAEhB,CAAC,EAAK;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4E,GAAG,GAAGhB,CAAC,CAACwE,YAAY,CAACC,OAAO;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGlF,QAAQ,CAACC,MAAM;MAACrD,cAAA,GAAAC,CAAA;MAClG,IAAImI,UAAU,GAAG,GAAG,EAAE;QAAApI,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAAA6J,MAAM,CAACrG,IAAI,CAAC,gBAAgB,CAAC;MAAA,CAAC;QAAAzD,cAAA,GAAAG,CAAA;MAAA;MAEpD,IAAM4J,uBAAuB,IAAA/J,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAAC6F,MAAM,CAAC,UAAApF,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAAChC,gBAAgB,CAACmI,aAAa,KAAK,UAAU;MAAD,CAAC,CAAC,CAAC3G,MAAM;MAACrD,cAAA,GAAAC,CAAA;MAC7G,IAAI8J,uBAAuB,GAAG3G,QAAQ,CAACC,MAAM,GAAG,GAAG,EAAE;QAAArD,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAAA6J,MAAM,CAACrG,IAAI,CAAC,2BAA2B,CAAC;MAAA,CAAC;QAAAzD,cAAA,GAAAG,CAAA;MAAA;MAAAH,cAAA,GAAAC,CAAA;MAE9F,OAAO6J,MAAM;IACf;EAAC;IAAA/I,GAAA;IAAAC,KAAA,EAED,SAAQwE,sBAAsBA,CAACpC,QAAkC,EAAU;MAAApD,cAAA,GAAAoB,CAAA;MACzE,IAAM6I,OAAO,IAAAjK,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAAC2D,GAAG,CAAC,UAAAlD,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAACwE,YAAY,CAAC6B,MAAM;MAAD,CAAC,CAAC;MACxD,IAAMC,UAAU,IAAAnK,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACmK,aAAa,CAACH,OAAO,CAAC;MAACjK,cAAA,GAAAC,CAAA;MAC/C,OAAO,iBAAiBkK,UAAU,SAAS;IAC7C;EAAC;IAAApJ,GAAA;IAAAC,KAAA,EAED,SAAQyE,yBAAyBA,CAACrC,QAAkC,EAAU;MAAApD,cAAA,GAAAoB,CAAA;MAC5E,IAAM4I,aAAa,IAAAhK,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAAC6F,MAAM,CAAC,UAAApF,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAAChC,gBAAgB,CAACmI,aAAa,KAAK,UAAU;MAAD,CAAC,CAAC,CAAC3G,MAAM;MAClG,IAAMgH,UAAU,IAAArK,cAAA,GAAAC,CAAA,QAAGuI,IAAI,CAACC,KAAK,CAAEuB,aAAa,GAAG5G,QAAQ,CAACC,MAAM,GAAI,GAAG,CAAC;MAACrD,cAAA,GAAAC,CAAA;MACvE,OAAO,GAAGoK,UAAU,2BAA2B;IACjD;EAAC;IAAAtJ,GAAA;IAAAC,KAAA,EAED,SAAQoJ,aAAaA,CAAIE,GAAQ,EAAK;MAAAtK,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACpC,OAAOqK,GAAG,CAAC1G,IAAI,CAAC,UAACC,CAAC,EAAE1D,CAAC,EACnB;QAAAH,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAAqK,GAAG,CAACrB,MAAM,CAAC,UAAAsB,CAAC,EAAI;UAAAvK,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAAA,OAAAsK,CAAC,KAAK1G,CAAC;QAAD,CAAC,CAAC,CAACR,MAAM,GAAGiH,GAAG,CAACrB,MAAM,CAAC,UAAAsB,CAAC,EAAI;UAAAvK,cAAA,GAAAoB,CAAA;UAAApB,cAAA,GAAAC,CAAA;UAAA,OAAAsK,CAAC,KAAKpK,CAAC;QAAD,CAAC,CAAC,CAACkD,MAAM;MAAD,CAClE,CAAC,CAACmH,GAAG,CAAC,CAAC;IACT;EAAC;IAAAzJ,GAAA;IAAAC,KAAA,EAED,SAAQ6G,iBAAiBA,CAACzE,QAAkC,EAAY;MAAApD,cAAA,GAAAoB,CAAA;MACtE,IAAMwG,SAAmB,IAAA5H,cAAA,GAAAC,CAAA,QAAG,EAAE;MAE9B,IAAM0E,aAAa,IAAA3E,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAACwB,MAAM,CAAC,UAACC,GAAG,EAAEhB,CAAC,EAAK;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4E,GAAG,GAAGhB,CAAC,CAACiB,UAAU;MAAD,CAAC,EAAE,CAAC,CAAC,GAAG1B,QAAQ,CAACC,MAAM;MAACrD,cAAA,GAAAC,CAAA;MAC3F,IAAI0E,aAAa,GAAG,GAAG,EAAE;QAAA3E,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAAA2H,SAAS,CAACnE,IAAI,CAAC,gCAAgC,CAAC;MAAA,CAAC;QAAAzD,cAAA,GAAAG,CAAA;MAAA;MAE1E,IAAMsK,WAAW,IAAAzK,cAAA,GAAAC,CAAA,QAAGmD,QAAQ,CAAC6F,MAAM,CAAC,UAAApF,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAACwE,YAAY,CAACC,OAAO,GAAG,GAAG;MAAD,CAAC,CAAC,CAACjF,MAAM;MAACrD,cAAA,GAAAC,CAAA;MAC9E,IAAIwK,WAAW,GAAGrH,QAAQ,CAACC,MAAM,GAAG,GAAG,EAAE;QAAArD,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAAA2H,SAAS,CAACnE,IAAI,CAAC,iCAAiC,CAAC;MAAA,CAAC;QAAAzD,cAAA,GAAAG,CAAA;MAAA;MAAAH,cAAA,GAAAC,CAAA;MAE3F,OAAO2H,SAAS;IAClB;EAAC;IAAA7G,GAAA;IAAAC,KAAA,EAED,SAAQ+G,kBAAkBA,CAAC3E,QAAkC,EAAY;MAAApD,cAAA,GAAAoB,CAAA;MACvE,IAAMgE,UAAoB,IAAApF,cAAA,GAAAC,CAAA,SAAG,EAAE;MAE/B,IAAMyK,iBAAiB,IAAA1K,cAAA,GAAAC,CAAA,SAAGmD,QAAQ,CAAC6F,MAAM,CAAC,UAAApF,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAAChC,gBAAgB,CAACmI,aAAa,KAAK,MAAM;MAAD,CAAC,CAAC,CAAC3G,MAAM;MAACrD,cAAA,GAAAC,CAAA;MACnG,IAAIyK,iBAAiB,GAAGtH,QAAQ,CAACC,MAAM,GAAG,GAAG,EAAE;QAAArD,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAAAmF,UAAU,CAAC3B,IAAI,CAAC,6BAA6B,CAAC;MAAA,CAAC;QAAAzD,cAAA,GAAAG,CAAA;MAAA;MAE9F,IAAMwK,YAAY,IAAA3K,cAAA,GAAAC,CAAA,SAAGmD,QAAQ,CAAC6F,MAAM,CAAC,UAAApF,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAAChC,gBAAgB,CAAC+I,QAAQ,KAAK,mBAAmB;MAAD,CAAC,CAAC,CAACvH,MAAM;MAACrD,cAAA,GAAAC,CAAA;MACtG,IAAI0K,YAAY,GAAGvH,QAAQ,CAACC,MAAM,GAAG,GAAG,EAAE;QAAArD,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAAAmF,UAAU,CAAC3B,IAAI,CAAC,4BAA4B,CAAC;MAAA,CAAC;QAAAzD,cAAA,GAAAG,CAAA;MAAA;MAAAH,cAAA,GAAAC,CAAA;MAExF,OAAOmF,UAAU;IACnB;EAAC;IAAArE,GAAA;IAAAC,KAAA,EAED,SAAQiH,0BAA0BA,CAChC7E,QAAkC,EAClCxB,aAA4B,EAClB;MAAA5B,cAAA,GAAAoB,CAAA;MACV,IAAMyJ,eAAyB,IAAA7K,cAAA,GAAAC,CAAA,SAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAErC,IAAI2B,aAAa,CAACqD,UAAU,KAAK,UAAU,EAAE;QAAAjF,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAC3C4K,eAAe,CAACpH,IAAI,CAAC,uCAAuC,CAAC;QAACzD,cAAA,GAAAC,CAAA;QAC9D4K,eAAe,CAACpH,IAAI,CAAC,kCAAkC,CAAC;MAC1D,CAAC,MAAM;QAAAzD,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QACL4K,eAAe,CAACpH,IAAI,CAAC,0BAA0B,CAAC;QAACzD,cAAA,GAAAC,CAAA;QACjD4K,eAAe,CAACpH,IAAI,CAAC,sCAAsC,CAAC;MAC9D;MAACzD,cAAA,GAAAC,CAAA;MAED,OAAO4K,eAAe;IACxB;EAAC;IAAA9J,GAAA;IAAAC,KAAA,EAED,SAAQmH,2BAA2BA,CAACvG,aAA4B,EAAsB;MAAA5B,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACpF,IAAI2B,aAAa,CAACkJ,gBAAgB,CAACzH,MAAM,KAAK,CAAC,EAAE;QAAArD,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAC/C,OAAO8K,SAAS;MAClB,CAAC;QAAA/K,cAAA,GAAAG,CAAA;MAAA;MAAAH,cAAA,GAAAC,CAAA;MAED,OAAO,kEAAkE;IAC3E;EAAC;IAAAc,GAAA;IAAAC,KAAA,EAED,SAAQ4H,mBAAmBA,CAACxF,QAAkC,EAAU;MAAApD,cAAA,GAAAoB,CAAA;MAEtE,IAAM4J,qBAAqB,IAAAhL,cAAA,GAAAC,CAAA,SAAGmD,QAAQ,CAAC6F,MAAM,CAAC,UAAApF,CAAC,EAAI;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4D,CAAC,CAAChC,gBAAgB,CAACmI,aAAa,KAAK,UAAU;MAAD,CAAC,CAAC,CAAC3G,MAAM;MAACrD,cAAA,GAAAC,CAAA;MAC3G,OAAOuI,IAAI,CAACC,KAAK,CAAEuC,qBAAqB,GAAG5H,QAAQ,CAACC,MAAM,GAAI,GAAG,CAAC;IACpE;EAAC;IAAAtC,GAAA;IAAAC,KAAA,EAED,SAAQ8H,sBAAsBA,CAAC1F,QAAkC,EAAU;MAAApD,cAAA,GAAAoB,CAAA;MAEzE,IAAMuD,aAAa,IAAA3E,cAAA,GAAAC,CAAA,SAAGmD,QAAQ,CAACwB,MAAM,CAAC,UAACC,GAAG,EAAEhB,CAAC,EAAK;QAAA7D,cAAA,GAAAoB,CAAA;QAAApB,cAAA,GAAAC,CAAA;QAAA,OAAA4E,GAAG,GAAGhB,CAAC,CAACiB,UAAU;MAAD,CAAC,EAAE,CAAC,CAAC,GAAG1B,QAAQ,CAACC,MAAM;MAACrD,cAAA,GAAAC,CAAA;MAC3F,OAAOuI,IAAI,CAACC,KAAK,CAAC9D,aAAa,GAAG,GAAG,CAAC;IACxC;EAAC;IAAA5D,GAAA;IAAAC,KAAA,EAED,SAAQsI,+BAA+BA,CAACnI,OAAgC,EAAY;MAAAnB,cAAA,GAAAoB,CAAA;MAClF,IAAMyJ,eAAe,IAAA7K,cAAA,GAAAC,CAAA,SAAG,CACtB,gEAAgE,EAChE,2CAA2C,EAC3C,mDAAmD,CACpD;MAACD,cAAA,GAAAC,CAAA;MAEF,IAAIkB,OAAO,CAACS,aAAa,CAACqD,UAAU,KAAK,UAAU,EAAE;QAAAjF,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QACnD4K,eAAe,CAACpH,IAAI,CAAC,oCAAoC,CAAC;MAC5D,CAAC,MAAM;QAAAzD,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QACL4K,eAAe,CAACpH,IAAI,CAAC,8CAA8C,CAAC;MACtE;MAACzD,cAAA,GAAAC,CAAA;MAED,OAAO4K,eAAe;IACxB;EAAC;IAAA9J,GAAA;IAAAC,KAAA,EAED,SAAQwI,qBAAqBA,CAAC5H,aAA4B,EAAEL,QAA2B,EAAY;MAAAvB,cAAA,GAAAoB,CAAA;MACjG,IAAMkE,KAAK,IAAAtF,cAAA,GAAAC,CAAA,SAAG,CACZ,2DAA2D,EAC3D,+CAA+C,CAChD;MAACD,cAAA,GAAAC,CAAA;MAEF,IAAI2B,aAAa,CAACqD,UAAU,KAAK,cAAc,EAAE;QAAAjF,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAC/CqF,KAAK,CAAC7B,IAAI,CAAC,8BAA8B,CAAC;MAC5C,CAAC;QAAAzD,cAAA,GAAAG,CAAA;MAAA;MAAAH,cAAA,GAAAC,CAAA;MAED,OAAOqF,KAAK;IACd;EAAC;IAAAvE,GAAA;IAAAC,KAAA,EAED,SAAQwG,iBAAiBA,CAACpD,QAAa,EAA6B;MAAA,IAAA6G,qBAAA,EAAAC,sBAAA;MAAAlL,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAElE,IAAI,CAAAD,cAAA,GAAAG,CAAA,YAAA8K,qBAAA,GAAA7G,QAAQ,CAAC+B,WAAW,aAApB8E,qBAAA,CAAsBE,QAAQ,CAAC,UAAU,CAAC,MAAAnL,cAAA,GAAAG,CAAA,YAAA+K,sBAAA,GAAI9G,QAAQ,CAAC+B,WAAW,aAApB+E,sBAAA,CAAsBC,QAAQ,CAAC,aAAa,CAAC,GAAE;QAAAnL,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAC/F,OAAO,MAAM;MACf,CAAC,MAAM;QAAA,IAAAmL,sBAAA,EAAAC,sBAAA;QAAArL,cAAA,GAAAG,CAAA;QAAAH,cAAA,GAAAC,CAAA;QAAA,IAAI,CAAAD,cAAA,GAAAG,CAAA,YAAAiL,sBAAA,GAAAhH,QAAQ,CAAC+B,WAAW,aAApBiF,sBAAA,CAAsBD,QAAQ,CAAC,WAAW,CAAC,MAAAnL,cAAA,GAAAG,CAAA,YAAAkL,sBAAA,GAAIjH,QAAQ,CAAC+B,WAAW,aAApBkF,sBAAA,CAAsBF,QAAQ,CAAC,aAAa,CAAC,GAAE;UAAAnL,cAAA,GAAAG,CAAA;UAAAH,cAAA,GAAAC,CAAA;UACvG,OAAO,QAAQ;QACjB,CAAC;UAAAD,cAAA,GAAAG,CAAA;QAAA;MAAD;MAACH,cAAA,GAAAC,CAAA;MACD,OAAO,KAAK;IACd;EAAC;IAAAc,GAAA;IAAAC,KAAA,EAED,SAAQ8C,gBAAgBA,CAACC,QAAmC,EAAU;MAAA/D,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MACpE,QAAQ8D,QAAQ;QACd,KAAK,MAAM;UAAA/D,cAAA,GAAAG,CAAA;UAAAH,cAAA,GAAAC,CAAA;UAAE,OAAO,CAAC;QACrB,KAAK,QAAQ;UAAAD,cAAA,GAAAG,CAAA;UAAAH,cAAA,GAAAC,CAAA;UAAE,OAAO,CAAC;QACvB,KAAK,KAAK;UAAAD,cAAA,GAAAG,CAAA;UAAAH,cAAA,GAAAC,CAAA;UAAE,OAAO,CAAC;MACtB;IACF;EAAC;IAAAc,GAAA;IAAAC,KAAA,EAED,SAAQgF,iBAAiBA,CAAA,EAAW;MAAAhG,cAAA,GAAAoB,CAAA;MAAApB,cAAA,GAAAC,CAAA;MAClC,OAAO,WAAWW,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI2H,IAAI,CAAC8C,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3E;EAAC;IAAAzK,GAAA;IAAAC,KAAA;MAAA,IAAAyK,eAAA,GAAAvK,iBAAA,CAED,aAA8C;QAAAlB,cAAA,GAAAoB,CAAA;QAC5C,IAAMP,GAAG,IAAAb,cAAA,GAAAC,CAAA,SAAGW,IAAI,CAACC,GAAG,CAAC,CAAC;QACtB,IAAM6K,cAAc,IAAA1L,cAAA,GAAAC,CAAA,SAAGY,GAAG,GAAG,IAAI,CAACF,aAAa;QAACX,cAAA,GAAAC,CAAA;QAEhD,IAAIyL,cAAc,GAAG,KAAK,EAAE;UAAA1L,cAAA,GAAAG,CAAA;UAAAH,cAAA,GAAAC,CAAA;UAC1B,IAAI,CAACS,YAAY,GAAG,CAAC;UAACV,cAAA,GAAAC,CAAA;UACtB,IAAI,CAACU,aAAa,GAAGE,GAAG;QAC1B,CAAC;UAAAb,cAAA,GAAAG,CAAA;QAAA;QAAAH,cAAA,GAAAC,CAAA;QAED,IAAI,IAAI,CAACS,YAAY,IAAI,IAAI,CAACX,MAAM,CAACU,kBAAkB,EAAE;UAAAT,cAAA,GAAAG,CAAA;UACvD,IAAMwL,QAAQ,IAAA3L,cAAA,GAAAC,CAAA,SAAG,KAAK,GAAGyL,cAAc;UAAC1L,cAAA,GAAAC,CAAA;UACxC,MAAM,IAAImH,OAAO,CAAC,UAAAC,OAAO,EAAI;YAAArH,cAAA,GAAAoB,CAAA;YAAApB,cAAA,GAAAC,CAAA;YAAA,OAAAqH,UAAU,CAACD,OAAO,EAAEsE,QAAQ,CAAC;UAAD,CAAC,CAAC;UAAC3L,cAAA,GAAAC,CAAA;UAC5D,IAAI,CAACS,YAAY,GAAG,CAAC;UAACV,cAAA,GAAAC,CAAA;UACtB,IAAI,CAACU,aAAa,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;QACjC,CAAC;UAAAb,cAAA,GAAAG,CAAA;QAAA;QAAAH,cAAA,GAAAC,CAAA;QAED,IAAI,CAACS,YAAY,EAAE;MACrB,CAAC;MAAA,SAjBaY,cAAcA,CAAA;QAAA,OAAAmK,eAAA,CAAAjJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdnB,cAAc;IAAA;EAAA;AAAA;AAqB9B,OAAO,IAAMsK,uBAAuB,IAAA5L,cAAA,GAAAC,CAAA,SAAG,IAAIJ,uBAAuB,CAAC,CAAC", "ignoreList": []}