{"version": 3, "names": ["useState", "useEffect", "useCallback", "databaseService", "authService", "useProgressData", "cov_2hzhz2h3qp", "f", "_ref", "s", "_ref2", "_slicedToArray", "data", "setData", "_ref3", "_ref4", "loading", "setLoading", "_ref5", "_ref6", "error", "setError", "_ref7", "_ref8", "refreshing", "setRefreshing", "mockProgressData", "overallProgress", "totalSessions", "hoursPlayed", "skillImprovement", "currentStreak", "weeklyImprovement", "skillProgress", "forehand", "current", "change", "trend", "backhand", "serve", "volley", "footwork", "strategy", "weeklyStats", "day", "sessions", "duration", "monthlyGoals", "goal", "progress", "total", "icon", "performanceTrends", "metric", "description", "achievements", "id", "title", "color", "unlocked", "unlocked_at", "aiInsights", "recommendation", "loadProgressData", "_asyncToGenerator", "isRefresh", "arguments", "length", "undefined", "b", "isAuthenticated", "console", "log", "_ref0", "getUserStatsSummary", "statsData", "statsError", "warn", "transformedData", "totalTrainingSessions", "Math", "round", "totalTrainingHours", "db<PERSON><PERSON>r", "err", "Error", "message", "refreshData"], "sources": ["useProgressData.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { databaseService } from '@/services/database/DatabaseService';\nimport { authService } from '@/services/auth/AuthService';\n\nexport interface SkillProgress {\n  current: number;\n  change: number;\n  trend: 'up' | 'down' | 'stable';\n}\n\nexport interface WeeklyStat {\n  day: string;\n  sessions: number;\n  duration: number;\n}\n\nexport interface MonthlyGoal {\n  goal: string;\n  progress: number;\n  total: number;\n  icon: string;\n}\n\nexport interface PerformanceTrend {\n  metric: string;\n  change: number;\n  description: string;\n}\n\nexport interface Achievement {\n  id: string;\n  title: string;\n  description: string;\n  color: string;\n  unlocked: boolean;\n  unlocked_at: string;\n  progress?: number;\n  total?: number;\n}\n\nexport interface AIInsight {\n  title: string;\n  description: string;\n  recommendation: string;\n}\n\nexport interface OverallProgress {\n  totalSessions: number;\n  hoursPlayed: number;\n  skillImprovement: number;\n  currentStreak: number;\n  weeklyImprovement: number;\n}\n\nexport interface ProgressData {\n  overallProgress: OverallProgress;\n  skillProgress: Record<string, SkillProgress>;\n  weeklyStats: WeeklyStat[];\n  monthlyGoals: MonthlyGoal[];\n  performanceTrends: PerformanceTrend[];\n  achievements: Achievement[];\n  aiInsights: AIInsight[];\n}\n\ninterface UseProgressDataReturn {\n  data: ProgressData | null;\n  loading: boolean;\n  error: string | null;\n  refreshing: boolean;\n  refreshData: () => Promise<void>;\n}\n\nexport function useProgressData(): UseProgressDataReturn {\n  const [data, setData] = useState<ProgressData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [refreshing, setRefreshing] = useState(false);\n\n  // Mock progress data for Sara Lee\n  const mockProgressData: ProgressData = {\n    overallProgress: {\n      totalSessions: 127,\n      hoursPlayed: 89,\n      skillImprovement: 12,\n      currentStreak: 7,\n      weeklyImprovement: 8,\n    },\n    skillProgress: {\n      forehand: { current: 78, change: 5, trend: 'up' },\n      backhand: { current: 65, change: 8, trend: 'up' },\n      serve: { current: 82, change: 2, trend: 'up' },\n      volley: { current: 70, change: 3, trend: 'up' },\n      footwork: { current: 75, change: 6, trend: 'up' },\n      strategy: { current: 68, change: 4, trend: 'up' },\n    },\n    weeklyStats: [\n      { day: 'Mon', sessions: 1, duration: 45 },\n      { day: 'Tue', sessions: 0, duration: 0 },\n      { day: 'Wed', sessions: 2, duration: 90 },\n      { day: 'Thu', sessions: 1, duration: 60 },\n      { day: 'Fri', sessions: 0, duration: 0 },\n      { day: 'Sat', sessions: 3, duration: 120 },\n      { day: 'Sun', sessions: 1, duration: 30 },\n    ],\n    monthlyGoals: [\n      {\n        goal: 'Complete 20 training sessions',\n        progress: 14,\n        total: 20,\n        icon: 'calendar',\n      },\n      {\n        goal: 'Improve serve accuracy to 85%',\n        progress: 82,\n        total: 85,\n        icon: 'target',\n      },\n      {\n        goal: 'Upload 15 practice videos',\n        progress: 7,\n        total: 15,\n        icon: 'bar-chart',\n      },\n    ],\n    performanceTrends: [\n      {\n        metric: 'First Serve Percentage',\n        change: 12,\n        description: 'Significant improvement in serve consistency over the past month',\n      },\n      {\n        metric: 'Unforced Errors',\n        change: -8,\n        description: 'Reduced unforced errors through better shot selection',\n      },\n      {\n        metric: 'Net Approaches',\n        change: 15,\n        description: 'More aggressive net play leading to higher point conversion',\n      },\n      {\n        metric: 'Break Point Conversion',\n        change: 6,\n        description: 'Better performance under pressure situations',\n      },\n    ],\n    achievements: [\n      {\n        id: 'achievement-1',\n        title: 'Serve Master',\n        description: 'Achieved 80%+ serve accuracy',\n        color: '#ffe600',\n        unlocked: true,\n        unlocked_at: '2024-12-18T20:00:00Z',\n      },\n      {\n        id: 'achievement-2',\n        title: 'Consistency King',\n        description: 'Complete 7 days of training',\n        color: '#23ba16',\n        unlocked: true,\n        unlocked_at: '2024-12-15T09:00:00Z',\n      },\n      {\n        id: 'achievement-3',\n        title: 'Video Analyst',\n        description: 'Upload 10 training videos',\n        color: '#23ba16',\n        unlocked: false,\n        unlocked_at: '',\n        progress: 7,\n        total: 10,\n      },\n      {\n        id: 'achievement-4',\n        title: 'Rally Master',\n        description: 'Win 20+ shot rallies',\n        color: '#3b82f6',\n        unlocked: true,\n        unlocked_at: '2024-12-10T14:30:00Z',\n      },\n    ],\n    aiInsights: [\n      {\n        title: 'Serve Improvement Trend',\n        description: 'Your serve accuracy has improved by 12% this month, particularly on second serves.',\n        recommendation: 'Continue practicing serve placement drills to maintain this momentum.',\n      },\n      {\n        title: 'Backhand Development',\n        description: 'Significant progress in backhand technique with 8% improvement in consistency.',\n        recommendation: 'Focus on adding more topspin to your backhand for better court positioning.',\n      },\n      {\n        title: 'Match Performance',\n        description: 'Your break point conversion rate has improved, showing better mental toughness.',\n        recommendation: 'Practice high-pressure scenarios to further enhance clutch performance.',\n      },\n    ],\n  };\n\n  const loadProgressData = useCallback(async (isRefresh = false) => {\n    try {\n      if (isRefresh) {\n        setRefreshing(true);\n      } else {\n        setLoading(true);\n      }\n      setError(null);\n\n      // Check if user is authenticated\n      if (!authService.isAuthenticated()) {\n        console.log('User not authenticated, using mock data');\n        setData(mockProgressData);\n        return;\n      }\n\n      try {\n        // Load real data from database\n        const { data: statsData, error: statsError } = await databaseService.getUserStatsSummary();\n\n        if (statsError) {\n          console.warn('Failed to load real progress data, using mock data:', statsError);\n          setData(mockProgressData);\n          return;\n        }\n\n        // Transform database data to match expected format\n        const transformedData: ProgressData = {\n          overallProgress: {\n            totalSessions: statsData.totalTrainingSessions || 0,\n            hoursPlayed: Math.round(statsData.totalTrainingHours || 0),\n            skillImprovement: 12, // TODO: Calculate from skill progression\n            currentStreak: 7, // TODO: Calculate from recent sessions\n            weeklyImprovement: 8, // TODO: Calculate from weekly comparison\n          },\n          skillProgress: {\n            forehand: { current: 78, change: 5, trend: 'up' },\n            backhand: { current: 65, change: 8, trend: 'up' },\n            serve: { current: 82, change: 2, trend: 'up' },\n            volley: { current: 70, change: 3, trend: 'up' },\n            footwork: { current: 75, change: 6, trend: 'up' },\n            strategy: { current: 68, change: 4, trend: 'up' },\n          },\n          weeklyStats: mockProgressData.weeklyStats, // TODO: Calculate from training sessions\n          monthlyGoals: mockProgressData.monthlyGoals, // TODO: Load from user goals\n          performanceTrends: mockProgressData.performanceTrends, // TODO: Calculate from match stats\n          achievements: mockProgressData.achievements, // TODO: Load from user achievements\n          aiInsights: mockProgressData.aiInsights, // TODO: Generate AI insights\n        };\n\n        console.log('Loaded real progress data:', transformedData);\n        setData(transformedData);\n      } catch (dbError) {\n        console.warn('Database error, falling back to mock data:', dbError);\n        setData(mockProgressData);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to load progress data');\n      console.error('Progress data loading error:', err);\n      // Fallback to mock data on error\n      setData(mockProgressData);\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, []);\n\n  const refreshData = useCallback(async () => {\n    await loadProgressData(true);\n  }, [loadProgressData]);\n\n  useEffect(() => {\n    loadProgressData();\n  }, [loadProgressData]);\n\n  return {\n    data,\n    loading,\n    error,\n    refreshing,\n    refreshData,\n  };\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,eAAe;AACxB,SAASC,WAAW;AAsEpB,OAAO,SAASC,eAAeA,CAAA,EAA0B;EAAAC,cAAA,GAAAC,CAAA;EACvD,IAAAC,IAAA,IAAAF,cAAA,GAAAG,CAAA,OAAwBT,QAAQ,CAAsB,IAAI,CAAC;IAAAU,KAAA,GAAAC,cAAA,CAAAH,IAAA;IAApDI,IAAI,GAAAF,KAAA;IAAEG,OAAO,GAAAH,KAAA;EACpB,IAAAI,KAAA,IAAAR,cAAA,GAAAG,CAAA,OAA8BT,QAAQ,CAAC,IAAI,CAAC;IAAAe,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAArCE,OAAO,GAAAD,KAAA;IAAEE,UAAU,GAAAF,KAAA;EAC1B,IAAAG,KAAA,IAAAZ,cAAA,GAAAG,CAAA,OAA0BT,QAAQ,CAAgB,IAAI,CAAC;IAAAmB,KAAA,GAAAR,cAAA,CAAAO,KAAA;IAAhDE,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EACtB,IAAAG,KAAA,IAAAhB,cAAA,GAAAG,CAAA,OAAoCT,QAAQ,CAAC,KAAK,CAAC;IAAAuB,KAAA,GAAAZ,cAAA,CAAAW,KAAA;IAA5CE,UAAU,GAAAD,KAAA;IAAEE,aAAa,GAAAF,KAAA;EAGhC,IAAMG,gBAA8B,IAAApB,cAAA,GAAAG,CAAA,OAAG;IACrCkB,eAAe,EAAE;MACfC,aAAa,EAAE,GAAG;MAClBC,WAAW,EAAE,EAAE;MACfC,gBAAgB,EAAE,EAAE;MACpBC,aAAa,EAAE,CAAC;MAChBC,iBAAiB,EAAE;IACrB,CAAC;IACDC,aAAa,EAAE;MACbC,QAAQ,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC;MACjDC,QAAQ,EAAE;QAAEH,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC;MACjDE,KAAK,EAAE;QAAEJ,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC;MAC9CG,MAAM,EAAE;QAAEL,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC;MAC/CI,QAAQ,EAAE;QAAEN,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK,CAAC;MACjDK,QAAQ,EAAE;QAAEP,OAAO,EAAE,EAAE;QAAEC,MAAM,EAAE,CAAC;QAAEC,KAAK,EAAE;MAAK;IAClD,CAAC;IACDM,WAAW,EAAE,CACX;MAAEC,GAAG,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAG,CAAC,EACzC;MAAEF,GAAG,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE,CAAC,EACxC;MAAEF,GAAG,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAG,CAAC,EACzC;MAAEF,GAAG,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAG,CAAC,EACzC;MAAEF,GAAG,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAE,CAAC,EACxC;MAAEF,GAAG,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAI,CAAC,EAC1C;MAAEF,GAAG,EAAE,KAAK;MAAEC,QAAQ,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAG,CAAC,CAC1C;IACDC,YAAY,EAAE,CACZ;MACEC,IAAI,EAAE,+BAA+B;MACrCC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,IAAI,EAAE,+BAA+B;MACrCC,QAAQ,EAAE,EAAE;MACZC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE;IACR,CAAC,EACD;MACEH,IAAI,EAAE,2BAA2B;MACjCC,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE,EAAE;MACTC,IAAI,EAAE;IACR,CAAC,CACF;IACDC,iBAAiB,EAAE,CACjB;MACEC,MAAM,EAAE,wBAAwB;MAChCjB,MAAM,EAAE,EAAE;MACVkB,WAAW,EAAE;IACf,CAAC,EACD;MACED,MAAM,EAAE,iBAAiB;MACzBjB,MAAM,EAAE,CAAC,CAAC;MACVkB,WAAW,EAAE;IACf,CAAC,EACD;MACED,MAAM,EAAE,gBAAgB;MACxBjB,MAAM,EAAE,EAAE;MACVkB,WAAW,EAAE;IACf,CAAC,EACD;MACED,MAAM,EAAE,wBAAwB;MAChCjB,MAAM,EAAE,CAAC;MACTkB,WAAW,EAAE;IACf,CAAC,CACF;IACDC,YAAY,EAAE,CACZ;MACEC,EAAE,EAAE,eAAe;MACnBC,KAAK,EAAE,cAAc;MACrBH,WAAW,EAAE,8BAA8B;MAC3CI,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;IACf,CAAC,EACD;MACEJ,EAAE,EAAE,eAAe;MACnBC,KAAK,EAAE,kBAAkB;MACzBH,WAAW,EAAE,6BAA6B;MAC1CI,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;IACf,CAAC,EACD;MACEJ,EAAE,EAAE,eAAe;MACnBC,KAAK,EAAE,eAAe;MACtBH,WAAW,EAAE,2BAA2B;MACxCI,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,KAAK;MACfC,WAAW,EAAE,EAAE;MACfX,QAAQ,EAAE,CAAC;MACXC,KAAK,EAAE;IACT,CAAC,EACD;MACEM,EAAE,EAAE,eAAe;MACnBC,KAAK,EAAE,cAAc;MACrBH,WAAW,EAAE,sBAAsB;MACnCI,KAAK,EAAE,SAAS;MAChBC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE;IACf,CAAC,CACF;IACDC,UAAU,EAAE,CACV;MACEJ,KAAK,EAAE,yBAAyB;MAChCH,WAAW,EAAE,oFAAoF;MACjGQ,cAAc,EAAE;IAClB,CAAC,EACD;MACEL,KAAK,EAAE,sBAAsB;MAC7BH,WAAW,EAAE,gFAAgF;MAC7FQ,cAAc,EAAE;IAClB,CAAC,EACD;MACEL,KAAK,EAAE,mBAAmB;MAC1BH,WAAW,EAAE,iFAAiF;MAC9FQ,cAAc,EAAE;IAClB,CAAC;EAEL,CAAC;EAED,IAAMC,gBAAgB,IAAAzD,cAAA,GAAAG,CAAA,OAAGP,WAAW,CAAA8D,iBAAA,CAAC,aAA6B;IAAA,IAAtBC,SAAS,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAA5D,cAAA,GAAA+D,CAAA,UAAG,KAAK;IAAA/D,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC3D,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACF,IAAIwD,SAAS,EAAE;QAAA3D,cAAA,GAAA+D,CAAA;QAAA/D,cAAA,GAAAG,CAAA;QACbgB,aAAa,CAAC,IAAI,CAAC;MACrB,CAAC,MAAM;QAAAnB,cAAA,GAAA+D,CAAA;QAAA/D,cAAA,GAAAG,CAAA;QACLQ,UAAU,CAAC,IAAI,CAAC;MAClB;MAACX,cAAA,GAAAG,CAAA;MACDY,QAAQ,CAAC,IAAI,CAAC;MAACf,cAAA,GAAAG,CAAA;MAGf,IAAI,CAACL,WAAW,CAACkE,eAAe,CAAC,CAAC,EAAE;QAAAhE,cAAA,GAAA+D,CAAA;QAAA/D,cAAA,GAAAG,CAAA;QAClC8D,OAAO,CAACC,GAAG,CAAC,yCAAyC,CAAC;QAAClE,cAAA,GAAAG,CAAA;QACvDI,OAAO,CAACa,gBAAgB,CAAC;QAACpB,cAAA,GAAAG,CAAA;QAC1B;MACF,CAAC;QAAAH,cAAA,GAAA+D,CAAA;MAAA;MAAA/D,cAAA,GAAAG,CAAA;MAED,IAAI;QAEF,IAAAgE,KAAA,IAAAnE,cAAA,GAAAG,CAAA,cAAqDN,eAAe,CAACuE,mBAAmB,CAAC,CAAC;UAA5EC,SAAS,GAAAF,KAAA,CAAf7D,IAAI;UAAoBgE,UAAU,GAAAH,KAAA,CAAjBrD,KAAK;QAA6Dd,cAAA,GAAAG,CAAA;QAE3F,IAAImE,UAAU,EAAE;UAAAtE,cAAA,GAAA+D,CAAA;UAAA/D,cAAA,GAAAG,CAAA;UACd8D,OAAO,CAACM,IAAI,CAAC,qDAAqD,EAAED,UAAU,CAAC;UAACtE,cAAA,GAAAG,CAAA;UAChFI,OAAO,CAACa,gBAAgB,CAAC;UAACpB,cAAA,GAAAG,CAAA;UAC1B;QACF,CAAC;UAAAH,cAAA,GAAA+D,CAAA;QAAA;QAGD,IAAMS,eAA6B,IAAAxE,cAAA,GAAAG,CAAA,QAAG;UACpCkB,eAAe,EAAE;YACfC,aAAa,EAAE,CAAAtB,cAAA,GAAA+D,CAAA,UAAAM,SAAS,CAACI,qBAAqB,MAAAzE,cAAA,GAAA+D,CAAA,UAAI,CAAC;YACnDxC,WAAW,EAAEmD,IAAI,CAACC,KAAK,CAAC,CAAA3E,cAAA,GAAA+D,CAAA,UAAAM,SAAS,CAACO,kBAAkB,MAAA5E,cAAA,GAAA+D,CAAA,UAAI,CAAC,EAAC;YAC1DvC,gBAAgB,EAAE,EAAE;YACpBC,aAAa,EAAE,CAAC;YAChBC,iBAAiB,EAAE;UACrB,CAAC;UACDC,aAAa,EAAE;YACbC,QAAQ,EAAE;cAAEC,OAAO,EAAE,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAK,CAAC;YACjDC,QAAQ,EAAE;cAAEH,OAAO,EAAE,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAK,CAAC;YACjDE,KAAK,EAAE;cAAEJ,OAAO,EAAE,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAK,CAAC;YAC9CG,MAAM,EAAE;cAAEL,OAAO,EAAE,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAK,CAAC;YAC/CI,QAAQ,EAAE;cAAEN,OAAO,EAAE,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAK,CAAC;YACjDK,QAAQ,EAAE;cAAEP,OAAO,EAAE,EAAE;cAAEC,MAAM,EAAE,CAAC;cAAEC,KAAK,EAAE;YAAK;UAClD,CAAC;UACDM,WAAW,EAAEjB,gBAAgB,CAACiB,WAAW;UACzCI,YAAY,EAAErB,gBAAgB,CAACqB,YAAY;UAC3CK,iBAAiB,EAAE1B,gBAAgB,CAAC0B,iBAAiB;UACrDG,YAAY,EAAE7B,gBAAgB,CAAC6B,YAAY;UAC3CM,UAAU,EAAEnC,gBAAgB,CAACmC;QAC/B,CAAC;QAACvD,cAAA,GAAAG,CAAA;QAEF8D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEM,eAAe,CAAC;QAACxE,cAAA,GAAAG,CAAA;QAC3DI,OAAO,CAACiE,eAAe,CAAC;MAC1B,CAAC,CAAC,OAAOK,OAAO,EAAE;QAAA7E,cAAA,GAAAG,CAAA;QAChB8D,OAAO,CAACM,IAAI,CAAC,4CAA4C,EAAEM,OAAO,CAAC;QAAC7E,cAAA,GAAAG,CAAA;QACpEI,OAAO,CAACa,gBAAgB,CAAC;MAC3B;IACF,CAAC,CAAC,OAAO0D,GAAG,EAAE;MAAA9E,cAAA,GAAAG,CAAA;MACZY,QAAQ,CAAC+D,GAAG,YAAYC,KAAK,IAAA/E,cAAA,GAAA+D,CAAA,UAAGe,GAAG,CAACE,OAAO,KAAAhF,cAAA,GAAA+D,CAAA,UAAG,8BAA8B,EAAC;MAAC/D,cAAA,GAAAG,CAAA;MAC9E8D,OAAO,CAACnD,KAAK,CAAC,8BAA8B,EAAEgE,GAAG,CAAC;MAAC9E,cAAA,GAAAG,CAAA;MAEnDI,OAAO,CAACa,gBAAgB,CAAC;IAC3B,CAAC,SAAS;MAAApB,cAAA,GAAAG,CAAA;MACRQ,UAAU,CAAC,KAAK,CAAC;MAACX,cAAA,GAAAG,CAAA;MAClBgB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC,GAAE,EAAE,CAAC;EAEN,IAAM8D,WAAW,IAAAjF,cAAA,GAAAG,CAAA,QAAGP,WAAW,CAAA8D,iBAAA,CAAC,aAAY;IAAA1D,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC1C,MAAMsD,gBAAgB,CAAC,IAAI,CAAC;EAC9B,CAAC,GAAE,CAACA,gBAAgB,CAAC,CAAC;EAACzD,cAAA,GAAAG,CAAA;EAEvBR,SAAS,CAAC,YAAM;IAAAK,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACdsD,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAACzD,cAAA,GAAAG,CAAA;EAEvB,OAAO;IACLG,IAAI,EAAJA,IAAI;IACJI,OAAO,EAAPA,OAAO;IACPI,KAAK,EAALA,KAAK;IACLI,UAAU,EAAVA,UAAU;IACV+D,WAAW,EAAXA;EACF,CAAC;AACH", "ignoreList": []}