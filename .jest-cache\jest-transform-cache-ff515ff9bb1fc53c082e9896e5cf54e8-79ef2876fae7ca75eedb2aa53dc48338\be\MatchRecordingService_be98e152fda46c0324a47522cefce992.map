{"version": 3, "names": ["_VideoRecordingService", "require", "_MatchRepository", "_FileUploadService", "_performance", "MatchRecordingService", "_classCallCheck2", "default", "currentSession", "sessionListeners", "scoreListeners", "offlineSyncQueue", "syncInterval", "autoSaveInterval", "_createClass2", "key", "value", "_startMatch", "_asyncToGenerator2", "metadata", "options", "performanceMonitor", "start", "validateMatchMetadata", "Error", "matchRecording", "id", "Date", "now", "Math", "random", "toString", "substr", "Object", "assign", "startTime", "toISOString", "score", "initializeScore", "matchFormat", "statistics", "initializeStatistics", "userId", "status", "createdAt", "updatedAt", "session", "generateSessionId", "match", "currentSet", "currentGame", "isRecording", "isPaused", "pausedTime", "totalPausedDuration", "videoRecordingActive", "enableVideoRecording", "autoScoreDetection", "enableAutoScoreDetection", "videoRecordingService", "startRecording", "videoConfig", "savedMatch", "saveMatchToDatabase", "success", "error", "data", "databaseId", "setupOfflineSync", "notifySessionListeners", "startAutoSave", "end", "console", "cleanupFailedSession", "startMatch", "_x", "_x2", "apply", "arguments", "_addPoint", "winner", "eventType", "length", "undefined", "shotType", "courtPosition", "gameEvent", "generateEventId", "timestamp", "player", "description", "updatedScore", "updateScore", "updateStatistics", "setComplete", "isSetComplete", "sets", "matchComplete", "isMatchComplete", "gameComplete", "isGameComplete", "endMatch", "updateMatchInDatabase", "notifyScoreListeners", "addPoint", "_x3", "_pauseMatch", "pauseRecording", "pauseMatch", "_resumeMatch", "pauseDuration", "resumeRecording", "resumeMatch", "_endMatch", "endTime", "totalDuration", "durationMinutes", "round", "videoResult", "stopRecording", "uploadResult", "fileUploadService", "uploadVideo", "uri", "folder", "videoUrl", "url", "videoDurationSeconds", "duration", "videoFileSizeBytes", "size", "thumbnail", "thumbnailResult", "uploadThumbnail", "videoThumbnailUrl", "calculateFinalStatistics", "finalMatch", "_cancelMatch", "cancelMatch", "getCurrentSession", "addSessionListener", "listener", "push", "removeSessionListener", "filter", "l", "addScoreListener", "removeScoreListener", "_metadata$opponentNam", "<PERSON><PERSON><PERSON>", "trim", "matchType", "surface", "format", "maxSets", "finalScore", "result", "setsWon", "setsLost", "matchId", "aces", "doubleFaults", "firstServesIn", "firstServesAttempted", "firstServePointsWon", "secondServePointsWon", "firstServeReturnPointsWon", "secondServeReturnPointsWon", "breakPointsConverted", "breakPointsFaced", "winners", "unforcedErrors", "forcedErrors", "totalPointsWon", "totalPointsPlayed", "netPointsAttempted", "netPointsWon", "forehandWinners", "backhandWinners", "forehandErrors", "backhandErrors", "currentScore", "setNumber", "gameNumber", "event", "userGames", "<PERSON><PERSON><PERSON><PERSON>", "is<PERSON><PERSON><PERSON>", "isCompleted", "set", "setsToWin", "firstServePercentage", "breakPointConversionRate", "netSuccessRate", "_saveMatchToDatabase", "matchData", "user_id", "opponent_name", "match_type", "match_format", "location", "court_name", "<PERSON><PERSON><PERSON>", "weather_conditions", "weather", "temperature", "match_date", "split", "start_time", "toTimeString", "current_score", "JSON", "stringify", "created_at", "updated_at", "attempts", "maxAttempts", "_result$data", "matchRepository", "createMatch", "Promise", "resolve", "setTimeout", "_x4", "_updateMatchInDatabase", "updateData", "end_time", "duration_minutes", "getTime", "final_score", "generateFinalScoreString", "determineMatchResult", "sets_won", "sets_lost", "updateMatch", "_x5", "map", "join", "_this", "for<PERSON>ach", "_this2", "Map", "startOfflineSync", "_this3", "clearInterval", "setInterval", "syncOfflineData", "_syncOfflineData", "_this$offlineSyncQueu", "_this$offlineSyncQueu2", "queue", "get", "updates", "_toConsumableArray2", "update", "processOfflineUpdate", "_this$offlineSyncQueu3", "_x6", "_processOfflineUpdate", "type", "warn", "_x7", "_this4", "_cleanupFailedSession", "matchRecordingService", "exports"], "sources": ["MatchRecordingService.ts"], "sourcesContent": ["/**\n * Match Recording Service\n * Core service for recording tennis matches with real-time score tracking\n */\n\nimport { \n  MatchRecording, \n  MatchSession, \n  MatchMetadata, \n  MatchScore, \n  SetScore, \n  GameScore, \n  GameEvent, \n  MatchStatistics,\n  VideoRecordingConfig \n} from '@/src/types/match';\nimport { videoRecordingService } from '@/src/services/video/VideoRecordingService';\nimport { matchRepository } from '@/src/services/database/MatchRepository';\nimport { fileUploadService } from '@/src/services/storage/FileUploadService';\nimport { performanceMonitor } from '@/utils/performance';\n\nexport interface MatchRecordingOptions {\n  enableVideoRecording: boolean;\n  enableAutoScoreDetection: boolean;\n  videoConfig: VideoRecordingConfig;\n  enableStatisticsTracking: boolean;\n}\n\nclass MatchRecordingService {\n  private currentSession: MatchSession | null = null;\n  private sessionListeners: ((session: MatchSession | null) => void)[] = [];\n  private scoreListeners: ((score: MatchScore) => void)[] = [];\n  private offlineSyncQueue: Map<string, any[]> | null = null;\n  private syncInterval: NodeJS.Timeout | null = null;\n  private autoSaveInterval: NodeJS.Timeout | null = null;\n\n  /**\n   * Start a new match recording session with real database integration\n   */\n  async startMatch(\n    metadata: MatchMetadata,\n    options: MatchRecordingOptions\n  ): Promise<MatchSession> {\n    try {\n      performanceMonitor.start('match_recording_start');\n\n      // Validate metadata\n      this.validateMatchMetadata(metadata);\n\n      // Check for existing active session\n      if (this.currentSession) {\n        throw new Error('Another match recording is already in progress');\n      }\n\n      // Initialize match recording\n      const matchRecording: MatchRecording = {\n        id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n        metadata: {\n          ...metadata,\n          startTime: new Date().toISOString(),\n        },\n        score: this.initializeScore(metadata.matchFormat),\n        statistics: this.initializeStatistics(metadata.userId),\n        status: 'recording',\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString(),\n      };\n\n      // Create match session\n      const session: MatchSession = {\n        id: this.generateSessionId(),\n        match: matchRecording,\n        currentSet: 1,\n        currentGame: 1,\n        isRecording: true,\n        isPaused: false,\n        startTime: Date.now(),\n        pausedTime: 0,\n        totalPausedDuration: 0,\n        videoRecordingActive: options.enableVideoRecording,\n        autoScoreDetection: options.enableAutoScoreDetection,\n      };\n\n      // Start video recording if enabled\n      if (options.enableVideoRecording) {\n        await videoRecordingService.startRecording(options.videoConfig);\n      }\n\n      // Save initial match to database with real implementation\n      const savedMatch = await this.saveMatchToDatabase(matchRecording);\n      if (!savedMatch.success) {\n        throw new Error(savedMatch.error || 'Failed to save match to database');\n      }\n\n      session.match.id = savedMatch.data!.id;\n      session.match.databaseId = savedMatch.data!.databaseId;\n\n      // Set up offline sync queue\n      this.setupOfflineSync(session.match.id);\n\n      this.currentSession = session;\n      this.notifySessionListeners();\n\n      // Start auto-save interval\n      this.startAutoSave();\n\n      performanceMonitor.end('match_recording_start');\n      return session;\n    } catch (error) {\n      console.error('Failed to start match recording:', error);\n\n      // Clean up any partial state\n      if (this.currentSession) {\n        await this.cleanupFailedSession();\n      }\n\n      throw error;\n    }\n  }\n\n  /**\n   * Add a point to the current game\n   */\n  async addPoint(\n    winner: 'user' | 'opponent', \n    eventType: 'ace' | 'winner' | 'unforced_error' | 'forced_error' | 'normal' = 'normal',\n    shotType?: string,\n    courtPosition?: string\n  ): Promise<void> {\n    if (!this.currentSession) {\n      throw new Error('No active match session');\n    }\n\n    try {\n      const session = this.currentSession;\n      const currentSet = session.currentSet;\n      const currentGame = session.currentGame;\n\n      // Create game event\n      const gameEvent: GameEvent = {\n        id: this.generateEventId(),\n        timestamp: Date.now(),\n        eventType: eventType === 'normal' ? 'point_won' : eventType,\n        player: winner,\n        shotType: shotType as any,\n        courtPosition: courtPosition as any,\n        description: `Point won by ${winner}`,\n      };\n\n      // Update score\n      const updatedScore = this.updateScore(\n        session.match.score,\n        currentSet,\n        currentGame,\n        winner,\n        gameEvent\n      );\n\n      // Update statistics\n      this.updateStatistics(session.match.statistics, gameEvent);\n\n      // Update session\n      session.match.score = updatedScore;\n      session.match.updatedAt = new Date().toISOString();\n\n      // Check if set or match is complete\n      const setComplete = this.isSetComplete(updatedScore.sets[currentSet - 1]);\n      const matchComplete = this.isMatchComplete(updatedScore, session.match.metadata.matchFormat);\n\n      if (setComplete && !matchComplete) {\n        session.currentSet++;\n        session.currentGame = 1;\n      } else if (!setComplete) {\n        // Check if game is complete\n        const gameComplete = this.isGameComplete(\n          updatedScore.sets[currentSet - 1],\n          currentGame\n        );\n        if (gameComplete) {\n          session.currentGame++;\n        }\n      }\n\n      if (matchComplete) {\n        await this.endMatch();\n      } else {\n        // Save updated match to database\n        await this.updateMatchInDatabase(session.match);\n      }\n\n      this.notifyScoreListeners();\n      this.notifySessionListeners();\n    } catch (error) {\n      console.error('Failed to add point:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Pause the current match\n   */\n  async pauseMatch(): Promise<void> {\n    if (!this.currentSession || this.currentSession.isPaused) {\n      return;\n    }\n\n    try {\n      this.currentSession.isPaused = true;\n      this.currentSession.pausedTime = Date.now();\n      this.currentSession.match.status = 'paused';\n\n      // Pause video recording if active\n      if (this.currentSession.videoRecordingActive) {\n        await videoRecordingService.pauseRecording();\n      }\n\n      await this.updateMatchInDatabase(this.currentSession.match);\n      this.notifySessionListeners();\n    } catch (error) {\n      console.error('Failed to pause match:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Resume the current match\n   */\n  async resumeMatch(): Promise<void> {\n    if (!this.currentSession || !this.currentSession.isPaused) {\n      return;\n    }\n\n    try {\n      const pauseDuration = Date.now() - this.currentSession.pausedTime;\n      this.currentSession.totalPausedDuration += pauseDuration;\n      this.currentSession.isPaused = false;\n      this.currentSession.pausedTime = 0;\n      this.currentSession.match.status = 'recording';\n\n      // Resume video recording if active\n      if (this.currentSession.videoRecordingActive) {\n        await videoRecordingService.resumeRecording();\n      }\n\n      await this.updateMatchInDatabase(this.currentSession.match);\n      this.notifySessionListeners();\n    } catch (error) {\n      console.error('Failed to resume match:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * End the current match\n   */\n  async endMatch(): Promise<MatchRecording> {\n    if (!this.currentSession) {\n      throw new Error('No active match session');\n    }\n\n    try {\n      performanceMonitor.start('match_recording_end');\n\n      const session = this.currentSession;\n      const endTime = Date.now();\n      const totalDuration = (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60;\n\n      // Update match metadata\n      session.match.metadata.endTime = new Date().toISOString();\n      session.match.metadata.durationMinutes = Math.round(totalDuration);\n      session.match.status = 'completed';\n\n      // Stop video recording if active\n      if (session.videoRecordingActive) {\n        const videoResult = await videoRecordingService.stopRecording();\n\n        // Upload video to storage\n        const uploadResult = await fileUploadService.uploadVideo(videoResult.uri, {\n          folder: `matches/${session.match.id || 'temp'}`,\n        });\n\n        if (uploadResult.data) {\n          session.match.videoUrl = uploadResult.data.url;\n          session.match.videoDurationSeconds = videoResult.duration;\n          session.match.videoFileSizeBytes = uploadResult.data.size;\n\n          // Upload thumbnail if available\n          if (videoResult.thumbnail) {\n            const thumbnailResult = await fileUploadService.uploadThumbnail(\n              videoResult.uri,\n              videoResult.thumbnail,\n              {\n                folder: `matches/${session.match.id || 'temp'}/thumbnails`,\n              }\n            );\n\n            if (thumbnailResult.data) {\n              session.match.videoThumbnailUrl = thumbnailResult.data.url;\n            }\n          }\n        }\n      }\n\n      // Calculate final statistics\n      this.calculateFinalStatistics(session.match.statistics, session.match.score);\n\n      // Save final match to database\n      const finalMatch = await this.updateMatchInDatabase(session.match);\n\n      // Clear current session\n      this.currentSession = null;\n      this.notifySessionListeners();\n\n      performanceMonitor.end('match_recording_end');\n      return finalMatch;\n    } catch (error) {\n      console.error('Failed to end match:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Cancel the current match\n   */\n  async cancelMatch(): Promise<void> {\n    if (!this.currentSession) {\n      return;\n    }\n\n    try {\n      // Stop video recording if active\n      if (this.currentSession.videoRecordingActive) {\n        await videoRecordingService.stopRecording();\n      }\n\n      // Update match status\n      this.currentSession.match.status = 'cancelled';\n      await this.updateMatchInDatabase(this.currentSession.match);\n\n      // Clear session\n      this.currentSession = null;\n      this.notifySessionListeners();\n    } catch (error) {\n      console.error('Failed to cancel match:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current session\n   */\n  getCurrentSession(): MatchSession | null {\n    return this.currentSession;\n  }\n\n  /**\n   * Add session listener\n   */\n  addSessionListener(listener: (session: MatchSession | null) => void): void {\n    this.sessionListeners.push(listener);\n  }\n\n  /**\n   * Remove session listener\n   */\n  removeSessionListener(listener: (session: MatchSession | null) => void): void {\n    this.sessionListeners = this.sessionListeners.filter(l => l !== listener);\n  }\n\n  /**\n   * Add score listener\n   */\n  addScoreListener(listener: (score: MatchScore) => void): void {\n    this.scoreListeners.push(listener);\n  }\n\n  /**\n   * Remove score listener\n   */\n  removeScoreListener(listener: (score: MatchScore) => void): void {\n    this.scoreListeners = this.scoreListeners.filter(l => l !== listener);\n  }\n\n  // Private helper methods\n\n  private validateMatchMetadata(metadata: MatchMetadata): void {\n    if (!metadata.opponentName?.trim()) {\n      throw new Error('Opponent name is required');\n    }\n    if (!metadata.userId) {\n      throw new Error('User ID is required');\n    }\n    if (!metadata.matchType) {\n      throw new Error('Match type is required');\n    }\n    if (!metadata.matchFormat) {\n      throw new Error('Match format is required');\n    }\n    if (!metadata.surface) {\n      throw new Error('Court surface is required');\n    }\n  }\n\n  private initializeScore(format: string): MatchScore {\n    const maxSets = format === 'best_of_5' ? 5 : 3;\n    return {\n      sets: [],\n      finalScore: '',\n      result: 'win', // Will be determined at match end\n      setsWon: 0,\n      setsLost: 0,\n    };\n  }\n\n  private initializeStatistics(userId: string): MatchStatistics {\n    return {\n      matchId: '', // Will be set when match is saved\n      userId,\n      aces: 0,\n      doubleFaults: 0,\n      firstServesIn: 0,\n      firstServesAttempted: 0,\n      firstServePointsWon: 0,\n      secondServePointsWon: 0,\n      firstServeReturnPointsWon: 0,\n      secondServeReturnPointsWon: 0,\n      breakPointsConverted: 0,\n      breakPointsFaced: 0,\n      winners: 0,\n      unforcedErrors: 0,\n      forcedErrors: 0,\n      totalPointsWon: 0,\n      totalPointsPlayed: 0,\n      netPointsAttempted: 0,\n      netPointsWon: 0,\n      forehandWinners: 0,\n      backhandWinners: 0,\n      forehandErrors: 0,\n      backhandErrors: 0,\n    };\n  }\n\n  private updateScore(\n    currentScore: MatchScore,\n    setNumber: number,\n    gameNumber: number,\n    winner: 'user' | 'opponent',\n    event: GameEvent\n  ): MatchScore {\n    // Implementation for updating tennis score\n    // This is a simplified version - full tennis scoring logic would be more complex\n    const updatedScore = { ...currentScore };\n    \n    // Ensure we have the current set\n    while (updatedScore.sets.length < setNumber) {\n      updatedScore.sets.push({\n        setNumber: updatedScore.sets.length + 1,\n        userGames: 0,\n        opponentGames: 0,\n        isTiebreak: false,\n        isCompleted: false,\n      });\n    }\n\n    const currentSet = updatedScore.sets[setNumber - 1];\n    \n    // Add point logic here (simplified)\n    if (winner === 'user') {\n      // User wins point - implement tennis scoring logic\n      // This would include 15, 30, 40, game logic\n    } else {\n      // Opponent wins point\n    }\n\n    return updatedScore;\n  }\n\n  private updateStatistics(statistics: MatchStatistics, event: GameEvent): void {\n    statistics.totalPointsPlayed++;\n    \n    if (event.player === 'user') {\n      statistics.totalPointsWon++;\n    }\n\n    switch (event.eventType) {\n      case 'ace':\n        statistics.aces++;\n        break;\n      case 'double_fault':\n        statistics.doubleFaults++;\n        break;\n      case 'winner':\n        statistics.winners++;\n        break;\n      case 'unforced_error':\n        statistics.unforcedErrors++;\n        break;\n      case 'forced_error':\n        statistics.forcedErrors++;\n        break;\n    }\n  }\n\n  private isSetComplete(set: SetScore): boolean {\n    // Simplified set completion logic\n    return (set.userGames >= 6 && set.userGames - set.opponentGames >= 2) ||\n           (set.opponentGames >= 6 && set.opponentGames - set.userGames >= 2) ||\n           set.isTiebreak;\n  }\n\n  private isGameComplete(set: SetScore, gameNumber: number): boolean {\n    // Simplified game completion logic\n    return true; // Placeholder\n  }\n\n  private isMatchComplete(score: MatchScore, format: string): boolean {\n    const setsToWin = format === 'best_of_5' ? 3 : 2;\n    return score.setsWon >= setsToWin || score.setsLost >= setsToWin;\n  }\n\n  private calculateFinalStatistics(statistics: MatchStatistics, score: MatchScore): void {\n    // Calculate percentages and final stats\n    if (statistics.firstServesAttempted > 0) {\n      statistics.firstServePercentage = (statistics.firstServesIn / statistics.firstServesAttempted) * 100;\n    }\n    \n    if (statistics.breakPointsFaced > 0) {\n      statistics.breakPointConversionRate = (statistics.breakPointsConverted / statistics.breakPointsFaced) * 100;\n    }\n    \n    if (statistics.netPointsAttempted > 0) {\n      statistics.netSuccessRate = (statistics.netPointsWon / statistics.netPointsAttempted) * 100;\n    }\n  }\n\n  private async saveMatchToDatabase(match: MatchRecording): Promise<{ success: boolean; data?: any; error?: string }> {\n    try {\n      // Prepare match data for database with comprehensive mapping\n      const matchData = {\n        id: match.id,\n        user_id: match.metadata.userId,\n        opponent_name: match.metadata.opponentName,\n        match_type: match.metadata.matchType || 'friendly',\n        match_format: match.metadata.matchFormat,\n        surface: match.metadata.surface,\n        location: match.metadata.location,\n        court_name: match.metadata.courtName,\n        weather_conditions: match.metadata.weather,\n        temperature: match.metadata.temperature,\n        match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],\n        start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],\n        status: match.status,\n        current_score: JSON.stringify(match.score),\n        statistics: JSON.stringify(match.statistics),\n        created_at: match.createdAt,\n        updated_at: match.updatedAt,\n      };\n\n      // Save to database with retry logic\n      let attempts = 0;\n      const maxAttempts = 3;\n\n      while (attempts < maxAttempts) {\n        try {\n          const result = await matchRepository.createMatch(matchData);\n\n          if (result.error) {\n            if (attempts === maxAttempts - 1) {\n              return { success: false, error: result.error };\n            }\n            attempts++;\n            await new Promise(resolve => setTimeout(resolve, 1000 * attempts));\n            continue;\n          }\n\n          return { success: true, data: { id: match.id, databaseId: result.data?.id } };\n        } catch (error) {\n          attempts++;\n          if (attempts === maxAttempts) {\n            throw error;\n          }\n          await new Promise(resolve => setTimeout(resolve, 1000 * attempts));\n        }\n      }\n\n      return { success: false, error: 'Failed to save after multiple attempts' };\n    } catch (error) {\n      console.error('Error saving match to database:', error);\n      return { success: false, error: 'Database connection failed' };\n    }\n  }\n\n  private async updateMatchInDatabase(match: MatchRecording): Promise<{ success: boolean; error?: string }> {\n    try {\n      if (!match.id) {\n        return { success: false, error: 'Match ID is required for update' };\n      }\n\n      const updateData = {\n        current_score: JSON.stringify(match.score),\n        statistics: JSON.stringify(match.statistics),\n        status: match.status,\n        updated_at: new Date().toISOString(),\n      };\n\n      // Add completion data if match is finished\n      if (match.status === 'completed' && match.metadata.endTime) {\n        updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];\n        updateData.duration_minutes = Math.round(\n          (new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60)\n        );\n        updateData.final_score = this.generateFinalScoreString(match.score);\n        updateData.result = this.determineMatchResult(match.score, match.metadata.userId);\n        updateData.sets_won = match.score.setsWon;\n        updateData.sets_lost = match.score.setsLost;\n      }\n\n      const result = await matchRepository.updateMatch(match.id, updateData);\n\n      if (result.error) {\n        return { success: false, error: result.error };\n      }\n\n      return { success: true };\n    } catch (error) {\n      console.error('Error updating match in database:', error);\n      return { success: false, error: 'Database connection failed' };\n    }\n  }\n\n  /**\n   * Generate final score string for display\n   */\n  private generateFinalScoreString(score: MatchScore): string {\n    if (!score.sets || score.sets.length === 0) {\n      return '0-0';\n    }\n\n    return score.sets\n      .map(set => `${set.userGames}-${set.opponentGames}`)\n      .join(', ');\n  }\n\n  /**\n   * Determine match result from score\n   */\n  private determineMatchResult(score: MatchScore, userId: string): 'win' | 'loss' | 'draw' {\n    if (score.setsWon > score.setsLost) {\n      return 'win';\n    } else if (score.setsLost > score.setsWon) {\n      return 'loss';\n    }\n    return 'draw';\n  }\n\n  private generateSessionId(): string {\n    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private generateEventId(): string {\n    return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private notifySessionListeners(): void {\n    this.sessionListeners.forEach(listener => listener(this.currentSession));\n  }\n\n  private notifyScoreListeners(): void {\n    if (this.currentSession) {\n      this.scoreListeners.forEach(listener => listener(this.currentSession!.match.score));\n    }\n  }\n\n  /**\n   * Set up offline synchronization for the match\n   */\n  private setupOfflineSync(matchId: string): void {\n    try {\n      // Initialize offline sync queue for this match\n      if (!this.offlineSyncQueue) {\n        this.offlineSyncQueue = new Map();\n      }\n\n      // Create sync queue for this match\n      this.offlineSyncQueue.set(matchId, []);\n\n      // Set up periodic sync if online\n      this.startOfflineSync(matchId);\n    } catch (error) {\n      console.error('Failed to setup offline sync:', error);\n    }\n  }\n\n  /**\n   * Start offline synchronization process\n   */\n  private startOfflineSync(matchId: string): void {\n    // Clear any existing sync interval\n    if (this.syncInterval) {\n      clearInterval(this.syncInterval);\n    }\n\n    // Set up sync interval (every 30 seconds)\n    this.syncInterval = setInterval(async () => {\n      await this.syncOfflineData(matchId);\n    }, 30000);\n  }\n\n  /**\n   * Synchronize offline data with server\n   */\n  private async syncOfflineData(matchId: string): Promise<void> {\n    try {\n      const queue = this.offlineSyncQueue?.get(matchId);\n      if (!queue || queue.length === 0) {\n        return;\n      }\n\n      // Process queued updates\n      const updates = [...queue];\n      this.offlineSyncQueue?.set(matchId, []); // Clear queue\n\n      for (const update of updates) {\n        try {\n          await this.processOfflineUpdate(update);\n        } catch (error) {\n          console.error('Failed to sync update:', error);\n          // Re-queue failed update\n          this.offlineSyncQueue?.get(matchId)?.push(update);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to sync offline data:', error);\n    }\n  }\n\n  /**\n   * Process individual offline update\n   */\n  private async processOfflineUpdate(update: any): Promise<void> {\n    switch (update.type) {\n      case 'match_update':\n        await this.updateMatchInDatabase(update.data);\n        break;\n      case 'score_update':\n        // Handle score updates\n        break;\n      case 'statistics_update':\n        // Handle statistics updates\n        break;\n      default:\n        console.warn('Unknown update type:', update.type);\n    }\n  }\n\n  /**\n   * Start auto-save functionality\n   */\n  private startAutoSave(): void {\n    // Clear any existing auto-save interval\n    if (this.autoSaveInterval) {\n      clearInterval(this.autoSaveInterval);\n    }\n\n    // Set up auto-save interval (every 2 minutes)\n    this.autoSaveInterval = setInterval(async () => {\n      if (this.currentSession) {\n        try {\n          await this.updateMatchInDatabase(this.currentSession.match);\n        } catch (error) {\n          console.error('Auto-save failed:', error);\n        }\n      }\n    }, 120000); // 2 minutes\n  }\n\n  /**\n   * Clean up failed session\n   */\n  private async cleanupFailedSession(): Promise<void> {\n    try {\n      if (this.currentSession) {\n        // Stop video recording if active\n        if (this.currentSession.videoRecordingActive) {\n          await videoRecordingService.stopRecording();\n        }\n\n        // Clear intervals\n        if (this.autoSaveInterval) {\n          clearInterval(this.autoSaveInterval);\n          this.autoSaveInterval = null;\n        }\n\n        if (this.syncInterval) {\n          clearInterval(this.syncInterval);\n          this.syncInterval = null;\n        }\n\n        // Clear session\n        this.currentSession = null;\n      }\n    } catch (error) {\n      console.error('Failed to cleanup session:', error);\n    }\n  }\n}\n\n// Export singleton instance\nexport const matchRecordingService = new MatchRecordingService();\n"], "mappings": ";;;;;;;;;AAgBA,IAAAA,sBAAA,GAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAD,OAAA;AACA,IAAAE,kBAAA,GAAAF,OAAA;AACA,IAAAG,YAAA,GAAAH,OAAA;AAAyD,IASnDI,qBAAqB;EAAA,SAAAA,sBAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,qBAAA;IAAA,KACjBG,cAAc,GAAwB,IAAI;IAAA,KAC1CC,gBAAgB,GAA+C,EAAE;IAAA,KACjEC,cAAc,GAAoC,EAAE;IAAA,KACpDC,gBAAgB,GAA8B,IAAI;IAAA,KAClDC,YAAY,GAA0B,IAAI;IAAA,KAC1CC,gBAAgB,GAA0B,IAAI;EAAA;EAAA,WAAAC,aAAA,CAAAP,OAAA,EAAAF,qBAAA;IAAAU,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,OAAAC,kBAAA,CAAAX,OAAA,EAKtD,WACEY,QAAuB,EACvBC,OAA8B,EACP;QACvB,IAAI;UACFC,+BAAkB,CAACC,KAAK,CAAC,uBAAuB,CAAC;UAGjD,IAAI,CAACC,qBAAqB,CAACJ,QAAQ,CAAC;UAGpC,IAAI,IAAI,CAACX,cAAc,EAAE;YACvB,MAAM,IAAIgB,KAAK,CAAC,gDAAgD,CAAC;UACnE;UAGA,IAAMC,cAA8B,GAAG;YACrCC,EAAE,EAAE,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACpEb,QAAQ,EAAAc,MAAA,CAAAC,MAAA,KACHf,QAAQ;cACXgB,SAAS,EAAE,IAAIR,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;YAAC,EACpC;YACDC,KAAK,EAAE,IAAI,CAACC,eAAe,CAACnB,QAAQ,CAACoB,WAAW,CAAC;YACjDC,UAAU,EAAE,IAAI,CAACC,oBAAoB,CAACtB,QAAQ,CAACuB,MAAM,CAAC;YACtDC,MAAM,EAAE,WAAW;YACnBC,SAAS,EAAE,IAAIjB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;YACnCS,SAAS,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UACpC,CAAC;UAGD,IAAMU,OAAqB,GAAG;YAC5BpB,EAAE,EAAE,IAAI,CAACqB,iBAAiB,CAAC,CAAC;YAC5BC,KAAK,EAAEvB,cAAc;YACrBwB,UAAU,EAAE,CAAC;YACbC,WAAW,EAAE,CAAC;YACdC,WAAW,EAAE,IAAI;YACjBC,QAAQ,EAAE,KAAK;YACfjB,SAAS,EAAER,IAAI,CAACC,GAAG,CAAC,CAAC;YACrByB,UAAU,EAAE,CAAC;YACbC,mBAAmB,EAAE,CAAC;YACtBC,oBAAoB,EAAEnC,OAAO,CAACoC,oBAAoB;YAClDC,kBAAkB,EAAErC,OAAO,CAACsC;UAC9B,CAAC;UAGD,IAAItC,OAAO,CAACoC,oBAAoB,EAAE;YAChC,MAAMG,4CAAqB,CAACC,cAAc,CAACxC,OAAO,CAACyC,WAAW,CAAC;UACjE;UAGA,IAAMC,UAAU,SAAS,IAAI,CAACC,mBAAmB,CAACtC,cAAc,CAAC;UACjE,IAAI,CAACqC,UAAU,CAACE,OAAO,EAAE;YACvB,MAAM,IAAIxC,KAAK,CAACsC,UAAU,CAACG,KAAK,IAAI,kCAAkC,CAAC;UACzE;UAEAnB,OAAO,CAACE,KAAK,CAACtB,EAAE,GAAGoC,UAAU,CAACI,IAAI,CAAExC,EAAE;UACtCoB,OAAO,CAACE,KAAK,CAACmB,UAAU,GAAGL,UAAU,CAACI,IAAI,CAAEC,UAAU;UAGtD,IAAI,CAACC,gBAAgB,CAACtB,OAAO,CAACE,KAAK,CAACtB,EAAE,CAAC;UAEvC,IAAI,CAAClB,cAAc,GAAGsC,OAAO;UAC7B,IAAI,CAACuB,sBAAsB,CAAC,CAAC;UAG7B,IAAI,CAACC,aAAa,CAAC,CAAC;UAEpBjD,+BAAkB,CAACkD,GAAG,CAAC,uBAAuB,CAAC;UAC/C,OAAOzB,OAAO;QAChB,CAAC,CAAC,OAAOmB,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAGxD,IAAI,IAAI,CAACzD,cAAc,EAAE;YACvB,MAAM,IAAI,CAACiE,oBAAoB,CAAC,CAAC;UACnC;UAEA,MAAMR,KAAK;QACb;MACF,CAAC;MAAA,SA/EKS,UAAUA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAA3D,WAAA,CAAA4D,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVJ,UAAU;IAAA;EAAA;IAAA3D,GAAA;IAAAC,KAAA;MAAA,IAAA+D,SAAA,OAAA7D,kBAAA,CAAAX,OAAA,EAoFhB,WACEyE,MAA2B,EAIZ;QAAA,IAHfC,SAA0E,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,MAAG,QAAQ;QAAA,IACrFM,QAAiB,GAAAN,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAAK,SAAA;QAAA,IACjBE,aAAsB,GAAAP,SAAA,CAAAI,MAAA,OAAAJ,SAAA,MAAAK,SAAA;QAEtB,IAAI,CAAC,IAAI,CAAC3E,cAAc,EAAE;UACxB,MAAM,IAAIgB,KAAK,CAAC,yBAAyB,CAAC;QAC5C;QAEA,IAAI;UACF,IAAMsB,OAAO,GAAG,IAAI,CAACtC,cAAc;UACnC,IAAMyC,UAAU,GAAGH,OAAO,CAACG,UAAU;UACrC,IAAMC,WAAW,GAAGJ,OAAO,CAACI,WAAW;UAGvC,IAAMoC,SAAoB,GAAG;YAC3B5D,EAAE,EAAE,IAAI,CAAC6D,eAAe,CAAC,CAAC;YAC1BC,SAAS,EAAE7D,IAAI,CAACC,GAAG,CAAC,CAAC;YACrBqD,SAAS,EAAEA,SAAS,KAAK,QAAQ,GAAG,WAAW,GAAGA,SAAS;YAC3DQ,MAAM,EAAET,MAAM;YACdI,QAAQ,EAAEA,QAAe;YACzBC,aAAa,EAAEA,aAAoB;YACnCK,WAAW,EAAE,gBAAgBV,MAAM;UACrC,CAAC;UAGD,IAAMW,YAAY,GAAG,IAAI,CAACC,WAAW,CACnC9C,OAAO,CAACE,KAAK,CAACX,KAAK,EACnBY,UAAU,EACVC,WAAW,EACX8B,MAAM,EACNM,SACF,CAAC;UAGD,IAAI,CAACO,gBAAgB,CAAC/C,OAAO,CAACE,KAAK,CAACR,UAAU,EAAE8C,SAAS,CAAC;UAG1DxC,OAAO,CAACE,KAAK,CAACX,KAAK,GAAGsD,YAAY;UAClC7C,OAAO,CAACE,KAAK,CAACH,SAAS,GAAG,IAAIlB,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UAGlD,IAAM0D,WAAW,GAAG,IAAI,CAACC,aAAa,CAACJ,YAAY,CAACK,IAAI,CAAC/C,UAAU,GAAG,CAAC,CAAC,CAAC;UACzE,IAAMgD,aAAa,GAAG,IAAI,CAACC,eAAe,CAACP,YAAY,EAAE7C,OAAO,CAACE,KAAK,CAAC7B,QAAQ,CAACoB,WAAW,CAAC;UAE5F,IAAIuD,WAAW,IAAI,CAACG,aAAa,EAAE;YACjCnD,OAAO,CAACG,UAAU,EAAE;YACpBH,OAAO,CAACI,WAAW,GAAG,CAAC;UACzB,CAAC,MAAM,IAAI,CAAC4C,WAAW,EAAE;YAEvB,IAAMK,YAAY,GAAG,IAAI,CAACC,cAAc,CACtCT,YAAY,CAACK,IAAI,CAAC/C,UAAU,GAAG,CAAC,CAAC,EACjCC,WACF,CAAC;YACD,IAAIiD,YAAY,EAAE;cAChBrD,OAAO,CAACI,WAAW,EAAE;YACvB;UACF;UAEA,IAAI+C,aAAa,EAAE;YACjB,MAAM,IAAI,CAACI,QAAQ,CAAC,CAAC;UACvB,CAAC,MAAM;YAEL,MAAM,IAAI,CAACC,qBAAqB,CAACxD,OAAO,CAACE,KAAK,CAAC;UACjD;UAEA,IAAI,CAACuD,oBAAoB,CAAC,CAAC;UAC3B,IAAI,CAAClC,sBAAsB,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAzEKuC,QAAQA,CAAAC,GAAA;QAAA,OAAA1B,SAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAR0B,QAAQ;IAAA;EAAA;IAAAzF,GAAA;IAAAC,KAAA;MAAA,IAAA0F,WAAA,OAAAxF,kBAAA,CAAAX,OAAA,EA8Ed,aAAkC;QAChC,IAAI,CAAC,IAAI,CAACC,cAAc,IAAI,IAAI,CAACA,cAAc,CAAC4C,QAAQ,EAAE;UACxD;QACF;QAEA,IAAI;UACF,IAAI,CAAC5C,cAAc,CAAC4C,QAAQ,GAAG,IAAI;UACnC,IAAI,CAAC5C,cAAc,CAAC6C,UAAU,GAAG1B,IAAI,CAACC,GAAG,CAAC,CAAC;UAC3C,IAAI,CAACpB,cAAc,CAACwC,KAAK,CAACL,MAAM,GAAG,QAAQ;UAG3C,IAAI,IAAI,CAACnC,cAAc,CAAC+C,oBAAoB,EAAE;YAC5C,MAAMI,4CAAqB,CAACgD,cAAc,CAAC,CAAC;UAC9C;UAEA,MAAM,IAAI,CAACL,qBAAqB,CAAC,IAAI,CAAC9F,cAAc,CAACwC,KAAK,CAAC;UAC3D,IAAI,CAACqB,sBAAsB,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SArBK2C,UAAUA,CAAA;QAAA,OAAAF,WAAA,CAAA7B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAV8B,UAAU;IAAA;EAAA;IAAA7F,GAAA;IAAAC,KAAA;MAAA,IAAA6F,YAAA,OAAA3F,kBAAA,CAAAX,OAAA,EA0BhB,aAAmC;QACjC,IAAI,CAAC,IAAI,CAACC,cAAc,IAAI,CAAC,IAAI,CAACA,cAAc,CAAC4C,QAAQ,EAAE;UACzD;QACF;QAEA,IAAI;UACF,IAAM0D,aAAa,GAAGnF,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACpB,cAAc,CAAC6C,UAAU;UACjE,IAAI,CAAC7C,cAAc,CAAC8C,mBAAmB,IAAIwD,aAAa;UACxD,IAAI,CAACtG,cAAc,CAAC4C,QAAQ,GAAG,KAAK;UACpC,IAAI,CAAC5C,cAAc,CAAC6C,UAAU,GAAG,CAAC;UAClC,IAAI,CAAC7C,cAAc,CAACwC,KAAK,CAACL,MAAM,GAAG,WAAW;UAG9C,IAAI,IAAI,CAACnC,cAAc,CAAC+C,oBAAoB,EAAE;YAC5C,MAAMI,4CAAqB,CAACoD,eAAe,CAAC,CAAC;UAC/C;UAEA,MAAM,IAAI,CAACT,qBAAqB,CAAC,IAAI,CAAC9F,cAAc,CAACwC,KAAK,CAAC;UAC3D,IAAI,CAACqB,sBAAsB,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAvBK+C,WAAWA,CAAA;QAAA,OAAAH,YAAA,CAAAhC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXkC,WAAW;IAAA;EAAA;IAAAjG,GAAA;IAAAC,KAAA;MAAA,IAAAiG,SAAA,OAAA/F,kBAAA,CAAAX,OAAA,EA4BjB,aAA0C;QACxC,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;UACxB,MAAM,IAAIgB,KAAK,CAAC,yBAAyB,CAAC;QAC5C;QAEA,IAAI;UACFH,+BAAkB,CAACC,KAAK,CAAC,qBAAqB,CAAC;UAE/C,IAAMwB,OAAO,GAAG,IAAI,CAACtC,cAAc;UACnC,IAAM0G,OAAO,GAAGvF,IAAI,CAACC,GAAG,CAAC,CAAC;UAC1B,IAAMuF,aAAa,GAAG,CAACD,OAAO,GAAGpE,OAAO,CAACX,SAAS,GAAGW,OAAO,CAACQ,mBAAmB,IAAI,IAAI,GAAG,EAAE;UAG7FR,OAAO,CAACE,KAAK,CAAC7B,QAAQ,CAAC+F,OAAO,GAAG,IAAIvF,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;UACzDU,OAAO,CAACE,KAAK,CAAC7B,QAAQ,CAACiG,eAAe,GAAGvF,IAAI,CAACwF,KAAK,CAACF,aAAa,CAAC;UAClErE,OAAO,CAACE,KAAK,CAACL,MAAM,GAAG,WAAW;UAGlC,IAAIG,OAAO,CAACS,oBAAoB,EAAE;YAChC,IAAM+D,WAAW,SAAS3D,4CAAqB,CAAC4D,aAAa,CAAC,CAAC;YAG/D,IAAMC,YAAY,SAASC,oCAAiB,CAACC,WAAW,CAACJ,WAAW,CAACK,GAAG,EAAE;cACxEC,MAAM,EAAE,WAAW9E,OAAO,CAACE,KAAK,CAACtB,EAAE,IAAI,MAAM;YAC/C,CAAC,CAAC;YAEF,IAAI8F,YAAY,CAACtD,IAAI,EAAE;cACrBpB,OAAO,CAACE,KAAK,CAAC6E,QAAQ,GAAGL,YAAY,CAACtD,IAAI,CAAC4D,GAAG;cAC9ChF,OAAO,CAACE,KAAK,CAAC+E,oBAAoB,GAAGT,WAAW,CAACU,QAAQ;cACzDlF,OAAO,CAACE,KAAK,CAACiF,kBAAkB,GAAGT,YAAY,CAACtD,IAAI,CAACgE,IAAI;cAGzD,IAAIZ,WAAW,CAACa,SAAS,EAAE;gBACzB,IAAMC,eAAe,SAASX,oCAAiB,CAACY,eAAe,CAC7Df,WAAW,CAACK,GAAG,EACfL,WAAW,CAACa,SAAS,EACrB;kBACEP,MAAM,EAAE,WAAW9E,OAAO,CAACE,KAAK,CAACtB,EAAE,IAAI,MAAM;gBAC/C,CACF,CAAC;gBAED,IAAI0G,eAAe,CAAClE,IAAI,EAAE;kBACxBpB,OAAO,CAACE,KAAK,CAACsF,iBAAiB,GAAGF,eAAe,CAAClE,IAAI,CAAC4D,GAAG;gBAC5D;cACF;YACF;UACF;UAGA,IAAI,CAACS,wBAAwB,CAACzF,OAAO,CAACE,KAAK,CAACR,UAAU,EAAEM,OAAO,CAACE,KAAK,CAACX,KAAK,CAAC;UAG5E,IAAMmG,UAAU,SAAS,IAAI,CAAClC,qBAAqB,CAACxD,OAAO,CAACE,KAAK,CAAC;UAGlE,IAAI,CAACxC,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC6D,sBAAsB,CAAC,CAAC;UAE7BhD,+BAAkB,CAACkD,GAAG,CAAC,qBAAqB,CAAC;UAC7C,OAAOiE,UAAU;QACnB,CAAC,CAAC,OAAOvE,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC5C,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAhEKoC,QAAQA,CAAA;QAAA,OAAAY,SAAA,CAAApC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAARuB,QAAQ;IAAA;EAAA;IAAAtF,GAAA;IAAAC,KAAA;MAAA,IAAAyH,YAAA,OAAAvH,kBAAA,CAAAX,OAAA,EAqEd,aAAmC;QACjC,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;UACxB;QACF;QAEA,IAAI;UAEF,IAAI,IAAI,CAACA,cAAc,CAAC+C,oBAAoB,EAAE;YAC5C,MAAMI,4CAAqB,CAAC4D,aAAa,CAAC,CAAC;UAC7C;UAGA,IAAI,CAAC/G,cAAc,CAACwC,KAAK,CAACL,MAAM,GAAG,WAAW;UAC9C,MAAM,IAAI,CAAC2D,qBAAqB,CAAC,IAAI,CAAC9F,cAAc,CAACwC,KAAK,CAAC;UAG3D,IAAI,CAACxC,cAAc,GAAG,IAAI;UAC1B,IAAI,CAAC6D,sBAAsB,CAAC,CAAC;QAC/B,CAAC,CAAC,OAAOJ,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;UAC/C,MAAMA,KAAK;QACb;MACF,CAAC;MAAA,SAtBKyE,WAAWA,CAAA;QAAA,OAAAD,YAAA,CAAA5D,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAX4D,WAAW;IAAA;EAAA;IAAA3H,GAAA;IAAAC,KAAA,EA2BjB,SAAA2H,iBAAiBA,CAAA,EAAwB;MACvC,OAAO,IAAI,CAACnI,cAAc;IAC5B;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAKD,SAAA4H,kBAAkBA,CAACC,QAAgD,EAAQ;MACzE,IAAI,CAACpI,gBAAgB,CAACqI,IAAI,CAACD,QAAQ,CAAC;IACtC;EAAC;IAAA9H,GAAA;IAAAC,KAAA,EAKD,SAAA+H,qBAAqBA,CAACF,QAAgD,EAAQ;MAC5E,IAAI,CAACpI,gBAAgB,GAAG,IAAI,CAACA,gBAAgB,CAACuI,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,KAAKJ,QAAQ;MAAA,EAAC;IAC3E;EAAC;IAAA9H,GAAA;IAAAC,KAAA,EAKD,SAAAkI,gBAAgBA,CAACL,QAAqC,EAAQ;MAC5D,IAAI,CAACnI,cAAc,CAACoI,IAAI,CAACD,QAAQ,CAAC;IACpC;EAAC;IAAA9H,GAAA;IAAAC,KAAA,EAKD,SAAAmI,mBAAmBA,CAACN,QAAqC,EAAQ;MAC/D,IAAI,CAACnI,cAAc,GAAG,IAAI,CAACA,cAAc,CAACsI,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,KAAKJ,QAAQ;MAAA,EAAC;IACvE;EAAC;IAAA9H,GAAA;IAAAC,KAAA,EAID,SAAQO,qBAAqBA,CAACJ,QAAuB,EAAQ;MAAA,IAAAiI,qBAAA;MAC3D,IAAI,GAAAA,qBAAA,GAACjI,QAAQ,CAACkI,YAAY,aAArBD,qBAAA,CAAuBE,IAAI,CAAC,CAAC,GAAE;QAClC,MAAM,IAAI9H,KAAK,CAAC,2BAA2B,CAAC;MAC9C;MACA,IAAI,CAACL,QAAQ,CAACuB,MAAM,EAAE;QACpB,MAAM,IAAIlB,KAAK,CAAC,qBAAqB,CAAC;MACxC;MACA,IAAI,CAACL,QAAQ,CAACoI,SAAS,EAAE;QACvB,MAAM,IAAI/H,KAAK,CAAC,wBAAwB,CAAC;MAC3C;MACA,IAAI,CAACL,QAAQ,CAACoB,WAAW,EAAE;QACzB,MAAM,IAAIf,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MACA,IAAI,CAACL,QAAQ,CAACqI,OAAO,EAAE;QACrB,MAAM,IAAIhI,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IACF;EAAC;IAAAT,GAAA;IAAAC,KAAA,EAED,SAAQsB,eAAeA,CAACmH,MAAc,EAAc;MAClD,IAAMC,OAAO,GAAGD,MAAM,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC;MAC9C,OAAO;QACLzD,IAAI,EAAE,EAAE;QACR2D,UAAU,EAAE,EAAE;QACdC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE;MACZ,CAAC;IACH;EAAC;IAAA/I,GAAA;IAAAC,KAAA,EAED,SAAQyB,oBAAoBA,CAACC,MAAc,EAAmB;MAC5D,OAAO;QACLqH,OAAO,EAAE,EAAE;QACXrH,MAAM,EAANA,MAAM;QACNsH,IAAI,EAAE,CAAC;QACPC,YAAY,EAAE,CAAC;QACfC,aAAa,EAAE,CAAC;QAChBC,oBAAoB,EAAE,CAAC;QACvBC,mBAAmB,EAAE,CAAC;QACtBC,oBAAoB,EAAE,CAAC;QACvBC,yBAAyB,EAAE,CAAC;QAC5BC,0BAA0B,EAAE,CAAC;QAC7BC,oBAAoB,EAAE,CAAC;QACvBC,gBAAgB,EAAE,CAAC;QACnBC,OAAO,EAAE,CAAC;QACVC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE,CAAC;QACfC,cAAc,EAAE,CAAC;QACjBC,iBAAiB,EAAE,CAAC;QACpBC,kBAAkB,EAAE,CAAC;QACrBC,YAAY,EAAE,CAAC;QACfC,eAAe,EAAE,CAAC;QAClBC,eAAe,EAAE,CAAC;QAClBC,cAAc,EAAE,CAAC;QACjBC,cAAc,EAAE;MAClB,CAAC;IACH;EAAC;IAAArK,GAAA;IAAAC,KAAA,EAED,SAAQ4E,WAAWA,CACjByF,YAAwB,EACxBC,SAAiB,EACjBC,UAAkB,EAClBvG,MAA2B,EAC3BwG,KAAgB,EACJ;MAGZ,IAAM7F,YAAY,GAAA1D,MAAA,CAAAC,MAAA,KAAQmJ,YAAY,CAAE;MAGxC,OAAO1F,YAAY,CAACK,IAAI,CAACd,MAAM,GAAGoG,SAAS,EAAE;QAC3C3F,YAAY,CAACK,IAAI,CAAC8C,IAAI,CAAC;UACrBwC,SAAS,EAAE3F,YAAY,CAACK,IAAI,CAACd,MAAM,GAAG,CAAC;UACvCuG,SAAS,EAAE,CAAC;UACZC,aAAa,EAAE,CAAC;UAChBC,UAAU,EAAE,KAAK;UACjBC,WAAW,EAAE;QACf,CAAC,CAAC;MACJ;MAEA,IAAM3I,UAAU,GAAG0C,YAAY,CAACK,IAAI,CAACsF,SAAS,GAAG,CAAC,CAAC;MAGnD,IAAItG,MAAM,KAAK,MAAM,EAAE,CAGvB,CAAC,MAAM,CAEP;MAEA,OAAOW,YAAY;IACrB;EAAC;IAAA5E,GAAA;IAAAC,KAAA,EAED,SAAQ6E,gBAAgBA,CAACrD,UAA2B,EAAEgJ,KAAgB,EAAQ;MAC5EhJ,UAAU,CAACsI,iBAAiB,EAAE;MAE9B,IAAIU,KAAK,CAAC/F,MAAM,KAAK,MAAM,EAAE;QAC3BjD,UAAU,CAACqI,cAAc,EAAE;MAC7B;MAEA,QAAQW,KAAK,CAACvG,SAAS;QACrB,KAAK,KAAK;UACRzC,UAAU,CAACwH,IAAI,EAAE;UACjB;QACF,KAAK,cAAc;UACjBxH,UAAU,CAACyH,YAAY,EAAE;UACzB;QACF,KAAK,QAAQ;UACXzH,UAAU,CAACkI,OAAO,EAAE;UACpB;QACF,KAAK,gBAAgB;UACnBlI,UAAU,CAACmI,cAAc,EAAE;UAC3B;QACF,KAAK,cAAc;UACjBnI,UAAU,CAACoI,YAAY,EAAE;UACzB;MACJ;IACF;EAAC;IAAA7J,GAAA;IAAAC,KAAA,EAED,SAAQ+E,aAAaA,CAAC8F,GAAa,EAAW;MAE5C,OAAQA,GAAG,CAACJ,SAAS,IAAI,CAAC,IAAII,GAAG,CAACJ,SAAS,GAAGI,GAAG,CAACH,aAAa,IAAI,CAAC,IAC5DG,GAAG,CAACH,aAAa,IAAI,CAAC,IAAIG,GAAG,CAACH,aAAa,GAAGG,GAAG,CAACJ,SAAS,IAAI,CAAE,IAClEI,GAAG,CAACF,UAAU;IACvB;EAAC;IAAA5K,GAAA;IAAAC,KAAA,EAED,SAAQoF,cAAcA,CAACyF,GAAa,EAAEN,UAAkB,EAAW;MAEjE,OAAO,IAAI;IACb;EAAC;IAAAxK,GAAA;IAAAC,KAAA,EAED,SAAQkF,eAAeA,CAAC7D,KAAiB,EAAEoH,MAAc,EAAW;MAClE,IAAMqC,SAAS,GAAGrC,MAAM,KAAK,WAAW,GAAG,CAAC,GAAG,CAAC;MAChD,OAAOpH,KAAK,CAACwH,OAAO,IAAIiC,SAAS,IAAIzJ,KAAK,CAACyH,QAAQ,IAAIgC,SAAS;IAClE;EAAC;IAAA/K,GAAA;IAAAC,KAAA,EAED,SAAQuH,wBAAwBA,CAAC/F,UAA2B,EAAEH,KAAiB,EAAQ;MAErF,IAAIG,UAAU,CAAC2H,oBAAoB,GAAG,CAAC,EAAE;QACvC3H,UAAU,CAACuJ,oBAAoB,GAAIvJ,UAAU,CAAC0H,aAAa,GAAG1H,UAAU,CAAC2H,oBAAoB,GAAI,GAAG;MACtG;MAEA,IAAI3H,UAAU,CAACiI,gBAAgB,GAAG,CAAC,EAAE;QACnCjI,UAAU,CAACwJ,wBAAwB,GAAIxJ,UAAU,CAACgI,oBAAoB,GAAGhI,UAAU,CAACiI,gBAAgB,GAAI,GAAG;MAC7G;MAEA,IAAIjI,UAAU,CAACuI,kBAAkB,GAAG,CAAC,EAAE;QACrCvI,UAAU,CAACyJ,cAAc,GAAIzJ,UAAU,CAACwI,YAAY,GAAGxI,UAAU,CAACuI,kBAAkB,GAAI,GAAG;MAC7F;IACF;EAAC;IAAAhK,GAAA;IAAAC,KAAA;MAAA,IAAAkL,oBAAA,OAAAhL,kBAAA,CAAAX,OAAA,EAED,WAAkCyC,KAAqB,EAA6D;QAClH,IAAI;UAEF,IAAMmJ,SAAS,GAAG;YAChBzK,EAAE,EAAEsB,KAAK,CAACtB,EAAE;YACZ0K,OAAO,EAAEpJ,KAAK,CAAC7B,QAAQ,CAACuB,MAAM;YAC9B2J,aAAa,EAAErJ,KAAK,CAAC7B,QAAQ,CAACkI,YAAY;YAC1CiD,UAAU,EAAEtJ,KAAK,CAAC7B,QAAQ,CAACoI,SAAS,IAAI,UAAU;YAClDgD,YAAY,EAAEvJ,KAAK,CAAC7B,QAAQ,CAACoB,WAAW;YACxCiH,OAAO,EAAExG,KAAK,CAAC7B,QAAQ,CAACqI,OAAO;YAC/BgD,QAAQ,EAAExJ,KAAK,CAAC7B,QAAQ,CAACqL,QAAQ;YACjCC,UAAU,EAAEzJ,KAAK,CAAC7B,QAAQ,CAACuL,SAAS;YACpCC,kBAAkB,EAAE3J,KAAK,CAAC7B,QAAQ,CAACyL,OAAO;YAC1CC,WAAW,EAAE7J,KAAK,CAAC7B,QAAQ,CAAC0L,WAAW;YACvCC,UAAU,EAAE,IAAInL,IAAI,CAACqB,KAAK,CAAC7B,QAAQ,CAACgB,SAAS,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC2K,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC1EC,UAAU,EAAE,IAAIrL,IAAI,CAACqB,KAAK,CAAC7B,QAAQ,CAACgB,SAAS,CAAC,CAAC8K,YAAY,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC3EpK,MAAM,EAAEK,KAAK,CAACL,MAAM;YACpBuK,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACpK,KAAK,CAACX,KAAK,CAAC;YAC1CG,UAAU,EAAE2K,IAAI,CAACC,SAAS,CAACpK,KAAK,CAACR,UAAU,CAAC;YAC5C6K,UAAU,EAAErK,KAAK,CAACJ,SAAS;YAC3B0K,UAAU,EAAEtK,KAAK,CAACH;UACpB,CAAC;UAGD,IAAI0K,QAAQ,GAAG,CAAC;UAChB,IAAMC,WAAW,GAAG,CAAC;UAErB,OAAOD,QAAQ,GAAGC,WAAW,EAAE;YAC7B,IAAI;cAAA,IAAAC,YAAA;cACF,IAAM7D,MAAM,SAAS8D,gCAAe,CAACC,WAAW,CAACxB,SAAS,CAAC;cAE3D,IAAIvC,MAAM,CAAC3F,KAAK,EAAE;gBAChB,IAAIsJ,QAAQ,KAAKC,WAAW,GAAG,CAAC,EAAE;kBAChC,OAAO;oBAAExJ,OAAO,EAAE,KAAK;oBAAEC,KAAK,EAAE2F,MAAM,CAAC3F;kBAAM,CAAC;gBAChD;gBACAsJ,QAAQ,EAAE;gBACV,MAAM,IAAIK,OAAO,CAAC,UAAAC,OAAO;kBAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,GAAGN,QAAQ,CAAC;gBAAA,EAAC;gBAClE;cACF;cAEA,OAAO;gBAAEvJ,OAAO,EAAE,IAAI;gBAAEE,IAAI,EAAE;kBAAExC,EAAE,EAAEsB,KAAK,CAACtB,EAAE;kBAAEyC,UAAU,GAAAsJ,YAAA,GAAE7D,MAAM,CAAC1F,IAAI,qBAAXuJ,YAAA,CAAa/L;gBAAG;cAAE,CAAC;YAC/E,CAAC,CAAC,OAAOuC,KAAK,EAAE;cACdsJ,QAAQ,EAAE;cACV,IAAIA,QAAQ,KAAKC,WAAW,EAAE;gBAC5B,MAAMvJ,KAAK;cACb;cACA,MAAM,IAAI2J,OAAO,CAAC,UAAAC,OAAO;gBAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,GAAGN,QAAQ,CAAC;cAAA,EAAC;YACpE;UACF;UAEA,OAAO;YAAEvJ,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAE;UAAyC,CAAC;QAC5E,CAAC,CAAC,OAAOA,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;UACvD,OAAO;YAAED,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAE;UAA6B,CAAC;QAChE;MACF,CAAC;MAAA,SAvDaF,mBAAmBA,CAAAgK,GAAA;QAAA,OAAA7B,oBAAA,CAAArH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBf,mBAAmB;IAAA;EAAA;IAAAhD,GAAA;IAAAC,KAAA;MAAA,IAAAgN,sBAAA,OAAA9M,kBAAA,CAAAX,OAAA,EAyDjC,WAAoCyC,KAAqB,EAAiD;QACxG,IAAI;UACF,IAAI,CAACA,KAAK,CAACtB,EAAE,EAAE;YACb,OAAO;cAAEsC,OAAO,EAAE,KAAK;cAAEC,KAAK,EAAE;YAAkC,CAAC;UACrE;UAEA,IAAMgK,UAAU,GAAG;YACjBf,aAAa,EAAEC,IAAI,CAACC,SAAS,CAACpK,KAAK,CAACX,KAAK,CAAC;YAC1CG,UAAU,EAAE2K,IAAI,CAACC,SAAS,CAACpK,KAAK,CAACR,UAAU,CAAC;YAC5CG,MAAM,EAAEK,KAAK,CAACL,MAAM;YACpB2K,UAAU,EAAE,IAAI3L,IAAI,CAAC,CAAC,CAACS,WAAW,CAAC;UACrC,CAAC;UAGD,IAAIY,KAAK,CAACL,MAAM,KAAK,WAAW,IAAIK,KAAK,CAAC7B,QAAQ,CAAC+F,OAAO,EAAE;YAC1D+G,UAAU,CAACC,QAAQ,GAAG,IAAIvM,IAAI,CAACqB,KAAK,CAAC7B,QAAQ,CAAC+F,OAAO,CAAC,CAAC+F,YAAY,CAAC,CAAC,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACnFkB,UAAU,CAACE,gBAAgB,GAAGtM,IAAI,CAACwF,KAAK,CACtC,CAAC,IAAI1F,IAAI,CAACqB,KAAK,CAAC7B,QAAQ,CAAC+F,OAAO,CAAC,CAACkH,OAAO,CAAC,CAAC,GAAG,IAAIzM,IAAI,CAACqB,KAAK,CAAC7B,QAAQ,CAACgB,SAAS,CAAC,CAACiM,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,EAAE,CAC1G,CAAC;YACDH,UAAU,CAACI,WAAW,GAAG,IAAI,CAACC,wBAAwB,CAACtL,KAAK,CAACX,KAAK,CAAC;YACnE4L,UAAU,CAACrE,MAAM,GAAG,IAAI,CAAC2E,oBAAoB,CAACvL,KAAK,CAACX,KAAK,EAAEW,KAAK,CAAC7B,QAAQ,CAACuB,MAAM,CAAC;YACjFuL,UAAU,CAACO,QAAQ,GAAGxL,KAAK,CAACX,KAAK,CAACwH,OAAO;YACzCoE,UAAU,CAACQ,SAAS,GAAGzL,KAAK,CAACX,KAAK,CAACyH,QAAQ;UAC7C;UAEA,IAAMF,MAAM,SAAS8D,gCAAe,CAACgB,WAAW,CAAC1L,KAAK,CAACtB,EAAE,EAAEuM,UAAU,CAAC;UAEtE,IAAIrE,MAAM,CAAC3F,KAAK,EAAE;YAChB,OAAO;cAAED,OAAO,EAAE,KAAK;cAAEC,KAAK,EAAE2F,MAAM,CAAC3F;YAAM,CAAC;UAChD;UAEA,OAAO;YAAED,OAAO,EAAE;UAAK,CAAC;QAC1B,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UACzD,OAAO;YAAED,OAAO,EAAE,KAAK;YAAEC,KAAK,EAAE;UAA6B,CAAC;QAChE;MACF,CAAC;MAAA,SApCaqC,qBAAqBA,CAAAqI,GAAA;QAAA,OAAAX,sBAAA,CAAAnJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBwB,qBAAqB;IAAA;EAAA;IAAAvF,GAAA;IAAAC,KAAA,EAyCnC,SAAQsN,wBAAwBA,CAACjM,KAAiB,EAAU;MAC1D,IAAI,CAACA,KAAK,CAAC2D,IAAI,IAAI3D,KAAK,CAAC2D,IAAI,CAACd,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAO,KAAK;MACd;MAEA,OAAO7C,KAAK,CAAC2D,IAAI,CACd4I,GAAG,CAAC,UAAA/C,GAAG;QAAA,OAAI,GAAGA,GAAG,CAACJ,SAAS,IAAII,GAAG,CAACH,aAAa,EAAE;MAAA,EAAC,CACnDmD,IAAI,CAAC,IAAI,CAAC;IACf;EAAC;IAAA9N,GAAA;IAAAC,KAAA,EAKD,SAAQuN,oBAAoBA,CAAClM,KAAiB,EAAEK,MAAc,EAA2B;MACvF,IAAIL,KAAK,CAACwH,OAAO,GAAGxH,KAAK,CAACyH,QAAQ,EAAE;QAClC,OAAO,KAAK;MACd,CAAC,MAAM,IAAIzH,KAAK,CAACyH,QAAQ,GAAGzH,KAAK,CAACwH,OAAO,EAAE;QACzC,OAAO,MAAM;MACf;MACA,OAAO,MAAM;IACf;EAAC;IAAA9I,GAAA;IAAAC,KAAA,EAED,SAAQ+B,iBAAiBA,CAAA,EAAW;MAClC,OAAO,WAAWpB,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3E;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAED,SAAQuE,eAAeA,CAAA,EAAW;MAChC,OAAO,SAAS5D,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACzE;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAED,SAAQqD,sBAAsBA,CAAA,EAAS;MAAA,IAAAyK,KAAA;MACrC,IAAI,CAACrO,gBAAgB,CAACsO,OAAO,CAAC,UAAAlG,QAAQ;QAAA,OAAIA,QAAQ,CAACiG,KAAI,CAACtO,cAAc,CAAC;MAAA,EAAC;IAC1E;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAED,SAAQuF,oBAAoBA,CAAA,EAAS;MAAA,IAAAyI,MAAA;MACnC,IAAI,IAAI,CAACxO,cAAc,EAAE;QACvB,IAAI,CAACE,cAAc,CAACqO,OAAO,CAAC,UAAAlG,QAAQ;UAAA,OAAIA,QAAQ,CAACmG,MAAI,CAACxO,cAAc,CAAEwC,KAAK,CAACX,KAAK,CAAC;QAAA,EAAC;MACrF;IACF;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAKD,SAAQoD,gBAAgBA,CAAC2F,OAAe,EAAQ;MAC9C,IAAI;QAEF,IAAI,CAAC,IAAI,CAACpJ,gBAAgB,EAAE;UAC1B,IAAI,CAACA,gBAAgB,GAAG,IAAIsO,GAAG,CAAC,CAAC;QACnC;QAGA,IAAI,CAACtO,gBAAgB,CAACkL,GAAG,CAAC9B,OAAO,EAAE,EAAE,CAAC;QAGtC,IAAI,CAACmF,gBAAgB,CAACnF,OAAO,CAAC;MAChC,CAAC,CAAC,OAAO9F,KAAK,EAAE;QACdO,OAAO,CAACP,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACvD;IACF;EAAC;IAAAlD,GAAA;IAAAC,KAAA,EAKD,SAAQkO,gBAAgBA,CAACnF,OAAe,EAAQ;MAAA,IAAAoF,MAAA;MAE9C,IAAI,IAAI,CAACvO,YAAY,EAAE;QACrBwO,aAAa,CAAC,IAAI,CAACxO,YAAY,CAAC;MAClC;MAGA,IAAI,CAACA,YAAY,GAAGyO,WAAW,KAAAnO,kBAAA,CAAAX,OAAA,EAAC,aAAY;QAC1C,MAAM4O,MAAI,CAACG,eAAe,CAACvF,OAAO,CAAC;MACrC,CAAC,GAAE,KAAK,CAAC;IACX;EAAC;IAAAhJ,GAAA;IAAAC,KAAA;MAAA,IAAAuO,gBAAA,OAAArO,kBAAA,CAAAX,OAAA,EAKD,WAA8BwJ,OAAe,EAAiB;QAC5D,IAAI;UAAA,IAAAyF,qBAAA,EAAAC,sBAAA;UACF,IAAMC,KAAK,IAAAF,qBAAA,GAAG,IAAI,CAAC7O,gBAAgB,qBAArB6O,qBAAA,CAAuBG,GAAG,CAAC5F,OAAO,CAAC;UACjD,IAAI,CAAC2F,KAAK,IAAIA,KAAK,CAACxK,MAAM,KAAK,CAAC,EAAE;YAChC;UACF;UAGA,IAAM0K,OAAO,OAAAC,mBAAA,CAAAtP,OAAA,EAAOmP,KAAK,CAAC;UAC1B,CAAAD,sBAAA,OAAI,CAAC9O,gBAAgB,aAArB8O,sBAAA,CAAuB5D,GAAG,CAAC9B,OAAO,EAAE,EAAE,CAAC;UAEvC,KAAK,IAAM+F,MAAM,IAAIF,OAAO,EAAE;YAC5B,IAAI;cACF,MAAM,IAAI,CAACG,oBAAoB,CAACD,MAAM,CAAC;YACzC,CAAC,CAAC,OAAO7L,KAAK,EAAE;cAAA,IAAA+L,sBAAA;cACdxL,OAAO,CAACP,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;cAE9C,CAAA+L,sBAAA,OAAI,CAACrP,gBAAgB,cAAAqP,sBAAA,GAArBA,sBAAA,CAAuBL,GAAG,CAAC5F,OAAO,CAAC,aAAnCiG,sBAAA,CAAqClH,IAAI,CAACgH,MAAM,CAAC;YACnD;UACF;QACF,CAAC,CAAC,OAAO7L,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;QACtD;MACF,CAAC;MAAA,SAvBaqL,eAAeA,CAAAW,GAAA;QAAA,OAAAV,gBAAA,CAAA1K,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAfwK,eAAe;IAAA;EAAA;IAAAvO,GAAA;IAAAC,KAAA;MAAA,IAAAkP,qBAAA,OAAAhP,kBAAA,CAAAX,OAAA,EA4B7B,WAAmCuP,MAAW,EAAiB;QAC7D,QAAQA,MAAM,CAACK,IAAI;UACjB,KAAK,cAAc;YACjB,MAAM,IAAI,CAAC7J,qBAAqB,CAACwJ,MAAM,CAAC5L,IAAI,CAAC;YAC7C;UACF,KAAK,cAAc;YAEjB;UACF,KAAK,mBAAmB;YAEtB;UACF;YACEM,OAAO,CAAC4L,IAAI,CAAC,sBAAsB,EAAEN,MAAM,CAACK,IAAI,CAAC;QACrD;MACF,CAAC;MAAA,SAdaJ,oBAAoBA,CAAAM,GAAA;QAAA,OAAAH,qBAAA,CAAArL,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBiL,oBAAoB;IAAA;EAAA;IAAAhP,GAAA;IAAAC,KAAA,EAmBlC,SAAQsD,aAAaA,CAAA,EAAS;MAAA,IAAAgM,MAAA;MAE5B,IAAI,IAAI,CAACzP,gBAAgB,EAAE;QACzBuO,aAAa,CAAC,IAAI,CAACvO,gBAAgB,CAAC;MACtC;MAGA,IAAI,CAACA,gBAAgB,GAAGwO,WAAW,KAAAnO,kBAAA,CAAAX,OAAA,EAAC,aAAY;QAC9C,IAAI+P,MAAI,CAAC9P,cAAc,EAAE;UACvB,IAAI;YACF,MAAM8P,MAAI,CAAChK,qBAAqB,CAACgK,MAAI,CAAC9P,cAAc,CAACwC,KAAK,CAAC;UAC7D,CAAC,CAAC,OAAOiB,KAAK,EAAE;YACdO,OAAO,CAACP,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;UAC3C;QACF;MACF,CAAC,GAAE,MAAM,CAAC;IACZ;EAAC;IAAAlD,GAAA;IAAAC,KAAA;MAAA,IAAAuP,qBAAA,OAAArP,kBAAA,CAAAX,OAAA,EAKD,aAAoD;QAClD,IAAI;UACF,IAAI,IAAI,CAACC,cAAc,EAAE;YAEvB,IAAI,IAAI,CAACA,cAAc,CAAC+C,oBAAoB,EAAE;cAC5C,MAAMI,4CAAqB,CAAC4D,aAAa,CAAC,CAAC;YAC7C;YAGA,IAAI,IAAI,CAAC1G,gBAAgB,EAAE;cACzBuO,aAAa,CAAC,IAAI,CAACvO,gBAAgB,CAAC;cACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;YAC9B;YAEA,IAAI,IAAI,CAACD,YAAY,EAAE;cACrBwO,aAAa,CAAC,IAAI,CAACxO,YAAY,CAAC;cAChC,IAAI,CAACA,YAAY,GAAG,IAAI;YAC1B;YAGA,IAAI,CAACJ,cAAc,GAAG,IAAI;UAC5B;QACF,CAAC,CAAC,OAAOyD,KAAK,EAAE;UACdO,OAAO,CAACP,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC;MAAA,SAzBaQ,oBAAoBA,CAAA;QAAA,OAAA8L,qBAAA,CAAA1L,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBL,oBAAoB;IAAA;EAAA;AAAA;AA6B7B,IAAM+L,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,GAAG,IAAInQ,qBAAqB,CAAC,CAAC", "ignoreList": []}