f9b91d9e4ab93a0557726f216b7a446b
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_15w3oqugc4() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\computerVision.ts";
  var hash = "9d114b99f683471d238bb61102b43b262acbbc04";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\computerVision.ts",
    statementMap: {
      "0": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 47,
          column: 41
        }
      },
      "1": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 84,
          column: 5
        }
      },
      "2": {
        start: {
          line: 62,
          column: 6
        },
        end: {
          line: 62,
          column: 49
        }
      },
      "3": {
        start: {
          line: 70,
          column: 25
        },
        end: {
          line: 70,
          column: 50
        }
      },
      "4": {
        start: {
          line: 71,
          column: 23
        },
        end: {
          line: 71,
          column: 56
        }
      },
      "5": {
        start: {
          line: 72,
          column: 24
        },
        end: {
          line: 72,
          column: 57
        }
      },
      "6": {
        start: {
          line: 73,
          column: 25
        },
        end: {
          line: 73,
          column: 68
        }
      },
      "7": {
        start: {
          line: 75,
          column: 6
        },
        end: {
          line: 80,
          column: 8
        }
      },
      "8": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 52
        }
      },
      "9": {
        start: {
          line: 83,
          column: 6
        },
        end: {
          line: 83,
          column: 40
        }
      },
      "10": {
        start: {
          line: 92,
          column: 23
        },
        end: {
          line: 92,
          column: 70
        }
      },
      "11": {
        start: {
          line: 92,
          column: 44
        },
        end: {
          line: 92,
          column: 69
        }
      },
      "12": {
        start: {
          line: 93,
          column: 22
        },
        end: {
          line: 93,
          column: 68
        }
      },
      "13": {
        start: {
          line: 93,
          column: 43
        },
        end: {
          line: 93,
          column: 67
        }
      },
      "14": {
        start: {
          line: 94,
          column: 26
        },
        end: {
          line: 94,
          column: 76
        }
      },
      "15": {
        start: {
          line: 94,
          column: 47
        },
        end: {
          line: 94,
          column: 75
        }
      },
      "16": {
        start: {
          line: 95,
          column: 25
        },
        end: {
          line: 95,
          column: 74
        }
      },
      "17": {
        start: {
          line: 95,
          column: 46
        },
        end: {
          line: 95,
          column: 73
        }
      },
      "18": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 99,
          column: 5
        }
      },
      "19": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 98,
          column: 24
        }
      },
      "20": {
        start: {
          line: 102,
          column: 25
        },
        end: {
          line: 102,
          column: 65
        }
      },
      "21": {
        start: {
          line: 103,
          column: 25
        },
        end: {
          line: 103,
          column: 67
        }
      },
      "22": {
        start: {
          line: 104,
          column: 22
        },
        end: {
          line: 104,
          column: 34
        }
      },
      "23": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "24": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 21
        }
      },
      "25": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 114,
          column: 5
        }
      },
      "26": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 113,
          column: 24
        }
      },
      "27": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 119,
          column: 5
        }
      },
      "28": {
        start: {
          line: 118,
          column: 6
        },
        end: {
          line: 118,
          column: 22
        }
      },
      "29": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 126,
          column: 5
        }
      },
      "30": {
        start: {
          line: 123,
          column: 6
        },
        end: {
          line: 123,
          column: 24
        }
      },
      "31": {
        start: {
          line: 125,
          column: 6
        },
        end: {
          line: 125,
          column: 24
        }
      },
      "32": {
        start: {
          line: 133,
          column: 22
        },
        end: {
          line: 133,
          column: 68
        }
      },
      "33": {
        start: {
          line: 133,
          column: 43
        },
        end: {
          line: 133,
          column: 67
        }
      },
      "34": {
        start: {
          line: 134,
          column: 21
        },
        end: {
          line: 134,
          column: 66
        }
      },
      "35": {
        start: {
          line: 134,
          column: 42
        },
        end: {
          line: 134,
          column: 65
        }
      },
      "36": {
        start: {
          line: 135,
          column: 21
        },
        end: {
          line: 135,
          column: 66
        }
      },
      "37": {
        start: {
          line: 135,
          column: 42
        },
        end: {
          line: 135,
          column: 65
        }
      },
      "38": {
        start: {
          line: 136,
          column: 20
        },
        end: {
          line: 136,
          column: 64
        }
      },
      "39": {
        start: {
          line: 136,
          column: 41
        },
        end: {
          line: 136,
          column: 63
        }
      },
      "40": {
        start: {
          line: 137,
          column: 26
        },
        end: {
          line: 137,
          column: 76
        }
      },
      "41": {
        start: {
          line: 137,
          column: 47
        },
        end: {
          line: 137,
          column: 75
        }
      },
      "42": {
        start: {
          line: 138,
          column: 25
        },
        end: {
          line: 138,
          column: 74
        }
      },
      "43": {
        start: {
          line: 138,
          column: 46
        },
        end: {
          line: 138,
          column: 73
        }
      },
      "44": {
        start: {
          line: 141,
          column: 21
        },
        end: {
          line: 141,
          column: 83
        }
      },
      "45": {
        start: {
          line: 142,
          column: 29
        },
        end: {
          line: 142,
          column: 88
        }
      },
      "46": {
        start: {
          line: 143,
          column: 24
        },
        end: {
          line: 143,
          column: 68
        }
      },
      "47": {
        start: {
          line: 144,
          column: 26
        },
        end: {
          line: 144,
          column: 74
        }
      },
      "48": {
        start: {
          line: 145,
          column: 25
        },
        end: {
          line: 145,
          column: 72
        }
      },
      "49": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 153,
          column: 6
        }
      },
      "50": {
        start: {
          line: 160,
          column: 30
        },
        end: {
          line: 166,
          column: 9
        }
      },
      "51": {
        start: {
          line: 168,
          column: 24
        },
        end: {
          line: 172,
          column: 9
        }
      },
      "52": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 69
        }
      },
      "53": {
        start: {
          line: 181,
          column: 41
        },
        end: {
          line: 181,
          column: 43
        }
      },
      "54": {
        start: {
          line: 183,
          column: 4
        },
        end: {
          line: 191,
          column: 5
        }
      },
      "55": {
        start: {
          line: 183,
          column: 17
        },
        end: {
          line: 183,
          column: 18
        }
      },
      "56": {
        start: {
          line: 184,
          column: 24
        },
        end: {
          line: 184,
          column: 37
        }
      },
      "57": {
        start: {
          line: 185,
          column: 27
        },
        end: {
          line: 185,
          column: 36
        }
      },
      "58": {
        start: {
          line: 187,
          column: 23
        },
        end: {
          line: 187,
          column: 91
        }
      },
      "59": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 190,
          column: 7
        }
      },
      "60": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 33
        }
      },
      "61": {
        start: {
          line: 193,
          column: 4
        },
        end: {
          line: 193,
          column: 21
        }
      },
      "62": {
        start: {
          line: 204,
          column: 32
        },
        end: {
          line: 204,
          column: 34
        }
      },
      "63": {
        start: {
          line: 205,
          column: 35
        },
        end: {
          line: 205,
          column: 37
        }
      },
      "64": {
        start: {
          line: 206,
          column: 38
        },
        end: {
          line: 206,
          column: 40
        }
      },
      "65": {
        start: {
          line: 209,
          column: 28
        },
        end: {
          line: 209,
          column: 98
        }
      },
      "66": {
        start: {
          line: 209,
          column: 56
        },
        end: {
          line: 209,
          column: 76
        }
      },
      "67": {
        start: {
          line: 211,
          column: 4
        },
        end: {
          line: 216,
          column: 5
        }
      },
      "68": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 52
        }
      },
      "69": {
        start: {
          line: 213,
          column: 11
        },
        end: {
          line: 216,
          column: 5
        }
      },
      "70": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 58
        }
      },
      "71": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 67
        }
      },
      "72": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 219,
          column: 103
        }
      },
      "73": {
        start: {
          line: 219,
          column: 52
        },
        end: {
          line: 219,
          column: 81
        }
      },
      "74": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 223,
          column: 5
        }
      },
      "75": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 221,
          column: 60
        }
      },
      "76": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 75
        }
      },
      "77": {
        start: {
          line: 225,
          column: 29
        },
        end: {
          line: 225,
          column: 113
        }
      },
      "78": {
        start: {
          line: 225,
          column: 57
        },
        end: {
          line: 225,
          column: 91
        }
      },
      "79": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "80": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 49
        }
      },
      "81": {
        start: {
          line: 231,
          column: 28
        },
        end: {
          line: 231,
          column: 95
        }
      },
      "82": {
        start: {
          line: 231,
          column: 57
        },
        end: {
          line: 231,
          column: 72
        }
      },
      "83": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 235,
          column: 5
        }
      },
      "84": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 233,
          column: 66
        }
      },
      "85": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 75
        }
      },
      "86": {
        start: {
          line: 237,
          column: 4
        },
        end: {
          line: 237,
          column: 56
        }
      },
      "87": {
        start: {
          line: 243,
          column: 34
        },
        end: {
          line: 243,
          column: 36
        }
      },
      "88": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "89": {
        start: {
          line: 245,
          column: 17
        },
        end: {
          line: 245,
          column: 18
        }
      },
      "90": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "91": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 253,
          column: 18
        }
      },
      "92": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 272,
          column: 6
        }
      },
      "93": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 288,
          column: 8
        }
      },
      "94": {
        start: {
          line: 276,
          column: 32
        },
        end: {
          line: 288,
          column: 5
        }
      },
      "95": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 296,
          column: 6
        }
      },
      "96": {
        start: {
          line: 300,
          column: 4
        },
        end: {
          line: 304,
          column: 6
        }
      },
      "97": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 309,
          column: 35
        }
      },
      "98": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 313,
          column: 35
        }
      },
      "99": {
        start: {
          line: 317,
          column: 4
        },
        end: {
          line: 317,
          column: 35
        }
      },
      "100": {
        start: {
          line: 321,
          column: 4
        },
        end: {
          line: 321,
          column: 35
        }
      },
      "101": {
        start: {
          line: 325,
          column: 4
        },
        end: {
          line: 325,
          column: 35
        }
      },
      "102": {
        start: {
          line: 330,
          column: 4
        },
        end: {
          line: 335,
          column: 6
        }
      },
      "103": {
        start: {
          line: 339,
          column: 4
        },
        end: {
          line: 348,
          column: 6
        }
      },
      "104": {
        start: {
          line: 352,
          column: 37
        },
        end: {
          line: 352,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 46,
            column: 2
          },
          end: {
            line: 46,
            column: 3
          }
        },
        loc: {
          start: {
            line: 46,
            column: 38
          },
          end: {
            line: 48,
            column: 3
          }
        },
        line: 46
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 54,
            column: 2
          },
          end: {
            line: 54,
            column: 3
          }
        },
        loc: {
          start: {
            line: 59,
            column: 5
          },
          end: {
            line: 85,
            column: 3
          }
        },
        line: 59
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        },
        loc: {
          start: {
            line: 90,
            column: 103
          },
          end: {
            line: 127,
            column: 3
          }
        },
        line: 90
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 92,
            column: 38
          },
          end: {
            line: 92,
            column: 39
          }
        },
        loc: {
          start: {
            line: 92,
            column: 44
          },
          end: {
            line: 92,
            column: 69
          }
        },
        line: 92
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 93,
            column: 37
          },
          end: {
            line: 93,
            column: 38
          }
        },
        loc: {
          start: {
            line: 93,
            column: 43
          },
          end: {
            line: 93,
            column: 67
          }
        },
        line: 93
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 94,
            column: 41
          },
          end: {
            line: 94,
            column: 42
          }
        },
        loc: {
          start: {
            line: 94,
            column: 47
          },
          end: {
            line: 94,
            column: 75
          }
        },
        line: 94
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 95,
            column: 40
          },
          end: {
            line: 95,
            column: 41
          }
        },
        loc: {
          start: {
            line: 95,
            column: 46
          },
          end: {
            line: 95,
            column: 73
          }
        },
        line: 95
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 132,
            column: 2
          },
          end: {
            line: 132,
            column: 3
          }
        },
        loc: {
          start: {
            line: 132,
            column: 102
          },
          end: {
            line: 154,
            column: 3
          }
        },
        line: 132
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 133,
            column: 37
          },
          end: {
            line: 133,
            column: 38
          }
        },
        loc: {
          start: {
            line: 133,
            column: 43
          },
          end: {
            line: 133,
            column: 67
          }
        },
        line: 133
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 134,
            column: 36
          },
          end: {
            line: 134,
            column: 37
          }
        },
        loc: {
          start: {
            line: 134,
            column: 42
          },
          end: {
            line: 134,
            column: 65
          }
        },
        line: 134
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 135,
            column: 36
          },
          end: {
            line: 135,
            column: 37
          }
        },
        loc: {
          start: {
            line: 135,
            column: 42
          },
          end: {
            line: 135,
            column: 65
          }
        },
        line: 135
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 136,
            column: 35
          },
          end: {
            line: 136,
            column: 36
          }
        },
        loc: {
          start: {
            line: 136,
            column: 41
          },
          end: {
            line: 136,
            column: 63
          }
        },
        line: 136
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 137,
            column: 41
          },
          end: {
            line: 137,
            column: 42
          }
        },
        loc: {
          start: {
            line: 137,
            column: 47
          },
          end: {
            line: 137,
            column: 75
          }
        },
        line: 137
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 138,
            column: 40
          },
          end: {
            line: 138,
            column: 41
          }
        },
        loc: {
          start: {
            line: 138,
            column: 46
          },
          end: {
            line: 138,
            column: 73
          }
        },
        line: 138
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 159,
            column: 2
          },
          end: {
            line: 159,
            column: 3
          }
        },
        loc: {
          start: {
            line: 159,
            column: 111
          },
          end: {
            line: 175,
            column: 3
          }
        },
        line: 159
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 180,
            column: 2
          },
          end: {
            line: 180,
            column: 3
          }
        },
        loc: {
          start: {
            line: 180,
            column: 60
          },
          end: {
            line: 194,
            column: 3
          }
        },
        line: 180
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 199,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        },
        loc: {
          start: {
            line: 203,
            column: 4
          },
          end: {
            line: 238,
            column: 3
          }
        },
        line: 203
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 209,
            column: 44
          },
          end: {
            line: 209,
            column: 45
          }
        },
        loc: {
          start: {
            line: 209,
            column: 56
          },
          end: {
            line: 209,
            column: 76
          }
        },
        line: 209
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 219,
            column: 40
          },
          end: {
            line: 219,
            column: 41
          }
        },
        loc: {
          start: {
            line: 219,
            column: 52
          },
          end: {
            line: 219,
            column: 81
          }
        },
        line: 219
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 225,
            column: 45
          },
          end: {
            line: 225,
            column: 46
          }
        },
        loc: {
          start: {
            line: 225,
            column: 57
          },
          end: {
            line: 225,
            column: 91
          }
        },
        line: 225
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 231,
            column: 45
          },
          end: {
            line: 231,
            column: 46
          }
        },
        loc: {
          start: {
            line: 231,
            column: 57
          },
          end: {
            line: 231,
            column: 72
          }
        },
        line: 231
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 242,
            column: 2
          },
          end: {
            line: 242,
            column: 3
          }
        },
        loc: {
          start: {
            line: 242,
            column: 46
          },
          end: {
            line: 254,
            column: 3
          }
        },
        line: 242
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 256,
            column: 2
          },
          end: {
            line: 256,
            column: 3
          }
        },
        loc: {
          start: {
            line: 256,
            column: 50
          },
          end: {
            line: 273,
            column: 3
          }
        },
        line: 256
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 275,
            column: 2
          },
          end: {
            line: 275,
            column: 3
          }
        },
        loc: {
          start: {
            line: 275,
            column: 71
          },
          end: {
            line: 289,
            column: 3
          }
        },
        line: 275
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 276,
            column: 22
          },
          end: {
            line: 276,
            column: 23
          }
        },
        loc: {
          start: {
            line: 276,
            column: 32
          },
          end: {
            line: 288,
            column: 5
          }
        },
        line: 276
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 291,
            column: 2
          },
          end: {
            line: 291,
            column: 3
          }
        },
        loc: {
          start: {
            line: 291,
            column: 69
          },
          end: {
            line: 297,
            column: 3
          }
        },
        line: 291
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 299,
            column: 2
          },
          end: {
            line: 299,
            column: 3
          }
        },
        loc: {
          start: {
            line: 299,
            column: 81
          },
          end: {
            line: 305,
            column: 3
          }
        },
        line: 299
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 307,
            column: 2
          },
          end: {
            line: 307,
            column: 3
          }
        },
        loc: {
          start: {
            line: 307,
            column: 136
          },
          end: {
            line: 310,
            column: 3
          }
        },
        line: 307
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 312,
            column: 2
          },
          end: {
            line: 312,
            column: 3
          }
        },
        loc: {
          start: {
            line: 312,
            column: 103
          },
          end: {
            line: 314,
            column: 3
          }
        },
        line: 312
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 316,
            column: 2
          },
          end: {
            line: 316,
            column: 3
          }
        },
        loc: {
          start: {
            line: 316,
            column: 88
          },
          end: {
            line: 318,
            column: 3
          }
        },
        line: 316
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 320,
            column: 2
          },
          end: {
            line: 320,
            column: 3
          }
        },
        loc: {
          start: {
            line: 320,
            column: 86
          },
          end: {
            line: 322,
            column: 3
          }
        },
        line: 320
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 324,
            column: 2
          },
          end: {
            line: 324,
            column: 3
          }
        },
        loc: {
          start: {
            line: 324,
            column: 85
          },
          end: {
            line: 326,
            column: 3
          }
        },
        line: 324
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 328,
            column: 2
          },
          end: {
            line: 328,
            column: 3
          }
        },
        loc: {
          start: {
            line: 328,
            column: 118
          },
          end: {
            line: 336,
            column: 3
          }
        },
        line: 328
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 338,
            column: 2
          },
          end: {
            line: 338,
            column: 3
          }
        },
        loc: {
          start: {
            line: 338,
            column: 32
          },
          end: {
            line: 349,
            column: 3
          }
        },
        line: 338
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 97,
            column: 4
          },
          end: {
            line: 99,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 4
          },
          end: {
            line: 99,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "1": {
        loc: {
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 97,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 97,
            column: 8
          },
          end: {
            line: 97,
            column: 19
          }
        }, {
          start: {
            line: 97,
            column: 23
          },
          end: {
            line: 97,
            column: 33
          }
        }, {
          start: {
            line: 97,
            column: 37
          },
          end: {
            line: 97,
            column: 51
          }
        }, {
          start: {
            line: 97,
            column: 55
          },
          end: {
            line: 97,
            column: 68
          }
        }],
        line: 97
      },
      "2": {
        loc: {
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 109,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 109,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "3": {
        loc: {
          start: {
            line: 112,
            column: 4
          },
          end: {
            line: 114,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 4
          },
          end: {
            line: 114,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "4": {
        loc: {
          start: {
            line: 117,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 4
          },
          end: {
            line: 119,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "5": {
        loc: {
          start: {
            line: 122,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 4
          },
          end: {
            line: 126,
            column: 5
          }
        }, {
          start: {
            line: 124,
            column: 11
          },
          end: {
            line: 126,
            column: 5
          }
        }],
        line: 122
      },
      "6": {
        loc: {
          start: {
            line: 188,
            column: 6
          },
          end: {
            line: 190,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 188,
            column: 6
          },
          end: {
            line: 190,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 188
      },
      "7": {
        loc: {
          start: {
            line: 211,
            column: 4
          },
          end: {
            line: 216,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 4
          },
          end: {
            line: 216,
            column: 5
          }
        }, {
          start: {
            line: 213,
            column: 11
          },
          end: {
            line: 216,
            column: 5
          }
        }],
        line: 211
      },
      "8": {
        loc: {
          start: {
            line: 213,
            column: 11
          },
          end: {
            line: 216,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 11
          },
          end: {
            line: 216,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "9": {
        loc: {
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 223,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 223,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "10": {
        loc: {
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 228,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 228,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "11": {
        loc: {
          start: {
            line: 232,
            column: 4
          },
          end: {
            line: 235,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 4
          },
          end: {
            line: 235,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 232
      },
      "12": {
        loc: {
          start: {
            line: 249,
            column: 18
          },
          end: {
            line: 249,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 27
          },
          end: {
            line: 249,
            column: 37
          }
        }, {
          start: {
            line: 249,
            column: 40
          },
          end: {
            line: 249,
            column: 78
          }
        }],
        line: 249
      },
      "13": {
        loc: {
          start: {
            line: 249,
            column: 40
          },
          end: {
            line: 249,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 49
          },
          end: {
            line: 249,
            column: 59
          }
        }, {
          start: {
            line: 249,
            column: 62
          },
          end: {
            line: 249,
            column: 78
          }
        }],
        line: 249
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0, 0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9d114b99f683471d238bb61102b43b262acbbc04"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_15w3oqugc4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_15w3oqugc4();
var ComputerVisionService = function () {
  function ComputerVisionService() {
    _classCallCheck(this, ComputerVisionService);
  }
  return _createClass(ComputerVisionService, [{
    key: "isWebEnvironment",
    value: function isWebEnvironment() {
      cov_15w3oqugc4().f[0]++;
      cov_15w3oqugc4().s[0]++;
      return typeof window !== 'undefined';
    }
  }, {
    key: "analyzeVideoFrames",
    value: (function () {
      var _analyzeVideoFrames = _asyncToGenerator(function* (videoUrl) {
        cov_15w3oqugc4().f[1]++;
        cov_15w3oqugc4().s[1]++;
        try {
          cov_15w3oqugc4().s[2]++;
          console.log('Processing video:', videoUrl);
          var mockFrames = (cov_15w3oqugc4().s[3]++, this.generateMockFrames());
          var analysis = (cov_15w3oqugc4().s[4]++, this.analyzeTechnique(mockFrames));
          var movements = (cov_15w3oqugc4().s[5]++, this.analyzeMovements(mockFrames));
          var highlights = (cov_15w3oqugc4().s[6]++, this.detectHighlights(mockFrames, analysis));
          cov_15w3oqugc4().s[7]++;
          return {
            frames: mockFrames,
            analysis: analysis,
            movements: movements,
            highlights: highlights
          };
        } catch (error) {
          cov_15w3oqugc4().s[8]++;
          console.error('Video analysis error:', error);
          cov_15w3oqugc4().s[9]++;
          return this.getFallbackAnalysis();
        }
      });
      function analyzeVideoFrames(_x) {
        return _analyzeVideoFrames.apply(this, arguments);
      }
      return analyzeVideoFrames;
    }())
  }, {
    key: "detectShotType",
    value: function detectShotType(keypoints) {
      cov_15w3oqugc4().f[2]++;
      var rightWrist = (cov_15w3oqugc4().s[10]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[3]++;
        cov_15w3oqugc4().s[11]++;
        return kp.name === 'right_wrist';
      }));
      var leftWrist = (cov_15w3oqugc4().s[12]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[4]++;
        cov_15w3oqugc4().s[13]++;
        return kp.name === 'left_wrist';
      }));
      var rightShoulder = (cov_15w3oqugc4().s[14]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[5]++;
        cov_15w3oqugc4().s[15]++;
        return kp.name === 'right_shoulder';
      }));
      var leftShoulder = (cov_15w3oqugc4().s[16]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[6]++;
        cov_15w3oqugc4().s[17]++;
        return kp.name === 'left_shoulder';
      }));
      cov_15w3oqugc4().s[18]++;
      if ((cov_15w3oqugc4().b[1][0]++, !rightWrist) || (cov_15w3oqugc4().b[1][1]++, !leftWrist) || (cov_15w3oqugc4().b[1][2]++, !rightShoulder) || (cov_15w3oqugc4().b[1][3]++, !leftShoulder)) {
        cov_15w3oqugc4().b[0][0]++;
        cov_15w3oqugc4().s[19]++;
        return 'forehand';
      } else {
        cov_15w3oqugc4().b[0][1]++;
      }
      var armExtension = (cov_15w3oqugc4().s[20]++, Math.abs(rightWrist.x - rightShoulder.x));
      var bodyRotation = (cov_15w3oqugc4().s[21]++, Math.abs(rightShoulder.x - leftShoulder.x));
      var armHeight = (cov_15w3oqugc4().s[22]++, rightWrist.y);
      cov_15w3oqugc4().s[23]++;
      if (armHeight < rightShoulder.y - 50) {
        cov_15w3oqugc4().b[2][0]++;
        cov_15w3oqugc4().s[24]++;
        return 'serve';
      } else {
        cov_15w3oqugc4().b[2][1]++;
      }
      cov_15w3oqugc4().s[25]++;
      if (armHeight < rightShoulder.y - 100) {
        cov_15w3oqugc4().b[3][0]++;
        cov_15w3oqugc4().s[26]++;
        return 'overhead';
      } else {
        cov_15w3oqugc4().b[3][1]++;
      }
      cov_15w3oqugc4().s[27]++;
      if (armExtension < 30) {
        cov_15w3oqugc4().b[4][0]++;
        cov_15w3oqugc4().s[28]++;
        return 'volley';
      } else {
        cov_15w3oqugc4().b[4][1]++;
      }
      cov_15w3oqugc4().s[29]++;
      if (rightWrist.x > rightShoulder.x + 20) {
        cov_15w3oqugc4().b[5][0]++;
        cov_15w3oqugc4().s[30]++;
        return 'forehand';
      } else {
        cov_15w3oqugc4().b[5][1]++;
        cov_15w3oqugc4().s[31]++;
        return 'backhand';
      }
    }
  }, {
    key: "analyzeBiomechanics",
    value: function analyzeBiomechanics(keypoints, shotType) {
      cov_15w3oqugc4().f[7]++;
      var rightKnee = (cov_15w3oqugc4().s[32]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[8]++;
        cov_15w3oqugc4().s[33]++;
        return kp.name === 'right_knee';
      }));
      var leftKnee = (cov_15w3oqugc4().s[34]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[9]++;
        cov_15w3oqugc4().s[35]++;
        return kp.name === 'left_knee';
      }));
      var rightHip = (cov_15w3oqugc4().s[36]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[10]++;
        cov_15w3oqugc4().s[37]++;
        return kp.name === 'right_hip';
      }));
      var leftHip = (cov_15w3oqugc4().s[38]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[11]++;
        cov_15w3oqugc4().s[39]++;
        return kp.name === 'left_hip';
      }));
      var rightShoulder = (cov_15w3oqugc4().s[40]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[12]++;
        cov_15w3oqugc4().s[41]++;
        return kp.name === 'right_shoulder';
      }));
      var leftShoulder = (cov_15w3oqugc4().s[42]++, keypoints.find(function (kp) {
        cov_15w3oqugc4().f[13]++;
        cov_15w3oqugc4().s[43]++;
        return kp.name === 'left_shoulder';
      }));
      var kneeBend = (cov_15w3oqugc4().s[44]++, this.calculateKneeBend(rightKnee, leftKnee, rightHip, leftHip));
      var shoulderRotation = (cov_15w3oqugc4().s[45]++, this.calculateShoulderRotation(rightShoulder, leftShoulder));
      var hipRotation = (cov_15w3oqugc4().s[46]++, this.calculateHipRotation(rightHip, leftHip));
      var followThrough = (cov_15w3oqugc4().s[47]++, this.calculateFollowThrough(keypoints, shotType));
      var contactPoint = (cov_15w3oqugc4().s[48]++, this.calculateContactPoint(keypoints, shotType));
      cov_15w3oqugc4().s[49]++;
      return {
        kneeBend: kneeBend,
        shoulderRotation: shoulderRotation,
        hipRotation: hipRotation,
        followThrough: followThrough,
        contactPoint: contactPoint
      };
    }
  }, {
    key: "scoreTechnique",
    value: function scoreTechnique(biomechanics, timing) {
      cov_15w3oqugc4().f[14]++;
      var biomechanicsScore = (cov_15w3oqugc4().s[50]++, (biomechanics.kneeBend + biomechanics.shoulderRotation + biomechanics.hipRotation + biomechanics.followThrough + biomechanics.contactPoint) / 5);
      var timingScore = (cov_15w3oqugc4().s[51]++, (timing.preparation + timing.contact + timing.recovery) / 3);
      cov_15w3oqugc4().s[52]++;
      return Math.round(biomechanicsScore * 0.7 + timingScore * 0.3);
    }
  }, {
    key: "analyzeFootwork",
    value: function analyzeFootwork(frames) {
      cov_15w3oqugc4().f[15]++;
      var movements = (cov_15w3oqugc4().s[53]++, []);
      cov_15w3oqugc4().s[54]++;
      for (var i = (cov_15w3oqugc4().s[55]++, 1); i < frames.length; i++) {
        var prevFrame = (cov_15w3oqugc4().s[56]++, frames[i - 1]);
        var currentFrame = (cov_15w3oqugc4().s[57]++, frames[i]);
        var movement = (cov_15w3oqugc4().s[58]++, this.detectMovementType(prevFrame.keypoints, currentFrame.keypoints));
        cov_15w3oqugc4().s[59]++;
        if (movement) {
          cov_15w3oqugc4().b[6][0]++;
          cov_15w3oqugc4().s[60]++;
          movements.push(movement);
        } else {
          cov_15w3oqugc4().b[6][1]++;
        }
      }
      cov_15w3oqugc4().s[61]++;
      return movements;
    }
  }, {
    key: "generateInsights",
    value: function generateInsights(analysis, movements) {
      cov_15w3oqugc4().f[16]++;
      var strengths = (cov_15w3oqugc4().s[62]++, []);
      var improvements = (cov_15w3oqugc4().s[63]++, []);
      var recommendations = (cov_15w3oqugc4().s[64]++, []);
      var avgBiomechanics = (cov_15w3oqugc4().s[65]++, analysis.reduce(function (sum, a) {
        cov_15w3oqugc4().f[17]++;
        cov_15w3oqugc4().s[66]++;
        return sum + a.overallScore;
      }, 0) / analysis.length);
      cov_15w3oqugc4().s[67]++;
      if (avgBiomechanics > 80) {
        cov_15w3oqugc4().b[7][0]++;
        cov_15w3oqugc4().s[68]++;
        strengths.push('Excellent overall technique');
      } else {
        cov_15w3oqugc4().b[7][1]++;
        cov_15w3oqugc4().s[69]++;
        if (avgBiomechanics < 60) {
          cov_15w3oqugc4().b[8][0]++;
          cov_15w3oqugc4().s[70]++;
          improvements.push('Focus on fundamental technique');
          cov_15w3oqugc4().s[71]++;
          recommendations.push('Work with a coach on basic mechanics');
        } else {
          cov_15w3oqugc4().b[8][1]++;
        }
      }
      var avgKneeBend = (cov_15w3oqugc4().s[72]++, analysis.reduce(function (sum, a) {
        cov_15w3oqugc4().f[18]++;
        cov_15w3oqugc4().s[73]++;
        return sum + a.biomechanics.kneeBend;
      }, 0) / analysis.length);
      cov_15w3oqugc4().s[74]++;
      if (avgKneeBend < 60) {
        cov_15w3oqugc4().b[9][0]++;
        cov_15w3oqugc4().s[75]++;
        improvements.push('Improve knee bend for more power');
        cov_15w3oqugc4().s[76]++;
        recommendations.push('Practice shadow swings with deeper knee bend');
      } else {
        cov_15w3oqugc4().b[9][1]++;
      }
      var avgFollowThrough = (cov_15w3oqugc4().s[77]++, analysis.reduce(function (sum, a) {
        cov_15w3oqugc4().f[19]++;
        cov_15w3oqugc4().s[78]++;
        return sum + a.biomechanics.followThrough;
      }, 0) / analysis.length);
      cov_15w3oqugc4().s[79]++;
      if (avgFollowThrough > 80) {
        cov_15w3oqugc4().b[10][0]++;
        cov_15w3oqugc4().s[80]++;
        strengths.push('Excellent follow-through');
      } else {
        cov_15w3oqugc4().b[10][1]++;
      }
      var footworkQuality = (cov_15w3oqugc4().s[81]++, movements.reduce(function (sum, m) {
        cov_15w3oqugc4().f[20]++;
        cov_15w3oqugc4().s[82]++;
        return sum + m.quality;
      }, 0) / movements.length);
      cov_15w3oqugc4().s[83]++;
      if (footworkQuality < 70) {
        cov_15w3oqugc4().b[11][0]++;
        cov_15w3oqugc4().s[84]++;
        improvements.push('Work on footwork and court positioning');
        cov_15w3oqugc4().s[85]++;
        recommendations.push('Practice ladder drills and split-step timing');
      } else {
        cov_15w3oqugc4().b[11][1]++;
      }
      cov_15w3oqugc4().s[86]++;
      return {
        strengths: strengths,
        improvements: improvements,
        recommendations: recommendations
      };
    }
  }, {
    key: "generateMockFrames",
    value: function generateMockFrames() {
      cov_15w3oqugc4().f[21]++;
      var frames = (cov_15w3oqugc4().s[87]++, []);
      cov_15w3oqugc4().s[88]++;
      for (var i = (cov_15w3oqugc4().s[89]++, 0); i < 30; i++) {
        cov_15w3oqugc4().s[90]++;
        frames.push({
          timestamp: i * 100,
          keypoints: this.generateMockKeypoints(),
          shotType: i < 10 ? (cov_15w3oqugc4().b[12][0]++, 'forehand') : (cov_15w3oqugc4().b[12][1]++, i < 20 ? (cov_15w3oqugc4().b[13][0]++, 'backhand') : (cov_15w3oqugc4().b[13][1]++, 'serve'))
        });
      }
      cov_15w3oqugc4().s[91]++;
      return frames;
    }
  }, {
    key: "generateMockKeypoints",
    value: function generateMockKeypoints() {
      cov_15w3oqugc4().f[22]++;
      cov_15w3oqugc4().s[92]++;
      return [{
        x: 320,
        y: 100,
        confidence: 0.9,
        name: 'nose'
      }, {
        x: 300,
        y: 150,
        confidence: 0.8,
        name: 'left_shoulder'
      }, {
        x: 340,
        y: 150,
        confidence: 0.8,
        name: 'right_shoulder'
      }, {
        x: 280,
        y: 200,
        confidence: 0.7,
        name: 'left_elbow'
      }, {
        x: 360,
        y: 200,
        confidence: 0.7,
        name: 'right_elbow'
      }, {
        x: 260,
        y: 250,
        confidence: 0.6,
        name: 'left_wrist'
      }, {
        x: 380,
        y: 250,
        confidence: 0.6,
        name: 'right_wrist'
      }, {
        x: 310,
        y: 300,
        confidence: 0.8,
        name: 'left_hip'
      }, {
        x: 330,
        y: 300,
        confidence: 0.8,
        name: 'right_hip'
      }, {
        x: 300,
        y: 400,
        confidence: 0.7,
        name: 'left_knee'
      }, {
        x: 340,
        y: 400,
        confidence: 0.7,
        name: 'right_knee'
      }, {
        x: 290,
        y: 500,
        confidence: 0.6,
        name: 'left_ankle'
      }, {
        x: 350,
        y: 500,
        confidence: 0.6,
        name: 'right_ankle'
      }];
    }
  }, {
    key: "analyzeTechnique",
    value: function analyzeTechnique(frames) {
      var _this = this;
      cov_15w3oqugc4().f[23]++;
      cov_15w3oqugc4().s[93]++;
      return frames.map(function (frame) {
        cov_15w3oqugc4().f[24]++;
        cov_15w3oqugc4().s[94]++;
        return {
          shotType: _this.detectShotType(frame.keypoints),
          overallScore: 78 + Math.random() * 20,
          biomechanics: _this.analyzeBiomechanics(frame.keypoints, 'forehand'),
          timing: {
            preparation: 75 + Math.random() * 20,
            contact: 80 + Math.random() * 15,
            recovery: 70 + Math.random() * 25
          },
          consistency: 75,
          powerGeneration: 80,
          accuracy: 85
        };
      });
    }
  }, {
    key: "analyzeMovements",
    value: function analyzeMovements(frames) {
      cov_15w3oqugc4().f[25]++;
      cov_15w3oqugc4().s[95]++;
      return [{
        type: 'lateral',
        quality: 80,
        timing: 85,
        efficiency: 75
      }, {
        type: 'split_step',
        quality: 90,
        timing: 95,
        efficiency: 88
      }, {
        type: 'forward',
        quality: 75,
        timing: 70,
        efficiency: 80
      }];
    }
  }, {
    key: "detectHighlights",
    value: function detectHighlights(frames, analysis) {
      cov_15w3oqugc4().f[26]++;
      cov_15w3oqugc4().s[96]++;
      return [{
        timestamp: 1200,
        type: 'excellent_contact',
        description: 'Perfect contact point on forehand'
      }, {
        timestamp: 2500,
        type: 'good_footwork',
        description: 'Excellent split-step timing'
      }, {
        timestamp: 4100,
        type: 'improvement_needed',
        description: 'Late preparation on backhand'
      }];
    }
  }, {
    key: "calculateKneeBend",
    value: function calculateKneeBend(rightKnee, leftKnee, rightHip, leftHip) {
      cov_15w3oqugc4().f[27]++;
      cov_15w3oqugc4().s[97]++;
      return 75 + Math.random() * 20;
    }
  }, {
    key: "calculateShoulderRotation",
    value: function calculateShoulderRotation(rightShoulder, leftShoulder) {
      cov_15w3oqugc4().f[28]++;
      cov_15w3oqugc4().s[98]++;
      return 80 + Math.random() * 15;
    }
  }, {
    key: "calculateHipRotation",
    value: function calculateHipRotation(rightHip, leftHip) {
      cov_15w3oqugc4().f[29]++;
      cov_15w3oqugc4().s[99]++;
      return 78 + Math.random() * 18;
    }
  }, {
    key: "calculateFollowThrough",
    value: function calculateFollowThrough(keypoints, shotType) {
      cov_15w3oqugc4().f[30]++;
      cov_15w3oqugc4().s[100]++;
      return 82 + Math.random() * 15;
    }
  }, {
    key: "calculateContactPoint",
    value: function calculateContactPoint(keypoints, shotType) {
      cov_15w3oqugc4().f[31]++;
      cov_15w3oqugc4().s[101]++;
      return 85 + Math.random() * 12;
    }
  }, {
    key: "detectMovementType",
    value: function detectMovementType(prevKeypoints, currentKeypoints) {
      cov_15w3oqugc4().f[32]++;
      cov_15w3oqugc4().s[102]++;
      return {
        type: 'lateral',
        quality: 80,
        timing: 85,
        efficiency: 75
      };
    }
  }, {
    key: "getFallbackAnalysis",
    value: function getFallbackAnalysis() {
      cov_15w3oqugc4().f[33]++;
      cov_15w3oqugc4().s[103]++;
      return {
        frames: this.generateMockFrames(),
        analysis: this.analyzeTechnique(this.generateMockFrames()),
        movements: [{
          type: 'lateral',
          quality: 75,
          timing: 80,
          efficiency: 70
        }],
        highlights: [{
          timestamp: 1000,
          type: 'good_technique',
          description: 'Solid overall form'
        }]
      };
    }
  }]);
}();
export var computerVisionService = (cov_15w3oqugc4().s[104]++, new ComputerVisionService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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