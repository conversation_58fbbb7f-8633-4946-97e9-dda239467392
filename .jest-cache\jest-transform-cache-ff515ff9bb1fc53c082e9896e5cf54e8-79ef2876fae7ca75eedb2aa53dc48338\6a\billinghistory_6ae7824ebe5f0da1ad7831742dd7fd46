d6816a680e6bedfaaec9a3b231738f44
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1g4k49t9sd() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\payment\\billing-history.tsx";
  var hash = "99ec463cf85b129eafc33560254697abfc0a7444";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\payment\\billing-history.tsx",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 15
        },
        end: {
          line: 17,
          column: 1
        }
      },
      "1": {
        start: {
          line: 29,
          column: 27
        },
        end: {
          line: 54,
          column: 4
        }
      },
      "2": {
        start: {
          line: 56,
          column: 32
        },
        end: {
          line: 59,
          column: 3
        }
      },
      "3": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 55
        }
      },
      "4": {
        start: {
          line: 61,
          column: 25
        },
        end: {
          line: 72,
          column: 3
        }
      },
      "5": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 71,
          column: 5
        }
      },
      "6": {
        start: {
          line: 64,
          column: 8
        },
        end: {
          line: 64,
          column: 28
        }
      },
      "7": {
        start: {
          line: 66,
          column: 8
        },
        end: {
          line: 66,
          column: 30
        }
      },
      "8": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 68,
          column: 25
        }
      },
      "9": {
        start: {
          line: 70,
          column: 8
        },
        end: {
          line: 70,
          column: 27
        }
      },
      "10": {
        start: {
          line: 74,
          column: 21
        },
        end: {
          line: 81,
          column: 3
        }
      },
      "11": {
        start: {
          line: 75,
          column: 17
        },
        end: {
          line: 75,
          column: 37
        }
      },
      "12": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 80,
          column: 7
        }
      },
      "13": {
        start: {
          line: 83,
          column: 2
        },
        end: {
          line: 162,
          column: 4
        }
      },
      "14": {
        start: {
          line: 88,
          column: 25
        },
        end: {
          line: 88,
          column: 38
        }
      },
      "15": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 147,
          column: 19
        }
      },
      "16": {
        start: {
          line: 141,
          column: 33
        },
        end: {
          line: 141,
          column: 62
        }
      },
      "17": {
        start: {
          line: 165,
          column: 15
        },
        end: {
          line: 311,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "BillingHistoryScreen",
        decl: {
          start: {
            line: 28,
            column: 24
          },
          end: {
            line: 28,
            column: 44
          }
        },
        loc: {
          start: {
            line: 28,
            column: 47
          },
          end: {
            line: 163,
            column: 1
          }
        },
        line: 28
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 56,
            column: 32
          },
          end: {
            line: 56,
            column: 33
          }
        },
        loc: {
          start: {
            line: 56,
            column: 59
          },
          end: {
            line: 59,
            column: 3
          }
        },
        line: 56
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 61,
            column: 25
          },
          end: {
            line: 61,
            column: 26
          }
        },
        loc: {
          start: {
            line: 61,
            column: 45
          },
          end: {
            line: 72,
            column: 3
          }
        },
        line: 61
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 74,
            column: 21
          },
          end: {
            line: 74,
            column: 22
          }
        },
        loc: {
          start: {
            line: 74,
            column: 45
          },
          end: {
            line: 81,
            column: 3
          }
        },
        line: 74
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 88,
            column: 19
          },
          end: {
            line: 88,
            column: 20
          }
        },
        loc: {
          start: {
            line: 88,
            column: 25
          },
          end: {
            line: 88,
            column: 38
          }
        },
        line: 88
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 111,
            column: 30
          },
          end: {
            line: 111,
            column: 31
          }
        },
        loc: {
          start: {
            line: 112,
            column: 12
          },
          end: {
            line: 147,
            column: 19
          }
        },
        line: 112
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 141,
            column: 27
          },
          end: {
            line: 141,
            column: 28
          }
        },
        loc: {
          start: {
            line: 141,
            column: 33
          },
          end: {
            line: 141,
            column: 62
          }
        },
        line: 141
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 62,
            column: 4
          },
          end: {
            line: 71,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 63,
            column: 6
          },
          end: {
            line: 64,
            column: 28
          }
        }, {
          start: {
            line: 65,
            column: 6
          },
          end: {
            line: 66,
            column: 30
          }
        }, {
          start: {
            line: 67,
            column: 6
          },
          end: {
            line: 68,
            column: 25
          }
        }, {
          start: {
            line: 69,
            column: 6
          },
          end: {
            line: 70,
            column: 27
          }
        }],
        line: 62
      },
      "1": {
        loc: {
          start: {
            line: 138,
            column: 15
          },
          end: {
            line: 146,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 15
          },
          end: {
            line: 138,
            column: 32
          }
        }, {
          start: {
            line: 138,
            column: 36
          },
          end: {
            line: 138,
            column: 60
          }
        }, {
          start: {
            line: 139,
            column: 16
          },
          end: {
            line: 145,
            column: 35
          }
        }],
        line: 138
      },
      "2": {
        loc: {
          start: {
            line: 150,
            column: 11
          },
          end: {
            line: 158,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 150,
            column: 11
          },
          end: {
            line: 150,
            column: 38
          }
        }, {
          start: {
            line: 151,
            column: 12
          },
          end: {
            line: 157,
            column: 19
          }
        }],
        line: 150
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0, 0, 0],
      "1": [0, 0, 0],
      "2": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "99ec463cf85b129eafc33560254697abfc0a7444"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1g4k49t9sd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1g4k49t9sd();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { ArrowLeft, Download, Calendar, DollarSign } from 'lucide-react-native';
import Button from "../../components/ui/Button";
import Card from "../../components/ui/Card";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_1g4k49t9sd().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  green: '#10b981'
});
export default function BillingHistoryScreen() {
  cov_1g4k49t9sd().f[0]++;
  var _ref = (cov_1g4k49t9sd().s[1]++, useState([{
      id: '1',
      date: '2024-01-15',
      amount: '$9.99',
      status: 'paid',
      description: 'Premium Plan - Monthly',
      invoiceUrl: 'https://example.com/invoice/1'
    }, {
      id: '2',
      date: '2023-12-15',
      amount: '$9.99',
      status: 'paid',
      description: 'Premium Plan - Monthly',
      invoiceUrl: 'https://example.com/invoice/2'
    }, {
      id: '3',
      date: '2023-11-15',
      amount: '$9.99',
      status: 'paid',
      description: 'Premium Plan - Monthly',
      invoiceUrl: 'https://example.com/invoice/3'
    }])),
    _ref2 = _slicedToArray(_ref, 1),
    billingHistory = _ref2[0];
  cov_1g4k49t9sd().s[2]++;
  var handleDownloadInvoice = function handleDownloadInvoice(record) {
    cov_1g4k49t9sd().f[1]++;
    cov_1g4k49t9sd().s[3]++;
    console.log('Downloading invoice for:', record.id);
  };
  cov_1g4k49t9sd().s[4]++;
  var getStatusColor = function getStatusColor(status) {
    cov_1g4k49t9sd().f[2]++;
    cov_1g4k49t9sd().s[5]++;
    switch (status) {
      case 'paid':
        cov_1g4k49t9sd().b[0][0]++;
        cov_1g4k49t9sd().s[6]++;
        return colors.green;
      case 'pending':
        cov_1g4k49t9sd().b[0][1]++;
        cov_1g4k49t9sd().s[7]++;
        return colors.primary;
      case 'failed':
        cov_1g4k49t9sd().b[0][2]++;
        cov_1g4k49t9sd().s[8]++;
        return '#ef4444';
      default:
        cov_1g4k49t9sd().b[0][3]++;
        cov_1g4k49t9sd().s[9]++;
        return colors.gray;
    }
  };
  cov_1g4k49t9sd().s[10]++;
  var formatDate = function formatDate(dateString) {
    cov_1g4k49t9sd().f[3]++;
    var date = (cov_1g4k49t9sd().s[11]++, new Date(dateString));
    cov_1g4k49t9sd().s[12]++;
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  cov_1g4k49t9sd().s[13]++;
  return _jsxs(SafeAreaView, {
    style: styles.container,
    children: [_jsxs(View, {
      style: styles.header,
      children: [_jsx(Button, {
        title: "",
        onPress: function onPress() {
          cov_1g4k49t9sd().f[4]++;
          cov_1g4k49t9sd().s[14]++;
          return router.back();
        },
        variant: "ghost",
        style: styles.backButton,
        children: _jsx(ArrowLeft, {
          size: 24,
          color: colors.dark
        })
      }), _jsx(Text, {
        style: styles.headerTitle,
        children: "Billing History"
      }), _jsx(View, {
        style: styles.placeholder
      })]
    }), _jsxs(ScrollView, {
      style: styles.content,
      children: [_jsxs(Card, {
        style: styles.summaryCard,
        children: [_jsxs(View, {
          style: styles.summaryHeader,
          children: [_jsx(DollarSign, {
            size: 24,
            color: colors.primary
          }), _jsx(Text, {
            style: styles.summaryTitle,
            children: "Total Spent"
          })]
        }), _jsx(Text, {
          style: styles.summaryAmount,
          children: "$29.97"
        }), _jsx(Text, {
          style: styles.summaryPeriod,
          children: "Last 3 months"
        })]
      }), _jsxs(View, {
        style: styles.historySection,
        children: [_jsx(Text, {
          style: styles.sectionTitle,
          children: "Payment History"
        }), billingHistory.map(function (record) {
          cov_1g4k49t9sd().f[5]++;
          cov_1g4k49t9sd().s[15]++;
          return _jsxs(Card, {
            style: styles.recordCard,
            children: [_jsxs(View, {
              style: styles.recordHeader,
              children: [_jsxs(View, {
                style: styles.recordInfo,
                children: [_jsx(Text, {
                  style: styles.recordDescription,
                  children: record.description
                }), _jsxs(View, {
                  style: styles.recordMeta,
                  children: [_jsx(Calendar, {
                    size: 14,
                    color: colors.gray
                  }), _jsx(Text, {
                    style: styles.recordDate,
                    children: formatDate(record.date)
                  })]
                })]
              }), _jsxs(View, {
                style: styles.recordRight,
                children: [_jsx(Text, {
                  style: styles.recordAmount,
                  children: record.amount
                }), _jsx(View, {
                  style: [styles.statusBadge, {
                    backgroundColor: getStatusColor(record.status)
                  }],
                  children: _jsx(Text, {
                    style: styles.statusText,
                    children: record.status.toUpperCase()
                  })
                })]
              })]
            }), (cov_1g4k49t9sd().b[1][0]++, record.invoiceUrl) && (cov_1g4k49t9sd().b[1][1]++, record.status === 'paid') && (cov_1g4k49t9sd().b[1][2]++, _jsxs(TouchableOpacity, {
              style: styles.downloadButton,
              onPress: function onPress() {
                cov_1g4k49t9sd().f[6]++;
                cov_1g4k49t9sd().s[16]++;
                return handleDownloadInvoice(record);
              },
              children: [_jsx(Download, {
                size: 16,
                color: colors.primary
              }), _jsx(Text, {
                style: styles.downloadText,
                children: "Download Invoice"
              })]
            }))]
          }, record.id);
        }), (cov_1g4k49t9sd().b[2][0]++, billingHistory.length === 0) && (cov_1g4k49t9sd().b[2][1]++, _jsxs(View, {
          style: styles.emptyState,
          children: [_jsx(Calendar, {
            size: 48,
            color: colors.gray
          }), _jsx(Text, {
            style: styles.emptyTitle,
            children: "No Billing History"
          }), _jsx(Text, {
            style: styles.emptyDescription,
            children: "Your payment history will appear here"
          })]
        }))]
      })]
    })]
  });
}
var styles = (cov_1g4k49t9sd().s[17]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  backButton: {
    width: 40,
    height: 40
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  placeholder: {
    width: 40
  },
  content: {
    flex: 1,
    padding: 20
  },
  summaryCard: {
    marginBottom: 24,
    padding: 20,
    alignItems: 'center'
  },
  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16
  },
  summaryTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginLeft: 8
  },
  summaryAmount: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: colors.primary,
    marginBottom: 4
  },
  summaryPeriod: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  historySection: {
    flex: 1
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 16
  },
  recordCard: {
    marginBottom: 12,
    padding: 16
  },
  recordHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12
  },
  recordInfo: {
    flex: 1
  },
  recordDescription: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 4
  },
  recordMeta: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  recordDate: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginLeft: 4
  },
  recordRight: {
    alignItems: 'flex-end'
  },
  recordAmount: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: colors.dark,
    marginBottom: 4
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8
  },
  statusText: {
    fontSize: 10,
    fontFamily: 'Inter-SemiBold',
    color: colors.white
  },
  downloadButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray
  },
  downloadText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.primary,
    marginLeft: 4
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginTop: 16,
    marginBottom: 8
  },
  emptyDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    textAlign: 'center'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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