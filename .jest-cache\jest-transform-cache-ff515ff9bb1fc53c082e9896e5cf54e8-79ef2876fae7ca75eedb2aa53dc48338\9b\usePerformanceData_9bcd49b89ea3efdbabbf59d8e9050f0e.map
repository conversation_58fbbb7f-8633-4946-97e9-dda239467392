{"version": 3, "names": ["useState", "useCallback", "useMemo", "useRef", "useAuth", "supabase", "useAdvancedCache", "usePerformanceData", "options", "arguments", "length", "undefined", "cov_1bpwe9i7vk", "b", "f", "_ref", "s", "_ref$enableCache", "enableCache", "_ref$cacheTimeout", "cacheTimeout", "_ref$autoRefresh", "autoRefresh", "_ref$refreshInterval", "refreshInterval", "_ref2", "user", "_ref3", "_ref4", "_slicedToArray", "loading", "setLoading", "_ref5", "_ref6", "error", "setError", "lastFetchRef", "cache<PERSON>ey", "id", "_ref7", "fetchPerformanceDataFromAPI", "ttl", "dependencies", "enabled", "cachedData", "data", "cacheLoading", "cacheError", "fetchCachedData", "fetchData", "invalidateCache", "invalidate", "refreshCache", "refresh", "_asyncToGenerator", "Error", "startTime", "Date", "now", "_ref9", "from", "select", "eq", "order", "ascending", "limit", "foreignTable", "single", "userData", "userError", "message", "fetchTime", "console", "warn", "result", "matches", "match_results", "sessions", "training_sessions", "skillStats", "skill_stats", "lastFetched", "userId", "current", "err", "errorMessage", "isStale", "age", "force", "refreshData", "React", "useEffect", "interval", "setInterval", "clearInterval", "usePerformanceMetrics", "dataSource", "_ref10", "recentMatches", "slice", "recentSessions", "latestSkillStats", "winRate", "filter", "m", "averageSessionScore", "reduce", "sum", "overall_score", "skillTrend", "previous", "skills", "trends", "skill", "currentValue", "previousValue", "change", "trend", "totalMatches", "totalSessions", "lastUpdated", "usePerformanceDataSelector", "selector", "deps", "_ref11", "concat", "_toConsumableArray"], "sources": ["usePerformanceData.ts"], "sourcesContent": ["/**\n * Optimized Performance Data Hook\n * \n * Focused hook for fetching and caching performance data\n * with memoization and selective re-rendering.\n */\n\nimport { useState, useCallback, useMemo, useRef } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { supabase } from '@/lib/supabase';\nimport { useAdvancedCache } from '@/hooks/usePerformanceOptimization';\n\nexport interface PerformanceDataCache {\n  matches: any[];\n  sessions: any[];\n  skillStats: any[];\n  lastFetched: number;\n  userId: string;\n}\n\ninterface UsePerformanceDataOptions {\n  enableCache?: boolean;\n  cacheTimeout?: number;\n  autoRefresh?: boolean;\n  refreshInterval?: number;\n}\n\ninterface UsePerformanceDataReturn {\n  data: PerformanceDataCache | null;\n  loading: boolean;\n  error: string | null;\n  fetchData: (force?: boolean) => Promise<void>;\n  refreshData: () => Promise<void>;\n  invalidateCache: () => void;\n  isStale: boolean;\n}\n\n/**\n * Optimized hook for performance data fetching with intelligent caching\n */\nexport function usePerformanceData(options: UsePerformanceDataOptions = {}): UsePerformanceDataReturn {\n  const {\n    enableCache = true,\n    cacheTimeout = 300000, // 5 minutes\n    autoRefresh = false,\n    refreshInterval = 600000, // 10 minutes\n  } = options;\n\n  const { user } = useAuth();\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const lastFetchRef = useRef<number>(0);\n\n  // Memoized cache key based on user ID\n  const cacheKey = useMemo(() => \n    user ? `performance_data_${user.id}` : null, \n    [user?.id]\n  );\n\n  // Advanced caching with automatic invalidation\n  const {\n    data: cachedData,\n    loading: cacheLoading,\n    error: cacheError,\n    fetchData: fetchCachedData,\n    invalidate: invalidateCache,\n    refresh: refreshCache,\n  } = useAdvancedCache<PerformanceDataCache>(\n    cacheKey || 'performance_data_anonymous',\n    () => fetchPerformanceDataFromAPI(),\n    {\n      ttl: cacheTimeout,\n      dependencies: [user?.id],\n      enabled: enableCache && !!user,\n    }\n  );\n\n  // Memoized data fetcher with optimized queries\n  const fetchPerformanceDataFromAPI = useCallback(async (): Promise<PerformanceDataCache> => {\n    if (!user) {\n      throw new Error('User not authenticated');\n    }\n\n    const startTime = Date.now();\n\n    try {\n      // Single optimized query with joins instead of multiple queries\n      const { data: userData, error: userError } = await supabase\n        .from('users')\n        .select(`\n          id,\n          skill_level,\n          match_results!inner (\n            id,\n            opponent_name,\n            result,\n            match_score,\n            created_at,\n            match_stats,\n            duration_minutes\n          ),\n          training_sessions!inner (\n            id,\n            title,\n            session_type,\n            overall_score,\n            created_at,\n            duration_minutes,\n            improvement_areas\n          ),\n          skill_stats!inner (\n            forehand,\n            backhand,\n            serve,\n            volley,\n            footwork,\n            strategy,\n            mental_game,\n            updated_at\n          )\n        `)\n        .eq('id', user.id)\n        .order('match_results(created_at)', { ascending: false })\n        .order('training_sessions(created_at)', { ascending: false })\n        .order('skill_stats(updated_at)', { ascending: false })\n        .limit(10, { foreignTable: 'match_results' })\n        .limit(20, { foreignTable: 'training_sessions' })\n        .limit(5, { foreignTable: 'skill_stats' })\n        .single();\n\n      if (userError) {\n        throw new Error(`Failed to fetch performance data: ${userError.message}`);\n      }\n\n      const fetchTime = Date.now() - startTime;\n      \n      // Log slow queries for optimization\n      if (fetchTime > 2000) {\n        console.warn(`Slow performance data query: ${fetchTime}ms`);\n      }\n\n      const result: PerformanceDataCache = {\n        matches: userData?.match_results || [],\n        sessions: userData?.training_sessions || [],\n        skillStats: userData?.skill_stats || [],\n        lastFetched: Date.now(),\n        userId: user.id,\n      };\n\n      lastFetchRef.current = Date.now();\n      return result;\n\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch performance data';\n      console.error('Performance data fetch error:', err);\n      throw new Error(errorMessage);\n    }\n  }, [user?.id]);\n\n  // Memoized stale data checker\n  const isStale = useMemo(() => {\n    if (!cachedData) return true;\n    const age = Date.now() - cachedData.lastFetched;\n    return age > cacheTimeout;\n  }, [cachedData?.lastFetched, cacheTimeout]);\n\n  // Optimized fetch function with loading state management\n  const fetchData = useCallback(async (force = false) => {\n    if (!user) return;\n\n    // Avoid duplicate requests\n    if (loading && !force) return;\n\n    try {\n      setLoading(true);\n      setError(null);\n      \n      await fetchCachedData(force);\n      \n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch data';\n      setError(errorMessage);\n    } finally {\n      setLoading(false);\n    }\n  }, [user, loading, fetchCachedData]);\n\n  // Optimized refresh function\n  const refreshData = useCallback(async () => {\n    await fetchData(true);\n  }, [fetchData]);\n\n  // Auto-refresh logic with cleanup\n  React.useEffect(() => {\n    if (!autoRefresh || !user) return;\n\n    const interval = setInterval(() => {\n      if (isStale) {\n        fetchData(false); // Use cache if available\n      }\n    }, refreshInterval);\n\n    return () => clearInterval(interval);\n  }, [autoRefresh, user, isStale, fetchData, refreshInterval]);\n\n  // Memoized return value to prevent unnecessary re-renders\n  return useMemo(() => ({\n    data: cachedData,\n    loading: loading || cacheLoading,\n    error: error || cacheError?.message || null,\n    fetchData,\n    refreshData,\n    invalidateCache,\n    isStale,\n  }), [\n    cachedData,\n    loading,\n    cacheLoading,\n    error,\n    cacheError,\n    fetchData,\n    refreshData,\n    invalidateCache,\n    isStale,\n  ]);\n}\n\n/**\n * Optimized hook for specific performance metrics with selective updates\n */\nexport function usePerformanceMetrics(dataSource?: PerformanceDataCache) {\n  return useMemo(() => {\n    if (!dataSource) return null;\n\n    const { matches, sessions, skillStats } = dataSource;\n\n    // Memoized calculations to prevent recalculation on every render\n    const recentMatches = matches.slice(0, 5);\n    const recentSessions = sessions.slice(0, 10);\n    const latestSkillStats = skillStats[0];\n\n    const winRate = matches.length > 0 \n      ? (matches.filter(m => m.result === 'win').length / matches.length) * 100 \n      : 0;\n\n    const averageSessionScore = sessions.length > 0\n      ? sessions.reduce((sum, s) => sum + (s.overall_score || 0), 0) / sessions.length\n      : 0;\n\n    const skillTrend = skillStats.length >= 2 ? (() => {\n      const current = skillStats[0];\n      const previous = skillStats[1];\n      const skills = ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game'];\n      \n      return skills.reduce((trends, skill) => {\n        const currentValue = current?.[skill] || 0;\n        const previousValue = previous?.[skill] || 0;\n        const change = currentValue - previousValue;\n        \n        trends[skill] = {\n          current: currentValue,\n          previous: previousValue,\n          change,\n          trend: change > 2 ? 'improving' : change < -2 ? 'declining' : 'stable',\n        };\n        \n        return trends;\n      }, {} as Record<string, any>);\n    })() : {};\n\n    return {\n      recentMatches,\n      recentSessions,\n      latestSkillStats,\n      winRate,\n      averageSessionScore,\n      skillTrend,\n      totalMatches: matches.length,\n      totalSessions: sessions.length,\n      lastUpdated: dataSource.lastFetched,\n    };\n  }, [dataSource]);\n}\n\n/**\n * Performance data selector hook for component-specific data\n */\nexport function usePerformanceDataSelector<T>(\n  selector: (data: PerformanceDataCache | null) => T,\n  deps: React.DependencyList = []\n) {\n  const { data } = usePerformanceData();\n  \n  return useMemo(() => selector(data), [data, ...deps]);\n}\n\nexport default {\n  usePerformanceData,\n  usePerformanceMetrics,\n  usePerformanceDataSelector,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AAC9D,SAASC,OAAO;AAChB,SAASC,QAAQ;AACjB,SAASC,gBAAgB;AA8BzB,OAAO,SAASC,kBAAkBA,CAAA,EAAoE;EAAA,IAAnEC,OAAkC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;EAAAD,cAAA,GAAAE,CAAA;EACxE,IAAAC,IAAA,IAAAH,cAAA,GAAAI,CAAA,OAKIR,OAAO;IAAAS,gBAAA,GAAAF,IAAA,CAJTG,WAAW;IAAXA,WAAW,GAAAD,gBAAA,eAAAL,cAAA,GAAAC,CAAA,UAAG,IAAI,IAAAI,gBAAA;IAAAE,iBAAA,GAAAJ,IAAA,CAClBK,YAAY;IAAZA,YAAY,GAAAD,iBAAA,eAAAP,cAAA,GAAAC,CAAA,UAAG,MAAM,IAAAM,iBAAA;IAAAE,gBAAA,GAAAN,IAAA,CACrBO,WAAW;IAAXA,WAAW,GAAAD,gBAAA,eAAAT,cAAA,GAAAC,CAAA,UAAG,KAAK,IAAAQ,gBAAA;IAAAE,oBAAA,GAAAR,IAAA,CACnBS,eAAe;IAAfA,eAAe,GAAAD,oBAAA,eAAAX,cAAA,GAAAC,CAAA,UAAG,MAAM,IAAAU,oBAAA;EAG1B,IAAAE,KAAA,IAAAb,cAAA,GAAAI,CAAA,OAAiBZ,OAAO,CAAC,CAAC;IAAlBsB,IAAI,GAAAD,KAAA,CAAJC,IAAI;EACZ,IAAAC,KAAA,IAAAf,cAAA,GAAAI,CAAA,OAA8BhB,QAAQ,CAAC,KAAK,CAAC;IAAA4B,KAAA,GAAAC,cAAA,CAAAF,KAAA;IAAtCG,OAAO,GAAAF,KAAA;IAAEG,UAAU,GAAAH,KAAA;EAC1B,IAAAI,KAAA,IAAApB,cAAA,GAAAI,CAAA,OAA0BhB,QAAQ,CAAgB,IAAI,CAAC;IAAAiC,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAAhDE,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EACtB,IAAMG,YAAY,IAAAxB,cAAA,GAAAI,CAAA,OAAGb,MAAM,CAAS,CAAC,CAAC;EAGtC,IAAMkC,QAAQ,IAAAzB,cAAA,GAAAI,CAAA,OAAGd,OAAO,CAAC,YACvB;IAAAU,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAAA,OAAAU,IAAI,IAAAd,cAAA,GAAAC,CAAA,UAAG,oBAAoBa,IAAI,CAACY,EAAE,EAAE,KAAA1B,cAAA,GAAAC,CAAA,UAAG,IAAI;EAAD,CAAC,EAC3C,CAACa,IAAI,oBAAJA,IAAI,CAAEY,EAAE,CACX,CAAC;EAGD,IAAAC,KAAA,IAAA3B,cAAA,GAAAI,CAAA,OAOIV,gBAAgB,CAClB,CAAAM,cAAA,GAAAC,CAAA,UAAAwB,QAAQ,MAAAzB,cAAA,GAAAC,CAAA,UAAI,4BAA4B,GACxC,YAAM;MAAAD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAwB,2BAA2B,CAAC,CAAC;IAAD,CAAC,EACnC;MACEC,GAAG,EAAErB,YAAY;MACjBsB,YAAY,EAAE,CAAChB,IAAI,oBAAJA,IAAI,CAAEY,EAAE,CAAC;MACxBK,OAAO,EAAE,CAAA/B,cAAA,GAAAC,CAAA,UAAAK,WAAW,MAAAN,cAAA,GAAAC,CAAA,UAAI,CAAC,CAACa,IAAI;IAChC,CACF,CAAC;IAdOkB,UAAU,GAAAL,KAAA,CAAhBM,IAAI;IACKC,YAAY,GAAAP,KAAA,CAArBT,OAAO;IACAiB,UAAU,GAAAR,KAAA,CAAjBL,KAAK;IACMc,eAAe,GAAAT,KAAA,CAA1BU,SAAS;IACGC,eAAe,GAAAX,KAAA,CAA3BY,UAAU;IACDC,YAAY,GAAAb,KAAA,CAArBc,OAAO;EAYT,IAAMb,2BAA2B,IAAA5B,cAAA,GAAAI,CAAA,OAAGf,WAAW,CAAAqD,iBAAA,CAAC,aAA2C;IAAA1C,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACzF,IAAI,CAACU,IAAI,EAAE;MAAAd,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MACT,MAAM,IAAIuC,KAAK,CAAC,wBAAwB,CAAC;IAC3C,CAAC;MAAA3C,cAAA,GAAAC,CAAA;IAAA;IAED,IAAM2C,SAAS,IAAA5C,cAAA,GAAAI,CAAA,QAAGyC,IAAI,CAACC,GAAG,CAAC,CAAC;IAAC9C,cAAA,GAAAI,CAAA;IAE7B,IAAI;MAEF,IAAA2C,KAAA,IAAA/C,cAAA,GAAAI,CAAA,cAAmDX,QAAQ,CACxDuD,IAAI,CAAC,OAAO,CAAC,CACbC,MAAM,CAAC;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,CAAC,CACDC,EAAE,CAAC,IAAI,EAAEpC,IAAI,CAACY,EAAE,CAAC,CACjByB,KAAK,CAAC,2BAA2B,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC,CACxDD,KAAK,CAAC,+BAA+B,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC,CAC5DD,KAAK,CAAC,yBAAyB,EAAE;UAAEC,SAAS,EAAE;QAAM,CAAC,CAAC,CACtDC,KAAK,CAAC,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAgB,CAAC,CAAC,CAC5CD,KAAK,CAAC,EAAE,EAAE;UAAEC,YAAY,EAAE;QAAoB,CAAC,CAAC,CAChDD,KAAK,CAAC,CAAC,EAAE;UAAEC,YAAY,EAAE;QAAc,CAAC,CAAC,CACzCC,MAAM,CAAC,CAAC;QAzCGC,QAAQ,GAAAT,KAAA,CAAdd,IAAI;QAAmBwB,SAAS,GAAAV,KAAA,CAAhBzB,KAAK;MAyCjBtB,cAAA,GAAAI,CAAA;MAEZ,IAAIqD,SAAS,EAAE;QAAAzD,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACb,MAAM,IAAIuC,KAAK,CAAC,qCAAqCc,SAAS,CAACC,OAAO,EAAE,CAAC;MAC3E,CAAC;QAAA1D,cAAA,GAAAC,CAAA;MAAA;MAED,IAAM0D,SAAS,IAAA3D,cAAA,GAAAI,CAAA,QAAGyC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGF,SAAS;MAAC5C,cAAA,GAAAI,CAAA;MAGzC,IAAIuD,SAAS,GAAG,IAAI,EAAE;QAAA3D,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACpBwD,OAAO,CAACC,IAAI,CAAC,gCAAgCF,SAAS,IAAI,CAAC;MAC7D,CAAC;QAAA3D,cAAA,GAAAC,CAAA;MAAA;MAED,IAAM6D,MAA4B,IAAA9D,cAAA,GAAAI,CAAA,QAAG;QACnC2D,OAAO,EAAE,CAAA/D,cAAA,GAAAC,CAAA,WAAAuD,QAAQ,oBAARA,QAAQ,CAAEQ,aAAa,MAAAhE,cAAA,GAAAC,CAAA,WAAI,EAAE;QACtCgE,QAAQ,EAAE,CAAAjE,cAAA,GAAAC,CAAA,WAAAuD,QAAQ,oBAARA,QAAQ,CAAEU,iBAAiB,MAAAlE,cAAA,GAAAC,CAAA,WAAI,EAAE;QAC3CkE,UAAU,EAAE,CAAAnE,cAAA,GAAAC,CAAA,WAAAuD,QAAQ,oBAARA,QAAQ,CAAEY,WAAW,MAAApE,cAAA,GAAAC,CAAA,WAAI,EAAE;QACvCoE,WAAW,EAAExB,IAAI,CAACC,GAAG,CAAC,CAAC;QACvBwB,MAAM,EAAExD,IAAI,CAACY;MACf,CAAC;MAAC1B,cAAA,GAAAI,CAAA;MAEFoB,YAAY,CAAC+C,OAAO,GAAG1B,IAAI,CAACC,GAAG,CAAC,CAAC;MAAC9C,cAAA,GAAAI,CAAA;MAClC,OAAO0D,MAAM;IAEf,CAAC,CAAC,OAAOU,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAzE,cAAA,GAAAI,CAAA,QAAGoE,GAAG,YAAY7B,KAAK,IAAA3C,cAAA,GAAAC,CAAA,WAAGuE,GAAG,CAACd,OAAO,KAAA1D,cAAA,GAAAC,CAAA,WAAG,kCAAkC;MAACD,cAAA,GAAAI,CAAA;MAC7FwD,OAAO,CAACtC,KAAK,CAAC,+BAA+B,EAAEkD,GAAG,CAAC;MAACxE,cAAA,GAAAI,CAAA;MACpD,MAAM,IAAIuC,KAAK,CAAC8B,YAAY,CAAC;IAC/B;EACF,CAAC,GAAE,CAAC3D,IAAI,oBAAJA,IAAI,CAAEY,EAAE,CAAC,CAAC;EAGd,IAAMgD,OAAO,IAAA1E,cAAA,GAAAI,CAAA,QAAGd,OAAO,CAAC,YAAM;IAAAU,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC5B,IAAI,CAAC4B,UAAU,EAAE;MAAAhC,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAC7B,IAAM0E,GAAG,IAAA3E,cAAA,GAAAI,CAAA,QAAGyC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGd,UAAU,CAACqC,WAAW;IAACrE,cAAA,GAAAI,CAAA;IAChD,OAAOuE,GAAG,GAAGnE,YAAY;EAC3B,CAAC,EAAE,CAACwB,UAAU,oBAAVA,UAAU,CAAEqC,WAAW,EAAE7D,YAAY,CAAC,CAAC;EAG3C,IAAM6B,SAAS,IAAArC,cAAA,GAAAI,CAAA,QAAGf,WAAW,CAAAqD,iBAAA,CAAC,aAAyB;IAAA,IAAlBkC,KAAK,GAAA/E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,WAAG,KAAK;IAAAD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAChD,IAAI,CAACU,IAAI,EAAE;MAAAd,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAGlB,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAAAiB,OAAO,MAAAlB,cAAA,GAAAC,CAAA,WAAI,CAAC2E,KAAK,GAAE;MAAA5E,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAE9B,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACFe,UAAU,CAAC,IAAI,CAAC;MAACnB,cAAA,GAAAI,CAAA;MACjBmB,QAAQ,CAAC,IAAI,CAAC;MAACvB,cAAA,GAAAI,CAAA;MAEf,MAAMgC,eAAe,CAACwC,KAAK,CAAC;IAE9B,CAAC,CAAC,OAAOJ,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAzE,cAAA,GAAAI,CAAA,QAAGoE,GAAG,YAAY7B,KAAK,IAAA3C,cAAA,GAAAC,CAAA,WAAGuE,GAAG,CAACd,OAAO,KAAA1D,cAAA,GAAAC,CAAA,WAAG,sBAAsB;MAACD,cAAA,GAAAI,CAAA;MACjFmB,QAAQ,CAACkD,YAAY,CAAC;IACxB,CAAC,SAAS;MAAAzE,cAAA,GAAAI,CAAA;MACRe,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,GAAE,CAACL,IAAI,EAAEI,OAAO,EAAEkB,eAAe,CAAC,CAAC;EAGpC,IAAMyC,WAAW,IAAA7E,cAAA,GAAAI,CAAA,QAAGf,WAAW,CAAAqD,iBAAA,CAAC,aAAY;IAAA1C,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC1C,MAAMiC,SAAS,CAAC,IAAI,CAAC;EACvB,CAAC,GAAE,CAACA,SAAS,CAAC,CAAC;EAACrC,cAAA,GAAAI,CAAA;EAGhB0E,KAAK,CAACC,SAAS,CAAC,YAAM;IAAA/E,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACpB,IAAI,CAAAJ,cAAA,GAAAC,CAAA,YAACS,WAAW,MAAAV,cAAA,GAAAC,CAAA,WAAI,CAACa,IAAI,GAAE;MAAAd,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAElC,IAAM+E,QAAQ,IAAAhF,cAAA,GAAAI,CAAA,QAAG6E,WAAW,CAAC,YAAM;MAAAjF,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACjC,IAAIsE,OAAO,EAAE;QAAA1E,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACXiC,SAAS,CAAC,KAAK,CAAC;MAClB,CAAC;QAAArC,cAAA,GAAAC,CAAA;MAAA;IACH,CAAC,EAAEW,eAAe,CAAC;IAACZ,cAAA,GAAAI,CAAA;IAEpB,OAAO,YAAM;MAAAJ,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAA8E,aAAa,CAACF,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAACtE,WAAW,EAAEI,IAAI,EAAE4D,OAAO,EAAErC,SAAS,EAAEzB,eAAe,CAAC,CAAC;EAACZ,cAAA,GAAAI,CAAA;EAG7D,OAAOd,OAAO,CAAC,YAAO;IAAAU,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAAA;MACpB6B,IAAI,EAAED,UAAU;MAChBd,OAAO,EAAE,CAAAlB,cAAA,GAAAC,CAAA,WAAAiB,OAAO,MAAAlB,cAAA,GAAAC,CAAA,WAAIiC,YAAY;MAChCZ,KAAK,EAAE,CAAAtB,cAAA,GAAAC,CAAA,WAAAqB,KAAK,MAAAtB,cAAA,GAAAC,CAAA,WAAIkC,UAAU,oBAAVA,UAAU,CAAEuB,OAAO,MAAA1D,cAAA,GAAAC,CAAA,WAAI,IAAI;MAC3CoC,SAAS,EAATA,SAAS;MACTwC,WAAW,EAAXA,WAAW;MACXvC,eAAe,EAAfA,eAAe;MACfoC,OAAO,EAAPA;IACF,CAAC;EAAD,CAAE,EAAE,CACF1C,UAAU,EACVd,OAAO,EACPgB,YAAY,EACZZ,KAAK,EACLa,UAAU,EACVE,SAAS,EACTwC,WAAW,EACXvC,eAAe,EACfoC,OAAO,CACR,CAAC;AACJ;AAKA,OAAO,SAASS,qBAAqBA,CAACC,UAAiC,EAAE;EAAApF,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAI,CAAA;EACvE,OAAOd,OAAO,CAAC,YAAM;IAAAU,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACnB,IAAI,CAACgF,UAAU,EAAE;MAAApF,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAE7B,IAAAoF,MAAA,IAAArF,cAAA,GAAAI,CAAA,QAA0CgF,UAAU;MAA5CrB,OAAO,GAAAsB,MAAA,CAAPtB,OAAO;MAAEE,QAAQ,GAAAoB,MAAA,CAARpB,QAAQ;MAAEE,UAAU,GAAAkB,MAAA,CAAVlB,UAAU;IAGrC,IAAMmB,aAAa,IAAAtF,cAAA,GAAAI,CAAA,QAAG2D,OAAO,CAACwB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IACzC,IAAMC,cAAc,IAAAxF,cAAA,GAAAI,CAAA,QAAG6D,QAAQ,CAACsB,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;IAC5C,IAAME,gBAAgB,IAAAzF,cAAA,GAAAI,CAAA,QAAG+D,UAAU,CAAC,CAAC,CAAC;IAEtC,IAAMuB,OAAO,IAAA1F,cAAA,GAAAI,CAAA,QAAG2D,OAAO,CAACjE,MAAM,GAAG,CAAC,IAAAE,cAAA,GAAAC,CAAA,WAC7B8D,OAAO,CAAC4B,MAAM,CAAC,UAAAC,CAAC,EAAI;MAAA5F,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAwF,CAAC,CAAC9B,MAAM,KAAK,KAAK;IAAD,CAAC,CAAC,CAAChE,MAAM,GAAGiE,OAAO,CAACjE,MAAM,GAAI,GAAG,KAAAE,cAAA,GAAAC,CAAA,WACvE,CAAC;IAEL,IAAM4F,mBAAmB,IAAA7F,cAAA,GAAAI,CAAA,QAAG6D,QAAQ,CAACnE,MAAM,GAAG,CAAC,IAAAE,cAAA,GAAAC,CAAA,WAC3CgE,QAAQ,CAAC6B,MAAM,CAAC,UAACC,GAAG,EAAE3F,CAAC,EAAK;MAAAJ,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAA2F,GAAG,IAAI,CAAA/F,cAAA,GAAAC,CAAA,WAAAG,CAAC,CAAC4F,aAAa,MAAAhG,cAAA,GAAAC,CAAA,WAAI,CAAC,EAAC;IAAD,CAAC,EAAE,CAAC,CAAC,GAAGgE,QAAQ,CAACnE,MAAM,KAAAE,cAAA,GAAAC,CAAA,WAC9E,CAAC;IAEL,IAAMgG,UAAU,IAAAjG,cAAA,GAAAI,CAAA,QAAG+D,UAAU,CAACrE,MAAM,IAAI,CAAC,IAAAE,cAAA,GAAAC,CAAA,WAAI,YAAM;MAAAD,cAAA,GAAAE,CAAA;MACjD,IAAMqE,OAAO,IAAAvE,cAAA,GAAAI,CAAA,QAAG+D,UAAU,CAAC,CAAC,CAAC;MAC7B,IAAM+B,QAAQ,IAAAlG,cAAA,GAAAI,CAAA,QAAG+D,UAAU,CAAC,CAAC,CAAC;MAC9B,IAAMgC,MAAM,IAAAnG,cAAA,GAAAI,CAAA,QAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC;MAACJ,cAAA,GAAAI,CAAA;MAElG,OAAO+F,MAAM,CAACL,MAAM,CAAC,UAACM,MAAM,EAAEC,KAAK,EAAK;QAAArG,cAAA,GAAAE,CAAA;QACtC,IAAMoG,YAAY,IAAAtG,cAAA,GAAAI,CAAA,QAAG,CAAAJ,cAAA,GAAAC,CAAA,WAAAsE,OAAO,oBAAPA,OAAO,CAAG8B,KAAK,CAAC,MAAArG,cAAA,GAAAC,CAAA,WAAI,CAAC;QAC1C,IAAMsG,aAAa,IAAAvG,cAAA,GAAAI,CAAA,QAAG,CAAAJ,cAAA,GAAAC,CAAA,WAAAiG,QAAQ,oBAARA,QAAQ,CAAGG,KAAK,CAAC,MAAArG,cAAA,GAAAC,CAAA,WAAI,CAAC;QAC5C,IAAMuG,MAAM,IAAAxG,cAAA,GAAAI,CAAA,QAAGkG,YAAY,GAAGC,aAAa;QAACvG,cAAA,GAAAI,CAAA;QAE5CgG,MAAM,CAACC,KAAK,CAAC,GAAG;UACd9B,OAAO,EAAE+B,YAAY;UACrBJ,QAAQ,EAAEK,aAAa;UACvBC,MAAM,EAANA,MAAM;UACNC,KAAK,EAAED,MAAM,GAAG,CAAC,IAAAxG,cAAA,GAAAC,CAAA,WAAG,WAAW,KAAAD,cAAA,GAAAC,CAAA,WAAGuG,MAAM,GAAG,CAAC,CAAC,IAAAxG,cAAA,GAAAC,CAAA,WAAG,WAAW,KAAAD,cAAA,GAAAC,CAAA,WAAG,QAAQ;QACxE,CAAC;QAACD,cAAA,GAAAI,CAAA;QAEF,OAAOgG,MAAM;MACf,CAAC,EAAE,CAAC,CAAwB,CAAC;IAC/B,CAAC,CAAE,CAAC,KAAApG,cAAA,GAAAC,CAAA,WAAG,CAAC,CAAC;IAACD,cAAA,GAAAI,CAAA;IAEV,OAAO;MACLkF,aAAa,EAAbA,aAAa;MACbE,cAAc,EAAdA,cAAc;MACdC,gBAAgB,EAAhBA,gBAAgB;MAChBC,OAAO,EAAPA,OAAO;MACPG,mBAAmB,EAAnBA,mBAAmB;MACnBI,UAAU,EAAVA,UAAU;MACVS,YAAY,EAAE3C,OAAO,CAACjE,MAAM;MAC5B6G,aAAa,EAAE1C,QAAQ,CAACnE,MAAM;MAC9B8G,WAAW,EAAExB,UAAU,CAACf;IAC1B,CAAC;EACH,CAAC,EAAE,CAACe,UAAU,CAAC,CAAC;AAClB;AAKA,OAAO,SAASyB,0BAA0BA,CACxCC,QAAkD,EAElD;EAAA,IADAC,IAA0B,GAAAlH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,WAAG,EAAE;EAAAD,cAAA,GAAAE,CAAA;EAE/B,IAAA8G,MAAA,IAAAhH,cAAA,GAAAI,CAAA,QAAiBT,kBAAkB,CAAC,CAAC;IAA7BsC,IAAI,GAAA+E,MAAA,CAAJ/E,IAAI;EAA0BjC,cAAA,GAAAI,CAAA;EAEtC,OAAOd,OAAO,CAAC,YAAM;IAAAU,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAAA,OAAA0G,QAAQ,CAAC7E,IAAI,CAAC;EAAD,CAAC,GAAGA,IAAI,EAAAgF,MAAA,CAAAC,kBAAA,CAAKH,IAAI,EAAC,CAAC;AACvD;AAEA,eAAe;EACbpH,kBAAkB,EAAlBA,kBAAkB;EAClBwF,qBAAqB,EAArBA,qBAAqB;EACrB0B,0BAA0B,EAA1BA;AACF,CAAC", "ignoreList": []}