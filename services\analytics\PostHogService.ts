/**
 * PostHog Analytics Service
 * Replaces Amplitude/Mixpanel with PostHog for event tracking and user analytics
 */

import { Platform } from 'react-native';
import PostHog from 'posthog-js';

export interface UserProperties {
  userId?: string;
  email?: string;
  name?: string;
  tennisLevel?: string;
  subscriptionTier?: string;
  platform?: string;
  appVersion?: string;
  [key: string]: any;
}

export interface EventProperties {
  [key: string]: string | number | boolean | null | undefined;
}

export interface AnalyticsEvent {
  event: string;
  properties?: EventProperties;
  timestamp?: Date;
}

class PostHogService {
  private isInitialized = false;
  private posthog: any = null;
  private projectApiKey: string;
  private apiHost: string;
  private userId: string | null = null;

  constructor() {
    this.projectApiKey = process.env.POSTHOG_PROJECT_API_KEY || '';
    this.apiHost = process.env.POSTHOG_API_HOST || 'https://app.posthog.com';
  }

  /**
   * Initialize PostHog analytics
   */
  async initialize(): Promise<void> {
    try {
      if (!this.projectApiKey) {
        console.warn('PostHog project API key not configured');
        return;
      }

      if (Platform.OS === 'web') {
        // Initialize PostHog for web
        PostHog.init(this.projectApiKey, {
          api_host: this.apiHost,
          autocapture: true,
          capture_pageview: true,
          disable_session_recording: false,
          loaded: (posthog) => {
            this.posthog = posthog;
            console.log('PostHog initialized for web');
          },
        });
      } else {
        // For React Native, we'll use the JS SDK with some modifications
        // In a real implementation, you might use posthog-react-native
        this.posthog = {
          capture: this.mockCapture.bind(this),
          identify: this.mockIdentify.bind(this),
          alias: this.mockAlias.bind(this),
          reset: this.mockReset.bind(this),
          group: this.mockGroup.bind(this),
        };
        console.log('PostHog initialized for React Native (mock)');
      }

      this.isInitialized = true;
      
      // Set default properties
      this.setDefaultProperties();
      
      console.log('PostHog analytics service initialized');
    } catch (error) {
      console.error('Failed to initialize PostHog:', error);
    }
  }

  /**
   * Set default properties for all events
   */
  private setDefaultProperties(): void {
    const defaultProps = {
      platform: Platform.OS,
      app_version: process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0',
      environment: process.env.EXPO_PUBLIC_APP_ENV || 'development',
    };

    if (this.posthog?.register) {
      this.posthog.register(defaultProps);
    }
  }

  /**
   * Identify user
   */
  identify(userId: string, properties?: UserProperties): void {
    if (!this.isInitialized || !this.posthog) return;

    this.userId = userId;
    
    try {
      this.posthog.identify(userId, {
        ...properties,
        $set: properties,
      });
      
      console.log('User identified:', userId, properties);
    } catch (error) {
      console.error('Failed to identify user:', error);
    }
  }

  /**
   * Track event
   */
  capture(event: string, properties?: EventProperties): void {
    if (!this.isInitialized || !this.posthog) return;

    try {
      this.posthog.capture(event, {
        ...properties,
        timestamp: new Date().toISOString(),
      });
      
      console.log('Event tracked:', event, properties);
    } catch (error) {
      console.error('Failed to track event:', error);
    }
  }

  /**
   * Set user properties
   */
  setUserProperties(properties: UserProperties): void {
    if (!this.isInitialized || !this.posthog) return;

    try {
      this.posthog.setPersonProperties(properties);
      console.log('User properties set:', properties);
    } catch (error) {
      console.error('Failed to set user properties:', error);
    }
  }

  /**
   * Create alias for user
   */
  alias(alias: string): void {
    if (!this.isInitialized || !this.posthog) return;

    try {
      this.posthog.alias(alias);
      console.log('User alias created:', alias);
    } catch (error) {
      console.error('Failed to create alias:', error);
    }
  }

  /**
   * Reset user session
   */
  reset(): void {
    if (!this.isInitialized || !this.posthog) return;

    try {
      this.posthog.reset();
      this.userId = null;
      console.log('User session reset');
    } catch (error) {
      console.error('Failed to reset session:', error);
    }
  }

  /**
   * Group user
   */
  group(groupType: string, groupKey: string, properties?: Record<string, any>): void {
    if (!this.isInitialized || !this.posthog) return;

    try {
      this.posthog.group(groupType, groupKey, properties);
      console.log('User grouped:', groupType, groupKey, properties);
    } catch (error) {
      console.error('Failed to group user:', error);
    }
  }

  // Event tracking methods for common tennis app events

  /**
   * Track user login
   */
  trackLogin(method: 'email' | 'google' | 'facebook' | 'magic_link'): void {
    this.capture('user_login', {
      login_method: method,
      platform: Platform.OS,
    });
  }

  /**
   * Track user registration
   */
  trackRegistration(method: 'email' | 'google' | 'facebook' | 'magic_link'): void {
    this.capture('user_registration', {
      registration_method: method,
      platform: Platform.OS,
    });
  }

  /**
   * Track video upload
   */
  trackVideoUpload(videoType: string, duration: number, fileSize: number): void {
    this.capture('video_uploaded', {
      video_type: videoType,
      duration_seconds: duration,
      file_size_mb: Math.round(fileSize / (1024 * 1024) * 100) / 100,
    });
  }

  /**
   * Track training session
   */
  trackTrainingSession(sessionType: string, duration: number, completed: boolean): void {
    this.capture('training_session', {
      session_type: sessionType,
      duration_minutes: Math.round(duration / 60),
      completed,
    });
  }

  /**
   * Track purchase/subscription
   */
  trackPurchase(productId: string, price: number, currency: string, subscriptionTier?: string): void {
    this.capture('purchase_completed', {
      product_id: productId,
      price,
      currency,
      subscription_tier: subscriptionTier,
      revenue: price, // PostHog revenue tracking
    });
  }

  /**
   * Track match recording
   */
  trackMatchRecording(matchType: string, duration: number, score?: string): void {
    this.capture('match_recorded', {
      match_type: matchType,
      duration_minutes: Math.round(duration / 60),
      score,
    });
  }

  /**
   * Track AI analysis
   */
  trackAIAnalysis(analysisType: string, confidence: number, processingTime: number): void {
    this.capture('ai_analysis_completed', {
      analysis_type: analysisType,
      confidence_score: confidence,
      processing_time_seconds: processingTime,
    });
  }

  /**
   * Track feature usage
   */
  trackFeatureUsage(featureName: string, context?: string): void {
    this.capture('feature_used', {
      feature_name: featureName,
      context,
    });
  }

  /**
   * Track error
   */
  trackError(errorType: string, errorMessage: string, context?: string): void {
    this.capture('error_occurred', {
      error_type: errorType,
      error_message: errorMessage,
      context,
    });
  }

  /**
   * Track subscription events
   */
  trackSubscriptionEvent(event: 'started' | 'cancelled' | 'renewed' | 'upgraded' | 'downgraded', tier: string): void {
    this.capture(`subscription_${event}`, {
      subscription_tier: tier,
    });
  }

  // Mock methods for React Native (until posthog-react-native is properly integrated)
  private mockCapture(event: string, properties?: any): void {
    console.log('[PostHog Mock] Event:', event, properties);
  }

  private mockIdentify(userId: string, properties?: any): void {
    console.log('[PostHog Mock] Identify:', userId, properties);
  }

  private mockAlias(alias: string): void {
    console.log('[PostHog Mock] Alias:', alias);
  }

  private mockReset(): void {
    console.log('[PostHog Mock] Reset');
  }

  private mockGroup(groupType: string, groupKey: string, properties?: any): void {
    console.log('[PostHog Mock] Group:', groupType, groupKey, properties);
  }

  /**
   * Check if service is ready
   */
  isReady(): boolean {
    return this.isInitialized && !!this.posthog;
  }

  /**
   * Get current user ID
   */
  getCurrentUserId(): string | null {
    return this.userId;
  }
}

// Export singleton instance
export const postHogService = new PostHogService();
