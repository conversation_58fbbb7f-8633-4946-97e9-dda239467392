1581c8707ce6305af5e78981951b0f1f
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_h79tewk8j() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\monitoring\\RealTimePerformanceMonitor.ts";
  var hash = "d547ff0ca60576649256f56d8c1ebd013c5de59a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\monitoring\\RealTimePerformanceMonitor.ts",
    statementMap: {
      "0": {
        start: {
          line: 69,
          column: 54
        },
        end: {
          line: 69,
          column: 63
        }
      },
      "1": {
        start: {
          line: 70,
          column: 39
        },
        end: {
          line: 70,
          column: 41
        }
      },
      "2": {
        start: {
          line: 71,
          column: 63
        },
        end: {
          line: 71,
          column: 65
        }
      },
      "3": {
        start: {
          line: 72,
          column: 54
        },
        end: {
          line: 72,
          column: 58
        }
      },
      "4": {
        start: {
          line: 73,
          column: 25
        },
        end: {
          line: 73,
          column: 30
        }
      },
      "5": {
        start: {
          line: 75,
          column: 37
        },
        end: {
          line: 89,
          column: 3
        }
      },
      "6": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 98,
          column: 5
        }
      },
      "7": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 60
        }
      },
      "8": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 97,
          column: 13
        }
      },
      "9": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 100,
          column: 29
        }
      },
      "10": {
        start: {
          line: 101,
          column: 4
        },
        end: {
          line: 101,
          column: 61
        }
      },
      "11": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 105,
          column: 29
        }
      },
      "12": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 28
        }
      },
      "13": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 108,
          column: 26
        }
      },
      "14": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 115,
          column: 35
        }
      },
      "15": {
        start: {
          line: 115,
          column: 28
        },
        end: {
          line: 115,
          column: 35
        }
      },
      "16": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 117,
          column: 30
        }
      },
      "17": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 122,
          column: 5
        }
      },
      "18": {
        start: {
          line: 120,
          column: 6
        },
        end: {
          line: 120,
          column: 45
        }
      },
      "19": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 37
        }
      },
      "20": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 124,
          column: 60
        }
      },
      "21": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 131,
          column: 32
        }
      },
      "22": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 132,
          column: 33
        }
      },
      "23": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 139,
          column: 34
        }
      },
      "24": {
        start: {
          line: 146,
          column: 18
        },
        end: {
          line: 146,
          column: 50
        }
      },
      "25": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 149,
          column: 5
        }
      },
      "26": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 38
        }
      },
      "27": {
        start: {
          line: 156,
          column: 18
        },
        end: {
          line: 156,
          column: 57
        }
      },
      "28": {
        start: {
          line: 156,
          column: 40
        },
        end: {
          line: 156,
          column: 56
        }
      },
      "29": {
        start: {
          line: 157,
          column: 4
        },
        end: {
          line: 159,
          column: 5
        }
      },
      "30": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 32
        }
      },
      "31": {
        start: {
          line: 166,
          column: 4
        },
        end: {
          line: 166,
          column: 21
        }
      },
      "32": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 173,
          column: 51
        }
      },
      "33": {
        start: {
          line: 175,
          column: 4
        },
        end: {
          line: 178,
          column: 5
        }
      },
      "34": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 176,
          column: 28
        }
      },
      "35": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 29
        }
      },
      "36": {
        start: {
          line: 190,
          column: 26
        },
        end: {
          line: 190,
          column: 56
        }
      },
      "37": {
        start: {
          line: 191,
          column: 23
        },
        end: {
          line: 191,
          column: 45
        }
      },
      "38": {
        start: {
          line: 193,
          column: 26
        },
        end: {
          line: 193,
          column: 77
        }
      },
      "39": {
        start: {
          line: 193,
          column: 52
        },
        end: {
          line: 193,
          column: 76
        }
      },
      "40": {
        start: {
          line: 194,
          column: 23
        },
        end: {
          line: 194,
          column: 58
        }
      },
      "41": {
        start: {
          line: 194,
          column: 46
        },
        end: {
          line: 194,
          column: 57
        }
      },
      "42": {
        start: {
          line: 195,
          column: 19
        },
        end: {
          line: 195,
          column: 50
        }
      },
      "43": {
        start: {
          line: 195,
          column: 42
        },
        end: {
          line: 195,
          column: 49
        }
      },
      "44": {
        start: {
          line: 197,
          column: 20
        },
        end: {
          line: 199,
          column: 9
        }
      },
      "45": {
        start: {
          line: 198,
          column: 36
        },
        end: {
          line: 198,
          column: 45
        }
      },
      "46": {
        start: {
          line: 202,
          column: 54
        },
        end: {
          line: 202,
          column: 62
        }
      },
      "47": {
        start: {
          line: 203,
          column: 4
        },
        end: {
          line: 214,
          column: 5
        }
      },
      "48": {
        start: {
          line: 204,
          column: 24
        },
        end: {
          line: 204,
          column: 70
        }
      },
      "49": {
        start: {
          line: 205,
          column: 25
        },
        end: {
          line: 205,
          column: 68
        }
      },
      "50": {
        start: {
          line: 207,
          column: 23
        },
        end: {
          line: 207,
          column: 86
        }
      },
      "51": {
        start: {
          line: 207,
          column: 54
        },
        end: {
          line: 207,
          column: 63
        }
      },
      "52": {
        start: {
          line: 208,
          column: 24
        },
        end: {
          line: 208,
          column: 89
        }
      },
      "53": {
        start: {
          line: 208,
          column: 56
        },
        end: {
          line: 208,
          column: 65
        }
      },
      "54": {
        start: {
          line: 210,
          column: 21
        },
        end: {
          line: 210,
          column: 62
        }
      },
      "55": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 213,
          column: 49
        }
      },
      "56": {
        start: {
          line: 212,
          column: 23
        },
        end: {
          line: 212,
          column: 43
        }
      },
      "57": {
        start: {
          line: 213,
          column: 11
        },
        end: {
          line: 213,
          column: 49
        }
      },
      "58": {
        start: {
          line: 213,
          column: 29
        },
        end: {
          line: 213,
          column: 49
        }
      },
      "59": {
        start: {
          line: 216,
          column: 4
        },
        end: {
          line: 216,
          column: 50
        }
      },
      "60": {
        start: {
          line: 228,
          column: 19
        },
        end: {
          line: 228,
          column: 48
        }
      },
      "61": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 235,
          column: 6
        }
      },
      "62": {
        start: {
          line: 241,
          column: 22
        },
        end: {
          line: 241,
          column: 32
        }
      },
      "63": {
        start: {
          line: 242,
          column: 44
        },
        end: {
          line: 242,
          column: 46
        }
      },
      "64": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 284,
          column: 5
        }
      },
      "65": {
        start: {
          line: 246,
          column: 28
        },
        end: {
          line: 246,
          column: 64
        }
      },
      "66": {
        start: {
          line: 247,
          column: 6
        },
        end: {
          line: 247,
          column: 40
        }
      },
      "67": {
        start: {
          line: 250,
          column: 29
        },
        end: {
          line: 250,
          column: 66
        }
      },
      "68": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 251,
          column: 41
        }
      },
      "69": {
        start: {
          line: 254,
          column: 28
        },
        end: {
          line: 254,
          column: 64
        }
      },
      "70": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 40
        }
      },
      "71": {
        start: {
          line: 258,
          column: 27
        },
        end: {
          line: 258,
          column: 68
        }
      },
      "72": {
        start: {
          line: 259,
          column: 6
        },
        end: {
          line: 259,
          column: 39
        }
      },
      "73": {
        start: {
          line: 262,
          column: 28
        },
        end: {
          line: 262,
          column: 70
        }
      },
      "74": {
        start: {
          line: 263,
          column: 6
        },
        end: {
          line: 263,
          column: 40
        }
      },
      "75": {
        start: {
          line: 266,
          column: 6
        },
        end: {
          line: 266,
          column: 36
        }
      },
      "76": {
        start: {
          line: 269,
          column: 6
        },
        end: {
          line: 271,
          column: 7
        }
      },
      "77": {
        start: {
          line: 270,
          column: 8
        },
        end: {
          line: 270,
          column: 37
        }
      },
      "78": {
        start: {
          line: 274,
          column: 21
        },
        end: {
          line: 274,
          column: 42
        }
      },
      "79": {
        start: {
          line: 275,
          column: 6
        },
        end: {
          line: 275,
          column: 35
        }
      },
      "80": {
        start: {
          line: 278,
          column: 6
        },
        end: {
          line: 280,
          column: 7
        }
      },
      "81": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 279,
          column: 55
        }
      },
      "82": {
        start: {
          line: 283,
          column: 6
        },
        end: {
          line: 283,
          column: 69
        }
      },
      "83": {
        start: {
          line: 289,
          column: 4
        },
        end: {
          line: 307,
          column: 6
        }
      },
      "84": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 329,
          column: 6
        }
      },
      "85": {
        start: {
          line: 333,
          column: 4
        },
        end: {
          line: 343,
          column: 6
        }
      },
      "86": {
        start: {
          line: 347,
          column: 23
        },
        end: {
          line: 347,
          column: 54
        }
      },
      "87": {
        start: {
          line: 349,
          column: 4
        },
        end: {
          line: 367,
          column: 6
        }
      },
      "88": {
        start: {
          line: 371,
          column: 4
        },
        end: {
          line: 397,
          column: 5
        }
      },
      "89": {
        start: {
          line: 372,
          column: 27
        },
        end: {
          line: 372,
          column: 77
        }
      },
      "90": {
        start: {
          line: 374,
          column: 6
        },
        end: {
          line: 393,
          column: 8
        }
      },
      "91": {
        start: {
          line: 395,
          column: 6
        },
        end: {
          line: 395,
          column: 63
        }
      },
      "92": {
        start: {
          line: 396,
          column: 6
        },
        end: {
          line: 396,
          column: 16
        }
      },
      "93": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 413,
          column: 7
        }
      },
      "94": {
        start: {
          line: 402,
          column: 6
        },
        end: {
          line: 404,
          column: 7
        }
      },
      "95": {
        start: {
          line: 403,
          column: 8
        },
        end: {
          line: 403,
          column: 42
        }
      },
      "96": {
        start: {
          line: 406,
          column: 28
        },
        end: {
          line: 406,
          column: 58
        }
      },
      "97": {
        start: {
          line: 407,
          column: 6
        },
        end: {
          line: 407,
          column: 33
        }
      },
      "98": {
        start: {
          line: 410,
          column: 6
        },
        end: {
          line: 412,
          column: 7
        }
      },
      "99": {
        start: {
          line: 411,
          column: 8
        },
        end: {
          line: 411,
          column: 30
        }
      },
      "100": {
        start: {
          line: 417,
          column: 4
        },
        end: {
          line: 446,
          column: 7
        }
      },
      "101": {
        start: {
          line: 418,
          column: 6
        },
        end: {
          line: 418,
          column: 36
        }
      },
      "102": {
        start: {
          line: 418,
          column: 29
        },
        end: {
          line: 418,
          column: 36
        }
      },
      "103": {
        start: {
          line: 420,
          column: 52
        },
        end: {
          line: 420,
          column: 56
        }
      },
      "104": {
        start: {
          line: 422,
          column: 6
        },
        end: {
          line: 426,
          column: 7
        }
      },
      "105": {
        start: {
          line: 423,
          column: 8
        },
        end: {
          line: 423,
          column: 30
        }
      },
      "106": {
        start: {
          line: 424,
          column: 13
        },
        end: {
          line: 426,
          column: 7
        }
      },
      "107": {
        start: {
          line: 425,
          column: 8
        },
        end: {
          line: 425,
          column: 29
        }
      },
      "108": {
        start: {
          line: 428,
          column: 6
        },
        end: {
          line: 445,
          column: 7
        }
      },
      "109": {
        start: {
          line: 429,
          column: 40
        },
        end: {
          line: 437,
          column: 9
        }
      },
      "110": {
        start: {
          line: 439,
          column: 8
        },
        end: {
          line: 439,
          column: 35
        }
      },
      "111": {
        start: {
          line: 442,
          column: 8
        },
        end: {
          line: 444,
          column: 9
        }
      },
      "112": {
        start: {
          line: 443,
          column: 10
        },
        end: {
          line: 443,
          column: 49
        }
      },
      "113": {
        start: {
          line: 450,
          column: 34
        },
        end: {
          line: 450,
          column: 36
        }
      },
      "114": {
        start: {
          line: 452,
          column: 4
        },
        end: {
          line: 469,
          column: 5
        }
      },
      "115": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 454,
          column: 74
        }
      },
      "116": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 455,
          column: 81
        }
      },
      "117": {
        start: {
          line: 456,
          column: 8
        },
        end: {
          line: 456,
          column: 14
        }
      },
      "118": {
        start: {
          line: 458,
          column: 8
        },
        end: {
          line: 458,
          column: 54
        }
      },
      "119": {
        start: {
          line: 459,
          column: 8
        },
        end: {
          line: 459,
          column: 72
        }
      },
      "120": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 460,
          column: 14
        }
      },
      "121": {
        start: {
          line: 462,
          column: 8
        },
        end: {
          line: 462,
          column: 65
        }
      },
      "122": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 463,
          column: 63
        }
      },
      "123": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 464,
          column: 14
        }
      },
      "124": {
        start: {
          line: 466,
          column: 8
        },
        end: {
          line: 466,
          column: 63
        }
      },
      "125": {
        start: {
          line: 467,
          column: 8
        },
        end: {
          line: 467,
          column: 63
        }
      },
      "126": {
        start: {
          line: 468,
          column: 8
        },
        end: {
          line: 468,
          column: 14
        }
      },
      "127": {
        start: {
          line: 471,
          column: 4
        },
        end: {
          line: 471,
          column: 23
        }
      },
      "128": {
        start: {
          line: 475,
          column: 22
        },
        end: {
          line: 475,
          column: 32
        }
      },
      "129": {
        start: {
          line: 476,
          column: 23
        },
        end: {
          line: 476,
          column: 63
        }
      },
      "130": {
        start: {
          line: 477,
          column: 26
        },
        end: {
          line: 477,
          column: 81
        }
      },
      "131": {
        start: {
          line: 477,
          column: 49
        },
        end: {
          line: 477,
          column: 80
        }
      },
      "132": {
        start: {
          line: 480,
          column: 25
        },
        end: {
          line: 480,
          column: 66
        }
      },
      "133": {
        start: {
          line: 483,
          column: 19
        },
        end: {
          line: 488,
          column: 5
        }
      },
      "134": {
        start: {
          line: 491,
          column: 28
        },
        end: {
          line: 491,
          column: 71
        }
      },
      "135": {
        start: {
          line: 493,
          column: 4
        },
        end: {
          line: 500,
          column: 6
        }
      },
      "136": {
        start: {
          line: 497,
          column: 38
        },
        end: {
          line: 497,
          column: 53
        }
      },
      "137": {
        start: {
          line: 504,
          column: 4
        },
        end: {
          line: 504,
          column: 41
        }
      },
      "138": {
        start: {
          line: 504,
          column: 30
        },
        end: {
          line: 504,
          column: 41
        }
      },
      "139": {
        start: {
          line: 506,
          column: 16
        },
        end: {
          line: 506,
          column: 19
        }
      },
      "140": {
        start: {
          line: 507,
          column: 20
        },
        end: {
          line: 513,
          column: 5
        }
      },
      "141": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 527,
          column: 7
        }
      },
      "142": {
        start: {
          line: 516,
          column: 30
        },
        end: {
          line: 516,
          column: 74
        }
      },
      "143": {
        start: {
          line: 516,
          column: 50
        },
        end: {
          line: 516,
          column: 73
        }
      },
      "144": {
        start: {
          line: 517,
          column: 6
        },
        end: {
          line: 517,
          column: 47
        }
      },
      "145": {
        start: {
          line: 517,
          column: 40
        },
        end: {
          line: 517,
          column: 47
        }
      },
      "146": {
        start: {
          line: 519,
          column: 28
        },
        end: {
          line: 524,
          column: 36
        }
      },
      "147": {
        start: {
          line: 520,
          column: 8
        },
        end: {
          line: 520,
          column: 48
        }
      },
      "148": {
        start: {
          line: 520,
          column: 31
        },
        end: {
          line: 520,
          column: 48
        }
      },
      "149": {
        start: {
          line: 522,
          column: 32
        },
        end: {
          line: 522,
          column: 85
        }
      },
      "150": {
        start: {
          line: 523,
          column: 8
        },
        end: {
          line: 523,
          column: 51
        }
      },
      "151": {
        start: {
          line: 526,
          column: 6
        },
        end: {
          line: 526,
          column: 46
        }
      },
      "152": {
        start: {
          line: 529,
          column: 4
        },
        end: {
          line: 529,
          column: 42
        }
      },
      "153": {
        start: {
          line: 533,
          column: 26
        },
        end: {
          line: 533,
          column: 60
        }
      },
      "154": {
        start: {
          line: 534,
          column: 4
        },
        end: {
          line: 534,
          column: 57
        }
      },
      "155": {
        start: {
          line: 534,
          column: 48
        },
        end: {
          line: 534,
          column: 55
        }
      },
      "156": {
        start: {
          line: 538,
          column: 35
        },
        end: {
          line: 538,
          column: 37
        }
      },
      "157": {
        start: {
          line: 541,
          column: 24
        },
        end: {
          line: 543,
          column: 5
        }
      },
      "158": {
        start: {
          line: 542,
          column: 6
        },
        end: {
          line: 542,
          column: 52
        }
      },
      "159": {
        start: {
          line: 545,
          column: 4
        },
        end: {
          line: 553,
          column: 5
        }
      },
      "160": {
        start: {
          line: 546,
          column: 6
        },
        end: {
          line: 552,
          column: 9
        }
      },
      "161": {
        start: {
          line: 555,
          column: 4
        },
        end: {
          line: 555,
          column: 27
        }
      },
      "162": {
        start: {
          line: 560,
          column: 4
        },
        end: {
          line: 560,
          column: 61
        }
      },
      "163": {
        start: {
          line: 564,
          column: 4
        },
        end: {
          line: 570,
          column: 7
        }
      },
      "164": {
        start: {
          line: 565,
          column: 6
        },
        end: {
          line: 569,
          column: 7
        }
      },
      "165": {
        start: {
          line: 566,
          column: 8
        },
        end: {
          line: 566,
          column: 25
        }
      },
      "166": {
        start: {
          line: 568,
          column: 8
        },
        end: {
          line: 568,
          column: 71
        }
      },
      "167": {
        start: {
          line: 575,
          column: 42
        },
        end: {
          line: 575,
          column: 74
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 94,
            column: 2
          },
          end: {
            line: 94,
            column: 3
          }
        },
        loc: {
          start: {
            line: 94,
            column: 26
          },
          end: {
            line: 109,
            column: 3
          }
        },
        line: 94
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 103,
            column: 42
          },
          end: {
            line: 103,
            column: 43
          }
        },
        loc: {
          start: {
            line: 103,
            column: 48
          },
          end: {
            line: 105,
            column: 5
          }
        },
        line: 103
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 114,
            column: 2
          },
          end: {
            line: 114,
            column: 3
          }
        },
        loc: {
          start: {
            line: 114,
            column: 25
          },
          end: {
            line: 125,
            column: 3
          }
        },
        line: 114
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 130,
            column: 2
          },
          end: {
            line: 130,
            column: 3
          }
        },
        loc: {
          start: {
            line: 130,
            column: 55
          },
          end: {
            line: 133,
            column: 3
          }
        },
        line: 130
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 138,
            column: 2
          },
          end: {
            line: 138,
            column: 3
          }
        },
        loc: {
          start: {
            line: 138,
            column: 67
          },
          end: {
            line: 140,
            column: 3
          }
        },
        line: 138
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 145,
            column: 2
          },
          end: {
            line: 145,
            column: 3
          }
        },
        loc: {
          start: {
            line: 145,
            column: 70
          },
          end: {
            line: 150,
            column: 3
          }
        },
        line: 145
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 155,
            column: 2
          },
          end: {
            line: 155,
            column: 3
          }
        },
        loc: {
          start: {
            line: 155,
            column: 42
          },
          end: {
            line: 160,
            column: 3
          }
        },
        line: 155
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 156,
            column: 35
          },
          end: {
            line: 156,
            column: 36
          }
        },
        loc: {
          start: {
            line: 156,
            column: 40
          },
          end: {
            line: 156,
            column: 56
          }
        },
        line: 156
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 165,
            column: 2
          },
          end: {
            line: 165,
            column: 3
          }
        },
        loc: {
          start: {
            line: 165,
            column: 22
          },
          end: {
            line: 167,
            column: 3
          }
        },
        line: 165
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 172,
            column: 2
          },
          end: {
            line: 172,
            column: 3
          }
        },
        loc: {
          start: {
            line: 172,
            column: 59
          },
          end: {
            line: 179,
            column: 3
          }
        },
        line: 172
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 184,
            column: 2
          },
          end: {
            line: 184,
            column: 3
          }
        },
        loc: {
          start: {
            line: 189,
            column: 4
          },
          end: {
            line: 217,
            column: 3
          }
        },
        line: 189
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 193,
            column: 47
          },
          end: {
            line: 193,
            column: 48
          }
        },
        loc: {
          start: {
            line: 193,
            column: 52
          },
          end: {
            line: 193,
            column: 76
          }
        },
        line: 193
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 194,
            column: 41
          },
          end: {
            line: 194,
            column: 42
          }
        },
        loc: {
          start: {
            line: 194,
            column: 46
          },
          end: {
            line: 194,
            column: 57
          }
        },
        line: 194
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 195,
            column: 37
          },
          end: {
            line: 195,
            column: 38
          }
        },
        loc: {
          start: {
            line: 195,
            column: 42
          },
          end: {
            line: 195,
            column: 49
          }
        },
        line: 195
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 198,
            column: 22
          },
          end: {
            line: 198,
            column: 23
          }
        },
        loc: {
          start: {
            line: 198,
            column: 36
          },
          end: {
            line: 198,
            column: 45
          }
        },
        line: 198
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 207,
            column: 40
          },
          end: {
            line: 207,
            column: 41
          }
        },
        loc: {
          start: {
            line: 207,
            column: 54
          },
          end: {
            line: 207,
            column: 63
          }
        },
        line: 207
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 208,
            column: 42
          },
          end: {
            line: 208,
            column: 43
          }
        },
        loc: {
          start: {
            line: 208,
            column: 56
          },
          end: {
            line: 208,
            column: 65
          }
        },
        line: 208
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 222,
            column: 2
          },
          end: {
            line: 222,
            column: 3
          }
        },
        loc: {
          start: {
            line: 227,
            column: 5
          },
          end: {
            line: 236,
            column: 3
          }
        },
        line: 227
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 240,
            column: 2
          },
          end: {
            line: 240,
            column: 3
          }
        },
        loc: {
          start: {
            line: 240,
            column: 48
          },
          end: {
            line: 285,
            column: 3
          }
        },
        line: 240
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 287,
            column: 2
          },
          end: {
            line: 287,
            column: 3
          }
        },
        loc: {
          start: {
            line: 287,
            column: 71
          },
          end: {
            line: 308,
            column: 3
          }
        },
        line: 287
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 310,
            column: 2
          },
          end: {
            line: 310,
            column: 3
          }
        },
        loc: {
          start: {
            line: 310,
            column: 72
          },
          end: {
            line: 330,
            column: 3
          }
        },
        line: 310
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 332,
            column: 2
          },
          end: {
            line: 332,
            column: 3
          }
        },
        loc: {
          start: {
            line: 332,
            column: 71
          },
          end: {
            line: 344,
            column: 3
          }
        },
        line: 332
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 346,
            column: 2
          },
          end: {
            line: 346,
            column: 3
          }
        },
        loc: {
          start: {
            line: 346,
            column: 85
          },
          end: {
            line: 368,
            column: 3
          }
        },
        line: 346
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 370,
            column: 2
          },
          end: {
            line: 370,
            column: 3
          }
        },
        loc: {
          start: {
            line: 370,
            column: 86
          },
          end: {
            line: 398,
            column: 3
          }
        },
        line: 370
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 400,
            column: 2
          },
          end: {
            line: 400,
            column: 3
          }
        },
        loc: {
          start: {
            line: 400,
            column: 62
          },
          end: {
            line: 414,
            column: 3
          }
        },
        line: 400
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 401,
            column: 23
          },
          end: {
            line: 401,
            column: 24
          }
        },
        loc: {
          start: {
            line: 401,
            column: 33
          },
          end: {
            line: 413,
            column: 5
          }
        },
        line: 401
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 416,
            column: 2
          },
          end: {
            line: 416,
            column: 3
          }
        },
        loc: {
          start: {
            line: 416,
            column: 58
          },
          end: {
            line: 447,
            column: 3
          }
        },
        line: 416
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 417,
            column: 20
          },
          end: {
            line: 417,
            column: 21
          }
        },
        loc: {
          start: {
            line: 417,
            column: 30
          },
          end: {
            line: 446,
            column: 5
          }
        },
        line: 417
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 449,
            column: 2
          },
          end: {
            line: 449,
            column: 3
          }
        },
        loc: {
          start: {
            line: 449,
            column: 90
          },
          end: {
            line: 472,
            column: 3
          }
        },
        line: 449
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 474,
            column: 2
          },
          end: {
            line: 474,
            column: 3
          }
        },
        loc: {
          start: {
            line: 474,
            column: 46
          },
          end: {
            line: 501,
            column: 3
          }
        },
        line: 474
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 477,
            column: 44
          },
          end: {
            line: 477,
            column: 45
          }
        },
        loc: {
          start: {
            line: 477,
            column: 49
          },
          end: {
            line: 477,
            column: 80
          }
        },
        line: 477
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 497,
            column: 33
          },
          end: {
            line: 497,
            column: 34
          }
        },
        loc: {
          start: {
            line: 497,
            column: 38
          },
          end: {
            line: 497,
            column: 53
          }
        },
        line: 497
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 503,
            column: 2
          },
          end: {
            line: 503,
            column: 3
          }
        },
        loc: {
          start: {
            line: 503,
            column: 70
          },
          end: {
            line: 530,
            column: 3
          }
        },
        line: 503
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 515,
            column: 36
          },
          end: {
            line: 515,
            column: 37
          }
        },
        loc: {
          start: {
            line: 515,
            column: 60
          },
          end: {
            line: 527,
            column: 5
          }
        },
        line: 515
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 516,
            column: 45
          },
          end: {
            line: 516,
            column: 46
          }
        },
        loc: {
          start: {
            line: 516,
            column: 50
          },
          end: {
            line: 516,
            column: 73
          }
        },
        line: 516
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 519,
            column: 51
          },
          end: {
            line: 519,
            column: 52
          }
        },
        loc: {
          start: {
            line: 519,
            column: 68
          },
          end: {
            line: 524,
            column: 7
          }
        },
        line: 519
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 532,
            column: 2
          },
          end: {
            line: 532,
            column: 3
          }
        },
        loc: {
          start: {
            line: 532,
            column: 71
          },
          end: {
            line: 535,
            column: 3
          }
        },
        line: 532
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 534,
            column: 43
          },
          end: {
            line: 534,
            column: 44
          }
        },
        loc: {
          start: {
            line: 534,
            column: 48
          },
          end: {
            line: 534,
            column: 55
          }
        },
        line: 534
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 537,
            column: 2
          },
          end: {
            line: 537,
            column: 3
          }
        },
        loc: {
          start: {
            line: 537,
            column: 71
          },
          end: {
            line: 556,
            column: 3
          }
        },
        line: 537
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 541,
            column: 39
          },
          end: {
            line: 541,
            column: 40
          }
        },
        loc: {
          start: {
            line: 542,
            column: 6
          },
          end: {
            line: 542,
            column: 52
          }
        },
        line: 542
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 558,
            column: 2
          },
          end: {
            line: 558,
            column: 3
          }
        },
        loc: {
          start: {
            line: 558,
            column: 85
          },
          end: {
            line: 561,
            column: 3
          }
        },
        line: 558
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 563,
            column: 2
          },
          end: {
            line: 563,
            column: 3
          }
        },
        loc: {
          start: {
            line: 563,
            column: 59
          },
          end: {
            line: 571,
            column: 3
          }
        },
        line: 563
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 564,
            column: 27
          },
          end: {
            line: 564,
            column: 28
          }
        },
        loc: {
          start: {
            line: 564,
            column: 39
          },
          end: {
            line: 570,
            column: 5
          }
        },
        line: 564
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "1": {
        loc: {
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 115,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 115,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 115
      },
      "2": {
        loc: {
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 119,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 119
      },
      "3": {
        loc: {
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "4": {
        loc: {
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 159,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 159,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 157
      },
      "5": {
        loc: {
          start: {
            line: 175,
            column: 4
          },
          end: {
            line: 178,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 175,
            column: 4
          },
          end: {
            line: 178,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 175
      },
      "6": {
        loc: {
          start: {
            line: 184,
            column: 39
          },
          end: {
            line: 184,
            column: 66
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 184,
            column: 59
          },
          end: {
            line: 184,
            column: 66
          }
        }],
        line: 184
      },
      "7": {
        loc: {
          start: {
            line: 190,
            column: 26
          },
          end: {
            line: 190,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 190,
            column: 26
          },
          end: {
            line: 190,
            column: 50
          }
        }, {
          start: {
            line: 190,
            column: 54
          },
          end: {
            line: 190,
            column: 56
          }
        }],
        line: 190
      },
      "8": {
        loc: {
          start: {
            line: 197,
            column: 20
          },
          end: {
            line: 199,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 198,
            column: 8
          },
          end: {
            line: 198,
            column: 65
          }
        }, {
          start: {
            line: 199,
            column: 8
          },
          end: {
            line: 199,
            column: 9
          }
        }],
        line: 197
      },
      "9": {
        loc: {
          start: {
            line: 203,
            column: 4
          },
          end: {
            line: 214,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 203,
            column: 4
          },
          end: {
            line: 214,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 203
      },
      "10": {
        loc: {
          start: {
            line: 212,
            column: 6
          },
          end: {
            line: 213,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 212,
            column: 6
          },
          end: {
            line: 213,
            column: 49
          }
        }, {
          start: {
            line: 213,
            column: 11
          },
          end: {
            line: 213,
            column: 49
          }
        }],
        line: 212
      },
      "11": {
        loc: {
          start: {
            line: 213,
            column: 11
          },
          end: {
            line: 213,
            column: 49
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 11
          },
          end: {
            line: 213,
            column: 49
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "12": {
        loc: {
          start: {
            line: 269,
            column: 6
          },
          end: {
            line: 271,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 269,
            column: 6
          },
          end: {
            line: 271,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 269
      },
      "13": {
        loc: {
          start: {
            line: 278,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 278,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 278
      },
      "14": {
        loc: {
          start: {
            line: 377,
            column: 17
          },
          end: {
            line: 378,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 378,
            column: 12
          },
          end: {
            line: 378,
            column: 80
          }
        }, {
          start: {
            line: 378,
            column: 83
          },
          end: {
            line: 378,
            column: 84
          }
        }],
        line: 377
      },
      "15": {
        loc: {
          start: {
            line: 387,
            column: 17
          },
          end: {
            line: 387,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 387,
            column: 17
          },
          end: {
            line: 387,
            column: 57
          }
        }, {
          start: {
            line: 387,
            column: 61
          },
          end: {
            line: 387,
            column: 62
          }
        }],
        line: 387
      },
      "16": {
        loc: {
          start: {
            line: 402,
            column: 6
          },
          end: {
            line: 404,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 402,
            column: 6
          },
          end: {
            line: 404,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 402
      },
      "17": {
        loc: {
          start: {
            line: 410,
            column: 6
          },
          end: {
            line: 412,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 410,
            column: 6
          },
          end: {
            line: 412,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 410
      },
      "18": {
        loc: {
          start: {
            line: 418,
            column: 6
          },
          end: {
            line: 418,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 418,
            column: 6
          },
          end: {
            line: 418,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 418
      },
      "19": {
        loc: {
          start: {
            line: 422,
            column: 6
          },
          end: {
            line: 426,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 422,
            column: 6
          },
          end: {
            line: 426,
            column: 7
          }
        }, {
          start: {
            line: 424,
            column: 13
          },
          end: {
            line: 426,
            column: 7
          }
        }],
        line: 422
      },
      "20": {
        loc: {
          start: {
            line: 424,
            column: 13
          },
          end: {
            line: 426,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 424,
            column: 13
          },
          end: {
            line: 426,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 424
      },
      "21": {
        loc: {
          start: {
            line: 428,
            column: 6
          },
          end: {
            line: 445,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 428,
            column: 6
          },
          end: {
            line: 445,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 428
      },
      "22": {
        loc: {
          start: {
            line: 442,
            column: 8
          },
          end: {
            line: 444,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 442,
            column: 8
          },
          end: {
            line: 444,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 442
      },
      "23": {
        loc: {
          start: {
            line: 452,
            column: 4
          },
          end: {
            line: 469,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 453,
            column: 6
          },
          end: {
            line: 456,
            column: 14
          }
        }, {
          start: {
            line: 457,
            column: 6
          },
          end: {
            line: 460,
            column: 14
          }
        }, {
          start: {
            line: 461,
            column: 6
          },
          end: {
            line: 464,
            column: 14
          }
        }, {
          start: {
            line: 465,
            column: 6
          },
          end: {
            line: 468,
            column: 14
          }
        }],
        line: 452
      },
      "24": {
        loc: {
          start: {
            line: 504,
            column: 4
          },
          end: {
            line: 504,
            column: 41
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 504,
            column: 4
          },
          end: {
            line: 504,
            column: 41
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 504
      },
      "25": {
        loc: {
          start: {
            line: 517,
            column: 6
          },
          end: {
            line: 517,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 517,
            column: 6
          },
          end: {
            line: 517,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 517
      },
      "26": {
        loc: {
          start: {
            line: 520,
            column: 8
          },
          end: {
            line: 520,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 520,
            column: 8
          },
          end: {
            line: 520,
            column: 48
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 520
      },
      "27": {
        loc: {
          start: {
            line: 533,
            column: 26
          },
          end: {
            line: 533,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 533,
            column: 26
          },
          end: {
            line: 533,
            column: 54
          }
        }, {
          start: {
            line: 533,
            column: 58
          },
          end: {
            line: 533,
            column: 60
          }
        }],
        line: 533
      },
      "28": {
        loc: {
          start: {
            line: 542,
            column: 6
          },
          end: {
            line: 542,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 542,
            column: 6
          },
          end: {
            line: 542,
            column: 36
          }
        }, {
          start: {
            line: 542,
            column: 40
          },
          end: {
            line: 542,
            column: 52
          }
        }],
        line: 542
      },
      "29": {
        loc: {
          start: {
            line: 545,
            column: 4
          },
          end: {
            line: 553,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 545,
            column: 4
          },
          end: {
            line: 553,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 545
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0, 0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d547ff0ca60576649256f56d8c1ebd013c5de59a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_h79tewk8j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_h79tewk8j();
import { advancedCacheManager } from "../caching/AdvancedCacheManager";
import { bundleAnalysisService } from "../../utils/bundleAnalysis";
var RealTimePerformanceMonitor = function () {
  function RealTimePerformanceMonitor() {
    _classCallCheck(this, RealTimePerformanceMonitor);
    this.metrics = (cov_h79tewk8j().s[0]++, new Map());
    this.alerts = (cov_h79tewk8j().s[1]++, []);
    this.listeners = (cov_h79tewk8j().s[2]++, []);
    this.monitoringInterval = (cov_h79tewk8j().s[3]++, null);
    this.isMonitoring = (cov_h79tewk8j().s[4]++, false);
    this.config = (cov_h79tewk8j().s[5]++, {
      enabled: true,
      interval: 5000,
      alertThresholds: {
        renderTime: {
          warning: 16,
          critical: 32
        },
        networkLatency: {
          warning: 1000,
          critical: 3000
        },
        memoryUsage: {
          warning: 80,
          critical: 95
        },
        cacheHitRate: {
          warning: 70,
          critical: 50
        },
        bundleSize: {
          warning: 1000000,
          critical: 2000000
        }
      },
      enableTrends: true,
      maxHistorySize: 100,
      enableAlerts: true,
      enableAutoOptimization: false
    });
  }
  return _createClass(RealTimePerformanceMonitor, [{
    key: "startMonitoring",
    value: function startMonitoring() {
      var _this = this;
      cov_h79tewk8j().f[0]++;
      cov_h79tewk8j().s[6]++;
      if (this.isMonitoring) {
        cov_h79tewk8j().b[0][0]++;
        cov_h79tewk8j().s[7]++;
        console.log('Performance monitoring already running');
        cov_h79tewk8j().s[8]++;
        return;
      } else {
        cov_h79tewk8j().b[0][1]++;
      }
      cov_h79tewk8j().s[9]++;
      this.isMonitoring = true;
      cov_h79tewk8j().s[10]++;
      console.log('Starting real-time performance monitoring');
      cov_h79tewk8j().s[11]++;
      this.monitoringInterval = setInterval(function () {
        cov_h79tewk8j().f[1]++;
        cov_h79tewk8j().s[12]++;
        _this.collectMetrics();
      }, this.config.interval);
      cov_h79tewk8j().s[13]++;
      this.collectMetrics();
    }
  }, {
    key: "stopMonitoring",
    value: function stopMonitoring() {
      cov_h79tewk8j().f[2]++;
      cov_h79tewk8j().s[14]++;
      if (!this.isMonitoring) {
        cov_h79tewk8j().b[1][0]++;
        cov_h79tewk8j().s[15]++;
        return;
      } else {
        cov_h79tewk8j().b[1][1]++;
      }
      cov_h79tewk8j().s[16]++;
      this.isMonitoring = false;
      cov_h79tewk8j().s[17]++;
      if (this.monitoringInterval) {
        cov_h79tewk8j().b[2][0]++;
        cov_h79tewk8j().s[18]++;
        clearInterval(this.monitoringInterval);
        cov_h79tewk8j().s[19]++;
        this.monitoringInterval = null;
      } else {
        cov_h79tewk8j().b[2][1]++;
      }
      cov_h79tewk8j().s[20]++;
      console.log('Stopped real-time performance monitoring');
    }
  }, {
    key: "getCurrentReport",
    value: (function () {
      var _getCurrentReport = _asyncToGenerator(function* () {
        cov_h79tewk8j().f[3]++;
        cov_h79tewk8j().s[21]++;
        yield this.collectMetrics();
        cov_h79tewk8j().s[22]++;
        return this.generateReport();
      });
      function getCurrentReport() {
        return _getCurrentReport.apply(this, arguments);
      }
      return getCurrentReport;
    }())
  }, {
    key: "addListener",
    value: function addListener(listener) {
      cov_h79tewk8j().f[4]++;
      cov_h79tewk8j().s[23]++;
      this.listeners.push(listener);
    }
  }, {
    key: "removeListener",
    value: function removeListener(listener) {
      cov_h79tewk8j().f[5]++;
      var index = (cov_h79tewk8j().s[24]++, this.listeners.indexOf(listener));
      cov_h79tewk8j().s[25]++;
      if (index > -1) {
        cov_h79tewk8j().b[3][0]++;
        cov_h79tewk8j().s[26]++;
        this.listeners.splice(index, 1);
      } else {
        cov_h79tewk8j().b[3][1]++;
      }
    }
  }, {
    key: "acknowledgeAlert",
    value: function acknowledgeAlert(alertId) {
      cov_h79tewk8j().f[6]++;
      var alert = (cov_h79tewk8j().s[27]++, this.alerts.find(function (a) {
        cov_h79tewk8j().f[7]++;
        cov_h79tewk8j().s[28]++;
        return a.id === alertId;
      }));
      cov_h79tewk8j().s[29]++;
      if (alert) {
        cov_h79tewk8j().b[4][0]++;
        cov_h79tewk8j().s[30]++;
        alert.acknowledged = true;
      } else {
        cov_h79tewk8j().b[4][1]++;
      }
    }
  }, {
    key: "clearAlerts",
    value: function clearAlerts() {
      cov_h79tewk8j().f[8]++;
      cov_h79tewk8j().s[31]++;
      this.alerts = [];
    }
  }, {
    key: "updateConfig",
    value: function updateConfig(newConfig) {
      cov_h79tewk8j().f[9]++;
      cov_h79tewk8j().s[32]++;
      this.config = Object.assign({}, this.config, newConfig);
      cov_h79tewk8j().s[33]++;
      if (this.isMonitoring) {
        cov_h79tewk8j().b[5][0]++;
        cov_h79tewk8j().s[34]++;
        this.stopMonitoring();
        cov_h79tewk8j().s[35]++;
        this.startMonitoring();
      } else {
        cov_h79tewk8j().b[5][1]++;
      }
    }
  }, {
    key: "getPerformanceTrends",
    value: function getPerformanceTrends(metric) {
      var timeRange = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_h79tewk8j().b[6][0]++, 3600000);
      cov_h79tewk8j().f[10]++;
      var metricHistory = (cov_h79tewk8j().s[36]++, (cov_h79tewk8j().b[7][0]++, this.metrics.get(metric)) || (cov_h79tewk8j().b[7][1]++, []));
      var cutoffTime = (cov_h79tewk8j().s[37]++, Date.now() - timeRange);
      var recentMetrics = (cov_h79tewk8j().s[38]++, metricHistory.filter(function (m) {
        cov_h79tewk8j().f[11]++;
        cov_h79tewk8j().s[39]++;
        return m.timestamp > cutoffTime;
      }));
      var timestamps = (cov_h79tewk8j().s[40]++, recentMetrics.map(function (m) {
        cov_h79tewk8j().f[12]++;
        cov_h79tewk8j().s[41]++;
        return m.timestamp;
      }));
      var values = (cov_h79tewk8j().s[42]++, recentMetrics.map(function (m) {
        cov_h79tewk8j().f[13]++;
        cov_h79tewk8j().s[43]++;
        return m.value;
      }));
      var average = (cov_h79tewk8j().s[44]++, values.length > 0 ? (cov_h79tewk8j().b[8][0]++, values.reduce(function (sum, val) {
        cov_h79tewk8j().f[14]++;
        cov_h79tewk8j().s[45]++;
        return sum + val;
      }, 0) / values.length) : (cov_h79tewk8j().b[8][1]++, 0));
      var trend = (cov_h79tewk8j().s[46]++, 'stable');
      cov_h79tewk8j().s[47]++;
      if (values.length >= 2) {
        cov_h79tewk8j().b[9][0]++;
        var firstHalf = (cov_h79tewk8j().s[48]++, values.slice(0, Math.floor(values.length / 2)));
        var secondHalf = (cov_h79tewk8j().s[49]++, values.slice(Math.floor(values.length / 2)));
        var firstAvg = (cov_h79tewk8j().s[50]++, firstHalf.reduce(function (sum, val) {
          cov_h79tewk8j().f[15]++;
          cov_h79tewk8j().s[51]++;
          return sum + val;
        }, 0) / firstHalf.length);
        var secondAvg = (cov_h79tewk8j().s[52]++, secondHalf.reduce(function (sum, val) {
          cov_h79tewk8j().f[16]++;
          cov_h79tewk8j().s[53]++;
          return sum + val;
        }, 0) / secondHalf.length);
        var change = (cov_h79tewk8j().s[54]++, (secondAvg - firstAvg) / firstAvg * 100);
        cov_h79tewk8j().s[55]++;
        if (change > 10) {
          cov_h79tewk8j().b[10][0]++;
          cov_h79tewk8j().s[56]++;
          trend = 'degrading';
        } else {
          cov_h79tewk8j().b[10][1]++;
          cov_h79tewk8j().s[57]++;
          if (change < -10) {
            cov_h79tewk8j().b[11][0]++;
            cov_h79tewk8j().s[58]++;
            trend = 'improving';
          } else {
            cov_h79tewk8j().b[11][1]++;
          }
        }
      } else {
        cov_h79tewk8j().b[9][1]++;
      }
      cov_h79tewk8j().s[59]++;
      return {
        timestamps: timestamps,
        values: values,
        average: average,
        trend: trend
      };
    }
  }, {
    key: "exportPerformanceData",
    value: (function () {
      var _exportPerformanceData = _asyncToGenerator(function* () {
        cov_h79tewk8j().f[17]++;
        var report = (cov_h79tewk8j().s[60]++, yield this.getCurrentReport());
        cov_h79tewk8j().s[61]++;
        return {
          report: report,
          rawMetrics: Object.fromEntries(this.metrics),
          alerts: this.alerts,
          config: this.config
        };
      });
      function exportPerformanceData() {
        return _exportPerformanceData.apply(this, arguments);
      }
      return exportPerformanceData;
    }())
  }, {
    key: "collectMetrics",
    value: function () {
      var _collectMetrics = _asyncToGenerator(function* () {
        cov_h79tewk8j().f[18]++;
        var timestamp = (cov_h79tewk8j().s[62]++, Date.now());
        var newMetrics = (cov_h79tewk8j().s[63]++, []);
        cov_h79tewk8j().s[64]++;
        try {
          var renderMetrics = (cov_h79tewk8j().s[65]++, this.collectRenderMetrics(timestamp));
          cov_h79tewk8j().s[66]++;
          newMetrics.push.apply(newMetrics, _toConsumableArray(renderMetrics));
          var networkMetrics = (cov_h79tewk8j().s[67]++, this.collectNetworkMetrics(timestamp));
          cov_h79tewk8j().s[68]++;
          newMetrics.push.apply(newMetrics, _toConsumableArray(networkMetrics));
          var memoryMetrics = (cov_h79tewk8j().s[69]++, this.collectMemoryMetrics(timestamp));
          cov_h79tewk8j().s[70]++;
          newMetrics.push.apply(newMetrics, _toConsumableArray(memoryMetrics));
          var cacheMetrics = (cov_h79tewk8j().s[71]++, yield this.collectCacheMetrics(timestamp));
          cov_h79tewk8j().s[72]++;
          newMetrics.push.apply(newMetrics, _toConsumableArray(cacheMetrics));
          var bundleMetrics = (cov_h79tewk8j().s[73]++, yield this.collectBundleMetrics(timestamp));
          cov_h79tewk8j().s[74]++;
          newMetrics.push.apply(newMetrics, _toConsumableArray(bundleMetrics));
          cov_h79tewk8j().s[75]++;
          this.storeMetrics(newMetrics);
          cov_h79tewk8j().s[76]++;
          if (this.config.enableAlerts) {
            cov_h79tewk8j().b[12][0]++;
            cov_h79tewk8j().s[77]++;
            this.checkAlerts(newMetrics);
          } else {
            cov_h79tewk8j().b[12][1]++;
          }
          var report = (cov_h79tewk8j().s[78]++, this.generateReport());
          cov_h79tewk8j().s[79]++;
          this.notifyListeners(report);
          cov_h79tewk8j().s[80]++;
          if (this.config.enableAutoOptimization) {
            cov_h79tewk8j().b[13][0]++;
            cov_h79tewk8j().s[81]++;
            yield this.performAutoOptimization(newMetrics);
          } else {
            cov_h79tewk8j().b[13][1]++;
          }
        } catch (error) {
          cov_h79tewk8j().s[82]++;
          console.error('Failed to collect performance metrics:', error);
        }
      });
      function collectMetrics() {
        return _collectMetrics.apply(this, arguments);
      }
      return collectMetrics;
    }()
  }, {
    key: "collectRenderMetrics",
    value: function collectRenderMetrics(timestamp) {
      cov_h79tewk8j().f[19]++;
      cov_h79tewk8j().s[83]++;
      return [{
        name: 'averageRenderTime',
        value: Math.random() * 20 + 5,
        unit: 'ms',
        timestamp: timestamp,
        category: 'render',
        severity: 'info',
        threshold: this.config.alertThresholds.renderTime
      }, {
        name: 'frameDrops',
        value: Math.floor(Math.random() * 5),
        unit: 'count',
        timestamp: timestamp,
        category: 'render',
        severity: 'info'
      }];
    }
  }, {
    key: "collectNetworkMetrics",
    value: function collectNetworkMetrics(timestamp) {
      cov_h79tewk8j().f[20]++;
      cov_h79tewk8j().s[84]++;
      return [{
        name: 'networkLatency',
        value: Math.random() * 2000 + 100,
        unit: 'ms',
        timestamp: timestamp,
        category: 'network',
        severity: 'info',
        threshold: this.config.alertThresholds.networkLatency
      }, {
        name: 'requestsPerSecond',
        value: Math.random() * 10 + 1,
        unit: 'req/s',
        timestamp: timestamp,
        category: 'network',
        severity: 'info'
      }];
    }
  }, {
    key: "collectMemoryMetrics",
    value: function collectMemoryMetrics(timestamp) {
      cov_h79tewk8j().f[21]++;
      cov_h79tewk8j().s[85]++;
      return [{
        name: 'memoryUsage',
        value: Math.random() * 40 + 40,
        unit: '%',
        timestamp: timestamp,
        category: 'memory',
        severity: 'info',
        threshold: this.config.alertThresholds.memoryUsage
      }];
    }
  }, {
    key: "collectCacheMetrics",
    value: function () {
      var _collectCacheMetrics = _asyncToGenerator(function* (timestamp) {
        cov_h79tewk8j().f[22]++;
        var cacheStats = (cov_h79tewk8j().s[86]++, advancedCacheManager.getStats());
        cov_h79tewk8j().s[87]++;
        return [{
          name: 'cacheHitRate',
          value: cacheStats.hitRate * 100,
          unit: '%',
          timestamp: timestamp,
          category: 'cache',
          severity: 'info',
          threshold: this.config.alertThresholds.cacheHitRate
        }, {
          name: 'cacheSize',
          value: cacheStats.memoryUsage,
          unit: 'bytes',
          timestamp: timestamp,
          category: 'cache',
          severity: 'info'
        }];
      });
      function collectCacheMetrics(_x) {
        return _collectCacheMetrics.apply(this, arguments);
      }
      return collectCacheMetrics;
    }()
  }, {
    key: "collectBundleMetrics",
    value: function () {
      var _collectBundleMetrics = _asyncToGenerator(function* (timestamp) {
        cov_h79tewk8j().f[23]++;
        cov_h79tewk8j().s[88]++;
        try {
          var _bundleReport$bundleS, _bundleReport$perform;
          var bundleReport = (cov_h79tewk8j().s[89]++, yield bundleAnalysisService.getPerformanceReport());
          cov_h79tewk8j().s[90]++;
          return [{
            name: 'bundleSize',
            value: bundleReport != null && (_bundleReport$bundleS = bundleReport.bundleSize) != null && _bundleReport$bundleS.total ? (cov_h79tewk8j().b[14][0]++, parseInt(bundleReport.bundleSize.total.replace(/[^\d]/g, '')) * 1024) : (cov_h79tewk8j().b[14][1]++, 0),
            unit: 'bytes',
            timestamp: timestamp,
            category: 'bundle',
            severity: 'info',
            threshold: this.config.alertThresholds.bundleSize
          }, {
            name: 'loadTime',
            value: (cov_h79tewk8j().b[15][0]++, bundleReport == null || (_bundleReport$perform = bundleReport.performance) == null ? void 0 : _bundleReport$perform.totalLoadTime) || (cov_h79tewk8j().b[15][1]++, 0),
            unit: 'ms',
            timestamp: timestamp,
            category: 'bundle',
            severity: 'info'
          }];
        } catch (error) {
          cov_h79tewk8j().s[91]++;
          console.warn('Failed to collect bundle metrics:', error);
          cov_h79tewk8j().s[92]++;
          return [];
        }
      });
      function collectBundleMetrics(_x2) {
        return _collectBundleMetrics.apply(this, arguments);
      }
      return collectBundleMetrics;
    }()
  }, {
    key: "storeMetrics",
    value: function storeMetrics(newMetrics) {
      var _this2 = this;
      cov_h79tewk8j().f[24]++;
      cov_h79tewk8j().s[93]++;
      newMetrics.forEach(function (metric) {
        cov_h79tewk8j().f[25]++;
        cov_h79tewk8j().s[94]++;
        if (!_this2.metrics.has(metric.name)) {
          cov_h79tewk8j().b[16][0]++;
          cov_h79tewk8j().s[95]++;
          _this2.metrics.set(metric.name, []);
        } else {
          cov_h79tewk8j().b[16][1]++;
        }
        var metricHistory = (cov_h79tewk8j().s[96]++, _this2.metrics.get(metric.name));
        cov_h79tewk8j().s[97]++;
        metricHistory.push(metric);
        cov_h79tewk8j().s[98]++;
        if (metricHistory.length > _this2.config.maxHistorySize) {
          cov_h79tewk8j().b[17][0]++;
          cov_h79tewk8j().s[99]++;
          metricHistory.shift();
        } else {
          cov_h79tewk8j().b[17][1]++;
        }
      });
    }
  }, {
    key: "checkAlerts",
    value: function checkAlerts(metrics) {
      var _this3 = this;
      cov_h79tewk8j().f[26]++;
      cov_h79tewk8j().s[100]++;
      metrics.forEach(function (metric) {
        cov_h79tewk8j().f[27]++;
        cov_h79tewk8j().s[101]++;
        if (!metric.threshold) {
          cov_h79tewk8j().b[18][0]++;
          cov_h79tewk8j().s[102]++;
          return;
        } else {
          cov_h79tewk8j().b[18][1]++;
        }
        var severity = (cov_h79tewk8j().s[103]++, null);
        cov_h79tewk8j().s[104]++;
        if (metric.value >= metric.threshold.critical) {
          cov_h79tewk8j().b[19][0]++;
          cov_h79tewk8j().s[105]++;
          severity = 'critical';
        } else {
          cov_h79tewk8j().b[19][1]++;
          cov_h79tewk8j().s[106]++;
          if (metric.value >= metric.threshold.warning) {
            cov_h79tewk8j().b[20][0]++;
            cov_h79tewk8j().s[107]++;
            severity = 'warning';
          } else {
            cov_h79tewk8j().b[20][1]++;
          }
        }
        cov_h79tewk8j().s[108]++;
        if (severity) {
          cov_h79tewk8j().b[21][0]++;
          var alert = (cov_h79tewk8j().s[109]++, {
            id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            metric: metric.name,
            severity: severity,
            message: `${metric.name} is ${severity}: ${metric.value}${metric.unit}`,
            timestamp: metric.timestamp,
            acknowledged: false,
            suggestions: _this3.generateAlertSuggestions(metric, severity)
          });
          cov_h79tewk8j().s[110]++;
          _this3.alerts.unshift(alert);
          cov_h79tewk8j().s[111]++;
          if (_this3.alerts.length > 50) {
            cov_h79tewk8j().b[22][0]++;
            cov_h79tewk8j().s[112]++;
            _this3.alerts = _this3.alerts.slice(0, 50);
          } else {
            cov_h79tewk8j().b[22][1]++;
          }
        } else {
          cov_h79tewk8j().b[21][1]++;
        }
      });
    }
  }, {
    key: "generateAlertSuggestions",
    value: function generateAlertSuggestions(metric, severity) {
      cov_h79tewk8j().f[28]++;
      var suggestions = (cov_h79tewk8j().s[113]++, []);
      cov_h79tewk8j().s[114]++;
      switch (metric.name) {
        case 'averageRenderTime':
          cov_h79tewk8j().b[23][0]++;
          cov_h79tewk8j().s[115]++;
          suggestions.push('Optimize component re-renders with React.memo');
          cov_h79tewk8j().s[116]++;
          suggestions.push('Use useCallback and useMemo for expensive operations');
          cov_h79tewk8j().s[117]++;
          break;
        case 'networkLatency':
          cov_h79tewk8j().b[23][1]++;
          cov_h79tewk8j().s[118]++;
          suggestions.push('Implement request caching');
          cov_h79tewk8j().s[119]++;
          suggestions.push('Use request batching for multiple API calls');
          cov_h79tewk8j().s[120]++;
          break;
        case 'memoryUsage':
          cov_h79tewk8j().b[23][2]++;
          cov_h79tewk8j().s[121]++;
          suggestions.push('Check for memory leaks in components');
          cov_h79tewk8j().s[122]++;
          suggestions.push('Optimize image loading and caching');
          cov_h79tewk8j().s[123]++;
          break;
        case 'cacheHitRate':
          cov_h79tewk8j().b[23][3]++;
          cov_h79tewk8j().s[124]++;
          suggestions.push('Increase cache TTL for stable data');
          cov_h79tewk8j().s[125]++;
          suggestions.push('Implement cache warming strategies');
          cov_h79tewk8j().s[126]++;
          break;
      }
      cov_h79tewk8j().s[127]++;
      return suggestions;
    }
  }, {
    key: "generateReport",
    value: function generateReport() {
      cov_h79tewk8j().f[29]++;
      var timestamp = (cov_h79tewk8j().s[128]++, Date.now());
      var allMetrics = (cov_h79tewk8j().s[129]++, Array.from(this.metrics.values()).flat());
      var recentMetrics = (cov_h79tewk8j().s[130]++, allMetrics.filter(function (m) {
        cov_h79tewk8j().f[30]++;
        cov_h79tewk8j().s[131]++;
        return timestamp - m.timestamp < 60000;
      }));
      var overallScore = (cov_h79tewk8j().s[132]++, this.calculateOverallScore(recentMetrics));
      var trends = (cov_h79tewk8j().s[133]++, {
        renderPerformance: this.getMetricValues('averageRenderTime', 10),
        networkLatency: this.getMetricValues('networkLatency', 10),
        memoryUsage: this.getMetricValues('memoryUsage', 10),
        cacheHitRate: this.getMetricValues('cacheHitRate', 10)
      });
      var recommendations = (cov_h79tewk8j().s[134]++, this.generateRecommendations(recentMetrics));
      cov_h79tewk8j().s[135]++;
      return {
        timestamp: timestamp,
        overallScore: overallScore,
        metrics: recentMetrics,
        alerts: this.alerts.filter(function (a) {
          cov_h79tewk8j().f[31]++;
          cov_h79tewk8j().s[136]++;
          return !a.acknowledged;
        }).slice(0, 10),
        trends: trends,
        recommendations: recommendations
      };
    }
  }, {
    key: "calculateOverallScore",
    value: function calculateOverallScore(metrics) {
      cov_h79tewk8j().f[32]++;
      cov_h79tewk8j().s[137]++;
      if (metrics.length === 0) {
        cov_h79tewk8j().b[24][0]++;
        cov_h79tewk8j().s[138]++;
        return 100;
      } else {
        cov_h79tewk8j().b[24][1]++;
      }
      var score = (cov_h79tewk8j().s[139]++, 100);
      var weights = (cov_h79tewk8j().s[140]++, {
        render: 0.3,
        network: 0.25,
        memory: 0.2,
        cache: 0.15,
        bundle: 0.1
      });
      cov_h79tewk8j().s[141]++;
      Object.entries(weights).forEach(function (_ref) {
        var _ref2 = _slicedToArray(_ref, 2),
          category = _ref2[0],
          weight = _ref2[1];
        cov_h79tewk8j().f[33]++;
        var categoryMetrics = (cov_h79tewk8j().s[142]++, metrics.filter(function (m) {
          cov_h79tewk8j().f[34]++;
          cov_h79tewk8j().s[143]++;
          return m.category === category;
        }));
        cov_h79tewk8j().s[144]++;
        if (categoryMetrics.length === 0) {
          cov_h79tewk8j().b[25][0]++;
          cov_h79tewk8j().s[145]++;
          return;
        } else {
          cov_h79tewk8j().b[25][1]++;
        }
        var categoryScore = (cov_h79tewk8j().s[146]++, categoryMetrics.reduce(function (sum, metric) {
          cov_h79tewk8j().f[35]++;
          cov_h79tewk8j().s[147]++;
          if (!metric.threshold) {
            cov_h79tewk8j().b[26][0]++;
            cov_h79tewk8j().s[148]++;
            return sum + 100;
          } else {
            cov_h79tewk8j().b[26][1]++;
          }
          var normalizedValue = (cov_h79tewk8j().s[149]++, Math.min(metric.value / metric.threshold.critical, 1));
          cov_h79tewk8j().s[150]++;
          return sum + (100 - normalizedValue * 100);
        }, 0) / categoryMetrics.length);
        cov_h79tewk8j().s[151]++;
        score -= (100 - categoryScore) * weight;
      });
      cov_h79tewk8j().s[152]++;
      return Math.max(0, Math.round(score));
    }
  }, {
    key: "getMetricValues",
    value: function getMetricValues(metricName, count) {
      cov_h79tewk8j().f[36]++;
      var metricHistory = (cov_h79tewk8j().s[153]++, (cov_h79tewk8j().b[27][0]++, this.metrics.get(metricName)) || (cov_h79tewk8j().b[27][1]++, []));
      cov_h79tewk8j().s[154]++;
      return metricHistory.slice(-count).map(function (m) {
        cov_h79tewk8j().f[37]++;
        cov_h79tewk8j().s[155]++;
        return m.value;
      });
    }
  }, {
    key: "generateRecommendations",
    value: function generateRecommendations(metrics) {
      cov_h79tewk8j().f[38]++;
      var recommendations = (cov_h79tewk8j().s[156]++, []);
      var slowRenders = (cov_h79tewk8j().s[157]++, metrics.filter(function (m) {
        cov_h79tewk8j().f[39]++;
        cov_h79tewk8j().s[158]++;
        return (cov_h79tewk8j().b[28][0]++, m.name === 'averageRenderTime') && (cov_h79tewk8j().b[28][1]++, m.value > 16);
      }));
      cov_h79tewk8j().s[159]++;
      if (slowRenders.length > 0) {
        cov_h79tewk8j().b[29][0]++;
        cov_h79tewk8j().s[160]++;
        recommendations.push({
          priority: 'high',
          category: 'Render Performance',
          description: 'Optimize component rendering',
          impact: 'Improve user experience and responsiveness',
          implementation: 'Use React.memo, useCallback, and useMemo'
        });
      } else {
        cov_h79tewk8j().b[29][1]++;
      }
      cov_h79tewk8j().s[161]++;
      return recommendations;
    }
  }, {
    key: "performAutoOptimization",
    value: function () {
      var _performAutoOptimization = _asyncToGenerator(function* (metrics) {
        cov_h79tewk8j().f[40]++;
        cov_h79tewk8j().s[162]++;
        console.log('Auto-optimization would be performed here');
      });
      function performAutoOptimization(_x3) {
        return _performAutoOptimization.apply(this, arguments);
      }
      return performAutoOptimization;
    }()
  }, {
    key: "notifyListeners",
    value: function notifyListeners(report) {
      cov_h79tewk8j().f[41]++;
      cov_h79tewk8j().s[163]++;
      this.listeners.forEach(function (listener) {
        cov_h79tewk8j().f[42]++;
        cov_h79tewk8j().s[164]++;
        try {
          cov_h79tewk8j().s[165]++;
          listener(report);
        } catch (error) {
          cov_h79tewk8j().s[166]++;
          console.error('Performance monitoring listener error:', error);
        }
      });
    }
  }]);
}();
export var realTimePerformanceMonitor = (cov_h79tewk8j().s[167]++, new RealTimePerformanceMonitor());
export default realTimePerformanceMonitor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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