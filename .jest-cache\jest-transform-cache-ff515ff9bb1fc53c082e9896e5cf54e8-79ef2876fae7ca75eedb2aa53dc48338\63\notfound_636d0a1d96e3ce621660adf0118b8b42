a1b5effa0e12dd3ee3aae841629bff28
function cov_gza4pauxk() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\+not-found.tsx";
  var hash = "48956adedda2efe9c3d864b48e84ab93288bee80";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\+not-found.tsx",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 2
        },
        end: {
          line: 15,
          column: 4
        }
      },
      "1": {
        start: {
          line: 18,
          column: 15
        },
        end: {
          line: 33,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "NotFoundScreen",
        decl: {
          start: {
            line: 4,
            column: 24
          },
          end: {
            line: 4,
            column: 38
          }
        },
        loc: {
          start: {
            line: 4,
            column: 41
          },
          end: {
            line: 16,
            column: 1
          }
        },
        line: 4
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0
    },
    f: {
      "0": 0
    },
    b: {},
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "48956adedda2efe9c3d864b48e84ab93288bee80"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_gza4pauxk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_gza4pauxk();
import { Link, Stack } from 'expo-router';
import { StyleSheet, Text, View } from 'react-native';
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
export default function NotFoundScreen() {
  cov_gza4pauxk().f[0]++;
  cov_gza4pauxk().s[0]++;
  return _jsxs(_Fragment, {
    children: [_jsx(Stack.Screen, {
      options: {
        title: 'Oops!'
      }
    }), _jsxs(View, {
      style: styles.container,
      children: [_jsx(Text, {
        style: styles.text,
        children: "This screen doesn't exist."
      }), _jsx(Link, {
        href: "/",
        style: styles.link,
        children: _jsx(Text, {
          children: "Go to home screen!"
        })
      })]
    })]
  });
}
var styles = (cov_gza4pauxk().s[1]++, StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20
  },
  text: {
    fontSize: 20,
    fontWeight: 600
  },
  link: {
    marginTop: 15,
    paddingVertical: 15
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJMaW5rIiwiU3RhY2siLCJTdHlsZVNoZWV0IiwiVGV4dCIsIlZpZXciLCJqc3giLCJfanN4IiwianN4cyIsIl9qc3hzIiwiRnJhZ21lbnQiLCJfRnJhZ21lbnQiLCJOb3RGb3VuZFNjcmVlbiIsImNvdl9nemE0cGF1eGsiLCJmIiwicyIsImNoaWxkcmVuIiwiU2NyZWVuIiwib3B0aW9ucyIsInRpdGxlIiwic3R5bGUiLCJzdHlsZXMiLCJjb250YWluZXIiLCJ0ZXh0IiwiaHJlZiIsImxpbmsiLCJjcmVhdGUiLCJmbGV4IiwiYWxpZ25JdGVtcyIsImp1c3RpZnlDb250ZW50IiwicGFkZGluZyIsImZvbnRTaXplIiwiZm9udFdlaWdodCIsIm1hcmdpblRvcCIsInBhZGRpbmdWZXJ0aWNhbCJdLCJzb3VyY2VzIjpbIitub3QtZm91bmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IExpbmssIFN0YWNrIH0gZnJvbSAnZXhwby1yb3V0ZXInO1xuaW1wb3J0IHsgU3R5bGVTaGVldCwgVGV4dCwgVmlldyB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kU2NyZWVuKCkge1xuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8U3RhY2suU2NyZWVuIG9wdGlvbnM9e3sgdGl0bGU6ICdPb3BzIScgfX0gLz5cbiAgICAgIDxWaWV3IHN0eWxlPXtzdHlsZXMuY29udGFpbmVyfT5cbiAgICAgICAgPFRleHQgc3R5bGU9e3N0eWxlcy50ZXh0fT5UaGlzIHNjcmVlbiBkb2Vzbid0IGV4aXN0LjwvVGV4dD5cbiAgICAgICAgPExpbmsgaHJlZj1cIi9cIiBzdHlsZT17c3R5bGVzLmxpbmt9PlxuICAgICAgICAgIDxUZXh0PkdvIHRvIGhvbWUgc2NyZWVuITwvVGV4dD5cbiAgICAgICAgPC9MaW5rPlxuICAgICAgPC9WaWV3PlxuICAgIDwvPlxuICApO1xufVxuXG5jb25zdCBzdHlsZXMgPSBTdHlsZVNoZWV0LmNyZWF0ZSh7XG4gIGNvbnRhaW5lcjoge1xuICAgIGZsZXg6IDEsXG4gICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXG4gICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxuICAgIHBhZGRpbmc6IDIwLFxuICB9LFxuICB0ZXh0OiB7XG4gICAgZm9udFNpemU6IDIwLFxuICAgIGZvbnRXZWlnaHQ6IDYwMCxcbiAgfSxcbiAgbGluazoge1xuICAgIG1hcmdpblRvcDogMTUsXG4gICAgcGFkZGluZ1ZlcnRpY2FsOiAxNSxcbiAgfSxcbn0pO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLFNBQVNBLElBQUksRUFBRUMsS0FBSyxRQUFRLGFBQWE7QUFDekMsU0FBU0MsVUFBVSxFQUFFQyxJQUFJLEVBQUVDLElBQUksUUFBUSxjQUFjO0FBQUMsU0FBQUMsR0FBQSxJQUFBQyxJQUFBLEVBQUFDLElBQUEsSUFBQUMsS0FBQSxFQUFBQyxRQUFBLElBQUFDLFNBQUE7QUFFdEQsZUFBZSxTQUFTQyxjQUFjQSxDQUFBLEVBQUc7RUFBQUMsYUFBQSxHQUFBQyxDQUFBO0VBQUFELGFBQUEsR0FBQUUsQ0FBQTtFQUN2QyxPQUNFTixLQUFBLENBQUFFLFNBQUE7SUFBQUssUUFBQSxHQUNFVCxJQUFBLENBQUNMLEtBQUssQ0FBQ2UsTUFBTTtNQUFDQyxPQUFPLEVBQUU7UUFBRUMsS0FBSyxFQUFFO01BQVE7SUFBRSxDQUFFLENBQUMsRUFDN0NWLEtBQUEsQ0FBQ0osSUFBSTtNQUFDZSxLQUFLLEVBQUVDLE1BQU0sQ0FBQ0MsU0FBVTtNQUFBTixRQUFBLEdBQzVCVCxJQUFBLENBQUNILElBQUk7UUFBQ2dCLEtBQUssRUFBRUMsTUFBTSxDQUFDRSxJQUFLO1FBQUFQLFFBQUEsRUFBQztNQUEwQixDQUFNLENBQUMsRUFDM0RULElBQUEsQ0FBQ04sSUFBSTtRQUFDdUIsSUFBSSxFQUFDLEdBQUc7UUFBQ0osS0FBSyxFQUFFQyxNQUFNLENBQUNJLElBQUs7UUFBQVQsUUFBQSxFQUNoQ1QsSUFBQSxDQUFDSCxJQUFJO1VBQUFZLFFBQUEsRUFBQztRQUFrQixDQUFNO01BQUMsQ0FDM0IsQ0FBQztJQUFBLENBQ0gsQ0FBQztFQUFBLENBQ1AsQ0FBQztBQUVQO0FBRUEsSUFBTUssTUFBTSxJQUFBUixhQUFBLEdBQUFFLENBQUEsT0FBR1osVUFBVSxDQUFDdUIsTUFBTSxDQUFDO0VBQy9CSixTQUFTLEVBQUU7SUFDVEssSUFBSSxFQUFFLENBQUM7SUFDUEMsVUFBVSxFQUFFLFFBQVE7SUFDcEJDLGNBQWMsRUFBRSxRQUFRO0lBQ3hCQyxPQUFPLEVBQUU7RUFDWCxDQUFDO0VBQ0RQLElBQUksRUFBRTtJQUNKUSxRQUFRLEVBQUUsRUFBRTtJQUNaQyxVQUFVLEVBQUU7RUFDZCxDQUFDO0VBQ0RQLElBQUksRUFBRTtJQUNKUSxTQUFTLEVBQUUsRUFBRTtJQUNiQyxlQUFlLEVBQUU7RUFDbkI7QUFDRixDQUFDLENBQUMiLCJpZ25vcmVMaXN0IjpbXX0=