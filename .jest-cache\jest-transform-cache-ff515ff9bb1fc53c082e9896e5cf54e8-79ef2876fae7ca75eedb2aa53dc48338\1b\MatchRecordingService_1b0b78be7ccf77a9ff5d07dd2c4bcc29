a9659936a02227df24e1a6e7fdf978fb
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2k9ta5zj0b() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\match\\MatchRecordingService.ts";
  var hash = "a74cddf9dd12a1d0ea03499a43d630188d9966fd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\match\\MatchRecordingService.ts",
    statementMap: {
      "0": {
        start: {
          line: 30,
          column: 48
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "1": {
        start: {
          line: 31,
          column: 73
        },
        end: {
          line: 31,
          column: 75
        }
      },
      "2": {
        start: {
          line: 32,
          column: 60
        },
        end: {
          line: 32,
          column: 62
        }
      },
      "3": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 115,
          column: 5
        }
      },
      "4": {
        start: {
          line: 42,
          column: 6
        },
        end: {
          line: 42,
          column: 56
        }
      },
      "5": {
        start: {
          line: 45,
          column: 6
        },
        end: {
          line: 45,
          column: 43
        }
      },
      "6": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 50,
          column: 7
        }
      },
      "7": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 74
        }
      },
      "8": {
        start: {
          line: 53,
          column: 45
        },
        end: {
          line: 64,
          column: 7
        }
      },
      "9": {
        start: {
          line: 67,
          column: 36
        },
        end: {
          line: 79,
          column: 7
        }
      },
      "10": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 84,
          column: 7
        }
      },
      "11": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 72
        }
      },
      "12": {
        start: {
          line: 87,
          column: 25
        },
        end: {
          line: 87,
          column: 71
        }
      },
      "13": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 90,
          column: 7
        }
      },
      "14": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 80
        }
      },
      "15": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 45
        }
      },
      "16": {
        start: {
          line: 93,
          column: 6
        },
        end: {
          line: 93,
          column: 61
        }
      },
      "17": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 46
        }
      },
      "18": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 98,
          column: 36
        }
      },
      "19": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 36
        }
      },
      "20": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 27
        }
      },
      "21": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 54
        }
      },
      "22": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 21
        }
      },
      "23": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 63
        }
      },
      "24": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 112,
          column: 7
        }
      },
      "25": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 111,
          column: 42
        }
      },
      "26": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 18
        }
      },
      "27": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "28": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 49
        }
      },
      "29": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 193,
          column: 5
        }
      },
      "30": {
        start: {
          line: 132,
          column: 22
        },
        end: {
          line: 132,
          column: 41
        }
      },
      "31": {
        start: {
          line: 133,
          column: 25
        },
        end: {
          line: 133,
          column: 43
        }
      },
      "32": {
        start: {
          line: 134,
          column: 26
        },
        end: {
          line: 134,
          column: 45
        }
      },
      "33": {
        start: {
          line: 137,
          column: 35
        },
        end: {
          line: 145,
          column: 7
        }
      },
      "34": {
        start: {
          line: 148,
          column: 27
        },
        end: {
          line: 154,
          column: 7
        }
      },
      "35": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 65
        }
      },
      "36": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 41
        }
      },
      "37": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 161,
          column: 57
        }
      },
      "38": {
        start: {
          line: 164,
          column: 26
        },
        end: {
          line: 164,
          column: 79
        }
      },
      "39": {
        start: {
          line: 165,
          column: 28
        },
        end: {
          line: 165,
          column: 98
        }
      },
      "40": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 179,
          column: 7
        }
      },
      "41": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 29
        }
      },
      "42": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 32
        }
      },
      "43": {
        start: {
          line: 170,
          column: 13
        },
        end: {
          line: 179,
          column: 7
        }
      },
      "44": {
        start: {
          line: 172,
          column: 29
        },
        end: {
          line: 175,
          column: 9
        }
      },
      "45": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 178,
          column: 9
        }
      },
      "46": {
        start: {
          line: 177,
          column: 10
        },
        end: {
          line: 177,
          column: 32
        }
      },
      "47": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 186,
          column: 7
        }
      },
      "48": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 30
        }
      },
      "49": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 56
        }
      },
      "50": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 34
        }
      },
      "51": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 36
        }
      },
      "52": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 191,
          column: 51
        }
      },
      "53": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 18
        }
      },
      "54": {
        start: {
          line: 200,
          column: 4
        },
        end: {
          line: 202,
          column: 5
        }
      },
      "55": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 13
        }
      },
      "56": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "57": {
        start: {
          line: 205,
          column: 6
        },
        end: {
          line: 205,
          column: 42
        }
      },
      "58": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 206,
          column: 50
        }
      },
      "59": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 207,
          column: 50
        }
      },
      "60": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 212,
          column: 7
        }
      },
      "61": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 211,
          column: 53
        }
      },
      "62": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 66
        }
      },
      "63": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 36
        }
      },
      "64": {
        start: {
          line: 217,
          column: 6
        },
        end: {
          line: 217,
          column: 53
        }
      },
      "65": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 18
        }
      },
      "66": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "67": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 13
        }
      },
      "68": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 247,
          column: 5
        }
      },
      "69": {
        start: {
          line: 231,
          column: 28
        },
        end: {
          line: 231,
          column: 71
        }
      },
      "70": {
        start: {
          line: 232,
          column: 6
        },
        end: {
          line: 232,
          column: 63
        }
      },
      "71": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 233,
          column: 43
        }
      },
      "72": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 41
        }
      },
      "73": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 235,
          column: 53
        }
      },
      "74": {
        start: {
          line: 238,
          column: 6
        },
        end: {
          line: 240,
          column: 7
        }
      },
      "75": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 239,
          column: 54
        }
      },
      "76": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 66
        }
      },
      "77": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 243,
          column: 36
        }
      },
      "78": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 54
        }
      },
      "79": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 246,
          column: 18
        }
      },
      "80": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 256,
          column: 5
        }
      },
      "81": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 49
        }
      },
      "82": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 316,
          column: 5
        }
      },
      "83": {
        start: {
          line: 259,
          column: 6
        },
        end: {
          line: 259,
          column: 54
        }
      },
      "84": {
        start: {
          line: 261,
          column: 22
        },
        end: {
          line: 261,
          column: 41
        }
      },
      "85": {
        start: {
          line: 262,
          column: 22
        },
        end: {
          line: 262,
          column: 32
        }
      },
      "86": {
        start: {
          line: 263,
          column: 28
        },
        end: {
          line: 263,
          column: 99
        }
      },
      "87": {
        start: {
          line: 266,
          column: 6
        },
        end: {
          line: 266,
          column: 64
        }
      },
      "88": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 73
        }
      },
      "89": {
        start: {
          line: 268,
          column: 6
        },
        end: {
          line: 268,
          column: 41
        }
      },
      "90": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 299,
          column: 7
        }
      },
      "91": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 272,
          column: 71
        }
      },
      "92": {
        start: {
          line: 275,
          column: 29
        },
        end: {
          line: 277,
          column: 10
        }
      },
      "93": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 298,
          column: 9
        }
      },
      "94": {
        start: {
          line: 280,
          column: 10
        },
        end: {
          line: 280,
          column: 57
        }
      },
      "95": {
        start: {
          line: 281,
          column: 10
        },
        end: {
          line: 281,
          column: 68
        }
      },
      "96": {
        start: {
          line: 282,
          column: 10
        },
        end: {
          line: 282,
          column: 68
        }
      },
      "97": {
        start: {
          line: 285,
          column: 10
        },
        end: {
          line: 297,
          column: 11
        }
      },
      "98": {
        start: {
          line: 286,
          column: 36
        },
        end: {
          line: 292,
          column: 13
        }
      },
      "99": {
        start: {
          line: 294,
          column: 12
        },
        end: {
          line: 296,
          column: 13
        }
      },
      "100": {
        start: {
          line: 295,
          column: 14
        },
        end: {
          line: 295,
          column: 73
        }
      },
      "101": {
        start: {
          line: 302,
          column: 6
        },
        end: {
          line: 302,
          column: 83
        }
      },
      "102": {
        start: {
          line: 305,
          column: 25
        },
        end: {
          line: 305,
          column: 72
        }
      },
      "103": {
        start: {
          line: 308,
          column: 6
        },
        end: {
          line: 308,
          column: 33
        }
      },
      "104": {
        start: {
          line: 309,
          column: 6
        },
        end: {
          line: 309,
          column: 36
        }
      },
      "105": {
        start: {
          line: 311,
          column: 6
        },
        end: {
          line: 311,
          column: 52
        }
      },
      "106": {
        start: {
          line: 312,
          column: 6
        },
        end: {
          line: 312,
          column: 24
        }
      },
      "107": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 314,
          column: 51
        }
      },
      "108": {
        start: {
          line: 315,
          column: 6
        },
        end: {
          line: 315,
          column: 18
        }
      },
      "109": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 325,
          column: 5
        }
      },
      "110": {
        start: {
          line: 324,
          column: 6
        },
        end: {
          line: 324,
          column: 13
        }
      },
      "111": {
        start: {
          line: 327,
          column: 4
        },
        end: {
          line: 343,
          column: 5
        }
      },
      "112": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 331,
          column: 7
        }
      },
      "113": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 330,
          column: 52
        }
      },
      "114": {
        start: {
          line: 334,
          column: 6
        },
        end: {
          line: 334,
          column: 53
        }
      },
      "115": {
        start: {
          line: 335,
          column: 6
        },
        end: {
          line: 335,
          column: 66
        }
      },
      "116": {
        start: {
          line: 338,
          column: 6
        },
        end: {
          line: 338,
          column: 33
        }
      },
      "117": {
        start: {
          line: 339,
          column: 6
        },
        end: {
          line: 339,
          column: 36
        }
      },
      "118": {
        start: {
          line: 341,
          column: 6
        },
        end: {
          line: 341,
          column: 54
        }
      },
      "119": {
        start: {
          line: 342,
          column: 6
        },
        end: {
          line: 342,
          column: 18
        }
      },
      "120": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 350,
          column: 31
        }
      },
      "121": {
        start: {
          line: 357,
          column: 4
        },
        end: {
          line: 357,
          column: 41
        }
      },
      "122": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 364,
          column: 78
        }
      },
      "123": {
        start: {
          line: 364,
          column: 62
        },
        end: {
          line: 364,
          column: 76
        }
      },
      "124": {
        start: {
          line: 371,
          column: 4
        },
        end: {
          line: 371,
          column: 39
        }
      },
      "125": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 74
        }
      },
      "126": {
        start: {
          line: 378,
          column: 58
        },
        end: {
          line: 378,
          column: 72
        }
      },
      "127": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 386,
          column: 5
        }
      },
      "128": {
        start: {
          line: 385,
          column: 6
        },
        end: {
          line: 385,
          column: 51
        }
      },
      "129": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 389,
          column: 5
        }
      },
      "130": {
        start: {
          line: 388,
          column: 6
        },
        end: {
          line: 388,
          column: 45
        }
      },
      "131": {
        start: {
          line: 390,
          column: 4
        },
        end: {
          line: 392,
          column: 5
        }
      },
      "132": {
        start: {
          line: 391,
          column: 6
        },
        end: {
          line: 391,
          column: 48
        }
      },
      "133": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 395,
          column: 5
        }
      },
      "134": {
        start: {
          line: 394,
          column: 6
        },
        end: {
          line: 394,
          column: 50
        }
      },
      "135": {
        start: {
          line: 396,
          column: 4
        },
        end: {
          line: 398,
          column: 5
        }
      },
      "136": {
        start: {
          line: 397,
          column: 6
        },
        end: {
          line: 397,
          column: 51
        }
      },
      "137": {
        start: {
          line: 402,
          column: 20
        },
        end: {
          line: 402,
          column: 50
        }
      },
      "138": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 409,
          column: 6
        }
      },
      "139": {
        start: {
          line: 413,
          column: 4
        },
        end: {
          line: 437,
          column: 6
        }
      },
      "140": {
        start: {
          line: 449,
          column: 25
        },
        end: {
          line: 449,
          column: 44
        }
      },
      "141": {
        start: {
          line: 452,
          column: 4
        },
        end: {
          line: 460,
          column: 5
        }
      },
      "142": {
        start: {
          line: 453,
          column: 6
        },
        end: {
          line: 459,
          column: 9
        }
      },
      "143": {
        start: {
          line: 462,
          column: 23
        },
        end: {
          line: 462,
          column: 55
        }
      },
      "144": {
        start: {
          line: 465,
          column: 4
        },
        end: {
          line: 470,
          column: 5
        }
      },
      "145": {
        start: {
          line: 472,
          column: 4
        },
        end: {
          line: 472,
          column: 24
        }
      },
      "146": {
        start: {
          line: 476,
          column: 4
        },
        end: {
          line: 476,
          column: 35
        }
      },
      "147": {
        start: {
          line: 478,
          column: 4
        },
        end: {
          line: 480,
          column: 5
        }
      },
      "148": {
        start: {
          line: 479,
          column: 6
        },
        end: {
          line: 479,
          column: 34
        }
      },
      "149": {
        start: {
          line: 482,
          column: 4
        },
        end: {
          line: 498,
          column: 5
        }
      },
      "150": {
        start: {
          line: 484,
          column: 8
        },
        end: {
          line: 484,
          column: 26
        }
      },
      "151": {
        start: {
          line: 485,
          column: 8
        },
        end: {
          line: 485,
          column: 14
        }
      },
      "152": {
        start: {
          line: 487,
          column: 8
        },
        end: {
          line: 487,
          column: 34
        }
      },
      "153": {
        start: {
          line: 488,
          column: 8
        },
        end: {
          line: 488,
          column: 14
        }
      },
      "154": {
        start: {
          line: 490,
          column: 8
        },
        end: {
          line: 490,
          column: 29
        }
      },
      "155": {
        start: {
          line: 491,
          column: 8
        },
        end: {
          line: 491,
          column: 14
        }
      },
      "156": {
        start: {
          line: 493,
          column: 8
        },
        end: {
          line: 493,
          column: 36
        }
      },
      "157": {
        start: {
          line: 494,
          column: 8
        },
        end: {
          line: 494,
          column: 14
        }
      },
      "158": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 496,
          column: 34
        }
      },
      "159": {
        start: {
          line: 497,
          column: 8
        },
        end: {
          line: 497,
          column: 14
        }
      },
      "160": {
        start: {
          line: 503,
          column: 4
        },
        end: {
          line: 505,
          column: 26
        }
      },
      "161": {
        start: {
          line: 510,
          column: 4
        },
        end: {
          line: 510,
          column: 16
        }
      },
      "162": {
        start: {
          line: 514,
          column: 22
        },
        end: {
          line: 514,
          column: 52
        }
      },
      "163": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 515,
          column: 69
        }
      },
      "164": {
        start: {
          line: 520,
          column: 4
        },
        end: {
          line: 522,
          column: 5
        }
      },
      "165": {
        start: {
          line: 521,
          column: 6
        },
        end: {
          line: 521,
          column: 107
        }
      },
      "166": {
        start: {
          line: 524,
          column: 4
        },
        end: {
          line: 526,
          column: 5
        }
      },
      "167": {
        start: {
          line: 525,
          column: 6
        },
        end: {
          line: 525,
          column: 114
        }
      },
      "168": {
        start: {
          line: 528,
          column: 4
        },
        end: {
          line: 530,
          column: 5
        }
      },
      "169": {
        start: {
          line: 529,
          column: 6
        },
        end: {
          line: 529,
          column: 98
        }
      },
      "170": {
        start: {
          line: 534,
          column: 4
        },
        end: {
          line: 587,
          column: 5
        }
      },
      "171": {
        start: {
          line: 536,
          column: 24
        },
        end: {
          line: 554,
          column: 7
        }
      },
      "172": {
        start: {
          line: 557,
          column: 21
        },
        end: {
          line: 557,
          column: 22
        }
      },
      "173": {
        start: {
          line: 558,
          column: 26
        },
        end: {
          line: 558,
          column: 27
        }
      },
      "174": {
        start: {
          line: 560,
          column: 6
        },
        end: {
          line: 581,
          column: 7
        }
      },
      "175": {
        start: {
          line: 561,
          column: 8
        },
        end: {
          line: 580,
          column: 9
        }
      },
      "176": {
        start: {
          line: 562,
          column: 25
        },
        end: {
          line: 562,
          column: 69
        }
      },
      "177": {
        start: {
          line: 564,
          column: 10
        },
        end: {
          line: 571,
          column: 11
        }
      },
      "178": {
        start: {
          line: 565,
          column: 12
        },
        end: {
          line: 567,
          column: 13
        }
      },
      "179": {
        start: {
          line: 566,
          column: 14
        },
        end: {
          line: 566,
          column: 61
        }
      },
      "180": {
        start: {
          line: 568,
          column: 12
        },
        end: {
          line: 568,
          column: 23
        }
      },
      "181": {
        start: {
          line: 569,
          column: 12
        },
        end: {
          line: 569,
          column: 79
        }
      },
      "182": {
        start: {
          line: 569,
          column: 41
        },
        end: {
          line: 569,
          column: 77
        }
      },
      "183": {
        start: {
          line: 570,
          column: 12
        },
        end: {
          line: 570,
          column: 21
        }
      },
      "184": {
        start: {
          line: 573,
          column: 10
        },
        end: {
          line: 573,
          column: 88
        }
      },
      "185": {
        start: {
          line: 575,
          column: 10
        },
        end: {
          line: 575,
          column: 21
        }
      },
      "186": {
        start: {
          line: 576,
          column: 10
        },
        end: {
          line: 578,
          column: 11
        }
      },
      "187": {
        start: {
          line: 577,
          column: 12
        },
        end: {
          line: 577,
          column: 24
        }
      },
      "188": {
        start: {
          line: 579,
          column: 10
        },
        end: {
          line: 579,
          column: 77
        }
      },
      "189": {
        start: {
          line: 579,
          column: 39
        },
        end: {
          line: 579,
          column: 75
        }
      },
      "190": {
        start: {
          line: 583,
          column: 6
        },
        end: {
          line: 583,
          column: 81
        }
      },
      "191": {
        start: {
          line: 585,
          column: 6
        },
        end: {
          line: 585,
          column: 62
        }
      },
      "192": {
        start: {
          line: 586,
          column: 6
        },
        end: {
          line: 586,
          column: 69
        }
      },
      "193": {
        start: {
          line: 591,
          column: 4
        },
        end: {
          line: 625,
          column: 5
        }
      },
      "194": {
        start: {
          line: 592,
          column: 6
        },
        end: {
          line: 594,
          column: 7
        }
      },
      "195": {
        start: {
          line: 593,
          column: 8
        },
        end: {
          line: 593,
          column: 76
        }
      },
      "196": {
        start: {
          line: 596,
          column: 25
        },
        end: {
          line: 601,
          column: 7
        }
      },
      "197": {
        start: {
          line: 604,
          column: 6
        },
        end: {
          line: 613,
          column: 7
        }
      },
      "198": {
        start: {
          line: 605,
          column: 8
        },
        end: {
          line: 605,
          column: 92
        }
      },
      "199": {
        start: {
          line: 606,
          column: 8
        },
        end: {
          line: 608,
          column: 10
        }
      },
      "200": {
        start: {
          line: 609,
          column: 8
        },
        end: {
          line: 609,
          column: 76
        }
      },
      "201": {
        start: {
          line: 610,
          column: 8
        },
        end: {
          line: 610,
          column: 90
        }
      },
      "202": {
        start: {
          line: 611,
          column: 8
        },
        end: {
          line: 611,
          column: 50
        }
      },
      "203": {
        start: {
          line: 612,
          column: 8
        },
        end: {
          line: 612,
          column: 52
        }
      },
      "204": {
        start: {
          line: 615,
          column: 21
        },
        end: {
          line: 615,
          column: 76
        }
      },
      "205": {
        start: {
          line: 617,
          column: 6
        },
        end: {
          line: 619,
          column: 7
        }
      },
      "206": {
        start: {
          line: 618,
          column: 8
        },
        end: {
          line: 618,
          column: 55
        }
      },
      "207": {
        start: {
          line: 621,
          column: 6
        },
        end: {
          line: 621,
          column: 31
        }
      },
      "208": {
        start: {
          line: 623,
          column: 6
        },
        end: {
          line: 623,
          column: 64
        }
      },
      "209": {
        start: {
          line: 624,
          column: 6
        },
        end: {
          line: 624,
          column: 69
        }
      },
      "210": {
        start: {
          line: 632,
          column: 4
        },
        end: {
          line: 634,
          column: 5
        }
      },
      "211": {
        start: {
          line: 633,
          column: 6
        },
        end: {
          line: 633,
          column: 19
        }
      },
      "212": {
        start: {
          line: 636,
          column: 4
        },
        end: {
          line: 638,
          column: 18
        }
      },
      "213": {
        start: {
          line: 637,
          column: 18
        },
        end: {
          line: 637,
          column: 57
        }
      },
      "214": {
        start: {
          line: 645,
          column: 4
        },
        end: {
          line: 649,
          column: 5
        }
      },
      "215": {
        start: {
          line: 646,
          column: 6
        },
        end: {
          line: 646,
          column: 19
        }
      },
      "216": {
        start: {
          line: 647,
          column: 11
        },
        end: {
          line: 649,
          column: 5
        }
      },
      "217": {
        start: {
          line: 648,
          column: 6
        },
        end: {
          line: 648,
          column: 20
        }
      },
      "218": {
        start: {
          line: 650,
          column: 4
        },
        end: {
          line: 650,
          column: 18
        }
      },
      "219": {
        start: {
          line: 654,
          column: 4
        },
        end: {
          line: 654,
          column: 78
        }
      },
      "220": {
        start: {
          line: 658,
          column: 4
        },
        end: {
          line: 658,
          column: 76
        }
      },
      "221": {
        start: {
          line: 662,
          column: 4
        },
        end: {
          line: 662,
          column: 77
        }
      },
      "222": {
        start: {
          line: 662,
          column: 46
        },
        end: {
          line: 662,
          column: 75
        }
      },
      "223": {
        start: {
          line: 666,
          column: 4
        },
        end: {
          line: 668,
          column: 5
        }
      },
      "224": {
        start: {
          line: 667,
          column: 6
        },
        end: {
          line: 667,
          column: 90
        }
      },
      "225": {
        start: {
          line: 667,
          column: 46
        },
        end: {
          line: 667,
          column: 88
        }
      },
      "226": {
        start: {
          line: 673,
          column: 37
        },
        end: {
          line: 673,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 37,
            column: 2
          },
          end: {
            line: 37,
            column: 3
          }
        },
        loc: {
          start: {
            line: 40,
            column: 27
          },
          end: {
            line: 116,
            column: 3
          }
        },
        line: 40
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 121,
            column: 3
          }
        },
        loc: {
          start: {
            line: 126,
            column: 19
          },
          end: {
            line: 194,
            column: 3
          }
        },
        line: 126
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 199,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        },
        loc: {
          start: {
            line: 199,
            column: 36
          },
          end: {
            line: 220,
            column: 3
          }
        },
        line: 199
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 225,
            column: 2
          },
          end: {
            line: 225,
            column: 3
          }
        },
        loc: {
          start: {
            line: 225,
            column: 37
          },
          end: {
            line: 248,
            column: 3
          }
        },
        line: 225
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 253,
            column: 2
          },
          end: {
            line: 253,
            column: 3
          }
        },
        loc: {
          start: {
            line: 253,
            column: 44
          },
          end: {
            line: 317,
            column: 3
          }
        },
        line: 253
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 322,
            column: 2
          },
          end: {
            line: 322,
            column: 3
          }
        },
        loc: {
          start: {
            line: 322,
            column: 37
          },
          end: {
            line: 344,
            column: 3
          }
        },
        line: 322
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 349,
            column: 2
          },
          end: {
            line: 349,
            column: 3
          }
        },
        loc: {
          start: {
            line: 349,
            column: 43
          },
          end: {
            line: 351,
            column: 3
          }
        },
        line: 349
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 356,
            column: 2
          },
          end: {
            line: 356,
            column: 3
          }
        },
        loc: {
          start: {
            line: 356,
            column: 77
          },
          end: {
            line: 358,
            column: 3
          }
        },
        line: 356
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 363,
            column: 2
          },
          end: {
            line: 363,
            column: 3
          }
        },
        loc: {
          start: {
            line: 363,
            column: 80
          },
          end: {
            line: 365,
            column: 3
          }
        },
        line: 363
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 364,
            column: 57
          },
          end: {
            line: 364,
            column: 58
          }
        },
        loc: {
          start: {
            line: 364,
            column: 62
          },
          end: {
            line: 364,
            column: 76
          }
        },
        line: 364
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 370,
            column: 2
          },
          end: {
            line: 370,
            column: 3
          }
        },
        loc: {
          start: {
            line: 370,
            column: 64
          },
          end: {
            line: 372,
            column: 3
          }
        },
        line: 370
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 377,
            column: 2
          },
          end: {
            line: 377,
            column: 3
          }
        },
        loc: {
          start: {
            line: 377,
            column: 67
          },
          end: {
            line: 379,
            column: 3
          }
        },
        line: 377
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 378,
            column: 53
          },
          end: {
            line: 378,
            column: 54
          }
        },
        loc: {
          start: {
            line: 378,
            column: 58
          },
          end: {
            line: 378,
            column: 72
          }
        },
        line: 378
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 383,
            column: 2
          },
          end: {
            line: 383,
            column: 3
          }
        },
        loc: {
          start: {
            line: 383,
            column: 63
          },
          end: {
            line: 399,
            column: 3
          }
        },
        line: 383
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 401,
            column: 2
          },
          end: {
            line: 401,
            column: 3
          }
        },
        loc: {
          start: {
            line: 401,
            column: 54
          },
          end: {
            line: 410,
            column: 3
          }
        },
        line: 401
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 412,
            column: 2
          },
          end: {
            line: 412,
            column: 3
          }
        },
        loc: {
          start: {
            line: 412,
            column: 64
          },
          end: {
            line: 438,
            column: 3
          }
        },
        line: 412
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 440,
            column: 2
          },
          end: {
            line: 440,
            column: 3
          }
        },
        loc: {
          start: {
            line: 446,
            column: 16
          },
          end: {
            line: 473,
            column: 3
          }
        },
        line: 446
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 475,
            column: 2
          },
          end: {
            line: 475,
            column: 3
          }
        },
        loc: {
          start: {
            line: 475,
            column: 80
          },
          end: {
            line: 499,
            column: 3
          }
        },
        line: 475
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 501,
            column: 2
          },
          end: {
            line: 501,
            column: 3
          }
        },
        loc: {
          start: {
            line: 501,
            column: 48
          },
          end: {
            line: 506,
            column: 3
          }
        },
        line: 501
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 508,
            column: 2
          },
          end: {
            line: 508,
            column: 3
          }
        },
        loc: {
          start: {
            line: 508,
            column: 69
          },
          end: {
            line: 511,
            column: 3
          }
        },
        line: 508
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 513,
            column: 2
          },
          end: {
            line: 513,
            column: 3
          }
        },
        loc: {
          start: {
            line: 513,
            column: 70
          },
          end: {
            line: 516,
            column: 3
          }
        },
        line: 513
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 518,
            column: 2
          },
          end: {
            line: 518,
            column: 3
          }
        },
        loc: {
          start: {
            line: 518,
            column: 89
          },
          end: {
            line: 531,
            column: 3
          }
        },
        line: 518
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 533,
            column: 2
          },
          end: {
            line: 533,
            column: 3
          }
        },
        loc: {
          start: {
            line: 533,
            column: 118
          },
          end: {
            line: 588,
            column: 3
          }
        },
        line: 533
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 569,
            column: 30
          },
          end: {
            line: 569,
            column: 31
          }
        },
        loc: {
          start: {
            line: 569,
            column: 41
          },
          end: {
            line: 569,
            column: 77
          }
        },
        line: 569
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 579,
            column: 28
          },
          end: {
            line: 579,
            column: 29
          }
        },
        loc: {
          start: {
            line: 579,
            column: 39
          },
          end: {
            line: 579,
            column: 75
          }
        },
        line: 579
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 590,
            column: 2
          },
          end: {
            line: 590,
            column: 3
          }
        },
        loc: {
          start: {
            line: 590,
            column: 108
          },
          end: {
            line: 626,
            column: 3
          }
        },
        line: 590
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 631,
            column: 2
          },
          end: {
            line: 631,
            column: 3
          }
        },
        loc: {
          start: {
            line: 631,
            column: 62
          },
          end: {
            line: 639,
            column: 3
          }
        },
        line: 631
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 637,
            column: 11
          },
          end: {
            line: 637,
            column: 12
          }
        },
        loc: {
          start: {
            line: 637,
            column: 18
          },
          end: {
            line: 637,
            column: 57
          }
        },
        line: 637
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 644,
            column: 2
          },
          end: {
            line: 644,
            column: 3
          }
        },
        loc: {
          start: {
            line: 644,
            column: 91
          },
          end: {
            line: 651,
            column: 3
          }
        },
        line: 644
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 653,
            column: 2
          },
          end: {
            line: 653,
            column: 3
          }
        },
        loc: {
          start: {
            line: 653,
            column: 38
          },
          end: {
            line: 655,
            column: 3
          }
        },
        line: 653
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 657,
            column: 2
          },
          end: {
            line: 657,
            column: 3
          }
        },
        loc: {
          start: {
            line: 657,
            column: 36
          },
          end: {
            line: 659,
            column: 3
          }
        },
        line: 657
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 661,
            column: 2
          },
          end: {
            line: 661,
            column: 3
          }
        },
        loc: {
          start: {
            line: 661,
            column: 41
          },
          end: {
            line: 663,
            column: 3
          }
        },
        line: 661
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 662,
            column: 34
          },
          end: {
            line: 662,
            column: 35
          }
        },
        loc: {
          start: {
            line: 662,
            column: 46
          },
          end: {
            line: 662,
            column: 75
          }
        },
        line: 662
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 665,
            column: 2
          },
          end: {
            line: 665,
            column: 3
          }
        },
        loc: {
          start: {
            line: 665,
            column: 39
          },
          end: {
            line: 669,
            column: 3
          }
        },
        line: 665
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 667,
            column: 34
          },
          end: {
            line: 667,
            column: 35
          }
        },
        loc: {
          start: {
            line: 667,
            column: 46
          },
          end: {
            line: 667,
            column: 88
          }
        },
        line: 667
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 48,
            column: 6
          },
          end: {
            line: 50,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 6
          },
          end: {
            line: 50,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "1": {
        loc: {
          start: {
            line: 82,
            column: 6
          },
          end: {
            line: 84,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 6
          },
          end: {
            line: 84,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "2": {
        loc: {
          start: {
            line: 88,
            column: 6
          },
          end: {
            line: 90,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 88,
            column: 6
          },
          end: {
            line: 90,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 88
      },
      "3": {
        loc: {
          start: {
            line: 89,
            column: 24
          },
          end: {
            line: 89,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 89,
            column: 24
          },
          end: {
            line: 89,
            column: 40
          }
        }, {
          start: {
            line: 89,
            column: 44
          },
          end: {
            line: 89,
            column: 78
          }
        }],
        line: 89
      },
      "4": {
        loc: {
          start: {
            line: 110,
            column: 6
          },
          end: {
            line: 112,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 6
          },
          end: {
            line: 112,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "5": {
        loc: {
          start: {
            line: 123,
            column: 4
          },
          end: {
            line: 123,
            column: 89
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 123,
            column: 81
          },
          end: {
            line: 123,
            column: 89
          }
        }],
        line: 123
      },
      "6": {
        loc: {
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "7": {
        loc: {
          start: {
            line: 140,
            column: 19
          },
          end: {
            line: 140,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 140,
            column: 44
          },
          end: {
            line: 140,
            column: 55
          }
        }, {
          start: {
            line: 140,
            column: 58
          },
          end: {
            line: 140,
            column: 67
          }
        }],
        line: 140
      },
      "8": {
        loc: {
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 179,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 179,
            column: 7
          }
        }, {
          start: {
            line: 170,
            column: 13
          },
          end: {
            line: 179,
            column: 7
          }
        }],
        line: 167
      },
      "9": {
        loc: {
          start: {
            line: 167,
            column: 10
          },
          end: {
            line: 167,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 10
          },
          end: {
            line: 167,
            column: 21
          }
        }, {
          start: {
            line: 167,
            column: 25
          },
          end: {
            line: 167,
            column: 39
          }
        }],
        line: 167
      },
      "10": {
        loc: {
          start: {
            line: 170,
            column: 13
          },
          end: {
            line: 179,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 13
          },
          end: {
            line: 179,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "11": {
        loc: {
          start: {
            line: 176,
            column: 8
          },
          end: {
            line: 178,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 8
          },
          end: {
            line: 178,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "12": {
        loc: {
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 186,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 186,
            column: 7
          }
        }, {
          start: {
            line: 183,
            column: 13
          },
          end: {
            line: 186,
            column: 7
          }
        }],
        line: 181
      },
      "13": {
        loc: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "14": {
        loc: {
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 200,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 200,
            column: 28
          }
        }, {
          start: {
            line: 200,
            column: 32
          },
          end: {
            line: 200,
            column: 60
          }
        }],
        line: 200
      },
      "15": {
        loc: {
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "16": {
        loc: {
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 228,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 228,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "17": {
        loc: {
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 226,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 226,
            column: 28
          }
        }, {
          start: {
            line: 226,
            column: 32
          },
          end: {
            line: 226,
            column: 61
          }
        }],
        line: 226
      },
      "18": {
        loc: {
          start: {
            line: 238,
            column: 6
          },
          end: {
            line: 240,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 6
          },
          end: {
            line: 240,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      },
      "19": {
        loc: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "20": {
        loc: {
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "21": {
        loc: {
          start: {
            line: 276,
            column: 29
          },
          end: {
            line: 276,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 276,
            column: 29
          },
          end: {
            line: 276,
            column: 45
          }
        }, {
          start: {
            line: 276,
            column: 49
          },
          end: {
            line: 276,
            column: 55
          }
        }],
        line: 276
      },
      "22": {
        loc: {
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 298,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 298,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "23": {
        loc: {
          start: {
            line: 285,
            column: 10
          },
          end: {
            line: 297,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 285,
            column: 10
          },
          end: {
            line: 297,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 285
      },
      "24": {
        loc: {
          start: {
            line: 290,
            column: 35
          },
          end: {
            line: 290,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 35
          },
          end: {
            line: 290,
            column: 51
          }
        }, {
          start: {
            line: 290,
            column: 55
          },
          end: {
            line: 290,
            column: 61
          }
        }],
        line: 290
      },
      "25": {
        loc: {
          start: {
            line: 294,
            column: 12
          },
          end: {
            line: 296,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 294,
            column: 12
          },
          end: {
            line: 296,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 294
      },
      "26": {
        loc: {
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 325,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 325,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 323
      },
      "27": {
        loc: {
          start: {
            line: 329,
            column: 6
          },
          end: {
            line: 331,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 329,
            column: 6
          },
          end: {
            line: 331,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 329
      },
      "28": {
        loc: {
          start: {
            line: 384,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 384,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 384
      },
      "29": {
        loc: {
          start: {
            line: 387,
            column: 4
          },
          end: {
            line: 389,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 387,
            column: 4
          },
          end: {
            line: 389,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 387
      },
      "30": {
        loc: {
          start: {
            line: 390,
            column: 4
          },
          end: {
            line: 392,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 390,
            column: 4
          },
          end: {
            line: 392,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 390
      },
      "31": {
        loc: {
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 393
      },
      "32": {
        loc: {
          start: {
            line: 396,
            column: 4
          },
          end: {
            line: 398,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 396,
            column: 4
          },
          end: {
            line: 398,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 396
      },
      "33": {
        loc: {
          start: {
            line: 402,
            column: 20
          },
          end: {
            line: 402,
            column: 50
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 402,
            column: 45
          },
          end: {
            line: 402,
            column: 46
          }
        }, {
          start: {
            line: 402,
            column: 49
          },
          end: {
            line: 402,
            column: 50
          }
        }],
        line: 402
      },
      "34": {
        loc: {
          start: {
            line: 465,
            column: 4
          },
          end: {
            line: 470,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 465,
            column: 4
          },
          end: {
            line: 470,
            column: 5
          }
        }, {
          start: {
            line: 468,
            column: 11
          },
          end: {
            line: 470,
            column: 5
          }
        }],
        line: 465
      },
      "35": {
        loc: {
          start: {
            line: 478,
            column: 4
          },
          end: {
            line: 480,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 478,
            column: 4
          },
          end: {
            line: 480,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 478
      },
      "36": {
        loc: {
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 498,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 483,
            column: 6
          },
          end: {
            line: 485,
            column: 14
          }
        }, {
          start: {
            line: 486,
            column: 6
          },
          end: {
            line: 488,
            column: 14
          }
        }, {
          start: {
            line: 489,
            column: 6
          },
          end: {
            line: 491,
            column: 14
          }
        }, {
          start: {
            line: 492,
            column: 6
          },
          end: {
            line: 494,
            column: 14
          }
        }, {
          start: {
            line: 495,
            column: 6
          },
          end: {
            line: 497,
            column: 14
          }
        }],
        line: 482
      },
      "37": {
        loc: {
          start: {
            line: 503,
            column: 11
          },
          end: {
            line: 505,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 503,
            column: 12
          },
          end: {
            line: 503,
            column: 30
          }
        }, {
          start: {
            line: 503,
            column: 34
          },
          end: {
            line: 503,
            column: 72
          }
        }, {
          start: {
            line: 504,
            column: 12
          },
          end: {
            line: 504,
            column: 34
          }
        }, {
          start: {
            line: 504,
            column: 38
          },
          end: {
            line: 504,
            column: 76
          }
        }, {
          start: {
            line: 505,
            column: 11
          },
          end: {
            line: 505,
            column: 25
          }
        }],
        line: 503
      },
      "38": {
        loc: {
          start: {
            line: 514,
            column: 22
          },
          end: {
            line: 514,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 514,
            column: 47
          },
          end: {
            line: 514,
            column: 48
          }
        }, {
          start: {
            line: 514,
            column: 51
          },
          end: {
            line: 514,
            column: 52
          }
        }],
        line: 514
      },
      "39": {
        loc: {
          start: {
            line: 515,
            column: 11
          },
          end: {
            line: 515,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 515,
            column: 11
          },
          end: {
            line: 515,
            column: 37
          }
        }, {
          start: {
            line: 515,
            column: 41
          },
          end: {
            line: 515,
            column: 68
          }
        }],
        line: 515
      },
      "40": {
        loc: {
          start: {
            line: 520,
            column: 4
          },
          end: {
            line: 522,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 520,
            column: 4
          },
          end: {
            line: 522,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 520
      },
      "41": {
        loc: {
          start: {
            line: 524,
            column: 4
          },
          end: {
            line: 526,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 524,
            column: 4
          },
          end: {
            line: 526,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 524
      },
      "42": {
        loc: {
          start: {
            line: 528,
            column: 4
          },
          end: {
            line: 530,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 528,
            column: 4
          },
          end: {
            line: 530,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 528
      },
      "43": {
        loc: {
          start: {
            line: 540,
            column: 20
          },
          end: {
            line: 540,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 540,
            column: 20
          },
          end: {
            line: 540,
            column: 44
          }
        }, {
          start: {
            line: 540,
            column: 48
          },
          end: {
            line: 540,
            column: 58
          }
        }],
        line: 540
      },
      "44": {
        loc: {
          start: {
            line: 564,
            column: 10
          },
          end: {
            line: 571,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 564,
            column: 10
          },
          end: {
            line: 571,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 564
      },
      "45": {
        loc: {
          start: {
            line: 565,
            column: 12
          },
          end: {
            line: 567,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 565,
            column: 12
          },
          end: {
            line: 567,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 565
      },
      "46": {
        loc: {
          start: {
            line: 576,
            column: 10
          },
          end: {
            line: 578,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 576,
            column: 10
          },
          end: {
            line: 578,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 576
      },
      "47": {
        loc: {
          start: {
            line: 592,
            column: 6
          },
          end: {
            line: 594,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 592,
            column: 6
          },
          end: {
            line: 594,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 592
      },
      "48": {
        loc: {
          start: {
            line: 604,
            column: 6
          },
          end: {
            line: 613,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 604,
            column: 6
          },
          end: {
            line: 613,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 604
      },
      "49": {
        loc: {
          start: {
            line: 604,
            column: 10
          },
          end: {
            line: 604,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 604,
            column: 10
          },
          end: {
            line: 604,
            column: 38
          }
        }, {
          start: {
            line: 604,
            column: 42
          },
          end: {
            line: 604,
            column: 64
          }
        }],
        line: 604
      },
      "50": {
        loc: {
          start: {
            line: 617,
            column: 6
          },
          end: {
            line: 619,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 617,
            column: 6
          },
          end: {
            line: 619,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 617
      },
      "51": {
        loc: {
          start: {
            line: 632,
            column: 4
          },
          end: {
            line: 634,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 632,
            column: 4
          },
          end: {
            line: 634,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 632
      },
      "52": {
        loc: {
          start: {
            line: 632,
            column: 8
          },
          end: {
            line: 632,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 632,
            column: 8
          },
          end: {
            line: 632,
            column: 19
          }
        }, {
          start: {
            line: 632,
            column: 23
          },
          end: {
            line: 632,
            column: 46
          }
        }],
        line: 632
      },
      "53": {
        loc: {
          start: {
            line: 645,
            column: 4
          },
          end: {
            line: 649,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 645,
            column: 4
          },
          end: {
            line: 649,
            column: 5
          }
        }, {
          start: {
            line: 647,
            column: 11
          },
          end: {
            line: 649,
            column: 5
          }
        }],
        line: 645
      },
      "54": {
        loc: {
          start: {
            line: 647,
            column: 11
          },
          end: {
            line: 649,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 647,
            column: 11
          },
          end: {
            line: 649,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 647
      },
      "55": {
        loc: {
          start: {
            line: 666,
            column: 4
          },
          end: {
            line: 668,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 666,
            column: 4
          },
          end: {
            line: 668,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 666
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0],
      "37": [0, 0, 0, 0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a74cddf9dd12a1d0ea03499a43d630188d9966fd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2k9ta5zj0b = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2k9ta5zj0b();
import { videoRecordingService } from "../video/VideoRecordingService";
import { matchRepository } from "../database/MatchRepository";
import { fileUploadService } from "../storage/FileUploadService";
import { performanceMonitor } from "../../../utils/performance";
var MatchRecordingService = function () {
  function MatchRecordingService() {
    _classCallCheck(this, MatchRecordingService);
    this.currentSession = (cov_2k9ta5zj0b().s[0]++, null);
    this.sessionListeners = (cov_2k9ta5zj0b().s[1]++, []);
    this.scoreListeners = (cov_2k9ta5zj0b().s[2]++, []);
  }
  return _createClass(MatchRecordingService, [{
    key: "startMatch",
    value: (function () {
      var _startMatch = _asyncToGenerator(function* (metadata, options) {
        cov_2k9ta5zj0b().f[0]++;
        cov_2k9ta5zj0b().s[3]++;
        try {
          cov_2k9ta5zj0b().s[4]++;
          performanceMonitor.start('match_recording_start');
          cov_2k9ta5zj0b().s[5]++;
          this.validateMatchMetadata(metadata);
          cov_2k9ta5zj0b().s[6]++;
          if (this.currentSession) {
            cov_2k9ta5zj0b().b[0][0]++;
            cov_2k9ta5zj0b().s[7]++;
            throw new Error('Another match recording is already in progress');
          } else {
            cov_2k9ta5zj0b().b[0][1]++;
          }
          var matchRecording = (cov_2k9ta5zj0b().s[8]++, {
            id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            metadata: Object.assign({}, metadata, {
              startTime: new Date().toISOString()
            }),
            score: this.initializeScore(metadata.matchFormat),
            statistics: this.initializeStatistics(metadata.userId),
            status: 'recording',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          });
          var session = (cov_2k9ta5zj0b().s[9]++, {
            id: this.generateSessionId(),
            match: matchRecording,
            currentSet: 1,
            currentGame: 1,
            isRecording: true,
            isPaused: false,
            startTime: Date.now(),
            pausedTime: 0,
            totalPausedDuration: 0,
            videoRecordingActive: options.enableVideoRecording,
            autoScoreDetection: options.enableAutoScoreDetection
          });
          cov_2k9ta5zj0b().s[10]++;
          if (options.enableVideoRecording) {
            cov_2k9ta5zj0b().b[1][0]++;
            cov_2k9ta5zj0b().s[11]++;
            yield videoRecordingService.startRecording(options.videoConfig);
          } else {
            cov_2k9ta5zj0b().b[1][1]++;
          }
          var savedMatch = (cov_2k9ta5zj0b().s[12]++, yield this.saveMatchToDatabase(matchRecording));
          cov_2k9ta5zj0b().s[13]++;
          if (!savedMatch.success) {
            cov_2k9ta5zj0b().b[2][0]++;
            cov_2k9ta5zj0b().s[14]++;
            throw new Error((cov_2k9ta5zj0b().b[3][0]++, savedMatch.error) || (cov_2k9ta5zj0b().b[3][1]++, 'Failed to save match to database'));
          } else {
            cov_2k9ta5zj0b().b[2][1]++;
          }
          cov_2k9ta5zj0b().s[15]++;
          session.match.id = savedMatch.data.id;
          cov_2k9ta5zj0b().s[16]++;
          session.match.databaseId = savedMatch.data.databaseId;
          cov_2k9ta5zj0b().s[17]++;
          this.setupOfflineSync(session.match.id);
          cov_2k9ta5zj0b().s[18]++;
          this.currentSession = session;
          cov_2k9ta5zj0b().s[19]++;
          this.notifySessionListeners();
          cov_2k9ta5zj0b().s[20]++;
          this.startAutoSave();
          cov_2k9ta5zj0b().s[21]++;
          performanceMonitor.end('match_recording_start');
          cov_2k9ta5zj0b().s[22]++;
          return session;
        } catch (error) {
          cov_2k9ta5zj0b().s[23]++;
          console.error('Failed to start match recording:', error);
          cov_2k9ta5zj0b().s[24]++;
          if (this.currentSession) {
            cov_2k9ta5zj0b().b[4][0]++;
            cov_2k9ta5zj0b().s[25]++;
            yield this.cleanupFailedSession();
          } else {
            cov_2k9ta5zj0b().b[4][1]++;
          }
          cov_2k9ta5zj0b().s[26]++;
          throw error;
        }
      });
      function startMatch(_x, _x2) {
        return _startMatch.apply(this, arguments);
      }
      return startMatch;
    }())
  }, {
    key: "addPoint",
    value: (function () {
      var _addPoint = _asyncToGenerator(function* (winner) {
        var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2k9ta5zj0b().b[5][0]++, 'normal');
        var shotType = arguments.length > 2 ? arguments[2] : undefined;
        var courtPosition = arguments.length > 3 ? arguments[3] : undefined;
        cov_2k9ta5zj0b().f[1]++;
        cov_2k9ta5zj0b().s[27]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[6][0]++;
          cov_2k9ta5zj0b().s[28]++;
          throw new Error('No active match session');
        } else {
          cov_2k9ta5zj0b().b[6][1]++;
        }
        cov_2k9ta5zj0b().s[29]++;
        try {
          var session = (cov_2k9ta5zj0b().s[30]++, this.currentSession);
          var currentSet = (cov_2k9ta5zj0b().s[31]++, session.currentSet);
          var currentGame = (cov_2k9ta5zj0b().s[32]++, session.currentGame);
          var gameEvent = (cov_2k9ta5zj0b().s[33]++, {
            id: this.generateEventId(),
            timestamp: Date.now(),
            eventType: eventType === 'normal' ? (cov_2k9ta5zj0b().b[7][0]++, 'point_won') : (cov_2k9ta5zj0b().b[7][1]++, eventType),
            player: winner,
            shotType: shotType,
            courtPosition: courtPosition,
            description: `Point won by ${winner}`
          });
          var updatedScore = (cov_2k9ta5zj0b().s[34]++, this.updateScore(session.match.score, currentSet, currentGame, winner, gameEvent));
          cov_2k9ta5zj0b().s[35]++;
          this.updateStatistics(session.match.statistics, gameEvent);
          cov_2k9ta5zj0b().s[36]++;
          session.match.score = updatedScore;
          cov_2k9ta5zj0b().s[37]++;
          session.match.updatedAt = new Date().toISOString();
          var setComplete = (cov_2k9ta5zj0b().s[38]++, this.isSetComplete(updatedScore.sets[currentSet - 1]));
          var matchComplete = (cov_2k9ta5zj0b().s[39]++, this.isMatchComplete(updatedScore, session.match.metadata.matchFormat));
          cov_2k9ta5zj0b().s[40]++;
          if ((cov_2k9ta5zj0b().b[9][0]++, setComplete) && (cov_2k9ta5zj0b().b[9][1]++, !matchComplete)) {
            cov_2k9ta5zj0b().b[8][0]++;
            cov_2k9ta5zj0b().s[41]++;
            session.currentSet++;
            cov_2k9ta5zj0b().s[42]++;
            session.currentGame = 1;
          } else {
            cov_2k9ta5zj0b().b[8][1]++;
            cov_2k9ta5zj0b().s[43]++;
            if (!setComplete) {
              cov_2k9ta5zj0b().b[10][0]++;
              var gameComplete = (cov_2k9ta5zj0b().s[44]++, this.isGameComplete(updatedScore.sets[currentSet - 1], currentGame));
              cov_2k9ta5zj0b().s[45]++;
              if (gameComplete) {
                cov_2k9ta5zj0b().b[11][0]++;
                cov_2k9ta5zj0b().s[46]++;
                session.currentGame++;
              } else {
                cov_2k9ta5zj0b().b[11][1]++;
              }
            } else {
              cov_2k9ta5zj0b().b[10][1]++;
            }
          }
          cov_2k9ta5zj0b().s[47]++;
          if (matchComplete) {
            cov_2k9ta5zj0b().b[12][0]++;
            cov_2k9ta5zj0b().s[48]++;
            yield this.endMatch();
          } else {
            cov_2k9ta5zj0b().b[12][1]++;
            cov_2k9ta5zj0b().s[49]++;
            yield this.updateMatchInDatabase(session.match);
          }
          cov_2k9ta5zj0b().s[50]++;
          this.notifyScoreListeners();
          cov_2k9ta5zj0b().s[51]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[52]++;
          console.error('Failed to add point:', error);
          cov_2k9ta5zj0b().s[53]++;
          throw error;
        }
      });
      function addPoint(_x3) {
        return _addPoint.apply(this, arguments);
      }
      return addPoint;
    }())
  }, {
    key: "pauseMatch",
    value: (function () {
      var _pauseMatch = _asyncToGenerator(function* () {
        cov_2k9ta5zj0b().f[2]++;
        cov_2k9ta5zj0b().s[54]++;
        if ((cov_2k9ta5zj0b().b[14][0]++, !this.currentSession) || (cov_2k9ta5zj0b().b[14][1]++, this.currentSession.isPaused)) {
          cov_2k9ta5zj0b().b[13][0]++;
          cov_2k9ta5zj0b().s[55]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[13][1]++;
        }
        cov_2k9ta5zj0b().s[56]++;
        try {
          cov_2k9ta5zj0b().s[57]++;
          this.currentSession.isPaused = true;
          cov_2k9ta5zj0b().s[58]++;
          this.currentSession.pausedTime = Date.now();
          cov_2k9ta5zj0b().s[59]++;
          this.currentSession.match.status = 'paused';
          cov_2k9ta5zj0b().s[60]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[15][0]++;
            cov_2k9ta5zj0b().s[61]++;
            yield videoRecordingService.pauseRecording();
          } else {
            cov_2k9ta5zj0b().b[15][1]++;
          }
          cov_2k9ta5zj0b().s[62]++;
          yield this.updateMatchInDatabase(this.currentSession.match);
          cov_2k9ta5zj0b().s[63]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[64]++;
          console.error('Failed to pause match:', error);
          cov_2k9ta5zj0b().s[65]++;
          throw error;
        }
      });
      function pauseMatch() {
        return _pauseMatch.apply(this, arguments);
      }
      return pauseMatch;
    }())
  }, {
    key: "resumeMatch",
    value: (function () {
      var _resumeMatch = _asyncToGenerator(function* () {
        cov_2k9ta5zj0b().f[3]++;
        cov_2k9ta5zj0b().s[66]++;
        if ((cov_2k9ta5zj0b().b[17][0]++, !this.currentSession) || (cov_2k9ta5zj0b().b[17][1]++, !this.currentSession.isPaused)) {
          cov_2k9ta5zj0b().b[16][0]++;
          cov_2k9ta5zj0b().s[67]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[16][1]++;
        }
        cov_2k9ta5zj0b().s[68]++;
        try {
          var pauseDuration = (cov_2k9ta5zj0b().s[69]++, Date.now() - this.currentSession.pausedTime);
          cov_2k9ta5zj0b().s[70]++;
          this.currentSession.totalPausedDuration += pauseDuration;
          cov_2k9ta5zj0b().s[71]++;
          this.currentSession.isPaused = false;
          cov_2k9ta5zj0b().s[72]++;
          this.currentSession.pausedTime = 0;
          cov_2k9ta5zj0b().s[73]++;
          this.currentSession.match.status = 'recording';
          cov_2k9ta5zj0b().s[74]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[18][0]++;
            cov_2k9ta5zj0b().s[75]++;
            yield videoRecordingService.resumeRecording();
          } else {
            cov_2k9ta5zj0b().b[18][1]++;
          }
          cov_2k9ta5zj0b().s[76]++;
          yield this.updateMatchInDatabase(this.currentSession.match);
          cov_2k9ta5zj0b().s[77]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[78]++;
          console.error('Failed to resume match:', error);
          cov_2k9ta5zj0b().s[79]++;
          throw error;
        }
      });
      function resumeMatch() {
        return _resumeMatch.apply(this, arguments);
      }
      return resumeMatch;
    }())
  }, {
    key: "endMatch",
    value: (function () {
      var _endMatch = _asyncToGenerator(function* () {
        cov_2k9ta5zj0b().f[4]++;
        cov_2k9ta5zj0b().s[80]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[19][0]++;
          cov_2k9ta5zj0b().s[81]++;
          throw new Error('No active match session');
        } else {
          cov_2k9ta5zj0b().b[19][1]++;
        }
        cov_2k9ta5zj0b().s[82]++;
        try {
          cov_2k9ta5zj0b().s[83]++;
          performanceMonitor.start('match_recording_end');
          var session = (cov_2k9ta5zj0b().s[84]++, this.currentSession);
          var endTime = (cov_2k9ta5zj0b().s[85]++, Date.now());
          var totalDuration = (cov_2k9ta5zj0b().s[86]++, (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60);
          cov_2k9ta5zj0b().s[87]++;
          session.match.metadata.endTime = new Date().toISOString();
          cov_2k9ta5zj0b().s[88]++;
          session.match.metadata.durationMinutes = Math.round(totalDuration);
          cov_2k9ta5zj0b().s[89]++;
          session.match.status = 'completed';
          cov_2k9ta5zj0b().s[90]++;
          if (session.videoRecordingActive) {
            cov_2k9ta5zj0b().b[20][0]++;
            var videoResult = (cov_2k9ta5zj0b().s[91]++, yield videoRecordingService.stopRecording());
            var uploadResult = (cov_2k9ta5zj0b().s[92]++, yield fileUploadService.uploadVideo(videoResult.uri, {
              folder: `matches/${(cov_2k9ta5zj0b().b[21][0]++, session.match.id) || (cov_2k9ta5zj0b().b[21][1]++, 'temp')}`
            }));
            cov_2k9ta5zj0b().s[93]++;
            if (uploadResult.data) {
              cov_2k9ta5zj0b().b[22][0]++;
              cov_2k9ta5zj0b().s[94]++;
              session.match.videoUrl = uploadResult.data.url;
              cov_2k9ta5zj0b().s[95]++;
              session.match.videoDurationSeconds = videoResult.duration;
              cov_2k9ta5zj0b().s[96]++;
              session.match.videoFileSizeBytes = uploadResult.data.size;
              cov_2k9ta5zj0b().s[97]++;
              if (videoResult.thumbnail) {
                cov_2k9ta5zj0b().b[23][0]++;
                var thumbnailResult = (cov_2k9ta5zj0b().s[98]++, yield fileUploadService.uploadThumbnail(videoResult.uri, videoResult.thumbnail, {
                  folder: `matches/${(cov_2k9ta5zj0b().b[24][0]++, session.match.id) || (cov_2k9ta5zj0b().b[24][1]++, 'temp')}/thumbnails`
                }));
                cov_2k9ta5zj0b().s[99]++;
                if (thumbnailResult.data) {
                  cov_2k9ta5zj0b().b[25][0]++;
                  cov_2k9ta5zj0b().s[100]++;
                  session.match.videoThumbnailUrl = thumbnailResult.data.url;
                } else {
                  cov_2k9ta5zj0b().b[25][1]++;
                }
              } else {
                cov_2k9ta5zj0b().b[23][1]++;
              }
            } else {
              cov_2k9ta5zj0b().b[22][1]++;
            }
          } else {
            cov_2k9ta5zj0b().b[20][1]++;
          }
          cov_2k9ta5zj0b().s[101]++;
          this.calculateFinalStatistics(session.match.statistics, session.match.score);
          var finalMatch = (cov_2k9ta5zj0b().s[102]++, yield this.updateMatchInDatabase(session.match));
          cov_2k9ta5zj0b().s[103]++;
          this.currentSession = null;
          cov_2k9ta5zj0b().s[104]++;
          this.notifySessionListeners();
          cov_2k9ta5zj0b().s[105]++;
          performanceMonitor.end('match_recording_end');
          cov_2k9ta5zj0b().s[106]++;
          return finalMatch;
        } catch (error) {
          cov_2k9ta5zj0b().s[107]++;
          console.error('Failed to end match:', error);
          cov_2k9ta5zj0b().s[108]++;
          throw error;
        }
      });
      function endMatch() {
        return _endMatch.apply(this, arguments);
      }
      return endMatch;
    }())
  }, {
    key: "cancelMatch",
    value: (function () {
      var _cancelMatch = _asyncToGenerator(function* () {
        cov_2k9ta5zj0b().f[5]++;
        cov_2k9ta5zj0b().s[109]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[26][0]++;
          cov_2k9ta5zj0b().s[110]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[26][1]++;
        }
        cov_2k9ta5zj0b().s[111]++;
        try {
          cov_2k9ta5zj0b().s[112]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[27][0]++;
            cov_2k9ta5zj0b().s[113]++;
            yield videoRecordingService.stopRecording();
          } else {
            cov_2k9ta5zj0b().b[27][1]++;
          }
          cov_2k9ta5zj0b().s[114]++;
          this.currentSession.match.status = 'cancelled';
          cov_2k9ta5zj0b().s[115]++;
          yield this.updateMatchInDatabase(this.currentSession.match);
          cov_2k9ta5zj0b().s[116]++;
          this.currentSession = null;
          cov_2k9ta5zj0b().s[117]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[118]++;
          console.error('Failed to cancel match:', error);
          cov_2k9ta5zj0b().s[119]++;
          throw error;
        }
      });
      function cancelMatch() {
        return _cancelMatch.apply(this, arguments);
      }
      return cancelMatch;
    }())
  }, {
    key: "getCurrentSession",
    value: function getCurrentSession() {
      cov_2k9ta5zj0b().f[6]++;
      cov_2k9ta5zj0b().s[120]++;
      return this.currentSession;
    }
  }, {
    key: "addSessionListener",
    value: function addSessionListener(listener) {
      cov_2k9ta5zj0b().f[7]++;
      cov_2k9ta5zj0b().s[121]++;
      this.sessionListeners.push(listener);
    }
  }, {
    key: "removeSessionListener",
    value: function removeSessionListener(listener) {
      cov_2k9ta5zj0b().f[8]++;
      cov_2k9ta5zj0b().s[122]++;
      this.sessionListeners = this.sessionListeners.filter(function (l) {
        cov_2k9ta5zj0b().f[9]++;
        cov_2k9ta5zj0b().s[123]++;
        return l !== listener;
      });
    }
  }, {
    key: "addScoreListener",
    value: function addScoreListener(listener) {
      cov_2k9ta5zj0b().f[10]++;
      cov_2k9ta5zj0b().s[124]++;
      this.scoreListeners.push(listener);
    }
  }, {
    key: "removeScoreListener",
    value: function removeScoreListener(listener) {
      cov_2k9ta5zj0b().f[11]++;
      cov_2k9ta5zj0b().s[125]++;
      this.scoreListeners = this.scoreListeners.filter(function (l) {
        cov_2k9ta5zj0b().f[12]++;
        cov_2k9ta5zj0b().s[126]++;
        return l !== listener;
      });
    }
  }, {
    key: "validateMatchMetadata",
    value: function validateMatchMetadata(metadata) {
      var _metadata$opponentNam;
      cov_2k9ta5zj0b().f[13]++;
      cov_2k9ta5zj0b().s[127]++;
      if (!((_metadata$opponentNam = metadata.opponentName) != null && _metadata$opponentNam.trim())) {
        cov_2k9ta5zj0b().b[28][0]++;
        cov_2k9ta5zj0b().s[128]++;
        throw new Error('Opponent name is required');
      } else {
        cov_2k9ta5zj0b().b[28][1]++;
      }
      cov_2k9ta5zj0b().s[129]++;
      if (!metadata.userId) {
        cov_2k9ta5zj0b().b[29][0]++;
        cov_2k9ta5zj0b().s[130]++;
        throw new Error('User ID is required');
      } else {
        cov_2k9ta5zj0b().b[29][1]++;
      }
      cov_2k9ta5zj0b().s[131]++;
      if (!metadata.matchType) {
        cov_2k9ta5zj0b().b[30][0]++;
        cov_2k9ta5zj0b().s[132]++;
        throw new Error('Match type is required');
      } else {
        cov_2k9ta5zj0b().b[30][1]++;
      }
      cov_2k9ta5zj0b().s[133]++;
      if (!metadata.matchFormat) {
        cov_2k9ta5zj0b().b[31][0]++;
        cov_2k9ta5zj0b().s[134]++;
        throw new Error('Match format is required');
      } else {
        cov_2k9ta5zj0b().b[31][1]++;
      }
      cov_2k9ta5zj0b().s[135]++;
      if (!metadata.surface) {
        cov_2k9ta5zj0b().b[32][0]++;
        cov_2k9ta5zj0b().s[136]++;
        throw new Error('Court surface is required');
      } else {
        cov_2k9ta5zj0b().b[32][1]++;
      }
    }
  }, {
    key: "initializeScore",
    value: function initializeScore(format) {
      cov_2k9ta5zj0b().f[14]++;
      var maxSets = (cov_2k9ta5zj0b().s[137]++, format === 'best_of_5' ? (cov_2k9ta5zj0b().b[33][0]++, 5) : (cov_2k9ta5zj0b().b[33][1]++, 3));
      cov_2k9ta5zj0b().s[138]++;
      return {
        sets: [],
        finalScore: '',
        result: 'win',
        setsWon: 0,
        setsLost: 0
      };
    }
  }, {
    key: "initializeStatistics",
    value: function initializeStatistics(userId) {
      cov_2k9ta5zj0b().f[15]++;
      cov_2k9ta5zj0b().s[139]++;
      return {
        matchId: '',
        userId: userId,
        aces: 0,
        doubleFaults: 0,
        firstServesIn: 0,
        firstServesAttempted: 0,
        firstServePointsWon: 0,
        secondServePointsWon: 0,
        firstServeReturnPointsWon: 0,
        secondServeReturnPointsWon: 0,
        breakPointsConverted: 0,
        breakPointsFaced: 0,
        winners: 0,
        unforcedErrors: 0,
        forcedErrors: 0,
        totalPointsWon: 0,
        totalPointsPlayed: 0,
        netPointsAttempted: 0,
        netPointsWon: 0,
        forehandWinners: 0,
        backhandWinners: 0,
        forehandErrors: 0,
        backhandErrors: 0
      };
    }
  }, {
    key: "updateScore",
    value: function updateScore(currentScore, setNumber, gameNumber, winner, event) {
      cov_2k9ta5zj0b().f[16]++;
      var updatedScore = (cov_2k9ta5zj0b().s[140]++, Object.assign({}, currentScore));
      cov_2k9ta5zj0b().s[141]++;
      while (updatedScore.sets.length < setNumber) {
        cov_2k9ta5zj0b().s[142]++;
        updatedScore.sets.push({
          setNumber: updatedScore.sets.length + 1,
          userGames: 0,
          opponentGames: 0,
          isTiebreak: false,
          isCompleted: false
        });
      }
      var currentSet = (cov_2k9ta5zj0b().s[143]++, updatedScore.sets[setNumber - 1]);
      cov_2k9ta5zj0b().s[144]++;
      if (winner === 'user') {
        cov_2k9ta5zj0b().b[34][0]++;
      } else {
        cov_2k9ta5zj0b().b[34][1]++;
      }
      cov_2k9ta5zj0b().s[145]++;
      return updatedScore;
    }
  }, {
    key: "updateStatistics",
    value: function updateStatistics(statistics, event) {
      cov_2k9ta5zj0b().f[17]++;
      cov_2k9ta5zj0b().s[146]++;
      statistics.totalPointsPlayed++;
      cov_2k9ta5zj0b().s[147]++;
      if (event.player === 'user') {
        cov_2k9ta5zj0b().b[35][0]++;
        cov_2k9ta5zj0b().s[148]++;
        statistics.totalPointsWon++;
      } else {
        cov_2k9ta5zj0b().b[35][1]++;
      }
      cov_2k9ta5zj0b().s[149]++;
      switch (event.eventType) {
        case 'ace':
          cov_2k9ta5zj0b().b[36][0]++;
          cov_2k9ta5zj0b().s[150]++;
          statistics.aces++;
          cov_2k9ta5zj0b().s[151]++;
          break;
        case 'double_fault':
          cov_2k9ta5zj0b().b[36][1]++;
          cov_2k9ta5zj0b().s[152]++;
          statistics.doubleFaults++;
          cov_2k9ta5zj0b().s[153]++;
          break;
        case 'winner':
          cov_2k9ta5zj0b().b[36][2]++;
          cov_2k9ta5zj0b().s[154]++;
          statistics.winners++;
          cov_2k9ta5zj0b().s[155]++;
          break;
        case 'unforced_error':
          cov_2k9ta5zj0b().b[36][3]++;
          cov_2k9ta5zj0b().s[156]++;
          statistics.unforcedErrors++;
          cov_2k9ta5zj0b().s[157]++;
          break;
        case 'forced_error':
          cov_2k9ta5zj0b().b[36][4]++;
          cov_2k9ta5zj0b().s[158]++;
          statistics.forcedErrors++;
          cov_2k9ta5zj0b().s[159]++;
          break;
      }
    }
  }, {
    key: "isSetComplete",
    value: function isSetComplete(set) {
      cov_2k9ta5zj0b().f[18]++;
      cov_2k9ta5zj0b().s[160]++;
      return (cov_2k9ta5zj0b().b[37][0]++, set.userGames >= 6) && (cov_2k9ta5zj0b().b[37][1]++, set.userGames - set.opponentGames >= 2) || (cov_2k9ta5zj0b().b[37][2]++, set.opponentGames >= 6) && (cov_2k9ta5zj0b().b[37][3]++, set.opponentGames - set.userGames >= 2) || (cov_2k9ta5zj0b().b[37][4]++, set.isTiebreak);
    }
  }, {
    key: "isGameComplete",
    value: function isGameComplete(set, gameNumber) {
      cov_2k9ta5zj0b().f[19]++;
      cov_2k9ta5zj0b().s[161]++;
      return true;
    }
  }, {
    key: "isMatchComplete",
    value: function isMatchComplete(score, format) {
      cov_2k9ta5zj0b().f[20]++;
      var setsToWin = (cov_2k9ta5zj0b().s[162]++, format === 'best_of_5' ? (cov_2k9ta5zj0b().b[38][0]++, 3) : (cov_2k9ta5zj0b().b[38][1]++, 2));
      cov_2k9ta5zj0b().s[163]++;
      return (cov_2k9ta5zj0b().b[39][0]++, score.setsWon >= setsToWin) || (cov_2k9ta5zj0b().b[39][1]++, score.setsLost >= setsToWin);
    }
  }, {
    key: "calculateFinalStatistics",
    value: function calculateFinalStatistics(statistics, score) {
      cov_2k9ta5zj0b().f[21]++;
      cov_2k9ta5zj0b().s[164]++;
      if (statistics.firstServesAttempted > 0) {
        cov_2k9ta5zj0b().b[40][0]++;
        cov_2k9ta5zj0b().s[165]++;
        statistics.firstServePercentage = statistics.firstServesIn / statistics.firstServesAttempted * 100;
      } else {
        cov_2k9ta5zj0b().b[40][1]++;
      }
      cov_2k9ta5zj0b().s[166]++;
      if (statistics.breakPointsFaced > 0) {
        cov_2k9ta5zj0b().b[41][0]++;
        cov_2k9ta5zj0b().s[167]++;
        statistics.breakPointConversionRate = statistics.breakPointsConverted / statistics.breakPointsFaced * 100;
      } else {
        cov_2k9ta5zj0b().b[41][1]++;
      }
      cov_2k9ta5zj0b().s[168]++;
      if (statistics.netPointsAttempted > 0) {
        cov_2k9ta5zj0b().b[42][0]++;
        cov_2k9ta5zj0b().s[169]++;
        statistics.netSuccessRate = statistics.netPointsWon / statistics.netPointsAttempted * 100;
      } else {
        cov_2k9ta5zj0b().b[42][1]++;
      }
    }
  }, {
    key: "saveMatchToDatabase",
    value: function () {
      var _saveMatchToDatabase = _asyncToGenerator(function* (match) {
        cov_2k9ta5zj0b().f[22]++;
        cov_2k9ta5zj0b().s[170]++;
        try {
          var matchData = (cov_2k9ta5zj0b().s[171]++, {
            id: match.id,
            user_id: match.metadata.userId,
            opponent_name: match.metadata.opponentName,
            match_type: (cov_2k9ta5zj0b().b[43][0]++, match.metadata.matchType) || (cov_2k9ta5zj0b().b[43][1]++, 'friendly'),
            match_format: match.metadata.matchFormat,
            surface: match.metadata.surface,
            location: match.metadata.location,
            court_name: match.metadata.courtName,
            weather_conditions: match.metadata.weather,
            temperature: match.metadata.temperature,
            match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],
            start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],
            status: match.status,
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            created_at: match.createdAt,
            updated_at: match.updatedAt
          });
          var attempts = (cov_2k9ta5zj0b().s[172]++, 0);
          var maxAttempts = (cov_2k9ta5zj0b().s[173]++, 3);
          cov_2k9ta5zj0b().s[174]++;
          while (attempts < maxAttempts) {
            cov_2k9ta5zj0b().s[175]++;
            try {
              var _result$data;
              var result = (cov_2k9ta5zj0b().s[176]++, yield matchRepository.createMatch(matchData));
              cov_2k9ta5zj0b().s[177]++;
              if (result.error) {
                cov_2k9ta5zj0b().b[44][0]++;
                cov_2k9ta5zj0b().s[178]++;
                if (attempts === maxAttempts - 1) {
                  cov_2k9ta5zj0b().b[45][0]++;
                  cov_2k9ta5zj0b().s[179]++;
                  return {
                    success: false,
                    error: result.error
                  };
                } else {
                  cov_2k9ta5zj0b().b[45][1]++;
                }
                cov_2k9ta5zj0b().s[180]++;
                attempts++;
                cov_2k9ta5zj0b().s[181]++;
                yield new Promise(function (resolve) {
                  cov_2k9ta5zj0b().f[23]++;
                  cov_2k9ta5zj0b().s[182]++;
                  return setTimeout(resolve, 1000 * attempts);
                });
                cov_2k9ta5zj0b().s[183]++;
                continue;
              } else {
                cov_2k9ta5zj0b().b[44][1]++;
              }
              cov_2k9ta5zj0b().s[184]++;
              return {
                success: true,
                data: {
                  id: match.id,
                  databaseId: (_result$data = result.data) == null ? void 0 : _result$data.id
                }
              };
            } catch (error) {
              cov_2k9ta5zj0b().s[185]++;
              attempts++;
              cov_2k9ta5zj0b().s[186]++;
              if (attempts === maxAttempts) {
                cov_2k9ta5zj0b().b[46][0]++;
                cov_2k9ta5zj0b().s[187]++;
                throw error;
              } else {
                cov_2k9ta5zj0b().b[46][1]++;
              }
              cov_2k9ta5zj0b().s[188]++;
              yield new Promise(function (resolve) {
                cov_2k9ta5zj0b().f[24]++;
                cov_2k9ta5zj0b().s[189]++;
                return setTimeout(resolve, 1000 * attempts);
              });
            }
          }
          cov_2k9ta5zj0b().s[190]++;
          return {
            success: false,
            error: 'Failed to save after multiple attempts'
          };
        } catch (error) {
          cov_2k9ta5zj0b().s[191]++;
          console.error('Error saving match to database:', error);
          cov_2k9ta5zj0b().s[192]++;
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function saveMatchToDatabase(_x4) {
        return _saveMatchToDatabase.apply(this, arguments);
      }
      return saveMatchToDatabase;
    }()
  }, {
    key: "updateMatchInDatabase",
    value: function () {
      var _updateMatchInDatabase = _asyncToGenerator(function* (match) {
        cov_2k9ta5zj0b().f[25]++;
        cov_2k9ta5zj0b().s[193]++;
        try {
          cov_2k9ta5zj0b().s[194]++;
          if (!match.id) {
            cov_2k9ta5zj0b().b[47][0]++;
            cov_2k9ta5zj0b().s[195]++;
            return {
              success: false,
              error: 'Match ID is required for update'
            };
          } else {
            cov_2k9ta5zj0b().b[47][1]++;
          }
          var updateData = (cov_2k9ta5zj0b().s[196]++, {
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            status: match.status,
            updated_at: new Date().toISOString()
          });
          cov_2k9ta5zj0b().s[197]++;
          if ((cov_2k9ta5zj0b().b[49][0]++, match.status === 'completed') && (cov_2k9ta5zj0b().b[49][1]++, match.metadata.endTime)) {
            cov_2k9ta5zj0b().b[48][0]++;
            cov_2k9ta5zj0b().s[198]++;
            updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];
            cov_2k9ta5zj0b().s[199]++;
            updateData.duration_minutes = Math.round((new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60));
            cov_2k9ta5zj0b().s[200]++;
            updateData.final_score = this.generateFinalScoreString(match.score);
            cov_2k9ta5zj0b().s[201]++;
            updateData.result = this.determineMatchResult(match.score, match.metadata.userId);
            cov_2k9ta5zj0b().s[202]++;
            updateData.sets_won = match.score.setsWon;
            cov_2k9ta5zj0b().s[203]++;
            updateData.sets_lost = match.score.setsLost;
          } else {
            cov_2k9ta5zj0b().b[48][1]++;
          }
          var result = (cov_2k9ta5zj0b().s[204]++, yield matchRepository.updateMatch(match.id, updateData));
          cov_2k9ta5zj0b().s[205]++;
          if (result.error) {
            cov_2k9ta5zj0b().b[50][0]++;
            cov_2k9ta5zj0b().s[206]++;
            return {
              success: false,
              error: result.error
            };
          } else {
            cov_2k9ta5zj0b().b[50][1]++;
          }
          cov_2k9ta5zj0b().s[207]++;
          return {
            success: true
          };
        } catch (error) {
          cov_2k9ta5zj0b().s[208]++;
          console.error('Error updating match in database:', error);
          cov_2k9ta5zj0b().s[209]++;
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function updateMatchInDatabase(_x5) {
        return _updateMatchInDatabase.apply(this, arguments);
      }
      return updateMatchInDatabase;
    }()
  }, {
    key: "generateFinalScoreString",
    value: function generateFinalScoreString(score) {
      cov_2k9ta5zj0b().f[26]++;
      cov_2k9ta5zj0b().s[210]++;
      if ((cov_2k9ta5zj0b().b[52][0]++, !score.sets) || (cov_2k9ta5zj0b().b[52][1]++, score.sets.length === 0)) {
        cov_2k9ta5zj0b().b[51][0]++;
        cov_2k9ta5zj0b().s[211]++;
        return '0-0';
      } else {
        cov_2k9ta5zj0b().b[51][1]++;
      }
      cov_2k9ta5zj0b().s[212]++;
      return score.sets.map(function (set) {
        cov_2k9ta5zj0b().f[27]++;
        cov_2k9ta5zj0b().s[213]++;
        return `${set.userGames}-${set.opponentGames}`;
      }).join(', ');
    }
  }, {
    key: "determineMatchResult",
    value: function determineMatchResult(score, userId) {
      cov_2k9ta5zj0b().f[28]++;
      cov_2k9ta5zj0b().s[214]++;
      if (score.setsWon > score.setsLost) {
        cov_2k9ta5zj0b().b[53][0]++;
        cov_2k9ta5zj0b().s[215]++;
        return 'win';
      } else {
        cov_2k9ta5zj0b().b[53][1]++;
        cov_2k9ta5zj0b().s[216]++;
        if (score.setsLost > score.setsWon) {
          cov_2k9ta5zj0b().b[54][0]++;
          cov_2k9ta5zj0b().s[217]++;
          return 'loss';
        } else {
          cov_2k9ta5zj0b().b[54][1]++;
        }
      }
      cov_2k9ta5zj0b().s[218]++;
      return 'draw';
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      cov_2k9ta5zj0b().f[29]++;
      cov_2k9ta5zj0b().s[219]++;
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateEventId",
    value: function generateEventId() {
      cov_2k9ta5zj0b().f[30]++;
      cov_2k9ta5zj0b().s[220]++;
      return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "notifySessionListeners",
    value: function notifySessionListeners() {
      var _this = this;
      cov_2k9ta5zj0b().f[31]++;
      cov_2k9ta5zj0b().s[221]++;
      this.sessionListeners.forEach(function (listener) {
        cov_2k9ta5zj0b().f[32]++;
        cov_2k9ta5zj0b().s[222]++;
        return listener(_this.currentSession);
      });
    }
  }, {
    key: "notifyScoreListeners",
    value: function notifyScoreListeners() {
      var _this2 = this;
      cov_2k9ta5zj0b().f[33]++;
      cov_2k9ta5zj0b().s[223]++;
      if (this.currentSession) {
        cov_2k9ta5zj0b().b[55][0]++;
        cov_2k9ta5zj0b().s[224]++;
        this.scoreListeners.forEach(function (listener) {
          cov_2k9ta5zj0b().f[34]++;
          cov_2k9ta5zj0b().s[225]++;
          return listener(_this2.currentSession.match.score);
        });
      } else {
        cov_2k9ta5zj0b().b[55][1]++;
      }
    }
  }]);
}();
export var matchRecordingService = (cov_2k9ta5zj0b().s[226]++, new MatchRecordingService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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