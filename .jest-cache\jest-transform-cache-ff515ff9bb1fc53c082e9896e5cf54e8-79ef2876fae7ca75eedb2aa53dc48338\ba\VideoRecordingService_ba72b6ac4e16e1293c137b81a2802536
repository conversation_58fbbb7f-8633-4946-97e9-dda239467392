9c3df5e796a169a14affceee5ada8467
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.videoRecordingService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _expoCamera = require("expo-camera");
var MediaLibrary = _interopRequireWildcard(require("expo-media-library"));
var FileSystem = _interopRequireWildcard(require("expo-file-system"));
var _performance = require("../../../utils/performance");
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function cov_t4rs1xx0t() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\video\\VideoRecordingService.ts";
  var hash = "c2d81470d07194971cd0f212eac41cba598a7be3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\video\\VideoRecordingService.ts",
    statementMap: {
      "0": {
        start: {
          line: 35,
          column: 37
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "1": {
        start: {
          line: 36,
          column: 24
        },
        end: {
          line: 36,
          column: 29
        }
      },
      "2": {
        start: {
          line: 37,
          column: 21
        },
        end: {
          line: 37,
          column: 26
        }
      },
      "3": {
        start: {
          line: 38,
          column: 31
        },
        end: {
          line: 38,
          column: 32
        }
      },
      "4": {
        start: {
          line: 39,
          column: 27
        },
        end: {
          line: 39,
          column: 28
        }
      },
      "5": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 51
        }
      },
      "6": {
        start: {
          line: 41,
          column: 77
        },
        end: {
          line: 41,
          column: 81
        }
      },
      "7": {
        start: {
          line: 42,
          column: 52
        },
        end: {
          line: 42,
          column: 56
        }
      },
      "8": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 60,
          column: 5
        }
      },
      "9": {
        start: {
          line: 49,
          column: 26
        },
        end: {
          line: 49,
          column: 57
        }
      },
      "10": {
        start: {
          line: 50,
          column: 6
        },
        end: {
          line: 52,
          column: 7
        }
      },
      "11": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 94
        }
      },
      "12": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 54,
          column: 53
        }
      },
      "13": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 51
        }
      },
      "14": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 76
        }
      },
      "15": {
        start: {
          line: 59,
          column: 6
        },
        end: {
          line: 59,
          column: 18
        }
      },
      "16": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 84,
          column: 5
        }
      },
      "17": {
        start: {
          line: 68,
          column: 31
        },
        end: {
          line: 68,
          column: 75
        }
      },
      "18": {
        start: {
          line: 69,
          column: 35
        },
        end: {
          line: 69,
          column: 83
        }
      },
      "19": {
        start: {
          line: 70,
          column: 37
        },
        end: {
          line: 70,
          column: 81
        }
      },
      "20": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 76,
          column: 8
        }
      },
      "21": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 61
        }
      },
      "22": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 83,
          column: 8
        }
      },
      "23": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 91,
          column: 25
        }
      },
      "24": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 100,
          column: 5
        }
      },
      "25": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 50
        }
      },
      "26": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 104,
          column: 5
        }
      },
      "27": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 55
        }
      },
      "28": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "29": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 56
        }
      },
      "30": {
        start: {
          line: 109,
          column: 31
        },
        end: {
          line: 109,
          column: 63
        }
      },
      "31": {
        start: {
          line: 111,
          column: 6
        },
        end: {
          line: 111,
          column: 30
        }
      },
      "32": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 112,
          column: 28
        }
      },
      "33": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 113,
          column: 43
        }
      },
      "34": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 30
        }
      },
      "35": {
        start: {
          line: 116,
          column: 31
        },
        end: {
          line: 116,
          column: 75
        }
      },
      "36": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 37
        }
      },
      "37": {
        start: {
          line: 121,
          column: 21
        },
        end: {
          line: 121,
          column: 43
        }
      },
      "38": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 122,
          column: 44
        }
      },
      "39": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 124,
          column: 54
        }
      },
      "40": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 126,
          column: 31
        }
      },
      "41": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 57
        }
      },
      "42": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 18
        }
      },
      "43": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "44": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 53
        }
      },
      "45": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 179,
          column: 5
        }
      },
      "46": {
        start: {
          line: 141,
          column: 6
        },
        end: {
          line: 141,
          column: 55
        }
      },
      "47": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 37
        }
      },
      "48": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 31
        }
      },
      "49": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 28
        }
      },
      "50": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 36
        }
      },
      "51": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 152,
          column: 7
        }
      },
      "52": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 151,
          column: 55
        }
      },
      "53": {
        start: {
          line: 155,
          column: 23
        },
        end: {
          line: 155,
          column: 78
        }
      },
      "54": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 158,
          column: 7
        }
      },
      "55": {
        start: {
          line: 157,
          column: 8
        },
        end: {
          line: 157,
          column: 52
        }
      },
      "56": {
        start: {
          line: 160,
          column: 23
        },
        end: {
          line: 160,
          column: 90
        }
      },
      "57": {
        start: {
          line: 163,
          column: 24
        },
        end: {
          line: 163,
          column: 78
        }
      },
      "58": {
        start: {
          line: 165,
          column: 43
        },
        end: {
          line: 172,
          column: 7
        }
      },
      "59": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 53
        }
      },
      "60": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 20
        }
      },
      "61": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 56
        }
      },
      "62": {
        start: {
          line: 178,
          column: 6
        },
        end: {
          line: 178,
          column: 18
        }
      },
      "63": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 188,
          column: 5
        }
      },
      "64": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 187,
          column: 13
        }
      },
      "65": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 198,
          column: 5
        }
      },
      "66": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 193,
          column: 27
        }
      },
      "67": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 194,
          column: 67
        }
      },
      "68": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 57
        }
      },
      "69": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 18
        }
      },
      "70": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 207,
          column: 5
        }
      },
      "71": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 206,
          column: 13
        }
      },
      "72": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 217,
          column: 5
        }
      },
      "73": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 28
        }
      },
      "74": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 68
        }
      },
      "75": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 58
        }
      },
      "76": {
        start: {
          line: 216,
          column: 6
        },
        end: {
          line: 216,
          column: 18
        }
      },
      "77": {
        start: {
          line: 224,
          column: 24
        },
        end: {
          line: 224,
          column: 34
        }
      },
      "78": {
        start: {
          line: 225,
          column: 21
        },
        end: {
          line: 227,
          column: 9
        }
      },
      "79": {
        start: {
          line: 229,
          column: 4
        },
        end: {
          line: 234,
          column: 6
        }
      },
      "80": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 241,
          column: 37
        }
      },
      "81": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 259,
          column: 5
        }
      },
      "82": {
        start: {
          line: 249,
          column: 26
        },
        end: {
          line: 249,
          column: 70
        }
      },
      "83": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 252,
          column: 7
        }
      },
      "84": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 251,
          column: 75
        }
      },
      "85": {
        start: {
          line: 254,
          column: 20
        },
        end: {
          line: 254,
          column: 60
        }
      },
      "86": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 23
        }
      },
      "87": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 257,
          column: 63
        }
      },
      "88": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 258,
          column: 18
        }
      },
      "89": {
        start: {
          line: 266,
          column: 4
        },
        end: {
          line: 307,
          column: 5
        }
      },
      "90": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 52
        }
      },
      "91": {
        start: {
          line: 270,
          column: 23
        },
        end: {
          line: 270,
          column: 57
        }
      },
      "92": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 273,
          column: 7
        }
      },
      "93": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 272,
          column: 64
        }
      },
      "94": {
        start: {
          line: 275,
          column: 27
        },
        end: {
          line: 275,
          column: 45
        }
      },
      "95": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 83
        }
      },
      "96": {
        start: {
          line: 279,
          column: 28
        },
        end: {
          line: 279,
          column: 86
        }
      },
      "97": {
        start: {
          line: 282,
          column: 34
        },
        end: {
          line: 286,
          column: 7
        }
      },
      "98": {
        start: {
          line: 288,
          column: 23
        },
        end: {
          line: 288,
          column: 51
        }
      },
      "99": {
        start: {
          line: 292,
          column: 6
        },
        end: {
          line: 295,
          column: 9
        }
      },
      "100": {
        start: {
          line: 297,
          column: 29
        },
        end: {
          line: 297,
          column: 73
        }
      },
      "101": {
        start: {
          line: 298,
          column: 29
        },
        end: {
          line: 298,
          column: 53
        }
      },
      "102": {
        start: {
          line: 300,
          column: 6
        },
        end: {
          line: 300,
          column: 82
        }
      },
      "103": {
        start: {
          line: 302,
          column: 6
        },
        end: {
          line: 302,
          column: 50
        }
      },
      "104": {
        start: {
          line: 303,
          column: 6
        },
        end: {
          line: 303,
          column: 27
        }
      },
      "105": {
        start: {
          line: 305,
          column: 6
        },
        end: {
          line: 305,
          column: 56
        }
      },
      "106": {
        start: {
          line: 306,
          column: 6
        },
        end: {
          line: 306,
          column: 18
        }
      },
      "107": {
        start: {
          line: 314,
          column: 4
        },
        end: {
          line: 332,
          column: 5
        }
      },
      "108": {
        start: {
          line: 316,
          column: 27
        },
        end: {
          line: 316,
          column: 84
        }
      },
      "109": {
        start: {
          line: 318,
          column: 6
        },
        end: {
          line: 318,
          column: 58
        }
      },
      "110": {
        start: {
          line: 328,
          column: 6
        },
        end: {
          line: 328,
          column: 16
        }
      },
      "111": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 60
        }
      },
      "112": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 331,
          column: 16
        }
      },
      "113": {
        start: {
          line: 339,
          column: 53
        },
        end: {
          line: 344,
          column: 5
        }
      },
      "114": {
        start: {
          line: 346,
          column: 4
        },
        end: {
          line: 351,
          column: 6
        }
      },
      "115": {
        start: {
          line: 358,
          column: 4
        },
        end: {
          line: 360,
          column: 5
        }
      },
      "116": {
        start: {
          line: 359,
          column: 6
        },
        end: {
          line: 359,
          column: 43
        }
      },
      "117": {
        start: {
          line: 362,
          column: 4
        },
        end: {
          line: 367,
          column: 13
        }
      },
      "118": {
        start: {
          line: 363,
          column: 6
        },
        end: {
          line: 366,
          column: 7
        }
      },
      "119": {
        start: {
          line: 364,
          column: 25
        },
        end: {
          line: 364,
          column: 50
        }
      },
      "120": {
        start: {
          line: 365,
          column: 8
        },
        end: {
          line: 365,
          column: 40
        }
      },
      "121": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 377,
          column: 5
        }
      },
      "122": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 375,
          column: 43
        }
      },
      "123": {
        start: {
          line: 376,
          column: 6
        },
        end: {
          line: 376,
          column: 35
        }
      },
      "124": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 384,
          column: 34
        }
      },
      "125": {
        start: {
          line: 385,
          column: 4
        },
        end: {
          line: 385,
          column: 29
        }
      },
      "126": {
        start: {
          line: 386,
          column: 4
        },
        end: {
          line: 386,
          column: 26
        }
      },
      "127": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 387,
          column: 36
        }
      },
      "128": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 388,
          column: 33
        }
      },
      "129": {
        start: {
          line: 393,
          column: 37
        },
        end: {
          line: 393,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 47,
            column: 2
          },
          end: {
            line: 47,
            column: 3
          }
        },
        loc: {
          start: {
            line: 47,
            column: 36
          },
          end: {
            line: 61,
            column: 3
          }
        },
        line: 47
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 66,
            column: 2
          },
          end: {
            line: 66,
            column: 3
          }
        },
        loc: {
          start: {
            line: 66,
            column: 57
          },
          end: {
            line: 85,
            column: 3
          }
        },
        line: 66
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        },
        loc: {
          start: {
            line: 90,
            column: 41
          },
          end: {
            line: 92,
            column: 3
          }
        },
        line: 90
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 97,
            column: 2
          },
          end: {
            line: 97,
            column: 3
          }
        },
        loc: {
          start: {
            line: 97,
            column: 68
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 97
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 135,
            column: 2
          },
          end: {
            line: 135,
            column: 3
          }
        },
        loc: {
          start: {
            line: 135,
            column: 55
          },
          end: {
            line: 180,
            column: 3
          }
        },
        line: 135
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 185,
            column: 2
          },
          end: {
            line: 185,
            column: 3
          }
        },
        loc: {
          start: {
            line: 185,
            column: 40
          },
          end: {
            line: 199,
            column: 3
          }
        },
        line: 185
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 204,
            column: 2
          },
          end: {
            line: 204,
            column: 3
          }
        },
        loc: {
          start: {
            line: 204,
            column: 41
          },
          end: {
            line: 218,
            column: 3
          }
        },
        line: 204
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 223,
            column: 2
          },
          end: {
            line: 223,
            column: 3
          }
        },
        loc: {
          start: {
            line: 223,
            column: 42
          },
          end: {
            line: 235,
            column: 3
          }
        },
        line: 223
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 240,
            column: 2
          },
          end: {
            line: 240,
            column: 3
          }
        },
        loc: {
          start: {
            line: 240,
            column: 77
          },
          end: {
            line: 242,
            column: 3
          }
        },
        line: 240
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 247,
            column: 2
          },
          end: {
            line: 247,
            column: 3
          }
        },
        loc: {
          start: {
            line: 247,
            column: 52
          },
          end: {
            line: 260,
            column: 3
          }
        },
        line: 247
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 265,
            column: 2
          },
          end: {
            line: 265,
            column: 3
          }
        },
        loc: {
          start: {
            line: 265,
            column: 99
          },
          end: {
            line: 308,
            column: 3
          }
        },
        line: 265
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 313,
            column: 2
          },
          end: {
            line: 313,
            column: 3
          }
        },
        loc: {
          start: {
            line: 313,
            column: 64
          },
          end: {
            line: 333,
            column: 3
          }
        },
        line: 313
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 338,
            column: 2
          },
          end: {
            line: 338,
            column: 3
          }
        },
        loc: {
          start: {
            line: 338,
            column: 65
          },
          end: {
            line: 352,
            column: 3
          }
        },
        line: 338
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 357,
            column: 2
          },
          end: {
            line: 357,
            column: 3
          }
        },
        loc: {
          start: {
            line: 357,
            column: 42
          },
          end: {
            line: 368,
            column: 3
          }
        },
        line: 357
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 362,
            column: 40
          },
          end: {
            line: 362,
            column: 41
          }
        },
        loc: {
          start: {
            line: 362,
            column: 46
          },
          end: {
            line: 367,
            column: 5
          }
        },
        line: 362
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 373,
            column: 41
          },
          end: {
            line: 378,
            column: 3
          }
        },
        line: 373
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 383,
            column: 2
          },
          end: {
            line: 383,
            column: 3
          }
        },
        loc: {
          start: {
            line: 383,
            column: 18
          },
          end: {
            line: 389,
            column: 3
          }
        },
        line: 383
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 50,
            column: 6
          },
          end: {
            line: 52,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 6
          },
          end: {
            line: 52,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "1": {
        loc: {
          start: {
            line: 50,
            column: 10
          },
          end: {
            line: 50,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 10
          },
          end: {
            line: 50,
            column: 29
          }
        }, {
          start: {
            line: 50,
            column: 33
          },
          end: {
            line: 50,
            column: 56
          }
        }],
        line: 50
      },
      "2": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 100,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 100,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "3": {
        loc: {
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "4": {
        loc: {
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "5": {
        loc: {
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 136,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 136,
            column: 23
          }
        }, {
          start: {
            line: 136,
            column: 27
          },
          end: {
            line: 136,
            column: 44
          }
        }],
        line: 136
      },
      "6": {
        loc: {
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 152,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 152,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "7": {
        loc: {
          start: {
            line: 156,
            column: 6
          },
          end: {
            line: 158,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 6
          },
          end: {
            line: 158,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "8": {
        loc: {
          start: {
            line: 168,
            column: 18
          },
          end: {
            line: 168,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 168,
            column: 18
          },
          end: {
            line: 168,
            column: 31
          }
        }, {
          start: {
            line: 168,
            column: 35
          },
          end: {
            line: 168,
            column: 36
          }
        }],
        line: 168
      },
      "9": {
        loc: {
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "10": {
        loc: {
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 186,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 186,
            column: 25
          }
        }, {
          start: {
            line: 186,
            column: 29
          },
          end: {
            line: 186,
            column: 42
          }
        }],
        line: 186
      },
      "11": {
        loc: {
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "12": {
        loc: {
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 205,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 205,
            column: 25
          }
        }, {
          start: {
            line: 205,
            column: 29
          },
          end: {
            line: 205,
            column: 43
          }
        }],
        line: 205
      },
      "13": {
        loc: {
          start: {
            line: 225,
            column: 21
          },
          end: {
            line: 227,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 226,
            column: 76
          }
        }, {
          start: {
            line: 227,
            column: 8
          },
          end: {
            line: 227,
            column: 9
          }
        }],
        line: 225
      },
      "14": {
        loc: {
          start: {
            line: 250,
            column: 6
          },
          end: {
            line: 252,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 6
          },
          end: {
            line: 252,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "15": {
        loc: {
          start: {
            line: 265,
            column: 35
          },
          end: {
            line: 265,
            column: 80
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 265,
            column: 72
          },
          end: {
            line: 265,
            column: 80
          }
        }],
        line: 265
      },
      "16": {
        loc: {
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "17": {
        loc: {
          start: {
            line: 275,
            column: 27
          },
          end: {
            line: 275,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 275,
            column: 27
          },
          end: {
            line: 275,
            column: 40
          }
        }, {
          start: {
            line: 275,
            column: 44
          },
          end: {
            line: 275,
            column: 45
          }
        }],
        line: 275
      },
      "18": {
        loc: {
          start: {
            line: 298,
            column: 29
          },
          end: {
            line: 298,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 298,
            column: 29
          },
          end: {
            line: 298,
            column: 48
          }
        }, {
          start: {
            line: 298,
            column: 52
          },
          end: {
            line: 298,
            column: 53
          }
        }],
        line: 298
      },
      "19": {
        loc: {
          start: {
            line: 347,
            column: 15
          },
          end: {
            line: 347,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 347,
            column: 15
          },
          end: {
            line: 347,
            column: 41
          }
        }, {
          start: {
            line: 347,
            column: 45
          },
          end: {
            line: 347,
            column: 65
          }
        }],
        line: 347
      },
      "20": {
        loc: {
          start: {
            line: 358,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 358
      },
      "21": {
        loc: {
          start: {
            line: 363,
            column: 6
          },
          end: {
            line: 366,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 363,
            column: 6
          },
          end: {
            line: 366,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 363
      },
      "22": {
        loc: {
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 377,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 377,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 374
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c2d81470d07194971cd0f212eac41cba598a7be3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_t4rs1xx0t = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_t4rs1xx0t();
var VideoRecordingService = function () {
  function VideoRecordingService() {
    (0, _classCallCheck2.default)(this, VideoRecordingService);
    this.cameraRef = (cov_t4rs1xx0t().s[0]++, null);
    this.isRecording = (cov_t4rs1xx0t().s[1]++, false);
    this.isPaused = (cov_t4rs1xx0t().s[2]++, false);
    this.recordingStartTime = (cov_t4rs1xx0t().s[3]++, 0);
    this.pausedDuration = (cov_t4rs1xx0t().s[4]++, 0);
    this.currentRecordingUri = (cov_t4rs1xx0t().s[5]++, null);
    this.progressCallback = (cov_t4rs1xx0t().s[6]++, null);
    this.progressInterval = (cov_t4rs1xx0t().s[7]++, null);
  }
  return (0, _createClass2.default)(VideoRecordingService, [{
    key: "initialize",
    value: (function () {
      var _initialize = (0, _asyncToGenerator2.default)(function* () {
        cov_t4rs1xx0t().f[0]++;
        cov_t4rs1xx0t().s[8]++;
        try {
          var permissions = (cov_t4rs1xx0t().s[9]++, yield this.requestPermissions());
          cov_t4rs1xx0t().s[10]++;
          if ((cov_t4rs1xx0t().b[1][0]++, !permissions.camera) || (cov_t4rs1xx0t().b[1][1]++, !permissions.microphone)) {
            cov_t4rs1xx0t().b[0][0]++;
            cov_t4rs1xx0t().s[11]++;
            throw new Error('Camera and microphone permissions are required for video recording');
          } else {
            cov_t4rs1xx0t().b[0][1]++;
          }
          cov_t4rs1xx0t().s[12]++;
          _performance.performanceMonitor.start('video_service_init');
          cov_t4rs1xx0t().s[13]++;
          _performance.performanceMonitor.end('video_service_init');
        } catch (error) {
          cov_t4rs1xx0t().s[14]++;
          console.error('Failed to initialize video recording service:', error);
          cov_t4rs1xx0t().s[15]++;
          throw error;
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "requestPermissions",
    value: (function () {
      var _requestPermissions = (0, _asyncToGenerator2.default)(function* () {
        cov_t4rs1xx0t().f[1]++;
        cov_t4rs1xx0t().s[16]++;
        try {
          var cameraPermission = (cov_t4rs1xx0t().s[17]++, yield _expoCamera.Camera.requestCameraPermissionsAsync());
          var microphonePermission = (cov_t4rs1xx0t().s[18]++, yield _expoCamera.Camera.requestMicrophonePermissionsAsync());
          var mediaLibraryPermission = (cov_t4rs1xx0t().s[19]++, yield MediaLibrary.requestPermissionsAsync());
          cov_t4rs1xx0t().s[20]++;
          return {
            camera: cameraPermission.status === 'granted',
            microphone: microphonePermission.status === 'granted',
            mediaLibrary: mediaLibraryPermission.status === 'granted'
          };
        } catch (error) {
          cov_t4rs1xx0t().s[21]++;
          console.error('Failed to request permissions:', error);
          cov_t4rs1xx0t().s[22]++;
          return {
            camera: false,
            microphone: false,
            mediaLibrary: false
          };
        }
      });
      function requestPermissions() {
        return _requestPermissions.apply(this, arguments);
      }
      return requestPermissions;
    }())
  }, {
    key: "setCameraRef",
    value: function setCameraRef(ref) {
      cov_t4rs1xx0t().f[2]++;
      cov_t4rs1xx0t().s[23]++;
      this.cameraRef = ref;
    }
  }, {
    key: "startRecording",
    value: (function () {
      var _startRecording = (0, _asyncToGenerator2.default)(function* (config) {
        cov_t4rs1xx0t().f[3]++;
        cov_t4rs1xx0t().s[24]++;
        if (!this.cameraRef) {
          cov_t4rs1xx0t().b[2][0]++;
          cov_t4rs1xx0t().s[25]++;
          throw new Error('Camera reference not set');
        } else {
          cov_t4rs1xx0t().b[2][1]++;
        }
        cov_t4rs1xx0t().s[26]++;
        if (this.isRecording) {
          cov_t4rs1xx0t().b[3][0]++;
          cov_t4rs1xx0t().s[27]++;
          throw new Error('Recording already in progress');
        } else {
          cov_t4rs1xx0t().b[3][1]++;
        }
        cov_t4rs1xx0t().s[28]++;
        try {
          cov_t4rs1xx0t().s[29]++;
          _performance.performanceMonitor.start('video_recording_start');
          var recordingOptions = (cov_t4rs1xx0t().s[30]++, this.getRecordingOptions(config));
          cov_t4rs1xx0t().s[31]++;
          this.isRecording = true;
          cov_t4rs1xx0t().s[32]++;
          this.isPaused = false;
          cov_t4rs1xx0t().s[33]++;
          this.recordingStartTime = Date.now();
          cov_t4rs1xx0t().s[34]++;
          this.pausedDuration = 0;
          var recordingPromise = (cov_t4rs1xx0t().s[35]++, this.cameraRef.recordAsync(recordingOptions));
          cov_t4rs1xx0t().s[36]++;
          this.startProgressMonitoring();
          var result = (cov_t4rs1xx0t().s[37]++, yield recordingPromise);
          cov_t4rs1xx0t().s[38]++;
          this.currentRecordingUri = result.uri;
          cov_t4rs1xx0t().s[39]++;
          _performance.performanceMonitor.end('video_recording_start');
        } catch (error) {
          cov_t4rs1xx0t().s[40]++;
          this.isRecording = false;
          cov_t4rs1xx0t().s[41]++;
          console.error('Failed to start recording:', error);
          cov_t4rs1xx0t().s[42]++;
          throw error;
        }
      });
      function startRecording(_x) {
        return _startRecording.apply(this, arguments);
      }
      return startRecording;
    }())
  }, {
    key: "stopRecording",
    value: (function () {
      var _stopRecording = (0, _asyncToGenerator2.default)(function* () {
        cov_t4rs1xx0t().f[4]++;
        cov_t4rs1xx0t().s[43]++;
        if ((cov_t4rs1xx0t().b[5][0]++, !this.cameraRef) || (cov_t4rs1xx0t().b[5][1]++, !this.isRecording)) {
          cov_t4rs1xx0t().b[4][0]++;
          cov_t4rs1xx0t().s[44]++;
          throw new Error('No active recording to stop');
        } else {
          cov_t4rs1xx0t().b[4][1]++;
        }
        cov_t4rs1xx0t().s[45]++;
        try {
          cov_t4rs1xx0t().s[46]++;
          _performance.performanceMonitor.start('video_recording_stop');
          cov_t4rs1xx0t().s[47]++;
          this.cameraRef.stopRecording();
          cov_t4rs1xx0t().s[48]++;
          this.isRecording = false;
          cov_t4rs1xx0t().s[49]++;
          this.isPaused = false;
          cov_t4rs1xx0t().s[50]++;
          this.stopProgressMonitoring();
          cov_t4rs1xx0t().s[51]++;
          if (!this.currentRecordingUri) {
            cov_t4rs1xx0t().b[6][0]++;
            cov_t4rs1xx0t().s[52]++;
            throw new Error('Recording URI not available');
          } else {
            cov_t4rs1xx0t().b[6][1]++;
          }
          var fileInfo = (cov_t4rs1xx0t().s[53]++, yield FileSystem.getInfoAsync(this.currentRecordingUri));
          cov_t4rs1xx0t().s[54]++;
          if (!fileInfo.exists) {
            cov_t4rs1xx0t().b[7][0]++;
            cov_t4rs1xx0t().s[55]++;
            throw new Error('Recording file not found');
          } else {
            cov_t4rs1xx0t().b[7][1]++;
          }
          var duration = (cov_t4rs1xx0t().s[56]++, (Date.now() - this.recordingStartTime - this.pausedDuration) / 1000);
          var thumbnail = (cov_t4rs1xx0t().s[57]++, yield this.generateThumbnail(this.currentRecordingUri));
          var result = (cov_t4rs1xx0t().s[58]++, {
            uri: this.currentRecordingUri,
            duration: duration,
            fileSize: (cov_t4rs1xx0t().b[8][0]++, fileInfo.size) || (cov_t4rs1xx0t().b[8][1]++, 0),
            width: 1920,
            height: 1080,
            thumbnail: thumbnail
          });
          cov_t4rs1xx0t().s[59]++;
          _performance.performanceMonitor.end('video_recording_stop');
          cov_t4rs1xx0t().s[60]++;
          return result;
        } catch (error) {
          cov_t4rs1xx0t().s[61]++;
          console.error('Failed to stop recording:', error);
          cov_t4rs1xx0t().s[62]++;
          throw error;
        }
      });
      function stopRecording() {
        return _stopRecording.apply(this, arguments);
      }
      return stopRecording;
    }())
  }, {
    key: "pauseRecording",
    value: (function () {
      var _pauseRecording = (0, _asyncToGenerator2.default)(function* () {
        cov_t4rs1xx0t().f[5]++;
        cov_t4rs1xx0t().s[63]++;
        if ((cov_t4rs1xx0t().b[10][0]++, !this.isRecording) || (cov_t4rs1xx0t().b[10][1]++, this.isPaused)) {
          cov_t4rs1xx0t().b[9][0]++;
          cov_t4rs1xx0t().s[64]++;
          return;
        } else {
          cov_t4rs1xx0t().b[9][1]++;
        }
        cov_t4rs1xx0t().s[65]++;
        try {
          cov_t4rs1xx0t().s[66]++;
          this.isPaused = true;
          cov_t4rs1xx0t().s[67]++;
          console.log('Recording paused (placeholder implementation)');
        } catch (error) {
          cov_t4rs1xx0t().s[68]++;
          console.error('Failed to pause recording:', error);
          cov_t4rs1xx0t().s[69]++;
          throw error;
        }
      });
      function pauseRecording() {
        return _pauseRecording.apply(this, arguments);
      }
      return pauseRecording;
    }())
  }, {
    key: "resumeRecording",
    value: (function () {
      var _resumeRecording = (0, _asyncToGenerator2.default)(function* () {
        cov_t4rs1xx0t().f[6]++;
        cov_t4rs1xx0t().s[70]++;
        if ((cov_t4rs1xx0t().b[12][0]++, !this.isRecording) || (cov_t4rs1xx0t().b[12][1]++, !this.isPaused)) {
          cov_t4rs1xx0t().b[11][0]++;
          cov_t4rs1xx0t().s[71]++;
          return;
        } else {
          cov_t4rs1xx0t().b[11][1]++;
        }
        cov_t4rs1xx0t().s[72]++;
        try {
          cov_t4rs1xx0t().s[73]++;
          this.isPaused = false;
          cov_t4rs1xx0t().s[74]++;
          console.log('Recording resumed (placeholder implementation)');
        } catch (error) {
          cov_t4rs1xx0t().s[75]++;
          console.error('Failed to resume recording:', error);
          cov_t4rs1xx0t().s[76]++;
          throw error;
        }
      });
      function resumeRecording() {
        return _resumeRecording.apply(this, arguments);
      }
      return resumeRecording;
    }())
  }, {
    key: "getRecordingStatus",
    value: function getRecordingStatus() {
      cov_t4rs1xx0t().f[7]++;
      var currentTime = (cov_t4rs1xx0t().s[77]++, Date.now());
      var duration = (cov_t4rs1xx0t().s[78]++, this.isRecording ? (cov_t4rs1xx0t().b[13][0]++, (currentTime - this.recordingStartTime - this.pausedDuration) / 1000) : (cov_t4rs1xx0t().b[13][1]++, 0));
      cov_t4rs1xx0t().s[79]++;
      return {
        duration: duration,
        fileSize: 0,
        isRecording: this.isRecording,
        isPaused: this.isPaused
      };
    }
  }, {
    key: "setProgressCallback",
    value: function setProgressCallback(callback) {
      cov_t4rs1xx0t().f[8]++;
      cov_t4rs1xx0t().s[80]++;
      this.progressCallback = callback;
    }
  }, {
    key: "saveToGallery",
    value: (function () {
      var _saveToGallery = (0, _asyncToGenerator2.default)(function* (uri) {
        cov_t4rs1xx0t().f[9]++;
        cov_t4rs1xx0t().s[81]++;
        try {
          var permissions = (cov_t4rs1xx0t().s[82]++, yield MediaLibrary.requestPermissionsAsync());
          cov_t4rs1xx0t().s[83]++;
          if (!permissions.granted) {
            cov_t4rs1xx0t().b[14][0]++;
            cov_t4rs1xx0t().s[84]++;
            throw new Error('Media library permission required to save video');
          } else {
            cov_t4rs1xx0t().b[14][1]++;
          }
          var asset = (cov_t4rs1xx0t().s[85]++, yield MediaLibrary.createAssetAsync(uri));
          cov_t4rs1xx0t().s[86]++;
          return asset.uri;
        } catch (error) {
          cov_t4rs1xx0t().s[87]++;
          console.error('Failed to save video to gallery:', error);
          cov_t4rs1xx0t().s[88]++;
          throw error;
        }
      });
      function saveToGallery(_x2) {
        return _saveToGallery.apply(this, arguments);
      }
      return saveToGallery;
    }())
  }, {
    key: "compressVideo",
    value: (function () {
      var _compressVideo = (0, _asyncToGenerator2.default)(function* (uri) {
        var quality = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_t4rs1xx0t().b[15][0]++, 'medium');
        cov_t4rs1xx0t().f[10]++;
        cov_t4rs1xx0t().s[89]++;
        try {
          cov_t4rs1xx0t().s[90]++;
          _performance.performanceMonitor.start('video_compression');
          var fileInfo = (cov_t4rs1xx0t().s[91]++, yield FileSystem.getInfoAsync(uri));
          cov_t4rs1xx0t().s[92]++;
          if (!fileInfo.exists) {
            cov_t4rs1xx0t().b[16][0]++;
            cov_t4rs1xx0t().s[93]++;
            throw new Error('Video file not found for compression');
          } else {
            cov_t4rs1xx0t().b[16][1]++;
          }
          var originalSize = (cov_t4rs1xx0t().s[94]++, (cov_t4rs1xx0t().b[17][0]++, fileInfo.size) || (cov_t4rs1xx0t().b[17][1]++, 0));
          cov_t4rs1xx0t().s[95]++;
          console.log(`Compressing video: ${originalSize} bytes, quality: ${quality}`);
          var compressedUri = (cov_t4rs1xx0t().s[96]++, `${FileSystem.cacheDirectory}compressed_${Date.now()}.mp4`);
          var compressionSettings = (cov_t4rs1xx0t().s[97]++, {
            low: {
              bitrate: 500000,
              resolution: 480
            },
            medium: {
              bitrate: 1000000,
              resolution: 720
            },
            high: {
              bitrate: 2000000,
              resolution: 1080
            }
          });
          var settings = (cov_t4rs1xx0t().s[98]++, compressionSettings[quality]);
          cov_t4rs1xx0t().s[99]++;
          yield FileSystem.copyAsync({
            from: uri,
            to: compressedUri
          });
          var compressedInfo = (cov_t4rs1xx0t().s[100]++, yield FileSystem.getInfoAsync(compressedUri));
          var compressedSize = (cov_t4rs1xx0t().s[101]++, (cov_t4rs1xx0t().b[18][0]++, compressedInfo.size) || (cov_t4rs1xx0t().b[18][1]++, 0));
          cov_t4rs1xx0t().s[102]++;
          console.log(`Video compressed: ${originalSize} -> ${compressedSize} bytes`);
          cov_t4rs1xx0t().s[103]++;
          _performance.performanceMonitor.end('video_compression');
          cov_t4rs1xx0t().s[104]++;
          return compressedUri;
        } catch (error) {
          cov_t4rs1xx0t().s[105]++;
          console.error('Failed to compress video:', error);
          cov_t4rs1xx0t().s[106]++;
          throw error;
        }
      });
      function compressVideo(_x3) {
        return _compressVideo.apply(this, arguments);
      }
      return compressVideo;
    }())
  }, {
    key: "generateThumbnail",
    value: (function () {
      var _generateThumbnail = (0, _asyncToGenerator2.default)(function* (uri) {
        cov_t4rs1xx0t().f[11]++;
        cov_t4rs1xx0t().s[107]++;
        try {
          var thumbnailUri = (cov_t4rs1xx0t().s[108]++, `${FileSystem.cacheDirectory}thumbnail_${Date.now()}.jpg`);
          cov_t4rs1xx0t().s[109]++;
          console.log('Generating thumbnail for video:', uri);
          cov_t4rs1xx0t().s[110]++;
          return '';
        } catch (error) {
          cov_t4rs1xx0t().s[111]++;
          console.error('Failed to generate thumbnail:', error);
          cov_t4rs1xx0t().s[112]++;
          return '';
        }
      });
      function generateThumbnail(_x4) {
        return _generateThumbnail.apply(this, arguments);
      }
      return generateThumbnail;
    }())
  }, {
    key: "getRecordingOptions",
    value: function getRecordingOptions(config) {
      cov_t4rs1xx0t().f[12]++;
      var qualityMap = (cov_t4rs1xx0t().s[113]++, {
        low: _expoCamera.VideoQuality['480p'],
        medium: _expoCamera.VideoQuality['720p'],
        high: _expoCamera.VideoQuality['1080p'],
        ultra: _expoCamera.VideoQuality['2160p']
      });
      cov_t4rs1xx0t().s[114]++;
      return {
        quality: (cov_t4rs1xx0t().b[19][0]++, qualityMap[config.quality]) || (cov_t4rs1xx0t().b[19][1]++, _expoCamera.VideoQuality['720p']),
        maxDuration: config.maxDurationMinutes * 60,
        mute: !config.enableAudio
      };
    }
  }, {
    key: "startProgressMonitoring",
    value: function startProgressMonitoring() {
      var _this = this;
      cov_t4rs1xx0t().f[13]++;
      cov_t4rs1xx0t().s[115]++;
      if (this.progressInterval) {
        cov_t4rs1xx0t().b[20][0]++;
        cov_t4rs1xx0t().s[116]++;
        clearInterval(this.progressInterval);
      } else {
        cov_t4rs1xx0t().b[20][1]++;
      }
      cov_t4rs1xx0t().s[117]++;
      this.progressInterval = setInterval(function () {
        cov_t4rs1xx0t().f[14]++;
        cov_t4rs1xx0t().s[118]++;
        if (_this.progressCallback) {
          cov_t4rs1xx0t().b[21][0]++;
          var progress = (cov_t4rs1xx0t().s[119]++, _this.getRecordingStatus());
          cov_t4rs1xx0t().s[120]++;
          _this.progressCallback(progress);
        } else {
          cov_t4rs1xx0t().b[21][1]++;
        }
      }, 1000);
    }
  }, {
    key: "stopProgressMonitoring",
    value: function stopProgressMonitoring() {
      cov_t4rs1xx0t().f[15]++;
      cov_t4rs1xx0t().s[121]++;
      if (this.progressInterval) {
        cov_t4rs1xx0t().b[22][0]++;
        cov_t4rs1xx0t().s[122]++;
        clearInterval(this.progressInterval);
        cov_t4rs1xx0t().s[123]++;
        this.progressInterval = null;
      } else {
        cov_t4rs1xx0t().b[22][1]++;
      }
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      cov_t4rs1xx0t().f[16]++;
      cov_t4rs1xx0t().s[124]++;
      this.stopProgressMonitoring();
      cov_t4rs1xx0t().s[125]++;
      this.isRecording = false;
      cov_t4rs1xx0t().s[126]++;
      this.isPaused = false;
      cov_t4rs1xx0t().s[127]++;
      this.currentRecordingUri = null;
      cov_t4rs1xx0t().s[128]++;
      this.progressCallback = null;
    }
  }]);
}();
var videoRecordingService = exports.videoRecordingService = (cov_t4rs1xx0t().s[129]++, new VideoRecordingService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfZXhwb0NhbWVyYSIsInJlcXVpcmUiLCJNZWRpYUxpYnJhcnkiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsIkZpbGVTeXN0ZW0iLCJfcGVyZm9ybWFuY2UiLCJfZ2V0UmVxdWlyZVdpbGRjYXJkQ2FjaGUiLCJlIiwiV2Vha01hcCIsInIiLCJ0IiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJoYXMiLCJnZXQiLCJuIiwiX19wcm90b19fIiwiYSIsIk9iamVjdCIsImRlZmluZVByb3BlcnR5IiwiZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yIiwidSIsImhhc093blByb3BlcnR5IiwiY2FsbCIsImkiLCJzZXQiLCJjb3ZfdDRyczF4eDB0IiwicGF0aCIsImhhc2giLCJnbG9iYWwiLCJGdW5jdGlvbiIsImdjdiIsImNvdmVyYWdlRGF0YSIsInN0YXRlbWVudE1hcCIsInN0YXJ0IiwibGluZSIsImNvbHVtbiIsImVuZCIsImZuTWFwIiwibmFtZSIsImRlY2wiLCJsb2MiLCJicmFuY2hNYXAiLCJ0eXBlIiwibG9jYXRpb25zIiwidW5kZWZpbmVkIiwicyIsImYiLCJiIiwiX2NvdmVyYWdlU2NoZW1hIiwiY292ZXJhZ2UiLCJhY3R1YWxDb3ZlcmFnZSIsIlZpZGVvUmVjb3JkaW5nU2VydmljZSIsIl9jbGFzc0NhbGxDaGVjazIiLCJjYW1lcmFSZWYiLCJpc1JlY29yZGluZyIsImlzUGF1c2VkIiwicmVjb3JkaW5nU3RhcnRUaW1lIiwicGF1c2VkRHVyYXRpb24iLCJjdXJyZW50UmVjb3JkaW5nVXJpIiwicHJvZ3Jlc3NDYWxsYmFjayIsInByb2dyZXNzSW50ZXJ2YWwiLCJfY3JlYXRlQ2xhc3MyIiwia2V5IiwidmFsdWUiLCJfaW5pdGlhbGl6ZSIsIl9hc3luY1RvR2VuZXJhdG9yMiIsInBlcm1pc3Npb25zIiwicmVxdWVzdFBlcm1pc3Npb25zIiwiY2FtZXJhIiwibWljcm9waG9uZSIsIkVycm9yIiwicGVyZm9ybWFuY2VNb25pdG9yIiwiZXJyb3IiLCJjb25zb2xlIiwiaW5pdGlhbGl6ZSIsImFwcGx5IiwiYXJndW1lbnRzIiwiX3JlcXVlc3RQZXJtaXNzaW9ucyIsImNhbWVyYVBlcm1pc3Npb24iLCJDYW1lcmEiLCJyZXF1ZXN0Q2FtZXJhUGVybWlzc2lvbnNBc3luYyIsIm1pY3JvcGhvbmVQZXJtaXNzaW9uIiwicmVxdWVzdE1pY3JvcGhvbmVQZXJtaXNzaW9uc0FzeW5jIiwibWVkaWFMaWJyYXJ5UGVybWlzc2lvbiIsInJlcXVlc3RQZXJtaXNzaW9uc0FzeW5jIiwic3RhdHVzIiwibWVkaWFMaWJyYXJ5Iiwic2V0Q2FtZXJhUmVmIiwicmVmIiwiX3N0YXJ0UmVjb3JkaW5nIiwiY29uZmlnIiwicmVjb3JkaW5nT3B0aW9ucyIsImdldFJlY29yZGluZ09wdGlvbnMiLCJEYXRlIiwibm93IiwicmVjb3JkaW5nUHJvbWlzZSIsInJlY29yZEFzeW5jIiwic3RhcnRQcm9ncmVzc01vbml0b3JpbmciLCJyZXN1bHQiLCJ1cmkiLCJzdGFydFJlY29yZGluZyIsIl94IiwiX3N0b3BSZWNvcmRpbmciLCJzdG9wUmVjb3JkaW5nIiwic3RvcFByb2dyZXNzTW9uaXRvcmluZyIsImZpbGVJbmZvIiwiZ2V0SW5mb0FzeW5jIiwiZXhpc3RzIiwiZHVyYXRpb24iLCJ0aHVtYm5haWwiLCJnZW5lcmF0ZVRodW1ibmFpbCIsImZpbGVTaXplIiwic2l6ZSIsIndpZHRoIiwiaGVpZ2h0IiwiX3BhdXNlUmVjb3JkaW5nIiwibG9nIiwicGF1c2VSZWNvcmRpbmciLCJfcmVzdW1lUmVjb3JkaW5nIiwicmVzdW1lUmVjb3JkaW5nIiwiZ2V0UmVjb3JkaW5nU3RhdHVzIiwiY3VycmVudFRpbWUiLCJzZXRQcm9ncmVzc0NhbGxiYWNrIiwiY2FsbGJhY2siLCJfc2F2ZVRvR2FsbGVyeSIsImdyYW50ZWQiLCJhc3NldCIsImNyZWF0ZUFzc2V0QXN5bmMiLCJzYXZlVG9HYWxsZXJ5IiwiX3gyIiwiX2NvbXByZXNzVmlkZW8iLCJxdWFsaXR5IiwibGVuZ3RoIiwib3JpZ2luYWxTaXplIiwiY29tcHJlc3NlZFVyaSIsImNhY2hlRGlyZWN0b3J5IiwiY29tcHJlc3Npb25TZXR0aW5ncyIsImxvdyIsImJpdHJhdGUiLCJyZXNvbHV0aW9uIiwibWVkaXVtIiwiaGlnaCIsInNldHRpbmdzIiwiY29weUFzeW5jIiwiZnJvbSIsInRvIiwiY29tcHJlc3NlZEluZm8iLCJjb21wcmVzc2VkU2l6ZSIsImNvbXByZXNzVmlkZW8iLCJfeDMiLCJfZ2VuZXJhdGVUaHVtYm5haWwiLCJ0aHVtYm5haWxVcmkiLCJfeDQiLCJxdWFsaXR5TWFwIiwiVmlkZW9RdWFsaXR5IiwidWx0cmEiLCJtYXhEdXJhdGlvbiIsIm1heER1cmF0aW9uTWludXRlcyIsIm11dGUiLCJlbmFibGVBdWRpbyIsIl90aGlzIiwiY2xlYXJJbnRlcnZhbCIsInNldEludGVydmFsIiwicHJvZ3Jlc3MiLCJjbGVhbnVwIiwidmlkZW9SZWNvcmRpbmdTZXJ2aWNlIiwiZXhwb3J0cyJdLCJzb3VyY2VzIjpbIlZpZGVvUmVjb3JkaW5nU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFZpZGVvIFJlY29yZGluZyBTZXJ2aWNlXG4gKiBIYW5kbGVzIHZpZGVvIHJlY29yZGluZyBmdW5jdGlvbmFsaXR5IGZvciB0ZW5uaXMgbWF0Y2hlcyB1c2luZyBSZWFjdCBOYXRpdmUgQ2FtZXJhXG4gKi9cblxuaW1wb3J0IHsgQ2FtZXJhLCBDYW1lcmFUeXBlLCBGbGFzaE1vZGUsIFZpZGVvUXVhbGl0eSB9IGZyb20gJ2V4cG8tY2FtZXJhJztcbmltcG9ydCAqIGFzIE1lZGlhTGlicmFyeSBmcm9tICdleHBvLW1lZGlhLWxpYnJhcnknO1xuaW1wb3J0ICogYXMgRmlsZVN5c3RlbSBmcm9tICdleHBvLWZpbGUtc3lzdGVtJztcbmltcG9ydCB7IFZpZGVvUmVjb3JkaW5nQ29uZmlnIH0gZnJvbSAnQC9zcmMvdHlwZXMvbWF0Y2gnO1xuaW1wb3J0IHsgcGVyZm9ybWFuY2VNb25pdG9yIH0gZnJvbSAnQC91dGlscy9wZXJmb3JtYW5jZSc7XG5cbmV4cG9ydCBpbnRlcmZhY2UgVmlkZW9SZWNvcmRpbmdSZXN1bHQge1xuICB1cmk6IHN0cmluZztcbiAgZHVyYXRpb246IG51bWJlcjtcbiAgZmlsZVNpemU6IG51bWJlcjtcbiAgd2lkdGg6IG51bWJlcjtcbiAgaGVpZ2h0OiBudW1iZXI7XG4gIHRodW1ibmFpbD86IHN0cmluZztcbn1cblxuZXhwb3J0IGludGVyZmFjZSBSZWNvcmRpbmdQcm9ncmVzcyB7XG4gIGR1cmF0aW9uOiBudW1iZXI7XG4gIGZpbGVTaXplOiBudW1iZXI7XG4gIGlzUmVjb3JkaW5nOiBib29sZWFuO1xuICBpc1BhdXNlZDogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBDYW1lcmFQZXJtaXNzaW9ucyB7XG4gIGNhbWVyYTogYm9vbGVhbjtcbiAgbWljcm9waG9uZTogYm9vbGVhbjtcbiAgbWVkaWFMaWJyYXJ5OiBib29sZWFuO1xufVxuXG5jbGFzcyBWaWRlb1JlY29yZGluZ1NlcnZpY2Uge1xuICBwcml2YXRlIGNhbWVyYVJlZjogQ2FtZXJhIHwgbnVsbCA9IG51bGw7XG4gIHByaXZhdGUgaXNSZWNvcmRpbmcgPSBmYWxzZTtcbiAgcHJpdmF0ZSBpc1BhdXNlZCA9IGZhbHNlO1xuICBwcml2YXRlIHJlY29yZGluZ1N0YXJ0VGltZSA9IDA7XG4gIHByaXZhdGUgcGF1c2VkRHVyYXRpb24gPSAwO1xuICBwcml2YXRlIGN1cnJlbnRSZWNvcmRpbmdVcmk6IHN0cmluZyB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIHByb2dyZXNzQ2FsbGJhY2s6ICgocHJvZ3Jlc3M6IFJlY29yZGluZ1Byb2dyZXNzKSA9PiB2b2lkKSB8IG51bGwgPSBudWxsO1xuICBwcml2YXRlIHByb2dyZXNzSW50ZXJ2YWw6IE5vZGVKUy5UaW1lb3V0IHwgbnVsbCA9IG51bGw7XG5cbiAgLyoqXG4gICAqIEluaXRpYWxpemUgdGhlIHZpZGVvIHJlY29yZGluZyBzZXJ2aWNlXG4gICAqL1xuICBhc3luYyBpbml0aWFsaXplKCk6IFByb21pc2U8dm9pZD4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwZXJtaXNzaW9ucyA9IGF3YWl0IHRoaXMucmVxdWVzdFBlcm1pc3Npb25zKCk7XG4gICAgICBpZiAoIXBlcm1pc3Npb25zLmNhbWVyYSB8fCAhcGVybWlzc2lvbnMubWljcm9waG9uZSkge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0NhbWVyYSBhbmQgbWljcm9waG9uZSBwZXJtaXNzaW9ucyBhcmUgcmVxdWlyZWQgZm9yIHZpZGVvIHJlY29yZGluZycpO1xuICAgICAgfVxuICAgICAgXG4gICAgICBwZXJmb3JtYW5jZU1vbml0b3Iuc3RhcnQoJ3ZpZGVvX3NlcnZpY2VfaW5pdCcpO1xuICAgICAgLy8gQWRkaXRpb25hbCBpbml0aWFsaXphdGlvbiBpZiBuZWVkZWRcbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci5lbmQoJ3ZpZGVvX3NlcnZpY2VfaW5pdCcpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gaW5pdGlhbGl6ZSB2aWRlbyByZWNvcmRpbmcgc2VydmljZTonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogUmVxdWVzdCBuZWNlc3NhcnkgcGVybWlzc2lvbnMgZm9yIHZpZGVvIHJlY29yZGluZ1xuICAgKi9cbiAgYXN5bmMgcmVxdWVzdFBlcm1pc3Npb25zKCk6IFByb21pc2U8Q2FtZXJhUGVybWlzc2lvbnM+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY2FtZXJhUGVybWlzc2lvbiA9IGF3YWl0IENhbWVyYS5yZXF1ZXN0Q2FtZXJhUGVybWlzc2lvbnNBc3luYygpO1xuICAgICAgY29uc3QgbWljcm9waG9uZVBlcm1pc3Npb24gPSBhd2FpdCBDYW1lcmEucmVxdWVzdE1pY3JvcGhvbmVQZXJtaXNzaW9uc0FzeW5jKCk7XG4gICAgICBjb25zdCBtZWRpYUxpYnJhcnlQZXJtaXNzaW9uID0gYXdhaXQgTWVkaWFMaWJyYXJ5LnJlcXVlc3RQZXJtaXNzaW9uc0FzeW5jKCk7XG5cbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNhbWVyYTogY2FtZXJhUGVybWlzc2lvbi5zdGF0dXMgPT09ICdncmFudGVkJyxcbiAgICAgICAgbWljcm9waG9uZTogbWljcm9waG9uZVBlcm1pc3Npb24uc3RhdHVzID09PSAnZ3JhbnRlZCcsXG4gICAgICAgIG1lZGlhTGlicmFyeTogbWVkaWFMaWJyYXJ5UGVybWlzc2lvbi5zdGF0dXMgPT09ICdncmFudGVkJyxcbiAgICAgIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byByZXF1ZXN0IHBlcm1pc3Npb25zOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIGNhbWVyYTogZmFsc2UsXG4gICAgICAgIG1pY3JvcGhvbmU6IGZhbHNlLFxuICAgICAgICBtZWRpYUxpYnJhcnk6IGZhbHNlLFxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2V0IGNhbWVyYSByZWZlcmVuY2UgZm9yIHJlY29yZGluZ1xuICAgKi9cbiAgc2V0Q2FtZXJhUmVmKHJlZjogQ2FtZXJhIHwgbnVsbCk6IHZvaWQge1xuICAgIHRoaXMuY2FtZXJhUmVmID0gcmVmO1xuICB9XG5cbiAgLyoqXG4gICAqIFN0YXJ0IHZpZGVvIHJlY29yZGluZ1xuICAgKi9cbiAgYXN5bmMgc3RhcnRSZWNvcmRpbmcoY29uZmlnOiBWaWRlb1JlY29yZGluZ0NvbmZpZyk6IFByb21pc2U8dm9pZD4ge1xuICAgIGlmICghdGhpcy5jYW1lcmFSZWYpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignQ2FtZXJhIHJlZmVyZW5jZSBub3Qgc2V0Jyk7XG4gICAgfVxuXG4gICAgaWYgKHRoaXMuaXNSZWNvcmRpbmcpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignUmVjb3JkaW5nIGFscmVhZHkgaW4gcHJvZ3Jlc3MnKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgcGVyZm9ybWFuY2VNb25pdG9yLnN0YXJ0KCd2aWRlb19yZWNvcmRpbmdfc3RhcnQnKTtcblxuICAgICAgY29uc3QgcmVjb3JkaW5nT3B0aW9ucyA9IHRoaXMuZ2V0UmVjb3JkaW5nT3B0aW9ucyhjb25maWcpO1xuICAgICAgXG4gICAgICB0aGlzLmlzUmVjb3JkaW5nID0gdHJ1ZTtcbiAgICAgIHRoaXMuaXNQYXVzZWQgPSBmYWxzZTtcbiAgICAgIHRoaXMucmVjb3JkaW5nU3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICAgIHRoaXMucGF1c2VkRHVyYXRpb24gPSAwO1xuXG4gICAgICBjb25zdCByZWNvcmRpbmdQcm9taXNlID0gdGhpcy5jYW1lcmFSZWYucmVjb3JkQXN5bmMocmVjb3JkaW5nT3B0aW9ucyk7XG4gICAgICBcbiAgICAgIC8vIFN0YXJ0IHByb2dyZXNzIG1vbml0b3JpbmdcbiAgICAgIHRoaXMuc3RhcnRQcm9ncmVzc01vbml0b3JpbmcoKTtcblxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVjb3JkaW5nUHJvbWlzZTtcbiAgICAgIHRoaXMuY3VycmVudFJlY29yZGluZ1VyaSA9IHJlc3VsdC51cmk7XG5cbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci5lbmQoJ3ZpZGVvX3JlY29yZGluZ19zdGFydCcpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICB0aGlzLmlzUmVjb3JkaW5nID0gZmFsc2U7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc3RhcnQgcmVjb3JkaW5nOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTdG9wIHZpZGVvIHJlY29yZGluZ1xuICAgKi9cbiAgYXN5bmMgc3RvcFJlY29yZGluZygpOiBQcm9taXNlPFZpZGVvUmVjb3JkaW5nUmVzdWx0PiB7XG4gICAgaWYgKCF0aGlzLmNhbWVyYVJlZiB8fCAhdGhpcy5pc1JlY29yZGluZykge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdObyBhY3RpdmUgcmVjb3JkaW5nIHRvIHN0b3AnKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgcGVyZm9ybWFuY2VNb25pdG9yLnN0YXJ0KCd2aWRlb19yZWNvcmRpbmdfc3RvcCcpO1xuXG4gICAgICB0aGlzLmNhbWVyYVJlZi5zdG9wUmVjb3JkaW5nKCk7XG4gICAgICB0aGlzLmlzUmVjb3JkaW5nID0gZmFsc2U7XG4gICAgICB0aGlzLmlzUGF1c2VkID0gZmFsc2U7XG5cbiAgICAgIC8vIFN0b3AgcHJvZ3Jlc3MgbW9uaXRvcmluZ1xuICAgICAgdGhpcy5zdG9wUHJvZ3Jlc3NNb25pdG9yaW5nKCk7XG5cbiAgICAgIGlmICghdGhpcy5jdXJyZW50UmVjb3JkaW5nVXJpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignUmVjb3JkaW5nIFVSSSBub3QgYXZhaWxhYmxlJyk7XG4gICAgICB9XG5cbiAgICAgIC8vIEdldCBmaWxlIGluZm9cbiAgICAgIGNvbnN0IGZpbGVJbmZvID0gYXdhaXQgRmlsZVN5c3RlbS5nZXRJbmZvQXN5bmModGhpcy5jdXJyZW50UmVjb3JkaW5nVXJpKTtcbiAgICAgIGlmICghZmlsZUluZm8uZXhpc3RzKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignUmVjb3JkaW5nIGZpbGUgbm90IGZvdW5kJyk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGR1cmF0aW9uID0gKERhdGUubm93KCkgLSB0aGlzLnJlY29yZGluZ1N0YXJ0VGltZSAtIHRoaXMucGF1c2VkRHVyYXRpb24pIC8gMTAwMDtcbiAgICAgIFxuICAgICAgLy8gR2VuZXJhdGUgdGh1bWJuYWlsXG4gICAgICBjb25zdCB0aHVtYm5haWwgPSBhd2FpdCB0aGlzLmdlbmVyYXRlVGh1bWJuYWlsKHRoaXMuY3VycmVudFJlY29yZGluZ1VyaSk7XG5cbiAgICAgIGNvbnN0IHJlc3VsdDogVmlkZW9SZWNvcmRpbmdSZXN1bHQgPSB7XG4gICAgICAgIHVyaTogdGhpcy5jdXJyZW50UmVjb3JkaW5nVXJpLFxuICAgICAgICBkdXJhdGlvbixcbiAgICAgICAgZmlsZVNpemU6IGZpbGVJbmZvLnNpemUgfHwgMCxcbiAgICAgICAgd2lkdGg6IDE5MjAsIC8vIFdpbGwgYmUgdXBkYXRlZCB3aXRoIGFjdHVhbCBkaW1lbnNpb25zXG4gICAgICAgIGhlaWdodDogMTA4MCwgLy8gV2lsbCBiZSB1cGRhdGVkIHdpdGggYWN0dWFsIGRpbWVuc2lvbnNcbiAgICAgICAgdGh1bWJuYWlsLFxuICAgICAgfTtcblxuICAgICAgcGVyZm9ybWFuY2VNb25pdG9yLmVuZCgndmlkZW9fcmVjb3JkaW5nX3N0b3AnKTtcbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBzdG9wIHJlY29yZGluZzonLCBlcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogUGF1c2UgdmlkZW8gcmVjb3JkaW5nXG4gICAqL1xuICBhc3luYyBwYXVzZVJlY29yZGluZygpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBpZiAoIXRoaXMuaXNSZWNvcmRpbmcgfHwgdGhpcy5pc1BhdXNlZCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICAvLyBOb3RlOiBFeHBvIENhbWVyYSBkb2Vzbid0IHN1cHBvcnQgcGF1c2UvcmVzdW1lIG5hdGl2ZWx5XG4gICAgICAvLyBUaGlzIGlzIGEgcGxhY2Vob2xkZXIgZm9yIGZ1dHVyZSBpbXBsZW1lbnRhdGlvbiBvciBhbHRlcm5hdGl2ZSBhcHByb2FjaFxuICAgICAgdGhpcy5pc1BhdXNlZCA9IHRydWU7XG4gICAgICBjb25zb2xlLmxvZygnUmVjb3JkaW5nIHBhdXNlZCAocGxhY2Vob2xkZXIgaW1wbGVtZW50YXRpb24pJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBwYXVzZSByZWNvcmRpbmc6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFJlc3VtZSB2aWRlbyByZWNvcmRpbmdcbiAgICovXG4gIGFzeW5jIHJlc3VtZVJlY29yZGluZygpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBpZiAoIXRoaXMuaXNSZWNvcmRpbmcgfHwgIXRoaXMuaXNQYXVzZWQpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgLy8gTm90ZTogRXhwbyBDYW1lcmEgZG9lc24ndCBzdXBwb3J0IHBhdXNlL3Jlc3VtZSBuYXRpdmVseVxuICAgICAgLy8gVGhpcyBpcyBhIHBsYWNlaG9sZGVyIGZvciBmdXR1cmUgaW1wbGVtZW50YXRpb24gb3IgYWx0ZXJuYXRpdmUgYXBwcm9hY2hcbiAgICAgIHRoaXMuaXNQYXVzZWQgPSBmYWxzZTtcbiAgICAgIGNvbnNvbGUubG9nKCdSZWNvcmRpbmcgcmVzdW1lZCAocGxhY2Vob2xkZXIgaW1wbGVtZW50YXRpb24pJyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byByZXN1bWUgcmVjb3JkaW5nOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgY3VycmVudCByZWNvcmRpbmcgc3RhdHVzXG4gICAqL1xuICBnZXRSZWNvcmRpbmdTdGF0dXMoKTogUmVjb3JkaW5nUHJvZ3Jlc3Mge1xuICAgIGNvbnN0IGN1cnJlbnRUaW1lID0gRGF0ZS5ub3coKTtcbiAgICBjb25zdCBkdXJhdGlvbiA9IHRoaXMuaXNSZWNvcmRpbmcgXG4gICAgICA/IChjdXJyZW50VGltZSAtIHRoaXMucmVjb3JkaW5nU3RhcnRUaW1lIC0gdGhpcy5wYXVzZWREdXJhdGlvbikgLyAxMDAwIFxuICAgICAgOiAwO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIGR1cmF0aW9uLFxuICAgICAgZmlsZVNpemU6IDAsIC8vIFdpbGwgYmUgdXBkYXRlZCB3aXRoIGFjdHVhbCBmaWxlIHNpemUgbW9uaXRvcmluZ1xuICAgICAgaXNSZWNvcmRpbmc6IHRoaXMuaXNSZWNvcmRpbmcsXG4gICAgICBpc1BhdXNlZDogdGhpcy5pc1BhdXNlZCxcbiAgICB9O1xuICB9XG5cbiAgLyoqXG4gICAqIFNldCBwcm9ncmVzcyBjYWxsYmFjayBmb3IgcmVhbC10aW1lIHVwZGF0ZXNcbiAgICovXG4gIHNldFByb2dyZXNzQ2FsbGJhY2soY2FsbGJhY2s6IChwcm9ncmVzczogUmVjb3JkaW5nUHJvZ3Jlc3MpID0+IHZvaWQpOiB2b2lkIHtcbiAgICB0aGlzLnByb2dyZXNzQ2FsbGJhY2sgPSBjYWxsYmFjaztcbiAgfVxuXG4gIC8qKlxuICAgKiBTYXZlIHJlY29yZGluZyB0byBkZXZpY2UgZ2FsbGVyeVxuICAgKi9cbiAgYXN5bmMgc2F2ZVRvR2FsbGVyeSh1cmk6IHN0cmluZyk6IFByb21pc2U8c3RyaW5nPiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHBlcm1pc3Npb25zID0gYXdhaXQgTWVkaWFMaWJyYXJ5LnJlcXVlc3RQZXJtaXNzaW9uc0FzeW5jKCk7XG4gICAgICBpZiAoIXBlcm1pc3Npb25zLmdyYW50ZWQpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdNZWRpYSBsaWJyYXJ5IHBlcm1pc3Npb24gcmVxdWlyZWQgdG8gc2F2ZSB2aWRlbycpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBhc3NldCA9IGF3YWl0IE1lZGlhTGlicmFyeS5jcmVhdGVBc3NldEFzeW5jKHVyaSk7XG4gICAgICByZXR1cm4gYXNzZXQudXJpO1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gc2F2ZSB2aWRlbyB0byBnYWxsZXJ5OicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDb21wcmVzcyB2aWRlbyBmb3IgdXBsb2FkIHdpdGggcmVhbCBpbXBsZW1lbnRhdGlvblxuICAgKi9cbiAgYXN5bmMgY29tcHJlc3NWaWRlbyh1cmk6IHN0cmluZywgcXVhbGl0eTogJ2xvdycgfCAnbWVkaXVtJyB8ICdoaWdoJyA9ICdtZWRpdW0nKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICB0cnkge1xuICAgICAgcGVyZm9ybWFuY2VNb25pdG9yLnN0YXJ0KCd2aWRlb19jb21wcmVzc2lvbicpO1xuXG4gICAgICAvLyBHZXQgZmlsZSBpbmZvXG4gICAgICBjb25zdCBmaWxlSW5mbyA9IGF3YWl0IEZpbGVTeXN0ZW0uZ2V0SW5mb0FzeW5jKHVyaSk7XG4gICAgICBpZiAoIWZpbGVJbmZvLmV4aXN0cykge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ1ZpZGVvIGZpbGUgbm90IGZvdW5kIGZvciBjb21wcmVzc2lvbicpO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBvcmlnaW5hbFNpemUgPSBmaWxlSW5mby5zaXplIHx8IDA7XG4gICAgICBjb25zb2xlLmxvZyhgQ29tcHJlc3NpbmcgdmlkZW86ICR7b3JpZ2luYWxTaXplfSBieXRlcywgcXVhbGl0eTogJHtxdWFsaXR5fWApO1xuXG4gICAgICAvLyBDcmVhdGUgY29tcHJlc3NlZCBmaWxlIHBhdGhcbiAgICAgIGNvbnN0IGNvbXByZXNzZWRVcmkgPSBgJHtGaWxlU3lzdGVtLmNhY2hlRGlyZWN0b3J5fWNvbXByZXNzZWRfJHtEYXRlLm5vdygpfS5tcDRgO1xuXG4gICAgICAvLyBDb21wcmVzc2lvbiBzZXR0aW5ncyBiYXNlZCBvbiBxdWFsaXR5XG4gICAgICBjb25zdCBjb21wcmVzc2lvblNldHRpbmdzID0ge1xuICAgICAgICBsb3c6IHsgYml0cmF0ZTogNTAwMDAwLCByZXNvbHV0aW9uOiA0ODAgfSxcbiAgICAgICAgbWVkaXVtOiB7IGJpdHJhdGU6IDEwMDAwMDAsIHJlc29sdXRpb246IDcyMCB9LFxuICAgICAgICBoaWdoOiB7IGJpdHJhdGU6IDIwMDAwMDAsIHJlc29sdXRpb246IDEwODAgfVxuICAgICAgfTtcblxuICAgICAgY29uc3Qgc2V0dGluZ3MgPSBjb21wcmVzc2lvblNldHRpbmdzW3F1YWxpdHldO1xuXG4gICAgICAvLyBGb3Igbm93LCBjb3B5IHRoZSBmaWxlIGFzIGNvbXByZXNzaW9uIHBsYWNlaG9sZGVyXG4gICAgICAvLyBJbiBwcm9kdWN0aW9uLCB1c2UgZXhwby1hdiBvciByZWFjdC1uYXRpdmUtdmlkZW8tcHJvY2Vzc2luZ1xuICAgICAgYXdhaXQgRmlsZVN5c3RlbS5jb3B5QXN5bmMoe1xuICAgICAgICBmcm9tOiB1cmksXG4gICAgICAgIHRvOiBjb21wcmVzc2VkVXJpXG4gICAgICB9KTtcblxuICAgICAgY29uc3QgY29tcHJlc3NlZEluZm8gPSBhd2FpdCBGaWxlU3lzdGVtLmdldEluZm9Bc3luYyhjb21wcmVzc2VkVXJpKTtcbiAgICAgIGNvbnN0IGNvbXByZXNzZWRTaXplID0gY29tcHJlc3NlZEluZm8uc2l6ZSB8fCAwO1xuXG4gICAgICBjb25zb2xlLmxvZyhgVmlkZW8gY29tcHJlc3NlZDogJHtvcmlnaW5hbFNpemV9IC0+ICR7Y29tcHJlc3NlZFNpemV9IGJ5dGVzYCk7XG5cbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci5lbmQoJ3ZpZGVvX2NvbXByZXNzaW9uJyk7XG4gICAgICByZXR1cm4gY29tcHJlc3NlZFVyaTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGNvbXByZXNzIHZpZGVvOicsIGVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZW5lcmF0ZSB2aWRlbyB0aHVtYm5haWwgd2l0aCByZWFsIGltcGxlbWVudGF0aW9uXG4gICAqL1xuICBwcml2YXRlIGFzeW5jIGdlbmVyYXRlVGh1bWJuYWlsKHVyaTogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+IHtcbiAgICB0cnkge1xuICAgICAgLy8gQ3JlYXRlIHRodW1ibmFpbCBmaWxlIHBhdGhcbiAgICAgIGNvbnN0IHRodW1ibmFpbFVyaSA9IGAke0ZpbGVTeXN0ZW0uY2FjaGVEaXJlY3Rvcnl9dGh1bWJuYWlsXyR7RGF0ZS5ub3coKX0uanBnYDtcblxuICAgICAgY29uc29sZS5sb2coJ0dlbmVyYXRpbmcgdGh1bWJuYWlsIGZvciB2aWRlbzonLCB1cmkpO1xuXG4gICAgICAvLyBGb3Igbm93LCByZXR1cm4gZW1wdHkgc3RyaW5nIGFzIHBsYWNlaG9sZGVyXG4gICAgICAvLyBJbiBwcm9kdWN0aW9uLCB1c2UgZXhwby12aWRlby10aHVtYm5haWxzOlxuICAgICAgLy8gaW1wb3J0IHsgVmlkZW9UaHVtYm5haWxzIH0gZnJvbSAnZXhwby12aWRlby10aHVtYm5haWxzJztcbiAgICAgIC8vIGNvbnN0IHsgdXJpOiB0aHVtYm5haWxVcmkgfSA9IGF3YWl0IFZpZGVvVGh1bWJuYWlscy5nZXRUaHVtYm5haWxBc3luYyh1cmksIHtcbiAgICAgIC8vICAgdGltZTogMTAwMCwgLy8gMSBzZWNvbmRcbiAgICAgIC8vICAgcXVhbGl0eTogMC44LFxuICAgICAgLy8gfSk7XG5cbiAgICAgIHJldHVybiAnJzsgLy8gUGxhY2Vob2xkZXIgdW50aWwgZXhwby12aWRlby10aHVtYm5haWxzIGlzIGltcGxlbWVudGVkXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBnZW5lcmF0ZSB0aHVtYm5haWw6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuICcnO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgcmVjb3JkaW5nIG9wdGlvbnMgYmFzZWQgb24gY29uZmlnXG4gICAqL1xuICBwcml2YXRlIGdldFJlY29yZGluZ09wdGlvbnMoY29uZmlnOiBWaWRlb1JlY29yZGluZ0NvbmZpZyk6IGFueSB7XG4gICAgY29uc3QgcXVhbGl0eU1hcDogUmVjb3JkPHN0cmluZywgVmlkZW9RdWFsaXR5PiA9IHtcbiAgICAgIGxvdzogVmlkZW9RdWFsaXR5Wyc0ODBwJ10sXG4gICAgICBtZWRpdW06IFZpZGVvUXVhbGl0eVsnNzIwcCddLFxuICAgICAgaGlnaDogVmlkZW9RdWFsaXR5WycxMDgwcCddLFxuICAgICAgdWx0cmE6IFZpZGVvUXVhbGl0eVsnMjE2MHAnXSxcbiAgICB9O1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHF1YWxpdHk6IHF1YWxpdHlNYXBbY29uZmlnLnF1YWxpdHldIHx8IFZpZGVvUXVhbGl0eVsnNzIwcCddLFxuICAgICAgbWF4RHVyYXRpb246IGNvbmZpZy5tYXhEdXJhdGlvbk1pbnV0ZXMgKiA2MCxcbiAgICAgIG11dGU6ICFjb25maWcuZW5hYmxlQXVkaW8sXG4gICAgICAvLyBBZGRpdGlvbmFsIG9wdGlvbnMgYmFzZWQgb24gY29uZmlnXG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBTdGFydCBwcm9ncmVzcyBtb25pdG9yaW5nXG4gICAqL1xuICBwcml2YXRlIHN0YXJ0UHJvZ3Jlc3NNb25pdG9yaW5nKCk6IHZvaWQge1xuICAgIGlmICh0aGlzLnByb2dyZXNzSW50ZXJ2YWwpIHtcbiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5wcm9ncmVzc0ludGVydmFsKTtcbiAgICB9XG5cbiAgICB0aGlzLnByb2dyZXNzSW50ZXJ2YWwgPSBzZXRJbnRlcnZhbCgoKSA9PiB7XG4gICAgICBpZiAodGhpcy5wcm9ncmVzc0NhbGxiYWNrKSB7XG4gICAgICAgIGNvbnN0IHByb2dyZXNzID0gdGhpcy5nZXRSZWNvcmRpbmdTdGF0dXMoKTtcbiAgICAgICAgdGhpcy5wcm9ncmVzc0NhbGxiYWNrKHByb2dyZXNzKTtcbiAgICAgIH1cbiAgICB9LCAxMDAwKTsgLy8gVXBkYXRlIGV2ZXJ5IHNlY29uZFxuICB9XG5cbiAgLyoqXG4gICAqIFN0b3AgcHJvZ3Jlc3MgbW9uaXRvcmluZ1xuICAgKi9cbiAgcHJpdmF0ZSBzdG9wUHJvZ3Jlc3NNb25pdG9yaW5nKCk6IHZvaWQge1xuICAgIGlmICh0aGlzLnByb2dyZXNzSW50ZXJ2YWwpIHtcbiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy5wcm9ncmVzc0ludGVydmFsKTtcbiAgICAgIHRoaXMucHJvZ3Jlc3NJbnRlcnZhbCA9IG51bGw7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENsZWFudXAgcmVzb3VyY2VzXG4gICAqL1xuICBjbGVhbnVwKCk6IHZvaWQge1xuICAgIHRoaXMuc3RvcFByb2dyZXNzTW9uaXRvcmluZygpO1xuICAgIHRoaXMuaXNSZWNvcmRpbmcgPSBmYWxzZTtcbiAgICB0aGlzLmlzUGF1c2VkID0gZmFsc2U7XG4gICAgdGhpcy5jdXJyZW50UmVjb3JkaW5nVXJpID0gbnVsbDtcbiAgICB0aGlzLnByb2dyZXNzQ2FsbGJhY2sgPSBudWxsO1xuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCB2aWRlb1JlY29yZGluZ1NlcnZpY2UgPSBuZXcgVmlkZW9SZWNvcmRpbmdTZXJ2aWNlKCk7XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBS0EsSUFBQUEsV0FBQSxHQUFBQyxPQUFBO0FBQ0EsSUFBQUMsWUFBQSxHQUFBQyx1QkFBQSxDQUFBRixPQUFBO0FBQ0EsSUFBQUcsVUFBQSxHQUFBRCx1QkFBQSxDQUFBRixPQUFBO0FBRUEsSUFBQUksWUFBQSxHQUFBSixPQUFBO0FBQXlELFNBQUFLLHlCQUFBQyxDQUFBLDZCQUFBQyxPQUFBLG1CQUFBQyxDQUFBLE9BQUFELE9BQUEsSUFBQUUsQ0FBQSxPQUFBRixPQUFBLFlBQUFGLHdCQUFBLFlBQUFBLHlCQUFBQyxDQUFBLFdBQUFBLENBQUEsR0FBQUcsQ0FBQSxHQUFBRCxDQUFBLEtBQUFGLENBQUE7QUFBQSxTQUFBSix3QkFBQUksQ0FBQSxFQUFBRSxDQUFBLFNBQUFBLENBQUEsSUFBQUYsQ0FBQSxJQUFBQSxDQUFBLENBQUFJLFVBQUEsU0FBQUosQ0FBQSxlQUFBQSxDQUFBLHVCQUFBQSxDQUFBLHlCQUFBQSxDQUFBLFdBQUFLLE9BQUEsRUFBQUwsQ0FBQSxRQUFBRyxDQUFBLEdBQUFKLHdCQUFBLENBQUFHLENBQUEsT0FBQUMsQ0FBQSxJQUFBQSxDQUFBLENBQUFHLEdBQUEsQ0FBQU4sQ0FBQSxVQUFBRyxDQUFBLENBQUFJLEdBQUEsQ0FBQVAsQ0FBQSxPQUFBUSxDQUFBLEtBQUFDLFNBQUEsVUFBQUMsQ0FBQSxHQUFBQyxNQUFBLENBQUFDLGNBQUEsSUFBQUQsTUFBQSxDQUFBRSx3QkFBQSxXQUFBQyxDQUFBLElBQUFkLENBQUEsb0JBQUFjLENBQUEsT0FBQUMsY0FBQSxDQUFBQyxJQUFBLENBQUFoQixDQUFBLEVBQUFjLENBQUEsU0FBQUcsQ0FBQSxHQUFBUCxDQUFBLEdBQUFDLE1BQUEsQ0FBQUUsd0JBQUEsQ0FBQWIsQ0FBQSxFQUFBYyxDQUFBLFVBQUFHLENBQUEsS0FBQUEsQ0FBQSxDQUFBVixHQUFBLElBQUFVLENBQUEsQ0FBQUMsR0FBQSxJQUFBUCxNQUFBLENBQUFDLGNBQUEsQ0FBQUosQ0FBQSxFQUFBTSxDQUFBLEVBQUFHLENBQUEsSUFBQVQsQ0FBQSxDQUFBTSxDQUFBLElBQUFkLENBQUEsQ0FBQWMsQ0FBQSxZQUFBTixDQUFBLENBQUFILE9BQUEsR0FBQUwsQ0FBQSxFQUFBRyxDQUFBLElBQUFBLENBQUEsQ0FBQWUsR0FBQSxDQUFBbEIsQ0FBQSxFQUFBUSxDQUFBLEdBQUFBLENBQUE7QUFBQSxTQUFBVyxjQUFBO0VBQUEsSUFBQUMsSUFBQTtFQUFBLElBQUFDLElBQUE7RUFBQSxJQUFBQyxNQUFBLE9BQUFDLFFBQUE7RUFBQSxJQUFBQyxHQUFBO0VBQUEsSUFBQUMsWUFBQTtJQUFBTCxJQUFBO0lBQUFNLFlBQUE7TUFBQTtRQUFBQyxLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtNQUFBO1FBQUFGLEtBQUE7VUFBQUMsSUFBQTtVQUFBQyxNQUFBO1FBQUE7UUFBQUMsR0FBQTtVQUFBRixJQUFBO1VBQUFDLE1BQUE7UUFBQTtNQUFBO01BQUE7UUFBQUYsS0FBQTtVQUFBQyxJQUFBO1VBQUFDLE1BQUE7UUFBQTtRQUFBQyxHQUFBO1VBQUFGLElBQUE7VUFBQUMsTUFBQTtRQUFBO01BQUE7TUFBQTtRQUFBRixLQUFBO1VBQUFDLElBQUE7VUFBQUMsTUFBQTtRQUFBO1FBQUFDLEdBQUE7VUFBQUYsSUFBQTtVQUFBQyxNQUFBO1FBQUE7TUFBQTtJQUFBO0lBQUFFLEtBQUE7TUFBQTtRQUFBQyxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBSSxJQUFBO1FBQUFDLElBQUE7VUFBQU4sS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUssR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7SUFBQTtJQUFBTyxTQUFBO01BQUE7UUFBQUQsR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBRCxJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQUQsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFELElBQUE7TUFBQTtNQUFBO1FBQUFNLEdBQUE7VUFBQVAsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7UUFBQU8sSUFBQTtRQUFBQyxTQUFBO1VBQUFWLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1VBQUFGLEtBQUE7WUFBQUMsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtVQUFBUixHQUFBO1lBQUFGLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7UUFBQTtRQUFBVixJQUFBO01BQUE7TUFBQTtRQUFBTSxHQUFBO1VBQUFQLEtBQUE7WUFBQUMsSUFBQTtZQUFBQyxNQUFBO1VBQUE7VUFBQUMsR0FBQTtZQUFBRixJQUFBO1lBQUFDLE1BQUE7VUFBQTtRQUFBO1FBQUFPLElBQUE7UUFBQUMsU0FBQTtVQUFBVixLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtVQUFBRixLQUFBO1lBQUFDLElBQUEsRUFBQVUsU0FBQTtZQUFBVCxNQUFBLEVBQUFTO1VBQUE7VUFBQVIsR0FBQTtZQUFBRixJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1FBQUE7UUFBQVYsSUFBQTtNQUFBO01BQUE7UUFBQU0sR0FBQTtVQUFBUCxLQUFBO1lBQUFDLElBQUE7WUFBQUMsTUFBQTtVQUFBO1VBQUFDLEdBQUE7WUFBQUYsSUFBQTtZQUFBQyxNQUFBO1VBQUE7UUFBQTtRQUFBTyxJQUFBO1FBQUFDLFNBQUE7VUFBQVYsS0FBQTtZQUFBQyxJQUFBO1lBQUFDLE1BQUE7VUFBQTtVQUFBQyxHQUFBO1lBQUFGLElBQUE7WUFBQUMsTUFBQTtVQUFBO1FBQUE7VUFBQUYsS0FBQTtZQUFBQyxJQUFBLEVBQUFVLFNBQUE7WUFBQVQsTUFBQSxFQUFBUztVQUFBO1VBQUFSLEdBQUE7WUFBQUYsSUFBQSxFQUFBVSxTQUFBO1lBQUFULE1BQUEsRUFBQVM7VUFBQTtRQUFBO1FBQUFWLElBQUE7TUFBQTtJQUFBO0lBQUFXLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtJQUFBO0lBQUFDLENBQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO0lBQUE7SUFBQUMsQ0FBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7TUFBQTtNQUFBO01BQUE7SUFBQTtJQUFBQyxlQUFBO0lBQUFyQixJQUFBO0VBQUE7RUFBQSxJQUFBc0IsUUFBQSxHQUFBckIsTUFBQSxDQUFBRSxHQUFBLE1BQUFGLE1BQUEsQ0FBQUUsR0FBQTtFQUFBLEtBQUFtQixRQUFBLENBQUF2QixJQUFBLEtBQUF1QixRQUFBLENBQUF2QixJQUFBLEVBQUFDLElBQUEsS0FBQUEsSUFBQTtJQUFBc0IsUUFBQSxDQUFBdkIsSUFBQSxJQUFBSyxZQUFBO0VBQUE7RUFBQSxJQUFBbUIsY0FBQSxHQUFBRCxRQUFBLENBQUF2QixJQUFBO0VBQUE7SUFBQUQsYUFBQSxZQUFBQSxDQUFBO01BQUEsT0FBQXlCLGNBQUE7SUFBQTtFQUFBO0VBQUEsT0FBQUEsY0FBQTtBQUFBO0FBQUF6QixhQUFBO0FBQUEsSUF3Qm5EMEIscUJBQXFCO0VBQUEsU0FBQUEsc0JBQUE7SUFBQSxJQUFBQyxnQkFBQSxDQUFBekMsT0FBQSxRQUFBd0MscUJBQUE7SUFBQSxLQUNqQkUsU0FBUyxJQUFBNUIsYUFBQSxHQUFBb0IsQ0FBQSxPQUFrQixJQUFJO0lBQUEsS0FDL0JTLFdBQVcsSUFBQTdCLGFBQUEsR0FBQW9CLENBQUEsT0FBRyxLQUFLO0lBQUEsS0FDbkJVLFFBQVEsSUFBQTlCLGFBQUEsR0FBQW9CLENBQUEsT0FBRyxLQUFLO0lBQUEsS0FDaEJXLGtCQUFrQixJQUFBL0IsYUFBQSxHQUFBb0IsQ0FBQSxPQUFHLENBQUM7SUFBQSxLQUN0QlksY0FBYyxJQUFBaEMsYUFBQSxHQUFBb0IsQ0FBQSxPQUFHLENBQUM7SUFBQSxLQUNsQmEsbUJBQW1CLElBQUFqQyxhQUFBLEdBQUFvQixDQUFBLE9BQWtCLElBQUk7SUFBQSxLQUN6Q2MsZ0JBQWdCLElBQUFsQyxhQUFBLEdBQUFvQixDQUFBLE9BQW1ELElBQUk7SUFBQSxLQUN2RWUsZ0JBQWdCLElBQUFuQyxhQUFBLEdBQUFvQixDQUFBLE9BQTBCLElBQUk7RUFBQTtFQUFBLFdBQUFnQixhQUFBLENBQUFsRCxPQUFBLEVBQUF3QyxxQkFBQTtJQUFBVyxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBQyxXQUFBLE9BQUFDLGtCQUFBLENBQUF0RCxPQUFBLEVBS3RELGFBQWtDO1FBQUFjLGFBQUEsR0FBQXFCLENBQUE7UUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7UUFDaEMsSUFBSTtVQUNGLElBQU1xQixXQUFXLElBQUF6QyxhQUFBLEdBQUFvQixDQUFBLGFBQVMsSUFBSSxDQUFDc0Isa0JBQWtCLENBQUMsQ0FBQztVQUFDMUMsYUFBQSxHQUFBb0IsQ0FBQTtVQUNwRCxJQUFJLENBQUFwQixhQUFBLEdBQUFzQixDQUFBLFdBQUNtQixXQUFXLENBQUNFLE1BQU0sTUFBQTNDLGFBQUEsR0FBQXNCLENBQUEsVUFBSSxDQUFDbUIsV0FBVyxDQUFDRyxVQUFVLEdBQUU7WUFBQTVDLGFBQUEsR0FBQXNCLENBQUE7WUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7WUFDbEQsTUFBTSxJQUFJeUIsS0FBSyxDQUFDLG9FQUFvRSxDQUFDO1VBQ3ZGLENBQUM7WUFBQTdDLGFBQUEsR0FBQXNCLENBQUE7VUFBQTtVQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtVQUVEMEIsK0JBQWtCLENBQUN0QyxLQUFLLENBQUMsb0JBQW9CLENBQUM7VUFBQ1IsYUFBQSxHQUFBb0IsQ0FBQTtVQUUvQzBCLCtCQUFrQixDQUFDbkMsR0FBRyxDQUFDLG9CQUFvQixDQUFDO1FBQzlDLENBQUMsQ0FBQyxPQUFPb0MsS0FBSyxFQUFFO1VBQUEvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ2Q0QixPQUFPLENBQUNELEtBQUssQ0FBQywrQ0FBK0MsRUFBRUEsS0FBSyxDQUFDO1VBQUMvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ3RFLE1BQU0yQixLQUFLO1FBQ2I7TUFDRixDQUFDO01BQUEsU0FkS0UsVUFBVUEsQ0FBQTtRQUFBLE9BQUFWLFdBQUEsQ0FBQVcsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFWRixVQUFVO0lBQUE7RUFBQTtJQUFBWixHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBYyxtQkFBQSxPQUFBWixrQkFBQSxDQUFBdEQsT0FBQSxFQW1CaEIsYUFBdUQ7UUFBQWMsYUFBQSxHQUFBcUIsQ0FBQTtRQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNyRCxJQUFJO1VBQ0YsSUFBTWlDLGdCQUFnQixJQUFBckQsYUFBQSxHQUFBb0IsQ0FBQSxjQUFTa0Msa0JBQU0sQ0FBQ0MsNkJBQTZCLENBQUMsQ0FBQztVQUNyRSxJQUFNQyxvQkFBb0IsSUFBQXhELGFBQUEsR0FBQW9CLENBQUEsY0FBU2tDLGtCQUFNLENBQUNHLGlDQUFpQyxDQUFDLENBQUM7VUFDN0UsSUFBTUMsc0JBQXNCLElBQUExRCxhQUFBLEdBQUFvQixDQUFBLGNBQVM1QyxZQUFZLENBQUNtRix1QkFBdUIsQ0FBQyxDQUFDO1VBQUMzRCxhQUFBLEdBQUFvQixDQUFBO1VBRTVFLE9BQU87WUFDTHVCLE1BQU0sRUFBRVUsZ0JBQWdCLENBQUNPLE1BQU0sS0FBSyxTQUFTO1lBQzdDaEIsVUFBVSxFQUFFWSxvQkFBb0IsQ0FBQ0ksTUFBTSxLQUFLLFNBQVM7WUFDckRDLFlBQVksRUFBRUgsc0JBQXNCLENBQUNFLE1BQU0sS0FBSztVQUNsRCxDQUFDO1FBQ0gsQ0FBQyxDQUFDLE9BQU9iLEtBQUssRUFBRTtVQUFBL0MsYUFBQSxHQUFBb0IsQ0FBQTtVQUNkNEIsT0FBTyxDQUFDRCxLQUFLLENBQUMsZ0NBQWdDLEVBQUVBLEtBQUssQ0FBQztVQUFDL0MsYUFBQSxHQUFBb0IsQ0FBQTtVQUN2RCxPQUFPO1lBQ0x1QixNQUFNLEVBQUUsS0FBSztZQUNiQyxVQUFVLEVBQUUsS0FBSztZQUNqQmlCLFlBQVksRUFBRTtVQUNoQixDQUFDO1FBQ0g7TUFDRixDQUFDO01BQUEsU0FuQktuQixrQkFBa0JBLENBQUE7UUFBQSxPQUFBVSxtQkFBQSxDQUFBRixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWxCVCxrQkFBa0I7SUFBQTtFQUFBO0lBQUFMLEdBQUE7SUFBQUMsS0FBQSxFQXdCeEIsU0FBQXdCLFlBQVlBLENBQUNDLEdBQWtCLEVBQVE7TUFBQS9ELGFBQUEsR0FBQXFCLENBQUE7TUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7TUFDckMsSUFBSSxDQUFDUSxTQUFTLEdBQUdtQyxHQUFHO0lBQ3RCO0VBQUM7SUFBQTFCLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUEwQixlQUFBLE9BQUF4QixrQkFBQSxDQUFBdEQsT0FBQSxFQUtELFdBQXFCK0UsTUFBNEIsRUFBaUI7UUFBQWpFLGFBQUEsR0FBQXFCLENBQUE7UUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7UUFDaEUsSUFBSSxDQUFDLElBQUksQ0FBQ1EsU0FBUyxFQUFFO1VBQUE1QixhQUFBLEdBQUFzQixDQUFBO1VBQUF0QixhQUFBLEdBQUFvQixDQUFBO1VBQ25CLE1BQU0sSUFBSXlCLEtBQUssQ0FBQywwQkFBMEIsQ0FBQztRQUM3QyxDQUFDO1VBQUE3QyxhQUFBLEdBQUFzQixDQUFBO1FBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFFRCxJQUFJLElBQUksQ0FBQ1MsV0FBVyxFQUFFO1VBQUE3QixhQUFBLEdBQUFzQixDQUFBO1VBQUF0QixhQUFBLEdBQUFvQixDQUFBO1VBQ3BCLE1BQU0sSUFBSXlCLEtBQUssQ0FBQywrQkFBK0IsQ0FBQztRQUNsRCxDQUFDO1VBQUE3QyxhQUFBLEdBQUFzQixDQUFBO1FBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFFRCxJQUFJO1VBQUFwQixhQUFBLEdBQUFvQixDQUFBO1VBQ0YwQiwrQkFBa0IsQ0FBQ3RDLEtBQUssQ0FBQyx1QkFBdUIsQ0FBQztVQUVqRCxJQUFNMEQsZ0JBQWdCLElBQUFsRSxhQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDK0MsbUJBQW1CLENBQUNGLE1BQU0sQ0FBQztVQUFDakUsYUFBQSxHQUFBb0IsQ0FBQTtVQUUxRCxJQUFJLENBQUNTLFdBQVcsR0FBRyxJQUFJO1VBQUM3QixhQUFBLEdBQUFvQixDQUFBO1VBQ3hCLElBQUksQ0FBQ1UsUUFBUSxHQUFHLEtBQUs7VUFBQzlCLGFBQUEsR0FBQW9CLENBQUE7VUFDdEIsSUFBSSxDQUFDVyxrQkFBa0IsR0FBR3FDLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7VUFBQ3JFLGFBQUEsR0FBQW9CLENBQUE7VUFDckMsSUFBSSxDQUFDWSxjQUFjLEdBQUcsQ0FBQztVQUV2QixJQUFNc0MsZ0JBQWdCLElBQUF0RSxhQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDUSxTQUFTLENBQUMyQyxXQUFXLENBQUNMLGdCQUFnQixDQUFDO1VBQUNsRSxhQUFBLEdBQUFvQixDQUFBO1VBR3RFLElBQUksQ0FBQ29ELHVCQUF1QixDQUFDLENBQUM7VUFFOUIsSUFBTUMsTUFBTSxJQUFBekUsYUFBQSxHQUFBb0IsQ0FBQSxjQUFTa0QsZ0JBQWdCO1VBQUN0RSxhQUFBLEdBQUFvQixDQUFBO1VBQ3RDLElBQUksQ0FBQ2EsbUJBQW1CLEdBQUd3QyxNQUFNLENBQUNDLEdBQUc7VUFBQzFFLGFBQUEsR0FBQW9CLENBQUE7VUFFdEMwQiwrQkFBa0IsQ0FBQ25DLEdBQUcsQ0FBQyx1QkFBdUIsQ0FBQztRQUNqRCxDQUFDLENBQUMsT0FBT29DLEtBQUssRUFBRTtVQUFBL0MsYUFBQSxHQUFBb0IsQ0FBQTtVQUNkLElBQUksQ0FBQ1MsV0FBVyxHQUFHLEtBQUs7VUFBQzdCLGFBQUEsR0FBQW9CLENBQUE7VUFDekI0QixPQUFPLENBQUNELEtBQUssQ0FBQyw0QkFBNEIsRUFBRUEsS0FBSyxDQUFDO1VBQUMvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ25ELE1BQU0yQixLQUFLO1FBQ2I7TUFDRixDQUFDO01BQUEsU0FqQ0s0QixjQUFjQSxDQUFBQyxFQUFBO1FBQUEsT0FBQVosZUFBQSxDQUFBZCxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWR3QixjQUFjO0lBQUE7RUFBQTtJQUFBdEMsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQXVDLGNBQUEsT0FBQXJDLGtCQUFBLENBQUF0RCxPQUFBLEVBc0NwQixhQUFxRDtRQUFBYyxhQUFBLEdBQUFxQixDQUFBO1FBQUFyQixhQUFBLEdBQUFvQixDQUFBO1FBQ25ELElBQUksQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsV0FBQyxJQUFJLENBQUNNLFNBQVMsTUFBQTVCLGFBQUEsR0FBQXNCLENBQUEsVUFBSSxDQUFDLElBQUksQ0FBQ08sV0FBVyxHQUFFO1VBQUE3QixhQUFBLEdBQUFzQixDQUFBO1VBQUF0QixhQUFBLEdBQUFvQixDQUFBO1VBQ3hDLE1BQU0sSUFBSXlCLEtBQUssQ0FBQyw2QkFBNkIsQ0FBQztRQUNoRCxDQUFDO1VBQUE3QyxhQUFBLEdBQUFzQixDQUFBO1FBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFFRCxJQUFJO1VBQUFwQixhQUFBLEdBQUFvQixDQUFBO1VBQ0YwQiwrQkFBa0IsQ0FBQ3RDLEtBQUssQ0FBQyxzQkFBc0IsQ0FBQztVQUFDUixhQUFBLEdBQUFvQixDQUFBO1VBRWpELElBQUksQ0FBQ1EsU0FBUyxDQUFDa0QsYUFBYSxDQUFDLENBQUM7VUFBQzlFLGFBQUEsR0FBQW9CLENBQUE7VUFDL0IsSUFBSSxDQUFDUyxXQUFXLEdBQUcsS0FBSztVQUFDN0IsYUFBQSxHQUFBb0IsQ0FBQTtVQUN6QixJQUFJLENBQUNVLFFBQVEsR0FBRyxLQUFLO1VBQUM5QixhQUFBLEdBQUFvQixDQUFBO1VBR3RCLElBQUksQ0FBQzJELHNCQUFzQixDQUFDLENBQUM7VUFBQy9FLGFBQUEsR0FBQW9CLENBQUE7VUFFOUIsSUFBSSxDQUFDLElBQUksQ0FBQ2EsbUJBQW1CLEVBQUU7WUFBQWpDLGFBQUEsR0FBQXNCLENBQUE7WUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7WUFDN0IsTUFBTSxJQUFJeUIsS0FBSyxDQUFDLDZCQUE2QixDQUFDO1VBQ2hELENBQUM7WUFBQTdDLGFBQUEsR0FBQXNCLENBQUE7VUFBQTtVQUdELElBQU0wRCxRQUFRLElBQUFoRixhQUFBLEdBQUFvQixDQUFBLGNBQVMxQyxVQUFVLENBQUN1RyxZQUFZLENBQUMsSUFBSSxDQUFDaEQsbUJBQW1CLENBQUM7VUFBQ2pDLGFBQUEsR0FBQW9CLENBQUE7VUFDekUsSUFBSSxDQUFDNEQsUUFBUSxDQUFDRSxNQUFNLEVBQUU7WUFBQWxGLGFBQUEsR0FBQXNCLENBQUE7WUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7WUFDcEIsTUFBTSxJQUFJeUIsS0FBSyxDQUFDLDBCQUEwQixDQUFDO1VBQzdDLENBQUM7WUFBQTdDLGFBQUEsR0FBQXNCLENBQUE7VUFBQTtVQUVELElBQU02RCxRQUFRLElBQUFuRixhQUFBLEdBQUFvQixDQUFBLFFBQUcsQ0FBQ2dELElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsR0FBRyxJQUFJLENBQUN0QyxrQkFBa0IsR0FBRyxJQUFJLENBQUNDLGNBQWMsSUFBSSxJQUFJO1VBR3BGLElBQU1vRCxTQUFTLElBQUFwRixhQUFBLEdBQUFvQixDQUFBLGNBQVMsSUFBSSxDQUFDaUUsaUJBQWlCLENBQUMsSUFBSSxDQUFDcEQsbUJBQW1CLENBQUM7VUFFeEUsSUFBTXdDLE1BQTRCLElBQUF6RSxhQUFBLEdBQUFvQixDQUFBLFFBQUc7WUFDbkNzRCxHQUFHLEVBQUUsSUFBSSxDQUFDekMsbUJBQW1CO1lBQzdCa0QsUUFBUSxFQUFSQSxRQUFRO1lBQ1JHLFFBQVEsRUFBRSxDQUFBdEYsYUFBQSxHQUFBc0IsQ0FBQSxVQUFBMEQsUUFBUSxDQUFDTyxJQUFJLE1BQUF2RixhQUFBLEdBQUFzQixDQUFBLFVBQUksQ0FBQztZQUM1QmtFLEtBQUssRUFBRSxJQUFJO1lBQ1hDLE1BQU0sRUFBRSxJQUFJO1lBQ1pMLFNBQVMsRUFBVEE7VUFDRixDQUFDO1VBQUNwRixhQUFBLEdBQUFvQixDQUFBO1VBRUYwQiwrQkFBa0IsQ0FBQ25DLEdBQUcsQ0FBQyxzQkFBc0IsQ0FBQztVQUFDWCxhQUFBLEdBQUFvQixDQUFBO1VBQy9DLE9BQU9xRCxNQUFNO1FBQ2YsQ0FBQyxDQUFDLE9BQU8xQixLQUFLLEVBQUU7VUFBQS9DLGFBQUEsR0FBQW9CLENBQUE7VUFDZDRCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDJCQUEyQixFQUFFQSxLQUFLLENBQUM7VUFBQy9DLGFBQUEsR0FBQW9CLENBQUE7VUFDbEQsTUFBTTJCLEtBQUs7UUFDYjtNQUNGLENBQUM7TUFBQSxTQTdDSytCLGFBQWFBLENBQUE7UUFBQSxPQUFBRCxjQUFBLENBQUEzQixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWIyQixhQUFhO0lBQUE7RUFBQTtJQUFBekMsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQW9ELGVBQUEsT0FBQWxELGtCQUFBLENBQUF0RCxPQUFBLEVBa0RuQixhQUFzQztRQUFBYyxhQUFBLEdBQUFxQixDQUFBO1FBQUFyQixhQUFBLEdBQUFvQixDQUFBO1FBQ3BDLElBQUksQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsWUFBQyxJQUFJLENBQUNPLFdBQVcsTUFBQTdCLGFBQUEsR0FBQXNCLENBQUEsV0FBSSxJQUFJLENBQUNRLFFBQVEsR0FBRTtVQUFBOUIsYUFBQSxHQUFBc0IsQ0FBQTtVQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtVQUN0QztRQUNGLENBQUM7VUFBQXBCLGFBQUEsR0FBQXNCLENBQUE7UUFBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUVELElBQUk7VUFBQXBCLGFBQUEsR0FBQW9CLENBQUE7VUFHRixJQUFJLENBQUNVLFFBQVEsR0FBRyxJQUFJO1VBQUM5QixhQUFBLEdBQUFvQixDQUFBO1VBQ3JCNEIsT0FBTyxDQUFDMkMsR0FBRyxDQUFDLCtDQUErQyxDQUFDO1FBQzlELENBQUMsQ0FBQyxPQUFPNUMsS0FBSyxFQUFFO1VBQUEvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ2Q0QixPQUFPLENBQUNELEtBQUssQ0FBQyw0QkFBNEIsRUFBRUEsS0FBSyxDQUFDO1VBQUMvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ25ELE1BQU0yQixLQUFLO1FBQ2I7TUFDRixDQUFDO01BQUEsU0FkSzZDLGNBQWNBLENBQUE7UUFBQSxPQUFBRixlQUFBLENBQUF4QyxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWR5QyxjQUFjO0lBQUE7RUFBQTtJQUFBdkQsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQXVELGdCQUFBLE9BQUFyRCxrQkFBQSxDQUFBdEQsT0FBQSxFQW1CcEIsYUFBdUM7UUFBQWMsYUFBQSxHQUFBcUIsQ0FBQTtRQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNyQyxJQUFJLENBQUFwQixhQUFBLEdBQUFzQixDQUFBLFlBQUMsSUFBSSxDQUFDTyxXQUFXLE1BQUE3QixhQUFBLEdBQUFzQixDQUFBLFdBQUksQ0FBQyxJQUFJLENBQUNRLFFBQVEsR0FBRTtVQUFBOUIsYUFBQSxHQUFBc0IsQ0FBQTtVQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtVQUN2QztRQUNGLENBQUM7VUFBQXBCLGFBQUEsR0FBQXNCLENBQUE7UUFBQTtRQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtRQUVELElBQUk7VUFBQXBCLGFBQUEsR0FBQW9CLENBQUE7VUFHRixJQUFJLENBQUNVLFFBQVEsR0FBRyxLQUFLO1VBQUM5QixhQUFBLEdBQUFvQixDQUFBO1VBQ3RCNEIsT0FBTyxDQUFDMkMsR0FBRyxDQUFDLGdEQUFnRCxDQUFDO1FBQy9ELENBQUMsQ0FBQyxPQUFPNUMsS0FBSyxFQUFFO1VBQUEvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ2Q0QixPQUFPLENBQUNELEtBQUssQ0FBQyw2QkFBNkIsRUFBRUEsS0FBSyxDQUFDO1VBQUMvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ3BELE1BQU0yQixLQUFLO1FBQ2I7TUFDRixDQUFDO01BQUEsU0FkSytDLGVBQWVBLENBQUE7UUFBQSxPQUFBRCxnQkFBQSxDQUFBM0MsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFmMkMsZUFBZTtJQUFBO0VBQUE7SUFBQXpELEdBQUE7SUFBQUMsS0FBQSxFQW1CckIsU0FBQXlELGtCQUFrQkEsQ0FBQSxFQUFzQjtNQUFBL0YsYUFBQSxHQUFBcUIsQ0FBQTtNQUN0QyxJQUFNMkUsV0FBVyxJQUFBaEcsYUFBQSxHQUFBb0IsQ0FBQSxRQUFHZ0QsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztNQUM5QixJQUFNYyxRQUFRLElBQUFuRixhQUFBLEdBQUFvQixDQUFBLFFBQUcsSUFBSSxDQUFDUyxXQUFXLElBQUE3QixhQUFBLEdBQUFzQixDQUFBLFdBQzdCLENBQUMwRSxXQUFXLEdBQUcsSUFBSSxDQUFDakUsa0JBQWtCLEdBQUcsSUFBSSxDQUFDQyxjQUFjLElBQUksSUFBSSxLQUFBaEMsYUFBQSxHQUFBc0IsQ0FBQSxXQUNwRSxDQUFDO01BQUN0QixhQUFBLEdBQUFvQixDQUFBO01BRU4sT0FBTztRQUNMK0QsUUFBUSxFQUFSQSxRQUFRO1FBQ1JHLFFBQVEsRUFBRSxDQUFDO1FBQ1h6RCxXQUFXLEVBQUUsSUFBSSxDQUFDQSxXQUFXO1FBQzdCQyxRQUFRLEVBQUUsSUFBSSxDQUFDQTtNQUNqQixDQUFDO0lBQ0g7RUFBQztJQUFBTyxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBMkQsbUJBQW1CQSxDQUFDQyxRQUErQyxFQUFRO01BQUFsRyxhQUFBLEdBQUFxQixDQUFBO01BQUFyQixhQUFBLEdBQUFvQixDQUFBO01BQ3pFLElBQUksQ0FBQ2MsZ0JBQWdCLEdBQUdnRSxRQUFRO0lBQ2xDO0VBQUM7SUFBQTdELEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE2RCxjQUFBLE9BQUEzRCxrQkFBQSxDQUFBdEQsT0FBQSxFQUtELFdBQW9Cd0YsR0FBVyxFQUFtQjtRQUFBMUUsYUFBQSxHQUFBcUIsQ0FBQTtRQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtRQUNoRCxJQUFJO1VBQ0YsSUFBTXFCLFdBQVcsSUFBQXpDLGFBQUEsR0FBQW9CLENBQUEsY0FBUzVDLFlBQVksQ0FBQ21GLHVCQUF1QixDQUFDLENBQUM7VUFBQzNELGFBQUEsR0FBQW9CLENBQUE7VUFDakUsSUFBSSxDQUFDcUIsV0FBVyxDQUFDMkQsT0FBTyxFQUFFO1lBQUFwRyxhQUFBLEdBQUFzQixDQUFBO1lBQUF0QixhQUFBLEdBQUFvQixDQUFBO1lBQ3hCLE1BQU0sSUFBSXlCLEtBQUssQ0FBQyxpREFBaUQsQ0FBQztVQUNwRSxDQUFDO1lBQUE3QyxhQUFBLEdBQUFzQixDQUFBO1VBQUE7VUFFRCxJQUFNK0UsS0FBSyxJQUFBckcsYUFBQSxHQUFBb0IsQ0FBQSxjQUFTNUMsWUFBWSxDQUFDOEgsZ0JBQWdCLENBQUM1QixHQUFHLENBQUM7VUFBQzFFLGFBQUEsR0FBQW9CLENBQUE7VUFDdkQsT0FBT2lGLEtBQUssQ0FBQzNCLEdBQUc7UUFDbEIsQ0FBQyxDQUFDLE9BQU8zQixLQUFLLEVBQUU7VUFBQS9DLGFBQUEsR0FBQW9CLENBQUE7VUFDZDRCLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLGtDQUFrQyxFQUFFQSxLQUFLLENBQUM7VUFBQy9DLGFBQUEsR0FBQW9CLENBQUE7VUFDekQsTUFBTTJCLEtBQUs7UUFDYjtNQUNGLENBQUM7TUFBQSxTQWJLd0QsYUFBYUEsQ0FBQUMsR0FBQTtRQUFBLE9BQUFMLGNBQUEsQ0FBQWpELEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBYm9ELGFBQWE7SUFBQTtFQUFBO0lBQUFsRSxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBbUUsY0FBQSxPQUFBakUsa0JBQUEsQ0FBQXRELE9BQUEsRUFrQm5CLFdBQW9Cd0YsR0FBVyxFQUFrRTtRQUFBLElBQWhFZ0MsT0FBa0MsR0FBQXZELFNBQUEsQ0FBQXdELE1BQUEsUUFBQXhELFNBQUEsUUFBQWhDLFNBQUEsR0FBQWdDLFNBQUEsT0FBQW5ELGFBQUEsR0FBQXNCLENBQUEsV0FBRyxRQUFRO1FBQUF0QixhQUFBLEdBQUFxQixDQUFBO1FBQUFyQixhQUFBLEdBQUFvQixDQUFBO1FBQzVFLElBQUk7VUFBQXBCLGFBQUEsR0FBQW9CLENBQUE7VUFDRjBCLCtCQUFrQixDQUFDdEMsS0FBSyxDQUFDLG1CQUFtQixDQUFDO1VBRzdDLElBQU13RSxRQUFRLElBQUFoRixhQUFBLEdBQUFvQixDQUFBLGNBQVMxQyxVQUFVLENBQUN1RyxZQUFZLENBQUNQLEdBQUcsQ0FBQztVQUFDMUUsYUFBQSxHQUFBb0IsQ0FBQTtVQUNwRCxJQUFJLENBQUM0RCxRQUFRLENBQUNFLE1BQU0sRUFBRTtZQUFBbEYsYUFBQSxHQUFBc0IsQ0FBQTtZQUFBdEIsYUFBQSxHQUFBb0IsQ0FBQTtZQUNwQixNQUFNLElBQUl5QixLQUFLLENBQUMsc0NBQXNDLENBQUM7VUFDekQsQ0FBQztZQUFBN0MsYUFBQSxHQUFBc0IsQ0FBQTtVQUFBO1VBRUQsSUFBTXNGLFlBQVksSUFBQTVHLGFBQUEsR0FBQW9CLENBQUEsUUFBRyxDQUFBcEIsYUFBQSxHQUFBc0IsQ0FBQSxXQUFBMEQsUUFBUSxDQUFDTyxJQUFJLE1BQUF2RixhQUFBLEdBQUFzQixDQUFBLFdBQUksQ0FBQztVQUFDdEIsYUFBQSxHQUFBb0IsQ0FBQTtVQUN4QzRCLE9BQU8sQ0FBQzJDLEdBQUcsQ0FBQyxzQkFBc0JpQixZQUFZLG9CQUFvQkYsT0FBTyxFQUFFLENBQUM7VUFHNUUsSUFBTUcsYUFBYSxJQUFBN0csYUFBQSxHQUFBb0IsQ0FBQSxRQUFHLEdBQUcxQyxVQUFVLENBQUNvSSxjQUFjLGNBQWMxQyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLE1BQU07VUFHaEYsSUFBTTBDLG1CQUFtQixJQUFBL0csYUFBQSxHQUFBb0IsQ0FBQSxRQUFHO1lBQzFCNEYsR0FBRyxFQUFFO2NBQUVDLE9BQU8sRUFBRSxNQUFNO2NBQUVDLFVBQVUsRUFBRTtZQUFJLENBQUM7WUFDekNDLE1BQU0sRUFBRTtjQUFFRixPQUFPLEVBQUUsT0FBTztjQUFFQyxVQUFVLEVBQUU7WUFBSSxDQUFDO1lBQzdDRSxJQUFJLEVBQUU7Y0FBRUgsT0FBTyxFQUFFLE9BQU87Y0FBRUMsVUFBVSxFQUFFO1lBQUs7VUFDN0MsQ0FBQztVQUVELElBQU1HLFFBQVEsSUFBQXJILGFBQUEsR0FBQW9CLENBQUEsUUFBRzJGLG1CQUFtQixDQUFDTCxPQUFPLENBQUM7VUFBQzFHLGFBQUEsR0FBQW9CLENBQUE7VUFJOUMsTUFBTTFDLFVBQVUsQ0FBQzRJLFNBQVMsQ0FBQztZQUN6QkMsSUFBSSxFQUFFN0MsR0FBRztZQUNUOEMsRUFBRSxFQUFFWDtVQUNOLENBQUMsQ0FBQztVQUVGLElBQU1ZLGNBQWMsSUFBQXpILGFBQUEsR0FBQW9CLENBQUEsZUFBUzFDLFVBQVUsQ0FBQ3VHLFlBQVksQ0FBQzRCLGFBQWEsQ0FBQztVQUNuRSxJQUFNYSxjQUFjLElBQUExSCxhQUFBLEdBQUFvQixDQUFBLFNBQUcsQ0FBQXBCLGFBQUEsR0FBQXNCLENBQUEsV0FBQW1HLGNBQWMsQ0FBQ2xDLElBQUksTUFBQXZGLGFBQUEsR0FBQXNCLENBQUEsV0FBSSxDQUFDO1VBQUN0QixhQUFBLEdBQUFvQixDQUFBO1VBRWhENEIsT0FBTyxDQUFDMkMsR0FBRyxDQUFDLHFCQUFxQmlCLFlBQVksT0FBT2MsY0FBYyxRQUFRLENBQUM7VUFBQzFILGFBQUEsR0FBQW9CLENBQUE7VUFFNUUwQiwrQkFBa0IsQ0FBQ25DLEdBQUcsQ0FBQyxtQkFBbUIsQ0FBQztVQUFDWCxhQUFBLEdBQUFvQixDQUFBO1VBQzVDLE9BQU95RixhQUFhO1FBQ3RCLENBQUMsQ0FBQyxPQUFPOUQsS0FBSyxFQUFFO1VBQUEvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ2Q0QixPQUFPLENBQUNELEtBQUssQ0FBQywyQkFBMkIsRUFBRUEsS0FBSyxDQUFDO1VBQUMvQyxhQUFBLEdBQUFvQixDQUFBO1VBQ2xELE1BQU0yQixLQUFLO1FBQ2I7TUFDRixDQUFDO01BQUEsU0EzQ0s0RSxhQUFhQSxDQUFBQyxHQUFBO1FBQUEsT0FBQW5CLGNBQUEsQ0FBQXZELEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBYndFLGFBQWE7SUFBQTtFQUFBO0lBQUF0RixHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBdUYsa0JBQUEsT0FBQXJGLGtCQUFBLENBQUF0RCxPQUFBLEVBZ0RuQixXQUFnQ3dGLEdBQVcsRUFBbUI7UUFBQTFFLGFBQUEsR0FBQXFCLENBQUE7UUFBQXJCLGFBQUEsR0FBQW9CLENBQUE7UUFDNUQsSUFBSTtVQUVGLElBQU0wRyxZQUFZLElBQUE5SCxhQUFBLEdBQUFvQixDQUFBLFNBQUcsR0FBRzFDLFVBQVUsQ0FBQ29JLGNBQWMsYUFBYTFDLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsTUFBTTtVQUFDckUsYUFBQSxHQUFBb0IsQ0FBQTtVQUUvRTRCLE9BQU8sQ0FBQzJDLEdBQUcsQ0FBQyxpQ0FBaUMsRUFBRWpCLEdBQUcsQ0FBQztVQUFDMUUsYUFBQSxHQUFBb0IsQ0FBQTtVQVVwRCxPQUFPLEVBQUU7UUFDWCxDQUFDLENBQUMsT0FBTzJCLEtBQUssRUFBRTtVQUFBL0MsYUFBQSxHQUFBb0IsQ0FBQTtVQUNkNEIsT0FBTyxDQUFDRCxLQUFLLENBQUMsK0JBQStCLEVBQUVBLEtBQUssQ0FBQztVQUFDL0MsYUFBQSxHQUFBb0IsQ0FBQTtVQUN0RCxPQUFPLEVBQUU7UUFDWDtNQUNGLENBQUM7TUFBQSxTQXBCYWlFLGlCQUFpQkEsQ0FBQTBDLEdBQUE7UUFBQSxPQUFBRixrQkFBQSxDQUFBM0UsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFqQmtDLGlCQUFpQjtJQUFBO0VBQUE7SUFBQWhELEdBQUE7SUFBQUMsS0FBQSxFQXlCL0IsU0FBUTZCLG1CQUFtQkEsQ0FBQ0YsTUFBNEIsRUFBTztNQUFBakUsYUFBQSxHQUFBcUIsQ0FBQTtNQUM3RCxJQUFNMkcsVUFBd0MsSUFBQWhJLGFBQUEsR0FBQW9CLENBQUEsU0FBRztRQUMvQzRGLEdBQUcsRUFBRWlCLHdCQUFZLENBQUMsTUFBTSxDQUFDO1FBQ3pCZCxNQUFNLEVBQUVjLHdCQUFZLENBQUMsTUFBTSxDQUFDO1FBQzVCYixJQUFJLEVBQUVhLHdCQUFZLENBQUMsT0FBTyxDQUFDO1FBQzNCQyxLQUFLLEVBQUVELHdCQUFZLENBQUMsT0FBTztNQUM3QixDQUFDO01BQUNqSSxhQUFBLEdBQUFvQixDQUFBO01BRUYsT0FBTztRQUNMc0YsT0FBTyxFQUFFLENBQUExRyxhQUFBLEdBQUFzQixDQUFBLFdBQUEwRyxVQUFVLENBQUMvRCxNQUFNLENBQUN5QyxPQUFPLENBQUMsTUFBQTFHLGFBQUEsR0FBQXNCLENBQUEsV0FBSTJHLHdCQUFZLENBQUMsTUFBTSxDQUFDO1FBQzNERSxXQUFXLEVBQUVsRSxNQUFNLENBQUNtRSxrQkFBa0IsR0FBRyxFQUFFO1FBQzNDQyxJQUFJLEVBQUUsQ0FBQ3BFLE1BQU0sQ0FBQ3FFO01BRWhCLENBQUM7SUFDSDtFQUFDO0lBQUFqRyxHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFRa0MsdUJBQXVCQSxDQUFBLEVBQVM7TUFBQSxJQUFBK0QsS0FBQTtNQUFBdkksYUFBQSxHQUFBcUIsQ0FBQTtNQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtNQUN0QyxJQUFJLElBQUksQ0FBQ2UsZ0JBQWdCLEVBQUU7UUFBQW5DLGFBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFDekJvSCxhQUFhLENBQUMsSUFBSSxDQUFDckcsZ0JBQWdCLENBQUM7TUFDdEMsQ0FBQztRQUFBbkMsYUFBQSxHQUFBc0IsQ0FBQTtNQUFBO01BQUF0QixhQUFBLEdBQUFvQixDQUFBO01BRUQsSUFBSSxDQUFDZSxnQkFBZ0IsR0FBR3NHLFdBQVcsQ0FBQyxZQUFNO1FBQUF6SSxhQUFBLEdBQUFxQixDQUFBO1FBQUFyQixhQUFBLEdBQUFvQixDQUFBO1FBQ3hDLElBQUltSCxLQUFJLENBQUNyRyxnQkFBZ0IsRUFBRTtVQUFBbEMsYUFBQSxHQUFBc0IsQ0FBQTtVQUN6QixJQUFNb0gsUUFBUSxJQUFBMUksYUFBQSxHQUFBb0IsQ0FBQSxTQUFHbUgsS0FBSSxDQUFDeEMsa0JBQWtCLENBQUMsQ0FBQztVQUFDL0YsYUFBQSxHQUFBb0IsQ0FBQTtVQUMzQ21ILEtBQUksQ0FBQ3JHLGdCQUFnQixDQUFDd0csUUFBUSxDQUFDO1FBQ2pDLENBQUM7VUFBQTFJLGFBQUEsR0FBQXNCLENBQUE7UUFBQTtNQUNILENBQUMsRUFBRSxJQUFJLENBQUM7SUFDVjtFQUFDO0lBQUFlLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQVF5QyxzQkFBc0JBLENBQUEsRUFBUztNQUFBL0UsYUFBQSxHQUFBcUIsQ0FBQTtNQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtNQUNyQyxJQUFJLElBQUksQ0FBQ2UsZ0JBQWdCLEVBQUU7UUFBQW5DLGFBQUEsR0FBQXNCLENBQUE7UUFBQXRCLGFBQUEsR0FBQW9CLENBQUE7UUFDekJvSCxhQUFhLENBQUMsSUFBSSxDQUFDckcsZ0JBQWdCLENBQUM7UUFBQ25DLGFBQUEsR0FBQW9CLENBQUE7UUFDckMsSUFBSSxDQUFDZSxnQkFBZ0IsR0FBRyxJQUFJO01BQzlCLENBQUM7UUFBQW5DLGFBQUEsR0FBQXNCLENBQUE7TUFBQTtJQUNIO0VBQUM7SUFBQWUsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQXFHLE9BQU9BLENBQUEsRUFBUztNQUFBM0ksYUFBQSxHQUFBcUIsQ0FBQTtNQUFBckIsYUFBQSxHQUFBb0IsQ0FBQTtNQUNkLElBQUksQ0FBQzJELHNCQUFzQixDQUFDLENBQUM7TUFBQy9FLGFBQUEsR0FBQW9CLENBQUE7TUFDOUIsSUFBSSxDQUFDUyxXQUFXLEdBQUcsS0FBSztNQUFDN0IsYUFBQSxHQUFBb0IsQ0FBQTtNQUN6QixJQUFJLENBQUNVLFFBQVEsR0FBRyxLQUFLO01BQUM5QixhQUFBLEdBQUFvQixDQUFBO01BQ3RCLElBQUksQ0FBQ2EsbUJBQW1CLEdBQUcsSUFBSTtNQUFDakMsYUFBQSxHQUFBb0IsQ0FBQTtNQUNoQyxJQUFJLENBQUNjLGdCQUFnQixHQUFHLElBQUk7SUFDOUI7RUFBQztBQUFBO0FBSUksSUFBTTBHLHFCQUFxQixHQUFBQyxPQUFBLENBQUFELHFCQUFBLElBQUE1SSxhQUFBLEdBQUFvQixDQUFBLFNBQUcsSUFBSU0scUJBQXFCLENBQUMsQ0FBQyIsImlnbm9yZUxpc3QiOltdfQ==