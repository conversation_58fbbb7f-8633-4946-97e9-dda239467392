66f94f10abeebf62d92b31a3551f89ac
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _Animation = _interopRequireDefault(require("./Animation"));
var _NativeAnimatedHelper = require("../NativeAnimatedHelper");
var DecayAnimation = function (_Animation$default) {
  function DecayAnimation(config) {
    var _this;
    (0, _classCallCheck2.default)(this, DecayAnimation);
    var _config$deceleration, _config$isInteraction, _config$iterations;
    _this = _callSuper(this, DecayAnimation);
    _this._deceleration = (_config$deceleration = config.deceleration) !== null && _config$deceleration !== void 0 ? _config$deceleration : 0.998;
    _this._velocity = config.velocity;
    _this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);
    _this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !_this._useNativeDriver;
    _this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;
    return _this;
  }
  (0, _inherits2.default)(DecayAnimation, _Animation$default);
  return (0, _createClass2.default)(DecayAnimation, [{
    key: "__getNativeAnimationConfig",
    value: function __getNativeAnimationConfig() {
      return {
        type: 'decay',
        deceleration: this._deceleration,
        velocity: this._velocity,
        iterations: this.__iterations
      };
    }
  }, {
    key: "start",
    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {
      this.__active = true;
      this._lastValue = fromValue;
      this._fromValue = fromValue;
      this._onUpdate = onUpdate;
      this.__onEnd = onEnd;
      this._startTime = Date.now();
      if (this._useNativeDriver) {
        this.__startNativeAnimation(animatedValue);
      } else {
        this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));
      }
    }
  }, {
    key: "onUpdate",
    value: function onUpdate() {
      var now = Date.now();
      var value = this._fromValue + this._velocity / (1 - this._deceleration) * (1 - Math.exp(-(1 - this._deceleration) * (now - this._startTime)));
      this._onUpdate(value);
      if (Math.abs(this._lastValue - value) < 0.1) {
        this.__debouncedOnEnd({
          finished: true
        });
        return;
      }
      this._lastValue = value;
      if (this.__active) {
        this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));
      }
    }
  }, {
    key: "stop",
    value: function stop() {
      _superPropGet(DecayAnimation, "stop", this, 3)([]);
      this.__active = false;
      global.cancelAnimationFrame(this._animationFrame);
      this.__debouncedOnEnd({
        finished: false
      });
    }
  }]);
}(_Animation.default);
var _default = exports.default = DecayAnimation;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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