f7717dc69b74c83a8d11688503a9aa7b
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = renderApplication;
exports.getApplication = getApplication;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _AppContainer = _interopRequireDefault(require("./AppContainer"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _render = _interopRequireWildcard(require("../render"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _react = _interopRequireDefault(require("react"));
function renderApplication(RootComponent, WrapperComponent, callback, options) {
  var shouldHydrate = options.hydrate,
    initialProps = options.initialProps,
    rootTag = options.rootTag;
  var renderFn = shouldHydrate ? _render.hydrate : _render.default;
  (0, _invariant.default)(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);
  return renderFn(_react.default.createElement(_AppContainer.default, {
    WrapperComponent: WrapperComponent,
    ref: callback,
    rootTag: rootTag
  }, _react.default.createElement(RootComponent, initialProps)), rootTag);
}
function getApplication(RootComponent, initialProps, WrapperComponent) {
  var element = _react.default.createElement(_AppContainer.default, {
    WrapperComponent: WrapperComponent,
    rootTag: {}
  }, _react.default.createElement(RootComponent, initialProps));
  var getStyleElement = function getStyleElement(props) {
    var sheet = _StyleSheet.default.getSheet();
    return _react.default.createElement("style", (0, _extends2.default)({}, props, {
      dangerouslySetInnerHTML: {
        __html: sheet.textContent
      },
      id: sheet.id
    }));
  };
  return {
    element: element,
    getStyleElement: getStyleElement
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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