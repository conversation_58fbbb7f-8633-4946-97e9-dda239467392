{"version": 3, "names": ["FileSystem", "DocumentPicker", "Sharing", "ExportService", "_classCallCheck", "_createClass", "key", "value", "_generateProgressReport", "_asyncToGenerator", "userId", "options", "cov_m06lqxmi1", "f", "s", "_ref", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "supabase", "date<PERSON><PERSON><PERSON>", "b", "start", "Date", "now", "end", "_ref2", "from", "select", "eq", "gte", "toISOString", "lte", "order", "ascending", "trainingSessions", "data", "_ref3", "matchResults", "_ref4", "achievements", "_ref5", "skillStats", "summary", "calculateSummary", "skillProgression", "calculateSkillProgression", "report", "generatedAt", "trainingData", "error", "console", "generateProgressReport", "_x", "_x2", "apply", "arguments", "_exportProgressReport", "content", "filename", "mimeType", "format", "generatePDFContent", "generateCSVContent", "JSON", "stringify", "generateTextContent", "Error", "fileUri", "documentDirectory", "writeAsStringAsync", "encoding", "EncodingType", "UTF8", "exportProgressReport", "_x3", "_x4", "_shareExportedFile", "isAvailable", "isAvailableAsync", "shareAsync", "getMimeTypeFromUri", "dialogTitle", "shareExportedFile", "_x5", "_exportTrainingData", "sessionIds", "_ref6", "_ref7", "in", "sessions", "length", "generateTrainingCSV", "exportTrainingData", "_x6", "_x7", "_x8", "_exportMatchData", "matchIds", "_ref8", "_ref9", "matches", "generateMatchCSV", "exportMatchData", "_x9", "_x0", "_x1", "_importData", "result", "getDocumentAsync", "type", "copyToCacheDirectory", "canceled", "fileContent", "readAsStringAsync", "assets", "uri", "name", "endsWith", "parse", "parseCSV", "importData", "totalSessions", "totalPlayTime", "reduce", "sum", "session", "duration_minutes", "averageScore", "overall_score", "improvementRate", "Math", "round", "firstStats", "lastStats", "skills", "map", "skill", "startRating", "endRating", "improvement", "_generatePDFContent", "join", "_x10", "headers", "rows", "created_at", "toLocaleDateString", "session_type", "improvement_areas", "concat", "_toConsumableArray", "row", "achievement", "title", "description", "trim", "ai_feedback_summary", "match", "opponent_name", "match_score", "surface", "lines", "split", "slice", "line", "values", "obj", "for<PERSON>ach", "header", "index", "exportService"], "sources": ["exportService.ts"], "sourcesContent": ["// Export Service for Progress Reports and Data Export\nimport * as FileSystem from 'expo-file-system';\nimport * as DocumentPicker from 'expo-document-picker';\nimport * as Sharing from 'expo-sharing';\n\nexport interface ExportOptions {\n  format: 'pdf' | 'csv' | 'json' | 'txt';\n  dateRange?: {\n    start: Date;\n    end: Date;\n  };\n  includeCharts?: boolean;\n  includePhotos?: boolean;\n  includeVideos?: boolean;\n}\n\nexport interface ProgressReport {\n  userId: string;\n  generatedAt: Date;\n  dateRange: {\n    start: Date;\n    end: Date;\n  };\n  summary: {\n    totalSessions: number;\n    totalPlayTime: number;\n    averageScore: number;\n    improvementRate: number;\n  };\n  skillProgression: {\n    skill: string;\n    startRating: number;\n    endRating: number;\n    improvement: number;\n  }[];\n  achievements: any[];\n  matchResults: any[];\n  trainingData: any[];\n}\n\nclass ExportService {\n  /**\n   * Generate comprehensive progress report\n   */\n  async generateProgressReport(\n    userId: string,\n    options: ExportOptions\n  ): Promise<ProgressReport> {\n    try {\n      const { supabase } = await import('@/lib/supabase');\n      \n      const dateRange = options.dateRange || {\n        start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago\n        end: new Date(),\n      };\n\n      // Fetch training sessions\n      const { data: trainingSessions } = await supabase\n        .from('training_sessions')\n        .select('*')\n        .eq('user_id', userId)\n        .gte('created_at', dateRange.start.toISOString())\n        .lte('created_at', dateRange.end.toISOString())\n        .order('created_at', { ascending: true });\n\n      // Fetch match results\n      const { data: matchResults } = await supabase\n        .from('match_results')\n        .select('*')\n        .eq('user_id', userId)\n        .gte('created_at', dateRange.start.toISOString())\n        .lte('created_at', dateRange.end.toISOString())\n        .order('created_at', { ascending: true });\n\n      // Fetch achievements\n      const { data: achievements } = await supabase\n        .from('achievements')\n        .select('*')\n        .eq('user_id', userId)\n        .gte('unlocked_at', dateRange.start.toISOString())\n        .lte('unlocked_at', dateRange.end.toISOString())\n        .order('unlocked_at', { ascending: true });\n\n      // Fetch skill progression\n      const { data: skillStats } = await supabase\n        .from('skill_stats')\n        .select('*')\n        .eq('user_id', userId)\n        .order('updated_at', { ascending: true });\n\n      // Calculate summary statistics\n      const summary = this.calculateSummary(trainingSessions || [], matchResults || []);\n      \n      // Calculate skill progression\n      const skillProgression = this.calculateSkillProgression(skillStats || []);\n\n      const report: ProgressReport = {\n        userId,\n        generatedAt: new Date(),\n        dateRange,\n        summary,\n        skillProgression,\n        achievements: achievements || [],\n        matchResults: matchResults || [],\n        trainingData: trainingSessions || [],\n      };\n\n      return report;\n    } catch (error) {\n      console.error('Error generating progress report:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Export progress report to file\n   */\n  async exportProgressReport(\n    report: ProgressReport,\n    options: ExportOptions\n  ): Promise<string> {\n    try {\n      let content: string;\n      let filename: string;\n      let mimeType: string;\n\n      switch (options.format) {\n        case 'pdf':\n          content = await this.generatePDFContent(report);\n          filename = `tennis_progress_${report.userId}_${Date.now()}.pdf`;\n          mimeType = 'application/pdf';\n          break;\n        \n        case 'csv':\n          content = this.generateCSVContent(report);\n          filename = `tennis_progress_${report.userId}_${Date.now()}.csv`;\n          mimeType = 'text/csv';\n          break;\n        \n        case 'json':\n          content = JSON.stringify(report, null, 2);\n          filename = `tennis_progress_${report.userId}_${Date.now()}.json`;\n          mimeType = 'application/json';\n          break;\n        \n        case 'txt':\n          content = this.generateTextContent(report);\n          filename = `tennis_progress_${report.userId}_${Date.now()}.txt`;\n          mimeType = 'text/plain';\n          break;\n        \n        default:\n          throw new Error('Unsupported export format');\n      }\n\n      // Write file to device storage\n      const fileUri = `${FileSystem.documentDirectory}${filename}`;\n      await FileSystem.writeAsStringAsync(fileUri, content, {\n        encoding: FileSystem.EncodingType.UTF8,\n      });\n\n      return fileUri;\n    } catch (error) {\n      console.error('Error exporting progress report:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Share exported file\n   */\n  async shareExportedFile(fileUri: string): Promise<void> {\n    try {\n      const isAvailable = await Sharing.isAvailableAsync();\n      \n      if (isAvailable) {\n        await Sharing.shareAsync(fileUri, {\n          mimeType: this.getMimeTypeFromUri(fileUri),\n          dialogTitle: 'Share Tennis Progress Report',\n        });\n      } else {\n        throw new Error('Sharing is not available on this device');\n      }\n    } catch (error) {\n      console.error('Error sharing file:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Export training session data\n   */\n  async exportTrainingData(\n    userId: string,\n    sessionIds: string[],\n    format: 'csv' | 'json'\n  ): Promise<string> {\n    try {\n      const { supabase } = await import('@/lib/supabase');\n      \n      const { data: sessions } = await supabase\n        .from('training_sessions')\n        .select('*')\n        .eq('user_id', userId)\n        .in('id', sessionIds);\n\n      if (!sessions || sessions.length === 0) {\n        throw new Error('No training sessions found');\n      }\n\n      let content: string;\n      let filename: string;\n\n      if (format === 'csv') {\n        content = this.generateTrainingCSV(sessions);\n        filename = `training_sessions_${Date.now()}.csv`;\n      } else {\n        content = JSON.stringify(sessions, null, 2);\n        filename = `training_sessions_${Date.now()}.json`;\n      }\n\n      const fileUri = `${FileSystem.documentDirectory}${filename}`;\n      await FileSystem.writeAsStringAsync(fileUri, content, {\n        encoding: FileSystem.EncodingType.UTF8,\n      });\n\n      return fileUri;\n    } catch (error) {\n      console.error('Error exporting training data:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Export match statistics\n   */\n  async exportMatchData(\n    userId: string,\n    matchIds: string[],\n    format: 'csv' | 'json'\n  ): Promise<string> {\n    try {\n      const { supabase } = await import('@/lib/supabase');\n      \n      const { data: matches } = await supabase\n        .from('match_results')\n        .select('*')\n        .eq('user_id', userId)\n        .in('id', matchIds);\n\n      if (!matches || matches.length === 0) {\n        throw new Error('No match results found');\n      }\n\n      let content: string;\n      let filename: string;\n\n      if (format === 'csv') {\n        content = this.generateMatchCSV(matches);\n        filename = `match_results_${Date.now()}.csv`;\n      } else {\n        content = JSON.stringify(matches, null, 2);\n        filename = `match_results_${Date.now()}.json`;\n      }\n\n      const fileUri = `${FileSystem.documentDirectory}${filename}`;\n      await FileSystem.writeAsStringAsync(fileUri, content, {\n        encoding: FileSystem.EncodingType.UTF8,\n      });\n\n      return fileUri;\n    } catch (error) {\n      console.error('Error exporting match data:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Import data from file\n   */\n  async importData(): Promise<any> {\n    try {\n      const result = await DocumentPicker.getDocumentAsync({\n        type: ['application/json', 'text/csv'],\n        copyToCacheDirectory: true,\n      });\n\n      if (result.canceled) {\n        return null;\n      }\n\n      const fileContent = await FileSystem.readAsStringAsync(result.assets[0].uri);\n      \n      if (result.assets[0].name.endsWith('.json')) {\n        return JSON.parse(fileContent);\n      } else if (result.assets[0].name.endsWith('.csv')) {\n        return this.parseCSV(fileContent);\n      }\n\n      throw new Error('Unsupported file format');\n    } catch (error) {\n      console.error('Error importing data:', error);\n      throw error;\n    }\n  }\n\n  // Private helper methods\n\n  private calculateSummary(trainingSessions: any[], matchResults: any[]) {\n    const totalSessions = trainingSessions.length;\n    const totalPlayTime = trainingSessions.reduce(\n      (sum, session) => sum + (session.duration_minutes || 0), 0\n    );\n    const averageScore = trainingSessions.length > 0\n      ? trainingSessions.reduce((sum, session) => sum + (session.overall_score || 0), 0) / trainingSessions.length\n      : 0;\n    \n    // Calculate improvement rate (simplified)\n    const improvementRate = trainingSessions.length > 1\n      ? ((trainingSessions[trainingSessions.length - 1].overall_score || 0) - \n         (trainingSessions[0].overall_score || 0)) / trainingSessions.length\n      : 0;\n\n    return {\n      totalSessions,\n      totalPlayTime,\n      averageScore: Math.round(averageScore),\n      improvementRate: Math.round(improvementRate * 100) / 100,\n    };\n  }\n\n  private calculateSkillProgression(skillStats: any[]) {\n    if (skillStats.length < 2) {\n      return [];\n    }\n\n    const firstStats = skillStats[0];\n    const lastStats = skillStats[skillStats.length - 1];\n    const skills = ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game'];\n\n    return skills.map(skill => ({\n      skill,\n      startRating: firstStats[skill] || 0,\n      endRating: lastStats[skill] || 0,\n      improvement: (lastStats[skill] || 0) - (firstStats[skill] || 0),\n    }));\n  }\n\n  private async generatePDFContent(report: ProgressReport): Promise<string> {\n    // In a real implementation, you would use a PDF generation library\n    // For now, return HTML that could be converted to PDF\n    return `\n      <html>\n        <head><title>Tennis Progress Report</title></head>\n        <body>\n          <h1>Tennis Progress Report</h1>\n          <h2>Summary</h2>\n          <p>Total Sessions: ${report.summary.totalSessions}</p>\n          <p>Total Play Time: ${report.summary.totalPlayTime} minutes</p>\n          <p>Average Score: ${report.summary.averageScore}</p>\n          <h2>Skill Progression</h2>\n          ${report.skillProgression.map(skill => \n            `<p>${skill.skill}: ${skill.startRating} → ${skill.endRating} (${skill.improvement > 0 ? '+' : ''}${skill.improvement})</p>`\n          ).join('')}\n        </body>\n      </html>\n    `;\n  }\n\n  private generateCSVContent(report: ProgressReport): string {\n    const headers = ['Date', 'Session Type', 'Duration', 'Score', 'Improvements'];\n    const rows = report.trainingData.map(session => [\n      new Date(session.created_at).toLocaleDateString(),\n      session.session_type,\n      session.duration_minutes,\n      session.overall_score,\n      (session.improvement_areas || []).join('; '),\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  }\n\n  private generateTextContent(report: ProgressReport): string {\n    return `\nTENNIS PROGRESS REPORT\nGenerated: ${report.generatedAt.toLocaleDateString()}\nPeriod: ${report.dateRange.start.toLocaleDateString()} - ${report.dateRange.end.toLocaleDateString()}\n\nSUMMARY\n=======\nTotal Sessions: ${report.summary.totalSessions}\nTotal Play Time: ${report.summary.totalPlayTime} minutes\nAverage Score: ${report.summary.averageScore}\nImprovement Rate: ${report.summary.improvementRate}\n\nSKILL PROGRESSION\n================\n${report.skillProgression.map(skill => \n  `${skill.skill}: ${skill.startRating} → ${skill.endRating} (${skill.improvement > 0 ? '+' : ''}${skill.improvement})`\n).join('\\n')}\n\nACHIEVEMENTS\n===========\n${report.achievements.map(achievement => \n  `- ${achievement.title}: ${achievement.description}`\n).join('\\n')}\n    `.trim();\n  }\n\n  private generateTrainingCSV(sessions: any[]): string {\n    const headers = ['Date', 'Type', 'Title', 'Duration', 'Score', 'Feedback'];\n    const rows = sessions.map(session => [\n      new Date(session.created_at).toLocaleDateString(),\n      session.session_type,\n      session.title,\n      session.duration_minutes,\n      session.overall_score,\n      session.ai_feedback_summary || '',\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  }\n\n  private generateMatchCSV(matches: any[]): string {\n    const headers = ['Date', 'Opponent', 'Result', 'Score', 'Surface', 'Duration'];\n    const rows = matches.map(match => [\n      new Date(match.created_at).toLocaleDateString(),\n      match.opponent_name,\n      match.result,\n      match.match_score,\n      match.surface,\n      match.duration_minutes,\n    ]);\n\n    return [headers, ...rows].map(row => row.join(',')).join('\\n');\n  }\n\n  private parseCSV(content: string): any[] {\n    const lines = content.split('\\n');\n    const headers = lines[0].split(',');\n    \n    return lines.slice(1).map(line => {\n      const values = line.split(',');\n      const obj: any = {};\n      headers.forEach((header, index) => {\n        obj[header] = values[index];\n      });\n      return obj;\n    });\n  }\n\n  private getMimeTypeFromUri(uri: string): string {\n    if (uri.endsWith('.pdf')) return 'application/pdf';\n    if (uri.endsWith('.csv')) return 'text/csv';\n    if (uri.endsWith('.json')) return 'application/json';\n    if (uri.endsWith('.txt')) return 'text/plain';\n    return 'application/octet-stream';\n  }\n}\n\nexport const exportService = new ExportService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,OAAO,KAAKA,UAAU,MAAM,kBAAkB;AAC9C,OAAO,KAAKC,cAAc,MAAM,sBAAsB;AACtD,OAAO,KAAKC,OAAO,MAAM,cAAc;AAAC,IAqClCC,aAAa;EAAA,SAAAA,cAAA;IAAAC,eAAA,OAAAD,aAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,aAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAAC,uBAAA,GAAAC,iBAAA,CAIjB,WACEC,MAAc,EACdC,OAAsB,EACG;QAAAC,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QACzB,IAAI;UACF,IAAAC,IAAA,IAAAH,aAAA,GAAAE,CAAA,aAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;cAAA,OAAAC,uBAAA,CAAAC,OAAA;YAAA;YAAQC,QAAQ,GAAAN,IAAA,CAARM,QAAQ;UAEhB,IAAMC,SAAS,IAAAV,aAAA,GAAAE,CAAA,OAAG,CAAAF,aAAA,GAAAW,CAAA,UAAAZ,OAAO,CAACW,SAAS,MAAAV,aAAA,GAAAW,CAAA,UAAI;YACrCC,KAAK,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YACtDC,GAAG,EAAE,IAAIF,IAAI,CAAC;UAChB,CAAC;UAGD,IAAAG,KAAA,IAAAhB,aAAA,GAAAE,CAAA,aAAyCO,QAAQ,CAC9CQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAErB,MAAM,CAAC,CACrBsB,GAAG,CAAC,YAAY,EAAEV,SAAS,CAACE,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC,CAChDC,GAAG,CAAC,YAAY,EAAEZ,SAAS,CAACK,GAAG,CAACM,WAAW,CAAC,CAAC,CAAC,CAC9CE,KAAK,CAAC,YAAY,EAAE;cAAEC,SAAS,EAAE;YAAK,CAAC,CAAC;YAN7BC,gBAAgB,GAAAT,KAAA,CAAtBU,IAAI;UASZ,IAAAC,KAAA,IAAA3B,aAAA,GAAAE,CAAA,aAAqCO,QAAQ,CAC1CQ,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAErB,MAAM,CAAC,CACrBsB,GAAG,CAAC,YAAY,EAAEV,SAAS,CAACE,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC,CAChDC,GAAG,CAAC,YAAY,EAAEZ,SAAS,CAACK,GAAG,CAACM,WAAW,CAAC,CAAC,CAAC,CAC9CE,KAAK,CAAC,YAAY,EAAE;cAAEC,SAAS,EAAE;YAAK,CAAC,CAAC;YAN7BI,YAAY,GAAAD,KAAA,CAAlBD,IAAI;UASZ,IAAAG,KAAA,IAAA7B,aAAA,GAAAE,CAAA,aAAqCO,QAAQ,CAC1CQ,IAAI,CAAC,cAAc,CAAC,CACpBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAErB,MAAM,CAAC,CACrBsB,GAAG,CAAC,aAAa,EAAEV,SAAS,CAACE,KAAK,CAACS,WAAW,CAAC,CAAC,CAAC,CACjDC,GAAG,CAAC,aAAa,EAAEZ,SAAS,CAACK,GAAG,CAACM,WAAW,CAAC,CAAC,CAAC,CAC/CE,KAAK,CAAC,aAAa,EAAE;cAAEC,SAAS,EAAE;YAAK,CAAC,CAAC;YAN9BM,YAAY,GAAAD,KAAA,CAAlBH,IAAI;UASZ,IAAAK,KAAA,IAAA/B,aAAA,GAAAE,CAAA,aAAmCO,QAAQ,CACxCQ,IAAI,CAAC,aAAa,CAAC,CACnBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAErB,MAAM,CAAC,CACrByB,KAAK,CAAC,YAAY,EAAE;cAAEC,SAAS,EAAE;YAAK,CAAC,CAAC;YAJ7BQ,UAAU,GAAAD,KAAA,CAAhBL,IAAI;UAOZ,IAAMO,OAAO,IAAAjC,aAAA,GAAAE,CAAA,OAAG,IAAI,CAACgC,gBAAgB,CAAC,CAAAlC,aAAA,GAAAW,CAAA,UAAAc,gBAAgB,MAAAzB,aAAA,GAAAW,CAAA,UAAI,EAAE,GAAE,CAAAX,aAAA,GAAAW,CAAA,UAAAiB,YAAY,MAAA5B,aAAA,GAAAW,CAAA,UAAI,EAAE,EAAC;UAGjF,IAAMwB,gBAAgB,IAAAnC,aAAA,GAAAE,CAAA,OAAG,IAAI,CAACkC,yBAAyB,CAAC,CAAApC,aAAA,GAAAW,CAAA,UAAAqB,UAAU,MAAAhC,aAAA,GAAAW,CAAA,UAAI,EAAE,EAAC;UAEzE,IAAM0B,MAAsB,IAAArC,aAAA,GAAAE,CAAA,OAAG;YAC7BJ,MAAM,EAANA,MAAM;YACNwC,WAAW,EAAE,IAAIzB,IAAI,CAAC,CAAC;YACvBH,SAAS,EAATA,SAAS;YACTuB,OAAO,EAAPA,OAAO;YACPE,gBAAgB,EAAhBA,gBAAgB;YAChBL,YAAY,EAAE,CAAA9B,aAAA,GAAAW,CAAA,UAAAmB,YAAY,MAAA9B,aAAA,GAAAW,CAAA,UAAI,EAAE;YAChCiB,YAAY,EAAE,CAAA5B,aAAA,GAAAW,CAAA,UAAAiB,YAAY,MAAA5B,aAAA,GAAAW,CAAA,UAAI,EAAE;YAChC4B,YAAY,EAAE,CAAAvC,aAAA,GAAAW,CAAA,UAAAc,gBAAgB,MAAAzB,aAAA,GAAAW,CAAA,UAAI,EAAE;UACtC,CAAC;UAACX,aAAA,GAAAE,CAAA;UAEF,OAAOmC,MAAM;QACf,CAAC,CAAC,OAAOG,KAAK,EAAE;UAAAxC,aAAA,GAAAE,CAAA;UACduC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;UAACxC,aAAA,GAAAE,CAAA;UAC1D,MAAMsC,KAAK;QACb;MACF,CAAC;MAAA,SApEKE,sBAAsBA,CAAAC,EAAA,EAAAC,GAAA;QAAA,OAAAhD,uBAAA,CAAAiD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBJ,sBAAsB;IAAA;EAAA;IAAAhD,GAAA;IAAAC,KAAA;MAAA,IAAAoD,qBAAA,GAAAlD,iBAAA,CAyE5B,WACEwC,MAAsB,EACtBtC,OAAsB,EACL;QAAAC,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QACjB,IAAI;UACF,IAAI8C,OAAe;UACnB,IAAIC,QAAgB;UACpB,IAAIC,QAAgB;UAAClD,aAAA,GAAAE,CAAA;UAErB,QAAQH,OAAO,CAACoD,MAAM;YACpB,KAAK,KAAK;cAAAnD,aAAA,GAAAW,CAAA;cAAAX,aAAA,GAAAE,CAAA;cACR8C,OAAO,SAAS,IAAI,CAACI,kBAAkB,CAACf,MAAM,CAAC;cAACrC,aAAA,GAAAE,CAAA;cAChD+C,QAAQ,GAAG,mBAAmBZ,MAAM,CAACvC,MAAM,IAAIe,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;cAACd,aAAA,GAAAE,CAAA;cAChEgD,QAAQ,GAAG,iBAAiB;cAAClD,aAAA,GAAAE,CAAA;cAC7B;YAEF,KAAK,KAAK;cAAAF,aAAA,GAAAW,CAAA;cAAAX,aAAA,GAAAE,CAAA;cACR8C,OAAO,GAAG,IAAI,CAACK,kBAAkB,CAAChB,MAAM,CAAC;cAACrC,aAAA,GAAAE,CAAA;cAC1C+C,QAAQ,GAAG,mBAAmBZ,MAAM,CAACvC,MAAM,IAAIe,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;cAACd,aAAA,GAAAE,CAAA;cAChEgD,QAAQ,GAAG,UAAU;cAAClD,aAAA,GAAAE,CAAA;cACtB;YAEF,KAAK,MAAM;cAAAF,aAAA,GAAAW,CAAA;cAAAX,aAAA,GAAAE,CAAA;cACT8C,OAAO,GAAGM,IAAI,CAACC,SAAS,CAAClB,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;cAACrC,aAAA,GAAAE,CAAA;cAC1C+C,QAAQ,GAAG,mBAAmBZ,MAAM,CAACvC,MAAM,IAAIe,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO;cAACd,aAAA,GAAAE,CAAA;cACjEgD,QAAQ,GAAG,kBAAkB;cAAClD,aAAA,GAAAE,CAAA;cAC9B;YAEF,KAAK,KAAK;cAAAF,aAAA,GAAAW,CAAA;cAAAX,aAAA,GAAAE,CAAA;cACR8C,OAAO,GAAG,IAAI,CAACQ,mBAAmB,CAACnB,MAAM,CAAC;cAACrC,aAAA,GAAAE,CAAA;cAC3C+C,QAAQ,GAAG,mBAAmBZ,MAAM,CAACvC,MAAM,IAAIe,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;cAACd,aAAA,GAAAE,CAAA;cAChEgD,QAAQ,GAAG,YAAY;cAAClD,aAAA,GAAAE,CAAA;cACxB;YAEF;cAAAF,aAAA,GAAAW,CAAA;cAAAX,aAAA,GAAAE,CAAA;cACE,MAAM,IAAIuD,KAAK,CAAC,2BAA2B,CAAC;UAChD;UAGA,IAAMC,OAAO,IAAA1D,aAAA,GAAAE,CAAA,QAAG,GAAGd,UAAU,CAACuE,iBAAiB,GAAGV,QAAQ,EAAE;UAACjD,aAAA,GAAAE,CAAA;UAC7D,MAAMd,UAAU,CAACwE,kBAAkB,CAACF,OAAO,EAAEV,OAAO,EAAE;YACpDa,QAAQ,EAAEzE,UAAU,CAAC0E,YAAY,CAACC;UACpC,CAAC,CAAC;UAAC/D,aAAA,GAAAE,CAAA;UAEH,OAAOwD,OAAO;QAChB,CAAC,CAAC,OAAOlB,KAAK,EAAE;UAAAxC,aAAA,GAAAE,CAAA;UACduC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAACxC,aAAA,GAAAE,CAAA;UACzD,MAAMsC,KAAK;QACb;MACF,CAAC;MAAA,SAjDKwB,oBAAoBA,CAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAnB,qBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBkB,oBAAoB;IAAA;EAAA;IAAAtE,GAAA;IAAAC,KAAA;MAAA,IAAAwE,kBAAA,GAAAtE,iBAAA,CAsD1B,WAAwB6D,OAAe,EAAiB;QAAA1D,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QACtD,IAAI;UACF,IAAMkE,WAAW,IAAApE,aAAA,GAAAE,CAAA,cAASZ,OAAO,CAAC+E,gBAAgB,CAAC,CAAC;UAACrE,aAAA,GAAAE,CAAA;UAErD,IAAIkE,WAAW,EAAE;YAAApE,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACf,MAAMZ,OAAO,CAACgF,UAAU,CAACZ,OAAO,EAAE;cAChCR,QAAQ,EAAE,IAAI,CAACqB,kBAAkB,CAACb,OAAO,CAAC;cAC1Cc,WAAW,EAAE;YACf,CAAC,CAAC;UACJ,CAAC,MAAM;YAAAxE,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACL,MAAM,IAAIuD,KAAK,CAAC,yCAAyC,CAAC;UAC5D;QACF,CAAC,CAAC,OAAOjB,KAAK,EAAE;UAAAxC,aAAA,GAAAE,CAAA;UACduC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;UAACxC,aAAA,GAAAE,CAAA;UAC5C,MAAMsC,KAAK;QACb;MACF,CAAC;MAAA,SAhBKiC,iBAAiBA,CAAAC,GAAA;QAAA,OAAAP,kBAAA,CAAAtB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjB2B,iBAAiB;IAAA;EAAA;IAAA/E,GAAA;IAAAC,KAAA;MAAA,IAAAgF,mBAAA,GAAA9E,iBAAA,CAqBvB,WACEC,MAAc,EACd8E,UAAoB,EACpBzB,MAAsB,EACL;QAAAnD,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QACjB,IAAI;UACF,IAAA2E,KAAA,IAAA7E,aAAA,GAAAE,CAAA,cAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;cAAA,OAAAC,uBAAA,CAAAC,OAAA;YAAA;YAAQC,QAAQ,GAAAoE,KAAA,CAARpE,QAAQ;UAEhB,IAAAqE,KAAA,IAAA9E,aAAA,GAAAE,CAAA,cAAiCO,QAAQ,CACtCQ,IAAI,CAAC,mBAAmB,CAAC,CACzBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAErB,MAAM,CAAC,CACrBiF,EAAE,CAAC,IAAI,EAAEH,UAAU,CAAC;YAJTI,QAAQ,GAAAF,KAAA,CAAdpD,IAAI;UAIY1B,aAAA,GAAAE,CAAA;UAExB,IAAI,CAAAF,aAAA,GAAAW,CAAA,YAACqE,QAAQ,MAAAhF,aAAA,GAAAW,CAAA,WAAIqE,QAAQ,CAACC,MAAM,KAAK,CAAC,GAAE;YAAAjF,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACtC,MAAM,IAAIuD,KAAK,CAAC,4BAA4B,CAAC;UAC/C,CAAC;YAAAzD,aAAA,GAAAW,CAAA;UAAA;UAED,IAAIqC,OAAe;UACnB,IAAIC,QAAgB;UAACjD,aAAA,GAAAE,CAAA;UAErB,IAAIiD,MAAM,KAAK,KAAK,EAAE;YAAAnD,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACpB8C,OAAO,GAAG,IAAI,CAACkC,mBAAmB,CAACF,QAAQ,CAAC;YAAChF,aAAA,GAAAE,CAAA;YAC7C+C,QAAQ,GAAG,qBAAqBpC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;UAClD,CAAC,MAAM;YAAAd,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACL8C,OAAO,GAAGM,IAAI,CAACC,SAAS,CAACyB,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;YAAChF,aAAA,GAAAE,CAAA;YAC5C+C,QAAQ,GAAG,qBAAqBpC,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO;UACnD;UAEA,IAAM4C,OAAO,IAAA1D,aAAA,GAAAE,CAAA,QAAG,GAAGd,UAAU,CAACuE,iBAAiB,GAAGV,QAAQ,EAAE;UAACjD,aAAA,GAAAE,CAAA;UAC7D,MAAMd,UAAU,CAACwE,kBAAkB,CAACF,OAAO,EAAEV,OAAO,EAAE;YACpDa,QAAQ,EAAEzE,UAAU,CAAC0E,YAAY,CAACC;UACpC,CAAC,CAAC;UAAC/D,aAAA,GAAAE,CAAA;UAEH,OAAOwD,OAAO;QAChB,CAAC,CAAC,OAAOlB,KAAK,EAAE;UAAAxC,aAAA,GAAAE,CAAA;UACduC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAACxC,aAAA,GAAAE,CAAA;UACvD,MAAMsC,KAAK;QACb;MACF,CAAC;MAAA,SAvCK2C,kBAAkBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAX,mBAAA,CAAA9B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBqC,kBAAkB;IAAA;EAAA;IAAAzF,GAAA;IAAAC,KAAA;MAAA,IAAA4F,gBAAA,GAAA1F,iBAAA,CA4CxB,WACEC,MAAc,EACd0F,QAAkB,EAClBrC,MAAsB,EACL;QAAAnD,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QACjB,IAAI;UACF,IAAAuF,KAAA,IAAAzF,aAAA,GAAAE,CAAA,cAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;cAAA,OAAAC,uBAAA,CAAAC,OAAA;YAAA;YAAQC,QAAQ,GAAAgF,KAAA,CAARhF,QAAQ;UAEhB,IAAAiF,KAAA,IAAA1F,aAAA,GAAAE,CAAA,cAAgCO,QAAQ,CACrCQ,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXC,EAAE,CAAC,SAAS,EAAErB,MAAM,CAAC,CACrBiF,EAAE,CAAC,IAAI,EAAES,QAAQ,CAAC;YAJPG,OAAO,GAAAD,KAAA,CAAbhE,IAAI;UAIU1B,aAAA,GAAAE,CAAA;UAEtB,IAAI,CAAAF,aAAA,GAAAW,CAAA,YAACgF,OAAO,MAAA3F,aAAA,GAAAW,CAAA,WAAIgF,OAAO,CAACV,MAAM,KAAK,CAAC,GAAE;YAAAjF,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACpC,MAAM,IAAIuD,KAAK,CAAC,wBAAwB,CAAC;UAC3C,CAAC;YAAAzD,aAAA,GAAAW,CAAA;UAAA;UAED,IAAIqC,OAAe;UACnB,IAAIC,QAAgB;UAACjD,aAAA,GAAAE,CAAA;UAErB,IAAIiD,MAAM,KAAK,KAAK,EAAE;YAAAnD,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACpB8C,OAAO,GAAG,IAAI,CAAC4C,gBAAgB,CAACD,OAAO,CAAC;YAAC3F,aAAA,GAAAE,CAAA;YACzC+C,QAAQ,GAAG,iBAAiBpC,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;UAC9C,CAAC,MAAM;YAAAd,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACL8C,OAAO,GAAGM,IAAI,CAACC,SAAS,CAACoC,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;YAAC3F,aAAA,GAAAE,CAAA;YAC3C+C,QAAQ,GAAG,iBAAiBpC,IAAI,CAACC,GAAG,CAAC,CAAC,OAAO;UAC/C;UAEA,IAAM4C,OAAO,IAAA1D,aAAA,GAAAE,CAAA,QAAG,GAAGd,UAAU,CAACuE,iBAAiB,GAAGV,QAAQ,EAAE;UAACjD,aAAA,GAAAE,CAAA;UAC7D,MAAMd,UAAU,CAACwE,kBAAkB,CAACF,OAAO,EAAEV,OAAO,EAAE;YACpDa,QAAQ,EAAEzE,UAAU,CAAC0E,YAAY,CAACC;UACpC,CAAC,CAAC;UAAC/D,aAAA,GAAAE,CAAA;UAEH,OAAOwD,OAAO;QAChB,CAAC,CAAC,OAAOlB,KAAK,EAAE;UAAAxC,aAAA,GAAAE,CAAA;UACduC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UAACxC,aAAA,GAAAE,CAAA;UACpD,MAAMsC,KAAK;QACb;MACF,CAAC;MAAA,SAvCKqD,eAAeA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAT,gBAAA,CAAA1C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAf+C,eAAe;IAAA;EAAA;IAAAnG,GAAA;IAAAC,KAAA;MAAA,IAAAsG,WAAA,GAAApG,iBAAA,CA4CrB,aAAiC;QAAAG,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAC/B,IAAI;UACF,IAAMgG,MAAM,IAAAlG,aAAA,GAAAE,CAAA,cAASb,cAAc,CAAC8G,gBAAgB,CAAC;YACnDC,IAAI,EAAE,CAAC,kBAAkB,EAAE,UAAU,CAAC;YACtCC,oBAAoB,EAAE;UACxB,CAAC,CAAC;UAACrG,aAAA,GAAAE,CAAA;UAEH,IAAIgG,MAAM,CAACI,QAAQ,EAAE;YAAAtG,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YACnB,OAAO,IAAI;UACb,CAAC;YAAAF,aAAA,GAAAW,CAAA;UAAA;UAED,IAAM4F,WAAW,IAAAvG,aAAA,GAAAE,CAAA,cAASd,UAAU,CAACoH,iBAAiB,CAACN,MAAM,CAACO,MAAM,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC;UAAC1G,aAAA,GAAAE,CAAA;UAE7E,IAAIgG,MAAM,CAACO,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAAA5G,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YAC3C,OAAOoD,IAAI,CAACuD,KAAK,CAACN,WAAW,CAAC;UAChC,CAAC,MAAM;YAAAvG,aAAA,GAAAW,CAAA;YAAAX,aAAA,GAAAE,CAAA;YAAA,IAAIgG,MAAM,CAACO,MAAM,CAAC,CAAC,CAAC,CAACE,IAAI,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;cAAA5G,aAAA,GAAAW,CAAA;cAAAX,aAAA,GAAAE,CAAA;cACjD,OAAO,IAAI,CAAC4G,QAAQ,CAACP,WAAW,CAAC;YACnC,CAAC;cAAAvG,aAAA,GAAAW,CAAA;YAAA;UAAD;UAACX,aAAA,GAAAE,CAAA;UAED,MAAM,IAAIuD,KAAK,CAAC,yBAAyB,CAAC;QAC5C,CAAC,CAAC,OAAOjB,KAAK,EAAE;UAAAxC,aAAA,GAAAE,CAAA;UACduC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAACxC,aAAA,GAAAE,CAAA;UAC9C,MAAMsC,KAAK;QACb;MACF,CAAC;MAAA,SAxBKuE,UAAUA,CAAA;QAAA,OAAAd,WAAA,CAAApD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAViE,UAAU;IAAA;EAAA;IAAArH,GAAA;IAAAC,KAAA,EA4BhB,SAAQuC,gBAAgBA,CAACT,gBAAuB,EAAEG,YAAmB,EAAE;MAAA5B,aAAA,GAAAC,CAAA;MACrE,IAAM+G,aAAa,IAAAhH,aAAA,GAAAE,CAAA,QAAGuB,gBAAgB,CAACwD,MAAM;MAC7C,IAAMgC,aAAa,IAAAjH,aAAA,GAAAE,CAAA,QAAGuB,gBAAgB,CAACyF,MAAM,CAC3C,UAACC,GAAG,EAAEC,OAAO,EAAK;QAAApH,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,OAAAiH,GAAG,IAAI,CAAAnH,aAAA,GAAAW,CAAA,WAAAyG,OAAO,CAACC,gBAAgB,MAAArH,aAAA,GAAAW,CAAA,WAAI,CAAC,EAAC;MAAD,CAAC,EAAE,CAC3D,CAAC;MACD,IAAM2G,YAAY,IAAAtH,aAAA,GAAAE,CAAA,QAAGuB,gBAAgB,CAACwD,MAAM,GAAG,CAAC,IAAAjF,aAAA,GAAAW,CAAA,WAC5Cc,gBAAgB,CAACyF,MAAM,CAAC,UAACC,GAAG,EAAEC,OAAO,EAAK;QAAApH,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,OAAAiH,GAAG,IAAI,CAAAnH,aAAA,GAAAW,CAAA,WAAAyG,OAAO,CAACG,aAAa,MAAAvH,aAAA,GAAAW,CAAA,WAAI,CAAC,EAAC;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGc,gBAAgB,CAACwD,MAAM,KAAAjF,aAAA,GAAAW,CAAA,WAC1G,CAAC;MAGL,IAAM6G,eAAe,IAAAxH,aAAA,GAAAE,CAAA,QAAGuB,gBAAgB,CAACwD,MAAM,GAAG,CAAC,IAAAjF,aAAA,GAAAW,CAAA,WAC/C,CAAC,CAAC,CAAAX,aAAA,GAAAW,CAAA,WAAAc,gBAAgB,CAACA,gBAAgB,CAACwD,MAAM,GAAG,CAAC,CAAC,CAACsC,aAAa,MAAAvH,aAAA,GAAAW,CAAA,WAAI,CAAC,MAChE,CAAAX,aAAA,GAAAW,CAAA,WAAAc,gBAAgB,CAAC,CAAC,CAAC,CAAC8F,aAAa,MAAAvH,aAAA,GAAAW,CAAA,WAAI,CAAC,EAAC,IAAIc,gBAAgB,CAACwD,MAAM,KAAAjF,aAAA,GAAAW,CAAA,WACpE,CAAC;MAACX,aAAA,GAAAE,CAAA;MAEN,OAAO;QACL8G,aAAa,EAAbA,aAAa;QACbC,aAAa,EAAbA,aAAa;QACbK,YAAY,EAAEG,IAAI,CAACC,KAAK,CAACJ,YAAY,CAAC;QACtCE,eAAe,EAAEC,IAAI,CAACC,KAAK,CAACF,eAAe,GAAG,GAAG,CAAC,GAAG;MACvD,CAAC;IACH;EAAC;IAAA9H,GAAA;IAAAC,KAAA,EAED,SAAQyC,yBAAyBA,CAACJ,UAAiB,EAAE;MAAAhC,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAE,CAAA;MACnD,IAAI8B,UAAU,CAACiD,MAAM,GAAG,CAAC,EAAE;QAAAjF,aAAA,GAAAW,CAAA;QAAAX,aAAA,GAAAE,CAAA;QACzB,OAAO,EAAE;MACX,CAAC;QAAAF,aAAA,GAAAW,CAAA;MAAA;MAED,IAAMgH,UAAU,IAAA3H,aAAA,GAAAE,CAAA,QAAG8B,UAAU,CAAC,CAAC,CAAC;MAChC,IAAM4F,SAAS,IAAA5H,aAAA,GAAAE,CAAA,QAAG8B,UAAU,CAACA,UAAU,CAACiD,MAAM,GAAG,CAAC,CAAC;MACnD,IAAM4C,MAAM,IAAA7H,aAAA,GAAAE,CAAA,QAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,CAAC;MAACF,aAAA,GAAAE,CAAA;MAElG,OAAO2H,MAAM,CAACC,GAAG,CAAC,UAAAC,KAAK,EAAK;QAAA/H,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA;UAC1B6H,KAAK,EAALA,KAAK;UACLC,WAAW,EAAE,CAAAhI,aAAA,GAAAW,CAAA,WAAAgH,UAAU,CAACI,KAAK,CAAC,MAAA/H,aAAA,GAAAW,CAAA,WAAI,CAAC;UACnCsH,SAAS,EAAE,CAAAjI,aAAA,GAAAW,CAAA,WAAAiH,SAAS,CAACG,KAAK,CAAC,MAAA/H,aAAA,GAAAW,CAAA,WAAI,CAAC;UAChCuH,WAAW,EAAE,CAAC,CAAAlI,aAAA,GAAAW,CAAA,WAAAiH,SAAS,CAACG,KAAK,CAAC,MAAA/H,aAAA,GAAAW,CAAA,WAAI,CAAC,MAAK,CAAAX,aAAA,GAAAW,CAAA,WAAAgH,UAAU,CAACI,KAAK,CAAC,MAAA/H,aAAA,GAAAW,CAAA,WAAI,CAAC;QAChE,CAAC;MAAD,CAAE,CAAC;IACL;EAAC;IAAAjB,GAAA;IAAAC,KAAA;MAAA,IAAAwI,mBAAA,GAAAtI,iBAAA,CAED,WAAiCwC,MAAsB,EAAmB;QAAArC,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAGxE,OAAO;AACX;AACA;AACA;AACA;AACA;AACA,+BAA+BmC,MAAM,CAACJ,OAAO,CAAC+E,aAAa;AAC3D,gCAAgC3E,MAAM,CAACJ,OAAO,CAACgF,aAAa;AAC5D,8BAA8B5E,MAAM,CAACJ,OAAO,CAACqF,YAAY;AACzD;AACA,YAAYjF,MAAM,CAACF,gBAAgB,CAAC2F,GAAG,CAAC,UAAAC,KAAK,EACjC;UAAA/H,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAE,CAAA;UAAA,aAAM6H,KAAK,CAACA,KAAK,KAAKA,KAAK,CAACC,WAAW,MAAMD,KAAK,CAACE,SAAS,KAAKF,KAAK,CAACG,WAAW,GAAG,CAAC,IAAAlI,aAAA,GAAAW,CAAA,WAAG,GAAG,KAAAX,aAAA,GAAAW,CAAA,WAAG,EAAE,IAAGoH,KAAK,CAACG,WAAW,OAAO;QAAD,CAC7H,CAAC,CAACE,IAAI,CAAC,EAAE,CAAC;AACpB;AACA;AACA,KAAK;MACH,CAAC;MAAA,SAnBahF,kBAAkBA,CAAAiF,IAAA;QAAA,OAAAF,mBAAA,CAAAtF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBM,kBAAkB;IAAA;EAAA;IAAA1D,GAAA;IAAAC,KAAA,EAqBhC,SAAQ0D,kBAAkBA,CAAChB,MAAsB,EAAU;MAAArC,aAAA,GAAAC,CAAA;MACzD,IAAMqI,OAAO,IAAAtI,aAAA,GAAAE,CAAA,SAAG,CAAC,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,OAAO,EAAE,cAAc,CAAC;MAC7E,IAAMqI,IAAI,IAAAvI,aAAA,GAAAE,CAAA,SAAGmC,MAAM,CAACE,YAAY,CAACuF,GAAG,CAAC,UAAAV,OAAO,EAAI;QAAApH,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,QAC9C,IAAIW,IAAI,CAACuG,OAAO,CAACoB,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EACjDrB,OAAO,CAACsB,YAAY,EACpBtB,OAAO,CAACC,gBAAgB,EACxBD,OAAO,CAACG,aAAa,EACrB,CAAC,CAAAvH,aAAA,GAAAW,CAAA,WAAAyG,OAAO,CAACuB,iBAAiB,MAAA3I,aAAA,GAAAW,CAAA,WAAI,EAAE,GAAEyH,IAAI,CAAC,IAAI,CAAC,CAC7C;MAAD,CAAC,CAAC;MAACpI,aAAA,GAAAE,CAAA;MAEH,OAAO,CAACoI,OAAO,EAAAM,MAAA,CAAAC,kBAAA,CAAKN,IAAI,GAAET,GAAG,CAAC,UAAAgB,GAAG,EAAI;QAAA9I,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,OAAA4I,GAAG,CAACV,IAAI,CAAC,GAAG,CAAC;MAAD,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;IAChE;EAAC;IAAA1I,GAAA;IAAAC,KAAA,EAED,SAAQ6D,mBAAmBA,CAACnB,MAAsB,EAAU;MAAArC,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAE,CAAA;MAC1D,OAAO;AACX;AACA,aAAamC,MAAM,CAACC,WAAW,CAACmG,kBAAkB,CAAC,CAAC;AACpD,UAAUpG,MAAM,CAAC3B,SAAS,CAACE,KAAK,CAAC6H,kBAAkB,CAAC,CAAC,MAAMpG,MAAM,CAAC3B,SAAS,CAACK,GAAG,CAAC0H,kBAAkB,CAAC,CAAC;AACpG;AACA;AACA;AACA,kBAAkBpG,MAAM,CAACJ,OAAO,CAAC+E,aAAa;AAC9C,mBAAmB3E,MAAM,CAACJ,OAAO,CAACgF,aAAa;AAC/C,iBAAiB5E,MAAM,CAACJ,OAAO,CAACqF,YAAY;AAC5C,oBAAoBjF,MAAM,CAACJ,OAAO,CAACuF,eAAe;AAClD;AACA;AACA;AACA,EAAEnF,MAAM,CAACF,gBAAgB,CAAC2F,GAAG,CAAC,UAAAC,KAAK,EACjC;QAAA/H,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,UAAG6H,KAAK,CAACA,KAAK,KAAKA,KAAK,CAACC,WAAW,MAAMD,KAAK,CAACE,SAAS,KAAKF,KAAK,CAACG,WAAW,GAAG,CAAC,IAAAlI,aAAA,GAAAW,CAAA,WAAG,GAAG,KAAAX,aAAA,GAAAW,CAAA,WAAG,EAAE,IAAGoH,KAAK,CAACG,WAAW,GAAG;MAAD,CACtH,CAAC,CAACE,IAAI,CAAC,IAAI,CAAC;AACZ;AACA;AACA;AACA,EAAE/F,MAAM,CAACP,YAAY,CAACgG,GAAG,CAAC,UAAAiB,WAAW,EACnC;QAAA/I,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,YAAK6I,WAAW,CAACC,KAAK,KAAKD,WAAW,CAACE,WAAW,EAAE;MAAD,CACrD,CAAC,CAACb,IAAI,CAAC,IAAI,CAAC;AACZ,KAAK,CAACc,IAAI,CAAC,CAAC;IACV;EAAC;IAAAxJ,GAAA;IAAAC,KAAA,EAED,SAAQuF,mBAAmBA,CAACF,QAAe,EAAU;MAAAhF,aAAA,GAAAC,CAAA;MACnD,IAAMqI,OAAO,IAAAtI,aAAA,GAAAE,CAAA,SAAG,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,CAAC;MAC1E,IAAMqI,IAAI,IAAAvI,aAAA,GAAAE,CAAA,SAAG8E,QAAQ,CAAC8C,GAAG,CAAC,UAAAV,OAAO,EAAI;QAAApH,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,QACnC,IAAIW,IAAI,CAACuG,OAAO,CAACoB,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EACjDrB,OAAO,CAACsB,YAAY,EACpBtB,OAAO,CAAC4B,KAAK,EACb5B,OAAO,CAACC,gBAAgB,EACxBD,OAAO,CAACG,aAAa,EACrB,CAAAvH,aAAA,GAAAW,CAAA,WAAAyG,OAAO,CAAC+B,mBAAmB,MAAAnJ,aAAA,GAAAW,CAAA,WAAI,EAAE,EAClC;MAAD,CAAC,CAAC;MAACX,aAAA,GAAAE,CAAA;MAEH,OAAO,CAACoI,OAAO,EAAAM,MAAA,CAAAC,kBAAA,CAAKN,IAAI,GAAET,GAAG,CAAC,UAAAgB,GAAG,EAAI;QAAA9I,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,OAAA4I,GAAG,CAACV,IAAI,CAAC,GAAG,CAAC;MAAD,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;IAChE;EAAC;IAAA1I,GAAA;IAAAC,KAAA,EAED,SAAQiG,gBAAgBA,CAACD,OAAc,EAAU;MAAA3F,aAAA,GAAAC,CAAA;MAC/C,IAAMqI,OAAO,IAAAtI,aAAA,GAAAE,CAAA,SAAG,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC;MAC9E,IAAMqI,IAAI,IAAAvI,aAAA,GAAAE,CAAA,SAAGyF,OAAO,CAACmC,GAAG,CAAC,UAAAsB,KAAK,EAAI;QAAApJ,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,QAChC,IAAIW,IAAI,CAACuI,KAAK,CAACZ,UAAU,CAAC,CAACC,kBAAkB,CAAC,CAAC,EAC/CW,KAAK,CAACC,aAAa,EACnBD,KAAK,CAAClD,MAAM,EACZkD,KAAK,CAACE,WAAW,EACjBF,KAAK,CAACG,OAAO,EACbH,KAAK,CAAC/B,gBAAgB,CACvB;MAAD,CAAC,CAAC;MAACrH,aAAA,GAAAE,CAAA;MAEH,OAAO,CAACoI,OAAO,EAAAM,MAAA,CAAAC,kBAAA,CAAKN,IAAI,GAAET,GAAG,CAAC,UAAAgB,GAAG,EAAI;QAAA9I,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAE,CAAA;QAAA,OAAA4I,GAAG,CAACV,IAAI,CAAC,GAAG,CAAC;MAAD,CAAC,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC;IAChE;EAAC;IAAA1I,GAAA;IAAAC,KAAA,EAED,SAAQmH,QAAQA,CAAC9D,OAAe,EAAS;MAAAhD,aAAA,GAAAC,CAAA;MACvC,IAAMuJ,KAAK,IAAAxJ,aAAA,GAAAE,CAAA,SAAG8C,OAAO,CAACyG,KAAK,CAAC,IAAI,CAAC;MACjC,IAAMnB,OAAO,IAAAtI,aAAA,GAAAE,CAAA,SAAGsJ,KAAK,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC;MAACzJ,aAAA,GAAAE,CAAA;MAEpC,OAAOsJ,KAAK,CAACE,KAAK,CAAC,CAAC,CAAC,CAAC5B,GAAG,CAAC,UAAA6B,IAAI,EAAI;QAAA3J,aAAA,GAAAC,CAAA;QAChC,IAAM2J,MAAM,IAAA5J,aAAA,GAAAE,CAAA,SAAGyJ,IAAI,CAACF,KAAK,CAAC,GAAG,CAAC;QAC9B,IAAMI,GAAQ,IAAA7J,aAAA,GAAAE,CAAA,SAAG,CAAC,CAAC;QAACF,aAAA,GAAAE,CAAA;QACpBoI,OAAO,CAACwB,OAAO,CAAC,UAACC,MAAM,EAAEC,KAAK,EAAK;UAAAhK,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAE,CAAA;UACjC2J,GAAG,CAACE,MAAM,CAAC,GAAGH,MAAM,CAACI,KAAK,CAAC;QAC7B,CAAC,CAAC;QAAChK,aAAA,GAAAE,CAAA;QACH,OAAO2J,GAAG;MACZ,CAAC,CAAC;IACJ;EAAC;IAAAnK,GAAA;IAAAC,KAAA,EAED,SAAQ4E,kBAAkBA,CAACmC,GAAW,EAAU;MAAA1G,aAAA,GAAAC,CAAA;MAAAD,aAAA,GAAAE,CAAA;MAC9C,IAAIwG,GAAG,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAAA5G,aAAA,GAAAW,CAAA;QAAAX,aAAA,GAAAE,CAAA;QAAA,OAAO,iBAAiB;MAAA,CAAC;QAAAF,aAAA,GAAAW,CAAA;MAAA;MAAAX,aAAA,GAAAE,CAAA;MACnD,IAAIwG,GAAG,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAAA5G,aAAA,GAAAW,CAAA;QAAAX,aAAA,GAAAE,CAAA;QAAA,OAAO,UAAU;MAAA,CAAC;QAAAF,aAAA,GAAAW,CAAA;MAAA;MAAAX,aAAA,GAAAE,CAAA;MAC5C,IAAIwG,GAAG,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;QAAA5G,aAAA,GAAAW,CAAA;QAAAX,aAAA,GAAAE,CAAA;QAAA,OAAO,kBAAkB;MAAA,CAAC;QAAAF,aAAA,GAAAW,CAAA;MAAA;MAAAX,aAAA,GAAAE,CAAA;MACrD,IAAIwG,GAAG,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;QAAA5G,aAAA,GAAAW,CAAA;QAAAX,aAAA,GAAAE,CAAA;QAAA,OAAO,YAAY;MAAA,CAAC;QAAAF,aAAA,GAAAW,CAAA;MAAA;MAAAX,aAAA,GAAAE,CAAA;MAC9C,OAAO,0BAA0B;IACnC;EAAC;AAAA;AAGH,OAAO,IAAM+J,aAAa,IAAAjK,aAAA,GAAAE,CAAA,SAAG,IAAIX,aAAa,CAAC,CAAC", "ignoreList": []}