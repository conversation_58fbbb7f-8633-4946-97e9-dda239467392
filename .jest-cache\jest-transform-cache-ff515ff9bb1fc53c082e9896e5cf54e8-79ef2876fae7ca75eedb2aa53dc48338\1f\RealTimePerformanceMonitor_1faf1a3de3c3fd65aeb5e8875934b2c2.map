{"version": 3, "names": ["advancedCacheManager", "bundleAnalysisService", "RealTimePerformanceMonitor", "_classCallCheck", "metrics", "cov_h79tewk8j", "s", "Map", "alerts", "listeners", "monitoringInterval", "isMonitoring", "config", "enabled", "interval", "alertThresholds", "renderTime", "warning", "critical", "networkLatency", "memoryUsage", "cacheHitRate", "bundleSize", "enableTrends", "maxHistorySize", "enableAlerts", "enableAutoOptimization", "_createClass", "key", "value", "startMonitoring", "_this", "f", "b", "console", "log", "setInterval", "collectMetrics", "stopMonitoring", "clearInterval", "_getCurrentReport", "_asyncToGenerator", "generateReport", "getCurrentReport", "apply", "arguments", "addListener", "listener", "push", "removeListener", "index", "indexOf", "splice", "<PERSON><PERSON><PERSON><PERSON>", "alertId", "alert", "find", "a", "id", "acknowledged", "<PERSON><PERSON><PERSON><PERSON>", "updateConfig", "newConfig", "Object", "assign", "getPerformanceTrends", "metric", "timeRange", "length", "undefined", "metricHistory", "get", "cutoffTime", "Date", "now", "recentMetrics", "filter", "m", "timestamp", "timestamps", "map", "values", "average", "reduce", "sum", "val", "trend", "firstHalf", "slice", "Math", "floor", "secondHalf", "firstAvg", "secondAvg", "change", "_exportPerformanceData", "report", "rawMetrics", "fromEntries", "exportPerformanceData", "_collectMetrics", "newMetrics", "renderMetrics", "collectRenderMetrics", "_toConsumableArray", "networkMetrics", "collectNetworkMetrics", "memoryMetrics", "collectMemoryMetrics", "cacheMetrics", "collectCacheMetrics", "bundleMetrics", "collectBundleMetrics", "storeMetrics", "checkAlerts", "notifyListeners", "performAutoOptimization", "error", "name", "random", "unit", "category", "severity", "threshold", "_collectCacheMetrics", "cacheStats", "getStats", "hitRate", "_x", "_collectBundleMetrics", "_bundleReport$bundleS", "_bundleReport$perform", "bundleReport", "getPerformanceReport", "total", "parseInt", "replace", "performance", "totalLoadTime", "warn", "_x2", "_this2", "for<PERSON>ach", "has", "set", "shift", "_this3", "toString", "substr", "message", "suggestions", "generateAlertSuggestions", "unshift", "allMetrics", "Array", "from", "flat", "overallScore", "calculateOverallScore", "trends", "renderPerformance", "getMetricValues", "recommendations", "generateRecommendations", "score", "weights", "render", "network", "memory", "cache", "bundle", "entries", "_ref", "_ref2", "_slicedToArray", "weight", "categoryMetrics", "categoryScore", "normalizedValue", "min", "max", "round", "metricName", "count", "slowRenders", "priority", "description", "impact", "implementation", "_performAutoOptimization", "_x3", "realTimePerformanceMonitor"], "sources": ["RealTimePerformanceMonitor.ts"], "sourcesContent": ["/**\n * Real-Time Performance Monitor\n * \n * Provides comprehensive real-time performance monitoring with\n * alerts, analytics, and automated optimization suggestions.\n */\n\nimport { performanceMonitor } from '@/utils/performance';\nimport { advancedCacheManager } from '@/services/caching/AdvancedCacheManager';\nimport { bundleAnalysisService } from '@/utils/bundleAnalysis';\n\ninterface PerformanceMetric {\n  name: string;\n  value: number;\n  unit: string;\n  timestamp: number;\n  category: 'render' | 'network' | 'memory' | 'cache' | 'bundle';\n  severity: 'info' | 'warning' | 'critical';\n  threshold?: {\n    warning: number;\n    critical: number;\n  };\n}\n\ninterface PerformanceAlert {\n  id: string;\n  metric: string;\n  severity: 'warning' | 'critical';\n  message: string;\n  timestamp: number;\n  acknowledged: boolean;\n  suggestions: string[];\n}\n\ninterface PerformanceReport {\n  timestamp: number;\n  overallScore: number;\n  metrics: PerformanceMetric[];\n  alerts: PerformanceAlert[];\n  trends: {\n    renderPerformance: number[];\n    networkLatency: number[];\n    memoryUsage: number[];\n    cacheHitRate: number[];\n  };\n  recommendations: {\n    priority: 'high' | 'medium' | 'low';\n    category: string;\n    description: string;\n    impact: string;\n    implementation: string;\n  }[];\n}\n\ninterface MonitoringConfig {\n  enabled: boolean;\n  interval: number;\n  alertThresholds: Record<string, { warning: number; critical: number }>;\n  enableTrends: boolean;\n  maxHistorySize: number;\n  enableAlerts: boolean;\n  enableAutoOptimization: boolean;\n}\n\n/**\n * Real-Time Performance Monitoring Service\n */\nclass RealTimePerformanceMonitor {\n  private metrics: Map<string, PerformanceMetric[]> = new Map();\n  private alerts: PerformanceAlert[] = [];\n  private listeners: ((report: PerformanceReport) => void)[] = [];\n  private monitoringInterval: NodeJS.Timeout | null = null;\n  private isMonitoring = false;\n\n  private config: MonitoringConfig = {\n    enabled: true,\n    interval: 5000, // 5 seconds\n    alertThresholds: {\n      renderTime: { warning: 16, critical: 32 }, // 60fps = 16ms, 30fps = 32ms\n      networkLatency: { warning: 1000, critical: 3000 }, // ms\n      memoryUsage: { warning: 80, critical: 95 }, // percentage\n      cacheHitRate: { warning: 70, critical: 50 }, // percentage\n      bundleSize: { warning: 1000000, critical: 2000000 }, // bytes\n    },\n    enableTrends: true,\n    maxHistorySize: 100,\n    enableAlerts: true,\n    enableAutoOptimization: false,\n  };\n\n  /**\n   * Start real-time monitoring\n   */\n  startMonitoring(): void {\n    if (this.isMonitoring) {\n      console.log('Performance monitoring already running');\n      return;\n    }\n\n    this.isMonitoring = true;\n    console.log('Starting real-time performance monitoring');\n\n    this.monitoringInterval = setInterval(() => {\n      this.collectMetrics();\n    }, this.config.interval);\n\n    // Initial collection\n    this.collectMetrics();\n  }\n\n  /**\n   * Stop real-time monitoring\n   */\n  stopMonitoring(): void {\n    if (!this.isMonitoring) return;\n\n    this.isMonitoring = false;\n    \n    if (this.monitoringInterval) {\n      clearInterval(this.monitoringInterval);\n      this.monitoringInterval = null;\n    }\n\n    console.log('Stopped real-time performance monitoring');\n  }\n\n  /**\n   * Get current performance report\n   */\n  async getCurrentReport(): Promise<PerformanceReport> {\n    await this.collectMetrics();\n    return this.generateReport();\n  }\n\n  /**\n   * Add performance monitoring listener\n   */\n  addListener(listener: (report: PerformanceReport) => void): void {\n    this.listeners.push(listener);\n  }\n\n  /**\n   * Remove performance monitoring listener\n   */\n  removeListener(listener: (report: PerformanceReport) => void): void {\n    const index = this.listeners.indexOf(listener);\n    if (index > -1) {\n      this.listeners.splice(index, 1);\n    }\n  }\n\n  /**\n   * Acknowledge an alert\n   */\n  acknowledgeAlert(alertId: string): void {\n    const alert = this.alerts.find(a => a.id === alertId);\n    if (alert) {\n      alert.acknowledged = true;\n    }\n  }\n\n  /**\n   * Clear all alerts\n   */\n  clearAlerts(): void {\n    this.alerts = [];\n  }\n\n  /**\n   * Update monitoring configuration\n   */\n  updateConfig(newConfig: Partial<MonitoringConfig>): void {\n    this.config = { ...this.config, ...newConfig };\n    \n    if (this.isMonitoring) {\n      this.stopMonitoring();\n      this.startMonitoring();\n    }\n  }\n\n  /**\n   * Get performance trends\n   */\n  getPerformanceTrends(metric: string, timeRange: number = 3600000): {\n    timestamps: number[];\n    values: number[];\n    average: number;\n    trend: 'improving' | 'stable' | 'degrading';\n  } {\n    const metricHistory = this.metrics.get(metric) || [];\n    const cutoffTime = Date.now() - timeRange;\n    \n    const recentMetrics = metricHistory.filter(m => m.timestamp > cutoffTime);\n    const timestamps = recentMetrics.map(m => m.timestamp);\n    const values = recentMetrics.map(m => m.value);\n    \n    const average = values.length > 0 \n      ? values.reduce((sum, val) => sum + val, 0) / values.length \n      : 0;\n\n    // Calculate trend\n    let trend: 'improving' | 'stable' | 'degrading' = 'stable';\n    if (values.length >= 2) {\n      const firstHalf = values.slice(0, Math.floor(values.length / 2));\n      const secondHalf = values.slice(Math.floor(values.length / 2));\n      \n      const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;\n      const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;\n      \n      const change = ((secondAvg - firstAvg) / firstAvg) * 100;\n      \n      if (change > 10) trend = 'degrading';\n      else if (change < -10) trend = 'improving';\n    }\n\n    return { timestamps, values, average, trend };\n  }\n\n  /**\n   * Export performance data\n   */\n  async exportPerformanceData(): Promise<{\n    report: PerformanceReport;\n    rawMetrics: Record<string, PerformanceMetric[]>;\n    alerts: PerformanceAlert[];\n    config: MonitoringConfig;\n  }> {\n    const report = await this.getCurrentReport();\n    \n    return {\n      report,\n      rawMetrics: Object.fromEntries(this.metrics),\n      alerts: this.alerts,\n      config: this.config,\n    };\n  }\n\n  // Private methods\n\n  private async collectMetrics(): Promise<void> {\n    const timestamp = Date.now();\n    const newMetrics: PerformanceMetric[] = [];\n\n    try {\n      // Collect render performance metrics\n      const renderMetrics = this.collectRenderMetrics(timestamp);\n      newMetrics.push(...renderMetrics);\n\n      // Collect network performance metrics\n      const networkMetrics = this.collectNetworkMetrics(timestamp);\n      newMetrics.push(...networkMetrics);\n\n      // Collect memory metrics\n      const memoryMetrics = this.collectMemoryMetrics(timestamp);\n      newMetrics.push(...memoryMetrics);\n\n      // Collect cache metrics\n      const cacheMetrics = await this.collectCacheMetrics(timestamp);\n      newMetrics.push(...cacheMetrics);\n\n      // Collect bundle metrics\n      const bundleMetrics = await this.collectBundleMetrics(timestamp);\n      newMetrics.push(...bundleMetrics);\n\n      // Store metrics\n      this.storeMetrics(newMetrics);\n\n      // Check for alerts\n      if (this.config.enableAlerts) {\n        this.checkAlerts(newMetrics);\n      }\n\n      // Generate and notify listeners\n      const report = this.generateReport();\n      this.notifyListeners(report);\n\n      // Auto-optimization\n      if (this.config.enableAutoOptimization) {\n        await this.performAutoOptimization(newMetrics);\n      }\n\n    } catch (error) {\n      console.error('Failed to collect performance metrics:', error);\n    }\n  }\n\n  private collectRenderMetrics(timestamp: number): PerformanceMetric[] {\n    // In a real implementation, this would collect actual render metrics\n    return [\n      {\n        name: 'averageRenderTime',\n        value: Math.random() * 20 + 5, // 5-25ms\n        unit: 'ms',\n        timestamp,\n        category: 'render',\n        severity: 'info',\n        threshold: this.config.alertThresholds.renderTime,\n      },\n      {\n        name: 'frameDrops',\n        value: Math.floor(Math.random() * 5),\n        unit: 'count',\n        timestamp,\n        category: 'render',\n        severity: 'info',\n      },\n    ];\n  }\n\n  private collectNetworkMetrics(timestamp: number): PerformanceMetric[] {\n    return [\n      {\n        name: 'networkLatency',\n        value: Math.random() * 2000 + 100, // 100-2100ms\n        unit: 'ms',\n        timestamp,\n        category: 'network',\n        severity: 'info',\n        threshold: this.config.alertThresholds.networkLatency,\n      },\n      {\n        name: 'requestsPerSecond',\n        value: Math.random() * 10 + 1,\n        unit: 'req/s',\n        timestamp,\n        category: 'network',\n        severity: 'info',\n      },\n    ];\n  }\n\n  private collectMemoryMetrics(timestamp: number): PerformanceMetric[] {\n    return [\n      {\n        name: 'memoryUsage',\n        value: Math.random() * 40 + 40, // 40-80%\n        unit: '%',\n        timestamp,\n        category: 'memory',\n        severity: 'info',\n        threshold: this.config.alertThresholds.memoryUsage,\n      },\n    ];\n  }\n\n  private async collectCacheMetrics(timestamp: number): Promise<PerformanceMetric[]> {\n    const cacheStats = advancedCacheManager.getStats();\n    \n    return [\n      {\n        name: 'cacheHitRate',\n        value: cacheStats.hitRate * 100,\n        unit: '%',\n        timestamp,\n        category: 'cache',\n        severity: 'info',\n        threshold: this.config.alertThresholds.cacheHitRate,\n      },\n      {\n        name: 'cacheSize',\n        value: cacheStats.memoryUsage,\n        unit: 'bytes',\n        timestamp,\n        category: 'cache',\n        severity: 'info',\n      },\n    ];\n  }\n\n  private async collectBundleMetrics(timestamp: number): Promise<PerformanceMetric[]> {\n    try {\n      const bundleReport = await bundleAnalysisService.getPerformanceReport();\n      \n      return [\n        {\n          name: 'bundleSize',\n          value: bundleReport?.bundleSize?.total ? \n            parseInt(bundleReport.bundleSize.total.replace(/[^\\d]/g, '')) * 1024 : 0,\n          unit: 'bytes',\n          timestamp,\n          category: 'bundle',\n          severity: 'info',\n          threshold: this.config.alertThresholds.bundleSize,\n        },\n        {\n          name: 'loadTime',\n          value: bundleReport?.performance?.totalLoadTime || 0,\n          unit: 'ms',\n          timestamp,\n          category: 'bundle',\n          severity: 'info',\n        },\n      ];\n    } catch (error) {\n      console.warn('Failed to collect bundle metrics:', error);\n      return [];\n    }\n  }\n\n  private storeMetrics(newMetrics: PerformanceMetric[]): void {\n    newMetrics.forEach(metric => {\n      if (!this.metrics.has(metric.name)) {\n        this.metrics.set(metric.name, []);\n      }\n      \n      const metricHistory = this.metrics.get(metric.name)!;\n      metricHistory.push(metric);\n      \n      // Limit history size\n      if (metricHistory.length > this.config.maxHistorySize) {\n        metricHistory.shift();\n      }\n    });\n  }\n\n  private checkAlerts(metrics: PerformanceMetric[]): void {\n    metrics.forEach(metric => {\n      if (!metric.threshold) return;\n\n      let severity: 'warning' | 'critical' | null = null;\n      \n      if (metric.value >= metric.threshold.critical) {\n        severity = 'critical';\n      } else if (metric.value >= metric.threshold.warning) {\n        severity = 'warning';\n      }\n\n      if (severity) {\n        const alert: PerformanceAlert = {\n          id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n          metric: metric.name,\n          severity,\n          message: `${metric.name} is ${severity}: ${metric.value}${metric.unit}`,\n          timestamp: metric.timestamp,\n          acknowledged: false,\n          suggestions: this.generateAlertSuggestions(metric, severity),\n        };\n\n        this.alerts.unshift(alert);\n        \n        // Limit alerts history\n        if (this.alerts.length > 50) {\n          this.alerts = this.alerts.slice(0, 50);\n        }\n      }\n    });\n  }\n\n  private generateAlertSuggestions(metric: PerformanceMetric, severity: string): string[] {\n    const suggestions: string[] = [];\n    \n    switch (metric.name) {\n      case 'averageRenderTime':\n        suggestions.push('Optimize component re-renders with React.memo');\n        suggestions.push('Use useCallback and useMemo for expensive operations');\n        break;\n      case 'networkLatency':\n        suggestions.push('Implement request caching');\n        suggestions.push('Use request batching for multiple API calls');\n        break;\n      case 'memoryUsage':\n        suggestions.push('Check for memory leaks in components');\n        suggestions.push('Optimize image loading and caching');\n        break;\n      case 'cacheHitRate':\n        suggestions.push('Increase cache TTL for stable data');\n        suggestions.push('Implement cache warming strategies');\n        break;\n    }\n    \n    return suggestions;\n  }\n\n  private generateReport(): PerformanceReport {\n    const timestamp = Date.now();\n    const allMetrics = Array.from(this.metrics.values()).flat();\n    const recentMetrics = allMetrics.filter(m => timestamp - m.timestamp < 60000); // Last minute\n    \n    // Calculate overall score\n    const overallScore = this.calculateOverallScore(recentMetrics);\n    \n    // Generate trends\n    const trends = {\n      renderPerformance: this.getMetricValues('averageRenderTime', 10),\n      networkLatency: this.getMetricValues('networkLatency', 10),\n      memoryUsage: this.getMetricValues('memoryUsage', 10),\n      cacheHitRate: this.getMetricValues('cacheHitRate', 10),\n    };\n\n    // Generate recommendations\n    const recommendations = this.generateRecommendations(recentMetrics);\n\n    return {\n      timestamp,\n      overallScore,\n      metrics: recentMetrics,\n      alerts: this.alerts.filter(a => !a.acknowledged).slice(0, 10),\n      trends,\n      recommendations,\n    };\n  }\n\n  private calculateOverallScore(metrics: PerformanceMetric[]): number {\n    if (metrics.length === 0) return 100;\n\n    let score = 100;\n    const weights = {\n      render: 0.3,\n      network: 0.25,\n      memory: 0.2,\n      cache: 0.15,\n      bundle: 0.1,\n    };\n\n    Object.entries(weights).forEach(([category, weight]) => {\n      const categoryMetrics = metrics.filter(m => m.category === category);\n      if (categoryMetrics.length === 0) return;\n\n      const categoryScore = categoryMetrics.reduce((sum, metric) => {\n        if (!metric.threshold) return sum + 100;\n        \n        const normalizedValue = Math.min(metric.value / metric.threshold.critical, 1);\n        return sum + (100 - normalizedValue * 100);\n      }, 0) / categoryMetrics.length;\n\n      score -= (100 - categoryScore) * weight;\n    });\n\n    return Math.max(0, Math.round(score));\n  }\n\n  private getMetricValues(metricName: string, count: number): number[] {\n    const metricHistory = this.metrics.get(metricName) || [];\n    return metricHistory.slice(-count).map(m => m.value);\n  }\n\n  private generateRecommendations(metrics: PerformanceMetric[]): any[] {\n    const recommendations: any[] = [];\n    \n    // Analyze metrics and generate recommendations\n    const slowRenders = metrics.filter(m => \n      m.name === 'averageRenderTime' && m.value > 16\n    );\n    \n    if (slowRenders.length > 0) {\n      recommendations.push({\n        priority: 'high',\n        category: 'Render Performance',\n        description: 'Optimize component rendering',\n        impact: 'Improve user experience and responsiveness',\n        implementation: 'Use React.memo, useCallback, and useMemo',\n      });\n    }\n\n    return recommendations;\n  }\n\n  private async performAutoOptimization(metrics: PerformanceMetric[]): Promise<void> {\n    // Implement automatic optimization based on metrics\n    console.log('Auto-optimization would be performed here');\n  }\n\n  private notifyListeners(report: PerformanceReport): void {\n    this.listeners.forEach(listener => {\n      try {\n        listener(report);\n      } catch (error) {\n        console.error('Performance monitoring listener error:', error);\n      }\n    });\n  }\n}\n\n// Export singleton instance\nexport const realTimePerformanceMonitor = new RealTimePerformanceMonitor();\nexport default realTimePerformanceMonitor;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,SAASA,oBAAoB;AAC7B,SAASC,qBAAqB;AAAiC,IA0DzDC,0BAA0B;EAAA,SAAAA,2BAAA;IAAAC,eAAA,OAAAD,0BAAA;IAAA,KACtBE,OAAO,IAAAC,aAAA,GAAAC,CAAA,OAAqC,IAAIC,GAAG,CAAC,CAAC;IAAA,KACrDC,MAAM,IAAAH,aAAA,GAAAC,CAAA,OAAuB,EAAE;IAAA,KAC/BG,SAAS,IAAAJ,aAAA,GAAAC,CAAA,OAA4C,EAAE;IAAA,KACvDI,kBAAkB,IAAAL,aAAA,GAAAC,CAAA,OAA0B,IAAI;IAAA,KAChDK,YAAY,IAAAN,aAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KAEpBM,MAAM,IAAAP,aAAA,GAAAC,CAAA,OAAqB;MACjCO,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,IAAI;MACdC,eAAe,EAAE;QACfC,UAAU,EAAE;UAAEC,OAAO,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC;QACzCC,cAAc,EAAE;UAAEF,OAAO,EAAE,IAAI;UAAEC,QAAQ,EAAE;QAAK,CAAC;QACjDE,WAAW,EAAE;UAAEH,OAAO,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC;QAC1CG,YAAY,EAAE;UAAEJ,OAAO,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC;QAC3CI,UAAU,EAAE;UAAEL,OAAO,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAQ;MACpD,CAAC;MACDK,YAAY,EAAE,IAAI;MAClBC,cAAc,EAAE,GAAG;MACnBC,YAAY,EAAE,IAAI;MAClBC,sBAAsB,EAAE;IAC1B,CAAC;EAAA;EAAA,OAAAC,YAAA,CAAAzB,0BAAA;IAAA0B,GAAA;IAAAC,KAAA,EAKD,SAAAC,eAAeA,CAAA,EAAS;MAAA,IAAAC,KAAA;MAAA1B,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MACtB,IAAI,IAAI,CAACK,YAAY,EAAE;QAAAN,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACrB4B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QAAC9B,aAAA,GAAAC,CAAA;QACtD;MACF,CAAC;QAAAD,aAAA,GAAA4B,CAAA;MAAA;MAAA5B,aAAA,GAAAC,CAAA;MAED,IAAI,CAACK,YAAY,GAAG,IAAI;MAACN,aAAA,GAAAC,CAAA;MACzB4B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAAC9B,aAAA,GAAAC,CAAA;MAEzD,IAAI,CAACI,kBAAkB,GAAG0B,WAAW,CAAC,YAAM;QAAA/B,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAC1CyB,KAAI,CAACM,cAAc,CAAC,CAAC;MACvB,CAAC,EAAE,IAAI,CAACzB,MAAM,CAACE,QAAQ,CAAC;MAACT,aAAA,GAAAC,CAAA;MAGzB,IAAI,CAAC+B,cAAc,CAAC,CAAC;IACvB;EAAC;IAAAT,GAAA;IAAAC,KAAA,EAKD,SAAAS,cAAcA,CAAA,EAAS;MAAAjC,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MACrB,IAAI,CAAC,IAAI,CAACK,YAAY,EAAE;QAAAN,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,aAAA,GAAA4B,CAAA;MAAA;MAAA5B,aAAA,GAAAC,CAAA;MAE/B,IAAI,CAACK,YAAY,GAAG,KAAK;MAACN,aAAA,GAAAC,CAAA;MAE1B,IAAI,IAAI,CAACI,kBAAkB,EAAE;QAAAL,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QAC3BiC,aAAa,CAAC,IAAI,CAAC7B,kBAAkB,CAAC;QAACL,aAAA,GAAAC,CAAA;QACvC,IAAI,CAACI,kBAAkB,GAAG,IAAI;MAChC,CAAC;QAAAL,aAAA,GAAA4B,CAAA;MAAA;MAAA5B,aAAA,GAAAC,CAAA;MAED4B,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACzD;EAAC;IAAAP,GAAA;IAAAC,KAAA;MAAA,IAAAW,iBAAA,GAAAC,iBAAA,CAKD,aAAqD;QAAApC,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QACnD,MAAM,IAAI,CAAC+B,cAAc,CAAC,CAAC;QAAChC,aAAA,GAAAC,CAAA;QAC5B,OAAO,IAAI,CAACoC,cAAc,CAAC,CAAC;MAC9B,CAAC;MAAA,SAHKC,gBAAgBA,CAAA;QAAA,OAAAH,iBAAA,CAAAI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAhBF,gBAAgB;IAAA;EAAA;IAAAf,GAAA;IAAAC,KAAA,EAQtB,SAAAiB,WAAWA,CAACC,QAA6C,EAAQ;MAAA1C,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MAC/D,IAAI,CAACG,SAAS,CAACuC,IAAI,CAACD,QAAQ,CAAC;IAC/B;EAAC;IAAAnB,GAAA;IAAAC,KAAA,EAKD,SAAAoB,cAAcA,CAACF,QAA6C,EAAQ;MAAA1C,aAAA,GAAA2B,CAAA;MAClE,IAAMkB,KAAK,IAAA7C,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACG,SAAS,CAAC0C,OAAO,CAACJ,QAAQ,CAAC;MAAC1C,aAAA,GAAAC,CAAA;MAC/C,IAAI4C,KAAK,GAAG,CAAC,CAAC,EAAE;QAAA7C,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACd,IAAI,CAACG,SAAS,CAAC2C,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC,CAAC;QAAA7C,aAAA,GAAA4B,CAAA;MAAA;IACH;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAAwB,gBAAgBA,CAACC,OAAe,EAAQ;MAAAjD,aAAA,GAAA2B,CAAA;MACtC,IAAMuB,KAAK,IAAAlD,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACE,MAAM,CAACgD,IAAI,CAAC,UAAAC,CAAC,EAAI;QAAApD,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAAA,OAAAmD,CAAC,CAACC,EAAE,KAAKJ,OAAO;MAAD,CAAC,CAAC;MAACjD,aAAA,GAAAC,CAAA;MACtD,IAAIiD,KAAK,EAAE;QAAAlD,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACTiD,KAAK,CAACI,YAAY,GAAG,IAAI;MAC3B,CAAC;QAAAtD,aAAA,GAAA4B,CAAA;MAAA;IACH;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAA+B,WAAWA,CAAA,EAAS;MAAAvD,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MAClB,IAAI,CAACE,MAAM,GAAG,EAAE;IAClB;EAAC;IAAAoB,GAAA;IAAAC,KAAA,EAKD,SAAAgC,YAAYA,CAACC,SAAoC,EAAQ;MAAAzD,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MACvD,IAAI,CAACM,MAAM,GAAAmD,MAAA,CAAAC,MAAA,KAAQ,IAAI,CAACpD,MAAM,EAAKkD,SAAS,CAAE;MAACzD,aAAA,GAAAC,CAAA;MAE/C,IAAI,IAAI,CAACK,YAAY,EAAE;QAAAN,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QACrB,IAAI,CAACgC,cAAc,CAAC,CAAC;QAACjC,aAAA,GAAAC,CAAA;QACtB,IAAI,CAACwB,eAAe,CAAC,CAAC;MACxB,CAAC;QAAAzB,aAAA,GAAA4B,CAAA;MAAA;IACH;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAKD,SAAAoC,oBAAoBA,CAACC,MAAc,EAKjC;MAAA,IALmCC,SAAiB,GAAAtB,SAAA,CAAAuB,MAAA,QAAAvB,SAAA,QAAAwB,SAAA,GAAAxB,SAAA,OAAAxC,aAAA,GAAA4B,CAAA,UAAG,OAAO;MAAA5B,aAAA,GAAA2B,CAAA;MAM9D,IAAMsC,aAAa,IAAAjE,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAA4B,CAAA,cAAI,CAAC7B,OAAO,CAACmE,GAAG,CAACL,MAAM,CAAC,MAAA7D,aAAA,GAAA4B,CAAA,UAAI,EAAE;MACpD,IAAMuC,UAAU,IAAAnE,aAAA,GAAAC,CAAA,QAAGmE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGP,SAAS;MAEzC,IAAMQ,aAAa,IAAAtE,aAAA,GAAAC,CAAA,QAAGgE,aAAa,CAACM,MAAM,CAAC,UAAAC,CAAC,EAAI;QAAAxE,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAAA,OAAAuE,CAAC,CAACC,SAAS,GAAGN,UAAU;MAAD,CAAC,CAAC;MACzE,IAAMO,UAAU,IAAA1E,aAAA,GAAAC,CAAA,QAAGqE,aAAa,CAACK,GAAG,CAAC,UAAAH,CAAC,EAAI;QAAAxE,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAAA,OAAAuE,CAAC,CAACC,SAAS;MAAD,CAAC,CAAC;MACtD,IAAMG,MAAM,IAAA5E,aAAA,GAAAC,CAAA,QAAGqE,aAAa,CAACK,GAAG,CAAC,UAAAH,CAAC,EAAI;QAAAxE,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAAA,OAAAuE,CAAC,CAAChD,KAAK;MAAD,CAAC,CAAC;MAE9C,IAAMqD,OAAO,IAAA7E,aAAA,GAAAC,CAAA,QAAG2E,MAAM,CAACb,MAAM,GAAG,CAAC,IAAA/D,aAAA,GAAA4B,CAAA,UAC7BgD,MAAM,CAACE,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG,EAAK;QAAAhF,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAAA,OAAA8E,GAAG,GAAGC,GAAG;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGJ,MAAM,CAACb,MAAM,KAAA/D,aAAA,GAAA4B,CAAA,UACzD,CAAC;MAGL,IAAIqD,KAA2C,IAAAjF,aAAA,GAAAC,CAAA,QAAG,QAAQ;MAACD,aAAA,GAAAC,CAAA;MAC3D,IAAI2E,MAAM,CAACb,MAAM,IAAI,CAAC,EAAE;QAAA/D,aAAA,GAAA4B,CAAA;QACtB,IAAMsD,SAAS,IAAAlF,aAAA,GAAAC,CAAA,QAAG2E,MAAM,CAACO,KAAK,CAAC,CAAC,EAAEC,IAAI,CAACC,KAAK,CAACT,MAAM,CAACb,MAAM,GAAG,CAAC,CAAC,CAAC;QAChE,IAAMuB,UAAU,IAAAtF,aAAA,GAAAC,CAAA,QAAG2E,MAAM,CAACO,KAAK,CAACC,IAAI,CAACC,KAAK,CAACT,MAAM,CAACb,MAAM,GAAG,CAAC,CAAC,CAAC;QAE9D,IAAMwB,QAAQ,IAAAvF,aAAA,GAAAC,CAAA,QAAGiF,SAAS,CAACJ,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG,EAAK;UAAAhF,aAAA,GAAA2B,CAAA;UAAA3B,aAAA,GAAAC,CAAA;UAAA,OAAA8E,GAAG,GAAGC,GAAG;QAAD,CAAC,EAAE,CAAC,CAAC,GAAGE,SAAS,CAACnB,MAAM;QAChF,IAAMyB,SAAS,IAAAxF,aAAA,GAAAC,CAAA,QAAGqF,UAAU,CAACR,MAAM,CAAC,UAACC,GAAG,EAAEC,GAAG,EAAK;UAAAhF,aAAA,GAAA2B,CAAA;UAAA3B,aAAA,GAAAC,CAAA;UAAA,OAAA8E,GAAG,GAAGC,GAAG;QAAD,CAAC,EAAE,CAAC,CAAC,GAAGM,UAAU,CAACvB,MAAM;QAEnF,IAAM0B,MAAM,IAAAzF,aAAA,GAAAC,CAAA,QAAI,CAACuF,SAAS,GAAGD,QAAQ,IAAIA,QAAQ,GAAI,GAAG;QAACvF,aAAA,GAAAC,CAAA;QAEzD,IAAIwF,MAAM,GAAG,EAAE,EAAE;UAAAzF,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAAgF,KAAK,GAAG,WAAW;QAAA,CAAC,MAChC;UAAAjF,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAA,IAAIwF,MAAM,GAAG,CAAC,EAAE,EAAE;YAAAzF,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAAgF,KAAK,GAAG,WAAW;UAAA,CAAC;YAAAjF,aAAA,GAAA4B,CAAA;UAAA;QAAD;MAC5C,CAAC;QAAA5B,aAAA,GAAA4B,CAAA;MAAA;MAAA5B,aAAA,GAAAC,CAAA;MAED,OAAO;QAAEyE,UAAU,EAAVA,UAAU;QAAEE,MAAM,EAANA,MAAM;QAAEC,OAAO,EAAPA,OAAO;QAAEI,KAAK,EAALA;MAAM,CAAC;IAC/C;EAAC;IAAA1D,GAAA;IAAAC,KAAA;MAAA,IAAAkE,sBAAA,GAAAtD,iBAAA,CAKD,aAKG;QAAApC,aAAA,GAAA2B,CAAA;QACD,IAAMgE,MAAM,IAAA3F,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACqC,gBAAgB,CAAC,CAAC;QAACtC,aAAA,GAAAC,CAAA;QAE7C,OAAO;UACL0F,MAAM,EAANA,MAAM;UACNC,UAAU,EAAElC,MAAM,CAACmC,WAAW,CAAC,IAAI,CAAC9F,OAAO,CAAC;UAC5CI,MAAM,EAAE,IAAI,CAACA,MAAM;UACnBI,MAAM,EAAE,IAAI,CAACA;QACf,CAAC;MACH,CAAC;MAAA,SAdKuF,qBAAqBA,CAAA;QAAA,OAAAJ,sBAAA,CAAAnD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBsD,qBAAqB;IAAA;EAAA;IAAAvE,GAAA;IAAAC,KAAA;MAAA,IAAAuE,eAAA,GAAA3D,iBAAA,CAkB3B,aAA8C;QAAApC,aAAA,GAAA2B,CAAA;QAC5C,IAAM8C,SAAS,IAAAzE,aAAA,GAAAC,CAAA,QAAGmE,IAAI,CAACC,GAAG,CAAC,CAAC;QAC5B,IAAM2B,UAA+B,IAAAhG,aAAA,GAAAC,CAAA,QAAG,EAAE;QAACD,aAAA,GAAAC,CAAA;QAE3C,IAAI;UAEF,IAAMgG,aAAa,IAAAjG,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACiG,oBAAoB,CAACzB,SAAS,CAAC;UAACzE,aAAA,GAAAC,CAAA;UAC3D+F,UAAU,CAACrD,IAAI,CAAAJ,KAAA,CAAfyD,UAAU,EAAAG,kBAAA,CAASF,aAAa,EAAC;UAGjC,IAAMG,cAAc,IAAApG,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACoG,qBAAqB,CAAC5B,SAAS,CAAC;UAACzE,aAAA,GAAAC,CAAA;UAC7D+F,UAAU,CAACrD,IAAI,CAAAJ,KAAA,CAAfyD,UAAU,EAAAG,kBAAA,CAASC,cAAc,EAAC;UAGlC,IAAME,aAAa,IAAAtG,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACsG,oBAAoB,CAAC9B,SAAS,CAAC;UAACzE,aAAA,GAAAC,CAAA;UAC3D+F,UAAU,CAACrD,IAAI,CAAAJ,KAAA,CAAfyD,UAAU,EAAAG,kBAAA,CAASG,aAAa,EAAC;UAGjC,IAAME,YAAY,IAAAxG,aAAA,GAAAC,CAAA,cAAS,IAAI,CAACwG,mBAAmB,CAAChC,SAAS,CAAC;UAACzE,aAAA,GAAAC,CAAA;UAC/D+F,UAAU,CAACrD,IAAI,CAAAJ,KAAA,CAAfyD,UAAU,EAAAG,kBAAA,CAASK,YAAY,EAAC;UAGhC,IAAME,aAAa,IAAA1G,aAAA,GAAAC,CAAA,cAAS,IAAI,CAAC0G,oBAAoB,CAAClC,SAAS,CAAC;UAACzE,aAAA,GAAAC,CAAA;UACjE+F,UAAU,CAACrD,IAAI,CAAAJ,KAAA,CAAfyD,UAAU,EAAAG,kBAAA,CAASO,aAAa,EAAC;UAAC1G,aAAA,GAAAC,CAAA;UAGlC,IAAI,CAAC2G,YAAY,CAACZ,UAAU,CAAC;UAAChG,aAAA,GAAAC,CAAA;UAG9B,IAAI,IAAI,CAACM,MAAM,CAACa,YAAY,EAAE;YAAApB,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAC5B,IAAI,CAAC4G,WAAW,CAACb,UAAU,CAAC;UAC9B,CAAC;YAAAhG,aAAA,GAAA4B,CAAA;UAAA;UAGD,IAAM+D,MAAM,IAAA3F,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACoC,cAAc,CAAC,CAAC;UAACrC,aAAA,GAAAC,CAAA;UACrC,IAAI,CAAC6G,eAAe,CAACnB,MAAM,CAAC;UAAC3F,aAAA,GAAAC,CAAA;UAG7B,IAAI,IAAI,CAACM,MAAM,CAACc,sBAAsB,EAAE;YAAArB,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YACtC,MAAM,IAAI,CAAC8G,uBAAuB,CAACf,UAAU,CAAC;UAChD,CAAC;YAAAhG,aAAA,GAAA4B,CAAA;UAAA;QAEH,CAAC,CAAC,OAAOoF,KAAK,EAAE;UAAAhH,aAAA,GAAAC,CAAA;UACd4B,OAAO,CAACmF,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAChE;MACF,CAAC;MAAA,SA7CahF,cAAcA,CAAA;QAAA,OAAA+D,eAAA,CAAAxD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdR,cAAc;IAAA;EAAA;IAAAT,GAAA;IAAAC,KAAA,EA+C5B,SAAQ0E,oBAAoBA,CAACzB,SAAiB,EAAuB;MAAAzE,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MAEnE,OAAO,CACL;QACEgH,IAAI,EAAE,mBAAmB;QACzBzF,KAAK,EAAE4D,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;QAC7BC,IAAI,EAAE,IAAI;QACV1C,SAAS,EAATA,SAAS;QACT2C,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,IAAI,CAAC/G,MAAM,CAACG,eAAe,CAACC;MACzC,CAAC,EACD;QACEsG,IAAI,EAAE,YAAY;QAClBzF,KAAK,EAAE4D,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;QACpCC,IAAI,EAAE,OAAO;QACb1C,SAAS,EAATA,SAAS;QACT2C,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE;MACZ,CAAC,CACF;IACH;EAAC;IAAA9F,GAAA;IAAAC,KAAA,EAED,SAAQ6E,qBAAqBA,CAAC5B,SAAiB,EAAuB;MAAAzE,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MACpE,OAAO,CACL;QACEgH,IAAI,EAAE,gBAAgB;QACtBzF,KAAK,EAAE4D,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG;QACjCC,IAAI,EAAE,IAAI;QACV1C,SAAS,EAATA,SAAS;QACT2C,QAAQ,EAAE,SAAS;QACnBC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,IAAI,CAAC/G,MAAM,CAACG,eAAe,CAACI;MACzC,CAAC,EACD;QACEmG,IAAI,EAAE,mBAAmB;QACzBzF,KAAK,EAAE4D,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;QAC7BC,IAAI,EAAE,OAAO;QACb1C,SAAS,EAATA,SAAS;QACT2C,QAAQ,EAAE,SAAS;QACnBC,QAAQ,EAAE;MACZ,CAAC,CACF;IACH;EAAC;IAAA9F,GAAA;IAAAC,KAAA,EAED,SAAQ+E,oBAAoBA,CAAC9B,SAAiB,EAAuB;MAAAzE,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MACnE,OAAO,CACL;QACEgH,IAAI,EAAE,aAAa;QACnBzF,KAAK,EAAE4D,IAAI,CAAC8B,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;QAC9BC,IAAI,EAAE,GAAG;QACT1C,SAAS,EAATA,SAAS;QACT2C,QAAQ,EAAE,QAAQ;QAClBC,QAAQ,EAAE,MAAM;QAChBC,SAAS,EAAE,IAAI,CAAC/G,MAAM,CAACG,eAAe,CAACK;MACzC,CAAC,CACF;IACH;EAAC;IAAAQ,GAAA;IAAAC,KAAA;MAAA,IAAA+F,oBAAA,GAAAnF,iBAAA,CAED,WAAkCqC,SAAiB,EAAgC;QAAAzE,aAAA,GAAA2B,CAAA;QACjF,IAAM6F,UAAU,IAAAxH,aAAA,GAAAC,CAAA,QAAGN,oBAAoB,CAAC8H,QAAQ,CAAC,CAAC;QAACzH,aAAA,GAAAC,CAAA;QAEnD,OAAO,CACL;UACEgH,IAAI,EAAE,cAAc;UACpBzF,KAAK,EAAEgG,UAAU,CAACE,OAAO,GAAG,GAAG;UAC/BP,IAAI,EAAE,GAAG;UACT1C,SAAS,EAATA,SAAS;UACT2C,QAAQ,EAAE,OAAO;UACjBC,QAAQ,EAAE,MAAM;UAChBC,SAAS,EAAE,IAAI,CAAC/G,MAAM,CAACG,eAAe,CAACM;QACzC,CAAC,EACD;UACEiG,IAAI,EAAE,WAAW;UACjBzF,KAAK,EAAEgG,UAAU,CAACzG,WAAW;UAC7BoG,IAAI,EAAE,OAAO;UACb1C,SAAS,EAATA,SAAS;UACT2C,QAAQ,EAAE,OAAO;UACjBC,QAAQ,EAAE;QACZ,CAAC,CACF;MACH,CAAC;MAAA,SAtBaZ,mBAAmBA,CAAAkB,EAAA;QAAA,OAAAJ,oBAAA,CAAAhF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBiE,mBAAmB;IAAA;EAAA;IAAAlF,GAAA;IAAAC,KAAA;MAAA,IAAAoG,qBAAA,GAAAxF,iBAAA,CAwBjC,WAAmCqC,SAAiB,EAAgC;QAAAzE,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAClF,IAAI;UAAA,IAAA4H,qBAAA,EAAAC,qBAAA;UACF,IAAMC,YAAY,IAAA/H,aAAA,GAAAC,CAAA,cAASL,qBAAqB,CAACoI,oBAAoB,CAAC,CAAC;UAAChI,aAAA,GAAAC,CAAA;UAExE,OAAO,CACL;YACEgH,IAAI,EAAE,YAAY;YAClBzF,KAAK,EAAEuG,YAAY,aAAAF,qBAAA,GAAZE,YAAY,CAAE9G,UAAU,aAAxB4G,qBAAA,CAA0BI,KAAK,IAAAjI,aAAA,GAAA4B,CAAA,WACpCsG,QAAQ,CAACH,YAAY,CAAC9G,UAAU,CAACgH,KAAK,CAACE,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC,GAAG,IAAI,KAAAnI,aAAA,GAAA4B,CAAA,WAAG,CAAC;YAC1EuF,IAAI,EAAE,OAAO;YACb1C,SAAS,EAATA,SAAS;YACT2C,QAAQ,EAAE,QAAQ;YAClBC,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,IAAI,CAAC/G,MAAM,CAACG,eAAe,CAACO;UACzC,CAAC,EACD;YACEgG,IAAI,EAAE,UAAU;YAChBzF,KAAK,EAAE,CAAAxB,aAAA,GAAA4B,CAAA,WAAAmG,YAAY,aAAAD,qBAAA,GAAZC,YAAY,CAAEK,WAAW,qBAAzBN,qBAAA,CAA2BO,aAAa,MAAArI,aAAA,GAAA4B,CAAA,WAAI,CAAC;YACpDuF,IAAI,EAAE,IAAI;YACV1C,SAAS,EAATA,SAAS;YACT2C,QAAQ,EAAE,QAAQ;YAClBC,QAAQ,EAAE;UACZ,CAAC,CACF;QACH,CAAC,CAAC,OAAOL,KAAK,EAAE;UAAAhH,aAAA,GAAAC,CAAA;UACd4B,OAAO,CAACyG,IAAI,CAAC,mCAAmC,EAAEtB,KAAK,CAAC;UAAChH,aAAA,GAAAC,CAAA;UACzD,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SA5Ba0G,oBAAoBA,CAAA4B,GAAA;QAAA,OAAAX,qBAAA,CAAArF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBmE,oBAAoB;IAAA;EAAA;IAAApF,GAAA;IAAAC,KAAA,EA8BlC,SAAQoF,YAAYA,CAACZ,UAA+B,EAAQ;MAAA,IAAAwC,MAAA;MAAAxI,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MAC1D+F,UAAU,CAACyC,OAAO,CAAC,UAAA5E,MAAM,EAAI;QAAA7D,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAC3B,IAAI,CAACuI,MAAI,CAACzI,OAAO,CAAC2I,GAAG,CAAC7E,MAAM,CAACoD,IAAI,CAAC,EAAE;UAAAjH,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAClCuI,MAAI,CAACzI,OAAO,CAAC4I,GAAG,CAAC9E,MAAM,CAACoD,IAAI,EAAE,EAAE,CAAC;QACnC,CAAC;UAAAjH,aAAA,GAAA4B,CAAA;QAAA;QAED,IAAMqC,aAAa,IAAAjE,aAAA,GAAAC,CAAA,QAAGuI,MAAI,CAACzI,OAAO,CAACmE,GAAG,CAACL,MAAM,CAACoD,IAAI,CAAC,CAAC;QAACjH,aAAA,GAAAC,CAAA;QACrDgE,aAAa,CAACtB,IAAI,CAACkB,MAAM,CAAC;QAAC7D,aAAA,GAAAC,CAAA;QAG3B,IAAIgE,aAAa,CAACF,MAAM,GAAGyE,MAAI,CAACjI,MAAM,CAACY,cAAc,EAAE;UAAAnB,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UACrDgE,aAAa,CAAC2E,KAAK,CAAC,CAAC;QACvB,CAAC;UAAA5I,aAAA,GAAA4B,CAAA;QAAA;MACH,CAAC,CAAC;IACJ;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAQqF,WAAWA,CAAC9G,OAA4B,EAAQ;MAAA,IAAA8I,MAAA;MAAA7I,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MACtDF,OAAO,CAAC0I,OAAO,CAAC,UAAA5E,MAAM,EAAI;QAAA7D,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QACxB,IAAI,CAAC4D,MAAM,CAACyD,SAAS,EAAE;UAAAtH,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAA;QAAM,CAAC;UAAAD,aAAA,GAAA4B,CAAA;QAAA;QAE9B,IAAIyF,QAAuC,IAAArH,aAAA,GAAAC,CAAA,SAAG,IAAI;QAACD,aAAA,GAAAC,CAAA;QAEnD,IAAI4D,MAAM,CAACrC,KAAK,IAAIqC,MAAM,CAACyD,SAAS,CAACzG,QAAQ,EAAE;UAAAb,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAC7CoH,QAAQ,GAAG,UAAU;QACvB,CAAC,MAAM;UAAArH,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAA,IAAI4D,MAAM,CAACrC,KAAK,IAAIqC,MAAM,CAACyD,SAAS,CAAC1G,OAAO,EAAE;YAAAZ,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YACnDoH,QAAQ,GAAG,SAAS;UACtB,CAAC;YAAArH,aAAA,GAAA4B,CAAA;UAAA;QAAD;QAAC5B,aAAA,GAAAC,CAAA;QAED,IAAIoH,QAAQ,EAAE;UAAArH,aAAA,GAAA4B,CAAA;UACZ,IAAMsB,KAAuB,IAAAlD,aAAA,GAAAC,CAAA,SAAG;YAC9BoD,EAAE,EAAE,SAASe,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIe,IAAI,CAAC8B,MAAM,CAAC,CAAC,CAAC4B,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACpElF,MAAM,EAAEA,MAAM,CAACoD,IAAI;YACnBI,QAAQ,EAARA,QAAQ;YACR2B,OAAO,EAAE,GAAGnF,MAAM,CAACoD,IAAI,OAAOI,QAAQ,KAAKxD,MAAM,CAACrC,KAAK,GAAGqC,MAAM,CAACsD,IAAI,EAAE;YACvE1C,SAAS,EAAEZ,MAAM,CAACY,SAAS;YAC3BnB,YAAY,EAAE,KAAK;YACnB2F,WAAW,EAAEJ,MAAI,CAACK,wBAAwB,CAACrF,MAAM,EAAEwD,QAAQ;UAC7D,CAAC;UAACrH,aAAA,GAAAC,CAAA;UAEF4I,MAAI,CAAC1I,MAAM,CAACgJ,OAAO,CAACjG,KAAK,CAAC;UAAClD,aAAA,GAAAC,CAAA;UAG3B,IAAI4I,MAAI,CAAC1I,MAAM,CAAC4D,MAAM,GAAG,EAAE,EAAE;YAAA/D,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAC3B4I,MAAI,CAAC1I,MAAM,GAAG0I,MAAI,CAAC1I,MAAM,CAACgF,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;UACxC,CAAC;YAAAnF,aAAA,GAAA4B,CAAA;UAAA;QACH,CAAC;UAAA5B,aAAA,GAAA4B,CAAA;QAAA;MACH,CAAC,CAAC;IACJ;EAAC;IAAAL,GAAA;IAAAC,KAAA,EAED,SAAQ0H,wBAAwBA,CAACrF,MAAyB,EAAEwD,QAAgB,EAAY;MAAArH,aAAA,GAAA2B,CAAA;MACtF,IAAMsH,WAAqB,IAAAjJ,aAAA,GAAAC,CAAA,SAAG,EAAE;MAACD,aAAA,GAAAC,CAAA;MAEjC,QAAQ4D,MAAM,CAACoD,IAAI;QACjB,KAAK,mBAAmB;UAAAjH,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UACtBgJ,WAAW,CAACtG,IAAI,CAAC,+CAA+C,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UAClEgJ,WAAW,CAACtG,IAAI,CAAC,sDAAsD,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UACzE;QACF,KAAK,gBAAgB;UAAAD,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UACnBgJ,WAAW,CAACtG,IAAI,CAAC,2BAA2B,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UAC9CgJ,WAAW,CAACtG,IAAI,CAAC,6CAA6C,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UAChE;QACF,KAAK,aAAa;UAAAD,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAChBgJ,WAAW,CAACtG,IAAI,CAAC,sCAAsC,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UACzDgJ,WAAW,CAACtG,IAAI,CAAC,oCAAoC,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UACvD;QACF,KAAK,cAAc;UAAAD,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UACjBgJ,WAAW,CAACtG,IAAI,CAAC,oCAAoC,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UACvDgJ,WAAW,CAACtG,IAAI,CAAC,oCAAoC,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UACvD;MACJ;MAACD,aAAA,GAAAC,CAAA;MAED,OAAOgJ,WAAW;IACpB;EAAC;IAAA1H,GAAA;IAAAC,KAAA,EAED,SAAQa,cAAcA,CAAA,EAAsB;MAAArC,aAAA,GAAA2B,CAAA;MAC1C,IAAM8C,SAAS,IAAAzE,aAAA,GAAAC,CAAA,SAAGmE,IAAI,CAACC,GAAG,CAAC,CAAC;MAC5B,IAAM+E,UAAU,IAAApJ,aAAA,GAAAC,CAAA,SAAGoJ,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvJ,OAAO,CAAC6E,MAAM,CAAC,CAAC,CAAC,CAAC2E,IAAI,CAAC,CAAC;MAC3D,IAAMjF,aAAa,IAAAtE,aAAA,GAAAC,CAAA,SAAGmJ,UAAU,CAAC7E,MAAM,CAAC,UAAAC,CAAC,EAAI;QAAAxE,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAAA,OAAAwE,SAAS,GAAGD,CAAC,CAACC,SAAS,GAAG,KAAK;MAAD,CAAC,CAAC;MAG7E,IAAM+E,YAAY,IAAAxJ,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACwJ,qBAAqB,CAACnF,aAAa,CAAC;MAG9D,IAAMoF,MAAM,IAAA1J,aAAA,GAAAC,CAAA,SAAG;QACb0J,iBAAiB,EAAE,IAAI,CAACC,eAAe,CAAC,mBAAmB,EAAE,EAAE,CAAC;QAChE9I,cAAc,EAAE,IAAI,CAAC8I,eAAe,CAAC,gBAAgB,EAAE,EAAE,CAAC;QAC1D7I,WAAW,EAAE,IAAI,CAAC6I,eAAe,CAAC,aAAa,EAAE,EAAE,CAAC;QACpD5I,YAAY,EAAE,IAAI,CAAC4I,eAAe,CAAC,cAAc,EAAE,EAAE;MACvD,CAAC;MAGD,IAAMC,eAAe,IAAA7J,aAAA,GAAAC,CAAA,SAAG,IAAI,CAAC6J,uBAAuB,CAACxF,aAAa,CAAC;MAACtE,aAAA,GAAAC,CAAA;MAEpE,OAAO;QACLwE,SAAS,EAATA,SAAS;QACT+E,YAAY,EAAZA,YAAY;QACZzJ,OAAO,EAAEuE,aAAa;QACtBnE,MAAM,EAAE,IAAI,CAACA,MAAM,CAACoE,MAAM,CAAC,UAAAnB,CAAC,EAAI;UAAApD,aAAA,GAAA2B,CAAA;UAAA3B,aAAA,GAAAC,CAAA;UAAA,QAACmD,CAAC,CAACE,YAAY;QAAD,CAAC,CAAC,CAAC6B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;QAC7DuE,MAAM,EAANA,MAAM;QACNG,eAAe,EAAfA;MACF,CAAC;IACH;EAAC;IAAAtI,GAAA;IAAAC,KAAA,EAED,SAAQiI,qBAAqBA,CAAC1J,OAA4B,EAAU;MAAAC,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MAClE,IAAIF,OAAO,CAACgE,MAAM,KAAK,CAAC,EAAE;QAAA/D,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QAAA,OAAO,GAAG;MAAA,CAAC;QAAAD,aAAA,GAAA4B,CAAA;MAAA;MAErC,IAAImI,KAAK,IAAA/J,aAAA,GAAAC,CAAA,SAAG,GAAG;MACf,IAAM+J,OAAO,IAAAhK,aAAA,GAAAC,CAAA,SAAG;QACdgK,MAAM,EAAE,GAAG;QACXC,OAAO,EAAE,IAAI;QACbC,MAAM,EAAE,GAAG;QACXC,KAAK,EAAE,IAAI;QACXC,MAAM,EAAE;MACV,CAAC;MAACrK,aAAA,GAAAC,CAAA;MAEFyD,MAAM,CAAC4G,OAAO,CAACN,OAAO,CAAC,CAACvB,OAAO,CAAC,UAAA8B,IAAA,EAAwB;QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA;UAAtBnD,QAAQ,GAAAoD,KAAA;UAAEE,MAAM,GAAAF,KAAA;QAAAxK,aAAA,GAAA2B,CAAA;QAChD,IAAMgJ,eAAe,IAAA3K,aAAA,GAAAC,CAAA,SAAGF,OAAO,CAACwE,MAAM,CAAC,UAAAC,CAAC,EAAI;UAAAxE,aAAA,GAAA2B,CAAA;UAAA3B,aAAA,GAAAC,CAAA;UAAA,OAAAuE,CAAC,CAAC4C,QAAQ,KAAKA,QAAQ;QAAD,CAAC,CAAC;QAACpH,aAAA,GAAAC,CAAA;QACrE,IAAI0K,eAAe,CAAC5G,MAAM,KAAK,CAAC,EAAE;UAAA/D,aAAA,GAAA4B,CAAA;UAAA5B,aAAA,GAAAC,CAAA;UAAA;QAAM,CAAC;UAAAD,aAAA,GAAA4B,CAAA;QAAA;QAEzC,IAAMgJ,aAAa,IAAA5K,aAAA,GAAAC,CAAA,SAAG0K,eAAe,CAAC7F,MAAM,CAAC,UAACC,GAAG,EAAElB,MAAM,EAAK;UAAA7D,aAAA,GAAA2B,CAAA;UAAA3B,aAAA,GAAAC,CAAA;UAC5D,IAAI,CAAC4D,MAAM,CAACyD,SAAS,EAAE;YAAAtH,aAAA,GAAA4B,CAAA;YAAA5B,aAAA,GAAAC,CAAA;YAAA,OAAO8E,GAAG,GAAG,GAAG;UAAA,CAAC;YAAA/E,aAAA,GAAA4B,CAAA;UAAA;UAExC,IAAMiJ,eAAe,IAAA7K,aAAA,GAAAC,CAAA,SAAGmF,IAAI,CAAC0F,GAAG,CAACjH,MAAM,CAACrC,KAAK,GAAGqC,MAAM,CAACyD,SAAS,CAACzG,QAAQ,EAAE,CAAC,CAAC;UAACb,aAAA,GAAAC,CAAA;UAC9E,OAAO8E,GAAG,IAAI,GAAG,GAAG8F,eAAe,GAAG,GAAG,CAAC;QAC5C,CAAC,EAAE,CAAC,CAAC,GAAGF,eAAe,CAAC5G,MAAM;QAAC/D,aAAA,GAAAC,CAAA;QAE/B8J,KAAK,IAAI,CAAC,GAAG,GAAGa,aAAa,IAAIF,MAAM;MACzC,CAAC,CAAC;MAAC1K,aAAA,GAAAC,CAAA;MAEH,OAAOmF,IAAI,CAAC2F,GAAG,CAAC,CAAC,EAAE3F,IAAI,CAAC4F,KAAK,CAACjB,KAAK,CAAC,CAAC;IACvC;EAAC;IAAAxI,GAAA;IAAAC,KAAA,EAED,SAAQoI,eAAeA,CAACqB,UAAkB,EAAEC,KAAa,EAAY;MAAAlL,aAAA,GAAA2B,CAAA;MACnE,IAAMsC,aAAa,IAAAjE,aAAA,GAAAC,CAAA,SAAG,CAAAD,aAAA,GAAA4B,CAAA,eAAI,CAAC7B,OAAO,CAACmE,GAAG,CAAC+G,UAAU,CAAC,MAAAjL,aAAA,GAAA4B,CAAA,WAAI,EAAE;MAAC5B,aAAA,GAAAC,CAAA;MACzD,OAAOgE,aAAa,CAACkB,KAAK,CAAC,CAAC+F,KAAK,CAAC,CAACvG,GAAG,CAAC,UAAAH,CAAC,EAAI;QAAAxE,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAAA,OAAAuE,CAAC,CAAChD,KAAK;MAAD,CAAC,CAAC;IACtD;EAAC;IAAAD,GAAA;IAAAC,KAAA,EAED,SAAQsI,uBAAuBA,CAAC/J,OAA4B,EAAS;MAAAC,aAAA,GAAA2B,CAAA;MACnE,IAAMkI,eAAsB,IAAA7J,aAAA,GAAAC,CAAA,SAAG,EAAE;MAGjC,IAAMkL,WAAW,IAAAnL,aAAA,GAAAC,CAAA,SAAGF,OAAO,CAACwE,MAAM,CAAC,UAAAC,CAAC,EAClC;QAAAxE,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAAA,QAAAD,aAAA,GAAA4B,CAAA,WAAA4C,CAAC,CAACyC,IAAI,KAAK,mBAAmB,MAAAjH,aAAA,GAAA4B,CAAA,WAAI4C,CAAC,CAAChD,KAAK,GAAG,EAAE;MAAD,CAC/C,CAAC;MAACxB,aAAA,GAAAC,CAAA;MAEF,IAAIkL,WAAW,CAACpH,MAAM,GAAG,CAAC,EAAE;QAAA/D,aAAA,GAAA4B,CAAA;QAAA5B,aAAA,GAAAC,CAAA;QAC1B4J,eAAe,CAAClH,IAAI,CAAC;UACnByI,QAAQ,EAAE,MAAM;UAChBhE,QAAQ,EAAE,oBAAoB;UAC9BiE,WAAW,EAAE,8BAA8B;UAC3CC,MAAM,EAAE,4CAA4C;UACpDC,cAAc,EAAE;QAClB,CAAC,CAAC;MACJ,CAAC;QAAAvL,aAAA,GAAA4B,CAAA;MAAA;MAAA5B,aAAA,GAAAC,CAAA;MAED,OAAO4J,eAAe;IACxB;EAAC;IAAAtI,GAAA;IAAAC,KAAA;MAAA,IAAAgK,wBAAA,GAAApJ,iBAAA,CAED,WAAsCrC,OAA4B,EAAiB;QAAAC,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QAEjF4B,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MAC1D,CAAC;MAAA,SAHaiF,uBAAuBA,CAAA0E,GAAA;QAAA,OAAAD,wBAAA,CAAAjJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAvBuE,uBAAuB;IAAA;EAAA;IAAAxF,GAAA;IAAAC,KAAA,EAKrC,SAAQsF,eAAeA,CAACnB,MAAyB,EAAQ;MAAA3F,aAAA,GAAA2B,CAAA;MAAA3B,aAAA,GAAAC,CAAA;MACvD,IAAI,CAACG,SAAS,CAACqI,OAAO,CAAC,UAAA/F,QAAQ,EAAI;QAAA1C,aAAA,GAAA2B,CAAA;QAAA3B,aAAA,GAAAC,CAAA;QACjC,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFyC,QAAQ,CAACiD,MAAM,CAAC;QAClB,CAAC,CAAC,OAAOqB,KAAK,EAAE;UAAAhH,aAAA,GAAAC,CAAA;UACd4B,OAAO,CAACmF,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAChE;MACF,CAAC,CAAC;IACJ;EAAC;AAAA;AAIH,OAAO,IAAM0E,0BAA0B,IAAA1L,aAAA,GAAAC,CAAA,SAAG,IAAIJ,0BAA0B,CAAC,CAAC;AAC1E,eAAe6L,0BAA0B", "ignoreList": []}