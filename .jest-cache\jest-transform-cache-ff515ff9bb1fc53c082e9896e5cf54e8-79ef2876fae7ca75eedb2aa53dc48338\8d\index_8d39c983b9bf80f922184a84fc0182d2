f2e0bd9e57810904fad3173d54c275b5
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["color", "indeterminate", "progress", "trackColor", "style"];
var ProgressBar = React.forwardRef(function (props, ref) {
  var _props$color = props.color,
    color = _props$color === void 0 ? '#1976D2' : _props$color,
    _props$indeterminate = props.indeterminate,
    indeterminate = _props$indeterminate === void 0 ? false : _props$indeterminate,
    _props$progress = props.progress,
    progress = _props$progress === void 0 ? 0 : _props$progress,
    _props$trackColor = props.trackColor,
    trackColor = _props$trackColor === void 0 ? 'transparent' : _props$trackColor,
    style = props.style,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var percentageProgress = progress * 100;
  var width = indeterminate ? '25%' : percentageProgress + "%";
  return React.createElement(_View.default, (0, _extends2.default)({}, other, {
    "aria-valuemax": 100,
    "aria-valuemin": 0,
    "aria-valuenow": indeterminate ? null : percentageProgress,
    ref: ref,
    role: "progressbar",
    style: [styles.track, style, {
      backgroundColor: trackColor
    }]
  }), React.createElement(_View.default, {
    style: [{
      backgroundColor: color,
      width: width
    }, styles.progress, indeterminate && styles.animation]
  }));
});
ProgressBar.displayName = 'ProgressBar';
var styles = _StyleSheet.default.create({
  track: {
    forcedColorAdjust: 'none',
    height: 5,
    overflow: 'hidden',
    userSelect: 'none',
    zIndex: 0
  },
  progress: {
    forcedColorAdjust: 'none',
    height: '100%',
    zIndex: -1
  },
  animation: {
    animationDuration: '1s',
    animationKeyframes: [{
      '0%': {
        transform: 'translateX(-100%)'
      },
      '100%': {
        transform: 'translateX(400%)'
      }
    }],
    animationTimingFunction: 'linear',
    animationIterationCount: 'infinite'
  }
});
var _default = exports.default = ProgressBar;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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