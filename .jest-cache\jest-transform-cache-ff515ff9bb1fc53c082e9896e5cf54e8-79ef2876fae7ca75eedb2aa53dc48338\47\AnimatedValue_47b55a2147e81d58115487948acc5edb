a9feab4eccbb58debcf9d31ca1741b2b
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var _InteractionManager = _interopRequireDefault(require("../../../../exports/InteractionManager"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var NativeAnimatedAPI = _NativeAnimatedHelper.default.API;
function _flush(rootNode) {
  var animatedStyles = new Set();
  function findAnimatedStyles(node) {
    if (typeof node.update === 'function') {
      animatedStyles.add(node);
    } else {
      node.__getChildren().forEach(findAnimatedStyles);
    }
  }
  findAnimatedStyles(rootNode);
  animatedStyles.forEach(function (animatedStyle) {
    return animatedStyle.update();
  });
}
function _executeAsAnimatedBatch(id, operation) {
  NativeAnimatedAPI.setWaitingForIdentifier(id);
  operation();
  NativeAnimatedAPI.unsetWaitingForIdentifier(id);
}
var AnimatedValue = function (_AnimatedWithChildren2) {
  function AnimatedValue(value, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedValue);
    _this = _callSuper(this, AnimatedValue);
    if (typeof value !== 'number') {
      throw new Error('AnimatedValue: Attempting to set value to undefined');
    }
    _this._startingValue = _this._value = value;
    _this._offset = 0;
    _this._animation = null;
    if (config && config.useNativeDriver) {
      _this.__makeNative();
    }
    return _this;
  }
  (0, _inherits2.default)(AnimatedValue, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedValue, [{
    key: "__detach",
    value: function __detach() {
      var _this2 = this;
      if (this.__isNative) {
        NativeAnimatedAPI.getValue(this.__getNativeTag(), function (value) {
          _this2._value = value - _this2._offset;
        });
      }
      this.stopAnimation();
      _superPropGet(AnimatedValue, "__detach", this, 3)([]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return this._value + this._offset;
    }
  }, {
    key: "setValue",
    value: function setValue(value) {
      var _this3 = this;
      if (this._animation) {
        this._animation.stop();
        this._animation = null;
      }
      this._updateValue(value, !this.__isNative);
      if (this.__isNative) {
        _executeAsAnimatedBatch(this.__getNativeTag().toString(), function () {
          return NativeAnimatedAPI.setAnimatedNodeValue(_this3.__getNativeTag(), value);
        });
      }
    }
  }, {
    key: "setOffset",
    value: function setOffset(offset) {
      this._offset = offset;
      if (this.__isNative) {
        NativeAnimatedAPI.setAnimatedNodeOffset(this.__getNativeTag(), offset);
      }
    }
  }, {
    key: "flattenOffset",
    value: function flattenOffset() {
      this._value += this._offset;
      this._offset = 0;
      if (this.__isNative) {
        NativeAnimatedAPI.flattenAnimatedNodeOffset(this.__getNativeTag());
      }
    }
  }, {
    key: "extractOffset",
    value: function extractOffset() {
      this._offset += this._value;
      this._value = 0;
      if (this.__isNative) {
        NativeAnimatedAPI.extractAnimatedNodeOffset(this.__getNativeTag());
      }
    }
  }, {
    key: "stopAnimation",
    value: function stopAnimation(callback) {
      this.stopTracking();
      this._animation && this._animation.stop();
      this._animation = null;
      if (callback) {
        if (this.__isNative) {
          NativeAnimatedAPI.getValue(this.__getNativeTag(), callback);
        } else {
          callback(this.__getValue());
        }
      }
    }
  }, {
    key: "resetAnimation",
    value: function resetAnimation(callback) {
      this.stopAnimation(callback);
      this._value = this._startingValue;
      if (this.__isNative) {
        NativeAnimatedAPI.setAnimatedNodeValue(this.__getNativeTag(), this._startingValue);
      }
    }
  }, {
    key: "__onAnimatedValueUpdateReceived",
    value: function __onAnimatedValueUpdateReceived(value) {
      this._updateValue(value, false);
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "animate",
    value: function animate(animation, callback) {
      var _this4 = this;
      var handle = null;
      if (animation.__isInteraction) {
        handle = _InteractionManager.default.createInteractionHandle();
      }
      var previousAnimation = this._animation;
      this._animation && this._animation.stop();
      this._animation = animation;
      animation.start(this._value, function (value) {
        _this4._updateValue(value, true);
      }, function (result) {
        _this4._animation = null;
        if (handle !== null) {
          _InteractionManager.default.clearInteractionHandle(handle);
        }
        callback && callback(result);
      }, previousAnimation, this);
    }
  }, {
    key: "stopTracking",
    value: function stopTracking() {
      this._tracking && this._tracking.__detach();
      this._tracking = null;
    }
  }, {
    key: "track",
    value: function track(tracking) {
      this.stopTracking();
      this._tracking = tracking;
      this._tracking && this._tracking.update();
    }
  }, {
    key: "_updateValue",
    value: function _updateValue(value, flush) {
      if (value === undefined) {
        throw new Error('AnimatedValue: Attempting to set value to undefined');
      }
      this._value = value;
      if (flush) {
        _flush(this);
      }
      _superPropGet(AnimatedValue, "__callListeners", this, 3)([this.__getValue()]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'value',
        value: this._value,
        offset: this._offset
      };
    }
  }]);
}(_AnimatedWithChildren.default);
var _default = exports.default = AnimatedValue;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0MiIsInJlcXVpcmUiLCJfY2xhc3NDYWxsQ2hlY2syIiwiX2NyZWF0ZUNsYXNzMiIsIl9wb3NzaWJsZUNvbnN0cnVjdG9yUmV0dXJuMiIsIl9nZXRQcm90b3R5cGVPZjIiLCJfZ2V0MiIsIl9pbmhlcml0czIiLCJfY2FsbFN1cGVyIiwidCIsIm8iLCJlIiwiZGVmYXVsdCIsIl9pc05hdGl2ZVJlZmxlY3RDb25zdHJ1Y3QiLCJSZWZsZWN0IiwiY29uc3RydWN0IiwiY29uc3RydWN0b3IiLCJhcHBseSIsIkJvb2xlYW4iLCJwcm90b3R5cGUiLCJ2YWx1ZU9mIiwiY2FsbCIsIl9zdXBlclByb3BHZXQiLCJyIiwicCIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJleHBvcnRzIiwiX19lc01vZHVsZSIsIl9BbmltYXRlZEludGVycG9sYXRpb24iLCJfQW5pbWF0ZWRXaXRoQ2hpbGRyZW4iLCJfSW50ZXJhY3Rpb25NYW5hZ2VyIiwiX05hdGl2ZUFuaW1hdGVkSGVscGVyIiwiTmF0aXZlQW5pbWF0ZWRBUEkiLCJBUEkiLCJfZmx1c2giLCJyb290Tm9kZSIsImFuaW1hdGVkU3R5bGVzIiwiU2V0IiwiZmluZEFuaW1hdGVkU3R5bGVzIiwibm9kZSIsInVwZGF0ZSIsImFkZCIsIl9fZ2V0Q2hpbGRyZW4iLCJmb3JFYWNoIiwiYW5pbWF0ZWRTdHlsZSIsIl9leGVjdXRlQXNBbmltYXRlZEJhdGNoIiwiaWQiLCJvcGVyYXRpb24iLCJzZXRXYWl0aW5nRm9ySWRlbnRpZmllciIsInVuc2V0V2FpdGluZ0ZvcklkZW50aWZpZXIiLCJBbmltYXRlZFZhbHVlIiwiX0FuaW1hdGVkV2l0aENoaWxkcmVuMiIsInZhbHVlIiwiY29uZmlnIiwiX3RoaXMiLCJFcnJvciIsIl9zdGFydGluZ1ZhbHVlIiwiX3ZhbHVlIiwiX29mZnNldCIsIl9hbmltYXRpb24iLCJ1c2VOYXRpdmVEcml2ZXIiLCJfX21ha2VOYXRpdmUiLCJrZXkiLCJfX2RldGFjaCIsIl90aGlzMiIsIl9faXNOYXRpdmUiLCJnZXRWYWx1ZSIsIl9fZ2V0TmF0aXZlVGFnIiwic3RvcEFuaW1hdGlvbiIsIl9fZ2V0VmFsdWUiLCJzZXRWYWx1ZSIsIl90aGlzMyIsInN0b3AiLCJfdXBkYXRlVmFsdWUiLCJ0b1N0cmluZyIsInNldEFuaW1hdGVkTm9kZVZhbHVlIiwic2V0T2Zmc2V0Iiwib2Zmc2V0Iiwic2V0QW5pbWF0ZWROb2RlT2Zmc2V0IiwiZmxhdHRlbk9mZnNldCIsImZsYXR0ZW5BbmltYXRlZE5vZGVPZmZzZXQiLCJleHRyYWN0T2Zmc2V0IiwiZXh0cmFjdEFuaW1hdGVkTm9kZU9mZnNldCIsImNhbGxiYWNrIiwic3RvcFRyYWNraW5nIiwicmVzZXRBbmltYXRpb24iLCJfX29uQW5pbWF0ZWRWYWx1ZVVwZGF0ZVJlY2VpdmVkIiwiaW50ZXJwb2xhdGUiLCJhbmltYXRlIiwiYW5pbWF0aW9uIiwiX3RoaXM0IiwiaGFuZGxlIiwiX19pc0ludGVyYWN0aW9uIiwiY3JlYXRlSW50ZXJhY3Rpb25IYW5kbGUiLCJwcmV2aW91c0FuaW1hdGlvbiIsInN0YXJ0IiwicmVzdWx0IiwiY2xlYXJJbnRlcmFjdGlvbkhhbmRsZSIsIl90cmFja2luZyIsInRyYWNrIiwidHJhY2tpbmciLCJmbHVzaCIsInVuZGVmaW5lZCIsIl9fZ2V0TmF0aXZlQ29uZmlnIiwidHlwZSIsIl9kZWZhdWx0IiwibW9kdWxlIl0sInNvdXJjZXMiOlsiQW5pbWF0ZWRWYWx1ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKiBAZm9ybWF0XG4gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG52YXIgX2ludGVyb3BSZXF1aXJlRGVmYXVsdCA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdFwiKS5kZWZhdWx0O1xuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDtcbnZhciBfQW5pbWF0ZWRJbnRlcnBvbGF0aW9uID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9BbmltYXRlZEludGVycG9sYXRpb25cIikpO1xudmFyIF9BbmltYXRlZFdpdGhDaGlsZHJlbiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4vQW5pbWF0ZWRXaXRoQ2hpbGRyZW5cIikpO1xudmFyIF9JbnRlcmFjdGlvbk1hbmFnZXIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi8uLi8uLi8uLi9leHBvcnRzL0ludGVyYWN0aW9uTWFuYWdlclwiKSk7XG52YXIgX05hdGl2ZUFuaW1hdGVkSGVscGVyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vTmF0aXZlQW5pbWF0ZWRIZWxwZXJcIikpO1xudmFyIE5hdGl2ZUFuaW1hdGVkQVBJID0gX05hdGl2ZUFuaW1hdGVkSGVscGVyLmRlZmF1bHQuQVBJO1xuXG4vKipcbiAqIEFuaW1hdGVkIHdvcmtzIGJ5IGJ1aWxkaW5nIGEgZGlyZWN0ZWQgYWN5Y2xpYyBncmFwaCBvZiBkZXBlbmRlbmNpZXNcbiAqIHRyYW5zcGFyZW50bHkgd2hlbiB5b3UgcmVuZGVyIHlvdXIgQW5pbWF0ZWQgY29tcG9uZW50cy5cbiAqXG4gKiAgICAgICAgICAgICAgIG5ldyBBbmltYXRlZC5WYWx1ZSgwKVxuICogICAgIC5pbnRlcnBvbGF0ZSgpICAgICAgICAuaW50ZXJwb2xhdGUoKSAgICBuZXcgQW5pbWF0ZWQuVmFsdWUoMSlcbiAqICAgICAgICAgb3BhY2l0eSAgICAgICAgICAgICAgIHRyYW5zbGF0ZVkgICAgICBzY2FsZVxuICogICAgICAgICAgc3R5bGUgICAgICAgICAgICAgICAgICAgICAgICAgdHJhbnNmb3JtXG4gKiAgICAgICAgIFZpZXcjMjM0ICAgICAgICAgICAgICAgICAgICAgICAgIHN0eWxlXG4gKiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgVmlldyMxMjNcbiAqXG4gKiBBKSBUb3AgRG93biBwaGFzZVxuICogV2hlbiBhbiBBbmltYXRlZC5WYWx1ZSBpcyB1cGRhdGVkLCB3ZSByZWN1cnNpdmVseSBnbyBkb3duIHRocm91Z2ggdGhpc1xuICogZ3JhcGggaW4gb3JkZXIgdG8gZmluZCBsZWFmIG5vZGVzOiB0aGUgdmlld3MgdGhhdCB3ZSBmbGFnIGFzIG5lZWRpbmdcbiAqIGFuIHVwZGF0ZS5cbiAqXG4gKiBCKSBCb3R0b20gVXAgcGhhc2VcbiAqIFdoZW4gYSB2aWV3IGlzIGZsYWdnZWQgYXMgbmVlZGluZyBhbiB1cGRhdGUsIHdlIHJlY3Vyc2l2ZWx5IGdvIGJhY2sgdXBcbiAqIGluIG9yZGVyIHRvIGJ1aWxkIHRoZSBuZXcgdmFsdWUgdGhhdCBpdCBuZWVkcy4gVGhlIHJlYXNvbiB3aHkgd2UgbmVlZFxuICogdGhpcyB0d28tcGhhc2VzIHByb2Nlc3MgaXMgdG8gZGVhbCB3aXRoIGNvbXBvc2l0ZSBwcm9wcyBzdWNoIGFzXG4gKiB0cmFuc2Zvcm0gd2hpY2ggY2FuIHJlY2VpdmUgdmFsdWVzIGZyb20gbXVsdGlwbGUgcGFyZW50cy5cbiAqL1xuZnVuY3Rpb24gX2ZsdXNoKHJvb3ROb2RlKSB7XG4gIHZhciBhbmltYXRlZFN0eWxlcyA9IG5ldyBTZXQoKTtcbiAgZnVuY3Rpb24gZmluZEFuaW1hdGVkU3R5bGVzKG5vZGUpIHtcbiAgICAvKiAkRmxvd0ZpeE1lW3Byb3AtbWlzc2luZ10gKD49MC42OC4wIHNpdGU9cmVhY3RfbmF0aXZlX2ZiKSBUaGlzIGNvbW1lbnRcbiAgICAgKiBzdXBwcmVzc2VzIGFuIGVycm9yIGZvdW5kIHdoZW4gRmxvdyB2MC42OCB3YXMgZGVwbG95ZWQuIFRvIHNlZSB0aGUgZXJyb3JcbiAgICAgKiBkZWxldGUgdGhpcyBjb21tZW50IGFuZCBydW4gRmxvdy4gKi9cbiAgICBpZiAodHlwZW9mIG5vZGUudXBkYXRlID09PSAnZnVuY3Rpb24nKSB7XG4gICAgICBhbmltYXRlZFN0eWxlcy5hZGQobm9kZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIG5vZGUuX19nZXRDaGlsZHJlbigpLmZvckVhY2goZmluZEFuaW1hdGVkU3R5bGVzKTtcbiAgICB9XG4gIH1cbiAgZmluZEFuaW1hdGVkU3R5bGVzKHJvb3ROb2RlKTtcbiAgLy8gJEZsb3dGaXhNZVtwcm9wLW1pc3NpbmddXG4gIGFuaW1hdGVkU3R5bGVzLmZvckVhY2goYW5pbWF0ZWRTdHlsZSA9PiBhbmltYXRlZFN0eWxlLnVwZGF0ZSgpKTtcbn1cblxuLyoqXG4gKiBTb21lIG9wZXJhdGlvbnMgYXJlIGV4ZWN1dGVkIG9ubHkgb24gYmF0Y2ggZW5kLCB3aGljaCBpcyBfbW9zdGx5XyBzY2hlZHVsZWQgd2hlblxuICogQW5pbWF0ZWQgY29tcG9uZW50IHByb3BzIGNoYW5nZS4gRm9yIHNvbWUgb2YgdGhlIGNoYW5nZXMgd2hpY2ggcmVxdWlyZSBpbW1lZGlhdGUgZXhlY3V0aW9uXG4gKiAoZS5nLiBzZXRWYWx1ZSksIHdlIGNyZWF0ZSBhIHNlcGFyYXRlIGJhdGNoIGluIGNhc2Ugbm9uZSBpcyBzY2hlZHVsZWQuXG4gKi9cbmZ1bmN0aW9uIF9leGVjdXRlQXNBbmltYXRlZEJhdGNoKGlkLCBvcGVyYXRpb24pIHtcbiAgTmF0aXZlQW5pbWF0ZWRBUEkuc2V0V2FpdGluZ0ZvcklkZW50aWZpZXIoaWQpO1xuICBvcGVyYXRpb24oKTtcbiAgTmF0aXZlQW5pbWF0ZWRBUEkudW5zZXRXYWl0aW5nRm9ySWRlbnRpZmllcihpZCk7XG59XG5cbi8qKlxuICogU3RhbmRhcmQgdmFsdWUgZm9yIGRyaXZpbmcgYW5pbWF0aW9ucy4gIE9uZSBgQW5pbWF0ZWQuVmFsdWVgIGNhbiBkcml2ZVxuICogbXVsdGlwbGUgcHJvcGVydGllcyBpbiBhIHN5bmNocm9uaXplZCBmYXNoaW9uLCBidXQgY2FuIG9ubHkgYmUgZHJpdmVuIGJ5IG9uZVxuICogbWVjaGFuaXNtIGF0IGEgdGltZS4gIFVzaW5nIGEgbmV3IG1lY2hhbmlzbSAoZS5nLiBzdGFydGluZyBhIG5ldyBhbmltYXRpb24sXG4gKiBvciBjYWxsaW5nIGBzZXRWYWx1ZWApIHdpbGwgc3RvcCBhbnkgcHJldmlvdXMgb25lcy5cbiAqXG4gKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZHZhbHVlXG4gKi9cbmNsYXNzIEFuaW1hdGVkVmFsdWUgZXh0ZW5kcyBfQW5pbWF0ZWRXaXRoQ2hpbGRyZW4uZGVmYXVsdCB7XG4gIGNvbnN0cnVjdG9yKHZhbHVlLCBjb25maWcpIHtcbiAgICBzdXBlcigpO1xuICAgIGlmICh0eXBlb2YgdmFsdWUgIT09ICdudW1iZXInKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ0FuaW1hdGVkVmFsdWU6IEF0dGVtcHRpbmcgdG8gc2V0IHZhbHVlIHRvIHVuZGVmaW5lZCcpO1xuICAgIH1cbiAgICB0aGlzLl9zdGFydGluZ1ZhbHVlID0gdGhpcy5fdmFsdWUgPSB2YWx1ZTtcbiAgICB0aGlzLl9vZmZzZXQgPSAwO1xuICAgIHRoaXMuX2FuaW1hdGlvbiA9IG51bGw7XG4gICAgaWYgKGNvbmZpZyAmJiBjb25maWcudXNlTmF0aXZlRHJpdmVyKSB7XG4gICAgICB0aGlzLl9fbWFrZU5hdGl2ZSgpO1xuICAgIH1cbiAgfVxuICBfX2RldGFjaCgpIHtcbiAgICBpZiAodGhpcy5fX2lzTmF0aXZlKSB7XG4gICAgICBOYXRpdmVBbmltYXRlZEFQSS5nZXRWYWx1ZSh0aGlzLl9fZ2V0TmF0aXZlVGFnKCksIHZhbHVlID0+IHtcbiAgICAgICAgdGhpcy5fdmFsdWUgPSB2YWx1ZSAtIHRoaXMuX29mZnNldDtcbiAgICAgIH0pO1xuICAgIH1cbiAgICB0aGlzLnN0b3BBbmltYXRpb24oKTtcbiAgICBzdXBlci5fX2RldGFjaCgpO1xuICB9XG4gIF9fZ2V0VmFsdWUoKSB7XG4gICAgcmV0dXJuIHRoaXMuX3ZhbHVlICsgdGhpcy5fb2Zmc2V0O1xuICB9XG5cbiAgLyoqXG4gICAqIERpcmVjdGx5IHNldCB0aGUgdmFsdWUuICBUaGlzIHdpbGwgc3RvcCBhbnkgYW5pbWF0aW9ucyBydW5uaW5nIG9uIHRoZSB2YWx1ZVxuICAgKiBhbmQgdXBkYXRlIGFsbCB0aGUgYm91bmQgcHJvcGVydGllcy5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWR2YWx1ZSNzZXR2YWx1ZVxuICAgKi9cbiAgc2V0VmFsdWUodmFsdWUpIHtcbiAgICBpZiAodGhpcy5fYW5pbWF0aW9uKSB7XG4gICAgICB0aGlzLl9hbmltYXRpb24uc3RvcCgpO1xuICAgICAgdGhpcy5fYW5pbWF0aW9uID0gbnVsbDtcbiAgICB9XG4gICAgdGhpcy5fdXBkYXRlVmFsdWUodmFsdWUsICF0aGlzLl9faXNOYXRpdmUgLyogZG9uJ3QgcGVyZm9ybSBhIGZsdXNoIGZvciBuYXRpdmVseSBkcml2ZW4gdmFsdWVzICovKTtcbiAgICBpZiAodGhpcy5fX2lzTmF0aXZlKSB7XG4gICAgICBfZXhlY3V0ZUFzQW5pbWF0ZWRCYXRjaCh0aGlzLl9fZ2V0TmF0aXZlVGFnKCkudG9TdHJpbmcoKSwgKCkgPT4gTmF0aXZlQW5pbWF0ZWRBUEkuc2V0QW5pbWF0ZWROb2RlVmFsdWUodGhpcy5fX2dldE5hdGl2ZVRhZygpLCB2YWx1ZSkpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTZXRzIGFuIG9mZnNldCB0aGF0IGlzIGFwcGxpZWQgb24gdG9wIG9mIHdoYXRldmVyIHZhbHVlIGlzIHNldCwgd2hldGhlciB2aWFcbiAgICogYHNldFZhbHVlYCwgYW4gYW5pbWF0aW9uLCBvciBgQW5pbWF0ZWQuZXZlbnRgLiAgVXNlZnVsIGZvciBjb21wZW5zYXRpbmdcbiAgICogdGhpbmdzIGxpa2UgdGhlIHN0YXJ0IG9mIGEgcGFuIGdlc3R1cmUuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkdmFsdWUjc2V0b2Zmc2V0XG4gICAqL1xuICBzZXRPZmZzZXQob2Zmc2V0KSB7XG4gICAgdGhpcy5fb2Zmc2V0ID0gb2Zmc2V0O1xuICAgIGlmICh0aGlzLl9faXNOYXRpdmUpIHtcbiAgICAgIE5hdGl2ZUFuaW1hdGVkQVBJLnNldEFuaW1hdGVkTm9kZU9mZnNldCh0aGlzLl9fZ2V0TmF0aXZlVGFnKCksIG9mZnNldCk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIE1lcmdlcyB0aGUgb2Zmc2V0IHZhbHVlIGludG8gdGhlIGJhc2UgdmFsdWUgYW5kIHJlc2V0cyB0aGUgb2Zmc2V0IHRvIHplcm8uXG4gICAqIFRoZSBmaW5hbCBvdXRwdXQgb2YgdGhlIHZhbHVlIGlzIHVuY2hhbmdlZC5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWR2YWx1ZSNmbGF0dGVub2Zmc2V0XG4gICAqL1xuICBmbGF0dGVuT2Zmc2V0KCkge1xuICAgIHRoaXMuX3ZhbHVlICs9IHRoaXMuX29mZnNldDtcbiAgICB0aGlzLl9vZmZzZXQgPSAwO1xuICAgIGlmICh0aGlzLl9faXNOYXRpdmUpIHtcbiAgICAgIE5hdGl2ZUFuaW1hdGVkQVBJLmZsYXR0ZW5BbmltYXRlZE5vZGVPZmZzZXQodGhpcy5fX2dldE5hdGl2ZVRhZygpKTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogU2V0cyB0aGUgb2Zmc2V0IHZhbHVlIHRvIHRoZSBiYXNlIHZhbHVlLCBhbmQgcmVzZXRzIHRoZSBiYXNlIHZhbHVlIHRvIHplcm8uXG4gICAqIFRoZSBmaW5hbCBvdXRwdXQgb2YgdGhlIHZhbHVlIGlzIHVuY2hhbmdlZC5cbiAgICpcbiAgICogU2VlIGh0dHBzOi8vcmVhY3RuYXRpdmUuZGV2L2RvY3MvYW5pbWF0ZWR2YWx1ZSNleHRyYWN0b2Zmc2V0XG4gICAqL1xuICBleHRyYWN0T2Zmc2V0KCkge1xuICAgIHRoaXMuX29mZnNldCArPSB0aGlzLl92YWx1ZTtcbiAgICB0aGlzLl92YWx1ZSA9IDA7XG4gICAgaWYgKHRoaXMuX19pc05hdGl2ZSkge1xuICAgICAgTmF0aXZlQW5pbWF0ZWRBUEkuZXh0cmFjdEFuaW1hdGVkTm9kZU9mZnNldCh0aGlzLl9fZ2V0TmF0aXZlVGFnKCkpO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBTdG9wcyBhbnkgcnVubmluZyBhbmltYXRpb24gb3IgdHJhY2tpbmcuIGBjYWxsYmFja2AgaXMgaW52b2tlZCB3aXRoIHRoZVxuICAgKiBmaW5hbCB2YWx1ZSBhZnRlciBzdG9wcGluZyB0aGUgYW5pbWF0aW9uLCB3aGljaCBpcyB1c2VmdWwgZm9yIHVwZGF0aW5nXG4gICAqIHN0YXRlIHRvIG1hdGNoIHRoZSBhbmltYXRpb24gcG9zaXRpb24gd2l0aCBsYXlvdXQuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkdmFsdWUjc3RvcGFuaW1hdGlvblxuICAgKi9cbiAgc3RvcEFuaW1hdGlvbihjYWxsYmFjaykge1xuICAgIHRoaXMuc3RvcFRyYWNraW5nKCk7XG4gICAgdGhpcy5fYW5pbWF0aW9uICYmIHRoaXMuX2FuaW1hdGlvbi5zdG9wKCk7XG4gICAgdGhpcy5fYW5pbWF0aW9uID0gbnVsbDtcbiAgICBpZiAoY2FsbGJhY2spIHtcbiAgICAgIGlmICh0aGlzLl9faXNOYXRpdmUpIHtcbiAgICAgICAgTmF0aXZlQW5pbWF0ZWRBUEkuZ2V0VmFsdWUodGhpcy5fX2dldE5hdGl2ZVRhZygpLCBjYWxsYmFjayk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjYWxsYmFjayh0aGlzLl9fZ2V0VmFsdWUoKSk7XG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFN0b3BzIGFueSBhbmltYXRpb24gYW5kIHJlc2V0cyB0aGUgdmFsdWUgdG8gaXRzIG9yaWdpbmFsLlxuICAgKlxuICAgKiBTZWUgaHR0cHM6Ly9yZWFjdG5hdGl2ZS5kZXYvZG9jcy9hbmltYXRlZHZhbHVlI3Jlc2V0YW5pbWF0aW9uXG4gICAqL1xuICByZXNldEFuaW1hdGlvbihjYWxsYmFjaykge1xuICAgIHRoaXMuc3RvcEFuaW1hdGlvbihjYWxsYmFjayk7XG4gICAgdGhpcy5fdmFsdWUgPSB0aGlzLl9zdGFydGluZ1ZhbHVlO1xuICAgIGlmICh0aGlzLl9faXNOYXRpdmUpIHtcbiAgICAgIE5hdGl2ZUFuaW1hdGVkQVBJLnNldEFuaW1hdGVkTm9kZVZhbHVlKHRoaXMuX19nZXROYXRpdmVUYWcoKSwgdGhpcy5fc3RhcnRpbmdWYWx1ZSk7XG4gICAgfVxuICB9XG4gIF9fb25BbmltYXRlZFZhbHVlVXBkYXRlUmVjZWl2ZWQodmFsdWUpIHtcbiAgICB0aGlzLl91cGRhdGVWYWx1ZSh2YWx1ZSwgZmFsc2UgLypmbHVzaCovKTtcbiAgfVxuXG4gIC8qKlxuICAgKiBJbnRlcnBvbGF0ZXMgdGhlIHZhbHVlIGJlZm9yZSB1cGRhdGluZyB0aGUgcHJvcGVydHksIGUuZy4gbWFwcGluZyAwLTEgdG9cbiAgICogMC0xMC5cbiAgICovXG4gIGludGVycG9sYXRlKGNvbmZpZykge1xuICAgIHJldHVybiBuZXcgX0FuaW1hdGVkSW50ZXJwb2xhdGlvbi5kZWZhdWx0KHRoaXMsIGNvbmZpZyk7XG4gIH1cblxuICAvKipcbiAgICogVHlwaWNhbGx5IG9ubHkgdXNlZCBpbnRlcm5hbGx5LCBidXQgY291bGQgYmUgdXNlZCBieSBhIGN1c3RvbSBBbmltYXRpb25cbiAgICogY2xhc3MuXG4gICAqXG4gICAqIFNlZSBodHRwczovL3JlYWN0bmF0aXZlLmRldi9kb2NzL2FuaW1hdGVkdmFsdWUjYW5pbWF0ZVxuICAgKi9cbiAgYW5pbWF0ZShhbmltYXRpb24sIGNhbGxiYWNrKSB7XG4gICAgdmFyIGhhbmRsZSA9IG51bGw7XG4gICAgaWYgKGFuaW1hdGlvbi5fX2lzSW50ZXJhY3Rpb24pIHtcbiAgICAgIGhhbmRsZSA9IF9JbnRlcmFjdGlvbk1hbmFnZXIuZGVmYXVsdC5jcmVhdGVJbnRlcmFjdGlvbkhhbmRsZSgpO1xuICAgIH1cbiAgICB2YXIgcHJldmlvdXNBbmltYXRpb24gPSB0aGlzLl9hbmltYXRpb247XG4gICAgdGhpcy5fYW5pbWF0aW9uICYmIHRoaXMuX2FuaW1hdGlvbi5zdG9wKCk7XG4gICAgdGhpcy5fYW5pbWF0aW9uID0gYW5pbWF0aW9uO1xuICAgIGFuaW1hdGlvbi5zdGFydCh0aGlzLl92YWx1ZSwgdmFsdWUgPT4ge1xuICAgICAgLy8gTmF0aXZlbHkgZHJpdmVuIGFuaW1hdGlvbnMgd2lsbCBuZXZlciBjYWxsIGludG8gdGhhdCBjYWxsYmFja1xuICAgICAgdGhpcy5fdXBkYXRlVmFsdWUodmFsdWUsIHRydWUgLyogZmx1c2ggKi8pO1xuICAgIH0sIHJlc3VsdCA9PiB7XG4gICAgICB0aGlzLl9hbmltYXRpb24gPSBudWxsO1xuICAgICAgaWYgKGhhbmRsZSAhPT0gbnVsbCkge1xuICAgICAgICBfSW50ZXJhY3Rpb25NYW5hZ2VyLmRlZmF1bHQuY2xlYXJJbnRlcmFjdGlvbkhhbmRsZShoYW5kbGUpO1xuICAgICAgfVxuICAgICAgY2FsbGJhY2sgJiYgY2FsbGJhY2socmVzdWx0KTtcbiAgICB9LCBwcmV2aW91c0FuaW1hdGlvbiwgdGhpcyk7XG4gIH1cblxuICAvKipcbiAgICogVHlwaWNhbGx5IG9ubHkgdXNlZCBpbnRlcm5hbGx5LlxuICAgKi9cbiAgc3RvcFRyYWNraW5nKCkge1xuICAgIHRoaXMuX3RyYWNraW5nICYmIHRoaXMuX3RyYWNraW5nLl9fZGV0YWNoKCk7XG4gICAgdGhpcy5fdHJhY2tpbmcgPSBudWxsO1xuICB9XG5cbiAgLyoqXG4gICAqIFR5cGljYWxseSBvbmx5IHVzZWQgaW50ZXJuYWxseS5cbiAgICovXG4gIHRyYWNrKHRyYWNraW5nKSB7XG4gICAgdGhpcy5zdG9wVHJhY2tpbmcoKTtcbiAgICB0aGlzLl90cmFja2luZyA9IHRyYWNraW5nO1xuICAgIC8vIE1ha2Ugc3VyZSB0aGF0IHRoZSB0cmFja2luZyBhbmltYXRpb24gc3RhcnRzIGV4ZWN1dGluZ1xuICAgIHRoaXMuX3RyYWNraW5nICYmIHRoaXMuX3RyYWNraW5nLnVwZGF0ZSgpO1xuICB9XG4gIF91cGRhdGVWYWx1ZSh2YWx1ZSwgZmx1c2gpIHtcbiAgICBpZiAodmFsdWUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdBbmltYXRlZFZhbHVlOiBBdHRlbXB0aW5nIHRvIHNldCB2YWx1ZSB0byB1bmRlZmluZWQnKTtcbiAgICB9XG4gICAgdGhpcy5fdmFsdWUgPSB2YWx1ZTtcbiAgICBpZiAoZmx1c2gpIHtcbiAgICAgIF9mbHVzaCh0aGlzKTtcbiAgICB9XG4gICAgc3VwZXIuX19jYWxsTGlzdGVuZXJzKHRoaXMuX19nZXRWYWx1ZSgpKTtcbiAgfVxuICBfX2dldE5hdGl2ZUNvbmZpZygpIHtcbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogJ3ZhbHVlJyxcbiAgICAgIHZhbHVlOiB0aGlzLl92YWx1ZSxcbiAgICAgIG9mZnNldDogdGhpcy5fb2Zmc2V0XG4gICAgfTtcbiAgfVxufVxudmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gQW5pbWF0ZWRWYWx1ZTtcbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBVUEsWUFBWTs7QUFBQyxJQUFBQSx1QkFBQSxHQUFBQyxPQUFBO0FBQUEsSUFBQUMsZ0JBQUEsR0FBQUYsdUJBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFFLGFBQUEsR0FBQUgsdUJBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFHLDJCQUFBLEdBQUFKLHVCQUFBLENBQUFDLE9BQUE7QUFBQSxJQUFBSSxnQkFBQSxHQUFBTCx1QkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQUssS0FBQSxHQUFBTix1QkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQU0sVUFBQSxHQUFBUCx1QkFBQSxDQUFBQyxPQUFBO0FBQUEsU0FBQU8sV0FBQUMsQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsV0FBQUQsQ0FBQSxPQUFBTCxnQkFBQSxDQUFBTyxPQUFBLEVBQUFGLENBQUEsT0FBQU4sMkJBQUEsQ0FBQVEsT0FBQSxFQUFBSCxDQUFBLEVBQUFJLHlCQUFBLEtBQUFDLE9BQUEsQ0FBQUMsU0FBQSxDQUFBTCxDQUFBLEVBQUFDLENBQUEsWUFBQU4sZ0JBQUEsQ0FBQU8sT0FBQSxFQUFBSCxDQUFBLEVBQUFPLFdBQUEsSUFBQU4sQ0FBQSxDQUFBTyxLQUFBLENBQUFSLENBQUEsRUFBQUUsQ0FBQTtBQUFBLFNBQUFFLDBCQUFBLGNBQUFKLENBQUEsSUFBQVMsT0FBQSxDQUFBQyxTQUFBLENBQUFDLE9BQUEsQ0FBQUMsSUFBQSxDQUFBUCxPQUFBLENBQUFDLFNBQUEsQ0FBQUcsT0FBQSxpQ0FBQVQsQ0FBQSxhQUFBSSx5QkFBQSxZQUFBQSwwQkFBQSxhQUFBSixDQUFBO0FBQUEsU0FBQWEsY0FBQWIsQ0FBQSxFQUFBQyxDQUFBLEVBQUFDLENBQUEsRUFBQVksQ0FBQSxRQUFBQyxDQUFBLE9BQUFsQixLQUFBLENBQUFNLE9BQUEsTUFBQVAsZ0JBQUEsQ0FBQU8sT0FBQSxNQUFBVyxDQUFBLEdBQUFkLENBQUEsQ0FBQVUsU0FBQSxHQUFBVixDQUFBLEdBQUFDLENBQUEsRUFBQUMsQ0FBQSxjQUFBWSxDQUFBLHlCQUFBQyxDQUFBLGFBQUFmLENBQUEsV0FBQWUsQ0FBQSxDQUFBUCxLQUFBLENBQUFOLENBQUEsRUFBQUYsQ0FBQSxPQUFBZSxDQUFBO0FBRWIsSUFBSUMsc0JBQXNCLEdBQUd4QixPQUFPLENBQUMsOENBQThDLENBQUMsQ0FBQ1csT0FBTztBQUM1RmMsT0FBTyxDQUFDQyxVQUFVLEdBQUcsSUFBSTtBQUN6QkQsT0FBTyxDQUFDZCxPQUFPLEdBQUcsS0FBSyxDQUFDO0FBQ3hCLElBQUlnQixzQkFBc0IsR0FBR0gsc0JBQXNCLENBQUN4QixPQUFPLDBCQUEwQixDQUFDLENBQUM7QUFDdkYsSUFBSTRCLHFCQUFxQixHQUFHSixzQkFBc0IsQ0FBQ3hCLE9BQU8seUJBQXlCLENBQUMsQ0FBQztBQUNyRixJQUFJNkIsbUJBQW1CLEdBQUdMLHNCQUFzQixDQUFDeEIsT0FBTyx5Q0FBeUMsQ0FBQyxDQUFDO0FBQ25HLElBQUk4QixxQkFBcUIsR0FBR04sc0JBQXNCLENBQUN4QixPQUFPLDBCQUEwQixDQUFDLENBQUM7QUFDdEYsSUFBSStCLGlCQUFpQixHQUFHRCxxQkFBcUIsQ0FBQ25CLE9BQU8sQ0FBQ3FCLEdBQUc7QUF3QnpELFNBQVNDLE1BQU1BLENBQUNDLFFBQVEsRUFBRTtFQUN4QixJQUFJQyxjQUFjLEdBQUcsSUFBSUMsR0FBRyxDQUFDLENBQUM7RUFDOUIsU0FBU0Msa0JBQWtCQSxDQUFDQyxJQUFJLEVBQUU7SUFJaEMsSUFBSSxPQUFPQSxJQUFJLENBQUNDLE1BQU0sS0FBSyxVQUFVLEVBQUU7TUFDckNKLGNBQWMsQ0FBQ0ssR0FBRyxDQUFDRixJQUFJLENBQUM7SUFDMUIsQ0FBQyxNQUFNO01BQ0xBLElBQUksQ0FBQ0csYUFBYSxDQUFDLENBQUMsQ0FBQ0MsT0FBTyxDQUFDTCxrQkFBa0IsQ0FBQztJQUNsRDtFQUNGO0VBQ0FBLGtCQUFrQixDQUFDSCxRQUFRLENBQUM7RUFFNUJDLGNBQWMsQ0FBQ08sT0FBTyxDQUFDLFVBQUFDLGFBQWE7SUFBQSxPQUFJQSxhQUFhLENBQUNKLE1BQU0sQ0FBQyxDQUFDO0VBQUEsRUFBQztBQUNqRTtBQU9BLFNBQVNLLHVCQUF1QkEsQ0FBQ0MsRUFBRSxFQUFFQyxTQUFTLEVBQUU7RUFDOUNmLGlCQUFpQixDQUFDZ0IsdUJBQXVCLENBQUNGLEVBQUUsQ0FBQztFQUM3Q0MsU0FBUyxDQUFDLENBQUM7RUFDWGYsaUJBQWlCLENBQUNpQix5QkFBeUIsQ0FBQ0gsRUFBRSxDQUFDO0FBQ2pEO0FBQUMsSUFVS0ksYUFBYSxhQUFBQyxzQkFBQTtFQUNqQixTQUFBRCxjQUFZRSxLQUFLLEVBQUVDLE1BQU0sRUFBRTtJQUFBLElBQUFDLEtBQUE7SUFBQSxJQUFBcEQsZ0JBQUEsQ0FBQVUsT0FBQSxRQUFBc0MsYUFBQTtJQUN6QkksS0FBQSxHQUFBOUMsVUFBQSxPQUFBMEMsYUFBQTtJQUNBLElBQUksT0FBT0UsS0FBSyxLQUFLLFFBQVEsRUFBRTtNQUM3QixNQUFNLElBQUlHLEtBQUssQ0FBQyxxREFBcUQsQ0FBQztJQUN4RTtJQUNBRCxLQUFBLENBQUtFLGNBQWMsR0FBR0YsS0FBQSxDQUFLRyxNQUFNLEdBQUdMLEtBQUs7SUFDekNFLEtBQUEsQ0FBS0ksT0FBTyxHQUFHLENBQUM7SUFDaEJKLEtBQUEsQ0FBS0ssVUFBVSxHQUFHLElBQUk7SUFDdEIsSUFBSU4sTUFBTSxJQUFJQSxNQUFNLENBQUNPLGVBQWUsRUFBRTtNQUNwQ04sS0FBQSxDQUFLTyxZQUFZLENBQUMsQ0FBQztJQUNyQjtJQUFDLE9BQUFQLEtBQUE7RUFDSDtFQUFDLElBQUEvQyxVQUFBLENBQUFLLE9BQUEsRUFBQXNDLGFBQUEsRUFBQUMsc0JBQUE7RUFBQSxXQUFBaEQsYUFBQSxDQUFBUyxPQUFBLEVBQUFzQyxhQUFBO0lBQUFZLEdBQUE7SUFBQVYsS0FBQSxFQUNELFNBQUFXLFFBQVFBLENBQUEsRUFBRztNQUFBLElBQUFDLE1BQUE7TUFDVCxJQUFJLElBQUksQ0FBQ0MsVUFBVSxFQUFFO1FBQ25CakMsaUJBQWlCLENBQUNrQyxRQUFRLENBQUMsSUFBSSxDQUFDQyxjQUFjLENBQUMsQ0FBQyxFQUFFLFVBQUFmLEtBQUssRUFBSTtVQUN6RFksTUFBSSxDQUFDUCxNQUFNLEdBQUdMLEtBQUssR0FBR1ksTUFBSSxDQUFDTixPQUFPO1FBQ3BDLENBQUMsQ0FBQztNQUNKO01BQ0EsSUFBSSxDQUFDVSxhQUFhLENBQUMsQ0FBQztNQUNwQjlDLGFBQUEsQ0FBQTRCLGFBQUE7SUFDRjtFQUFDO0lBQUFZLEdBQUE7SUFBQVYsS0FBQSxFQUNELFNBQUFpQixVQUFVQSxDQUFBLEVBQUc7TUFDWCxPQUFPLElBQUksQ0FBQ1osTUFBTSxHQUFHLElBQUksQ0FBQ0MsT0FBTztJQUNuQztFQUFDO0lBQUFJLEdBQUE7SUFBQVYsS0FBQSxFQVFELFNBQUFrQixRQUFRQSxDQUFDbEIsS0FBSyxFQUFFO01BQUEsSUFBQW1CLE1BQUE7TUFDZCxJQUFJLElBQUksQ0FBQ1osVUFBVSxFQUFFO1FBQ25CLElBQUksQ0FBQ0EsVUFBVSxDQUFDYSxJQUFJLENBQUMsQ0FBQztRQUN0QixJQUFJLENBQUNiLFVBQVUsR0FBRyxJQUFJO01BQ3hCO01BQ0EsSUFBSSxDQUFDYyxZQUFZLENBQUNyQixLQUFLLEVBQUUsQ0FBQyxJQUFJLENBQUNhLFVBQWlFLENBQUM7TUFDakcsSUFBSSxJQUFJLENBQUNBLFVBQVUsRUFBRTtRQUNuQnBCLHVCQUF1QixDQUFDLElBQUksQ0FBQ3NCLGNBQWMsQ0FBQyxDQUFDLENBQUNPLFFBQVEsQ0FBQyxDQUFDLEVBQUU7VUFBQSxPQUFNMUMsaUJBQWlCLENBQUMyQyxvQkFBb0IsQ0FBQ0osTUFBSSxDQUFDSixjQUFjLENBQUMsQ0FBQyxFQUFFZixLQUFLLENBQUM7UUFBQSxFQUFDO01BQ3ZJO0lBQ0Y7RUFBQztJQUFBVSxHQUFBO0lBQUFWLEtBQUEsRUFTRCxTQUFBd0IsU0FBU0EsQ0FBQ0MsTUFBTSxFQUFFO01BQ2hCLElBQUksQ0FBQ25CLE9BQU8sR0FBR21CLE1BQU07TUFDckIsSUFBSSxJQUFJLENBQUNaLFVBQVUsRUFBRTtRQUNuQmpDLGlCQUFpQixDQUFDOEMscUJBQXFCLENBQUMsSUFBSSxDQUFDWCxjQUFjLENBQUMsQ0FBQyxFQUFFVSxNQUFNLENBQUM7TUFDeEU7SUFDRjtFQUFDO0lBQUFmLEdBQUE7SUFBQVYsS0FBQSxFQVFELFNBQUEyQixhQUFhQSxDQUFBLEVBQUc7TUFDZCxJQUFJLENBQUN0QixNQUFNLElBQUksSUFBSSxDQUFDQyxPQUFPO01BQzNCLElBQUksQ0FBQ0EsT0FBTyxHQUFHLENBQUM7TUFDaEIsSUFBSSxJQUFJLENBQUNPLFVBQVUsRUFBRTtRQUNuQmpDLGlCQUFpQixDQUFDZ0QseUJBQXlCLENBQUMsSUFBSSxDQUFDYixjQUFjLENBQUMsQ0FBQyxDQUFDO01BQ3BFO0lBQ0Y7RUFBQztJQUFBTCxHQUFBO0lBQUFWLEtBQUEsRUFRRCxTQUFBNkIsYUFBYUEsQ0FBQSxFQUFHO01BQ2QsSUFBSSxDQUFDdkIsT0FBTyxJQUFJLElBQUksQ0FBQ0QsTUFBTTtNQUMzQixJQUFJLENBQUNBLE1BQU0sR0FBRyxDQUFDO01BQ2YsSUFBSSxJQUFJLENBQUNRLFVBQVUsRUFBRTtRQUNuQmpDLGlCQUFpQixDQUFDa0QseUJBQXlCLENBQUMsSUFBSSxDQUFDZixjQUFjLENBQUMsQ0FBQyxDQUFDO01BQ3BFO0lBQ0Y7RUFBQztJQUFBTCxHQUFBO0lBQUFWLEtBQUEsRUFTRCxTQUFBZ0IsYUFBYUEsQ0FBQ2UsUUFBUSxFQUFFO01BQ3RCLElBQUksQ0FBQ0MsWUFBWSxDQUFDLENBQUM7TUFDbkIsSUFBSSxDQUFDekIsVUFBVSxJQUFJLElBQUksQ0FBQ0EsVUFBVSxDQUFDYSxJQUFJLENBQUMsQ0FBQztNQUN6QyxJQUFJLENBQUNiLFVBQVUsR0FBRyxJQUFJO01BQ3RCLElBQUl3QixRQUFRLEVBQUU7UUFDWixJQUFJLElBQUksQ0FBQ2xCLFVBQVUsRUFBRTtVQUNuQmpDLGlCQUFpQixDQUFDa0MsUUFBUSxDQUFDLElBQUksQ0FBQ0MsY0FBYyxDQUFDLENBQUMsRUFBRWdCLFFBQVEsQ0FBQztRQUM3RCxDQUFDLE1BQU07VUFDTEEsUUFBUSxDQUFDLElBQUksQ0FBQ2QsVUFBVSxDQUFDLENBQUMsQ0FBQztRQUM3QjtNQUNGO0lBQ0Y7RUFBQztJQUFBUCxHQUFBO0lBQUFWLEtBQUEsRUFPRCxTQUFBaUMsY0FBY0EsQ0FBQ0YsUUFBUSxFQUFFO01BQ3ZCLElBQUksQ0FBQ2YsYUFBYSxDQUFDZSxRQUFRLENBQUM7TUFDNUIsSUFBSSxDQUFDMUIsTUFBTSxHQUFHLElBQUksQ0FBQ0QsY0FBYztNQUNqQyxJQUFJLElBQUksQ0FBQ1MsVUFBVSxFQUFFO1FBQ25CakMsaUJBQWlCLENBQUMyQyxvQkFBb0IsQ0FBQyxJQUFJLENBQUNSLGNBQWMsQ0FBQyxDQUFDLEVBQUUsSUFBSSxDQUFDWCxjQUFjLENBQUM7TUFDcEY7SUFDRjtFQUFDO0lBQUFNLEdBQUE7SUFBQVYsS0FBQSxFQUNELFNBQUFrQywrQkFBK0JBLENBQUNsQyxLQUFLLEVBQUU7TUFDckMsSUFBSSxDQUFDcUIsWUFBWSxDQUFDckIsS0FBSyxFQUFFLEtBQWUsQ0FBQztJQUMzQztFQUFDO0lBQUFVLEdBQUE7SUFBQVYsS0FBQSxFQU1ELFNBQUFtQyxXQUFXQSxDQUFDbEMsTUFBTSxFQUFFO01BQ2xCLE9BQU8sSUFBSXpCLHNCQUFzQixDQUFDaEIsT0FBTyxDQUFDLElBQUksRUFBRXlDLE1BQU0sQ0FBQztJQUN6RDtFQUFDO0lBQUFTLEdBQUE7SUFBQVYsS0FBQSxFQVFELFNBQUFvQyxPQUFPQSxDQUFDQyxTQUFTLEVBQUVOLFFBQVEsRUFBRTtNQUFBLElBQUFPLE1BQUE7TUFDM0IsSUFBSUMsTUFBTSxHQUFHLElBQUk7TUFDakIsSUFBSUYsU0FBUyxDQUFDRyxlQUFlLEVBQUU7UUFDN0JELE1BQU0sR0FBRzdELG1CQUFtQixDQUFDbEIsT0FBTyxDQUFDaUYsdUJBQXVCLENBQUMsQ0FBQztNQUNoRTtNQUNBLElBQUlDLGlCQUFpQixHQUFHLElBQUksQ0FBQ25DLFVBQVU7TUFDdkMsSUFBSSxDQUFDQSxVQUFVLElBQUksSUFBSSxDQUFDQSxVQUFVLENBQUNhLElBQUksQ0FBQyxDQUFDO01BQ3pDLElBQUksQ0FBQ2IsVUFBVSxHQUFHOEIsU0FBUztNQUMzQkEsU0FBUyxDQUFDTSxLQUFLLENBQUMsSUFBSSxDQUFDdEMsTUFBTSxFQUFFLFVBQUFMLEtBQUssRUFBSTtRQUVwQ3NDLE1BQUksQ0FBQ2pCLFlBQVksQ0FBQ3JCLEtBQUssRUFBRSxJQUFnQixDQUFDO01BQzVDLENBQUMsRUFBRSxVQUFBNEMsTUFBTSxFQUFJO1FBQ1hOLE1BQUksQ0FBQy9CLFVBQVUsR0FBRyxJQUFJO1FBQ3RCLElBQUlnQyxNQUFNLEtBQUssSUFBSSxFQUFFO1VBQ25CN0QsbUJBQW1CLENBQUNsQixPQUFPLENBQUNxRixzQkFBc0IsQ0FBQ04sTUFBTSxDQUFDO1FBQzVEO1FBQ0FSLFFBQVEsSUFBSUEsUUFBUSxDQUFDYSxNQUFNLENBQUM7TUFDOUIsQ0FBQyxFQUFFRixpQkFBaUIsRUFBRSxJQUFJLENBQUM7SUFDN0I7RUFBQztJQUFBaEMsR0FBQTtJQUFBVixLQUFBLEVBS0QsU0FBQWdDLFlBQVlBLENBQUEsRUFBRztNQUNiLElBQUksQ0FBQ2MsU0FBUyxJQUFJLElBQUksQ0FBQ0EsU0FBUyxDQUFDbkMsUUFBUSxDQUFDLENBQUM7TUFDM0MsSUFBSSxDQUFDbUMsU0FBUyxHQUFHLElBQUk7SUFDdkI7RUFBQztJQUFBcEMsR0FBQTtJQUFBVixLQUFBLEVBS0QsU0FBQStDLEtBQUtBLENBQUNDLFFBQVEsRUFBRTtNQUNkLElBQUksQ0FBQ2hCLFlBQVksQ0FBQyxDQUFDO01BQ25CLElBQUksQ0FBQ2MsU0FBUyxHQUFHRSxRQUFRO01BRXpCLElBQUksQ0FBQ0YsU0FBUyxJQUFJLElBQUksQ0FBQ0EsU0FBUyxDQUFDMUQsTUFBTSxDQUFDLENBQUM7SUFDM0M7RUFBQztJQUFBc0IsR0FBQTtJQUFBVixLQUFBLEVBQ0QsU0FBQXFCLFlBQVlBLENBQUNyQixLQUFLLEVBQUVpRCxLQUFLLEVBQUU7TUFDekIsSUFBSWpELEtBQUssS0FBS2tELFNBQVMsRUFBRTtRQUN2QixNQUFNLElBQUkvQyxLQUFLLENBQUMscURBQXFELENBQUM7TUFDeEU7TUFDQSxJQUFJLENBQUNFLE1BQU0sR0FBR0wsS0FBSztNQUNuQixJQUFJaUQsS0FBSyxFQUFFO1FBQ1RuRSxNQUFNLENBQUMsSUFBSSxDQUFDO01BQ2Q7TUFDQVosYUFBQSxDQUFBNEIsYUFBQSwrQkFBc0IsSUFBSSxDQUFDbUIsVUFBVSxDQUFDLENBQUM7SUFDekM7RUFBQztJQUFBUCxHQUFBO0lBQUFWLEtBQUEsRUFDRCxTQUFBbUQsaUJBQWlCQSxDQUFBLEVBQUc7TUFDbEIsT0FBTztRQUNMQyxJQUFJLEVBQUUsT0FBTztRQUNicEQsS0FBSyxFQUFFLElBQUksQ0FBQ0ssTUFBTTtRQUNsQm9CLE1BQU0sRUFBRSxJQUFJLENBQUNuQjtNQUNmLENBQUM7SUFDSDtFQUFDO0FBQUEsRUE1THlCN0IscUJBQXFCLENBQUNqQixPQUFPO0FBOEx6RCxJQUFJNkYsUUFBUSxHQUFHL0UsT0FBTyxDQUFDZCxPQUFPLEdBQUdzQyxhQUFhO0FBQzlDd0QsTUFBTSxDQUFDaEYsT0FBTyxHQUFHQSxPQUFPLENBQUNkLE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=