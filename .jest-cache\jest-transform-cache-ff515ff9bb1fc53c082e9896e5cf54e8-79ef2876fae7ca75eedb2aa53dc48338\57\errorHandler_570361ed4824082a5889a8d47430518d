4b367a998880226337c5022d09242040
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.withErrorHandling = exports.showError = exports.handleError = exports.errorHandler = exports.createValidationError = exports.createNetworkError = exports.createAIServiceError = exports.ErrorType = exports.ErrorSeverity = exports.AppError = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _defineProperty2 = _interopRequireDefault(require("@babel/runtime/helpers/defineProperty"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _wrapNativeSuper2 = _interopRequireDefault(require("@babel/runtime/helpers/wrapNativeSuper"));
var _reactNative = require("react-native");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var ErrorType = exports.ErrorType = function (ErrorType) {
  ErrorType["NETWORK"] = "NETWORK";
  ErrorType["AUTHENTICATION"] = "AUTHENTICATION";
  ErrorType["VALIDATION"] = "VALIDATION";
  ErrorType["PERMISSION"] = "PERMISSION";
  ErrorType["AI_SERVICE"] = "AI_SERVICE";
  ErrorType["VIDEO_PROCESSING"] = "VIDEO_PROCESSING";
  ErrorType["DATABASE"] = "DATABASE";
  ErrorType["UNKNOWN"] = "UNKNOWN";
  return ErrorType;
}({});
var ErrorSeverity = exports.ErrorSeverity = function (ErrorSeverity) {
  ErrorSeverity["LOW"] = "LOW";
  ErrorSeverity["MEDIUM"] = "MEDIUM";
  ErrorSeverity["HIGH"] = "HIGH";
  ErrorSeverity["CRITICAL"] = "CRITICAL";
  return ErrorSeverity;
}({});
var AppError = exports.AppError = function (_Error) {
  function AppError(message) {
    var _this;
    var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : ErrorType.UNKNOWN;
    var severity = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : ErrorSeverity.MEDIUM;
    var userMessage = arguments.length > 3 ? arguments[3] : undefined;
    var technicalDetails = arguments.length > 4 ? arguments[4] : undefined;
    var context = arguments.length > 5 ? arguments[5] : undefined;
    (0, _classCallCheck2.default)(this, AppError);
    _this = _callSuper(this, AppError, [message]);
    _this.name = 'AppError';
    _this.type = type;
    _this.severity = severity;
    _this.userMessage = userMessage || _this.getDefaultUserMessage(type);
    _this.technicalDetails = technicalDetails;
    _this.timestamp = new Date();
    _this.context = context;
    return _this;
  }
  (0, _inherits2.default)(AppError, _Error);
  return (0, _createClass2.default)(AppError, [{
    key: "getDefaultUserMessage",
    value: function getDefaultUserMessage(type) {
      var messages = (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)({}, ErrorType.NETWORK, 'Please check your internet connection and try again.'), ErrorType.AUTHENTICATION, 'Please log in again to continue.'), ErrorType.VALIDATION, 'Please check your input and try again.'), ErrorType.PERMISSION, 'This feature requires additional permissions.'), ErrorType.AI_SERVICE, 'AI service is temporarily unavailable. Please try again later.'), ErrorType.VIDEO_PROCESSING, 'Video processing failed. Please try with a different video.'), ErrorType.DATABASE, 'Data operation failed. Please try again.'), ErrorType.UNKNOWN, 'An unexpected error occurred. Please try again.');
      return messages[type];
    }
  }]);
}((0, _wrapNativeSuper2.default)(Error));
var ErrorHandler = function () {
  function ErrorHandler() {
    (0, _classCallCheck2.default)(this, ErrorHandler);
    this.errorLog = [];
    this.maxLogSize = 100;
  }
  return (0, _createClass2.default)(ErrorHandler, [{
    key: "handle",
    value: function handle(error, context) {
      var appError;
      if (error instanceof AppError) {
        appError = error;
      } else {
        appError = this.convertToAppError(error, context);
      }
      if (context) {
        appError.context = Object.assign({}, appError.context, context);
      }
      this.logError(appError);
      this.reportToCrashAnalytics(appError);
      return appError;
    }
  }, {
    key: "convertToAppError",
    value: function convertToAppError(error, context) {
      var type = ErrorType.UNKNOWN;
      var severity = ErrorSeverity.MEDIUM;
      if (error.message.includes('network') || error.message.includes('fetch')) {
        type = ErrorType.NETWORK;
      } else if (error.message.includes('auth') || error.message.includes('unauthorized')) {
        type = ErrorType.AUTHENTICATION;
        severity = ErrorSeverity.HIGH;
      } else if (error.message.includes('validation') || error.message.includes('invalid')) {
        type = ErrorType.VALIDATION;
        severity = ErrorSeverity.LOW;
      } else if (error.message.includes('permission')) {
        type = ErrorType.PERMISSION;
        severity = ErrorSeverity.HIGH;
      }
      return new AppError(error.message, type, severity, undefined, {
        originalError: error
      }, context);
    }
  }, {
    key: "logError",
    value: function logError(error) {
      this.errorLog.unshift(error);
      if (this.errorLog.length > this.maxLogSize) {
        this.errorLog.pop();
      }
      if (__DEV__) {
        console.group(`🚨 ${error.type} Error - ${error.severity}`);
        console.error('Message:', error.message);
        console.error('User Message:', error.userMessage);
        console.error('Technical Details:', error.technicalDetails);
        console.error('Context:', error.context);
        console.error('Stack:', error.stack);
        console.groupEnd();
      }
      this.storeErrorLocally(error);
    }
  }, {
    key: "storeErrorLocally",
    value: (function () {
      var _storeErrorLocally = (0, _asyncToGenerator2.default)(function* (error) {
        try {
          var AsyncStorage = require('@react-native-async-storage/async-storage').default;
          var errorData = {
            message: error.message,
            type: error.type,
            severity: error.severity,
            userMessage: error.userMessage,
            timestamp: error.timestamp.toISOString(),
            context: error.context
          };
          var existingErrors = yield AsyncStorage.getItem('app_errors');
          var errors = existingErrors ? JSON.parse(existingErrors) : [];
          errors.unshift(errorData);
          if (errors.length > 50) {
            errors.splice(50);
          }
          yield AsyncStorage.setItem('app_errors', JSON.stringify(errors));
        } catch (storageError) {
          console.error('Failed to store error locally:', storageError);
        }
      });
      function storeErrorLocally(_x) {
        return _storeErrorLocally.apply(this, arguments);
      }
      return storeErrorLocally;
    }())
  }, {
    key: "reportToCrashAnalytics",
    value: function reportToCrashAnalytics(error) {
      if (!__DEV__ && error.severity === ErrorSeverity.CRITICAL) {
        console.log('Would report to crash analytics:', error);
      }
    }
  }, {
    key: "showUserError",
    value: function showUserError(error, options) {
      var _this2 = this;
      var title = (options == null ? void 0 : options.title) || 'Error';
      var buttons = [];
      if (options != null && options.showRetry && options != null && options.onRetry) {
        buttons.push({
          text: 'Retry',
          onPress: options.onRetry
        });
      }
      if (options != null && options.showDetails && __DEV__) {
        buttons.push({
          text: 'Details',
          onPress: function onPress() {
            return _this2.showErrorDetails(error);
          }
        });
      }
      buttons.push({
        text: 'OK',
        style: 'cancel'
      });
      _reactNative.Alert.alert(title, error.userMessage, buttons);
    }
  }, {
    key: "showErrorDetails",
    value: function showErrorDetails(error) {
      var details = `
Type: ${error.type}
Severity: ${error.severity}
Time: ${error.timestamp.toLocaleString()}
Technical: ${error.message}
Context: ${JSON.stringify(error.context, null, 2)}
    `.trim();
      _reactNative.Alert.alert('Error Details', details);
    }
  }, {
    key: "getErrorStats",
    value: function getErrorStats() {
      var byType = {};
      var bySeverity = {};
      this.errorLog.forEach(function (error) {
        byType[error.type] = (byType[error.type] || 0) + 1;
        bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
      });
      return {
        total: this.errorLog.length,
        byType: byType,
        bySeverity: bySeverity,
        recent: this.errorLog.slice(0, 10)
      };
    }
  }, {
    key: "clearErrorLog",
    value: function clearErrorLog() {
      this.errorLog = [];
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      if (!ErrorHandler.instance) {
        ErrorHandler.instance = new ErrorHandler();
      }
      return ErrorHandler.instance;
    }
  }]);
}();
var errorHandler = exports.errorHandler = ErrorHandler.getInstance();
var handleError = exports.handleError = function handleError(error, context) {
  return errorHandler.handle(error, context);
};
var showError = exports.showError = function showError(error, options) {
  errorHandler.showUserError(error, options);
};
var _withErrorHandling = exports.withErrorHandling = function () {
  var _ref = (0, _asyncToGenerator2.default)(function* (operation, context, options) {
    try {
      return yield operation();
    } catch (error) {
      var appError = handleError(error, context);
      if (options != null && options.onError) {
        options.onError(appError);
      }
      if (options != null && options.showUserError) {
        showError(appError, {
          showRetry: options == null ? void 0 : options.retryable,
          onRetry: options != null && options.retryable ? function () {
            return _withErrorHandling(operation, context, options);
          } : undefined
        });
      }
      return null;
    }
  });
  return function withErrorHandling(_x2, _x3, _x4) {
    return _ref.apply(this, arguments);
  };
}();
var createNetworkError = exports.createNetworkError = function createNetworkError(message, details) {
  return new AppError(message, ErrorType.NETWORK, ErrorSeverity.MEDIUM, 'Please check your internet connection and try again.', details);
};
var createValidationError = exports.createValidationError = function createValidationError(field, message) {
  return new AppError(`Validation failed for ${field}: ${message}`, ErrorType.VALIDATION, ErrorSeverity.LOW, message, {
    field: field
  });
};
var createAIServiceError = exports.createAIServiceError = function createAIServiceError(service, details) {
  return new AppError(`AI service ${service} failed`, ErrorType.AI_SERVICE, ErrorSeverity.MEDIUM, 'AI analysis is temporarily unavailable. Please try again later.', Object.assign({
    service: service
  }, details));
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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