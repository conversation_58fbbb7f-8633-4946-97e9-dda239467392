5a3f307f273f439f3e32c295fe1dd05b
function cov_13a93lw55j() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\ui\\ProgressRing.tsx";
  var hash = "c26821ca357bb38d0f0f037d4f884d90bba7c08a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\ui\\ProgressRing.tsx",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 9,
          column: 1
        }
      },
      "1": {
        start: {
          line: 28,
          column: 17
        },
        end: {
          line: 28,
          column: 41
        }
      },
      "2": {
        start: {
          line: 29,
          column: 24
        },
        end: {
          line: 29,
          column: 44
        }
      },
      "3": {
        start: {
          line: 30,
          column: 26
        },
        end: {
          line: 30,
          column: 39
        }
      },
      "4": {
        start: {
          line: 31,
          column: 27
        },
        end: {
          line: 31,
          column: 75
        }
      },
      "5": {
        start: {
          line: 33,
          column: 2
        },
        end: {
          line: 64,
          column: 4
        }
      },
      "6": {
        start: {
          line: 67,
          column: 15
        },
        end: {
          line: 92,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "ProgressRing",
        decl: {
          start: {
            line: 20,
            column: 24
          },
          end: {
            line: 20,
            column: 36
          }
        },
        loc: {
          start: {
            line: 27,
            column: 22
          },
          end: {
            line: 65,
            column: 1
          }
        },
        line: 27
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 22,
            column: 2
          },
          end: {
            line: 22,
            column: 11
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 22,
            column: 9
          },
          end: {
            line: 22,
            column: 11
          }
        }],
        line: 22
      },
      "1": {
        loc: {
          start: {
            line: 23,
            column: 2
          },
          end: {
            line: 23,
            column: 17
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 23,
            column: 16
          },
          end: {
            line: 23,
            column: 17
          }
        }],
        line: 23
      },
      "2": {
        loc: {
          start: {
            line: 26,
            column: 2
          },
          end: {
            line: 26,
            column: 24
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 26,
            column: 10
          },
          end: {
            line: 26,
            column: 24
          }
        }],
        line: 26
      },
      "3": {
        loc: {
          start: {
            line: 60,
            column: 9
          },
          end: {
            line: 60,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 9
          },
          end: {
            line: 60,
            column: 14
          }
        }, {
          start: {
            line: 60,
            column: 18
          },
          end: {
            line: 60,
            column: 59
          }
        }],
        line: 60
      },
      "4": {
        loc: {
          start: {
            line: 61,
            column: 9
          },
          end: {
            line: 61,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 61,
            column: 9
          },
          end: {
            line: 61,
            column: 14
          }
        }, {
          start: {
            line: 61,
            column: 18
          },
          end: {
            line: 61,
            column: 59
          }
        }],
        line: 61
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c26821ca357bb38d0f0f037d4f884d90bba7c08a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_13a93lw55j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_13a93lw55j();
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Circle } from 'react-native-svg';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_13a93lw55j().s[0]++, {
  primary: '#23ba16',
  lightGray: '#e5e7eb',
  dark: '#171717'
});
export default function ProgressRing(_ref) {
  var progress = _ref.progress,
    _ref$size = _ref.size,
    size = _ref$size === void 0 ? (cov_13a93lw55j().b[0][0]++, 80) : _ref$size,
    _ref$strokeWidth = _ref.strokeWidth,
    strokeWidth = _ref$strokeWidth === void 0 ? (cov_13a93lw55j().b[1][0]++, 8) : _ref$strokeWidth,
    label = _ref.label,
    value = _ref.value,
    _ref$color = _ref.color,
    color = _ref$color === void 0 ? (cov_13a93lw55j().b[2][0]++, colors.primary) : _ref$color;
  cov_13a93lw55j().f[0]++;
  var radius = (cov_13a93lw55j().s[1]++, (size - strokeWidth) / 2);
  var circumference = (cov_13a93lw55j().s[2]++, radius * 2 * Math.PI);
  var strokeDasharray = (cov_13a93lw55j().s[3]++, circumference);
  var strokeDashoffset = (cov_13a93lw55j().s[4]++, circumference - progress / 100 * circumference);
  cov_13a93lw55j().s[5]++;
  return _jsxs(View, {
    style: [styles.container, {
      width: size,
      height: size
    }],
    children: [_jsxs(Svg, {
      width: size,
      height: size,
      style: styles.svg,
      children: [_jsx(Circle, {
        cx: size / 2,
        cy: size / 2,
        r: radius,
        stroke: colors.lightGray,
        strokeWidth: strokeWidth,
        fill: "transparent"
      }), _jsx(Circle, {
        cx: size / 2,
        cy: size / 2,
        r: radius,
        stroke: color,
        strokeWidth: strokeWidth,
        fill: "transparent",
        strokeDasharray: strokeDasharray,
        strokeDashoffset: strokeDashoffset,
        strokeLinecap: "round",
        transform: `rotate(-90 ${size / 2} ${size / 2})`
      })]
    }), _jsxs(View, {
      style: styles.content,
      children: [(cov_13a93lw55j().b[3][0]++, value) && (cov_13a93lw55j().b[3][1]++, _jsx(Text, {
        style: styles.value,
        children: value
      })), (cov_13a93lw55j().b[4][0]++, label) && (cov_13a93lw55j().b[4][1]++, _jsx(Text, {
        style: styles.label,
        children: label
      }))]
    })]
  });
}
var styles = (cov_13a93lw55j().s[6]++, StyleSheet.create({
  container: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center'
  },
  svg: {
    position: 'absolute'
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  value: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: colors.dark
  },
  label: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: colors.dark,
    opacity: 0.7,
    textAlign: 'center'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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