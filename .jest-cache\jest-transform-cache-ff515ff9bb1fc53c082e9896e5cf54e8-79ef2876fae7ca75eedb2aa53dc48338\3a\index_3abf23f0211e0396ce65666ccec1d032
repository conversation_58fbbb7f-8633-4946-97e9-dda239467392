96ecad980fc3dd5865f820758b07e67e
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _View = _interopRequireDefault(require("../../../exports/View"));
var _StyleSheet = _interopRequireDefault(require("../../../exports/StyleSheet"));
var _deepDiffer = _interopRequireDefault(require("../deepDiffer"));
var _Platform = _interopRequireDefault(require("../../../exports/Platform"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var React = _interopRequireWildcard(require("react"));
var _VirtualizedList = _interopRequireDefault(require("../VirtualizedList"));
var _VirtualizeUtils = require("../VirtualizeUtils");
var _memoizeOne = _interopRequireDefault(require("memoize-one"));
var _excluded = ["numColumns", "columnWrapperStyle", "removeClippedSubviews", "strictMode"];
function removeClippedSubviewsOrDefault(removeClippedSubviews) {
  return removeClippedSubviews !== null && removeClippedSubviews !== void 0 ? removeClippedSubviews : _Platform.default.OS === 'android';
}
function numColumnsOrDefault(numColumns) {
  return numColumns !== null && numColumns !== void 0 ? numColumns : 1;
}
function isArrayLike(data) {
  return typeof Object(data).length === 'number';
}
var FlatList = function (_React$PureComponent) {
  function FlatList(_props) {
    var _this;
    (0, _classCallCheck2.default)(this, FlatList);
    _this = _callSuper(this, FlatList, [_props]);
    _this._virtualizedListPairs = [];
    _this._captureRef = function (ref) {
      _this._listRef = ref;
    };
    _this._getItem = function (data, index) {
      var numColumns = numColumnsOrDefault(_this.props.numColumns);
      if (numColumns > 1) {
        var ret = [];
        for (var kk = 0; kk < numColumns; kk++) {
          var itemIndex = index * numColumns + kk;
          if (itemIndex < data.length) {
            var _item = data[itemIndex];
            ret.push(_item);
          }
        }
        return ret;
      } else {
        return data[index];
      }
    };
    _this._getItemCount = function (data) {
      if (data != null && isArrayLike(data)) {
        var numColumns = numColumnsOrDefault(_this.props.numColumns);
        return numColumns > 1 ? Math.ceil(data.length / numColumns) : data.length;
      } else {
        return 0;
      }
    };
    _this._keyExtractor = function (items, index) {
      var _this$props$keyExtrac;
      var numColumns = numColumnsOrDefault(_this.props.numColumns);
      var keyExtractor = (_this$props$keyExtrac = _this.props.keyExtractor) !== null && _this$props$keyExtrac !== void 0 ? _this$props$keyExtrac : _VirtualizeUtils.keyExtractor;
      if (numColumns > 1) {
        (0, _invariant.default)(Array.isArray(items), 'FlatList: Encountered internal consistency error, expected each item to consist of an ' + 'array with 1-%s columns; instead, received a single item.', numColumns);
        return items.map(function (item, kk) {
          return keyExtractor(item, index * numColumns + kk);
        }).join(':');
      }
      return keyExtractor(items, index);
    };
    _this._renderer = function (ListItemComponent, renderItem, columnWrapperStyle, numColumns, extraData) {
      var cols = numColumnsOrDefault(numColumns);
      var render = function render(props) {
        if (ListItemComponent) {
          return React.createElement(ListItemComponent, props);
        } else if (renderItem) {
          return renderItem(props);
        } else {
          return null;
        }
      };
      var renderProp = function renderProp(info) {
        if (cols > 1) {
          var _item2 = info.item,
            _index = info.index;
          (0, _invariant.default)(Array.isArray(_item2), 'Expected array of items with numColumns > 1');
          return React.createElement(_View.default, {
            style: [styles.row, columnWrapperStyle]
          }, _item2.map(function (it, kk) {
            var element = render({
              item: it,
              index: _index * cols + kk,
              separators: info.separators
            });
            return element != null ? React.createElement(React.Fragment, {
              key: kk
            }, element) : null;
          }));
        } else {
          return render(info);
        }
      };
      return ListItemComponent ? {
        ListItemComponent: renderProp
      } : {
        renderItem: renderProp
      };
    };
    _this._memoizedRenderer = (0, _memoizeOne.default)(_this._renderer);
    _this._checkProps(_this.props);
    if (_this.props.viewabilityConfigCallbackPairs) {
      _this._virtualizedListPairs = _this.props.viewabilityConfigCallbackPairs.map(function (pair) {
        return {
          viewabilityConfig: pair.viewabilityConfig,
          onViewableItemsChanged: _this._createOnViewableItemsChanged(pair.onViewableItemsChanged)
        };
      });
    } else if (_this.props.onViewableItemsChanged) {
      _this._virtualizedListPairs.push({
        viewabilityConfig: _this.props.viewabilityConfig,
        onViewableItemsChanged: _this._createOnViewableItemsChanged(_this.props.onViewableItemsChanged)
      });
    }
    return _this;
  }
  (0, _inherits2.default)(FlatList, _React$PureComponent);
  return (0, _createClass2.default)(FlatList, [{
    key: "scrollToEnd",
    value: function scrollToEnd(params) {
      if (this._listRef) {
        this._listRef.scrollToEnd(params);
      }
    }
  }, {
    key: "scrollToIndex",
    value: function scrollToIndex(params) {
      if (this._listRef) {
        this._listRef.scrollToIndex(params);
      }
    }
  }, {
    key: "scrollToItem",
    value: function scrollToItem(params) {
      if (this._listRef) {
        this._listRef.scrollToItem(params);
      }
    }
  }, {
    key: "scrollToOffset",
    value: function scrollToOffset(params) {
      if (this._listRef) {
        this._listRef.scrollToOffset(params);
      }
    }
  }, {
    key: "recordInteraction",
    value: function recordInteraction() {
      if (this._listRef) {
        this._listRef.recordInteraction();
      }
    }
  }, {
    key: "flashScrollIndicators",
    value: function flashScrollIndicators() {
      if (this._listRef) {
        this._listRef.flashScrollIndicators();
      }
    }
  }, {
    key: "getScrollResponder",
    value: function getScrollResponder() {
      if (this._listRef) {
        return this._listRef.getScrollResponder();
      }
    }
  }, {
    key: "getNativeScrollRef",
    value: function getNativeScrollRef() {
      if (this._listRef) {
        return this._listRef.getScrollRef();
      }
    }
  }, {
    key: "getScrollableNode",
    value: function getScrollableNode() {
      if (this._listRef) {
        return this._listRef.getScrollableNode();
      }
    }
  }, {
    key: "componentDidUpdate",
    value: function componentDidUpdate(prevProps) {
      (0, _invariant.default)(prevProps.numColumns === this.props.numColumns, 'Changing numColumns on the fly is not supported. Change the key prop on FlatList when ' + 'changing the number of columns to force a fresh render of the component.');
      (0, _invariant.default)(prevProps.onViewableItemsChanged === this.props.onViewableItemsChanged, 'Changing onViewableItemsChanged on the fly is not supported');
      (0, _invariant.default)(!(0, _deepDiffer.default)(prevProps.viewabilityConfig, this.props.viewabilityConfig), 'Changing viewabilityConfig on the fly is not supported');
      (0, _invariant.default)(prevProps.viewabilityConfigCallbackPairs === this.props.viewabilityConfigCallbackPairs, 'Changing viewabilityConfigCallbackPairs on the fly is not supported');
      this._checkProps(this.props);
    }
  }, {
    key: "_checkProps",
    value: function _checkProps(props) {
      var getItem = props.getItem,
        getItemCount = props.getItemCount,
        horizontal = props.horizontal,
        columnWrapperStyle = props.columnWrapperStyle,
        onViewableItemsChanged = props.onViewableItemsChanged,
        viewabilityConfigCallbackPairs = props.viewabilityConfigCallbackPairs;
      var numColumns = numColumnsOrDefault(this.props.numColumns);
      (0, _invariant.default)(!getItem && !getItemCount, 'FlatList does not support custom data formats.');
      if (numColumns > 1) {
        (0, _invariant.default)(!horizontal, 'numColumns does not support horizontal.');
      } else {
        (0, _invariant.default)(!columnWrapperStyle, 'columnWrapperStyle not supported for single column lists');
      }
      (0, _invariant.default)(!(onViewableItemsChanged && viewabilityConfigCallbackPairs), 'FlatList does not support setting both onViewableItemsChanged and ' + 'viewabilityConfigCallbackPairs.');
    }
  }, {
    key: "_pushMultiColumnViewable",
    value: function _pushMultiColumnViewable(arr, v) {
      var _this$props$keyExtrac2;
      var numColumns = numColumnsOrDefault(this.props.numColumns);
      var keyExtractor = (_this$props$keyExtrac2 = this.props.keyExtractor) !== null && _this$props$keyExtrac2 !== void 0 ? _this$props$keyExtrac2 : _VirtualizeUtils.keyExtractor;
      v.item.forEach(function (item, ii) {
        (0, _invariant.default)(v.index != null, 'Missing index!');
        var index = v.index * numColumns + ii;
        arr.push((0, _objectSpread2.default)((0, _objectSpread2.default)({}, v), {}, {
          item: item,
          key: keyExtractor(item, index),
          index: index
        }));
      });
    }
  }, {
    key: "_createOnViewableItemsChanged",
    value: function _createOnViewableItemsChanged(onViewableItemsChanged) {
      var _this2 = this;
      return function (info) {
        var numColumns = numColumnsOrDefault(_this2.props.numColumns);
        if (onViewableItemsChanged) {
          if (numColumns > 1) {
            var changed = [];
            var viewableItems = [];
            info.viewableItems.forEach(function (v) {
              return _this2._pushMultiColumnViewable(viewableItems, v);
            });
            info.changed.forEach(function (v) {
              return _this2._pushMultiColumnViewable(changed, v);
            });
            onViewableItemsChanged({
              viewableItems: viewableItems,
              changed: changed
            });
          } else {
            onViewableItemsChanged(info);
          }
        }
      };
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props = this.props,
        numColumns = _this$props.numColumns,
        columnWrapperStyle = _this$props.columnWrapperStyle,
        _removeClippedSubviews = _this$props.removeClippedSubviews,
        _this$props$strictMod = _this$props.strictMode,
        strictMode = _this$props$strictMod === void 0 ? false : _this$props$strictMod,
        restProps = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);
      var renderer = strictMode ? this._memoizedRenderer : this._renderer;
      return (React.createElement(_VirtualizedList.default, (0, _extends2.default)({}, restProps, {
          getItem: this._getItem,
          getItemCount: this._getItemCount,
          keyExtractor: this._keyExtractor,
          ref: this._captureRef,
          viewabilityConfigCallbackPairs: this._virtualizedListPairs,
          removeClippedSubviews: removeClippedSubviewsOrDefault(_removeClippedSubviews)
        }, renderer(this.props.ListItemComponent, this.props.renderItem, columnWrapperStyle, numColumns, this.props.extraData)))
      );
    }
  }]);
}(React.PureComponent);
var styles = _StyleSheet.default.create({
  row: {
    flexDirection: 'row'
  }
});
var _default = exports.default = FlatList;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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