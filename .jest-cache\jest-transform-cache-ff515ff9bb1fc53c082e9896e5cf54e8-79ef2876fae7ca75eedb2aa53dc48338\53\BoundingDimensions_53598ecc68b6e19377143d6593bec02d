c77e4aaf4b2e9e9dbd78af8fdb3dbf35
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _PooledClass = _interopRequireDefault(require("../../vendor/react-native/PooledClass"));
var twoArgumentPooler = _PooledClass.default.twoArgumentPooler;
function BoundingDimensions(width, height) {
  this.width = width;
  this.height = height;
}
BoundingDimensions.prototype.destructor = function () {
  this.width = null;
  this.height = null;
};
BoundingDimensions.getPooledFromElement = function (element) {
  return BoundingDimensions.getPooled(element.offsetWidth, element.offsetHeight);
};
_PooledClass.default.addPoolingTo(BoundingDimensions, twoArgumentPooler);
var _default = exports.default = BoundingDimensions;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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