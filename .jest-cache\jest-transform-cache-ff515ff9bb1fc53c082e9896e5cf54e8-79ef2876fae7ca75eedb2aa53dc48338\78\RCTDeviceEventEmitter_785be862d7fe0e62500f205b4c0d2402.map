{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_EventEmitter", "_default", "module"], "sources": ["RCTDeviceEventEmitter.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _EventEmitter = _interopRequireDefault(require(\"../vendor/emitter/EventEmitter\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n// FIXME: use typed events\n/**\n * Global EventEmitter used by the native platform to emit events to JavaScript.\n * Events are identified by globally unique event names.\n *\n * NativeModules that emit events should instead subclass `NativeEventEmitter`.\n */\nvar _default = exports.default = new _EventEmitter.default();\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,aAAa,GAAGL,sBAAsB,CAACC,OAAO,iCAAiC,CAAC,CAAC;AAiBrF,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAG,IAAIG,aAAa,CAACH,OAAO,CAAC,CAAC;AAC5DK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}