{"version": 3, "names": ["useEffect", "useFrameworkReady", "cov_2eqb4lkags", "f", "s", "window", "frameworkReady"], "sources": ["useFrameworkReady.ts"], "sourcesContent": ["import { useEffect } from 'react';\n\ndeclare global {\n  interface Window {\n    frameworkReady?: () => void;\n  }\n}\n\nexport function useFrameworkReady() {\n  useEffect(() => {\n    window.frameworkReady?.();\n  });\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,SAAS,QAAQ,OAAO;AAQjC,OAAO,SAASC,iBAAiBA,CAAA,EAAG;EAAAC,cAAA,GAAAC,CAAA;EAAAD,cAAA,GAAAE,CAAA;EAClCJ,SAAS,CAAC,YAAM;IAAAE,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAE,CAAA;IACdC,MAAM,CAACC,cAAc,YAArBD,MAAM,CAACC,cAAc,CAAG,CAAC;EAC3B,CAAC,CAAC;AACJ", "ignoreList": []}