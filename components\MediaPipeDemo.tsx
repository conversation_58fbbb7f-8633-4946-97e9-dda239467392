/**
 * MediaPipe Demo Component
 * Minimal working example of video input → MediaPipe pose detection → data output
 */

import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { videoProcessingService } from '@/services/videoProcessing';

interface PoseData {
  frameIndex: number;
  timestamp: number;
  confidence: number;
  movementType: string;
  technicalFeedback: string[];
}

interface AnalysisResults {
  videoMetadata: {
    duration: number;
    frameCount: number;
    resolution: { width: number; height: number };
  };
  poseData: PoseData[];
  overallAnalysis: {
    dominantMovements: string[];
    technicalScore: number;
    improvements: string[];
    strengths: string[];
  };
  aiCoachingFeedback: string;
}

export default function MediaPipeDemo() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<AnalysisResults | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleVideoUpload = async () => {
    if (typeof window === 'undefined') {
      Alert.alert('Error', 'This demo requires a web browser environment');
      return;
    }

    // Create file input for web
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'video/*';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        await processVideo(file);
      }
    };
    input.click();
  };

  const processVideo = async (videoFile: File) => {
    setIsProcessing(true);
    setError(null);
    setResults(null);

    try {
      console.log('Starting MediaPipe video processing...');
      
      // Process video with MediaPipe
      const mediaPipeResults = await videoProcessingService.processVideo(videoFile, {
        frameRate: 3, // Process 3 frames per second for demo
        maxDuration: 15, // Limit to 15 seconds for demo
        includeVisualization: false
      });

      // Convert to demo format
      const demoResults: AnalysisResults = {
        videoMetadata: mediaPipeResults.videoMetadata,
        poseData: mediaPipeResults.poseAnalysis.map(frame => ({
          frameIndex: frame.frameIndex,
          timestamp: frame.timestamp,
          confidence: frame.confidence,
          movementType: frame.movements[0]?.movementType || 'unknown',
          technicalFeedback: frame.technicalFeedback
        })),
        overallAnalysis: mediaPipeResults.overallAnalysis,
        aiCoachingFeedback: mediaPipeResults.aiCoachingFeedback
      };

      setResults(demoResults);
      console.log('MediaPipe processing completed successfully');
      
    } catch (err) {
      console.error('MediaPipe processing failed:', err);
      setError(err instanceof Error ? err.message : 'Processing failed');
    } finally {
      setIsProcessing(false);
    }
  };

  const renderResults = () => {
    if (!results) return null;

    return (
      <ScrollView style={styles.resultsContainer}>
        <Text style={styles.sectionTitle}>📹 Video Metadata</Text>
        <View style={styles.metadataContainer}>
          <Text style={styles.metadataText}>Duration: {results.videoMetadata.duration.toFixed(1)}s</Text>
          <Text style={styles.metadataText}>Frames Processed: {results.videoMetadata.frameCount}</Text>
          <Text style={styles.metadataText}>
            Resolution: {results.videoMetadata.resolution.width}x{results.videoMetadata.resolution.height}
          </Text>
        </View>

        <Text style={styles.sectionTitle}>🤖 Overall Analysis</Text>
        <View style={styles.analysisContainer}>
          <Text style={styles.scoreText}>Technical Score: {results.overallAnalysis.technicalScore}/100</Text>
          <Text style={styles.movementsText}>
            Dominant Movements: {results.overallAnalysis.dominantMovements.join(', ') || 'None detected'}
          </Text>
          <Text style={styles.improvementsText}>
            Improvements: {results.overallAnalysis.improvements.join(', ') || 'None identified'}
          </Text>
          <Text style={styles.strengthsText}>
            Strengths: {results.overallAnalysis.strengths.join(', ') || 'Analysis in progress'}
          </Text>
        </View>

        <Text style={styles.sectionTitle}>🎯 Pose Detection Data</Text>
        <View style={styles.poseDataContainer}>
          {results.poseData.slice(0, 5).map((pose, index) => (
            <View key={index} style={styles.poseFrame}>
              <Text style={styles.frameTitle}>Frame {pose.frameIndex} ({pose.timestamp.toFixed(1)}s)</Text>
              <Text style={styles.frameData}>Movement: {pose.movementType}</Text>
              <Text style={styles.frameData}>Confidence: {(pose.confidence * 100).toFixed(1)}%</Text>
              {pose.technicalFeedback.length > 0 && (
                <Text style={styles.frameData}>
                  Feedback: {pose.technicalFeedback.join(', ')}
                </Text>
              )}
            </View>
          ))}
          {results.poseData.length > 5 && (
            <Text style={styles.moreFrames}>
              ... and {results.poseData.length - 5} more frames
            </Text>
          )}
        </View>

        <Text style={styles.sectionTitle}>🧠 AI Coaching Feedback</Text>
        <View style={styles.feedbackContainer}>
          <Text style={styles.feedbackText}>{results.aiCoachingFeedback}</Text>
        </View>
      </ScrollView>
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>MediaPipe Tennis Analysis Demo</Text>
      <Text style={styles.subtitle}>
        Upload a tennis video to see real-time pose detection and AI analysis
      </Text>

      <TouchableOpacity
        style={[styles.uploadButton, isProcessing && styles.uploadButtonDisabled]}
        onPress={handleVideoUpload}
        disabled={isProcessing}
      >
        {isProcessing ? (
          <View style={styles.processingContainer}>
            <ActivityIndicator color="#fff" size="small" />
            <Text style={styles.uploadButtonText}>Processing Video...</Text>
          </View>
        ) : (
          <Text style={styles.uploadButtonText}>📹 Upload Tennis Video</Text>
        )}
      </TouchableOpacity>

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>❌ Error: {error}</Text>
        </View>
      )}

      {renderResults()}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
    lineHeight: 22,
  },
  uploadButton: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
    marginBottom: 20,
  },
  uploadButtonDisabled: {
    backgroundColor: '#999',
  },
  uploadButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  processingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  errorContainer: {
    backgroundColor: '#ffebee',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  errorText: {
    color: '#c62828',
    fontSize: 14,
  },
  resultsContainer: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 20,
    marginBottom: 10,
    color: '#333',
  },
  metadataContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  metadataText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  analysisContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  scoreText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#007AFF',
    marginBottom: 8,
  },
  movementsText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 5,
  },
  improvementsText: {
    fontSize: 14,
    color: '#ff6b35',
    marginBottom: 5,
  },
  strengthsText: {
    fontSize: 14,
    color: '#28a745',
  },
  poseDataContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  poseFrame: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingBottom: 10,
    marginBottom: 10,
  },
  frameTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  frameData: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  moreFrames: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
    textAlign: 'center',
  },
  feedbackContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  feedbackText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 20,
  },
});
