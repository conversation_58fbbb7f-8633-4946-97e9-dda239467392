0d69bec757d5dc65abd77bcf4bcbf44c
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _FlatList = _interopRequireDefault(require("../../../../exports/FlatList"));
var _createAnimatedComponent = _interopRequireDefault(require("../createAnimatedComponent"));
var FlatListWithEventThrottle = React.forwardRef(function (props, ref) {
  return React.createElement(_FlatList.default, (0, _extends2.default)({
    scrollEventThrottle: 0.0001
  }, props, {
    ref: ref
  }));
});
var _default = exports.default = (0, _createAnimatedComponent.default)(FlatListWithEventThrottle);
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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