080d91d6acc251f0ced0eec84ea44555
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _Image = _interopRequireDefault(require("../Image"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["children", "style", "imageStyle", "imageRef"];
var emptyObject = {};
var ImageBackground = (0, _react.forwardRef)(function (props, forwardedRef) {
  var children = props.children,
    _props$style = props.style,
    style = _props$style === void 0 ? emptyObject : _props$style,
    imageStyle = props.imageStyle,
    imageRef = props.imageRef,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var _StyleSheet$flatten = _StyleSheet.default.flatten(style),
    height = _StyleSheet$flatten.height,
    width = _StyleSheet$flatten.width;
  return React.createElement(_View.default, {
    ref: forwardedRef,
    style: style
  }, React.createElement(_Image.default, (0, _extends2.default)({}, rest, {
    ref: imageRef,
    style: [{
      width: width,
      height: height,
      zIndex: -1
    }, _StyleSheet.default.absoluteFill, imageStyle]
  })), children);
});
ImageBackground.displayName = 'ImageBackground';
var _default = exports.default = ImageBackground;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiX2V4dGVuZHMyIiwiX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UyIiwiX3JlYWN0IiwiUmVhY3QiLCJfSW1hZ2UiLCJfU3R5bGVTaGVldCIsIl9WaWV3IiwiX2V4Y2x1ZGVkIiwiZW1wdHlPYmplY3QiLCJJbWFnZUJhY2tncm91bmQiLCJmb3J3YXJkUmVmIiwicHJvcHMiLCJmb3J3YXJkZWRSZWYiLCJjaGlsZHJlbiIsIl9wcm9wcyRzdHlsZSIsInN0eWxlIiwiaW1hZ2VTdHlsZSIsImltYWdlUmVmIiwicmVzdCIsIl9TdHlsZVNoZWV0JGZsYXR0ZW4iLCJmbGF0dGVuIiwiaGVpZ2h0Iiwid2lkdGgiLCJjcmVhdGVFbGVtZW50IiwicmVmIiwiekluZGV4IiwiYWJzb2x1dGVGaWxsIiwiZGlzcGxheU5hbWUiLCJfZGVmYXVsdCIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbImluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG52YXIgX2ludGVyb3BSZXF1aXJlRGVmYXVsdCA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdFwiKS5kZWZhdWx0O1xudmFyIF9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVXaWxkY2FyZFwiKS5kZWZhdWx0O1xuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDtcbnZhciBfZXh0ZW5kczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2V4dGVuZHNcIikpO1xudmFyIF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZVwiKSk7XG52YXIgX3JlYWN0ID0gX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQocmVxdWlyZShcInJlYWN0XCIpKTtcbnZhciBSZWFjdCA9IF9yZWFjdDtcbnZhciBfSW1hZ2UgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi9JbWFnZVwiKSk7XG52YXIgX1N0eWxlU2hlZXQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi9TdHlsZVNoZWV0XCIpKTtcbnZhciBfVmlldyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uL1ZpZXdcIikpO1xudmFyIF9leGNsdWRlZCA9IFtcImNoaWxkcmVuXCIsIFwic3R5bGVcIiwgXCJpbWFnZVN0eWxlXCIsIFwiaW1hZ2VSZWZcIl07XG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKi9cbnZhciBlbXB0eU9iamVjdCA9IHt9O1xuXG4vKipcbiAqIFZlcnkgc2ltcGxlIGRyb3AtaW4gcmVwbGFjZW1lbnQgZm9yIDxJbWFnZT4gd2hpY2ggc3VwcG9ydHMgbmVzdGluZyB2aWV3cy5cbiAqL1xudmFyIEltYWdlQmFja2dyb3VuZCA9IC8qI19fUFVSRV9fKi8oMCwgX3JlYWN0LmZvcndhcmRSZWYpKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIHZhciBjaGlsZHJlbiA9IHByb3BzLmNoaWxkcmVuLFxuICAgIF9wcm9wcyRzdHlsZSA9IHByb3BzLnN0eWxlLFxuICAgIHN0eWxlID0gX3Byb3BzJHN0eWxlID09PSB2b2lkIDAgPyBlbXB0eU9iamVjdCA6IF9wcm9wcyRzdHlsZSxcbiAgICBpbWFnZVN0eWxlID0gcHJvcHMuaW1hZ2VTdHlsZSxcbiAgICBpbWFnZVJlZiA9IHByb3BzLmltYWdlUmVmLFxuICAgIHJlc3QgPSAoMCwgX29iamVjdFdpdGhvdXRQcm9wZXJ0aWVzTG9vc2UyLmRlZmF1bHQpKHByb3BzLCBfZXhjbHVkZWQpO1xuICB2YXIgX1N0eWxlU2hlZXQkZmxhdHRlbiA9IF9TdHlsZVNoZWV0LmRlZmF1bHQuZmxhdHRlbihzdHlsZSksXG4gICAgaGVpZ2h0ID0gX1N0eWxlU2hlZXQkZmxhdHRlbi5oZWlnaHQsXG4gICAgd2lkdGggPSBfU3R5bGVTaGVldCRmbGF0dGVuLndpZHRoO1xuICByZXR1cm4gLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoX1ZpZXcuZGVmYXVsdCwge1xuICAgIHJlZjogZm9yd2FyZGVkUmVmLFxuICAgIHN0eWxlOiBzdHlsZVxuICB9LCAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChfSW1hZ2UuZGVmYXVsdCwgKDAsIF9leHRlbmRzMi5kZWZhdWx0KSh7fSwgcmVzdCwge1xuICAgIHJlZjogaW1hZ2VSZWYsXG4gICAgc3R5bGU6IFt7XG4gICAgICAvLyBUZW1wb3JhcnkgV29ya2Fyb3VuZDpcbiAgICAgIC8vIEN1cnJlbnQgKGltcGVyZmVjdCB5ZXQpIGltcGxlbWVudGF0aW9uIG9mIDxJbWFnZT4gb3ZlcndyaXRlcyB3aWR0aCBhbmQgaGVpZ2h0IHN0eWxlc1xuICAgICAgLy8gKHdoaWNoIGlzIG5vdCBxdWl0ZSBjb3JyZWN0KSwgYW5kIHRoZXNlIHN0eWxlcyBjb25mbGljdCB3aXRoIGV4cGxpY2l0bHkgc2V0IHN0eWxlc1xuICAgICAgLy8gb2YgPEltYWdlQmFja2dyb3VuZD4gYW5kIHdpdGggb3VyIGludGVybmFsIGxheW91dCBtb2RlbCBoZXJlLlxuICAgICAgLy8gU28sIHdlIGhhdmUgdG8gcHJveHkvcmVhcHBseSB0aGVzZSBzdHlsZXMgZXhwbGljaXRseSBmb3IgYWN0dWFsIDxJbWFnZT4gY29tcG9uZW50LlxuICAgICAgLy8gVGhpcyB3b3JrYXJvdW5kIHNob3VsZCBiZSByZW1vdmVkIGFmdGVyIGltcGxlbWVudGluZyBwcm9wZXIgc3VwcG9ydCBvZlxuICAgICAgLy8gaW50cmluc2ljIGNvbnRlbnQgc2l6ZSBvZiB0aGUgPEltYWdlPi5cbiAgICAgIHdpZHRoLFxuICAgICAgaGVpZ2h0LFxuICAgICAgekluZGV4OiAtMVxuICAgIH0sIF9TdHlsZVNoZWV0LmRlZmF1bHQuYWJzb2x1dGVGaWxsLCBpbWFnZVN0eWxlXVxuICB9KSksIGNoaWxkcmVuKTtcbn0pO1xuSW1hZ2VCYWNrZ3JvdW5kLmRpc3BsYXlOYW1lID0gJ0ltYWdlQmFja2dyb3VuZCc7XG52YXIgX2RlZmF1bHQgPSBleHBvcnRzLmRlZmF1bHQgPSBJbWFnZUJhY2tncm91bmQ7XG5tb2R1bGUuZXhwb3J0cyA9IGV4cG9ydHMuZGVmYXVsdDsiXSwibWFwcGluZ3MiOiJBQUFBLFlBQVk7O0FBRVosSUFBSUEsc0JBQXNCLEdBQUdDLE9BQU8sQ0FBQyw4Q0FBOEMsQ0FBQyxDQUFDQyxPQUFPO0FBQzVGLElBQUlDLHVCQUF1QixHQUFHRixPQUFPLENBQUMsK0NBQStDLENBQUMsQ0FBQ0MsT0FBTztBQUM5RkUsT0FBTyxDQUFDQyxVQUFVLEdBQUcsSUFBSTtBQUN6QkQsT0FBTyxDQUFDRixPQUFPLEdBQUcsS0FBSyxDQUFDO0FBQ3hCLElBQUlJLFNBQVMsR0FBR04sc0JBQXNCLENBQUNDLE9BQU8sQ0FBQyxnQ0FBZ0MsQ0FBQyxDQUFDO0FBQ2pGLElBQUlNLDhCQUE4QixHQUFHUCxzQkFBc0IsQ0FBQ0MsT0FBTyxDQUFDLHFEQUFxRCxDQUFDLENBQUM7QUFDM0gsSUFBSU8sTUFBTSxHQUFHTCx1QkFBdUIsQ0FBQ0YsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDO0FBQ3RELElBQUlRLEtBQUssR0FBR0QsTUFBTTtBQUNsQixJQUFJRSxNQUFNLEdBQUdWLHNCQUFzQixDQUFDQyxPQUFPLFdBQVcsQ0FBQyxDQUFDO0FBQ3hELElBQUlVLFdBQVcsR0FBR1gsc0JBQXNCLENBQUNDLE9BQU8sZ0JBQWdCLENBQUMsQ0FBQztBQUNsRSxJQUFJVyxLQUFLLEdBQUdaLHNCQUFzQixDQUFDQyxPQUFPLFVBQVUsQ0FBQyxDQUFDO0FBQ3RELElBQUlZLFNBQVMsR0FBRyxDQUFDLFVBQVUsRUFBRSxPQUFPLEVBQUUsWUFBWSxFQUFFLFVBQVUsQ0FBQztBQVMvRCxJQUFJQyxXQUFXLEdBQUcsQ0FBQyxDQUFDO0FBS3BCLElBQUlDLGVBQWUsR0FBZ0IsQ0FBQyxDQUFDLEVBQUVQLE1BQU0sQ0FBQ1EsVUFBVSxFQUFFLFVBQUNDLEtBQUssRUFBRUMsWUFBWSxFQUFLO0VBQ2pGLElBQUlDLFFBQVEsR0FBR0YsS0FBSyxDQUFDRSxRQUFRO0lBQzNCQyxZQUFZLEdBQUdILEtBQUssQ0FBQ0ksS0FBSztJQUMxQkEsS0FBSyxHQUFHRCxZQUFZLEtBQUssS0FBSyxDQUFDLEdBQUdOLFdBQVcsR0FBR00sWUFBWTtJQUM1REUsVUFBVSxHQUFHTCxLQUFLLENBQUNLLFVBQVU7SUFDN0JDLFFBQVEsR0FBR04sS0FBSyxDQUFDTSxRQUFRO0lBQ3pCQyxJQUFJLEdBQUcsQ0FBQyxDQUFDLEVBQUVqQiw4QkFBOEIsQ0FBQ0wsT0FBTyxFQUFFZSxLQUFLLEVBQUVKLFNBQVMsQ0FBQztFQUN0RSxJQUFJWSxtQkFBbUIsR0FBR2QsV0FBVyxDQUFDVCxPQUFPLENBQUN3QixPQUFPLENBQUNMLEtBQUssQ0FBQztJQUMxRE0sTUFBTSxHQUFHRixtQkFBbUIsQ0FBQ0UsTUFBTTtJQUNuQ0MsS0FBSyxHQUFHSCxtQkFBbUIsQ0FBQ0csS0FBSztFQUNuQyxPQUFvQm5CLEtBQUssQ0FBQ29CLGFBQWEsQ0FBQ2pCLEtBQUssQ0FBQ1YsT0FBTyxFQUFFO0lBQ3JENEIsR0FBRyxFQUFFWixZQUFZO0lBQ2pCRyxLQUFLLEVBQUVBO0VBQ1QsQ0FBQyxFQUFlWixLQUFLLENBQUNvQixhQUFhLENBQUNuQixNQUFNLENBQUNSLE9BQU8sRUFBRSxDQUFDLENBQUMsRUFBRUksU0FBUyxDQUFDSixPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVzQixJQUFJLEVBQUU7SUFDbkZNLEdBQUcsRUFBRVAsUUFBUTtJQUNiRixLQUFLLEVBQUUsQ0FBQztNQVFOTyxLQUFLLEVBQUxBLEtBQUs7TUFDTEQsTUFBTSxFQUFOQSxNQUFNO01BQ05JLE1BQU0sRUFBRSxDQUFDO0lBQ1gsQ0FBQyxFQUFFcEIsV0FBVyxDQUFDVCxPQUFPLENBQUM4QixZQUFZLEVBQUVWLFVBQVU7RUFDakQsQ0FBQyxDQUFDLENBQUMsRUFBRUgsUUFBUSxDQUFDO0FBQ2hCLENBQUMsQ0FBQztBQUNGSixlQUFlLENBQUNrQixXQUFXLEdBQUcsaUJBQWlCO0FBQy9DLElBQUlDLFFBQVEsR0FBRzlCLE9BQU8sQ0FBQ0YsT0FBTyxHQUFHYSxlQUFlO0FBQ2hEb0IsTUFBTSxDQUFDL0IsT0FBTyxHQUFHQSxPQUFPLENBQUNGLE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=