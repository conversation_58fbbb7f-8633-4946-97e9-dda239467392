1c0f8b80fca1d473c40d65112895a320
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var React = _interopRequireWildcard(require("react"));
var StateSafePureComponent = function (_React$PureComponent) {
  function StateSafePureComponent(props) {
    var _this;
    (0, _classCallCheck2.default)(this, StateSafePureComponent);
    _this = _callSuper(this, StateSafePureComponent, [props]);
    _this._inAsyncStateUpdate = false;
    _this._installSetStateHooks();
    return _this;
  }
  (0, _inherits2.default)(StateSafePureComponent, _React$PureComponent);
  return (0, _createClass2.default)(StateSafePureComponent, [{
    key: "setState",
    value: function setState(partialState, callback) {
      var _this2 = this;
      if (typeof partialState === 'function') {
        _superPropGet(StateSafePureComponent, "setState", this, 3)([function (state, props) {
          _this2._inAsyncStateUpdate = true;
          var ret;
          try {
            ret = partialState(state, props);
          } catch (err) {
            throw err;
          } finally {
            _this2._inAsyncStateUpdate = false;
          }
          return ret;
        }, callback]);
      } else {
        _superPropGet(StateSafePureComponent, "setState", this, 3)([partialState, callback]);
      }
    }
  }, {
    key: "_installSetStateHooks",
    value: function _installSetStateHooks() {
      var that = this;
      var props = this.props,
        state = this.state;
      Object.defineProperty(this, 'props', {
        get: function get() {
          (0, _invariant.default)(!that._inAsyncStateUpdate, '"this.props" should not be accessed during state updates');
          return props;
        },
        set: function set(newProps) {
          props = newProps;
        }
      });
      Object.defineProperty(this, 'state', {
        get: function get() {
          (0, _invariant.default)(!that._inAsyncStateUpdate, '"this.state" should not be acceessed during state updates');
          return state;
        },
        set: function set(newState) {
          state = newState;
        }
      });
    }
  }]);
}(React.PureComponent);
exports.default = StateSafePureComponent;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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