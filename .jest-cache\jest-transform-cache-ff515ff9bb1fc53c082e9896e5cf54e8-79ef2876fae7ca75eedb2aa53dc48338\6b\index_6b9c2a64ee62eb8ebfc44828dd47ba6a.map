{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_invariant", "_canUseDom", "dimensions", "window", "fontScale", "height", "scale", "width", "screen", "listeners", "shouldInit", "update", "win", "visualViewport", "Math", "round", "docEl", "document", "documentElement", "clientHeight", "clientWidth", "devicePixelRatio", "handleResize", "Array", "isArray", "for<PERSON>ach", "handler", "Dimensions", "key", "value", "get", "dimension", "set", "initialDimensions", "addEventListener", "type", "_this", "push", "remove", "removeEventListener", "filter", "_handler", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _canUseDom = _interopRequireDefault(require(\"../../modules/canUseDom\"));\nvar dimensions = {\n  window: {\n    fontScale: 1,\n    height: 0,\n    scale: 1,\n    width: 0\n  },\n  screen: {\n    fontScale: 1,\n    height: 0,\n    scale: 1,\n    width: 0\n  }\n};\nvar listeners = {};\nvar shouldInit = _canUseDom.default;\nfunction update() {\n  if (!_canUseDom.default) {\n    return;\n  }\n  var win = window;\n  var height;\n  var width;\n\n  /**\n   * iOS does not update viewport dimensions on keyboard open/close.\n   * window.visualViewport(https://developer.mozilla.org/en-US/docs/Web/API/VisualViewport)\n   * is used instead of document.documentElement.clientHeight (which remains as a fallback)\n   */\n  if (win.visualViewport) {\n    var visualViewport = win.visualViewport;\n    /**\n     * We are multiplying by scale because height and width from visual viewport\n     * also react to pinch zoom, and become smaller when zoomed. But it is not desired\n     * behaviour, since originally documentElement client height and width were used,\n     * and they do not react to pinch zoom.\n     */\n    height = Math.round(visualViewport.height * visualViewport.scale);\n    width = Math.round(visualViewport.width * visualViewport.scale);\n  } else {\n    var docEl = win.document.documentElement;\n    height = docEl.clientHeight;\n    width = docEl.clientWidth;\n  }\n  dimensions.window = {\n    fontScale: 1,\n    height,\n    scale: win.devicePixelRatio || 1,\n    width\n  };\n  dimensions.screen = {\n    fontScale: 1,\n    height: win.screen.height,\n    scale: win.devicePixelRatio || 1,\n    width: win.screen.width\n  };\n}\nfunction handleResize() {\n  update();\n  if (Array.isArray(listeners['change'])) {\n    listeners['change'].forEach(handler => handler(dimensions));\n  }\n}\nclass Dimensions {\n  static get(dimension) {\n    if (shouldInit) {\n      shouldInit = false;\n      update();\n    }\n    (0, _invariant.default)(dimensions[dimension], \"No dimension set for key \" + dimension);\n    return dimensions[dimension];\n  }\n  static set(initialDimensions) {\n    if (initialDimensions) {\n      if (_canUseDom.default) {\n        (0, _invariant.default)(false, 'Dimensions cannot be set in the browser');\n      } else {\n        if (initialDimensions.screen != null) {\n          dimensions.screen = initialDimensions.screen;\n        }\n        if (initialDimensions.window != null) {\n          dimensions.window = initialDimensions.window;\n        }\n      }\n    }\n  }\n  static addEventListener(type, handler) {\n    listeners[type] = listeners[type] || [];\n    listeners[type].push(handler);\n    return {\n      remove: () => {\n        this.removeEventListener(type, handler);\n      }\n    };\n  }\n  static removeEventListener(type, handler) {\n    if (Array.isArray(listeners[type])) {\n      listeners[type] = listeners[type].filter(_handler => _handler !== handler);\n    }\n  }\n}\nexports.default = Dimensions;\nif (_canUseDom.default) {\n  if (window.visualViewport) {\n    window.visualViewport.addEventListener('resize', handleResize, false);\n  } else {\n    window.addEventListener('resize', handleResize, false);\n  }\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,UAAU,GAAGJ,sBAAsB,CAACH,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIQ,UAAU,GAAGL,sBAAsB,CAACH,OAAO,0BAA0B,CAAC,CAAC;AAC3E,IAAIS,UAAU,GAAG;EACfC,MAAM,EAAE;IACNC,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE;EACT,CAAC;EACDC,MAAM,EAAE;IACNJ,SAAS,EAAE,CAAC;IACZC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE;EACT;AACF,CAAC;AACD,IAAIE,SAAS,GAAG,CAAC,CAAC;AAClB,IAAIC,UAAU,GAAGT,UAAU,CAACJ,OAAO;AACnC,SAASc,MAAMA,CAAA,EAAG;EAChB,IAAI,CAACV,UAAU,CAACJ,OAAO,EAAE;IACvB;EACF;EACA,IAAIe,GAAG,GAAGT,MAAM;EAChB,IAAIE,MAAM;EACV,IAAIE,KAAK;EAOT,IAAIK,GAAG,CAACC,cAAc,EAAE;IACtB,IAAIA,cAAc,GAAGD,GAAG,CAACC,cAAc;IAOvCR,MAAM,GAAGS,IAAI,CAACC,KAAK,CAACF,cAAc,CAACR,MAAM,GAAGQ,cAAc,CAACP,KAAK,CAAC;IACjEC,KAAK,GAAGO,IAAI,CAACC,KAAK,CAACF,cAAc,CAACN,KAAK,GAAGM,cAAc,CAACP,KAAK,CAAC;EACjE,CAAC,MAAM;IACL,IAAIU,KAAK,GAAGJ,GAAG,CAACK,QAAQ,CAACC,eAAe;IACxCb,MAAM,GAAGW,KAAK,CAACG,YAAY;IAC3BZ,KAAK,GAAGS,KAAK,CAACI,WAAW;EAC3B;EACAlB,UAAU,CAACC,MAAM,GAAG;IAClBC,SAAS,EAAE,CAAC;IACZC,MAAM,EAANA,MAAM;IACNC,KAAK,EAAEM,GAAG,CAACS,gBAAgB,IAAI,CAAC;IAChCd,KAAK,EAALA;EACF,CAAC;EACDL,UAAU,CAACM,MAAM,GAAG;IAClBJ,SAAS,EAAE,CAAC;IACZC,MAAM,EAAEO,GAAG,CAACJ,MAAM,CAACH,MAAM;IACzBC,KAAK,EAAEM,GAAG,CAACS,gBAAgB,IAAI,CAAC;IAChCd,KAAK,EAAEK,GAAG,CAACJ,MAAM,CAACD;EACpB,CAAC;AACH;AACA,SAASe,YAAYA,CAAA,EAAG;EACtBX,MAAM,CAAC,CAAC;EACR,IAAIY,KAAK,CAACC,OAAO,CAACf,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE;IACtCA,SAAS,CAAC,QAAQ,CAAC,CAACgB,OAAO,CAAC,UAAAC,OAAO;MAAA,OAAIA,OAAO,CAACxB,UAAU,CAAC;IAAA,EAAC;EAC7D;AACF;AAAC,IACKyB,UAAU;EAAA,SAAAA,WAAA;IAAA,IAAAjC,gBAAA,CAAAG,OAAA,QAAA8B,UAAA;EAAA;EAAA,WAAAhC,aAAA,CAAAE,OAAA,EAAA8B,UAAA;IAAAC,GAAA;IAAAC,KAAA,EACd,SAAOC,GAAGA,CAACC,SAAS,EAAE;MACpB,IAAIrB,UAAU,EAAE;QACdA,UAAU,GAAG,KAAK;QAClBC,MAAM,CAAC,CAAC;MACV;MACA,CAAC,CAAC,EAAEX,UAAU,CAACH,OAAO,EAAEK,UAAU,CAAC6B,SAAS,CAAC,EAAE,2BAA2B,GAAGA,SAAS,CAAC;MACvF,OAAO7B,UAAU,CAAC6B,SAAS,CAAC;IAC9B;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAOG,GAAGA,CAACC,iBAAiB,EAAE;MAC5B,IAAIA,iBAAiB,EAAE;QACrB,IAAIhC,UAAU,CAACJ,OAAO,EAAE;UACtB,CAAC,CAAC,EAAEG,UAAU,CAACH,OAAO,EAAE,KAAK,EAAE,yCAAyC,CAAC;QAC3E,CAAC,MAAM;UACL,IAAIoC,iBAAiB,CAACzB,MAAM,IAAI,IAAI,EAAE;YACpCN,UAAU,CAACM,MAAM,GAAGyB,iBAAiB,CAACzB,MAAM;UAC9C;UACA,IAAIyB,iBAAiB,CAAC9B,MAAM,IAAI,IAAI,EAAE;YACpCD,UAAU,CAACC,MAAM,GAAG8B,iBAAiB,CAAC9B,MAAM;UAC9C;QACF;MACF;IACF;EAAC;IAAAyB,GAAA;IAAAC,KAAA,EACD,SAAOK,gBAAgBA,CAACC,IAAI,EAAET,OAAO,EAAE;MAAA,IAAAU,KAAA;MACrC3B,SAAS,CAAC0B,IAAI,CAAC,GAAG1B,SAAS,CAAC0B,IAAI,CAAC,IAAI,EAAE;MACvC1B,SAAS,CAAC0B,IAAI,CAAC,CAACE,IAAI,CAACX,OAAO,CAAC;MAC7B,OAAO;QACLY,MAAM,EAAE,SAARA,MAAMA,CAAA,EAAQ;UACZF,KAAI,CAACG,mBAAmB,CAACJ,IAAI,EAAET,OAAO,CAAC;QACzC;MACF,CAAC;IACH;EAAC;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAOU,mBAAmBA,CAACJ,IAAI,EAAET,OAAO,EAAE;MACxC,IAAIH,KAAK,CAACC,OAAO,CAACf,SAAS,CAAC0B,IAAI,CAAC,CAAC,EAAE;QAClC1B,SAAS,CAAC0B,IAAI,CAAC,GAAG1B,SAAS,CAAC0B,IAAI,CAAC,CAACK,MAAM,CAAC,UAAAC,QAAQ;UAAA,OAAIA,QAAQ,KAAKf,OAAO;QAAA,EAAC;MAC5E;IACF;EAAC;AAAA;AAEH5B,OAAO,CAACD,OAAO,GAAG8B,UAAU;AAC5B,IAAI1B,UAAU,CAACJ,OAAO,EAAE;EACtB,IAAIM,MAAM,CAACU,cAAc,EAAE;IACzBV,MAAM,CAACU,cAAc,CAACqB,gBAAgB,CAAC,QAAQ,EAAEZ,YAAY,EAAE,KAAK,CAAC;EACvE,CAAC,MAAM;IACLnB,MAAM,CAAC+B,gBAAgB,CAAC,QAAQ,EAAEZ,YAAY,EAAE,KAAK,CAAC;EACxD;AACF;AACAoB,MAAM,CAAC5C,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}