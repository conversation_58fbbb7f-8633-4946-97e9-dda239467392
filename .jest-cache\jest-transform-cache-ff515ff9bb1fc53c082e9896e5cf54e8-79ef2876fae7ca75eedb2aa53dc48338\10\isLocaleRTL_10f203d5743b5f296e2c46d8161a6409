378085599b6f07c92c03b4b0618ae52d
"use strict";

exports.__esModule = true;
exports.isLocaleRTL = isLocaleRTL;
var rtlScripts = new Set(['Arab', 'Syrc', 'Samr', 'Mand', 'Thaa', 'Mend', 'Nkoo', 'Adlm', 'Rohg', 'Hebr']);
var rtlLangs = new Set(['ae', 'ar', 'arc', 'bcc', 'bqi', 'ckb', 'dv', 'fa', 'far', 'glk', 'he', 'iw', 'khw', 'ks', 'ku', 'mzn', 'nqo', 'pnb', 'ps', 'sd', 'ug', 'ur', 'yi']);
var cache = new Map();
function isLocaleRTL(locale) {
  var cachedRTL = cache.get(locale);
  if (cachedRTL) {
    return cachedRTL;
  }
  var isRTL = false;
  if (Intl.Locale) {
    try {
      var script = new Intl.Locale(locale).maximize().script;
      isRTL = rtlScripts.has(script);
    } catch (_unused) {
      var lang = locale.split('-')[0];
      isRTL = rtlLangs.has(lang);
    }
  } else {
    var _lang = locale.split('-')[0];
    isRTL = rtlLangs.has(_lang);
  }
  cache.set(locale, isRTL);
  return isRTL;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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