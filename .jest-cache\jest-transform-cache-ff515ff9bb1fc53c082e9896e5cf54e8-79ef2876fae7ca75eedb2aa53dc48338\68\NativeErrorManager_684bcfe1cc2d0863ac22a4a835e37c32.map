{"version": 3, "names": ["_NativeModulesProxy", "_interopRequireDefault", "require", "_default", "exports", "default", "NativeModulesProxy", "ExpoModulesCoreErrorManager"], "sources": ["NativeErrorManager.ts"], "sourcesContent": ["import NativeModulesProxy from '../NativeModulesProxy';\nexport default NativeModulesProxy.ExpoModulesCoreErrorManager;\n"], "mappings": ";;;;;AAAA,IAAAA,mBAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAuD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GACxCC,2BAAkB,CAACC,2BAA2B", "ignoreList": []}