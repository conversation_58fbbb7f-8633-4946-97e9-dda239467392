51f951bfdd2d179bdbb8aea64a230c54
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_d4vyd1l1l() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\match-recording.tsx";
  var hash = "fa93987066a58f9183060c22aa18fb17d154a8e7";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\match-recording.tsx",
    statementMap: {
      "0": {
        start: {
          line: 46,
          column: 28
        },
        end: {
          line: 46,
          column: 37
        }
      },
      "1": {
        start: {
          line: 47,
          column: 42
        },
        end: {
          line: 47,
          column: 54
        }
      },
      "2": {
        start: {
          line: 48,
          column: 42
        },
        end: {
          line: 48,
          column: 57
        }
      },
      "3": {
        start: {
          line: 49,
          column: 30
        },
        end: {
          line: 49,
          column: 45
        }
      },
      "4": {
        start: {
          line: 51,
          column: 38
        },
        end: {
          line: 69,
          column: 4
        }
      },
      "5": {
        start: {
          line: 71,
          column: 21
        },
        end: {
          line: 83,
          column: 3
        }
      },
      "6": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 75,
          column: 5
        }
      },
      "7": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 57
        }
      },
      "8": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 74,
          column: 13
        }
      },
      "9": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 81,
          column: 8
        }
      },
      "10": {
        start: {
          line: 77,
          column: 27
        },
        end: {
          line: 81,
          column: 5
        }
      },
      "11": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 82,
          column: 26
        }
      },
      "12": {
        start: {
          line: 85,
          column: 19
        },
        end: {
          line: 121,
          column: 3
        }
      },
      "13": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 120,
          column: 7
        }
      },
      "14": {
        start: {
          line: 87,
          column: 23
        },
        end: {
          line: 87,
          column: 34
        }
      },
      "15": {
        start: {
          line: 88,
          column: 28
        },
        end: {
          line: 88,
          column: 44
        }
      },
      "16": {
        start: {
          line: 89,
          column: 23
        },
        end: {
          line: 89,
          column: 81
        }
      },
      "17": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 34
        }
      },
      "18": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 117,
          column: 7
        }
      },
      "19": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 97,
          column: 35
        }
      },
      "20": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 98,
          column: 38
        }
      },
      "21": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 33
        }
      },
      "22": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 116,
          column: 9
        }
      },
      "23": {
        start: {
          line: 104,
          column: 10
        },
        end: {
          line: 104,
          column: 60
        }
      },
      "24": {
        start: {
          line: 105,
          column: 10
        },
        end: {
          line: 105,
          column: 50
        }
      },
      "25": {
        start: {
          line: 106,
          column: 10
        },
        end: {
          line: 106,
          column: 39
        }
      },
      "26": {
        start: {
          line: 107,
          column: 10
        },
        end: {
          line: 107,
          column: 34
        }
      },
      "27": {
        start: {
          line: 110,
          column: 29
        },
        end: {
          line: 110,
          column: 73
        }
      },
      "28": {
        start: {
          line: 111,
          column: 10
        },
        end: {
          line: 115,
          column: 11
        }
      },
      "29": {
        start: {
          line: 112,
          column: 12
        },
        end: {
          line: 112,
          column: 44
        }
      },
      "30": {
        start: {
          line: 113,
          column: 12
        },
        end: {
          line: 113,
          column: 49
        }
      },
      "31": {
        start: {
          line: 114,
          column: 12
        },
        end: {
          line: 114,
          column: 42
        }
      },
      "32": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 22
        }
      },
      "33": {
        start: {
          line: 123,
          column: 21
        },
        end: {
          line: 156,
          column: 3
        }
      },
      "34": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 155,
          column: 6
        }
      },
      "35": {
        start: {
          line: 133,
          column: 12
        },
        end: {
          line: 151,
          column: 15
        }
      },
      "36": {
        start: {
          line: 158,
          column: 20
        },
        end: {
          line: 221,
          column: 3
        }
      },
      "37": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 162,
          column: 5
        }
      },
      "38": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 56
        }
      },
      "39": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 161,
          column: 13
        }
      },
      "40": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 164,
          column: 20
        }
      },
      "41": {
        start: {
          line: 165,
          column: 4
        },
        end: {
          line: 220,
          column: 5
        }
      },
      "42": {
        start: {
          line: 167,
          column: 23
        },
        end: {
          line: 169,
          column: 11
        }
      },
      "43": {
        start: {
          line: 172,
          column: 26
        },
        end: {
          line: 172,
          column: 56
        }
      },
      "44": {
        start: {
          line: 173,
          column: 26
        },
        end: {
          line: 173,
          column: 56
        }
      },
      "45": {
        start: {
          line: 174,
          column: 21
        },
        end: {
          line: 174,
          column: 63
        }
      },
      "46": {
        start: {
          line: 177,
          column: 25
        },
        end: {
          line: 177,
          column: 103
        }
      },
      "47": {
        start: {
          line: 180,
          column: 30
        },
        end: {
          line: 193,
          column: 8
        }
      },
      "48": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 197,
          column: 7
        }
      },
      "49": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 196,
          column: 31
        }
      },
      "50": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 215,
          column: 8
        }
      },
      "51": {
        start: {
          line: 206,
          column: 14
        },
        end: {
          line: 206,
          column: 28
        }
      },
      "52": {
        start: {
          line: 207,
          column: 14
        },
        end: {
          line: 207,
          column: 46
        }
      },
      "53": {
        start: {
          line: 212,
          column: 27
        },
        end: {
          line: 212,
          column: 40
        }
      },
      "54": {
        start: {
          line: 217,
          column: 6
        },
        end: {
          line: 217,
          column: 70
        }
      },
      "55": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 23
        }
      },
      "56": {
        start: {
          line: 223,
          column: 22
        },
        end: {
          line: 233,
          column: 3
        }
      },
      "57": {
        start: {
          line: 224,
          column: 22
        },
        end: {
          line: 224,
          column: 40
        }
      },
      "58": {
        start: {
          line: 225,
          column: 21
        },
        end: {
          line: 225,
          column: 38
        }
      },
      "59": {
        start: {
          line: 226,
          column: 20
        },
        end: {
          line: 226,
          column: 31
        }
      },
      "60": {
        start: {
          line: 228,
          column: 4
        },
        end: {
          line: 232,
          column: 6
        }
      },
      "61": {
        start: {
          line: 235,
          column: 2
        },
        end: {
          line: 300,
          column: 3
        }
      },
      "62": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 299,
          column: 6
        }
      },
      "63": {
        start: {
          line: 243,
          column: 45
        },
        end: {
          line: 243,
          column: 58
        }
      },
      "64": {
        start: {
          line: 272,
          column: 18
        },
        end: {
          line: 288,
          column: 37
        }
      },
      "65": {
        start: {
          line: 278,
          column: 35
        },
        end: {
          line: 278,
          column: 99
        }
      },
      "66": {
        start: {
          line: 278,
          column: 58
        },
        end: {
          line: 278,
          column: 97
        }
      },
      "67": {
        start: {
          line: 302,
          column: 2
        },
        end: {
          line: 397,
          column: 4
        }
      },
      "68": {
        start: {
          line: 309,
          column: 43
        },
        end: {
          line: 309,
          column: 56
        }
      },
      "69": {
        start: {
          line: 343,
          column: 31
        },
        end: {
          line: 343,
          column: 50
        }
      },
      "70": {
        start: {
          line: 360,
          column: 31
        },
        end: {
          line: 360,
          column: 50
        }
      },
      "71": {
        start: {
          line: 400,
          column: 15
        },
        end: {
          line: 637,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "MatchRecordingScreen",
        decl: {
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 44
          }
        },
        loc: {
          start: {
            line: 45,
            column: 47
          },
          end: {
            line: 398,
            column: 1
          }
        },
        line: 45
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 71,
            column: 21
          },
          end: {
            line: 71,
            column: 22
          }
        },
        loc: {
          start: {
            line: 71,
            column: 27
          },
          end: {
            line: 83,
            column: 3
          }
        },
        line: 71
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 77,
            column: 18
          },
          end: {
            line: 77,
            column: 19
          }
        },
        loc: {
          start: {
            line: 77,
            column: 27
          },
          end: {
            line: 81,
            column: 5
          }
        },
        line: 77
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 85,
            column: 19
          },
          end: {
            line: 85,
            column: 20
          }
        },
        loc: {
          start: {
            line: 85,
            column: 54
          },
          end: {
            line: 121,
            column: 3
          }
        },
        line: 85
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 86,
            column: 18
          },
          end: {
            line: 86,
            column: 19
          }
        },
        loc: {
          start: {
            line: 86,
            column: 26
          },
          end: {
            line: 120,
            column: 5
          }
        },
        line: 86
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 123,
            column: 21
          },
          end: {
            line: 123,
            column: 22
          }
        },
        loc: {
          start: {
            line: 123,
            column: 27
          },
          end: {
            line: 156,
            column: 3
          }
        },
        line: 123
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 132,
            column: 19
          },
          end: {
            line: 132,
            column: 20
          }
        },
        loc: {
          start: {
            line: 132,
            column: 25
          },
          end: {
            line: 152,
            column: 11
          }
        },
        line: 132
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 158,
            column: 20
          },
          end: {
            line: 158,
            column: 21
          }
        },
        loc: {
          start: {
            line: 158,
            column: 32
          },
          end: {
            line: 221,
            column: 3
          }
        },
        line: 158
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 205,
            column: 21
          },
          end: {
            line: 205,
            column: 22
          }
        },
        loc: {
          start: {
            line: 205,
            column: 27
          },
          end: {
            line: 208,
            column: 13
          }
        },
        line: 205
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 212,
            column: 21
          },
          end: {
            line: 212,
            column: 22
          }
        },
        loc: {
          start: {
            line: 212,
            column: 27
          },
          end: {
            line: 212,
            column: 40
          }
        },
        line: 212
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 223,
            column: 22
          },
          end: {
            line: 223,
            column: 23
          }
        },
        loc: {
          start: {
            line: 223,
            column: 61
          },
          end: {
            line: 233,
            column: 3
          }
        },
        line: 223
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 243,
            column: 39
          },
          end: {
            line: 243,
            column: 40
          }
        },
        loc: {
          start: {
            line: 243,
            column: 45
          },
          end: {
            line: 243,
            column: 58
          }
        },
        line: 243
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 271,
            column: 59
          },
          end: {
            line: 271,
            column: 60
          }
        },
        loc: {
          start: {
            line: 272,
            column: 18
          },
          end: {
            line: 288,
            column: 37
          }
        },
        line: 272
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 278,
            column: 29
          },
          end: {
            line: 278,
            column: 30
          }
        },
        loc: {
          start: {
            line: 278,
            column: 35
          },
          end: {
            line: 278,
            column: 99
          }
        },
        line: 278
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 278,
            column: 49
          },
          end: {
            line: 278,
            column: 50
          }
        },
        loc: {
          start: {
            line: 278,
            column: 58
          },
          end: {
            line: 278,
            column: 97
          }
        },
        line: 278
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 309,
            column: 37
          },
          end: {
            line: 309,
            column: 38
          }
        },
        loc: {
          start: {
            line: 309,
            column: 43
          },
          end: {
            line: 309,
            column: 56
          }
        },
        line: 309
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 343,
            column: 25
          },
          end: {
            line: 343,
            column: 26
          }
        },
        loc: {
          start: {
            line: 343,
            column: 31
          },
          end: {
            line: 343,
            column: 50
          }
        },
        line: 343
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 360,
            column: 25
          },
          end: {
            line: 360,
            column: 26
          }
        },
        loc: {
          start: {
            line: 360,
            column: 31
          },
          end: {
            line: 360,
            column: 50
          }
        },
        line: 360
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 30
          }
        }, {
          start: {
            line: 53,
            column: 34
          },
          end: {
            line: 53,
            column: 39
          }
        }],
        line: 53
      },
      "1": {
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 75,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 75,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "2": {
        loc: {
          start: {
            line: 89,
            column: 23
          },
          end: {
            line: 89,
            column: 81
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 89,
            column: 46
          },
          end: {
            line: 89,
            column: 62
          }
        }, {
          start: {
            line: 89,
            column: 65
          },
          end: {
            line: 89,
            column: 81
          }
        }],
        line: 89
      },
      "3": {
        loc: {
          start: {
            line: 95,
            column: 6
          },
          end: {
            line: 117,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 6
          },
          end: {
            line: 117,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 95
      },
      "4": {
        loc: {
          start: {
            line: 95,
            column: 10
          },
          end: {
            line: 95,
            column: 97
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 95,
            column: 10
          },
          end: {
            line: 95,
            column: 40
          }
        }, {
          start: {
            line: 95,
            column: 44
          },
          end: {
            line: 95,
            column: 97
          }
        }],
        line: 95
      },
      "5": {
        loc: {
          start: {
            line: 102,
            column: 8
          },
          end: {
            line: 116,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 8
          },
          end: {
            line: 116,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "6": {
        loc: {
          start: {
            line: 102,
            column: 12
          },
          end: {
            line: 102,
            column: 96
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 102,
            column: 12
          },
          end: {
            line: 102,
            column: 41
          }
        }, {
          start: {
            line: 102,
            column: 45
          },
          end: {
            line: 102,
            column: 96
          }
        }],
        line: 102
      },
      "7": {
        loc: {
          start: {
            line: 110,
            column: 29
          },
          end: {
            line: 110,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 110,
            column: 68
          },
          end: {
            line: 110,
            column: 69
          }
        }, {
          start: {
            line: 110,
            column: 72
          },
          end: {
            line: 110,
            column: 73
          }
        }],
        line: 110
      },
      "8": {
        loc: {
          start: {
            line: 111,
            column: 10
          },
          end: {
            line: 115,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 111,
            column: 10
          },
          end: {
            line: 115,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 111
      },
      "9": {
        loc: {
          start: {
            line: 135,
            column: 22
          },
          end: {
            line: 135,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 135,
            column: 22
          },
          end: {
            line: 135,
            column: 40
          }
        }, {
          start: {
            line: 135,
            column: 44
          },
          end: {
            line: 135,
            column: 49
          }
        }],
        line: 135
      },
      "10": {
        loc: {
          start: {
            line: 159,
            column: 4
          },
          end: {
            line: 162,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 159,
            column: 4
          },
          end: {
            line: 162,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 159
      },
      "11": {
        loc: {
          start: {
            line: 167,
            column: 23
          },
          end: {
            line: 169,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 168,
            column: 10
          },
          end: {
            line: 168,
            column: 93
          }
        }, {
          start: {
            line: 169,
            column: 10
          },
          end: {
            line: 169,
            column: 11
          }
        }],
        line: 167
      },
      "12": {
        loc: {
          start: {
            line: 174,
            column: 21
          },
          end: {
            line: 174,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 174,
            column: 49
          },
          end: {
            line: 174,
            column: 54
          }
        }, {
          start: {
            line: 174,
            column: 57
          },
          end: {
            line: 174,
            column: 63
          }
        }],
        line: 174
      },
      "13": {
        loc: {
          start: {
            line: 195,
            column: 6
          },
          end: {
            line: 197,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 6
          },
          end: {
            line: 197,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 195
      },
      "14": {
        loc: {
          start: {
            line: 235,
            column: 2
          },
          end: {
            line: 300,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 235,
            column: 2
          },
          end: {
            line: 300,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 235
      },
      "15": {
        loc: {
          start: {
            line: 254,
            column: 47
          },
          end: {
            line: 254,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 254,
            column: 47
          },
          end: {
            line: 254,
            column: 65
          }
        }, {
          start: {
            line: 254,
            column: 69
          },
          end: {
            line: 254,
            column: 79
          }
        }],
        line: 254
      },
      "16": {
        loc: {
          start: {
            line: 263,
            column: 19
          },
          end: {
            line: 263,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 19
          },
          end: {
            line: 263,
            column: 31
          }
        }, {
          start: {
            line: 263,
            column: 35
          },
          end: {
            line: 263,
            column: 56
          }
        }],
        line: 263
      },
      "17": {
        loc: {
          start: {
            line: 276,
            column: 22
          },
          end: {
            line: 276,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 276,
            column: 22
          },
          end: {
            line: 276,
            column: 55
          }
        }, {
          start: {
            line: 276,
            column: 59
          },
          end: {
            line: 276,
            column: 86
          }
        }],
        line: 276
      },
      "18": {
        loc: {
          start: {
            line: 283,
            column: 24
          },
          end: {
            line: 283,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 283,
            column: 24
          },
          end: {
            line: 283,
            column: 57
          }
        }, {
          start: {
            line: 283,
            column: 61
          },
          end: {
            line: 283,
            column: 92
          }
        }],
        line: 283
      },
      "19": {
        loc: {
          start: {
            line: 324,
            column: 13
          },
          end: {
            line: 328,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 324,
            column: 13
          },
          end: {
            line: 324,
            column: 39
          }
        }, {
          start: {
            line: 325,
            column: 14
          },
          end: {
            line: 327,
            column: 21
          }
        }],
        line: 324
      },
      "20": {
        loc: {
          start: {
            line: 342,
            column: 44
          },
          end: {
            line: 342,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 342,
            column: 44
          },
          end: {
            line: 342,
            column: 70
          }
        }, {
          start: {
            line: 342,
            column: 74
          },
          end: {
            line: 342,
            column: 100
          }
        }],
        line: 342
      },
      "21": {
        loc: {
          start: {
            line: 359,
            column: 44
          },
          end: {
            line: 359,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 359,
            column: 44
          },
          end: {
            line: 359,
            column: 70
          }
        }, {
          start: {
            line: 359,
            column: 74
          },
          end: {
            line: 359,
            column: 100
          }
        }],
        line: 359
      },
      "22": {
        loc: {
          start: {
            line: 376,
            column: 11
          },
          end: {
            line: 393,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 376,
            column: 11
          },
          end: {
            line: 376,
            column: 37
          }
        }, {
          start: {
            line: 377,
            column: 12
          },
          end: {
            line: 392,
            column: 19
          }
        }],
        line: 376
      },
      "23": {
        loc: {
          start: {
            line: 379,
            column: 43
          },
          end: {
            line: 379,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 379,
            column: 43
          },
          end: {
            line: 379,
            column: 49
          }
        }, {
          start: {
            line: 379,
            column: 53
          },
          end: {
            line: 379,
            column: 78
          }
        }],
        line: 379
      },
      "24": {
        loc: {
          start: {
            line: 383,
            column: 17
          },
          end: {
            line: 390,
            column: 17
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 384,
            column: 18
          },
          end: {
            line: 384,
            column: 53
          }
        }, {
          start: {
            line: 386,
            column: 18
          },
          end: {
            line: 389,
            column: 21
          }
        }],
        line: 383
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "fa93987066a58f9183060c22aa18fb17d154a8e7"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_d4vyd1l1l = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_d4vyd1l1l();
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Alert, ScrollView, ActivityIndicator } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from "../contexts/AuthContext";
import { databaseService } from "../services/database/DatabaseService";
import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
export default function MatchRecordingScreen() {
  cov_d4vyd1l1l().f[0]++;
  var _ref = (cov_d4vyd1l1l().s[0]++, useAuth()),
    user = _ref.user,
    profile = _ref.profile;
  var _ref2 = (cov_d4vyd1l1l().s[1]++, useState('')),
    _ref3 = _slicedToArray(_ref2, 2),
    opponentName = _ref3[0],
    setOpponentName = _ref3[1];
  var _ref4 = (cov_d4vyd1l1l().s[2]++, useState(false)),
    _ref5 = _slicedToArray(_ref4, 2),
    matchStarted = _ref5[0],
    setMatchStarted = _ref5[1];
  var _ref6 = (cov_d4vyd1l1l().s[3]++, useState(false)),
    _ref7 = _slicedToArray(_ref6, 2),
    saving = _ref7[0],
    setSaving = _ref7[1];
  var _ref8 = (cov_d4vyd1l1l().s[4]++, useState({
      player1: {
        name: (cov_d4vyd1l1l().b[0][0]++, profile == null ? void 0 : profile.full_name) || (cov_d4vyd1l1l().b[0][1]++, 'You'),
        sets: [],
        currentGame: 0,
        currentSet: 0
      },
      player2: {
        name: '',
        sets: [],
        currentGame: 0,
        currentSet: 0
      },
      isMatchComplete: false,
      winner: null,
      matchFormat: 'best_of_3',
      surface: 'hard',
      startTime: new Date()
    })),
    _ref9 = _slicedToArray(_ref8, 2),
    matchState = _ref9[0],
    setMatchState = _ref9[1];
  cov_d4vyd1l1l().s[5]++;
  var startMatch = function startMatch() {
    cov_d4vyd1l1l().f[1]++;
    cov_d4vyd1l1l().s[6]++;
    if (!opponentName.trim()) {
      cov_d4vyd1l1l().b[1][0]++;
      cov_d4vyd1l1l().s[7]++;
      Alert.alert('Error', 'Please enter opponent name');
      cov_d4vyd1l1l().s[8]++;
      return;
    } else {
      cov_d4vyd1l1l().b[1][1]++;
    }
    cov_d4vyd1l1l().s[9]++;
    setMatchState(function (prev) {
      cov_d4vyd1l1l().f[2]++;
      cov_d4vyd1l1l().s[10]++;
      return Object.assign({}, prev, {
        player2: Object.assign({}, prev.player2, {
          name: opponentName
        }),
        startTime: new Date()
      });
    });
    cov_d4vyd1l1l().s[11]++;
    setMatchStarted(true);
  };
  cov_d4vyd1l1l().s[12]++;
  var addPoint = function addPoint(player) {
    cov_d4vyd1l1l().f[3]++;
    cov_d4vyd1l1l().s[13]++;
    setMatchState(function (prev) {
      cov_d4vyd1l1l().f[4]++;
      var newState = (cov_d4vyd1l1l().s[14]++, Object.assign({}, prev));
      var currentPlayer = (cov_d4vyd1l1l().s[15]++, newState[player]);
      var opponent = (cov_d4vyd1l1l().s[16]++, player === 'player1' ? (cov_d4vyd1l1l().b[2][0]++, newState.player2) : (cov_d4vyd1l1l().b[2][1]++, newState.player1));
      cov_d4vyd1l1l().s[17]++;
      currentPlayer.currentGame++;
      cov_d4vyd1l1l().s[18]++;
      if ((cov_d4vyd1l1l().b[4][0]++, currentPlayer.currentGame >= 4) && (cov_d4vyd1l1l().b[4][1]++, currentPlayer.currentGame - opponent.currentGame >= 2)) {
        cov_d4vyd1l1l().b[3][0]++;
        cov_d4vyd1l1l().s[19]++;
        currentPlayer.currentSet++;
        cov_d4vyd1l1l().s[20]++;
        currentPlayer.currentGame = 0;
        cov_d4vyd1l1l().s[21]++;
        opponent.currentGame = 0;
        cov_d4vyd1l1l().s[22]++;
        if ((cov_d4vyd1l1l().b[6][0]++, currentPlayer.currentSet >= 6) && (cov_d4vyd1l1l().b[6][1]++, currentPlayer.currentSet - opponent.currentSet >= 2)) {
          cov_d4vyd1l1l().b[5][0]++;
          cov_d4vyd1l1l().s[23]++;
          currentPlayer.sets.push(currentPlayer.currentSet);
          cov_d4vyd1l1l().s[24]++;
          opponent.sets.push(opponent.currentSet);
          cov_d4vyd1l1l().s[25]++;
          currentPlayer.currentSet = 0;
          cov_d4vyd1l1l().s[26]++;
          opponent.currentSet = 0;
          var setsNeeded = (cov_d4vyd1l1l().s[27]++, newState.matchFormat === 'best_of_3' ? (cov_d4vyd1l1l().b[7][0]++, 2) : (cov_d4vyd1l1l().b[7][1]++, 3));
          cov_d4vyd1l1l().s[28]++;
          if (currentPlayer.sets.length >= setsNeeded) {
            cov_d4vyd1l1l().b[8][0]++;
            cov_d4vyd1l1l().s[29]++;
            newState.isMatchComplete = true;
            cov_d4vyd1l1l().s[30]++;
            newState.winner = currentPlayer.name;
            cov_d4vyd1l1l().s[31]++;
            newState.endTime = new Date();
          } else {
            cov_d4vyd1l1l().b[8][1]++;
          }
        } else {
          cov_d4vyd1l1l().b[5][1]++;
        }
      } else {
        cov_d4vyd1l1l().b[3][1]++;
      }
      cov_d4vyd1l1l().s[32]++;
      return newState;
    });
  };
  cov_d4vyd1l1l().s[33]++;
  var resetMatch = function resetMatch() {
    cov_d4vyd1l1l().f[5]++;
    cov_d4vyd1l1l().s[34]++;
    Alert.alert('Reset Match', 'Are you sure you want to reset the current match?', [{
      text: 'Cancel',
      style: 'cancel'
    }, {
      text: 'Reset',
      style: 'destructive',
      onPress: function onPress() {
        cov_d4vyd1l1l().f[6]++;
        cov_d4vyd1l1l().s[35]++;
        setMatchState({
          player1: {
            name: (cov_d4vyd1l1l().b[9][0]++, profile == null ? void 0 : profile.full_name) || (cov_d4vyd1l1l().b[9][1]++, 'You'),
            sets: [],
            currentGame: 0,
            currentSet: 0
          },
          player2: {
            name: opponentName,
            sets: [],
            currentGame: 0,
            currentSet: 0
          },
          isMatchComplete: false,
          winner: null,
          matchFormat: 'best_of_3',
          surface: 'hard',
          startTime: new Date()
        });
      }
    }]);
  };
  cov_d4vyd1l1l().s[36]++;
  var saveMatch = function () {
    var _ref0 = _asyncToGenerator(function* () {
      cov_d4vyd1l1l().f[7]++;
      cov_d4vyd1l1l().s[37]++;
      if (!matchState.isMatchComplete) {
        cov_d4vyd1l1l().b[10][0]++;
        cov_d4vyd1l1l().s[38]++;
        Alert.alert('Error', 'Match is not complete yet');
        cov_d4vyd1l1l().s[39]++;
        return;
      } else {
        cov_d4vyd1l1l().b[10][1]++;
      }
      cov_d4vyd1l1l().s[40]++;
      setSaving(true);
      cov_d4vyd1l1l().s[41]++;
      try {
        var _matchState$endTime;
        var duration = (cov_d4vyd1l1l().s[42]++, matchState.endTime ? (cov_d4vyd1l1l().b[11][0]++, Math.round((matchState.endTime.getTime() - matchState.startTime.getTime()) / 60000)) : (cov_d4vyd1l1l().b[11][1]++, 0));
        var player1Sets = (cov_d4vyd1l1l().s[43]++, matchState.player1.sets.length);
        var player2Sets = (cov_d4vyd1l1l().s[44]++, matchState.player2.sets.length);
        var result = (cov_d4vyd1l1l().s[45]++, player1Sets > player2Sets ? (cov_d4vyd1l1l().b[12][0]++, 'win') : (cov_d4vyd1l1l().b[12][1]++, 'loss'));
        var finalScore = (cov_d4vyd1l1l().s[46]++, `${matchState.player1.sets.join('-')} vs ${matchState.player2.sets.join('-')}`);
        var _ref1 = (cov_d4vyd1l1l().s[47]++, yield databaseService.createMatch({
            opponent_name: matchState.player2.name,
            match_type: 'friendly',
            match_format: matchState.matchFormat,
            result: result,
            final_score: finalScore,
            sets_won: player1Sets,
            sets_lost: player2Sets,
            surface: matchState.surface,
            match_date: matchState.startTime.toISOString().split('T')[0],
            start_time: matchState.startTime.toTimeString().split(' ')[0],
            end_time: (_matchState$endTime = matchState.endTime) == null ? void 0 : _matchState$endTime.toTimeString().split(' ')[0],
            duration_minutes: duration
          })),
          data = _ref1.data,
          error = _ref1.error;
        cov_d4vyd1l1l().s[48]++;
        if (error) {
          cov_d4vyd1l1l().b[13][0]++;
          cov_d4vyd1l1l().s[49]++;
          throw new Error(error);
        } else {
          cov_d4vyd1l1l().b[13][1]++;
        }
        cov_d4vyd1l1l().s[50]++;
        Alert.alert('Match Saved', 'Your match has been saved successfully!', [{
          text: 'View Matches',
          onPress: function onPress() {
            cov_d4vyd1l1l().f[8]++;
            cov_d4vyd1l1l().s[51]++;
            router.back();
            cov_d4vyd1l1l().s[52]++;
            router.push('/(tabs)/progress');
          }
        }, {
          text: 'OK',
          onPress: function onPress() {
            cov_d4vyd1l1l().f[9]++;
            cov_d4vyd1l1l().s[53]++;
            return router.back();
          }
        }]);
      } catch (error) {
        cov_d4vyd1l1l().s[54]++;
        Alert.alert('Error', 'Failed to save match. Please try again.');
      } finally {
        cov_d4vyd1l1l().s[55]++;
        setSaving(false);
      }
    });
    return function saveMatch() {
      return _ref0.apply(this, arguments);
    };
  }();
  cov_d4vyd1l1l().s[56]++;
  var formatScore = function formatScore(player) {
    cov_d4vyd1l1l().f[10]++;
    var gameScore = (cov_d4vyd1l1l().s[57]++, player.currentGame);
    var setScore = (cov_d4vyd1l1l().s[58]++, player.currentSet);
    var setsWon = (cov_d4vyd1l1l().s[59]++, player.sets);
    cov_d4vyd1l1l().s[60]++;
    return {
      sets: setsWon.join(' '),
      currentSet: setScore,
      currentGame: gameScore
    };
  };
  cov_d4vyd1l1l().s[61]++;
  if (!matchStarted) {
    cov_d4vyd1l1l().b[14][0]++;
    cov_d4vyd1l1l().s[62]++;
    return _jsx(SafeAreaView, {
      style: styles.container,
      children: _jsxs(LinearGradient, {
        colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
        style: styles.gradient,
        children: [_jsxs(View, {
          style: styles.header,
          children: [_jsx(TouchableOpacity, {
            onPress: function onPress() {
              cov_d4vyd1l1l().f[11]++;
              cov_d4vyd1l1l().s[63]++;
              return router.back();
            },
            style: styles.backButton,
            children: _jsx(Ionicons, {
              name: "arrow-back",
              size: 24,
              color: "white"
            })
          }), _jsx(Text, {
            style: styles.headerTitle,
            children: "Record Match"
          })]
        }), _jsxs(View, {
          style: styles.setupContainer,
          children: [_jsx(Text, {
            style: styles.setupTitle,
            children: "Match Setup"
          }), _jsxs(View, {
            style: styles.playerInfo,
            children: [_jsx(Text, {
              style: styles.playerLabel,
              children: "You"
            }), _jsx(Text, {
              style: styles.playerName,
              children: (cov_d4vyd1l1l().b[15][0]++, profile == null ? void 0 : profile.full_name) || (cov_d4vyd1l1l().b[15][1]++, 'Player 1')
            })]
          }), _jsx(Text, {
            style: styles.vsText,
            children: "VS"
          }), _jsxs(View, {
            style: styles.inputContainer,
            children: [_jsx(Text, {
              style: styles.inputLabel,
              children: "Opponent Name"
            }), _jsx(TouchableOpacity, {
              style: styles.input,
              children: _jsx(Text, {
                style: styles.inputText,
                children: (cov_d4vyd1l1l().b[16][0]++, opponentName) || (cov_d4vyd1l1l().b[16][1]++, 'Enter opponent name')
              })
            })]
          }), _jsxs(View, {
            style: styles.formatContainer,
            children: [_jsx(Text, {
              style: styles.formatLabel,
              children: "Match Format"
            }), _jsx(View, {
              style: styles.formatOptions,
              children: ['best_of_3', 'best_of_5', 'pro_set'].map(function (format) {
                cov_d4vyd1l1l().f[12]++;
                cov_d4vyd1l1l().s[64]++;
                return _jsx(TouchableOpacity, {
                  style: [styles.formatOption, (cov_d4vyd1l1l().b[17][0]++, matchState.matchFormat === format) && (cov_d4vyd1l1l().b[17][1]++, styles.formatOptionSelected)],
                  onPress: function onPress() {
                    cov_d4vyd1l1l().f[13]++;
                    cov_d4vyd1l1l().s[65]++;
                    return setMatchState(function (prev) {
                      cov_d4vyd1l1l().f[14]++;
                      cov_d4vyd1l1l().s[66]++;
                      return Object.assign({}, prev, {
                        matchFormat: format
                      });
                    });
                  },
                  children: _jsx(Text, {
                    style: [styles.formatOptionText, (cov_d4vyd1l1l().b[18][0]++, matchState.matchFormat === format) && (cov_d4vyd1l1l().b[18][1]++, styles.formatOptionTextSelected)],
                    children: format.replace('_', ' ').toUpperCase()
                  })
                }, format);
              })
            })]
          }), _jsx(TouchableOpacity, {
            style: styles.startButton,
            onPress: startMatch,
            children: _jsx(Text, {
              style: styles.startButtonText,
              children: "Start Match"
            })
          })]
        })]
      })
    });
  } else {
    cov_d4vyd1l1l().b[14][1]++;
  }
  cov_d4vyd1l1l().s[67]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(LinearGradient, {
      colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
      style: styles.gradient,
      children: [_jsxs(View, {
        style: styles.header,
        children: [_jsx(TouchableOpacity, {
          onPress: function onPress() {
            cov_d4vyd1l1l().f[15]++;
            cov_d4vyd1l1l().s[68]++;
            return router.back();
          },
          style: styles.backButton,
          children: _jsx(Ionicons, {
            name: "arrow-back",
            size: 24,
            color: "white"
          })
        }), _jsx(Text, {
          style: styles.headerTitle,
          children: "Live Match"
        }), _jsx(TouchableOpacity, {
          onPress: resetMatch,
          style: styles.resetButton,
          children: _jsx(Ionicons, {
            name: "refresh",
            size: 24,
            color: "white"
          })
        })]
      }), _jsxs(ScrollView, {
        style: styles.content,
        children: [_jsxs(View, {
          style: styles.matchStatus,
          children: [_jsx(Text, {
            style: styles.matchFormat,
            children: matchState.matchFormat.replace('_', ' ').toUpperCase()
          }), (cov_d4vyd1l1l().b[19][0]++, matchState.isMatchComplete) && (cov_d4vyd1l1l().b[19][1]++, _jsxs(Text, {
            style: styles.matchComplete,
            children: ["\uD83C\uDFC6 ", matchState.winner, " Wins!"]
          }))]
        }), _jsxs(View, {
          style: styles.scoreboard,
          children: [_jsxs(View, {
            style: styles.playerRow,
            children: [_jsx(Text, {
              style: styles.playerNameScore,
              children: matchState.player1.name
            }), _jsxs(View, {
              style: styles.scoreContainer,
              children: [_jsx(Text, {
                style: styles.setsScore,
                children: formatScore(matchState.player1).sets
              }), _jsx(Text, {
                style: styles.currentSetScore,
                children: formatScore(matchState.player1).currentSet
              }), _jsx(Text, {
                style: styles.currentGameScore,
                children: formatScore(matchState.player1).currentGame
              })]
            }), _jsx(TouchableOpacity, {
              style: [styles.pointButton, (cov_d4vyd1l1l().b[20][0]++, matchState.isMatchComplete) && (cov_d4vyd1l1l().b[20][1]++, styles.pointButtonDisabled)],
              onPress: function onPress() {
                cov_d4vyd1l1l().f[16]++;
                cov_d4vyd1l1l().s[69]++;
                return addPoint('player1');
              },
              disabled: matchState.isMatchComplete,
              children: _jsx(Text, {
                style: styles.pointButtonText,
                children: "+"
              })
            })]
          }), _jsxs(View, {
            style: styles.playerRow,
            children: [_jsx(Text, {
              style: styles.playerNameScore,
              children: matchState.player2.name
            }), _jsxs(View, {
              style: styles.scoreContainer,
              children: [_jsx(Text, {
                style: styles.setsScore,
                children: formatScore(matchState.player2).sets
              }), _jsx(Text, {
                style: styles.currentSetScore,
                children: formatScore(matchState.player2).currentSet
              }), _jsx(Text, {
                style: styles.currentGameScore,
                children: formatScore(matchState.player2).currentGame
              })]
            }), _jsx(TouchableOpacity, {
              style: [styles.pointButton, (cov_d4vyd1l1l().b[21][0]++, matchState.isMatchComplete) && (cov_d4vyd1l1l().b[21][1]++, styles.pointButtonDisabled)],
              onPress: function onPress() {
                cov_d4vyd1l1l().f[17]++;
                cov_d4vyd1l1l().s[70]++;
                return addPoint('player2');
              },
              disabled: matchState.isMatchComplete,
              children: _jsx(Text, {
                style: styles.pointButtonText,
                children: "+"
              })
            })]
          })]
        }), _jsxs(View, {
          style: styles.scoreLabels,
          children: [_jsx(Text, {
            style: styles.scoreLabel,
            children: "Sets"
          }), _jsx(Text, {
            style: styles.scoreLabel,
            children: "Games"
          }), _jsx(Text, {
            style: styles.scoreLabel,
            children: "Points"
          })]
        }), (cov_d4vyd1l1l().b[22][0]++, matchState.isMatchComplete) && (cov_d4vyd1l1l().b[22][1]++, _jsx(View, {
          style: styles.matchActions,
          children: _jsx(TouchableOpacity, {
            style: [styles.saveButton, (cov_d4vyd1l1l().b[23][0]++, saving) && (cov_d4vyd1l1l().b[23][1]++, styles.saveButtonDisabled)],
            onPress: saveMatch,
            disabled: saving,
            children: saving ? (cov_d4vyd1l1l().b[24][0]++, _jsx(ActivityIndicator, {
              color: "white"
            })) : (cov_d4vyd1l1l().b[24][1]++, _jsxs(_Fragment, {
              children: [_jsx(Ionicons, {
                name: "save",
                size: 20,
                color: "white"
              }), _jsx(Text, {
                style: styles.saveButtonText,
                children: "Save Match"
              })]
            }))
          })
        }))]
      })]
    })
  });
}
var styles = (cov_d4vyd1l1l().s[71]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16
  },
  backButton: {
    padding: 8
  },
  resetButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white'
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  setupContainer: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 20
  },
  setupTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 40
  },
  playerInfo: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
    alignItems: 'center'
  },
  playerLabel: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8
  },
  playerName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white'
  },
  vsText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginVertical: 20
  },
  inputContainer: {
    marginBottom: 30
  },
  inputLabel: {
    fontSize: 16,
    color: 'white',
    marginBottom: 8
  },
  input: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)'
  },
  inputText: {
    fontSize: 16,
    color: 'white'
  },
  formatContainer: {
    marginBottom: 40
  },
  formatLabel: {
    fontSize: 16,
    color: 'white',
    marginBottom: 12
  },
  formatOptions: {
    flexDirection: 'row',
    gap: 12
  },
  formatOption: {
    flex: 1,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)'
  },
  formatOptionSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderColor: 'white'
  },
  formatOptionText: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
    fontWeight: '500'
  },
  formatOptionTextSelected: {
    color: 'white',
    fontWeight: 'bold'
  },
  startButton: {
    backgroundColor: 'white',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center'
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e3a8a'
  },
  matchStatus: {
    alignItems: 'center',
    marginBottom: 30
  },
  matchFormat: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 8
  },
  matchComplete: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffe600'
  },
  scoreboard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16
  },
  playerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)'
  },
  playerNameScore: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    flex: 1
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20
  },
  setsScore: {
    fontSize: 16,
    color: 'white',
    minWidth: 40,
    textAlign: 'center'
  },
  currentSetScore: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    minWidth: 30,
    textAlign: 'center'
  },
  currentGameScore: {
    fontSize: 20,
    color: 'white',
    minWidth: 30,
    textAlign: 'center'
  },
  pointButton: {
    backgroundColor: '#23ba16',
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 16
  },
  pointButtonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)'
  },
  pointButtonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white'
  },
  scoreLabels: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingRight: 76,
    gap: 20,
    marginBottom: 30
  },
  scoreLabel: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.6)',
    minWidth: 40,
    textAlign: 'center'
  },
  matchActions: {
    paddingVertical: 20
  },
  saveButton: {
    backgroundColor: '#23ba16',
    borderRadius: 12,
    paddingVertical: 16,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8
  },
  saveButtonDisabled: {
    opacity: 0.6
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: 'white'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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