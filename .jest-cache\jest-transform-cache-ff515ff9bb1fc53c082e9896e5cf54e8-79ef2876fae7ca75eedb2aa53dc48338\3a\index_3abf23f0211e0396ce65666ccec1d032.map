{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_interopRequireDefault", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "_objectSpread2", "_View", "_StyleSheet", "_<PERSON><PERSON><PERSON><PERSON>", "_Platform", "_invariant", "React", "_VirtualizedList", "_VirtualizeUtils", "_memoizeOne", "_excluded", "removeClippedSubviewsOrDefault", "removeClippedSubviews", "OS", "numColumnsOrDefault", "numColumns", "isArrayLike", "data", "Object", "length", "FlatList", "_React$PureComponent", "_props", "_this", "_virtualizedListPairs", "_captureRef", "ref", "_listRef", "_getItem", "index", "props", "ret", "kk", "itemIndex", "_item", "push", "_getItemCount", "Math", "ceil", "_keyExtractor", "items", "_this$props$keyExtrac", "keyExtractor", "Array", "isArray", "map", "item", "join", "_renderer", "ListItemComponent", "renderItem", "columnWrapperStyle", "extraData", "cols", "render", "createElement", "renderProp", "info", "_item2", "_index", "style", "styles", "row", "it", "element", "separators", "Fragment", "key", "_memoized<PERSON><PERSON><PERSON>", "_checkProps", "viewabilityConfigCallbackPairs", "pair", "viewabilityConfig", "onViewableItemsChanged", "_createOnViewableItemsChanged", "value", "scrollToEnd", "params", "scrollToIndex", "scrollToItem", "scrollToOffset", "recordInteraction", "flashScrollIndicators", "getScrollResponder", "getNativeScrollRef", "getScrollRef", "getScrollableNode", "componentDidUpdate", "prevProps", "getItem", "getItemCount", "horizontal", "_pushMultiColumnViewable", "arr", "v", "_this$props$keyExtrac2", "for<PERSON>ach", "ii", "_this2", "changed", "viewableItems", "_this$props", "_removeClippedSubviews", "_this$props$strictMod", "strictMode", "restProps", "renderer", "PureComponent", "create", "flexDirection", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _View = _interopRequireDefault(require(\"../../../exports/View\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../../../exports/StyleSheet\"));\nvar _deepDiffer = _interopRequireDefault(require(\"../deepDiffer\"));\nvar _Platform = _interopRequireDefault(require(\"../../../exports/Platform\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _VirtualizedList = _interopRequireDefault(require(\"../VirtualizedList\"));\nvar _VirtualizeUtils = require(\"../VirtualizeUtils\");\nvar _memoizeOne = _interopRequireDefault(require(\"memoize-one\"));\nvar _excluded = [\"numColumns\", \"columnWrapperStyle\", \"removeClippedSubviews\", \"strictMode\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n/**\n * Default Props Helper Functions\n * Use the following helper functions for default values\n */\n\n// removeClippedSubviewsOrDefault(this.props.removeClippedSubviews)\nfunction removeClippedSubviewsOrDefault(removeClippedSubviews) {\n  return removeClippedSubviews !== null && removeClippedSubviews !== void 0 ? removeClippedSubviews : _Platform.default.OS === 'android';\n}\n\n// numColumnsOrDefault(this.props.numColumns)\nfunction numColumnsOrDefault(numColumns) {\n  return numColumns !== null && numColumns !== void 0 ? numColumns : 1;\n}\nfunction isArrayLike(data) {\n  // $FlowExpectedError[incompatible-use]\n  return typeof Object(data).length === 'number';\n}\n/**\n * A performant interface for rendering simple, flat lists, supporting the most handy features:\n *\n *  - Fully cross-platform.\n *  - Optional horizontal mode.\n *  - Configurable viewability callbacks.\n *  - Header support.\n *  - Footer support.\n *  - Separator support.\n *  - Pull to Refresh.\n *  - Scroll loading.\n *  - ScrollToIndex support.\n *\n * If you need section support, use [`<SectionList>`](docs/sectionlist.html).\n *\n * Minimal Example:\n *\n *     <FlatList\n *       data={[{key: 'a'}, {key: 'b'}]}\n *       renderItem={({item}) => <Text>{item.key}</Text>}\n *     />\n *\n * More complex, multi-select example demonstrating `PureComponent` usage for perf optimization and avoiding bugs.\n *\n * - By binding the `onPressItem` handler, the props will remain `===` and `PureComponent` will\n *   prevent wasteful re-renders unless the actual `id`, `selected`, or `title` props change, even\n *   if the components rendered in `MyListItem` did not have such optimizations.\n * - By passing `extraData={this.state}` to `FlatList` we make sure `FlatList` itself will re-render\n *   when the `state.selected` changes. Without setting this prop, `FlatList` would not know it\n *   needs to re-render any items because it is also a `PureComponent` and the prop comparison will\n *   not show any changes.\n * - `keyExtractor` tells the list to use the `id`s for the react keys instead of the default `key` property.\n *\n *\n *     class MyListItem extends React.PureComponent {\n *       _onPress = () => {\n *         this.props.onPressItem(this.props.id);\n *       };\n *\n *       render() {\n *         const textColor = this.props.selected ? \"red\" : \"black\";\n *         return (\n *           <TouchableOpacity onPress={this._onPress}>\n *             <View>\n *               <Text style={{ color: textColor }}>\n *                 {this.props.title}\n *               </Text>\n *             </View>\n *           </TouchableOpacity>\n *         );\n *       }\n *     }\n *\n *     class MultiSelectList extends React.PureComponent {\n *       state = {selected: (new Map(): Map<string, boolean>)};\n *\n *       _keyExtractor = (item, index) => item.id;\n *\n *       _onPressItem = (id: string) => {\n *         // updater functions are preferred for transactional updates\n *         this.setState((state) => {\n *           // copy the map rather than modifying state.\n *           const selected = new Map(state.selected);\n *           selected.set(id, !selected.get(id)); // toggle\n *           return {selected};\n *         });\n *       };\n *\n *       _renderItem = ({item}) => (\n *         <MyListItem\n *           id={item.id}\n *           onPressItem={this._onPressItem}\n *           selected={!!this.state.selected.get(item.id)}\n *           title={item.title}\n *         />\n *       );\n *\n *       render() {\n *         return (\n *           <FlatList\n *             data={this.props.data}\n *             extraData={this.state}\n *             keyExtractor={this._keyExtractor}\n *             renderItem={this._renderItem}\n *           />\n *         );\n *       }\n *     }\n *\n * This is a convenience wrapper around [`<VirtualizedList>`](docs/virtualizedlist.html),\n * and thus inherits its props (as well as those of `ScrollView`) that aren't explicitly listed\n * here, along with the following caveats:\n *\n * - Internal state is not preserved when content scrolls out of the render window. Make sure all\n *   your data is captured in the item data or external stores like Flux, Redux, or Relay.\n * - This is a `PureComponent` which means that it will not re-render if `props` remain shallow-\n *   equal. Make sure that everything your `renderItem` function depends on is passed as a prop\n *   (e.g. `extraData`) that is not `===` after updates, otherwise your UI may not update on\n *   changes. This includes the `data` prop and parent component state.\n * - In order to constrain memory and enable smooth scrolling, content is rendered asynchronously\n *   offscreen. This means it's possible to scroll faster than the fill rate ands momentarily see\n *   blank content. This is a tradeoff that can be adjusted to suit the needs of each application,\n *   and we are working on improving it behind the scenes.\n * - By default, the list looks for a `key` prop on each item and uses that for the React key.\n *   Alternatively, you can provide a custom `keyExtractor` prop.\n *\n * Also inherits [ScrollView Props](docs/scrollview.html#props), unless it is nested in another FlatList of same orientation.\n */\nclass FlatList extends React.PureComponent {\n  /**\n   * Scrolls to the end of the content. May be janky without `getItemLayout` prop.\n   */\n  scrollToEnd(params) {\n    if (this._listRef) {\n      this._listRef.scrollToEnd(params);\n    }\n  }\n\n  /**\n   * Scrolls to the item at the specified index such that it is positioned in the viewable area\n   * such that `viewPosition` 0 places it at the top, 1 at the bottom, and 0.5 centered in the\n   * middle. `viewOffset` is a fixed number of pixels to offset the final target position.\n   *\n   * Note: cannot scroll to locations outside the render window without specifying the\n   * `getItemLayout` prop.\n   */\n  scrollToIndex(params) {\n    if (this._listRef) {\n      this._listRef.scrollToIndex(params);\n    }\n  }\n\n  /**\n   * Requires linear scan through data - use `scrollToIndex` instead if possible.\n   *\n   * Note: cannot scroll to locations outside the render window without specifying the\n   * `getItemLayout` prop.\n   */\n  scrollToItem(params) {\n    if (this._listRef) {\n      this._listRef.scrollToItem(params);\n    }\n  }\n\n  /**\n   * Scroll to a specific content pixel offset in the list.\n   *\n   * Check out [scrollToOffset](docs/virtualizedlist.html#scrolltooffset) of VirtualizedList\n   */\n  scrollToOffset(params) {\n    if (this._listRef) {\n      this._listRef.scrollToOffset(params);\n    }\n  }\n\n  /**\n   * Tells the list an interaction has occurred, which should trigger viewability calculations, e.g.\n   * if `waitForInteractions` is true and the user has not scrolled. This is typically called by\n   * taps on items or by navigation actions.\n   */\n  recordInteraction() {\n    if (this._listRef) {\n      this._listRef.recordInteraction();\n    }\n  }\n\n  /**\n   * Displays the scroll indicators momentarily.\n   *\n   * @platform ios\n   */\n  flashScrollIndicators() {\n    if (this._listRef) {\n      this._listRef.flashScrollIndicators();\n    }\n  }\n\n  /**\n   * Provides a handle to the underlying scroll responder.\n   */\n  getScrollResponder() {\n    if (this._listRef) {\n      return this._listRef.getScrollResponder();\n    }\n  }\n\n  /**\n   * Provides a reference to the underlying host component\n   */\n  getNativeScrollRef() {\n    if (this._listRef) {\n      /* $FlowFixMe[incompatible-return] Suppresses errors found when fixing\n       * TextInput typing */\n      return this._listRef.getScrollRef();\n    }\n  }\n  getScrollableNode() {\n    if (this._listRef) {\n      return this._listRef.getScrollableNode();\n    }\n  }\n  constructor(_props) {\n    super(_props);\n    this._virtualizedListPairs = [];\n    this._captureRef = ref => {\n      this._listRef = ref;\n    };\n    this._getItem = (data, index) => {\n      var numColumns = numColumnsOrDefault(this.props.numColumns);\n      if (numColumns > 1) {\n        var ret = [];\n        for (var kk = 0; kk < numColumns; kk++) {\n          var itemIndex = index * numColumns + kk;\n          if (itemIndex < data.length) {\n            var _item = data[itemIndex];\n            ret.push(_item);\n          }\n        }\n        return ret;\n      } else {\n        return data[index];\n      }\n    };\n    this._getItemCount = data => {\n      // Legacy behavior of FlatList was to forward \"undefined\" length if invalid\n      // data like a non-arraylike object is passed. VirtualizedList would then\n      // coerce this, and the math would work out to no-op. For compatibility, if\n      // invalid data is passed, we tell VirtualizedList there are zero items\n      // available to prevent it from trying to read from the invalid data\n      // (without propagating invalidly typed data).\n      if (data != null && isArrayLike(data)) {\n        var numColumns = numColumnsOrDefault(this.props.numColumns);\n        return numColumns > 1 ? Math.ceil(data.length / numColumns) : data.length;\n      } else {\n        return 0;\n      }\n    };\n    this._keyExtractor = (items, index) => {\n      var _this$props$keyExtrac;\n      var numColumns = numColumnsOrDefault(this.props.numColumns);\n      var keyExtractor = (_this$props$keyExtrac = this.props.keyExtractor) !== null && _this$props$keyExtrac !== void 0 ? _this$props$keyExtrac : _VirtualizeUtils.keyExtractor;\n      if (numColumns > 1) {\n        (0, _invariant.default)(Array.isArray(items), 'FlatList: Encountered internal consistency error, expected each item to consist of an ' + 'array with 1-%s columns; instead, received a single item.', numColumns);\n        return items.map((item, kk) => keyExtractor(item, index * numColumns + kk)).join(':');\n      }\n\n      // $FlowFixMe[incompatible-call] Can't call keyExtractor with an array\n      return keyExtractor(items, index);\n    };\n    this._renderer = (ListItemComponent, renderItem, columnWrapperStyle, numColumns, extraData\n    // $FlowFixMe[missing-local-annot]\n    ) => {\n      var cols = numColumnsOrDefault(numColumns);\n      var render = props => {\n        if (ListItemComponent) {\n          // $FlowFixMe[not-a-component] Component isn't valid\n          // $FlowFixMe[incompatible-type-arg] Component isn't valid\n          // $FlowFixMe[incompatible-return] Component isn't valid\n          return /*#__PURE__*/React.createElement(ListItemComponent, props);\n        } else if (renderItem) {\n          // $FlowFixMe[incompatible-call]\n          return renderItem(props);\n        } else {\n          return null;\n        }\n      };\n      var renderProp = info => {\n        if (cols > 1) {\n          var _item2 = info.item,\n            _index = info.index;\n          (0, _invariant.default)(Array.isArray(_item2), 'Expected array of items with numColumns > 1');\n          return /*#__PURE__*/React.createElement(_View.default, {\n            style: [styles.row, columnWrapperStyle]\n          }, _item2.map((it, kk) => {\n            var element = render({\n              // $FlowFixMe[incompatible-call]\n              item: it,\n              index: _index * cols + kk,\n              separators: info.separators\n            });\n            return element != null ? /*#__PURE__*/React.createElement(React.Fragment, {\n              key: kk\n            }, element) : null;\n          }));\n        } else {\n          return render(info);\n        }\n      };\n      return ListItemComponent ? {\n        ListItemComponent: renderProp\n      } : {\n        renderItem: renderProp\n      };\n    };\n    this._memoizedRenderer = (0, _memoizeOne.default)(this._renderer);\n    this._checkProps(this.props);\n    if (this.props.viewabilityConfigCallbackPairs) {\n      this._virtualizedListPairs = this.props.viewabilityConfigCallbackPairs.map(pair => ({\n        viewabilityConfig: pair.viewabilityConfig,\n        onViewableItemsChanged: this._createOnViewableItemsChanged(pair.onViewableItemsChanged)\n      }));\n    } else if (this.props.onViewableItemsChanged) {\n      this._virtualizedListPairs.push({\n        /* $FlowFixMe[incompatible-call] (>=0.63.0 site=react_native_fb) This\n         * comment suppresses an error found when Flow v0.63 was deployed. To\n         * see the error delete this comment and run Flow. */\n        viewabilityConfig: this.props.viewabilityConfig,\n        onViewableItemsChanged: this._createOnViewableItemsChanged(this.props.onViewableItemsChanged)\n      });\n    }\n  }\n\n  // $FlowFixMe[missing-local-annot]\n  componentDidUpdate(prevProps) {\n    (0, _invariant.default)(prevProps.numColumns === this.props.numColumns, 'Changing numColumns on the fly is not supported. Change the key prop on FlatList when ' + 'changing the number of columns to force a fresh render of the component.');\n    (0, _invariant.default)(prevProps.onViewableItemsChanged === this.props.onViewableItemsChanged, 'Changing onViewableItemsChanged on the fly is not supported');\n    (0, _invariant.default)(!(0, _deepDiffer.default)(prevProps.viewabilityConfig, this.props.viewabilityConfig), 'Changing viewabilityConfig on the fly is not supported');\n    (0, _invariant.default)(prevProps.viewabilityConfigCallbackPairs === this.props.viewabilityConfigCallbackPairs, 'Changing viewabilityConfigCallbackPairs on the fly is not supported');\n    this._checkProps(this.props);\n  }\n  // $FlowFixMe[missing-local-annot]\n  _checkProps(props) {\n    var getItem = props.getItem,\n      getItemCount = props.getItemCount,\n      horizontal = props.horizontal,\n      columnWrapperStyle = props.columnWrapperStyle,\n      onViewableItemsChanged = props.onViewableItemsChanged,\n      viewabilityConfigCallbackPairs = props.viewabilityConfigCallbackPairs;\n    var numColumns = numColumnsOrDefault(this.props.numColumns);\n    (0, _invariant.default)(!getItem && !getItemCount, 'FlatList does not support custom data formats.');\n    if (numColumns > 1) {\n      (0, _invariant.default)(!horizontal, 'numColumns does not support horizontal.');\n    } else {\n      (0, _invariant.default)(!columnWrapperStyle, 'columnWrapperStyle not supported for single column lists');\n    }\n    (0, _invariant.default)(!(onViewableItemsChanged && viewabilityConfigCallbackPairs), 'FlatList does not support setting both onViewableItemsChanged and ' + 'viewabilityConfigCallbackPairs.');\n  }\n  _pushMultiColumnViewable(arr, v) {\n    var _this$props$keyExtrac2;\n    var numColumns = numColumnsOrDefault(this.props.numColumns);\n    var keyExtractor = (_this$props$keyExtrac2 = this.props.keyExtractor) !== null && _this$props$keyExtrac2 !== void 0 ? _this$props$keyExtrac2 : _VirtualizeUtils.keyExtractor;\n    v.item.forEach((item, ii) => {\n      (0, _invariant.default)(v.index != null, 'Missing index!');\n      var index = v.index * numColumns + ii;\n      arr.push((0, _objectSpread2.default)((0, _objectSpread2.default)({}, v), {}, {\n        item,\n        key: keyExtractor(item, index),\n        index\n      }));\n    });\n  }\n  _createOnViewableItemsChanged(onViewableItemsChanged\n  // $FlowFixMe[missing-local-annot]\n  ) {\n    return info => {\n      var numColumns = numColumnsOrDefault(this.props.numColumns);\n      if (onViewableItemsChanged) {\n        if (numColumns > 1) {\n          var changed = [];\n          var viewableItems = [];\n          info.viewableItems.forEach(v => this._pushMultiColumnViewable(viewableItems, v));\n          info.changed.forEach(v => this._pushMultiColumnViewable(changed, v));\n          onViewableItemsChanged({\n            viewableItems,\n            changed\n          });\n        } else {\n          onViewableItemsChanged(info);\n        }\n      }\n    };\n  }\n\n  // $FlowFixMe[missing-local-annot]\n\n  render() {\n    var _this$props = this.props,\n      numColumns = _this$props.numColumns,\n      columnWrapperStyle = _this$props.columnWrapperStyle,\n      _removeClippedSubviews = _this$props.removeClippedSubviews,\n      _this$props$strictMod = _this$props.strictMode,\n      strictMode = _this$props$strictMod === void 0 ? false : _this$props$strictMod,\n      restProps = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);\n    var renderer = strictMode ? this._memoizedRenderer : this._renderer;\n    return (\n      /*#__PURE__*/\n      // $FlowFixMe[incompatible-exact] - `restProps` (`Props`) is inexact.\n      React.createElement(_VirtualizedList.default, (0, _extends2.default)({}, restProps, {\n        getItem: this._getItem,\n        getItemCount: this._getItemCount,\n        keyExtractor: this._keyExtractor,\n        ref: this._captureRef,\n        viewabilityConfigCallbackPairs: this._virtualizedListPairs,\n        removeClippedSubviews: removeClippedSubviewsOrDefault(_removeClippedSubviews)\n      }, renderer(this.props.ListItemComponent, this.props.renderItem, columnWrapperStyle, numColumns, this.props.extraData)))\n    );\n  }\n}\nvar styles = _StyleSheet.default.create({\n  row: {\n    flexDirection: 'row'\n  }\n});\nvar _default = exports.default = FlatList;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,SAAAM,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAM,OAAA,EAAAF,CAAA,OAAAL,2BAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAM,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEb,IAAIa,sBAAsB,GAAGpB,OAAO,CAAC,8CAA8C,CAAC,CAACU,OAAO;AAC5F,IAAIW,uBAAuB,GAAGrB,OAAO,CAAC,+CAA+C,CAAC,CAACU,OAAO;AAC9FY,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACZ,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIc,SAAS,GAAGJ,sBAAsB,CAACpB,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIyB,8BAA8B,GAAGL,sBAAsB,CAACpB,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAI0B,cAAc,GAAGN,sBAAsB,CAACpB,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAI2B,KAAK,GAAGP,sBAAsB,CAACpB,OAAO,wBAAwB,CAAC,CAAC;AACpE,IAAI4B,WAAW,GAAGR,sBAAsB,CAACpB,OAAO,8BAA8B,CAAC,CAAC;AAChF,IAAI6B,WAAW,GAAGT,sBAAsB,CAACpB,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAI8B,SAAS,GAAGV,sBAAsB,CAACpB,OAAO,4BAA4B,CAAC,CAAC;AAC5E,IAAI+B,UAAU,GAAGX,sBAAsB,CAACpB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIgC,KAAK,GAAGX,uBAAuB,CAACrB,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIiC,gBAAgB,GAAGb,sBAAsB,CAACpB,OAAO,qBAAqB,CAAC,CAAC;AAC5E,IAAIkC,gBAAgB,GAAGlC,OAAO,qBAAqB,CAAC;AACpD,IAAImC,WAAW,GAAGf,sBAAsB,CAACpB,OAAO,CAAC,aAAa,CAAC,CAAC;AAChE,IAAIoC,SAAS,GAAG,CAAC,YAAY,EAAE,oBAAoB,EAAE,uBAAuB,EAAE,YAAY,CAAC;AAgB3F,SAASC,8BAA8BA,CAACC,qBAAqB,EAAE;EAC7D,OAAOA,qBAAqB,KAAK,IAAI,IAAIA,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGR,SAAS,CAACpB,OAAO,CAAC6B,EAAE,KAAK,SAAS;AACxI;AAGA,SAASC,mBAAmBA,CAACC,UAAU,EAAE;EACvC,OAAOA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAG,CAAC;AACtE;AACA,SAASC,WAAWA,CAACC,IAAI,EAAE;EAEzB,OAAO,OAAOC,MAAM,CAACD,IAAI,CAAC,CAACE,MAAM,KAAK,QAAQ;AAChD;AAAC,IA6GKC,QAAQ,aAAAC,oBAAA;EA6FZ,SAAAD,SAAYE,MAAM,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAhD,gBAAA,CAAAS,OAAA,QAAAoC,QAAA;IAClBG,KAAA,GAAA3C,UAAA,OAAAwC,QAAA,GAAME,MAAM;IACZC,KAAA,CAAKC,qBAAqB,GAAG,EAAE;IAC/BD,KAAA,CAAKE,WAAW,GAAG,UAAAC,GAAG,EAAI;MACxBH,KAAA,CAAKI,QAAQ,GAAGD,GAAG;IACrB,CAAC;IACDH,KAAA,CAAKK,QAAQ,GAAG,UAACX,IAAI,EAAEY,KAAK,EAAK;MAC/B,IAAId,UAAU,GAAGD,mBAAmB,CAACS,KAAA,CAAKO,KAAK,CAACf,UAAU,CAAC;MAC3D,IAAIA,UAAU,GAAG,CAAC,EAAE;QAClB,IAAIgB,GAAG,GAAG,EAAE;QACZ,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGjB,UAAU,EAAEiB,EAAE,EAAE,EAAE;UACtC,IAAIC,SAAS,GAAGJ,KAAK,GAAGd,UAAU,GAAGiB,EAAE;UACvC,IAAIC,SAAS,GAAGhB,IAAI,CAACE,MAAM,EAAE;YAC3B,IAAIe,KAAK,GAAGjB,IAAI,CAACgB,SAAS,CAAC;YAC3BF,GAAG,CAACI,IAAI,CAACD,KAAK,CAAC;UACjB;QACF;QACA,OAAOH,GAAG;MACZ,CAAC,MAAM;QACL,OAAOd,IAAI,CAACY,KAAK,CAAC;MACpB;IACF,CAAC;IACDN,KAAA,CAAKa,aAAa,GAAG,UAAAnB,IAAI,EAAI;MAO3B,IAAIA,IAAI,IAAI,IAAI,IAAID,WAAW,CAACC,IAAI,CAAC,EAAE;QACrC,IAAIF,UAAU,GAAGD,mBAAmB,CAACS,KAAA,CAAKO,KAAK,CAACf,UAAU,CAAC;QAC3D,OAAOA,UAAU,GAAG,CAAC,GAAGsB,IAAI,CAACC,IAAI,CAACrB,IAAI,CAACE,MAAM,GAAGJ,UAAU,CAAC,GAAGE,IAAI,CAACE,MAAM;MAC3E,CAAC,MAAM;QACL,OAAO,CAAC;MACV;IACF,CAAC;IACDI,KAAA,CAAKgB,aAAa,GAAG,UAACC,KAAK,EAAEX,KAAK,EAAK;MACrC,IAAIY,qBAAqB;MACzB,IAAI1B,UAAU,GAAGD,mBAAmB,CAACS,KAAA,CAAKO,KAAK,CAACf,UAAU,CAAC;MAC3D,IAAI2B,YAAY,GAAG,CAACD,qBAAqB,GAAGlB,KAAA,CAAKO,KAAK,CAACY,YAAY,MAAM,IAAI,IAAID,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGjC,gBAAgB,CAACkC,YAAY;MACzK,IAAI3B,UAAU,GAAG,CAAC,EAAE;QAClB,CAAC,CAAC,EAAEV,UAAU,CAACrB,OAAO,EAAE2D,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE,wFAAwF,GAAG,2DAA2D,EAAEzB,UAAU,CAAC;QACjN,OAAOyB,KAAK,CAACK,GAAG,CAAC,UAACC,IAAI,EAAEd,EAAE;UAAA,OAAKU,YAAY,CAACI,IAAI,EAAEjB,KAAK,GAAGd,UAAU,GAAGiB,EAAE,CAAC;QAAA,EAAC,CAACe,IAAI,CAAC,GAAG,CAAC;MACvF;MAGA,OAAOL,YAAY,CAACF,KAAK,EAAEX,KAAK,CAAC;IACnC,CAAC;IACDN,KAAA,CAAKyB,SAAS,GAAG,UAACC,iBAAiB,EAAEC,UAAU,EAAEC,kBAAkB,EAAEpC,UAAU,EAAEqC,SAAS,EAErF;MACH,IAAIC,IAAI,GAAGvC,mBAAmB,CAACC,UAAU,CAAC;MAC1C,IAAIuC,MAAM,GAAG,SAATA,MAAMA,CAAGxB,KAAK,EAAI;QACpB,IAAImB,iBAAiB,EAAE;UAIrB,OAAoB3C,KAAK,CAACiD,aAAa,CAACN,iBAAiB,EAAEnB,KAAK,CAAC;QACnE,CAAC,MAAM,IAAIoB,UAAU,EAAE;UAErB,OAAOA,UAAU,CAACpB,KAAK,CAAC;QAC1B,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC;MACD,IAAI0B,UAAU,GAAG,SAAbA,UAAUA,CAAGC,IAAI,EAAI;QACvB,IAAIJ,IAAI,GAAG,CAAC,EAAE;UACZ,IAAIK,MAAM,GAAGD,IAAI,CAACX,IAAI;YACpBa,MAAM,GAAGF,IAAI,CAAC5B,KAAK;UACrB,CAAC,CAAC,EAAExB,UAAU,CAACrB,OAAO,EAAE2D,KAAK,CAACC,OAAO,CAACc,MAAM,CAAC,EAAE,6CAA6C,CAAC;UAC7F,OAAoBpD,KAAK,CAACiD,aAAa,CAACtD,KAAK,CAACjB,OAAO,EAAE;YACrD4E,KAAK,EAAE,CAACC,MAAM,CAACC,GAAG,EAAEX,kBAAkB;UACxC,CAAC,EAAEO,MAAM,CAACb,GAAG,CAAC,UAACkB,EAAE,EAAE/B,EAAE,EAAK;YACxB,IAAIgC,OAAO,GAAGV,MAAM,CAAC;cAEnBR,IAAI,EAAEiB,EAAE;cACRlC,KAAK,EAAE8B,MAAM,GAAGN,IAAI,GAAGrB,EAAE;cACzBiC,UAAU,EAAER,IAAI,CAACQ;YACnB,CAAC,CAAC;YACF,OAAOD,OAAO,IAAI,IAAI,GAAgB1D,KAAK,CAACiD,aAAa,CAACjD,KAAK,CAAC4D,QAAQ,EAAE;cACxEC,GAAG,EAAEnC;YACP,CAAC,EAAEgC,OAAO,CAAC,GAAG,IAAI;UACpB,CAAC,CAAC,CAAC;QACL,CAAC,MAAM;UACL,OAAOV,MAAM,CAACG,IAAI,CAAC;QACrB;MACF,CAAC;MACD,OAAOR,iBAAiB,GAAG;QACzBA,iBAAiB,EAAEO;MACrB,CAAC,GAAG;QACFN,UAAU,EAAEM;MACd,CAAC;IACH,CAAC;IACDjC,KAAA,CAAK6C,iBAAiB,GAAG,CAAC,CAAC,EAAE3D,WAAW,CAACzB,OAAO,EAAEuC,KAAA,CAAKyB,SAAS,CAAC;IACjEzB,KAAA,CAAK8C,WAAW,CAAC9C,KAAA,CAAKO,KAAK,CAAC;IAC5B,IAAIP,KAAA,CAAKO,KAAK,CAACwC,8BAA8B,EAAE;MAC7C/C,KAAA,CAAKC,qBAAqB,GAAGD,KAAA,CAAKO,KAAK,CAACwC,8BAA8B,CAACzB,GAAG,CAAC,UAAA0B,IAAI;QAAA,OAAK;UAClFC,iBAAiB,EAAED,IAAI,CAACC,iBAAiB;UACzCC,sBAAsB,EAAElD,KAAA,CAAKmD,6BAA6B,CAACH,IAAI,CAACE,sBAAsB;QACxF,CAAC;MAAA,CAAC,CAAC;IACL,CAAC,MAAM,IAAIlD,KAAA,CAAKO,KAAK,CAAC2C,sBAAsB,EAAE;MAC5ClD,KAAA,CAAKC,qBAAqB,CAACW,IAAI,CAAC;QAI9BqC,iBAAiB,EAAEjD,KAAA,CAAKO,KAAK,CAAC0C,iBAAiB;QAC/CC,sBAAsB,EAAElD,KAAA,CAAKmD,6BAA6B,CAACnD,KAAA,CAAKO,KAAK,CAAC2C,sBAAsB;MAC9F,CAAC,CAAC;IACJ;IAAC,OAAAlD,KAAA;EACH;EAAC,IAAA5C,UAAA,CAAAK,OAAA,EAAAoC,QAAA,EAAAC,oBAAA;EAAA,WAAA7C,aAAA,CAAAQ,OAAA,EAAAoC,QAAA;IAAA+C,GAAA;IAAAQ,KAAA,EAtMD,SAAAC,WAAWA,CAACC,MAAM,EAAE;MAClB,IAAI,IAAI,CAAClD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACiD,WAAW,CAACC,MAAM,CAAC;MACnC;IACF;EAAC;IAAAV,GAAA;IAAAQ,KAAA,EAUD,SAAAG,aAAaA,CAACD,MAAM,EAAE;MACpB,IAAI,IAAI,CAAClD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACmD,aAAa,CAACD,MAAM,CAAC;MACrC;IACF;EAAC;IAAAV,GAAA;IAAAQ,KAAA,EAQD,SAAAI,YAAYA,CAACF,MAAM,EAAE;MACnB,IAAI,IAAI,CAAClD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACoD,YAAY,CAACF,MAAM,CAAC;MACpC;IACF;EAAC;IAAAV,GAAA;IAAAQ,KAAA,EAOD,SAAAK,cAAcA,CAACH,MAAM,EAAE;MACrB,IAAI,IAAI,CAAClD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACqD,cAAc,CAACH,MAAM,CAAC;MACtC;IACF;EAAC;IAAAV,GAAA;IAAAQ,KAAA,EAOD,SAAAM,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAACtD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACsD,iBAAiB,CAAC,CAAC;MACnC;IACF;EAAC;IAAAd,GAAA;IAAAQ,KAAA,EAOD,SAAAO,qBAAqBA,CAAA,EAAG;MACtB,IAAI,IAAI,CAACvD,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACuD,qBAAqB,CAAC,CAAC;MACvC;IACF;EAAC;IAAAf,GAAA;IAAAQ,KAAA,EAKD,SAAAQ,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACxD,QAAQ,EAAE;QACjB,OAAO,IAAI,CAACA,QAAQ,CAACwD,kBAAkB,CAAC,CAAC;MAC3C;IACF;EAAC;IAAAhB,GAAA;IAAAQ,KAAA,EAKD,SAAAS,kBAAkBA,CAAA,EAAG;MACnB,IAAI,IAAI,CAACzD,QAAQ,EAAE;QAGjB,OAAO,IAAI,CAACA,QAAQ,CAAC0D,YAAY,CAAC,CAAC;MACrC;IACF;EAAC;IAAAlB,GAAA;IAAAQ,KAAA,EACD,SAAAW,iBAAiBA,CAAA,EAAG;MAClB,IAAI,IAAI,CAAC3D,QAAQ,EAAE;QACjB,OAAO,IAAI,CAACA,QAAQ,CAAC2D,iBAAiB,CAAC,CAAC;MAC1C;IACF;EAAC;IAAAnB,GAAA;IAAAQ,KAAA,EAiHD,SAAAY,kBAAkBA,CAACC,SAAS,EAAE;MAC5B,CAAC,CAAC,EAAEnF,UAAU,CAACrB,OAAO,EAAEwG,SAAS,CAACzE,UAAU,KAAK,IAAI,CAACe,KAAK,CAACf,UAAU,EAAE,wFAAwF,GAAG,0EAA0E,CAAC;MAC9O,CAAC,CAAC,EAAEV,UAAU,CAACrB,OAAO,EAAEwG,SAAS,CAACf,sBAAsB,KAAK,IAAI,CAAC3C,KAAK,CAAC2C,sBAAsB,EAAE,6DAA6D,CAAC;MAC9J,CAAC,CAAC,EAAEpE,UAAU,CAACrB,OAAO,EAAE,CAAC,CAAC,CAAC,EAAEmB,WAAW,CAACnB,OAAO,EAAEwG,SAAS,CAAChB,iBAAiB,EAAE,IAAI,CAAC1C,KAAK,CAAC0C,iBAAiB,CAAC,EAAE,wDAAwD,CAAC;MACvK,CAAC,CAAC,EAAEnE,UAAU,CAACrB,OAAO,EAAEwG,SAAS,CAAClB,8BAA8B,KAAK,IAAI,CAACxC,KAAK,CAACwC,8BAA8B,EAAE,qEAAqE,CAAC;MACtL,IAAI,CAACD,WAAW,CAAC,IAAI,CAACvC,KAAK,CAAC;IAC9B;EAAC;IAAAqC,GAAA;IAAAQ,KAAA,EAED,SAAAN,WAAWA,CAACvC,KAAK,EAAE;MACjB,IAAI2D,OAAO,GAAG3D,KAAK,CAAC2D,OAAO;QACzBC,YAAY,GAAG5D,KAAK,CAAC4D,YAAY;QACjCC,UAAU,GAAG7D,KAAK,CAAC6D,UAAU;QAC7BxC,kBAAkB,GAAGrB,KAAK,CAACqB,kBAAkB;QAC7CsB,sBAAsB,GAAG3C,KAAK,CAAC2C,sBAAsB;QACrDH,8BAA8B,GAAGxC,KAAK,CAACwC,8BAA8B;MACvE,IAAIvD,UAAU,GAAGD,mBAAmB,CAAC,IAAI,CAACgB,KAAK,CAACf,UAAU,CAAC;MAC3D,CAAC,CAAC,EAAEV,UAAU,CAACrB,OAAO,EAAE,CAACyG,OAAO,IAAI,CAACC,YAAY,EAAE,gDAAgD,CAAC;MACpG,IAAI3E,UAAU,GAAG,CAAC,EAAE;QAClB,CAAC,CAAC,EAAEV,UAAU,CAACrB,OAAO,EAAE,CAAC2G,UAAU,EAAE,yCAAyC,CAAC;MACjF,CAAC,MAAM;QACL,CAAC,CAAC,EAAEtF,UAAU,CAACrB,OAAO,EAAE,CAACmE,kBAAkB,EAAE,0DAA0D,CAAC;MAC1G;MACA,CAAC,CAAC,EAAE9C,UAAU,CAACrB,OAAO,EAAE,EAAEyF,sBAAsB,IAAIH,8BAA8B,CAAC,EAAE,oEAAoE,GAAG,iCAAiC,CAAC;IAChM;EAAC;IAAAH,GAAA;IAAAQ,KAAA,EACD,SAAAiB,wBAAwBA,CAACC,GAAG,EAAEC,CAAC,EAAE;MAC/B,IAAIC,sBAAsB;MAC1B,IAAIhF,UAAU,GAAGD,mBAAmB,CAAC,IAAI,CAACgB,KAAK,CAACf,UAAU,CAAC;MAC3D,IAAI2B,YAAY,GAAG,CAACqD,sBAAsB,GAAG,IAAI,CAACjE,KAAK,CAACY,YAAY,MAAM,IAAI,IAAIqD,sBAAsB,KAAK,KAAK,CAAC,GAAGA,sBAAsB,GAAGvF,gBAAgB,CAACkC,YAAY;MAC5KoD,CAAC,CAAChD,IAAI,CAACkD,OAAO,CAAC,UAAClD,IAAI,EAAEmD,EAAE,EAAK;QAC3B,CAAC,CAAC,EAAE5F,UAAU,CAACrB,OAAO,EAAE8G,CAAC,CAACjE,KAAK,IAAI,IAAI,EAAE,gBAAgB,CAAC;QAC1D,IAAIA,KAAK,GAAGiE,CAAC,CAACjE,KAAK,GAAGd,UAAU,GAAGkF,EAAE;QACrCJ,GAAG,CAAC1D,IAAI,CAAC,CAAC,CAAC,EAAEnC,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEgB,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAE8G,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3EhD,IAAI,EAAJA,IAAI;UACJqB,GAAG,EAAEzB,YAAY,CAACI,IAAI,EAAEjB,KAAK,CAAC;UAC9BA,KAAK,EAALA;QACF,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ;EAAC;IAAAsC,GAAA;IAAAQ,KAAA,EACD,SAAAD,6BAA6BA,CAACD,sBAAsB,EAElD;MAAA,IAAAyB,MAAA;MACA,OAAO,UAAAzC,IAAI,EAAI;QACb,IAAI1C,UAAU,GAAGD,mBAAmB,CAACoF,MAAI,CAACpE,KAAK,CAACf,UAAU,CAAC;QAC3D,IAAI0D,sBAAsB,EAAE;UAC1B,IAAI1D,UAAU,GAAG,CAAC,EAAE;YAClB,IAAIoF,OAAO,GAAG,EAAE;YAChB,IAAIC,aAAa,GAAG,EAAE;YACtB3C,IAAI,CAAC2C,aAAa,CAACJ,OAAO,CAAC,UAAAF,CAAC;cAAA,OAAII,MAAI,CAACN,wBAAwB,CAACQ,aAAa,EAAEN,CAAC,CAAC;YAAA,EAAC;YAChFrC,IAAI,CAAC0C,OAAO,CAACH,OAAO,CAAC,UAAAF,CAAC;cAAA,OAAII,MAAI,CAACN,wBAAwB,CAACO,OAAO,EAAEL,CAAC,CAAC;YAAA,EAAC;YACpErB,sBAAsB,CAAC;cACrB2B,aAAa,EAAbA,aAAa;cACbD,OAAO,EAAPA;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACL1B,sBAAsB,CAAChB,IAAI,CAAC;UAC9B;QACF;MACF,CAAC;IACH;EAAC;IAAAU,GAAA;IAAAQ,KAAA,EAID,SAAArB,MAAMA,CAAA,EAAG;MACP,IAAI+C,WAAW,GAAG,IAAI,CAACvE,KAAK;QAC1Bf,UAAU,GAAGsF,WAAW,CAACtF,UAAU;QACnCoC,kBAAkB,GAAGkD,WAAW,CAAClD,kBAAkB;QACnDmD,sBAAsB,GAAGD,WAAW,CAACzF,qBAAqB;QAC1D2F,qBAAqB,GAAGF,WAAW,CAACG,UAAU;QAC9CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,qBAAqB;QAC7EE,SAAS,GAAG,CAAC,CAAC,EAAE1G,8BAA8B,CAACf,OAAO,EAAEqH,WAAW,EAAE3F,SAAS,CAAC;MACjF,IAAIgG,QAAQ,GAAGF,UAAU,GAAG,IAAI,CAACpC,iBAAiB,GAAG,IAAI,CAACpB,SAAS;MACnE,QAGE1C,KAAK,CAACiD,aAAa,CAAChD,gBAAgB,CAACvB,OAAO,EAAE,CAAC,CAAC,EAAEc,SAAS,CAACd,OAAO,EAAE,CAAC,CAAC,EAAEyH,SAAS,EAAE;UAClFhB,OAAO,EAAE,IAAI,CAAC7D,QAAQ;UACtB8D,YAAY,EAAE,IAAI,CAACtD,aAAa;UAChCM,YAAY,EAAE,IAAI,CAACH,aAAa;UAChCb,GAAG,EAAE,IAAI,CAACD,WAAW;UACrB6C,8BAA8B,EAAE,IAAI,CAAC9C,qBAAqB;UAC1DZ,qBAAqB,EAAED,8BAA8B,CAAC2F,sBAAsB;QAC9E,CAAC,EAAEI,QAAQ,CAAC,IAAI,CAAC5E,KAAK,CAACmB,iBAAiB,EAAE,IAAI,CAACnB,KAAK,CAACoB,UAAU,EAAEC,kBAAkB,EAAEpC,UAAU,EAAE,IAAI,CAACe,KAAK,CAACsB,SAAS,CAAC,CAAC;MAAC;IAE5H;EAAC;AAAA,EAhSoB9C,KAAK,CAACqG,aAAa;AAkS1C,IAAI9C,MAAM,GAAG3D,WAAW,CAAClB,OAAO,CAAC4H,MAAM,CAAC;EACtC9C,GAAG,EAAE;IACH+C,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAGlH,OAAO,CAACZ,OAAO,GAAGoC,QAAQ;AACzC2F,MAAM,CAACnH,OAAO,GAAGA,OAAO,CAACZ,OAAO", "ignoreList": []}