8f971d59c0fa85e90371e113efb8db2f
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _createElement = _interopRequireDefault(require("../createElement"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["aria-readonly", "color", "disabled", "onChange", "onValueChange", "readOnly", "style", "value"];
var CheckBox = React.forwardRef(function (props, forwardedRef) {
  var ariaReadOnly = props['aria-readonly'],
    color = props.color,
    disabled = props.disabled,
    onChange = props.onChange,
    onValueChange = props.onValueChange,
    readOnly = props.readOnly,
    style = props.style,
    value = props.value,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  function handleChange(event) {
    var value = event.nativeEvent.target.checked;
    event.nativeEvent.value = value;
    onChange && onChange(event);
    onValueChange && onValueChange(value);
  }
  var fakeControl = React.createElement(_View.default, {
    style: [styles.fakeControl, value && styles.fakeControlChecked, value && color && {
      backgroundColor: color,
      borderColor: color
    }, disabled && styles.fakeControlDisabled, value && disabled && styles.fakeControlCheckedAndDisabled]
  });
  var nativeControl = (0, _createElement.default)('input', {
    checked: value,
    disabled: disabled,
    onChange: handleChange,
    readOnly: readOnly === true || ariaReadOnly === true || other.accessibilityReadOnly === true,
    ref: forwardedRef,
    style: [styles.nativeControl, styles.cursorInherit],
    type: 'checkbox'
  });
  return React.createElement(_View.default, (0, _extends2.default)({}, other, {
    "aria-disabled": disabled,
    "aria-readonly": ariaReadOnly,
    style: [styles.root, style, disabled && styles.cursorDefault]
  }), fakeControl, nativeControl);
});
CheckBox.displayName = 'CheckBox';
var styles = _StyleSheet.default.create({
  root: {
    cursor: 'pointer',
    height: 16,
    userSelect: 'none',
    width: 16
  },
  cursorDefault: {
    cursor: 'default'
  },
  cursorInherit: {
    cursor: 'inherit'
  },
  fakeControl: {
    alignItems: 'center',
    backgroundColor: '#fff',
    borderColor: '#657786',
    borderRadius: 2,
    borderStyle: 'solid',
    borderWidth: 2,
    height: '100%',
    justifyContent: 'center',
    width: '100%'
  },
  fakeControlChecked: {
    backgroundColor: '#009688',
    backgroundImage: 'url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgMSAxIgogICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWluWU1pbiBtZWV0Ij4KICA8cGF0aAogICAgIGQ9Ik0gMC4wNDAzODA1OSwwLjYyNjc3NjcgMC4xNDY0NDY2MSwwLjUyMDcxMDY4IDAuNDI5Mjg5MzIsMC44MDM1NTMzOSAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IE0gMC4yMTcxNTcyOSwwLjgwMzU1MzM5IDAuODUzNTUzMzksMC4xNjcxNTcyOSAwLjk1OTYxOTQxLDAuMjczMjIzMyAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IgogICAgIGlkPSJyZWN0Mzc4MCIKICAgICBzdHlsZT0iZmlsbDojZmZmZmZmO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lIiAvPgo8L3N2Zz4K")',
    backgroundRepeat: 'no-repeat',
    borderColor: '#009688'
  },
  fakeControlDisabled: {
    borderColor: '#CCD6DD'
  },
  fakeControlCheckedAndDisabled: {
    backgroundColor: '#AAB8C2',
    borderColor: '#AAB8C2'
  },
  nativeControl: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _StyleSheet.default.absoluteFillObject), {}, {
    height: '100%',
    margin: 0,
    appearance: 'none',
    padding: 0,
    width: '100%'
  })
});
var _default = exports.default = CheckBox;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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