/**
 * Integration Demo Component
 * Demonstrates Firebase FCM, PostHog Analytics, Sentry Error Monitoring, 
 * Magic.link Auth, and Stripe Payments
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
} from 'react-native';
import { notificationService } from '@/services/notifications/NotificationService';
import { pushNotificationBackend } from '@/services/notifications/PushNotificationBackend';
import { postHogService } from '@/services/analytics/PostHogService';
import { sentryService } from '@/services/monitoring/SentryService';
import { magicLinkService } from '@/services/auth/MagicLinkService';
import { useAuth } from '@/contexts/AuthContext';

export default function IntegrationDemo() {
  const [email, setEmail] = useState('');
  const [notificationTitle, setNotificationTitle] = useState('Test Notification');
  const [notificationBody, setNotificationBody] = useState('This is a test notification');
  const [isLoading, setIsLoading] = useState(false);
  const { user, signInWithMagicLink } = useAuth();

  useEffect(() => {
    initializeServices();
  }, []);

  const initializeServices = async () => {
    try {
      await notificationService.initialize();
      await postHogService.initialize();
      await sentryService.initialize();
      await magicLinkService.initialize();
      console.log('All services initialized successfully');
    } catch (error) {
      console.error('Failed to initialize services:', error);
    }
  };

  // Notification Examples
  const requestNotificationPermissions = async () => {
    try {
      const permissions = await notificationService.requestPermissions();
      Alert.alert('Permissions', `Status: ${permissions.status}`);
      
      // Track event
      postHogService.trackFeatureUsage('notification_permissions_requested');
    } catch (error) {
      sentryService.captureException(error as Error, {
        tags: { feature: 'notifications' },
      });
    }
  };

  const sendLocalNotification = async () => {
    try {
      const notificationId = await notificationService.sendLocalNotification({
        title: notificationTitle,
        body: notificationBody,
        data: { type: 'demo', timestamp: Date.now() },
      });
      
      Alert.alert('Success', `Local notification sent: ${notificationId}`);
      
      // Track event
      postHogService.trackFeatureUsage('local_notification_sent');
    } catch (error) {
      sentryService.captureException(error as Error, {
        tags: { feature: 'notifications' },
      });
    }
  };

  const scheduleNotification = async () => {
    try {
      const notificationId = await notificationService.scheduleNotification(
        {
          title: 'Scheduled Notification',
          body: 'This notification was scheduled for 10 seconds from now',
          data: { type: 'scheduled' },
        },
        10 // 10 seconds from now
      );
      
      Alert.alert('Success', `Notification scheduled: ${notificationId}`);
      
      // Track event
      postHogService.trackFeatureUsage('notification_scheduled');
    } catch (error) {
      sentryService.captureException(error as Error, {
        tags: { feature: 'notifications' },
      });
    }
  };

  const sendPushNotification = async () => {
    try {
      if (!user) {
        Alert.alert('Error', 'Please login first');
        return;
      }

      const results = await pushNotificationBackend.sendNotificationToUser(user.id, {
        title: notificationTitle,
        body: notificationBody,
        type: 'demo',
      });
      
      Alert.alert('Push Sent', `Results: ${JSON.stringify(results)}`);
      
      // Track event
      postHogService.trackFeatureUsage('push_notification_sent');
    } catch (error) {
      sentryService.captureException(error as Error, {
        tags: { feature: 'push_notifications' },
      });
    }
  };

  // Analytics Examples
  const trackCustomEvent = () => {
    postHogService.capture('demo_button_clicked', {
      button_name: 'custom_event',
      timestamp: new Date().toISOString(),
      user_id: user?.id,
    });
    
    Alert.alert('Analytics', 'Custom event tracked in PostHog');
  };

  const trackVideoUpload = () => {
    postHogService.trackVideoUpload('forehand', 120, 50 * 1024 * 1024); // 50MB file
    Alert.alert('Analytics', 'Video upload event tracked');
  };

  const trackTrainingSession = () => {
    postHogService.trackTrainingSession('serve_practice', 1800, true); // 30 minutes, completed
    Alert.alert('Analytics', 'Training session tracked');
  };

  // Error Monitoring Examples
  const captureTestError = () => {
    try {
      throw new Error('This is a test error for Sentry');
    } catch (error) {
      sentryService.captureException(error as Error, {
        tags: { 
          error_type: 'demo',
          feature: 'error_monitoring' 
        },
        extra: {
          demo_data: 'This is extra context for the error',
          timestamp: Date.now(),
        },
      });
      
      Alert.alert('Error Monitoring', 'Test error captured in Sentry');
    }
  };

  const captureTestMessage = () => {
    sentryService.captureMessage('This is a test message for Sentry', 'info', {
      tags: { feature: 'demo' },
      extra: { message_type: 'demo' },
    });
    
    Alert.alert('Error Monitoring', 'Test message captured in Sentry');
  };

  const addBreadcrumb = () => {
    sentryService.addBreadcrumb({
      message: 'User clicked demo breadcrumb button',
      category: 'user_action',
      level: 'info',
      data: { button: 'breadcrumb_demo' },
    });
    
    Alert.alert('Error Monitoring', 'Breadcrumb added to Sentry');
  };

  // Authentication Examples
  const loginWithMagicLink = async () => {
    if (!email) {
      Alert.alert('Error', 'Please enter an email address');
      return;
    }

    setIsLoading(true);
    try {
      const result = await signInWithMagicLink(email);
      if (result.error) {
        Alert.alert('Login Failed', result.error);
      } else {
        Alert.alert('Success', 'Magic link sent! Check your email.');
        
        // Track event
        postHogService.trackLogin('magic_link');
      }
    } catch (error) {
      sentryService.trackAuthError(error as Error, 'magic_link');
      Alert.alert('Error', 'Failed to send magic link');
    } finally {
      setIsLoading(false);
    }
  };

  // Performance Monitoring Example
  const measurePerformance = async () => {
    try {
      const result = await sentryService.measurePerformance(
        'demo_operation',
        async () => {
          // Simulate some work
          await new Promise(resolve => setTimeout(resolve, 1000));
          return 'Operation completed';
        },
        {
          description: 'Demo performance measurement',
          tags: { feature: 'demo' },
        }
      );
      
      Alert.alert('Performance', `Operation result: ${result}`);
    } catch (error) {
      Alert.alert('Error', 'Performance measurement failed');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>🚀 Integration Demo</Text>
      
      {/* Notifications Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📱 Notifications (Firebase FCM + Expo)</Text>
        
        <TextInput
          style={styles.input}
          placeholder="Notification Title"
          value={notificationTitle}
          onChangeText={setNotificationTitle}
        />
        
        <TextInput
          style={styles.input}
          placeholder="Notification Body"
          value={notificationBody}
          onChangeText={setNotificationBody}
          multiline
        />
        
        <TouchableOpacity style={styles.button} onPress={requestNotificationPermissions}>
          <Text style={styles.buttonText}>Request Permissions</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={sendLocalNotification}>
          <Text style={styles.buttonText}>Send Local Notification</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={scheduleNotification}>
          <Text style={styles.buttonText}>Schedule Notification (10s)</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={sendPushNotification}>
          <Text style={styles.buttonText}>Send Push Notification</Text>
        </TouchableOpacity>
      </View>

      {/* Analytics Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>📊 Analytics (PostHog)</Text>
        
        <TouchableOpacity style={styles.button} onPress={trackCustomEvent}>
          <Text style={styles.buttonText}>Track Custom Event</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={trackVideoUpload}>
          <Text style={styles.buttonText}>Track Video Upload</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={trackTrainingSession}>
          <Text style={styles.buttonText}>Track Training Session</Text>
        </TouchableOpacity>
      </View>

      {/* Error Monitoring Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🐛 Error Monitoring (Sentry)</Text>
        
        <TouchableOpacity style={styles.button} onPress={captureTestError}>
          <Text style={styles.buttonText}>Capture Test Error</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={captureTestMessage}>
          <Text style={styles.buttonText}>Capture Test Message</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={addBreadcrumb}>
          <Text style={styles.buttonText}>Add Breadcrumb</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.button} onPress={measurePerformance}>
          <Text style={styles.buttonText}>Measure Performance</Text>
        </TouchableOpacity>
      </View>

      {/* Authentication Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🔐 Authentication (Magic.link)</Text>
        
        <TextInput
          style={styles.input}
          placeholder="Email for Magic Link"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
        />
        
        <TouchableOpacity 
          style={[styles.button, isLoading && styles.buttonDisabled]} 
          onPress={loginWithMagicLink}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Sending...' : 'Send Magic Link'}
          </Text>
        </TouchableOpacity>
        
        {user && (
          <Text style={styles.userInfo}>
            Logged in as: {user.email}
          </Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 30,
    color: '#333',
  },
  section: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    marginBottom: 10,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 10,
  },
  buttonDisabled: {
    backgroundColor: '#999',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  userInfo: {
    marginTop: 10,
    padding: 10,
    backgroundColor: '#e8f5e8',
    borderRadius: 8,
    color: '#2d5a2d',
    textAlign: 'center',
  },
});
