{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_PooledClass", "twoArgumentPooler", "Position", "left", "top", "prototype", "destructor", "addPoolingTo", "_default", "module"], "sources": ["Position.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _PooledClass = _interopRequireDefault(require(\"../../vendor/react-native/PooledClass\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar twoArgumentPooler = _PooledClass.default.twoArgumentPooler;\nfunction Position(left, top) {\n  this.left = left;\n  this.top = top;\n}\nPosition.prototype.destructor = function () {\n  this.left = null;\n  this.top = null;\n};\n_PooledClass.default.addPoolingTo(Position, twoArgumentPooler);\nvar _default = exports.default = Position;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,YAAY,GAAGL,sBAAsB,CAACC,OAAO,wCAAwC,CAAC,CAAC;AAU3F,IAAIK,iBAAiB,GAAGD,YAAY,CAACH,OAAO,CAACI,iBAAiB;AAC9D,SAASC,QAAQA,CAACC,IAAI,EAAEC,GAAG,EAAE;EAC3B,IAAI,CAACD,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACC,GAAG,GAAGA,GAAG;AAChB;AACAF,QAAQ,CAACG,SAAS,CAACC,UAAU,GAAG,YAAY;EAC1C,IAAI,CAACH,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,GAAG,GAAG,IAAI;AACjB,CAAC;AACDJ,YAAY,CAACH,OAAO,CAACU,YAAY,CAACL,QAAQ,EAAED,iBAAiB,CAAC;AAC9D,IAAIO,QAAQ,GAAGV,OAAO,CAACD,OAAO,GAAGK,QAAQ;AACzCO,MAAM,CAACX,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}