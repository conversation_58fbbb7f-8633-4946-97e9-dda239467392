181f4db3906dd706cf704dec396f21cf
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1bpwe9i7vk() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\optimized\\usePerformanceData.ts";
  var hash = "b251df93fecf3249bd7e824b2bf2f502d91d8802";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\optimized\\usePerformanceData.ts",
    statementMap: {
      "0": {
        start: {
          line: 47,
          column: 6
        },
        end: {
          line: 47,
          column: 13
        }
      },
      "1": {
        start: {
          line: 49,
          column: 19
        },
        end: {
          line: 49,
          column: 28
        }
      },
      "2": {
        start: {
          line: 50,
          column: 32
        },
        end: {
          line: 50,
          column: 47
        }
      },
      "3": {
        start: {
          line: 51,
          column: 28
        },
        end: {
          line: 51,
          column: 57
        }
      },
      "4": {
        start: {
          line: 52,
          column: 23
        },
        end: {
          line: 52,
          column: 40
        }
      },
      "5": {
        start: {
          line: 55,
          column: 19
        },
        end: {
          line: 58,
          column: 3
        }
      },
      "6": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 47
        }
      },
      "7": {
        start: {
          line: 68,
          column: 6
        },
        end: {
          line: 76,
          column: 3
        }
      },
      "8": {
        start: {
          line: 70,
          column: 10
        },
        end: {
          line: 70,
          column: 39
        }
      },
      "9": {
        start: {
          line: 79,
          column: 38
        },
        end: {
          line: 158,
          column: 16
        }
      },
      "10": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 82,
          column: 5
        }
      },
      "11": {
        start: {
          line: 81,
          column: 6
        },
        end: {
          line: 81,
          column: 48
        }
      },
      "12": {
        start: {
          line: 84,
          column: 22
        },
        end: {
          line: 84,
          column: 32
        }
      },
      "13": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 157,
          column: 5
        }
      },
      "14": {
        start: {
          line: 88,
          column: 51
        },
        end: {
          line: 129,
          column: 17
        }
      },
      "15": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 133,
          column: 7
        }
      },
      "16": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 132,
          column: 82
        }
      },
      "17": {
        start: {
          line: 135,
          column: 24
        },
        end: {
          line: 135,
          column: 46
        }
      },
      "18": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 140,
          column: 7
        }
      },
      "19": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 139,
          column: 68
        }
      },
      "20": {
        start: {
          line: 142,
          column: 43
        },
        end: {
          line: 148,
          column: 7
        }
      },
      "21": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 40
        }
      },
      "22": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 151,
          column: 20
        }
      },
      "23": {
        start: {
          line: 154,
          column: 27
        },
        end: {
          line: 154,
          column: 98
        }
      },
      "24": {
        start: {
          line: 155,
          column: 6
        },
        end: {
          line: 155,
          column: 58
        }
      },
      "25": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 36
        }
      },
      "26": {
        start: {
          line: 161,
          column: 18
        },
        end: {
          line: 165,
          column: 45
        }
      },
      "27": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 162,
          column: 33
        }
      },
      "28": {
        start: {
          line: 162,
          column: 21
        },
        end: {
          line: 162,
          column: 33
        }
      },
      "29": {
        start: {
          line: 163,
          column: 16
        },
        end: {
          line: 163,
          column: 51
        }
      },
      "30": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 164,
          column: 30
        }
      },
      "31": {
        start: {
          line: 168,
          column: 20
        },
        end: {
          line: 186,
          column: 38
        }
      },
      "32": {
        start: {
          line: 169,
          column: 4
        },
        end: {
          line: 169,
          column: 22
        }
      },
      "33": {
        start: {
          line: 169,
          column: 15
        },
        end: {
          line: 169,
          column: 22
        }
      },
      "34": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 172,
          column: 34
        }
      },
      "35": {
        start: {
          line: 172,
          column: 27
        },
        end: {
          line: 172,
          column: 34
        }
      },
      "36": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 185,
          column: 5
        }
      },
      "37": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 23
        }
      },
      "38": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 176,
          column: 21
        }
      },
      "39": {
        start: {
          line: 178,
          column: 6
        },
        end: {
          line: 178,
          column: 35
        }
      },
      "40": {
        start: {
          line: 181,
          column: 27
        },
        end: {
          line: 181,
          column: 86
        }
      },
      "41": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 29
        }
      },
      "42": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 184,
          column: 24
        }
      },
      "43": {
        start: {
          line: 189,
          column: 22
        },
        end: {
          line: 191,
          column: 17
        }
      },
      "44": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 190,
          column: 26
        }
      },
      "45": {
        start: {
          line: 194,
          column: 2
        },
        end: {
          line: 204,
          column: 63
        }
      },
      "46": {
        start: {
          line: 195,
          column: 4
        },
        end: {
          line: 195,
          column: 38
        }
      },
      "47": {
        start: {
          line: 195,
          column: 31
        },
        end: {
          line: 195,
          column: 38
        }
      },
      "48": {
        start: {
          line: 197,
          column: 21
        },
        end: {
          line: 201,
          column: 23
        }
      },
      "49": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 200,
          column: 7
        }
      },
      "50": {
        start: {
          line: 199,
          column: 8
        },
        end: {
          line: 199,
          column: 25
        }
      },
      "51": {
        start: {
          line: 203,
          column: 4
        },
        end: {
          line: 203,
          column: 41
        }
      },
      "52": {
        start: {
          line: 203,
          column: 17
        },
        end: {
          line: 203,
          column: 40
        }
      },
      "53": {
        start: {
          line: 207,
          column: 2
        },
        end: {
          line: 225,
          column: 5
        }
      },
      "54": {
        start: {
          line: 207,
          column: 24
        },
        end: {
          line: 215,
          column: 3
        }
      },
      "55": {
        start: {
          line: 232,
          column: 2
        },
        end: {
          line: 282,
          column: 19
        }
      },
      "56": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 233,
          column: 33
        }
      },
      "57": {
        start: {
          line: 233,
          column: 21
        },
        end: {
          line: 233,
          column: 33
        }
      },
      "58": {
        start: {
          line: 235,
          column: 46
        },
        end: {
          line: 235,
          column: 56
        }
      },
      "59": {
        start: {
          line: 238,
          column: 26
        },
        end: {
          line: 238,
          column: 45
        }
      },
      "60": {
        start: {
          line: 239,
          column: 27
        },
        end: {
          line: 239,
          column: 48
        }
      },
      "61": {
        start: {
          line: 240,
          column: 29
        },
        end: {
          line: 240,
          column: 42
        }
      },
      "62": {
        start: {
          line: 242,
          column: 20
        },
        end: {
          line: 244,
          column: 9
        }
      },
      "63": {
        start: {
          line: 243,
          column: 29
        },
        end: {
          line: 243,
          column: 47
        }
      },
      "64": {
        start: {
          line: 246,
          column: 32
        },
        end: {
          line: 248,
          column: 9
        }
      },
      "65": {
        start: {
          line: 247,
          column: 36
        },
        end: {
          line: 247,
          column: 64
        }
      },
      "66": {
        start: {
          line: 250,
          column: 23
        },
        end: {
          line: 269,
          column: 13
        }
      },
      "67": {
        start: {
          line: 251,
          column: 22
        },
        end: {
          line: 251,
          column: 35
        }
      },
      "68": {
        start: {
          line: 252,
          column: 23
        },
        end: {
          line: 252,
          column: 36
        }
      },
      "69": {
        start: {
          line: 253,
          column: 21
        },
        end: {
          line: 253,
          column: 103
        }
      },
      "70": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 268,
          column: 36
        }
      },
      "71": {
        start: {
          line: 256,
          column: 29
        },
        end: {
          line: 256,
          column: 50
        }
      },
      "72": {
        start: {
          line: 257,
          column: 30
        },
        end: {
          line: 257,
          column: 52
        }
      },
      "73": {
        start: {
          line: 258,
          column: 23
        },
        end: {
          line: 258,
          column: 51
        }
      },
      "74": {
        start: {
          line: 260,
          column: 8
        },
        end: {
          line: 265,
          column: 10
        }
      },
      "75": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 267,
          column: 22
        }
      },
      "76": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 281,
          column: 6
        }
      },
      "77": {
        start: {
          line: 292,
          column: 19
        },
        end: {
          line: 292,
          column: 39
        }
      },
      "78": {
        start: {
          line: 294,
          column: 2
        },
        end: {
          line: 294,
          column: 56
        }
      },
      "79": {
        start: {
          line: 294,
          column: 23
        },
        end: {
          line: 294,
          column: 37
        }
      }
    },
    fnMap: {
      "0": {
        name: "usePerformanceData",
        decl: {
          start: {
            line: 41,
            column: 16
          },
          end: {
            line: 41,
            column: 34
          }
        },
        loc: {
          start: {
            line: 41,
            column: 102
          },
          end: {
            line: 226,
            column: 1
          }
        },
        line: 41
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 55,
            column: 27
          },
          end: {
            line: 55,
            column: 28
          }
        },
        loc: {
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 56,
            column: 47
          }
        },
        line: 56
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        loc: {
          start: {
            line: 70,
            column: 10
          },
          end: {
            line: 70,
            column: 39
          }
        },
        line: 70
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 79,
            column: 50
          },
          end: {
            line: 79,
            column: 51
          }
        },
        loc: {
          start: {
            line: 79,
            column: 93
          },
          end: {
            line: 158,
            column: 3
          }
        },
        line: 79
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 161,
            column: 26
          },
          end: {
            line: 161,
            column: 27
          }
        },
        loc: {
          start: {
            line: 161,
            column: 32
          },
          end: {
            line: 165,
            column: 3
          }
        },
        line: 161
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 168,
            column: 32
          },
          end: {
            line: 168,
            column: 33
          }
        },
        loc: {
          start: {
            line: 168,
            column: 57
          },
          end: {
            line: 186,
            column: 3
          }
        },
        line: 168
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 189,
            column: 34
          },
          end: {
            line: 189,
            column: 35
          }
        },
        loc: {
          start: {
            line: 189,
            column: 46
          },
          end: {
            line: 191,
            column: 3
          }
        },
        line: 189
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 194,
            column: 18
          },
          end: {
            line: 194,
            column: 19
          }
        },
        loc: {
          start: {
            line: 194,
            column: 24
          },
          end: {
            line: 204,
            column: 3
          }
        },
        line: 194
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 197,
            column: 33
          },
          end: {
            line: 197,
            column: 34
          }
        },
        loc: {
          start: {
            line: 197,
            column: 39
          },
          end: {
            line: 201,
            column: 5
          }
        },
        line: 197
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 203,
            column: 11
          },
          end: {
            line: 203,
            column: 12
          }
        },
        loc: {
          start: {
            line: 203,
            column: 17
          },
          end: {
            line: 203,
            column: 40
          }
        },
        line: 203
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 207,
            column: 17
          },
          end: {
            line: 207,
            column: 18
          }
        },
        loc: {
          start: {
            line: 207,
            column: 24
          },
          end: {
            line: 215,
            column: 3
          }
        },
        line: 207
      },
      "11": {
        name: "usePerformanceMetrics",
        decl: {
          start: {
            line: 231,
            column: 16
          },
          end: {
            line: 231,
            column: 37
          }
        },
        loc: {
          start: {
            line: 231,
            column: 73
          },
          end: {
            line: 283,
            column: 1
          }
        },
        line: 231
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 232,
            column: 17
          },
          end: {
            line: 232,
            column: 18
          }
        },
        loc: {
          start: {
            line: 232,
            column: 23
          },
          end: {
            line: 282,
            column: 3
          }
        },
        line: 232
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 243,
            column: 24
          },
          end: {
            line: 243,
            column: 25
          }
        },
        loc: {
          start: {
            line: 243,
            column: 29
          },
          end: {
            line: 243,
            column: 47
          }
        },
        line: 243
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 247,
            column: 24
          },
          end: {
            line: 247,
            column: 25
          }
        },
        loc: {
          start: {
            line: 247,
            column: 36
          },
          end: {
            line: 247,
            column: 64
          }
        },
        line: 247
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 250,
            column: 49
          },
          end: {
            line: 250,
            column: 50
          }
        },
        loc: {
          start: {
            line: 250,
            column: 55
          },
          end: {
            line: 269,
            column: 5
          }
        },
        line: 250
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 255,
            column: 27
          },
          end: {
            line: 255,
            column: 28
          }
        },
        loc: {
          start: {
            line: 255,
            column: 46
          },
          end: {
            line: 268,
            column: 7
          }
        },
        line: 255
      },
      "17": {
        name: "usePerformanceDataSelector",
        decl: {
          start: {
            line: 288,
            column: 16
          },
          end: {
            line: 288,
            column: 42
          }
        },
        loc: {
          start: {
            line: 291,
            column: 2
          },
          end: {
            line: 295,
            column: 1
          }
        },
        line: 291
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 294,
            column: 17
          },
          end: {
            line: 294,
            column: 18
          }
        },
        loc: {
          start: {
            line: 294,
            column: 23
          },
          end: {
            line: 294,
            column: 37
          }
        },
        line: 294
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 41,
            column: 35
          },
          end: {
            line: 41,
            column: 74
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 41,
            column: 72
          },
          end: {
            line: 41,
            column: 74
          }
        }],
        line: 41
      },
      "1": {
        loc: {
          start: {
            line: 43,
            column: 4
          },
          end: {
            line: 43,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 43,
            column: 18
          },
          end: {
            line: 43,
            column: 22
          }
        }],
        line: 43
      },
      "2": {
        loc: {
          start: {
            line: 44,
            column: 4
          },
          end: {
            line: 44,
            column: 25
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 44,
            column: 19
          },
          end: {
            line: 44,
            column: 25
          }
        }],
        line: 44
      },
      "3": {
        loc: {
          start: {
            line: 45,
            column: 4
          },
          end: {
            line: 45,
            column: 23
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 45,
            column: 18
          },
          end: {
            line: 45,
            column: 23
          }
        }],
        line: 45
      },
      "4": {
        loc: {
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 46,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 46,
            column: 22
          },
          end: {
            line: 46,
            column: 28
          }
        }],
        line: 46
      },
      "5": {
        loc: {
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 56,
            column: 47
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 56,
            column: 11
          },
          end: {
            line: 56,
            column: 40
          }
        }, {
          start: {
            line: 56,
            column: 43
          },
          end: {
            line: 56,
            column: 47
          }
        }],
        line: 56
      },
      "6": {
        loc: {
          start: {
            line: 69,
            column: 4
          },
          end: {
            line: 69,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 4
          },
          end: {
            line: 69,
            column: 12
          }
        }, {
          start: {
            line: 69,
            column: 16
          },
          end: {
            line: 69,
            column: 44
          }
        }],
        line: 69
      },
      "7": {
        loc: {
          start: {
            line: 74,
            column: 15
          },
          end: {
            line: 74,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 74,
            column: 15
          },
          end: {
            line: 74,
            column: 26
          }
        }, {
          start: {
            line: 74,
            column: 30
          },
          end: {
            line: 74,
            column: 36
          }
        }],
        line: 74
      },
      "8": {
        loc: {
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 80,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 80
      },
      "9": {
        loc: {
          start: {
            line: 131,
            column: 6
          },
          end: {
            line: 133,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 6
          },
          end: {
            line: 133,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "10": {
        loc: {
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 140,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 140,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "11": {
        loc: {
          start: {
            line: 143,
            column: 17
          },
          end: {
            line: 143,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 143,
            column: 17
          },
          end: {
            line: 143,
            column: 40
          }
        }, {
          start: {
            line: 143,
            column: 44
          },
          end: {
            line: 143,
            column: 46
          }
        }],
        line: 143
      },
      "12": {
        loc: {
          start: {
            line: 144,
            column: 18
          },
          end: {
            line: 144,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 144,
            column: 18
          },
          end: {
            line: 144,
            column: 45
          }
        }, {
          start: {
            line: 144,
            column: 49
          },
          end: {
            line: 144,
            column: 51
          }
        }],
        line: 144
      },
      "13": {
        loc: {
          start: {
            line: 145,
            column: 20
          },
          end: {
            line: 145,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 145,
            column: 20
          },
          end: {
            line: 145,
            column: 41
          }
        }, {
          start: {
            line: 145,
            column: 45
          },
          end: {
            line: 145,
            column: 47
          }
        }],
        line: 145
      },
      "14": {
        loc: {
          start: {
            line: 154,
            column: 27
          },
          end: {
            line: 154,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 50
          },
          end: {
            line: 154,
            column: 61
          }
        }, {
          start: {
            line: 154,
            column: 64
          },
          end: {
            line: 154,
            column: 98
          }
        }],
        line: 154
      },
      "15": {
        loc: {
          start: {
            line: 162,
            column: 4
          },
          end: {
            line: 162,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 162,
            column: 4
          },
          end: {
            line: 162,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 162
      },
      "16": {
        loc: {
          start: {
            line: 168,
            column: 39
          },
          end: {
            line: 168,
            column: 52
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 168,
            column: 47
          },
          end: {
            line: 168,
            column: 52
          }
        }],
        line: 168
      },
      "17": {
        loc: {
          start: {
            line: 169,
            column: 4
          },
          end: {
            line: 169,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 169,
            column: 4
          },
          end: {
            line: 169,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 169
      },
      "18": {
        loc: {
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 172,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 172,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "19": {
        loc: {
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 172,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 172,
            column: 8
          },
          end: {
            line: 172,
            column: 15
          }
        }, {
          start: {
            line: 172,
            column: 19
          },
          end: {
            line: 172,
            column: 25
          }
        }],
        line: 172
      },
      "20": {
        loc: {
          start: {
            line: 181,
            column: 27
          },
          end: {
            line: 181,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 181,
            column: 50
          },
          end: {
            line: 181,
            column: 61
          }
        }, {
          start: {
            line: 181,
            column: 64
          },
          end: {
            line: 181,
            column: 86
          }
        }],
        line: 181
      },
      "21": {
        loc: {
          start: {
            line: 195,
            column: 4
          },
          end: {
            line: 195,
            column: 38
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 4
          },
          end: {
            line: 195,
            column: 38
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 195
      },
      "22": {
        loc: {
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 195,
            column: 29
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 195,
            column: 8
          },
          end: {
            line: 195,
            column: 20
          }
        }, {
          start: {
            line: 195,
            column: 24
          },
          end: {
            line: 195,
            column: 29
          }
        }],
        line: 195
      },
      "23": {
        loc: {
          start: {
            line: 198,
            column: 6
          },
          end: {
            line: 200,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 6
          },
          end: {
            line: 200,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 198
      },
      "24": {
        loc: {
          start: {
            line: 209,
            column: 13
          },
          end: {
            line: 209,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 13
          },
          end: {
            line: 209,
            column: 20
          }
        }, {
          start: {
            line: 209,
            column: 24
          },
          end: {
            line: 209,
            column: 36
          }
        }],
        line: 209
      },
      "25": {
        loc: {
          start: {
            line: 210,
            column: 11
          },
          end: {
            line: 210,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 210,
            column: 11
          },
          end: {
            line: 210,
            column: 16
          }
        }, {
          start: {
            line: 210,
            column: 20
          },
          end: {
            line: 210,
            column: 39
          }
        }, {
          start: {
            line: 210,
            column: 43
          },
          end: {
            line: 210,
            column: 47
          }
        }],
        line: 210
      },
      "26": {
        loc: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 233,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 233,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "27": {
        loc: {
          start: {
            line: 242,
            column: 20
          },
          end: {
            line: 244,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 243,
            column: 8
          },
          end: {
            line: 243,
            column: 79
          }
        }, {
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 244,
            column: 9
          }
        }],
        line: 242
      },
      "28": {
        loc: {
          start: {
            line: 246,
            column: 32
          },
          end: {
            line: 248,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 247,
            column: 8
          },
          end: {
            line: 247,
            column: 86
          }
        }, {
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 248,
            column: 9
          }
        }],
        line: 246
      },
      "29": {
        loc: {
          start: {
            line: 247,
            column: 43
          },
          end: {
            line: 247,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 247,
            column: 43
          },
          end: {
            line: 247,
            column: 58
          }
        }, {
          start: {
            line: 247,
            column: 62
          },
          end: {
            line: 247,
            column: 63
          }
        }],
        line: 247
      },
      "30": {
        loc: {
          start: {
            line: 250,
            column: 23
          },
          end: {
            line: 269,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 250,
            column: 48
          },
          end: {
            line: 269,
            column: 8
          }
        }, {
          start: {
            line: 269,
            column: 11
          },
          end: {
            line: 269,
            column: 13
          }
        }],
        line: 250
      },
      "31": {
        loc: {
          start: {
            line: 256,
            column: 29
          },
          end: {
            line: 256,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 256,
            column: 29
          },
          end: {
            line: 256,
            column: 45
          }
        }, {
          start: {
            line: 256,
            column: 49
          },
          end: {
            line: 256,
            column: 50
          }
        }],
        line: 256
      },
      "32": {
        loc: {
          start: {
            line: 257,
            column: 30
          },
          end: {
            line: 257,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 257,
            column: 30
          },
          end: {
            line: 257,
            column: 47
          }
        }, {
          start: {
            line: 257,
            column: 51
          },
          end: {
            line: 257,
            column: 52
          }
        }],
        line: 257
      },
      "33": {
        loc: {
          start: {
            line: 264,
            column: 17
          },
          end: {
            line: 264,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 264,
            column: 30
          },
          end: {
            line: 264,
            column: 41
          }
        }, {
          start: {
            line: 264,
            column: 44
          },
          end: {
            line: 264,
            column: 80
          }
        }],
        line: 264
      },
      "34": {
        loc: {
          start: {
            line: 264,
            column: 44
          },
          end: {
            line: 264,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 264,
            column: 58
          },
          end: {
            line: 264,
            column: 69
          }
        }, {
          start: {
            line: 264,
            column: 72
          },
          end: {
            line: 264,
            column: 80
          }
        }],
        line: 264
      },
      "35": {
        loc: {
          start: {
            line: 290,
            column: 2
          },
          end: {
            line: 290,
            column: 33
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 290,
            column: 31
          },
          end: {
            line: 290,
            column: 33
          }
        }],
        line: 290
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b251df93fecf3249bd7e824b2bf2f502d91d8802"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1bpwe9i7vk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1bpwe9i7vk();
import { useState, useCallback, useMemo, useRef } from 'react';
import { useAuth } from "../../contexts/AuthContext";
import { supabase } from "../../lib/supabase";
import { useAdvancedCache } from "../usePerformanceOptimization";
export function usePerformanceData() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1bpwe9i7vk().b[0][0]++, {});
  cov_1bpwe9i7vk().f[0]++;
  var _ref = (cov_1bpwe9i7vk().s[0]++, options),
    _ref$enableCache = _ref.enableCache,
    enableCache = _ref$enableCache === void 0 ? (cov_1bpwe9i7vk().b[1][0]++, true) : _ref$enableCache,
    _ref$cacheTimeout = _ref.cacheTimeout,
    cacheTimeout = _ref$cacheTimeout === void 0 ? (cov_1bpwe9i7vk().b[2][0]++, 300000) : _ref$cacheTimeout,
    _ref$autoRefresh = _ref.autoRefresh,
    autoRefresh = _ref$autoRefresh === void 0 ? (cov_1bpwe9i7vk().b[3][0]++, false) : _ref$autoRefresh,
    _ref$refreshInterval = _ref.refreshInterval,
    refreshInterval = _ref$refreshInterval === void 0 ? (cov_1bpwe9i7vk().b[4][0]++, 600000) : _ref$refreshInterval;
  var _ref2 = (cov_1bpwe9i7vk().s[1]++, useAuth()),
    user = _ref2.user;
  var _ref3 = (cov_1bpwe9i7vk().s[2]++, useState(false)),
    _ref4 = _slicedToArray(_ref3, 2),
    loading = _ref4[0],
    setLoading = _ref4[1];
  var _ref5 = (cov_1bpwe9i7vk().s[3]++, useState(null)),
    _ref6 = _slicedToArray(_ref5, 2),
    error = _ref6[0],
    setError = _ref6[1];
  var lastFetchRef = (cov_1bpwe9i7vk().s[4]++, useRef(0));
  var cacheKey = (cov_1bpwe9i7vk().s[5]++, useMemo(function () {
    cov_1bpwe9i7vk().f[1]++;
    cov_1bpwe9i7vk().s[6]++;
    return user ? (cov_1bpwe9i7vk().b[5][0]++, `performance_data_${user.id}`) : (cov_1bpwe9i7vk().b[5][1]++, null);
  }, [user == null ? void 0 : user.id]));
  var _ref7 = (cov_1bpwe9i7vk().s[7]++, useAdvancedCache((cov_1bpwe9i7vk().b[6][0]++, cacheKey) || (cov_1bpwe9i7vk().b[6][1]++, 'performance_data_anonymous'), function () {
      cov_1bpwe9i7vk().f[2]++;
      cov_1bpwe9i7vk().s[8]++;
      return fetchPerformanceDataFromAPI();
    }, {
      ttl: cacheTimeout,
      dependencies: [user == null ? void 0 : user.id],
      enabled: (cov_1bpwe9i7vk().b[7][0]++, enableCache) && (cov_1bpwe9i7vk().b[7][1]++, !!user)
    })),
    cachedData = _ref7.data,
    cacheLoading = _ref7.loading,
    cacheError = _ref7.error,
    fetchCachedData = _ref7.fetchData,
    invalidateCache = _ref7.invalidate,
    refreshCache = _ref7.refresh;
  var fetchPerformanceDataFromAPI = (cov_1bpwe9i7vk().s[9]++, useCallback(_asyncToGenerator(function* () {
    cov_1bpwe9i7vk().f[3]++;
    cov_1bpwe9i7vk().s[10]++;
    if (!user) {
      cov_1bpwe9i7vk().b[8][0]++;
      cov_1bpwe9i7vk().s[11]++;
      throw new Error('User not authenticated');
    } else {
      cov_1bpwe9i7vk().b[8][1]++;
    }
    var startTime = (cov_1bpwe9i7vk().s[12]++, Date.now());
    cov_1bpwe9i7vk().s[13]++;
    try {
      var _ref9 = (cov_1bpwe9i7vk().s[14]++, yield supabase.from('users').select(`
          id,
          skill_level,
          match_results!inner (
            id,
            opponent_name,
            result,
            match_score,
            created_at,
            match_stats,
            duration_minutes
          ),
          training_sessions!inner (
            id,
            title,
            session_type,
            overall_score,
            created_at,
            duration_minutes,
            improvement_areas
          ),
          skill_stats!inner (
            forehand,
            backhand,
            serve,
            volley,
            footwork,
            strategy,
            mental_game,
            updated_at
          )
        `).eq('id', user.id).order('match_results(created_at)', {
          ascending: false
        }).order('training_sessions(created_at)', {
          ascending: false
        }).order('skill_stats(updated_at)', {
          ascending: false
        }).limit(10, {
          foreignTable: 'match_results'
        }).limit(20, {
          foreignTable: 'training_sessions'
        }).limit(5, {
          foreignTable: 'skill_stats'
        }).single()),
        userData = _ref9.data,
        userError = _ref9.error;
      cov_1bpwe9i7vk().s[15]++;
      if (userError) {
        cov_1bpwe9i7vk().b[9][0]++;
        cov_1bpwe9i7vk().s[16]++;
        throw new Error(`Failed to fetch performance data: ${userError.message}`);
      } else {
        cov_1bpwe9i7vk().b[9][1]++;
      }
      var fetchTime = (cov_1bpwe9i7vk().s[17]++, Date.now() - startTime);
      cov_1bpwe9i7vk().s[18]++;
      if (fetchTime > 2000) {
        cov_1bpwe9i7vk().b[10][0]++;
        cov_1bpwe9i7vk().s[19]++;
        console.warn(`Slow performance data query: ${fetchTime}ms`);
      } else {
        cov_1bpwe9i7vk().b[10][1]++;
      }
      var result = (cov_1bpwe9i7vk().s[20]++, {
        matches: (cov_1bpwe9i7vk().b[11][0]++, userData == null ? void 0 : userData.match_results) || (cov_1bpwe9i7vk().b[11][1]++, []),
        sessions: (cov_1bpwe9i7vk().b[12][0]++, userData == null ? void 0 : userData.training_sessions) || (cov_1bpwe9i7vk().b[12][1]++, []),
        skillStats: (cov_1bpwe9i7vk().b[13][0]++, userData == null ? void 0 : userData.skill_stats) || (cov_1bpwe9i7vk().b[13][1]++, []),
        lastFetched: Date.now(),
        userId: user.id
      });
      cov_1bpwe9i7vk().s[21]++;
      lastFetchRef.current = Date.now();
      cov_1bpwe9i7vk().s[22]++;
      return result;
    } catch (err) {
      var errorMessage = (cov_1bpwe9i7vk().s[23]++, err instanceof Error ? (cov_1bpwe9i7vk().b[14][0]++, err.message) : (cov_1bpwe9i7vk().b[14][1]++, 'Failed to fetch performance data'));
      cov_1bpwe9i7vk().s[24]++;
      console.error('Performance data fetch error:', err);
      cov_1bpwe9i7vk().s[25]++;
      throw new Error(errorMessage);
    }
  }), [user == null ? void 0 : user.id]));
  var isStale = (cov_1bpwe9i7vk().s[26]++, useMemo(function () {
    cov_1bpwe9i7vk().f[4]++;
    cov_1bpwe9i7vk().s[27]++;
    if (!cachedData) {
      cov_1bpwe9i7vk().b[15][0]++;
      cov_1bpwe9i7vk().s[28]++;
      return true;
    } else {
      cov_1bpwe9i7vk().b[15][1]++;
    }
    var age = (cov_1bpwe9i7vk().s[29]++, Date.now() - cachedData.lastFetched);
    cov_1bpwe9i7vk().s[30]++;
    return age > cacheTimeout;
  }, [cachedData == null ? void 0 : cachedData.lastFetched, cacheTimeout]));
  var fetchData = (cov_1bpwe9i7vk().s[31]++, useCallback(_asyncToGenerator(function* () {
    var force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1bpwe9i7vk().b[16][0]++, false);
    cov_1bpwe9i7vk().f[5]++;
    cov_1bpwe9i7vk().s[32]++;
    if (!user) {
      cov_1bpwe9i7vk().b[17][0]++;
      cov_1bpwe9i7vk().s[33]++;
      return;
    } else {
      cov_1bpwe9i7vk().b[17][1]++;
    }
    cov_1bpwe9i7vk().s[34]++;
    if ((cov_1bpwe9i7vk().b[19][0]++, loading) && (cov_1bpwe9i7vk().b[19][1]++, !force)) {
      cov_1bpwe9i7vk().b[18][0]++;
      cov_1bpwe9i7vk().s[35]++;
      return;
    } else {
      cov_1bpwe9i7vk().b[18][1]++;
    }
    cov_1bpwe9i7vk().s[36]++;
    try {
      cov_1bpwe9i7vk().s[37]++;
      setLoading(true);
      cov_1bpwe9i7vk().s[38]++;
      setError(null);
      cov_1bpwe9i7vk().s[39]++;
      yield fetchCachedData(force);
    } catch (err) {
      var errorMessage = (cov_1bpwe9i7vk().s[40]++, err instanceof Error ? (cov_1bpwe9i7vk().b[20][0]++, err.message) : (cov_1bpwe9i7vk().b[20][1]++, 'Failed to fetch data'));
      cov_1bpwe9i7vk().s[41]++;
      setError(errorMessage);
    } finally {
      cov_1bpwe9i7vk().s[42]++;
      setLoading(false);
    }
  }), [user, loading, fetchCachedData]));
  var refreshData = (cov_1bpwe9i7vk().s[43]++, useCallback(_asyncToGenerator(function* () {
    cov_1bpwe9i7vk().f[6]++;
    cov_1bpwe9i7vk().s[44]++;
    yield fetchData(true);
  }), [fetchData]));
  cov_1bpwe9i7vk().s[45]++;
  React.useEffect(function () {
    cov_1bpwe9i7vk().f[7]++;
    cov_1bpwe9i7vk().s[46]++;
    if ((cov_1bpwe9i7vk().b[22][0]++, !autoRefresh) || (cov_1bpwe9i7vk().b[22][1]++, !user)) {
      cov_1bpwe9i7vk().b[21][0]++;
      cov_1bpwe9i7vk().s[47]++;
      return;
    } else {
      cov_1bpwe9i7vk().b[21][1]++;
    }
    var interval = (cov_1bpwe9i7vk().s[48]++, setInterval(function () {
      cov_1bpwe9i7vk().f[8]++;
      cov_1bpwe9i7vk().s[49]++;
      if (isStale) {
        cov_1bpwe9i7vk().b[23][0]++;
        cov_1bpwe9i7vk().s[50]++;
        fetchData(false);
      } else {
        cov_1bpwe9i7vk().b[23][1]++;
      }
    }, refreshInterval));
    cov_1bpwe9i7vk().s[51]++;
    return function () {
      cov_1bpwe9i7vk().f[9]++;
      cov_1bpwe9i7vk().s[52]++;
      return clearInterval(interval);
    };
  }, [autoRefresh, user, isStale, fetchData, refreshInterval]);
  cov_1bpwe9i7vk().s[53]++;
  return useMemo(function () {
    cov_1bpwe9i7vk().f[10]++;
    cov_1bpwe9i7vk().s[54]++;
    return {
      data: cachedData,
      loading: (cov_1bpwe9i7vk().b[24][0]++, loading) || (cov_1bpwe9i7vk().b[24][1]++, cacheLoading),
      error: (cov_1bpwe9i7vk().b[25][0]++, error) || (cov_1bpwe9i7vk().b[25][1]++, cacheError == null ? void 0 : cacheError.message) || (cov_1bpwe9i7vk().b[25][2]++, null),
      fetchData: fetchData,
      refreshData: refreshData,
      invalidateCache: invalidateCache,
      isStale: isStale
    };
  }, [cachedData, loading, cacheLoading, error, cacheError, fetchData, refreshData, invalidateCache, isStale]);
}
export function usePerformanceMetrics(dataSource) {
  cov_1bpwe9i7vk().f[11]++;
  cov_1bpwe9i7vk().s[55]++;
  return useMemo(function () {
    cov_1bpwe9i7vk().f[12]++;
    cov_1bpwe9i7vk().s[56]++;
    if (!dataSource) {
      cov_1bpwe9i7vk().b[26][0]++;
      cov_1bpwe9i7vk().s[57]++;
      return null;
    } else {
      cov_1bpwe9i7vk().b[26][1]++;
    }
    var _ref10 = (cov_1bpwe9i7vk().s[58]++, dataSource),
      matches = _ref10.matches,
      sessions = _ref10.sessions,
      skillStats = _ref10.skillStats;
    var recentMatches = (cov_1bpwe9i7vk().s[59]++, matches.slice(0, 5));
    var recentSessions = (cov_1bpwe9i7vk().s[60]++, sessions.slice(0, 10));
    var latestSkillStats = (cov_1bpwe9i7vk().s[61]++, skillStats[0]);
    var winRate = (cov_1bpwe9i7vk().s[62]++, matches.length > 0 ? (cov_1bpwe9i7vk().b[27][0]++, matches.filter(function (m) {
      cov_1bpwe9i7vk().f[13]++;
      cov_1bpwe9i7vk().s[63]++;
      return m.result === 'win';
    }).length / matches.length * 100) : (cov_1bpwe9i7vk().b[27][1]++, 0));
    var averageSessionScore = (cov_1bpwe9i7vk().s[64]++, sessions.length > 0 ? (cov_1bpwe9i7vk().b[28][0]++, sessions.reduce(function (sum, s) {
      cov_1bpwe9i7vk().f[14]++;
      cov_1bpwe9i7vk().s[65]++;
      return sum + ((cov_1bpwe9i7vk().b[29][0]++, s.overall_score) || (cov_1bpwe9i7vk().b[29][1]++, 0));
    }, 0) / sessions.length) : (cov_1bpwe9i7vk().b[28][1]++, 0));
    var skillTrend = (cov_1bpwe9i7vk().s[66]++, skillStats.length >= 2 ? (cov_1bpwe9i7vk().b[30][0]++, function () {
      cov_1bpwe9i7vk().f[15]++;
      var current = (cov_1bpwe9i7vk().s[67]++, skillStats[0]);
      var previous = (cov_1bpwe9i7vk().s[68]++, skillStats[1]);
      var skills = (cov_1bpwe9i7vk().s[69]++, ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game']);
      cov_1bpwe9i7vk().s[70]++;
      return skills.reduce(function (trends, skill) {
        cov_1bpwe9i7vk().f[16]++;
        var currentValue = (cov_1bpwe9i7vk().s[71]++, (cov_1bpwe9i7vk().b[31][0]++, current == null ? void 0 : current[skill]) || (cov_1bpwe9i7vk().b[31][1]++, 0));
        var previousValue = (cov_1bpwe9i7vk().s[72]++, (cov_1bpwe9i7vk().b[32][0]++, previous == null ? void 0 : previous[skill]) || (cov_1bpwe9i7vk().b[32][1]++, 0));
        var change = (cov_1bpwe9i7vk().s[73]++, currentValue - previousValue);
        cov_1bpwe9i7vk().s[74]++;
        trends[skill] = {
          current: currentValue,
          previous: previousValue,
          change: change,
          trend: change > 2 ? (cov_1bpwe9i7vk().b[33][0]++, 'improving') : (cov_1bpwe9i7vk().b[33][1]++, change < -2 ? (cov_1bpwe9i7vk().b[34][0]++, 'declining') : (cov_1bpwe9i7vk().b[34][1]++, 'stable'))
        };
        cov_1bpwe9i7vk().s[75]++;
        return trends;
      }, {});
    }()) : (cov_1bpwe9i7vk().b[30][1]++, {}));
    cov_1bpwe9i7vk().s[76]++;
    return {
      recentMatches: recentMatches,
      recentSessions: recentSessions,
      latestSkillStats: latestSkillStats,
      winRate: winRate,
      averageSessionScore: averageSessionScore,
      skillTrend: skillTrend,
      totalMatches: matches.length,
      totalSessions: sessions.length,
      lastUpdated: dataSource.lastFetched
    };
  }, [dataSource]);
}
export function usePerformanceDataSelector(selector) {
  var deps = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1bpwe9i7vk().b[35][0]++, []);
  cov_1bpwe9i7vk().f[17]++;
  var _ref11 = (cov_1bpwe9i7vk().s[77]++, usePerformanceData()),
    data = _ref11.data;
  cov_1bpwe9i7vk().s[78]++;
  return useMemo(function () {
    cov_1bpwe9i7vk().f[18]++;
    cov_1bpwe9i7vk().s[79]++;
    return selector(data);
  }, [data].concat(_toConsumableArray(deps)));
}
export default {
  usePerformanceData: usePerformanceData,
  usePerformanceMetrics: usePerformanceMetrics,
  usePerformanceDataSelector: usePerformanceDataSelector
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwidXNlTWVtbyIsInVzZVJlZiIsInVzZUF1dGgiLCJzdXBhYmFzZSIsInVzZUFkdmFuY2VkQ2FjaGUiLCJ1c2VQZXJmb3JtYW5jZURhdGEiLCJvcHRpb25zIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwidW5kZWZpbmVkIiwiY292XzFicHdlOWk3dmsiLCJiIiwiZiIsIl9yZWYiLCJzIiwiX3JlZiRlbmFibGVDYWNoZSIsImVuYWJsZUNhY2hlIiwiX3JlZiRjYWNoZVRpbWVvdXQiLCJjYWNoZVRpbWVvdXQiLCJfcmVmJGF1dG9SZWZyZXNoIiwiYXV0b1JlZnJlc2giLCJfcmVmJHJlZnJlc2hJbnRlcnZhbCIsInJlZnJlc2hJbnRlcnZhbCIsIl9yZWYyIiwidXNlciIsIl9yZWYzIiwiX3JlZjQiLCJfc2xpY2VkVG9BcnJheSIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwiX3JlZjUiLCJfcmVmNiIsImVycm9yIiwic2V0RXJyb3IiLCJsYXN0RmV0Y2hSZWYiLCJjYWNoZUtleSIsImlkIiwiX3JlZjciLCJmZXRjaFBlcmZvcm1hbmNlRGF0YUZyb21BUEkiLCJ0dGwiLCJkZXBlbmRlbmNpZXMiLCJlbmFibGVkIiwiY2FjaGVkRGF0YSIsImRhdGEiLCJjYWNoZUxvYWRpbmciLCJjYWNoZUVycm9yIiwiZmV0Y2hDYWNoZWREYXRhIiwiZmV0Y2hEYXRhIiwiaW52YWxpZGF0ZUNhY2hlIiwiaW52YWxpZGF0ZSIsInJlZnJlc2hDYWNoZSIsInJlZnJlc2giLCJfYXN5bmNUb0dlbmVyYXRvciIsIkVycm9yIiwic3RhcnRUaW1lIiwiRGF0ZSIsIm5vdyIsIl9yZWY5IiwiZnJvbSIsInNlbGVjdCIsImVxIiwib3JkZXIiLCJhc2NlbmRpbmciLCJsaW1pdCIsImZvcmVpZ25UYWJsZSIsInNpbmdsZSIsInVzZXJEYXRhIiwidXNlckVycm9yIiwibWVzc2FnZSIsImZldGNoVGltZSIsImNvbnNvbGUiLCJ3YXJuIiwicmVzdWx0IiwibWF0Y2hlcyIsIm1hdGNoX3Jlc3VsdHMiLCJzZXNzaW9ucyIsInRyYWluaW5nX3Nlc3Npb25zIiwic2tpbGxTdGF0cyIsInNraWxsX3N0YXRzIiwibGFzdEZldGNoZWQiLCJ1c2VySWQiLCJjdXJyZW50IiwiZXJyIiwiZXJyb3JNZXNzYWdlIiwiaXNTdGFsZSIsImFnZSIsImZvcmNlIiwicmVmcmVzaERhdGEiLCJSZWFjdCIsInVzZUVmZmVjdCIsImludGVydmFsIiwic2V0SW50ZXJ2YWwiLCJjbGVhckludGVydmFsIiwidXNlUGVyZm9ybWFuY2VNZXRyaWNzIiwiZGF0YVNvdXJjZSIsIl9yZWYxMCIsInJlY2VudE1hdGNoZXMiLCJzbGljZSIsInJlY2VudFNlc3Npb25zIiwibGF0ZXN0U2tpbGxTdGF0cyIsIndpblJhdGUiLCJmaWx0ZXIiLCJtIiwiYXZlcmFnZVNlc3Npb25TY29yZSIsInJlZHVjZSIsInN1bSIsIm92ZXJhbGxfc2NvcmUiLCJza2lsbFRyZW5kIiwicHJldmlvdXMiLCJza2lsbHMiLCJ0cmVuZHMiLCJza2lsbCIsImN1cnJlbnRWYWx1ZSIsInByZXZpb3VzVmFsdWUiLCJjaGFuZ2UiLCJ0cmVuZCIsInRvdGFsTWF0Y2hlcyIsInRvdGFsU2Vzc2lvbnMiLCJsYXN0VXBkYXRlZCIsInVzZVBlcmZvcm1hbmNlRGF0YVNlbGVjdG9yIiwic2VsZWN0b3IiLCJkZXBzIiwiX3JlZjExIiwiY29uY2F0IiwiX3RvQ29uc3VtYWJsZUFycmF5Il0sInNvdXJjZXMiOlsidXNlUGVyZm9ybWFuY2VEYXRhLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogT3B0aW1pemVkIFBlcmZvcm1hbmNlIERhdGEgSG9va1xuICogXG4gKiBGb2N1c2VkIGhvb2sgZm9yIGZldGNoaW5nIGFuZCBjYWNoaW5nIHBlcmZvcm1hbmNlIGRhdGFcbiAqIHdpdGggbWVtb2l6YXRpb24gYW5kIHNlbGVjdGl2ZSByZS1yZW5kZXJpbmcuXG4gKi9cblxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrLCB1c2VNZW1vLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VBdXRoIH0gZnJvbSAnQC9jb250ZXh0cy9BdXRoQ29udGV4dCc7XG5pbXBvcnQgeyBzdXBhYmFzZSB9IGZyb20gJ0AvbGliL3N1cGFiYXNlJztcbmltcG9ydCB7IHVzZUFkdmFuY2VkQ2FjaGUgfSBmcm9tICdAL2hvb2tzL3VzZVBlcmZvcm1hbmNlT3B0aW1pemF0aW9uJztcblxuZXhwb3J0IGludGVyZmFjZSBQZXJmb3JtYW5jZURhdGFDYWNoZSB7XG4gIG1hdGNoZXM6IGFueVtdO1xuICBzZXNzaW9uczogYW55W107XG4gIHNraWxsU3RhdHM6IGFueVtdO1xuICBsYXN0RmV0Y2hlZDogbnVtYmVyO1xuICB1c2VySWQ6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIFVzZVBlcmZvcm1hbmNlRGF0YU9wdGlvbnMge1xuICBlbmFibGVDYWNoZT86IGJvb2xlYW47XG4gIGNhY2hlVGltZW91dD86IG51bWJlcjtcbiAgYXV0b1JlZnJlc2g/OiBib29sZWFuO1xuICByZWZyZXNoSW50ZXJ2YWw/OiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBVc2VQZXJmb3JtYW5jZURhdGFSZXR1cm4ge1xuICBkYXRhOiBQZXJmb3JtYW5jZURhdGFDYWNoZSB8IG51bGw7XG4gIGxvYWRpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuICBmZXRjaERhdGE6IChmb3JjZT86IGJvb2xlYW4pID0+IFByb21pc2U8dm9pZD47XG4gIHJlZnJlc2hEYXRhOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBpbnZhbGlkYXRlQ2FjaGU6ICgpID0+IHZvaWQ7XG4gIGlzU3RhbGU6IGJvb2xlYW47XG59XG5cbi8qKlxuICogT3B0aW1pemVkIGhvb2sgZm9yIHBlcmZvcm1hbmNlIGRhdGEgZmV0Y2hpbmcgd2l0aCBpbnRlbGxpZ2VudCBjYWNoaW5nXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VQZXJmb3JtYW5jZURhdGEob3B0aW9uczogVXNlUGVyZm9ybWFuY2VEYXRhT3B0aW9ucyA9IHt9KTogVXNlUGVyZm9ybWFuY2VEYXRhUmV0dXJuIHtcbiAgY29uc3Qge1xuICAgIGVuYWJsZUNhY2hlID0gdHJ1ZSxcbiAgICBjYWNoZVRpbWVvdXQgPSAzMDAwMDAsIC8vIDUgbWludXRlc1xuICAgIGF1dG9SZWZyZXNoID0gZmFsc2UsXG4gICAgcmVmcmVzaEludGVydmFsID0gNjAwMDAwLCAvLyAxMCBtaW51dGVzXG4gIH0gPSBvcHRpb25zO1xuXG4gIGNvbnN0IHsgdXNlciB9ID0gdXNlQXV0aCgpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IGxhc3RGZXRjaFJlZiA9IHVzZVJlZjxudW1iZXI+KDApO1xuXG4gIC8vIE1lbW9pemVkIGNhY2hlIGtleSBiYXNlZCBvbiB1c2VyIElEXG4gIGNvbnN0IGNhY2hlS2V5ID0gdXNlTWVtbygoKSA9PiBcbiAgICB1c2VyID8gYHBlcmZvcm1hbmNlX2RhdGFfJHt1c2VyLmlkfWAgOiBudWxsLCBcbiAgICBbdXNlcj8uaWRdXG4gICk7XG5cbiAgLy8gQWR2YW5jZWQgY2FjaGluZyB3aXRoIGF1dG9tYXRpYyBpbnZhbGlkYXRpb25cbiAgY29uc3Qge1xuICAgIGRhdGE6IGNhY2hlZERhdGEsXG4gICAgbG9hZGluZzogY2FjaGVMb2FkaW5nLFxuICAgIGVycm9yOiBjYWNoZUVycm9yLFxuICAgIGZldGNoRGF0YTogZmV0Y2hDYWNoZWREYXRhLFxuICAgIGludmFsaWRhdGU6IGludmFsaWRhdGVDYWNoZSxcbiAgICByZWZyZXNoOiByZWZyZXNoQ2FjaGUsXG4gIH0gPSB1c2VBZHZhbmNlZENhY2hlPFBlcmZvcm1hbmNlRGF0YUNhY2hlPihcbiAgICBjYWNoZUtleSB8fCAncGVyZm9ybWFuY2VfZGF0YV9hbm9ueW1vdXMnLFxuICAgICgpID0+IGZldGNoUGVyZm9ybWFuY2VEYXRhRnJvbUFQSSgpLFxuICAgIHtcbiAgICAgIHR0bDogY2FjaGVUaW1lb3V0LFxuICAgICAgZGVwZW5kZW5jaWVzOiBbdXNlcj8uaWRdLFxuICAgICAgZW5hYmxlZDogZW5hYmxlQ2FjaGUgJiYgISF1c2VyLFxuICAgIH1cbiAgKTtcblxuICAvLyBNZW1vaXplZCBkYXRhIGZldGNoZXIgd2l0aCBvcHRpbWl6ZWQgcXVlcmllc1xuICBjb25zdCBmZXRjaFBlcmZvcm1hbmNlRGF0YUZyb21BUEkgPSB1c2VDYWxsYmFjayhhc3luYyAoKTogUHJvbWlzZTxQZXJmb3JtYW5jZURhdGFDYWNoZT4gPT4ge1xuICAgIGlmICghdXNlcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdVc2VyIG5vdCBhdXRoZW50aWNhdGVkJyk7XG4gICAgfVxuXG4gICAgY29uc3Qgc3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcblxuICAgIHRyeSB7XG4gICAgICAvLyBTaW5nbGUgb3B0aW1pemVkIHF1ZXJ5IHdpdGggam9pbnMgaW5zdGVhZCBvZiBtdWx0aXBsZSBxdWVyaWVzXG4gICAgICBjb25zdCB7IGRhdGE6IHVzZXJEYXRhLCBlcnJvcjogdXNlckVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZVxuICAgICAgICAuZnJvbSgndXNlcnMnKVxuICAgICAgICAuc2VsZWN0KGBcbiAgICAgICAgICBpZCxcbiAgICAgICAgICBza2lsbF9sZXZlbCxcbiAgICAgICAgICBtYXRjaF9yZXN1bHRzIWlubmVyIChcbiAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgb3Bwb25lbnRfbmFtZSxcbiAgICAgICAgICAgIHJlc3VsdCxcbiAgICAgICAgICAgIG1hdGNoX3Njb3JlLFxuICAgICAgICAgICAgY3JlYXRlZF9hdCxcbiAgICAgICAgICAgIG1hdGNoX3N0YXRzLFxuICAgICAgICAgICAgZHVyYXRpb25fbWludXRlc1xuICAgICAgICAgICksXG4gICAgICAgICAgdHJhaW5pbmdfc2Vzc2lvbnMhaW5uZXIgKFxuICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICB0aXRsZSxcbiAgICAgICAgICAgIHNlc3Npb25fdHlwZSxcbiAgICAgICAgICAgIG92ZXJhbGxfc2NvcmUsXG4gICAgICAgICAgICBjcmVhdGVkX2F0LFxuICAgICAgICAgICAgZHVyYXRpb25fbWludXRlcyxcbiAgICAgICAgICAgIGltcHJvdmVtZW50X2FyZWFzXG4gICAgICAgICAgKSxcbiAgICAgICAgICBza2lsbF9zdGF0cyFpbm5lciAoXG4gICAgICAgICAgICBmb3JlaGFuZCxcbiAgICAgICAgICAgIGJhY2toYW5kLFxuICAgICAgICAgICAgc2VydmUsXG4gICAgICAgICAgICB2b2xsZXksXG4gICAgICAgICAgICBmb290d29yayxcbiAgICAgICAgICAgIHN0cmF0ZWd5LFxuICAgICAgICAgICAgbWVudGFsX2dhbWUsXG4gICAgICAgICAgICB1cGRhdGVkX2F0XG4gICAgICAgICAgKVxuICAgICAgICBgKVxuICAgICAgICAuZXEoJ2lkJywgdXNlci5pZClcbiAgICAgICAgLm9yZGVyKCdtYXRjaF9yZXN1bHRzKGNyZWF0ZWRfYXQpJywgeyBhc2NlbmRpbmc6IGZhbHNlIH0pXG4gICAgICAgIC5vcmRlcigndHJhaW5pbmdfc2Vzc2lvbnMoY3JlYXRlZF9hdCknLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgICAgLm9yZGVyKCdza2lsbF9zdGF0cyh1cGRhdGVkX2F0KScsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAubGltaXQoMTAsIHsgZm9yZWlnblRhYmxlOiAnbWF0Y2hfcmVzdWx0cycgfSlcbiAgICAgICAgLmxpbWl0KDIwLCB7IGZvcmVpZ25UYWJsZTogJ3RyYWluaW5nX3Nlc3Npb25zJyB9KVxuICAgICAgICAubGltaXQoNSwgeyBmb3JlaWduVGFibGU6ICdza2lsbF9zdGF0cycgfSlcbiAgICAgICAgLnNpbmdsZSgpO1xuXG4gICAgICBpZiAodXNlckVycm9yKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIHBlcmZvcm1hbmNlIGRhdGE6ICR7dXNlckVycm9yLm1lc3NhZ2V9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGZldGNoVGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG4gICAgICBcbiAgICAgIC8vIExvZyBzbG93IHF1ZXJpZXMgZm9yIG9wdGltaXphdGlvblxuICAgICAgaWYgKGZldGNoVGltZSA+IDIwMDApIHtcbiAgICAgICAgY29uc29sZS53YXJuKGBTbG93IHBlcmZvcm1hbmNlIGRhdGEgcXVlcnk6ICR7ZmV0Y2hUaW1lfW1zYCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlc3VsdDogUGVyZm9ybWFuY2VEYXRhQ2FjaGUgPSB7XG4gICAgICAgIG1hdGNoZXM6IHVzZXJEYXRhPy5tYXRjaF9yZXN1bHRzIHx8IFtdLFxuICAgICAgICBzZXNzaW9uczogdXNlckRhdGE/LnRyYWluaW5nX3Nlc3Npb25zIHx8IFtdLFxuICAgICAgICBza2lsbFN0YXRzOiB1c2VyRGF0YT8uc2tpbGxfc3RhdHMgfHwgW10sXG4gICAgICAgIGxhc3RGZXRjaGVkOiBEYXRlLm5vdygpLFxuICAgICAgICB1c2VySWQ6IHVzZXIuaWQsXG4gICAgICB9O1xuXG4gICAgICBsYXN0RmV0Y2hSZWYuY3VycmVudCA9IERhdGUubm93KCk7XG4gICAgICByZXR1cm4gcmVzdWx0O1xuXG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBmZXRjaCBwZXJmb3JtYW5jZSBkYXRhJztcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1BlcmZvcm1hbmNlIGRhdGEgZmV0Y2ggZXJyb3I6JywgZXJyKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgIH1cbiAgfSwgW3VzZXI/LmlkXSk7XG5cbiAgLy8gTWVtb2l6ZWQgc3RhbGUgZGF0YSBjaGVja2VyXG4gIGNvbnN0IGlzU3RhbGUgPSB1c2VNZW1vKCgpID0+IHtcbiAgICBpZiAoIWNhY2hlZERhdGEpIHJldHVybiB0cnVlO1xuICAgIGNvbnN0IGFnZSA9IERhdGUubm93KCkgLSBjYWNoZWREYXRhLmxhc3RGZXRjaGVkO1xuICAgIHJldHVybiBhZ2UgPiBjYWNoZVRpbWVvdXQ7XG4gIH0sIFtjYWNoZWREYXRhPy5sYXN0RmV0Y2hlZCwgY2FjaGVUaW1lb3V0XSk7XG5cbiAgLy8gT3B0aW1pemVkIGZldGNoIGZ1bmN0aW9uIHdpdGggbG9hZGluZyBzdGF0ZSBtYW5hZ2VtZW50XG4gIGNvbnN0IGZldGNoRGF0YSA9IHVzZUNhbGxiYWNrKGFzeW5jIChmb3JjZSA9IGZhbHNlKSA9PiB7XG4gICAgaWYgKCF1c2VyKSByZXR1cm47XG5cbiAgICAvLyBBdm9pZCBkdXBsaWNhdGUgcmVxdWVzdHNcbiAgICBpZiAobG9hZGluZyAmJiAhZm9yY2UpIHJldHVybjtcblxuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG4gICAgICBcbiAgICAgIGF3YWl0IGZldGNoQ2FjaGVkRGF0YShmb3JjZSk7XG4gICAgICBcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIGZldGNoIGRhdGEnO1xuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfVxuICB9LCBbdXNlciwgbG9hZGluZywgZmV0Y2hDYWNoZWREYXRhXSk7XG5cbiAgLy8gT3B0aW1pemVkIHJlZnJlc2ggZnVuY3Rpb25cbiAgY29uc3QgcmVmcmVzaERhdGEgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgYXdhaXQgZmV0Y2hEYXRhKHRydWUpO1xuICB9LCBbZmV0Y2hEYXRhXSk7XG5cbiAgLy8gQXV0by1yZWZyZXNoIGxvZ2ljIHdpdGggY2xlYW51cFxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghYXV0b1JlZnJlc2ggfHwgIXVzZXIpIHJldHVybjtcblxuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgaWYgKGlzU3RhbGUpIHtcbiAgICAgICAgZmV0Y2hEYXRhKGZhbHNlKTsgLy8gVXNlIGNhY2hlIGlmIGF2YWlsYWJsZVxuICAgICAgfVxuICAgIH0sIHJlZnJlc2hJbnRlcnZhbCk7XG5cbiAgICByZXR1cm4gKCkgPT4gY2xlYXJJbnRlcnZhbChpbnRlcnZhbCk7XG4gIH0sIFthdXRvUmVmcmVzaCwgdXNlciwgaXNTdGFsZSwgZmV0Y2hEYXRhLCByZWZyZXNoSW50ZXJ2YWxdKTtcblxuICAvLyBNZW1vaXplZCByZXR1cm4gdmFsdWUgdG8gcHJldmVudCB1bm5lY2Vzc2FyeSByZS1yZW5kZXJzXG4gIHJldHVybiB1c2VNZW1vKCgpID0+ICh7XG4gICAgZGF0YTogY2FjaGVkRGF0YSxcbiAgICBsb2FkaW5nOiBsb2FkaW5nIHx8IGNhY2hlTG9hZGluZyxcbiAgICBlcnJvcjogZXJyb3IgfHwgY2FjaGVFcnJvcj8ubWVzc2FnZSB8fCBudWxsLFxuICAgIGZldGNoRGF0YSxcbiAgICByZWZyZXNoRGF0YSxcbiAgICBpbnZhbGlkYXRlQ2FjaGUsXG4gICAgaXNTdGFsZSxcbiAgfSksIFtcbiAgICBjYWNoZWREYXRhLFxuICAgIGxvYWRpbmcsXG4gICAgY2FjaGVMb2FkaW5nLFxuICAgIGVycm9yLFxuICAgIGNhY2hlRXJyb3IsXG4gICAgZmV0Y2hEYXRhLFxuICAgIHJlZnJlc2hEYXRhLFxuICAgIGludmFsaWRhdGVDYWNoZSxcbiAgICBpc1N0YWxlLFxuICBdKTtcbn1cblxuLyoqXG4gKiBPcHRpbWl6ZWQgaG9vayBmb3Igc3BlY2lmaWMgcGVyZm9ybWFuY2UgbWV0cmljcyB3aXRoIHNlbGVjdGl2ZSB1cGRhdGVzXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VQZXJmb3JtYW5jZU1ldHJpY3MoZGF0YVNvdXJjZT86IFBlcmZvcm1hbmNlRGF0YUNhY2hlKSB7XG4gIHJldHVybiB1c2VNZW1vKCgpID0+IHtcbiAgICBpZiAoIWRhdGFTb3VyY2UpIHJldHVybiBudWxsO1xuXG4gICAgY29uc3QgeyBtYXRjaGVzLCBzZXNzaW9ucywgc2tpbGxTdGF0cyB9ID0gZGF0YVNvdXJjZTtcblxuICAgIC8vIE1lbW9pemVkIGNhbGN1bGF0aW9ucyB0byBwcmV2ZW50IHJlY2FsY3VsYXRpb24gb24gZXZlcnkgcmVuZGVyXG4gICAgY29uc3QgcmVjZW50TWF0Y2hlcyA9IG1hdGNoZXMuc2xpY2UoMCwgNSk7XG4gICAgY29uc3QgcmVjZW50U2Vzc2lvbnMgPSBzZXNzaW9ucy5zbGljZSgwLCAxMCk7XG4gICAgY29uc3QgbGF0ZXN0U2tpbGxTdGF0cyA9IHNraWxsU3RhdHNbMF07XG5cbiAgICBjb25zdCB3aW5SYXRlID0gbWF0Y2hlcy5sZW5ndGggPiAwIFxuICAgICAgPyAobWF0Y2hlcy5maWx0ZXIobSA9PiBtLnJlc3VsdCA9PT0gJ3dpbicpLmxlbmd0aCAvIG1hdGNoZXMubGVuZ3RoKSAqIDEwMCBcbiAgICAgIDogMDtcblxuICAgIGNvbnN0IGF2ZXJhZ2VTZXNzaW9uU2NvcmUgPSBzZXNzaW9ucy5sZW5ndGggPiAwXG4gICAgICA/IHNlc3Npb25zLnJlZHVjZSgoc3VtLCBzKSA9PiBzdW0gKyAocy5vdmVyYWxsX3Njb3JlIHx8IDApLCAwKSAvIHNlc3Npb25zLmxlbmd0aFxuICAgICAgOiAwO1xuXG4gICAgY29uc3Qgc2tpbGxUcmVuZCA9IHNraWxsU3RhdHMubGVuZ3RoID49IDIgPyAoKCkgPT4ge1xuICAgICAgY29uc3QgY3VycmVudCA9IHNraWxsU3RhdHNbMF07XG4gICAgICBjb25zdCBwcmV2aW91cyA9IHNraWxsU3RhdHNbMV07XG4gICAgICBjb25zdCBza2lsbHMgPSBbJ2ZvcmVoYW5kJywgJ2JhY2toYW5kJywgJ3NlcnZlJywgJ3ZvbGxleScsICdmb290d29yaycsICdzdHJhdGVneScsICdtZW50YWxfZ2FtZSddO1xuICAgICAgXG4gICAgICByZXR1cm4gc2tpbGxzLnJlZHVjZSgodHJlbmRzLCBza2lsbCkgPT4ge1xuICAgICAgICBjb25zdCBjdXJyZW50VmFsdWUgPSBjdXJyZW50Py5bc2tpbGxdIHx8IDA7XG4gICAgICAgIGNvbnN0IHByZXZpb3VzVmFsdWUgPSBwcmV2aW91cz8uW3NraWxsXSB8fCAwO1xuICAgICAgICBjb25zdCBjaGFuZ2UgPSBjdXJyZW50VmFsdWUgLSBwcmV2aW91c1ZhbHVlO1xuICAgICAgICBcbiAgICAgICAgdHJlbmRzW3NraWxsXSA9IHtcbiAgICAgICAgICBjdXJyZW50OiBjdXJyZW50VmFsdWUsXG4gICAgICAgICAgcHJldmlvdXM6IHByZXZpb3VzVmFsdWUsXG4gICAgICAgICAgY2hhbmdlLFxuICAgICAgICAgIHRyZW5kOiBjaGFuZ2UgPiAyID8gJ2ltcHJvdmluZycgOiBjaGFuZ2UgPCAtMiA/ICdkZWNsaW5pbmcnIDogJ3N0YWJsZScsXG4gICAgICAgIH07XG4gICAgICAgIFxuICAgICAgICByZXR1cm4gdHJlbmRzO1xuICAgICAgfSwge30gYXMgUmVjb3JkPHN0cmluZywgYW55Pik7XG4gICAgfSkoKSA6IHt9O1xuXG4gICAgcmV0dXJuIHtcbiAgICAgIHJlY2VudE1hdGNoZXMsXG4gICAgICByZWNlbnRTZXNzaW9ucyxcbiAgICAgIGxhdGVzdFNraWxsU3RhdHMsXG4gICAgICB3aW5SYXRlLFxuICAgICAgYXZlcmFnZVNlc3Npb25TY29yZSxcbiAgICAgIHNraWxsVHJlbmQsXG4gICAgICB0b3RhbE1hdGNoZXM6IG1hdGNoZXMubGVuZ3RoLFxuICAgICAgdG90YWxTZXNzaW9uczogc2Vzc2lvbnMubGVuZ3RoLFxuICAgICAgbGFzdFVwZGF0ZWQ6IGRhdGFTb3VyY2UubGFzdEZldGNoZWQsXG4gICAgfTtcbiAgfSwgW2RhdGFTb3VyY2VdKTtcbn1cblxuLyoqXG4gKiBQZXJmb3JtYW5jZSBkYXRhIHNlbGVjdG9yIGhvb2sgZm9yIGNvbXBvbmVudC1zcGVjaWZpYyBkYXRhXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB1c2VQZXJmb3JtYW5jZURhdGFTZWxlY3RvcjxUPihcbiAgc2VsZWN0b3I6IChkYXRhOiBQZXJmb3JtYW5jZURhdGFDYWNoZSB8IG51bGwpID0+IFQsXG4gIGRlcHM6IFJlYWN0LkRlcGVuZGVuY3lMaXN0ID0gW11cbikge1xuICBjb25zdCB7IGRhdGEgfSA9IHVzZVBlcmZvcm1hbmNlRGF0YSgpO1xuICBcbiAgcmV0dXJuIHVzZU1lbW8oKCkgPT4gc2VsZWN0b3IoZGF0YSksIFtkYXRhLCAuLi5kZXBzXSk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHtcbiAgdXNlUGVyZm9ybWFuY2VEYXRhLFxuICB1c2VQZXJmb3JtYW5jZU1ldHJpY3MsXG4gIHVzZVBlcmZvcm1hbmNlRGF0YVNlbGVjdG9yLFxufTtcbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9BLFNBQVNBLFFBQVEsRUFBRUMsV0FBVyxFQUFFQyxPQUFPLEVBQUVDLE1BQU0sUUFBUSxPQUFPO0FBQzlELFNBQVNDLE9BQU87QUFDaEIsU0FBU0MsUUFBUTtBQUNqQixTQUFTQyxnQkFBZ0I7QUE4QnpCLE9BQU8sU0FBU0Msa0JBQWtCQSxDQUFBLEVBQW9FO0VBQUEsSUFBbkVDLE9BQWtDLEdBQUFDLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxPQUFBRyxjQUFBLEdBQUFDLENBQUEsVUFBRyxDQUFDLENBQUM7RUFBQUQsY0FBQSxHQUFBRSxDQUFBO0VBQ3hFLElBQUFDLElBQUEsSUFBQUgsY0FBQSxHQUFBSSxDQUFBLE9BS0lSLE9BQU87SUFBQVMsZ0JBQUEsR0FBQUYsSUFBQSxDQUpURyxXQUFXO0lBQVhBLFdBQVcsR0FBQUQsZ0JBQUEsZUFBQUwsY0FBQSxHQUFBQyxDQUFBLFVBQUcsSUFBSSxJQUFBSSxnQkFBQTtJQUFBRSxpQkFBQSxHQUFBSixJQUFBLENBQ2xCSyxZQUFZO0lBQVpBLFlBQVksR0FBQUQsaUJBQUEsZUFBQVAsY0FBQSxHQUFBQyxDQUFBLFVBQUcsTUFBTSxJQUFBTSxpQkFBQTtJQUFBRSxnQkFBQSxHQUFBTixJQUFBLENBQ3JCTyxXQUFXO0lBQVhBLFdBQVcsR0FBQUQsZ0JBQUEsZUFBQVQsY0FBQSxHQUFBQyxDQUFBLFVBQUcsS0FBSyxJQUFBUSxnQkFBQTtJQUFBRSxvQkFBQSxHQUFBUixJQUFBLENBQ25CUyxlQUFlO0lBQWZBLGVBQWUsR0FBQUQsb0JBQUEsZUFBQVgsY0FBQSxHQUFBQyxDQUFBLFVBQUcsTUFBTSxJQUFBVSxvQkFBQTtFQUcxQixJQUFBRSxLQUFBLElBQUFiLGNBQUEsR0FBQUksQ0FBQSxPQUFpQlosT0FBTyxDQUFDLENBQUM7SUFBbEJzQixJQUFJLEdBQUFELEtBQUEsQ0FBSkMsSUFBSTtFQUNaLElBQUFDLEtBQUEsSUFBQWYsY0FBQSxHQUFBSSxDQUFBLE9BQThCaEIsUUFBUSxDQUFDLEtBQUssQ0FBQztJQUFBNEIsS0FBQSxHQUFBQyxjQUFBLENBQUFGLEtBQUE7SUFBdENHLE9BQU8sR0FBQUYsS0FBQTtJQUFFRyxVQUFVLEdBQUFILEtBQUE7RUFDMUIsSUFBQUksS0FBQSxJQUFBcEIsY0FBQSxHQUFBSSxDQUFBLE9BQTBCaEIsUUFBUSxDQUFnQixJQUFJLENBQUM7SUFBQWlDLEtBQUEsR0FBQUosY0FBQSxDQUFBRyxLQUFBO0lBQWhERSxLQUFLLEdBQUFELEtBQUE7SUFBRUUsUUFBUSxHQUFBRixLQUFBO0VBQ3RCLElBQU1HLFlBQVksSUFBQXhCLGNBQUEsR0FBQUksQ0FBQSxPQUFHYixNQUFNLENBQVMsQ0FBQyxDQUFDO0VBR3RDLElBQU1rQyxRQUFRLElBQUF6QixjQUFBLEdBQUFJLENBQUEsT0FBR2QsT0FBTyxDQUFDLFlBQ3ZCO0lBQUFVLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFJLENBQUE7SUFBQSxPQUFBVSxJQUFJLElBQUFkLGNBQUEsR0FBQUMsQ0FBQSxVQUFHLG9CQUFvQmEsSUFBSSxDQUFDWSxFQUFFLEVBQUUsS0FBQTFCLGNBQUEsR0FBQUMsQ0FBQSxVQUFHLElBQUk7RUFBRCxDQUFDLEVBQzNDLENBQUNhLElBQUksb0JBQUpBLElBQUksQ0FBRVksRUFBRSxDQUNYLENBQUM7RUFHRCxJQUFBQyxLQUFBLElBQUEzQixjQUFBLEdBQUFJLENBQUEsT0FPSVYsZ0JBQWdCLENBQ2xCLENBQUFNLGNBQUEsR0FBQUMsQ0FBQSxVQUFBd0IsUUFBUSxNQUFBekIsY0FBQSxHQUFBQyxDQUFBLFVBQUksNEJBQTRCLEdBQ3hDLFlBQU07TUFBQUQsY0FBQSxHQUFBRSxDQUFBO01BQUFGLGNBQUEsR0FBQUksQ0FBQTtNQUFBLE9BQUF3QiwyQkFBMkIsQ0FBQyxDQUFDO0lBQUQsQ0FBQyxFQUNuQztNQUNFQyxHQUFHLEVBQUVyQixZQUFZO01BQ2pCc0IsWUFBWSxFQUFFLENBQUNoQixJQUFJLG9CQUFKQSxJQUFJLENBQUVZLEVBQUUsQ0FBQztNQUN4QkssT0FBTyxFQUFFLENBQUEvQixjQUFBLEdBQUFDLENBQUEsVUFBQUssV0FBVyxNQUFBTixjQUFBLEdBQUFDLENBQUEsVUFBSSxDQUFDLENBQUNhLElBQUk7SUFDaEMsQ0FDRixDQUFDO0lBZE9rQixVQUFVLEdBQUFMLEtBQUEsQ0FBaEJNLElBQUk7SUFDS0MsWUFBWSxHQUFBUCxLQUFBLENBQXJCVCxPQUFPO0lBQ0FpQixVQUFVLEdBQUFSLEtBQUEsQ0FBakJMLEtBQUs7SUFDTWMsZUFBZSxHQUFBVCxLQUFBLENBQTFCVSxTQUFTO0lBQ0dDLGVBQWUsR0FBQVgsS0FBQSxDQUEzQlksVUFBVTtJQUNEQyxZQUFZLEdBQUFiLEtBQUEsQ0FBckJjLE9BQU87RUFZVCxJQUFNYiwyQkFBMkIsSUFBQTVCLGNBQUEsR0FBQUksQ0FBQSxPQUFHZixXQUFXLENBQUFxRCxpQkFBQSxDQUFDLGFBQTJDO0lBQUExQyxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQ3pGLElBQUksQ0FBQ1UsSUFBSSxFQUFFO01BQUFkLGNBQUEsR0FBQUMsQ0FBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFDVCxNQUFNLElBQUl1QyxLQUFLLENBQUMsd0JBQXdCLENBQUM7SUFDM0MsQ0FBQztNQUFBM0MsY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFFRCxJQUFNMkMsU0FBUyxJQUFBNUMsY0FBQSxHQUFBSSxDQUFBLFFBQUd5QyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDO0lBQUM5QyxjQUFBLEdBQUFJLENBQUE7SUFFN0IsSUFBSTtNQUVGLElBQUEyQyxLQUFBLElBQUEvQyxjQUFBLEdBQUFJLENBQUEsY0FBbURYLFFBQVEsQ0FDeER1RCxJQUFJLENBQUMsT0FBTyxDQUFDLENBQ2JDLE1BQU0sQ0FBQztBQUNoQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLENBQUMsQ0FDREMsRUFBRSxDQUFDLElBQUksRUFBRXBDLElBQUksQ0FBQ1ksRUFBRSxDQUFDLENBQ2pCeUIsS0FBSyxDQUFDLDJCQUEyQixFQUFFO1VBQUVDLFNBQVMsRUFBRTtRQUFNLENBQUMsQ0FBQyxDQUN4REQsS0FBSyxDQUFDLCtCQUErQixFQUFFO1VBQUVDLFNBQVMsRUFBRTtRQUFNLENBQUMsQ0FBQyxDQUM1REQsS0FBSyxDQUFDLHlCQUF5QixFQUFFO1VBQUVDLFNBQVMsRUFBRTtRQUFNLENBQUMsQ0FBQyxDQUN0REMsS0FBSyxDQUFDLEVBQUUsRUFBRTtVQUFFQyxZQUFZLEVBQUU7UUFBZ0IsQ0FBQyxDQUFDLENBQzVDRCxLQUFLLENBQUMsRUFBRSxFQUFFO1VBQUVDLFlBQVksRUFBRTtRQUFvQixDQUFDLENBQUMsQ0FDaERELEtBQUssQ0FBQyxDQUFDLEVBQUU7VUFBRUMsWUFBWSxFQUFFO1FBQWMsQ0FBQyxDQUFDLENBQ3pDQyxNQUFNLENBQUMsQ0FBQztRQXpDR0MsUUFBUSxHQUFBVCxLQUFBLENBQWRkLElBQUk7UUFBbUJ3QixTQUFTLEdBQUFWLEtBQUEsQ0FBaEJ6QixLQUFLO01BeUNqQnRCLGNBQUEsR0FBQUksQ0FBQTtNQUVaLElBQUlxRCxTQUFTLEVBQUU7UUFBQXpELGNBQUEsR0FBQUMsQ0FBQTtRQUFBRCxjQUFBLEdBQUFJLENBQUE7UUFDYixNQUFNLElBQUl1QyxLQUFLLENBQUMscUNBQXFDYyxTQUFTLENBQUNDLE9BQU8sRUFBRSxDQUFDO01BQzNFLENBQUM7UUFBQTFELGNBQUEsR0FBQUMsQ0FBQTtNQUFBO01BRUQsSUFBTTBELFNBQVMsSUFBQTNELGNBQUEsR0FBQUksQ0FBQSxRQUFHeUMsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHRixTQUFTO01BQUM1QyxjQUFBLEdBQUFJLENBQUE7TUFHekMsSUFBSXVELFNBQVMsR0FBRyxJQUFJLEVBQUU7UUFBQTNELGNBQUEsR0FBQUMsQ0FBQTtRQUFBRCxjQUFBLEdBQUFJLENBQUE7UUFDcEJ3RCxPQUFPLENBQUNDLElBQUksQ0FBQyxnQ0FBZ0NGLFNBQVMsSUFBSSxDQUFDO01BQzdELENBQUM7UUFBQTNELGNBQUEsR0FBQUMsQ0FBQTtNQUFBO01BRUQsSUFBTTZELE1BQTRCLElBQUE5RCxjQUFBLEdBQUFJLENBQUEsUUFBRztRQUNuQzJELE9BQU8sRUFBRSxDQUFBL0QsY0FBQSxHQUFBQyxDQUFBLFdBQUF1RCxRQUFRLG9CQUFSQSxRQUFRLENBQUVRLGFBQWEsTUFBQWhFLGNBQUEsR0FBQUMsQ0FBQSxXQUFJLEVBQUU7UUFDdENnRSxRQUFRLEVBQUUsQ0FBQWpFLGNBQUEsR0FBQUMsQ0FBQSxXQUFBdUQsUUFBUSxvQkFBUkEsUUFBUSxDQUFFVSxpQkFBaUIsTUFBQWxFLGNBQUEsR0FBQUMsQ0FBQSxXQUFJLEVBQUU7UUFDM0NrRSxVQUFVLEVBQUUsQ0FBQW5FLGNBQUEsR0FBQUMsQ0FBQSxXQUFBdUQsUUFBUSxvQkFBUkEsUUFBUSxDQUFFWSxXQUFXLE1BQUFwRSxjQUFBLEdBQUFDLENBQUEsV0FBSSxFQUFFO1FBQ3ZDb0UsV0FBVyxFQUFFeEIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztRQUN2QndCLE1BQU0sRUFBRXhELElBQUksQ0FBQ1k7TUFDZixDQUFDO01BQUMxQixjQUFBLEdBQUFJLENBQUE7TUFFRm9CLFlBQVksQ0FBQytDLE9BQU8sR0FBRzFCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7TUFBQzlDLGNBQUEsR0FBQUksQ0FBQTtNQUNsQyxPQUFPMEQsTUFBTTtJQUVmLENBQUMsQ0FBQyxPQUFPVSxHQUFHLEVBQUU7TUFDWixJQUFNQyxZQUFZLElBQUF6RSxjQUFBLEdBQUFJLENBQUEsUUFBR29FLEdBQUcsWUFBWTdCLEtBQUssSUFBQTNDLGNBQUEsR0FBQUMsQ0FBQSxXQUFHdUUsR0FBRyxDQUFDZCxPQUFPLEtBQUExRCxjQUFBLEdBQUFDLENBQUEsV0FBRyxrQ0FBa0M7TUFBQ0QsY0FBQSxHQUFBSSxDQUFBO01BQzdGd0QsT0FBTyxDQUFDdEMsS0FBSyxDQUFDLCtCQUErQixFQUFFa0QsR0FBRyxDQUFDO01BQUN4RSxjQUFBLEdBQUFJLENBQUE7TUFDcEQsTUFBTSxJQUFJdUMsS0FBSyxDQUFDOEIsWUFBWSxDQUFDO0lBQy9CO0VBQ0YsQ0FBQyxHQUFFLENBQUMzRCxJQUFJLG9CQUFKQSxJQUFJLENBQUVZLEVBQUUsQ0FBQyxDQUFDO0VBR2QsSUFBTWdELE9BQU8sSUFBQTFFLGNBQUEsR0FBQUksQ0FBQSxRQUFHZCxPQUFPLENBQUMsWUFBTTtJQUFBVSxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQzVCLElBQUksQ0FBQzRCLFVBQVUsRUFBRTtNQUFBaEMsY0FBQSxHQUFBQyxDQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUFBLE9BQU8sSUFBSTtJQUFBLENBQUM7TUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFDN0IsSUFBTTBFLEdBQUcsSUFBQTNFLGNBQUEsR0FBQUksQ0FBQSxRQUFHeUMsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHZCxVQUFVLENBQUNxQyxXQUFXO0lBQUNyRSxjQUFBLEdBQUFJLENBQUE7SUFDaEQsT0FBT3VFLEdBQUcsR0FBR25FLFlBQVk7RUFDM0IsQ0FBQyxFQUFFLENBQUN3QixVQUFVLG9CQUFWQSxVQUFVLENBQUVxQyxXQUFXLEVBQUU3RCxZQUFZLENBQUMsQ0FBQztFQUczQyxJQUFNNkIsU0FBUyxJQUFBckMsY0FBQSxHQUFBSSxDQUFBLFFBQUdmLFdBQVcsQ0FBQXFELGlCQUFBLENBQUMsYUFBeUI7SUFBQSxJQUFsQmtDLEtBQUssR0FBQS9FLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxPQUFBRyxjQUFBLEdBQUFDLENBQUEsV0FBRyxLQUFLO0lBQUFELGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFJLENBQUE7SUFDaEQsSUFBSSxDQUFDVSxJQUFJLEVBQUU7TUFBQWQsY0FBQSxHQUFBQyxDQUFBO01BQUFELGNBQUEsR0FBQUksQ0FBQTtNQUFBO0lBQU0sQ0FBQztNQUFBSixjQUFBLEdBQUFDLENBQUE7SUFBQTtJQUFBRCxjQUFBLEdBQUFJLENBQUE7SUFHbEIsSUFBSSxDQUFBSixjQUFBLEdBQUFDLENBQUEsV0FBQWlCLE9BQU8sTUFBQWxCLGNBQUEsR0FBQUMsQ0FBQSxXQUFJLENBQUMyRSxLQUFLLEdBQUU7TUFBQTVFLGNBQUEsR0FBQUMsQ0FBQTtNQUFBRCxjQUFBLEdBQUFJLENBQUE7TUFBQTtJQUFNLENBQUM7TUFBQUosY0FBQSxHQUFBQyxDQUFBO0lBQUE7SUFBQUQsY0FBQSxHQUFBSSxDQUFBO0lBRTlCLElBQUk7TUFBQUosY0FBQSxHQUFBSSxDQUFBO01BQ0ZlLFVBQVUsQ0FBQyxJQUFJLENBQUM7TUFBQ25CLGNBQUEsR0FBQUksQ0FBQTtNQUNqQm1CLFFBQVEsQ0FBQyxJQUFJLENBQUM7TUFBQ3ZCLGNBQUEsR0FBQUksQ0FBQTtNQUVmLE1BQU1nQyxlQUFlLENBQUN3QyxLQUFLLENBQUM7SUFFOUIsQ0FBQyxDQUFDLE9BQU9KLEdBQUcsRUFBRTtNQUNaLElBQU1DLFlBQVksSUFBQXpFLGNBQUEsR0FBQUksQ0FBQSxRQUFHb0UsR0FBRyxZQUFZN0IsS0FBSyxJQUFBM0MsY0FBQSxHQUFBQyxDQUFBLFdBQUd1RSxHQUFHLENBQUNkLE9BQU8sS0FBQTFELGNBQUEsR0FBQUMsQ0FBQSxXQUFHLHNCQUFzQjtNQUFDRCxjQUFBLEdBQUFJLENBQUE7TUFDakZtQixRQUFRLENBQUNrRCxZQUFZLENBQUM7SUFDeEIsQ0FBQyxTQUFTO01BQUF6RSxjQUFBLEdBQUFJLENBQUE7TUFDUmUsVUFBVSxDQUFDLEtBQUssQ0FBQztJQUNuQjtFQUNGLENBQUMsR0FBRSxDQUFDTCxJQUFJLEVBQUVJLE9BQU8sRUFBRWtCLGVBQWUsQ0FBQyxDQUFDO0VBR3BDLElBQU15QyxXQUFXLElBQUE3RSxjQUFBLEdBQUFJLENBQUEsUUFBR2YsV0FBVyxDQUFBcUQsaUJBQUEsQ0FBQyxhQUFZO0lBQUExQyxjQUFBLEdBQUFFLENBQUE7SUFBQUYsY0FBQSxHQUFBSSxDQUFBO0lBQzFDLE1BQU1pQyxTQUFTLENBQUMsSUFBSSxDQUFDO0VBQ3ZCLENBQUMsR0FBRSxDQUFDQSxTQUFTLENBQUMsQ0FBQztFQUFDckMsY0FBQSxHQUFBSSxDQUFBO0VBR2hCMEUsS0FBSyxDQUFDQyxTQUFTLENBQUMsWUFBTTtJQUFBL0UsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUksQ0FBQTtJQUNwQixJQUFJLENBQUFKLGNBQUEsR0FBQUMsQ0FBQSxZQUFDUyxXQUFXLE1BQUFWLGNBQUEsR0FBQUMsQ0FBQSxXQUFJLENBQUNhLElBQUksR0FBRTtNQUFBZCxjQUFBLEdBQUFDLENBQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BQUE7SUFBTSxDQUFDO01BQUFKLGNBQUEsR0FBQUMsQ0FBQTtJQUFBO0lBRWxDLElBQU0rRSxRQUFRLElBQUFoRixjQUFBLEdBQUFJLENBQUEsUUFBRzZFLFdBQVcsQ0FBQyxZQUFNO01BQUFqRixjQUFBLEdBQUFFLENBQUE7TUFBQUYsY0FBQSxHQUFBSSxDQUFBO01BQ2pDLElBQUlzRSxPQUFPLEVBQUU7UUFBQTFFLGNBQUEsR0FBQUMsQ0FBQTtRQUFBRCxjQUFBLEdBQUFJLENBQUE7UUFDWGlDLFNBQVMsQ0FBQyxLQUFLLENBQUM7TUFDbEIsQ0FBQztRQUFBckMsY0FBQSxHQUFBQyxDQUFBO01BQUE7SUFDSCxDQUFDLEVBQUVXLGVBQWUsQ0FBQztJQUFDWixjQUFBLEdBQUFJLENBQUE7SUFFcEIsT0FBTyxZQUFNO01BQUFKLGNBQUEsR0FBQUUsQ0FBQTtNQUFBRixjQUFBLEdBQUFJLENBQUE7TUFBQSxPQUFBOEUsYUFBYSxDQUFDRixRQUFRLENBQUM7SUFBRCxDQUFDO0VBQ3RDLENBQUMsRUFBRSxDQUFDdEUsV0FBVyxFQUFFSSxJQUFJLEVBQUU0RCxPQUFPLEVBQUVyQyxTQUFTLEVBQUV6QixlQUFlLENBQUMsQ0FBQztFQUFDWixjQUFBLEdBQUFJLENBQUE7RUFHN0QsT0FBT2QsT0FBTyxDQUFDLFlBQU87SUFBQVUsY0FBQSxHQUFBRSxDQUFBO0lBQUFGLGNBQUEsR0FBQUksQ0FBQTtJQUFBO01BQ3BCNkIsSUFBSSxFQUFFRCxVQUFVO01BQ2hCZCxPQUFPLEVBQUUsQ0FBQWxCLGNBQUEsR0FBQUMsQ0FBQSxXQUFBaUIsT0FBTyxNQUFBbEIsY0FBQSxHQUFBQyxDQUFBLFdBQUlpQyxZQUFZO01BQ2hDWixLQUFLLEVBQUUsQ0FBQXRCLGNBQUEsR0FBQUMsQ0FBQSxXQUFBcUIsS0FBSyxNQUFBdEIsY0FBQSxHQUFBQyxDQUFBLFdBQUlrQyxVQUFVLG9CQUFWQSxVQUFVLENBQUV1QixPQUFPLE1BQUExRCxjQUFBLEdBQUFDLENBQUEsV0FBSSxJQUFJO01BQzNDb0MsU0FBUyxFQUFUQSxTQUFTO01BQ1R3QyxXQUFXLEVBQVhBLFdBQVc7TUFDWHZDLGVBQWUsRUFBZkEsZUFBZTtNQUNmb0MsT0FBTyxFQUFQQTtJQUNGLENBQUM7RUFBRCxDQUFFLEVBQUUsQ0FDRjFDLFVBQVUsRUFDVmQsT0FBTyxFQUNQZ0IsWUFBWSxFQUNaWixLQUFLLEVBQ0xhLFVBQVUsRUFDVkUsU0FBUyxFQUNUd0MsV0FBVyxFQUNYdkMsZUFBZSxFQUNmb0MsT0FBTyxDQUNSLENBQUM7QUFDSjtBQUtBLE9BQU8sU0FBU1MscUJBQXFCQSxDQUFDQyxVQUFpQyxFQUFFO0VBQUFwRixjQUFBLEdBQUFFLENBQUE7RUFBQUYsY0FBQSxHQUFBSSxDQUFBO0VBQ3ZFLE9BQU9kLE9BQU8sQ0FBQyxZQUFNO0lBQUFVLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFJLENBQUE7SUFDbkIsSUFBSSxDQUFDZ0YsVUFBVSxFQUFFO01BQUFwRixjQUFBLEdBQUFDLENBQUE7TUFBQUQsY0FBQSxHQUFBSSxDQUFBO01BQUEsT0FBTyxJQUFJO0lBQUEsQ0FBQztNQUFBSixjQUFBLEdBQUFDLENBQUE7SUFBQTtJQUU3QixJQUFBb0YsTUFBQSxJQUFBckYsY0FBQSxHQUFBSSxDQUFBLFFBQTBDZ0YsVUFBVTtNQUE1Q3JCLE9BQU8sR0FBQXNCLE1BQUEsQ0FBUHRCLE9BQU87TUFBRUUsUUFBUSxHQUFBb0IsTUFBQSxDQUFScEIsUUFBUTtNQUFFRSxVQUFVLEdBQUFrQixNQUFBLENBQVZsQixVQUFVO0lBR3JDLElBQU1tQixhQUFhLElBQUF0RixjQUFBLEdBQUFJLENBQUEsUUFBRzJELE9BQU8sQ0FBQ3dCLEtBQUssQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO0lBQ3pDLElBQU1DLGNBQWMsSUFBQXhGLGNBQUEsR0FBQUksQ0FBQSxRQUFHNkQsUUFBUSxDQUFDc0IsS0FBSyxDQUFDLENBQUMsRUFBRSxFQUFFLENBQUM7SUFDNUMsSUFBTUUsZ0JBQWdCLElBQUF6RixjQUFBLEdBQUFJLENBQUEsUUFBRytELFVBQVUsQ0FBQyxDQUFDLENBQUM7SUFFdEMsSUFBTXVCLE9BQU8sSUFBQTFGLGNBQUEsR0FBQUksQ0FBQSxRQUFHMkQsT0FBTyxDQUFDakUsTUFBTSxHQUFHLENBQUMsSUFBQUUsY0FBQSxHQUFBQyxDQUFBLFdBQzdCOEQsT0FBTyxDQUFDNEIsTUFBTSxDQUFDLFVBQUFDLENBQUMsRUFBSTtNQUFBNUYsY0FBQSxHQUFBRSxDQUFBO01BQUFGLGNBQUEsR0FBQUksQ0FBQTtNQUFBLE9BQUF3RixDQUFDLENBQUM5QixNQUFNLEtBQUssS0FBSztJQUFELENBQUMsQ0FBQyxDQUFDaEUsTUFBTSxHQUFHaUUsT0FBTyxDQUFDakUsTUFBTSxHQUFJLEdBQUcsS0FBQUUsY0FBQSxHQUFBQyxDQUFBLFdBQ3ZFLENBQUM7SUFFTCxJQUFNNEYsbUJBQW1CLElBQUE3RixjQUFBLEdBQUFJLENBQUEsUUFBRzZELFFBQVEsQ0FBQ25FLE1BQU0sR0FBRyxDQUFDLElBQUFFLGNBQUEsR0FBQUMsQ0FBQSxXQUMzQ2dFLFFBQVEsQ0FBQzZCLE1BQU0sQ0FBQyxVQUFDQyxHQUFHLEVBQUUzRixDQUFDLEVBQUs7TUFBQUosY0FBQSxHQUFBRSxDQUFBO01BQUFGLGNBQUEsR0FBQUksQ0FBQTtNQUFBLE9BQUEyRixHQUFHLElBQUksQ0FBQS9GLGNBQUEsR0FBQUMsQ0FBQSxXQUFBRyxDQUFDLENBQUM0RixhQUFhLE1BQUFoRyxjQUFBLEdBQUFDLENBQUEsV0FBSSxDQUFDLEVBQUM7SUFBRCxDQUFDLEVBQUUsQ0FBQyxDQUFDLEdBQUdnRSxRQUFRLENBQUNuRSxNQUFNLEtBQUFFLGNBQUEsR0FBQUMsQ0FBQSxXQUM5RSxDQUFDO0lBRUwsSUFBTWdHLFVBQVUsSUFBQWpHLGNBQUEsR0FBQUksQ0FBQSxRQUFHK0QsVUFBVSxDQUFDckUsTUFBTSxJQUFJLENBQUMsSUFBQUUsY0FBQSxHQUFBQyxDQUFBLFdBQUksWUFBTTtNQUFBRCxjQUFBLEdBQUFFLENBQUE7TUFDakQsSUFBTXFFLE9BQU8sSUFBQXZFLGNBQUEsR0FBQUksQ0FBQSxRQUFHK0QsVUFBVSxDQUFDLENBQUMsQ0FBQztNQUM3QixJQUFNK0IsUUFBUSxJQUFBbEcsY0FBQSxHQUFBSSxDQUFBLFFBQUcrRCxVQUFVLENBQUMsQ0FBQyxDQUFDO01BQzlCLElBQU1nQyxNQUFNLElBQUFuRyxjQUFBLEdBQUFJLENBQUEsUUFBRyxDQUFDLFVBQVUsRUFBRSxVQUFVLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxVQUFVLEVBQUUsVUFBVSxFQUFFLGFBQWEsQ0FBQztNQUFDSixjQUFBLEdBQUFJLENBQUE7TUFFbEcsT0FBTytGLE1BQU0sQ0FBQ0wsTUFBTSxDQUFDLFVBQUNNLE1BQU0sRUFBRUMsS0FBSyxFQUFLO1FBQUFyRyxjQUFBLEdBQUFFLENBQUE7UUFDdEMsSUFBTW9HLFlBQVksSUFBQXRHLGNBQUEsR0FBQUksQ0FBQSxRQUFHLENBQUFKLGNBQUEsR0FBQUMsQ0FBQSxXQUFBc0UsT0FBTyxvQkFBUEEsT0FBTyxDQUFHOEIsS0FBSyxDQUFDLE1BQUFyRyxjQUFBLEdBQUFDLENBQUEsV0FBSSxDQUFDO1FBQzFDLElBQU1zRyxhQUFhLElBQUF2RyxjQUFBLEdBQUFJLENBQUEsUUFBRyxDQUFBSixjQUFBLEdBQUFDLENBQUEsV0FBQWlHLFFBQVEsb0JBQVJBLFFBQVEsQ0FBR0csS0FBSyxDQUFDLE1BQUFyRyxjQUFBLEdBQUFDLENBQUEsV0FBSSxDQUFDO1FBQzVDLElBQU11RyxNQUFNLElBQUF4RyxjQUFBLEdBQUFJLENBQUEsUUFBR2tHLFlBQVksR0FBR0MsYUFBYTtRQUFDdkcsY0FBQSxHQUFBSSxDQUFBO1FBRTVDZ0csTUFBTSxDQUFDQyxLQUFLLENBQUMsR0FBRztVQUNkOUIsT0FBTyxFQUFFK0IsWUFBWTtVQUNyQkosUUFBUSxFQUFFSyxhQUFhO1VBQ3ZCQyxNQUFNLEVBQU5BLE1BQU07VUFDTkMsS0FBSyxFQUFFRCxNQUFNLEdBQUcsQ0FBQyxJQUFBeEcsY0FBQSxHQUFBQyxDQUFBLFdBQUcsV0FBVyxLQUFBRCxjQUFBLEdBQUFDLENBQUEsV0FBR3VHLE1BQU0sR0FBRyxDQUFDLENBQUMsSUFBQXhHLGNBQUEsR0FBQUMsQ0FBQSxXQUFHLFdBQVcsS0FBQUQsY0FBQSxHQUFBQyxDQUFBLFdBQUcsUUFBUTtRQUN4RSxDQUFDO1FBQUNELGNBQUEsR0FBQUksQ0FBQTtRQUVGLE9BQU9nRyxNQUFNO01BQ2YsQ0FBQyxFQUFFLENBQUMsQ0FBd0IsQ0FBQztJQUMvQixDQUFDLENBQUUsQ0FBQyxLQUFBcEcsY0FBQSxHQUFBQyxDQUFBLFdBQUcsQ0FBQyxDQUFDO0lBQUNELGNBQUEsR0FBQUksQ0FBQTtJQUVWLE9BQU87TUFDTGtGLGFBQWEsRUFBYkEsYUFBYTtNQUNiRSxjQUFjLEVBQWRBLGNBQWM7TUFDZEMsZ0JBQWdCLEVBQWhCQSxnQkFBZ0I7TUFDaEJDLE9BQU8sRUFBUEEsT0FBTztNQUNQRyxtQkFBbUIsRUFBbkJBLG1CQUFtQjtNQUNuQkksVUFBVSxFQUFWQSxVQUFVO01BQ1ZTLFlBQVksRUFBRTNDLE9BQU8sQ0FBQ2pFLE1BQU07TUFDNUI2RyxhQUFhLEVBQUUxQyxRQUFRLENBQUNuRSxNQUFNO01BQzlCOEcsV0FBVyxFQUFFeEIsVUFBVSxDQUFDZjtJQUMxQixDQUFDO0VBQ0gsQ0FBQyxFQUFFLENBQUNlLFVBQVUsQ0FBQyxDQUFDO0FBQ2xCO0FBS0EsT0FBTyxTQUFTeUIsMEJBQTBCQSxDQUN4Q0MsUUFBa0QsRUFFbEQ7RUFBQSxJQURBQyxJQUEwQixHQUFBbEgsU0FBQSxDQUFBQyxNQUFBLFFBQUFELFNBQUEsUUFBQUUsU0FBQSxHQUFBRixTQUFBLE9BQUFHLGNBQUEsR0FBQUMsQ0FBQSxXQUFHLEVBQUU7RUFBQUQsY0FBQSxHQUFBRSxDQUFBO0VBRS9CLElBQUE4RyxNQUFBLElBQUFoSCxjQUFBLEdBQUFJLENBQUEsUUFBaUJULGtCQUFrQixDQUFDLENBQUM7SUFBN0JzQyxJQUFJLEdBQUErRSxNQUFBLENBQUovRSxJQUFJO0VBQTBCakMsY0FBQSxHQUFBSSxDQUFBO0VBRXRDLE9BQU9kLE9BQU8sQ0FBQyxZQUFNO0lBQUFVLGNBQUEsR0FBQUUsQ0FBQTtJQUFBRixjQUFBLEdBQUFJLENBQUE7SUFBQSxPQUFBMEcsUUFBUSxDQUFDN0UsSUFBSSxDQUFDO0VBQUQsQ0FBQyxHQUFHQSxJQUFJLEVBQUFnRixNQUFBLENBQUFDLGtCQUFBLENBQUtILElBQUksRUFBQyxDQUFDO0FBQ3ZEO0FBRUEsZUFBZTtFQUNicEgsa0JBQWtCLEVBQWxCQSxrQkFBa0I7RUFDbEJ3RixxQkFBcUIsRUFBckJBLHFCQUFxQjtFQUNyQjBCLDBCQUEwQixFQUExQkE7QUFDRixDQUFDIiwiaWdub3JlTGlzdCI6W119