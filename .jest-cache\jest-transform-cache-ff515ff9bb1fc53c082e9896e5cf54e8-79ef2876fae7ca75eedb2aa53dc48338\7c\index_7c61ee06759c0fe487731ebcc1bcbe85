a42b5289bf7d36a0a5d6f68addb30faa
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _AccessibilityUtil = _interopRequireDefault(require("../../modules/AccessibilityUtil"));
var _BoundingDimensions = _interopRequireDefault(require("./BoundingDimensions"));
var _normalizeColors = _interopRequireDefault(require("@react-native/normalize-colors"));
var _Position = _interopRequireDefault(require("./Position"));
var _react = _interopRequireDefault(require("react"));
var _UIManager = _interopRequireDefault(require("../UIManager"));
var _View = _interopRequireDefault(require("../View"));
var _warnOnce = require("../../modules/warnOnce");
var extractSingleTouch = function extractSingleTouch(nativeEvent) {
  var touches = nativeEvent.touches;
  var changedTouches = nativeEvent.changedTouches;
  var hasTouches = touches && touches.length > 0;
  var hasChangedTouches = changedTouches && changedTouches.length > 0;
  return !hasTouches && hasChangedTouches ? changedTouches[0] : hasTouches ? touches[0] : nativeEvent;
};
var States = {
  NOT_RESPONDER: 'NOT_RESPONDER',
  RESPONDER_INACTIVE_PRESS_IN: 'RESPONDER_INACTIVE_PRESS_IN',
  RESPONDER_INACTIVE_PRESS_OUT: 'RESPONDER_INACTIVE_PRESS_OUT',
  RESPONDER_ACTIVE_PRESS_IN: 'RESPONDER_ACTIVE_PRESS_IN',
  RESPONDER_ACTIVE_PRESS_OUT: 'RESPONDER_ACTIVE_PRESS_OUT',
  RESPONDER_ACTIVE_LONG_PRESS_IN: 'RESPONDER_ACTIVE_LONG_PRESS_IN',
  RESPONDER_ACTIVE_LONG_PRESS_OUT: 'RESPONDER_ACTIVE_LONG_PRESS_OUT',
  ERROR: 'ERROR'
};
var baseStatesConditions = {
  NOT_RESPONDER: false,
  RESPONDER_INACTIVE_PRESS_IN: false,
  RESPONDER_INACTIVE_PRESS_OUT: false,
  RESPONDER_ACTIVE_PRESS_IN: false,
  RESPONDER_ACTIVE_PRESS_OUT: false,
  RESPONDER_ACTIVE_LONG_PRESS_IN: false,
  RESPONDER_ACTIVE_LONG_PRESS_OUT: false,
  ERROR: false
};
var IsActive = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, baseStatesConditions), {}, {
  RESPONDER_ACTIVE_PRESS_OUT: true,
  RESPONDER_ACTIVE_PRESS_IN: true
});
var IsPressingIn = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, baseStatesConditions), {}, {
  RESPONDER_INACTIVE_PRESS_IN: true,
  RESPONDER_ACTIVE_PRESS_IN: true,
  RESPONDER_ACTIVE_LONG_PRESS_IN: true
});
var IsLongPressingIn = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, baseStatesConditions), {}, {
  RESPONDER_ACTIVE_LONG_PRESS_IN: true
});
var Signals = {
  DELAY: 'DELAY',
  RESPONDER_GRANT: 'RESPONDER_GRANT',
  RESPONDER_RELEASE: 'RESPONDER_RELEASE',
  RESPONDER_TERMINATED: 'RESPONDER_TERMINATED',
  ENTER_PRESS_RECT: 'ENTER_PRESS_RECT',
  LEAVE_PRESS_RECT: 'LEAVE_PRESS_RECT',
  LONG_PRESS_DETECTED: 'LONG_PRESS_DETECTED'
};
var Transitions = {
  NOT_RESPONDER: {
    DELAY: States.ERROR,
    RESPONDER_GRANT: States.RESPONDER_INACTIVE_PRESS_IN,
    RESPONDER_RELEASE: States.ERROR,
    RESPONDER_TERMINATED: States.ERROR,
    ENTER_PRESS_RECT: States.ERROR,
    LEAVE_PRESS_RECT: States.ERROR,
    LONG_PRESS_DETECTED: States.ERROR
  },
  RESPONDER_INACTIVE_PRESS_IN: {
    DELAY: States.RESPONDER_ACTIVE_PRESS_IN,
    RESPONDER_GRANT: States.ERROR,
    RESPONDER_RELEASE: States.NOT_RESPONDER,
    RESPONDER_TERMINATED: States.NOT_RESPONDER,
    ENTER_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_IN,
    LEAVE_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_OUT,
    LONG_PRESS_DETECTED: States.ERROR
  },
  RESPONDER_INACTIVE_PRESS_OUT: {
    DELAY: States.RESPONDER_ACTIVE_PRESS_OUT,
    RESPONDER_GRANT: States.ERROR,
    RESPONDER_RELEASE: States.NOT_RESPONDER,
    RESPONDER_TERMINATED: States.NOT_RESPONDER,
    ENTER_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_IN,
    LEAVE_PRESS_RECT: States.RESPONDER_INACTIVE_PRESS_OUT,
    LONG_PRESS_DETECTED: States.ERROR
  },
  RESPONDER_ACTIVE_PRESS_IN: {
    DELAY: States.ERROR,
    RESPONDER_GRANT: States.ERROR,
    RESPONDER_RELEASE: States.NOT_RESPONDER,
    RESPONDER_TERMINATED: States.NOT_RESPONDER,
    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_IN,
    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_OUT,
    LONG_PRESS_DETECTED: States.RESPONDER_ACTIVE_LONG_PRESS_IN
  },
  RESPONDER_ACTIVE_PRESS_OUT: {
    DELAY: States.ERROR,
    RESPONDER_GRANT: States.ERROR,
    RESPONDER_RELEASE: States.NOT_RESPONDER,
    RESPONDER_TERMINATED: States.NOT_RESPONDER,
    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_IN,
    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_PRESS_OUT,
    LONG_PRESS_DETECTED: States.ERROR
  },
  RESPONDER_ACTIVE_LONG_PRESS_IN: {
    DELAY: States.ERROR,
    RESPONDER_GRANT: States.ERROR,
    RESPONDER_RELEASE: States.NOT_RESPONDER,
    RESPONDER_TERMINATED: States.NOT_RESPONDER,
    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_IN,
    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_OUT,
    LONG_PRESS_DETECTED: States.RESPONDER_ACTIVE_LONG_PRESS_IN
  },
  RESPONDER_ACTIVE_LONG_PRESS_OUT: {
    DELAY: States.ERROR,
    RESPONDER_GRANT: States.ERROR,
    RESPONDER_RELEASE: States.NOT_RESPONDER,
    RESPONDER_TERMINATED: States.NOT_RESPONDER,
    ENTER_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_IN,
    LEAVE_PRESS_RECT: States.RESPONDER_ACTIVE_LONG_PRESS_OUT,
    LONG_PRESS_DETECTED: States.ERROR
  },
  error: {
    DELAY: States.NOT_RESPONDER,
    RESPONDER_GRANT: States.RESPONDER_INACTIVE_PRESS_IN,
    RESPONDER_RELEASE: States.NOT_RESPONDER,
    RESPONDER_TERMINATED: States.NOT_RESPONDER,
    ENTER_PRESS_RECT: States.NOT_RESPONDER,
    LEAVE_PRESS_RECT: States.NOT_RESPONDER,
    LONG_PRESS_DETECTED: States.NOT_RESPONDER
  }
};
var HIGHLIGHT_DELAY_MS = 130;
var PRESS_EXPAND_PX = 20;
var LONG_PRESS_THRESHOLD = 500;
var LONG_PRESS_DELAY_MS = LONG_PRESS_THRESHOLD - HIGHLIGHT_DELAY_MS;
var LONG_PRESS_ALLOWED_MOVEMENT = 10;
var TouchableMixin = {
  componentDidMount: function componentDidMount() {
    var _this = this;
    (0, _warnOnce.warnOnce)('TouchableMixin', 'TouchableMixin is deprecated. Please use Pressable.');
    var touchableNode = this.getTouchableNode && this.getTouchableNode();
    if (touchableNode && touchableNode.addEventListener) {
      this._touchableBlurListener = function (e) {
        if (_this._isTouchableKeyboardActive) {
          if (_this.state.touchable.touchState && _this.state.touchable.touchState !== States.NOT_RESPONDER) {
            _this.touchableHandleResponderTerminate({
              nativeEvent: e
            });
          }
          _this._isTouchableKeyboardActive = false;
        }
      };
      touchableNode.addEventListener('blur', this._touchableBlurListener);
    }
  },
  componentWillUnmount: function componentWillUnmount() {
    var touchableNode = this.getTouchableNode && this.getTouchableNode();
    if (touchableNode && touchableNode.addEventListener) {
      touchableNode.removeEventListener('blur', this._touchableBlurListener);
    }
    this.touchableDelayTimeout && clearTimeout(this.touchableDelayTimeout);
    this.longPressDelayTimeout && clearTimeout(this.longPressDelayTimeout);
    this.pressOutDelayTimeout && clearTimeout(this.pressOutDelayTimeout);
    this.pressInLocation = null;
    this.state.touchable.responderID = null;
  },
  touchableGetInitialState: function touchableGetInitialState() {
    return {
      touchable: {
        touchState: undefined,
        responderID: null
      }
    };
  },
  touchableHandleResponderTerminationRequest: function touchableHandleResponderTerminationRequest() {
    return !this.props.rejectResponderTermination;
  },
  touchableHandleStartShouldSetResponder: function touchableHandleStartShouldSetResponder() {
    return !this.props.disabled;
  },
  touchableLongPressCancelsPress: function touchableLongPressCancelsPress() {
    return true;
  },
  touchableHandleResponderGrant: function touchableHandleResponderGrant(e) {
    var dispatchID = e.currentTarget;
    e.persist();
    this.pressOutDelayTimeout && clearTimeout(this.pressOutDelayTimeout);
    this.pressOutDelayTimeout = null;
    this.state.touchable.touchState = States.NOT_RESPONDER;
    this.state.touchable.responderID = dispatchID;
    this._receiveSignal(Signals.RESPONDER_GRANT, e);
    var delayMS = this.touchableGetHighlightDelayMS !== undefined ? Math.max(this.touchableGetHighlightDelayMS(), 0) : HIGHLIGHT_DELAY_MS;
    delayMS = isNaN(delayMS) ? HIGHLIGHT_DELAY_MS : delayMS;
    if (delayMS !== 0) {
      this.touchableDelayTimeout = setTimeout(this._handleDelay.bind(this, e), delayMS);
    } else {
      this._handleDelay(e);
    }
    var longDelayMS = this.touchableGetLongPressDelayMS !== undefined ? Math.max(this.touchableGetLongPressDelayMS(), 10) : LONG_PRESS_DELAY_MS;
    longDelayMS = isNaN(longDelayMS) ? LONG_PRESS_DELAY_MS : longDelayMS;
    this.longPressDelayTimeout = setTimeout(this._handleLongDelay.bind(this, e), longDelayMS + delayMS);
  },
  touchableHandleResponderRelease: function touchableHandleResponderRelease(e) {
    this.pressInLocation = null;
    this._receiveSignal(Signals.RESPONDER_RELEASE, e);
  },
  touchableHandleResponderTerminate: function touchableHandleResponderTerminate(e) {
    this.pressInLocation = null;
    this._receiveSignal(Signals.RESPONDER_TERMINATED, e);
  },
  touchableHandleResponderMove: function touchableHandleResponderMove(e) {
    if (!this.state.touchable.positionOnActivate) {
      return;
    }
    var positionOnActivate = this.state.touchable.positionOnActivate;
    var dimensionsOnActivate = this.state.touchable.dimensionsOnActivate;
    var pressRectOffset = this.touchableGetPressRectOffset ? this.touchableGetPressRectOffset() : {
      left: PRESS_EXPAND_PX,
      right: PRESS_EXPAND_PX,
      top: PRESS_EXPAND_PX,
      bottom: PRESS_EXPAND_PX
    };
    var pressExpandLeft = pressRectOffset.left;
    var pressExpandTop = pressRectOffset.top;
    var pressExpandRight = pressRectOffset.right;
    var pressExpandBottom = pressRectOffset.bottom;
    var hitSlop = this.touchableGetHitSlop ? this.touchableGetHitSlop() : null;
    if (hitSlop) {
      pressExpandLeft += hitSlop.left || 0;
      pressExpandTop += hitSlop.top || 0;
      pressExpandRight += hitSlop.right || 0;
      pressExpandBottom += hitSlop.bottom || 0;
    }
    var touch = extractSingleTouch(e.nativeEvent);
    var pageX = touch && touch.pageX;
    var pageY = touch && touch.pageY;
    if (this.pressInLocation) {
      var movedDistance = this._getDistanceBetweenPoints(pageX, pageY, this.pressInLocation.pageX, this.pressInLocation.pageY);
      if (movedDistance > LONG_PRESS_ALLOWED_MOVEMENT) {
        this._cancelLongPressDelayTimeout();
      }
    }
    var isTouchWithinActive = pageX > positionOnActivate.left - pressExpandLeft && pageY > positionOnActivate.top - pressExpandTop && pageX < positionOnActivate.left + dimensionsOnActivate.width + pressExpandRight && pageY < positionOnActivate.top + dimensionsOnActivate.height + pressExpandBottom;
    if (isTouchWithinActive) {
      var prevState = this.state.touchable.touchState;
      this._receiveSignal(Signals.ENTER_PRESS_RECT, e);
      var curState = this.state.touchable.touchState;
      if (curState === States.RESPONDER_INACTIVE_PRESS_IN && prevState !== States.RESPONDER_INACTIVE_PRESS_IN) {
        this._cancelLongPressDelayTimeout();
      }
    } else {
      this._cancelLongPressDelayTimeout();
      this._receiveSignal(Signals.LEAVE_PRESS_RECT, e);
    }
  },
  touchableHandleFocus: function touchableHandleFocus(e) {
    this.props.onFocus && this.props.onFocus(e);
  },
  touchableHandleBlur: function touchableHandleBlur(e) {
    this.props.onBlur && this.props.onBlur(e);
  },
  _remeasureMetricsOnActivation: function _remeasureMetricsOnActivation() {
    var tag = this.state.touchable.responderID;
    if (tag == null) {
      return;
    }
    _UIManager.default.measure(tag, this._handleQueryLayout);
  },
  _handleQueryLayout: function _handleQueryLayout(l, t, w, h, globalX, globalY) {
    if (!l && !t && !w && !h && !globalX && !globalY) {
      return;
    }
    this.state.touchable.positionOnActivate && _Position.default.release(this.state.touchable.positionOnActivate);
    this.state.touchable.dimensionsOnActivate && _BoundingDimensions.default.release(this.state.touchable.dimensionsOnActivate);
    this.state.touchable.positionOnActivate = _Position.default.getPooled(globalX, globalY);
    this.state.touchable.dimensionsOnActivate = _BoundingDimensions.default.getPooled(w, h);
  },
  _handleDelay: function _handleDelay(e) {
    this.touchableDelayTimeout = null;
    this._receiveSignal(Signals.DELAY, e);
  },
  _handleLongDelay: function _handleLongDelay(e) {
    this.longPressDelayTimeout = null;
    var curState = this.state.touchable.touchState;
    if (curState !== States.RESPONDER_ACTIVE_PRESS_IN && curState !== States.RESPONDER_ACTIVE_LONG_PRESS_IN) {
      console.error('Attempted to transition from state `' + curState + '` to `' + States.RESPONDER_ACTIVE_LONG_PRESS_IN + '`, which is not supported. This is ' + 'most likely due to `Touchable.longPressDelayTimeout` not being cancelled.');
    } else {
      this._receiveSignal(Signals.LONG_PRESS_DETECTED, e);
    }
  },
  _receiveSignal: function _receiveSignal(signal, e) {
    var responderID = this.state.touchable.responderID;
    var curState = this.state.touchable.touchState;
    var nextState = Transitions[curState] && Transitions[curState][signal];
    if (!responderID && signal === Signals.RESPONDER_RELEASE) {
      return;
    }
    if (!nextState) {
      throw new Error('Unrecognized signal `' + signal + '` or state `' + curState + '` for Touchable responder `' + responderID + '`');
    }
    if (nextState === States.ERROR) {
      throw new Error('Touchable cannot transition from `' + curState + '` to `' + signal + '` for responder `' + responderID + '`');
    }
    if (curState !== nextState) {
      this._performSideEffectsForTransition(curState, nextState, signal, e);
      this.state.touchable.touchState = nextState;
    }
  },
  _cancelLongPressDelayTimeout: function _cancelLongPressDelayTimeout() {
    this.longPressDelayTimeout && clearTimeout(this.longPressDelayTimeout);
    this.longPressDelayTimeout = null;
  },
  _isHighlight: function _isHighlight(state) {
    return state === States.RESPONDER_ACTIVE_PRESS_IN || state === States.RESPONDER_ACTIVE_LONG_PRESS_IN;
  },
  _savePressInLocation: function _savePressInLocation(e) {
    var touch = extractSingleTouch(e.nativeEvent);
    var pageX = touch && touch.pageX;
    var pageY = touch && touch.pageY;
    var locationX = touch && touch.locationX;
    var locationY = touch && touch.locationY;
    this.pressInLocation = {
      pageX: pageX,
      pageY: pageY,
      locationX: locationX,
      locationY: locationY
    };
  },
  _getDistanceBetweenPoints: function _getDistanceBetweenPoints(aX, aY, bX, bY) {
    var deltaX = aX - bX;
    var deltaY = aY - bY;
    return Math.sqrt(deltaX * deltaX + deltaY * deltaY);
  },
  _performSideEffectsForTransition: function _performSideEffectsForTransition(curState, nextState, signal, e) {
    var curIsHighlight = this._isHighlight(curState);
    var newIsHighlight = this._isHighlight(nextState);
    var isFinalSignal = signal === Signals.RESPONDER_TERMINATED || signal === Signals.RESPONDER_RELEASE;
    if (isFinalSignal) {
      this._cancelLongPressDelayTimeout();
    }
    var isInitialTransition = curState === States.NOT_RESPONDER && nextState === States.RESPONDER_INACTIVE_PRESS_IN;
    var isActiveTransition = !IsActive[curState] && IsActive[nextState];
    if (isInitialTransition || isActiveTransition) {
      this._remeasureMetricsOnActivation();
    }
    if (IsPressingIn[curState] && signal === Signals.LONG_PRESS_DETECTED) {
      this.touchableHandleLongPress && this.touchableHandleLongPress(e);
    }
    if (newIsHighlight && !curIsHighlight) {
      this._startHighlight(e);
    } else if (!newIsHighlight && curIsHighlight) {
      this._endHighlight(e);
    }
    if (IsPressingIn[curState] && signal === Signals.RESPONDER_RELEASE) {
      var hasLongPressHandler = !!this.props.onLongPress;
      var pressIsLongButStillCallOnPress = IsLongPressingIn[curState] && (!hasLongPressHandler || !this.touchableLongPressCancelsPress());
      var shouldInvokePress = !IsLongPressingIn[curState] || pressIsLongButStillCallOnPress;
      if (shouldInvokePress && this.touchableHandlePress) {
        if (!newIsHighlight && !curIsHighlight) {
          this._startHighlight(e);
          this._endHighlight(e);
        }
        this.touchableHandlePress(e);
      }
    }
    this.touchableDelayTimeout && clearTimeout(this.touchableDelayTimeout);
    this.touchableDelayTimeout = null;
  },
  _playTouchSound: function _playTouchSound() {
    _UIManager.default.playTouchSound();
  },
  _startHighlight: function _startHighlight(e) {
    this._savePressInLocation(e);
    this.touchableHandleActivePressIn && this.touchableHandleActivePressIn(e);
  },
  _endHighlight: function _endHighlight(e) {
    var _this2 = this;
    if (this.touchableHandleActivePressOut) {
      if (this.touchableGetPressOutDelayMS && this.touchableGetPressOutDelayMS()) {
        this.pressOutDelayTimeout = setTimeout(function () {
          _this2.touchableHandleActivePressOut(e);
        }, this.touchableGetPressOutDelayMS());
      } else {
        this.touchableHandleActivePressOut(e);
      }
    }
  },
  touchableHandleKeyEvent: function touchableHandleKeyEvent(e) {
    var type = e.type,
      key = e.key;
    if (key === 'Enter' || key === ' ') {
      if (type === 'keydown') {
        if (!this._isTouchableKeyboardActive) {
          if (!this.state.touchable.touchState || this.state.touchable.touchState === States.NOT_RESPONDER) {
            this.touchableHandleResponderGrant(e);
            this._isTouchableKeyboardActive = true;
          }
        }
      } else if (type === 'keyup') {
        if (this._isTouchableKeyboardActive) {
          if (this.state.touchable.touchState && this.state.touchable.touchState !== States.NOT_RESPONDER) {
            this.touchableHandleResponderRelease(e);
            this._isTouchableKeyboardActive = false;
          }
        }
      }
      e.stopPropagation();
      if (!(key === 'Enter' && _AccessibilityUtil.default.propsToAriaRole(this.props) === 'link')) {
        e.preventDefault();
      }
    }
  },
  withoutDefaultFocusAndBlur: {}
};
var touchableHandleFocus = TouchableMixin.touchableHandleFocus,
  touchableHandleBlur = TouchableMixin.touchableHandleBlur,
  TouchableMixinWithoutDefaultFocusAndBlur = (0, _objectWithoutPropertiesLoose2.default)(TouchableMixin, ["touchableHandleFocus", "touchableHandleBlur"]);
TouchableMixin.withoutDefaultFocusAndBlur = TouchableMixinWithoutDefaultFocusAndBlur;
var Touchable = {
  Mixin: TouchableMixin,
  TOUCH_TARGET_DEBUG: false,
  renderDebugView: function renderDebugView(_ref) {
    var color = _ref.color,
      hitSlop = _ref.hitSlop;
    if (!Touchable.TOUCH_TARGET_DEBUG) {
      return null;
    }
    if (process.env.NODE_ENV !== 'production') {
      throw Error('Touchable.TOUCH_TARGET_DEBUG should not be enabled in prod!');
    }
    var debugHitSlopStyle = {};
    hitSlop = hitSlop || {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0
    };
    for (var key in hitSlop) {
      debugHitSlopStyle[key] = -hitSlop[key];
    }
    var normalizedColor = (0, _normalizeColors.default)(color);
    if (typeof normalizedColor !== 'number') {
      return null;
    }
    var hexColor = '#' + ('00000000' + normalizedColor.toString(16)).substr(-8);
    return _react.default.createElement(_View.default, {
      pointerEvents: "none",
      style: (0, _objectSpread2.default)({
        position: 'absolute',
        borderColor: hexColor.slice(0, -2) + '55',
        borderWidth: 1,
        borderStyle: 'dashed',
        backgroundColor: hexColor.slice(0, -2) + '0F'
      }, debugHitSlopStyle)
    });
  }
};
var _default = exports.default = Touchable;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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