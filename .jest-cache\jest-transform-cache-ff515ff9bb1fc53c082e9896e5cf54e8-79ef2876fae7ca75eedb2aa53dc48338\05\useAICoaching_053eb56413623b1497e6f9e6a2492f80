569bc3c7948fc5710cf13ccb33a21594
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1yhg68p8kn() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useAICoaching.ts";
  var hash = "eb99fb8126c0f4fc51e6e24cadfb1063e52903c1";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useAICoaching.ts",
    statementMap: {
      "0": {
        start: {
          line: 39,
          column: 40
        },
        end: {
          line: 39,
          column: 82
        }
      },
      "1": {
        start: {
          line: 40,
          column: 34
        },
        end: {
          line: 40,
          column: 75
        }
      },
      "2": {
        start: {
          line: 41,
          column: 40
        },
        end: {
          line: 41,
          column: 55
        }
      },
      "3": {
        start: {
          line: 42,
          column: 44
        },
        end: {
          line: 42,
          column: 63
        }
      },
      "4": {
        start: {
          line: 43,
          column: 19
        },
        end: {
          line: 43,
          column: 28
        }
      },
      "5": {
        start: {
          line: 44,
          column: 34
        },
        end: {
          line: 44,
          column: 47
        }
      },
      "6": {
        start: {
          line: 49,
          column: 28
        },
        end: {
          line: 87,
          column: 33
        }
      },
      "7": {
        start: {
          line: 50,
          column: 4
        },
        end: {
          line: 50,
          column: 22
        }
      },
      "8": {
        start: {
          line: 50,
          column: 15
        },
        end: {
          line: 50,
          column: 22
        }
      },
      "9": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 86,
          column: 5
        }
      },
      "10": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 27
        }
      },
      "11": {
        start: {
          line: 55,
          column: 24
        },
        end: {
          line: 55,
          column: 44
        }
      },
      "12": {
        start: {
          line: 56,
          column: 46
        },
        end: {
          line: 72,
          column: 7
        }
      },
      "13": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 74,
          column: 33
        }
      },
      "14": {
        start: {
          line: 77,
          column: 6
        },
        end: {
          line: 80,
          column: 8
        }
      },
      "15": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 28
        }
      },
      "16": {
        start: {
          line: 84,
          column: 6
        },
        end: {
          line: 84,
          column: 60
        }
      },
      "17": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 85,
          column: 28
        }
      },
      "18": {
        start: {
          line: 92,
          column: 27
        },
        end: {
          line: 99,
          column: 19
        }
      },
      "19": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 98,
          column: 5
        }
      },
      "20": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 94,
          column: 73
        }
      },
      "21": {
        start: {
          line: 94,
          column: 29
        },
        end: {
          line: 94,
          column: 71
        }
      },
      "22": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 97,
          column: 25
        }
      },
      "23": {
        start: {
          line: 104,
          column: 23
        },
        end: {
          line: 173,
          column: 61
        }
      },
      "24": {
        start: {
          line: 105,
          column: 4
        },
        end: {
          line: 105,
          column: 38
        }
      },
      "25": {
        start: {
          line: 105,
          column: 31
        },
        end: {
          line: 105,
          column: 38
        }
      },
      "26": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "27": {
        start: {
          line: 108,
          column: 25
        },
        end: {
          line: 113,
          column: 7
        }
      },
      "28": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 64
        }
      },
      "29": {
        start: {
          line: 116,
          column: 31
        },
        end: {
          line: 116,
          column: 62
        }
      },
      "30": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 135,
          column: 9
        }
      },
      "31": {
        start: {
          line: 120,
          column: 8
        },
        end: {
          line: 120,
          column: 31
        }
      },
      "32": {
        start: {
          line: 120,
          column: 19
        },
        end: {
          line: 120,
          column: 31
        }
      },
      "33": {
        start: {
          line: 122,
          column: 33
        },
        end: {
          line: 122,
          column: 74
        }
      },
      "34": {
        start: {
          line: 123,
          column: 28
        },
        end: {
          line: 123,
          column: 133
        }
      },
      "35": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 134,
          column: 10
        }
      },
      "36": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 169,
          column: 7
        }
      },
      "37": {
        start: {
          line: 139,
          column: 33
        },
        end: {
          line: 143,
          column: 9
        }
      },
      "38": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 154,
          column: 11
        }
      },
      "39": {
        start: {
          line: 147,
          column: 10
        },
        end: {
          line: 147,
          column: 33
        }
      },
      "40": {
        start: {
          line: 147,
          column: 21
        },
        end: {
          line: 147,
          column: 33
        }
      },
      "41": {
        start: {
          line: 148,
          column: 10
        },
        end: {
          line: 153,
          column: 12
        }
      },
      "42": {
        start: {
          line: 157,
          column: 8
        },
        end: {
          line: 160,
          column: 10
        }
      },
      "43": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 168,
          column: 9
        }
      },
      "44": {
        start: {
          line: 164,
          column: 10
        },
        end: {
          line: 167,
          column: 12
        }
      },
      "45": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 54
        }
      },
      "46": {
        start: {
          line: 178,
          column: 27
        },
        end: {
          line: 233,
          column: 27
        }
      },
      "47": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 179,
          column: 52
        }
      },
      "48": {
        start: {
          line: 179,
          column: 45
        },
        end: {
          line: 179,
          column: 52
        }
      },
      "49": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 232,
          column: 5
        }
      },
      "50": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 27
        }
      },
      "51": {
        start: {
          line: 185,
          column: 29
        },
        end: {
          line: 185,
          column: 114
        }
      },
      "52": {
        start: {
          line: 185,
          column: 67
        },
        end: {
          line: 185,
          column: 87
        }
      },
      "53": {
        start: {
          line: 186,
          column: 26
        },
        end: {
          line: 186,
          column: 61
        }
      },
      "54": {
        start: {
          line: 187,
          column: 26
        },
        end: {
          line: 187,
          column: 61
        }
      },
      "55": {
        start: {
          line: 190,
          column: 34
        },
        end: {
          line: 190,
          column: 36
        }
      },
      "56": {
        start: {
          line: 191,
          column: 37
        },
        end: {
          line: 191,
          column: 39
        }
      },
      "57": {
        start: {
          line: 192,
          column: 34
        },
        end: {
          line: 192,
          column: 36
        }
      },
      "58": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 196,
          column: 7
        }
      },
      "59": {
        start: {
          line: 195,
          column: 8
        },
        end: {
          line: 195,
          column: 57
        }
      },
      "60": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 199,
          column: 7
        }
      },
      "61": {
        start: {
          line: 198,
          column: 8
        },
        end: {
          line: 198,
          column: 56
        }
      },
      "62": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 202,
          column: 7
        }
      },
      "63": {
        start: {
          line: 201,
          column: 8
        },
        end: {
          line: 201,
          column: 61
        }
      },
      "64": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 207,
          column: 7
        }
      },
      "65": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 205,
          column: 66
        }
      },
      "66": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 206,
          column: 58
        }
      },
      "67": {
        start: {
          line: 208,
          column: 6
        },
        end: {
          line: 211,
          column: 7
        }
      },
      "68": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 209,
          column: 63
        }
      },
      "69": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 210,
          column: 54
        }
      },
      "70": {
        start: {
          line: 213,
          column: 35
        },
        end: {
          line: 218,
          column: 7
        }
      },
      "71": {
        start: {
          line: 220,
          column: 46
        },
        end: {
          line: 225,
          column: 7
        }
      },
      "72": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 31
        }
      },
      "73": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 228,
          column: 28
        }
      },
      "74": {
        start: {
          line: 230,
          column: 6
        },
        end: {
          line: 230,
          column: 57
        }
      },
      "75": {
        start: {
          line: 231,
          column: 6
        },
        end: {
          line: 231,
          column: 28
        }
      },
      "76": {
        start: {
          line: 238,
          column: 37
        },
        end: {
          line: 253,
          column: 27
        }
      },
      "77": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 239,
          column: 27
        }
      },
      "78": {
        start: {
          line: 239,
          column: 15
        },
        end: {
          line: 239,
          column: 27
        }
      },
      "79": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 252,
          column: 5
        }
      },
      "80": {
        start: {
          line: 242,
          column: 30
        },
        end: {
          line: 246,
          column: 7
        }
      },
      "81": {
        start: {
          line: 244,
          column: 37
        },
        end: {
          line: 244,
          column: 61
        }
      },
      "82": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 29
        }
      },
      "83": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 250,
          column: 70
        }
      },
      "84": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 251,
          column: 18
        }
      },
      "85": {
        start: {
          line: 256,
          column: 2
        },
        end: {
          line: 262,
          column: 9
        }
      },
      "86": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 261,
          column: 6
        }
      },
      "87": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 260,
          column: 7
        }
      },
      "88": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 259,
          column: 27
        }
      },
      "89": {
        start: {
          line: 264,
          column: 2
        },
        end: {
          line: 273,
          column: 4
        }
      },
      "90": {
        start: {
          line: 279,
          column: 2
        },
        end: {
          line: 279,
          column: 36
        }
      },
      "91": {
        start: {
          line: 279,
          column: 26
        },
        end: {
          line: 279,
          column: 36
        }
      },
      "92": {
        start: {
          line: 281,
          column: 20
        },
        end: {
          line: 281,
          column: 47
        }
      },
      "93": {
        start: {
          line: 281,
          column: 37
        },
        end: {
          line: 281,
          column: 46
        }
      },
      "94": {
        start: {
          line: 282,
          column: 18
        },
        end: {
          line: 282,
          column: 77
        }
      },
      "95": {
        start: {
          line: 282,
          column: 47
        },
        end: {
          line: 282,
          column: 54
        }
      },
      "96": {
        start: {
          line: 283,
          column: 19
        },
        end: {
          line: 283,
          column: 101
        }
      },
      "97": {
        start: {
          line: 283,
          column: 48
        },
        end: {
          line: 283,
          column: 78
        }
      },
      "98": {
        start: {
          line: 284,
          column: 28
        },
        end: {
          line: 284,
          column: 47
        }
      },
      "99": {
        start: {
          line: 287,
          column: 2
        },
        end: {
          line: 287,
          column: 67
        }
      },
      "100": {
        start: {
          line: 291,
          column: 2
        },
        end: {
          line: 291,
          column: 35
        }
      },
      "101": {
        start: {
          line: 291,
          column: 26
        },
        end: {
          line: 291,
          column: 35
        }
      },
      "102": {
        start: {
          line: 293,
          column: 20
        },
        end: {
          line: 293,
          column: 68
        }
      },
      "103": {
        start: {
          line: 294,
          column: 21
        },
        end: {
          line: 294,
          column: 66
        }
      },
      "104": {
        start: {
          line: 296,
          column: 19
        },
        end: {
          line: 296,
          column: 86
        }
      },
      "105": {
        start: {
          line: 296,
          column: 48
        },
        end: {
          line: 296,
          column: 63
        }
      },
      "106": {
        start: {
          line: 297,
          column: 20
        },
        end: {
          line: 297,
          column: 89
        }
      },
      "107": {
        start: {
          line: 297,
          column: 50
        },
        end: {
          line: 297,
          column: 65
        }
      },
      "108": {
        start: {
          line: 299,
          column: 2
        },
        end: {
          line: 299,
          column: 63
        }
      }
    },
    fnMap: {
      "0": {
        name: "useAICoaching",
        decl: {
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 29
          }
        },
        loc: {
          start: {
            line: 38,
            column: 53
          },
          end: {
            line: 274,
            column: 1
          }
        },
        line: 38
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 49,
            column: 40
          },
          end: {
            line: 49,
            column: 41
          }
        },
        loc: {
          start: {
            line: 49,
            column: 52
          },
          end: {
            line: 87,
            column: 3
          }
        },
        line: 49
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 92,
            column: 39
          },
          end: {
            line: 92,
            column: 40
          }
        },
        loc: {
          start: {
            line: 92,
            column: 45
          },
          end: {
            line: 99,
            column: 3
          }
        },
        line: 92
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 94,
            column: 21
          },
          end: {
            line: 94,
            column: 22
          }
        },
        loc: {
          start: {
            line: 94,
            column: 29
          },
          end: {
            line: 94,
            column: 71
          }
        },
        line: 94
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 104,
            column: 35
          },
          end: {
            line: 104,
            column: 36
          }
        },
        loc: {
          start: {
            line: 104,
            column: 78
          },
          end: {
            line: 173,
            column: 3
          }
        },
        line: 104
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 116,
            column: 23
          },
          end: {
            line: 116,
            column: 24
          }
        },
        loc: {
          start: {
            line: 116,
            column: 31
          },
          end: {
            line: 116,
            column: 62
          }
        },
        line: 116
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 119,
            column: 21
          },
          end: {
            line: 119,
            column: 22
          }
        },
        loc: {
          start: {
            line: 119,
            column: 29
          },
          end: {
            line: 135,
            column: 7
          }
        },
        line: 119
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 146,
            column: 23
          },
          end: {
            line: 146,
            column: 24
          }
        },
        loc: {
          start: {
            line: 146,
            column: 31
          },
          end: {
            line: 154,
            column: 9
          }
        },
        line: 146
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 178,
            column: 39
          },
          end: {
            line: 178,
            column: 40
          }
        },
        loc: {
          start: {
            line: 178,
            column: 51
          },
          end: {
            line: 233,
            column: 3
          }
        },
        line: 178
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 185,
            column: 50
          },
          end: {
            line: 185,
            column: 51
          }
        },
        loc: {
          start: {
            line: 185,
            column: 67
          },
          end: {
            line: 185,
            column: 87
          }
        },
        line: 185
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 238,
            column: 49
          },
          end: {
            line: 238,
            column: 50
          }
        },
        loc: {
          start: {
            line: 238,
            column: 61
          },
          end: {
            line: 253,
            column: 3
          }
        },
        line: 238
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 244,
            column: 26
          },
          end: {
            line: 244,
            column: 27
          }
        },
        loc: {
          start: {
            line: 244,
            column: 37
          },
          end: {
            line: 244,
            column: 61
          }
        },
        line: 244
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 256,
            column: 12
          },
          end: {
            line: 256,
            column: 13
          }
        },
        loc: {
          start: {
            line: 256,
            column: 18
          },
          end: {
            line: 262,
            column: 3
          }
        },
        line: 256
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 257,
            column: 11
          },
          end: {
            line: 257,
            column: 12
          }
        },
        loc: {
          start: {
            line: 257,
            column: 17
          },
          end: {
            line: 261,
            column: 5
          }
        },
        line: 257
      },
      "14": {
        name: "calculateConsistency",
        decl: {
          start: {
            line: 278,
            column: 9
          },
          end: {
            line: 278,
            column: 29
          }
        },
        loc: {
          start: {
            line: 278,
            column: 54
          },
          end: {
            line: 288,
            column: 1
          }
        },
        line: 278
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 281,
            column: 32
          },
          end: {
            line: 281,
            column: 33
          }
        },
        loc: {
          start: {
            line: 281,
            column: 37
          },
          end: {
            line: 281,
            column: 46
          }
        },
        line: 281
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 282,
            column: 35
          },
          end: {
            line: 282,
            column: 36
          }
        },
        loc: {
          start: {
            line: 282,
            column: 47
          },
          end: {
            line: 282,
            column: 54
          }
        },
        line: 282
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 283,
            column: 36
          },
          end: {
            line: 283,
            column: 37
          }
        },
        loc: {
          start: {
            line: 283,
            column: 48
          },
          end: {
            line: 283,
            column: 78
          }
        },
        line: 283
      },
      "18": {
        name: "calculateImprovement",
        decl: {
          start: {
            line: 290,
            column: 9
          },
          end: {
            line: 290,
            column: 29
          }
        },
        loc: {
          start: {
            line: 290,
            column: 54
          },
          end: {
            line: 300,
            column: 1
          }
        },
        line: 290
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 296,
            column: 36
          },
          end: {
            line: 296,
            column: 37
          }
        },
        loc: {
          start: {
            line: 296,
            column: 48
          },
          end: {
            line: 296,
            column: 63
          }
        },
        line: 296
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 297,
            column: 38
          },
          end: {
            line: 297,
            column: 39
          }
        },
        loc: {
          start: {
            line: 297,
            column: 50
          },
          end: {
            line: 297,
            column: 65
          }
        },
        line: 297
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 50,
            column: 22
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 4
          },
          end: {
            line: 50,
            column: 22
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "1": {
        loc: {
          start: {
            line: 93,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 4
          },
          end: {
            line: 98,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "2": {
        loc: {
          start: {
            line: 94,
            column: 29
          },
          end: {
            line: 94,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 94,
            column: 36
          },
          end: {
            line: 94,
            column: 64
          }
        }, {
          start: {
            line: 94,
            column: 67
          },
          end: {
            line: 94,
            column: 71
          }
        }],
        line: 94
      },
      "3": {
        loc: {
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 105,
            column: 38
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 105,
            column: 4
          },
          end: {
            line: 105,
            column: 38
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 105
      },
      "4": {
        loc: {
          start: {
            line: 105,
            column: 8
          },
          end: {
            line: 105,
            column: 29
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 105,
            column: 8
          },
          end: {
            line: 105,
            column: 20
          }
        }, {
          start: {
            line: 105,
            column: 24
          },
          end: {
            line: 105,
            column: 29
          }
        }],
        line: 105
      },
      "5": {
        loc: {
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 120,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 8
          },
          end: {
            line: 120,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 120
      },
      "6": {
        loc: {
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 169,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 169,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "7": {
        loc: {
          start: {
            line: 147,
            column: 10
          },
          end: {
            line: 147,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 10
          },
          end: {
            line: 147,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "8": {
        loc: {
          start: {
            line: 163,
            column: 8
          },
          end: {
            line: 168,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 8
          },
          end: {
            line: 168,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "9": {
        loc: {
          start: {
            line: 163,
            column: 12
          },
          end: {
            line: 163,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 163,
            column: 12
          },
          end: {
            line: 163,
            column: 24
          }
        }, {
          start: {
            line: 163,
            column: 28
          },
          end: {
            line: 163,
            column: 80
          }
        }],
        line: 163
      },
      "10": {
        loc: {
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 179,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 179,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "11": {
        loc: {
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 179,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 179,
            column: 13
          }
        }, {
          start: {
            line: 179,
            column: 17
          },
          end: {
            line: 179,
            column: 43
          }
        }],
        line: 179
      },
      "12": {
        loc: {
          start: {
            line: 194,
            column: 6
          },
          end: {
            line: 196,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 6
          },
          end: {
            line: 196,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "13": {
        loc: {
          start: {
            line: 197,
            column: 6
          },
          end: {
            line: 199,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 197,
            column: 6
          },
          end: {
            line: 199,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 197
      },
      "14": {
        loc: {
          start: {
            line: 200,
            column: 6
          },
          end: {
            line: 202,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 6
          },
          end: {
            line: 202,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "15": {
        loc: {
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 207,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 207,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "16": {
        loc: {
          start: {
            line: 208,
            column: 6
          },
          end: {
            line: 211,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 208,
            column: 6
          },
          end: {
            line: 211,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 208
      },
      "17": {
        loc: {
          start: {
            line: 221,
            column: 19
          },
          end: {
            line: 221,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 221,
            column: 42
          },
          end: {
            line: 221,
            column: 51
          }
        }, {
          start: {
            line: 221,
            column: 54
          },
          end: {
            line: 221,
            column: 95
          }
        }],
        line: 221
      },
      "18": {
        loc: {
          start: {
            line: 222,
            column: 22
          },
          end: {
            line: 222,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 222,
            column: 48
          },
          end: {
            line: 222,
            column: 60
          }
        }, {
          start: {
            line: 222,
            column: 63
          },
          end: {
            line: 222,
            column: 99
          }
        }],
        line: 222
      },
      "19": {
        loc: {
          start: {
            line: 223,
            column: 19
          },
          end: {
            line: 223,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 223,
            column: 42
          },
          end: {
            line: 223,
            column: 51
          }
        }, {
          start: {
            line: 223,
            column: 54
          },
          end: {
            line: 223,
            column: 83
          }
        }],
        line: 223
      },
      "20": {
        loc: {
          start: {
            line: 239,
            column: 4
          },
          end: {
            line: 239,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 239,
            column: 4
          },
          end: {
            line: 239,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 239
      },
      "21": {
        loc: {
          start: {
            line: 258,
            column: 6
          },
          end: {
            line: 260,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 258,
            column: 6
          },
          end: {
            line: 260,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 258
      },
      "22": {
        loc: {
          start: {
            line: 279,
            column: 2
          },
          end: {
            line: 279,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 2
          },
          end: {
            line: 279,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "23": {
        loc: {
          start: {
            line: 291,
            column: 2
          },
          end: {
            line: 291,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 2
          },
          end: {
            line: 291,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 291
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "eb99fb8126c0f4fc51e6e24cadfb1063e52903c1"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1yhg68p8kn = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1yhg68p8kn();
import { useState, useCallback, useEffect } from 'react';
import { aiAnalysisService } from "../services/aiAnalysis";
import { useAuth } from "../contexts/AuthContext";
import { useRealtime } from "./useRealtime";
export function useAICoaching() {
  cov_1yhg68p8kn().f[0]++;
  var _ref = (cov_1yhg68p8kn().s[0]++, useState(null)),
    _ref2 = _slicedToArray(_ref, 2),
    liveSession = _ref2[0],
    setLiveSession = _ref2[1];
  var _ref3 = (cov_1yhg68p8kn().s[1]++, useState(null)),
    _ref4 = _slicedToArray(_ref3, 2),
    insights = _ref4[0],
    setInsights = _ref4[1];
  var _ref5 = (cov_1yhg68p8kn().s[2]++, useState(false)),
    _ref6 = _slicedToArray(_ref5, 2),
    isAnalyzing = _ref6[0],
    setIsAnalyzing = _ref6[1];
  var _ref7 = (cov_1yhg68p8kn().s[3]++, useState([])),
    _ref8 = _slicedToArray(_ref7, 2),
    recentActions = _ref8[0],
    setRecentActions = _ref8[1];
  var _ref9 = (cov_1yhg68p8kn().s[4]++, useAuth()),
    user = _ref9.user;
  var _ref0 = (cov_1yhg68p8kn().s[5]++, useRealtime()),
    sendCoachingMessage = _ref0.sendCoachingMessage;
  var startLiveCoaching = (cov_1yhg68p8kn().s[6]++, useCallback(_asyncToGenerator(function* () {
    cov_1yhg68p8kn().f[1]++;
    cov_1yhg68p8kn().s[7]++;
    if (!user) {
      cov_1yhg68p8kn().b[0][0]++;
      cov_1yhg68p8kn().s[8]++;
      return;
    } else {
      cov_1yhg68p8kn().b[0][1]++;
    }
    cov_1yhg68p8kn().s[9]++;
    try {
      cov_1yhg68p8kn().s[10]++;
      setIsAnalyzing(true);
      var sessionId = (cov_1yhg68p8kn().s[11]++, `live-${Date.now()}`);
      var newSession = (cov_1yhg68p8kn().s[12]++, {
        sessionId: sessionId,
        isActive: true,
        currentFocus: 'Getting started with your practice session',
        suggestions: ['Start with a proper warm-up', 'Focus on your form over power', 'Take your time between shots'],
        encouragement: 'Ready to improve your game! Let\'s start strong.',
        performanceMetrics: {
          shotsAnalyzed: 0,
          accuracy: 0,
          consistency: 0,
          improvement: 0
        }
      });
      cov_1yhg68p8kn().s[13]++;
      setLiveSession(newSession);
      cov_1yhg68p8kn().s[14]++;
      yield sendCoachingMessage('Welcome to your AI coaching session! I\'ll be analyzing your technique and providing real-time feedback.', 'encouragement');
      cov_1yhg68p8kn().s[15]++;
      setIsAnalyzing(false);
    } catch (error) {
      cov_1yhg68p8kn().s[16]++;
      console.error('Error starting live coaching:', error);
      cov_1yhg68p8kn().s[17]++;
      setIsAnalyzing(false);
    }
  }), [user, sendCoachingMessage]));
  var stopLiveCoaching = (cov_1yhg68p8kn().s[18]++, useCallback(function () {
    cov_1yhg68p8kn().f[2]++;
    cov_1yhg68p8kn().s[19]++;
    if (liveSession) {
      cov_1yhg68p8kn().b[1][0]++;
      cov_1yhg68p8kn().s[20]++;
      setLiveSession(function (prev) {
        cov_1yhg68p8kn().f[3]++;
        cov_1yhg68p8kn().s[21]++;
        return prev ? (cov_1yhg68p8kn().b[2][0]++, Object.assign({}, prev, {
          isActive: false
        })) : (cov_1yhg68p8kn().b[2][1]++, null);
      });
      cov_1yhg68p8kn().s[22]++;
      generateInsights();
    } else {
      cov_1yhg68p8kn().b[1][1]++;
    }
  }, [liveSession]));
  var recordAction = (cov_1yhg68p8kn().s[23]++, useCallback(function () {
    var _ref10 = _asyncToGenerator(function* (action, quality) {
      cov_1yhg68p8kn().f[4]++;
      cov_1yhg68p8kn().s[24]++;
      if ((cov_1yhg68p8kn().b[4][0]++, !liveSession) || (cov_1yhg68p8kn().b[4][1]++, !user)) {
        cov_1yhg68p8kn().b[3][0]++;
        cov_1yhg68p8kn().s[25]++;
        return;
      } else {
        cov_1yhg68p8kn().b[3][1]++;
      }
      cov_1yhg68p8kn().s[26]++;
      try {
        var actionData = (cov_1yhg68p8kn().s[27]++, {
          action: action,
          quality: quality,
          timestamp: Date.now(),
          sessionId: liveSession.sessionId
        });
        cov_1yhg68p8kn().s[28]++;
        setRecentActions(function (prev) {
          cov_1yhg68p8kn().f[5]++;
          cov_1yhg68p8kn().s[29]++;
          return [].concat(_toConsumableArray(prev.slice(-9)), [actionData]);
        });
        cov_1yhg68p8kn().s[30]++;
        setLiveSession(function (prev) {
          cov_1yhg68p8kn().f[6]++;
          cov_1yhg68p8kn().s[31]++;
          if (!prev) {
            cov_1yhg68p8kn().b[5][0]++;
            cov_1yhg68p8kn().s[32]++;
            return null;
          } else {
            cov_1yhg68p8kn().b[5][1]++;
          }
          var newShotsAnalyzed = (cov_1yhg68p8kn().s[33]++, prev.performanceMetrics.shotsAnalyzed + 1);
          var newAccuracy = (cov_1yhg68p8kn().s[34]++, (prev.performanceMetrics.accuracy * prev.performanceMetrics.shotsAnalyzed + quality) / newShotsAnalyzed);
          cov_1yhg68p8kn().s[35]++;
          return Object.assign({}, prev, {
            performanceMetrics: Object.assign({}, prev.performanceMetrics, {
              shotsAnalyzed: newShotsAnalyzed,
              accuracy: Math.round(newAccuracy),
              consistency: Math.round(calculateConsistency([].concat(_toConsumableArray(recentActions), [actionData]))),
              improvement: Math.round(calculateImprovement([].concat(_toConsumableArray(recentActions), [actionData])))
            })
          });
        });
        cov_1yhg68p8kn().s[36]++;
        if (recentActions.length >= 2) {
          cov_1yhg68p8kn().b[6][0]++;
          var realtimeCoaching = (cov_1yhg68p8kn().s[37]++, yield aiAnalysisService.generateRealTimeCoaching(action, [].concat(_toConsumableArray(recentActions), [actionData]), {
            skillLevel: 'intermediate'
          }));
          cov_1yhg68p8kn().s[38]++;
          setLiveSession(function (prev) {
            cov_1yhg68p8kn().f[7]++;
            cov_1yhg68p8kn().s[39]++;
            if (!prev) {
              cov_1yhg68p8kn().b[7][0]++;
              cov_1yhg68p8kn().s[40]++;
              return null;
            } else {
              cov_1yhg68p8kn().b[7][1]++;
            }
            cov_1yhg68p8kn().s[41]++;
            return Object.assign({}, prev, {
              currentFocus: realtimeCoaching.adaptiveCoaching.nextFocus,
              suggestions: realtimeCoaching.liveAnalysis.suggestions,
              encouragement: realtimeCoaching.liveAnalysis.encouragement
            });
          });
          cov_1yhg68p8kn().s[42]++;
          yield sendCoachingMessage(realtimeCoaching.liveAnalysis.encouragement, 'encouragement');
          cov_1yhg68p8kn().s[43]++;
          if ((cov_1yhg68p8kn().b[9][0]++, quality < 70) && (cov_1yhg68p8kn().b[9][1]++, realtimeCoaching.liveAnalysis.suggestions.length > 0)) {
            cov_1yhg68p8kn().b[8][0]++;
            cov_1yhg68p8kn().s[44]++;
            yield sendCoachingMessage(realtimeCoaching.liveAnalysis.suggestions[0], 'correction');
          } else {
            cov_1yhg68p8kn().b[8][1]++;
          }
        } else {
          cov_1yhg68p8kn().b[6][1]++;
        }
      } catch (error) {
        cov_1yhg68p8kn().s[45]++;
        console.error('Error recording action:', error);
      }
    });
    return function (_x, _x2) {
      return _ref10.apply(this, arguments);
    };
  }(), [liveSession, user, recentActions, sendCoachingMessage]));
  var generateInsights = (cov_1yhg68p8kn().s[46]++, useCallback(_asyncToGenerator(function* () {
    cov_1yhg68p8kn().f[8]++;
    cov_1yhg68p8kn().s[47]++;
    if ((cov_1yhg68p8kn().b[11][0]++, !user) || (cov_1yhg68p8kn().b[11][1]++, recentActions.length === 0)) {
      cov_1yhg68p8kn().b[10][0]++;
      cov_1yhg68p8kn().s[48]++;
      return;
    } else {
      cov_1yhg68p8kn().b[10][1]++;
    }
    cov_1yhg68p8kn().s[49]++;
    try {
      cov_1yhg68p8kn().s[50]++;
      setIsAnalyzing(true);
      var averageQuality = (cov_1yhg68p8kn().s[51]++, recentActions.reduce(function (sum, action) {
        cov_1yhg68p8kn().f[9]++;
        cov_1yhg68p8kn().s[52]++;
        return sum + action.quality;
      }, 0) / recentActions.length);
      var consistency = (cov_1yhg68p8kn().s[53]++, calculateConsistency(recentActions));
      var improvement = (cov_1yhg68p8kn().s[54]++, calculateImprovement(recentActions));
      var strengths = (cov_1yhg68p8kn().s[55]++, []);
      var improvements = (cov_1yhg68p8kn().s[56]++, []);
      var nextSteps = (cov_1yhg68p8kn().s[57]++, []);
      cov_1yhg68p8kn().s[58]++;
      if (averageQuality > 75) {
        cov_1yhg68p8kn().b[12][0]++;
        cov_1yhg68p8kn().s[59]++;
        strengths.push('Excellent shot quality overall');
      } else {
        cov_1yhg68p8kn().b[12][1]++;
      }
      cov_1yhg68p8kn().s[60]++;
      if (consistency > 70) {
        cov_1yhg68p8kn().b[13][0]++;
        cov_1yhg68p8kn().s[61]++;
        strengths.push('Good consistency in technique');
      } else {
        cov_1yhg68p8kn().b[13][1]++;
      }
      cov_1yhg68p8kn().s[62]++;
      if (improvement > 0) {
        cov_1yhg68p8kn().b[14][0]++;
        cov_1yhg68p8kn().s[63]++;
        strengths.push('Showing improvement during session');
      } else {
        cov_1yhg68p8kn().b[14][1]++;
      }
      cov_1yhg68p8kn().s[64]++;
      if (averageQuality < 60) {
        cov_1yhg68p8kn().b[15][0]++;
        cov_1yhg68p8kn().s[65]++;
        improvements.push('Focus on shot accuracy and placement');
        cov_1yhg68p8kn().s[66]++;
        nextSteps.push('Practice basic technique drills');
      } else {
        cov_1yhg68p8kn().b[15][1]++;
      }
      cov_1yhg68p8kn().s[67]++;
      if (consistency < 50) {
        cov_1yhg68p8kn().b[16][0]++;
        cov_1yhg68p8kn().s[68]++;
        improvements.push('Work on consistency between shots');
        cov_1yhg68p8kn().s[69]++;
        nextSteps.push('Slow down and focus on form');
      } else {
        cov_1yhg68p8kn().b[16][1]++;
      }
      var motivationalMessages = (cov_1yhg68p8kn().s[70]++, ['Great work today! Keep practicing and you\'ll see continued improvement.', 'Your dedication is showing in your technique. Keep it up!', 'Every practice session makes you better. Stay focused on your goals.', 'Excellent effort! Remember, consistency beats perfection.']);
      var newInsights = (cov_1yhg68p8kn().s[71]++, {
        strengths: strengths.length > 0 ? (cov_1yhg68p8kn().b[17][0]++, strengths) : (cov_1yhg68p8kn().b[17][1]++, ['Good effort and focus during practice']),
        improvements: improvements.length > 0 ? (cov_1yhg68p8kn().b[18][0]++, improvements) : (cov_1yhg68p8kn().b[18][1]++, ['Continue working on fundamentals']),
        nextSteps: nextSteps.length > 0 ? (cov_1yhg68p8kn().b[19][0]++, nextSteps) : (cov_1yhg68p8kn().b[19][1]++, ['Keep practicing regularly']),
        motivationalMessage: motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)]
      });
      cov_1yhg68p8kn().s[72]++;
      setInsights(newInsights);
      cov_1yhg68p8kn().s[73]++;
      setIsAnalyzing(false);
    } catch (error) {
      cov_1yhg68p8kn().s[74]++;
      console.error('Error generating insights:', error);
      cov_1yhg68p8kn().s[75]++;
      setIsAnalyzing(false);
    }
  }), [user, recentActions]));
  var getTrainingRecommendations = (cov_1yhg68p8kn().s[76]++, useCallback(_asyncToGenerator(function* () {
    cov_1yhg68p8kn().f[10]++;
    cov_1yhg68p8kn().s[77]++;
    if (!user) {
      cov_1yhg68p8kn().b[20][0]++;
      cov_1yhg68p8kn().s[78]++;
      return null;
    } else {
      cov_1yhg68p8kn().b[20][1]++;
    }
    cov_1yhg68p8kn().s[79]++;
    try {
      var recommendations = (cov_1yhg68p8kn().s[80]++, yield aiAnalysisService.generateTrainingRecommendations({
        skillLevel: 'intermediate'
      }, recentActions.map(function (action) {
        cov_1yhg68p8kn().f[11]++;
        cov_1yhg68p8kn().s[81]++;
        return {
          title: action.action
        };
      }), ['improve consistency', 'increase accuracy']));
      cov_1yhg68p8kn().s[82]++;
      return recommendations;
    } catch (error) {
      cov_1yhg68p8kn().s[83]++;
      console.error('Error getting training recommendations:', error);
      cov_1yhg68p8kn().s[84]++;
      return null;
    }
  }), [user, recentActions]));
  cov_1yhg68p8kn().s[85]++;
  useEffect(function () {
    cov_1yhg68p8kn().f[12]++;
    cov_1yhg68p8kn().s[86]++;
    return function () {
      cov_1yhg68p8kn().f[13]++;
      cov_1yhg68p8kn().s[87]++;
      if (liveSession != null && liveSession.isActive) {
        cov_1yhg68p8kn().b[21][0]++;
        cov_1yhg68p8kn().s[88]++;
        stopLiveCoaching();
      } else {
        cov_1yhg68p8kn().b[21][1]++;
      }
    };
  }, []);
  cov_1yhg68p8kn().s[89]++;
  return {
    liveSession: liveSession,
    insights: insights,
    isAnalyzing: isAnalyzing,
    startLiveCoaching: startLiveCoaching,
    stopLiveCoaching: stopLiveCoaching,
    recordAction: recordAction,
    generateInsights: generateInsights,
    getTrainingRecommendations: getTrainingRecommendations
  };
}
function calculateConsistency(actions) {
  cov_1yhg68p8kn().f[14]++;
  cov_1yhg68p8kn().s[90]++;
  if (actions.length < 2) {
    cov_1yhg68p8kn().b[22][0]++;
    cov_1yhg68p8kn().s[91]++;
    return 50;
  } else {
    cov_1yhg68p8kn().b[22][1]++;
  }
  var qualities = (cov_1yhg68p8kn().s[92]++, actions.map(function (a) {
    cov_1yhg68p8kn().f[15]++;
    cov_1yhg68p8kn().s[93]++;
    return a.quality;
  }));
  var average = (cov_1yhg68p8kn().s[94]++, qualities.reduce(function (sum, q) {
    cov_1yhg68p8kn().f[16]++;
    cov_1yhg68p8kn().s[95]++;
    return sum + q;
  }, 0) / qualities.length);
  var variance = (cov_1yhg68p8kn().s[96]++, qualities.reduce(function (sum, q) {
    cov_1yhg68p8kn().f[17]++;
    cov_1yhg68p8kn().s[97]++;
    return sum + Math.pow(q - average, 2);
  }, 0) / qualities.length);
  var standardDeviation = (cov_1yhg68p8kn().s[98]++, Math.sqrt(variance));
  cov_1yhg68p8kn().s[99]++;
  return Math.max(0, Math.min(100, 100 - standardDeviation * 2));
}
function calculateImprovement(actions) {
  cov_1yhg68p8kn().f[18]++;
  cov_1yhg68p8kn().s[100]++;
  if (actions.length < 3) {
    cov_1yhg68p8kn().b[23][0]++;
    cov_1yhg68p8kn().s[101]++;
    return 0;
  } else {
    cov_1yhg68p8kn().b[23][1]++;
  }
  var firstHalf = (cov_1yhg68p8kn().s[102]++, actions.slice(0, Math.floor(actions.length / 2)));
  var secondHalf = (cov_1yhg68p8kn().s[103]++, actions.slice(Math.floor(actions.length / 2)));
  var firstAvg = (cov_1yhg68p8kn().s[104]++, firstHalf.reduce(function (sum, a) {
    cov_1yhg68p8kn().f[19]++;
    cov_1yhg68p8kn().s[105]++;
    return sum + a.quality;
  }, 0) / firstHalf.length);
  var secondAvg = (cov_1yhg68p8kn().s[106]++, secondHalf.reduce(function (sum, a) {
    cov_1yhg68p8kn().f[20]++;
    cov_1yhg68p8kn().s[107]++;
    return sum + a.quality;
  }, 0) / secondHalf.length);
  cov_1yhg68p8kn().s[108]++;
  return Math.round((secondAvg - firstAvg) / firstAvg * 100);
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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