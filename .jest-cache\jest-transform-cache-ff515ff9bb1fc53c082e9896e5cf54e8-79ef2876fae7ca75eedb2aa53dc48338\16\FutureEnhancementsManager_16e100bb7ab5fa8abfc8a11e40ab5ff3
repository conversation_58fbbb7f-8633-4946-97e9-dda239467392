c0d76907c9f295ac3edddd32551a7171
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2ehhv10rb3() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\planning\\FutureEnhancementsManager.ts";
  var hash = "4b90fc6cbad411db3012c4da31332a5e046e415d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\planning\\FutureEnhancementsManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 99,
          column: 51
        },
        end: {
          line: 99,
          column: 60
        }
      },
      "1": {
        start: {
          line: 100,
          column: 59
        },
        end: {
          line: 100,
          column: 68
        }
      },
      "2": {
        start: {
          line: 101,
          column: 53
        },
        end: {
          line: 101,
          column: 62
        }
      },
      "3": {
        start: {
          line: 102,
          column: 46
        },
        end: {
          line: 102,
          column: 48
        }
      },
      "4": {
        start: {
          line: 104,
          column: 90
        },
        end: {
          line: 321,
          column: 3
        }
      },
      "5": {
        start: {
          line: 324,
          column: 4
        },
        end: {
          line: 324,
          column: 40
        }
      },
      "6": {
        start: {
          line: 331,
          column: 4
        },
        end: {
          line: 347,
          column: 5
        }
      },
      "7": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 333,
          column: 36
        }
      },
      "8": {
        start: {
          line: 336,
          column: 6
        },
        end: {
          line: 336,
          column: 40
        }
      },
      "9": {
        start: {
          line: 339,
          column: 6
        },
        end: {
          line: 339,
          column: 33
        }
      },
      "10": {
        start: {
          line: 342,
          column: 6
        },
        end: {
          line: 342,
          column: 37
        }
      },
      "11": {
        start: {
          line: 344,
          column: 6
        },
        end: {
          line: 344,
          column: 74
        }
      },
      "12": {
        start: {
          line: 346,
          column: 6
        },
        end: {
          line: 346,
          column: 80
        }
      },
      "13": {
        start: {
          line: 364,
          column: 19
        },
        end: {
          line: 364,
          column: 58
        }
      },
      "14": {
        start: {
          line: 365,
          column: 80
        },
        end: {
          line: 365,
          column: 82
        }
      },
      "15": {
        start: {
          line: 368,
          column: 4
        },
        end: {
          line: 376,
          column: 7
        }
      },
      "16": {
        start: {
          line: 369,
          column: 6
        },
        end: {
          line: 375,
          column: 9
        }
      },
      "17": {
        start: {
          line: 370,
          column: 8
        },
        end: {
          line: 374,
          column: 11
        }
      },
      "18": {
        start: {
          line: 379,
          column: 4
        },
        end: {
          line: 379,
          column: 45
        }
      },
      "19": {
        start: {
          line: 379,
          column: 28
        },
        end: {
          line: 379,
          column: 43
        }
      },
      "20": {
        start: {
          line: 382,
          column: 28
        },
        end: {
          line: 382,
          column: 90
        }
      },
      "21": {
        start: {
          line: 382,
          column: 58
        },
        end: {
          line: 382,
          column: 86
        }
      },
      "22": {
        start: {
          line: 385,
          column: 24
        },
        end: {
          line: 385,
          column: 27
        }
      },
      "23": {
        start: {
          line: 388,
          column: 21
        },
        end: {
          line: 388,
          column: 57
        }
      },
      "24": {
        start: {
          line: 388,
          column: 45
        },
        end: {
          line: 388,
          column: 56
        }
      },
      "25": {
        start: {
          line: 389,
          column: 27
        },
        end: {
          line: 393,
          column: 5
        }
      },
      "26": {
        start: {
          line: 390,
          column: 36
        },
        end: {
          line: 390,
          column: 71
        }
      },
      "27": {
        start: {
          line: 391,
          column: 38
        },
        end: {
          line: 391,
          column: 113
        }
      },
      "28": {
        start: {
          line: 392,
          column: 35
        },
        end: {
          line: 392,
          column: 71
        }
      },
      "29": {
        start: {
          line: 395,
          column: 4
        },
        end: {
          line: 401,
          column: 6
        }
      },
      "30": {
        start: {
          line: 414,
          column: 57
        },
        end: {
          line: 414,
          column: 59
        }
      },
      "31": {
        start: {
          line: 415,
          column: 54
        },
        end: {
          line: 415,
          column: 56
        }
      },
      "32": {
        start: {
          line: 416,
          column: 57
        },
        end: {
          line: 416,
          column: 59
        }
      },
      "33": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 422,
          column: 7
        }
      },
      "34": {
        start: {
          line: 419,
          column: 6
        },
        end: {
          line: 419,
          column: 105
        }
      },
      "35": {
        start: {
          line: 420,
          column: 6
        },
        end: {
          line: 420,
          column: 93
        }
      },
      "36": {
        start: {
          line: 421,
          column: 6
        },
        end: {
          line: 421,
          column: 105
        }
      },
      "37": {
        start: {
          line: 425,
          column: 27
        },
        end: {
          line: 425,
          column: 62
        }
      },
      "38": {
        start: {
          line: 427,
          column: 4
        },
        end: {
          line: 433,
          column: 6
        }
      },
      "39": {
        start: {
          line: 450,
          column: 19
        },
        end: {
          line: 450,
          column: 61
        }
      },
      "40": {
        start: {
          line: 451,
          column: 33
        },
        end: {
          line: 451,
          column: 86
        }
      },
      "41": {
        start: {
          line: 451,
          column: 56
        },
        end: {
          line: 451,
          column: 85
        }
      },
      "42": {
        start: {
          line: 453,
          column: 36
        },
        end: {
          line: 482,
          column: 6
        }
      },
      "43": {
        start: {
          line: 458,
          column: 6
        },
        end: {
          line: 474,
          column: 7
        }
      },
      "44": {
        start: {
          line: 459,
          column: 8
        },
        end: {
          line: 459,
          column: 33
        }
      },
      "45": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 460,
          column: 59
        }
      },
      "46": {
        start: {
          line: 461,
          column: 8
        },
        end: {
          line: 461,
          column: 31
        }
      },
      "47": {
        start: {
          line: 462,
          column: 13
        },
        end: {
          line: 474,
          column: 7
        }
      },
      "48": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 463,
          column: 33
        }
      },
      "49": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 464,
          column: 62
        }
      },
      "50": {
        start: {
          line: 465,
          column: 8
        },
        end: {
          line: 465,
          column: 32
        }
      },
      "51": {
        start: {
          line: 466,
          column: 13
        },
        end: {
          line: 474,
          column: 7
        }
      },
      "52": {
        start: {
          line: 467,
          column: 8
        },
        end: {
          line: 467,
          column: 34
        }
      },
      "53": {
        start: {
          line: 468,
          column: 8
        },
        end: {
          line: 468,
          column: 59
        }
      },
      "54": {
        start: {
          line: 469,
          column: 8
        },
        end: {
          line: 469,
          column: 33
        }
      },
      "55": {
        start: {
          line: 471,
          column: 8
        },
        end: {
          line: 471,
          column: 32
        }
      },
      "56": {
        start: {
          line: 472,
          column: 8
        },
        end: {
          line: 472,
          column: 49
        }
      },
      "57": {
        start: {
          line: 473,
          column: 8
        },
        end: {
          line: 473,
          column: 32
        }
      },
      "58": {
        start: {
          line: 476,
          column: 6
        },
        end: {
          line: 481,
          column: 8
        }
      },
      "59": {
        start: {
          line: 485,
          column: 33
        },
        end: {
          line: 485,
          column: 69
        }
      },
      "60": {
        start: {
          line: 487,
          column: 4
        },
        end: {
          line: 492,
          column: 6
        }
      },
      "61": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 504,
          column: 7
        }
      },
      "62": {
        start: {
          line: 501,
          column: 21
        },
        end: {
          line: 501,
          column: 51
        }
      },
      "63": {
        start: {
          line: 502,
          column: 21
        },
        end: {
          line: 502,
          column: 51
        }
      },
      "64": {
        start: {
          line: 503,
          column: 6
        },
        end: {
          line: 503,
          column: 29
        }
      },
      "65": {
        start: {
          line: 522,
          column: 28
        },
        end: {
          line: 524,
          column: 5
        }
      },
      "66": {
        start: {
          line: 523,
          column: 6
        },
        end: {
          line: 523,
          column: 40
        }
      },
      "67": {
        start: {
          line: 526,
          column: 54
        },
        end: {
          line: 526,
          column: 56
        }
      },
      "68": {
        start: {
          line: 527,
          column: 51
        },
        end: {
          line: 527,
          column: 53
        }
      },
      "69": {
        start: {
          line: 529,
          column: 4
        },
        end: {
          line: 534,
          column: 7
        }
      },
      "70": {
        start: {
          line: 530,
          column: 6
        },
        end: {
          line: 531,
          column: 86
        }
      },
      "71": {
        start: {
          line: 532,
          column: 6
        },
        end: {
          line: 533,
          column: 80
        }
      },
      "72": {
        start: {
          line: 536,
          column: 4
        },
        end: {
          line: 547,
          column: 6
        }
      },
      "73": {
        start: {
          line: 553,
          column: 4
        },
        end: {
          line: 564,
          column: 7
        }
      },
      "74": {
        start: {
          line: 554,
          column: 43
        },
        end: {
          line: 561,
          column: 7
        }
      },
      "75": {
        start: {
          line: 563,
          column: 6
        },
        end: {
          line: 563,
          column: 61
        }
      },
      "76": {
        start: {
          line: 569,
          column: 22
        },
        end: {
          line: 571,
          column: 67
        }
      },
      "77": {
        start: {
          line: 573,
          column: 21
        },
        end: {
          line: 574,
          column: 67
        }
      },
      "78": {
        start: {
          line: 576,
          column: 21
        },
        end: {
          line: 578,
          column: 62
        }
      },
      "79": {
        start: {
          line: 580,
          column: 20
        },
        end: {
          line: 580,
          column: 57
        }
      },
      "80": {
        start: {
          line: 582,
          column: 4
        },
        end: {
          line: 582,
          column: 54
        }
      },
      "81": {
        start: {
          line: 586,
          column: 63
        },
        end: {
          line: 623,
          column: 5
        }
      },
      "82": {
        start: {
          line: 625,
          column: 4
        },
        end: {
          line: 637,
          column: 7
        }
      },
      "83": {
        start: {
          line: 626,
          column: 41
        },
        end: {
          line: 634,
          column: 7
        }
      },
      "84": {
        start: {
          line: 636,
          column: 6
        },
        end: {
          line: 636,
          column: 53
        }
      },
      "85": {
        start: {
          line: 641,
          column: 35
        },
        end: {
          line: 712,
          column: 5
        }
      },
      "86": {
        start: {
          line: 714,
          column: 4
        },
        end: {
          line: 716,
          column: 7
        }
      },
      "87": {
        start: {
          line: 715,
          column: 6
        },
        end: {
          line: 715,
          column: 46
        }
      },
      "88": {
        start: {
          line: 720,
          column: 4
        },
        end: {
          line: 720,
          column: 69
        }
      },
      "89": {
        start: {
          line: 724,
          column: 30
        },
        end: {
          line: 726,
          column: 5
        }
      },
      "90": {
        start: {
          line: 725,
          column: 21
        },
        end: {
          line: 725,
          column: 57
        }
      },
      "91": {
        start: {
          line: 728,
          column: 4
        },
        end: {
          line: 728,
          column: 77
        }
      },
      "92": {
        start: {
          line: 732,
          column: 29
        },
        end: {
          line: 733,
          column: 65
        }
      },
      "93": {
        start: {
          line: 733,
          column: 23
        },
        end: {
          line: 733,
          column: 64
        }
      },
      "94": {
        start: {
          line: 735,
          column: 4
        },
        end: {
          line: 735,
          column: 72
        }
      },
      "95": {
        start: {
          line: 739,
          column: 28
        },
        end: {
          line: 739,
          column: 71
        }
      },
      "96": {
        start: {
          line: 740,
          column: 26
        },
        end: {
          line: 740,
          column: 68
        }
      },
      "97": {
        start: {
          line: 742,
          column: 24
        },
        end: {
          line: 746,
          column: 9
        }
      },
      "98": {
        start: {
          line: 748,
          column: 29
        },
        end: {
          line: 748,
          column: 60
        }
      },
      "99": {
        start: {
          line: 750,
          column: 4
        },
        end: {
          line: 750,
          column: 64
        }
      },
      "100": {
        start: {
          line: 755,
          column: 41
        },
        end: {
          line: 755,
          column: 72
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 323,
            column: 2
          },
          end: {
            line: 323,
            column: 3
          }
        },
        loc: {
          start: {
            line: 323,
            column: 16
          },
          end: {
            line: 325,
            column: 3
          }
        },
        line: 323
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 330,
            column: 2
          },
          end: {
            line: 330,
            column: 3
          }
        },
        loc: {
          start: {
            line: 330,
            column: 62
          },
          end: {
            line: 348,
            column: 3
          }
        },
        line: 330
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 353,
            column: 2
          },
          end: {
            line: 353,
            column: 3
          }
        },
        loc: {
          start: {
            line: 363,
            column: 4
          },
          end: {
            line: 402,
            column: 3
          }
        },
        line: 363
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 368,
            column: 19
          },
          end: {
            line: 368,
            column: 20
          }
        },
        loc: {
          start: {
            line: 368,
            column: 28
          },
          end: {
            line: 376,
            column: 5
          }
        },
        line: 368
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 369,
            column: 31
          },
          end: {
            line: 369,
            column: 32
          }
        },
        loc: {
          start: {
            line: 369,
            column: 44
          },
          end: {
            line: 375,
            column: 7
          }
        },
        line: 369
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 379,
            column: 18
          },
          end: {
            line: 379,
            column: 19
          }
        },
        loc: {
          start: {
            line: 379,
            column: 28
          },
          end: {
            line: 379,
            column: 43
          }
        },
        line: 379
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 382,
            column: 42
          },
          end: {
            line: 382,
            column: 43
          }
        },
        loc: {
          start: {
            line: 382,
            column: 58
          },
          end: {
            line: 382,
            column: 86
          }
        },
        line: 382
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 388,
            column: 36
          },
          end: {
            line: 388,
            column: 37
          }
        },
        loc: {
          start: {
            line: 388,
            column: 45
          },
          end: {
            line: 388,
            column: 56
          }
        },
        line: 388
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 390,
            column: 28
          },
          end: {
            line: 390,
            column: 29
          }
        },
        loc: {
          start: {
            line: 390,
            column: 36
          },
          end: {
            line: 390,
            column: 71
          }
        },
        line: 390
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 391,
            column: 30
          },
          end: {
            line: 391,
            column: 31
          }
        },
        loc: {
          start: {
            line: 391,
            column: 38
          },
          end: {
            line: 391,
            column: 113
          }
        },
        line: 391
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 392,
            column: 27
          },
          end: {
            line: 392,
            column: 28
          }
        },
        loc: {
          start: {
            line: 392,
            column: 35
          },
          end: {
            line: 392,
            column: 71
          }
        },
        line: 392
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 407,
            column: 2
          },
          end: {
            line: 407,
            column: 3
          }
        },
        loc: {
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 434,
            column: 3
          }
        },
        line: 413
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 418,
            column: 36
          },
          end: {
            line: 418,
            column: 37
          }
        },
        loc: {
          start: {
            line: 418,
            column: 51
          },
          end: {
            line: 422,
            column: 5
          }
        },
        line: 418
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 439,
            column: 2
          },
          end: {
            line: 439,
            column: 3
          }
        },
        loc: {
          start: {
            line: 449,
            column: 4
          },
          end: {
            line: 493,
            column: 3
          }
        },
        line: 449
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 451,
            column: 47
          },
          end: {
            line: 451,
            column: 48
          }
        },
        loc: {
          start: {
            line: 451,
            column: 56
          },
          end: {
            line: 451,
            column: 85
          }
        },
        line: 451
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 453,
            column: 47
          },
          end: {
            line: 453,
            column: 48
          }
        },
        loc: {
          start: {
            line: 453,
            column: 56
          },
          end: {
            line: 482,
            column: 5
          }
        },
        line: 453
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 498,
            column: 2
          },
          end: {
            line: 498,
            column: 3
          }
        },
        loc: {
          start: {
            line: 498,
            column: 42
          },
          end: {
            line: 505,
            column: 3
          }
        },
        line: 498
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 499,
            column: 40
          },
          end: {
            line: 499,
            column: 41
          }
        },
        loc: {
          start: {
            line: 499,
            column: 50
          },
          end: {
            line: 504,
            column: 5
          }
        },
        line: 499
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 510,
            column: 2
          },
          end: {
            line: 510,
            column: 3
          }
        },
        loc: {
          start: {
            line: 521,
            column: 4
          },
          end: {
            line: 548,
            column: 3
          }
        },
        line: 521
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 522,
            column: 59
          },
          end: {
            line: 522,
            column: 60
          }
        },
        loc: {
          start: {
            line: 523,
            column: 6
          },
          end: {
            line: 523,
            column: 40
          }
        },
        line: 523
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 529,
            column: 36
          },
          end: {
            line: 529,
            column: 37
          }
        },
        loc: {
          start: {
            line: 529,
            column: 51
          },
          end: {
            line: 534,
            column: 5
          }
        },
        line: 529
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 552,
            column: 2
          },
          end: {
            line: 552,
            column: 3
          }
        },
        loc: {
          start: {
            line: 552,
            column: 41
          },
          end: {
            line: 565,
            column: 3
          }
        },
        line: 552
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 553,
            column: 37
          },
          end: {
            line: 553,
            column: 38
          }
        },
        loc: {
          start: {
            line: 553,
            column: 52
          },
          end: {
            line: 564,
            column: 5
          }
        },
        line: 553
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 567,
            column: 2
          },
          end: {
            line: 567,
            column: 3
          }
        },
        loc: {
          start: {
            line: 567,
            column: 77
          },
          end: {
            line: 583,
            column: 3
          }
        },
        line: 567
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 585,
            column: 2
          },
          end: {
            line: 585,
            column: 3
          }
        },
        loc: {
          start: {
            line: 585,
            column: 45
          },
          end: {
            line: 638,
            column: 3
          }
        },
        line: 585
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 625,
            column: 19
          },
          end: {
            line: 625,
            column: 20
          }
        },
        loc: {
          start: {
            line: 625,
            column: 28
          },
          end: {
            line: 637,
            column: 5
          }
        },
        line: 625
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 640,
            column: 2
          },
          end: {
            line: 640,
            column: 3
          }
        },
        loc: {
          start: {
            line: 640,
            column: 38
          },
          end: {
            line: 717,
            column: 3
          }
        },
        line: 640
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 714,
            column: 19
          },
          end: {
            line: 714,
            column: 20
          }
        },
        loc: {
          start: {
            line: 714,
            column: 28
          },
          end: {
            line: 716,
            column: 5
          }
        },
        line: 714
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 719,
            column: 2
          },
          end: {
            line: 719,
            column: 3
          }
        },
        loc: {
          start: {
            line: 719,
            column: 42
          },
          end: {
            line: 721,
            column: 3
          }
        },
        line: 719
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 723,
            column: 2
          },
          end: {
            line: 723,
            column: 3
          }
        },
        loc: {
          start: {
            line: 723,
            column: 49
          },
          end: {
            line: 729,
            column: 3
          }
        },
        line: 723
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 725,
            column: 6
          },
          end: {
            line: 725,
            column: 7
          }
        },
        loc: {
          start: {
            line: 725,
            column: 21
          },
          end: {
            line: 725,
            column: 57
          }
        },
        line: 725
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 731,
            column: 2
          },
          end: {
            line: 731,
            column: 3
          }
        },
        loc: {
          start: {
            line: 731,
            column: 50
          },
          end: {
            line: 736,
            column: 3
          }
        },
        line: 731
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 733,
            column: 14
          },
          end: {
            line: 733,
            column: 15
          }
        },
        loc: {
          start: {
            line: 733,
            column: 23
          },
          end: {
            line: 733,
            column: 64
          }
        },
        line: 733
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 738,
            column: 2
          },
          end: {
            line: 738,
            column: 3
          }
        },
        loc: {
          start: {
            line: 738,
            column: 67
          },
          end: {
            line: 751,
            column: 3
          }
        },
        line: 738
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 391,
            column: 38
          },
          end: {
            line: 391,
            column: 113
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 391,
            column: 38
          },
          end: {
            line: 391,
            column: 73
          }
        }, {
          start: {
            line: 391,
            column: 77
          },
          end: {
            line: 391,
            column: 113
          }
        }],
        line: 391
      },
      "1": {
        loc: {
          start: {
            line: 419,
            column: 52
          },
          end: {
            line: 419,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 419,
            column: 52
          },
          end: {
            line: 419,
            column: 94
          }
        }, {
          start: {
            line: 419,
            column: 98
          },
          end: {
            line: 419,
            column: 99
          }
        }],
        line: 419
      },
      "2": {
        loc: {
          start: {
            line: 420,
            column: 46
          },
          end: {
            line: 420,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 420,
            column: 46
          },
          end: {
            line: 420,
            column: 82
          }
        }, {
          start: {
            line: 420,
            column: 86
          },
          end: {
            line: 420,
            column: 87
          }
        }],
        line: 420
      },
      "3": {
        loc: {
          start: {
            line: 421,
            column: 52
          },
          end: {
            line: 421,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 421,
            column: 52
          },
          end: {
            line: 421,
            column: 94
          }
        }, {
          start: {
            line: 421,
            column: 98
          },
          end: {
            line: 421,
            column: 99
          }
        }],
        line: 421
      },
      "4": {
        loc: {
          start: {
            line: 458,
            column: 6
          },
          end: {
            line: 474,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 458,
            column: 6
          },
          end: {
            line: 474,
            column: 7
          }
        }, {
          start: {
            line: 462,
            column: 13
          },
          end: {
            line: 474,
            column: 7
          }
        }],
        line: 458
      },
      "5": {
        loc: {
          start: {
            line: 458,
            column: 10
          },
          end: {
            line: 458,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 458,
            column: 10
          },
          end: {
            line: 458,
            column: 35
          }
        }, {
          start: {
            line: 458,
            column: 39
          },
          end: {
            line: 458,
            column: 66
          }
        }],
        line: 458
      },
      "6": {
        loc: {
          start: {
            line: 462,
            column: 13
          },
          end: {
            line: 474,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 462,
            column: 13
          },
          end: {
            line: 474,
            column: 7
          }
        }, {
          start: {
            line: 466,
            column: 13
          },
          end: {
            line: 474,
            column: 7
          }
        }],
        line: 462
      },
      "7": {
        loc: {
          start: {
            line: 462,
            column: 17
          },
          end: {
            line: 462,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 462,
            column: 17
          },
          end: {
            line: 462,
            column: 42
          }
        }, {
          start: {
            line: 462,
            column: 46
          },
          end: {
            line: 462,
            column: 77
          }
        }],
        line: 462
      },
      "8": {
        loc: {
          start: {
            line: 466,
            column: 13
          },
          end: {
            line: 474,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 466,
            column: 13
          },
          end: {
            line: 474,
            column: 7
          }
        }, {
          start: {
            line: 470,
            column: 13
          },
          end: {
            line: 474,
            column: 7
          }
        }],
        line: 466
      },
      "9": {
        loc: {
          start: {
            line: 531,
            column: 9
          },
          end: {
            line: 531,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 531,
            column: 9
          },
          end: {
            line: 531,
            column: 48
          }
        }, {
          start: {
            line: 531,
            column: 52
          },
          end: {
            line: 531,
            column: 53
          }
        }],
        line: 531
      },
      "10": {
        loc: {
          start: {
            line: 533,
            column: 9
          },
          end: {
            line: 533,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 533,
            column: 9
          },
          end: {
            line: 533,
            column: 42
          }
        }, {
          start: {
            line: 533,
            column: 46
          },
          end: {
            line: 533,
            column: 47
          }
        }],
        line: 533
      },
      "11": {
        loc: {
          start: {
            line: 569,
            column: 22
          },
          end: {
            line: 571,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 569,
            column: 60
          },
          end: {
            line: 569,
            column: 62
          }
        }, {
          start: {
            line: 570,
            column: 21
          },
          end: {
            line: 571,
            column: 67
          }
        }],
        line: 569
      },
      "12": {
        loc: {
          start: {
            line: 570,
            column: 21
          },
          end: {
            line: 571,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 570,
            column: 61
          },
          end: {
            line: 570,
            column: 63
          }
        }, {
          start: {
            line: 571,
            column: 21
          },
          end: {
            line: 571,
            column: 67
          }
        }],
        line: 570
      },
      "13": {
        loc: {
          start: {
            line: 571,
            column: 21
          },
          end: {
            line: 571,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 571,
            column: 60
          },
          end: {
            line: 571,
            column: 62
          }
        }, {
          start: {
            line: 571,
            column: 65
          },
          end: {
            line: 571,
            column: 67
          }
        }],
        line: 571
      },
      "14": {
        loc: {
          start: {
            line: 573,
            column: 21
          },
          end: {
            line: 574,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 573,
            column: 61
          },
          end: {
            line: 573,
            column: 63
          }
        }, {
          start: {
            line: 574,
            column: 20
          },
          end: {
            line: 574,
            column: 67
          }
        }],
        line: 573
      },
      "15": {
        loc: {
          start: {
            line: 574,
            column: 20
          },
          end: {
            line: 574,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 574,
            column: 60
          },
          end: {
            line: 574,
            column: 62
          }
        }, {
          start: {
            line: 574,
            column: 65
          },
          end: {
            line: 574,
            column: 67
          }
        }],
        line: 574
      },
      "16": {
        loc: {
          start: {
            line: 576,
            column: 21
          },
          end: {
            line: 578,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 576,
            column: 59
          },
          end: {
            line: 576,
            column: 61
          }
        }, {
          start: {
            line: 577,
            column: 20
          },
          end: {
            line: 578,
            column: 62
          }
        }],
        line: 576
      },
      "17": {
        loc: {
          start: {
            line: 577,
            column: 20
          },
          end: {
            line: 578,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 577,
            column: 53
          },
          end: {
            line: 577,
            column: 55
          }
        }, {
          start: {
            line: 578,
            column: 20
          },
          end: {
            line: 578,
            column: 62
          }
        }],
        line: 577
      },
      "18": {
        loc: {
          start: {
            line: 578,
            column: 20
          },
          end: {
            line: 578,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 578,
            column: 55
          },
          end: {
            line: 578,
            column: 57
          }
        }, {
          start: {
            line: 578,
            column: 60
          },
          end: {
            line: 578,
            column: 62
          }
        }],
        line: 578
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "4b90fc6cbad411db3012c4da31332a5e046e415d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2ehhv10rb3 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2ehhv10rb3();
var FutureEnhancementsManager = function () {
  function FutureEnhancementsManager() {
    _classCallCheck(this, FutureEnhancementsManager);
    this.enhancements = (cov_2ehhv10rb3().s[0]++, new Map());
    this.technologyTrends = (cov_2ehhv10rb3().s[1]++, new Map());
    this.roadmapPhases = (cov_2ehhv10rb3().s[2]++, new Map());
    this.innovationPipeline = (cov_2ehhv10rb3().s[3]++, []);
    this.FUTURE_ENHANCEMENTS = (cov_2ehhv10rb3().s[4]++, [{
      id: 'quantum_optimization',
      name: 'Quantum Computing Optimization',
      description: 'Leverage quantum computing for complex optimization problems',
      category: 'performance',
      phase: 'research',
      priority: 'medium',
      complexity: 'revolutionary',
      dependencies: [],
      prerequisites: {
        technology: ['quantum_computing', 'quantum_algorithms'],
        infrastructure: ['quantum_cloud_access'],
        expertise: ['quantum_programming', 'optimization_theory']
      },
      impact: {
        performance: 95,
        userExperience: 70,
        technicalDebt: -20,
        marketAdvantage: 90
      },
      resources: {
        developmentTime: 2000,
        teamSize: 3,
        budget: 500000,
        infrastructure: ['quantum_simulator', 'cloud_quantum_access']
      }
    }, {
      id: 'neural_processing_units',
      name: 'Neural Processing Unit Integration',
      description: 'Integrate dedicated NPU chips for AI acceleration',
      category: 'ai',
      phase: 'design',
      priority: 'high',
      complexity: 'complex',
      dependencies: ['phase3c_native'],
      prerequisites: {
        technology: ['npu_apis', 'neural_frameworks'],
        infrastructure: ['npu_devices'],
        expertise: ['neural_optimization', 'hardware_integration']
      },
      impact: {
        performance: 85,
        userExperience: 80,
        technicalDebt: -10,
        marketAdvantage: 75
      },
      resources: {
        developmentTime: 800,
        teamSize: 4,
        budget: 200000,
        infrastructure: ['npu_dev_kits', 'testing_devices']
      }
    }, {
      id: 'real_time_ray_tracing',
      name: 'Real-time Ray Tracing Graphics',
      description: 'Implement real-time ray tracing for 3D tennis visualization',
      category: 'hardware',
      phase: 'research',
      priority: 'medium',
      complexity: 'complex',
      dependencies: ['phase3c_gpu'],
      prerequisites: {
        technology: ['ray_tracing_apis', 'gpu_compute'],
        infrastructure: ['rt_capable_gpus'],
        expertise: ['graphics_programming', 'ray_tracing']
      },
      impact: {
        performance: 60,
        userExperience: 95,
        technicalDebt: 10,
        marketAdvantage: 85
      },
      resources: {
        developmentTime: 1200,
        teamSize: 5,
        budget: 300000,
        infrastructure: ['rt_gpus', 'graphics_workstations']
      }
    }, {
      id: 'edge_ai_inference',
      name: 'Edge AI Inference Optimization',
      description: 'Deploy AI models directly to edge locations for ultra-low latency',
      category: 'ai',
      phase: 'development',
      priority: 'high',
      complexity: 'complex',
      dependencies: ['phase3b_edge', 'phase3a_ai'],
      prerequisites: {
        technology: ['edge_ai_frameworks', 'model_optimization'],
        infrastructure: ['edge_compute_nodes'],
        expertise: ['edge_computing', 'ai_optimization']
      },
      impact: {
        performance: 80,
        userExperience: 85,
        technicalDebt: -5,
        marketAdvantage: 80
      },
      resources: {
        developmentTime: 600,
        teamSize: 3,
        budget: 150000,
        infrastructure: ['edge_servers', 'ai_accelerators']
      }
    }, {
      id: 'blockchain_performance_tracking',
      name: 'Blockchain Performance Analytics',
      description: 'Immutable performance tracking and verification system',
      category: 'analytics',
      phase: 'research',
      priority: 'low',
      complexity: 'complex',
      dependencies: [],
      prerequisites: {
        technology: ['blockchain_platforms', 'smart_contracts'],
        infrastructure: ['blockchain_nodes'],
        expertise: ['blockchain_development', 'cryptography']
      },
      impact: {
        performance: 20,
        userExperience: 40,
        technicalDebt: 15,
        marketAdvantage: 60
      },
      resources: {
        developmentTime: 400,
        teamSize: 2,
        budget: 100000,
        infrastructure: ['blockchain_testnet']
      }
    }, {
      id: 'augmented_reality_coaching',
      name: 'AR-Powered Tennis Coaching',
      description: 'Real-time AR overlays for tennis technique improvement',
      category: 'ui',
      phase: 'design',
      priority: 'high',
      complexity: 'complex',
      dependencies: ['phase3c_gpu', 'phase3a_ai'],
      prerequisites: {
        technology: ['ar_frameworks', 'computer_vision'],
        infrastructure: ['ar_devices'],
        expertise: ['ar_development', 'sports_analytics']
      },
      impact: {
        performance: 70,
        userExperience: 95,
        technicalDebt: 20,
        marketAdvantage: 90
      },
      resources: {
        developmentTime: 1000,
        teamSize: 6,
        budget: 400000,
        infrastructure: ['ar_headsets', 'motion_capture']
      }
    }, {
      id: 'predictive_maintenance',
      name: 'Predictive System Maintenance',
      description: 'AI-powered predictive maintenance for all optimization systems',
      category: 'ai',
      phase: 'development',
      priority: 'medium',
      complexity: 'moderate',
      dependencies: ['phase3a_ai'],
      prerequisites: {
        technology: ['predictive_analytics', 'anomaly_detection'],
        infrastructure: ['monitoring_systems'],
        expertise: ['machine_learning', 'system_monitoring']
      },
      impact: {
        performance: 75,
        userExperience: 60,
        technicalDebt: -30,
        marketAdvantage: 50
      },
      resources: {
        developmentTime: 300,
        teamSize: 2,
        budget: 75000,
        infrastructure: ['monitoring_tools']
      }
    }, {
      id: 'zero_latency_networking',
      name: 'Zero-Latency Networking',
      description: 'Ultra-low latency networking with 5G and edge computing',
      category: 'network',
      phase: 'research',
      priority: 'high',
      complexity: 'complex',
      dependencies: ['phase3b_edge'],
      prerequisites: {
        technology: ['5g_networks', 'edge_computing'],
        infrastructure: ['5g_infrastructure'],
        expertise: ['network_optimization', 'edge_computing']
      },
      impact: {
        performance: 90,
        userExperience: 85,
        technicalDebt: -10,
        marketAdvantage: 85
      },
      resources: {
        developmentTime: 800,
        teamSize: 4,
        budget: 250000,
        infrastructure: ['5g_testbeds', 'edge_nodes']
      }
    }]);
    cov_2ehhv10rb3().f[0]++;
    cov_2ehhv10rb3().s[5]++;
    this.initializeFutureEnhancements();
  }
  return _createClass(FutureEnhancementsManager, [{
    key: "initializeFutureEnhancements",
    value: (function () {
      var _initializeFutureEnhancements = _asyncToGenerator(function* () {
        cov_2ehhv10rb3().f[1]++;
        cov_2ehhv10rb3().s[6]++;
        try {
          cov_2ehhv10rb3().s[7]++;
          this.loadFutureEnhancements();
          cov_2ehhv10rb3().s[8]++;
          this.initializeTechnologyTrends();
          cov_2ehhv10rb3().s[9]++;
          this.createRoadmapPhases();
          cov_2ehhv10rb3().s[10]++;
          this.buildInnovationPipeline();
          cov_2ehhv10rb3().s[11]++;
          console.log('Future Enhancements Manager initialized successfully');
        } catch (error) {
          cov_2ehhv10rb3().s[12]++;
          console.error('Failed to initialize Future Enhancements Manager:', error);
        }
      });
      function initializeFutureEnhancements() {
        return _initializeFutureEnhancements.apply(this, arguments);
      }
      return initializeFutureEnhancements;
    }())
  }, {
    key: "getStrategicRoadmap",
    value: function getStrategicRoadmap() {
      cov_2ehhv10rb3().f[2]++;
      var phases = (cov_2ehhv10rb3().s[13]++, Array.from(this.roadmapPhases.values()));
      var timeline = (cov_2ehhv10rb3().s[14]++, []);
      cov_2ehhv10rb3().s[15]++;
      phases.forEach(function (phase) {
        cov_2ehhv10rb3().f[3]++;
        cov_2ehhv10rb3().s[16]++;
        phase.milestones.forEach(function (milestone) {
          cov_2ehhv10rb3().f[4]++;
          cov_2ehhv10rb3().s[17]++;
          timeline.push({
            date: milestone.date,
            milestone: milestone.name,
            phase: phase.name
          });
        });
      });
      cov_2ehhv10rb3().s[18]++;
      timeline.sort(function (a, b) {
        cov_2ehhv10rb3().f[5]++;
        cov_2ehhv10rb3().s[19]++;
        return a.date - b.date;
      });
      var totalInvestment = (cov_2ehhv10rb3().s[20]++, phases.reduce(function (sum, phase) {
        cov_2ehhv10rb3().f[6]++;
        cov_2ehhv10rb3().s[21]++;
        return sum + phase.resources.budget;
      }, 0));
      var expectedROI = (cov_2ehhv10rb3().s[22]++, 250);
      var allRisks = (cov_2ehhv10rb3().s[23]++, phases.flatMap(function (phase) {
        cov_2ehhv10rb3().f[7]++;
        cov_2ehhv10rb3().s[24]++;
        return phase.risks;
      }));
      var riskAssessment = (cov_2ehhv10rb3().s[25]++, {
        high: allRisks.filter(function (risk) {
          cov_2ehhv10rb3().f[8]++;
          cov_2ehhv10rb3().s[26]++;
          return risk.probability * risk.impact > 70;
        }).length,
        medium: allRisks.filter(function (risk) {
          cov_2ehhv10rb3().f[9]++;
          cov_2ehhv10rb3().s[27]++;
          return (cov_2ehhv10rb3().b[0][0]++, risk.probability * risk.impact > 30) && (cov_2ehhv10rb3().b[0][1]++, risk.probability * risk.impact <= 70);
        }).length,
        low: allRisks.filter(function (risk) {
          cov_2ehhv10rb3().f[10]++;
          cov_2ehhv10rb3().s[28]++;
          return risk.probability * risk.impact <= 30;
        }).length
      });
      cov_2ehhv10rb3().s[29]++;
      return {
        phases: phases,
        timeline: timeline,
        totalInvestment: totalInvestment,
        expectedROI: expectedROI,
        riskAssessment: riskAssessment
      };
    }
  }, {
    key: "getInnovationPipeline",
    value: function getInnovationPipeline() {
      cov_2ehhv10rb3().f[11]++;
      var priorityDistribution = (cov_2ehhv10rb3().s[30]++, {});
      var phaseDistribution = (cov_2ehhv10rb3().s[31]++, {});
      var categoryDistribution = (cov_2ehhv10rb3().s[32]++, {});
      cov_2ehhv10rb3().s[33]++;
      this.innovationPipeline.forEach(function (enhancement) {
        cov_2ehhv10rb3().f[12]++;
        cov_2ehhv10rb3().s[34]++;
        priorityDistribution[enhancement.priority] = ((cov_2ehhv10rb3().b[1][0]++, priorityDistribution[enhancement.priority]) || (cov_2ehhv10rb3().b[1][1]++, 0)) + 1;
        cov_2ehhv10rb3().s[35]++;
        phaseDistribution[enhancement.phase] = ((cov_2ehhv10rb3().b[2][0]++, phaseDistribution[enhancement.phase]) || (cov_2ehhv10rb3().b[2][1]++, 0)) + 1;
        cov_2ehhv10rb3().s[36]++;
        categoryDistribution[enhancement.category] = ((cov_2ehhv10rb3().b[3][0]++, categoryDistribution[enhancement.category]) || (cov_2ehhv10rb3().b[3][1]++, 0)) + 1;
      });
      var readinessScore = (cov_2ehhv10rb3().s[37]++, this.calculateInnovationReadiness());
      cov_2ehhv10rb3().s[38]++;
      return {
        pipeline: this.innovationPipeline,
        priorityDistribution: priorityDistribution,
        phaseDistribution: phaseDistribution,
        categoryDistribution: categoryDistribution,
        readinessScore: readinessScore
      };
    }
  }, {
    key: "getTechnologyTrendAnalysis",
    value: function getTechnologyTrendAnalysis() {
      cov_2ehhv10rb3().f[13]++;
      var trends = (cov_2ehhv10rb3().s[39]++, Array.from(this.technologyTrends.values()));
      var emergingTechnologies = (cov_2ehhv10rb3().s[40]++, trends.filter(function (trend) {
        cov_2ehhv10rb3().f[14]++;
        cov_2ehhv10rb3().s[41]++;
        return trend.maturity === 'emerging';
      }));
      var adoptionRecommendations = (cov_2ehhv10rb3().s[42]++, trends.map(function (trend) {
        cov_2ehhv10rb3().f[15]++;
        var recommendation;
        var reasoning;
        var timeline;
        cov_2ehhv10rb3().s[43]++;
        if ((cov_2ehhv10rb3().b[5][0]++, trend.relevanceScore > 80) && (cov_2ehhv10rb3().b[5][1]++, trend.maturity === 'mature')) {
          cov_2ehhv10rb3().b[4][0]++;
          cov_2ehhv10rb3().s[44]++;
          recommendation = 'adopt';
          cov_2ehhv10rb3().s[45]++;
          reasoning = 'High relevance and mature technology';
          cov_2ehhv10rb3().s[46]++;
          timeline = 'Immediate';
        } else {
          cov_2ehhv10rb3().b[4][1]++;
          cov_2ehhv10rb3().s[47]++;
          if ((cov_2ehhv10rb3().b[7][0]++, trend.relevanceScore > 60) && (cov_2ehhv10rb3().b[7][1]++, trend.maturity === 'developing')) {
            cov_2ehhv10rb3().b[6][0]++;
            cov_2ehhv10rb3().s[48]++;
            recommendation = 'trial';
            cov_2ehhv10rb3().s[49]++;
            reasoning = 'Good potential with developing maturity';
            cov_2ehhv10rb3().s[50]++;
            timeline = '3-6 months';
          } else {
            cov_2ehhv10rb3().b[6][1]++;
            cov_2ehhv10rb3().s[51]++;
            if (trend.relevanceScore > 40) {
              cov_2ehhv10rb3().b[8][0]++;
              cov_2ehhv10rb3().s[52]++;
              recommendation = 'assess';
              cov_2ehhv10rb3().s[53]++;
              reasoning = 'Moderate potential, needs evaluation';
              cov_2ehhv10rb3().s[54]++;
              timeline = '6-12 months';
            } else {
              cov_2ehhv10rb3().b[8][1]++;
              cov_2ehhv10rb3().s[55]++;
              recommendation = 'hold';
              cov_2ehhv10rb3().s[56]++;
              reasoning = 'Low relevance or high risk';
              cov_2ehhv10rb3().s[57]++;
              timeline = '12+ months';
            }
          }
        }
        cov_2ehhv10rb3().s[58]++;
        return {
          technology: trend.name,
          recommendation: recommendation,
          reasoning: reasoning,
          timeline: timeline
        };
      }));
      var competitiveAdvantage = (cov_2ehhv10rb3().s[59]++, this.calculateCompetitiveAdvantage());
      cov_2ehhv10rb3().s[60]++;
      return {
        trends: trends,
        emergingTechnologies: emergingTechnologies,
        adoptionRecommendations: adoptionRecommendations,
        competitiveAdvantage: competitiveAdvantage
      };
    }
  }, {
    key: "prioritizeEnhancements",
    value: function prioritizeEnhancements() {
      var _this = this;
      cov_2ehhv10rb3().f[16]++;
      cov_2ehhv10rb3().s[61]++;
      return this.innovationPipeline.sort(function (a, b) {
        cov_2ehhv10rb3().f[17]++;
        var scoreA = (cov_2ehhv10rb3().s[62]++, _this.calculatePriorityScore(a));
        var scoreB = (cov_2ehhv10rb3().s[63]++, _this.calculatePriorityScore(b));
        cov_2ehhv10rb3().s[64]++;
        return scoreB - scoreA;
      });
    }
  }, {
    key: "getInvestmentAnalysis",
    value: function getInvestmentAnalysis() {
      cov_2ehhv10rb3().f[18]++;
      var totalInvestment = (cov_2ehhv10rb3().s[65]++, this.innovationPipeline.reduce(function (sum, enhancement) {
        cov_2ehhv10rb3().f[19]++;
        cov_2ehhv10rb3().s[66]++;
        return sum + enhancement.resources.budget;
      }, 0));
      var categoryBreakdown = (cov_2ehhv10rb3().s[67]++, {});
      var phaseBreakdown = (cov_2ehhv10rb3().s[68]++, {});
      cov_2ehhv10rb3().s[69]++;
      this.innovationPipeline.forEach(function (enhancement) {
        cov_2ehhv10rb3().f[20]++;
        cov_2ehhv10rb3().s[70]++;
        categoryBreakdown[enhancement.category] = ((cov_2ehhv10rb3().b[9][0]++, categoryBreakdown[enhancement.category]) || (cov_2ehhv10rb3().b[9][1]++, 0)) + enhancement.resources.budget;
        cov_2ehhv10rb3().s[71]++;
        phaseBreakdown[enhancement.phase] = ((cov_2ehhv10rb3().b[10][0]++, phaseBreakdown[enhancement.phase]) || (cov_2ehhv10rb3().b[10][1]++, 0)) + enhancement.resources.budget;
      });
      cov_2ehhv10rb3().s[72]++;
      return {
        totalInvestment: totalInvestment,
        categoryBreakdown: categoryBreakdown,
        phaseBreakdown: phaseBreakdown,
        roi: {
          shortTerm: 25,
          mediumTerm: 150,
          longTerm: 300
        },
        paybackPeriod: 18,
        riskAdjustedReturn: 180
      };
    }
  }, {
    key: "loadFutureEnhancements",
    value: function loadFutureEnhancements() {
      var _this2 = this;
      cov_2ehhv10rb3().f[21]++;
      cov_2ehhv10rb3().s[73]++;
      this.FUTURE_ENHANCEMENTS.forEach(function (enhancement) {
        cov_2ehhv10rb3().f[22]++;
        var fullEnhancement = (cov_2ehhv10rb3().s[74]++, Object.assign({}, enhancement, {
          timeline: {
            estimatedStart: Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000,
            estimatedCompletion: Date.now() + (Math.random() * 2 + 1) * 365 * 24 * 60 * 60 * 1000
          },
          feasibility: _this2.calculateFeasibility(enhancement)
        }));
        cov_2ehhv10rb3().s[75]++;
        _this2.enhancements.set(enhancement.id, fullEnhancement);
      });
    }
  }, {
    key: "calculateFeasibility",
    value: function calculateFeasibility(enhancement) {
      cov_2ehhv10rb3().f[23]++;
      var technical = (cov_2ehhv10rb3().s[76]++, enhancement.complexity === 'simple' ? (cov_2ehhv10rb3().b[11][0]++, 90) : (cov_2ehhv10rb3().b[11][1]++, enhancement.complexity === 'moderate' ? (cov_2ehhv10rb3().b[12][0]++, 70) : (cov_2ehhv10rb3().b[12][1]++, enhancement.complexity === 'complex' ? (cov_2ehhv10rb3().b[13][0]++, 50) : (cov_2ehhv10rb3().b[13][1]++, 30))));
      var resource = (cov_2ehhv10rb3().s[77]++, enhancement.resources.budget < 100000 ? (cov_2ehhv10rb3().b[14][0]++, 90) : (cov_2ehhv10rb3().b[14][1]++, enhancement.resources.budget < 300000 ? (cov_2ehhv10rb3().b[15][0]++, 70) : (cov_2ehhv10rb3().b[15][1]++, 50)));
      var timeline = (cov_2ehhv10rb3().s[78]++, enhancement.phase === 'development' ? (cov_2ehhv10rb3().b[16][0]++, 80) : (cov_2ehhv10rb3().b[16][1]++, enhancement.phase === 'design' ? (cov_2ehhv10rb3().b[17][0]++, 60) : (cov_2ehhv10rb3().b[17][1]++, enhancement.phase === 'research' ? (cov_2ehhv10rb3().b[18][0]++, 40) : (cov_2ehhv10rb3().b[18][1]++, 20))));
      var overall = (cov_2ehhv10rb3().s[79]++, (technical + resource + timeline) / 3);
      cov_2ehhv10rb3().s[80]++;
      return {
        technical: technical,
        resource: resource,
        timeline: timeline,
        overall: overall
      };
    }
  }, {
    key: "initializeTechnologyTrends",
    value: function initializeTechnologyTrends() {
      var _this3 = this;
      cov_2ehhv10rb3().f[24]++;
      var trends = (cov_2ehhv10rb3().s[81]++, [{
        id: 'quantum_computing',
        name: 'Quantum Computing',
        description: 'Quantum computers for complex optimization',
        category: 'computing',
        maturity: 'emerging',
        adoptionRate: 5,
        relevanceScore: 70,
        timeToMainstream: 60,
        risks: ['Technical complexity', 'Limited availability'],
        opportunities: ['Revolutionary performance gains', 'Competitive advantage']
      }, {
        id: 'neural_processing',
        name: 'Neural Processing Units',
        description: 'Dedicated AI acceleration chips',
        category: 'hardware',
        maturity: 'developing',
        adoptionRate: 25,
        relevanceScore: 85,
        timeToMainstream: 24,
        risks: ['Hardware dependency', 'Cost'],
        opportunities: ['AI acceleration', 'Energy efficiency']
      }, {
        id: 'edge_ai',
        name: 'Edge AI Computing',
        description: 'AI processing at network edge',
        category: 'ai',
        maturity: 'developing',
        adoptionRate: 40,
        relevanceScore: 90,
        timeToMainstream: 18,
        risks: ['Infrastructure requirements', 'Complexity'],
        opportunities: ['Ultra-low latency', 'Privacy']
      }]);
      cov_2ehhv10rb3().s[82]++;
      trends.forEach(function (trend) {
        cov_2ehhv10rb3().f[25]++;
        var fullTrend = (cov_2ehhv10rb3().s[83]++, Object.assign({}, trend, {
          potentialImpact: {
            performance: 70 + Math.random() * 30,
            cost: 50 + Math.random() * 50,
            complexity: 30 + Math.random() * 40,
            marketPosition: 60 + Math.random() * 40
          }
        }));
        cov_2ehhv10rb3().s[84]++;
        _this3.technologyTrends.set(trend.id, fullTrend);
      });
    }
  }, {
    key: "createRoadmapPhases",
    value: function createRoadmapPhases() {
      var _this4 = this;
      cov_2ehhv10rb3().f[26]++;
      var phases = (cov_2ehhv10rb3().s[85]++, [{
        id: 'phase_4a',
        name: 'Phase 4A: Next-Gen AI Integration',
        description: 'Advanced AI and neural processing integration',
        startDate: Date.now() + 90 * 24 * 60 * 60 * 1000,
        endDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        objectives: ['Integrate Neural Processing Units', 'Deploy Edge AI Inference', 'Implement Predictive Maintenance'],
        enhancements: ['neural_processing_units', 'edge_ai_inference', 'predictive_maintenance'],
        milestones: [{
          id: 'npu_integration',
          name: 'NPU Integration Complete',
          date: Date.now() + 180 * 24 * 60 * 60 * 1000,
          criteria: ['NPU APIs integrated', 'Performance benchmarks met'],
          status: 'pending'
        }],
        resources: {
          budget: 425000,
          team: ['ai_engineers', 'hardware_specialists', 'performance_engineers'],
          infrastructure: ['npu_devices', 'edge_servers', 'monitoring_tools']
        },
        risks: [{
          description: 'NPU hardware availability',
          probability: 30,
          impact: 70,
          mitigation: 'Multiple vendor partnerships'
        }]
      }, {
        id: 'phase_4b',
        name: 'Phase 4B: Immersive Technologies',
        description: 'AR/VR and advanced visualization',
        startDate: Date.now() + 365 * 24 * 60 * 60 * 1000,
        endDate: Date.now() + 2 * 365 * 24 * 60 * 60 * 1000,
        objectives: ['Implement AR Coaching', 'Deploy Real-time Ray Tracing', 'Create Immersive Training'],
        enhancements: ['augmented_reality_coaching', 'real_time_ray_tracing'],
        milestones: [{
          id: 'ar_prototype',
          name: 'AR Coaching Prototype',
          date: Date.now() + 450 * 24 * 60 * 60 * 1000,
          criteria: ['AR prototype functional', 'User testing complete'],
          status: 'pending'
        }],
        resources: {
          budget: 700000,
          team: ['ar_developers', 'graphics_engineers', 'ux_designers'],
          infrastructure: ['ar_devices', 'graphics_workstations', 'motion_capture']
        },
        risks: [{
          description: 'AR technology maturity',
          probability: 40,
          impact: 60,
          mitigation: 'Phased implementation approach'
        }]
      }]);
      cov_2ehhv10rb3().s[86]++;
      phases.forEach(function (phase) {
        cov_2ehhv10rb3().f[27]++;
        cov_2ehhv10rb3().s[87]++;
        _this4.roadmapPhases.set(phase.id, phase);
      });
    }
  }, {
    key: "buildInnovationPipeline",
    value: function buildInnovationPipeline() {
      cov_2ehhv10rb3().f[28]++;
      cov_2ehhv10rb3().s[88]++;
      this.innovationPipeline = Array.from(this.enhancements.values());
    }
  }, {
    key: "calculateInnovationReadiness",
    value: function calculateInnovationReadiness() {
      cov_2ehhv10rb3().f[29]++;
      var readyEnhancements = (cov_2ehhv10rb3().s[89]++, this.innovationPipeline.filter(function (enhancement) {
        cov_2ehhv10rb3().f[30]++;
        cov_2ehhv10rb3().s[90]++;
        return enhancement.feasibility.overall > 70;
      }));
      cov_2ehhv10rb3().s[91]++;
      return readyEnhancements.length / this.innovationPipeline.length * 100;
    }
  }, {
    key: "calculateCompetitiveAdvantage",
    value: function calculateCompetitiveAdvantage() {
      cov_2ehhv10rb3().f[31]++;
      var highImpactTrends = (cov_2ehhv10rb3().s[92]++, Array.from(this.technologyTrends.values()).filter(function (trend) {
        cov_2ehhv10rb3().f[32]++;
        cov_2ehhv10rb3().s[93]++;
        return trend.potentialImpact.marketPosition > 70;
      }));
      cov_2ehhv10rb3().s[94]++;
      return highImpactTrends.length / this.technologyTrends.size * 100;
    }
  }, {
    key: "calculatePriorityScore",
    value: function calculatePriorityScore(enhancement) {
      cov_2ehhv10rb3().f[33]++;
      var priorityWeights = (cov_2ehhv10rb3().s[95]++, {
        critical: 4,
        high: 3,
        medium: 2,
        low: 1
      });
      var priorityScore = (cov_2ehhv10rb3().s[96]++, priorityWeights[enhancement.priority] * 25);
      var impactScore = (cov_2ehhv10rb3().s[97]++, (enhancement.impact.performance + enhancement.impact.userExperience + enhancement.impact.marketAdvantage) / 3);
      var feasibilityScore = (cov_2ehhv10rb3().s[98]++, enhancement.feasibility.overall);
      cov_2ehhv10rb3().s[99]++;
      return (priorityScore + impactScore + feasibilityScore) / 3;
    }
  }]);
}();
export var futureEnhancementsManager = (cov_2ehhv10rb3().s[100]++, new FutureEnhancementsManager());
export default futureEnhancementsManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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