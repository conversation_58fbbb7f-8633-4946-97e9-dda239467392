74472cd34a397dd2751be682c7b5ba36
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_256kqr0gyr() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\components\\ai\\PoseVisualization.tsx";
  var hash = "71bf431dd8172e84ef6a8daf7de75cf7c91b867f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\components\\ai\\PoseVisualization.tsx",
    statementMap: {
      "0": {
        start: {
          line: 28,
          column: 26
        },
        end: {
          line: 28,
          column: 50
        }
      },
      "1": {
        start: {
          line: 30,
          column: 15
        },
        end: {
          line: 42,
          column: 1
        }
      },
      "2": {
        start: {
          line: 45,
          column: 25
        },
        end: {
          line: 68,
          column: 1
        }
      },
      "3": {
        start: {
          line: 93,
          column: 48
        },
        end: {
          line: 93,
          column: 75
        }
      },
      "4": {
        start: {
          line: 94,
          column: 46
        },
        end: {
          line: 94,
          column: 65
        }
      },
      "5": {
        start: {
          line: 95,
          column: 23
        },
        end: {
          line: 95,
          column: 39
        }
      },
      "6": {
        start: {
          line: 96,
          column: 25
        },
        end: {
          line: 96,
          column: 43
        }
      },
      "7": {
        start: {
          line: 98,
          column: 19
        },
        end: {
          line: 98,
          column: 29
        }
      },
      "8": {
        start: {
          line: 99,
          column: 20
        },
        end: {
          line: 99,
          column: 35
        }
      },
      "9": {
        start: {
          line: 101,
          column: 2
        },
        end: {
          line: 103,
          column: 26
        }
      },
      "10": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 102,
          column: 42
        }
      },
      "11": {
        start: {
          line: 105,
          column: 2
        },
        end: {
          line: 107,
          column: 18
        }
      },
      "12": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 106,
          column: 33
        }
      },
      "13": {
        start: {
          line: 109,
          column: 2
        },
        end: {
          line: 140,
          column: 78
        }
      },
      "14": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 133,
          column: 5
        }
      },
      "15": {
        start: {
          line: 111,
          column: 22
        },
        end: {
          line: 126,
          column: 7
        }
      },
      "16": {
        start: {
          line: 112,
          column: 20
        },
        end: {
          line: 112,
          column: 30
        }
      },
      "17": {
        start: {
          line: 113,
          column: 26
        },
        end: {
          line: 113,
          column: 54
        }
      },
      "18": {
        start: {
          line: 114,
          column: 30
        },
        end: {
          line: 114,
          column: 57
        }
      },
      "19": {
        start: {
          line: 116,
          column: 8
        },
        end: {
          line: 123,
          column: 9
        }
      },
      "20": {
        start: {
          line: 117,
          column: 10
        },
        end: {
          line: 121,
          column: 13
        }
      },
      "21": {
        start: {
          line: 118,
          column: 30
        },
        end: {
          line: 118,
          column: 66
        }
      },
      "22": {
        start: {
          line: 119,
          column: 12
        },
        end: {
          line: 119,
          column: 39
        }
      },
      "23": {
        start: {
          line: 120,
          column: 12
        },
        end: {
          line: 120,
          column: 29
        }
      },
      "24": {
        start: {
          line: 122,
          column: 10
        },
        end: {
          line: 122,
          column: 39
        }
      },
      "25": {
        start: {
          line: 125,
          column: 8
        },
        end: {
          line: 125,
          column: 62
        }
      },
      "26": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 60
        }
      },
      "27": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 132,
          column: 7
        }
      },
      "28": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 51
        }
      },
      "29": {
        start: {
          line: 135,
          column: 4
        },
        end: {
          line: 139,
          column: 6
        }
      },
      "30": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 138,
          column: 7
        }
      },
      "31": {
        start: {
          line: 137,
          column: 8
        },
        end: {
          line: 137,
          column: 51
        }
      },
      "32": {
        start: {
          line: 142,
          column: 26
        },
        end: {
          line: 142,
          column: 59
        }
      },
      "33": {
        start: {
          line: 143,
          column: 2
        },
        end: {
          line: 143,
          column: 36
        }
      },
      "34": {
        start: {
          line: 143,
          column: 24
        },
        end: {
          line: 143,
          column: 36
        }
      },
      "35": {
        start: {
          line: 145,
          column: 25
        },
        end: {
          line: 149,
          column: 3
        }
      },
      "36": {
        start: {
          line: 146,
          column: 25
        },
        end: {
          line: 146,
          column: 40
        }
      },
      "37": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 147,
          column: 36
        }
      },
      "38": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 148,
          column: 38
        }
      },
      "39": {
        start: {
          line: 151,
          column: 24
        },
        end: {
          line: 156,
          column: 3
        }
      },
      "40": {
        start: {
          line: 152,
          column: 4
        },
        end: {
          line: 152,
          column: 26
        }
      },
      "41": {
        start: {
          line: 153,
          column: 4
        },
        end: {
          line: 153,
          column: 29
        }
      },
      "42": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 154,
          column: 23
        }
      },
      "43": {
        start: {
          line: 155,
          column: 4
        },
        end: {
          line: 155,
          column: 31
        }
      },
      "44": {
        start: {
          line: 158,
          column: 30
        },
        end: {
          line: 183,
          column: 3
        }
      },
      "45": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 182,
          column: 7
        }
      },
      "46": {
        start: {
          line: 160,
          column: 16
        },
        end: {
          line: 160,
          column: 37
        }
      },
      "47": {
        start: {
          line: 161,
          column: 16
        },
        end: {
          line: 161,
          column: 38
        }
      },
      "48": {
        start: {
          line: 162,
          column: 25
        },
        end: {
          line: 162,
          column: 44
        }
      },
      "49": {
        start: {
          line: 165,
          column: 26
        },
        end: {
          line: 165,
          column: 40
        }
      },
      "50": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 170,
          column: 7
        }
      },
      "51": {
        start: {
          line: 167,
          column: 8
        },
        end: {
          line: 167,
          column: 36
        }
      },
      "52": {
        start: {
          line: 168,
          column: 13
        },
        end: {
          line: 170,
          column: 7
        }
      },
      "53": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 35
        }
      },
      "54": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 181,
          column: 8
        }
      },
      "55": {
        start: {
          line: 185,
          column: 32
        },
        end: {
          line: 219,
          column: 3
        }
      },
      "56": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 218,
          column: 7
        }
      },
      "57": {
        start: {
          line: 187,
          column: 33
        },
        end: {
          line: 187,
          column: 43
        }
      },
      "58": {
        start: {
          line: 188,
          column: 28
        },
        end: {
          line: 188,
          column: 47
        }
      },
      "59": {
        start: {
          line: 189,
          column: 26
        },
        end: {
          line: 189,
          column: 43
        }
      },
      "60": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 191,
          column: 54
        }
      },
      "61": {
        start: {
          line: 191,
          column: 42
        },
        end: {
          line: 191,
          column: 54
        }
      },
      "62": {
        start: {
          line: 193,
          column: 17
        },
        end: {
          line: 193,
          column: 43
        }
      },
      "63": {
        start: {
          line: 194,
          column: 17
        },
        end: {
          line: 194,
          column: 44
        }
      },
      "64": {
        start: {
          line: 195,
          column: 17
        },
        end: {
          line: 195,
          column: 41
        }
      },
      "65": {
        start: {
          line: 196,
          column: 17
        },
        end: {
          line: 196,
          column: 42
        }
      },
      "66": {
        start: {
          line: 198,
          column: 28
        },
        end: {
          line: 198,
          column: 83
        }
      },
      "67": {
        start: {
          line: 201,
          column: 28
        },
        end: {
          line: 201,
          column: 39
        }
      },
      "68": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 204,
          column: 7
        }
      },
      "69": {
        start: {
          line: 203,
          column: 8
        },
        end: {
          line: 203,
          column: 37
        }
      },
      "70": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 217,
          column: 8
        }
      },
      "71": {
        start: {
          line: 221,
          column: 23
        },
        end: {
          line: 276,
          column: 3
        }
      },
      "72": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 222,
          column: 33
        }
      },
      "73": {
        start: {
          line: 222,
          column: 21
        },
        end: {
          line: 222,
          column: 33
        }
      },
      "74": {
        start: {
          line: 224,
          column: 19
        },
        end: {
          line: 224,
          column: 44
        }
      },
      "75": {
        start: {
          line: 225,
          column: 27
        },
        end: {
          line: 246,
          column: 5
        }
      },
      "76": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 275,
          column: 7
        }
      },
      "77": {
        start: {
          line: 249,
          column: 6
        },
        end: {
          line: 249,
          column: 43
        }
      },
      "78": {
        start: {
          line: 249,
          column: 31
        },
        end: {
          line: 249,
          column: 43
        }
      },
      "79": {
        start: {
          line: 251,
          column: 16
        },
        end: {
          line: 251,
          column: 52
        }
      },
      "80": {
        start: {
          line: 252,
          column: 16
        },
        end: {
          line: 252,
          column: 53
        }
      },
      "81": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 274,
          column: 8
        }
      },
      "82": {
        start: {
          line: 278,
          column: 36
        },
        end: {
          line: 302,
          column: 3
        }
      },
      "83": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 279,
          column: 37
        }
      },
      "84": {
        start: {
          line: 279,
          column: 25
        },
        end: {
          line: 279,
          column: 37
        }
      },
      "85": {
        start: {
          line: 281,
          column: 23
        },
        end: {
          line: 281,
          column: 49
        }
      },
      "86": {
        start: {
          line: 282,
          column: 28
        },
        end: {
          line: 283,
          column: 72
        }
      },
      "87": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 301,
          column: 6
        }
      },
      "88": {
        start: {
          line: 304,
          column: 29
        },
        end: {
          line: 318,
          column: 3
        }
      },
      "89": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 317,
          column: 11
        }
      },
      "90": {
        start: {
          line: 320,
          column: 25
        },
        end: {
          line: 338,
          column: 3
        }
      },
      "91": {
        start: {
          line: 321,
          column: 4
        },
        end: {
          line: 337,
          column: 11
        }
      },
      "92": {
        start: {
          line: 341,
          column: 40
        },
        end: {
          line: 346,
          column: 5
        }
      },
      "93": {
        start: {
          line: 341,
          column: 78
        },
        end: {
          line: 346,
          column: 3
        }
      },
      "94": {
        start: {
          line: 348,
          column: 2
        },
        end: {
          line: 388,
          column: 4
        }
      },
      "95": {
        start: {
          line: 392,
          column: 30
        },
        end: {
          line: 403,
          column: 1
        }
      },
      "96": {
        start: {
          line: 394,
          column: 2
        },
        end: {
          line: 396,
          column: 3
        }
      },
      "97": {
        start: {
          line: 395,
          column: 4
        },
        end: {
          line: 395,
          column: 106
        }
      },
      "98": {
        start: {
          line: 398,
          column: 2
        },
        end: {
          line: 400,
          column: 3
        }
      },
      "99": {
        start: {
          line: 399,
          column: 4
        },
        end: {
          line: 399,
          column: 105
        }
      },
      "100": {
        start: {
          line: 402,
          column: 2
        },
        end: {
          line: 402,
          column: 15
        }
      },
      "101": {
        start: {
          line: 405,
          column: 32
        },
        end: {
          line: 420,
          column: 1
        }
      },
      "102": {
        start: {
          line: 407,
          column: 23
        },
        end: {
          line: 407,
          column: 33
        }
      },
      "103": {
        start: {
          line: 410,
          column: 2
        },
        end: {
          line: 417,
          column: 3
        }
      },
      "104": {
        start: {
          line: 411,
          column: 27
        },
        end: {
          line: 414,
          column: 5
        }
      },
      "105": {
        start: {
          line: 416,
          column: 4
        },
        end: {
          line: 416,
          column: 101
        }
      },
      "106": {
        start: {
          line: 416,
          column: 43
        },
        end: {
          line: 416,
          column: 99
        }
      },
      "107": {
        start: {
          line: 419,
          column: 2
        },
        end: {
          line: 419,
          column: 15
        }
      },
      "108": {
        start: {
          line: 422,
          column: 15
        },
        end: {
          line: 511,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "PoseVisualization",
        decl: {
          start: {
            line: 82,
            column: 16
          },
          end: {
            line: 82,
            column: 33
          }
        },
        loc: {
          start: {
            line: 92,
            column: 27
          },
          end: {
            line: 389,
            column: 1
          }
        },
        line: 92
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 101,
            column: 12
          },
          end: {
            line: 101,
            column: 13
          }
        },
        loc: {
          start: {
            line: 101,
            column: 18
          },
          end: {
            line: 103,
            column: 3
          }
        },
        line: 101
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 105,
            column: 12
          },
          end: {
            line: 105,
            column: 13
          }
        },
        loc: {
          start: {
            line: 105,
            column: 18
          },
          end: {
            line: 107,
            column: 3
          }
        },
        line: 105
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 109,
            column: 12
          },
          end: {
            line: 109,
            column: 13
          }
        },
        loc: {
          start: {
            line: 109,
            column: 18
          },
          end: {
            line: 140,
            column: 3
          }
        },
        line: 109
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 111,
            column: 22
          },
          end: {
            line: 111,
            column: 23
          }
        },
        loc: {
          start: {
            line: 111,
            column: 28
          },
          end: {
            line: 126,
            column: 7
          }
        },
        line: 111
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 117,
            column: 29
          },
          end: {
            line: 117,
            column: 30
          }
        },
        loc: {
          start: {
            line: 117,
            column: 37
          },
          end: {
            line: 121,
            column: 11
          }
        },
        line: 117
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 135,
            column: 11
          },
          end: {
            line: 135,
            column: 12
          }
        },
        loc: {
          start: {
            line: 135,
            column: 17
          },
          end: {
            line: 139,
            column: 5
          }
        },
        line: 135
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 145,
            column: 25
          },
          end: {
            line: 145,
            column: 26
          }
        },
        loc: {
          start: {
            line: 145,
            column: 31
          },
          end: {
            line: 149,
            column: 3
          }
        },
        line: 145
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 151,
            column: 24
          },
          end: {
            line: 151,
            column: 25
          }
        },
        loc: {
          start: {
            line: 151,
            column: 30
          },
          end: {
            line: 156,
            column: 3
          }
        },
        line: 151
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 158,
            column: 30
          },
          end: {
            line: 158,
            column: 31
          }
        },
        loc: {
          start: {
            line: 158,
            column: 61
          },
          end: {
            line: 183,
            column: 3
          }
        },
        line: 158
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 159,
            column: 25
          },
          end: {
            line: 159,
            column: 26
          }
        },
        loc: {
          start: {
            line: 159,
            column: 46
          },
          end: {
            line: 182,
            column: 5
          }
        },
        line: 159
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 185,
            column: 32
          },
          end: {
            line: 185,
            column: 33
          }
        },
        loc: {
          start: {
            line: 185,
            column: 63
          },
          end: {
            line: 219,
            column: 3
          }
        },
        line: 185
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 186,
            column: 32
          },
          end: {
            line: 186,
            column: 33
          }
        },
        loc: {
          start: {
            line: 186,
            column: 55
          },
          end: {
            line: 218,
            column: 5
          }
        },
        line: 186
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 221,
            column: 23
          },
          end: {
            line: 221,
            column: 24
          }
        },
        loc: {
          start: {
            line: 221,
            column: 54
          },
          end: {
            line: 276,
            column: 3
          }
        },
        line: 221
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 248,
            column: 30
          },
          end: {
            line: 248,
            column: 31
          }
        },
        loc: {
          start: {
            line: 248,
            column: 52
          },
          end: {
            line: 275,
            column: 5
          }
        },
        line: 248
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 278,
            column: 36
          },
          end: {
            line: 278,
            column: 37
          }
        },
        loc: {
          start: {
            line: 278,
            column: 42
          },
          end: {
            line: 302,
            column: 3
          }
        },
        line: 278
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 304,
            column: 29
          },
          end: {
            line: 304,
            column: 30
          }
        },
        loc: {
          start: {
            line: 305,
            column: 4
          },
          end: {
            line: 317,
            column: 11
          }
        },
        line: 305
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 320,
            column: 25
          },
          end: {
            line: 320,
            column: 26
          }
        },
        loc: {
          start: {
            line: 321,
            column: 4
          },
          end: {
            line: 337,
            column: 11
          }
        },
        line: 321
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 341,
            column: 67
          },
          end: {
            line: 341,
            column: 68
          }
        },
        loc: {
          start: {
            line: 341,
            column: 78
          },
          end: {
            line: 346,
            column: 3
          }
        },
        line: 341
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 392,
            column: 30
          },
          end: {
            line: 392,
            column: 31
          }
        },
        loc: {
          start: {
            line: 392,
            column: 100
          },
          end: {
            line: 403,
            column: 1
          }
        },
        line: 392
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 405,
            column: 32
          },
          end: {
            line: 405,
            column: 33
          }
        },
        loc: {
          start: {
            line: 405,
            column: 101
          },
          end: {
            line: 420,
            column: 1
          }
        },
        line: 405
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 416,
            column: 31
          },
          end: {
            line: 416,
            column: 32
          }
        },
        loc: {
          start: {
            line: 416,
            column: 43
          },
          end: {
            line: 416,
            column: 99
          }
        },
        line: 416
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 84,
            column: 2
          },
          end: {
            line: 84,
            column: 23
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 84,
            column: 22
          },
          end: {
            line: 84,
            column: 23
          }
        }],
        line: 84
      },
      "1": {
        loc: {
          start: {
            line: 85,
            column: 2
          },
          end: {
            line: 85,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 85,
            column: 14
          },
          end: {
            line: 85,
            column: 19
          }
        }],
        line: 85
      },
      "2": {
        loc: {
          start: {
            line: 86,
            column: 2
          },
          end: {
            line: 86,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 86,
            column: 18
          },
          end: {
            line: 86,
            column: 19
          }
        }],
        line: 86
      },
      "3": {
        loc: {
          start: {
            line: 87,
            column: 2
          },
          end: {
            line: 87,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 87,
            column: 15
          },
          end: {
            line: 87,
            column: 19
          }
        }],
        line: 87
      },
      "4": {
        loc: {
          start: {
            line: 88,
            column: 2
          },
          end: {
            line: 88,
            column: 24
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 88,
            column: 19
          },
          end: {
            line: 88,
            column: 24
          }
        }],
        line: 88
      },
      "5": {
        loc: {
          start: {
            line: 89,
            column: 2
          },
          end: {
            line: 89,
            column: 24
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 89,
            column: 20
          },
          end: {
            line: 89,
            column: 24
          }
        }],
        line: 89
      },
      "6": {
        loc: {
          start: {
            line: 110,
            column: 4
          },
          end: {
            line: 133,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 4
          },
          end: {
            line: 133,
            column: 5
          }
        }, {
          start: {
            line: 129,
            column: 11
          },
          end: {
            line: 133,
            column: 5
          }
        }],
        line: 110
      },
      "7": {
        loc: {
          start: {
            line: 110,
            column: 8
          },
          end: {
            line: 110,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 110,
            column: 8
          },
          end: {
            line: 110,
            column: 22
          }
        }, {
          start: {
            line: 110,
            column: 26
          },
          end: {
            line: 110,
            column: 53
          }
        }],
        line: 110
      },
      "8": {
        loc: {
          start: {
            line: 116,
            column: 8
          },
          end: {
            line: 123,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 8
          },
          end: {
            line: 123,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      },
      "9": {
        loc: {
          start: {
            line: 130,
            column: 6
          },
          end: {
            line: 132,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 6
          },
          end: {
            line: 132,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "10": {
        loc: {
          start: {
            line: 136,
            column: 6
          },
          end: {
            line: 138,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 6
          },
          end: {
            line: 138,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "11": {
        loc: {
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 143,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 143,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "12": {
        loc: {
          start: {
            line: 166,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        }, {
          start: {
            line: 168,
            column: 13
          },
          end: {
            line: 170,
            column: 7
          }
        }],
        line: 166
      },
      "13": {
        loc: {
          start: {
            line: 168,
            column: 13
          },
          end: {
            line: 170,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 13
          },
          end: {
            line: 170,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "14": {
        loc: {
          start: {
            line: 168,
            column: 17
          },
          end: {
            line: 168,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 168,
            column: 17
          },
          end: {
            line: 168,
            column: 32
          }
        }, {
          start: {
            line: 168,
            column: 36
          },
          end: {
            line: 168,
            column: 81
          }
        }],
        line: 168
      },
      "15": {
        loc: {
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 191,
            column: 54
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 191,
            column: 54
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "16": {
        loc: {
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 191,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 191,
            column: 24
          }
        }, {
          start: {
            line: 191,
            column: 28
          },
          end: {
            line: 191,
            column: 40
          }
        }],
        line: 191
      },
      "17": {
        loc: {
          start: {
            line: 202,
            column: 6
          },
          end: {
            line: 204,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 6
          },
          end: {
            line: 204,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "18": {
        loc: {
          start: {
            line: 202,
            column: 10
          },
          end: {
            line: 202,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 202,
            column: 10
          },
          end: {
            line: 202,
            column: 25
          }
        }, {
          start: {
            line: 202,
            column: 29
          },
          end: {
            line: 202,
            column: 81
          }
        }],
        line: 202
      },
      "19": {
        loc: {
          start: {
            line: 222,
            column: 4
          },
          end: {
            line: 222,
            column: 33
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 4
          },
          end: {
            line: 222,
            column: 33
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "20": {
        loc: {
          start: {
            line: 249,
            column: 6
          },
          end: {
            line: 249,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 249,
            column: 6
          },
          end: {
            line: 249,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 249
      },
      "21": {
        loc: {
          start: {
            line: 279,
            column: 4
          },
          end: {
            line: 279,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 4
          },
          end: {
            line: 279,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "22": {
        loc: {
          start: {
            line: 282,
            column: 28
          },
          end: {
            line: 283,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 282,
            column: 47
          },
          end: {
            line: 282,
            column: 59
          }
        }, {
          start: {
            line: 283,
            column: 27
          },
          end: {
            line: 283,
            column: 72
          }
        }],
        line: 282
      },
      "23": {
        loc: {
          start: {
            line: 283,
            column: 27
          },
          end: {
            line: 283,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 283,
            column: 46
          },
          end: {
            line: 283,
            column: 59
          }
        }, {
          start: {
            line: 283,
            column: 62
          },
          end: {
            line: 283,
            column: 72
          }
        }],
        line: 283
      },
      "24": {
        loc: {
          start: {
            line: 327,
            column: 9
          },
          end: {
            line: 331,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 328,
            column: 10
          },
          end: {
            line: 328,
            column: 50
          }
        }, {
          start: {
            line: 330,
            column: 10
          },
          end: {
            line: 330,
            column: 49
          }
        }],
        line: 327
      },
      "25": {
        loc: {
          start: {
            line: 394,
            column: 2
          },
          end: {
            line: 396,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 394,
            column: 2
          },
          end: {
            line: 396,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 394
      },
      "26": {
        loc: {
          start: {
            line: 398,
            column: 2
          },
          end: {
            line: 400,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 398,
            column: 2
          },
          end: {
            line: 400,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 398
      },
      "27": {
        loc: {
          start: {
            line: 410,
            column: 2
          },
          end: {
            line: 417,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 410,
            column: 2
          },
          end: {
            line: 417,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 410
      },
      "28": {
        loc: {
          start: {
            line: 416,
            column: 43
          },
          end: {
            line: 416,
            column: 99
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 416,
            column: 44
          },
          end: {
            line: 416,
            column: 55
          }
        }, {
          start: {
            line: 416,
            column: 59
          },
          end: {
            line: 416,
            column: 68
          }
        }, {
          start: {
            line: 416,
            column: 74
          },
          end: {
            line: 416,
            column: 83
          }
        }, {
          start: {
            line: 416,
            column: 87
          },
          end: {
            line: 416,
            column: 98
          }
        }],
        line: 416
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "71bf431dd8172e84ef6a8daf7de75cf7c91b867f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_256kqr0gyr = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_256kqr0gyr();
import React, { useRef, useEffect, useState } from 'react';
import { View, Text, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import Svg, { Circle, Line, G, Text as SvgText, Defs, LinearGradient, Stop } from 'react-native-svg';
import { Play, Pause, RotateCcw, Settings } from 'lucide-react-native';
import { TENNIS_POSE_LANDMARKS } from "../../services/ai/MediaPipeService";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var _ref = (cov_256kqr0gyr().s[0]++, Dimensions.get('window')),
  width = _ref.width,
  height = _ref.height;
var colors = (cov_256kqr0gyr().s[1]++, {
  primary: '#23ba16',
  secondary: '#1a5e1a',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  blue: '#3b82f6',
  yellow: '#eab308',
  green: '#10b981',
  purple: '#8b5cf6'
});
var POSE_CONNECTIONS = (cov_256kqr0gyr().s[2]++, [[TENNIS_POSE_LANDMARKS.LEFT_EYE, TENNIS_POSE_LANDMARKS.RIGHT_EYE], [TENNIS_POSE_LANDMARKS.LEFT_EAR, TENNIS_POSE_LANDMARKS.LEFT_EYE], [TENNIS_POSE_LANDMARKS.RIGHT_EAR, TENNIS_POSE_LANDMARKS.RIGHT_EYE], [TENNIS_POSE_LANDMARKS.LEFT_SHOULDER, TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER], [TENNIS_POSE_LANDMARKS.LEFT_SHOULDER, TENNIS_POSE_LANDMARKS.LEFT_HIP], [TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER, TENNIS_POSE_LANDMARKS.RIGHT_HIP], [TENNIS_POSE_LANDMARKS.LEFT_HIP, TENNIS_POSE_LANDMARKS.RIGHT_HIP], [TENNIS_POSE_LANDMARKS.LEFT_SHOULDER, TENNIS_POSE_LANDMARKS.LEFT_ELBOW], [TENNIS_POSE_LANDMARKS.LEFT_ELBOW, TENNIS_POSE_LANDMARKS.LEFT_WRIST], [TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER, TENNIS_POSE_LANDMARKS.RIGHT_ELBOW], [TENNIS_POSE_LANDMARKS.RIGHT_ELBOW, TENNIS_POSE_LANDMARKS.RIGHT_WRIST], [TENNIS_POSE_LANDMARKS.LEFT_HIP, TENNIS_POSE_LANDMARKS.LEFT_KNEE], [TENNIS_POSE_LANDMARKS.LEFT_KNEE, TENNIS_POSE_LANDMARKS.LEFT_ANKLE], [TENNIS_POSE_LANDMARKS.RIGHT_HIP, TENNIS_POSE_LANDMARKS.RIGHT_KNEE], [TENNIS_POSE_LANDMARKS.RIGHT_KNEE, TENNIS_POSE_LANDMARKS.RIGHT_ANKLE]]);
export function PoseVisualization(_ref2) {
  var movementAnalyses = _ref2.movementAnalyses,
    _ref2$currentFrameInd = _ref2.currentFrameIndex,
    currentFrameIndex = _ref2$currentFrameInd === void 0 ? (cov_256kqr0gyr().b[0][0]++, 0) : _ref2$currentFrameInd,
    _ref2$isPlaying = _ref2.isPlaying,
    isPlaying = _ref2$isPlaying === void 0 ? (cov_256kqr0gyr().b[1][0]++, false) : _ref2$isPlaying,
    _ref2$playbackSpeed = _ref2.playbackSpeed,
    playbackSpeed = _ref2$playbackSpeed === void 0 ? (cov_256kqr0gyr().b[2][0]++, 1) : _ref2$playbackSpeed,
    _ref2$showAngles = _ref2.showAngles,
    showAngles = _ref2$showAngles === void 0 ? (cov_256kqr0gyr().b[3][0]++, true) : _ref2$showAngles,
    _ref2$showConfidence = _ref2.showConfidence,
    showConfidence = _ref2$showConfidence === void 0 ? (cov_256kqr0gyr().b[4][0]++, false) : _ref2$showConfidence,
    _ref2$highlightIssues = _ref2.highlightIssues,
    highlightIssues = _ref2$highlightIssues === void 0 ? (cov_256kqr0gyr().b[5][0]++, true) : _ref2$highlightIssues,
    onFrameChange = _ref2.onFrameChange,
    onPlayStateChange = _ref2.onPlayStateChange;
  cov_256kqr0gyr().f[0]++;
  var _ref3 = (cov_256kqr0gyr().s[3]++, useState(currentFrameIndex)),
    _ref4 = _slicedToArray(_ref3, 2),
    localFrameIndex = _ref4[0],
    setLocalFrameIndex = _ref4[1];
  var _ref5 = (cov_256kqr0gyr().s[4]++, useState(isPlaying)),
    _ref6 = _slicedToArray(_ref5, 2),
    localIsPlaying = _ref6[0],
    setLocalIsPlaying = _ref6[1];
  var animationRef = (cov_256kqr0gyr().s[5]++, useRef());
  var lastUpdateTime = (cov_256kqr0gyr().s[6]++, useRef(Date.now()));
  var svgWidth = (cov_256kqr0gyr().s[7]++, width - 32);
  var svgHeight = (cov_256kqr0gyr().s[8]++, svgWidth * 0.75);
  cov_256kqr0gyr().s[9]++;
  useEffect(function () {
    cov_256kqr0gyr().f[1]++;
    cov_256kqr0gyr().s[10]++;
    setLocalFrameIndex(currentFrameIndex);
  }, [currentFrameIndex]);
  cov_256kqr0gyr().s[11]++;
  useEffect(function () {
    cov_256kqr0gyr().f[2]++;
    cov_256kqr0gyr().s[12]++;
    setLocalIsPlaying(isPlaying);
  }, [isPlaying]);
  cov_256kqr0gyr().s[13]++;
  useEffect(function () {
    cov_256kqr0gyr().f[3]++;
    cov_256kqr0gyr().s[14]++;
    if ((cov_256kqr0gyr().b[7][0]++, localIsPlaying) && (cov_256kqr0gyr().b[7][1]++, movementAnalyses.length > 1)) {
      cov_256kqr0gyr().b[6][0]++;
      cov_256kqr0gyr().s[15]++;
      var _animate = function animate() {
        cov_256kqr0gyr().f[4]++;
        var now = (cov_256kqr0gyr().s[16]++, Date.now());
        var deltaTime = (cov_256kqr0gyr().s[17]++, now - lastUpdateTime.current);
        var frameInterval = (cov_256kqr0gyr().s[18]++, 1000 / (30 * playbackSpeed));
        cov_256kqr0gyr().s[19]++;
        if (deltaTime >= frameInterval) {
          cov_256kqr0gyr().b[8][0]++;
          cov_256kqr0gyr().s[20]++;
          setLocalFrameIndex(function (prev) {
            cov_256kqr0gyr().f[5]++;
            var nextIndex = (cov_256kqr0gyr().s[21]++, (prev + 1) % movementAnalyses.length);
            cov_256kqr0gyr().s[22]++;
            onFrameChange == null || onFrameChange(nextIndex);
            cov_256kqr0gyr().s[23]++;
            return nextIndex;
          });
          cov_256kqr0gyr().s[24]++;
          lastUpdateTime.current = now;
        } else {
          cov_256kqr0gyr().b[8][1]++;
        }
        cov_256kqr0gyr().s[25]++;
        animationRef.current = requestAnimationFrame(_animate);
      };
      cov_256kqr0gyr().s[26]++;
      animationRef.current = requestAnimationFrame(_animate);
    } else {
      cov_256kqr0gyr().b[6][1]++;
      cov_256kqr0gyr().s[27]++;
      if (animationRef.current) {
        cov_256kqr0gyr().b[9][0]++;
        cov_256kqr0gyr().s[28]++;
        cancelAnimationFrame(animationRef.current);
      } else {
        cov_256kqr0gyr().b[9][1]++;
      }
    }
    cov_256kqr0gyr().s[29]++;
    return function () {
      cov_256kqr0gyr().f[6]++;
      cov_256kqr0gyr().s[30]++;
      if (animationRef.current) {
        cov_256kqr0gyr().b[10][0]++;
        cov_256kqr0gyr().s[31]++;
        cancelAnimationFrame(animationRef.current);
      } else {
        cov_256kqr0gyr().b[10][1]++;
      }
    };
  }, [localIsPlaying, playbackSpeed, movementAnalyses.length, onFrameChange]);
  var currentAnalysis = (cov_256kqr0gyr().s[32]++, movementAnalyses[localFrameIndex]);
  cov_256kqr0gyr().s[33]++;
  if (!currentAnalysis) {
    cov_256kqr0gyr().b[11][0]++;
    cov_256kqr0gyr().s[34]++;
    return null;
  } else {
    cov_256kqr0gyr().b[11][1]++;
  }
  cov_256kqr0gyr().s[35]++;
  var togglePlayback = function togglePlayback() {
    cov_256kqr0gyr().f[7]++;
    var newIsPlaying = (cov_256kqr0gyr().s[36]++, !localIsPlaying);
    cov_256kqr0gyr().s[37]++;
    setLocalIsPlaying(newIsPlaying);
    cov_256kqr0gyr().s[38]++;
    onPlayStateChange == null || onPlayStateChange(newIsPlaying);
  };
  cov_256kqr0gyr().s[39]++;
  var resetPlayback = function resetPlayback() {
    cov_256kqr0gyr().f[8]++;
    cov_256kqr0gyr().s[40]++;
    setLocalFrameIndex(0);
    cov_256kqr0gyr().s[41]++;
    setLocalIsPlaying(false);
    cov_256kqr0gyr().s[42]++;
    onFrameChange == null || onFrameChange(0);
    cov_256kqr0gyr().s[43]++;
    onPlayStateChange == null || onPlayStateChange(false);
  };
  cov_256kqr0gyr().s[44]++;
  var renderPoseLandmarks = function renderPoseLandmarks(landmarks) {
    cov_256kqr0gyr().f[9]++;
    cov_256kqr0gyr().s[45]++;
    return landmarks.map(function (landmark, index) {
      cov_256kqr0gyr().f[10]++;
      var x = (cov_256kqr0gyr().s[46]++, landmark.x * svgWidth);
      var y = (cov_256kqr0gyr().s[47]++, landmark.y * svgHeight);
      var visibility = (cov_256kqr0gyr().s[48]++, landmark.visibility);
      var landmarkColor = (cov_256kqr0gyr().s[49]++, colors.primary);
      cov_256kqr0gyr().s[50]++;
      if (visibility < 0.5) {
        cov_256kqr0gyr().b[12][0]++;
        cov_256kqr0gyr().s[51]++;
        landmarkColor = colors.gray;
      } else {
        cov_256kqr0gyr().b[12][1]++;
        cov_256kqr0gyr().s[52]++;
        if ((cov_256kqr0gyr().b[14][0]++, highlightIssues) && (cov_256kqr0gyr().b[14][1]++, isProblematicLandmark(index, currentAnalysis))) {
          cov_256kqr0gyr().b[13][0]++;
          cov_256kqr0gyr().s[53]++;
          landmarkColor = colors.red;
        } else {
          cov_256kqr0gyr().b[13][1]++;
        }
      }
      cov_256kqr0gyr().s[54]++;
      return _jsx(Circle, {
        cx: x,
        cy: y,
        r: visibility * 4 + 2,
        fill: landmarkColor,
        opacity: visibility
      }, `landmark-${index}`);
    });
  };
  cov_256kqr0gyr().s[55]++;
  var renderPoseConnections = function renderPoseConnections(landmarks) {
    cov_256kqr0gyr().f[11]++;
    cov_256kqr0gyr().s[56]++;
    return POSE_CONNECTIONS.map(function (connection, index) {
      cov_256kqr0gyr().f[12]++;
      var _ref7 = (cov_256kqr0gyr().s[57]++, connection),
        _ref8 = _slicedToArray(_ref7, 2),
        startIdx = _ref8[0],
        endIdx = _ref8[1];
      var startLandmark = (cov_256kqr0gyr().s[58]++, landmarks[startIdx]);
      var endLandmark = (cov_256kqr0gyr().s[59]++, landmarks[endIdx]);
      cov_256kqr0gyr().s[60]++;
      if ((cov_256kqr0gyr().b[16][0]++, !startLandmark) || (cov_256kqr0gyr().b[16][1]++, !endLandmark)) {
        cov_256kqr0gyr().b[15][0]++;
        cov_256kqr0gyr().s[61]++;
        return null;
      } else {
        cov_256kqr0gyr().b[15][1]++;
      }
      var x1 = (cov_256kqr0gyr().s[62]++, startLandmark.x * svgWidth);
      var y1 = (cov_256kqr0gyr().s[63]++, startLandmark.y * svgHeight);
      var x2 = (cov_256kqr0gyr().s[64]++, endLandmark.x * svgWidth);
      var y2 = (cov_256kqr0gyr().s[65]++, endLandmark.y * svgHeight);
      var avgVisibility = (cov_256kqr0gyr().s[66]++, (startLandmark.visibility + endLandmark.visibility) / 2);
      var connectionColor = (cov_256kqr0gyr().s[67]++, colors.blue);
      cov_256kqr0gyr().s[68]++;
      if ((cov_256kqr0gyr().b[18][0]++, highlightIssues) && (cov_256kqr0gyr().b[18][1]++, isProblematicConnection(connection, currentAnalysis))) {
        cov_256kqr0gyr().b[17][0]++;
        cov_256kqr0gyr().s[69]++;
        connectionColor = colors.red;
      } else {
        cov_256kqr0gyr().b[17][1]++;
      }
      cov_256kqr0gyr().s[70]++;
      return _jsx(Line, {
        x1: x1,
        y1: y1,
        x2: x2,
        y2: y2,
        stroke: connectionColor,
        strokeWidth: 2,
        opacity: avgVisibility * 0.8
      }, `connection-${index}`);
    });
  };
  cov_256kqr0gyr().s[71]++;
  var renderAngles = function renderAngles(landmarks) {
    cov_256kqr0gyr().f[13]++;
    cov_256kqr0gyr().s[72]++;
    if (!showAngles) {
      cov_256kqr0gyr().b[19][0]++;
      cov_256kqr0gyr().s[73]++;
      return null;
    } else {
      cov_256kqr0gyr().b[19][1]++;
    }
    var angles = (cov_256kqr0gyr().s[74]++, currentAnalysis.keyAngles);
    var anglePositions = (cov_256kqr0gyr().s[75]++, [{
      angle: angles.shoulderAngle,
      position: landmarks[TENNIS_POSE_LANDMARKS.LEFT_ELBOW],
      label: 'Shoulder'
    }, {
      angle: angles.elbowAngle,
      position: landmarks[TENNIS_POSE_LANDMARKS.LEFT_ELBOW],
      label: 'Elbow'
    }, {
      angle: angles.hipAngle,
      position: landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP],
      label: 'Hip'
    }, {
      angle: angles.kneeAngle,
      position: landmarks[TENNIS_POSE_LANDMARKS.LEFT_KNEE],
      label: 'Knee'
    }]);
    cov_256kqr0gyr().s[76]++;
    return anglePositions.map(function (angleData, index) {
      cov_256kqr0gyr().f[14]++;
      cov_256kqr0gyr().s[77]++;
      if (!angleData.position) {
        cov_256kqr0gyr().b[20][0]++;
        cov_256kqr0gyr().s[78]++;
        return null;
      } else {
        cov_256kqr0gyr().b[20][1]++;
      }
      var x = (cov_256kqr0gyr().s[79]++, angleData.position.x * svgWidth + 20);
      var y = (cov_256kqr0gyr().s[80]++, angleData.position.y * svgHeight - 10);
      cov_256kqr0gyr().s[81]++;
      return _jsxs(G, {
        children: [_jsxs(SvgText, {
          x: x,
          y: y,
          fontSize: "12",
          fill: colors.dark,
          fontWeight: "bold",
          children: [Math.round(angleData.angle), "\xB0"]
        }), _jsx(SvgText, {
          x: x,
          y: y + 15,
          fontSize: "10",
          fill: colors.gray,
          children: angleData.label
        })]
      }, `angle-${index}`);
    });
  };
  cov_256kqr0gyr().s[82]++;
  var renderConfidenceIndicator = function renderConfidenceIndicator() {
    cov_256kqr0gyr().f[15]++;
    cov_256kqr0gyr().s[83]++;
    if (!showConfidence) {
      cov_256kqr0gyr().b[21][0]++;
      cov_256kqr0gyr().s[84]++;
      return null;
    } else {
      cov_256kqr0gyr().b[21][1]++;
    }
    var confidence = (cov_256kqr0gyr().s[85]++, currentAnalysis.confidence);
    var confidenceColor = (cov_256kqr0gyr().s[86]++, confidence > 0.8 ? (cov_256kqr0gyr().b[22][0]++, colors.green) : (cov_256kqr0gyr().b[22][1]++, confidence > 0.6 ? (cov_256kqr0gyr().b[23][0]++, colors.yellow) : (cov_256kqr0gyr().b[23][1]++, colors.red)));
    cov_256kqr0gyr().s[87]++;
    return _jsxs(View, {
      style: styles.confidenceIndicator,
      children: [_jsx(Text, {
        style: styles.confidenceLabel,
        children: "Confidence"
      }), _jsx(View, {
        style: styles.confidenceBar,
        children: _jsx(View, {
          style: [styles.confidenceFill, {
            width: `${confidence * 100}%`,
            backgroundColor: confidenceColor
          }]
        })
      }), _jsxs(Text, {
        style: styles.confidenceValue,
        children: [Math.round(confidence * 100), "%"]
      })]
    });
  };
  cov_256kqr0gyr().s[88]++;
  var renderMovementInfo = function renderMovementInfo() {
    cov_256kqr0gyr().f[16]++;
    cov_256kqr0gyr().s[89]++;
    return _jsxs(View, {
      style: styles.movementInfo,
      children: [_jsx(Text, {
        style: styles.movementType,
        children: currentAnalysis.movementType.toUpperCase()
      }), _jsxs(Text, {
        style: styles.movementDetails,
        children: ["Frame ", localFrameIndex + 1, " of ", movementAnalyses.length]
      }), _jsxs(Text, {
        style: styles.bodyPosition,
        children: ["Stance: ", currentAnalysis.bodyPosition.stance, " \u2022 Weight: ", currentAnalysis.bodyPosition.weight, " \u2022 Balance: ", Math.round(currentAnalysis.bodyPosition.balance * 100), "%"]
      })]
    });
  };
  cov_256kqr0gyr().s[90]++;
  var renderControls = function renderControls() {
    cov_256kqr0gyr().f[17]++;
    cov_256kqr0gyr().s[91]++;
    return _jsxs(View, {
      style: styles.controls,
      children: [_jsx(TouchableOpacity, {
        style: styles.controlButton,
        onPress: resetPlayback,
        children: _jsx(RotateCcw, {
          size: 20,
          color: colors.primary
        })
      }), _jsx(TouchableOpacity, {
        style: styles.playButton,
        onPress: togglePlayback,
        children: localIsPlaying ? (cov_256kqr0gyr().b[24][0]++, _jsx(Pause, {
          size: 24,
          color: colors.white
        })) : (cov_256kqr0gyr().b[24][1]++, _jsx(Play, {
          size: 24,
          color: colors.white
        }))
      }), _jsx(TouchableOpacity, {
        style: styles.controlButton,
        children: _jsx(Settings, {
          size: 20,
          color: colors.primary
        })
      })]
    });
  };
  var mockLandmarks = (cov_256kqr0gyr().s[92]++, Array.from({
    length: 33
  }, function (_, i) {
    cov_256kqr0gyr().f[18]++;
    cov_256kqr0gyr().s[93]++;
    return {
      x: Math.random() * 0.8 + 0.1,
      y: Math.random() * 0.8 + 0.1,
      z: Math.random() * 0.2 - 0.1,
      visibility: Math.random() * 0.3 + 0.7
    };
  }));
  cov_256kqr0gyr().s[94]++;
  return _jsxs(View, {
    style: styles.container,
    children: [renderMovementInfo(), _jsxs(View, {
      style: styles.visualizationContainer,
      children: [_jsxs(Svg, {
        width: svgWidth,
        height: svgHeight,
        style: styles.svg,
        children: [_jsx(Defs, {
          children: _jsxs(LinearGradient, {
            id: "backgroundGradient",
            x1: "0%",
            y1: "0%",
            x2: "100%",
            y2: "100%",
            children: [_jsx(Stop, {
              offset: "0%",
              stopColor: colors.lightGray,
              stopOpacity: "0.3"
            }), _jsx(Stop, {
              offset: "100%",
              stopColor: colors.white,
              stopOpacity: "0.1"
            })]
          })
        }), _jsx(Circle, {
          cx: svgWidth / 2,
          cy: svgHeight / 2,
          r: Math.min(svgWidth, svgHeight) / 2 - 20,
          fill: "url(#backgroundGradient)",
          stroke: colors.gray,
          strokeWidth: 1,
          strokeDasharray: "5,5",
          opacity: 0.3
        }), _jsx(G, {
          children: renderPoseConnections(mockLandmarks)
        }), _jsx(G, {
          children: renderPoseLandmarks(mockLandmarks)
        }), _jsx(G, {
          children: renderAngles(mockLandmarks)
        })]
      }), renderConfidenceIndicator()]
    }), renderControls()]
  });
}
cov_256kqr0gyr().s[95]++;
var isProblematicLandmark = function isProblematicLandmark(landmarkIndex, analysis) {
  cov_256kqr0gyr().f[19]++;
  cov_256kqr0gyr().s[96]++;
  if (analysis.technicalMetrics.followThrough === 'none') {
    cov_256kqr0gyr().b[25][0]++;
    cov_256kqr0gyr().s[97]++;
    return [TENNIS_POSE_LANDMARKS.RIGHT_WRIST, TENNIS_POSE_LANDMARKS.RIGHT_ELBOW].includes(landmarkIndex);
  } else {
    cov_256kqr0gyr().b[25][1]++;
  }
  cov_256kqr0gyr().s[98]++;
  if (analysis.bodyPosition.balance < 0.6) {
    cov_256kqr0gyr().b[26][0]++;
    cov_256kqr0gyr().s[99]++;
    return [TENNIS_POSE_LANDMARKS.LEFT_ANKLE, TENNIS_POSE_LANDMARKS.RIGHT_ANKLE].includes(landmarkIndex);
  } else {
    cov_256kqr0gyr().b[26][1]++;
  }
  cov_256kqr0gyr().s[100]++;
  return false;
};
cov_256kqr0gyr().s[101]++;
var isProblematicConnection = function isProblematicConnection(connection, analysis) {
  cov_256kqr0gyr().f[20]++;
  var _ref9 = (cov_256kqr0gyr().s[102]++, connection),
    _ref0 = _slicedToArray(_ref9, 2),
    start = _ref0[0],
    end = _ref0[1];
  cov_256kqr0gyr().s[103]++;
  if (analysis.technicalMetrics.followThrough === 'none') {
    cov_256kqr0gyr().b[27][0]++;
    var armConnections = (cov_256kqr0gyr().s[104]++, [[TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER, TENNIS_POSE_LANDMARKS.RIGHT_ELBOW], [TENNIS_POSE_LANDMARKS.RIGHT_ELBOW, TENNIS_POSE_LANDMARKS.RIGHT_WRIST]]);
    cov_256kqr0gyr().s[105]++;
    return armConnections.some(function (_ref1) {
      var _ref10 = _slicedToArray(_ref1, 2),
        s = _ref10[0],
        e = _ref10[1];
      cov_256kqr0gyr().f[21]++;
      cov_256kqr0gyr().s[106]++;
      return (cov_256kqr0gyr().b[28][0]++, s === start) && (cov_256kqr0gyr().b[28][1]++, e === end) || (cov_256kqr0gyr().b[28][2]++, s === end) && (cov_256kqr0gyr().b[28][3]++, e === start);
    });
  } else {
    cov_256kqr0gyr().b[27][1]++;
  }
  cov_256kqr0gyr().s[107]++;
  return false;
};
var styles = (cov_256kqr0gyr().s[108]++, StyleSheet.create({
  container: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    margin: 16
  },
  movementInfo: {
    marginBottom: 16
  },
  movementType: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary,
    textAlign: 'center',
    marginBottom: 4
  },
  movementDetails: {
    fontSize: 14,
    color: colors.gray,
    textAlign: 'center',
    marginBottom: 4
  },
  bodyPosition: {
    fontSize: 12,
    color: colors.gray,
    textAlign: 'center'
  },
  visualizationContainer: {
    position: 'relative',
    alignItems: 'center',
    marginBottom: 16
  },
  svg: {
    backgroundColor: colors.lightGray,
    borderRadius: 8
  },
  confidenceIndicator: {
    position: 'absolute',
    top: 10,
    right: 10,
    backgroundColor: colors.white,
    padding: 8,
    borderRadius: 8,
    minWidth: 80
  },
  confidenceLabel: {
    fontSize: 10,
    color: colors.gray,
    marginBottom: 4
  },
  confidenceBar: {
    height: 4,
    backgroundColor: colors.lightGray,
    borderRadius: 2,
    marginBottom: 4
  },
  confidenceFill: {
    height: '100%',
    borderRadius: 2
  },
  confidenceValue: {
    fontSize: 12,
    fontWeight: 'bold',
    color: colors.dark,
    textAlign: 'center'
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 20
  },
  controlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.lightGray,
    justifyContent: 'center',
    alignItems: 'center'
  },
  playButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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