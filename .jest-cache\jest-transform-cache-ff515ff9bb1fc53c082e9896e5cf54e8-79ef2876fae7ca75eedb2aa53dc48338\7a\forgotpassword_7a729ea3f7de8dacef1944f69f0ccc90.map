{"version": 3, "names": ["React", "useState", "View", "Text", "TextInput", "TouchableOpacity", "StyleSheet", "SafeAreaView", "<PERSON><PERSON>", "KeyboardAvoidingView", "Platform", "LinearGradient", "router", "useAuth", "Mail", "ArrowLeft", "jsx", "_jsx", "jsxs", "_jsxs", "ForgotPasswordScreen", "cov_1gey8j1jx8", "f", "_ref", "s", "_ref2", "_slicedToArray", "email", "setEmail", "_ref3", "_ref4", "loading", "setLoading", "_ref5", "resetPassword", "handleResetPassword", "_ref6", "_asyncToGenerator", "b", "alert", "_ref7", "error", "text", "onPress", "back", "apply", "arguments", "handleBack", "style", "styles", "container", "children", "colors", "gradient", "behavior", "OS", "keyboard<PERSON>iew", "content", "backButton", "size", "color", "header", "title", "subtitle", "form", "inputContainer", "inputIcon", "input", "placeholder", "placeholderTextColor", "value", "onChangeText", "keyboardType", "autoCapitalize", "autoCorrect", "resetButton", "resetButtonDisabled", "disabled", "resetButtonText", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "backToLoginText", "create", "flex", "justifyContent", "padding", "position", "top", "left", "zIndex", "alignItems", "marginBottom", "fontSize", "fontWeight", "textAlign", "lineHeight", "backgroundColor", "borderRadius", "shadowColor", "shadowOffset", "width", "height", "shadowOpacity", "shadowRadius", "elevation", "flexDirection", "paddingHorizontal", "paddingVertical", "marginRight", "opacity"], "sources": ["forgot-password.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  View,\n  Text,\n  TextInput,\n  TouchableOpacity,\n  StyleSheet,\n  SafeAreaView,\n  Alert,\n  KeyboardAvoidingView,\n  Platform,\n} from 'react-native';\nimport { LinearGradient } from 'expo-linear-gradient';\nimport { router } from 'expo-router';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { Mail, ArrowLeft } from 'lucide-react-native';\n\nexport default function ForgotPasswordScreen() {\n  const [email, setEmail] = useState('');\n  const [loading, setLoading] = useState(false);\n  const { resetPassword } = useAuth();\n\n  const handleResetPassword = async () => {\n    if (!email) {\n      Alert.alert('Error', 'Please enter your email address');\n      return;\n    }\n\n    setLoading(true);\n    try {\n      const { error } = await resetPassword(email);\n\n      if (error) {\n        Alert.alert('Error', error);\n      } else {\n        Alert.alert(\n          'Reset Email Sent',\n          'Check your email for password reset instructions.',\n          [\n            {\n              text: 'OK',\n              onPress: () => router.back(),\n            },\n          ]\n        );\n      }\n    } catch (error) {\n      Alert.alert('Error', 'An unexpected error occurred. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleBack = () => {\n    router.back();\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <LinearGradient\n        colors={['#1e3a8a', '#3b82f6', '#60a5fa']}\n        style={styles.gradient}\n      >\n        <KeyboardAvoidingView\n          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}\n          style={styles.keyboardView}\n        >\n          <View style={styles.content}>\n            <TouchableOpacity onPress={handleBack} style={styles.backButton}>\n              <ArrowLeft size={24} color=\"white\" />\n            </TouchableOpacity>\n\n            <View style={styles.header}>\n              <Text style={styles.title}>Reset Password</Text>\n              <Text style={styles.subtitle}>\n                Enter your email address and we'll send you instructions to reset your password\n              </Text>\n            </View>\n\n            <View style={styles.form}>\n              <View style={styles.inputContainer}>\n                <Mail size={20} color=\"#6b7280\" style={styles.inputIcon} />\n                <TextInput\n                  style={styles.input}\n                  placeholder=\"Email\"\n                  placeholderTextColor=\"#6b7280\"\n                  value={email}\n                  onChangeText={setEmail}\n                  keyboardType=\"email-address\"\n                  autoCapitalize=\"none\"\n                  autoCorrect={false}\n                />\n              </View>\n\n              <TouchableOpacity\n                style={[styles.resetButton, loading && styles.resetButtonDisabled]}\n                onPress={handleResetPassword}\n                disabled={loading}\n              >\n                <Text style={styles.resetButtonText}>\n                  {loading ? 'Sending...' : 'Send Reset Instructions'}\n                </Text>\n              </TouchableOpacity>\n\n              <TouchableOpacity onPress={handleBack} style={styles.backToLogin}>\n                <Text style={styles.backToLoginText}>Back to Sign In</Text>\n              </TouchableOpacity>\n            </View>\n          </View>\n        </KeyboardAvoidingView>\n      </LinearGradient>\n    </SafeAreaView>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  gradient: {\n    flex: 1,\n  },\n  keyboardView: {\n    flex: 1,\n  },\n  content: {\n    flex: 1,\n    justifyContent: 'center',\n    padding: 20,\n  },\n  backButton: {\n    position: 'absolute',\n    top: 60,\n    left: 20,\n    zIndex: 1,\n  },\n  header: {\n    alignItems: 'center',\n    marginBottom: 40,\n  },\n  title: {\n    fontSize: 32,\n    fontWeight: 'bold',\n    color: 'white',\n    marginBottom: 16,\n  },\n  subtitle: {\n    fontSize: 16,\n    color: 'rgba(255, 255, 255, 0.8)',\n    textAlign: 'center',\n    lineHeight: 24,\n  },\n  form: {\n    backgroundColor: 'white',\n    borderRadius: 20,\n    padding: 24,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 4,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 8,\n    elevation: 8,\n  },\n  inputContainer: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    backgroundColor: '#f9fafb',\n    borderRadius: 12,\n    marginBottom: 24,\n    paddingHorizontal: 16,\n    paddingVertical: 12,\n  },\n  inputIcon: {\n    marginRight: 12,\n  },\n  input: {\n    flex: 1,\n    fontSize: 16,\n    color: '#111827',\n  },\n  resetButton: {\n    backgroundColor: '#3b82f6',\n    borderRadius: 12,\n    paddingVertical: 16,\n    alignItems: 'center',\n    marginBottom: 24,\n  },\n  resetButtonDisabled: {\n    opacity: 0.6,\n  },\n  resetButtonText: {\n    color: 'white',\n    fontSize: 16,\n    fontWeight: '600',\n  },\n  backToLogin: {\n    alignItems: 'center',\n  },\n  backToLoginText: {\n    color: '#3b82f6',\n    fontSize: 14,\n    fontWeight: '500',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,IAAI,EACJC,SAAS,EACTC,gBAAgB,EAChBC,UAAU,EACVC,YAAY,EACZC,KAAK,EACLC,oBAAoB,EACpBC,QAAQ,QACH,cAAc;AACrB,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,OAAO;AAChB,SAASC,IAAI,EAAEC,SAAS,QAAQ,qBAAqB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEtD,eAAe,SAASC,oBAAoBA,CAAA,EAAG;EAAAC,cAAA,GAAAC,CAAA;EAC7C,IAAAC,IAAA,IAAAF,cAAA,GAAAG,CAAA,OAA0BvB,QAAQ,CAAC,EAAE,CAAC;IAAAwB,KAAA,GAAAC,cAAA,CAAAH,IAAA;IAA/BI,KAAK,GAAAF,KAAA;IAAEG,QAAQ,GAAAH,KAAA;EACtB,IAAAI,KAAA,IAAAR,cAAA,GAAAG,CAAA,OAA8BvB,QAAQ,CAAC,KAAK,CAAC;IAAA6B,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAAtCE,OAAO,GAAAD,KAAA;IAAEE,UAAU,GAAAF,KAAA;EAC1B,IAAAG,KAAA,IAAAZ,cAAA,GAAAG,CAAA,OAA0BX,OAAO,CAAC,CAAC;IAA3BqB,aAAa,GAAAD,KAAA,CAAbC,aAAa;EAAeb,cAAA,GAAAG,CAAA;EAEpC,IAAMW,mBAAmB;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAAAhB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACtC,IAAI,CAACG,KAAK,EAAE;QAAAN,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAG,CAAA;QACVhB,KAAK,CAAC+B,KAAK,CAAC,OAAO,EAAE,iCAAiC,CAAC;QAAClB,cAAA,GAAAG,CAAA;QACxD;MACF,CAAC;QAAAH,cAAA,GAAAiB,CAAA;MAAA;MAAAjB,cAAA,GAAAG,CAAA;MAEDQ,UAAU,CAAC,IAAI,CAAC;MAACX,cAAA,GAAAG,CAAA;MACjB,IAAI;QACF,IAAAgB,KAAA,IAAAnB,cAAA,GAAAG,CAAA,aAAwBU,aAAa,CAACP,KAAK,CAAC;UAApCc,KAAK,GAAAD,KAAA,CAALC,KAAK;QAAgCpB,cAAA,GAAAG,CAAA;QAE7C,IAAIiB,KAAK,EAAE;UAAApB,cAAA,GAAAiB,CAAA;UAAAjB,cAAA,GAAAG,CAAA;UACThB,KAAK,CAAC+B,KAAK,CAAC,OAAO,EAAEE,KAAK,CAAC;QAC7B,CAAC,MAAM;UAAApB,cAAA,GAAAiB,CAAA;UAAAjB,cAAA,GAAAG,CAAA;UACLhB,KAAK,CAAC+B,KAAK,CACT,kBAAkB,EAClB,mDAAmD,EACnD,CACE;YACEG,IAAI,EAAE,IAAI;YACVC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;cAAAtB,cAAA,GAAAC,CAAA;cAAAD,cAAA,GAAAG,CAAA;cAAA,OAAAZ,MAAM,CAACgC,IAAI,CAAC,CAAC;YAAD;UAC7B,CAAC,CAEL,CAAC;QACH;MACF,CAAC,CAAC,OAAOH,KAAK,EAAE;QAAApB,cAAA,GAAAG,CAAA;QACdhB,KAAK,CAAC+B,KAAK,CAAC,OAAO,EAAE,iDAAiD,CAAC;MACzE,CAAC,SAAS;QAAAlB,cAAA,GAAAG,CAAA;QACRQ,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBA7BKG,mBAAmBA,CAAA;MAAA,OAAAC,KAAA,CAAAS,KAAA,OAAAC,SAAA;IAAA;EAAA,GA6BxB;EAACzB,cAAA,GAAAG,CAAA;EAEF,IAAMuB,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IAAA1B,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACvBZ,MAAM,CAACgC,IAAI,CAAC,CAAC;EACf,CAAC;EAACvB,cAAA,GAAAG,CAAA;EAEF,OACEP,IAAA,CAACV,YAAY;IAACyC,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EACpClC,IAAA,CAACN,cAAc;MACbyC,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAE;MAC1CJ,KAAK,EAAEC,MAAM,CAACI,QAAS;MAAAF,QAAA,EAEvBlC,IAAA,CAACR,oBAAoB;QACnB6C,QAAQ,EAAE5C,QAAQ,CAAC6C,EAAE,KAAK,KAAK,IAAAlC,cAAA,GAAAiB,CAAA,UAAG,SAAS,KAAAjB,cAAA,GAAAiB,CAAA,UAAG,QAAQ,CAAC;QACvDU,KAAK,EAAEC,MAAM,CAACO,YAAa;QAAAL,QAAA,EAE3BhC,KAAA,CAACjB,IAAI;UAAC8C,KAAK,EAAEC,MAAM,CAACQ,OAAQ;UAAAN,QAAA,GAC1BlC,IAAA,CAACZ,gBAAgB;YAACsC,OAAO,EAAEI,UAAW;YAACC,KAAK,EAAEC,MAAM,CAACS,UAAW;YAAAP,QAAA,EAC9DlC,IAAA,CAACF,SAAS;cAAC4C,IAAI,EAAE,EAAG;cAACC,KAAK,EAAC;YAAO,CAAE;UAAC,CACrB,CAAC,EAEnBzC,KAAA,CAACjB,IAAI;YAAC8C,KAAK,EAAEC,MAAM,CAACY,MAAO;YAAAV,QAAA,GACzBlC,IAAA,CAACd,IAAI;cAAC6C,KAAK,EAAEC,MAAM,CAACa,KAAM;cAAAX,QAAA,EAAC;YAAc,CAAM,CAAC,EAChDlC,IAAA,CAACd,IAAI;cAAC6C,KAAK,EAAEC,MAAM,CAACc,QAAS;cAAAZ,QAAA,EAAC;YAE9B,CAAM,CAAC;UAAA,CACH,CAAC,EAEPhC,KAAA,CAACjB,IAAI;YAAC8C,KAAK,EAAEC,MAAM,CAACe,IAAK;YAAAb,QAAA,GACvBhC,KAAA,CAACjB,IAAI;cAAC8C,KAAK,EAAEC,MAAM,CAACgB,cAAe;cAAAd,QAAA,GACjClC,IAAA,CAACH,IAAI;gBAAC6C,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAC,SAAS;gBAACZ,KAAK,EAAEC,MAAM,CAACiB;cAAU,CAAE,CAAC,EAC3DjD,IAAA,CAACb,SAAS;gBACR4C,KAAK,EAAEC,MAAM,CAACkB,KAAM;gBACpBC,WAAW,EAAC,OAAO;gBACnBC,oBAAoB,EAAC,SAAS;gBAC9BC,KAAK,EAAE3C,KAAM;gBACb4C,YAAY,EAAE3C,QAAS;gBACvB4C,YAAY,EAAC,eAAe;gBAC5BC,cAAc,EAAC,MAAM;gBACrBC,WAAW,EAAE;cAAM,CACpB,CAAC;YAAA,CACE,CAAC,EAEPzD,IAAA,CAACZ,gBAAgB;cACf2C,KAAK,EAAE,CAACC,MAAM,CAAC0B,WAAW,EAAE,CAAAtD,cAAA,GAAAiB,CAAA,UAAAP,OAAO,MAAAV,cAAA,GAAAiB,CAAA,UAAIW,MAAM,CAAC2B,mBAAmB,EAAE;cACnEjC,OAAO,EAAER,mBAAoB;cAC7B0C,QAAQ,EAAE9C,OAAQ;cAAAoB,QAAA,EAElBlC,IAAA,CAACd,IAAI;gBAAC6C,KAAK,EAAEC,MAAM,CAAC6B,eAAgB;gBAAA3B,QAAA,EACjCpB,OAAO,IAAAV,cAAA,GAAAiB,CAAA,UAAG,YAAY,KAAAjB,cAAA,GAAAiB,CAAA,UAAG,yBAAyB;cAAA,CAC/C;YAAC,CACS,CAAC,EAEnBrB,IAAA,CAACZ,gBAAgB;cAACsC,OAAO,EAAEI,UAAW;cAACC,KAAK,EAAEC,MAAM,CAAC8B,WAAY;cAAA5B,QAAA,EAC/DlC,IAAA,CAACd,IAAI;gBAAC6C,KAAK,EAAEC,MAAM,CAAC+B,eAAgB;gBAAA7B,QAAA,EAAC;cAAe,CAAM;YAAC,CAC3C,CAAC;UAAA,CACf,CAAC;QAAA,CACH;MAAC,CACa;IAAC,CACT;EAAC,CACL,CAAC;AAEnB;AAEA,IAAMF,MAAM,IAAA5B,cAAA,GAAAG,CAAA,QAAGlB,UAAU,CAAC2E,MAAM,CAAC;EAC/B/B,SAAS,EAAE;IACTgC,IAAI,EAAE;EACR,CAAC;EACD7B,QAAQ,EAAE;IACR6B,IAAI,EAAE;EACR,CAAC;EACD1B,YAAY,EAAE;IACZ0B,IAAI,EAAE;EACR,CAAC;EACDzB,OAAO,EAAE;IACPyB,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE;EACX,CAAC;EACD1B,UAAU,EAAE;IACV2B,QAAQ,EAAE,UAAU;IACpBC,GAAG,EAAE,EAAE;IACPC,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE;EACV,CAAC;EACD3B,MAAM,EAAE;IACN4B,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACD5B,KAAK,EAAE;IACL6B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBhC,KAAK,EAAE,OAAO;IACd8B,YAAY,EAAE;EAChB,CAAC;EACD3B,QAAQ,EAAE;IACR4B,QAAQ,EAAE,EAAE;IACZ/B,KAAK,EAAE,0BAA0B;IACjCiC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd,CAAC;EACD9B,IAAI,EAAE;IACJ+B,eAAe,EAAE,OAAO;IACxBC,YAAY,EAAE,EAAE;IAChBZ,OAAO,EAAE,EAAE;IACXa,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDC,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDtC,cAAc,EAAE;IACduC,aAAa,EAAE,KAAK;IACpBf,UAAU,EAAE,QAAQ;IACpBM,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,EAAE;IAChBN,YAAY,EAAE,EAAE;IAChBe,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE;EACnB,CAAC;EACDxC,SAAS,EAAE;IACTyC,WAAW,EAAE;EACf,CAAC;EACDxC,KAAK,EAAE;IACLe,IAAI,EAAE,CAAC;IACPS,QAAQ,EAAE,EAAE;IACZ/B,KAAK,EAAE;EACT,CAAC;EACDe,WAAW,EAAE;IACXoB,eAAe,EAAE,SAAS;IAC1BC,YAAY,EAAE,EAAE;IAChBU,eAAe,EAAE,EAAE;IACnBjB,UAAU,EAAE,QAAQ;IACpBC,YAAY,EAAE;EAChB,CAAC;EACDd,mBAAmB,EAAE;IACnBgC,OAAO,EAAE;EACX,CAAC;EACD9B,eAAe,EAAE;IACflB,KAAK,EAAE,OAAO;IACd+B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDb,WAAW,EAAE;IACXU,UAAU,EAAE;EACd,CAAC;EACDT,eAAe,EAAE;IACfpB,KAAK,EAAE,SAAS;IAChB+B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd;AACF,CAAC,CAAC", "ignoreList": []}