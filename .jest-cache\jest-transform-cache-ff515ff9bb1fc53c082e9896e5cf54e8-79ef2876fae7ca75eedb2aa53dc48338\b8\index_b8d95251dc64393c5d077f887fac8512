f284332b02ee4a9682f3589adc38db95
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _toConsumableArray2 = _interopRequireDefault2(require("@babel/runtime/helpers/toConsumableArray"));
var _defineProperty2 = _interopRequireDefault2(require("@babel/runtime/helpers/defineProperty"));
var _PROPERTIES_FLIP;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.atomic = atomic;
exports.classic = classic;
exports.inline = inline;
exports.stringifyValueWithProperty = stringifyValueWithProperty;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _createReactDOMStyle = _interopRequireDefault(require("./createReactDOMStyle"));
var _hash = _interopRequireDefault(require("./hash"));
var _hyphenateStyleName = _interopRequireDefault(require("./hyphenateStyleName"));
var _normalizeValueWithProperty = _interopRequireDefault(require("./normalizeValueWithProperty"));
var _prefixStyles = _interopRequireDefault(require("../../../modules/prefixStyles"));
var _excluded = ["animationKeyframes"];
var cache = new Map();
var emptyObject = {};
var classicGroup = 1;
var atomicGroup = 3;
var customGroup = {
  borderColor: 2,
  borderRadius: 2,
  borderStyle: 2,
  borderWidth: 2,
  display: 2,
  flex: 2,
  inset: 2,
  margin: 2,
  overflow: 2,
  overscrollBehavior: 2,
  padding: 2,
  insetBlock: 2.1,
  insetInline: 2.1,
  marginInline: 2.1,
  marginBlock: 2.1,
  paddingInline: 2.1,
  paddingBlock: 2.1,
  borderBlockStartColor: 2.2,
  borderBlockStartStyle: 2.2,
  borderBlockStartWidth: 2.2,
  borderBlockEndColor: 2.2,
  borderBlockEndStyle: 2.2,
  borderBlockEndWidth: 2.2,
  borderInlineStartColor: 2.2,
  borderInlineStartStyle: 2.2,
  borderInlineStartWidth: 2.2,
  borderInlineEndColor: 2.2,
  borderInlineEndStyle: 2.2,
  borderInlineEndWidth: 2.2,
  borderEndStartRadius: 2.2,
  borderEndEndRadius: 2.2,
  borderStartStartRadius: 2.2,
  borderStartEndRadius: 2.2,
  insetBlockEnd: 2.2,
  insetBlockStart: 2.2,
  insetInlineEnd: 2.2,
  insetInlineStart: 2.2,
  marginBlockStart: 2.2,
  marginBlockEnd: 2.2,
  marginInlineStart: 2.2,
  marginInlineEnd: 2.2,
  paddingBlockStart: 2.2,
  paddingBlockEnd: 2.2,
  paddingInlineStart: 2.2,
  paddingInlineEnd: 2.2
};
var borderTopLeftRadius = 'borderTopLeftRadius';
var borderTopRightRadius = 'borderTopRightRadius';
var borderBottomLeftRadius = 'borderBottomLeftRadius';
var borderBottomRightRadius = 'borderBottomRightRadius';
var borderLeftColor = 'borderLeftColor';
var borderLeftStyle = 'borderLeftStyle';
var borderLeftWidth = 'borderLeftWidth';
var borderRightColor = 'borderRightColor';
var borderRightStyle = 'borderRightStyle';
var borderRightWidth = 'borderRightWidth';
var right = 'right';
var marginLeft = 'marginLeft';
var marginRight = 'marginRight';
var paddingLeft = 'paddingLeft';
var paddingRight = 'paddingRight';
var left = 'left';
var PROPERTIES_FLIP = (_PROPERTIES_FLIP = {}, (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)(_PROPERTIES_FLIP, borderTopLeftRadius, borderTopRightRadius), borderTopRightRadius, borderTopLeftRadius), borderBottomLeftRadius, borderBottomRightRadius), borderBottomRightRadius, borderBottomLeftRadius), borderLeftColor, borderRightColor), borderLeftStyle, borderRightStyle), borderLeftWidth, borderRightWidth), borderRightColor, borderLeftColor), borderRightStyle, borderLeftStyle), borderRightWidth, borderLeftWidth), (0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)((0, _defineProperty2.default)(_PROPERTIES_FLIP, left, right), marginLeft, marginRight), marginRight, marginLeft), paddingLeft, paddingRight), paddingRight, paddingLeft), right, left));
var PROPERTIES_I18N = {
  borderStartStartRadius: borderTopLeftRadius,
  borderStartEndRadius: borderTopRightRadius,
  borderEndStartRadius: borderBottomLeftRadius,
  borderEndEndRadius: borderBottomRightRadius,
  borderInlineStartColor: borderLeftColor,
  borderInlineStartStyle: borderLeftStyle,
  borderInlineStartWidth: borderLeftWidth,
  borderInlineEndColor: borderRightColor,
  borderInlineEndStyle: borderRightStyle,
  borderInlineEndWidth: borderRightWidth,
  insetInlineEnd: right,
  insetInlineStart: left,
  marginInlineStart: marginLeft,
  marginInlineEnd: marginRight,
  paddingInlineStart: paddingLeft,
  paddingInlineEnd: paddingRight
};
var PROPERTIES_VALUE = ['clear', 'float', 'textAlign'];
function atomic(style) {
  var compiledStyle = {
    $$css: true
  };
  var compiledRules = [];
  function atomicCompile(srcProp, prop, value) {
    var valueString = stringifyValueWithProperty(value, prop);
    var cacheKey = prop + valueString;
    var cachedResult = cache.get(cacheKey);
    var identifier;
    if (cachedResult != null) {
      identifier = cachedResult[0];
      compiledRules.push(cachedResult[1]);
    } else {
      var v = srcProp !== prop ? cacheKey : valueString;
      identifier = createIdentifier('r', srcProp, v);
      var order = customGroup[srcProp] || atomicGroup;
      var rules = createAtomicRules(identifier, prop, value);
      var orderedRules = [rules, order];
      compiledRules.push(orderedRules);
      cache.set(cacheKey, [identifier, orderedRules]);
    }
    return identifier;
  }
  Object.keys(style).sort().forEach(function (srcProp) {
    var value = style[srcProp];
    if (value != null) {
      var localizeableValue;
      if (PROPERTIES_VALUE.indexOf(srcProp) > -1) {
        var _left = atomicCompile(srcProp, srcProp, 'left');
        var _right = atomicCompile(srcProp, srcProp, 'right');
        if (value === 'start') {
          localizeableValue = [_left, _right];
        } else if (value === 'end') {
          localizeableValue = [_right, _left];
        }
      }
      var propPolyfill = PROPERTIES_I18N[srcProp];
      if (propPolyfill != null) {
        var ltr = atomicCompile(srcProp, propPolyfill, value);
        var rtl = atomicCompile(srcProp, PROPERTIES_FLIP[propPolyfill], value);
        localizeableValue = [ltr, rtl];
      }
      if (srcProp === 'transitionProperty') {
        var values = Array.isArray(value) ? value : [value];
        var polyfillIndices = [];
        for (var i = 0; i < values.length; i++) {
          var val = values[i];
          if (typeof val === 'string' && PROPERTIES_I18N[val] != null) {
            polyfillIndices.push(i);
          }
        }
        if (polyfillIndices.length > 0) {
          var ltrPolyfillValues = (0, _toConsumableArray2.default)(values);
          var rtlPolyfillValues = (0, _toConsumableArray2.default)(values);
          polyfillIndices.forEach(function (i) {
            var ltrVal = ltrPolyfillValues[i];
            if (typeof ltrVal === 'string') {
              var ltrPolyfill = PROPERTIES_I18N[ltrVal];
              var rtlPolyfill = PROPERTIES_FLIP[ltrPolyfill];
              ltrPolyfillValues[i] = ltrPolyfill;
              rtlPolyfillValues[i] = rtlPolyfill;
              var _ltr = atomicCompile(srcProp, srcProp, ltrPolyfillValues);
              var _rtl = atomicCompile(srcProp, srcProp, rtlPolyfillValues);
              localizeableValue = [_ltr, _rtl];
            }
          });
        }
      }
      if (localizeableValue == null) {
        localizeableValue = atomicCompile(srcProp, srcProp, value);
      } else {
        compiledStyle['$$css$localize'] = true;
      }
      compiledStyle[srcProp] = localizeableValue;
    }
  });
  return [compiledStyle, compiledRules];
}
function classic(style, name) {
  var compiledStyle = {
    $$css: true
  };
  var compiledRules = [];
  var animationKeyframes = style.animationKeyframes,
    rest = (0, _objectWithoutPropertiesLoose2.default)(style, _excluded);
  var identifier = createIdentifier('css', name, JSON.stringify(style));
  var selector = "." + identifier;
  var animationName;
  if (animationKeyframes != null) {
    var _processKeyframesValu = processKeyframesValue(animationKeyframes),
      animationNames = _processKeyframesValu[0],
      keyframesRules = _processKeyframesValu[1];
    animationName = animationNames.join(',');
    compiledRules.push.apply(compiledRules, (0, _toConsumableArray2.default)(keyframesRules));
  }
  var block = createDeclarationBlock((0, _objectSpread2.default)((0, _objectSpread2.default)({}, rest), {}, {
    animationName: animationName
  }));
  compiledRules.push("" + selector + block);
  compiledStyle[identifier] = identifier;
  return [compiledStyle, [[compiledRules, classicGroup]]];
}
function inline(originalStyle, isRTL) {
  var style = originalStyle || emptyObject;
  var frozenProps = {};
  var nextStyle = {};
  var _loop = function _loop() {
    var originalValue = style[originalProp];
    var prop = originalProp;
    var value = originalValue;
    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || originalValue == null) {
      return "continue";
    }
    if (PROPERTIES_VALUE.indexOf(originalProp) > -1) {
      if (originalValue === 'start') {
        value = isRTL ? 'right' : 'left';
      } else if (originalValue === 'end') {
        value = isRTL ? 'left' : 'right';
      }
    }
    var propPolyfill = PROPERTIES_I18N[originalProp];
    if (propPolyfill != null) {
      prop = isRTL ? PROPERTIES_FLIP[propPolyfill] : propPolyfill;
    }
    if (originalProp === 'transitionProperty') {
      var originalValues = Array.isArray(originalValue) ? originalValue : [originalValue];
      originalValues.forEach(function (val, i) {
        if (typeof val === 'string') {
          var valuePolyfill = PROPERTIES_I18N[val];
          if (valuePolyfill != null) {
            originalValues[i] = isRTL ? PROPERTIES_FLIP[valuePolyfill] : valuePolyfill;
            value = originalValues.join(' ');
          }
        }
      });
    }
    if (!frozenProps[prop]) {
      nextStyle[prop] = value;
    }
    if (prop === originalProp) {
      frozenProps[prop] = true;
    }
  };
  for (var originalProp in style) {
    var _ret = _loop();
    if (_ret === "continue") continue;
  }
  return (0, _createReactDOMStyle.default)(nextStyle, true);
}
function stringifyValueWithProperty(value, property) {
  var normalizedValue = (0, _normalizeValueWithProperty.default)(value, property);
  return typeof normalizedValue !== 'string' ? JSON.stringify(normalizedValue || '') : normalizedValue;
}
function createAtomicRules(identifier, property, value) {
  var rules = [];
  var selector = "." + identifier;
  switch (property) {
    case 'animationKeyframes':
      {
        var _processKeyframesValu2 = processKeyframesValue(value),
          animationNames = _processKeyframesValu2[0],
          keyframesRules = _processKeyframesValu2[1];
        var block = createDeclarationBlock({
          animationName: animationNames.join(',')
        });
        rules.push.apply(rules, ["" + selector + block].concat((0, _toConsumableArray2.default)(keyframesRules)));
        break;
      }
    case 'placeholderTextColor':
      {
        var _block = createDeclarationBlock({
          color: value,
          opacity: 1
        });
        rules.push(selector + "::-webkit-input-placeholder" + _block, selector + "::-moz-placeholder" + _block, selector + ":-ms-input-placeholder" + _block, selector + "::placeholder" + _block);
        break;
      }
    case 'pointerEvents':
      {
        var finalValue = value;
        if (value === 'auto' || value === 'box-only') {
          finalValue = 'auto!important';
          if (value === 'box-only') {
            var _block2 = createDeclarationBlock({
              pointerEvents: 'none'
            });
            rules.push(selector + ">*" + _block2);
          }
        } else if (value === 'none' || value === 'box-none') {
          finalValue = 'none!important';
          if (value === 'box-none') {
            var _block3 = createDeclarationBlock({
              pointerEvents: 'auto'
            });
            rules.push(selector + ">*" + _block3);
          }
        }
        var _block4 = createDeclarationBlock({
          pointerEvents: finalValue
        });
        rules.push("" + selector + _block4);
        break;
      }
    case 'scrollbarWidth':
      {
        if (value === 'none') {
          rules.push(selector + "::-webkit-scrollbar{display:none}");
        }
        var _block5 = createDeclarationBlock({
          scrollbarWidth: value
        });
        rules.push("" + selector + _block5);
        break;
      }
    default:
      {
        var _block6 = createDeclarationBlock((0, _defineProperty2.default)({}, property, value));
        rules.push("" + selector + _block6);
        break;
      }
  }
  return rules;
}
function createDeclarationBlock(style) {
  var domStyle = (0, _prefixStyles.default)((0, _createReactDOMStyle.default)(style));
  var declarationsString = Object.keys(domStyle).map(function (property) {
    var value = domStyle[property];
    var prop = (0, _hyphenateStyleName.default)(property);
    if (Array.isArray(value)) {
      return value.map(function (v) {
        return prop + ":" + v;
      }).join(';');
    } else {
      return prop + ":" + value;
    }
  }).sort().join(';');
  return "{" + declarationsString + ";}";
}
function createIdentifier(prefix, name, key) {
  var hashedString = (0, _hash.default)(name + key);
  return process.env.NODE_ENV !== 'production' ? prefix + "-" + name + "-" + hashedString : prefix + "-" + hashedString;
}
function createKeyframes(keyframes) {
  var prefixes = ['-webkit-', ''];
  var identifier = createIdentifier('r', 'animation', JSON.stringify(keyframes));
  var steps = '{' + Object.keys(keyframes).map(function (stepName) {
    var rule = keyframes[stepName];
    var block = createDeclarationBlock(rule);
    return "" + stepName + block;
  }).join('') + '}';
  var rules = prefixes.map(function (prefix) {
    return "@" + prefix + "keyframes " + identifier + steps;
  });
  return [identifier, rules];
}
function processKeyframesValue(keyframesValue) {
  if (typeof keyframesValue === 'number') {
    throw new Error("Invalid CSS keyframes type: " + typeof keyframesValue);
  }
  var animationNames = [];
  var rules = [];
  var value = Array.isArray(keyframesValue) ? keyframesValue : [keyframesValue];
  value.forEach(function (keyframes) {
    if (typeof keyframes === 'string') {
      animationNames.push(keyframes);
    } else {
      var _createKeyframes = createKeyframes(keyframes),
        identifier = _createKeyframes[0],
        keyframesRules = _createKeyframes[1];
      animationNames.push(identifier);
      rules.push.apply(rules, (0, _toConsumableArray2.default)(keyframesRules));
    }
  });
  return [animationNames, rules];
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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