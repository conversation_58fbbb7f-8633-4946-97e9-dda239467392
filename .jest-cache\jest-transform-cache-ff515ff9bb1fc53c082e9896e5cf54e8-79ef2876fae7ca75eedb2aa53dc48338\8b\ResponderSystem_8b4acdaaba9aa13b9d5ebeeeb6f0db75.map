{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "addNode", "attachListeners", "getResponderNode", "removeNode", "terminateResponder", "_createResponderEvent", "_ResponderEventTypes", "_utils", "_ResponderTouchHistoryStore", "_canUseDom", "emptyObject", "startRegistration", "bubbles", "moveRegistration", "scrollRegistration", "shouldSetResponderEvents", "touchstart", "mousedown", "touchmove", "mousemove", "scroll", "emptyResponder", "id", "idPath", "node", "responderListenersMap", "Map", "isEmulatingMouseEvents", "trackedTouchCount", "currentResponder", "responderTouchHistoryStore", "ResponderTouchHistoryStore", "changeCurrentResponder", "responder", "getResponderConfig", "config", "get", "eventListener", "domEvent", "eventType", "type", "eventTarget", "target", "isStartEvent", "isStartish", "isPrimaryPointerDown", "isMoveEvent", "isMoveish", "isEndEvent", "<PERSON><PERSON><PERSON><PERSON>", "isScrollEvent", "isScroll", "isSelectionChangeEvent", "isSelectionChange", "responderEvent", "touches", "length", "recordTouchTrack", "nativeEvent", "eventPaths", "getResponderPaths", "wasNegotiated", "wantsResponder", "currentResponderIdPath", "eventIdPath", "lowestCommonAncestor", "getLowestCommonAncestor", "indexOfLowestCommonAncestor", "indexOf", "index", "slice", "nodePath", "findWantsResponder", "attemptTransfer", "_currentResponder", "_getResponderConfig", "onResponderStart", "onResponderMove", "onResponderEnd", "onResponderRelease", "onResponderTerminate", "onResponderTerminationRequest", "cancelable", "currentTarget", "dispatchConfig", "registrationName", "isTerminateEvent", "isCancelish", "window", "contains", "relatedTarget", "hasValidSelection", "isReleaseEvent", "hasTargetTouches", "shouldTerminate", "shouldSetCallbacks", "shouldSetCallbackCaptureName", "shouldSetCallbackBubbleName", "check", "callback<PERSON><PERSON>", "shouldSetCallback", "pruned<PERSON>d<PERSON><PERSON>", "i", "result", "isPropagationStopped", "_i", "_id", "_node", "_result", "_id2", "_node2", "_currentResponder2", "currentId", "currentNode", "_getResponderConfig2", "onResponderGrant", "onResponderReject", "_getResponderConfig3", "allowTransfer", "documentEventsCapturePhase", "documentEventsBubblePhase", "__reactResponderSystemActive", "addEventListener", "for<PERSON>ach", "document", "setResponderId", "set", "has", "delete", "_currentResponder3", "_getResponderConfig4", "event"], "sources": ["ResponderSystem.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.addNode = addNode;\nexports.attachListeners = attachListeners;\nexports.getResponderNode = getResponderNode;\nexports.removeNode = removeNode;\nexports.terminateResponder = terminateResponder;\nvar _createResponderEvent = _interopRequireDefault(require(\"./createResponderEvent\"));\nvar _ResponderEventTypes = require(\"./ResponderEventTypes\");\nvar _utils = require(\"./utils\");\nvar _ResponderTouchHistoryStore = require(\"./ResponderTouchHistoryStore\");\nvar _canUseDom = _interopRequireDefault(require(\"../canUseDom\"));\n/**\n * Copyright (c) Nicolas Gallagher\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n/**\n * RESPONDER EVENT SYSTEM\n *\n * A single, global \"interaction lock\" on views. For a view to be the \"responder\" means\n * that pointer interactions are exclusive to that view and none other. The \"interaction\n * lock\" can be transferred (only) to ancestors of the current \"responder\" as long as\n * pointers continue to be active.\n *\n * Responder being granted:\n *\n * A view can become the \"responder\" after the following events:\n *  * \"pointerdown\" (implemented using \"touchstart\", \"mousedown\")\n *  * \"pointermove\" (implemented using \"touchmove\", \"mousemove\")\n *  * \"scroll\" (while a pointer is down)\n *  * \"selectionchange\" (while a pointer is down)\n *\n * If nothing is already the \"responder\", the event propagates to (capture) and from\n * (bubble) the event target until a view returns `true` for\n * `on*ShouldSetResponder(Capture)`.\n *\n * If something is already the responder, the event propagates to (capture) and from\n * (bubble) the lowest common ancestor of the event target and the current \"responder\".\n * Then negotiation happens between the current \"responder\" and a view that wants to\n * become the \"responder\": see the timing diagram below.\n *\n * (NOTE: Scrolled views either automatically become the \"responder\" or release the\n * \"interaction lock\". A native scroll view that isn't built on top of the responder\n * system must result in the current \"responder\" being notified that it no longer has\n * the \"interaction lock\" - the native system has taken over.\n *\n * Responder being released:\n *\n * As soon as there are no more active pointers that *started* inside descendants\n * of the *current* \"responder\", an `onResponderRelease` event is dispatched to the\n * current \"responder\", and the responder lock is released.\n *\n * Typical sequence of events:\n *  * startShouldSetResponder\n *  * responderGrant/Reject\n *  * responderStart\n *  * responderMove\n *  * responderEnd\n *  * responderRelease\n */\n\n/*                                             Negotiation Performed\n                                             +-----------------------+\n                                            /                         \\\nProcess low level events to    +     Current Responder      +   wantsResponderID\ndetermine who to perform negot-|   (if any exists at all)   |\niation/transition              | Otherwise just pass through|\n-------------------------------+----------------------------+------------------+\nBubble to find first ID        |                            |\nto return true:wantsResponderID|                            |\n                               |                            |\n     +--------------+          |                            |\n     | onTouchStart |          |                            |\n     +------+-------+    none  |                            |\n            |            return|                            |\n+-----------v-------------+true| +------------------------+ |\n|onStartShouldSetResponder|----->| onResponderStart (cur) |<-----------+\n+-----------+-------------+    | +------------------------+ |          |\n            |                  |                            | +--------+-------+\n            | returned true for|       false:REJECT +-------->|onResponderReject\n            | wantsResponderID |                    |       | +----------------+\n            | (now attempt     | +------------------+-----+ |\n            |  handoff)        | | onResponder            | |\n            +------------------->|    TerminationRequest  | |\n                               | +------------------+-----+ |\n                               |                    |       | +----------------+\n                               |         true:GRANT +-------->|onResponderGrant|\n                               |                            | +--------+-------+\n                               | +------------------------+ |          |\n                               | | onResponderTerminate   |<-----------+\n                               | +------------------+-----+ |\n                               |                    |       | +----------------+\n                               |                    +-------->|onResponderStart|\n                               |                            | +----------------+\nBubble to find first ID        |                            |\nto return true:wantsResponderID|                            |\n                               |                            |\n     +-------------+           |                            |\n     | onTouchMove |           |                            |\n     +------+------+     none  |                            |\n            |            return|                            |\n+-----------v-------------+true| +------------------------+ |\n|onMoveShouldSetResponder |----->| onResponderMove (cur)  |<-----------+\n+-----------+-------------+    | +------------------------+ |          |\n            |                  |                            | +--------+-------+\n            | returned true for|       false:REJECT +-------->|onResponderReject\n            | wantsResponderID |                    |       | +----------------+\n            | (now attempt     | +------------------+-----+ |\n            |  handoff)        | |   onResponder          | |\n            +------------------->|      TerminationRequest| |\n                               | +------------------+-----+ |\n                               |                    |       | +----------------+\n                               |         true:GRANT +-------->|onResponderGrant|\n                               |                            | +--------+-------+\n                               | +------------------------+ |          |\n                               | |   onResponderTerminate |<-----------+\n                               | +------------------+-----+ |\n                               |                    |       | +----------------+\n                               |                    +-------->|onResponderMove |\n                               |                            | +----------------+\n                               |                            |\n                               |                            |\n      Some active touch started|                            |\n      inside current responder | +------------------------+ |\n      +------------------------->|      onResponderEnd    | |\n      |                        | +------------------------+ |\n  +---+---------+              |                            |\n  | onTouchEnd  |              |                            |\n  +---+---------+              |                            |\n      |                        | +------------------------+ |\n      +------------------------->|     onResponderEnd     | |\n      No active touches started| +-----------+------------+ |\n      inside current responder |             |              |\n                               |             v              |\n                               | +------------------------+ |\n                               | |    onResponderRelease  | |\n                               | +------------------------+ |\n                               |                            |\n                               +                            + */\n\n/* ------------ TYPES ------------ */\n\nvar emptyObject = {};\n\n/* ------------ IMPLEMENTATION ------------ */\n\nvar startRegistration = ['onStartShouldSetResponderCapture', 'onStartShouldSetResponder', {\n  bubbles: true\n}];\nvar moveRegistration = ['onMoveShouldSetResponderCapture', 'onMoveShouldSetResponder', {\n  bubbles: true\n}];\nvar scrollRegistration = ['onScrollShouldSetResponderCapture', 'onScrollShouldSetResponder', {\n  bubbles: false\n}];\nvar shouldSetResponderEvents = {\n  touchstart: startRegistration,\n  mousedown: startRegistration,\n  touchmove: moveRegistration,\n  mousemove: moveRegistration,\n  scroll: scrollRegistration\n};\nvar emptyResponder = {\n  id: null,\n  idPath: null,\n  node: null\n};\nvar responderListenersMap = new Map();\nvar isEmulatingMouseEvents = false;\nvar trackedTouchCount = 0;\nvar currentResponder = {\n  id: null,\n  node: null,\n  idPath: null\n};\nvar responderTouchHistoryStore = new _ResponderTouchHistoryStore.ResponderTouchHistoryStore();\nfunction changeCurrentResponder(responder) {\n  currentResponder = responder;\n}\nfunction getResponderConfig(id) {\n  var config = responderListenersMap.get(id);\n  return config != null ? config : emptyObject;\n}\n\n/**\n * Process native events\n *\n * A single event listener is used to manage the responder system.\n * All pointers are tracked in the ResponderTouchHistoryStore. Native events\n * are interpreted in terms of the Responder System and checked to see if\n * the responder should be transferred. Each host node that is attached to\n * the Responder System has an ID, which is used to look up its associated\n * callbacks.\n */\nfunction eventListener(domEvent) {\n  var eventType = domEvent.type;\n  var eventTarget = domEvent.target;\n\n  /**\n   * Manage emulated events and early bailout.\n   * Since PointerEvent is not used yet (lack of support in older Safari), it's\n   * necessary to manually manage the mess of browser touch/mouse events.\n   * And bailout early for termination events when there is no active responder.\n   */\n\n  // Flag when browser may produce emulated events\n  if (eventType === 'touchstart') {\n    isEmulatingMouseEvents = true;\n  }\n  // Remove flag when browser will not produce emulated events\n  if (eventType === 'touchmove' || trackedTouchCount > 1) {\n    isEmulatingMouseEvents = false;\n  }\n  // Ignore various events in particular circumstances\n  if (\n  // Ignore browser emulated mouse events\n  eventType === 'mousedown' && isEmulatingMouseEvents || eventType === 'mousemove' && isEmulatingMouseEvents ||\n  // Ignore mousemove if a mousedown didn't occur first\n  eventType === 'mousemove' && trackedTouchCount < 1) {\n    return;\n  }\n  // Remove flag after emulated events are finished\n  if (isEmulatingMouseEvents && eventType === 'mouseup') {\n    if (trackedTouchCount === 0) {\n      isEmulatingMouseEvents = false;\n    }\n    return;\n  }\n  var isStartEvent = (0, _ResponderEventTypes.isStartish)(eventType) && (0, _utils.isPrimaryPointerDown)(domEvent);\n  var isMoveEvent = (0, _ResponderEventTypes.isMoveish)(eventType);\n  var isEndEvent = (0, _ResponderEventTypes.isEndish)(eventType);\n  var isScrollEvent = (0, _ResponderEventTypes.isScroll)(eventType);\n  var isSelectionChangeEvent = (0, _ResponderEventTypes.isSelectionChange)(eventType);\n  var responderEvent = (0, _createResponderEvent.default)(domEvent, responderTouchHistoryStore);\n\n  /**\n   * Record the state of active pointers\n   */\n\n  if (isStartEvent || isMoveEvent || isEndEvent) {\n    if (domEvent.touches) {\n      trackedTouchCount = domEvent.touches.length;\n    } else {\n      if (isStartEvent) {\n        trackedTouchCount = 1;\n      } else if (isEndEvent) {\n        trackedTouchCount = 0;\n      }\n    }\n    responderTouchHistoryStore.recordTouchTrack(eventType, responderEvent.nativeEvent);\n  }\n\n  /**\n   * Responder System logic\n   */\n\n  var eventPaths = (0, _utils.getResponderPaths)(domEvent);\n  var wasNegotiated = false;\n  var wantsResponder;\n\n  // If an event occured that might change the current responder...\n  if (isStartEvent || isMoveEvent || isScrollEvent && trackedTouchCount > 0) {\n    // If there is already a responder, prune the event paths to the lowest common ancestor\n    // of the existing responder and deepest target of the event.\n    var currentResponderIdPath = currentResponder.idPath;\n    var eventIdPath = eventPaths.idPath;\n    if (currentResponderIdPath != null && eventIdPath != null) {\n      var lowestCommonAncestor = (0, _utils.getLowestCommonAncestor)(currentResponderIdPath, eventIdPath);\n      if (lowestCommonAncestor != null) {\n        var indexOfLowestCommonAncestor = eventIdPath.indexOf(lowestCommonAncestor);\n        // Skip the current responder so it doesn't receive unexpected \"shouldSet\" events.\n        var index = indexOfLowestCommonAncestor + (lowestCommonAncestor === currentResponder.id ? 1 : 0);\n        eventPaths = {\n          idPath: eventIdPath.slice(index),\n          nodePath: eventPaths.nodePath.slice(index)\n        };\n      } else {\n        eventPaths = null;\n      }\n    }\n    if (eventPaths != null) {\n      // If a node wants to become the responder, attempt to transfer.\n      wantsResponder = findWantsResponder(eventPaths, domEvent, responderEvent);\n      if (wantsResponder != null) {\n        // Sets responder if none exists, or negotates with existing responder.\n        attemptTransfer(responderEvent, wantsResponder);\n        wasNegotiated = true;\n      }\n    }\n  }\n\n  // If there is now a responder, invoke its callbacks for the lifecycle of the gesture.\n  if (currentResponder.id != null && currentResponder.node != null) {\n    var _currentResponder = currentResponder,\n      id = _currentResponder.id,\n      node = _currentResponder.node;\n    var _getResponderConfig = getResponderConfig(id),\n      onResponderStart = _getResponderConfig.onResponderStart,\n      onResponderMove = _getResponderConfig.onResponderMove,\n      onResponderEnd = _getResponderConfig.onResponderEnd,\n      onResponderRelease = _getResponderConfig.onResponderRelease,\n      onResponderTerminate = _getResponderConfig.onResponderTerminate,\n      onResponderTerminationRequest = _getResponderConfig.onResponderTerminationRequest;\n    responderEvent.bubbles = false;\n    responderEvent.cancelable = false;\n    responderEvent.currentTarget = node;\n\n    // Start\n    if (isStartEvent) {\n      if (onResponderStart != null) {\n        responderEvent.dispatchConfig.registrationName = 'onResponderStart';\n        onResponderStart(responderEvent);\n      }\n    }\n    // Move\n    else if (isMoveEvent) {\n      if (onResponderMove != null) {\n        responderEvent.dispatchConfig.registrationName = 'onResponderMove';\n        onResponderMove(responderEvent);\n      }\n    } else {\n      var isTerminateEvent = (0, _ResponderEventTypes.isCancelish)(eventType) ||\n      // native context menu\n      eventType === 'contextmenu' ||\n      // window blur\n      eventType === 'blur' && eventTarget === window ||\n      // responder (or ancestors) blur\n      eventType === 'blur' && eventTarget.contains(node) && domEvent.relatedTarget !== node ||\n      // native scroll without using a pointer\n      isScrollEvent && trackedTouchCount === 0 ||\n      // native scroll on node that is parent of the responder (allow siblings to scroll)\n      isScrollEvent && eventTarget.contains(node) && eventTarget !== node ||\n      // native select/selectionchange on node\n      isSelectionChangeEvent && (0, _utils.hasValidSelection)(domEvent);\n      var isReleaseEvent = isEndEvent && !isTerminateEvent && !(0, _utils.hasTargetTouches)(node, domEvent.touches);\n\n      // End\n      if (isEndEvent) {\n        if (onResponderEnd != null) {\n          responderEvent.dispatchConfig.registrationName = 'onResponderEnd';\n          onResponderEnd(responderEvent);\n        }\n      }\n      // Release\n      if (isReleaseEvent) {\n        if (onResponderRelease != null) {\n          responderEvent.dispatchConfig.registrationName = 'onResponderRelease';\n          onResponderRelease(responderEvent);\n        }\n        changeCurrentResponder(emptyResponder);\n      }\n      // Terminate\n      if (isTerminateEvent) {\n        var shouldTerminate = true;\n\n        // Responders can still avoid termination but only for these events.\n        if (eventType === 'contextmenu' || eventType === 'scroll' || eventType === 'selectionchange') {\n          // Only call this function is it wasn't already called during negotiation.\n          if (wasNegotiated) {\n            shouldTerminate = false;\n          } else if (onResponderTerminationRequest != null) {\n            responderEvent.dispatchConfig.registrationName = 'onResponderTerminationRequest';\n            if (onResponderTerminationRequest(responderEvent) === false) {\n              shouldTerminate = false;\n            }\n          }\n        }\n        if (shouldTerminate) {\n          if (onResponderTerminate != null) {\n            responderEvent.dispatchConfig.registrationName = 'onResponderTerminate';\n            onResponderTerminate(responderEvent);\n          }\n          changeCurrentResponder(emptyResponder);\n          isEmulatingMouseEvents = false;\n          trackedTouchCount = 0;\n        }\n      }\n    }\n  }\n}\n\n/**\n * Walk the event path to/from the target node. At each node, stop and call the\n * relevant \"shouldSet\" functions for the given event type. If any of those functions\n * call \"stopPropagation\" on the event, stop searching for a responder.\n */\nfunction findWantsResponder(eventPaths, domEvent, responderEvent) {\n  var shouldSetCallbacks = shouldSetResponderEvents[domEvent.type]; // for Flow\n\n  if (shouldSetCallbacks != null) {\n    var idPath = eventPaths.idPath,\n      nodePath = eventPaths.nodePath;\n    var shouldSetCallbackCaptureName = shouldSetCallbacks[0];\n    var shouldSetCallbackBubbleName = shouldSetCallbacks[1];\n    var bubbles = shouldSetCallbacks[2].bubbles;\n    var check = function check(id, node, callbackName) {\n      var config = getResponderConfig(id);\n      var shouldSetCallback = config[callbackName];\n      if (shouldSetCallback != null) {\n        responderEvent.currentTarget = node;\n        if (shouldSetCallback(responderEvent) === true) {\n          // Start the path from the potential responder\n          var prunedIdPath = idPath.slice(idPath.indexOf(id));\n          return {\n            id,\n            node,\n            idPath: prunedIdPath\n          };\n        }\n      }\n    };\n\n    // capture\n    for (var i = idPath.length - 1; i >= 0; i--) {\n      var id = idPath[i];\n      var node = nodePath[i];\n      var result = check(id, node, shouldSetCallbackCaptureName);\n      if (result != null) {\n        return result;\n      }\n      if (responderEvent.isPropagationStopped() === true) {\n        return;\n      }\n    }\n\n    // bubble\n    if (bubbles) {\n      for (var _i = 0; _i < idPath.length; _i++) {\n        var _id = idPath[_i];\n        var _node = nodePath[_i];\n        var _result = check(_id, _node, shouldSetCallbackBubbleName);\n        if (_result != null) {\n          return _result;\n        }\n        if (responderEvent.isPropagationStopped() === true) {\n          return;\n        }\n      }\n    } else {\n      var _id2 = idPath[0];\n      var _node2 = nodePath[0];\n      var target = domEvent.target;\n      if (target === _node2) {\n        return check(_id2, _node2, shouldSetCallbackBubbleName);\n      }\n    }\n  }\n}\n\n/**\n * Attempt to transfer the responder.\n */\nfunction attemptTransfer(responderEvent, wantsResponder) {\n  var _currentResponder2 = currentResponder,\n    currentId = _currentResponder2.id,\n    currentNode = _currentResponder2.node;\n  var id = wantsResponder.id,\n    node = wantsResponder.node;\n  var _getResponderConfig2 = getResponderConfig(id),\n    onResponderGrant = _getResponderConfig2.onResponderGrant,\n    onResponderReject = _getResponderConfig2.onResponderReject;\n  responderEvent.bubbles = false;\n  responderEvent.cancelable = false;\n  responderEvent.currentTarget = node;\n\n  // Set responder\n  if (currentId == null) {\n    if (onResponderGrant != null) {\n      responderEvent.currentTarget = node;\n      responderEvent.dispatchConfig.registrationName = 'onResponderGrant';\n      onResponderGrant(responderEvent);\n    }\n    changeCurrentResponder(wantsResponder);\n  }\n  // Negotiate with current responder\n  else {\n    var _getResponderConfig3 = getResponderConfig(currentId),\n      onResponderTerminate = _getResponderConfig3.onResponderTerminate,\n      onResponderTerminationRequest = _getResponderConfig3.onResponderTerminationRequest;\n    var allowTransfer = true;\n    if (onResponderTerminationRequest != null) {\n      responderEvent.currentTarget = currentNode;\n      responderEvent.dispatchConfig.registrationName = 'onResponderTerminationRequest';\n      if (onResponderTerminationRequest(responderEvent) === false) {\n        allowTransfer = false;\n      }\n    }\n    if (allowTransfer) {\n      // Terminate existing responder\n      if (onResponderTerminate != null) {\n        responderEvent.currentTarget = currentNode;\n        responderEvent.dispatchConfig.registrationName = 'onResponderTerminate';\n        onResponderTerminate(responderEvent);\n      }\n      // Grant next responder\n      if (onResponderGrant != null) {\n        responderEvent.currentTarget = node;\n        responderEvent.dispatchConfig.registrationName = 'onResponderGrant';\n        onResponderGrant(responderEvent);\n      }\n      changeCurrentResponder(wantsResponder);\n    } else {\n      // Reject responder request\n      if (onResponderReject != null) {\n        responderEvent.currentTarget = node;\n        responderEvent.dispatchConfig.registrationName = 'onResponderReject';\n        onResponderReject(responderEvent);\n      }\n    }\n  }\n}\n\n/* ------------ PUBLIC API ------------ */\n\n/**\n * Attach Listeners\n *\n * Use native events as ReactDOM doesn't have a non-plugin API to implement\n * this system.\n */\nvar documentEventsCapturePhase = ['blur', 'scroll'];\nvar documentEventsBubblePhase = [\n// mouse\n'mousedown', 'mousemove', 'mouseup', 'dragstart',\n// touch\n'touchstart', 'touchmove', 'touchend', 'touchcancel',\n// other\n'contextmenu', 'select', 'selectionchange'];\nfunction attachListeners() {\n  if (_canUseDom.default && window.__reactResponderSystemActive == null) {\n    window.addEventListener('blur', eventListener);\n    documentEventsBubblePhase.forEach(eventType => {\n      document.addEventListener(eventType, eventListener);\n    });\n    documentEventsCapturePhase.forEach(eventType => {\n      document.addEventListener(eventType, eventListener, true);\n    });\n    window.__reactResponderSystemActive = true;\n  }\n}\n\n/**\n * Register a node with the ResponderSystem.\n */\nfunction addNode(id, node, config) {\n  (0, _utils.setResponderId)(node, id);\n  responderListenersMap.set(id, config);\n}\n\n/**\n * Unregister a node with the ResponderSystem.\n */\nfunction removeNode(id) {\n  if (currentResponder.id === id) {\n    terminateResponder();\n  }\n  if (responderListenersMap.has(id)) {\n    responderListenersMap.delete(id);\n  }\n}\n\n/**\n * Allow the current responder to be terminated from within components to support\n * more complex requirements, such as use with other React libraries for working\n * with scroll views, input views, etc.\n */\nfunction terminateResponder() {\n  var _currentResponder3 = currentResponder,\n    id = _currentResponder3.id,\n    node = _currentResponder3.node;\n  if (id != null && node != null) {\n    var _getResponderConfig4 = getResponderConfig(id),\n      onResponderTerminate = _getResponderConfig4.onResponderTerminate;\n    if (onResponderTerminate != null) {\n      var event = (0, _createResponderEvent.default)({}, responderTouchHistoryStore);\n      event.currentTarget = node;\n      onResponderTerminate(event);\n    }\n    changeCurrentResponder(emptyResponder);\n  }\n  isEmulatingMouseEvents = false;\n  trackedTouchCount = 0;\n}\n\n/**\n * Allow unit tests to inspect the current responder in the system.\n * FOR TESTING ONLY.\n */\nfunction getResponderNode() {\n  return currentResponder.node;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGA,OAAO;AACzBF,OAAO,CAACG,eAAe,GAAGA,eAAe;AACzCH,OAAO,CAACI,gBAAgB,GAAGA,gBAAgB;AAC3CJ,OAAO,CAACK,UAAU,GAAGA,UAAU;AAC/BL,OAAO,CAACM,kBAAkB,GAAGA,kBAAkB;AAC/C,IAAIC,qBAAqB,GAAGV,sBAAsB,CAACC,OAAO,yBAAyB,CAAC,CAAC;AACrF,IAAIU,oBAAoB,GAAGV,OAAO,wBAAwB,CAAC;AAC3D,IAAIW,MAAM,GAAGX,OAAO,UAAU,CAAC;AAC/B,IAAIY,2BAA2B,GAAGZ,OAAO,+BAA+B,CAAC;AACzE,IAAIa,UAAU,GAAGd,sBAAsB,CAACC,OAAO,eAAe,CAAC,CAAC;AAwIhE,IAAIc,WAAW,GAAG,CAAC,CAAC;AAIpB,IAAIC,iBAAiB,GAAG,CAAC,kCAAkC,EAAE,2BAA2B,EAAE;EACxFC,OAAO,EAAE;AACX,CAAC,CAAC;AACF,IAAIC,gBAAgB,GAAG,CAAC,iCAAiC,EAAE,0BAA0B,EAAE;EACrFD,OAAO,EAAE;AACX,CAAC,CAAC;AACF,IAAIE,kBAAkB,GAAG,CAAC,mCAAmC,EAAE,4BAA4B,EAAE;EAC3FF,OAAO,EAAE;AACX,CAAC,CAAC;AACF,IAAIG,wBAAwB,GAAG;EAC7BC,UAAU,EAAEL,iBAAiB;EAC7BM,SAAS,EAAEN,iBAAiB;EAC5BO,SAAS,EAAEL,gBAAgB;EAC3BM,SAAS,EAAEN,gBAAgB;EAC3BO,MAAM,EAAEN;AACV,CAAC;AACD,IAAIO,cAAc,GAAG;EACnBC,EAAE,EAAE,IAAI;EACRC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,qBAAqB,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrC,IAAIC,sBAAsB,GAAG,KAAK;AAClC,IAAIC,iBAAiB,GAAG,CAAC;AACzB,IAAIC,gBAAgB,GAAG;EACrBP,EAAE,EAAE,IAAI;EACRE,IAAI,EAAE,IAAI;EACVD,MAAM,EAAE;AACV,CAAC;AACD,IAAIO,0BAA0B,GAAG,IAAItB,2BAA2B,CAACuB,0BAA0B,CAAC,CAAC;AAC7F,SAASC,sBAAsBA,CAACC,SAAS,EAAE;EACzCJ,gBAAgB,GAAGI,SAAS;AAC9B;AACA,SAASC,kBAAkBA,CAACZ,EAAE,EAAE;EAC9B,IAAIa,MAAM,GAAGV,qBAAqB,CAACW,GAAG,CAACd,EAAE,CAAC;EAC1C,OAAOa,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGzB,WAAW;AAC9C;AAYA,SAAS2B,aAAaA,CAACC,QAAQ,EAAE;EAC/B,IAAIC,SAAS,GAAGD,QAAQ,CAACE,IAAI;EAC7B,IAAIC,WAAW,GAAGH,QAAQ,CAACI,MAAM;EAUjC,IAAIH,SAAS,KAAK,YAAY,EAAE;IAC9BZ,sBAAsB,GAAG,IAAI;EAC/B;EAEA,IAAIY,SAAS,KAAK,WAAW,IAAIX,iBAAiB,GAAG,CAAC,EAAE;IACtDD,sBAAsB,GAAG,KAAK;EAChC;EAEA,IAEAY,SAAS,KAAK,WAAW,IAAIZ,sBAAsB,IAAIY,SAAS,KAAK,WAAW,IAAIZ,sBAAsB,IAE1GY,SAAS,KAAK,WAAW,IAAIX,iBAAiB,GAAG,CAAC,EAAE;IAClD;EACF;EAEA,IAAID,sBAAsB,IAAIY,SAAS,KAAK,SAAS,EAAE;IACrD,IAAIX,iBAAiB,KAAK,CAAC,EAAE;MAC3BD,sBAAsB,GAAG,KAAK;IAChC;IACA;EACF;EACA,IAAIgB,YAAY,GAAG,CAAC,CAAC,EAAErC,oBAAoB,CAACsC,UAAU,EAAEL,SAAS,CAAC,IAAI,CAAC,CAAC,EAAEhC,MAAM,CAACsC,oBAAoB,EAAEP,QAAQ,CAAC;EAChH,IAAIQ,WAAW,GAAG,CAAC,CAAC,EAAExC,oBAAoB,CAACyC,SAAS,EAAER,SAAS,CAAC;EAChE,IAAIS,UAAU,GAAG,CAAC,CAAC,EAAE1C,oBAAoB,CAAC2C,QAAQ,EAAEV,SAAS,CAAC;EAC9D,IAAIW,aAAa,GAAG,CAAC,CAAC,EAAE5C,oBAAoB,CAAC6C,QAAQ,EAAEZ,SAAS,CAAC;EACjE,IAAIa,sBAAsB,GAAG,CAAC,CAAC,EAAE9C,oBAAoB,CAAC+C,iBAAiB,EAAEd,SAAS,CAAC;EACnF,IAAIe,cAAc,GAAG,CAAC,CAAC,EAAEjD,qBAAqB,CAACR,OAAO,EAAEyC,QAAQ,EAAER,0BAA0B,CAAC;EAM7F,IAAIa,YAAY,IAAIG,WAAW,IAAIE,UAAU,EAAE;IAC7C,IAAIV,QAAQ,CAACiB,OAAO,EAAE;MACpB3B,iBAAiB,GAAGU,QAAQ,CAACiB,OAAO,CAACC,MAAM;IAC7C,CAAC,MAAM;MACL,IAAIb,YAAY,EAAE;QAChBf,iBAAiB,GAAG,CAAC;MACvB,CAAC,MAAM,IAAIoB,UAAU,EAAE;QACrBpB,iBAAiB,GAAG,CAAC;MACvB;IACF;IACAE,0BAA0B,CAAC2B,gBAAgB,CAAClB,SAAS,EAAEe,cAAc,CAACI,WAAW,CAAC;EACpF;EAMA,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAEpD,MAAM,CAACqD,iBAAiB,EAAEtB,QAAQ,CAAC;EACxD,IAAIuB,aAAa,GAAG,KAAK;EACzB,IAAIC,cAAc;EAGlB,IAAInB,YAAY,IAAIG,WAAW,IAAII,aAAa,IAAItB,iBAAiB,GAAG,CAAC,EAAE;IAGzE,IAAImC,sBAAsB,GAAGlC,gBAAgB,CAACN,MAAM;IACpD,IAAIyC,WAAW,GAAGL,UAAU,CAACpC,MAAM;IACnC,IAAIwC,sBAAsB,IAAI,IAAI,IAAIC,WAAW,IAAI,IAAI,EAAE;MACzD,IAAIC,oBAAoB,GAAG,CAAC,CAAC,EAAE1D,MAAM,CAAC2D,uBAAuB,EAAEH,sBAAsB,EAAEC,WAAW,CAAC;MACnG,IAAIC,oBAAoB,IAAI,IAAI,EAAE;QAChC,IAAIE,2BAA2B,GAAGH,WAAW,CAACI,OAAO,CAACH,oBAAoB,CAAC;QAE3E,IAAII,KAAK,GAAGF,2BAA2B,IAAIF,oBAAoB,KAAKpC,gBAAgB,CAACP,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChGqC,UAAU,GAAG;UACXpC,MAAM,EAAEyC,WAAW,CAACM,KAAK,CAACD,KAAK,CAAC;UAChCE,QAAQ,EAAEZ,UAAU,CAACY,QAAQ,CAACD,KAAK,CAACD,KAAK;QAC3C,CAAC;MACH,CAAC,MAAM;QACLV,UAAU,GAAG,IAAI;MACnB;IACF;IACA,IAAIA,UAAU,IAAI,IAAI,EAAE;MAEtBG,cAAc,GAAGU,kBAAkB,CAACb,UAAU,EAAErB,QAAQ,EAAEgB,cAAc,CAAC;MACzE,IAAIQ,cAAc,IAAI,IAAI,EAAE;QAE1BW,eAAe,CAACnB,cAAc,EAAEQ,cAAc,CAAC;QAC/CD,aAAa,GAAG,IAAI;MACtB;IACF;EACF;EAGA,IAAIhC,gBAAgB,CAACP,EAAE,IAAI,IAAI,IAAIO,gBAAgB,CAACL,IAAI,IAAI,IAAI,EAAE;IAChE,IAAIkD,iBAAiB,GAAG7C,gBAAgB;MACtCP,EAAE,GAAGoD,iBAAiB,CAACpD,EAAE;MACzBE,IAAI,GAAGkD,iBAAiB,CAAClD,IAAI;IAC/B,IAAImD,mBAAmB,GAAGzC,kBAAkB,CAACZ,EAAE,CAAC;MAC9CsD,gBAAgB,GAAGD,mBAAmB,CAACC,gBAAgB;MACvDC,eAAe,GAAGF,mBAAmB,CAACE,eAAe;MACrDC,cAAc,GAAGH,mBAAmB,CAACG,cAAc;MACnDC,kBAAkB,GAAGJ,mBAAmB,CAACI,kBAAkB;MAC3DC,oBAAoB,GAAGL,mBAAmB,CAACK,oBAAoB;MAC/DC,6BAA6B,GAAGN,mBAAmB,CAACM,6BAA6B;IACnF3B,cAAc,CAAC1C,OAAO,GAAG,KAAK;IAC9B0C,cAAc,CAAC4B,UAAU,GAAG,KAAK;IACjC5B,cAAc,CAAC6B,aAAa,GAAG3D,IAAI;IAGnC,IAAImB,YAAY,EAAE;MAChB,IAAIiC,gBAAgB,IAAI,IAAI,EAAE;QAC5BtB,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,kBAAkB;QACnET,gBAAgB,CAACtB,cAAc,CAAC;MAClC;IACF,CAAC,MAEI,IAAIR,WAAW,EAAE;MACpB,IAAI+B,eAAe,IAAI,IAAI,EAAE;QAC3BvB,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,iBAAiB;QAClER,eAAe,CAACvB,cAAc,CAAC;MACjC;IACF,CAAC,MAAM;MACL,IAAIgC,gBAAgB,GAAG,CAAC,CAAC,EAAEhF,oBAAoB,CAACiF,WAAW,EAAEhD,SAAS,CAAC,IAEvEA,SAAS,KAAK,aAAa,IAE3BA,SAAS,KAAK,MAAM,IAAIE,WAAW,KAAK+C,MAAM,IAE9CjD,SAAS,KAAK,MAAM,IAAIE,WAAW,CAACgD,QAAQ,CAACjE,IAAI,CAAC,IAAIc,QAAQ,CAACoD,aAAa,KAAKlE,IAAI,IAErF0B,aAAa,IAAItB,iBAAiB,KAAK,CAAC,IAExCsB,aAAa,IAAIT,WAAW,CAACgD,QAAQ,CAACjE,IAAI,CAAC,IAAIiB,WAAW,KAAKjB,IAAI,IAEnE4B,sBAAsB,IAAI,CAAC,CAAC,EAAE7C,MAAM,CAACoF,iBAAiB,EAAErD,QAAQ,CAAC;MACjE,IAAIsD,cAAc,GAAG5C,UAAU,IAAI,CAACsC,gBAAgB,IAAI,CAAC,CAAC,CAAC,EAAE/E,MAAM,CAACsF,gBAAgB,EAAErE,IAAI,EAAEc,QAAQ,CAACiB,OAAO,CAAC;MAG7G,IAAIP,UAAU,EAAE;QACd,IAAI8B,cAAc,IAAI,IAAI,EAAE;UAC1BxB,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,gBAAgB;UACjEP,cAAc,CAACxB,cAAc,CAAC;QAChC;MACF;MAEA,IAAIsC,cAAc,EAAE;QAClB,IAAIb,kBAAkB,IAAI,IAAI,EAAE;UAC9BzB,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,oBAAoB;UACrEN,kBAAkB,CAACzB,cAAc,CAAC;QACpC;QACAtB,sBAAsB,CAACX,cAAc,CAAC;MACxC;MAEA,IAAIiE,gBAAgB,EAAE;QACpB,IAAIQ,eAAe,GAAG,IAAI;QAG1B,IAAIvD,SAAS,KAAK,aAAa,IAAIA,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,iBAAiB,EAAE;UAE5F,IAAIsB,aAAa,EAAE;YACjBiC,eAAe,GAAG,KAAK;UACzB,CAAC,MAAM,IAAIb,6BAA6B,IAAI,IAAI,EAAE;YAChD3B,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,+BAA+B;YAChF,IAAIJ,6BAA6B,CAAC3B,cAAc,CAAC,KAAK,KAAK,EAAE;cAC3DwC,eAAe,GAAG,KAAK;YACzB;UACF;QACF;QACA,IAAIA,eAAe,EAAE;UACnB,IAAId,oBAAoB,IAAI,IAAI,EAAE;YAChC1B,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,sBAAsB;YACvEL,oBAAoB,CAAC1B,cAAc,CAAC;UACtC;UACAtB,sBAAsB,CAACX,cAAc,CAAC;UACtCM,sBAAsB,GAAG,KAAK;UAC9BC,iBAAiB,GAAG,CAAC;QACvB;MACF;IACF;EACF;AACF;AAOA,SAAS4C,kBAAkBA,CAACb,UAAU,EAAErB,QAAQ,EAAEgB,cAAc,EAAE;EAChE,IAAIyC,kBAAkB,GAAGhF,wBAAwB,CAACuB,QAAQ,CAACE,IAAI,CAAC;EAEhE,IAAIuD,kBAAkB,IAAI,IAAI,EAAE;IAC9B,IAAIxE,MAAM,GAAGoC,UAAU,CAACpC,MAAM;MAC5BgD,QAAQ,GAAGZ,UAAU,CAACY,QAAQ;IAChC,IAAIyB,4BAA4B,GAAGD,kBAAkB,CAAC,CAAC,CAAC;IACxD,IAAIE,2BAA2B,GAAGF,kBAAkB,CAAC,CAAC,CAAC;IACvD,IAAInF,OAAO,GAAGmF,kBAAkB,CAAC,CAAC,CAAC,CAACnF,OAAO;IAC3C,IAAIsF,KAAK,GAAG,SAASA,KAAKA,CAAC5E,EAAE,EAAEE,IAAI,EAAE2E,YAAY,EAAE;MACjD,IAAIhE,MAAM,GAAGD,kBAAkB,CAACZ,EAAE,CAAC;MACnC,IAAI8E,iBAAiB,GAAGjE,MAAM,CAACgE,YAAY,CAAC;MAC5C,IAAIC,iBAAiB,IAAI,IAAI,EAAE;QAC7B9C,cAAc,CAAC6B,aAAa,GAAG3D,IAAI;QACnC,IAAI4E,iBAAiB,CAAC9C,cAAc,CAAC,KAAK,IAAI,EAAE;UAE9C,IAAI+C,YAAY,GAAG9E,MAAM,CAAC+C,KAAK,CAAC/C,MAAM,CAAC6C,OAAO,CAAC9C,EAAE,CAAC,CAAC;UACnD,OAAO;YACLA,EAAE,EAAFA,EAAE;YACFE,IAAI,EAAJA,IAAI;YACJD,MAAM,EAAE8E;UACV,CAAC;QACH;MACF;IACF,CAAC;IAGD,KAAK,IAAIC,CAAC,GAAG/E,MAAM,CAACiC,MAAM,GAAG,CAAC,EAAE8C,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC3C,IAAIhF,EAAE,GAAGC,MAAM,CAAC+E,CAAC,CAAC;MAClB,IAAI9E,IAAI,GAAG+C,QAAQ,CAAC+B,CAAC,CAAC;MACtB,IAAIC,MAAM,GAAGL,KAAK,CAAC5E,EAAE,EAAEE,IAAI,EAAEwE,4BAA4B,CAAC;MAC1D,IAAIO,MAAM,IAAI,IAAI,EAAE;QAClB,OAAOA,MAAM;MACf;MACA,IAAIjD,cAAc,CAACkD,oBAAoB,CAAC,CAAC,KAAK,IAAI,EAAE;QAClD;MACF;IACF;IAGA,IAAI5F,OAAO,EAAE;MACX,KAAK,IAAI6F,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGlF,MAAM,CAACiC,MAAM,EAAEiD,EAAE,EAAE,EAAE;QACzC,IAAIC,GAAG,GAAGnF,MAAM,CAACkF,EAAE,CAAC;QACpB,IAAIE,KAAK,GAAGpC,QAAQ,CAACkC,EAAE,CAAC;QACxB,IAAIG,OAAO,GAAGV,KAAK,CAACQ,GAAG,EAAEC,KAAK,EAAEV,2BAA2B,CAAC;QAC5D,IAAIW,OAAO,IAAI,IAAI,EAAE;UACnB,OAAOA,OAAO;QAChB;QACA,IAAItD,cAAc,CAACkD,oBAAoB,CAAC,CAAC,KAAK,IAAI,EAAE;UAClD;QACF;MACF;IACF,CAAC,MAAM;MACL,IAAIK,IAAI,GAAGtF,MAAM,CAAC,CAAC,CAAC;MACpB,IAAIuF,MAAM,GAAGvC,QAAQ,CAAC,CAAC,CAAC;MACxB,IAAI7B,MAAM,GAAGJ,QAAQ,CAACI,MAAM;MAC5B,IAAIA,MAAM,KAAKoE,MAAM,EAAE;QACrB,OAAOZ,KAAK,CAACW,IAAI,EAAEC,MAAM,EAAEb,2BAA2B,CAAC;MACzD;IACF;EACF;AACF;AAKA,SAASxB,eAAeA,CAACnB,cAAc,EAAEQ,cAAc,EAAE;EACvD,IAAIiD,kBAAkB,GAAGlF,gBAAgB;IACvCmF,SAAS,GAAGD,kBAAkB,CAACzF,EAAE;IACjC2F,WAAW,GAAGF,kBAAkB,CAACvF,IAAI;EACvC,IAAIF,EAAE,GAAGwC,cAAc,CAACxC,EAAE;IACxBE,IAAI,GAAGsC,cAAc,CAACtC,IAAI;EAC5B,IAAI0F,oBAAoB,GAAGhF,kBAAkB,CAACZ,EAAE,CAAC;IAC/C6F,gBAAgB,GAAGD,oBAAoB,CAACC,gBAAgB;IACxDC,iBAAiB,GAAGF,oBAAoB,CAACE,iBAAiB;EAC5D9D,cAAc,CAAC1C,OAAO,GAAG,KAAK;EAC9B0C,cAAc,CAAC4B,UAAU,GAAG,KAAK;EACjC5B,cAAc,CAAC6B,aAAa,GAAG3D,IAAI;EAGnC,IAAIwF,SAAS,IAAI,IAAI,EAAE;IACrB,IAAIG,gBAAgB,IAAI,IAAI,EAAE;MAC5B7D,cAAc,CAAC6B,aAAa,GAAG3D,IAAI;MACnC8B,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,kBAAkB;MACnE8B,gBAAgB,CAAC7D,cAAc,CAAC;IAClC;IACAtB,sBAAsB,CAAC8B,cAAc,CAAC;EACxC,CAAC,MAEI;IACH,IAAIuD,oBAAoB,GAAGnF,kBAAkB,CAAC8E,SAAS,CAAC;MACtDhC,oBAAoB,GAAGqC,oBAAoB,CAACrC,oBAAoB;MAChEC,6BAA6B,GAAGoC,oBAAoB,CAACpC,6BAA6B;IACpF,IAAIqC,aAAa,GAAG,IAAI;IACxB,IAAIrC,6BAA6B,IAAI,IAAI,EAAE;MACzC3B,cAAc,CAAC6B,aAAa,GAAG8B,WAAW;MAC1C3D,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,+BAA+B;MAChF,IAAIJ,6BAA6B,CAAC3B,cAAc,CAAC,KAAK,KAAK,EAAE;QAC3DgE,aAAa,GAAG,KAAK;MACvB;IACF;IACA,IAAIA,aAAa,EAAE;MAEjB,IAAItC,oBAAoB,IAAI,IAAI,EAAE;QAChC1B,cAAc,CAAC6B,aAAa,GAAG8B,WAAW;QAC1C3D,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,sBAAsB;QACvEL,oBAAoB,CAAC1B,cAAc,CAAC;MACtC;MAEA,IAAI6D,gBAAgB,IAAI,IAAI,EAAE;QAC5B7D,cAAc,CAAC6B,aAAa,GAAG3D,IAAI;QACnC8B,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,kBAAkB;QACnE8B,gBAAgB,CAAC7D,cAAc,CAAC;MAClC;MACAtB,sBAAsB,CAAC8B,cAAc,CAAC;IACxC,CAAC,MAAM;MAEL,IAAIsD,iBAAiB,IAAI,IAAI,EAAE;QAC7B9D,cAAc,CAAC6B,aAAa,GAAG3D,IAAI;QACnC8B,cAAc,CAAC8B,cAAc,CAACC,gBAAgB,GAAG,mBAAmB;QACpE+B,iBAAiB,CAAC9D,cAAc,CAAC;MACnC;IACF;EACF;AACF;AAUA,IAAIiE,0BAA0B,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;AACnD,IAAIC,yBAAyB,GAAG,CAEhC,WAAW,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAEhD,YAAY,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAEpD,aAAa,EAAE,QAAQ,EAAE,iBAAiB,CAAC;AAC3C,SAASvH,eAAeA,CAAA,EAAG;EACzB,IAAIQ,UAAU,CAACZ,OAAO,IAAI2F,MAAM,CAACiC,4BAA4B,IAAI,IAAI,EAAE;IACrEjC,MAAM,CAACkC,gBAAgB,CAAC,MAAM,EAAErF,aAAa,CAAC;IAC9CmF,yBAAyB,CAACG,OAAO,CAAC,UAAApF,SAAS,EAAI;MAC7CqF,QAAQ,CAACF,gBAAgB,CAACnF,SAAS,EAAEF,aAAa,CAAC;IACrD,CAAC,CAAC;IACFkF,0BAA0B,CAACI,OAAO,CAAC,UAAApF,SAAS,EAAI;MAC9CqF,QAAQ,CAACF,gBAAgB,CAACnF,SAAS,EAAEF,aAAa,EAAE,IAAI,CAAC;IAC3D,CAAC,CAAC;IACFmD,MAAM,CAACiC,4BAA4B,GAAG,IAAI;EAC5C;AACF;AAKA,SAASzH,OAAOA,CAACsB,EAAE,EAAEE,IAAI,EAAEW,MAAM,EAAE;EACjC,CAAC,CAAC,EAAE5B,MAAM,CAACsH,cAAc,EAAErG,IAAI,EAAEF,EAAE,CAAC;EACpCG,qBAAqB,CAACqG,GAAG,CAACxG,EAAE,EAAEa,MAAM,CAAC;AACvC;AAKA,SAAShC,UAAUA,CAACmB,EAAE,EAAE;EACtB,IAAIO,gBAAgB,CAACP,EAAE,KAAKA,EAAE,EAAE;IAC9BlB,kBAAkB,CAAC,CAAC;EACtB;EACA,IAAIqB,qBAAqB,CAACsG,GAAG,CAACzG,EAAE,CAAC,EAAE;IACjCG,qBAAqB,CAACuG,MAAM,CAAC1G,EAAE,CAAC;EAClC;AACF;AAOA,SAASlB,kBAAkBA,CAAA,EAAG;EAC5B,IAAI6H,kBAAkB,GAAGpG,gBAAgB;IACvCP,EAAE,GAAG2G,kBAAkB,CAAC3G,EAAE;IAC1BE,IAAI,GAAGyG,kBAAkB,CAACzG,IAAI;EAChC,IAAIF,EAAE,IAAI,IAAI,IAAIE,IAAI,IAAI,IAAI,EAAE;IAC9B,IAAI0G,oBAAoB,GAAGhG,kBAAkB,CAACZ,EAAE,CAAC;MAC/C0D,oBAAoB,GAAGkD,oBAAoB,CAAClD,oBAAoB;IAClE,IAAIA,oBAAoB,IAAI,IAAI,EAAE;MAChC,IAAImD,KAAK,GAAG,CAAC,CAAC,EAAE9H,qBAAqB,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEiC,0BAA0B,CAAC;MAC9EqG,KAAK,CAAChD,aAAa,GAAG3D,IAAI;MAC1BwD,oBAAoB,CAACmD,KAAK,CAAC;IAC7B;IACAnG,sBAAsB,CAACX,cAAc,CAAC;EACxC;EACAM,sBAAsB,GAAG,KAAK;EAC9BC,iBAAiB,GAAG,CAAC;AACvB;AAMA,SAAS1B,gBAAgBA,CAAA,EAAG;EAC1B,OAAO2B,gBAAgB,CAACL,IAAI;AAC9B", "ignoreList": []}