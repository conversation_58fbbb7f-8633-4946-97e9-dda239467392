774e1711eaf75b0207a0f91fc9f59ff8
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _AnimatedEvent = require("./AnimatedEvent");
var _AnimatedImplementation = _interopRequireDefault(require("./AnimatedImplementation"));
var _AnimatedInterpolation = _interopRequireDefault(require("./nodes/AnimatedInterpolation"));
var _AnimatedNode = _interopRequireDefault(require("./nodes/AnimatedNode"));
var _AnimatedValue = _interopRequireDefault(require("./nodes/AnimatedValue"));
var _AnimatedValueXY = _interopRequireDefault(require("./nodes/AnimatedValueXY"));
var _createAnimatedComponent = _interopRequireDefault(require("./createAnimatedComponent"));
var _AnimatedColor = _interopRequireDefault(require("./nodes/AnimatedColor"));
var inAnimationCallback = false;
function mockAnimationStart(start) {
  return function (callback) {
    var guardedCallback = callback == null ? callback : function () {
      if (inAnimationCallback) {
        console.warn('Ignoring recursive animation callback when running mock animations');
        return;
      }
      inAnimationCallback = true;
      try {
        callback.apply(void 0, arguments);
      } finally {
        inAnimationCallback = false;
      }
    };
    start(guardedCallback);
  };
}
var emptyAnimation = {
  start: function start() {},
  stop: function stop() {},
  reset: function reset() {},
  _startNativeLoop: function _startNativeLoop() {},
  _isUsingNativeDriver: function _isUsingNativeDriver() {
    return false;
  }
};
var mockCompositeAnimation = function mockCompositeAnimation(animations) {
  return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, emptyAnimation), {}, {
    start: mockAnimationStart(function (callback) {
      animations.forEach(function (animation) {
        return animation.start();
      });
      callback == null ? void 0 : callback({
        finished: true
      });
    })
  });
};
var spring = function spring(value, config) {
  var anyValue = value;
  return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, emptyAnimation), {}, {
    start: mockAnimationStart(function (callback) {
      anyValue.setValue(config.toValue);
      callback == null ? void 0 : callback({
        finished: true
      });
    })
  });
};
var timing = function timing(value, config) {
  var anyValue = value;
  return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, emptyAnimation), {}, {
    start: mockAnimationStart(function (callback) {
      anyValue.setValue(config.toValue);
      callback == null ? void 0 : callback({
        finished: true
      });
    })
  });
};
var decay = function decay(value, config) {
  return emptyAnimation;
};
var sequence = function sequence(animations) {
  return mockCompositeAnimation(animations);
};
var parallel = function parallel(animations, config) {
  return mockCompositeAnimation(animations);
};
var delay = function delay(time) {
  return emptyAnimation;
};
var stagger = function stagger(time, animations) {
  return mockCompositeAnimation(animations);
};
var loop = function loop(animation, _temp) {
  var _ref = _temp === void 0 ? {} : _temp,
    _ref$iterations = _ref.iterations,
    iterations = _ref$iterations === void 0 ? -1 : _ref$iterations;
  return emptyAnimation;
};
var _default = exports.default = {
  Value: _AnimatedValue.default,
  ValueXY: _AnimatedValueXY.default,
  Color: _AnimatedColor.default,
  Interpolation: _AnimatedInterpolation.default,
  Node: _AnimatedNode.default,
  decay: decay,
  timing: timing,
  spring: spring,
  add: _AnimatedImplementation.default.add,
  subtract: _AnimatedImplementation.default.subtract,
  divide: _AnimatedImplementation.default.divide,
  multiply: _AnimatedImplementation.default.multiply,
  modulo: _AnimatedImplementation.default.modulo,
  diffClamp: _AnimatedImplementation.default.diffClamp,
  delay: delay,
  sequence: sequence,
  parallel: parallel,
  stagger: stagger,
  loop: loop,
  event: _AnimatedImplementation.default.event,
  createAnimatedComponent: _createAnimatedComponent.default,
  attachNativeEvent: _AnimatedEvent.attachNativeEvent,
  forkEvent: _AnimatedImplementation.default.forkEvent,
  unforkEvent: _AnimatedImplementation.default.unforkEvent,
  Event: _AnimatedEvent.AnimatedEvent
};
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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