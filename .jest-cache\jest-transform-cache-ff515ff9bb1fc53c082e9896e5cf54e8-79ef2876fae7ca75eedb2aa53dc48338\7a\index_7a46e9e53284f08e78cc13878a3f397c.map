{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_interopRequireDefault", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_createForOfIteratorHelperLoose2", "_objectWithoutPropertiesLoose2", "_objectSpread2", "_View", "_VirtualizedList", "_VirtualizeUtils", "_invariant", "React", "_excluded", "VirtualizedSectionList", "_React$PureComponent", "_this", "arguments", "_keyExtractor", "item", "index", "info", "_subExtractor", "key", "String", "_convertViewable", "viewable", "_info$index", "keyExtractorWithNullableIndex", "section", "keyExtractor", "keyExtractorWithNonNullableIndex", "props", "_onViewableItemsChanged", "_ref", "viewableItems", "changed", "onViewableItemsChanged", "map", "filter", "_renderItem", "listItemCount", "_ref2", "infoIndex", "header", "renderSectionHeader", "renderSectionFooter", "renderItem", "SeparatorComponent", "_getSeparatorComponent", "createElement", "ItemWithSeparator", "LeadingSeparatorComponent", "SectionSeparatorComponent", "undefined", "cellKey", "leadingItem", "leadingSection", "prevCell<PERSON>ey", "setSelfHighlightCallback", "_setUpdateHighlightFor", "setSelfUpdatePropsCallback", "_setUpdatePropsFor", "updateHighlightFor", "_updateHighlightFor", "updatePropsFor", "_updatePropsFor", "trailingItem", "trailingSection", "inverted", "value", "updateProps", "_updatePropsMap", "updateHighlight", "_updateHighlightMap", "updateHighlightFn", "updatePropsFn", "_captureRef", "ref", "_listRef", "scrollToLocation", "params", "itemIndex", "i", "sectionIndex", "getItemCount", "sections", "data", "viewOffset", "stickySectionHeadersEnabled", "frame", "__getFrameMetricsApprox", "length", "toIndexParams", "scrollToIndex", "getListRef", "render", "_this2", "_this$props", "ItemSeparatorComponent", "_sections", "passThroughProps", "listHeaderOffset", "ListHeaderComponent", "stickyHeaderIndices", "itemCount", "_iterator", "_step", "done", "push", "getItem", "_getItem", "itemIdx", "sectionData", "_this$props2", "extractor", "isLastItemInList", "isLastItemInSection", "PureComponent", "_React$useState", "useState", "leadingSeparatorHiglighted", "setLeadingSeparatorHighlighted", "_React$useState2", "separatorHighlighted", "setSeparatorHighlighted", "_React$useState3", "leadingSeparatorProps", "setLeadingSeparatorProps", "_React$useState4", "separatorProps", "setSeparatorProps", "useEffect", "separators", "highlight", "unhighlight", "select", "newProps", "element", "leadingSeparator", "highlighted", "separator", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _createForOfIteratorHelperLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/createForOfIteratorHelperLoose\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _View = _interopRequireDefault(require(\"../../../exports/View\"));\nvar _VirtualizedList = _interopRequireDefault(require(\"../VirtualizedList\"));\nvar _VirtualizeUtils = require(\"../VirtualizeUtils\");\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _excluded = [\"ItemSeparatorComponent\", \"SectionSeparatorComponent\", \"renderItem\", \"renderSectionFooter\", \"renderSectionHeader\", \"sections\", \"stickySectionHeadersEnabled\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n/**\n * Right now this just flattens everything into one list and uses VirtualizedList under the\n * hood. The only operation that might not scale well is concatting the data arrays of all the\n * sections when new props are received, which should be plenty fast for up to ~10,000 items.\n */\nclass VirtualizedSectionList extends React.PureComponent {\n  constructor() {\n    super(...arguments);\n    this._keyExtractor = (item, index) => {\n      var info = this._subExtractor(index);\n      return info && info.key || String(index);\n    };\n    this._convertViewable = viewable => {\n      var _info$index;\n      (0, _invariant.default)(viewable.index != null, 'Received a broken ViewToken');\n      var info = this._subExtractor(viewable.index);\n      if (!info) {\n        return null;\n      }\n      var keyExtractorWithNullableIndex = info.section.keyExtractor;\n      var keyExtractorWithNonNullableIndex = this.props.keyExtractor || _VirtualizeUtils.keyExtractor;\n      var key = keyExtractorWithNullableIndex != null ? keyExtractorWithNullableIndex(viewable.item, info.index) : keyExtractorWithNonNullableIndex(viewable.item, (_info$index = info.index) !== null && _info$index !== void 0 ? _info$index : 0);\n      return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, viewable), {}, {\n        index: info.index,\n        key,\n        section: info.section\n      });\n    };\n    this._onViewableItemsChanged = _ref => {\n      var viewableItems = _ref.viewableItems,\n        changed = _ref.changed;\n      var onViewableItemsChanged = this.props.onViewableItemsChanged;\n      if (onViewableItemsChanged != null) {\n        onViewableItemsChanged({\n          viewableItems: viewableItems.map(this._convertViewable, this).filter(Boolean),\n          changed: changed.map(this._convertViewable, this).filter(Boolean)\n        });\n      }\n    };\n    this._renderItem = listItemCount =>\n    // eslint-disable-next-line react/no-unstable-nested-components\n    _ref2 => {\n      var item = _ref2.item,\n        index = _ref2.index;\n      var info = this._subExtractor(index);\n      if (!info) {\n        return null;\n      }\n      var infoIndex = info.index;\n      if (infoIndex == null) {\n        var section = info.section;\n        if (info.header === true) {\n          var renderSectionHeader = this.props.renderSectionHeader;\n          return renderSectionHeader ? renderSectionHeader({\n            section\n          }) : null;\n        } else {\n          var renderSectionFooter = this.props.renderSectionFooter;\n          return renderSectionFooter ? renderSectionFooter({\n            section\n          }) : null;\n        }\n      } else {\n        var renderItem = info.section.renderItem || this.props.renderItem;\n        var SeparatorComponent = this._getSeparatorComponent(index, info, listItemCount);\n        (0, _invariant.default)(renderItem, 'no renderItem!');\n        return /*#__PURE__*/React.createElement(ItemWithSeparator, {\n          SeparatorComponent: SeparatorComponent,\n          LeadingSeparatorComponent: infoIndex === 0 ? this.props.SectionSeparatorComponent : undefined,\n          cellKey: info.key,\n          index: infoIndex,\n          item: item,\n          leadingItem: info.leadingItem,\n          leadingSection: info.leadingSection,\n          prevCellKey: (this._subExtractor(index - 1) || {}).key\n          // Callback to provide updateHighlight for this item\n          ,\n          setSelfHighlightCallback: this._setUpdateHighlightFor,\n          setSelfUpdatePropsCallback: this._setUpdatePropsFor\n          // Provide child ability to set highlight/updateProps for previous item using prevCellKey\n          ,\n          updateHighlightFor: this._updateHighlightFor,\n          updatePropsFor: this._updatePropsFor,\n          renderItem: renderItem,\n          section: info.section,\n          trailingItem: info.trailingItem,\n          trailingSection: info.trailingSection,\n          inverted: !!this.props.inverted\n        });\n      }\n    };\n    this._updatePropsFor = (cellKey, value) => {\n      var updateProps = this._updatePropsMap[cellKey];\n      if (updateProps != null) {\n        updateProps(value);\n      }\n    };\n    this._updateHighlightFor = (cellKey, value) => {\n      var updateHighlight = this._updateHighlightMap[cellKey];\n      if (updateHighlight != null) {\n        updateHighlight(value);\n      }\n    };\n    this._setUpdateHighlightFor = (cellKey, updateHighlightFn) => {\n      if (updateHighlightFn != null) {\n        this._updateHighlightMap[cellKey] = updateHighlightFn;\n      } else {\n        // $FlowFixMe[prop-missing]\n        delete this._updateHighlightFor[cellKey];\n      }\n    };\n    this._setUpdatePropsFor = (cellKey, updatePropsFn) => {\n      if (updatePropsFn != null) {\n        this._updatePropsMap[cellKey] = updatePropsFn;\n      } else {\n        delete this._updatePropsMap[cellKey];\n      }\n    };\n    this._updateHighlightMap = {};\n    this._updatePropsMap = {};\n    this._captureRef = ref => {\n      this._listRef = ref;\n    };\n  }\n  scrollToLocation(params) {\n    var index = params.itemIndex;\n    for (var i = 0; i < params.sectionIndex; i++) {\n      index += this.props.getItemCount(this.props.sections[i].data) + 2;\n    }\n    var viewOffset = params.viewOffset || 0;\n    if (this._listRef == null) {\n      return;\n    }\n    if (params.itemIndex > 0 && this.props.stickySectionHeadersEnabled) {\n      var frame = this._listRef.__getFrameMetricsApprox(index - params.itemIndex, this._listRef.props);\n      viewOffset += frame.length;\n    }\n    var toIndexParams = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, params), {}, {\n      viewOffset,\n      index\n    });\n    // $FlowFixMe[incompatible-use]\n    this._listRef.scrollToIndex(toIndexParams);\n  }\n  getListRef() {\n    return this._listRef;\n  }\n  render() {\n    var _this$props = this.props,\n      ItemSeparatorComponent = _this$props.ItemSeparatorComponent,\n      SectionSeparatorComponent = _this$props.SectionSeparatorComponent,\n      _renderItem = _this$props.renderItem,\n      renderSectionFooter = _this$props.renderSectionFooter,\n      renderSectionHeader = _this$props.renderSectionHeader,\n      _sections = _this$props.sections,\n      stickySectionHeadersEnabled = _this$props.stickySectionHeadersEnabled,\n      passThroughProps = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);\n    var listHeaderOffset = this.props.ListHeaderComponent ? 1 : 0;\n    var stickyHeaderIndices = this.props.stickySectionHeadersEnabled ? [] : undefined;\n    var itemCount = 0;\n    for (var _iterator = (0, _createForOfIteratorHelperLoose2.default)(this.props.sections), _step; !(_step = _iterator()).done;) {\n      var section = _step.value;\n      // Track the section header indices\n      if (stickyHeaderIndices != null) {\n        stickyHeaderIndices.push(itemCount + listHeaderOffset);\n      }\n\n      // Add two for the section header and footer.\n      itemCount += 2;\n      itemCount += this.props.getItemCount(section.data);\n    }\n    var renderItem = this._renderItem(itemCount);\n    return /*#__PURE__*/React.createElement(_VirtualizedList.default, (0, _extends2.default)({}, passThroughProps, {\n      keyExtractor: this._keyExtractor,\n      stickyHeaderIndices: stickyHeaderIndices,\n      renderItem: renderItem,\n      data: this.props.sections,\n      getItem: (sections, index) => this._getItem(this.props, sections, index),\n      getItemCount: () => itemCount,\n      onViewableItemsChanged: this.props.onViewableItemsChanged ? this._onViewableItemsChanged : undefined,\n      ref: this._captureRef\n    }));\n  }\n  _getItem(props, sections, index) {\n    if (!sections) {\n      return null;\n    }\n    var itemIdx = index - 1;\n    for (var i = 0; i < sections.length; i++) {\n      var section = sections[i];\n      var sectionData = section.data;\n      var itemCount = props.getItemCount(sectionData);\n      if (itemIdx === -1 || itemIdx === itemCount) {\n        // We intend for there to be overflow by one on both ends of the list.\n        // This will be for headers and footers. When returning a header or footer\n        // item the section itself is the item.\n        return section;\n      } else if (itemIdx < itemCount) {\n        // If we are in the bounds of the list's data then return the item.\n        return props.getItem(sectionData, itemIdx);\n      } else {\n        itemIdx -= itemCount + 2; // Add two for the header and footer\n      }\n    }\n    return null;\n  }\n\n  // $FlowFixMe[missing-local-annot]\n\n  _subExtractor(index) {\n    var itemIndex = index;\n    var _this$props2 = this.props,\n      getItem = _this$props2.getItem,\n      getItemCount = _this$props2.getItemCount,\n      keyExtractor = _this$props2.keyExtractor,\n      sections = _this$props2.sections;\n    for (var i = 0; i < sections.length; i++) {\n      var section = sections[i];\n      var sectionData = section.data;\n      var key = section.key || String(i);\n      itemIndex -= 1; // The section adds an item for the header\n      if (itemIndex >= getItemCount(sectionData) + 1) {\n        itemIndex -= getItemCount(sectionData) + 1; // The section adds an item for the footer.\n      } else if (itemIndex === -1) {\n        return {\n          section,\n          key: key + ':header',\n          index: null,\n          header: true,\n          trailingSection: sections[i + 1]\n        };\n      } else if (itemIndex === getItemCount(sectionData)) {\n        return {\n          section,\n          key: key + ':footer',\n          index: null,\n          header: false,\n          trailingSection: sections[i + 1]\n        };\n      } else {\n        var extractor = section.keyExtractor || keyExtractor || _VirtualizeUtils.keyExtractor;\n        return {\n          section,\n          key: key + ':' + extractor(getItem(sectionData, itemIndex), itemIndex),\n          index: itemIndex,\n          leadingItem: getItem(sectionData, itemIndex - 1),\n          leadingSection: sections[i - 1],\n          trailingItem: getItem(sectionData, itemIndex + 1),\n          trailingSection: sections[i + 1]\n        };\n      }\n    }\n  }\n  _getSeparatorComponent(index, info, listItemCount) {\n    info = info || this._subExtractor(index);\n    if (!info) {\n      return null;\n    }\n    var ItemSeparatorComponent = info.section.ItemSeparatorComponent || this.props.ItemSeparatorComponent;\n    var SectionSeparatorComponent = this.props.SectionSeparatorComponent;\n    var isLastItemInList = index === listItemCount - 1;\n    var isLastItemInSection = info.index === this.props.getItemCount(info.section.data) - 1;\n    if (SectionSeparatorComponent && isLastItemInSection) {\n      return SectionSeparatorComponent;\n    }\n    if (ItemSeparatorComponent && !isLastItemInSection && !isLastItemInList) {\n      return ItemSeparatorComponent;\n    }\n    return null;\n  }\n}\nfunction ItemWithSeparator(props) {\n  var LeadingSeparatorComponent = props.LeadingSeparatorComponent,\n    SeparatorComponent = props.SeparatorComponent,\n    cellKey = props.cellKey,\n    prevCellKey = props.prevCellKey,\n    setSelfHighlightCallback = props.setSelfHighlightCallback,\n    updateHighlightFor = props.updateHighlightFor,\n    setSelfUpdatePropsCallback = props.setSelfUpdatePropsCallback,\n    updatePropsFor = props.updatePropsFor,\n    item = props.item,\n    index = props.index,\n    section = props.section,\n    inverted = props.inverted;\n  var _React$useState = React.useState(false),\n    leadingSeparatorHiglighted = _React$useState[0],\n    setLeadingSeparatorHighlighted = _React$useState[1];\n  var _React$useState2 = React.useState(false),\n    separatorHighlighted = _React$useState2[0],\n    setSeparatorHighlighted = _React$useState2[1];\n  var _React$useState3 = React.useState({\n      leadingItem: props.leadingItem,\n      leadingSection: props.leadingSection,\n      section: props.section,\n      trailingItem: props.item,\n      trailingSection: props.trailingSection\n    }),\n    leadingSeparatorProps = _React$useState3[0],\n    setLeadingSeparatorProps = _React$useState3[1];\n  var _React$useState4 = React.useState({\n      leadingItem: props.item,\n      leadingSection: props.leadingSection,\n      section: props.section,\n      trailingItem: props.trailingItem,\n      trailingSection: props.trailingSection\n    }),\n    separatorProps = _React$useState4[0],\n    setSeparatorProps = _React$useState4[1];\n  React.useEffect(() => {\n    setSelfHighlightCallback(cellKey, setSeparatorHighlighted);\n    // $FlowFixMe[incompatible-call]\n    setSelfUpdatePropsCallback(cellKey, setSeparatorProps);\n    return () => {\n      setSelfUpdatePropsCallback(cellKey, null);\n      setSelfHighlightCallback(cellKey, null);\n    };\n  }, [cellKey, setSelfHighlightCallback, setSeparatorProps, setSelfUpdatePropsCallback]);\n  var separators = {\n    highlight: () => {\n      setLeadingSeparatorHighlighted(true);\n      setSeparatorHighlighted(true);\n      if (prevCellKey != null) {\n        updateHighlightFor(prevCellKey, true);\n      }\n    },\n    unhighlight: () => {\n      setLeadingSeparatorHighlighted(false);\n      setSeparatorHighlighted(false);\n      if (prevCellKey != null) {\n        updateHighlightFor(prevCellKey, false);\n      }\n    },\n    updateProps: (select, newProps) => {\n      if (select === 'leading') {\n        if (LeadingSeparatorComponent != null) {\n          setLeadingSeparatorProps((0, _objectSpread2.default)((0, _objectSpread2.default)({}, leadingSeparatorProps), newProps));\n        } else if (prevCellKey != null) {\n          // update the previous item's separator\n          updatePropsFor(prevCellKey, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, leadingSeparatorProps), newProps));\n        }\n      } else if (select === 'trailing' && SeparatorComponent != null) {\n        setSeparatorProps((0, _objectSpread2.default)((0, _objectSpread2.default)({}, separatorProps), newProps));\n      }\n    }\n  };\n  var element = props.renderItem({\n    item,\n    index,\n    section,\n    separators\n  });\n  var leadingSeparator = LeadingSeparatorComponent != null && /*#__PURE__*/React.createElement(LeadingSeparatorComponent, (0, _extends2.default)({\n    highlighted: leadingSeparatorHiglighted\n  }, leadingSeparatorProps));\n  var separator = SeparatorComponent != null && /*#__PURE__*/React.createElement(SeparatorComponent, (0, _extends2.default)({\n    highlighted: separatorHighlighted\n  }, separatorProps));\n  return leadingSeparator || separator ? /*#__PURE__*/React.createElement(_View.default, null, inverted === false ? leadingSeparator : separator, element, inverted === false ? separator : leadingSeparator) : element;\n}\n\n/* $FlowFixMe[class-object-subtyping] added when improving typing for this\n * parameters */\n// $FlowFixMe[method-unbinding]\nvar _default = exports.default = VirtualizedSectionList;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,UAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,SAAAM,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAJ,gBAAA,CAAAM,OAAA,EAAAF,CAAA,OAAAL,2BAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAL,gBAAA,CAAAM,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAEb,IAAIa,sBAAsB,GAAGpB,OAAO,CAAC,8CAA8C,CAAC,CAACU,OAAO;AAC5F,IAAIW,uBAAuB,GAAGrB,OAAO,CAAC,+CAA+C,CAAC,CAACU,OAAO;AAC9FY,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACZ,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIc,SAAS,GAAGJ,sBAAsB,CAACpB,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIyB,gCAAgC,GAAGL,sBAAsB,CAACpB,OAAO,CAAC,uDAAuD,CAAC,CAAC;AAC/H,IAAI0B,8BAA8B,GAAGN,sBAAsB,CAACpB,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAI2B,cAAc,GAAGP,sBAAsB,CAACpB,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAI4B,KAAK,GAAGR,sBAAsB,CAACpB,OAAO,wBAAwB,CAAC,CAAC;AACpE,IAAI6B,gBAAgB,GAAGT,sBAAsB,CAACpB,OAAO,qBAAqB,CAAC,CAAC;AAC5E,IAAI8B,gBAAgB,GAAG9B,OAAO,qBAAqB,CAAC;AACpD,IAAI+B,UAAU,GAAGX,sBAAsB,CAACpB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIgC,KAAK,GAAGX,uBAAuB,CAACrB,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIiC,SAAS,GAAG,CAAC,wBAAwB,EAAE,2BAA2B,EAAE,YAAY,EAAE,qBAAqB,EAAE,qBAAqB,EAAE,UAAU,EAAE,6BAA6B,CAAC;AAAC,IAezKC,sBAAsB,aAAAC,oBAAA;EAC1B,SAAAD,uBAAA,EAAc;IAAA,IAAAE,KAAA;IAAA,IAAAnC,gBAAA,CAAAS,OAAA,QAAAwB,sBAAA;IACZE,KAAA,GAAA9B,UAAA,OAAA4B,sBAAA,EAASG,SAAS;IAClBD,KAAA,CAAKE,aAAa,GAAG,UAACC,IAAI,EAAEC,KAAK,EAAK;MACpC,IAAIC,IAAI,GAAGL,KAAA,CAAKM,aAAa,CAACF,KAAK,CAAC;MACpC,OAAOC,IAAI,IAAIA,IAAI,CAACE,GAAG,IAAIC,MAAM,CAACJ,KAAK,CAAC;IAC1C,CAAC;IACDJ,KAAA,CAAKS,gBAAgB,GAAG,UAAAC,QAAQ,EAAI;MAClC,IAAIC,WAAW;MACf,CAAC,CAAC,EAAEhB,UAAU,CAACrB,OAAO,EAAEoC,QAAQ,CAACN,KAAK,IAAI,IAAI,EAAE,6BAA6B,CAAC;MAC9E,IAAIC,IAAI,GAAGL,KAAA,CAAKM,aAAa,CAACI,QAAQ,CAACN,KAAK,CAAC;MAC7C,IAAI,CAACC,IAAI,EAAE;QACT,OAAO,IAAI;MACb;MACA,IAAIO,6BAA6B,GAAGP,IAAI,CAACQ,OAAO,CAACC,YAAY;MAC7D,IAAIC,gCAAgC,GAAGf,KAAA,CAAKgB,KAAK,CAACF,YAAY,IAAIpB,gBAAgB,CAACoB,YAAY;MAC/F,IAAIP,GAAG,GAAGK,6BAA6B,IAAI,IAAI,GAAGA,6BAA6B,CAACF,QAAQ,CAACP,IAAI,EAAEE,IAAI,CAACD,KAAK,CAAC,GAAGW,gCAAgC,CAACL,QAAQ,CAACP,IAAI,EAAE,CAACQ,WAAW,GAAGN,IAAI,CAACD,KAAK,MAAM,IAAI,IAAIO,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAAC,CAAC;MAC7O,OAAO,CAAC,CAAC,EAAEpB,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEiB,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEoC,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE;QAChFN,KAAK,EAAEC,IAAI,CAACD,KAAK;QACjBG,GAAG,EAAHA,GAAG;QACHM,OAAO,EAAER,IAAI,CAACQ;MAChB,CAAC,CAAC;IACJ,CAAC;IACDb,KAAA,CAAKiB,uBAAuB,GAAG,UAAAC,IAAI,EAAI;MACrC,IAAIC,aAAa,GAAGD,IAAI,CAACC,aAAa;QACpCC,OAAO,GAAGF,IAAI,CAACE,OAAO;MACxB,IAAIC,sBAAsB,GAAGrB,KAAA,CAAKgB,KAAK,CAACK,sBAAsB;MAC9D,IAAIA,sBAAsB,IAAI,IAAI,EAAE;QAClCA,sBAAsB,CAAC;UACrBF,aAAa,EAAEA,aAAa,CAACG,GAAG,CAACtB,KAAA,CAAKS,gBAAgB,EAAAT,KAAM,CAAC,CAACuB,MAAM,CAAC3C,OAAO,CAAC;UAC7EwC,OAAO,EAAEA,OAAO,CAACE,GAAG,CAACtB,KAAA,CAAKS,gBAAgB,EAAAT,KAAM,CAAC,CAACuB,MAAM,CAAC3C,OAAO;QAClE,CAAC,CAAC;MACJ;IACF,CAAC;IACDoB,KAAA,CAAKwB,WAAW,GAAG,UAAAC,aAAa;MAAA,QAEhC,UAAAC,KAAK,EAAI;UACP,IAAIvB,IAAI,GAAGuB,KAAK,CAACvB,IAAI;YACnBC,KAAK,GAAGsB,KAAK,CAACtB,KAAK;UACrB,IAAIC,IAAI,GAAGL,KAAA,CAAKM,aAAa,CAACF,KAAK,CAAC;UACpC,IAAI,CAACC,IAAI,EAAE;YACT,OAAO,IAAI;UACb;UACA,IAAIsB,SAAS,GAAGtB,IAAI,CAACD,KAAK;UAC1B,IAAIuB,SAAS,IAAI,IAAI,EAAE;YACrB,IAAId,OAAO,GAAGR,IAAI,CAACQ,OAAO;YAC1B,IAAIR,IAAI,CAACuB,MAAM,KAAK,IAAI,EAAE;cACxB,IAAIC,mBAAmB,GAAG7B,KAAA,CAAKgB,KAAK,CAACa,mBAAmB;cACxD,OAAOA,mBAAmB,GAAGA,mBAAmB,CAAC;gBAC/ChB,OAAO,EAAPA;cACF,CAAC,CAAC,GAAG,IAAI;YACX,CAAC,MAAM;cACL,IAAIiB,mBAAmB,GAAG9B,KAAA,CAAKgB,KAAK,CAACc,mBAAmB;cACxD,OAAOA,mBAAmB,GAAGA,mBAAmB,CAAC;gBAC/CjB,OAAO,EAAPA;cACF,CAAC,CAAC,GAAG,IAAI;YACX;UACF,CAAC,MAAM;YACL,IAAIkB,UAAU,GAAG1B,IAAI,CAACQ,OAAO,CAACkB,UAAU,IAAI/B,KAAA,CAAKgB,KAAK,CAACe,UAAU;YACjE,IAAIC,kBAAkB,GAAGhC,KAAA,CAAKiC,sBAAsB,CAAC7B,KAAK,EAAEC,IAAI,EAAEoB,aAAa,CAAC;YAChF,CAAC,CAAC,EAAE9B,UAAU,CAACrB,OAAO,EAAEyD,UAAU,EAAE,gBAAgB,CAAC;YACrD,OAAoBnC,KAAK,CAACsC,aAAa,CAACC,iBAAiB,EAAE;cACzDH,kBAAkB,EAAEA,kBAAkB;cACtCI,yBAAyB,EAAET,SAAS,KAAK,CAAC,GAAG3B,KAAA,CAAKgB,KAAK,CAACqB,yBAAyB,GAAGC,SAAS;cAC7FC,OAAO,EAAElC,IAAI,CAACE,GAAG;cACjBH,KAAK,EAAEuB,SAAS;cAChBxB,IAAI,EAAEA,IAAI;cACVqC,WAAW,EAAEnC,IAAI,CAACmC,WAAW;cAC7BC,cAAc,EAAEpC,IAAI,CAACoC,cAAc;cACnCC,WAAW,EAAE,CAAC1C,KAAA,CAAKM,aAAa,CAACF,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAEG,GAAG;cAGtDoC,wBAAwB,EAAE3C,KAAA,CAAK4C,sBAAsB;cACrDC,0BAA0B,EAAE7C,KAAA,CAAK8C,kBAAkB;cAGnDC,kBAAkB,EAAE/C,KAAA,CAAKgD,mBAAmB;cAC5CC,cAAc,EAAEjD,KAAA,CAAKkD,eAAe;cACpCnB,UAAU,EAAEA,UAAU;cACtBlB,OAAO,EAAER,IAAI,CAACQ,OAAO;cACrBsC,YAAY,EAAE9C,IAAI,CAAC8C,YAAY;cAC/BC,eAAe,EAAE/C,IAAI,CAAC+C,eAAe;cACrCC,QAAQ,EAAE,CAAC,CAACrD,KAAA,CAAKgB,KAAK,CAACqC;YACzB,CAAC,CAAC;UACJ;QACF;MAAC;IAAA;IACDrD,KAAA,CAAKkD,eAAe,GAAG,UAACX,OAAO,EAAEe,KAAK,EAAK;MACzC,IAAIC,WAAW,GAAGvD,KAAA,CAAKwD,eAAe,CAACjB,OAAO,CAAC;MAC/C,IAAIgB,WAAW,IAAI,IAAI,EAAE;QACvBA,WAAW,CAACD,KAAK,CAAC;MACpB;IACF,CAAC;IACDtD,KAAA,CAAKgD,mBAAmB,GAAG,UAACT,OAAO,EAAEe,KAAK,EAAK;MAC7C,IAAIG,eAAe,GAAGzD,KAAA,CAAK0D,mBAAmB,CAACnB,OAAO,CAAC;MACvD,IAAIkB,eAAe,IAAI,IAAI,EAAE;QAC3BA,eAAe,CAACH,KAAK,CAAC;MACxB;IACF,CAAC;IACDtD,KAAA,CAAK4C,sBAAsB,GAAG,UAACL,OAAO,EAAEoB,iBAAiB,EAAK;MAC5D,IAAIA,iBAAiB,IAAI,IAAI,EAAE;QAC7B3D,KAAA,CAAK0D,mBAAmB,CAACnB,OAAO,CAAC,GAAGoB,iBAAiB;MACvD,CAAC,MAAM;QAEL,OAAO3D,KAAA,CAAKgD,mBAAmB,CAACT,OAAO,CAAC;MAC1C;IACF,CAAC;IACDvC,KAAA,CAAK8C,kBAAkB,GAAG,UAACP,OAAO,EAAEqB,aAAa,EAAK;MACpD,IAAIA,aAAa,IAAI,IAAI,EAAE;QACzB5D,KAAA,CAAKwD,eAAe,CAACjB,OAAO,CAAC,GAAGqB,aAAa;MAC/C,CAAC,MAAM;QACL,OAAO5D,KAAA,CAAKwD,eAAe,CAACjB,OAAO,CAAC;MACtC;IACF,CAAC;IACDvC,KAAA,CAAK0D,mBAAmB,GAAG,CAAC,CAAC;IAC7B1D,KAAA,CAAKwD,eAAe,GAAG,CAAC,CAAC;IACzBxD,KAAA,CAAK6D,WAAW,GAAG,UAAAC,GAAG,EAAI;MACxB9D,KAAA,CAAK+D,QAAQ,GAAGD,GAAG;IACrB,CAAC;IAAC,OAAA9D,KAAA;EACJ;EAAC,IAAA/B,UAAA,CAAAK,OAAA,EAAAwB,sBAAA,EAAAC,oBAAA;EAAA,WAAAjC,aAAA,CAAAQ,OAAA,EAAAwB,sBAAA;IAAAS,GAAA;IAAA+C,KAAA,EACD,SAAAU,gBAAgBA,CAACC,MAAM,EAAE;MACvB,IAAI7D,KAAK,GAAG6D,MAAM,CAACC,SAAS;MAC5B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,YAAY,EAAED,CAAC,EAAE,EAAE;QAC5C/D,KAAK,IAAI,IAAI,CAACY,KAAK,CAACqD,YAAY,CAAC,IAAI,CAACrD,KAAK,CAACsD,QAAQ,CAACH,CAAC,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC;MACnE;MACA,IAAIC,UAAU,GAAGP,MAAM,CAACO,UAAU,IAAI,CAAC;MACvC,IAAI,IAAI,CAACT,QAAQ,IAAI,IAAI,EAAE;QACzB;MACF;MACA,IAAIE,MAAM,CAACC,SAAS,GAAG,CAAC,IAAI,IAAI,CAAClD,KAAK,CAACyD,2BAA2B,EAAE;QAClE,IAAIC,KAAK,GAAG,IAAI,CAACX,QAAQ,CAACY,uBAAuB,CAACvE,KAAK,GAAG6D,MAAM,CAACC,SAAS,EAAE,IAAI,CAACH,QAAQ,CAAC/C,KAAK,CAAC;QAChGwD,UAAU,IAAIE,KAAK,CAACE,MAAM;MAC5B;MACA,IAAIC,aAAa,GAAG,CAAC,CAAC,EAAEtF,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEiB,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAE2F,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QAC3FO,UAAU,EAAVA,UAAU;QACVpE,KAAK,EAALA;MACF,CAAC,CAAC;MAEF,IAAI,CAAC2D,QAAQ,CAACe,aAAa,CAACD,aAAa,CAAC;IAC5C;EAAC;IAAAtE,GAAA;IAAA+C,KAAA,EACD,SAAAyB,UAAUA,CAAA,EAAG;MACX,OAAO,IAAI,CAAChB,QAAQ;IACtB;EAAC;IAAAxD,GAAA;IAAA+C,KAAA,EACD,SAAA0B,MAAMA,CAAA,EAAG;MAAA,IAAAC,MAAA;MACP,IAAIC,WAAW,GAAG,IAAI,CAAClE,KAAK;QAC1BmE,sBAAsB,GAAGD,WAAW,CAACC,sBAAsB;QAC3D9C,yBAAyB,GAAG6C,WAAW,CAAC7C,yBAAyB;QACjEb,WAAW,GAAG0D,WAAW,CAACnD,UAAU;QACpCD,mBAAmB,GAAGoD,WAAW,CAACpD,mBAAmB;QACrDD,mBAAmB,GAAGqD,WAAW,CAACrD,mBAAmB;QACrDuD,SAAS,GAAGF,WAAW,CAACZ,QAAQ;QAChCG,2BAA2B,GAAGS,WAAW,CAACT,2BAA2B;QACrEY,gBAAgB,GAAG,CAAC,CAAC,EAAE/F,8BAA8B,CAAChB,OAAO,EAAE4G,WAAW,EAAErF,SAAS,CAAC;MACxF,IAAIyF,gBAAgB,GAAG,IAAI,CAACtE,KAAK,CAACuE,mBAAmB,GAAG,CAAC,GAAG,CAAC;MAC7D,IAAIC,mBAAmB,GAAG,IAAI,CAACxE,KAAK,CAACyD,2BAA2B,GAAG,EAAE,GAAGnC,SAAS;MACjF,IAAImD,SAAS,GAAG,CAAC;MACjB,KAAK,IAAIC,SAAS,GAAG,CAAC,CAAC,EAAErG,gCAAgC,CAACf,OAAO,EAAE,IAAI,CAAC0C,KAAK,CAACsD,QAAQ,CAAC,EAAEqB,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAEE,IAAI,GAAG;QAC5H,IAAI/E,OAAO,GAAG8E,KAAK,CAACrC,KAAK;QAEzB,IAAIkC,mBAAmB,IAAI,IAAI,EAAE;UAC/BA,mBAAmB,CAACK,IAAI,CAACJ,SAAS,GAAGH,gBAAgB,CAAC;QACxD;QAGAG,SAAS,IAAI,CAAC;QACdA,SAAS,IAAI,IAAI,CAACzE,KAAK,CAACqD,YAAY,CAACxD,OAAO,CAAC0D,IAAI,CAAC;MACpD;MACA,IAAIxC,UAAU,GAAG,IAAI,CAACP,WAAW,CAACiE,SAAS,CAAC;MAC5C,OAAoB7F,KAAK,CAACsC,aAAa,CAACzC,gBAAgB,CAACnB,OAAO,EAAE,CAAC,CAAC,EAAEc,SAAS,CAACd,OAAO,EAAE,CAAC,CAAC,EAAE+G,gBAAgB,EAAE;QAC7GvE,YAAY,EAAE,IAAI,CAACZ,aAAa;QAChCsF,mBAAmB,EAAEA,mBAAmB;QACxCzD,UAAU,EAAEA,UAAU;QACtBwC,IAAI,EAAE,IAAI,CAACvD,KAAK,CAACsD,QAAQ;QACzBwB,OAAO,EAAE,SAATA,OAAOA,CAAGxB,QAAQ,EAAElE,KAAK;UAAA,OAAK6E,MAAI,CAACc,QAAQ,CAACd,MAAI,CAACjE,KAAK,EAAEsD,QAAQ,EAAElE,KAAK,CAAC;QAAA;QACxEiE,YAAY,EAAE,SAAdA,YAAYA,CAAA;UAAA,OAAQoB,SAAS;QAAA;QAC7BpE,sBAAsB,EAAE,IAAI,CAACL,KAAK,CAACK,sBAAsB,GAAG,IAAI,CAACJ,uBAAuB,GAAGqB,SAAS;QACpGwB,GAAG,EAAE,IAAI,CAACD;MACZ,CAAC,CAAC,CAAC;IACL;EAAC;IAAAtD,GAAA;IAAA+C,KAAA,EACD,SAAAyC,QAAQA,CAAC/E,KAAK,EAAEsD,QAAQ,EAAElE,KAAK,EAAE;MAC/B,IAAI,CAACkE,QAAQ,EAAE;QACb,OAAO,IAAI;MACb;MACA,IAAI0B,OAAO,GAAG5F,KAAK,GAAG,CAAC;MACvB,KAAK,IAAI+D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,QAAQ,CAACM,MAAM,EAAET,CAAC,EAAE,EAAE;QACxC,IAAItD,OAAO,GAAGyD,QAAQ,CAACH,CAAC,CAAC;QACzB,IAAI8B,WAAW,GAAGpF,OAAO,CAAC0D,IAAI;QAC9B,IAAIkB,SAAS,GAAGzE,KAAK,CAACqD,YAAY,CAAC4B,WAAW,CAAC;QAC/C,IAAID,OAAO,KAAK,CAAC,CAAC,IAAIA,OAAO,KAAKP,SAAS,EAAE;UAI3C,OAAO5E,OAAO;QAChB,CAAC,MAAM,IAAImF,OAAO,GAAGP,SAAS,EAAE;UAE9B,OAAOzE,KAAK,CAAC8E,OAAO,CAACG,WAAW,EAAED,OAAO,CAAC;QAC5C,CAAC,MAAM;UACLA,OAAO,IAAIP,SAAS,GAAG,CAAC;QAC1B;MACF;MACA,OAAO,IAAI;IACb;EAAC;IAAAlF,GAAA;IAAA+C,KAAA,EAID,SAAAhD,aAAaA,CAACF,KAAK,EAAE;MACnB,IAAI8D,SAAS,GAAG9D,KAAK;MACrB,IAAI8F,YAAY,GAAG,IAAI,CAAClF,KAAK;QAC3B8E,OAAO,GAAGI,YAAY,CAACJ,OAAO;QAC9BzB,YAAY,GAAG6B,YAAY,CAAC7B,YAAY;QACxCvD,YAAY,GAAGoF,YAAY,CAACpF,YAAY;QACxCwD,QAAQ,GAAG4B,YAAY,CAAC5B,QAAQ;MAClC,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,QAAQ,CAACM,MAAM,EAAET,CAAC,EAAE,EAAE;QACxC,IAAItD,OAAO,GAAGyD,QAAQ,CAACH,CAAC,CAAC;QACzB,IAAI8B,WAAW,GAAGpF,OAAO,CAAC0D,IAAI;QAC9B,IAAIhE,GAAG,GAAGM,OAAO,CAACN,GAAG,IAAIC,MAAM,CAAC2D,CAAC,CAAC;QAClCD,SAAS,IAAI,CAAC;QACd,IAAIA,SAAS,IAAIG,YAAY,CAAC4B,WAAW,CAAC,GAAG,CAAC,EAAE;UAC9C/B,SAAS,IAAIG,YAAY,CAAC4B,WAAW,CAAC,GAAG,CAAC;QAC5C,CAAC,MAAM,IAAI/B,SAAS,KAAK,CAAC,CAAC,EAAE;UAC3B,OAAO;YACLrD,OAAO,EAAPA,OAAO;YACPN,GAAG,EAAEA,GAAG,GAAG,SAAS;YACpBH,KAAK,EAAE,IAAI;YACXwB,MAAM,EAAE,IAAI;YACZwB,eAAe,EAAEkB,QAAQ,CAACH,CAAC,GAAG,CAAC;UACjC,CAAC;QACH,CAAC,MAAM,IAAID,SAAS,KAAKG,YAAY,CAAC4B,WAAW,CAAC,EAAE;UAClD,OAAO;YACLpF,OAAO,EAAPA,OAAO;YACPN,GAAG,EAAEA,GAAG,GAAG,SAAS;YACpBH,KAAK,EAAE,IAAI;YACXwB,MAAM,EAAE,KAAK;YACbwB,eAAe,EAAEkB,QAAQ,CAACH,CAAC,GAAG,CAAC;UACjC,CAAC;QACH,CAAC,MAAM;UACL,IAAIgC,SAAS,GAAGtF,OAAO,CAACC,YAAY,IAAIA,YAAY,IAAIpB,gBAAgB,CAACoB,YAAY;UACrF,OAAO;YACLD,OAAO,EAAPA,OAAO;YACPN,GAAG,EAAEA,GAAG,GAAG,GAAG,GAAG4F,SAAS,CAACL,OAAO,CAACG,WAAW,EAAE/B,SAAS,CAAC,EAAEA,SAAS,CAAC;YACtE9D,KAAK,EAAE8D,SAAS;YAChB1B,WAAW,EAAEsD,OAAO,CAACG,WAAW,EAAE/B,SAAS,GAAG,CAAC,CAAC;YAChDzB,cAAc,EAAE6B,QAAQ,CAACH,CAAC,GAAG,CAAC,CAAC;YAC/BhB,YAAY,EAAE2C,OAAO,CAACG,WAAW,EAAE/B,SAAS,GAAG,CAAC,CAAC;YACjDd,eAAe,EAAEkB,QAAQ,CAACH,CAAC,GAAG,CAAC;UACjC,CAAC;QACH;MACF;IACF;EAAC;IAAA5D,GAAA;IAAA+C,KAAA,EACD,SAAArB,sBAAsBA,CAAC7B,KAAK,EAAEC,IAAI,EAAEoB,aAAa,EAAE;MACjDpB,IAAI,GAAGA,IAAI,IAAI,IAAI,CAACC,aAAa,CAACF,KAAK,CAAC;MACxC,IAAI,CAACC,IAAI,EAAE;QACT,OAAO,IAAI;MACb;MACA,IAAI8E,sBAAsB,GAAG9E,IAAI,CAACQ,OAAO,CAACsE,sBAAsB,IAAI,IAAI,CAACnE,KAAK,CAACmE,sBAAsB;MACrG,IAAI9C,yBAAyB,GAAG,IAAI,CAACrB,KAAK,CAACqB,yBAAyB;MACpE,IAAI+D,gBAAgB,GAAGhG,KAAK,KAAKqB,aAAa,GAAG,CAAC;MAClD,IAAI4E,mBAAmB,GAAGhG,IAAI,CAACD,KAAK,KAAK,IAAI,CAACY,KAAK,CAACqD,YAAY,CAAChE,IAAI,CAACQ,OAAO,CAAC0D,IAAI,CAAC,GAAG,CAAC;MACvF,IAAIlC,yBAAyB,IAAIgE,mBAAmB,EAAE;QACpD,OAAOhE,yBAAyB;MAClC;MACA,IAAI8C,sBAAsB,IAAI,CAACkB,mBAAmB,IAAI,CAACD,gBAAgB,EAAE;QACvE,OAAOjB,sBAAsB;MAC/B;MACA,OAAO,IAAI;IACb;EAAC;AAAA,EAxQkCvF,KAAK,CAAC0G,aAAa;AA0QxD,SAASnE,iBAAiBA,CAACnB,KAAK,EAAE;EAChC,IAAIoB,yBAAyB,GAAGpB,KAAK,CAACoB,yBAAyB;IAC7DJ,kBAAkB,GAAGhB,KAAK,CAACgB,kBAAkB;IAC7CO,OAAO,GAAGvB,KAAK,CAACuB,OAAO;IACvBG,WAAW,GAAG1B,KAAK,CAAC0B,WAAW;IAC/BC,wBAAwB,GAAG3B,KAAK,CAAC2B,wBAAwB;IACzDI,kBAAkB,GAAG/B,KAAK,CAAC+B,kBAAkB;IAC7CF,0BAA0B,GAAG7B,KAAK,CAAC6B,0BAA0B;IAC7DI,cAAc,GAAGjC,KAAK,CAACiC,cAAc;IACrC9C,IAAI,GAAGa,KAAK,CAACb,IAAI;IACjBC,KAAK,GAAGY,KAAK,CAACZ,KAAK;IACnBS,OAAO,GAAGG,KAAK,CAACH,OAAO;IACvBwC,QAAQ,GAAGrC,KAAK,CAACqC,QAAQ;EAC3B,IAAIkD,eAAe,GAAG3G,KAAK,CAAC4G,QAAQ,CAAC,KAAK,CAAC;IACzCC,0BAA0B,GAAGF,eAAe,CAAC,CAAC,CAAC;IAC/CG,8BAA8B,GAAGH,eAAe,CAAC,CAAC,CAAC;EACrD,IAAII,gBAAgB,GAAG/G,KAAK,CAAC4G,QAAQ,CAAC,KAAK,CAAC;IAC1CI,oBAAoB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC1CE,uBAAuB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAC/C,IAAIG,gBAAgB,GAAGlH,KAAK,CAAC4G,QAAQ,CAAC;MAClChE,WAAW,EAAExB,KAAK,CAACwB,WAAW;MAC9BC,cAAc,EAAEzB,KAAK,CAACyB,cAAc;MACpC5B,OAAO,EAAEG,KAAK,CAACH,OAAO;MACtBsC,YAAY,EAAEnC,KAAK,CAACb,IAAI;MACxBiD,eAAe,EAAEpC,KAAK,CAACoC;IACzB,CAAC,CAAC;IACF2D,qBAAqB,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC3CE,wBAAwB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EAChD,IAAIG,gBAAgB,GAAGrH,KAAK,CAAC4G,QAAQ,CAAC;MAClChE,WAAW,EAAExB,KAAK,CAACb,IAAI;MACvBsC,cAAc,EAAEzB,KAAK,CAACyB,cAAc;MACpC5B,OAAO,EAAEG,KAAK,CAACH,OAAO;MACtBsC,YAAY,EAAEnC,KAAK,CAACmC,YAAY;MAChCC,eAAe,EAAEpC,KAAK,CAACoC;IACzB,CAAC,CAAC;IACF8D,cAAc,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IACpCE,iBAAiB,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACzCrH,KAAK,CAACwH,SAAS,CAAC,YAAM;IACpBzE,wBAAwB,CAACJ,OAAO,EAAEsE,uBAAuB,CAAC;IAE1DhE,0BAA0B,CAACN,OAAO,EAAE4E,iBAAiB,CAAC;IACtD,OAAO,YAAM;MACXtE,0BAA0B,CAACN,OAAO,EAAE,IAAI,CAAC;MACzCI,wBAAwB,CAACJ,OAAO,EAAE,IAAI,CAAC;IACzC,CAAC;EACH,CAAC,EAAE,CAACA,OAAO,EAAEI,wBAAwB,EAAEwE,iBAAiB,EAAEtE,0BAA0B,CAAC,CAAC;EACtF,IAAIwE,UAAU,GAAG;IACfC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAQ;MACfZ,8BAA8B,CAAC,IAAI,CAAC;MACpCG,uBAAuB,CAAC,IAAI,CAAC;MAC7B,IAAInE,WAAW,IAAI,IAAI,EAAE;QACvBK,kBAAkB,CAACL,WAAW,EAAE,IAAI,CAAC;MACvC;IACF,CAAC;IACD6E,WAAW,EAAE,SAAbA,WAAWA,CAAA,EAAQ;MACjBb,8BAA8B,CAAC,KAAK,CAAC;MACrCG,uBAAuB,CAAC,KAAK,CAAC;MAC9B,IAAInE,WAAW,IAAI,IAAI,EAAE;QACvBK,kBAAkB,CAACL,WAAW,EAAE,KAAK,CAAC;MACxC;IACF,CAAC;IACDa,WAAW,EAAE,SAAbA,WAAWA,CAAGiE,MAAM,EAAEC,QAAQ,EAAK;MACjC,IAAID,MAAM,KAAK,SAAS,EAAE;QACxB,IAAIpF,yBAAyB,IAAI,IAAI,EAAE;UACrC4E,wBAAwB,CAAC,CAAC,CAAC,EAAEzH,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEiB,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEyI,qBAAqB,CAAC,EAAEU,QAAQ,CAAC,CAAC;QACzH,CAAC,MAAM,IAAI/E,WAAW,IAAI,IAAI,EAAE;UAE9BO,cAAc,CAACP,WAAW,EAAE,CAAC,CAAC,EAAEnD,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEiB,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEyI,qBAAqB,CAAC,EAAEU,QAAQ,CAAC,CAAC;QAC5H;MACF,CAAC,MAAM,IAAID,MAAM,KAAK,UAAU,IAAIxF,kBAAkB,IAAI,IAAI,EAAE;QAC9DmF,iBAAiB,CAAC,CAAC,CAAC,EAAE5H,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAEiB,cAAc,CAACjB,OAAO,EAAE,CAAC,CAAC,EAAE4I,cAAc,CAAC,EAAEO,QAAQ,CAAC,CAAC;MAC3G;IACF;EACF,CAAC;EACD,IAAIC,OAAO,GAAG1G,KAAK,CAACe,UAAU,CAAC;IAC7B5B,IAAI,EAAJA,IAAI;IACJC,KAAK,EAALA,KAAK;IACLS,OAAO,EAAPA,OAAO;IACPwG,UAAU,EAAVA;EACF,CAAC,CAAC;EACF,IAAIM,gBAAgB,GAAGvF,yBAAyB,IAAI,IAAI,IAAiBxC,KAAK,CAACsC,aAAa,CAACE,yBAAyB,EAAE,CAAC,CAAC,EAAEhD,SAAS,CAACd,OAAO,EAAE;IAC7IsJ,WAAW,EAAEnB;EACf,CAAC,EAAEM,qBAAqB,CAAC,CAAC;EAC1B,IAAIc,SAAS,GAAG7F,kBAAkB,IAAI,IAAI,IAAiBpC,KAAK,CAACsC,aAAa,CAACF,kBAAkB,EAAE,CAAC,CAAC,EAAE5C,SAAS,CAACd,OAAO,EAAE;IACxHsJ,WAAW,EAAEhB;EACf,CAAC,EAAEM,cAAc,CAAC,CAAC;EACnB,OAAOS,gBAAgB,IAAIE,SAAS,GAAgBjI,KAAK,CAACsC,aAAa,CAAC1C,KAAK,CAAClB,OAAO,EAAE,IAAI,EAAE+E,QAAQ,KAAK,KAAK,GAAGsE,gBAAgB,GAAGE,SAAS,EAAEH,OAAO,EAAErE,QAAQ,KAAK,KAAK,GAAGwE,SAAS,GAAGF,gBAAgB,CAAC,GAAGD,OAAO;AACvN;AAKA,IAAII,QAAQ,GAAG5I,OAAO,CAACZ,OAAO,GAAGwB,sBAAsB;AACvDiI,MAAM,CAAC7I,OAAO,GAAGA,OAAO,CAACZ,OAAO", "ignoreList": []}