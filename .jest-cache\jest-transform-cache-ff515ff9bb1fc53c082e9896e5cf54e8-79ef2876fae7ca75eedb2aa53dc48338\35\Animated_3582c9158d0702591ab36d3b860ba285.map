{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_objectSpread2", "_Platform", "_AnimatedFlatList", "_AnimatedImage", "_AnimatedScrollView", "_AnimatedSectionList", "_AnimatedText", "_<PERSON><PERSON><PERSON><PERSON>", "_AnimatedMock", "_AnimatedImplementation", "Animated", "isTesting", "_default", "FlatList", "Image", "ScrollView", "SectionList", "Text", "View", "module"], "sources": ["Animated.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _Platform = _interopRequireDefault(require(\"../../../exports/Platform\"));\nvar _AnimatedFlatList = _interopRequireDefault(require(\"./components/AnimatedFlatList\"));\nvar _AnimatedImage = _interopRequireDefault(require(\"./components/AnimatedImage\"));\nvar _AnimatedScrollView = _interopRequireDefault(require(\"./components/AnimatedScrollView\"));\nvar _AnimatedSectionList = _interopRequireDefault(require(\"./components/AnimatedSectionList\"));\nvar _AnimatedText = _interopRequireDefault(require(\"./components/AnimatedText\"));\nvar _AnimatedView = _interopRequireDefault(require(\"./components/AnimatedView\"));\nvar _AnimatedMock = _interopRequireDefault(require(\"./AnimatedMock\"));\nvar _AnimatedImplementation = _interopRequireDefault(require(\"./AnimatedImplementation\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nvar Animated = _Platform.default.isTesting ? _AnimatedMock.default : _AnimatedImplementation.default;\nvar _default = exports.default = (0, _objectSpread2.default)({\n  FlatList: _AnimatedFlatList.default,\n  Image: _AnimatedImage.default,\n  ScrollView: _AnimatedScrollView.default,\n  SectionList: _AnimatedSectionList.default,\n  Text: _AnimatedText.default,\n  View: _AnimatedView.default\n}, Animated);\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,cAAc,GAAGL,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIK,SAAS,GAAGN,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAC5E,IAAIM,iBAAiB,GAAGP,sBAAsB,CAACC,OAAO,gCAAgC,CAAC,CAAC;AACxF,IAAIO,cAAc,GAAGR,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AAClF,IAAIQ,mBAAmB,GAAGT,sBAAsB,CAACC,OAAO,kCAAkC,CAAC,CAAC;AAC5F,IAAIS,oBAAoB,GAAGV,sBAAsB,CAACC,OAAO,mCAAmC,CAAC,CAAC;AAC9F,IAAIU,aAAa,GAAGX,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAChF,IAAIW,aAAa,GAAGZ,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAChF,IAAIY,aAAa,GAAGb,sBAAsB,CAACC,OAAO,iBAAiB,CAAC,CAAC;AACrE,IAAIa,uBAAuB,GAAGd,sBAAsB,CAACC,OAAO,2BAA2B,CAAC,CAAC;AAWzF,IAAIc,QAAQ,GAAGT,SAAS,CAACJ,OAAO,CAACc,SAAS,GAAGH,aAAa,CAACX,OAAO,GAAGY,uBAAuB,CAACZ,OAAO;AACpG,IAAIe,QAAQ,GAAGd,OAAO,CAACD,OAAO,GAAG,CAAC,CAAC,EAAEG,cAAc,CAACH,OAAO,EAAE;EAC3DgB,QAAQ,EAAEX,iBAAiB,CAACL,OAAO;EACnCiB,KAAK,EAAEX,cAAc,CAACN,OAAO;EAC7BkB,UAAU,EAAEX,mBAAmB,CAACP,OAAO;EACvCmB,WAAW,EAAEX,oBAAoB,CAACR,OAAO;EACzCoB,IAAI,EAAEX,aAAa,CAACT,OAAO;EAC3BqB,IAAI,EAAEX,aAAa,CAACV;AACtB,CAAC,EAAEa,QAAQ,CAAC;AACZS,MAAM,CAACrB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}