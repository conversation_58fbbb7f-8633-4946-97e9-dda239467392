{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_invariant", "_canUseDom", "initialURL", "window", "location", "href", "Linking", "_eventCallbacks", "key", "value", "_dispatchEvent", "event", "_len", "arguments", "length", "data", "Array", "_key", "listeners", "isArray", "map", "listener", "apply", "addEventListener", "eventType", "callback", "_this", "push", "remove", "callbacks", "filteredCallbacks", "filter", "c", "toString", "removeEventListener", "console", "error", "canOpenURL", "Promise", "resolve", "getInitialURL", "openURL", "url", "target", "open", "e", "reject", "_validateURL", "urlToOpen", "URL", "indexOf", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _canUseDom = _interopRequireDefault(require(\"../../modules/canUseDom\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar initialURL = _canUseDom.default ? window.location.href : '';\nclass Linking {\n  constructor() {\n    this._eventCallbacks = {};\n  }\n  /**\n   * An object mapping of event name\n   * and all the callbacks subscribing to it\n   */\n  _dispatchEvent(event) {\n    for (var _len = arguments.length, data = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      data[_key - 1] = arguments[_key];\n    }\n    var listeners = this._eventCallbacks[event];\n    if (listeners != null && Array.isArray(listeners)) {\n      listeners.map(listener => {\n        listener(...data);\n      });\n    }\n  }\n\n  /**\n   * Adds a event listener for the specified event. The callback will be called when the\n   * said event is dispatched.\n   */\n  addEventListener(eventType, callback) {\n    var _this = this;\n    if (!_this._eventCallbacks[eventType]) {\n      _this._eventCallbacks[eventType] = [callback];\n    }\n    _this._eventCallbacks[eventType].push(callback);\n    return {\n      remove() {\n        var callbacks = _this._eventCallbacks[eventType];\n        var filteredCallbacks = callbacks.filter(c => c.toString() !== callback.toString());\n        _this._eventCallbacks[eventType] = filteredCallbacks;\n      }\n    };\n  }\n\n  /**\n   * Removes a previously added event listener for the specified event. The callback must\n   * be the same object as the one passed to `addEventListener`.\n   */\n  removeEventListener(eventType, callback) {\n    console.error(\"Linking.removeEventListener('\" + eventType + \"', ...): Method has been \" + 'deprecated. Please instead use `remove()` on the subscription ' + 'returned by `Linking.addEventListener`.');\n    var callbacks = this._eventCallbacks[eventType];\n    var filteredCallbacks = callbacks.filter(c => c.toString() !== callback.toString());\n    this._eventCallbacks[eventType] = filteredCallbacks;\n  }\n  canOpenURL() {\n    return Promise.resolve(true);\n  }\n  getInitialURL() {\n    return Promise.resolve(initialURL);\n  }\n\n  /**\n   * Try to open the given url in a secure fashion. The method returns a Promise object.\n   * If a target is passed (including undefined) that target will be used, otherwise '_blank'.\n   * If the url opens, the promise is resolved. If not, the promise is rejected.\n   * Dispatches the `onOpen` event if `url` is opened successfully.\n   */\n  openURL(url, target) {\n    if (arguments.length === 1) {\n      target = '_blank';\n    }\n    try {\n      open(url, target);\n      this._dispatchEvent('onOpen', url);\n      return Promise.resolve();\n    } catch (e) {\n      return Promise.reject(e);\n    }\n  }\n  _validateURL(url) {\n    (0, _invariant.default)(typeof url === 'string', 'Invalid URL: should be a string. Was: ' + url);\n    (0, _invariant.default)(url, 'Invalid URL: cannot be empty');\n  }\n}\nvar open = (url, target) => {\n  if (_canUseDom.default) {\n    var urlToOpen = new URL(url, window.location).toString();\n    if (urlToOpen.indexOf('tel:') === 0) {\n      window.location = urlToOpen;\n    } else {\n      window.open(urlToOpen, target, 'noopener');\n    }\n  }\n};\nvar _default = exports.default = new Linking();\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,UAAU,GAAGJ,sBAAsB,CAACH,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIQ,UAAU,GAAGL,sBAAsB,CAACH,OAAO,0BAA0B,CAAC,CAAC;AAW3E,IAAIS,UAAU,GAAGD,UAAU,CAACJ,OAAO,GAAGM,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,EAAE;AAAC,IAC1DC,OAAO;EACX,SAAAA,QAAA,EAAc;IAAA,IAAAZ,gBAAA,CAAAG,OAAA,QAAAS,OAAA;IACZ,IAAI,CAACC,eAAe,GAAG,CAAC,CAAC;EAC3B;EAAC,WAAAZ,aAAA,CAAAE,OAAA,EAAAS,OAAA;IAAAE,GAAA;IAAAC,KAAA,EAKD,SAAAC,cAAcA,CAACC,KAAK,EAAE;MACpB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QAC1GF,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAClC;MACA,IAAIC,SAAS,GAAG,IAAI,CAACX,eAAe,CAACI,KAAK,CAAC;MAC3C,IAAIO,SAAS,IAAI,IAAI,IAAIF,KAAK,CAACG,OAAO,CAACD,SAAS,CAAC,EAAE;QACjDA,SAAS,CAACE,GAAG,CAAC,UAAAC,QAAQ,EAAI;UACxBA,QAAQ,CAAAC,KAAA,SAAIP,IAAI,CAAC;QACnB,CAAC,CAAC;MACJ;IACF;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAMD,SAAAc,gBAAgBA,CAACC,SAAS,EAAEC,QAAQ,EAAE;MACpC,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAI,CAACA,KAAK,CAACnB,eAAe,CAACiB,SAAS,CAAC,EAAE;QACrCE,KAAK,CAACnB,eAAe,CAACiB,SAAS,CAAC,GAAG,CAACC,QAAQ,CAAC;MAC/C;MACAC,KAAK,CAACnB,eAAe,CAACiB,SAAS,CAAC,CAACG,IAAI,CAACF,QAAQ,CAAC;MAC/C,OAAO;QACLG,MAAM,WAANA,MAAMA,CAAA,EAAG;UACP,IAAIC,SAAS,GAAGH,KAAK,CAACnB,eAAe,CAACiB,SAAS,CAAC;UAChD,IAAIM,iBAAiB,GAAGD,SAAS,CAACE,MAAM,CAAC,UAAAC,CAAC;YAAA,OAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,KAAKR,QAAQ,CAACQ,QAAQ,CAAC,CAAC;UAAA,EAAC;UACnFP,KAAK,CAACnB,eAAe,CAACiB,SAAS,CAAC,GAAGM,iBAAiB;QACtD;MACF,CAAC;IACH;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EAMD,SAAAyB,mBAAmBA,CAACV,SAAS,EAAEC,QAAQ,EAAE;MACvCU,OAAO,CAACC,KAAK,CAAC,+BAA+B,GAAGZ,SAAS,GAAG,2BAA2B,GAAG,gEAAgE,GAAG,yCAAyC,CAAC;MACvM,IAAIK,SAAS,GAAG,IAAI,CAACtB,eAAe,CAACiB,SAAS,CAAC;MAC/C,IAAIM,iBAAiB,GAAGD,SAAS,CAACE,MAAM,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAACC,QAAQ,CAAC,CAAC,KAAKR,QAAQ,CAACQ,QAAQ,CAAC,CAAC;MAAA,EAAC;MACnF,IAAI,CAAC1B,eAAe,CAACiB,SAAS,CAAC,GAAGM,iBAAiB;IACrD;EAAC;IAAAtB,GAAA;IAAAC,KAAA,EACD,SAAA4B,UAAUA,CAAA,EAAG;MACX,OAAOC,OAAO,CAACC,OAAO,CAAC,IAAI,CAAC;IAC9B;EAAC;IAAA/B,GAAA;IAAAC,KAAA,EACD,SAAA+B,aAAaA,CAAA,EAAG;MACd,OAAOF,OAAO,CAACC,OAAO,CAACrC,UAAU,CAAC;IACpC;EAAC;IAAAM,GAAA;IAAAC,KAAA,EAQD,SAAAgC,OAAOA,CAACC,GAAG,EAAEC,MAAM,EAAE;MACnB,IAAI9B,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;QAC1B6B,MAAM,GAAG,QAAQ;MACnB;MACA,IAAI;QACFC,IAAI,CAACF,GAAG,EAAEC,MAAM,CAAC;QACjB,IAAI,CAACjC,cAAc,CAAC,QAAQ,EAAEgC,GAAG,CAAC;QAClC,OAAOJ,OAAO,CAACC,OAAO,CAAC,CAAC;MAC1B,CAAC,CAAC,OAAOM,CAAC,EAAE;QACV,OAAOP,OAAO,CAACQ,MAAM,CAACD,CAAC,CAAC;MAC1B;IACF;EAAC;IAAArC,GAAA;IAAAC,KAAA,EACD,SAAAsC,YAAYA,CAACL,GAAG,EAAE;MAChB,CAAC,CAAC,EAAE1C,UAAU,CAACH,OAAO,EAAE,OAAO6C,GAAG,KAAK,QAAQ,EAAE,wCAAwC,GAAGA,GAAG,CAAC;MAChG,CAAC,CAAC,EAAE1C,UAAU,CAACH,OAAO,EAAE6C,GAAG,EAAE,8BAA8B,CAAC;IAC9D;EAAC;AAAA;AAEH,IAAIE,IAAI,GAAG,SAAPA,IAAIA,CAAIF,GAAG,EAAEC,MAAM,EAAK;EAC1B,IAAI1C,UAAU,CAACJ,OAAO,EAAE;IACtB,IAAImD,SAAS,GAAG,IAAIC,GAAG,CAACP,GAAG,EAAEvC,MAAM,CAACC,QAAQ,CAAC,CAAC6B,QAAQ,CAAC,CAAC;IACxD,IAAIe,SAAS,CAACE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;MACnC/C,MAAM,CAACC,QAAQ,GAAG4C,SAAS;IAC7B,CAAC,MAAM;MACL7C,MAAM,CAACyC,IAAI,CAACI,SAAS,EAAEL,MAAM,EAAE,UAAU,CAAC;IAC5C;EACF;AACF,CAAC;AACD,IAAIQ,QAAQ,GAAGrD,OAAO,CAACD,OAAO,GAAG,IAAIS,OAAO,CAAC,CAAC;AAC9C8C,MAAM,CAACtD,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}