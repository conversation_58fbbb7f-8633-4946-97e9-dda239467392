add0032988238818aafc98cd766cb8bf
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_61901ie07() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useNativeOptimization.ts";
  var hash = "768d848683364915323aec57beda412e9a547fdf";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useNativeOptimization.ts",
    statementMap: {
      "0": {
        start: {
          line: 87,
          column: 31
        },
        end: {
          line: 87,
          column: 52
        }
      },
      "1": {
        start: {
          line: 89,
          column: 30
        },
        end: {
          line: 98,
          column: 4
        }
      },
      "2": {
        start: {
          line: 100,
          column: 28
        },
        end: {
          line: 137,
          column: 4
        }
      },
      "3": {
        start: {
          line: 142,
          column: 21
        },
        end: {
          line: 188,
          column: 35
        }
      },
      "4": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 143,
          column: 36
        }
      },
      "5": {
        start: {
          line: 143,
          column: 29
        },
        end: {
          line: 143,
          column: 36
        }
      },
      "6": {
        start: {
          line: 145,
          column: 4
        },
        end: {
          line: 187,
          column: 5
        }
      },
      "7": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 58
        }
      },
      "8": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 55
        }
      },
      "9": {
        start: {
          line: 149,
          column: 43
        },
        end: {
          line: 149,
          column: 45
        }
      },
      "10": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 154,
          column: 7
        }
      },
      "11": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 153,
          column: 45
        }
      },
      "12": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 159,
          column: 7
        }
      },
      "13": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 158,
          column: 45
        }
      },
      "14": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 164,
          column: 7
        }
      },
      "15": {
        start: {
          line: 163,
          column: 8
        },
        end: {
          line: 163,
          column: 45
        }
      },
      "16": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 169,
          column: 7
        }
      },
      "17": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 45
        }
      },
      "18": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 38
        }
      },
      "19": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 32
        }
      },
      "20": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 180,
          column: 10
        }
      },
      "21": {
        start: {
          line: 176,
          column: 24
        },
        end: {
          line: 180,
          column: 7
        }
      },
      "22": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 74
        }
      },
      "23": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 185,
          column: 72
        }
      },
      "24": {
        start: {
          line: 186,
          column: 6
        },
        end: {
          line: 186,
          column: 59
        }
      },
      "25": {
        start: {
          line: 186,
          column: 24
        },
        end: {
          line: 186,
          column: 56
        }
      },
      "26": {
        start: {
          line: 193,
          column: 25
        },
        end: {
          line: 215,
          column: 57
        }
      },
      "27": {
        start: {
          line: 194,
          column: 4
        },
        end: {
          line: 197,
          column: 5
        }
      },
      "28": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 195,
          column: 53
        }
      },
      "29": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 18
        }
      },
      "30": {
        start: {
          line: 199,
          column: 4
        },
        end: {
          line: 214,
          column: 5
        }
      },
      "31": {
        start: {
          line: 200,
          column: 21
        },
        end: {
          line: 200,
          column: 74
        }
      },
      "32": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 207,
          column: 9
        }
      },
      "33": {
        start: {
          line: 209,
          column: 6
        },
        end: {
          line: 209,
          column: 20
        }
      },
      "34": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 58
        }
      },
      "35": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 18
        }
      },
      "36": {
        start: {
          line: 220,
          column: 33
        },
        end: {
          line: 242,
          column: 55
        }
      },
      "37": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 224,
          column: 5
        }
      },
      "38": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 51
        }
      },
      "39": {
        start: {
          line: 223,
          column: 6
        },
        end: {
          line: 223,
          column: 18
        }
      },
      "40": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 241,
          column: 5
        }
      },
      "41": {
        start: {
          line: 227,
          column: 21
        },
        end: {
          line: 227,
          column: 80
        }
      },
      "42": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 234,
          column: 9
        }
      },
      "43": {
        start: {
          line: 236,
          column: 6
        },
        end: {
          line: 236,
          column: 20
        }
      },
      "44": {
        start: {
          line: 239,
          column: 6
        },
        end: {
          line: 239,
          column: 66
        }
      },
      "45": {
        start: {
          line: 240,
          column: 6
        },
        end: {
          line: 240,
          column: 18
        }
      },
      "46": {
        start: {
          line: 247,
          column: 25
        },
        end: {
          line: 268,
          column: 87
        }
      },
      "47": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 251,
          column: 5
        }
      },
      "48": {
        start: {
          line: 249,
          column: 6
        },
        end: {
          line: 249,
          column: 63
        }
      },
      "49": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 250,
          column: 18
        }
      },
      "50": {
        start: {
          line: 253,
          column: 4
        },
        end: {
          line: 267,
          column: 5
        }
      },
      "51": {
        start: {
          line: 254,
          column: 21
        },
        end: {
          line: 254,
          column: 75
        }
      },
      "52": {
        start: {
          line: 256,
          column: 6
        },
        end: {
          line: 260,
          column: 9
        }
      },
      "53": {
        start: {
          line: 262,
          column: 6
        },
        end: {
          line: 262,
          column: 20
        }
      },
      "54": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 57
        }
      },
      "55": {
        start: {
          line: 266,
          column: 6
        },
        end: {
          line: 266,
          column: 18
        }
      },
      "56": {
        start: {
          line: 273,
          column: 33
        },
        end: {
          line: 290,
          column: 62
        }
      },
      "57": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 277,
          column: 5
        }
      },
      "58": {
        start: {
          line: 275,
          column: 6
        },
        end: {
          line: 275,
          column: 58
        }
      },
      "59": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 16
        }
      },
      "60": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 289,
          column: 5
        }
      },
      "61": {
        start: {
          line: 280,
          column: 21
        },
        end: {
          line: 280,
          column: 67
        }
      },
      "62": {
        start: {
          line: 281,
          column: 6
        },
        end: {
          line: 281,
          column: 55
        }
      },
      "63": {
        start: {
          line: 283,
          column: 6
        },
        end: {
          line: 283,
          column: 58
        }
      },
      "64": {
        start: {
          line: 284,
          column: 6
        },
        end: {
          line: 284,
          column: 20
        }
      },
      "65": {
        start: {
          line: 287,
          column: 6
        },
        end: {
          line: 287,
          column: 66
        }
      },
      "66": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 16
        }
      },
      "67": {
        start: {
          line: 295,
          column: 23
        },
        end: {
          line: 325,
          column: 57
        }
      },
      "68": {
        start: {
          line: 299,
          column: 4
        },
        end: {
          line: 302,
          column: 5
        }
      },
      "69": {
        start: {
          line: 300,
          column: 6
        },
        end: {
          line: 300,
          column: 72
        }
      },
      "70": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 18
        }
      },
      "71": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 324,
          column: 5
        }
      },
      "72": {
        start: {
          line: 305,
          column: 21
        },
        end: {
          line: 311,
          column: 8
        }
      },
      "73": {
        start: {
          line: 313,
          column: 6
        },
        end: {
          line: 317,
          column: 9
        }
      },
      "74": {
        start: {
          line: 319,
          column: 6
        },
        end: {
          line: 319,
          column: 20
        }
      },
      "75": {
        start: {
          line: 322,
          column: 6
        },
        end: {
          line: 322,
          column: 55
        }
      },
      "76": {
        start: {
          line: 323,
          column: 6
        },
        end: {
          line: 323,
          column: 18
        }
      },
      "77": {
        start: {
          line: 330,
          column: 25
        },
        end: {
          line: 360,
          column: 57
        }
      },
      "78": {
        start: {
          line: 334,
          column: 4
        },
        end: {
          line: 337,
          column: 5
        }
      },
      "79": {
        start: {
          line: 335,
          column: 6
        },
        end: {
          line: 335,
          column: 70
        }
      },
      "80": {
        start: {
          line: 336,
          column: 6
        },
        end: {
          line: 336,
          column: 18
        }
      },
      "81": {
        start: {
          line: 339,
          column: 4
        },
        end: {
          line: 359,
          column: 5
        }
      },
      "82": {
        start: {
          line: 340,
          column: 21
        },
        end: {
          line: 346,
          column: 8
        }
      },
      "83": {
        start: {
          line: 348,
          column: 6
        },
        end: {
          line: 352,
          column: 9
        }
      },
      "84": {
        start: {
          line: 354,
          column: 6
        },
        end: {
          line: 354,
          column: 20
        }
      },
      "85": {
        start: {
          line: 357,
          column: 6
        },
        end: {
          line: 357,
          column: 58
        }
      },
      "86": {
        start: {
          line: 358,
          column: 6
        },
        end: {
          line: 358,
          column: 18
        }
      },
      "87": {
        start: {
          line: 365,
          column: 27
        },
        end: {
          line: 386,
          column: 35
        }
      },
      "88": {
        start: {
          line: 366,
          column: 4
        },
        end: {
          line: 366,
          column: 42
        }
      },
      "89": {
        start: {
          line: 366,
          column: 30
        },
        end: {
          line: 366,
          column: 42
        }
      },
      "90": {
        start: {
          line: 368,
          column: 4
        },
        end: {
          line: 385,
          column: 5
        }
      },
      "91": {
        start: {
          line: 369,
          column: 22
        },
        end: {
          line: 379,
          column: 7
        }
      },
      "92": {
        start: {
          line: 381,
          column: 6
        },
        end: {
          line: 381,
          column: 21
        }
      },
      "93": {
        start: {
          line: 383,
          column: 6
        },
        end: {
          line: 383,
          column: 60
        }
      },
      "94": {
        start: {
          line: 384,
          column: 6
        },
        end: {
          line: 384,
          column: 18
        }
      },
      "95": {
        start: {
          line: 391,
          column: 23
        },
        end: {
          line: 393,
          column: 8
        }
      },
      "96": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 392,
          column: 51
        }
      },
      "97": {
        start: {
          line: 392,
          column: 23
        },
        end: {
          line: 392,
          column: 48
        }
      },
      "98": {
        start: {
          line: 398,
          column: 28
        },
        end: {
          line: 463,
          column: 14
        }
      },
      "99": {
        start: {
          line: 399,
          column: 4
        },
        end: {
          line: 462,
          column: 5
        }
      },
      "100": {
        start: {
          line: 400,
          column: 57
        },
        end: {
          line: 400,
          column: 59
        }
      },
      "101": {
        start: {
          line: 403,
          column: 6
        },
        end: {
          line: 412,
          column: 7
        }
      },
      "102": {
        start: {
          line: 404,
          column: 27
        },
        end: {
          line: 404,
          column: 76
        }
      },
      "103": {
        start: {
          line: 405,
          column: 8
        },
        end: {
          line: 411,
          column: 10
        }
      },
      "104": {
        start: {
          line: 415,
          column: 6
        },
        end: {
          line: 424,
          column: 7
        }
      },
      "105": {
        start: {
          line: 416,
          column: 30
        },
        end: {
          line: 416,
          column: 74
        }
      },
      "106": {
        start: {
          line: 417,
          column: 8
        },
        end: {
          line: 423,
          column: 10
        }
      },
      "107": {
        start: {
          line: 427,
          column: 6
        },
        end: {
          line: 437,
          column: 7
        }
      },
      "108": {
        start: {
          line: 428,
          column: 30
        },
        end: {
          line: 428,
          column: 70
        }
      },
      "109": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 436,
          column: 10
        }
      },
      "110": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 449,
          column: 7
        }
      },
      "111": {
        start: {
          line: 441,
          column: 26
        },
        end: {
          line: 441,
          column: 76
        }
      },
      "112": {
        start: {
          line: 442,
          column: 8
        },
        end: {
          line: 448,
          column: 10
        }
      },
      "113": {
        start: {
          line: 452,
          column: 6
        },
        end: {
          line: 456,
          column: 8
        }
      },
      "114": {
        start: {
          line: 458,
          column: 6
        },
        end: {
          line: 458,
          column: 51
        }
      },
      "115": {
        start: {
          line: 458,
          column: 24
        },
        end: {
          line: 458,
          column: 48
        }
      },
      "116": {
        start: {
          line: 461,
          column: 6
        },
        end: {
          line: 461,
          column: 61
        }
      },
      "117": {
        start: {
          line: 466,
          column: 40
        },
        end: {
          line: 482,
          column: 3
        }
      },
      "118": {
        start: {
          line: 467,
          column: 16
        },
        end: {
          line: 467,
          column: 17
        }
      },
      "119": {
        start: {
          line: 469,
          column: 4
        },
        end: {
          line: 471,
          column: 5
        }
      },
      "120": {
        start: {
          line: 470,
          column: 6
        },
        end: {
          line: 470,
          column: 53
        }
      },
      "121": {
        start: {
          line: 473,
          column: 4
        },
        end: {
          line: 475,
          column: 5
        }
      },
      "122": {
        start: {
          line: 474,
          column: 6
        },
        end: {
          line: 474,
          column: 57
        }
      },
      "123": {
        start: {
          line: 477,
          column: 4
        },
        end: {
          line: 479,
          column: 5
        }
      },
      "124": {
        start: {
          line: 478,
          column: 6
        },
        end: {
          line: 478,
          column: 70
        }
      },
      "125": {
        start: {
          line: 481,
          column: 4
        },
        end: {
          line: 481,
          column: 32
        }
      },
      "126": {
        start: {
          line: 484,
          column: 39
        },
        end: {
          line: 508,
          column: 3
        }
      },
      "127": {
        start: {
          line: 485,
          column: 16
        },
        end: {
          line: 485,
          column: 17
        }
      },
      "128": {
        start: {
          line: 488,
          column: 4
        },
        end: {
          line: 493,
          column: 5
        }
      },
      "129": {
        start: {
          line: 489,
          column: 26
        },
        end: {
          line: 491,
          column: 11
        }
      },
      "130": {
        start: {
          line: 492,
          column: 6
        },
        end: {
          line: 492,
          column: 32
        }
      },
      "131": {
        start: {
          line: 496,
          column: 4
        },
        end: {
          line: 498,
          column: 5
        }
      },
      "132": {
        start: {
          line: 497,
          column: 6
        },
        end: {
          line: 497,
          column: 67
        }
      },
      "133": {
        start: {
          line: 501,
          column: 4
        },
        end: {
          line: 505,
          column: 5
        }
      },
      "134": {
        start: {
          line: 502,
          column: 26
        },
        end: {
          line: 503,
          column: 86
        }
      },
      "135": {
        start: {
          line: 504,
          column: 6
        },
        end: {
          line: 504,
          column: 40
        }
      },
      "136": {
        start: {
          line: 507,
          column: 4
        },
        end: {
          line: 507,
          column: 32
        }
      },
      "137": {
        start: {
          line: 510,
          column: 35
        },
        end: {
          line: 531,
          column: 3
        }
      },
      "138": {
        start: {
          line: 512,
          column: 15
        },
        end: {
          line: 512,
          column: 16
        }
      },
      "139": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 517,
          column: 5
        }
      },
      "140": {
        start: {
          line: 516,
          column: 6
        },
        end: {
          line: 516,
          column: 52
        }
      },
      "141": {
        start: {
          line: 520,
          column: 4
        },
        end: {
          line: 522,
          column: 5
        }
      },
      "142": {
        start: {
          line: 521,
          column: 6
        },
        end: {
          line: 521,
          column: 59
        }
      },
      "143": {
        start: {
          line: 525,
          column: 4
        },
        end: {
          line: 528,
          column: 5
        }
      },
      "144": {
        start: {
          line: 526,
          column: 25
        },
        end: {
          line: 526,
          column: 85
        }
      },
      "145": {
        start: {
          line: 527,
          column: 6
        },
        end: {
          line: 527,
          column: 25
        }
      },
      "146": {
        start: {
          line: 530,
          column: 4
        },
        end: {
          line: 530,
          column: 31
        }
      },
      "147": {
        start: {
          line: 534,
          column: 2
        },
        end: {
          line: 549,
          column: 98
        }
      },
      "148": {
        start: {
          line: 535,
          column: 4
        },
        end: {
          line: 535,
          column: 65
        }
      },
      "149": {
        start: {
          line: 535,
          column: 58
        },
        end: {
          line: 535,
          column: 65
        }
      },
      "150": {
        start: {
          line: 537,
          column: 21
        },
        end: {
          line: 546,
          column: 35
        }
      },
      "151": {
        start: {
          line: 539,
          column: 6
        },
        end: {
          line: 541,
          column: 7
        }
      },
      "152": {
        start: {
          line: 540,
          column: 8
        },
        end: {
          line: 540,
          column: 36
        }
      },
      "153": {
        start: {
          line: 543,
          column: 6
        },
        end: {
          line: 545,
          column: 7
        }
      },
      "154": {
        start: {
          line: 544,
          column: 8
        },
        end: {
          line: 544,
          column: 62
        }
      },
      "155": {
        start: {
          line: 548,
          column: 4
        },
        end: {
          line: 548,
          column: 41
        }
      },
      "156": {
        start: {
          line: 548,
          column: 17
        },
        end: {
          line: 548,
          column: 40
        }
      },
      "157": {
        start: {
          line: 552,
          column: 2
        },
        end: {
          line: 560,
          column: 47
        }
      },
      "158": {
        start: {
          line: 553,
          column: 4
        },
        end: {
          line: 553,
          column: 37
        }
      },
      "159": {
        start: {
          line: 553,
          column: 30
        },
        end: {
          line: 553,
          column: 37
        }
      },
      "160": {
        start: {
          line: 555,
          column: 21
        },
        end: {
          line: 557,
          column: 13
        }
      },
      "161": {
        start: {
          line: 556,
          column: 6
        },
        end: {
          line: 556,
          column: 26
        }
      },
      "162": {
        start: {
          line: 559,
          column: 4
        },
        end: {
          line: 559,
          column: 41
        }
      },
      "163": {
        start: {
          line: 559,
          column: 17
        },
        end: {
          line: 559,
          column: 40
        }
      },
      "164": {
        start: {
          line: 563,
          column: 2
        },
        end: {
          line: 567,
          column: 40
        }
      },
      "165": {
        start: {
          line: 564,
          column: 4
        },
        end: {
          line: 566,
          column: 5
        }
      },
      "166": {
        start: {
          line: 565,
          column: 6
        },
        end: {
          line: 565,
          column: 19
        }
      },
      "167": {
        start: {
          line: 570,
          column: 2
        },
        end: {
          line: 596,
          column: 5
        }
      },
      "168": {
        start: {
          line: 570,
          column: 24
        },
        end: {
          line: 584,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "useNativeOptimization",
        decl: {
          start: {
            line: 84,
            column: 16
          },
          end: {
            line: 84,
            column: 37
          }
        },
        loc: {
          start: {
            line: 86,
            column: 31
          },
          end: {
            line: 597,
            column: 1
          }
        },
        line: 86
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 142,
            column: 33
          },
          end: {
            line: 142,
            column: 34
          }
        },
        loc: {
          start: {
            line: 142,
            column: 45
          },
          end: {
            line: 188,
            column: 3
          }
        },
        line: 142
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 146,
            column: 15
          },
          end: {
            line: 146,
            column: 16
          }
        },
        loc: {
          start: {
            line: 146,
            column: 24
          },
          end: {
            line: 146,
            column: 55
          }
        },
        line: 146
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 176,
            column: 15
          },
          end: {
            line: 176,
            column: 16
          }
        },
        loc: {
          start: {
            line: 176,
            column: 24
          },
          end: {
            line: 180,
            column: 7
          }
        },
        line: 176
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 186,
            column: 15
          },
          end: {
            line: 186,
            column: 16
          }
        },
        loc: {
          start: {
            line: 186,
            column: 24
          },
          end: {
            line: 186,
            column: 56
          }
        },
        line: 186
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 193,
            column: 37
          },
          end: {
            line: 193,
            column: 38
          }
        },
        loc: {
          start: {
            line: 193,
            column: 58
          },
          end: {
            line: 215,
            column: 3
          }
        },
        line: 193
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 220,
            column: 45
          },
          end: {
            line: 220,
            column: 46
          }
        },
        loc: {
          start: {
            line: 220,
            column: 71
          },
          end: {
            line: 242,
            column: 3
          }
        },
        line: 220
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 247,
            column: 37
          },
          end: {
            line: 247,
            column: 38
          }
        },
        loc: {
          start: {
            line: 247,
            column: 100
          },
          end: {
            line: 268,
            column: 3
          }
        },
        line: 247
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 273,
            column: 45
          },
          end: {
            line: 273,
            column: 46
          }
        },
        loc: {
          start: {
            line: 273,
            column: 60
          },
          end: {
            line: 290,
            column: 3
          }
        },
        line: 273
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 295,
            column: 35
          },
          end: {
            line: 295,
            column: 36
          }
        },
        loc: {
          start: {
            line: 298,
            column: 7
          },
          end: {
            line: 325,
            column: 3
          }
        },
        line: 298
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 330,
            column: 37
          },
          end: {
            line: 330,
            column: 38
          }
        },
        loc: {
          start: {
            line: 333,
            column: 7
          },
          end: {
            line: 360,
            column: 3
          }
        },
        line: 333
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 365,
            column: 39
          },
          end: {
            line: 365,
            column: 40
          }
        },
        loc: {
          start: {
            line: 365,
            column: 51
          },
          end: {
            line: 386,
            column: 3
          }
        },
        line: 365
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 391,
            column: 35
          },
          end: {
            line: 391,
            column: 36
          }
        },
        loc: {
          start: {
            line: 391,
            column: 85
          },
          end: {
            line: 393,
            column: 3
          }
        },
        line: 391
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 392,
            column: 14
          },
          end: {
            line: 392,
            column: 15
          }
        },
        loc: {
          start: {
            line: 392,
            column: 23
          },
          end: {
            line: 392,
            column: 48
          }
        },
        line: 392
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 398,
            column: 40
          },
          end: {
            line: 398,
            column: 41
          }
        },
        loc: {
          start: {
            line: 398,
            column: 52
          },
          end: {
            line: 463,
            column: 3
          }
        },
        line: 398
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 458,
            column: 15
          },
          end: {
            line: 458,
            column: 16
          }
        },
        loc: {
          start: {
            line: 458,
            column: 24
          },
          end: {
            line: 458,
            column: 48
          }
        },
        line: 458
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 466,
            column: 40
          },
          end: {
            line: 466,
            column: 41
          }
        },
        loc: {
          start: {
            line: 466,
            column: 54
          },
          end: {
            line: 482,
            column: 3
          }
        },
        line: 466
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 484,
            column: 39
          },
          end: {
            line: 484,
            column: 40
          }
        },
        loc: {
          start: {
            line: 484,
            column: 53
          },
          end: {
            line: 508,
            column: 3
          }
        },
        line: 484
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 510,
            column: 35
          },
          end: {
            line: 510,
            column: 36
          }
        },
        loc: {
          start: {
            line: 510,
            column: 49
          },
          end: {
            line: 531,
            column: 3
          }
        },
        line: 510
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 534,
            column: 12
          },
          end: {
            line: 534,
            column: 13
          }
        },
        loc: {
          start: {
            line: 534,
            column: 18
          },
          end: {
            line: 549,
            column: 3
          }
        },
        line: 534
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 537,
            column: 33
          },
          end: {
            line: 537,
            column: 34
          }
        },
        loc: {
          start: {
            line: 537,
            column: 45
          },
          end: {
            line: 546,
            column: 5
          }
        },
        line: 537
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 548,
            column: 11
          },
          end: {
            line: 548,
            column: 12
          }
        },
        loc: {
          start: {
            line: 548,
            column: 17
          },
          end: {
            line: 548,
            column: 40
          }
        },
        line: 548
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 552,
            column: 12
          },
          end: {
            line: 552,
            column: 13
          }
        },
        loc: {
          start: {
            line: 552,
            column: 18
          },
          end: {
            line: 560,
            column: 3
          }
        },
        line: 552
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 555,
            column: 33
          },
          end: {
            line: 555,
            column: 34
          }
        },
        loc: {
          start: {
            line: 555,
            column: 39
          },
          end: {
            line: 557,
            column: 5
          }
        },
        line: 555
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 559,
            column: 11
          },
          end: {
            line: 559,
            column: 12
          }
        },
        loc: {
          start: {
            line: 559,
            column: 17
          },
          end: {
            line: 559,
            column: 40
          }
        },
        line: 559
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 563,
            column: 12
          },
          end: {
            line: 563,
            column: 13
          }
        },
        loc: {
          start: {
            line: 563,
            column: 18
          },
          end: {
            line: 567,
            column: 3
          }
        },
        line: 563
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 570,
            column: 17
          },
          end: {
            line: 570,
            column: 18
          }
        },
        loc: {
          start: {
            line: 570,
            column: 24
          },
          end: {
            line: 584,
            column: 3
          }
        },
        line: 570
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 85,
            column: 2
          },
          end: {
            line: 85,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 85,
            column: 53
          },
          end: {
            line: 85,
            column: 55
          }
        }],
        line: 85
      },
      "1": {
        loc: {
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 143,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 4
          },
          end: {
            line: 143,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "2": {
        loc: {
          start: {
            line: 151,
            column: 6
          },
          end: {
            line: 154,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 6
          },
          end: {
            line: 154,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "3": {
        loc: {
          start: {
            line: 156,
            column: 6
          },
          end: {
            line: 159,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 6
          },
          end: {
            line: 159,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "4": {
        loc: {
          start: {
            line: 161,
            column: 6
          },
          end: {
            line: 164,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 6
          },
          end: {
            line: 164,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "5": {
        loc: {
          start: {
            line: 166,
            column: 6
          },
          end: {
            line: 169,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 6
          },
          end: {
            line: 169,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "6": {
        loc: {
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 197,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 197,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "7": {
        loc: {
          start: {
            line: 194,
            column: 8
          },
          end: {
            line: 194,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 194,
            column: 8
          },
          end: {
            line: 194,
            column: 28
          }
        }, {
          start: {
            line: 194,
            column: 32
          },
          end: {
            line: 194,
            column: 61
          }
        }],
        line: 194
      },
      "8": {
        loc: {
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 224,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 224,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "9": {
        loc: {
          start: {
            line: 221,
            column: 8
          },
          end: {
            line: 221,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 221,
            column: 8
          },
          end: {
            line: 221,
            column: 28
          }
        }, {
          start: {
            line: 221,
            column: 32
          },
          end: {
            line: 221,
            column: 59
          }
        }],
        line: 221
      },
      "10": {
        loc: {
          start: {
            line: 247,
            column: 44
          },
          end: {
            line: 247,
            column: 95
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 247,
            column: 66
          },
          end: {
            line: 247,
            column: 95
          }
        }],
        line: 247
      },
      "11": {
        loc: {
          start: {
            line: 248,
            column: 4
          },
          end: {
            line: 251,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 248,
            column: 4
          },
          end: {
            line: 251,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 248
      },
      "12": {
        loc: {
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 248,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 248,
            column: 28
          }
        }, {
          start: {
            line: 248,
            column: 32
          },
          end: {
            line: 248,
            column: 60
          }
        }],
        line: 248
      },
      "13": {
        loc: {
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 277,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 274,
            column: 4
          },
          end: {
            line: 277,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 274
      },
      "14": {
        loc: {
          start: {
            line: 274,
            column: 8
          },
          end: {
            line: 274,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 274,
            column: 8
          },
          end: {
            line: 274,
            column: 28
          }
        }, {
          start: {
            line: 274,
            column: 32
          },
          end: {
            line: 274,
            column: 66
          }
        }],
        line: 274
      },
      "15": {
        loc: {
          start: {
            line: 299,
            column: 4
          },
          end: {
            line: 302,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 299,
            column: 4
          },
          end: {
            line: 302,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 299
      },
      "16": {
        loc: {
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 299,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 299,
            column: 8
          },
          end: {
            line: 299,
            column: 28
          }
        }, {
          start: {
            line: 299,
            column: 32
          },
          end: {
            line: 299,
            column: 61
          }
        }],
        line: 299
      },
      "17": {
        loc: {
          start: {
            line: 334,
            column: 4
          },
          end: {
            line: 337,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 334,
            column: 4
          },
          end: {
            line: 337,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 334
      },
      "18": {
        loc: {
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 334,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 334,
            column: 8
          },
          end: {
            line: 334,
            column: 28
          }
        }, {
          start: {
            line: 334,
            column: 32
          },
          end: {
            line: 334,
            column: 61
          }
        }],
        line: 334
      },
      "19": {
        loc: {
          start: {
            line: 366,
            column: 4
          },
          end: {
            line: 366,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 366,
            column: 4
          },
          end: {
            line: 366,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 366
      },
      "20": {
        loc: {
          start: {
            line: 370,
            column: 13
          },
          end: {
            line: 370,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 370,
            column: 44
          },
          end: {
            line: 370,
            column: 93
          }
        }, {
          start: {
            line: 370,
            column: 96
          },
          end: {
            line: 370,
            column: 100
          }
        }],
        line: 370
      },
      "21": {
        loc: {
          start: {
            line: 371,
            column: 23
          },
          end: {
            line: 371,
            column: 103
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 371,
            column: 52
          },
          end: {
            line: 371,
            column: 96
          }
        }, {
          start: {
            line: 371,
            column: 99
          },
          end: {
            line: 371,
            column: 103
          }
        }],
        line: 371
      },
      "22": {
        loc: {
          start: {
            line: 372,
            column: 16
          },
          end: {
            line: 372,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 372,
            column: 46
          },
          end: {
            line: 372,
            column: 86
          }
        }, {
          start: {
            line: 372,
            column: 89
          },
          end: {
            line: 372,
            column: 93
          }
        }],
        line: 372
      },
      "23": {
        loc: {
          start: {
            line: 373,
            column: 30
          },
          end: {
            line: 373,
            column: 123
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 373,
            column: 66
          },
          end: {
            line: 373,
            column: 116
          }
        }, {
          start: {
            line: 373,
            column: 119
          },
          end: {
            line: 373,
            column: 123
          }
        }],
        line: 373
      },
      "24": {
        loc: {
          start: {
            line: 403,
            column: 6
          },
          end: {
            line: 412,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 403,
            column: 6
          },
          end: {
            line: 412,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 403
      },
      "25": {
        loc: {
          start: {
            line: 415,
            column: 6
          },
          end: {
            line: 424,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 6
          },
          end: {
            line: 424,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "26": {
        loc: {
          start: {
            line: 427,
            column: 6
          },
          end: {
            line: 437,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 427,
            column: 6
          },
          end: {
            line: 437,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 427
      },
      "27": {
        loc: {
          start: {
            line: 440,
            column: 6
          },
          end: {
            line: 449,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 440,
            column: 6
          },
          end: {
            line: 449,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 440
      },
      "28": {
        loc: {
          start: {
            line: 469,
            column: 4
          },
          end: {
            line: 471,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 469,
            column: 4
          },
          end: {
            line: 471,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 469
      },
      "29": {
        loc: {
          start: {
            line: 469,
            column: 8
          },
          end: {
            line: 469,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 469,
            column: 8
          },
          end: {
            line: 469,
            column: 36
          }
        }, {
          start: {
            line: 469,
            column: 40
          },
          end: {
            line: 469,
            column: 71
          }
        }],
        line: 469
      },
      "30": {
        loc: {
          start: {
            line: 473,
            column: 4
          },
          end: {
            line: 475,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 473,
            column: 4
          },
          end: {
            line: 475,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 473
      },
      "31": {
        loc: {
          start: {
            line: 477,
            column: 4
          },
          end: {
            line: 479,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 477,
            column: 4
          },
          end: {
            line: 479,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 477
      },
      "32": {
        loc: {
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 488
      },
      "33": {
        loc: {
          start: {
            line: 489,
            column: 26
          },
          end: {
            line: 491,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 490,
            column: 10
          },
          end: {
            line: 490,
            column: 81
          }
        }, {
          start: {
            line: 491,
            column: 10
          },
          end: {
            line: 491,
            column: 11
          }
        }],
        line: 489
      },
      "34": {
        loc: {
          start: {
            line: 496,
            column: 4
          },
          end: {
            line: 498,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 496,
            column: 4
          },
          end: {
            line: 498,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 496
      },
      "35": {
        loc: {
          start: {
            line: 501,
            column: 4
          },
          end: {
            line: 505,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 501,
            column: 4
          },
          end: {
            line: 505,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 501
      },
      "36": {
        loc: {
          start: {
            line: 502,
            column: 26
          },
          end: {
            line: 503,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 502,
            column: 79
          },
          end: {
            line: 502,
            column: 82
          }
        }, {
          start: {
            line: 503,
            column: 25
          },
          end: {
            line: 503,
            column: 86
          }
        }],
        line: 502
      },
      "37": {
        loc: {
          start: {
            line: 503,
            column: 25
          },
          end: {
            line: 503,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 503,
            column: 79
          },
          end: {
            line: 503,
            column: 81
          }
        }, {
          start: {
            line: 503,
            column: 84
          },
          end: {
            line: 503,
            column: 86
          }
        }],
        line: 503
      },
      "38": {
        loc: {
          start: {
            line: 515,
            column: 4
          },
          end: {
            line: 517,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 515,
            column: 4
          },
          end: {
            line: 517,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 515
      },
      "39": {
        loc: {
          start: {
            line: 515,
            column: 8
          },
          end: {
            line: 515,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 515,
            column: 8
          },
          end: {
            line: 515,
            column: 36
          }
        }, {
          start: {
            line: 515,
            column: 40
          },
          end: {
            line: 515,
            column: 71
          }
        }],
        line: 515
      },
      "40": {
        loc: {
          start: {
            line: 520,
            column: 4
          },
          end: {
            line: 522,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 520,
            column: 4
          },
          end: {
            line: 522,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 520
      },
      "41": {
        loc: {
          start: {
            line: 525,
            column: 4
          },
          end: {
            line: 528,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 525,
            column: 4
          },
          end: {
            line: 528,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 525
      },
      "42": {
        loc: {
          start: {
            line: 526,
            column: 25
          },
          end: {
            line: 526,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 526,
            column: 78
          },
          end: {
            line: 526,
            column: 80
          }
        }, {
          start: {
            line: 526,
            column: 83
          },
          end: {
            line: 526,
            column: 85
          }
        }],
        line: 526
      },
      "43": {
        loc: {
          start: {
            line: 535,
            column: 4
          },
          end: {
            line: 535,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 535,
            column: 4
          },
          end: {
            line: 535,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 535
      },
      "44": {
        loc: {
          start: {
            line: 535,
            column: 8
          },
          end: {
            line: 535,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 535,
            column: 8
          },
          end: {
            line: 535,
            column: 32
          }
        }, {
          start: {
            line: 535,
            column: 36
          },
          end: {
            line: 535,
            column: 56
          }
        }],
        line: 535
      },
      "45": {
        loc: {
          start: {
            line: 539,
            column: 6
          },
          end: {
            line: 541,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 539,
            column: 6
          },
          end: {
            line: 541,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 539
      },
      "46": {
        loc: {
          start: {
            line: 539,
            column: 10
          },
          end: {
            line: 539,
            column: 91
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 539,
            column: 10
          },
          end: {
            line: 539,
            column: 37
          }
        }, {
          start: {
            line: 539,
            column: 41
          },
          end: {
            line: 539,
            column: 91
          }
        }],
        line: 539
      },
      "47": {
        loc: {
          start: {
            line: 543,
            column: 6
          },
          end: {
            line: 545,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 543,
            column: 6
          },
          end: {
            line: 545,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 543
      },
      "48": {
        loc: {
          start: {
            line: 553,
            column: 4
          },
          end: {
            line: 553,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 553,
            column: 4
          },
          end: {
            line: 553,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 553
      },
      "49": {
        loc: {
          start: {
            line: 564,
            column: 4
          },
          end: {
            line: 566,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 564,
            column: 4
          },
          end: {
            line: 566,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 564
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "768d848683364915323aec57beda412e9a547fdf"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_61901ie07 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_61901ie07();
import { useState, useEffect, useCallback, useMemo } from 'react';
import { Platform } from 'react-native';
import { gpuAccelerationManager } from "../services/native/GPUAccelerationManager";
import { nativeModuleManager } from "../services/native/NativeModuleManager";
import { advancedMemoryManager } from "../services/native/AdvancedMemoryManager";
import { backgroundProcessingManager } from "../services/native/BackgroundProcessingManager";
import { useEdgeOptimization } from "./useEdgeOptimization";
export function useNativeOptimization() {
  var initialConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_61901ie07().b[0][0]++, {});
  cov_61901ie07().f[0]++;
  var _ref = (cov_61901ie07().s[0]++, useEdgeOptimization()),
    edgeState = _ref.state;
  var _ref2 = (cov_61901ie07().s[1]++, useState(Object.assign({
      enableGPUAcceleration: true,
      enableNativeModules: true,
      enableAdvancedMemory: true,
      enableBackgroundProcessing: true,
      autoOptimization: true,
      optimizationInterval: 120000,
      aggressiveOptimization: false
    }, initialConfig))),
    _ref3 = _slicedToArray(_ref2, 2),
    config = _ref3[0],
    setConfig = _ref3[1];
  var _ref4 = (cov_61901ie07().s[2]++, useState({
      isInitialized: false,
      isOptimizing: false,
      gpuAcceleration: {
        available: false,
        utilization: 0,
        averageExecutionTime: 0,
        tasksPerSecond: 0,
        efficiency: 0
      },
      nativeModules: {
        totalModules: 0,
        availableModules: 0,
        averageExecutionTime: 0,
        successRate: 0,
        bridgeEfficiency: 0
      },
      memoryManagement: {
        totalAllocated: 0,
        totalAvailable: 0,
        memoryPressure: 'normal',
        leakCount: 0,
        gcEfficiency: 0,
        fragmentationRatio: 0
      },
      backgroundProcessing: {
        activeTaskCount: 0,
        queuedTaskCount: 0,
        totalTasksExecuted: 0,
        successRate: 0,
        currentStrategy: 'balanced'
      },
      platformOptimization: {
        hardwareAcceleration: 0,
        platformIntegration: 0,
        performanceGain: 0
      }
    })),
    _ref5 = _slicedToArray(_ref4, 2),
    state = _ref5[0],
    setState = _ref5[1];
  var initialize = (cov_61901ie07().s[3]++, useCallback(_asyncToGenerator(function* () {
    cov_61901ie07().f[1]++;
    cov_61901ie07().s[4]++;
    if (state.isInitialized) {
      cov_61901ie07().b[1][0]++;
      cov_61901ie07().s[5]++;
      return;
    } else {
      cov_61901ie07().b[1][1]++;
    }
    cov_61901ie07().s[6]++;
    try {
      cov_61901ie07().s[7]++;
      setState(function (prev) {
        cov_61901ie07().f[2]++;
        cov_61901ie07().s[8]++;
        return Object.assign({}, prev, {
          isOptimizing: true
        });
      });
      var initPromises = (cov_61901ie07().s[9]++, []);
      cov_61901ie07().s[10]++;
      if (config.enableGPUAcceleration) {
        cov_61901ie07().b[2][0]++;
        cov_61901ie07().s[11]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_61901ie07().b[2][1]++;
      }
      cov_61901ie07().s[12]++;
      if (config.enableNativeModules) {
        cov_61901ie07().b[3][0]++;
        cov_61901ie07().s[13]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_61901ie07().b[3][1]++;
      }
      cov_61901ie07().s[14]++;
      if (config.enableAdvancedMemory) {
        cov_61901ie07().b[4][0]++;
        cov_61901ie07().s[15]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_61901ie07().b[4][1]++;
      }
      cov_61901ie07().s[16]++;
      if (config.enableBackgroundProcessing) {
        cov_61901ie07().b[5][0]++;
        cov_61901ie07().s[17]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_61901ie07().b[5][1]++;
      }
      cov_61901ie07().s[18]++;
      yield Promise.all(initPromises);
      cov_61901ie07().s[19]++;
      yield updateNativeState();
      cov_61901ie07().s[20]++;
      setState(function (prev) {
        cov_61901ie07().f[3]++;
        cov_61901ie07().s[21]++;
        return Object.assign({}, prev, {
          isInitialized: true,
          isOptimizing: false
        });
      });
      cov_61901ie07().s[22]++;
      console.log('Native Optimization systems initialized successfully');
    } catch (error) {
      cov_61901ie07().s[23]++;
      console.error('Failed to initialize native optimization:', error);
      cov_61901ie07().s[24]++;
      setState(function (prev) {
        cov_61901ie07().f[4]++;
        cov_61901ie07().s[25]++;
        return Object.assign({}, prev, {
          isOptimizing: false
        });
      });
    }
  }), [state.isInitialized, config]));
  var executeGPUTask = (cov_61901ie07().s[26]++, useCallback(function () {
    var _ref7 = _asyncToGenerator(function* (task) {
      cov_61901ie07().f[5]++;
      cov_61901ie07().s[27]++;
      if ((cov_61901ie07().b[7][0]++, !state.isInitialized) || (cov_61901ie07().b[7][1]++, !config.enableGPUAcceleration)) {
        cov_61901ie07().b[6][0]++;
        cov_61901ie07().s[28]++;
        console.warn('GPU acceleration not available');
        cov_61901ie07().s[29]++;
        return null;
      } else {
        cov_61901ie07().b[6][1]++;
      }
      cov_61901ie07().s[30]++;
      try {
        var result = (cov_61901ie07().s[31]++, yield gpuAccelerationManager.executeComputeTask(task));
        cov_61901ie07().s[32]++;
        console.log(`GPU task executed:`, {
          success: result.success,
          executionTime: result.executionTime,
          processingUnit: result.processingUnit,
          efficiency: result.performance.efficiency
        });
        cov_61901ie07().s[33]++;
        return result;
      } catch (error) {
        cov_61901ie07().s[34]++;
        console.error('Failed to execute GPU task:', error);
        cov_61901ie07().s[35]++;
        return null;
      }
    });
    return function (_x) {
      return _ref7.apply(this, arguments);
    };
  }(), [state.isInitialized, config.enableGPUAcceleration]));
  var executeNativeOperation = (cov_61901ie07().s[36]++, useCallback(function () {
    var _ref8 = _asyncToGenerator(function* (operation) {
      cov_61901ie07().f[6]++;
      cov_61901ie07().s[37]++;
      if ((cov_61901ie07().b[9][0]++, !state.isInitialized) || (cov_61901ie07().b[9][1]++, !config.enableNativeModules)) {
        cov_61901ie07().b[8][0]++;
        cov_61901ie07().s[38]++;
        console.warn('Native modules not available');
        cov_61901ie07().s[39]++;
        return null;
      } else {
        cov_61901ie07().b[8][1]++;
      }
      cov_61901ie07().s[40]++;
      try {
        var result = (cov_61901ie07().s[41]++, yield nativeModuleManager.executeNativeOperation(operation));
        cov_61901ie07().s[42]++;
        console.log(`Native operation executed:`, {
          success: result.success,
          executionTime: result.executionTime,
          nativeModule: result.nativeModule,
          method: result.method
        });
        cov_61901ie07().s[43]++;
        return result;
      } catch (error) {
        cov_61901ie07().s[44]++;
        console.error('Failed to execute native operation:', error);
        cov_61901ie07().s[45]++;
        return null;
      }
    });
    return function (_x2) {
      return _ref8.apply(this, arguments);
    };
  }(), [state.isInitialized, config.enableNativeModules]));
  var optimizeMemory = (cov_61901ie07().s[46]++, useCallback(_asyncToGenerator(function* () {
    var aggressive = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_61901ie07().b[10][0]++, config.aggressiveOptimization);
    cov_61901ie07().f[7]++;
    cov_61901ie07().s[47]++;
    if ((cov_61901ie07().b[12][0]++, !state.isInitialized) || (cov_61901ie07().b[12][1]++, !config.enableAdvancedMemory)) {
      cov_61901ie07().b[11][0]++;
      cov_61901ie07().s[48]++;
      console.warn('Advanced memory management not available');
      cov_61901ie07().s[49]++;
      return null;
    } else {
      cov_61901ie07().b[11][1]++;
    }
    cov_61901ie07().s[50]++;
    try {
      var result = (cov_61901ie07().s[51]++, yield advancedMemoryManager.optimizeMemory(aggressive));
      cov_61901ie07().s[52]++;
      console.log(`Memory optimization completed:`, {
        freedMemory: result.freedMemory,
        optimizedPools: result.optimizedPools,
        leaksResolved: result.leaksResolved
      });
      cov_61901ie07().s[53]++;
      return result;
    } catch (error) {
      cov_61901ie07().s[54]++;
      console.error('Failed to optimize memory:', error);
      cov_61901ie07().s[55]++;
      return null;
    }
  }), [state.isInitialized, config.enableAdvancedMemory, config.aggressiveOptimization]));
  var scheduleBackgroundTask = (cov_61901ie07().s[56]++, useCallback(function (task) {
    cov_61901ie07().f[8]++;
    cov_61901ie07().s[57]++;
    if ((cov_61901ie07().b[14][0]++, !state.isInitialized) || (cov_61901ie07().b[14][1]++, !config.enableBackgroundProcessing)) {
      cov_61901ie07().b[13][0]++;
      cov_61901ie07().s[58]++;
      console.warn('Background processing not available');
      cov_61901ie07().s[59]++;
      return '';
    } else {
      cov_61901ie07().b[13][1]++;
    }
    cov_61901ie07().s[60]++;
    try {
      var taskId = (cov_61901ie07().s[61]++, backgroundProcessingManager.registerTask(task));
      cov_61901ie07().s[62]++;
      backgroundProcessingManager.scheduleTask(taskId);
      cov_61901ie07().s[63]++;
      console.log(`Background task scheduled: ${taskId}`);
      cov_61901ie07().s[64]++;
      return taskId;
    } catch (error) {
      cov_61901ie07().s[65]++;
      console.error('Failed to schedule background task:', error);
      cov_61901ie07().s[66]++;
      return '';
    }
  }, [state.isInitialized, config.enableBackgroundProcessing]));
  var analyzeVideo = (cov_61901ie07().s[67]++, useCallback(function () {
    var _ref0 = _asyncToGenerator(function* (videoData, analysisType) {
      cov_61901ie07().f[9]++;
      cov_61901ie07().s[68]++;
      if ((cov_61901ie07().b[16][0]++, !state.isInitialized) || (cov_61901ie07().b[16][1]++, !config.enableGPUAcceleration)) {
        cov_61901ie07().b[15][0]++;
        cov_61901ie07().s[69]++;
        console.warn('GPU acceleration not available for video analysis');
        cov_61901ie07().s[70]++;
        return null;
      } else {
        cov_61901ie07().b[15][1]++;
      }
      cov_61901ie07().s[71]++;
      try {
        var result = (cov_61901ie07().s[72]++, yield gpuAccelerationManager.analyzeVideo({
          videoData: videoData,
          analysisType: analysisType,
          frameRate: 30,
          resolution: {
            width: 1920,
            height: 1080
          },
          outputFormat: 'keypoints'
        }));
        cov_61901ie07().s[73]++;
        console.log(`Video analysis completed:`, {
          success: result.success,
          executionTime: result.executionTime,
          processingUnit: result.processingUnit
        });
        cov_61901ie07().s[74]++;
        return result;
      } catch (error) {
        cov_61901ie07().s[75]++;
        console.error('Failed to analyze video:', error);
        cov_61901ie07().s[76]++;
        return null;
      }
    });
    return function (_x3, _x4) {
      return _ref0.apply(this, arguments);
    };
  }(), [state.isInitialized, config.enableGPUAcceleration]));
  var runMLInference = (cov_61901ie07().s[77]++, useCallback(function () {
    var _ref1 = _asyncToGenerator(function* (modelId, inputData) {
      cov_61901ie07().f[10]++;
      cov_61901ie07().s[78]++;
      if ((cov_61901ie07().b[18][0]++, !state.isInitialized) || (cov_61901ie07().b[18][1]++, !config.enableGPUAcceleration)) {
        cov_61901ie07().b[17][0]++;
        cov_61901ie07().s[79]++;
        console.warn('GPU acceleration not available for ML inference');
        cov_61901ie07().s[80]++;
        return null;
      } else {
        cov_61901ie07().b[17][1]++;
      }
      cov_61901ie07().s[81]++;
      try {
        var result = (cov_61901ie07().s[82]++, yield gpuAccelerationManager.runMLInference({
          modelId: modelId,
          inputTensor: inputData,
          inputShape: [1, inputData.length],
          outputShape: [1, 10],
          precision: 'fp32'
        }));
        cov_61901ie07().s[83]++;
        console.log(`ML inference completed:`, {
          success: result.success,
          executionTime: result.executionTime,
          accuracy: result.performance.accuracy
        });
        cov_61901ie07().s[84]++;
        return result;
      } catch (error) {
        cov_61901ie07().s[85]++;
        console.error('Failed to run ML inference:', error);
        cov_61901ie07().s[86]++;
        return null;
      }
    });
    return function (_x5, _x6) {
      return _ref1.apply(this, arguments);
    };
  }(), [state.isInitialized, config.enableGPUAcceleration]));
  var getNativeMetrics = (cov_61901ie07().s[87]++, useCallback(_asyncToGenerator(function* () {
    cov_61901ie07().f[11]++;
    cov_61901ie07().s[88]++;
    if (!state.isInitialized) {
      cov_61901ie07().b[19][0]++;
      cov_61901ie07().s[89]++;
      return null;
    } else {
      cov_61901ie07().b[19][1]++;
    }
    cov_61901ie07().s[90]++;
    try {
      var metrics = (cov_61901ie07().s[91]++, {
        gpu: config.enableGPUAcceleration ? (cov_61901ie07().b[20][0]++, gpuAccelerationManager.getGPUPerformanceMetrics()) : (cov_61901ie07().b[20][1]++, null),
        nativeModules: config.enableNativeModules ? (cov_61901ie07().b[21][0]++, nativeModuleManager.getNativeModuleMetrics()) : (cov_61901ie07().b[21][1]++, null),
        memory: config.enableAdvancedMemory ? (cov_61901ie07().b[22][0]++, advancedMemoryManager.getMemoryMetrics()) : (cov_61901ie07().b[22][1]++, null),
        backgroundProcessing: config.enableBackgroundProcessing ? (cov_61901ie07().b[23][0]++, backgroundProcessingManager.getProcessingMetrics()) : (cov_61901ie07().b[23][1]++, null),
        platform: {
          os: Platform.OS,
          version: Platform.Version,
          constants: Platform.constants
        }
      });
      cov_61901ie07().s[92]++;
      return metrics;
    } catch (error) {
      cov_61901ie07().s[93]++;
      console.error('Failed to get native metrics:', error);
      cov_61901ie07().s[94]++;
      return null;
    }
  }), [state.isInitialized, config]));
  var updateConfig = (cov_61901ie07().s[95]++, useCallback(function (newConfig) {
    cov_61901ie07().f[12]++;
    cov_61901ie07().s[96]++;
    setConfig(function (prev) {
      cov_61901ie07().f[13]++;
      cov_61901ie07().s[97]++;
      return Object.assign({}, prev, newConfig);
    });
  }, []));
  var updateNativeState = (cov_61901ie07().s[98]++, useCallback(_asyncToGenerator(function* () {
    cov_61901ie07().f[14]++;
    cov_61901ie07().s[99]++;
    try {
      var newState = (cov_61901ie07().s[100]++, {});
      cov_61901ie07().s[101]++;
      if (config.enableGPUAcceleration) {
        cov_61901ie07().b[24][0]++;
        var gpuMetrics = (cov_61901ie07().s[102]++, gpuAccelerationManager.getGPUPerformanceMetrics());
        cov_61901ie07().s[103]++;
        newState.gpuAcceleration = {
          available: gpuMetrics.capabilities !== null,
          utilization: gpuMetrics.utilization,
          averageExecutionTime: gpuMetrics.averageExecutionTime,
          tasksPerSecond: gpuMetrics.tasksPerSecond,
          efficiency: gpuMetrics.efficiency
        };
      } else {
        cov_61901ie07().b[24][1]++;
      }
      cov_61901ie07().s[104]++;
      if (config.enableNativeModules) {
        cov_61901ie07().b[25][0]++;
        var nativeMetrics = (cov_61901ie07().s[105]++, nativeModuleManager.getNativeModuleMetrics());
        cov_61901ie07().s[106]++;
        newState.nativeModules = {
          totalModules: nativeMetrics.totalModules,
          availableModules: nativeMetrics.availableModules,
          averageExecutionTime: nativeMetrics.averageExecutionTime,
          successRate: nativeMetrics.successRate,
          bridgeEfficiency: nativeMetrics.bridgeEfficiency
        };
      } else {
        cov_61901ie07().b[25][1]++;
      }
      cov_61901ie07().s[107]++;
      if (config.enableAdvancedMemory) {
        cov_61901ie07().b[26][0]++;
        var memoryMetrics = (cov_61901ie07().s[108]++, advancedMemoryManager.getMemoryMetrics());
        cov_61901ie07().s[109]++;
        newState.memoryManagement = {
          totalAllocated: memoryMetrics.totalAllocated,
          totalAvailable: memoryMetrics.totalAvailable,
          memoryPressure: memoryMetrics.memoryPressure.level,
          leakCount: memoryMetrics.leakCount,
          gcEfficiency: memoryMetrics.gcEfficiency,
          fragmentationRatio: memoryMetrics.fragmentationRatio
        };
      } else {
        cov_61901ie07().b[26][1]++;
      }
      cov_61901ie07().s[110]++;
      if (config.enableBackgroundProcessing) {
        cov_61901ie07().b[27][0]++;
        var bgMetrics = (cov_61901ie07().s[111]++, backgroundProcessingManager.getProcessingMetrics());
        cov_61901ie07().s[112]++;
        newState.backgroundProcessing = {
          activeTaskCount: bgMetrics.activeTaskCount,
          queuedTaskCount: bgMetrics.queuedTaskCount,
          totalTasksExecuted: bgMetrics.totalTasksExecuted,
          successRate: bgMetrics.successRate,
          currentStrategy: bgMetrics.currentStrategy
        };
      } else {
        cov_61901ie07().b[27][1]++;
      }
      cov_61901ie07().s[113]++;
      newState.platformOptimization = {
        hardwareAcceleration: calculateHardwareAcceleration(),
        platformIntegration: calculatePlatformIntegration(),
        performanceGain: calculatePerformanceGain()
      };
      cov_61901ie07().s[114]++;
      setState(function (prev) {
        cov_61901ie07().f[15]++;
        cov_61901ie07().s[115]++;
        return Object.assign({}, prev, newState);
      });
    } catch (error) {
      cov_61901ie07().s[116]++;
      console.error('Failed to update native state:', error);
    }
  }), [config]));
  cov_61901ie07().s[117]++;
  var calculateHardwareAcceleration = function calculateHardwareAcceleration() {
    cov_61901ie07().f[16]++;
    var score = (cov_61901ie07().s[118]++, 0);
    cov_61901ie07().s[119]++;
    if ((cov_61901ie07().b[29][0]++, config.enableGPUAcceleration) && (cov_61901ie07().b[29][1]++, state.gpuAcceleration.available)) {
      cov_61901ie07().b[28][0]++;
      cov_61901ie07().s[120]++;
      score += state.gpuAcceleration.efficiency * 40;
    } else {
      cov_61901ie07().b[28][1]++;
    }
    cov_61901ie07().s[121]++;
    if (config.enableNativeModules) {
      cov_61901ie07().b[30][0]++;
      cov_61901ie07().s[122]++;
      score += state.nativeModules.bridgeEfficiency * 30;
    } else {
      cov_61901ie07().b[30][1]++;
    }
    cov_61901ie07().s[123]++;
    if (config.enableAdvancedMemory) {
      cov_61901ie07().b[31][0]++;
      cov_61901ie07().s[124]++;
      score += (100 - state.memoryManagement.fragmentationRatio) * 30;
    } else {
      cov_61901ie07().b[31][1]++;
    }
    cov_61901ie07().s[125]++;
    return Math.min(score, 100);
  };
  cov_61901ie07().s[126]++;
  var calculatePlatformIntegration = function calculatePlatformIntegration() {
    cov_61901ie07().f[17]++;
    var score = (cov_61901ie07().s[127]++, 0);
    cov_61901ie07().s[128]++;
    if (config.enableNativeModules) {
      cov_61901ie07().b[32][0]++;
      var moduleRatio = (cov_61901ie07().s[129]++, state.nativeModules.totalModules > 0 ? (cov_61901ie07().b[33][0]++, state.nativeModules.availableModules / state.nativeModules.totalModules) : (cov_61901ie07().b[33][1]++, 0));
      cov_61901ie07().s[130]++;
      score += moduleRatio * 50;
    } else {
      cov_61901ie07().b[32][1]++;
    }
    cov_61901ie07().s[131]++;
    if (config.enableBackgroundProcessing) {
      cov_61901ie07().b[34][0]++;
      cov_61901ie07().s[132]++;
      score += state.backgroundProcessing.successRate / 100 * 30;
    } else {
      cov_61901ie07().b[34][1]++;
    }
    cov_61901ie07().s[133]++;
    if (config.enableAdvancedMemory) {
      cov_61901ie07().b[35][0]++;
      var memoryScore = (cov_61901ie07().s[134]++, state.memoryManagement.memoryPressure === 'normal' ? (cov_61901ie07().b[36][0]++, 100) : (cov_61901ie07().b[36][1]++, state.memoryManagement.memoryPressure === 'warning' ? (cov_61901ie07().b[37][0]++, 70) : (cov_61901ie07().b[37][1]++, 30)));
      cov_61901ie07().s[135]++;
      score += memoryScore / 100 * 20;
    } else {
      cov_61901ie07().b[35][1]++;
    }
    cov_61901ie07().s[136]++;
    return Math.min(score, 100);
  };
  cov_61901ie07().s[137]++;
  var calculatePerformanceGain = function calculatePerformanceGain() {
    cov_61901ie07().f[18]++;
    var gain = (cov_61901ie07().s[138]++, 0);
    cov_61901ie07().s[139]++;
    if ((cov_61901ie07().b[39][0]++, config.enableGPUAcceleration) && (cov_61901ie07().b[39][1]++, state.gpuAcceleration.available)) {
      cov_61901ie07().b[38][0]++;
      cov_61901ie07().s[140]++;
      gain += state.gpuAcceleration.efficiency * 50;
    } else {
      cov_61901ie07().b[38][1]++;
    }
    cov_61901ie07().s[141]++;
    if (config.enableNativeModules) {
      cov_61901ie07().b[40][0]++;
      cov_61901ie07().s[142]++;
      gain += state.nativeModules.successRate / 100 * 30;
    } else {
      cov_61901ie07().b[40][1]++;
    }
    cov_61901ie07().s[143]++;
    if (config.enableAdvancedMemory) {
      cov_61901ie07().b[41][0]++;
      var memoryGain = (cov_61901ie07().s[144]++, state.memoryManagement.memoryPressure === 'normal' ? (cov_61901ie07().b[42][0]++, 20) : (cov_61901ie07().b[42][1]++, 10));
      cov_61901ie07().s[145]++;
      gain += memoryGain;
    } else {
      cov_61901ie07().b[41][1]++;
    }
    cov_61901ie07().s[146]++;
    return Math.min(gain, 100);
  };
  cov_61901ie07().s[147]++;
  useEffect(function () {
    cov_61901ie07().f[19]++;
    cov_61901ie07().s[148]++;
    if ((cov_61901ie07().b[44][0]++, !config.autoOptimization) || (cov_61901ie07().b[44][1]++, !state.isInitialized)) {
      cov_61901ie07().b[43][0]++;
      cov_61901ie07().s[149]++;
      return;
    } else {
      cov_61901ie07().b[43][1]++;
    }
    var interval = (cov_61901ie07().s[150]++, setInterval(_asyncToGenerator(function* () {
      cov_61901ie07().f[20]++;
      cov_61901ie07().s[151]++;
      if ((cov_61901ie07().b[46][0]++, config.enableAdvancedMemory) && (cov_61901ie07().b[46][1]++, state.memoryManagement.memoryPressure !== 'normal')) {
        cov_61901ie07().b[45][0]++;
        cov_61901ie07().s[152]++;
        yield optimizeMemory(false);
      } else {
        cov_61901ie07().b[45][1]++;
      }
      cov_61901ie07().s[153]++;
      if (config.enableGPUAcceleration) {
        cov_61901ie07().b[47][0]++;
        cov_61901ie07().s[154]++;
        yield gpuAccelerationManager.optimizeGPUPerformance();
      } else {
        cov_61901ie07().b[47][1]++;
      }
    }), config.optimizationInterval));
    cov_61901ie07().s[155]++;
    return function () {
      cov_61901ie07().f[21]++;
      cov_61901ie07().s[156]++;
      return clearInterval(interval);
    };
  }, [config.autoOptimization, config.optimizationInterval, state.isInitialized, optimizeMemory]);
  cov_61901ie07().s[157]++;
  useEffect(function () {
    cov_61901ie07().f[22]++;
    cov_61901ie07().s[158]++;
    if (!state.isInitialized) {
      cov_61901ie07().b[48][0]++;
      cov_61901ie07().s[159]++;
      return;
    } else {
      cov_61901ie07().b[48][1]++;
    }
    var interval = (cov_61901ie07().s[160]++, setInterval(function () {
      cov_61901ie07().f[23]++;
      cov_61901ie07().s[161]++;
      updateNativeState();
    }, 30000));
    cov_61901ie07().s[162]++;
    return function () {
      cov_61901ie07().f[24]++;
      cov_61901ie07().s[163]++;
      return clearInterval(interval);
    };
  }, [state.isInitialized, updateNativeState]);
  cov_61901ie07().s[164]++;
  useEffect(function () {
    cov_61901ie07().f[25]++;
    cov_61901ie07().s[165]++;
    if (!state.isInitialized) {
      cov_61901ie07().b[49][0]++;
      cov_61901ie07().s[166]++;
      initialize();
    } else {
      cov_61901ie07().b[49][1]++;
    }
  }, [state.isInitialized, initialize]);
  cov_61901ie07().s[167]++;
  return useMemo(function () {
    cov_61901ie07().f[26]++;
    cov_61901ie07().s[168]++;
    return {
      state: state,
      actions: {
        initialize: initialize,
        executeGPUTask: executeGPUTask,
        executeNativeOperation: executeNativeOperation,
        optimizeMemory: optimizeMemory,
        scheduleBackgroundTask: scheduleBackgroundTask,
        analyzeVideo: analyzeVideo,
        runMLInference: runMLInference,
        getNativeMetrics: getNativeMetrics,
        updateConfig: updateConfig
      },
      config: config
    };
  }, [state, initialize, executeGPUTask, executeNativeOperation, optimizeMemory, scheduleBackgroundTask, analyzeVideo, runMLInference, getNativeMetrics, updateConfig, config]);
}
export default useNativeOptimization;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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