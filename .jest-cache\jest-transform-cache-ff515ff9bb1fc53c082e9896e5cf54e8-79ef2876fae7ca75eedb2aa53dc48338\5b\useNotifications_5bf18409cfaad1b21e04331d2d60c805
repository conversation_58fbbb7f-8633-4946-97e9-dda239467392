40dd9b0dce299f6d02edc1ee1d5edbdf
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2l11fifpkv() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useNotifications.ts";
  var hash = "5fc0f696f3c117f2c637cabb8d0bc9d5d25f9a35";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useNotifications.ts",
    statementMap: {
      "0": {
        start: {
          line: 33,
          column: 44
        },
        end: {
          line: 33,
          column: 59
        }
      },
      "1": {
        start: {
          line: 34,
          column: 34
        },
        end: {
          line: 41,
          column: 4
        }
      },
      "2": {
        start: {
          line: 42,
          column: 36
        },
        end: {
          line: 42,
          column: 65
        }
      },
      "3": {
        start: {
          line: 43,
          column: 28
        },
        end: {
          line: 43,
          column: 57
        }
      },
      "4": {
        start: {
          line: 48,
          column: 21
        },
        end: {
          line: 71,
          column: 8
        }
      },
      "5": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "6": {
        start: {
          line: 50,
          column: 6
        },
        end: {
          line: 50,
          column: 21
        }
      },
      "7": {
        start: {
          line: 51,
          column: 26
        },
        end: {
          line: 51,
          column: 64
        }
      },
      "8": {
        start: {
          line: 52,
          column: 6
        },
        end: {
          line: 52,
          column: 36
        }
      },
      "9": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 62,
          column: 7
        }
      },
      "10": {
        start: {
          line: 55,
          column: 22
        },
        end: {
          line: 55,
          column: 56
        }
      },
      "11": {
        start: {
          line: 56,
          column: 8
        },
        end: {
          line: 56,
          column: 28
        }
      },
      "12": {
        start: {
          line: 58,
          column: 32
        },
        end: {
          line: 58,
          column: 65
        }
      },
      "13": {
        start: {
          line: 59,
          column: 8
        },
        end: {
          line: 59,
          column: 37
        }
      },
      "14": {
        start: {
          line: 61,
          column: 8
        },
        end: {
          line: 61,
          column: 83
        }
      },
      "15": {
        start: {
          line: 64,
          column: 6
        },
        end: {
          line: 64,
          column: 25
        }
      },
      "16": {
        start: {
          line: 66,
          column: 27
        },
        end: {
          line: 66,
          column: 100
        }
      },
      "17": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 67,
          column: 29
        }
      },
      "18": {
        start: {
          line: 68,
          column: 6
        },
        end: {
          line: 68,
          column: 30
        }
      },
      "19": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 69,
          column: 19
        }
      },
      "20": {
        start: {
          line: 76,
          column: 32
        },
        end: {
          line: 89,
          column: 21
        }
      },
      "21": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 79,
          column: 5
        }
      },
      "22": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 62
        }
      },
      "23": {
        start: {
          line: 81,
          column: 4
        },
        end: {
          line: 88,
          column: 5
        }
      },
      "24": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 21
        }
      },
      "25": {
        start: {
          line: 83,
          column: 6
        },
        end: {
          line: 83,
          column: 70
        }
      },
      "26": {
        start: {
          line: 85,
          column: 27
        },
        end: {
          line: 85,
          column: 93
        }
      },
      "27": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 86,
          column: 29
        }
      },
      "28": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 87,
          column: 16
        }
      },
      "29": {
        start: {
          line: 94,
          column: 31
        },
        end: {
          line: 110,
          column: 21
        }
      },
      "30": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 100,
          column: 5
        }
      },
      "31": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 62
        }
      },
      "32": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "33": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 21
        }
      },
      "34": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 78
        }
      },
      "35": {
        start: {
          line: 106,
          column: 27
        },
        end: {
          line: 106,
          column: 97
        }
      },
      "36": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 29
        }
      },
      "37": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 16
        }
      },
      "38": {
        start: {
          line: 115,
          column: 29
        },
        end: {
          line: 124,
          column: 8
        }
      },
      "39": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 123,
          column: 5
        }
      },
      "40": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 21
        }
      },
      "41": {
        start: {
          line: 118,
          column: 6
        },
        end: {
          line: 118,
          column: 67
        }
      },
      "42": {
        start: {
          line: 120,
          column: 27
        },
        end: {
          line: 120,
          column: 95
        }
      },
      "43": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 29
        }
      },
      "44": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 122,
          column: 16
        }
      },
      "45": {
        start: {
          line: 129,
          column: 28
        },
        end: {
          line: 142,
          column: 21
        }
      },
      "46": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 132,
          column: 5
        }
      },
      "47": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 62
        }
      },
      "48": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 141,
          column: 5
        }
      },
      "49": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 21
        }
      },
      "50": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 52
        }
      },
      "51": {
        start: {
          line: 138,
          column: 27
        },
        end: {
          line: 138,
          column: 95
        }
      },
      "52": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 139,
          column: 29
        }
      },
      "53": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 16
        }
      },
      "54": {
        start: {
          line: 147,
          column: 35
        },
        end: {
          line: 164,
          column: 21
        }
      },
      "55": {
        start: {
          line: 152,
          column: 4
        },
        end: {
          line: 154,
          column: 5
        }
      },
      "56": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 153,
          column: 62
        }
      },
      "57": {
        start: {
          line: 156,
          column: 4
        },
        end: {
          line: 163,
          column: 5
        }
      },
      "58": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 21
        }
      },
      "59": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 95
        }
      },
      "60": {
        start: {
          line: 160,
          column: 27
        },
        end: {
          line: 160,
          column: 102
        }
      },
      "61": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 161,
          column: 29
        }
      },
      "62": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 16
        }
      },
      "63": {
        start: {
          line: 169,
          column: 38
        },
        end: {
          line: 185,
          column: 21
        }
      },
      "64": {
        start: {
          line: 173,
          column: 4
        },
        end: {
          line: 175,
          column: 5
        }
      },
      "65": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 62
        }
      },
      "66": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 184,
          column: 5
        }
      },
      "67": {
        start: {
          line: 178,
          column: 6
        },
        end: {
          line: 178,
          column: 21
        }
      },
      "68": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 179,
          column: 91
        }
      },
      "69": {
        start: {
          line: 181,
          column: 27
        },
        end: {
          line: 181,
          column: 105
        }
      },
      "70": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 29
        }
      },
      "71": {
        start: {
          line: 183,
          column: 6
        },
        end: {
          line: 183,
          column: 16
        }
      },
      "72": {
        start: {
          line: 190,
          column: 29
        },
        end: {
          line: 207,
          column: 21
        }
      },
      "73": {
        start: {
          line: 195,
          column: 4
        },
        end: {
          line: 197,
          column: 5
        }
      },
      "74": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 62
        }
      },
      "75": {
        start: {
          line: 199,
          column: 4
        },
        end: {
          line: 206,
          column: 5
        }
      },
      "76": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 200,
          column: 21
        }
      },
      "77": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 84
        }
      },
      "78": {
        start: {
          line: 203,
          column: 27
        },
        end: {
          line: 203,
          column: 96
        }
      },
      "79": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 204,
          column: 29
        }
      },
      "80": {
        start: {
          line: 205,
          column: 6
        },
        end: {
          line: 205,
          column: 16
        }
      },
      "81": {
        start: {
          line: 212,
          column: 32
        },
        end: {
          line: 229,
          column: 21
        }
      },
      "82": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "83": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 62
        }
      },
      "84": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "85": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 21
        }
      },
      "86": {
        start: {
          line: 223,
          column: 6
        },
        end: {
          line: 223,
          column: 96
        }
      },
      "87": {
        start: {
          line: 225,
          column: 27
        },
        end: {
          line: 225,
          column: 99
        }
      },
      "88": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 29
        }
      },
      "89": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 16
        }
      },
      "90": {
        start: {
          line: 234,
          column: 25
        },
        end: {
          line: 244,
          column: 8
        }
      },
      "91": {
        start: {
          line: 235,
          column: 4
        },
        end: {
          line: 243,
          column: 5
        }
      },
      "92": {
        start: {
          line: 236,
          column: 6
        },
        end: {
          line: 236,
          column: 21
        }
      },
      "93": {
        start: {
          line: 237,
          column: 6
        },
        end: {
          line: 237,
          column: 54
        }
      },
      "94": {
        start: {
          line: 238,
          column: 30
        },
        end: {
          line: 238,
          column: 63
        }
      },
      "95": {
        start: {
          line: 239,
          column: 6
        },
        end: {
          line: 239,
          column: 35
        }
      },
      "96": {
        start: {
          line: 241,
          column: 27
        },
        end: {
          line: 241,
          column: 91
        }
      },
      "97": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 29
        }
      },
      "98": {
        start: {
          line: 249,
          column: 36
        },
        end: {
          line: 258,
          column: 8
        }
      },
      "99": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 257,
          column: 5
        }
      },
      "100": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 251,
          column: 21
        }
      },
      "101": {
        start: {
          line: 252,
          column: 6
        },
        end: {
          line: 252,
          column: 67
        }
      },
      "102": {
        start: {
          line: 254,
          column: 27
        },
        end: {
          line: 254,
          column: 103
        }
      },
      "103": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 29
        }
      },
      "104": {
        start: {
          line: 256,
          column: 6
        },
        end: {
          line: 256,
          column: 16
        }
      },
      "105": {
        start: {
          line: 261,
          column: 2
        },
        end: {
          line: 263,
          column: 19
        }
      },
      "106": {
        start: {
          line: 262,
          column: 4
        },
        end: {
          line: 262,
          column: 17
        }
      },
      "107": {
        start: {
          line: 266,
          column: 2
        },
        end: {
          line: 290,
          column: 22
        }
      },
      "108": {
        start: {
          line: 267,
          column: 4
        },
        end: {
          line: 267,
          column: 31
        }
      },
      "109": {
        start: {
          line: 267,
          column: 24
        },
        end: {
          line: 267,
          column: 31
        }
      },
      "110": {
        start: {
          line: 270,
          column: 33
        },
        end: {
          line: 272,
          column: 6
        }
      },
      "111": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 271,
          column: 58
        }
      },
      "112": {
        start: {
          line: 275,
          column: 29
        },
        end: {
          line: 284,
          column: 6
        }
      },
      "113": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 54
        }
      },
      "114": {
        start: {
          line: 279,
          column: 31
        },
        end: {
          line: 279,
          column: 73
        }
      },
      "115": {
        start: {
          line: 280,
          column: 6
        },
        end: {
          line: 283,
          column: 7
        }
      },
      "116": {
        start: {
          line: 282,
          column: 8
        },
        end: {
          line: 282,
          column: 65
        }
      },
      "117": {
        start: {
          line: 286,
          column: 4
        },
        end: {
          line: 289,
          column: 6
        }
      },
      "118": {
        start: {
          line: 287,
          column: 6
        },
        end: {
          line: 287,
          column: 73
        }
      },
      "119": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 69
        }
      },
      "120": {
        start: {
          line: 292,
          column: 2
        },
        end: {
          line: 317,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useNotifications",
        decl: {
          start: {
            line: 32,
            column: 16
          },
          end: {
            line: 32,
            column: 32
          }
        },
        loc: {
          start: {
            line: 32,
            column: 59
          },
          end: {
            line: 318,
            column: 1
          }
        },
        line: 32
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 48,
            column: 33
          },
          end: {
            line: 48,
            column: 34
          }
        },
        loc: {
          start: {
            line: 48,
            column: 63
          },
          end: {
            line: 71,
            column: 3
          }
        },
        line: 48
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 76,
            column: 44
          },
          end: {
            line: 76,
            column: 45
          }
        },
        loc: {
          start: {
            line: 76,
            column: 101
          },
          end: {
            line: 89,
            column: 3
          }
        },
        line: 76
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 94,
            column: 43
          },
          end: {
            line: 94,
            column: 44
          }
        },
        loc: {
          start: {
            line: 97,
            column: 24
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 97
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 115,
            column: 41
          },
          end: {
            line: 115,
            column: 42
          }
        },
        loc: {
          start: {
            line: 115,
            column: 90
          },
          end: {
            line: 124,
            column: 3
          }
        },
        line: 115
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 129,
            column: 40
          },
          end: {
            line: 129,
            column: 41
          }
        },
        loc: {
          start: {
            line: 129,
            column: 67
          },
          end: {
            line: 142,
            column: 3
          }
        },
        line: 129
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 147,
            column: 47
          },
          end: {
            line: 147,
            column: 48
          }
        },
        loc: {
          start: {
            line: 151,
            column: 24
          },
          end: {
            line: 164,
            column: 3
          }
        },
        line: 151
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 169,
            column: 50
          },
          end: {
            line: 169,
            column: 51
          }
        },
        loc: {
          start: {
            line: 172,
            column: 22
          },
          end: {
            line: 185,
            column: 3
          }
        },
        line: 172
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 190,
            column: 41
          },
          end: {
            line: 190,
            column: 42
          }
        },
        loc: {
          start: {
            line: 194,
            column: 22
          },
          end: {
            line: 207,
            column: 3
          }
        },
        line: 194
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 212,
            column: 44
          },
          end: {
            line: 212,
            column: 45
          }
        },
        loc: {
          start: {
            line: 216,
            column: 24
          },
          end: {
            line: 229,
            column: 3
          }
        },
        line: 216
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 234,
            column: 37
          },
          end: {
            line: 234,
            column: 38
          }
        },
        loc: {
          start: {
            line: 234,
            column: 91
          },
          end: {
            line: 244,
            column: 3
          }
        },
        line: 234
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 249,
            column: 48
          },
          end: {
            line: 249,
            column: 49
          }
        },
        loc: {
          start: {
            line: 249,
            column: 106
          },
          end: {
            line: 258,
            column: 3
          }
        },
        line: 249
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 261,
            column: 12
          },
          end: {
            line: 261,
            column: 13
          }
        },
        loc: {
          start: {
            line: 261,
            column: 18
          },
          end: {
            line: 263,
            column: 3
          }
        },
        line: 261
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 266,
            column: 12
          },
          end: {
            line: 266,
            column: 13
          }
        },
        loc: {
          start: {
            line: 266,
            column: 18
          },
          end: {
            line: 290,
            column: 3
          }
        },
        line: 266
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 270,
            column: 79
          },
          end: {
            line: 270,
            column: 80
          }
        },
        loc: {
          start: {
            line: 270,
            column: 95
          },
          end: {
            line: 272,
            column: 5
          }
        },
        line: 270
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 275,
            column: 83
          },
          end: {
            line: 275,
            column: 84
          }
        },
        loc: {
          start: {
            line: 275,
            column: 95
          },
          end: {
            line: 284,
            column: 5
          }
        },
        line: 275
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 286,
            column: 11
          },
          end: {
            line: 286,
            column: 12
          }
        },
        loc: {
          start: {
            line: 286,
            column: 17
          },
          end: {
            line: 289,
            column: 5
          }
        },
        line: 286
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 54,
            column: 6
          },
          end: {
            line: 62,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 54,
            column: 6
          },
          end: {
            line: 62,
            column: 7
          }
        }, {
          start: {
            line: 60,
            column: 13
          },
          end: {
            line: 62,
            column: 7
          }
        }],
        line: 54
      },
      "1": {
        loc: {
          start: {
            line: 66,
            column: 27
          },
          end: {
            line: 66,
            column: 100
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 66,
            column: 50
          },
          end: {
            line: 66,
            column: 61
          }
        }, {
          start: {
            line: 66,
            column: 64
          },
          end: {
            line: 66,
            column: 100
          }
        }],
        line: 66
      },
      "2": {
        loc: {
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 79,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 79,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "3": {
        loc: {
          start: {
            line: 85,
            column: 27
          },
          end: {
            line: 85,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 85,
            column: 50
          },
          end: {
            line: 85,
            column: 61
          }
        }, {
          start: {
            line: 85,
            column: 64
          },
          end: {
            line: 85,
            column: 93
          }
        }],
        line: 85
      },
      "4": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 100,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 100,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "5": {
        loc: {
          start: {
            line: 106,
            column: 27
          },
          end: {
            line: 106,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 106,
            column: 50
          },
          end: {
            line: 106,
            column: 61
          }
        }, {
          start: {
            line: 106,
            column: 64
          },
          end: {
            line: 106,
            column: 97
          }
        }],
        line: 106
      },
      "6": {
        loc: {
          start: {
            line: 120,
            column: 27
          },
          end: {
            line: 120,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 120,
            column: 50
          },
          end: {
            line: 120,
            column: 61
          }
        }, {
          start: {
            line: 120,
            column: 64
          },
          end: {
            line: 120,
            column: 95
          }
        }],
        line: 120
      },
      "7": {
        loc: {
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 132,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "8": {
        loc: {
          start: {
            line: 138,
            column: 27
          },
          end: {
            line: 138,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 138,
            column: 50
          },
          end: {
            line: 138,
            column: 61
          }
        }, {
          start: {
            line: 138,
            column: 64
          },
          end: {
            line: 138,
            column: 95
          }
        }],
        line: 138
      },
      "9": {
        loc: {
          start: {
            line: 152,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 152,
            column: 4
          },
          end: {
            line: 154,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 152
      },
      "10": {
        loc: {
          start: {
            line: 160,
            column: 27
          },
          end: {
            line: 160,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 160,
            column: 50
          },
          end: {
            line: 160,
            column: 61
          }
        }, {
          start: {
            line: 160,
            column: 64
          },
          end: {
            line: 160,
            column: 102
          }
        }],
        line: 160
      },
      "11": {
        loc: {
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 175,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 4
          },
          end: {
            line: 175,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "12": {
        loc: {
          start: {
            line: 181,
            column: 27
          },
          end: {
            line: 181,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 181,
            column: 50
          },
          end: {
            line: 181,
            column: 61
          }
        }, {
          start: {
            line: 181,
            column: 64
          },
          end: {
            line: 181,
            column: 105
          }
        }],
        line: 181
      },
      "13": {
        loc: {
          start: {
            line: 195,
            column: 4
          },
          end: {
            line: 197,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 4
          },
          end: {
            line: 197,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 195
      },
      "14": {
        loc: {
          start: {
            line: 203,
            column: 27
          },
          end: {
            line: 203,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 203,
            column: 50
          },
          end: {
            line: 203,
            column: 61
          }
        }, {
          start: {
            line: 203,
            column: 64
          },
          end: {
            line: 203,
            column: 96
          }
        }],
        line: 203
      },
      "15": {
        loc: {
          start: {
            line: 217,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      },
      "16": {
        loc: {
          start: {
            line: 225,
            column: 27
          },
          end: {
            line: 225,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 225,
            column: 50
          },
          end: {
            line: 225,
            column: 61
          }
        }, {
          start: {
            line: 225,
            column: 64
          },
          end: {
            line: 225,
            column: 99
          }
        }],
        line: 225
      },
      "17": {
        loc: {
          start: {
            line: 241,
            column: 27
          },
          end: {
            line: 241,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 241,
            column: 50
          },
          end: {
            line: 241,
            column: 61
          }
        }, {
          start: {
            line: 241,
            column: 64
          },
          end: {
            line: 241,
            column: 91
          }
        }],
        line: 241
      },
      "18": {
        loc: {
          start: {
            line: 254,
            column: 27
          },
          end: {
            line: 254,
            column: 103
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 254,
            column: 50
          },
          end: {
            line: 254,
            column: 61
          }
        }, {
          start: {
            line: 254,
            column: 64
          },
          end: {
            line: 254,
            column: 103
          }
        }],
        line: 254
      },
      "19": {
        loc: {
          start: {
            line: 267,
            column: 4
          },
          end: {
            line: 267,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 267,
            column: 4
          },
          end: {
            line: 267,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 267
      },
      "20": {
        loc: {
          start: {
            line: 280,
            column: 6
          },
          end: {
            line: 283,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 280,
            column: 6
          },
          end: {
            line: 283,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 280
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "5fc0f696f3c117f2c637cabb8d0bc9d5d25f9a35"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2l11fifpkv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2l11fifpkv();
import { useState, useCallback, useEffect } from 'react';
import { notificationService } from "../services/notificationService";
import * as Notifications from 'expo-notifications';
export function useNotifications() {
  cov_2l11fifpkv().f[0]++;
  var _ref = (cov_2l11fifpkv().s[0]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    isInitialized = _ref2[0],
    setIsInitialized = _ref2[1];
  var _ref3 = (cov_2l11fifpkv().s[1]++, useState({
      dailyTips: true,
      trainingReminders: true,
      matchReminders: true,
      achievementAlerts: true,
      progressUpdates: true,
      socialNotifications: false
    })),
    _ref4 = _slicedToArray(_ref3, 2),
    settings = _ref4[0],
    setSettings = _ref4[1];
  var _ref5 = (cov_2l11fifpkv().s[2]++, useState(null)),
    _ref6 = _slicedToArray(_ref5, 2),
    pushToken = _ref6[0],
    setPushToken = _ref6[1];
  var _ref7 = (cov_2l11fifpkv().s[3]++, useState(null)),
    _ref8 = _slicedToArray(_ref7, 2),
    error = _ref8[0],
    setError = _ref8[1];
  var initialize = (cov_2l11fifpkv().s[4]++, useCallback(_asyncToGenerator(function* () {
    cov_2l11fifpkv().f[1]++;
    cov_2l11fifpkv().s[5]++;
    try {
      cov_2l11fifpkv().s[6]++;
      setError(null);
      var initialized = (cov_2l11fifpkv().s[7]++, yield notificationService.initialize());
      cov_2l11fifpkv().s[8]++;
      setIsInitialized(initialized);
      cov_2l11fifpkv().s[9]++;
      if (initialized) {
        cov_2l11fifpkv().b[0][0]++;
        var token = (cov_2l11fifpkv().s[10]++, notificationService.getPushToken());
        cov_2l11fifpkv().s[11]++;
        setPushToken(token);
        var currentSettings = (cov_2l11fifpkv().s[12]++, notificationService.getSettings());
        cov_2l11fifpkv().s[13]++;
        setSettings(currentSettings);
      } else {
        cov_2l11fifpkv().b[0][1]++;
        cov_2l11fifpkv().s[14]++;
        setError('Failed to initialize notifications - permissions may be denied');
      }
      cov_2l11fifpkv().s[15]++;
      return initialized;
    } catch (err) {
      var errorMessage = (cov_2l11fifpkv().s[16]++, err instanceof Error ? (cov_2l11fifpkv().b[1][0]++, err.message) : (cov_2l11fifpkv().b[1][1]++, 'Failed to initialize notifications'));
      cov_2l11fifpkv().s[17]++;
      setError(errorMessage);
      cov_2l11fifpkv().s[18]++;
      setIsInitialized(false);
      cov_2l11fifpkv().s[19]++;
      return false;
    }
  }), []));
  var sendLocalNotification = (cov_2l11fifpkv().s[20]++, useCallback(function () {
    var _ref0 = _asyncToGenerator(function* (content) {
      cov_2l11fifpkv().f[2]++;
      cov_2l11fifpkv().s[21]++;
      if (!isInitialized) {
        cov_2l11fifpkv().b[2][0]++;
        cov_2l11fifpkv().s[22]++;
        throw new Error('Notification service not initialized');
      } else {
        cov_2l11fifpkv().b[2][1]++;
      }
      cov_2l11fifpkv().s[23]++;
      try {
        cov_2l11fifpkv().s[24]++;
        setError(null);
        cov_2l11fifpkv().s[25]++;
        return yield notificationService.sendLocalNotification(content);
      } catch (err) {
        var errorMessage = (cov_2l11fifpkv().s[26]++, err instanceof Error ? (cov_2l11fifpkv().b[3][0]++, err.message) : (cov_2l11fifpkv().b[3][1]++, 'Failed to send notification'));
        cov_2l11fifpkv().s[27]++;
        setError(errorMessage);
        cov_2l11fifpkv().s[28]++;
        throw err;
      }
    });
    return function (_x) {
      return _ref0.apply(this, arguments);
    };
  }(), [isInitialized]));
  var scheduleNotification = (cov_2l11fifpkv().s[29]++, useCallback(function () {
    var _ref1 = _asyncToGenerator(function* (content, trigger) {
      cov_2l11fifpkv().f[3]++;
      cov_2l11fifpkv().s[30]++;
      if (!isInitialized) {
        cov_2l11fifpkv().b[4][0]++;
        cov_2l11fifpkv().s[31]++;
        throw new Error('Notification service not initialized');
      } else {
        cov_2l11fifpkv().b[4][1]++;
      }
      cov_2l11fifpkv().s[32]++;
      try {
        cov_2l11fifpkv().s[33]++;
        setError(null);
        cov_2l11fifpkv().s[34]++;
        return yield notificationService.scheduleNotification(content, trigger);
      } catch (err) {
        var errorMessage = (cov_2l11fifpkv().s[35]++, err instanceof Error ? (cov_2l11fifpkv().b[5][0]++, err.message) : (cov_2l11fifpkv().b[5][1]++, 'Failed to schedule notification'));
        cov_2l11fifpkv().s[36]++;
        setError(errorMessage);
        cov_2l11fifpkv().s[37]++;
        throw err;
      }
    });
    return function (_x2, _x3) {
      return _ref1.apply(this, arguments);
    };
  }(), [isInitialized]));
  var cancelNotification = (cov_2l11fifpkv().s[38]++, useCallback(function () {
    var _ref10 = _asyncToGenerator(function* (notificationId) {
      cov_2l11fifpkv().f[4]++;
      cov_2l11fifpkv().s[39]++;
      try {
        cov_2l11fifpkv().s[40]++;
        setError(null);
        cov_2l11fifpkv().s[41]++;
        yield notificationService.cancelNotification(notificationId);
      } catch (err) {
        var errorMessage = (cov_2l11fifpkv().s[42]++, err instanceof Error ? (cov_2l11fifpkv().b[6][0]++, err.message) : (cov_2l11fifpkv().b[6][1]++, 'Failed to cancel notification'));
        cov_2l11fifpkv().s[43]++;
        setError(errorMessage);
        cov_2l11fifpkv().s[44]++;
        throw err;
      }
    });
    return function (_x4) {
      return _ref10.apply(this, arguments);
    };
  }(), []));
  var scheduleDailyTips = (cov_2l11fifpkv().s[45]++, useCallback(_asyncToGenerator(function* () {
    cov_2l11fifpkv().f[5]++;
    cov_2l11fifpkv().s[46]++;
    if (!isInitialized) {
      cov_2l11fifpkv().b[7][0]++;
      cov_2l11fifpkv().s[47]++;
      throw new Error('Notification service not initialized');
    } else {
      cov_2l11fifpkv().b[7][1]++;
    }
    cov_2l11fifpkv().s[48]++;
    try {
      cov_2l11fifpkv().s[49]++;
      setError(null);
      cov_2l11fifpkv().s[50]++;
      yield notificationService.scheduleDailyTips();
    } catch (err) {
      var errorMessage = (cov_2l11fifpkv().s[51]++, err instanceof Error ? (cov_2l11fifpkv().b[8][0]++, err.message) : (cov_2l11fifpkv().b[8][1]++, 'Failed to schedule daily tips'));
      cov_2l11fifpkv().s[52]++;
      setError(errorMessage);
      cov_2l11fifpkv().s[53]++;
      throw err;
    }
  }), [isInitialized]));
  var scheduleTrainingReminder = (cov_2l11fifpkv().s[54]++, useCallback(function () {
    var _ref12 = _asyncToGenerator(function* (title, message, scheduledTime) {
      cov_2l11fifpkv().f[6]++;
      cov_2l11fifpkv().s[55]++;
      if (!isInitialized) {
        cov_2l11fifpkv().b[9][0]++;
        cov_2l11fifpkv().s[56]++;
        throw new Error('Notification service not initialized');
      } else {
        cov_2l11fifpkv().b[9][1]++;
      }
      cov_2l11fifpkv().s[57]++;
      try {
        cov_2l11fifpkv().s[58]++;
        setError(null);
        cov_2l11fifpkv().s[59]++;
        return yield notificationService.scheduleTrainingReminder(title, message, scheduledTime);
      } catch (err) {
        var errorMessage = (cov_2l11fifpkv().s[60]++, err instanceof Error ? (cov_2l11fifpkv().b[10][0]++, err.message) : (cov_2l11fifpkv().b[10][1]++, 'Failed to schedule training reminder'));
        cov_2l11fifpkv().s[61]++;
        setError(errorMessage);
        cov_2l11fifpkv().s[62]++;
        throw err;
      }
    });
    return function (_x5, _x6, _x7) {
      return _ref12.apply(this, arguments);
    };
  }(), [isInitialized]));
  var sendAchievementNotification = (cov_2l11fifpkv().s[63]++, useCallback(function () {
    var _ref13 = _asyncToGenerator(function* (achievementTitle, description) {
      cov_2l11fifpkv().f[7]++;
      cov_2l11fifpkv().s[64]++;
      if (!isInitialized) {
        cov_2l11fifpkv().b[11][0]++;
        cov_2l11fifpkv().s[65]++;
        throw new Error('Notification service not initialized');
      } else {
        cov_2l11fifpkv().b[11][1]++;
      }
      cov_2l11fifpkv().s[66]++;
      try {
        cov_2l11fifpkv().s[67]++;
        setError(null);
        cov_2l11fifpkv().s[68]++;
        yield notificationService.sendAchievementNotification(achievementTitle, description);
      } catch (err) {
        var errorMessage = (cov_2l11fifpkv().s[69]++, err instanceof Error ? (cov_2l11fifpkv().b[12][0]++, err.message) : (cov_2l11fifpkv().b[12][1]++, 'Failed to send achievement notification'));
        cov_2l11fifpkv().s[70]++;
        setError(errorMessage);
        cov_2l11fifpkv().s[71]++;
        throw err;
      }
    });
    return function (_x8, _x9) {
      return _ref13.apply(this, arguments);
    };
  }(), [isInitialized]));
  var sendProgressUpdate = (cov_2l11fifpkv().s[72]++, useCallback(function () {
    var _ref14 = _asyncToGenerator(function* (skillName, oldRating, newRating) {
      cov_2l11fifpkv().f[8]++;
      cov_2l11fifpkv().s[73]++;
      if (!isInitialized) {
        cov_2l11fifpkv().b[13][0]++;
        cov_2l11fifpkv().s[74]++;
        throw new Error('Notification service not initialized');
      } else {
        cov_2l11fifpkv().b[13][1]++;
      }
      cov_2l11fifpkv().s[75]++;
      try {
        cov_2l11fifpkv().s[76]++;
        setError(null);
        cov_2l11fifpkv().s[77]++;
        yield notificationService.sendProgressUpdate(skillName, oldRating, newRating);
      } catch (err) {
        var errorMessage = (cov_2l11fifpkv().s[78]++, err instanceof Error ? (cov_2l11fifpkv().b[14][0]++, err.message) : (cov_2l11fifpkv().b[14][1]++, 'Failed to send progress update'));
        cov_2l11fifpkv().s[79]++;
        setError(errorMessage);
        cov_2l11fifpkv().s[80]++;
        throw err;
      }
    });
    return function (_x0, _x1, _x10) {
      return _ref14.apply(this, arguments);
    };
  }(), [isInitialized]));
  var scheduleMatchReminder = (cov_2l11fifpkv().s[81]++, useCallback(function () {
    var _ref15 = _asyncToGenerator(function* (opponentName, matchTime, location) {
      cov_2l11fifpkv().f[9]++;
      cov_2l11fifpkv().s[82]++;
      if (!isInitialized) {
        cov_2l11fifpkv().b[15][0]++;
        cov_2l11fifpkv().s[83]++;
        throw new Error('Notification service not initialized');
      } else {
        cov_2l11fifpkv().b[15][1]++;
      }
      cov_2l11fifpkv().s[84]++;
      try {
        cov_2l11fifpkv().s[85]++;
        setError(null);
        cov_2l11fifpkv().s[86]++;
        return yield notificationService.scheduleMatchReminder(opponentName, matchTime, location);
      } catch (err) {
        var errorMessage = (cov_2l11fifpkv().s[87]++, err instanceof Error ? (cov_2l11fifpkv().b[16][0]++, err.message) : (cov_2l11fifpkv().b[16][1]++, 'Failed to schedule match reminder'));
        cov_2l11fifpkv().s[88]++;
        setError(errorMessage);
        cov_2l11fifpkv().s[89]++;
        throw err;
      }
    });
    return function (_x11, _x12, _x13) {
      return _ref15.apply(this, arguments);
    };
  }(), [isInitialized]));
  var updateSettings = (cov_2l11fifpkv().s[90]++, useCallback(function (newSettings) {
    cov_2l11fifpkv().f[10]++;
    cov_2l11fifpkv().s[91]++;
    try {
      cov_2l11fifpkv().s[92]++;
      setError(null);
      cov_2l11fifpkv().s[93]++;
      notificationService.updateSettings(newSettings);
      var updatedSettings = (cov_2l11fifpkv().s[94]++, notificationService.getSettings());
      cov_2l11fifpkv().s[95]++;
      setSettings(updatedSettings);
    } catch (err) {
      var errorMessage = (cov_2l11fifpkv().s[96]++, err instanceof Error ? (cov_2l11fifpkv().b[17][0]++, err.message) : (cov_2l11fifpkv().b[17][1]++, 'Failed to update settings'));
      cov_2l11fifpkv().s[97]++;
      setError(errorMessage);
    }
  }, []));
  var getScheduledNotifications = (cov_2l11fifpkv().s[98]++, useCallback(_asyncToGenerator(function* () {
    cov_2l11fifpkv().f[11]++;
    cov_2l11fifpkv().s[99]++;
    try {
      cov_2l11fifpkv().s[100]++;
      setError(null);
      cov_2l11fifpkv().s[101]++;
      return yield notificationService.getScheduledNotifications();
    } catch (err) {
      var errorMessage = (cov_2l11fifpkv().s[102]++, err instanceof Error ? (cov_2l11fifpkv().b[18][0]++, err.message) : (cov_2l11fifpkv().b[18][1]++, 'Failed to get scheduled notifications'));
      cov_2l11fifpkv().s[103]++;
      setError(errorMessage);
      cov_2l11fifpkv().s[104]++;
      return [];
    }
  }), []));
  cov_2l11fifpkv().s[105]++;
  useEffect(function () {
    cov_2l11fifpkv().f[12]++;
    cov_2l11fifpkv().s[106]++;
    initialize();
  }, [initialize]);
  cov_2l11fifpkv().s[107]++;
  useEffect(function () {
    cov_2l11fifpkv().f[13]++;
    cov_2l11fifpkv().s[108]++;
    if (!isInitialized) {
      cov_2l11fifpkv().b[19][0]++;
      cov_2l11fifpkv().s[109]++;
      return;
    } else {
      cov_2l11fifpkv().b[19][1]++;
    }
    var notificationListener = (cov_2l11fifpkv().s[110]++, Notifications.addNotificationReceivedListener(function (notification) {
      cov_2l11fifpkv().f[14]++;
      cov_2l11fifpkv().s[111]++;
      console.log('Notification received:', notification);
    }));
    var responseListener = (cov_2l11fifpkv().s[112]++, Notifications.addNotificationResponseReceivedListener(function (response) {
      cov_2l11fifpkv().f[15]++;
      cov_2l11fifpkv().s[113]++;
      console.log('Notification response:', response);
      var notificationData = (cov_2l11fifpkv().s[114]++, response.notification.request.content.data);
      cov_2l11fifpkv().s[115]++;
      if (notificationData != null && notificationData.type) {
        cov_2l11fifpkv().b[20][0]++;
        cov_2l11fifpkv().s[116]++;
        console.log('Notification type:', notificationData.type);
      } else {
        cov_2l11fifpkv().b[20][1]++;
      }
    }));
    cov_2l11fifpkv().s[117]++;
    return function () {
      cov_2l11fifpkv().f[16]++;
      cov_2l11fifpkv().s[118]++;
      Notifications.removeNotificationSubscription(notificationListener);
      cov_2l11fifpkv().s[119]++;
      Notifications.removeNotificationSubscription(responseListener);
    };
  }, [isInitialized]);
  cov_2l11fifpkv().s[120]++;
  return {
    isInitialized: isInitialized,
    settings: settings,
    pushToken: pushToken,
    initialize: initialize,
    sendLocalNotification: sendLocalNotification,
    scheduleNotification: scheduleNotification,
    cancelNotification: cancelNotification,
    scheduleDailyTips: scheduleDailyTips,
    scheduleTrainingReminder: scheduleTrainingReminder,
    sendAchievementNotification: sendAchievementNotification,
    sendProgressUpdate: sendProgressUpdate,
    scheduleMatchReminder: scheduleMatchReminder,
    updateSettings: updateSettings,
    getScheduledNotifications: getScheduledNotifications,
    error: error
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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