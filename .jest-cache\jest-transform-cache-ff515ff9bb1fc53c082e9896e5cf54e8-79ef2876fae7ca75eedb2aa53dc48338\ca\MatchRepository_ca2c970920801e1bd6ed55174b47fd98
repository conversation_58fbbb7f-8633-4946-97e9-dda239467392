ebcd4dcff15781e1c0b3ea0fc9ad6a30
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchRepository = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _supabase = require("../../../lib/supabase");
var MatchRepository = function () {
  function MatchRepository() {
    (0, _classCallCheck2.default)(this, MatchRepository);
  }
  return (0, _createClass2.default)(MatchRepository, [{
    key: "createMatch",
    value: (function () {
      var _createMatch = (0, _asyncToGenerator2.default)(function* (matchData) {
        try {
          var _yield$supabase$from$ = yield _supabase.supabase.from('matches').insert([matchData]).select().single(),
            data = _yield$supabase$from$.data,
            error = _yield$supabase$from$.error;
          if (error) {
            console.error('Error creating match:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error creating match:', error);
          return {
            data: null,
            error: 'Failed to create match'
          };
        }
      });
      function createMatch(_x) {
        return _createMatch.apply(this, arguments);
      }
      return createMatch;
    }())
  }, {
    key: "updateMatch",
    value: (function () {
      var _updateMatch = (0, _asyncToGenerator2.default)(function* (matchId, updates) {
        try {
          var _yield$supabase$from$2 = yield _supabase.supabase.from('matches').update(Object.assign({}, updates, {
              updated_at: new Date().toISOString()
            })).eq('id', matchId).select().single(),
            data = _yield$supabase$from$2.data,
            error = _yield$supabase$from$2.error;
          if (error) {
            console.error('Error updating match:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error updating match:', error);
          return {
            data: null,
            error: 'Failed to update match'
          };
        }
      });
      function updateMatch(_x2, _x3) {
        return _updateMatch.apply(this, arguments);
      }
      return updateMatch;
    }())
  }, {
    key: "getMatch",
    value: (function () {
      var _getMatch = (0, _asyncToGenerator2.default)(function* (matchId) {
        try {
          var _yield$supabase$from$3 = yield _supabase.supabase.from('matches').select('*').eq('id', matchId).single(),
            data = _yield$supabase$from$3.data,
            error = _yield$supabase$from$3.error;
          if (error) {
            console.error('Error fetching match:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error fetching match:', error);
          return {
            data: null,
            error: 'Failed to fetch match'
          };
        }
      });
      function getMatch(_x4) {
        return _getMatch.apply(this, arguments);
      }
      return getMatch;
    }())
  }, {
    key: "getUserMatches",
    value: (function () {
      var _getUserMatches = (0, _asyncToGenerator2.default)(function* (userId) {
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 50;
        var offset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0;
        try {
          var _yield$supabase$from$4 = yield _supabase.supabase.from('matches').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).range(offset, offset + limit - 1),
            data = _yield$supabase$from$4.data,
            error = _yield$supabase$from$4.error;
          if (error) {
            console.error('Error fetching user matches:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error fetching user matches:', error);
          return {
            data: null,
            error: 'Failed to fetch matches'
          };
        }
      });
      function getUserMatches(_x5) {
        return _getUserMatches.apply(this, arguments);
      }
      return getUserMatches;
    }())
  }, {
    key: "deleteMatch",
    value: (function () {
      var _deleteMatch = (0, _asyncToGenerator2.default)(function* (matchId) {
        try {
          var _yield$supabase$from$5 = yield _supabase.supabase.from('matches').delete().eq('id', matchId),
            error = _yield$supabase$from$5.error;
          if (error) {
            console.error('Error deleting match:', error);
            return {
              error: error.message
            };
          }
          return {
            error: null
          };
        } catch (error) {
          console.error('Error deleting match:', error);
          return {
            error: 'Failed to delete match'
          };
        }
      });
      function deleteMatch(_x6) {
        return _deleteMatch.apply(this, arguments);
      }
      return deleteMatch;
    }())
  }, {
    key: "createMatchSets",
    value: (function () {
      var _createMatchSets = (0, _asyncToGenerator2.default)(function* (matchId, sets) {
        try {
          var setsData = sets.map(function (set) {
            return Object.assign({}, set, {
              match_id: matchId
            });
          });
          var _yield$supabase$from$6 = yield _supabase.supabase.from('match_sets').insert(setsData).select(),
            data = _yield$supabase$from$6.data,
            error = _yield$supabase$from$6.error;
          if (error) {
            console.error('Error creating match sets:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error creating match sets:', error);
          return {
            data: null,
            error: 'Failed to create match sets'
          };
        }
      });
      function createMatchSets(_x7, _x8) {
        return _createMatchSets.apply(this, arguments);
      }
      return createMatchSets;
    }())
  }, {
    key: "getMatchSets",
    value: (function () {
      var _getMatchSets = (0, _asyncToGenerator2.default)(function* (matchId) {
        try {
          var _yield$supabase$from$7 = yield _supabase.supabase.from('match_sets').select('*').eq('match_id', matchId).order('set_number'),
            data = _yield$supabase$from$7.data,
            error = _yield$supabase$from$7.error;
          if (error) {
            console.error('Error fetching match sets:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error fetching match sets:', error);
          return {
            data: null,
            error: 'Failed to fetch match sets'
          };
        }
      });
      function getMatchSets(_x9) {
        return _getMatchSets.apply(this, arguments);
      }
      return getMatchSets;
    }())
  }, {
    key: "createMatchStatistics",
    value: (function () {
      var _createMatchStatistics = (0, _asyncToGenerator2.default)(function* (statistics) {
        try {
          var _yield$supabase$from$8 = yield _supabase.supabase.from('match_statistics').insert([statistics]).select().single(),
            data = _yield$supabase$from$8.data,
            error = _yield$supabase$from$8.error;
          if (error) {
            console.error('Error creating match statistics:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error creating match statistics:', error);
          return {
            data: null,
            error: 'Failed to create match statistics'
          };
        }
      });
      function createMatchStatistics(_x0) {
        return _createMatchStatistics.apply(this, arguments);
      }
      return createMatchStatistics;
    }())
  }, {
    key: "updateMatchStatistics",
    value: (function () {
      var _updateMatchStatistics = (0, _asyncToGenerator2.default)(function* (matchId, userId, updates) {
        try {
          var _yield$supabase$from$9 = yield _supabase.supabase.from('match_statistics').update(updates).eq('match_id', matchId).eq('user_id', userId).select().single(),
            data = _yield$supabase$from$9.data,
            error = _yield$supabase$from$9.error;
          if (error) {
            console.error('Error updating match statistics:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error updating match statistics:', error);
          return {
            data: null,
            error: 'Failed to update match statistics'
          };
        }
      });
      function updateMatchStatistics(_x1, _x10, _x11) {
        return _updateMatchStatistics.apply(this, arguments);
      }
      return updateMatchStatistics;
    }())
  }, {
    key: "getMatchStatistics",
    value: (function () {
      var _getMatchStatistics = (0, _asyncToGenerator2.default)(function* (matchId, userId) {
        try {
          var _yield$supabase$from$0 = yield _supabase.supabase.from('match_statistics').select('*').eq('match_id', matchId).eq('user_id', userId).single(),
            data = _yield$supabase$from$0.data,
            error = _yield$supabase$from$0.error;
          if (error) {
            console.error('Error fetching match statistics:', error);
            return {
              data: null,
              error: error.message
            };
          }
          return {
            data: data,
            error: null
          };
        } catch (error) {
          console.error('Error fetching match statistics:', error);
          return {
            data: null,
            error: 'Failed to fetch match statistics'
          };
        }
      });
      function getMatchStatistics(_x12, _x13) {
        return _getMatchStatistics.apply(this, arguments);
      }
      return getMatchStatistics;
    }())
  }, {
    key: "convertToDatabase",
    value: function convertToDatabase(match) {
      return {
        user_id: match.metadata.userId,
        opponent_name: match.metadata.opponentName,
        opponent_id: match.metadata.opponentId,
        match_type: match.metadata.matchType,
        match_format: match.metadata.matchFormat,
        court_type: match.metadata.surface,
        court_location: match.metadata.location,
        weather_conditions: match.metadata.weatherConditions ? {
          conditions: match.metadata.weatherConditions,
          temperature: match.metadata.temperature
        } : null,
        match_status: match.status,
        start_time: match.metadata.startTime,
        end_time: match.metadata.endTime,
        duration_minutes: match.metadata.durationMinutes,
        final_score: match.score,
        match_notes: '',
        video_url: match.videoUrl,
        video_thumbnail_url: match.videoThumbnailUrl,
        video_duration_seconds: match.videoDurationSeconds,
        video_file_size_bytes: match.videoFileSizeBytes,
        analysis_status: 'pending'
      };
    }
  }, {
    key: "convertFromDatabase",
    value: function convertFromDatabase(dbMatch) {
      var _dbMatch$weather_cond, _dbMatch$weather_cond2;
      return {
        id: dbMatch.id,
        metadata: {
          userId: dbMatch.user_id,
          opponentName: dbMatch.opponent_name,
          opponentId: dbMatch.opponent_id,
          matchType: dbMatch.match_type,
          matchFormat: dbMatch.match_format,
          surface: dbMatch.court_type,
          location: dbMatch.court_location,
          weatherConditions: (_dbMatch$weather_cond = dbMatch.weather_conditions) == null ? void 0 : _dbMatch$weather_cond.conditions,
          temperature: (_dbMatch$weather_cond2 = dbMatch.weather_conditions) == null ? void 0 : _dbMatch$weather_cond2.temperature,
          matchDate: dbMatch.created_at.split('T')[0],
          startTime: dbMatch.start_time,
          endTime: dbMatch.end_time,
          durationMinutes: dbMatch.duration_minutes
        },
        score: dbMatch.final_score || {
          sets: [],
          finalScore: '',
          result: 'win',
          setsWon: 0,
          setsLost: 0
        },
        statistics: {
          matchId: dbMatch.id,
          userId: dbMatch.user_id,
          aces: 0,
          doubleFaults: 0,
          firstServesIn: 0,
          firstServesAttempted: 0,
          firstServePointsWon: 0,
          secondServePointsWon: 0,
          firstServeReturnPointsWon: 0,
          secondServeReturnPointsWon: 0,
          breakPointsConverted: 0,
          breakPointsFaced: 0,
          winners: 0,
          unforcedErrors: 0,
          forcedErrors: 0,
          totalPointsWon: 0,
          totalPointsPlayed: 0,
          netPointsAttempted: 0,
          netPointsWon: 0,
          forehandWinners: 0,
          backhandWinners: 0,
          forehandErrors: 0,
          backhandErrors: 0
        },
        videoUrl: dbMatch.video_url,
        videoThumbnailUrl: dbMatch.video_thumbnail_url,
        videoDurationSeconds: dbMatch.video_duration_seconds,
        videoFileSizeBytes: dbMatch.video_file_size_bytes,
        status: dbMatch.match_status,
        createdAt: dbMatch.created_at,
        updatedAt: dbMatch.updated_at
      };
    }
  }]);
}();
var matchRepository = exports.matchRepository = new MatchRepository();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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