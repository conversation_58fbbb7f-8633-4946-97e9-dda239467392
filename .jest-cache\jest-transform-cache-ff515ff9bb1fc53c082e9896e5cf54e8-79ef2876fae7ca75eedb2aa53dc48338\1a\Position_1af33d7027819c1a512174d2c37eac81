026631988b2670829a283857b1d5dc00
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _PooledClass = _interopRequireDefault(require("../../vendor/react-native/PooledClass"));
var twoArgumentPooler = _PooledClass.default.twoArgumentPooler;
function Position(left, top) {
  this.left = left;
  this.top = top;
}
Position.prototype.destructor = function () {
  this.left = null;
  this.top = null;
};
_PooledClass.default.addPoolingTo(Position, twoArgumentPooler);
var _default = exports.default = Position;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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