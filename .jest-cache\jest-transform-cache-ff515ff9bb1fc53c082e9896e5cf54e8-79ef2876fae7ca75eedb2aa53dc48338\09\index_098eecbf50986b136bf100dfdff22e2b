acab0adb0e1d4992c243ec02ce12f5b9
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _createElement = _interopRequireDefault(require("../createElement"));
var forwardedProps = _interopRequireWildcard(require("../../modules/forwardedProps"));
var _pick = _interopRequireDefault(require("../../modules/pick"));
var _useElementLayout = _interopRequireDefault(require("../../modules/useElementLayout"));
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _usePlatformMethods = _interopRequireDefault(require("../../modules/usePlatformMethods"));
var _useResponderEvents = _interopRequireDefault(require("../../modules/useResponderEvents"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _TextAncestorContext = _interopRequireDefault(require("../Text/TextAncestorContext"));
var _useLocale = require("../../modules/useLocale");
var _excluded = ["hrefAttrs", "onLayout", "onMoveShouldSetResponder", "onMoveShouldSetResponderCapture", "onResponderEnd", "onResponderGrant", "onResponderMove", "onResponderReject", "onResponderRelease", "onResponderStart", "onResponderTerminate", "onResponderTerminationRequest", "onScrollShouldSetResponder", "onScrollShouldSetResponderCapture", "onSelectionChangeShouldSetResponder", "onSelectionChangeShouldSetResponderCapture", "onStartShouldSetResponder", "onStartShouldSetResponderCapture"];
var forwardPropsList = Object.assign({}, forwardedProps.defaultProps, forwardedProps.accessibilityProps, forwardedProps.clickProps, forwardedProps.focusProps, forwardedProps.keyboardProps, forwardedProps.mouseProps, forwardedProps.touchProps, forwardedProps.styleProps, {
  href: true,
  lang: true,
  onScroll: true,
  onWheel: true,
  pointerEvents: true
});
var pickProps = function pickProps(props) {
  return (0, _pick.default)(props, forwardPropsList);
};
var View = React.forwardRef(function (props, forwardedRef) {
  var hrefAttrs = props.hrefAttrs,
    onLayout = props.onLayout,
    onMoveShouldSetResponder = props.onMoveShouldSetResponder,
    onMoveShouldSetResponderCapture = props.onMoveShouldSetResponderCapture,
    onResponderEnd = props.onResponderEnd,
    onResponderGrant = props.onResponderGrant,
    onResponderMove = props.onResponderMove,
    onResponderReject = props.onResponderReject,
    onResponderRelease = props.onResponderRelease,
    onResponderStart = props.onResponderStart,
    onResponderTerminate = props.onResponderTerminate,
    onResponderTerminationRequest = props.onResponderTerminationRequest,
    onScrollShouldSetResponder = props.onScrollShouldSetResponder,
    onScrollShouldSetResponderCapture = props.onScrollShouldSetResponderCapture,
    onSelectionChangeShouldSetResponder = props.onSelectionChangeShouldSetResponder,
    onSelectionChangeShouldSetResponderCapture = props.onSelectionChangeShouldSetResponderCapture,
    onStartShouldSetResponder = props.onStartShouldSetResponder,
    onStartShouldSetResponderCapture = props.onStartShouldSetResponderCapture,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  if (process.env.NODE_ENV !== 'production') {
    React.Children.toArray(props.children).forEach(function (item) {
      if (typeof item === 'string') {
        console.error("Unexpected text node: " + item + ". A text node cannot be a child of a <View>.");
      }
    });
  }
  var hasTextAncestor = React.useContext(_TextAncestorContext.default);
  var hostRef = React.useRef(null);
  var _useLocaleContext = (0, _useLocale.useLocaleContext)(),
    contextDirection = _useLocaleContext.direction;
  (0, _useElementLayout.default)(hostRef, onLayout);
  (0, _useResponderEvents.default)(hostRef, {
    onMoveShouldSetResponder: onMoveShouldSetResponder,
    onMoveShouldSetResponderCapture: onMoveShouldSetResponderCapture,
    onResponderEnd: onResponderEnd,
    onResponderGrant: onResponderGrant,
    onResponderMove: onResponderMove,
    onResponderReject: onResponderReject,
    onResponderRelease: onResponderRelease,
    onResponderStart: onResponderStart,
    onResponderTerminate: onResponderTerminate,
    onResponderTerminationRequest: onResponderTerminationRequest,
    onScrollShouldSetResponder: onScrollShouldSetResponder,
    onScrollShouldSetResponderCapture: onScrollShouldSetResponderCapture,
    onSelectionChangeShouldSetResponder: onSelectionChangeShouldSetResponder,
    onSelectionChangeShouldSetResponderCapture: onSelectionChangeShouldSetResponderCapture,
    onStartShouldSetResponder: onStartShouldSetResponder,
    onStartShouldSetResponderCapture: onStartShouldSetResponderCapture
  });
  var component = 'div';
  var langDirection = props.lang != null ? (0, _useLocale.getLocaleDirection)(props.lang) : null;
  var componentDirection = props.dir || langDirection;
  var writingDirection = componentDirection || contextDirection;
  var supportedProps = pickProps(rest);
  supportedProps.dir = componentDirection;
  supportedProps.style = [styles.view$raw, hasTextAncestor && styles.inline, props.style];
  if (props.href != null) {
    component = 'a';
    if (hrefAttrs != null) {
      var download = hrefAttrs.download,
        rel = hrefAttrs.rel,
        target = hrefAttrs.target;
      if (download != null) {
        supportedProps.download = download;
      }
      if (rel != null) {
        supportedProps.rel = rel;
      }
      if (typeof target === 'string') {
        supportedProps.target = target.charAt(0) !== '_' ? '_' + target : target;
      }
    }
  }
  var platformMethodsRef = (0, _usePlatformMethods.default)(supportedProps);
  var setRef = (0, _useMergeRefs.default)(hostRef, platformMethodsRef, forwardedRef);
  supportedProps.ref = setRef;
  return (0, _createElement.default)(component, supportedProps, {
    writingDirection: writingDirection
  });
});
View.displayName = 'View';
var styles = _StyleSheet.default.create({
  view$raw: {
    alignContent: 'flex-start',
    alignItems: 'stretch',
    backgroundColor: 'transparent',
    border: '0 solid black',
    boxSizing: 'border-box',
    display: 'flex',
    flexBasis: 'auto',
    flexDirection: 'column',
    flexShrink: 0,
    listStyle: 'none',
    margin: 0,
    minHeight: 0,
    minWidth: 0,
    padding: 0,
    position: 'relative',
    textDecoration: 'none',
    zIndex: 0
  },
  inline: {
    display: 'inline-flex'
  }
});
var _default = exports.default = View;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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