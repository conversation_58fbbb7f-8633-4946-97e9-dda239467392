b1124df83d7cd0e212f1ca20621036d5
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2iu4jdw5go() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\poseDetection.ts";
  var hash = "8540b58fd2df55e8aaec684af943e42f26a403ef";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\poseDetection.ts",
    statementMap: {
      "0": {
        start: {
          line: 54,
          column: 24
        },
        end: {
          line: 54,
          column: 29
        }
      },
      "1": {
        start: {
          line: 55,
          column: 26
        },
        end: {
          line: 65,
          column: 3
        }
      },
      "2": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 84,
          column: 5
        }
      },
      "3": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 58
        }
      },
      "4": {
        start: {
          line: 76,
          column: 6
        },
        end: {
          line: 76,
          column: 62
        }
      },
      "5": {
        start: {
          line: 76,
          column: 35
        },
        end: {
          line: 76,
          column: 60
        }
      },
      "6": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 30
        }
      },
      "7": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 62
        }
      },
      "8": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 80,
          column: 18
        }
      },
      "9": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 67
        }
      },
      "10": {
        start: {
          line: 83,
          column: 6
        },
        end: {
          line: 83,
          column: 19
        }
      },
      "11": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "12": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 35
        }
      },
      "13": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 110,
          column: 5
        }
      },
      "14": {
        start: {
          line: 96,
          column: 34
        },
        end: {
          line: 96,
          column: 36
        }
      },
      "15": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 104,
          column: 7
        }
      },
      "16": {
        start: {
          line: 98,
          column: 19
        },
        end: {
          line: 98,
          column: 20
        }
      },
      "17": {
        start: {
          line: 99,
          column: 22
        },
        end: {
          line: 99,
          column: 36
        }
      },
      "18": {
        start: {
          line: 100,
          column: 21
        },
        end: {
          line: 100,
          column: 67
        }
      },
      "19": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 103,
          column: 9
        }
      },
      "20": {
        start: {
          line: 102,
          column: 10
        },
        end: {
          line: 102,
          column: 27
        }
      },
      "21": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 106,
          column: 19
        }
      },
      "22": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 52
        }
      },
      "23": {
        start: {
          line: 109,
          column: 6
        },
        end: {
          line: 109,
          column: 56
        }
      },
      "24": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 137,
          column: 5
        }
      },
      "25": {
        start: {
          line: 123,
          column: 27
        },
        end: {
          line: 123,
          column: 58
        }
      },
      "26": {
        start: {
          line: 124,
          column: 21
        },
        end: {
          line: 124,
          column: 71
        }
      },
      "27": {
        start: {
          line: 125,
          column: 23
        },
        end: {
          line: 125,
          column: 73
        }
      },
      "28": {
        start: {
          line: 126,
          column: 30
        },
        end: {
          line: 126,
          column: 76
        }
      },
      "29": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 133,
          column: 8
        }
      },
      "30": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 56
        }
      },
      "31": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 40
        }
      },
      "32": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 174,
          column: 5
        }
      },
      "33": {
        start: {
          line: 149,
          column: 19
        },
        end: {
          line: 149,
          column: 70
        }
      },
      "34": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 157,
          column: 7
        }
      },
      "35": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 156,
          column: 10
        }
      },
      "36": {
        start: {
          line: 159,
          column: 23
        },
        end: {
          line: 159,
          column: 54
        }
      },
      "37": {
        start: {
          line: 160,
          column: 26
        },
        end: {
          line: 160,
          column: 60
        }
      },
      "38": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 166,
          column: 8
        }
      },
      "39": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 56
        }
      },
      "40": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 173,
          column: 8
        }
      },
      "41": {
        start: {
          line: 185,
          column: 22
        },
        end: {
          line: 185,
          column: 49
        }
      },
      "42": {
        start: {
          line: 186,
          column: 23
        },
        end: {
          line: 186,
          column: 72
        }
      },
      "43": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 187,
          column: 69
        }
      },
      "44": {
        start: {
          line: 188,
          column: 25
        },
        end: {
          line: 188,
          column: 62
        }
      },
      "45": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 194,
          column: 6
        }
      },
      "46": {
        start: {
          line: 201,
          column: 4
        },
        end: {
          line: 201,
          column: 58
        }
      },
      "47": {
        start: {
          line: 201,
          column: 33
        },
        end: {
          line: 201,
          column: 56
        }
      },
      "48": {
        start: {
          line: 204,
          column: 22
        },
        end: {
          line: 204,
          column: 55
        }
      },
      "49": {
        start: {
          line: 205,
          column: 21
        },
        end: {
          line: 205,
          column: 51
        }
      },
      "50": {
        start: {
          line: 206,
          column: 22
        },
        end: {
          line: 206,
          column: 64
        }
      },
      "51": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 214,
          column: 6
        }
      },
      "52": {
        start: {
          line: 218,
          column: 4
        },
        end: {
          line: 263,
          column: 7
        }
      },
      "53": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 254,
          column: 7
        }
      },
      "54": {
        start: {
          line: 224,
          column: 10
        },
        end: {
          line: 224,
          column: 18
        }
      },
      "55": {
        start: {
          line: 224,
          column: 19
        },
        end: {
          line: 224,
          column: 27
        }
      },
      "56": {
        start: {
          line: 224,
          column: 28
        },
        end: {
          line: 224,
          column: 34
        }
      },
      "57": {
        start: {
          line: 225,
          column: 10
        },
        end: {
          line: 225,
          column: 16
        }
      },
      "58": {
        start: {
          line: 227,
          column: 10
        },
        end: {
          line: 227,
          column: 18
        }
      },
      "59": {
        start: {
          line: 227,
          column: 19
        },
        end: {
          line: 227,
          column: 28
        }
      },
      "60": {
        start: {
          line: 227,
          column: 29
        },
        end: {
          line: 227,
          column: 38
        }
      },
      "61": {
        start: {
          line: 228,
          column: 10
        },
        end: {
          line: 228,
          column: 16
        }
      },
      "62": {
        start: {
          line: 230,
          column: 10
        },
        end: {
          line: 230,
          column: 18
        }
      },
      "63": {
        start: {
          line: 230,
          column: 19
        },
        end: {
          line: 230,
          column: 28
        }
      },
      "64": {
        start: {
          line: 230,
          column: 29
        },
        end: {
          line: 230,
          column: 38
        }
      },
      "65": {
        start: {
          line: 231,
          column: 10
        },
        end: {
          line: 231,
          column: 16
        }
      },
      "66": {
        start: {
          line: 233,
          column: 10
        },
        end: {
          line: 233,
          column: 40
        }
      },
      "67": {
        start: {
          line: 233,
          column: 41
        },
        end: {
          line: 233,
          column: 71
        }
      },
      "68": {
        start: {
          line: 233,
          column: 72
        },
        end: {
          line: 233,
          column: 81
        }
      },
      "69": {
        start: {
          line: 234,
          column: 10
        },
        end: {
          line: 234,
          column: 16
        }
      },
      "70": {
        start: {
          line: 236,
          column: 10
        },
        end: {
          line: 236,
          column: 40
        }
      },
      "71": {
        start: {
          line: 236,
          column: 41
        },
        end: {
          line: 236,
          column: 71
        }
      },
      "72": {
        start: {
          line: 236,
          column: 72
        },
        end: {
          line: 236,
          column: 81
        }
      },
      "73": {
        start: {
          line: 237,
          column: 10
        },
        end: {
          line: 237,
          column: 16
        }
      },
      "74": {
        start: {
          line: 239,
          column: 10
        },
        end: {
          line: 239,
          column: 19
        }
      },
      "75": {
        start: {
          line: 239,
          column: 20
        },
        end: {
          line: 239,
          column: 28
        }
      },
      "76": {
        start: {
          line: 239,
          column: 29
        },
        end: {
          line: 239,
          column: 35
        }
      },
      "77": {
        start: {
          line: 240,
          column: 10
        },
        end: {
          line: 240,
          column: 16
        }
      },
      "78": {
        start: {
          line: 242,
          column: 10
        },
        end: {
          line: 242,
          column: 19
        }
      },
      "79": {
        start: {
          line: 242,
          column: 20
        },
        end: {
          line: 242,
          column: 28
        }
      },
      "80": {
        start: {
          line: 242,
          column: 29
        },
        end: {
          line: 242,
          column: 35
        }
      },
      "81": {
        start: {
          line: 243,
          column: 10
        },
        end: {
          line: 243,
          column: 16
        }
      },
      "82": {
        start: {
          line: 245,
          column: 10
        },
        end: {
          line: 245,
          column: 19
        }
      },
      "83": {
        start: {
          line: 245,
          column: 20
        },
        end: {
          line: 245,
          column: 28
        }
      },
      "84": {
        start: {
          line: 245,
          column: 29
        },
        end: {
          line: 245,
          column: 37
        }
      },
      "85": {
        start: {
          line: 246,
          column: 10
        },
        end: {
          line: 246,
          column: 16
        }
      },
      "86": {
        start: {
          line: 248,
          column: 10
        },
        end: {
          line: 248,
          column: 19
        }
      },
      "87": {
        start: {
          line: 248,
          column: 20
        },
        end: {
          line: 248,
          column: 28
        }
      },
      "88": {
        start: {
          line: 248,
          column: 29
        },
        end: {
          line: 248,
          column: 37
        }
      },
      "89": {
        start: {
          line: 249,
          column: 10
        },
        end: {
          line: 249,
          column: 16
        }
      },
      "90": {
        start: {
          line: 251,
          column: 10
        },
        end: {
          line: 251,
          column: 48
        }
      },
      "91": {
        start: {
          line: 252,
          column: 10
        },
        end: {
          line: 252,
          column: 48
        }
      },
      "92": {
        start: {
          line: 253,
          column: 10
        },
        end: {
          line: 253,
          column: 42
        }
      },
      "93": {
        start: {
          line: 256,
          column: 6
        },
        end: {
          line: 262,
          column: 8
        }
      },
      "94": {
        start: {
          line: 267,
          column: 23
        },
        end: {
          line: 267,
          column: 68
        }
      },
      "95": {
        start: {
          line: 267,
          column: 43
        },
        end: {
          line: 267,
          column: 67
        }
      },
      "96": {
        start: {
          line: 268,
          column: 22
        },
        end: {
          line: 268,
          column: 66
        }
      },
      "97": {
        start: {
          line: 268,
          column: 42
        },
        end: {
          line: 268,
          column: 65
        }
      },
      "98": {
        start: {
          line: 269,
          column: 26
        },
        end: {
          line: 269,
          column: 74
        }
      },
      "99": {
        start: {
          line: 269,
          column: 46
        },
        end: {
          line: 269,
          column: 73
        }
      },
      "100": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 273,
          column: 5
        }
      },
      "101": {
        start: {
          line: 272,
          column: 6
        },
        end: {
          line: 272,
          column: 23
        }
      },
      "102": {
        start: {
          line: 276,
          column: 22
        },
        end: {
          line: 276,
          column: 34
        }
      },
      "103": {
        start: {
          line: 277,
          column: 25
        },
        end: {
          line: 277,
          column: 65
        }
      },
      "104": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 279,
          column: 40
        }
      },
      "105": {
        start: {
          line: 279,
          column: 25
        },
        end: {
          line: 279,
          column: 40
        }
      },
      "106": {
        start: {
          line: 280,
          column: 4
        },
        end: {
          line: 280,
          column: 43
        }
      },
      "107": {
        start: {
          line: 280,
          column: 25
        },
        end: {
          line: 280,
          column: 43
        }
      },
      "108": {
        start: {
          line: 281,
          column: 4
        },
        end: {
          line: 281,
          column: 45
        }
      },
      "109": {
        start: {
          line: 281,
          column: 29
        },
        end: {
          line: 281,
          column: 45
        }
      },
      "110": {
        start: {
          line: 282,
          column: 4
        },
        end: {
          line: 282,
          column: 58
        }
      },
      "111": {
        start: {
          line: 282,
          column: 40
        },
        end: {
          line: 282,
          column: 58
        }
      },
      "112": {
        start: {
          line: 283,
          column: 4
        },
        end: {
          line: 283,
          column: 22
        }
      },
      "113": {
        start: {
          line: 288,
          column: 18
        },
        end: {
          line: 288,
          column: 52
        }
      },
      "114": {
        start: {
          line: 289,
          column: 46
        },
        end: {
          line: 289,
          column: 102
        }
      },
      "115": {
        start: {
          line: 290,
          column: 4
        },
        end: {
          line: 290,
          column: 25
        }
      },
      "116": {
        start: {
          line: 295,
          column: 25
        },
        end: {
          line: 303,
          column: 5
        }
      },
      "117": {
        start: {
          line: 306,
          column: 20
        },
        end: {
          line: 310,
          column: 5
        }
      },
      "118": {
        start: {
          line: 313,
          column: 19
        },
        end: {
          line: 318,
          column: 5
        }
      },
      "119": {
        start: {
          line: 320,
          column: 4
        },
        end: {
          line: 324,
          column: 6
        }
      },
      "120": {
        start: {
          line: 328,
          column: 29
        },
        end: {
          line: 328,
          column: 77
        }
      },
      "121": {
        start: {
          line: 328,
          column: 47
        },
        end: {
          line: 328,
          column: 76
        }
      },
      "122": {
        start: {
          line: 329,
          column: 25
        },
        end: {
          line: 329,
          column: 69
        }
      },
      "123": {
        start: {
          line: 329,
          column: 43
        },
        end: {
          line: 329,
          column: 68
        }
      },
      "124": {
        start: {
          line: 330,
          column: 31
        },
        end: {
          line: 330,
          column: 82
        }
      },
      "125": {
        start: {
          line: 330,
          column: 49
        },
        end: {
          line: 330,
          column: 81
        }
      },
      "126": {
        start: {
          line: 332,
          column: 24
        },
        end: {
          line: 332,
          column: 61
        }
      },
      "127": {
        start: {
          line: 333,
          column: 20
        },
        end: {
          line: 333,
          column: 53
        }
      },
      "128": {
        start: {
          line: 334,
          column: 26
        },
        end: {
          line: 334,
          column: 65
        }
      },
      "129": {
        start: {
          line: 335,
          column: 21
        },
        end: {
          line: 335,
          column: 51
        }
      },
      "130": {
        start: {
          line: 336,
          column: 25
        },
        end: {
          line: 336,
          column: 110
        }
      },
      "131": {
        start: {
          line: 337,
          column: 24
        },
        end: {
          line: 337,
          column: 106
        }
      },
      "132": {
        start: {
          line: 339,
          column: 20
        },
        end: {
          line: 339,
          column: 103
        }
      },
      "133": {
        start: {
          line: 341,
          column: 4
        },
        end: {
          line: 349,
          column: 6
        }
      },
      "134": {
        start: {
          line: 353,
          column: 4
        },
        end: {
          line: 353,
          column: 38
        }
      },
      "135": {
        start: {
          line: 353,
          column: 28
        },
        end: {
          line: 353,
          column: 38
        }
      },
      "136": {
        start: {
          line: 355,
          column: 26
        },
        end: {
          line: 355,
          column: 94
        }
      },
      "137": {
        start: {
          line: 355,
          column: 54
        },
        end: {
          line: 355,
          column: 75
        }
      },
      "138": {
        start: {
          line: 356,
          column: 4
        },
        end: {
          line: 356,
          column: 54
        }
      },
      "139": {
        start: {
          line: 360,
          column: 31
        },
        end: {
          line: 360,
          column: 33
        }
      },
      "140": {
        start: {
          line: 362,
          column: 4
        },
        end: {
          line: 364,
          column: 5
        }
      },
      "141": {
        start: {
          line: 363,
          column: 6
        },
        end: {
          line: 363,
          column: 64
        }
      },
      "142": {
        start: {
          line: 366,
          column: 4
        },
        end: {
          line: 368,
          column: 5
        }
      },
      "143": {
        start: {
          line: 367,
          column: 6
        },
        end: {
          line: 367,
          column: 75
        }
      },
      "144": {
        start: {
          line: 370,
          column: 4
        },
        end: {
          line: 372,
          column: 5
        }
      },
      "145": {
        start: {
          line: 371,
          column: 6
        },
        end: {
          line: 371,
          column: 75
        }
      },
      "146": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 376,
          column: 5
        }
      },
      "147": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 375,
          column: 61
        }
      },
      "148": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 20
        }
      },
      "149": {
        start: {
          line: 382,
          column: 38
        },
        end: {
          line: 382,
          column: 40
        }
      },
      "150": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 386,
          column: 5
        }
      },
      "151": {
        start: {
          line: 385,
          column: 6
        },
        end: {
          line: 385,
          column: 78
        }
      },
      "152": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 390,
          column: 5
        }
      },
      "153": {
        start: {
          line: 389,
          column: 6
        },
        end: {
          line: 389,
          column: 84
        }
      },
      "154": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 394,
          column: 5
        }
      },
      "155": {
        start: {
          line: 393,
          column: 6
        },
        end: {
          line: 393,
          column: 82
        }
      },
      "156": {
        start: {
          line: 396,
          column: 4
        },
        end: {
          line: 396,
          column: 27
        }
      },
      "157": {
        start: {
          line: 400,
          column: 31
        },
        end: {
          line: 400,
          column: 33
        }
      },
      "158": {
        start: {
          line: 402,
          column: 4
        },
        end: {
          line: 404,
          column: 5
        }
      },
      "159": {
        start: {
          line: 403,
          column: 6
        },
        end: {
          line: 403,
          column: 56
        }
      },
      "160": {
        start: {
          line: 406,
          column: 4
        },
        end: {
          line: 416,
          column: 5
        }
      },
      "161": {
        start: {
          line: 408,
          column: 8
        },
        end: {
          line: 408,
          column: 73
        }
      },
      "162": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 14
        }
      },
      "163": {
        start: {
          line: 411,
          column: 8
        },
        end: {
          line: 411,
          column: 51
        }
      },
      "164": {
        start: {
          line: 412,
          column: 8
        },
        end: {
          line: 412,
          column: 14
        }
      },
      "165": {
        start: {
          line: 414,
          column: 8
        },
        end: {
          line: 414,
          column: 55
        }
      },
      "166": {
        start: {
          line: 415,
          column: 8
        },
        end: {
          line: 415,
          column: 14
        }
      },
      "167": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 418,
          column: 20
        }
      },
      "168": {
        start: {
          line: 422,
          column: 34
        },
        end: {
          line: 422,
          column: 36
        }
      },
      "169": {
        start: {
          line: 425,
          column: 23
        },
        end: {
          line: 425,
          column: 73
        }
      },
      "170": {
        start: {
          line: 425,
          column: 48
        },
        end: {
          line: 425,
          column: 72
        }
      },
      "171": {
        start: {
          line: 426,
          column: 26
        },
        end: {
          line: 426,
          column: 79
        }
      },
      "172": {
        start: {
          line: 426,
          column: 51
        },
        end: {
          line: 426,
          column: 78
        }
      },
      "173": {
        start: {
          line: 428,
          column: 4
        },
        end: {
          line: 430,
          column: 5
        }
      },
      "174": {
        start: {
          line: 429,
          column: 6
        },
        end: {
          line: 429,
          column: 58
        }
      },
      "175": {
        start: {
          line: 432,
          column: 4
        },
        end: {
          line: 432,
          column: 23
        }
      },
      "176": {
        start: {
          line: 437,
          column: 4
        },
        end: {
          line: 443,
          column: 6
        }
      },
      "177": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 448,
          column: 45
        }
      },
      "178": {
        start: {
          line: 453,
          column: 26
        },
        end: {
          line: 453,
          column: 27
        }
      },
      "179": {
        start: {
          line: 454,
          column: 22
        },
        end: {
          line: 454,
          column: 23
        }
      },
      "180": {
        start: {
          line: 456,
          column: 4
        },
        end: {
          line: 467,
          column: 7
        }
      },
      "181": {
        start: {
          line: 457,
          column: 28
        },
        end: {
          line: 457,
          column: 87
        }
      },
      "182": {
        start: {
          line: 457,
          column: 58
        },
        end: {
          line: 457,
          column: 86
        }
      },
      "183": {
        start: {
          line: 458,
          column: 6
        },
        end: {
          line: 466,
          column: 7
        }
      },
      "184": {
        start: {
          line: 459,
          column: 25
        },
        end: {
          line: 463,
          column: 9
        }
      },
      "185": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 464,
          column: 36
        }
      },
      "186": {
        start: {
          line: 465,
          column: 8
        },
        end: {
          line: 465,
          column: 22
        }
      },
      "187": {
        start: {
          line: 469,
          column: 26
        },
        end: {
          line: 469,
          column: 55
        }
      },
      "188": {
        start: {
          line: 470,
          column: 4
        },
        end: {
          line: 470,
          column: 65
        }
      },
      "189": {
        start: {
          line: 475,
          column: 4
        },
        end: {
          line: 479,
          column: 6
        }
      },
      "190": {
        start: {
          line: 484,
          column: 4
        },
        end: {
          line: 484,
          column: 55
        }
      },
      "191": {
        start: {
          line: 484,
          column: 35
        },
        end: {
          line: 484,
          column: 53
        }
      },
      "192": {
        start: {
          line: 488,
          column: 32
        },
        end: {
          line: 488,
          column: 34
        }
      },
      "193": {
        start: {
          line: 490,
          column: 4
        },
        end: {
          line: 498,
          column: 5
        }
      },
      "194": {
        start: {
          line: 490,
          column: 17
        },
        end: {
          line: 490,
          column: 18
        }
      },
      "195": {
        start: {
          line: 491,
          column: 6
        },
        end: {
          line: 497,
          column: 9
        }
      },
      "196": {
        start: {
          line: 500,
          column: 4
        },
        end: {
          line: 500,
          column: 17
        }
      },
      "197": {
        start: {
          line: 504,
          column: 4
        },
        end: {
          line: 525,
          column: 6
        }
      },
      "198": {
        start: {
          line: 529,
          column: 36
        },
        end: {
          line: 529,
          column: 62
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 70,
            column: 2
          },
          end: {
            line: 70,
            column: 3
          }
        },
        loc: {
          start: {
            line: 70,
            column: 44
          },
          end: {
            line: 85,
            column: 3
          }
        },
        line: 70
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 76,
            column: 24
          },
          end: {
            line: 76,
            column: 25
          }
        },
        loc: {
          start: {
            line: 76,
            column: 35
          },
          end: {
            line: 76,
            column: 60
          }
        },
        line: 76
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        },
        loc: {
          start: {
            line: 90,
            column: 69
          },
          end: {
            line: 111,
            column: 3
          }
        },
        line: 90
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 116,
            column: 2
          },
          end: {
            line: 116,
            column: 3
          }
        },
        loc: {
          start: {
            line: 121,
            column: 4
          },
          end: {
            line: 138,
            column: 3
          }
        },
        line: 121
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 143,
            column: 2
          },
          end: {
            line: 143,
            column: 3
          }
        },
        loc: {
          start: {
            line: 147,
            column: 5
          },
          end: {
            line: 175,
            column: 3
          }
        },
        line: 147
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 180,
            column: 2
          },
          end: {
            line: 180,
            column: 3
          }
        },
        loc: {
          start: {
            line: 184,
            column: 4
          },
          end: {
            line: 195,
            column: 3
          }
        },
        line: 184
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 199,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        },
        loc: {
          start: {
            line: 199,
            column: 103
          },
          end: {
            line: 215,
            column: 3
          }
        },
        line: 199
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 201,
            column: 22
          },
          end: {
            line: 201,
            column: 23
          }
        },
        loc: {
          start: {
            line: 201,
            column: 33
          },
          end: {
            line: 201,
            column: 56
          }
        },
        line: 201
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 217,
            column: 2
          },
          end: {
            line: 217,
            column: 3
          }
        },
        loc: {
          start: {
            line: 217,
            column: 55
          },
          end: {
            line: 264,
            column: 3
          }
        },
        line: 217
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 218,
            column: 34
          },
          end: {
            line: 218,
            column: 35
          }
        },
        loc: {
          start: {
            line: 218,
            column: 51
          },
          end: {
            line: 263,
            column: 5
          }
        },
        line: 218
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 266,
            column: 2
          },
          end: {
            line: 266,
            column: 3
          }
        },
        loc: {
          start: {
            line: 266,
            column: 76
          },
          end: {
            line: 284,
            column: 3
          }
        },
        line: 266
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 267,
            column: 38
          },
          end: {
            line: 267,
            column: 39
          }
        },
        loc: {
          start: {
            line: 267,
            column: 43
          },
          end: {
            line: 267,
            column: 67
          }
        },
        line: 267
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 268,
            column: 37
          },
          end: {
            line: 268,
            column: 38
          }
        },
        loc: {
          start: {
            line: 268,
            column: 42
          },
          end: {
            line: 268,
            column: 65
          }
        },
        line: 268
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 269,
            column: 41
          },
          end: {
            line: 269,
            column: 42
          }
        },
        loc: {
          start: {
            line: 269,
            column: 46
          },
          end: {
            line: 269,
            column: 73
          }
        },
        line: 269
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 286,
            column: 2
          },
          end: {
            line: 286,
            column: 3
          }
        },
        loc: {
          start: {
            line: 286,
            column: 97
          },
          end: {
            line: 291,
            column: 3
          }
        },
        line: 286
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 293,
            column: 2
          },
          end: {
            line: 293,
            column: 3
          }
        },
        loc: {
          start: {
            line: 293,
            column: 74
          },
          end: {
            line: 325,
            column: 3
          }
        },
        line: 293
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 327,
            column: 2
          },
          end: {
            line: 327,
            column: 3
          }
        },
        loc: {
          start: {
            line: 327,
            column: 110
          },
          end: {
            line: 350,
            column: 3
          }
        },
        line: 327
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 328,
            column: 42
          },
          end: {
            line: 328,
            column: 43
          }
        },
        loc: {
          start: {
            line: 328,
            column: 47
          },
          end: {
            line: 328,
            column: 76
          }
        },
        line: 328
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 329,
            column: 38
          },
          end: {
            line: 329,
            column: 39
          }
        },
        loc: {
          start: {
            line: 329,
            column: 43
          },
          end: {
            line: 329,
            column: 68
          }
        },
        line: 329
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 330,
            column: 44
          },
          end: {
            line: 330,
            column: 45
          }
        },
        loc: {
          start: {
            line: 330,
            column: 49
          },
          end: {
            line: 330,
            column: 81
          }
        },
        line: 330
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 352,
            column: 2
          },
          end: {
            line: 352,
            column: 3
          }
        },
        loc: {
          start: {
            line: 352,
            column: 54
          },
          end: {
            line: 357,
            column: 3
          }
        },
        line: 352
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 355,
            column: 39
          },
          end: {
            line: 355,
            column: 40
          }
        },
        loc: {
          start: {
            line: 355,
            column: 54
          },
          end: {
            line: 355,
            column: 75
          }
        },
        line: 355
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 359,
            column: 2
          },
          end: {
            line: 359,
            column: 3
          }
        },
        loc: {
          start: {
            line: 359,
            column: 120
          },
          end: {
            line: 379,
            column: 3
          }
        },
        line: 359
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 381,
            column: 2
          },
          end: {
            line: 381,
            column: 3
          }
        },
        loc: {
          start: {
            line: 381,
            column: 89
          },
          end: {
            line: 397,
            column: 3
          }
        },
        line: 381
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 399,
            column: 2
          },
          end: {
            line: 399,
            column: 3
          }
        },
        loc: {
          start: {
            line: 399,
            column: 59
          },
          end: {
            line: 419,
            column: 3
          }
        },
        line: 399
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 421,
            column: 2
          },
          end: {
            line: 421,
            column: 3
          }
        },
        loc: {
          start: {
            line: 421,
            column: 62
          },
          end: {
            line: 433,
            column: 3
          }
        },
        line: 421
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 425,
            column: 43
          },
          end: {
            line: 425,
            column: 44
          }
        },
        loc: {
          start: {
            line: 425,
            column: 48
          },
          end: {
            line: 425,
            column: 72
          }
        },
        line: 425
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 426,
            column: 46
          },
          end: {
            line: 426,
            column: 47
          }
        },
        loc: {
          start: {
            line: 426,
            column: 51
          },
          end: {
            line: 426,
            column: 78
          }
        },
        line: 426
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 435,
            column: 2
          },
          end: {
            line: 435,
            column: 3
          }
        },
        loc: {
          start: {
            line: 435,
            column: 53
          },
          end: {
            line: 444,
            column: 3
          }
        },
        line: 435
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 446,
            column: 2
          },
          end: {
            line: 446,
            column: 3
          }
        },
        loc: {
          start: {
            line: 446,
            column: 67
          },
          end: {
            line: 449,
            column: 3
          }
        },
        line: 446
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 451,
            column: 2
          },
          end: {
            line: 451,
            column: 3
          }
        },
        loc: {
          start: {
            line: 451,
            column: 87
          },
          end: {
            line: 471,
            column: 3
          }
        },
        line: 451
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 456,
            column: 31
          },
          end: {
            line: 456,
            column: 32
          }
        },
        loc: {
          start: {
            line: 456,
            column: 47
          },
          end: {
            line: 467,
            column: 5
          }
        },
        line: 456
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 457,
            column: 53
          },
          end: {
            line: 457,
            column: 54
          }
        },
        loc: {
          start: {
            line: 457,
            column: 58
          },
          end: {
            line: 457,
            column: 86
          }
        },
        line: 457
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 473,
            column: 2
          },
          end: {
            line: 473,
            column: 3
          }
        },
        loc: {
          start: {
            line: 473,
            column: 85
          },
          end: {
            line: 480,
            column: 3
          }
        },
        line: 473
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 482,
            column: 2
          },
          end: {
            line: 482,
            column: 3
          }
        },
        loc: {
          start: {
            line: 482,
            column: 63
          },
          end: {
            line: 485,
            column: 3
          }
        },
        line: 482
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 484,
            column: 27
          },
          end: {
            line: 484,
            column: 28
          }
        },
        loc: {
          start: {
            line: 484,
            column: 35
          },
          end: {
            line: 484,
            column: 53
          }
        },
        line: 484
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 487,
            column: 2
          },
          end: {
            line: 487,
            column: 3
          }
        },
        loc: {
          start: {
            line: 487,
            column: 62
          },
          end: {
            line: 501,
            column: 3
          }
        },
        line: 487
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 503,
            column: 2
          },
          end: {
            line: 503,
            column: 3
          }
        },
        loc: {
          start: {
            line: 503,
            column: 32
          },
          end: {
            line: 526,
            column: 3
          }
        },
        line: 503
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "1": {
        loc: {
          start: {
            line: 101,
            column: 8
          },
          end: {
            line: 103,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 101,
            column: 8
          },
          end: {
            line: 103,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 101
      },
      "2": {
        loc: {
          start: {
            line: 151,
            column: 6
          },
          end: {
            line: 157,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 6
          },
          end: {
            line: 157,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "3": {
        loc: {
          start: {
            line: 222,
            column: 6
          },
          end: {
            line: 254,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 223,
            column: 8
          },
          end: {
            line: 225,
            column: 16
          }
        }, {
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 228,
            column: 16
          }
        }, {
          start: {
            line: 229,
            column: 8
          },
          end: {
            line: 231,
            column: 16
          }
        }, {
          start: {
            line: 232,
            column: 8
          },
          end: {
            line: 234,
            column: 16
          }
        }, {
          start: {
            line: 235,
            column: 8
          },
          end: {
            line: 237,
            column: 16
          }
        }, {
          start: {
            line: 238,
            column: 8
          },
          end: {
            line: 240,
            column: 16
          }
        }, {
          start: {
            line: 241,
            column: 8
          },
          end: {
            line: 243,
            column: 16
          }
        }, {
          start: {
            line: 244,
            column: 8
          },
          end: {
            line: 246,
            column: 16
          }
        }, {
          start: {
            line: 247,
            column: 8
          },
          end: {
            line: 249,
            column: 16
          }
        }, {
          start: {
            line: 250,
            column: 8
          },
          end: {
            line: 253,
            column: 42
          }
        }],
        line: 222
      },
      "4": {
        loc: {
          start: {
            line: 271,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "5": {
        loc: {
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 271,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 271,
            column: 8
          },
          end: {
            line: 271,
            column: 19
          }
        }, {
          start: {
            line: 271,
            column: 23
          },
          end: {
            line: 271,
            column: 33
          }
        }, {
          start: {
            line: 271,
            column: 37
          },
          end: {
            line: 271,
            column: 51
          }
        }],
        line: 271
      },
      "6": {
        loc: {
          start: {
            line: 279,
            column: 4
          },
          end: {
            line: 279,
            column: 40
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 4
          },
          end: {
            line: 279,
            column: 40
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "7": {
        loc: {
          start: {
            line: 280,
            column: 4
          },
          end: {
            line: 280,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 280,
            column: 4
          },
          end: {
            line: 280,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 280
      },
      "8": {
        loc: {
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 281,
            column: 45
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 281,
            column: 4
          },
          end: {
            line: 281,
            column: 45
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 281
      },
      "9": {
        loc: {
          start: {
            line: 282,
            column: 4
          },
          end: {
            line: 282,
            column: 58
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 4
          },
          end: {
            line: 282,
            column: 58
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "10": {
        loc: {
          start: {
            line: 353,
            column: 4
          },
          end: {
            line: 353,
            column: 38
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 353,
            column: 4
          },
          end: {
            line: 353,
            column: 38
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 353
      },
      "11": {
        loc: {
          start: {
            line: 362,
            column: 4
          },
          end: {
            line: 364,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 362,
            column: 4
          },
          end: {
            line: 364,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 362
      },
      "12": {
        loc: {
          start: {
            line: 366,
            column: 4
          },
          end: {
            line: 368,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 366,
            column: 4
          },
          end: {
            line: 368,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 366
      },
      "13": {
        loc: {
          start: {
            line: 370,
            column: 4
          },
          end: {
            line: 372,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 370,
            column: 4
          },
          end: {
            line: 372,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 370
      },
      "14": {
        loc: {
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 376,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 376,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 374
      },
      "15": {
        loc: {
          start: {
            line: 384,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 384,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 384
      },
      "16": {
        loc: {
          start: {
            line: 388,
            column: 4
          },
          end: {
            line: 390,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 388,
            column: 4
          },
          end: {
            line: 390,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 388
      },
      "17": {
        loc: {
          start: {
            line: 392,
            column: 4
          },
          end: {
            line: 394,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 392,
            column: 4
          },
          end: {
            line: 394,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 392
      },
      "18": {
        loc: {
          start: {
            line: 402,
            column: 4
          },
          end: {
            line: 404,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 402,
            column: 4
          },
          end: {
            line: 404,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 402
      },
      "19": {
        loc: {
          start: {
            line: 406,
            column: 4
          },
          end: {
            line: 416,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 407,
            column: 6
          },
          end: {
            line: 409,
            column: 14
          }
        }, {
          start: {
            line: 410,
            column: 6
          },
          end: {
            line: 412,
            column: 14
          }
        }, {
          start: {
            line: 413,
            column: 6
          },
          end: {
            line: 415,
            column: 14
          }
        }],
        line: 406
      },
      "20": {
        loc: {
          start: {
            line: 428,
            column: 4
          },
          end: {
            line: 430,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 428,
            column: 4
          },
          end: {
            line: 430,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 428
      },
      "21": {
        loc: {
          start: {
            line: 428,
            column: 8
          },
          end: {
            line: 428,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 428,
            column: 8
          },
          end: {
            line: 428,
            column: 18
          }
        }, {
          start: {
            line: 428,
            column: 22
          },
          end: {
            line: 428,
            column: 35
          }
        }, {
          start: {
            line: 428,
            column: 39
          },
          end: {
            line: 428,
            column: 75
          }
        }],
        line: 428
      },
      "22": {
        loc: {
          start: {
            line: 458,
            column: 6
          },
          end: {
            line: 466,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 458,
            column: 6
          },
          end: {
            line: 466,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 458
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "4": [0, 0],
      "5": [0, 0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0],
      "20": [0, 0],
      "21": [0, 0, 0],
      "22": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8540b58fd2df55e8aaec684af943e42f26a403ef"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2iu4jdw5go = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2iu4jdw5go();
var PoseDetectionService = function () {
  function PoseDetectionService() {
    _classCallCheck(this, PoseDetectionService);
    this.modelLoaded = (cov_2iu4jdw5go().s[0]++, false);
    this.landmarkNames = (cov_2iu4jdw5go().s[1]++, ['nose', 'left_eye_inner', 'left_eye', 'left_eye_outer', 'right_eye_inner', 'right_eye', 'right_eye_outer', 'left_ear', 'right_ear', 'mouth_left', 'mouth_right', 'left_shoulder', 'right_shoulder', 'left_elbow', 'right_elbow', 'left_wrist', 'right_wrist', 'left_pinky', 'right_pinky', 'left_index', 'right_index', 'left_thumb', 'right_thumb', 'left_hip', 'right_hip', 'left_knee', 'right_knee', 'left_ankle', 'right_ankle', 'left_heel', 'right_heel', 'left_foot_index', 'right_foot_index']);
  }
  return _createClass(PoseDetectionService, [{
    key: "initializeModel",
    value: (function () {
      var _initializeModel = _asyncToGenerator(function* () {
        cov_2iu4jdw5go().f[0]++;
        cov_2iu4jdw5go().s[2]++;
        try {
          cov_2iu4jdw5go().s[3]++;
          console.log('Initializing pose detection model...');
          cov_2iu4jdw5go().s[4]++;
          yield new Promise(function (resolve) {
            cov_2iu4jdw5go().f[1]++;
            cov_2iu4jdw5go().s[5]++;
            return setTimeout(resolve, 2000);
          });
          cov_2iu4jdw5go().s[6]++;
          this.modelLoaded = true;
          cov_2iu4jdw5go().s[7]++;
          console.log('Pose detection model loaded successfully');
          cov_2iu4jdw5go().s[8]++;
          return true;
        } catch (error) {
          cov_2iu4jdw5go().s[9]++;
          console.error('Failed to load pose detection model:', error);
          cov_2iu4jdw5go().s[10]++;
          return false;
        }
      });
      function initializeModel() {
        return _initializeModel.apply(this, arguments);
      }
      return initializeModel;
    }())
  }, {
    key: "detectPoses",
    value: (function () {
      var _detectPoses = _asyncToGenerator(function* (videoFrames) {
        cov_2iu4jdw5go().f[2]++;
        cov_2iu4jdw5go().s[11]++;
        if (!this.modelLoaded) {
          cov_2iu4jdw5go().b[0][0]++;
          cov_2iu4jdw5go().s[12]++;
          yield this.initializeModel();
        } else {
          cov_2iu4jdw5go().b[0][1]++;
        }
        cov_2iu4jdw5go().s[13]++;
        try {
          var poses = (cov_2iu4jdw5go().s[14]++, []);
          cov_2iu4jdw5go().s[15]++;
          for (var i = (cov_2iu4jdw5go().s[16]++, 0); i < videoFrames.length; i++) {
            var frame = (cov_2iu4jdw5go().s[17]++, videoFrames[i]);
            var pose = (cov_2iu4jdw5go().s[18]++, yield this.detectPoseInFrame(frame, i * 33.33));
            cov_2iu4jdw5go().s[19]++;
            if (pose) {
              cov_2iu4jdw5go().b[1][0]++;
              cov_2iu4jdw5go().s[20]++;
              poses.push(pose);
            } else {
              cov_2iu4jdw5go().b[1][1]++;
            }
          }
          cov_2iu4jdw5go().s[21]++;
          return poses;
        } catch (error) {
          cov_2iu4jdw5go().s[22]++;
          console.error('Pose detection error:', error);
          cov_2iu4jdw5go().s[23]++;
          return this.generateMockPoses(videoFrames.length);
        }
      });
      function detectPoses(_x) {
        return _detectPoses.apply(this, arguments);
      }
      return detectPoses;
    }())
  }, {
    key: "analyzeTechnique",
    value: function analyzeTechnique(poses) {
      cov_2iu4jdw5go().f[3]++;
      cov_2iu4jdw5go().s[24]++;
      try {
        var biomechanics = (cov_2iu4jdw5go().s[25]++, this.analyzeBiomechanics(poses));
        var scores = (cov_2iu4jdw5go().s[26]++, this.calculateTechniqueScores(poses, biomechanics));
        var insights = (cov_2iu4jdw5go().s[27]++, this.generateInsights(poses, biomechanics, scores));
        var recommendations = (cov_2iu4jdw5go().s[28]++, this.generateRecommendations(scores, insights));
        cov_2iu4jdw5go().s[29]++;
        return {
          biomechanics: biomechanics,
          scores: scores,
          insights: insights,
          recommendations: recommendations
        };
      } catch (error) {
        cov_2iu4jdw5go().s[30]++;
        console.error('Technique analysis error:', error);
        cov_2iu4jdw5go().s[31]++;
        return this.getFallbackAnalysis();
      }
    }
  }, {
    key: "analyzeLivePose",
    value: (function () {
      var _analyzeLivePose = _asyncToGenerator(function* (imageData) {
        cov_2iu4jdw5go().f[4]++;
        cov_2iu4jdw5go().s[32]++;
        try {
          var pose = (cov_2iu4jdw5go().s[33]++, yield this.detectPoseInFrame(imageData, Date.now()));
          cov_2iu4jdw5go().s[34]++;
          if (!pose) {
            cov_2iu4jdw5go().b[2][0]++;
            cov_2iu4jdw5go().s[35]++;
            return {
              pose: null,
              feedback: ['Unable to detect pose clearly'],
              corrections: ['Ensure good lighting and clear view']
            };
          } else {
            cov_2iu4jdw5go().b[2][1]++;
          }
          var feedback = (cov_2iu4jdw5go().s[36]++, this.generateLiveFeedback(pose));
          var corrections = (cov_2iu4jdw5go().s[37]++, this.generateLiveCorrections(pose));
          cov_2iu4jdw5go().s[38]++;
          return {
            pose: pose,
            feedback: feedback,
            corrections: corrections
          };
        } catch (error) {
          cov_2iu4jdw5go().s[39]++;
          console.error('Live pose analysis error:', error);
          cov_2iu4jdw5go().s[40]++;
          return {
            pose: null,
            feedback: ['Analysis temporarily unavailable'],
            corrections: ['Continue practicing']
          };
        }
      });
      function analyzeLivePose(_x2) {
        return _analyzeLivePose.apply(this, arguments);
      }
      return analyzeLivePose;
    }())
  }, {
    key: "compareWithIdeal",
    value: function compareWithIdeal(userPose, shotType) {
      cov_2iu4jdw5go().f[5]++;
      var idealPose = (cov_2iu4jdw5go().s[41]++, this.getIdealPose(shotType));
      var similarity = (cov_2iu4jdw5go().s[42]++, this.calculatePoseSimilarity(userPose, idealPose));
      var differences = (cov_2iu4jdw5go().s[43]++, this.identifyDifferences(userPose, idealPose));
      var improvements = (cov_2iu4jdw5go().s[44]++, this.suggestImprovements(differences));
      cov_2iu4jdw5go().s[45]++;
      return {
        similarity: similarity,
        differences: differences,
        improvements: improvements
      };
    }
  }, {
    key: "detectPoseInFrame",
    value: function () {
      var _detectPoseInFrame = _asyncToGenerator(function* (imageData, timestamp) {
        cov_2iu4jdw5go().f[6]++;
        cov_2iu4jdw5go().s[46]++;
        yield new Promise(function (resolve) {
          cov_2iu4jdw5go().f[7]++;
          cov_2iu4jdw5go().s[47]++;
          return setTimeout(resolve, 10);
        });
        var landmarks = (cov_2iu4jdw5go().s[48]++, this.generateRealisticLandmarks());
        var shotType = (cov_2iu4jdw5go().s[49]++, this.detectShotType(landmarks));
        var shotPhase = (cov_2iu4jdw5go().s[50]++, this.detectShotPhase(landmarks, timestamp));
        cov_2iu4jdw5go().s[51]++;
        return {
          landmarks: landmarks,
          timestamp: timestamp,
          confidence: 0.85 + Math.random() * 0.1,
          shotPhase: shotPhase,
          shotType: shotType
        };
      });
      function detectPoseInFrame(_x3, _x4) {
        return _detectPoseInFrame.apply(this, arguments);
      }
      return detectPoseInFrame;
    }()
  }, {
    key: "generateRealisticLandmarks",
    value: function generateRealisticLandmarks() {
      cov_2iu4jdw5go().f[8]++;
      cov_2iu4jdw5go().s[52]++;
      return this.landmarkNames.map(function (name, index) {
        cov_2iu4jdw5go().f[9]++;
        var x, y, z;
        cov_2iu4jdw5go().s[53]++;
        switch (name) {
          case 'nose':
            cov_2iu4jdw5go().b[3][0]++;
            cov_2iu4jdw5go().s[54]++;
            x = 0.5;
            cov_2iu4jdw5go().s[55]++;
            y = 0.2;
            cov_2iu4jdw5go().s[56]++;
            z = 0;
            cov_2iu4jdw5go().s[57]++;
            break;
          case 'left_shoulder':
            cov_2iu4jdw5go().b[3][1]++;
            cov_2iu4jdw5go().s[58]++;
            x = 0.4;
            cov_2iu4jdw5go().s[59]++;
            y = 0.35;
            cov_2iu4jdw5go().s[60]++;
            z = -0.1;
            cov_2iu4jdw5go().s[61]++;
            break;
          case 'right_shoulder':
            cov_2iu4jdw5go().b[3][2]++;
            cov_2iu4jdw5go().s[62]++;
            x = 0.6;
            cov_2iu4jdw5go().s[63]++;
            y = 0.35;
            cov_2iu4jdw5go().s[64]++;
            z = -0.1;
            cov_2iu4jdw5go().s[65]++;
            break;
          case 'left_wrist':
            cov_2iu4jdw5go().b[3][3]++;
            cov_2iu4jdw5go().s[66]++;
            x = 0.3 + Math.random() * 0.2;
            cov_2iu4jdw5go().s[67]++;
            y = 0.5 + Math.random() * 0.2;
            cov_2iu4jdw5go().s[68]++;
            z = -0.2;
            cov_2iu4jdw5go().s[69]++;
            break;
          case 'right_wrist':
            cov_2iu4jdw5go().b[3][4]++;
            cov_2iu4jdw5go().s[70]++;
            x = 0.7 + Math.random() * 0.2;
            cov_2iu4jdw5go().s[71]++;
            y = 0.5 + Math.random() * 0.2;
            cov_2iu4jdw5go().s[72]++;
            z = -0.2;
            cov_2iu4jdw5go().s[73]++;
            break;
          case 'left_hip':
            cov_2iu4jdw5go().b[3][5]++;
            cov_2iu4jdw5go().s[74]++;
            x = 0.45;
            cov_2iu4jdw5go().s[75]++;
            y = 0.6;
            cov_2iu4jdw5go().s[76]++;
            z = 0;
            cov_2iu4jdw5go().s[77]++;
            break;
          case 'right_hip':
            cov_2iu4jdw5go().b[3][6]++;
            cov_2iu4jdw5go().s[78]++;
            x = 0.55;
            cov_2iu4jdw5go().s[79]++;
            y = 0.6;
            cov_2iu4jdw5go().s[80]++;
            z = 0;
            cov_2iu4jdw5go().s[81]++;
            break;
          case 'left_knee':
            cov_2iu4jdw5go().b[3][7]++;
            cov_2iu4jdw5go().s[82]++;
            x = 0.43;
            cov_2iu4jdw5go().s[83]++;
            y = 0.8;
            cov_2iu4jdw5go().s[84]++;
            z = 0.1;
            cov_2iu4jdw5go().s[85]++;
            break;
          case 'right_knee':
            cov_2iu4jdw5go().b[3][8]++;
            cov_2iu4jdw5go().s[86]++;
            x = 0.57;
            cov_2iu4jdw5go().s[87]++;
            y = 0.8;
            cov_2iu4jdw5go().s[88]++;
            z = 0.1;
            cov_2iu4jdw5go().s[89]++;
            break;
          default:
            cov_2iu4jdw5go().b[3][9]++;
            cov_2iu4jdw5go().s[90]++;
            x = 0.5 + (Math.random() - 0.5) * 0.3;
            cov_2iu4jdw5go().s[91]++;
            y = 0.5 + (Math.random() - 0.5) * 0.6;
            cov_2iu4jdw5go().s[92]++;
            z = (Math.random() - 0.5) * 0.2;
        }
        cov_2iu4jdw5go().s[93]++;
        return {
          x: x,
          y: y,
          z: z,
          visibility: 0.8 + Math.random() * 0.2,
          name: name
        };
      });
    }
  }, {
    key: "detectShotType",
    value: function detectShotType(landmarks) {
      cov_2iu4jdw5go().f[10]++;
      var rightWrist = (cov_2iu4jdw5go().s[94]++, landmarks.find(function (l) {
        cov_2iu4jdw5go().f[11]++;
        cov_2iu4jdw5go().s[95]++;
        return l.name === 'right_wrist';
      }));
      var leftWrist = (cov_2iu4jdw5go().s[96]++, landmarks.find(function (l) {
        cov_2iu4jdw5go().f[12]++;
        cov_2iu4jdw5go().s[97]++;
        return l.name === 'left_wrist';
      }));
      var rightShoulder = (cov_2iu4jdw5go().s[98]++, landmarks.find(function (l) {
        cov_2iu4jdw5go().f[13]++;
        cov_2iu4jdw5go().s[99]++;
        return l.name === 'right_shoulder';
      }));
      cov_2iu4jdw5go().s[100]++;
      if ((cov_2iu4jdw5go().b[5][0]++, !rightWrist) || (cov_2iu4jdw5go().b[5][1]++, !leftWrist) || (cov_2iu4jdw5go().b[5][2]++, !rightShoulder)) {
        cov_2iu4jdw5go().b[4][0]++;
        cov_2iu4jdw5go().s[101]++;
        return 'unknown';
      } else {
        cov_2iu4jdw5go().b[4][1]++;
      }
      var armHeight = (cov_2iu4jdw5go().s[102]++, rightWrist.y);
      var armExtension = (cov_2iu4jdw5go().s[103]++, Math.abs(rightWrist.x - rightShoulder.x));
      cov_2iu4jdw5go().s[104]++;
      if (armHeight < 0.3) {
        cov_2iu4jdw5go().b[6][0]++;
        cov_2iu4jdw5go().s[105]++;
        return 'serve';
      } else {
        cov_2iu4jdw5go().b[6][1]++;
      }
      cov_2iu4jdw5go().s[106]++;
      if (armHeight < 0.4) {
        cov_2iu4jdw5go().b[7][0]++;
        cov_2iu4jdw5go().s[107]++;
        return 'overhead';
      } else {
        cov_2iu4jdw5go().b[7][1]++;
      }
      cov_2iu4jdw5go().s[108]++;
      if (armExtension < 0.15) {
        cov_2iu4jdw5go().b[8][0]++;
        cov_2iu4jdw5go().s[109]++;
        return 'volley';
      } else {
        cov_2iu4jdw5go().b[8][1]++;
      }
      cov_2iu4jdw5go().s[110]++;
      if (rightWrist.x > rightShoulder.x) {
        cov_2iu4jdw5go().b[9][0]++;
        cov_2iu4jdw5go().s[111]++;
        return 'forehand';
      } else {
        cov_2iu4jdw5go().b[9][1]++;
      }
      cov_2iu4jdw5go().s[112]++;
      return 'backhand';
    }
  }, {
    key: "detectShotPhase",
    value: function detectShotPhase(landmarks, timestamp) {
      cov_2iu4jdw5go().f[14]++;
      var phase = (cov_2iu4jdw5go().s[113]++, Math.floor(timestamp / 1000 % 4));
      var phases = (cov_2iu4jdw5go().s[114]++, ['preparation', 'contact', 'follow_through', 'recovery']);
      cov_2iu4jdw5go().s[115]++;
      return phases[phase];
    }
  }, {
    key: "analyzeBiomechanics",
    value: function analyzeBiomechanics(poses) {
      cov_2iu4jdw5go().f[15]++;
      var kineticChain = (cov_2iu4jdw5go().s[116]++, {
        groundForce: 75 + Math.random() * 20,
        legDrive: 70 + Math.random() * 25,
        hipRotation: 80 + Math.random() * 15,
        torsoRotation: 85 + Math.random() * 10,
        shoulderRotation: 78 + Math.random() * 18,
        armExtension: 82 + Math.random() * 15,
        wristSnap: 75 + Math.random() * 20
      });
      var balance = (cov_2iu4jdw5go().s[117]++, {
        centerOfGravity: {
          x: 0.5,
          y: 0.6
        },
        stability: 80 + Math.random() * 15,
        weightTransfer: 75 + Math.random() * 20
      });
      var timing = (cov_2iu4jdw5go().s[118]++, {
        preparationTime: 800 + Math.random() * 200,
        contactTime: 50 + Math.random() * 20,
        followThroughTime: 400 + Math.random() * 100,
        totalTime: 1250 + Math.random() * 300
      });
      cov_2iu4jdw5go().s[119]++;
      return {
        kineticChain: kineticChain,
        balance: balance,
        timing: timing
      };
    }
  }, {
    key: "calculateTechniqueScores",
    value: function calculateTechniqueScores(poses, biomechanics) {
      cov_2iu4jdw5go().f[16]++;
      var preparationPoses = (cov_2iu4jdw5go().s[120]++, poses.filter(function (p) {
        cov_2iu4jdw5go().f[17]++;
        cov_2iu4jdw5go().s[121]++;
        return p.shotPhase === 'preparation';
      }));
      var contactPoses = (cov_2iu4jdw5go().s[122]++, poses.filter(function (p) {
        cov_2iu4jdw5go().f[18]++;
        cov_2iu4jdw5go().s[123]++;
        return p.shotPhase === 'contact';
      }));
      var followThroughPoses = (cov_2iu4jdw5go().s[124]++, poses.filter(function (p) {
        cov_2iu4jdw5go().f[19]++;
        cov_2iu4jdw5go().s[125]++;
        return p.shotPhase === 'follow_through';
      }));
      var preparation = (cov_2iu4jdw5go().s[126]++, this.scorePosePhase(preparationPoses));
      var contact = (cov_2iu4jdw5go().s[127]++, this.scorePosePhase(contactPoses));
      var followThrough = (cov_2iu4jdw5go().s[128]++, this.scorePosePhase(followThroughPoses));
      var footwork = (cov_2iu4jdw5go().s[129]++, biomechanics.balance.stability);
      var bodyPosition = (cov_2iu4jdw5go().s[130]++, (biomechanics.kineticChain.hipRotation + biomechanics.kineticChain.torsoRotation) / 2);
      var racquetPath = (cov_2iu4jdw5go().s[131]++, (biomechanics.kineticChain.armExtension + biomechanics.kineticChain.wristSnap) / 2);
      var overall = (cov_2iu4jdw5go().s[132]++, (preparation + contact + followThrough + footwork + bodyPosition + racquetPath) / 6);
      cov_2iu4jdw5go().s[133]++;
      return {
        overall: Math.round(overall),
        preparation: Math.round(preparation),
        contact: Math.round(contact),
        followThrough: Math.round(followThrough),
        footwork: Math.round(footwork),
        bodyPosition: Math.round(bodyPosition),
        racquetPath: Math.round(racquetPath)
      };
    }
  }, {
    key: "scorePosePhase",
    value: function scorePosePhase(poses) {
      cov_2iu4jdw5go().f[20]++;
      cov_2iu4jdw5go().s[134]++;
      if (poses.length === 0) {
        cov_2iu4jdw5go().b[10][0]++;
        cov_2iu4jdw5go().s[135]++;
        return 70;
      } else {
        cov_2iu4jdw5go().b[10][1]++;
      }
      var avgConfidence = (cov_2iu4jdw5go().s[136]++, poses.reduce(function (sum, pose) {
        cov_2iu4jdw5go().f[21]++;
        cov_2iu4jdw5go().s[137]++;
        return sum + pose.confidence;
      }, 0) / poses.length);
      cov_2iu4jdw5go().s[138]++;
      return avgConfidence * 100 + Math.random() * 10;
    }
  }, {
    key: "generateInsights",
    value: function generateInsights(poses, biomechanics, scores) {
      cov_2iu4jdw5go().f[22]++;
      var insights = (cov_2iu4jdw5go().s[139]++, []);
      cov_2iu4jdw5go().s[140]++;
      if (scores.overall > 85) {
        cov_2iu4jdw5go().b[11][0]++;
        cov_2iu4jdw5go().s[141]++;
        insights.push('Excellent overall technique demonstrated');
      } else {
        cov_2iu4jdw5go().b[11][1]++;
      }
      cov_2iu4jdw5go().s[142]++;
      if (biomechanics.kineticChain.legDrive < 70) {
        cov_2iu4jdw5go().b[12][0]++;
        cov_2iu4jdw5go().s[143]++;
        insights.push('Limited leg drive - focus on using lower body power');
      } else {
        cov_2iu4jdw5go().b[12][1]++;
      }
      cov_2iu4jdw5go().s[144]++;
      if (biomechanics.balance.stability < 75) {
        cov_2iu4jdw5go().b[13][0]++;
        cov_2iu4jdw5go().s[145]++;
        insights.push('Balance could be improved for more consistent shots');
      } else {
        cov_2iu4jdw5go().b[13][1]++;
      }
      cov_2iu4jdw5go().s[146]++;
      if (scores.contact > 90) {
        cov_2iu4jdw5go().b[14][0]++;
        cov_2iu4jdw5go().s[147]++;
        insights.push('Outstanding contact point consistency');
      } else {
        cov_2iu4jdw5go().b[14][1]++;
      }
      cov_2iu4jdw5go().s[148]++;
      return insights;
    }
  }, {
    key: "generateRecommendations",
    value: function generateRecommendations(scores, insights) {
      cov_2iu4jdw5go().f[23]++;
      var recommendations = (cov_2iu4jdw5go().s[149]++, []);
      cov_2iu4jdw5go().s[150]++;
      if (scores.preparation < 70) {
        cov_2iu4jdw5go().b[15][0]++;
        cov_2iu4jdw5go().s[151]++;
        recommendations.push('Work on early preparation and racquet take-back');
      } else {
        cov_2iu4jdw5go().b[15][1]++;
      }
      cov_2iu4jdw5go().s[152]++;
      if (scores.footwork < 75) {
        cov_2iu4jdw5go().b[16][0]++;
        cov_2iu4jdw5go().s[153]++;
        recommendations.push('Practice footwork drills for better court positioning');
      } else {
        cov_2iu4jdw5go().b[16][1]++;
      }
      cov_2iu4jdw5go().s[154]++;
      if (scores.followThrough < 80) {
        cov_2iu4jdw5go().b[17][0]++;
        cov_2iu4jdw5go().s[155]++;
        recommendations.push('Focus on complete follow-through for better control');
      } else {
        cov_2iu4jdw5go().b[17][1]++;
      }
      cov_2iu4jdw5go().s[156]++;
      return recommendations;
    }
  }, {
    key: "generateLiveFeedback",
    value: function generateLiveFeedback(pose) {
      cov_2iu4jdw5go().f[24]++;
      var feedback = (cov_2iu4jdw5go().s[157]++, []);
      cov_2iu4jdw5go().s[158]++;
      if (pose.confidence > 0.9) {
        cov_2iu4jdw5go().b[18][0]++;
        cov_2iu4jdw5go().s[159]++;
        feedback.push('Excellent pose detection quality');
      } else {
        cov_2iu4jdw5go().b[18][1]++;
      }
      cov_2iu4jdw5go().s[160]++;
      switch (pose.shotPhase) {
        case 'preparation':
          cov_2iu4jdw5go().b[19][0]++;
          cov_2iu4jdw5go().s[161]++;
          feedback.push('Good preparation phase - maintain this position');
          cov_2iu4jdw5go().s[162]++;
          break;
        case 'contact':
          cov_2iu4jdw5go().b[19][1]++;
          cov_2iu4jdw5go().s[163]++;
          feedback.push('Contact point looks solid');
          cov_2iu4jdw5go().s[164]++;
          break;
        case 'follow_through':
          cov_2iu4jdw5go().b[19][2]++;
          cov_2iu4jdw5go().s[165]++;
          feedback.push('Nice follow-through extension');
          cov_2iu4jdw5go().s[166]++;
          break;
      }
      cov_2iu4jdw5go().s[167]++;
      return feedback;
    }
  }, {
    key: "generateLiveCorrections",
    value: function generateLiveCorrections(pose) {
      cov_2iu4jdw5go().f[25]++;
      var corrections = (cov_2iu4jdw5go().s[168]++, []);
      var rightWrist = (cov_2iu4jdw5go().s[169]++, pose.landmarks.find(function (l) {
        cov_2iu4jdw5go().f[26]++;
        cov_2iu4jdw5go().s[170]++;
        return l.name === 'right_wrist';
      }));
      var rightShoulder = (cov_2iu4jdw5go().s[171]++, pose.landmarks.find(function (l) {
        cov_2iu4jdw5go().f[27]++;
        cov_2iu4jdw5go().s[172]++;
        return l.name === 'right_shoulder';
      }));
      cov_2iu4jdw5go().s[173]++;
      if ((cov_2iu4jdw5go().b[21][0]++, rightWrist) && (cov_2iu4jdw5go().b[21][1]++, rightShoulder) && (cov_2iu4jdw5go().b[21][2]++, rightWrist.y > rightShoulder.y + 0.2)) {
        cov_2iu4jdw5go().b[20][0]++;
        cov_2iu4jdw5go().s[174]++;
        corrections.push('Raise your contact point higher');
      } else {
        cov_2iu4jdw5go().b[20][1]++;
      }
      cov_2iu4jdw5go().s[175]++;
      return corrections;
    }
  }, {
    key: "getIdealPose",
    value: function getIdealPose(shotType) {
      cov_2iu4jdw5go().f[28]++;
      cov_2iu4jdw5go().s[176]++;
      return {
        landmarks: this.generateIdealLandmarks(shotType),
        timestamp: 0,
        confidence: 1.0,
        shotPhase: 'contact',
        shotType: shotType
      };
    }
  }, {
    key: "generateIdealLandmarks",
    value: function generateIdealLandmarks(shotType) {
      cov_2iu4jdw5go().f[29]++;
      cov_2iu4jdw5go().s[177]++;
      return this.generateRealisticLandmarks();
    }
  }, {
    key: "calculatePoseSimilarity",
    value: function calculatePoseSimilarity(userPose, idealPose) {
      cov_2iu4jdw5go().f[30]++;
      var totalDifference = (cov_2iu4jdw5go().s[178]++, 0);
      var comparisons = (cov_2iu4jdw5go().s[179]++, 0);
      cov_2iu4jdw5go().s[180]++;
      userPose.landmarks.forEach(function (userLandmark) {
        cov_2iu4jdw5go().f[31]++;
        var idealLandmark = (cov_2iu4jdw5go().s[181]++, idealPose.landmarks.find(function (l) {
          cov_2iu4jdw5go().f[32]++;
          cov_2iu4jdw5go().s[182]++;
          return l.name === userLandmark.name;
        }));
        cov_2iu4jdw5go().s[183]++;
        if (idealLandmark) {
          cov_2iu4jdw5go().b[22][0]++;
          var distance = (cov_2iu4jdw5go().s[184]++, Math.sqrt(Math.pow(userLandmark.x - idealLandmark.x, 2) + Math.pow(userLandmark.y - idealLandmark.y, 2) + Math.pow(userLandmark.z - idealLandmark.z, 2)));
          cov_2iu4jdw5go().s[185]++;
          totalDifference += distance;
          cov_2iu4jdw5go().s[186]++;
          comparisons++;
        } else {
          cov_2iu4jdw5go().b[22][1]++;
        }
      });
      var avgDifference = (cov_2iu4jdw5go().s[187]++, totalDifference / comparisons);
      cov_2iu4jdw5go().s[188]++;
      return Math.max(0, Math.min(100, (1 - avgDifference) * 100));
    }
  }, {
    key: "identifyDifferences",
    value: function identifyDifferences(userPose, idealPose) {
      cov_2iu4jdw5go().f[33]++;
      cov_2iu4jdw5go().s[189]++;
      return ['Slight difference in racquet angle', 'Contact point could be higher', 'Body rotation timing'];
    }
  }, {
    key: "suggestImprovements",
    value: function suggestImprovements(differences) {
      cov_2iu4jdw5go().f[34]++;
      cov_2iu4jdw5go().s[190]++;
      return differences.map(function (diff) {
        cov_2iu4jdw5go().f[35]++;
        cov_2iu4jdw5go().s[191]++;
        return `Work on: ${diff}`;
      });
    }
  }, {
    key: "generateMockPoses",
    value: function generateMockPoses(frameCount) {
      cov_2iu4jdw5go().f[36]++;
      var poses = (cov_2iu4jdw5go().s[192]++, []);
      cov_2iu4jdw5go().s[193]++;
      for (var i = (cov_2iu4jdw5go().s[194]++, 0); i < frameCount; i++) {
        cov_2iu4jdw5go().s[195]++;
        poses.push({
          landmarks: this.generateRealisticLandmarks(),
          timestamp: i * 33.33,
          confidence: 0.8 + Math.random() * 0.15,
          shotPhase: ['preparation', 'contact', 'follow_through', 'recovery'][i % 4],
          shotType: 'forehand'
        });
      }
      cov_2iu4jdw5go().s[196]++;
      return poses;
    }
  }, {
    key: "getFallbackAnalysis",
    value: function getFallbackAnalysis() {
      cov_2iu4jdw5go().f[37]++;
      cov_2iu4jdw5go().s[197]++;
      return {
        biomechanics: {
          kineticChain: {
            groundForce: 75,
            legDrive: 70,
            hipRotation: 80,
            torsoRotation: 85,
            shoulderRotation: 78,
            armExtension: 82,
            wristSnap: 75
          },
          balance: {
            centerOfGravity: {
              x: 0.5,
              y: 0.6
            },
            stability: 80,
            weightTransfer: 75
          },
          timing: {
            preparationTime: 800,
            contactTime: 50,
            followThroughTime: 400,
            totalTime: 1250
          }
        },
        scores: {
          overall: 75,
          preparation: 70,
          contact: 80,
          followThrough: 75,
          footwork: 70,
          bodyPosition: 78,
          racquetPath: 80
        },
        insights: ['Good overall form', 'Continue working on consistency'],
        recommendations: ['Practice regularly', 'Focus on fundamentals']
      };
    }
  }]);
}();
export var poseDetectionService = (cov_2iu4jdw5go().s[198]++, new PoseDetectionService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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