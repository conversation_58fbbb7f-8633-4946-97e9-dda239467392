ec9c9820e4a49ca1dd566b50eb0a9873
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.addEventListener = addEventListener;
var _canUseDom = _interopRequireDefault(require("../canUseDom"));
var emptyFunction = function emptyFunction() {};
function supportsPassiveEvents() {
  var supported = false;
  if (_canUseDom.default) {
    try {
      var options = {};
      Object.defineProperty(options, 'passive', {
        get: function get() {
          supported = true;
          return false;
        }
      });
      window.addEventListener('test', null, options);
      window.removeEventListener('test', null, options);
    } catch (e) {}
  }
  return supported;
}
var canUsePassiveEvents = supportsPassiveEvents();
function getOptions(options) {
  if (options == null) {
    return false;
  }
  return canUsePassiveEvents ? options : <PERSON><PERSON><PERSON>(options.capture);
}
function isPropagationStopped() {
  return this.cancelBubble;
}
function isDefaultPrevented() {
  return this.defaultPrevented;
}
function normalizeEvent(event) {
  event.nativeEvent = event;
  event.persist = emptyFunction;
  event.isDefaultPrevented = isDefaultPrevented;
  event.isPropagationStopped = isPropagationStopped;
  return event;
}
function addEventListener(target, type, listener, options) {
  var opts = getOptions(options);
  var compatListener = function compatListener(e) {
    return listener(normalizeEvent(e));
  };
  target.addEventListener(type, compatListener, opts);
  return function removeEventListener() {
    if (target != null) {
      target.removeEventListener(type, compatListener, opts);
    }
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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