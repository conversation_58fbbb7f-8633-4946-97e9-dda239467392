{"version": 3, "names": ["exports", "__esModule", "default", "vibrate", "pattern", "window", "navigator", "Vibration", "cancel", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar vibrate = pattern => {\n  if ('vibrate' in window.navigator) {\n    window.navigator.vibrate(pattern);\n  }\n};\nvar Vibration = {\n  cancel() {\n    vibrate(0);\n  },\n  vibrate(pattern) {\n    if (pattern === void 0) {\n      pattern = 400;\n    }\n    vibrate(pattern);\n  }\n};\nvar _default = exports.default = Vibration;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAWxB,IAAIC,QAAO,GAAG,SAAVA,OAAOA,CAAGC,OAAO,EAAI;EACvB,IAAI,SAAS,IAAIC,MAAM,CAACC,SAAS,EAAE;IACjCD,MAAM,CAACC,SAAS,CAACH,OAAO,CAACC,OAAO,CAAC;EACnC;AACF,CAAC;AACD,IAAIG,SAAS,GAAG;EACdC,MAAM,WAANA,MAAMA,CAAA,EAAG;IACPL,QAAO,CAAC,CAAC,CAAC;EACZ,CAAC;EACDA,OAAO,WAAPA,OAAOA,CAACC,OAAO,EAAE;IACf,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MACtBA,OAAO,GAAG,GAAG;IACf;IACAD,QAAO,CAACC,OAAO,CAAC;EAClB;AACF,CAAC;AACD,IAAIK,QAAQ,GAAGT,OAAO,CAACE,OAAO,GAAGK,SAAS;AAC1CG,MAAM,CAACV,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}