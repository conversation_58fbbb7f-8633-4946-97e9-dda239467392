{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "_View", "_react", "_excluded", "UnimplementedView", "_ref", "style", "props", "createElement", "unimplementedViewStyles", "process", "env", "NODE_ENV", "alignSelf", "borderColor", "borderWidth", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _View = _interopRequireDefault(require(\"../../exports/View\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _excluded = [\"style\"];\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n/**\n * Common implementation for a simple stubbed view.\n */\nfunction UnimplementedView(_ref) {\n  var style = _ref.style,\n    props = (0, _objectWithoutPropertiesLoose2.default)(_ref, _excluded);\n  return /*#__PURE__*/_react.default.createElement(_View.default, (0, _extends2.default)({}, props, {\n    style: [unimplementedViewStyles, style]\n  }));\n}\nvar unimplementedViewStyles = process.env.NODE_ENV !== 'production' ? {\n  alignSelf: 'flex-start',\n  borderColor: 'red',\n  borderWidth: 1\n} : {};\nvar _default = exports.default = UnimplementedView;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,SAAS,GAAGL,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIK,8BAA8B,GAAGN,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIM,KAAK,GAAGP,sBAAsB,CAACC,OAAO,qBAAqB,CAAC,CAAC;AACjE,IAAIO,MAAM,GAAGR,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,SAAS,GAAG,CAAC,OAAO,CAAC;AAYzB,SAASC,iBAAiBA,CAACC,IAAI,EAAE;EAC/B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACpBC,KAAK,GAAG,CAAC,CAAC,EAAEP,8BAA8B,CAACJ,OAAO,EAAES,IAAI,EAAEF,SAAS,CAAC;EACtE,OAAoBD,MAAM,CAACN,OAAO,CAACY,aAAa,CAACP,KAAK,CAACL,OAAO,EAAE,CAAC,CAAC,EAAEG,SAAS,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEW,KAAK,EAAE;IAChGD,KAAK,EAAE,CAACG,uBAAuB,EAAEH,KAAK;EACxC,CAAC,CAAC,CAAC;AACL;AACA,IAAIG,uBAAuB,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EACpEC,SAAS,EAAE,YAAY;EACvBC,WAAW,EAAE,KAAK;EAClBC,WAAW,EAAE;AACf,CAAC,GAAG,CAAC,CAAC;AACN,IAAIC,QAAQ,GAAGnB,OAAO,CAACD,OAAO,GAAGQ,iBAAiB;AAClDa,MAAM,CAACpB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}