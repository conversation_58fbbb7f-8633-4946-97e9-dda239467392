/**
 * Sentry Error Monitoring Service
 * Replaces LogRocket/OpenReplay with Sentry for error tracking and performance monitoring
 */

import * as Sentry from '@sentry/react-native';
import { Platform } from 'react-native';

export interface ErrorContext {
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
  tags?: Record<string, string>;
  extra?: Record<string, any>;
  level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug';
  fingerprint?: string[];
}

export interface PerformanceContext {
  operation: string;
  description?: string;
  tags?: Record<string, string>;
  data?: Record<string, any>;
}

class SentryService {
  private isInitialized = false;
  private dsn: string;

  constructor() {
    this.dsn = process.env.SENTRY_DSN || '';
  }

  /**
   * Initialize Sentry error monitoring
   */
  async initialize(): Promise<void> {
    try {
      if (!this.dsn) {
        console.warn('Sentry DSN not configured');
        return;
      }

      Sentry.init({
        dsn: this.dsn,
        debug: __DEV__,
        environment: process.env.EXPO_PUBLIC_APP_ENV || 'development',
        release: process.env.EXPO_PUBLIC_APP_VERSION || '1.0.0',
        
        // Performance monitoring
        tracesSampleRate: __DEV__ ? 1.0 : 0.1,
        enableAutoSessionTracking: true,
        
        // Error filtering
        beforeSend: (event, hint) => {
          // Filter out development errors in production
          if (!__DEV__ && event.environment === 'development') {
            return null;
          }
          
          // Filter out network errors that are expected
          if (event.exception?.values?.[0]?.type === 'NetworkError') {
            return null;
          }
          
          return event;
        },
        
        // Integrations
        integrations: [
          new Sentry.ReactNativeTracing({
            routingInstrumentation: new Sentry.ReactNavigationInstrumentation(),
            enableNativeFramesTracking: !__DEV__,
          }),
        ],
      });

      this.isInitialized = true;
      console.log('Sentry error monitoring initialized');
    } catch (error) {
      console.error('Failed to initialize Sentry:', error);
    }
  }

  /**
   * Set user context
   */
  setUser(user: {
    id?: string;
    email?: string;
    username?: string;
    tennisLevel?: string;
    subscriptionTier?: string;
  }): void {
    if (!this.isInitialized) return;

    try {
      Sentry.setUser({
        id: user.id,
        email: user.email,
        username: user.username,
        tennis_level: user.tennisLevel,
        subscription_tier: user.subscriptionTier,
      });
      
      console.log('Sentry user context set:', user.id);
    } catch (error) {
      console.error('Failed to set Sentry user context:', error);
    }
  }

  /**
   * Set tags for error context
   */
  setTags(tags: Record<string, string>): void {
    if (!this.isInitialized) return;

    try {
      Sentry.setTags(tags);
      console.log('Sentry tags set:', tags);
    } catch (error) {
      console.error('Failed to set Sentry tags:', error);
    }
  }

  /**
   * Set extra context data
   */
  setExtra(key: string, value: any): void {
    if (!this.isInitialized) return;

    try {
      Sentry.setExtra(key, value);
    } catch (error) {
      console.error('Failed to set Sentry extra data:', error);
    }
  }

  /**
   * Capture exception
   */
  captureException(error: Error, context?: ErrorContext): string | undefined {
    if (!this.isInitialized) {
      console.error('Sentry not initialized, logging error:', error);
      return;
    }

    try {
      return Sentry.captureException(error, {
        user: context?.user,
        tags: context?.tags,
        extra: context?.extra,
        level: context?.level || 'error',
        fingerprint: context?.fingerprint,
      });
    } catch (sentryError) {
      console.error('Failed to capture exception in Sentry:', sentryError);
      return;
    }
  }

  /**
   * Capture message
   */
  captureMessage(message: string, level: 'fatal' | 'error' | 'warning' | 'info' | 'debug' = 'info', context?: ErrorContext): string | undefined {
    if (!this.isInitialized) {
      console.log('Sentry not initialized, logging message:', message);
      return;
    }

    try {
      return Sentry.captureMessage(message, level, {
        user: context?.user,
        tags: context?.tags,
        extra: context?.extra,
        fingerprint: context?.fingerprint,
      });
    } catch (error) {
      console.error('Failed to capture message in Sentry:', error);
      return;
    }
  }

  /**
   * Add breadcrumb
   */
  addBreadcrumb(breadcrumb: {
    message: string;
    category?: string;
    level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug';
    data?: Record<string, any>;
  }): void {
    if (!this.isInitialized) return;

    try {
      Sentry.addBreadcrumb({
        message: breadcrumb.message,
        category: breadcrumb.category || 'custom',
        level: breadcrumb.level || 'info',
        data: breadcrumb.data,
        timestamp: Date.now() / 1000,
      });
    } catch (error) {
      console.error('Failed to add breadcrumb:', error);
    }
  }

  /**
   * Start performance transaction
   */
  startTransaction(context: PerformanceContext): any {
    if (!this.isInitialized) return null;

    try {
      const transaction = Sentry.startTransaction({
        name: context.operation,
        op: context.operation,
        description: context.description,
        tags: context.tags,
        data: context.data,
      });
      
      return transaction;
    } catch (error) {
      console.error('Failed to start Sentry transaction:', error);
      return null;
    }
  }

  /**
   * Finish performance transaction
   */
  finishTransaction(transaction: any): void {
    if (!transaction) return;

    try {
      transaction.finish();
    } catch (error) {
      console.error('Failed to finish Sentry transaction:', error);
    }
  }

  /**
   * Measure performance of async operation
   */
  async measurePerformance<T>(
    operation: string,
    fn: () => Promise<T>,
    context?: PerformanceContext
  ): Promise<T> {
    const transaction = this.startTransaction({
      operation,
      ...context,
    });

    try {
      const result = await fn();
      this.finishTransaction(transaction);
      return result;
    } catch (error) {
      this.captureException(error as Error, {
        tags: { operation },
        extra: context?.data,
      });
      this.finishTransaction(transaction);
      throw error;
    }
  }

  // Tennis app specific error tracking methods

  /**
   * Track video analysis error
   */
  trackVideoAnalysisError(error: Error, videoId: string, analysisType: string): void {
    this.captureException(error, {
      tags: {
        error_type: 'video_analysis',
        analysis_type: analysisType,
      },
      extra: {
        video_id: videoId,
        platform: Platform.OS,
      },
    });
  }

  /**
   * Track authentication error
   */
  trackAuthError(error: Error, authMethod: string): void {
    this.captureException(error, {
      tags: {
        error_type: 'authentication',
        auth_method: authMethod,
      },
      extra: {
        platform: Platform.OS,
      },
    });
  }

  /**
   * Track payment error
   */
  trackPaymentError(error: Error, paymentMethod: string, amount?: number): void {
    this.captureException(error, {
      tags: {
        error_type: 'payment',
        payment_method: paymentMethod,
      },
      extra: {
        amount,
        platform: Platform.OS,
      },
    });
  }

  /**
   * Track API error
   */
  trackAPIError(error: Error, endpoint: string, method: string, statusCode?: number): void {
    this.captureException(error, {
      tags: {
        error_type: 'api',
        endpoint,
        method,
        status_code: statusCode?.toString(),
      },
      extra: {
        platform: Platform.OS,
      },
    });
  }

  /**
   * Track MediaPipe error
   */
  trackMediaPipeError(error: Error, operation: string): void {
    this.captureException(error, {
      tags: {
        error_type: 'mediapipe',
        operation,
      },
      extra: {
        platform: Platform.OS,
      },
    });
  }

  /**
   * Clear user context (on logout)
   */
  clearUser(): void {
    if (!this.isInitialized) return;

    try {
      Sentry.setUser(null);
      console.log('Sentry user context cleared');
    } catch (error) {
      console.error('Failed to clear Sentry user context:', error);
    }
  }

  /**
   * Check if service is ready
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Get current hub
   */
  getCurrentHub(): any {
    return Sentry.getCurrentHub();
  }
}

// Export singleton instance
export const sentryService = new SentryService();
