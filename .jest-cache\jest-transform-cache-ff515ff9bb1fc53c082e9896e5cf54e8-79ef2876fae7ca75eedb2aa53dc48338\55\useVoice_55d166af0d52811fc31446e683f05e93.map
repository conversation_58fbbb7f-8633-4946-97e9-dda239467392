{"version": 3, "names": ["useState", "useCallback", "useEffect", "voiceService", "useVoice", "cov_122bcgj3d0", "f", "_ref", "s", "_ref2", "_slicedToArray", "isListening", "setIsListening", "_ref3", "_ref4", "isSpeaking", "setIsSpeaking", "_ref5", "_ref6", "isInitialized", "setIsInitialized", "_ref7", "_ref8", "lastTranscript", "setLastTranscript", "_ref9", "_ref0", "lastCommand", "setLastCommand", "_ref1", "_ref10", "confidence", "setConfidence", "_ref11", "_ref12", "error", "setError", "initializeVoice", "_asyncToGenerator", "initialized", "initializeVoiceRecognition", "b", "err", "errorMessage", "Error", "message", "startListening", "result", "transcript", "isFinal", "command", "processVoiceCommand", "console", "log", "stopListening", "speak", "_ref16", "text", "options", "arguments", "length", "undefined", "_x", "apply", "stopSpeaking", "speakCoachingFeedback", "_ref18", "type", "customText", "_x2", "_x3", "processCommand", "getAvailableCommands", "interval", "setInterval", "isCurrentlyListening", "isCurrentlySpeaking", "clearInterval"], "sources": ["useVoice.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { voiceService, VoiceRecognitionResult, VoiceCommand, SpeechOptions } from '@/services/voiceService';\n\ninterface UseVoiceReturn {\n  // Voice recognition state\n  isListening: boolean;\n  isSpeaking: boolean;\n  isInitialized: boolean;\n  lastTranscript: string;\n  lastCommand: VoiceCommand | null;\n  confidence: number;\n  \n  // Voice recognition controls\n  initializeVoice: () => Promise<boolean>;\n  startListening: () => Promise<void>;\n  stopListening: () => Promise<void>;\n  \n  // Text-to-speech controls\n  speak: (text: string, options?: SpeechOptions) => Promise<void>;\n  stopSpeaking: () => Promise<void>;\n  speakCoachingFeedback: (type: 'encouragement' | 'correction' | 'tip' | 'instruction', customText?: string) => Promise<void>;\n  \n  // Command processing\n  processCommand: (transcript: string) => VoiceCommand | null;\n  getAvailableCommands: () => string[];\n  \n  // Error handling\n  error: string | null;\n}\n\nexport function useVoice(): UseVoiceReturn {\n  const [isListening, setIsListening] = useState(false);\n  const [isSpeaking, setIsSpeaking] = useState(false);\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [lastTranscript, setLastTranscript] = useState('');\n  const [lastCommand, setLastCommand] = useState<VoiceCommand | null>(null);\n  const [confidence, setConfidence] = useState(0);\n  const [error, setError] = useState<string | null>(null);\n\n  /**\n   * Initialize voice recognition\n   */\n  const initializeVoice = useCallback(async (): Promise<boolean> => {\n    try {\n      setError(null);\n      const initialized = await voiceService.initializeVoiceRecognition();\n      setIsInitialized(initialized);\n      \n      if (!initialized) {\n        setError('Voice recognition not available on this device');\n      }\n      \n      return initialized;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize voice recognition';\n      setError(errorMessage);\n      setIsInitialized(false);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Start listening for voice input\n   */\n  const startListening = useCallback(async (): Promise<void> => {\n    if (!isInitialized) {\n      throw new Error('Voice recognition not initialized');\n    }\n\n    try {\n      setError(null);\n      \n      await voiceService.startListening((result: VoiceRecognitionResult) => {\n        setLastTranscript(result.transcript);\n        setConfidence(result.confidence);\n        \n        if (result.isFinal) {\n          // Process the command\n          const command = voiceService.processVoiceCommand(result.transcript);\n          setLastCommand(command);\n          \n          if (command) {\n            console.log('Voice command recognized:', command);\n          }\n        }\n      });\n      \n      setIsListening(true);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to start listening';\n      setError(errorMessage);\n      throw err;\n    }\n  }, [isInitialized]);\n\n  /**\n   * Stop listening for voice input\n   */\n  const stopListening = useCallback(async (): Promise<void> => {\n    try {\n      setError(null);\n      await voiceService.stopListening();\n      setIsListening(false);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to stop listening';\n      setError(errorMessage);\n      throw err;\n    }\n  }, []);\n\n  /**\n   * Speak text using text-to-speech\n   */\n  const speak = useCallback(async (text: string, options: SpeechOptions = {}): Promise<void> => {\n    try {\n      setError(null);\n      setIsSpeaking(true);\n      \n      await voiceService.speak(text, options);\n      setIsSpeaking(false);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to speak text';\n      setError(errorMessage);\n      setIsSpeaking(false);\n      throw err;\n    }\n  }, []);\n\n  /**\n   * Stop current speech\n   */\n  const stopSpeaking = useCallback(async (): Promise<void> => {\n    try {\n      setError(null);\n      await voiceService.stopSpeaking();\n      setIsSpeaking(false);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to stop speaking';\n      setError(errorMessage);\n    }\n  }, []);\n\n  /**\n   * Speak coaching feedback\n   */\n  const speakCoachingFeedback = useCallback(async (\n    type: 'encouragement' | 'correction' | 'tip' | 'instruction',\n    customText?: string\n  ): Promise<void> => {\n    try {\n      setError(null);\n      setIsSpeaking(true);\n      \n      await voiceService.speakCoachingFeedback(type, customText);\n      setIsSpeaking(false);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to speak coaching feedback';\n      setError(errorMessage);\n      setIsSpeaking(false);\n      throw err;\n    }\n  }, []);\n\n  /**\n   * Process voice command\n   */\n  const processCommand = useCallback((transcript: string): VoiceCommand | null => {\n    try {\n      setError(null);\n      return voiceService.processVoiceCommand(transcript);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to process command';\n      setError(errorMessage);\n      return null;\n    }\n  }, []);\n\n  /**\n   * Get available voice commands\n   */\n  const getAvailableCommands = useCallback((): string[] => {\n    return voiceService.getAvailableCommands();\n  }, []);\n\n  // Update listening and speaking state based on service state\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setIsListening(voiceService.isCurrentlyListening());\n      setIsSpeaking(voiceService.isCurrentlySpeaking());\n    }, 500);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Initialize voice service on mount\n  useEffect(() => {\n    initializeVoice();\n  }, [initializeVoice]);\n\n  return {\n    // Voice recognition state\n    isListening,\n    isSpeaking,\n    isInitialized,\n    lastTranscript,\n    lastCommand,\n    confidence,\n    \n    // Voice recognition controls\n    initializeVoice,\n    startListening,\n    stopListening,\n    \n    // Text-to-speech controls\n    speak,\n    stopSpeaking,\n    speakCoachingFeedback,\n    \n    // Command processing\n    processCommand,\n    getAvailableCommands,\n    \n    // Error handling\n    error,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACxD,SAASC,YAAY;AA6BrB,OAAO,SAASC,QAAQA,CAAA,EAAmB;EAAAC,cAAA,GAAAC,CAAA;EACzC,IAAAC,IAAA,IAAAF,cAAA,GAAAG,CAAA,OAAsCR,QAAQ,CAAC,KAAK,CAAC;IAAAS,KAAA,GAAAC,cAAA,CAAAH,IAAA;IAA9CI,WAAW,GAAAF,KAAA;IAAEG,cAAc,GAAAH,KAAA;EAClC,IAAAI,KAAA,IAAAR,cAAA,GAAAG,CAAA,OAAoCR,QAAQ,CAAC,KAAK,CAAC;IAAAc,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAA5CE,UAAU,GAAAD,KAAA;IAAEE,aAAa,GAAAF,KAAA;EAChC,IAAAG,KAAA,IAAAZ,cAAA,GAAAG,CAAA,OAA0CR,QAAQ,CAAC,KAAK,CAAC;IAAAkB,KAAA,GAAAR,cAAA,CAAAO,KAAA;IAAlDE,aAAa,GAAAD,KAAA;IAAEE,gBAAgB,GAAAF,KAAA;EACtC,IAAAG,KAAA,IAAAhB,cAAA,GAAAG,CAAA,OAA4CR,QAAQ,CAAC,EAAE,CAAC;IAAAsB,KAAA,GAAAZ,cAAA,CAAAW,KAAA;IAAjDE,cAAc,GAAAD,KAAA;IAAEE,iBAAiB,GAAAF,KAAA;EACxC,IAAAG,KAAA,IAAApB,cAAA,GAAAG,CAAA,OAAsCR,QAAQ,CAAsB,IAAI,CAAC;IAAA0B,KAAA,GAAAhB,cAAA,CAAAe,KAAA;IAAlEE,WAAW,GAAAD,KAAA;IAAEE,cAAc,GAAAF,KAAA;EAClC,IAAAG,KAAA,IAAAxB,cAAA,GAAAG,CAAA,OAAoCR,QAAQ,CAAC,CAAC,CAAC;IAAA8B,MAAA,GAAApB,cAAA,CAAAmB,KAAA;IAAxCE,UAAU,GAAAD,MAAA;IAAEE,aAAa,GAAAF,MAAA;EAChC,IAAAG,MAAA,IAAA5B,cAAA,GAAAG,CAAA,OAA0BR,QAAQ,CAAgB,IAAI,CAAC;IAAAkC,MAAA,GAAAxB,cAAA,CAAAuB,MAAA;IAAhDE,KAAK,GAAAD,MAAA;IAAEE,QAAQ,GAAAF,MAAA;EAKtB,IAAMG,eAAe,IAAAhC,cAAA,GAAAG,CAAA,OAAGP,WAAW,CAAAqC,iBAAA,CAAC,aAA8B;IAAAjC,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAChE,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACF4B,QAAQ,CAAC,IAAI,CAAC;MACd,IAAMG,WAAW,IAAAlC,cAAA,GAAAG,CAAA,cAASL,YAAY,CAACqC,0BAA0B,CAAC,CAAC;MAACnC,cAAA,GAAAG,CAAA;MACpEY,gBAAgB,CAACmB,WAAW,CAAC;MAAClC,cAAA,GAAAG,CAAA;MAE9B,IAAI,CAAC+B,WAAW,EAAE;QAAAlC,cAAA,GAAAoC,CAAA;QAAApC,cAAA,GAAAG,CAAA;QAChB4B,QAAQ,CAAC,gDAAgD,CAAC;MAC5D,CAAC;QAAA/B,cAAA,GAAAoC,CAAA;MAAA;MAAApC,cAAA,GAAAG,CAAA;MAED,OAAO+B,WAAW;IACpB,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAtC,cAAA,GAAAG,CAAA,QAAGkC,GAAG,YAAYE,KAAK,IAAAvC,cAAA,GAAAoC,CAAA,UAAGC,GAAG,CAACG,OAAO,KAAAxC,cAAA,GAAAoC,CAAA,UAAG,wCAAwC;MAACpC,cAAA,GAAAG,CAAA;MACnG4B,QAAQ,CAACO,YAAY,CAAC;MAACtC,cAAA,GAAAG,CAAA;MACvBY,gBAAgB,CAAC,KAAK,CAAC;MAACf,cAAA,GAAAG,CAAA;MACxB,OAAO,KAAK;IACd;EACF,CAAC,GAAE,EAAE,CAAC;EAKN,IAAMsC,cAAc,IAAAzC,cAAA,GAAAG,CAAA,QAAGP,WAAW,CAAAqC,iBAAA,CAAC,aAA2B;IAAAjC,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC5D,IAAI,CAACW,aAAa,EAAE;MAAAd,cAAA,GAAAoC,CAAA;MAAApC,cAAA,GAAAG,CAAA;MAClB,MAAM,IAAIoC,KAAK,CAAC,mCAAmC,CAAC;IACtD,CAAC;MAAAvC,cAAA,GAAAoC,CAAA;IAAA;IAAApC,cAAA,GAAAG,CAAA;IAED,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACF4B,QAAQ,CAAC,IAAI,CAAC;MAAC/B,cAAA,GAAAG,CAAA;MAEf,MAAML,YAAY,CAAC2C,cAAc,CAAC,UAACC,MAA8B,EAAK;QAAA1C,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAG,CAAA;QACpEgB,iBAAiB,CAACuB,MAAM,CAACC,UAAU,CAAC;QAAC3C,cAAA,GAAAG,CAAA;QACrCwB,aAAa,CAACe,MAAM,CAAChB,UAAU,CAAC;QAAC1B,cAAA,GAAAG,CAAA;QAEjC,IAAIuC,MAAM,CAACE,OAAO,EAAE;UAAA5C,cAAA,GAAAoC,CAAA;UAElB,IAAMS,OAAO,IAAA7C,cAAA,GAAAG,CAAA,QAAGL,YAAY,CAACgD,mBAAmB,CAACJ,MAAM,CAACC,UAAU,CAAC;UAAC3C,cAAA,GAAAG,CAAA;UACpEoB,cAAc,CAACsB,OAAO,CAAC;UAAC7C,cAAA,GAAAG,CAAA;UAExB,IAAI0C,OAAO,EAAE;YAAA7C,cAAA,GAAAoC,CAAA;YAAApC,cAAA,GAAAG,CAAA;YACX4C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEH,OAAO,CAAC;UACnD,CAAC;YAAA7C,cAAA,GAAAoC,CAAA;UAAA;QACH,CAAC;UAAApC,cAAA,GAAAoC,CAAA;QAAA;MACH,CAAC,CAAC;MAACpC,cAAA,GAAAG,CAAA;MAEHI,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,CAAC,OAAO8B,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAtC,cAAA,GAAAG,CAAA,QAAGkC,GAAG,YAAYE,KAAK,IAAAvC,cAAA,GAAAoC,CAAA,UAAGC,GAAG,CAACG,OAAO,KAAAxC,cAAA,GAAAoC,CAAA,UAAG,2BAA2B;MAACpC,cAAA,GAAAG,CAAA;MACtF4B,QAAQ,CAACO,YAAY,CAAC;MAACtC,cAAA,GAAAG,CAAA;MACvB,MAAMkC,GAAG;IACX;EACF,CAAC,GAAE,CAACvB,aAAa,CAAC,CAAC;EAKnB,IAAMmC,aAAa,IAAAjD,cAAA,GAAAG,CAAA,QAAGP,WAAW,CAAAqC,iBAAA,CAAC,aAA2B;IAAAjC,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC3D,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACF4B,QAAQ,CAAC,IAAI,CAAC;MAAC/B,cAAA,GAAAG,CAAA;MACf,MAAML,YAAY,CAACmD,aAAa,CAAC,CAAC;MAACjD,cAAA,GAAAG,CAAA;MACnCI,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC,OAAO8B,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAtC,cAAA,GAAAG,CAAA,QAAGkC,GAAG,YAAYE,KAAK,IAAAvC,cAAA,GAAAoC,CAAA,UAAGC,GAAG,CAACG,OAAO,KAAAxC,cAAA,GAAAoC,CAAA,UAAG,0BAA0B;MAACpC,cAAA,GAAAG,CAAA;MACrF4B,QAAQ,CAACO,YAAY,CAAC;MAACtC,cAAA,GAAAG,CAAA;MACvB,MAAMkC,GAAG;IACX;EACF,CAAC,GAAE,EAAE,CAAC;EAKN,IAAMa,KAAK,IAAAlD,cAAA,GAAAG,CAAA,QAAGP,WAAW;IAAA,IAAAuD,MAAA,GAAAlB,iBAAA,CAAC,WAAOmB,IAAY,EAAiD;MAAA,IAA/CC,OAAsB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAtD,cAAA,GAAAoC,CAAA,UAAG,CAAC,CAAC;MAAApC,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACxE,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACF4B,QAAQ,CAAC,IAAI,CAAC;QAAC/B,cAAA,GAAAG,CAAA;QACfQ,aAAa,CAAC,IAAI,CAAC;QAACX,cAAA,GAAAG,CAAA;QAEpB,MAAML,YAAY,CAACoD,KAAK,CAACE,IAAI,EAAEC,OAAO,CAAC;QAACrD,cAAA,GAAAG,CAAA;QACxCQ,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,CAAC,OAAO0B,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAtC,cAAA,GAAAG,CAAA,QAAGkC,GAAG,YAAYE,KAAK,IAAAvC,cAAA,GAAAoC,CAAA,UAAGC,GAAG,CAACG,OAAO,KAAAxC,cAAA,GAAAoC,CAAA,UAAG,sBAAsB;QAACpC,cAAA,GAAAG,CAAA;QACjF4B,QAAQ,CAACO,YAAY,CAAC;QAACtC,cAAA,GAAAG,CAAA;QACvBQ,aAAa,CAAC,KAAK,CAAC;QAACX,cAAA,GAAAG,CAAA;QACrB,MAAMkC,GAAG;MACX;IACF,CAAC;IAAA,iBAAAoB,EAAA;MAAA,OAAAN,MAAA,CAAAO,KAAA,OAAAJ,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAKN,IAAMK,YAAY,IAAA3D,cAAA,GAAAG,CAAA,QAAGP,WAAW,CAAAqC,iBAAA,CAAC,aAA2B;IAAAjC,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC1D,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACF4B,QAAQ,CAAC,IAAI,CAAC;MAAC/B,cAAA,GAAAG,CAAA;MACf,MAAML,YAAY,CAAC6D,YAAY,CAAC,CAAC;MAAC3D,cAAA,GAAAG,CAAA;MAClCQ,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC,CAAC,OAAO0B,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAtC,cAAA,GAAAG,CAAA,QAAGkC,GAAG,YAAYE,KAAK,IAAAvC,cAAA,GAAAoC,CAAA,UAAGC,GAAG,CAACG,OAAO,KAAAxC,cAAA,GAAAoC,CAAA,UAAG,yBAAyB;MAACpC,cAAA,GAAAG,CAAA;MACpF4B,QAAQ,CAACO,YAAY,CAAC;IACxB;EACF,CAAC,GAAE,EAAE,CAAC;EAKN,IAAMsB,qBAAqB,IAAA5D,cAAA,GAAAG,CAAA,QAAGP,WAAW;IAAA,IAAAiE,MAAA,GAAA5B,iBAAA,CAAC,WACxC6B,IAA4D,EAC5DC,UAAmB,EACD;MAAA/D,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MAClB,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACF4B,QAAQ,CAAC,IAAI,CAAC;QAAC/B,cAAA,GAAAG,CAAA;QACfQ,aAAa,CAAC,IAAI,CAAC;QAACX,cAAA,GAAAG,CAAA;QAEpB,MAAML,YAAY,CAAC8D,qBAAqB,CAACE,IAAI,EAAEC,UAAU,CAAC;QAAC/D,cAAA,GAAAG,CAAA;QAC3DQ,aAAa,CAAC,KAAK,CAAC;MACtB,CAAC,CAAC,OAAO0B,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAtC,cAAA,GAAAG,CAAA,QAAGkC,GAAG,YAAYE,KAAK,IAAAvC,cAAA,GAAAoC,CAAA,WAAGC,GAAG,CAACG,OAAO,KAAAxC,cAAA,GAAAoC,CAAA,WAAG,mCAAmC;QAACpC,cAAA,GAAAG,CAAA;QAC9F4B,QAAQ,CAACO,YAAY,CAAC;QAACtC,cAAA,GAAAG,CAAA;QACvBQ,aAAa,CAAC,KAAK,CAAC;QAACX,cAAA,GAAAG,CAAA;QACrB,MAAMkC,GAAG;MACX;IACF,CAAC;IAAA,iBAAA2B,GAAA,EAAAC,GAAA;MAAA,OAAAJ,MAAA,CAAAH,KAAA,OAAAJ,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAKN,IAAMY,cAAc,IAAAlE,cAAA,GAAAG,CAAA,QAAGP,WAAW,CAAC,UAAC+C,UAAkB,EAA0B;IAAA3C,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC9E,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACF4B,QAAQ,CAAC,IAAI,CAAC;MAAC/B,cAAA,GAAAG,CAAA;MACf,OAAOL,YAAY,CAACgD,mBAAmB,CAACH,UAAU,CAAC;IACrD,CAAC,CAAC,OAAON,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAtC,cAAA,GAAAG,CAAA,QAAGkC,GAAG,YAAYE,KAAK,IAAAvC,cAAA,GAAAoC,CAAA,WAAGC,GAAG,CAACG,OAAO,KAAAxC,cAAA,GAAAoC,CAAA,WAAG,2BAA2B;MAACpC,cAAA,GAAAG,CAAA;MACtF4B,QAAQ,CAACO,YAAY,CAAC;MAACtC,cAAA,GAAAG,CAAA;MACvB,OAAO,IAAI;IACb;EACF,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMgE,oBAAoB,IAAAnE,cAAA,GAAAG,CAAA,QAAGP,WAAW,CAAC,YAAgB;IAAAI,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACvD,OAAOL,YAAY,CAACqE,oBAAoB,CAAC,CAAC;EAC5C,CAAC,EAAE,EAAE,CAAC;EAACnE,cAAA,GAAAG,CAAA;EAGPN,SAAS,CAAC,YAAM;IAAAG,cAAA,GAAAC,CAAA;IACd,IAAMmE,QAAQ,IAAApE,cAAA,GAAAG,CAAA,QAAGkE,WAAW,CAAC,YAAM;MAAArE,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACjCI,cAAc,CAACT,YAAY,CAACwE,oBAAoB,CAAC,CAAC,CAAC;MAACtE,cAAA,GAAAG,CAAA;MACpDQ,aAAa,CAACb,YAAY,CAACyE,mBAAmB,CAAC,CAAC,CAAC;IACnD,CAAC,EAAE,GAAG,CAAC;IAACvE,cAAA,GAAAG,CAAA;IAER,OAAO,YAAM;MAAAH,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MAAA,OAAAqE,aAAa,CAACJ,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAACpE,cAAA,GAAAG,CAAA;EAGPN,SAAS,CAAC,YAAM;IAAAG,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACd6B,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;EAAChC,cAAA,GAAAG,CAAA;EAEtB,OAAO;IAELG,WAAW,EAAXA,WAAW;IACXI,UAAU,EAAVA,UAAU;IACVI,aAAa,EAAbA,aAAa;IACbI,cAAc,EAAdA,cAAc;IACdI,WAAW,EAAXA,WAAW;IACXI,UAAU,EAAVA,UAAU;IAGVM,eAAe,EAAfA,eAAe;IACfS,cAAc,EAAdA,cAAc;IACdQ,aAAa,EAAbA,aAAa;IAGbC,KAAK,EAALA,KAAK;IACLS,YAAY,EAAZA,YAAY;IACZC,qBAAqB,EAArBA,qBAAqB;IAGrBM,cAAc,EAAdA,cAAc;IACdC,oBAAoB,EAApBA,oBAAoB;IAGpBrC,KAAK,EAALA;EACF,CAAC;AACH", "ignoreList": []}