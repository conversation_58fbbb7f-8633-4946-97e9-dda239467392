a12acfe35f555c07d23cbc7d4040daea
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var _normalizeColors = _interopRequireDefault(require("@react-native/normalize-colors"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var NativeAnimatedAPI = _NativeAnimatedHelper.default.API;
var defaultColor = {
  r: 0,
  g: 0,
  b: 0,
  a: 1.0
};
var _uniqueId = 1;
var processColorObject = function processColorObject(color) {
  return color;
};
function processColor(color) {
  if (color === undefined || color === null) {
    return null;
  }
  if (isRgbaValue(color)) {
    return color;
  }
  var normalizedColor = (0, _normalizeColors.default)(color);
  if (normalizedColor === undefined || normalizedColor === null) {
    return null;
  }
  if (typeof normalizedColor === 'object') {
    var processedColorObj = processColorObject(normalizedColor);
    if (processedColorObj != null) {
      return processedColorObj;
    }
  } else if (typeof normalizedColor === 'number') {
    var r = (normalizedColor & 0xff000000) >>> 24;
    var g = (normalizedColor & 0x00ff0000) >>> 16;
    var b = (normalizedColor & 0x0000ff00) >>> 8;
    var a = (normalizedColor & 0x000000ff) / 255;
    return {
      r: r,
      g: g,
      b: b,
      a: a
    };
  }
  return null;
}
function isRgbaValue(value) {
  return value && typeof value.r === 'number' && typeof value.g === 'number' && typeof value.b === 'number' && typeof value.a === 'number';
}
function isRgbaAnimatedValue(value) {
  return value && value.r instanceof _AnimatedValue.default && value.g instanceof _AnimatedValue.default && value.b instanceof _AnimatedValue.default && value.a instanceof _AnimatedValue.default;
}
var AnimatedColor = function (_AnimatedWithChildren2) {
  function AnimatedColor(valueIn, config) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedColor);
    _this = _callSuper(this, AnimatedColor);
    _this._listeners = {};
    var value = valueIn !== null && valueIn !== void 0 ? valueIn : defaultColor;
    if (isRgbaAnimatedValue(value)) {
      var rgbaAnimatedValue = value;
      _this.r = rgbaAnimatedValue.r;
      _this.g = rgbaAnimatedValue.g;
      _this.b = rgbaAnimatedValue.b;
      _this.a = rgbaAnimatedValue.a;
    } else {
      var _processColor;
      var processedColor = (_processColor = processColor(value)) !== null && _processColor !== void 0 ? _processColor : defaultColor;
      var initColor = defaultColor;
      if (isRgbaValue(processedColor)) {
        initColor = processedColor;
      } else {
        _this.nativeColor = processedColor;
      }
      _this.r = new _AnimatedValue.default(initColor.r);
      _this.g = new _AnimatedValue.default(initColor.g);
      _this.b = new _AnimatedValue.default(initColor.b);
      _this.a = new _AnimatedValue.default(initColor.a);
    }
    if (_this.nativeColor || config && config.useNativeDriver) {
      _this.__makeNative();
    }
    return _this;
  }
  (0, _inherits2.default)(AnimatedColor, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedColor, [{
    key: "setValue",
    value: function setValue(value) {
      var _processColor2;
      var shouldUpdateNodeConfig = false;
      if (this.__isNative) {
        var nativeTag = this.__getNativeTag();
        NativeAnimatedAPI.setWaitingForIdentifier(nativeTag.toString());
      }
      var processedColor = (_processColor2 = processColor(value)) !== null && _processColor2 !== void 0 ? _processColor2 : defaultColor;
      if (isRgbaValue(processedColor)) {
        var rgbaValue = processedColor;
        this.r.setValue(rgbaValue.r);
        this.g.setValue(rgbaValue.g);
        this.b.setValue(rgbaValue.b);
        this.a.setValue(rgbaValue.a);
        if (this.nativeColor != null) {
          this.nativeColor = null;
          shouldUpdateNodeConfig = true;
        }
      } else {
        var nativeColor = processedColor;
        if (this.nativeColor !== nativeColor) {
          this.nativeColor = nativeColor;
          shouldUpdateNodeConfig = true;
        }
      }
      if (this.__isNative) {
        var _nativeTag = this.__getNativeTag();
        if (shouldUpdateNodeConfig) {
          NativeAnimatedAPI.updateAnimatedNodeConfig(_nativeTag, this.__getNativeConfig());
        }
        NativeAnimatedAPI.unsetWaitingForIdentifier(_nativeTag.toString());
      }
    }
  }, {
    key: "setOffset",
    value: function setOffset(offset) {
      this.r.setOffset(offset.r);
      this.g.setOffset(offset.g);
      this.b.setOffset(offset.b);
      this.a.setOffset(offset.a);
    }
  }, {
    key: "flattenOffset",
    value: function flattenOffset() {
      this.r.flattenOffset();
      this.g.flattenOffset();
      this.b.flattenOffset();
      this.a.flattenOffset();
    }
  }, {
    key: "extractOffset",
    value: function extractOffset() {
      this.r.extractOffset();
      this.g.extractOffset();
      this.b.extractOffset();
      this.a.extractOffset();
    }
  }, {
    key: "addListener",
    value: function addListener(callback) {
      var _this2 = this;
      var id = String(_uniqueId++);
      var jointCallback = function jointCallback(_ref) {
        var number = _ref.value;
        callback(_this2.__getValue());
      };
      this._listeners[id] = {
        r: this.r.addListener(jointCallback),
        g: this.g.addListener(jointCallback),
        b: this.b.addListener(jointCallback),
        a: this.a.addListener(jointCallback)
      };
      return id;
    }
  }, {
    key: "removeListener",
    value: function removeListener(id) {
      this.r.removeListener(this._listeners[id].r);
      this.g.removeListener(this._listeners[id].g);
      this.b.removeListener(this._listeners[id].b);
      this.a.removeListener(this._listeners[id].a);
      delete this._listeners[id];
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners() {
      this.r.removeAllListeners();
      this.g.removeAllListeners();
      this.b.removeAllListeners();
      this.a.removeAllListeners();
      this._listeners = {};
    }
  }, {
    key: "stopAnimation",
    value: function stopAnimation(callback) {
      this.r.stopAnimation();
      this.g.stopAnimation();
      this.b.stopAnimation();
      this.a.stopAnimation();
      callback && callback(this.__getValue());
    }
  }, {
    key: "resetAnimation",
    value: function resetAnimation(callback) {
      this.r.resetAnimation();
      this.g.resetAnimation();
      this.b.resetAnimation();
      this.a.resetAnimation();
      callback && callback(this.__getValue());
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      if (this.nativeColor != null) {
        return this.nativeColor;
      } else {
        return "rgba(" + this.r.__getValue() + ", " + this.g.__getValue() + ", " + this.b.__getValue() + ", " + this.a.__getValue() + ")";
      }
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this.r.__addChild(this);
      this.g.__addChild(this);
      this.b.__addChild(this);
      this.a.__addChild(this);
      _superPropGet(AnimatedColor, "__attach", this, 3)([]);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this.r.__removeChild(this);
      this.g.__removeChild(this);
      this.b.__removeChild(this);
      this.a.__removeChild(this);
      _superPropGet(AnimatedColor, "__detach", this, 3)([]);
    }
  }, {
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this.r.__makeNative(platformConfig);
      this.g.__makeNative(platformConfig);
      this.b.__makeNative(platformConfig);
      this.a.__makeNative(platformConfig);
      _superPropGet(AnimatedColor, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'color',
        r: this.r.__getNativeTag(),
        g: this.g.__getNativeTag(),
        b: this.b.__getNativeTag(),
        a: this.a.__getNativeTag(),
        nativeColor: this.nativeColor
      };
    }
  }]);
}(_AnimatedWithChildren.default);
exports.default = AnimatedColor;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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