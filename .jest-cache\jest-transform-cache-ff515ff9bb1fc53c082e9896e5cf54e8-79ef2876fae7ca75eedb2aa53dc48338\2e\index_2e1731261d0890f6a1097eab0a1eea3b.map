{"version": 3, "names": ["exports", "__esModule", "default", "_useLocale", "require", "_default", "useLocaleContext", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nexports.__esModule = true;\nexports.default = void 0;\nvar _useLocale = require(\"../../modules/useLocale\");\nvar _default = exports.default = _useLocale.useLocaleContext;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAUZ,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIC,UAAU,GAAGC,OAAO,0BAA0B,CAAC;AACnD,IAAIC,QAAQ,GAAGL,OAAO,CAACE,OAAO,GAAGC,UAAU,CAACG,gBAAgB;AAC5DC,MAAM,CAACP,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}