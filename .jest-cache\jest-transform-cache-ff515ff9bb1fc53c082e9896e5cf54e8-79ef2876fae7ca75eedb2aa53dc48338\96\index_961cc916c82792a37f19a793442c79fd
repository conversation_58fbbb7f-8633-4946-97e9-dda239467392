f19c3382f736a47686b485b591d97231
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _InteractionManager = _interopRequireDefault(require("../../../exports/InteractionManager"));
var _TouchHistoryMath = _interopRequireDefault(require("../TouchHistoryMath"));
var currentCentroidXOfTouchesChangedAfter = _TouchHistoryMath.default.currentCentroidXOfTouchesChangedAfter;
var currentCentroidYOfTouchesChangedAfter = _TouchHistoryMath.default.currentCentroidYOfTouchesChangedAfter;
var previousCentroidXOfTouchesChangedAfter = _TouchHistoryMath.default.previousCentroidXOfTouchesChangedAfter;
var previousCentroidYOfTouchesChangedAfter = _TouchHistoryMath.default.previousCentroidYOfTouchesChangedAfter;
var currentCentroidX = _TouchHistoryMath.default.currentCentroidX;
var currentCentroidY = _TouchHistoryMath.default.currentCentroidY;
var PanResponder = {
  _initializeGestureState: function _initializeGestureState(gestureState) {
    gestureState.moveX = 0;
    gestureState.moveY = 0;
    gestureState.x0 = 0;
    gestureState.y0 = 0;
    gestureState.dx = 0;
    gestureState.dy = 0;
    gestureState.vx = 0;
    gestureState.vy = 0;
    gestureState.numberActiveTouches = 0;
    gestureState._accountsForMovesUpTo = 0;
  },
  _updateGestureStateOnMove: function _updateGestureStateOnMove(gestureState, touchHistory) {
    gestureState.numberActiveTouches = touchHistory.numberActiveTouches;
    gestureState.moveX = currentCentroidXOfTouchesChangedAfter(touchHistory, gestureState._accountsForMovesUpTo);
    gestureState.moveY = currentCentroidYOfTouchesChangedAfter(touchHistory, gestureState._accountsForMovesUpTo);
    var movedAfter = gestureState._accountsForMovesUpTo;
    var prevX = previousCentroidXOfTouchesChangedAfter(touchHistory, movedAfter);
    var x = currentCentroidXOfTouchesChangedAfter(touchHistory, movedAfter);
    var prevY = previousCentroidYOfTouchesChangedAfter(touchHistory, movedAfter);
    var y = currentCentroidYOfTouchesChangedAfter(touchHistory, movedAfter);
    var nextDX = gestureState.dx + (x - prevX);
    var nextDY = gestureState.dy + (y - prevY);
    var dt = touchHistory.mostRecentTimeStamp - gestureState._accountsForMovesUpTo;
    gestureState.vx = (nextDX - gestureState.dx) / dt;
    gestureState.vy = (nextDY - gestureState.dy) / dt;
    gestureState.dx = nextDX;
    gestureState.dy = nextDY;
    gestureState._accountsForMovesUpTo = touchHistory.mostRecentTimeStamp;
  },
  create: function create(config) {
    var interactionState = {
      handle: null,
      shouldCancelClick: false,
      timeout: null
    };
    var gestureState = {
      stateID: Math.random(),
      moveX: 0,
      moveY: 0,
      x0: 0,
      y0: 0,
      dx: 0,
      dy: 0,
      vx: 0,
      vy: 0,
      numberActiveTouches: 0,
      _accountsForMovesUpTo: 0
    };
    var panHandlers = {
      onStartShouldSetResponder: function onStartShouldSetResponder(event) {
        return config.onStartShouldSetPanResponder == null ? false : config.onStartShouldSetPanResponder(event, gestureState);
      },
      onMoveShouldSetResponder: function onMoveShouldSetResponder(event) {
        return config.onMoveShouldSetPanResponder == null ? false : config.onMoveShouldSetPanResponder(event, gestureState);
      },
      onStartShouldSetResponderCapture: function onStartShouldSetResponderCapture(event) {
        if (event.nativeEvent.touches.length === 1) {
          PanResponder._initializeGestureState(gestureState);
        }
        gestureState.numberActiveTouches = event.touchHistory.numberActiveTouches;
        return config.onStartShouldSetPanResponderCapture != null ? config.onStartShouldSetPanResponderCapture(event, gestureState) : false;
      },
      onMoveShouldSetResponderCapture: function onMoveShouldSetResponderCapture(event) {
        var touchHistory = event.touchHistory;
        if (gestureState._accountsForMovesUpTo === touchHistory.mostRecentTimeStamp) {
          return false;
        }
        PanResponder._updateGestureStateOnMove(gestureState, touchHistory);
        return config.onMoveShouldSetPanResponderCapture ? config.onMoveShouldSetPanResponderCapture(event, gestureState) : false;
      },
      onResponderGrant: function onResponderGrant(event) {
        if (!interactionState.handle) {
          interactionState.handle = _InteractionManager.default.createInteractionHandle();
        }
        if (interactionState.timeout) {
          clearInteractionTimeout(interactionState);
        }
        interactionState.shouldCancelClick = true;
        gestureState.x0 = currentCentroidX(event.touchHistory);
        gestureState.y0 = currentCentroidY(event.touchHistory);
        gestureState.dx = 0;
        gestureState.dy = 0;
        if (config.onPanResponderGrant) {
          config.onPanResponderGrant(event, gestureState);
        }
        return config.onShouldBlockNativeResponder == null ? true : config.onShouldBlockNativeResponder(event, gestureState);
      },
      onResponderReject: function onResponderReject(event) {
        clearInteractionHandle(interactionState, config.onPanResponderReject, event, gestureState);
      },
      onResponderRelease: function onResponderRelease(event) {
        clearInteractionHandle(interactionState, config.onPanResponderRelease, event, gestureState);
        setInteractionTimeout(interactionState);
        PanResponder._initializeGestureState(gestureState);
      },
      onResponderStart: function onResponderStart(event) {
        var touchHistory = event.touchHistory;
        gestureState.numberActiveTouches = touchHistory.numberActiveTouches;
        if (config.onPanResponderStart) {
          config.onPanResponderStart(event, gestureState);
        }
      },
      onResponderMove: function onResponderMove(event) {
        var touchHistory = event.touchHistory;
        if (gestureState._accountsForMovesUpTo === touchHistory.mostRecentTimeStamp) {
          return;
        }
        PanResponder._updateGestureStateOnMove(gestureState, touchHistory);
        if (config.onPanResponderMove) {
          config.onPanResponderMove(event, gestureState);
        }
      },
      onResponderEnd: function onResponderEnd(event) {
        var touchHistory = event.touchHistory;
        gestureState.numberActiveTouches = touchHistory.numberActiveTouches;
        clearInteractionHandle(interactionState, config.onPanResponderEnd, event, gestureState);
      },
      onResponderTerminate: function onResponderTerminate(event) {
        clearInteractionHandle(interactionState, config.onPanResponderTerminate, event, gestureState);
        setInteractionTimeout(interactionState);
        PanResponder._initializeGestureState(gestureState);
      },
      onResponderTerminationRequest: function onResponderTerminationRequest(event) {
        return config.onPanResponderTerminationRequest == null ? true : config.onPanResponderTerminationRequest(event, gestureState);
      },
      onClickCapture: function onClickCapture(event) {
        if (interactionState.shouldCancelClick === true) {
          event.stopPropagation();
          event.preventDefault();
        }
      }
    };
    return {
      panHandlers: panHandlers,
      getInteractionHandle: function getInteractionHandle() {
        return interactionState.handle;
      }
    };
  }
};
function clearInteractionHandle(interactionState, callback, event, gestureState) {
  if (interactionState.handle) {
    _InteractionManager.default.clearInteractionHandle(interactionState.handle);
    interactionState.handle = null;
  }
  if (callback) {
    callback(event, gestureState);
  }
}
function clearInteractionTimeout(interactionState) {
  clearTimeout(interactionState.timeout);
}
function setInteractionTimeout(interactionState) {
  interactionState.timeout = setTimeout(function () {
    interactionState.shouldCancelClick = false;
  }, 250);
}
var _default = exports.default = PanResponder;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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