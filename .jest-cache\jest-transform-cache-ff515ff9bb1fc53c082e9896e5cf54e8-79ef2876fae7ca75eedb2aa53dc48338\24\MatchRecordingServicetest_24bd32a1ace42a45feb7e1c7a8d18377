08f9229b10aad039a1c203954536eb81
_getJestObj().mock("../../services/video/VideoRecordingService");
_getJestObj().mock("../../services/database/MatchRepository");
_getJestObj().mock("../../services/storage/FileUploadService");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _MatchRecordingService = require("../../services/match/MatchRecordingService");
var _VideoRecordingService = require("../../services/video/VideoRecordingService");
var _MatchRepository = require("../../services/database/MatchRepository");
var _FileUploadService = require("../../services/storage/FileUploadService");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockVideoService = _VideoRecordingService.videoRecordingService;
var mockMatchRepository = _MatchRepository.matchRepository;
var mockFileUploadService = _FileUploadService.fileUploadService;
describe('MatchRecordingService', function () {
  var mockMetadata = {
    userId: 'user-123',
    opponentName: 'John Doe',
    matchType: 'friendly',
    matchFormat: 'best_of_3',
    surface: 'hard',
    location: 'Local Tennis Club',
    matchDate: '2024-01-15'
  };
  var mockOptions = {
    enableVideoRecording: true,
    enableAutoScoreDetection: false,
    videoConfig: {
      quality: 'medium',
      fps: 30,
      resolution: '720p',
      codec: 'h264',
      maxDurationMinutes: 180,
      maxFileSizeMB: 500,
      enableAudio: true,
      enableStabilization: true
    },
    enableStatisticsTracking: true
  };
  beforeEach(function () {
    jest.clearAllMocks();
    if (_MatchRecordingService.matchRecordingService.getCurrentSession()) {
      _MatchRecordingService.matchRecordingService.cancelMatch();
    }
  });
  describe('startMatch', function () {
    it('should start a new match recording session successfully', (0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      var session = yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
      expect(session).toBeDefined();
      expect(session.match.metadata.opponentName).toBe('John Doe');
      expect(session.isRecording).toBe(true);
      expect(session.videoRecordingActive).toBe(true);
      expect(mockVideoService.startRecording).toHaveBeenCalledWith(mockOptions.videoConfig);
      expect(mockMatchRepository.createMatch).toHaveBeenCalled();
    }));
    it('should throw error for invalid metadata', (0, _asyncToGenerator2.default)(function* () {
      var invalidMetadata = Object.assign({}, mockMetadata, {
        opponentName: ''
      });
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(invalidMetadata, mockOptions)).rejects.toThrow('Opponent name is required');
    }));
    it('should handle video recording failure gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockRejectedValue(new Error('Camera not available'));
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions)).rejects.toThrow('Camera not available');
    }));
    it('should handle database save failure', (0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: null,
        error: 'Database connection failed'
      });
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions)).rejects.toThrow('Database connection failed');
    }));
  });
  describe('addPoint', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
    }));
    it('should add a point for user successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.addPoint('user', 'winner', 'forehand');
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session).toBeDefined();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should add a point for opponent successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.addPoint('opponent', 'ace');
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session).toBeDefined();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should throw error when no active session', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.cancelMatch();
      yield expect(_MatchRecordingService.matchRecordingService.addPoint('user')).rejects.toThrow('No active match session');
    }));
    it('should update statistics correctly', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.addPoint('user', 'ace');
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session == null ? void 0 : session.match.statistics.aces).toBe(1);
      expect(session == null ? void 0 : session.match.statistics.totalPointsWon).toBe(1);
      expect(session == null ? void 0 : session.match.statistics.totalPointsPlayed).toBe(1);
    }));
  });
  describe('pauseMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockVideoService.pauseRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
    }));
    it('should pause match successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session == null ? void 0 : session.isPaused).toBe(true);
      expect(session == null ? void 0 : session.match.status).toBe('paused');
      expect(mockVideoService.pauseRecording).toHaveBeenCalled();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should not pause if already paused', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
      jest.clearAllMocks();
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
      expect(mockVideoService.pauseRecording).not.toHaveBeenCalled();
    }));
  });
  describe('resumeMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockVideoService.pauseRecording.mockResolvedValue(undefined);
      mockVideoService.resumeRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
    }));
    it('should resume match successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.resumeMatch();
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session == null ? void 0 : session.isPaused).toBe(false);
      expect(session == null ? void 0 : session.match.status).toBe('recording');
      expect(mockVideoService.resumeRecording).toHaveBeenCalled();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should not resume if not paused', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.resumeMatch();
      jest.clearAllMocks();
      yield _MatchRecordingService.matchRecordingService.resumeMatch();
      expect(mockVideoService.resumeRecording).not.toHaveBeenCalled();
    }));
  });
  describe('endMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockVideoService.stopRecording.mockResolvedValue({
        uri: 'file://video.mp4',
        duration: 3600,
        fileSize: 50000000,
        width: 1920,
        height: 1080,
        thumbnail: 'file://thumbnail.jpg'
      });
      mockFileUploadService.uploadVideo.mockResolvedValue({
        data: {
          url: 'https://storage.supabase.co/video.mp4',
          path: 'matches/match-123/video.mp4',
          size: 50000000,
          type: 'video'
        },
        error: null
      });
      mockFileUploadService.uploadThumbnail.mockResolvedValue({
        data: {
          url: 'https://storage.supabase.co/thumbnail.jpg',
          path: 'matches/match-123/thumbnail.jpg',
          size: 100000,
          type: 'image'
        },
        error: null
      });
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
    }));
    it('should end match successfully with video upload', (0, _asyncToGenerator2.default)(function* () {
      var result = yield _MatchRecordingService.matchRecordingService.endMatch();
      expect(result).toBeDefined();
      expect(result.status).toBe('completed');
      expect(result.videoUrl).toBe('https://storage.supabase.co/video.mp4');
      expect(result.videoThumbnailUrl).toBe('https://storage.supabase.co/thumbnail.jpg');
      expect(mockVideoService.stopRecording).toHaveBeenCalled();
      expect(mockFileUploadService.uploadVideo).toHaveBeenCalled();
      expect(mockFileUploadService.uploadThumbnail).toHaveBeenCalled();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should handle video upload failure gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockFileUploadService.uploadVideo.mockResolvedValue({
        data: null,
        error: 'Upload failed'
      });
      var result = yield _MatchRecordingService.matchRecordingService.endMatch();
      expect(result).toBeDefined();
      expect(result.status).toBe('completed');
      expect(result.videoUrl).toBeUndefined();
    }));
    it('should throw error when no active session', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.cancelMatch();
      yield expect(_MatchRecordingService.matchRecordingService.endMatch()).rejects.toThrow('No active match session');
    }));
  });
  describe('cancelMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockVideoService.stopRecording.mockResolvedValue({
        uri: 'file://video.mp4',
        duration: 3600,
        fileSize: 50000000,
        width: 1920,
        height: 1080
      });
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
    }));
    it('should cancel match successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.cancelMatch();
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session).toBeNull();
      expect(mockVideoService.stopRecording).toHaveBeenCalled();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalledWith('match-123', expect.objectContaining({
        match_status: 'cancelled'
      }));
    }));
    it('should handle cancellation when no active session', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.cancelMatch();
      yield expect(_MatchRecordingService.matchRecordingService.cancelMatch()).resolves.toBeUndefined();
    }));
  });
  describe('session listeners', function () {
    it('should notify session listeners on session changes', (0, _asyncToGenerator2.default)(function* () {
      var listener = jest.fn();
      _MatchRecordingService.matchRecordingService.addSessionListener(listener);
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
      expect(listener).toHaveBeenCalledWith(expect.objectContaining({
        isRecording: true
      }));
    }));
    it('should remove session listeners correctly', function () {
      var listener = jest.fn();
      _MatchRecordingService.matchRecordingService.addSessionListener(listener);
      _MatchRecordingService.matchRecordingService.removeSessionListener(listener);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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