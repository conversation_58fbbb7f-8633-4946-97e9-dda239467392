da4a98e8ab4bab09e7b8e489d0fb67e3
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_6vmdlqe7j() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\edge\\GeoOptimizedContentManager.ts";
  var hash = "3118e759b01d050621c04d39582a64d7c833e0a0";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\edge\\GeoOptimizedContentManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 120,
          column: 59
        },
        end: {
          line: 120,
          column: 68
        }
      },
      "1": {
        start: {
          line: 121,
          column: 57
        },
        end: {
          line: 121,
          column: 66
        }
      },
      "2": {
        start: {
          line: 122,
          column: 55
        },
        end: {
          line: 122,
          column: 64
        }
      },
      "3": {
        start: {
          line: 123,
          column: 61
        },
        end: {
          line: 123,
          column: 70
        }
      },
      "4": {
        start: {
          line: 125,
          column: 56
        },
        end: {
          line: 207,
          column: 3
        }
      },
      "5": {
        start: {
          line: 210,
          column: 4
        },
        end: {
          line: 210,
          column: 39
        }
      },
      "6": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 229,
          column: 5
        }
      },
      "7": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 221,
          column: 9
        }
      },
      "8": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 56
        }
      },
      "9": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 47
        }
      },
      "10": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 76
        }
      },
      "11": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 228,
          column: 82
        }
      },
      "12": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 291,
          column: 5
        }
      },
      "13": {
        start: {
          line: 237,
          column: 24
        },
        end: {
          line: 237,
          column: 34
        }
      },
      "14": {
        start: {
          line: 240,
          column: 23
        },
        end: {
          line: 240,
          column: 65
        }
      },
      "15": {
        start: {
          line: 241,
          column: 28
        },
        end: {
          line: 241,
          column: 64
        }
      },
      "16": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 245,
          column: 7
        }
      },
      "17": {
        start: {
          line: 244,
          column: 8
        },
        end: {
          line: 244,
          column: 29
        }
      },
      "18": {
        start: {
          line: 248,
          column: 29
        },
        end: {
          line: 248,
          column: 79
        }
      },
      "19": {
        start: {
          line: 251,
          column: 29
        },
        end: {
          line: 255,
          column: 7
        }
      },
      "20": {
        start: {
          line: 258,
          column: 28
        },
        end: {
          line: 262,
          column: 7
        }
      },
      "21": {
        start: {
          line: 265,
          column: 27
        },
        end: {
          line: 269,
          column: 7
        }
      },
      "22": {
        start: {
          line: 272,
          column: 49
        },
        end: {
          line: 278,
          column: 7
        }
      },
      "23": {
        start: {
          line: 281,
          column: 6
        },
        end: {
          line: 281,
          column: 61
        }
      },
      "24": {
        start: {
          line: 283,
          column: 29
        },
        end: {
          line: 283,
          column: 51
        }
      },
      "25": {
        start: {
          line: 284,
          column: 6
        },
        end: {
          line: 284,
          column: 88
        }
      },
      "26": {
        start: {
          line: 286,
          column: 6
        },
        end: {
          line: 286,
          column: 30
        }
      },
      "27": {
        start: {
          line: 289,
          column: 6
        },
        end: {
          line: 289,
          column: 63
        }
      },
      "28": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 46
        }
      },
      "29": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 321,
          column: 5
        }
      },
      "30": {
        start: {
          line: 302,
          column: 47
        },
        end: {
          line: 302,
          column: 49
        }
      },
      "31": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 314,
          column: 7
        }
      },
      "32": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 313,
          column: 9
        }
      },
      "33": {
        start: {
          line: 306,
          column: 32
        },
        end: {
          line: 308,
          column: 22
        }
      },
      "34": {
        start: {
          line: 310,
          column: 10
        },
        end: {
          line: 312,
          column: 11
        }
      },
      "35": {
        start: {
          line: 311,
          column: 12
        },
        end: {
          line: 311,
          column: 87
        }
      },
      "36": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 316,
          column: 48
        }
      },
      "37": {
        start: {
          line: 317,
          column: 6
        },
        end: {
          line: 317,
          column: 103
        }
      },
      "38": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 320,
          column: 66
        }
      },
      "39": {
        start: {
          line: 339,
          column: 26
        },
        end: {
          line: 340,
          column: 58
        }
      },
      "40": {
        start: {
          line: 340,
          column: 33
        },
        end: {
          line: 340,
          column: 54
        }
      },
      "41": {
        start: {
          line: 342,
          column: 29
        },
        end: {
          line: 342,
          column: 67
        }
      },
      "42": {
        start: {
          line: 345,
          column: 26
        },
        end: {
          line: 345,
          column: 53
        }
      },
      "43": {
        start: {
          line: 346,
          column: 22
        },
        end: {
          line: 347,
          column: 59
        }
      },
      "44": {
        start: {
          line: 347,
          column: 25
        },
        end: {
          line: 347,
          column: 51
        }
      },
      "45": {
        start: {
          line: 348,
          column: 25
        },
        end: {
          line: 348,
          column: 82
        }
      },
      "46": {
        start: {
          line: 351,
          column: 53
        },
        end: {
          line: 351,
          column: 55
        }
      },
      "47": {
        start: {
          line: 352,
          column: 4
        },
        end: {
          line: 358,
          column: 7
        }
      },
      "48": {
        start: {
          line: 353,
          column: 6
        },
        end: {
          line: 357,
          column: 8
        }
      },
      "49": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 367,
          column: 6
        }
      },
      "50": {
        start: {
          line: 379,
          column: 22
        },
        end: {
          line: 379,
          column: 64
        }
      },
      "51": {
        start: {
          line: 381,
          column: 36
        },
        end: {
          line: 395,
          column: 5
        }
      },
      "52": {
        start: {
          line: 397,
          column: 4
        },
        end: {
          line: 399,
          column: 5
        }
      },
      "53": {
        start: {
          line: 398,
          column: 6
        },
        end: {
          line: 398,
          column: 50
        }
      },
      "54": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 401,
          column: 59
        }
      },
      "55": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 403,
          column: 79
        }
      },
      "56": {
        start: {
          line: 404,
          column: 4
        },
        end: {
          line: 404,
          column: 21
        }
      },
      "57": {
        start: {
          line: 411,
          column: 46
        },
        end: {
          line: 425,
          column: 5
        }
      },
      "58": {
        start: {
          line: 427,
          column: 19
        },
        end: {
          line: 427,
          column: 65
        }
      },
      "59": {
        start: {
          line: 428,
          column: 4
        },
        end: {
          line: 428,
          column: 90
        }
      },
      "60": {
        start: {
          line: 436,
          column: 21
        },
        end: {
          line: 436,
          column: 62
        }
      },
      "61": {
        start: {
          line: 438,
          column: 4
        },
        end: {
          line: 441,
          column: 5
        }
      },
      "62": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 440,
          column: 82
        }
      },
      "63": {
        start: {
          line: 444,
          column: 27
        },
        end: {
          line: 459,
          column: 6
        }
      },
      "64": {
        start: {
          line: 445,
          column: 18
        },
        end: {
          line: 445,
          column: 19
        }
      },
      "65": {
        start: {
          line: 448,
          column: 6
        },
        end: {
          line: 448,
          column: 64
        }
      },
      "66": {
        start: {
          line: 448,
          column: 52
        },
        end: {
          line: 448,
          column: 64
        }
      },
      "67": {
        start: {
          line: 451,
          column: 6
        },
        end: {
          line: 451,
          column: 62
        }
      },
      "68": {
        start: {
          line: 451,
          column: 50
        },
        end: {
          line: 451,
          column: 62
        }
      },
      "69": {
        start: {
          line: 454,
          column: 18
        },
        end: {
          line: 454,
          column: 57
        }
      },
      "70": {
        start: {
          line: 455,
          column: 23
        },
        end: {
          line: 455,
          column: 57
        }
      },
      "71": {
        start: {
          line: 456,
          column: 6
        },
        end: {
          line: 456,
          column: 24
        }
      },
      "72": {
        start: {
          line: 458,
          column: 6
        },
        end: {
          line: 458,
          column: 32
        }
      },
      "73": {
        start: {
          line: 462,
          column: 4
        },
        end: {
          line: 462,
          column: 53
        }
      },
      "74": {
        start: {
          line: 462,
          column: 34
        },
        end: {
          line: 462,
          column: 51
        }
      },
      "75": {
        start: {
          line: 463,
          column: 4
        },
        end: {
          line: 463,
          column: 37
        }
      },
      "76": {
        start: {
          line: 471,
          column: 43
        },
        end: {
          line: 471,
          column: 45
        }
      },
      "77": {
        start: {
          line: 472,
          column: 27
        },
        end: {
          line: 472,
          column: 28
        }
      },
      "78": {
        start: {
          line: 473,
          column: 26
        },
        end: {
          line: 473,
          column: 27
        }
      },
      "79": {
        start: {
          line: 474,
          column: 27
        },
        end: {
          line: 474,
          column: 28
        }
      },
      "80": {
        start: {
          line: 477,
          column: 4
        },
        end: {
          line: 482,
          column: 5
        }
      },
      "81": {
        start: {
          line: 478,
          column: 30
        },
        end: {
          line: 478,
          column: 72
        }
      },
      "82": {
        start: {
          line: 479,
          column: 6
        },
        end: {
          line: 479,
          column: 67
        }
      },
      "83": {
        start: {
          line: 480,
          column: 6
        },
        end: {
          line: 480,
          column: 29
        }
      },
      "84": {
        start: {
          line: 481,
          column: 6
        },
        end: {
          line: 481,
          column: 28
        }
      },
      "85": {
        start: {
          line: 485,
          column: 29
        },
        end: {
          line: 485,
          column: 72
        }
      },
      "86": {
        start: {
          line: 486,
          column: 4
        },
        end: {
          line: 486,
          column: 65
        }
      },
      "87": {
        start: {
          line: 488,
          column: 4
        },
        end: {
          line: 501,
          column: 5
        }
      },
      "88": {
        start: {
          line: 490,
          column: 8
        },
        end: {
          line: 490,
          column: 31
        }
      },
      "89": {
        start: {
          line: 491,
          column: 8
        },
        end: {
          line: 491,
          column: 35
        }
      },
      "90": {
        start: {
          line: 492,
          column: 8
        },
        end: {
          line: 492,
          column: 14
        }
      },
      "91": {
        start: {
          line: 494,
          column: 8
        },
        end: {
          line: 494,
          column: 31
        }
      },
      "92": {
        start: {
          line: 495,
          column: 8
        },
        end: {
          line: 495,
          column: 35
        }
      },
      "93": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 496,
          column: 14
        }
      },
      "94": {
        start: {
          line: 498,
          column: 8
        },
        end: {
          line: 498,
          column: 31
        }
      },
      "95": {
        start: {
          line: 499,
          column: 8
        },
        end: {
          line: 499,
          column: 34
        }
      },
      "96": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 500,
          column: 14
        }
      },
      "97": {
        start: {
          line: 504,
          column: 4
        },
        end: {
          line: 508,
          column: 5
        }
      },
      "98": {
        start: {
          line: 505,
          column: 6
        },
        end: {
          line: 505,
          column: 55
        }
      },
      "99": {
        start: {
          line: 506,
          column: 6
        },
        end: {
          line: 506,
          column: 29
        }
      },
      "100": {
        start: {
          line: 507,
          column: 6
        },
        end: {
          line: 507,
          column: 28
        }
      },
      "101": {
        start: {
          line: 511,
          column: 4
        },
        end: {
          line: 515,
          column: 5
        }
      },
      "102": {
        start: {
          line: 512,
          column: 6
        },
        end: {
          line: 512,
          column: 62
        }
      },
      "103": {
        start: {
          line: 513,
          column: 6
        },
        end: {
          line: 513,
          column: 29
        }
      },
      "104": {
        start: {
          line: 514,
          column: 6
        },
        end: {
          line: 514,
          column: 28
        }
      },
      "105": {
        start: {
          line: 518,
          column: 4
        },
        end: {
          line: 520,
          column: 5
        }
      },
      "106": {
        start: {
          line: 519,
          column: 6
        },
        end: {
          line: 519,
          column: 55
        }
      },
      "107": {
        start: {
          line: 522,
          column: 4
        },
        end: {
          line: 529,
          column: 6
        }
      },
      "108": {
        start: {
          line: 538,
          column: 22
        },
        end: {
          line: 548,
          column: 6
        }
      },
      "109": {
        start: {
          line: 550,
          column: 4
        },
        end: {
          line: 554,
          column: 6
        }
      },
      "110": {
        start: {
          line: 562,
          column: 14
        },
        end: {
          line: 562,
          column: 21
        }
      },
      "111": {
        start: {
          line: 564,
          column: 4
        },
        end: {
          line: 564,
          column: 47
        }
      },
      "112": {
        start: {
          line: 564,
          column: 32
        },
        end: {
          line: 564,
          column: 47
        }
      },
      "113": {
        start: {
          line: 565,
          column: 4
        },
        end: {
          line: 565,
          column: 47
        }
      },
      "114": {
        start: {
          line: 565,
          column: 32
        },
        end: {
          line: 565,
          column: 47
        }
      },
      "115": {
        start: {
          line: 568,
          column: 4
        },
        end: {
          line: 570,
          column: 5
        }
      },
      "116": {
        start: {
          line: 569,
          column: 6
        },
        end: {
          line: 569,
          column: 78
        }
      },
      "117": {
        start: {
          line: 572,
          column: 4
        },
        end: {
          line: 576,
          column: 6
        }
      },
      "118": {
        start: {
          line: 584,
          column: 22
        },
        end: {
          line: 584,
          column: 69
        }
      },
      "119": {
        start: {
          line: 586,
          column: 36
        },
        end: {
          line: 608,
          column: 5
        }
      },
      "120": {
        start: {
          line: 610,
          column: 4
        },
        end: {
          line: 612,
          column: 5
        }
      },
      "121": {
        start: {
          line: 611,
          column: 6
        },
        end: {
          line: 611,
          column: 46
        }
      },
      "122": {
        start: {
          line: 614,
          column: 4
        },
        end: {
          line: 614,
          column: 55
        }
      },
      "123": {
        start: {
          line: 616,
          column: 4
        },
        end: {
          line: 616,
          column: 19
        }
      },
      "124": {
        start: {
          line: 621,
          column: 26
        },
        end: {
          line: 640,
          column: 5
        }
      },
      "125": {
        start: {
          line: 642,
          column: 4
        },
        end: {
          line: 656,
          column: 5
        }
      },
      "126": {
        start: {
          line: 643,
          column: 6
        },
        end: {
          line: 655,
          column: 7
        }
      },
      "127": {
        start: {
          line: 644,
          column: 8
        },
        end: {
          line: 654,
          column: 10
        }
      },
      "128": {
        start: {
          line: 660,
          column: 16
        },
        end: {
          line: 660,
          column: 130
        }
      },
      "129": {
        start: {
          line: 661,
          column: 4
        },
        end: {
          line: 661,
          column: 34
        }
      },
      "130": {
        start: {
          line: 666,
          column: 4
        },
        end: {
          line: 666,
          column: 16
        }
      },
      "131": {
        start: {
          line: 670,
          column: 4
        },
        end: {
          line: 697,
          column: 6
        }
      },
      "132": {
        start: {
          line: 701,
          column: 4
        },
        end: {
          line: 706,
          column: 5
        }
      },
      "133": {
        start: {
          line: 702,
          column: 6
        },
        end: {
          line: 702,
          column: 73
        }
      },
      "134": {
        start: {
          line: 705,
          column: 6
        },
        end: {
          line: 705,
          column: 83
        }
      },
      "135": {
        start: {
          line: 710,
          column: 48
        },
        end: {
          line: 714,
          column: 5
        }
      },
      "136": {
        start: {
          line: 715,
          column: 4
        },
        end: {
          line: 715,
          column: 39
        }
      },
      "137": {
        start: {
          line: 720,
          column: 15
        },
        end: {
          line: 720,
          column: 16
        }
      },
      "138": {
        start: {
          line: 722,
          column: 4
        },
        end: {
          line: 722,
          column: 69
        }
      },
      "139": {
        start: {
          line: 722,
          column: 22
        },
        end: {
          line: 722,
          column: 69
        }
      },
      "140": {
        start: {
          line: 723,
          column: 4
        },
        end: {
          line: 723,
          column: 75
        }
      },
      "141": {
        start: {
          line: 723,
          column: 24
        },
        end: {
          line: 723,
          column: 75
        }
      },
      "142": {
        start: {
          line: 724,
          column: 4
        },
        end: {
          line: 724,
          column: 77
        }
      },
      "143": {
        start: {
          line: 724,
          column: 24
        },
        end: {
          line: 724,
          column: 77
        }
      },
      "144": {
        start: {
          line: 725,
          column: 4
        },
        end: {
          line: 725,
          column: 75
        }
      },
      "145": {
        start: {
          line: 725,
          column: 24
        },
        end: {
          line: 725,
          column: 75
        }
      },
      "146": {
        start: {
          line: 727,
          column: 4
        },
        end: {
          line: 727,
          column: 16
        }
      },
      "147": {
        start: {
          line: 732,
          column: 42
        },
        end: {
          line: 732,
          column: 74
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 209,
            column: 2
          },
          end: {
            line: 209,
            column: 3
          }
        },
        loc: {
          start: {
            line: 209,
            column: 16
          },
          end: {
            line: 211,
            column: 3
          }
        },
        line: 209
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 216,
            column: 2
          },
          end: {
            line: 216,
            column: 3
          }
        },
        loc: {
          start: {
            line: 216,
            column: 61
          },
          end: {
            line: 230,
            column: 3
          }
        },
        line: 216
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 219,
            column: 36
          },
          end: {
            line: 219,
            column: 37
          }
        },
        loc: {
          start: {
            line: 219,
            column: 46
          },
          end: {
            line: 221,
            column: 7
          }
        },
        line: 219
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 235,
            column: 2
          },
          end: {
            line: 235,
            column: 3
          }
        },
        loc: {
          start: {
            line: 235,
            column: 92
          },
          end: {
            line: 292,
            column: 3
          }
        },
        line: 235
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 297,
            column: 2
          },
          end: {
            line: 297,
            column: 3
          }
        },
        loc: {
          start: {
            line: 300,
            column: 19
          },
          end: {
            line: 322,
            column: 3
          }
        },
        line: 300
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 327,
            column: 2
          },
          end: {
            line: 327,
            column: 3
          }
        },
        loc: {
          start: {
            line: 338,
            column: 4
          },
          end: {
            line: 368,
            column: 3
          }
        },
        line: 338
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 340,
            column: 14
          },
          end: {
            line: 340,
            column: 15
          }
        },
        loc: {
          start: {
            line: 340,
            column: 33
          },
          end: {
            line: 340,
            column: 54
          }
        },
        line: 340
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 347,
            column: 14
          },
          end: {
            line: 347,
            column: 15
          }
        },
        loc: {
          start: {
            line: 347,
            column: 25
          },
          end: {
            line: 347,
            column: 51
          }
        },
        line: 347
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 352,
            column: 33
          },
          end: {
            line: 352,
            column: 34
          }
        },
        loc: {
          start: {
            line: 352,
            column: 53
          },
          end: {
            line: 358,
            column: 5
          }
        },
        line: 352
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 378,
            column: 21
          },
          end: {
            line: 405,
            column: 3
          }
        },
        line: 378
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 409,
            column: 2
          },
          end: {
            line: 409,
            column: 3
          }
        },
        loc: {
          start: {
            line: 409,
            column: 82
          },
          end: {
            line: 429,
            column: 3
          }
        },
        line: 409
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 431,
            column: 2
          },
          end: {
            line: 431,
            column: 3
          }
        },
        loc: {
          start: {
            line: 435,
            column: 29
          },
          end: {
            line: 464,
            column: 3
          }
        },
        line: 435
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 444,
            column: 40
          },
          end: {
            line: 444,
            column: 41
          }
        },
        loc: {
          start: {
            line: 444,
            column: 51
          },
          end: {
            line: 459,
            column: 5
          }
        },
        line: 444
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 462,
            column: 24
          },
          end: {
            line: 462,
            column: 25
          }
        },
        loc: {
          start: {
            line: 462,
            column: 34
          },
          end: {
            line: 462,
            column: 51
          }
        },
        line: 462
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 466,
            column: 2
          },
          end: {
            line: 466,
            column: 3
          }
        },
        loc: {
          start: {
            line: 470,
            column: 48
          },
          end: {
            line: 530,
            column: 3
          }
        },
        line: 470
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 532,
            column: 2
          },
          end: {
            line: 532,
            column: 3
          }
        },
        loc: {
          start: {
            line: 536,
            column: 47
          },
          end: {
            line: 555,
            column: 3
          }
        },
        line: 536
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 557,
            column: 2
          },
          end: {
            line: 557,
            column: 3
          }
        },
        loc: {
          start: {
            line: 560,
            column: 33
          },
          end: {
            line: 577,
            column: 3
          }
        },
        line: 560
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 579,
            column: 2
          },
          end: {
            line: 579,
            column: 3
          }
        },
        loc: {
          start: {
            line: 583,
            column: 29
          },
          end: {
            line: 617,
            column: 3
          }
        },
        line: 583
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 619,
            column: 2
          },
          end: {
            line: 619,
            column: 3
          }
        },
        loc: {
          start: {
            line: 619,
            column: 61
          },
          end: {
            line: 657,
            column: 3
          }
        },
        line: 619
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 659,
            column: 2
          },
          end: {
            line: 659,
            column: 3
          }
        },
        loc: {
          start: {
            line: 659,
            column: 84
          },
          end: {
            line: 662,
            column: 3
          }
        },
        line: 659
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 664,
            column: 2
          },
          end: {
            line: 664,
            column: 3
          }
        },
        loc: {
          start: {
            line: 664,
            column: 59
          },
          end: {
            line: 667,
            column: 3
          }
        },
        line: 664
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 669,
            column: 2
          },
          end: {
            line: 669,
            column: 3
          }
        },
        loc: {
          start: {
            line: 669,
            column: 84
          },
          end: {
            line: 698,
            column: 3
          }
        },
        line: 669
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 700,
            column: 2
          },
          end: {
            line: 700,
            column: 3
          }
        },
        loc: {
          start: {
            line: 700,
            column: 89
          },
          end: {
            line: 707,
            column: 3
          }
        },
        line: 700
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 709,
            column: 2
          },
          end: {
            line: 709,
            column: 3
          }
        },
        loc: {
          start: {
            line: 709,
            column: 52
          },
          end: {
            line: 716,
            column: 3
          }
        },
        line: 709
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 718,
            column: 2
          },
          end: {
            line: 718,
            column: 3
          }
        },
        loc: {
          start: {
            line: 718,
            column: 75
          },
          end: {
            line: 728,
            column: 3
          }
        },
        line: 718
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 243,
            column: 6
          },
          end: {
            line: 245,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 243,
            column: 6
          },
          end: {
            line: 245,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 243
      },
      "1": {
        loc: {
          start: {
            line: 243,
            column: 10
          },
          end: {
            line: 243,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 10
          },
          end: {
            line: 243,
            column: 23
          }
        }, {
          start: {
            line: 243,
            column: 27
          },
          end: {
            line: 243,
            column: 59
          }
        }],
        line: 243
      },
      "2": {
        loc: {
          start: {
            line: 299,
            column: 4
          },
          end: {
            line: 299,
            column: 31
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 299,
            column: 24
          },
          end: {
            line: 299,
            column: 31
          }
        }],
        line: 299
      },
      "3": {
        loc: {
          start: {
            line: 306,
            column: 32
          },
          end: {
            line: 308,
            column: 22
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 307,
            column: 14
          },
          end: {
            line: 307,
            column: 53
          }
        }, {
          start: {
            line: 308,
            column: 14
          },
          end: {
            line: 308,
            column: 22
          }
        }],
        line: 306
      },
      "4": {
        loc: {
          start: {
            line: 348,
            column: 25
          },
          end: {
            line: 348,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 348,
            column: 45
          },
          end: {
            line: 348,
            column: 78
          }
        }, {
          start: {
            line: 348,
            column: 81
          },
          end: {
            line: 348,
            column: 82
          }
        }],
        line: 348
      },
      "5": {
        loc: {
          start: {
            line: 397,
            column: 4
          },
          end: {
            line: 399,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 397,
            column: 4
          },
          end: {
            line: 399,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 397
      },
      "6": {
        loc: {
          start: {
            line: 427,
            column: 19
          },
          end: {
            line: 427,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 427,
            column: 19
          },
          end: {
            line: 427,
            column: 46
          }
        }, {
          start: {
            line: 427,
            column: 50
          },
          end: {
            line: 427,
            column: 65
          }
        }],
        line: 427
      },
      "7": {
        loc: {
          start: {
            line: 428,
            column: 11
          },
          end: {
            line: 428,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 428,
            column: 11
          },
          end: {
            line: 428,
            column: 43
          }
        }, {
          start: {
            line: 428,
            column: 47
          },
          end: {
            line: 428,
            column: 89
          }
        }],
        line: 428
      },
      "8": {
        loc: {
          start: {
            line: 436,
            column: 21
          },
          end: {
            line: 436,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 436,
            column: 21
          },
          end: {
            line: 436,
            column: 56
          }
        }, {
          start: {
            line: 436,
            column: 60
          },
          end: {
            line: 436,
            column: 62
          }
        }],
        line: 436
      },
      "9": {
        loc: {
          start: {
            line: 438,
            column: 4
          },
          end: {
            line: 441,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 438,
            column: 4
          },
          end: {
            line: 441,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 438
      },
      "10": {
        loc: {
          start: {
            line: 448,
            column: 6
          },
          end: {
            line: 448,
            column: 64
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 448,
            column: 6
          },
          end: {
            line: 448,
            column: 64
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 448
      },
      "11": {
        loc: {
          start: {
            line: 451,
            column: 6
          },
          end: {
            line: 451,
            column: 62
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 451,
            column: 6
          },
          end: {
            line: 451,
            column: 62
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 451
      },
      "12": {
        loc: {
          start: {
            line: 477,
            column: 4
          },
          end: {
            line: 482,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 477,
            column: 4
          },
          end: {
            line: 482,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 477
      },
      "13": {
        loc: {
          start: {
            line: 488,
            column: 4
          },
          end: {
            line: 501,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 489,
            column: 6
          },
          end: {
            line: 492,
            column: 14
          }
        }, {
          start: {
            line: 493,
            column: 6
          },
          end: {
            line: 496,
            column: 14
          }
        }, {
          start: {
            line: 497,
            column: 6
          },
          end: {
            line: 500,
            column: 14
          }
        }],
        line: 488
      },
      "14": {
        loc: {
          start: {
            line: 504,
            column: 4
          },
          end: {
            line: 508,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 504,
            column: 4
          },
          end: {
            line: 508,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 504
      },
      "15": {
        loc: {
          start: {
            line: 511,
            column: 4
          },
          end: {
            line: 515,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 511,
            column: 4
          },
          end: {
            line: 515,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 511
      },
      "16": {
        loc: {
          start: {
            line: 511,
            column: 8
          },
          end: {
            line: 511,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 511,
            column: 8
          },
          end: {
            line: 511,
            column: 48
          }
        }, {
          start: {
            line: 511,
            column: 52
          },
          end: {
            line: 511,
            column: 90
          }
        }],
        line: 511
      },
      "17": {
        loc: {
          start: {
            line: 518,
            column: 4
          },
          end: {
            line: 520,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 518,
            column: 4
          },
          end: {
            line: 520,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 518
      },
      "18": {
        loc: {
          start: {
            line: 564,
            column: 4
          },
          end: {
            line: 564,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 564,
            column: 4
          },
          end: {
            line: 564,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 564
      },
      "19": {
        loc: {
          start: {
            line: 565,
            column: 4
          },
          end: {
            line: 565,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 565,
            column: 4
          },
          end: {
            line: 565,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 565
      },
      "20": {
        loc: {
          start: {
            line: 568,
            column: 4
          },
          end: {
            line: 570,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 568,
            column: 4
          },
          end: {
            line: 570,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 568
      },
      "21": {
        loc: {
          start: {
            line: 574,
            column: 16
          },
          end: {
            line: 574,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 574,
            column: 75
          },
          end: {
            line: 574,
            column: 87
          }
        }, {
          start: {
            line: 574,
            column: 90
          },
          end: {
            line: 574,
            column: 98
          }
        }],
        line: 574
      },
      "22": {
        loc: {
          start: {
            line: 593,
            column: 15
          },
          end: {
            line: 593,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 593,
            column: 57
          },
          end: {
            line: 593,
            column: 75
          }
        }, {
          start: {
            line: 593,
            column: 78
          },
          end: {
            line: 593,
            column: 80
          }
        }],
        line: 593
      },
      "23": {
        loc: {
          start: {
            line: 610,
            column: 4
          },
          end: {
            line: 612,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 610,
            column: 4
          },
          end: {
            line: 612,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 610
      },
      "24": {
        loc: {
          start: {
            line: 715,
            column: 11
          },
          end: {
            line: 715,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 715,
            column: 11
          },
          end: {
            line: 715,
            column: 30
          }
        }, {
          start: {
            line: 715,
            column: 34
          },
          end: {
            line: 715,
            column: 38
          }
        }],
        line: 715
      },
      "25": {
        loc: {
          start: {
            line: 722,
            column: 4
          },
          end: {
            line: 722,
            column: 69
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 722,
            column: 4
          },
          end: {
            line: 722,
            column: 69
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 722
      },
      "26": {
        loc: {
          start: {
            line: 723,
            column: 4
          },
          end: {
            line: 723,
            column: 75
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 723,
            column: 4
          },
          end: {
            line: 723,
            column: 75
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 723
      },
      "27": {
        loc: {
          start: {
            line: 724,
            column: 4
          },
          end: {
            line: 724,
            column: 77
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 724,
            column: 4
          },
          end: {
            line: 724,
            column: 77
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 724
      },
      "28": {
        loc: {
          start: {
            line: 725,
            column: 4
          },
          end: {
            line: 725,
            column: 75
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 725,
            column: 4
          },
          end: {
            line: 725,
            column: 75
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 725
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "3118e759b01d050621c04d39582a64d7c833e0a0"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_6vmdlqe7j = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_6vmdlqe7j();
import { globalCDNManager } from "./GlobalCDNManager";
import { performanceMonitor } from "../../utils/performance";
var GeoOptimizedContentManager = function () {
  function GeoOptimizedContentManager() {
    _classCallCheck(this, GeoOptimizedContentManager);
    this.contentVariants = (cov_6vmdlqe7j().s[0]++, new Map());
    this.regionalConfigs = (cov_6vmdlqe7j().s[1]++, new Map());
    this.geoLocationCache = (cov_6vmdlqe7j().s[2]++, new Map());
    this.optimizationCache = (cov_6vmdlqe7j().s[3]++, new Map());
    this.REGIONAL_CONFIGS = (cov_6vmdlqe7j().s[4]++, [{
      region: 'north_america',
      preferences: {
        imageFormats: ['webp', 'avif', 'jpeg'],
        videoCodecs: ['h264', 'h265', 'av1'],
        compressionLevel: 'medium',
        qualityPreference: 'balanced'
      },
      compliance: {
        dataRetention: 365,
        cookieConsent: true,
        gdprCompliant: false,
        localDataStorage: false
      },
      network: {
        averageBandwidth: 50,
        latencyTolerance: 100,
        mobileUsage: 60
      },
      cultural: {
        colorPreferences: ['blue', 'green', 'white'],
        layoutDirection: 'ltr',
        dateFormat: 'MM/DD/YYYY',
        numberFormat: '1,234.56',
        culturalSensitivities: []
      }
    }, {
      region: 'europe',
      preferences: {
        imageFormats: ['webp', 'jpeg', 'png'],
        videoCodecs: ['h264', 'h265'],
        compressionLevel: 'high',
        qualityPreference: 'quality'
      },
      compliance: {
        dataRetention: 90,
        cookieConsent: true,
        gdprCompliant: true,
        localDataStorage: true
      },
      network: {
        averageBandwidth: 40,
        latencyTolerance: 80,
        mobileUsage: 70
      },
      cultural: {
        colorPreferences: ['blue', 'gray', 'white'],
        layoutDirection: 'ltr',
        dateFormat: 'DD/MM/YYYY',
        numberFormat: '1.234,56',
        culturalSensitivities: ['privacy_focused']
      }
    }, {
      region: 'asia_pacific',
      preferences: {
        imageFormats: ['webp', 'jpeg'],
        videoCodecs: ['h264'],
        compressionLevel: 'high',
        qualityPreference: 'speed'
      },
      compliance: {
        dataRetention: 180,
        cookieConsent: false,
        gdprCompliant: false,
        localDataStorage: true
      },
      network: {
        averageBandwidth: 25,
        latencyTolerance: 150,
        mobileUsage: 85
      },
      cultural: {
        colorPreferences: ['red', 'gold', 'white'],
        layoutDirection: 'ltr',
        dateFormat: 'YYYY/MM/DD',
        numberFormat: '1,234.56',
        culturalSensitivities: ['mobile_first', 'data_conscious']
      }
    }]);
    cov_6vmdlqe7j().f[0]++;
    cov_6vmdlqe7j().s[5]++;
    this.initializeGeoContentManager();
  }
  return _createClass(GeoOptimizedContentManager, [{
    key: "initializeGeoContentManager",
    value: (function () {
      var _initializeGeoContentManager = _asyncToGenerator(function* () {
        var _this = this;
        cov_6vmdlqe7j().f[1]++;
        cov_6vmdlqe7j().s[6]++;
        try {
          cov_6vmdlqe7j().s[7]++;
          this.REGIONAL_CONFIGS.forEach(function (config) {
            cov_6vmdlqe7j().f[2]++;
            cov_6vmdlqe7j().s[8]++;
            _this.regionalConfigs.set(config.region, config);
          });
          cov_6vmdlqe7j().s[9]++;
          yield this.createSampleContentVariants();
          cov_6vmdlqe7j().s[10]++;
          console.log('Geo-Optimized Content Manager initialized successfully');
        } catch (error) {
          cov_6vmdlqe7j().s[11]++;
          console.error('Failed to initialize Geo-Optimized Content Manager:', error);
        }
      });
      function initializeGeoContentManager() {
        return _initializeGeoContentManager.apply(this, arguments);
      }
      return initializeGeoContentManager;
    }())
  }, {
    key: "getOptimizedContent",
    value: (function () {
      var _getOptimizedContent = _asyncToGenerator(function* (request) {
        cov_6vmdlqe7j().f[3]++;
        cov_6vmdlqe7j().s[12]++;
        try {
          var startTime = (cov_6vmdlqe7j().s[13]++, Date.now());
          var cacheKey = (cov_6vmdlqe7j().s[14]++, this.generateOptimizationCacheKey(request));
          var cachedContent = (cov_6vmdlqe7j().s[15]++, this.optimizationCache.get(cacheKey));
          cov_6vmdlqe7j().s[16]++;
          if ((cov_6vmdlqe7j().b[1][0]++, cachedContent) && (cov_6vmdlqe7j().b[1][1]++, this.isCacheValid(cachedContent))) {
            cov_6vmdlqe7j().b[0][0]++;
            cov_6vmdlqe7j().s[17]++;
            return cachedContent;
          } else {
            cov_6vmdlqe7j().b[0][1]++;
          }
          var regionalConfig = (cov_6vmdlqe7j().s[18]++, yield this.getRegionalConfig(request.userLocation));
          var contentVariant = (cov_6vmdlqe7j().s[19]++, yield this.selectContentVariant(request.contentId, request.userLocation, regionalConfig));
          var optimizations = (cov_6vmdlqe7j().s[20]++, yield this.applyGeoOptimizations(contentVariant, request, regionalConfig));
          var deliveryUrls = (cov_6vmdlqe7j().s[21]++, yield this.generateDeliveryUrls(contentVariant, request.userLocation, optimizations));
          var optimizedContent = (cov_6vmdlqe7j().s[22]++, {
            contentId: request.contentId,
            variant: contentVariant,
            deliveryUrls: deliveryUrls,
            optimizations: optimizations,
            caching: this.determineCachingStrategy(contentVariant, regionalConfig)
          });
          cov_6vmdlqe7j().s[23]++;
          this.optimizationCache.set(cacheKey, optimizedContent);
          var processingTime = (cov_6vmdlqe7j().s[24]++, Date.now() - startTime);
          cov_6vmdlqe7j().s[25]++;
          performanceMonitor.trackDatabaseQuery('geo_content_optimization', processingTime);
          cov_6vmdlqe7j().s[26]++;
          return optimizedContent;
        } catch (error) {
          cov_6vmdlqe7j().s[27]++;
          console.error('Failed to get optimized content:', error);
          cov_6vmdlqe7j().s[28]++;
          return this.getFallbackContent(request);
        }
      });
      function getOptimizedContent(_x) {
        return _getOptimizedContent.apply(this, arguments);
      }
      return getOptimizedContent;
    }())
  }, {
    key: "preloadContentGlobally",
    value: (function () {
      var _preloadContentGlobally = _asyncToGenerator(function* (contentIds) {
        var regions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_6vmdlqe7j().b[2][0]++, ['all']);
        cov_6vmdlqe7j().f[4]++;
        cov_6vmdlqe7j().s[29]++;
        try {
          var preloadPromises = (cov_6vmdlqe7j().s[30]++, []);
          cov_6vmdlqe7j().s[31]++;
          for (var contentId of contentIds) {
            cov_6vmdlqe7j().s[32]++;
            for (var region of regions) {
              var targetRegions = (cov_6vmdlqe7j().s[33]++, regions.includes('all') ? (cov_6vmdlqe7j().b[3][0]++, Array.from(this.regionalConfigs.keys())) : (cov_6vmdlqe7j().b[3][1]++, [region]));
              cov_6vmdlqe7j().s[34]++;
              for (var targetRegion of targetRegions) {
                cov_6vmdlqe7j().s[35]++;
                preloadPromises.push(this.preloadContentToRegion(contentId, targetRegion));
              }
            }
          }
          cov_6vmdlqe7j().s[36]++;
          yield Promise.allSettled(preloadPromises);
          cov_6vmdlqe7j().s[37]++;
          console.log(`Preloaded ${contentIds.length} content items to ${preloadPromises.length} regions`);
        } catch (error) {
          cov_6vmdlqe7j().s[38]++;
          console.error('Failed to preload content globally:', error);
        }
      });
      function preloadContentGlobally(_x2) {
        return _preloadContentGlobally.apply(this, arguments);
      }
      return preloadContentGlobally;
    }())
  }, {
    key: "getGeoOptimizationMetrics",
    value: function getGeoOptimizationMetrics() {
      var _this2 = this;
      cov_6vmdlqe7j().f[5]++;
      var totalVariants = (cov_6vmdlqe7j().s[39]++, Array.from(this.contentVariants.values()).reduce(function (sum, variants) {
        cov_6vmdlqe7j().f[6]++;
        cov_6vmdlqe7j().s[40]++;
        return sum + variants.length;
      }, 0));
      var regionalCoverage = (cov_6vmdlqe7j().s[41]++, this.regionalConfigs.size / 10 * 100);
      var cacheAttempts = (cov_6vmdlqe7j().s[42]++, this.optimizationCache.size);
      var cacheHits = (cov_6vmdlqe7j().s[43]++, Array.from(this.optimizationCache.values()).filter(function (content) {
        cov_6vmdlqe7j().f[7]++;
        cov_6vmdlqe7j().s[44]++;
        return _this2.isCacheValid(content);
      }).length);
      var cacheHitRate = (cov_6vmdlqe7j().s[45]++, cacheAttempts > 0 ? (cov_6vmdlqe7j().b[4][0]++, cacheHits / cacheAttempts * 100) : (cov_6vmdlqe7j().b[4][1]++, 0));
      var regionalPerformance = (cov_6vmdlqe7j().s[46]++, {});
      cov_6vmdlqe7j().s[47]++;
      this.regionalConfigs.forEach(function (config, region) {
        cov_6vmdlqe7j().f[8]++;
        cov_6vmdlqe7j().s[48]++;
        regionalPerformance[region] = {
          averageLoadTime: 200 - config.network.averageBandwidth * 2,
          optimizationRate: 85 + Math.random() * 10,
          userSatisfaction: 80 + Math.random() * 15
        };
      });
      cov_6vmdlqe7j().s[49]++;
      return {
        totalVariants: totalVariants,
        regionalCoverage: regionalCoverage,
        averageOptimization: 78,
        cacheHitRate: cacheHitRate,
        bandwidthSavings: 45,
        regionalPerformance: regionalPerformance
      };
    }
  }, {
    key: "createContentVariant",
    value: (function () {
      var _createContentVariant = _asyncToGenerator(function* (baseContentId, region, adaptations, content) {
        cov_6vmdlqe7j().f[9]++;
        var variantId = (cov_6vmdlqe7j().s[50]++, `${baseContentId}_${region}_${Date.now()}`);
        var variant = (cov_6vmdlqe7j().s[51]++, {
          id: variantId,
          baseContentId: baseContentId,
          region: region,
          language: this.getRegionLanguage(region),
          adaptations: adaptations,
          content: content,
          metadata: {
            createdAt: Date.now(),
            updatedAt: Date.now(),
            version: '1.0.0',
            size: this.calculateContentSize(content),
            format: 'optimized'
          }
        });
        cov_6vmdlqe7j().s[52]++;
        if (!this.contentVariants.has(baseContentId)) {
          cov_6vmdlqe7j().b[5][0]++;
          cov_6vmdlqe7j().s[53]++;
          this.contentVariants.set(baseContentId, []);
        } else {
          cov_6vmdlqe7j().b[5][1]++;
        }
        cov_6vmdlqe7j().s[54]++;
        this.contentVariants.get(baseContentId).push(variant);
        cov_6vmdlqe7j().s[55]++;
        console.log(`Created content variant: ${variantId} for region: ${region}`);
        cov_6vmdlqe7j().s[56]++;
        return variantId;
      });
      function createContentVariant(_x3, _x4, _x5, _x6) {
        return _createContentVariant.apply(this, arguments);
      }
      return createContentVariant;
    }())
  }, {
    key: "getRegionalConfig",
    value: function () {
      var _getRegionalConfig = _asyncToGenerator(function* (location) {
        cov_6vmdlqe7j().f[10]++;
        var regionMap = (cov_6vmdlqe7j().s[57]++, {
          'US': 'north_america',
          'CA': 'north_america',
          'MX': 'north_america',
          'GB': 'europe',
          'DE': 'europe',
          'FR': 'europe',
          'ES': 'europe',
          'IT': 'europe',
          'JP': 'asia_pacific',
          'KR': 'asia_pacific',
          'CN': 'asia_pacific',
          'AU': 'asia_pacific',
          'SG': 'asia_pacific'
        });
        var region = (cov_6vmdlqe7j().s[58]++, (cov_6vmdlqe7j().b[6][0]++, regionMap[location.country]) || (cov_6vmdlqe7j().b[6][1]++, 'north_america'));
        cov_6vmdlqe7j().s[59]++;
        return (cov_6vmdlqe7j().b[7][0]++, this.regionalConfigs.get(region)) || (cov_6vmdlqe7j().b[7][1]++, this.regionalConfigs.get('north_america'));
      });
      function getRegionalConfig(_x7) {
        return _getRegionalConfig.apply(this, arguments);
      }
      return getRegionalConfig;
    }()
  }, {
    key: "selectContentVariant",
    value: function () {
      var _selectContentVariant = _asyncToGenerator(function* (contentId, location, regionalConfig) {
        cov_6vmdlqe7j().f[11]++;
        var variants = (cov_6vmdlqe7j().s[60]++, (cov_6vmdlqe7j().b[8][0]++, this.contentVariants.get(contentId)) || (cov_6vmdlqe7j().b[8][1]++, []));
        cov_6vmdlqe7j().s[61]++;
        if (variants.length === 0) {
          cov_6vmdlqe7j().b[9][0]++;
          cov_6vmdlqe7j().s[62]++;
          return yield this.createDefaultVariant(contentId, location, regionalConfig);
        } else {
          cov_6vmdlqe7j().b[9][1]++;
        }
        var scoredVariants = (cov_6vmdlqe7j().s[63]++, variants.map(function (variant) {
          cov_6vmdlqe7j().f[12]++;
          var score = (cov_6vmdlqe7j().s[64]++, 0);
          cov_6vmdlqe7j().s[65]++;
          if (variant.region === regionalConfig.region) {
            cov_6vmdlqe7j().b[10][0]++;
            cov_6vmdlqe7j().s[66]++;
            score += 50;
          } else {
            cov_6vmdlqe7j().b[10][1]++;
          }
          cov_6vmdlqe7j().s[67]++;
          if (variant.language === location.language) {
            cov_6vmdlqe7j().b[11][0]++;
            cov_6vmdlqe7j().s[68]++;
            score += 30;
          } else {
            cov_6vmdlqe7j().b[11][1]++;
          }
          var age = (cov_6vmdlqe7j().s[69]++, Date.now() - variant.metadata.updatedAt);
          var ageScore = (cov_6vmdlqe7j().s[70]++, Math.max(0, 20 - age / 86400000));
          cov_6vmdlqe7j().s[71]++;
          score += ageScore;
          cov_6vmdlqe7j().s[72]++;
          return {
            variant: variant,
            score: score
          };
        }));
        cov_6vmdlqe7j().s[73]++;
        scoredVariants.sort(function (a, b) {
          cov_6vmdlqe7j().f[13]++;
          cov_6vmdlqe7j().s[74]++;
          return b.score - a.score;
        });
        cov_6vmdlqe7j().s[75]++;
        return scoredVariants[0].variant;
      });
      function selectContentVariant(_x8, _x9, _x0) {
        return _selectContentVariant.apply(this, arguments);
      }
      return selectContentVariant;
    }()
  }, {
    key: "applyGeoOptimizations",
    value: function () {
      var _applyGeoOptimizations = _asyncToGenerator(function* (variant, request, regionalConfig) {
        cov_6vmdlqe7j().f[14]++;
        var appliedOptimizations = (cov_6vmdlqe7j().s[76]++, []);
        var bandwidthSavings = (cov_6vmdlqe7j().s[77]++, 0);
        var loadTimeSavings = (cov_6vmdlqe7j().s[78]++, 0);
        var dataUsageSavings = (cov_6vmdlqe7j().s[79]++, 0);
        cov_6vmdlqe7j().s[80]++;
        if (variant.content.images) {
          cov_6vmdlqe7j().b[12][0]++;
          var preferredFormat = (cov_6vmdlqe7j().s[81]++, regionalConfig.preferences.imageFormats[0]);
          cov_6vmdlqe7j().s[82]++;
          appliedOptimizations.push(`image_format_${preferredFormat}`);
          cov_6vmdlqe7j().s[83]++;
          bandwidthSavings += 25;
          cov_6vmdlqe7j().s[84]++;
          loadTimeSavings += 15;
        } else {
          cov_6vmdlqe7j().b[12][1]++;
        }
        var compressionLevel = (cov_6vmdlqe7j().s[85]++, regionalConfig.preferences.compressionLevel);
        cov_6vmdlqe7j().s[86]++;
        appliedOptimizations.push(`compression_${compressionLevel}`);
        cov_6vmdlqe7j().s[87]++;
        switch (compressionLevel) {
          case 'high':
            cov_6vmdlqe7j().b[13][0]++;
            cov_6vmdlqe7j().s[88]++;
            bandwidthSavings += 40;
            cov_6vmdlqe7j().s[89]++;
            dataUsageSavings += 200000;
            cov_6vmdlqe7j().s[90]++;
            break;
          case 'medium':
            cov_6vmdlqe7j().b[13][1]++;
            cov_6vmdlqe7j().s[91]++;
            bandwidthSavings += 25;
            cov_6vmdlqe7j().s[92]++;
            dataUsageSavings += 100000;
            cov_6vmdlqe7j().s[93]++;
            break;
          case 'low':
            cov_6vmdlqe7j().b[13][2]++;
            cov_6vmdlqe7j().s[94]++;
            bandwidthSavings += 10;
            cov_6vmdlqe7j().s[95]++;
            dataUsageSavings += 50000;
            cov_6vmdlqe7j().s[96]++;
            break;
        }
        cov_6vmdlqe7j().s[97]++;
        if (request.deviceInfo.type === 'mobile') {
          cov_6vmdlqe7j().b[14][0]++;
          cov_6vmdlqe7j().s[98]++;
          appliedOptimizations.push('mobile_optimization');
          cov_6vmdlqe7j().s[99]++;
          bandwidthSavings += 15;
          cov_6vmdlqe7j().s[100]++;
          loadTimeSavings += 20;
        } else {
          cov_6vmdlqe7j().b[14][1]++;
        }
        cov_6vmdlqe7j().s[101]++;
        if ((cov_6vmdlqe7j().b[16][0]++, request.deviceInfo.connection === 'slow') || (cov_6vmdlqe7j().b[16][1]++, request.deviceInfo.connection === '4g')) {
          cov_6vmdlqe7j().b[15][0]++;
          cov_6vmdlqe7j().s[102]++;
          appliedOptimizations.push('low_bandwidth_optimization');
          cov_6vmdlqe7j().s[103]++;
          bandwidthSavings += 30;
          cov_6vmdlqe7j().s[104]++;
          loadTimeSavings += 25;
        } else {
          cov_6vmdlqe7j().b[15][1]++;
        }
        cov_6vmdlqe7j().s[105]++;
        if (variant.adaptations.cultural.length > 0) {
          cov_6vmdlqe7j().b[17][0]++;
          cov_6vmdlqe7j().s[106]++;
          appliedOptimizations.push('cultural_adaptation');
        } else {
          cov_6vmdlqe7j().b[17][1]++;
        }
        cov_6vmdlqe7j().s[107]++;
        return {
          applied: appliedOptimizations,
          estimatedSavings: {
            bandwidth: Math.min(bandwidthSavings, 80),
            loadTime: Math.min(loadTimeSavings, 70),
            dataUsage: dataUsageSavings
          }
        };
      });
      function applyGeoOptimizations(_x1, _x10, _x11) {
        return _applyGeoOptimizations.apply(this, arguments);
      }
      return applyGeoOptimizations;
    }()
  }, {
    key: "generateDeliveryUrls",
    value: function () {
      var _generateDeliveryUrls = _asyncToGenerator(function* (variant, location, optimizations) {
        cov_6vmdlqe7j().f[15]++;
        var cdnResult = (cov_6vmdlqe7j().s[108]++, yield globalCDNManager.getOptimizedDeliveryUrl({
          path: `/content/${variant.id}`,
          type: 'static',
          priority: 'medium',
          userLocation: {
            country: location.country,
            region: location.region,
            city: location.city,
            coordinates: location.coordinates
          }
        }));
        cov_6vmdlqe7j().s[109]++;
        return {
          primary: cdnResult.url,
          fallback: cdnResult.fallbackUrls,
          cdn: cdnResult.provider
        };
      });
      function generateDeliveryUrls(_x12, _x13, _x14) {
        return _generateDeliveryUrls.apply(this, arguments);
      }
      return generateDeliveryUrls;
    }()
  }, {
    key: "determineCachingStrategy",
    value: function determineCachingStrategy(variant, regionalConfig) {
      cov_6vmdlqe7j().f[16]++;
      var ttl = (cov_6vmdlqe7j().s[110]++, 3600000);
      cov_6vmdlqe7j().s[111]++;
      if (variant.content.images) {
        cov_6vmdlqe7j().b[18][0]++;
        cov_6vmdlqe7j().s[112]++;
        ttl = 86400000;
      } else {
        cov_6vmdlqe7j().b[18][1]++;
      }
      cov_6vmdlqe7j().s[113]++;
      if (variant.content.videos) {
        cov_6vmdlqe7j().b[19][0]++;
        cov_6vmdlqe7j().s[114]++;
        ttl = 43200000;
      } else {
        cov_6vmdlqe7j().b[19][1]++;
      }
      cov_6vmdlqe7j().s[115]++;
      if (regionalConfig.compliance.dataRetention < 30) {
        cov_6vmdlqe7j().b[20][0]++;
        cov_6vmdlqe7j().s[116]++;
        ttl = Math.min(ttl, regionalConfig.compliance.dataRetention * 86400000);
      } else {
        cov_6vmdlqe7j().b[20][1]++;
      }
      cov_6vmdlqe7j().s[117]++;
      return {
        ttl: ttl,
        strategy: regionalConfig.preferences.qualityPreference === 'speed' ? (cov_6vmdlqe7j().b[21][0]++, 'aggressive') : (cov_6vmdlqe7j().b[21][1]++, 'normal'),
        regions: [regionalConfig.region]
      };
    }
  }, {
    key: "createDefaultVariant",
    value: function () {
      var _createDefaultVariant = _asyncToGenerator(function* (contentId, location, regionalConfig) {
        cov_6vmdlqe7j().f[17]++;
        var variantId = (cov_6vmdlqe7j().s[118]++, `${contentId}_default_${regionalConfig.region}`);
        var variant = (cov_6vmdlqe7j().s[119]++, {
          id: variantId,
          baseContentId: contentId,
          region: regionalConfig.region,
          language: location.language,
          adaptations: {
            cultural: [],
            legal: regionalConfig.compliance.gdprCompliant ? (cov_6vmdlqe7j().b[22][0]++, ['gdpr_compliant']) : (cov_6vmdlqe7j().b[22][1]++, []),
            performance: ['basic_optimization'],
            localization: [`locale_${location.locale}`]
          },
          content: {
            text: {
              title: 'Default Content',
              description: 'Default content for region'
            },
            images: {
              thumbnail: '/images/default-thumbnail.jpg'
            }
          },
          metadata: {
            createdAt: Date.now(),
            updatedAt: Date.now(),
            version: '1.0.0',
            size: 50000,
            format: 'default'
          }
        });
        cov_6vmdlqe7j().s[120]++;
        if (!this.contentVariants.has(contentId)) {
          cov_6vmdlqe7j().b[23][0]++;
          cov_6vmdlqe7j().s[121]++;
          this.contentVariants.set(contentId, []);
        } else {
          cov_6vmdlqe7j().b[23][1]++;
        }
        cov_6vmdlqe7j().s[122]++;
        this.contentVariants.get(contentId).push(variant);
        cov_6vmdlqe7j().s[123]++;
        return variant;
      });
      function createDefaultVariant(_x15, _x16, _x17) {
        return _createDefaultVariant.apply(this, arguments);
      }
      return createDefaultVariant;
    }()
  }, {
    key: "createSampleContentVariants",
    value: function () {
      var _createSampleContentVariants = _asyncToGenerator(function* () {
        cov_6vmdlqe7j().f[18]++;
        var sampleContent = (cov_6vmdlqe7j().s[124]++, {
          'training_video_1': [{
            region: 'north_america',
            language: 'en',
            content: {
              videos: {
                main: '/videos/training-1-en-hd.mp4'
              },
              text: {
                title: 'Tennis Training Basics',
                description: 'Learn the fundamentals'
              }
            }
          }, {
            region: 'europe',
            language: 'en',
            content: {
              videos: {
                main: '/videos/training-1-en-compressed.mp4'
              },
              text: {
                title: 'Tennis Training Basics',
                description: 'Learn the fundamentals'
              }
            }
          }]
        });
        cov_6vmdlqe7j().s[125]++;
        for (var _ref of Object.entries(sampleContent)) {
          var _ref2 = _slicedToArray(_ref, 2);
          var contentId = _ref2[0];
          var variants = _ref2[1];
          cov_6vmdlqe7j().s[126]++;
          for (var variantData of variants) {
            cov_6vmdlqe7j().s[127]++;
            yield this.createContentVariant(contentId, variantData.region, {
              cultural: [],
              legal: [],
              performance: ['compression', 'format_optimization'],
              localization: [`locale_${variantData.language}`]
            }, variantData.content);
          }
        }
      });
      function createSampleContentVariants() {
        return _createSampleContentVariants.apply(this, arguments);
      }
      return createSampleContentVariants;
    }()
  }, {
    key: "generateOptimizationCacheKey",
    value: function generateOptimizationCacheKey(request) {
      cov_6vmdlqe7j().f[19]++;
      var key = (cov_6vmdlqe7j().s[128]++, `${request.contentId}_${request.userLocation.country}_${request.deviceInfo.type}_${request.deviceInfo.connection}`);
      cov_6vmdlqe7j().s[129]++;
      return btoa(key).slice(0, 32);
    }
  }, {
    key: "isCacheValid",
    value: function isCacheValid(content) {
      cov_6vmdlqe7j().f[20]++;
      cov_6vmdlqe7j().s[130]++;
      return true;
    }
  }, {
    key: "getFallbackContent",
    value: function getFallbackContent(request) {
      cov_6vmdlqe7j().f[21]++;
      cov_6vmdlqe7j().s[131]++;
      return {
        contentId: request.contentId,
        variant: {
          id: 'fallback',
          baseContentId: request.contentId,
          region: 'global',
          language: 'en',
          adaptations: {
            cultural: [],
            legal: [],
            performance: [],
            localization: []
          },
          content: {
            text: {
              title: 'Fallback Content'
            }
          },
          metadata: {
            createdAt: Date.now(),
            updatedAt: Date.now(),
            version: '1.0.0',
            size: 10000,
            format: 'fallback'
          }
        },
        deliveryUrls: {
          primary: 'https://fallback.acemind.app/content/fallback',
          fallback: [],
          cdn: 'fallback'
        },
        optimizations: {
          applied: ['fallback'],
          estimatedSavings: {
            bandwidth: 0,
            loadTime: 0,
            dataUsage: 0
          }
        },
        caching: {
          ttl: 300000,
          strategy: 'minimal',
          regions: ['global']
        }
      };
    }
  }, {
    key: "preloadContentToRegion",
    value: function () {
      var _preloadContentToRegion = _asyncToGenerator(function* (contentId, region) {
        cov_6vmdlqe7j().f[22]++;
        cov_6vmdlqe7j().s[132]++;
        try {
          cov_6vmdlqe7j().s[133]++;
          console.log(`Preloading content ${contentId} to region ${region}`);
        } catch (error) {
          cov_6vmdlqe7j().s[134]++;
          console.error(`Failed to preload content ${contentId} to ${region}:`, error);
        }
      });
      function preloadContentToRegion(_x18, _x19) {
        return _preloadContentToRegion.apply(this, arguments);
      }
      return preloadContentToRegion;
    }()
  }, {
    key: "getRegionLanguage",
    value: function getRegionLanguage(region) {
      cov_6vmdlqe7j().f[23]++;
      var languageMap = (cov_6vmdlqe7j().s[135]++, {
        'north_america': 'en',
        'europe': 'en',
        'asia_pacific': 'en'
      });
      cov_6vmdlqe7j().s[136]++;
      return (cov_6vmdlqe7j().b[24][0]++, languageMap[region]) || (cov_6vmdlqe7j().b[24][1]++, 'en');
    }
  }, {
    key: "calculateContentSize",
    value: function calculateContentSize(content) {
      cov_6vmdlqe7j().f[24]++;
      var size = (cov_6vmdlqe7j().s[137]++, 0);
      cov_6vmdlqe7j().s[138]++;
      if (content.text) {
        cov_6vmdlqe7j().b[25][0]++;
        cov_6vmdlqe7j().s[139]++;
        size += Object.keys(content.text).length * 100;
      } else {
        cov_6vmdlqe7j().b[25][1]++;
      }
      cov_6vmdlqe7j().s[140]++;
      if (content.images) {
        cov_6vmdlqe7j().b[26][0]++;
        cov_6vmdlqe7j().s[141]++;
        size += Object.keys(content.images).length * 50000;
      } else {
        cov_6vmdlqe7j().b[26][1]++;
      }
      cov_6vmdlqe7j().s[142]++;
      if (content.videos) {
        cov_6vmdlqe7j().b[27][0]++;
        cov_6vmdlqe7j().s[143]++;
        size += Object.keys(content.videos).length * 5000000;
      } else {
        cov_6vmdlqe7j().b[27][1]++;
      }
      cov_6vmdlqe7j().s[144]++;
      if (content.assets) {
        cov_6vmdlqe7j().b[28][0]++;
        cov_6vmdlqe7j().s[145]++;
        size += Object.keys(content.assets).length * 10000;
      } else {
        cov_6vmdlqe7j().b[28][1]++;
      }
      cov_6vmdlqe7j().s[146]++;
      return size;
    }
  }]);
}();
export var geoOptimizedContentManager = (cov_6vmdlqe7j().s[147]++, new GeoOptimizedContentManager());
export default geoOptimizedContentManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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