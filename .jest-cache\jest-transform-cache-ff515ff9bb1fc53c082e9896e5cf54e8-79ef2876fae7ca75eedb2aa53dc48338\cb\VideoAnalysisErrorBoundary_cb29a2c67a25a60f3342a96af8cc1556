32ae4fa5470c05286081b4d03cf5004e
function cov_181q53ux2z() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\VideoAnalysisErrorBoundary.tsx";
  var hash = "0558084b4f0a6eaa12e1ede260358ea94c7fdb45";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\VideoAnalysisErrorBoundary.tsx",
    statementMap: {
      "0": {
        start: {
          line: 12,
          column: 15
        },
        end: {
          line: 20,
          column: 1
        }
      },
      "1": {
        start: {
          line: 27,
          column: 35
        },
        end: {
          line: 77,
          column: 1
        }
      },
      "2": {
        start: {
          line: 28,
          column: 2
        },
        end: {
          line: 76,
          column: 9
        }
      },
      "3": {
        start: {
          line: 68,
          column: 12
        },
        end: {
          line: 68,
          column: 49
        }
      },
      "4": {
        start: {
          line: 79,
          column: 78
        },
        end: {
          line: 109,
          column: 1
        }
      },
      "5": {
        start: {
          line: 83,
          column: 22
        },
        end: {
          line: 98,
          column: 3
        }
      },
      "6": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 91,
          column: 7
        }
      },
      "7": {
        start: {
          line: 100,
          column: 2
        },
        end: {
          line: 108,
          column: 4
        }
      },
      "8": {
        start: {
          line: 111,
          column: 15
        },
        end: {
          line: 175,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 27,
            column: 35
          },
          end: {
            line: 27,
            column: 36
          }
        },
        loc: {
          start: {
            line: 28,
            column: 2
          },
          end: {
            line: 76,
            column: 9
          }
        },
        line: 28
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 66,
            column: 19
          },
          end: {
            line: 66,
            column: 20
          }
        },
        loc: {
          start: {
            line: 66,
            column: 25
          },
          end: {
            line: 69,
            column: 11
          }
        },
        line: 66
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 79,
            column: 78
          },
          end: {
            line: 79,
            column: 79
          }
        },
        loc: {
          start: {
            line: 82,
            column: 6
          },
          end: {
            line: 109,
            column: 1
          }
        },
        line: 82
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 83,
            column: 22
          },
          end: {
            line: 83,
            column: 23
          }
        },
        loc: {
          start: {
            line: 83,
            column: 68
          },
          end: {
            line: 98,
            column: 3
          }
        },
        line: 83
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 55,
            column: 9
          },
          end: {
            line: 62,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 9
          },
          end: {
            line: 55,
            column: 16
          }
        }, {
          start: {
            line: 56,
            column: 10
          },
          end: {
            line: 61,
            column: 12
          }
        }],
        line: 55
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0558084b4f0a6eaa12e1ede260358ea94c7fdb45"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_181q53ux2z = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_181q53ux2z();
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Video, AlertCircle, RefreshCw } from 'lucide-react-native';
import ErrorBoundary from "./ErrorBoundary";
import Button from "./ui/Button";
import Card from "./ui/Card";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_181q53ux2z().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  yellow: '#f59e0b'
});
cov_181q53ux2z().s[1]++;
var VideoAnalysisErrorFallback = function VideoAnalysisErrorFallback(_ref) {
  var onRetry = _ref.onRetry;
  cov_181q53ux2z().f[0]++;
  cov_181q53ux2z().s[2]++;
  return _jsx(Card, {
    style: styles.errorCard,
    children: _jsxs(View, {
      style: styles.errorContent,
      children: [_jsx(View, {
        style: styles.iconContainer,
        children: _jsx(AlertCircle, {
          size: 48,
          color: colors.red
        })
      }), _jsx(Text, {
        style: styles.errorTitle,
        children: "Video Analysis Failed"
      }), _jsx(Text, {
        style: styles.errorMessage,
        children: "We couldn't analyze your video at this time. This might be due to:"
      }), _jsxs(View, {
        style: styles.reasonsList,
        children: [_jsx(Text, {
          style: styles.reasonItem,
          children: "\u2022 Poor video quality or lighting"
        }), _jsx(Text, {
          style: styles.reasonItem,
          children: "\u2022 Network connectivity issues"
        }), _jsx(Text, {
          style: styles.reasonItem,
          children: "\u2022 Temporary service unavailability"
        }), _jsx(Text, {
          style: styles.reasonItem,
          children: "\u2022 Unsupported video format"
        })]
      }), _jsxs(View, {
        style: styles.suggestions,
        children: [_jsx(Text, {
          style: styles.suggestionsTitle,
          children: "Try these solutions:"
        }), _jsx(Text, {
          style: styles.suggestionItem,
          children: "\u2713 Ensure good lighting when recording"
        }), _jsx(Text, {
          style: styles.suggestionItem,
          children: "\u2713 Check your internet connection"
        }), _jsx(Text, {
          style: styles.suggestionItem,
          children: "\u2713 Record in landscape orientation"
        }), _jsx(Text, {
          style: styles.suggestionItem,
          children: "\u2713 Keep the camera steady during recording"
        })]
      }), _jsxs(View, {
        style: styles.actions,
        children: [(cov_181q53ux2z().b[0][0]++, onRetry) && (cov_181q53ux2z().b[0][1]++, _jsx(Button, {
          title: "Try Again",
          onPress: onRetry,
          style: styles.retryButton,
          icon: _jsx(RefreshCw, {
            size: 20,
            color: colors.white
          })
        })), _jsx(Button, {
          title: "Record New Video",
          onPress: function onPress() {
            cov_181q53ux2z().f[1]++;
            cov_181q53ux2z().s[3]++;
            console.log('Navigate to recording');
          },
          variant: "outline",
          style: styles.recordButton,
          icon: _jsx(Video, {
            size: 20,
            color: colors.primary
          })
        })]
      })]
    })
  });
};
cov_181q53ux2z().s[4]++;
var VideoAnalysisErrorBoundary = function VideoAnalysisErrorBoundary(_ref2) {
  var children = _ref2.children,
    onRetry = _ref2.onRetry;
  cov_181q53ux2z().f[2]++;
  cov_181q53ux2z().s[5]++;
  var handleError = function handleError(error, errorInfo) {
    cov_181q53ux2z().f[3]++;
    cov_181q53ux2z().s[6]++;
    console.error('Video Analysis Error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      context: 'video_analysis'
    });
  };
  cov_181q53ux2z().s[7]++;
  return _jsx(ErrorBoundary, {
    fallback: _jsx(VideoAnalysisErrorFallback, {
      onRetry: onRetry
    }),
    onError: handleError,
    showDetails: __DEV__,
    children: children
  });
};
var styles = (cov_181q53ux2z().s[8]++, StyleSheet.create({
  errorCard: {
    margin: 16,
    padding: 20
  },
  errorContent: {
    alignItems: 'center'
  },
  iconContainer: {
    marginBottom: 16
  },
  errorTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.dark,
    marginBottom: 8,
    textAlign: 'center'
  },
  errorMessage: {
    fontSize: 14,
    color: colors.gray,
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20
  },
  reasonsList: {
    alignSelf: 'stretch',
    marginBottom: 20
  },
  reasonItem: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 4,
    lineHeight: 20
  },
  suggestions: {
    alignSelf: 'stretch',
    backgroundColor: colors.lightGray,
    padding: 16,
    borderRadius: 8,
    marginBottom: 20
  },
  suggestionsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.dark,
    marginBottom: 8
  },
  suggestionItem: {
    fontSize: 13,
    color: colors.gray,
    marginBottom: 4,
    lineHeight: 18
  },
  actions: {
    alignSelf: 'stretch',
    gap: 12
  },
  retryButton: {
    backgroundColor: colors.primary
  },
  recordButton: {
    borderColor: colors.primary
  }
}));
export default VideoAnalysisErrorBoundary;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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