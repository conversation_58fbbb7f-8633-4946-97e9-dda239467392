91e38cbe11c0a87f35ee3794221f3633
function cov_9d0f9dnpf() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\ui\\LoadingState.tsx";
  var hash = "dc386c5dd0ca9ef7a4f1d8b7d16f4b7b800908fb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\ui\\LoadingState.tsx",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 11,
          column: 1
        }
      },
      "1": {
        start: {
          line: 18,
          column: 2
        },
        end: {
          line: 28,
          column: 4
        }
      },
      "2": {
        start: {
          line: 31,
          column: 15
        },
        end: {
          line: 70,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "LoadingState",
        decl: {
          start: {
            line: 17,
            column: 24
          },
          end: {
            line: 17,
            column: 36
          }
        },
        loc: {
          start: {
            line: 17,
            column: 84
          },
          end: {
            line: 29,
            column: 1
          }
        },
        line: 17
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 17,
            column: 39
          },
          end: {
            line: 17,
            column: 61
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 17,
            column: 49
          },
          end: {
            line: 17,
            column: 61
          }
        }],
        line: 17
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0
    },
    f: {
      "0": 0
    },
    b: {
      "0": [0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "dc386c5dd0ca9ef7a4f1d8b7d16f4b7b800908fb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_9d0f9dnpf = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_9d0f9dnpf();
import React from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { Zap } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_9d0f9dnpf().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
export default function LoadingState(_ref) {
  var _ref$message = _ref.message,
    message = _ref$message === void 0 ? (cov_9d0f9dnpf().b[0][0]++, 'Loading...') : _ref$message;
  cov_9d0f9dnpf().f[0]++;
  cov_9d0f9dnpf().s[1]++;
  return _jsx(View, {
    style: styles.container,
    children: _jsxs(View, {
      style: styles.content,
      children: [_jsx(View, {
        style: styles.iconContainer,
        children: _jsx(Zap, {
          size: 48,
          color: colors.primary
        })
      }), _jsx(ActivityIndicator, {
        size: "large",
        color: colors.primary,
        style: styles.spinner
      }), _jsx(Text, {
        style: styles.message,
        children: message
      })]
    })
  });
}
var styles = (cov_9d0f9dnpf().s[2]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 24
  },
  content: {
    alignItems: 'center',
    maxWidth: 280
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3
  },
  spinner: {
    marginBottom: 16
  },
  message: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: colors.gray,
    textAlign: 'center',
    lineHeight: 22
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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