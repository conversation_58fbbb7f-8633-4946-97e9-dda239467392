55a4a16c763dc6d041bc18e1e25b59d6
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = useAnimatedProps;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _AnimatedProps = _interopRequireDefault(require("./nodes/AnimatedProps"));
var _AnimatedEvent = require("./AnimatedEvent");
var _useRefEffect = _interopRequireDefault(require("../Utilities/useRefEffect"));
var _NativeAnimatedHelper = _interopRequireDefault(require("./NativeAnimatedHelper"));
var _react = require("react");
var _useLayoutEffect = _interopRequireDefault(require("../../../modules/useLayoutEffect"));
function useAnimatedProps(props) {
  var _useReducer = (0, _react.useReducer)(function (count) {
      return count + 1;
    }, 0),
    scheduleUpdate = _useReducer[1];
  var onUpdateRef = (0, _react.useRef)(null);
  var node = (0, _react.useMemo)(function () {
    return new _AnimatedProps.default(props, function () {
      return onUpdateRef.current == null ? void 0 : onUpdateRef.current();
    });
  }, [props]);
  useAnimatedPropsLifecycle(node);
  var refEffect = (0, _react.useCallback)(function (instance) {
    node.setNativeView(instance);
    onUpdateRef.current = function () {
      scheduleUpdate();
    };
    var target = getEventTarget(instance);
    var events = [];
    for (var propName in props) {
      var propValue = props[propName];
      if (propValue instanceof _AnimatedEvent.AnimatedEvent && propValue.__isNative) {
        propValue.__attach(target, propName);
        events.push([propName, propValue]);
      }
    }
    return function () {
      onUpdateRef.current = null;
      for (var _i = 0, _events = events; _i < _events.length; _i++) {
        var _events$_i = _events[_i],
          _propName = _events$_i[0],
          _propValue = _events$_i[1];
        _propValue.__detach(target, _propName);
      }
    };
  }, [props, node]);
  var callbackRef = (0, _useRefEffect.default)(refEffect);
  return [reduceAnimatedProps(node), callbackRef];
}
function reduceAnimatedProps(node) {
  return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, node.__getValue()), {}, {
    collapsable: false
  });
}
function useAnimatedPropsLifecycle(node) {
  var prevNodeRef = (0, _react.useRef)(null);
  var isUnmountingRef = (0, _react.useRef)(false);
  (0, _react.useEffect)(function () {
    _NativeAnimatedHelper.default.API.flushQueue();
  });
  (0, _useLayoutEffect.default)(function () {
    isUnmountingRef.current = false;
    return function () {
      isUnmountingRef.current = true;
    };
  }, []);
  (0, _useLayoutEffect.default)(function () {
    node.__attach();
    if (prevNodeRef.current != null) {
      var prevNode = prevNodeRef.current;
      prevNode.__restoreDefaultValues();
      prevNode.__detach();
      prevNodeRef.current = null;
    }
    return function () {
      if (isUnmountingRef.current) {
        node.__detach();
      } else {
        prevNodeRef.current = node;
      }
    };
  }, [node]);
}
function getEventTarget(instance) {
  return typeof instance === 'object' && typeof (instance == null ? void 0 : instance.getScrollableNode) === 'function' ? instance.getScrollableNode() : instance;
}
function isFabricInstance(instance) {
  var _instance$getScrollRe;
  return hasFabricHandle(instance) || hasFabricHandle(instance == null ? void 0 : instance.getNativeScrollRef == null ? void 0 : instance.getNativeScrollRef()) || hasFabricHandle(instance == null ? void 0 : instance.getScrollResponder == null ? void 0 : (_instance$getScrollRe = instance.getScrollResponder()) == null ? void 0 : _instance$getScrollRe.getNativeScrollRef == null ? void 0 : _instance$getScrollRe.getNativeScrollRef());
}
function hasFabricHandle(instance) {
  var _instance$_internalIn, _instance$_internalIn2;
  return (instance == null ? void 0 : (_instance$_internalIn = instance['_internalInstanceHandle']) == null ? void 0 : (_instance$_internalIn2 = _instance$_internalIn.stateNode) == null ? void 0 : _instance$_internalIn2.canonical) != null;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJleHBvcnRzIiwiX19lc01vZHVsZSIsInVzZUFuaW1hdGVkUHJvcHMiLCJfb2JqZWN0U3ByZWFkMiIsIl9BbmltYXRlZFByb3BzIiwiX0FuaW1hdGVkRXZlbnQiLCJfdXNlUmVmRWZmZWN0IiwiX05hdGl2ZUFuaW1hdGVkSGVscGVyIiwiX3JlYWN0IiwiX3VzZUxheW91dEVmZmVjdCIsInByb3BzIiwiX3VzZVJlZHVjZXIiLCJ1c2VSZWR1Y2VyIiwiY291bnQiLCJzY2hlZHVsZVVwZGF0ZSIsIm9uVXBkYXRlUmVmIiwidXNlUmVmIiwibm9kZSIsInVzZU1lbW8iLCJjdXJyZW50IiwidXNlQW5pbWF0ZWRQcm9wc0xpZmVjeWNsZSIsInJlZkVmZmVjdCIsInVzZUNhbGxiYWNrIiwiaW5zdGFuY2UiLCJzZXROYXRpdmVWaWV3IiwidGFyZ2V0IiwiZ2V0RXZlbnRUYXJnZXQiLCJldmVudHMiLCJwcm9wTmFtZSIsInByb3BWYWx1ZSIsIkFuaW1hdGVkRXZlbnQiLCJfX2lzTmF0aXZlIiwiX19hdHRhY2giLCJwdXNoIiwiX2kiLCJfZXZlbnRzIiwibGVuZ3RoIiwiX2V2ZW50cyRfaSIsIl9wcm9wTmFtZSIsIl9wcm9wVmFsdWUiLCJfX2RldGFjaCIsImNhbGxiYWNrUmVmIiwicmVkdWNlQW5pbWF0ZWRQcm9wcyIsIl9fZ2V0VmFsdWUiLCJjb2xsYXBzYWJsZSIsInByZXZOb2RlUmVmIiwiaXNVbm1vdW50aW5nUmVmIiwidXNlRWZmZWN0IiwiQVBJIiwiZmx1c2hRdWV1ZSIsInByZXZOb2RlIiwiX19yZXN0b3JlRGVmYXVsdFZhbHVlcyIsImdldFNjcm9sbGFibGVOb2RlIiwiaXNGYWJyaWNJbnN0YW5jZSIsIl9pbnN0YW5jZSRnZXRTY3JvbGxSZSIsImhhc0ZhYnJpY0hhbmRsZSIsImdldE5hdGl2ZVNjcm9sbFJlZiIsImdldFNjcm9sbFJlc3BvbmRlciIsIl9pbnN0YW5jZSRfaW50ZXJuYWxJbiIsIl9pbnN0YW5jZSRfaW50ZXJuYWxJbjIiLCJzdGF0ZU5vZGUiLCJjYW5vbmljYWwiLCJtb2R1bGUiXSwic291cmNlcyI6WyJ1c2VBbmltYXRlZFByb3BzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBcbiAqIEBmb3JtYXRcbiAqL1xuXG4ndXNlIHN0cmljdCc7XG5cbnZhciBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0ID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0XCIpLmRlZmF1bHQ7XG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdXNlQW5pbWF0ZWRQcm9wcztcbnZhciBfb2JqZWN0U3ByZWFkMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0U3ByZWFkMlwiKSk7XG52YXIgX0FuaW1hdGVkUHJvcHMgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuL25vZGVzL0FuaW1hdGVkUHJvcHNcIikpO1xudmFyIF9BbmltYXRlZEV2ZW50ID0gcmVxdWlyZShcIi4vQW5pbWF0ZWRFdmVudFwiKTtcbnZhciBfdXNlUmVmRWZmZWN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vVXRpbGl0aWVzL3VzZVJlZkVmZmVjdFwiKSk7XG52YXIgX05hdGl2ZUFuaW1hdGVkSGVscGVyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9OYXRpdmVBbmltYXRlZEhlbHBlclwiKSk7XG52YXIgX3JlYWN0ID0gcmVxdWlyZShcInJlYWN0XCIpO1xudmFyIF91c2VMYXlvdXRFZmZlY3QgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi8uLi8uLi9tb2R1bGVzL3VzZUxheW91dEVmZmVjdFwiKSk7XG5mdW5jdGlvbiB1c2VBbmltYXRlZFByb3BzKHByb3BzKSB7XG4gIHZhciBfdXNlUmVkdWNlciA9ICgwLCBfcmVhY3QudXNlUmVkdWNlcikoY291bnQgPT4gY291bnQgKyAxLCAwKSxcbiAgICBzY2hlZHVsZVVwZGF0ZSA9IF91c2VSZWR1Y2VyWzFdO1xuICB2YXIgb25VcGRhdGVSZWYgPSAoMCwgX3JlYWN0LnVzZVJlZikobnVsbCk7XG5cbiAgLy8gVE9ETzogT25seSBpbnZhbGlkYXRlIGBub2RlYCBpZiBhbmltYXRlZCBwcm9wcyBvciBgc3R5bGVgIGNoYW5nZS4gSW4gdGhlXG4gIC8vIHByZXZpb3VzIGltcGxlbWVudGF0aW9uLCB3ZSBwZXJtaXR0ZWQgYHN0eWxlYCB0byBvdmVycmlkZSBwcm9wcyB3aXRoIHRoZVxuICAvLyBzYW1lIG5hbWUgcHJvcGVydHkgbmFtZSBhcyBzdHlsZXMsIHNvIHdlIGNhbiBwcm9iYWJseSBjb250aW51ZSBkb2luZyB0aGF0LlxuICAvLyBUaGUgb3JkZXJpbmcgb2Ygb3RoZXIgcHJvcHMgKnNob3VsZCogbm90IG1hdHRlci5cbiAgdmFyIG5vZGUgPSAoMCwgX3JlYWN0LnVzZU1lbW8pKCgpID0+IG5ldyBfQW5pbWF0ZWRQcm9wcy5kZWZhdWx0KHByb3BzLCAoKSA9PiBvblVwZGF0ZVJlZi5jdXJyZW50ID09IG51bGwgPyB2b2lkIDAgOiBvblVwZGF0ZVJlZi5jdXJyZW50KCkpLCBbcHJvcHNdKTtcbiAgdXNlQW5pbWF0ZWRQcm9wc0xpZmVjeWNsZShub2RlKTtcblxuICAvLyBUT0RPOiBUaGlzIFwiZWZmZWN0XCIgZG9lcyB0aHJlZSB0aGluZ3M6XG4gIC8vXG4gIC8vICAgMSkgQ2FsbCBgc2V0TmF0aXZlVmlld2AuXG4gIC8vICAgMikgVXBkYXRlIGBvblVwZGF0ZVJlZmAuXG4gIC8vICAgMykgVXBkYXRlIGxpc3RlbmVycyBmb3IgYEFuaW1hdGVkRXZlbnRgIHByb3BzLlxuICAvL1xuICAvLyBJZGVhbGx5LCBlYWNoIG9mIHRoZXNlIHdvdWxkIGJlIHNlcGFyYXQgXCJlZmZlY3RzXCIgc28gdGhhdCB0aGV5IGFyZSBub3RcbiAgLy8gdW5uZWNlc3NhcmlseSByZS1ydW4gd2hlbiBpcnJlbGV2YW50IGRlcGVuZGVuY2llcyBjaGFuZ2UuIEZvciBleGFtcGxlLCB3ZVxuICAvLyBzaG91bGQgYmUgYWJsZSB0byBob2lzdCBhbGwgYEFuaW1hdGVkRXZlbnRgIHByb3BzIGFuZCBvbmx5IGRvICMzIGlmIGVpdGhlclxuICAvLyB0aGUgYEFuaW1hdGVkRXZlbnRgIHByb3BzIGNoYW5nZSBvciBgaW5zdGFuY2VgIGNoYW5nZXMuXG4gIC8vXG4gIC8vIEJ1dCB0aGVyZSBpcyBubyB3YXkgdG8gdHJhbnNwYXJlbnRseSBjb21wb3NlIHRocmVlIHNlcGFyYXRlIGNhbGxiYWNrIHJlZnMsXG4gIC8vIHNvIHdlIGp1c3QgY29tYmluZSB0aGVtIGFsbCBpbnRvIG9uZSBmb3Igbm93LlxuICB2YXIgcmVmRWZmZWN0ID0gKDAsIF9yZWFjdC51c2VDYWxsYmFjaykoaW5zdGFuY2UgPT4ge1xuICAgIC8vIE5PVEU6IFRoaXMgbWF5IGJlIGNhbGxlZCBtb3JlIG9mdGVuIHRoYW4gbmVjZXNzYXJ5IChlLmcuIHdoZW4gYHByb3BzYFxuICAgIC8vIGNoYW5nZXMpLCBidXQgYHNldE5hdGl2ZVZpZXdgIGFscmVhZHkgb3B0aW1pemVzIGZvciB0aGF0LlxuICAgIG5vZGUuc2V0TmF0aXZlVmlldyhpbnN0YW5jZSk7XG5cbiAgICAvLyBOT1RFOiBUaGlzIGNhbGxiYWNrIGlzIG9ubHkgdXNlZCBieSB0aGUgSmF2YVNjcmlwdCBhbmltYXRpb24gZHJpdmVyLlxuICAgIG9uVXBkYXRlUmVmLmN1cnJlbnQgPSAoKSA9PiB7XG4gICAgICAvLyBTY2hlZHVsZSBhbiB1cGRhdGUgZm9yIHRoaXMgY29tcG9uZW50IHRvIHVwZGF0ZSBgcmVkdWNlZFByb3BzYCxcbiAgICAgIC8vIGJ1dCBkbyBub3QgY29tcHV0ZSBpdCBpbW1lZGlhdGVseS4gSWYgYSBwYXJlbnQgYWxzbyB1cGRhdGVkLCB3ZVxuICAgICAgLy8gbmVlZCB0byBtZXJnZSB0aG9zZSBuZXcgcHJvcHMgaW4gYmVmb3JlIHVwZGF0aW5nLlxuICAgICAgc2NoZWR1bGVVcGRhdGUoKTtcbiAgICB9O1xuICAgIHZhciB0YXJnZXQgPSBnZXRFdmVudFRhcmdldChpbnN0YW5jZSk7XG4gICAgdmFyIGV2ZW50cyA9IFtdO1xuICAgIGZvciAodmFyIHByb3BOYW1lIGluIHByb3BzKSB7XG4gICAgICB2YXIgcHJvcFZhbHVlID0gcHJvcHNbcHJvcE5hbWVdO1xuICAgICAgaWYgKHByb3BWYWx1ZSBpbnN0YW5jZW9mIF9BbmltYXRlZEV2ZW50LkFuaW1hdGVkRXZlbnQgJiYgcHJvcFZhbHVlLl9faXNOYXRpdmUpIHtcbiAgICAgICAgcHJvcFZhbHVlLl9fYXR0YWNoKHRhcmdldCwgcHJvcE5hbWUpO1xuICAgICAgICBldmVudHMucHVzaChbcHJvcE5hbWUsIHByb3BWYWx1ZV0pO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgb25VcGRhdGVSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICBmb3IgKHZhciBfaSA9IDAsIF9ldmVudHMgPSBldmVudHM7IF9pIDwgX2V2ZW50cy5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgdmFyIF9ldmVudHMkX2kgPSBfZXZlbnRzW19pXSxcbiAgICAgICAgICBfcHJvcE5hbWUgPSBfZXZlbnRzJF9pWzBdLFxuICAgICAgICAgIF9wcm9wVmFsdWUgPSBfZXZlbnRzJF9pWzFdO1xuICAgICAgICBfcHJvcFZhbHVlLl9fZGV0YWNoKHRhcmdldCwgX3Byb3BOYW1lKTtcbiAgICAgIH1cbiAgICB9O1xuICB9LCBbcHJvcHMsIG5vZGVdKTtcbiAgdmFyIGNhbGxiYWNrUmVmID0gKDAsIF91c2VSZWZFZmZlY3QuZGVmYXVsdCkocmVmRWZmZWN0KTtcbiAgcmV0dXJuIFtyZWR1Y2VBbmltYXRlZFByb3BzKG5vZGUpLCBjYWxsYmFja1JlZl07XG59XG5mdW5jdGlvbiByZWR1Y2VBbmltYXRlZFByb3BzKG5vZGUpIHtcbiAgLy8gRm9yY2UgYGNvbGxhcHNhYmxlYCB0byBiZSBmYWxzZSBzbyB0aGF0IHRoZSBuYXRpdmUgdmlldyBpcyBub3QgZmxhdHRlbmVkLlxuICAvLyBGbGF0dGVuZWQgdmlld3MgY2Fubm90IGJlIGFjY3VyYXRlbHkgcmVmZXJlbmNlZCBieSB0aGUgbmF0aXZlIGRyaXZlci5cbiAgcmV0dXJuICgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSgoMCwgX29iamVjdFNwcmVhZDIuZGVmYXVsdCkoe30sIG5vZGUuX19nZXRWYWx1ZSgpKSwge30sIHtcbiAgICBjb2xsYXBzYWJsZTogZmFsc2VcbiAgfSk7XG59XG5cbi8qKlxuICogTWFuYWdlcyB0aGUgbGlmZWN5Y2xlIG9mIHRoZSBzdXBwbGllZCBgQW5pbWF0ZWRQcm9wc2AgYnkgaW52b2tpbmcgYF9fYXR0YWNoYFxuICogYW5kIGBfX2RldGFjaGAuIEhvd2V2ZXIsIHRoaXMgaXMgbW9yZSBjb21wbGljYXRlZCBiZWNhdXNlIGBBbmltYXRlZFByb3BzYFxuICogdXNlcyByZWZlcmVuY2UgY291bnRpbmcgdG8gZGV0ZXJtaW5lIHdoZW4gdG8gcmVjdXJzaXZlbHkgZGV0YWNoIGl0cyBjaGlsZHJlblxuICogbm9kZXMuIFNvIGluIG9yZGVyIHRvIG9wdGltaXplIHRoaXMsIHdlIGF2b2lkIGRldGFjaGluZyB1bnRpbCB0aGUgbmV4dCBhdHRhY2hcbiAqIHVubGVzcyB3ZSBhcmUgdW5tb3VudGluZy5cbiAqL1xuZnVuY3Rpb24gdXNlQW5pbWF0ZWRQcm9wc0xpZmVjeWNsZShub2RlKSB7XG4gIHZhciBwcmV2Tm9kZVJlZiA9ICgwLCBfcmVhY3QudXNlUmVmKShudWxsKTtcbiAgdmFyIGlzVW5tb3VudGluZ1JlZiA9ICgwLCBfcmVhY3QudXNlUmVmKShmYWxzZSk7XG4gICgwLCBfcmVhY3QudXNlRWZmZWN0KSgoKSA9PiB7XG4gICAgLy8gSXQgaXMgb2sgZm9yIG11bHRpcGxlIGNvbXBvbmVudHMgdG8gY2FsbCBgZmx1c2hRdWV1ZWAgYmVjYXVzZSBpdCBub29wc1xuICAgIC8vIGlmIHRoZSBxdWV1ZSBpcyBlbXB0eS4gV2hlbiBtdWx0aXBsZSBhbmltYXRlZCBjb21wb25lbnRzIGFyZSBtb3VudGVkIGF0XG4gICAgLy8gdGhlIHNhbWUgdGltZS4gT25seSBmaXJzdCBjb21wb25lbnQgZmx1c2hlcyB0aGUgcXVldWUgYW5kIHRoZSBvdGhlcnMgd2lsbCBub29wLlxuICAgIF9OYXRpdmVBbmltYXRlZEhlbHBlci5kZWZhdWx0LkFQSS5mbHVzaFF1ZXVlKCk7XG4gIH0pO1xuICAoMCwgX3VzZUxheW91dEVmZmVjdC5kZWZhdWx0KSgoKSA9PiB7XG4gICAgaXNVbm1vdW50aW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgaXNVbm1vdW50aW5nUmVmLmN1cnJlbnQgPSB0cnVlO1xuICAgIH07XG4gIH0sIFtdKTtcbiAgKDAsIF91c2VMYXlvdXRFZmZlY3QuZGVmYXVsdCkoKCkgPT4ge1xuICAgIG5vZGUuX19hdHRhY2goKTtcbiAgICBpZiAocHJldk5vZGVSZWYuY3VycmVudCAhPSBudWxsKSB7XG4gICAgICB2YXIgcHJldk5vZGUgPSBwcmV2Tm9kZVJlZi5jdXJyZW50O1xuICAgICAgLy8gVE9ETzogU3RvcCByZXN0b3JpbmcgZGVmYXVsdCB2YWx1ZXMgKHVubGVzcyBgcmVzZXRgIGlzIGNhbGxlZCkuXG4gICAgICBwcmV2Tm9kZS5fX3Jlc3RvcmVEZWZhdWx0VmFsdWVzKCk7XG4gICAgICBwcmV2Tm9kZS5fX2RldGFjaCgpO1xuICAgICAgcHJldk5vZGVSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAoaXNVbm1vdW50aW5nUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgLy8gTk9URTogRG8gbm90IHJlc3RvcmUgZGVmYXVsdCB2YWx1ZXMgb24gdW5tb3VudCwgc2VlIEQxODE5NzczNS5cbiAgICAgICAgbm9kZS5fX2RldGFjaCgpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcHJldk5vZGVSZWYuY3VycmVudCA9IG5vZGU7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW25vZGVdKTtcbn1cbmZ1bmN0aW9uIGdldEV2ZW50VGFyZ2V0KGluc3RhbmNlKSB7XG4gIHJldHVybiB0eXBlb2YgaW5zdGFuY2UgPT09ICdvYmplY3QnICYmIHR5cGVvZiAoaW5zdGFuY2UgPT0gbnVsbCA/IHZvaWQgMCA6IGluc3RhbmNlLmdldFNjcm9sbGFibGVOb2RlKSA9PT0gJ2Z1bmN0aW9uJyA/XG4gIC8vICRGbG93Rml4TWVbaW5jb21wYXRpYmxlLXVzZV0gLSBMZWdhY3kgaW5zdGFuY2UgYXNzdW1wdGlvbnMuXG4gIGluc3RhbmNlLmdldFNjcm9sbGFibGVOb2RlKCkgOiBpbnN0YW5jZTtcbn1cblxuLy8gJEZsb3dGaXhNZVt1bmNsZWFyLXR5cGVdIC0gTGVnYWN5IGluc3RhbmNlIGFzc3VtcHRpb25zLlxuZnVuY3Rpb24gaXNGYWJyaWNJbnN0YW5jZShpbnN0YW5jZSkge1xuICB2YXIgX2luc3RhbmNlJGdldFNjcm9sbFJlO1xuICByZXR1cm4gaGFzRmFicmljSGFuZGxlKGluc3RhbmNlKSB8fFxuICAvLyBTb21lIGNvbXBvbmVudHMgaGF2ZSBhIHNldE5hdGl2ZVByb3BzIGZ1bmN0aW9uIGJ1dCBhcmVuJ3QgYSBob3N0IGNvbXBvbmVudFxuICAvLyBzdWNoIGFzIGxpc3RzIGxpa2UgRmxhdExpc3QgYW5kIFNlY3Rpb25MaXN0LiBUaGVzZSBzaG91bGQgYWxzbyB1c2VcbiAgLy8gZm9yY2VVcGRhdGUgaW4gRmFicmljIHNpbmNlIHNldE5hdGl2ZVByb3BzIGRvZXNuJ3QgZXhpc3Qgb24gdGhlIHVuZGVybHlpbmdcbiAgLy8gaG9zdCBjb21wb25lbnQuIFRoaXMgY3JhenkgaGFjayBpcyBlc3NlbnRpYWxseSBzcGVjaWFsIGNhc2luZyB0aG9zZSBsaXN0cyBhbmRcbiAgLy8gU2Nyb2xsVmlldyBpdHNlbGYgdG8gdXNlIGZvcmNlVXBkYXRlIGluIEZhYnJpYy5cbiAgLy8gSWYgdGhlc2UgY29tcG9uZW50cyBlbmQgdXAgdXNpbmcgZm9yd2FyZFJlZiB0aGVuIHRoZXNlIGhhY2tzIGNhbiBnbyBhd2F5XG4gIC8vIGFzIGluc3RhbmNlIHdvdWxkIGFjdHVhbGx5IGJlIHRoZSB1bmRlcmx5aW5nIGhvc3QgY29tcG9uZW50IGFuZCB0aGUgYWJvdmUgY2hlY2tcbiAgLy8gd291bGQgYmUgc3VmZmljaWVudC5cbiAgaGFzRmFicmljSGFuZGxlKGluc3RhbmNlID09IG51bGwgPyB2b2lkIDAgOiBpbnN0YW5jZS5nZXROYXRpdmVTY3JvbGxSZWYgPT0gbnVsbCA/IHZvaWQgMCA6IGluc3RhbmNlLmdldE5hdGl2ZVNjcm9sbFJlZigpKSB8fCBoYXNGYWJyaWNIYW5kbGUoaW5zdGFuY2UgPT0gbnVsbCA/IHZvaWQgMCA6IGluc3RhbmNlLmdldFNjcm9sbFJlc3BvbmRlciA9PSBudWxsID8gdm9pZCAwIDogKF9pbnN0YW5jZSRnZXRTY3JvbGxSZSA9IGluc3RhbmNlLmdldFNjcm9sbFJlc3BvbmRlcigpKSA9PSBudWxsID8gdm9pZCAwIDogX2luc3RhbmNlJGdldFNjcm9sbFJlLmdldE5hdGl2ZVNjcm9sbFJlZiA9PSBudWxsID8gdm9pZCAwIDogX2luc3RhbmNlJGdldFNjcm9sbFJlLmdldE5hdGl2ZVNjcm9sbFJlZigpKTtcbn1cblxuLy8gJEZsb3dGaXhNZVt1bmNsZWFyLXR5cGVdIC0gTGVnYWN5IGluc3RhbmNlIGFzc3VtcHRpb25zLlxuZnVuY3Rpb24gaGFzRmFicmljSGFuZGxlKGluc3RhbmNlKSB7XG4gIHZhciBfaW5zdGFuY2UkX2ludGVybmFsSW4sIF9pbnN0YW5jZSRfaW50ZXJuYWxJbjI7XG4gIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBkb3Qtbm90YXRpb25cbiAgcmV0dXJuIChpbnN0YW5jZSA9PSBudWxsID8gdm9pZCAwIDogKF9pbnN0YW5jZSRfaW50ZXJuYWxJbiA9IGluc3RhbmNlWydfaW50ZXJuYWxJbnN0YW5jZUhhbmRsZSddKSA9PSBudWxsID8gdm9pZCAwIDogKF9pbnN0YW5jZSRfaW50ZXJuYWxJbjIgPSBfaW5zdGFuY2UkX2ludGVybmFsSW4uc3RhdGVOb2RlKSA9PSBudWxsID8gdm9pZCAwIDogX2luc3RhbmNlJF9pbnRlcm5hbEluMi5jYW5vbmljYWwpICE9IG51bGw7XG59XG5tb2R1bGUuZXhwb3J0cyA9IGV4cG9ydHMuZGVmYXVsdDsiXSwibWFwcGluZ3MiOiJBQVVBLFlBQVk7O0FBRVosSUFBSUEsc0JBQXNCLEdBQUdDLE9BQU8sQ0FBQyw4Q0FBOEMsQ0FBQyxDQUFDQyxPQUFPO0FBQzVGQyxPQUFPLENBQUNDLFVBQVUsR0FBRyxJQUFJO0FBQ3pCRCxPQUFPLENBQUNELE9BQU8sR0FBR0csZ0JBQWdCO0FBQ2xDLElBQUlDLGNBQWMsR0FBR04sc0JBQXNCLENBQUNDLE9BQU8sQ0FBQyxzQ0FBc0MsQ0FBQyxDQUFDO0FBQzVGLElBQUlNLGNBQWMsR0FBR1Asc0JBQXNCLENBQUNDLE9BQU8sd0JBQXdCLENBQUMsQ0FBQztBQUM3RSxJQUFJTyxjQUFjLEdBQUdQLE9BQU8sa0JBQWtCLENBQUM7QUFDL0MsSUFBSVEsYUFBYSxHQUFHVCxzQkFBc0IsQ0FBQ0MsT0FBTyw0QkFBNEIsQ0FBQyxDQUFDO0FBQ2hGLElBQUlTLHFCQUFxQixHQUFHVixzQkFBc0IsQ0FBQ0MsT0FBTyx5QkFBeUIsQ0FBQyxDQUFDO0FBQ3JGLElBQUlVLE1BQU0sR0FBR1YsT0FBTyxDQUFDLE9BQU8sQ0FBQztBQUM3QixJQUFJVyxnQkFBZ0IsR0FBR1osc0JBQXNCLENBQUNDLE9BQU8sbUNBQW1DLENBQUMsQ0FBQztBQUMxRixTQUFTSSxnQkFBZ0JBLENBQUNRLEtBQUssRUFBRTtFQUMvQixJQUFJQyxXQUFXLEdBQUcsQ0FBQyxDQUFDLEVBQUVILE1BQU0sQ0FBQ0ksVUFBVSxFQUFFLFVBQUFDLEtBQUs7TUFBQSxPQUFJQSxLQUFLLEdBQUcsQ0FBQztJQUFBLEdBQUUsQ0FBQyxDQUFDO0lBQzdEQyxjQUFjLEdBQUdILFdBQVcsQ0FBQyxDQUFDLENBQUM7RUFDakMsSUFBSUksV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFUCxNQUFNLENBQUNRLE1BQU0sRUFBRSxJQUFJLENBQUM7RUFNMUMsSUFBSUMsSUFBSSxHQUFHLENBQUMsQ0FBQyxFQUFFVCxNQUFNLENBQUNVLE9BQU8sRUFBRTtJQUFBLE9BQU0sSUFBSWQsY0FBYyxDQUFDTCxPQUFPLENBQUNXLEtBQUssRUFBRTtNQUFBLE9BQU1LLFdBQVcsQ0FBQ0ksT0FBTyxJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR0osV0FBVyxDQUFDSSxPQUFPLENBQUMsQ0FBQztJQUFBLEVBQUM7RUFBQSxHQUFFLENBQUNULEtBQUssQ0FBQyxDQUFDO0VBQ3BKVSx5QkFBeUIsQ0FBQ0gsSUFBSSxDQUFDO0VBZS9CLElBQUlJLFNBQVMsR0FBRyxDQUFDLENBQUMsRUFBRWIsTUFBTSxDQUFDYyxXQUFXLEVBQUUsVUFBQUMsUUFBUSxFQUFJO0lBR2xETixJQUFJLENBQUNPLGFBQWEsQ0FBQ0QsUUFBUSxDQUFDO0lBRzVCUixXQUFXLENBQUNJLE9BQU8sR0FBRyxZQUFNO01BSTFCTCxjQUFjLENBQUMsQ0FBQztJQUNsQixDQUFDO0lBQ0QsSUFBSVcsTUFBTSxHQUFHQyxjQUFjLENBQUNILFFBQVEsQ0FBQztJQUNyQyxJQUFJSSxNQUFNLEdBQUcsRUFBRTtJQUNmLEtBQUssSUFBSUMsUUFBUSxJQUFJbEIsS0FBSyxFQUFFO01BQzFCLElBQUltQixTQUFTLEdBQUduQixLQUFLLENBQUNrQixRQUFRLENBQUM7TUFDL0IsSUFBSUMsU0FBUyxZQUFZeEIsY0FBYyxDQUFDeUIsYUFBYSxJQUFJRCxTQUFTLENBQUNFLFVBQVUsRUFBRTtRQUM3RUYsU0FBUyxDQUFDRyxRQUFRLENBQUNQLE1BQU0sRUFBRUcsUUFBUSxDQUFDO1FBQ3BDRCxNQUFNLENBQUNNLElBQUksQ0FBQyxDQUFDTCxRQUFRLEVBQUVDLFNBQVMsQ0FBQyxDQUFDO01BQ3BDO0lBQ0Y7SUFDQSxPQUFPLFlBQU07TUFDWGQsV0FBVyxDQUFDSSxPQUFPLEdBQUcsSUFBSTtNQUMxQixLQUFLLElBQUllLEVBQUUsR0FBRyxDQUFDLEVBQUVDLE9BQU8sR0FBR1IsTUFBTSxFQUFFTyxFQUFFLEdBQUdDLE9BQU8sQ0FBQ0MsTUFBTSxFQUFFRixFQUFFLEVBQUUsRUFBRTtRQUM1RCxJQUFJRyxVQUFVLEdBQUdGLE9BQU8sQ0FBQ0QsRUFBRSxDQUFDO1VBQzFCSSxTQUFTLEdBQUdELFVBQVUsQ0FBQyxDQUFDLENBQUM7VUFDekJFLFVBQVUsR0FBR0YsVUFBVSxDQUFDLENBQUMsQ0FBQztRQUM1QkUsVUFBVSxDQUFDQyxRQUFRLENBQUNmLE1BQU0sRUFBRWEsU0FBUyxDQUFDO01BQ3hDO0lBQ0YsQ0FBQztFQUNILENBQUMsRUFBRSxDQUFDNUIsS0FBSyxFQUFFTyxJQUFJLENBQUMsQ0FBQztFQUNqQixJQUFJd0IsV0FBVyxHQUFHLENBQUMsQ0FBQyxFQUFFbkMsYUFBYSxDQUFDUCxPQUFPLEVBQUVzQixTQUFTLENBQUM7RUFDdkQsT0FBTyxDQUFDcUIsbUJBQW1CLENBQUN6QixJQUFJLENBQUMsRUFBRXdCLFdBQVcsQ0FBQztBQUNqRDtBQUNBLFNBQVNDLG1CQUFtQkEsQ0FBQ3pCLElBQUksRUFBRTtFQUdqQyxPQUFPLENBQUMsQ0FBQyxFQUFFZCxjQUFjLENBQUNKLE9BQU8sRUFBRSxDQUFDLENBQUMsRUFBRUksY0FBYyxDQUFDSixPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVrQixJQUFJLENBQUMwQixVQUFVLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7SUFDekZDLFdBQVcsRUFBRTtFQUNmLENBQUMsQ0FBQztBQUNKO0FBU0EsU0FBU3hCLHlCQUF5QkEsQ0FBQ0gsSUFBSSxFQUFFO0VBQ3ZDLElBQUk0QixXQUFXLEdBQUcsQ0FBQyxDQUFDLEVBQUVyQyxNQUFNLENBQUNRLE1BQU0sRUFBRSxJQUFJLENBQUM7RUFDMUMsSUFBSThCLGVBQWUsR0FBRyxDQUFDLENBQUMsRUFBRXRDLE1BQU0sQ0FBQ1EsTUFBTSxFQUFFLEtBQUssQ0FBQztFQUMvQyxDQUFDLENBQUMsRUFBRVIsTUFBTSxDQUFDdUMsU0FBUyxFQUFFLFlBQU07SUFJMUJ4QyxxQkFBcUIsQ0FBQ1IsT0FBTyxDQUFDaUQsR0FBRyxDQUFDQyxVQUFVLENBQUMsQ0FBQztFQUNoRCxDQUFDLENBQUM7RUFDRixDQUFDLENBQUMsRUFBRXhDLGdCQUFnQixDQUFDVixPQUFPLEVBQUUsWUFBTTtJQUNsQytDLGVBQWUsQ0FBQzNCLE9BQU8sR0FBRyxLQUFLO0lBQy9CLE9BQU8sWUFBTTtNQUNYMkIsZUFBZSxDQUFDM0IsT0FBTyxHQUFHLElBQUk7SUFDaEMsQ0FBQztFQUNILENBQUMsRUFBRSxFQUFFLENBQUM7RUFDTixDQUFDLENBQUMsRUFBRVYsZ0JBQWdCLENBQUNWLE9BQU8sRUFBRSxZQUFNO0lBQ2xDa0IsSUFBSSxDQUFDZSxRQUFRLENBQUMsQ0FBQztJQUNmLElBQUlhLFdBQVcsQ0FBQzFCLE9BQU8sSUFBSSxJQUFJLEVBQUU7TUFDL0IsSUFBSStCLFFBQVEsR0FBR0wsV0FBVyxDQUFDMUIsT0FBTztNQUVsQytCLFFBQVEsQ0FBQ0Msc0JBQXNCLENBQUMsQ0FBQztNQUNqQ0QsUUFBUSxDQUFDVixRQUFRLENBQUMsQ0FBQztNQUNuQkssV0FBVyxDQUFDMUIsT0FBTyxHQUFHLElBQUk7SUFDNUI7SUFDQSxPQUFPLFlBQU07TUFDWCxJQUFJMkIsZUFBZSxDQUFDM0IsT0FBTyxFQUFFO1FBRTNCRixJQUFJLENBQUN1QixRQUFRLENBQUMsQ0FBQztNQUNqQixDQUFDLE1BQU07UUFDTEssV0FBVyxDQUFDMUIsT0FBTyxHQUFHRixJQUFJO01BQzVCO0lBQ0YsQ0FBQztFQUNILENBQUMsRUFBRSxDQUFDQSxJQUFJLENBQUMsQ0FBQztBQUNaO0FBQ0EsU0FBU1MsY0FBY0EsQ0FBQ0gsUUFBUSxFQUFFO0VBQ2hDLE9BQU8sT0FBT0EsUUFBUSxLQUFLLFFBQVEsSUFBSSxRQUFRQSxRQUFRLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHQSxRQUFRLENBQUM2QixpQkFBaUIsQ0FBQyxLQUFLLFVBQVUsR0FFckg3QixRQUFRLENBQUM2QixpQkFBaUIsQ0FBQyxDQUFDLEdBQUc3QixRQUFRO0FBQ3pDO0FBR0EsU0FBUzhCLGdCQUFnQkEsQ0FBQzlCLFFBQVEsRUFBRTtFQUNsQyxJQUFJK0IscUJBQXFCO0VBQ3pCLE9BQU9DLGVBQWUsQ0FBQ2hDLFFBQVEsQ0FBQyxJQVNoQ2dDLGVBQWUsQ0FBQ2hDLFFBQVEsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdBLFFBQVEsQ0FBQ2lDLGtCQUFrQixJQUFJLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR2pDLFFBQVEsQ0FBQ2lDLGtCQUFrQixDQUFDLENBQUMsQ0FBQyxJQUFJRCxlQUFlLENBQUNoQyxRQUFRLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHQSxRQUFRLENBQUNrQyxrQkFBa0IsSUFBSSxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUcsQ0FBQ0gscUJBQXFCLEdBQUcvQixRQUFRLENBQUNrQyxrQkFBa0IsQ0FBQyxDQUFDLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHSCxxQkFBcUIsQ0FBQ0Usa0JBQWtCLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHRixxQkFBcUIsQ0FBQ0Usa0JBQWtCLENBQUMsQ0FBQyxDQUFDO0FBQzVZO0FBR0EsU0FBU0QsZUFBZUEsQ0FBQ2hDLFFBQVEsRUFBRTtFQUNqQyxJQUFJbUMscUJBQXFCLEVBQUVDLHNCQUFzQjtFQUVqRCxPQUFPLENBQUNwQyxRQUFRLElBQUksSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHLENBQUNtQyxxQkFBcUIsR0FBR25DLFFBQVEsQ0FBQyx5QkFBeUIsQ0FBQyxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBRyxDQUFDb0Msc0JBQXNCLEdBQUdELHFCQUFxQixDQUFDRSxTQUFTLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHRCxzQkFBc0IsQ0FBQ0UsU0FBUyxLQUFLLElBQUk7QUFDOU87QUFDQUMsTUFBTSxDQUFDOUQsT0FBTyxHQUFHQSxPQUFPLENBQUNELE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=