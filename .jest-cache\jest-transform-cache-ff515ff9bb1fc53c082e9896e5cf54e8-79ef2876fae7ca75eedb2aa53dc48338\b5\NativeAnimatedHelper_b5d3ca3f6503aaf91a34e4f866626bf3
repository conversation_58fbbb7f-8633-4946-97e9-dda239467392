709bcd9ea5690d43ea262b2f25529cf0
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.API = void 0;
exports.addWhitelistedInterpolationParam = addWhitelistedInterpolationParam;
exports.addWhitelistedStyleProp = addWhitelistedStyleProp;
exports.addWhitelistedTransformProp = addWhitelistedTransformProp;
exports.assertNativeAnimatedModule = assertNativeAnimatedModule;
exports.default = void 0;
exports.generateNewAnimationId = generateNewAnimationId;
exports.generateNewNodeTag = generateNewNodeTag;
exports.isSupportedColorStyleProp = isSupportedColorStyleProp;
exports.isSupportedInterpolationParam = isSupportedInterpolationParam;
exports.isSupportedStyleProp = isSupportedStyleProp;
exports.isSupportedTransformProp = isSupportedTransformProp;
exports.shouldUseNativeDriver = shouldUseNativeDriver;
exports.transformDataType = transformDataType;
exports.validateInterpolation = validateInterpolation;
exports.validateStyles = validateStyles;
exports.validateTransform = validateTransform;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _NativeAnimatedModule = _interopRequireDefault(require("./NativeAnimatedModule"));
var _NativeAnimatedTurboModule = _interopRequireDefault(require("./NativeAnimatedTurboModule"));
var _NativeEventEmitter = _interopRequireDefault(require("../EventEmitter/NativeEventEmitter"));
var _Platform = _interopRequireDefault(require("../Utilities/Platform"));
var _ReactNativeFeatureFlags = _interopRequireDefault(require("../ReactNative/ReactNativeFeatureFlags"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _RCTDeviceEventEmitter = _interopRequireDefault(require("../EventEmitter/RCTDeviceEventEmitter"));
var NativeAnimatedModule = _Platform.default.OS === 'ios' && global.RN$Bridgeless === true ? _NativeAnimatedTurboModule.default : _NativeAnimatedModule.default;
var __nativeAnimatedNodeTagCount = 1;
var __nativeAnimationIdCount = 1;
var nativeEventEmitter;
var waitingForQueuedOperations = new Set();
var queueOperations = false;
var queue = [];
var singleOpQueue = [];
var useSingleOpBatching = false;
_Platform.default.OS === 'android' && !!(NativeAnimatedModule != null && NativeAnimatedModule.queueAndExecuteBatchedOperations) && _ReactNativeFeatureFlags.default.animatedShouldUseSingleOp();
var flushQueueTimeout = null;
var eventListenerGetValueCallbacks = {};
var eventListenerAnimationFinishedCallbacks = {};
var globalEventEmitterGetValueListener = null;
var globalEventEmitterAnimationFinishedListener = null;
var nativeOps = useSingleOpBatching ? function () {
  var apis = ['createAnimatedNode', 'updateAnimatedNodeConfig', 'getValue', 'startListeningToAnimatedNodeValue', 'stopListeningToAnimatedNodeValue', 'connectAnimatedNodes', 'disconnectAnimatedNodes', 'startAnimatingNode', 'stopAnimation', 'setAnimatedNodeValue', 'setAnimatedNodeOffset', 'flattenAnimatedNodeOffset', 'extractAnimatedNodeOffset', 'connectAnimatedNodeToView', 'disconnectAnimatedNodeFromView', 'restoreDefaultValues', 'dropAnimatedNode', 'addAnimatedEventToView', 'removeAnimatedEventFromView', 'addListener', 'removeListener'];
  return apis.reduce(function (acc, functionName, i) {
    acc[functionName] = i + 1;
    return acc;
  }, {});
}() : NativeAnimatedModule;
var API = exports.API = {
  getValue: function getValue(tag, saveValueCallback) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    if (useSingleOpBatching) {
      if (saveValueCallback) {
        eventListenerGetValueCallbacks[tag] = saveValueCallback;
      }
      API.queueOperation(nativeOps.getValue, tag);
    } else {
      API.queueOperation(nativeOps.getValue, tag, saveValueCallback);
    }
  },
  setWaitingForIdentifier: function setWaitingForIdentifier(id) {
    waitingForQueuedOperations.add(id);
    queueOperations = true;
    if (_ReactNativeFeatureFlags.default.animatedShouldDebounceQueueFlush() && flushQueueTimeout) {
      clearTimeout(flushQueueTimeout);
    }
  },
  unsetWaitingForIdentifier: function unsetWaitingForIdentifier(id) {
    waitingForQueuedOperations.delete(id);
    if (waitingForQueuedOperations.size === 0) {
      queueOperations = false;
      API.disableQueue();
    }
  },
  disableQueue: function disableQueue() {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    if (_ReactNativeFeatureFlags.default.animatedShouldDebounceQueueFlush()) {
      var prevTimeout = flushQueueTimeout;
      clearImmediate(prevTimeout);
      flushQueueTimeout = setImmediate(API.flushQueue);
    } else {
      API.flushQueue();
    }
  },
  flushQueue: function flushQueue() {},
  queueOperation: function queueOperation(fn) {
    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
      args[_key - 1] = arguments[_key];
    }
    if (useSingleOpBatching) {
      singleOpQueue.push.apply(singleOpQueue, [fn].concat(args));
      return;
    }
    if (queueOperations || queue.length !== 0) {
      queue.push(function () {
        return fn.apply(void 0, args);
      });
    } else {
      fn.apply(void 0, args);
    }
  },
  createAnimatedNode: function createAnimatedNode(tag, config) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.createAnimatedNode, tag, config);
  },
  updateAnimatedNodeConfig: function updateAnimatedNodeConfig(tag, config) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
  },
  startListeningToAnimatedNodeValue: function startListeningToAnimatedNodeValue(tag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.startListeningToAnimatedNodeValue, tag);
  },
  stopListeningToAnimatedNodeValue: function stopListeningToAnimatedNodeValue(tag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.stopListeningToAnimatedNodeValue, tag);
  },
  connectAnimatedNodes: function connectAnimatedNodes(parentTag, childTag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.connectAnimatedNodes, parentTag, childTag);
  },
  disconnectAnimatedNodes: function disconnectAnimatedNodes(parentTag, childTag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.disconnectAnimatedNodes, parentTag, childTag);
  },
  startAnimatingNode: function startAnimatingNode(animationId, nodeTag, config, endCallback) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    if (useSingleOpBatching) {
      if (endCallback) {
        eventListenerAnimationFinishedCallbacks[animationId] = endCallback;
      }
      API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config);
    } else {
      API.queueOperation(nativeOps.startAnimatingNode, animationId, nodeTag, config, endCallback);
    }
  },
  stopAnimation: function stopAnimation(animationId) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.stopAnimation, animationId);
  },
  setAnimatedNodeValue: function setAnimatedNodeValue(nodeTag, value) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.setAnimatedNodeValue, nodeTag, value);
  },
  setAnimatedNodeOffset: function setAnimatedNodeOffset(nodeTag, offset) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.setAnimatedNodeOffset, nodeTag, offset);
  },
  flattenAnimatedNodeOffset: function flattenAnimatedNodeOffset(nodeTag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.flattenAnimatedNodeOffset, nodeTag);
  },
  extractAnimatedNodeOffset: function extractAnimatedNodeOffset(nodeTag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.extractAnimatedNodeOffset, nodeTag);
  },
  connectAnimatedNodeToView: function connectAnimatedNodeToView(nodeTag, viewTag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.connectAnimatedNodeToView, nodeTag, viewTag);
  },
  disconnectAnimatedNodeFromView: function disconnectAnimatedNodeFromView(nodeTag, viewTag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.disconnectAnimatedNodeFromView, nodeTag, viewTag);
  },
  restoreDefaultValues: function restoreDefaultValues(nodeTag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    if (nativeOps.restoreDefaultValues != null) {
      API.queueOperation(nativeOps.restoreDefaultValues, nodeTag);
    }
  },
  dropAnimatedNode: function dropAnimatedNode(tag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.dropAnimatedNode, tag);
  },
  addAnimatedEventToView: function addAnimatedEventToView(viewTag, eventName, eventMapping) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.addAnimatedEventToView, viewTag, eventName, eventMapping);
  },
  removeAnimatedEventFromView: function removeAnimatedEventFromView(viewTag, eventName, animatedNodeTag) {
    (0, _invariant.default)(nativeOps, 'Native animated module is not available');
    API.queueOperation(nativeOps.removeAnimatedEventFromView, viewTag, eventName, animatedNodeTag);
  }
};
function setupGlobalEventEmitterListeners() {
  globalEventEmitterGetValueListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleGetValue', function (params) {
    var tag = params.tag;
    var callback = eventListenerGetValueCallbacks[tag];
    if (!callback) {
      return;
    }
    callback(params.value);
    delete eventListenerGetValueCallbacks[tag];
  });
  globalEventEmitterAnimationFinishedListener = _RCTDeviceEventEmitter.default.addListener('onNativeAnimatedModuleAnimationFinished', function (params) {
    var animationId = params.animationId;
    var callback = eventListenerAnimationFinishedCallbacks[animationId];
    if (!callback) {
      return;
    }
    callback(params);
    delete eventListenerAnimationFinishedCallbacks[animationId];
  });
}
var SUPPORTED_COLOR_STYLES = {
  backgroundColor: true,
  borderBottomColor: true,
  borderColor: true,
  borderEndColor: true,
  borderLeftColor: true,
  borderRightColor: true,
  borderStartColor: true,
  borderTopColor: true,
  color: true,
  tintColor: true
};
var SUPPORTED_STYLES = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, SUPPORTED_COLOR_STYLES), {}, {
  borderBottomEndRadius: true,
  borderBottomLeftRadius: true,
  borderBottomRightRadius: true,
  borderBottomStartRadius: true,
  borderRadius: true,
  borderTopEndRadius: true,
  borderTopLeftRadius: true,
  borderTopRightRadius: true,
  borderTopStartRadius: true,
  elevation: true,
  opacity: true,
  transform: true,
  zIndex: true,
  shadowOpacity: true,
  shadowRadius: true,
  scaleX: true,
  scaleY: true,
  translateX: true,
  translateY: true
});
var SUPPORTED_TRANSFORMS = {
  translateX: true,
  translateY: true,
  scale: true,
  scaleX: true,
  scaleY: true,
  rotate: true,
  rotateX: true,
  rotateY: true,
  rotateZ: true,
  perspective: true
};
var SUPPORTED_INTERPOLATION_PARAMS = {
  inputRange: true,
  outputRange: true,
  extrapolate: true,
  extrapolateRight: true,
  extrapolateLeft: true
};
function addWhitelistedStyleProp(prop) {
  SUPPORTED_STYLES[prop] = true;
}
function addWhitelistedTransformProp(prop) {
  SUPPORTED_TRANSFORMS[prop] = true;
}
function addWhitelistedInterpolationParam(param) {
  SUPPORTED_INTERPOLATION_PARAMS[param] = true;
}
function isSupportedColorStyleProp(prop) {
  return SUPPORTED_COLOR_STYLES.hasOwnProperty(prop);
}
function isSupportedStyleProp(prop) {
  return SUPPORTED_STYLES.hasOwnProperty(prop);
}
function isSupportedTransformProp(prop) {
  return SUPPORTED_TRANSFORMS.hasOwnProperty(prop);
}
function isSupportedInterpolationParam(param) {
  return SUPPORTED_INTERPOLATION_PARAMS.hasOwnProperty(param);
}
function validateTransform(configs) {
  configs.forEach(function (config) {
    if (!isSupportedTransformProp(config.property)) {
      throw new Error("Property '" + config.property + "' is not supported by native animated module");
    }
  });
}
function validateStyles(styles) {
  for (var _key2 in styles) {
    if (!isSupportedStyleProp(_key2)) {
      throw new Error("Style property '" + _key2 + "' is not supported by native animated module");
    }
  }
}
function validateInterpolation(config) {
  for (var _key3 in config) {
    if (!isSupportedInterpolationParam(_key3)) {
      throw new Error("Interpolation property '" + _key3 + "' is not supported by native animated module");
    }
  }
}
function generateNewNodeTag() {
  return __nativeAnimatedNodeTagCount++;
}
function generateNewAnimationId() {
  return __nativeAnimationIdCount++;
}
function assertNativeAnimatedModule() {
  (0, _invariant.default)(NativeAnimatedModule, 'Native animated module is not available');
}
var _warnedMissingNativeAnimated = false;
function shouldUseNativeDriver(config) {
  if (config.useNativeDriver == null) {
    console.warn('Animated: `useNativeDriver` was not specified. This is a required ' + 'option and must be explicitly set to `true` or `false`');
  }
  if (config.useNativeDriver === true && !NativeAnimatedModule) {
    if (!_warnedMissingNativeAnimated) {
      console.warn('Animated: `useNativeDriver` is not supported because the native ' + 'animated module is missing. Falling back to JS-based animation. To ' + 'resolve this, add `RCTAnimation` module to this app, or remove ' + '`useNativeDriver`. ' + 'Make sure to run `bundle exec pod install` first. Read more about autolinking: https://github.com/react-native-community/cli/blob/master/docs/autolinking.md');
      _warnedMissingNativeAnimated = true;
    }
    return false;
  }
  return config.useNativeDriver || false;
}
function transformDataType(value) {
  if (typeof value !== 'string') {
    return value;
  }
  if (/deg$/.test(value)) {
    var degrees = parseFloat(value) || 0;
    var radians = degrees * Math.PI / 180.0;
    return radians;
  } else {
    return value;
  }
}
var _default = exports.default = {
  API: API,
  isSupportedColorStyleProp: isSupportedColorStyleProp,
  isSupportedStyleProp: isSupportedStyleProp,
  isSupportedTransformProp: isSupportedTransformProp,
  isSupportedInterpolationParam: isSupportedInterpolationParam,
  addWhitelistedStyleProp: addWhitelistedStyleProp,
  addWhitelistedTransformProp: addWhitelistedTransformProp,
  addWhitelistedInterpolationParam: addWhitelistedInterpolationParam,
  validateStyles: validateStyles,
  validateTransform: validateTransform,
  validateInterpolation: validateInterpolation,
  generateNewNodeTag: generateNewNodeTag,
  generateNewAnimationId: generateNewAnimationId,
  assertNativeAnimatedModule: assertNativeAnimatedModule,
  shouldUseNativeDriver: shouldUseNativeDriver,
  transformDataType: transformDataType,
  get nativeEventEmitter() {
    if (!nativeEventEmitter) {
      nativeEventEmitter = new _NativeEventEmitter.default(_Platform.default.OS !== 'ios' ? null : NativeAnimatedModule);
    }
    return nativeEventEmitter;
  }
};
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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