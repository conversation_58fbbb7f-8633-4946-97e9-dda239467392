/**
 * Migration Demo Page
 * Showcases the complete migration from Supabase to Firebase and OpenAI to DeepSeek
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';

import MigrationDemo from '@/components/MigrationDemo';
import ErrorBoundary from '@/components/ErrorBoundary';

export default function MigrationDemoPage() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Migration Demo</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>🔄 Complete Migration</Text>
          <Text style={styles.infoText}>
            This demo showcases the complete migration from the previous stack to modern alternatives:
          </Text>
          
          <View style={styles.migrationList}>
            <View style={styles.migrationItem}>
              <Text style={styles.migrationFrom}>Supabase Auth</Text>
              <Text style={styles.migrationArrow}>→</Text>
              <Text style={styles.migrationTo}>Firebase Auth</Text>
            </View>

            <View style={styles.migrationItem}>
              <Text style={styles.migrationFrom}>Supabase Database</Text>
              <Text style={styles.migrationArrow}>→</Text>
              <Text style={styles.migrationTo}>Firebase Firestore</Text>
            </View>

            <View style={styles.migrationItem}>
              <Text style={styles.migrationFrom}>OpenAI API</Text>
              <Text style={styles.migrationArrow}>→</Text>
              <Text style={styles.migrationTo}>DeepSeek API</Text>
            </View>

            <View style={styles.migrationItem}>
              <Text style={styles.migrationFrom}>Replicate API</Text>
              <Text style={styles.migrationArrow}>→</Text>
              <Text style={styles.migrationTo}>MediaPipe</Text>
            </View>
          </View>
        </View>

        <ErrorBoundary
          showDetails={__DEV__}
          onError={(error, errorInfo) => {
            console.log('Migration demo error caught:', error.message);
          }}
        >
          <MigrationDemo />
        </ErrorBoundary>

        <View style={styles.implementationSection}>
          <Text style={styles.implementationTitle}>📋 Implementation Details</Text>
          
          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>1. Firebase Authentication</Text>
            <Text style={styles.detailText}>
              • Email/password authentication{'\n'}
              • Google and Facebook social login{'\n'}
              • User profile management in Firestore{'\n'}
              • Real-time auth state management
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>2. Firebase Firestore</Text>
            <Text style={styles.detailText}>
              • NoSQL document database{'\n'}
              • Real-time data synchronization{'\n'}
              • Offline support and caching{'\n'}
              • Scalable and serverless
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>3. DeepSeek API</Text>
            <Text style={styles.detailText}>
              • Advanced language model for tennis coaching{'\n'}
              • Cost-effective alternative to OpenAI{'\n'}
              • Specialized models for different use cases{'\n'}
              • Compatible API format for easy migration
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>4. MediaPipe Integration</Text>
            <Text style={styles.detailText}>
              • Local pose detection and analysis{'\n'}
              • Real-time video processing{'\n'}
              • No cloud dependencies{'\n'}
              • Privacy-focused approach
            </Text>
          </View>
        </View>

        <View style={styles.benefitsSection}>
          <Text style={styles.benefitsTitle}>✨ Migration Benefits</Text>
          
          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>🔥</Text>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Firebase Ecosystem</Text>
              <Text style={styles.benefitDescription}>
                Unified Google Cloud platform with better integration and scaling
              </Text>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>💰</Text>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Cost Optimization</Text>
              <Text style={styles.benefitDescription}>
                DeepSeek offers competitive pricing compared to OpenAI
              </Text>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>🔒</Text>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Privacy & Performance</Text>
              <Text style={styles.benefitDescription}>
                Local MediaPipe processing ensures data privacy and faster analysis
              </Text>
            </View>
          </View>

          <View style={styles.benefitItem}>
            <Text style={styles.benefitIcon}>⚡</Text>
            <View style={styles.benefitContent}>
              <Text style={styles.benefitTitle}>Real-time Features</Text>
              <Text style={styles.benefitDescription}>
                Firebase real-time database enables live updates and collaboration
              </Text>
            </View>
          </View>
        </View>

        <View style={styles.configSection}>
          <Text style={styles.configTitle}>⚙️ Environment Configuration</Text>
          <View style={styles.configBlock}>
            <Text style={styles.configText}>
{`# Firebase Configuration
FIREBASE_API_KEY=your-firebase-api-key
FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_STORAGE_BUCKET=your-project.appspot.com
FIREBASE_MESSAGING_SENDER_ID=your-messaging-sender-id
FIREBASE_APP_ID=1:your-app-id:web:your-web-app-id

# DeepSeek API Configuration
DEEPSEEK_API_KEY=your-deepseek-api-key
EXPO_PUBLIC_DEEPSEEK_API_KEY=your-deepseek-api-key

# MediaPipe Configuration
EXPO_PUBLIC_MEDIAPIPE_MODEL_URL=https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task
EXPO_PUBLIC_ENABLE_MEDIAPIPE=true`}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  infoSection: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  migrationList: {
    gap: 12,
  },
  migrationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  migrationFrom: {
    flex: 1,
    fontSize: 14,
    color: '#dc3545',
    fontWeight: '500',
  },
  migrationArrow: {
    fontSize: 16,
    color: '#6c757d',
    marginHorizontal: 10,
  },
  migrationTo: {
    flex: 1,
    fontSize: 14,
    color: '#28a745',
    fontWeight: '500',
    textAlign: 'right',
  },
  implementationSection: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  implementationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  detailSection: {
    marginBottom: 20,
  },
  detailSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  benefitsSection: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  benefitIcon: {
    fontSize: 24,
    marginRight: 12,
    marginTop: 2,
  },
  benefitContent: {
    flex: 1,
  },
  benefitTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  benefitDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  configSection: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  configBlock: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  configText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#333',
    lineHeight: 18,
  },
});
