530a5955b65003e832cb17f03a646294
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _View = _interopRequireDefault(require("../../../exports/View"));
var _StyleSheet = _interopRequireDefault(require("../../../exports/StyleSheet"));
var _VirtualizedListContext = require("./VirtualizedListContext.js");
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var React = _interopRequireWildcard(require("react"));
var CellRenderer = function (_React$Component) {
  function CellRenderer() {
    var _this;
    (0, _classCallCheck2.default)(this, CellRenderer);
    _this = _callSuper(this, CellRenderer, arguments);
    _this.state = {
      separatorProps: {
        highlighted: false,
        leadingItem: _this.props.item
      }
    };
    _this._separators = {
      highlight: function highlight() {
        var _this$props = _this.props,
          cellKey = _this$props.cellKey,
          prevCellKey = _this$props.prevCellKey;
        _this.props.onUpdateSeparators([cellKey, prevCellKey], {
          highlighted: true
        });
      },
      unhighlight: function unhighlight() {
        var _this$props2 = _this.props,
          cellKey = _this$props2.cellKey,
          prevCellKey = _this$props2.prevCellKey;
        _this.props.onUpdateSeparators([cellKey, prevCellKey], {
          highlighted: false
        });
      },
      updateProps: function updateProps(select, newProps) {
        var _this$props3 = _this.props,
          cellKey = _this$props3.cellKey,
          prevCellKey = _this$props3.prevCellKey;
        _this.props.onUpdateSeparators([select === 'leading' ? prevCellKey : cellKey], newProps);
      }
    };
    _this._onLayout = function (nativeEvent) {
      _this.props.onCellLayout && _this.props.onCellLayout(nativeEvent, _this.props.cellKey, _this.props.index);
    };
    return _this;
  }
  (0, _inherits2.default)(CellRenderer, _React$Component);
  return (0, _createClass2.default)(CellRenderer, [{
    key: "updateSeparatorProps",
    value: function updateSeparatorProps(newProps) {
      this.setState(function (state) {
        return {
          separatorProps: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, state.separatorProps), newProps)
        };
      });
    }
  }, {
    key: "componentWillUnmount",
    value: function componentWillUnmount() {
      this.props.onUnmount(this.props.cellKey);
    }
  }, {
    key: "_renderElement",
    value: function _renderElement(renderItem, ListItemComponent, item, index) {
      if (renderItem && ListItemComponent) {
        console.warn('VirtualizedList: Both ListItemComponent and renderItem props are present. ListItemComponent will take' + ' precedence over renderItem.');
      }
      if (ListItemComponent) {
        return React.createElement(ListItemComponent, {
          item: item,
          index: index,
          separators: this._separators
        });
      }
      if (renderItem) {
        return renderItem({
          item: item,
          index: index,
          separators: this._separators
        });
      }
      (0, _invariant.default)(false, 'VirtualizedList: Either ListItemComponent or renderItem props are required but none were found.');
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props4 = this.props,
        CellRendererComponent = _this$props4.CellRendererComponent,
        ItemSeparatorComponent = _this$props4.ItemSeparatorComponent,
        ListItemComponent = _this$props4.ListItemComponent,
        cellKey = _this$props4.cellKey,
        horizontal = _this$props4.horizontal,
        item = _this$props4.item,
        index = _this$props4.index,
        inversionStyle = _this$props4.inversionStyle,
        onCellFocusCapture = _this$props4.onCellFocusCapture,
        onCellLayout = _this$props4.onCellLayout,
        renderItem = _this$props4.renderItem;
      var element = this._renderElement(renderItem, ListItemComponent, item, index);
      var itemSeparator = React.isValidElement(ItemSeparatorComponent) ? ItemSeparatorComponent : ItemSeparatorComponent && React.createElement(ItemSeparatorComponent, this.state.separatorProps);
      var cellStyle = inversionStyle ? horizontal ? [styles.rowReverse, inversionStyle] : [styles.columnReverse, inversionStyle] : horizontal ? [styles.row, inversionStyle] : inversionStyle;
      var result = !CellRendererComponent ? React.createElement(_View.default, (0, _extends2.default)({
        style: cellStyle,
        onFocusCapture: onCellFocusCapture
      }, onCellLayout && {
        onLayout: this._onLayout
      }), element, itemSeparator) : React.createElement(CellRendererComponent, (0, _extends2.default)({
        cellKey: cellKey,
        index: index,
        item: item,
        style: cellStyle,
        onFocusCapture: onCellFocusCapture
      }, onCellLayout && {
        onLayout: this._onLayout
      }), element, itemSeparator);
      return React.createElement(_VirtualizedListContext.VirtualizedListCellContextProvider, {
        cellKey: this.props.cellKey
      }, result);
    }
  }], [{
    key: "getDerivedStateFromProps",
    value: function getDerivedStateFromProps(props, prevState) {
      return {
        separatorProps: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, prevState.separatorProps), {}, {
          leadingItem: props.item
        })
      };
    }
  }]);
}(React.Component);
exports.default = CellRenderer;
var styles = _StyleSheet.default.create({
  row: {
    flexDirection: 'row'
  },
  rowReverse: {
    flexDirection: 'row-reverse'
  },
  columnReverse: {
    flexDirection: 'column-reverse'
  }
});
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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