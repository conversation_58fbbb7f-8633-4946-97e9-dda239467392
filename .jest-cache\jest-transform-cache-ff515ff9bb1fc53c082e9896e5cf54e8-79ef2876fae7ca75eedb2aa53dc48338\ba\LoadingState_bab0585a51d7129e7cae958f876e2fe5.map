{"version": 3, "names": ["React", "View", "Text", "StyleSheet", "ActivityIndicator", "Zap", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_9d0f9dnpf", "s", "primary", "white", "dark", "gray", "lightGray", "LoadingState", "_ref", "_ref$message", "message", "b", "f", "style", "styles", "container", "children", "content", "iconContainer", "size", "color", "spinner", "create", "flex", "backgroundColor", "alignItems", "justifyContent", "padding", "max<PERSON><PERSON><PERSON>", "width", "height", "borderRadius", "marginBottom", "shadowColor", "shadowOffset", "shadowOpacity", "shadowRadius", "elevation", "fontSize", "fontFamily", "textAlign", "lineHeight"], "sources": ["LoadingState.tsx"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet, ActivityIndicator } from 'react-native';\nimport { Zap } from 'lucide-react-native';\n\nconst colors = {\n  primary: '#23ba16',\n  white: '#ffffff',\n  dark: '#171717',\n  gray: '#6b7280',\n  lightGray: '#f9fafb',\n};\n\ninterface LoadingStateProps {\n  message?: string;\n}\n\nexport default function LoadingState({ message = 'Loading...' }: LoadingStateProps) {\n  return (\n    <View style={styles.container}>\n      <View style={styles.content}>\n        <View style={styles.iconContainer}>\n          <Zap size={48} color={colors.primary} />\n        </View>\n        <ActivityIndicator size=\"large\" color={colors.primary} style={styles.spinner} />\n        <Text style={styles.message}>{message}</Text>\n      </View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: colors.lightGray,\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: 24,\n  },\n  content: {\n    alignItems: 'center',\n    maxWidth: 280,\n  },\n  iconContainer: {\n    width: 80,\n    height: 80,\n    borderRadius: 40,\n    backgroundColor: colors.white,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginBottom: 24,\n    shadowColor: '#000',\n    shadowOffset: {\n      width: 0,\n      height: 2,\n    },\n    shadowOpacity: 0.1,\n    shadowRadius: 8,\n    elevation: 3,\n  },\n  spinner: {\n    marginBottom: 16,\n  },\n  message: {\n    fontSize: 16,\n    fontFamily: 'Inter-Medium',\n    color: colors.gray,\n    textAlign: 'center',\n    lineHeight: 22,\n  },\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,iBAAiB,QAAQ,cAAc;AACxE,SAASC,GAAG,QAAQ,qBAAqB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE1C,IAAMC,MAAM,IAAAC,aAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE;AACb,CAAC;AAMD,eAAe,SAASC,YAAYA,CAAAC,IAAA,EAAgD;EAAA,IAAAC,YAAA,GAAAD,IAAA,CAA7CE,OAAO;IAAPA,OAAO,GAAAD,YAAA,eAAAT,aAAA,GAAAW,CAAA,UAAG,YAAY,IAAAF,YAAA;EAAAT,aAAA,GAAAY,CAAA;EAAAZ,aAAA,GAAAC,CAAA;EAC3D,OACEL,IAAA,CAACN,IAAI;IAACuB,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,EAC5BlB,KAAA,CAACR,IAAI;MAACuB,KAAK,EAAEC,MAAM,CAACG,OAAQ;MAAAD,QAAA,GAC1BpB,IAAA,CAACN,IAAI;QAACuB,KAAK,EAAEC,MAAM,CAACI,aAAc;QAAAF,QAAA,EAChCpB,IAAA,CAACF,GAAG;UAACyB,IAAI,EAAE,EAAG;UAACC,KAAK,EAAErB,MAAM,CAACG;QAAQ,CAAE;MAAC,CACpC,CAAC,EACPN,IAAA,CAACH,iBAAiB;QAAC0B,IAAI,EAAC,OAAO;QAACC,KAAK,EAAErB,MAAM,CAACG,OAAQ;QAACW,KAAK,EAAEC,MAAM,CAACO;MAAQ,CAAE,CAAC,EAChFzB,IAAA,CAACL,IAAI;QAACsB,KAAK,EAAEC,MAAM,CAACJ,OAAQ;QAAAM,QAAA,EAAEN;MAAO,CAAO,CAAC;IAAA,CACzC;EAAC,CACH,CAAC;AAEX;AAEA,IAAMI,MAAM,IAAAd,aAAA,GAAAC,CAAA,OAAGT,UAAU,CAAC8B,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,IAAI,EAAE,CAAC;IACPC,eAAe,EAAEzB,MAAM,CAACO,SAAS;IACjCmB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,OAAO,EAAE;EACX,CAAC;EACDV,OAAO,EAAE;IACPQ,UAAU,EAAE,QAAQ;IACpBG,QAAQ,EAAE;EACZ,CAAC;EACDV,aAAa,EAAE;IACbW,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBP,eAAe,EAAEzB,MAAM,CAACI,KAAK;IAC7BsB,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBM,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,MAAM;IACnBC,YAAY,EAAE;MACZL,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE;IACV,CAAC;IACDK,aAAa,EAAE,GAAG;IAClBC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE;EACb,CAAC;EACDhB,OAAO,EAAE;IACPW,YAAY,EAAE;EAChB,CAAC;EACDtB,OAAO,EAAE;IACP4B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,cAAc;IAC1BnB,KAAK,EAAErB,MAAM,CAACM,IAAI;IAClBmC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC", "ignoreList": []}