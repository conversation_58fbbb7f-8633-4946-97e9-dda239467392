3d01720fcb40e6e24900cefc2553833f
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _AnimatedEvent = require("../AnimatedEvent");
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedStyle = _interopRequireDefault(require("./AnimatedStyle"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var AnimatedProps = function (_AnimatedNode$default) {
  function AnimatedProps(props, callback) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedProps);
    _this = _callSuper(this, AnimatedProps);
    if (props.style) {
      props = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, props), {}, {
        style: new _AnimatedStyle.default(props.style)
      });
    }
    _this._props = props;
    _this._callback = callback;
    _this.__attach();
    return _this;
  }
  (0, _inherits2.default)(AnimatedProps, _AnimatedNode$default);
  return (0, _createClass2.default)(AnimatedProps, [{
    key: "__getValue",
    value: function __getValue() {
      var props = {};
      for (var key in this._props) {
        var value = this._props[key];
        if (value instanceof _AnimatedNode.default) {
          if (!value.__isNative || value instanceof _AnimatedStyle.default) {
            props[key] = value.__getValue();
          }
        } else if (value instanceof _AnimatedEvent.AnimatedEvent) {
          props[key] = value.__getHandler();
        } else {
          props[key] = value;
        }
      }
      return props;
    }
  }, {
    key: "__getAnimatedValue",
    value: function __getAnimatedValue() {
      var props = {};
      for (var key in this._props) {
        var value = this._props[key];
        if (value instanceof _AnimatedNode.default) {
          props[key] = value.__getAnimatedValue();
        }
      }
      return props;
    }
  }, {
    key: "__attach",
    value: function __attach() {
      for (var key in this._props) {
        var value = this._props[key];
        if (value instanceof _AnimatedNode.default) {
          value.__addChild(this);
        }
      }
    }
  }, {
    key: "__detach",
    value: function __detach() {
      if (this.__isNative && this._animatedView) {
        this.__disconnectAnimatedView();
      }
      for (var key in this._props) {
        var value = this._props[key];
        if (value instanceof _AnimatedNode.default) {
          value.__removeChild(this);
        }
      }
      _superPropGet(AnimatedProps, "__detach", this, 3)([]);
    }
  }, {
    key: "update",
    value: function update() {
      this._callback();
    }
  }, {
    key: "__makeNative",
    value: function __makeNative() {
      if (!this.__isNative) {
        this.__isNative = true;
        for (var key in this._props) {
          var value = this._props[key];
          if (value instanceof _AnimatedNode.default) {
            value.__makeNative();
          }
        }
        if (this._animatedView) {
          this.__connectAnimatedView();
        }
      }
    }
  }, {
    key: "setNativeView",
    value: function setNativeView(animatedView) {
      if (this._animatedView === animatedView) {
        return;
      }
      this._animatedView = animatedView;
      if (this.__isNative) {
        this.__connectAnimatedView();
      }
    }
  }, {
    key: "__connectAnimatedView",
    value: function __connectAnimatedView() {
      (0, _invariant.default)(this.__isNative, 'Expected node to be marked as "native"');
      var nativeViewTag = this._animatedView;
      (0, _invariant.default)(nativeViewTag != null, 'Unable to locate attached view in the native tree');
      _NativeAnimatedHelper.default.API.connectAnimatedNodeToView(this.__getNativeTag(), nativeViewTag);
    }
  }, {
    key: "__disconnectAnimatedView",
    value: function __disconnectAnimatedView() {
      (0, _invariant.default)(this.__isNative, 'Expected node to be marked as "native"');
      var nativeViewTag = this._animatedView;
      (0, _invariant.default)(nativeViewTag != null, 'Unable to locate attached view in the native tree');
      _NativeAnimatedHelper.default.API.disconnectAnimatedNodeFromView(this.__getNativeTag(), nativeViewTag);
    }
  }, {
    key: "__restoreDefaultValues",
    value: function __restoreDefaultValues() {
      if (this.__isNative) {
        _NativeAnimatedHelper.default.API.restoreDefaultValues(this.__getNativeTag());
      }
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      var propsConfig = {};
      for (var propKey in this._props) {
        var value = this._props[propKey];
        if (value instanceof _AnimatedNode.default) {
          value.__makeNative();
          propsConfig[propKey] = value.__getNativeTag();
        }
      }
      return {
        type: 'props',
        props: propsConfig
      };
    }
  }]);
}(_AnimatedNode.default);
var _default = exports.default = AnimatedProps;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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