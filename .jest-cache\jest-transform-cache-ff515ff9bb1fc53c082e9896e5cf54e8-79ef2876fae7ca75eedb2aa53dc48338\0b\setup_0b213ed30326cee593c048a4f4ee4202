a4d3e00e8580d139c780cbf8e9190989
_getJestObj().mock('react-native', function () {
  return {
    Platform: {
      OS: 'ios',
      select: jest.fn(function (obj) {
        return obj.ios;
      })
    },
    Dimensions: {
      get: jest.fn(function () {
        return {
          width: 375,
          height: 812
        };
      })
    },
    Alert: {
      alert: jest.fn()
    }
  };
});
_getJestObj().mock('expo-constants', function () {
  return {
    default: {
      expoConfig: {
        extra: {
          supabaseUrl: 'https://test.supabase.co',
          supabaseAnonKey: 'test-anon-key'
        }
      }
    }
  };
});
_getJestObj().mock('expo-file-system', function () {
  return {
    getInfoAsync: jest.fn(),
    readAsStringAsync: jest.fn(),
    writeAsStringAsync: jest.fn(),
    copyAsync: jest.fn(),
    deleteAsync: jest.fn(),
    makeDirectoryAsync: jest.fn(),
    cacheDirectory: '/mock/cache/',
    documentDirectory: '/mock/documents/',
    getFreeDiskStorageAsync: jest.fn(function () {
      return Promise.resolve(1000000000);
    })
  };
});
_getJestObj().mock('expo-camera', function () {
  return {
    Camera: {
      requestCameraPermissionsAsync: jest.fn(function () {
        return Promise.resolve({
          status: 'granted'
        });
      }),
      requestMicrophonePermissionsAsync: jest.fn(function () {
        return Promise.resolve({
          status: 'granted'
        });
      }),
      Constants: {
        VideoQuality: {
          '720p': '720p',
          '1080p': '1080p'
        },
        FlashMode: {
          off: 'off',
          on: 'on',
          auto: 'auto'
        }
      }
    }
  };
});
_getJestObj().mock('expo-media-library', function () {
  return {
    requestPermissionsAsync: jest.fn(function () {
      return Promise.resolve({
        status: 'granted'
      });
    }),
    saveToLibraryAsync: jest.fn()
  };
});
_getJestObj().mock("../lib/supabase", function () {
  return {
    supabase: {
      from: jest.fn(function () {
        return {
          select: jest.fn(function () {
            return {
              eq: jest.fn(function () {
                return {
                  single: jest.fn(function () {
                    return Promise.resolve({
                      data: null,
                      error: null
                    });
                  }),
                  order: jest.fn(function () {
                    return {
                      limit: jest.fn(function () {
                        return Promise.resolve({
                          data: [],
                          error: null
                        });
                      })
                    };
                  })
                };
              })
            };
          }),
          insert: jest.fn(function () {
            return {
              select: jest.fn(function () {
                return {
                  single: jest.fn(function () {
                    return Promise.resolve({
                      data: null,
                      error: null
                    });
                  })
                };
              })
            };
          }),
          update: jest.fn(function () {
            return {
              eq: jest.fn(function () {
                return Promise.resolve({
                  data: null,
                  error: null
                });
              })
            };
          })
        };
      }),
      auth: {
        getUser: jest.fn(function () {
          return Promise.resolve({
            data: {
              user: null
            },
            error: null
          });
        }),
        signInWithPassword: jest.fn(),
        signUp: jest.fn(),
        signOut: jest.fn()
      },
      storage: {
        from: jest.fn(function () {
          return {
            upload: jest.fn(function () {
              return Promise.resolve({
                data: null,
                error: null
              });
            }),
            getPublicUrl: jest.fn(function () {
              return {
                data: {
                  publicUrl: 'https://test.com/file.mp4'
                }
              };
            })
          };
        })
      }
    }
  };
});
_getJestObj().mock("../utils/performance", function () {
  return {
    performanceMonitor: {
      start: jest.fn(),
      end: jest.fn(function () {
        return 100;
      }),
      trackDatabaseError: jest.fn(),
      trackAPIError: jest.fn()
    }
  };
});
_getJestObj().mock("../utils/errorHandling", function () {
  return {
    handleError: jest.fn(function (error) {
      return {
        type: 'UnknownError',
        severity: 'medium',
        message: error.message || 'Unknown error',
        userMessage: error.message || 'Something went wrong',
        timestamp: new Date().toISOString()
      };
    }),
    logError: jest.fn()
  };
});
_getJestObj().mock("../services/openai", function () {
  return {
    openAIService: {
      generateCompletion: jest.fn(function () {
        return Promise.resolve({
          success: true,
          data: 'Mock AI response'
        });
      }),
      generateCoachingAdvice: jest.fn(function () {
        return Promise.resolve({
          personalizedTip: 'Mock coaching tip',
          recommendations: ['Tip 1', 'Tip 2']
        });
      })
    }
  };
});
_getJestObj().mock("../config/environment", function () {
  return {
    env: {
      get: jest.fn(function (key) {
        var mockEnv = {
          EXPO_PUBLIC_SUPABASE_URL: 'https://test.supabase.co',
          EXPO_PUBLIC_SUPABASE_ANON_KEY: 'test-anon-key',
          OPENAI_API_KEY: 'test-openai-key',
          EXPO_PUBLIC_MEDIAPIPE_MODEL_URL: 'https://test.com/model.task'
        };
        return mockEnv[key];
      }),
      isFeatureEnabled: jest.fn(function () {
        return true;
      })
    }
  };
});
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
global.__DEV__ = true;
var originalWarn = console.warn;
var originalError = console.error;
beforeEach(function () {
  console.warn = jest.fn();
  console.error = jest.fn();
});
afterEach(function () {
  console.warn = originalWarn;
  console.error = originalError;
});
jest.setTimeout(30000);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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