6d1ebde215bda0cb2d385e4f2040307d
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2gb3samthe() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\native\\AdvancedMemoryManager.ts";
  var hash = "83e5d776499938981a8d6b38982c45ba03b45979";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\native\\AdvancedMemoryManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 84,
          column: 49
        },
        end: {
          line: 84,
          column: 58
        }
      },
      "1": {
        start: {
          line: 85,
          column: 49
        },
        end: {
          line: 85,
          column: 58
        }
      },
      "2": {
        start: {
          line: 88,
          column: 90
        },
        end: {
          line: 88,
          column: 92
        }
      },
      "3": {
        start: {
          line: 92,
          column: 42
        },
        end: {
          line: 99,
          column: 3
        }
      },
      "4": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 108,
          column: 6
        }
      },
      "5": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 117,
          column: 6
        }
      },
      "6": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 119,
          column: 49
        }
      },
      "7": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 120,
          column: 60
        }
      },
      "8": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 122,
          column: 35
        }
      },
      "9": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 148,
          column: 5
        }
      },
      "10": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 44
        }
      },
      "11": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 35
        }
      },
      "12": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 43
        }
      },
      "13": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 37
        }
      },
      "14": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 37
        }
      },
      "15": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 70
        }
      },
      "16": {
        start: {
          line: 147,
          column: 6
        },
        end: {
          line: 147,
          column: 76
        }
      },
      "17": {
        start: {
          line: 159,
          column: 4
        },
        end: {
          line: 212,
          column: 5
        }
      },
      "18": {
        start: {
          line: 160,
          column: 19
        },
        end: {
          line: 160,
          column: 49
        }
      },
      "19": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 163,
          column: 7
        }
      },
      "20": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 162,
          column: 62
        }
      },
      "21": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 174,
          column: 7
        }
      },
      "22": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 48
        }
      },
      "23": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 173,
          column: 9
        }
      },
      "24": {
        start: {
          line: 172,
          column: 10
        },
        end: {
          line: 172,
          column: 70
        }
      },
      "25": {
        start: {
          line: 177,
          column: 27
        },
        end: {
          line: 177,
          column: 54
        }
      },
      "26": {
        start: {
          line: 178,
          column: 43
        },
        end: {
          line: 193,
          column: 7
        }
      },
      "27": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 53
        }
      },
      "28": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 24
        }
      },
      "29": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 198,
          column: 29
        }
      },
      "30": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 73
        }
      },
      "31": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 204,
          column: 39
        }
      },
      "32": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 206,
          column: 65
        }
      },
      "33": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 207,
          column: 26
        }
      },
      "34": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 210,
          column: 57
        }
      },
      "35": {
        start: {
          line: 211,
          column: 6
        },
        end: {
          line: 211,
          column: 18
        }
      },
      "36": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 249,
          column: 5
        }
      },
      "37": {
        start: {
          line: 221,
          column: 42
        },
        end: {
          line: 221,
          column: 46
        }
      },
      "38": {
        start: {
          line: 222,
          column: 48
        },
        end: {
          line: 222,
          column: 52
        }
      },
      "39": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 230,
          column: 7
        }
      },
      "40": {
        start: {
          line: 225,
          column: 8
        },
        end: {
          line: 229,
          column: 9
        }
      },
      "41": {
        start: {
          line: 226,
          column: 10
        },
        end: {
          line: 226,
          column: 28
        }
      },
      "42": {
        start: {
          line: 227,
          column: 10
        },
        end: {
          line: 227,
          column: 59
        }
      },
      "43": {
        start: {
          line: 228,
          column: 10
        },
        end: {
          line: 228,
          column: 16
        }
      },
      "44": {
        start: {
          line: 232,
          column: 6
        },
        end: {
          line: 235,
          column: 7
        }
      },
      "45": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 233,
          column: 69
        }
      },
      "46": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 15
        }
      },
      "47": {
        start: {
          line: 238,
          column: 6
        },
        end: {
          line: 238,
          column: 50
        }
      },
      "48": {
        start: {
          line: 239,
          column: 6
        },
        end: {
          line: 239,
          column: 41
        }
      },
      "49": {
        start: {
          line: 240,
          column: 6
        },
        end: {
          line: 240,
          column: 46
        }
      },
      "50": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 243,
          column: 45
        }
      },
      "51": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 79
        }
      },
      "52": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 53
        }
      },
      "53": {
        start: {
          line: 261,
          column: 4
        },
        end: {
          line: 297,
          column: 5
        }
      },
      "54": {
        start: {
          line: 262,
          column: 6
        },
        end: {
          line: 262,
          column: 78
        }
      },
      "55": {
        start: {
          line: 264,
          column: 24
        },
        end: {
          line: 264,
          column: 25
        }
      },
      "56": {
        start: {
          line: 265,
          column: 27
        },
        end: {
          line: 265,
          column: 28
        }
      },
      "57": {
        start: {
          line: 266,
          column: 26
        },
        end: {
          line: 266,
          column: 27
        }
      },
      "58": {
        start: {
          line: 267,
          column: 28
        },
        end: {
          line: 267,
          column: 29
        }
      },
      "59": {
        start: {
          line: 270,
          column: 6
        },
        end: {
          line: 276,
          column: 7
        }
      },
      "60": {
        start: {
          line: 271,
          column: 26
        },
        end: {
          line: 271,
          column: 73
        }
      },
      "61": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 275,
          column: 9
        }
      },
      "62": {
        start: {
          line: 273,
          column: 10
        },
        end: {
          line: 273,
          column: 35
        }
      },
      "63": {
        start: {
          line: 274,
          column: 10
        },
        end: {
          line: 274,
          column: 27
        }
      },
      "64": {
        start: {
          line: 279,
          column: 28
        },
        end: {
          line: 279,
          column: 69
        }
      },
      "65": {
        start: {
          line: 280,
          column: 6
        },
        end: {
          line: 280,
          column: 43
        }
      },
      "66": {
        start: {
          line: 283,
          column: 6
        },
        end: {
          line: 285,
          column: 7
        }
      },
      "67": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 284,
          column: 65
        }
      },
      "68": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 40
        }
      },
      "69": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 79
        }
      },
      "70": {
        start: {
          line: 292,
          column: 6
        },
        end: {
          line: 292,
          column: 77
        }
      },
      "71": {
        start: {
          line: 295,
          column: 6
        },
        end: {
          line: 295,
          column: 57
        }
      },
      "72": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 296,
          column: 89
        }
      },
      "73": {
        start: {
          line: 312,
          column: 25
        },
        end: {
          line: 312,
          column: 26
        }
      },
      "74": {
        start: {
          line: 313,
          column: 25
        },
        end: {
          line: 313,
          column: 26
        }
      },
      "75": {
        start: {
          line: 314,
          column: 52
        },
        end: {
          line: 314,
          column: 54
        }
      },
      "76": {
        start: {
          line: 315,
          column: 29
        },
        end: {
          line: 315,
          column: 30
        }
      },
      "77": {
        start: {
          line: 317,
          column: 4
        },
        end: {
          line: 322,
          column: 5
        }
      },
      "78": {
        start: {
          line: 318,
          column: 6
        },
        end: {
          line: 318,
          column: 34
        }
      },
      "79": {
        start: {
          line: 319,
          column: 6
        },
        end: {
          line: 319,
          column: 39
        }
      },
      "80": {
        start: {
          line: 320,
          column: 6
        },
        end: {
          line: 320,
          column: 62
        }
      },
      "81": {
        start: {
          line: 321,
          column: 6
        },
        end: {
          line: 321,
          column: 64
        }
      },
      "82": {
        start: {
          line: 324,
          column: 31
        },
        end: {
          line: 326,
          column: 9
        }
      },
      "83": {
        start: {
          line: 328,
          column: 4
        },
        end: {
          line: 336,
          column: 6
        }
      },
      "84": {
        start: {
          line: 343,
          column: 4
        },
        end: {
          line: 343,
          column: 71
        }
      },
      "85": {
        start: {
          line: 349,
          column: 4
        },
        end: {
          line: 351,
          column: 5
        }
      },
      "86": {
        start: {
          line: 350,
          column: 6
        },
        end: {
          line: 350,
          column: 73
        }
      },
      "87": {
        start: {
          line: 359,
          column: 29
        },
        end: {
          line: 379,
          column: 5
        }
      },
      "88": {
        start: {
          line: 381,
          column: 4
        },
        end: {
          line: 381,
          column: 39
        }
      },
      "89": {
        start: {
          line: 382,
          column: 4
        },
        end: {
          line: 382,
          column: 69
        }
      },
      "90": {
        start: {
          line: 386,
          column: 4
        },
        end: {
          line: 386,
          column: 34
        }
      },
      "91": {
        start: {
          line: 390,
          column: 20
        },
        end: {
          line: 390,
          column: 21
        }
      },
      "92": {
        start: {
          line: 391,
          column: 40
        },
        end: {
          line: 391,
          column: 42
        }
      },
      "93": {
        start: {
          line: 394,
          column: 30
        },
        end: {
          line: 405,
          column: 6
        }
      },
      "94": {
        start: {
          line: 395,
          column: 6
        },
        end: {
          line: 404,
          column: 7
        }
      },
      "95": {
        start: {
          line: 397,
          column: 10
        },
        end: {
          line: 397,
          column: 49
        }
      },
      "96": {
        start: {
          line: 399,
          column: 10
        },
        end: {
          line: 399,
          column: 47
        }
      },
      "97": {
        start: {
          line: 401,
          column: 10
        },
        end: {
          line: 401,
          column: 43
        }
      },
      "98": {
        start: {
          line: 403,
          column: 10
        },
        end: {
          line: 403,
          column: 49
        }
      },
      "99": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 415,
          column: 5
        }
      },
      "100": {
        start: {
          line: 409,
          column: 6
        },
        end: {
          line: 409,
          column: 47
        }
      },
      "101": {
        start: {
          line: 409,
          column: 38
        },
        end: {
          line: 409,
          column: 47
        }
      },
      "102": {
        start: {
          line: 411,
          column: 6
        },
        end: {
          line: 411,
          column: 44
        }
      },
      "103": {
        start: {
          line: 412,
          column: 6
        },
        end: {
          line: 412,
          column: 35
        }
      },
      "104": {
        start: {
          line: 414,
          column: 6
        },
        end: {
          line: 414,
          column: 43
        }
      },
      "105": {
        start: {
          line: 414,
          column: 37
        },
        end: {
          line: 414,
          column: 43
        }
      },
      "106": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 420,
          column: 5
        }
      },
      "107": {
        start: {
          line: 419,
          column: 6
        },
        end: {
          line: 419,
          column: 42
        }
      },
      "108": {
        start: {
          line: 422,
          column: 4
        },
        end: {
          line: 422,
          column: 21
        }
      },
      "109": {
        start: {
          line: 426,
          column: 22
        },
        end: {
          line: 426,
          column: 23
        }
      },
      "110": {
        start: {
          line: 429,
          column: 16
        },
        end: {
          line: 429,
          column: 26
        }
      },
      "111": {
        start: {
          line: 430,
          column: 19
        },
        end: {
          line: 430,
          column: 47
        }
      },
      "112": {
        start: {
          line: 432,
          column: 27
        },
        end: {
          line: 436,
          column: 7
        }
      },
      "113": {
        start: {
          line: 434,
          column: 8
        },
        end: {
          line: 435,
          column: 43
        }
      },
      "114": {
        start: {
          line: 438,
          column: 4
        },
        end: {
          line: 441,
          column: 5
        }
      },
      "115": {
        start: {
          line: 439,
          column: 6
        },
        end: {
          line: 439,
          column: 43
        }
      },
      "116": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 440,
          column: 37
        }
      },
      "117": {
        start: {
          line: 444,
          column: 4
        },
        end: {
          line: 446,
          column: 5
        }
      },
      "118": {
        start: {
          line: 445,
          column: 6
        },
        end: {
          line: 445,
          column: 38
        }
      },
      "119": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 448,
          column: 23
        }
      },
      "120": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 453,
          column: 50
        }
      },
      "121": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 454,
          column: 47
        }
      },
      "122": {
        start: {
          line: 458,
          column: 40
        },
        end: {
          line: 458,
          column: 42
        }
      },
      "123": {
        start: {
          line: 460,
          column: 4
        },
        end: {
          line: 473,
          column: 5
        }
      },
      "124": {
        start: {
          line: 461,
          column: 6
        },
        end: {
          line: 461,
          column: 34
        }
      },
      "125": {
        start: {
          line: 461,
          column: 25
        },
        end: {
          line: 461,
          column: 34
        }
      },
      "126": {
        start: {
          line: 463,
          column: 28
        },
        end: {
          line: 463,
          column: 100
        }
      },
      "127": {
        start: {
          line: 465,
          column: 6
        },
        end: {
          line: 472,
          column: 7
        }
      },
      "128": {
        start: {
          line: 467,
          column: 25
        },
        end: {
          line: 467,
          column: 53
        }
      },
      "129": {
        start: {
          line: 468,
          column: 8
        },
        end: {
          line: 471,
          column: 9
        }
      },
      "130": {
        start: {
          line: 469,
          column: 10
        },
        end: {
          line: 469,
          column: 31
        }
      },
      "131": {
        start: {
          line: 470,
          column: 10
        },
        end: {
          line: 470,
          column: 35
        }
      },
      "132": {
        start: {
          line: 475,
          column: 4
        },
        end: {
          line: 475,
          column: 25
        }
      },
      "133": {
        start: {
          line: 479,
          column: 4
        },
        end: {
          line: 492,
          column: 5
        }
      },
      "134": {
        start: {
          line: 481,
          column: 6
        },
        end: {
          line: 481,
          column: 76
        }
      },
      "135": {
        start: {
          line: 488,
          column: 6
        },
        end: {
          line: 488,
          column: 18
        }
      },
      "136": {
        start: {
          line: 490,
          column: 6
        },
        end: {
          line: 490,
          column: 65
        }
      },
      "137": {
        start: {
          line: 491,
          column: 6
        },
        end: {
          line: 491,
          column: 19
        }
      },
      "138": {
        start: {
          line: 497,
          column: 24
        },
        end: {
          line: 497,
          column: 25
        }
      },
      "139": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 509,
          column: 5
        }
      },
      "140": {
        start: {
          line: 501,
          column: 6
        },
        end: {
          line: 501,
          column: 50
        }
      },
      "141": {
        start: {
          line: 502,
          column: 6
        },
        end: {
          line: 502,
          column: 44
        }
      },
      "142": {
        start: {
          line: 503,
          column: 6
        },
        end: {
          line: 503,
          column: 22
        }
      },
      "143": {
        start: {
          line: 504,
          column: 11
        },
        end: {
          line: 509,
          column: 5
        }
      },
      "144": {
        start: {
          line: 506,
          column: 6
        },
        end: {
          line: 506,
          column: 48
        }
      },
      "145": {
        start: {
          line: 507,
          column: 6
        },
        end: {
          line: 507,
          column: 44
        }
      },
      "146": {
        start: {
          line: 508,
          column: 6
        },
        end: {
          line: 508,
          column: 22
        }
      },
      "147": {
        start: {
          line: 512,
          column: 4
        },
        end: {
          line: 512,
          column: 68
        }
      },
      "148": {
        start: {
          line: 514,
          column: 4
        },
        end: {
          line: 514,
          column: 25
        }
      },
      "149": {
        start: {
          line: 518,
          column: 4
        },
        end: {
          line: 518,
          column: 76
        }
      },
      "150": {
        start: {
          line: 523,
          column: 23
        },
        end: {
          line: 523,
          column: 44
        }
      },
      "151": {
        start: {
          line: 524,
          column: 25
        },
        end: {
          line: 524,
          column: 68
        }
      },
      "152": {
        start: {
          line: 525,
          column: 28
        },
        end: {
          line: 525,
          column: 75
        }
      },
      "153": {
        start: {
          line: 526,
          column: 4
        },
        end: {
          line: 526,
          column: 103
        }
      },
      "154": {
        start: {
          line: 529,
          column: 4
        },
        end: {
          line: 529,
          column: 66
        }
      },
      "155": {
        start: {
          line: 534,
          column: 20
        },
        end: {
          line: 534,
          column: 43
        }
      },
      "156": {
        start: {
          line: 535,
          column: 24
        },
        end: {
          line: 535,
          column: 71
        }
      },
      "157": {
        start: {
          line: 536,
          column: 22
        },
        end: {
          line: 536,
          column: 80
        }
      },
      "158": {
        start: {
          line: 540,
          column: 4
        },
        end: {
          line: 543,
          column: 29
        }
      },
      "159": {
        start: {
          line: 540,
          column: 25
        },
        end: {
          line: 540,
          column: 42
        }
      },
      "160": {
        start: {
          line: 541,
          column: 9
        },
        end: {
          line: 543,
          column: 29
        }
      },
      "161": {
        start: {
          line: 541,
          column: 30
        },
        end: {
          line: 541,
          column: 48
        }
      },
      "162": {
        start: {
          line: 542,
          column: 9
        },
        end: {
          line: 543,
          column: 29
        }
      },
      "163": {
        start: {
          line: 542,
          column: 31
        },
        end: {
          line: 542,
          column: 50
        }
      },
      "164": {
        start: {
          line: 543,
          column: 9
        },
        end: {
          line: 543,
          column: 29
        }
      },
      "165": {
        start: {
          line: 546,
          column: 38
        },
        end: {
          line: 546,
          column: 40
        }
      },
      "166": {
        start: {
          line: 547,
          column: 4
        },
        end: {
          line: 555,
          column: 5
        }
      },
      "167": {
        start: {
          line: 548,
          column: 6
        },
        end: {
          line: 548,
          column: 66
        }
      },
      "168": {
        start: {
          line: 549,
          column: 11
        },
        end: {
          line: 555,
          column: 5
        }
      },
      "169": {
        start: {
          line: 550,
          column: 6
        },
        end: {
          line: 550,
          column: 54
        }
      },
      "170": {
        start: {
          line: 551,
          column: 6
        },
        end: {
          line: 551,
          column: 57
        }
      },
      "171": {
        start: {
          line: 552,
          column: 11
        },
        end: {
          line: 555,
          column: 5
        }
      },
      "172": {
        start: {
          line: 553,
          column: 6
        },
        end: {
          line: 553,
          column: 64
        }
      },
      "173": {
        start: {
          line: 554,
          column: 6
        },
        end: {
          line: 554,
          column: 59
        }
      },
      "174": {
        start: {
          line: 557,
          column: 4
        },
        end: {
          line: 564,
          column: 6
        }
      },
      "175": {
        start: {
          line: 569,
          column: 4
        },
        end: {
          line: 571,
          column: 14
        }
      },
      "176": {
        start: {
          line: 570,
          column: 6
        },
        end: {
          line: 570,
          column: 34
        }
      },
      "177": {
        start: {
          line: 576,
          column: 4
        },
        end: {
          line: 580,
          column: 13
        }
      },
      "178": {
        start: {
          line: 577,
          column: 6
        },
        end: {
          line: 579,
          column: 7
        }
      },
      "179": {
        start: {
          line: 578,
          column: 8
        },
        end: {
          line: 578,
          column: 40
        }
      },
      "180": {
        start: {
          line: 588,
          column: 55
        },
        end: {
          line: 588,
          column: 64
        }
      },
      "181": {
        start: {
          line: 591,
          column: 4
        },
        end: {
          line: 591,
          column: 52
        }
      },
      "182": {
        start: {
          line: 597,
          column: 32
        },
        end: {
          line: 597,
          column: 34
        }
      },
      "183": {
        start: {
          line: 598,
          column: 16
        },
        end: {
          line: 598,
          column: 26
        }
      },
      "184": {
        start: {
          line: 601,
          column: 25
        },
        end: {
          line: 601,
          column: 62
        }
      },
      "185": {
        start: {
          line: 603,
          column: 4
        },
        end: {
          line: 609,
          column: 7
        }
      },
      "186": {
        start: {
          line: 604,
          column: 21
        },
        end: {
          line: 604,
          column: 53
        }
      },
      "187": {
        start: {
          line: 605,
          column: 6
        },
        end: {
          line: 607,
          column: 7
        }
      },
      "188": {
        start: {
          line: 606,
          column: 8
        },
        end: {
          line: 606,
          column: 37
        }
      },
      "189": {
        start: {
          line: 608,
          column: 6
        },
        end: {
          line: 608,
          column: 55
        }
      },
      "190": {
        start: {
          line: 612,
          column: 4
        },
        end: {
          line: 637,
          column: 5
        }
      },
      "191": {
        start: {
          line: 613,
          column: 24
        },
        end: {
          line: 613,
          column: 79
        }
      },
      "192": {
        start: {
          line: 613,
          column: 59
        },
        end: {
          line: 613,
          column: 75
        }
      },
      "193": {
        start: {
          line: 614,
          column: 21
        },
        end: {
          line: 614,
          column: 110
        }
      },
      "194": {
        start: {
          line: 614,
          column: 56
        },
        end: {
          line: 614,
          column: 85
        }
      },
      "195": {
        start: {
          line: 617,
          column: 6
        },
        end: {
          line: 636,
          column: 7
        }
      },
      "196": {
        start: {
          line: 618,
          column: 27
        },
        end: {
          line: 618,
          column: 54
        }
      },
      "197": {
        start: {
          line: 621,
          column: 8
        },
        end: {
          line: 624,
          column: 32
        }
      },
      "198": {
        start: {
          line: 621,
          column: 38
        },
        end: {
          line: 621,
          column: 60
        }
      },
      "199": {
        start: {
          line: 622,
          column: 13
        },
        end: {
          line: 624,
          column: 32
        }
      },
      "200": {
        start: {
          line: 622,
          column: 42
        },
        end: {
          line: 622,
          column: 62
        }
      },
      "201": {
        start: {
          line: 623,
          column: 13
        },
        end: {
          line: 624,
          column: 32
        }
      },
      "202": {
        start: {
          line: 623,
          column: 42
        },
        end: {
          line: 623,
          column: 64
        }
      },
      "203": {
        start: {
          line: 624,
          column: 13
        },
        end: {
          line: 624,
          column: 32
        }
      },
      "204": {
        start: {
          line: 626,
          column: 8
        },
        end: {
          line: 635,
          column: 11
        }
      },
      "205": {
        start: {
          line: 639,
          column: 4
        },
        end: {
          line: 639,
          column: 17
        }
      },
      "206": {
        start: {
          line: 648,
          column: 23
        },
        end: {
          line: 648,
          column: 26
        }
      },
      "207": {
        start: {
          line: 649,
          column: 22
        },
        end: {
          line: 649,
          column: 27
        }
      },
      "208": {
        start: {
          line: 652,
          column: 4
        },
        end: {
          line: 652,
          column: 32
        }
      },
      "209": {
        start: {
          line: 656,
          column: 4
        },
        end: {
          line: 656,
          column: 31
        }
      },
      "210": {
        start: {
          line: 656,
          column: 24
        },
        end: {
          line: 656,
          column: 31
        }
      },
      "211": {
        start: {
          line: 658,
          column: 4
        },
        end: {
          line: 658,
          column: 26
        }
      },
      "212": {
        start: {
          line: 659,
          column: 4
        },
        end: {
          line: 659,
          column: 22
        }
      },
      "213": {
        start: {
          line: 660,
          column: 4
        },
        end: {
          line: 660,
          column: 40
        }
      },
      "214": {
        start: {
          line: 664,
          column: 4
        },
        end: {
          line: 664,
          column: 32
        }
      },
      "215": {
        start: {
          line: 665,
          column: 4
        },
        end: {
          line: 665,
          column: 44
        }
      },
      "216": {
        start: {
          line: 669,
          column: 4
        },
        end: {
          line: 669,
          column: 27
        }
      },
      "217": {
        start: {
          line: 673,
          column: 4
        },
        end: {
          line: 673,
          column: 32
        }
      },
      "218": {
        start: {
          line: 673,
          column: 25
        },
        end: {
          line: 673,
          column: 32
        }
      },
      "219": {
        start: {
          line: 675,
          column: 4
        },
        end: {
          line: 678,
          column: 37
        }
      },
      "220": {
        start: {
          line: 676,
          column: 6
        },
        end: {
          line: 676,
          column: 23
        }
      },
      "221": {
        start: {
          line: 677,
          column: 6
        },
        end: {
          line: 677,
          column: 24
        }
      },
      "222": {
        start: {
          line: 682,
          column: 4
        },
        end: {
          line: 707,
          column: 5
        }
      },
      "223": {
        start: {
          line: 684,
          column: 24
        },
        end: {
          line: 684,
          column: 34
        }
      },
      "224": {
        start: {
          line: 687,
          column: 6
        },
        end: {
          line: 700,
          column: 7
        }
      },
      "225": {
        start: {
          line: 689,
          column: 10
        },
        end: {
          line: 689,
          column: 36
        }
      },
      "226": {
        start: {
          line: 690,
          column: 10
        },
        end: {
          line: 690,
          column: 16
        }
      },
      "227": {
        start: {
          line: 692,
          column: 10
        },
        end: {
          line: 692,
          column: 34
        }
      },
      "228": {
        start: {
          line: 693,
          column: 10
        },
        end: {
          line: 693,
          column: 16
        }
      },
      "229": {
        start: {
          line: 695,
          column: 10
        },
        end: {
          line: 695,
          column: 38
        }
      },
      "230": {
        start: {
          line: 696,
          column: 10
        },
        end: {
          line: 696,
          column: 16
        }
      },
      "231": {
        start: {
          line: 698,
          column: 10
        },
        end: {
          line: 698,
          column: 34
        }
      },
      "232": {
        start: {
          line: 699,
          column: 10
        },
        end: {
          line: 699,
          column: 16
        }
      },
      "233": {
        start: {
          line: 702,
          column: 21
        },
        end: {
          line: 702,
          column: 43
        }
      },
      "234": {
        start: {
          line: 703,
          column: 6
        },
        end: {
          line: 703,
          column: 81
        }
      },
      "235": {
        start: {
          line: 706,
          column: 6
        },
        end: {
          line: 706,
          column: 51
        }
      },
      "236": {
        start: {
          line: 712,
          column: 4
        },
        end: {
          line: 712,
          column: 57
        }
      },
      "237": {
        start: {
          line: 712,
          column: 33
        },
        end: {
          line: 712,
          column: 55
        }
      },
      "238": {
        start: {
          line: 717,
          column: 4
        },
        end: {
          line: 717,
          column: 58
        }
      },
      "239": {
        start: {
          line: 717,
          column: 33
        },
        end: {
          line: 717,
          column: 56
        }
      },
      "240": {
        start: {
          line: 722,
          column: 4
        },
        end: {
          line: 722,
          column: 58
        }
      },
      "241": {
        start: {
          line: 722,
          column: 33
        },
        end: {
          line: 722,
          column: 56
        }
      },
      "242": {
        start: {
          line: 727,
          column: 4
        },
        end: {
          line: 727,
          column: 58
        }
      },
      "243": {
        start: {
          line: 727,
          column: 33
        },
        end: {
          line: 727,
          column: 56
        }
      },
      "244": {
        start: {
          line: 732,
          column: 37
        },
        end: {
          line: 732,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 101,
            column: 2
          },
          end: {
            line: 101,
            column: 3
          }
        },
        loc: {
          start: {
            line: 101,
            column: 16
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 101
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 128,
            column: 2
          },
          end: {
            line: 128,
            column: 3
          }
        },
        loc: {
          start: {
            line: 128,
            column: 57
          },
          end: {
            line: 149,
            column: 3
          }
        },
        line: 128
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 154,
            column: 2
          },
          end: {
            line: 154,
            column: 3
          }
        },
        loc: {
          start: {
            line: 158,
            column: 21
          },
          end: {
            line: 213,
            column: 3
          }
        },
        line: 158
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 218,
            column: 2
          },
          end: {
            line: 218,
            column: 3
          }
        },
        loc: {
          start: {
            line: 218,
            column: 56
          },
          end: {
            line: 250,
            column: 3
          }
        },
        line: 218
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 255,
            column: 2
          },
          end: {
            line: 255,
            column: 3
          }
        },
        loc: {
          start: {
            line: 260,
            column: 5
          },
          end: {
            line: 298,
            column: 3
          }
        },
        line: 260
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 303,
            column: 2
          },
          end: {
            line: 303,
            column: 3
          }
        },
        loc: {
          start: {
            line: 311,
            column: 4
          },
          end: {
            line: 337,
            column: 3
          }
        },
        line: 311
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 342,
            column: 2
          },
          end: {
            line: 342,
            column: 3
          }
        },
        loc: {
          start: {
            line: 342,
            column: 51
          },
          end: {
            line: 344,
            column: 3
          }
        },
        line: 342
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 348,
            column: 2
          },
          end: {
            line: 348,
            column: 3
          }
        },
        loc: {
          start: {
            line: 348,
            column: 58
          },
          end: {
            line: 352,
            column: 3
          }
        },
        line: 348
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 354,
            column: 2
          },
          end: {
            line: 354,
            column: 3
          }
        },
        loc: {
          start: {
            line: 358,
            column: 19
          },
          end: {
            line: 383,
            column: 3
          }
        },
        line: 358
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 385,
            column: 2
          },
          end: {
            line: 385,
            column: 3
          }
        },
        loc: {
          start: {
            line: 385,
            column: 63
          },
          end: {
            line: 387,
            column: 3
          }
        },
        line: 385
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 389,
            column: 2
          },
          end: {
            line: 389,
            column: 3
          }
        },
        loc: {
          start: {
            line: 389,
            column: 90
          },
          end: {
            line: 423,
            column: 3
          }
        },
        line: 389
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 394,
            column: 73
          },
          end: {
            line: 394,
            column: 74
          }
        },
        loc: {
          start: {
            line: 394,
            column: 83
          },
          end: {
            line: 405,
            column: 5
          }
        },
        line: 394
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 425,
            column: 2
          },
          end: {
            line: 425,
            column: 3
          }
        },
        loc: {
          start: {
            line: 425,
            column: 91
          },
          end: {
            line: 449,
            column: 3
          }
        },
        line: 425
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 433,
            column: 14
          },
          end: {
            line: 433,
            column: 15
          }
        },
        loc: {
          start: {
            line: 434,
            column: 8
          },
          end: {
            line: 435,
            column: 43
          }
        },
        line: 434
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 451,
            column: 2
          },
          end: {
            line: 451,
            column: 3
          }
        },
        loc: {
          start: {
            line: 451,
            column: 64
          },
          end: {
            line: 455,
            column: 3
          }
        },
        line: 451
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 457,
            column: 2
          },
          end: {
            line: 457,
            column: 3
          }
        },
        loc: {
          start: {
            line: 457,
            column: 79
          },
          end: {
            line: 476,
            column: 3
          }
        },
        line: 457
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 478,
            column: 2
          },
          end: {
            line: 478,
            column: 3
          }
        },
        loc: {
          start: {
            line: 478,
            column: 64
          },
          end: {
            line: 493,
            column: 3
          }
        },
        line: 478
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 495,
            column: 2
          },
          end: {
            line: 495,
            column: 3
          }
        },
        loc: {
          start: {
            line: 495,
            column: 61
          },
          end: {
            line: 515,
            column: 3
          }
        },
        line: 495
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 517,
            column: 2
          },
          end: {
            line: 517,
            column: 3
          }
        },
        loc: {
          start: {
            line: 517,
            column: 41
          },
          end: {
            line: 519,
            column: 3
          }
        },
        line: 517
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 521,
            column: 2
          },
          end: {
            line: 521,
            column: 3
          }
        },
        loc: {
          start: {
            line: 521,
            column: 56
          },
          end: {
            line: 530,
            column: 3
          }
        },
        line: 521
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 532,
            column: 2
          },
          end: {
            line: 532,
            column: 3
          }
        },
        loc: {
          start: {
            line: 532,
            column: 54
          },
          end: {
            line: 565,
            column: 3
          }
        },
        line: 532
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 567,
            column: 2
          },
          end: {
            line: 567,
            column: 3
          }
        },
        loc: {
          start: {
            line: 567,
            column: 40
          },
          end: {
            line: 572,
            column: 3
          }
        },
        line: 567
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 569,
            column: 16
          },
          end: {
            line: 569,
            column: 17
          }
        },
        loc: {
          start: {
            line: 569,
            column: 22
          },
          end: {
            line: 571,
            column: 5
          }
        },
        line: 569
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 574,
            column: 2
          },
          end: {
            line: 574,
            column: 3
          }
        },
        loc: {
          start: {
            line: 574,
            column: 42
          },
          end: {
            line: 581,
            column: 3
          }
        },
        line: 574
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 576,
            column: 16
          },
          end: {
            line: 576,
            column: 17
          }
        },
        loc: {
          start: {
            line: 576,
            column: 28
          },
          end: {
            line: 580,
            column: 5
          }
        },
        line: 576
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 590,
            column: 2
          },
          end: {
            line: 590,
            column: 3
          }
        },
        loc: {
          start: {
            line: 590,
            column: 36
          },
          end: {
            line: 592,
            column: 3
          }
        },
        line: 590
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 594,
            column: 2
          },
          end: {
            line: 594,
            column: 3
          }
        },
        loc: {
          start: {
            line: 596,
            column: 27
          },
          end: {
            line: 640,
            column: 3
          }
        },
        line: 596
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 603,
            column: 30
          },
          end: {
            line: 603,
            column: 31
          }
        },
        loc: {
          start: {
            line: 603,
            column: 39
          },
          end: {
            line: 609,
            column: 5
          }
        },
        line: 603
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 613,
            column: 43
          },
          end: {
            line: 613,
            column: 44
          }
        },
        loc: {
          start: {
            line: 613,
            column: 59
          },
          end: {
            line: 613,
            column: 75
          }
        },
        line: 613
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 614,
            column: 40
          },
          end: {
            line: 614,
            column: 41
          }
        },
        loc: {
          start: {
            line: 614,
            column: 56
          },
          end: {
            line: 614,
            column: 85
          }
        },
        line: 614
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 651,
            column: 2
          },
          end: {
            line: 651,
            column: 3
          }
        },
        loc: {
          start: {
            line: 651,
            column: 38
          },
          end: {
            line: 653,
            column: 3
          }
        },
        line: 651
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 655,
            column: 2
          },
          end: {
            line: 655,
            column: 3
          }
        },
        loc: {
          start: {
            line: 655,
            column: 31
          },
          end: {
            line: 661,
            column: 3
          }
        },
        line: 655
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 663,
            column: 2
          },
          end: {
            line: 663,
            column: 3
          }
        },
        loc: {
          start: {
            line: 663,
            column: 67
          },
          end: {
            line: 666,
            column: 3
          }
        },
        line: 663
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 668,
            column: 2
          },
          end: {
            line: 668,
            column: 3
          }
        },
        loc: {
          start: {
            line: 668,
            column: 26
          },
          end: {
            line: 670,
            column: 3
          }
        },
        line: 668
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 672,
            column: 2
          },
          end: {
            line: 672,
            column: 3
          }
        },
        loc: {
          start: {
            line: 672,
            column: 29
          },
          end: {
            line: 679,
            column: 3
          }
        },
        line: 672
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 675,
            column: 15
          },
          end: {
            line: 675,
            column: 16
          }
        },
        loc: {
          start: {
            line: 675,
            column: 21
          },
          end: {
            line: 678,
            column: 5
          }
        },
        line: 675
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 681,
            column: 2
          },
          end: {
            line: 681,
            column: 3
          }
        },
        loc: {
          start: {
            line: 681,
            column: 43
          },
          end: {
            line: 708,
            column: 3
          }
        },
        line: 681
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 710,
            column: 2
          },
          end: {
            line: 710,
            column: 3
          }
        },
        loc: {
          start: {
            line: 710,
            column: 46
          },
          end: {
            line: 713,
            column: 3
          }
        },
        line: 710
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 712,
            column: 22
          },
          end: {
            line: 712,
            column: 23
          }
        },
        loc: {
          start: {
            line: 712,
            column: 33
          },
          end: {
            line: 712,
            column: 55
          }
        },
        line: 712
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 715,
            column: 2
          },
          end: {
            line: 715,
            column: 3
          }
        },
        loc: {
          start: {
            line: 715,
            column: 44
          },
          end: {
            line: 718,
            column: 3
          }
        },
        line: 715
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 717,
            column: 22
          },
          end: {
            line: 717,
            column: 23
          }
        },
        loc: {
          start: {
            line: 717,
            column: 33
          },
          end: {
            line: 717,
            column: 56
          }
        },
        line: 717
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 720,
            column: 2
          },
          end: {
            line: 720,
            column: 3
          }
        },
        loc: {
          start: {
            line: 720,
            column: 48
          },
          end: {
            line: 723,
            column: 3
          }
        },
        line: 720
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 722,
            column: 22
          },
          end: {
            line: 722,
            column: 23
          }
        },
        loc: {
          start: {
            line: 722,
            column: 33
          },
          end: {
            line: 722,
            column: 56
          }
        },
        line: 722
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 725,
            column: 2
          },
          end: {
            line: 725,
            column: 3
          }
        },
        loc: {
          start: {
            line: 725,
            column: 44
          },
          end: {
            line: 728,
            column: 3
          }
        },
        line: 725
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 727,
            column: 22
          },
          end: {
            line: 727,
            column: 23
          }
        },
        loc: {
          start: {
            line: 727,
            column: 33
          },
          end: {
            line: 727,
            column: 56
          }
        },
        line: 727
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 157,
            column: 4
          },
          end: {
            line: 157,
            column: 56
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 157,
            column: 54
          },
          end: {
            line: 157,
            column: 56
          }
        }],
        line: 157
      },
      "1": {
        loc: {
          start: {
            line: 161,
            column: 6
          },
          end: {
            line: 163,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 161,
            column: 6
          },
          end: {
            line: 163,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 161
      },
      "2": {
        loc: {
          start: {
            line: 166,
            column: 6
          },
          end: {
            line: 174,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 166,
            column: 6
          },
          end: {
            line: 174,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 166
      },
      "3": {
        loc: {
          start: {
            line: 171,
            column: 8
          },
          end: {
            line: 173,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 8
          },
          end: {
            line: 173,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 171
      },
      "4": {
        loc: {
          start: {
            line: 225,
            column: 8
          },
          end: {
            line: 229,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 225,
            column: 8
          },
          end: {
            line: 229,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 225
      },
      "5": {
        loc: {
          start: {
            line: 232,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 232,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 232
      },
      "6": {
        loc: {
          start: {
            line: 232,
            column: 10
          },
          end: {
            line: 232,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 10
          },
          end: {
            line: 232,
            column: 21
          }
        }, {
          start: {
            line: 232,
            column: 25
          },
          end: {
            line: 232,
            column: 36
          }
        }],
        line: 232
      },
      "7": {
        loc: {
          start: {
            line: 255,
            column: 23
          },
          end: {
            line: 255,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 255,
            column: 45
          },
          end: {
            line: 255,
            column: 50
          }
        }],
        line: 255
      },
      "8": {
        loc: {
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 275,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 8
          },
          end: {
            line: 275,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "9": {
        loc: {
          start: {
            line: 283,
            column: 6
          },
          end: {
            line: 285,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 283,
            column: 6
          },
          end: {
            line: 285,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 283
      },
      "10": {
        loc: {
          start: {
            line: 324,
            column: 31
          },
          end: {
            line: 326,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 325,
            column: 8
          },
          end: {
            line: 325,
            column: 50
          }
        }, {
          start: {
            line: 326,
            column: 8
          },
          end: {
            line: 326,
            column: 9
          }
        }],
        line: 324
      },
      "11": {
        loc: {
          start: {
            line: 371,
            column: 28
          },
          end: {
            line: 371,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 371,
            column: 28
          },
          end: {
            line: 371,
            column: 44
          }
        }, {
          start: {
            line: 371,
            column: 48
          },
          end: {
            line: 371,
            column: 64
          }
        }],
        line: 371
      },
      "12": {
        loc: {
          start: {
            line: 395,
            column: 6
          },
          end: {
            line: 404,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 396,
            column: 8
          },
          end: {
            line: 397,
            column: 49
          }
        }, {
          start: {
            line: 398,
            column: 8
          },
          end: {
            line: 399,
            column: 47
          }
        }, {
          start: {
            line: 400,
            column: 8
          },
          end: {
            line: 401,
            column: 43
          }
        }, {
          start: {
            line: 402,
            column: 8
          },
          end: {
            line: 403,
            column: 49
          }
        }],
        line: 395
      },
      "13": {
        loc: {
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 409,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 409,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 409
      },
      "14": {
        loc: {
          start: {
            line: 414,
            column: 6
          },
          end: {
            line: 414,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 414,
            column: 6
          },
          end: {
            line: 414,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 414
      },
      "15": {
        loc: {
          start: {
            line: 430,
            column: 19
          },
          end: {
            line: 430,
            column: 47
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 430,
            column: 32
          },
          end: {
            line: 430,
            column: 38
          }
        }, {
          start: {
            line: 430,
            column: 41
          },
          end: {
            line: 430,
            column: 47
          }
        }],
        line: 430
      },
      "16": {
        loc: {
          start: {
            line: 434,
            column: 8
          },
          end: {
            line: 435,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 434,
            column: 8
          },
          end: {
            line: 434,
            column: 30
          }
        }, {
          start: {
            line: 435,
            column: 8
          },
          end: {
            line: 435,
            column: 43
          }
        }],
        line: 434
      },
      "17": {
        loc: {
          start: {
            line: 444,
            column: 4
          },
          end: {
            line: 446,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 444,
            column: 4
          },
          end: {
            line: 446,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 444
      },
      "18": {
        loc: {
          start: {
            line: 461,
            column: 6
          },
          end: {
            line: 461,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 461,
            column: 6
          },
          end: {
            line: 461,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 461
      },
      "19": {
        loc: {
          start: {
            line: 463,
            column: 28
          },
          end: {
            line: 463,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 463,
            column: 28
          },
          end: {
            line: 463,
            column: 38
          }
        }, {
          start: {
            line: 463,
            column: 42
          },
          end: {
            line: 463,
            column: 70
          }
        }, {
          start: {
            line: 463,
            column: 74
          },
          end: {
            line: 463,
            column: 100
          }
        }],
        line: 463
      },
      "20": {
        loc: {
          start: {
            line: 465,
            column: 6
          },
          end: {
            line: 472,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 465,
            column: 6
          },
          end: {
            line: 472,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 465
      },
      "21": {
        loc: {
          start: {
            line: 468,
            column: 8
          },
          end: {
            line: 471,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 468,
            column: 8
          },
          end: {
            line: 471,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 468
      },
      "22": {
        loc: {
          start: {
            line: 499,
            column: 4
          },
          end: {
            line: 509,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 499,
            column: 4
          },
          end: {
            line: 509,
            column: 5
          }
        }, {
          start: {
            line: 504,
            column: 11
          },
          end: {
            line: 509,
            column: 5
          }
        }],
        line: 499
      },
      "23": {
        loc: {
          start: {
            line: 499,
            column: 8
          },
          end: {
            line: 499,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 499,
            column: 8
          },
          end: {
            line: 499,
            column: 48
          }
        }, {
          start: {
            line: 499,
            column: 52
          },
          end: {
            line: 499,
            column: 93
          }
        }],
        line: 499
      },
      "24": {
        loc: {
          start: {
            line: 504,
            column: 11
          },
          end: {
            line: 509,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 504,
            column: 11
          },
          end: {
            line: 509,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 504
      },
      "25": {
        loc: {
          start: {
            line: 524,
            column: 25
          },
          end: {
            line: 524,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 524,
            column: 42
          },
          end: {
            line: 524,
            column: 64
          }
        }, {
          start: {
            line: 524,
            column: 67
          },
          end: {
            line: 524,
            column: 68
          }
        }],
        line: 524
      },
      "26": {
        loc: {
          start: {
            line: 525,
            column: 28
          },
          end: {
            line: 525,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 525,
            column: 47
          },
          end: {
            line: 525,
            column: 71
          }
        }, {
          start: {
            line: 525,
            column: 74
          },
          end: {
            line: 525,
            column: 75
          }
        }],
        line: 525
      },
      "27": {
        loc: {
          start: {
            line: 526,
            column: 42
          },
          end: {
            line: 526,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 526,
            column: 64
          },
          end: {
            line: 526,
            column: 98
          }
        }, {
          start: {
            line: 526,
            column: 101
          },
          end: {
            line: 526,
            column: 102
          }
        }],
        line: 526
      },
      "28": {
        loc: {
          start: {
            line: 536,
            column: 22
          },
          end: {
            line: 536,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 536,
            column: 40
          },
          end: {
            line: 536,
            column: 76
          }
        }, {
          start: {
            line: 536,
            column: 79
          },
          end: {
            line: 536,
            column: 80
          }
        }],
        line: 536
      },
      "29": {
        loc: {
          start: {
            line: 540,
            column: 4
          },
          end: {
            line: 543,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 540,
            column: 4
          },
          end: {
            line: 543,
            column: 29
          }
        }, {
          start: {
            line: 541,
            column: 9
          },
          end: {
            line: 543,
            column: 29
          }
        }],
        line: 540
      },
      "30": {
        loc: {
          start: {
            line: 541,
            column: 9
          },
          end: {
            line: 543,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 541,
            column: 9
          },
          end: {
            line: 543,
            column: 29
          }
        }, {
          start: {
            line: 542,
            column: 9
          },
          end: {
            line: 543,
            column: 29
          }
        }],
        line: 541
      },
      "31": {
        loc: {
          start: {
            line: 542,
            column: 9
          },
          end: {
            line: 543,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 542,
            column: 9
          },
          end: {
            line: 543,
            column: 29
          }
        }, {
          start: {
            line: 543,
            column: 9
          },
          end: {
            line: 543,
            column: 29
          }
        }],
        line: 542
      },
      "32": {
        loc: {
          start: {
            line: 547,
            column: 4
          },
          end: {
            line: 555,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 547,
            column: 4
          },
          end: {
            line: 555,
            column: 5
          }
        }, {
          start: {
            line: 549,
            column: 11
          },
          end: {
            line: 555,
            column: 5
          }
        }],
        line: 547
      },
      "33": {
        loc: {
          start: {
            line: 549,
            column: 11
          },
          end: {
            line: 555,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 549,
            column: 11
          },
          end: {
            line: 555,
            column: 5
          }
        }, {
          start: {
            line: 552,
            column: 11
          },
          end: {
            line: 555,
            column: 5
          }
        }],
        line: 549
      },
      "34": {
        loc: {
          start: {
            line: 552,
            column: 11
          },
          end: {
            line: 555,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 552,
            column: 11
          },
          end: {
            line: 555,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 552
      },
      "35": {
        loc: {
          start: {
            line: 577,
            column: 6
          },
          end: {
            line: 579,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 577,
            column: 6
          },
          end: {
            line: 579,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 577
      },
      "36": {
        loc: {
          start: {
            line: 577,
            column: 10
          },
          end: {
            line: 577,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 577,
            column: 10
          },
          end: {
            line: 577,
            column: 50
          }
        }, {
          start: {
            line: 577,
            column: 54
          },
          end: {
            line: 577,
            column: 95
          }
        }],
        line: 577
      },
      "37": {
        loc: {
          start: {
            line: 605,
            column: 6
          },
          end: {
            line: 607,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 605,
            column: 6
          },
          end: {
            line: 607,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 605
      },
      "38": {
        loc: {
          start: {
            line: 617,
            column: 6
          },
          end: {
            line: 636,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 617,
            column: 6
          },
          end: {
            line: 636,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 617
      },
      "39": {
        loc: {
          start: {
            line: 617,
            column: 10
          },
          end: {
            line: 617,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 617,
            column: 10
          },
          end: {
            line: 617,
            column: 34
          }
        }, {
          start: {
            line: 617,
            column: 38
          },
          end: {
            line: 617,
            column: 53
          }
        }],
        line: 617
      },
      "40": {
        loc: {
          start: {
            line: 621,
            column: 8
          },
          end: {
            line: 624,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 621,
            column: 8
          },
          end: {
            line: 624,
            column: 32
          }
        }, {
          start: {
            line: 622,
            column: 13
          },
          end: {
            line: 624,
            column: 32
          }
        }],
        line: 621
      },
      "41": {
        loc: {
          start: {
            line: 622,
            column: 13
          },
          end: {
            line: 624,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 622,
            column: 13
          },
          end: {
            line: 624,
            column: 32
          }
        }, {
          start: {
            line: 623,
            column: 13
          },
          end: {
            line: 624,
            column: 32
          }
        }],
        line: 622
      },
      "42": {
        loc: {
          start: {
            line: 623,
            column: 13
          },
          end: {
            line: 624,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 623,
            column: 13
          },
          end: {
            line: 624,
            column: 32
          }
        }, {
          start: {
            line: 624,
            column: 13
          },
          end: {
            line: 624,
            column: 32
          }
        }],
        line: 623
      },
      "43": {
        loc: {
          start: {
            line: 656,
            column: 4
          },
          end: {
            line: 656,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 656,
            column: 4
          },
          end: {
            line: 656,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 656
      },
      "44": {
        loc: {
          start: {
            line: 673,
            column: 4
          },
          end: {
            line: 673,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 673,
            column: 4
          },
          end: {
            line: 673,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 673
      },
      "45": {
        loc: {
          start: {
            line: 687,
            column: 6
          },
          end: {
            line: 700,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 688,
            column: 8
          },
          end: {
            line: 690,
            column: 16
          }
        }, {
          start: {
            line: 691,
            column: 8
          },
          end: {
            line: 693,
            column: 16
          }
        }, {
          start: {
            line: 694,
            column: 8
          },
          end: {
            line: 696,
            column: 16
          }
        }, {
          start: {
            line: 697,
            column: 8
          },
          end: {
            line: 699,
            column: 16
          }
        }],
        line: 687
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "83e5d776499938981a8d6b38982c45ba03b45979"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2gb3samthe = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2gb3samthe();
var AdvancedMemoryManager = function () {
  function AdvancedMemoryManager() {
    _classCallCheck(this, AdvancedMemoryManager);
    this.memoryPools = (cov_2gb3samthe().s[0]++, new Map());
    this.memoryLeaks = (cov_2gb3samthe().s[1]++, new Map());
    this.allocationHistory = (cov_2gb3samthe().s[2]++, []);
    this.DEFAULT_POOL_CONFIGS = (cov_2gb3samthe().s[3]++, {
      image: {
        maxSize: 128 * 1024 * 1024,
        type: 'image'
      },
      video: {
        maxSize: 256 * 1024 * 1024,
        type: 'video'
      },
      audio: {
        maxSize: 64 * 1024 * 1024,
        type: 'audio'
      },
      data: {
        maxSize: 64 * 1024 * 1024,
        type: 'data'
      },
      compute: {
        maxSize: 128 * 1024 * 1024,
        type: 'compute'
      },
      cache: {
        maxSize: 32 * 1024 * 1024,
        type: 'cache'
      }
    });
    cov_2gb3samthe().f[0]++;
    cov_2gb3samthe().s[4]++;
    this.gcOptimization = {
      strategy: 'adaptive',
      frequency: 30000,
      threshold: 0.8,
      batchSize: 100,
      pauseTime: 16
    };
    cov_2gb3samthe().s[5]++;
    this.memoryPressure = {
      level: 'normal',
      availableMemory: 0,
      usedMemory: 0,
      totalMemory: 0,
      pressureRatio: 0,
      recommendations: []
    };
    cov_2gb3samthe().s[6]++;
    this.leakDetector = new MemoryLeakDetector();
    cov_2gb3samthe().s[7]++;
    this.gcScheduler = new GCScheduler(this.gcOptimization);
    cov_2gb3samthe().s[8]++;
    this.initializeMemoryManager();
  }
  return _createClass(AdvancedMemoryManager, [{
    key: "initializeMemoryManager",
    value: (function () {
      var _initializeMemoryManager = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[1]++;
        cov_2gb3samthe().s[9]++;
        try {
          cov_2gb3samthe().s[10]++;
          yield this.createDefaultMemoryPools();
          cov_2gb3samthe().s[11]++;
          this.startMemoryMonitoring();
          cov_2gb3samthe().s[12]++;
          yield this.leakDetector.initialize();
          cov_2gb3samthe().s[13]++;
          yield this.gcScheduler.start();
          cov_2gb3samthe().s[14]++;
          this.startPressureMonitoring();
          cov_2gb3samthe().s[15]++;
          console.log('Advanced Memory Manager initialized successfully');
        } catch (error) {
          cov_2gb3samthe().s[16]++;
          console.error('Failed to initialize Advanced Memory Manager:', error);
        }
      });
      function initializeMemoryManager() {
        return _initializeMemoryManager.apply(this, arguments);
      }
      return initializeMemoryManager;
    }())
  }, {
    key: "allocateMemory",
    value: (function () {
      var _allocateMemory = _asyncToGenerator(function* (poolType, size) {
        var metadata = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_2gb3samthe().b[0][0]++, {});
        cov_2gb3samthe().f[2]++;
        cov_2gb3samthe().s[17]++;
        try {
          var pool = (cov_2gb3samthe().s[18]++, this.memoryPools.get(poolType));
          cov_2gb3samthe().s[19]++;
          if (!pool) {
            cov_2gb3samthe().b[1][0]++;
            cov_2gb3samthe().s[20]++;
            throw new Error(`Memory pool not found: ${poolType}`);
          } else {
            cov_2gb3samthe().b[1][1]++;
          }
          cov_2gb3samthe().s[21]++;
          if (!this.canAllocate(pool, size)) {
            cov_2gb3samthe().b[2][0]++;
            cov_2gb3samthe().s[22]++;
            yield this.freeMemoryInPool(pool, size);
            cov_2gb3samthe().s[23]++;
            if (!this.canAllocate(pool, size)) {
              cov_2gb3samthe().b[3][0]++;
              cov_2gb3samthe().s[24]++;
              throw new Error(`Insufficient memory in pool: ${poolType}`);
            } else {
              cov_2gb3samthe().b[3][1]++;
            }
          } else {
            cov_2gb3samthe().b[2][1]++;
          }
          var allocationId = (cov_2gb3samthe().s[25]++, this.generateAllocationId());
          var allocation = (cov_2gb3samthe().s[26]++, {
            id: allocationId,
            poolId: pool.id,
            size: size,
            timestamp: Date.now(),
            lastAccessed: Date.now(),
            accessCount: 1,
            type: poolType,
            metadata: Object.assign({
              source: 'unknown',
              priority: 'medium',
              compressed: false,
              locked: false
            }, metadata)
          });
          cov_2gb3samthe().s[27]++;
          pool.allocations.set(allocationId, allocation);
          cov_2gb3samthe().s[28]++;
          pool.used += size;
          cov_2gb3samthe().s[29]++;
          pool.available -= size;
          cov_2gb3samthe().s[30]++;
          this.allocationHistory.push({
            timestamp: Date.now(),
            allocation: allocation
          });
          cov_2gb3samthe().s[31]++;
          this.updatePoolPerformance(pool);
          cov_2gb3samthe().s[32]++;
          console.log(`Allocated ${size} bytes in pool ${poolType}`);
          cov_2gb3samthe().s[33]++;
          return allocationId;
        } catch (error) {
          cov_2gb3samthe().s[34]++;
          console.error('Failed to allocate memory:', error);
          cov_2gb3samthe().s[35]++;
          throw error;
        }
      });
      function allocateMemory(_x, _x2) {
        return _allocateMemory.apply(this, arguments);
      }
      return allocateMemory;
    }())
  }, {
    key: "freeMemory",
    value: (function () {
      var _freeMemory = _asyncToGenerator(function* (allocationId) {
        cov_2gb3samthe().f[3]++;
        cov_2gb3samthe().s[36]++;
        try {
          var targetPool = (cov_2gb3samthe().s[37]++, null);
          var allocation = (cov_2gb3samthe().s[38]++, null);
          cov_2gb3samthe().s[39]++;
          for (var pool of this.memoryPools.values()) {
            cov_2gb3samthe().s[40]++;
            if (pool.allocations.has(allocationId)) {
              cov_2gb3samthe().b[4][0]++;
              cov_2gb3samthe().s[41]++;
              targetPool = pool;
              cov_2gb3samthe().s[42]++;
              allocation = pool.allocations.get(allocationId);
              cov_2gb3samthe().s[43]++;
              break;
            } else {
              cov_2gb3samthe().b[4][1]++;
            }
          }
          cov_2gb3samthe().s[44]++;
          if ((cov_2gb3samthe().b[6][0]++, !targetPool) || (cov_2gb3samthe().b[6][1]++, !allocation)) {
            cov_2gb3samthe().b[5][0]++;
            cov_2gb3samthe().s[45]++;
            console.warn(`Memory allocation not found: ${allocationId}`);
            cov_2gb3samthe().s[46]++;
            return;
          } else {
            cov_2gb3samthe().b[5][1]++;
          }
          cov_2gb3samthe().s[47]++;
          targetPool.allocations.delete(allocationId);
          cov_2gb3samthe().s[48]++;
          targetPool.used -= allocation.size;
          cov_2gb3samthe().s[49]++;
          targetPool.available += allocation.size;
          cov_2gb3samthe().s[50]++;
          this.updatePoolPerformance(targetPool);
          cov_2gb3samthe().s[51]++;
          console.log(`Freed ${allocation.size} bytes from pool ${targetPool.id}`);
        } catch (error) {
          cov_2gb3samthe().s[52]++;
          console.error('Failed to free memory:', error);
        }
      });
      function freeMemory(_x3) {
        return _freeMemory.apply(this, arguments);
      }
      return freeMemory;
    }())
  }, {
    key: "optimizeMemory",
    value: (function () {
      var _optimizeMemory = _asyncToGenerator(function* () {
        var aggressive = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2gb3samthe().b[7][0]++, false);
        cov_2gb3samthe().f[4]++;
        cov_2gb3samthe().s[53]++;
        try {
          cov_2gb3samthe().s[54]++;
          console.log(`Starting memory optimization (aggressive: ${aggressive})`);
          var freedMemory = (cov_2gb3samthe().s[55]++, 0);
          var optimizedPools = (cov_2gb3samthe().s[56]++, 0);
          var leaksResolved = (cov_2gb3samthe().s[57]++, 0);
          var gcOptimizations = (cov_2gb3samthe().s[58]++, 0);
          cov_2gb3samthe().s[59]++;
          for (var pool of this.memoryPools.values()) {
            var poolFreed = (cov_2gb3samthe().s[60]++, yield this.optimizeMemoryPool(pool, aggressive));
            cov_2gb3samthe().s[61]++;
            if (poolFreed > 0) {
              cov_2gb3samthe().b[8][0]++;
              cov_2gb3samthe().s[62]++;
              freedMemory += poolFreed;
              cov_2gb3samthe().s[63]++;
              optimizedPools++;
            } else {
              cov_2gb3samthe().b[8][1]++;
            }
          }
          var resolvedLeaks = (cov_2gb3samthe().s[64]++, yield this.resolveMemoryLeaks(aggressive));
          cov_2gb3samthe().s[65]++;
          leaksResolved = resolvedLeaks.length;
          cov_2gb3samthe().s[66]++;
          if (aggressive) {
            cov_2gb3samthe().b[9][0]++;
            cov_2gb3samthe().s[67]++;
            gcOptimizations = yield this.optimizeGarbageCollection();
          } else {
            cov_2gb3samthe().b[9][1]++;
          }
          cov_2gb3samthe().s[68]++;
          yield this.updateMemoryPressure();
          cov_2gb3samthe().s[69]++;
          console.log(`Memory optimization completed: ${freedMemory} bytes freed`);
          cov_2gb3samthe().s[70]++;
          return {
            freedMemory: freedMemory,
            optimizedPools: optimizedPools,
            leaksResolved: leaksResolved,
            gcOptimizations: gcOptimizations
          };
        } catch (error) {
          cov_2gb3samthe().s[71]++;
          console.error('Failed to optimize memory:', error);
          cov_2gb3samthe().s[72]++;
          return {
            freedMemory: 0,
            optimizedPools: 0,
            leaksResolved: 0,
            gcOptimizations: 0
          };
        }
      });
      function optimizeMemory() {
        return _optimizeMemory.apply(this, arguments);
      }
      return optimizeMemory;
    }())
  }, {
    key: "getMemoryMetrics",
    value: function getMemoryMetrics() {
      cov_2gb3samthe().f[5]++;
      var totalAllocated = (cov_2gb3samthe().s[73]++, 0);
      var totalAvailable = (cov_2gb3samthe().s[74]++, 0);
      var poolUtilization = (cov_2gb3samthe().s[75]++, {});
      var totalFragmentation = (cov_2gb3samthe().s[76]++, 0);
      cov_2gb3samthe().s[77]++;
      for (var _ref of this.memoryPools.entries()) {
        var _ref2 = _slicedToArray(_ref, 2);
        var poolId = _ref2[0];
        var pool = _ref2[1];
        cov_2gb3samthe().s[78]++;
        totalAllocated += pool.used;
        cov_2gb3samthe().s[79]++;
        totalAvailable += pool.available;
        cov_2gb3samthe().s[80]++;
        poolUtilization[poolId] = pool.used / pool.size * 100;
        cov_2gb3samthe().s[81]++;
        totalFragmentation += pool.performance.fragmentationRatio;
      }
      var fragmentationRatio = (cov_2gb3samthe().s[82]++, this.memoryPools.size > 0 ? (cov_2gb3samthe().b[10][0]++, totalFragmentation / this.memoryPools.size) : (cov_2gb3samthe().b[10][1]++, 0));
      cov_2gb3samthe().s[83]++;
      return {
        totalAllocated: totalAllocated,
        totalAvailable: totalAvailable,
        poolUtilization: poolUtilization,
        memoryPressure: this.memoryPressure,
        leakCount: this.memoryLeaks.size,
        gcEfficiency: this.gcScheduler.getEfficiency(),
        fragmentationRatio: fragmentationRatio
      };
    }
  }, {
    key: "detectMemoryLeaks",
    value: (function () {
      var _detectMemoryLeaks = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[6]++;
        cov_2gb3samthe().s[84]++;
        return yield this.leakDetector.detectLeaks(this.allocationHistory);
      });
      function detectMemoryLeaks() {
        return _detectMemoryLeaks.apply(this, arguments);
      }
      return detectMemoryLeaks;
    }())
  }, {
    key: "createDefaultMemoryPools",
    value: function () {
      var _createDefaultMemoryPools = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[7]++;
        cov_2gb3samthe().s[85]++;
        for (var _ref3 of Object.entries(this.DEFAULT_POOL_CONFIGS)) {
          var _ref4 = _slicedToArray(_ref3, 2);
          var poolType = _ref4[0];
          var config = _ref4[1];
          cov_2gb3samthe().s[86]++;
          yield this.createMemoryPool(poolType, config.maxSize, config.type);
        }
      });
      function createDefaultMemoryPools() {
        return _createDefaultMemoryPools.apply(this, arguments);
      }
      return createDefaultMemoryPools;
    }()
  }, {
    key: "createMemoryPool",
    value: function () {
      var _createMemoryPool = _asyncToGenerator(function* (poolId, maxSize, type) {
        cov_2gb3samthe().f[8]++;
        var pool = (cov_2gb3samthe().s[87]++, {
          id: poolId,
          name: `${type.charAt(0).toUpperCase() + type.slice(1)} Pool`,
          type: type,
          size: maxSize,
          used: 0,
          available: maxSize,
          allocations: new Map(),
          configuration: {
            maxSize: maxSize,
            growthStrategy: 'adaptive',
            evictionPolicy: 'lru',
            compressionEnabled: (cov_2gb3samthe().b[11][0]++, type === 'image') || (cov_2gb3samthe().b[11][1]++, type === 'video')
          },
          performance: {
            allocationSpeed: 0,
            fragmentationRatio: 0,
            hitRate: 0,
            compressionRatio: 0
          }
        });
        cov_2gb3samthe().s[88]++;
        this.memoryPools.set(poolId, pool);
        cov_2gb3samthe().s[89]++;
        console.log(`Created memory pool: ${poolId} (${maxSize} bytes)`);
      });
      function createMemoryPool(_x4, _x5, _x6) {
        return _createMemoryPool.apply(this, arguments);
      }
      return createMemoryPool;
    }()
  }, {
    key: "canAllocate",
    value: function canAllocate(pool, size) {
      cov_2gb3samthe().f[9]++;
      cov_2gb3samthe().s[90]++;
      return pool.available >= size;
    }
  }, {
    key: "freeMemoryInPool",
    value: function () {
      var _freeMemoryInPool = _asyncToGenerator(function* (pool, requiredSize) {
        cov_2gb3samthe().f[10]++;
        var freedSize = (cov_2gb3samthe().s[91]++, 0);
        var allocationsToFree = (cov_2gb3samthe().s[92]++, []);
        var sortedAllocations = (cov_2gb3samthe().s[93]++, Array.from(pool.allocations.values()).sort(function (a, b) {
          cov_2gb3samthe().f[11]++;
          cov_2gb3samthe().s[94]++;
          switch (pool.configuration.evictionPolicy) {
            case 'lru':
              cov_2gb3samthe().b[12][0]++;
              cov_2gb3samthe().s[95]++;
              return a.lastAccessed - b.lastAccessed;
            case 'lfu':
              cov_2gb3samthe().b[12][1]++;
              cov_2gb3samthe().s[96]++;
              return a.accessCount - b.accessCount;
            case 'fifo':
              cov_2gb3samthe().b[12][2]++;
              cov_2gb3samthe().s[97]++;
              return a.timestamp - b.timestamp;
            default:
              cov_2gb3samthe().b[12][3]++;
              cov_2gb3samthe().s[98]++;
              return a.lastAccessed - b.lastAccessed;
          }
        }));
        cov_2gb3samthe().s[99]++;
        for (var allocation of sortedAllocations) {
          cov_2gb3samthe().s[100]++;
          if (allocation.metadata.locked) {
            cov_2gb3samthe().b[13][0]++;
            cov_2gb3samthe().s[101]++;
            continue;
          } else {
            cov_2gb3samthe().b[13][1]++;
          }
          cov_2gb3samthe().s[102]++;
          allocationsToFree.push(allocation.id);
          cov_2gb3samthe().s[103]++;
          freedSize += allocation.size;
          cov_2gb3samthe().s[104]++;
          if (freedSize >= requiredSize) {
            cov_2gb3samthe().b[14][0]++;
            cov_2gb3samthe().s[105]++;
            break;
          } else {
            cov_2gb3samthe().b[14][1]++;
          }
        }
        cov_2gb3samthe().s[106]++;
        for (var allocationId of allocationsToFree) {
          cov_2gb3samthe().s[107]++;
          yield this.freeMemory(allocationId);
        }
        cov_2gb3samthe().s[108]++;
        return freedSize;
      });
      function freeMemoryInPool(_x7, _x8) {
        return _freeMemoryInPool.apply(this, arguments);
      }
      return freeMemoryInPool;
    }()
  }, {
    key: "optimizeMemoryPool",
    value: function () {
      var _optimizeMemoryPool = _asyncToGenerator(function* (pool, aggressive) {
        cov_2gb3samthe().f[12]++;
        var freedMemory = (cov_2gb3samthe().s[109]++, 0);
        var now = (cov_2gb3samthe().s[110]++, Date.now());
        var maxAge = (cov_2gb3samthe().s[111]++, aggressive ? (cov_2gb3samthe().b[15][0]++, 300000) : (cov_2gb3samthe().b[15][1]++, 600000));
        var oldAllocations = (cov_2gb3samthe().s[112]++, Array.from(pool.allocations.values()).filter(function (alloc) {
          cov_2gb3samthe().f[13]++;
          cov_2gb3samthe().s[113]++;
          return (cov_2gb3samthe().b[16][0]++, !alloc.metadata.locked) && (cov_2gb3samthe().b[16][1]++, now - alloc.lastAccessed > maxAge);
        }));
        cov_2gb3samthe().s[114]++;
        for (var allocation of oldAllocations) {
          cov_2gb3samthe().s[115]++;
          yield this.freeMemory(allocation.id);
          cov_2gb3samthe().s[116]++;
          freedMemory += allocation.size;
        }
        cov_2gb3samthe().s[117]++;
        if (pool.performance.fragmentationRatio > 0.3) {
          cov_2gb3samthe().b[17][0]++;
          cov_2gb3samthe().s[118]++;
          yield this.defragmentPool(pool);
        } else {
          cov_2gb3samthe().b[17][1]++;
        }
        cov_2gb3samthe().s[119]++;
        return freedMemory;
      });
      function optimizeMemoryPool(_x9, _x0) {
        return _optimizeMemoryPool.apply(this, arguments);
      }
      return optimizeMemoryPool;
    }()
  }, {
    key: "defragmentPool",
    value: function () {
      var _defragmentPool = _asyncToGenerator(function* (pool) {
        cov_2gb3samthe().f[14]++;
        cov_2gb3samthe().s[120]++;
        console.log(`Defragmenting pool: ${pool.id}`);
        cov_2gb3samthe().s[121]++;
        pool.performance.fragmentationRatio *= 0.5;
      });
      function defragmentPool(_x1) {
        return _defragmentPool.apply(this, arguments);
      }
      return defragmentPool;
    }()
  }, {
    key: "resolveMemoryLeaks",
    value: function () {
      var _resolveMemoryLeaks = _asyncToGenerator(function* (aggressive) {
        cov_2gb3samthe().f[15]++;
        var resolvedLeaks = (cov_2gb3samthe().s[122]++, []);
        cov_2gb3samthe().s[123]++;
        for (var leak of this.memoryLeaks.values()) {
          cov_2gb3samthe().s[124]++;
          if (leak.resolved) {
            cov_2gb3samthe().b[18][0]++;
            cov_2gb3samthe().s[125]++;
            continue;
          } else {
            cov_2gb3samthe().b[18][1]++;
          }
          var shouldResolve = (cov_2gb3samthe().s[126]++, (cov_2gb3samthe().b[19][0]++, aggressive) || (cov_2gb3samthe().b[19][1]++, leak.severity === 'critical') || (cov_2gb3samthe().b[19][2]++, leak.severity === 'severe'));
          cov_2gb3samthe().s[127]++;
          if (shouldResolve) {
            cov_2gb3samthe().b[20][0]++;
            var resolved = (cov_2gb3samthe().s[128]++, yield this.resolveLeak(leak));
            cov_2gb3samthe().s[129]++;
            if (resolved) {
              cov_2gb3samthe().b[21][0]++;
              cov_2gb3samthe().s[130]++;
              leak.resolved = true;
              cov_2gb3samthe().s[131]++;
              resolvedLeaks.push(leak);
            } else {
              cov_2gb3samthe().b[21][1]++;
            }
          } else {
            cov_2gb3samthe().b[20][1]++;
          }
        }
        cov_2gb3samthe().s[132]++;
        return resolvedLeaks;
      });
      function resolveMemoryLeaks(_x10) {
        return _resolveMemoryLeaks.apply(this, arguments);
      }
      return resolveMemoryLeaks;
    }()
  }, {
    key: "resolveLeak",
    value: function () {
      var _resolveLeak = _asyncToGenerator(function* (leak) {
        cov_2gb3samthe().f[16]++;
        cov_2gb3samthe().s[133]++;
        try {
          cov_2gb3samthe().s[134]++;
          console.log(`Resolving memory leak: ${leak.id} (${leak.size} bytes)`);
          cov_2gb3samthe().s[135]++;
          return true;
        } catch (error) {
          cov_2gb3samthe().s[136]++;
          console.error(`Failed to resolve leak ${leak.id}:`, error);
          cov_2gb3samthe().s[137]++;
          return false;
        }
      });
      function resolveLeak(_x11) {
        return _resolveLeak.apply(this, arguments);
      }
      return resolveLeak;
    }()
  }, {
    key: "optimizeGarbageCollection",
    value: function () {
      var _optimizeGarbageCollection = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[17]++;
        var optimizations = (cov_2gb3samthe().s[138]++, 0);
        cov_2gb3samthe().s[139]++;
        if ((cov_2gb3samthe().b[23][0]++, this.memoryPressure.level === 'critical') || (cov_2gb3samthe().b[23][1]++, this.memoryPressure.level === 'emergency')) {
          cov_2gb3samthe().b[22][0]++;
          cov_2gb3samthe().s[140]++;
          this.gcOptimization.strategy = 'aggressive';
          cov_2gb3samthe().s[141]++;
          this.gcOptimization.frequency = 10000;
          cov_2gb3samthe().s[142]++;
          optimizations++;
        } else {
          cov_2gb3samthe().b[22][1]++;
          cov_2gb3samthe().s[143]++;
          if (this.memoryPressure.level === 'warning') {
            cov_2gb3samthe().b[24][0]++;
            cov_2gb3samthe().s[144]++;
            this.gcOptimization.strategy = 'balanced';
            cov_2gb3samthe().s[145]++;
            this.gcOptimization.frequency = 20000;
            cov_2gb3samthe().s[146]++;
            optimizations++;
          } else {
            cov_2gb3samthe().b[24][1]++;
          }
        }
        cov_2gb3samthe().s[147]++;
        yield this.gcScheduler.updateConfiguration(this.gcOptimization);
        cov_2gb3samthe().s[148]++;
        return optimizations;
      });
      function optimizeGarbageCollection() {
        return _optimizeGarbageCollection.apply(this, arguments);
      }
      return optimizeGarbageCollection;
    }()
  }, {
    key: "generateAllocationId",
    value: function generateAllocationId() {
      cov_2gb3samthe().f[18]++;
      cov_2gb3samthe().s[149]++;
      return `alloc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "updatePoolPerformance",
    value: function updatePoolPerformance(pool) {
      cov_2gb3samthe().f[19]++;
      var allocCount = (cov_2gb3samthe().s[150]++, pool.allocations.size);
      var avgAllocSize = (cov_2gb3samthe().s[151]++, allocCount > 0 ? (cov_2gb3samthe().b[25][0]++, pool.used / allocCount) : (cov_2gb3samthe().b[25][1]++, 0));
      var idealAllocCount = (cov_2gb3samthe().s[152]++, avgAllocSize > 0 ? (cov_2gb3samthe().b[26][0]++, pool.size / avgAllocSize) : (cov_2gb3samthe().b[26][1]++, 0));
      cov_2gb3samthe().s[153]++;
      pool.performance.fragmentationRatio = idealAllocCount > 0 ? (cov_2gb3samthe().b[27][0]++, 1 - allocCount / idealAllocCount) : (cov_2gb3samthe().b[27][1]++, 0);
      cov_2gb3samthe().s[154]++;
      pool.performance.hitRate = Math.min(pool.used / pool.size, 1);
    }
  }, {
    key: "updateMemoryPressure",
    value: function () {
      var _updateMemoryPressure = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[20]++;
        var metrics = (cov_2gb3samthe().s[155]++, this.getMemoryMetrics());
        var totalMemory = (cov_2gb3samthe().s[156]++, metrics.totalAllocated + metrics.totalAvailable);
        var usedRatio = (cov_2gb3samthe().s[157]++, totalMemory > 0 ? (cov_2gb3samthe().b[28][0]++, metrics.totalAllocated / totalMemory) : (cov_2gb3samthe().b[28][1]++, 0));
        var level;
        cov_2gb3samthe().s[158]++;
        if (usedRatio < 0.6) {
          cov_2gb3samthe().b[29][0]++;
          cov_2gb3samthe().s[159]++;
          level = 'normal';
        } else {
          cov_2gb3samthe().b[29][1]++;
          cov_2gb3samthe().s[160]++;
          if (usedRatio < 0.8) {
            cov_2gb3samthe().b[30][0]++;
            cov_2gb3samthe().s[161]++;
            level = 'warning';
          } else {
            cov_2gb3samthe().b[30][1]++;
            cov_2gb3samthe().s[162]++;
            if (usedRatio < 0.95) {
              cov_2gb3samthe().b[31][0]++;
              cov_2gb3samthe().s[163]++;
              level = 'critical';
            } else {
              cov_2gb3samthe().b[31][1]++;
              cov_2gb3samthe().s[164]++;
              level = 'emergency';
            }
          }
        }
        var recommendations = (cov_2gb3samthe().s[165]++, []);
        cov_2gb3samthe().s[166]++;
        if (level === 'warning') {
          cov_2gb3samthe().b[32][0]++;
          cov_2gb3samthe().s[167]++;
          recommendations.push('Consider freeing unused allocations');
        } else {
          cov_2gb3samthe().b[32][1]++;
          cov_2gb3samthe().s[168]++;
          if (level === 'critical') {
            cov_2gb3samthe().b[33][0]++;
            cov_2gb3samthe().s[169]++;
            recommendations.push('Free memory immediately');
            cov_2gb3samthe().s[170]++;
            recommendations.push('Reduce image/video quality');
          } else {
            cov_2gb3samthe().b[33][1]++;
            cov_2gb3samthe().s[171]++;
            if (level === 'emergency') {
              cov_2gb3samthe().b[34][0]++;
              cov_2gb3samthe().s[172]++;
              recommendations.push('Emergency memory cleanup required');
              cov_2gb3samthe().s[173]++;
              recommendations.push('Close non-essential features');
            } else {
              cov_2gb3samthe().b[34][1]++;
            }
          }
        }
        cov_2gb3samthe().s[174]++;
        this.memoryPressure = {
          level: level,
          availableMemory: metrics.totalAvailable,
          usedMemory: metrics.totalAllocated,
          totalMemory: totalMemory,
          pressureRatio: usedRatio,
          recommendations: recommendations
        };
      });
      function updateMemoryPressure() {
        return _updateMemoryPressure.apply(this, arguments);
      }
      return updateMemoryPressure;
    }()
  }, {
    key: "startMemoryMonitoring",
    value: function startMemoryMonitoring() {
      var _this = this;
      cov_2gb3samthe().f[21]++;
      cov_2gb3samthe().s[175]++;
      setInterval(function () {
        cov_2gb3samthe().f[22]++;
        cov_2gb3samthe().s[176]++;
        _this.updateMemoryPressure();
      }, 10000);
    }
  }, {
    key: "startPressureMonitoring",
    value: function startPressureMonitoring() {
      var _this2 = this;
      cov_2gb3samthe().f[23]++;
      cov_2gb3samthe().s[177]++;
      setInterval(_asyncToGenerator(function* () {
        cov_2gb3samthe().f[24]++;
        cov_2gb3samthe().s[178]++;
        if ((cov_2gb3samthe().b[36][0]++, _this2.memoryPressure.level === 'critical') || (cov_2gb3samthe().b[36][1]++, _this2.memoryPressure.level === 'emergency')) {
          cov_2gb3samthe().b[35][0]++;
          cov_2gb3samthe().s[179]++;
          yield _this2.optimizeMemory(true);
        } else {
          cov_2gb3samthe().b[35][1]++;
        }
      }), 5000);
    }
  }]);
}();
var MemoryLeakDetector = function () {
  function MemoryLeakDetector() {
    _classCallCheck(this, MemoryLeakDetector);
    this.suspiciousAllocations = (cov_2gb3samthe().s[180]++, new Map());
  }
  return _createClass(MemoryLeakDetector, [{
    key: "initialize",
    value: function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[25]++;
        cov_2gb3samthe().s[181]++;
        console.log('Memory Leak Detector initialized');
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }()
  }, {
    key: "detectLeaks",
    value: function () {
      var _detectLeaks = _asyncToGenerator(function* (allocationHistory) {
        cov_2gb3samthe().f[26]++;
        var leaks = (cov_2gb3samthe().s[182]++, []);
        var now = (cov_2gb3samthe().s[183]++, Date.now());
        var sourceGroups = (cov_2gb3samthe().s[184]++, new Map());
        cov_2gb3samthe().s[185]++;
        allocationHistory.forEach(function (entry) {
          cov_2gb3samthe().f[27]++;
          var source = (cov_2gb3samthe().s[186]++, entry.allocation.metadata.source);
          cov_2gb3samthe().s[187]++;
          if (!sourceGroups.has(source)) {
            cov_2gb3samthe().b[37][0]++;
            cov_2gb3samthe().s[188]++;
            sourceGroups.set(source, []);
          } else {
            cov_2gb3samthe().b[37][1]++;
          }
          cov_2gb3samthe().s[189]++;
          sourceGroups.get(source).push(entry.allocation);
        });
        cov_2gb3samthe().s[190]++;
        for (var _ref6 of sourceGroups.entries()) {
          var _ref7 = _slicedToArray(_ref6, 2);
          var source = _ref7[0];
          var allocations = _ref7[1];
          var totalSize = (cov_2gb3samthe().s[191]++, allocations.reduce(function (sum, alloc) {
            cov_2gb3samthe().f[28]++;
            cov_2gb3samthe().s[192]++;
            return sum + alloc.size;
          }, 0));
          var avgAge = (cov_2gb3samthe().s[193]++, allocations.reduce(function (sum, alloc) {
            cov_2gb3samthe().f[29]++;
            cov_2gb3samthe().s[194]++;
            return sum + (now - alloc.timestamp);
          }, 0) / allocations.length);
          cov_2gb3samthe().s[195]++;
          if ((cov_2gb3samthe().b[39][0]++, allocations.length > 100) && (cov_2gb3samthe().b[39][1]++, avgAge > 600000)) {
            cov_2gb3samthe().b[38][0]++;
            var growthRate = (cov_2gb3samthe().s[196]++, totalSize / (avgAge / 1000));
            var severity = void 0;
            cov_2gb3samthe().s[197]++;
            if (growthRate > 1024 * 1024) {
              cov_2gb3samthe().b[40][0]++;
              cov_2gb3samthe().s[198]++;
              severity = 'critical';
            } else {
              cov_2gb3samthe().b[40][1]++;
              cov_2gb3samthe().s[199]++;
              if (growthRate > 512 * 1024) {
                cov_2gb3samthe().b[41][0]++;
                cov_2gb3samthe().s[200]++;
                severity = 'severe';
              } else {
                cov_2gb3samthe().b[41][1]++;
                cov_2gb3samthe().s[201]++;
                if (growthRate > 256 * 1024) {
                  cov_2gb3samthe().b[42][0]++;
                  cov_2gb3samthe().s[202]++;
                  severity = 'moderate';
                } else {
                  cov_2gb3samthe().b[42][1]++;
                  cov_2gb3samthe().s[203]++;
                  severity = 'minor';
                }
              }
            }
            cov_2gb3samthe().s[204]++;
            leaks.push({
              id: `leak_${source}_${Date.now()}`,
              source: source,
              size: totalSize,
              duration: avgAge,
              growthRate: growthRate,
              severity: severity,
              detectionTime: now,
              resolved: false
            });
          } else {
            cov_2gb3samthe().b[38][1]++;
          }
        }
        cov_2gb3samthe().s[205]++;
        return leaks;
      });
      function detectLeaks(_x12) {
        return _detectLeaks.apply(this, arguments);
      }
      return detectLeaks;
    }()
  }]);
}();
var GCScheduler = function () {
  function GCScheduler(config) {
    _classCallCheck(this, GCScheduler);
    this.efficiency = (cov_2gb3samthe().s[206]++, 0.8);
    this.isRunning = (cov_2gb3samthe().s[207]++, false);
    cov_2gb3samthe().f[30]++;
    cov_2gb3samthe().s[208]++;
    this.configuration = config;
  }
  return _createClass(GCScheduler, [{
    key: "start",
    value: function () {
      var _start = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[31]++;
        cov_2gb3samthe().s[209]++;
        if (this.isRunning) {
          cov_2gb3samthe().b[43][0]++;
          cov_2gb3samthe().s[210]++;
          return;
        } else {
          cov_2gb3samthe().b[43][1]++;
        }
        cov_2gb3samthe().s[211]++;
        this.isRunning = true;
        cov_2gb3samthe().s[212]++;
        this.scheduleGC();
        cov_2gb3samthe().s[213]++;
        console.log('GC Scheduler started');
      });
      function start() {
        return _start.apply(this, arguments);
      }
      return start;
    }()
  }, {
    key: "updateConfiguration",
    value: function () {
      var _updateConfiguration = _asyncToGenerator(function* (config) {
        cov_2gb3samthe().f[32]++;
        cov_2gb3samthe().s[214]++;
        this.configuration = config;
        cov_2gb3samthe().s[215]++;
        console.log('GC configuration updated');
      });
      function updateConfiguration(_x13) {
        return _updateConfiguration.apply(this, arguments);
      }
      return updateConfiguration;
    }()
  }, {
    key: "getEfficiency",
    value: function getEfficiency() {
      cov_2gb3samthe().f[33]++;
      cov_2gb3samthe().s[216]++;
      return this.efficiency;
    }
  }, {
    key: "scheduleGC",
    value: function scheduleGC() {
      var _this3 = this;
      cov_2gb3samthe().f[34]++;
      cov_2gb3samthe().s[217]++;
      if (!this.isRunning) {
        cov_2gb3samthe().b[44][0]++;
        cov_2gb3samthe().s[218]++;
        return;
      } else {
        cov_2gb3samthe().b[44][1]++;
      }
      cov_2gb3samthe().s[219]++;
      setTimeout(function () {
        cov_2gb3samthe().f[35]++;
        cov_2gb3samthe().s[220]++;
        _this3.performGC();
        cov_2gb3samthe().s[221]++;
        _this3.scheduleGC();
      }, this.configuration.frequency);
    }
  }, {
    key: "performGC",
    value: function () {
      var _performGC = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[36]++;
        cov_2gb3samthe().s[222]++;
        try {
          var startTime = (cov_2gb3samthe().s[223]++, Date.now());
          cov_2gb3samthe().s[224]++;
          switch (this.configuration.strategy) {
            case 'aggressive':
              cov_2gb3samthe().b[45][0]++;
              cov_2gb3samthe().s[225]++;
              yield this.aggressiveGC();
              cov_2gb3samthe().s[226]++;
              break;
            case 'balanced':
              cov_2gb3samthe().b[45][1]++;
              cov_2gb3samthe().s[227]++;
              yield this.balancedGC();
              cov_2gb3samthe().s[228]++;
              break;
            case 'conservative':
              cov_2gb3samthe().b[45][2]++;
              cov_2gb3samthe().s[229]++;
              yield this.conservativeGC();
              cov_2gb3samthe().s[230]++;
              break;
            case 'adaptive':
              cov_2gb3samthe().b[45][3]++;
              cov_2gb3samthe().s[231]++;
              yield this.adaptiveGC();
              cov_2gb3samthe().s[232]++;
              break;
          }
          var gcTime = (cov_2gb3samthe().s[233]++, Date.now() - startTime);
          cov_2gb3samthe().s[234]++;
          this.efficiency = Math.max(0, 1 - gcTime / this.configuration.pauseTime);
        } catch (error) {
          cov_2gb3samthe().s[235]++;
          console.error('GC execution failed:', error);
        }
      });
      function performGC() {
        return _performGC.apply(this, arguments);
      }
      return performGC;
    }()
  }, {
    key: "aggressiveGC",
    value: function () {
      var _aggressiveGC = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[37]++;
        cov_2gb3samthe().s[236]++;
        yield new Promise(function (resolve) {
          cov_2gb3samthe().f[38]++;
          cov_2gb3samthe().s[237]++;
          return setTimeout(resolve, 8);
        });
      });
      function aggressiveGC() {
        return _aggressiveGC.apply(this, arguments);
      }
      return aggressiveGC;
    }()
  }, {
    key: "balancedGC",
    value: function () {
      var _balancedGC = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[39]++;
        cov_2gb3samthe().s[238]++;
        yield new Promise(function (resolve) {
          cov_2gb3samthe().f[40]++;
          cov_2gb3samthe().s[239]++;
          return setTimeout(resolve, 12);
        });
      });
      function balancedGC() {
        return _balancedGC.apply(this, arguments);
      }
      return balancedGC;
    }()
  }, {
    key: "conservativeGC",
    value: function () {
      var _conservativeGC = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[41]++;
        cov_2gb3samthe().s[240]++;
        yield new Promise(function (resolve) {
          cov_2gb3samthe().f[42]++;
          cov_2gb3samthe().s[241]++;
          return setTimeout(resolve, 16);
        });
      });
      function conservativeGC() {
        return _conservativeGC.apply(this, arguments);
      }
      return conservativeGC;
    }()
  }, {
    key: "adaptiveGC",
    value: function () {
      var _adaptiveGC = _asyncToGenerator(function* () {
        cov_2gb3samthe().f[43]++;
        cov_2gb3samthe().s[242]++;
        yield new Promise(function (resolve) {
          cov_2gb3samthe().f[44]++;
          cov_2gb3samthe().s[243]++;
          return setTimeout(resolve, 10);
        });
      });
      function adaptiveGC() {
        return _adaptiveGC.apply(this, arguments);
      }
      return adaptiveGC;
    }()
  }]);
}();
export var advancedMemoryManager = (cov_2gb3samthe().s[244]++, new AdvancedMemoryManager());
export default advancedMemoryManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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