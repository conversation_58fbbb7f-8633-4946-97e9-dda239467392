{"version": 3, "names": ["useState", "useEffect", "useCallback", "<PERSON><PERSON>", "Platform", "LocalAuthentication", "encryptionService", "rateLimitingService", "privacyService", "ValidationUtils", "useAuth", "useSecurity", "options", "arguments", "length", "undefined", "cov_1xkfn0j5kn", "b", "f", "_ref", "s", "user", "_ref2", "isSecureDevice", "biometricAvailable", "biometricType", "isAppLocked", "lastActivity", "securityLevel", "_ref3", "_slicedToArray", "securityState", "setSecurityState", "_ref4", "_ref5", "failedAttempts", "setFailedAttempts", "_ref6", "_ref7", "isInitialized", "setIsInitialized", "defaultOptions", "Object", "assign", "requireBiometric", "autoLockTimeout", "maxFailedAttempts", "enableTamperDetection", "initializeSecurity", "_asyncToGenerator", "isSecure", "checkDeviceSecurity", "hasHardwareAsync", "supportedAuthenticationTypesAsync", "initialize", "prev", "Date", "determineSecurityLevel", "error", "console", "authenticateWithBiometrics", "Error", "result", "authenticateAsync", "promptMessage", "cancelLabel", "fallback<PERSON><PERSON><PERSON>", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "success", "updateLastActivity", "handleMaxFailedAttempts", "lockApp", "unlockApp", "authenticated", "validateInput", "input", "type", "hasInjectionPatterns", "warn", "isEmailDomainSafe", "isUrlSafe", "secureStore", "_ref1", "key", "value", "requireAuthentication", "_x", "_x2", "apply", "secureRetrieve", "_ref10", "_x3", "checkRateLimit", "_ref11", "endpoint", "checkLimit", "id", "allowed", "alert", "retryAfter", "text", "_x4", "getPrivacySettings", "updatePrivacySettings", "_ref13", "settings", "_x5", "requestDataExport", "requestDataDeletion", "hasScreenLock", "isEnrolledAsync", "OS", "clearAll", "checkAutoLock", "now", "timeSinceLastActivity", "getTime", "interval", "setInterval", "clearInterval"], "sources": ["useSecurity.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { Alert, Platform } from 'react-native';\nimport * as LocalAuthentication from 'expo-local-authentication';\nimport { encryptionService } from '@/services/encryption';\nimport { rateLimitingService } from '@/services/rateLimiting';\nimport { privacyService } from '@/services/privacy';\nimport { ValidationUtils } from '@/utils/validation';\nimport { useAuth } from './useAuth';\n\n/**\n * Security Hook\n * Provides comprehensive security features and utilities\n */\n\nexport interface SecurityState {\n  isSecureDevice: boolean;\n  biometricAvailable: boolean;\n  biometricType: LocalAuthentication.AuthenticationType[];\n  isAppLocked: boolean;\n  lastActivity: Date | null;\n  securityLevel: 'low' | 'medium' | 'high';\n}\n\nexport interface SecurityOptions {\n  requireBiometric?: boolean;\n  autoLockTimeout?: number; // in milliseconds\n  maxFailedAttempts?: number;\n  enableTamperDetection?: boolean;\n}\n\nexport function useSecurity(options: SecurityOptions = {}) {\n  const { user } = useAuth();\n  const [securityState, setSecurityState] = useState<SecurityState>({\n    isSecureDevice: false,\n    biometricAvailable: false,\n    biometricType: [],\n    isAppLocked: false,\n    lastActivity: null,\n    securityLevel: 'medium',\n  });\n\n  const [failedAttempts, setFailedAttempts] = useState(0);\n  const [isInitialized, setIsInitialized] = useState(false);\n\n  const defaultOptions: Required<SecurityOptions> = {\n    requireBiometric: false,\n    autoLockTimeout: 5 * 60 * 1000, // 5 minutes\n    maxFailedAttempts: 3,\n    enableTamperDetection: true,\n    ...options,\n  };\n\n  /**\n   * Initialize security system\n   */\n  const initializeSecurity = useCallback(async () => {\n    try {\n      // Check device security\n      const isSecure = await checkDeviceSecurity();\n      \n      // Check biometric availability\n      const biometricAvailable = await LocalAuthentication.hasHardwareAsync();\n      const biometricType = await LocalAuthentication.supportedAuthenticationTypesAsync();\n      \n      // Initialize encryption service\n      await encryptionService.initialize();\n      \n      // Initialize rate limiting\n      await rateLimitingService.initialize();\n\n      setSecurityState(prev => ({\n        ...prev,\n        isSecureDevice: isSecure,\n        biometricAvailable,\n        biometricType,\n        lastActivity: new Date(),\n        securityLevel: determineSecurityLevel(isSecure, biometricAvailable),\n      }));\n\n      setIsInitialized(true);\n    } catch (error) {\n      console.error('Security initialization failed:', error);\n      setIsInitialized(true);\n    }\n  }, []);\n\n  /**\n   * Authenticate user with biometrics\n   */\n  const authenticateWithBiometrics = useCallback(async (): Promise<boolean> => {\n    try {\n      if (!securityState.biometricAvailable) {\n        throw new Error('Biometric authentication not available');\n      }\n\n      const result = await LocalAuthentication.authenticateAsync({\n        promptMessage: 'Authenticate to access AceMind',\n        cancelLabel: 'Cancel',\n        fallbackLabel: 'Use Passcode',\n        disableDeviceFallback: false,\n      });\n\n      if (result.success) {\n        setFailedAttempts(0);\n        updateLastActivity();\n        return true;\n      } else {\n        setFailedAttempts(prev => prev + 1);\n        \n        if (failedAttempts >= defaultOptions.maxFailedAttempts - 1) {\n          await handleMaxFailedAttempts();\n        }\n        \n        return false;\n      }\n    } catch (error) {\n      console.error('Biometric authentication failed:', error);\n      return false;\n    }\n  }, [securityState.biometricAvailable, failedAttempts, defaultOptions.maxFailedAttempts]);\n\n  /**\n   * Lock the application\n   */\n  const lockApp = useCallback(() => {\n    setSecurityState(prev => ({\n      ...prev,\n      isAppLocked: true,\n    }));\n  }, []);\n\n  /**\n   * Unlock the application\n   */\n  const unlockApp = useCallback(async (): Promise<boolean> => {\n    try {\n      if (defaultOptions.requireBiometric && securityState.biometricAvailable) {\n        const authenticated = await authenticateWithBiometrics();\n        if (!authenticated) {\n          return false;\n        }\n      }\n\n      setSecurityState(prev => ({\n        ...prev,\n        isAppLocked: false,\n        lastActivity: new Date(),\n      }));\n\n      return true;\n    } catch (error) {\n      console.error('App unlock failed:', error);\n      return false;\n    }\n  }, [defaultOptions.requireBiometric, securityState.biometricAvailable, authenticateWithBiometrics]);\n\n  /**\n   * Update last activity timestamp\n   */\n  const updateLastActivity = useCallback(() => {\n    setSecurityState(prev => ({\n      ...prev,\n      lastActivity: new Date(),\n    }));\n  }, []);\n\n  /**\n   * Check if input is safe\n   */\n  const validateInput = useCallback((input: string, type: 'text' | 'email' | 'url' = 'text'): boolean => {\n    // Check for injection patterns\n    if (ValidationUtils.hasInjectionPatterns(input)) {\n      console.warn('Potentially malicious input detected');\n      return false;\n    }\n\n    // Type-specific validation\n    switch (type) {\n      case 'email':\n        return ValidationUtils.isEmailDomainSafe(input);\n      case 'url':\n        return ValidationUtils.isUrlSafe(input);\n      default:\n        return true;\n    }\n  }, []);\n\n  /**\n   * Secure data storage\n   */\n  const secureStore = useCallback(async (key: string, value: string): Promise<void> => {\n    try {\n      await encryptionService.secureStore(key, value, {\n        requireAuthentication: securityState.securityLevel === 'high',\n      });\n    } catch (error) {\n      console.error('Secure storage failed:', error);\n      throw new Error('Failed to store data securely');\n    }\n  }, [securityState.securityLevel]);\n\n  /**\n   * Secure data retrieval\n   */\n  const secureRetrieve = useCallback(async (key: string): Promise<string | null> => {\n    try {\n      return await encryptionService.secureRetrieve(key, {\n        requireAuthentication: securityState.securityLevel === 'high',\n      });\n    } catch (error) {\n      console.error('Secure retrieval failed:', error);\n      return null;\n    }\n  }, [securityState.securityLevel]);\n\n  /**\n   * Check rate limit for endpoint\n   */\n  const checkRateLimit = useCallback(async (endpoint: string): Promise<boolean> => {\n    if (!user) return true;\n\n    try {\n      const result = await rateLimitingService.checkLimit(endpoint, user.id);\n      \n      if (!result.allowed) {\n        Alert.alert(\n          'Rate Limit Exceeded',\n          `Too many requests. Please try again in ${result.retryAfter} seconds.`,\n          [{ text: 'OK' }]\n        );\n        return false;\n      }\n\n      return true;\n    } catch (error) {\n      console.error('Rate limit check failed:', error);\n      return true; // Allow on error\n    }\n  }, [user]);\n\n  /**\n   * Get privacy settings\n   */\n  const getPrivacySettings = useCallback(async () => {\n    if (!user) return null;\n\n    try {\n      return await privacyService.getPrivacySettings(user.id);\n    } catch (error) {\n      console.error('Failed to get privacy settings:', error);\n      return null;\n    }\n  }, [user]);\n\n  /**\n   * Update privacy settings\n   */\n  const updatePrivacySettings = useCallback(async (settings: any) => {\n    if (!user) throw new Error('User not authenticated');\n\n    try {\n      return await privacyService.updatePrivacySettings(user.id, settings);\n    } catch (error) {\n      console.error('Failed to update privacy settings:', error);\n      throw error;\n    }\n  }, [user]);\n\n  /**\n   * Request data export\n   */\n  const requestDataExport = useCallback(async () => {\n    if (!user) throw new Error('User not authenticated');\n\n    try {\n      return await privacyService.requestDataExport(user.id);\n    } catch (error) {\n      console.error('Failed to request data export:', error);\n      throw error;\n    }\n  }, [user]);\n\n  /**\n   * Request data deletion\n   */\n  const requestDataDeletion = useCallback(async (type: 'partial' | 'complete' = 'complete') => {\n    if (!user) throw new Error('User not authenticated');\n\n    try {\n      return await privacyService.requestDataDeletion(user.id, type);\n    } catch (error) {\n      console.error('Failed to request data deletion:', error);\n      throw error;\n    }\n  }, [user]);\n\n  /**\n   * Check device security\n   */\n  const checkDeviceSecurity = useCallback(async (): Promise<boolean> => {\n    try {\n      // Check if device has screen lock\n      const hasScreenLock = await LocalAuthentication.isEnrolledAsync();\n      \n      // Additional security checks for different platforms\n      if (Platform.OS === 'ios') {\n        // iOS-specific security checks\n        return hasScreenLock;\n      } else if (Platform.OS === 'android') {\n        // Android-specific security checks\n        return hasScreenLock;\n      }\n\n      return hasScreenLock;\n    } catch (error) {\n      console.error('Device security check failed:', error);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Determine security level\n   */\n  const determineSecurityLevel = useCallback((\n    isSecure: boolean, \n    biometricAvailable: boolean\n  ): 'low' | 'medium' | 'high' => {\n    if (isSecure && biometricAvailable) return 'high';\n    if (isSecure || biometricAvailable) return 'medium';\n    return 'low';\n  }, []);\n\n  /**\n   * Handle maximum failed attempts\n   */\n  const handleMaxFailedAttempts = useCallback(async () => {\n    // Lock the app\n    lockApp();\n    \n    // Clear sensitive data\n    await encryptionService.clearAll();\n    \n    // Show security alert\n    Alert.alert(\n      'Security Alert',\n      'Too many failed authentication attempts. The app has been locked for security.',\n      [{ text: 'OK' }]\n    );\n  }, [lockApp]);\n\n  /**\n   * Auto-lock functionality\n   */\n  useEffect(() => {\n    if (!isInitialized || !securityState.lastActivity) return;\n\n    const checkAutoLock = () => {\n      const now = new Date();\n      const timeSinceLastActivity = now.getTime() - securityState.lastActivity!.getTime();\n      \n      if (timeSinceLastActivity >= defaultOptions.autoLockTimeout && !securityState.isAppLocked) {\n        lockApp();\n      }\n    };\n\n    const interval = setInterval(checkAutoLock, 30000); // Check every 30 seconds\n    \n    return () => clearInterval(interval);\n  }, [isInitialized, securityState.lastActivity, securityState.isAppLocked, defaultOptions.autoLockTimeout, lockApp]);\n\n  /**\n   * Initialize on mount\n   */\n  useEffect(() => {\n    initializeSecurity();\n  }, [initializeSecurity]);\n\n  return {\n    // State\n    securityState,\n    isInitialized,\n    failedAttempts,\n\n    // Authentication\n    authenticateWithBiometrics,\n    lockApp,\n    unlockApp,\n    updateLastActivity,\n\n    // Validation\n    validateInput,\n\n    // Storage\n    secureStore,\n    secureRetrieve,\n\n    // Rate limiting\n    checkRateLimit,\n\n    // Privacy\n    getPrivacySettings,\n    updatePrivacySettings,\n    requestDataExport,\n    requestDataDeletion,\n\n    // Utilities\n    checkDeviceSecurity,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,KAAK,EAAEC,QAAQ,QAAQ,cAAc;AAC9C,OAAO,KAAKC,mBAAmB,MAAM,2BAA2B;AAChE,SAASC,iBAAiB;AAC1B,SAASC,mBAAmB;AAC5B,SAASC,cAAc;AACvB,SAASC,eAAe;AACxB,SAASC,OAAO;AAuBhB,OAAO,SAASC,WAAWA,CAAA,EAAgC;EAAA,IAA/BC,OAAwB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;EAAAD,cAAA,GAAAE,CAAA;EACvD,IAAAC,IAAA,IAAAH,cAAA,GAAAI,CAAA,OAAiBV,OAAO,CAAC,CAAC;IAAlBW,IAAI,GAAAF,IAAA,CAAJE,IAAI;EACZ,IAAAC,KAAA,IAAAN,cAAA,GAAAI,CAAA,OAA0CpB,QAAQ,CAAgB;MAChEuB,cAAc,EAAE,KAAK;MACrBC,kBAAkB,EAAE,KAAK;MACzBC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE;IACjB,CAAC,CAAC;IAAAC,KAAA,GAAAC,cAAA,CAAAR,KAAA;IAPKS,aAAa,GAAAF,KAAA;IAAEG,gBAAgB,GAAAH,KAAA;EAStC,IAAAI,KAAA,IAAAjB,cAAA,GAAAI,CAAA,OAA4CpB,QAAQ,CAAC,CAAC,CAAC;IAAAkC,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAAhDE,cAAc,GAAAD,KAAA;IAAEE,iBAAiB,GAAAF,KAAA;EACxC,IAAAG,KAAA,IAAArB,cAAA,GAAAI,CAAA,OAA0CpB,QAAQ,CAAC,KAAK,CAAC;IAAAsC,KAAA,GAAAR,cAAA,CAAAO,KAAA;IAAlDE,aAAa,GAAAD,KAAA;IAAEE,gBAAgB,GAAAF,KAAA;EAEtC,IAAMG,cAAyC,IAAAzB,cAAA,GAAAI,CAAA,OAAAsB,MAAA,CAAAC,MAAA;IAC7CC,gBAAgB,EAAE,KAAK;IACvBC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;IAC9BC,iBAAiB,EAAE,CAAC;IACpBC,qBAAqB,EAAE;EAAI,GACxBnC,OAAO,EACX;EAKD,IAAMoC,kBAAkB,IAAAhC,cAAA,GAAAI,CAAA,OAAGlB,WAAW,CAAA+C,iBAAA,CAAC,aAAY;IAAAjC,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACjD,IAAI;MAEF,IAAM8B,QAAQ,IAAAlC,cAAA,GAAAI,CAAA,aAAS+B,mBAAmB,CAAC,CAAC;MAG5C,IAAM3B,kBAAkB,IAAAR,cAAA,GAAAI,CAAA,aAASf,mBAAmB,CAAC+C,gBAAgB,CAAC,CAAC;MACvE,IAAM3B,aAAa,IAAAT,cAAA,GAAAI,CAAA,aAASf,mBAAmB,CAACgD,iCAAiC,CAAC,CAAC;MAACrC,cAAA,GAAAI,CAAA;MAGpF,MAAMd,iBAAiB,CAACgD,UAAU,CAAC,CAAC;MAACtC,cAAA,GAAAI,CAAA;MAGrC,MAAMb,mBAAmB,CAAC+C,UAAU,CAAC,CAAC;MAACtC,cAAA,GAAAI,CAAA;MAEvCY,gBAAgB,CAAC,UAAAuB,IAAI,EAAK;QAAAvC,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAsB,MAAA,CAAAC,MAAA,KACrBY,IAAI;UACPhC,cAAc,EAAE2B,QAAQ;UACxB1B,kBAAkB,EAAlBA,kBAAkB;UAClBC,aAAa,EAAbA,aAAa;UACbE,YAAY,EAAE,IAAI6B,IAAI,CAAC,CAAC;UACxB5B,aAAa,EAAE6B,sBAAsB,CAACP,QAAQ,EAAE1B,kBAAkB;QAAC;MACrE,CAAE,CAAC;MAACR,cAAA,GAAAI,CAAA;MAEJoB,gBAAgB,CAAC,IAAI,CAAC;IACxB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MAAA1C,cAAA,GAAAI,CAAA;MACduC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAAC1C,cAAA,GAAAI,CAAA;MACxDoB,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC,GAAE,EAAE,CAAC;EAKN,IAAMoB,0BAA0B,IAAA5C,cAAA,GAAAI,CAAA,QAAGlB,WAAW,CAAA+C,iBAAA,CAAC,aAA8B;IAAAjC,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC3E,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACF,IAAI,CAACW,aAAa,CAACP,kBAAkB,EAAE;QAAAR,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACrC,MAAM,IAAIyC,KAAK,CAAC,wCAAwC,CAAC;MAC3D,CAAC;QAAA7C,cAAA,GAAAC,CAAA;MAAA;MAED,IAAM6C,MAAM,IAAA9C,cAAA,GAAAI,CAAA,cAASf,mBAAmB,CAAC0D,iBAAiB,CAAC;QACzDC,aAAa,EAAE,gCAAgC;QAC/CC,WAAW,EAAE,QAAQ;QACrBC,aAAa,EAAE,cAAc;QAC7BC,qBAAqB,EAAE;MACzB,CAAC,CAAC;MAACnD,cAAA,GAAAI,CAAA;MAEH,IAAI0C,MAAM,CAACM,OAAO,EAAE;QAAApD,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAClBgB,iBAAiB,CAAC,CAAC,CAAC;QAACpB,cAAA,GAAAI,CAAA;QACrBiD,kBAAkB,CAAC,CAAC;QAACrD,cAAA,GAAAI,CAAA;QACrB,OAAO,IAAI;MACb,CAAC,MAAM;QAAAJ,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACLgB,iBAAiB,CAAC,UAAAmB,IAAI,EAAI;UAAAvC,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAmC,IAAI,GAAG,CAAC;QAAD,CAAC,CAAC;QAACvC,cAAA,GAAAI,CAAA;QAEpC,IAAIe,cAAc,IAAIM,cAAc,CAACK,iBAAiB,GAAG,CAAC,EAAE;UAAA9B,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UAC1D,MAAMkD,uBAAuB,CAAC,CAAC;QACjC,CAAC;UAAAtD,cAAA,GAAAC,CAAA;QAAA;QAAAD,cAAA,GAAAI,CAAA;QAED,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOsC,KAAK,EAAE;MAAA1C,cAAA,GAAAI,CAAA;MACduC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAAC1C,cAAA,GAAAI,CAAA;MACzD,OAAO,KAAK;IACd;EACF,CAAC,GAAE,CAACW,aAAa,CAACP,kBAAkB,EAAEW,cAAc,EAAEM,cAAc,CAACK,iBAAiB,CAAC,CAAC;EAKxF,IAAMyB,OAAO,IAAAvD,cAAA,GAAAI,CAAA,QAAGlB,WAAW,CAAC,YAAM;IAAAc,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAChCY,gBAAgB,CAAC,UAAAuB,IAAI,EAAK;MAAAvC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAsB,MAAA,CAAAC,MAAA,KACrBY,IAAI;QACP7B,WAAW,EAAE;MAAI;IACnB,CAAE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAKN,IAAM8C,SAAS,IAAAxD,cAAA,GAAAI,CAAA,QAAGlB,WAAW,CAAA+C,iBAAA,CAAC,aAA8B;IAAAjC,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC1D,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACF,IAAI,CAAAJ,cAAA,GAAAC,CAAA,UAAAwB,cAAc,CAACG,gBAAgB,MAAA5B,cAAA,GAAAC,CAAA,UAAIc,aAAa,CAACP,kBAAkB,GAAE;QAAAR,cAAA,GAAAC,CAAA;QACvE,IAAMwD,aAAa,IAAAzD,cAAA,GAAAI,CAAA,cAASwC,0BAA0B,CAAC,CAAC;QAAC5C,cAAA,GAAAI,CAAA;QACzD,IAAI,CAACqD,aAAa,EAAE;UAAAzD,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UAClB,OAAO,KAAK;QACd,CAAC;UAAAJ,cAAA,GAAAC,CAAA;QAAA;MACH,CAAC;QAAAD,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAEDY,gBAAgB,CAAC,UAAAuB,IAAI,EAAK;QAAAvC,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAsB,MAAA,CAAAC,MAAA,KACrBY,IAAI;UACP7B,WAAW,EAAE,KAAK;UAClBC,YAAY,EAAE,IAAI6B,IAAI,CAAC;QAAC;MAC1B,CAAE,CAAC;MAACxC,cAAA,GAAAI,CAAA;MAEJ,OAAO,IAAI;IACb,CAAC,CAAC,OAAOsC,KAAK,EAAE;MAAA1C,cAAA,GAAAI,CAAA;MACduC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAAC1C,cAAA,GAAAI,CAAA;MAC3C,OAAO,KAAK;IACd;EACF,CAAC,GAAE,CAACqB,cAAc,CAACG,gBAAgB,EAAEb,aAAa,CAACP,kBAAkB,EAAEoC,0BAA0B,CAAC,CAAC;EAKnG,IAAMS,kBAAkB,IAAArD,cAAA,GAAAI,CAAA,QAAGlB,WAAW,CAAC,YAAM;IAAAc,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC3CY,gBAAgB,CAAC,UAAAuB,IAAI,EAAK;MAAAvC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAsB,MAAA,CAAAC,MAAA,KACrBY,IAAI;QACP5B,YAAY,EAAE,IAAI6B,IAAI,CAAC;MAAC;IAC1B,CAAE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMkB,aAAa,IAAA1D,cAAA,GAAAI,CAAA,QAAGlB,WAAW,CAAC,UAACyE,KAAa,EAAuD;IAAA,IAArDC,IAA8B,GAAA/D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,UAAG,MAAM;IAAAD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAEvF,IAAIX,eAAe,CAACoE,oBAAoB,CAACF,KAAK,CAAC,EAAE;MAAA3D,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAC/CuC,OAAO,CAACmB,IAAI,CAAC,sCAAsC,CAAC;MAAC9D,cAAA,GAAAI,CAAA;MACrD,OAAO,KAAK;IACd,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAGD,QAAQwD,IAAI;MACV,KAAK,OAAO;QAAA5D,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACV,OAAOX,eAAe,CAACsE,iBAAiB,CAACJ,KAAK,CAAC;MACjD,KAAK,KAAK;QAAA3D,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACR,OAAOX,eAAe,CAACuE,SAAS,CAACL,KAAK,CAAC;MACzC;QAAA3D,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACE,OAAO,IAAI;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAKN,IAAM6D,WAAW,IAAAjE,cAAA,GAAAI,CAAA,QAAGlB,WAAW;IAAA,IAAAgF,KAAA,GAAAjC,iBAAA,CAAC,WAAOkC,GAAW,EAAEC,KAAa,EAAoB;MAAApE,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACnF,IAAI;QAAAJ,cAAA,GAAAI,CAAA;QACF,MAAMd,iBAAiB,CAAC2E,WAAW,CAACE,GAAG,EAAEC,KAAK,EAAE;UAC9CC,qBAAqB,EAAEtD,aAAa,CAACH,aAAa,KAAK;QACzD,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO8B,KAAK,EAAE;QAAA1C,cAAA,GAAAI,CAAA;QACduC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAAC1C,cAAA,GAAAI,CAAA;QAC/C,MAAM,IAAIyC,KAAK,CAAC,+BAA+B,CAAC;MAClD;IACF,CAAC;IAAA,iBAAAyB,EAAA,EAAAC,GAAA;MAAA,OAAAL,KAAA,CAAAM,KAAA,OAAA3E,SAAA;IAAA;EAAA,KAAE,CAACkB,aAAa,CAACH,aAAa,CAAC,CAAC;EAKjC,IAAM6D,cAAc,IAAAzE,cAAA,GAAAI,CAAA,QAAGlB,WAAW;IAAA,IAAAwF,MAAA,GAAAzC,iBAAA,CAAC,WAAOkC,GAAW,EAA6B;MAAAnE,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAChF,IAAI;QAAAJ,cAAA,GAAAI,CAAA;QACF,aAAad,iBAAiB,CAACmF,cAAc,CAACN,GAAG,EAAE;UACjDE,qBAAqB,EAAEtD,aAAa,CAACH,aAAa,KAAK;QACzD,CAAC,CAAC;MACJ,CAAC,CAAC,OAAO8B,KAAK,EAAE;QAAA1C,cAAA,GAAAI,CAAA;QACduC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAAC1C,cAAA,GAAAI,CAAA;QACjD,OAAO,IAAI;MACb;IACF,CAAC;IAAA,iBAAAuE,GAAA;MAAA,OAAAD,MAAA,CAAAF,KAAA,OAAA3E,SAAA;IAAA;EAAA,KAAE,CAACkB,aAAa,CAACH,aAAa,CAAC,CAAC;EAKjC,IAAMgE,cAAc,IAAA5E,cAAA,GAAAI,CAAA,QAAGlB,WAAW;IAAA,IAAA2F,MAAA,GAAA5C,iBAAA,CAAC,WAAO6C,QAAgB,EAAuB;MAAA9E,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAC/E,IAAI,CAACC,IAAI,EAAE;QAAAL,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAAA,OAAO,IAAI;MAAA,CAAC;QAAAJ,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAEvB,IAAI;QACF,IAAM0C,MAAM,IAAA9C,cAAA,GAAAI,CAAA,cAASb,mBAAmB,CAACwF,UAAU,CAACD,QAAQ,EAAEzE,IAAI,CAAC2E,EAAE,CAAC;QAAChF,cAAA,GAAAI,CAAA;QAEvE,IAAI,CAAC0C,MAAM,CAACmC,OAAO,EAAE;UAAAjF,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UACnBjB,KAAK,CAAC+F,KAAK,CACT,qBAAqB,EACrB,0CAA0CpC,MAAM,CAACqC,UAAU,WAAW,EACtE,CAAC;YAAEC,IAAI,EAAE;UAAK,CAAC,CACjB,CAAC;UAACpF,cAAA,GAAAI,CAAA;UACF,OAAO,KAAK;QACd,CAAC;UAAAJ,cAAA,GAAAC,CAAA;QAAA;QAAAD,cAAA,GAAAI,CAAA;QAED,OAAO,IAAI;MACb,CAAC,CAAC,OAAOsC,KAAK,EAAE;QAAA1C,cAAA,GAAAI,CAAA;QACduC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAAC1C,cAAA,GAAAI,CAAA;QACjD,OAAO,IAAI;MACb;IACF,CAAC;IAAA,iBAAAiF,GAAA;MAAA,OAAAR,MAAA,CAAAL,KAAA,OAAA3E,SAAA;IAAA;EAAA,KAAE,CAACQ,IAAI,CAAC,CAAC;EAKV,IAAMiF,kBAAkB,IAAAtF,cAAA,GAAAI,CAAA,QAAGlB,WAAW,CAAA+C,iBAAA,CAAC,aAAY;IAAAjC,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACjD,IAAI,CAACC,IAAI,EAAE;MAAAL,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAEvB,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACF,aAAaZ,cAAc,CAAC8F,kBAAkB,CAACjF,IAAI,CAAC2E,EAAE,CAAC;IACzD,CAAC,CAAC,OAAOtC,KAAK,EAAE;MAAA1C,cAAA,GAAAI,CAAA;MACduC,OAAO,CAACD,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MAAC1C,cAAA,GAAAI,CAAA;MACxD,OAAO,IAAI;IACb;EACF,CAAC,GAAE,CAACC,IAAI,CAAC,CAAC;EAKV,IAAMkF,qBAAqB,IAAAvF,cAAA,GAAAI,CAAA,QAAGlB,WAAW;IAAA,IAAAsG,MAAA,GAAAvD,iBAAA,CAAC,WAAOwD,QAAa,EAAK;MAAAzF,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACjE,IAAI,CAACC,IAAI,EAAE;QAAAL,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAAA,MAAM,IAAIyC,KAAK,CAAC,wBAAwB,CAAC;MAAA,CAAC;QAAA7C,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAErD,IAAI;QAAAJ,cAAA,GAAAI,CAAA;QACF,aAAaZ,cAAc,CAAC+F,qBAAqB,CAAClF,IAAI,CAAC2E,EAAE,EAAES,QAAQ,CAAC;MACtE,CAAC,CAAC,OAAO/C,KAAK,EAAE;QAAA1C,cAAA,GAAAI,CAAA;QACduC,OAAO,CAACD,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;QAAC1C,cAAA,GAAAI,CAAA;QAC3D,MAAMsC,KAAK;MACb;IACF,CAAC;IAAA,iBAAAgD,GAAA;MAAA,OAAAF,MAAA,CAAAhB,KAAA,OAAA3E,SAAA;IAAA;EAAA,KAAE,CAACQ,IAAI,CAAC,CAAC;EAKV,IAAMsF,iBAAiB,IAAA3F,cAAA,GAAAI,CAAA,QAAGlB,WAAW,CAAA+C,iBAAA,CAAC,aAAY;IAAAjC,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAChD,IAAI,CAACC,IAAI,EAAE;MAAAL,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,MAAM,IAAIyC,KAAK,CAAC,wBAAwB,CAAC;IAAA,CAAC;MAAA7C,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAErD,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACF,aAAaZ,cAAc,CAACmG,iBAAiB,CAACtF,IAAI,CAAC2E,EAAE,CAAC;IACxD,CAAC,CAAC,OAAOtC,KAAK,EAAE;MAAA1C,cAAA,GAAAI,CAAA;MACduC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAAC1C,cAAA,GAAAI,CAAA;MACvD,MAAMsC,KAAK;IACb;EACF,CAAC,GAAE,CAACrC,IAAI,CAAC,CAAC;EAKV,IAAMuF,mBAAmB,IAAA5F,cAAA,GAAAI,CAAA,SAAGlB,WAAW,CAAA+C,iBAAA,CAAC,aAAqD;IAAA,IAA9C2B,IAA4B,GAAA/D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,WAAG,UAAU;IAAAD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACtF,IAAI,CAACC,IAAI,EAAE;MAAAL,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,MAAM,IAAIyC,KAAK,CAAC,wBAAwB,CAAC;IAAA,CAAC;MAAA7C,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAErD,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACF,aAAaZ,cAAc,CAACoG,mBAAmB,CAACvF,IAAI,CAAC2E,EAAE,EAAEpB,IAAI,CAAC;IAChE,CAAC,CAAC,OAAOlB,KAAK,EAAE;MAAA1C,cAAA,GAAAI,CAAA;MACduC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MAAC1C,cAAA,GAAAI,CAAA;MACzD,MAAMsC,KAAK;IACb;EACF,CAAC,GAAE,CAACrC,IAAI,CAAC,CAAC;EAKV,IAAM8B,mBAAmB,IAAAnC,cAAA,GAAAI,CAAA,SAAGlB,WAAW,CAAA+C,iBAAA,CAAC,aAA8B;IAAAjC,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACpE,IAAI;MAEF,IAAMyF,aAAa,IAAA7F,cAAA,GAAAI,CAAA,eAASf,mBAAmB,CAACyG,eAAe,CAAC,CAAC;MAAC9F,cAAA,GAAAI,CAAA;MAGlE,IAAIhB,QAAQ,CAAC2G,EAAE,KAAK,KAAK,EAAE;QAAA/F,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAEzB,OAAOyF,aAAa;MACtB,CAAC,MAAM;QAAA7F,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAAA,IAAIhB,QAAQ,CAAC2G,EAAE,KAAK,SAAS,EAAE;UAAA/F,cAAA,GAAAC,CAAA;UAAAD,cAAA,GAAAI,CAAA;UAEpC,OAAOyF,aAAa;QACtB,CAAC;UAAA7F,cAAA,GAAAC,CAAA;QAAA;MAAD;MAACD,cAAA,GAAAI,CAAA;MAED,OAAOyF,aAAa;IACtB,CAAC,CAAC,OAAOnD,KAAK,EAAE;MAAA1C,cAAA,GAAAI,CAAA;MACduC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MAAC1C,cAAA,GAAAI,CAAA;MACtD,OAAO,KAAK;IACd;EACF,CAAC,GAAE,EAAE,CAAC;EAKN,IAAMqC,sBAAsB,IAAAzC,cAAA,GAAAI,CAAA,SAAGlB,WAAW,CAAC,UACzCgD,QAAiB,EACjB1B,kBAA2B,EACG;IAAAR,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC9B,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAAAiC,QAAQ,MAAAlC,cAAA,GAAAC,CAAA,WAAIO,kBAAkB,GAAE;MAAAR,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,MAAM;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAClD,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAAAiC,QAAQ,MAAAlC,cAAA,GAAAC,CAAA,WAAIO,kBAAkB,GAAE;MAAAR,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,QAAQ;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IACpD,OAAO,KAAK;EACd,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMkD,uBAAuB,IAAAtD,cAAA,GAAAI,CAAA,SAAGlB,WAAW,CAAA+C,iBAAA,CAAC,aAAY;IAAAjC,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAEtDmD,OAAO,CAAC,CAAC;IAACvD,cAAA,GAAAI,CAAA;IAGV,MAAMd,iBAAiB,CAAC0G,QAAQ,CAAC,CAAC;IAAChG,cAAA,GAAAI,CAAA;IAGnCjB,KAAK,CAAC+F,KAAK,CACT,gBAAgB,EAChB,gFAAgF,EAChF,CAAC;MAAEE,IAAI,EAAE;IAAK,CAAC,CACjB,CAAC;EACH,CAAC,GAAE,CAAC7B,OAAO,CAAC,CAAC;EAACvD,cAAA,GAAAI,CAAA;EAKdnB,SAAS,CAAC,YAAM;IAAAe,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd,IAAI,CAAAJ,cAAA,GAAAC,CAAA,YAACsB,aAAa,MAAAvB,cAAA,GAAAC,CAAA,WAAI,CAACc,aAAa,CAACJ,YAAY,GAAE;MAAAX,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAE1D,IAAM6F,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAAAjG,cAAA,GAAAE,CAAA;MAC1B,IAAMgG,GAAG,IAAAlG,cAAA,GAAAI,CAAA,SAAG,IAAIoC,IAAI,CAAC,CAAC;MACtB,IAAM2D,qBAAqB,IAAAnG,cAAA,GAAAI,CAAA,SAAG8F,GAAG,CAACE,OAAO,CAAC,CAAC,GAAGrF,aAAa,CAACJ,YAAY,CAAEyF,OAAO,CAAC,CAAC;MAACpG,cAAA,GAAAI,CAAA;MAEpF,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAAAkG,qBAAqB,IAAI1E,cAAc,CAACI,eAAe,MAAA7B,cAAA,GAAAC,CAAA,WAAI,CAACc,aAAa,CAACL,WAAW,GAAE;QAAAV,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACzFmD,OAAO,CAAC,CAAC;MACX,CAAC;QAAAvD,cAAA,GAAAC,CAAA;MAAA;IACH,CAAC;IAED,IAAMoG,QAAQ,IAAArG,cAAA,GAAAI,CAAA,SAAGkG,WAAW,CAACL,aAAa,EAAE,KAAK,CAAC;IAACjG,cAAA,GAAAI,CAAA;IAEnD,OAAO,YAAM;MAAAJ,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAmG,aAAa,CAACF,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAAC9E,aAAa,EAAER,aAAa,CAACJ,YAAY,EAAEI,aAAa,CAACL,WAAW,EAAEe,cAAc,CAACI,eAAe,EAAE0B,OAAO,CAAC,CAAC;EAACvD,cAAA,GAAAI,CAAA;EAKpHnB,SAAS,CAAC,YAAM;IAAAe,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd4B,kBAAkB,CAAC,CAAC;EACtB,CAAC,EAAE,CAACA,kBAAkB,CAAC,CAAC;EAAChC,cAAA,GAAAI,CAAA;EAEzB,OAAO;IAELW,aAAa,EAAbA,aAAa;IACbQ,aAAa,EAAbA,aAAa;IACbJ,cAAc,EAAdA,cAAc;IAGdyB,0BAA0B,EAA1BA,0BAA0B;IAC1BW,OAAO,EAAPA,OAAO;IACPC,SAAS,EAATA,SAAS;IACTH,kBAAkB,EAAlBA,kBAAkB;IAGlBK,aAAa,EAAbA,aAAa;IAGbO,WAAW,EAAXA,WAAW;IACXQ,cAAc,EAAdA,cAAc;IAGdG,cAAc,EAAdA,cAAc;IAGdU,kBAAkB,EAAlBA,kBAAkB;IAClBC,qBAAqB,EAArBA,qBAAqB;IACrBI,iBAAiB,EAAjBA,iBAAiB;IACjBC,mBAAmB,EAAnBA,mBAAmB;IAGnBzD,mBAAmB,EAAnBA;EACF,CAAC;AACH", "ignoreList": []}