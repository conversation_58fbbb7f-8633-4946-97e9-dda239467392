{"version": 3, "names": ["_interopRequireWildcard", "require", "default", "exports", "__esModule", "LocaleProvider", "getLocaleDirection", "useLocaleContext", "_react", "_isLocaleRTL", "defaultLocale", "direction", "locale", "LocaleContext", "createContext", "isLocaleRTL", "props", "children", "needsContext", "createElement", "Provider", "value", "useContext"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.LocaleProvider = LocaleProvider;\nexports.getLocaleDirection = getLocaleDirection;\nexports.useLocaleContext = useLocaleContext;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _isLocaleRTL = require(\"./isLocaleRTL\");\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar defaultLocale = {\n  direction: 'ltr',\n  locale: 'en-US'\n};\nvar LocaleContext = /*#__PURE__*/(0, _react.createContext)(defaultLocale);\nfunction getLocaleDirection(locale) {\n  return (0, _isLocaleRTL.isLocaleRTL)(locale) ? 'rtl' : 'ltr';\n}\nfunction LocaleProvider(props) {\n  var direction = props.direction,\n    locale = props.locale,\n    children = props.children;\n  var needsContext = direction || locale;\n  return needsContext ? /*#__PURE__*/_react.default.createElement(LocaleContext.Provider, {\n    children: children,\n    value: {\n      direction: locale ? getLocaleDirection(locale) : direction,\n      locale\n    }\n  }) : children;\n}\nfunction useLocaleContext() {\n  return (0, _react.useContext)(LocaleContext);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,cAAc,GAAGA,cAAc;AACvCF,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/CH,OAAO,CAACI,gBAAgB,GAAGA,gBAAgB;AAC3C,IAAIC,MAAM,GAAGR,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,IAAIQ,YAAY,GAAGR,OAAO,gBAAgB,CAAC;AAU3C,IAAIS,aAAa,GAAG;EAClBC,SAAS,EAAE,KAAK;EAChBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,aAAa,GAAgB,CAAC,CAAC,EAAEL,MAAM,CAACM,aAAa,EAAEJ,aAAa,CAAC;AACzE,SAASJ,kBAAkBA,CAACM,MAAM,EAAE;EAClC,OAAO,CAAC,CAAC,EAAEH,YAAY,CAACM,WAAW,EAAEH,MAAM,CAAC,GAAG,KAAK,GAAG,KAAK;AAC9D;AACA,SAASP,cAAcA,CAACW,KAAK,EAAE;EAC7B,IAAIL,SAAS,GAAGK,KAAK,CAACL,SAAS;IAC7BC,MAAM,GAAGI,KAAK,CAACJ,MAAM;IACrBK,QAAQ,GAAGD,KAAK,CAACC,QAAQ;EAC3B,IAAIC,YAAY,GAAGP,SAAS,IAAIC,MAAM;EACtC,OAAOM,YAAY,GAAgBV,MAAM,CAACN,OAAO,CAACiB,aAAa,CAACN,aAAa,CAACO,QAAQ,EAAE;IACtFH,QAAQ,EAAEA,QAAQ;IAClBI,KAAK,EAAE;MACLV,SAAS,EAAEC,MAAM,GAAGN,kBAAkB,CAACM,MAAM,CAAC,GAAGD,SAAS;MAC1DC,MAAM,EAANA;IACF;EACF,CAAC,CAAC,GAAGK,QAAQ;AACf;AACA,SAASV,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,CAAC,CAAC,EAAEC,MAAM,CAACc,UAAU,EAAET,aAAa,CAAC;AAC9C", "ignoreList": []}