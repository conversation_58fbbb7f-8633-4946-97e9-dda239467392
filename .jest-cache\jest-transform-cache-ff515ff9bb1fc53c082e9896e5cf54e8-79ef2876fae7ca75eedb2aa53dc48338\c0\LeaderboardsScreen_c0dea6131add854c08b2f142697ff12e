5ce5788bfdf69dd89587ab7bad06a82e
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_qlvp571ft() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\social\\LeaderboardsScreen.tsx";
  var hash = "e1b8bbcb48aff30b903736ba954b5bf03f0e6b3b";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\social\\LeaderboardsScreen.tsx",
    statementMap: {
      "0": {
        start: {
          line: 27,
          column: 30
        },
        end: {
          line: 27,
          column: 39
        }
      },
      "1": {
        start: {
          line: 28,
          column: 42
        },
        end: {
          line: 28,
          column: 69
        }
      },
      "2": {
        start: {
          line: 29,
          column: 56
        },
        end: {
          line: 29,
          column: 90
        }
      },
      "3": {
        start: {
          line: 30,
          column: 32
        },
        end: {
          line: 30,
          column: 64
        }
      },
      "4": {
        start: {
          line: 31,
          column: 42
        },
        end: {
          line: 31,
          column: 81
        }
      },
      "5": {
        start: {
          line: 32,
          column: 32
        },
        end: {
          line: 32,
          column: 46
        }
      },
      "6": {
        start: {
          line: 33,
          column: 46
        },
        end: {
          line: 33,
          column: 61
        }
      },
      "7": {
        start: {
          line: 34,
          column: 38
        },
        end: {
          line: 34,
          column: 53
        }
      },
      "8": {
        start: {
          line: 36,
          column: 2
        },
        end: {
          line: 38,
          column: 9
        }
      },
      "9": {
        start: {
          line: 37,
          column: 4
        },
        end: {
          line: 37,
          column: 23
        }
      },
      "10": {
        start: {
          line: 40,
          column: 2
        },
        end: {
          line: 44,
          column: 28
        }
      },
      "11": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 43,
          column: 5
        }
      },
      "12": {
        start: {
          line: 42,
          column: 6
        },
        end: {
          line: 42,
          column: 53
        }
      },
      "13": {
        start: {
          line: 46,
          column: 27
        },
        end: {
          line: 64,
          column: 3
        }
      },
      "14": {
        start: {
          line: 47,
          column: 4
        },
        end: {
          line: 63,
          column: 5
        }
      },
      "15": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 23
        }
      },
      "16": {
        start: {
          line: 49,
          column: 44
        },
        end: {
          line: 49,
          column: 81
        }
      },
      "17": {
        start: {
          line: 51,
          column: 6
        },
        end: {
          line: 58,
          column: 7
        }
      },
      "18": {
        start: {
          line: 52,
          column: 8
        },
        end: {
          line: 52,
          column: 60
        }
      },
      "19": {
        start: {
          line: 54,
          column: 8
        },
        end: {
          line: 54,
          column: 30
        }
      },
      "20": {
        start: {
          line: 55,
          column: 8
        },
        end: {
          line: 57,
          column: 9
        }
      },
      "21": {
        start: {
          line: 56,
          column: 10
        },
        end: {
          line: 56,
          column: 42
        }
      },
      "22": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 58
        }
      },
      "23": {
        start: {
          line: 62,
          column: 6
        },
        end: {
          line: 62,
          column: 24
        }
      },
      "24": {
        start: {
          line: 66,
          column: 33
        },
        end: {
          line: 88,
          column: 3
        }
      },
      "25": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 87,
          column: 5
        }
      },
      "26": {
        start: {
          line: 68,
          column: 6
        },
        end: {
          line: 68,
          column: 30
        }
      },
      "27": {
        start: {
          line: 69,
          column: 46
        },
        end: {
          line: 72,
          column: 8
        }
      },
      "28": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 76,
          column: 7
        }
      },
      "29": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 75,
          column: 42
        }
      },
      "30": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 82,
          column: 7
        }
      },
      "31": {
        start: {
          line: 79,
          column: 8
        },
        end: {
          line: 79,
          column: 46
        }
      },
      "32": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 30
        }
      },
      "33": {
        start: {
          line: 84,
          column: 6
        },
        end: {
          line: 84,
          column: 65
        }
      },
      "34": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 86,
          column: 31
        }
      },
      "35": {
        start: {
          line: 90,
          column: 24
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "36": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 91,
          column: 24
        }
      },
      "37": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 92,
          column: 29
        }
      },
      "38": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 95,
          column: 5
        }
      },
      "39": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 94,
          column: 59
        }
      },
      "40": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 96,
          column: 25
        }
      },
      "41": {
        start: {
          line: 99,
          column: 22
        },
        end: {
          line: 110,
          column: 3
        }
      },
      "42": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "43": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 52
        }
      },
      "44": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 104,
          column: 51
        }
      },
      "45": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 51
        }
      },
      "46": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 52
        }
      },
      "47": {
        start: {
          line: 112,
          column: 24
        },
        end: {
          line: 123,
          column: 3
        }
      },
      "48": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 122,
          column: 5
        }
      },
      "49": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 115,
          column: 71
        }
      },
      "50": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 71
        }
      },
      "51": {
        start: {
          line: 119,
          column: 8
        },
        end: {
          line: 119,
          column: 71
        }
      },
      "52": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 71
        }
      },
      "53": {
        start: {
          line: 125,
          column: 22
        },
        end: {
          line: 136,
          column: 3
        }
      },
      "54": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 135,
          column: 5
        }
      },
      "55": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 128,
          column: 43
        }
      },
      "56": {
        start: {
          line: 130,
          column: 8
        },
        end: {
          line: 130,
          column: 39
        }
      },
      "57": {
        start: {
          line: 132,
          column: 8
        },
        end: {
          line: 132,
          column: 38
        }
      },
      "58": {
        start: {
          line: 134,
          column: 8
        },
        end: {
          line: 134,
          column: 32
        }
      },
      "59": {
        start: {
          line: 138,
          column: 26
        },
        end: {
          line: 157,
          column: 3
        }
      },
      "60": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 156,
          column: 5
        }
      },
      "61": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 24
        }
      },
      "62": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 143,
          column: 34
        }
      },
      "63": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 145,
          column: 29
        }
      },
      "64": {
        start: {
          line: 147,
          column: 8
        },
        end: {
          line: 147,
          column: 23
        }
      },
      "65": {
        start: {
          line: 149,
          column: 8
        },
        end: {
          line: 149,
          column: 23
        }
      },
      "66": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 151,
          column: 32
        }
      },
      "67": {
        start: {
          line: 153,
          column: 8
        },
        end: {
          line: 153,
          column: 25
        }
      },
      "68": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 155,
          column: 22
        }
      },
      "69": {
        start: {
          line: 159,
          column: 31
        },
        end: {
          line: 178,
          column: 3
        }
      },
      "70": {
        start: {
          line: 160,
          column: 23
        },
        end: {
          line: 160,
          column: 65
        }
      },
      "71": {
        start: {
          line: 162,
          column: 4
        },
        end: {
          line: 177,
          column: 6
        }
      },
      "72": {
        start: {
          line: 166,
          column: 23
        },
        end: {
          line: 166,
          column: 58
        }
      },
      "73": {
        start: {
          line: 180,
          column: 33
        },
        end: {
          line: 253,
          column: 3
        }
      },
      "74": {
        start: {
          line: 181,
          column: 23
        },
        end: {
          line: 181,
          column: 48
        }
      },
      "75": {
        start: {
          line: 182,
          column: 21
        },
        end: {
          line: 182,
          column: 44
        }
      },
      "76": {
        start: {
          line: 183,
          column: 26
        },
        end: {
          line: 183,
          column: 86
        }
      },
      "77": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 252,
          column: 6
        }
      },
      "78": {
        start: {
          line: 193,
          column: 23
        },
        end: {
          line: 193,
          column: 59
        }
      },
      "79": {
        start: {
          line: 255,
          column: 29
        },
        end: {
          line: 290,
          column: 3
        }
      },
      "80": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 256,
          column: 57
        }
      },
      "81": {
        start: {
          line: 256,
          column: 45
        },
        end: {
          line: 256,
          column: 57
        }
      },
      "82": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 289,
          column: 6
        }
      },
      "83": {
        start: {
          line: 292,
          column: 2
        },
        end: {
          line: 299,
          column: 3
        }
      },
      "84": {
        start: {
          line: 293,
          column: 4
        },
        end: {
          line: 298,
          column: 6
        }
      },
      "85": {
        start: {
          line: 301,
          column: 2
        },
        end: {
          line: 352,
          column: 4
        }
      },
      "86": {
        start: {
          line: 355,
          column: 15
        },
        end: {
          line: 609,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "LeaderboardsScreen",
        decl: {
          start: {
            line: 26,
            column: 16
          },
          end: {
            line: 26,
            column: 34
          }
        },
        loc: {
          start: {
            line: 26,
            column: 85
          },
          end: {
            line: 353,
            column: 1
          }
        },
        line: 26
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 36,
            column: 12
          },
          end: {
            line: 36,
            column: 13
          }
        },
        loc: {
          start: {
            line: 36,
            column: 18
          },
          end: {
            line: 38,
            column: 3
          }
        },
        line: 36
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 40,
            column: 12
          },
          end: {
            line: 40,
            column: 13
          }
        },
        loc: {
          start: {
            line: 40,
            column: 18
          },
          end: {
            line: 44,
            column: 3
          }
        },
        line: 40
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 46,
            column: 27
          },
          end: {
            line: 46,
            column: 28
          }
        },
        loc: {
          start: {
            line: 46,
            column: 39
          },
          end: {
            line: 64,
            column: 3
          }
        },
        line: 46
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 66,
            column: 33
          },
          end: {
            line: 66,
            column: 34
          }
        },
        loc: {
          start: {
            line: 66,
            column: 66
          },
          end: {
            line: 88,
            column: 3
          }
        },
        line: 66
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 90,
            column: 24
          },
          end: {
            line: 90,
            column: 25
          }
        },
        loc: {
          start: {
            line: 90,
            column: 36
          },
          end: {
            line: 97,
            column: 3
          }
        },
        line: 90
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 99,
            column: 22
          },
          end: {
            line: 99,
            column: 23
          }
        },
        loc: {
          start: {
            line: 99,
            column: 40
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 99
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 112,
            column: 24
          },
          end: {
            line: 112,
            column: 25
          }
        },
        loc: {
          start: {
            line: 112,
            column: 42
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 112
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 125,
            column: 22
          },
          end: {
            line: 125,
            column: 23
          }
        },
        loc: {
          start: {
            line: 125,
            column: 59
          },
          end: {
            line: 136,
            column: 3
          }
        },
        line: 125
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 138,
            column: 26
          },
          end: {
            line: 138,
            column: 27
          }
        },
        loc: {
          start: {
            line: 138,
            column: 48
          },
          end: {
            line: 157,
            column: 3
          }
        },
        line: 138
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 159,
            column: 31
          },
          end: {
            line: 159,
            column: 32
          }
        },
        loc: {
          start: {
            line: 159,
            column: 61
          },
          end: {
            line: 178,
            column: 3
          }
        },
        line: 159
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 166,
            column: 17
          },
          end: {
            line: 166,
            column: 18
          }
        },
        loc: {
          start: {
            line: 166,
            column: 23
          },
          end: {
            line: 166,
            column: 58
          }
        },
        line: 166
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 180,
            column: 33
          },
          end: {
            line: 180,
            column: 34
          }
        },
        loc: {
          start: {
            line: 180,
            column: 77
          },
          end: {
            line: 253,
            column: 3
          }
        },
        line: 180
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 193,
            column: 17
          },
          end: {
            line: 193,
            column: 18
          }
        },
        loc: {
          start: {
            line: 193,
            column: 23
          },
          end: {
            line: 193,
            column: 59
          }
        },
        line: 193
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 255,
            column: 29
          },
          end: {
            line: 255,
            column: 30
          }
        },
        loc: {
          start: {
            line: 255,
            column: 35
          },
          end: {
            line: 290,
            column: 3
          }
        },
        line: 255
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 41,
            column: 4
          },
          end: {
            line: 43,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 41,
            column: 4
          },
          end: {
            line: 43,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 41
      },
      "1": {
        loc: {
          start: {
            line: 51,
            column: 6
          },
          end: {
            line: 58,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 6
          },
          end: {
            line: 58,
            column: 7
          }
        }, {
          start: {
            line: 53,
            column: 13
          },
          end: {
            line: 58,
            column: 7
          }
        }],
        line: 51
      },
      "2": {
        loc: {
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 57,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 55,
            column: 8
          },
          end: {
            line: 57,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 55
      },
      "3": {
        loc: {
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 55,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 55,
            column: 12
          },
          end: {
            line: 55,
            column: 27
          }
        }, {
          start: {
            line: 55,
            column: 31
          },
          end: {
            line: 55,
            column: 51
          }
        }],
        line: 55
      },
      "4": {
        loc: {
          start: {
            line: 71,
            column: 8
          },
          end: {
            line: 71,
            column: 118
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 71,
            column: 28
          },
          end: {
            line: 71,
            column: 83
          }
        }, {
          start: {
            line: 71,
            column: 86
          },
          end: {
            line: 71,
            column: 118
          }
        }],
        line: 71
      },
      "5": {
        loc: {
          start: {
            line: 74,
            column: 6
          },
          end: {
            line: 76,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 6
          },
          end: {
            line: 76,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 74
      },
      "6": {
        loc: {
          start: {
            line: 78,
            column: 6
          },
          end: {
            line: 82,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 78,
            column: 6
          },
          end: {
            line: 82,
            column: 7
          }
        }, {
          start: {
            line: 80,
            column: 13
          },
          end: {
            line: 82,
            column: 7
          }
        }],
        line: 78
      },
      "7": {
        loc: {
          start: {
            line: 93,
            column: 4
          },
          end: {
            line: 95,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 4
          },
          end: {
            line: 95,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "8": {
        loc: {
          start: {
            line: 100,
            column: 4
          },
          end: {
            line: 109,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 101,
            column: 6
          },
          end: {
            line: 102,
            column: 52
          }
        }, {
          start: {
            line: 103,
            column: 6
          },
          end: {
            line: 104,
            column: 51
          }
        }, {
          start: {
            line: 105,
            column: 6
          },
          end: {
            line: 106,
            column: 51
          }
        }, {
          start: {
            line: 107,
            column: 6
          },
          end: {
            line: 108,
            column: 52
          }
        }],
        line: 100
      },
      "9": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 114,
            column: 6
          },
          end: {
            line: 115,
            column: 71
          }
        }, {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 117,
            column: 71
          }
        }, {
          start: {
            line: 118,
            column: 6
          },
          end: {
            line: 119,
            column: 71
          }
        }, {
          start: {
            line: 120,
            column: 6
          },
          end: {
            line: 121,
            column: 71
          }
        }],
        line: 113
      },
      "10": {
        loc: {
          start: {
            line: 126,
            column: 4
          },
          end: {
            line: 135,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 127,
            column: 6
          },
          end: {
            line: 128,
            column: 43
          }
        }, {
          start: {
            line: 129,
            column: 6
          },
          end: {
            line: 130,
            column: 39
          }
        }, {
          start: {
            line: 131,
            column: 6
          },
          end: {
            line: 132,
            column: 38
          }
        }, {
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 134,
            column: 32
          }
        }],
        line: 126
      },
      "11": {
        loc: {
          start: {
            line: 139,
            column: 4
          },
          end: {
            line: 156,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 140,
            column: 6
          },
          end: {
            line: 141,
            column: 24
          }
        }, {
          start: {
            line: 142,
            column: 6
          },
          end: {
            line: 143,
            column: 34
          }
        }, {
          start: {
            line: 144,
            column: 6
          },
          end: {
            line: 145,
            column: 29
          }
        }, {
          start: {
            line: 146,
            column: 6
          },
          end: {
            line: 147,
            column: 23
          }
        }, {
          start: {
            line: 148,
            column: 6
          },
          end: {
            line: 149,
            column: 23
          }
        }, {
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 151,
            column: 32
          }
        }, {
          start: {
            line: 152,
            column: 6
          },
          end: {
            line: 153,
            column: 25
          }
        }, {
          start: {
            line: 154,
            column: 6
          },
          end: {
            line: 155,
            column: 22
          }
        }],
        line: 139
      },
      "12": {
        loc: {
          start: {
            line: 165,
            column: 39
          },
          end: {
            line: 165,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 165,
            column: 39
          },
          end: {
            line: 165,
            column: 49
          }
        }, {
          start: {
            line: 165,
            column: 53
          },
          end: {
            line: 165,
            column: 71
          }
        }],
        line: 165
      },
      "13": {
        loc: {
          start: {
            line: 171,
            column: 17
          },
          end: {
            line: 171,
            column: 51
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 171,
            column: 30
          },
          end: {
            line: 171,
            column: 39
          }
        }, {
          start: {
            line: 171,
            column: 42
          },
          end: {
            line: 171,
            column: 51
          }
        }],
        line: 171
      },
      "14": {
        loc: {
          start: {
            line: 173,
            column: 38
          },
          end: {
            line: 173,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 173,
            column: 38
          },
          end: {
            line: 173,
            column: 48
          }
        }, {
          start: {
            line: 173,
            column: 52
          },
          end: {
            line: 173,
            column: 74
          }
        }],
        line: 173
      },
      "15": {
        loc: {
          start: {
            line: 183,
            column: 26
          },
          end: {
            line: 183,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 26
          },
          end: {
            line: 183,
            column: 43
          }
        }, {
          start: {
            line: 183,
            column: 47
          },
          end: {
            line: 183,
            column: 86
          }
        }],
        line: 183
      },
      "16": {
        loc: {
          start: {
            line: 190,
            column: 10
          },
          end: {
            line: 190,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 190,
            column: 10
          },
          end: {
            line: 190,
            column: 23
          }
        }, {
          start: {
            line: 190,
            column: 27
          },
          end: {
            line: 190,
            column: 50
          }
        }],
        line: 190
      },
      "17": {
        loc: {
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 191,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 10
          },
          end: {
            line: 191,
            column: 25
          }
        }, {
          start: {
            line: 191,
            column: 29
          },
          end: {
            line: 191,
            column: 44
          }
        }],
        line: 191
      },
      "18": {
        loc: {
          start: {
            line: 196,
            column: 11
          },
          end: {
            line: 209,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 197,
            column: 12
          },
          end: {
            line: 204,
            column: 29
          }
        }, {
          start: {
            line: 206,
            column: 12
          },
          end: {
            line: 208,
            column: 19
          }
        }],
        line: 196
      },
      "19": {
        loc: {
          start: {
            line: 218,
            column: 13
          },
          end: {
            line: 218,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 218,
            column: 13
          },
          end: {
            line: 218,
            column: 45
          }
        }, {
          start: {
            line: 218,
            column: 49
          },
          end: {
            line: 218,
            column: 64
          }
        }],
        line: 218
      },
      "20": {
        loc: {
          start: {
            line: 221,
            column: 13
          },
          end: {
            line: 223,
            column: 34
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 222,
            column: 16
          },
          end: {
            line: 222,
            column: 93
          }
        }, {
          start: {
            line: 223,
            column: 16
          },
          end: {
            line: 223,
            column: 34
          }
        }],
        line: 221
      },
      "21": {
        loc: {
          start: {
            line: 221,
            column: 13
          },
          end: {
            line: 221,
            column: 86
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 221,
            column: 13
          },
          end: {
            line: 221,
            column: 46
          }
        }, {
          start: {
            line: 221,
            column: 50
          },
          end: {
            line: 221,
            column: 86
          }
        }],
        line: 221
      },
      "22": {
        loc: {
          start: {
            line: 230,
            column: 38
          },
          end: {
            line: 230,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 230,
            column: 38
          },
          end: {
            line: 230,
            column: 67
          }
        }, {
          start: {
            line: 230,
            column: 71
          },
          end: {
            line: 230,
            column: 80
          }
        }],
        line: 230
      },
      "23": {
        loc: {
          start: {
            line: 232,
            column: 11
          },
          end: {
            line: 249,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 11
          },
          end: {
            line: 232,
            column: 34
          }
        }, {
          start: {
            line: 233,
            column: 12
          },
          end: {
            line: 248,
            column: 19
          }
        }],
        line: 232
      },
      "24": {
        loc: {
          start: {
            line: 235,
            column: 14
          },
          end: {
            line: 235,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 235,
            column: 38
          },
          end: {
            line: 235,
            column: 51
          }
        }, {
          start: {
            line: 235,
            column: 54
          },
          end: {
            line: 235,
            column: 69
          }
        }],
        line: 235
      },
      "25": {
        loc: {
          start: {
            line: 238,
            column: 22
          },
          end: {
            line: 238,
            column: 71
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 238,
            column: 46
          },
          end: {
            line: 238,
            column: 56
          }
        }, {
          start: {
            line: 238,
            column: 59
          },
          end: {
            line: 238,
            column: 71
          }
        }],
        line: 238
      },
      "26": {
        loc: {
          start: {
            line: 240,
            column: 23
          },
          end: {
            line: 240,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 240,
            column: 47
          },
          end: {
            line: 240,
            column: 56
          }
        }, {
          start: {
            line: 240,
            column: 59
          },
          end: {
            line: 240,
            column: 68
          }
        }],
        line: 240
      },
      "27": {
        loc: {
          start: {
            line: 244,
            column: 16
          },
          end: {
            line: 244,
            column: 79
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 244,
            column: 40
          },
          end: {
            line: 244,
            column: 57
          }
        }, {
          start: {
            line: 244,
            column: 60
          },
          end: {
            line: 244,
            column: 79
          }
        }],
        line: 244
      },
      "28": {
        loc: {
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 256,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 256,
            column: 4
          },
          end: {
            line: 256,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 256
      },
      "29": {
        loc: {
          start: {
            line: 256,
            column: 8
          },
          end: {
            line: 256,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 256,
            column: 8
          },
          end: {
            line: 256,
            column: 21
          }
        }, {
          start: {
            line: 256,
            column: 25
          },
          end: {
            line: 256,
            column: 43
          }
        }],
        line: 256
      },
      "30": {
        loc: {
          start: {
            line: 268,
            column: 47
          },
          end: {
            line: 268,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 268,
            column: 47
          },
          end: {
            line: 268,
            column: 76
          }
        }, {
          start: {
            line: 268,
            column: 80
          },
          end: {
            line: 268,
            column: 89
          }
        }],
        line: 268
      },
      "31": {
        loc: {
          start: {
            line: 272,
            column: 11
          },
          end: {
            line: 286,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 272,
            column: 11
          },
          end: {
            line: 272,
            column: 41
          }
        }, {
          start: {
            line: 273,
            column: 12
          },
          end: {
            line: 285,
            column: 19
          }
        }],
        line: 272
      },
      "32": {
        loc: {
          start: {
            line: 275,
            column: 22
          },
          end: {
            line: 275,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 275,
            column: 53
          },
          end: {
            line: 275,
            column: 66
          }
        }, {
          start: {
            line: 275,
            column: 69
          },
          end: {
            line: 275,
            column: 84
          }
        }],
        line: 275
      },
      "33": {
        loc: {
          start: {
            line: 277,
            column: 23
          },
          end: {
            line: 277,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 277,
            column: 54
          },
          end: {
            line: 277,
            column: 63
          }
        }, {
          start: {
            line: 277,
            column: 66
          },
          end: {
            line: 277,
            column: 75
          }
        }],
        line: 277
      },
      "34": {
        loc: {
          start: {
            line: 281,
            column: 16
          },
          end: {
            line: 281,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 281,
            column: 47
          },
          end: {
            line: 281,
            column: 64
          }
        }, {
          start: {
            line: 281,
            column: 67
          },
          end: {
            line: 281,
            column: 86
          }
        }],
        line: 281
      },
      "35": {
        loc: {
          start: {
            line: 283,
            column: 17
          },
          end: {
            line: 283,
            column: 56
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 283,
            column: 48
          },
          end: {
            line: 283,
            column: 51
          }
        }, {
          start: {
            line: 283,
            column: 54
          },
          end: {
            line: 283,
            column: 56
          }
        }],
        line: 283
      },
      "36": {
        loc: {
          start: {
            line: 292,
            column: 2
          },
          end: {
            line: 299,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 292,
            column: 2
          },
          end: {
            line: 299,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 292
      },
      "37": {
        loc: {
          start: {
            line: 332,
            column: 9
          },
          end: {
            line: 349,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 333,
            column: 10
          },
          end: {
            line: 336,
            column: 17
          }
        }, {
          start: {
            line: 337,
            column: 12
          },
          end: {
            line: 349,
            column: 9
          }
        }],
        line: 332
      },
      "38": {
        loc: {
          start: {
            line: 337,
            column: 12
          },
          end: {
            line: 349,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 338,
            column: 10
          },
          end: {
            line: 344,
            column: 17
          }
        }, {
          start: {
            line: 346,
            column: 10
          },
          end: {
            line: 348,
            column: 17
          }
        }],
        line: 337
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0, 0],
      "9": [0, 0, 0, 0],
      "10": [0, 0, 0, 0],
      "11": [0, 0, 0, 0, 0, 0, 0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e1b8bbcb48aff30b903736ba954b5bf03f0e6b3b"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_qlvp571ft = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_qlvp571ft();
import React, { useState, useEffect } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, RefreshControl, ActivityIndicator } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { socialService } from "../../services/social/SocialService";
import { useAuth } from "../../contexts/AuthContext";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export function LeaderboardsScreen(_ref) {
  var onNavigateToProfile = _ref.onNavigateToProfile;
  cov_qlvp571ft().f[0]++;
  var _ref2 = (cov_qlvp571ft().s[0]++, useAuth()),
    isAuthenticated = _ref2.isAuthenticated;
  var _ref3 = (cov_qlvp571ft().s[1]++, useState([])),
    _ref4 = _slicedToArray(_ref3, 2),
    leaderboards = _ref4[0],
    setLeaderboards = _ref4[1];
  var _ref5 = (cov_qlvp571ft().s[2]++, useState(null)),
    _ref6 = _slicedToArray(_ref5, 2),
    selectedLeaderboard = _ref6[0],
    setSelectedLeaderboard = _ref6[1];
  var _ref7 = (cov_qlvp571ft().s[3]++, useState([])),
    _ref8 = _slicedToArray(_ref7, 2),
    entries = _ref8[0],
    setEntries = _ref8[1];
  var _ref9 = (cov_qlvp571ft().s[4]++, useState(null)),
    _ref0 = _slicedToArray(_ref9, 2),
    userPosition = _ref0[0],
    setUserPosition = _ref0[1];
  var _ref1 = (cov_qlvp571ft().s[5]++, useState(true)),
    _ref10 = _slicedToArray(_ref1, 2),
    loading = _ref10[0],
    setLoading = _ref10[1];
  var _ref11 = (cov_qlvp571ft().s[6]++, useState(false)),
    _ref12 = _slicedToArray(_ref11, 2),
    entriesLoading = _ref12[0],
    setEntriesLoading = _ref12[1];
  var _ref13 = (cov_qlvp571ft().s[7]++, useState(false)),
    _ref14 = _slicedToArray(_ref13, 2),
    refreshing = _ref14[0],
    setRefreshing = _ref14[1];
  cov_qlvp571ft().s[8]++;
  useEffect(function () {
    cov_qlvp571ft().f[1]++;
    cov_qlvp571ft().s[9]++;
    loadLeaderboards();
  }, []);
  cov_qlvp571ft().s[10]++;
  useEffect(function () {
    cov_qlvp571ft().f[2]++;
    cov_qlvp571ft().s[11]++;
    if (selectedLeaderboard) {
      cov_qlvp571ft().b[0][0]++;
      cov_qlvp571ft().s[12]++;
      loadLeaderboardEntries(selectedLeaderboard.id);
    } else {
      cov_qlvp571ft().b[0][1]++;
    }
  }, [selectedLeaderboard]);
  cov_qlvp571ft().s[13]++;
  var loadLeaderboards = function () {
    var _ref15 = _asyncToGenerator(function* () {
      cov_qlvp571ft().f[3]++;
      cov_qlvp571ft().s[14]++;
      try {
        cov_qlvp571ft().s[15]++;
        setLoading(true);
        var _ref16 = (cov_qlvp571ft().s[16]++, yield socialService.getLeaderboards()),
          data = _ref16.leaderboards,
          error = _ref16.error;
        cov_qlvp571ft().s[17]++;
        if (error) {
          cov_qlvp571ft().b[1][0]++;
          cov_qlvp571ft().s[18]++;
          console.error('Error loading leaderboards:', error);
        } else {
          cov_qlvp571ft().b[1][1]++;
          cov_qlvp571ft().s[19]++;
          setLeaderboards(data);
          cov_qlvp571ft().s[20]++;
          if ((cov_qlvp571ft().b[3][0]++, data.length > 0) && (cov_qlvp571ft().b[3][1]++, !selectedLeaderboard)) {
            cov_qlvp571ft().b[2][0]++;
            cov_qlvp571ft().s[21]++;
            setSelectedLeaderboard(data[0]);
          } else {
            cov_qlvp571ft().b[2][1]++;
          }
        }
      } catch (error) {
        cov_qlvp571ft().s[22]++;
        console.error('Error loading leaderboards:', error);
      } finally {
        cov_qlvp571ft().s[23]++;
        setLoading(false);
      }
    });
    return function loadLeaderboards() {
      return _ref15.apply(this, arguments);
    };
  }();
  cov_qlvp571ft().s[24]++;
  var loadLeaderboardEntries = function () {
    var _ref17 = _asyncToGenerator(function* (leaderboardId) {
      cov_qlvp571ft().f[4]++;
      cov_qlvp571ft().s[25]++;
      try {
        cov_qlvp571ft().s[26]++;
        setEntriesLoading(true);
        var _ref18 = (cov_qlvp571ft().s[27]++, yield Promise.all([socialService.getLeaderboardEntries(leaderboardId), isAuthenticated() ? (cov_qlvp571ft().b[4][0]++, socialService.getUserLeaderboardPosition(leaderboardId)) : (cov_qlvp571ft().b[4][1]++, Promise.resolve({
            entry: null
          }))])),
          _ref19 = _slicedToArray(_ref18, 2),
          entriesResult = _ref19[0],
          positionResult = _ref19[1];
        cov_qlvp571ft().s[28]++;
        if (entriesResult.entries) {
          cov_qlvp571ft().b[5][0]++;
          cov_qlvp571ft().s[29]++;
          setEntries(entriesResult.entries);
        } else {
          cov_qlvp571ft().b[5][1]++;
        }
        cov_qlvp571ft().s[30]++;
        if (positionResult.entry) {
          cov_qlvp571ft().b[6][0]++;
          cov_qlvp571ft().s[31]++;
          setUserPosition(positionResult.entry);
        } else {
          cov_qlvp571ft().b[6][1]++;
          cov_qlvp571ft().s[32]++;
          setUserPosition(null);
        }
      } catch (error) {
        cov_qlvp571ft().s[33]++;
        console.error('Error loading leaderboard entries:', error);
      } finally {
        cov_qlvp571ft().s[34]++;
        setEntriesLoading(false);
      }
    });
    return function loadLeaderboardEntries(_x) {
      return _ref17.apply(this, arguments);
    };
  }();
  cov_qlvp571ft().s[35]++;
  var handleRefresh = function () {
    var _ref20 = _asyncToGenerator(function* () {
      cov_qlvp571ft().f[5]++;
      cov_qlvp571ft().s[36]++;
      setRefreshing(true);
      cov_qlvp571ft().s[37]++;
      yield loadLeaderboards();
      cov_qlvp571ft().s[38]++;
      if (selectedLeaderboard) {
        cov_qlvp571ft().b[7][0]++;
        cov_qlvp571ft().s[39]++;
        yield loadLeaderboardEntries(selectedLeaderboard.id);
      } else {
        cov_qlvp571ft().b[7][1]++;
      }
      cov_qlvp571ft().s[40]++;
      setRefreshing(false);
    });
    return function handleRefresh() {
      return _ref20.apply(this, arguments);
    };
  }();
  cov_qlvp571ft().s[41]++;
  var getRankIcon = function getRankIcon(rank) {
    cov_qlvp571ft().f[6]++;
    cov_qlvp571ft().s[42]++;
    switch (rank) {
      case 1:
        cov_qlvp571ft().b[8][0]++;
        cov_qlvp571ft().s[43]++;
        return {
          name: 'trophy',
          color: '#F59E0B'
        };
      case 2:
        cov_qlvp571ft().b[8][1]++;
        cov_qlvp571ft().s[44]++;
        return {
          name: 'medal',
          color: '#9CA3AF'
        };
      case 3:
        cov_qlvp571ft().b[8][2]++;
        cov_qlvp571ft().s[45]++;
        return {
          name: 'medal',
          color: '#CD7F32'
        };
      default:
        cov_qlvp571ft().b[8][3]++;
        cov_qlvp571ft().s[46]++;
        return {
          name: 'person',
          color: '#6B7280'
        };
    }
  };
  cov_qlvp571ft().s[47]++;
  var getRankColors = function getRankColors(rank) {
    cov_qlvp571ft().f[7]++;
    cov_qlvp571ft().s[48]++;
    switch (rank) {
      case 1:
        cov_qlvp571ft().b[9][0]++;
        cov_qlvp571ft().s[49]++;
        return {
          background: ['#F59E0B', '#F97316'],
          text: '#FFFFFF'
        };
      case 2:
        cov_qlvp571ft().b[9][1]++;
        cov_qlvp571ft().s[50]++;
        return {
          background: ['#9CA3AF', '#6B7280'],
          text: '#FFFFFF'
        };
      case 3:
        cov_qlvp571ft().b[9][2]++;
        cov_qlvp571ft().s[51]++;
        return {
          background: ['#CD7F32', '#A0522D'],
          text: '#FFFFFF'
        };
      default:
        cov_qlvp571ft().b[9][3]++;
        cov_qlvp571ft().s[52]++;
        return {
          background: ['#F3F4F6', '#E5E7EB'],
          text: '#374151'
        };
    }
  };
  cov_qlvp571ft().s[53]++;
  var formatScore = function formatScore(score, category) {
    cov_qlvp571ft().f[8]++;
    cov_qlvp571ft().s[54]++;
    switch (category) {
      case 'wins':
        cov_qlvp571ft().b[10][0]++;
        cov_qlvp571ft().s[55]++;
        return `${Math.floor(score)} wins`;
      case 'improvement':
        cov_qlvp571ft().b[10][1]++;
        cov_qlvp571ft().s[56]++;
        return `+${score.toFixed(1)}%`;
      case 'consistency':
        cov_qlvp571ft().b[10][2]++;
        cov_qlvp571ft().s[57]++;
        return `${score.toFixed(1)}%`;
      default:
        cov_qlvp571ft().b[10][3]++;
        cov_qlvp571ft().s[58]++;
        return score.toFixed(0);
    }
  };
  cov_qlvp571ft().s[59]++;
  var getCategoryIcon = function getCategoryIcon(category) {
    cov_qlvp571ft().f[9]++;
    cov_qlvp571ft().s[60]++;
    switch (category) {
      case 'overall':
        cov_qlvp571ft().b[11][0]++;
        cov_qlvp571ft().s[61]++;
        return 'trophy';
      case 'wins':
        cov_qlvp571ft().b[11][1]++;
        cov_qlvp571ft().s[62]++;
        return 'checkmark-circle';
      case 'improvement':
        cov_qlvp571ft().b[11][2]++;
        cov_qlvp571ft().s[63]++;
        return 'trending-up';
      case 'consistency':
        cov_qlvp571ft().b[11][3]++;
        cov_qlvp571ft().s[64]++;
        return 'pulse';
      case 'serve':
        cov_qlvp571ft().b[11][4]++;
        cov_qlvp571ft().s[65]++;
        return 'flash';
      case 'return':
        cov_qlvp571ft().b[11][5]++;
        cov_qlvp571ft().s[66]++;
        return 'return-up-back';
      case 'fitness':
        cov_qlvp571ft().b[11][6]++;
        cov_qlvp571ft().s[67]++;
        return 'fitness';
      default:
        cov_qlvp571ft().b[11][7]++;
        cov_qlvp571ft().s[68]++;
        return 'star';
    }
  };
  cov_qlvp571ft().s[69]++;
  var renderLeaderboardTab = function renderLeaderboardTab(leaderboard) {
    cov_qlvp571ft().f[10]++;
    var isSelected = (cov_qlvp571ft().s[70]++, (selectedLeaderboard == null ? void 0 : selectedLeaderboard.id) === leaderboard.id);
    cov_qlvp571ft().s[71]++;
    return _jsxs(TouchableOpacity, {
      style: [styles.leaderboardTab, (cov_qlvp571ft().b[12][0]++, isSelected) && (cov_qlvp571ft().b[12][1]++, styles.selectedTab)],
      onPress: function onPress() {
        cov_qlvp571ft().f[11]++;
        cov_qlvp571ft().s[72]++;
        return setSelectedLeaderboard(leaderboard);
      },
      children: [_jsx(Ionicons, {
        name: getCategoryIcon(leaderboard.category),
        size: 16,
        color: isSelected ? (cov_qlvp571ft().b[13][0]++, '#3B82F6') : (cov_qlvp571ft().b[13][1]++, '#6B7280')
      }), _jsx(Text, {
        style: [styles.tabText, (cov_qlvp571ft().b[14][0]++, isSelected) && (cov_qlvp571ft().b[14][1]++, styles.selectedTabText)],
        children: leaderboard.name
      })]
    }, leaderboard.id);
  };
  cov_qlvp571ft().s[73]++;
  var renderLeaderboardEntry = function renderLeaderboardEntry(entry, index) {
    var _entry$user_profile, _entry$user_profile2, _entry$user_profile3;
    cov_qlvp571ft().f[12]++;
    var rankColors = (cov_qlvp571ft().s[74]++, getRankColors(entry.rank));
    var rankIcon = (cov_qlvp571ft().s[75]++, getRankIcon(entry.rank));
    var isCurrentUser = (cov_qlvp571ft().s[76]++, (cov_qlvp571ft().b[15][0]++, isAuthenticated()) && (cov_qlvp571ft().b[15][1]++, entry.user_id === (userPosition == null ? void 0 : userPosition.user_id)));
    cov_qlvp571ft().s[77]++;
    return _jsxs(TouchableOpacity, {
      style: [styles.entryItem, (cov_qlvp571ft().b[16][0]++, isCurrentUser) && (cov_qlvp571ft().b[16][1]++, styles.currentUserEntry), (cov_qlvp571ft().b[17][0]++, entry.rank <= 3) && (cov_qlvp571ft().b[17][1]++, styles.topEntry)],
      onPress: function onPress() {
        cov_qlvp571ft().f[13]++;
        cov_qlvp571ft().s[78]++;
        return onNavigateToProfile == null ? void 0 : onNavigateToProfile(entry.user_id);
      },
      children: [_jsx(View, {
        style: styles.rankContainer,
        children: entry.rank <= 3 ? (cov_qlvp571ft().b[18][0]++, _jsx(LinearGradient, {
          colors: rankColors.background,
          style: styles.rankBadge,
          start: {
            x: 0,
            y: 0
          },
          end: {
            x: 1,
            y: 1
          },
          children: _jsx(Ionicons, {
            name: rankIcon.name,
            size: 16,
            color: rankColors.text
          })
        })) : (cov_qlvp571ft().b[18][1]++, _jsx(View, {
          style: styles.rankNumber,
          children: _jsx(Text, {
            style: styles.rankText,
            children: entry.rank
          })
        }))
      }), _jsx(View, {
        style: styles.playerAvatar,
        children: _jsx(Ionicons, {
          name: "person",
          size: 24,
          color: "#6B7280"
        })
      }), _jsxs(View, {
        style: styles.playerInfo,
        children: [_jsx(Text, {
          style: styles.playerName,
          children: (cov_qlvp571ft().b[19][0]++, (_entry$user_profile = entry.user_profile) == null ? void 0 : _entry$user_profile.display_name) || (cov_qlvp571ft().b[19][1]++, 'Tennis Player')
        }), _jsx(Text, {
          style: styles.playerLocation,
          children: (cov_qlvp571ft().b[21][0]++, (_entry$user_profile2 = entry.user_profile) != null && _entry$user_profile2.location_city) && (cov_qlvp571ft().b[21][1]++, (_entry$user_profile3 = entry.user_profile) != null && _entry$user_profile3.location_country) ? (cov_qlvp571ft().b[20][0]++, `${entry.user_profile.location_city}, ${entry.user_profile.location_country}`) : (cov_qlvp571ft().b[20][1]++, 'Location not set')
        })]
      }), _jsxs(View, {
        style: styles.scoreContainer,
        children: [_jsx(Text, {
          style: styles.scoreValue,
          children: formatScore(entry.score, (cov_qlvp571ft().b[22][0]++, selectedLeaderboard == null ? void 0 : selectedLeaderboard.category) || (cov_qlvp571ft().b[22][1]++, 'overall'))
        }), (cov_qlvp571ft().b[23][0]++, entry.rank_change !== 0) && (cov_qlvp571ft().b[23][1]++, _jsxs(View, {
          style: [styles.rankChange, entry.rank_change > 0 ? (cov_qlvp571ft().b[24][0]++, styles.rankUp) : (cov_qlvp571ft().b[24][1]++, styles.rankDown)],
          children: [_jsx(Ionicons, {
            name: entry.rank_change > 0 ? (cov_qlvp571ft().b[25][0]++, 'arrow-up') : (cov_qlvp571ft().b[25][1]++, 'arrow-down'),
            size: 12,
            color: entry.rank_change > 0 ? (cov_qlvp571ft().b[26][0]++, '#10B981') : (cov_qlvp571ft().b[26][1]++, '#EF4444')
          }), _jsx(Text, {
            style: [styles.rankChangeText, entry.rank_change > 0 ? (cov_qlvp571ft().b[27][0]++, styles.rankUpText) : (cov_qlvp571ft().b[27][1]++, styles.rankDownText)],
            children: Math.abs(entry.rank_change)
          })]
        }))]
      })]
    }, entry.id);
  };
  cov_qlvp571ft().s[79]++;
  var renderUserPosition = function renderUserPosition() {
    cov_qlvp571ft().f[14]++;
    cov_qlvp571ft().s[80]++;
    if ((cov_qlvp571ft().b[29][0]++, !userPosition) || (cov_qlvp571ft().b[29][1]++, !isAuthenticated())) {
      cov_qlvp571ft().b[28][0]++;
      cov_qlvp571ft().s[81]++;
      return null;
    } else {
      cov_qlvp571ft().b[28][1]++;
    }
    cov_qlvp571ft().s[82]++;
    return _jsxs(View, {
      style: styles.userPositionCard,
      children: [_jsx(Text, {
        style: styles.userPositionTitle,
        children: "Your Position"
      }), _jsxs(View, {
        style: styles.userPositionContent,
        children: [_jsxs(View, {
          style: styles.userPositionRank,
          children: [_jsxs(Text, {
            style: styles.userPositionRankNumber,
            children: ["#", userPosition.rank]
          }), _jsx(Text, {
            style: styles.userPositionRankLabel,
            children: "Rank"
          })]
        }), _jsxs(View, {
          style: styles.userPositionScore,
          children: [_jsx(Text, {
            style: styles.userPositionScoreNumber,
            children: formatScore(userPosition.score, (cov_qlvp571ft().b[30][0]++, selectedLeaderboard == null ? void 0 : selectedLeaderboard.category) || (cov_qlvp571ft().b[30][1]++, 'overall'))
          }), _jsx(Text, {
            style: styles.userPositionScoreLabel,
            children: "Score"
          })]
        }), (cov_qlvp571ft().b[31][0]++, userPosition.rank_change !== 0) && (cov_qlvp571ft().b[31][1]++, _jsxs(View, {
          style: styles.userPositionChange,
          children: [_jsx(Ionicons, {
            name: userPosition.rank_change > 0 ? (cov_qlvp571ft().b[32][0]++, 'trending-up') : (cov_qlvp571ft().b[32][1]++, 'trending-down'),
            size: 20,
            color: userPosition.rank_change > 0 ? (cov_qlvp571ft().b[33][0]++, '#10B981') : (cov_qlvp571ft().b[33][1]++, '#EF4444')
          }), _jsxs(Text, {
            style: [styles.userPositionChangeText, userPosition.rank_change > 0 ? (cov_qlvp571ft().b[34][0]++, styles.rankUpText) : (cov_qlvp571ft().b[34][1]++, styles.rankDownText)],
            children: [userPosition.rank_change > 0 ? (cov_qlvp571ft().b[35][0]++, '+') : (cov_qlvp571ft().b[35][1]++, ''), userPosition.rank_change]
          })]
        }))]
      })]
    });
  };
  cov_qlvp571ft().s[83]++;
  if (loading) {
    cov_qlvp571ft().b[36][0]++;
    cov_qlvp571ft().s[84]++;
    return _jsxs(View, {
      style: styles.loadingContainer,
      children: [_jsx(ActivityIndicator, {
        size: "large",
        color: "#3B82F6"
      }), _jsx(Text, {
        style: styles.loadingText,
        children: "Loading leaderboards..."
      })]
    });
  } else {
    cov_qlvp571ft().b[36][1]++;
  }
  cov_qlvp571ft().s[85]++;
  return _jsxs(View, {
    style: styles.container,
    children: [_jsxs(View, {
      style: styles.header,
      children: [_jsx(Text, {
        style: styles.title,
        children: "Leaderboards"
      }), _jsx(Text, {
        style: styles.subtitle,
        children: "See how you rank against other tennis players"
      })]
    }), _jsx(ScrollView, {
      horizontal: true,
      showsHorizontalScrollIndicator: false,
      style: styles.tabsContainer,
      contentContainerStyle: styles.tabsContent,
      children: leaderboards.map(renderLeaderboardTab)
    }), renderUserPosition(), _jsx(ScrollView, {
      style: styles.entriesContainer,
      refreshControl: _jsx(RefreshControl, {
        refreshing: refreshing,
        onRefresh: handleRefresh
      }),
      showsVerticalScrollIndicator: false,
      children: entriesLoading ? (cov_qlvp571ft().b[37][0]++, _jsxs(View, {
        style: styles.entriesLoadingContainer,
        children: [_jsx(ActivityIndicator, {
          size: "large",
          color: "#3B82F6"
        }), _jsx(Text, {
          style: styles.loadingText,
          children: "Loading rankings..."
        })]
      })) : (cov_qlvp571ft().b[37][1]++, entries.length === 0 ? (cov_qlvp571ft().b[38][0]++, _jsxs(View, {
        style: styles.emptyState,
        children: [_jsx(Ionicons, {
          name: "trophy-outline",
          size: 64,
          color: "#9CA3AF"
        }), _jsx(Text, {
          style: styles.emptyTitle,
          children: "No Rankings Yet"
        }), _jsx(Text, {
          style: styles.emptyText,
          children: "Play more matches to appear on the leaderboard!"
        })]
      })) : (cov_qlvp571ft().b[38][1]++, _jsx(View, {
        style: styles.entriesList,
        children: entries.map(renderLeaderboardEntry)
      })))
    })]
  });
}
var styles = (cov_qlvp571ft().s[86]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F9FAFB'
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#6B7280'
  },
  header: {
    padding: 24,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280'
  },
  tabsContainer: {
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  tabsContent: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8
  },
  leaderboardTab: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    gap: 8
  },
  selectedTab: {
    backgroundColor: '#EBF4FF',
    borderWidth: 1,
    borderColor: '#3B82F6'
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280'
  },
  selectedTabText: {
    color: '#3B82F6',
    fontWeight: '600'
  },
  userPositionCard: {
    margin: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2
  },
  userPositionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 12
  },
  userPositionContent: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center'
  },
  userPositionRank: {
    alignItems: 'center'
  },
  userPositionRankNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3B82F6'
  },
  userPositionRankLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4
  },
  userPositionScore: {
    alignItems: 'center'
  },
  userPositionScoreNumber: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827'
  },
  userPositionScoreLabel: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 4
  },
  userPositionChange: {
    alignItems: 'center'
  },
  userPositionChangeText: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 4
  },
  entriesContainer: {
    flex: 1
  },
  entriesLoadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 64
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 24
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginTop: 16,
    marginBottom: 8
  },
  emptyText: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24
  },
  entriesList: {
    padding: 16
  },
  entryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  currentUserEntry: {
    borderWidth: 2,
    borderColor: '#3B82F6',
    backgroundColor: '#F0F9FF'
  },
  topEntry: {
    shadowOpacity: 0.15,
    shadowRadius: 4,
    elevation: 4
  },
  rankContainer: {
    width: 40,
    alignItems: 'center',
    marginRight: 12
  },
  rankBadge: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center'
  },
  rankNumber: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center'
  },
  rankText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151'
  },
  playerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12
  },
  playerInfo: {
    flex: 1
  },
  playerName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2
  },
  playerLocation: {
    fontSize: 14,
    color: '#6B7280'
  },
  scoreContainer: {
    alignItems: 'flex-end'
  },
  scoreValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 2
  },
  rankChange: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    gap: 2
  },
  rankUp: {
    backgroundColor: '#ECFDF5'
  },
  rankDown: {
    backgroundColor: '#FEF2F2'
  },
  rankChangeText: {
    fontSize: 12,
    fontWeight: '500'
  },
  rankUpText: {
    color: '#10B981'
  },
  rankDownText: {
    color: '#EF4444'
  }
}));
export default LeaderboardsScreen;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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