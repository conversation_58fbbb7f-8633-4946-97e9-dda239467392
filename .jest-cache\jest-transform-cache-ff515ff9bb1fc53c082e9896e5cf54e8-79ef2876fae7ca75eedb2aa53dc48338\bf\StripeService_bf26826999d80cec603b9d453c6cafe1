2edeec493b607693e070e15534cd1e88
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2jjpjgc9x5() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\payment\\StripeService.ts";
  var hash = "f2eff6c3b257d35f21b30acc1098533f7533d40d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\payment\\StripeService.ts",
    statementMap: {
      "0": {
        start: {
          line: 136,
          column: 24
        },
        end: {
          line: 136,
          column: 28
        }
      },
      "1": {
        start: {
          line: 137,
          column: 35
        },
        end: {
          line: 137,
          column: 40
        }
      },
      "2": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 140,
          column: 72
        }
      },
      "3": {
        start: {
          line: 141,
          column: 4
        },
        end: {
          line: 141,
          column: 75
        }
      },
      "4": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "5": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 151,
          column: 7
        }
      },
      "6": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 150,
          column: 33
        }
      },
      "7": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 155,
          column: 7
        }
      },
      "8": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 154,
          column: 82
        }
      },
      "9": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 165,
          column: 7
        }
      },
      "10": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 159,
          column: 34
        }
      },
      "11": {
        start: {
          line: 160,
          column: 8
        },
        end: {
          line: 160,
          column: 63
        }
      },
      "12": {
        start: {
          line: 164,
          column: 8
        },
        end: {
          line: 164,
          column: 73
        }
      },
      "13": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 167,
          column: 32
        }
      },
      "14": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 31
        }
      },
      "15": {
        start: {
          line: 170,
          column: 23
        },
        end: {
          line: 170,
          column: 63
        }
      },
      "16": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 61
        }
      },
      "17": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 190,
          column: 7
        }
      },
      "18": {
        start: {
          line: 180,
          column: 6
        },
        end: {
          line: 183,
          column: 7
        }
      },
      "19": {
        start: {
          line: 181,
          column: 8
        },
        end: {
          line: 181,
          column: 18
        }
      },
      "20": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 15
        }
      },
      "21": {
        start: {
          line: 185,
          column: 21
        },
        end: {
          line: 185,
          column: 53
        }
      },
      "22": {
        start: {
          line: 186,
          column: 6
        },
        end: {
          line: 186,
          column: 47
        }
      },
      "23": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 187,
          column: 38
        }
      },
      "24": {
        start: {
          line: 187,
          column: 28
        },
        end: {
          line: 187,
          column: 37
        }
      },
      "25": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 75
        }
      },
      "26": {
        start: {
          line: 188,
          column: 29
        },
        end: {
          line: 188,
          column: 74
        }
      },
      "27": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 40
        }
      },
      "28": {
        start: {
          line: 201,
          column: 4
        },
        end: {
          line: 217,
          column: 5
        }
      },
      "29": {
        start: {
          line: 202,
          column: 23
        },
        end: {
          line: 205,
          column: 8
        }
      },
      "30": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 210,
          column: 7
        }
      },
      "31": {
        start: {
          line: 208,
          column: 22
        },
        end: {
          line: 208,
          column: 43
        }
      },
      "32": {
        start: {
          line: 209,
          column: 8
        },
        end: {
          line: 209,
          column: 87
        }
      },
      "33": {
        start: {
          line: 212,
          column: 23
        },
        end: {
          line: 212,
          column: 44
        }
      },
      "34": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 26
        }
      },
      "35": {
        start: {
          line: 215,
          column: 23
        },
        end: {
          line: 215,
          column: 63
        }
      },
      "36": {
        start: {
          line: 216,
          column: 6
        },
        end: {
          line: 216,
          column: 61
        }
      },
      "37": {
        start: {
          line: 224,
          column: 4
        },
        end: {
          line: 237,
          column: 5
        }
      },
      "38": {
        start: {
          line: 225,
          column: 23
        },
        end: {
          line: 225,
          column: 78
        }
      },
      "39": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 230,
          column: 7
        }
      },
      "40": {
        start: {
          line: 228,
          column: 22
        },
        end: {
          line: 228,
          column: 43
        }
      },
      "41": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 229,
          column: 84
        }
      },
      "42": {
        start: {
          line: 232,
          column: 23
        },
        end: {
          line: 232,
          column: 44
        }
      },
      "43": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 233,
          column: 26
        }
      },
      "44": {
        start: {
          line: 235,
          column: 23
        },
        end: {
          line: 235,
          column: 63
        }
      },
      "45": {
        start: {
          line: 236,
          column: 6
        },
        end: {
          line: 236,
          column: 61
        }
      },
      "46": {
        start: {
          line: 265,
          column: 4
        },
        end: {
          line: 280,
          column: 5
        }
      },
      "47": {
        start: {
          line: 266,
          column: 6
        },
        end: {
          line: 268,
          column: 7
        }
      },
      "48": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 267,
          column: 32
        }
      },
      "49": {
        start: {
          line: 270,
          column: 39
        },
        end: {
          line: 270,
          column: 89
        }
      },
      "50": {
        start: {
          line: 272,
          column: 6
        },
        end: {
          line: 274,
          column: 7
        }
      },
      "51": {
        start: {
          line: 273,
          column: 8
        },
        end: {
          line: 273,
          column: 61
        }
      },
      "52": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 31
        }
      },
      "53": {
        start: {
          line: 278,
          column: 23
        },
        end: {
          line: 278,
          column: 63
        }
      },
      "54": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 66
        }
      },
      "55": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 302,
          column: 5
        }
      },
      "56": {
        start: {
          line: 288,
          column: 23
        },
        end: {
          line: 291,
          column: 8
        }
      },
      "57": {
        start: {
          line: 293,
          column: 6
        },
        end: {
          line: 296,
          column: 7
        }
      },
      "58": {
        start: {
          line: 294,
          column: 22
        },
        end: {
          line: 294,
          column: 43
        }
      },
      "59": {
        start: {
          line: 295,
          column: 8
        },
        end: {
          line: 295,
          column: 93
        }
      },
      "60": {
        start: {
          line: 298,
          column: 6
        },
        end: {
          line: 298,
          column: 31
        }
      },
      "61": {
        start: {
          line: 300,
          column: 23
        },
        end: {
          line: 300,
          column: 63
        }
      },
      "62": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 61
        }
      },
      "63": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 322,
          column: 5
        }
      },
      "64": {
        start: {
          line: 310,
          column: 23
        },
        end: {
          line: 310,
          column: 85
        }
      },
      "65": {
        start: {
          line: 312,
          column: 6
        },
        end: {
          line: 315,
          column: 7
        }
      },
      "66": {
        start: {
          line: 313,
          column: 22
        },
        end: {
          line: 313,
          column: 43
        }
      },
      "67": {
        start: {
          line: 314,
          column: 8
        },
        end: {
          line: 314,
          column: 95
        }
      },
      "68": {
        start: {
          line: 317,
          column: 19
        },
        end: {
          line: 317,
          column: 40
        }
      },
      "69": {
        start: {
          line: 318,
          column: 6
        },
        end: {
          line: 318,
          column: 49
        }
      },
      "70": {
        start: {
          line: 320,
          column: 23
        },
        end: {
          line: 320,
          column: 63
        }
      },
      "71": {
        start: {
          line: 321,
          column: 6
        },
        end: {
          line: 321,
          column: 65
        }
      },
      "72": {
        start: {
          line: 329,
          column: 4
        },
        end: {
          line: 348,
          column: 5
        }
      },
      "73": {
        start: {
          line: 330,
          column: 23
        },
        end: {
          line: 336,
          column: 8
        }
      },
      "74": {
        start: {
          line: 338,
          column: 6
        },
        end: {
          line: 341,
          column: 7
        }
      },
      "75": {
        start: {
          line: 339,
          column: 22
        },
        end: {
          line: 339,
          column: 43
        }
      },
      "76": {
        start: {
          line: 340,
          column: 8
        },
        end: {
          line: 340,
          column: 95
        }
      },
      "77": {
        start: {
          line: 343,
          column: 27
        },
        end: {
          line: 343,
          column: 48
        }
      },
      "78": {
        start: {
          line: 344,
          column: 6
        },
        end: {
          line: 344,
          column: 30
        }
      },
      "79": {
        start: {
          line: 346,
          column: 23
        },
        end: {
          line: 346,
          column: 63
        }
      },
      "80": {
        start: {
          line: 347,
          column: 6
        },
        end: {
          line: 347,
          column: 65
        }
      },
      "81": {
        start: {
          line: 355,
          column: 4
        },
        end: {
          line: 371,
          column: 5
        }
      },
      "82": {
        start: {
          line: 356,
          column: 23
        },
        end: {
          line: 356,
          column: 82
        }
      },
      "83": {
        start: {
          line: 358,
          column: 6
        },
        end: {
          line: 364,
          column: 7
        }
      },
      "84": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 361,
          column: 9
        }
      },
      "85": {
        start: {
          line: 360,
          column: 10
        },
        end: {
          line: 360,
          column: 40
        }
      },
      "86": {
        start: {
          line: 362,
          column: 22
        },
        end: {
          line: 362,
          column: 43
        }
      },
      "87": {
        start: {
          line: 363,
          column: 8
        },
        end: {
          line: 363,
          column: 92
        }
      },
      "88": {
        start: {
          line: 366,
          column: 27
        },
        end: {
          line: 366,
          column: 48
        }
      },
      "89": {
        start: {
          line: 367,
          column: 6
        },
        end: {
          line: 367,
          column: 30
        }
      },
      "90": {
        start: {
          line: 369,
          column: 23
        },
        end: {
          line: 369,
          column: 63
        }
      },
      "91": {
        start: {
          line: 370,
          column: 6
        },
        end: {
          line: 370,
          column: 65
        }
      },
      "92": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 394,
          column: 5
        }
      },
      "93": {
        start: {
          line: 379,
          column: 23
        },
        end: {
          line: 382,
          column: 8
        }
      },
      "94": {
        start: {
          line: 384,
          column: 6
        },
        end: {
          line: 387,
          column: 7
        }
      },
      "95": {
        start: {
          line: 385,
          column: 22
        },
        end: {
          line: 385,
          column: 43
        }
      },
      "96": {
        start: {
          line: 386,
          column: 8
        },
        end: {
          line: 386,
          column: 95
        }
      },
      "97": {
        start: {
          line: 389,
          column: 27
        },
        end: {
          line: 389,
          column: 48
        }
      },
      "98": {
        start: {
          line: 390,
          column: 6
        },
        end: {
          line: 390,
          column: 30
        }
      },
      "99": {
        start: {
          line: 392,
          column: 23
        },
        end: {
          line: 392,
          column: 63
        }
      },
      "100": {
        start: {
          line: 393,
          column: 6
        },
        end: {
          line: 393,
          column: 65
        }
      },
      "101": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 414,
          column: 5
        }
      },
      "102": {
        start: {
          line: 402,
          column: 23
        },
        end: {
          line: 402,
          column: 93
        }
      },
      "103": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 407,
          column: 7
        }
      },
      "104": {
        start: {
          line: 405,
          column: 22
        },
        end: {
          line: 405,
          column: 43
        }
      },
      "105": {
        start: {
          line: 406,
          column: 8
        },
        end: {
          line: 406,
          column: 82
        }
      },
      "106": {
        start: {
          line: 409,
          column: 19
        },
        end: {
          line: 409,
          column: 40
        }
      },
      "107": {
        start: {
          line: 410,
          column: 6
        },
        end: {
          line: 410,
          column: 43
        }
      },
      "108": {
        start: {
          line: 412,
          column: 23
        },
        end: {
          line: 412,
          column: 63
        }
      },
      "109": {
        start: {
          line: 413,
          column: 6
        },
        end: {
          line: 413,
          column: 59
        }
      },
      "110": {
        start: {
          line: 421,
          column: 4
        },
        end: {
          line: 437,
          column: 5
        }
      },
      "111": {
        start: {
          line: 422,
          column: 23
        },
        end: {
          line: 425,
          column: 8
        }
      },
      "112": {
        start: {
          line: 427,
          column: 6
        },
        end: {
          line: 430,
          column: 7
        }
      },
      "113": {
        start: {
          line: 428,
          column: 22
        },
        end: {
          line: 428,
          column: 43
        }
      },
      "114": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 429,
          column: 98
        }
      },
      "115": {
        start: {
          line: 432,
          column: 28
        },
        end: {
          line: 432,
          column: 49
        }
      },
      "116": {
        start: {
          line: 433,
          column: 6
        },
        end: {
          line: 433,
          column: 31
        }
      },
      "117": {
        start: {
          line: 435,
          column: 23
        },
        end: {
          line: 435,
          column: 63
        }
      },
      "118": {
        start: {
          line: 436,
          column: 6
        },
        end: {
          line: 436,
          column: 66
        }
      },
      "119": {
        start: {
          line: 444,
          column: 4
        },
        end: {
          line: 461,
          column: 5
        }
      },
      "120": {
        start: {
          line: 445,
          column: 6
        },
        end: {
          line: 447,
          column: 7
        }
      },
      "121": {
        start: {
          line: 446,
          column: 8
        },
        end: {
          line: 446,
          column: 32
        }
      },
      "122": {
        start: {
          line: 449,
          column: 24
        },
        end: {
          line: 451,
          column: 8
        }
      },
      "123": {
        start: {
          line: 453,
          column: 6
        },
        end: {
          line: 455,
          column: 7
        }
      },
      "124": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 454,
          column: 56
        }
      },
      "125": {
        start: {
          line: 457,
          column: 6
        },
        end: {
          line: 457,
          column: 31
        }
      },
      "126": {
        start: {
          line: 459,
          column: 23
        },
        end: {
          line: 459,
          column: 63
        }
      },
      "127": {
        start: {
          line: 460,
          column: 6
        },
        end: {
          line: 460,
          column: 61
        }
      },
      "128": {
        start: {
          line: 468,
          column: 20
        },
        end: {
          line: 468,
          column: 57
        }
      },
      "129": {
        start: {
          line: 469,
          column: 4
        },
        end: {
          line: 471,
          column: 5
        }
      },
      "130": {
        start: {
          line: 470,
          column: 6
        },
        end: {
          line: 470,
          column: 48
        }
      },
      "131": {
        start: {
          line: 473,
          column: 4
        },
        end: {
          line: 480,
          column: 7
        }
      },
      "132": {
        start: {
          line: 485,
          column: 29
        },
        end: {
          line: 485,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 139,
            column: 2
          },
          end: {
            line: 139,
            column: 3
          }
        },
        loc: {
          start: {
            line: 139,
            column: 16
          },
          end: {
            line: 142,
            column: 3
          }
        },
        line: 139
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 147,
            column: 2
          },
          end: {
            line: 147,
            column: 3
          }
        },
        loc: {
          start: {
            line: 147,
            column: 68
          },
          end: {
            line: 173,
            column: 3
          }
        },
        line: 147
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 178,
            column: 2
          },
          end: {
            line: 178,
            column: 3
          }
        },
        loc: {
          start: {
            line: 178,
            column: 46
          },
          end: {
            line: 191,
            column: 3
          }
        },
        line: 178
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 179,
            column: 23
          },
          end: {
            line: 179,
            column: 24
          }
        },
        loc: {
          start: {
            line: 179,
            column: 44
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 179
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 187,
            column: 22
          },
          end: {
            line: 187,
            column: 23
          }
        },
        loc: {
          start: {
            line: 187,
            column: 28
          },
          end: {
            line: 187,
            column: 37
          }
        },
        line: 187
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 188,
            column: 23
          },
          end: {
            line: 188,
            column: 24
          }
        },
        loc: {
          start: {
            line: 188,
            column: 29
          },
          end: {
            line: 188,
            column: 74
          }
        },
        line: 188
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 196,
            column: 2
          },
          end: {
            line: 196,
            column: 3
          }
        },
        loc: {
          start: {
            line: 200,
            column: 67
          },
          end: {
            line: 218,
            column: 3
          }
        },
        line: 200
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 223,
            column: 2
          },
          end: {
            line: 223,
            column: 3
          }
        },
        loc: {
          start: {
            line: 223,
            column: 84
          },
          end: {
            line: 238,
            column: 3
          }
        },
        line: 223
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 243,
            column: 2
          },
          end: {
            line: 243,
            column: 3
          }
        },
        loc: {
          start: {
            line: 264,
            column: 77
          },
          end: {
            line: 281,
            column: 3
          }
        },
        line: 264
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 286,
            column: 2
          },
          end: {
            line: 286,
            column: 3
          }
        },
        loc: {
          start: {
            line: 286,
            column: 100
          },
          end: {
            line: 303,
            column: 3
          }
        },
        line: 286
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 308,
            column: 2
          },
          end: {
            line: 308,
            column: 3
          }
        },
        loc: {
          start: {
            line: 308,
            column: 96
          },
          end: {
            line: 323,
            column: 3
          }
        },
        line: 308
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 328,
            column: 2
          },
          end: {
            line: 328,
            column: 3
          }
        },
        loc: {
          start: {
            line: 328,
            column: 140
          },
          end: {
            line: 349,
            column: 3
          }
        },
        line: 328
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 354,
            column: 2
          },
          end: {
            line: 354,
            column: 3
          }
        },
        loc: {
          start: {
            line: 354,
            column: 96
          },
          end: {
            line: 372,
            column: 3
          }
        },
        line: 354
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 377,
            column: 2
          },
          end: {
            line: 377,
            column: 3
          }
        },
        loc: {
          start: {
            line: 377,
            column: 127
          },
          end: {
            line: 395,
            column: 3
          }
        },
        line: 377
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 400,
            column: 2
          },
          end: {
            line: 400,
            column: 3
          }
        },
        loc: {
          start: {
            line: 400,
            column: 96
          },
          end: {
            line: 415,
            column: 3
          }
        },
        line: 400
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 420,
            column: 2
          },
          end: {
            line: 420,
            column: 3
          }
        },
        loc: {
          start: {
            line: 420,
            column: 136
          },
          end: {
            line: 438,
            column: 3
          }
        },
        line: 420
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 443,
            column: 2
          },
          end: {
            line: 443,
            column: 3
          }
        },
        loc: {
          start: {
            line: 443,
            column: 123
          },
          end: {
            line: 462,
            column: 3
          }
        },
        line: 443
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 467,
            column: 2
          },
          end: {
            line: 467,
            column: 3
          }
        },
        loc: {
          start: {
            line: 467,
            column: 105
          },
          end: {
            line: 481,
            column: 3
          }
        },
        line: 467
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 140,
            column: 32
          },
          end: {
            line: 140,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 140,
            column: 32
          },
          end: {
            line: 140,
            column: 65
          }
        }, {
          start: {
            line: 140,
            column: 69
          },
          end: {
            line: 140,
            column: 71
          }
        }],
        line: 140
      },
      "1": {
        loc: {
          start: {
            line: 141,
            column: 22
          },
          end: {
            line: 141,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 141,
            column: 22
          },
          end: {
            line: 141,
            column: 45
          }
        }, {
          start: {
            line: 141,
            column: 49
          },
          end: {
            line: 141,
            column: 74
          }
        }],
        line: 141
      },
      "2": {
        loc: {
          start: {
            line: 149,
            column: 6
          },
          end: {
            line: 151,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 149,
            column: 6
          },
          end: {
            line: 151,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 149
      },
      "3": {
        loc: {
          start: {
            line: 153,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "4": {
        loc: {
          start: {
            line: 157,
            column: 6
          },
          end: {
            line: 165,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 157,
            column: 6
          },
          end: {
            line: 165,
            column: 7
          }
        }, {
          start: {
            line: 161,
            column: 13
          },
          end: {
            line: 165,
            column: 7
          }
        }],
        line: 157
      },
      "5": {
        loc: {
          start: {
            line: 180,
            column: 6
          },
          end: {
            line: 183,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 180,
            column: 6
          },
          end: {
            line: 183,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 180
      },
      "6": {
        loc: {
          start: {
            line: 180,
            column: 10
          },
          end: {
            line: 180,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 180,
            column: 10
          },
          end: {
            line: 180,
            column: 39
          }
        }, {
          start: {
            line: 180,
            column: 43
          },
          end: {
            line: 180,
            column: 56
          }
        }],
        line: 180
      },
      "7": {
        loc: {
          start: {
            line: 207,
            column: 6
          },
          end: {
            line: 210,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 207,
            column: 6
          },
          end: {
            line: 210,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 207
      },
      "8": {
        loc: {
          start: {
            line: 209,
            column: 40
          },
          end: {
            line: 209,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 209,
            column: 40
          },
          end: {
            line: 209,
            column: 53
          }
        }, {
          start: {
            line: 209,
            column: 57
          },
          end: {
            line: 209,
            column: 84
          }
        }],
        line: 209
      },
      "9": {
        loc: {
          start: {
            line: 227,
            column: 6
          },
          end: {
            line: 230,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 227,
            column: 6
          },
          end: {
            line: 230,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 227
      },
      "10": {
        loc: {
          start: {
            line: 229,
            column: 40
          },
          end: {
            line: 229,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 229,
            column: 40
          },
          end: {
            line: 229,
            column: 53
          }
        }, {
          start: {
            line: 229,
            column: 57
          },
          end: {
            line: 229,
            column: 81
          }
        }],
        line: 229
      },
      "11": {
        loc: {
          start: {
            line: 266,
            column: 6
          },
          end: {
            line: 268,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 266,
            column: 6
          },
          end: {
            line: 268,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 266
      },
      "12": {
        loc: {
          start: {
            line: 272,
            column: 6
          },
          end: {
            line: 274,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 272,
            column: 6
          },
          end: {
            line: 274,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 272
      },
      "13": {
        loc: {
          start: {
            line: 293,
            column: 6
          },
          end: {
            line: 296,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 6
          },
          end: {
            line: 296,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 293
      },
      "14": {
        loc: {
          start: {
            line: 295,
            column: 40
          },
          end: {
            line: 295,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 295,
            column: 40
          },
          end: {
            line: 295,
            column: 53
          }
        }, {
          start: {
            line: 295,
            column: 57
          },
          end: {
            line: 295,
            column: 90
          }
        }],
        line: 295
      },
      "15": {
        loc: {
          start: {
            line: 312,
            column: 6
          },
          end: {
            line: 315,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 312,
            column: 6
          },
          end: {
            line: 315,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 312
      },
      "16": {
        loc: {
          start: {
            line: 314,
            column: 44
          },
          end: {
            line: 314,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 314,
            column: 44
          },
          end: {
            line: 314,
            column: 57
          }
        }, {
          start: {
            line: 314,
            column: 61
          },
          end: {
            line: 314,
            column: 92
          }
        }],
        line: 314
      },
      "17": {
        loc: {
          start: {
            line: 318,
            column: 31
          },
          end: {
            line: 318,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 318,
            column: 31
          },
          end: {
            line: 318,
            column: 40
          }
        }, {
          start: {
            line: 318,
            column: 44
          },
          end: {
            line: 318,
            column: 46
          }
        }],
        line: 318
      },
      "18": {
        loc: {
          start: {
            line: 338,
            column: 6
          },
          end: {
            line: 341,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 338,
            column: 6
          },
          end: {
            line: 341,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 338
      },
      "19": {
        loc: {
          start: {
            line: 340,
            column: 44
          },
          end: {
            line: 340,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 340,
            column: 44
          },
          end: {
            line: 340,
            column: 57
          }
        }, {
          start: {
            line: 340,
            column: 61
          },
          end: {
            line: 340,
            column: 92
          }
        }],
        line: 340
      },
      "20": {
        loc: {
          start: {
            line: 358,
            column: 6
          },
          end: {
            line: 364,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 6
          },
          end: {
            line: 364,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 358
      },
      "21": {
        loc: {
          start: {
            line: 359,
            column: 8
          },
          end: {
            line: 361,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 359,
            column: 8
          },
          end: {
            line: 361,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 359
      },
      "22": {
        loc: {
          start: {
            line: 363,
            column: 44
          },
          end: {
            line: 363,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 363,
            column: 44
          },
          end: {
            line: 363,
            column: 57
          }
        }, {
          start: {
            line: 363,
            column: 61
          },
          end: {
            line: 363,
            column: 89
          }
        }],
        line: 363
      },
      "23": {
        loc: {
          start: {
            line: 377,
            column: 27
          },
          end: {
            line: 377,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 377,
            column: 50
          },
          end: {
            line: 377,
            column: 55
          }
        }],
        line: 377
      },
      "24": {
        loc: {
          start: {
            line: 384,
            column: 6
          },
          end: {
            line: 387,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 384,
            column: 6
          },
          end: {
            line: 387,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 384
      },
      "25": {
        loc: {
          start: {
            line: 386,
            column: 44
          },
          end: {
            line: 386,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 386,
            column: 44
          },
          end: {
            line: 386,
            column: 57
          }
        }, {
          start: {
            line: 386,
            column: 61
          },
          end: {
            line: 386,
            column: 92
          }
        }],
        line: 386
      },
      "26": {
        loc: {
          start: {
            line: 400,
            column: 20
          },
          end: {
            line: 400,
            column: 38
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 400,
            column: 36
          },
          end: {
            line: 400,
            column: 38
          }
        }],
        line: 400
      },
      "27": {
        loc: {
          start: {
            line: 404,
            column: 6
          },
          end: {
            line: 407,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 6
          },
          end: {
            line: 407,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "28": {
        loc: {
          start: {
            line: 406,
            column: 38
          },
          end: {
            line: 406,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 406,
            column: 38
          },
          end: {
            line: 406,
            column: 51
          }
        }, {
          start: {
            line: 406,
            column: 55
          },
          end: {
            line: 406,
            column: 79
          }
        }],
        line: 406
      },
      "29": {
        loc: {
          start: {
            line: 410,
            column: 25
          },
          end: {
            line: 410,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 410,
            column: 25
          },
          end: {
            line: 410,
            column: 34
          }
        }, {
          start: {
            line: 410,
            column: 38
          },
          end: {
            line: 410,
            column: 40
          }
        }],
        line: 410
      },
      "30": {
        loc: {
          start: {
            line: 420,
            column: 44
          },
          end: {
            line: 420,
            column: 68
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 420,
            column: 63
          },
          end: {
            line: 420,
            column: 68
          }
        }],
        line: 420
      },
      "31": {
        loc: {
          start: {
            line: 427,
            column: 6
          },
          end: {
            line: 430,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 427,
            column: 6
          },
          end: {
            line: 430,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 427
      },
      "32": {
        loc: {
          start: {
            line: 429,
            column: 45
          },
          end: {
            line: 429,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 429,
            column: 45
          },
          end: {
            line: 429,
            column: 58
          }
        }, {
          start: {
            line: 429,
            column: 62
          },
          end: {
            line: 429,
            column: 95
          }
        }],
        line: 429
      },
      "33": {
        loc: {
          start: {
            line: 445,
            column: 6
          },
          end: {
            line: 447,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 445,
            column: 6
          },
          end: {
            line: 447,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 445
      },
      "34": {
        loc: {
          start: {
            line: 453,
            column: 6
          },
          end: {
            line: 455,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 453,
            column: 6
          },
          end: {
            line: 455,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 453
      },
      "35": {
        loc: {
          start: {
            line: 467,
            column: 59
          },
          end: {
            line: 467,
            column: 84
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 467,
            column: 82
          },
          end: {
            line: 467,
            column: 84
          }
        }],
        line: 467
      },
      "36": {
        loc: {
          start: {
            line: 469,
            column: 4
          },
          end: {
            line: 471,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 469,
            column: 4
          },
          end: {
            line: 471,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 469
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0],
      "36": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f2eff6c3b257d35f21b30acc1098533f7533d40d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2jjpjgc9x5 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2jjpjgc9x5();
import { Platform } from 'react-native';
import { authService } from "../auth/AuthService";
import { handleError } from "../../utils/errorHandling";
import env from "../../config/environment";
var StripeService = function () {
  function StripeService() {
    _classCallCheck(this, StripeService);
    this.stripe = (cov_2jjpjgc9x5().s[0]++, null);
    this.isInitialized = (cov_2jjpjgc9x5().s[1]++, false);
    cov_2jjpjgc9x5().f[0]++;
    cov_2jjpjgc9x5().s[2]++;
    this.stripePublishableKey = (cov_2jjpjgc9x5().b[0][0]++, env.get('STRIPE_PUBLISHABLE_KEY')) || (cov_2jjpjgc9x5().b[0][1]++, '');
    cov_2jjpjgc9x5().s[3]++;
    this.apiBaseUrl = (cov_2jjpjgc9x5().b[1][0]++, env.get('API_BASE_URL')) || (cov_2jjpjgc9x5().b[1][1]++, 'https://api.acemind.com');
  }
  return _createClass(StripeService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_2jjpjgc9x5().f[1]++;
        cov_2jjpjgc9x5().s[4]++;
        try {
          cov_2jjpjgc9x5().s[5]++;
          if (this.isInitialized) {
            cov_2jjpjgc9x5().b[2][0]++;
            cov_2jjpjgc9x5().s[6]++;
            return {
              success: true
            };
          } else {
            cov_2jjpjgc9x5().b[2][1]++;
          }
          cov_2jjpjgc9x5().s[7]++;
          if (!this.stripePublishableKey) {
            cov_2jjpjgc9x5().b[3][0]++;
            cov_2jjpjgc9x5().s[8]++;
            return {
              success: false,
              error: 'Stripe publishable key not configured'
            };
          } else {
            cov_2jjpjgc9x5().b[3][1]++;
          }
          cov_2jjpjgc9x5().s[9]++;
          if (Platform.OS === 'web') {
            cov_2jjpjgc9x5().b[4][0]++;
            cov_2jjpjgc9x5().s[10]++;
            yield this.loadStripeJS();
            cov_2jjpjgc9x5().s[11]++;
            this.stripe = window.Stripe(this.stripePublishableKey);
          } else {
            cov_2jjpjgc9x5().b[4][1]++;
            cov_2jjpjgc9x5().s[12]++;
            console.log('Stripe React Native SDK would be initialized here');
          }
          cov_2jjpjgc9x5().s[13]++;
          this.isInitialized = true;
          cov_2jjpjgc9x5().s[14]++;
          return {
            success: true
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[15]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[16]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "loadStripeJS",
    value: (function () {
      var _loadStripeJS = _asyncToGenerator(function* () {
        cov_2jjpjgc9x5().f[2]++;
        cov_2jjpjgc9x5().s[17]++;
        return new Promise(function (resolve, reject) {
          cov_2jjpjgc9x5().f[3]++;
          cov_2jjpjgc9x5().s[18]++;
          if ((cov_2jjpjgc9x5().b[6][0]++, typeof window !== 'undefined') && (cov_2jjpjgc9x5().b[6][1]++, window.Stripe)) {
            cov_2jjpjgc9x5().b[5][0]++;
            cov_2jjpjgc9x5().s[19]++;
            resolve();
            cov_2jjpjgc9x5().s[20]++;
            return;
          } else {
            cov_2jjpjgc9x5().b[5][1]++;
          }
          var script = (cov_2jjpjgc9x5().s[21]++, document.createElement('script'));
          cov_2jjpjgc9x5().s[22]++;
          script.src = 'https://js.stripe.com/v3/';
          cov_2jjpjgc9x5().s[23]++;
          script.onload = function () {
            cov_2jjpjgc9x5().f[4]++;
            cov_2jjpjgc9x5().s[24]++;
            return resolve();
          };
          cov_2jjpjgc9x5().s[25]++;
          script.onerror = function () {
            cov_2jjpjgc9x5().f[5]++;
            cov_2jjpjgc9x5().s[26]++;
            return reject(new Error('Failed to load Stripe.js'));
          };
          cov_2jjpjgc9x5().s[27]++;
          document.head.appendChild(script);
        });
      });
      function loadStripeJS() {
        return _loadStripeJS.apply(this, arguments);
      }
      return loadStripeJS;
    }())
  }, {
    key: "createCustomer",
    value: (function () {
      var _createCustomer = _asyncToGenerator(function* (customerData) {
        cov_2jjpjgc9x5().f[6]++;
        cov_2jjpjgc9x5().s[28]++;
        try {
          var response = (cov_2jjpjgc9x5().s[29]++, yield this.makeAuthenticatedRequest('/stripe/customers', {
            method: 'POST',
            body: JSON.stringify(customerData)
          }));
          cov_2jjpjgc9x5().s[30]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[7][0]++;
            var error = (cov_2jjpjgc9x5().s[31]++, yield response.json());
            cov_2jjpjgc9x5().s[32]++;
            return {
              customer: null,
              error: (cov_2jjpjgc9x5().b[8][0]++, error.message) || (cov_2jjpjgc9x5().b[8][1]++, 'Failed to create customer')
            };
          } else {
            cov_2jjpjgc9x5().b[7][1]++;
          }
          var customer = (cov_2jjpjgc9x5().s[33]++, yield response.json());
          cov_2jjpjgc9x5().s[34]++;
          return {
            customer: customer
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[35]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[36]++;
          return {
            customer: null,
            error: appError.userMessage
          };
        }
      });
      function createCustomer(_x) {
        return _createCustomer.apply(this, arguments);
      }
      return createCustomer;
    }())
  }, {
    key: "getCustomer",
    value: (function () {
      var _getCustomer = _asyncToGenerator(function* () {
        cov_2jjpjgc9x5().f[7]++;
        cov_2jjpjgc9x5().s[37]++;
        try {
          var response = (cov_2jjpjgc9x5().s[38]++, yield this.makeAuthenticatedRequest('/stripe/customer'));
          cov_2jjpjgc9x5().s[39]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[9][0]++;
            var error = (cov_2jjpjgc9x5().s[40]++, yield response.json());
            cov_2jjpjgc9x5().s[41]++;
            return {
              customer: null,
              error: (cov_2jjpjgc9x5().b[10][0]++, error.message) || (cov_2jjpjgc9x5().b[10][1]++, 'Failed to get customer')
            };
          } else {
            cov_2jjpjgc9x5().b[9][1]++;
          }
          var customer = (cov_2jjpjgc9x5().s[42]++, yield response.json());
          cov_2jjpjgc9x5().s[43]++;
          return {
            customer: customer
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[44]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[45]++;
          return {
            customer: null,
            error: appError.userMessage
          };
        }
      });
      function getCustomer() {
        return _getCustomer.apply(this, arguments);
      }
      return getCustomer;
    }())
  }, {
    key: "createPaymentMethod",
    value: (function () {
      var _createPaymentMethod = _asyncToGenerator(function* (paymentData) {
        cov_2jjpjgc9x5().f[8]++;
        cov_2jjpjgc9x5().s[46]++;
        try {
          cov_2jjpjgc9x5().s[47]++;
          if (!this.stripe) {
            cov_2jjpjgc9x5().b[11][0]++;
            cov_2jjpjgc9x5().s[48]++;
            yield this.initialize();
          } else {
            cov_2jjpjgc9x5().b[11][1]++;
          }
          var _ref = (cov_2jjpjgc9x5().s[49]++, yield this.stripe.createPaymentMethod(paymentData)),
            paymentMethod = _ref.paymentMethod,
            error = _ref.error;
          cov_2jjpjgc9x5().s[50]++;
          if (error) {
            cov_2jjpjgc9x5().b[12][0]++;
            cov_2jjpjgc9x5().s[51]++;
            return {
              paymentMethod: null,
              error: error.message
            };
          } else {
            cov_2jjpjgc9x5().b[12][1]++;
          }
          cov_2jjpjgc9x5().s[52]++;
          return {
            paymentMethod: paymentMethod
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[53]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[54]++;
          return {
            paymentMethod: null,
            error: appError.userMessage
          };
        }
      });
      function createPaymentMethod(_x2) {
        return _createPaymentMethod.apply(this, arguments);
      }
      return createPaymentMethod;
    }())
  }, {
    key: "attachPaymentMethod",
    value: (function () {
      var _attachPaymentMethod = _asyncToGenerator(function* (paymentMethodId) {
        cov_2jjpjgc9x5().f[9]++;
        cov_2jjpjgc9x5().s[55]++;
        try {
          var response = (cov_2jjpjgc9x5().s[56]++, yield this.makeAuthenticatedRequest('/stripe/payment-methods/attach', {
            method: 'POST',
            body: JSON.stringify({
              payment_method_id: paymentMethodId
            })
          }));
          cov_2jjpjgc9x5().s[57]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[13][0]++;
            var error = (cov_2jjpjgc9x5().s[58]++, yield response.json());
            cov_2jjpjgc9x5().s[59]++;
            return {
              success: false,
              error: (cov_2jjpjgc9x5().b[14][0]++, error.message) || (cov_2jjpjgc9x5().b[14][1]++, 'Failed to attach payment method')
            };
          } else {
            cov_2jjpjgc9x5().b[13][1]++;
          }
          cov_2jjpjgc9x5().s[60]++;
          return {
            success: true
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[61]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[62]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function attachPaymentMethod(_x3) {
        return _attachPaymentMethod.apply(this, arguments);
      }
      return attachPaymentMethod;
    }())
  }, {
    key: "getPaymentMethods",
    value: (function () {
      var _getPaymentMethods = _asyncToGenerator(function* () {
        cov_2jjpjgc9x5().f[10]++;
        cov_2jjpjgc9x5().s[63]++;
        try {
          var response = (cov_2jjpjgc9x5().s[64]++, yield this.makeAuthenticatedRequest('/stripe/payment-methods'));
          cov_2jjpjgc9x5().s[65]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[15][0]++;
            var error = (cov_2jjpjgc9x5().s[66]++, yield response.json());
            cov_2jjpjgc9x5().s[67]++;
            return {
              paymentMethods: [],
              error: (cov_2jjpjgc9x5().b[16][0]++, error.message) || (cov_2jjpjgc9x5().b[16][1]++, 'Failed to get payment methods')
            };
          } else {
            cov_2jjpjgc9x5().b[15][1]++;
          }
          var data = (cov_2jjpjgc9x5().s[68]++, yield response.json());
          cov_2jjpjgc9x5().s[69]++;
          return {
            paymentMethods: (cov_2jjpjgc9x5().b[17][0]++, data.data) || (cov_2jjpjgc9x5().b[17][1]++, [])
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[70]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[71]++;
          return {
            paymentMethods: [],
            error: appError.userMessage
          };
        }
      });
      function getPaymentMethods() {
        return _getPaymentMethods.apply(this, arguments);
      }
      return getPaymentMethods;
    }())
  }, {
    key: "createSubscription",
    value: (function () {
      var _createSubscription = _asyncToGenerator(function* (priceId, paymentMethodId) {
        cov_2jjpjgc9x5().f[11]++;
        cov_2jjpjgc9x5().s[72]++;
        try {
          var response = (cov_2jjpjgc9x5().s[73]++, yield this.makeAuthenticatedRequest('/stripe/subscriptions', {
            method: 'POST',
            body: JSON.stringify({
              price_id: priceId,
              payment_method_id: paymentMethodId
            })
          }));
          cov_2jjpjgc9x5().s[74]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[18][0]++;
            var error = (cov_2jjpjgc9x5().s[75]++, yield response.json());
            cov_2jjpjgc9x5().s[76]++;
            return {
              subscription: null,
              error: (cov_2jjpjgc9x5().b[19][0]++, error.message) || (cov_2jjpjgc9x5().b[19][1]++, 'Failed to create subscription')
            };
          } else {
            cov_2jjpjgc9x5().b[18][1]++;
          }
          var subscription = (cov_2jjpjgc9x5().s[77]++, yield response.json());
          cov_2jjpjgc9x5().s[78]++;
          return {
            subscription: subscription
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[79]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[80]++;
          return {
            subscription: null,
            error: appError.userMessage
          };
        }
      });
      function createSubscription(_x4, _x5) {
        return _createSubscription.apply(this, arguments);
      }
      return createSubscription;
    }())
  }, {
    key: "getSubscription",
    value: (function () {
      var _getSubscription = _asyncToGenerator(function* () {
        cov_2jjpjgc9x5().f[12]++;
        cov_2jjpjgc9x5().s[81]++;
        try {
          var response = (cov_2jjpjgc9x5().s[82]++, yield this.makeAuthenticatedRequest('/stripe/subscription'));
          cov_2jjpjgc9x5().s[83]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[20][0]++;
            cov_2jjpjgc9x5().s[84]++;
            if (response.status === 404) {
              cov_2jjpjgc9x5().b[21][0]++;
              cov_2jjpjgc9x5().s[85]++;
              return {
                subscription: null
              };
            } else {
              cov_2jjpjgc9x5().b[21][1]++;
            }
            var error = (cov_2jjpjgc9x5().s[86]++, yield response.json());
            cov_2jjpjgc9x5().s[87]++;
            return {
              subscription: null,
              error: (cov_2jjpjgc9x5().b[22][0]++, error.message) || (cov_2jjpjgc9x5().b[22][1]++, 'Failed to get subscription')
            };
          } else {
            cov_2jjpjgc9x5().b[20][1]++;
          }
          var subscription = (cov_2jjpjgc9x5().s[88]++, yield response.json());
          cov_2jjpjgc9x5().s[89]++;
          return {
            subscription: subscription
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[90]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[91]++;
          return {
            subscription: null,
            error: appError.userMessage
          };
        }
      });
      function getSubscription() {
        return _getSubscription.apply(this, arguments);
      }
      return getSubscription;
    }())
  }, {
    key: "cancelSubscription",
    value: (function () {
      var _cancelSubscription = _asyncToGenerator(function* () {
        var immediately = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2jjpjgc9x5().b[23][0]++, false);
        cov_2jjpjgc9x5().f[13]++;
        cov_2jjpjgc9x5().s[92]++;
        try {
          var response = (cov_2jjpjgc9x5().s[93]++, yield this.makeAuthenticatedRequest('/stripe/subscription/cancel', {
            method: 'POST',
            body: JSON.stringify({
              immediately: immediately
            })
          }));
          cov_2jjpjgc9x5().s[94]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[24][0]++;
            var error = (cov_2jjpjgc9x5().s[95]++, yield response.json());
            cov_2jjpjgc9x5().s[96]++;
            return {
              subscription: null,
              error: (cov_2jjpjgc9x5().b[25][0]++, error.message) || (cov_2jjpjgc9x5().b[25][1]++, 'Failed to cancel subscription')
            };
          } else {
            cov_2jjpjgc9x5().b[24][1]++;
          }
          var subscription = (cov_2jjpjgc9x5().s[97]++, yield response.json());
          cov_2jjpjgc9x5().s[98]++;
          return {
            subscription: subscription
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[99]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[100]++;
          return {
            subscription: null,
            error: appError.userMessage
          };
        }
      });
      function cancelSubscription() {
        return _cancelSubscription.apply(this, arguments);
      }
      return cancelSubscription;
    }())
  }, {
    key: "getInvoices",
    value: (function () {
      var _getInvoices = _asyncToGenerator(function* () {
        var limit = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2jjpjgc9x5().b[26][0]++, 10);
        cov_2jjpjgc9x5().f[14]++;
        cov_2jjpjgc9x5().s[101]++;
        try {
          var response = (cov_2jjpjgc9x5().s[102]++, yield this.makeAuthenticatedRequest(`/stripe/invoices?limit=${limit}`));
          cov_2jjpjgc9x5().s[103]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[27][0]++;
            var error = (cov_2jjpjgc9x5().s[104]++, yield response.json());
            cov_2jjpjgc9x5().s[105]++;
            return {
              invoices: [],
              error: (cov_2jjpjgc9x5().b[28][0]++, error.message) || (cov_2jjpjgc9x5().b[28][1]++, 'Failed to get invoices')
            };
          } else {
            cov_2jjpjgc9x5().b[27][1]++;
          }
          var data = (cov_2jjpjgc9x5().s[106]++, yield response.json());
          cov_2jjpjgc9x5().s[107]++;
          return {
            invoices: (cov_2jjpjgc9x5().b[29][0]++, data.data) || (cov_2jjpjgc9x5().b[29][1]++, [])
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[108]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[109]++;
          return {
            invoices: [],
            error: appError.userMessage
          };
        }
      });
      function getInvoices() {
        return _getInvoices.apply(this, arguments);
      }
      return getInvoices;
    }())
  }, {
    key: "createPaymentIntent",
    value: (function () {
      var _createPaymentIntent = _asyncToGenerator(function* (amount) {
        var currency = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2jjpjgc9x5().b[30][0]++, 'usd');
        cov_2jjpjgc9x5().f[15]++;
        cov_2jjpjgc9x5().s[110]++;
        try {
          var response = (cov_2jjpjgc9x5().s[111]++, yield this.makeAuthenticatedRequest('/stripe/payment-intents', {
            method: 'POST',
            body: JSON.stringify({
              amount: amount,
              currency: currency
            })
          }));
          cov_2jjpjgc9x5().s[112]++;
          if (!response.ok) {
            cov_2jjpjgc9x5().b[31][0]++;
            var error = (cov_2jjpjgc9x5().s[113]++, yield response.json());
            cov_2jjpjgc9x5().s[114]++;
            return {
              paymentIntent: null,
              error: (cov_2jjpjgc9x5().b[32][0]++, error.message) || (cov_2jjpjgc9x5().b[32][1]++, 'Failed to create payment intent')
            };
          } else {
            cov_2jjpjgc9x5().b[31][1]++;
          }
          var paymentIntent = (cov_2jjpjgc9x5().s[115]++, yield response.json());
          cov_2jjpjgc9x5().s[116]++;
          return {
            paymentIntent: paymentIntent
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[117]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[118]++;
          return {
            paymentIntent: null,
            error: appError.userMessage
          };
        }
      });
      function createPaymentIntent(_x6) {
        return _createPaymentIntent.apply(this, arguments);
      }
      return createPaymentIntent;
    }())
  }, {
    key: "confirmPaymentIntent",
    value: (function () {
      var _confirmPaymentIntent = _asyncToGenerator(function* (clientSecret, paymentMethodId) {
        cov_2jjpjgc9x5().f[16]++;
        cov_2jjpjgc9x5().s[119]++;
        try {
          cov_2jjpjgc9x5().s[120]++;
          if (!this.stripe) {
            cov_2jjpjgc9x5().b[33][0]++;
            cov_2jjpjgc9x5().s[121]++;
            yield this.initialize();
          } else {
            cov_2jjpjgc9x5().b[33][1]++;
          }
          var _ref2 = (cov_2jjpjgc9x5().s[122]++, yield this.stripe.confirmCardPayment(clientSecret, {
              payment_method: paymentMethodId
            })),
            error = _ref2.error;
          cov_2jjpjgc9x5().s[123]++;
          if (error) {
            cov_2jjpjgc9x5().b[34][0]++;
            cov_2jjpjgc9x5().s[124]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_2jjpjgc9x5().b[34][1]++;
          }
          cov_2jjpjgc9x5().s[125]++;
          return {
            success: true
          };
        } catch (error) {
          var appError = (cov_2jjpjgc9x5().s[126]++, handleError(error, {
            showAlert: false
          }));
          cov_2jjpjgc9x5().s[127]++;
          return {
            success: false,
            error: appError.userMessage
          };
        }
      });
      function confirmPaymentIntent(_x7, _x8) {
        return _confirmPaymentIntent.apply(this, arguments);
      }
      return confirmPaymentIntent;
    }())
  }, {
    key: "makeAuthenticatedRequest",
    value: (function () {
      var _makeAuthenticatedRequest = _asyncToGenerator(function* (endpoint) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2jjpjgc9x5().b[35][0]++, {});
        cov_2jjpjgc9x5().f[17]++;
        var session = (cov_2jjpjgc9x5().s[128]++, authService.getCurrentState().session);
        cov_2jjpjgc9x5().s[129]++;
        if (!(session != null && session.access_token)) {
          cov_2jjpjgc9x5().b[36][0]++;
          cov_2jjpjgc9x5().s[130]++;
          throw new Error('User not authenticated');
        } else {
          cov_2jjpjgc9x5().b[36][1]++;
        }
        cov_2jjpjgc9x5().s[131]++;
        return fetch(`${this.apiBaseUrl}${endpoint}`, Object.assign({}, options, {
          headers: Object.assign({
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${session.access_token}`
          }, options.headers)
        }));
      });
      function makeAuthenticatedRequest(_x9) {
        return _makeAuthenticatedRequest.apply(this, arguments);
      }
      return makeAuthenticatedRequest;
    }())
  }]);
}();
export var stripeService = (cov_2jjpjgc9x5().s[132]++, new StripeService());
export default stripeService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJQbGF0Zm9ybSIsImF1dGhTZXJ2aWNlIiwiaGFuZGxlRXJyb3IiLCJlbnYiLCJTdHJpcGVTZXJ2aWNlIiwiX2NsYXNzQ2FsbENoZWNrIiwic3RyaXBlIiwiY292XzJqanBqZ2M5eDUiLCJzIiwiaXNJbml0aWFsaXplZCIsImYiLCJzdHJpcGVQdWJsaXNoYWJsZUtleSIsImIiLCJnZXQiLCJhcGlCYXNlVXJsIiwiX2NyZWF0ZUNsYXNzIiwia2V5IiwidmFsdWUiLCJfaW5pdGlhbGl6ZSIsIl9hc3luY1RvR2VuZXJhdG9yIiwic3VjY2VzcyIsImVycm9yIiwiT1MiLCJsb2FkU3RyaXBlSlMiLCJ3aW5kb3ciLCJTdHJpcGUiLCJjb25zb2xlIiwibG9nIiwiYXBwRXJyb3IiLCJzaG93QWxlcnQiLCJ1c2VyTWVzc2FnZSIsImluaXRpYWxpemUiLCJhcHBseSIsImFyZ3VtZW50cyIsIl9sb2FkU3RyaXBlSlMiLCJQcm9taXNlIiwicmVzb2x2ZSIsInJlamVjdCIsInNjcmlwdCIsImRvY3VtZW50IiwiY3JlYXRlRWxlbWVudCIsInNyYyIsIm9ubG9hZCIsIm9uZXJyb3IiLCJFcnJvciIsImhlYWQiLCJhcHBlbmRDaGlsZCIsIl9jcmVhdGVDdXN0b21lciIsImN1c3RvbWVyRGF0YSIsInJlc3BvbnNlIiwibWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0IiwibWV0aG9kIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJvayIsImpzb24iLCJjdXN0b21lciIsIm1lc3NhZ2UiLCJjcmVhdGVDdXN0b21lciIsIl94IiwiX2dldEN1c3RvbWVyIiwiZ2V0Q3VzdG9tZXIiLCJfY3JlYXRlUGF5bWVudE1ldGhvZCIsInBheW1lbnREYXRhIiwiX3JlZiIsImNyZWF0ZVBheW1lbnRNZXRob2QiLCJwYXltZW50TWV0aG9kIiwiX3gyIiwiX2F0dGFjaFBheW1lbnRNZXRob2QiLCJwYXltZW50TWV0aG9kSWQiLCJwYXltZW50X21ldGhvZF9pZCIsImF0dGFjaFBheW1lbnRNZXRob2QiLCJfeDMiLCJfZ2V0UGF5bWVudE1ldGhvZHMiLCJwYXltZW50TWV0aG9kcyIsImRhdGEiLCJnZXRQYXltZW50TWV0aG9kcyIsIl9jcmVhdGVTdWJzY3JpcHRpb24iLCJwcmljZUlkIiwicHJpY2VfaWQiLCJzdWJzY3JpcHRpb24iLCJjcmVhdGVTdWJzY3JpcHRpb24iLCJfeDQiLCJfeDUiLCJfZ2V0U3Vic2NyaXB0aW9uIiwic3RhdHVzIiwiZ2V0U3Vic2NyaXB0aW9uIiwiX2NhbmNlbFN1YnNjcmlwdGlvbiIsImltbWVkaWF0ZWx5IiwibGVuZ3RoIiwidW5kZWZpbmVkIiwiY2FuY2VsU3Vic2NyaXB0aW9uIiwiX2dldEludm9pY2VzIiwibGltaXQiLCJpbnZvaWNlcyIsImdldEludm9pY2VzIiwiX2NyZWF0ZVBheW1lbnRJbnRlbnQiLCJhbW91bnQiLCJjdXJyZW5jeSIsInBheW1lbnRJbnRlbnQiLCJjcmVhdGVQYXltZW50SW50ZW50IiwiX3g2IiwiX2NvbmZpcm1QYXltZW50SW50ZW50IiwiY2xpZW50U2VjcmV0IiwiX3JlZjIiLCJjb25maXJtQ2FyZFBheW1lbnQiLCJwYXltZW50X21ldGhvZCIsImNvbmZpcm1QYXltZW50SW50ZW50IiwiX3g3IiwiX3g4IiwiX21ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCIsImVuZHBvaW50Iiwib3B0aW9ucyIsInNlc3Npb24iLCJnZXRDdXJyZW50U3RhdGUiLCJhY2Nlc3NfdG9rZW4iLCJmZXRjaCIsIk9iamVjdCIsImFzc2lnbiIsImhlYWRlcnMiLCJfeDkiLCJzdHJpcGVTZXJ2aWNlIl0sInNvdXJjZXMiOlsiU3RyaXBlU2VydmljZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFN0cmlwZSBQYXltZW50IFNlcnZpY2VcbiAqIFxuICogUmVhbCBTdHJpcGUgaW50ZWdyYXRpb24gZm9yIHBheW1lbnQgcHJvY2Vzc2luZywgc3Vic2NyaXB0aW9ucywgYW5kIGJpbGxpbmdcbiAqL1xuXG5pbXBvcnQgeyBQbGF0Zm9ybSB9IGZyb20gJ3JlYWN0LW5hdGl2ZSc7XG5pbXBvcnQgeyBhdXRoU2VydmljZSB9IGZyb20gJy4uL2F1dGgvQXV0aFNlcnZpY2UnO1xuaW1wb3J0IHsgaGFuZGxlRXJyb3IsIGxvZ0Vycm9yIH0gZnJvbSAnQC91dGlscy9lcnJvckhhbmRsaW5nJztcbmltcG9ydCBlbnYgZnJvbSAnQC9jb25maWcvZW52aXJvbm1lbnQnO1xuXG4vLyBTdHJpcGUgV2ViIFNESyB0eXBlc1xuZGVjbGFyZSBnbG9iYWwge1xuICBpbnRlcmZhY2UgV2luZG93IHtcbiAgICBTdHJpcGU6IGFueTtcbiAgICBzdHJpcGU6IGFueTtcbiAgfVxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFN0cmlwZUN1c3RvbWVyIHtcbiAgaWQ6IHN0cmluZztcbiAgZW1haWw6IHN0cmluZztcbiAgbmFtZT86IHN0cmluZztcbiAgcGhvbmU/OiBzdHJpbmc7XG4gIGNyZWF0ZWQ6IG51bWJlcjtcbiAgZGVmYXVsdF9zb3VyY2U/OiBzdHJpbmc7XG4gIGludm9pY2Vfc2V0dGluZ3M6IHtcbiAgICBkZWZhdWx0X3BheW1lbnRfbWV0aG9kPzogc3RyaW5nO1xuICB9O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFN0cmlwZVBheW1lbnRNZXRob2Qge1xuICBpZDogc3RyaW5nO1xuICB0eXBlOiAnY2FyZCcgfCAnYXBwbGVfcGF5JyB8ICdnb29nbGVfcGF5JztcbiAgY2FyZD86IHtcbiAgICBicmFuZDogc3RyaW5nO1xuICAgIGxhc3Q0OiBzdHJpbmc7XG4gICAgZXhwX21vbnRoOiBudW1iZXI7XG4gICAgZXhwX3llYXI6IG51bWJlcjtcbiAgICBjb3VudHJ5OiBzdHJpbmc7XG4gIH07XG4gIGJpbGxpbmdfZGV0YWlsczoge1xuICAgIG5hbWU/OiBzdHJpbmc7XG4gICAgZW1haWw/OiBzdHJpbmc7XG4gICAgcGhvbmU/OiBzdHJpbmc7XG4gICAgYWRkcmVzcz86IHtcbiAgICAgIGNpdHk/OiBzdHJpbmc7XG4gICAgICBjb3VudHJ5Pzogc3RyaW5nO1xuICAgICAgbGluZTE/OiBzdHJpbmc7XG4gICAgICBsaW5lMj86IHN0cmluZztcbiAgICAgIHBvc3RhbF9jb2RlPzogc3RyaW5nO1xuICAgICAgc3RhdGU/OiBzdHJpbmc7XG4gICAgfTtcbiAgfTtcbiAgY3JlYXRlZDogbnVtYmVyO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFN0cmlwZVN1YnNjcmlwdGlvbiB7XG4gIGlkOiBzdHJpbmc7XG4gIGN1c3RvbWVyOiBzdHJpbmc7XG4gIHN0YXR1czogJ2FjdGl2ZScgfCAncGFzdF9kdWUnIHwgJ3VucGFpZCcgfCAnY2FuY2VsZWQnIHwgJ2luY29tcGxldGUnIHwgJ2luY29tcGxldGVfZXhwaXJlZCcgfCAndHJpYWxpbmcnO1xuICBjdXJyZW50X3BlcmlvZF9zdGFydDogbnVtYmVyO1xuICBjdXJyZW50X3BlcmlvZF9lbmQ6IG51bWJlcjtcbiAgdHJpYWxfc3RhcnQ/OiBudW1iZXI7XG4gIHRyaWFsX2VuZD86IG51bWJlcjtcbiAgY2FuY2VsX2F0X3BlcmlvZF9lbmQ6IGJvb2xlYW47XG4gIGNhbmNlbGVkX2F0PzogbnVtYmVyO1xuICBpdGVtczoge1xuICAgIGRhdGE6IEFycmF5PHtcbiAgICAgIGlkOiBzdHJpbmc7XG4gICAgICBwcmljZToge1xuICAgICAgICBpZDogc3RyaW5nO1xuICAgICAgICBuaWNrbmFtZT86IHN0cmluZztcbiAgICAgICAgdW5pdF9hbW91bnQ6IG51bWJlcjtcbiAgICAgICAgY3VycmVuY3k6IHN0cmluZztcbiAgICAgICAgcmVjdXJyaW5nOiB7XG4gICAgICAgICAgaW50ZXJ2YWw6ICdtb250aCcgfCAneWVhcic7XG4gICAgICAgICAgaW50ZXJ2YWxfY291bnQ6IG51bWJlcjtcbiAgICAgICAgfTtcbiAgICAgIH07XG4gICAgICBxdWFudGl0eTogbnVtYmVyO1xuICAgIH0+O1xuICB9O1xuICBsYXRlc3RfaW52b2ljZT86IHtcbiAgICBpZDogc3RyaW5nO1xuICAgIHN0YXR1czogc3RyaW5nO1xuICAgIGFtb3VudF9wYWlkOiBudW1iZXI7XG4gICAgYW1vdW50X2R1ZTogbnVtYmVyO1xuICAgIGhvc3RlZF9pbnZvaWNlX3VybDogc3RyaW5nO1xuICAgIGludm9pY2VfcGRmOiBzdHJpbmc7XG4gIH07XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgU3RyaXBlSW52b2ljZSB7XG4gIGlkOiBzdHJpbmc7XG4gIGN1c3RvbWVyOiBzdHJpbmc7XG4gIGFtb3VudF9wYWlkOiBudW1iZXI7XG4gIGFtb3VudF9kdWU6IG51bWJlcjtcbiAgY3VycmVuY3k6IHN0cmluZztcbiAgc3RhdHVzOiAnZHJhZnQnIHwgJ29wZW4nIHwgJ3BhaWQnIHwgJ3VuY29sbGVjdGlibGUnIHwgJ3ZvaWQnO1xuICBjcmVhdGVkOiBudW1iZXI7XG4gIGR1ZV9kYXRlPzogbnVtYmVyO1xuICBob3N0ZWRfaW52b2ljZV91cmw6IHN0cmluZztcbiAgaW52b2ljZV9wZGY6IHN0cmluZztcbiAgbGluZXM6IHtcbiAgICBkYXRhOiBBcnJheTx7XG4gICAgICBpZDogc3RyaW5nO1xuICAgICAgZGVzY3JpcHRpb24/OiBzdHJpbmc7XG4gICAgICBhbW91bnQ6IG51bWJlcjtcbiAgICAgIGN1cnJlbmN5OiBzdHJpbmc7XG4gICAgICBwZXJpb2Q6IHtcbiAgICAgICAgc3RhcnQ6IG51bWJlcjtcbiAgICAgICAgZW5kOiBudW1iZXI7XG4gICAgICB9O1xuICAgIH0+O1xuICB9O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFBheW1lbnRJbnRlbnQge1xuICBpZDogc3RyaW5nO1xuICBhbW91bnQ6IG51bWJlcjtcbiAgY3VycmVuY3k6IHN0cmluZztcbiAgc3RhdHVzOiAncmVxdWlyZXNfcGF5bWVudF9tZXRob2QnIHwgJ3JlcXVpcmVzX2NvbmZpcm1hdGlvbicgfCAncmVxdWlyZXNfYWN0aW9uJyB8ICdwcm9jZXNzaW5nJyB8ICdzdWNjZWVkZWQnIHwgJ2NhbmNlbGVkJztcbiAgY2xpZW50X3NlY3JldDogc3RyaW5nO1xuICBwYXltZW50X21ldGhvZD86IHN0cmluZztcbiAgbGFzdF9wYXltZW50X2Vycm9yPzoge1xuICAgIGNvZGU6IHN0cmluZztcbiAgICBtZXNzYWdlOiBzdHJpbmc7XG4gICAgdHlwZTogc3RyaW5nO1xuICB9O1xufVxuXG5jbGFzcyBTdHJpcGVTZXJ2aWNlIHtcbiAgcHJpdmF0ZSBzdHJpcGVQdWJsaXNoYWJsZUtleTogc3RyaW5nO1xuICBwcml2YXRlIGFwaUJhc2VVcmw6IHN0cmluZztcbiAgcHJpdmF0ZSBzdHJpcGU6IGFueSA9IG51bGw7XG4gIHByaXZhdGUgaXNJbml0aWFsaXplZDogYm9vbGVhbiA9IGZhbHNlO1xuXG4gIGNvbnN0cnVjdG9yKCkge1xuICAgIHRoaXMuc3RyaXBlUHVibGlzaGFibGVLZXkgPSBlbnYuZ2V0KCdTVFJJUEVfUFVCTElTSEFCTEVfS0VZJykgfHwgJyc7XG4gICAgdGhpcy5hcGlCYXNlVXJsID0gZW52LmdldCgnQVBJX0JBU0VfVVJMJykgfHwgJ2h0dHBzOi8vYXBpLmFjZW1pbmQuY29tJztcbiAgfVxuXG4gIC8qKlxuICAgKiBJbml0aWFsaXplIFN0cmlwZSBTREtcbiAgICovXG4gIGFzeW5jIGluaXRpYWxpemUoKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKHRoaXMuaXNJbml0aWFsaXplZCkge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG4gICAgICB9XG5cbiAgICAgIGlmICghdGhpcy5zdHJpcGVQdWJsaXNoYWJsZUtleSkge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6ICdTdHJpcGUgcHVibGlzaGFibGUga2V5IG5vdCBjb25maWd1cmVkJyB9O1xuICAgICAgfVxuXG4gICAgICBpZiAoUGxhdGZvcm0uT1MgPT09ICd3ZWInKSB7XG4gICAgICAgIC8vIExvYWQgU3RyaXBlLmpzIGZvciB3ZWJcbiAgICAgICAgYXdhaXQgdGhpcy5sb2FkU3RyaXBlSlMoKTtcbiAgICAgICAgdGhpcy5zdHJpcGUgPSB3aW5kb3cuU3RyaXBlKHRoaXMuc3RyaXBlUHVibGlzaGFibGVLZXkpO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgLy8gRm9yIFJlYWN0IE5hdGl2ZSwgd2Ugd291bGQgdXNlIEBzdHJpcGUvc3RyaXBlLXJlYWN0LW5hdGl2ZVxuICAgICAgICAvLyBUaGlzIGlzIGEgcGxhY2Vob2xkZXIgZm9yIHRoZSBuYXRpdmUgaW1wbGVtZW50YXRpb25cbiAgICAgICAgY29uc29sZS5sb2coJ1N0cmlwZSBSZWFjdCBOYXRpdmUgU0RLIHdvdWxkIGJlIGluaXRpYWxpemVkIGhlcmUnKTtcbiAgICAgIH1cblxuICAgICAgdGhpcy5pc0luaXRpYWxpemVkID0gdHJ1ZTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgYXBwRXJyb3IgPSBoYW5kbGVFcnJvcihlcnJvciwgeyBzaG93QWxlcnQ6IGZhbHNlIH0pO1xuICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBhcHBFcnJvci51c2VyTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBMb2FkIFN0cmlwZS5qcyBzY3JpcHQgZm9yIHdlYlxuICAgKi9cbiAgcHJpdmF0ZSBhc3luYyBsb2FkU3RyaXBlSlMoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cuU3RyaXBlKSB7XG4gICAgICAgIHJlc29sdmUoKTtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBzY3JpcHQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdzY3JpcHQnKTtcbiAgICAgIHNjcmlwdC5zcmMgPSAnaHR0cHM6Ly9qcy5zdHJpcGUuY29tL3YzLyc7XG4gICAgICBzY3JpcHQub25sb2FkID0gKCkgPT4gcmVzb2x2ZSgpO1xuICAgICAgc2NyaXB0Lm9uZXJyb3IgPSAoKSA9PiByZWplY3QobmV3IEVycm9yKCdGYWlsZWQgdG8gbG9hZCBTdHJpcGUuanMnKSk7XG4gICAgICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKHNjcmlwdCk7XG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIG9yIGdldCBTdHJpcGUgY3VzdG9tZXJcbiAgICovXG4gIGFzeW5jIGNyZWF0ZUN1c3RvbWVyKGN1c3RvbWVyRGF0YToge1xuICAgIGVtYWlsOiBzdHJpbmc7XG4gICAgbmFtZT86IHN0cmluZztcbiAgICBwaG9uZT86IHN0cmluZztcbiAgfSk6IFByb21pc2U8eyBjdXN0b21lcjogU3RyaXBlQ3VzdG9tZXIgfCBudWxsOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5tYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoJy9zdHJpcGUvY3VzdG9tZXJzJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoY3VzdG9tZXJEYXRhKSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICByZXR1cm4geyBjdXN0b21lcjogbnVsbCwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBjcmVhdGUgY3VzdG9tZXInIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGN1c3RvbWVyID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIHsgY3VzdG9tZXIgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgYXBwRXJyb3IgPSBoYW5kbGVFcnJvcihlcnJvciwgeyBzaG93QWxlcnQ6IGZhbHNlIH0pO1xuICAgICAgcmV0dXJuIHsgY3VzdG9tZXI6IG51bGwsIGVycm9yOiBhcHBFcnJvci51c2VyTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgY3VzdG9tZXIgZGV0YWlsc1xuICAgKi9cbiAgYXN5bmMgZ2V0Q3VzdG9tZXIoKTogUHJvbWlzZTx7IGN1c3RvbWVyOiBTdHJpcGVDdXN0b21lciB8IG51bGw7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLm1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCgnL3N0cmlwZS9jdXN0b21lcicpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICByZXR1cm4geyBjdXN0b21lcjogbnVsbCwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBnZXQgY3VzdG9tZXInIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGN1c3RvbWVyID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIHsgY3VzdG9tZXIgfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgYXBwRXJyb3IgPSBoYW5kbGVFcnJvcihlcnJvciwgeyBzaG93QWxlcnQ6IGZhbHNlIH0pO1xuICAgICAgcmV0dXJuIHsgY3VzdG9tZXI6IG51bGwsIGVycm9yOiBhcHBFcnJvci51c2VyTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDcmVhdGUgcGF5bWVudCBtZXRob2RcbiAgICovXG4gIGFzeW5jIGNyZWF0ZVBheW1lbnRNZXRob2QocGF5bWVudERhdGE6IHtcbiAgICB0eXBlOiAnY2FyZCc7XG4gICAgY2FyZDoge1xuICAgICAgbnVtYmVyOiBzdHJpbmc7XG4gICAgICBleHBfbW9udGg6IG51bWJlcjtcbiAgICAgIGV4cF95ZWFyOiBudW1iZXI7XG4gICAgICBjdmM6IHN0cmluZztcbiAgICB9O1xuICAgIGJpbGxpbmdfZGV0YWlscz86IHtcbiAgICAgIG5hbWU/OiBzdHJpbmc7XG4gICAgICBlbWFpbD86IHN0cmluZztcbiAgICAgIHBob25lPzogc3RyaW5nO1xuICAgICAgYWRkcmVzcz86IHtcbiAgICAgICAgY2l0eT86IHN0cmluZztcbiAgICAgICAgY291bnRyeT86IHN0cmluZztcbiAgICAgICAgbGluZTE/OiBzdHJpbmc7XG4gICAgICAgIGxpbmUyPzogc3RyaW5nO1xuICAgICAgICBwb3N0YWxfY29kZT86IHN0cmluZztcbiAgICAgICAgc3RhdGU/OiBzdHJpbmc7XG4gICAgICB9O1xuICAgIH07XG4gIH0pOiBQcm9taXNlPHsgcGF5bWVudE1ldGhvZDogU3RyaXBlUGF5bWVudE1ldGhvZCB8IG51bGw7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKCF0aGlzLnN0cmlwZSkge1xuICAgICAgICBhd2FpdCB0aGlzLmluaXRpYWxpemUoKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgeyBwYXltZW50TWV0aG9kLCBlcnJvciB9ID0gYXdhaXQgdGhpcy5zdHJpcGUuY3JlYXRlUGF5bWVudE1ldGhvZChwYXltZW50RGF0YSk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICByZXR1cm4geyBwYXltZW50TWV0aG9kOiBudWxsLCBlcnJvcjogZXJyb3IubWVzc2FnZSB9O1xuICAgICAgfVxuXG4gICAgICByZXR1cm4geyBwYXltZW50TWV0aG9kIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGFwcEVycm9yID0gaGFuZGxlRXJyb3IoZXJyb3IsIHsgc2hvd0FsZXJ0OiBmYWxzZSB9KTtcbiAgICAgIHJldHVybiB7IHBheW1lbnRNZXRob2Q6IG51bGwsIGVycm9yOiBhcHBFcnJvci51c2VyTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBBdHRhY2ggcGF5bWVudCBtZXRob2QgdG8gY3VzdG9tZXJcbiAgICovXG4gIGFzeW5jIGF0dGFjaFBheW1lbnRNZXRob2QocGF5bWVudE1ldGhvZElkOiBzdHJpbmcpOiBQcm9taXNlPHsgc3VjY2VzczogYm9vbGVhbjsgZXJyb3I/OiBzdHJpbmcgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMubWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KCcvc3RyaXBlL3BheW1lbnQtbWV0aG9kcy9hdHRhY2gnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7IHBheW1lbnRfbWV0aG9kX2lkOiBwYXltZW50TWV0aG9kSWQgfSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yOiBlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gYXR0YWNoIHBheW1lbnQgbWV0aG9kJyB9O1xuICAgICAgfVxuXG4gICAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGFwcEVycm9yID0gaGFuZGxlRXJyb3IoZXJyb3IsIHsgc2hvd0FsZXJ0OiBmYWxzZSB9KTtcbiAgICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvcjogYXBwRXJyb3IudXNlck1lc3NhZ2UgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IGN1c3RvbWVyIHBheW1lbnQgbWV0aG9kc1xuICAgKi9cbiAgYXN5bmMgZ2V0UGF5bWVudE1ldGhvZHMoKTogUHJvbWlzZTx7IHBheW1lbnRNZXRob2RzOiBTdHJpcGVQYXltZW50TWV0aG9kW107IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLm1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCgnL3N0cmlwZS9wYXltZW50LW1ldGhvZHMnKTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgcmV0dXJuIHsgcGF5bWVudE1ldGhvZHM6IFtdLCBlcnJvcjogZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGdldCBwYXltZW50IG1ldGhvZHMnIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4geyBwYXltZW50TWV0aG9kczogZGF0YS5kYXRhIHx8IFtdIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGFwcEVycm9yID0gaGFuZGxlRXJyb3IoZXJyb3IsIHsgc2hvd0FsZXJ0OiBmYWxzZSB9KTtcbiAgICAgIHJldHVybiB7IHBheW1lbnRNZXRob2RzOiBbXSwgZXJyb3I6IGFwcEVycm9yLnVzZXJNZXNzYWdlIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBzdWJzY3JpcHRpb25cbiAgICovXG4gIGFzeW5jIGNyZWF0ZVN1YnNjcmlwdGlvbihwcmljZUlkOiBzdHJpbmcsIHBheW1lbnRNZXRob2RJZD86IHN0cmluZyk6IFByb21pc2U8eyBzdWJzY3JpcHRpb246IFN0cmlwZVN1YnNjcmlwdGlvbiB8IG51bGw7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLm1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCgnL3N0cmlwZS9zdWJzY3JpcHRpb25zJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIHByaWNlX2lkOiBwcmljZUlkLFxuICAgICAgICAgIHBheW1lbnRfbWV0aG9kX2lkOiBwYXltZW50TWV0aG9kSWQsXG4gICAgICAgIH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3IgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHJldHVybiB7IHN1YnNjcmlwdGlvbjogbnVsbCwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfHwgJ0ZhaWxlZCB0byBjcmVhdGUgc3Vic2NyaXB0aW9uJyB9O1xuICAgICAgfVxuXG4gICAgICBjb25zdCBzdWJzY3JpcHRpb24gPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4geyBzdWJzY3JpcHRpb24gfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgYXBwRXJyb3IgPSBoYW5kbGVFcnJvcihlcnJvciwgeyBzaG93QWxlcnQ6IGZhbHNlIH0pO1xuICAgICAgcmV0dXJuIHsgc3Vic2NyaXB0aW9uOiBudWxsLCBlcnJvcjogYXBwRXJyb3IudXNlck1lc3NhZ2UgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogR2V0IGN1cnJlbnQgc3Vic2NyaXB0aW9uXG4gICAqL1xuICBhc3luYyBnZXRTdWJzY3JpcHRpb24oKTogUHJvbWlzZTx7IHN1YnNjcmlwdGlvbjogU3RyaXBlU3Vic2NyaXB0aW9uIHwgbnVsbDsgZXJyb3I/OiBzdHJpbmcgfT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHRoaXMubWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KCcvc3RyaXBlL3N1YnNjcmlwdGlvbicpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwNCkge1xuICAgICAgICAgIHJldHVybiB7IHN1YnNjcmlwdGlvbjogbnVsbCB9OyAvLyBObyBzdWJzY3JpcHRpb24gZm91bmRcbiAgICAgICAgfVxuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgcmV0dXJuIHsgc3Vic2NyaXB0aW9uOiBudWxsLCBlcnJvcjogZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGdldCBzdWJzY3JpcHRpb24nIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHN1YnNjcmlwdGlvbiA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHJldHVybiB7IHN1YnNjcmlwdGlvbiB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBhcHBFcnJvciA9IGhhbmRsZUVycm9yKGVycm9yLCB7IHNob3dBbGVydDogZmFsc2UgfSk7XG4gICAgICByZXR1cm4geyBzdWJzY3JpcHRpb246IG51bGwsIGVycm9yOiBhcHBFcnJvci51c2VyTWVzc2FnZSB9O1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBDYW5jZWwgc3Vic2NyaXB0aW9uXG4gICAqL1xuICBhc3luYyBjYW5jZWxTdWJzY3JpcHRpb24oaW1tZWRpYXRlbHk6IGJvb2xlYW4gPSBmYWxzZSk6IFByb21pc2U8eyBzdWJzY3JpcHRpb246IFN0cmlwZVN1YnNjcmlwdGlvbiB8IG51bGw7IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCB0aGlzLm1ha2VBdXRoZW50aWNhdGVkUmVxdWVzdCgnL3N0cmlwZS9zdWJzY3JpcHRpb24vY2FuY2VsJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBpbW1lZGlhdGVseSB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGVycm9yID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICByZXR1cm4geyBzdWJzY3JpcHRpb246IG51bGwsIGVycm9yOiBlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gY2FuY2VsIHN1YnNjcmlwdGlvbicgfTtcbiAgICAgIH1cblxuICAgICAgY29uc3Qgc3Vic2NyaXB0aW9uID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIHsgc3Vic2NyaXB0aW9uIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnN0IGFwcEVycm9yID0gaGFuZGxlRXJyb3IoZXJyb3IsIHsgc2hvd0FsZXJ0OiBmYWxzZSB9KTtcbiAgICAgIHJldHVybiB7IHN1YnNjcmlwdGlvbjogbnVsbCwgZXJyb3I6IGFwcEVycm9yLnVzZXJNZXNzYWdlIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBiaWxsaW5nIGhpc3RvcnlcbiAgICovXG4gIGFzeW5jIGdldEludm9pY2VzKGxpbWl0OiBudW1iZXIgPSAxMCk6IFByb21pc2U8eyBpbnZvaWNlczogU3RyaXBlSW52b2ljZVtdOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5tYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoYC9zdHJpcGUvaW52b2ljZXM/bGltaXQ9JHtsaW1pdH1gKTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvciA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgcmV0dXJuIHsgaW52b2ljZXM6IFtdLCBlcnJvcjogZXJyb3IubWVzc2FnZSB8fCAnRmFpbGVkIHRvIGdldCBpbnZvaWNlcycgfTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHJldHVybiB7IGludm9pY2VzOiBkYXRhLmRhdGEgfHwgW10gfTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc3QgYXBwRXJyb3IgPSBoYW5kbGVFcnJvcihlcnJvciwgeyBzaG93QWxlcnQ6IGZhbHNlIH0pO1xuICAgICAgcmV0dXJuIHsgaW52b2ljZXM6IFtdLCBlcnJvcjogYXBwRXJyb3IudXNlck1lc3NhZ2UgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIHBheW1lbnQgaW50ZW50IGZvciBvbmUtdGltZSBwYXltZW50c1xuICAgKi9cbiAgYXN5bmMgY3JlYXRlUGF5bWVudEludGVudChhbW91bnQ6IG51bWJlciwgY3VycmVuY3k6IHN0cmluZyA9ICd1c2QnKTogUHJvbWlzZTx7IHBheW1lbnRJbnRlbnQ6IFBheW1lbnRJbnRlbnQgfCBudWxsOyBlcnJvcj86IHN0cmluZyB9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgdGhpcy5tYWtlQXV0aGVudGljYXRlZFJlcXVlc3QoJy9zdHJpcGUvcGF5bWVudC1pbnRlbnRzJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBhbW91bnQsIGN1cnJlbmN5IH0pLFxuICAgICAgfSk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZXJyb3IgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHJldHVybiB7IHBheW1lbnRJbnRlbnQ6IG51bGwsIGVycm9yOiBlcnJvci5tZXNzYWdlIHx8ICdGYWlsZWQgdG8gY3JlYXRlIHBheW1lbnQgaW50ZW50JyB9O1xuICAgICAgfVxuXG4gICAgICBjb25zdCBwYXltZW50SW50ZW50ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIHsgcGF5bWVudEludGVudCB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBhcHBFcnJvciA9IGhhbmRsZUVycm9yKGVycm9yLCB7IHNob3dBbGVydDogZmFsc2UgfSk7XG4gICAgICByZXR1cm4geyBwYXltZW50SW50ZW50OiBudWxsLCBlcnJvcjogYXBwRXJyb3IudXNlck1lc3NhZ2UgfTtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQ29uZmlybSBwYXltZW50IGludGVudFxuICAgKi9cbiAgYXN5bmMgY29uZmlybVBheW1lbnRJbnRlbnQoY2xpZW50U2VjcmV0OiBzdHJpbmcsIHBheW1lbnRNZXRob2RJZDogc3RyaW5nKTogUHJvbWlzZTx7IHN1Y2Nlc3M6IGJvb2xlYW47IGVycm9yPzogc3RyaW5nIH0+IHtcbiAgICB0cnkge1xuICAgICAgaWYgKCF0aGlzLnN0cmlwZSkge1xuICAgICAgICBhd2FpdCB0aGlzLmluaXRpYWxpemUoKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgeyBlcnJvciB9ID0gYXdhaXQgdGhpcy5zdHJpcGUuY29uZmlybUNhcmRQYXltZW50KGNsaWVudFNlY3JldCwge1xuICAgICAgICBwYXltZW50X21ldGhvZDogcGF5bWVudE1ldGhvZElkLFxuICAgICAgfSk7XG5cbiAgICAgIGlmIChlcnJvcikge1xuICAgICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zdCBhcHBFcnJvciA9IGhhbmRsZUVycm9yKGVycm9yLCB7IHNob3dBbGVydDogZmFsc2UgfSk7XG4gICAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3I6IGFwcEVycm9yLnVzZXJNZXNzYWdlIH07XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIE1ha2UgYXV0aGVudGljYXRlZCByZXF1ZXN0IHRvIGJhY2tlbmRcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgbWFrZUF1dGhlbnRpY2F0ZWRSZXF1ZXN0KGVuZHBvaW50OiBzdHJpbmcsIG9wdGlvbnM6IFJlcXVlc3RJbml0ID0ge30pOiBQcm9taXNlPFJlc3BvbnNlPiB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IGF1dGhTZXJ2aWNlLmdldEN1cnJlbnRTdGF0ZSgpLnNlc3Npb247XG4gICAgaWYgKCFzZXNzaW9uPy5hY2Nlc3NfdG9rZW4pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignVXNlciBub3QgYXV0aGVudGljYXRlZCcpO1xuICAgIH1cblxuICAgIHJldHVybiBmZXRjaChgJHt0aGlzLmFwaUJhc2VVcmx9JHtlbmRwb2ludH1gLCB7XG4gICAgICAuLi5vcHRpb25zLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAnQXV0aG9yaXphdGlvbic6IGBCZWFyZXIgJHtzZXNzaW9uLmFjY2Vzc190b2tlbn1gLFxuICAgICAgICAuLi5vcHRpb25zLmhlYWRlcnMsXG4gICAgICB9LFxuICAgIH0pO1xuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBzdHJpcGVTZXJ2aWNlID0gbmV3IFN0cmlwZVNlcnZpY2UoKTtcbmV4cG9ydCBkZWZhdWx0IHN0cmlwZVNlcnZpY2U7XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTUEsU0FBU0EsUUFBUSxRQUFRLGNBQWM7QUFDdkMsU0FBU0MsV0FBVztBQUNwQixTQUFTQyxXQUFXO0FBQ3BCLE9BQU9DLEdBQUc7QUFBNkIsSUEySGpDQyxhQUFhO0VBTWpCLFNBQUFBLGNBQUEsRUFBYztJQUFBQyxlQUFBLE9BQUFELGFBQUE7SUFBQSxLQUhORSxNQUFNLElBQUFDLGNBQUEsR0FBQUMsQ0FBQSxPQUFRLElBQUk7SUFBQSxLQUNsQkMsYUFBYSxJQUFBRixjQUFBLEdBQUFDLENBQUEsT0FBWSxLQUFLO0lBQUFELGNBQUEsR0FBQUcsQ0FBQTtJQUFBSCxjQUFBLEdBQUFDLENBQUE7SUFHcEMsSUFBSSxDQUFDRyxvQkFBb0IsR0FBRyxDQUFBSixjQUFBLEdBQUFLLENBQUEsVUFBQVQsR0FBRyxDQUFDVSxHQUFHLENBQUMsd0JBQXdCLENBQUMsTUFBQU4sY0FBQSxHQUFBSyxDQUFBLFVBQUksRUFBRTtJQUFDTCxjQUFBLEdBQUFDLENBQUE7SUFDcEUsSUFBSSxDQUFDTSxVQUFVLEdBQUcsQ0FBQVAsY0FBQSxHQUFBSyxDQUFBLFVBQUFULEdBQUcsQ0FBQ1UsR0FBRyxDQUFDLGNBQWMsQ0FBQyxNQUFBTixjQUFBLEdBQUFLLENBQUEsVUFBSSx5QkFBeUI7RUFDeEU7RUFBQyxPQUFBRyxZQUFBLENBQUFYLGFBQUE7SUFBQVksR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQUMsV0FBQSxHQUFBQyxpQkFBQSxDQUtELGFBQWtFO1FBQUFaLGNBQUEsR0FBQUcsQ0FBQTtRQUFBSCxjQUFBLEdBQUFDLENBQUE7UUFDaEUsSUFBSTtVQUFBRCxjQUFBLEdBQUFDLENBQUE7VUFDRixJQUFJLElBQUksQ0FBQ0MsYUFBYSxFQUFFO1lBQUFGLGNBQUEsR0FBQUssQ0FBQTtZQUFBTCxjQUFBLEdBQUFDLENBQUE7WUFDdEIsT0FBTztjQUFFWSxPQUFPLEVBQUU7WUFBSyxDQUFDO1VBQzFCLENBQUM7WUFBQWIsY0FBQSxHQUFBSyxDQUFBO1VBQUE7VUFBQUwsY0FBQSxHQUFBQyxDQUFBO1VBRUQsSUFBSSxDQUFDLElBQUksQ0FBQ0csb0JBQW9CLEVBQUU7WUFBQUosY0FBQSxHQUFBSyxDQUFBO1lBQUFMLGNBQUEsR0FBQUMsQ0FBQTtZQUM5QixPQUFPO2NBQUVZLE9BQU8sRUFBRSxLQUFLO2NBQUVDLEtBQUssRUFBRTtZQUF3QyxDQUFDO1VBQzNFLENBQUM7WUFBQWQsY0FBQSxHQUFBSyxDQUFBO1VBQUE7VUFBQUwsY0FBQSxHQUFBQyxDQUFBO1VBRUQsSUFBSVIsUUFBUSxDQUFDc0IsRUFBRSxLQUFLLEtBQUssRUFBRTtZQUFBZixjQUFBLEdBQUFLLENBQUE7WUFBQUwsY0FBQSxHQUFBQyxDQUFBO1lBRXpCLE1BQU0sSUFBSSxDQUFDZSxZQUFZLENBQUMsQ0FBQztZQUFDaEIsY0FBQSxHQUFBQyxDQUFBO1lBQzFCLElBQUksQ0FBQ0YsTUFBTSxHQUFHa0IsTUFBTSxDQUFDQyxNQUFNLENBQUMsSUFBSSxDQUFDZCxvQkFBb0IsQ0FBQztVQUN4RCxDQUFDLE1BQU07WUFBQUosY0FBQSxHQUFBSyxDQUFBO1lBQUFMLGNBQUEsR0FBQUMsQ0FBQTtZQUdMa0IsT0FBTyxDQUFDQyxHQUFHLENBQUMsbURBQW1ELENBQUM7VUFDbEU7VUFBQ3BCLGNBQUEsR0FBQUMsQ0FBQTtVQUVELElBQUksQ0FBQ0MsYUFBYSxHQUFHLElBQUk7VUFBQ0YsY0FBQSxHQUFBQyxDQUFBO1VBQzFCLE9BQU87WUFBRVksT0FBTyxFQUFFO1VBQUssQ0FBQztRQUMxQixDQUFDLENBQUMsT0FBT0MsS0FBSyxFQUFFO1VBQ2QsSUFBTU8sUUFBUSxJQUFBckIsY0FBQSxHQUFBQyxDQUFBLFFBQUdOLFdBQVcsQ0FBQ21CLEtBQUssRUFBRTtZQUFFUSxTQUFTLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ3RCLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUVZLE9BQU8sRUFBRSxLQUFLO1lBQUVDLEtBQUssRUFBRU8sUUFBUSxDQUFDRTtVQUFZLENBQUM7UUFDeEQ7TUFDRixDQUFDO01BQUEsU0ExQktDLFVBQVVBLENBQUE7UUFBQSxPQUFBYixXQUFBLENBQUFjLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBVkYsVUFBVTtJQUFBO0VBQUE7SUFBQWYsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWlCLGFBQUEsR0FBQWYsaUJBQUEsQ0ErQmhCLGFBQTRDO1FBQUFaLGNBQUEsR0FBQUcsQ0FBQTtRQUFBSCxjQUFBLEdBQUFDLENBQUE7UUFDMUMsT0FBTyxJQUFJMkIsT0FBTyxDQUFDLFVBQUNDLE9BQU8sRUFBRUMsTUFBTSxFQUFLO1VBQUE5QixjQUFBLEdBQUFHLENBQUE7VUFBQUgsY0FBQSxHQUFBQyxDQUFBO1VBQ3RDLElBQUksQ0FBQUQsY0FBQSxHQUFBSyxDQUFBLGlCQUFPWSxNQUFNLEtBQUssV0FBVyxNQUFBakIsY0FBQSxHQUFBSyxDQUFBLFVBQUlZLE1BQU0sQ0FBQ0MsTUFBTSxHQUFFO1lBQUFsQixjQUFBLEdBQUFLLENBQUE7WUFBQUwsY0FBQSxHQUFBQyxDQUFBO1lBQ2xENEIsT0FBTyxDQUFDLENBQUM7WUFBQzdCLGNBQUEsR0FBQUMsQ0FBQTtZQUNWO1VBQ0YsQ0FBQztZQUFBRCxjQUFBLEdBQUFLLENBQUE7VUFBQTtVQUVELElBQU0wQixNQUFNLElBQUEvQixjQUFBLEdBQUFDLENBQUEsUUFBRytCLFFBQVEsQ0FBQ0MsYUFBYSxDQUFDLFFBQVEsQ0FBQztVQUFDakMsY0FBQSxHQUFBQyxDQUFBO1VBQ2hEOEIsTUFBTSxDQUFDRyxHQUFHLEdBQUcsMkJBQTJCO1VBQUNsQyxjQUFBLEdBQUFDLENBQUE7VUFDekM4QixNQUFNLENBQUNJLE1BQU0sR0FBRyxZQUFNO1lBQUFuQyxjQUFBLEdBQUFHLENBQUE7WUFBQUgsY0FBQSxHQUFBQyxDQUFBO1lBQUEsT0FBQTRCLE9BQU8sQ0FBQyxDQUFDO1VBQUQsQ0FBQztVQUFDN0IsY0FBQSxHQUFBQyxDQUFBO1VBQ2hDOEIsTUFBTSxDQUFDSyxPQUFPLEdBQUcsWUFBTTtZQUFBcEMsY0FBQSxHQUFBRyxDQUFBO1lBQUFILGNBQUEsR0FBQUMsQ0FBQTtZQUFBLE9BQUE2QixNQUFNLENBQUMsSUFBSU8sS0FBSyxDQUFDLDBCQUEwQixDQUFDLENBQUM7VUFBRCxDQUFDO1VBQUNyQyxjQUFBLEdBQUFDLENBQUE7VUFDckUrQixRQUFRLENBQUNNLElBQUksQ0FBQ0MsV0FBVyxDQUFDUixNQUFNLENBQUM7UUFDbkMsQ0FBQyxDQUFDO01BQ0osQ0FBQztNQUFBLFNBYmFmLFlBQVlBLENBQUE7UUFBQSxPQUFBVyxhQUFBLENBQUFGLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBWlYsWUFBWTtJQUFBO0VBQUE7SUFBQVAsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQThCLGVBQUEsR0FBQTVCLGlCQUFBLENBa0IxQixXQUFxQjZCLFlBSXBCLEVBQWdFO1FBQUF6QyxjQUFBLEdBQUFHLENBQUE7UUFBQUgsY0FBQSxHQUFBQyxDQUFBO1FBQy9ELElBQUk7VUFDRixJQUFNeUMsUUFBUSxJQUFBMUMsY0FBQSxHQUFBQyxDQUFBLGNBQVMsSUFBSSxDQUFDMEMsd0JBQXdCLENBQUMsbUJBQW1CLEVBQUU7WUFDeEVDLE1BQU0sRUFBRSxNQUFNO1lBQ2RDLElBQUksRUFBRUMsSUFBSSxDQUFDQyxTQUFTLENBQUNOLFlBQVk7VUFDbkMsQ0FBQyxDQUFDO1VBQUN6QyxjQUFBLEdBQUFDLENBQUE7VUFFSCxJQUFJLENBQUN5QyxRQUFRLENBQUNNLEVBQUUsRUFBRTtZQUFBaEQsY0FBQSxHQUFBSyxDQUFBO1lBQ2hCLElBQU1TLEtBQUssSUFBQWQsY0FBQSxHQUFBQyxDQUFBLGNBQVN5QyxRQUFRLENBQUNPLElBQUksQ0FBQyxDQUFDO1lBQUNqRCxjQUFBLEdBQUFDLENBQUE7WUFDcEMsT0FBTztjQUFFaUQsUUFBUSxFQUFFLElBQUk7Y0FBRXBDLEtBQUssRUFBRSxDQUFBZCxjQUFBLEdBQUFLLENBQUEsVUFBQVMsS0FBSyxDQUFDcUMsT0FBTyxNQUFBbkQsY0FBQSxHQUFBSyxDQUFBLFVBQUksMkJBQTJCO1lBQUMsQ0FBQztVQUNoRixDQUFDO1lBQUFMLGNBQUEsR0FBQUssQ0FBQTtVQUFBO1VBRUQsSUFBTTZDLFFBQVEsSUFBQWxELGNBQUEsR0FBQUMsQ0FBQSxjQUFTeUMsUUFBUSxDQUFDTyxJQUFJLENBQUMsQ0FBQztVQUFDakQsY0FBQSxHQUFBQyxDQUFBO1VBQ3ZDLE9BQU87WUFBRWlELFFBQVEsRUFBUkE7VUFBUyxDQUFDO1FBQ3JCLENBQUMsQ0FBQyxPQUFPcEMsS0FBSyxFQUFFO1VBQ2QsSUFBTU8sUUFBUSxJQUFBckIsY0FBQSxHQUFBQyxDQUFBLFFBQUdOLFdBQVcsQ0FBQ21CLEtBQUssRUFBRTtZQUFFUSxTQUFTLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ3RCLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUVpRCxRQUFRLEVBQUUsSUFBSTtZQUFFcEMsS0FBSyxFQUFFTyxRQUFRLENBQUNFO1VBQVksQ0FBQztRQUN4RDtNQUNGLENBQUM7TUFBQSxTQXRCSzZCLGNBQWNBLENBQUFDLEVBQUE7UUFBQSxPQUFBYixlQUFBLENBQUFmLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBZDBCLGNBQWM7SUFBQTtFQUFBO0lBQUEzQyxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBNEMsWUFBQSxHQUFBMUMsaUJBQUEsQ0EyQnBCLGFBQWtGO1FBQUFaLGNBQUEsR0FBQUcsQ0FBQTtRQUFBSCxjQUFBLEdBQUFDLENBQUE7UUFDaEYsSUFBSTtVQUNGLElBQU15QyxRQUFRLElBQUExQyxjQUFBLEdBQUFDLENBQUEsY0FBUyxJQUFJLENBQUMwQyx3QkFBd0IsQ0FBQyxrQkFBa0IsQ0FBQztVQUFDM0MsY0FBQSxHQUFBQyxDQUFBO1VBRXpFLElBQUksQ0FBQ3lDLFFBQVEsQ0FBQ00sRUFBRSxFQUFFO1lBQUFoRCxjQUFBLEdBQUFLLENBQUE7WUFDaEIsSUFBTVMsS0FBSyxJQUFBZCxjQUFBLEdBQUFDLENBQUEsY0FBU3lDLFFBQVEsQ0FBQ08sSUFBSSxDQUFDLENBQUM7WUFBQ2pELGNBQUEsR0FBQUMsQ0FBQTtZQUNwQyxPQUFPO2NBQUVpRCxRQUFRLEVBQUUsSUFBSTtjQUFFcEMsS0FBSyxFQUFFLENBQUFkLGNBQUEsR0FBQUssQ0FBQSxXQUFBUyxLQUFLLENBQUNxQyxPQUFPLE1BQUFuRCxjQUFBLEdBQUFLLENBQUEsV0FBSSx3QkFBd0I7WUFBQyxDQUFDO1VBQzdFLENBQUM7WUFBQUwsY0FBQSxHQUFBSyxDQUFBO1VBQUE7VUFFRCxJQUFNNkMsUUFBUSxJQUFBbEQsY0FBQSxHQUFBQyxDQUFBLGNBQVN5QyxRQUFRLENBQUNPLElBQUksQ0FBQyxDQUFDO1VBQUNqRCxjQUFBLEdBQUFDLENBQUE7VUFDdkMsT0FBTztZQUFFaUQsUUFBUSxFQUFSQTtVQUFTLENBQUM7UUFDckIsQ0FBQyxDQUFDLE9BQU9wQyxLQUFLLEVBQUU7VUFDZCxJQUFNTyxRQUFRLElBQUFyQixjQUFBLEdBQUFDLENBQUEsUUFBR04sV0FBVyxDQUFDbUIsS0FBSyxFQUFFO1lBQUVRLFNBQVMsRUFBRTtVQUFNLENBQUMsQ0FBQztVQUFDdEIsY0FBQSxHQUFBQyxDQUFBO1VBQzFELE9BQU87WUFBRWlELFFBQVEsRUFBRSxJQUFJO1lBQUVwQyxLQUFLLEVBQUVPLFFBQVEsQ0FBQ0U7VUFBWSxDQUFDO1FBQ3hEO01BQ0YsQ0FBQztNQUFBLFNBZktnQyxXQUFXQSxDQUFBO1FBQUEsT0FBQUQsWUFBQSxDQUFBN0IsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFYNkIsV0FBVztJQUFBO0VBQUE7SUFBQTlDLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE4QyxvQkFBQSxHQUFBNUMsaUJBQUEsQ0FvQmpCLFdBQTBCNkMsV0FxQnpCLEVBQTBFO1FBQUF6RCxjQUFBLEdBQUFHLENBQUE7UUFBQUgsY0FBQSxHQUFBQyxDQUFBO1FBQ3pFLElBQUk7VUFBQUQsY0FBQSxHQUFBQyxDQUFBO1VBQ0YsSUFBSSxDQUFDLElBQUksQ0FBQ0YsTUFBTSxFQUFFO1lBQUFDLGNBQUEsR0FBQUssQ0FBQTtZQUFBTCxjQUFBLEdBQUFDLENBQUE7WUFDaEIsTUFBTSxJQUFJLENBQUN1QixVQUFVLENBQUMsQ0FBQztVQUN6QixDQUFDO1lBQUF4QixjQUFBLEdBQUFLLENBQUE7VUFBQTtVQUVELElBQUFxRCxJQUFBLElBQUExRCxjQUFBLEdBQUFDLENBQUEsY0FBdUMsSUFBSSxDQUFDRixNQUFNLENBQUM0RCxtQkFBbUIsQ0FBQ0YsV0FBVyxDQUFDO1lBQTNFRyxhQUFhLEdBQUFGLElBQUEsQ0FBYkUsYUFBYTtZQUFFOUMsS0FBSyxHQUFBNEMsSUFBQSxDQUFMNUMsS0FBSztVQUF3RGQsY0FBQSxHQUFBQyxDQUFBO1VBRXBGLElBQUlhLEtBQUssRUFBRTtZQUFBZCxjQUFBLEdBQUFLLENBQUE7WUFBQUwsY0FBQSxHQUFBQyxDQUFBO1lBQ1QsT0FBTztjQUFFMkQsYUFBYSxFQUFFLElBQUk7Y0FBRTlDLEtBQUssRUFBRUEsS0FBSyxDQUFDcUM7WUFBUSxDQUFDO1VBQ3RELENBQUM7WUFBQW5ELGNBQUEsR0FBQUssQ0FBQTtVQUFBO1VBQUFMLGNBQUEsR0FBQUMsQ0FBQTtVQUVELE9BQU87WUFBRTJELGFBQWEsRUFBYkE7VUFBYyxDQUFDO1FBQzFCLENBQUMsQ0FBQyxPQUFPOUMsS0FBSyxFQUFFO1VBQ2QsSUFBTU8sUUFBUSxJQUFBckIsY0FBQSxHQUFBQyxDQUFBLFFBQUdOLFdBQVcsQ0FBQ21CLEtBQUssRUFBRTtZQUFFUSxTQUFTLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ3RCLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUUyRCxhQUFhLEVBQUUsSUFBSTtZQUFFOUMsS0FBSyxFQUFFTyxRQUFRLENBQUNFO1VBQVksQ0FBQztRQUM3RDtNQUNGLENBQUM7TUFBQSxTQXRDS29DLG1CQUFtQkEsQ0FBQUUsR0FBQTtRQUFBLE9BQUFMLG9CQUFBLENBQUEvQixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQW5CaUMsbUJBQW1CO0lBQUE7RUFBQTtJQUFBbEQsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQW9ELG9CQUFBLEdBQUFsRCxpQkFBQSxDQTJDekIsV0FBMEJtRCxlQUF1QixFQUFpRDtRQUFBL0QsY0FBQSxHQUFBRyxDQUFBO1FBQUFILGNBQUEsR0FBQUMsQ0FBQTtRQUNoRyxJQUFJO1VBQ0YsSUFBTXlDLFFBQVEsSUFBQTFDLGNBQUEsR0FBQUMsQ0FBQSxjQUFTLElBQUksQ0FBQzBDLHdCQUF3QixDQUFDLGdDQUFnQyxFQUFFO1lBQ3JGQyxNQUFNLEVBQUUsTUFBTTtZQUNkQyxJQUFJLEVBQUVDLElBQUksQ0FBQ0MsU0FBUyxDQUFDO2NBQUVpQixpQkFBaUIsRUFBRUQ7WUFBZ0IsQ0FBQztVQUM3RCxDQUFDLENBQUM7VUFBQy9ELGNBQUEsR0FBQUMsQ0FBQTtVQUVILElBQUksQ0FBQ3lDLFFBQVEsQ0FBQ00sRUFBRSxFQUFFO1lBQUFoRCxjQUFBLEdBQUFLLENBQUE7WUFDaEIsSUFBTVMsS0FBSyxJQUFBZCxjQUFBLEdBQUFDLENBQUEsY0FBU3lDLFFBQVEsQ0FBQ08sSUFBSSxDQUFDLENBQUM7WUFBQ2pELGNBQUEsR0FBQUMsQ0FBQTtZQUNwQyxPQUFPO2NBQUVZLE9BQU8sRUFBRSxLQUFLO2NBQUVDLEtBQUssRUFBRSxDQUFBZCxjQUFBLEdBQUFLLENBQUEsV0FBQVMsS0FBSyxDQUFDcUMsT0FBTyxNQUFBbkQsY0FBQSxHQUFBSyxDQUFBLFdBQUksaUNBQWlDO1lBQUMsQ0FBQztVQUN0RixDQUFDO1lBQUFMLGNBQUEsR0FBQUssQ0FBQTtVQUFBO1VBQUFMLGNBQUEsR0FBQUMsQ0FBQTtVQUVELE9BQU87WUFBRVksT0FBTyxFQUFFO1VBQUssQ0FBQztRQUMxQixDQUFDLENBQUMsT0FBT0MsS0FBSyxFQUFFO1VBQ2QsSUFBTU8sUUFBUSxJQUFBckIsY0FBQSxHQUFBQyxDQUFBLFFBQUdOLFdBQVcsQ0FBQ21CLEtBQUssRUFBRTtZQUFFUSxTQUFTLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ3RCLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUVZLE9BQU8sRUFBRSxLQUFLO1lBQUVDLEtBQUssRUFBRU8sUUFBUSxDQUFDRTtVQUFZLENBQUM7UUFDeEQ7TUFDRixDQUFDO01BQUEsU0FqQkswQyxtQkFBbUJBLENBQUFDLEdBQUE7UUFBQSxPQUFBSixvQkFBQSxDQUFBckMsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFuQnVDLG1CQUFtQjtJQUFBO0VBQUE7SUFBQXhELEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUF5RCxrQkFBQSxHQUFBdkQsaUJBQUEsQ0FzQnpCLGFBQThGO1FBQUFaLGNBQUEsR0FBQUcsQ0FBQTtRQUFBSCxjQUFBLEdBQUFDLENBQUE7UUFDNUYsSUFBSTtVQUNGLElBQU15QyxRQUFRLElBQUExQyxjQUFBLEdBQUFDLENBQUEsY0FBUyxJQUFJLENBQUMwQyx3QkFBd0IsQ0FBQyx5QkFBeUIsQ0FBQztVQUFDM0MsY0FBQSxHQUFBQyxDQUFBO1VBRWhGLElBQUksQ0FBQ3lDLFFBQVEsQ0FBQ00sRUFBRSxFQUFFO1lBQUFoRCxjQUFBLEdBQUFLLENBQUE7WUFDaEIsSUFBTVMsS0FBSyxJQUFBZCxjQUFBLEdBQUFDLENBQUEsY0FBU3lDLFFBQVEsQ0FBQ08sSUFBSSxDQUFDLENBQUM7WUFBQ2pELGNBQUEsR0FBQUMsQ0FBQTtZQUNwQyxPQUFPO2NBQUVtRSxjQUFjLEVBQUUsRUFBRTtjQUFFdEQsS0FBSyxFQUFFLENBQUFkLGNBQUEsR0FBQUssQ0FBQSxXQUFBUyxLQUFLLENBQUNxQyxPQUFPLE1BQUFuRCxjQUFBLEdBQUFLLENBQUEsV0FBSSwrQkFBK0I7WUFBQyxDQUFDO1VBQ3hGLENBQUM7WUFBQUwsY0FBQSxHQUFBSyxDQUFBO1VBQUE7VUFFRCxJQUFNZ0UsSUFBSSxJQUFBckUsY0FBQSxHQUFBQyxDQUFBLGNBQVN5QyxRQUFRLENBQUNPLElBQUksQ0FBQyxDQUFDO1VBQUNqRCxjQUFBLEdBQUFDLENBQUE7VUFDbkMsT0FBTztZQUFFbUUsY0FBYyxFQUFFLENBQUFwRSxjQUFBLEdBQUFLLENBQUEsV0FBQWdFLElBQUksQ0FBQ0EsSUFBSSxNQUFBckUsY0FBQSxHQUFBSyxDQUFBLFdBQUksRUFBRTtVQUFDLENBQUM7UUFDNUMsQ0FBQyxDQUFDLE9BQU9TLEtBQUssRUFBRTtVQUNkLElBQU1PLFFBQVEsSUFBQXJCLGNBQUEsR0FBQUMsQ0FBQSxRQUFHTixXQUFXLENBQUNtQixLQUFLLEVBQUU7WUFBRVEsU0FBUyxFQUFFO1VBQU0sQ0FBQyxDQUFDO1VBQUN0QixjQUFBLEdBQUFDLENBQUE7VUFDMUQsT0FBTztZQUFFbUUsY0FBYyxFQUFFLEVBQUU7WUFBRXRELEtBQUssRUFBRU8sUUFBUSxDQUFDRTtVQUFZLENBQUM7UUFDNUQ7TUFDRixDQUFDO01BQUEsU0FmSytDLGlCQUFpQkEsQ0FBQTtRQUFBLE9BQUFILGtCQUFBLENBQUExQyxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWpCNEMsaUJBQWlCO0lBQUE7RUFBQTtJQUFBN0QsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQTZELG1CQUFBLEdBQUEzRCxpQkFBQSxDQW9CdkIsV0FBeUI0RCxPQUFlLEVBQUVULGVBQXdCLEVBQXdFO1FBQUEvRCxjQUFBLEdBQUFHLENBQUE7UUFBQUgsY0FBQSxHQUFBQyxDQUFBO1FBQ3hJLElBQUk7VUFDRixJQUFNeUMsUUFBUSxJQUFBMUMsY0FBQSxHQUFBQyxDQUFBLGNBQVMsSUFBSSxDQUFDMEMsd0JBQXdCLENBQUMsdUJBQXVCLEVBQUU7WUFDNUVDLE1BQU0sRUFBRSxNQUFNO1lBQ2RDLElBQUksRUFBRUMsSUFBSSxDQUFDQyxTQUFTLENBQUM7Y0FDbkIwQixRQUFRLEVBQUVELE9BQU87Y0FDakJSLGlCQUFpQixFQUFFRDtZQUNyQixDQUFDO1VBQ0gsQ0FBQyxDQUFDO1VBQUMvRCxjQUFBLEdBQUFDLENBQUE7VUFFSCxJQUFJLENBQUN5QyxRQUFRLENBQUNNLEVBQUUsRUFBRTtZQUFBaEQsY0FBQSxHQUFBSyxDQUFBO1lBQ2hCLElBQU1TLEtBQUssSUFBQWQsY0FBQSxHQUFBQyxDQUFBLGNBQVN5QyxRQUFRLENBQUNPLElBQUksQ0FBQyxDQUFDO1lBQUNqRCxjQUFBLEdBQUFDLENBQUE7WUFDcEMsT0FBTztjQUFFeUUsWUFBWSxFQUFFLElBQUk7Y0FBRTVELEtBQUssRUFBRSxDQUFBZCxjQUFBLEdBQUFLLENBQUEsV0FBQVMsS0FBSyxDQUFDcUMsT0FBTyxNQUFBbkQsY0FBQSxHQUFBSyxDQUFBLFdBQUksK0JBQStCO1lBQUMsQ0FBQztVQUN4RixDQUFDO1lBQUFMLGNBQUEsR0FBQUssQ0FBQTtVQUFBO1VBRUQsSUFBTXFFLFlBQVksSUFBQTFFLGNBQUEsR0FBQUMsQ0FBQSxjQUFTeUMsUUFBUSxDQUFDTyxJQUFJLENBQUMsQ0FBQztVQUFDakQsY0FBQSxHQUFBQyxDQUFBO1VBQzNDLE9BQU87WUFBRXlFLFlBQVksRUFBWkE7VUFBYSxDQUFDO1FBQ3pCLENBQUMsQ0FBQyxPQUFPNUQsS0FBSyxFQUFFO1VBQ2QsSUFBTU8sUUFBUSxJQUFBckIsY0FBQSxHQUFBQyxDQUFBLFFBQUdOLFdBQVcsQ0FBQ21CLEtBQUssRUFBRTtZQUFFUSxTQUFTLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ3RCLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUV5RSxZQUFZLEVBQUUsSUFBSTtZQUFFNUQsS0FBSyxFQUFFTyxRQUFRLENBQUNFO1VBQVksQ0FBQztRQUM1RDtNQUNGLENBQUM7TUFBQSxTQXJCS29ELGtCQUFrQkEsQ0FBQUMsR0FBQSxFQUFBQyxHQUFBO1FBQUEsT0FBQU4sbUJBQUEsQ0FBQTlDLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBbEJpRCxrQkFBa0I7SUFBQTtFQUFBO0lBQUFsRSxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBb0UsZ0JBQUEsR0FBQWxFLGlCQUFBLENBMEJ4QixhQUE4RjtRQUFBWixjQUFBLEdBQUFHLENBQUE7UUFBQUgsY0FBQSxHQUFBQyxDQUFBO1FBQzVGLElBQUk7VUFDRixJQUFNeUMsUUFBUSxJQUFBMUMsY0FBQSxHQUFBQyxDQUFBLGNBQVMsSUFBSSxDQUFDMEMsd0JBQXdCLENBQUMsc0JBQXNCLENBQUM7VUFBQzNDLGNBQUEsR0FBQUMsQ0FBQTtVQUU3RSxJQUFJLENBQUN5QyxRQUFRLENBQUNNLEVBQUUsRUFBRTtZQUFBaEQsY0FBQSxHQUFBSyxDQUFBO1lBQUFMLGNBQUEsR0FBQUMsQ0FBQTtZQUNoQixJQUFJeUMsUUFBUSxDQUFDcUMsTUFBTSxLQUFLLEdBQUcsRUFBRTtjQUFBL0UsY0FBQSxHQUFBSyxDQUFBO2NBQUFMLGNBQUEsR0FBQUMsQ0FBQTtjQUMzQixPQUFPO2dCQUFFeUUsWUFBWSxFQUFFO2NBQUssQ0FBQztZQUMvQixDQUFDO2NBQUExRSxjQUFBLEdBQUFLLENBQUE7WUFBQTtZQUNELElBQU1TLEtBQUssSUFBQWQsY0FBQSxHQUFBQyxDQUFBLGNBQVN5QyxRQUFRLENBQUNPLElBQUksQ0FBQyxDQUFDO1lBQUNqRCxjQUFBLEdBQUFDLENBQUE7WUFDcEMsT0FBTztjQUFFeUUsWUFBWSxFQUFFLElBQUk7Y0FBRTVELEtBQUssRUFBRSxDQUFBZCxjQUFBLEdBQUFLLENBQUEsV0FBQVMsS0FBSyxDQUFDcUMsT0FBTyxNQUFBbkQsY0FBQSxHQUFBSyxDQUFBLFdBQUksNEJBQTRCO1lBQUMsQ0FBQztVQUNyRixDQUFDO1lBQUFMLGNBQUEsR0FBQUssQ0FBQTtVQUFBO1VBRUQsSUFBTXFFLFlBQVksSUFBQTFFLGNBQUEsR0FBQUMsQ0FBQSxjQUFTeUMsUUFBUSxDQUFDTyxJQUFJLENBQUMsQ0FBQztVQUFDakQsY0FBQSxHQUFBQyxDQUFBO1VBQzNDLE9BQU87WUFBRXlFLFlBQVksRUFBWkE7VUFBYSxDQUFDO1FBQ3pCLENBQUMsQ0FBQyxPQUFPNUQsS0FBSyxFQUFFO1VBQ2QsSUFBTU8sUUFBUSxJQUFBckIsY0FBQSxHQUFBQyxDQUFBLFFBQUdOLFdBQVcsQ0FBQ21CLEtBQUssRUFBRTtZQUFFUSxTQUFTLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ3RCLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUV5RSxZQUFZLEVBQUUsSUFBSTtZQUFFNUQsS0FBSyxFQUFFTyxRQUFRLENBQUNFO1VBQVksQ0FBQztRQUM1RDtNQUNGLENBQUM7TUFBQSxTQWxCS3lELGVBQWVBLENBQUE7UUFBQSxPQUFBRixnQkFBQSxDQUFBckQsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFmc0QsZUFBZTtJQUFBO0VBQUE7SUFBQXZFLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUF1RSxtQkFBQSxHQUFBckUsaUJBQUEsQ0F1QnJCLGFBQTZIO1FBQUEsSUFBcEdzRSxXQUFvQixHQUFBeEQsU0FBQSxDQUFBeUQsTUFBQSxRQUFBekQsU0FBQSxRQUFBMEQsU0FBQSxHQUFBMUQsU0FBQSxPQUFBMUIsY0FBQSxHQUFBSyxDQUFBLFdBQUcsS0FBSztRQUFBTCxjQUFBLEdBQUFHLENBQUE7UUFBQUgsY0FBQSxHQUFBQyxDQUFBO1FBQ25ELElBQUk7VUFDRixJQUFNeUMsUUFBUSxJQUFBMUMsY0FBQSxHQUFBQyxDQUFBLGNBQVMsSUFBSSxDQUFDMEMsd0JBQXdCLENBQUMsNkJBQTZCLEVBQUU7WUFDbEZDLE1BQU0sRUFBRSxNQUFNO1lBQ2RDLElBQUksRUFBRUMsSUFBSSxDQUFDQyxTQUFTLENBQUM7Y0FBRW1DLFdBQVcsRUFBWEE7WUFBWSxDQUFDO1VBQ3RDLENBQUMsQ0FBQztVQUFDbEYsY0FBQSxHQUFBQyxDQUFBO1VBRUgsSUFBSSxDQUFDeUMsUUFBUSxDQUFDTSxFQUFFLEVBQUU7WUFBQWhELGNBQUEsR0FBQUssQ0FBQTtZQUNoQixJQUFNUyxLQUFLLElBQUFkLGNBQUEsR0FBQUMsQ0FBQSxjQUFTeUMsUUFBUSxDQUFDTyxJQUFJLENBQUMsQ0FBQztZQUFDakQsY0FBQSxHQUFBQyxDQUFBO1lBQ3BDLE9BQU87Y0FBRXlFLFlBQVksRUFBRSxJQUFJO2NBQUU1RCxLQUFLLEVBQUUsQ0FBQWQsY0FBQSxHQUFBSyxDQUFBLFdBQUFTLEtBQUssQ0FBQ3FDLE9BQU8sTUFBQW5ELGNBQUEsR0FBQUssQ0FBQSxXQUFJLCtCQUErQjtZQUFDLENBQUM7VUFDeEYsQ0FBQztZQUFBTCxjQUFBLEdBQUFLLENBQUE7VUFBQTtVQUVELElBQU1xRSxZQUFZLElBQUExRSxjQUFBLEdBQUFDLENBQUEsY0FBU3lDLFFBQVEsQ0FBQ08sSUFBSSxDQUFDLENBQUM7VUFBQ2pELGNBQUEsR0FBQUMsQ0FBQTtVQUMzQyxPQUFPO1lBQUV5RSxZQUFZLEVBQVpBO1VBQWEsQ0FBQztRQUN6QixDQUFDLENBQUMsT0FBTzVELEtBQUssRUFBRTtVQUNkLElBQU1PLFFBQVEsSUFBQXJCLGNBQUEsR0FBQUMsQ0FBQSxRQUFHTixXQUFXLENBQUNtQixLQUFLLEVBQUU7WUFBRVEsU0FBUyxFQUFFO1VBQU0sQ0FBQyxDQUFDO1VBQUN0QixjQUFBLEdBQUFDLENBQUE7VUFDMUQsT0FBTztZQUFFeUUsWUFBWSxFQUFFLElBQUk7WUFBRTVELEtBQUssRUFBRU8sUUFBUSxDQUFDRTtVQUFZLENBQUM7UUFDNUQ7TUFDRixDQUFDO01BQUEsU0FsQks4RCxrQkFBa0JBLENBQUE7UUFBQSxPQUFBSixtQkFBQSxDQUFBeEQsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFsQjJELGtCQUFrQjtJQUFBO0VBQUE7SUFBQTVFLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUE0RSxZQUFBLEdBQUExRSxpQkFBQSxDQXVCeEIsYUFBOEY7UUFBQSxJQUE1RTJFLEtBQWEsR0FBQTdELFNBQUEsQ0FBQXlELE1BQUEsUUFBQXpELFNBQUEsUUFBQTBELFNBQUEsR0FBQTFELFNBQUEsT0FBQTFCLGNBQUEsR0FBQUssQ0FBQSxXQUFHLEVBQUU7UUFBQUwsY0FBQSxHQUFBRyxDQUFBO1FBQUFILGNBQUEsR0FBQUMsQ0FBQTtRQUNsQyxJQUFJO1VBQ0YsSUFBTXlDLFFBQVEsSUFBQTFDLGNBQUEsR0FBQUMsQ0FBQSxlQUFTLElBQUksQ0FBQzBDLHdCQUF3QixDQUFDLDBCQUEwQjRDLEtBQUssRUFBRSxDQUFDO1VBQUN2RixjQUFBLEdBQUFDLENBQUE7VUFFeEYsSUFBSSxDQUFDeUMsUUFBUSxDQUFDTSxFQUFFLEVBQUU7WUFBQWhELGNBQUEsR0FBQUssQ0FBQTtZQUNoQixJQUFNUyxLQUFLLElBQUFkLGNBQUEsR0FBQUMsQ0FBQSxlQUFTeUMsUUFBUSxDQUFDTyxJQUFJLENBQUMsQ0FBQztZQUFDakQsY0FBQSxHQUFBQyxDQUFBO1lBQ3BDLE9BQU87Y0FBRXVGLFFBQVEsRUFBRSxFQUFFO2NBQUUxRSxLQUFLLEVBQUUsQ0FBQWQsY0FBQSxHQUFBSyxDQUFBLFdBQUFTLEtBQUssQ0FBQ3FDLE9BQU8sTUFBQW5ELGNBQUEsR0FBQUssQ0FBQSxXQUFJLHdCQUF3QjtZQUFDLENBQUM7VUFDM0UsQ0FBQztZQUFBTCxjQUFBLEdBQUFLLENBQUE7VUFBQTtVQUVELElBQU1nRSxJQUFJLElBQUFyRSxjQUFBLEdBQUFDLENBQUEsZUFBU3lDLFFBQVEsQ0FBQ08sSUFBSSxDQUFDLENBQUM7VUFBQ2pELGNBQUEsR0FBQUMsQ0FBQTtVQUNuQyxPQUFPO1lBQUV1RixRQUFRLEVBQUUsQ0FBQXhGLGNBQUEsR0FBQUssQ0FBQSxXQUFBZ0UsSUFBSSxDQUFDQSxJQUFJLE1BQUFyRSxjQUFBLEdBQUFLLENBQUEsV0FBSSxFQUFFO1VBQUMsQ0FBQztRQUN0QyxDQUFDLENBQUMsT0FBT1MsS0FBSyxFQUFFO1VBQ2QsSUFBTU8sUUFBUSxJQUFBckIsY0FBQSxHQUFBQyxDQUFBLFNBQUdOLFdBQVcsQ0FBQ21CLEtBQUssRUFBRTtZQUFFUSxTQUFTLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ3RCLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUV1RixRQUFRLEVBQUUsRUFBRTtZQUFFMUUsS0FBSyxFQUFFTyxRQUFRLENBQUNFO1VBQVksQ0FBQztRQUN0RDtNQUNGLENBQUM7TUFBQSxTQWZLa0UsV0FBV0EsQ0FBQTtRQUFBLE9BQUFILFlBQUEsQ0FBQTdELEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBWCtELFdBQVc7SUFBQTtFQUFBO0lBQUFoRixHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBZ0Ysb0JBQUEsR0FBQTlFLGlCQUFBLENBb0JqQixXQUEwQitFLE1BQWMsRUFBOEY7UUFBQSxJQUE1RkMsUUFBZ0IsR0FBQWxFLFNBQUEsQ0FBQXlELE1BQUEsUUFBQXpELFNBQUEsUUFBQTBELFNBQUEsR0FBQTFELFNBQUEsT0FBQTFCLGNBQUEsR0FBQUssQ0FBQSxXQUFHLEtBQUs7UUFBQUwsY0FBQSxHQUFBRyxDQUFBO1FBQUFILGNBQUEsR0FBQUMsQ0FBQTtRQUNoRSxJQUFJO1VBQ0YsSUFBTXlDLFFBQVEsSUFBQTFDLGNBQUEsR0FBQUMsQ0FBQSxlQUFTLElBQUksQ0FBQzBDLHdCQUF3QixDQUFDLHlCQUF5QixFQUFFO1lBQzlFQyxNQUFNLEVBQUUsTUFBTTtZQUNkQyxJQUFJLEVBQUVDLElBQUksQ0FBQ0MsU0FBUyxDQUFDO2NBQUU0QyxNQUFNLEVBQU5BLE1BQU07Y0FBRUMsUUFBUSxFQUFSQTtZQUFTLENBQUM7VUFDM0MsQ0FBQyxDQUFDO1VBQUM1RixjQUFBLEdBQUFDLENBQUE7VUFFSCxJQUFJLENBQUN5QyxRQUFRLENBQUNNLEVBQUUsRUFBRTtZQUFBaEQsY0FBQSxHQUFBSyxDQUFBO1lBQ2hCLElBQU1TLEtBQUssSUFBQWQsY0FBQSxHQUFBQyxDQUFBLGVBQVN5QyxRQUFRLENBQUNPLElBQUksQ0FBQyxDQUFDO1lBQUNqRCxjQUFBLEdBQUFDLENBQUE7WUFDcEMsT0FBTztjQUFFNEYsYUFBYSxFQUFFLElBQUk7Y0FBRS9FLEtBQUssRUFBRSxDQUFBZCxjQUFBLEdBQUFLLENBQUEsV0FBQVMsS0FBSyxDQUFDcUMsT0FBTyxNQUFBbkQsY0FBQSxHQUFBSyxDQUFBLFdBQUksaUNBQWlDO1lBQUMsQ0FBQztVQUMzRixDQUFDO1lBQUFMLGNBQUEsR0FBQUssQ0FBQTtVQUFBO1VBRUQsSUFBTXdGLGFBQWEsSUFBQTdGLGNBQUEsR0FBQUMsQ0FBQSxlQUFTeUMsUUFBUSxDQUFDTyxJQUFJLENBQUMsQ0FBQztVQUFDakQsY0FBQSxHQUFBQyxDQUFBO1VBQzVDLE9BQU87WUFBRTRGLGFBQWEsRUFBYkE7VUFBYyxDQUFDO1FBQzFCLENBQUMsQ0FBQyxPQUFPL0UsS0FBSyxFQUFFO1VBQ2QsSUFBTU8sUUFBUSxJQUFBckIsY0FBQSxHQUFBQyxDQUFBLFNBQUdOLFdBQVcsQ0FBQ21CLEtBQUssRUFBRTtZQUFFUSxTQUFTLEVBQUU7VUFBTSxDQUFDLENBQUM7VUFBQ3RCLGNBQUEsR0FBQUMsQ0FBQTtVQUMxRCxPQUFPO1lBQUU0RixhQUFhLEVBQUUsSUFBSTtZQUFFL0UsS0FBSyxFQUFFTyxRQUFRLENBQUNFO1VBQVksQ0FBQztRQUM3RDtNQUNGLENBQUM7TUFBQSxTQWxCS3VFLG1CQUFtQkEsQ0FBQUMsR0FBQTtRQUFBLE9BQUFMLG9CQUFBLENBQUFqRSxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQW5Cb0UsbUJBQW1CO0lBQUE7RUFBQTtJQUFBckYsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQXNGLHFCQUFBLEdBQUFwRixpQkFBQSxDQXVCekIsV0FBMkJxRixZQUFvQixFQUFFbEMsZUFBdUIsRUFBaUQ7UUFBQS9ELGNBQUEsR0FBQUcsQ0FBQTtRQUFBSCxjQUFBLEdBQUFDLENBQUE7UUFDdkgsSUFBSTtVQUFBRCxjQUFBLEdBQUFDLENBQUE7VUFDRixJQUFJLENBQUMsSUFBSSxDQUFDRixNQUFNLEVBQUU7WUFBQUMsY0FBQSxHQUFBSyxDQUFBO1lBQUFMLGNBQUEsR0FBQUMsQ0FBQTtZQUNoQixNQUFNLElBQUksQ0FBQ3VCLFVBQVUsQ0FBQyxDQUFDO1VBQ3pCLENBQUM7WUFBQXhCLGNBQUEsR0FBQUssQ0FBQTtVQUFBO1VBRUQsSUFBQTZGLEtBQUEsSUFBQWxHLGNBQUEsR0FBQUMsQ0FBQSxlQUF3QixJQUFJLENBQUNGLE1BQU0sQ0FBQ29HLGtCQUFrQixDQUFDRixZQUFZLEVBQUU7Y0FDbkVHLGNBQWMsRUFBRXJDO1lBQ2xCLENBQUMsQ0FBQztZQUZNakQsS0FBSyxHQUFBb0YsS0FBQSxDQUFMcEYsS0FBSztVQUVWZCxjQUFBLEdBQUFDLENBQUE7VUFFSCxJQUFJYSxLQUFLLEVBQUU7WUFBQWQsY0FBQSxHQUFBSyxDQUFBO1lBQUFMLGNBQUEsR0FBQUMsQ0FBQTtZQUNULE9BQU87Y0FBRVksT0FBTyxFQUFFLEtBQUs7Y0FBRUMsS0FBSyxFQUFFQSxLQUFLLENBQUNxQztZQUFRLENBQUM7VUFDakQsQ0FBQztZQUFBbkQsY0FBQSxHQUFBSyxDQUFBO1VBQUE7VUFBQUwsY0FBQSxHQUFBQyxDQUFBO1VBRUQsT0FBTztZQUFFWSxPQUFPLEVBQUU7VUFBSyxDQUFDO1FBQzFCLENBQUMsQ0FBQyxPQUFPQyxLQUFLLEVBQUU7VUFDZCxJQUFNTyxRQUFRLElBQUFyQixjQUFBLEdBQUFDLENBQUEsU0FBR04sV0FBVyxDQUFDbUIsS0FBSyxFQUFFO1lBQUVRLFNBQVMsRUFBRTtVQUFNLENBQUMsQ0FBQztVQUFDdEIsY0FBQSxHQUFBQyxDQUFBO1VBQzFELE9BQU87WUFBRVksT0FBTyxFQUFFLEtBQUs7WUFBRUMsS0FBSyxFQUFFTyxRQUFRLENBQUNFO1VBQVksQ0FBQztRQUN4RDtNQUNGLENBQUM7TUFBQSxTQW5CSzhFLG9CQUFvQkEsQ0FBQUMsR0FBQSxFQUFBQyxHQUFBO1FBQUEsT0FBQVAscUJBQUEsQ0FBQXZFLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBcEIyRSxvQkFBb0I7SUFBQTtFQUFBO0lBQUE1RixHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBOEYseUJBQUEsR0FBQTVGLGlCQUFBLENBd0IxQixXQUF1QzZGLFFBQWdCLEVBQWdEO1FBQUEsSUFBOUNDLE9BQW9CLEdBQUFoRixTQUFBLENBQUF5RCxNQUFBLFFBQUF6RCxTQUFBLFFBQUEwRCxTQUFBLEdBQUExRCxTQUFBLE9BQUExQixjQUFBLEdBQUFLLENBQUEsV0FBRyxDQUFDLENBQUM7UUFBQUwsY0FBQSxHQUFBRyxDQUFBO1FBQ2hGLElBQU13RyxPQUFPLElBQUEzRyxjQUFBLEdBQUFDLENBQUEsU0FBR1AsV0FBVyxDQUFDa0gsZUFBZSxDQUFDLENBQUMsQ0FBQ0QsT0FBTztRQUFDM0csY0FBQSxHQUFBQyxDQUFBO1FBQ3RELElBQUksRUFBQzBHLE9BQU8sWUFBUEEsT0FBTyxDQUFFRSxZQUFZLEdBQUU7VUFBQTdHLGNBQUEsR0FBQUssQ0FBQTtVQUFBTCxjQUFBLEdBQUFDLENBQUE7VUFDMUIsTUFBTSxJQUFJb0MsS0FBSyxDQUFDLHdCQUF3QixDQUFDO1FBQzNDLENBQUM7VUFBQXJDLGNBQUEsR0FBQUssQ0FBQTtRQUFBO1FBQUFMLGNBQUEsR0FBQUMsQ0FBQTtRQUVELE9BQU82RyxLQUFLLENBQUMsR0FBRyxJQUFJLENBQUN2RyxVQUFVLEdBQUdrRyxRQUFRLEVBQUUsRUFBQU0sTUFBQSxDQUFBQyxNQUFBLEtBQ3ZDTixPQUFPO1VBQ1ZPLE9BQU8sRUFBQUYsTUFBQSxDQUFBQyxNQUFBO1lBQ0wsY0FBYyxFQUFFLGtCQUFrQjtZQUNsQyxlQUFlLEVBQUUsVUFBVUwsT0FBTyxDQUFDRSxZQUFZO1VBQUUsR0FDOUNILE9BQU8sQ0FBQ08sT0FBTztRQUNuQixFQUNGLENBQUM7TUFDSixDQUFDO01BQUEsU0FkYXRFLHdCQUF3QkEsQ0FBQXVFLEdBQUE7UUFBQSxPQUFBVix5QkFBQSxDQUFBL0UsS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUF4QmlCLHdCQUF3QjtJQUFBO0VBQUE7QUFBQTtBQWtCeEMsT0FBTyxJQUFNd0UsYUFBYSxJQUFBbkgsY0FBQSxHQUFBQyxDQUFBLFNBQUcsSUFBSUosYUFBYSxDQUFDLENBQUM7QUFDaEQsZUFBZXNILGFBQWEiLCJpZ25vcmVMaXN0IjpbXX0=