a9d107b9789b234667f513c696aa7b6e
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _createForOfIteratorHelperLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/createForOfIteratorHelperLoose"));
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var AnimatedWithChildren = function (_AnimatedNode$default) {
  function AnimatedWithChildren() {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedWithChildren);
    _this = _callSuper(this, AnimatedWithChildren);
    _this._children = [];
    return _this;
  }
  (0, _inherits2.default)(AnimatedWithChildren, _AnimatedNode$default);
  return (0, _createClass2.default)(AnimatedWithChildren, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      if (!this.__isNative) {
        this.__isNative = true;
        for (var _iterator = (0, _createForOfIteratorHelperLoose2.default)(this._children), _step; !(_step = _iterator()).done;) {
          var child = _step.value;
          child.__makeNative(platformConfig);
          _NativeAnimatedHelper.default.API.connectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());
        }
      }
      _superPropGet(AnimatedWithChildren, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__addChild",
    value: function __addChild(child) {
      if (this._children.length === 0) {
        this.__attach();
      }
      this._children.push(child);
      if (this.__isNative) {
        child.__makeNative(this.__getPlatformConfig());
        _NativeAnimatedHelper.default.API.connectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());
      }
    }
  }, {
    key: "__removeChild",
    value: function __removeChild(child) {
      var index = this._children.indexOf(child);
      if (index === -1) {
        console.warn("Trying to remove a child that doesn't exist");
        return;
      }
      if (this.__isNative && child.__isNative) {
        _NativeAnimatedHelper.default.API.disconnectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());
      }
      this._children.splice(index, 1);
      if (this._children.length === 0) {
        this.__detach();
      }
    }
  }, {
    key: "__getChildren",
    value: function __getChildren() {
      return this._children;
    }
  }, {
    key: "__callListeners",
    value: function __callListeners(value) {
      _superPropGet(AnimatedWithChildren, "__callListeners", this, 3)([value]);
      if (!this.__isNative) {
        for (var _iterator2 = (0, _createForOfIteratorHelperLoose2.default)(this._children), _step2; !(_step2 = _iterator2()).done;) {
          var child = _step2.value;
          if (child.__getValue) {
            child.__callListeners(child.__getValue());
          }
        }
      }
    }
  }]);
}(_AnimatedNode.default);
var _default = exports.default = AnimatedWithChildren;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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