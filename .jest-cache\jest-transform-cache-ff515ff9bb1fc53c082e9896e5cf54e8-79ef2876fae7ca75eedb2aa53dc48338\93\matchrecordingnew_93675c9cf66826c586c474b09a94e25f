c8c4eedd4fabe9ddd0eff831f5a33710
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2mjk4tpwdv() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\match-recording-new.tsx";
  var hash = "8431352513f4d29e7e245faa17afb26e85551f98";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\match-recording-new.tsx",
    statementMap: {
      "0": {
        start: {
          line: 42,
          column: 26
        },
        end: {
          line: 42,
          column: 50
        }
      },
      "1": {
        start: {
          line: 44,
          column: 15
        },
        end: {
          line: 55,
          column: 1
        }
      },
      "2": {
        start: {
          line: 58,
          column: 19
        },
        end: {
          line: 58,
          column: 28
        }
      },
      "3": {
        start: {
          line: 76,
          column: 6
        },
        end: {
          line: 76,
          column: 25
        }
      },
      "4": {
        start: {
          line: 79,
          column: 46
        },
        end: {
          line: 79,
          column: 60
        }
      },
      "5": {
        start: {
          line: 80,
          column: 52
        },
        end: {
          line: 80,
          column: 67
        }
      },
      "6": {
        start: {
          line: 81,
          column: 38
        },
        end: {
          line: 81,
          column: 63
        }
      },
      "7": {
        start: {
          line: 82,
          column: 36
        },
        end: {
          line: 101,
          column: 4
        }
      },
      "8": {
        start: {
          line: 103,
          column: 20
        },
        end: {
          line: 103,
          column: 40
        }
      },
      "9": {
        start: {
          line: 106,
          column: 2
        },
        end: {
          line: 110,
          column: 26
        }
      },
      "10": {
        start: {
          line: 107,
          column: 4
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "11": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 60
        }
      },
      "12": {
        start: {
          line: 113,
          column: 27
        },
        end: {
          line: 132,
          column: 3
        }
      },
      "13": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 131,
          column: 5
        }
      },
      "14": {
        start: {
          line: 115,
          column: 21
        },
        end: {
          line: 115,
          column: 49
        }
      },
      "15": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 119,
          column: 7
        }
      },
      "16": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 66
        }
      },
      "17": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 15
        }
      },
      "18": {
        start: {
          line: 121,
          column: 23
        },
        end: {
          line: 121,
          column: 53
        }
      },
      "19": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 126,
          column: 9
        }
      },
      "20": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 31
        }
      },
      "21": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 62
        }
      },
      "22": {
        start: {
          line: 135,
          column: 25
        },
        end: {
          line: 157,
          column: 3
        }
      },
      "23": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 156,
          column: 5
        }
      },
      "24": {
        start: {
          line: 137,
          column: 21
        },
        end: {
          line: 137,
          column: 37
        }
      },
      "25": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 153,
          column: 7
        }
      },
      "26": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 152,
          column: 10
        }
      },
      "27": {
        start: {
          line: 145,
          column: 29
        },
        end: {
          line: 145,
          column: 65
        }
      },
      "28": {
        start: {
          line: 149,
          column: 29
        },
        end: {
          line: 149,
          column: 42
        }
      },
      "29": {
        start: {
          line: 155,
          column: 6
        },
        end: {
          line: 155,
          column: 50
        }
      },
      "30": {
        start: {
          line: 160,
          column: 25
        },
        end: {
          line: 166,
          column: 3
        }
      },
      "31": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 165,
          column: 5
        }
      },
      "32": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 40
        }
      },
      "33": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 50
        }
      },
      "34": {
        start: {
          line: 169,
          column: 25
        },
        end: {
          line: 178,
          column: 3
        }
      },
      "35": {
        start: {
          line: 170,
          column: 18
        },
        end: {
          line: 170,
          column: 44
        }
      },
      "36": {
        start: {
          line: 171,
          column: 20
        },
        end: {
          line: 171,
          column: 53
        }
      },
      "37": {
        start: {
          line: 172,
          column: 17
        },
        end: {
          line: 172,
          column: 29
        }
      },
      "38": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 176,
          column: 5
        }
      },
      "39": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 99
        }
      },
      "40": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 177,
          column: 60
        }
      },
      "41": {
        start: {
          line: 181,
          column: 27
        },
        end: {
          line: 300,
          column: 3
        }
      },
      "42": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 299,
          column: 12
        }
      },
      "43": {
        start: {
          line: 185,
          column: 43
        },
        end: {
          line: 185,
          column: 56
        }
      },
      "44": {
        start: {
          line: 198,
          column: 38
        },
        end: {
          line: 198,
          column: 93
        }
      },
      "45": {
        start: {
          line: 198,
          column: 60
        },
        end: {
          line: 198,
          column: 91
        }
      },
      "46": {
        start: {
          line: 208,
          column: 16
        },
        end: {
          line: 224,
          column: 35
        }
      },
      "47": {
        start: {
          line: 214,
          column: 33
        },
        end: {
          line: 214,
          column: 85
        }
      },
      "48": {
        start: {
          line: 214,
          column: 55
        },
        end: {
          line: 214,
          column: 83
        }
      },
      "49": {
        start: {
          line: 233,
          column: 16
        },
        end: {
          line: 249,
          column: 35
        }
      },
      "50": {
        start: {
          line: 239,
          column: 33
        },
        end: {
          line: 239,
          column: 89
        }
      },
      "51": {
        start: {
          line: 239,
          column: 55
        },
        end: {
          line: 239,
          column: 87
        }
      },
      "52": {
        start: {
          line: 258,
          column: 16
        },
        end: {
          line: 274,
          column: 35
        }
      },
      "53": {
        start: {
          line: 264,
          column: 33
        },
        end: {
          line: 264,
          column: 77
        }
      },
      "54": {
        start: {
          line: 264,
          column: 55
        },
        end: {
          line: 264,
          column: 75
        }
      },
      "55": {
        start: {
          line: 284,
          column: 38
        },
        end: {
          line: 284,
          column: 89
        }
      },
      "56": {
        start: {
          line: 284,
          column: 60
        },
        end: {
          line: 284,
          column: 87
        }
      },
      "57": {
        start: {
          line: 303,
          column: 27
        },
        end: {
          line: 355,
          column: 3
        }
      },
      "58": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 354,
          column: 11
        }
      },
      "59": {
        start: {
          line: 315,
          column: 29
        },
        end: {
          line: 317,
          column: 15
        }
      },
      "60": {
        start: {
          line: 338,
          column: 29
        },
        end: {
          line: 338,
          column: 55
        }
      },
      "61": {
        start: {
          line: 357,
          column: 2
        },
        end: {
          line: 359,
          column: 3
        }
      },
      "62": {
        start: {
          line: 358,
          column: 4
        },
        end: {
          line: 358,
          column: 30
        }
      },
      "63": {
        start: {
          line: 361,
          column: 2
        },
        end: {
          line: 484,
          column: 4
        }
      },
      "64": {
        start: {
          line: 364,
          column: 41
        },
        end: {
          line: 364,
          column: 54
        }
      },
      "65": {
        start: {
          line: 368,
          column: 41
        },
        end: {
          line: 368,
          column: 67
        }
      },
      "66": {
        start: {
          line: 429,
          column: 29
        },
        end: {
          line: 429,
          column: 51
        }
      },
      "67": {
        start: {
          line: 438,
          column: 29
        },
        end: {
          line: 438,
          column: 55
        }
      },
      "68": {
        start: {
          line: 487,
          column: 15
        },
        end: {
          line: 787,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "EnhancedMatchRecordingScreen",
        decl: {
          start: {
            line: 57,
            column: 24
          },
          end: {
            line: 57,
            column: 52
          }
        },
        loc: {
          start: {
            line: 57,
            column: 55
          },
          end: {
            line: 485,
            column: 1
          }
        },
        line: 57
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 106,
            column: 12
          },
          end: {
            line: 106,
            column: 13
          }
        },
        loc: {
          start: {
            line: 106,
            column: 18
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 106
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 113,
            column: 27
          },
          end: {
            line: 113,
            column: 28
          }
        },
        loc: {
          start: {
            line: 113,
            column: 39
          },
          end: {
            line: 132,
            column: 3
          }
        },
        line: 113
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 135,
            column: 25
          },
          end: {
            line: 135,
            column: 26
          }
        },
        loc: {
          start: {
            line: 135,
            column: 37
          },
          end: {
            line: 157,
            column: 3
          }
        },
        line: 135
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 145,
            column: 23
          },
          end: {
            line: 145,
            column: 24
          }
        },
        loc: {
          start: {
            line: 145,
            column: 29
          },
          end: {
            line: 145,
            column: 65
          }
        },
        line: 145
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 149,
            column: 23
          },
          end: {
            line: 149,
            column: 24
          }
        },
        loc: {
          start: {
            line: 149,
            column: 29
          },
          end: {
            line: 149,
            column: 42
          }
        },
        line: 149
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 160,
            column: 25
          },
          end: {
            line: 160,
            column: 26
          }
        },
        loc: {
          start: {
            line: 160,
            column: 84
          },
          end: {
            line: 166,
            column: 3
          }
        },
        line: 160
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 169,
            column: 25
          },
          end: {
            line: 169,
            column: 26
          }
        },
        loc: {
          start: {
            line: 169,
            column: 54
          },
          end: {
            line: 178,
            column: 3
          }
        },
        line: 169
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 181,
            column: 27
          },
          end: {
            line: 181,
            column: 28
          }
        },
        loc: {
          start: {
            line: 182,
            column: 4
          },
          end: {
            line: 299,
            column: 12
          }
        },
        line: 182
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 185,
            column: 37
          },
          end: {
            line: 185,
            column: 38
          }
        },
        loc: {
          start: {
            line: 185,
            column: 43
          },
          end: {
            line: 185,
            column: 56
          }
        },
        line: 185
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 198,
            column: 28
          },
          end: {
            line: 198,
            column: 29
          }
        },
        loc: {
          start: {
            line: 198,
            column: 38
          },
          end: {
            line: 198,
            column: 93
          }
        },
        line: 198
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 198,
            column: 51
          },
          end: {
            line: 198,
            column: 52
          }
        },
        loc: {
          start: {
            line: 198,
            column: 60
          },
          end: {
            line: 198,
            column: 91
          }
        },
        line: 198
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 207,
            column: 85
          },
          end: {
            line: 207,
            column: 86
          }
        },
        loc: {
          start: {
            line: 208,
            column: 16
          },
          end: {
            line: 224,
            column: 35
          }
        },
        line: 208
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 214,
            column: 27
          },
          end: {
            line: 214,
            column: 28
          }
        },
        loc: {
          start: {
            line: 214,
            column: 33
          },
          end: {
            line: 214,
            column: 85
          }
        },
        line: 214
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 214,
            column: 46
          },
          end: {
            line: 214,
            column: 47
          }
        },
        loc: {
          start: {
            line: 214,
            column: 55
          },
          end: {
            line: 214,
            column: 83
          }
        },
        line: 214
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 232,
            column: 76
          },
          end: {
            line: 232,
            column: 77
          }
        },
        loc: {
          start: {
            line: 233,
            column: 16
          },
          end: {
            line: 249,
            column: 35
          }
        },
        line: 233
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 239,
            column: 27
          },
          end: {
            line: 239,
            column: 28
          }
        },
        loc: {
          start: {
            line: 239,
            column: 33
          },
          end: {
            line: 239,
            column: 89
          }
        },
        line: 239
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 239,
            column: 46
          },
          end: {
            line: 239,
            column: 47
          }
        },
        loc: {
          start: {
            line: 239,
            column: 55
          },
          end: {
            line: 239,
            column: 87
          }
        },
        line: 239
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 257,
            column: 75
          },
          end: {
            line: 257,
            column: 76
          }
        },
        loc: {
          start: {
            line: 258,
            column: 16
          },
          end: {
            line: 274,
            column: 35
          }
        },
        line: 258
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 264,
            column: 27
          },
          end: {
            line: 264,
            column: 28
          }
        },
        loc: {
          start: {
            line: 264,
            column: 33
          },
          end: {
            line: 264,
            column: 77
          }
        },
        line: 264
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 264,
            column: 46
          },
          end: {
            line: 264,
            column: 47
          }
        },
        loc: {
          start: {
            line: 264,
            column: 55
          },
          end: {
            line: 264,
            column: 75
          }
        },
        line: 264
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 284,
            column: 28
          },
          end: {
            line: 284,
            column: 29
          }
        },
        loc: {
          start: {
            line: 284,
            column: 38
          },
          end: {
            line: 284,
            column: 89
          }
        },
        line: 284
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 284,
            column: 51
          },
          end: {
            line: 284,
            column: 52
          }
        },
        loc: {
          start: {
            line: 284,
            column: 60
          },
          end: {
            line: 284,
            column: 87
          }
        },
        line: 284
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 303,
            column: 27
          },
          end: {
            line: 303,
            column: 28
          }
        },
        loc: {
          start: {
            line: 304,
            column: 4
          },
          end: {
            line: 354,
            column: 11
          }
        },
        line: 304
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 315,
            column: 23
          },
          end: {
            line: 315,
            column: 24
          }
        },
        loc: {
          start: {
            line: 315,
            column: 29
          },
          end: {
            line: 317,
            column: 15
          }
        },
        line: 315
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 338,
            column: 23
          },
          end: {
            line: 338,
            column: 24
          }
        },
        loc: {
          start: {
            line: 338,
            column: 29
          },
          end: {
            line: 338,
            column: 55
          }
        },
        line: 338
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 364,
            column: 35
          },
          end: {
            line: 364,
            column: 36
          }
        },
        loc: {
          start: {
            line: 364,
            column: 41
          },
          end: {
            line: 364,
            column: 54
          }
        },
        line: 364
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 368,
            column: 35
          },
          end: {
            line: 368,
            column: 36
          }
        },
        loc: {
          start: {
            line: 368,
            column: 41
          },
          end: {
            line: 368,
            column: 67
          }
        },
        line: 368
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 429,
            column: 23
          },
          end: {
            line: 429,
            column: 24
          }
        },
        loc: {
          start: {
            line: 429,
            column: 29
          },
          end: {
            line: 429,
            column: 51
          }
        },
        line: 429
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 438,
            column: 23
          },
          end: {
            line: 438,
            column: 24
          }
        },
        loc: {
          start: {
            line: 438,
            column: 29
          },
          end: {
            line: 438,
            column: 55
          }
        },
        line: 438
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 109,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 107,
            column: 4
          },
          end: {
            line: 109,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 107
      },
      "1": {
        loc: {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 116
      },
      "2": {
        loc: {
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 153,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 153,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "3": {
        loc: {
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 176,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 176,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "4": {
        loc: {
          start: {
            line: 212,
            column: 20
          },
          end: {
            line: 212,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 212,
            column: 20
          },
          end: {
            line: 212,
            column: 48
          }
        }, {
          start: {
            line: 212,
            column: 52
          },
          end: {
            line: 212,
            column: 79
          }
        }],
        line: 212
      },
      "5": {
        loc: {
          start: {
            line: 219,
            column: 22
          },
          end: {
            line: 219,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 219,
            column: 22
          },
          end: {
            line: 219,
            column: 50
          }
        }, {
          start: {
            line: 219,
            column: 54
          },
          end: {
            line: 219,
            column: 79
          }
        }],
        line: 219
      },
      "6": {
        loc: {
          start: {
            line: 237,
            column: 20
          },
          end: {
            line: 237,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 237,
            column: 20
          },
          end: {
            line: 237,
            column: 52
          }
        }, {
          start: {
            line: 237,
            column: 56
          },
          end: {
            line: 237,
            column: 83
          }
        }],
        line: 237
      },
      "7": {
        loc: {
          start: {
            line: 244,
            column: 22
          },
          end: {
            line: 244,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 22
          },
          end: {
            line: 244,
            column: 54
          }
        }, {
          start: {
            line: 244,
            column: 58
          },
          end: {
            line: 244,
            column: 83
          }
        }],
        line: 244
      },
      "8": {
        loc: {
          start: {
            line: 262,
            column: 20
          },
          end: {
            line: 262,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 262,
            column: 20
          },
          end: {
            line: 262,
            column: 49
          }
        }, {
          start: {
            line: 262,
            column: 53
          },
          end: {
            line: 262,
            column: 80
          }
        }],
        line: 262
      },
      "9": {
        loc: {
          start: {
            line: 269,
            column: 22
          },
          end: {
            line: 269,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 269,
            column: 22
          },
          end: {
            line: 269,
            column: 51
          }
        }, {
          start: {
            line: 269,
            column: 55
          },
          end: {
            line: 269,
            column: 80
          }
        }],
        line: 269
      },
      "10": {
        loc: {
          start: {
            line: 316,
            column: 16
          },
          end: {
            line: 316,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 316,
            column: 49
          },
          end: {
            line: 316,
            column: 65
          }
        }, {
          start: {
            line: 316,
            column: 68
          },
          end: {
            line: 316,
            column: 83
          }
        }],
        line: 316
      },
      "11": {
        loc: {
          start: {
            line: 325,
            column: 16
          },
          end: {
            line: 325,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 325,
            column: 16
          },
          end: {
            line: 325,
            column: 46
          }
        }, {
          start: {
            line: 325,
            column: 50
          },
          end: {
            line: 325,
            column: 75
          }
        }],
        line: 325
      },
      "12": {
        loc: {
          start: {
            line: 329,
            column: 15
          },
          end: {
            line: 333,
            column: 15
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 330,
            column: 16
          },
          end: {
            line: 330,
            column: 57
          }
        }, {
          start: {
            line: 332,
            column: 16
          },
          end: {
            line: 332,
            column: 56
          }
        }],
        line: 329
      },
      "13": {
        loc: {
          start: {
            line: 344,
            column: 11
          },
          end: {
            line: 351,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 344,
            column: 11
          },
          end: {
            line: 344,
            column: 41
          }
        }, {
          start: {
            line: 345,
            column: 12
          },
          end: {
            line: 350,
            column: 19
          }
        }],
        line: 344
      },
      "14": {
        loc: {
          start: {
            line: 357,
            column: 2
          },
          end: {
            line: 359,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 357,
            column: 2
          },
          end: {
            line: 359,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 357
      },
      "15": {
        loc: {
          start: {
            line: 450,
            column: 13
          },
          end: {
            line: 466,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 451,
            column: 14
          },
          end: {
            line: 457,
            column: 16
          }
        }, {
          start: {
            line: 459,
            column: 14
          },
          end: {
            line: 465,
            column: 16
          }
        }],
        line: 450
      },
      "16": {
        loc: {
          start: {
            line: 478,
            column: 7
          },
          end: {
            line: 482,
            column: 7
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 478,
            column: 7
          },
          end: {
            line: 478,
            column: 18
          }
        }, {
          start: {
            line: 479,
            column: 8
          },
          end: {
            line: 481,
            column: 15
          }
        }],
        line: 478
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "8431352513f4d29e7e245faa17afb26e85551f98"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2mjk4tpwdv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2mjk4tpwdv();
import React, { useState, useEffect, useRef } from 'react';
import { View, Text, StyleSheet, Alert, ScrollView, TouchableOpacity, Modal, TextInput, Dimensions } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import { Camera, CameraType } from 'expo-camera';
import { Play, Pause, Square, Video, Settings, Timer, Trophy, Plus, ArrowLeft, RotateCcw } from 'lucide-react-native';
import { Button } from "../components/ui/Button";
import { Card } from "../components/ui/Card";
import { useAuth } from "../contexts/AuthContext";
import { useMatchRecording } from "../src/hooks/useMatchRecording";
import { videoRecordingService } from "../src/services/video/VideoRecordingService";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var _ref = (cov_2mjk4tpwdv().s[0]++, Dimensions.get('window')),
  width = _ref.width,
  height = _ref.height;
var colors = (cov_2mjk4tpwdv().s[1]++, {
  primary: '#23ba16',
  secondary: '#1a5e1a',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  red: '#ef4444',
  blue: '#3b82f6',
  yellow: '#eab308',
  green: '#10b981'
});
export default function EnhancedMatchRecordingScreen() {
  cov_2mjk4tpwdv().f[0]++;
  var _ref2 = (cov_2mjk4tpwdv().s[2]++, useAuth()),
    user = _ref2.user;
  var _ref3 = (cov_2mjk4tpwdv().s[3]++, useMatchRecording()),
    state = _ref3.state,
    currentSession = _ref3.currentSession,
    recordingProgress = _ref3.recordingProgress,
    startMatch = _ref3.startMatch,
    endMatch = _ref3.endMatch,
    pauseMatch = _ref3.pauseMatch,
    resumeMatch = _ref3.resumeMatch,
    cancelMatch = _ref3.cancelMatch,
    addPoint = _ref3.addPoint,
    toggleVideoRecording = _ref3.toggleVideoRecording,
    validateMatchForm = _ref3.validateMatchForm,
    createMatchMetadata = _ref3.createMatchMetadata,
    getMatchDuration = _ref3.getMatchDuration,
    getFormattedScore = _ref3.getFormattedScore,
    isMatchInProgress = _ref3.isMatchInProgress,
    canAddPoint = _ref3.canAddPoint;
  var _ref4 = (cov_2mjk4tpwdv().s[4]++, useState(true)),
    _ref5 = _slicedToArray(_ref4, 2),
    showSetupModal = _ref5[0],
    setShowSetupModal = _ref5[1];
  var _ref6 = (cov_2mjk4tpwdv().s[5]++, useState(false)),
    _ref7 = _slicedToArray(_ref6, 2),
    showVideoSettings = _ref7[0],
    setShowVideoSettings = _ref7[1];
  var _ref8 = (cov_2mjk4tpwdv().s[6]++, useState(CameraType.back)),
    _ref9 = _slicedToArray(_ref8, 2),
    cameraType = _ref9[0],
    setCameraType = _ref9[1];
  var _ref0 = (cov_2mjk4tpwdv().s[7]++, useState({
      opponentName: '',
      matchType: 'friendly',
      matchFormat: 'best_of_3',
      surface: 'hard',
      location: '',
      courtName: '',
      weatherConditions: '',
      temperature: undefined,
      tournamentName: '',
      tournamentRound: '',
      privacySettings: {
        isPublic: false,
        allowSharing: true,
        shareWithFriends: true,
        shareStatistics: true,
        shareVideo: false,
        allowComments: true
      }
    })),
    _ref1 = _slicedToArray(_ref0, 2),
    matchForm = _ref1[0],
    setMatchForm = _ref1[1];
  var cameraRef = (cov_2mjk4tpwdv().s[8]++, useRef(null));
  cov_2mjk4tpwdv().s[9]++;
  useEffect(function () {
    cov_2mjk4tpwdv().f[1]++;
    cov_2mjk4tpwdv().s[10]++;
    if (cameraRef.current) {
      cov_2mjk4tpwdv().b[0][0]++;
      cov_2mjk4tpwdv().s[11]++;
      videoRecordingService.setCameraRef(cameraRef.current);
    } else {
      cov_2mjk4tpwdv().b[0][1]++;
    }
  }, [cameraRef.current]);
  cov_2mjk4tpwdv().s[12]++;
  var handleStartMatch = function () {
    var _ref10 = _asyncToGenerator(function* () {
      cov_2mjk4tpwdv().f[2]++;
      cov_2mjk4tpwdv().s[13]++;
      try {
        var errors = (cov_2mjk4tpwdv().s[14]++, validateMatchForm(matchForm));
        cov_2mjk4tpwdv().s[15]++;
        if (Object.keys(errors).length > 0) {
          cov_2mjk4tpwdv().b[1][0]++;
          cov_2mjk4tpwdv().s[16]++;
          Alert.alert('Validation Error', Object.values(errors)[0]);
          cov_2mjk4tpwdv().s[17]++;
          return;
        } else {
          cov_2mjk4tpwdv().b[1][1]++;
        }
        var metadata = (cov_2mjk4tpwdv().s[18]++, createMatchMetadata(matchForm));
        cov_2mjk4tpwdv().s[19]++;
        yield startMatch(metadata, {
          enableVideoRecording: true,
          enableAutoScoreDetection: false,
          enableStatisticsTracking: true
        });
        cov_2mjk4tpwdv().s[20]++;
        setShowSetupModal(false);
      } catch (error) {
        cov_2mjk4tpwdv().s[21]++;
        Alert.alert('Error', 'Failed to start match recording');
      }
    });
    return function handleStartMatch() {
      return _ref10.apply(this, arguments);
    };
  }();
  cov_2mjk4tpwdv().s[22]++;
  var handleEndMatch = function () {
    var _ref11 = _asyncToGenerator(function* () {
      cov_2mjk4tpwdv().f[3]++;
      cov_2mjk4tpwdv().s[23]++;
      try {
        var result = (cov_2mjk4tpwdv().s[24]++, yield endMatch());
        cov_2mjk4tpwdv().s[25]++;
        if (result) {
          cov_2mjk4tpwdv().b[2][0]++;
          cov_2mjk4tpwdv().s[26]++;
          Alert.alert('Match Completed', 'Your match has been saved successfully!', [{
            text: 'View Match',
            onPress: function onPress() {
              cov_2mjk4tpwdv().f[4]++;
              cov_2mjk4tpwdv().s[27]++;
              return router.push(`/matches/${result.id}`);
            }
          }, {
            text: 'OK',
            onPress: function onPress() {
              cov_2mjk4tpwdv().f[5]++;
              cov_2mjk4tpwdv().s[28]++;
              return router.back();
            }
          }]);
        } else {
          cov_2mjk4tpwdv().b[2][1]++;
        }
      } catch (error) {
        cov_2mjk4tpwdv().s[29]++;
        Alert.alert('Error', 'Failed to end match');
      }
    });
    return function handleEndMatch() {
      return _ref11.apply(this, arguments);
    };
  }();
  cov_2mjk4tpwdv().s[30]++;
  var handleAddPoint = function () {
    var _ref12 = _asyncToGenerator(function* (winner, eventType) {
      cov_2mjk4tpwdv().f[6]++;
      cov_2mjk4tpwdv().s[31]++;
      try {
        cov_2mjk4tpwdv().s[32]++;
        yield addPoint(winner, eventType);
      } catch (error) {
        cov_2mjk4tpwdv().s[33]++;
        Alert.alert('Error', 'Failed to add point');
      }
    });
    return function handleAddPoint(_x, _x2) {
      return _ref12.apply(this, arguments);
    };
  }();
  cov_2mjk4tpwdv().s[34]++;
  var formatDuration = function formatDuration(seconds) {
    cov_2mjk4tpwdv().f[7]++;
    var hours = (cov_2mjk4tpwdv().s[35]++, Math.floor(seconds / 3600));
    var minutes = (cov_2mjk4tpwdv().s[36]++, Math.floor(seconds % 3600 / 60));
    var secs = (cov_2mjk4tpwdv().s[37]++, seconds % 60);
    cov_2mjk4tpwdv().s[38]++;
    if (hours > 0) {
      cov_2mjk4tpwdv().b[3][0]++;
      cov_2mjk4tpwdv().s[39]++;
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
      cov_2mjk4tpwdv().b[3][1]++;
    }
    cov_2mjk4tpwdv().s[40]++;
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };
  cov_2mjk4tpwdv().s[41]++;
  var renderSetupModal = function renderSetupModal() {
    cov_2mjk4tpwdv().f[8]++;
    cov_2mjk4tpwdv().s[42]++;
    return _jsx(Modal, {
      visible: showSetupModal,
      animationType: "slide",
      presentationStyle: "pageSheet",
      children: _jsxs(SafeAreaView, {
        style: styles.modalContainer,
        children: [_jsxs(View, {
          style: styles.modalHeader,
          children: [_jsx(TouchableOpacity, {
            onPress: function onPress() {
              cov_2mjk4tpwdv().f[9]++;
              cov_2mjk4tpwdv().s[43]++;
              return router.back();
            },
            children: _jsx(ArrowLeft, {
              size: 24,
              color: colors.dark
            })
          }), _jsx(Text, {
            style: styles.modalTitle,
            children: "Match Setup"
          }), _jsx(View, {
            style: {
              width: 24
            }
          })]
        }), _jsxs(ScrollView, {
          style: styles.modalContent,
          children: [_jsxs(Card, {
            style: styles.formCard,
            children: [_jsx(Text, {
              style: styles.formLabel,
              children: "Opponent Name *"
            }), _jsx(TextInput, {
              style: styles.textInput,
              value: matchForm.opponentName,
              onChangeText: function onChangeText(text) {
                cov_2mjk4tpwdv().f[10]++;
                cov_2mjk4tpwdv().s[44]++;
                return setMatchForm(function (prev) {
                  cov_2mjk4tpwdv().f[11]++;
                  cov_2mjk4tpwdv().s[45]++;
                  return Object.assign({}, prev, {
                    opponentName: text
                  });
                });
              },
              placeholder: "Enter opponent name",
              placeholderTextColor: colors.gray
            })]
          }), _jsxs(Card, {
            style: styles.formCard,
            children: [_jsx(Text, {
              style: styles.formLabel,
              children: "Match Type"
            }), _jsx(View, {
              style: styles.optionRow,
              children: ['practice', 'tournament', 'friendly', 'lesson'].map(function (type) {
                cov_2mjk4tpwdv().f[12]++;
                cov_2mjk4tpwdv().s[46]++;
                return _jsx(TouchableOpacity, {
                  style: [styles.optionButton, (cov_2mjk4tpwdv().b[4][0]++, matchForm.matchType === type) && (cov_2mjk4tpwdv().b[4][1]++, styles.optionButtonSelected)],
                  onPress: function onPress() {
                    cov_2mjk4tpwdv().f[13]++;
                    cov_2mjk4tpwdv().s[47]++;
                    return setMatchForm(function (prev) {
                      cov_2mjk4tpwdv().f[14]++;
                      cov_2mjk4tpwdv().s[48]++;
                      return Object.assign({}, prev, {
                        matchType: type
                      });
                    });
                  },
                  children: _jsx(Text, {
                    style: [styles.optionText, (cov_2mjk4tpwdv().b[5][0]++, matchForm.matchType === type) && (cov_2mjk4tpwdv().b[5][1]++, styles.optionTextSelected)],
                    children: type.charAt(0).toUpperCase() + type.slice(1)
                  })
                }, type);
              })
            })]
          }), _jsxs(Card, {
            style: styles.formCard,
            children: [_jsx(Text, {
              style: styles.formLabel,
              children: "Match Format"
            }), _jsx(View, {
              style: styles.optionRow,
              children: ['best_of_3', 'best_of_5', 'pro_set'].map(function (format) {
                cov_2mjk4tpwdv().f[15]++;
                cov_2mjk4tpwdv().s[49]++;
                return _jsx(TouchableOpacity, {
                  style: [styles.optionButton, (cov_2mjk4tpwdv().b[6][0]++, matchForm.matchFormat === format) && (cov_2mjk4tpwdv().b[6][1]++, styles.optionButtonSelected)],
                  onPress: function onPress() {
                    cov_2mjk4tpwdv().f[16]++;
                    cov_2mjk4tpwdv().s[50]++;
                    return setMatchForm(function (prev) {
                      cov_2mjk4tpwdv().f[17]++;
                      cov_2mjk4tpwdv().s[51]++;
                      return Object.assign({}, prev, {
                        matchFormat: format
                      });
                    });
                  },
                  children: _jsx(Text, {
                    style: [styles.optionText, (cov_2mjk4tpwdv().b[7][0]++, matchForm.matchFormat === format) && (cov_2mjk4tpwdv().b[7][1]++, styles.optionTextSelected)],
                    children: format.replace('_', ' ').toUpperCase()
                  })
                }, format);
              })
            })]
          }), _jsxs(Card, {
            style: styles.formCard,
            children: [_jsx(Text, {
              style: styles.formLabel,
              children: "Court Surface"
            }), _jsx(View, {
              style: styles.optionRow,
              children: ['hard', 'clay', 'grass', 'indoor'].map(function (surface) {
                cov_2mjk4tpwdv().f[18]++;
                cov_2mjk4tpwdv().s[52]++;
                return _jsx(TouchableOpacity, {
                  style: [styles.optionButton, (cov_2mjk4tpwdv().b[8][0]++, matchForm.surface === surface) && (cov_2mjk4tpwdv().b[8][1]++, styles.optionButtonSelected)],
                  onPress: function onPress() {
                    cov_2mjk4tpwdv().f[19]++;
                    cov_2mjk4tpwdv().s[53]++;
                    return setMatchForm(function (prev) {
                      cov_2mjk4tpwdv().f[20]++;
                      cov_2mjk4tpwdv().s[54]++;
                      return Object.assign({}, prev, {
                        surface: surface
                      });
                    });
                  },
                  children: _jsx(Text, {
                    style: [styles.optionText, (cov_2mjk4tpwdv().b[9][0]++, matchForm.surface === surface) && (cov_2mjk4tpwdv().b[9][1]++, styles.optionTextSelected)],
                    children: surface.charAt(0).toUpperCase() + surface.slice(1)
                  })
                }, surface);
              })
            })]
          }), _jsxs(Card, {
            style: styles.formCard,
            children: [_jsx(Text, {
              style: styles.formLabel,
              children: "Location (Optional)"
            }), _jsx(TextInput, {
              style: styles.textInput,
              value: matchForm.location,
              onChangeText: function onChangeText(text) {
                cov_2mjk4tpwdv().f[21]++;
                cov_2mjk4tpwdv().s[55]++;
                return setMatchForm(function (prev) {
                  cov_2mjk4tpwdv().f[22]++;
                  cov_2mjk4tpwdv().s[56]++;
                  return Object.assign({}, prev, {
                    location: text
                  });
                });
              },
              placeholder: "Court location",
              placeholderTextColor: colors.gray
            })]
          }), _jsx(Button, {
            title: "Start Match Recording",
            onPress: handleStartMatch,
            style: styles.startButton,
            disabled: state.loading,
            loading: state.loading
          })]
        })]
      })
    });
  };
  cov_2mjk4tpwdv().s[57]++;
  var renderCameraView = function renderCameraView() {
    cov_2mjk4tpwdv().f[23]++;
    cov_2mjk4tpwdv().s[58]++;
    return _jsx(View, {
      style: styles.cameraContainer,
      children: _jsx(Camera, {
        ref: cameraRef,
        style: styles.camera,
        type: cameraType,
        ratio: "16:9",
        children: _jsxs(View, {
          style: styles.cameraOverlay,
          children: [_jsxs(View, {
            style: styles.cameraControls,
            children: [_jsx(TouchableOpacity, {
              style: styles.cameraButton,
              onPress: function onPress() {
                cov_2mjk4tpwdv().f[24]++;
                cov_2mjk4tpwdv().s[59]++;
                return setCameraType(cameraType === CameraType.back ? (cov_2mjk4tpwdv().b[10][0]++, CameraType.front) : (cov_2mjk4tpwdv().b[10][1]++, CameraType.back));
              },
              children: _jsx(RotateCcw, {
                size: 20,
                color: colors.white
              })
            }), _jsx(TouchableOpacity, {
              style: [styles.recordButton, (cov_2mjk4tpwdv().b[11][0]++, recordingProgress == null ? void 0 : recordingProgress.isRecording) && (cov_2mjk4tpwdv().b[11][1]++, styles.recordButtonActive)],
              onPress: toggleVideoRecording,
              children: recordingProgress != null && recordingProgress.isRecording ? (cov_2mjk4tpwdv().b[12][0]++, _jsx(Square, {
                size: 24,
                color: colors.white
              })) : (cov_2mjk4tpwdv().b[12][1]++, _jsx(Video, {
                size: 24,
                color: colors.white
              }))
            }), _jsx(TouchableOpacity, {
              style: styles.cameraButton,
              onPress: function onPress() {
                cov_2mjk4tpwdv().f[25]++;
                cov_2mjk4tpwdv().s[60]++;
                return setShowVideoSettings(true);
              },
              children: _jsx(Settings, {
                size: 20,
                color: colors.white
              })
            })]
          }), (cov_2mjk4tpwdv().b[13][0]++, recordingProgress == null ? void 0 : recordingProgress.isRecording) && (cov_2mjk4tpwdv().b[13][1]++, _jsxs(View, {
            style: styles.recordingIndicator,
            children: [_jsx(View, {
              style: styles.recordingDot
            }), _jsxs(Text, {
              style: styles.recordingText,
              children: ["REC ", formatDuration(Math.floor(recordingProgress.duration))]
            })]
          }))]
        })
      })
    });
  };
  cov_2mjk4tpwdv().s[61]++;
  if (showSetupModal) {
    cov_2mjk4tpwdv().b[14][0]++;
    cov_2mjk4tpwdv().s[62]++;
    return renderSetupModal();
  } else {
    cov_2mjk4tpwdv().b[14][1]++;
  }
  cov_2mjk4tpwdv().s[63]++;
  return _jsxs(SafeAreaView, {
    style: styles.container,
    children: [_jsxs(View, {
      style: styles.header,
      children: [_jsx(TouchableOpacity, {
        onPress: function onPress() {
          cov_2mjk4tpwdv().f[26]++;
          cov_2mjk4tpwdv().s[64]++;
          return router.back();
        },
        children: _jsx(ArrowLeft, {
          size: 24,
          color: colors.dark
        })
      }), _jsx(Text, {
        style: styles.headerTitle,
        children: "Live Match"
      }), _jsx(TouchableOpacity, {
        onPress: function onPress() {
          cov_2mjk4tpwdv().f[27]++;
          cov_2mjk4tpwdv().s[65]++;
          return setShowVideoSettings(true);
        },
        children: _jsx(Settings, {
          size: 24,
          color: colors.dark
        })
      })]
    }), _jsxs(ScrollView, {
      style: styles.content,
      children: [renderCameraView(), _jsx(Card, {
        style: styles.matchInfoCard,
        children: _jsxs(View, {
          style: styles.matchInfoRow,
          children: [_jsxs(View, {
            style: styles.matchInfoItem,
            children: [_jsx(Timer, {
              size: 16,
              color: colors.gray
            }), _jsx(Text, {
              style: styles.matchInfoText,
              children: formatDuration(getMatchDuration())
            })]
          }), _jsx(View, {
            style: styles.matchInfoItem,
            children: _jsx(Text, {
              style: styles.matchFormat,
              children: currentSession == null ? void 0 : currentSession.match.metadata.matchFormat.replace('_', ' ').toUpperCase()
            })
          }), _jsx(View, {
            style: styles.matchInfoItem,
            children: _jsx(Text, {
              style: styles.surfaceText,
              children: currentSession == null ? void 0 : currentSession.match.metadata.surface.toUpperCase()
            })
          })]
        })
      }), _jsxs(Card, {
        style: styles.scoreCard,
        children: [_jsx(Text, {
          style: styles.scoreTitle,
          children: "Current Score"
        }), _jsx(Text, {
          style: styles.scoreDisplay,
          children: getFormattedScore()
        }), _jsxs(View, {
          style: styles.playerScores,
          children: [_jsxs(View, {
            style: styles.playerScore,
            children: [_jsx(Text, {
              style: styles.playerName,
              children: "You"
            }), _jsx(Text, {
              style: styles.playerPoints,
              children: state.currentScore.setsWon
            })]
          }), _jsx(Text, {
            style: styles.scoreSeparator,
            children: "-"
          }), _jsxs(View, {
            style: styles.playerScore,
            children: [_jsx(Text, {
              style: styles.playerName,
              children: currentSession == null ? void 0 : currentSession.match.metadata.opponentName
            }), _jsx(Text, {
              style: styles.playerPoints,
              children: state.currentScore.setsLost
            })]
          })]
        })]
      }), _jsxs(Card, {
        style: styles.controlsCard,
        children: [_jsx(Text, {
          style: styles.controlsTitle,
          children: "Add Point"
        }), _jsxs(View, {
          style: styles.pointControls,
          children: [_jsxs(TouchableOpacity, {
            style: [styles.pointButton, styles.userPointButton],
            onPress: function onPress() {
              cov_2mjk4tpwdv().f[28]++;
              cov_2mjk4tpwdv().s[66]++;
              return handleAddPoint('user');
            },
            disabled: !canAddPoint(),
            children: [_jsx(Plus, {
              size: 24,
              color: colors.white
            }), _jsx(Text, {
              style: styles.pointButtonText,
              children: "Your Point"
            })]
          }), _jsxs(TouchableOpacity, {
            style: [styles.pointButton, styles.opponentPointButton],
            onPress: function onPress() {
              cov_2mjk4tpwdv().f[29]++;
              cov_2mjk4tpwdv().s[67]++;
              return handleAddPoint('opponent');
            },
            disabled: !canAddPoint(),
            children: [_jsx(Plus, {
              size: 24,
              color: colors.white
            }), _jsx(Text, {
              style: styles.pointButtonText,
              children: "Opponent Point"
            })]
          })]
        })]
      }), _jsx(Card, {
        style: styles.matchControlsCard,
        children: _jsxs(View, {
          style: styles.matchControlsRow,
          children: [isMatchInProgress() ? (cov_2mjk4tpwdv().b[15][0]++, _jsx(Button, {
            title: "Pause Match",
            onPress: pauseMatch,
            variant: "outline",
            style: styles.controlButton,
            icon: _jsx(Pause, {
              size: 16,
              color: colors.primary
            })
          })) : (cov_2mjk4tpwdv().b[15][1]++, _jsx(Button, {
            title: "Resume Match",
            onPress: resumeMatch,
            variant: "outline",
            style: styles.controlButton,
            icon: _jsx(Play, {
              size: 16,
              color: colors.primary
            })
          })), _jsx(Button, {
            title: "End Match",
            onPress: handleEndMatch,
            style: styles.controlButton,
            icon: _jsx(Trophy, {
              size: 16,
              color: colors.white
            })
          })]
        })
      })]
    }), (cov_2mjk4tpwdv().b[16][0]++, state.error) && (cov_2mjk4tpwdv().b[16][1]++, _jsx(View, {
      style: styles.errorContainer,
      children: _jsx(Text, {
        style: styles.errorText,
        children: state.error
      })
    }))]
  });
}
var styles = (cov_2mjk4tpwdv().s[68]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb'
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.dark
  },
  content: {
    flex: 1,
    padding: 16
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb'
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: colors.dark
  },
  modalContent: {
    flex: 1,
    padding: 16
  },
  formCard: {
    marginBottom: 16,
    padding: 16
  },
  formLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.dark,
    marginBottom: 8
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: colors.dark,
    backgroundColor: colors.white
  },
  optionRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8
  },
  optionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#d1d5db',
    backgroundColor: colors.white
  },
  optionButtonSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary
  },
  optionText: {
    fontSize: 14,
    color: colors.gray
  },
  optionTextSelected: {
    color: colors.white
  },
  startButton: {
    marginTop: 24
  },
  cameraContainer: {
    height: height * 0.3,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16
  },
  camera: {
    flex: 1
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'space-between'
  },
  cameraControls: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20
  },
  cameraButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center'
  },
  recordButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.white
  },
  recordButtonActive: {
    backgroundColor: colors.red
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginBottom: 20
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.red,
    marginRight: 8
  },
  recordingText: {
    color: colors.white,
    fontSize: 14,
    fontWeight: '500'
  },
  matchInfoCard: {
    marginBottom: 16,
    padding: 16
  },
  matchInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center'
  },
  matchInfoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  matchInfoText: {
    fontSize: 14,
    color: colors.gray,
    marginLeft: 4
  },
  matchFormat: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.primary
  },
  surfaceText: {
    fontSize: 12,
    color: colors.gray,
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 4
  },
  scoreCard: {
    marginBottom: 16,
    padding: 20,
    alignItems: 'center'
  },
  scoreTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray,
    marginBottom: 8
  },
  scoreDisplay: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.dark,
    marginBottom: 16
  },
  playerScores: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 20
  },
  playerScore: {
    alignItems: 'center'
  },
  playerName: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 4
  },
  playerPoints: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.primary
  },
  scoreSeparator: {
    fontSize: 24,
    color: colors.gray
  },
  controlsCard: {
    marginBottom: 16,
    padding: 16
  },
  controlsTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.dark,
    marginBottom: 16,
    textAlign: 'center'
  },
  pointControls: {
    flexDirection: 'row',
    gap: 12
  },
  pointButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8
  },
  userPointButton: {
    backgroundColor: colors.primary
  },
  opponentPointButton: {
    backgroundColor: colors.blue
  },
  pointButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '500'
  },
  matchControlsCard: {
    marginBottom: 16,
    padding: 16
  },
  matchControlsRow: {
    flexDirection: 'row',
    gap: 12
  },
  controlButton: {
    flex: 1
  },
  errorContainer: {
    backgroundColor: colors.red,
    padding: 12,
    margin: 16,
    borderRadius: 8
  },
  errorText: {
    color: colors.white,
    textAlign: 'center',
    fontSize: 14
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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