a4fb8a0173204060cf2b5dc7e7571333
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _Platform = _interopRequireDefault(require("../../../exports/Platform"));
var React = _interopRequireWildcard(require("react"));
var _VirtualizedSectionList = _interopRequireDefault(require("../VirtualizedSectionList"));
var _excluded = ["stickySectionHeadersEnabled"];
var SectionList = function (_React$PureComponent) {
  function SectionList() {
    var _this;
    (0, _classCallCheck2.default)(this, SectionList);
    _this = _callSuper(this, SectionList, arguments);
    _this._captureRef = function (ref) {
      _this._wrapperListRef = ref;
    };
    return _this;
  }
  (0, _inherits2.default)(SectionList, _React$PureComponent);
  return (0, _createClass2.default)(SectionList, [{
    key: "scrollToLocation",
    value: function scrollToLocation(params) {
      if (this._wrapperListRef != null) {
        this._wrapperListRef.scrollToLocation(params);
      }
    }
  }, {
    key: "recordInteraction",
    value: function recordInteraction() {
      var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();
      listRef && listRef.recordInteraction();
    }
  }, {
    key: "flashScrollIndicators",
    value: function flashScrollIndicators() {
      var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();
      listRef && listRef.flashScrollIndicators();
    }
  }, {
    key: "getScrollResponder",
    value: function getScrollResponder() {
      var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();
      if (listRef) {
        return listRef.getScrollResponder();
      }
    }
  }, {
    key: "getScrollableNode",
    value: function getScrollableNode() {
      var listRef = this._wrapperListRef && this._wrapperListRef.getListRef();
      if (listRef) {
        return listRef.getScrollableNode();
      }
    }
  }, {
    key: "render",
    value: function render() {
      var _this$props = this.props,
        _stickySectionHeadersEnabled = _this$props.stickySectionHeadersEnabled,
        restProps = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);
      var stickySectionHeadersEnabled = _stickySectionHeadersEnabled !== null && _stickySectionHeadersEnabled !== void 0 ? _stickySectionHeadersEnabled : _Platform.default.OS === 'ios';
      return React.createElement(_VirtualizedSectionList.default, (0, _extends2.default)({}, restProps, {
        stickySectionHeadersEnabled: stickySectionHeadersEnabled,
        ref: this._captureRef,
        getItemCount: function getItemCount(items) {
          return items.length;
        },
        getItem: function getItem(items, index) {
          return items[index];
        }
      }));
    }
  }]);
}(React.PureComponent);
exports.default = SectionList;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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