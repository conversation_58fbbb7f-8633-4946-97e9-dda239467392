923267167b1c1ad8c27d26c4cc16fbad
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_bv36vqhj0() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\database\\DatabaseService.ts";
  var hash = "ef932ed9f5f8d21c433cb1790facfd288b4ad1a6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\database\\DatabaseService.ts",
    statementMap: {
      "0": {
        start: {
          line: 167,
          column: 4
        },
        end: {
          line: 167,
          column: 52
        }
      },
      "1": {
        start: {
          line: 178,
          column: 4
        },
        end: {
          line: 198,
          column: 5
        }
      },
      "2": {
        start: {
          line: 179,
          column: 27
        },
        end: {
          line: 179,
          column: 75
        }
      },
      "3": {
        start: {
          line: 180,
          column: 6
        },
        end: {
          line: 182,
          column: 7
        }
      },
      "4": {
        start: {
          line: 181,
          column: 8
        },
        end: {
          line: 181,
          column: 61
        }
      },
      "5": {
        start: {
          line: 184,
          column: 30
        },
        end: {
          line: 189,
          column: 42
        }
      },
      "6": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 193,
          column: 7
        }
      },
      "7": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 192,
          column: 50
        }
      },
      "8": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 195,
          column: 34
        }
      },
      "9": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 101
        }
      },
      "10": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "11": {
        start: {
          line: 206,
          column: 30
        },
        end: {
          line: 210,
          column: 17
        }
      },
      "12": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 214,
          column: 7
        }
      },
      "13": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 213,
          column: 52
        }
      },
      "14": {
        start: {
          line: 216,
          column: 6
        },
        end: {
          line: 216,
          column: 22
        }
      },
      "15": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 101
        }
      },
      "16": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 248,
          column: 5
        }
      },
      "17": {
        start: {
          line: 227,
          column: 21
        },
        end: {
          line: 227,
          column: 59
        }
      },
      "18": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 230,
          column: 7
        }
      },
      "19": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 229,
          column: 63
        }
      },
      "20": {
        start: {
          line: 232,
          column: 30
        },
        end: {
          line: 239,
          column: 17
        }
      },
      "21": {
        start: {
          line: 241,
          column: 6
        },
        end: {
          line: 243,
          column: 7
        }
      },
      "22": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 242,
          column: 52
        }
      },
      "23": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 22
        }
      },
      "24": {
        start: {
          line: 247,
          column: 6
        },
        end: {
          line: 247,
          column: 102
        }
      },
      "25": {
        start: {
          line: 255,
          column: 4
        },
        end: {
          line: 270,
          column: 5
        }
      },
      "26": {
        start: {
          line: 256,
          column: 30
        },
        end: {
          line: 261,
          column: 17
        }
      },
      "27": {
        start: {
          line: 263,
          column: 6
        },
        end: {
          line: 265,
          column: 7
        }
      },
      "28": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 264,
          column: 52
        }
      },
      "29": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 22
        }
      },
      "30": {
        start: {
          line: 269,
          column: 6
        },
        end: {
          line: 269,
          column: 102
        }
      },
      "31": {
        start: {
          line: 277,
          column: 4
        },
        end: {
          line: 290,
          column: 5
        }
      },
      "32": {
        start: {
          line: 278,
          column: 24
        },
        end: {
          line: 281,
          column: 26
        }
      },
      "33": {
        start: {
          line: 283,
          column: 6
        },
        end: {
          line: 285,
          column: 7
        }
      },
      "34": {
        start: {
          line: 284,
          column: 8
        },
        end: {
          line: 284,
          column: 56
        }
      },
      "35": {
        start: {
          line: 287,
          column: 6
        },
        end: {
          line: 287,
          column: 31
        }
      },
      "36": {
        start: {
          line: 289,
          column: 6
        },
        end: {
          line: 289,
          column: 106
        }
      },
      "37": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 315,
          column: 5
        }
      },
      "38": {
        start: {
          line: 302,
          column: 30
        },
        end: {
          line: 306,
          column: 17
        }
      },
      "39": {
        start: {
          line: 308,
          column: 6
        },
        end: {
          line: 310,
          column: 7
        }
      },
      "40": {
        start: {
          line: 309,
          column: 8
        },
        end: {
          line: 309,
          column: 52
        }
      },
      "41": {
        start: {
          line: 312,
          column: 6
        },
        end: {
          line: 312,
          column: 22
        }
      },
      "42": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 314,
          column: 112
        }
      },
      "43": {
        start: {
          line: 322,
          column: 4
        },
        end: {
          line: 336,
          column: 5
        }
      },
      "44": {
        start: {
          line: 323,
          column: 30
        },
        end: {
          line: 327,
          column: 17
        }
      },
      "45": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 331,
          column: 7
        }
      },
      "46": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 330,
          column: 52
        }
      },
      "47": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 333,
          column: 22
        }
      },
      "48": {
        start: {
          line: 335,
          column: 6
        },
        end: {
          line: 335,
          column: 111
        }
      },
      "49": {
        start: {
          line: 347,
          column: 4
        },
        end: {
          line: 367,
          column: 5
        }
      },
      "50": {
        start: {
          line: 348,
          column: 27
        },
        end: {
          line: 348,
          column: 75
        }
      },
      "51": {
        start: {
          line: 349,
          column: 6
        },
        end: {
          line: 351,
          column: 7
        }
      },
      "52": {
        start: {
          line: 350,
          column: 8
        },
        end: {
          line: 350,
          column: 61
        }
      },
      "53": {
        start: {
          line: 353,
          column: 30
        },
        end: {
          line: 358,
          column: 42
        }
      },
      "54": {
        start: {
          line: 360,
          column: 6
        },
        end: {
          line: 362,
          column: 7
        }
      },
      "55": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 361,
          column: 50
        }
      },
      "56": {
        start: {
          line: 364,
          column: 6
        },
        end: {
          line: 364,
          column: 34
        }
      },
      "57": {
        start: {
          line: 366,
          column: 6
        },
        end: {
          line: 366,
          column: 111
        }
      },
      "58": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 396,
          column: 5
        }
      },
      "59": {
        start: {
          line: 375,
          column: 21
        },
        end: {
          line: 375,
          column: 59
        }
      },
      "60": {
        start: {
          line: 376,
          column: 6
        },
        end: {
          line: 378,
          column: 7
        }
      },
      "61": {
        start: {
          line: 377,
          column: 8
        },
        end: {
          line: 377,
          column: 63
        }
      },
      "62": {
        start: {
          line: 380,
          column: 30
        },
        end: {
          line: 387,
          column: 17
        }
      },
      "63": {
        start: {
          line: 389,
          column: 6
        },
        end: {
          line: 391,
          column: 7
        }
      },
      "64": {
        start: {
          line: 390,
          column: 8
        },
        end: {
          line: 390,
          column: 52
        }
      },
      "65": {
        start: {
          line: 393,
          column: 6
        },
        end: {
          line: 393,
          column: 22
        }
      },
      "66": {
        start: {
          line: 395,
          column: 6
        },
        end: {
          line: 395,
          column: 113
        }
      },
      "67": {
        start: {
          line: 407,
          column: 4
        },
        end: {
          line: 427,
          column: 5
        }
      },
      "68": {
        start: {
          line: 408,
          column: 27
        },
        end: {
          line: 408,
          column: 75
        }
      },
      "69": {
        start: {
          line: 409,
          column: 6
        },
        end: {
          line: 411,
          column: 7
        }
      },
      "70": {
        start: {
          line: 410,
          column: 8
        },
        end: {
          line: 410,
          column: 61
        }
      },
      "71": {
        start: {
          line: 413,
          column: 30
        },
        end: {
          line: 418,
          column: 42
        }
      },
      "72": {
        start: {
          line: 420,
          column: 6
        },
        end: {
          line: 422,
          column: 7
        }
      },
      "73": {
        start: {
          line: 421,
          column: 8
        },
        end: {
          line: 421,
          column: 50
        }
      },
      "74": {
        start: {
          line: 424,
          column: 6
        },
        end: {
          line: 424,
          column: 34
        }
      },
      "75": {
        start: {
          line: 426,
          column: 6
        },
        end: {
          line: 426,
          column: 108
        }
      },
      "76": {
        start: {
          line: 434,
          column: 4
        },
        end: {
          line: 456,
          column: 5
        }
      },
      "77": {
        start: {
          line: 435,
          column: 21
        },
        end: {
          line: 435,
          column: 59
        }
      },
      "78": {
        start: {
          line: 436,
          column: 6
        },
        end: {
          line: 438,
          column: 7
        }
      },
      "79": {
        start: {
          line: 437,
          column: 8
        },
        end: {
          line: 437,
          column: 63
        }
      },
      "80": {
        start: {
          line: 440,
          column: 30
        },
        end: {
          line: 447,
          column: 17
        }
      },
      "81": {
        start: {
          line: 449,
          column: 6
        },
        end: {
          line: 451,
          column: 7
        }
      },
      "82": {
        start: {
          line: 450,
          column: 8
        },
        end: {
          line: 450,
          column: 52
        }
      },
      "83": {
        start: {
          line: 453,
          column: 6
        },
        end: {
          line: 453,
          column: 22
        }
      },
      "84": {
        start: {
          line: 455,
          column: 6
        },
        end: {
          line: 455,
          column: 111
        }
      },
      "85": {
        start: {
          line: 463,
          column: 4
        },
        end: {
          line: 478,
          column: 5
        }
      },
      "86": {
        start: {
          line: 464,
          column: 30
        },
        end: {
          line: 469,
          column: 17
        }
      },
      "87": {
        start: {
          line: 471,
          column: 6
        },
        end: {
          line: 473,
          column: 7
        }
      },
      "88": {
        start: {
          line: 472,
          column: 8
        },
        end: {
          line: 472,
          column: 52
        }
      },
      "89": {
        start: {
          line: 475,
          column: 6
        },
        end: {
          line: 475,
          column: 22
        }
      },
      "90": {
        start: {
          line: 477,
          column: 6
        },
        end: {
          line: 477,
          column: 111
        }
      },
      "91": {
        start: {
          line: 489,
          column: 4
        },
        end: {
          line: 514,
          column: 5
        }
      },
      "92": {
        start: {
          line: 490,
          column: 18
        },
        end: {
          line: 493,
          column: 30
        }
      },
      "93": {
        start: {
          line: 495,
          column: 6
        },
        end: {
          line: 497,
          column: 7
        }
      },
      "94": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 496,
          column: 47
        }
      },
      "95": {
        start: {
          line: 499,
          column: 6
        },
        end: {
          line: 501,
          column: 7
        }
      },
      "96": {
        start: {
          line: 500,
          column: 8
        },
        end: {
          line: 500,
          column: 57
        }
      },
      "97": {
        start: {
          line: 503,
          column: 30
        },
        end: {
          line: 505,
          column: 42
        }
      },
      "98": {
        start: {
          line: 507,
          column: 6
        },
        end: {
          line: 509,
          column: 7
        }
      },
      "99": {
        start: {
          line: 508,
          column: 8
        },
        end: {
          line: 508,
          column: 50
        }
      },
      "100": {
        start: {
          line: 511,
          column: 6
        },
        end: {
          line: 511,
          column: 34
        }
      },
      "101": {
        start: {
          line: 513,
          column: 6
        },
        end: {
          line: 513,
          column: 100
        }
      },
      "102": {
        start: {
          line: 521,
          column: 4
        },
        end: {
          line: 535,
          column: 5
        }
      },
      "103": {
        start: {
          line: 522,
          column: 30
        },
        end: {
          line: 526,
          column: 17
        }
      },
      "104": {
        start: {
          line: 528,
          column: 6
        },
        end: {
          line: 530,
          column: 7
        }
      },
      "105": {
        start: {
          line: 529,
          column: 8
        },
        end: {
          line: 529,
          column: 52
        }
      },
      "106": {
        start: {
          line: 532,
          column: 6
        },
        end: {
          line: 532,
          column: 22
        }
      },
      "107": {
        start: {
          line: 534,
          column: 6
        },
        end: {
          line: 534,
          column: 101
        }
      },
      "108": {
        start: {
          line: 542,
          column: 4
        },
        end: {
          line: 562,
          column: 5
        }
      },
      "109": {
        start: {
          line: 543,
          column: 27
        },
        end: {
          line: 543,
          column: 75
        }
      },
      "110": {
        start: {
          line: 544,
          column: 6
        },
        end: {
          line: 546,
          column: 7
        }
      },
      "111": {
        start: {
          line: 545,
          column: 8
        },
        end: {
          line: 545,
          column: 63
        }
      },
      "112": {
        start: {
          line: 548,
          column: 30
        },
        end: {
          line: 553,
          column: 17
        }
      },
      "113": {
        start: {
          line: 555,
          column: 6
        },
        end: {
          line: 557,
          column: 7
        }
      },
      "114": {
        start: {
          line: 556,
          column: 8
        },
        end: {
          line: 556,
          column: 52
        }
      },
      "115": {
        start: {
          line: 559,
          column: 6
        },
        end: {
          line: 559,
          column: 22
        }
      },
      "116": {
        start: {
          line: 561,
          column: 6
        },
        end: {
          line: 561,
          column: 110
        }
      },
      "117": {
        start: {
          line: 569,
          column: 4
        },
        end: {
          line: 592,
          column: 5
        }
      },
      "118": {
        start: {
          line: 570,
          column: 21
        },
        end: {
          line: 570,
          column: 59
        }
      },
      "119": {
        start: {
          line: 571,
          column: 6
        },
        end: {
          line: 573,
          column: 7
        }
      },
      "120": {
        start: {
          line: 572,
          column: 8
        },
        end: {
          line: 572,
          column: 63
        }
      },
      "121": {
        start: {
          line: 575,
          column: 30
        },
        end: {
          line: 583,
          column: 17
        }
      },
      "122": {
        start: {
          line: 585,
          column: 6
        },
        end: {
          line: 587,
          column: 7
        }
      },
      "123": {
        start: {
          line: 586,
          column: 8
        },
        end: {
          line: 586,
          column: 52
        }
      },
      "124": {
        start: {
          line: 589,
          column: 6
        },
        end: {
          line: 589,
          column: 22
        }
      },
      "125": {
        start: {
          line: 591,
          column: 6
        },
        end: {
          line: 591,
          column: 111
        }
      },
      "126": {
        start: {
          line: 603,
          column: 4
        },
        end: {
          line: 634,
          column: 5
        }
      },
      "127": {
        start: {
          line: 604,
          column: 27
        },
        end: {
          line: 604,
          column: 75
        }
      },
      "128": {
        start: {
          line: 605,
          column: 6
        },
        end: {
          line: 607,
          column: 7
        }
      },
      "129": {
        start: {
          line: 606,
          column: 8
        },
        end: {
          line: 606,
          column: 63
        }
      },
      "130": {
        start: {
          line: 610,
          column: 32
        },
        end: {
          line: 610,
          column: 73
        }
      },
      "131": {
        start: {
          line: 611,
          column: 27
        },
        end: {
          line: 611,
          column: 41
        }
      },
      "132": {
        start: {
          line: 612,
          column: 19
        },
        end: {
          line: 612,
          column: 65
        }
      },
      "133": {
        start: {
          line: 612,
          column: 39
        },
        end: {
          line: 612,
          column: 57
        }
      },
      "134": {
        start: {
          line: 613,
          column: 28
        },
        end: {
          line: 613,
          column: 78
        }
      },
      "135": {
        start: {
          line: 616,
          column: 33
        },
        end: {
          line: 616,
          column: 83
        }
      },
      "136": {
        start: {
          line: 617,
          column: 36
        },
        end: {
          line: 617,
          column: 51
        }
      },
      "137": {
        start: {
          line: 618,
          column: 33
        },
        end: {
          line: 618,
          column: 96
        }
      },
      "138": {
        start: {
          line: 618,
          column: 61
        },
        end: {
          line: 618,
          column: 92
        }
      },
      "139": {
        start: {
          line: 620,
          column: 6
        },
        end: {
          line: 631,
          column: 8
        }
      },
      "140": {
        start: {
          line: 633,
          column: 6
        },
        end: {
          line: 633,
          column: 111
        }
      },
      "141": {
        start: {
          line: 639,
          column: 31
        },
        end: {
          line: 639,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 166,
            column: 2
          },
          end: {
            line: 166,
            column: 3
          }
        },
        loc: {
          start: {
            line: 166,
            column: 16
          },
          end: {
            line: 168,
            column: 3
          }
        },
        line: 166
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 177,
            column: 2
          },
          end: {
            line: 177,
            column: 3
          }
        },
        loc: {
          start: {
            line: 177,
            column: 104
          },
          end: {
            line: 199,
            column: 3
          }
        },
        line: 177
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 204,
            column: 2
          },
          end: {
            line: 204,
            column: 3
          }
        },
        loc: {
          start: {
            line: 204,
            column: 83
          },
          end: {
            line: 220,
            column: 3
          }
        },
        line: 204
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 225,
            column: 2
          },
          end: {
            line: 225,
            column: 3
          }
        },
        loc: {
          start: {
            line: 225,
            column: 141
          },
          end: {
            line: 249,
            column: 3
          }
        },
        line: 225
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 254,
            column: 2
          },
          end: {
            line: 254,
            column: 3
          }
        },
        loc: {
          start: {
            line: 254,
            column: 111
          },
          end: {
            line: 271,
            column: 3
          }
        },
        line: 254
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 276,
            column: 2
          },
          end: {
            line: 276,
            column: 3
          }
        },
        loc: {
          start: {
            line: 276,
            column: 84
          },
          end: {
            line: 291,
            column: 3
          }
        },
        line: 276
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 300,
            column: 2
          },
          end: {
            line: 300,
            column: 3
          }
        },
        loc: {
          start: {
            line: 300,
            column: 103
          },
          end: {
            line: 316,
            column: 3
          }
        },
        line: 300
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 321,
            column: 2
          },
          end: {
            line: 321,
            column: 3
          }
        },
        loc: {
          start: {
            line: 321,
            column: 144
          },
          end: {
            line: 337,
            column: 3
          }
        },
        line: 321
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 346,
            column: 2
          },
          end: {
            line: 346,
            column: 3
          }
        },
        loc: {
          start: {
            line: 346,
            column: 123
          },
          end: {
            line: 368,
            column: 3
          }
        },
        line: 346
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 373,
            column: 173
          },
          end: {
            line: 397,
            column: 3
          }
        },
        line: 373
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 406,
            column: 2
          },
          end: {
            line: 406,
            column: 3
          }
        },
        loc: {
          start: {
            line: 406,
            column: 118
          },
          end: {
            line: 428,
            column: 3
          }
        },
        line: 406
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 433,
            column: 2
          },
          end: {
            line: 433,
            column: 3
          }
        },
        loc: {
          start: {
            line: 433,
            column: 168
          },
          end: {
            line: 457,
            column: 3
          }
        },
        line: 433
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 462,
            column: 2
          },
          end: {
            line: 462,
            column: 3
          }
        },
        loc: {
          start: {
            line: 462,
            column: 138
          },
          end: {
            line: 479,
            column: 3
          }
        },
        line: 462
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 488,
            column: 2
          },
          end: {
            line: 488,
            column: 3
          }
        },
        loc: {
          start: {
            line: 488,
            column: 126
          },
          end: {
            line: 515,
            column: 3
          }
        },
        line: 488
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 520,
            column: 2
          },
          end: {
            line: 520,
            column: 3
          }
        },
        loc: {
          start: {
            line: 520,
            column: 83
          },
          end: {
            line: 536,
            column: 3
          }
        },
        line: 520
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 541,
            column: 2
          },
          end: {
            line: 541,
            column: 3
          }
        },
        loc: {
          start: {
            line: 541,
            column: 124
          },
          end: {
            line: 563,
            column: 3
          }
        },
        line: 541
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 568,
            column: 2
          },
          end: {
            line: 568,
            column: 3
          }
        },
        loc: {
          start: {
            line: 568,
            column: 148
          },
          end: {
            line: 593,
            column: 3
          }
        },
        line: 568
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 602,
            column: 2
          },
          end: {
            line: 602,
            column: 3
          }
        },
        loc: {
          start: {
            line: 602,
            column: 85
          },
          end: {
            line: 635,
            column: 3
          }
        },
        line: 602
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 612,
            column: 34
          },
          end: {
            line: 612,
            column: 35
          }
        },
        loc: {
          start: {
            line: 612,
            column: 39
          },
          end: {
            line: 612,
            column: 57
          }
        },
        line: 612
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 618,
            column: 49
          },
          end: {
            line: 618,
            column: 50
          }
        },
        loc: {
          start: {
            line: 618,
            column: 61
          },
          end: {
            line: 618,
            column: 92
          }
        },
        line: 618
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 177,
            column: 36
          },
          end: {
            line: 177,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 177,
            column: 44
          },
          end: {
            line: 177,
            column: 46
          }
        }],
        line: 177
      },
      "1": {
        loc: {
          start: {
            line: 177,
            column: 48
          },
          end: {
            line: 177,
            column: 58
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 177,
            column: 57
          },
          end: {
            line: 177,
            column: 58
          }
        }],
        line: 177
      },
      "2": {
        loc: {
          start: {
            line: 179,
            column: 27
          },
          end: {
            line: 179,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 27
          },
          end: {
            line: 179,
            column: 33
          }
        }, {
          start: {
            line: 179,
            column: 37
          },
          end: {
            line: 179,
            column: 75
          }
        }],
        line: 179
      },
      "3": {
        loc: {
          start: {
            line: 180,
            column: 6
          },
          end: {
            line: 182,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 180,
            column: 6
          },
          end: {
            line: 182,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 180
      },
      "4": {
        loc: {
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 193,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 193,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 191
      },
      "5": {
        loc: {
          start: {
            line: 195,
            column: 21
          },
          end: {
            line: 195,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 195,
            column: 21
          },
          end: {
            line: 195,
            column: 25
          }
        }, {
          start: {
            line: 195,
            column: 29
          },
          end: {
            line: 195,
            column: 31
          }
        }],
        line: 195
      },
      "6": {
        loc: {
          start: {
            line: 197,
            column: 32
          },
          end: {
            line: 197,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 197,
            column: 57
          },
          end: {
            line: 197,
            column: 70
          }
        }, {
          start: {
            line: 197,
            column: 73
          },
          end: {
            line: 197,
            column: 98
          }
        }],
        line: 197
      },
      "7": {
        loc: {
          start: {
            line: 212,
            column: 6
          },
          end: {
            line: 214,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 212,
            column: 6
          },
          end: {
            line: 214,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 212
      },
      "8": {
        loc: {
          start: {
            line: 218,
            column: 34
          },
          end: {
            line: 218,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 218,
            column: 59
          },
          end: {
            line: 218,
            column: 72
          }
        }, {
          start: {
            line: 218,
            column: 75
          },
          end: {
            line: 218,
            column: 98
          }
        }],
        line: 218
      },
      "9": {
        loc: {
          start: {
            line: 228,
            column: 6
          },
          end: {
            line: 230,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 6
          },
          end: {
            line: 230,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "10": {
        loc: {
          start: {
            line: 241,
            column: 6
          },
          end: {
            line: 243,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 6
          },
          end: {
            line: 243,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "11": {
        loc: {
          start: {
            line: 247,
            column: 34
          },
          end: {
            line: 247,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 247,
            column: 59
          },
          end: {
            line: 247,
            column: 72
          }
        }, {
          start: {
            line: 247,
            column: 75
          },
          end: {
            line: 247,
            column: 99
          }
        }],
        line: 247
      },
      "12": {
        loc: {
          start: {
            line: 263,
            column: 6
          },
          end: {
            line: 265,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 6
          },
          end: {
            line: 265,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "13": {
        loc: {
          start: {
            line: 269,
            column: 34
          },
          end: {
            line: 269,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 269,
            column: 59
          },
          end: {
            line: 269,
            column: 72
          }
        }, {
          start: {
            line: 269,
            column: 75
          },
          end: {
            line: 269,
            column: 99
          }
        }],
        line: 269
      },
      "14": {
        loc: {
          start: {
            line: 283,
            column: 6
          },
          end: {
            line: 285,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 283,
            column: 6
          },
          end: {
            line: 285,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 283
      },
      "15": {
        loc: {
          start: {
            line: 289,
            column: 38
          },
          end: {
            line: 289,
            column: 103
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 289,
            column: 63
          },
          end: {
            line: 289,
            column: 76
          }
        }, {
          start: {
            line: 289,
            column: 79
          },
          end: {
            line: 289,
            column: 103
          }
        }],
        line: 289
      },
      "16": {
        loc: {
          start: {
            line: 308,
            column: 6
          },
          end: {
            line: 310,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 308,
            column: 6
          },
          end: {
            line: 310,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 308
      },
      "17": {
        loc: {
          start: {
            line: 308,
            column: 10
          },
          end: {
            line: 308,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 308,
            column: 10
          },
          end: {
            line: 308,
            column: 15
          }
        }, {
          start: {
            line: 308,
            column: 19
          },
          end: {
            line: 308,
            column: 44
          }
        }],
        line: 308
      },
      "18": {
        loc: {
          start: {
            line: 314,
            column: 34
          },
          end: {
            line: 314,
            column: 109
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 314,
            column: 59
          },
          end: {
            line: 314,
            column: 72
          }
        }, {
          start: {
            line: 314,
            column: 75
          },
          end: {
            line: 314,
            column: 109
          }
        }],
        line: 314
      },
      "19": {
        loc: {
          start: {
            line: 329,
            column: 6
          },
          end: {
            line: 331,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 329,
            column: 6
          },
          end: {
            line: 331,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 329
      },
      "20": {
        loc: {
          start: {
            line: 335,
            column: 34
          },
          end: {
            line: 335,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 335,
            column: 59
          },
          end: {
            line: 335,
            column: 72
          }
        }, {
          start: {
            line: 335,
            column: 75
          },
          end: {
            line: 335,
            column: 108
          }
        }],
        line: 335
      },
      "21": {
        loc: {
          start: {
            line: 346,
            column: 45
          },
          end: {
            line: 346,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 346,
            column: 53
          },
          end: {
            line: 346,
            column: 55
          }
        }],
        line: 346
      },
      "22": {
        loc: {
          start: {
            line: 346,
            column: 57
          },
          end: {
            line: 346,
            column: 67
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 346,
            column: 66
          },
          end: {
            line: 346,
            column: 67
          }
        }],
        line: 346
      },
      "23": {
        loc: {
          start: {
            line: 348,
            column: 27
          },
          end: {
            line: 348,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 348,
            column: 27
          },
          end: {
            line: 348,
            column: 33
          }
        }, {
          start: {
            line: 348,
            column: 37
          },
          end: {
            line: 348,
            column: 75
          }
        }],
        line: 348
      },
      "24": {
        loc: {
          start: {
            line: 349,
            column: 6
          },
          end: {
            line: 351,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 349,
            column: 6
          },
          end: {
            line: 351,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 349
      },
      "25": {
        loc: {
          start: {
            line: 360,
            column: 6
          },
          end: {
            line: 362,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 360,
            column: 6
          },
          end: {
            line: 362,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 360
      },
      "26": {
        loc: {
          start: {
            line: 364,
            column: 21
          },
          end: {
            line: 364,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 364,
            column: 21
          },
          end: {
            line: 364,
            column: 25
          }
        }, {
          start: {
            line: 364,
            column: 29
          },
          end: {
            line: 364,
            column: 31
          }
        }],
        line: 364
      },
      "27": {
        loc: {
          start: {
            line: 366,
            column: 32
          },
          end: {
            line: 366,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 366,
            column: 57
          },
          end: {
            line: 366,
            column: 70
          }
        }, {
          start: {
            line: 366,
            column: 73
          },
          end: {
            line: 366,
            column: 108
          }
        }],
        line: 366
      },
      "28": {
        loc: {
          start: {
            line: 376,
            column: 6
          },
          end: {
            line: 378,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 376,
            column: 6
          },
          end: {
            line: 378,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 376
      },
      "29": {
        loc: {
          start: {
            line: 389,
            column: 6
          },
          end: {
            line: 391,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 389,
            column: 6
          },
          end: {
            line: 391,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 389
      },
      "30": {
        loc: {
          start: {
            line: 395,
            column: 34
          },
          end: {
            line: 395,
            column: 110
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 395,
            column: 59
          },
          end: {
            line: 395,
            column: 72
          }
        }, {
          start: {
            line: 395,
            column: 75
          },
          end: {
            line: 395,
            column: 110
          }
        }],
        line: 395
      },
      "31": {
        loc: {
          start: {
            line: 406,
            column: 42
          },
          end: {
            line: 406,
            column: 52
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 406,
            column: 50
          },
          end: {
            line: 406,
            column: 52
          }
        }],
        line: 406
      },
      "32": {
        loc: {
          start: {
            line: 406,
            column: 54
          },
          end: {
            line: 406,
            column: 64
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 406,
            column: 63
          },
          end: {
            line: 406,
            column: 64
          }
        }],
        line: 406
      },
      "33": {
        loc: {
          start: {
            line: 408,
            column: 27
          },
          end: {
            line: 408,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 408,
            column: 27
          },
          end: {
            line: 408,
            column: 33
          }
        }, {
          start: {
            line: 408,
            column: 37
          },
          end: {
            line: 408,
            column: 75
          }
        }],
        line: 408
      },
      "34": {
        loc: {
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 411,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 411,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 409
      },
      "35": {
        loc: {
          start: {
            line: 420,
            column: 6
          },
          end: {
            line: 422,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 420,
            column: 6
          },
          end: {
            line: 422,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 420
      },
      "36": {
        loc: {
          start: {
            line: 424,
            column: 21
          },
          end: {
            line: 424,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 424,
            column: 21
          },
          end: {
            line: 424,
            column: 25
          }
        }, {
          start: {
            line: 424,
            column: 29
          },
          end: {
            line: 424,
            column: 31
          }
        }],
        line: 424
      },
      "37": {
        loc: {
          start: {
            line: 426,
            column: 32
          },
          end: {
            line: 426,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 426,
            column: 57
          },
          end: {
            line: 426,
            column: 70
          }
        }, {
          start: {
            line: 426,
            column: 73
          },
          end: {
            line: 426,
            column: 105
          }
        }],
        line: 426
      },
      "38": {
        loc: {
          start: {
            line: 436,
            column: 6
          },
          end: {
            line: 438,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 436,
            column: 6
          },
          end: {
            line: 438,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 436
      },
      "39": {
        loc: {
          start: {
            line: 449,
            column: 6
          },
          end: {
            line: 451,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 449,
            column: 6
          },
          end: {
            line: 451,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 449
      },
      "40": {
        loc: {
          start: {
            line: 455,
            column: 34
          },
          end: {
            line: 455,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 455,
            column: 59
          },
          end: {
            line: 455,
            column: 72
          }
        }, {
          start: {
            line: 455,
            column: 75
          },
          end: {
            line: 455,
            column: 108
          }
        }],
        line: 455
      },
      "41": {
        loc: {
          start: {
            line: 471,
            column: 6
          },
          end: {
            line: 473,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 471,
            column: 6
          },
          end: {
            line: 473,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 471
      },
      "42": {
        loc: {
          start: {
            line: 477,
            column: 34
          },
          end: {
            line: 477,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 477,
            column: 59
          },
          end: {
            line: 477,
            column: 72
          }
        }, {
          start: {
            line: 477,
            column: 75
          },
          end: {
            line: 477,
            column: 108
          }
        }],
        line: 477
      },
      "43": {
        loc: {
          start: {
            line: 488,
            column: 58
          },
          end: {
            line: 488,
            column: 68
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 488,
            column: 66
          },
          end: {
            line: 488,
            column: 68
          }
        }],
        line: 488
      },
      "44": {
        loc: {
          start: {
            line: 488,
            column: 70
          },
          end: {
            line: 488,
            column: 80
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 488,
            column: 79
          },
          end: {
            line: 488,
            column: 80
          }
        }],
        line: 488
      },
      "45": {
        loc: {
          start: {
            line: 495,
            column: 6
          },
          end: {
            line: 497,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 495,
            column: 6
          },
          end: {
            line: 497,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 495
      },
      "46": {
        loc: {
          start: {
            line: 499,
            column: 6
          },
          end: {
            line: 501,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 499,
            column: 6
          },
          end: {
            line: 501,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 499
      },
      "47": {
        loc: {
          start: {
            line: 507,
            column: 6
          },
          end: {
            line: 509,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 507,
            column: 6
          },
          end: {
            line: 509,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 507
      },
      "48": {
        loc: {
          start: {
            line: 511,
            column: 21
          },
          end: {
            line: 511,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 511,
            column: 21
          },
          end: {
            line: 511,
            column: 25
          }
        }, {
          start: {
            line: 511,
            column: 29
          },
          end: {
            line: 511,
            column: 31
          }
        }],
        line: 511
      },
      "49": {
        loc: {
          start: {
            line: 513,
            column: 32
          },
          end: {
            line: 513,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 513,
            column: 57
          },
          end: {
            line: 513,
            column: 70
          }
        }, {
          start: {
            line: 513,
            column: 73
          },
          end: {
            line: 513,
            column: 97
          }
        }],
        line: 513
      },
      "50": {
        loc: {
          start: {
            line: 528,
            column: 6
          },
          end: {
            line: 530,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 528,
            column: 6
          },
          end: {
            line: 530,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 528
      },
      "51": {
        loc: {
          start: {
            line: 534,
            column: 34
          },
          end: {
            line: 534,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 534,
            column: 59
          },
          end: {
            line: 534,
            column: 72
          }
        }, {
          start: {
            line: 534,
            column: 75
          },
          end: {
            line: 534,
            column: 98
          }
        }],
        line: 534
      },
      "52": {
        loc: {
          start: {
            line: 543,
            column: 27
          },
          end: {
            line: 543,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 543,
            column: 27
          },
          end: {
            line: 543,
            column: 33
          }
        }, {
          start: {
            line: 543,
            column: 37
          },
          end: {
            line: 543,
            column: 75
          }
        }],
        line: 543
      },
      "53": {
        loc: {
          start: {
            line: 544,
            column: 6
          },
          end: {
            line: 546,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 544,
            column: 6
          },
          end: {
            line: 546,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 544
      },
      "54": {
        loc: {
          start: {
            line: 555,
            column: 6
          },
          end: {
            line: 557,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 555,
            column: 6
          },
          end: {
            line: 557,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 555
      },
      "55": {
        loc: {
          start: {
            line: 555,
            column: 10
          },
          end: {
            line: 555,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 555,
            column: 10
          },
          end: {
            line: 555,
            column: 15
          }
        }, {
          start: {
            line: 555,
            column: 19
          },
          end: {
            line: 555,
            column: 44
          }
        }],
        line: 555
      },
      "56": {
        loc: {
          start: {
            line: 561,
            column: 34
          },
          end: {
            line: 561,
            column: 107
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 561,
            column: 59
          },
          end: {
            line: 561,
            column: 72
          }
        }, {
          start: {
            line: 561,
            column: 75
          },
          end: {
            line: 561,
            column: 107
          }
        }],
        line: 561
      },
      "57": {
        loc: {
          start: {
            line: 571,
            column: 6
          },
          end: {
            line: 573,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 571,
            column: 6
          },
          end: {
            line: 573,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 571
      },
      "58": {
        loc: {
          start: {
            line: 585,
            column: 6
          },
          end: {
            line: 587,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 585,
            column: 6
          },
          end: {
            line: 587,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 585
      },
      "59": {
        loc: {
          start: {
            line: 591,
            column: 34
          },
          end: {
            line: 591,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 591,
            column: 59
          },
          end: {
            line: 591,
            column: 72
          }
        }, {
          start: {
            line: 591,
            column: 75
          },
          end: {
            line: 591,
            column: 108
          }
        }],
        line: 591
      },
      "60": {
        loc: {
          start: {
            line: 604,
            column: 27
          },
          end: {
            line: 604,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 604,
            column: 27
          },
          end: {
            line: 604,
            column: 33
          }
        }, {
          start: {
            line: 604,
            column: 37
          },
          end: {
            line: 604,
            column: 75
          }
        }],
        line: 604
      },
      "61": {
        loc: {
          start: {
            line: 605,
            column: 6
          },
          end: {
            line: 607,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 605,
            column: 6
          },
          end: {
            line: 607,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 605
      },
      "62": {
        loc: {
          start: {
            line: 613,
            column: 28
          },
          end: {
            line: 613,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 613,
            column: 47
          },
          end: {
            line: 613,
            column: 74
          }
        }, {
          start: {
            line: 613,
            column: 77
          },
          end: {
            line: 613,
            column: 78
          }
        }],
        line: 613
      },
      "63": {
        loc: {
          start: {
            line: 633,
            column: 34
          },
          end: {
            line: 633,
            column: 108
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 633,
            column: 59
          },
          end: {
            line: 633,
            column: 72
          }
        }, {
          start: {
            line: 633,
            column: 75
          },
          end: {
            line: 633,
            column: 108
          }
        }],
        line: 633
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0],
      "22": [0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0],
      "32": [0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0],
      "44": [0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ef932ed9f5f8d21c433cb1790facfd288b4ad1a6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_bv36vqhj0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_bv36vqhj0();
import { authService } from "../auth/AuthService";
var DatabaseService = function () {
  function DatabaseService() {
    _classCallCheck(this, DatabaseService);
    cov_bv36vqhj0().f[0]++;
    cov_bv36vqhj0().s[0]++;
    this.supabase = authService.getSupabaseClient();
  }
  return _createClass(DatabaseService, [{
    key: "getMatches",
    value: function () {
      var _getMatches = _asyncToGenerator(function* (userId) {
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_bv36vqhj0().b[0][0]++, 50);
        var offset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_bv36vqhj0().b[1][0]++, 0);
        cov_bv36vqhj0().f[1]++;
        cov_bv36vqhj0().s[1]++;
        try {
          var _authService$getCurre;
          var targetUserId = (cov_bv36vqhj0().s[2]++, (cov_bv36vqhj0().b[2][0]++, userId) || (cov_bv36vqhj0().b[2][1]++, (_authService$getCurre = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre.id));
          cov_bv36vqhj0().s[3]++;
          if (!targetUserId) {
            cov_bv36vqhj0().b[3][0]++;
            cov_bv36vqhj0().s[4]++;
            return {
              data: [],
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[3][1]++;
          }
          var _ref = (cov_bv36vqhj0().s[5]++, yield this.supabase.from('matches').select('*').eq('user_id', targetUserId).order('match_date', {
              ascending: false
            }).range(offset, offset + limit - 1)),
            data = _ref.data,
            error = _ref.error;
          cov_bv36vqhj0().s[6]++;
          if (error) {
            cov_bv36vqhj0().b[4][0]++;
            cov_bv36vqhj0().s[7]++;
            return {
              data: [],
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[4][1]++;
          }
          cov_bv36vqhj0().s[8]++;
          return {
            data: (cov_bv36vqhj0().b[5][0]++, data) || (cov_bv36vqhj0().b[5][1]++, [])
          };
        } catch (error) {
          cov_bv36vqhj0().s[9]++;
          return {
            data: [],
            error: error instanceof Error ? (cov_bv36vqhj0().b[6][0]++, error.message) : (cov_bv36vqhj0().b[6][1]++, 'Failed to fetch matches')
          };
        }
      });
      function getMatches(_x) {
        return _getMatches.apply(this, arguments);
      }
      return getMatches;
    }()
  }, {
    key: "getMatch",
    value: (function () {
      var _getMatch = _asyncToGenerator(function* (matchId) {
        cov_bv36vqhj0().f[2]++;
        cov_bv36vqhj0().s[10]++;
        try {
          var _ref2 = (cov_bv36vqhj0().s[11]++, yield this.supabase.from('matches').select('*').eq('id', matchId).single()),
            data = _ref2.data,
            error = _ref2.error;
          cov_bv36vqhj0().s[12]++;
          if (error) {
            cov_bv36vqhj0().b[7][0]++;
            cov_bv36vqhj0().s[13]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[7][1]++;
          }
          cov_bv36vqhj0().s[14]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[15]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[8][0]++, error.message) : (cov_bv36vqhj0().b[8][1]++, 'Failed to fetch match')
          };
        }
      });
      function getMatch(_x2) {
        return _getMatch.apply(this, arguments);
      }
      return getMatch;
    }())
  }, {
    key: "createMatch",
    value: (function () {
      var _createMatch = _asyncToGenerator(function* (matchData) {
        cov_bv36vqhj0().f[3]++;
        cov_bv36vqhj0().s[16]++;
        try {
          var _authService$getCurre2;
          var userId = (cov_bv36vqhj0().s[17]++, (_authService$getCurre2 = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre2.id);
          cov_bv36vqhj0().s[18]++;
          if (!userId) {
            cov_bv36vqhj0().b[9][0]++;
            cov_bv36vqhj0().s[19]++;
            return {
              data: null,
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[9][1]++;
          }
          var _ref3 = (cov_bv36vqhj0().s[20]++, yield this.supabase.from('matches').insert(Object.assign({}, matchData, {
              user_id: userId
            })).select().single()),
            data = _ref3.data,
            error = _ref3.error;
          cov_bv36vqhj0().s[21]++;
          if (error) {
            cov_bv36vqhj0().b[10][0]++;
            cov_bv36vqhj0().s[22]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[10][1]++;
          }
          cov_bv36vqhj0().s[23]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[24]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[11][0]++, error.message) : (cov_bv36vqhj0().b[11][1]++, 'Failed to create match')
          };
        }
      });
      function createMatch(_x3) {
        return _createMatch.apply(this, arguments);
      }
      return createMatch;
    }())
  }, {
    key: "updateMatch",
    value: (function () {
      var _updateMatch = _asyncToGenerator(function* (matchId, updates) {
        cov_bv36vqhj0().f[4]++;
        cov_bv36vqhj0().s[25]++;
        try {
          var _ref4 = (cov_bv36vqhj0().s[26]++, yield this.supabase.from('matches').update(updates).eq('id', matchId).select().single()),
            data = _ref4.data,
            error = _ref4.error;
          cov_bv36vqhj0().s[27]++;
          if (error) {
            cov_bv36vqhj0().b[12][0]++;
            cov_bv36vqhj0().s[28]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[12][1]++;
          }
          cov_bv36vqhj0().s[29]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[30]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[13][0]++, error.message) : (cov_bv36vqhj0().b[13][1]++, 'Failed to update match')
          };
        }
      });
      function updateMatch(_x4, _x5) {
        return _updateMatch.apply(this, arguments);
      }
      return updateMatch;
    }())
  }, {
    key: "deleteMatch",
    value: (function () {
      var _deleteMatch = _asyncToGenerator(function* (matchId) {
        cov_bv36vqhj0().f[5]++;
        cov_bv36vqhj0().s[31]++;
        try {
          var _ref5 = (cov_bv36vqhj0().s[32]++, yield this.supabase.from('matches').delete().eq('id', matchId)),
            error = _ref5.error;
          cov_bv36vqhj0().s[33]++;
          if (error) {
            cov_bv36vqhj0().b[14][0]++;
            cov_bv36vqhj0().s[34]++;
            return {
              success: false,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[14][1]++;
          }
          cov_bv36vqhj0().s[35]++;
          return {
            success: true
          };
        } catch (error) {
          cov_bv36vqhj0().s[36]++;
          return {
            success: false,
            error: error instanceof Error ? (cov_bv36vqhj0().b[15][0]++, error.message) : (cov_bv36vqhj0().b[15][1]++, 'Failed to delete match')
          };
        }
      });
      function deleteMatch(_x6) {
        return _deleteMatch.apply(this, arguments);
      }
      return deleteMatch;
    }())
  }, {
    key: "getMatchStatistics",
    value: function () {
      var _getMatchStatistics = _asyncToGenerator(function* (matchId) {
        cov_bv36vqhj0().f[6]++;
        cov_bv36vqhj0().s[37]++;
        try {
          var _ref6 = (cov_bv36vqhj0().s[38]++, yield this.supabase.from('match_statistics').select('*').eq('match_id', matchId).single()),
            data = _ref6.data,
            error = _ref6.error;
          cov_bv36vqhj0().s[39]++;
          if ((cov_bv36vqhj0().b[17][0]++, error) && (cov_bv36vqhj0().b[17][1]++, error.code !== 'PGRST116')) {
            cov_bv36vqhj0().b[16][0]++;
            cov_bv36vqhj0().s[40]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[16][1]++;
          }
          cov_bv36vqhj0().s[41]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[42]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[18][0]++, error.message) : (cov_bv36vqhj0().b[18][1]++, 'Failed to fetch match statistics')
          };
        }
      });
      function getMatchStatistics(_x7) {
        return _getMatchStatistics.apply(this, arguments);
      }
      return getMatchStatistics;
    }()
  }, {
    key: "upsertMatchStatistics",
    value: (function () {
      var _upsertMatchStatistics = _asyncToGenerator(function* (statsData) {
        cov_bv36vqhj0().f[7]++;
        cov_bv36vqhj0().s[43]++;
        try {
          var _ref7 = (cov_bv36vqhj0().s[44]++, yield this.supabase.from('match_statistics').upsert(statsData, {
              onConflict: 'match_id,user_id'
            }).select().single()),
            data = _ref7.data,
            error = _ref7.error;
          cov_bv36vqhj0().s[45]++;
          if (error) {
            cov_bv36vqhj0().b[19][0]++;
            cov_bv36vqhj0().s[46]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[19][1]++;
          }
          cov_bv36vqhj0().s[47]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[48]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[20][0]++, error.message) : (cov_bv36vqhj0().b[20][1]++, 'Failed to save match statistics')
          };
        }
      });
      function upsertMatchStatistics(_x8) {
        return _upsertMatchStatistics.apply(this, arguments);
      }
      return upsertMatchStatistics;
    }())
  }, {
    key: "getTrainingSessions",
    value: function () {
      var _getTrainingSessions = _asyncToGenerator(function* (userId) {
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_bv36vqhj0().b[21][0]++, 50);
        var offset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_bv36vqhj0().b[22][0]++, 0);
        cov_bv36vqhj0().f[8]++;
        cov_bv36vqhj0().s[49]++;
        try {
          var _authService$getCurre3;
          var targetUserId = (cov_bv36vqhj0().s[50]++, (cov_bv36vqhj0().b[23][0]++, userId) || (cov_bv36vqhj0().b[23][1]++, (_authService$getCurre3 = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre3.id));
          cov_bv36vqhj0().s[51]++;
          if (!targetUserId) {
            cov_bv36vqhj0().b[24][0]++;
            cov_bv36vqhj0().s[52]++;
            return {
              data: [],
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[24][1]++;
          }
          var _ref8 = (cov_bv36vqhj0().s[53]++, yield this.supabase.from('training_sessions').select('*').eq('user_id', targetUserId).order('session_date', {
              ascending: false
            }).range(offset, offset + limit - 1)),
            data = _ref8.data,
            error = _ref8.error;
          cov_bv36vqhj0().s[54]++;
          if (error) {
            cov_bv36vqhj0().b[25][0]++;
            cov_bv36vqhj0().s[55]++;
            return {
              data: [],
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[25][1]++;
          }
          cov_bv36vqhj0().s[56]++;
          return {
            data: (cov_bv36vqhj0().b[26][0]++, data) || (cov_bv36vqhj0().b[26][1]++, [])
          };
        } catch (error) {
          cov_bv36vqhj0().s[57]++;
          return {
            data: [],
            error: error instanceof Error ? (cov_bv36vqhj0().b[27][0]++, error.message) : (cov_bv36vqhj0().b[27][1]++, 'Failed to fetch training sessions')
          };
        }
      });
      function getTrainingSessions(_x9) {
        return _getTrainingSessions.apply(this, arguments);
      }
      return getTrainingSessions;
    }()
  }, {
    key: "createTrainingSession",
    value: (function () {
      var _createTrainingSession = _asyncToGenerator(function* (sessionData) {
        cov_bv36vqhj0().f[9]++;
        cov_bv36vqhj0().s[58]++;
        try {
          var _authService$getCurre4;
          var userId = (cov_bv36vqhj0().s[59]++, (_authService$getCurre4 = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre4.id);
          cov_bv36vqhj0().s[60]++;
          if (!userId) {
            cov_bv36vqhj0().b[28][0]++;
            cov_bv36vqhj0().s[61]++;
            return {
              data: null,
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[28][1]++;
          }
          var _ref9 = (cov_bv36vqhj0().s[62]++, yield this.supabase.from('training_sessions').insert(Object.assign({}, sessionData, {
              user_id: userId
            })).select().single()),
            data = _ref9.data,
            error = _ref9.error;
          cov_bv36vqhj0().s[63]++;
          if (error) {
            cov_bv36vqhj0().b[29][0]++;
            cov_bv36vqhj0().s[64]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[29][1]++;
          }
          cov_bv36vqhj0().s[65]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[66]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[30][0]++, error.message) : (cov_bv36vqhj0().b[30][1]++, 'Failed to create training session')
          };
        }
      });
      function createTrainingSession(_x0) {
        return _createTrainingSession.apply(this, arguments);
      }
      return createTrainingSession;
    }())
  }, {
    key: "getVideoAnalyses",
    value: function () {
      var _getVideoAnalyses = _asyncToGenerator(function* (userId) {
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_bv36vqhj0().b[31][0]++, 20);
        var offset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_bv36vqhj0().b[32][0]++, 0);
        cov_bv36vqhj0().f[10]++;
        cov_bv36vqhj0().s[67]++;
        try {
          var _authService$getCurre5;
          var targetUserId = (cov_bv36vqhj0().s[68]++, (cov_bv36vqhj0().b[33][0]++, userId) || (cov_bv36vqhj0().b[33][1]++, (_authService$getCurre5 = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre5.id));
          cov_bv36vqhj0().s[69]++;
          if (!targetUserId) {
            cov_bv36vqhj0().b[34][0]++;
            cov_bv36vqhj0().s[70]++;
            return {
              data: [],
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[34][1]++;
          }
          var _ref0 = (cov_bv36vqhj0().s[71]++, yield this.supabase.from('video_analyses').select('*').eq('user_id', targetUserId).order('created_at', {
              ascending: false
            }).range(offset, offset + limit - 1)),
            data = _ref0.data,
            error = _ref0.error;
          cov_bv36vqhj0().s[72]++;
          if (error) {
            cov_bv36vqhj0().b[35][0]++;
            cov_bv36vqhj0().s[73]++;
            return {
              data: [],
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[35][1]++;
          }
          cov_bv36vqhj0().s[74]++;
          return {
            data: (cov_bv36vqhj0().b[36][0]++, data) || (cov_bv36vqhj0().b[36][1]++, [])
          };
        } catch (error) {
          cov_bv36vqhj0().s[75]++;
          return {
            data: [],
            error: error instanceof Error ? (cov_bv36vqhj0().b[37][0]++, error.message) : (cov_bv36vqhj0().b[37][1]++, 'Failed to fetch video analyses')
          };
        }
      });
      function getVideoAnalyses(_x1) {
        return _getVideoAnalyses.apply(this, arguments);
      }
      return getVideoAnalyses;
    }()
  }, {
    key: "createVideoAnalysis",
    value: (function () {
      var _createVideoAnalysis = _asyncToGenerator(function* (analysisData) {
        cov_bv36vqhj0().f[11]++;
        cov_bv36vqhj0().s[76]++;
        try {
          var _authService$getCurre6;
          var userId = (cov_bv36vqhj0().s[77]++, (_authService$getCurre6 = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre6.id);
          cov_bv36vqhj0().s[78]++;
          if (!userId) {
            cov_bv36vqhj0().b[38][0]++;
            cov_bv36vqhj0().s[79]++;
            return {
              data: null,
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[38][1]++;
          }
          var _ref1 = (cov_bv36vqhj0().s[80]++, yield this.supabase.from('video_analyses').insert(Object.assign({}, analysisData, {
              user_id: userId
            })).select().single()),
            data = _ref1.data,
            error = _ref1.error;
          cov_bv36vqhj0().s[81]++;
          if (error) {
            cov_bv36vqhj0().b[39][0]++;
            cov_bv36vqhj0().s[82]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[39][1]++;
          }
          cov_bv36vqhj0().s[83]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[84]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[40][0]++, error.message) : (cov_bv36vqhj0().b[40][1]++, 'Failed to create video analysis')
          };
        }
      });
      function createVideoAnalysis(_x10) {
        return _createVideoAnalysis.apply(this, arguments);
      }
      return createVideoAnalysis;
    }())
  }, {
    key: "updateVideoAnalysis",
    value: (function () {
      var _updateVideoAnalysis = _asyncToGenerator(function* (analysisId, updates) {
        cov_bv36vqhj0().f[12]++;
        cov_bv36vqhj0().s[85]++;
        try {
          var _ref10 = (cov_bv36vqhj0().s[86]++, yield this.supabase.from('video_analyses').update(updates).eq('id', analysisId).select().single()),
            data = _ref10.data,
            error = _ref10.error;
          cov_bv36vqhj0().s[87]++;
          if (error) {
            cov_bv36vqhj0().b[41][0]++;
            cov_bv36vqhj0().s[88]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[41][1]++;
          }
          cov_bv36vqhj0().s[89]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[90]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[42][0]++, error.message) : (cov_bv36vqhj0().b[42][1]++, 'Failed to update video analysis')
          };
        }
      });
      function updateVideoAnalysis(_x11, _x12) {
        return _updateVideoAnalysis.apply(this, arguments);
      }
      return updateVideoAnalysis;
    }())
  }, {
    key: "getDrills",
    value: function () {
      var _getDrills = _asyncToGenerator(function* (category, difficulty) {
        var limit = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_bv36vqhj0().b[43][0]++, 50);
        var offset = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : (cov_bv36vqhj0().b[44][0]++, 0);
        cov_bv36vqhj0().f[13]++;
        cov_bv36vqhj0().s[91]++;
        try {
          var query = (cov_bv36vqhj0().s[92]++, this.supabase.from('drills').select('*').eq('is_public', true));
          cov_bv36vqhj0().s[93]++;
          if (category) {
            cov_bv36vqhj0().b[45][0]++;
            cov_bv36vqhj0().s[94]++;
            query = query.eq('category', category);
          } else {
            cov_bv36vqhj0().b[45][1]++;
          }
          cov_bv36vqhj0().s[95]++;
          if (difficulty) {
            cov_bv36vqhj0().b[46][0]++;
            cov_bv36vqhj0().s[96]++;
            query = query.eq('difficulty_level', difficulty);
          } else {
            cov_bv36vqhj0().b[46][1]++;
          }
          var _ref11 = (cov_bv36vqhj0().s[97]++, yield query.order('usage_count', {
              ascending: false
            }).range(offset, offset + limit - 1)),
            data = _ref11.data,
            error = _ref11.error;
          cov_bv36vqhj0().s[98]++;
          if (error) {
            cov_bv36vqhj0().b[47][0]++;
            cov_bv36vqhj0().s[99]++;
            return {
              data: [],
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[47][1]++;
          }
          cov_bv36vqhj0().s[100]++;
          return {
            data: (cov_bv36vqhj0().b[48][0]++, data) || (cov_bv36vqhj0().b[48][1]++, [])
          };
        } catch (error) {
          cov_bv36vqhj0().s[101]++;
          return {
            data: [],
            error: error instanceof Error ? (cov_bv36vqhj0().b[49][0]++, error.message) : (cov_bv36vqhj0().b[49][1]++, 'Failed to fetch drills')
          };
        }
      });
      function getDrills(_x13, _x14) {
        return _getDrills.apply(this, arguments);
      }
      return getDrills;
    }()
  }, {
    key: "getDrill",
    value: (function () {
      var _getDrill = _asyncToGenerator(function* (drillId) {
        cov_bv36vqhj0().f[14]++;
        cov_bv36vqhj0().s[102]++;
        try {
          var _ref12 = (cov_bv36vqhj0().s[103]++, yield this.supabase.from('drills').select('*').eq('id', drillId).single()),
            data = _ref12.data,
            error = _ref12.error;
          cov_bv36vqhj0().s[104]++;
          if (error) {
            cov_bv36vqhj0().b[50][0]++;
            cov_bv36vqhj0().s[105]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[50][1]++;
          }
          cov_bv36vqhj0().s[106]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[107]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[51][0]++, error.message) : (cov_bv36vqhj0().b[51][1]++, 'Failed to fetch drill')
          };
        }
      });
      function getDrill(_x15) {
        return _getDrill.apply(this, arguments);
      }
      return getDrill;
    }())
  }, {
    key: "getUserDrillProgress",
    value: (function () {
      var _getUserDrillProgress = _asyncToGenerator(function* (drillId, userId) {
        cov_bv36vqhj0().f[15]++;
        cov_bv36vqhj0().s[108]++;
        try {
          var _authService$getCurre7;
          var targetUserId = (cov_bv36vqhj0().s[109]++, (cov_bv36vqhj0().b[52][0]++, userId) || (cov_bv36vqhj0().b[52][1]++, (_authService$getCurre7 = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre7.id));
          cov_bv36vqhj0().s[110]++;
          if (!targetUserId) {
            cov_bv36vqhj0().b[53][0]++;
            cov_bv36vqhj0().s[111]++;
            return {
              data: null,
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[53][1]++;
          }
          var _ref13 = (cov_bv36vqhj0().s[112]++, yield this.supabase.from('user_drill_progress').select('*').eq('user_id', targetUserId).eq('drill_id', drillId).single()),
            data = _ref13.data,
            error = _ref13.error;
          cov_bv36vqhj0().s[113]++;
          if ((cov_bv36vqhj0().b[55][0]++, error) && (cov_bv36vqhj0().b[55][1]++, error.code !== 'PGRST116')) {
            cov_bv36vqhj0().b[54][0]++;
            cov_bv36vqhj0().s[114]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[54][1]++;
          }
          cov_bv36vqhj0().s[115]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[116]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[56][0]++, error.message) : (cov_bv36vqhj0().b[56][1]++, 'Failed to fetch drill progress')
          };
        }
      });
      function getUserDrillProgress(_x16, _x17) {
        return _getUserDrillProgress.apply(this, arguments);
      }
      return getUserDrillProgress;
    }())
  }, {
    key: "updateDrillProgress",
    value: (function () {
      var _updateDrillProgress = _asyncToGenerator(function* (drillId, progressData) {
        cov_bv36vqhj0().f[16]++;
        cov_bv36vqhj0().s[117]++;
        try {
          var _authService$getCurre8;
          var userId = (cov_bv36vqhj0().s[118]++, (_authService$getCurre8 = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre8.id);
          cov_bv36vqhj0().s[119]++;
          if (!userId) {
            cov_bv36vqhj0().b[57][0]++;
            cov_bv36vqhj0().s[120]++;
            return {
              data: null,
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[57][1]++;
          }
          var _ref14 = (cov_bv36vqhj0().s[121]++, yield this.supabase.from('user_drill_progress').upsert(Object.assign({}, progressData, {
              user_id: userId,
              drill_id: drillId
            }), {
              onConflict: 'user_id,drill_id'
            }).select().single()),
            data = _ref14.data,
            error = _ref14.error;
          cov_bv36vqhj0().s[122]++;
          if (error) {
            cov_bv36vqhj0().b[58][0]++;
            cov_bv36vqhj0().s[123]++;
            return {
              data: null,
              error: error.message
            };
          } else {
            cov_bv36vqhj0().b[58][1]++;
          }
          cov_bv36vqhj0().s[124]++;
          return {
            data: data
          };
        } catch (error) {
          cov_bv36vqhj0().s[125]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[59][0]++, error.message) : (cov_bv36vqhj0().b[59][1]++, 'Failed to update drill progress')
          };
        }
      });
      function updateDrillProgress(_x18, _x19) {
        return _updateDrillProgress.apply(this, arguments);
      }
      return updateDrillProgress;
    }())
  }, {
    key: "getUserStatsSummary",
    value: function () {
      var _getUserStatsSummary = _asyncToGenerator(function* (userId) {
        cov_bv36vqhj0().f[17]++;
        cov_bv36vqhj0().s[126]++;
        try {
          var _authService$getCurre9;
          var targetUserId = (cov_bv36vqhj0().s[127]++, (cov_bv36vqhj0().b[60][0]++, userId) || (cov_bv36vqhj0().b[60][1]++, (_authService$getCurre9 = authService.getCurrentState().user) == null ? void 0 : _authService$getCurre9.id));
          cov_bv36vqhj0().s[128]++;
          if (!targetUserId) {
            cov_bv36vqhj0().b[61][0]++;
            cov_bv36vqhj0().s[129]++;
            return {
              data: null,
              error: 'User not authenticated'
            };
          } else {
            cov_bv36vqhj0().b[61][1]++;
          }
          var _ref15 = (cov_bv36vqhj0().s[130]++, yield this.getMatches(targetUserId, 1000)),
            matches = _ref15.data;
          var totalMatches = (cov_bv36vqhj0().s[131]++, matches.length);
          var wins = (cov_bv36vqhj0().s[132]++, matches.filter(function (m) {
            cov_bv36vqhj0().f[18]++;
            cov_bv36vqhj0().s[133]++;
            return m.result === 'win';
          }).length);
          var winPercentage = (cov_bv36vqhj0().s[134]++, totalMatches > 0 ? (cov_bv36vqhj0().b[62][0]++, wins / totalMatches * 100) : (cov_bv36vqhj0().b[62][1]++, 0));
          var _ref16 = (cov_bv36vqhj0().s[135]++, yield this.getTrainingSessions(targetUserId, 1000)),
            sessions = _ref16.data;
          var totalTrainingSessions = (cov_bv36vqhj0().s[136]++, sessions.length);
          var totalTrainingHours = (cov_bv36vqhj0().s[137]++, sessions.reduce(function (sum, s) {
            cov_bv36vqhj0().f[19]++;
            cov_bv36vqhj0().s[138]++;
            return sum + s.duration_minutes / 60;
          }, 0));
          cov_bv36vqhj0().s[139]++;
          return {
            data: {
              totalMatches: totalMatches,
              wins: wins,
              losses: totalMatches - wins,
              winPercentage: Math.round(winPercentage),
              totalTrainingSessions: totalTrainingSessions,
              totalTrainingHours: Math.round(totalTrainingHours * 10) / 10,
              recentMatches: matches.slice(0, 5),
              recentSessions: sessions.slice(0, 5)
            }
          };
        } catch (error) {
          cov_bv36vqhj0().s[140]++;
          return {
            data: null,
            error: error instanceof Error ? (cov_bv36vqhj0().b[63][0]++, error.message) : (cov_bv36vqhj0().b[63][1]++, 'Failed to fetch user statistics')
          };
        }
      });
      function getUserStatsSummary(_x20) {
        return _getUserStatsSummary.apply(this, arguments);
      }
      return getUserStatsSummary;
    }()
  }]);
}();
export var databaseService = (cov_bv36vqhj0().s[141]++, new DatabaseService());
export default databaseService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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