{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_objectSpread2", "_AnimatedEvent", "_AnimatedImplementation", "_AnimatedInterpolation", "_AnimatedNode", "_AnimatedValue", "_AnimatedValueXY", "_createAnimatedComponent", "_AnimatedColor", "inAnimationCallback", "mockAnimationStart", "start", "callback", "guarded<PERSON><PERSON>back", "console", "warn", "apply", "arguments", "emptyAnimation", "stop", "reset", "_startNativeLoop", "_isUsingNativeDriver", "mockCompositeAnimation", "animations", "for<PERSON>ach", "animation", "finished", "spring", "value", "config", "anyValue", "setValue", "toValue", "timing", "decay", "sequence", "parallel", "delay", "time", "stagger", "loop", "_temp", "_ref", "_ref$iterations", "iterations", "_default", "Value", "ValueXY", "Color", "Interpolation", "Node", "add", "subtract", "divide", "multiply", "modulo", "diffClamp", "event", "createAnimatedComponent", "attachNativeEvent", "forkEvent", "unforkEvent", "Event", "AnimatedEvent", "module"], "sources": ["AnimatedMock.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _AnimatedEvent = require(\"./AnimatedEvent\");\nvar _AnimatedImplementation = _interopRequireDefault(require(\"./AnimatedImplementation\"));\nvar _AnimatedInterpolation = _interopRequireDefault(require(\"./nodes/AnimatedInterpolation\"));\nvar _AnimatedNode = _interopRequireDefault(require(\"./nodes/AnimatedNode\"));\nvar _AnimatedValue = _interopRequireDefault(require(\"./nodes/AnimatedValue\"));\nvar _AnimatedValueXY = _interopRequireDefault(require(\"./nodes/AnimatedValueXY\"));\nvar _createAnimatedComponent = _interopRequireDefault(require(\"./createAnimatedComponent\"));\nvar _AnimatedColor = _interopRequireDefault(require(\"./nodes/AnimatedColor\"));\n/**\n * Animations are a source of flakiness in snapshot testing. This mock replaces\n * animation functions from AnimatedImplementation with empty animations for\n * predictability in tests. When possible the animation will run immediately\n * to the final state.\n */\n\n// Prevent any callback invocation from recursively triggering another\n// callback, which may trigger another animation\nvar inAnimationCallback = false;\nfunction mockAnimationStart(start) {\n  return callback => {\n    var guardedCallback = callback == null ? callback : function () {\n      if (inAnimationCallback) {\n        console.warn('Ignoring recursive animation callback when running mock animations');\n        return;\n      }\n      inAnimationCallback = true;\n      try {\n        callback(...arguments);\n      } finally {\n        inAnimationCallback = false;\n      }\n    };\n    start(guardedCallback);\n  };\n}\nvar emptyAnimation = {\n  start: () => {},\n  stop: () => {},\n  reset: () => {},\n  _startNativeLoop: () => {},\n  _isUsingNativeDriver: () => {\n    return false;\n  }\n};\nvar mockCompositeAnimation = animations => (0, _objectSpread2.default)((0, _objectSpread2.default)({}, emptyAnimation), {}, {\n  start: mockAnimationStart(callback => {\n    animations.forEach(animation => animation.start());\n    callback == null ? void 0 : callback({\n      finished: true\n    });\n  })\n});\nvar spring = function spring(value, config) {\n  var anyValue = value;\n  return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, emptyAnimation), {}, {\n    start: mockAnimationStart(callback => {\n      anyValue.setValue(config.toValue);\n      callback == null ? void 0 : callback({\n        finished: true\n      });\n    })\n  });\n};\nvar timing = function timing(value, config) {\n  var anyValue = value;\n  return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, emptyAnimation), {}, {\n    start: mockAnimationStart(callback => {\n      anyValue.setValue(config.toValue);\n      callback == null ? void 0 : callback({\n        finished: true\n      });\n    })\n  });\n};\nvar decay = function decay(value, config) {\n  return emptyAnimation;\n};\nvar sequence = function sequence(animations) {\n  return mockCompositeAnimation(animations);\n};\nvar parallel = function parallel(animations, config) {\n  return mockCompositeAnimation(animations);\n};\nvar delay = function delay(time) {\n  return emptyAnimation;\n};\nvar stagger = function stagger(time, animations) {\n  return mockCompositeAnimation(animations);\n};\nvar loop = function loop(animation, // $FlowFixMe[prop-missing]\n_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n    _ref$iterations = _ref.iterations,\n    iterations = _ref$iterations === void 0 ? -1 : _ref$iterations;\n  return emptyAnimation;\n};\nvar _default = exports.default = {\n  Value: _AnimatedValue.default,\n  ValueXY: _AnimatedValueXY.default,\n  Color: _AnimatedColor.default,\n  Interpolation: _AnimatedInterpolation.default,\n  Node: _AnimatedNode.default,\n  decay,\n  timing,\n  spring,\n  add: _AnimatedImplementation.default.add,\n  subtract: _AnimatedImplementation.default.subtract,\n  divide: _AnimatedImplementation.default.divide,\n  multiply: _AnimatedImplementation.default.multiply,\n  modulo: _AnimatedImplementation.default.modulo,\n  diffClamp: _AnimatedImplementation.default.diffClamp,\n  delay,\n  sequence,\n  parallel,\n  stagger,\n  loop,\n  event: _AnimatedImplementation.default.event,\n  createAnimatedComponent: _createAnimatedComponent.default,\n  attachNativeEvent: _AnimatedEvent.attachNativeEvent,\n  forkEvent: _AnimatedImplementation.default.forkEvent,\n  unforkEvent: _AnimatedImplementation.default.unforkEvent,\n  Event: _AnimatedEvent.AnimatedEvent\n};\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,cAAc,GAAGL,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIK,cAAc,GAAGL,OAAO,kBAAkB,CAAC;AAC/C,IAAIM,uBAAuB,GAAGP,sBAAsB,CAACC,OAAO,2BAA2B,CAAC,CAAC;AACzF,IAAIO,sBAAsB,GAAGR,sBAAsB,CAACC,OAAO,gCAAgC,CAAC,CAAC;AAC7F,IAAIQ,aAAa,GAAGT,sBAAsB,CAACC,OAAO,uBAAuB,CAAC,CAAC;AAC3E,IAAIS,cAAc,GAAGV,sBAAsB,CAACC,OAAO,wBAAwB,CAAC,CAAC;AAC7E,IAAIU,gBAAgB,GAAGX,sBAAsB,CAACC,OAAO,0BAA0B,CAAC,CAAC;AACjF,IAAIW,wBAAwB,GAAGZ,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAC3F,IAAIY,cAAc,GAAGb,sBAAsB,CAACC,OAAO,wBAAwB,CAAC,CAAC;AAU7E,IAAIa,mBAAmB,GAAG,KAAK;AAC/B,SAASC,kBAAkBA,CAACC,KAAK,EAAE;EACjC,OAAO,UAAAC,QAAQ,EAAI;IACjB,IAAIC,eAAe,GAAGD,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,YAAY;MAC9D,IAAIH,mBAAmB,EAAE;QACvBK,OAAO,CAACC,IAAI,CAAC,oEAAoE,CAAC;QAClF;MACF;MACAN,mBAAmB,GAAG,IAAI;MAC1B,IAAI;QACFG,QAAQ,CAAAI,KAAA,SAAIC,SAAS,CAAC;MACxB,CAAC,SAAS;QACRR,mBAAmB,GAAG,KAAK;MAC7B;IACF,CAAC;IACDE,KAAK,CAACE,eAAe,CAAC;EACxB,CAAC;AACH;AACA,IAAIK,cAAc,GAAG;EACnBP,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;EACfQ,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAQ,CAAC,CAAC;EACdC,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ,CAAC,CAAC;EACfC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA,EAAQ,CAAC,CAAC;EAC1BC,oBAAoB,EAAE,SAAtBA,oBAAoBA,CAAA,EAAQ;IAC1B,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIC,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAGC,UAAU;EAAA,OAAI,CAAC,CAAC,EAAExB,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEG,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEqB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;IAC1HP,KAAK,EAAED,kBAAkB,CAAC,UAAAE,QAAQ,EAAI;MACpCY,UAAU,CAACC,OAAO,CAAC,UAAAC,SAAS;QAAA,OAAIA,SAAS,CAACf,KAAK,CAAC,CAAC;MAAA,EAAC;MAClDC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC;QACnCe,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AAAA;AACF,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC1C,IAAIC,QAAQ,GAAGF,KAAK;EACpB,OAAO,CAAC,CAAC,EAAE7B,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEG,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEqB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;IACtFP,KAAK,EAAED,kBAAkB,CAAC,UAAAE,QAAQ,EAAI;MACpCmB,QAAQ,CAACC,QAAQ,CAACF,MAAM,CAACG,OAAO,CAAC;MACjCrB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC;QACnCe,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD,IAAIO,MAAM,GAAG,SAASA,MAAMA,CAACL,KAAK,EAAEC,MAAM,EAAE;EAC1C,IAAIC,QAAQ,GAAGF,KAAK;EACpB,OAAO,CAAC,CAAC,EAAE7B,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEG,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEqB,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;IACtFP,KAAK,EAAED,kBAAkB,CAAC,UAAAE,QAAQ,EAAI;MACpCmB,QAAQ,CAACC,QAAQ,CAACF,MAAM,CAACG,OAAO,CAAC;MACjCrB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC;QACnCe,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC;AACD,IAAIQ,KAAK,GAAG,SAASA,KAAKA,CAACN,KAAK,EAAEC,MAAM,EAAE;EACxC,OAAOZ,cAAc;AACvB,CAAC;AACD,IAAIkB,QAAQ,GAAG,SAASA,QAAQA,CAACZ,UAAU,EAAE;EAC3C,OAAOD,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AACD,IAAIa,QAAQ,GAAG,SAASA,QAAQA,CAACb,UAAU,EAAEM,MAAM,EAAE;EACnD,OAAOP,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AACD,IAAIc,KAAK,GAAG,SAASA,KAAKA,CAACC,IAAI,EAAE;EAC/B,OAAOrB,cAAc;AACvB,CAAC;AACD,IAAIsB,OAAO,GAAG,SAASA,OAAOA,CAACD,IAAI,EAAEf,UAAU,EAAE;EAC/C,OAAOD,sBAAsB,CAACC,UAAU,CAAC;AAC3C,CAAC;AACD,IAAIiB,IAAI,GAAG,SAASA,IAAIA,CAACf,SAAS,EAClCgB,KAAK,EAAE;EACL,IAAIC,IAAI,GAAGD,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;IACtCE,eAAe,GAAGD,IAAI,CAACE,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,eAAe;EAChE,OAAO1B,cAAc;AACvB,CAAC;AACD,IAAI4B,QAAQ,GAAGhD,OAAO,CAACD,OAAO,GAAG;EAC/BkD,KAAK,EAAE1C,cAAc,CAACR,OAAO;EAC7BmD,OAAO,EAAE1C,gBAAgB,CAACT,OAAO;EACjCoD,KAAK,EAAEzC,cAAc,CAACX,OAAO;EAC7BqD,aAAa,EAAE/C,sBAAsB,CAACN,OAAO;EAC7CsD,IAAI,EAAE/C,aAAa,CAACP,OAAO;EAC3BsC,KAAK,EAALA,KAAK;EACLD,MAAM,EAANA,MAAM;EACNN,MAAM,EAANA,MAAM;EACNwB,GAAG,EAAElD,uBAAuB,CAACL,OAAO,CAACuD,GAAG;EACxCC,QAAQ,EAAEnD,uBAAuB,CAACL,OAAO,CAACwD,QAAQ;EAClDC,MAAM,EAAEpD,uBAAuB,CAACL,OAAO,CAACyD,MAAM;EAC9CC,QAAQ,EAAErD,uBAAuB,CAACL,OAAO,CAAC0D,QAAQ;EAClDC,MAAM,EAAEtD,uBAAuB,CAACL,OAAO,CAAC2D,MAAM;EAC9CC,SAAS,EAAEvD,uBAAuB,CAACL,OAAO,CAAC4D,SAAS;EACpDnB,KAAK,EAALA,KAAK;EACLF,QAAQ,EAARA,QAAQ;EACRC,QAAQ,EAARA,QAAQ;EACRG,OAAO,EAAPA,OAAO;EACPC,IAAI,EAAJA,IAAI;EACJiB,KAAK,EAAExD,uBAAuB,CAACL,OAAO,CAAC6D,KAAK;EAC5CC,uBAAuB,EAAEpD,wBAAwB,CAACV,OAAO;EACzD+D,iBAAiB,EAAE3D,cAAc,CAAC2D,iBAAiB;EACnDC,SAAS,EAAE3D,uBAAuB,CAACL,OAAO,CAACgE,SAAS;EACpDC,WAAW,EAAE5D,uBAAuB,CAACL,OAAO,CAACiE,WAAW;EACxDC,KAAK,EAAE9D,cAAc,CAAC+D;AACxB,CAAC;AACDC,MAAM,CAACnE,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}