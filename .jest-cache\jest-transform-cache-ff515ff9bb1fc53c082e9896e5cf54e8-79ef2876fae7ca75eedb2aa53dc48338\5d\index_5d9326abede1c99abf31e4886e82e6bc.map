{"version": 3, "names": ["_interopRequireWildcard", "require", "default", "exports", "__esModule", "useStable", "React", "UNINITIALIZED", "Symbol", "Object", "freeze", "getInitialValue", "ref", "useRef", "current", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = useStable;\nvar React = _interopRequireWildcard(require(\"react\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar UNINITIALIZED = typeof Symbol === 'function' && typeof Symbol() === 'symbol' ? Symbol() : Object.freeze({});\nfunction useStable(getInitialValue) {\n  var ref = React.useRef(UNINITIALIZED);\n  if (ref.current === UNINITIALIZED) {\n    ref.current = getInitialValue();\n  }\n  // $FlowFixMe (#64650789) Trouble refining types where `Symbol` is concerned.\n  return ref.current;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,SAAS;AAC3B,IAAIC,KAAK,GAAGN,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AAUrD,IAAIM,aAAa,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAAC,CAAC,KAAK,QAAQ,GAAGA,MAAM,CAAC,CAAC,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/G,SAASL,SAASA,CAACM,eAAe,EAAE;EAClC,IAAIC,GAAG,GAAGN,KAAK,CAACO,MAAM,CAACN,aAAa,CAAC;EACrC,IAAIK,GAAG,CAACE,OAAO,KAAKP,aAAa,EAAE;IACjCK,GAAG,CAACE,OAAO,GAAGH,eAAe,CAAC,CAAC;EACjC;EAEA,OAAOC,GAAG,CAACE,OAAO;AACpB;AACAC,MAAM,CAACZ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}