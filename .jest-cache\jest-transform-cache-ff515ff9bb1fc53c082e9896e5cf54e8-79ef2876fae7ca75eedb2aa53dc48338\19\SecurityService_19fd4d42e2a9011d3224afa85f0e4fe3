f1e8784f218acc8a9be9cf090290d4fd
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_18lsccydd9() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\security\\SecurityService.ts";
  var hash = "81930887c98eae6ffc4636a721e4cce333dc553e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\security\\SecurityService.ts",
    statementMap: {
      "0": {
        start: {
          line: 47,
          column: 56
        },
        end: {
          line: 47,
          column: 65
        }
      },
      "1": {
        start: {
          line: 48,
          column: 79
        },
        end: {
          line: 48,
          column: 88
        }
      },
      "2": {
        start: {
          line: 49,
          column: 44
        },
        end: {
          line: 49,
          column: 46
        }
      },
      "3": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 60,
          column: 6
        }
      },
      "4": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 51
        }
      },
      "5": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 63,
          column: 40
        }
      },
      "6": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 73,
          column: 22
        }
      },
      "7": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 72,
          column: 35
        }
      },
      "8": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 78,
          column: 23
        }
      },
      "9": {
        start: {
          line: 77,
          column: 6
        },
        end: {
          line: 77,
          column: 35
        }
      },
      "10": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "11": {
        start: {
          line: 86,
          column: 18
        },
        end: {
          line: 86,
          column: 49
        }
      },
      "12": {
        start: {
          line: 87,
          column: 19
        },
        end: {
          line: 87,
          column: 55
        }
      },
      "13": {
        start: {
          line: 88,
          column: 17
        },
        end: {
          line: 88,
          column: 53
        }
      },
      "14": {
        start: {
          line: 90,
          column: 25
        },
        end: {
          line: 93,
          column: 8
        }
      },
      "15": {
        start: {
          line: 95,
          column: 24
        },
        end: {
          line: 99,
          column: 8
        }
      },
      "16": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 105,
          column: 8
        }
      },
      "17": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 59
        }
      },
      "18": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 43
        }
      },
      "19": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 136,
          column: 5
        }
      },
      "20": {
        start: {
          line: 117,
          column: 18
        },
        end: {
          line: 117,
          column: 49
        }
      },
      "21": {
        start: {
          line: 118,
          column: 19
        },
        end: {
          line: 118,
          column: 61
        }
      },
      "22": {
        start: {
          line: 119,
          column: 17
        },
        end: {
          line: 119,
          column: 57
        }
      },
      "23": {
        start: {
          line: 121,
          column: 25
        },
        end: {
          line: 124,
          column: 8
        }
      },
      "24": {
        start: {
          line: 126,
          column: 24
        },
        end: {
          line: 130,
          column: 8
        }
      },
      "25": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 132,
          column: 51
        }
      },
      "26": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 59
        }
      },
      "27": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 43
        }
      },
      "28": {
        start: {
          line: 143,
          column: 25
        },
        end: {
          line: 143,
          column: 80
        }
      },
      "29": {
        start: {
          line: 144,
          column: 17
        },
        end: {
          line: 147,
          column: 17
        }
      },
      "30": {
        start: {
          line: 149,
          column: 4
        },
        end: {
          line: 149,
          column: 40
        }
      },
      "31": {
        start: {
          line: 156,
          column: 35
        },
        end: {
          line: 156,
          column: 68
        }
      },
      "32": {
        start: {
          line: 157,
          column: 4
        },
        end: {
          line: 157,
          column: 33
        }
      },
      "33": {
        start: {
          line: 168,
          column: 16
        },
        end: {
          line: 168,
          column: 63
        }
      },
      "34": {
        start: {
          line: 169,
          column: 19
        },
        end: {
          line: 169,
          column: 77
        }
      },
      "35": {
        start: {
          line: 170,
          column: 16
        },
        end: {
          line: 170,
          column: 26
        }
      },
      "36": {
        start: {
          line: 172,
          column: 18
        },
        end: {
          line: 172,
          column: 53
        }
      },
      "37": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 187,
          column: 5
        }
      },
      "38": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 180,
          column: 9
        }
      },
      "39": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 186,
          column: 8
        }
      },
      "40": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 203,
          column: 5
        }
      },
      "41": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 196,
          column: 9
        }
      },
      "42": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 202,
          column: 8
        }
      },
      "43": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "44": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 212,
          column: 9
        }
      },
      "45": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 218,
          column: 8
        }
      },
      "46": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 222,
          column: 18
        }
      },
      "47": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 223,
          column: 28
        }
      },
      "48": {
        start: {
          line: 224,
          column: 4
        },
        end: {
          line: 224,
          column: 47
        }
      },
      "49": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 230,
          column: 6
        }
      },
      "50": {
        start: {
          line: 241,
          column: 16
        },
        end: {
          line: 241,
          column: 26
        }
      },
      "51": {
        start: {
          line: 242,
          column: 28
        },
        end: {
          line: 242,
          column: 67
        }
      },
      "52": {
        start: {
          line: 244,
          column: 21
        },
        end: {
          line: 244,
          column: 55
        }
      },
      "53": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 261,
          column: 5
        }
      },
      "54": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 44
        }
      },
      "55": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 255,
          column: 9
        }
      },
      "56": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 260,
          column: 8
        }
      },
      "57": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 281,
          column: 5
        }
      },
      "58": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 268,
          column: 9
        }
      },
      "59": {
        start: {
          line: 270,
          column: 6
        },
        end: {
          line: 275,
          column: 9
        }
      },
      "60": {
        start: {
          line: 277,
          column: 6
        },
        end: {
          line: 280,
          column: 8
        }
      },
      "61": {
        start: {
          line: 284,
          column: 4
        },
        end: {
          line: 295,
          column: 5
        }
      },
      "62": {
        start: {
          line: 286,
          column: 6
        },
        end: {
          line: 289,
          column: 9
        }
      },
      "63": {
        start: {
          line: 291,
          column: 6
        },
        end: {
          line: 294,
          column: 8
        }
      },
      "64": {
        start: {
          line: 298,
          column: 4
        },
        end: {
          line: 298,
          column: 21
        }
      },
      "65": {
        start: {
          line: 299,
          column: 4
        },
        end: {
          line: 299,
          column: 31
        }
      },
      "66": {
        start: {
          line: 300,
          column: 4
        },
        end: {
          line: 300,
          column: 49
        }
      },
      "67": {
        start: {
          line: 302,
          column: 21
        },
        end: {
          line: 302,
          column: 67
        }
      },
      "68": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 309,
          column: 7
        }
      },
      "69": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 315,
          column: 6
        }
      },
      "70": {
        start: {
          line: 322,
          column: 4
        },
        end: {
          line: 324,
          column: 5
        }
      },
      "71": {
        start: {
          line: 323,
          column: 6
        },
        end: {
          line: 323,
          column: 16
        }
      },
      "72": {
        start: {
          line: 326,
          column: 4
        },
        end: {
          line: 331,
          column: 14
        }
      },
      "73": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 340,
          column: 5
        }
      },
      "74": {
        start: {
          line: 339,
          column: 6
        },
        end: {
          line: 339,
          column: 16
        }
      },
      "75": {
        start: {
          line: 342,
          column: 4
        },
        end: {
          line: 347,
          column: 14
        }
      },
      "76": {
        start: {
          line: 354,
          column: 18
        },
        end: {
          line: 354,
          column: 82
        }
      },
      "77": {
        start: {
          line: 355,
          column: 17
        },
        end: {
          line: 355,
          column: 19
        }
      },
      "78": {
        start: {
          line: 357,
          column: 4
        },
        end: {
          line: 359,
          column: 5
        }
      },
      "79": {
        start: {
          line: 357,
          column: 17
        },
        end: {
          line: 357,
          column: 18
        }
      },
      "80": {
        start: {
          line: 358,
          column: 6
        },
        end: {
          line: 358,
          column: 71
        }
      },
      "81": {
        start: {
          line: 361,
          column: 4
        },
        end: {
          line: 361,
          column: 18
        }
      },
      "82": {
        start: {
          line: 368,
          column: 4
        },
        end: {
          line: 368,
          column: 59
        }
      },
      "83": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 381,
          column: 5
        }
      },
      "84": {
        start: {
          line: 376,
          column: 24
        },
        end: {
          line: 376,
          column: 42
        }
      },
      "85": {
        start: {
          line: 377,
          column: 6
        },
        end: {
          line: 377,
          column: 65
        }
      },
      "86": {
        start: {
          line: 379,
          column: 6
        },
        end: {
          line: 379,
          column: 68
        }
      },
      "87": {
        start: {
          line: 380,
          column: 6
        },
        end: {
          line: 380,
          column: 55
        }
      },
      "88": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 399,
          column: 5
        }
      },
      "89": {
        start: {
          line: 389,
          column: 21
        },
        end: {
          line: 389,
          column: 52
        }
      },
      "90": {
        start: {
          line: 390,
          column: 6
        },
        end: {
          line: 392,
          column: 7
        }
      },
      "91": {
        start: {
          line: 391,
          column: 8
        },
        end: {
          line: 391,
          column: 20
        }
      },
      "92": {
        start: {
          line: 394,
          column: 39
        },
        end: {
          line: 394,
          column: 57
        }
      },
      "93": {
        start: {
          line: 395,
          column: 6
        },
        end: {
          line: 395,
          column: 37
        }
      },
      "94": {
        start: {
          line: 397,
          column: 6
        },
        end: {
          line: 397,
          column: 71
        }
      },
      "95": {
        start: {
          line: 398,
          column: 6
        },
        end: {
          line: 398,
          column: 18
        }
      },
      "96": {
        start: {
          line: 406,
          column: 41
        },
        end: {
          line: 409,
          column: 5
        }
      },
      "97": {
        start: {
          line: 411,
          column: 4
        },
        end: {
          line: 411,
          column: 44
        }
      },
      "98": {
        start: {
          line: 414,
          column: 4
        },
        end: {
          line: 421,
          column: 5
        }
      },
      "99": {
        start: {
          line: 415,
          column: 6
        },
        end: {
          line: 415,
          column: 63
        }
      },
      "100": {
        start: {
          line: 418,
          column: 6
        },
        end: {
          line: 420,
          column: 7
        }
      },
      "101": {
        start: {
          line: 419,
          column: 8
        },
        end: {
          line: 419,
          column: 53
        }
      },
      "102": {
        start: {
          line: 428,
          column: 17
        },
        end: {
          line: 428,
          column: 36
        }
      },
      "103": {
        start: {
          line: 430,
          column: 4
        },
        end: {
          line: 432,
          column: 5
        }
      },
      "104": {
        start: {
          line: 431,
          column: 6
        },
        end: {
          line: 431,
          column: 67
        }
      },
      "105": {
        start: {
          line: 431,
          column: 38
        },
        end: {
          line: 431,
          column: 65
        }
      },
      "106": {
        start: {
          line: 434,
          column: 4
        },
        end: {
          line: 436,
          column: 23
        }
      },
      "107": {
        start: {
          line: 435,
          column: 22
        },
        end: {
          line: 435,
          column: 47
        }
      },
      "108": {
        start: {
          line: 443,
          column: 16
        },
        end: {
          line: 443,
          column: 26
        }
      },
      "109": {
        start: {
          line: 444,
          column: 19
        },
        end: {
          line: 444,
          column: 58
        }
      },
      "110": {
        start: {
          line: 446,
          column: 4
        },
        end: {
          line: 450,
          column: 5
        }
      },
      "111": {
        start: {
          line: 447,
          column: 6
        },
        end: {
          line: 449,
          column: 7
        }
      },
      "112": {
        start: {
          line: 448,
          column: 8
        },
        end: {
          line: 448,
          column: 40
        }
      },
      "113": {
        start: {
          line: 457,
          column: 16
        },
        end: {
          line: 457,
          column: 26
        }
      },
      "114": {
        start: {
          line: 458,
          column: 19
        },
        end: {
          line: 458,
          column: 38
        }
      },
      "115": {
        start: {
          line: 460,
          column: 4
        },
        end: {
          line: 462,
          column: 6
        }
      },
      "116": {
        start: {
          line: 461,
          column: 15
        },
        end: {
          line: 461,
          column: 45
        }
      },
      "117": {
        start: {
          line: 469,
          column: 4
        },
        end: {
          line: 475,
          column: 5
        }
      },
      "118": {
        start: {
          line: 472,
          column: 6
        },
        end: {
          line: 472,
          column: 60
        }
      },
      "119": {
        start: {
          line: 474,
          column: 6
        },
        end: {
          line: 474,
          column: 61
        }
      },
      "120": {
        start: {
          line: 482,
          column: 4
        },
        end: {
          line: 493,
          column: 5
        }
      },
      "121": {
        start: {
          line: 484,
          column: 6
        },
        end: {
          line: 486,
          column: 7
        }
      },
      "122": {
        start: {
          line: 485,
          column: 8
        },
        end: {
          line: 485,
          column: 21
        }
      },
      "123": {
        start: {
          line: 489,
          column: 26
        },
        end: {
          line: 489,
          column: 50
        }
      },
      "124": {
        start: {
          line: 490,
          column: 6
        },
        end: {
          line: 490,
          column: 37
        }
      },
      "125": {
        start: {
          line: 492,
          column: 6
        },
        end: {
          line: 492,
          column: 19
        }
      },
      "126": {
        start: {
          line: 505,
          column: 25
        },
        end: {
          line: 508,
          column: 5
        }
      },
      "127": {
        start: {
          line: 506,
          column: 15
        },
        end: {
          line: 507,
          column: 60
        }
      },
      "128": {
        start: {
          line: 511,
          column: 25
        },
        end: {
          line: 511,
          column: 84
        }
      },
      "129": {
        start: {
          line: 511,
          column: 54
        },
        end: {
          line: 511,
          column: 83
        }
      },
      "130": {
        start: {
          line: 512,
          column: 4
        },
        end: {
          line: 520,
          column: 5
        }
      },
      "131": {
        start: {
          line: 513,
          column: 6
        },
        end: {
          line: 518,
          column: 9
        }
      },
      "132": {
        start: {
          line: 519,
          column: 6
        },
        end: {
          line: 519,
          column: 18
        }
      },
      "133": {
        start: {
          line: 523,
          column: 26
        },
        end: {
          line: 525,
          column: 5
        }
      },
      "134": {
        start: {
          line: 524,
          column: 15
        },
        end: {
          line: 524,
          column: 59
        }
      },
      "135": {
        start: {
          line: 526,
          column: 4
        },
        end: {
          line: 534,
          column: 5
        }
      },
      "136": {
        start: {
          line: 527,
          column: 6
        },
        end: {
          line: 532,
          column: 9
        }
      },
      "137": {
        start: {
          line: 533,
          column: 6
        },
        end: {
          line: 533,
          column: 18
        }
      },
      "138": {
        start: {
          line: 536,
          column: 4
        },
        end: {
          line: 536,
          column: 17
        }
      },
      "139": {
        start: {
          line: 548,
          column: 27
        },
        end: {
          line: 550,
          column: 12
        }
      },
      "140": {
        start: {
          line: 549,
          column: 15
        },
        end: {
          line: 549,
          column: 44
        }
      },
      "141": {
        start: {
          line: 552,
          column: 4
        },
        end: {
          line: 557,
          column: 6
        }
      },
      "142": {
        start: {
          line: 562,
          column: 31
        },
        end: {
          line: 562,
          column: 52
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 52,
            column: 2
          },
          end: {
            line: 52,
            column: 3
          }
        },
        loc: {
          start: {
            line: 52,
            column: 16
          },
          end: {
            line: 64,
            column: 3
          }
        },
        line: 52
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 69,
            column: 2
          },
          end: {
            line: 69,
            column: 3
          }
        },
        loc: {
          start: {
            line: 69,
            column: 47
          },
          end: {
            line: 79,
            column: 3
          }
        },
        line: 69
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 71,
            column: 16
          },
          end: {
            line: 71,
            column: 17
          }
        },
        loc: {
          start: {
            line: 71,
            column: 22
          },
          end: {
            line: 73,
            column: 5
          }
        },
        line: 71
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 76,
            column: 16
          },
          end: {
            line: 76,
            column: 17
          }
        },
        loc: {
          start: {
            line: 76,
            column: 22
          },
          end: {
            line: 78,
            column: 5
          }
        },
        line: 76
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 84,
            column: 2
          },
          end: {
            line: 84,
            column: 3
          }
        },
        loc: {
          start: {
            line: 84,
            column: 59
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 84
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 115,
            column: 2
          },
          end: {
            line: 115,
            column: 3
          }
        },
        loc: {
          start: {
            line: 115,
            column: 68
          },
          end: {
            line: 137,
            column: 3
          }
        },
        line: 115
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 142,
            column: 2
          },
          end: {
            line: 142,
            column: 3
          }
        },
        loc: {
          start: {
            line: 142,
            column: 80
          },
          end: {
            line: 150,
            column: 3
          }
        },
        line: 142
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 155,
            column: 2
          },
          end: {
            line: 155,
            column: 3
          }
        },
        loc: {
          start: {
            line: 155,
            column: 72
          },
          end: {
            line: 158,
            column: 3
          }
        },
        line: 155
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 163,
            column: 2
          },
          end: {
            line: 163,
            column: 3
          }
        },
        loc: {
          start: {
            line: 167,
            column: 4
          },
          end: {
            line: 231,
            column: 3
          }
        },
        line: 167
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 236,
            column: 2
          },
          end: {
            line: 236,
            column: 3
          }
        },
        loc: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 316,
            column: 3
          }
        },
        line: 240
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 321,
            column: 2
          },
          end: {
            line: 321,
            column: 3
          }
        },
        loc: {
          start: {
            line: 321,
            column: 39
          },
          end: {
            line: 332,
            column: 3
          }
        },
        line: 321
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 337,
            column: 2
          },
          end: {
            line: 337,
            column: 3
          }
        },
        loc: {
          start: {
            line: 337,
            column: 42
          },
          end: {
            line: 348,
            column: 3
          }
        },
        line: 337
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 353,
            column: 2
          },
          end: {
            line: 353,
            column: 3
          }
        },
        loc: {
          start: {
            line: 353,
            column: 51
          },
          end: {
            line: 362,
            column: 3
          }
        },
        line: 353
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 367,
            column: 2
          },
          end: {
            line: 367,
            column: 3
          }
        },
        loc: {
          start: {
            line: 367,
            column: 42
          },
          end: {
            line: 369,
            column: 3
          }
        },
        line: 367
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 374,
            column: 2
          },
          end: {
            line: 374,
            column: 3
          }
        },
        loc: {
          start: {
            line: 374,
            column: 62
          },
          end: {
            line: 382,
            column: 3
          }
        },
        line: 374
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 387,
            column: 2
          },
          end: {
            line: 387,
            column: 3
          }
        },
        loc: {
          start: {
            line: 387,
            column: 60
          },
          end: {
            line: 400,
            column: 3
          }
        },
        line: 387
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 405,
            column: 2
          },
          end: {
            line: 405,
            column: 3
          }
        },
        loc: {
          start: {
            line: 405,
            column: 91
          },
          end: {
            line: 422,
            column: 3
          }
        },
        line: 405
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 427,
            column: 2
          },
          end: {
            line: 427,
            column: 3
          }
        },
        loc: {
          start: {
            line: 427,
            column: 96
          },
          end: {
            line: 437,
            column: 3
          }
        },
        line: 427
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 431,
            column: 29
          },
          end: {
            line: 431,
            column: 30
          }
        },
        loc: {
          start: {
            line: 431,
            column: 38
          },
          end: {
            line: 431,
            column: 65
          }
        },
        line: 431
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 435,
            column: 12
          },
          end: {
            line: 435,
            column: 13
          }
        },
        loc: {
          start: {
            line: 435,
            column: 22
          },
          end: {
            line: 435,
            column: 47
          }
        },
        line: 435
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 442,
            column: 2
          },
          end: {
            line: 442,
            column: 3
          }
        },
        loc: {
          start: {
            line: 442,
            column: 40
          },
          end: {
            line: 451,
            column: 3
          }
        },
        line: 442
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 456,
            column: 2
          },
          end: {
            line: 456,
            column: 3
          }
        },
        loc: {
          start: {
            line: 456,
            column: 40
          },
          end: {
            line: 463,
            column: 3
          }
        },
        line: 456
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 461,
            column: 6
          },
          end: {
            line: 461,
            column: 7
          }
        },
        loc: {
          start: {
            line: 461,
            column: 15
          },
          end: {
            line: 461,
            column: 45
          }
        },
        line: 461
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 468,
            column: 2
          },
          end: {
            line: 468,
            column: 3
          }
        },
        loc: {
          start: {
            line: 468,
            column: 78
          },
          end: {
            line: 476,
            column: 3
          }
        },
        line: 468
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 481,
            column: 2
          },
          end: {
            line: 481,
            column: 3
          }
        },
        loc: {
          start: {
            line: 481,
            column: 47
          },
          end: {
            line: 494,
            column: 3
          }
        },
        line: 481
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 499,
            column: 2
          },
          end: {
            line: 499,
            column: 3
          }
        },
        loc: {
          start: {
            line: 503,
            column: 14
          },
          end: {
            line: 537,
            column: 3
          }
        },
        line: 503
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 506,
            column: 6
          },
          end: {
            line: 506,
            column: 7
          }
        },
        loc: {
          start: {
            line: 506,
            column: 15
          },
          end: {
            line: 507,
            column: 60
          }
        },
        line: 506
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 511,
            column: 45
          },
          end: {
            line: 511,
            column: 46
          }
        },
        loc: {
          start: {
            line: 511,
            column: 54
          },
          end: {
            line: 511,
            column: 83
          }
        },
        line: 511
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 524,
            column: 6
          },
          end: {
            line: 524,
            column: 7
          }
        },
        loc: {
          start: {
            line: 524,
            column: 15
          },
          end: {
            line: 524,
            column: 59
          }
        },
        line: 524
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 542,
            column: 2
          },
          end: {
            line: 542,
            column: 3
          }
        },
        loc: {
          start: {
            line: 547,
            column: 4
          },
          end: {
            line: 558,
            column: 3
          }
        },
        line: 547
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 549,
            column: 6
          },
          end: {
            line: 549,
            column: 7
          }
        },
        loc: {
          start: {
            line: 549,
            column: 15
          },
          end: {
            line: 549,
            column: 44
          }
        },
        line: 549
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 54,
            column: 21
          },
          end: {
            line: 54,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 21
          },
          end: {
            line: 54,
            column: 46
          }
        }, {
          start: {
            line: 54,
            column: 50
          },
          end: {
            line: 54,
            column: 78
          }
        }],
        line: 54
      },
      "1": {
        loc: {
          start: {
            line: 86,
            column: 18
          },
          end: {
            line: 86,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 86,
            column: 18
          },
          end: {
            line: 86,
            column: 27
          }
        }, {
          start: {
            line: 86,
            column: 31
          },
          end: {
            line: 86,
            column: 49
          }
        }],
        line: 86
      },
      "2": {
        loc: {
          start: {
            line: 117,
            column: 18
          },
          end: {
            line: 117,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 117,
            column: 18
          },
          end: {
            line: 117,
            column: 27
          }
        }, {
          start: {
            line: 117,
            column: 31
          },
          end: {
            line: 117,
            column: 49
          }
        }],
        line: 117
      },
      "3": {
        loc: {
          start: {
            line: 143,
            column: 25
          },
          end: {
            line: 143,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 143,
            column: 25
          },
          end: {
            line: 143,
            column: 29
          }
        }, {
          start: {
            line: 143,
            column: 33
          },
          end: {
            line: 143,
            column: 80
          }
        }],
        line: 143
      },
      "4": {
        loc: {
          start: {
            line: 168,
            column: 16
          },
          end: {
            line: 168,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 168,
            column: 16
          },
          end: {
            line: 168,
            column: 27
          }
        }, {
          start: {
            line: 168,
            column: 31
          },
          end: {
            line: 168,
            column: 63
          }
        }],
        line: 168
      },
      "5": {
        loc: {
          start: {
            line: 169,
            column: 20
          },
          end: {
            line: 169,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 20
          },
          end: {
            line: 169,
            column: 33
          }
        }, {
          start: {
            line: 169,
            column: 37
          },
          end: {
            line: 169,
            column: 64
          }
        }],
        line: 169
      },
      "6": {
        loc: {
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 187,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 174,
            column: 4
          },
          end: {
            line: 187,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 174
      },
      "7": {
        loc: {
          start: {
            line: 190,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 190,
            column: 4
          },
          end: {
            line: 203,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 190
      },
      "8": {
        loc: {
          start: {
            line: 206,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 206,
            column: 4
          },
          end: {
            line: 219,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 206
      },
      "9": {
        loc: {
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 261,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 261,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "10": {
        loc: {
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 281,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 281,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "11": {
        loc: {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "12": {
        loc: {
          start: {
            line: 308,
            column: 16
          },
          end: {
            line: 308,
            column: 44
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 308,
            column: 27
          },
          end: {
            line: 308,
            column: 33
          }
        }, {
          start: {
            line: 308,
            column: 36
          },
          end: {
            line: 308,
            column: 44
          }
        }],
        line: 308
      },
      "13": {
        loc: {
          start: {
            line: 314,
            column: 19
          },
          end: {
            line: 314,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 314,
            column: 30
          },
          end: {
            line: 314,
            column: 68
          }
        }, {
          start: {
            line: 314,
            column: 71
          },
          end: {
            line: 314,
            column: 80
          }
        }],
        line: 314
      },
      "14": {
        loc: {
          start: {
            line: 322,
            column: 4
          },
          end: {
            line: 324,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 322,
            column: 4
          },
          end: {
            line: 324,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 322
      },
      "15": {
        loc: {
          start: {
            line: 338,
            column: 4
          },
          end: {
            line: 340,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 338,
            column: 4
          },
          end: {
            line: 340,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 338
      },
      "16": {
        loc: {
          start: {
            line: 353,
            column: 22
          },
          end: {
            line: 353,
            column: 41
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 353,
            column: 39
          },
          end: {
            line: 353,
            column: 41
          }
        }],
        line: 353
      },
      "17": {
        loc: {
          start: {
            line: 390,
            column: 6
          },
          end: {
            line: 392,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 390,
            column: 6
          },
          end: {
            line: 392,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 390
      },
      "18": {
        loc: {
          start: {
            line: 408,
            column: 17
          },
          end: {
            line: 408,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 408,
            column: 17
          },
          end: {
            line: 408,
            column: 32
          }
        }, {
          start: {
            line: 408,
            column: 36
          },
          end: {
            line: 408,
            column: 46
          }
        }],
        line: 408
      },
      "19": {
        loc: {
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 421,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 421,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 414
      },
      "20": {
        loc: {
          start: {
            line: 418,
            column: 6
          },
          end: {
            line: 420,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 418,
            column: 6
          },
          end: {
            line: 420,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 418
      },
      "21": {
        loc: {
          start: {
            line: 427,
            column: 58
          },
          end: {
            line: 427,
            column: 77
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 427,
            column: 74
          },
          end: {
            line: 427,
            column: 77
          }
        }],
        line: 427
      },
      "22": {
        loc: {
          start: {
            line: 430,
            column: 4
          },
          end: {
            line: 432,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 430,
            column: 4
          },
          end: {
            line: 432,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 430
      },
      "23": {
        loc: {
          start: {
            line: 447,
            column: 6
          },
          end: {
            line: 449,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 447,
            column: 6
          },
          end: {
            line: 449,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 447
      },
      "24": {
        loc: {
          start: {
            line: 484,
            column: 6
          },
          end: {
            line: 486,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 484,
            column: 6
          },
          end: {
            line: 486,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 484
      },
      "25": {
        loc: {
          start: {
            line: 484,
            column: 10
          },
          end: {
            line: 484,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 484,
            column: 10
          },
          end: {
            line: 484,
            column: 16
          }
        }, {
          start: {
            line: 484,
            column: 20
          },
          end: {
            line: 484,
            column: 37
          }
        }],
        line: 484
      },
      "26": {
        loc: {
          start: {
            line: 506,
            column: 15
          },
          end: {
            line: 507,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 506,
            column: 15
          },
          end: {
            line: 506,
            column: 38
          }
        }, {
          start: {
            line: 507,
            column: 15
          },
          end: {
            line: 507,
            column: 60
          }
        }],
        line: 506
      },
      "27": {
        loc: {
          start: {
            line: 512,
            column: 4
          },
          end: {
            line: 520,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 512,
            column: 4
          },
          end: {
            line: 520,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 512
      },
      "28": {
        loc: {
          start: {
            line: 526,
            column: 4
          },
          end: {
            line: 534,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 526,
            column: 4
          },
          end: {
            line: 534,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 526
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "81930887c98eae6ffc4636a721e4cce333dc553e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_18lsccydd9 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_18lsccydd9();
import CryptoJS from 'crypto-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { handleError, logError } from "../../utils/errorHandling";
import env from "../../config/environment";
var SecurityService = function () {
  function SecurityService() {
    _classCallCheck(this, SecurityService);
    this.rateLimitStore = (cov_18lsccydd9().s[0]++, new Map());
    this.loginAttempts = (cov_18lsccydd9().s[1]++, new Map());
    this.securityEvents = (cov_18lsccydd9().s[2]++, []);
    cov_18lsccydd9().f[0]++;
    cov_18lsccydd9().s[3]++;
    this.config = {
      encryptionKey: (cov_18lsccydd9().b[0][0]++, env.get('ENCRYPTION_KEY')) || (cov_18lsccydd9().b[0][1]++, this.generateEncryptionKey()),
      rateLimitWindow: 15,
      rateLimitMaxRequests: 100,
      sessionTimeout: 60,
      maxLoginAttempts: 5,
      lockoutDuration: 30
    };
    cov_18lsccydd9().s[4]++;
    this.encryptionKey = this.config.encryptionKey;
    cov_18lsccydd9().s[5]++;
    this.initializeSecurityMonitoring();
  }
  return _createClass(SecurityService, [{
    key: "initializeSecurityMonitoring",
    value: function initializeSecurityMonitoring() {
      var _this = this;
      cov_18lsccydd9().f[1]++;
      cov_18lsccydd9().s[6]++;
      setInterval(function () {
        cov_18lsccydd9().f[2]++;
        cov_18lsccydd9().s[7]++;
        _this.cleanupRateLimitStore();
      }, 5 * 60 * 1000);
      cov_18lsccydd9().s[8]++;
      setInterval(function () {
        cov_18lsccydd9().f[3]++;
        cov_18lsccydd9().s[9]++;
        _this.cleanupSecurityEvents();
      }, 60 * 60 * 1000);
    }
  }, {
    key: "encrypt",
    value: function encrypt(data, customKey) {
      cov_18lsccydd9().f[4]++;
      cov_18lsccydd9().s[10]++;
      try {
        var key = (cov_18lsccydd9().s[11]++, (cov_18lsccydd9().b[1][0]++, customKey) || (cov_18lsccydd9().b[1][1]++, this.encryptionKey));
        var salt = (cov_18lsccydd9().s[12]++, CryptoJS.lib.WordArray.random(256 / 8));
        var iv = (cov_18lsccydd9().s[13]++, CryptoJS.lib.WordArray.random(128 / 8));
        var derivedKey = (cov_18lsccydd9().s[14]++, CryptoJS.PBKDF2(key, salt, {
          keySize: 256 / 32,
          iterations: 10000
        }));
        var encrypted = (cov_18lsccydd9().s[15]++, CryptoJS.AES.encrypt(data, derivedKey, {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7
        }));
        cov_18lsccydd9().s[16]++;
        return {
          data: encrypted.toString(),
          iv: iv.toString(),
          salt: salt.toString()
        };
      } catch (error) {
        cov_18lsccydd9().s[17]++;
        logError(handleError(error), {
          context: 'encrypt'
        });
        cov_18lsccydd9().s[18]++;
        throw new Error('Encryption failed');
      }
    }
  }, {
    key: "decrypt",
    value: function decrypt(encryptedData, customKey) {
      cov_18lsccydd9().f[5]++;
      cov_18lsccydd9().s[19]++;
      try {
        var key = (cov_18lsccydd9().s[20]++, (cov_18lsccydd9().b[2][0]++, customKey) || (cov_18lsccydd9().b[2][1]++, this.encryptionKey));
        var salt = (cov_18lsccydd9().s[21]++, CryptoJS.enc.Hex.parse(encryptedData.salt));
        var iv = (cov_18lsccydd9().s[22]++, CryptoJS.enc.Hex.parse(encryptedData.iv));
        var derivedKey = (cov_18lsccydd9().s[23]++, CryptoJS.PBKDF2(key, salt, {
          keySize: 256 / 32,
          iterations: 10000
        }));
        var decrypted = (cov_18lsccydd9().s[24]++, CryptoJS.AES.decrypt(encryptedData.data, derivedKey, {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7
        }));
        cov_18lsccydd9().s[25]++;
        return decrypted.toString(CryptoJS.enc.Utf8);
      } catch (error) {
        cov_18lsccydd9().s[26]++;
        logError(handleError(error), {
          context: 'decrypt'
        });
        cov_18lsccydd9().s[27]++;
        throw new Error('Decryption failed');
      }
    }
  }, {
    key: "hashPassword",
    value: function hashPassword(password, salt) {
      cov_18lsccydd9().f[6]++;
      var passwordSalt = (cov_18lsccydd9().s[28]++, (cov_18lsccydd9().b[3][0]++, salt) || (cov_18lsccydd9().b[3][1]++, CryptoJS.lib.WordArray.random(256 / 8).toString()));
      var hash = (cov_18lsccydd9().s[29]++, CryptoJS.PBKDF2(password, passwordSalt, {
        keySize: 256 / 32,
        iterations: 10000
      }).toString());
      cov_18lsccydd9().s[30]++;
      return {
        hash: hash,
        salt: passwordSalt
      };
    }
  }, {
    key: "verifyPassword",
    value: function verifyPassword(password, hash, salt) {
      cov_18lsccydd9().f[7]++;
      var _ref = (cov_18lsccydd9().s[31]++, this.hashPassword(password, salt)),
        computedHash = _ref.hash;
      cov_18lsccydd9().s[32]++;
      return computedHash === hash;
    }
  }, {
    key: "checkRateLimit",
    value: function checkRateLimit(identifier, maxRequests, windowMinutes) {
      cov_18lsccydd9().f[8]++;
      var max = (cov_18lsccydd9().s[33]++, (cov_18lsccydd9().b[4][0]++, maxRequests) || (cov_18lsccydd9().b[4][1]++, this.config.rateLimitMaxRequests));
      var window = (cov_18lsccydd9().s[34]++, ((cov_18lsccydd9().b[5][0]++, windowMinutes) || (cov_18lsccydd9().b[5][1]++, this.config.rateLimitWindow)) * 60 * 1000);
      var now = (cov_18lsccydd9().s[35]++, Date.now());
      var entry = (cov_18lsccydd9().s[36]++, this.rateLimitStore.get(identifier));
      cov_18lsccydd9().s[37]++;
      if (!entry) {
        cov_18lsccydd9().b[6][0]++;
        cov_18lsccydd9().s[38]++;
        this.rateLimitStore.set(identifier, {
          count: 1,
          firstRequest: now,
          lastRequest: now
        });
        cov_18lsccydd9().s[39]++;
        return {
          allowed: true,
          remaining: max - 1,
          resetTime: now + window
        };
      } else {
        cov_18lsccydd9().b[6][1]++;
      }
      cov_18lsccydd9().s[40]++;
      if (now - entry.firstRequest > window) {
        cov_18lsccydd9().b[7][0]++;
        cov_18lsccydd9().s[41]++;
        this.rateLimitStore.set(identifier, {
          count: 1,
          firstRequest: now,
          lastRequest: now
        });
        cov_18lsccydd9().s[42]++;
        return {
          allowed: true,
          remaining: max - 1,
          resetTime: now + window
        };
      } else {
        cov_18lsccydd9().b[7][1]++;
      }
      cov_18lsccydd9().s[43]++;
      if (entry.count >= max) {
        cov_18lsccydd9().b[8][0]++;
        cov_18lsccydd9().s[44]++;
        this.logSecurityEvent({
          type: 'rate_limit_exceeded',
          details: {
            identifier: identifier,
            count: entry.count,
            max: max
          },
          timestamp: now,
          severity: 'medium'
        });
        cov_18lsccydd9().s[45]++;
        return {
          allowed: false,
          remaining: 0,
          resetTime: entry.firstRequest + window
        };
      } else {
        cov_18lsccydd9().b[8][1]++;
      }
      cov_18lsccydd9().s[46]++;
      entry.count++;
      cov_18lsccydd9().s[47]++;
      entry.lastRequest = now;
      cov_18lsccydd9().s[48]++;
      this.rateLimitStore.set(identifier, entry);
      cov_18lsccydd9().s[49]++;
      return {
        allowed: true,
        remaining: max - entry.count,
        resetTime: entry.firstRequest + window
      };
    }
  }, {
    key: "trackLoginAttempt",
    value: function trackLoginAttempt(identifier, success) {
      cov_18lsccydd9().f[9]++;
      var now = (cov_18lsccydd9().s[50]++, Date.now());
      var lockoutDuration = (cov_18lsccydd9().s[51]++, this.config.lockoutDuration * 60 * 1000);
      var attempts = (cov_18lsccydd9().s[52]++, this.loginAttempts.get(identifier));
      cov_18lsccydd9().s[53]++;
      if (success) {
        cov_18lsccydd9().b[9][0]++;
        cov_18lsccydd9().s[54]++;
        this.loginAttempts.delete(identifier);
        cov_18lsccydd9().s[55]++;
        this.logSecurityEvent({
          type: 'login_attempt',
          details: {
            identifier: identifier,
            success: true
          },
          timestamp: now,
          severity: 'low'
        });
        cov_18lsccydd9().s[56]++;
        return {
          allowed: true,
          attemptsRemaining: this.config.maxLoginAttempts
        };
      } else {
        cov_18lsccydd9().b[9][1]++;
      }
      cov_18lsccydd9().s[57]++;
      if (!attempts) {
        cov_18lsccydd9().b[10][0]++;
        cov_18lsccydd9().s[58]++;
        this.loginAttempts.set(identifier, {
          count: 1,
          lastAttempt: now
        });
        cov_18lsccydd9().s[59]++;
        this.logSecurityEvent({
          type: 'failed_login',
          details: {
            identifier: identifier,
            attempt: 1
          },
          timestamp: now,
          severity: 'low'
        });
        cov_18lsccydd9().s[60]++;
        return {
          allowed: true,
          attemptsRemaining: this.config.maxLoginAttempts - 1
        };
      } else {
        cov_18lsccydd9().b[10][1]++;
      }
      cov_18lsccydd9().s[61]++;
      if (now - attempts.lastAttempt > lockoutDuration) {
        cov_18lsccydd9().b[11][0]++;
        cov_18lsccydd9().s[62]++;
        this.loginAttempts.set(identifier, {
          count: 1,
          lastAttempt: now
        });
        cov_18lsccydd9().s[63]++;
        return {
          allowed: true,
          attemptsRemaining: this.config.maxLoginAttempts - 1
        };
      } else {
        cov_18lsccydd9().b[11][1]++;
      }
      cov_18lsccydd9().s[64]++;
      attempts.count++;
      cov_18lsccydd9().s[65]++;
      attempts.lastAttempt = now;
      cov_18lsccydd9().s[66]++;
      this.loginAttempts.set(identifier, attempts);
      var isLocked = (cov_18lsccydd9().s[67]++, attempts.count >= this.config.maxLoginAttempts);
      cov_18lsccydd9().s[68]++;
      this.logSecurityEvent({
        type: 'failed_login',
        details: {
          identifier: identifier,
          attempt: attempts.count,
          locked: isLocked
        },
        timestamp: now,
        severity: isLocked ? (cov_18lsccydd9().b[12][0]++, 'high') : (cov_18lsccydd9().b[12][1]++, 'medium')
      });
      cov_18lsccydd9().s[69]++;
      return {
        allowed: !isLocked,
        attemptsRemaining: Math.max(0, this.config.maxLoginAttempts - attempts.count),
        lockoutTime: isLocked ? (cov_18lsccydd9().b[13][0]++, attempts.lastAttempt + lockoutDuration) : (cov_18lsccydd9().b[13][1]++, undefined)
      };
    }
  }, {
    key: "sanitizeInput",
    value: function sanitizeInput(input) {
      cov_18lsccydd9().f[10]++;
      cov_18lsccydd9().s[70]++;
      if (typeof input !== 'string') {
        cov_18lsccydd9().b[14][0]++;
        cov_18lsccydd9().s[71]++;
        return '';
      } else {
        cov_18lsccydd9().b[14][1]++;
      }
      cov_18lsccydd9().s[72]++;
      return input.replace(/[<>]/g, '').replace(/javascript:/gi, '').replace(/on\w+=/gi, '').replace(/script/gi, '').trim();
    }
  }, {
    key: "sanitizeSQLInput",
    value: function sanitizeSQLInput(input) {
      cov_18lsccydd9().f[11]++;
      cov_18lsccydd9().s[73]++;
      if (typeof input !== 'string') {
        cov_18lsccydd9().b[15][0]++;
        cov_18lsccydd9().s[74]++;
        return '';
      } else {
        cov_18lsccydd9().b[15][1]++;
      }
      cov_18lsccydd9().s[75]++;
      return input.replace(/['";\\]/g, '').replace(/--/g, '').replace(/\/\*/g, '').replace(/\*\//g, '').trim();
    }
  }, {
    key: "generateSecureToken",
    value: function generateSecureToken() {
      var length = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_18lsccydd9().b[16][0]++, 32);
      cov_18lsccydd9().f[12]++;
      var chars = (cov_18lsccydd9().s[76]++, 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789');
      var result = (cov_18lsccydd9().s[77]++, '');
      cov_18lsccydd9().s[78]++;
      for (var i = (cov_18lsccydd9().s[79]++, 0); i < length; i++) {
        cov_18lsccydd9().s[80]++;
        result += chars.charAt(Math.floor(Math.random() * chars.length));
      }
      cov_18lsccydd9().s[81]++;
      return result;
    }
  }, {
    key: "generateEncryptionKey",
    value: function generateEncryptionKey() {
      cov_18lsccydd9().f[13]++;
      cov_18lsccydd9().s[82]++;
      return CryptoJS.lib.WordArray.random(256 / 8).toString();
    }
  }, {
    key: "secureStore",
    value: (function () {
      var _secureStore = _asyncToGenerator(function* (key, data) {
        cov_18lsccydd9().f[14]++;
        cov_18lsccydd9().s[83]++;
        try {
          var encrypted = (cov_18lsccydd9().s[84]++, this.encrypt(data));
          cov_18lsccydd9().s[85]++;
          yield AsyncStorage.setItem(key, JSON.stringify(encrypted));
        } catch (error) {
          cov_18lsccydd9().s[86]++;
          logError(handleError(error), {
            context: 'secureStore',
            key: key
          });
          cov_18lsccydd9().s[87]++;
          throw new Error('Failed to securely store data');
        }
      });
      function secureStore(_x, _x2) {
        return _secureStore.apply(this, arguments);
      }
      return secureStore;
    }())
  }, {
    key: "secureRetrieve",
    value: (function () {
      var _secureRetrieve = _asyncToGenerator(function* (key) {
        cov_18lsccydd9().f[15]++;
        cov_18lsccydd9().s[88]++;
        try {
          var stored = (cov_18lsccydd9().s[89]++, yield AsyncStorage.getItem(key));
          cov_18lsccydd9().s[90]++;
          if (!stored) {
            cov_18lsccydd9().b[17][0]++;
            cov_18lsccydd9().s[91]++;
            return null;
          } else {
            cov_18lsccydd9().b[17][1]++;
          }
          var encrypted = (cov_18lsccydd9().s[92]++, JSON.parse(stored));
          cov_18lsccydd9().s[93]++;
          return this.decrypt(encrypted);
        } catch (error) {
          cov_18lsccydd9().s[94]++;
          logError(handleError(error), {
            context: 'secureRetrieve',
            key: key
          });
          cov_18lsccydd9().s[95]++;
          return null;
        }
      });
      function secureRetrieve(_x3) {
        return _secureRetrieve.apply(this, arguments);
      }
      return secureRetrieve;
    }())
  }, {
    key: "logSecurityEvent",
    value: function logSecurityEvent(event) {
      cov_18lsccydd9().f[16]++;
      var securityEvent = (cov_18lsccydd9().s[96]++, Object.assign({}, event, {
        timestamp: (cov_18lsccydd9().b[18][0]++, event.timestamp) || (cov_18lsccydd9().b[18][1]++, Date.now())
      }));
      cov_18lsccydd9().s[97]++;
      this.securityEvents.push(securityEvent);
      cov_18lsccydd9().s[98]++;
      if (event.severity === 'critical') {
        cov_18lsccydd9().b[19][0]++;
        cov_18lsccydd9().s[99]++;
        console.error('CRITICAL SECURITY EVENT:', securityEvent);
        cov_18lsccydd9().s[100]++;
        if (env.getEnvironment() === 'production') {
          cov_18lsccydd9().b[20][0]++;
          cov_18lsccydd9().s[101]++;
          this.sendToSecurityMonitoring(securityEvent);
        } else {
          cov_18lsccydd9().b[20][1]++;
        }
      } else {
        cov_18lsccydd9().b[19][1]++;
      }
    }
  }, {
    key: "getSecurityEvents",
    value: function getSecurityEvents(severity) {
      var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_18lsccydd9().b[21][0]++, 100);
      cov_18lsccydd9().f[17]++;
      var events = (cov_18lsccydd9().s[102]++, this.securityEvents);
      cov_18lsccydd9().s[103]++;
      if (severity) {
        cov_18lsccydd9().b[22][0]++;
        cov_18lsccydd9().s[104]++;
        events = events.filter(function (event) {
          cov_18lsccydd9().f[18]++;
          cov_18lsccydd9().s[105]++;
          return event.severity === severity;
        });
      } else {
        cov_18lsccydd9().b[22][1]++;
      }
      cov_18lsccydd9().s[106]++;
      return events.sort(function (a, b) {
        cov_18lsccydd9().f[19]++;
        cov_18lsccydd9().s[107]++;
        return b.timestamp - a.timestamp;
      }).slice(0, limit);
    }
  }, {
    key: "cleanupRateLimitStore",
    value: function cleanupRateLimitStore() {
      cov_18lsccydd9().f[20]++;
      var now = (cov_18lsccydd9().s[108]++, Date.now());
      var window = (cov_18lsccydd9().s[109]++, this.config.rateLimitWindow * 60 * 1000);
      cov_18lsccydd9().s[110]++;
      for (var _ref2 of this.rateLimitStore.entries()) {
        var _ref3 = _slicedToArray(_ref2, 2);
        var key = _ref3[0];
        var entry = _ref3[1];
        cov_18lsccydd9().s[111]++;
        if (now - entry.firstRequest > window) {
          cov_18lsccydd9().b[23][0]++;
          cov_18lsccydd9().s[112]++;
          this.rateLimitStore.delete(key);
        } else {
          cov_18lsccydd9().b[23][1]++;
        }
      }
    }
  }, {
    key: "cleanupSecurityEvents",
    value: function cleanupSecurityEvents() {
      cov_18lsccydd9().f[21]++;
      var now = (cov_18lsccydd9().s[113]++, Date.now());
      var maxAge = (cov_18lsccydd9().s[114]++, 24 * 60 * 60 * 1000);
      cov_18lsccydd9().s[115]++;
      this.securityEvents = this.securityEvents.filter(function (event) {
        cov_18lsccydd9().f[22]++;
        cov_18lsccydd9().s[116]++;
        return now - event.timestamp < maxAge;
      });
    }
  }, {
    key: "sendToSecurityMonitoring",
    value: (function () {
      var _sendToSecurityMonitoring = _asyncToGenerator(function* (event) {
        cov_18lsccydd9().f[23]++;
        cov_18lsccydd9().s[117]++;
        try {
          cov_18lsccydd9().s[118]++;
          console.log('Sending to security monitoring:', event);
        } catch (error) {
          cov_18lsccydd9().s[119]++;
          console.error('Failed to send security event:', error);
        }
      });
      function sendToSecurityMonitoring(_x4) {
        return _sendToSecurityMonitoring.apply(this, arguments);
      }
      return sendToSecurityMonitoring;
    }())
  }, {
    key: "validateSessionToken",
    value: function validateSessionToken(token) {
      cov_18lsccydd9().f[24]++;
      cov_18lsccydd9().s[120]++;
      try {
        cov_18lsccydd9().s[121]++;
        if ((cov_18lsccydd9().b[25][0]++, !token) || (cov_18lsccydd9().b[25][1]++, token.length < 32)) {
          cov_18lsccydd9().b[24][0]++;
          cov_18lsccydd9().s[122]++;
          return false;
        } else {
          cov_18lsccydd9().b[24][1]++;
        }
        var base64Regex = (cov_18lsccydd9().s[123]++, /^[A-Za-z0-9+/]*={0,2}$/);
        cov_18lsccydd9().s[124]++;
        return base64Regex.test(token);
      } catch (error) {
        cov_18lsccydd9().s[125]++;
        return false;
      }
    }
  }, {
    key: "detectSuspiciousActivity",
    value: function detectSuspiciousActivity(userId, activity) {
      cov_18lsccydd9().f[25]++;
      var recentEvents = (cov_18lsccydd9().s[126]++, this.securityEvents.filter(function (event) {
        cov_18lsccydd9().f[26]++;
        cov_18lsccydd9().s[127]++;
        return (cov_18lsccydd9().b[26][0]++, event.userId === userId) && (cov_18lsccydd9().b[26][1]++, event.timestamp > Date.now() - 60 * 60 * 1000);
      }));
      var failedLogins = (cov_18lsccydd9().s[128]++, recentEvents.filter(function (event) {
        cov_18lsccydd9().f[27]++;
        cov_18lsccydd9().s[129]++;
        return event.type === 'failed_login';
      }));
      cov_18lsccydd9().s[130]++;
      if (failedLogins.length > 10) {
        cov_18lsccydd9().b[27][0]++;
        cov_18lsccydd9().s[131]++;
        this.logSecurityEvent({
          type: 'suspicious_activity',
          userId: userId,
          details: {
            reason: 'excessive_failed_logins',
            count: failedLogins.length
          },
          severity: 'high'
        });
        cov_18lsccydd9().s[132]++;
        return true;
      } else {
        cov_18lsccydd9().b[27][1]++;
      }
      var rapidRequests = (cov_18lsccydd9().s[133]++, recentEvents.filter(function (event) {
        cov_18lsccydd9().f[28]++;
        cov_18lsccydd9().s[134]++;
        return event.timestamp > Date.now() - 5 * 60 * 1000;
      }));
      cov_18lsccydd9().s[135]++;
      if (rapidRequests.length > 50) {
        cov_18lsccydd9().b[28][0]++;
        cov_18lsccydd9().s[136]++;
        this.logSecurityEvent({
          type: 'suspicious_activity',
          userId: userId,
          details: {
            reason: 'rapid_requests',
            count: rapidRequests.length
          },
          severity: 'medium'
        });
        cov_18lsccydd9().s[137]++;
        return true;
      } else {
        cov_18lsccydd9().b[28][1]++;
      }
      cov_18lsccydd9().s[138]++;
      return false;
    }
  }, {
    key: "getSecurityStatus",
    value: function getSecurityStatus() {
      cov_18lsccydd9().f[29]++;
      var criticalEvents = (cov_18lsccydd9().s[139]++, this.securityEvents.filter(function (event) {
        cov_18lsccydd9().f[30]++;
        cov_18lsccydd9().s[140]++;
        return event.severity === 'critical';
      }).length);
      cov_18lsccydd9().s[141]++;
      return {
        rateLimitEntries: this.rateLimitStore.size,
        loginAttempts: this.loginAttempts.size,
        securityEvents: this.securityEvents.length,
        criticalEvents: criticalEvents
      };
    }
  }]);
}();
export var securityService = (cov_18lsccydd9().s[142]++, new SecurityService());
export default securityService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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