4250c3810c58649e6ba8b73485ec1d63
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _createForOfIteratorHelperLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/createForOfIteratorHelperLoose"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var ViewabilityHelper = function () {
  function ViewabilityHelper(config) {
    (0, _classCallCheck2.default)(this, ViewabilityHelper);
    if (config === void 0) {
      config = {
        viewAreaCoveragePercentThreshold: 0
      };
    }
    this._hasInteracted = false;
    this._timers = new Set();
    this._viewableIndices = [];
    this._viewableItems = new Map();
    this._config = config;
  }
  return (0, _createClass2.default)(ViewabilityHelper, [{
    key: "dispose",
    value: function dispose() {
      this._timers.forEach(clearTimeout);
    }
  }, {
    key: "computeViewableItems",
    value: function computeViewableItems(props, scrollOffset, viewportHeight, getFrameMetrics, renderRange) {
      var itemCount = props.getItemCount(props.data);
      var _this$_config = this._config,
        itemVisiblePercentThreshold = _this$_config.itemVisiblePercentThreshold,
        viewAreaCoveragePercentThreshold = _this$_config.viewAreaCoveragePercentThreshold;
      var viewAreaMode = viewAreaCoveragePercentThreshold != null;
      var viewablePercentThreshold = viewAreaMode ? viewAreaCoveragePercentThreshold : itemVisiblePercentThreshold;
      (0, _invariant.default)(viewablePercentThreshold != null && itemVisiblePercentThreshold != null !== (viewAreaCoveragePercentThreshold != null), 'Must set exactly one of itemVisiblePercentThreshold or viewAreaCoveragePercentThreshold');
      var viewableIndices = [];
      if (itemCount === 0) {
        return viewableIndices;
      }
      var firstVisible = -1;
      var _ref = renderRange || {
          first: 0,
          last: itemCount - 1
        },
        first = _ref.first,
        last = _ref.last;
      if (last >= itemCount) {
        console.warn('Invalid render range computing viewability ' + JSON.stringify({
          renderRange: renderRange,
          itemCount: itemCount
        }));
        return [];
      }
      for (var idx = first; idx <= last; idx++) {
        var metrics = getFrameMetrics(idx, props);
        if (!metrics) {
          continue;
        }
        var top = metrics.offset - scrollOffset;
        var bottom = top + metrics.length;
        if (top < viewportHeight && bottom > 0) {
          firstVisible = idx;
          if (_isViewable(viewAreaMode, viewablePercentThreshold, top, bottom, viewportHeight, metrics.length)) {
            viewableIndices.push(idx);
          }
        } else if (firstVisible >= 0) {
          break;
        }
      }
      return viewableIndices;
    }
  }, {
    key: "onUpdate",
    value: function onUpdate(props, scrollOffset, viewportHeight, getFrameMetrics, createViewToken, onViewableItemsChanged, renderRange) {
      var _this = this;
      var itemCount = props.getItemCount(props.data);
      if (this._config.waitForInteraction && !this._hasInteracted || itemCount === 0 || !getFrameMetrics(0, props)) {
        return;
      }
      var viewableIndices = [];
      if (itemCount) {
        viewableIndices = this.computeViewableItems(props, scrollOffset, viewportHeight, getFrameMetrics, renderRange);
      }
      if (this._viewableIndices.length === viewableIndices.length && this._viewableIndices.every(function (v, ii) {
        return v === viewableIndices[ii];
      })) {
        return;
      }
      this._viewableIndices = viewableIndices;
      if (this._config.minimumViewTime) {
        var handle = setTimeout(function () {
          _this._timers.delete(handle);
          _this._onUpdateSync(props, viewableIndices, onViewableItemsChanged, createViewToken);
        }, this._config.minimumViewTime);
        this._timers.add(handle);
      } else {
        this._onUpdateSync(props, viewableIndices, onViewableItemsChanged, createViewToken);
      }
    }
  }, {
    key: "resetViewableIndices",
    value: function resetViewableIndices() {
      this._viewableIndices = [];
    }
  }, {
    key: "recordInteraction",
    value: function recordInteraction() {
      this._hasInteracted = true;
    }
  }, {
    key: "_onUpdateSync",
    value: function _onUpdateSync(props, viewableIndicesToCheck, onViewableItemsChanged, createViewToken) {
      var _this2 = this;
      viewableIndicesToCheck = viewableIndicesToCheck.filter(function (ii) {
        return _this2._viewableIndices.includes(ii);
      });
      var prevItems = this._viewableItems;
      var nextItems = new Map(viewableIndicesToCheck.map(function (ii) {
        var viewable = createViewToken(ii, true, props);
        return [viewable.key, viewable];
      }));
      var changed = [];
      for (var _iterator = (0, _createForOfIteratorHelperLoose2.default)(nextItems), _step; !(_step = _iterator()).done;) {
        var _step$value = _step.value,
          key = _step$value[0],
          viewable = _step$value[1];
        if (!prevItems.has(key)) {
          changed.push(viewable);
        }
      }
      for (var _iterator2 = (0, _createForOfIteratorHelperLoose2.default)(prevItems), _step2; !(_step2 = _iterator2()).done;) {
        var _step2$value = _step2.value,
          _key = _step2$value[0],
          _viewable = _step2$value[1];
        if (!nextItems.has(_key)) {
          changed.push((0, _objectSpread2.default)((0, _objectSpread2.default)({}, _viewable), {}, {
            isViewable: false
          }));
        }
      }
      if (changed.length > 0) {
        this._viewableItems = nextItems;
        onViewableItemsChanged({
          viewableItems: Array.from(nextItems.values()),
          changed: changed,
          viewabilityConfig: this._config
        });
      }
    }
  }]);
}();
function _isViewable(viewAreaMode, viewablePercentThreshold, top, bottom, viewportHeight, itemLength) {
  if (_isEntirelyVisible(top, bottom, viewportHeight)) {
    return true;
  } else {
    var pixels = _getPixelsVisible(top, bottom, viewportHeight);
    var percent = 100 * (viewAreaMode ? pixels / viewportHeight : pixels / itemLength);
    return percent >= viewablePercentThreshold;
  }
}
function _getPixelsVisible(top, bottom, viewportHeight) {
  var visibleHeight = Math.min(bottom, viewportHeight) - Math.max(top, 0);
  return Math.max(0, visibleHeight);
}
function _isEntirelyVisible(top, bottom, viewportHeight) {
  return top >= 0 && bottom <= viewportHeight && bottom > top;
}
var _default = exports.default = ViewabilityHelper;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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