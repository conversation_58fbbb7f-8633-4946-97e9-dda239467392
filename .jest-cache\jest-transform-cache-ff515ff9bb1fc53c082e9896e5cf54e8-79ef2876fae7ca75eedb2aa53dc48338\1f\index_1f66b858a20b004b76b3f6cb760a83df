1bd4b49a04176aae9bca9224ea695539
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = useColorScheme;
var React = _interopRequireWildcard(require("react"));
var _Appearance = _interopRequireDefault(require("../Appearance"));
function useColorScheme() {
  var _React$useState = React.useState(_Appearance.default.getColorScheme()),
    colorScheme = _React$useState[0],
    setColorScheme = _React$useState[1];
  React.useEffect(function () {
    function listener(appearance) {
      setColorScheme(appearance.colorScheme);
    }
    var _Appearance$addChange = _Appearance.default.addChangeListener(listener),
      remove = _Appearance$addChange.remove;
    return remove;
  });
  return colorScheme;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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