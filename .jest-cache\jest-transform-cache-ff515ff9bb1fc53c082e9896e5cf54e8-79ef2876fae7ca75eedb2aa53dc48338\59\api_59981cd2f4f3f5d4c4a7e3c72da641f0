b5b6141bb43241ce709938831ec21a88
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.apiService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _env2 = require("expo/virtual/env");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _supabase = require("../lib/supabase");
var _openai = require("./openai");
function cov_29vg1lngs() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\api.ts";
  var hash = "f0808e9b24f062926c6678b8fd89b82e2dacefdd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\api.ts",
    statementMap: {
      "0": {
        start: {
          line: 7,
          column: 20
        },
        end: {
          line: 7,
          column: 94
        }
      },
      "1": {
        start: {
          line: 8,
          column: 19
        },
        end: {
          line: 8,
          column: 79
        }
      },
      "2": {
        start: {
          line: 12,
          column: 4
        },
        end: {
          line: 12,
          column: 59
        }
      },
      "3": {
        start: {
          line: 12,
          column: 34
        },
        end: {
          line: 12,
          column: 57
        }
      },
      "4": {
        start: {
          line: 16,
          column: 27
        },
        end: {
          line: 25,
          column: 3
        }
      },
      "5": {
        start: {
          line: 27,
          column: 39
        },
        end: {
          line: 38,
          column: 3
        }
      },
      "6": {
        start: {
          line: 40,
          column: 50
        },
        end: {
          line: 78,
          column: 3
        }
      },
      "7": {
        start: {
          line: 80,
          column: 41
        },
        end: {
          line: 100,
          column: 3
        }
      },
      "8": {
        start: {
          line: 102,
          column: 44
        },
        end: {
          line: 135,
          column: 3
        }
      },
      "9": {
        start: {
          line: 137,
          column: 46
        },
        end: {
          line: 165,
          column: 3
        }
      },
      "10": {
        start: {
          line: 167,
          column: 32
        },
        end: {
          line: 174,
          column: 3
        }
      },
      "11": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 309,
          column: 5
        }
      },
      "12": {
        start: {
          line: 181,
          column: 47
        },
        end: {
          line: 185,
          column: 17
        }
      },
      "13": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 190,
          column: 7
        }
      },
      "14": {
        start: {
          line: 188,
          column: 8
        },
        end: {
          line: 188,
          column: 57
        }
      },
      "15": {
        start: {
          line: 189,
          column: 8
        },
        end: {
          line: 189,
          column: 53
        }
      },
      "16": {
        start: {
          line: 193,
          column: 59
        },
        end: {
          line: 197,
          column: 17
        }
      },
      "17": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 223,
          column: 7
        }
      },
      "18": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 200,
          column: 70
        }
      },
      "19": {
        start: {
          line: 202,
          column: 60
        },
        end: {
          line: 215,
          column: 19
        }
      },
      "20": {
        start: {
          line: 217,
          column: 8
        },
        end: {
          line: 219,
          column: 9
        }
      },
      "21": {
        start: {
          line: 218,
          column: 10
        },
        end: {
          line: 218,
          column: 58
        }
      },
      "22": {
        start: {
          line: 222,
          column: 32
        },
        end: {
          line: 222,
          column: 68
        }
      },
      "23": {
        start: {
          line: 226,
          column: 61
        },
        end: {
          line: 231,
          column: 17
        }
      },
      "24": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 235,
          column: 7
        }
      },
      "25": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 74
        }
      },
      "26": {
        start: {
          line: 238,
          column: 60
        },
        end: {
          line: 243,
          column: 17
        }
      },
      "27": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 247,
          column: 7
        }
      },
      "28": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 246,
          column: 67
        }
      },
      "29": {
        start: {
          line: 249,
          column: 26
        },
        end: {
          line: 249,
          column: 102
        }
      },
      "30": {
        start: {
          line: 252,
          column: 63
        },
        end: {
          line: 256,
          column: 51
        }
      },
      "31": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 260,
          column: 7
        }
      },
      "32": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 259,
          column: 73
        }
      },
      "33": {
        start: {
          line: 263,
          column: 65
        },
        end: {
          line: 268,
          column: 18
        }
      },
      "34": {
        start: {
          line: 270,
          column: 6
        },
        end: {
          line: 272,
          column: 7
        }
      },
      "35": {
        start: {
          line: 271,
          column: 8
        },
        end: {
          line: 271,
          column: 75
        }
      },
      "36": {
        start: {
          line: 275,
          column: 55
        },
        end: {
          line: 280,
          column: 17
        }
      },
      "37": {
        start: {
          line: 282,
          column: 6
        },
        end: {
          line: 284,
          column: 7
        }
      },
      "38": {
        start: {
          line: 283,
          column: 8
        },
        end: {
          line: 283,
          column: 61
        }
      },
      "39": {
        start: {
          line: 286,
          column: 23
        },
        end: {
          line: 286,
          column: 103
        }
      },
      "40": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 296,
          column: 8
        }
      },
      "41": {
        start: {
          line: 298,
          column: 6
        },
        end: {
          line: 298,
          column: 57
        }
      },
      "42": {
        start: {
          line: 300,
          column: 6
        },
        end: {
          line: 308,
          column: 8
        }
      },
      "43": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 397,
          column: 5
        }
      },
      "44": {
        start: {
          line: 315,
          column: 29
        },
        end: {
          line: 319,
          column: 17
        }
      },
      "45": {
        start: {
          line: 321,
          column: 35
        },
        end: {
          line: 325,
          column: 17
        }
      },
      "46": {
        start: {
          line: 327,
          column: 39
        },
        end: {
          line: 332,
          column: 17
        }
      },
      "47": {
        start: {
          line: 335,
          column: 30
        },
        end: {
          line: 343,
          column: 7
        }
      },
      "48": {
        start: {
          line: 337,
          column: 49
        },
        end: {
          line: 337,
          column: 56
        }
      },
      "49": {
        start: {
          line: 345,
          column: 25
        },
        end: {
          line: 345,
          column: 84
        }
      },
      "50": {
        start: {
          line: 346,
          column: 22
        },
        end: {
          line: 346,
          column: 48
        }
      },
      "51": {
        start: {
          line: 348,
          column: 21
        },
        end: {
          line: 353,
          column: 7
        }
      },
      "52": {
        start: {
          line: 356,
          column: 30
        },
        end: {
          line: 360,
          column: 17
        }
      },
      "53": {
        start: {
          line: 362,
          column: 6
        },
        end: {
          line: 373,
          column: 7
        }
      },
      "54": {
        start: {
          line: 363,
          column: 8
        },
        end: {
          line: 363,
          column: 53
        }
      },
      "55": {
        start: {
          line: 365,
          column: 8
        },
        end: {
          line: 372,
          column: 10
        }
      },
      "56": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 375,
          column: 18
        }
      },
      "57": {
        start: {
          line: 377,
          column: 6
        },
        end: {
          line: 377,
          column: 55
        }
      },
      "58": {
        start: {
          line: 379,
          column: 27
        },
        end: {
          line: 385,
          column: 7
        }
      },
      "59": {
        start: {
          line: 387,
          column: 22
        },
        end: {
          line: 387,
          column: 83
        }
      },
      "60": {
        start: {
          line: 389,
          column: 6
        },
        end: {
          line: 396,
          column: 8
        }
      },
      "61": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 418,
          column: 5
        }
      },
      "62": {
        start: {
          line: 402,
          column: 24
        },
        end: {
          line: 405,
          column: 33
        }
      },
      "63": {
        start: {
          line: 407,
          column: 6
        },
        end: {
          line: 410,
          column: 7
        }
      },
      "64": {
        start: {
          line: 408,
          column: 8
        },
        end: {
          line: 408,
          column: 68
        }
      },
      "65": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 63
        }
      },
      "66": {
        start: {
          line: 412,
          column: 6
        },
        end: {
          line: 412,
          column: 63
        }
      },
      "67": {
        start: {
          line: 414,
          column: 27
        },
        end: {
          line: 414,
          column: 84
        }
      },
      "68": {
        start: {
          line: 414,
          column: 60
        },
        end: {
          line: 414,
          column: 83
        }
      },
      "69": {
        start: {
          line: 415,
          column: 6
        },
        end: {
          line: 417,
          column: 7
        }
      },
      "70": {
        start: {
          line: 416,
          column: 8
        },
        end: {
          line: 416,
          column: 33
        }
      },
      "71": {
        start: {
          line: 422,
          column: 4
        },
        end: {
          line: 474,
          column: 5
        }
      },
      "72": {
        start: {
          line: 424,
          column: 56
        },
        end: {
          line: 428,
          column: 17
        }
      },
      "73": {
        start: {
          line: 430,
          column: 6
        },
        end: {
          line: 433,
          column: 7
        }
      },
      "74": {
        start: {
          line: 431,
          column: 8
        },
        end: {
          line: 431,
          column: 67
        }
      },
      "75": {
        start: {
          line: 432,
          column: 8
        },
        end: {
          line: 432,
          column: 57
        }
      },
      "76": {
        start: {
          line: 436,
          column: 27
        },
        end: {
          line: 436,
          column: 46
        }
      },
      "77": {
        start: {
          line: 437,
          column: 24
        },
        end: {
          line: 437,
          column: 106
        }
      },
      "78": {
        start: {
          line: 439,
          column: 6
        },
        end: {
          line: 443,
          column: 9
        }
      },
      "79": {
        start: {
          line: 440,
          column: 29
        },
        end: {
          line: 440,
          column: 76
        }
      },
      "80": {
        start: {
          line: 442,
          column: 8
        },
        end: {
          line: 442,
          column: 108
        }
      },
      "81": {
        start: {
          line: 446,
          column: 53
        },
        end: {
          line: 451,
          column: 17
        }
      },
      "82": {
        start: {
          line: 453,
          column: 6
        },
        end: {
          line: 456,
          column: 7
        }
      },
      "83": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 454,
          column: 60
        }
      },
      "84": {
        start: {
          line: 455,
          column: 8
        },
        end: {
          line: 455,
          column: 50
        }
      },
      "85": {
        start: {
          line: 458,
          column: 6
        },
        end: {
          line: 458,
          column: 22
        }
      },
      "86": {
        start: {
          line: 460,
          column: 6
        },
        end: {
          line: 460,
          column: 57
        }
      },
      "87": {
        start: {
          line: 462,
          column: 27
        },
        end: {
          line: 462,
          column: 53
        }
      },
      "88": {
        start: {
          line: 463,
          column: 6
        },
        end: {
          line: 468,
          column: 9
        }
      },
      "89": {
        start: {
          line: 464,
          column: 8
        },
        end: {
          line: 467,
          column: 9
        }
      },
      "90": {
        start: {
          line: 465,
          column: 31
        },
        end: {
          line: 465,
          column: 78
        }
      },
      "91": {
        start: {
          line: 466,
          column: 10
        },
        end: {
          line: 466,
          column: 99
        }
      },
      "92": {
        start: {
          line: 470,
          column: 6
        },
        end: {
          line: 470,
          column: 57
        }
      },
      "93": {
        start: {
          line: 471,
          column: 6
        },
        end: {
          line: 471,
          column: 41
        }
      },
      "94": {
        start: {
          line: 473,
          column: 6
        },
        end: {
          line: 473,
          column: 26
        }
      },
      "95": {
        start: {
          line: 490,
          column: 4
        },
        end: {
          line: 565,
          column: 5
        }
      },
      "96": {
        start: {
          line: 492,
          column: 39
        },
        end: {
          line: 503,
          column: 18
        }
      },
      "97": {
        start: {
          line: 505,
          column: 6
        },
        end: {
          line: 507,
          column: 7
        }
      },
      "98": {
        start: {
          line: 506,
          column: 8
        },
        end: {
          line: 506,
          column: 51
        }
      },
      "99": {
        start: {
          line: 510,
          column: 23
        },
        end: {
          line: 510,
          column: 24
        }
      },
      "100": {
        start: {
          line: 510,
          column: 42
        },
        end: {
          line: 510,
          column: 43
        }
      },
      "101": {
        start: {
          line: 510,
          column: 61
        },
        end: {
          line: 510,
          column: 62
        }
      },
      "102": {
        start: {
          line: 511,
          column: 24
        },
        end: {
          line: 511,
          column: 25
        }
      },
      "103": {
        start: {
          line: 511,
          column: 43
        },
        end: {
          line: 511,
          column: 44
        }
      },
      "104": {
        start: {
          line: 512,
          column: 25
        },
        end: {
          line: 512,
          column: 26
        }
      },
      "105": {
        start: {
          line: 514,
          column: 6
        },
        end: {
          line: 534,
          column: 9
        }
      },
      "106": {
        start: {
          line: 515,
          column: 8
        },
        end: {
          line: 533,
          column: 9
        }
      },
      "107": {
        start: {
          line: 516,
          column: 24
        },
        end: {
          line: 518,
          column: 30
        }
      },
      "108": {
        start: {
          line: 521,
          column: 30
        },
        end: {
          line: 521,
          column: 62
        }
      },
      "109": {
        start: {
          line: 522,
          column: 33
        },
        end: {
          line: 522,
          column: 78
        }
      },
      "110": {
        start: {
          line: 523,
          column: 33
        },
        end: {
          line: 523,
          column: 78
        }
      },
      "111": {
        start: {
          line: 524,
          column: 31
        },
        end: {
          line: 524,
          column: 64
        }
      },
      "112": {
        start: {
          line: 525,
          column: 33
        },
        end: {
          line: 525,
          column: 68
        }
      },
      "113": {
        start: {
          line: 527,
          column: 10
        },
        end: {
          line: 527,
          column: 36
        }
      },
      "114": {
        start: {
          line: 528,
          column: 10
        },
        end: {
          line: 528,
          column: 42
        }
      },
      "115": {
        start: {
          line: 529,
          column: 10
        },
        end: {
          line: 529,
          column: 42
        }
      },
      "116": {
        start: {
          line: 530,
          column: 10
        },
        end: {
          line: 530,
          column: 38
        }
      },
      "117": {
        start: {
          line: 531,
          column: 10
        },
        end: {
          line: 531,
          column: 42
        }
      },
      "118": {
        start: {
          line: 532,
          column: 10
        },
        end: {
          line: 532,
          column: 25
        }
      },
      "119": {
        start: {
          line: 536,
          column: 6
        },
        end: {
          line: 538,
          column: 7
        }
      },
      "120": {
        start: {
          line: 537,
          column: 8
        },
        end: {
          line: 537,
          column: 51
        }
      },
      "121": {
        start: {
          line: 540,
          column: 26
        },
        end: {
          line: 540,
          column: 63
        }
      },
      "122": {
        start: {
          line: 541,
          column: 29
        },
        end: {
          line: 541,
          column: 69
        }
      },
      "123": {
        start: {
          line: 542,
          column: 29
        },
        end: {
          line: 542,
          column: 69
        }
      },
      "124": {
        start: {
          line: 543,
          column: 27
        },
        end: {
          line: 543,
          column: 65
        }
      },
      "125": {
        start: {
          line: 544,
          column: 29
        },
        end: {
          line: 544,
          column: 69
        }
      },
      "126": {
        start: {
          line: 545,
          column: 28
        },
        end: {
          line: 547,
          column: 7
        }
      },
      "127": {
        start: {
          line: 550,
          column: 31
        },
        end: {
          line: 550,
          column: 90
        }
      },
      "128": {
        start: {
          line: 552,
          column: 6
        },
        end: {
          line: 561,
          column: 8
        }
      },
      "129": {
        start: {
          line: 563,
          column: 6
        },
        end: {
          line: 563,
          column: 65
        }
      },
      "130": {
        start: {
          line: 564,
          column: 6
        },
        end: {
          line: 564,
          column: 49
        }
      },
      "131": {
        start: {
          line: 581,
          column: 4
        },
        end: {
          line: 645,
          column: 5
        }
      },
      "132": {
        start: {
          line: 582,
          column: 25
        },
        end: {
          line: 582,
          column: 35
        }
      },
      "133": {
        start: {
          line: 583,
          column: 6
        },
        end: {
          line: 583,
          column: 51
        }
      },
      "134": {
        start: {
          line: 586,
          column: 40
        },
        end: {
          line: 590,
          column: 68
        }
      },
      "135": {
        start: {
          line: 592,
          column: 6
        },
        end: {
          line: 595,
          column: 7
        }
      },
      "136": {
        start: {
          line: 593,
          column: 8
        },
        end: {
          line: 593,
          column: 66
        }
      },
      "137": {
        start: {
          line: 594,
          column: 8
        },
        end: {
          line: 594,
          column: 44
        }
      },
      "138": {
        start: {
          line: 597,
          column: 27
        },
        end: {
          line: 597,
          column: 41
        }
      },
      "139": {
        start: {
          line: 598,
          column: 32
        },
        end: {
          line: 598,
          column: 51
        }
      },
      "140": {
        start: {
          line: 599,
          column: 32
        },
        end: {
          line: 601,
          column: 7
        }
      },
      "141": {
        start: {
          line: 600,
          column: 26
        },
        end: {
          line: 600,
          column: 63
        }
      },
      "142": {
        start: {
          line: 602,
          column: 27
        },
        end: {
          line: 606,
          column: 11
        }
      },
      "143": {
        start: {
          line: 604,
          column: 30
        },
        end: {
          line: 604,
          column: 64
        }
      },
      "144": {
        start: {
          line: 609,
          column: 26
        },
        end: {
          line: 609,
          column: 36
        }
      },
      "145": {
        start: {
          line: 610,
          column: 6
        },
        end: {
          line: 610,
          column: 54
        }
      },
      "146": {
        start: {
          line: 612,
          column: 45
        },
        end: {
          line: 617,
          column: 67
        }
      },
      "147": {
        start: {
          line: 619,
          column: 34
        },
        end: {
          line: 623,
          column: 11
        }
      },
      "148": {
        start: {
          line: 621,
          column: 30
        },
        end: {
          line: 621,
          column: 64
        }
      },
      "149": {
        start: {
          line: 625,
          column: 26
        },
        end: {
          line: 627,
          column: 11
        }
      },
      "150": {
        start: {
          line: 630,
          column: 29
        },
        end: {
          line: 633,
          column: 9
        }
      },
      "151": {
        start: {
          line: 630,
          column: 58
        },
        end: {
          line: 633,
          column: 7
        }
      },
      "152": {
        start: {
          line: 635,
          column: 6
        },
        end: {
          line: 641,
          column: 8
        }
      },
      "153": {
        start: {
          line: 643,
          column: 6
        },
        end: {
          line: 643,
          column: 63
        }
      },
      "154": {
        start: {
          line: 644,
          column: 6
        },
        end: {
          line: 644,
          column: 42
        }
      },
      "155": {
        start: {
          line: 652,
          column: 33
        },
        end: {
          line: 652,
          column: 64
        }
      },
      "156": {
        start: {
          line: 653,
          column: 17
        },
        end: {
          line: 653,
          column: 32
        }
      },
      "157": {
        start: {
          line: 654,
          column: 25
        },
        end: {
          line: 654,
          column: 48
        }
      },
      "158": {
        start: {
          line: 656,
          column: 4
        },
        end: {
          line: 658,
          column: 7
        }
      },
      "159": {
        start: {
          line: 665,
          column: 20
        },
        end: {
          line: 665,
          column: 54
        }
      },
      "160": {
        start: {
          line: 666,
          column: 19
        },
        end: {
          line: 666,
          column: 52
        }
      },
      "161": {
        start: {
          line: 668,
          column: 4
        },
        end: {
          line: 668,
          column: 67
        }
      },
      "162": {
        start: {
          line: 675,
          column: 31
        },
        end: {
          line: 675,
          column: 60
        }
      },
      "163": {
        start: {
          line: 676,
          column: 25
        },
        end: {
          line: 676,
          column: 48
        }
      },
      "164": {
        start: {
          line: 678,
          column: 4
        },
        end: {
          line: 678,
          column: 44
        }
      },
      "165": {
        start: {
          line: 678,
          column: 34
        },
        end: {
          line: 678,
          column: 44
        }
      },
      "166": {
        start: {
          line: 680,
          column: 4
        },
        end: {
          line: 680,
          column: 81
        }
      },
      "167": {
        start: {
          line: 687,
          column: 27
        },
        end: {
          line: 687,
          column: 52
        }
      },
      "168": {
        start: {
          line: 688,
          column: 30
        },
        end: {
          line: 688,
          column: 58
        }
      },
      "169": {
        start: {
          line: 690,
          column: 4
        },
        end: {
          line: 690,
          column: 88
        }
      },
      "170": {
        start: {
          line: 697,
          column: 4
        },
        end: {
          line: 743,
          column: 5
        }
      },
      "171": {
        start: {
          line: 698,
          column: 26
        },
        end: {
          line: 698,
          column: 36
        }
      },
      "172": {
        start: {
          line: 699,
          column: 6
        },
        end: {
          line: 699,
          column: 55
        }
      },
      "173": {
        start: {
          line: 701,
          column: 35
        },
        end: {
          line: 707,
          column: 17
        }
      },
      "174": {
        start: {
          line: 709,
          column: 6
        },
        end: {
          line: 711,
          column: 7
        }
      },
      "175": {
        start: {
          line: 710,
          column: 8
        },
        end: {
          line: 710,
          column: 17
        }
      },
      "176": {
        start: {
          line: 714,
          column: 27
        },
        end: {
          line: 714,
          column: 28
        }
      },
      "177": {
        start: {
          line: 715,
          column: 28
        },
        end: {
          line: 715,
          column: 29
        }
      },
      "178": {
        start: {
          line: 717,
          column: 6
        },
        end: {
          line: 734,
          column: 9
        }
      },
      "179": {
        start: {
          line: 718,
          column: 8
        },
        end: {
          line: 733,
          column: 9
        }
      },
      "180": {
        start: {
          line: 719,
          column: 24
        },
        end: {
          line: 721,
          column: 30
        }
      },
      "181": {
        start: {
          line: 723,
          column: 28
        },
        end: {
          line: 729,
          column: 15
        }
      },
      "182": {
        start: {
          line: 731,
          column: 10
        },
        end: {
          line: 731,
          column: 38
        }
      },
      "183": {
        start: {
          line: 732,
          column: 10
        },
        end: {
          line: 732,
          column: 28
        }
      },
      "184": {
        start: {
          line: 736,
          column: 6
        },
        end: {
          line: 736,
          column: 42
        }
      },
      "185": {
        start: {
          line: 736,
          column: 33
        },
        end: {
          line: 736,
          column: 42
        }
      },
      "186": {
        start: {
          line: 738,
          column: 31
        },
        end: {
          line: 738,
          column: 63
        }
      },
      "187": {
        start: {
          line: 739,
          column: 6
        },
        end: {
          line: 739,
          column: 58
        }
      },
      "188": {
        start: {
          line: 741,
          column: 6
        },
        end: {
          line: 741,
          column: 67
        }
      },
      "189": {
        start: {
          line: 742,
          column: 6
        },
        end: {
          line: 742,
          column: 15
        }
      },
      "190": {
        start: {
          line: 750,
          column: 4
        },
        end: {
          line: 759,
          column: 6
        }
      },
      "191": {
        start: {
          line: 766,
          column: 4
        },
        end: {
          line: 772,
          column: 6
        }
      },
      "192": {
        start: {
          line: 776,
          column: 26
        },
        end: {
          line: 776,
          column: 42
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 11,
            column: 2
          },
          end: {
            line: 11,
            column: 3
          }
        },
        loc: {
          start: {
            line: 11,
            column: 55
          },
          end: {
            line: 13,
            column: 3
          }
        },
        line: 11
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 12,
            column: 23
          },
          end: {
            line: 12,
            column: 24
          }
        },
        loc: {
          start: {
            line: 12,
            column: 34
          },
          end: {
            line: 12,
            column: 57
          }
        },
        line: 12
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 178,
            column: 2
          },
          end: {
            line: 178,
            column: 3
          }
        },
        loc: {
          start: {
            line: 178,
            column: 65
          },
          end: {
            line: 310,
            column: 3
          }
        },
        line: 178
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 312,
            column: 2
          },
          end: {
            line: 312,
            column: 3
          }
        },
        loc: {
          start: {
            line: 312,
            column: 72
          },
          end: {
            line: 398,
            column: 3
          }
        },
        line: 312
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 337,
            column: 44
          },
          end: {
            line: 337,
            column: 45
          }
        },
        loc: {
          start: {
            line: 337,
            column: 49
          },
          end: {
            line: 337,
            column: 56
          }
        },
        line: 337
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 400,
            column: 2
          },
          end: {
            line: 400,
            column: 3
          }
        },
        loc: {
          start: {
            line: 400,
            column: 70
          },
          end: {
            line: 419,
            column: 3
          }
        },
        line: 400
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 414,
            column: 55
          },
          end: {
            line: 414,
            column: 56
          }
        },
        loc: {
          start: {
            line: 414,
            column: 60
          },
          end: {
            line: 414,
            column: 83
          }
        },
        line: 414
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 421,
            column: 2
          },
          end: {
            line: 421,
            column: 3
          }
        },
        loc: {
          start: {
            line: 421,
            column: 62
          },
          end: {
            line: 475,
            column: 3
          }
        },
        line: 421
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 439,
            column: 24
          },
          end: {
            line: 439,
            column: 25
          }
        },
        loc: {
          start: {
            line: 439,
            column: 31
          },
          end: {
            line: 443,
            column: 7
          }
        },
        line: 439
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 463,
            column: 40
          },
          end: {
            line: 463,
            column: 41
          }
        },
        loc: {
          start: {
            line: 463,
            column: 47
          },
          end: {
            line: 468,
            column: 7
          }
        },
        line: 463
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 480,
            column: 2
          },
          end: {
            line: 480,
            column: 3
          }
        },
        loc: {
          start: {
            line: 489,
            column: 5
          },
          end: {
            line: 566,
            column: 3
          }
        },
        line: 489
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 514,
            column: 22
          },
          end: {
            line: 514,
            column: 23
          }
        },
        loc: {
          start: {
            line: 514,
            column: 31
          },
          end: {
            line: 534,
            column: 7
          }
        },
        line: 514
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 571,
            column: 2
          },
          end: {
            line: 571,
            column: 3
          }
        },
        loc: {
          start: {
            line: 580,
            column: 5
          },
          end: {
            line: 646,
            column: 3
          }
        },
        line: 580
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 600,
            column: 8
          },
          end: {
            line: 600,
            column: 9
          }
        },
        loc: {
          start: {
            line: 600,
            column: 26
          },
          end: {
            line: 600,
            column: 63
          }
        },
        line: 600
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 604,
            column: 12
          },
          end: {
            line: 604,
            column: 13
          }
        },
        loc: {
          start: {
            line: 604,
            column: 30
          },
          end: {
            line: 604,
            column: 64
          }
        },
        line: 604
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 621,
            column: 12
          },
          end: {
            line: 621,
            column: 13
          }
        },
        loc: {
          start: {
            line: 621,
            column: 30
          },
          end: {
            line: 621,
            column: 64
          }
        },
        line: 621
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 630,
            column: 46
          },
          end: {
            line: 630,
            column: 47
          }
        },
        loc: {
          start: {
            line: 630,
            column: 58
          },
          end: {
            line: 633,
            column: 7
          }
        },
        line: 630
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 651,
            column: 2
          },
          end: {
            line: 651,
            column: 3
          }
        },
        loc: {
          start: {
            line: 651,
            column: 51
          },
          end: {
            line: 659,
            column: 3
          }
        },
        line: 651
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 664,
            column: 2
          },
          end: {
            line: 664,
            column: 3
          }
        },
        loc: {
          start: {
            line: 664,
            column: 89
          },
          end: {
            line: 669,
            column: 3
          }
        },
        line: 664
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 674,
            column: 2
          },
          end: {
            line: 674,
            column: 3
          }
        },
        loc: {
          start: {
            line: 674,
            column: 52
          },
          end: {
            line: 681,
            column: 3
          }
        },
        line: 674
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 686,
            column: 2
          },
          end: {
            line: 686,
            column: 3
          }
        },
        loc: {
          start: {
            line: 686,
            column: 54
          },
          end: {
            line: 691,
            column: 3
          }
        },
        line: 686
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 696,
            column: 2
          },
          end: {
            line: 696,
            column: 3
          }
        },
        loc: {
          start: {
            line: 696,
            column: 98
          },
          end: {
            line: 744,
            column: 3
          }
        },
        line: 696
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 717,
            column: 25
          },
          end: {
            line: 717,
            column: 26
          }
        },
        loc: {
          start: {
            line: 717,
            column: 34
          },
          end: {
            line: 734,
            column: 7
          }
        },
        line: 717
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 749,
            column: 2
          },
          end: {
            line: 749,
            column: 3
          }
        },
        loc: {
          start: {
            line: 749,
            column: 41
          },
          end: {
            line: 760,
            column: 3
          }
        },
        line: 749
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 765,
            column: 2
          },
          end: {
            line: 765,
            column: 3
          }
        },
        loc: {
          start: {
            line: 765,
            column: 34
          },
          end: {
            line: 773,
            column: 3
          }
        },
        line: 765
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 7,
            column: 20
          },
          end: {
            line: 7,
            column: 94
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 7,
            column: 20
          },
          end: {
            line: 7,
            column: 56
          }
        }, {
          start: {
            line: 7,
            column: 60
          },
          end: {
            line: 7,
            column: 94
          }
        }],
        line: 7
      },
      "1": {
        loc: {
          start: {
            line: 8,
            column: 19
          },
          end: {
            line: 8,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 19
          },
          end: {
            line: 8,
            column: 60
          }
        }, {
          start: {
            line: 8,
            column: 64
          },
          end: {
            line: 8,
            column: 79
          }
        }],
        line: 8
      },
      "2": {
        loc: {
          start: {
            line: 11,
            column: 22
          },
          end: {
            line: 11,
            column: 38
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 11,
            column: 35
          },
          end: {
            line: 11,
            column: 38
          }
        }],
        line: 11
      },
      "3": {
        loc: {
          start: {
            line: 187,
            column: 6
          },
          end: {
            line: 190,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 187,
            column: 6
          },
          end: {
            line: 190,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 187
      },
      "4": {
        loc: {
          start: {
            line: 199,
            column: 6
          },
          end: {
            line: 223,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 6
          },
          end: {
            line: 223,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 199
      },
      "5": {
        loc: {
          start: {
            line: 217,
            column: 8
          },
          end: {
            line: 219,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 217,
            column: 8
          },
          end: {
            line: 219,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 217
      },
      "6": {
        loc: {
          start: {
            line: 222,
            column: 32
          },
          end: {
            line: 222,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 222,
            column: 32
          },
          end: {
            line: 222,
            column: 45
          }
        }, {
          start: {
            line: 222,
            column: 49
          },
          end: {
            line: 222,
            column: 68
          }
        }],
        line: 222
      },
      "7": {
        loc: {
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 235,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 233
      },
      "8": {
        loc: {
          start: {
            line: 245,
            column: 6
          },
          end: {
            line: 247,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 6
          },
          end: {
            line: 247,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "9": {
        loc: {
          start: {
            line: 249,
            column: 26
          },
          end: {
            line: 249,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 76
          },
          end: {
            line: 249,
            column: 95
          }
        }, {
          start: {
            line: 249,
            column: 98
          },
          end: {
            line: 249,
            column: 102
          }
        }],
        line: 249
      },
      "10": {
        loc: {
          start: {
            line: 249,
            column: 26
          },
          end: {
            line: 249,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 249,
            column: 26
          },
          end: {
            line: 249,
            column: 42
          }
        }, {
          start: {
            line: 249,
            column: 46
          },
          end: {
            line: 249,
            column: 73
          }
        }],
        line: 249
      },
      "11": {
        loc: {
          start: {
            line: 258,
            column: 6
          },
          end: {
            line: 260,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 258,
            column: 6
          },
          end: {
            line: 260,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 258
      },
      "12": {
        loc: {
          start: {
            line: 270,
            column: 6
          },
          end: {
            line: 272,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 6
          },
          end: {
            line: 272,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "13": {
        loc: {
          start: {
            line: 282,
            column: 6
          },
          end: {
            line: 284,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 282,
            column: 6
          },
          end: {
            line: 284,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 282
      },
      "14": {
        loc: {
          start: {
            line: 286,
            column: 23
          },
          end: {
            line: 286,
            column: 103
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 286,
            column: 67
          },
          end: {
            line: 286,
            column: 83
          }
        }, {
          start: {
            line: 286,
            column: 86
          },
          end: {
            line: 286,
            column: 103
          }
        }],
        line: 286
      },
      "15": {
        loc: {
          start: {
            line: 286,
            column: 23
          },
          end: {
            line: 286,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 286,
            column: 23
          },
          end: {
            line: 286,
            column: 36
          }
        }, {
          start: {
            line: 286,
            column: 40
          },
          end: {
            line: 286,
            column: 64
          }
        }],
        line: 286
      },
      "16": {
        loc: {
          start: {
            line: 289,
            column: 14
          },
          end: {
            line: 289,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 289,
            column: 14
          },
          end: {
            line: 289,
            column: 18
          }
        }, {
          start: {
            line: 289,
            column: 22
          },
          end: {
            line: 289,
            column: 35
          }
        }],
        line: 289
      },
      "17": {
        loc: {
          start: {
            line: 290,
            column: 20
          },
          end: {
            line: 290,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 20
          },
          end: {
            line: 290,
            column: 30
          }
        }, {
          start: {
            line: 290,
            column: 34
          },
          end: {
            line: 290,
            column: 53
          }
        }],
        line: 290
      },
      "18": {
        loc: {
          start: {
            line: 291,
            column: 24
          },
          end: {
            line: 291,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 24
          },
          end: {
            line: 291,
            column: 38
          }
        }, {
          start: {
            line: 291,
            column: 42
          },
          end: {
            line: 291,
            column: 65
          }
        }],
        line: 291
      },
      "19": {
        loc: {
          start: {
            line: 293,
            column: 22
          },
          end: {
            line: 293,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 293,
            column: 22
          },
          end: {
            line: 293,
            column: 34
          }
        }, {
          start: {
            line: 293,
            column: 38
          },
          end: {
            line: 293,
            column: 59
          }
        }],
        line: 293
      },
      "20": {
        loc: {
          start: {
            line: 294,
            column: 23
          },
          end: {
            line: 294,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 294,
            column: 23
          },
          end: {
            line: 294,
            column: 36
          }
        }, {
          start: {
            line: 294,
            column: 40
          },
          end: {
            line: 294,
            column: 62
          }
        }],
        line: 294
      },
      "21": {
        loc: {
          start: {
            line: 336,
            column: 20
          },
          end: {
            line: 336,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 336,
            column: 20
          },
          end: {
            line: 336,
            column: 37
          }
        }, {
          start: {
            line: 336,
            column: 41
          },
          end: {
            line: 336,
            column: 55
          }
        }],
        line: 336
      },
      "22": {
        loc: {
          start: {
            line: 337,
            column: 24
          },
          end: {
            line: 337,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 337,
            column: 24
          },
          end: {
            line: 337,
            column: 57
          }
        }, {
          start: {
            line: 337,
            column: 61
          },
          end: {
            line: 337,
            column: 63
          }
        }],
        line: 337
      },
      "23": {
        loc: {
          start: {
            line: 338,
            column: 22
          },
          end: {
            line: 341,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 338,
            column: 22
          },
          end: {
            line: 338,
            column: 32
          }
        }, {
          start: {
            line: 338,
            column: 36
          },
          end: {
            line: 341,
            column: 9
          }
        }],
        line: 338
      },
      "24": {
        loc: {
          start: {
            line: 342,
            column: 17
          },
          end: {
            line: 342,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 342,
            column: 17
          },
          end: {
            line: 342,
            column: 24
          }
        }, {
          start: {
            line: 342,
            column: 28
          },
          end: {
            line: 342,
            column: 50
          }
        }],
        line: 342
      },
      "25": {
        loc: {
          start: {
            line: 362,
            column: 6
          },
          end: {
            line: 373,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 362,
            column: 6
          },
          end: {
            line: 373,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 362
      },
      "26": {
        loc: {
          start: {
            line: 407,
            column: 6
          },
          end: {
            line: 410,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 407,
            column: 6
          },
          end: {
            line: 410,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 407
      },
      "27": {
        loc: {
          start: {
            line: 415,
            column: 6
          },
          end: {
            line: 417,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 6
          },
          end: {
            line: 417,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "28": {
        loc: {
          start: {
            line: 430,
            column: 6
          },
          end: {
            line: 433,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 430,
            column: 6
          },
          end: {
            line: 433,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 430
      },
      "29": {
        loc: {
          start: {
            line: 453,
            column: 6
          },
          end: {
            line: 456,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 453,
            column: 6
          },
          end: {
            line: 456,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 453
      },
      "30": {
        loc: {
          start: {
            line: 464,
            column: 8
          },
          end: {
            line: 467,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 464,
            column: 8
          },
          end: {
            line: 467,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 464
      },
      "31": {
        loc: {
          start: {
            line: 464,
            column: 12
          },
          end: {
            line: 464,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 464,
            column: 12
          },
          end: {
            line: 464,
            column: 69
          }
        }, {
          start: {
            line: 464,
            column: 73
          },
          end: {
            line: 464,
            column: 85
          }
        }],
        line: 464
      },
      "32": {
        loc: {
          start: {
            line: 505,
            column: 6
          },
          end: {
            line: 507,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 505,
            column: 6
          },
          end: {
            line: 507,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 505
      },
      "33": {
        loc: {
          start: {
            line: 505,
            column: 10
          },
          end: {
            line: 505,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 505,
            column: 10
          },
          end: {
            line: 505,
            column: 15
          }
        }, {
          start: {
            line: 505,
            column: 19
          },
          end: {
            line: 505,
            column: 27
          }
        }, {
          start: {
            line: 505,
            column: 31
          },
          end: {
            line: 505,
            column: 51
          }
        }],
        line: 505
      },
      "34": {
        loc: {
          start: {
            line: 515,
            column: 8
          },
          end: {
            line: 533,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 515,
            column: 8
          },
          end: {
            line: 533,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 515
      },
      "35": {
        loc: {
          start: {
            line: 516,
            column: 24
          },
          end: {
            line: 518,
            column: 30
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 517,
            column: 14
          },
          end: {
            line: 517,
            column: 42
          }
        }, {
          start: {
            line: 518,
            column: 14
          },
          end: {
            line: 518,
            column: 30
          }
        }],
        line: 516
      },
      "36": {
        loc: {
          start: {
            line: 536,
            column: 6
          },
          end: {
            line: 538,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 536,
            column: 6
          },
          end: {
            line: 538,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 536
      },
      "37": {
        loc: {
          start: {
            line: 592,
            column: 6
          },
          end: {
            line: 595,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 592,
            column: 6
          },
          end: {
            line: 595,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 592
      },
      "38": {
        loc: {
          start: {
            line: 597,
            column: 27
          },
          end: {
            line: 597,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 597,
            column: 27
          },
          end: {
            line: 597,
            column: 35
          }
        }, {
          start: {
            line: 597,
            column: 39
          },
          end: {
            line: 597,
            column: 41
          }
        }],
        line: 597
      },
      "39": {
        loc: {
          start: {
            line: 600,
            column: 33
          },
          end: {
            line: 600,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 600,
            column: 33
          },
          end: {
            line: 600,
            column: 57
          }
        }, {
          start: {
            line: 600,
            column: 61
          },
          end: {
            line: 600,
            column: 62
          }
        }],
        line: 600
      },
      "40": {
        loc: {
          start: {
            line: 602,
            column: 27
          },
          end: {
            line: 606,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 603,
            column: 10
          },
          end: {
            line: 605,
            column: 32
          }
        }, {
          start: {
            line: 606,
            column: 10
          },
          end: {
            line: 606,
            column: 11
          }
        }],
        line: 602
      },
      "41": {
        loc: {
          start: {
            line: 604,
            column: 37
          },
          end: {
            line: 604,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 604,
            column: 37
          },
          end: {
            line: 604,
            column: 58
          }
        }, {
          start: {
            line: 604,
            column: 62
          },
          end: {
            line: 604,
            column: 63
          }
        }],
        line: 604
      },
      "42": {
        loc: {
          start: {
            line: 619,
            column: 34
          },
          end: {
            line: 623,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 620,
            column: 10
          },
          end: {
            line: 622,
            column: 41
          }
        }, {
          start: {
            line: 623,
            column: 10
          },
          end: {
            line: 623,
            column: 11
          }
        }],
        line: 619
      },
      "43": {
        loc: {
          start: {
            line: 619,
            column: 34
          },
          end: {
            line: 619,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 619,
            column: 34
          },
          end: {
            line: 619,
            column: 54
          }
        }, {
          start: {
            line: 619,
            column: 58
          },
          end: {
            line: 619,
            column: 89
          }
        }],
        line: 619
      },
      "44": {
        loc: {
          start: {
            line: 621,
            column: 37
          },
          end: {
            line: 621,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 621,
            column: 37
          },
          end: {
            line: 621,
            column: 58
          }
        }, {
          start: {
            line: 621,
            column: 62
          },
          end: {
            line: 621,
            column: 63
          }
        }],
        line: 621
      },
      "45": {
        loc: {
          start: {
            line: 625,
            column: 26
          },
          end: {
            line: 627,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 626,
            column: 10
          },
          end: {
            line: 626,
            column: 88
          }
        }, {
          start: {
            line: 627,
            column: 10
          },
          end: {
            line: 627,
            column: 11
          }
        }],
        line: 625
      },
      "46": {
        loc: {
          start: {
            line: 631,
            column: 26
          },
          end: {
            line: 631,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 631,
            column: 26
          },
          end: {
            line: 631,
            column: 51
          }
        }, {
          start: {
            line: 631,
            column: 55
          },
          end: {
            line: 631,
            column: 90
          }
        }],
        line: 631
      },
      "47": {
        loc: {
          start: {
            line: 632,
            column: 27
          },
          end: {
            line: 632,
            column: 92
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 632,
            column: 27
          },
          end: {
            line: 632,
            column: 53
          }
        }, {
          start: {
            line: 632,
            column: 57
          },
          end: {
            line: 632,
            column: 92
          }
        }],
        line: 632
      },
      "48": {
        loc: {
          start: {
            line: 652,
            column: 33
          },
          end: {
            line: 652,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 652,
            column: 33
          },
          end: {
            line: 652,
            column: 59
          }
        }, {
          start: {
            line: 652,
            column: 63
          },
          end: {
            line: 652,
            column: 64
          }
        }],
        line: 652
      },
      "49": {
        loc: {
          start: {
            line: 653,
            column: 17
          },
          end: {
            line: 653,
            column: 32
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 653,
            column: 17
          },
          end: {
            line: 653,
            column: 27
          }
        }, {
          start: {
            line: 653,
            column: 31
          },
          end: {
            line: 653,
            column: 32
          }
        }],
        line: 653
      },
      "50": {
        loc: {
          start: {
            line: 654,
            column: 25
          },
          end: {
            line: 654,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 654,
            column: 25
          },
          end: {
            line: 654,
            column: 43
          }
        }, {
          start: {
            line: 654,
            column: 47
          },
          end: {
            line: 654,
            column: 48
          }
        }],
        line: 654
      },
      "51": {
        loc: {
          start: {
            line: 665,
            column: 20
          },
          end: {
            line: 665,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 665,
            column: 20
          },
          end: {
            line: 665,
            column: 49
          }
        }, {
          start: {
            line: 665,
            column: 53
          },
          end: {
            line: 665,
            column: 54
          }
        }],
        line: 665
      },
      "52": {
        loc: {
          start: {
            line: 666,
            column: 19
          },
          end: {
            line: 666,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 666,
            column: 19
          },
          end: {
            line: 666,
            column: 47
          }
        }, {
          start: {
            line: 666,
            column: 51
          },
          end: {
            line: 666,
            column: 52
          }
        }],
        line: 666
      },
      "53": {
        loc: {
          start: {
            line: 675,
            column: 31
          },
          end: {
            line: 675,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 675,
            column: 31
          },
          end: {
            line: 675,
            column: 55
          }
        }, {
          start: {
            line: 675,
            column: 59
          },
          end: {
            line: 675,
            column: 60
          }
        }],
        line: 675
      },
      "54": {
        loc: {
          start: {
            line: 676,
            column: 25
          },
          end: {
            line: 676,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 676,
            column: 25
          },
          end: {
            line: 676,
            column: 43
          }
        }, {
          start: {
            line: 676,
            column: 47
          },
          end: {
            line: 676,
            column: 48
          }
        }],
        line: 676
      },
      "55": {
        loc: {
          start: {
            line: 678,
            column: 4
          },
          end: {
            line: 678,
            column: 44
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 678,
            column: 4
          },
          end: {
            line: 678,
            column: 44
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 678
      },
      "56": {
        loc: {
          start: {
            line: 687,
            column: 27
          },
          end: {
            line: 687,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 687,
            column: 27
          },
          end: {
            line: 687,
            column: 47
          }
        }, {
          start: {
            line: 687,
            column: 51
          },
          end: {
            line: 687,
            column: 52
          }
        }],
        line: 687
      },
      "57": {
        loc: {
          start: {
            line: 688,
            column: 30
          },
          end: {
            line: 688,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 688,
            column: 30
          },
          end: {
            line: 688,
            column: 53
          }
        }, {
          start: {
            line: 688,
            column: 57
          },
          end: {
            line: 688,
            column: 58
          }
        }],
        line: 688
      },
      "58": {
        loc: {
          start: {
            line: 709,
            column: 6
          },
          end: {
            line: 711,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 709,
            column: 6
          },
          end: {
            line: 711,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 709
      },
      "59": {
        loc: {
          start: {
            line: 709,
            column: 10
          },
          end: {
            line: 709,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 709,
            column: 10
          },
          end: {
            line: 709,
            column: 21
          }
        }, {
          start: {
            line: 709,
            column: 25
          },
          end: {
            line: 709,
            column: 48
          }
        }],
        line: 709
      },
      "60": {
        loc: {
          start: {
            line: 718,
            column: 8
          },
          end: {
            line: 733,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 718,
            column: 8
          },
          end: {
            line: 733,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 718
      },
      "61": {
        loc: {
          start: {
            line: 719,
            column: 24
          },
          end: {
            line: 721,
            column: 30
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 720,
            column: 14
          },
          end: {
            line: 720,
            column: 42
          }
        }, {
          start: {
            line: 721,
            column: 14
          },
          end: {
            line: 721,
            column: 30
          }
        }],
        line: 719
      },
      "62": {
        loc: {
          start: {
            line: 736,
            column: 6
          },
          end: {
            line: 736,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 736,
            column: 6
          },
          end: {
            line: 736,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 736
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0, 0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f0808e9b24f062926c6678b8fd89b82e2dacefdd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_29vg1lngs = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_29vg1lngs();
var ApiService = function () {
  function ApiService() {
    (0, _classCallCheck2.default)(this, ApiService);
    this.baseUrl = (cov_29vg1lngs().s[0]++, (cov_29vg1lngs().b[0][0]++, _env2.env.EXPO_PUBLIC_SUPABASE_URL) || (cov_29vg1lngs().b[0][1]++, 'https://your-project.supabase.co'));
    this.apiKey = (cov_29vg1lngs().s[1]++, (cov_29vg1lngs().b[1][0]++, _env2.env.EXPO_PUBLIC_SUPABASE_ANON_KEY) || (cov_29vg1lngs().b[1][1]++, 'your-anon-key'));
    this.mockUser = (cov_29vg1lngs().s[4]++, {
      id: 'user-sara-lee-123',
      email: '<EMAIL>',
      full_name: 'Sara Lee',
      skill_level: 'club',
      preferred_surface: 'hard',
      goals: ['serve', 'mental'],
      created_at: '2024-01-15T10:00:00Z',
      updated_at: '2024-12-20T15:30:00Z'
    });
    this.mockSkillStats = (cov_29vg1lngs().s[5]++, {
      id: 'stats-sara-123',
      user_id: 'user-sara-lee-123',
      forehand: 78,
      backhand: 65,
      serve: 82,
      volley: 70,
      footwork: 75,
      strategy: 68,
      mental_game: 73,
      updated_at: '2024-12-20T15:30:00Z'
    });
    this.mockRecentSessions = (cov_29vg1lngs().s[6]++, [{
      id: 'session-1',
      user_id: 'user-sara-lee-123',
      session_type: 'video_analysis',
      title: 'Forehand Technique Analysis',
      description: 'Analyzed crosscourt forehand consistency',
      duration_minutes: 45,
      ai_feedback_summary: 'Excellent toss placement and follow-through. Work on knee bend for more power.',
      improvement_areas: ['Follow-through', 'Knee bend', 'Contact point'],
      skill_improvements: {
        forehand: 5,
        serve: 2
      },
      video_url: 'https://example.com/video1.mp4',
      created_at: '2024-12-20T14:00:00Z'
    }, {
      id: 'session-2',
      user_id: 'user-sara-lee-123',
      session_type: 'match_simulation',
      title: 'vs AI Aggressive Baseliner',
      description: 'Simulated match against aggressive playing style',
      duration_minutes: 90,
      ai_feedback_summary: 'Great court positioning! Focus on varying shot placement to keep opponent guessing.',
      improvement_areas: ['Shot variety', 'Net approaches', 'Defensive positioning'],
      skill_improvements: {
        strategy: 3,
        mental_game: 4
      },
      created_at: '2024-12-19T16:30:00Z'
    }, {
      id: 'session-3',
      user_id: 'user-sara-lee-123',
      session_type: 'drill_practice',
      title: 'Serve Placement Drill',
      description: 'Practiced targeting different service boxes',
      duration_minutes: 30,
      ai_feedback_summary: 'Improved accuracy by 15%! Keep working on second serve consistency.',
      improvement_areas: ['Second serve', 'Placement accuracy'],
      skill_improvements: {
        serve: 3
      },
      created_at: '2024-12-18T11:00:00Z'
    }]);
    this.mockLatestMatch = (cov_29vg1lngs().s[7]++, {
      id: 'match-1',
      user_id: 'user-sara-lee-123',
      opponent_name: 'AI: Aggressive Baseliner',
      opponent_type: 'ai',
      match_score: '6-3, 3-6, 6-4',
      sets: [6, 3, 6],
      opponent_sets: [3, 6, 4],
      surface: 'Hard Court',
      duration_minutes: 134,
      result: 'win',
      match_stats: {
        winners: 23,
        unforced_errors: 18,
        aces: 7,
        double_faults: 4,
        first_serve_percentage: 68,
        break_points_converted: '4/7'
      },
      created_at: '2024-12-19T18:45:00Z'
    });
    this.mockAchievements = (cov_29vg1lngs().s[8]++, [{
      id: 'achievement-1',
      user_id: 'user-sara-lee-123',
      badge_type: 'serve_master',
      title: 'Serve Master',
      description: 'Achieved 80%+ serve accuracy',
      icon: 'trophy',
      color: '#ffe600',
      unlocked_at: '2024-12-18T20:00:00Z'
    }, {
      id: 'achievement-2',
      user_id: 'user-sara-lee-123',
      badge_type: 'consistency_king',
      title: 'Consistency King',
      description: 'Complete 7 days of training',
      icon: 'target',
      color: '#23ba16',
      unlocked_at: '2024-12-15T09:00:00Z'
    }, {
      id: 'achievement-3',
      user_id: 'user-sara-lee-123',
      badge_type: 'video_analyst',
      title: 'Video Analyst',
      description: 'Upload 10 training videos',
      icon: 'bar-chart',
      color: '#23ba16',
      unlocked_at: '2024-12-20T16:00:00Z',
      progress: 7,
      total: 10
    }]);
    this.mockNotifications = (cov_29vg1lngs().s[9]++, [{
      id: 'notif-1',
      user_id: 'user-sara-lee-123',
      type: 'achievement',
      title: 'New Badge Unlocked!',
      message: 'You earned the "Serve Master" badge for achieving 80%+ serve accuracy!',
      read: false,
      created_at: '2024-12-18T20:00:00Z'
    }, {
      id: 'notif-2',
      user_id: 'user-sara-lee-123',
      type: 'tip',
      title: 'Daily AI Tip',
      message: 'Focus on keeping your wrist loose during serves for more power.',
      read: false,
      created_at: '2024-12-20T08:00:00Z'
    }, {
      id: 'notif-3',
      user_id: 'user-sara-lee-123',
      type: 'match_result',
      title: 'Match Complete',
      message: 'Great win against AI Aggressive Baseliner! Check your analysis.',
      read: true,
      created_at: '2024-12-19T18:50:00Z'
    }]);
    this.mockDailyTip = (cov_29vg1lngs().s[10]++, {
      id: 'tip-daily-1',
      user_id: 'user-sara-lee-123',
      tip_text: 'Focus on your follow-through when hitting forehands. Keep your racquet head up and finish with your elbow pointing towards your target for better topspin and control.',
      category: 'technique',
      personalized: true,
      created_at: '2024-12-20T08:00:00Z'
    });
  }
  return (0, _createClass2.default)(ApiService, [{
    key: "delay",
    value: function () {
      var _delay = (0, _asyncToGenerator2.default)(function* () {
        var ms = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_29vg1lngs().b[2][0]++, 800);
        cov_29vg1lngs().f[0]++;
        cov_29vg1lngs().s[2]++;
        return new Promise(function (resolve) {
          cov_29vg1lngs().f[1]++;
          cov_29vg1lngs().s[3]++;
          return setTimeout(resolve, ms);
        });
      });
      function delay() {
        return _delay.apply(this, arguments);
      }
      return delay;
    }()
  }, {
    key: "getDashboardData",
    value: function () {
      var _getDashboardData = (0, _asyncToGenerator2.default)(function* (userId) {
        cov_29vg1lngs().f[2]++;
        cov_29vg1lngs().s[11]++;
        try {
          var _ref = (cov_29vg1lngs().s[12]++, yield _supabase.supabase.from('users').select('*').eq('id', userId).single()),
            user = _ref.data,
            userError = _ref.error;
          cov_29vg1lngs().s[13]++;
          if (userError) {
            cov_29vg1lngs().b[3][0]++;
            cov_29vg1lngs().s[14]++;
            console.error('Error fetching user:', userError);
            cov_29vg1lngs().s[15]++;
            throw new Error('Failed to fetch user data');
          } else {
            cov_29vg1lngs().b[3][1]++;
          }
          var _ref2 = (cov_29vg1lngs().s[16]++, yield _supabase.supabase.from('skill_stats').select('*').eq('user_id', userId).single()),
            skillStats = _ref2.data,
            skillStatsError = _ref2.error;
          cov_29vg1lngs().s[17]++;
          if (skillStatsError) {
            cov_29vg1lngs().b[4][0]++;
            cov_29vg1lngs().s[18]++;
            console.error('Error fetching skill stats:', skillStatsError);
            var _ref3 = (cov_29vg1lngs().s[19]++, yield _supabase.supabase.from('skill_stats').insert({
                user_id: userId,
                forehand: 50,
                backhand: 50,
                serve: 50,
                volley: 50,
                footwork: 50,
                strategy: 50,
                mental_game: 50
              }).select().single()),
              newSkillStats = _ref3.data,
              createError = _ref3.error;
            cov_29vg1lngs().s[20]++;
            if (createError) {
              cov_29vg1lngs().b[5][0]++;
              cov_29vg1lngs().s[21]++;
              throw new Error('Failed to create skill stats');
            } else {
              cov_29vg1lngs().b[5][1]++;
            }
            var finalSkillStats = (cov_29vg1lngs().s[22]++, (cov_29vg1lngs().b[6][0]++, newSkillStats) || (cov_29vg1lngs().b[6][1]++, this.mockSkillStats));
          } else {
            cov_29vg1lngs().b[4][1]++;
          }
          var _ref4 = (cov_29vg1lngs().s[23]++, yield _supabase.supabase.from('training_sessions').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(3)),
            recentSessions = _ref4.data,
            sessionsError = _ref4.error;
          cov_29vg1lngs().s[24]++;
          if (sessionsError) {
            cov_29vg1lngs().b[7][0]++;
            cov_29vg1lngs().s[25]++;
            console.error('Error fetching training sessions:', sessionsError);
          } else {
            cov_29vg1lngs().b[7][1]++;
          }
          var _ref5 = (cov_29vg1lngs().s[26]++, yield _supabase.supabase.from('match_results').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(1)),
            latestMatchArray = _ref5.data,
            matchError = _ref5.error;
          cov_29vg1lngs().s[27]++;
          if (matchError) {
            cov_29vg1lngs().b[8][0]++;
            cov_29vg1lngs().s[28]++;
            console.error('Error fetching match results:', matchError);
          } else {
            cov_29vg1lngs().b[8][1]++;
          }
          var latestMatch = (cov_29vg1lngs().s[29]++, (cov_29vg1lngs().b[10][0]++, latestMatchArray) && (cov_29vg1lngs().b[10][1]++, latestMatchArray.length > 0) ? (cov_29vg1lngs().b[9][0]++, latestMatchArray[0]) : (cov_29vg1lngs().b[9][1]++, null));
          var _ref6 = (cov_29vg1lngs().s[30]++, yield _supabase.supabase.from('achievements').select('*').eq('user_id', userId).order('unlocked_at', {
              ascending: false
            })),
            achievements = _ref6.data,
            achievementsError = _ref6.error;
          cov_29vg1lngs().s[31]++;
          if (achievementsError) {
            cov_29vg1lngs().b[11][0]++;
            cov_29vg1lngs().s[32]++;
            console.error('Error fetching achievements:', achievementsError);
          } else {
            cov_29vg1lngs().b[11][1]++;
          }
          var _ref7 = (cov_29vg1lngs().s[33]++, yield _supabase.supabase.from('notifications').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(10)),
            notifications = _ref7.data,
            notificationsError = _ref7.error;
          cov_29vg1lngs().s[34]++;
          if (notificationsError) {
            cov_29vg1lngs().b[12][0]++;
            cov_29vg1lngs().s[35]++;
            console.error('Error fetching notifications:', notificationsError);
          } else {
            cov_29vg1lngs().b[12][1]++;
          }
          var _ref8 = (cov_29vg1lngs().s[36]++, yield _supabase.supabase.from('ai_tips').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(1)),
            dailyTipArray = _ref8.data,
            tipError = _ref8.error;
          cov_29vg1lngs().s[37]++;
          if (tipError) {
            cov_29vg1lngs().b[13][0]++;
            cov_29vg1lngs().s[38]++;
            console.error('Error fetching daily tip:', tipError);
          } else {
            cov_29vg1lngs().b[13][1]++;
          }
          var dailyTip = (cov_29vg1lngs().s[39]++, (cov_29vg1lngs().b[15][0]++, dailyTipArray) && (cov_29vg1lngs().b[15][1]++, dailyTipArray.length > 0) ? (cov_29vg1lngs().b[14][0]++, dailyTipArray[0]) : (cov_29vg1lngs().b[14][1]++, this.mockDailyTip));
          cov_29vg1lngs().s[40]++;
          return {
            user: (cov_29vg1lngs().b[16][0]++, user) || (cov_29vg1lngs().b[16][1]++, this.mockUser),
            skillStats: (cov_29vg1lngs().b[17][0]++, skillStats) || (cov_29vg1lngs().b[17][1]++, this.mockSkillStats),
            recentSessions: (cov_29vg1lngs().b[18][0]++, recentSessions) || (cov_29vg1lngs().b[18][1]++, this.mockRecentSessions),
            latestMatch: latestMatch,
            achievements: (cov_29vg1lngs().b[19][0]++, achievements) || (cov_29vg1lngs().b[19][1]++, this.mockAchievements),
            notifications: (cov_29vg1lngs().b[20][0]++, notifications) || (cov_29vg1lngs().b[20][1]++, this.mockNotifications),
            dailyTip: dailyTip
          };
        } catch (error) {
          cov_29vg1lngs().s[41]++;
          console.error('Error in getDashboardData:', error);
          cov_29vg1lngs().s[42]++;
          return {
            user: this.mockUser,
            skillStats: this.mockSkillStats,
            recentSessions: this.mockRecentSessions,
            latestMatch: this.mockLatestMatch,
            achievements: this.mockAchievements,
            notifications: this.mockNotifications,
            dailyTip: this.mockDailyTip
          };
        }
      });
      function getDashboardData(_x) {
        return _getDashboardData.apply(this, arguments);
      }
      return getDashboardData;
    }()
  }, {
    key: "generateAITip",
    value: function () {
      var _generateAITip = (0, _asyncToGenerator2.default)(function* (userId, context) {
        cov_29vg1lngs().f[3]++;
        cov_29vg1lngs().s[43]++;
        try {
          var _ref9 = (cov_29vg1lngs().s[44]++, yield _supabase.supabase.from('users').select('*').eq('id', userId).single()),
            user = _ref9.data;
          var _ref0 = (cov_29vg1lngs().s[45]++, yield _supabase.supabase.from('skill_stats').select('*').eq('user_id', userId).single()),
            skillStats = _ref0.data;
          var _ref1 = (cov_29vg1lngs().s[46]++, yield _supabase.supabase.from('training_sessions').select('title').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(3)),
            recentSessions = _ref1.data;
          var coachingRequest = (cov_29vg1lngs().s[47]++, {
            skillLevel: (cov_29vg1lngs().b[21][0]++, user == null ? void 0 : user.skill_level) || (cov_29vg1lngs().b[21][1]++, 'intermediate'),
            recentSessions: (cov_29vg1lngs().b[22][0]++, recentSessions == null ? void 0 : recentSessions.map(function (s) {
              cov_29vg1lngs().f[4]++;
              cov_29vg1lngs().s[48]++;
              return s.title;
            })) || (cov_29vg1lngs().b[22][1]++, []),
            currentStats: (cov_29vg1lngs().b[23][0]++, skillStats) || (cov_29vg1lngs().b[23][1]++, {
              forehand: 50,
              backhand: 50,
              serve: 50,
              volley: 50,
              footwork: 50,
              strategy: 50,
              mental_game: 50
            }),
            context: (cov_29vg1lngs().b[24][0]++, context) || (cov_29vg1lngs().b[24][1]++, 'daily tip generation')
          });
          var aiCoaching = (cov_29vg1lngs().s[49]++, yield _openai.openAIService.generateCoachingAdvice(coachingRequest));
          var tipText = (cov_29vg1lngs().s[50]++, aiCoaching.personalizedTip);
          var newTip = (cov_29vg1lngs().s[51]++, {
            user_id: userId,
            tip_text: tipText,
            category: 'technique',
            personalized: true
          });
          var _ref10 = (cov_29vg1lngs().s[52]++, yield _supabase.supabase.from('ai_tips').insert(newTip).select().single()),
            data = _ref10.data,
            error = _ref10.error;
          cov_29vg1lngs().s[53]++;
          if (error) {
            cov_29vg1lngs().b[25][0]++;
            cov_29vg1lngs().s[54]++;
            console.error('Error saving AI tip:', error);
            cov_29vg1lngs().s[55]++;
            return {
              id: `tip-${Date.now()}`,
              user_id: userId,
              tip_text: tipText,
              category: 'technique',
              personalized: true,
              created_at: new Date().toISOString()
            };
          } else {
            cov_29vg1lngs().b[25][1]++;
          }
          cov_29vg1lngs().s[56]++;
          return data;
        } catch (error) {
          cov_29vg1lngs().s[57]++;
          console.error('Error generating AI tip:', error);
          var fallbackTips = (cov_29vg1lngs().s[58]++, ['Focus on your split step timing - it should happen just as your opponent makes contact with the ball.', 'Practice your serve toss consistency by catching 10 tosses in a row at the same height.', 'When approaching the net, aim your approach shot deep and to your opponent\'s weaker side.', 'Work on your recovery step after each shot to maintain better court positioning.', 'Use the continental grip for all volleys to improve your net game consistency.']);
          var _tipText = (cov_29vg1lngs().s[59]++, fallbackTips[Math.floor(Math.random() * fallbackTips.length)]);
          cov_29vg1lngs().s[60]++;
          return {
            id: `tip-${Date.now()}`,
            user_id: userId,
            tip_text: _tipText,
            category: 'technique',
            personalized: false,
            created_at: new Date().toISOString()
          };
        }
      });
      function generateAITip(_x2, _x3) {
        return _generateAITip.apply(this, arguments);
      }
      return generateAITip;
    }()
  }, {
    key: "markNotificationAsRead",
    value: function () {
      var _markNotificationAsRead = (0, _asyncToGenerator2.default)(function* (notificationId) {
        cov_29vg1lngs().f[5]++;
        cov_29vg1lngs().s[61]++;
        try {
          var _ref11 = (cov_29vg1lngs().s[62]++, yield _supabase.supabase.from('notifications').update({
              read: true
            }).eq('id', notificationId)),
            error = _ref11.error;
          cov_29vg1lngs().s[63]++;
          if (error) {
            cov_29vg1lngs().b[26][0]++;
            cov_29vg1lngs().s[64]++;
            console.error('Error marking notification as read:', error);
            cov_29vg1lngs().s[65]++;
            throw new Error('Failed to mark notification as read');
          } else {
            cov_29vg1lngs().b[26][1]++;
          }
        } catch (error) {
          cov_29vg1lngs().s[66]++;
          console.error('Error in markNotificationAsRead:', error);
          var notification = (cov_29vg1lngs().s[67]++, this.mockNotifications.find(function (n) {
            cov_29vg1lngs().f[6]++;
            cov_29vg1lngs().s[68]++;
            return n.id === notificationId;
          }));
          cov_29vg1lngs().s[69]++;
          if (notification) {
            cov_29vg1lngs().b[27][0]++;
            cov_29vg1lngs().s[70]++;
            notification.read = true;
          } else {
            cov_29vg1lngs().b[27][1]++;
          }
        }
      });
      function markNotificationAsRead(_x4) {
        return _markNotificationAsRead.apply(this, arguments);
      }
      return markNotificationAsRead;
    }()
  }, {
    key: "refreshUserStats",
    value: function () {
      var _refreshUserStats = (0, _asyncToGenerator2.default)(function* (userId) {
        cov_29vg1lngs().f[7]++;
        cov_29vg1lngs().s[71]++;
        try {
          var _ref12 = (cov_29vg1lngs().s[72]++, yield _supabase.supabase.from('skill_stats').select('*').eq('user_id', userId).single()),
            currentStats = _ref12.data,
            fetchError = _ref12.error;
          cov_29vg1lngs().s[73]++;
          if (fetchError) {
            cov_29vg1lngs().b[28][0]++;
            cov_29vg1lngs().s[74]++;
            console.error('Error fetching current stats:', fetchError);
            cov_29vg1lngs().s[75]++;
            throw new Error('Failed to fetch current stats');
          } else {
            cov_29vg1lngs().b[28][1]++;
          }
          var updatedStats = (cov_29vg1lngs().s[76]++, Object.assign({}, currentStats));
          var skillKeys = (cov_29vg1lngs().s[77]++, ['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy', 'mental_game']);
          cov_29vg1lngs().s[78]++;
          skillKeys.forEach(function (key) {
            cov_29vg1lngs().f[8]++;
            var currentValue = (cov_29vg1lngs().s[79]++, updatedStats[key]);
            cov_29vg1lngs().s[80]++;
            updatedStats[key] = Math.min(100, currentValue + Math.floor(Math.random() * 3));
          });
          var _ref13 = (cov_29vg1lngs().s[81]++, yield _supabase.supabase.from('skill_stats').update(updatedStats).eq('user_id', userId).select().single()),
            newStats = _ref13.data,
            updateError = _ref13.error;
          cov_29vg1lngs().s[82]++;
          if (updateError) {
            cov_29vg1lngs().b[29][0]++;
            cov_29vg1lngs().s[83]++;
            console.error('Error updating stats:', updateError);
            cov_29vg1lngs().s[84]++;
            throw new Error('Failed to update stats');
          } else {
            cov_29vg1lngs().b[29][1]++;
          }
          cov_29vg1lngs().s[85]++;
          return newStats;
        } catch (error) {
          cov_29vg1lngs().s[86]++;
          console.error('Error in refreshUserStats:', error);
          var _updatedStats = (cov_29vg1lngs().s[87]++, Object.assign({}, this.mockSkillStats));
          cov_29vg1lngs().s[88]++;
          Object.keys(_updatedStats).forEach(function (key) {
            cov_29vg1lngs().f[9]++;
            cov_29vg1lngs().s[89]++;
            if ((cov_29vg1lngs().b[31][0]++, typeof _updatedStats[key] === 'number') && (cov_29vg1lngs().b[31][1]++, key !== 'id')) {
              cov_29vg1lngs().b[30][0]++;
              var currentValue = (cov_29vg1lngs().s[90]++, _updatedStats[key]);
              cov_29vg1lngs().s[91]++;
              _updatedStats[key] = Math.min(100, currentValue + Math.floor(Math.random() * 3));
            } else {
              cov_29vg1lngs().b[30][1]++;
            }
          });
          cov_29vg1lngs().s[92]++;
          _updatedStats.updated_at = new Date().toISOString();
          cov_29vg1lngs().s[93]++;
          this.mockSkillStats = _updatedStats;
          cov_29vg1lngs().s[94]++;
          return _updatedStats;
        }
      });
      function refreshUserStats(_x5) {
        return _refreshUserStats.apply(this, arguments);
      }
      return refreshUserStats;
    }()
  }, {
    key: "getPerformanceMetrics",
    value: (function () {
      var _getPerformanceMetrics = (0, _asyncToGenerator2.default)(function* (userId) {
        var _this = this;
        cov_29vg1lngs().f[10]++;
        cov_29vg1lngs().s[95]++;
        try {
          var _ref14 = (cov_29vg1lngs().s[96]++, yield _supabase.supabase.from('matches').select(`
          id,
          statistics,
          result,
          match_date,
          created_at
        `).eq('user_id', userId).order('match_date', {
              ascending: false
            }).limit(10)),
            matches = _ref14.data,
            error = _ref14.error;
          cov_29vg1lngs().s[97]++;
          if ((cov_29vg1lngs().b[33][0]++, error) || (cov_29vg1lngs().b[33][1]++, !matches) || (cov_29vg1lngs().b[33][2]++, matches.length === 0)) {
            cov_29vg1lngs().b[32][0]++;
            cov_29vg1lngs().s[98]++;
            return this.getDefaultPerformanceMetrics();
          } else {
            cov_29vg1lngs().b[32][1]++;
          }
          var totalServe = (cov_29vg1lngs().s[99]++, 0),
            totalForehand = (cov_29vg1lngs().s[100]++, 0),
            totalBackhand = (cov_29vg1lngs().s[101]++, 0);
          var totalVolley = (cov_29vg1lngs().s[102]++, 0),
            totalMovement = (cov_29vg1lngs().s[103]++, 0);
          var validMatches = (cov_29vg1lngs().s[104]++, 0);
          cov_29vg1lngs().s[105]++;
          matches.forEach(function (match) {
            cov_29vg1lngs().f[11]++;
            cov_29vg1lngs().s[106]++;
            if (match.statistics) {
              cov_29vg1lngs().b[34][0]++;
              var stats = (cov_29vg1lngs().s[107]++, typeof match.statistics === 'string' ? (cov_29vg1lngs().b[35][0]++, JSON.parse(match.statistics)) : (cov_29vg1lngs().b[35][1]++, match.statistics));
              var _serveRating = (cov_29vg1lngs().s[108]++, _this.calculateServeRating(stats));
              var _forehandRating = (cov_29vg1lngs().s[109]++, _this.calculateStrokeRating(stats, 'forehand'));
              var _backhandRating = (cov_29vg1lngs().s[110]++, _this.calculateStrokeRating(stats, 'backhand'));
              var _volleyRating = (cov_29vg1lngs().s[111]++, _this.calculateVolleyRating(stats));
              var _movementRating = (cov_29vg1lngs().s[112]++, _this.calculateMovementRating(stats));
              cov_29vg1lngs().s[113]++;
              totalServe += _serveRating;
              cov_29vg1lngs().s[114]++;
              totalForehand += _forehandRating;
              cov_29vg1lngs().s[115]++;
              totalBackhand += _backhandRating;
              cov_29vg1lngs().s[116]++;
              totalVolley += _volleyRating;
              cov_29vg1lngs().s[117]++;
              totalMovement += _movementRating;
              cov_29vg1lngs().s[118]++;
              validMatches++;
            } else {
              cov_29vg1lngs().b[34][1]++;
            }
          });
          cov_29vg1lngs().s[119]++;
          if (validMatches === 0) {
            cov_29vg1lngs().b[36][0]++;
            cov_29vg1lngs().s[120]++;
            return this.getDefaultPerformanceMetrics();
          } else {
            cov_29vg1lngs().b[36][1]++;
          }
          var serveRating = (cov_29vg1lngs().s[121]++, Math.round(totalServe / validMatches));
          var forehandRating = (cov_29vg1lngs().s[122]++, Math.round(totalForehand / validMatches));
          var backhandRating = (cov_29vg1lngs().s[123]++, Math.round(totalBackhand / validMatches));
          var volleyRating = (cov_29vg1lngs().s[124]++, Math.round(totalVolley / validMatches));
          var movementRating = (cov_29vg1lngs().s[125]++, Math.round(totalMovement / validMatches));
          var overallRating = (cov_29vg1lngs().s[126]++, Math.round((serveRating + forehandRating + backhandRating + volleyRating + movementRating) / 5));
          var improvementTrend = (cov_29vg1lngs().s[127]++, yield this.calculateImprovementTrend(userId, overallRating));
          cov_29vg1lngs().s[128]++;
          return {
            overallRating: overallRating,
            serveRating: serveRating,
            forehandRating: forehandRating,
            backhandRating: backhandRating,
            volleyRating: volleyRating,
            movementRating: movementRating,
            improvementTrend: improvementTrend,
            lastUpdated: new Date().toISOString()
          };
        } catch (error) {
          cov_29vg1lngs().s[129]++;
          console.error('Error getting performance metrics:', error);
          cov_29vg1lngs().s[130]++;
          return this.getDefaultPerformanceMetrics();
        }
      });
      function getPerformanceMetrics(_x6) {
        return _getPerformanceMetrics.apply(this, arguments);
      }
      return getPerformanceMetrics;
    }())
  }, {
    key: "getWeeklyStatistics",
    value: (function () {
      var _getWeeklyStatistics = (0, _asyncToGenerator2.default)(function* (userId) {
        cov_29vg1lngs().f[12]++;
        cov_29vg1lngs().s[131]++;
        try {
          var oneWeekAgo = (cov_29vg1lngs().s[132]++, new Date());
          cov_29vg1lngs().s[133]++;
          oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
          var _ref15 = (cov_29vg1lngs().s[134]++, yield _supabase.supabase.from('training_sessions').select('*').eq('user_id', userId).gte('session_date', oneWeekAgo.toISOString().split('T')[0])),
            sessions = _ref15.data,
            error = _ref15.error;
          cov_29vg1lngs().s[135]++;
          if (error) {
            cov_29vg1lngs().b[37][0]++;
            cov_29vg1lngs().s[136]++;
            console.error('Error fetching weekly statistics:', error);
            cov_29vg1lngs().s[137]++;
            return this.getDefaultWeeklyStats();
          } else {
            cov_29vg1lngs().b[37][1]++;
          }
          var sessionsData = (cov_29vg1lngs().s[138]++, (cov_29vg1lngs().b[38][0]++, sessions) || (cov_29vg1lngs().b[38][1]++, []));
          var sessionsCompleted = (cov_29vg1lngs().s[139]++, sessionsData.length);
          var totalPracticeTime = (cov_29vg1lngs().s[140]++, sessionsData.reduce(function (sum, session) {
            cov_29vg1lngs().f[13]++;
            cov_29vg1lngs().s[141]++;
            return sum + ((cov_29vg1lngs().b[39][0]++, session.duration_minutes) || (cov_29vg1lngs().b[39][1]++, 0));
          }, 0));
          var averageScore = (cov_29vg1lngs().s[142]++, sessionsCompleted > 0 ? (cov_29vg1lngs().b[40][0]++, Math.round(sessionsData.reduce(function (sum, session) {
            cov_29vg1lngs().f[14]++;
            cov_29vg1lngs().s[143]++;
            return sum + ((cov_29vg1lngs().b[41][0]++, session.overall_score) || (cov_29vg1lngs().b[41][1]++, 0));
          }, 0) / sessionsCompleted)) : (cov_29vg1lngs().b[40][1]++, 0));
          var twoWeeksAgo = (cov_29vg1lngs().s[144]++, new Date());
          cov_29vg1lngs().s[145]++;
          twoWeeksAgo.setDate(twoWeeksAgo.getDate() - 14);
          var _ref16 = (cov_29vg1lngs().s[146]++, yield _supabase.supabase.from('training_sessions').select('overall_score').eq('user_id', userId).gte('session_date', twoWeeksAgo.toISOString().split('T')[0]).lt('session_date', oneWeekAgo.toISOString().split('T')[0])),
            previousWeekSessions = _ref16.data;
          var previousWeekAverage = (cov_29vg1lngs().s[147]++, (cov_29vg1lngs().b[43][0]++, previousWeekSessions) && (cov_29vg1lngs().b[43][1]++, previousWeekSessions.length > 0) ? (cov_29vg1lngs().b[42][0]++, previousWeekSessions.reduce(function (sum, session) {
            cov_29vg1lngs().f[15]++;
            cov_29vg1lngs().s[148]++;
            return sum + ((cov_29vg1lngs().b[44][0]++, session.overall_score) || (cov_29vg1lngs().b[44][1]++, 0));
          }, 0) / previousWeekSessions.length) : (cov_29vg1lngs().b[42][1]++, 0));
          var improvement = (cov_29vg1lngs().s[149]++, previousWeekAverage > 0 ? (cov_29vg1lngs().b[45][0]++, Math.round((averageScore - previousWeekAverage) / previousWeekAverage * 100)) : (cov_29vg1lngs().b[45][1]++, 0));
          var sessionMetrics = (cov_29vg1lngs().s[150]++, sessionsData.map(function (session) {
            cov_29vg1lngs().f[16]++;
            cov_29vg1lngs().s[151]++;
            return {
              improvementScore: (cov_29vg1lngs().b[46][0]++, session.improvement_score) || (cov_29vg1lngs().b[46][1]++, Math.floor(Math.random() * 20) + 70),
              consistencyRating: (cov_29vg1lngs().b[47][0]++, session.consistency_rating) || (cov_29vg1lngs().b[47][1]++, Math.floor(Math.random() * 15) + 75)
            };
          }));
          cov_29vg1lngs().s[152]++;
          return {
            sessionsCompleted: sessionsCompleted,
            totalPracticeTime: totalPracticeTime,
            averageScore: averageScore,
            improvement: improvement,
            sessionMetrics: sessionMetrics
          };
        } catch (error) {
          cov_29vg1lngs().s[153]++;
          console.error('Error getting weekly statistics:', error);
          cov_29vg1lngs().s[154]++;
          return this.getDefaultWeeklyStats();
        }
      });
      function getWeeklyStatistics(_x7) {
        return _getWeeklyStatistics.apply(this, arguments);
      }
      return getWeeklyStatistics;
    }())
  }, {
    key: "calculateServeRating",
    value: function calculateServeRating(stats) {
      cov_29vg1lngs().f[17]++;
      var firstServePercentage = (cov_29vg1lngs().s[155]++, (cov_29vg1lngs().b[48][0]++, stats.firstServePercentage) || (cov_29vg1lngs().b[48][1]++, 0));
      var aces = (cov_29vg1lngs().s[156]++, (cov_29vg1lngs().b[49][0]++, stats.aces) || (cov_29vg1lngs().b[49][1]++, 0));
      var doubleFaults = (cov_29vg1lngs().s[157]++, (cov_29vg1lngs().b[50][0]++, stats.doubleFaults) || (cov_29vg1lngs().b[50][1]++, 0));
      cov_29vg1lngs().s[158]++;
      return Math.max(0, Math.min(100, firstServePercentage + aces * 2 - doubleFaults * 3));
    }
  }, {
    key: "calculateStrokeRating",
    value: function calculateStrokeRating(stats, strokeType) {
      cov_29vg1lngs().f[18]++;
      var winners = (cov_29vg1lngs().s[159]++, (cov_29vg1lngs().b[51][0]++, stats[`${strokeType}Winners`]) || (cov_29vg1lngs().b[51][1]++, 0));
      var errors = (cov_29vg1lngs().s[160]++, (cov_29vg1lngs().b[52][0]++, stats[`${strokeType}Errors`]) || (cov_29vg1lngs().b[52][1]++, 0));
      cov_29vg1lngs().s[161]++;
      return Math.max(0, Math.min(100, 70 + winners * 2 - errors));
    }
  }, {
    key: "calculateVolleyRating",
    value: function calculateVolleyRating(stats) {
      cov_29vg1lngs().f[19]++;
      var netPointsAttempted = (cov_29vg1lngs().s[162]++, (cov_29vg1lngs().b[53][0]++, stats.netPointsAttempted) || (cov_29vg1lngs().b[53][1]++, 0));
      var netPointsWon = (cov_29vg1lngs().s[163]++, (cov_29vg1lngs().b[54][0]++, stats.netPointsWon) || (cov_29vg1lngs().b[54][1]++, 0));
      cov_29vg1lngs().s[164]++;
      if (netPointsAttempted === 0) {
        cov_29vg1lngs().b[55][0]++;
        cov_29vg1lngs().s[165]++;
        return 70;
      } else {
        cov_29vg1lngs().b[55][1]++;
      }
      cov_29vg1lngs().s[166]++;
      return Math.max(0, Math.min(100, netPointsWon / netPointsAttempted * 100));
    }
  }, {
    key: "calculateMovementRating",
    value: function calculateMovementRating(stats) {
      cov_29vg1lngs().f[20]++;
      var totalPointsWon = (cov_29vg1lngs().s[167]++, (cov_29vg1lngs().b[56][0]++, stats.totalPointsWon) || (cov_29vg1lngs().b[56][1]++, 0));
      var totalPointsPlayed = (cov_29vg1lngs().s[168]++, (cov_29vg1lngs().b[57][0]++, stats.totalPointsPlayed) || (cov_29vg1lngs().b[57][1]++, 1));
      cov_29vg1lngs().s[169]++;
      return Math.max(0, Math.min(100, 70 + totalPointsWon / totalPointsPlayed * 30));
    }
  }, {
    key: "calculateImprovementTrend",
    value: (function () {
      var _calculateImprovementTrend = (0, _asyncToGenerator2.default)(function* (userId, currentRating) {
        var _this2 = this;
        cov_29vg1lngs().f[21]++;
        cov_29vg1lngs().s[170]++;
        try {
          var oneMonthAgo = (cov_29vg1lngs().s[171]++, new Date());
          cov_29vg1lngs().s[172]++;
          oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
          var _ref17 = (cov_29vg1lngs().s[173]++, yield _supabase.supabase.from('matches').select('statistics').eq('user_id', userId).lt('match_date', oneMonthAgo.toISOString().split('T')[0]).order('match_date', {
              ascending: false
            }).limit(5)),
            oldMatches = _ref17.data;
          cov_29vg1lngs().s[174]++;
          if ((cov_29vg1lngs().b[59][0]++, !oldMatches) || (cov_29vg1lngs().b[59][1]++, oldMatches.length === 0)) {
            cov_29vg1lngs().b[58][0]++;
            cov_29vg1lngs().s[175]++;
            return 0;
          } else {
            cov_29vg1lngs().b[58][1]++;
          }
          var totalOldRating = (cov_29vg1lngs().s[176]++, 0);
          var validOldMatches = (cov_29vg1lngs().s[177]++, 0);
          cov_29vg1lngs().s[178]++;
          oldMatches.forEach(function (match) {
            cov_29vg1lngs().f[22]++;
            cov_29vg1lngs().s[179]++;
            if (match.statistics) {
              cov_29vg1lngs().b[60][0]++;
              var stats = (cov_29vg1lngs().s[180]++, typeof match.statistics === 'string' ? (cov_29vg1lngs().b[61][0]++, JSON.parse(match.statistics)) : (cov_29vg1lngs().b[61][1]++, match.statistics));
              var oldRating = (cov_29vg1lngs().s[181]++, (_this2.calculateServeRating(stats) + _this2.calculateStrokeRating(stats, 'forehand') + _this2.calculateStrokeRating(stats, 'backhand') + _this2.calculateVolleyRating(stats) + _this2.calculateMovementRating(stats)) / 5);
              cov_29vg1lngs().s[182]++;
              totalOldRating += oldRating;
              cov_29vg1lngs().s[183]++;
              validOldMatches++;
            } else {
              cov_29vg1lngs().b[60][1]++;
            }
          });
          cov_29vg1lngs().s[184]++;
          if (validOldMatches === 0) {
            cov_29vg1lngs().b[62][0]++;
            cov_29vg1lngs().s[185]++;
            return 0;
          } else {
            cov_29vg1lngs().b[62][1]++;
          }
          var oldAverageRating = (cov_29vg1lngs().s[186]++, totalOldRating / validOldMatches);
          cov_29vg1lngs().s[187]++;
          return Math.round(currentRating - oldAverageRating);
        } catch (error) {
          cov_29vg1lngs().s[188]++;
          console.error('Error calculating improvement trend:', error);
          cov_29vg1lngs().s[189]++;
          return 0;
        }
      });
      function calculateImprovementTrend(_x8, _x9) {
        return _calculateImprovementTrend.apply(this, arguments);
      }
      return calculateImprovementTrend;
    }())
  }, {
    key: "getDefaultPerformanceMetrics",
    value: function getDefaultPerformanceMetrics() {
      cov_29vg1lngs().f[23]++;
      cov_29vg1lngs().s[190]++;
      return {
        overallRating: 75,
        serveRating: 70,
        forehandRating: 80,
        backhandRating: 70,
        volleyRating: 65,
        movementRating: 75,
        improvementTrend: 0,
        lastUpdated: new Date().toISOString()
      };
    }
  }, {
    key: "getDefaultWeeklyStats",
    value: function getDefaultWeeklyStats() {
      cov_29vg1lngs().f[24]++;
      cov_29vg1lngs().s[191]++;
      return {
        sessionsCompleted: 0,
        totalPracticeTime: 0,
        averageScore: 0,
        improvement: 0,
        sessionMetrics: []
      };
    }
  }]);
}();
var apiService = exports.apiService = (cov_29vg1lngs().s[192]++, new ApiService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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