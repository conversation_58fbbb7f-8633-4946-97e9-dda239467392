83f46fa672081377d6ed83cbd821cff4
function cov_2eqb4lkags() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useFrameworkReady.ts";
  var hash = "1cdda28b2b80ed07dfdd1d0876a3ebfc7988ef6f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useFrameworkReady.ts",
    statementMap: {
      "0": {
        start: {
          line: 10,
          column: 2
        },
        end: {
          line: 12,
          column: 5
        }
      },
      "1": {
        start: {
          line: 11,
          column: 4
        },
        end: {
          line: 11,
          column: 30
        }
      }
    },
    fnMap: {
      "0": {
        name: "useFrameworkReady",
        decl: {
          start: {
            line: 9,
            column: 16
          },
          end: {
            line: 9,
            column: 33
          }
        },
        loc: {
          start: {
            line: 9,
            column: 36
          },
          end: {
            line: 13,
            column: 1
          }
        },
        line: 9
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 10,
            column: 12
          },
          end: {
            line: 10,
            column: 13
          }
        },
        loc: {
          start: {
            line: 10,
            column: 18
          },
          end: {
            line: 12,
            column: 3
          }
        },
        line: 10
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0
    },
    f: {
      "0": 0,
      "1": 0
    },
    b: {},
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "1cdda28b2b80ed07dfdd1d0876a3ebfc7988ef6f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2eqb4lkags = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2eqb4lkags();
import { useEffect } from 'react';
export function useFrameworkReady() {
  cov_2eqb4lkags().f[0]++;
  cov_2eqb4lkags().s[0]++;
  useEffect(function () {
    cov_2eqb4lkags().f[1]++;
    cov_2eqb4lkags().s[1]++;
    window.frameworkReady == null || window.frameworkReady();
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VGcmFtZXdvcmtSZWFkeSIsImNvdl8yZXFiNGxrYWdzIiwiZiIsInMiLCJ3aW5kb3ciLCJmcmFtZXdvcmtSZWFkeSJdLCJzb3VyY2VzIjpbInVzZUZyYW1ld29ya1JlYWR5LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcblxuZGVjbGFyZSBnbG9iYWwge1xuICBpbnRlcmZhY2UgV2luZG93IHtcbiAgICBmcmFtZXdvcmtSZWFkeT86ICgpID0+IHZvaWQ7XG4gIH1cbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUZyYW1ld29ya1JlYWR5KCkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIHdpbmRvdy5mcmFtZXdvcmtSZWFkeT8uKCk7XG4gIH0pO1xufVxuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxTQUFTQSxTQUFTLFFBQVEsT0FBTztBQVFqQyxPQUFPLFNBQVNDLGlCQUFpQkEsQ0FBQSxFQUFHO0VBQUFDLGNBQUEsR0FBQUMsQ0FBQTtFQUFBRCxjQUFBLEdBQUFFLENBQUE7RUFDbENKLFNBQVMsQ0FBQyxZQUFNO0lBQUFFLGNBQUEsR0FBQUMsQ0FBQTtJQUFBRCxjQUFBLEdBQUFFLENBQUE7SUFDZEMsTUFBTSxDQUFDQyxjQUFjLFlBQXJCRCxNQUFNLENBQUNDLGNBQWMsQ0FBRyxDQUFDO0VBQzNCLENBQUMsQ0FBQztBQUNKIiwiaWdub3JlTGlzdCI6W119