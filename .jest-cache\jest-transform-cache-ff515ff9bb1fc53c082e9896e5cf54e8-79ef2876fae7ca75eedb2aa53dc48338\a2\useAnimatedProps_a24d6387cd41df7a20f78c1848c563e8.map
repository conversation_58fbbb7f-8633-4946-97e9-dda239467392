{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "useAnimatedProps", "_objectSpread2", "_AnimatedProps", "_AnimatedEvent", "_useRefEffect", "_NativeAnimatedHelper", "_react", "_useLayoutEffect", "props", "_useReducer", "useReducer", "count", "scheduleUpdate", "onUpdateRef", "useRef", "node", "useMemo", "current", "useAnimatedPropsLifecycle", "refEffect", "useCallback", "instance", "setNativeView", "target", "getEventTarget", "events", "propName", "propValue", "AnimatedEvent", "__isNative", "__attach", "push", "_i", "_events", "length", "_events$_i", "_propName", "_propValue", "__detach", "callback<PERSON><PERSON>", "reduceAnimatedProps", "__getValue", "collapsable", "prevNodeRef", "isUnmountingRef", "useEffect", "API", "flushQueue", "prevNode", "__restore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getScrollableNode", "isFabricInstance", "_instance$getScrollRe", "has<PERSON>ab<PERSON><PERSON><PERSON><PERSON>", "getNativeScrollRef", "getScrollResponder", "_instance$_internalIn", "_instance$_internalIn2", "stateNode", "canonical", "module"], "sources": ["useAnimatedProps.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = useAnimatedProps;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _AnimatedProps = _interopRequireDefault(require(\"./nodes/AnimatedProps\"));\nvar _AnimatedEvent = require(\"./AnimatedEvent\");\nvar _useRefEffect = _interopRequireDefault(require(\"../Utilities/useRefEffect\"));\nvar _NativeAnimatedHelper = _interopRequireDefault(require(\"./NativeAnimatedHelper\"));\nvar _react = require(\"react\");\nvar _useLayoutEffect = _interopRequireDefault(require(\"../../../modules/useLayoutEffect\"));\nfunction useAnimatedProps(props) {\n  var _useReducer = (0, _react.useReducer)(count => count + 1, 0),\n    scheduleUpdate = _useReducer[1];\n  var onUpdateRef = (0, _react.useRef)(null);\n\n  // TODO: Only invalidate `node` if animated props or `style` change. In the\n  // previous implementation, we permitted `style` to override props with the\n  // same name property name as styles, so we can probably continue doing that.\n  // The ordering of other props *should* not matter.\n  var node = (0, _react.useMemo)(() => new _AnimatedProps.default(props, () => onUpdateRef.current == null ? void 0 : onUpdateRef.current()), [props]);\n  useAnimatedPropsLifecycle(node);\n\n  // TODO: This \"effect\" does three things:\n  //\n  //   1) Call `setNativeView`.\n  //   2) Update `onUpdateRef`.\n  //   3) Update listeners for `AnimatedEvent` props.\n  //\n  // Ideally, each of these would be separat \"effects\" so that they are not\n  // unnecessarily re-run when irrelevant dependencies change. For example, we\n  // should be able to hoist all `AnimatedEvent` props and only do #3 if either\n  // the `AnimatedEvent` props change or `instance` changes.\n  //\n  // But there is no way to transparently compose three separate callback refs,\n  // so we just combine them all into one for now.\n  var refEffect = (0, _react.useCallback)(instance => {\n    // NOTE: This may be called more often than necessary (e.g. when `props`\n    // changes), but `setNativeView` already optimizes for that.\n    node.setNativeView(instance);\n\n    // NOTE: This callback is only used by the JavaScript animation driver.\n    onUpdateRef.current = () => {\n      // Schedule an update for this component to update `reducedProps`,\n      // but do not compute it immediately. If a parent also updated, we\n      // need to merge those new props in before updating.\n      scheduleUpdate();\n    };\n    var target = getEventTarget(instance);\n    var events = [];\n    for (var propName in props) {\n      var propValue = props[propName];\n      if (propValue instanceof _AnimatedEvent.AnimatedEvent && propValue.__isNative) {\n        propValue.__attach(target, propName);\n        events.push([propName, propValue]);\n      }\n    }\n    return () => {\n      onUpdateRef.current = null;\n      for (var _i = 0, _events = events; _i < _events.length; _i++) {\n        var _events$_i = _events[_i],\n          _propName = _events$_i[0],\n          _propValue = _events$_i[1];\n        _propValue.__detach(target, _propName);\n      }\n    };\n  }, [props, node]);\n  var callbackRef = (0, _useRefEffect.default)(refEffect);\n  return [reduceAnimatedProps(node), callbackRef];\n}\nfunction reduceAnimatedProps(node) {\n  // Force `collapsable` to be false so that the native view is not flattened.\n  // Flattened views cannot be accurately referenced by the native driver.\n  return (0, _objectSpread2.default)((0, _objectSpread2.default)({}, node.__getValue()), {}, {\n    collapsable: false\n  });\n}\n\n/**\n * Manages the lifecycle of the supplied `AnimatedProps` by invoking `__attach`\n * and `__detach`. However, this is more complicated because `AnimatedProps`\n * uses reference counting to determine when to recursively detach its children\n * nodes. So in order to optimize this, we avoid detaching until the next attach\n * unless we are unmounting.\n */\nfunction useAnimatedPropsLifecycle(node) {\n  var prevNodeRef = (0, _react.useRef)(null);\n  var isUnmountingRef = (0, _react.useRef)(false);\n  (0, _react.useEffect)(() => {\n    // It is ok for multiple components to call `flushQueue` because it noops\n    // if the queue is empty. When multiple animated components are mounted at\n    // the same time. Only first component flushes the queue and the others will noop.\n    _NativeAnimatedHelper.default.API.flushQueue();\n  });\n  (0, _useLayoutEffect.default)(() => {\n    isUnmountingRef.current = false;\n    return () => {\n      isUnmountingRef.current = true;\n    };\n  }, []);\n  (0, _useLayoutEffect.default)(() => {\n    node.__attach();\n    if (prevNodeRef.current != null) {\n      var prevNode = prevNodeRef.current;\n      // TODO: Stop restoring default values (unless `reset` is called).\n      prevNode.__restoreDefaultValues();\n      prevNode.__detach();\n      prevNodeRef.current = null;\n    }\n    return () => {\n      if (isUnmountingRef.current) {\n        // NOTE: Do not restore default values on unmount, see D18197735.\n        node.__detach();\n      } else {\n        prevNodeRef.current = node;\n      }\n    };\n  }, [node]);\n}\nfunction getEventTarget(instance) {\n  return typeof instance === 'object' && typeof (instance == null ? void 0 : instance.getScrollableNode) === 'function' ?\n  // $FlowFixMe[incompatible-use] - Legacy instance assumptions.\n  instance.getScrollableNode() : instance;\n}\n\n// $FlowFixMe[unclear-type] - Legacy instance assumptions.\nfunction isFabricInstance(instance) {\n  var _instance$getScrollRe;\n  return hasFabricHandle(instance) ||\n  // Some components have a setNativeProps function but aren't a host component\n  // such as lists like FlatList and SectionList. These should also use\n  // forceUpdate in Fabric since setNativeProps doesn't exist on the underlying\n  // host component. This crazy hack is essentially special casing those lists and\n  // ScrollView itself to use forceUpdate in Fabric.\n  // If these components end up using forwardRef then these hacks can go away\n  // as instance would actually be the underlying host component and the above check\n  // would be sufficient.\n  hasFabricHandle(instance == null ? void 0 : instance.getNativeScrollRef == null ? void 0 : instance.getNativeScrollRef()) || hasFabricHandle(instance == null ? void 0 : instance.getScrollResponder == null ? void 0 : (_instance$getScrollRe = instance.getScrollResponder()) == null ? void 0 : _instance$getScrollRe.getNativeScrollRef == null ? void 0 : _instance$getScrollRe.getNativeScrollRef());\n}\n\n// $FlowFixMe[unclear-type] - Legacy instance assumptions.\nfunction hasFabricHandle(instance) {\n  var _instance$_internalIn, _instance$_internalIn2;\n  // eslint-disable-next-line dot-notation\n  return (instance == null ? void 0 : (_instance$_internalIn = instance['_internalInstanceHandle']) == null ? void 0 : (_instance$_internalIn2 = _instance$_internalIn.stateNode) == null ? void 0 : _instance$_internalIn2.canonical) != null;\n}\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,gBAAgB;AAClC,IAAIC,cAAc,GAAGN,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIM,cAAc,GAAGP,sBAAsB,CAACC,OAAO,wBAAwB,CAAC,CAAC;AAC7E,IAAIO,cAAc,GAAGP,OAAO,kBAAkB,CAAC;AAC/C,IAAIQ,aAAa,GAAGT,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAChF,IAAIS,qBAAqB,GAAGV,sBAAsB,CAACC,OAAO,yBAAyB,CAAC,CAAC;AACrF,IAAIU,MAAM,GAAGV,OAAO,CAAC,OAAO,CAAC;AAC7B,IAAIW,gBAAgB,GAAGZ,sBAAsB,CAACC,OAAO,mCAAmC,CAAC,CAAC;AAC1F,SAASI,gBAAgBA,CAACQ,KAAK,EAAE;EAC/B,IAAIC,WAAW,GAAG,CAAC,CAAC,EAAEH,MAAM,CAACI,UAAU,EAAE,UAAAC,KAAK;MAAA,OAAIA,KAAK,GAAG,CAAC;IAAA,GAAE,CAAC,CAAC;IAC7DC,cAAc,GAAGH,WAAW,CAAC,CAAC,CAAC;EACjC,IAAII,WAAW,GAAG,CAAC,CAAC,EAAEP,MAAM,CAACQ,MAAM,EAAE,IAAI,CAAC;EAM1C,IAAIC,IAAI,GAAG,CAAC,CAAC,EAAET,MAAM,CAACU,OAAO,EAAE;IAAA,OAAM,IAAId,cAAc,CAACL,OAAO,CAACW,KAAK,EAAE;MAAA,OAAMK,WAAW,CAACI,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGJ,WAAW,CAACI,OAAO,CAAC,CAAC;IAAA,EAAC;EAAA,GAAE,CAACT,KAAK,CAAC,CAAC;EACpJU,yBAAyB,CAACH,IAAI,CAAC;EAe/B,IAAII,SAAS,GAAG,CAAC,CAAC,EAAEb,MAAM,CAACc,WAAW,EAAE,UAAAC,QAAQ,EAAI;IAGlDN,IAAI,CAACO,aAAa,CAACD,QAAQ,CAAC;IAG5BR,WAAW,CAACI,OAAO,GAAG,YAAM;MAI1BL,cAAc,CAAC,CAAC;IAClB,CAAC;IACD,IAAIW,MAAM,GAAGC,cAAc,CAACH,QAAQ,CAAC;IACrC,IAAII,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,QAAQ,IAAIlB,KAAK,EAAE;MAC1B,IAAImB,SAAS,GAAGnB,KAAK,CAACkB,QAAQ,CAAC;MAC/B,IAAIC,SAAS,YAAYxB,cAAc,CAACyB,aAAa,IAAID,SAAS,CAACE,UAAU,EAAE;QAC7EF,SAAS,CAACG,QAAQ,CAACP,MAAM,EAAEG,QAAQ,CAAC;QACpCD,MAAM,CAACM,IAAI,CAAC,CAACL,QAAQ,EAAEC,SAAS,CAAC,CAAC;MACpC;IACF;IACA,OAAO,YAAM;MACXd,WAAW,CAACI,OAAO,GAAG,IAAI;MAC1B,KAAK,IAAIe,EAAE,GAAG,CAAC,EAAEC,OAAO,GAAGR,MAAM,EAAEO,EAAE,GAAGC,OAAO,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC5D,IAAIG,UAAU,GAAGF,OAAO,CAACD,EAAE,CAAC;UAC1BI,SAAS,GAAGD,UAAU,CAAC,CAAC,CAAC;UACzBE,UAAU,GAAGF,UAAU,CAAC,CAAC,CAAC;QAC5BE,UAAU,CAACC,QAAQ,CAACf,MAAM,EAAEa,SAAS,CAAC;MACxC;IACF,CAAC;EACH,CAAC,EAAE,CAAC5B,KAAK,EAAEO,IAAI,CAAC,CAAC;EACjB,IAAIwB,WAAW,GAAG,CAAC,CAAC,EAAEnC,aAAa,CAACP,OAAO,EAAEsB,SAAS,CAAC;EACvD,OAAO,CAACqB,mBAAmB,CAACzB,IAAI,CAAC,EAAEwB,WAAW,CAAC;AACjD;AACA,SAASC,mBAAmBA,CAACzB,IAAI,EAAE;EAGjC,OAAO,CAAC,CAAC,EAAEd,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEI,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEkB,IAAI,CAAC0B,UAAU,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACzFC,WAAW,EAAE;EACf,CAAC,CAAC;AACJ;AASA,SAASxB,yBAAyBA,CAACH,IAAI,EAAE;EACvC,IAAI4B,WAAW,GAAG,CAAC,CAAC,EAAErC,MAAM,CAACQ,MAAM,EAAE,IAAI,CAAC;EAC1C,IAAI8B,eAAe,GAAG,CAAC,CAAC,EAAEtC,MAAM,CAACQ,MAAM,EAAE,KAAK,CAAC;EAC/C,CAAC,CAAC,EAAER,MAAM,CAACuC,SAAS,EAAE,YAAM;IAI1BxC,qBAAqB,CAACR,OAAO,CAACiD,GAAG,CAACC,UAAU,CAAC,CAAC;EAChD,CAAC,CAAC;EACF,CAAC,CAAC,EAAExC,gBAAgB,CAACV,OAAO,EAAE,YAAM;IAClC+C,eAAe,CAAC3B,OAAO,GAAG,KAAK;IAC/B,OAAO,YAAM;MACX2B,eAAe,CAAC3B,OAAO,GAAG,IAAI;IAChC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,CAAC,CAAC,EAAEV,gBAAgB,CAACV,OAAO,EAAE,YAAM;IAClCkB,IAAI,CAACe,QAAQ,CAAC,CAAC;IACf,IAAIa,WAAW,CAAC1B,OAAO,IAAI,IAAI,EAAE;MAC/B,IAAI+B,QAAQ,GAAGL,WAAW,CAAC1B,OAAO;MAElC+B,QAAQ,CAACC,sBAAsB,CAAC,CAAC;MACjCD,QAAQ,CAACV,QAAQ,CAAC,CAAC;MACnBK,WAAW,CAAC1B,OAAO,GAAG,IAAI;IAC5B;IACA,OAAO,YAAM;MACX,IAAI2B,eAAe,CAAC3B,OAAO,EAAE;QAE3BF,IAAI,CAACuB,QAAQ,CAAC,CAAC;MACjB,CAAC,MAAM;QACLK,WAAW,CAAC1B,OAAO,GAAGF,IAAI;MAC5B;IACF,CAAC;EACH,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;AACZ;AACA,SAASS,cAAcA,CAACH,QAAQ,EAAE;EAChC,OAAO,OAAOA,QAAQ,KAAK,QAAQ,IAAI,QAAQA,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC6B,iBAAiB,CAAC,KAAK,UAAU,GAErH7B,QAAQ,CAAC6B,iBAAiB,CAAC,CAAC,GAAG7B,QAAQ;AACzC;AAGA,SAAS8B,gBAAgBA,CAAC9B,QAAQ,EAAE;EAClC,IAAI+B,qBAAqB;EACzB,OAAOC,eAAe,CAAChC,QAAQ,CAAC,IAShCgC,eAAe,CAAChC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACiC,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGjC,QAAQ,CAACiC,kBAAkB,CAAC,CAAC,CAAC,IAAID,eAAe,CAAChC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACkC,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACH,qBAAqB,GAAG/B,QAAQ,CAACkC,kBAAkB,CAAC,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGH,qBAAqB,CAACE,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACE,kBAAkB,CAAC,CAAC,CAAC;AAC5Y;AAGA,SAASD,eAAeA,CAAChC,QAAQ,EAAE;EACjC,IAAImC,qBAAqB,EAAEC,sBAAsB;EAEjD,OAAO,CAACpC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,CAACmC,qBAAqB,GAAGnC,QAAQ,CAAC,yBAAyB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,CAACoC,sBAAsB,GAAGD,qBAAqB,CAACE,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,sBAAsB,CAACE,SAAS,KAAK,IAAI;AAC9O;AACAC,MAAM,CAAC9D,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}