5318b117716dbd95c2022c2666a9f795
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _createElement = _interopRequireDefault(require("../createElement"));
var _multiplyStyleLengthValue = _interopRequireDefault(require("../../modules/multiplyStyleLengthValue"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["aria-label", "accessibilityLabel", "activeThumbColor", "activeTrackColor", "disabled", "onValueChange", "style", "thumbColor", "trackColor", "value"];
var emptyObject = {};
var thumbDefaultBoxShadow = '0px 1px 3px rgba(0,0,0,0.5)';
var thumbFocusedBoxShadow = thumbDefaultBoxShadow + ", 0 0 0 10px rgba(0,0,0,0.1)";
var defaultActiveTrackColor = '#A3D3CF';
var defaultTrackColor = '#939393';
var defaultDisabledTrackColor = '#D5D5D5';
var defaultActiveThumbColor = '#009688';
var defaultThumbColor = '#FAFAFA';
var defaultDisabledThumbColor = '#BDBDBD';
var Switch = React.forwardRef(function (props, forwardedRef) {
  var ariaLabel = props['aria-label'],
    accessibilityLabel = props.accessibilityLabel,
    activeThumbColor = props.activeThumbColor,
    activeTrackColor = props.activeTrackColor,
    _props$disabled = props.disabled,
    disabled = _props$disabled === void 0 ? false : _props$disabled,
    onValueChange = props.onValueChange,
    _props$style = props.style,
    style = _props$style === void 0 ? emptyObject : _props$style,
    thumbColor = props.thumbColor,
    trackColor = props.trackColor,
    _props$value = props.value,
    value = _props$value === void 0 ? false : _props$value,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var thumbRef = React.useRef(null);
  function handleChange(event) {
    if (onValueChange != null) {
      onValueChange(event.nativeEvent.target.checked);
    }
  }
  function handleFocusState(event) {
    var isFocused = event.nativeEvent.type === 'focus';
    var boxShadow = isFocused ? thumbFocusedBoxShadow : thumbDefaultBoxShadow;
    if (thumbRef.current != null) {
      thumbRef.current.style.boxShadow = boxShadow;
    }
  }
  var _StyleSheet$flatten = _StyleSheet.default.flatten(style),
    styleHeight = _StyleSheet$flatten.height,
    styleWidth = _StyleSheet$flatten.width;
  var height = styleHeight || '20px';
  var minWidth = (0, _multiplyStyleLengthValue.default)(height, 2);
  var width = styleWidth > minWidth ? styleWidth : minWidth;
  var trackBorderRadius = (0, _multiplyStyleLengthValue.default)(height, 0.5);
  var trackCurrentColor = function () {
    if (value === true) {
      if (trackColor != null && typeof trackColor === 'object') {
        return trackColor.true;
      } else {
        return activeTrackColor !== null && activeTrackColor !== void 0 ? activeTrackColor : defaultActiveTrackColor;
      }
    } else {
      if (trackColor != null && typeof trackColor === 'object') {
        return trackColor.false;
      } else {
        return trackColor !== null && trackColor !== void 0 ? trackColor : defaultTrackColor;
      }
    }
  }();
  var thumbCurrentColor = value ? activeThumbColor !== null && activeThumbColor !== void 0 ? activeThumbColor : defaultActiveThumbColor : thumbColor !== null && thumbColor !== void 0 ? thumbColor : defaultThumbColor;
  var thumbHeight = height;
  var thumbWidth = thumbHeight;
  var rootStyle = [styles.root, style, disabled && styles.cursorDefault, {
    height: height,
    width: width
  }];
  var disabledTrackColor = function () {
    if (value === true) {
      if (typeof activeTrackColor === 'string' && activeTrackColor != null || typeof trackColor === 'object' && trackColor != null && trackColor.true) {
        return trackCurrentColor;
      } else {
        return defaultDisabledTrackColor;
      }
    } else {
      if (typeof trackColor === 'string' && trackColor != null || typeof trackColor === 'object' && trackColor != null && trackColor.false) {
        return trackCurrentColor;
      } else {
        return defaultDisabledTrackColor;
      }
    }
  }();
  var disabledThumbColor = function () {
    if (value === true) {
      if (activeThumbColor == null) {
        return defaultDisabledThumbColor;
      } else {
        return thumbCurrentColor;
      }
    } else {
      if (thumbColor == null) {
        return defaultDisabledThumbColor;
      } else {
        return thumbCurrentColor;
      }
    }
  }();
  var trackStyle = [styles.track, {
    backgroundColor: disabled ? disabledTrackColor : trackCurrentColor,
    borderRadius: trackBorderRadius
  }];
  var thumbStyle = [styles.thumb, value && styles.thumbActive, {
    backgroundColor: disabled ? disabledThumbColor : thumbCurrentColor,
    height: thumbHeight,
    marginStart: value ? (0, _multiplyStyleLengthValue.default)(thumbWidth, -1) : 0,
    width: thumbWidth
  }];
  var nativeControl = (0, _createElement.default)('input', {
    'aria-label': ariaLabel || accessibilityLabel,
    checked: value,
    disabled: disabled,
    onBlur: handleFocusState,
    onChange: handleChange,
    onFocus: handleFocusState,
    ref: forwardedRef,
    style: [styles.nativeControl, styles.cursorInherit],
    type: 'checkbox',
    role: 'switch'
  });
  return React.createElement(_View.default, (0, _extends2.default)({}, other, {
    style: rootStyle
  }), React.createElement(_View.default, {
    style: trackStyle
  }), React.createElement(_View.default, {
    ref: thumbRef,
    style: thumbStyle
  }), nativeControl);
});
Switch.displayName = 'Switch';
var styles = _StyleSheet.default.create({
  root: {
    cursor: 'pointer',
    userSelect: 'none'
  },
  cursorDefault: {
    cursor: 'default'
  },
  cursorInherit: {
    cursor: 'inherit'
  },
  track: (0, _objectSpread2.default)((0, _objectSpread2.default)({
    forcedColorAdjust: 'none'
  }, _StyleSheet.default.absoluteFillObject), {}, {
    height: '70%',
    margin: 'auto',
    transitionDuration: '0.1s',
    width: '100%'
  }),
  thumb: {
    forcedColorAdjust: 'none',
    alignSelf: 'flex-start',
    borderRadius: '100%',
    boxShadow: thumbDefaultBoxShadow,
    start: '0%',
    transform: 'translateZ(0)',
    transitionDuration: '0.1s'
  },
  thumbActive: {
    insetInlineStart: '100%'
  },
  nativeControl: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _StyleSheet.default.absoluteFillObject), {}, {
    height: '100%',
    margin: 0,
    appearance: 'none',
    padding: 0,
    width: '100%'
  })
});
var _default = exports.default = Switch;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiX29iamVjdFNwcmVhZDIiLCJfZXh0ZW5kczIiLCJfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZTIiLCJSZWFjdCIsIl9jcmVhdGVFbGVtZW50IiwiX211bHRpcGx5U3R5bGVMZW5ndGhWYWx1ZSIsIl9TdHlsZVNoZWV0IiwiX1ZpZXciLCJfZXhjbHVkZWQiLCJlbXB0eU9iamVjdCIsInRodW1iRGVmYXVsdEJveFNoYWRvdyIsInRodW1iRm9jdXNlZEJveFNoYWRvdyIsImRlZmF1bHRBY3RpdmVUcmFja0NvbG9yIiwiZGVmYXVsdFRyYWNrQ29sb3IiLCJkZWZhdWx0RGlzYWJsZWRUcmFja0NvbG9yIiwiZGVmYXVsdEFjdGl2ZVRodW1iQ29sb3IiLCJkZWZhdWx0VGh1bWJDb2xvciIsImRlZmF1bHREaXNhYmxlZFRodW1iQ29sb3IiLCJTd2l0Y2giLCJmb3J3YXJkUmVmIiwicHJvcHMiLCJmb3J3YXJkZWRSZWYiLCJhcmlhTGFiZWwiLCJhY2Nlc3NpYmlsaXR5TGFiZWwiLCJhY3RpdmVUaHVtYkNvbG9yIiwiYWN0aXZlVHJhY2tDb2xvciIsIl9wcm9wcyRkaXNhYmxlZCIsImRpc2FibGVkIiwib25WYWx1ZUNoYW5nZSIsIl9wcm9wcyRzdHlsZSIsInN0eWxlIiwidGh1bWJDb2xvciIsInRyYWNrQ29sb3IiLCJfcHJvcHMkdmFsdWUiLCJ2YWx1ZSIsIm90aGVyIiwidGh1bWJSZWYiLCJ1c2VSZWYiLCJoYW5kbGVDaGFuZ2UiLCJldmVudCIsIm5hdGl2ZUV2ZW50IiwidGFyZ2V0IiwiY2hlY2tlZCIsImhhbmRsZUZvY3VzU3RhdGUiLCJpc0ZvY3VzZWQiLCJ0eXBlIiwiYm94U2hhZG93IiwiY3VycmVudCIsIl9TdHlsZVNoZWV0JGZsYXR0ZW4iLCJmbGF0dGVuIiwic3R5bGVIZWlnaHQiLCJoZWlnaHQiLCJzdHlsZVdpZHRoIiwid2lkdGgiLCJtaW5XaWR0aCIsInRyYWNrQm9yZGVyUmFkaXVzIiwidHJhY2tDdXJyZW50Q29sb3IiLCJ0cnVlIiwiZmFsc2UiLCJ0aHVtYkN1cnJlbnRDb2xvciIsInRodW1iSGVpZ2h0IiwidGh1bWJXaWR0aCIsInJvb3RTdHlsZSIsInN0eWxlcyIsInJvb3QiLCJjdXJzb3JEZWZhdWx0IiwiZGlzYWJsZWRUcmFja0NvbG9yIiwiZGlzYWJsZWRUaHVtYkNvbG9yIiwidHJhY2tTdHlsZSIsInRyYWNrIiwiYmFja2dyb3VuZENvbG9yIiwiYm9yZGVyUmFkaXVzIiwidGh1bWJTdHlsZSIsInRodW1iIiwidGh1bWJBY3RpdmUiLCJtYXJnaW5TdGFydCIsIm5hdGl2ZUNvbnRyb2wiLCJvbkJsdXIiLCJvbkNoYW5nZSIsIm9uRm9jdXMiLCJyZWYiLCJjdXJzb3JJbmhlcml0Iiwicm9sZSIsImNyZWF0ZUVsZW1lbnQiLCJkaXNwbGF5TmFtZSIsImNyZWF0ZSIsImN1cnNvciIsInVzZXJTZWxlY3QiLCJmb3JjZWRDb2xvckFkanVzdCIsImFic29sdXRlRmlsbE9iamVjdCIsIm1hcmdpbiIsInRyYW5zaXRpb25EdXJhdGlvbiIsImFsaWduU2VsZiIsInN0YXJ0IiwidHJhbnNmb3JtIiwiaW5zZXRJbmxpbmVTdGFydCIsImFwcGVhcmFuY2UiLCJwYWRkaW5nIiwiX2RlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyJpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbi8qKlxuICogQ29weXJpZ2h0IChjKSBOaWNvbGFzIEdhbGxhZ2hlci5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBcbiAqL1xuXG4ndXNlIGNsaWVudCc7XG5cbnZhciBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0ID0gcmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvaW50ZXJvcFJlcXVpcmVEZWZhdWx0XCIpLmRlZmF1bHQ7XG52YXIgX2ludGVyb3BSZXF1aXJlV2lsZGNhcmQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZVdpbGRjYXJkXCIpLmRlZmF1bHQ7XG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdm9pZCAwO1xudmFyIF9vYmplY3RTcHJlYWQyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9vYmplY3RTcHJlYWQyXCIpKTtcbnZhciBfZXh0ZW5kczIgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2V4dGVuZHNcIikpO1xudmFyIF9vYmplY3RXaXRob3V0UHJvcGVydGllc0xvb3NlMiA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIkBiYWJlbC9ydW50aW1lL2hlbHBlcnMvb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZVwiKSk7XG52YXIgUmVhY3QgPSBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKFwicmVhY3RcIikpO1xudmFyIF9jcmVhdGVFbGVtZW50ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vY3JlYXRlRWxlbWVudFwiKSk7XG52YXIgX211bHRpcGx5U3R5bGVMZW5ndGhWYWx1ZSA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uLy4uL21vZHVsZXMvbXVsdGlwbHlTdHlsZUxlbmd0aFZhbHVlXCIpKTtcbnZhciBfU3R5bGVTaGVldCA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uL1N0eWxlU2hlZXRcIikpO1xudmFyIF9WaWV3ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vVmlld1wiKSk7XG52YXIgX2V4Y2x1ZGVkID0gW1wiYXJpYS1sYWJlbFwiLCBcImFjY2Vzc2liaWxpdHlMYWJlbFwiLCBcImFjdGl2ZVRodW1iQ29sb3JcIiwgXCJhY3RpdmVUcmFja0NvbG9yXCIsIFwiZGlzYWJsZWRcIiwgXCJvblZhbHVlQ2hhbmdlXCIsIFwic3R5bGVcIiwgXCJ0aHVtYkNvbG9yXCIsIFwidHJhY2tDb2xvclwiLCBcInZhbHVlXCJdO1xudmFyIGVtcHR5T2JqZWN0ID0ge307XG52YXIgdGh1bWJEZWZhdWx0Qm94U2hhZG93ID0gJzBweCAxcHggM3B4IHJnYmEoMCwwLDAsMC41KSc7XG52YXIgdGh1bWJGb2N1c2VkQm94U2hhZG93ID0gdGh1bWJEZWZhdWx0Qm94U2hhZG93ICsgXCIsIDAgMCAwIDEwcHggcmdiYSgwLDAsMCwwLjEpXCI7XG52YXIgZGVmYXVsdEFjdGl2ZVRyYWNrQ29sb3IgPSAnI0EzRDNDRic7XG52YXIgZGVmYXVsdFRyYWNrQ29sb3IgPSAnIzkzOTM5Myc7XG52YXIgZGVmYXVsdERpc2FibGVkVHJhY2tDb2xvciA9ICcjRDVENUQ1JztcbnZhciBkZWZhdWx0QWN0aXZlVGh1bWJDb2xvciA9ICcjMDA5Njg4JztcbnZhciBkZWZhdWx0VGh1bWJDb2xvciA9ICcjRkFGQUZBJztcbnZhciBkZWZhdWx0RGlzYWJsZWRUaHVtYkNvbG9yID0gJyNCREJEQkQnO1xudmFyIFN3aXRjaCA9IC8qI19fUFVSRV9fKi9SZWFjdC5mb3J3YXJkUmVmKChwcm9wcywgZm9yd2FyZGVkUmVmKSA9PiB7XG4gIHZhciBhcmlhTGFiZWwgPSBwcm9wc1snYXJpYS1sYWJlbCddLFxuICAgIGFjY2Vzc2liaWxpdHlMYWJlbCA9IHByb3BzLmFjY2Vzc2liaWxpdHlMYWJlbCxcbiAgICBhY3RpdmVUaHVtYkNvbG9yID0gcHJvcHMuYWN0aXZlVGh1bWJDb2xvcixcbiAgICBhY3RpdmVUcmFja0NvbG9yID0gcHJvcHMuYWN0aXZlVHJhY2tDb2xvcixcbiAgICBfcHJvcHMkZGlzYWJsZWQgPSBwcm9wcy5kaXNhYmxlZCxcbiAgICBkaXNhYmxlZCA9IF9wcm9wcyRkaXNhYmxlZCA9PT0gdm9pZCAwID8gZmFsc2UgOiBfcHJvcHMkZGlzYWJsZWQsXG4gICAgb25WYWx1ZUNoYW5nZSA9IHByb3BzLm9uVmFsdWVDaGFuZ2UsXG4gICAgX3Byb3BzJHN0eWxlID0gcHJvcHMuc3R5bGUsXG4gICAgc3R5bGUgPSBfcHJvcHMkc3R5bGUgPT09IHZvaWQgMCA/IGVtcHR5T2JqZWN0IDogX3Byb3BzJHN0eWxlLFxuICAgIHRodW1iQ29sb3IgPSBwcm9wcy50aHVtYkNvbG9yLFxuICAgIHRyYWNrQ29sb3IgPSBwcm9wcy50cmFja0NvbG9yLFxuICAgIF9wcm9wcyR2YWx1ZSA9IHByb3BzLnZhbHVlLFxuICAgIHZhbHVlID0gX3Byb3BzJHZhbHVlID09PSB2b2lkIDAgPyBmYWxzZSA6IF9wcm9wcyR2YWx1ZSxcbiAgICBvdGhlciA9ICgwLCBfb2JqZWN0V2l0aG91dFByb3BlcnRpZXNMb29zZTIuZGVmYXVsdCkocHJvcHMsIF9leGNsdWRlZCk7XG4gIHZhciB0aHVtYlJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgZnVuY3Rpb24gaGFuZGxlQ2hhbmdlKGV2ZW50KSB7XG4gICAgaWYgKG9uVmFsdWVDaGFuZ2UgIT0gbnVsbCkge1xuICAgICAgb25WYWx1ZUNoYW5nZShldmVudC5uYXRpdmVFdmVudC50YXJnZXQuY2hlY2tlZCk7XG4gICAgfVxuICB9XG4gIGZ1bmN0aW9uIGhhbmRsZUZvY3VzU3RhdGUoZXZlbnQpIHtcbiAgICB2YXIgaXNGb2N1c2VkID0gZXZlbnQubmF0aXZlRXZlbnQudHlwZSA9PT0gJ2ZvY3VzJztcbiAgICB2YXIgYm94U2hhZG93ID0gaXNGb2N1c2VkID8gdGh1bWJGb2N1c2VkQm94U2hhZG93IDogdGh1bWJEZWZhdWx0Qm94U2hhZG93O1xuICAgIGlmICh0aHVtYlJlZi5jdXJyZW50ICE9IG51bGwpIHtcbiAgICAgIHRodW1iUmVmLmN1cnJlbnQuc3R5bGUuYm94U2hhZG93ID0gYm94U2hhZG93O1xuICAgIH1cbiAgfVxuICB2YXIgX1N0eWxlU2hlZXQkZmxhdHRlbiA9IF9TdHlsZVNoZWV0LmRlZmF1bHQuZmxhdHRlbihzdHlsZSksXG4gICAgc3R5bGVIZWlnaHQgPSBfU3R5bGVTaGVldCRmbGF0dGVuLmhlaWdodCxcbiAgICBzdHlsZVdpZHRoID0gX1N0eWxlU2hlZXQkZmxhdHRlbi53aWR0aDtcbiAgdmFyIGhlaWdodCA9IHN0eWxlSGVpZ2h0IHx8ICcyMHB4JztcbiAgdmFyIG1pbldpZHRoID0gKDAsIF9tdWx0aXBseVN0eWxlTGVuZ3RoVmFsdWUuZGVmYXVsdCkoaGVpZ2h0LCAyKTtcbiAgdmFyIHdpZHRoID0gc3R5bGVXaWR0aCA+IG1pbldpZHRoID8gc3R5bGVXaWR0aCA6IG1pbldpZHRoO1xuICB2YXIgdHJhY2tCb3JkZXJSYWRpdXMgPSAoMCwgX211bHRpcGx5U3R5bGVMZW5ndGhWYWx1ZS5kZWZhdWx0KShoZWlnaHQsIDAuNSk7XG4gIHZhciB0cmFja0N1cnJlbnRDb2xvciA9IGZ1bmN0aW9uICgpIHtcbiAgICBpZiAodmFsdWUgPT09IHRydWUpIHtcbiAgICAgIGlmICh0cmFja0NvbG9yICE9IG51bGwgJiYgdHlwZW9mIHRyYWNrQ29sb3IgPT09ICdvYmplY3QnKSB7XG4gICAgICAgIHJldHVybiB0cmFja0NvbG9yLnRydWU7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gYWN0aXZlVHJhY2tDb2xvciAhPT0gbnVsbCAmJiBhY3RpdmVUcmFja0NvbG9yICE9PSB2b2lkIDAgPyBhY3RpdmVUcmFja0NvbG9yIDogZGVmYXVsdEFjdGl2ZVRyYWNrQ29sb3I7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGlmICh0cmFja0NvbG9yICE9IG51bGwgJiYgdHlwZW9mIHRyYWNrQ29sb3IgPT09ICdvYmplY3QnKSB7XG4gICAgICAgIHJldHVybiB0cmFja0NvbG9yLmZhbHNlO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHRyYWNrQ29sb3IgIT09IG51bGwgJiYgdHJhY2tDb2xvciAhPT0gdm9pZCAwID8gdHJhY2tDb2xvciA6IGRlZmF1bHRUcmFja0NvbG9yO1xuICAgICAgfVxuICAgIH1cbiAgfSgpO1xuICB2YXIgdGh1bWJDdXJyZW50Q29sb3IgPSB2YWx1ZSA/IGFjdGl2ZVRodW1iQ29sb3IgIT09IG51bGwgJiYgYWN0aXZlVGh1bWJDb2xvciAhPT0gdm9pZCAwID8gYWN0aXZlVGh1bWJDb2xvciA6IGRlZmF1bHRBY3RpdmVUaHVtYkNvbG9yIDogdGh1bWJDb2xvciAhPT0gbnVsbCAmJiB0aHVtYkNvbG9yICE9PSB2b2lkIDAgPyB0aHVtYkNvbG9yIDogZGVmYXVsdFRodW1iQ29sb3I7XG4gIHZhciB0aHVtYkhlaWdodCA9IGhlaWdodDtcbiAgdmFyIHRodW1iV2lkdGggPSB0aHVtYkhlaWdodDtcbiAgdmFyIHJvb3RTdHlsZSA9IFtzdHlsZXMucm9vdCwgc3R5bGUsIGRpc2FibGVkICYmIHN0eWxlcy5jdXJzb3JEZWZhdWx0LCB7XG4gICAgaGVpZ2h0LFxuICAgIHdpZHRoXG4gIH1dO1xuICB2YXIgZGlzYWJsZWRUcmFja0NvbG9yID0gZnVuY3Rpb24gKCkge1xuICAgIGlmICh2YWx1ZSA9PT0gdHJ1ZSkge1xuICAgICAgaWYgKHR5cGVvZiBhY3RpdmVUcmFja0NvbG9yID09PSAnc3RyaW5nJyAmJiBhY3RpdmVUcmFja0NvbG9yICE9IG51bGwgfHwgdHlwZW9mIHRyYWNrQ29sb3IgPT09ICdvYmplY3QnICYmIHRyYWNrQ29sb3IgIT0gbnVsbCAmJiB0cmFja0NvbG9yLnRydWUpIHtcbiAgICAgICAgcmV0dXJuIHRyYWNrQ3VycmVudENvbG9yO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGRlZmF1bHREaXNhYmxlZFRyYWNrQ29sb3I7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGlmICh0eXBlb2YgdHJhY2tDb2xvciA9PT0gJ3N0cmluZycgJiYgdHJhY2tDb2xvciAhPSBudWxsIHx8IHR5cGVvZiB0cmFja0NvbG9yID09PSAnb2JqZWN0JyAmJiB0cmFja0NvbG9yICE9IG51bGwgJiYgdHJhY2tDb2xvci5mYWxzZSkge1xuICAgICAgICByZXR1cm4gdHJhY2tDdXJyZW50Q29sb3I7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gZGVmYXVsdERpc2FibGVkVHJhY2tDb2xvcjtcbiAgICAgIH1cbiAgICB9XG4gIH0oKTtcbiAgdmFyIGRpc2FibGVkVGh1bWJDb2xvciA9IGZ1bmN0aW9uICgpIHtcbiAgICBpZiAodmFsdWUgPT09IHRydWUpIHtcbiAgICAgIGlmIChhY3RpdmVUaHVtYkNvbG9yID09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIGRlZmF1bHREaXNhYmxlZFRodW1iQ29sb3I7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gdGh1bWJDdXJyZW50Q29sb3I7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIGlmICh0aHVtYkNvbG9yID09IG51bGwpIHtcbiAgICAgICAgcmV0dXJuIGRlZmF1bHREaXNhYmxlZFRodW1iQ29sb3I7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gdGh1bWJDdXJyZW50Q29sb3I7XG4gICAgICB9XG4gICAgfVxuICB9KCk7XG4gIHZhciB0cmFja1N0eWxlID0gW3N0eWxlcy50cmFjaywge1xuICAgIGJhY2tncm91bmRDb2xvcjogZGlzYWJsZWQgPyBkaXNhYmxlZFRyYWNrQ29sb3IgOiB0cmFja0N1cnJlbnRDb2xvcixcbiAgICBib3JkZXJSYWRpdXM6IHRyYWNrQm9yZGVyUmFkaXVzXG4gIH1dO1xuICB2YXIgdGh1bWJTdHlsZSA9IFtzdHlsZXMudGh1bWIsIHZhbHVlICYmIHN0eWxlcy50aHVtYkFjdGl2ZSwge1xuICAgIGJhY2tncm91bmRDb2xvcjogZGlzYWJsZWQgPyBkaXNhYmxlZFRodW1iQ29sb3IgOiB0aHVtYkN1cnJlbnRDb2xvcixcbiAgICBoZWlnaHQ6IHRodW1iSGVpZ2h0LFxuICAgIG1hcmdpblN0YXJ0OiB2YWx1ZSA/ICgwLCBfbXVsdGlwbHlTdHlsZUxlbmd0aFZhbHVlLmRlZmF1bHQpKHRodW1iV2lkdGgsIC0xKSA6IDAsXG4gICAgd2lkdGg6IHRodW1iV2lkdGhcbiAgfV07XG4gIHZhciBuYXRpdmVDb250cm9sID0gKDAsIF9jcmVhdGVFbGVtZW50LmRlZmF1bHQpKCdpbnB1dCcsIHtcbiAgICAnYXJpYS1sYWJlbCc6IGFyaWFMYWJlbCB8fCBhY2Nlc3NpYmlsaXR5TGFiZWwsXG4gICAgY2hlY2tlZDogdmFsdWUsXG4gICAgZGlzYWJsZWQ6IGRpc2FibGVkLFxuICAgIG9uQmx1cjogaGFuZGxlRm9jdXNTdGF0ZSxcbiAgICBvbkNoYW5nZTogaGFuZGxlQ2hhbmdlLFxuICAgIG9uRm9jdXM6IGhhbmRsZUZvY3VzU3RhdGUsXG4gICAgcmVmOiBmb3J3YXJkZWRSZWYsXG4gICAgc3R5bGU6IFtzdHlsZXMubmF0aXZlQ29udHJvbCwgc3R5bGVzLmN1cnNvckluaGVyaXRdLFxuICAgIHR5cGU6ICdjaGVja2JveCcsXG4gICAgcm9sZTogJ3N3aXRjaCdcbiAgfSk7XG4gIHJldHVybiAvKiNfX1BVUkVfXyovUmVhY3QuY3JlYXRlRWxlbWVudChfVmlldy5kZWZhdWx0LCAoMCwgX2V4dGVuZHMyLmRlZmF1bHQpKHt9LCBvdGhlciwge1xuICAgIHN0eWxlOiByb290U3R5bGVcbiAgfSksIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KF9WaWV3LmRlZmF1bHQsIHtcbiAgICBzdHlsZTogdHJhY2tTdHlsZVxuICB9KSwgLyojX19QVVJFX18qL1JlYWN0LmNyZWF0ZUVsZW1lbnQoX1ZpZXcuZGVmYXVsdCwge1xuICAgIHJlZjogdGh1bWJSZWYsXG4gICAgc3R5bGU6IHRodW1iU3R5bGVcbiAgfSksIG5hdGl2ZUNvbnRyb2wpO1xufSk7XG5Td2l0Y2guZGlzcGxheU5hbWUgPSAnU3dpdGNoJztcbnZhciBzdHlsZXMgPSBfU3R5bGVTaGVldC5kZWZhdWx0LmNyZWF0ZSh7XG4gIHJvb3Q6IHtcbiAgICBjdXJzb3I6ICdwb2ludGVyJyxcbiAgICB1c2VyU2VsZWN0OiAnbm9uZSdcbiAgfSxcbiAgY3Vyc29yRGVmYXVsdDoge1xuICAgIGN1cnNvcjogJ2RlZmF1bHQnXG4gIH0sXG4gIGN1cnNvckluaGVyaXQ6IHtcbiAgICBjdXJzb3I6ICdpbmhlcml0J1xuICB9LFxuICB0cmFjazogKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7XG4gICAgZm9yY2VkQ29sb3JBZGp1c3Q6ICdub25lJ1xuICB9LCBfU3R5bGVTaGVldC5kZWZhdWx0LmFic29sdXRlRmlsbE9iamVjdCksIHt9LCB7XG4gICAgaGVpZ2h0OiAnNzAlJyxcbiAgICBtYXJnaW46ICdhdXRvJyxcbiAgICB0cmFuc2l0aW9uRHVyYXRpb246ICcwLjFzJyxcbiAgICB3aWR0aDogJzEwMCUnXG4gIH0pLFxuICB0aHVtYjoge1xuICAgIGZvcmNlZENvbG9yQWRqdXN0OiAnbm9uZScsXG4gICAgYWxpZ25TZWxmOiAnZmxleC1zdGFydCcsXG4gICAgYm9yZGVyUmFkaXVzOiAnMTAwJScsXG4gICAgYm94U2hhZG93OiB0aHVtYkRlZmF1bHRCb3hTaGFkb3csXG4gICAgc3RhcnQ6ICcwJScsXG4gICAgdHJhbnNmb3JtOiAndHJhbnNsYXRlWigwKScsXG4gICAgdHJhbnNpdGlvbkR1cmF0aW9uOiAnMC4xcydcbiAgfSxcbiAgdGh1bWJBY3RpdmU6IHtcbiAgICBpbnNldElubGluZVN0YXJ0OiAnMTAwJSdcbiAgfSxcbiAgbmF0aXZlQ29udHJvbDogKDAsIF9vYmplY3RTcHJlYWQyLmRlZmF1bHQpKCgwLCBfb2JqZWN0U3ByZWFkMi5kZWZhdWx0KSh7fSwgX1N0eWxlU2hlZXQuZGVmYXVsdC5hYnNvbHV0ZUZpbGxPYmplY3QpLCB7fSwge1xuICAgIGhlaWdodDogJzEwMCUnLFxuICAgIG1hcmdpbjogMCxcbiAgICBhcHBlYXJhbmNlOiAnbm9uZScsXG4gICAgcGFkZGluZzogMCxcbiAgICB3aWR0aDogJzEwMCUnXG4gIH0pXG59KTtcbnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IFN3aXRjaDtcbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTtBQVVaLFlBQVk7O0FBRVosSUFBSUEsc0JBQXNCLEdBQUdDLE9BQU8sQ0FBQyw4Q0FBOEMsQ0FBQyxDQUFDQyxPQUFPO0FBQzVGLElBQUlDLHVCQUF1QixHQUFHRixPQUFPLENBQUMsK0NBQStDLENBQUMsQ0FBQ0MsT0FBTztBQUM5RkUsT0FBTyxDQUFDQyxVQUFVLEdBQUcsSUFBSTtBQUN6QkQsT0FBTyxDQUFDRixPQUFPLEdBQUcsS0FBSyxDQUFDO0FBQ3hCLElBQUlJLGNBQWMsR0FBR04sc0JBQXNCLENBQUNDLE9BQU8sQ0FBQyxzQ0FBc0MsQ0FBQyxDQUFDO0FBQzVGLElBQUlNLFNBQVMsR0FBR1Asc0JBQXNCLENBQUNDLE9BQU8sQ0FBQyxnQ0FBZ0MsQ0FBQyxDQUFDO0FBQ2pGLElBQUlPLDhCQUE4QixHQUFHUixzQkFBc0IsQ0FBQ0MsT0FBTyxDQUFDLHFEQUFxRCxDQUFDLENBQUM7QUFDM0gsSUFBSVEsS0FBSyxHQUFHTix1QkFBdUIsQ0FBQ0YsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDO0FBQ3JELElBQUlTLGNBQWMsR0FBR1Ysc0JBQXNCLENBQUNDLE9BQU8sbUJBQW1CLENBQUMsQ0FBQztBQUN4RSxJQUFJVSx5QkFBeUIsR0FBR1gsc0JBQXNCLENBQUNDLE9BQU8seUNBQXlDLENBQUMsQ0FBQztBQUN6RyxJQUFJVyxXQUFXLEdBQUdaLHNCQUFzQixDQUFDQyxPQUFPLGdCQUFnQixDQUFDLENBQUM7QUFDbEUsSUFBSVksS0FBSyxHQUFHYixzQkFBc0IsQ0FBQ0MsT0FBTyxVQUFVLENBQUMsQ0FBQztBQUN0RCxJQUFJYSxTQUFTLEdBQUcsQ0FBQyxZQUFZLEVBQUUsb0JBQW9CLEVBQUUsa0JBQWtCLEVBQUUsa0JBQWtCLEVBQUUsVUFBVSxFQUFFLGVBQWUsRUFBRSxPQUFPLEVBQUUsWUFBWSxFQUFFLFlBQVksRUFBRSxPQUFPLENBQUM7QUFDdkssSUFBSUMsV0FBVyxHQUFHLENBQUMsQ0FBQztBQUNwQixJQUFJQyxxQkFBcUIsR0FBRyw2QkFBNkI7QUFDekQsSUFBSUMscUJBQXFCLEdBQUdELHFCQUFxQixHQUFHLDhCQUE4QjtBQUNsRixJQUFJRSx1QkFBdUIsR0FBRyxTQUFTO0FBQ3ZDLElBQUlDLGlCQUFpQixHQUFHLFNBQVM7QUFDakMsSUFBSUMseUJBQXlCLEdBQUcsU0FBUztBQUN6QyxJQUFJQyx1QkFBdUIsR0FBRyxTQUFTO0FBQ3ZDLElBQUlDLGlCQUFpQixHQUFHLFNBQVM7QUFDakMsSUFBSUMseUJBQXlCLEdBQUcsU0FBUztBQUN6QyxJQUFJQyxNQUFNLEdBQWdCZixLQUFLLENBQUNnQixVQUFVLENBQUMsVUFBQ0MsS0FBSyxFQUFFQyxZQUFZLEVBQUs7RUFDbEUsSUFBSUMsU0FBUyxHQUFHRixLQUFLLENBQUMsWUFBWSxDQUFDO0lBQ2pDRyxrQkFBa0IsR0FBR0gsS0FBSyxDQUFDRyxrQkFBa0I7SUFDN0NDLGdCQUFnQixHQUFHSixLQUFLLENBQUNJLGdCQUFnQjtJQUN6Q0MsZ0JBQWdCLEdBQUdMLEtBQUssQ0FBQ0ssZ0JBQWdCO0lBQ3pDQyxlQUFlLEdBQUdOLEtBQUssQ0FBQ08sUUFBUTtJQUNoQ0EsUUFBUSxHQUFHRCxlQUFlLEtBQUssS0FBSyxDQUFDLEdBQUcsS0FBSyxHQUFHQSxlQUFlO0lBQy9ERSxhQUFhLEdBQUdSLEtBQUssQ0FBQ1EsYUFBYTtJQUNuQ0MsWUFBWSxHQUFHVCxLQUFLLENBQUNVLEtBQUs7SUFDMUJBLEtBQUssR0FBR0QsWUFBWSxLQUFLLEtBQUssQ0FBQyxHQUFHcEIsV0FBVyxHQUFHb0IsWUFBWTtJQUM1REUsVUFBVSxHQUFHWCxLQUFLLENBQUNXLFVBQVU7SUFDN0JDLFVBQVUsR0FBR1osS0FBSyxDQUFDWSxVQUFVO0lBQzdCQyxZQUFZLEdBQUdiLEtBQUssQ0FBQ2MsS0FBSztJQUMxQkEsS0FBSyxHQUFHRCxZQUFZLEtBQUssS0FBSyxDQUFDLEdBQUcsS0FBSyxHQUFHQSxZQUFZO0lBQ3RERSxLQUFLLEdBQUcsQ0FBQyxDQUFDLEVBQUVqQyw4QkFBOEIsQ0FBQ04sT0FBTyxFQUFFd0IsS0FBSyxFQUFFWixTQUFTLENBQUM7RUFDdkUsSUFBSTRCLFFBQVEsR0FBR2pDLEtBQUssQ0FBQ2tDLE1BQU0sQ0FBQyxJQUFJLENBQUM7RUFDakMsU0FBU0MsWUFBWUEsQ0FBQ0MsS0FBSyxFQUFFO0lBQzNCLElBQUlYLGFBQWEsSUFBSSxJQUFJLEVBQUU7TUFDekJBLGFBQWEsQ0FBQ1csS0FBSyxDQUFDQyxXQUFXLENBQUNDLE1BQU0sQ0FBQ0MsT0FBTyxDQUFDO0lBQ2pEO0VBQ0Y7RUFDQSxTQUFTQyxnQkFBZ0JBLENBQUNKLEtBQUssRUFBRTtJQUMvQixJQUFJSyxTQUFTLEdBQUdMLEtBQUssQ0FBQ0MsV0FBVyxDQUFDSyxJQUFJLEtBQUssT0FBTztJQUNsRCxJQUFJQyxTQUFTLEdBQUdGLFNBQVMsR0FBR2pDLHFCQUFxQixHQUFHRCxxQkFBcUI7SUFDekUsSUFBSTBCLFFBQVEsQ0FBQ1csT0FBTyxJQUFJLElBQUksRUFBRTtNQUM1QlgsUUFBUSxDQUFDVyxPQUFPLENBQUNqQixLQUFLLENBQUNnQixTQUFTLEdBQUdBLFNBQVM7SUFDOUM7RUFDRjtFQUNBLElBQUlFLG1CQUFtQixHQUFHMUMsV0FBVyxDQUFDVixPQUFPLENBQUNxRCxPQUFPLENBQUNuQixLQUFLLENBQUM7SUFDMURvQixXQUFXLEdBQUdGLG1CQUFtQixDQUFDRyxNQUFNO0lBQ3hDQyxVQUFVLEdBQUdKLG1CQUFtQixDQUFDSyxLQUFLO0VBQ3hDLElBQUlGLE1BQU0sR0FBR0QsV0FBVyxJQUFJLE1BQU07RUFDbEMsSUFBSUksUUFBUSxHQUFHLENBQUMsQ0FBQyxFQUFFakQseUJBQXlCLENBQUNULE9BQU8sRUFBRXVELE1BQU0sRUFBRSxDQUFDLENBQUM7RUFDaEUsSUFBSUUsS0FBSyxHQUFHRCxVQUFVLEdBQUdFLFFBQVEsR0FBR0YsVUFBVSxHQUFHRSxRQUFRO0VBQ3pELElBQUlDLGlCQUFpQixHQUFHLENBQUMsQ0FBQyxFQUFFbEQseUJBQXlCLENBQUNULE9BQU8sRUFBRXVELE1BQU0sRUFBRSxHQUFHLENBQUM7RUFDM0UsSUFBSUssaUJBQWlCLEdBQUcsWUFBWTtJQUNsQyxJQUFJdEIsS0FBSyxLQUFLLElBQUksRUFBRTtNQUNsQixJQUFJRixVQUFVLElBQUksSUFBSSxJQUFJLE9BQU9BLFVBQVUsS0FBSyxRQUFRLEVBQUU7UUFDeEQsT0FBT0EsVUFBVSxDQUFDeUIsSUFBSTtNQUN4QixDQUFDLE1BQU07UUFDTCxPQUFPaEMsZ0JBQWdCLEtBQUssSUFBSSxJQUFJQSxnQkFBZ0IsS0FBSyxLQUFLLENBQUMsR0FBR0EsZ0JBQWdCLEdBQUdiLHVCQUF1QjtNQUM5RztJQUNGLENBQUMsTUFBTTtNQUNMLElBQUlvQixVQUFVLElBQUksSUFBSSxJQUFJLE9BQU9BLFVBQVUsS0FBSyxRQUFRLEVBQUU7UUFDeEQsT0FBT0EsVUFBVSxDQUFDMEIsS0FBSztNQUN6QixDQUFDLE1BQU07UUFDTCxPQUFPMUIsVUFBVSxLQUFLLElBQUksSUFBSUEsVUFBVSxLQUFLLEtBQUssQ0FBQyxHQUFHQSxVQUFVLEdBQUduQixpQkFBaUI7TUFDdEY7SUFDRjtFQUNGLENBQUMsQ0FBQyxDQUFDO0VBQ0gsSUFBSThDLGlCQUFpQixHQUFHekIsS0FBSyxHQUFHVixnQkFBZ0IsS0FBSyxJQUFJLElBQUlBLGdCQUFnQixLQUFLLEtBQUssQ0FBQyxHQUFHQSxnQkFBZ0IsR0FBR1QsdUJBQXVCLEdBQUdnQixVQUFVLEtBQUssSUFBSSxJQUFJQSxVQUFVLEtBQUssS0FBSyxDQUFDLEdBQUdBLFVBQVUsR0FBR2YsaUJBQWlCO0VBQ3JOLElBQUk0QyxXQUFXLEdBQUdULE1BQU07RUFDeEIsSUFBSVUsVUFBVSxHQUFHRCxXQUFXO0VBQzVCLElBQUlFLFNBQVMsR0FBRyxDQUFDQyxNQUFNLENBQUNDLElBQUksRUFBRWxDLEtBQUssRUFBRUgsUUFBUSxJQUFJb0MsTUFBTSxDQUFDRSxhQUFhLEVBQUU7SUFDckVkLE1BQU0sRUFBTkEsTUFBTTtJQUNORSxLQUFLLEVBQUxBO0VBQ0YsQ0FBQyxDQUFDO0VBQ0YsSUFBSWEsa0JBQWtCLEdBQUcsWUFBWTtJQUNuQyxJQUFJaEMsS0FBSyxLQUFLLElBQUksRUFBRTtNQUNsQixJQUFJLE9BQU9ULGdCQUFnQixLQUFLLFFBQVEsSUFBSUEsZ0JBQWdCLElBQUksSUFBSSxJQUFJLE9BQU9PLFVBQVUsS0FBSyxRQUFRLElBQUlBLFVBQVUsSUFBSSxJQUFJLElBQUlBLFVBQVUsQ0FBQ3lCLElBQUksRUFBRTtRQUMvSSxPQUFPRCxpQkFBaUI7TUFDMUIsQ0FBQyxNQUFNO1FBQ0wsT0FBTzFDLHlCQUF5QjtNQUNsQztJQUNGLENBQUMsTUFBTTtNQUNMLElBQUksT0FBT2tCLFVBQVUsS0FBSyxRQUFRLElBQUlBLFVBQVUsSUFBSSxJQUFJLElBQUksT0FBT0EsVUFBVSxLQUFLLFFBQVEsSUFBSUEsVUFBVSxJQUFJLElBQUksSUFBSUEsVUFBVSxDQUFDMEIsS0FBSyxFQUFFO1FBQ3BJLE9BQU9GLGlCQUFpQjtNQUMxQixDQUFDLE1BQU07UUFDTCxPQUFPMUMseUJBQXlCO01BQ2xDO0lBQ0Y7RUFDRixDQUFDLENBQUMsQ0FBQztFQUNILElBQUlxRCxrQkFBa0IsR0FBRyxZQUFZO0lBQ25DLElBQUlqQyxLQUFLLEtBQUssSUFBSSxFQUFFO01BQ2xCLElBQUlWLGdCQUFnQixJQUFJLElBQUksRUFBRTtRQUM1QixPQUFPUCx5QkFBeUI7TUFDbEMsQ0FBQyxNQUFNO1FBQ0wsT0FBTzBDLGlCQUFpQjtNQUMxQjtJQUNGLENBQUMsTUFBTTtNQUNMLElBQUk1QixVQUFVLElBQUksSUFBSSxFQUFFO1FBQ3RCLE9BQU9kLHlCQUF5QjtNQUNsQyxDQUFDLE1BQU07UUFDTCxPQUFPMEMsaUJBQWlCO01BQzFCO0lBQ0Y7RUFDRixDQUFDLENBQUMsQ0FBQztFQUNILElBQUlTLFVBQVUsR0FBRyxDQUFDTCxNQUFNLENBQUNNLEtBQUssRUFBRTtJQUM5QkMsZUFBZSxFQUFFM0MsUUFBUSxHQUFHdUMsa0JBQWtCLEdBQUdWLGlCQUFpQjtJQUNsRWUsWUFBWSxFQUFFaEI7RUFDaEIsQ0FBQyxDQUFDO0VBQ0YsSUFBSWlCLFVBQVUsR0FBRyxDQUFDVCxNQUFNLENBQUNVLEtBQUssRUFBRXZDLEtBQUssSUFBSTZCLE1BQU0sQ0FBQ1csV0FBVyxFQUFFO0lBQzNESixlQUFlLEVBQUUzQyxRQUFRLEdBQUd3QyxrQkFBa0IsR0FBR1IsaUJBQWlCO0lBQ2xFUixNQUFNLEVBQUVTLFdBQVc7SUFDbkJlLFdBQVcsRUFBRXpDLEtBQUssR0FBRyxDQUFDLENBQUMsRUFBRTdCLHlCQUF5QixDQUFDVCxPQUFPLEVBQUVpRSxVQUFVLEVBQUUsQ0FBQyxDQUFDLENBQUMsR0FBRyxDQUFDO0lBQy9FUixLQUFLLEVBQUVRO0VBQ1QsQ0FBQyxDQUFDO0VBQ0YsSUFBSWUsYUFBYSxHQUFHLENBQUMsQ0FBQyxFQUFFeEUsY0FBYyxDQUFDUixPQUFPLEVBQUUsT0FBTyxFQUFFO0lBQ3ZELFlBQVksRUFBRTBCLFNBQVMsSUFBSUMsa0JBQWtCO0lBQzdDbUIsT0FBTyxFQUFFUixLQUFLO0lBQ2RQLFFBQVEsRUFBRUEsUUFBUTtJQUNsQmtELE1BQU0sRUFBRWxDLGdCQUFnQjtJQUN4Qm1DLFFBQVEsRUFBRXhDLFlBQVk7SUFDdEJ5QyxPQUFPLEVBQUVwQyxnQkFBZ0I7SUFDekJxQyxHQUFHLEVBQUUzRCxZQUFZO0lBQ2pCUyxLQUFLLEVBQUUsQ0FBQ2lDLE1BQU0sQ0FBQ2EsYUFBYSxFQUFFYixNQUFNLENBQUNrQixhQUFhLENBQUM7SUFDbkRwQyxJQUFJLEVBQUUsVUFBVTtJQUNoQnFDLElBQUksRUFBRTtFQUNSLENBQUMsQ0FBQztFQUNGLE9BQW9CL0UsS0FBSyxDQUFDZ0YsYUFBYSxDQUFDNUUsS0FBSyxDQUFDWCxPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVLLFNBQVMsQ0FBQ0wsT0FBTyxFQUFFLENBQUMsQ0FBQyxFQUFFdUMsS0FBSyxFQUFFO0lBQ3ZGTCxLQUFLLEVBQUVnQztFQUNULENBQUMsQ0FBQyxFQUFlM0QsS0FBSyxDQUFDZ0YsYUFBYSxDQUFDNUUsS0FBSyxDQUFDWCxPQUFPLEVBQUU7SUFDbERrQyxLQUFLLEVBQUVzQztFQUNULENBQUMsQ0FBQyxFQUFlakUsS0FBSyxDQUFDZ0YsYUFBYSxDQUFDNUUsS0FBSyxDQUFDWCxPQUFPLEVBQUU7SUFDbERvRixHQUFHLEVBQUU1QyxRQUFRO0lBQ2JOLEtBQUssRUFBRTBDO0VBQ1QsQ0FBQyxDQUFDLEVBQUVJLGFBQWEsQ0FBQztBQUNwQixDQUFDLENBQUM7QUFDRjFELE1BQU0sQ0FBQ2tFLFdBQVcsR0FBRyxRQUFRO0FBQzdCLElBQUlyQixNQUFNLEdBQUd6RCxXQUFXLENBQUNWLE9BQU8sQ0FBQ3lGLE1BQU0sQ0FBQztFQUN0Q3JCLElBQUksRUFBRTtJQUNKc0IsTUFBTSxFQUFFLFNBQVM7SUFDakJDLFVBQVUsRUFBRTtFQUNkLENBQUM7RUFDRHRCLGFBQWEsRUFBRTtJQUNicUIsTUFBTSxFQUFFO0VBQ1YsQ0FBQztFQUNETCxhQUFhLEVBQUU7SUFDYkssTUFBTSxFQUFFO0VBQ1YsQ0FBQztFQUNEakIsS0FBSyxFQUFFLENBQUMsQ0FBQyxFQUFFckUsY0FBYyxDQUFDSixPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVJLGNBQWMsQ0FBQ0osT0FBTyxFQUFFO0lBQzdENEYsaUJBQWlCLEVBQUU7RUFDckIsQ0FBQyxFQUFFbEYsV0FBVyxDQUFDVixPQUFPLENBQUM2RixrQkFBa0IsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFO0lBQzlDdEMsTUFBTSxFQUFFLEtBQUs7SUFDYnVDLE1BQU0sRUFBRSxNQUFNO0lBQ2RDLGtCQUFrQixFQUFFLE1BQU07SUFDMUJ0QyxLQUFLLEVBQUU7RUFDVCxDQUFDLENBQUM7RUFDRm9CLEtBQUssRUFBRTtJQUNMZSxpQkFBaUIsRUFBRSxNQUFNO0lBQ3pCSSxTQUFTLEVBQUUsWUFBWTtJQUN2QnJCLFlBQVksRUFBRSxNQUFNO0lBQ3BCekIsU0FBUyxFQUFFcEMscUJBQXFCO0lBQ2hDbUYsS0FBSyxFQUFFLElBQUk7SUFDWEMsU0FBUyxFQUFFLGVBQWU7SUFDMUJILGtCQUFrQixFQUFFO0VBQ3RCLENBQUM7RUFDRGpCLFdBQVcsRUFBRTtJQUNYcUIsZ0JBQWdCLEVBQUU7RUFDcEIsQ0FBQztFQUNEbkIsYUFBYSxFQUFFLENBQUMsQ0FBQyxFQUFFNUUsY0FBYyxDQUFDSixPQUFPLEVBQUUsQ0FBQyxDQUFDLEVBQUVJLGNBQWMsQ0FBQ0osT0FBTyxFQUFFLENBQUMsQ0FBQyxFQUFFVSxXQUFXLENBQUNWLE9BQU8sQ0FBQzZGLGtCQUFrQixDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUU7SUFDdEh0QyxNQUFNLEVBQUUsTUFBTTtJQUNkdUMsTUFBTSxFQUFFLENBQUM7SUFDVE0sVUFBVSxFQUFFLE1BQU07SUFDbEJDLE9BQU8sRUFBRSxDQUFDO0lBQ1Y1QyxLQUFLLEVBQUU7RUFDVCxDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBQ0YsSUFBSTZDLFFBQVEsR0FBR3BHLE9BQU8sQ0FBQ0YsT0FBTyxHQUFHc0IsTUFBTTtBQUN2Q2lGLE1BQU0sQ0FBQ3JHLE9BQU8sR0FBR0EsT0FBTyxDQUFDRixPQUFPIiwiaWdub3JlTGlzdCI6W119