b79d9df4b1d198c3abadd371fd0249ce
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1yxf45akko() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useOffline.ts";
  var hash = "ae2b250b062ff311ff380e0ddbc25a53b12b33ba";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useOffline.ts",
    statementMap: {
      "0": {
        start: {
          line: 35,
          column: 34
        },
        end: {
          line: 35,
          column: 48
        }
      },
      "1": {
        start: {
          line: 36,
          column: 38
        },
        end: {
          line: 41,
          column: 4
        }
      },
      "2": {
        start: {
          line: 42,
          column: 28
        },
        end: {
          line: 42,
          column: 57
        }
      },
      "3": {
        start: {
          line: 47,
          column: 20
        },
        end: {
          line: 60,
          column: 8
        }
      },
      "4": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 59,
          column: 5
        }
      },
      "5": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 21
        }
      },
      "6": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 54,
          column: 67
        }
      },
      "7": {
        start: {
          line: 56,
          column: 27
        },
        end: {
          line: 56,
          column: 86
        }
      },
      "8": {
        start: {
          line: 57,
          column: 6
        },
        end: {
          line: 57,
          column: 29
        }
      },
      "9": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 16
        }
      },
      "10": {
        start: {
          line: 65,
          column: 24
        },
        end: {
          line: 74,
          column: 8
        }
      },
      "11": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 73,
          column: 5
        }
      },
      "12": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 67,
          column: 21
        }
      },
      "13": {
        start: {
          line: 68,
          column: 6
        },
        end: {
          line: 68,
          column: 56
        }
      },
      "14": {
        start: {
          line: 70,
          column: 27
        },
        end: {
          line: 70,
          column: 91
        }
      },
      "15": {
        start: {
          line: 71,
          column: 6
        },
        end: {
          line: 71,
          column: 29
        }
      },
      "16": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 72,
          column: 18
        }
      },
      "17": {
        start: {
          line: 79,
          column: 27
        },
        end: {
          line: 88,
          column: 8
        }
      },
      "18": {
        start: {
          line: 80,
          column: 4
        },
        end: {
          line: 87,
          column: 5
        }
      },
      "19": {
        start: {
          line: 81,
          column: 6
        },
        end: {
          line: 81,
          column: 21
        }
      },
      "20": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 49
        }
      },
      "21": {
        start: {
          line: 84,
          column: 27
        },
        end: {
          line: 84,
          column: 94
        }
      },
      "22": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 85,
          column: 29
        }
      },
      "23": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 86,
          column: 16
        }
      },
      "24": {
        start: {
          line: 93,
          column: 21
        },
        end: {
          line: 102,
          column: 8
        }
      },
      "25": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 101,
          column: 5
        }
      },
      "26": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 95,
          column: 21
        }
      },
      "27": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 40
        }
      },
      "28": {
        start: {
          line: 98,
          column: 27
        },
        end: {
          line: 98,
          column: 87
        }
      },
      "29": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 29
        }
      },
      "30": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 100,
          column: 16
        }
      },
      "31": {
        start: {
          line: 107,
          column: 22
        },
        end: {
          line: 116,
          column: 8
        }
      },
      "32": {
        start: {
          line: 108,
          column: 4
        },
        end: {
          line: 115,
          column: 5
        }
      },
      "33": {
        start: {
          line: 109,
          column: 6
        },
        end: {
          line: 109,
          column: 21
        }
      },
      "34": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 110,
          column: 65
        }
      },
      "35": {
        start: {
          line: 112,
          column: 27
        },
        end: {
          line: 112,
          column: 88
        }
      },
      "36": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 113,
          column: 29
        }
      },
      "37": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 16
        }
      },
      "38": {
        start: {
          line: 121,
          column: 29
        },
        end: {
          line: 130,
          column: 8
        }
      },
      "39": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "40": {
        start: {
          line: 123,
          column: 6
        },
        end: {
          line: 123,
          column: 21
        }
      },
      "41": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 124,
          column: 48
        }
      },
      "42": {
        start: {
          line: 126,
          column: 27
        },
        end: {
          line: 126,
          column: 96
        }
      },
      "43": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 29
        }
      },
      "44": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 16
        }
      },
      "45": {
        start: {
          line: 135,
          column: 28
        },
        end: {
          line: 144,
          column: 8
        }
      },
      "46": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 143,
          column: 5
        }
      },
      "47": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 21
        }
      },
      "48": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 138,
          column: 54
        }
      },
      "49": {
        start: {
          line: 140,
          column: 27
        },
        end: {
          line: 140,
          column: 95
        }
      },
      "50": {
        start: {
          line: 141,
          column: 6
        },
        end: {
          line: 141,
          column: 29
        }
      },
      "51": {
        start: {
          line: 142,
          column: 6
        },
        end: {
          line: 142,
          column: 16
        }
      },
      "52": {
        start: {
          line: 149,
          column: 24
        },
        end: {
          line: 158,
          column: 8
        }
      },
      "53": {
        start: {
          line: 150,
          column: 4
        },
        end: {
          line: 157,
          column: 5
        }
      },
      "54": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 151,
          column: 21
        }
      },
      "55": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 152,
          column: 59
        }
      },
      "56": {
        start: {
          line: 154,
          column: 27
        },
        end: {
          line: 154,
          column: 91
        }
      },
      "57": {
        start: {
          line: 155,
          column: 6
        },
        end: {
          line: 155,
          column: 29
        }
      },
      "58": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 16
        }
      },
      "59": {
        start: {
          line: 163,
          column: 28
        },
        end: {
          line: 172,
          column: 8
        }
      },
      "60": {
        start: {
          line: 164,
          column: 4
        },
        end: {
          line: 171,
          column: 5
        }
      },
      "61": {
        start: {
          line: 165,
          column: 6
        },
        end: {
          line: 165,
          column: 21
        }
      },
      "62": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 60
        }
      },
      "63": {
        start: {
          line: 168,
          column: 27
        },
        end: {
          line: 168,
          column: 96
        }
      },
      "64": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 169,
          column: 29
        }
      },
      "65": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 18
        }
      },
      "66": {
        start: {
          line: 177,
          column: 32
        },
        end: {
          line: 186,
          column: 8
        }
      },
      "67": {
        start: {
          line: 178,
          column: 4
        },
        end: {
          line: 185,
          column: 5
        }
      },
      "68": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 179,
          column: 21
        }
      },
      "69": {
        start: {
          line: 180,
          column: 6
        },
        end: {
          line: 180,
          column: 67
        }
      },
      "70": {
        start: {
          line: 182,
          column: 27
        },
        end: {
          line: 182,
          column: 99
        }
      },
      "71": {
        start: {
          line: 183,
          column: 6
        },
        end: {
          line: 183,
          column: 29
        }
      },
      "72": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 184,
          column: 16
        }
      },
      "73": {
        start: {
          line: 191,
          column: 36
        },
        end: {
          line: 200,
          column: 8
        }
      },
      "74": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 199,
          column: 5
        }
      },
      "75": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 193,
          column: 21
        }
      },
      "76": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 194,
          column: 68
        }
      },
      "77": {
        start: {
          line: 196,
          column: 27
        },
        end: {
          line: 196,
          column: 104
        }
      },
      "78": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 29
        }
      },
      "79": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 198,
          column: 16
        }
      },
      "80": {
        start: {
          line: 205,
          column: 26
        },
        end: {
          line: 214,
          column: 8
        }
      },
      "81": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 213,
          column: 5
        }
      },
      "82": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 207,
          column: 21
        }
      },
      "83": {
        start: {
          line: 208,
          column: 6
        },
        end: {
          line: 208,
          column: 58
        }
      },
      "84": {
        start: {
          line: 210,
          column: 27
        },
        end: {
          line: 210,
          column: 93
        }
      },
      "85": {
        start: {
          line: 211,
          column: 6
        },
        end: {
          line: 211,
          column: 29
        }
      },
      "86": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 16
        }
      },
      "87": {
        start: {
          line: 219,
          column: 30
        },
        end: {
          line: 228,
          column: 8
        }
      },
      "88": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 227,
          column: 5
        }
      },
      "89": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 221,
          column: 21
        }
      },
      "90": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 62
        }
      },
      "91": {
        start: {
          line: 224,
          column: 27
        },
        end: {
          line: 224,
          column: 98
        }
      },
      "92": {
        start: {
          line: 225,
          column: 6
        },
        end: {
          line: 225,
          column: 29
        }
      },
      "93": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 18
        }
      },
      "94": {
        start: {
          line: 231,
          column: 2
        },
        end: {
          line: 261,
          column: 9
        }
      },
      "95": {
        start: {
          line: 234,
          column: 37
        },
        end: {
          line: 252,
          column: 5
        }
      },
      "96": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 251,
          column: 7
        }
      },
      "97": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 236,
          column: 42
        }
      },
      "98": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 242,
          column: 11
        }
      },
      "99": {
        start: {
          line: 240,
          column: 10
        },
        end: {
          line: 240,
          column: 32
        }
      },
      "100": {
        start: {
          line: 241,
          column: 10
        },
        end: {
          line: 241,
          column: 39
        }
      },
      "101": {
        start: {
          line: 245,
          column: 30
        },
        end: {
          line: 245,
          column: 66
        }
      },
      "102": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 246,
          column: 37
        }
      },
      "103": {
        start: {
          line: 247,
          column: 8
        },
        end: {
          line: 247,
          column: 44
        }
      },
      "104": {
        start: {
          line: 249,
          column: 29
        },
        end: {
          line: 249,
          column: 104
        }
      },
      "105": {
        start: {
          line: 250,
          column: 8
        },
        end: {
          line: 250,
          column: 31
        }
      },
      "106": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 254,
          column: 31
        }
      },
      "107": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 260,
          column: 6
        }
      },
      "108": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 259,
          column: 7
        }
      },
      "109": {
        start: {
          line: 258,
          column: 8
        },
        end: {
          line: 258,
          column: 22
        }
      },
      "110": {
        start: {
          line: 263,
          column: 2
        },
        end: {
          line: 291,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useOffline",
        decl: {
          start: {
            line: 34,
            column: 16
          },
          end: {
            line: 34,
            column: 26
          }
        },
        loc: {
          start: {
            line: 34,
            column: 47
          },
          end: {
            line: 292,
            column: 1
          }
        },
        line: 34
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 47,
            column: 32
          },
          end: {
            line: 47,
            column: 33
          }
        },
        loc: {
          start: {
            line: 51,
            column: 22
          },
          end: {
            line: 60,
            column: 3
          }
        },
        line: 51
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 65,
            column: 36
          },
          end: {
            line: 65,
            column: 37
          }
        },
        loc: {
          start: {
            line: 65,
            column: 81
          },
          end: {
            line: 74,
            column: 3
          }
        },
        line: 65
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 79,
            column: 39
          },
          end: {
            line: 79,
            column: 40
          }
        },
        loc: {
          start: {
            line: 79,
            column: 77
          },
          end: {
            line: 88,
            column: 3
          }
        },
        line: 79
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 93,
            column: 33
          },
          end: {
            line: 93,
            column: 34
          }
        },
        loc: {
          start: {
            line: 93,
            column: 60
          },
          end: {
            line: 102,
            column: 3
          }
        },
        line: 93
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 107,
            column: 34
          },
          end: {
            line: 107,
            column: 35
          }
        },
        loc: {
          start: {
            line: 107,
            column: 86
          },
          end: {
            line: 116,
            column: 3
          }
        },
        line: 107
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 121,
            column: 41
          },
          end: {
            line: 121,
            column: 42
          }
        },
        loc: {
          start: {
            line: 121,
            column: 68
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 121
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 135,
            column: 40
          },
          end: {
            line: 135,
            column: 41
          }
        },
        loc: {
          start: {
            line: 135,
            column: 78
          },
          end: {
            line: 144,
            column: 3
          }
        },
        line: 135
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 149,
            column: 36
          },
          end: {
            line: 149,
            column: 37
          }
        },
        loc: {
          start: {
            line: 149,
            column: 92
          },
          end: {
            line: 158,
            column: 3
          }
        },
        line: 149
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 163,
            column: 40
          },
          end: {
            line: 163,
            column: 41
          }
        },
        loc: {
          start: {
            line: 163,
            column: 80
          },
          end: {
            line: 172,
            column: 3
          }
        },
        line: 163
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 177,
            column: 44
          },
          end: {
            line: 177,
            column: 45
          }
        },
        loc: {
          start: {
            line: 177,
            column: 102
          },
          end: {
            line: 186,
            column: 3
          }
        },
        line: 177
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 191,
            column: 48
          },
          end: {
            line: 191,
            column: 49
          }
        },
        loc: {
          start: {
            line: 191,
            column: 90
          },
          end: {
            line: 200,
            column: 3
          }
        },
        line: 191
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 205,
            column: 38
          },
          end: {
            line: 205,
            column: 39
          }
        },
        loc: {
          start: {
            line: 205,
            column: 91
          },
          end: {
            line: 214,
            column: 3
          }
        },
        line: 205
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 219,
            column: 42
          },
          end: {
            line: 219,
            column: 43
          }
        },
        loc: {
          start: {
            line: 219,
            column: 82
          },
          end: {
            line: 228,
            column: 3
          }
        },
        line: 219
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 231,
            column: 12
          },
          end: {
            line: 231,
            column: 13
          }
        },
        loc: {
          start: {
            line: 231,
            column: 18
          },
          end: {
            line: 261,
            column: 3
          }
        },
        line: 231
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 234,
            column: 37
          },
          end: {
            line: 234,
            column: 38
          }
        },
        loc: {
          start: {
            line: 234,
            column: 49
          },
          end: {
            line: 252,
            column: 5
          }
        },
        line: 234
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 239,
            column: 56
          },
          end: {
            line: 239,
            column: 57
          }
        },
        loc: {
          start: {
            line: 239,
            column: 80
          },
          end: {
            line: 242,
            column: 9
          }
        },
        line: 239
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 256,
            column: 11
          },
          end: {
            line: 256,
            column: 12
          }
        },
        loc: {
          start: {
            line: 256,
            column: 17
          },
          end: {
            line: 260,
            column: 5
          }
        },
        line: 256
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 56,
            column: 27
          },
          end: {
            line: 56,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 56,
            column: 50
          },
          end: {
            line: 56,
            column: 61
          }
        }, {
          start: {
            line: 56,
            column: 64
          },
          end: {
            line: 56,
            column: 86
          }
        }],
        line: 56
      },
      "1": {
        loc: {
          start: {
            line: 70,
            column: 27
          },
          end: {
            line: 70,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 70,
            column: 50
          },
          end: {
            line: 70,
            column: 61
          }
        }, {
          start: {
            line: 70,
            column: 64
          },
          end: {
            line: 70,
            column: 91
          }
        }],
        line: 70
      },
      "2": {
        loc: {
          start: {
            line: 84,
            column: 27
          },
          end: {
            line: 84,
            column: 94
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 84,
            column: 50
          },
          end: {
            line: 84,
            column: 61
          }
        }, {
          start: {
            line: 84,
            column: 64
          },
          end: {
            line: 84,
            column: 94
          }
        }],
        line: 84
      },
      "3": {
        loc: {
          start: {
            line: 98,
            column: 27
          },
          end: {
            line: 98,
            column: 87
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 98,
            column: 50
          },
          end: {
            line: 98,
            column: 61
          }
        }, {
          start: {
            line: 98,
            column: 64
          },
          end: {
            line: 98,
            column: 87
          }
        }],
        line: 98
      },
      "4": {
        loc: {
          start: {
            line: 112,
            column: 27
          },
          end: {
            line: 112,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 112,
            column: 50
          },
          end: {
            line: 112,
            column: 61
          }
        }, {
          start: {
            line: 112,
            column: 64
          },
          end: {
            line: 112,
            column: 88
          }
        }],
        line: 112
      },
      "5": {
        loc: {
          start: {
            line: 126,
            column: 27
          },
          end: {
            line: 126,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 126,
            column: 50
          },
          end: {
            line: 126,
            column: 61
          }
        }, {
          start: {
            line: 126,
            column: 64
          },
          end: {
            line: 126,
            column: 96
          }
        }],
        line: 126
      },
      "6": {
        loc: {
          start: {
            line: 140,
            column: 27
          },
          end: {
            line: 140,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 140,
            column: 50
          },
          end: {
            line: 140,
            column: 61
          }
        }, {
          start: {
            line: 140,
            column: 64
          },
          end: {
            line: 140,
            column: 95
          }
        }],
        line: 140
      },
      "7": {
        loc: {
          start: {
            line: 154,
            column: 27
          },
          end: {
            line: 154,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 154,
            column: 50
          },
          end: {
            line: 154,
            column: 61
          }
        }, {
          start: {
            line: 154,
            column: 64
          },
          end: {
            line: 154,
            column: 91
          }
        }],
        line: 154
      },
      "8": {
        loc: {
          start: {
            line: 168,
            column: 27
          },
          end: {
            line: 168,
            column: 96
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 168,
            column: 50
          },
          end: {
            line: 168,
            column: 61
          }
        }, {
          start: {
            line: 168,
            column: 64
          },
          end: {
            line: 168,
            column: 96
          }
        }],
        line: 168
      },
      "9": {
        loc: {
          start: {
            line: 182,
            column: 27
          },
          end: {
            line: 182,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 182,
            column: 50
          },
          end: {
            line: 182,
            column: 61
          }
        }, {
          start: {
            line: 182,
            column: 64
          },
          end: {
            line: 182,
            column: 99
          }
        }],
        line: 182
      },
      "10": {
        loc: {
          start: {
            line: 196,
            column: 27
          },
          end: {
            line: 196,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 196,
            column: 50
          },
          end: {
            line: 196,
            column: 61
          }
        }, {
          start: {
            line: 196,
            column: 64
          },
          end: {
            line: 196,
            column: 104
          }
        }],
        line: 196
      },
      "11": {
        loc: {
          start: {
            line: 210,
            column: 27
          },
          end: {
            line: 210,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 210,
            column: 50
          },
          end: {
            line: 210,
            column: 61
          }
        }, {
          start: {
            line: 210,
            column: 64
          },
          end: {
            line: 210,
            column: 93
          }
        }],
        line: 210
      },
      "12": {
        loc: {
          start: {
            line: 224,
            column: 27
          },
          end: {
            line: 224,
            column: 98
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 224,
            column: 50
          },
          end: {
            line: 224,
            column: 61
          }
        }, {
          start: {
            line: 224,
            column: 64
          },
          end: {
            line: 224,
            column: 98
          }
        }],
        line: 224
      },
      "13": {
        loc: {
          start: {
            line: 249,
            column: 29
          },
          end: {
            line: 249,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 249,
            column: 52
          },
          end: {
            line: 249,
            column: 63
          }
        }, {
          start: {
            line: 249,
            column: 66
          },
          end: {
            line: 249,
            column: 104
          }
        }],
        line: 249
      },
      "14": {
        loc: {
          start: {
            line: 257,
            column: 6
          },
          end: {
            line: 259,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 6
          },
          end: {
            line: 259,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "15": {
        loc: {
          start: {
            line: 268,
            column: 18
          },
          end: {
            line: 268,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 268,
            column: 48
          },
          end: {
            line: 268,
            column: 81
          }
        }, {
          start: {
            line: 268,
            column: 84
          },
          end: {
            line: 268,
            column: 88
          }
        }],
        line: 268
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ae2b250b062ff311ff380e0ddbc25a53b12b33ba"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1yxf45akko = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1yxf45akko();
import { useState, useCallback, useEffect } from 'react';
import { offlineService } from "../services/offlineService";
export function useOffline() {
  cov_1yxf45akko().f[0]++;
  var _ref = (cov_1yxf45akko().s[0]++, useState(true)),
    _ref2 = _slicedToArray(_ref, 2),
    isOnline = _ref2[0],
    setIsOnline = _ref2[1];
  var _ref3 = (cov_1yxf45akko().s[1]++, useState({
      isOnline: true,
      lastSyncTime: 0,
      pendingActions: 0,
      failedActions: 0
    })),
    _ref4 = _slicedToArray(_ref3, 2),
    syncStatus = _ref4[0],
    setSyncStatus = _ref4[1];
  var _ref5 = (cov_1yxf45akko().s[2]++, useState(null)),
    _ref6 = _slicedToArray(_ref5, 2),
    error = _ref6[0],
    setError = _ref6[1];
  var cacheData = (cov_1yxf45akko().s[3]++, useCallback(function () {
    var _ref7 = _asyncToGenerator(function* (key, data, expirationMinutes) {
      cov_1yxf45akko().f[1]++;
      cov_1yxf45akko().s[4]++;
      try {
        cov_1yxf45akko().s[5]++;
        setError(null);
        cov_1yxf45akko().s[6]++;
        yield offlineService.cacheData(key, data, expirationMinutes);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[7]++, err instanceof Error ? (cov_1yxf45akko().b[0][0]++, err.message) : (cov_1yxf45akko().b[0][1]++, 'Failed to cache data'));
        cov_1yxf45akko().s[8]++;
        setError(errorMessage);
        cov_1yxf45akko().s[9]++;
        throw err;
      }
    });
    return function (_x, _x2, _x3) {
      return _ref7.apply(this, arguments);
    };
  }(), []));
  var getCachedData = (cov_1yxf45akko().s[10]++, useCallback(function () {
    var _ref8 = _asyncToGenerator(function* (key) {
      cov_1yxf45akko().f[2]++;
      cov_1yxf45akko().s[11]++;
      try {
        cov_1yxf45akko().s[12]++;
        setError(null);
        cov_1yxf45akko().s[13]++;
        return yield offlineService.getCachedData(key);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[14]++, err instanceof Error ? (cov_1yxf45akko().b[1][0]++, err.message) : (cov_1yxf45akko().b[1][1]++, 'Failed to get cached data'));
        cov_1yxf45akko().s[15]++;
        setError(errorMessage);
        cov_1yxf45akko().s[16]++;
        return null;
      }
    });
    return function (_x4) {
      return _ref8.apply(this, arguments);
    };
  }(), []));
  var removeCachedData = (cov_1yxf45akko().s[17]++, useCallback(function () {
    var _ref9 = _asyncToGenerator(function* (key) {
      cov_1yxf45akko().f[3]++;
      cov_1yxf45akko().s[18]++;
      try {
        cov_1yxf45akko().s[19]++;
        setError(null);
        cov_1yxf45akko().s[20]++;
        yield offlineService.removeCachedData(key);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[21]++, err instanceof Error ? (cov_1yxf45akko().b[2][0]++, err.message) : (cov_1yxf45akko().b[2][1]++, 'Failed to remove cached data'));
        cov_1yxf45akko().s[22]++;
        setError(errorMessage);
        cov_1yxf45akko().s[23]++;
        throw err;
      }
    });
    return function (_x5) {
      return _ref9.apply(this, arguments);
    };
  }(), []));
  var clearCache = (cov_1yxf45akko().s[24]++, useCallback(_asyncToGenerator(function* () {
    cov_1yxf45akko().f[4]++;
    cov_1yxf45akko().s[25]++;
    try {
      cov_1yxf45akko().s[26]++;
      setError(null);
      cov_1yxf45akko().s[27]++;
      yield offlineService.clearCache();
    } catch (err) {
      var errorMessage = (cov_1yxf45akko().s[28]++, err instanceof Error ? (cov_1yxf45akko().b[3][0]++, err.message) : (cov_1yxf45akko().b[3][1]++, 'Failed to clear cache'));
      cov_1yxf45akko().s[29]++;
      setError(errorMessage);
      cov_1yxf45akko().s[30]++;
      throw err;
    }
  }), []));
  var queueAction = (cov_1yxf45akko().s[31]++, useCallback(function () {
    var _ref1 = _asyncToGenerator(function* (type, data) {
      cov_1yxf45akko().f[5]++;
      cov_1yxf45akko().s[32]++;
      try {
        cov_1yxf45akko().s[33]++;
        setError(null);
        cov_1yxf45akko().s[34]++;
        return yield offlineService.queueOfflineAction(type, data);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[35]++, err instanceof Error ? (cov_1yxf45akko().b[4][0]++, err.message) : (cov_1yxf45akko().b[4][1]++, 'Failed to queue action'));
        cov_1yxf45akko().s[36]++;
        setError(errorMessage);
        cov_1yxf45akko().s[37]++;
        throw err;
      }
    });
    return function (_x6, _x7) {
      return _ref1.apply(this, arguments);
    };
  }(), []));
  var syncPendingActions = (cov_1yxf45akko().s[38]++, useCallback(_asyncToGenerator(function* () {
    cov_1yxf45akko().f[6]++;
    cov_1yxf45akko().s[39]++;
    try {
      cov_1yxf45akko().s[40]++;
      setError(null);
      cov_1yxf45akko().s[41]++;
      yield offlineService.syncPendingActions();
    } catch (err) {
      var errorMessage = (cov_1yxf45akko().s[42]++, err instanceof Error ? (cov_1yxf45akko().b[5][0]++, err.message) : (cov_1yxf45akko().b[5][1]++, 'Failed to sync pending actions'));
      cov_1yxf45akko().s[43]++;
      setError(errorMessage);
      cov_1yxf45akko().s[44]++;
      throw err;
    }
  }), []));
  var getPendingActions = (cov_1yxf45akko().s[45]++, useCallback(_asyncToGenerator(function* () {
    cov_1yxf45akko().f[7]++;
    cov_1yxf45akko().s[46]++;
    try {
      cov_1yxf45akko().s[47]++;
      setError(null);
      cov_1yxf45akko().s[48]++;
      return yield offlineService.getPendingActions();
    } catch (err) {
      var errorMessage = (cov_1yxf45akko().s[49]++, err instanceof Error ? (cov_1yxf45akko().b[6][0]++, err.message) : (cov_1yxf45akko().b[6][1]++, 'Failed to get pending actions'));
      cov_1yxf45akko().s[50]++;
      setError(errorMessage);
      cov_1yxf45akko().s[51]++;
      return [];
    }
  }), []));
  var cacheUserData = (cov_1yxf45akko().s[52]++, useCallback(function () {
    var _ref12 = _asyncToGenerator(function* (userId, userData) {
      cov_1yxf45akko().f[8]++;
      cov_1yxf45akko().s[53]++;
      try {
        cov_1yxf45akko().s[54]++;
        setError(null);
        cov_1yxf45akko().s[55]++;
        yield offlineService.cacheUserData(userId, userData);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[56]++, err instanceof Error ? (cov_1yxf45akko().b[7][0]++, err.message) : (cov_1yxf45akko().b[7][1]++, 'Failed to cache user data'));
        cov_1yxf45akko().s[57]++;
        setError(errorMessage);
        cov_1yxf45akko().s[58]++;
        throw err;
      }
    });
    return function (_x8, _x9) {
      return _ref12.apply(this, arguments);
    };
  }(), []));
  var getCachedUserData = (cov_1yxf45akko().s[59]++, useCallback(function () {
    var _ref13 = _asyncToGenerator(function* (userId) {
      cov_1yxf45akko().f[9]++;
      cov_1yxf45akko().s[60]++;
      try {
        cov_1yxf45akko().s[61]++;
        setError(null);
        cov_1yxf45akko().s[62]++;
        return yield offlineService.getCachedUserData(userId);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[63]++, err instanceof Error ? (cov_1yxf45akko().b[8][0]++, err.message) : (cov_1yxf45akko().b[8][1]++, 'Failed to get cached user data'));
        cov_1yxf45akko().s[64]++;
        setError(errorMessage);
        cov_1yxf45akko().s[65]++;
        return null;
      }
    });
    return function (_x0) {
      return _ref13.apply(this, arguments);
    };
  }(), []));
  var cacheTrainingSessions = (cov_1yxf45akko().s[66]++, useCallback(function () {
    var _ref14 = _asyncToGenerator(function* (userId, sessions) {
      cov_1yxf45akko().f[10]++;
      cov_1yxf45akko().s[67]++;
      try {
        cov_1yxf45akko().s[68]++;
        setError(null);
        cov_1yxf45akko().s[69]++;
        yield offlineService.cacheTrainingSessions(userId, sessions);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[70]++, err instanceof Error ? (cov_1yxf45akko().b[9][0]++, err.message) : (cov_1yxf45akko().b[9][1]++, 'Failed to cache training sessions'));
        cov_1yxf45akko().s[71]++;
        setError(errorMessage);
        cov_1yxf45akko().s[72]++;
        throw err;
      }
    });
    return function (_x1, _x10) {
      return _ref14.apply(this, arguments);
    };
  }(), []));
  var getCachedTrainingSessions = (cov_1yxf45akko().s[73]++, useCallback(function () {
    var _ref15 = _asyncToGenerator(function* (userId) {
      cov_1yxf45akko().f[11]++;
      cov_1yxf45akko().s[74]++;
      try {
        cov_1yxf45akko().s[75]++;
        setError(null);
        cov_1yxf45akko().s[76]++;
        return yield offlineService.getCachedTrainingSessions(userId);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[77]++, err instanceof Error ? (cov_1yxf45akko().b[10][0]++, err.message) : (cov_1yxf45akko().b[10][1]++, 'Failed to get cached training sessions'));
        cov_1yxf45akko().s[78]++;
        setError(errorMessage);
        cov_1yxf45akko().s[79]++;
        return [];
      }
    });
    return function (_x11) {
      return _ref15.apply(this, arguments);
    };
  }(), []));
  var cacheSkillStats = (cov_1yxf45akko().s[80]++, useCallback(function () {
    var _ref16 = _asyncToGenerator(function* (userId, stats) {
      cov_1yxf45akko().f[12]++;
      cov_1yxf45akko().s[81]++;
      try {
        cov_1yxf45akko().s[82]++;
        setError(null);
        cov_1yxf45akko().s[83]++;
        yield offlineService.cacheSkillStats(userId, stats);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[84]++, err instanceof Error ? (cov_1yxf45akko().b[11][0]++, err.message) : (cov_1yxf45akko().b[11][1]++, 'Failed to cache skill stats'));
        cov_1yxf45akko().s[85]++;
        setError(errorMessage);
        cov_1yxf45akko().s[86]++;
        throw err;
      }
    });
    return function (_x12, _x13) {
      return _ref16.apply(this, arguments);
    };
  }(), []));
  var getCachedSkillStats = (cov_1yxf45akko().s[87]++, useCallback(function () {
    var _ref17 = _asyncToGenerator(function* (userId) {
      cov_1yxf45akko().f[13]++;
      cov_1yxf45akko().s[88]++;
      try {
        cov_1yxf45akko().s[89]++;
        setError(null);
        cov_1yxf45akko().s[90]++;
        return yield offlineService.getCachedSkillStats(userId);
      } catch (err) {
        var errorMessage = (cov_1yxf45akko().s[91]++, err instanceof Error ? (cov_1yxf45akko().b[12][0]++, err.message) : (cov_1yxf45akko().b[12][1]++, 'Failed to get cached skill stats'));
        cov_1yxf45akko().s[92]++;
        setError(errorMessage);
        cov_1yxf45akko().s[93]++;
        return null;
      }
    });
    return function (_x14) {
      return _ref17.apply(this, arguments);
    };
  }(), []));
  cov_1yxf45akko().s[94]++;
  useEffect(function () {
    cov_1yxf45akko().f[14]++;
    var unsubscribe;
    cov_1yxf45akko().s[95]++;
    var initializeOfflineService = function () {
      var _ref18 = _asyncToGenerator(function* () {
        cov_1yxf45akko().f[15]++;
        cov_1yxf45akko().s[96]++;
        try {
          cov_1yxf45akko().s[97]++;
          yield offlineService.initialize();
          cov_1yxf45akko().s[98]++;
          unsubscribe = offlineService.onSyncStatusChange(function (status) {
            cov_1yxf45akko().f[16]++;
            cov_1yxf45akko().s[99]++;
            setSyncStatus(status);
            cov_1yxf45akko().s[100]++;
            setIsOnline(status.isOnline);
          });
          var initialStatus = (cov_1yxf45akko().s[101]++, yield offlineService.getSyncStatus());
          cov_1yxf45akko().s[102]++;
          setSyncStatus(initialStatus);
          cov_1yxf45akko().s[103]++;
          setIsOnline(initialStatus.isOnline);
        } catch (err) {
          var errorMessage = (cov_1yxf45akko().s[104]++, err instanceof Error ? (cov_1yxf45akko().b[13][0]++, err.message) : (cov_1yxf45akko().b[13][1]++, 'Failed to initialize offline service'));
          cov_1yxf45akko().s[105]++;
          setError(errorMessage);
        }
      });
      return function initializeOfflineService() {
        return _ref18.apply(this, arguments);
      };
    }();
    cov_1yxf45akko().s[106]++;
    initializeOfflineService();
    cov_1yxf45akko().s[107]++;
    return function () {
      cov_1yxf45akko().f[17]++;
      cov_1yxf45akko().s[108]++;
      if (unsubscribe) {
        cov_1yxf45akko().b[14][0]++;
        cov_1yxf45akko().s[109]++;
        unsubscribe();
      } else {
        cov_1yxf45akko().b[14][1]++;
      }
    };
  }, []);
  cov_1yxf45akko().s[110]++;
  return {
    isOnline: isOnline,
    syncStatus: syncStatus,
    pendingActions: syncStatus.pendingActions,
    lastSyncTime: syncStatus.lastSyncTime > 0 ? (cov_1yxf45akko().b[15][0]++, new Date(syncStatus.lastSyncTime)) : (cov_1yxf45akko().b[15][1]++, null),
    cacheData: cacheData,
    getCachedData: getCachedData,
    removeCachedData: removeCachedData,
    clearCache: clearCache,
    queueAction: queueAction,
    syncPendingActions: syncPendingActions,
    getPendingActions: getPendingActions,
    cacheUserData: cacheUserData,
    getCachedUserData: getCachedUserData,
    cacheTrainingSessions: cacheTrainingSessions,
    getCachedTrainingSessions: getCachedTrainingSessions,
    cacheSkillStats: cacheSkillStats,
    getCachedSkillStats: getCachedSkillStats,
    error: error
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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