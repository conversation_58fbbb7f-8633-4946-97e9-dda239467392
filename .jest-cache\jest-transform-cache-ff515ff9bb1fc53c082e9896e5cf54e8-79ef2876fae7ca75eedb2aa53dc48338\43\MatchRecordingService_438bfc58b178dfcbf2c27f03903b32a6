ec0eae657e68e5062a0dea751e20579c
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchRecordingService = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _VideoRecordingService = require("../video/VideoRecordingService");
var _MatchRepository = require("../database/MatchRepository");
var _FileUploadService = require("../storage/FileUploadService");
var _performance = require("../../../utils/performance");
var MatchRecordingService = function () {
  function MatchRecordingService() {
    (0, _classCallCheck2.default)(this, MatchRecordingService);
    this.currentSession = null;
    this.sessionListeners = [];
    this.scoreListeners = [];
    this.offlineSyncQueue = null;
    this.syncInterval = null;
    this.autoSaveInterval = null;
  }
  return (0, _createClass2.default)(MatchRecordingService, [{
    key: "startMatch",
    value: (function () {
      var _startMatch = (0, _asyncToGenerator2.default)(function* (metadata, options) {
        try {
          _performance.performanceMonitor.start('match_recording_start');
          this.validateMatchMetadata(metadata);
          if (this.currentSession) {
            throw new Error('Another match recording is already in progress');
          }
          var matchRecording = {
            id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            metadata: Object.assign({}, metadata, {
              startTime: new Date().toISOString()
            }),
            score: this.initializeScore(metadata.matchFormat),
            statistics: this.initializeStatistics(metadata.userId),
            status: 'recording',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          var session = {
            id: this.generateSessionId(),
            match: matchRecording,
            currentSet: 1,
            currentGame: 1,
            isRecording: true,
            isPaused: false,
            startTime: Date.now(),
            pausedTime: 0,
            totalPausedDuration: 0,
            videoRecordingActive: options.enableVideoRecording,
            autoScoreDetection: options.enableAutoScoreDetection
          };
          if (options.enableVideoRecording) {
            yield _VideoRecordingService.videoRecordingService.startRecording(options.videoConfig);
          }
          var savedMatch = yield this.saveMatchToDatabase(matchRecording);
          if (!savedMatch.success) {
            throw new Error(savedMatch.error || 'Failed to save match to database');
          }
          session.match.id = savedMatch.data.id;
          session.match.databaseId = savedMatch.data.databaseId;
          this.setupOfflineSync(session.match.id);
          this.currentSession = session;
          this.notifySessionListeners();
          this.startAutoSave();
          _performance.performanceMonitor.end('match_recording_start');
          return session;
        } catch (error) {
          console.error('Failed to start match recording:', error);
          if (this.currentSession) {
            yield this.cleanupFailedSession();
          }
          throw error;
        }
      });
      function startMatch(_x, _x2) {
        return _startMatch.apply(this, arguments);
      }
      return startMatch;
    }())
  }, {
    key: "addPoint",
    value: (function () {
      var _addPoint = (0, _asyncToGenerator2.default)(function* (winner) {
        var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'normal';
        var shotType = arguments.length > 2 ? arguments[2] : undefined;
        var courtPosition = arguments.length > 3 ? arguments[3] : undefined;
        if (!this.currentSession) {
          throw new Error('No active match session');
        }
        try {
          var session = this.currentSession;
          var currentSet = session.currentSet;
          var currentGame = session.currentGame;
          var gameEvent = {
            id: this.generateEventId(),
            timestamp: Date.now(),
            eventType: eventType === 'normal' ? 'point_won' : eventType,
            player: winner,
            shotType: shotType,
            courtPosition: courtPosition,
            description: `Point won by ${winner}`
          };
          var updatedScore = this.updateScore(session.match.score, currentSet, currentGame, winner, gameEvent);
          this.updateStatistics(session.match.statistics, gameEvent);
          session.match.score = updatedScore;
          session.match.updatedAt = new Date().toISOString();
          var setComplete = this.isSetComplete(updatedScore.sets[currentSet - 1]);
          var matchComplete = this.isMatchComplete(updatedScore, session.match.metadata.matchFormat);
          if (setComplete && !matchComplete) {
            session.currentSet++;
            session.currentGame = 1;
          } else if (!setComplete) {
            var gameComplete = this.isGameComplete(updatedScore.sets[currentSet - 1], currentGame);
            if (gameComplete) {
              session.currentGame++;
            }
          }
          if (matchComplete) {
            yield this.endMatch();
          } else {
            try {
              yield this.updateMatchInDatabase(session.match);
            } catch (error) {
              console.error('Failed to update match in database:', error);
            }
          }
          this.notifyScoreListeners();
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to add point:', error);
          throw error;
        }
      });
      function addPoint(_x3) {
        return _addPoint.apply(this, arguments);
      }
      return addPoint;
    }())
  }, {
    key: "pauseMatch",
    value: (function () {
      var _pauseMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession || this.currentSession.isPaused) {
          return;
        }
        try {
          this.currentSession.isPaused = true;
          this.currentSession.pausedTime = Date.now();
          this.currentSession.match.status = 'paused';
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.pauseRecording();
          }
          try {
            yield this.updateMatchInDatabase(this.currentSession.match);
          } catch (error) {
            console.error('Failed to update match in database:', error);
          }
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to pause match:', error);
          throw error;
        }
      });
      function pauseMatch() {
        return _pauseMatch.apply(this, arguments);
      }
      return pauseMatch;
    }())
  }, {
    key: "resumeMatch",
    value: (function () {
      var _resumeMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession || !this.currentSession.isPaused) {
          return;
        }
        try {
          var pauseDuration = Date.now() - this.currentSession.pausedTime;
          this.currentSession.totalPausedDuration += pauseDuration;
          this.currentSession.isPaused = false;
          this.currentSession.pausedTime = 0;
          this.currentSession.match.status = 'recording';
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.resumeRecording();
          }
          try {
            yield this.updateMatchInDatabase(this.currentSession.match);
          } catch (error) {
            console.error('Failed to update match in database:', error);
          }
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to resume match:', error);
          throw error;
        }
      });
      function resumeMatch() {
        return _resumeMatch.apply(this, arguments);
      }
      return resumeMatch;
    }())
  }, {
    key: "endMatch",
    value: (function () {
      var _endMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession) {
          throw new Error('No active match session');
        }
        try {
          _performance.performanceMonitor.start('match_recording_end');
          var session = this.currentSession;
          var endTime = Date.now();
          var totalDuration = (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60;
          session.match.metadata.endTime = new Date().toISOString();
          session.match.metadata.durationMinutes = Math.round(totalDuration);
          session.match.status = 'completed';
          if (session.videoRecordingActive) {
            var videoResult = yield _VideoRecordingService.videoRecordingService.stopRecording();
            var uploadResult = yield _FileUploadService.fileUploadService.uploadVideo(videoResult.uri, {
              folder: `matches/${session.match.id || 'temp'}`
            });
            if (uploadResult.data) {
              session.match.videoUrl = uploadResult.data.url;
              session.match.videoDurationSeconds = videoResult.duration;
              session.match.videoFileSizeBytes = uploadResult.data.size;
              if (videoResult.thumbnail) {
                var thumbnailResult = yield _FileUploadService.fileUploadService.uploadThumbnail(videoResult.uri, videoResult.thumbnail, {
                  folder: `matches/${session.match.id || 'temp'}/thumbnails`
                });
                if (thumbnailResult.data) {
                  session.match.videoThumbnailUrl = thumbnailResult.data.url;
                }
              }
            }
          }
          this.calculateFinalStatistics(session.match.statistics, session.match.score);
          yield this.updateMatchInDatabase(session.match);
          var finalMatch = Object.assign({}, session.match);
          this.currentSession = null;
          this.notifySessionListeners();
          _performance.performanceMonitor.end('match_recording_end');
          return finalMatch;
        } catch (error) {
          console.error('Failed to end match:', error);
          throw error;
        }
      });
      function endMatch() {
        return _endMatch.apply(this, arguments);
      }
      return endMatch;
    }())
  }, {
    key: "cancelMatch",
    value: (function () {
      var _cancelMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession) {
          return;
        }
        try {
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.stopRecording();
          }
          this.currentSession.match.status = 'cancelled';
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.currentSession = null;
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to cancel match:', error);
          throw error;
        }
      });
      function cancelMatch() {
        return _cancelMatch.apply(this, arguments);
      }
      return cancelMatch;
    }())
  }, {
    key: "getCurrentSession",
    value: function getCurrentSession() {
      return this.currentSession;
    }
  }, {
    key: "addSessionListener",
    value: function addSessionListener(listener) {
      this.sessionListeners.push(listener);
    }
  }, {
    key: "removeSessionListener",
    value: function removeSessionListener(listener) {
      this.sessionListeners = this.sessionListeners.filter(function (l) {
        return l !== listener;
      });
    }
  }, {
    key: "addScoreListener",
    value: function addScoreListener(listener) {
      this.scoreListeners.push(listener);
    }
  }, {
    key: "removeScoreListener",
    value: function removeScoreListener(listener) {
      this.scoreListeners = this.scoreListeners.filter(function (l) {
        return l !== listener;
      });
    }
  }, {
    key: "validateMatchMetadata",
    value: function validateMatchMetadata(metadata) {
      var _metadata$opponentNam;
      if (!((_metadata$opponentNam = metadata.opponentName) != null && _metadata$opponentNam.trim())) {
        throw new Error('Opponent name is required');
      }
      if (!metadata.userId) {
        throw new Error('User ID is required');
      }
      if (!metadata.matchType) {
        throw new Error('Match type is required');
      }
      if (!metadata.matchFormat) {
        throw new Error('Match format is required');
      }
      if (!metadata.surface) {
        throw new Error('Court surface is required');
      }
    }
  }, {
    key: "initializeScore",
    value: function initializeScore(format) {
      var maxSets = format === 'best_of_5' ? 5 : 3;
      return {
        sets: [],
        finalScore: '',
        result: 'win',
        setsWon: 0,
        setsLost: 0
      };
    }
  }, {
    key: "initializeStatistics",
    value: function initializeStatistics(userId) {
      return {
        matchId: '',
        userId: userId,
        aces: 0,
        doubleFaults: 0,
        firstServesIn: 0,
        firstServesAttempted: 0,
        firstServePointsWon: 0,
        secondServePointsWon: 0,
        firstServeReturnPointsWon: 0,
        secondServeReturnPointsWon: 0,
        breakPointsConverted: 0,
        breakPointsFaced: 0,
        winners: 0,
        unforcedErrors: 0,
        forcedErrors: 0,
        totalPointsWon: 0,
        totalPointsPlayed: 0,
        netPointsAttempted: 0,
        netPointsWon: 0,
        forehandWinners: 0,
        backhandWinners: 0,
        forehandErrors: 0,
        backhandErrors: 0
      };
    }
  }, {
    key: "updateScore",
    value: function updateScore(currentScore, setNumber, gameNumber, winner, event) {
      var updatedScore = Object.assign({}, currentScore);
      while (updatedScore.sets.length < setNumber) {
        updatedScore.sets.push({
          setNumber: updatedScore.sets.length + 1,
          userGames: 0,
          opponentGames: 0,
          isTiebreak: false,
          isCompleted: false
        });
      }
      var currentSet = updatedScore.sets[setNumber - 1];
      if (winner === 'user') {} else {}
      return updatedScore;
    }
  }, {
    key: "updateStatistics",
    value: function updateStatistics(statistics, event) {
      statistics.totalPointsPlayed++;
      if (event.player === 'user') {
        statistics.totalPointsWon++;
      }
      switch (event.eventType) {
        case 'ace':
          statistics.aces++;
          break;
        case 'double_fault':
          statistics.doubleFaults++;
          break;
        case 'winner':
          statistics.winners++;
          break;
        case 'unforced_error':
          statistics.unforcedErrors++;
          break;
        case 'forced_error':
          statistics.forcedErrors++;
          break;
      }
    }
  }, {
    key: "calculateFinalStatistics",
    value: function calculateFinalStatistics(statistics, score) {
      var _this$currentSession;
      if (statistics.firstServesAttempted > 0) {
        statistics.firstServePercentage = Math.round(statistics.firstServesIn / statistics.firstServesAttempted * 100);
      }
      if (statistics.breakPointsFaced > 0) {
        statistics.breakPointsSaved = statistics.breakPointsFaced - statistics.breakPointsConverted;
      }
      if (statistics.totalPointsPlayed > 0) {
        statistics.pointWinPercentage = Math.round(statistics.totalPointsWon / statistics.totalPointsPlayed * 100);
      }
      statistics.matchId = ((_this$currentSession = this.currentSession) == null ? void 0 : _this$currentSession.match.id) || '';
    }
  }, {
    key: "isSetComplete",
    value: function isSetComplete(set) {
      var userGames = set.userGames;
      var opponentGames = set.opponentGames;
      if (userGames >= 6 && userGames - opponentGames >= 2) {
        return true;
      }
      if (opponentGames >= 6 && opponentGames - userGames >= 2) {
        return true;
      }
      if (userGames === 7 && opponentGames === 6 || opponentGames === 7 && userGames === 6) {
        return true;
      }
      return false;
    }
  }, {
    key: "isGameComplete",
    value: function isGameComplete(set, gameNumber) {
      return false;
    }
  }, {
    key: "isMatchComplete",
    value: function isMatchComplete(score, format) {
      var setsToWin = format === 'best_of_5' ? 3 : 2;
      return score.setsWon >= setsToWin || score.setsLost >= setsToWin;
    }
  }, {
    key: "saveMatchToDatabase",
    value: function () {
      var _saveMatchToDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        try {
          var matchData = {
            id: match.id,
            user_id: match.metadata.userId,
            opponent_name: match.metadata.opponentName,
            match_type: match.metadata.matchType || 'friendly',
            match_format: match.metadata.matchFormat,
            surface: match.metadata.surface,
            location: match.metadata.location,
            court_name: match.metadata.courtName,
            weather_conditions: match.metadata.weather,
            temperature: match.metadata.temperature,
            match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],
            start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],
            status: match.status,
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            created_at: match.createdAt,
            updated_at: match.updatedAt
          };
          var attempts = 0;
          var maxAttempts = 3;
          while (attempts < maxAttempts) {
            try {
              var _result$data;
              var result = yield _MatchRepository.matchRepository.createMatch(matchData);
              if (result.error) {
                if (attempts === maxAttempts - 1) {
                  return {
                    success: false,
                    error: result.error
                  };
                }
                attempts++;
                yield new Promise(function (resolve) {
                  return setTimeout(resolve, 1000 * attempts);
                });
                continue;
              }
              return {
                success: true,
                data: {
                  id: match.id,
                  databaseId: (_result$data = result.data) == null ? void 0 : _result$data.id
                }
              };
            } catch (error) {
              attempts++;
              if (attempts === maxAttempts) {
                throw error;
              }
              yield new Promise(function (resolve) {
                return setTimeout(resolve, 1000 * attempts);
              });
            }
          }
          return {
            success: false,
            error: 'Failed to save after multiple attempts'
          };
        } catch (error) {
          console.error('Error saving match to database:', error);
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function saveMatchToDatabase(_x4) {
        return _saveMatchToDatabase.apply(this, arguments);
      }
      return saveMatchToDatabase;
    }()
  }, {
    key: "updateMatchInDatabase",
    value: function () {
      var _updateMatchInDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        try {
          if (!match.id) {
            throw new Error('Match ID is required for update');
          }
          var updateData = {
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            status: match.status,
            updated_at: new Date().toISOString()
          };
          if (match.status === 'completed' && match.metadata.endTime) {
            updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];
            updateData.duration_minutes = Math.round((new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60));
            updateData.final_score = this.generateFinalScoreString(match.score);
            updateData.result = this.determineMatchResult(match.score, match.metadata.userId);
            updateData.sets_won = match.score.setsWon;
            updateData.sets_lost = match.score.setsLost;
          }
          var result = yield _MatchRepository.matchRepository.updateMatch(match.id, updateData);
          if (result && result.error) {
            throw new Error(result.error);
          }
          return Object.assign({}, match, {
            updatedAt: new Date().toISOString()
          });
        } catch (error) {
          console.error('Error updating match in database:', error);
          throw new Error('Database connection failed');
        }
      });
      function updateMatchInDatabase(_x5) {
        return _updateMatchInDatabase.apply(this, arguments);
      }
      return updateMatchInDatabase;
    }()
  }, {
    key: "generateFinalScoreString",
    value: function generateFinalScoreString(score) {
      if (!score.sets || score.sets.length === 0) {
        return '0-0';
      }
      return score.sets.map(function (set) {
        return `${set.userGames}-${set.opponentGames}`;
      }).join(', ');
    }
  }, {
    key: "determineMatchResult",
    value: function determineMatchResult(score, userId) {
      if (score.setsWon > score.setsLost) {
        return 'win';
      } else if (score.setsLost > score.setsWon) {
        return 'loss';
      }
      return 'draw';
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateEventId",
    value: function generateEventId() {
      return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "notifySessionListeners",
    value: function notifySessionListeners() {
      var _this = this;
      this.sessionListeners.forEach(function (listener) {
        return listener(_this.currentSession);
      });
    }
  }, {
    key: "notifyScoreListeners",
    value: function notifyScoreListeners() {
      var _this2 = this;
      if (this.currentSession) {
        this.scoreListeners.forEach(function (listener) {
          return listener(_this2.currentSession.match.score);
        });
      }
    }
  }, {
    key: "setupOfflineSync",
    value: function setupOfflineSync(matchId) {
      try {
        if (!this.offlineSyncQueue) {
          this.offlineSyncQueue = new Map();
        }
        this.offlineSyncQueue.set(matchId, []);
        this.startOfflineSync(matchId);
      } catch (error) {
        console.error('Failed to setup offline sync:', error);
      }
    }
  }, {
    key: "startOfflineSync",
    value: function startOfflineSync(matchId) {
      var _this3 = this;
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
      }
      this.syncInterval = setInterval((0, _asyncToGenerator2.default)(function* () {
        yield _this3.syncOfflineData(matchId);
      }), 30000);
    }
  }, {
    key: "syncOfflineData",
    value: (function () {
      var _syncOfflineData = (0, _asyncToGenerator2.default)(function* (matchId) {
        try {
          var _this$offlineSyncQueu, _this$offlineSyncQueu2;
          var queue = (_this$offlineSyncQueu = this.offlineSyncQueue) == null ? void 0 : _this$offlineSyncQueu.get(matchId);
          if (!queue || queue.length === 0) {
            return;
          }
          var updates = (0, _toConsumableArray2.default)(queue);
          (_this$offlineSyncQueu2 = this.offlineSyncQueue) == null || _this$offlineSyncQueu2.set(matchId, []);
          for (var update of updates) {
            try {
              yield this.processOfflineUpdate(update);
            } catch (error) {
              var _this$offlineSyncQueu3;
              console.error('Failed to sync update:', error);
              (_this$offlineSyncQueu3 = this.offlineSyncQueue) == null || (_this$offlineSyncQueu3 = _this$offlineSyncQueu3.get(matchId)) == null || _this$offlineSyncQueu3.push(update);
            }
          }
        } catch (error) {
          console.error('Failed to sync offline data:', error);
        }
      });
      function syncOfflineData(_x6) {
        return _syncOfflineData.apply(this, arguments);
      }
      return syncOfflineData;
    }())
  }, {
    key: "processOfflineUpdate",
    value: (function () {
      var _processOfflineUpdate = (0, _asyncToGenerator2.default)(function* (update) {
        switch (update.type) {
          case 'match_update':
            yield this.updateMatchInDatabase(update.data);
            break;
          case 'score_update':
            break;
          case 'statistics_update':
            break;
          default:
            console.warn('Unknown update type:', update.type);
        }
      });
      function processOfflineUpdate(_x7) {
        return _processOfflineUpdate.apply(this, arguments);
      }
      return processOfflineUpdate;
    }())
  }, {
    key: "startAutoSave",
    value: function startAutoSave() {
      var _this4 = this;
      if (this.autoSaveInterval) {
        clearInterval(this.autoSaveInterval);
      }
      this.autoSaveInterval = setInterval((0, _asyncToGenerator2.default)(function* () {
        if (_this4.currentSession) {
          try {
            yield _this4.updateMatchInDatabase(_this4.currentSession.match);
          } catch (error) {
            console.error('Auto-save failed:', error);
          }
        }
      }), 120000);
    }
  }, {
    key: "cleanupFailedSession",
    value: (function () {
      var _cleanupFailedSession = (0, _asyncToGenerator2.default)(function* () {
        try {
          if (this.currentSession) {
            if (this.currentSession.videoRecordingActive) {
              yield _VideoRecordingService.videoRecordingService.stopRecording();
            }
            if (this.autoSaveInterval) {
              clearInterval(this.autoSaveInterval);
              this.autoSaveInterval = null;
            }
            if (this.syncInterval) {
              clearInterval(this.syncInterval);
              this.syncInterval = null;
            }
            this.currentSession = null;
          }
        } catch (error) {
          console.error('Failed to cleanup session:', error);
        }
      });
      function cleanupFailedSession() {
        return _cleanupFailedSession.apply(this, arguments);
      }
      return cleanupFailedSession;
    }())
  }]);
}();
var matchRecordingService = exports.matchRecordingService = new MatchRecordingService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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