{"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "exports", "__esModule", "VirtualizedListCellContextProvider", "VirtualizedListContext", "VirtualizedListContextProvider", "VirtualizedListContextResetter", "_objectSpread2", "_react", "React", "__DEV__", "process", "env", "NODE_ENV", "createContext", "displayName", "_ref", "children", "createElement", "Provider", "value", "_ref2", "context", "useMemo", "cellKey", "getScrollMetrics", "horizontal", "getOutermostParentListRef", "registerAsNestedChild", "unregisterAsNestedChild", "_ref3", "currContext", "useContext"], "sources": ["VirtualizedListContext.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.VirtualizedListCellContextProvider = VirtualizedListCellContextProvider;\nexports.VirtualizedListContext = void 0;\nexports.VirtualizedListContextProvider = VirtualizedListContextProvider;\nexports.VirtualizedListContextResetter = VirtualizedListContextResetter;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nvar VirtualizedListContext = exports.VirtualizedListContext = /*#__PURE__*/React.createContext(null);\nif (__DEV__) {\n  VirtualizedListContext.displayName = 'VirtualizedListContext';\n}\n\n/**\n * Resets the context. Intended for use by portal-like components (e.g. Modal).\n */\nfunction VirtualizedListContextResetter(_ref) {\n  var children = _ref.children;\n  return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n    value: null\n  }, children);\n}\n\n/**\n * Sets the context with memoization. Intended to be used by `VirtualizedList`.\n */\nfunction VirtualizedListContextProvider(_ref2) {\n  var children = _ref2.children,\n    value = _ref2.value;\n  // Avoid setting a newly created context object if the values are identical.\n  var context = (0, _react.useMemo)(() => ({\n    cellKey: null,\n    getScrollMetrics: value.getScrollMetrics,\n    horizontal: value.horizontal,\n    getOutermostParentListRef: value.getOutermostParentListRef,\n    registerAsNestedChild: value.registerAsNestedChild,\n    unregisterAsNestedChild: value.unregisterAsNestedChild\n  }), [value.getScrollMetrics, value.horizontal, value.getOutermostParentListRef, value.registerAsNestedChild, value.unregisterAsNestedChild]);\n  return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n    value: context\n  }, children);\n}\n\n/**\n * Sets the `cellKey`. Intended to be used by `VirtualizedList` for each cell.\n */\nfunction VirtualizedListCellContextProvider(_ref3) {\n  var cellKey = _ref3.cellKey,\n    children = _ref3.children;\n  // Avoid setting a newly created context object if the values are identical.\n  var currContext = (0, _react.useContext)(VirtualizedListContext);\n  var context = (0, _react.useMemo)(() => currContext == null ? null : (0, _objectSpread2.default)((0, _objectSpread2.default)({}, currContext), {}, {\n    cellKey\n  }), [currContext, cellKey]);\n  return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n    value: context\n  }, children);\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,kCAAkC,GAAGA,kCAAkC;AAC/EF,OAAO,CAACG,sBAAsB,GAAG,KAAK,CAAC;AACvCH,OAAO,CAACI,8BAA8B,GAAGA,8BAA8B;AACvEJ,OAAO,CAACK,8BAA8B,GAAGA,8BAA8B;AACvE,IAAIC,cAAc,GAAGP,sBAAsB,CAACF,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIU,MAAM,GAAGX,uBAAuB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,IAAIW,KAAK,GAAGD,MAAM;AAWlB,IAAIE,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACnD,IAAIT,sBAAsB,GAAGH,OAAO,CAACG,sBAAsB,GAAgBK,KAAK,CAACK,aAAa,CAAC,IAAI,CAAC;AACpG,IAAIJ,OAAO,EAAE;EACXN,sBAAsB,CAACW,WAAW,GAAG,wBAAwB;AAC/D;AAKA,SAAST,8BAA8BA,CAACU,IAAI,EAAE;EAC5C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAoBR,KAAK,CAACS,aAAa,CAACd,sBAAsB,CAACe,QAAQ,EAAE;IACvEC,KAAK,EAAE;EACT,CAAC,EAAEH,QAAQ,CAAC;AACd;AAKA,SAASZ,8BAA8BA,CAACgB,KAAK,EAAE;EAC7C,IAAIJ,QAAQ,GAAGI,KAAK,CAACJ,QAAQ;IAC3BG,KAAK,GAAGC,KAAK,CAACD,KAAK;EAErB,IAAIE,OAAO,GAAG,CAAC,CAAC,EAAEd,MAAM,CAACe,OAAO,EAAE;IAAA,OAAO;MACvCC,OAAO,EAAE,IAAI;MACbC,gBAAgB,EAAEL,KAAK,CAACK,gBAAgB;MACxCC,UAAU,EAAEN,KAAK,CAACM,UAAU;MAC5BC,yBAAyB,EAAEP,KAAK,CAACO,yBAAyB;MAC1DC,qBAAqB,EAAER,KAAK,CAACQ,qBAAqB;MAClDC,uBAAuB,EAAET,KAAK,CAACS;IACjC,CAAC;EAAA,CAAC,EAAE,CAACT,KAAK,CAACK,gBAAgB,EAAEL,KAAK,CAACM,UAAU,EAAEN,KAAK,CAACO,yBAAyB,EAAEP,KAAK,CAACQ,qBAAqB,EAAER,KAAK,CAACS,uBAAuB,CAAC,CAAC;EAC5I,OAAoBpB,KAAK,CAACS,aAAa,CAACd,sBAAsB,CAACe,QAAQ,EAAE;IACvEC,KAAK,EAAEE;EACT,CAAC,EAAEL,QAAQ,CAAC;AACd;AAKA,SAASd,kCAAkCA,CAAC2B,KAAK,EAAE;EACjD,IAAIN,OAAO,GAAGM,KAAK,CAACN,OAAO;IACzBP,QAAQ,GAAGa,KAAK,CAACb,QAAQ;EAE3B,IAAIc,WAAW,GAAG,CAAC,CAAC,EAAEvB,MAAM,CAACwB,UAAU,EAAE5B,sBAAsB,CAAC;EAChE,IAAIkB,OAAO,GAAG,CAAC,CAAC,EAAEd,MAAM,CAACe,OAAO,EAAE;IAAA,OAAMQ,WAAW,IAAI,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,EAAExB,cAAc,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEQ,cAAc,CAACR,OAAO,EAAE,CAAC,CAAC,EAAEgC,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;MACjJP,OAAO,EAAPA;IACF,CAAC,CAAC;EAAA,GAAE,CAACO,WAAW,EAAEP,OAAO,CAAC,CAAC;EAC3B,OAAoBf,KAAK,CAACS,aAAa,CAACd,sBAAsB,CAACe,QAAQ,EAAE;IACvEC,KAAK,EAAEE;EACT,CAAC,EAAEL,QAAQ,CAAC;AACd", "ignoreList": []}