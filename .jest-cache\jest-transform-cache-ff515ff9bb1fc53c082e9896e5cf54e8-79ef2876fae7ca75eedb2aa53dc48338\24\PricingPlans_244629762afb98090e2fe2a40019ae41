50e99313d6f4db49a6a1a2d1c52fcda3
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_zt71b318p() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\subscription\\PricingPlans.tsx";
  var hash = "d44244786d370c1cb2ea7033c7d1a768ced69c74";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\subscription\\PricingPlans.tsx",
    statementMap: {
      "0": {
        start: {
          line: 41,
          column: 36
        },
        end: {
          line: 41,
          column: 45
        }
      },
      "1": {
        start: {
          line: 42,
          column: 42
        },
        end: {
          line: 42,
          column: 83
        }
      },
      "2": {
        start: {
          line: 43,
          column: 32
        },
        end: {
          line: 43,
          column: 61
        }
      },
      "3": {
        start: {
          line: 45,
          column: 27
        },
        end: {
          line: 82,
          column: 3
        }
      },
      "4": {
        start: {
          line: 46,
          column: 4
        },
        end: {
          line: 49,
          column: 5
        }
      },
      "5": {
        start: {
          line: 47,
          column: 6
        },
        end: {
          line: 47,
          column: 80
        }
      },
      "6": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 13
        }
      },
      "7": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 54,
          column: 5
        }
      },
      "8": {
        start: {
          line: 52,
          column: 6
        },
        end: {
          line: 52,
          column: 68
        }
      },
      "9": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 13
        }
      },
      "10": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 59,
          column: 5
        }
      },
      "11": {
        start: {
          line: 57,
          column: 6
        },
        end: {
          line: 57,
          column: 78
        }
      },
      "12": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 13
        }
      },
      "13": {
        start: {
          line: 61,
          column: 4
        },
        end: {
          line: 61,
          column: 24
        }
      },
      "14": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 81,
          column: 5
        }
      },
      "15": {
        start: {
          line: 64,
          column: 6
        },
        end: {
          line: 76,
          column: 7
        }
      },
      "16": {
        start: {
          line: 65,
          column: 8
        },
        end: {
          line: 65,
          column: 41
        }
      },
      "17": {
        start: {
          line: 68,
          column: 8
        },
        end: {
          line: 75,
          column: 10
        }
      },
      "18": {
        start: {
          line: 73,
          column: 47
        },
        end: {
          line: 73,
          column: 81
        }
      },
      "19": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 80
        }
      },
      "20": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 80,
          column: 23
        }
      },
      "21": {
        start: {
          line: 84,
          column: 27
        },
        end: {
          line: 108,
          column: 3
        }
      },
      "22": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 88,
          column: 5
        }
      },
      "23": {
        start: {
          line: 86,
          column: 6
        },
        end: {
          line: 86,
          column: 79
        }
      },
      "24": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 87,
          column: 13
        }
      },
      "25": {
        start: {
          line: 90,
          column: 4
        },
        end: {
          line: 90,
          column: 35
        }
      },
      "26": {
        start: {
          line: 92,
          column: 4
        },
        end: {
          line: 107,
          column: 5
        }
      },
      "27": {
        start: {
          line: 93,
          column: 38
        },
        end: {
          line: 93,
          column: 82
        }
      },
      "28": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 102,
          column: 7
        }
      },
      "29": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 96,
          column: 42
        }
      },
      "30": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 101,
          column: 10
        }
      },
      "31": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 71
        }
      },
      "32": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 106,
          column: 23
        }
      },
      "33": {
        start: {
          line: 110,
          column: 24
        },
        end: {
          line: 120,
          column: 3
        }
      },
      "34": {
        start: {
          line: 111,
          column: 20
        },
        end: {
          line: 111,
          column: 52
        }
      },
      "35": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 112,
          column: 30
        }
      },
      "36": {
        start: {
          line: 112,
          column: 18
        },
        end: {
          line: 112,
          column: 30
        }
      },
      "37": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 119,
          column: 6
        }
      },
      "38": {
        start: {
          line: 122,
          column: 25
        },
        end: {
          line: 223,
          column: 3
        }
      },
      "39": {
        start: {
          line: 123,
          column: 26
        },
        end: {
          line: 123,
          column: 49
        }
      },
      "40": {
        start: {
          line: 124,
          column: 18
        },
        end: {
          line: 124,
          column: 85
        }
      },
      "41": {
        start: {
          line: 125,
          column: 26
        },
        end: {
          line: 125,
          column: 84
        }
      },
      "42": {
        start: {
          line: 126,
          column: 20
        },
        end: {
          line: 126,
          column: 105
        }
      },
      "43": {
        start: {
          line: 127,
          column: 26
        },
        end: {
          line: 127,
          column: 79
        }
      },
      "44": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 222,
          column: 6
        }
      },
      "45": {
        start: {
          line: 181,
          column: 31
        },
        end: {
          line: 181,
          column: 53
        }
      },
      "46": {
        start: {
          line: 195,
          column: 35
        },
        end: {
          line: 195,
          column: 57
        }
      },
      "47": {
        start: {
          line: 208,
          column: 33
        },
        end: {
          line: 208,
          column: 55
        }
      },
      "48": {
        start: {
          line: 225,
          column: 2
        },
        end: {
          line: 289,
          column: 4
        }
      },
      "49": {
        start: {
          line: 241,
          column: 25
        },
        end: {
          line: 241,
          column: 51
        }
      },
      "50": {
        start: {
          line: 258,
          column: 25
        },
        end: {
          line: 258,
          column: 50
        }
      },
      "51": {
        start: {
          line: 292,
          column: 15
        },
        end: {
          line: 532,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "PricingPlans",
        decl: {
          start: {
            line: 36,
            column: 16
          },
          end: {
            line: 36,
            column: 28
          }
        },
        loc: {
          start: {
            line: 40,
            column: 22
          },
          end: {
            line: 290,
            column: 1
          }
        },
        line: 40
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 45,
            column: 27
          },
          end: {
            line: 45,
            column: 28
          }
        },
        loc: {
          start: {
            line: 45,
            column: 61
          },
          end: {
            line: 82,
            column: 3
          }
        },
        line: 45
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 73,
            column: 41
          },
          end: {
            line: 73,
            column: 42
          }
        },
        loc: {
          start: {
            line: 73,
            column: 47
          },
          end: {
            line: 73,
            column: 81
          }
        },
        line: 73
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 84,
            column: 27
          },
          end: {
            line: 84,
            column: 28
          }
        },
        loc: {
          start: {
            line: 84,
            column: 61
          },
          end: {
            line: 108,
            column: 3
          }
        },
        line: 84
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 110,
            column: 24
          },
          end: {
            line: 110,
            column: 25
          }
        },
        loc: {
          start: {
            line: 110,
            column: 47
          },
          end: {
            line: 120,
            column: 3
          }
        },
        line: 110
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 122,
            column: 25
          },
          end: {
            line: 122,
            column: 26
          }
        },
        loc: {
          start: {
            line: 122,
            column: 53
          },
          end: {
            line: 223,
            column: 3
          }
        },
        line: 122
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 181,
            column: 25
          },
          end: {
            line: 181,
            column: 26
          }
        },
        loc: {
          start: {
            line: 181,
            column: 31
          },
          end: {
            line: 181,
            column: 53
          }
        },
        line: 181
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 195,
            column: 29
          },
          end: {
            line: 195,
            column: 30
          }
        },
        loc: {
          start: {
            line: 195,
            column: 35
          },
          end: {
            line: 195,
            column: 57
          }
        },
        line: 195
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 208,
            column: 27
          },
          end: {
            line: 208,
            column: 28
          }
        },
        loc: {
          start: {
            line: 208,
            column: 33
          },
          end: {
            line: 208,
            column: 55
          }
        },
        line: 208
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 241,
            column: 19
          },
          end: {
            line: 241,
            column: 20
          }
        },
        loc: {
          start: {
            line: 241,
            column: 25
          },
          end: {
            line: 241,
            column: 51
          }
        },
        line: 241
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 258,
            column: 19
          },
          end: {
            line: 258,
            column: 20
          }
        },
        loc: {
          start: {
            line: 258,
            column: 25
          },
          end: {
            line: 258,
            column: 50
          }
        },
        line: 258
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 38,
            column: 2
          },
          end: {
            line: 38,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 38,
            column: 16
          },
          end: {
            line: 38,
            column: 22
          }
        }],
        line: 38
      },
      "1": {
        loc: {
          start: {
            line: 39,
            column: 2
          },
          end: {
            line: 39,
            column: 24
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 39,
            column: 20
          },
          end: {
            line: 39,
            column: 24
          }
        }],
        line: 39
      },
      "2": {
        loc: {
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 49,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 46,
            column: 4
          },
          end: {
            line: 49,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 46
      },
      "3": {
        loc: {
          start: {
            line: 51,
            column: 4
          },
          end: {
            line: 54,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 4
          },
          end: {
            line: 54,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "4": {
        loc: {
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 56,
            column: 4
          },
          end: {
            line: 59,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 56
      },
      "5": {
        loc: {
          start: {
            line: 64,
            column: 6
          },
          end: {
            line: 76,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 64,
            column: 6
          },
          end: {
            line: 76,
            column: 7
          }
        }, {
          start: {
            line: 66,
            column: 13
          },
          end: {
            line: 76,
            column: 7
          }
        }],
        line: 64
      },
      "6": {
        loc: {
          start: {
            line: 85,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 85,
            column: 4
          },
          end: {
            line: 88,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 85
      },
      "7": {
        loc: {
          start: {
            line: 95,
            column: 6
          },
          end: {
            line: 102,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 95,
            column: 6
          },
          end: {
            line: 102,
            column: 7
          }
        }, {
          start: {
            line: 97,
            column: 13
          },
          end: {
            line: 102,
            column: 7
          }
        }],
        line: 95
      },
      "8": {
        loc: {
          start: {
            line: 112,
            column: 4
          },
          end: {
            line: 112,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 112,
            column: 4
          },
          end: {
            line: 112,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 112
      },
      "9": {
        loc: {
          start: {
            line: 124,
            column: 18
          },
          end: {
            line: 124,
            column: 85
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 124,
            column: 47
          },
          end: {
            line: 124,
            column: 65
          }
        }, {
          start: {
            line: 124,
            column: 68
          },
          end: {
            line: 124,
            column: 85
          }
        }],
        line: 124
      },
      "10": {
        loc: {
          start: {
            line: 125,
            column: 26
          },
          end: {
            line: 125,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 125,
            column: 54
          },
          end: {
            line: 125,
            column: 77
          }
        }, {
          start: {
            line: 125,
            column: 80
          },
          end: {
            line: 125,
            column: 84
          }
        }],
        line: 125
      },
      "11": {
        loc: {
          start: {
            line: 126,
            column: 20
          },
          end: {
            line: 126,
            column: 105
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 126,
            column: 36
          },
          end: {
            line: 126,
            column: 101
          }
        }, {
          start: {
            line: 126,
            column: 104
          },
          end: {
            line: 126,
            column: 105
          }
        }],
        line: 126
      },
      "12": {
        loc: {
          start: {
            line: 127,
            column: 26
          },
          end: {
            line: 127,
            column: 79
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 127,
            column: 26
          },
          end: {
            line: 127,
            column: 45
          }
        }, {
          start: {
            line: 127,
            column: 49
          },
          end: {
            line: 127,
            column: 79
          }
        }],
        line: 127
      },
      "13": {
        loc: {
          start: {
            line: 130,
            column: 51
          },
          end: {
            line: 130,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 130,
            column: 51
          },
          end: {
            line: 130,
            column: 64
          }
        }, {
          start: {
            line: 130,
            column: 68
          },
          end: {
            line: 130,
            column: 90
          }
        }],
        line: 130
      },
      "14": {
        loc: {
          start: {
            line: 131,
            column: 9
          },
          end: {
            line: 135,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 131,
            column: 9
          },
          end: {
            line: 131,
            column: 21
          }
        }, {
          start: {
            line: 132,
            column: 10
          },
          end: {
            line: 134,
            column: 17
          }
        }],
        line: 131
      },
      "15": {
        loc: {
          start: {
            line: 148,
            column: 13
          },
          end: {
            line: 152,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 148,
            column: 13
          },
          end: {
            line: 148,
            column: 35
          }
        }, {
          start: {
            line: 149,
            column: 14
          },
          end: {
            line: 151,
            column: 21
          }
        }],
        line: 148
      },
      "16": {
        loc: {
          start: {
            line: 150,
            column: 18
          },
          end: {
            line: 150,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 150,
            column: 47
          },
          end: {
            line: 150,
            column: 54
          }
        }, {
          start: {
            line: 150,
            column: 57
          },
          end: {
            line: 150,
            column: 63
          }
        }],
        line: 150
      },
      "17": {
        loc: {
          start: {
            line: 155,
            column: 11
          },
          end: {
            line: 160,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 155,
            column: 11
          },
          end: {
            line: 155,
            column: 36
          }
        }, {
          start: {
            line: 155,
            column: 40
          },
          end: {
            line: 155,
            column: 53
          }
        }, {
          start: {
            line: 155,
            column: 57
          },
          end: {
            line: 155,
            column: 68
          }
        }, {
          start: {
            line: 156,
            column: 12
          },
          end: {
            line: 159,
            column: 19
          }
        }],
        line: 155
      },
      "18": {
        loc: {
          start: {
            line: 166,
            column: 13
          },
          end: {
            line: 170,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 166,
            column: 13
          },
          end: {
            line: 166,
            column: 37
          }
        }, {
          start: {
            line: 167,
            column: 14
          },
          end: {
            line: 169,
            column: 21
          }
        }],
        line: 166
      },
      "19": {
        loc: {
          start: {
            line: 174,
            column: 13
          },
          end: {
            line: 218,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 175,
            column: 14
          },
          end: {
            line: 177,
            column: 21
          }
        }, {
          start: {
            line: 178,
            column: 16
          },
          end: {
            line: 218,
            column: 13
          }
        }],
        line: 174
      },
      "20": {
        loc: {
          start: {
            line: 178,
            column: 16
          },
          end: {
            line: 218,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 179,
            column: 14
          },
          end: {
            line: 189,
            column: 33
          }
        }, {
          start: {
            line: 191,
            column: 14
          },
          end: {
            line: 217,
            column: 21
          }
        }],
        line: 178
      },
      "21": {
        loc: {
          start: {
            line: 184,
            column: 17
          },
          end: {
            line: 188,
            column: 17
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 185,
            column: 18
          },
          end: {
            line: 185,
            column: 55
          }
        }, {
          start: {
            line: 187,
            column: 18
          },
          end: {
            line: 187,
            column: 76
          }
        }],
        line: 184
      },
      "22": {
        loc: {
          start: {
            line: 192,
            column: 17
          },
          end: {
            line: 204,
            column: 17
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 192,
            column: 17
          },
          end: {
            line: 192,
            column: 32
          }
        }, {
          start: {
            line: 193,
            column: 18
          },
          end: {
            line: 203,
            column: 37
          }
        }],
        line: 192
      },
      "23": {
        loc: {
          start: {
            line: 198,
            column: 21
          },
          end: {
            line: 202,
            column: 21
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 199,
            column: 22
          },
          end: {
            line: 199,
            column: 59
          }
        }, {
          start: {
            line: 201,
            column: 22
          },
          end: {
            line: 201,
            column: 82
          }
        }],
        line: 198
      },
      "24": {
        loc: {
          start: {
            line: 211,
            column: 19
          },
          end: {
            line: 215,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 212,
            column: 20
          },
          end: {
            line: 212,
            column: 57
          }
        }, {
          start: {
            line: 214,
            column: 20
          },
          end: {
            line: 214,
            column: 81
          }
        }],
        line: 211
      },
      "25": {
        loc: {
          start: {
            line: 239,
            column: 12
          },
          end: {
            line: 239,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 239,
            column: 12
          },
          end: {
            line: 239,
            column: 38
          }
        }, {
          start: {
            line: 239,
            column: 42
          },
          end: {
            line: 239,
            column: 68
          }
        }],
        line: 239
      },
      "26": {
        loc: {
          start: {
            line: 246,
            column: 14
          },
          end: {
            line: 246,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 246,
            column: 14
          },
          end: {
            line: 246,
            column: 40
          }
        }, {
          start: {
            line: 246,
            column: 44
          },
          end: {
            line: 246,
            column: 74
          }
        }],
        line: 246
      },
      "27": {
        loc: {
          start: {
            line: 256,
            column: 12
          },
          end: {
            line: 256,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 256,
            column: 12
          },
          end: {
            line: 256,
            column: 37
          }
        }, {
          start: {
            line: 256,
            column: 41
          },
          end: {
            line: 256,
            column: 67
          }
        }],
        line: 256
      },
      "28": {
        loc: {
          start: {
            line: 263,
            column: 14
          },
          end: {
            line: 263,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 263,
            column: 14
          },
          end: {
            line: 263,
            column: 39
          }
        }, {
          start: {
            line: 263,
            column: 43
          },
          end: {
            line: 263,
            column: 73
          }
        }],
        line: 263
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0, 0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d44244786d370c1cb2ea7033c7d1a768ced69c74"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_zt71b318p = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_zt71b318p();
import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { PRICING_PLANS, SUBSCRIPTION_FEATURES, formatPrice, calculateSavingsPercentage } from "../../config/subscription.config";
import { paymentService } from "../../services/payment/PaymentService";
import { useAuth } from "../../contexts/AuthContext";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export function PricingPlans(_ref) {
  var onSelectPlan = _ref.onSelectPlan,
    _ref$currentTier = _ref.currentTier,
    currentTier = _ref$currentTier === void 0 ? (cov_zt71b318p().b[0][0]++, 'free') : _ref$currentTier,
    _ref$showTrialButton = _ref.showTrialButton,
    showTrialButton = _ref$showTrialButton === void 0 ? (cov_zt71b318p().b[1][0]++, true) : _ref$showTrialButton;
  cov_zt71b318p().f[0]++;
  var _ref2 = (cov_zt71b318p().s[0]++, useAuth()),
    user = _ref2.user,
    isAuthenticated = _ref2.isAuthenticated;
  var _ref3 = (cov_zt71b318p().s[1]++, useState('monthly')),
    _ref4 = _slicedToArray(_ref3, 2),
    billingCycle = _ref4[0],
    setBillingCycle = _ref4[1];
  var _ref5 = (cov_zt71b318p().s[2]++, useState(null)),
    _ref6 = _slicedToArray(_ref5, 2),
    loading = _ref6[0],
    setLoading = _ref6[1];
  cov_zt71b318p().s[3]++;
  var handleSelectPlan = function () {
    var _ref7 = _asyncToGenerator(function* (tier) {
      cov_zt71b318p().f[1]++;
      cov_zt71b318p().s[4]++;
      if (!isAuthenticated()) {
        cov_zt71b318p().b[2][0]++;
        cov_zt71b318p().s[5]++;
        Alert.alert('Sign In Required', 'Please sign in to subscribe to a plan.');
        cov_zt71b318p().s[6]++;
        return;
      } else {
        cov_zt71b318p().b[2][1]++;
      }
      cov_zt71b318p().s[7]++;
      if (tier.id === 'free') {
        cov_zt71b318p().b[3][0]++;
        cov_zt71b318p().s[8]++;
        Alert.alert('Free Plan', 'You are already on the free plan.');
        cov_zt71b318p().s[9]++;
        return;
      } else {
        cov_zt71b318p().b[3][1]++;
      }
      cov_zt71b318p().s[10]++;
      if (tier.id === currentTier) {
        cov_zt71b318p().b[4][0]++;
        cov_zt71b318p().s[11]++;
        Alert.alert('Current Plan', 'You are already subscribed to this plan.');
        cov_zt71b318p().s[12]++;
        return;
      } else {
        cov_zt71b318p().b[4][1]++;
      }
      cov_zt71b318p().s[13]++;
      setLoading(tier.id);
      cov_zt71b318p().s[14]++;
      try {
        cov_zt71b318p().s[15]++;
        if (onSelectPlan) {
          cov_zt71b318p().b[5][0]++;
          cov_zt71b318p().s[16]++;
          onSelectPlan(tier, billingCycle);
        } else {
          cov_zt71b318p().b[5][1]++;
          cov_zt71b318p().s[17]++;
          Alert.alert('Subscribe to ' + tier.name, `You selected the ${tier.name} plan (${billingCycle}). This would normally open the payment flow.`, [{
            text: 'Cancel',
            style: 'cancel'
          }, {
            text: 'Continue',
            onPress: function onPress() {
              cov_zt71b318p().f[2]++;
              cov_zt71b318p().s[18]++;
              return console.log('Navigate to payment');
            }
          }]);
        }
      } catch (error) {
        cov_zt71b318p().s[19]++;
        Alert.alert('Error', 'Failed to process subscription. Please try again.');
      } finally {
        cov_zt71b318p().s[20]++;
        setLoading(null);
      }
    });
    return function handleSelectPlan(_x) {
      return _ref7.apply(this, arguments);
    };
  }();
  cov_zt71b318p().s[21]++;
  var handleStartTrial = function () {
    var _ref8 = _asyncToGenerator(function* (tier) {
      cov_zt71b318p().f[3]++;
      cov_zt71b318p().s[22]++;
      if (!isAuthenticated()) {
        cov_zt71b318p().b[6][0]++;
        cov_zt71b318p().s[23]++;
        Alert.alert('Sign In Required', 'Please sign in to start a free trial.');
        cov_zt71b318p().s[24]++;
        return;
      } else {
        cov_zt71b318p().b[6][1]++;
      }
      cov_zt71b318p().s[25]++;
      setLoading(`trial-${tier.id}`);
      cov_zt71b318p().s[26]++;
      try {
        var _ref9 = (cov_zt71b318p().s[27]++, yield paymentService.startFreeTrial(tier.id)),
          subscription = _ref9.subscription,
          error = _ref9.error;
        cov_zt71b318p().s[28]++;
        if (error) {
          cov_zt71b318p().b[7][0]++;
          cov_zt71b318p().s[29]++;
          Alert.alert('Trial Error', error);
        } else {
          cov_zt71b318p().b[7][1]++;
          cov_zt71b318p().s[30]++;
          Alert.alert('Trial Started!', `Your 14-day free trial of ${tier.name} has started. Enjoy all premium features!`);
        }
      } catch (error) {
        cov_zt71b318p().s[31]++;
        Alert.alert('Error', 'Failed to start trial. Please try again.');
      } finally {
        cov_zt71b318p().s[32]++;
        setLoading(null);
      }
    });
    return function handleStartTrial(_x2) {
      return _ref8.apply(this, arguments);
    };
  }();
  cov_zt71b318p().s[33]++;
  var renderFeature = function renderFeature(featureId) {
    cov_zt71b318p().f[4]++;
    var feature = (cov_zt71b318p().s[34]++, SUBSCRIPTION_FEATURES[featureId]);
    cov_zt71b318p().s[35]++;
    if (!feature) {
      cov_zt71b318p().b[8][0]++;
      cov_zt71b318p().s[36]++;
      return null;
    } else {
      cov_zt71b318p().b[8][1]++;
    }
    cov_zt71b318p().s[37]++;
    return _jsxs(View, {
      style: styles.featureItem,
      children: [_jsx(Ionicons, {
        name: "checkmark-circle",
        size: 16,
        color: "#10B981"
      }), _jsx(Text, {
        style: styles.featureText,
        children: feature.name
      })]
    }, featureId);
  };
  cov_zt71b318p().s[38]++;
  var renderPlanCard = function renderPlanCard(tier) {
    cov_zt71b318p().f[5]++;
    var isCurrentPlan = (cov_zt71b318p().s[39]++, tier.id === currentTier);
    var price = (cov_zt71b318p().s[40]++, billingCycle === 'monthly' ? (cov_zt71b318p().b[9][0]++, tier.price_monthly) : (cov_zt71b318p().b[9][1]++, tier.price_yearly));
    var originalPrice = (cov_zt71b318p().s[41]++, billingCycle === 'yearly' ? (cov_zt71b318p().b[10][0]++, tier.price_monthly * 12) : (cov_zt71b318p().b[10][1]++, null));
    var savings = (cov_zt71b318p().s[42]++, originalPrice ? (cov_zt71b318p().b[11][0]++, calculateSavingsPercentage(tier.price_monthly, tier.price_yearly)) : (cov_zt71b318p().b[11][1]++, 0));
    var isLoadingThis = (cov_zt71b318p().s[43]++, (cov_zt71b318p().b[12][0]++, loading === tier.id) || (cov_zt71b318p().b[12][1]++, loading === `trial-${tier.id}`));
    cov_zt71b318p().s[44]++;
    return _jsxs(View, {
      style: [styles.planCard, (cov_zt71b318p().b[13][0]++, isCurrentPlan) && (cov_zt71b318p().b[13][1]++, styles.currentPlanCard)],
      children: [(cov_zt71b318p().b[14][0]++, tier.popular) && (cov_zt71b318p().b[14][1]++, _jsx(View, {
        style: styles.popularBadge,
        children: _jsx(Text, {
          style: styles.popularBadgeText,
          children: tier.badge
        })
      })), _jsxs(LinearGradient, {
        colors: tier.gradient,
        style: styles.planHeader,
        start: {
          x: 0,
          y: 0
        },
        end: {
          x: 1,
          y: 1
        },
        children: [_jsx(Text, {
          style: styles.planName,
          children: tier.name
        }), _jsx(Text, {
          style: styles.planDescription,
          children: tier.description
        }), _jsxs(View, {
          style: styles.priceContainer,
          children: [_jsx(Text, {
            style: styles.price,
            children: formatPrice(price)
          }), (cov_zt71b318p().b[15][0]++, tier.price_monthly > 0) && (cov_zt71b318p().b[15][1]++, _jsxs(Text, {
            style: styles.pricePeriod,
            children: ["/", billingCycle === 'monthly' ? (cov_zt71b318p().b[16][0]++, 'month') : (cov_zt71b318p().b[16][1]++, 'year')]
          }))]
        }), (cov_zt71b318p().b[17][0]++, billingCycle === 'yearly') && (cov_zt71b318p().b[17][1]++, originalPrice) && (cov_zt71b318p().b[17][2]++, savings > 0) && (cov_zt71b318p().b[17][3]++, _jsxs(View, {
          style: styles.savingsContainer,
          children: [_jsx(Text, {
            style: styles.originalPrice,
            children: formatPrice(originalPrice)
          }), _jsxs(Text, {
            style: styles.savings,
            children: ["Save ", savings, "%"]
          })]
        }))]
      }), _jsxs(View, {
        style: styles.planBody,
        children: [_jsxs(View, {
          style: styles.featuresContainer,
          children: [tier.features.slice(0, 6).map(renderFeature), (cov_zt71b318p().b[18][0]++, tier.features.length > 6) && (cov_zt71b318p().b[18][1]++, _jsxs(Text, {
            style: styles.moreFeatures,
            children: ["+", tier.features.length - 6, " more features"]
          }))]
        }), _jsx(View, {
          style: styles.planActions,
          children: isCurrentPlan ? (cov_zt71b318p().b[19][0]++, _jsx(View, {
            style: styles.currentPlanButton,
            children: _jsx(Text, {
              style: styles.currentPlanButtonText,
              children: "Current Plan"
            })
          })) : (cov_zt71b318p().b[19][1]++, tier.id === 'free' ? (cov_zt71b318p().b[20][0]++, _jsx(TouchableOpacity, {
            style: [styles.planButton, styles.freePlanButton],
            onPress: function onPress() {
              cov_zt71b318p().f[6]++;
              cov_zt71b318p().s[45]++;
              return handleSelectPlan(tier);
            },
            disabled: isLoadingThis,
            children: isLoadingThis ? (cov_zt71b318p().b[21][0]++, _jsx(ActivityIndicator, {
              color: "#6B7280"
            })) : (cov_zt71b318p().b[21][1]++, _jsx(Text, {
              style: styles.freePlanButtonText,
              children: "Get Started"
            }))
          })) : (cov_zt71b318p().b[20][1]++, _jsxs(View, {
            style: styles.paidPlanActions,
            children: [(cov_zt71b318p().b[22][0]++, showTrialButton) && (cov_zt71b318p().b[22][1]++, _jsx(TouchableOpacity, {
              style: [styles.planButton, styles.trialButton],
              onPress: function onPress() {
                cov_zt71b318p().f[7]++;
                cov_zt71b318p().s[46]++;
                return handleStartTrial(tier);
              },
              disabled: isLoadingThis,
              children: loading === `trial-${tier.id}` ? (cov_zt71b318p().b[23][0]++, _jsx(ActivityIndicator, {
                color: "#FFFFFF"
              })) : (cov_zt71b318p().b[23][1]++, _jsx(Text, {
                style: styles.trialButtonText,
                children: "Start Free Trial"
              }))
            })), _jsx(TouchableOpacity, {
              style: [styles.planButton, styles.subscribePlanButton],
              onPress: function onPress() {
                cov_zt71b318p().f[8]++;
                cov_zt71b318p().s[47]++;
                return handleSelectPlan(tier);
              },
              disabled: isLoadingThis,
              children: loading === tier.id ? (cov_zt71b318p().b[24][0]++, _jsx(ActivityIndicator, {
                color: "#FFFFFF"
              })) : (cov_zt71b318p().b[24][1]++, _jsx(Text, {
                style: styles.subscribePlanButtonText,
                children: "Subscribe"
              }))
            })]
          })))
        })]
      })]
    }, tier.id);
  };
  cov_zt71b318p().s[48]++;
  return _jsxs(ScrollView, {
    style: styles.container,
    showsVerticalScrollIndicator: false,
    children: [_jsxs(View, {
      style: styles.header,
      children: [_jsx(Text, {
        style: styles.title,
        children: "Choose Your Plan"
      }), _jsx(Text, {
        style: styles.subtitle,
        children: "Unlock the full potential of your tennis training"
      })]
    }), _jsxs(View, {
      style: styles.billingToggle,
      children: [_jsx(TouchableOpacity, {
        style: [styles.billingOption, (cov_zt71b318p().b[25][0]++, billingCycle === 'monthly') && (cov_zt71b318p().b[25][1]++, styles.billingOptionActive)],
        onPress: function onPress() {
          cov_zt71b318p().f[9]++;
          cov_zt71b318p().s[49]++;
          return setBillingCycle('monthly');
        },
        children: _jsx(Text, {
          style: [styles.billingOptionText, (cov_zt71b318p().b[26][0]++, billingCycle === 'monthly') && (cov_zt71b318p().b[26][1]++, styles.billingOptionTextActive)],
          children: "Monthly"
        })
      }), _jsxs(TouchableOpacity, {
        style: [styles.billingOption, (cov_zt71b318p().b[27][0]++, billingCycle === 'yearly') && (cov_zt71b318p().b[27][1]++, styles.billingOptionActive)],
        onPress: function onPress() {
          cov_zt71b318p().f[10]++;
          cov_zt71b318p().s[50]++;
          return setBillingCycle('yearly');
        },
        children: [_jsx(Text, {
          style: [styles.billingOptionText, (cov_zt71b318p().b[28][0]++, billingCycle === 'yearly') && (cov_zt71b318p().b[28][1]++, styles.billingOptionTextActive)],
          children: "Yearly"
        }), _jsx(View, {
          style: styles.savingsBadge,
          children: _jsx(Text, {
            style: styles.savingsBadgeText,
            children: "Save 17%"
          })
        })]
      })]
    }), _jsx(View, {
      style: styles.plansContainer,
      children: PRICING_PLANS.map(renderPlanCard)
    }), _jsxs(View, {
      style: styles.footer,
      children: [_jsx(Text, {
        style: styles.footerText,
        children: "All plans include a 14-day free trial. Cancel anytime."
      }), _jsx(Text, {
        style: styles.footerSubtext,
        children: "Prices may vary by region. Taxes may apply."
      })]
    })]
  });
}
var styles = (cov_zt71b318p().s[51]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  header: {
    padding: 24,
    alignItems: 'center'
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center'
  },
  billingToggle: {
    flexDirection: 'row',
    backgroundColor: '#E5E7EB',
    borderRadius: 12,
    padding: 4,
    marginHorizontal: 24,
    marginBottom: 24
  },
  billingOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center'
  },
  billingOptionActive: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  billingOptionText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#6B7280'
  },
  billingOptionTextActive: {
    color: '#111827'
  },
  savingsBadge: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    marginLeft: 8
  },
  savingsBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  plansContainer: {
    paddingHorizontal: 24,
    gap: 16
  },
  planCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4
  },
  currentPlanCard: {
    borderWidth: 2,
    borderColor: '#10B981'
  },
  popularBadge: {
    position: 'absolute',
    top: 16,
    right: 16,
    backgroundColor: '#F59E0B',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 1
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  planHeader: {
    padding: 24,
    alignItems: 'center'
  },
  planName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8
  },
  planDescription: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: 16
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'baseline'
  },
  price: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#FFFFFF'
  },
  pricePeriod: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
    marginLeft: 4
  },
  savingsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8
  },
  originalPrice: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.7,
    textDecorationLine: 'line-through',
    marginRight: 8
  },
  savings: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8
  },
  planBody: {
    padding: 24
  },
  featuresContainer: {
    marginBottom: 24
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  featureText: {
    fontSize: 14,
    color: '#374151',
    marginLeft: 8,
    flex: 1
  },
  moreFeatures: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
    marginTop: 8
  },
  planActions: {
    gap: 12
  },
  paidPlanActions: {
    gap: 12
  },
  planButton: {
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center'
  },
  freePlanButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB'
  },
  freePlanButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151'
  },
  trialButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#3B82F6'
  },
  trialButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3B82F6'
  },
  subscribePlanButton: {
    backgroundColor: '#3B82F6'
  },
  subscribePlanButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  currentPlanButton: {
    backgroundColor: '#10B981',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center'
  },
  currentPlanButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  footer: {
    padding: 24,
    alignItems: 'center'
  },
  footerText: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 8
  },
  footerSubtext: {
    fontSize: 12,
    color: '#9CA3AF',
    textAlign: 'center'
  }
}));
export default PricingPlans;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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