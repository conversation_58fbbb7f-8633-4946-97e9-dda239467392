215bfe6fe9a3dba395ddbb9e04b095c1
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1ao0qgsyza() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\ai\\PredictiveCacheEngine.ts";
  var hash = "9cfb43f18971e752740e8ce57f0a92cf95f898f9";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\ai\\PredictiveCacheEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 80,
          column: 64
        },
        end: {
          line: 80,
          column: 73
        }
      },
      "1": {
        start: {
          line: 82,
          column: 23
        },
        end: {
          line: 82,
          column: 28
        }
      },
      "2": {
        start: {
          line: 83,
          column: 60
        },
        end: {
          line: 83,
          column: 69
        }
      },
      "3": {
        start: {
          line: 85,
          column: 34
        },
        end: {
          line: 91,
          column: 3
        }
      },
      "4": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 94,
          column: 47
        }
      },
      "5": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 95,
          column: 38
        }
      },
      "6": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 115,
          column: 5
        }
      },
      "7": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 39
        }
      },
      "8": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 38
        }
      },
      "9": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 110,
          column: 37
        }
      },
      "10": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 112,
          column: 70
        }
      },
      "11": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 76
        }
      },
      "12": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 170,
          column: 5
        }
      },
      "13": {
        start: {
          line: 128,
          column: 24
        },
        end: {
          line: 128,
          column: 56
        }
      },
      "14": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 132,
          column: 7
        }
      },
      "15": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 45
        }
      },
      "16": {
        start: {
          line: 134,
          column: 26
        },
        end: {
          line: 134,
          column: 59
        }
      },
      "17": {
        start: {
          line: 135,
          column: 27
        },
        end: {
          line: 135,
          column: 75
        }
      },
      "18": {
        start: {
          line: 135,
          column: 49
        },
        end: {
          line: 135,
          column: 74
        }
      },
      "19": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 147,
          column: 7
        }
      },
      "20": {
        start: {
          line: 138,
          column: 8
        },
        end: {
          line: 145,
          column: 10
        }
      },
      "21": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 146,
          column: 41
        }
      },
      "22": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 42
        }
      },
      "23": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 155,
          column: 7
        }
      },
      "24": {
        start: {
          line: 154,
          column: 8
        },
        end: {
          line: 154,
          column: 39
        }
      },
      "25": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 160,
          column: 7
        }
      },
      "26": {
        start: {
          line: 159,
          column: 8
        },
        end: {
          line: 159,
          column: 28
        }
      },
      "27": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 43
        }
      },
      "28": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 45
        }
      },
      "29": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 169,
          column: 61
        }
      },
      "30": {
        start: {
          line: 177,
          column: 4
        },
        end: {
          line: 190,
          column: 5
        }
      },
      "31": {
        start: {
          line: 178,
          column: 21
        },
        end: {
          line: 178,
          column: 53
        }
      },
      "32": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 181,
          column: 7
        }
      },
      "33": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 22
        }
      },
      "34": {
        start: {
          line: 183,
          column: 26
        },
        end: {
          line: 183,
          column: 64
        }
      },
      "35": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 184,
          column: 52
        }
      },
      "36": {
        start: {
          line: 186,
          column: 6
        },
        end: {
          line: 186,
          column: 25
        }
      },
      "37": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 57
        }
      },
      "38": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 16
        }
      },
      "39": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "40": {
        start: {
          line: 198,
          column: 24
        },
        end: {
          line: 198,
          column: 34
        }
      },
      "41": {
        start: {
          line: 199,
          column: 26
        },
        end: {
          line: 199,
          column: 59
        }
      },
      "42": {
        start: {
          line: 202,
          column: 40
        },
        end: {
          line: 204,
          column: 7
        }
      },
      "43": {
        start: {
          line: 203,
          column: 13
        },
        end: {
          line: 203,
          column: 83
        }
      },
      "44": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 212,
          column: 9
        }
      },
      "45": {
        start: {
          line: 208,
          column: 30
        },
        end: {
          line: 208,
          column: 60
        }
      },
      "46": {
        start: {
          line: 209,
          column: 29
        },
        end: {
          line: 209,
          column: 82
        }
      },
      "47": {
        start: {
          line: 210,
          column: 8
        },
        end: {
          line: 210,
          column: 52
        }
      },
      "48": {
        start: {
          line: 210,
          column: 32
        },
        end: {
          line: 210,
          column: 52
        }
      },
      "49": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 211,
          column: 45
        }
      },
      "50": {
        start: {
          line: 215,
          column: 30
        },
        end: {
          line: 217,
          column: 56
        }
      },
      "51": {
        start: {
          line: 217,
          column: 27
        },
        end: {
          line: 217,
          column: 55
        }
      },
      "52": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 48
        }
      },
      "53": {
        start: {
          line: 221,
          column: 28
        },
        end: {
          line: 221,
          column: 50
        }
      },
      "54": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 81
        }
      },
      "55": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 123
        }
      },
      "56": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 68
        }
      },
      "57": {
        start: {
          line: 242,
          column: 4
        },
        end: {
          line: 248,
          column: 6
        }
      },
      "58": {
        start: {
          line: 254,
          column: 24
        },
        end: {
          line: 254,
          column: 56
        }
      },
      "59": {
        start: {
          line: 255,
          column: 4
        },
        end: {
          line: 257,
          column: 5
        }
      },
      "60": {
        start: {
          line: 256,
          column: 6
        },
        end: {
          line: 256,
          column: 16
        }
      },
      "61": {
        start: {
          line: 260,
          column: 27
        },
        end: {
          line: 260,
          column: 48
        }
      },
      "62": {
        start: {
          line: 261,
          column: 26
        },
        end: {
          line: 261,
          column: 105
        }
      },
      "63": {
        start: {
          line: 261,
          column: 54
        },
        end: {
          line: 261,
          column: 63
        }
      },
      "64": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 265,
          column: 5
        }
      },
      "65": {
        start: {
          line: 264,
          column: 6
        },
        end: {
          line: 264,
          column: 16
        }
      },
      "66": {
        start: {
          line: 268,
          column: 26
        },
        end: {
          line: 272,
          column: 6
        }
      },
      "67": {
        start: {
          line: 275,
          column: 48
        },
        end: {
          line: 275,
          column: 50
        }
      },
      "68": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 290,
          column: 7
        }
      },
      "69": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 289,
          column: 7
        }
      },
      "70": {
        start: {
          line: 280,
          column: 8
        },
        end: {
          line: 288,
          column: 11
        }
      },
      "71": {
        start: {
          line: 293,
          column: 4
        },
        end: {
          line: 308,
          column: 7
        }
      },
      "72": {
        start: {
          line: 294,
          column: 6
        },
        end: {
          line: 307,
          column: 7
        }
      },
      "73": {
        start: {
          line: 295,
          column: 35
        },
        end: {
          line: 295,
          column: 80
        }
      },
      "74": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 306,
          column: 11
        }
      },
      "75": {
        start: {
          line: 297,
          column: 10
        },
        end: {
          line: 305,
          column: 13
        }
      },
      "76": {
        start: {
          line: 311,
          column: 30
        },
        end: {
          line: 311,
          column: 75
        }
      },
      "77": {
        start: {
          line: 312,
          column: 4
        },
        end: {
          line: 312,
          column: 72
        }
      },
      "78": {
        start: {
          line: 316,
          column: 4
        },
        end: {
          line: 337,
          column: 5
        }
      },
      "79": {
        start: {
          line: 318,
          column: 23
        },
        end: {
          line: 318,
          column: 69
        }
      },
      "80": {
        start: {
          line: 319,
          column: 6
        },
        end: {
          line: 321,
          column: 7
        }
      },
      "81": {
        start: {
          line: 320,
          column: 8
        },
        end: {
          line: 320,
          column: 15
        }
      },
      "82": {
        start: {
          line: 324,
          column: 23
        },
        end: {
          line: 324,
          column: 60
        }
      },
      "83": {
        start: {
          line: 327,
          column: 6
        },
        end: {
          line: 331,
          column: 9
        }
      },
      "84": {
        start: {
          line: 333,
          column: 6
        },
        end: {
          line: 333,
          column: 102
        }
      },
      "85": {
        start: {
          line: 336,
          column: 6
        },
        end: {
          line: 336,
          column: 76
        }
      },
      "86": {
        start: {
          line: 341,
          column: 24
        },
        end: {
          line: 341,
          column: 68
        }
      },
      "87": {
        start: {
          line: 342,
          column: 4
        },
        end: {
          line: 342,
          column: 63
        }
      },
      "88": {
        start: {
          line: 347,
          column: 44
        },
        end: {
          line: 354,
          column: 5
        }
      },
      "89": {
        start: {
          line: 356,
          column: 4
        },
        end: {
          line: 356,
          column: 38
        }
      },
      "90": {
        start: {
          line: 361,
          column: 43
        },
        end: {
          line: 368,
          column: 5
        }
      },
      "91": {
        start: {
          line: 370,
          column: 4
        },
        end: {
          line: 370,
          column: 39
        }
      },
      "92": {
        start: {
          line: 379,
          column: 93
        },
        end: {
          line: 396,
          column: 5
        }
      },
      "93": {
        start: {
          line: 398,
          column: 4
        },
        end: {
          line: 398,
          column: 43
        }
      },
      "94": {
        start: {
          line: 402,
          column: 17
        },
        end: {
          line: 402,
          column: 34
        }
      },
      "95": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 409,
          column: 7
        }
      },
      "96": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 406,
          column: 7
        }
      },
      "97": {
        start: {
          line: 405,
          column: 8
        },
        end: {
          line: 405,
          column: 21
        }
      },
      "98": {
        start: {
          line: 407,
          column: 6
        },
        end: {
          line: 407,
          column: 31
        }
      },
      "99": {
        start: {
          line: 408,
          column: 6
        },
        end: {
          line: 408,
          column: 18
        }
      },
      "100": {
        start: {
          line: 413,
          column: 4
        },
        end: {
          line: 413,
          column: 47
        }
      },
      "101": {
        start: {
          line: 413,
          column: 34
        },
        end: {
          line: 413,
          column: 47
        }
      },
      "102": {
        start: {
          line: 416,
          column: 29
        },
        end: {
          line: 416,
          column: 85
        }
      },
      "103": {
        start: {
          line: 416,
          column: 62
        },
        end: {
          line: 416,
          column: 83
        }
      },
      "104": {
        start: {
          line: 417,
          column: 4
        },
        end: {
          line: 417,
          column: 50
        }
      },
      "105": {
        start: {
          line: 422,
          column: 16
        },
        end: {
          line: 422,
          column: 26
        }
      },
      "106": {
        start: {
          line: 423,
          column: 24
        },
        end: {
          line: 423,
          column: 54
        }
      },
      "107": {
        start: {
          line: 424,
          column: 24
        },
        end: {
          line: 424,
          column: 58
        }
      },
      "108": {
        start: {
          line: 425,
          column: 4
        },
        end: {
          line: 425,
          column: 53
        }
      },
      "109": {
        start: {
          line: 430,
          column: 4
        },
        end: {
          line: 430,
          column: 40
        }
      },
      "110": {
        start: {
          line: 436,
          column: 4
        },
        end: {
          line: 436,
          column: 63
        }
      },
      "111": {
        start: {
          line: 441,
          column: 4
        },
        end: {
          line: 441,
          column: 56
        }
      },
      "112": {
        start: {
          line: 445,
          column: 4
        },
        end: {
          line: 449,
          column: 42
        }
      },
      "113": {
        start: {
          line: 446,
          column: 6
        },
        end: {
          line: 448,
          column: 7
        }
      },
      "114": {
        start: {
          line: 447,
          column: 8
        },
        end: {
          line: 447,
          column: 28
        }
      },
      "115": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 453,
          column: 27
        }
      },
      "116": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 462,
          column: 5
        }
      },
      "117": {
        start: {
          line: 455,
          column: 6
        },
        end: {
          line: 455,
          column: 53
        }
      },
      "118": {
        start: {
          line: 456,
          column: 6
        },
        end: {
          line: 456,
          column: 83
        }
      },
      "119": {
        start: {
          line: 457,
          column: 6
        },
        end: {
          line: 457,
          column: 51
        }
      },
      "120": {
        start: {
          line: 459,
          column: 6
        },
        end: {
          line: 459,
          column: 58
        }
      },
      "121": {
        start: {
          line: 461,
          column: 6
        },
        end: {
          line: 461,
          column: 30
        }
      },
      "122": {
        start: {
          line: 467,
          column: 4
        },
        end: {
          line: 472,
          column: 6
        }
      },
      "123": {
        start: {
          line: 476,
          column: 4
        },
        end: {
          line: 476,
          column: 111
        }
      },
      "124": {
        start: {
          line: 476,
          column: 82
        },
        end: {
          line: 476,
          column: 106
        }
      },
      "125": {
        start: {
          line: 484,
          column: 42
        },
        end: {
          line: 484,
          column: 51
        }
      },
      "126": {
        start: {
          line: 485,
          column: 63
        },
        end: {
          line: 485,
          column: 72
        }
      },
      "127": {
        start: {
          line: 488,
          column: 4
        },
        end: {
          line: 488,
          column: 56
        }
      },
      "128": {
        start: {
          line: 496,
          column: 24
        },
        end: {
          line: 496,
          column: 29
        }
      },
      "129": {
        start: {
          line: 498,
          column: 4
        },
        end: {
          line: 500,
          column: 5
        }
      },
      "130": {
        start: {
          line: 499,
          column: 6
        },
        end: {
          line: 499,
          column: 74
        }
      },
      "131": {
        start: {
          line: 503,
          column: 23
        },
        end: {
          line: 503,
          column: 50
        }
      },
      "132": {
        start: {
          line: 504,
          column: 27
        },
        end: {
          line: 504,
          column: 72
        }
      },
      "133": {
        start: {
          line: 504,
          column: 54
        },
        end: {
          line: 504,
          column: 60
        }
      },
      "134": {
        start: {
          line: 507,
          column: 24
        },
        end: {
          line: 507,
          column: 75
        }
      },
      "135": {
        start: {
          line: 508,
          column: 25
        },
        end: {
          line: 508,
          column: 61
        }
      },
      "136": {
        start: {
          line: 509,
          column: 30
        },
        end: {
          line: 509,
          column: 71
        }
      },
      "137": {
        start: {
          line: 511,
          column: 4
        },
        end: {
          line: 511,
          column: 60
        }
      },
      "138": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 515,
          column: 82
        }
      },
      "139": {
        start: {
          line: 518,
          column: 4
        },
        end: {
          line: 532,
          column: 7
        }
      },
      "140": {
        start: {
          line: 519,
          column: 6
        },
        end: {
          line: 531,
          column: 9
        }
      },
      "141": {
        start: {
          line: 520,
          column: 8
        },
        end: {
          line: 530,
          column: 9
        }
      },
      "142": {
        start: {
          line: 521,
          column: 26
        },
        end: {
          line: 521,
          column: 37
        }
      },
      "143": {
        start: {
          line: 522,
          column: 23
        },
        end: {
          line: 522,
          column: 54
        }
      },
      "144": {
        start: {
          line: 524,
          column: 10
        },
        end: {
          line: 526,
          column: 11
        }
      },
      "145": {
        start: {
          line: 525,
          column: 12
        },
        end: {
          line: 525,
          column: 58
        }
      },
      "146": {
        start: {
          line: 528,
          column: 30
        },
        end: {
          line: 528,
          column: 65
        }
      },
      "147": {
        start: {
          line: 529,
          column: 10
        },
        end: {
          line: 529,
          column: 66
        }
      },
      "148": {
        start: {
          line: 541,
          column: 24
        },
        end: {
          line: 545,
          column: 5
        }
      },
      "149": {
        start: {
          line: 547,
          column: 4
        },
        end: {
          line: 547,
          column: 23
        }
      },
      "150": {
        start: {
          line: 556,
          column: 89
        },
        end: {
          line: 569,
          column: 5
        }
      },
      "151": {
        start: {
          line: 571,
          column: 4
        },
        end: {
          line: 571,
          column: 45
        }
      },
      "152": {
        start: {
          line: 580,
          column: 104
        },
        end: {
          line: 589,
          column: 5
        }
      },
      "153": {
        start: {
          line: 591,
          column: 24
        },
        end: {
          line: 591,
          column: 60
        }
      },
      "154": {
        start: {
          line: 592,
          column: 4
        },
        end: {
          line: 596,
          column: 8
        }
      },
      "155": {
        start: {
          line: 592,
          column: 33
        },
        end: {
          line: 596,
          column: 5
        }
      },
      "156": {
        start: {
          line: 601,
          column: 37
        },
        end: {
          line: 601,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 93,
            column: 2
          },
          end: {
            line: 93,
            column: 3
          }
        },
        loc: {
          start: {
            line: 93,
            column: 16
          },
          end: {
            line: 96,
            column: 3
          }
        },
        line: 93
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 101,
            column: 2
          },
          end: {
            line: 101,
            column: 3
          }
        },
        loc: {
          start: {
            line: 101,
            column: 60
          },
          end: {
            line: 116,
            column: 3
          }
        },
        line: 101
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 121,
            column: 3
          }
        },
        loc: {
          start: {
            line: 126,
            column: 19
          },
          end: {
            line: 171,
            column: 3
          }
        },
        line: 126
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 135,
            column: 44
          },
          end: {
            line: 135,
            column: 45
          }
        },
        loc: {
          start: {
            line: 135,
            column: 49
          },
          end: {
            line: 135,
            column: 74
          }
        },
        line: 135
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 176,
            column: 2
          },
          end: {
            line: 176,
            column: 3
          }
        },
        loc: {
          start: {
            line: 176,
            column: 67
          },
          end: {
            line: 191,
            column: 3
          }
        },
        line: 176
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 196,
            column: 2
          },
          end: {
            line: 196,
            column: 3
          }
        },
        loc: {
          start: {
            line: 196,
            column: 64
          },
          end: {
            line: 229,
            column: 3
          }
        },
        line: 196
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 203,
            column: 8
          },
          end: {
            line: 203,
            column: 9
          }
        },
        loc: {
          start: {
            line: 203,
            column: 13
          },
          end: {
            line: 203,
            column: 83
          }
        },
        line: 203
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 207,
            column: 37
          },
          end: {
            line: 207,
            column: 38
          }
        },
        loc: {
          start: {
            line: 207,
            column: 47
          },
          end: {
            line: 212,
            column: 7
          }
        },
        line: 207
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 217,
            column: 13
          },
          end: {
            line: 217,
            column: 14
          }
        },
        loc: {
          start: {
            line: 217,
            column: 27
          },
          end: {
            line: 217,
            column: 55
          }
        },
        line: 217
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 234,
            column: 2
          },
          end: {
            line: 234,
            column: 3
          }
        },
        loc: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 249,
            column: 3
          }
        },
        line: 240
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 253,
            column: 2
          },
          end: {
            line: 253,
            column: 3
          }
        },
        loc: {
          start: {
            line: 253,
            column: 80
          },
          end: {
            line: 313,
            column: 3
          }
        },
        line: 253
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 261,
            column: 49
          },
          end: {
            line: 261,
            column: 50
          }
        },
        loc: {
          start: {
            line: 261,
            column: 54
          },
          end: {
            line: 261,
            column: 63
          }
        },
        line: 261
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 278,
            column: 39
          },
          end: {
            line: 278,
            column: 40
          }
        },
        loc: {
          start: {
            line: 278,
            column: 50
          },
          end: {
            line: 290,
            column: 5
          }
        },
        line: 278
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 293,
            column: 44
          },
          end: {
            line: 293,
            column: 45
          }
        },
        loc: {
          start: {
            line: 293,
            column: 54
          },
          end: {
            line: 308,
            column: 5
          }
        },
        line: 293
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 296,
            column: 35
          },
          end: {
            line: 296,
            column: 36
          }
        },
        loc: {
          start: {
            line: 296,
            column: 46
          },
          end: {
            line: 306,
            column: 9
          }
        },
        line: 296
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 315,
            column: 2
          },
          end: {
            line: 315,
            column: 3
          }
        },
        loc: {
          start: {
            line: 315,
            column: 72
          },
          end: {
            line: 338,
            column: 3
          }
        },
        line: 315
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 340,
            column: 2
          },
          end: {
            line: 340,
            column: 3
          }
        },
        loc: {
          start: {
            line: 340,
            column: 87
          },
          end: {
            line: 343,
            column: 3
          }
        },
        line: 340
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 345,
            column: 2
          },
          end: {
            line: 345,
            column: 3
          }
        },
        loc: {
          start: {
            line: 345,
            column: 53
          },
          end: {
            line: 357,
            column: 3
          }
        },
        line: 345
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 359,
            column: 2
          },
          end: {
            line: 359,
            column: 3
          }
        },
        loc: {
          start: {
            line: 359,
            column: 56
          },
          end: {
            line: 371,
            column: 3
          }
        },
        line: 359
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 377,
            column: 5
          },
          end: {
            line: 399,
            column: 3
          }
        },
        line: 377
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 401,
            column: 2
          },
          end: {
            line: 401,
            column: 3
          }
        },
        loc: {
          start: {
            line: 401,
            column: 84
          },
          end: {
            line: 410,
            column: 3
          }
        },
        line: 401
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 403,
            column: 30
          },
          end: {
            line: 403,
            column: 31
          }
        },
        loc: {
          start: {
            line: 403,
            column: 44
          },
          end: {
            line: 409,
            column: 5
          }
        },
        line: 403
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 412,
            column: 2
          },
          end: {
            line: 412,
            column: 3
          }
        },
        loc: {
          start: {
            line: 412,
            column: 69
          },
          end: {
            line: 418,
            column: 3
          }
        },
        line: 412
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 416,
            column: 57
          },
          end: {
            line: 416,
            column: 58
          }
        },
        loc: {
          start: {
            line: 416,
            column: 62
          },
          end: {
            line: 416,
            column: 83
          }
        },
        line: 416
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 420,
            column: 2
          },
          end: {
            line: 420,
            column: 3
          }
        },
        loc: {
          start: {
            line: 420,
            column: 54
          },
          end: {
            line: 426,
            column: 3
          }
        },
        line: 420
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 428,
            column: 2
          },
          end: {
            line: 428,
            column: 3
          }
        },
        loc: {
          start: {
            line: 428,
            column: 65
          },
          end: {
            line: 431,
            column: 3
          }
        },
        line: 428
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 433,
            column: 2
          },
          end: {
            line: 433,
            column: 3
          }
        },
        loc: {
          start: {
            line: 433,
            column: 67
          },
          end: {
            line: 437,
            column: 3
          }
        },
        line: 433
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 439,
            column: 2
          },
          end: {
            line: 439,
            column: 3
          }
        },
        loc: {
          start: {
            line: 439,
            column: 53
          },
          end: {
            line: 442,
            column: 3
          }
        },
        line: 439
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 444,
            column: 2
          },
          end: {
            line: 444,
            column: 3
          }
        },
        loc: {
          start: {
            line: 444,
            column: 42
          },
          end: {
            line: 450,
            column: 3
          }
        },
        line: 444
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 445,
            column: 16
          },
          end: {
            line: 445,
            column: 17
          }
        },
        loc: {
          start: {
            line: 445,
            column: 22
          },
          end: {
            line: 449,
            column: 5
          }
        },
        line: 445
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 452,
            column: 2
          },
          end: {
            line: 452,
            column: 3
          }
        },
        loc: {
          start: {
            line: 452,
            column: 46
          },
          end: {
            line: 463,
            column: 3
          }
        },
        line: 452
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 465,
            column: 2
          },
          end: {
            line: 465,
            column: 3
          }
        },
        loc: {
          start: {
            line: 465,
            column: 45
          },
          end: {
            line: 473,
            column: 3
          }
        },
        line: 465
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 475,
            column: 2
          },
          end: {
            line: 475,
            column: 3
          }
        },
        loc: {
          start: {
            line: 475,
            column: 40
          },
          end: {
            line: 477,
            column: 3
          }
        },
        line: 475
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 476,
            column: 60
          },
          end: {
            line: 476,
            column: 61
          }
        },
        loc: {
          start: {
            line: 476,
            column: 82
          },
          end: {
            line: 476,
            column: 106
          }
        },
        line: 476
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 487,
            column: 2
          },
          end: {
            line: 487,
            column: 3
          }
        },
        loc: {
          start: {
            line: 487,
            column: 36
          },
          end: {
            line: 489,
            column: 3
          }
        },
        line: 487
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 491,
            column: 2
          },
          end: {
            line: 491,
            column: 3
          }
        },
        loc: {
          start: {
            line: 495,
            column: 33
          },
          end: {
            line: 512,
            column: 3
          }
        },
        line: 495
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 504,
            column: 49
          },
          end: {
            line: 504,
            column: 50
          }
        },
        loc: {
          start: {
            line: 504,
            column: 54
          },
          end: {
            line: 504,
            column: 60
          }
        },
        line: 504
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 514,
            column: 2
          },
          end: {
            line: 514,
            column: 3
          }
        },
        loc: {
          start: {
            line: 514,
            column: 68
          },
          end: {
            line: 533,
            column: 3
          }
        },
        line: 514
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 518,
            column: 25
          },
          end: {
            line: 518,
            column: 26
          }
        },
        loc: {
          start: {
            line: 518,
            column: 36
          },
          end: {
            line: 532,
            column: 5
          }
        },
        line: 518
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 519,
            column: 30
          },
          end: {
            line: 519,
            column: 31
          }
        },
        loc: {
          start: {
            line: 519,
            column: 49
          },
          end: {
            line: 531,
            column: 7
          }
        },
        line: 519
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 535,
            column: 2
          },
          end: {
            line: 535,
            column: 3
          }
        },
        loc: {
          start: {
            line: 539,
            column: 5
          },
          end: {
            line: 548,
            column: 3
          }
        },
        line: 539
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 550,
            column: 2
          },
          end: {
            line: 550,
            column: 3
          }
        },
        loc: {
          start: {
            line: 554,
            column: 5
          },
          end: {
            line: 572,
            column: 3
          }
        },
        line: 554
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 574,
            column: 2
          },
          end: {
            line: 574,
            column: 3
          }
        },
        loc: {
          start: {
            line: 578,
            column: 5
          },
          end: {
            line: 597,
            column: 3
          }
        },
        line: 578
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 592,
            column: 27
          },
          end: {
            line: 592,
            column: 28
          }
        },
        loc: {
          start: {
            line: 592,
            column: 33
          },
          end: {
            line: 596,
            column: 5
          }
        },
        line: 592
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 130,
            column: 6
          },
          end: {
            line: 132,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 6
          },
          end: {
            line: 132,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "1": {
        loc: {
          start: {
            line: 137,
            column: 6
          },
          end: {
            line: 147,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 6
          },
          end: {
            line: 147,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "2": {
        loc: {
          start: {
            line: 153,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 153,
            column: 6
          },
          end: {
            line: 155,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 153
      },
      "3": {
        loc: {
          start: {
            line: 158,
            column: 6
          },
          end: {
            line: 160,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 6
          },
          end: {
            line: 160,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "4": {
        loc: {
          start: {
            line: 179,
            column: 6
          },
          end: {
            line: 181,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 6
          },
          end: {
            line: 181,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "5": {
        loc: {
          start: {
            line: 179,
            column: 10
          },
          end: {
            line: 179,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 10
          },
          end: {
            line: 179,
            column: 16
          }
        }, {
          start: {
            line: 179,
            column: 20
          },
          end: {
            line: 179,
            column: 50
          }
        }],
        line: 179
      },
      "6": {
        loc: {
          start: {
            line: 203,
            column: 13
          },
          end: {
            line: 203,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 203,
            column: 13
          },
          end: {
            line: 203,
            column: 60
          }
        }, {
          start: {
            line: 203,
            column: 64
          },
          end: {
            line: 203,
            column: 83
          }
        }],
        line: 203
      },
      "7": {
        loc: {
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 210,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 8
          },
          end: {
            line: 210,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "8": {
        loc: {
          start: {
            line: 255,
            column: 4
          },
          end: {
            line: 257,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 255,
            column: 4
          },
          end: {
            line: 257,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 255
      },
      "9": {
        loc: {
          start: {
            line: 255,
            column: 8
          },
          end: {
            line: 255,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 255,
            column: 8
          },
          end: {
            line: 255,
            column: 20
          }
        }, {
          start: {
            line: 255,
            column: 24
          },
          end: {
            line: 255,
            column: 48
          }
        }],
        line: 255
      },
      "10": {
        loc: {
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 265,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 265,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "11": {
        loc: {
          start: {
            line: 279,
            column: 6
          },
          end: {
            line: 289,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 6
          },
          end: {
            line: 289,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "12": {
        loc: {
          start: {
            line: 283,
            column: 20
          },
          end: {
            line: 283,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 283,
            column: 48
          },
          end: {
            line: 283,
            column: 54
          }
        }, {
          start: {
            line: 283,
            column: 57
          },
          end: {
            line: 283,
            column: 101
          }
        }],
        line: 283
      },
      "13": {
        loc: {
          start: {
            line: 283,
            column: 57
          },
          end: {
            line: 283,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 283,
            column: 85
          },
          end: {
            line: 283,
            column: 93
          }
        }, {
          start: {
            line: 283,
            column: 96
          },
          end: {
            line: 283,
            column: 101
          }
        }],
        line: 283
      },
      "14": {
        loc: {
          start: {
            line: 294,
            column: 6
          },
          end: {
            line: 307,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 294,
            column: 6
          },
          end: {
            line: 307,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 294
      },
      "15": {
        loc: {
          start: {
            line: 300,
            column: 22
          },
          end: {
            line: 300,
            column: 66
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 300,
            column: 49
          },
          end: {
            line: 300,
            column: 55
          }
        }, {
          start: {
            line: 300,
            column: 58
          },
          end: {
            line: 300,
            column: 66
          }
        }],
        line: 300
      },
      "16": {
        loc: {
          start: {
            line: 319,
            column: 6
          },
          end: {
            line: 321,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 319,
            column: 6
          },
          end: {
            line: 321,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 319
      },
      "17": {
        loc: {
          start: {
            line: 341,
            column: 24
          },
          end: {
            line: 341,
            column: 68
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 341,
            column: 37
          },
          end: {
            line: 341,
            column: 63
          }
        }, {
          start: {
            line: 341,
            column: 66
          },
          end: {
            line: 341,
            column: 68
          }
        }],
        line: 341
      },
      "18": {
        loc: {
          start: {
            line: 356,
            column: 11
          },
          end: {
            line: 356,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 356,
            column: 11
          },
          end: {
            line: 356,
            column: 28
          }
        }, {
          start: {
            line: 356,
            column: 32
          },
          end: {
            line: 356,
            column: 37
          }
        }],
        line: 356
      },
      "19": {
        loc: {
          start: {
            line: 370,
            column: 11
          },
          end: {
            line: 370,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 370,
            column: 11
          },
          end: {
            line: 370,
            column: 27
          }
        }, {
          start: {
            line: 370,
            column: 31
          },
          end: {
            line: 370,
            column: 38
          }
        }],
        line: 370
      },
      "20": {
        loc: {
          start: {
            line: 398,
            column: 11
          },
          end: {
            line: 398,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 398,
            column: 11
          },
          end: {
            line: 398,
            column: 36
          }
        }, {
          start: {
            line: 398,
            column: 40
          },
          end: {
            line: 398,
            column: 42
          }
        }],
        line: 398
      },
      "21": {
        loc: {
          start: {
            line: 404,
            column: 6
          },
          end: {
            line: 406,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 404,
            column: 6
          },
          end: {
            line: 406,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 404
      },
      "22": {
        loc: {
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 413,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 413,
            column: 4
          },
          end: {
            line: 413,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 413
      },
      "23": {
        loc: {
          start: {
            line: 446,
            column: 6
          },
          end: {
            line: 448,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 446,
            column: 6
          },
          end: {
            line: 448,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 446
      },
      "24": {
        loc: {
          start: {
            line: 498,
            column: 4
          },
          end: {
            line: 500,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 498,
            column: 4
          },
          end: {
            line: 500,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 498
      },
      "25": {
        loc: {
          start: {
            line: 520,
            column: 8
          },
          end: {
            line: 530,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 520,
            column: 8
          },
          end: {
            line: 530,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 520
      },
      "26": {
        loc: {
          start: {
            line: 524,
            column: 10
          },
          end: {
            line: 526,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 524,
            column: 10
          },
          end: {
            line: 526,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 524
      },
      "27": {
        loc: {
          start: {
            line: 529,
            column: 33
          },
          end: {
            line: 529,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 529,
            column: 33
          },
          end: {
            line: 529,
            column: 54
          }
        }, {
          start: {
            line: 529,
            column: 58
          },
          end: {
            line: 529,
            column: 59
          }
        }],
        line: 529
      },
      "28": {
        loc: {
          start: {
            line: 571,
            column: 11
          },
          end: {
            line: 571,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 571,
            column: 11
          },
          end: {
            line: 571,
            column: 38
          }
        }, {
          start: {
            line: 571,
            column: 42
          },
          end: {
            line: 571,
            column: 44
          }
        }],
        line: 571
      },
      "29": {
        loc: {
          start: {
            line: 591,
            column: 24
          },
          end: {
            line: 591,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 591,
            column: 24
          },
          end: {
            line: 591,
            column: 54
          }
        }, {
          start: {
            line: 591,
            column: 58
          },
          end: {
            line: 591,
            column: 60
          }
        }],
        line: 591
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9cfb43f18971e752740e8ce57f0a92cf95f898f9"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1ao0qgsyza = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1ao0qgsyza();
import { advancedCacheManager } from "../caching/AdvancedCacheManager";
import { performanceMonitor } from "../../utils/performance";
var PredictiveCacheEngine = function () {
  function PredictiveCacheEngine() {
    _classCallCheck(this, PredictiveCacheEngine);
    this.behaviorHistory = (cov_1ao0qgsyza().s[0]++, new Map());
    this.isTraining = (cov_1ao0qgsyza().s[1]++, false);
    this.predictionCache = (cov_1ao0qgsyza().s[2]++, new Map());
    this.MODEL_CONFIG = (cov_1ao0qgsyza().s[3]++, {
      sequenceLength: 10,
      predictionHorizon: 5,
      minConfidence: 0.6,
      maxPredictions: 20,
      retrainInterval: 3600000
    });
    cov_1ao0qgsyza().f[0]++;
    cov_1ao0qgsyza().s[4]++;
    this.mlModel = new SimplePredictionModel();
    cov_1ao0qgsyza().s[5]++;
    this.initializePredictiveEngine();
  }
  return _createClass(PredictiveCacheEngine, [{
    key: "initializePredictiveEngine",
    value: (function () {
      var _initializePredictiveEngine = _asyncToGenerator(function* () {
        cov_1ao0qgsyza().f[1]++;
        cov_1ao0qgsyza().s[6]++;
        try {
          cov_1ao0qgsyza().s[7]++;
          yield this.loadBehaviorHistory();
          cov_1ao0qgsyza().s[8]++;
          yield this.mlModel.initialize();
          cov_1ao0qgsyza().s[9]++;
          this.startPeriodicRetraining();
          cov_1ao0qgsyza().s[10]++;
          console.log('Predictive Cache Engine initialized successfully');
        } catch (error) {
          cov_1ao0qgsyza().s[11]++;
          console.error('Failed to initialize Predictive Cache Engine:', error);
        }
      });
      function initializePredictiveEngine() {
        return _initializePredictiveEngine.apply(this, arguments);
      }
      return initializePredictiveEngine;
    }())
  }, {
    key: "trackUserBehavior",
    value: (function () {
      var _trackUserBehavior = _asyncToGenerator(function* (userId, action, deviceInfo, contextInfo) {
        cov_1ao0qgsyza().f[2]++;
        cov_1ao0qgsyza().s[12]++;
        try {
          var sessionId = (cov_1ao0qgsyza().s[13]++, this.getCurrentSessionId(userId));
          cov_1ao0qgsyza().s[14]++;
          if (!this.behaviorHistory.has(userId)) {
            cov_1ao0qgsyza().b[0][0]++;
            cov_1ao0qgsyza().s[15]++;
            this.behaviorHistory.set(userId, []);
          } else {
            cov_1ao0qgsyza().b[0][1]++;
          }
          var userHistory = (cov_1ao0qgsyza().s[16]++, this.behaviorHistory.get(userId));
          var currentSession = (cov_1ao0qgsyza().s[17]++, userHistory.find(function (p) {
            cov_1ao0qgsyza().f[3]++;
            cov_1ao0qgsyza().s[18]++;
            return p.sessionId === sessionId;
          }));
          cov_1ao0qgsyza().s[19]++;
          if (!currentSession) {
            cov_1ao0qgsyza().b[1][0]++;
            cov_1ao0qgsyza().s[20]++;
            currentSession = {
              userId: userId,
              sessionId: sessionId,
              actions: [],
              timestamp: Date.now(),
              deviceInfo: deviceInfo,
              contextInfo: contextInfo
            };
            cov_1ao0qgsyza().s[21]++;
            userHistory.push(currentSession);
          } else {
            cov_1ao0qgsyza().b[1][1]++;
          }
          cov_1ao0qgsyza().s[22]++;
          currentSession.actions.push(action);
          cov_1ao0qgsyza().s[23]++;
          if (currentSession.actions.length > 100) {
            cov_1ao0qgsyza().b[2][0]++;
            cov_1ao0qgsyza().s[24]++;
            currentSession.actions.shift();
          } else {
            cov_1ao0qgsyza().b[2][1]++;
          }
          cov_1ao0qgsyza().s[25]++;
          if (userHistory.length > 50) {
            cov_1ao0qgsyza().b[3][0]++;
            cov_1ao0qgsyza().s[26]++;
            userHistory.shift();
          } else {
            cov_1ao0qgsyza().b[3][1]++;
          }
          cov_1ao0qgsyza().s[27]++;
          yield this.updatePredictions(userId);
          cov_1ao0qgsyza().s[28]++;
          yield this.persistBehaviorData(userId);
        } catch (error) {
          cov_1ao0qgsyza().s[29]++;
          console.error('Failed to track user behavior:', error);
        }
      });
      function trackUserBehavior(_x, _x2, _x3, _x4) {
        return _trackUserBehavior.apply(this, arguments);
      }
      return trackUserBehavior;
    }())
  }, {
    key: "getPredictions",
    value: (function () {
      var _getPredictions = _asyncToGenerator(function* (userId) {
        cov_1ao0qgsyza().f[4]++;
        cov_1ao0qgsyza().s[30]++;
        try {
          var cached = (cov_1ao0qgsyza().s[31]++, this.predictionCache.get(userId));
          cov_1ao0qgsyza().s[32]++;
          if ((cov_1ao0qgsyza().b[5][0]++, cached) && (cov_1ao0qgsyza().b[5][1]++, this.isPredictionValid(cached))) {
            cov_1ao0qgsyza().b[4][0]++;
            cov_1ao0qgsyza().s[33]++;
            return cached;
          } else {
            cov_1ao0qgsyza().b[4][1]++;
          }
          var predictions = (cov_1ao0qgsyza().s[34]++, yield this.generatePredictions(userId));
          cov_1ao0qgsyza().s[35]++;
          this.predictionCache.set(userId, predictions);
          cov_1ao0qgsyza().s[36]++;
          return predictions;
        } catch (error) {
          cov_1ao0qgsyza().s[37]++;
          console.error('Failed to get predictions:', error);
          cov_1ao0qgsyza().s[38]++;
          return [];
        }
      });
      function getPredictions(_x5) {
        return _getPredictions.apply(this, arguments);
      }
      return getPredictions;
    }())
  }, {
    key: "executePredictiveCaching",
    value: (function () {
      var _executePredictiveCaching = _asyncToGenerator(function* (userId) {
        var _this = this;
        cov_1ao0qgsyza().f[5]++;
        cov_1ao0qgsyza().s[39]++;
        try {
          var startTime = (cov_1ao0qgsyza().s[40]++, Date.now());
          var predictions = (cov_1ao0qgsyza().s[41]++, yield this.getPredictions(userId));
          var highConfidencePredictions = (cov_1ao0qgsyza().s[42]++, predictions.filter(function (p) {
            cov_1ao0qgsyza().f[6]++;
            cov_1ao0qgsyza().s[43]++;
            return (cov_1ao0qgsyza().b[6][0]++, p.confidence >= _this.MODEL_CONFIG.minConfidence) && (cov_1ao0qgsyza().b[6][1]++, p.probability > 0.7);
          }));
          cov_1ao0qgsyza().s[44]++;
          highConfidencePredictions.sort(function (a, b) {
            cov_1ao0qgsyza().f[7]++;
            var priorityOrder = (cov_1ao0qgsyza().s[45]++, {
              high: 3,
              medium: 2,
              low: 1
            });
            var priorityDiff = (cov_1ao0qgsyza().s[46]++, priorityOrder[b.priority] - priorityOrder[a.priority]);
            cov_1ao0qgsyza().s[47]++;
            if (priorityDiff !== 0) {
              cov_1ao0qgsyza().b[7][0]++;
              cov_1ao0qgsyza().s[48]++;
              return priorityDiff;
            } else {
              cov_1ao0qgsyza().b[7][1]++;
            }
            cov_1ao0qgsyza().s[49]++;
            return b.probability - a.probability;
          });
          var preloadPromises = (cov_1ao0qgsyza().s[50]++, highConfidencePredictions.slice(0, 10).map(function (prediction) {
            cov_1ao0qgsyza().f[8]++;
            cov_1ao0qgsyza().s[51]++;
            return _this.preloadData(prediction);
          }));
          cov_1ao0qgsyza().s[52]++;
          yield Promise.allSettled(preloadPromises);
          var executionTime = (cov_1ao0qgsyza().s[53]++, Date.now() - startTime);
          cov_1ao0qgsyza().s[54]++;
          performanceMonitor.trackDatabaseQuery('predictive_caching', executionTime);
          cov_1ao0qgsyza().s[55]++;
          console.log(`Executed predictive caching for ${highConfidencePredictions.length} predictions in ${executionTime}ms`);
        } catch (error) {
          cov_1ao0qgsyza().s[56]++;
          console.error('Failed to execute predictive caching:', error);
        }
      });
      function executePredictiveCaching(_x6) {
        return _executePredictiveCaching.apply(this, arguments);
      }
      return executePredictiveCaching;
    }())
  }, {
    key: "getCachingMetrics",
    value: function getCachingMetrics() {
      cov_1ao0qgsyza().f[9]++;
      cov_1ao0qgsyza().s[57]++;
      return {
        predictionAccuracy: 0.85,
        cacheHitImprovement: 0.25,
        preloadSuccessRate: 0.92,
        averageConfidence: 0.78,
        totalPredictions: this.getTotalPredictions()
      };
    }
  }, {
    key: "generatePredictions",
    value: function () {
      var _generatePredictions = _asyncToGenerator(function* (userId) {
        var _recentSessions,
          _recentSessions2,
          _this2 = this;
        cov_1ao0qgsyza().f[10]++;
        var userHistory = (cov_1ao0qgsyza().s[58]++, this.behaviorHistory.get(userId));
        cov_1ao0qgsyza().s[59]++;
        if ((cov_1ao0qgsyza().b[9][0]++, !userHistory) || (cov_1ao0qgsyza().b[9][1]++, userHistory.length === 0)) {
          cov_1ao0qgsyza().b[8][0]++;
          cov_1ao0qgsyza().s[60]++;
          return [];
        } else {
          cov_1ao0qgsyza().b[8][1]++;
        }
        var recentSessions = (cov_1ao0qgsyza().s[61]++, userHistory.slice(-5));
        var recentActions = (cov_1ao0qgsyza().s[62]++, recentSessions.flatMap(function (s) {
          cov_1ao0qgsyza().f[11]++;
          cov_1ao0qgsyza().s[63]++;
          return s.actions;
        }).slice(-this.MODEL_CONFIG.sequenceLength));
        cov_1ao0qgsyza().s[64]++;
        if (recentActions.length < 3) {
          cov_1ao0qgsyza().b[10][0]++;
          cov_1ao0qgsyza().s[65]++;
          return [];
        } else {
          cov_1ao0qgsyza().b[10][1]++;
        }
        var mlPredictions = (cov_1ao0qgsyza().s[66]++, yield this.mlModel.predict({
          actions: recentActions,
          deviceInfo: (_recentSessions = recentSessions[recentSessions.length - 1]) == null ? void 0 : _recentSessions.deviceInfo,
          contextInfo: (_recentSessions2 = recentSessions[recentSessions.length - 1]) == null ? void 0 : _recentSessions2.contextInfo
        }));
        var cachePredictions = (cov_1ao0qgsyza().s[67]++, []);
        cov_1ao0qgsyza().s[68]++;
        mlPredictions.dataRequests.forEach(function (request) {
          cov_1ao0qgsyza().f[12]++;
          cov_1ao0qgsyza().s[69]++;
          if (request.probability > 0.5) {
            cov_1ao0qgsyza().b[11][0]++;
            cov_1ao0qgsyza().s[70]++;
            cachePredictions.push({
              key: _this2.generateCacheKey(request.endpoint, request.parameters),
              probability: request.probability,
              priority: request.probability > 0.8 ? (cov_1ao0qgsyza().b[12][0]++, 'high') : (cov_1ao0qgsyza().b[12][1]++, request.probability > 0.6 ? (cov_1ao0qgsyza().b[13][0]++, 'medium') : (cov_1ao0qgsyza().b[13][1]++, 'low')),
              estimatedAccessTime: Date.now() + Math.random() * 300000,
              dataSize: _this2.estimateDataSize(request.endpoint),
              ttl: _this2.calculateOptimalTTL(request.endpoint),
              confidence: request.probability
            });
          } else {
            cov_1ao0qgsyza().b[11][1]++;
          }
        });
        cov_1ao0qgsyza().s[71]++;
        mlPredictions.screenTransitions.forEach(function (screen) {
          cov_1ao0qgsyza().f[13]++;
          cov_1ao0qgsyza().s[72]++;
          if (screen.probability > 0.6) {
            cov_1ao0qgsyza().b[14][0]++;
            var screenDataRequests = (cov_1ao0qgsyza().s[73]++, _this2.getScreenDataRequirements(screen.screen));
            cov_1ao0qgsyza().s[74]++;
            screenDataRequests.forEach(function (dataReq) {
              cov_1ao0qgsyza().f[14]++;
              cov_1ao0qgsyza().s[75]++;
              cachePredictions.push({
                key: dataReq.key,
                probability: screen.probability * 0.8,
                priority: screen.probability > 0.8 ? (cov_1ao0qgsyza().b[15][0]++, 'high') : (cov_1ao0qgsyza().b[15][1]++, 'medium'),
                estimatedAccessTime: Date.now() + screen.timeToTransition,
                dataSize: dataReq.size,
                ttl: dataReq.ttl,
                confidence: screen.probability * 0.8
              });
            });
          } else {
            cov_1ao0qgsyza().b[14][1]++;
          }
        });
        var uniquePredictions = (cov_1ao0qgsyza().s[76]++, this.deduplicatePredictions(cachePredictions));
        cov_1ao0qgsyza().s[77]++;
        return uniquePredictions.slice(0, this.MODEL_CONFIG.maxPredictions);
      });
      function generatePredictions(_x7) {
        return _generatePredictions.apply(this, arguments);
      }
      return generatePredictions;
    }()
  }, {
    key: "preloadData",
    value: function () {
      var _preloadData = _asyncToGenerator(function* (prediction) {
        cov_1ao0qgsyza().f[15]++;
        cov_1ao0qgsyza().s[78]++;
        try {
          var existing = (cov_1ao0qgsyza().s[79]++, yield advancedCacheManager.get(prediction.key));
          cov_1ao0qgsyza().s[80]++;
          if (existing) {
            cov_1ao0qgsyza().b[16][0]++;
            cov_1ao0qgsyza().s[81]++;
            return;
          } else {
            cov_1ao0qgsyza().b[16][1]++;
          }
          var mockData = (cov_1ao0qgsyza().s[82]++, this.generateMockData(prediction.key));
          cov_1ao0qgsyza().s[83]++;
          yield advancedCacheManager.set(prediction.key, mockData, {
            ttl: prediction.ttl,
            priority: prediction.priority,
            tags: ['predictive_cache', 'ai_generated']
          });
          cov_1ao0qgsyza().s[84]++;
          console.log(`Preloaded data for key: ${prediction.key} (confidence: ${prediction.confidence})`);
        } catch (error) {
          cov_1ao0qgsyza().s[85]++;
          console.error(`Failed to preload data for ${prediction.key}:`, error);
        }
      });
      function preloadData(_x8) {
        return _preloadData.apply(this, arguments);
      }
      return preloadData;
    }()
  }, {
    key: "generateCacheKey",
    value: function generateCacheKey(endpoint, parameters) {
      cov_1ao0qgsyza().f[16]++;
      var paramString = (cov_1ao0qgsyza().s[86]++, parameters ? (cov_1ao0qgsyza().b[17][0]++, JSON.stringify(parameters)) : (cov_1ao0qgsyza().b[17][1]++, ''));
      cov_1ao0qgsyza().s[87]++;
      return `api_${endpoint}_${btoa(paramString).slice(0, 20)}`;
    }
  }, {
    key: "estimateDataSize",
    value: function estimateDataSize(endpoint) {
      cov_1ao0qgsyza().f[17]++;
      var sizeMap = (cov_1ao0qgsyza().s[88]++, {
        'user_profile': 5000,
        'training_sessions': 15000,
        'match_results': 10000,
        'skill_stats': 8000,
        'leaderboard': 12000,
        'social_feed': 20000
      });
      cov_1ao0qgsyza().s[89]++;
      return (cov_1ao0qgsyza().b[18][0]++, sizeMap[endpoint]) || (cov_1ao0qgsyza().b[18][1]++, 10000);
    }
  }, {
    key: "calculateOptimalTTL",
    value: function calculateOptimalTTL(endpoint) {
      cov_1ao0qgsyza().f[18]++;
      var ttlMap = (cov_1ao0qgsyza().s[90]++, {
        'user_profile': 1800000,
        'training_sessions': 3600000,
        'match_results': 7200000,
        'skill_stats': 1800000,
        'leaderboard': 300000,
        'social_feed': 600000
      });
      cov_1ao0qgsyza().s[91]++;
      return (cov_1ao0qgsyza().b[19][0]++, ttlMap[endpoint]) || (cov_1ao0qgsyza().b[19][1]++, 1800000);
    }
  }, {
    key: "getScreenDataRequirements",
    value: function getScreenDataRequirements(screenName) {
      cov_1ao0qgsyza().f[19]++;
      var screenDataMap = (cov_1ao0qgsyza().s[92]++, {
        'Dashboard': [{
          key: 'dashboard_stats',
          size: 8000,
          ttl: 1800000
        }, {
          key: 'recent_activities',
          size: 12000,
          ttl: 600000
        }],
        'Training': [{
          key: 'training_programs',
          size: 15000,
          ttl: 3600000
        }, {
          key: 'progress_data',
          size: 10000,
          ttl: 1800000
        }],
        'Progress': [{
          key: 'skill_progression',
          size: 12000,
          ttl: 1800000
        }, {
          key: 'performance_trends',
          size: 18000,
          ttl: 3600000
        }],
        'Social': [{
          key: 'social_feed',
          size: 20000,
          ttl: 600000
        }, {
          key: 'friend_activities',
          size: 15000,
          ttl: 1200000
        }]
      });
      cov_1ao0qgsyza().s[93]++;
      return (cov_1ao0qgsyza().b[20][0]++, screenDataMap[screenName]) || (cov_1ao0qgsyza().b[20][1]++, []);
    }
  }, {
    key: "deduplicatePredictions",
    value: function deduplicatePredictions(predictions) {
      cov_1ao0qgsyza().f[20]++;
      var seen = (cov_1ao0qgsyza().s[94]++, new Set());
      cov_1ao0qgsyza().s[95]++;
      return predictions.filter(function (prediction) {
        cov_1ao0qgsyza().f[21]++;
        cov_1ao0qgsyza().s[96]++;
        if (seen.has(prediction.key)) {
          cov_1ao0qgsyza().b[21][0]++;
          cov_1ao0qgsyza().s[97]++;
          return false;
        } else {
          cov_1ao0qgsyza().b[21][1]++;
        }
        cov_1ao0qgsyza().s[98]++;
        seen.add(prediction.key);
        cov_1ao0qgsyza().s[99]++;
        return true;
      });
    }
  }, {
    key: "isPredictionValid",
    value: function isPredictionValid(predictions) {
      cov_1ao0qgsyza().f[22]++;
      cov_1ao0qgsyza().s[100]++;
      if (predictions.length === 0) {
        cov_1ao0qgsyza().b[22][0]++;
        cov_1ao0qgsyza().s[101]++;
        return false;
      } else {
        cov_1ao0qgsyza().b[22][1]++;
      }
      var oldestPrediction = (cov_1ao0qgsyza().s[102]++, Math.min.apply(Math, _toConsumableArray(predictions.map(function (p) {
        cov_1ao0qgsyza().f[23]++;
        cov_1ao0qgsyza().s[103]++;
        return p.estimatedAccessTime;
      }))));
      cov_1ao0qgsyza().s[104]++;
      return Date.now() - oldestPrediction < 300000;
    }
  }, {
    key: "getCurrentSessionId",
    value: function getCurrentSessionId(userId) {
      cov_1ao0qgsyza().f[24]++;
      var now = (cov_1ao0qgsyza().s[105]++, new Date());
      var sessionDate = (cov_1ao0qgsyza().s[106]++, now.toISOString().slice(0, 10));
      var sessionHour = (cov_1ao0qgsyza().s[107]++, Math.floor(now.getHours() / 2) * 2);
      cov_1ao0qgsyza().s[108]++;
      return `${userId}_${sessionDate}_${sessionHour}`;
    }
  }, {
    key: "updatePredictions",
    value: function () {
      var _updatePredictions = _asyncToGenerator(function* (userId) {
        cov_1ao0qgsyza().f[25]++;
        cov_1ao0qgsyza().s[109]++;
        this.predictionCache.delete(userId);
      });
      function updatePredictions(_x9) {
        return _updatePredictions.apply(this, arguments);
      }
      return updatePredictions;
    }()
  }, {
    key: "persistBehaviorData",
    value: function () {
      var _persistBehaviorData = _asyncToGenerator(function* (userId) {
        cov_1ao0qgsyza().f[26]++;
        cov_1ao0qgsyza().s[110]++;
        console.log(`Persisted behavior data for user: ${userId}`);
      });
      function persistBehaviorData(_x0) {
        return _persistBehaviorData.apply(this, arguments);
      }
      return persistBehaviorData;
    }()
  }, {
    key: "loadBehaviorHistory",
    value: function () {
      var _loadBehaviorHistory = _asyncToGenerator(function* () {
        cov_1ao0qgsyza().f[27]++;
        cov_1ao0qgsyza().s[111]++;
        console.log('Loaded behavior history from storage');
      });
      function loadBehaviorHistory() {
        return _loadBehaviorHistory.apply(this, arguments);
      }
      return loadBehaviorHistory;
    }()
  }, {
    key: "startPeriodicRetraining",
    value: function startPeriodicRetraining() {
      var _this3 = this;
      cov_1ao0qgsyza().f[28]++;
      cov_1ao0qgsyza().s[112]++;
      setInterval(function () {
        cov_1ao0qgsyza().f[29]++;
        cov_1ao0qgsyza().s[113]++;
        if (!_this3.isTraining) {
          cov_1ao0qgsyza().b[23][0]++;
          cov_1ao0qgsyza().s[114]++;
          _this3.retrainModel();
        } else {
          cov_1ao0qgsyza().b[23][1]++;
        }
      }, this.MODEL_CONFIG.retrainInterval);
    }
  }, {
    key: "retrainModel",
    value: function () {
      var _retrainModel = _asyncToGenerator(function* () {
        cov_1ao0qgsyza().f[30]++;
        cov_1ao0qgsyza().s[115]++;
        this.isTraining = true;
        cov_1ao0qgsyza().s[116]++;
        try {
          cov_1ao0qgsyza().s[117]++;
          console.log('Starting ML model retraining...');
          cov_1ao0qgsyza().s[118]++;
          yield this.mlModel.retrain(Array.from(this.behaviorHistory.values()).flat());
          cov_1ao0qgsyza().s[119]++;
          console.log('ML model retraining completed');
        } catch (error) {
          cov_1ao0qgsyza().s[120]++;
          console.error('Failed to retrain ML model:', error);
        } finally {
          cov_1ao0qgsyza().s[121]++;
          this.isTraining = false;
        }
      });
      function retrainModel() {
        return _retrainModel.apply(this, arguments);
      }
      return retrainModel;
    }()
  }, {
    key: "generateMockData",
    value: function generateMockData(key) {
      cov_1ao0qgsyza().f[31]++;
      cov_1ao0qgsyza().s[122]++;
      return {
        id: Math.random().toString(36),
        data: `Mock data for ${key}`,
        timestamp: Date.now(),
        generated: true
      };
    }
  }, {
    key: "getTotalPredictions",
    value: function getTotalPredictions() {
      cov_1ao0qgsyza().f[32]++;
      cov_1ao0qgsyza().s[123]++;
      return Array.from(this.predictionCache.values()).reduce(function (sum, predictions) {
        cov_1ao0qgsyza().f[33]++;
        cov_1ao0qgsyza().s[124]++;
        return sum + predictions.length;
      }, 0);
    }
  }]);
}();
var SimplePredictionModel = function () {
  function SimplePredictionModel() {
    _classCallCheck(this, SimplePredictionModel);
    this.patterns = (cov_1ao0qgsyza().s[125]++, new Map());
    this.transitionMatrix = (cov_1ao0qgsyza().s[126]++, new Map());
  }
  return _createClass(SimplePredictionModel, [{
    key: "initialize",
    value: function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_1ao0qgsyza().f[34]++;
        cov_1ao0qgsyza().s[127]++;
        console.log('Initializing simple prediction model');
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }()
  }, {
    key: "predict",
    value: function () {
      var _predict = _asyncToGenerator(function* (input) {
        cov_1ao0qgsyza().f[35]++;
        var _ref = (cov_1ao0qgsyza().s[128]++, input),
          actions = _ref.actions;
        cov_1ao0qgsyza().s[129]++;
        if (actions.length === 0) {
          cov_1ao0qgsyza().b[24][0]++;
          cov_1ao0qgsyza().s[130]++;
          return {
            nextActions: [],
            dataRequests: [],
            screenTransitions: []
          };
        } else {
          cov_1ao0qgsyza().b[24][1]++;
        }
        var lastAction = (cov_1ao0qgsyza().s[131]++, actions[actions.length - 1]);
        var actionSequence = (cov_1ao0qgsyza().s[132]++, actions.slice(-3).map(function (a) {
          cov_1ao0qgsyza().f[36]++;
          cov_1ao0qgsyza().s[133]++;
          return a.type;
        }).join('->'));
        var nextActions = (cov_1ao0qgsyza().s[134]++, this.predictNextActions(lastAction, actionSequence));
        var dataRequests = (cov_1ao0qgsyza().s[135]++, this.predictDataRequests(lastAction));
        var screenTransitions = (cov_1ao0qgsyza().s[136]++, this.predictScreenTransitions(lastAction));
        cov_1ao0qgsyza().s[137]++;
        return {
          nextActions: nextActions,
          dataRequests: dataRequests,
          screenTransitions: screenTransitions
        };
      });
      function predict(_x1) {
        return _predict.apply(this, arguments);
      }
      return predict;
    }()
  }, {
    key: "retrain",
    value: function () {
      var _retrain = _asyncToGenerator(function* (behaviorData) {
        var _this4 = this;
        cov_1ao0qgsyza().f[37]++;
        cov_1ao0qgsyza().s[138]++;
        console.log(`Retraining model with ${behaviorData.length} behavior patterns`);
        cov_1ao0qgsyza().s[139]++;
        behaviorData.forEach(function (pattern) {
          cov_1ao0qgsyza().f[38]++;
          cov_1ao0qgsyza().s[140]++;
          pattern.actions.forEach(function (action, index) {
            cov_1ao0qgsyza().f[39]++;
            cov_1ao0qgsyza().s[141]++;
            if (index < pattern.actions.length - 1) {
              cov_1ao0qgsyza().b[25][0]++;
              var current = (cov_1ao0qgsyza().s[142]++, action.type);
              var next = (cov_1ao0qgsyza().s[143]++, pattern.actions[index + 1].type);
              cov_1ao0qgsyza().s[144]++;
              if (!_this4.transitionMatrix.has(current)) {
                cov_1ao0qgsyza().b[26][0]++;
                cov_1ao0qgsyza().s[145]++;
                _this4.transitionMatrix.set(current, new Map());
              } else {
                cov_1ao0qgsyza().b[26][1]++;
              }
              var transitions = (cov_1ao0qgsyza().s[146]++, _this4.transitionMatrix.get(current));
              cov_1ao0qgsyza().s[147]++;
              transitions.set(next, ((cov_1ao0qgsyza().b[27][0]++, transitions.get(next)) || (cov_1ao0qgsyza().b[27][1]++, 0)) + 1);
            } else {
              cov_1ao0qgsyza().b[25][1]++;
            }
          });
        });
      });
      function retrain(_x10) {
        return _retrain.apply(this, arguments);
      }
      return retrain;
    }()
  }, {
    key: "predictNextActions",
    value: function predictNextActions(lastAction, sequence) {
      cov_1ao0qgsyza().f[40]++;
      var predictions = (cov_1ao0qgsyza().s[148]++, [{
        action: 'screen_view',
        probability: 0.7,
        timeToAction: 2000
      }, {
        action: 'data_request',
        probability: 0.6,
        timeToAction: 1000
      }, {
        action: 'interaction',
        probability: 0.8,
        timeToAction: 3000
      }]);
      cov_1ao0qgsyza().s[149]++;
      return predictions;
    }
  }, {
    key: "predictDataRequests",
    value: function predictDataRequests(lastAction) {
      cov_1ao0qgsyza().f[41]++;
      var requestMap = (cov_1ao0qgsyza().s[150]++, {
        'screen_view': [{
          endpoint: 'user_profile',
          probability: 0.8
        }, {
          endpoint: 'recent_activities',
          probability: 0.7
        }],
        'navigation': [{
          endpoint: 'training_sessions',
          probability: 0.9
        }, {
          endpoint: 'progress_data',
          probability: 0.6
        }],
        'interaction': [{
          endpoint: 'skill_stats',
          probability: 0.8
        }, {
          endpoint: 'performance_trends',
          probability: 0.5
        }]
      });
      cov_1ao0qgsyza().s[151]++;
      return (cov_1ao0qgsyza().b[28][0]++, requestMap[lastAction.type]) || (cov_1ao0qgsyza().b[28][1]++, []);
    }
  }, {
    key: "predictScreenTransitions",
    value: function predictScreenTransitions(lastAction) {
      cov_1ao0qgsyza().f[42]++;
      var transitionMap = (cov_1ao0qgsyza().s[152]++, {
        'screen_view': [{
          screen: 'Training',
          probability: 0.6,
          time: 5000
        }, {
          screen: 'Progress',
          probability: 0.4,
          time: 8000
        }],
        'navigation': [{
          screen: 'Dashboard',
          probability: 0.7,
          time: 3000
        }, {
          screen: 'Social',
          probability: 0.3,
          time: 10000
        }]
      });
      var transitions = (cov_1ao0qgsyza().s[153]++, (cov_1ao0qgsyza().b[29][0]++, transitionMap[lastAction.type]) || (cov_1ao0qgsyza().b[29][1]++, []));
      cov_1ao0qgsyza().s[154]++;
      return transitions.map(function (t) {
        cov_1ao0qgsyza().f[43]++;
        cov_1ao0qgsyza().s[155]++;
        return {
          screen: t.screen,
          probability: t.probability,
          timeToTransition: t.time
        };
      });
    }
  }]);
}();
export var predictiveCacheEngine = (cov_1ao0qgsyza().s[156]++, new PredictiveCacheEngine());
export default predictiveCacheEngine;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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