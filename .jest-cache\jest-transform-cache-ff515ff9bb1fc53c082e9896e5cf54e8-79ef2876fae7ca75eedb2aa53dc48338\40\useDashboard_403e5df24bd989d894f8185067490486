2b1b4dfa002c6a0e30b55a926dc94896
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1bt68itmx4() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useDashboard.ts";
  var hash = "a50e19094534649c14b6b119aa96259b5e559c2f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useDashboard.ts",
    statementMap: {
      "0": {
        start: {
          line: 17,
          column: 26
        },
        end: {
          line: 17,
          column: 62
        }
      },
      "1": {
        start: {
          line: 18,
          column: 32
        },
        end: {
          line: 18,
          column: 46
        }
      },
      "2": {
        start: {
          line: 19,
          column: 28
        },
        end: {
          line: 19,
          column: 57
        }
      },
      "3": {
        start: {
          line: 20,
          column: 38
        },
        end: {
          line: 20,
          column: 53
        }
      },
      "4": {
        start: {
          line: 21,
          column: 19
        },
        end: {
          line: 21,
          column: 28
        }
      },
      "5": {
        start: {
          line: 23,
          column: 28
        },
        end: {
          line: 46,
          column: 12
        }
      },
      "6": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 27,
          column: 5
        }
      },
      "7": {
        start: {
          line: 25,
          column: 6
        },
        end: {
          line: 25,
          column: 24
        }
      },
      "8": {
        start: {
          line: 26,
          column: 6
        },
        end: {
          line: 26,
          column: 13
        }
      },
      "9": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 45,
          column: 5
        }
      },
      "10": {
        start: {
          line: 30,
          column: 6
        },
        end: {
          line: 34,
          column: 7
        }
      },
      "11": {
        start: {
          line: 31,
          column: 8
        },
        end: {
          line: 31,
          column: 28
        }
      },
      "12": {
        start: {
          line: 33,
          column: 8
        },
        end: {
          line: 33,
          column: 25
        }
      },
      "13": {
        start: {
          line: 35,
          column: 6
        },
        end: {
          line: 35,
          column: 21
        }
      },
      "14": {
        start: {
          line: 37,
          column: 28
        },
        end: {
          line: 37,
          column: 70
        }
      },
      "15": {
        start: {
          line: 38,
          column: 6
        },
        end: {
          line: 38,
          column: 29
        }
      },
      "16": {
        start: {
          line: 40,
          column: 6
        },
        end: {
          line: 40,
          column: 85
        }
      },
      "17": {
        start: {
          line: 41,
          column: 6
        },
        end: {
          line: 41,
          column: 58
        }
      },
      "18": {
        start: {
          line: 43,
          column: 6
        },
        end: {
          line: 43,
          column: 24
        }
      },
      "19": {
        start: {
          line: 44,
          column: 6
        },
        end: {
          line: 44,
          column: 27
        }
      },
      "20": {
        start: {
          line: 48,
          column: 22
        },
        end: {
          line: 50,
          column: 25
        }
      },
      "21": {
        start: {
          line: 49,
          column: 4
        },
        end: {
          line: 49,
          column: 34
        }
      },
      "22": {
        start: {
          line: 52,
          column: 25
        },
        end: {
          line: 66,
          column: 18
        }
      },
      "23": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 53,
          column: 31
        }
      },
      "24": {
        start: {
          line: 53,
          column: 24
        },
        end: {
          line: 53,
          column: 31
        }
      },
      "25": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 65,
          column: 5
        }
      },
      "26": {
        start: {
          line: 56,
          column: 22
        },
        end: {
          line: 56,
          column: 104
        }
      },
      "27": {
        start: {
          line: 56,
          column: 83
        },
        end: {
          line: 56,
          column: 90
        }
      },
      "28": {
        start: {
          line: 57,
          column: 21
        },
        end: {
          line: 57,
          column: 69
        }
      },
      "29": {
        start: {
          line: 59,
          column: 6
        },
        end: {
          line: 62,
          column: 16
        }
      },
      "30": {
        start: {
          line: 59,
          column: 22
        },
        end: {
          line: 62,
          column: 14
        }
      },
      "31": {
        start: {
          line: 64,
          column: 6
        },
        end: {
          line: 64,
          column: 56
        }
      },
      "32": {
        start: {
          line: 68,
          column: 31
        },
        end: {
          line: 83,
          column: 8
        }
      },
      "33": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 82,
          column: 5
        }
      },
      "34": {
        start: {
          line: 70,
          column: 6
        },
        end: {
          line: 70,
          column: 62
        }
      },
      "35": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 79,
          column: 16
        }
      },
      "36": {
        start: {
          line: 72,
          column: 22
        },
        end: {
          line: 79,
          column: 14
        }
      },
      "37": {
        start: {
          line: 75,
          column: 10
        },
        end: {
          line: 77,
          column: 26
        }
      },
      "38": {
        start: {
          line: 81,
          column: 6
        },
        end: {
          line: 81,
          column: 65
        }
      },
      "39": {
        start: {
          line: 85,
          column: 2
        },
        end: {
          line: 87,
          column: 26
        }
      },
      "40": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 24
        }
      },
      "41": {
        start: {
          line: 89,
          column: 2
        },
        end: {
          line: 97,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useDashboard",
        decl: {
          start: {
            line: 16,
            column: 16
          },
          end: {
            line: 16,
            column: 28
          }
        },
        loc: {
          start: {
            line: 16,
            column: 51
          },
          end: {
            line: 98,
            column: 1
          }
        },
        line: 16
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 23,
            column: 40
          },
          end: {
            line: 23,
            column: 41
          }
        },
        loc: {
          start: {
            line: 23,
            column: 69
          },
          end: {
            line: 46,
            column: 3
          }
        },
        line: 23
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 48,
            column: 34
          },
          end: {
            line: 48,
            column: 35
          }
        },
        loc: {
          start: {
            line: 48,
            column: 46
          },
          end: {
            line: 50,
            column: 3
          }
        },
        line: 48
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 52,
            column: 37
          },
          end: {
            line: 52,
            column: 38
          }
        },
        loc: {
          start: {
            line: 52,
            column: 49
          },
          end: {
            line: 66,
            column: 3
          }
        },
        line: 52
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 56,
            column: 78
          },
          end: {
            line: 56,
            column: 79
          }
        },
        loc: {
          start: {
            line: 56,
            column: 83
          },
          end: {
            line: 56,
            column: 90
          }
        },
        line: 56
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 59,
            column: 14
          },
          end: {
            line: 59,
            column: 15
          }
        },
        loc: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 62,
            column: 14
          }
        },
        line: 59
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 68,
            column: 43
          },
          end: {
            line: 68,
            column: 44
          }
        },
        loc: {
          start: {
            line: 68,
            column: 77
          },
          end: {
            line: 83,
            column: 3
          }
        },
        line: 68
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 72,
            column: 14
          },
          end: {
            line: 72,
            column: 15
          }
        },
        loc: {
          start: {
            line: 72,
            column: 22
          },
          end: {
            line: 79,
            column: 14
          }
        },
        line: 72
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 74,
            column: 46
          },
          end: {
            line: 74,
            column: 47
          }
        },
        loc: {
          start: {
            line: 75,
            column: 10
          },
          end: {
            line: 77,
            column: 26
          }
        },
        line: 75
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 85,
            column: 12
          },
          end: {
            line: 85,
            column: 13
          }
        },
        loc: {
          start: {
            line: 85,
            column: 18
          },
          end: {
            line: 87,
            column: 3
          }
        },
        line: 85
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 23,
            column: 47
          },
          end: {
            line: 23,
            column: 64
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 23,
            column: 59
          },
          end: {
            line: 23,
            column: 64
          }
        }],
        line: 23
      },
      "1": {
        loc: {
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 27,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 27,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 24
      },
      "2": {
        loc: {
          start: {
            line: 30,
            column: 6
          },
          end: {
            line: 34,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 30,
            column: 6
          },
          end: {
            line: 34,
            column: 7
          }
        }, {
          start: {
            line: 32,
            column: 13
          },
          end: {
            line: 34,
            column: 7
          }
        }],
        line: 30
      },
      "3": {
        loc: {
          start: {
            line: 40,
            column: 15
          },
          end: {
            line: 40,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 40,
            column: 38
          },
          end: {
            line: 40,
            column: 49
          }
        }, {
          start: {
            line: 40,
            column: 52
          },
          end: {
            line: 40,
            column: 83
          }
        }],
        line: 40
      },
      "4": {
        loc: {
          start: {
            line: 53,
            column: 4
          },
          end: {
            line: 53,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 53,
            column: 4
          },
          end: {
            line: 53,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 53
      },
      "5": {
        loc: {
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 53,
            column: 22
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 53,
            column: 8
          },
          end: {
            line: 53,
            column: 13
          }
        }, {
          start: {
            line: 53,
            column: 17
          },
          end: {
            line: 53,
            column: 22
          }
        }],
        line: 53
      },
      "6": {
        loc: {
          start: {
            line: 59,
            column: 22
          },
          end: {
            line: 62,
            column: 14
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 59,
            column: 29
          },
          end: {
            line: 62,
            column: 7
          }
        }, {
          start: {
            line: 62,
            column: 10
          },
          end: {
            line: 62,
            column: 14
          }
        }],
        line: 59
      },
      "7": {
        loc: {
          start: {
            line: 72,
            column: 22
          },
          end: {
            line: 79,
            column: 14
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 72,
            column: 29
          },
          end: {
            line: 79,
            column: 7
          }
        }, {
          start: {
            line: 79,
            column: 10
          },
          end: {
            line: 79,
            column: 14
          }
        }],
        line: 72
      },
      "8": {
        loc: {
          start: {
            line: 75,
            column: 10
          },
          end: {
            line: 77,
            column: 26
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 76,
            column: 14
          },
          end: {
            line: 76,
            column: 45
          }
        }, {
          start: {
            line: 77,
            column: 14
          },
          end: {
            line: 77,
            column: 26
          }
        }],
        line: 75
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a50e19094534649c14b6b119aa96259b5e559c2f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1bt68itmx4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1bt68itmx4();
import { useState, useEffect, useCallback } from 'react';
import { apiService } from "../services/api";
import { useAuth } from "../contexts/AuthContext";
export function useDashboard() {
  cov_1bt68itmx4().f[0]++;
  var _ref = (cov_1bt68itmx4().s[0]++, useState(null)),
    _ref2 = _slicedToArray(_ref, 2),
    data = _ref2[0],
    setData = _ref2[1];
  var _ref3 = (cov_1bt68itmx4().s[1]++, useState(true)),
    _ref4 = _slicedToArray(_ref3, 2),
    loading = _ref4[0],
    setLoading = _ref4[1];
  var _ref5 = (cov_1bt68itmx4().s[2]++, useState(null)),
    _ref6 = _slicedToArray(_ref5, 2),
    error = _ref6[0],
    setError = _ref6[1];
  var _ref7 = (cov_1bt68itmx4().s[3]++, useState(false)),
    _ref8 = _slicedToArray(_ref7, 2),
    refreshing = _ref8[0],
    setRefreshing = _ref8[1];
  var _ref9 = (cov_1bt68itmx4().s[4]++, useAuth()),
    user = _ref9.user;
  var loadDashboardData = (cov_1bt68itmx4().s[5]++, useCallback(_asyncToGenerator(function* () {
    var isRefresh = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1bt68itmx4().b[0][0]++, false);
    cov_1bt68itmx4().f[1]++;
    cov_1bt68itmx4().s[6]++;
    if (!user) {
      cov_1bt68itmx4().b[1][0]++;
      cov_1bt68itmx4().s[7]++;
      setLoading(false);
      cov_1bt68itmx4().s[8]++;
      return;
    } else {
      cov_1bt68itmx4().b[1][1]++;
    }
    cov_1bt68itmx4().s[9]++;
    try {
      cov_1bt68itmx4().s[10]++;
      if (isRefresh) {
        cov_1bt68itmx4().b[2][0]++;
        cov_1bt68itmx4().s[11]++;
        setRefreshing(true);
      } else {
        cov_1bt68itmx4().b[2][1]++;
        cov_1bt68itmx4().s[12]++;
        setLoading(true);
      }
      cov_1bt68itmx4().s[13]++;
      setError(null);
      var dashboardData = (cov_1bt68itmx4().s[14]++, yield apiService.getDashboardData(user.id));
      cov_1bt68itmx4().s[15]++;
      setData(dashboardData);
    } catch (err) {
      cov_1bt68itmx4().s[16]++;
      setError(err instanceof Error ? (cov_1bt68itmx4().b[3][0]++, err.message) : (cov_1bt68itmx4().b[3][1]++, 'Failed to load dashboard data'));
      cov_1bt68itmx4().s[17]++;
      console.error('Dashboard data loading error:', err);
    } finally {
      cov_1bt68itmx4().s[18]++;
      setLoading(false);
      cov_1bt68itmx4().s[19]++;
      setRefreshing(false);
    }
  }), [user]));
  var refreshData = (cov_1bt68itmx4().s[20]++, useCallback(_asyncToGenerator(function* () {
    cov_1bt68itmx4().f[2]++;
    cov_1bt68itmx4().s[21]++;
    yield loadDashboardData(true);
  }), [loadDashboardData]));
  var generateNewTip = (cov_1bt68itmx4().s[22]++, useCallback(_asyncToGenerator(function* () {
    cov_1bt68itmx4().f[3]++;
    cov_1bt68itmx4().s[23]++;
    if ((cov_1bt68itmx4().b[5][0]++, !data) || (cov_1bt68itmx4().b[5][1]++, !user)) {
      cov_1bt68itmx4().b[4][0]++;
      cov_1bt68itmx4().s[24]++;
      return;
    } else {
      cov_1bt68itmx4().b[4][1]++;
    }
    cov_1bt68itmx4().s[25]++;
    try {
      var context = (cov_1bt68itmx4().s[26]++, `Recent sessions: ${data.recentSessions.slice(0, 2).map(function (s) {
        cov_1bt68itmx4().f[4]++;
        cov_1bt68itmx4().s[27]++;
        return s.title;
      }).join(', ')}`);
      var newTip = (cov_1bt68itmx4().s[28]++, yield apiService.generateAITip(user.id, context));
      cov_1bt68itmx4().s[29]++;
      setData(function (prev) {
        cov_1bt68itmx4().f[5]++;
        cov_1bt68itmx4().s[30]++;
        return prev ? (cov_1bt68itmx4().b[6][0]++, Object.assign({}, prev, {
          dailyTip: newTip
        })) : (cov_1bt68itmx4().b[6][1]++, null);
      });
    } catch (err) {
      cov_1bt68itmx4().s[31]++;
      console.error('Failed to generate new tip:', err);
    }
  }), [data, user]));
  var markNotificationRead = (cov_1bt68itmx4().s[32]++, useCallback(function () {
    var _ref11 = _asyncToGenerator(function* (notificationId) {
      cov_1bt68itmx4().f[6]++;
      cov_1bt68itmx4().s[33]++;
      try {
        cov_1bt68itmx4().s[34]++;
        yield apiService.markNotificationAsRead(notificationId);
        cov_1bt68itmx4().s[35]++;
        setData(function (prev) {
          cov_1bt68itmx4().f[7]++;
          cov_1bt68itmx4().s[36]++;
          return prev ? (cov_1bt68itmx4().b[7][0]++, Object.assign({}, prev, {
            notifications: prev.notifications.map(function (notification) {
              cov_1bt68itmx4().f[8]++;
              cov_1bt68itmx4().s[37]++;
              return notification.id === notificationId ? (cov_1bt68itmx4().b[8][0]++, Object.assign({}, notification, {
                read: true
              })) : (cov_1bt68itmx4().b[8][1]++, notification);
            })
          })) : (cov_1bt68itmx4().b[7][1]++, null);
        });
      } catch (err) {
        cov_1bt68itmx4().s[38]++;
        console.error('Failed to mark notification as read:', err);
      }
    });
    return function (_x) {
      return _ref11.apply(this, arguments);
    };
  }(), []));
  cov_1bt68itmx4().s[39]++;
  useEffect(function () {
    cov_1bt68itmx4().f[9]++;
    cov_1bt68itmx4().s[40]++;
    loadDashboardData();
  }, [loadDashboardData]);
  cov_1bt68itmx4().s[41]++;
  return {
    data: data,
    loading: loading,
    error: error,
    refreshing: refreshing,
    refreshData: refreshData,
    generateNewTip: generateNewTip,
    markNotificationRead: markNotificationRead
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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