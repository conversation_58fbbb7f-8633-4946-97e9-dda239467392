717da96681222159c504ff8c80242e2b
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = createResponderEvent;
var _getBoundingClientRect = _interopRequireDefault(require("../../modules/getBoundingClientRect"));
var emptyFunction = function emptyFunction() {};
var emptyObject = {};
var emptyArray = [];
function normalizeIdentifier(identifier) {
  return identifier > 20 ? identifier % 20 : identifier;
}
function createResponderEvent(domEvent, responderTouchHistoryStore) {
  var rect;
  var propagationWasStopped = false;
  var changedTouches;
  var touches;
  var domEventChangedTouches = domEvent.changedTouches;
  var domEventType = domEvent.type;
  var metaKey = domEvent.metaKey === true;
  var shiftKey = domEvent.shiftKey === true;
  var force = domEventChangedTouches && domEventChangedTouches[0].force || 0;
  var identifier = normalizeIdentifier(domEventChangedTouches && domEventChangedTouches[0].identifier || 0);
  var clientX = domEventChangedTouches && domEventChangedTouches[0].clientX || domEvent.clientX;
  var clientY = domEventChangedTouches && domEventChangedTouches[0].clientY || domEvent.clientY;
  var pageX = domEventChangedTouches && domEventChangedTouches[0].pageX || domEvent.pageX;
  var pageY = domEventChangedTouches && domEventChangedTouches[0].pageY || domEvent.pageY;
  var preventDefault = typeof domEvent.preventDefault === 'function' ? domEvent.preventDefault.bind(domEvent) : emptyFunction;
  var timestamp = domEvent.timeStamp;
  function normalizeTouches(touches) {
    return Array.prototype.slice.call(touches).map(function (touch) {
      return {
        force: touch.force,
        identifier: normalizeIdentifier(touch.identifier),
        get locationX() {
          return locationX(touch.clientX);
        },
        get locationY() {
          return locationY(touch.clientY);
        },
        pageX: touch.pageX,
        pageY: touch.pageY,
        target: touch.target,
        timestamp: timestamp
      };
    });
  }
  if (domEventChangedTouches != null) {
    changedTouches = normalizeTouches(domEventChangedTouches);
    touches = normalizeTouches(domEvent.touches);
  } else {
    var emulatedTouches = [{
      force: force,
      identifier: identifier,
      get locationX() {
        return locationX(clientX);
      },
      get locationY() {
        return locationY(clientY);
      },
      pageX: pageX,
      pageY: pageY,
      target: domEvent.target,
      timestamp: timestamp
    }];
    changedTouches = emulatedTouches;
    touches = domEventType === 'mouseup' || domEventType === 'dragstart' ? emptyArray : emulatedTouches;
  }
  var responderEvent = {
    bubbles: true,
    cancelable: true,
    currentTarget: null,
    defaultPrevented: domEvent.defaultPrevented,
    dispatchConfig: emptyObject,
    eventPhase: domEvent.eventPhase,
    isDefaultPrevented: function isDefaultPrevented() {
      return domEvent.defaultPrevented;
    },
    isPropagationStopped: function isPropagationStopped() {
      return propagationWasStopped;
    },
    isTrusted: domEvent.isTrusted,
    nativeEvent: {
      altKey: false,
      ctrlKey: false,
      metaKey: metaKey,
      shiftKey: shiftKey,
      changedTouches: changedTouches,
      force: force,
      identifier: identifier,
      get locationX() {
        return locationX(clientX);
      },
      get locationY() {
        return locationY(clientY);
      },
      pageX: pageX,
      pageY: pageY,
      target: domEvent.target,
      timestamp: timestamp,
      touches: touches,
      type: domEventType
    },
    persist: emptyFunction,
    preventDefault: preventDefault,
    stopPropagation: function stopPropagation() {
      propagationWasStopped = true;
    },
    target: domEvent.target,
    timeStamp: timestamp,
    touchHistory: responderTouchHistoryStore.touchHistory
  };
  function locationX(x) {
    rect = rect || (0, _getBoundingClientRect.default)(responderEvent.currentTarget);
    if (rect) {
      return x - rect.left;
    }
  }
  function locationY(y) {
    rect = rect || (0, _getBoundingClientRect.default)(responderEvent.currentTarget);
    if (rect) {
      return y - rect.top;
    }
  }
  return responderEvent;
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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