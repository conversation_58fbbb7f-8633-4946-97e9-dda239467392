db34b59c7568cd153c0657c6091c114d
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1tiq4f76kv() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\subscription\\PremiumGate.tsx";
  var hash = "9b32f67ef2d4387e86ca8496031bfef814a72781";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\subscription\\PremiumGate.tsx",
    statementMap: {
      "0": {
        start: {
          line: 46,
          column: 22
        },
        end: {
          line: 46,
          column: 31
        }
      },
      "1": {
        start: {
          line: 47,
          column: 50
        },
        end: {
          line: 47,
          column: 65
        }
      },
      "2": {
        start: {
          line: 48,
          column: 50
        },
        end: {
          line: 48,
          column: 65
        }
      },
      "3": {
        start: {
          line: 50,
          column: 20
        },
        end: {
          line: 50,
          column: 62
        }
      },
      "4": {
        start: {
          line: 51,
          column: 18
        },
        end: {
          line: 51,
          column: 50
        }
      },
      "5": {
        start: {
          line: 52,
          column: 22
        },
        end: {
          line: 52,
          column: 53
        }
      },
      "6": {
        start: {
          line: 54,
          column: 2
        },
        end: {
          line: 56,
          column: 3
        }
      },
      "7": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 27
        }
      },
      "8": {
        start: {
          line: 59,
          column: 23
        },
        end: {
          line: 61,
          column: 3
        }
      },
      "9": {
        start: {
          line: 60,
          column: 4
        },
        end: {
          line: 60,
          column: 59
        }
      },
      "10": {
        start: {
          line: 63,
          column: 24
        },
        end: {
          line: 66,
          column: 3
        }
      },
      "11": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 64,
          column: 31
        }
      },
      "12": {
        start: {
          line: 65,
          column: 4
        },
        end: {
          line: 65,
          column: 30
        }
      },
      "13": {
        start: {
          line: 68,
          column: 27
        },
        end: {
          line: 86,
          column: 3
        }
      },
      "14": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 30
        }
      },
      "15": {
        start: {
          line: 69,
          column: 23
        },
        end: {
          line: 69,
          column: 30
        }
      },
      "16": {
        start: {
          line: 71,
          column: 4
        },
        end: {
          line: 85,
          column: 5
        }
      },
      "17": {
        start: {
          line: 72,
          column: 38
        },
        end: {
          line: 72,
          column: 90
        }
      },
      "18": {
        start: {
          line: 74,
          column: 6
        },
        end: {
          line: 82,
          column: 7
        }
      },
      "19": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 75,
          column: 42
        }
      },
      "20": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 80,
          column: 10
        }
      },
      "21": {
        start: {
          line: 81,
          column: 8
        },
        end: {
          line: 81,
          column: 35
        }
      },
      "22": {
        start: {
          line: 84,
          column: 6
        },
        end: {
          line: 84,
          column: 71
        }
      },
      "23": {
        start: {
          line: 88,
          column: 2
        },
        end: {
          line: 90,
          column: 3
        }
      },
      "24": {
        start: {
          line: 89,
          column: 4
        },
        end: {
          line: 89,
          column: 27
        }
      },
      "25": {
        start: {
          line: 92,
          column: 2
        },
        end: {
          line: 94,
          column: 3
        }
      },
      "26": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 16
        }
      },
      "27": {
        start: {
          line: 96,
          column: 2
        },
        end: {
          line: 259,
          column: 4
        }
      },
      "28": {
        start: {
          line: 100,
          column: 23
        },
        end: {
          line: 100,
          column: 48
        }
      },
      "29": {
        start: {
          line: 121,
          column: 29
        },
        end: {
          line: 121,
          column: 54
        }
      },
      "30": {
        start: {
          line: 139,
          column: 30
        },
        end: {
          line: 139,
          column: 56
        }
      },
      "31": {
        start: {
          line: 145,
          column: 29
        },
        end: {
          line: 145,
          column: 55
        }
      },
      "32": {
        start: {
          line: 186,
          column: 26
        },
        end: {
          line: 186,
          column: 52
        }
      },
      "33": {
        start: {
          line: 187,
          column: 16
        },
        end: {
          line: 192,
          column: 25
        }
      },
      "34": {
        start: {
          line: 232,
          column: 30
        },
        end: {
          line: 232,
          column: 56
        }
      },
      "35": {
        start: {
          line: 238,
          column: 29
        },
        end: {
          line: 238,
          column: 55
        }
      },
      "36": {
        start: {
          line: 248,
          column: 14
        },
        end: {
          line: 248,
          column: 41
        }
      },
      "37": {
        start: {
          line: 250,
          column: 14
        },
        end: {
          line: 253,
          column: 16
        }
      },
      "38": {
        start: {
          line: 262,
          column: 15
        },
        end: {
          line: 481,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "PremiumGate",
        decl: {
          start: {
            line: 39,
            column: 16
          },
          end: {
            line: 39,
            column: 27
          }
        },
        loc: {
          start: {
            line: 45,
            column: 21
          },
          end: {
            line: 260,
            column: 1
          }
        },
        line: 45
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 59,
            column: 62
          },
          end: {
            line: 59,
            column: 63
          }
        },
        loc: {
          start: {
            line: 60,
            column: 4
          },
          end: {
            line: 60,
            column: 59
          }
        },
        line: 60
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 63,
            column: 24
          },
          end: {
            line: 63,
            column: 25
          }
        },
        loc: {
          start: {
            line: 63,
            column: 30
          },
          end: {
            line: 66,
            column: 3
          }
        },
        line: 63
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 68,
            column: 27
          },
          end: {
            line: 68,
            column: 28
          }
        },
        loc: {
          start: {
            line: 68,
            column: 39
          },
          end: {
            line: 86,
            column: 3
          }
        },
        line: 68
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 100,
            column: 17
          },
          end: {
            line: 100,
            column: 18
          }
        },
        loc: {
          start: {
            line: 100,
            column: 23
          },
          end: {
            line: 100,
            column: 48
          }
        },
        line: 100
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 121,
            column: 23
          },
          end: {
            line: 121,
            column: 24
          }
        },
        loc: {
          start: {
            line: 121,
            column: 29
          },
          end: {
            line: 121,
            column: 54
          }
        },
        line: 121
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 139,
            column: 24
          },
          end: {
            line: 139,
            column: 25
          }
        },
        loc: {
          start: {
            line: 139,
            column: 30
          },
          end: {
            line: 139,
            column: 56
          }
        },
        line: 139
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 145,
            column: 23
          },
          end: {
            line: 145,
            column: 24
          }
        },
        loc: {
          start: {
            line: 145,
            column: 29
          },
          end: {
            line: 145,
            column: 55
          }
        },
        line: 145
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 185,
            column: 54
          },
          end: {
            line: 185,
            column: 55
          }
        },
        loc: {
          start: {
            line: 185,
            column: 61
          },
          end: {
            line: 193,
            column: 15
          }
        },
        line: 185
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 232,
            column: 24
          },
          end: {
            line: 232,
            column: 25
          }
        },
        loc: {
          start: {
            line: 232,
            column: 30
          },
          end: {
            line: 232,
            column: 56
          }
        },
        line: 232
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 238,
            column: 23
          },
          end: {
            line: 238,
            column: 24
          }
        },
        loc: {
          start: {
            line: 238,
            column: 29
          },
          end: {
            line: 238,
            column: 55
          }
        },
        line: 238
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 247,
            column: 26
          },
          end: {
            line: 247,
            column: 27
          }
        },
        loc: {
          start: {
            line: 247,
            column: 50
          },
          end: {
            line: 254,
            column: 13
          }
        },
        line: 247
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 43,
            column: 2
          },
          end: {
            line: 43,
            column: 26
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 43,
            column: 22
          },
          end: {
            line: 43,
            column: 26
          }
        }],
        line: 43
      },
      "1": {
        loc: {
          start: {
            line: 54,
            column: 2
          },
          end: {
            line: 56,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 54,
            column: 2
          },
          end: {
            line: 56,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 54
      },
      "2": {
        loc: {
          start: {
            line: 60,
            column: 4
          },
          end: {
            line: 60,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 60,
            column: 4
          },
          end: {
            line: 60,
            column: 37
          }
        }, {
          start: {
            line: 60,
            column: 41
          },
          end: {
            line: 60,
            column: 59
          }
        }],
        line: 60
      },
      "3": {
        loc: {
          start: {
            line: 69,
            column: 4
          },
          end: {
            line: 69,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 69,
            column: 4
          },
          end: {
            line: 69,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 69
      },
      "4": {
        loc: {
          start: {
            line: 74,
            column: 6
          },
          end: {
            line: 82,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 74,
            column: 6
          },
          end: {
            line: 82,
            column: 7
          }
        }, {
          start: {
            line: 76,
            column: 13
          },
          end: {
            line: 82,
            column: 7
          }
        }],
        line: 74
      },
      "5": {
        loc: {
          start: {
            line: 88,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 88,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 88
      },
      "6": {
        loc: {
          start: {
            line: 92,
            column: 2
          },
          end: {
            line: 94,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 2
          },
          end: {
            line: 94,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "7": {
        loc: {
          start: {
            line: 116,
            column: 15
          },
          end: {
            line: 116,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 116,
            column: 15
          },
          end: {
            line: 116,
            column: 28
          }
        }, {
          start: {
            line: 116,
            column: 32
          },
          end: {
            line: 116,
            column: 85
          }
        }],
        line: 116
      },
      "8": {
        loc: {
          start: {
            line: 153,
            column: 13
          },
          end: {
            line: 167,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 153,
            column: 13
          },
          end: {
            line: 153,
            column: 20
          }
        }, {
          start: {
            line: 154,
            column: 14
          },
          end: {
            line: 166,
            column: 21
          }
        }],
        line: 153
      },
      "9": {
        loc: {
          start: {
            line: 156,
            column: 26
          },
          end: {
            line: 156,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 156,
            column: 26
          },
          end: {
            line: 156,
            column: 48
          }
        }, {
          start: {
            line: 156,
            column: 52
          },
          end: {
            line: 156,
            column: 74
          }
        }],
        line: 156
      },
      "10": {
        loc: {
          start: {
            line: 169,
            column: 13
          },
          end: {
            line: 181,
            column: 13
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 13
          },
          end: {
            line: 169,
            column: 25
          }
        }, {
          start: {
            line: 170,
            column: 14
          },
          end: {
            line: 180,
            column: 21
          }
        }],
        line: 169
      },
      "11": {
        loc: {
          start: {
            line: 187,
            column: 23
          },
          end: {
            line: 192,
            column: 24
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 188,
            column: 18
          },
          end: {
            line: 191,
            column: 25
          }
        }, {
          start: {
            line: 192,
            column: 20
          },
          end: {
            line: 192,
            column: 24
          }
        }],
        line: 187
      },
      "12": {
        loc: {
          start: {
            line: 194,
            column: 15
          },
          end: {
            line: 198,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 194,
            column: 15
          },
          end: {
            line: 194,
            column: 27
          }
        }, {
          start: {
            line: 194,
            column: 31
          },
          end: {
            line: 194,
            column: 63
          }
        }, {
          start: {
            line: 195,
            column: 16
          },
          end: {
            line: 197,
            column: 23
          }
        }],
        line: 194
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9b32f67ef2d4387e86ca8496031bfef814a72781"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1tiq4f76kv = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1tiq4f76kv();
import React, { useState } from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal, ScrollView, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { BlurView } from 'expo-blur';
import { SUBSCRIPTION_FEATURES, SUBSCRIPTION_TIERS, formatPrice } from "../../config/subscription.config";
import { paymentService } from "../../services/payment/PaymentService";
import { useAuth } from "../../contexts/AuthContext";
import PricingPlans from "./PricingPlans";
import { Fragment as _Fragment, jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export function PremiumGate(_ref) {
  var featureId = _ref.featureId,
    children = _ref.children,
    fallback = _ref.fallback,
    _ref$showUpgradePromp = _ref.showUpgradePrompt,
    showUpgradePrompt = _ref$showUpgradePromp === void 0 ? (cov_1tiq4f76kv().b[0][0]++, true) : _ref$showUpgradePromp,
    customMessage = _ref.customMessage;
  cov_1tiq4f76kv().f[0]++;
  var _ref2 = (cov_1tiq4f76kv().s[0]++, useAuth()),
    profile = _ref2.profile;
  var _ref3 = (cov_1tiq4f76kv().s[1]++, useState(false)),
    _ref4 = _slicedToArray(_ref3, 2),
    showUpgradeModal = _ref4[0],
    setShowUpgradeModal = _ref4[1];
  var _ref5 = (cov_1tiq4f76kv().s[2]++, useState(false)),
    _ref6 = _slicedToArray(_ref5, 2),
    showPricingModal = _ref6[0],
    setShowPricingModal = _ref6[1];
  var hasAccess = (cov_1tiq4f76kv().s[3]++, paymentService.canAccessFeature(featureId));
  var feature = (cov_1tiq4f76kv().s[4]++, SUBSCRIPTION_FEATURES[featureId]);
  var currentTier = (cov_1tiq4f76kv().s[5]++, paymentService.getCurrentTier());
  cov_1tiq4f76kv().s[6]++;
  if (hasAccess) {
    cov_1tiq4f76kv().b[1][0]++;
    cov_1tiq4f76kv().s[7]++;
    return _jsx(_Fragment, {
      children: children
    });
  } else {
    cov_1tiq4f76kv().b[1][1]++;
  }
  var requiredTier = (cov_1tiq4f76kv().s[8]++, Object.values(SUBSCRIPTION_TIERS).find(function (tier) {
    cov_1tiq4f76kv().f[1]++;
    cov_1tiq4f76kv().s[9]++;
    return (cov_1tiq4f76kv().b[2][0]++, tier.features.includes(featureId)) && (cov_1tiq4f76kv().b[2][1]++, tier.id !== 'free');
  }));
  cov_1tiq4f76kv().s[10]++;
  var handleUpgrade = function handleUpgrade() {
    cov_1tiq4f76kv().f[2]++;
    cov_1tiq4f76kv().s[11]++;
    setShowUpgradeModal(false);
    cov_1tiq4f76kv().s[12]++;
    setShowPricingModal(true);
  };
  cov_1tiq4f76kv().s[13]++;
  var handleStartTrial = function () {
    var _ref7 = _asyncToGenerator(function* () {
      cov_1tiq4f76kv().f[3]++;
      cov_1tiq4f76kv().s[14]++;
      if (!requiredTier) {
        cov_1tiq4f76kv().b[3][0]++;
        cov_1tiq4f76kv().s[15]++;
        return;
      } else {
        cov_1tiq4f76kv().b[3][1]++;
      }
      cov_1tiq4f76kv().s[16]++;
      try {
        var _ref8 = (cov_1tiq4f76kv().s[17]++, yield paymentService.startFreeTrial(requiredTier.id)),
          subscription = _ref8.subscription,
          error = _ref8.error;
        cov_1tiq4f76kv().s[18]++;
        if (error) {
          cov_1tiq4f76kv().b[4][0]++;
          cov_1tiq4f76kv().s[19]++;
          Alert.alert('Trial Error', error);
        } else {
          cov_1tiq4f76kv().b[4][1]++;
          cov_1tiq4f76kv().s[20]++;
          Alert.alert('Trial Started!', `Your 14-day free trial has started. You now have access to ${feature == null ? void 0 : feature.name}!`);
          cov_1tiq4f76kv().s[21]++;
          setShowUpgradeModal(false);
        }
      } catch (error) {
        cov_1tiq4f76kv().s[22]++;
        Alert.alert('Error', 'Failed to start trial. Please try again.');
      }
    });
    return function handleStartTrial() {
      return _ref7.apply(this, arguments);
    };
  }();
  cov_1tiq4f76kv().s[23]++;
  if (fallback) {
    cov_1tiq4f76kv().b[5][0]++;
    cov_1tiq4f76kv().s[24]++;
    return _jsx(_Fragment, {
      children: fallback
    });
  } else {
    cov_1tiq4f76kv().b[5][1]++;
  }
  cov_1tiq4f76kv().s[25]++;
  if (!showUpgradePrompt) {
    cov_1tiq4f76kv().b[6][0]++;
    cov_1tiq4f76kv().s[26]++;
    return null;
  } else {
    cov_1tiq4f76kv().b[6][1]++;
  }
  cov_1tiq4f76kv().s[27]++;
  return _jsxs(_Fragment, {
    children: [_jsxs(TouchableOpacity, {
      style: styles.lockedFeature,
      onPress: function onPress() {
        cov_1tiq4f76kv().f[4]++;
        cov_1tiq4f76kv().s[28]++;
        return setShowUpgradeModal(true);
      },
      activeOpacity: 0.8,
      children: [_jsx(BlurView, {
        intensity: 20,
        style: styles.blurOverlay,
        children: _jsxs(View, {
          style: styles.lockContainer,
          children: [_jsx(LinearGradient, {
            colors: ['#3B82F6', '#8B5CF6'],
            style: styles.lockIcon,
            start: {
              x: 0,
              y: 0
            },
            end: {
              x: 1,
              y: 1
            },
            children: _jsx(Ionicons, {
              name: "lock-closed",
              size: 24,
              color: "#FFFFFF"
            })
          }), _jsx(Text, {
            style: styles.lockTitle,
            children: "Premium Feature"
          }), _jsx(Text, {
            style: styles.lockMessage,
            children: (cov_1tiq4f76kv().b[7][0]++, customMessage) || (cov_1tiq4f76kv().b[7][1]++, `Unlock ${feature == null ? void 0 : feature.name} with a premium subscription`)
          }), _jsxs(TouchableOpacity, {
            style: styles.unlockButton,
            onPress: function onPress() {
              cov_1tiq4f76kv().f[5]++;
              cov_1tiq4f76kv().s[29]++;
              return setShowUpgradeModal(true);
            },
            children: [_jsx(Text, {
              style: styles.unlockButtonText,
              children: "Unlock Now"
            }), _jsx(Ionicons, {
              name: "arrow-forward",
              size: 16,
              color: "#FFFFFF"
            })]
          })]
        })
      }), _jsx(View, {
        style: styles.previewContent,
        children: children
      })]
    }), _jsx(Modal, {
      visible: showUpgradeModal,
      animationType: "slide",
      presentationStyle: "pageSheet",
      onRequestClose: function onRequestClose() {
        cov_1tiq4f76kv().f[6]++;
        cov_1tiq4f76kv().s[30]++;
        return setShowUpgradeModal(false);
      },
      children: _jsxs(View, {
        style: styles.modalContainer,
        children: [_jsxs(View, {
          style: styles.modalHeader,
          children: [_jsx(TouchableOpacity, {
            style: styles.closeButton,
            onPress: function onPress() {
              cov_1tiq4f76kv().f[7]++;
              cov_1tiq4f76kv().s[31]++;
              return setShowUpgradeModal(false);
            },
            children: _jsx(Ionicons, {
              name: "close",
              size: 24,
              color: "#6B7280"
            })
          }), _jsx(Text, {
            style: styles.modalTitle,
            children: "Unlock Premium Feature"
          })]
        }), _jsxs(ScrollView, {
          style: styles.modalContent,
          showsVerticalScrollIndicator: false,
          children: [(cov_1tiq4f76kv().b[8][0]++, feature) && (cov_1tiq4f76kv().b[8][1]++, _jsxs(View, {
            style: styles.featureHighlight,
            children: [_jsx(LinearGradient, {
              colors: (cov_1tiq4f76kv().b[9][0]++, requiredTier == null ? void 0 : requiredTier.gradient) || (cov_1tiq4f76kv().b[9][1]++, ['#3B82F6', '#8B5CF6']),
              style: styles.featureIcon,
              start: {
                x: 0,
                y: 0
              },
              end: {
                x: 1,
                y: 1
              },
              children: _jsx(Ionicons, {
                name: "star",
                size: 32,
                color: "#FFFFFF"
              })
            }), _jsx(Text, {
              style: styles.featureName,
              children: feature.name
            }), _jsx(Text, {
              style: styles.featureDescription,
              children: feature.description
            })]
          })), (cov_1tiq4f76kv().b[10][0]++, requiredTier) && (cov_1tiq4f76kv().b[10][1]++, _jsxs(View, {
            style: styles.tierInfo,
            children: [_jsxs(Text, {
              style: styles.tierTitle,
              children: ["Available in ", requiredTier.name, " Plan"]
            }), _jsxs(Text, {
              style: styles.tierPrice,
              children: ["Starting at ", formatPrice(requiredTier.price_monthly), "/month"]
            }), _jsx(Text, {
              style: styles.tierDescription,
              children: requiredTier.description
            })]
          })), _jsxs(View, {
            style: styles.benefitsList,
            children: [_jsx(Text, {
              style: styles.benefitsTitle,
              children: "What you'll get:"
            }), requiredTier == null ? void 0 : requiredTier.features.slice(0, 5).map(function (fId) {
              cov_1tiq4f76kv().f[8]++;
              var f = (cov_1tiq4f76kv().s[32]++, SUBSCRIPTION_FEATURES[fId]);
              cov_1tiq4f76kv().s[33]++;
              return f ? (cov_1tiq4f76kv().b[11][0]++, _jsxs(View, {
                style: styles.benefitItem,
                children: [_jsx(Ionicons, {
                  name: "checkmark-circle",
                  size: 20,
                  color: "#10B981"
                }), _jsx(Text, {
                  style: styles.benefitText,
                  children: f.name
                })]
              }, fId)) : (cov_1tiq4f76kv().b[11][1]++, null);
            }), (cov_1tiq4f76kv().b[12][0]++, requiredTier) && (cov_1tiq4f76kv().b[12][1]++, requiredTier.features.length > 5) && (cov_1tiq4f76kv().b[12][2]++, _jsxs(Text, {
              style: styles.moreBenefits,
              children: ["+", requiredTier.features.length - 5, " more premium features"]
            }))]
          }), _jsxs(View, {
            style: styles.actionButtons,
            children: [_jsx(TouchableOpacity, {
              style: styles.trialButton,
              onPress: handleStartTrial,
              children: _jsx(Text, {
                style: styles.trialButtonText,
                children: "Start 14-Day Free Trial"
              })
            }), _jsx(TouchableOpacity, {
              style: styles.upgradeButton,
              onPress: handleUpgrade,
              children: _jsx(Text, {
                style: styles.upgradeButtonText,
                children: "View All Plans"
              })
            })]
          }), _jsxs(View, {
            style: styles.guarantee,
            children: [_jsx(Ionicons, {
              name: "shield-checkmark",
              size: 20,
              color: "#10B981"
            }), _jsx(Text, {
              style: styles.guaranteeText,
              children: "Cancel anytime. No questions asked."
            })]
          })]
        })]
      })
    }), _jsx(Modal, {
      visible: showPricingModal,
      animationType: "slide",
      presentationStyle: "fullScreen",
      onRequestClose: function onRequestClose() {
        cov_1tiq4f76kv().f[9]++;
        cov_1tiq4f76kv().s[34]++;
        return setShowPricingModal(false);
      },
      children: _jsxs(View, {
        style: styles.pricingModalContainer,
        children: [_jsxs(View, {
          style: styles.pricingModalHeader,
          children: [_jsx(TouchableOpacity, {
            style: styles.closeButton,
            onPress: function onPress() {
              cov_1tiq4f76kv().f[10]++;
              cov_1tiq4f76kv().s[35]++;
              return setShowPricingModal(false);
            },
            children: _jsx(Ionicons, {
              name: "close",
              size: 24,
              color: "#6B7280"
            })
          }), _jsx(Text, {
            style: styles.pricingModalTitle,
            children: "Choose Your Plan"
          })]
        }), _jsx(PricingPlans, {
          currentTier: currentTier.id,
          onSelectPlan: function onSelectPlan(tier, billingCycle) {
            cov_1tiq4f76kv().f[11]++;
            cov_1tiq4f76kv().s[36]++;
            setShowPricingModal(false);
            cov_1tiq4f76kv().s[37]++;
            Alert.alert('Plan Selected', `You selected ${tier.name} (${billingCycle}). This would open the payment flow.`);
          }
        })]
      })
    })]
  });
}
var styles = (cov_1tiq4f76kv().s[38]++, StyleSheet.create({
  lockedFeature: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: 12
  },
  blurOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)'
  },
  previewContent: {
    opacity: 0.3
  },
  lockContainer: {
    alignItems: 'center',
    padding: 24
  },
  lockIcon: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16
  },
  lockTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center'
  },
  lockMessage: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center',
    marginBottom: 20,
    paddingHorizontal: 20
  },
  unlockButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3B82F6',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 25,
    gap: 8
  },
  unlockButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF'
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    position: 'relative'
  },
  closeButton: {
    position: 'absolute',
    left: 16,
    padding: 8
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827'
  },
  modalContent: {
    flex: 1,
    padding: 24
  },
  featureHighlight: {
    alignItems: 'center',
    marginBottom: 32
  },
  featureIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16
  },
  featureName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center'
  },
  featureDescription: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24
  },
  tierInfo: {
    backgroundColor: '#F9FAFB',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    alignItems: 'center'
  },
  tierTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4
  },
  tierPrice: {
    fontSize: 16,
    fontWeight: '500',
    color: '#3B82F6',
    marginBottom: 8
  },
  tierDescription: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center'
  },
  benefitsList: {
    marginBottom: 32
  },
  benefitsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16
  },
  benefitItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  benefitText: {
    fontSize: 16,
    color: '#374151',
    marginLeft: 12,
    flex: 1
  },
  moreBenefits: {
    fontSize: 14,
    color: '#6B7280',
    fontStyle: 'italic',
    marginTop: 8,
    marginLeft: 32
  },
  actionButtons: {
    gap: 12,
    marginBottom: 24
  },
  trialButton: {
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center'
  },
  trialButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF'
  },
  upgradeButton: {
    backgroundColor: 'transparent',
    borderWidth: 2,
    borderColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center'
  },
  upgradeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#3B82F6'
  },
  guarantee: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8
  },
  guaranteeText: {
    fontSize: 14,
    color: '#6B7280'
  },
  pricingModalContainer: {
    flex: 1,
    backgroundColor: '#FFFFFF'
  },
  pricingModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    position: 'relative'
  },
  pricingModalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827'
  }
}));
export default PremiumGate;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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