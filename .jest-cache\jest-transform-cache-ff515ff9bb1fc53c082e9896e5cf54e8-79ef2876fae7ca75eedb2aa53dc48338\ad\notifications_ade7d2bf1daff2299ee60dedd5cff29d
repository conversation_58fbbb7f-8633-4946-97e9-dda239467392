8834b7022a7d21b3df36127992b8aef0
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_xqa7bwrht() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\settings\\notifications.tsx";
  var hash = "93e8b408278e4bbbcca4e3d87f65d0fbe0927e43";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\settings\\notifications.tsx",
    statementMap: {
      "0": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 34,
          column: 1
        }
      },
      "1": {
        start: {
          line: 46,
          column: 32
        },
        end: {
          line: 46,
          column: 47
        }
      },
      "2": {
        start: {
          line: 47,
          column: 34
        },
        end: {
          line: 96,
          column: 4
        }
      },
      "3": {
        start: {
          line: 98,
          column: 24
        },
        end: {
          line: 106,
          column: 3
        }
      },
      "4": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 105,
          column: 6
        }
      },
      "5": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 104,
          column: 7
        }
      },
      "6": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 103,
          column: 19
        }
      },
      "7": {
        start: {
          line: 108,
          column: 21
        },
        end: {
          line: 124,
          column: 3
        }
      },
      "8": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 21
        }
      },
      "9": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 123,
          column: 5
        }
      },
      "10": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 112,
          column: 62
        }
      },
      "11": {
        start: {
          line: 112,
          column: 35
        },
        end: {
          line: 112,
          column: 60
        }
      },
      "12": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 118,
          column: 8
        }
      },
      "13": {
        start: {
          line: 117,
          column: 38
        },
        end: {
          line: 117,
          column: 51
        }
      },
      "14": {
        start: {
          line: 120,
          column: 6
        },
        end: {
          line: 120,
          column: 76
        }
      },
      "15": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 122,
          column: 24
        }
      },
      "16": {
        start: {
          line: 126,
          column: 37
        },
        end: {
          line: 156,
          column: 3
        }
      },
      "17": {
        start: {
          line: 127,
          column: 29
        },
        end: {
          line: 127,
          column: 74
        }
      },
      "18": {
        start: {
          line: 127,
          column: 50
        },
        end: {
          line: 127,
          column: 73
        }
      },
      "19": {
        start: {
          line: 129,
          column: 4
        },
        end: {
          line: 155,
          column: 6
        }
      },
      "20": {
        start: {
          line: 133,
          column: 32
        },
        end: {
          line: 133,
          column: 44
        }
      },
      "21": {
        start: {
          line: 134,
          column: 10
        },
        end: {
          line: 152,
          column: 12
        }
      },
      "22": {
        start: {
          line: 147,
          column: 37
        },
        end: {
          line: 147,
          column: 62
        }
      },
      "23": {
        start: {
          line: 158,
          column: 2
        },
        end: {
          line: 227,
          column: 4
        }
      },
      "24": {
        start: {
          line: 166,
          column: 43
        },
        end: {
          line: 166,
          column: 56
        }
      },
      "25": {
        start: {
          line: 197,
          column: 18
        },
        end: {
          line: 197,
          column: 80
        }
      },
      "26": {
        start: {
          line: 197,
          column: 38
        },
        end: {
          line: 197,
          column: 78
        }
      },
      "27": {
        start: {
          line: 197,
          column: 53
        },
        end: {
          line: 197,
          column: 76
        }
      },
      "28": {
        start: {
          line: 205,
          column: 18
        },
        end: {
          line: 205,
          column: 81
        }
      },
      "29": {
        start: {
          line: 205,
          column: 38
        },
        end: {
          line: 205,
          column: 79
        }
      },
      "30": {
        start: {
          line: 205,
          column: 53
        },
        end: {
          line: 205,
          column: 77
        }
      },
      "31": {
        start: {
          line: 230,
          column: 15
        },
        end: {
          line: 366,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "NotificationsScreen",
        decl: {
          start: {
            line: 45,
            column: 24
          },
          end: {
            line: 45,
            column: 43
          }
        },
        loc: {
          start: {
            line: 45,
            column: 46
          },
          end: {
            line: 228,
            column: 1
          }
        },
        line: 45
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 98,
            column: 24
          },
          end: {
            line: 98,
            column: 25
          }
        },
        loc: {
          start: {
            line: 98,
            column: 40
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 98
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 99,
            column: 16
          },
          end: {
            line: 99,
            column: 17
          }
        },
        loc: {
          start: {
            line: 100,
            column: 6
          },
          end: {
            line: 104,
            column: 7
          }
        },
        line: 100
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 100,
            column: 15
          },
          end: {
            line: 100,
            column: 16
          }
        },
        loc: {
          start: {
            line: 101,
            column: 8
          },
          end: {
            line: 103,
            column: 19
          }
        },
        line: 101
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 108,
            column: 21
          },
          end: {
            line: 108,
            column: 22
          }
        },
        loc: {
          start: {
            line: 108,
            column: 33
          },
          end: {
            line: 124,
            column: 3
          }
        },
        line: 108
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 112,
            column: 24
          },
          end: {
            line: 112,
            column: 25
          }
        },
        loc: {
          start: {
            line: 112,
            column: 35
          },
          end: {
            line: 112,
            column: 60
          }
        },
        line: 112
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 117,
            column: 32
          },
          end: {
            line: 117,
            column: 33
          }
        },
        loc: {
          start: {
            line: 117,
            column: 38
          },
          end: {
            line: 117,
            column: 51
          }
        },
        line: 117
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 126,
            column: 37
          },
          end: {
            line: 126,
            column: 38
          }
        },
        loc: {
          start: {
            line: 126,
            column: 74
          },
          end: {
            line: 156,
            column: 3
          }
        },
        line: 126
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 127,
            column: 45
          },
          end: {
            line: 127,
            column: 46
          }
        },
        loc: {
          start: {
            line: 127,
            column: 50
          },
          end: {
            line: 127,
            column: 73
          }
        },
        line: 127
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 132,
            column: 30
          },
          end: {
            line: 132,
            column: 31
          }
        },
        loc: {
          start: {
            line: 132,
            column: 43
          },
          end: {
            line: 153,
            column: 9
          }
        },
        line: 132
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 147,
            column: 31
          },
          end: {
            line: 147,
            column: 32
          }
        },
        loc: {
          start: {
            line: 147,
            column: 37
          },
          end: {
            line: 147,
            column: 62
          }
        },
        line: 147
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 166,
            column: 37
          },
          end: {
            line: 166,
            column: 38
          }
        },
        loc: {
          start: {
            line: 166,
            column: 43
          },
          end: {
            line: 166,
            column: 56
          }
        },
        line: 166
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 196,
            column: 25
          },
          end: {
            line: 196,
            column: 26
          }
        },
        loc: {
          start: {
            line: 196,
            column: 31
          },
          end: {
            line: 198,
            column: 17
          }
        },
        line: 196
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 197,
            column: 30
          },
          end: {
            line: 197,
            column: 31
          }
        },
        loc: {
          start: {
            line: 197,
            column: 38
          },
          end: {
            line: 197,
            column: 78
          }
        },
        line: 197
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 197,
            column: 47
          },
          end: {
            line: 197,
            column: 48
          }
        },
        loc: {
          start: {
            line: 197,
            column: 53
          },
          end: {
            line: 197,
            column: 76
          }
        },
        line: 197
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 204,
            column: 25
          },
          end: {
            line: 204,
            column: 26
          }
        },
        loc: {
          start: {
            line: 204,
            column: 31
          },
          end: {
            line: 206,
            column: 17
          }
        },
        line: 204
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 205,
            column: 30
          },
          end: {
            line: 205,
            column: 31
          }
        },
        loc: {
          start: {
            line: 205,
            column: 38
          },
          end: {
            line: 205,
            column: 79
          }
        },
        line: 205
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 205,
            column: 47
          },
          end: {
            line: 205,
            column: 48
          }
        },
        loc: {
          start: {
            line: 205,
            column: 53
          },
          end: {
            line: 205,
            column: 77
          }
        },
        line: 205
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 101,
            column: 8
          },
          end: {
            line: 103,
            column: 19
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 102,
            column: 12
          },
          end: {
            line: 102,
            column: 53
          }
        }, {
          start: {
            line: 103,
            column: 12
          },
          end: {
            line: 103,
            column: 19
          }
        }],
        line: 101
      },
      "1": {
        loc: {
          start: {
            line: 149,
            column: 28
          },
          end: {
            line: 149,
            column: 72
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 149,
            column: 46
          },
          end: {
            line: 149,
            column: 58
          }
        }, {
          start: {
            line: 149,
            column: 61
          },
          end: {
            line: 149,
            column: 72
          }
        }],
        line: 149
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "93e8b408278e4bbbcca4e3d87f65d0fbe0927e43"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_xqa7bwrht = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_xqa7bwrht();
import React, { useState } from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView, Switch, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import Card from "../../components/ui/Card";
import Button from "../../components/ui/Button";
import { ArrowLeft, Bell, MessageSquare, Trophy, Calendar, Zap, Users, Settings } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_xqa7bwrht().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
export default function NotificationsScreen() {
  cov_xqa7bwrht().f[0]++;
  var _ref = (cov_xqa7bwrht().s[1]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    loading = _ref2[0],
    setLoading = _ref2[1];
  var _ref3 = (cov_xqa7bwrht().s[2]++, useState([{
      id: 'training_reminders',
      title: 'Training Reminders',
      description: 'Get notified about scheduled training sessions',
      icon: Calendar,
      enabled: true,
      category: 'training'
    }, {
      id: 'ai_coaching_tips',
      title: 'AI Coaching Tips',
      description: 'Receive personalized coaching advice',
      icon: Zap,
      enabled: true,
      category: 'training'
    }, {
      id: 'match_analysis',
      title: 'Match Analysis Ready',
      description: 'When your video analysis is complete',
      icon: Trophy,
      enabled: true,
      category: 'training'
    }, {
      id: 'social_interactions',
      title: 'Social Interactions',
      description: 'Comments, likes, and friend requests',
      icon: Users,
      enabled: false,
      category: 'social'
    }, {
      id: 'messages',
      title: 'Messages',
      description: 'Direct messages from other players',
      icon: MessageSquare,
      enabled: true,
      category: 'social'
    }, {
      id: 'system_updates',
      title: 'App Updates',
      description: 'New features and important announcements',
      icon: Settings,
      enabled: true,
      category: 'system'
    }])),
    _ref4 = _slicedToArray(_ref3, 2),
    settings = _ref4[0],
    setSettings = _ref4[1];
  cov_xqa7bwrht().s[3]++;
  var toggleSetting = function toggleSetting(id) {
    cov_xqa7bwrht().f[1]++;
    cov_xqa7bwrht().s[4]++;
    setSettings(function (prev) {
      cov_xqa7bwrht().f[2]++;
      cov_xqa7bwrht().s[5]++;
      return prev.map(function (setting) {
        cov_xqa7bwrht().f[3]++;
        cov_xqa7bwrht().s[6]++;
        return setting.id === id ? (cov_xqa7bwrht().b[0][0]++, Object.assign({}, setting, {
          enabled: !setting.enabled
        })) : (cov_xqa7bwrht().b[0][1]++, setting);
      });
    });
  };
  cov_xqa7bwrht().s[7]++;
  var handleSave = function () {
    var _ref5 = _asyncToGenerator(function* () {
      cov_xqa7bwrht().f[4]++;
      cov_xqa7bwrht().s[8]++;
      setLoading(true);
      cov_xqa7bwrht().s[9]++;
      try {
        cov_xqa7bwrht().s[10]++;
        yield new Promise(function (resolve) {
          cov_xqa7bwrht().f[5]++;
          cov_xqa7bwrht().s[11]++;
          return setTimeout(resolve, 1000);
        });
        cov_xqa7bwrht().s[12]++;
        Alert.alert('Success', 'Notification preferences saved successfully!', [{
          text: 'OK',
          onPress: function onPress() {
            cov_xqa7bwrht().f[6]++;
            cov_xqa7bwrht().s[13]++;
            return router.back();
          }
        }]);
      } catch (error) {
        cov_xqa7bwrht().s[14]++;
        Alert.alert('Error', 'Failed to save preferences. Please try again.');
      } finally {
        cov_xqa7bwrht().s[15]++;
        setLoading(false);
      }
    });
    return function handleSave() {
      return _ref5.apply(this, arguments);
    };
  }();
  cov_xqa7bwrht().s[16]++;
  var renderNotificationCategory = function renderNotificationCategory(category, title) {
    cov_xqa7bwrht().f[7]++;
    var categorySettings = (cov_xqa7bwrht().s[17]++, settings.filter(function (s) {
      cov_xqa7bwrht().f[8]++;
      cov_xqa7bwrht().s[18]++;
      return s.category === category;
    }));
    cov_xqa7bwrht().s[19]++;
    return _jsxs(Card, {
      style: styles.categoryCard,
      children: [_jsx(Text, {
        style: styles.categoryTitle,
        children: title
      }), categorySettings.map(function (setting) {
        cov_xqa7bwrht().f[9]++;
        var IconComponent = (cov_xqa7bwrht().s[20]++, setting.icon);
        cov_xqa7bwrht().s[21]++;
        return _jsxs(View, {
          style: styles.settingRow,
          children: [_jsxs(View, {
            style: styles.settingInfo,
            children: [_jsx(View, {
              style: styles.settingIcon,
              children: _jsx(IconComponent, {
                size: 20,
                color: colors.primary
              })
            }), _jsxs(View, {
              style: styles.settingText,
              children: [_jsx(Text, {
                style: styles.settingTitle,
                children: setting.title
              }), _jsx(Text, {
                style: styles.settingDescription,
                children: setting.description
              })]
            })]
          }), _jsx(Switch, {
            value: setting.enabled,
            onValueChange: function onValueChange() {
              cov_xqa7bwrht().f[10]++;
              cov_xqa7bwrht().s[22]++;
              return toggleSetting(setting.id);
            },
            trackColor: {
              false: colors.lightGray,
              true: colors.primary
            },
            thumbColor: setting.enabled ? (cov_xqa7bwrht().b[1][0]++, colors.white) : (cov_xqa7bwrht().b[1][1]++, colors.gray)
          })]
        }, setting.id);
      })]
    }, category);
  };
  cov_xqa7bwrht().s[23]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(LinearGradient, {
      colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
      style: styles.gradient,
      children: [_jsxs(View, {
        style: styles.header,
        children: [_jsx(TouchableOpacity, {
          onPress: function onPress() {
            cov_xqa7bwrht().f[11]++;
            cov_xqa7bwrht().s[24]++;
            return router.back();
          },
          style: styles.backButton,
          children: _jsx(ArrowLeft, {
            size: 24,
            color: "white"
          })
        }), _jsx(Text, {
          style: styles.title,
          children: "Notifications"
        }), _jsx(View, {
          style: styles.placeholder
        })]
      }), _jsxs(ScrollView, {
        style: styles.content,
        showsVerticalScrollIndicator: false,
        children: [_jsxs(Card, {
          style: styles.overviewCard,
          children: [_jsxs(View, {
            style: styles.overviewHeader,
            children: [_jsx(Bell, {
              size: 24,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.overviewTitle,
              children: "Notification Preferences"
            })]
          }), _jsx(Text, {
            style: styles.overviewText,
            children: "Customize which notifications you'd like to receive to stay updated on your tennis journey."
          })]
        }), renderNotificationCategory('training', 'Training & Coaching'), renderNotificationCategory('social', 'Social & Community'), renderNotificationCategory('system', 'System & Updates'), _jsxs(Card, {
          style: styles.actionsCard,
          children: [_jsx(Text, {
            style: styles.actionsTitle,
            children: "Quick Actions"
          }), _jsxs(View, {
            style: styles.actionButtons,
            children: [_jsx(TouchableOpacity, {
              style: styles.actionButton,
              onPress: function onPress() {
                cov_xqa7bwrht().f[12]++;
                cov_xqa7bwrht().s[25]++;
                setSettings(function (prev) {
                  cov_xqa7bwrht().f[13]++;
                  cov_xqa7bwrht().s[26]++;
                  return prev.map(function (s) {
                    cov_xqa7bwrht().f[14]++;
                    cov_xqa7bwrht().s[27]++;
                    return Object.assign({}, s, {
                      enabled: true
                    });
                  });
                });
              },
              children: _jsx(Text, {
                style: styles.actionButtonText,
                children: "Enable All"
              })
            }), _jsx(TouchableOpacity, {
              style: [styles.actionButton, styles.actionButtonSecondary],
              onPress: function onPress() {
                cov_xqa7bwrht().f[15]++;
                cov_xqa7bwrht().s[28]++;
                setSettings(function (prev) {
                  cov_xqa7bwrht().f[16]++;
                  cov_xqa7bwrht().s[29]++;
                  return prev.map(function (s) {
                    cov_xqa7bwrht().f[17]++;
                    cov_xqa7bwrht().s[30]++;
                    return Object.assign({}, s, {
                      enabled: false
                    });
                  });
                });
              },
              children: _jsx(Text, {
                style: [styles.actionButtonText, styles.actionButtonTextSecondary],
                children: "Disable All"
              })
            })]
          })]
        }), _jsx(View, {
          style: styles.saveContainer,
          children: _jsx(Button, {
            title: "Save Preferences",
            onPress: handleSave,
            loading: loading,
            style: styles.saveButton
          })
        })]
      })]
    })
  });
}
var styles = (cov_xqa7bwrht().s[31]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10
  },
  backButton: {
    padding: 8
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: colors.white
  },
  placeholder: {
    width: 40
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  overviewCard: {
    padding: 20,
    marginBottom: 16
  },
  overviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12
  },
  overviewTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginLeft: 12
  },
  overviewText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 20
  },
  categoryCard: {
    padding: 20,
    marginBottom: 16
  },
  categoryTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 16
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12
  },
  settingText: {
    flex: 1
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: colors.dark,
    marginBottom: 2
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  actionsCard: {
    padding: 20,
    marginBottom: 16
  },
  actionsTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 16
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    backgroundColor: colors.primary,
    alignItems: 'center'
  },
  actionButtonSecondary: {
    backgroundColor: colors.lightGray
  },
  actionButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.white
  },
  actionButtonTextSecondary: {
    color: colors.dark
  },
  saveContainer: {
    paddingVertical: 20
  },
  saveButton: {
    width: '100%'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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