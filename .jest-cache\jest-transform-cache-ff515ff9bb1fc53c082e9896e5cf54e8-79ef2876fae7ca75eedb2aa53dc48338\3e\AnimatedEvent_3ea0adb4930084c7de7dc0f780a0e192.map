{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireWildcard", "default", "_interopRequireDefault", "exports", "__esModule", "AnimatedEvent", "attachNativeEvent", "_AnimatedValue", "_NativeAnimatedHelper", "_invariant", "__DEV__", "process", "env", "NODE_ENV", "viewRef", "eventName", "arg<PERSON><PERSON><PERSON>", "eventMappings", "traverse", "value", "path", "__makeNative", "push", "nativeEventPath", "animatedValueTag", "__getNativeTag", "_key", "concat", "nativeEvent", "for<PERSON>ach", "mapping", "API", "addAnimatedEventToView", "detach", "removeAnimatedEventFromView", "validateMapping", "args", "validate", "recMapping", "recEvt", "key", "mappingKey", "length", "idx", "config", "_listeners", "_argMapping", "console", "warn", "useNativeDriver", "listener", "__addListener", "_callListeners", "bind", "_attachedEvent", "__isNative", "shouldUseNativeDriver", "callback", "__removeListener", "filter", "__attach", "__detach", "viewTag", "__<PERSON><PERSON><PERSON><PERSON>", "_this", "_validatedMapping", "_len", "arguments", "Array", "_key2", "apply", "validatedMapping", "_len2", "_key3", "setValue", "_len3", "_key4"], "sources": ["AnimatedEvent.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.AnimatedEvent = void 0;\nexports.attachNativeEvent = attachNativeEvent;\nvar _AnimatedValue = _interopRequireDefault(require(\"./nodes/AnimatedValue\"));\nvar _NativeAnimatedHelper = _interopRequireWildcard(require(\"./NativeAnimatedHelper\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nfunction attachNativeEvent(viewRef, eventName, argMapping) {\n  // Find animated values in `argMapping` and create an array representing their\n  // key path inside the `nativeEvent` object. Ex.: ['contentOffset', 'x'].\n  var eventMappings = [];\n  var traverse = (value, path) => {\n    if (value instanceof _AnimatedValue.default) {\n      value.__makeNative();\n      eventMappings.push({\n        nativeEventPath: path,\n        animatedValueTag: value.__getNativeTag()\n      });\n    } else if (typeof value === 'object') {\n      for (var _key in value) {\n        traverse(value[_key], path.concat(_key));\n      }\n    }\n  };\n  (0, _invariant.default)(argMapping[0] && argMapping[0].nativeEvent, 'Native driven events only support animated values contained inside `nativeEvent`.');\n\n  // Assume that the event containing `nativeEvent` is always the first argument.\n  traverse(argMapping[0].nativeEvent, []);\n  if (viewRef != null) {\n    eventMappings.forEach(mapping => {\n      _NativeAnimatedHelper.default.API.addAnimatedEventToView(viewRef, eventName, mapping);\n    });\n  }\n  return {\n    detach() {\n      if (viewRef != null) {\n        eventMappings.forEach(mapping => {\n          _NativeAnimatedHelper.default.API.removeAnimatedEventFromView(viewRef, eventName,\n          // $FlowFixMe[incompatible-call]\n          mapping.animatedValueTag);\n        });\n      }\n    }\n  };\n}\nfunction validateMapping(argMapping, args) {\n  var validate = (recMapping, recEvt, key) => {\n    if (recMapping instanceof _AnimatedValue.default) {\n      (0, _invariant.default)(typeof recEvt === 'number', 'Bad mapping of event key ' + key + ', should be number but got ' + typeof recEvt);\n      return;\n    }\n    if (typeof recEvt === 'number') {\n      (0, _invariant.default)(recMapping instanceof _AnimatedValue.default, 'Bad mapping of type ' + typeof recMapping + ' for key ' + key + ', event value must map to AnimatedValue');\n      return;\n    }\n    (0, _invariant.default)(typeof recMapping === 'object', 'Bad mapping of type ' + typeof recMapping + ' for key ' + key);\n    (0, _invariant.default)(typeof recEvt === 'object', 'Bad event of type ' + typeof recEvt + ' for key ' + key);\n    for (var mappingKey in recMapping) {\n      validate(recMapping[mappingKey], recEvt[mappingKey], mappingKey);\n    }\n  };\n  (0, _invariant.default)(args.length >= argMapping.length, 'Event has less arguments than mapping');\n  argMapping.forEach((mapping, idx) => {\n    validate(mapping, args[idx], 'arg' + idx);\n  });\n}\nclass AnimatedEvent {\n  constructor(argMapping, config) {\n    this._listeners = [];\n    this._argMapping = argMapping;\n    if (config == null) {\n      console.warn('Animated.event now requires a second argument for options');\n      config = {\n        useNativeDriver: false\n      };\n    }\n    if (config.listener) {\n      this.__addListener(config.listener);\n    }\n    this._callListeners = this._callListeners.bind(this);\n    this._attachedEvent = null;\n    this.__isNative = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);\n  }\n  __addListener(callback) {\n    this._listeners.push(callback);\n  }\n  __removeListener(callback) {\n    this._listeners = this._listeners.filter(listener => listener !== callback);\n  }\n  __attach(viewRef, eventName) {\n    (0, _invariant.default)(this.__isNative, 'Only native driven events need to be attached.');\n    this._attachedEvent = attachNativeEvent(viewRef, eventName, this._argMapping);\n  }\n  __detach(viewTag, eventName) {\n    (0, _invariant.default)(this.__isNative, 'Only native driven events need to be detached.');\n    this._attachedEvent && this._attachedEvent.detach();\n  }\n  __getHandler() {\n    var _this = this;\n    if (this.__isNative) {\n      if (__DEV__) {\n        var _validatedMapping = false;\n        return function () {\n          for (var _len = arguments.length, args = new Array(_len), _key2 = 0; _key2 < _len; _key2++) {\n            args[_key2] = arguments[_key2];\n          }\n          if (!_validatedMapping) {\n            validateMapping(_this._argMapping, args);\n            _validatedMapping = true;\n          }\n          _this._callListeners(...args);\n        };\n      } else {\n        return this._callListeners;\n      }\n    }\n    var validatedMapping = false;\n    return function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key3 = 0; _key3 < _len2; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      if (__DEV__ && !validatedMapping) {\n        validateMapping(_this._argMapping, args);\n        validatedMapping = true;\n      }\n      var traverse = (recMapping, recEvt, key) => {\n        if (recMapping instanceof _AnimatedValue.default) {\n          if (typeof recEvt === 'number') {\n            recMapping.setValue(recEvt);\n          }\n        } else if (typeof recMapping === 'object') {\n          for (var mappingKey in recMapping) {\n            /* $FlowFixMe(>=0.120.0) This comment suppresses an error found\n             * when Flow v0.120 was deployed. To see the error, delete this\n             * comment and run Flow. */\n            traverse(recMapping[mappingKey], recEvt[mappingKey], mappingKey);\n          }\n        }\n      };\n      _this._argMapping.forEach((mapping, idx) => {\n        traverse(mapping, args[idx], 'arg' + idx);\n      });\n      _this._callListeners(...args);\n    };\n  }\n  _callListeners() {\n    for (var _len3 = arguments.length, args = new Array(_len3), _key4 = 0; _key4 < _len3; _key4++) {\n      args[_key4] = arguments[_key4];\n    }\n    this._listeners.forEach(listener => listener(...args));\n  }\n}\nexports.AnimatedEvent = AnimatedEvent;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,uBAAuB,GAAGH,OAAO,CAAC,+CAA+C,CAAC,CAACI,OAAO;AAC9F,IAAIC,sBAAsB,GAAGL,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,aAAa,GAAG,KAAK,CAAC;AAC9BF,OAAO,CAACG,iBAAiB,GAAGA,iBAAiB;AAC7C,IAAIC,cAAc,GAAGL,sBAAsB,CAACL,OAAO,wBAAwB,CAAC,CAAC;AAC7E,IAAIW,qBAAqB,GAAGR,uBAAuB,CAACH,OAAO,yBAAyB,CAAC,CAAC;AACtF,IAAIY,UAAU,GAAGP,sBAAsB,CAACL,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIa,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACnD,SAASP,iBAAiBA,CAACQ,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAE;EAGzD,IAAIC,aAAa,GAAG,EAAE;EACtB,IAAIC,SAAQ,GAAG,SAAXA,QAAQA,CAAIC,KAAK,EAAEC,IAAI,EAAK;IAC9B,IAAID,KAAK,YAAYZ,cAAc,CAACN,OAAO,EAAE;MAC3CkB,KAAK,CAACE,YAAY,CAAC,CAAC;MACpBJ,aAAa,CAACK,IAAI,CAAC;QACjBC,eAAe,EAAEH,IAAI;QACrBI,gBAAgB,EAAEL,KAAK,CAACM,cAAc,CAAC;MACzC,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAON,KAAK,KAAK,QAAQ,EAAE;MACpC,KAAK,IAAIO,IAAI,IAAIP,KAAK,EAAE;QACtBD,SAAQ,CAACC,KAAK,CAACO,IAAI,CAAC,EAAEN,IAAI,CAACO,MAAM,CAACD,IAAI,CAAC,CAAC;MAC1C;IACF;EACF,CAAC;EACD,CAAC,CAAC,EAAEjB,UAAU,CAACR,OAAO,EAAEe,UAAU,CAAC,CAAC,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,CAACY,WAAW,EAAE,mFAAmF,CAAC;EAGxJV,SAAQ,CAACF,UAAU,CAAC,CAAC,CAAC,CAACY,WAAW,EAAE,EAAE,CAAC;EACvC,IAAId,OAAO,IAAI,IAAI,EAAE;IACnBG,aAAa,CAACY,OAAO,CAAC,UAAAC,OAAO,EAAI;MAC/BtB,qBAAqB,CAACP,OAAO,CAAC8B,GAAG,CAACC,sBAAsB,CAAClB,OAAO,EAAEC,SAAS,EAAEe,OAAO,CAAC;IACvF,CAAC,CAAC;EACJ;EACA,OAAO;IACLG,MAAM,WAANA,MAAMA,CAAA,EAAG;MACP,IAAInB,OAAO,IAAI,IAAI,EAAE;QACnBG,aAAa,CAACY,OAAO,CAAC,UAAAC,OAAO,EAAI;UAC/BtB,qBAAqB,CAACP,OAAO,CAAC8B,GAAG,CAACG,2BAA2B,CAACpB,OAAO,EAAEC,SAAS,EAEhFe,OAAO,CAACN,gBAAgB,CAAC;QAC3B,CAAC,CAAC;MACJ;IACF;EACF,CAAC;AACH;AACA,SAASW,eAAeA,CAACnB,UAAU,EAAEoB,IAAI,EAAE;EACzC,IAAIC,SAAQ,GAAG,SAAXA,QAAQA,CAAIC,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAK;IAC1C,IAAIF,UAAU,YAAY/B,cAAc,CAACN,OAAO,EAAE;MAChD,CAAC,CAAC,EAAEQ,UAAU,CAACR,OAAO,EAAE,OAAOsC,MAAM,KAAK,QAAQ,EAAE,2BAA2B,GAAGC,GAAG,GAAG,6BAA6B,GAAG,OAAOD,MAAM,CAAC;MACtI;IACF;IACA,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,CAAC,CAAC,EAAE9B,UAAU,CAACR,OAAO,EAAEqC,UAAU,YAAY/B,cAAc,CAACN,OAAO,EAAE,sBAAsB,GAAG,OAAOqC,UAAU,GAAG,WAAW,GAAGE,GAAG,GAAG,yCAAyC,CAAC;MACjL;IACF;IACA,CAAC,CAAC,EAAE/B,UAAU,CAACR,OAAO,EAAE,OAAOqC,UAAU,KAAK,QAAQ,EAAE,sBAAsB,GAAG,OAAOA,UAAU,GAAG,WAAW,GAAGE,GAAG,CAAC;IACvH,CAAC,CAAC,EAAE/B,UAAU,CAACR,OAAO,EAAE,OAAOsC,MAAM,KAAK,QAAQ,EAAE,oBAAoB,GAAG,OAAOA,MAAM,GAAG,WAAW,GAAGC,GAAG,CAAC;IAC7G,KAAK,IAAIC,UAAU,IAAIH,UAAU,EAAE;MACjCD,SAAQ,CAACC,UAAU,CAACG,UAAU,CAAC,EAAEF,MAAM,CAACE,UAAU,CAAC,EAAEA,UAAU,CAAC;IAClE;EACF,CAAC;EACD,CAAC,CAAC,EAAEhC,UAAU,CAACR,OAAO,EAAEmC,IAAI,CAACM,MAAM,IAAI1B,UAAU,CAAC0B,MAAM,EAAE,uCAAuC,CAAC;EAClG1B,UAAU,CAACa,OAAO,CAAC,UAACC,OAAO,EAAEa,GAAG,EAAK;IACnCN,SAAQ,CAACP,OAAO,EAAEM,IAAI,CAACO,GAAG,CAAC,EAAE,KAAK,GAAGA,GAAG,CAAC;EAC3C,CAAC,CAAC;AACJ;AAAC,IACKtC,aAAa;EACjB,SAAAA,cAAYW,UAAU,EAAE4B,MAAM,EAAE;IAAA,IAAA9C,gBAAA,CAAAG,OAAA,QAAAI,aAAA;IAC9B,IAAI,CAACwC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,WAAW,GAAG9B,UAAU;IAC7B,IAAI4B,MAAM,IAAI,IAAI,EAAE;MAClBG,OAAO,CAACC,IAAI,CAAC,2DAA2D,CAAC;MACzEJ,MAAM,GAAG;QACPK,eAAe,EAAE;MACnB,CAAC;IACH;IACA,IAAIL,MAAM,CAACM,QAAQ,EAAE;MACnB,IAAI,CAACC,aAAa,CAACP,MAAM,CAACM,QAAQ,CAAC;IACrC;IACA,IAAI,CAACE,cAAc,GAAG,IAAI,CAACA,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IACpD,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC,EAAE/C,qBAAqB,CAACgD,qBAAqB,EAAEZ,MAAM,CAAC;EAC5E;EAAC,WAAA7C,aAAA,CAAAE,OAAA,EAAAI,aAAA;IAAAmC,GAAA;IAAArB,KAAA,EACD,SAAAgC,aAAaA,CAACM,QAAQ,EAAE;MACtB,IAAI,CAACZ,UAAU,CAACvB,IAAI,CAACmC,QAAQ,CAAC;IAChC;EAAC;IAAAjB,GAAA;IAAArB,KAAA,EACD,SAAAuC,gBAAgBA,CAACD,QAAQ,EAAE;MACzB,IAAI,CAACZ,UAAU,GAAG,IAAI,CAACA,UAAU,CAACc,MAAM,CAAC,UAAAT,QAAQ;QAAA,OAAIA,QAAQ,KAAKO,QAAQ;MAAA,EAAC;IAC7E;EAAC;IAAAjB,GAAA;IAAArB,KAAA,EACD,SAAAyC,QAAQA,CAAC9C,OAAO,EAAEC,SAAS,EAAE;MAC3B,CAAC,CAAC,EAAEN,UAAU,CAACR,OAAO,EAAE,IAAI,CAACsD,UAAU,EAAE,gDAAgD,CAAC;MAC1F,IAAI,CAACD,cAAc,GAAGhD,iBAAiB,CAACQ,OAAO,EAAEC,SAAS,EAAE,IAAI,CAAC+B,WAAW,CAAC;IAC/E;EAAC;IAAAN,GAAA;IAAArB,KAAA,EACD,SAAA0C,QAAQA,CAACC,OAAO,EAAE/C,SAAS,EAAE;MAC3B,CAAC,CAAC,EAAEN,UAAU,CAACR,OAAO,EAAE,IAAI,CAACsD,UAAU,EAAE,gDAAgD,CAAC;MAC1F,IAAI,CAACD,cAAc,IAAI,IAAI,CAACA,cAAc,CAACrB,MAAM,CAAC,CAAC;IACrD;EAAC;IAAAO,GAAA;IAAArB,KAAA,EACD,SAAA4C,YAAYA,CAAA,EAAG;MACb,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAI,IAAI,CAACT,UAAU,EAAE;QACnB,IAAI7C,OAAO,EAAE;UACX,IAAIuD,iBAAiB,GAAG,KAAK;UAC7B,OAAO,YAAY;YACjB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACzB,MAAM,EAAEN,IAAI,GAAG,IAAIgC,KAAK,CAACF,IAAI,CAAC,EAAEG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGH,IAAI,EAAEG,KAAK,EAAE,EAAE;cAC1FjC,IAAI,CAACiC,KAAK,CAAC,GAAGF,SAAS,CAACE,KAAK,CAAC;YAChC;YACA,IAAI,CAACJ,iBAAiB,EAAE;cACtB9B,eAAe,CAAC6B,KAAK,CAAClB,WAAW,EAAEV,IAAI,CAAC;cACxC6B,iBAAiB,GAAG,IAAI;YAC1B;YACAD,KAAK,CAACZ,cAAc,CAAAkB,KAAA,CAApBN,KAAK,EAAmB5B,IAAI,CAAC;UAC/B,CAAC;QACH,CAAC,MAAM;UACL,OAAO,IAAI,CAACgB,cAAc;QAC5B;MACF;MACA,IAAImB,gBAAgB,GAAG,KAAK;MAC5B,OAAO,YAAY;QACjB,KAAK,IAAIC,KAAK,GAAGL,SAAS,CAACzB,MAAM,EAAEN,IAAI,GAAG,IAAIgC,KAAK,CAACI,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;UAC7FrC,IAAI,CAACqC,KAAK,CAAC,GAAGN,SAAS,CAACM,KAAK,CAAC;QAChC;QACA,IAAI/D,OAAO,IAAI,CAAC6D,gBAAgB,EAAE;UAChCpC,eAAe,CAAC6B,KAAK,CAAClB,WAAW,EAAEV,IAAI,CAAC;UACxCmC,gBAAgB,GAAG,IAAI;QACzB;QACA,IAAIrD,UAAQ,GAAG,SAAXA,QAAQA,CAAIoB,UAAU,EAAEC,MAAM,EAAEC,GAAG,EAAK;UAC1C,IAAIF,UAAU,YAAY/B,cAAc,CAACN,OAAO,EAAE;YAChD,IAAI,OAAOsC,MAAM,KAAK,QAAQ,EAAE;cAC9BD,UAAU,CAACoC,QAAQ,CAACnC,MAAM,CAAC;YAC7B;UACF,CAAC,MAAM,IAAI,OAAOD,UAAU,KAAK,QAAQ,EAAE;YACzC,KAAK,IAAIG,UAAU,IAAIH,UAAU,EAAE;cAIjCpB,UAAQ,CAACoB,UAAU,CAACG,UAAU,CAAC,EAAEF,MAAM,CAACE,UAAU,CAAC,EAAEA,UAAU,CAAC;YAClE;UACF;QACF,CAAC;QACDuB,KAAK,CAAClB,WAAW,CAACjB,OAAO,CAAC,UAACC,OAAO,EAAEa,GAAG,EAAK;UAC1CzB,UAAQ,CAACY,OAAO,EAAEM,IAAI,CAACO,GAAG,CAAC,EAAE,KAAK,GAAGA,GAAG,CAAC;QAC3C,CAAC,CAAC;QACFqB,KAAK,CAACZ,cAAc,CAAAkB,KAAA,CAApBN,KAAK,EAAmB5B,IAAI,CAAC;MAC/B,CAAC;IACH;EAAC;IAAAI,GAAA;IAAArB,KAAA,EACD,SAAAiC,cAAcA,CAAA,EAAG;MACf,KAAK,IAAIuB,KAAK,GAAGR,SAAS,CAACzB,MAAM,EAAEN,IAAI,GAAG,IAAIgC,KAAK,CAACO,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FxC,IAAI,CAACwC,KAAK,CAAC,GAAGT,SAAS,CAACS,KAAK,CAAC;MAChC;MACA,IAAI,CAAC/B,UAAU,CAAChB,OAAO,CAAC,UAAAqB,QAAQ;QAAA,OAAIA,QAAQ,CAAAoB,KAAA,SAAIlC,IAAI,CAAC;MAAA,EAAC;IACxD;EAAC;AAAA;AAEHjC,OAAO,CAACE,aAAa,GAAGA,aAAa", "ignoreList": []}