2be1c336e9624d33960c97240aa243c4
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1gdttvm7jx() {
  var path = "C:\\_SaaS\\AceMind\\project\\utils\\performance.ts";
  var hash = "6811c1115e646e31a31b4f25952c7be013f08f7a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\utils\\performance.ts",
    statementMap: {
      "0": {
        start: {
          line: 18,
          column: 53
        },
        end: {
          line: 18,
          column: 62
        }
      },
      "1": {
        start: {
          line: 19,
          column: 64
        },
        end: {
          line: 19,
          column: 66
        }
      },
      "2": {
        start: {
          line: 25,
          column: 22
        },
        end: {
          line: 25,
          column: 32
        }
      },
      "3": {
        start: {
          line: 26,
          column: 4
        },
        end: {
          line: 30,
          column: 7
        }
      },
      "4": {
        start: {
          line: 37,
          column: 19
        },
        end: {
          line: 37,
          column: 41
        }
      },
      "5": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 41,
          column: 5
        }
      },
      "6": {
        start: {
          line: 39,
          column: 6
        },
        end: {
          line: 39,
          column: 61
        }
      },
      "7": {
        start: {
          line: 40,
          column: 6
        },
        end: {
          line: 40,
          column: 18
        }
      },
      "8": {
        start: {
          line: 43,
          column: 20
        },
        end: {
          line: 43,
          column: 30
        }
      },
      "9": {
        start: {
          line: 44,
          column: 21
        },
        end: {
          line: 44,
          column: 47
        }
      },
      "10": {
        start: {
          line: 46,
          column: 48
        },
        end: {
          line: 50,
          column: 5
        }
      },
      "11": {
        start: {
          line: 52,
          column: 4
        },
        end: {
          line: 52,
          column: 44
        }
      },
      "12": {
        start: {
          line: 53,
          column: 4
        },
        end: {
          line: 53,
          column: 42
        }
      },
      "13": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 27
        }
      },
      "14": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 34
        }
      },
      "15": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 45
        }
      },
      "16": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 76,
          column: 25
        }
      },
      "17": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 83,
          column: 34
        }
      },
      "18": {
        start: {
          line: 90,
          column: 18
        },
        end: {
          line: 90,
          column: 50
        }
      },
      "19": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "20": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 38
        }
      },
      "21": {
        start: {
          line: 97,
          column: 4
        },
        end: {
          line: 103,
          column: 7
        }
      },
      "22": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 102,
          column: 7
        }
      },
      "23": {
        start: {
          line: 99,
          column: 8
        },
        end: {
          line: 99,
          column: 25
        }
      },
      "24": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 101,
          column: 63
        }
      },
      "25": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 70
        }
      },
      "26": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 115,
          column: 5
        }
      },
      "27": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 79
        }
      },
      "28": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 122,
          column: 71
        }
      },
      "29": {
        start: {
          line: 129,
          column: 18
        },
        end: {
          line: 129,
          column: 28
        }
      },
      "30": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 140,
          column: 5
        }
      },
      "31": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 136,
          column: 9
        }
      },
      "32": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 32
        }
      },
      "33": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 139,
          column: 16
        }
      },
      "34": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 164,
          column: 7
        }
      },
      "35": {
        start: {
          line: 148,
          column: 19
        },
        end: {
          line: 148,
          column: 20
        }
      },
      "36": {
        start: {
          line: 149,
          column: 20
        },
        end: {
          line: 149,
          column: 30
        }
      },
      "37": {
        start: {
          line: 151,
          column: 25
        },
        end: {
          line: 161,
          column: 7
        }
      },
      "38": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 152,
          column: 17
        }
      },
      "39": {
        start: {
          line: 153,
          column: 24
        },
        end: {
          line: 153,
          column: 42
        }
      },
      "40": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 160,
          column: 9
        }
      },
      "41": {
        start: {
          line: 156,
          column: 10
        },
        end: {
          line: 156,
          column: 44
        }
      },
      "42": {
        start: {
          line: 158,
          column: 22
        },
        end: {
          line: 158,
          column: 47
        }
      },
      "43": {
        start: {
          line: 159,
          column: 10
        },
        end: {
          line: 159,
          column: 23
        }
      },
      "44": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 40
        }
      },
      "45": {
        start: {
          line: 171,
          column: 4
        },
        end: {
          line: 173,
          column: 5
        }
      },
      "46": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 70
        }
      },
      "47": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 174,
          column: 13
        }
      },
      "48": {
        start: {
          line: 186,
          column: 24
        },
        end: {
          line: 186,
          column: 26
        }
      },
      "49": {
        start: {
          line: 187,
          column: 20
        },
        end: {
          line: 187,
          column: 40
        }
      },
      "50": {
        start: {
          line: 190,
          column: 24
        },
        end: {
          line: 192,
          column: 5
        }
      },
      "51": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 191,
          column: 64
        }
      },
      "52": {
        start: {
          line: 194,
          column: 4
        },
        end: {
          line: 201,
          column: 5
        }
      },
      "53": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 200,
          column: 9
        }
      },
      "54": {
        start: {
          line: 204,
          column: 24
        },
        end: {
          line: 204,
          column: 45
        }
      },
      "55": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 212,
          column: 5
        }
      },
      "56": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 211,
          column: 9
        }
      },
      "57": {
        start: {
          line: 214,
          column: 4
        },
        end: {
          line: 214,
          column: 23
        }
      },
      "58": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 221,
          column: 74
        }
      },
      "59": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 225,
          column: 5
        }
      },
      "60": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 75
        }
      },
      "61": {
        start: {
          line: 232,
          column: 4
        },
        end: {
          line: 232,
          column: 64
        }
      },
      "62": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 240,
          column: 16
        }
      },
      "63": {
        start: {
          line: 244,
          column: 34
        },
        end: {
          line: 244,
          column: 58
        }
      },
      "64": {
        start: {
          line: 250,
          column: 2
        },
        end: {
          line: 272,
          column: 4
        }
      },
      "65": {
        start: {
          line: 251,
          column: 27
        },
        end: {
          line: 251,
          column: 43
        }
      },
      "66": {
        start: {
          line: 252,
          column: 23
        },
        end: {
          line: 252,
          column: 74
        }
      },
      "67": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 269,
          column: 6
        }
      },
      "68": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 259,
          column: 9
        }
      },
      "69": {
        start: {
          line: 261,
          column: 6
        },
        end: {
          line: 268,
          column: 7
        }
      },
      "70": {
        start: {
          line: 262,
          column: 23
        },
        end: {
          line: 262,
          column: 61
        }
      },
      "71": {
        start: {
          line: 263,
          column: 8
        },
        end: {
          line: 263,
          column: 43
        }
      },
      "72": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 264,
          column: 22
        }
      },
      "73": {
        start: {
          line: 266,
          column: 8
        },
        end: {
          line: 266,
          column: 43
        }
      },
      "74": {
        start: {
          line: 267,
          column: 8
        },
        end: {
          line: 267,
          column: 20
        }
      },
      "75": {
        start: {
          line: 271,
          column: 4
        },
        end: {
          line: 271,
          column: 22
        }
      },
      "76": {
        start: {
          line: 283,
          column: 39
        },
        end: {
          line: 283,
          column: 43
        }
      },
      "77": {
        start: {
          line: 285,
          column: 2
        },
        end: {
          line: 297,
          column: 4
        }
      },
      "78": {
        start: {
          line: 286,
          column: 18
        },
        end: {
          line: 289,
          column: 5
        }
      },
      "79": {
        start: {
          line: 287,
          column: 6
        },
        end: {
          line: 287,
          column: 21
        }
      },
      "80": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 36
        }
      },
      "81": {
        start: {
          line: 288,
          column: 22
        },
        end: {
          line: 288,
          column: 36
        }
      },
      "82": {
        start: {
          line: 291,
          column: 20
        },
        end: {
          line: 291,
          column: 41
        }
      },
      "83": {
        start: {
          line: 293,
          column: 4
        },
        end: {
          line: 293,
          column: 39
        }
      },
      "84": {
        start: {
          line: 293,
          column: 17
        },
        end: {
          line: 293,
          column: 39
        }
      },
      "85": {
        start: {
          line: 294,
          column: 4
        },
        end: {
          line: 294,
          column: 45
        }
      },
      "86": {
        start: {
          line: 296,
          column: 4
        },
        end: {
          line: 296,
          column: 31
        }
      },
      "87": {
        start: {
          line: 296,
          column: 17
        },
        end: {
          line: 296,
          column: 31
        }
      },
      "88": {
        start: {
          line: 309,
          column: 2
        },
        end: {
          line: 315,
          column: 4
        }
      },
      "89": {
        start: {
          line: 310,
          column: 4
        },
        end: {
          line: 314,
          column: 5
        }
      },
      "90": {
        start: {
          line: 311,
          column: 6
        },
        end: {
          line: 311,
          column: 29
        }
      },
      "91": {
        start: {
          line: 312,
          column: 6
        },
        end: {
          line: 312,
          column: 24
        }
      },
      "92": {
        start: {
          line: 313,
          column: 6
        },
        end: {
          line: 313,
          column: 50
        }
      },
      "93": {
        start: {
          line: 313,
          column: 23
        },
        end: {
          line: 313,
          column: 41
        }
      },
      "94": {
        start: {
          line: 322,
          column: 2
        },
        end: {
          line: 326,
          column: 5
        }
      },
      "95": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 325,
          column: 7
        }
      },
      "96": {
        start: {
          line: 324,
          column: 6
        },
        end: {
          line: 324,
          column: 22
        }
      },
      "97": {
        start: {
          line: 333,
          column: 23
        },
        end: {
          line: 333,
          column: 25
        }
      },
      "98": {
        start: {
          line: 334,
          column: 43
        },
        end: {
          line: 334,
          column: 47
        }
      },
      "99": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 344,
          column: 31
        }
      },
      "100": {
        start: {
          line: 345,
          column: 4
        },
        end: {
          line: 345,
          column: 31
        }
      },
      "101": {
        start: {
          line: 346,
          column: 4
        },
        end: {
          line: 346,
          column: 23
        }
      },
      "102": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 350,
          column: 26
        }
      },
      "103": {
        start: {
          line: 352,
          column: 4
        },
        end: {
          line: 356,
          column: 5
        }
      },
      "104": {
        start: {
          line: 353,
          column: 6
        },
        end: {
          line: 353,
          column: 19
        }
      },
      "105": {
        start: {
          line: 355,
          column: 6
        },
        end: {
          line: 355,
          column: 27
        }
      },
      "106": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 363,
          column: 5
        }
      },
      "107": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 361,
          column: 33
        }
      },
      "108": {
        start: {
          line: 362,
          column: 6
        },
        end: {
          line: 362,
          column: 26
        }
      },
      "109": {
        start: {
          line: 365,
          column: 4
        },
        end: {
          line: 369,
          column: 5
        }
      },
      "110": {
        start: {
          line: 366,
          column: 20
        },
        end: {
          line: 366,
          column: 35
        }
      },
      "111": {
        start: {
          line: 367,
          column: 6
        },
        end: {
          line: 367,
          column: 22
        }
      },
      "112": {
        start: {
          line: 368,
          column: 6
        },
        end: {
          line: 368,
          column: 28
        }
      },
      "113": {
        start: {
          line: 373,
          column: 4
        },
        end: {
          line: 373,
          column: 29
        }
      },
      "114": {
        start: {
          line: 373,
          column: 22
        },
        end: {
          line: 373,
          column: 29
        }
      },
      "115": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 377,
          column: 26
        }
      },
      "116": {
        start: {
          line: 376,
          column: 6
        },
        end: {
          line: 376,
          column: 19
        }
      },
      "117": {
        start: {
          line: 386,
          column: 48
        },
        end: {
          line: 386,
          column: 50
        }
      },
      "118": {
        start: {
          line: 389,
          column: 4
        },
        end: {
          line: 391,
          column: 5
        }
      },
      "119": {
        start: {
          line: 390,
          column: 6
        },
        end: {
          line: 390,
          column: 51
        }
      },
      "120": {
        start: {
          line: 392,
          column: 4
        },
        end: {
          line: 392,
          column: 34
        }
      },
      "121": {
        start: {
          line: 396,
          column: 4
        },
        end: {
          line: 407,
          column: 5
        }
      },
      "122": {
        start: {
          line: 397,
          column: 6
        },
        end: {
          line: 406,
          column: 19
        }
      },
      "123": {
        start: {
          line: 398,
          column: 23
        },
        end: {
          line: 398,
          column: 50
        }
      },
      "124": {
        start: {
          line: 399,
          column: 22
        },
        end: {
          line: 404,
          column: 9
        }
      },
      "125": {
        start: {
          line: 405,
          column: 8
        },
        end: {
          line: 405,
          column: 36
        }
      },
      "126": {
        start: {
          line: 411,
          column: 4
        },
        end: {
          line: 411,
          column: 34
        }
      },
      "127": {
        start: {
          line: 415,
          column: 18
        },
        end: {
          line: 415,
          column: 50
        }
      },
      "128": {
        start: {
          line: 416,
          column: 4
        },
        end: {
          line: 418,
          column: 5
        }
      },
      "129": {
        start: {
          line: 417,
          column: 6
        },
        end: {
          line: 417,
          column: 38
        }
      },
      "130": {
        start: {
          line: 422,
          column: 4
        },
        end: {
          line: 428,
          column: 7
        }
      },
      "131": {
        start: {
          line: 423,
          column: 6
        },
        end: {
          line: 427,
          column: 7
        }
      },
      "132": {
        start: {
          line: 424,
          column: 8
        },
        end: {
          line: 424,
          column: 24
        }
      },
      "133": {
        start: {
          line: 426,
          column: 8
        },
        end: {
          line: 426,
          column: 58
        }
      },
      "134": {
        start: {
          line: 435,
          column: 33
        },
        end: {
          line: 492,
          column: 1
        }
      },
      "135": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 448,
          column: 44
        }
      },
      "136": {
        start: {
          line: 448,
          column: 33
        },
        end: {
          line: 448,
          column: 44
        }
      },
      "137": {
        start: {
          line: 450,
          column: 61
        },
        end: {
          line: 450,
          column: 68
        }
      },
      "138": {
        start: {
          line: 451,
          column: 19
        },
        end: {
          line: 451,
          column: 40
        }
      },
      "139": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 453,
          column: 52
        }
      },
      "140": {
        start: {
          line: 453,
          column: 15
        },
        end: {
          line: 453,
          column: 52
        }
      },
      "141": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 454,
          column: 54
        }
      },
      "142": {
        start: {
          line: 454,
          column: 16
        },
        end: {
          line: 454,
          column: 54
        }
      },
      "143": {
        start: {
          line: 455,
          column: 4
        },
        end: {
          line: 455,
          column: 43
        }
      },
      "144": {
        start: {
          line: 456,
          column: 4
        },
        end: {
          line: 456,
          column: 31
        }
      },
      "145": {
        start: {
          line: 457,
          column: 4
        },
        end: {
          line: 457,
          column: 36
        }
      },
      "146": {
        start: {
          line: 459,
          column: 22
        },
        end: {
          line: 459,
          column: 51
        }
      },
      "147": {
        start: {
          line: 460,
          column: 4
        },
        end: {
          line: 460,
          column: 52
        }
      },
      "148": {
        start: {
          line: 472,
          column: 24
        },
        end: {
          line: 472,
          column: 54
        }
      },
      "149": {
        start: {
          line: 474,
          column: 16
        },
        end: {
          line: 474,
          column: 29
        }
      },
      "150": {
        start: {
          line: 475,
          column: 17
        },
        end: {
          line: 475,
          column: 31
        }
      },
      "151": {
        start: {
          line: 477,
          column: 4
        },
        end: {
          line: 480,
          column: 5
        }
      },
      "152": {
        start: {
          line: 478,
          column: 6
        },
        end: {
          line: 478,
          column: 23
        }
      },
      "153": {
        start: {
          line: 479,
          column: 6
        },
        end: {
          line: 479,
          column: 35
        }
      },
      "154": {
        start: {
          line: 482,
          column: 4
        },
        end: {
          line: 485,
          column: 5
        }
      },
      "155": {
        start: {
          line: 483,
          column: 6
        },
        end: {
          line: 483,
          column: 25
        }
      },
      "156": {
        start: {
          line: 484,
          column: 6
        },
        end: {
          line: 484,
          column: 35
        }
      },
      "157": {
        start: {
          line: 487,
          column: 4
        },
        end: {
          line: 490,
          column: 6
        }
      },
      "158": {
        start: {
          line: 499,
          column: 39
        },
        end: {
          line: 499,
          column: 48
        }
      },
      "159": {
        start: {
          line: 500,
          column: 37
        },
        end: {
          line: 500,
          column: 39
        }
      },
      "160": {
        start: {
          line: 501,
          column: 30
        },
        end: {
          line: 501,
          column: 33
        }
      },
      "161": {
        start: {
          line: 504,
          column: 4
        },
        end: {
          line: 506,
          column: 5
        }
      },
      "162": {
        start: {
          line: 505,
          column: 6
        },
        end: {
          line: 505,
          column: 75
        }
      },
      "163": {
        start: {
          line: 507,
          column: 4
        },
        end: {
          line: 507,
          column: 46
        }
      },
      "164": {
        start: {
          line: 511,
          column: 4
        },
        end: {
          line: 517,
          column: 7
        }
      },
      "165": {
        start: {
          line: 521,
          column: 20
        },
        end: {
          line: 521,
          column: 41
        }
      },
      "166": {
        start: {
          line: 522,
          column: 4
        },
        end: {
          line: 522,
          column: 25
        }
      },
      "167": {
        start: {
          line: 522,
          column: 18
        },
        end: {
          line: 522,
          column: 25
        }
      },
      "168": {
        start: {
          line: 524,
          column: 20
        },
        end: {
          line: 524,
          column: 30
        }
      },
      "169": {
        start: {
          line: 525,
          column: 21
        },
        end: {
          line: 525,
          column: 48
        }
      },
      "170": {
        start: {
          line: 527,
          column: 29
        },
        end: {
          line: 533,
          column: 5
        }
      },
      "171": {
        start: {
          line: 535,
          column: 4
        },
        end: {
          line: 535,
          column: 53
        }
      },
      "172": {
        start: {
          line: 536,
          column: 4
        },
        end: {
          line: 538,
          column: 5
        }
      },
      "173": {
        start: {
          line: 537,
          column: 6
        },
        end: {
          line: 537,
          column: 35
        }
      },
      "174": {
        start: {
          line: 540,
          column: 4
        },
        end: {
          line: 540,
          column: 29
        }
      },
      "175": {
        start: {
          line: 543,
          column: 4
        },
        end: {
          line: 545,
          column: 5
        }
      },
      "176": {
        start: {
          line: 544,
          column: 6
        },
        end: {
          line: 544,
          column: 78
        }
      },
      "177": {
        start: {
          line: 554,
          column: 18
        },
        end: {
          line: 554,
          column: 47
        }
      },
      "178": {
        start: {
          line: 555,
          column: 4
        },
        end: {
          line: 557,
          column: 5
        }
      },
      "179": {
        start: {
          line: 556,
          column: 6
        },
        end: {
          line: 556,
          column: 94
        }
      },
      "180": {
        start: {
          line: 559,
          column: 22
        },
        end: {
          line: 559,
          column: 88
        }
      },
      "181": {
        start: {
          line: 559,
          column: 66
        },
        end: {
          line: 559,
          column: 84
        }
      },
      "182": {
        start: {
          line: 560,
          column: 19
        },
        end: {
          line: 560,
          column: 81
        }
      },
      "183": {
        start: {
          line: 560,
          column: 56
        },
        end: {
          line: 560,
          column: 73
        }
      },
      "184": {
        start: {
          line: 561,
          column: 17
        },
        end: {
          line: 561,
          column: 81
        }
      },
      "185": {
        start: {
          line: 561,
          column: 54
        },
        end: {
          line: 561,
          column: 73
        }
      },
      "186": {
        start: {
          line: 563,
          column: 4
        },
        end: {
          line: 568,
          column: 6
        }
      },
      "187": {
        start: {
          line: 577,
          column: 47
        },
        end: {
          line: 577,
          column: 56
        }
      },
      "188": {
        start: {
          line: 578,
          column: 44
        },
        end: {
          line: 578,
          column: 53
        }
      },
      "189": {
        start: {
          line: 581,
          column: 4
        },
        end: {
          line: 583,
          column: 5
        }
      },
      "190": {
        start: {
          line: 582,
          column: 6
        },
        end: {
          line: 582,
          column: 79
        }
      },
      "191": {
        start: {
          line: 584,
          column: 4
        },
        end: {
          line: 584,
          column: 48
        }
      },
      "192": {
        start: {
          line: 588,
          column: 4
        },
        end: {
          line: 588,
          column: 51
        }
      },
      "193": {
        start: {
          line: 592,
          column: 18
        },
        end: {
          line: 592,
          column: 59
        }
      },
      "194": {
        start: {
          line: 593,
          column: 4
        },
        end: {
          line: 593,
          column: 27
        }
      },
      "195": {
        start: {
          line: 596,
          column: 4
        },
        end: {
          line: 598,
          column: 5
        }
      },
      "196": {
        start: {
          line: 597,
          column: 6
        },
        end: {
          line: 597,
          column: 20
        }
      },
      "197": {
        start: {
          line: 600,
          column: 4
        },
        end: {
          line: 600,
          column: 47
        }
      },
      "198": {
        start: {
          line: 603,
          column: 4
        },
        end: {
          line: 605,
          column: 5
        }
      },
      "199": {
        start: {
          line: 604,
          column: 6
        },
        end: {
          line: 604,
          column: 73
        }
      },
      "200": {
        start: {
          line: 613,
          column: 18
        },
        end: {
          line: 613,
          column: 59
        }
      },
      "201": {
        start: {
          line: 614,
          column: 4
        },
        end: {
          line: 616,
          column: 5
        }
      },
      "202": {
        start: {
          line: 615,
          column: 6
        },
        end: {
          line: 615,
          column: 72
        }
      },
      "203": {
        start: {
          line: 618,
          column: 18
        },
        end: {
          line: 618,
          column: 60
        }
      },
      "204": {
        start: {
          line: 618,
          column: 46
        },
        end: {
          line: 618,
          column: 56
        }
      },
      "205": {
        start: {
          line: 619,
          column: 16
        },
        end: {
          line: 619,
          column: 34
        }
      },
      "206": {
        start: {
          line: 621,
          column: 4
        },
        end: {
          line: 625,
          column: 6
        }
      },
      "207": {
        start: {
          line: 629,
          column: 39
        },
        end: {
          line: 629,
          column: 41
        }
      },
      "208": {
        start: {
          line: 630,
          column: 4
        },
        end: {
          line: 632,
          column: 5
        }
      },
      "209": {
        start: {
          line: 631,
          column: 6
        },
        end: {
          line: 631,
          column: 67
        }
      },
      "210": {
        start: {
          line: 633,
          column: 4
        },
        end: {
          line: 633,
          column: 17
        }
      },
      "211": {
        start: {
          line: 640,
          column: 30
        },
        end: {
          line: 668,
          column: 1
        }
      },
      "212": {
        start: {
          line: 645,
          column: 4
        },
        end: {
          line: 651,
          column: 5
        }
      },
      "213": {
        start: {
          line: 646,
          column: 6
        },
        end: {
          line: 650,
          column: 9
        }
      },
      "214": {
        start: {
          line: 658,
          column: 4
        },
        end: {
          line: 666,
          column: 5
        }
      },
      "215": {
        start: {
          line: 659,
          column: 18
        },
        end: {
          line: 659,
          column: 49
        }
      },
      "216": {
        start: {
          line: 660,
          column: 20
        },
        end: {
          line: 660,
          column: 65
        }
      },
      "217": {
        start: {
          line: 661,
          column: 6
        },
        end: {
          line: 661,
          column: 46
        }
      },
      "218": {
        start: {
          line: 663,
          column: 6
        },
        end: {
          line: 665,
          column: 7
        }
      },
      "219": {
        start: {
          line: 664,
          column: 8
        },
        end: {
          line: 664,
          column: 74
        }
      },
      "220": {
        start: {
          line: 675,
          column: 36
        },
        end: {
          line: 675,
          column: 45
        }
      },
      "221": {
        start: {
          line: 676,
          column: 49
        },
        end: {
          line: 676,
          column: 58
        }
      },
      "222": {
        start: {
          line: 677,
          column: 46
        },
        end: {
          line: 677,
          column: 55
        }
      },
      "223": {
        start: {
          line: 678,
          column: 20
        },
        end: {
          line: 678,
          column: 23
        }
      },
      "224": {
        start: {
          line: 681,
          column: 4
        },
        end: {
          line: 683,
          column: 5
        }
      },
      "225": {
        start: {
          line: 682,
          column: 6
        },
        end: {
          line: 682,
          column: 51
        }
      },
      "226": {
        start: {
          line: 684,
          column: 4
        },
        end: {
          line: 684,
          column: 34
        }
      },
      "227": {
        start: {
          line: 689,
          column: 4
        },
        end: {
          line: 691,
          column: 5
        }
      },
      "228": {
        start: {
          line: 690,
          column: 6
        },
        end: {
          line: 690,
          column: 21
        }
      },
      "229": {
        start: {
          line: 693,
          column: 4
        },
        end: {
          line: 693,
          column: 31
        }
      },
      "230": {
        start: {
          line: 694,
          column: 4
        },
        end: {
          line: 694,
          column: 52
        }
      },
      "231": {
        start: {
          line: 695,
          column: 4
        },
        end: {
          line: 695,
          column: 34
        }
      },
      "232": {
        start: {
          line: 699,
          column: 27
        },
        end: {
          line: 699,
          column: 56
        }
      },
      "233": {
        start: {
          line: 700,
          column: 4
        },
        end: {
          line: 703,
          column: 5
        }
      },
      "234": {
        start: {
          line: 701,
          column: 6
        },
        end: {
          line: 701,
          column: 23
        }
      },
      "235": {
        start: {
          line: 702,
          column: 6
        },
        end: {
          line: 702,
          column: 18
        }
      },
      "236": {
        start: {
          line: 705,
          column: 24
        },
        end: {
          line: 705,
          column: 55
        }
      },
      "237": {
        start: {
          line: 706,
          column: 4
        },
        end: {
          line: 706,
          column: 48
        }
      },
      "238": {
        start: {
          line: 708,
          column: 4
        },
        end: {
          line: 708,
          column: 31
        }
      },
      "239": {
        start: {
          line: 712,
          column: 4
        },
        end: {
          line: 712,
          column: 27
        }
      },
      "240": {
        start: {
          line: 713,
          column: 4
        },
        end: {
          line: 713,
          column: 37
        }
      },
      "241": {
        start: {
          line: 714,
          column: 4
        },
        end: {
          line: 714,
          column: 34
        }
      },
      "242": {
        start: {
          line: 718,
          column: 4
        },
        end: {
          line: 718,
          column: 23
        }
      },
      "243": {
        start: {
          line: 719,
          column: 4
        },
        end: {
          line: 719,
          column: 33
        }
      },
      "244": {
        start: {
          line: 720,
          column: 4
        },
        end: {
          line: 720,
          column: 30
        }
      },
      "245": {
        start: {
          line: 724,
          column: 16
        },
        end: {
          line: 724,
          column: 26
        }
      },
      "246": {
        start: {
          line: 727,
          column: 4
        },
        end: {
          line: 731,
          column: 5
        }
      },
      "247": {
        start: {
          line: 728,
          column: 6
        },
        end: {
          line: 730,
          column: 7
        }
      },
      "248": {
        start: {
          line: 729,
          column: 8
        },
        end: {
          line: 729,
          column: 25
        }
      },
      "249": {
        start: {
          line: 734,
          column: 4
        },
        end: {
          line: 740,
          column: 5
        }
      },
      "250": {
        start: {
          line: 735,
          column: 29
        },
        end: {
          line: 736,
          column: 38
        }
      },
      "251": {
        start: {
          line: 736,
          column: 32
        },
        end: {
          line: 736,
          column: 37
        }
      },
      "252": {
        start: {
          line: 738,
          column: 23
        },
        end: {
          line: 738,
          column: 78
        }
      },
      "253": {
        start: {
          line: 739,
          column: 6
        },
        end: {
          line: 739,
          column: 52
        }
      },
      "254": {
        start: {
          line: 739,
          column: 34
        },
        end: {
          line: 739,
          column: 50
        }
      },
      "255": {
        start: {
          line: 748,
          column: 24
        },
        end: {
          line: 748,
          column: 101
        }
      },
      "256": {
        start: {
          line: 748,
          column: 86
        },
        end: {
          line: 748,
          column: 97
        }
      },
      "257": {
        start: {
          line: 749,
          column: 25
        },
        end: {
          line: 752,
          column: 26
        }
      },
      "258": {
        start: {
          line: 750,
          column: 30
        },
        end: {
          line: 750,
          column: 35
        }
      },
      "259": {
        start: {
          line: 752,
          column: 22
        },
        end: {
          line: 752,
          column: 25
        }
      },
      "260": {
        start: {
          line: 754,
          column: 4
        },
        end: {
          line: 758,
          column: 6
        }
      },
      "261": {
        start: {
          line: 765,
          column: 40
        },
        end: {
          line: 853,
          column: 1
        }
      },
      "262": {
        start: {
          line: 773,
          column: 20
        },
        end: {
          line: 773,
          column: 61
        }
      },
      "263": {
        start: {
          line: 775,
          column: 4
        },
        end: {
          line: 787,
          column: 7
        }
      },
      "264": {
        start: {
          line: 776,
          column: 24
        },
        end: {
          line: 776,
          column: 34
        }
      },
      "265": {
        start: {
          line: 777,
          column: 24
        },
        end: {
          line: 777,
          column: 42
        }
      },
      "266": {
        start: {
          line: 778,
          column: 23
        },
        end: {
          line: 778,
          column: 45
        }
      },
      "267": {
        start: {
          line: 780,
          column: 6
        },
        end: {
          line: 780,
          column: 40
        }
      },
      "268": {
        start: {
          line: 782,
          column: 6
        },
        end: {
          line: 784,
          column: 7
        }
      },
      "269": {
        start: {
          line: 783,
          column: 8
        },
        end: {
          line: 783,
          column: 81
        }
      },
      "270": {
        start: {
          line: 786,
          column: 6
        },
        end: {
          line: 786,
          column: 23
        }
      },
      "271": {
        start: {
          line: 798,
          column: 22
        },
        end: {
          line: 798,
          column: 46
        }
      },
      "272": {
        start: {
          line: 799,
          column: 23
        },
        end: {
          line: 799,
          column: 74
        }
      },
      "273": {
        start: {
          line: 801,
          column: 4
        },
        end: {
          line: 809,
          column: 6
        }
      },
      "274": {
        start: {
          line: 819,
          column: 4
        },
        end: {
          line: 831,
          column: 7
        }
      },
      "275": {
        start: {
          line: 820,
          column: 6
        },
        end: {
          line: 830,
          column: 7
        }
      },
      "276": {
        start: {
          line: 821,
          column: 8
        },
        end: {
          line: 824,
          column: 11
        }
      },
      "277": {
        start: {
          line: 822,
          column: 10
        },
        end: {
          line: 822,
          column: 46
        }
      },
      "278": {
        start: {
          line: 822,
          column: 36
        },
        end: {
          line: 822,
          column: 44
        }
      },
      "279": {
        start: {
          line: 823,
          column: 10
        },
        end: {
          line: 823,
          column: 20
        }
      },
      "280": {
        start: {
          line: 826,
          column: 8
        },
        end: {
          line: 829,
          column: 18
        }
      },
      "281": {
        start: {
          line: 827,
          column: 10
        },
        end: {
          line: 827,
          column: 46
        }
      },
      "282": {
        start: {
          line: 827,
          column: 36
        },
        end: {
          line: 827,
          column: 44
        }
      },
      "283": {
        start: {
          line: 828,
          column: 10
        },
        end: {
          line: 828,
          column: 20
        }
      },
      "284": {
        start: {
          line: 838,
          column: 23
        },
        end: {
          line: 838,
          column: 47
        }
      },
      "285": {
        start: {
          line: 839,
          column: 25
        },
        end: {
          line: 839,
          column: 59
        }
      },
      "286": {
        start: {
          line: 841,
          column: 4
        },
        end: {
          line: 851,
          column: 6
        }
      },
      "287": {
        start: {
          line: 842,
          column: 49
        },
        end: {
          line: 846,
          column: 7
        }
      },
      "288": {
        start: {
          line: 860,
          column: 25
        },
        end: {
          line: 860,
          column: 30
        }
      },
      "289": {
        start: {
          line: 861,
          column: 35
        },
        end: {
          line: 861,
          column: 37
        }
      },
      "290": {
        start: {
          line: 864,
          column: 4
        },
        end: {
          line: 866,
          column: 5
        }
      },
      "291": {
        start: {
          line: 865,
          column: 6
        },
        end: {
          line: 865,
          column: 73
        }
      },
      "292": {
        start: {
          line: 867,
          column: 4
        },
        end: {
          line: 867,
          column: 45
        }
      },
      "293": {
        start: {
          line: 871,
          column: 4
        },
        end: {
          line: 871,
          column: 34
        }
      },
      "294": {
        start: {
          line: 871,
          column: 27
        },
        end: {
          line: 871,
          column: 34
        }
      },
      "295": {
        start: {
          line: 873,
          column: 4
        },
        end: {
          line: 873,
          column: 29
        }
      },
      "296": {
        start: {
          line: 876,
          column: 4
        },
        end: {
          line: 876,
          column: 50
        }
      },
      "297": {
        start: {
          line: 879,
          column: 4
        },
        end: {
          line: 885,
          column: 7
        }
      },
      "298": {
        start: {
          line: 880,
          column: 6
        },
        end: {
          line: 884,
          column: 9
        }
      },
      "299": {
        start: {
          line: 888,
          column: 4
        },
        end: {
          line: 891,
          column: 14
        }
      },
      "300": {
        start: {
          line: 889,
          column: 6
        },
        end: {
          line: 889,
          column: 21
        }
      },
      "301": {
        start: {
          line: 890,
          column: 6
        },
        end: {
          line: 890,
          column: 28
        }
      },
      "302": {
        start: {
          line: 895,
          column: 4
        },
        end: {
          line: 895,
          column: 30
        }
      },
      "303": {
        start: {
          line: 900,
          column: 4
        },
        end: {
          line: 902,
          column: 5
        }
      },
      "304": {
        start: {
          line: 901,
          column: 6
        },
        end: {
          line: 901,
          column: 63
        }
      },
      "305": {
        start: {
          line: 906,
          column: 4
        },
        end: {
          line: 906,
          column: 25
        }
      },
      "306": {
        start: {
          line: 906,
          column: 18
        },
        end: {
          line: 906,
          column: 25
        }
      },
      "307": {
        start: {
          line: 908,
          column: 25
        },
        end: {
          line: 908,
          column: 82
        }
      },
      "308": {
        start: {
          line: 909,
          column: 27
        },
        end: {
          line: 909,
          column: 82
        }
      },
      "309": {
        start: {
          line: 910,
          column: 23
        },
        end: {
          line: 910,
          column: 61
        }
      },
      "310": {
        start: {
          line: 912,
          column: 19
        },
        end: {
          line: 918,
          column: 5
        }
      },
      "311": {
        start: {
          line: 921,
          column: 4
        },
        end: {
          line: 925,
          column: 5
        }
      },
      "312": {
        start: {
          line: 922,
          column: 6
        },
        end: {
          line: 922,
          column: 79
        }
      },
      "313": {
        start: {
          line: 924,
          column: 6
        },
        end: {
          line: 924,
          column: 65
        }
      },
      "314": {
        start: {
          line: 927,
          column: 4
        },
        end: {
          line: 927,
          column: 47
        }
      },
      "315": {
        start: {
          line: 931,
          column: 4
        },
        end: {
          line: 937,
          column: 5
        }
      },
      "316": {
        start: {
          line: 932,
          column: 21
        },
        end: {
          line: 932,
          column: 69
        }
      },
      "317": {
        start: {
          line: 933,
          column: 6
        },
        end: {
          line: 933,
          column: 48
        }
      },
      "318": {
        start: {
          line: 935,
          column: 6
        },
        end: {
          line: 935,
          column: 69
        }
      },
      "319": {
        start: {
          line: 936,
          column: 6
        },
        end: {
          line: 936,
          column: 16
        }
      },
      "320": {
        start: {
          line: 942,
          column: 0
        },
        end: {
          line: 944,
          column: 1
        }
      },
      "321": {
        start: {
          line: 943,
          column: 2
        },
        end: {
          line: 943,
          column: 59
        }
      },
      "322": {
        start: {
          line: 947,
          column: 30
        },
        end: {
          line: 947,
          column: 69
        }
      },
      "323": {
        start: {
          line: 948,
          column: 32
        },
        end: {
          line: 948,
          column: 73
        }
      },
      "324": {
        start: {
          line: 949,
          column: 29
        },
        end: {
          line: 949,
          column: 56
        }
      },
      "325": {
        start: {
          line: 950,
          column: 40
        },
        end: {
          line: 950,
          column: 78
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 24,
            column: 2
          },
          end: {
            line: 24,
            column: 3
          }
        },
        loc: {
          start: {
            line: 24,
            column: 60
          },
          end: {
            line: 31,
            column: 3
          }
        },
        line: 24
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 36,
            column: 2
          },
          end: {
            line: 36,
            column: 3
          }
        },
        loc: {
          start: {
            line: 36,
            column: 47
          },
          end: {
            line: 56,
            column: 3
          }
        },
        line: 36
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 61,
            column: 2
          },
          end: {
            line: 61,
            column: 3
          }
        },
        loc: {
          start: {
            line: 61,
            column: 58
          },
          end: {
            line: 63,
            column: 3
          }
        },
        line: 61
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 68,
            column: 2
          },
          end: {
            line: 68,
            column: 3
          }
        },
        loc: {
          start: {
            line: 68,
            column: 40
          },
          end: {
            line: 70,
            column: 3
          }
        },
        line: 68
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 75,
            column: 2
          },
          end: {
            line: 75,
            column: 3
          }
        },
        loc: {
          start: {
            line: 75,
            column: 16
          },
          end: {
            line: 77,
            column: 3
          }
        },
        line: 75
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 82,
            column: 2
          },
          end: {
            line: 82,
            column: 3
          }
        },
        loc: {
          start: {
            line: 82,
            column: 68
          },
          end: {
            line: 84,
            column: 3
          }
        },
        line: 82
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 89,
            column: 2
          },
          end: {
            line: 89,
            column: 3
          }
        },
        loc: {
          start: {
            line: 89,
            column: 71
          },
          end: {
            line: 94,
            column: 3
          }
        },
        line: 89
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 96,
            column: 2
          },
          end: {
            line: 96,
            column: 3
          }
        },
        loc: {
          start: {
            line: 96,
            column: 60
          },
          end: {
            line: 104,
            column: 3
          }
        },
        line: 96
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 97,
            column: 27
          },
          end: {
            line: 97,
            column: 28
          }
        },
        loc: {
          start: {
            line: 97,
            column: 39
          },
          end: {
            line: 103,
            column: 5
          }
        },
        line: 97
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 109,
            column: 2
          },
          end: {
            line: 109,
            column: 3
          }
        },
        loc: {
          start: {
            line: 109,
            column: 68
          },
          end: {
            line: 116,
            column: 3
          }
        },
        line: 109
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 121,
            column: 3
          }
        },
        loc: {
          start: {
            line: 121,
            column: 69
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 121
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 128,
            column: 2
          },
          end: {
            line: 128,
            column: 3
          }
        },
        loc: {
          start: {
            line: 128,
            column: 87
          },
          end: {
            line: 141,
            column: 3
          }
        },
        line: 128
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 146,
            column: 2
          },
          end: {
            line: 146,
            column: 3
          }
        },
        loc: {
          start: {
            line: 146,
            column: 61
          },
          end: {
            line: 165,
            column: 3
          }
        },
        line: 146
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 147,
            column: 23
          },
          end: {
            line: 147,
            column: 24
          }
        },
        loc: {
          start: {
            line: 147,
            column: 36
          },
          end: {
            line: 164,
            column: 5
          }
        },
        line: 147
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 151,
            column: 25
          },
          end: {
            line: 151,
            column: 26
          }
        },
        loc: {
          start: {
            line: 151,
            column: 31
          },
          end: {
            line: 161,
            column: 7
          }
        },
        line: 151
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 170,
            column: 2
          },
          end: {
            line: 170,
            column: 3
          }
        },
        loc: {
          start: {
            line: 170,
            column: 27
          },
          end: {
            line: 175,
            column: 3
          }
        },
        line: 170
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 180,
            column: 2
          },
          end: {
            line: 180,
            column: 3
          }
        },
        loc: {
          start: {
            line: 185,
            column: 5
          },
          end: {
            line: 215,
            column: 3
          }
        },
        line: 185
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 190,
            column: 39
          },
          end: {
            line: 190,
            column: 40
          }
        },
        loc: {
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 191,
            column: 64
          }
        },
        line: 191
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 220,
            column: 2
          },
          end: {
            line: 220,
            column: 3
          }
        },
        loc: {
          start: {
            line: 220,
            column: 64
          },
          end: {
            line: 226,
            column: 3
          }
        },
        line: 220
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 231,
            column: 2
          },
          end: {
            line: 231,
            column: 3
          }
        },
        loc: {
          start: {
            line: 231,
            column: 60
          },
          end: {
            line: 233,
            column: 3
          }
        },
        line: 231
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 238,
            column: 2
          },
          end: {
            line: 238,
            column: 3
          }
        },
        loc: {
          start: {
            line: 238,
            column: 28
          },
          end: {
            line: 241,
            column: 3
          }
        },
        line: 238
      },
      "21": {
        name: "measurePerformance",
        decl: {
          start: {
            line: 249,
            column: 16
          },
          end: {
            line: 249,
            column: 34
          }
        },
        loc: {
          start: {
            line: 249,
            column: 50
          },
          end: {
            line: 273,
            column: 1
          }
        },
        line: 249
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 250,
            column: 9
          },
          end: {
            line: 250,
            column: 10
          }
        },
        loc: {
          start: {
            line: 250,
            column: 85
          },
          end: {
            line: 272,
            column: 3
          }
        },
        line: 250
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 254,
            column: 23
          },
          end: {
            line: 254,
            column: 24
          }
        },
        loc: {
          start: {
            line: 254,
            column: 55
          },
          end: {
            line: 269,
            column: 5
          }
        },
        line: 254
      },
      "24": {
        name: "debounce",
        decl: {
          start: {
            line: 278,
            column: 16
          },
          end: {
            line: 278,
            column: 24
          }
        },
        loc: {
          start: {
            line: 282,
            column: 36
          },
          end: {
            line: 298,
            column: 1
          }
        },
        line: 282
      },
      "25": {
        name: "executedFunction",
        decl: {
          start: {
            line: 285,
            column: 18
          },
          end: {
            line: 285,
            column: 34
          }
        },
        loc: {
          start: {
            line: 285,
            column: 59
          },
          end: {
            line: 297,
            column: 3
          }
        },
        line: 285
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 286,
            column: 18
          },
          end: {
            line: 286,
            column: 19
          }
        },
        loc: {
          start: {
            line: 286,
            column: 24
          },
          end: {
            line: 289,
            column: 5
          }
        },
        line: 286
      },
      "27": {
        name: "throttle",
        decl: {
          start: {
            line: 303,
            column: 16
          },
          end: {
            line: 303,
            column: 24
          }
        },
        loc: {
          start: {
            line: 306,
            column: 36
          },
          end: {
            line: 316,
            column: 1
          }
        },
        line: 306
      },
      "28": {
        name: "executedFunction",
        decl: {
          start: {
            line: 309,
            column: 18
          },
          end: {
            line: 309,
            column: 34
          }
        },
        loc: {
          start: {
            line: 309,
            column: 59
          },
          end: {
            line: 315,
            column: 3
          }
        },
        line: 309
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 313,
            column: 17
          },
          end: {
            line: 313,
            column: 18
          }
        },
        loc: {
          start: {
            line: 313,
            column: 23
          },
          end: {
            line: 313,
            column: 41
          }
        },
        line: 313
      },
      "30": {
        name: "runAfterInteractions",
        decl: {
          start: {
            line: 321,
            column: 16
          },
          end: {
            line: 321,
            column: 36
          }
        },
        loc: {
          start: {
            line: 321,
            column: 67
          },
          end: {
            line: 327,
            column: 1
          }
        },
        line: 321
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 322,
            column: 21
          },
          end: {
            line: 322,
            column: 22
          }
        },
        loc: {
          start: {
            line: 322,
            column: 34
          },
          end: {
            line: 326,
            column: 3
          }
        },
        line: 322
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 323,
            column: 44
          },
          end: {
            line: 323,
            column: 45
          }
        },
        loc: {
          start: {
            line: 323,
            column: 50
          },
          end: {
            line: 325,
            column: 5
          }
        },
        line: 323
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 339,
            column: 2
          },
          end: {
            line: 339,
            column: 3
          }
        },
        loc: {
          start: {
            line: 343,
            column: 4
          },
          end: {
            line: 347,
            column: 3
          }
        },
        line: 343
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 349,
            column: 2
          },
          end: {
            line: 349,
            column: 3
          }
        },
        loc: {
          start: {
            line: 349,
            column: 21
          },
          end: {
            line: 357,
            column: 3
          }
        },
        line: 349
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 359,
            column: 2
          },
          end: {
            line: 359,
            column: 3
          }
        },
        loc: {
          start: {
            line: 359,
            column: 16
          },
          end: {
            line: 370,
            column: 3
          }
        },
        line: 359
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 372,
            column: 2
          },
          end: {
            line: 372,
            column: 3
          }
        },
        loc: {
          start: {
            line: 372,
            column: 32
          },
          end: {
            line: 378,
            column: 3
          }
        },
        line: 372
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 375,
            column: 30
          },
          end: {
            line: 375,
            column: 31
          }
        },
        loc: {
          start: {
            line: 375,
            column: 36
          },
          end: {
            line: 377,
            column: 5
          }
        },
        line: 375
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 388,
            column: 2
          },
          end: {
            line: 388,
            column: 3
          }
        },
        loc: {
          start: {
            line: 388,
            column: 38
          },
          end: {
            line: 393,
            column: 3
          }
        },
        line: 388
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 395,
            column: 2
          },
          end: {
            line: 395,
            column: 3
          }
        },
        loc: {
          start: {
            line: 395,
            column: 41
          },
          end: {
            line: 408,
            column: 3
          }
        },
        line: 395
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 397,
            column: 18
          },
          end: {
            line: 397,
            column: 19
          }
        },
        loc: {
          start: {
            line: 397,
            column: 24
          },
          end: {
            line: 406,
            column: 7
          }
        },
        line: 397
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 410,
            column: 2
          },
          end: {
            line: 410,
            column: 3
          }
        },
        loc: {
          start: {
            line: 410,
            column: 52
          },
          end: {
            line: 412,
            column: 3
          }
        },
        line: 410
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 414,
            column: 2
          },
          end: {
            line: 414,
            column: 3
          }
        },
        loc: {
          start: {
            line: 414,
            column: 55
          },
          end: {
            line: 419,
            column: 3
          }
        },
        line: 414
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 421,
            column: 2
          },
          end: {
            line: 421,
            column: 3
          }
        },
        loc: {
          start: {
            line: 421,
            column: 44
          },
          end: {
            line: 429,
            column: 3
          }
        },
        line: 421
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 422,
            column: 27
          },
          end: {
            line: 422,
            column: 28
          }
        },
        loc: {
          start: {
            line: 422,
            column: 39
          },
          end: {
            line: 428,
            column: 5
          }
        },
        line: 422
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 439,
            column: 2
          },
          end: {
            line: 439,
            column: 3
          }
        },
        loc: {
          start: {
            line: 447,
            column: 12
          },
          end: {
            line: 461,
            column: 3
          }
        },
        line: 447
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 466,
            column: 2
          },
          end: {
            line: 466,
            column: 3
          }
        },
        loc: {
          start: {
            line: 471,
            column: 39
          },
          end: {
            line: 491,
            column: 3
          }
        },
        line: 471
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 503,
            column: 2
          },
          end: {
            line: 503,
            column: 3
          }
        },
        loc: {
          start: {
            line: 503,
            column: 50
          },
          end: {
            line: 508,
            column: 3
          }
        },
        line: 503
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 510,
            column: 2
          },
          end: {
            line: 510,
            column: 3
          }
        },
        loc: {
          start: {
            line: 510,
            column: 62
          },
          end: {
            line: 518,
            column: 3
          }
        },
        line: 510
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 520,
            column: 2
          },
          end: {
            line: 520,
            column: 3
          }
        },
        loc: {
          start: {
            line: 520,
            column: 61
          },
          end: {
            line: 546,
            column: 3
          }
        },
        line: 520
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 548,
            column: 2
          },
          end: {
            line: 548,
            column: 3
          }
        },
        loc: {
          start: {
            line: 553,
            column: 4
          },
          end: {
            line: 569,
            column: 3
          }
        },
        line: 553
      },
      "51": {
        name: "(anonymous_51)",
        decl: {
          start: {
            line: 559,
            column: 52
          },
          end: {
            line: 559,
            column: 53
          }
        },
        loc: {
          start: {
            line: 559,
            column: 66
          },
          end: {
            line: 559,
            column: 84
          }
        },
        line: 559
      },
      "52": {
        name: "(anonymous_52)",
        decl: {
          start: {
            line: 560,
            column: 49
          },
          end: {
            line: 560,
            column: 50
          }
        },
        loc: {
          start: {
            line: 560,
            column: 56
          },
          end: {
            line: 560,
            column: 73
          }
        },
        line: 560
      },
      "53": {
        name: "(anonymous_53)",
        decl: {
          start: {
            line: 561,
            column: 47
          },
          end: {
            line: 561,
            column: 48
          }
        },
        loc: {
          start: {
            line: 561,
            column: 54
          },
          end: {
            line: 561,
            column: 73
          }
        },
        line: 561
      },
      "54": {
        name: "(anonymous_54)",
        decl: {
          start: {
            line: 580,
            column: 2
          },
          end: {
            line: 580,
            column: 3
          }
        },
        loc: {
          start: {
            line: 580,
            column: 52
          },
          end: {
            line: 585,
            column: 3
          }
        },
        line: 580
      },
      "55": {
        name: "(anonymous_55)",
        decl: {
          start: {
            line: 587,
            column: 2
          },
          end: {
            line: 587,
            column: 3
          }
        },
        loc: {
          start: {
            line: 587,
            column: 42
          },
          end: {
            line: 589,
            column: 3
          }
        },
        line: 587
      },
      "56": {
        name: "(anonymous_56)",
        decl: {
          start: {
            line: 591,
            column: 2
          },
          end: {
            line: 591,
            column: 3
          }
        },
        loc: {
          start: {
            line: 591,
            column: 63
          },
          end: {
            line: 606,
            column: 3
          }
        },
        line: 591
      },
      "57": {
        name: "(anonymous_57)",
        decl: {
          start: {
            line: 608,
            column: 2
          },
          end: {
            line: 608,
            column: 3
          }
        },
        loc: {
          start: {
            line: 612,
            column: 4
          },
          end: {
            line: 626,
            column: 3
          }
        },
        line: 612
      },
      "58": {
        name: "(anonymous_58)",
        decl: {
          start: {
            line: 618,
            column: 31
          },
          end: {
            line: 618,
            column: 32
          }
        },
        loc: {
          start: {
            line: 618,
            column: 46
          },
          end: {
            line: 618,
            column: 56
          }
        },
        line: 618
      },
      "59": {
        name: "(anonymous_59)",
        decl: {
          start: {
            line: 628,
            column: 2
          },
          end: {
            line: 628,
            column: 3
          }
        },
        loc: {
          start: {
            line: 628,
            column: 37
          },
          end: {
            line: 634,
            column: 3
          }
        },
        line: 628
      },
      "60": {
        name: "(anonymous_60)",
        decl: {
          start: {
            line: 644,
            column: 2
          },
          end: {
            line: 644,
            column: 3
          }
        },
        loc: {
          start: {
            line: 644,
            column: 24
          },
          end: {
            line: 652,
            column: 3
          }
        },
        line: 644
      },
      "61": {
        name: "(anonymous_61)",
        decl: {
          start: {
            line: 657,
            column: 2
          },
          end: {
            line: 657,
            column: 3
          }
        },
        loc: {
          start: {
            line: 657,
            column: 52
          },
          end: {
            line: 667,
            column: 3
          }
        },
        line: 657
      },
      "62": {
        name: "(anonymous_62)",
        decl: {
          start: {
            line: 680,
            column: 2
          },
          end: {
            line: 680,
            column: 3
          }
        },
        loc: {
          start: {
            line: 680,
            column: 38
          },
          end: {
            line: 685,
            column: 3
          }
        },
        line: 680
      },
      "63": {
        name: "(anonymous_63)",
        decl: {
          start: {
            line: 687,
            column: 2
          },
          end: {
            line: 687,
            column: 3
          }
        },
        loc: {
          start: {
            line: 687,
            column: 51
          },
          end: {
            line: 696,
            column: 3
          }
        },
        line: 687
      },
      "64": {
        name: "(anonymous_64)",
        decl: {
          start: {
            line: 698,
            column: 2
          },
          end: {
            line: 698,
            column: 3
          }
        },
        loc: {
          start: {
            line: 698,
            column: 24
          },
          end: {
            line: 709,
            column: 3
          }
        },
        line: 698
      },
      "65": {
        name: "(anonymous_65)",
        decl: {
          start: {
            line: 711,
            column: 2
          },
          end: {
            line: 711,
            column: 3
          }
        },
        loc: {
          start: {
            line: 711,
            column: 28
          },
          end: {
            line: 715,
            column: 3
          }
        },
        line: 711
      },
      "66": {
        name: "(anonymous_66)",
        decl: {
          start: {
            line: 717,
            column: 2
          },
          end: {
            line: 717,
            column: 3
          }
        },
        loc: {
          start: {
            line: 717,
            column: 16
          },
          end: {
            line: 721,
            column: 3
          }
        },
        line: 717
      },
      "67": {
        name: "(anonymous_67)",
        decl: {
          start: {
            line: 723,
            column: 2
          },
          end: {
            line: 723,
            column: 3
          }
        },
        loc: {
          start: {
            line: 723,
            column: 26
          },
          end: {
            line: 741,
            column: 3
          }
        },
        line: 723
      },
      "68": {
        name: "(anonymous_68)",
        decl: {
          start: {
            line: 736,
            column: 14
          },
          end: {
            line: 736,
            column: 15
          }
        },
        loc: {
          start: {
            line: 736,
            column: 32
          },
          end: {
            line: 736,
            column: 37
          }
        },
        line: 736
      },
      "69": {
        name: "(anonymous_69)",
        decl: {
          start: {
            line: 739,
            column: 23
          },
          end: {
            line: 739,
            column: 24
          }
        },
        loc: {
          start: {
            line: 739,
            column: 34
          },
          end: {
            line: 739,
            column: 50
          }
        },
        line: 739
      },
      "70": {
        name: "(anonymous_70)",
        decl: {
          start: {
            line: 743,
            column: 2
          },
          end: {
            line: 743,
            column: 3
          }
        },
        loc: {
          start: {
            line: 747,
            column: 4
          },
          end: {
            line: 759,
            column: 3
          }
        },
        line: 747
      },
      "71": {
        name: "(anonymous_71)",
        decl: {
          start: {
            line: 748,
            column: 70
          },
          end: {
            line: 748,
            column: 71
          }
        },
        loc: {
          start: {
            line: 748,
            column: 86
          },
          end: {
            line: 748,
            column: 97
          }
        },
        line: 748
      },
      "72": {
        name: "(anonymous_72)",
        decl: {
          start: {
            line: 750,
            column: 12
          },
          end: {
            line: 750,
            column: 13
          }
        },
        loc: {
          start: {
            line: 750,
            column: 30
          },
          end: {
            line: 750,
            column: 35
          }
        },
        line: 750
      },
      "73": {
        name: "(anonymous_73)",
        decl: {
          start: {
            line: 752,
            column: 11
          },
          end: {
            line: 752,
            column: 12
          }
        },
        loc: {
          start: {
            line: 752,
            column: 22
          },
          end: {
            line: 752,
            column: 25
          }
        },
        line: 752
      },
      "74": {
        name: "(anonymous_74)",
        decl: {
          start: {
            line: 769,
            column: 21
          },
          end: {
            line: 769,
            column: 22
          }
        },
        loc: {
          start: {
            line: 772,
            column: 7
          },
          end: {
            line: 788,
            column: 3
          }
        },
        line: 772
      },
      "75": {
        name: "(anonymous_75)",
        decl: {
          start: {
            line: 775,
            column: 22
          },
          end: {
            line: 775,
            column: 23
          }
        },
        loc: {
          start: {
            line: 775,
            column: 34
          },
          end: {
            line: 787,
            column: 5
          }
        },
        line: 775
      },
      "76": {
        name: "(anonymous_76)",
        decl: {
          start: {
            line: 793,
            column: 24
          },
          end: {
            line: 793,
            column: 25
          }
        },
        loc: {
          start: {
            line: 797,
            column: 13
          },
          end: {
            line: 810,
            column: 3
          }
        },
        line: 797
      },
      "77": {
        name: "(anonymous_77)",
        decl: {
          start: {
            line: 815,
            column: 21
          },
          end: {
            line: 815,
            column: 22
          }
        },
        loc: {
          start: {
            line: 818,
            column: 22
          },
          end: {
            line: 832,
            column: 3
          }
        },
        line: 818
      },
      "78": {
        name: "(anonymous_78)",
        decl: {
          start: {
            line: 819,
            column: 23
          },
          end: {
            line: 819,
            column: 24
          }
        },
        loc: {
          start: {
            line: 819,
            column: 36
          },
          end: {
            line: 831,
            column: 5
          }
        },
        line: 819
      },
      "79": {
        name: "(anonymous_79)",
        decl: {
          start: {
            line: 821,
            column: 44
          },
          end: {
            line: 821,
            column: 45
          }
        },
        loc: {
          start: {
            line: 821,
            column: 50
          },
          end: {
            line: 824,
            column: 9
          }
        },
        line: 821
      },
      "80": {
        name: "(anonymous_80)",
        decl: {
          start: {
            line: 822,
            column: 26
          },
          end: {
            line: 822,
            column: 27
          }
        },
        loc: {
          start: {
            line: 822,
            column: 36
          },
          end: {
            line: 822,
            column: 44
          }
        },
        line: 822
      },
      "81": {
        name: "(anonymous_81)",
        decl: {
          start: {
            line: 826,
            column: 19
          },
          end: {
            line: 826,
            column: 20
          }
        },
        loc: {
          start: {
            line: 826,
            column: 25
          },
          end: {
            line: 829,
            column: 9
          }
        },
        line: 826
      },
      "82": {
        name: "(anonymous_82)",
        decl: {
          start: {
            line: 827,
            column: 26
          },
          end: {
            line: 827,
            column: 27
          }
        },
        loc: {
          start: {
            line: 827,
            column: 36
          },
          end: {
            line: 827,
            column: 44
          }
        },
        line: 827
      },
      "83": {
        name: "(anonymous_83)",
        decl: {
          start: {
            line: 837,
            column: 23
          },
          end: {
            line: 837,
            column: 24
          }
        },
        loc: {
          start: {
            line: 837,
            column: 66
          },
          end: {
            line: 852,
            column: 3
          }
        },
        line: 837
      },
      "84": {
        name: "(anonymous_84)",
        decl: {
          start: {
            line: 842,
            column: 21
          },
          end: {
            line: 842,
            column: 22
          }
        },
        loc: {
          start: {
            line: 842,
            column: 49
          },
          end: {
            line: 846,
            column: 7
          }
        },
        line: 842
      },
      "85": {
        name: "(anonymous_85)",
        decl: {
          start: {
            line: 863,
            column: 2
          },
          end: {
            line: 863,
            column: 3
          }
        },
        loc: {
          start: {
            line: 863,
            column: 49
          },
          end: {
            line: 868,
            column: 3
          }
        },
        line: 863
      },
      "86": {
        name: "(anonymous_86)",
        decl: {
          start: {
            line: 870,
            column: 2
          },
          end: {
            line: 870,
            column: 3
          }
        },
        loc: {
          start: {
            line: 870,
            column: 26
          },
          end: {
            line: 892,
            column: 3
          }
        },
        line: 870
      },
      "87": {
        name: "(anonymous_87)",
        decl: {
          start: {
            line: 879,
            column: 35
          },
          end: {
            line: 879,
            column: 36
          }
        },
        loc: {
          start: {
            line: 879,
            column: 47
          },
          end: {
            line: 885,
            column: 5
          }
        },
        line: 879
      },
      "88": {
        name: "(anonymous_88)",
        decl: {
          start: {
            line: 888,
            column: 16
          },
          end: {
            line: 888,
            column: 17
          }
        },
        loc: {
          start: {
            line: 888,
            column: 22
          },
          end: {
            line: 891,
            column: 5
          }
        },
        line: 888
      },
      "89": {
        name: "(anonymous_89)",
        decl: {
          start: {
            line: 894,
            column: 2
          },
          end: {
            line: 894,
            column: 3
          }
        },
        loc: {
          start: {
            line: 894,
            column: 25
          },
          end: {
            line: 896,
            column: 3
          }
        },
        line: 894
      },
      "90": {
        name: "(anonymous_90)",
        decl: {
          start: {
            line: 898,
            column: 2
          },
          end: {
            line: 898,
            column: 3
          }
        },
        loc: {
          start: {
            line: 898,
            column: 26
          },
          end: {
            line: 903,
            column: 3
          }
        },
        line: 898
      },
      "91": {
        name: "(anonymous_91)",
        decl: {
          start: {
            line: 905,
            column: 2
          },
          end: {
            line: 905,
            column: 3
          }
        },
        loc: {
          start: {
            line: 905,
            column: 48
          },
          end: {
            line: 928,
            column: 3
          }
        },
        line: 905
      },
      "92": {
        name: "(anonymous_92)",
        decl: {
          start: {
            line: 930,
            column: 2
          },
          end: {
            line: 930,
            column: 3
          }
        },
        loc: {
          start: {
            line: 930,
            column: 43
          },
          end: {
            line: 938,
            column: 3
          }
        },
        line: 930
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 41,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 38,
            column: 4
          },
          end: {
            line: 41,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 38
      },
      "1": {
        loc: {
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 91,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 91
      },
      "2": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 115,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 115,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 113
      },
      "3": {
        loc: {
          start: {
            line: 128,
            column: 30
          },
          end: {
            line: 128,
            column: 68
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 128,
            column: 44
          },
          end: {
            line: 128,
            column: 68
          }
        }],
        line: 128
      },
      "4": {
        loc: {
          start: {
            line: 146,
            column: 19
          },
          end: {
            line: 146,
            column: 42
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 146,
            column: 38
          },
          end: {
            line: 146,
            column: 42
          }
        }],
        line: 146
      },
      "5": {
        loc: {
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 160,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 160,
            column: 9
          }
        }, {
          start: {
            line: 157,
            column: 15
          },
          end: {
            line: 160,
            column: 9
          }
        }],
        line: 155
      },
      "6": {
        loc: {
          start: {
            line: 171,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 171,
            column: 4
          },
          end: {
            line: 173,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 171
      },
      "7": {
        loc: {
          start: {
            line: 171,
            column: 8
          },
          end: {
            line: 171,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 8
          },
          end: {
            line: 171,
            column: 29
          }
        }, {
          start: {
            line: 171,
            column: 33
          },
          end: {
            line: 171,
            column: 56
          }
        }],
        line: 171
      },
      "8": {
        loc: {
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 191,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 191,
            column: 6
          },
          end: {
            line: 191,
            column: 31
          }
        }, {
          start: {
            line: 191,
            column: 35
          },
          end: {
            line: 191,
            column: 45
          }
        }, {
          start: {
            line: 191,
            column: 49
          },
          end: {
            line: 191,
            column: 64
          }
        }],
        line: 191
      },
      "9": {
        loc: {
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 201,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 194
      },
      "10": {
        loc: {
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 212,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 212,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "11": {
        loc: {
          start: {
            line: 208,
            column: 18
          },
          end: {
            line: 208,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 208,
            column: 38
          },
          end: {
            line: 208,
            column: 44
          }
        }, {
          start: {
            line: 208,
            column: 47
          },
          end: {
            line: 208,
            column: 55
          }
        }],
        line: 208
      },
      "12": {
        loc: {
          start: {
            line: 223,
            column: 4
          },
          end: {
            line: 225,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 223,
            column: 4
          },
          end: {
            line: 225,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 223
      },
      "13": {
        loc: {
          start: {
            line: 252,
            column: 23
          },
          end: {
            line: 252,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 252,
            column: 23
          },
          end: {
            line: 252,
            column: 27
          }
        }, {
          start: {
            line: 252,
            column: 31
          },
          end: {
            line: 252,
            column: 74
          }
        }],
        line: 252
      },
      "14": {
        loc: {
          start: {
            line: 281,
            column: 2
          },
          end: {
            line: 281,
            column: 19
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 281,
            column: 14
          },
          end: {
            line: 281,
            column: 19
          }
        }],
        line: 281
      },
      "15": {
        loc: {
          start: {
            line: 288,
            column: 6
          },
          end: {
            line: 288,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 288,
            column: 6
          },
          end: {
            line: 288,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 288
      },
      "16": {
        loc: {
          start: {
            line: 291,
            column: 20
          },
          end: {
            line: 291,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 20
          },
          end: {
            line: 291,
            column: 29
          }
        }, {
          start: {
            line: 291,
            column: 33
          },
          end: {
            line: 291,
            column: 41
          }
        }],
        line: 291
      },
      "17": {
        loc: {
          start: {
            line: 293,
            column: 4
          },
          end: {
            line: 293,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 293,
            column: 4
          },
          end: {
            line: 293,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 293
      },
      "18": {
        loc: {
          start: {
            line: 296,
            column: 4
          },
          end: {
            line: 296,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 296,
            column: 4
          },
          end: {
            line: 296,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 296
      },
      "19": {
        loc: {
          start: {
            line: 310,
            column: 4
          },
          end: {
            line: 314,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 4
          },
          end: {
            line: 314,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 310
      },
      "20": {
        loc: {
          start: {
            line: 341,
            column: 4
          },
          end: {
            line: 341,
            column: 18
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 341,
            column: 16
          },
          end: {
            line: 341,
            column: 18
          }
        }],
        line: 341
      },
      "21": {
        loc: {
          start: {
            line: 342,
            column: 4
          },
          end: {
            line: 342,
            column: 15
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 342,
            column: 12
          },
          end: {
            line: 342,
            column: 15
          }
        }],
        line: 342
      },
      "22": {
        loc: {
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 356,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 356,
            column: 5
          }
        }, {
          start: {
            line: 354,
            column: 11
          },
          end: {
            line: 356,
            column: 5
          }
        }],
        line: 352
      },
      "23": {
        loc: {
          start: {
            line: 360,
            column: 4
          },
          end: {
            line: 363,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 360,
            column: 4
          },
          end: {
            line: 363,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 360
      },
      "24": {
        loc: {
          start: {
            line: 365,
            column: 4
          },
          end: {
            line: 369,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 365,
            column: 4
          },
          end: {
            line: 369,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 365
      },
      "25": {
        loc: {
          start: {
            line: 373,
            column: 4
          },
          end: {
            line: 373,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 373,
            column: 4
          },
          end: {
            line: 373,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 373
      },
      "26": {
        loc: {
          start: {
            line: 389,
            column: 4
          },
          end: {
            line: 391,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 389,
            column: 4
          },
          end: {
            line: 391,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 389
      },
      "27": {
        loc: {
          start: {
            line: 395,
            column: 18
          },
          end: {
            line: 395,
            column: 33
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 395,
            column: 29
          },
          end: {
            line: 395,
            column: 33
          }
        }],
        line: 395
      },
      "28": {
        loc: {
          start: {
            line: 396,
            column: 4
          },
          end: {
            line: 407,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 396,
            column: 4
          },
          end: {
            line: 407,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 396
      },
      "29": {
        loc: {
          start: {
            line: 396,
            column: 8
          },
          end: {
            line: 396,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 396,
            column: 8
          },
          end: {
            line: 396,
            column: 29
          }
        }, {
          start: {
            line: 396,
            column: 33
          },
          end: {
            line: 396,
            column: 56
          }
        }],
        line: 396
      },
      "30": {
        loc: {
          start: {
            line: 416,
            column: 4
          },
          end: {
            line: 418,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 416,
            column: 4
          },
          end: {
            line: 418,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 416
      },
      "31": {
        loc: {
          start: {
            line: 441,
            column: 4
          },
          end: {
            line: 446,
            column: 10
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 446,
            column: 8
          },
          end: {
            line: 446,
            column: 10
          }
        }],
        line: 441
      },
      "32": {
        loc: {
          start: {
            line: 448,
            column: 4
          },
          end: {
            line: 448,
            column: 44
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 448,
            column: 4
          },
          end: {
            line: 448,
            column: 44
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 448
      },
      "33": {
        loc: {
          start: {
            line: 450,
            column: 27
          },
          end: {
            line: 450,
            column: 39
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 450,
            column: 37
          },
          end: {
            line: 450,
            column: 39
          }
        }],
        line: 450
      },
      "34": {
        loc: {
          start: {
            line: 450,
            column: 41
          },
          end: {
            line: 450,
            column: 56
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 450,
            column: 50
          },
          end: {
            line: 450,
            column: 56
          }
        }],
        line: 450
      },
      "35": {
        loc: {
          start: {
            line: 453,
            column: 4
          },
          end: {
            line: 453,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 453,
            column: 4
          },
          end: {
            line: 453,
            column: 52
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 453
      },
      "36": {
        loc: {
          start: {
            line: 454,
            column: 4
          },
          end: {
            line: 454,
            column: 54
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 454,
            column: 4
          },
          end: {
            line: 454,
            column: 54
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 454
      },
      "37": {
        loc: {
          start: {
            line: 459,
            column: 22
          },
          end: {
            line: 459,
            column: 51
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 459,
            column: 42
          },
          end: {
            line: 459,
            column: 45
          }
        }, {
          start: {
            line: 459,
            column: 48
          },
          end: {
            line: 459,
            column: 51
          }
        }],
        line: 459
      },
      "38": {
        loc: {
          start: {
            line: 477,
            column: 4
          },
          end: {
            line: 480,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 477,
            column: 4
          },
          end: {
            line: 480,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 477
      },
      "39": {
        loc: {
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 485,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 485,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 482
      },
      "40": {
        loc: {
          start: {
            line: 504,
            column: 4
          },
          end: {
            line: 506,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 504,
            column: 4
          },
          end: {
            line: 506,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 504
      },
      "41": {
        loc: {
          start: {
            line: 522,
            column: 4
          },
          end: {
            line: 522,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 522,
            column: 4
          },
          end: {
            line: 522,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 522
      },
      "42": {
        loc: {
          start: {
            line: 536,
            column: 4
          },
          end: {
            line: 538,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 536,
            column: 4
          },
          end: {
            line: 538,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 536
      },
      "43": {
        loc: {
          start: {
            line: 543,
            column: 4
          },
          end: {
            line: 545,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 543,
            column: 4
          },
          end: {
            line: 545,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 543
      },
      "44": {
        loc: {
          start: {
            line: 555,
            column: 4
          },
          end: {
            line: 557,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 555,
            column: 4
          },
          end: {
            line: 557,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 555
      },
      "45": {
        loc: {
          start: {
            line: 581,
            column: 4
          },
          end: {
            line: 583,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 581,
            column: 4
          },
          end: {
            line: 583,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 581
      },
      "46": {
        loc: {
          start: {
            line: 592,
            column: 18
          },
          end: {
            line: 592,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 592,
            column: 18
          },
          end: {
            line: 592,
            column: 53
          }
        }, {
          start: {
            line: 592,
            column: 57
          },
          end: {
            line: 592,
            column: 59
          }
        }],
        line: 592
      },
      "47": {
        loc: {
          start: {
            line: 596,
            column: 4
          },
          end: {
            line: 598,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 596,
            column: 4
          },
          end: {
            line: 598,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 596
      },
      "48": {
        loc: {
          start: {
            line: 603,
            column: 4
          },
          end: {
            line: 605,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 603,
            column: 4
          },
          end: {
            line: 605,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 603
      },
      "49": {
        loc: {
          start: {
            line: 613,
            column: 18
          },
          end: {
            line: 613,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 613,
            column: 18
          },
          end: {
            line: 613,
            column: 53
          }
        }, {
          start: {
            line: 613,
            column: 57
          },
          end: {
            line: 613,
            column: 59
          }
        }],
        line: 613
      },
      "50": {
        loc: {
          start: {
            line: 614,
            column: 4
          },
          end: {
            line: 616,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 614,
            column: 4
          },
          end: {
            line: 616,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 614
      },
      "51": {
        loc: {
          start: {
            line: 645,
            column: 4
          },
          end: {
            line: 651,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 645,
            column: 4
          },
          end: {
            line: 651,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 645
      },
      "52": {
        loc: {
          start: {
            line: 658,
            column: 4
          },
          end: {
            line: 666,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 658,
            column: 4
          },
          end: {
            line: 666,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 658
      },
      "53": {
        loc: {
          start: {
            line: 660,
            column: 29
          },
          end: {
            line: 660,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 660,
            column: 29
          },
          end: {
            line: 660,
            column: 49
          }
        }, {
          start: {
            line: 660,
            column: 53
          },
          end: {
            line: 660,
            column: 56
          }
        }],
        line: 660
      },
      "54": {
        loc: {
          start: {
            line: 663,
            column: 6
          },
          end: {
            line: 665,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 663,
            column: 6
          },
          end: {
            line: 665,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 663
      },
      "55": {
        loc: {
          start: {
            line: 681,
            column: 4
          },
          end: {
            line: 683,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 681,
            column: 4
          },
          end: {
            line: 683,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 681
      },
      "56": {
        loc: {
          start: {
            line: 687,
            column: 31
          },
          end: {
            line: 687,
            column: 43
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 687,
            column: 37
          },
          end: {
            line: 687,
            column: 43
          }
        }],
        line: 687
      },
      "57": {
        loc: {
          start: {
            line: 689,
            column: 4
          },
          end: {
            line: 691,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 689,
            column: 4
          },
          end: {
            line: 691,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 689
      },
      "58": {
        loc: {
          start: {
            line: 700,
            column: 4
          },
          end: {
            line: 703,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 700,
            column: 4
          },
          end: {
            line: 703,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 700
      },
      "59": {
        loc: {
          start: {
            line: 700,
            column: 8
          },
          end: {
            line: 700,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 700,
            column: 8
          },
          end: {
            line: 700,
            column: 22
          }
        }, {
          start: {
            line: 700,
            column: 26
          },
          end: {
            line: 700,
            column: 53
          }
        }],
        line: 700
      },
      "60": {
        loc: {
          start: {
            line: 705,
            column: 24
          },
          end: {
            line: 705,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 705,
            column: 24
          },
          end: {
            line: 705,
            column: 50
          }
        }, {
          start: {
            line: 705,
            column: 54
          },
          end: {
            line: 705,
            column: 55
          }
        }],
        line: 705
      },
      "61": {
        loc: {
          start: {
            line: 728,
            column: 6
          },
          end: {
            line: 730,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 728,
            column: 6
          },
          end: {
            line: 730,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 728
      },
      "62": {
        loc: {
          start: {
            line: 734,
            column: 4
          },
          end: {
            line: 740,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 734,
            column: 4
          },
          end: {
            line: 740,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 734
      },
      "63": {
        loc: {
          start: {
            line: 756,
            column: 15
          },
          end: {
            line: 756,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 756,
            column: 33
          },
          end: {
            line: 756,
            column: 70
          }
        }, {
          start: {
            line: 756,
            column: 73
          },
          end: {
            line: 756,
            column: 74
          }
        }],
        line: 756
      },
      "64": {
        loc: {
          start: {
            line: 782,
            column: 6
          },
          end: {
            line: 784,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 782,
            column: 6
          },
          end: {
            line: 784,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 782
      },
      "65": {
        loc: {
          start: {
            line: 793,
            column: 43
          },
          end: {
            line: 797,
            column: 8
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 797,
            column: 6
          },
          end: {
            line: 797,
            column: 8
          }
        }],
        line: 793
      },
      "66": {
        loc: {
          start: {
            line: 799,
            column: 23
          },
          end: {
            line: 799,
            column: 74
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 799,
            column: 47
          },
          end: {
            line: 799,
            column: 70
          }
        }, {
          start: {
            line: 799,
            column: 73
          },
          end: {
            line: 799,
            column: 74
          }
        }],
        line: 799
      },
      "67": {
        loc: {
          start: {
            line: 804,
            column: 17
          },
          end: {
            line: 804,
            column: 43
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 804,
            column: 36
          },
          end: {
            line: 804,
            column: 38
          }
        }, {
          start: {
            line: 804,
            column: 41
          },
          end: {
            line: 804,
            column: 43
          }
        }],
        line: 804
      },
      "68": {
        loc: {
          start: {
            line: 808,
            column: 16
          },
          end: {
            line: 808,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 808,
            column: 16
          },
          end: {
            line: 808,
            column: 32
          }
        }, {
          start: {
            line: 808,
            column: 36
          },
          end: {
            line: 808,
            column: 41
          }
        }],
        line: 808
      },
      "69": {
        loc: {
          start: {
            line: 817,
            column: 4
          },
          end: {
            line: 817,
            column: 13
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 817,
            column: 12
          },
          end: {
            line: 817,
            column: 13
          }
        }],
        line: 817
      },
      "70": {
        loc: {
          start: {
            line: 820,
            column: 6
          },
          end: {
            line: 830,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 820,
            column: 6
          },
          end: {
            line: 830,
            column: 7
          }
        }, {
          start: {
            line: 825,
            column: 13
          },
          end: {
            line: 830,
            column: 7
          }
        }],
        line: 820
      },
      "71": {
        loc: {
          start: {
            line: 820,
            column: 10
          },
          end: {
            line: 820,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 820,
            column: 10
          },
          end: {
            line: 820,
            column: 31
          }
        }, {
          start: {
            line: 820,
            column: 35
          },
          end: {
            line: 820,
            column: 66
          }
        }],
        line: 820
      },
      "72": {
        loc: {
          start: {
            line: 864,
            column: 4
          },
          end: {
            line: 866,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 864,
            column: 4
          },
          end: {
            line: 866,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 864
      },
      "73": {
        loc: {
          start: {
            line: 871,
            column: 4
          },
          end: {
            line: 871,
            column: 34
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 871,
            column: 4
          },
          end: {
            line: 871,
            column: 34
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 871
      },
      "74": {
        loc: {
          start: {
            line: 900,
            column: 4
          },
          end: {
            line: 902,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 900,
            column: 4
          },
          end: {
            line: 902,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 900
      },
      "75": {
        loc: {
          start: {
            line: 906,
            column: 4
          },
          end: {
            line: 906,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 906,
            column: 4
          },
          end: {
            line: 906,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 906
      },
      "76": {
        loc: {
          start: {
            line: 933,
            column: 13
          },
          end: {
            line: 933,
            column: 47
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 933,
            column: 22
          },
          end: {
            line: 933,
            column: 42
          }
        }, {
          start: {
            line: 933,
            column: 45
          },
          end: {
            line: 933,
            column: 47
          }
        }],
        line: 933
      },
      "77": {
        loc: {
          start: {
            line: 942,
            column: 0
          },
          end: {
            line: 944,
            column: 1
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 942,
            column: 0
          },
          end: {
            line: 944,
            column: 1
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 942
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0,
      "227": 0,
      "228": 0,
      "229": 0,
      "230": 0,
      "231": 0,
      "232": 0,
      "233": 0,
      "234": 0,
      "235": 0,
      "236": 0,
      "237": 0,
      "238": 0,
      "239": 0,
      "240": 0,
      "241": 0,
      "242": 0,
      "243": 0,
      "244": 0,
      "245": 0,
      "246": 0,
      "247": 0,
      "248": 0,
      "249": 0,
      "250": 0,
      "251": 0,
      "252": 0,
      "253": 0,
      "254": 0,
      "255": 0,
      "256": 0,
      "257": 0,
      "258": 0,
      "259": 0,
      "260": 0,
      "261": 0,
      "262": 0,
      "263": 0,
      "264": 0,
      "265": 0,
      "266": 0,
      "267": 0,
      "268": 0,
      "269": 0,
      "270": 0,
      "271": 0,
      "272": 0,
      "273": 0,
      "274": 0,
      "275": 0,
      "276": 0,
      "277": 0,
      "278": 0,
      "279": 0,
      "280": 0,
      "281": 0,
      "282": 0,
      "283": 0,
      "284": 0,
      "285": 0,
      "286": 0,
      "287": 0,
      "288": 0,
      "289": 0,
      "290": 0,
      "291": 0,
      "292": 0,
      "293": 0,
      "294": 0,
      "295": 0,
      "296": 0,
      "297": 0,
      "298": 0,
      "299": 0,
      "300": 0,
      "301": 0,
      "302": 0,
      "303": 0,
      "304": 0,
      "305": 0,
      "306": 0,
      "307": 0,
      "308": 0,
      "309": 0,
      "310": 0,
      "311": 0,
      "312": 0,
      "313": 0,
      "314": 0,
      "315": 0,
      "316": 0,
      "317": 0,
      "318": 0,
      "319": 0,
      "320": 0,
      "321": 0,
      "322": 0,
      "323": 0,
      "324": 0,
      "325": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0],
      "21": [0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0],
      "32": [0, 0],
      "33": [0],
      "34": [0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0],
      "56": [0],
      "57": [0, 0],
      "58": [0, 0],
      "59": [0, 0],
      "60": [0, 0],
      "61": [0, 0],
      "62": [0, 0],
      "63": [0, 0],
      "64": [0, 0],
      "65": [0],
      "66": [0, 0],
      "67": [0, 0],
      "68": [0, 0],
      "69": [0],
      "70": [0, 0],
      "71": [0, 0],
      "72": [0, 0],
      "73": [0, 0],
      "74": [0, 0],
      "75": [0, 0],
      "76": [0, 0],
      "77": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6811c1115e646e31a31b4f25952c7be013f08f7a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1gdttvm7jx = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1gdttvm7jx();
import React from 'react';
import { InteractionManager, Platform, Dimensions } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
var PerformanceMonitor = function () {
  function PerformanceMonitor() {
    _classCallCheck(this, PerformanceMonitor);
    this.metrics = (cov_1gdttvm7jx().s[0]++, new Map());
    this.observers = (cov_1gdttvm7jx().s[1]++, []);
  }
  return _createClass(PerformanceMonitor, [{
    key: "start",
    value: function start(name, metadata) {
      cov_1gdttvm7jx().f[0]++;
      var startTime = (cov_1gdttvm7jx().s[2]++, Date.now());
      cov_1gdttvm7jx().s[3]++;
      this.metrics.set(name, {
        name: name,
        startTime: startTime,
        metadata: metadata
      });
    }
  }, {
    key: "end",
    value: function end(name) {
      cov_1gdttvm7jx().f[1]++;
      var metric = (cov_1gdttvm7jx().s[4]++, this.metrics.get(name));
      cov_1gdttvm7jx().s[5]++;
      if (!metric) {
        cov_1gdttvm7jx().b[0][0]++;
        cov_1gdttvm7jx().s[6]++;
        console.warn(`Performance metric "${name}" not found`);
        cov_1gdttvm7jx().s[7]++;
        return null;
      } else {
        cov_1gdttvm7jx().b[0][1]++;
      }
      var endTime = (cov_1gdttvm7jx().s[8]++, Date.now());
      var duration = (cov_1gdttvm7jx().s[9]++, endTime - metric.startTime);
      var completedMetric = (cov_1gdttvm7jx().s[10]++, Object.assign({}, metric, {
        endTime: endTime,
        duration: duration
      }));
      cov_1gdttvm7jx().s[11]++;
      this.metrics.set(name, completedMetric);
      cov_1gdttvm7jx().s[12]++;
      this.notifyObservers(completedMetric);
      cov_1gdttvm7jx().s[13]++;
      return completedMetric;
    }
  }, {
    key: "getMetric",
    value: function getMetric(name) {
      cov_1gdttvm7jx().f[2]++;
      cov_1gdttvm7jx().s[14]++;
      return this.metrics.get(name);
    }
  }, {
    key: "getAllMetrics",
    value: function getAllMetrics() {
      cov_1gdttvm7jx().f[3]++;
      cov_1gdttvm7jx().s[15]++;
      return Array.from(this.metrics.values());
    }
  }, {
    key: "clear",
    value: function clear() {
      cov_1gdttvm7jx().f[4]++;
      cov_1gdttvm7jx().s[16]++;
      this.metrics.clear();
    }
  }, {
    key: "addObserver",
    value: function addObserver(observer) {
      cov_1gdttvm7jx().f[5]++;
      cov_1gdttvm7jx().s[17]++;
      this.observers.push(observer);
    }
  }, {
    key: "removeObserver",
    value: function removeObserver(observer) {
      cov_1gdttvm7jx().f[6]++;
      var index = (cov_1gdttvm7jx().s[18]++, this.observers.indexOf(observer));
      cov_1gdttvm7jx().s[19]++;
      if (index > -1) {
        cov_1gdttvm7jx().b[1][0]++;
        cov_1gdttvm7jx().s[20]++;
        this.observers.splice(index, 1);
      } else {
        cov_1gdttvm7jx().b[1][1]++;
      }
    }
  }, {
    key: "notifyObservers",
    value: function notifyObservers(metric) {
      cov_1gdttvm7jx().f[7]++;
      cov_1gdttvm7jx().s[21]++;
      this.observers.forEach(function (observer) {
        cov_1gdttvm7jx().f[8]++;
        cov_1gdttvm7jx().s[22]++;
        try {
          cov_1gdttvm7jx().s[23]++;
          observer(metric);
        } catch (error) {
          cov_1gdttvm7jx().s[24]++;
          console.error('Error in performance observer:', error);
        }
      });
    }
  }, {
    key: "trackComponentLoad",
    value: function trackComponentLoad(componentName, loadTime) {
      cov_1gdttvm7jx().f[9]++;
      cov_1gdttvm7jx().s[25]++;
      console.log(`Component ${componentName} loaded in ${loadTime}ms`);
      cov_1gdttvm7jx().s[26]++;
      if (loadTime > 2000) {
        cov_1gdttvm7jx().b[2][0]++;
        cov_1gdttvm7jx().s[27]++;
        console.warn(`Slow component load: ${componentName} took ${loadTime}ms`);
      } else {
        cov_1gdttvm7jx().b[2][1]++;
      }
    }
  }, {
    key: "trackComponentLoadError",
    value: function trackComponentLoadError(componentName, error) {
      cov_1gdttvm7jx().f[10]++;
      cov_1gdttvm7jx().s[28]++;
      console.error(`Component ${componentName} failed to load:`, error);
    }
  }, {
    key: "measureNetworkLatency",
    value: (function () {
      var _measureNetworkLatency = _asyncToGenerator(function* () {
        var url = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1gdttvm7jx().b[3][0]++, 'https://www.google.com');
        cov_1gdttvm7jx().f[11]++;
        var start = (cov_1gdttvm7jx().s[29]++, Date.now());
        cov_1gdttvm7jx().s[30]++;
        try {
          cov_1gdttvm7jx().s[31]++;
          yield fetch(url, {
            method: 'HEAD',
            mode: 'no-cors',
            cache: 'no-cache'
          });
          cov_1gdttvm7jx().s[32]++;
          return Date.now() - start;
        } catch (error) {
          cov_1gdttvm7jx().s[33]++;
          return -1;
        }
      });
      function measureNetworkLatency() {
        return _measureNetworkLatency.apply(this, arguments);
      }
      return measureNetworkLatency;
    }())
  }, {
    key: "measureFrameRate",
    value: function measureFrameRate() {
      var duration = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1gdttvm7jx().b[4][0]++, 1000);
      cov_1gdttvm7jx().f[12]++;
      cov_1gdttvm7jx().s[34]++;
      return new Promise(function (resolve) {
        cov_1gdttvm7jx().f[13]++;
        var frames = (cov_1gdttvm7jx().s[35]++, 0);
        var start = (cov_1gdttvm7jx().s[36]++, Date.now());
        cov_1gdttvm7jx().s[37]++;
        var _countFrame = function countFrame() {
          cov_1gdttvm7jx().f[14]++;
          cov_1gdttvm7jx().s[38]++;
          frames++;
          var elapsed = (cov_1gdttvm7jx().s[39]++, Date.now() - start);
          cov_1gdttvm7jx().s[40]++;
          if (elapsed < duration) {
            cov_1gdttvm7jx().b[5][0]++;
            cov_1gdttvm7jx().s[41]++;
            requestAnimationFrame(_countFrame);
          } else {
            cov_1gdttvm7jx().b[5][1]++;
            var fps = (cov_1gdttvm7jx().s[42]++, frames * 1000 / elapsed);
            cov_1gdttvm7jx().s[43]++;
            resolve(fps);
          }
        };
        cov_1gdttvm7jx().s[44]++;
        requestAnimationFrame(_countFrame);
      });
    }
  }, {
    key: "getMemoryUsage",
    value: function getMemoryUsage() {
      cov_1gdttvm7jx().f[15]++;
      cov_1gdttvm7jx().s[45]++;
      if ((cov_1gdttvm7jx().b[7][0]++, Platform.OS === 'web') && (cov_1gdttvm7jx().b[7][1]++, 'memory' in performance)) {
        cov_1gdttvm7jx().b[6][0]++;
        cov_1gdttvm7jx().s[46]++;
        return performance.memory.usedJSHeapSize / 1024 / 1024;
      } else {
        cov_1gdttvm7jx().b[6][1]++;
      }
      cov_1gdttvm7jx().s[47]++;
      return 0;
    }
  }, {
    key: "getOptimizationSuggestions",
    value: function getOptimizationSuggestions() {
      cov_1gdttvm7jx().f[16]++;
      var suggestions = (cov_1gdttvm7jx().s[48]++, []);
      var metrics = (cov_1gdttvm7jx().s[49]++, this.getAllMetrics());
      var slowRenders = (cov_1gdttvm7jx().s[50]++, metrics.filter(function (m) {
        cov_1gdttvm7jx().f[17]++;
        cov_1gdttvm7jx().s[51]++;
        return (cov_1gdttvm7jx().b[8][0]++, m.name.includes('render')) && (cov_1gdttvm7jx().b[8][1]++, m.duration) && (cov_1gdttvm7jx().b[8][2]++, m.duration > 16);
      }));
      cov_1gdttvm7jx().s[52]++;
      if (slowRenders.length > 0) {
        cov_1gdttvm7jx().b[9][0]++;
        cov_1gdttvm7jx().s[53]++;
        suggestions.push({
          type: 'render',
          severity: 'medium',
          message: `${slowRenders.length} slow renders detected`,
          action: 'Use React.memo, optimize re-renders, implement virtualization'
        });
      } else {
        cov_1gdttvm7jx().b[9][1]++;
      }
      var memoryUsage = (cov_1gdttvm7jx().s[54]++, this.getMemoryUsage());
      cov_1gdttvm7jx().s[55]++;
      if (memoryUsage > 100) {
        cov_1gdttvm7jx().b[10][0]++;
        cov_1gdttvm7jx().s[56]++;
        suggestions.push({
          type: 'memory',
          severity: memoryUsage > 200 ? (cov_1gdttvm7jx().b[11][0]++, 'high') : (cov_1gdttvm7jx().b[11][1]++, 'medium'),
          message: `High memory usage: ${memoryUsage.toFixed(1)}MB`,
          action: 'Implement lazy loading, optimize images, clear unused data'
        });
      } else {
        cov_1gdttvm7jx().b[10][1]++;
      }
      cov_1gdttvm7jx().s[57]++;
      return suggestions;
    }
  }, {
    key: "trackDatabaseQuery",
    value: function trackDatabaseQuery(queryName, duration) {
      cov_1gdttvm7jx().f[18]++;
      cov_1gdttvm7jx().s[58]++;
      console.log(`Database query ${queryName} completed in ${duration}ms`);
      cov_1gdttvm7jx().s[59]++;
      if (duration > 2000) {
        cov_1gdttvm7jx().b[12][0]++;
        cov_1gdttvm7jx().s[60]++;
        console.warn(`Slow database query: ${queryName} took ${duration}ms`);
      } else {
        cov_1gdttvm7jx().b[12][1]++;
      }
    }
  }, {
    key: "trackDatabaseError",
    value: function trackDatabaseError(queryName, error) {
      cov_1gdttvm7jx().f[19]++;
      cov_1gdttvm7jx().s[61]++;
      console.error(`Database query ${queryName} failed:`, error);
    }
  }, {
    key: "getCacheHitRate",
    value: function getCacheHitRate() {
      cov_1gdttvm7jx().f[20]++;
      cov_1gdttvm7jx().s[62]++;
      return 0.75;
    }
  }]);
}();
export var performanceMonitor = (cov_1gdttvm7jx().s[63]++, new PerformanceMonitor());
export function measurePerformance(name) {
  cov_1gdttvm7jx().f[21]++;
  cov_1gdttvm7jx().s[64]++;
  return function (target, propertyKey, descriptor) {
    cov_1gdttvm7jx().f[22]++;
    var originalMethod = (cov_1gdttvm7jx().s[65]++, descriptor.value);
    var metricName = (cov_1gdttvm7jx().s[66]++, (cov_1gdttvm7jx().b[13][0]++, name) || (cov_1gdttvm7jx().b[13][1]++, `${target.constructor.name}.${propertyKey}`));
    cov_1gdttvm7jx().s[67]++;
    descriptor.value = _asyncToGenerator(function* () {
      cov_1gdttvm7jx().f[23]++;
      cov_1gdttvm7jx().s[68]++;
      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
        args[_key] = arguments[_key];
      }
      performanceMonitor.start(metricName, {
        className: target.constructor.name,
        methodName: propertyKey,
        args: args.length
      });
      cov_1gdttvm7jx().s[69]++;
      try {
        var result = (cov_1gdttvm7jx().s[70]++, yield originalMethod.apply(this, args));
        cov_1gdttvm7jx().s[71]++;
        performanceMonitor.end(metricName);
        cov_1gdttvm7jx().s[72]++;
        return result;
      } catch (error) {
        cov_1gdttvm7jx().s[73]++;
        performanceMonitor.end(metricName);
        cov_1gdttvm7jx().s[74]++;
        throw error;
      }
    });
    cov_1gdttvm7jx().s[75]++;
    return descriptor;
  };
}
export function debounce(func, wait) {
  var immediate = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_1gdttvm7jx().b[14][0]++, false);
  cov_1gdttvm7jx().f[24]++;
  var timeout = (cov_1gdttvm7jx().s[76]++, null);
  cov_1gdttvm7jx().s[77]++;
  return function executedFunction() {
    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {
      args[_key2] = arguments[_key2];
    }
    cov_1gdttvm7jx().f[25]++;
    cov_1gdttvm7jx().s[78]++;
    var later = function later() {
      cov_1gdttvm7jx().f[26]++;
      cov_1gdttvm7jx().s[79]++;
      timeout = null;
      cov_1gdttvm7jx().s[80]++;
      if (!immediate) {
        cov_1gdttvm7jx().b[15][0]++;
        cov_1gdttvm7jx().s[81]++;
        func.apply(void 0, args);
      } else {
        cov_1gdttvm7jx().b[15][1]++;
      }
    };
    var callNow = (cov_1gdttvm7jx().s[82]++, (cov_1gdttvm7jx().b[16][0]++, immediate) && (cov_1gdttvm7jx().b[16][1]++, !timeout));
    cov_1gdttvm7jx().s[83]++;
    if (timeout) {
      cov_1gdttvm7jx().b[17][0]++;
      cov_1gdttvm7jx().s[84]++;
      clearTimeout(timeout);
    } else {
      cov_1gdttvm7jx().b[17][1]++;
    }
    cov_1gdttvm7jx().s[85]++;
    timeout = setTimeout(later, wait);
    cov_1gdttvm7jx().s[86]++;
    if (callNow) {
      cov_1gdttvm7jx().b[18][0]++;
      cov_1gdttvm7jx().s[87]++;
      func.apply(void 0, args);
    } else {
      cov_1gdttvm7jx().b[18][1]++;
    }
  };
}
export function throttle(func, limit) {
  cov_1gdttvm7jx().f[27]++;
  var inThrottle;
  cov_1gdttvm7jx().s[88]++;
  return function executedFunction() {
    cov_1gdttvm7jx().f[28]++;
    cov_1gdttvm7jx().s[89]++;
    if (!inThrottle) {
      cov_1gdttvm7jx().b[19][0]++;
      cov_1gdttvm7jx().s[90]++;
      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {
        args[_key3] = arguments[_key3];
      }
      func.apply(null, args);
      cov_1gdttvm7jx().s[91]++;
      inThrottle = true;
      cov_1gdttvm7jx().s[92]++;
      setTimeout(function () {
        cov_1gdttvm7jx().f[29]++;
        cov_1gdttvm7jx().s[93]++;
        return inThrottle = false;
      }, limit);
    } else {
      cov_1gdttvm7jx().b[19][1]++;
    }
  };
}
export function runAfterInteractions(func) {
  cov_1gdttvm7jx().f[30]++;
  cov_1gdttvm7jx().s[94]++;
  return new Promise(function (resolve) {
    cov_1gdttvm7jx().f[31]++;
    cov_1gdttvm7jx().s[95]++;
    InteractionManager.runAfterInteractions(function () {
      cov_1gdttvm7jx().f[32]++;
      cov_1gdttvm7jx().s[96]++;
      resolve(func());
    });
  });
}
export var BatchProcessor = function () {
  function BatchProcessor(processor) {
    var batchSize = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1gdttvm7jx().b[20][0]++, 10);
    var delay = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_1gdttvm7jx().b[21][0]++, 100);
    _classCallCheck(this, BatchProcessor);
    this.batch = (cov_1gdttvm7jx().s[97]++, []);
    this.timeout = (cov_1gdttvm7jx().s[98]++, null);
    cov_1gdttvm7jx().f[33]++;
    cov_1gdttvm7jx().s[99]++;
    this.processor = processor;
    cov_1gdttvm7jx().s[100]++;
    this.batchSize = batchSize;
    cov_1gdttvm7jx().s[101]++;
    this.delay = delay;
  }
  return _createClass(BatchProcessor, [{
    key: "add",
    value: function add(item) {
      cov_1gdttvm7jx().f[34]++;
      cov_1gdttvm7jx().s[102]++;
      this.batch.push(item);
      cov_1gdttvm7jx().s[103]++;
      if (this.batch.length >= this.batchSize) {
        cov_1gdttvm7jx().b[22][0]++;
        cov_1gdttvm7jx().s[104]++;
        this.flush();
      } else {
        cov_1gdttvm7jx().b[22][1]++;
        cov_1gdttvm7jx().s[105]++;
        this.scheduleFlush();
      }
    }
  }, {
    key: "flush",
    value: function flush() {
      cov_1gdttvm7jx().f[35]++;
      cov_1gdttvm7jx().s[106]++;
      if (this.timeout) {
        cov_1gdttvm7jx().b[23][0]++;
        cov_1gdttvm7jx().s[107]++;
        clearTimeout(this.timeout);
        cov_1gdttvm7jx().s[108]++;
        this.timeout = null;
      } else {
        cov_1gdttvm7jx().b[23][1]++;
      }
      cov_1gdttvm7jx().s[109]++;
      if (this.batch.length > 0) {
        cov_1gdttvm7jx().b[24][0]++;
        var items = (cov_1gdttvm7jx().s[110]++, _toConsumableArray(this.batch));
        cov_1gdttvm7jx().s[111]++;
        this.batch = [];
        cov_1gdttvm7jx().s[112]++;
        this.processor(items);
      } else {
        cov_1gdttvm7jx().b[24][1]++;
      }
    }
  }, {
    key: "scheduleFlush",
    value: function scheduleFlush() {
      var _this = this;
      cov_1gdttvm7jx().f[36]++;
      cov_1gdttvm7jx().s[113]++;
      if (this.timeout) {
        cov_1gdttvm7jx().b[25][0]++;
        cov_1gdttvm7jx().s[114]++;
        return;
      } else {
        cov_1gdttvm7jx().b[25][1]++;
      }
      cov_1gdttvm7jx().s[115]++;
      this.timeout = setTimeout(function () {
        cov_1gdttvm7jx().f[37]++;
        cov_1gdttvm7jx().s[116]++;
        _this.flush();
      }, this.delay);
    }
  }]);
}();
export var MemoryMonitor = function () {
  function MemoryMonitor() {
    _classCallCheck(this, MemoryMonitor);
    this.observers = (cov_1gdttvm7jx().s[117]++, []);
  }
  return _createClass(MemoryMonitor, [{
    key: "startMonitoring",
    value: function startMonitoring() {
      var _this2 = this;
      var interval = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1gdttvm7jx().b[27][0]++, 5000);
      cov_1gdttvm7jx().f[39]++;
      cov_1gdttvm7jx().s[121]++;
      if ((cov_1gdttvm7jx().b[29][0]++, Platform.OS === 'web') && (cov_1gdttvm7jx().b[29][1]++, 'memory' in performance)) {
        cov_1gdttvm7jx().b[28][0]++;
        cov_1gdttvm7jx().s[122]++;
        setInterval(function () {
          cov_1gdttvm7jx().f[40]++;
          var memory = (cov_1gdttvm7jx().s[123]++, performance.memory);
          var usage = (cov_1gdttvm7jx().s[124]++, {
            usedJSHeapSize: memory.usedJSHeapSize,
            totalJSHeapSize: memory.totalJSHeapSize,
            jsHeapSizeLimit: memory.jsHeapSizeLimit,
            timestamp: Date.now()
          });
          cov_1gdttvm7jx().s[125]++;
          _this2.notifyObservers(usage);
        }, interval);
      } else {
        cov_1gdttvm7jx().b[28][1]++;
      }
    }
  }, {
    key: "addObserver",
    value: function addObserver(observer) {
      cov_1gdttvm7jx().f[41]++;
      cov_1gdttvm7jx().s[126]++;
      this.observers.push(observer);
    }
  }, {
    key: "removeObserver",
    value: function removeObserver(observer) {
      cov_1gdttvm7jx().f[42]++;
      var index = (cov_1gdttvm7jx().s[127]++, this.observers.indexOf(observer));
      cov_1gdttvm7jx().s[128]++;
      if (index > -1) {
        cov_1gdttvm7jx().b[30][0]++;
        cov_1gdttvm7jx().s[129]++;
        this.observers.splice(index, 1);
      } else {
        cov_1gdttvm7jx().b[30][1]++;
      }
    }
  }, {
    key: "notifyObservers",
    value: function notifyObservers(usage) {
      cov_1gdttvm7jx().f[43]++;
      cov_1gdttvm7jx().s[130]++;
      this.observers.forEach(function (observer) {
        cov_1gdttvm7jx().f[44]++;
        cov_1gdttvm7jx().s[131]++;
        try {
          cov_1gdttvm7jx().s[132]++;
          observer(usage);
        } catch (error) {
          cov_1gdttvm7jx().s[133]++;
          console.error('Error in memory observer:', error);
        }
      });
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      cov_1gdttvm7jx().f[38]++;
      cov_1gdttvm7jx().s[118]++;
      if (!MemoryMonitor.instance) {
        cov_1gdttvm7jx().b[26][0]++;
        cov_1gdttvm7jx().s[119]++;
        MemoryMonitor.instance = new MemoryMonitor();
      } else {
        cov_1gdttvm7jx().b[26][1]++;
      }
      cov_1gdttvm7jx().s[120]++;
      return MemoryMonitor.instance;
    }
  }]);
}();
export var imageOptimization = (cov_1gdttvm7jx().s[134]++, {
  getOptimizedUrl: function getOptimizedUrl(url) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1gdttvm7jx().b[31][0]++, {});
    cov_1gdttvm7jx().f[45]++;
    cov_1gdttvm7jx().s[135]++;
    if (!url.startsWith('http')) {
      cov_1gdttvm7jx().b[32][0]++;
      cov_1gdttvm7jx().s[136]++;
      return url;
    } else {
      cov_1gdttvm7jx().b[32][1]++;
    }
    var _ref2 = (cov_1gdttvm7jx().s[137]++, options),
      width = _ref2.width,
      height = _ref2.height,
      _ref2$quality = _ref2.quality,
      quality = _ref2$quality === void 0 ? (cov_1gdttvm7jx().b[33][0]++, 80) : _ref2$quality,
      _ref2$format = _ref2.format,
      format = _ref2$format === void 0 ? (cov_1gdttvm7jx().b[34][0]++, 'webp') : _ref2$format;
    var params = (cov_1gdttvm7jx().s[138]++, new URLSearchParams());
    cov_1gdttvm7jx().s[139]++;
    if (width) {
      cov_1gdttvm7jx().b[35][0]++;
      cov_1gdttvm7jx().s[140]++;
      params.append('w', width.toString());
    } else {
      cov_1gdttvm7jx().b[35][1]++;
    }
    cov_1gdttvm7jx().s[141]++;
    if (height) {
      cov_1gdttvm7jx().b[36][0]++;
      cov_1gdttvm7jx().s[142]++;
      params.append('h', height.toString());
    } else {
      cov_1gdttvm7jx().b[36][1]++;
    }
    cov_1gdttvm7jx().s[143]++;
    params.append('q', quality.toString());
    cov_1gdttvm7jx().s[144]++;
    params.append('f', format);
    cov_1gdttvm7jx().s[145]++;
    params.append('auto', 'format');
    var separator = (cov_1gdttvm7jx().s[146]++, url.includes('?') ? (cov_1gdttvm7jx().b[37][0]++, '&') : (cov_1gdttvm7jx().b[37][1]++, '?'));
    cov_1gdttvm7jx().s[147]++;
    return `${url}${separator}${params.toString()}`;
  },
  getOptimalDimensions: function getOptimalDimensions(originalWidth, originalHeight, maxWidth, maxHeight) {
    cov_1gdttvm7jx().f[46]++;
    var aspectRatio = (cov_1gdttvm7jx().s[148]++, originalWidth / originalHeight);
    var width = (cov_1gdttvm7jx().s[149]++, originalWidth);
    var height = (cov_1gdttvm7jx().s[150]++, originalHeight);
    cov_1gdttvm7jx().s[151]++;
    if (width > maxWidth) {
      cov_1gdttvm7jx().b[38][0]++;
      cov_1gdttvm7jx().s[152]++;
      width = maxWidth;
      cov_1gdttvm7jx().s[153]++;
      height = width / aspectRatio;
    } else {
      cov_1gdttvm7jx().b[38][1]++;
    }
    cov_1gdttvm7jx().s[154]++;
    if (height > maxHeight) {
      cov_1gdttvm7jx().b[39][0]++;
      cov_1gdttvm7jx().s[155]++;
      height = maxHeight;
      cov_1gdttvm7jx().s[156]++;
      width = height * aspectRatio;
    } else {
      cov_1gdttvm7jx().b[39][1]++;
    }
    cov_1gdttvm7jx().s[157]++;
    return {
      width: Math.round(width),
      height: Math.round(height)
    };
  }
});
export var NetworkPerformanceMonitor = function () {
  function NetworkPerformanceMonitor() {
    _classCallCheck(this, NetworkPerformanceMonitor);
    this.requests = (cov_1gdttvm7jx().s[158]++, new Map());
    this.completedRequests = (cov_1gdttvm7jx().s[159]++, []);
    this.maxStoredRequests = (cov_1gdttvm7jx().s[160]++, 100);
  }
  return _createClass(NetworkPerformanceMonitor, [{
    key: "startRequest",
    value: function startRequest(id, url, method) {
      cov_1gdttvm7jx().f[48]++;
      cov_1gdttvm7jx().s[164]++;
      this.requests.set(id, {
        id: id,
        url: url,
        method: method,
        startTime: Date.now(),
        timestamp: new Date().toISOString()
      });
    }
  }, {
    key: "endRequest",
    value: function endRequest(id, status, size) {
      cov_1gdttvm7jx().f[49]++;
      var request = (cov_1gdttvm7jx().s[165]++, this.requests.get(id));
      cov_1gdttvm7jx().s[166]++;
      if (!request) {
        cov_1gdttvm7jx().b[41][0]++;
        cov_1gdttvm7jx().s[167]++;
        return;
      } else {
        cov_1gdttvm7jx().b[41][1]++;
      }
      var endTime = (cov_1gdttvm7jx().s[168]++, Date.now());
      var duration = (cov_1gdttvm7jx().s[169]++, endTime - request.startTime);
      var completedRequest = (cov_1gdttvm7jx().s[170]++, Object.assign({}, request, {
        endTime: endTime,
        duration: duration,
        status: status,
        size: size
      }));
      cov_1gdttvm7jx().s[171]++;
      this.completedRequests.unshift(completedRequest);
      cov_1gdttvm7jx().s[172]++;
      if (this.completedRequests.length > this.maxStoredRequests) {
        cov_1gdttvm7jx().b[42][0]++;
        cov_1gdttvm7jx().s[173]++;
        this.completedRequests.pop();
      } else {
        cov_1gdttvm7jx().b[42][1]++;
      }
      cov_1gdttvm7jx().s[174]++;
      this.requests.delete(id);
      cov_1gdttvm7jx().s[175]++;
      if (duration > 3000) {
        cov_1gdttvm7jx().b[43][0]++;
        cov_1gdttvm7jx().s[176]++;
        console.warn(`Slow network request: ${request.url} took ${duration}ms`);
      } else {
        cov_1gdttvm7jx().b[43][1]++;
      }
    }
  }, {
    key: "getNetworkStats",
    value: function getNetworkStats() {
      cov_1gdttvm7jx().f[50]++;
      var total = (cov_1gdttvm7jx().s[177]++, this.completedRequests.length);
      cov_1gdttvm7jx().s[178]++;
      if (total === 0) {
        cov_1gdttvm7jx().b[44][0]++;
        cov_1gdttvm7jx().s[179]++;
        return {
          averageResponseTime: 0,
          totalRequests: 0,
          failedRequests: 0,
          slowRequests: 0
        };
      } else {
        cov_1gdttvm7jx().b[44][1]++;
      }
      var totalTime = (cov_1gdttvm7jx().s[180]++, this.completedRequests.reduce(function (sum, req) {
        cov_1gdttvm7jx().f[51]++;
        cov_1gdttvm7jx().s[181]++;
        return sum + req.duration;
      }, 0));
      var failed = (cov_1gdttvm7jx().s[182]++, this.completedRequests.filter(function (req) {
        cov_1gdttvm7jx().f[52]++;
        cov_1gdttvm7jx().s[183]++;
        return req.status >= 400;
      }).length);
      var slow = (cov_1gdttvm7jx().s[184]++, this.completedRequests.filter(function (req) {
        cov_1gdttvm7jx().f[53]++;
        cov_1gdttvm7jx().s[185]++;
        return req.duration > 3000;
      }).length);
      cov_1gdttvm7jx().s[186]++;
      return {
        averageResponseTime: Math.round(totalTime / total),
        totalRequests: total,
        failedRequests: failed,
        slowRequests: slow
      };
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      cov_1gdttvm7jx().f[47]++;
      cov_1gdttvm7jx().s[161]++;
      if (!NetworkPerformanceMonitor.instance) {
        cov_1gdttvm7jx().b[40][0]++;
        cov_1gdttvm7jx().s[162]++;
        NetworkPerformanceMonitor.instance = new NetworkPerformanceMonitor();
      } else {
        cov_1gdttvm7jx().b[40][1]++;
      }
      cov_1gdttvm7jx().s[163]++;
      return NetworkPerformanceMonitor.instance;
    }
  }]);
}();
export var ComponentPerformanceTracker = function () {
  function ComponentPerformanceTracker() {
    _classCallCheck(this, ComponentPerformanceTracker);
    this.renderTimes = (cov_1gdttvm7jx().s[187]++, new Map());
    this.mountTimes = (cov_1gdttvm7jx().s[188]++, new Map());
  }
  return _createClass(ComponentPerformanceTracker, [{
    key: "trackMount",
    value: function trackMount(componentName) {
      cov_1gdttvm7jx().f[55]++;
      cov_1gdttvm7jx().s[192]++;
      this.mountTimes.set(componentName, Date.now());
    }
  }, {
    key: "trackRender",
    value: function trackRender(componentName, renderTime) {
      cov_1gdttvm7jx().f[56]++;
      var times = (cov_1gdttvm7jx().s[193]++, (cov_1gdttvm7jx().b[46][0]++, this.renderTimes.get(componentName)) || (cov_1gdttvm7jx().b[46][1]++, []));
      cov_1gdttvm7jx().s[194]++;
      times.push(renderTime);
      cov_1gdttvm7jx().s[195]++;
      if (times.length > 50) {
        cov_1gdttvm7jx().b[47][0]++;
        cov_1gdttvm7jx().s[196]++;
        times.shift();
      } else {
        cov_1gdttvm7jx().b[47][1]++;
      }
      cov_1gdttvm7jx().s[197]++;
      this.renderTimes.set(componentName, times);
      cov_1gdttvm7jx().s[198]++;
      if (renderTime > 16) {
        cov_1gdttvm7jx().b[48][0]++;
        cov_1gdttvm7jx().s[199]++;
        console.warn(`Slow render: ${componentName} took ${renderTime}ms`);
      } else {
        cov_1gdttvm7jx().b[48][1]++;
      }
    }
  }, {
    key: "getComponentStats",
    value: function getComponentStats(componentName) {
      cov_1gdttvm7jx().f[57]++;
      var times = (cov_1gdttvm7jx().s[200]++, (cov_1gdttvm7jx().b[49][0]++, this.renderTimes.get(componentName)) || (cov_1gdttvm7jx().b[49][1]++, []));
      cov_1gdttvm7jx().s[201]++;
      if (times.length === 0) {
        cov_1gdttvm7jx().b[50][0]++;
        cov_1gdttvm7jx().s[202]++;
        return {
          averageRenderTime: 0,
          maxRenderTime: 0,
          renderCount: 0
        };
      } else {
        cov_1gdttvm7jx().b[50][1]++;
      }
      var total = (cov_1gdttvm7jx().s[203]++, times.reduce(function (sum, time) {
        cov_1gdttvm7jx().f[58]++;
        cov_1gdttvm7jx().s[204]++;
        return sum + time;
      }, 0));
      var max = (cov_1gdttvm7jx().s[205]++, Math.max.apply(Math, _toConsumableArray(times)));
      cov_1gdttvm7jx().s[206]++;
      return {
        averageRenderTime: Math.round(total / times.length * 100) / 100,
        maxRenderTime: max,
        renderCount: times.length
      };
    }
  }, {
    key: "getAllStats",
    value: function getAllStats() {
      cov_1gdttvm7jx().f[59]++;
      var stats = (cov_1gdttvm7jx().s[207]++, {});
      cov_1gdttvm7jx().s[208]++;
      for (var _ref3 of this.renderTimes) {
        var _ref4 = _slicedToArray(_ref3, 1);
        var componentName = _ref4[0];
        cov_1gdttvm7jx().s[209]++;
        stats[componentName] = this.getComponentStats(componentName);
      }
      cov_1gdttvm7jx().s[210]++;
      return stats;
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      cov_1gdttvm7jx().f[54]++;
      cov_1gdttvm7jx().s[189]++;
      if (!ComponentPerformanceTracker.instance) {
        cov_1gdttvm7jx().b[45][0]++;
        cov_1gdttvm7jx().s[190]++;
        ComponentPerformanceTracker.instance = new ComponentPerformanceTracker();
      } else {
        cov_1gdttvm7jx().b[45][1]++;
      }
      cov_1gdttvm7jx().s[191]++;
      return ComponentPerformanceTracker.instance;
    }
  }]);
}();
export var bundleAnalyzer = (cov_1gdttvm7jx().s[211]++, {
  logBundleInfo: function logBundleInfo() {
    cov_1gdttvm7jx().f[60]++;
    cov_1gdttvm7jx().s[212]++;
    if (__DEV__) {
      cov_1gdttvm7jx().b[51][0]++;
      cov_1gdttvm7jx().s[213]++;
      console.log('Bundle Analysis:', {
        platform: Platform.OS,
        version: Platform.Version,
        timestamp: new Date().toISOString()
      });
    } else {
      cov_1gdttvm7jx().b[51][1]++;
    }
  },
  trackComponentRender: function trackComponentRender(componentName) {
    cov_1gdttvm7jx().f[61]++;
    cov_1gdttvm7jx().s[214]++;
    if (__DEV__) {
      cov_1gdttvm7jx().b[52][0]++;
      var key = (cov_1gdttvm7jx().s[215]++, `render_count_${componentName}`);
      var count = (cov_1gdttvm7jx().s[216]++, parseInt((cov_1gdttvm7jx().b[53][0]++, global[key]) || (cov_1gdttvm7jx().b[53][1]++, '0'), 10) + 1);
      cov_1gdttvm7jx().s[217]++;
      global[key] = count.toString();
      cov_1gdttvm7jx().s[218]++;
      if (count % 10 === 0) {
        cov_1gdttvm7jx().b[54][0]++;
        cov_1gdttvm7jx().s[219]++;
        console.log(`Component ${componentName} rendered ${count} times`);
      } else {
        cov_1gdttvm7jx().b[54][1]++;
      }
    } else {
      cov_1gdttvm7jx().b[52][1]++;
    }
  }
});
export var AdvancedCache = function () {
  function AdvancedCache() {
    _classCallCheck(this, AdvancedCache);
    this.cache = (cov_1gdttvm7jx().s[220]++, new Map());
    this.expirationTimes = (cov_1gdttvm7jx().s[221]++, new Map());
    this.accessCounts = (cov_1gdttvm7jx().s[222]++, new Map());
    this.maxSize = (cov_1gdttvm7jx().s[223]++, 100);
  }
  return _createClass(AdvancedCache, [{
    key: "set",
    value: function set(key, value) {
      var ttl = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_1gdttvm7jx().b[56][0]++, 300000);
      cov_1gdttvm7jx().f[63]++;
      cov_1gdttvm7jx().s[227]++;
      if (this.cache.size >= this.maxSize) {
        cov_1gdttvm7jx().b[57][0]++;
        cov_1gdttvm7jx().s[228]++;
        this.cleanup();
      } else {
        cov_1gdttvm7jx().b[57][1]++;
      }
      cov_1gdttvm7jx().s[229]++;
      this.cache.set(key, value);
      cov_1gdttvm7jx().s[230]++;
      this.expirationTimes.set(key, Date.now() + ttl);
      cov_1gdttvm7jx().s[231]++;
      this.accessCounts.set(key, 0);
    }
  }, {
    key: "get",
    value: function get(key) {
      cov_1gdttvm7jx().f[64]++;
      var expirationTime = (cov_1gdttvm7jx().s[232]++, this.expirationTimes.get(key));
      cov_1gdttvm7jx().s[233]++;
      if ((cov_1gdttvm7jx().b[59][0]++, expirationTime) && (cov_1gdttvm7jx().b[59][1]++, Date.now() > expirationTime)) {
        cov_1gdttvm7jx().b[58][0]++;
        cov_1gdttvm7jx().s[234]++;
        this.delete(key);
        cov_1gdttvm7jx().s[235]++;
        return null;
      } else {
        cov_1gdttvm7jx().b[58][1]++;
      }
      var accessCount = (cov_1gdttvm7jx().s[236]++, (cov_1gdttvm7jx().b[60][0]++, this.accessCounts.get(key)) || (cov_1gdttvm7jx().b[60][1]++, 0));
      cov_1gdttvm7jx().s[237]++;
      this.accessCounts.set(key, accessCount + 1);
      cov_1gdttvm7jx().s[238]++;
      return this.cache.get(key);
    }
  }, {
    key: "delete",
    value: function _delete(key) {
      cov_1gdttvm7jx().f[65]++;
      cov_1gdttvm7jx().s[239]++;
      this.cache.delete(key);
      cov_1gdttvm7jx().s[240]++;
      this.expirationTimes.delete(key);
      cov_1gdttvm7jx().s[241]++;
      this.accessCounts.delete(key);
    }
  }, {
    key: "clear",
    value: function clear() {
      cov_1gdttvm7jx().f[66]++;
      cov_1gdttvm7jx().s[242]++;
      this.cache.clear();
      cov_1gdttvm7jx().s[243]++;
      this.expirationTimes.clear();
      cov_1gdttvm7jx().s[244]++;
      this.accessCounts.clear();
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      var _this3 = this;
      cov_1gdttvm7jx().f[67]++;
      var now = (cov_1gdttvm7jx().s[245]++, Date.now());
      cov_1gdttvm7jx().s[246]++;
      for (var _ref5 of this.expirationTimes) {
        var _ref6 = _slicedToArray(_ref5, 2);
        var key = _ref6[0];
        var expirationTime = _ref6[1];
        cov_1gdttvm7jx().s[247]++;
        if (now > expirationTime) {
          cov_1gdttvm7jx().b[61][0]++;
          cov_1gdttvm7jx().s[248]++;
          this.delete(key);
        } else {
          cov_1gdttvm7jx().b[61][1]++;
        }
      }
      cov_1gdttvm7jx().s[249]++;
      if (this.cache.size >= this.maxSize) {
        cov_1gdttvm7jx().b[62][0]++;
        var sortedByAccess = (cov_1gdttvm7jx().s[250]++, Array.from(this.accessCounts.entries()).sort(function (_ref7, _ref8) {
          var _ref9 = _slicedToArray(_ref7, 2),
            a = _ref9[1];
          var _ref0 = _slicedToArray(_ref8, 2),
            b = _ref0[1];
          cov_1gdttvm7jx().f[68]++;
          cov_1gdttvm7jx().s[251]++;
          return a - b;
        }));
        var toRemove = (cov_1gdttvm7jx().s[252]++, sortedByAccess.slice(0, Math.floor(this.maxSize * 0.2)));
        cov_1gdttvm7jx().s[253]++;
        toRemove.forEach(function (_ref1) {
          var _ref10 = _slicedToArray(_ref1, 1),
            key = _ref10[0];
          cov_1gdttvm7jx().f[69]++;
          cov_1gdttvm7jx().s[254]++;
          return _this3.delete(key);
        });
      } else {
        cov_1gdttvm7jx().b[62][1]++;
      }
    }
  }, {
    key: "getStats",
    value: function getStats() {
      cov_1gdttvm7jx().f[70]++;
      var totalAccess = (cov_1gdttvm7jx().s[255]++, Array.from(this.accessCounts.values()).reduce(function (sum, count) {
        cov_1gdttvm7jx().f[71]++;
        cov_1gdttvm7jx().s[256]++;
        return sum + count;
      }, 0));
      var mostAccessed = (cov_1gdttvm7jx().s[257]++, Array.from(this.accessCounts.entries()).sort(function (_ref11, _ref12) {
        var _ref13 = _slicedToArray(_ref11, 2),
          a = _ref13[1];
        var _ref14 = _slicedToArray(_ref12, 2),
          b = _ref14[1];
        cov_1gdttvm7jx().f[72]++;
        cov_1gdttvm7jx().s[258]++;
        return b - a;
      }).slice(0, 5).map(function (_ref15) {
        var _ref16 = _slicedToArray(_ref15, 1),
          key = _ref16[0];
        cov_1gdttvm7jx().f[73]++;
        cov_1gdttvm7jx().s[259]++;
        return key;
      }));
      cov_1gdttvm7jx().s[260]++;
      return {
        size: this.cache.size,
        hitRate: totalAccess > 0 ? (cov_1gdttvm7jx().b[63][0]++, this.cache.size / totalAccess * 100) : (cov_1gdttvm7jx().b[63][1]++, 0),
        mostAccessed: mostAccessed
      };
    }
  }], [{
    key: "getInstance",
    value: function getInstance() {
      cov_1gdttvm7jx().f[62]++;
      cov_1gdttvm7jx().s[224]++;
      if (!AdvancedCache.instance) {
        cov_1gdttvm7jx().b[55][0]++;
        cov_1gdttvm7jx().s[225]++;
        AdvancedCache.instance = new AdvancedCache();
      } else {
        cov_1gdttvm7jx().b[55][1]++;
      }
      cov_1gdttvm7jx().s[226]++;
      return AdvancedCache.instance;
    }
  }]);
}();
export var performanceOptimizations = (cov_1gdttvm7jx().s[261]++, {
  lazyLoadComponent: function lazyLoadComponent(importFunc, componentName) {
    cov_1gdttvm7jx().f[74]++;
    var tracker = (cov_1gdttvm7jx().s[262]++, ComponentPerformanceTracker.getInstance());
    cov_1gdttvm7jx().s[263]++;
    return React.lazy(_asyncToGenerator(function* () {
      cov_1gdttvm7jx().f[75]++;
      var startTime = (cov_1gdttvm7jx().s[264]++, Date.now());
      var component = (cov_1gdttvm7jx().s[265]++, yield importFunc());
      var loadTime = (cov_1gdttvm7jx().s[266]++, Date.now() - startTime);
      cov_1gdttvm7jx().s[267]++;
      tracker.trackMount(componentName);
      cov_1gdttvm7jx().s[268]++;
      if (loadTime > 1000) {
        cov_1gdttvm7jx().b[64][0]++;
        cov_1gdttvm7jx().s[269]++;
        console.warn(`Slow component load: ${componentName} took ${loadTime}ms`);
      } else {
        cov_1gdttvm7jx().b[64][1]++;
      }
      cov_1gdttvm7jx().s[270]++;
      return component;
    }));
  },
  optimizeImageLoading: function optimizeImageLoading(imageUrl) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1gdttvm7jx().b[65][0]++, {});
    cov_1gdttvm7jx().f[76]++;
    var _ref18 = (cov_1gdttvm7jx().s[271]++, Dimensions.get('window')),
      width = _ref18.width;
    var pixelRatio = (cov_1gdttvm7jx().s[272]++, Platform.OS === 'web' ? (cov_1gdttvm7jx().b[66][0]++, window.devicePixelRatio) : (cov_1gdttvm7jx().b[66][1]++, 2));
    cov_1gdttvm7jx().s[273]++;
    return {
      uri: imageOptimization.getOptimizedUrl(imageUrl, {
        width: Math.round(width * pixelRatio),
        quality: options.priority ? (cov_1gdttvm7jx().b[67][0]++, 90) : (cov_1gdttvm7jx().b[67][1]++, 75),
        format: 'webp'
      }),
      placeholder: options.lowQualityPlaceholder,
      priority: (cov_1gdttvm7jx().b[68][0]++, options.priority) || (cov_1gdttvm7jx().b[68][1]++, false)
    };
  },
  batchStateUpdates: function batchStateUpdates(updates) {
    var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1gdttvm7jx().b[69][0]++, 0);
    cov_1gdttvm7jx().f[77]++;
    cov_1gdttvm7jx().s[274]++;
    return new Promise(function (resolve) {
      cov_1gdttvm7jx().f[78]++;
      cov_1gdttvm7jx().s[275]++;
      if ((cov_1gdttvm7jx().b[71][0]++, Platform.OS === 'web') && (cov_1gdttvm7jx().b[71][1]++, 'requestIdleCallback' in window)) {
        cov_1gdttvm7jx().b[70][0]++;
        cov_1gdttvm7jx().s[276]++;
        window.requestIdleCallback(function () {
          cov_1gdttvm7jx().f[79]++;
          cov_1gdttvm7jx().s[277]++;
          updates.forEach(function (update) {
            cov_1gdttvm7jx().f[80]++;
            cov_1gdttvm7jx().s[278]++;
            return update();
          });
          cov_1gdttvm7jx().s[279]++;
          resolve();
        });
      } else {
        cov_1gdttvm7jx().b[70][1]++;
        cov_1gdttvm7jx().s[280]++;
        setTimeout(function () {
          cov_1gdttvm7jx().f[81]++;
          cov_1gdttvm7jx().s[281]++;
          updates.forEach(function (update) {
            cov_1gdttvm7jx().f[82]++;
            cov_1gdttvm7jx().s[282]++;
            return update();
          });
          cov_1gdttvm7jx().s[283]++;
          resolve();
        }, delay);
      }
    });
  },
  getVirtualizedProps: function getVirtualizedProps(itemCount, itemHeight) {
    cov_1gdttvm7jx().f[83]++;
    var _ref19 = (cov_1gdttvm7jx().s[284]++, Dimensions.get('window')),
      height = _ref19.height;
    var visibleItems = (cov_1gdttvm7jx().s[285]++, Math.ceil(height / itemHeight) + 2);
    cov_1gdttvm7jx().s[286]++;
    return {
      getItemLayout: function getItemLayout(_, index) {
        cov_1gdttvm7jx().f[84]++;
        cov_1gdttvm7jx().s[287]++;
        return {
          length: itemHeight,
          offset: itemHeight * index,
          index: index
        };
      },
      initialNumToRender: Math.min(visibleItems, itemCount),
      maxToRenderPerBatch: 5,
      windowSize: 10,
      removeClippedSubviews: true
    };
  }
});
export var GlobalPerformanceManager = function () {
  function GlobalPerformanceManager() {
    _classCallCheck(this, GlobalPerformanceManager);
    this.isMonitoring = (cov_1gdttvm7jx().s[288]++, false);
    this.performanceData = (cov_1gdttvm7jx().s[289]++, []);
  }
  return _createClass(GlobalPerformanceManager, [{
    key: "startMonitoring",
    value: function startMonitoring() {
      var _this4 = this;
      cov_1gdttvm7jx().f[86]++;
      cov_1gdttvm7jx().s[293]++;
      if (this.isMonitoring) {
        cov_1gdttvm7jx().b[73][0]++;
        cov_1gdttvm7jx().s[294]++;
        return;
      } else {
        cov_1gdttvm7jx().b[73][1]++;
      }
      cov_1gdttvm7jx().s[295]++;
      this.isMonitoring = true;
      cov_1gdttvm7jx().s[296]++;
      MemoryMonitor.getInstance().startMonitoring();
      cov_1gdttvm7jx().s[297]++;
      performanceMonitor.addObserver(function (metric) {
        cov_1gdttvm7jx().f[87]++;
        cov_1gdttvm7jx().s[298]++;
        _this4.performanceData.push(Object.assign({
          type: 'performance'
        }, metric, {
          timestamp: Date.now()
        }));
      });
      cov_1gdttvm7jx().s[299]++;
      setInterval(function () {
        cov_1gdttvm7jx().f[88]++;
        cov_1gdttvm7jx().s[300]++;
        _this4.cleanup();
        cov_1gdttvm7jx().s[301]++;
        _this4.generateReport();
      }, 60000);
    }
  }, {
    key: "stopMonitoring",
    value: function stopMonitoring() {
      cov_1gdttvm7jx().f[89]++;
      cov_1gdttvm7jx().s[302]++;
      this.isMonitoring = false;
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      cov_1gdttvm7jx().f[90]++;
      cov_1gdttvm7jx().s[303]++;
      if (this.performanceData.length > 1000) {
        cov_1gdttvm7jx().b[74][0]++;
        cov_1gdttvm7jx().s[304]++;
        this.performanceData = this.performanceData.slice(-1000);
      } else {
        cov_1gdttvm7jx().b[74][1]++;
      }
    }
  }, {
    key: "generateReport",
    value: function () {
      var _generateReport = _asyncToGenerator(function* () {
        cov_1gdttvm7jx().f[91]++;
        cov_1gdttvm7jx().s[305]++;
        if (!__DEV__) {
          cov_1gdttvm7jx().b[75][0]++;
          cov_1gdttvm7jx().s[306]++;
          return;
        } else {
          cov_1gdttvm7jx().b[75][1]++;
        }
        var networkStats = (cov_1gdttvm7jx().s[307]++, NetworkPerformanceMonitor.getInstance().getNetworkStats());
        var componentStats = (cov_1gdttvm7jx().s[308]++, ComponentPerformanceTracker.getInstance().getAllStats());
        var cacheStats = (cov_1gdttvm7jx().s[309]++, AdvancedCache.getInstance().getStats());
        var report = (cov_1gdttvm7jx().s[310]++, {
          timestamp: new Date().toISOString(),
          network: networkStats,
          components: componentStats,
          cache: cacheStats,
          performanceEntries: this.performanceData.length
        });
        cov_1gdttvm7jx().s[311]++;
        try {
          cov_1gdttvm7jx().s[312]++;
          yield AsyncStorage.setItem('performance_report', JSON.stringify(report));
        } catch (error) {
          cov_1gdttvm7jx().s[313]++;
          console.warn('Failed to store performance report:', error);
        }
        cov_1gdttvm7jx().s[314]++;
        console.log('Performance Report:', report);
      });
      function generateReport() {
        return _generateReport.apply(this, arguments);
      }
      return generateReport;
    }()
  }, {
    key: "getStoredReports",
    value: function () {
      var _getStoredReports = _asyncToGenerator(function* () {
        cov_1gdttvm7jx().f[92]++;
        cov_1gdttvm7jx().s[315]++;
        try {
          var stored = (cov_1gdttvm7jx().s[316]++, yield AsyncStorage.getItem('performance_report'));
          cov_1gdttvm7jx().s[317]++;
          return stored ? (cov_1gdttvm7jx().b[76][0]++, [JSON.parse(stored)]) : (cov_1gdttvm7jx().b[76][1]++, []);
        } catch (error) {
          cov_1gdttvm7jx().s[318]++;
          console.warn('Failed to retrieve performance reports:', error);
          cov_1gdttvm7jx().s[319]++;
          return [];
        }
      });
      function getStoredReports() {
        return _getStoredReports.apply(this, arguments);
      }
      return getStoredReports;
    }()
  }], [{
    key: "getInstance",
    value: function getInstance() {
      cov_1gdttvm7jx().f[85]++;
      cov_1gdttvm7jx().s[290]++;
      if (!GlobalPerformanceManager.instance) {
        cov_1gdttvm7jx().b[72][0]++;
        cov_1gdttvm7jx().s[291]++;
        GlobalPerformanceManager.instance = new GlobalPerformanceManager();
      } else {
        cov_1gdttvm7jx().b[72][1]++;
      }
      cov_1gdttvm7jx().s[292]++;
      return GlobalPerformanceManager.instance;
    }
  }]);
}();
cov_1gdttvm7jx().s[320]++;
if (__DEV__) {
  cov_1gdttvm7jx().b[77][0]++;
  cov_1gdttvm7jx().s[321]++;
  GlobalPerformanceManager.getInstance().startMonitoring();
} else {
  cov_1gdttvm7jx().b[77][1]++;
}
export var networkMonitor = (cov_1gdttvm7jx().s[322]++, NetworkPerformanceMonitor.getInstance());
export var componentTracker = (cov_1gdttvm7jx().s[323]++, ComponentPerformanceTracker.getInstance());
export var advancedCache = (cov_1gdttvm7jx().s[324]++, AdvancedCache.getInstance());
export var globalPerformanceManager = (cov_1gdttvm7jx().s[325]++, GlobalPerformanceManager.getInstance());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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