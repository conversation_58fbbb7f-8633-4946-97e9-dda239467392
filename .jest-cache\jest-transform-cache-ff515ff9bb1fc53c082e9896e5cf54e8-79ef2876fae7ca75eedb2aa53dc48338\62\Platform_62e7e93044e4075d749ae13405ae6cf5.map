{"version": 3, "names": ["_reactNative", "require", "_browser", "__DEV__", "undefined", "console", "warn", "nativeSelect", "window", "ReactNativePlatform", "select", "specifics", "hasOwnProperty", "native", "default", "Platform", "OS", "isDOMAvailable", "canUseEventListeners", "canUseViewport", "isAsyncDebugging", "_default", "exports"], "sources": ["Platform.ts"], "sourcesContent": ["import { PlatformOSType, Platform as ReactNativePlatform } from 'react-native';\n\nimport {\n  isDOMAvailable,\n  canUseEventListeners,\n  canUseViewport,\n  isAsyncDebugging,\n} from './environment/browser';\n\nexport type PlatformSelectOSType = PlatformOSType | 'native' | 'electron' | 'default';\n\nexport type PlatformSelect = <T>(specifics: { [platform in PlatformSelectOSType]?: T }) => T;\n\nif (__DEV__ && typeof process.env.EXPO_OS === 'undefined') {\n  console.warn(\n    `The global process.env.EXPO_OS is not defined. This should be inlined by babel-preset-expo during transformation.`\n  );\n}\n\nconst nativeSelect =\n  typeof window !== 'undefined'\n    ? ReactNativePlatform.select\n    : // process.env.EXPO_OS is injected by `babel-preset-expo` and available in both client and `react-server` environments.\n      // Opt to use the env var when possible, and fallback to the React Native Platform module when it's not (arbitrary bundlers and transformers).\n      function select<T>(specifics: { [platform in PlatformSelectOSType]?: T }): T | undefined {\n        if (!process.env.EXPO_OS) return undefined;\n        if (specifics.hasOwnProperty(process.env.EXPO_OS)) {\n          return specifics[process.env.EXPO_OS as PlatformSelectOSType]!;\n        } else if (process.env.EXPO_OS !== 'web' && specifics.hasOwnProperty('native')) {\n          return specifics.native!;\n        } else if (specifics.hasOwnProperty('default')) {\n          return specifics.default!;\n        }\n        // do nothing...\n        return undefined;\n      };\n\nconst Platform = {\n  /**\n   * Denotes the currently running platform.\n   * Can be one of ios, android, web.\n   */\n  OS: process.env.EXPO_OS || ReactNativePlatform.OS,\n  /**\n   * Returns the value with the matching platform.\n   * Object keys can be any of ios, android, native, web, default.\n   *\n   * @ios ios, native, default\n   * @android android, native, default\n   * @web web, default\n   */\n  select: nativeSelect as PlatformSelect,\n  /**\n   * Denotes if the DOM API is available in the current environment.\n   * The DOM is not available in native React runtimes and Node.js.\n   */\n  isDOMAvailable,\n  /**\n   * Denotes if the current environment can attach event listeners\n   * to the window. This will return false in native React\n   * runtimes and Node.js.\n   */\n  canUseEventListeners,\n  /**\n   * Denotes if the current environment can inspect properties of the\n   * screen on which the current window is being rendered. This will\n   * return false in native React runtimes and Node.js.\n   */\n  canUseViewport,\n  /**\n   * If the JavaScript is being executed in a remote JavaScript environment.\n   * When `true`, synchronous native invocations cannot be executed.\n   */\n  isAsyncDebugging,\n};\n\nexport default Platform;\n"], "mappings": ";;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,QAAA,GAAAD,OAAA;AAWA,IAAIE,OAAO,IAAI,OAAAC,SAA0B,KAAK,WAAW,EAAE;EACzDC,OAAO,CAACC,IAAI,CACV,mHACF,CAAC;AACH;AAEA,IAAMC,YAAY,GAChB,OAAOC,MAAM,KAAK,WAAW,GACzBC,qBAAmB,CAACC,MAAM,GAG1B,SAASA,MAAMA,CAAIC,SAAqD,EAAiB;EACvF,IAAI,CAAAP,SAAoB,EAAE,OAAOA,SAAS;EAC1C,IAAIO,SAAS,CAACC,cAAc,CAAAR,SAAoB,CAAC,EAAE;IACjD,OAAOO,SAAS,CAAAP,SAAA,CAA6C;EAC/D,CAAC,MAAM,IAAI,QAAiCO,SAAS,CAACC,cAAc,CAAC,QAAQ,CAAC,EAAE;IAC9E,OAAOD,SAAS,CAACE,MAAM;EACzB,CAAC,MAAM,IAAIF,SAAS,CAACC,cAAc,CAAC,SAAS,CAAC,EAAE;IAC9C,OAAOD,SAAS,CAACG,OAAO;EAC1B;EAEA,OAAOV,SAAS;AAClB,CAAC;AAEP,IAAMW,QAAQ,GAAG;EAKfC,EAAE,EAAEZ,SAAA,IAAuBK,qBAAmB,CAACO,EAAE;EASjDN,MAAM,EAAEH,YAA8B;EAKtCU,cAAc,EAAdA,uBAAc;EAMdC,oBAAoB,EAApBA,6BAAoB;EAMpBC,cAAc,EAAdA,uBAAc;EAKdC,gBAAgB,EAAhBA;AACF,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAR,OAAA,GAEaC,QAAQ", "ignoreList": []}