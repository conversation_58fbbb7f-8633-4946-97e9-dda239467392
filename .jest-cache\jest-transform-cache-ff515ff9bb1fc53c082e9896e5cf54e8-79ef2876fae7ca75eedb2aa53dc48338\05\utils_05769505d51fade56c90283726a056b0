a012c24a75298abb947c0dbaa1f04453
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.getLowestCommonAncestor = getLowestCommonAncestor;
exports.getResponderPaths = getResponderPaths;
exports.hasTargetTouches = hasTargetTouches;
exports.hasValidSelection = hasValidSelection;
exports.isPrimaryPointerDown = isPrimaryPointerDown;
exports.setResponderId = setResponderId;
var _isSelectionValid = _interopRequireDefault(require("../../modules/isSelectionValid"));
var keyName = '__reactResponderId';
function getEventPath(domEvent) {
  if (domEvent.type === 'selectionchange') {
    var target = window.getSelection().anchorNode;
    return composedPathFallback(target);
  } else {
    var path = domEvent.composedPath != null ? domEvent.composedPath() : composedPathFallback(domEvent.target);
    return path;
  }
}
function composedPathFallback(target) {
  var path = [];
  while (target != null && target !== document.body) {
    path.push(target);
    target = target.parentNode;
  }
  return path;
}
function getResponderId(node) {
  if (node != null) {
    return node[keyName];
  }
  return null;
}
function setResponderId(node, id) {
  if (node != null) {
    node[keyName] = id;
  }
}
function getResponderPaths(domEvent) {
  var idPath = [];
  var nodePath = [];
  var eventPath = getEventPath(domEvent);
  for (var i = 0; i < eventPath.length; i++) {
    var node = eventPath[i];
    var id = getResponderId(node);
    if (id != null) {
      idPath.push(id);
      nodePath.push(node);
    }
  }
  return {
    idPath: idPath,
    nodePath: nodePath
  };
}
function getLowestCommonAncestor(pathA, pathB) {
  var pathALength = pathA.length;
  var pathBLength = pathB.length;
  if (pathALength === 0 || pathBLength === 0 || pathA[pathALength - 1] !== pathB[pathBLength - 1]) {
    return null;
  }
  var itemA = pathA[0];
  var indexA = 0;
  var itemB = pathB[0];
  var indexB = 0;
  if (pathALength - pathBLength > 0) {
    indexA = pathALength - pathBLength;
    itemA = pathA[indexA];
    pathALength = pathBLength;
  }
  if (pathBLength - pathALength > 0) {
    indexB = pathBLength - pathALength;
    itemB = pathB[indexB];
    pathBLength = pathALength;
  }
  var depth = pathALength;
  while (depth--) {
    if (itemA === itemB) {
      return itemA;
    }
    itemA = pathA[indexA++];
    itemB = pathB[indexB++];
  }
  return null;
}
function hasTargetTouches(target, touches) {
  if (!touches || touches.length === 0) {
    return false;
  }
  for (var i = 0; i < touches.length; i++) {
    var node = touches[i].target;
    if (node != null) {
      if (target.contains(node)) {
        return true;
      }
    }
  }
  return false;
}
function hasValidSelection(domEvent) {
  if (domEvent.type === 'selectionchange') {
    return (0, _isSelectionValid.default)();
  }
  return domEvent.type === 'select';
}
function isPrimaryPointerDown(domEvent) {
  var altKey = domEvent.altKey,
    button = domEvent.button,
    buttons = domEvent.buttons,
    ctrlKey = domEvent.ctrlKey,
    type = domEvent.type;
  var isTouch = type === 'touchstart' || type === 'touchmove';
  var isPrimaryMouseDown = type === 'mousedown' && (button === 0 || buttons === 1);
  var isPrimaryMouseMove = type === 'mousemove' && buttons === 1;
  var noModifiers = altKey === false && ctrlKey === false;
  if (isTouch || isPrimaryMouseDown && noModifiers || isPrimaryMouseMove && noModifiers) {
    return true;
  }
  return false;
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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