04f73f931702621e840cbaaad9d79571
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedValue = _interopRequireDefault(require("../nodes/AnimatedValue"));
var _AnimatedValueXY = _interopRequireDefault(require("../nodes/AnimatedValueXY"));
var _AnimatedInterpolation = _interopRequireDefault(require("../nodes/AnimatedInterpolation"));
var _Easing = _interopRequireDefault(require("../../../../exports/Easing"));
var _Animation = _interopRequireDefault(require("./Animation"));
var _NativeAnimatedHelper = require("../NativeAnimatedHelper");
var _AnimatedColor = _interopRequireDefault(require("../nodes/AnimatedColor"));
var _easeInOut;
function easeInOut() {
  if (!_easeInOut) {
    _easeInOut = _Easing.default.inOut(_Easing.default.ease);
  }
  return _easeInOut;
}
var TimingAnimation = function (_Animation$default) {
  function TimingAnimation(config) {
    var _this;
    (0, _classCallCheck2.default)(this, TimingAnimation);
    var _config$easing, _config$duration, _config$delay, _config$iterations, _config$isInteraction;
    _this = _callSuper(this, TimingAnimation);
    _this._toValue = config.toValue;
    _this._easing = (_config$easing = config.easing) !== null && _config$easing !== void 0 ? _config$easing : easeInOut();
    _this._duration = (_config$duration = config.duration) !== null && _config$duration !== void 0 ? _config$duration : 500;
    _this._delay = (_config$delay = config.delay) !== null && _config$delay !== void 0 ? _config$delay : 0;
    _this.__iterations = (_config$iterations = config.iterations) !== null && _config$iterations !== void 0 ? _config$iterations : 1;
    _this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);
    _this._platformConfig = config.platformConfig;
    _this.__isInteraction = (_config$isInteraction = config.isInteraction) !== null && _config$isInteraction !== void 0 ? _config$isInteraction : !_this._useNativeDriver;
    return _this;
  }
  (0, _inherits2.default)(TimingAnimation, _Animation$default);
  return (0, _createClass2.default)(TimingAnimation, [{
    key: "__getNativeAnimationConfig",
    value: function __getNativeAnimationConfig() {
      var frameDuration = 1000.0 / 60.0;
      var frames = [];
      var numFrames = Math.round(this._duration / frameDuration);
      for (var frame = 0; frame < numFrames; frame++) {
        frames.push(this._easing(frame / numFrames));
      }
      frames.push(this._easing(1));
      return {
        type: 'frames',
        frames: frames,
        toValue: this._toValue,
        iterations: this.__iterations,
        platformConfig: this._platformConfig
      };
    }
  }, {
    key: "start",
    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {
      var _this2 = this;
      this.__active = true;
      this._fromValue = fromValue;
      this._onUpdate = onUpdate;
      this.__onEnd = onEnd;
      var start = function start() {
        if (_this2._duration === 0 && !_this2._useNativeDriver) {
          _this2._onUpdate(_this2._toValue);
          _this2.__debouncedOnEnd({
            finished: true
          });
        } else {
          _this2._startTime = Date.now();
          if (_this2._useNativeDriver) {
            _this2.__startNativeAnimation(animatedValue);
          } else {
            _this2._animationFrame = requestAnimationFrame(_this2.onUpdate.bind(_this2));
          }
        }
      };
      if (this._delay) {
        this._timeout = setTimeout(start, this._delay);
      } else {
        start();
      }
    }
  }, {
    key: "onUpdate",
    value: function onUpdate() {
      var now = Date.now();
      if (now >= this._startTime + this._duration) {
        if (this._duration === 0) {
          this._onUpdate(this._toValue);
        } else {
          this._onUpdate(this._fromValue + this._easing(1) * (this._toValue - this._fromValue));
        }
        this.__debouncedOnEnd({
          finished: true
        });
        return;
      }
      this._onUpdate(this._fromValue + this._easing((now - this._startTime) / this._duration) * (this._toValue - this._fromValue));
      if (this.__active) {
        this._animationFrame = requestAnimationFrame(this.onUpdate.bind(this));
      }
    }
  }, {
    key: "stop",
    value: function stop() {
      _superPropGet(TimingAnimation, "stop", this, 3)([]);
      this.__active = false;
      clearTimeout(this._timeout);
      global.cancelAnimationFrame(this._animationFrame);
      this.__debouncedOnEnd({
        finished: false
      });
    }
  }]);
}(_Animation.default);
var _default = exports.default = TimingAnimation;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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