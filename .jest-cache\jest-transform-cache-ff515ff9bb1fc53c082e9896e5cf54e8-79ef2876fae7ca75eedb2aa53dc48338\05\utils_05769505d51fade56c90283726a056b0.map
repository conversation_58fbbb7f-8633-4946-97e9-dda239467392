{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "getLowestCommonAncestor", "getResponderPaths", "hasTargetTouches", "hasValidSelection", "isPrimaryPointerDown", "setResponderId", "_isSelectionValid", "keyName", "getEventPath", "domEvent", "type", "target", "window", "getSelection", "anchorNode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "<PERSON><PERSON><PERSON>", "document", "body", "push", "parentNode", "getResponderId", "node", "id", "idPath", "nodePath", "eventPath", "i", "length", "pathA", "pathB", "pathALength", "path<PERSON>ength", "itemA", "indexA", "itemB", "indexB", "depth", "touches", "contains", "altKey", "button", "buttons", "ctrl<PERSON>ey", "is<PERSON><PERSON>ch", "isPrimaryMouseDown", "isPrimaryMouseMove", "noModifiers"], "sources": ["utils.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.getLowestCommonAncestor = getLowestCommonAncestor;\nexports.getResponderPaths = getResponderPaths;\nexports.hasTargetTouches = hasTargetTouches;\nexports.hasValidSelection = hasValidSelection;\nexports.isPrimaryPointerDown = isPrimaryPointerDown;\nexports.setResponderId = setResponderId;\nvar _isSelectionValid = _interopRequireDefault(require(\"../../modules/isSelectionValid\"));\n/**\n * Copyright (c) Nicolas <PERSON>\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar keyName = '__reactResponderId';\nfunction getEventPath(domEvent) {\n  // The 'selectionchange' event always has the 'document' as the target.\n  // Use the anchor node as the initial target to reconstruct a path.\n  // (We actually only need the first \"responder\" node in practice.)\n  if (domEvent.type === 'selectionchange') {\n    var target = window.getSelection().anchorNode;\n    return composedPathFallback(target);\n  } else {\n    var path = domEvent.composedPath != null ? domEvent.composedPath() : composedPathFallback(domEvent.target);\n    return path;\n  }\n}\nfunction composedPathFallback(target) {\n  var path = [];\n  while (target != null && target !== document.body) {\n    path.push(target);\n    target = target.parentNode;\n  }\n  return path;\n}\n\n/**\n * Retrieve the responderId from a host node\n */\nfunction getResponderId(node) {\n  if (node != null) {\n    return node[keyName];\n  }\n  return null;\n}\n\n/**\n * Store the responderId on a host node\n */\nfunction setResponderId(node, id) {\n  if (node != null) {\n    node[keyName] = id;\n  }\n}\n\n/**\n * Filter the event path to contain only the nodes attached to the responder system\n */\nfunction getResponderPaths(domEvent) {\n  var idPath = [];\n  var nodePath = [];\n  var eventPath = getEventPath(domEvent);\n  for (var i = 0; i < eventPath.length; i++) {\n    var node = eventPath[i];\n    var id = getResponderId(node);\n    if (id != null) {\n      idPath.push(id);\n      nodePath.push(node);\n    }\n  }\n  return {\n    idPath,\n    nodePath\n  };\n}\n\n/**\n * Walk the paths and find the first common ancestor\n */\nfunction getLowestCommonAncestor(pathA, pathB) {\n  var pathALength = pathA.length;\n  var pathBLength = pathB.length;\n  if (\n  // If either path is empty\n  pathALength === 0 || pathBLength === 0 ||\n  // If the last elements aren't the same there can't be a common ancestor\n  // that is connected to the responder system\n  pathA[pathALength - 1] !== pathB[pathBLength - 1]) {\n    return null;\n  }\n  var itemA = pathA[0];\n  var indexA = 0;\n  var itemB = pathB[0];\n  var indexB = 0;\n\n  // If A is deeper, skip indices that can't match.\n  if (pathALength - pathBLength > 0) {\n    indexA = pathALength - pathBLength;\n    itemA = pathA[indexA];\n    pathALength = pathBLength;\n  }\n\n  // If B is deeper, skip indices that can't match\n  if (pathBLength - pathALength > 0) {\n    indexB = pathBLength - pathALength;\n    itemB = pathB[indexB];\n    pathBLength = pathALength;\n  }\n\n  // Walk in lockstep until a match is found\n  var depth = pathALength;\n  while (depth--) {\n    if (itemA === itemB) {\n      return itemA;\n    }\n    itemA = pathA[indexA++];\n    itemB = pathB[indexB++];\n  }\n  return null;\n}\n\n/**\n * Determine whether any of the active touches are within the current responder.\n * This cannot rely on W3C `targetTouches`, as neither IE11 nor Safari implement it.\n */\nfunction hasTargetTouches(target, touches) {\n  if (!touches || touches.length === 0) {\n    return false;\n  }\n  for (var i = 0; i < touches.length; i++) {\n    var node = touches[i].target;\n    if (node != null) {\n      if (target.contains(node)) {\n        return true;\n      }\n    }\n  }\n  return false;\n}\n\n/**\n * Ignore 'selectionchange' events that don't correspond with a person's intent to\n * select text.\n */\nfunction hasValidSelection(domEvent) {\n  if (domEvent.type === 'selectionchange') {\n    return (0, _isSelectionValid.default)();\n  }\n  return domEvent.type === 'select';\n}\n\n/**\n * Events are only valid if the primary button was used without specific modifier keys.\n */\nfunction isPrimaryPointerDown(domEvent) {\n  var altKey = domEvent.altKey,\n    button = domEvent.button,\n    buttons = domEvent.buttons,\n    ctrlKey = domEvent.ctrlKey,\n    type = domEvent.type;\n  var isTouch = type === 'touchstart' || type === 'touchmove';\n  var isPrimaryMouseDown = type === 'mousedown' && (button === 0 || buttons === 1);\n  var isPrimaryMouseMove = type === 'mousemove' && buttons === 1;\n  var noModifiers = altKey === false && ctrlKey === false;\n  if (isTouch || isPrimaryMouseDown && noModifiers || isPrimaryMouseMove && noModifiers) {\n    return true;\n  }\n  return false;\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,uBAAuB,GAAGA,uBAAuB;AACzDF,OAAO,CAACG,iBAAiB,GAAGA,iBAAiB;AAC7CH,OAAO,CAACI,gBAAgB,GAAGA,gBAAgB;AAC3CJ,OAAO,CAACK,iBAAiB,GAAGA,iBAAiB;AAC7CL,OAAO,CAACM,oBAAoB,GAAGA,oBAAoB;AACnDN,OAAO,CAACO,cAAc,GAAGA,cAAc;AACvC,IAAIC,iBAAiB,GAAGX,sBAAsB,CAACC,OAAO,iCAAiC,CAAC,CAAC;AAUzF,IAAIW,OAAO,GAAG,oBAAoB;AAClC,SAASC,YAAYA,CAACC,QAAQ,EAAE;EAI9B,IAAIA,QAAQ,CAACC,IAAI,KAAK,iBAAiB,EAAE;IACvC,IAAIC,MAAM,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC,CAACC,UAAU;IAC7C,OAAOC,oBAAoB,CAACJ,MAAM,CAAC;EACrC,CAAC,MAAM;IACL,IAAIK,IAAI,GAAGP,QAAQ,CAACQ,YAAY,IAAI,IAAI,GAAGR,QAAQ,CAACQ,YAAY,CAAC,CAAC,GAAGF,oBAAoB,CAACN,QAAQ,CAACE,MAAM,CAAC;IAC1G,OAAOK,IAAI;EACb;AACF;AACA,SAASD,oBAAoBA,CAACJ,MAAM,EAAE;EACpC,IAAIK,IAAI,GAAG,EAAE;EACb,OAAOL,MAAM,IAAI,IAAI,IAAIA,MAAM,KAAKO,QAAQ,CAACC,IAAI,EAAE;IACjDH,IAAI,CAACI,IAAI,CAACT,MAAM,CAAC;IACjBA,MAAM,GAAGA,MAAM,CAACU,UAAU;EAC5B;EACA,OAAOL,IAAI;AACb;AAKA,SAASM,cAAcA,CAACC,IAAI,EAAE;EAC5B,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,OAAOA,IAAI,CAAChB,OAAO,CAAC;EACtB;EACA,OAAO,IAAI;AACb;AAKA,SAASF,cAAcA,CAACkB,IAAI,EAAEC,EAAE,EAAE;EAChC,IAAID,IAAI,IAAI,IAAI,EAAE;IAChBA,IAAI,CAAChB,OAAO,CAAC,GAAGiB,EAAE;EACpB;AACF;AAKA,SAASvB,iBAAiBA,CAACQ,QAAQ,EAAE;EACnC,IAAIgB,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAG,EAAE;EACjB,IAAIC,SAAS,GAAGnB,YAAY,CAACC,QAAQ,CAAC;EACtC,KAAK,IAAImB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC,IAAIL,IAAI,GAAGI,SAAS,CAACC,CAAC,CAAC;IACvB,IAAIJ,EAAE,GAAGF,cAAc,CAACC,IAAI,CAAC;IAC7B,IAAIC,EAAE,IAAI,IAAI,EAAE;MACdC,MAAM,CAACL,IAAI,CAACI,EAAE,CAAC;MACfE,QAAQ,CAACN,IAAI,CAACG,IAAI,CAAC;IACrB;EACF;EACA,OAAO;IACLE,MAAM,EAANA,MAAM;IACNC,QAAQ,EAARA;EACF,CAAC;AACH;AAKA,SAAS1B,uBAAuBA,CAAC8B,KAAK,EAAEC,KAAK,EAAE;EAC7C,IAAIC,WAAW,GAAGF,KAAK,CAACD,MAAM;EAC9B,IAAII,WAAW,GAAGF,KAAK,CAACF,MAAM;EAC9B,IAEAG,WAAW,KAAK,CAAC,IAAIC,WAAW,KAAK,CAAC,IAGtCH,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,KAAKD,KAAK,CAACE,WAAW,GAAG,CAAC,CAAC,EAAE;IACjD,OAAO,IAAI;EACb;EACA,IAAIC,KAAK,GAAGJ,KAAK,CAAC,CAAC,CAAC;EACpB,IAAIK,MAAM,GAAG,CAAC;EACd,IAAIC,KAAK,GAAGL,KAAK,CAAC,CAAC,CAAC;EACpB,IAAIM,MAAM,GAAG,CAAC;EAGd,IAAIL,WAAW,GAAGC,WAAW,GAAG,CAAC,EAAE;IACjCE,MAAM,GAAGH,WAAW,GAAGC,WAAW;IAClCC,KAAK,GAAGJ,KAAK,CAACK,MAAM,CAAC;IACrBH,WAAW,GAAGC,WAAW;EAC3B;EAGA,IAAIA,WAAW,GAAGD,WAAW,GAAG,CAAC,EAAE;IACjCK,MAAM,GAAGJ,WAAW,GAAGD,WAAW;IAClCI,KAAK,GAAGL,KAAK,CAACM,MAAM,CAAC;IACrBJ,WAAW,GAAGD,WAAW;EAC3B;EAGA,IAAIM,KAAK,GAAGN,WAAW;EACvB,OAAOM,KAAK,EAAE,EAAE;IACd,IAAIJ,KAAK,KAAKE,KAAK,EAAE;MACnB,OAAOF,KAAK;IACd;IACAA,KAAK,GAAGJ,KAAK,CAACK,MAAM,EAAE,CAAC;IACvBC,KAAK,GAAGL,KAAK,CAACM,MAAM,EAAE,CAAC;EACzB;EACA,OAAO,IAAI;AACb;AAMA,SAASnC,gBAAgBA,CAACS,MAAM,EAAE4B,OAAO,EAAE;EACzC,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACV,MAAM,KAAK,CAAC,EAAE;IACpC,OAAO,KAAK;EACd;EACA,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,OAAO,CAACV,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAIL,IAAI,GAAGgB,OAAO,CAACX,CAAC,CAAC,CAACjB,MAAM;IAC5B,IAAIY,IAAI,IAAI,IAAI,EAAE;MAChB,IAAIZ,MAAM,CAAC6B,QAAQ,CAACjB,IAAI,CAAC,EAAE;QACzB,OAAO,IAAI;MACb;IACF;EACF;EACA,OAAO,KAAK;AACd;AAMA,SAASpB,iBAAiBA,CAACM,QAAQ,EAAE;EACnC,IAAIA,QAAQ,CAACC,IAAI,KAAK,iBAAiB,EAAE;IACvC,OAAO,CAAC,CAAC,EAAEJ,iBAAiB,CAACT,OAAO,EAAE,CAAC;EACzC;EACA,OAAOY,QAAQ,CAACC,IAAI,KAAK,QAAQ;AACnC;AAKA,SAASN,oBAAoBA,CAACK,QAAQ,EAAE;EACtC,IAAIgC,MAAM,GAAGhC,QAAQ,CAACgC,MAAM;IAC1BC,MAAM,GAAGjC,QAAQ,CAACiC,MAAM;IACxBC,OAAO,GAAGlC,QAAQ,CAACkC,OAAO;IAC1BC,OAAO,GAAGnC,QAAQ,CAACmC,OAAO;IAC1BlC,IAAI,GAAGD,QAAQ,CAACC,IAAI;EACtB,IAAImC,OAAO,GAAGnC,IAAI,KAAK,YAAY,IAAIA,IAAI,KAAK,WAAW;EAC3D,IAAIoC,kBAAkB,GAAGpC,IAAI,KAAK,WAAW,KAAKgC,MAAM,KAAK,CAAC,IAAIC,OAAO,KAAK,CAAC,CAAC;EAChF,IAAII,kBAAkB,GAAGrC,IAAI,KAAK,WAAW,IAAIiC,OAAO,KAAK,CAAC;EAC9D,IAAIK,WAAW,GAAGP,MAAM,KAAK,KAAK,IAAIG,OAAO,KAAK,KAAK;EACvD,IAAIC,OAAO,IAAIC,kBAAkB,IAAIE,WAAW,IAAID,kBAAkB,IAAIC,WAAW,EAAE;IACrF,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd", "ignoreList": []}