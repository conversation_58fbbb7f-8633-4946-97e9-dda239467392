35c9c4fa98522e9cc81c45e568822bc8
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1joafyqxzc() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\profile.tsx";
  var hash = "bd4028e13c1dd4fcb455b5065d831f393016a293";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\profile.tsx",
    statementMap: {
      "0": {
        start: {
          line: 41,
          column: 15
        },
        end: {
          line: 48,
          column: 1
        }
      },
      "1": {
        start: {
          line: 51,
          column: 34
        },
        end: {
          line: 51,
          column: 49
        }
      },
      "2": {
        start: {
          line: 52,
          column: 44
        },
        end: {
          line: 52,
          column: 58
        }
      },
      "3": {
        start: {
          line: 53,
          column: 38
        },
        end: {
          line: 53,
          column: 52
        }
      },
      "4": {
        start: {
          line: 54,
          column: 22
        },
        end: {
          line: 54,
          column: 31
        }
      },
      "5": {
        start: {
          line: 56,
          column: 22
        },
        end: {
          line: 65,
          column: 3
        }
      },
      "6": {
        start: {
          line: 67,
          column: 23
        },
        end: {
          line: 222,
          column: 3
        }
      },
      "7": {
        start: {
          line: 224,
          column: 26
        },
        end: {
          line: 256,
          column: 3
        }
      },
      "8": {
        start: {
          line: 225,
          column: 4
        },
        end: {
          line: 255,
          column: 5
        }
      },
      "9": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 227,
          column: 38
        }
      },
      "10": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 228,
          column: 14
        }
      },
      "11": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 39
        }
      },
      "12": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 14
        }
      },
      "13": {
        start: {
          line: 233,
          column: 8
        },
        end: {
          line: 233,
          column: 38
        }
      },
      "14": {
        start: {
          line: 234,
          column: 8
        },
        end: {
          line: 234,
          column: 14
        }
      },
      "15": {
        start: {
          line: 236,
          column: 8
        },
        end: {
          line: 236,
          column: 53
        }
      },
      "16": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 14
        }
      },
      "17": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 239,
          column: 53
        }
      },
      "18": {
        start: {
          line: 240,
          column: 8
        },
        end: {
          line: 240,
          column: 14
        }
      },
      "19": {
        start: {
          line: 242,
          column: 8
        },
        end: {
          line: 242,
          column: 39
        }
      },
      "20": {
        start: {
          line: 243,
          column: 8
        },
        end: {
          line: 243,
          column: 14
        }
      },
      "21": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 245,
          column: 50
        }
      },
      "22": {
        start: {
          line: 246,
          column: 8
        },
        end: {
          line: 246,
          column: 14
        }
      },
      "23": {
        start: {
          line: 248,
          column: 8
        },
        end: {
          line: 248,
          column: 36
        }
      },
      "24": {
        start: {
          line: 249,
          column: 8
        },
        end: {
          line: 249,
          column: 14
        }
      },
      "25": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 251,
          column: 82
        }
      },
      "26": {
        start: {
          line: 252,
          column: 8
        },
        end: {
          line: 252,
          column: 14
        }
      },
      "27": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 254,
          column: 67
        }
      },
      "28": {
        start: {
          line: 258,
          column: 23
        },
        end: {
          line: 281,
          column: 3
        }
      },
      "29": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 280,
          column: 6
        }
      },
      "30": {
        start: {
          line: 268,
          column: 12
        },
        end: {
          line: 276,
          column: 13
        }
      },
      "31": {
        start: {
          line: 269,
          column: 32
        },
        end: {
          line: 269,
          column: 47
        }
      },
      "32": {
        start: {
          line: 270,
          column: 14
        },
        end: {
          line: 272,
          column: 15
        }
      },
      "33": {
        start: {
          line: 271,
          column: 16
        },
        end: {
          line: 271,
          column: 78
        }
      },
      "34": {
        start: {
          line: 275,
          column: 14
        },
        end: {
          line: 275,
          column: 76
        }
      },
      "35": {
        start: {
          line: 283,
          column: 2
        },
        end: {
          line: 400,
          column: 4
        }
      },
      "36": {
        start: {
          line: 329,
          column: 10
        },
        end: {
          line: 380,
          column: 17
        }
      },
      "37": {
        start: {
          line: 333,
          column: 36
        },
        end: {
          line: 333,
          column: 45
        }
      },
      "38": {
        start: {
          line: 335,
          column: 14
        },
        end: {
          line: 378,
          column: 16
        }
      },
      "39": {
        start: {
          line: 342,
          column: 33
        },
        end: {
          line: 342,
          column: 57
        }
      },
      "40": {
        start: {
          line: 403,
          column: 15
        },
        end: {
          line: 591,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "ProfileScreen",
        decl: {
          start: {
            line: 50,
            column: 24
          },
          end: {
            line: 50,
            column: 37
          }
        },
        loc: {
          start: {
            line: 50,
            column: 40
          },
          end: {
            line: 401,
            column: 1
          }
        },
        line: 50
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 224,
            column: 26
          },
          end: {
            line: 224,
            column: 27
          }
        },
        loc: {
          start: {
            line: 224,
            column: 46
          },
          end: {
            line: 256,
            column: 3
          }
        },
        line: 224
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 258,
            column: 23
          },
          end: {
            line: 258,
            column: 24
          }
        },
        loc: {
          start: {
            line: 258,
            column: 35
          },
          end: {
            line: 281,
            column: 3
          }
        },
        line: 258
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 267,
            column: 19
          },
          end: {
            line: 267,
            column: 20
          }
        },
        loc: {
          start: {
            line: 267,
            column: 31
          },
          end: {
            line: 277,
            column: 11
          }
        },
        line: 267
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 328,
            column: 26
          },
          end: {
            line: 328,
            column: 27
          }
        },
        loc: {
          start: {
            line: 329,
            column: 10
          },
          end: {
            line: 380,
            column: 17
          }
        },
        line: 329
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 332,
            column: 31
          },
          end: {
            line: 332,
            column: 32
          }
        },
        loc: {
          start: {
            line: 332,
            column: 52
          },
          end: {
            line: 379,
            column: 13
          }
        },
        line: 332
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 342,
            column: 27
          },
          end: {
            line: 342,
            column: 28
          }
        },
        loc: {
          start: {
            line: 342,
            column: 33
          },
          end: {
            line: 342,
            column: 57
          }
        },
        line: 342
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 225,
            column: 4
          },
          end: {
            line: 255,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 226,
            column: 6
          },
          end: {
            line: 228,
            column: 14
          }
        }, {
          start: {
            line: 229,
            column: 6
          },
          end: {
            line: 231,
            column: 14
          }
        }, {
          start: {
            line: 232,
            column: 6
          },
          end: {
            line: 234,
            column: 14
          }
        }, {
          start: {
            line: 235,
            column: 6
          },
          end: {
            line: 237,
            column: 14
          }
        }, {
          start: {
            line: 238,
            column: 6
          },
          end: {
            line: 240,
            column: 14
          }
        }, {
          start: {
            line: 241,
            column: 6
          },
          end: {
            line: 243,
            column: 14
          }
        }, {
          start: {
            line: 244,
            column: 6
          },
          end: {
            line: 246,
            column: 14
          }
        }, {
          start: {
            line: 247,
            column: 6
          },
          end: {
            line: 249,
            column: 14
          }
        }, {
          start: {
            line: 250,
            column: 6
          },
          end: {
            line: 252,
            column: 14
          }
        }, {
          start: {
            line: 253,
            column: 6
          },
          end: {
            line: 254,
            column: 67
          }
        }],
        line: 225
      },
      "1": {
        loc: {
          start: {
            line: 270,
            column: 14
          },
          end: {
            line: 272,
            column: 15
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 14
          },
          end: {
            line: 272,
            column: 15
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 270
      },
      "2": {
        loc: {
          start: {
            line: 340,
            column: 20
          },
          end: {
            line: 340,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 340,
            column: 20
          },
          end: {
            line: 340,
            column: 58
          }
        }, {
          start: {
            line: 340,
            column: 62
          },
          end: {
            line: 340,
            column: 81
          }
        }],
        line: 340
      },
      "3": {
        loc: {
          start: {
            line: 352,
            column: 25
          },
          end: {
            line: 356,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 352,
            column: 25
          },
          end: {
            line: 352,
            column: 44
          }
        }, {
          start: {
            line: 353,
            column: 26
          },
          end: {
            line: 355,
            column: 33
          }
        }],
        line: 352
      },
      "4": {
        loc: {
          start: {
            line: 358,
            column: 23
          },
          end: {
            line: 360,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 358,
            column: 23
          },
          end: {
            line: 358,
            column: 45
          }
        }, {
          start: {
            line: 359,
            column: 24
          },
          end: {
            line: 359,
            column: 89
          }
        }],
        line: 358
      },
      "5": {
        loc: {
          start: {
            line: 365,
            column: 21
          },
          end: {
            line: 372,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 365,
            column: 21
          },
          end: {
            line: 365,
            column: 44
          }
        }, {
          start: {
            line: 366,
            column: 22
          },
          end: {
            line: 371,
            column: 24
          }
        }],
        line: 365
      },
      "6": {
        loc: {
          start: {
            line: 373,
            column: 21
          },
          end: {
            line: 375,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 373,
            column: 21
          },
          end: {
            line: 373,
            column: 43
          }
        }, {
          start: {
            line: 374,
            column: 22
          },
          end: {
            line: 374,
            column: 68
          }
        }],
        line: 373
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "bd4028e13c1dd4fcb455b5065d831f393016a293"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1joafyqxzc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1joafyqxzc();
import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, SafeAreaView, TouchableOpacity, Switch, Alert, Image } from 'react-native';
import Card from "../../components/ui/Card";
import Button from "../../components/ui/Button";
import { useAuth } from "../../contexts/AuthContext";
import { router } from 'expo-router';
import { User, Bell, Shield, HelpCircle, Star, ChevronRight, Camera, Moon, Globe, Heart, Crown, Brain, Smartphone, Target, Trophy, Users, CreditCard } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_1joafyqxzc().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
export default function ProfileScreen() {
  cov_1joafyqxzc().f[0]++;
  var _ref = (cov_1joafyqxzc().s[1]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    darkMode = _ref2[0],
    setDarkMode = _ref2[1];
  var _ref3 = (cov_1joafyqxzc().s[2]++, useState(true)),
    _ref4 = _slicedToArray(_ref3, 2),
    notifications = _ref4[0],
    setNotifications = _ref4[1];
  var _ref5 = (cov_1joafyqxzc().s[3]++, useState(true)),
    _ref6 = _slicedToArray(_ref5, 2),
    autoBackup = _ref6[0],
    setAutoBackup = _ref6[1];
  var _ref7 = (cov_1joafyqxzc().s[4]++, useAuth()),
    signOut = _ref7.signOut;
  var userProfile = (cov_1joafyqxzc().s[5]++, {
    name: 'Alex Johnson',
    email: '<EMAIL>',
    memberSince: 'January 2024',
    skillLevel: 'Intermediate',
    subscription: 'Premium',
    totalSessions: 127,
    hoursPlayed: 89,
    improvements: 23
  });
  var menuSections = (cov_1joafyqxzc().s[6]++, [{
    title: 'Account',
    items: [{
      id: 'edit-profile',
      title: 'Edit Profile',
      icon: User,
      hasArrow: true
    }, {
      id: 'subscription',
      title: 'Subscription',
      icon: Crown,
      hasArrow: true,
      badge: 'Premium',
      badgeColor: colors.yellow
    }, {
      id: 'connected-devices',
      title: 'Connected Devices',
      icon: Globe,
      hasArrow: true,
      subtitle: '2 devices connected'
    }]
  }, {
    title: 'Preferences',
    items: [{
      id: 'notifications',
      title: 'Push Notifications',
      icon: Bell,
      hasSwitch: true,
      switchValue: notifications,
      onSwitchChange: setNotifications
    }, {
      id: 'dark-mode',
      title: 'Dark Mode',
      icon: Moon,
      hasSwitch: true,
      switchValue: darkMode,
      onSwitchChange: setDarkMode
    }, {
      id: 'auto-backup',
      title: 'Auto Backup Videos',
      icon: Heart,
      hasSwitch: true,
      switchValue: autoBackup,
      onSwitchChange: setAutoBackup
    }]
  }, {
    title: 'Tennis Features',
    items: [{
      id: 'drills',
      title: 'Drill Library',
      icon: Target,
      hasArrow: true,
      subtitle: 'Browse tennis drills and exercises'
    }, {
      id: 'matches',
      title: 'Match History',
      icon: Trophy,
      hasArrow: true,
      subtitle: 'View detailed match analysis'
    }, {
      id: 'social',
      title: 'Tennis Community',
      icon: Users,
      hasArrow: true,
      subtitle: 'Connect with other players'
    }]
  }, {
    title: 'Features Demo',
    items: [{
      id: 'ai-demo',
      title: 'AI/ML Demo',
      icon: Brain,
      hasArrow: true,
      subtitle: 'Experience AI-powered coaching'
    }, {
      id: 'core-features-demo',
      title: 'Core Features Demo',
      icon: Smartphone,
      hasArrow: true,
      subtitle: 'Test camera, voice, notifications & more'
    }]
  }, {
    title: 'Account',
    items: [{
      id: 'edit-profile',
      title: 'Edit Profile',
      icon: User,
      hasArrow: true,
      subtitle: 'Update your personal information'
    }, {
      id: 'subscription',
      title: 'Subscription',
      icon: CreditCard,
      hasArrow: true,
      subtitle: 'Manage your plan and billing'
    }, {
      id: 'notifications',
      title: 'Notifications',
      icon: Bell,
      hasArrow: true
    }, {
      id: 'privacy',
      title: 'Privacy & Security',
      icon: Shield,
      hasArrow: true
    }]
  }, {
    title: 'Support',
    items: [{
      id: 'help',
      title: 'Help Center',
      icon: HelpCircle,
      hasArrow: true
    }, {
      id: 'feedback',
      title: 'Send Feedback',
      icon: Star,
      hasArrow: true
    }, {
      id: 'privacy',
      title: 'Privacy Policy',
      icon: Shield,
      hasArrow: true
    }]
  }]);
  cov_1joafyqxzc().s[7]++;
  var handleMenuPress = function handleMenuPress(itemId) {
    cov_1joafyqxzc().f[1]++;
    cov_1joafyqxzc().s[8]++;
    switch (itemId) {
      case 'drills':
        cov_1joafyqxzc().b[0][0]++;
        cov_1joafyqxzc().s[9]++;
        router.push('/drills');
        cov_1joafyqxzc().s[10]++;
        break;
      case 'matches':
        cov_1joafyqxzc().b[0][1]++;
        cov_1joafyqxzc().s[11]++;
        router.push('/matches');
        cov_1joafyqxzc().s[12]++;
        break;
      case 'social':
        cov_1joafyqxzc().b[0][2]++;
        cov_1joafyqxzc().s[13]++;
        router.push('/social');
        cov_1joafyqxzc().s[14]++;
        break;
      case 'edit-profile':
        cov_1joafyqxzc().b[0][3]++;
        cov_1joafyqxzc().s[15]++;
        router.push('/settings/edit-profile');
        cov_1joafyqxzc().s[16]++;
        break;
      case 'subscription':
        cov_1joafyqxzc().b[0][4]++;
        cov_1joafyqxzc().s[17]++;
        router.push('/settings/subscription');
        cov_1joafyqxzc().s[18]++;
        break;
      case 'ai-demo':
        cov_1joafyqxzc().b[0][5]++;
        cov_1joafyqxzc().s[19]++;
        router.push('/ai-demo');
        cov_1joafyqxzc().s[20]++;
        break;
      case 'core-features-demo':
        cov_1joafyqxzc().b[0][6]++;
        cov_1joafyqxzc().s[21]++;
        router.push('/core-features-demo');
        cov_1joafyqxzc().s[22]++;
        break;
      case 'help':
        cov_1joafyqxzc().b[0][7]++;
        cov_1joafyqxzc().s[23]++;
        router.push('/help');
        cov_1joafyqxzc().s[24]++;
        break;
      case 'feedback':
        cov_1joafyqxzc().b[0][8]++;
        cov_1joafyqxzc().s[25]++;
        Alert.alert('Feedback', 'Thank you for wanting to help improve AceMind!');
        cov_1joafyqxzc().s[26]++;
        break;
      default:
        cov_1joafyqxzc().b[0][9]++;
        cov_1joafyqxzc().s[27]++;
        Alert.alert('Coming Soon', 'This feature is coming soon!');
    }
  };
  cov_1joafyqxzc().s[28]++;
  var handleLogout = function () {
    var _ref8 = _asyncToGenerator(function* () {
      cov_1joafyqxzc().f[2]++;
      cov_1joafyqxzc().s[29]++;
      Alert.alert('Sign Out', 'Are you sure you want to sign out?', [{
        text: 'Cancel',
        style: 'cancel'
      }, {
        text: 'Sign Out',
        style: 'destructive',
        onPress: function () {
          var _onPress = _asyncToGenerator(function* () {
            cov_1joafyqxzc().f[3]++;
            cov_1joafyqxzc().s[30]++;
            try {
              var _ref9 = (cov_1joafyqxzc().s[31]++, yield signOut()),
                error = _ref9.error;
              cov_1joafyqxzc().s[32]++;
              if (error) {
                cov_1joafyqxzc().b[1][0]++;
                cov_1joafyqxzc().s[33]++;
                Alert.alert('Error', 'Failed to sign out. Please try again.');
              } else {
                cov_1joafyqxzc().b[1][1]++;
              }
            } catch (error) {
              cov_1joafyqxzc().s[34]++;
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          });
          function onPress() {
            return _onPress.apply(this, arguments);
          }
          return onPress;
        }()
      }]);
    });
    return function handleLogout() {
      return _ref8.apply(this, arguments);
    };
  }();
  cov_1joafyqxzc().s[35]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsxs(ScrollView, {
      style: styles.scrollView,
      showsVerticalScrollIndicator: false,
      children: [_jsxs(Card, {
        variant: "elevated",
        style: styles.profileCard,
        children: [_jsxs(View, {
          style: styles.profileHeader,
          children: [_jsxs(View, {
            style: styles.avatarContainer,
            children: [_jsx(Image, {
              source: {
                uri: 'https://images.pexels.com/photos/91227/pexels-photo-91227.jpeg?auto=compress&cs=tinysrgb&w=150'
              },
              style: styles.avatar
            }), _jsx(TouchableOpacity, {
              style: styles.cameraButton,
              children: _jsx(Camera, {
                size: 16,
                color: colors.white
              })
            })]
          }), _jsxs(View, {
            style: styles.profileInfo,
            children: [_jsx(Text, {
              style: styles.userName,
              children: userProfile.name
            }), _jsx(Text, {
              style: styles.userEmail,
              children: userProfile.email
            }), _jsxs(View, {
              style: styles.membershipInfo,
              children: [_jsx(Text, {
                style: styles.skillLevel,
                children: userProfile.skillLevel
              }), _jsxs(Text, {
                style: styles.memberSince,
                children: ["Member since ", userProfile.memberSince]
              })]
            })]
          })]
        }), _jsxs(View, {
          style: styles.statsContainer,
          children: [_jsxs(View, {
            style: styles.statItem,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: userProfile.totalSessions
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Sessions"
            })]
          }), _jsx(View, {
            style: styles.statDivider
          }), _jsxs(View, {
            style: styles.statItem,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: userProfile.hoursPlayed
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Hours"
            })]
          }), _jsx(View, {
            style: styles.statDivider
          }), _jsxs(View, {
            style: styles.statItem,
            children: [_jsx(Text, {
              style: styles.statValue,
              children: userProfile.improvements
            }), _jsx(Text, {
              style: styles.statLabel,
              children: "Improvements"
            })]
          })]
        })]
      }), menuSections.map(function (section, sectionIndex) {
        cov_1joafyqxzc().f[4]++;
        cov_1joafyqxzc().s[36]++;
        return _jsxs(Card, {
          variant: "elevated",
          style: styles.menuCard,
          children: [_jsx(Text, {
            style: styles.sectionTitle,
            children: section.title
          }), section.items.map(function (item, itemIndex) {
            cov_1joafyqxzc().f[5]++;
            var IconComponent = (cov_1joafyqxzc().s[37]++, item.icon);
            cov_1joafyqxzc().s[38]++;
            return _jsxs(TouchableOpacity, {
              style: [styles.menuItem, (cov_1joafyqxzc().b[2][0]++, itemIndex === section.items.length - 1) && (cov_1joafyqxzc().b[2][1]++, styles.lastMenuItem)],
              onPress: function onPress() {
                cov_1joafyqxzc().f[6]++;
                cov_1joafyqxzc().s[39]++;
                return handleMenuPress(item.id);
              },
              disabled: item.hasSwitch,
              children: [_jsxs(View, {
                style: styles.menuItemLeft,
                children: [_jsx(View, {
                  style: styles.menuIcon,
                  children: _jsx(IconComponent, {
                    size: 20,
                    color: colors.gray
                  })
                }), _jsxs(View, {
                  style: styles.menuContent,
                  children: [_jsxs(View, {
                    style: styles.menuTitleContainer,
                    children: [_jsx(Text, {
                      style: styles.menuTitle,
                      children: item.title
                    }), (cov_1joafyqxzc().b[3][0]++, item.badge) && (cov_1joafyqxzc().b[3][1]++, _jsx(View, {
                      style: [styles.badge, {
                        backgroundColor: item.badgeColor
                      }],
                      children: _jsx(Text, {
                        style: styles.badgeText,
                        children: item.badge
                      })
                    }))]
                  }), (cov_1joafyqxzc().b[4][0]++, item.subtitle) && (cov_1joafyqxzc().b[4][1]++, _jsx(Text, {
                    style: styles.menuSubtitle,
                    children: item.subtitle
                  }))]
                })]
              }), _jsxs(View, {
                style: styles.menuItemRight,
                children: [(cov_1joafyqxzc().b[5][0]++, item.hasSwitch) && (cov_1joafyqxzc().b[5][1]++, _jsx(Switch, {
                  value: item.switchValue,
                  onValueChange: item.onSwitchChange,
                  trackColor: {
                    false: colors.lightGray,
                    true: colors.primary
                  },
                  thumbColor: colors.white
                })), (cov_1joafyqxzc().b[6][0]++, item.hasArrow) && (cov_1joafyqxzc().b[6][1]++, _jsx(ChevronRight, {
                  size: 16,
                  color: colors.gray
                }))]
              })]
            }, item.id);
          })]
        }, sectionIndex);
      }), _jsx(View, {
        style: styles.logoutContainer,
        children: _jsx(Button, {
          title: "Sign Out",
          onPress: handleLogout,
          variant: "outline",
          style: styles.logoutButton,
          textStyle: styles.logoutButtonText
        })
      }), _jsx(View, {
        style: styles.versionContainer,
        children: _jsx(Text, {
          style: styles.versionText,
          children: "AceMind v1.0.0"
        })
      })]
    })
  });
}
var styles = (cov_1joafyqxzc().s[40]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray
  },
  scrollView: {
    flex: 1
  },
  profileCard: {
    margin: 24,
    marginBottom: 16
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24
  },
  avatarContainer: {
    position: 'relative',
    marginRight: 16
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    width: 28,
    height: 28,
    borderRadius: 14,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: colors.white
  },
  profileInfo: {
    flex: 1
  },
  userName: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: colors.dark
  },
  userEmail: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4,
    marginBottom: 8
  },
  membershipInfo: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  skillLevel: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: colors.primary,
    backgroundColor: colors.lightGray,
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
    marginRight: 8
  },
  memberSince: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray
  },
  statItem: {
    alignItems: 'center',
    flex: 1
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: colors.dark
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 4
  },
  statDivider: {
    width: 1,
    height: 30,
    backgroundColor: colors.lightGray
  },
  menuCard: {
    marginHorizontal: 24,
    marginBottom: 16
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 16
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  lastMenuItem: {
    borderBottomWidth: 0
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.lightGray,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16
  },
  menuContent: {
    flex: 1
  },
  menuTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center'
  },
  menuTitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: colors.dark
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8,
    marginLeft: 8
  },
  badgeText: {
    fontSize: 12,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  menuSubtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 2
  },
  menuItemRight: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  logoutContainer: {
    margin: 24,
    marginTop: 8
  },
  logoutButton: {
    borderColor: '#ef4444'
  },
  logoutButtonText: {
    color: '#ef4444'
  },
  versionContainer: {
    alignItems: 'center',
    paddingBottom: 32
  },
  versionText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************