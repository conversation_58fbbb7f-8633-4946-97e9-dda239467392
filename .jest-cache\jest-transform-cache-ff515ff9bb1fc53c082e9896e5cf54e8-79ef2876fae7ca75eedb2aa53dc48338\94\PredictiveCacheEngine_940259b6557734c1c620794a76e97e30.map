{"version": 3, "names": ["advancedCacheManager", "performanceMonitor", "PredictiveCacheEngine", "_classCallCheck", "behaviorHistory", "cov_1ao0qgsyza", "s", "Map", "isTraining", "predictionCache", "MODEL_CONFIG", "sequenceLength", "predictionHorizon", "minConfidence", "maxPredictions", "retrainInterval", "f", "mlModel", "SimplePredictionModel", "initializePredictiveEngine", "_createClass", "key", "value", "_initializePredictiveEngine", "_asyncToGenerator", "loadBehaviorHistory", "initialize", "startPeriodicRetraining", "console", "log", "error", "apply", "arguments", "_trackUserBehavior", "userId", "action", "deviceInfo", "contextInfo", "sessionId", "getCurrentSessionId", "has", "b", "set", "userHistory", "get", "currentSession", "find", "p", "actions", "timestamp", "Date", "now", "push", "length", "shift", "updatePredictions", "persistBehaviorData", "trackUserBehavior", "_x", "_x2", "_x3", "_x4", "_getPredictions", "cached", "isPredictionValid", "predictions", "generatePredictions", "getPredictions", "_x5", "_executePredictiveCaching", "_this", "startTime", "highConfidencePredictions", "filter", "confidence", "probability", "sort", "a", "priorityOrder", "high", "medium", "low", "priorityDiff", "priority", "preloadPromises", "slice", "map", "prediction", "preloadData", "Promise", "allSettled", "executionTime", "trackDatabaseQuery", "executePredictiveCaching", "_x6", "getCachingMetrics", "predictionAccuracy", "cacheHitImprovement", "preloadSuccessRate", "averageConfidence", "totalPredictions", "getTotalPredictions", "_generatePredictions", "_recentSessions", "_recentSessions2", "_this2", "recentSessions", "recentActions", "flatMap", "mlPredictions", "predict", "cachePredictions", "dataRequests", "for<PERSON>ach", "request", "generate<PERSON>ache<PERSON>ey", "endpoint", "parameters", "estimatedAccessTime", "Math", "random", "dataSize", "estimateDataSize", "ttl", "calculateOptimalTTL", "screenTransitions", "screen", "screenDataRequests", "getScreenDataRequirements", "dataReq", "timeToTransition", "size", "uniquePredictions", "deduplicatePredictions", "_x7", "_preloadData", "existing", "mockData", "generateMockData", "tags", "_x8", "paramString", "JSON", "stringify", "btoa", "sizeMap", "ttlMap", "screenName", "screenDataMap", "seen", "Set", "add", "oldestPrediction", "min", "_toConsumableArray", "sessionDate", "toISOString", "sessionHour", "floor", "getHours", "_updatePredictions", "delete", "_x9", "_persistBehaviorData", "_x0", "_loadBehaviorHistory", "_this3", "setInterval", "retrainModel", "_retrainModel", "retrain", "Array", "from", "values", "flat", "id", "toString", "data", "generated", "reduce", "sum", "patterns", "transitionMatrix", "_initialize", "_predict", "input", "_ref", "nextActions", "lastAction", "actionSequence", "type", "join", "predictNextActions", "predictDataRequests", "predictScreenTransitions", "_x1", "_retrain", "behaviorData", "_this4", "pattern", "index", "current", "next", "transitions", "_x10", "sequence", "timeToAction", "requestMap", "transitionMap", "time", "t", "predictiveCacheEngine"], "sources": ["PredictiveCacheEngine.ts"], "sourcesContent": ["/**\n * Predictive Cache Engine\n * \n * AI-powered caching system that predicts user behavior and preloads\n * content using machine learning models and behavioral analytics.\n */\n\nimport { advancedCacheManager } from '@/services/caching/AdvancedCacheManager';\nimport { performanceMonitor } from '@/utils/performance';\n\ninterface UserBehaviorPattern {\n  userId: string;\n  sessionId: string;\n  actions: UserAction[];\n  timestamp: number;\n  deviceInfo: DeviceProfile;\n  contextInfo: UserContext;\n}\n\ninterface UserAction {\n  type: 'screen_view' | 'data_request' | 'interaction' | 'search' | 'navigation';\n  target: string;\n  timestamp: number;\n  duration?: number;\n  metadata?: Record<string, any>;\n}\n\ninterface DeviceProfile {\n  platform: 'ios' | 'android' | 'web';\n  model: string;\n  osVersion: string;\n  screenSize: { width: number; height: number };\n  memoryGB: number;\n  cpuCores: number;\n  networkType: '5g' | '4g' | 'wifi' | 'slow';\n  batteryLevel?: number;\n  isLowPowerMode?: boolean;\n}\n\ninterface UserContext {\n  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';\n  dayOfWeek: string;\n  location?: 'home' | 'gym' | 'court' | 'travel';\n  activityLevel: 'low' | 'medium' | 'high';\n  sessionType: 'training' | 'match' | 'analysis' | 'social';\n}\n\ninterface CachePrediction {\n  key: string;\n  probability: number;\n  priority: 'high' | 'medium' | 'low';\n  estimatedAccessTime: number;\n  dataSize: number;\n  ttl: number;\n  confidence: number;\n}\n\ninterface MLModelPrediction {\n  nextActions: Array<{\n    action: string;\n    probability: number;\n    timeToAction: number;\n  }>;\n  dataRequests: Array<{\n    endpoint: string;\n    probability: number;\n    parameters?: Record<string, any>;\n  }>;\n  screenTransitions: Array<{\n    screen: string;\n    probability: number;\n    timeToTransition: number;\n  }>;\n}\n\n/**\n * AI-Powered Predictive Caching Engine\n */\nclass PredictiveCacheEngine {\n  private behaviorHistory: Map<string, UserBehaviorPattern[]> = new Map();\n  private mlModel: SimplePredictionModel;\n  private isTraining = false;\n  private predictionCache: Map<string, CachePrediction[]> = new Map();\n  \n  private readonly MODEL_CONFIG = {\n    sequenceLength: 10, // Look at last 10 actions\n    predictionHorizon: 5, // Predict next 5 actions\n    minConfidence: 0.6, // Minimum confidence for predictions\n    maxPredictions: 20, // Maximum predictions per user\n    retrainInterval: 3600000, // Retrain every hour\n  };\n\n  constructor() {\n    this.mlModel = new SimplePredictionModel();\n    this.initializePredictiveEngine();\n  }\n\n  /**\n   * Initialize the predictive caching engine\n   */\n  private async initializePredictiveEngine(): Promise<void> {\n    try {\n      // Load existing behavior data\n      await this.loadBehaviorHistory();\n      \n      // Initialize ML model\n      await this.mlModel.initialize();\n      \n      // Start periodic retraining\n      this.startPeriodicRetraining();\n      \n      console.log('Predictive Cache Engine initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Predictive Cache Engine:', error);\n    }\n  }\n\n  /**\n   * Track user behavior for ML training\n   */\n  async trackUserBehavior(\n    userId: string,\n    action: UserAction,\n    deviceInfo: DeviceProfile,\n    contextInfo: UserContext\n  ): Promise<void> {\n    try {\n      const sessionId = this.getCurrentSessionId(userId);\n      \n      if (!this.behaviorHistory.has(userId)) {\n        this.behaviorHistory.set(userId, []);\n      }\n      \n      const userHistory = this.behaviorHistory.get(userId)!;\n      let currentSession = userHistory.find(p => p.sessionId === sessionId);\n      \n      if (!currentSession) {\n        currentSession = {\n          userId,\n          sessionId,\n          actions: [],\n          timestamp: Date.now(),\n          deviceInfo,\n          contextInfo,\n        };\n        userHistory.push(currentSession);\n      }\n      \n      // Add action to current session\n      currentSession.actions.push(action);\n      \n      // Limit session size\n      if (currentSession.actions.length > 100) {\n        currentSession.actions.shift();\n      }\n      \n      // Limit user history\n      if (userHistory.length > 50) {\n        userHistory.shift();\n      }\n      \n      // Trigger prediction update\n      await this.updatePredictions(userId);\n      \n      // Persist behavior data\n      await this.persistBehaviorData(userId);\n      \n    } catch (error) {\n      console.error('Failed to track user behavior:', error);\n    }\n  }\n\n  /**\n   * Get cache predictions for a user\n   */\n  async getPredictions(userId: string): Promise<CachePrediction[]> {\n    try {\n      const cached = this.predictionCache.get(userId);\n      if (cached && this.isPredictionValid(cached)) {\n        return cached;\n      }\n      \n      const predictions = await this.generatePredictions(userId);\n      this.predictionCache.set(userId, predictions);\n      \n      return predictions;\n    } catch (error) {\n      console.error('Failed to get predictions:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Execute predictive caching based on ML predictions\n   */\n  async executePredictiveCaching(userId: string): Promise<void> {\n    try {\n      const startTime = Date.now();\n      const predictions = await this.getPredictions(userId);\n      \n      // Filter high-confidence predictions\n      const highConfidencePredictions = predictions.filter(\n        p => p.confidence >= this.MODEL_CONFIG.minConfidence && p.probability > 0.7\n      );\n      \n      // Sort by priority and probability\n      highConfidencePredictions.sort((a, b) => {\n        const priorityOrder = { high: 3, medium: 2, low: 1 };\n        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];\n        if (priorityDiff !== 0) return priorityDiff;\n        return b.probability - a.probability;\n      });\n      \n      // Execute preloading for top predictions\n      const preloadPromises = highConfidencePredictions\n        .slice(0, 10) // Limit to top 10 predictions\n        .map(prediction => this.preloadData(prediction));\n      \n      await Promise.allSettled(preloadPromises);\n      \n      const executionTime = Date.now() - startTime;\n      performanceMonitor.trackDatabaseQuery('predictive_caching', executionTime);\n      \n      console.log(`Executed predictive caching for ${highConfidencePredictions.length} predictions in ${executionTime}ms`);\n      \n    } catch (error) {\n      console.error('Failed to execute predictive caching:', error);\n    }\n  }\n\n  /**\n   * Get caching effectiveness metrics\n   */\n  getCachingMetrics(): {\n    predictionAccuracy: number;\n    cacheHitImprovement: number;\n    preloadSuccessRate: number;\n    averageConfidence: number;\n    totalPredictions: number;\n  } {\n    // In a real implementation, these would be calculated from actual metrics\n    return {\n      predictionAccuracy: 0.85, // 85% accuracy\n      cacheHitImprovement: 0.25, // 25% improvement in cache hits\n      preloadSuccessRate: 0.92, // 92% successful preloads\n      averageConfidence: 0.78, // 78% average confidence\n      totalPredictions: this.getTotalPredictions(),\n    };\n  }\n\n  // Private helper methods\n\n  private async generatePredictions(userId: string): Promise<CachePrediction[]> {\n    const userHistory = this.behaviorHistory.get(userId);\n    if (!userHistory || userHistory.length === 0) {\n      return [];\n    }\n    \n    // Get recent behavior patterns\n    const recentSessions = userHistory.slice(-5); // Last 5 sessions\n    const recentActions = recentSessions.flatMap(s => s.actions).slice(-this.MODEL_CONFIG.sequenceLength);\n    \n    if (recentActions.length < 3) {\n      return []; // Need minimum actions for prediction\n    }\n    \n    // Generate ML predictions\n    const mlPredictions = await this.mlModel.predict({\n      actions: recentActions,\n      deviceInfo: recentSessions[recentSessions.length - 1]?.deviceInfo,\n      contextInfo: recentSessions[recentSessions.length - 1]?.contextInfo,\n    });\n    \n    // Convert ML predictions to cache predictions\n    const cachePredictions: CachePrediction[] = [];\n    \n    // Predict data requests\n    mlPredictions.dataRequests.forEach(request => {\n      if (request.probability > 0.5) {\n        cachePredictions.push({\n          key: this.generateCacheKey(request.endpoint, request.parameters),\n          probability: request.probability,\n          priority: request.probability > 0.8 ? 'high' : request.probability > 0.6 ? 'medium' : 'low',\n          estimatedAccessTime: Date.now() + (Math.random() * 300000), // Within 5 minutes\n          dataSize: this.estimateDataSize(request.endpoint),\n          ttl: this.calculateOptimalTTL(request.endpoint),\n          confidence: request.probability,\n        });\n      }\n    });\n    \n    // Predict screen-based data needs\n    mlPredictions.screenTransitions.forEach(screen => {\n      if (screen.probability > 0.6) {\n        const screenDataRequests = this.getScreenDataRequirements(screen.screen);\n        screenDataRequests.forEach(dataReq => {\n          cachePredictions.push({\n            key: dataReq.key,\n            probability: screen.probability * 0.8, // Slightly lower confidence\n            priority: screen.probability > 0.8 ? 'high' : 'medium',\n            estimatedAccessTime: Date.now() + screen.timeToTransition,\n            dataSize: dataReq.size,\n            ttl: dataReq.ttl,\n            confidence: screen.probability * 0.8,\n          });\n        });\n      }\n    });\n    \n    // Remove duplicates and sort by priority\n    const uniquePredictions = this.deduplicatePredictions(cachePredictions);\n    return uniquePredictions.slice(0, this.MODEL_CONFIG.maxPredictions);\n  }\n\n  private async preloadData(prediction: CachePrediction): Promise<void> {\n    try {\n      // Check if data is already cached\n      const existing = await advancedCacheManager.get(prediction.key);\n      if (existing) {\n        return; // Already cached\n      }\n      \n      // Generate mock data for preloading (in real app, this would fetch actual data)\n      const mockData = this.generateMockData(prediction.key);\n      \n      // Cache the predicted data\n      await advancedCacheManager.set(prediction.key, mockData, {\n        ttl: prediction.ttl,\n        priority: prediction.priority,\n        tags: ['predictive_cache', 'ai_generated'],\n      });\n      \n      console.log(`Preloaded data for key: ${prediction.key} (confidence: ${prediction.confidence})`);\n      \n    } catch (error) {\n      console.error(`Failed to preload data for ${prediction.key}:`, error);\n    }\n  }\n\n  private generateCacheKey(endpoint: string, parameters?: Record<string, any>): string {\n    const paramString = parameters ? JSON.stringify(parameters) : '';\n    return `api_${endpoint}_${btoa(paramString).slice(0, 20)}`;\n  }\n\n  private estimateDataSize(endpoint: string): number {\n    // Estimate data size based on endpoint\n    const sizeMap: Record<string, number> = {\n      'user_profile': 5000,\n      'training_sessions': 15000,\n      'match_results': 10000,\n      'skill_stats': 8000,\n      'leaderboard': 12000,\n      'social_feed': 20000,\n    };\n    \n    return sizeMap[endpoint] || 10000; // Default 10KB\n  }\n\n  private calculateOptimalTTL(endpoint: string): number {\n    // Calculate optimal TTL based on data volatility\n    const ttlMap: Record<string, number> = {\n      'user_profile': 1800000, // 30 minutes\n      'training_sessions': 3600000, // 1 hour\n      'match_results': 7200000, // 2 hours\n      'skill_stats': 1800000, // 30 minutes\n      'leaderboard': 300000, // 5 minutes\n      'social_feed': 600000, // 10 minutes\n    };\n    \n    return ttlMap[endpoint] || 1800000; // Default 30 minutes\n  }\n\n  private getScreenDataRequirements(screenName: string): Array<{\n    key: string;\n    size: number;\n    ttl: number;\n  }> {\n    // Define data requirements for each screen\n    const screenDataMap: Record<string, Array<{ key: string; size: number; ttl: number }>> = {\n      'Dashboard': [\n        { key: 'dashboard_stats', size: 8000, ttl: 1800000 },\n        { key: 'recent_activities', size: 12000, ttl: 600000 },\n      ],\n      'Training': [\n        { key: 'training_programs', size: 15000, ttl: 3600000 },\n        { key: 'progress_data', size: 10000, ttl: 1800000 },\n      ],\n      'Progress': [\n        { key: 'skill_progression', size: 12000, ttl: 1800000 },\n        { key: 'performance_trends', size: 18000, ttl: 3600000 },\n      ],\n      'Social': [\n        { key: 'social_feed', size: 20000, ttl: 600000 },\n        { key: 'friend_activities', size: 15000, ttl: 1200000 },\n      ],\n    };\n    \n    return screenDataMap[screenName] || [];\n  }\n\n  private deduplicatePredictions(predictions: CachePrediction[]): CachePrediction[] {\n    const seen = new Set<string>();\n    return predictions.filter(prediction => {\n      if (seen.has(prediction.key)) {\n        return false;\n      }\n      seen.add(prediction.key);\n      return true;\n    });\n  }\n\n  private isPredictionValid(predictions: CachePrediction[]): boolean {\n    if (predictions.length === 0) return false;\n    \n    // Check if predictions are still relevant (within 5 minutes)\n    const oldestPrediction = Math.min(...predictions.map(p => p.estimatedAccessTime));\n    return Date.now() - oldestPrediction < 300000;\n  }\n\n  private getCurrentSessionId(userId: string): string {\n    // Generate session ID based on user and time\n    const now = new Date();\n    const sessionDate = now.toISOString().slice(0, 10); // YYYY-MM-DD\n    const sessionHour = Math.floor(now.getHours() / 2) * 2; // 2-hour sessions\n    return `${userId}_${sessionDate}_${sessionHour}`;\n  }\n\n  private async updatePredictions(userId: string): Promise<void> {\n    // Invalidate cached predictions to force regeneration\n    this.predictionCache.delete(userId);\n  }\n\n  private async persistBehaviorData(userId: string): Promise<void> {\n    // In a real implementation, this would persist to a database\n    // For now, we'll just log the action\n    console.log(`Persisted behavior data for user: ${userId}`);\n  }\n\n  private async loadBehaviorHistory(): Promise<void> {\n    // In a real implementation, this would load from a database\n    console.log('Loaded behavior history from storage');\n  }\n\n  private startPeriodicRetraining(): void {\n    setInterval(() => {\n      if (!this.isTraining) {\n        this.retrainModel();\n      }\n    }, this.MODEL_CONFIG.retrainInterval);\n  }\n\n  private async retrainModel(): Promise<void> {\n    this.isTraining = true;\n    try {\n      console.log('Starting ML model retraining...');\n      await this.mlModel.retrain(Array.from(this.behaviorHistory.values()).flat());\n      console.log('ML model retraining completed');\n    } catch (error) {\n      console.error('Failed to retrain ML model:', error);\n    } finally {\n      this.isTraining = false;\n    }\n  }\n\n  private generateMockData(key: string): any {\n    // Generate mock data based on cache key\n    return {\n      id: Math.random().toString(36),\n      data: `Mock data for ${key}`,\n      timestamp: Date.now(),\n      generated: true,\n    };\n  }\n\n  private getTotalPredictions(): number {\n    return Array.from(this.predictionCache.values()).reduce((sum, predictions) => sum + predictions.length, 0);\n  }\n}\n\n/**\n * Simple ML Prediction Model (placeholder for more sophisticated models)\n */\nclass SimplePredictionModel {\n  private patterns: Map<string, number> = new Map();\n  private transitionMatrix: Map<string, Map<string, number>> = new Map();\n\n  async initialize(): Promise<void> {\n    console.log('Initializing simple prediction model');\n  }\n\n  async predict(input: {\n    actions: UserAction[];\n    deviceInfo?: DeviceProfile;\n    contextInfo?: UserContext;\n  }): Promise<MLModelPrediction> {\n    const { actions } = input;\n    \n    if (actions.length === 0) {\n      return { nextActions: [], dataRequests: [], screenTransitions: [] };\n    }\n    \n    // Simple pattern-based prediction\n    const lastAction = actions[actions.length - 1];\n    const actionSequence = actions.slice(-3).map(a => a.type).join('->');\n    \n    // Generate predictions based on simple heuristics\n    const nextActions = this.predictNextActions(lastAction, actionSequence);\n    const dataRequests = this.predictDataRequests(lastAction);\n    const screenTransitions = this.predictScreenTransitions(lastAction);\n    \n    return { nextActions, dataRequests, screenTransitions };\n  }\n\n  async retrain(behaviorData: UserBehaviorPattern[]): Promise<void> {\n    console.log(`Retraining model with ${behaviorData.length} behavior patterns`);\n    \n    // Simple pattern learning\n    behaviorData.forEach(pattern => {\n      pattern.actions.forEach((action, index) => {\n        if (index < pattern.actions.length - 1) {\n          const current = action.type;\n          const next = pattern.actions[index + 1].type;\n          \n          if (!this.transitionMatrix.has(current)) {\n            this.transitionMatrix.set(current, new Map());\n          }\n          \n          const transitions = this.transitionMatrix.get(current)!;\n          transitions.set(next, (transitions.get(next) || 0) + 1);\n        }\n      });\n    });\n  }\n\n  private predictNextActions(lastAction: UserAction, sequence: string): Array<{\n    action: string;\n    probability: number;\n    timeToAction: number;\n  }> {\n    // Simple prediction based on common patterns\n    const predictions = [\n      { action: 'screen_view', probability: 0.7, timeToAction: 2000 },\n      { action: 'data_request', probability: 0.6, timeToAction: 1000 },\n      { action: 'interaction', probability: 0.8, timeToAction: 3000 },\n    ];\n    \n    return predictions;\n  }\n\n  private predictDataRequests(lastAction: UserAction): Array<{\n    endpoint: string;\n    probability: number;\n    parameters?: Record<string, any>;\n  }> {\n    // Predict likely data requests based on last action\n    const requestMap: Record<string, Array<{ endpoint: string; probability: number }>> = {\n      'screen_view': [\n        { endpoint: 'user_profile', probability: 0.8 },\n        { endpoint: 'recent_activities', probability: 0.7 },\n      ],\n      'navigation': [\n        { endpoint: 'training_sessions', probability: 0.9 },\n        { endpoint: 'progress_data', probability: 0.6 },\n      ],\n      'interaction': [\n        { endpoint: 'skill_stats', probability: 0.8 },\n        { endpoint: 'performance_trends', probability: 0.5 },\n      ],\n    };\n    \n    return requestMap[lastAction.type] || [];\n  }\n\n  private predictScreenTransitions(lastAction: UserAction): Array<{\n    screen: string;\n    probability: number;\n    timeToTransition: number;\n  }> {\n    // Predict likely screen transitions\n    const transitionMap: Record<string, Array<{ screen: string; probability: number; time: number }>> = {\n      'screen_view': [\n        { screen: 'Training', probability: 0.6, time: 5000 },\n        { screen: 'Progress', probability: 0.4, time: 8000 },\n      ],\n      'navigation': [\n        { screen: 'Dashboard', probability: 0.7, time: 3000 },\n        { screen: 'Social', probability: 0.3, time: 10000 },\n      ],\n    };\n    \n    const transitions = transitionMap[lastAction.type] || [];\n    return transitions.map(t => ({\n      screen: t.screen,\n      probability: t.probability,\n      timeToTransition: t.time,\n    }));\n  }\n}\n\n// Export singleton instance\nexport const predictiveCacheEngine = new PredictiveCacheEngine();\nexport default predictiveCacheEngine;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,oBAAoB;AAC7B,SAASC,kBAAkB;AAA8B,IAsEnDC,qBAAqB;EAczB,SAAAA,sBAAA,EAAc;IAAAC,eAAA,OAAAD,qBAAA;IAAA,KAbNE,eAAe,IAAAC,cAAA,GAAAC,CAAA,OAAuC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAE/DC,UAAU,IAAAH,cAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KAClBG,eAAe,IAAAJ,cAAA,GAAAC,CAAA,OAAmC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAElDG,YAAY,IAAAL,cAAA,GAAAC,CAAA,OAAG;MAC9BK,cAAc,EAAE,EAAE;MAClBC,iBAAiB,EAAE,CAAC;MACpBC,aAAa,EAAE,GAAG;MAClBC,cAAc,EAAE,EAAE;MAClBC,eAAe,EAAE;IACnB,CAAC;IAAAV,cAAA,GAAAW,CAAA;IAAAX,cAAA,GAAAC,CAAA;IAGC,IAAI,CAACW,OAAO,GAAG,IAAIC,qBAAqB,CAAC,CAAC;IAACb,cAAA,GAAAC,CAAA;IAC3C,IAAI,CAACa,0BAA0B,CAAC,CAAC;EACnC;EAAC,OAAAC,YAAA,CAAAlB,qBAAA;IAAAmB,GAAA;IAAAC,KAAA;MAAA,IAAAC,2BAAA,GAAAC,iBAAA,CAKD,aAA0D;QAAAnB,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QACxD,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF,MAAM,IAAI,CAACmB,mBAAmB,CAAC,CAAC;UAACpB,cAAA,GAAAC,CAAA;UAGjC,MAAM,IAAI,CAACW,OAAO,CAACS,UAAU,CAAC,CAAC;UAACrB,cAAA,GAAAC,CAAA;UAGhC,IAAI,CAACqB,uBAAuB,CAAC,CAAC;UAACtB,cAAA,GAAAC,CAAA;UAE/BsB,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;QACjE,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAzB,cAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;QACvE;MACF,CAAC;MAAA,SAfaX,0BAA0BA,CAAA;QAAA,OAAAI,2BAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA1Bb,0BAA0B;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA;MAAA,IAAAW,kBAAA,GAAAT,iBAAA,CAoBxC,WACEU,MAAc,EACdC,MAAkB,EAClBC,UAAyB,EACzBC,WAAwB,EACT;QAAAhC,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QACf,IAAI;UACF,IAAMgC,SAAS,IAAAjC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACiC,mBAAmB,CAACL,MAAM,CAAC;UAAC7B,cAAA,GAAAC,CAAA;UAEnD,IAAI,CAAC,IAAI,CAACF,eAAe,CAACoC,GAAG,CAACN,MAAM,CAAC,EAAE;YAAA7B,cAAA,GAAAoC,CAAA;YAAApC,cAAA,GAAAC,CAAA;YACrC,IAAI,CAACF,eAAe,CAACsC,GAAG,CAACR,MAAM,EAAE,EAAE,CAAC;UACtC,CAAC;YAAA7B,cAAA,GAAAoC,CAAA;UAAA;UAED,IAAME,WAAW,IAAAtC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,eAAe,CAACwC,GAAG,CAACV,MAAM,CAAC,CAAC;UACrD,IAAIW,cAAc,IAAAxC,cAAA,GAAAC,CAAA,QAAGqC,WAAW,CAACG,IAAI,CAAC,UAAAC,CAAC,EAAI;YAAA1C,cAAA,GAAAW,CAAA;YAAAX,cAAA,GAAAC,CAAA;YAAA,OAAAyC,CAAC,CAACT,SAAS,KAAKA,SAAS;UAAD,CAAC,CAAC;UAACjC,cAAA,GAAAC,CAAA;UAEtE,IAAI,CAACuC,cAAc,EAAE;YAAAxC,cAAA,GAAAoC,CAAA;YAAApC,cAAA,GAAAC,CAAA;YACnBuC,cAAc,GAAG;cACfX,MAAM,EAANA,MAAM;cACNI,SAAS,EAATA,SAAS;cACTU,OAAO,EAAE,EAAE;cACXC,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;cACrBf,UAAU,EAAVA,UAAU;cACVC,WAAW,EAAXA;YACF,CAAC;YAAChC,cAAA,GAAAC,CAAA;YACFqC,WAAW,CAACS,IAAI,CAACP,cAAc,CAAC;UAClC,CAAC;YAAAxC,cAAA,GAAAoC,CAAA;UAAA;UAAApC,cAAA,GAAAC,CAAA;UAGDuC,cAAc,CAACG,OAAO,CAACI,IAAI,CAACjB,MAAM,CAAC;UAAC9B,cAAA,GAAAC,CAAA;UAGpC,IAAIuC,cAAc,CAACG,OAAO,CAACK,MAAM,GAAG,GAAG,EAAE;YAAAhD,cAAA,GAAAoC,CAAA;YAAApC,cAAA,GAAAC,CAAA;YACvCuC,cAAc,CAACG,OAAO,CAACM,KAAK,CAAC,CAAC;UAChC,CAAC;YAAAjD,cAAA,GAAAoC,CAAA;UAAA;UAAApC,cAAA,GAAAC,CAAA;UAGD,IAAIqC,WAAW,CAACU,MAAM,GAAG,EAAE,EAAE;YAAAhD,cAAA,GAAAoC,CAAA;YAAApC,cAAA,GAAAC,CAAA;YAC3BqC,WAAW,CAACW,KAAK,CAAC,CAAC;UACrB,CAAC;YAAAjD,cAAA,GAAAoC,CAAA;UAAA;UAAApC,cAAA,GAAAC,CAAA;UAGD,MAAM,IAAI,CAACiD,iBAAiB,CAACrB,MAAM,CAAC;UAAC7B,cAAA,GAAAC,CAAA;UAGrC,MAAM,IAAI,CAACkD,mBAAmB,CAACtB,MAAM,CAAC;QAExC,CAAC,CAAC,OAAOJ,KAAK,EAAE;UAAAzB,cAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACxD;MACF,CAAC;MAAA,SAlDK2B,iBAAiBA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAA5B,kBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjByB,iBAAiB;IAAA;EAAA;IAAApC,GAAA;IAAAC,KAAA;MAAA,IAAAwC,eAAA,GAAAtC,iBAAA,CAuDvB,WAAqBU,MAAc,EAA8B;QAAA7B,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAC/D,IAAI;UACF,IAAMyD,MAAM,IAAA1D,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACG,eAAe,CAACmC,GAAG,CAACV,MAAM,CAAC;UAAC7B,cAAA,GAAAC,CAAA;UAChD,IAAI,CAAAD,cAAA,GAAAoC,CAAA,UAAAsB,MAAM,MAAA1D,cAAA,GAAAoC,CAAA,UAAI,IAAI,CAACuB,iBAAiB,CAACD,MAAM,CAAC,GAAE;YAAA1D,cAAA,GAAAoC,CAAA;YAAApC,cAAA,GAAAC,CAAA;YAC5C,OAAOyD,MAAM;UACf,CAAC;YAAA1D,cAAA,GAAAoC,CAAA;UAAA;UAED,IAAMwB,WAAW,IAAA5D,cAAA,GAAAC,CAAA,cAAS,IAAI,CAAC4D,mBAAmB,CAAChC,MAAM,CAAC;UAAC7B,cAAA,GAAAC,CAAA;UAC3D,IAAI,CAACG,eAAe,CAACiC,GAAG,CAACR,MAAM,EAAE+B,WAAW,CAAC;UAAC5D,cAAA,GAAAC,CAAA;UAE9C,OAAO2D,WAAW;QACpB,CAAC,CAAC,OAAOnC,KAAK,EAAE;UAAAzB,cAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAACzB,cAAA,GAAAC,CAAA;UACnD,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SAfK6D,cAAcA,CAAAC,GAAA;QAAA,OAAAN,eAAA,CAAA/B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdmC,cAAc;IAAA;EAAA;IAAA9C,GAAA;IAAAC,KAAA;MAAA,IAAA+C,yBAAA,GAAA7C,iBAAA,CAoBpB,WAA+BU,MAAc,EAAiB;QAAA,IAAAoC,KAAA;QAAAjE,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAC5D,IAAI;UACF,IAAMiE,SAAS,IAAAlE,cAAA,GAAAC,CAAA,QAAG4C,IAAI,CAACC,GAAG,CAAC,CAAC;UAC5B,IAAMc,WAAW,IAAA5D,cAAA,GAAAC,CAAA,cAAS,IAAI,CAAC6D,cAAc,CAACjC,MAAM,CAAC;UAGrD,IAAMsC,yBAAyB,IAAAnE,cAAA,GAAAC,CAAA,QAAG2D,WAAW,CAACQ,MAAM,CAClD,UAAA1B,CAAC,EAAI;YAAA1C,cAAA,GAAAW,CAAA;YAAAX,cAAA,GAAAC,CAAA;YAAA,QAAAD,cAAA,GAAAoC,CAAA,UAAAM,CAAC,CAAC2B,UAAU,IAAIJ,KAAI,CAAC5D,YAAY,CAACG,aAAa,MAAAR,cAAA,GAAAoC,CAAA,UAAIM,CAAC,CAAC4B,WAAW,GAAG,GAAG;UAAD,CAC5E,CAAC;UAACtE,cAAA,GAAAC,CAAA;UAGFkE,yBAAyB,CAACI,IAAI,CAAC,UAACC,CAAC,EAAEpC,CAAC,EAAK;YAAApC,cAAA,GAAAW,CAAA;YACvC,IAAM8D,aAAa,IAAAzE,cAAA,GAAAC,CAAA,QAAG;cAAEyE,IAAI,EAAE,CAAC;cAAEC,MAAM,EAAE,CAAC;cAAEC,GAAG,EAAE;YAAE,CAAC;YACpD,IAAMC,YAAY,IAAA7E,cAAA,GAAAC,CAAA,QAAGwE,aAAa,CAACrC,CAAC,CAAC0C,QAAQ,CAAC,GAAGL,aAAa,CAACD,CAAC,CAACM,QAAQ,CAAC;YAAC9E,cAAA,GAAAC,CAAA;YAC3E,IAAI4E,YAAY,KAAK,CAAC,EAAE;cAAA7E,cAAA,GAAAoC,CAAA;cAAApC,cAAA,GAAAC,CAAA;cAAA,OAAO4E,YAAY;YAAA,CAAC;cAAA7E,cAAA,GAAAoC,CAAA;YAAA;YAAApC,cAAA,GAAAC,CAAA;YAC5C,OAAOmC,CAAC,CAACkC,WAAW,GAAGE,CAAC,CAACF,WAAW;UACtC,CAAC,CAAC;UAGF,IAAMS,eAAe,IAAA/E,cAAA,GAAAC,CAAA,QAAGkE,yBAAyB,CAC9Ca,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CACZC,GAAG,CAAC,UAAAC,UAAU,EAAI;YAAAlF,cAAA,GAAAW,CAAA;YAAAX,cAAA,GAAAC,CAAA;YAAA,OAAAgE,KAAI,CAACkB,WAAW,CAACD,UAAU,CAAC;UAAD,CAAC,CAAC;UAAClF,cAAA,GAAAC,CAAA;UAEnD,MAAMmF,OAAO,CAACC,UAAU,CAACN,eAAe,CAAC;UAEzC,IAAMO,aAAa,IAAAtF,cAAA,GAAAC,CAAA,QAAG4C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGoB,SAAS;UAAClE,cAAA,GAAAC,CAAA;UAC7CL,kBAAkB,CAAC2F,kBAAkB,CAAC,oBAAoB,EAAED,aAAa,CAAC;UAACtF,cAAA,GAAAC,CAAA;UAE3EsB,OAAO,CAACC,GAAG,CAAC,mCAAmC2C,yBAAyB,CAACnB,MAAM,mBAAmBsC,aAAa,IAAI,CAAC;QAEtH,CAAC,CAAC,OAAO7D,KAAK,EAAE;UAAAzB,cAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC/D;MACF,CAAC;MAAA,SAjCK+D,wBAAwBA,CAAAC,GAAA;QAAA,OAAAzB,yBAAA,CAAAtC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAxB6D,wBAAwB;IAAA;EAAA;IAAAxE,GAAA;IAAAC,KAAA,EAsC9B,SAAAyE,iBAAiBA,CAAA,EAMf;MAAA1F,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAC,CAAA;MAEA,OAAO;QACL0F,kBAAkB,EAAE,IAAI;QACxBC,mBAAmB,EAAE,IAAI;QACzBC,kBAAkB,EAAE,IAAI;QACxBC,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI,CAACC,mBAAmB,CAAC;MAC7C,CAAC;IACH;EAAC;IAAAhF,GAAA;IAAAC,KAAA;MAAA,IAAAgF,oBAAA,GAAA9E,iBAAA,CAID,WAAkCU,MAAc,EAA8B;QAAA,IAAAqE,eAAA;UAAAC,gBAAA;UAAAC,MAAA;QAAApG,cAAA,GAAAW,CAAA;QAC5E,IAAM2B,WAAW,IAAAtC,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,eAAe,CAACwC,GAAG,CAACV,MAAM,CAAC;QAAC7B,cAAA,GAAAC,CAAA;QACrD,IAAI,CAAAD,cAAA,GAAAoC,CAAA,WAACE,WAAW,MAAAtC,cAAA,GAAAoC,CAAA,UAAIE,WAAW,CAACU,MAAM,KAAK,CAAC,GAAE;UAAAhD,cAAA,GAAAoC,CAAA;UAAApC,cAAA,GAAAC,CAAA;UAC5C,OAAO,EAAE;QACX,CAAC;UAAAD,cAAA,GAAAoC,CAAA;QAAA;QAGD,IAAMiE,cAAc,IAAArG,cAAA,GAAAC,CAAA,QAAGqC,WAAW,CAAC0C,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5C,IAAMsB,aAAa,IAAAtG,cAAA,GAAAC,CAAA,QAAGoG,cAAc,CAACE,OAAO,CAAC,UAAAtG,CAAC,EAAI;UAAAD,cAAA,GAAAW,CAAA;UAAAX,cAAA,GAAAC,CAAA;UAAA,OAAAA,CAAC,CAAC0C,OAAO;QAAD,CAAC,CAAC,CAACqC,KAAK,CAAC,CAAC,IAAI,CAAC3E,YAAY,CAACC,cAAc,CAAC;QAACN,cAAA,GAAAC,CAAA;QAEtG,IAAIqG,aAAa,CAACtD,MAAM,GAAG,CAAC,EAAE;UAAAhD,cAAA,GAAAoC,CAAA;UAAApC,cAAA,GAAAC,CAAA;UAC5B,OAAO,EAAE;QACX,CAAC;UAAAD,cAAA,GAAAoC,CAAA;QAAA;QAGD,IAAMoE,aAAa,IAAAxG,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACW,OAAO,CAAC6F,OAAO,CAAC;UAC/C9D,OAAO,EAAE2D,aAAa;UACtBvE,UAAU,GAAAmE,eAAA,GAAEG,cAAc,CAACA,cAAc,CAACrD,MAAM,GAAG,CAAC,CAAC,qBAAzCkD,eAAA,CAA2CnE,UAAU;UACjEC,WAAW,GAAAmE,gBAAA,GAAEE,cAAc,CAACA,cAAc,CAACrD,MAAM,GAAG,CAAC,CAAC,qBAAzCmD,gBAAA,CAA2CnE;QAC1D,CAAC,CAAC;QAGF,IAAM0E,gBAAmC,IAAA1G,cAAA,GAAAC,CAAA,QAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAG/CuG,aAAa,CAACG,YAAY,CAACC,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAA7G,cAAA,GAAAW,CAAA;UAAAX,cAAA,GAAAC,CAAA;UAC5C,IAAI4G,OAAO,CAACvC,WAAW,GAAG,GAAG,EAAE;YAAAtE,cAAA,GAAAoC,CAAA;YAAApC,cAAA,GAAAC,CAAA;YAC7ByG,gBAAgB,CAAC3D,IAAI,CAAC;cACpB/B,GAAG,EAAEoF,MAAI,CAACU,gBAAgB,CAACD,OAAO,CAACE,QAAQ,EAAEF,OAAO,CAACG,UAAU,CAAC;cAChE1C,WAAW,EAAEuC,OAAO,CAACvC,WAAW;cAChCQ,QAAQ,EAAE+B,OAAO,CAACvC,WAAW,GAAG,GAAG,IAAAtE,cAAA,GAAAoC,CAAA,WAAG,MAAM,KAAApC,cAAA,GAAAoC,CAAA,WAAGyE,OAAO,CAACvC,WAAW,GAAG,GAAG,IAAAtE,cAAA,GAAAoC,CAAA,WAAG,QAAQ,KAAApC,cAAA,GAAAoC,CAAA,WAAG,KAAK;cAC3F6E,mBAAmB,EAAEpE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIoE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,MAAO;cAC1DC,QAAQ,EAAEhB,MAAI,CAACiB,gBAAgB,CAACR,OAAO,CAACE,QAAQ,CAAC;cACjDO,GAAG,EAAElB,MAAI,CAACmB,mBAAmB,CAACV,OAAO,CAACE,QAAQ,CAAC;cAC/C1C,UAAU,EAAEwC,OAAO,CAACvC;YACtB,CAAC,CAAC;UACJ,CAAC;YAAAtE,cAAA,GAAAoC,CAAA;UAAA;QACH,CAAC,CAAC;QAACpC,cAAA,GAAAC,CAAA;QAGHuG,aAAa,CAACgB,iBAAiB,CAACZ,OAAO,CAAC,UAAAa,MAAM,EAAI;UAAAzH,cAAA,GAAAW,CAAA;UAAAX,cAAA,GAAAC,CAAA;UAChD,IAAIwH,MAAM,CAACnD,WAAW,GAAG,GAAG,EAAE;YAAAtE,cAAA,GAAAoC,CAAA;YAC5B,IAAMsF,kBAAkB,IAAA1H,cAAA,GAAAC,CAAA,QAAGmG,MAAI,CAACuB,yBAAyB,CAACF,MAAM,CAACA,MAAM,CAAC;YAACzH,cAAA,GAAAC,CAAA;YACzEyH,kBAAkB,CAACd,OAAO,CAAC,UAAAgB,OAAO,EAAI;cAAA5H,cAAA,GAAAW,CAAA;cAAAX,cAAA,GAAAC,CAAA;cACpCyG,gBAAgB,CAAC3D,IAAI,CAAC;gBACpB/B,GAAG,EAAE4G,OAAO,CAAC5G,GAAG;gBAChBsD,WAAW,EAAEmD,MAAM,CAACnD,WAAW,GAAG,GAAG;gBACrCQ,QAAQ,EAAE2C,MAAM,CAACnD,WAAW,GAAG,GAAG,IAAAtE,cAAA,GAAAoC,CAAA,WAAG,MAAM,KAAApC,cAAA,GAAAoC,CAAA,WAAG,QAAQ;gBACtD6E,mBAAmB,EAAEpE,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG2E,MAAM,CAACI,gBAAgB;gBACzDT,QAAQ,EAAEQ,OAAO,CAACE,IAAI;gBACtBR,GAAG,EAAEM,OAAO,CAACN,GAAG;gBAChBjD,UAAU,EAAEoD,MAAM,CAACnD,WAAW,GAAG;cACnC,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC;YAAAtE,cAAA,GAAAoC,CAAA;UAAA;QACH,CAAC,CAAC;QAGF,IAAM2F,iBAAiB,IAAA/H,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC+H,sBAAsB,CAACtB,gBAAgB,CAAC;QAAC1G,cAAA,GAAAC,CAAA;QACxE,OAAO8H,iBAAiB,CAAC/C,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC3E,YAAY,CAACI,cAAc,CAAC;MACrE,CAAC;MAAA,SA5DaoD,mBAAmBA,CAAAoE,GAAA;QAAA,OAAAhC,oBAAA,CAAAvE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBkC,mBAAmB;IAAA;EAAA;IAAA7C,GAAA;IAAAC,KAAA;MAAA,IAAAiH,YAAA,GAAA/G,iBAAA,CA8DjC,WAA0B+D,UAA2B,EAAiB;QAAAlF,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QACpE,IAAI;UAEF,IAAMkI,QAAQ,IAAAnI,cAAA,GAAAC,CAAA,cAASN,oBAAoB,CAAC4C,GAAG,CAAC2C,UAAU,CAAClE,GAAG,CAAC;UAAChB,cAAA,GAAAC,CAAA;UAChE,IAAIkI,QAAQ,EAAE;YAAAnI,cAAA,GAAAoC,CAAA;YAAApC,cAAA,GAAAC,CAAA;YACZ;UACF,CAAC;YAAAD,cAAA,GAAAoC,CAAA;UAAA;UAGD,IAAMgG,QAAQ,IAAApI,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACoI,gBAAgB,CAACnD,UAAU,CAAClE,GAAG,CAAC;UAAChB,cAAA,GAAAC,CAAA;UAGvD,MAAMN,oBAAoB,CAAC0C,GAAG,CAAC6C,UAAU,CAAClE,GAAG,EAAEoH,QAAQ,EAAE;YACvDd,GAAG,EAAEpC,UAAU,CAACoC,GAAG;YACnBxC,QAAQ,EAAEI,UAAU,CAACJ,QAAQ;YAC7BwD,IAAI,EAAE,CAAC,kBAAkB,EAAE,cAAc;UAC3C,CAAC,CAAC;UAACtI,cAAA,GAAAC,CAAA;UAEHsB,OAAO,CAACC,GAAG,CAAC,2BAA2B0D,UAAU,CAAClE,GAAG,iBAAiBkE,UAAU,CAACb,UAAU,GAAG,CAAC;QAEjG,CAAC,CAAC,OAAO5C,KAAK,EAAE;UAAAzB,cAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,8BAA8ByD,UAAU,CAAClE,GAAG,GAAG,EAAES,KAAK,CAAC;QACvE;MACF,CAAC;MAAA,SAvBa0D,WAAWA,CAAAoD,GAAA;QAAA,OAAAL,YAAA,CAAAxG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAXwD,WAAW;IAAA;EAAA;IAAAnE,GAAA;IAAAC,KAAA,EAyBzB,SAAQ6F,gBAAgBA,CAACC,QAAgB,EAAEC,UAAgC,EAAU;MAAAhH,cAAA,GAAAW,CAAA;MACnF,IAAM6H,WAAW,IAAAxI,cAAA,GAAAC,CAAA,QAAG+G,UAAU,IAAAhH,cAAA,GAAAoC,CAAA,WAAGqG,IAAI,CAACC,SAAS,CAAC1B,UAAU,CAAC,KAAAhH,cAAA,GAAAoC,CAAA,WAAG,EAAE;MAACpC,cAAA,GAAAC,CAAA;MACjE,OAAO,OAAO8G,QAAQ,IAAI4B,IAAI,CAACH,WAAW,CAAC,CAACxD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE;IAC5D;EAAC;IAAAhE,GAAA;IAAAC,KAAA,EAED,SAAQoG,gBAAgBA,CAACN,QAAgB,EAAU;MAAA/G,cAAA,GAAAW,CAAA;MAEjD,IAAMiI,OAA+B,IAAA5I,cAAA,GAAAC,CAAA,QAAG;QACtC,cAAc,EAAE,IAAI;QACpB,mBAAmB,EAAE,KAAK;QAC1B,eAAe,EAAE,KAAK;QACtB,aAAa,EAAE,IAAI;QACnB,aAAa,EAAE,KAAK;QACpB,aAAa,EAAE;MACjB,CAAC;MAACD,cAAA,GAAAC,CAAA;MAEF,OAAO,CAAAD,cAAA,GAAAoC,CAAA,WAAAwG,OAAO,CAAC7B,QAAQ,CAAC,MAAA/G,cAAA,GAAAoC,CAAA,WAAI,KAAK;IACnC;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAQsG,mBAAmBA,CAACR,QAAgB,EAAU;MAAA/G,cAAA,GAAAW,CAAA;MAEpD,IAAMkI,MAA8B,IAAA7I,cAAA,GAAAC,CAAA,QAAG;QACrC,cAAc,EAAE,OAAO;QACvB,mBAAmB,EAAE,OAAO;QAC5B,eAAe,EAAE,OAAO;QACxB,aAAa,EAAE,OAAO;QACtB,aAAa,EAAE,MAAM;QACrB,aAAa,EAAE;MACjB,CAAC;MAACD,cAAA,GAAAC,CAAA;MAEF,OAAO,CAAAD,cAAA,GAAAoC,CAAA,WAAAyG,MAAM,CAAC9B,QAAQ,CAAC,MAAA/G,cAAA,GAAAoC,CAAA,WAAI,OAAO;IACpC;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAQ0G,yBAAyBA,CAACmB,UAAkB,EAIjD;MAAA9I,cAAA,GAAAW,CAAA;MAED,IAAMoI,aAAgF,IAAA/I,cAAA,GAAAC,CAAA,QAAG;QACvF,WAAW,EAAE,CACX;UAAEe,GAAG,EAAE,iBAAiB;UAAE8G,IAAI,EAAE,IAAI;UAAER,GAAG,EAAE;QAAQ,CAAC,EACpD;UAAEtG,GAAG,EAAE,mBAAmB;UAAE8G,IAAI,EAAE,KAAK;UAAER,GAAG,EAAE;QAAO,CAAC,CACvD;QACD,UAAU,EAAE,CACV;UAAEtG,GAAG,EAAE,mBAAmB;UAAE8G,IAAI,EAAE,KAAK;UAAER,GAAG,EAAE;QAAQ,CAAC,EACvD;UAAEtG,GAAG,EAAE,eAAe;UAAE8G,IAAI,EAAE,KAAK;UAAER,GAAG,EAAE;QAAQ,CAAC,CACpD;QACD,UAAU,EAAE,CACV;UAAEtG,GAAG,EAAE,mBAAmB;UAAE8G,IAAI,EAAE,KAAK;UAAER,GAAG,EAAE;QAAQ,CAAC,EACvD;UAAEtG,GAAG,EAAE,oBAAoB;UAAE8G,IAAI,EAAE,KAAK;UAAER,GAAG,EAAE;QAAQ,CAAC,CACzD;QACD,QAAQ,EAAE,CACR;UAAEtG,GAAG,EAAE,aAAa;UAAE8G,IAAI,EAAE,KAAK;UAAER,GAAG,EAAE;QAAO,CAAC,EAChD;UAAEtG,GAAG,EAAE,mBAAmB;UAAE8G,IAAI,EAAE,KAAK;UAAER,GAAG,EAAE;QAAQ,CAAC;MAE3D,CAAC;MAACtH,cAAA,GAAAC,CAAA;MAEF,OAAO,CAAAD,cAAA,GAAAoC,CAAA,WAAA2G,aAAa,CAACD,UAAU,CAAC,MAAA9I,cAAA,GAAAoC,CAAA,WAAI,EAAE;IACxC;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAQ+G,sBAAsBA,CAACpE,WAA8B,EAAqB;MAAA5D,cAAA,GAAAW,CAAA;MAChF,IAAMqI,IAAI,IAAAhJ,cAAA,GAAAC,CAAA,QAAG,IAAIgJ,GAAG,CAAS,CAAC;MAACjJ,cAAA,GAAAC,CAAA;MAC/B,OAAO2D,WAAW,CAACQ,MAAM,CAAC,UAAAc,UAAU,EAAI;QAAAlF,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QACtC,IAAI+I,IAAI,CAAC7G,GAAG,CAAC+C,UAAU,CAAClE,GAAG,CAAC,EAAE;UAAAhB,cAAA,GAAAoC,CAAA;UAAApC,cAAA,GAAAC,CAAA;UAC5B,OAAO,KAAK;QACd,CAAC;UAAAD,cAAA,GAAAoC,CAAA;QAAA;QAAApC,cAAA,GAAAC,CAAA;QACD+I,IAAI,CAACE,GAAG,CAAChE,UAAU,CAAClE,GAAG,CAAC;QAAChB,cAAA,GAAAC,CAAA;QACzB,OAAO,IAAI;MACb,CAAC,CAAC;IACJ;EAAC;IAAAe,GAAA;IAAAC,KAAA,EAED,SAAQ0C,iBAAiBA,CAACC,WAA8B,EAAW;MAAA5D,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAC,CAAA;MACjE,IAAI2D,WAAW,CAACZ,MAAM,KAAK,CAAC,EAAE;QAAAhD,cAAA,GAAAoC,CAAA;QAAApC,cAAA,GAAAC,CAAA;QAAA,OAAO,KAAK;MAAA,CAAC;QAAAD,cAAA,GAAAoC,CAAA;MAAA;MAG3C,IAAM+G,gBAAgB,IAAAnJ,cAAA,GAAAC,CAAA,SAAGiH,IAAI,CAACkC,GAAG,CAAA1H,KAAA,CAARwF,IAAI,EAAAmC,kBAAA,CAAQzF,WAAW,CAACqB,GAAG,CAAC,UAAAvC,CAAC,EAAI;QAAA1C,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAAA,OAAAyC,CAAC,CAACuE,mBAAmB;MAAD,CAAC,CAAC,EAAC;MAACjH,cAAA,GAAAC,CAAA;MAClF,OAAO4C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGqG,gBAAgB,GAAG,MAAM;IAC/C;EAAC;IAAAnI,GAAA;IAAAC,KAAA,EAED,SAAQiB,mBAAmBA,CAACL,MAAc,EAAU;MAAA7B,cAAA,GAAAW,CAAA;MAElD,IAAMmC,GAAG,IAAA9C,cAAA,GAAAC,CAAA,SAAG,IAAI4C,IAAI,CAAC,CAAC;MACtB,IAAMyG,WAAW,IAAAtJ,cAAA,GAAAC,CAAA,SAAG6C,GAAG,CAACyG,WAAW,CAAC,CAAC,CAACvE,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MAClD,IAAMwE,WAAW,IAAAxJ,cAAA,GAAAC,CAAA,SAAGiH,IAAI,CAACuC,KAAK,CAAC3G,GAAG,CAAC4G,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;MAAC1J,cAAA,GAAAC,CAAA;MACvD,OAAO,GAAG4B,MAAM,IAAIyH,WAAW,IAAIE,WAAW,EAAE;IAClD;EAAC;IAAAxI,GAAA;IAAAC,KAAA;MAAA,IAAA0I,kBAAA,GAAAxI,iBAAA,CAED,WAAgCU,MAAc,EAAiB;QAAA7B,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAE7D,IAAI,CAACG,eAAe,CAACwJ,MAAM,CAAC/H,MAAM,CAAC;MACrC,CAAC;MAAA,SAHaqB,iBAAiBA,CAAA2G,GAAA;QAAA,OAAAF,kBAAA,CAAAjI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBuB,iBAAiB;IAAA;EAAA;IAAAlC,GAAA;IAAAC,KAAA;MAAA,IAAA6I,oBAAA,GAAA3I,iBAAA,CAK/B,WAAkCU,MAAc,EAAiB;QAAA7B,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAG/DsB,OAAO,CAACC,GAAG,CAAC,qCAAqCK,MAAM,EAAE,CAAC;MAC5D,CAAC;MAAA,SAJasB,mBAAmBA,CAAA4G,GAAA;QAAA,OAAAD,oBAAA,CAAApI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBwB,mBAAmB;IAAA;EAAA;IAAAnC,GAAA;IAAAC,KAAA;MAAA,IAAA+I,oBAAA,GAAA7I,iBAAA,CAMjC,aAAmD;QAAAnB,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAEjDsB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC;MAAA,SAHaJ,mBAAmBA,CAAA;QAAA,OAAA4I,oBAAA,CAAAtI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBP,mBAAmB;IAAA;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAKjC,SAAQK,uBAAuBA,CAAA,EAAS;MAAA,IAAA2I,MAAA;MAAAjK,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAC,CAAA;MACtCiK,WAAW,CAAC,YAAM;QAAAlK,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAChB,IAAI,CAACgK,MAAI,CAAC9J,UAAU,EAAE;UAAAH,cAAA,GAAAoC,CAAA;UAAApC,cAAA,GAAAC,CAAA;UACpBgK,MAAI,CAACE,YAAY,CAAC,CAAC;QACrB,CAAC;UAAAnK,cAAA,GAAAoC,CAAA;QAAA;MACH,CAAC,EAAE,IAAI,CAAC/B,YAAY,CAACK,eAAe,CAAC;IACvC;EAAC;IAAAM,GAAA;IAAAC,KAAA;MAAA,IAAAmJ,aAAA,GAAAjJ,iBAAA,CAED,aAA4C;QAAAnB,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAC1C,IAAI,CAACE,UAAU,GAAG,IAAI;QAACH,cAAA,GAAAC,CAAA;QACvB,IAAI;UAAAD,cAAA,GAAAC,CAAA;UACFsB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAACxB,cAAA,GAAAC,CAAA;UAC/C,MAAM,IAAI,CAACW,OAAO,CAACyJ,OAAO,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACxK,eAAe,CAACyK,MAAM,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC;UAACzK,cAAA,GAAAC,CAAA;UAC7EsB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAzB,cAAA,GAAAC,CAAA;UACdsB,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;QACrD,CAAC,SAAS;UAAAzB,cAAA,GAAAC,CAAA;UACR,IAAI,CAACE,UAAU,GAAG,KAAK;QACzB;MACF,CAAC;MAAA,SAXagK,YAAYA,CAAA;QAAA,OAAAC,aAAA,CAAA1I,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAZwI,YAAY;IAAA;EAAA;IAAAnJ,GAAA;IAAAC,KAAA,EAa1B,SAAQoH,gBAAgBA,CAACrH,GAAW,EAAO;MAAAhB,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAC,CAAA;MAEzC,OAAO;QACLyK,EAAE,EAAExD,IAAI,CAACC,MAAM,CAAC,CAAC,CAACwD,QAAQ,CAAC,EAAE,CAAC;QAC9BC,IAAI,EAAE,iBAAiB5J,GAAG,EAAE;QAC5B4B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrB+H,SAAS,EAAE;MACb,CAAC;IACH;EAAC;IAAA7J,GAAA;IAAAC,KAAA,EAED,SAAQ+E,mBAAmBA,CAAA,EAAW;MAAAhG,cAAA,GAAAW,CAAA;MAAAX,cAAA,GAAAC,CAAA;MACpC,OAAOqK,KAAK,CAACC,IAAI,CAAC,IAAI,CAACnK,eAAe,CAACoK,MAAM,CAAC,CAAC,CAAC,CAACM,MAAM,CAAC,UAACC,GAAG,EAAEnH,WAAW,EAAK;QAAA5D,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAAA,OAAA8K,GAAG,GAAGnH,WAAW,CAACZ,MAAM;MAAD,CAAC,EAAE,CAAC,CAAC;IAC5G;EAAC;AAAA;AAAA,IAMGnC,qBAAqB;EAAA,SAAAA,sBAAA;IAAAf,eAAA,OAAAe,qBAAA;IAAA,KACjBmK,QAAQ,IAAAhL,cAAA,GAAAC,CAAA,SAAwB,IAAIC,GAAG,CAAC,CAAC;IAAA,KACzC+K,gBAAgB,IAAAjL,cAAA,GAAAC,CAAA,SAAqC,IAAIC,GAAG,CAAC,CAAC;EAAA;EAAA,OAAAa,YAAA,CAAAF,qBAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAAiK,WAAA,GAAA/J,iBAAA,CAEtE,aAAkC;QAAAnB,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAChCsB,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACrD,CAAC;MAAA,SAFKH,UAAUA,CAAA;QAAA,OAAA6J,WAAA,CAAAxJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVN,UAAU;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA;MAAA,IAAAkK,QAAA,GAAAhK,iBAAA,CAIhB,WAAciK,KAIb,EAA8B;QAAApL,cAAA,GAAAW,CAAA;QAC7B,IAAA0K,IAAA,IAAArL,cAAA,GAAAC,CAAA,SAAoBmL,KAAK;UAAjBzI,OAAO,GAAA0I,IAAA,CAAP1I,OAAO;QAAW3C,cAAA,GAAAC,CAAA;QAE1B,IAAI0C,OAAO,CAACK,MAAM,KAAK,CAAC,EAAE;UAAAhD,cAAA,GAAAoC,CAAA;UAAApC,cAAA,GAAAC,CAAA;UACxB,OAAO;YAAEqL,WAAW,EAAE,EAAE;YAAE3E,YAAY,EAAE,EAAE;YAAEa,iBAAiB,EAAE;UAAG,CAAC;QACrE,CAAC;UAAAxH,cAAA,GAAAoC,CAAA;QAAA;QAGD,IAAMmJ,UAAU,IAAAvL,cAAA,GAAAC,CAAA,SAAG0C,OAAO,CAACA,OAAO,CAACK,MAAM,GAAG,CAAC,CAAC;QAC9C,IAAMwI,cAAc,IAAAxL,cAAA,GAAAC,CAAA,SAAG0C,OAAO,CAACqC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,UAAAT,CAAC,EAAI;UAAAxE,cAAA,GAAAW,CAAA;UAAAX,cAAA,GAAAC,CAAA;UAAA,OAAAuE,CAAC,CAACiH,IAAI;QAAD,CAAC,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;QAGpE,IAAMJ,WAAW,IAAAtL,cAAA,GAAAC,CAAA,SAAG,IAAI,CAAC0L,kBAAkB,CAACJ,UAAU,EAAEC,cAAc,CAAC;QACvE,IAAM7E,YAAY,IAAA3G,cAAA,GAAAC,CAAA,SAAG,IAAI,CAAC2L,mBAAmB,CAACL,UAAU,CAAC;QACzD,IAAM/D,iBAAiB,IAAAxH,cAAA,GAAAC,CAAA,SAAG,IAAI,CAAC4L,wBAAwB,CAACN,UAAU,CAAC;QAACvL,cAAA,GAAAC,CAAA;QAEpE,OAAO;UAAEqL,WAAW,EAAXA,WAAW;UAAE3E,YAAY,EAAZA,YAAY;UAAEa,iBAAiB,EAAjBA;QAAkB,CAAC;MACzD,CAAC;MAAA,SArBKf,OAAOA,CAAAqF,GAAA;QAAA,OAAAX,QAAA,CAAAzJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAP8E,OAAO;IAAA;EAAA;IAAAzF,GAAA;IAAAC,KAAA;MAAA,IAAA8K,QAAA,GAAA5K,iBAAA,CAuBb,WAAc6K,YAAmC,EAAiB;QAAA,IAAAC,MAAA;QAAAjM,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAChEsB,OAAO,CAACC,GAAG,CAAC,yBAAyBwK,YAAY,CAAChJ,MAAM,oBAAoB,CAAC;QAAChD,cAAA,GAAAC,CAAA;QAG9E+L,YAAY,CAACpF,OAAO,CAAC,UAAAsF,OAAO,EAAI;UAAAlM,cAAA,GAAAW,CAAA;UAAAX,cAAA,GAAAC,CAAA;UAC9BiM,OAAO,CAACvJ,OAAO,CAACiE,OAAO,CAAC,UAAC9E,MAAM,EAAEqK,KAAK,EAAK;YAAAnM,cAAA,GAAAW,CAAA;YAAAX,cAAA,GAAAC,CAAA;YACzC,IAAIkM,KAAK,GAAGD,OAAO,CAACvJ,OAAO,CAACK,MAAM,GAAG,CAAC,EAAE;cAAAhD,cAAA,GAAAoC,CAAA;cACtC,IAAMgK,OAAO,IAAApM,cAAA,GAAAC,CAAA,SAAG6B,MAAM,CAAC2J,IAAI;cAC3B,IAAMY,IAAI,IAAArM,cAAA,GAAAC,CAAA,SAAGiM,OAAO,CAACvJ,OAAO,CAACwJ,KAAK,GAAG,CAAC,CAAC,CAACV,IAAI;cAACzL,cAAA,GAAAC,CAAA;cAE7C,IAAI,CAACgM,MAAI,CAAChB,gBAAgB,CAAC9I,GAAG,CAACiK,OAAO,CAAC,EAAE;gBAAApM,cAAA,GAAAoC,CAAA;gBAAApC,cAAA,GAAAC,CAAA;gBACvCgM,MAAI,CAAChB,gBAAgB,CAAC5I,GAAG,CAAC+J,OAAO,EAAE,IAAIlM,GAAG,CAAC,CAAC,CAAC;cAC/C,CAAC;gBAAAF,cAAA,GAAAoC,CAAA;cAAA;cAED,IAAMkK,WAAW,IAAAtM,cAAA,GAAAC,CAAA,SAAGgM,MAAI,CAAChB,gBAAgB,CAAC1I,GAAG,CAAC6J,OAAO,CAAC,CAAC;cAACpM,cAAA,GAAAC,CAAA;cACxDqM,WAAW,CAACjK,GAAG,CAACgK,IAAI,EAAE,CAAC,CAAArM,cAAA,GAAAoC,CAAA,WAAAkK,WAAW,CAAC/J,GAAG,CAAC8J,IAAI,CAAC,MAAArM,cAAA,GAAAoC,CAAA,WAAI,CAAC,KAAI,CAAC,CAAC;YACzD,CAAC;cAAApC,cAAA,GAAAoC,CAAA;YAAA;UACH,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MAAA,SAnBKiI,OAAOA,CAAAkC,IAAA;QAAA,OAAAR,QAAA,CAAArK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAP0I,OAAO;IAAA;EAAA;IAAArJ,GAAA;IAAAC,KAAA,EAqBb,SAAQ0K,kBAAkBA,CAACJ,UAAsB,EAAEiB,QAAgB,EAIhE;MAAAxM,cAAA,GAAAW,CAAA;MAED,IAAMiD,WAAW,IAAA5D,cAAA,GAAAC,CAAA,SAAG,CAClB;QAAE6B,MAAM,EAAE,aAAa;QAAEwC,WAAW,EAAE,GAAG;QAAEmI,YAAY,EAAE;MAAK,CAAC,EAC/D;QAAE3K,MAAM,EAAE,cAAc;QAAEwC,WAAW,EAAE,GAAG;QAAEmI,YAAY,EAAE;MAAK,CAAC,EAChE;QAAE3K,MAAM,EAAE,aAAa;QAAEwC,WAAW,EAAE,GAAG;QAAEmI,YAAY,EAAE;MAAK,CAAC,CAChE;MAACzM,cAAA,GAAAC,CAAA;MAEF,OAAO2D,WAAW;IACpB;EAAC;IAAA5C,GAAA;IAAAC,KAAA,EAED,SAAQ2K,mBAAmBA,CAACL,UAAsB,EAI/C;MAAAvL,cAAA,GAAAW,CAAA;MAED,IAAM+L,UAA4E,IAAA1M,cAAA,GAAAC,CAAA,SAAG;QACnF,aAAa,EAAE,CACb;UAAE8G,QAAQ,EAAE,cAAc;UAAEzC,WAAW,EAAE;QAAI,CAAC,EAC9C;UAAEyC,QAAQ,EAAE,mBAAmB;UAAEzC,WAAW,EAAE;QAAI,CAAC,CACpD;QACD,YAAY,EAAE,CACZ;UAAEyC,QAAQ,EAAE,mBAAmB;UAAEzC,WAAW,EAAE;QAAI,CAAC,EACnD;UAAEyC,QAAQ,EAAE,eAAe;UAAEzC,WAAW,EAAE;QAAI,CAAC,CAChD;QACD,aAAa,EAAE,CACb;UAAEyC,QAAQ,EAAE,aAAa;UAAEzC,WAAW,EAAE;QAAI,CAAC,EAC7C;UAAEyC,QAAQ,EAAE,oBAAoB;UAAEzC,WAAW,EAAE;QAAI,CAAC;MAExD,CAAC;MAACtE,cAAA,GAAAC,CAAA;MAEF,OAAO,CAAAD,cAAA,GAAAoC,CAAA,WAAAsK,UAAU,CAACnB,UAAU,CAACE,IAAI,CAAC,MAAAzL,cAAA,GAAAoC,CAAA,WAAI,EAAE;IAC1C;EAAC;IAAApB,GAAA;IAAAC,KAAA,EAED,SAAQ4K,wBAAwBA,CAACN,UAAsB,EAIpD;MAAAvL,cAAA,GAAAW,CAAA;MAED,IAAMgM,aAA2F,IAAA3M,cAAA,GAAAC,CAAA,SAAG;QAClG,aAAa,EAAE,CACb;UAAEwH,MAAM,EAAE,UAAU;UAAEnD,WAAW,EAAE,GAAG;UAAEsI,IAAI,EAAE;QAAK,CAAC,EACpD;UAAEnF,MAAM,EAAE,UAAU;UAAEnD,WAAW,EAAE,GAAG;UAAEsI,IAAI,EAAE;QAAK,CAAC,CACrD;QACD,YAAY,EAAE,CACZ;UAAEnF,MAAM,EAAE,WAAW;UAAEnD,WAAW,EAAE,GAAG;UAAEsI,IAAI,EAAE;QAAK,CAAC,EACrD;UAAEnF,MAAM,EAAE,QAAQ;UAAEnD,WAAW,EAAE,GAAG;UAAEsI,IAAI,EAAE;QAAM,CAAC;MAEvD,CAAC;MAED,IAAMN,WAAW,IAAAtM,cAAA,GAAAC,CAAA,SAAG,CAAAD,cAAA,GAAAoC,CAAA,WAAAuK,aAAa,CAACpB,UAAU,CAACE,IAAI,CAAC,MAAAzL,cAAA,GAAAoC,CAAA,WAAI,EAAE;MAACpC,cAAA,GAAAC,CAAA;MACzD,OAAOqM,WAAW,CAACrH,GAAG,CAAC,UAAA4H,CAAC,EAAK;QAAA7M,cAAA,GAAAW,CAAA;QAAAX,cAAA,GAAAC,CAAA;QAAA;UAC3BwH,MAAM,EAAEoF,CAAC,CAACpF,MAAM;UAChBnD,WAAW,EAAEuI,CAAC,CAACvI,WAAW;UAC1BuD,gBAAgB,EAAEgF,CAAC,CAACD;QACtB,CAAC;MAAD,CAAE,CAAC;IACL;EAAC;AAAA;AAIH,OAAO,IAAME,qBAAqB,IAAA9M,cAAA,GAAAC,CAAA,SAAG,IAAIJ,qBAAqB,CAAC,CAAC;AAChE,eAAeiN,qBAAqB", "ignoreList": []}