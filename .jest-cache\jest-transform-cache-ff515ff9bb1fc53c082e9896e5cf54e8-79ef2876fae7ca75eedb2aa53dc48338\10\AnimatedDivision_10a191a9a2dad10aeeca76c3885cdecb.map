{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_AnimatedInterpolation", "_AnimatedNode", "_AnimatedValue", "_Animated<PERSON>ith<PERSON><PERSON><PERSON><PERSON>", "AnimatedDivision", "_AnimatedWithChildren2", "a", "b", "_this", "_warnedAboutDivideByZero", "__getValue", "console", "error", "_a", "_b", "key", "value", "__makeNative", "platformConfig", "interpolate", "config", "__attach", "__add<PERSON><PERSON>d", "__detach", "__remove<PERSON><PERSON>d", "__getNativeConfig", "type", "input", "__getNativeTag", "_default", "module"], "sources": ["AnimatedDivision.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _AnimatedInterpolation = _interopRequireDefault(require(\"./AnimatedInterpolation\"));\nvar _AnimatedNode = _interopRequireDefault(require(\"./AnimatedNode\"));\nvar _AnimatedValue = _interopRequireDefault(require(\"./AnimatedValue\"));\nvar _AnimatedWithChildren = _interopRequireDefault(require(\"./AnimatedWithChildren\"));\nclass AnimatedDivision extends _AnimatedWithChildren.default {\n  constructor(a, b) {\n    super();\n    this._warnedAboutDivideByZero = false;\n    if (b === 0 || b instanceof _AnimatedNode.default && b.__getValue() === 0) {\n      console.error('Detected potential division by zero in AnimatedDivision');\n    }\n    this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;\n    this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;\n  }\n  __makeNative(platformConfig) {\n    this._a.__makeNative(platformConfig);\n    this._b.__makeNative(platformConfig);\n    super.__makeNative(platformConfig);\n  }\n  __getValue() {\n    var a = this._a.__getValue();\n    var b = this._b.__getValue();\n    if (b === 0) {\n      // Prevent spamming the console/LogBox\n      if (!this._warnedAboutDivideByZero) {\n        console.error('Detected division by zero in AnimatedDivision');\n        this._warnedAboutDivideByZero = true;\n      }\n      // Passing infinity/NaN to Fabric will cause a native crash\n      return 0;\n    }\n    this._warnedAboutDivideByZero = false;\n    return a / b;\n  }\n  interpolate(config) {\n    return new _AnimatedInterpolation.default(this, config);\n  }\n  __attach() {\n    this._a.__addChild(this);\n    this._b.__addChild(this);\n  }\n  __detach() {\n    this._a.__removeChild(this);\n    this._b.__removeChild(this);\n    super.__detach();\n  }\n  __getNativeConfig() {\n    return {\n      type: 'division',\n      input: [this._a.__getNativeTag(), this._b.__getNativeTag()]\n    };\n  }\n}\nvar _default = exports.default = AnimatedDivision;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,sBAAsB,GAAGH,sBAAsB,CAACxB,OAAO,0BAA0B,CAAC,CAAC;AACvF,IAAI4B,aAAa,GAAGJ,sBAAsB,CAACxB,OAAO,iBAAiB,CAAC,CAAC;AACrE,IAAI6B,cAAc,GAAGL,sBAAsB,CAACxB,OAAO,kBAAkB,CAAC,CAAC;AACvE,IAAI8B,qBAAqB,GAAGN,sBAAsB,CAACxB,OAAO,yBAAyB,CAAC,CAAC;AAAC,IAChF+B,gBAAgB,aAAAC,sBAAA;EACpB,SAAAD,iBAAYE,CAAC,EAAEC,CAAC,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAAlC,gBAAA,CAAAU,OAAA,QAAAoB,gBAAA;IAChBI,KAAA,GAAA5B,UAAA,OAAAwB,gBAAA;IACAI,KAAA,CAAKC,wBAAwB,GAAG,KAAK;IACrC,IAAIF,CAAC,KAAK,CAAC,IAAIA,CAAC,YAAYN,aAAa,CAACjB,OAAO,IAAIuB,CAAC,CAACG,UAAU,CAAC,CAAC,KAAK,CAAC,EAAE;MACzEC,OAAO,CAACC,KAAK,CAAC,yDAAyD,CAAC;IAC1E;IACAJ,KAAA,CAAKK,EAAE,GAAG,OAAOP,CAAC,KAAK,QAAQ,GAAG,IAAIJ,cAAc,CAAClB,OAAO,CAACsB,CAAC,CAAC,GAAGA,CAAC;IACnEE,KAAA,CAAKM,EAAE,GAAG,OAAOP,CAAC,KAAK,QAAQ,GAAG,IAAIL,cAAc,CAAClB,OAAO,CAACuB,CAAC,CAAC,GAAGA,CAAC;IAAC,OAAAC,KAAA;EACtE;EAAC,IAAA7B,UAAA,CAAAK,OAAA,EAAAoB,gBAAA,EAAAC,sBAAA;EAAA,WAAA9B,aAAA,CAAAS,OAAA,EAAAoB,gBAAA;IAAAW,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAACL,EAAE,CAACI,YAAY,CAACC,cAAc,CAAC;MACpC,IAAI,CAACJ,EAAE,CAACG,YAAY,CAACC,cAAc,CAAC;MACpCxB,aAAA,CAAAU,gBAAA,4BAAmBc,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAN,UAAUA,CAAA,EAAG;MACX,IAAIJ,CAAC,GAAG,IAAI,CAACO,EAAE,CAACH,UAAU,CAAC,CAAC;MAC5B,IAAIH,CAAC,GAAG,IAAI,CAACO,EAAE,CAACJ,UAAU,CAAC,CAAC;MAC5B,IAAIH,CAAC,KAAK,CAAC,EAAE;QAEX,IAAI,CAAC,IAAI,CAACE,wBAAwB,EAAE;UAClCE,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;UAC9D,IAAI,CAACH,wBAAwB,GAAG,IAAI;QACtC;QAEA,OAAO,CAAC;MACV;MACA,IAAI,CAACA,wBAAwB,GAAG,KAAK;MACrC,OAAOH,CAAC,GAAGC,CAAC;IACd;EAAC;IAAAQ,GAAA;IAAAC,KAAA,EACD,SAAAG,WAAWA,CAACC,MAAM,EAAE;MAClB,OAAO,IAAIpB,sBAAsB,CAAChB,OAAO,CAAC,IAAI,EAAEoC,MAAM,CAAC;IACzD;EAAC;IAAAL,GAAA;IAAAC,KAAA,EACD,SAAAK,QAAQA,CAAA,EAAG;MACT,IAAI,CAACR,EAAE,CAACS,UAAU,CAAC,IAAI,CAAC;MACxB,IAAI,CAACR,EAAE,CAACQ,UAAU,CAAC,IAAI,CAAC;IAC1B;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAAO,QAAQA,CAAA,EAAG;MACT,IAAI,CAACV,EAAE,CAACW,aAAa,CAAC,IAAI,CAAC;MAC3B,IAAI,CAACV,EAAE,CAACU,aAAa,CAAC,IAAI,CAAC;MAC3B9B,aAAA,CAAAU,gBAAA;IACF;EAAC;IAAAW,GAAA;IAAAC,KAAA,EACD,SAAAS,iBAAiBA,CAAA,EAAG;MAClB,OAAO;QACLC,IAAI,EAAE,UAAU;QAChBC,KAAK,EAAE,CAAC,IAAI,CAACd,EAAE,CAACe,cAAc,CAAC,CAAC,EAAE,IAAI,CAACd,EAAE,CAACc,cAAc,CAAC,CAAC;MAC5D,CAAC;IACH;EAAC;AAAA,EA/C4BzB,qBAAqB,CAACnB,OAAO;AAiD5D,IAAI6C,QAAQ,GAAG/B,OAAO,CAACd,OAAO,GAAGoB,gBAAgB;AACjD0B,MAAM,CAAChC,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}