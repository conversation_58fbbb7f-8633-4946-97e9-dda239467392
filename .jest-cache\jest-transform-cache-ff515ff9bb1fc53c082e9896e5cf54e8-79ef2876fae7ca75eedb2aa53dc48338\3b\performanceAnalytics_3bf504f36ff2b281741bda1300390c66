a2de23c31fd28c4a3c1bf084fad8d29a
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2gc0uz0ypt() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\performanceAnalytics.ts";
  var hash = "0c7a72dfc800d85c785be403e5b84704d95146bc";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\performanceAnalytics.ts",
    statementMap: {
      "0": {
        start: {
          line: 96,
          column: 26
        },
        end: {
          line: 96,
          column: 60
        }
      },
      "1": {
        start: {
          line: 97,
          column: 21
        },
        end: {
          line: 97,
          column: 60
        }
      },
      "2": {
        start: {
          line: 98,
          column: 28
        },
        end: {
          line: 98,
          column: 64
        }
      },
      "3": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 104,
          column: 6
        }
      },
      "4": {
        start: {
          line: 116,
          column: 29
        },
        end: {
          line: 116,
          column: 79
        }
      },
      "5": {
        start: {
          line: 116,
          column: 55
        },
        end: {
          line: 116,
          column: 78
        }
      },
      "6": {
        start: {
          line: 117,
          column: 22
        },
        end: {
          line: 117,
          column: 50
        }
      },
      "7": {
        start: {
          line: 118,
          column: 25
        },
        end: {
          line: 118,
          column: 51
        }
      },
      "8": {
        start: {
          line: 120,
          column: 28
        },
        end: {
          line: 120,
          column: 94
        }
      },
      "9": {
        start: {
          line: 120,
          column: 56
        },
        end: {
          line: 120,
          column: 75
        }
      },
      "10": {
        start: {
          line: 122,
          column: 28
        },
        end: {
          line: 122,
          column: 67
        }
      },
      "11": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 129,
          column: 6
        }
      },
      "12": {
        start: {
          line: 143,
          column: 26
        },
        end: {
          line: 143,
          column: 75
        }
      },
      "13": {
        start: {
          line: 144,
          column: 33
        },
        end: {
          line: 144,
          column: 94
        }
      },
      "14": {
        start: {
          line: 145,
          column: 36
        },
        end: {
          line: 145,
          column: 87
        }
      },
      "15": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 151,
          column: 6
        }
      },
      "16": {
        start: {
          line: 167,
          column: 34
        },
        end: {
          line: 167,
          column: 97
        }
      },
      "17": {
        start: {
          line: 168,
          column: 30
        },
        end: {
          line: 168,
          column: 88
        }
      },
      "18": {
        start: {
          line: 169,
          column: 32
        },
        end: {
          line: 169,
          column: 91
        }
      },
      "19": {
        start: {
          line: 170,
          column: 30
        },
        end: {
          line: 170,
          column: 88
        }
      },
      "20": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 177,
          column: 6
        }
      },
      "21": {
        start: {
          line: 192,
          column: 28
        },
        end: {
          line: 192,
          column: 75
        }
      },
      "22": {
        start: {
          line: 193,
          column: 26
        },
        end: {
          line: 193,
          column: 71
        }
      },
      "23": {
        start: {
          line: 194,
          column: 35
        },
        end: {
          line: 194,
          column: 102
        }
      },
      "24": {
        start: {
          line: 195,
          column: 27
        },
        end: {
          line: 195,
          column: 83
        }
      },
      "25": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 202,
          column: 6
        }
      },
      "26": {
        start: {
          line: 208,
          column: 26
        },
        end: {
          line: 208,
          column: 69
        }
      },
      "27": {
        start: {
          line: 209,
          column: 22
        },
        end: {
          line: 209,
          column: 93
        }
      },
      "28": {
        start: {
          line: 210,
          column: 23
        },
        end: {
          line: 210,
          column: 64
        }
      },
      "29": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 217,
          column: 38
        }
      },
      "30": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 220,
          column: 58
        }
      },
      "31": {
        start: {
          line: 224,
          column: 32
        },
        end: {
          line: 224,
          column: 34
        }
      },
      "32": {
        start: {
          line: 225,
          column: 33
        },
        end: {
          line: 225,
          column: 35
        }
      },
      "33": {
        start: {
          line: 226,
          column: 38
        },
        end: {
          line: 226,
          column: 40
        }
      },
      "34": {
        start: {
          line: 227,
          column: 40
        },
        end: {
          line: 227,
          column: 42
        }
      },
      "35": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 236,
          column: 5
        }
      },
      "36": {
        start: {
          line: 231,
          column: 6
        },
        end: {
          line: 231,
          column: 47
        }
      },
      "37": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 233,
          column: 49
        }
      },
      "38": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 62
        }
      },
      "39": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 235,
          column: 48
        }
      },
      "40": {
        start: {
          line: 239,
          column: 22
        },
        end: {
          line: 239,
          column: 70
        }
      },
      "41": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 246,
          column: 5
        }
      },
      "42": {
        start: {
          line: 241,
          column: 6
        },
        end: {
          line: 241,
          column: 48
        }
      },
      "43": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 243,
          column: 50
        }
      },
      "44": {
        start: {
          line: 244,
          column: 6
        },
        end: {
          line: 244,
          column: 66
        }
      },
      "45": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 53
        }
      },
      "46": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 257,
          column: 5
        }
      },
      "47": {
        start: {
          line: 250,
          column: 29
        },
        end: {
          line: 250,
          column: 77
        }
      },
      "48": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 256,
          column: 7
        }
      },
      "49": {
        start: {
          line: 252,
          column: 8
        },
        end: {
          line: 252,
          column: 45
        }
      },
      "50": {
        start: {
          line: 254,
          column: 8
        },
        end: {
          line: 254,
          column: 54
        }
      },
      "51": {
        start: {
          line: 255,
          column: 8
        },
        end: {
          line: 255,
          column: 50
        }
      },
      "52": {
        start: {
          line: 259,
          column: 33
        },
        end: {
          line: 259,
          column: 74
        }
      },
      "53": {
        start: {
          line: 261,
          column: 4
        },
        end: {
          line: 267,
          column: 6
        }
      },
      "54": {
        start: {
          line: 271,
          column: 27
        },
        end: {
          line: 271,
          column: 104
        }
      },
      "55": {
        start: {
          line: 272,
          column: 24
        },
        end: {
          line: 272,
          column: 97
        }
      },
      "56": {
        start: {
          line: 273,
          column: 26
        },
        end: {
          line: 273,
          column: 28
        }
      },
      "57": {
        start: {
          line: 274,
          column: 28
        },
        end: {
          line: 274,
          column: 64
        }
      },
      "58": {
        start: {
          line: 275,
          column: 25
        },
        end: {
          line: 275,
          column: 27
        }
      },
      "59": {
        start: {
          line: 276,
          column: 25
        },
        end: {
          line: 276,
          column: 27
        }
      },
      "60": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 285,
          column: 6
        }
      },
      "61": {
        start: {
          line: 290,
          column: 33
        },
        end: {
          line: 292,
          column: 10
        }
      },
      "62": {
        start: {
          line: 294,
          column: 4
        },
        end: {
          line: 294,
          column: 65
        }
      },
      "63": {
        start: {
          line: 298,
          column: 20
        },
        end: {
          line: 303,
          column: 5
        }
      },
      "64": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 305,
          column: 89
        }
      },
      "65": {
        start: {
          line: 305,
          column: 54
        },
        end: {
          line: 305,
          column: 66
        }
      },
      "66": {
        start: {
          line: 309,
          column: 38
        },
        end: {
          line: 309,
          column: 40
        }
      },
      "67": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 318,
          column: 7
        }
      },
      "68": {
        start: {
          line: 312,
          column: 6
        },
        end: {
          line: 314,
          column: 7
        }
      },
      "69": {
        start: {
          line: 313,
          column: 8
        },
        end: {
          line: 313,
          column: 95
        }
      },
      "70": {
        start: {
          line: 315,
          column: 6
        },
        end: {
          line: 317,
          column: 7
        }
      },
      "71": {
        start: {
          line: 316,
          column: 8
        },
        end: {
          line: 316,
          column: 79
        }
      },
      "72": {
        start: {
          line: 320,
          column: 4
        },
        end: {
          line: 320,
          column: 27
        }
      },
      "73": {
        start: {
          line: 324,
          column: 29
        },
        end: {
          line: 324,
          column: 31
        }
      },
      "74": {
        start: {
          line: 326,
          column: 4
        },
        end: {
          line: 335,
          column: 5
        }
      },
      "75": {
        start: {
          line: 327,
          column: 31
        },
        end: {
          line: 328,
          column: 78
        }
      },
      "76": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 328,
          column: 51
        }
      },
      "77": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 334,
          column: 7
        }
      },
      "78": {
        start: {
          line: 331,
          column: 8
        },
        end: {
          line: 331,
          column: 69
        }
      },
      "79": {
        start: {
          line: 332,
          column: 13
        },
        end: {
          line: 334,
          column: 7
        }
      },
      "80": {
        start: {
          line: 333,
          column: 8
        },
        end: {
          line: 333,
          column: 85
        }
      },
      "81": {
        start: {
          line: 337,
          column: 4
        },
        end: {
          line: 337,
          column: 18
        }
      },
      "82": {
        start: {
          line: 341,
          column: 4
        },
        end: {
          line: 346,
          column: 10
        }
      },
      "83": {
        start: {
          line: 342,
          column: 23
        },
        end: {
          line: 342,
          column: 50
        }
      },
      "84": {
        start: {
          line: 343,
          column: 21
        },
        end: {
          line: 346,
          column: 7
        }
      },
      "85": {
        start: {
          line: 350,
          column: 38
        },
        end: {
          line: 350,
          column: 40
        }
      },
      "86": {
        start: {
          line: 352,
          column: 4
        },
        end: {
          line: 355,
          column: 5
        }
      },
      "87": {
        start: {
          line: 352,
          column: 36
        },
        end: {
          line: 352,
          column: 61
        }
      },
      "88": {
        start: {
          line: 353,
          column: 6
        },
        end: {
          line: 353,
          column: 84
        }
      },
      "89": {
        start: {
          line: 354,
          column: 6
        },
        end: {
          line: 354,
          column: 89
        }
      },
      "90": {
        start: {
          line: 357,
          column: 4
        },
        end: {
          line: 360,
          column: 5
        }
      },
      "91": {
        start: {
          line: 357,
          column: 36
        },
        end: {
          line: 357,
          column: 63
        }
      },
      "92": {
        start: {
          line: 358,
          column: 6
        },
        end: {
          line: 358,
          column: 64
        }
      },
      "93": {
        start: {
          line: 359,
          column: 6
        },
        end: {
          line: 359,
          column: 82
        }
      },
      "94": {
        start: {
          line: 362,
          column: 4
        },
        end: {
          line: 362,
          column: 27
        }
      },
      "95": {
        start: {
          line: 367,
          column: 30
        },
        end: {
          line: 367,
          column: 73
        }
      },
      "96": {
        start: {
          line: 370,
          column: 21
        },
        end: {
          line: 370,
          column: 22
        }
      },
      "97": {
        start: {
          line: 371,
          column: 4
        },
        end: {
          line: 373,
          column: 5
        }
      },
      "98": {
        start: {
          line: 372,
          column: 6
        },
        end: {
          line: 372,
          column: 22
        }
      },
      "99": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 375,
          column: 69
        }
      },
      "100": {
        start: {
          line: 379,
          column: 30
        },
        end: {
          line: 379,
          column: 32
        }
      },
      "101": {
        start: {
          line: 381,
          column: 4
        },
        end: {
          line: 383,
          column: 5
        }
      },
      "102": {
        start: {
          line: 382,
          column: 6
        },
        end: {
          line: 382,
          column: 47
        }
      },
      "103": {
        start: {
          line: 385,
          column: 4
        },
        end: {
          line: 387,
          column: 5
        }
      },
      "104": {
        start: {
          line: 386,
          column: 6
        },
        end: {
          line: 386,
          column: 45
        }
      },
      "105": {
        start: {
          line: 389,
          column: 4
        },
        end: {
          line: 389,
          column: 19
        }
      },
      "106": {
        start: {
          line: 393,
          column: 34
        },
        end: {
          line: 393,
          column: 36
        }
      },
      "107": {
        start: {
          line: 395,
          column: 4
        },
        end: {
          line: 397,
          column: 5
        }
      },
      "108": {
        start: {
          line: 396,
          column: 6
        },
        end: {
          line: 396,
          column: 67
        }
      },
      "109": {
        start: {
          line: 399,
          column: 4
        },
        end: {
          line: 401,
          column: 5
        }
      },
      "110": {
        start: {
          line: 400,
          column: 6
        },
        end: {
          line: 400,
          column: 68
        }
      },
      "111": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 403,
          column: 23
        }
      },
      "112": {
        start: {
          line: 408,
          column: 20
        },
        end: {
          line: 408,
          column: 22
        }
      },
      "113": {
        start: {
          line: 410,
          column: 4
        },
        end: {
          line: 412,
          column: 5
        }
      },
      "114": {
        start: {
          line: 411,
          column: 6
        },
        end: {
          line: 411,
          column: 22
        }
      },
      "115": {
        start: {
          line: 414,
          column: 4
        },
        end: {
          line: 416,
          column: 5
        }
      },
      "116": {
        start: {
          line: 415,
          column: 6
        },
        end: {
          line: 415,
          column: 22
        }
      },
      "117": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 418,
          column: 36
        }
      },
      "118": {
        start: {
          line: 422,
          column: 4
        },
        end: {
          line: 422,
          column: 47
        }
      },
      "119": {
        start: {
          line: 422,
          column: 37
        },
        end: {
          line: 422,
          column: 47
        }
      },
      "120": {
        start: {
          line: 424,
          column: 28
        },
        end: {
          line: 424,
          column: 91
        }
      },
      "121": {
        start: {
          line: 425,
          column: 27
        },
        end: {
          line: 425,
          column: 142
        }
      },
      "122": {
        start: {
          line: 427,
          column: 27
        },
        end: {
          line: 427,
          column: 59
        }
      },
      "123": {
        start: {
          line: 428,
          column: 4
        },
        end: {
          line: 428,
          column: 58
        }
      },
      "124": {
        start: {
          line: 432,
          column: 4
        },
        end: {
          line: 432,
          column: 47
        }
      },
      "125": {
        start: {
          line: 432,
          column: 37
        },
        end: {
          line: 432,
          column: 47
        }
      },
      "126": {
        start: {
          line: 434,
          column: 30
        },
        end: {
          line: 434,
          column: 95
        }
      },
      "127": {
        start: {
          line: 434,
          column: 58
        },
        end: {
          line: 434,
          column: 94
        }
      },
      "128": {
        start: {
          line: 435,
          column: 24
        },
        end: {
          line: 435,
          column: 94
        }
      },
      "129": {
        start: {
          line: 437,
          column: 4
        },
        end: {
          line: 437,
          column: 69
        }
      },
      "130": {
        start: {
          line: 441,
          column: 38
        },
        end: {
          line: 441,
          column: 40
        }
      },
      "131": {
        start: {
          line: 443,
          column: 4
        },
        end: {
          line: 446,
          column: 5
        }
      },
      "132": {
        start: {
          line: 444,
          column: 6
        },
        end: {
          line: 444,
          column: 63
        }
      },
      "133": {
        start: {
          line: 445,
          column: 6
        },
        end: {
          line: 445,
          column: 78
        }
      },
      "134": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 451,
          column: 5
        }
      },
      "135": {
        start: {
          line: 449,
          column: 6
        },
        end: {
          line: 449,
          column: 63
        }
      },
      "136": {
        start: {
          line: 450,
          column: 6
        },
        end: {
          line: 450,
          column: 60
        }
      },
      "137": {
        start: {
          line: 453,
          column: 4
        },
        end: {
          line: 453,
          column: 27
        }
      },
      "138": {
        start: {
          line: 457,
          column: 25
        },
        end: {
          line: 457,
          column: 47
        }
      },
      "139": {
        start: {
          line: 458,
          column: 30
        },
        end: {
          line: 458,
          column: 55
        }
      },
      "140": {
        start: {
          line: 460,
          column: 4
        },
        end: {
          line: 460,
          column: 56
        }
      },
      "141": {
        start: {
          line: 464,
          column: 43
        },
        end: {
          line: 464,
          column: 76
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 91,
            column: 2
          },
          end: {
            line: 91,
            column: 3
          }
        },
        loc: {
          start: {
            line: 95,
            column: 4
          },
          end: {
            line: 105,
            column: 3
          }
        },
        line: 95
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 110,
            column: 2
          },
          end: {
            line: 110,
            column: 3
          }
        },
        loc: {
          start: {
            line: 115,
            column: 4
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 115
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 116,
            column: 45
          },
          end: {
            line: 116,
            column: 46
          }
        },
        loc: {
          start: {
            line: 116,
            column: 55
          },
          end: {
            line: 116,
            column: 78
          }
        },
        line: 116
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 120,
            column: 41
          },
          end: {
            line: 120,
            column: 42
          }
        },
        loc: {
          start: {
            line: 120,
            column: 56
          },
          end: {
            line: 120,
            column: 75
          }
        },
        line: 120
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 135,
            column: 2
          },
          end: {
            line: 135,
            column: 3
          }
        },
        loc: {
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 152,
            column: 3
          }
        },
        line: 142
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 157,
            column: 2
          },
          end: {
            line: 157,
            column: 3
          }
        },
        loc: {
          start: {
            line: 166,
            column: 4
          },
          end: {
            line: 178,
            column: 3
          }
        },
        line: 166
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 183,
            column: 2
          },
          end: {
            line: 183,
            column: 3
          }
        },
        loc: {
          start: {
            line: 191,
            column: 4
          },
          end: {
            line: 203,
            column: 3
          }
        },
        line: 191
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 207,
            column: 2
          },
          end: {
            line: 207,
            column: 3
          }
        },
        loc: {
          start: {
            line: 207,
            column: 65
          },
          end: {
            line: 221,
            column: 3
          }
        },
        line: 207
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 223,
            column: 2
          },
          end: {
            line: 223,
            column: 3
          }
        },
        loc: {
          start: {
            line: 223,
            column: 83
          },
          end: {
            line: 268,
            column: 3
          }
        },
        line: 223
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 270,
            column: 2
          },
          end: {
            line: 270,
            column: 3
          }
        },
        loc: {
          start: {
            line: 270,
            column: 76
          },
          end: {
            line: 286,
            column: 3
          }
        },
        line: 270
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 288,
            column: 2
          },
          end: {
            line: 288,
            column: 3
          }
        },
        loc: {
          start: {
            line: 288,
            column: 67
          },
          end: {
            line: 295,
            column: 3
          }
        },
        line: 288
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 297,
            column: 2
          },
          end: {
            line: 297,
            column: 3
          }
        },
        loc: {
          start: {
            line: 297,
            column: 72
          },
          end: {
            line: 306,
            column: 3
          }
        },
        line: 297
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 305,
            column: 37
          },
          end: {
            line: 305,
            column: 38
          }
        },
        loc: {
          start: {
            line: 305,
            column: 54
          },
          end: {
            line: 305,
            column: 66
          }
        },
        line: 305
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 308,
            column: 2
          },
          end: {
            line: 308,
            column: 3
          }
        },
        loc: {
          start: {
            line: 308,
            column: 72
          },
          end: {
            line: 321,
            column: 3
          }
        },
        line: 308
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 311,
            column: 18
          },
          end: {
            line: 311,
            column: 19
          }
        },
        loc: {
          start: {
            line: 311,
            column: 26
          },
          end: {
            line: 318,
            column: 5
          }
        },
        line: 311
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 323,
            column: 2
          },
          end: {
            line: 323,
            column: 3
          }
        },
        loc: {
          start: {
            line: 323,
            column: 103
          },
          end: {
            line: 338,
            column: 3
          }
        },
        line: 323
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 327,
            column: 52
          },
          end: {
            line: 327,
            column: 53
          }
        },
        loc: {
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 328,
            column: 51
          }
        },
        line: 328
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 340,
            column: 2
          },
          end: {
            line: 340,
            column: 3
          }
        },
        loc: {
          start: {
            line: 340,
            column: 91
          },
          end: {
            line: 347,
            column: 3
          }
        },
        line: 340
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 342,
            column: 14
          },
          end: {
            line: 342,
            column: 15
          }
        },
        loc: {
          start: {
            line: 342,
            column: 23
          },
          end: {
            line: 342,
            column: 50
          }
        },
        line: 342
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 343,
            column: 11
          },
          end: {
            line: 343,
            column: 12
          }
        },
        loc: {
          start: {
            line: 343,
            column: 21
          },
          end: {
            line: 346,
            column: 7
          }
        },
        line: 343
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 349,
            column: 2
          },
          end: {
            line: 349,
            column: 3
          }
        },
        loc: {
          start: {
            line: 349,
            column: 77
          },
          end: {
            line: 363,
            column: 3
          }
        },
        line: 349
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 352,
            column: 27
          },
          end: {
            line: 352,
            column: 28
          }
        },
        loc: {
          start: {
            line: 352,
            column: 36
          },
          end: {
            line: 352,
            column: 61
          }
        },
        line: 352
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 357,
            column: 27
          },
          end: {
            line: 357,
            column: 28
          }
        },
        loc: {
          start: {
            line: 357,
            column: 36
          },
          end: {
            line: 357,
            column: 63
          }
        },
        line: 357
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 365,
            column: 2
          },
          end: {
            line: 365,
            column: 3
          }
        },
        loc: {
          start: {
            line: 365,
            column: 96
          },
          end: {
            line: 376,
            column: 3
          }
        },
        line: 365
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 378,
            column: 2
          },
          end: {
            line: 378,
            column: 3
          }
        },
        loc: {
          start: {
            line: 378,
            column: 93
          },
          end: {
            line: 390,
            column: 3
          }
        },
        line: 378
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 392,
            column: 2
          },
          end: {
            line: 392,
            column: 3
          }
        },
        loc: {
          start: {
            line: 392,
            column: 94
          },
          end: {
            line: 404,
            column: 3
          }
        },
        line: 392
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 406,
            column: 2
          },
          end: {
            line: 406,
            column: 3
          }
        },
        loc: {
          start: {
            line: 406,
            column: 86
          },
          end: {
            line: 419,
            column: 3
          }
        },
        line: 406
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 421,
            column: 2
          },
          end: {
            line: 421,
            column: 3
          }
        },
        loc: {
          start: {
            line: 421,
            column: 80
          },
          end: {
            line: 429,
            column: 3
          }
        },
        line: 421
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 431,
            column: 2
          },
          end: {
            line: 431,
            column: 3
          }
        },
        loc: {
          start: {
            line: 431,
            column: 78
          },
          end: {
            line: 438,
            column: 3
          }
        },
        line: 431
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 434,
            column: 51
          },
          end: {
            line: 434,
            column: 52
          }
        },
        loc: {
          start: {
            line: 434,
            column: 58
          },
          end: {
            line: 434,
            column: 94
          }
        },
        line: 434
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 440,
            column: 2
          },
          end: {
            line: 440,
            column: 3
          }
        },
        loc: {
          start: {
            line: 440,
            column: 87
          },
          end: {
            line: 454,
            column: 3
          }
        },
        line: 440
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 456,
            column: 2
          },
          end: {
            line: 456,
            column: 3
          }
        },
        loc: {
          start: {
            line: 456,
            column: 86
          },
          end: {
            line: 461,
            column: 3
          }
        },
        line: 456
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 230,
            column: 4
          },
          end: {
            line: 236,
            column: 5
          }
        }, {
          start: {
            line: 232,
            column: 11
          },
          end: {
            line: 236,
            column: 5
          }
        }],
        line: 230
      },
      "1": {
        loc: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 246,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 246,
            column: 5
          }
        }, {
          start: {
            line: 242,
            column: 11
          },
          end: {
            line: 246,
            column: 5
          }
        }],
        line: 240
      },
      "2": {
        loc: {
          start: {
            line: 249,
            column: 4
          },
          end: {
            line: 257,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 249,
            column: 4
          },
          end: {
            line: 257,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 249
      },
      "3": {
        loc: {
          start: {
            line: 251,
            column: 6
          },
          end: {
            line: 256,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 6
          },
          end: {
            line: 256,
            column: 7
          }
        }, {
          start: {
            line: 253,
            column: 13
          },
          end: {
            line: 256,
            column: 7
          }
        }],
        line: 251
      },
      "4": {
        loc: {
          start: {
            line: 290,
            column: 33
          },
          end: {
            line: 292,
            column: 10
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 291,
            column: 8
          },
          end: {
            line: 291,
            column: 67
          }
        }, {
          start: {
            line: 292,
            column: 8
          },
          end: {
            line: 292,
            column: 10
          }
        }],
        line: 290
      },
      "5": {
        loc: {
          start: {
            line: 302,
            column: 6
          },
          end: {
            line: 302,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 302,
            column: 35
          },
          end: {
            line: 302,
            column: 94
          }
        }, {
          start: {
            line: 302,
            column: 97
          },
          end: {
            line: 302,
            column: 99
          }
        }],
        line: 302
      },
      "6": {
        loc: {
          start: {
            line: 312,
            column: 6
          },
          end: {
            line: 314,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 312,
            column: 6
          },
          end: {
            line: 314,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 312
      },
      "7": {
        loc: {
          start: {
            line: 315,
            column: 6
          },
          end: {
            line: 317,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 6
          },
          end: {
            line: 317,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 315
      },
      "8": {
        loc: {
          start: {
            line: 326,
            column: 4
          },
          end: {
            line: 335,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 326,
            column: 4
          },
          end: {
            line: 335,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 326
      },
      "9": {
        loc: {
          start: {
            line: 330,
            column: 6
          },
          end: {
            line: 334,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 330,
            column: 6
          },
          end: {
            line: 334,
            column: 7
          }
        }, {
          start: {
            line: 332,
            column: 13
          },
          end: {
            line: 334,
            column: 7
          }
        }],
        line: 330
      },
      "10": {
        loc: {
          start: {
            line: 332,
            column: 13
          },
          end: {
            line: 334,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 13
          },
          end: {
            line: 334,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "11": {
        loc: {
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 355,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 352,
            column: 4
          },
          end: {
            line: 355,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 352
      },
      "12": {
        loc: {
          start: {
            line: 357,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 357,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 357
      },
      "13": {
        loc: {
          start: {
            line: 371,
            column: 4
          },
          end: {
            line: 373,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 371,
            column: 4
          },
          end: {
            line: 373,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 371
      },
      "14": {
        loc: {
          start: {
            line: 371,
            column: 8
          },
          end: {
            line: 371,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 371,
            column: 8
          },
          end: {
            line: 371,
            column: 38
          }
        }, {
          start: {
            line: 371,
            column: 42
          },
          end: {
            line: 371,
            column: 89
          }
        }],
        line: 371
      },
      "15": {
        loc: {
          start: {
            line: 381,
            column: 4
          },
          end: {
            line: 383,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 381,
            column: 4
          },
          end: {
            line: 383,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 381
      },
      "16": {
        loc: {
          start: {
            line: 381,
            column: 8
          },
          end: {
            line: 381,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 381,
            column: 8
          },
          end: {
            line: 381,
            column: 31
          }
        }, {
          start: {
            line: 381,
            column: 35
          },
          end: {
            line: 381,
            column: 83
          }
        }],
        line: 381
      },
      "17": {
        loc: {
          start: {
            line: 385,
            column: 4
          },
          end: {
            line: 387,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 385,
            column: 4
          },
          end: {
            line: 387,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 385
      },
      "18": {
        loc: {
          start: {
            line: 395,
            column: 4
          },
          end: {
            line: 397,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 395,
            column: 4
          },
          end: {
            line: 397,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 395
      },
      "19": {
        loc: {
          start: {
            line: 399,
            column: 4
          },
          end: {
            line: 401,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 399,
            column: 4
          },
          end: {
            line: 401,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 399
      },
      "20": {
        loc: {
          start: {
            line: 399,
            column: 8
          },
          end: {
            line: 399,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 399,
            column: 8
          },
          end: {
            line: 399,
            column: 37
          }
        }, {
          start: {
            line: 399,
            column: 41
          },
          end: {
            line: 399,
            column: 80
          }
        }],
        line: 399
      },
      "21": {
        loc: {
          start: {
            line: 410,
            column: 4
          },
          end: {
            line: 412,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 410,
            column: 4
          },
          end: {
            line: 412,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 410
      },
      "22": {
        loc: {
          start: {
            line: 410,
            column: 8
          },
          end: {
            line: 410,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 410,
            column: 8
          },
          end: {
            line: 410,
            column: 26
          }
        }, {
          start: {
            line: 410,
            column: 30
          },
          end: {
            line: 410,
            column: 77
          }
        }],
        line: 410
      },
      "23": {
        loc: {
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 416,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 416,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 414
      },
      "24": {
        loc: {
          start: {
            line: 414,
            column: 8
          },
          end: {
            line: 414,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 414,
            column: 8
          },
          end: {
            line: 414,
            column: 27
          }
        }, {
          start: {
            line: 414,
            column: 31
          },
          end: {
            line: 414,
            column: 76
          }
        }],
        line: 414
      },
      "25": {
        loc: {
          start: {
            line: 422,
            column: 4
          },
          end: {
            line: 422,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 422,
            column: 4
          },
          end: {
            line: 422,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 422
      },
      "26": {
        loc: {
          start: {
            line: 432,
            column: 4
          },
          end: {
            line: 432,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 432,
            column: 4
          },
          end: {
            line: 432,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 432
      },
      "27": {
        loc: {
          start: {
            line: 443,
            column: 4
          },
          end: {
            line: 446,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 443,
            column: 4
          },
          end: {
            line: 446,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 443
      },
      "28": {
        loc: {
          start: {
            line: 448,
            column: 4
          },
          end: {
            line: 451,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 448,
            column: 4
          },
          end: {
            line: 451,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 448
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0c7a72dfc800d85c785be403e5b84704d95146bc"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2gc0uz0ypt = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2gc0uz0ypt();
var PerformanceAnalyticsService = function () {
  function PerformanceAnalyticsService() {
    _classCallCheck(this, PerformanceAnalyticsService);
  }
  return _createClass(PerformanceAnalyticsService, [{
    key: "analyzeMatchPerformance",
    value: function analyzeMatchPerformance(stats) {
      cov_2gc0uz0ypt().f[0]++;
      var overallRating = (cov_2gc0uz0ypt().s[0]++, this.calculateOverallRating(stats));
      var insights = (cov_2gc0uz0ypt().s[1]++, this.generatePerformanceInsights(stats));
      var advancedMetrics = (cov_2gc0uz0ypt().s[2]++, this.calculateAdvancedMetrics(stats));
      cov_2gc0uz0ypt().s[3]++;
      return {
        overallRating: overallRating,
        insights: insights,
        advancedMetrics: advancedMetrics
      };
    }
  }, {
    key: "analyzeShotPerformance",
    value: function analyzeShotPerformance(shots) {
      cov_2gc0uz0ypt().f[1]++;
      var sortedByAccuracy = (cov_2gc0uz0ypt().s[4]++, _toConsumableArray(shots).sort(function (a, b) {
        cov_2gc0uz0ypt().f[2]++;
        cov_2gc0uz0ypt().s[5]++;
        return b.accuracy - a.accuracy;
      }));
      var bestShots = (cov_2gc0uz0ypt().s[6]++, sortedByAccuracy.slice(0, 2));
      var weakestShots = (cov_2gc0uz0ypt().s[7]++, sortedByAccuracy.slice(-2));
      var overallAccuracy = (cov_2gc0uz0ypt().s[8]++, shots.reduce(function (sum, shot) {
        cov_2gc0uz0ypt().f[3]++;
        cov_2gc0uz0ypt().s[9]++;
        return sum + shot.accuracy;
      }, 0) / shots.length);
      var recommendations = (cov_2gc0uz0ypt().s[10]++, this.generateShotRecommendations(shots));
      cov_2gc0uz0ypt().s[11]++;
      return {
        bestShots: bestShots,
        weakestShots: weakestShots,
        overallAccuracy: overallAccuracy,
        recommendations: recommendations
      };
    }
  }, {
    key: "analyzePerformanceTrends",
    value: function analyzePerformanceTrends(recentMatches, historicalData) {
      cov_2gc0uz0ypt().f[4]++;
      var trendAnalysis = (cov_2gc0uz0ypt().s[12]++, this.analyzeTrends(recentMatches, historicalData));
      var projectedImprovement = (cov_2gc0uz0ypt().s[13]++, this.projectSkillImprovement(historicalData.skillProgression));
      var trainingRecommendations = (cov_2gc0uz0ypt().s[14]++, this.generateTrainingRecommendations(trendAnalysis));
      cov_2gc0uz0ypt().s[15]++;
      return {
        trendAnalysis: trendAnalysis,
        projectedImprovement: projectedImprovement,
        trainingRecommendations: trainingRecommendations
      };
    }
  }, {
    key: "analyzeTacticalPerformance",
    value: function analyzeTacticalPerformance(playerStats, opponentStyle, courtSurface) {
      cov_2gc0uz0ypt().f[5]++;
      var tacticalEffectiveness = (cov_2gc0uz0ypt().s[16]++, this.calculateTacticalEffectiveness(playerStats, opponentStyle));
      var successfulTactics = (cov_2gc0uz0ypt().s[17]++, this.identifySuccessfulTactics(playerStats, opponentStyle));
      var tacticalAdjustments = (cov_2gc0uz0ypt().s[18]++, this.suggestTacticalAdjustments(playerStats, opponentStyle));
      var surfaceAdaptation = (cov_2gc0uz0ypt().s[19]++, this.calculateSurfaceAdaptation(playerStats, courtSurface));
      cov_2gc0uz0ypt().s[20]++;
      return {
        tacticalEffectiveness: tacticalEffectiveness,
        successfulTactics: successfulTactics,
        tacticalAdjustments: tacticalAdjustments,
        surfaceAdaptation: surfaceAdaptation
      };
    }
  }, {
    key: "analyzeFitnessMetrics",
    value: function analyzeFitnessMetrics(performanceBySet, matchDuration) {
      cov_2gc0uz0ypt().f[6]++;
      var enduranceRating = (cov_2gc0uz0ypt().s[21]++, this.calculateEnduranceRating(performanceBySet));
      var fatigueImpact = (cov_2gc0uz0ypt().s[22]++, this.calculateFatigueImpact(performanceBySet));
      var fitnessRecommendations = (cov_2gc0uz0ypt().s[23]++, this.generateFitnessRecommendations(enduranceRating, fatigueImpact));
      var recoveryNeeded = (cov_2gc0uz0ypt().s[24]++, this.calculateRecoveryTime(matchDuration, fatigueImpact));
      cov_2gc0uz0ypt().s[25]++;
      return {
        enduranceRating: enduranceRating,
        fatigueImpact: fatigueImpact,
        fitnessRecommendations: fitnessRecommendations,
        recoveryNeeded: recoveryNeeded
      };
    }
  }, {
    key: "calculateOverallRating",
    value: function calculateOverallRating(stats) {
      cov_2gc0uz0ypt().f[7]++;
      var winPercentage = (cov_2gc0uz0ypt().s[26]++, stats.pointsWon / stats.totalPoints * 100);
      var errorRate = (cov_2gc0uz0ypt().s[27]++, (stats.unforcedErrors + stats.doubleFaults) / stats.totalPoints * 100);
      var winnerRate = (cov_2gc0uz0ypt().s[28]++, stats.winners / stats.totalPoints * 100);
      var rating = (cov_2gc0uz0ypt().s[29]++, winPercentage * 0.4 + (100 - errorRate) * 0.3 + winnerRate * 0.2 + stats.firstServePercentage * 0.1);
      cov_2gc0uz0ypt().s[30]++;
      return Math.round(Math.max(0, Math.min(100, rating)));
    }
  }, {
    key: "generatePerformanceInsights",
    value: function generatePerformanceInsights(stats) {
      cov_2gc0uz0ypt().f[8]++;
      var strengths = (cov_2gc0uz0ypt().s[31]++, []);
      var weaknesses = (cov_2gc0uz0ypt().s[32]++, []);
      var recommendations = (cov_2gc0uz0ypt().s[33]++, []);
      var nextTrainingFocus = (cov_2gc0uz0ypt().s[34]++, []);
      cov_2gc0uz0ypt().s[35]++;
      if (stats.firstServePercentage > 65) {
        cov_2gc0uz0ypt().b[0][0]++;
        cov_2gc0uz0ypt().s[36]++;
        strengths.push('Consistent first serve');
      } else {
        cov_2gc0uz0ypt().b[0][1]++;
        cov_2gc0uz0ypt().s[37]++;
        weaknesses.push('First serve consistency');
        cov_2gc0uz0ypt().s[38]++;
        recommendations.push('Focus on serve placement drills');
        cov_2gc0uz0ypt().s[39]++;
        nextTrainingFocus.push('Serve technique');
      }
      var errorRate = (cov_2gc0uz0ypt().s[40]++, stats.unforcedErrors / stats.totalPoints * 100);
      cov_2gc0uz0ypt().s[41]++;
      if (errorRate < 15) {
        cov_2gc0uz0ypt().b[1][0]++;
        cov_2gc0uz0ypt().s[42]++;
        strengths.push('Low unforced error rate');
      } else {
        cov_2gc0uz0ypt().b[1][1]++;
        cov_2gc0uz0ypt().s[43]++;
        weaknesses.push('Too many unforced errors');
        cov_2gc0uz0ypt().s[44]++;
        recommendations.push('Work on shot selection and patience');
        cov_2gc0uz0ypt().s[45]++;
        nextTrainingFocus.push('Consistency training');
      }
      cov_2gc0uz0ypt().s[46]++;
      if (stats.netApproaches > 0) {
        cov_2gc0uz0ypt().b[2][0]++;
        var netSuccessRate = (cov_2gc0uz0ypt().s[47]++, stats.netPointsWon / stats.netApproaches * 100);
        cov_2gc0uz0ypt().s[48]++;
        if (netSuccessRate > 70) {
          cov_2gc0uz0ypt().b[3][0]++;
          cov_2gc0uz0ypt().s[49]++;
          strengths.push('Effective net play');
        } else {
          cov_2gc0uz0ypt().b[3][1]++;
          cov_2gc0uz0ypt().s[50]++;
          weaknesses.push('Net game needs improvement');
          cov_2gc0uz0ypt().s[51]++;
          nextTrainingFocus.push('Volley practice');
        }
      } else {
        cov_2gc0uz0ypt().b[2][1]++;
      }
      var competitiveReadiness = (cov_2gc0uz0ypt().s[52]++, this.calculateCompetitiveReadiness(stats));
      cov_2gc0uz0ypt().s[53]++;
      return {
        strengths: strengths,
        weaknesses: weaknesses,
        recommendations: recommendations,
        nextTrainingFocus: nextTrainingFocus,
        competitiveReadiness: competitiveReadiness
      };
    }
  }, {
    key: "calculateAdvancedMetrics",
    value: function calculateAdvancedMetrics(stats) {
      cov_2gc0uz0ypt().f[9]++;
      var aggressiveness = (cov_2gc0uz0ypt().s[54]++, Math.min(100, stats.winners / (stats.winners + stats.unforcedErrors) * 100));
      var consistency = (cov_2gc0uz0ypt().s[55]++, Math.max(0, 100 - stats.unforcedErrors / stats.totalPoints * 100 * 5));
      var courtCoverage = (cov_2gc0uz0ypt().s[56]++, 75);
      var mentalToughness = (cov_2gc0uz0ypt().s[57]++, this.calculateMentalToughness(stats));
      var adaptability = (cov_2gc0uz0ypt().s[58]++, 70);
      var fitnessLevel = (cov_2gc0uz0ypt().s[59]++, 80);
      cov_2gc0uz0ypt().s[60]++;
      return {
        aggressiveness: Math.round(aggressiveness),
        consistency: Math.round(consistency),
        courtCoverage: courtCoverage,
        mentalToughness: mentalToughness,
        adaptability: adaptability,
        fitnessLevel: fitnessLevel
      };
    }
  }, {
    key: "calculateMentalToughness",
    value: function calculateMentalToughness(stats) {
      cov_2gc0uz0ypt().f[10]++;
      var breakPointConversion = (cov_2gc0uz0ypt().s[61]++, stats.breakPointsTotal > 0 ? (cov_2gc0uz0ypt().b[4][0]++, stats.breakPointsConverted / stats.breakPointsTotal * 100) : (cov_2gc0uz0ypt().b[4][1]++, 50));
      cov_2gc0uz0ypt().s[62]++;
      return Math.round(Math.min(100, breakPointConversion * 1.2));
    }
  }, {
    key: "calculateCompetitiveReadiness",
    value: function calculateCompetitiveReadiness(stats) {
      cov_2gc0uz0ypt().f[11]++;
      var factors = (cov_2gc0uz0ypt().s[63]++, [stats.firstServePercentage, Math.max(0, 100 - stats.unforcedErrors / stats.totalPoints * 100 * 3), stats.pointsWon / stats.totalPoints * 100, stats.breakPointsTotal > 0 ? (cov_2gc0uz0ypt().b[5][0]++, stats.breakPointsConverted / stats.breakPointsTotal * 100) : (cov_2gc0uz0ypt().b[5][1]++, 70)]);
      cov_2gc0uz0ypt().s[64]++;
      return Math.round(factors.reduce(function (sum, factor) {
        cov_2gc0uz0ypt().f[12]++;
        cov_2gc0uz0ypt().s[65]++;
        return sum + factor;
      }, 0) / factors.length);
    }
  }, {
    key: "generateShotRecommendations",
    value: function generateShotRecommendations(shots) {
      cov_2gc0uz0ypt().f[13]++;
      var recommendations = (cov_2gc0uz0ypt().s[66]++, []);
      cov_2gc0uz0ypt().s[67]++;
      shots.forEach(function (shot) {
        cov_2gc0uz0ypt().f[14]++;
        cov_2gc0uz0ypt().s[68]++;
        if (shot.accuracy < 60) {
          cov_2gc0uz0ypt().b[6][0]++;
          cov_2gc0uz0ypt().s[69]++;
          recommendations.push(`Improve ${shot.shotType} consistency through repetition drills`);
        } else {
          cov_2gc0uz0ypt().b[6][1]++;
        }
        cov_2gc0uz0ypt().s[70]++;
        if (shot.errors > shot.winners) {
          cov_2gc0uz0ypt().b[7][0]++;
          cov_2gc0uz0ypt().s[71]++;
          recommendations.push(`Focus on ${shot.shotType} placement over power`);
        } else {
          cov_2gc0uz0ypt().b[7][1]++;
        }
      });
      cov_2gc0uz0ypt().s[72]++;
      return recommendations;
    }
  }, {
    key: "analyzeTrends",
    value: function analyzeTrends(recentMatches, historicalData) {
      cov_2gc0uz0ypt().f[15]++;
      var trends = (cov_2gc0uz0ypt().s[73]++, []);
      cov_2gc0uz0ypt().s[74]++;
      if (recentMatches.length >= 3) {
        cov_2gc0uz0ypt().b[8][0]++;
        var recentAvgWinRate = (cov_2gc0uz0ypt().s[75]++, recentMatches.reduce(function (sum, match) {
          cov_2gc0uz0ypt().f[16]++;
          cov_2gc0uz0ypt().s[76]++;
          return sum + match.pointsWon / match.totalPoints;
        }, 0) / recentMatches.length);
        cov_2gc0uz0ypt().s[77]++;
        if (recentAvgWinRate > 0.55) {
          cov_2gc0uz0ypt().b[9][0]++;
          cov_2gc0uz0ypt().s[78]++;
          trends.push('Improving match performance over recent games');
        } else {
          cov_2gc0uz0ypt().b[9][1]++;
          cov_2gc0uz0ypt().s[79]++;
          if (recentAvgWinRate < 0.45) {
            cov_2gc0uz0ypt().b[10][0]++;
            cov_2gc0uz0ypt().s[80]++;
            trends.push('Performance decline in recent matches - focus on fundamentals');
          } else {
            cov_2gc0uz0ypt().b[10][1]++;
          }
        }
      } else {
        cov_2gc0uz0ypt().b[8][1]++;
      }
      cov_2gc0uz0ypt().s[81]++;
      return trends;
    }
  }, {
    key: "projectSkillImprovement",
    value: function projectSkillImprovement(skillProgression) {
      cov_2gc0uz0ypt().f[17]++;
      cov_2gc0uz0ypt().s[82]++;
      return skillProgression.filter(function (skill) {
        cov_2gc0uz0ypt().f[18]++;
        cov_2gc0uz0ypt().s[83]++;
        return skill.trend === 'improving';
      }).map(function (skill) {
        cov_2gc0uz0ypt().f[19]++;
        cov_2gc0uz0ypt().s[84]++;
        return {
          skill: skill.skill,
          projectedRating: Math.min(100, skill.currentRating + skill.changeRate * 4)
        };
      });
    }
  }, {
    key: "generateTrainingRecommendations",
    value: function generateTrainingRecommendations(trendAnalysis) {
      cov_2gc0uz0ypt().f[20]++;
      var recommendations = (cov_2gc0uz0ypt().s[85]++, []);
      cov_2gc0uz0ypt().s[86]++;
      if (trendAnalysis.some(function (trend) {
        cov_2gc0uz0ypt().f[21]++;
        cov_2gc0uz0ypt().s[87]++;
        return trend.includes('decline');
      })) {
        cov_2gc0uz0ypt().b[11][0]++;
        cov_2gc0uz0ypt().s[88]++;
        recommendations.push('Increase practice frequency and focus on fundamentals');
        cov_2gc0uz0ypt().s[89]++;
        recommendations.push('Consider working with a coach to identify technical issues');
      } else {
        cov_2gc0uz0ypt().b[11][1]++;
      }
      cov_2gc0uz0ypt().s[90]++;
      if (trendAnalysis.some(function (trend) {
        cov_2gc0uz0ypt().f[22]++;
        cov_2gc0uz0ypt().s[91]++;
        return trend.includes('improving');
      })) {
        cov_2gc0uz0ypt().b[12][0]++;
        cov_2gc0uz0ypt().s[92]++;
        recommendations.push('Maintain current training routine');
        cov_2gc0uz0ypt().s[93]++;
        recommendations.push('Consider adding more challenging practice scenarios');
      } else {
        cov_2gc0uz0ypt().b[12][1]++;
      }
      cov_2gc0uz0ypt().s[94]++;
      return recommendations;
    }
  }, {
    key: "calculateTacticalEffectiveness",
    value: function calculateTacticalEffectiveness(stats, opponentStyle) {
      cov_2gc0uz0ypt().f[23]++;
      var baseEffectiveness = (cov_2gc0uz0ypt().s[95]++, stats.pointsWon / stats.totalPoints * 100);
      var adjustment = (cov_2gc0uz0ypt().s[96]++, 0);
      cov_2gc0uz0ypt().s[97]++;
      if ((cov_2gc0uz0ypt().b[14][0]++, opponentStyle === 'aggressive') && (cov_2gc0uz0ypt().b[14][1]++, stats.unforcedErrors < stats.totalPoints * 0.15)) {
        cov_2gc0uz0ypt().b[13][0]++;
        cov_2gc0uz0ypt().s[98]++;
        adjustment = 10;
      } else {
        cov_2gc0uz0ypt().b[13][1]++;
      }
      cov_2gc0uz0ypt().s[99]++;
      return Math.round(Math.min(100, baseEffectiveness + adjustment));
    }
  }, {
    key: "identifySuccessfulTactics",
    value: function identifySuccessfulTactics(stats, opponentStyle) {
      cov_2gc0uz0ypt().f[24]++;
      var tactics = (cov_2gc0uz0ypt().s[100]++, []);
      cov_2gc0uz0ypt().s[101]++;
      if ((cov_2gc0uz0ypt().b[16][0]++, stats.netApproaches > 0) && (cov_2gc0uz0ypt().b[16][1]++, stats.netPointsWon / stats.netApproaches > 0.7)) {
        cov_2gc0uz0ypt().b[15][0]++;
        cov_2gc0uz0ypt().s[102]++;
        tactics.push('Effective net approaches');
      } else {
        cov_2gc0uz0ypt().b[15][1]++;
      }
      cov_2gc0uz0ypt().s[103]++;
      if (stats.firstServePercentage > 70) {
        cov_2gc0uz0ypt().b[17][0]++;
        cov_2gc0uz0ypt().s[104]++;
        tactics.push('Strong serve placement');
      } else {
        cov_2gc0uz0ypt().b[17][1]++;
      }
      cov_2gc0uz0ypt().s[105]++;
      return tactics;
    }
  }, {
    key: "suggestTacticalAdjustments",
    value: function suggestTacticalAdjustments(stats, opponentStyle) {
      cov_2gc0uz0ypt().f[25]++;
      var adjustments = (cov_2gc0uz0ypt().s[106]++, []);
      cov_2gc0uz0ypt().s[107]++;
      if (stats.unforcedErrors > stats.totalPoints * 0.2) {
        cov_2gc0uz0ypt().b[18][0]++;
        cov_2gc0uz0ypt().s[108]++;
        adjustments.push('Reduce risk-taking, focus on consistency');
      } else {
        cov_2gc0uz0ypt().b[18][1]++;
      }
      cov_2gc0uz0ypt().s[109]++;
      if ((cov_2gc0uz0ypt().b[20][0]++, opponentStyle === 'defensive') && (cov_2gc0uz0ypt().b[20][1]++, stats.winners < stats.totalPoints * 0.1)) {
        cov_2gc0uz0ypt().b[19][0]++;
        cov_2gc0uz0ypt().s[110]++;
        adjustments.push('Increase aggression and court positioning');
      } else {
        cov_2gc0uz0ypt().b[19][1]++;
      }
      cov_2gc0uz0ypt().s[111]++;
      return adjustments;
    }
  }, {
    key: "calculateSurfaceAdaptation",
    value: function calculateSurfaceAdaptation(stats, surface) {
      cov_2gc0uz0ypt().f[26]++;
      var baseScore = (cov_2gc0uz0ypt().s[112]++, 70);
      cov_2gc0uz0ypt().s[113]++;
      if ((cov_2gc0uz0ypt().b[22][0]++, surface === 'clay') && (cov_2gc0uz0ypt().b[22][1]++, stats.unforcedErrors < stats.totalPoints * 0.15)) {
        cov_2gc0uz0ypt().b[21][0]++;
        cov_2gc0uz0ypt().s[114]++;
        baseScore += 15;
      } else {
        cov_2gc0uz0ypt().b[21][1]++;
      }
      cov_2gc0uz0ypt().s[115]++;
      if ((cov_2gc0uz0ypt().b[24][0]++, surface === 'grass') && (cov_2gc0uz0ypt().b[24][1]++, stats.netApproaches > stats.totalPoints * 0.1)) {
        cov_2gc0uz0ypt().b[23][0]++;
        cov_2gc0uz0ypt().s[116]++;
        baseScore += 10;
      } else {
        cov_2gc0uz0ypt().b[23][1]++;
      }
      cov_2gc0uz0ypt().s[117]++;
      return Math.min(100, baseScore);
    }
  }, {
    key: "calculateEnduranceRating",
    value: function calculateEnduranceRating(performanceBySet) {
      cov_2gc0uz0ypt().f[27]++;
      cov_2gc0uz0ypt().s[118]++;
      if (performanceBySet.length < 2) {
        cov_2gc0uz0ypt().b[25][0]++;
        cov_2gc0uz0ypt().s[119]++;
        return 75;
      } else {
        cov_2gc0uz0ypt().b[25][1]++;
      }
      var firstSetWinRate = (cov_2gc0uz0ypt().s[120]++, performanceBySet[0].pointsWon / performanceBySet[0].totalPoints);
      var lastSetWinRate = (cov_2gc0uz0ypt().s[121]++, performanceBySet[performanceBySet.length - 1].pointsWon / performanceBySet[performanceBySet.length - 1].totalPoints);
      var enduranceRatio = (cov_2gc0uz0ypt().s[122]++, lastSetWinRate / firstSetWinRate);
      cov_2gc0uz0ypt().s[123]++;
      return Math.round(Math.min(100, enduranceRatio * 75));
    }
  }, {
    key: "calculateFatigueImpact",
    value: function calculateFatigueImpact(performanceBySet) {
      cov_2gc0uz0ypt().f[28]++;
      cov_2gc0uz0ypt().s[124]++;
      if (performanceBySet.length < 2) {
        cov_2gc0uz0ypt().b[26][0]++;
        cov_2gc0uz0ypt().s[125]++;
        return 10;
      } else {
        cov_2gc0uz0ypt().b[26][1]++;
      }
      var errorRateIncrease = (cov_2gc0uz0ypt().s[126]++, performanceBySet.map(function (set) {
        cov_2gc0uz0ypt().f[29]++;
        cov_2gc0uz0ypt().s[127]++;
        return set.unforcedErrors / set.totalPoints;
      }));
      var avgIncrease = (cov_2gc0uz0ypt().s[128]++, errorRateIncrease[errorRateIncrease.length - 1] - errorRateIncrease[0]);
      cov_2gc0uz0ypt().s[129]++;
      return Math.round(Math.max(0, Math.min(100, avgIncrease * 500)));
    }
  }, {
    key: "generateFitnessRecommendations",
    value: function generateFitnessRecommendations(endurance, fatigue) {
      cov_2gc0uz0ypt().f[30]++;
      var recommendations = (cov_2gc0uz0ypt().s[130]++, []);
      cov_2gc0uz0ypt().s[131]++;
      if (endurance < 60) {
        cov_2gc0uz0ypt().b[27][0]++;
        cov_2gc0uz0ypt().s[132]++;
        recommendations.push('Increase cardiovascular training');
        cov_2gc0uz0ypt().s[133]++;
        recommendations.push('Add longer practice sessions to build endurance');
      } else {
        cov_2gc0uz0ypt().b[27][1]++;
      }
      cov_2gc0uz0ypt().s[134]++;
      if (fatigue > 30) {
        cov_2gc0uz0ypt().b[28][0]++;
        cov_2gc0uz0ypt().s[135]++;
        recommendations.push('Focus on recovery between points');
        cov_2gc0uz0ypt().s[136]++;
        recommendations.push('Improve physical conditioning');
      } else {
        cov_2gc0uz0ypt().b[28][1]++;
      }
      cov_2gc0uz0ypt().s[137]++;
      return recommendations;
    }
  }, {
    key: "calculateRecoveryTime",
    value: function calculateRecoveryTime(matchDuration, fatigueImpact) {
      cov_2gc0uz0ypt().f[31]++;
      var baseRecovery = (cov_2gc0uz0ypt().s[138]++, matchDuration / 60 * 2);
      var fatigueMultiplier = (cov_2gc0uz0ypt().s[139]++, 1 + fatigueImpact / 100);
      cov_2gc0uz0ypt().s[140]++;
      return Math.round(baseRecovery * fatigueMultiplier);
    }
  }]);
}();
export var performanceAnalyticsService = (cov_2gc0uz0ypt().s[141]++, new PerformanceAnalyticsService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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