ca40ebc2cbecf63812bfb7a06a190b7c
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1iyq7u5472() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useEdgeOptimization.ts";
  var hash = "f0c0999bb75ddaa9842d5e88d890a892980bfb3a";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useEdgeOptimization.ts",
    statementMap: {
      "0": {
        start: {
          line: 80,
          column: 29
        },
        end: {
          line: 80,
          column: 48
        }
      },
      "1": {
        start: {
          line: 82,
          column: 30
        },
        end: {
          line: 91,
          column: 4
        }
      },
      "2": {
        start: {
          line: 93,
          column: 28
        },
        end: {
          line: 119,
          column: 4
        }
      },
      "3": {
        start: {
          line: 124,
          column: 21
        },
        end: {
          line: 173,
          column: 35
        }
      },
      "4": {
        start: {
          line: 125,
          column: 4
        },
        end: {
          line: 125,
          column: 36
        }
      },
      "5": {
        start: {
          line: 125,
          column: 29
        },
        end: {
          line: 125,
          column: 36
        }
      },
      "6": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 172,
          column: 5
        }
      },
      "7": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 58
        }
      },
      "8": {
        start: {
          line: 128,
          column: 24
        },
        end: {
          line: 128,
          column: 55
        }
      },
      "9": {
        start: {
          line: 131,
          column: 43
        },
        end: {
          line: 131,
          column: 45
        }
      },
      "10": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 136,
          column: 7
        }
      },
      "11": {
        start: {
          line: 135,
          column: 8
        },
        end: {
          line: 135,
          column: 45
        }
      },
      "12": {
        start: {
          line: 138,
          column: 6
        },
        end: {
          line: 141,
          column: 7
        }
      },
      "13": {
        start: {
          line: 140,
          column: 8
        },
        end: {
          line: 140,
          column: 45
        }
      },
      "14": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 146,
          column: 7
        }
      },
      "15": {
        start: {
          line: 145,
          column: 8
        },
        end: {
          line: 145,
          column: 45
        }
      },
      "16": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 151,
          column: 7
        }
      },
      "17": {
        start: {
          line: 150,
          column: 8
        },
        end: {
          line: 150,
          column: 45
        }
      },
      "18": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 153,
          column: 38
        }
      },
      "19": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 33
        }
      },
      "20": {
        start: {
          line: 159,
          column: 6
        },
        end: {
          line: 159,
          column: 30
        }
      },
      "21": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 165,
          column: 10
        }
      },
      "22": {
        start: {
          line: 161,
          column: 24
        },
        end: {
          line: 165,
          column: 7
        }
      },
      "23": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 167,
          column: 72
        }
      },
      "24": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 70
        }
      },
      "25": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 59
        }
      },
      "26": {
        start: {
          line: 171,
          column: 24
        },
        end: {
          line: 171,
          column: 56
        }
      },
      "27": {
        start: {
          line: 178,
          column: 26
        },
        end: {
          line: 222,
          column: 47
        }
      },
      "28": {
        start: {
          line: 179,
          column: 4
        },
        end: {
          line: 182,
          column: 5
        }
      },
      "29": {
        start: {
          line: 180,
          column: 6
        },
        end: {
          line: 180,
          column: 81
        }
      },
      "30": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 18
        }
      },
      "31": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 221,
          column: 5
        }
      },
      "32": {
        start: {
          line: 185,
          column: 34
        },
        end: {
          line: 204,
          column: 7
        }
      },
      "33": {
        start: {
          line: 206,
          column: 31
        },
        end: {
          line: 208,
          column: 7
        }
      },
      "34": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 214,
          column: 9
        }
      },
      "35": {
        start: {
          line: 216,
          column: 6
        },
        end: {
          line: 216,
          column: 30
        }
      },
      "36": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 58
        }
      },
      "37": {
        start: {
          line: 220,
          column: 6
        },
        end: {
          line: 220,
          column: 18
        }
      },
      "38": {
        start: {
          line: 227,
          column: 30
        },
        end: {
          line: 257,
          column: 47
        }
      },
      "39": {
        start: {
          line: 228,
          column: 4
        },
        end: {
          line: 231,
          column: 5
        }
      },
      "40": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 229,
          column: 56
        }
      },
      "41": {
        start: {
          line: 230,
          column: 6
        },
        end: {
          line: 230,
          column: 18
        }
      },
      "42": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 256,
          column: 5
        }
      },
      "43": {
        start: {
          line: 234,
          column: 22
        },
        end: {
          line: 240,
          column: 7
        }
      },
      "44": {
        start: {
          line: 242,
          column: 21
        },
        end: {
          line: 242,
          column: 71
        }
      },
      "45": {
        start: {
          line: 244,
          column: 6
        },
        end: {
          line: 249,
          column: 9
        }
      },
      "46": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 251,
          column: 20
        }
      },
      "47": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 254,
          column: 63
        }
      },
      "48": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 18
        }
      },
      "49": {
        start: {
          line: 262,
          column: 23
        },
        end: {
          line: 299,
          column: 47
        }
      },
      "50": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 266,
          column: 5
        }
      },
      "51": {
        start: {
          line: 264,
          column: 6
        },
        end: {
          line: 264,
          column: 56
        }
      },
      "52": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 18
        }
      },
      "53": {
        start: {
          line: 268,
          column: 4
        },
        end: {
          line: 298,
          column: 5
        }
      },
      "54": {
        start: {
          line: 269,
          column: 29
        },
        end: {
          line: 282,
          column: 7
        }
      },
      "55": {
        start: {
          line: 284,
          column: 30
        },
        end: {
          line: 284,
          column: 82
        }
      },
      "56": {
        start: {
          line: 286,
          column: 6
        },
        end: {
          line: 291,
          column: 9
        }
      },
      "57": {
        start: {
          line: 293,
          column: 6
        },
        end: {
          line: 293,
          column: 29
        }
      },
      "58": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 296,
          column: 55
        }
      },
      "59": {
        start: {
          line: 297,
          column: 6
        },
        end: {
          line: 297,
          column: 18
        }
      },
      "60": {
        start: {
          line: 304,
          column: 26
        },
        end: {
          line: 336,
          column: 35
        }
      },
      "61": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 311,
          column: 5
        }
      },
      "62": {
        start: {
          line: 309,
          column: 6
        },
        end: {
          line: 309,
          column: 56
        }
      },
      "63": {
        start: {
          line: 310,
          column: 6
        },
        end: {
          line: 310,
          column: 13
        }
      },
      "64": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 335,
          column: 5
        }
      },
      "65": {
        start: {
          line: 314,
          column: 47
        },
        end: {
          line: 314,
          column: 49
        }
      },
      "66": {
        start: {
          line: 317,
          column: 6
        },
        end: {
          line: 321,
          column: 7
        }
      },
      "67": {
        start: {
          line: 318,
          column: 8
        },
        end: {
          line: 320,
          column: 10
        }
      },
      "68": {
        start: {
          line: 324,
          column: 6
        },
        end: {
          line: 328,
          column: 7
        }
      },
      "69": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 327,
          column: 10
        }
      },
      "70": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 48
        }
      },
      "71": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 331,
          column: 75
        }
      },
      "72": {
        start: {
          line: 334,
          column: 6
        },
        end: {
          line: 334,
          column: 66
        }
      },
      "73": {
        start: {
          line: 341,
          column: 27
        },
        end: {
          line: 357,
          column: 35
        }
      },
      "74": {
        start: {
          line: 342,
          column: 4
        },
        end: {
          line: 342,
          column: 42
        }
      },
      "75": {
        start: {
          line: 342,
          column: 30
        },
        end: {
          line: 342,
          column: 42
        }
      },
      "76": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 356,
          column: 5
        }
      },
      "77": {
        start: {
          line: 345,
          column: 22
        },
        end: {
          line: 350,
          column: 7
        }
      },
      "78": {
        start: {
          line: 352,
          column: 6
        },
        end: {
          line: 352,
          column: 21
        }
      },
      "79": {
        start: {
          line: 354,
          column: 6
        },
        end: {
          line: 354,
          column: 60
        }
      },
      "80": {
        start: {
          line: 355,
          column: 6
        },
        end: {
          line: 355,
          column: 18
        }
      },
      "81": {
        start: {
          line: 362,
          column: 23
        },
        end: {
          line: 364,
          column: 8
        }
      },
      "82": {
        start: {
          line: 363,
          column: 4
        },
        end: {
          line: 363,
          column: 51
        }
      },
      "83": {
        start: {
          line: 363,
          column: 23
        },
        end: {
          line: 363,
          column: 48
        }
      },
      "84": {
        start: {
          line: 369,
          column: 29
        },
        end: {
          line: 389,
          column: 8
        }
      },
      "85": {
        start: {
          line: 370,
          column: 4
        },
        end: {
          line: 388,
          column: 5
        }
      },
      "86": {
        start: {
          line: 372,
          column: 27
        },
        end: {
          line: 378,
          column: 7
        }
      },
      "87": {
        start: {
          line: 380,
          column: 6
        },
        end: {
          line: 383,
          column: 10
        }
      },
      "88": {
        start: {
          line: 380,
          column: 24
        },
        end: {
          line: 383,
          column: 7
        }
      },
      "89": {
        start: {
          line: 385,
          column: 6
        },
        end: {
          line: 385,
          column: 59
        }
      },
      "90": {
        start: {
          line: 387,
          column: 6
        },
        end: {
          line: 387,
          column: 62
        }
      },
      "91": {
        start: {
          line: 394,
          column: 26
        },
        end: {
          line: 445,
          column: 39
        }
      },
      "92": {
        start: {
          line: 395,
          column: 4
        },
        end: {
          line: 444,
          column: 5
        }
      },
      "93": {
        start: {
          line: 396,
          column: 55
        },
        end: {
          line: 396,
          column: 57
        }
      },
      "94": {
        start: {
          line: 399,
          column: 6
        },
        end: {
          line: 407,
          column: 7
        }
      },
      "95": {
        start: {
          line: 400,
          column: 27
        },
        end: {
          line: 400,
          column: 79
        }
      },
      "96": {
        start: {
          line: 401,
          column: 8
        },
        end: {
          line: 406,
          column: 10
        }
      },
      "97": {
        start: {
          line: 410,
          column: 6
        },
        end: {
          line: 418,
          column: 7
        }
      },
      "98": {
        start: {
          line: 411,
          column: 34
        },
        end: {
          line: 411,
          column: 76
        }
      },
      "99": {
        start: {
          line: 412,
          column: 8
        },
        end: {
          line: 417,
          column: 10
        }
      },
      "100": {
        start: {
          line: 421,
          column: 6
        },
        end: {
          line: 428,
          column: 7
        }
      },
      "101": {
        start: {
          line: 422,
          column: 26
        },
        end: {
          line: 422,
          column: 69
        }
      },
      "102": {
        start: {
          line: 423,
          column: 8
        },
        end: {
          line: 427,
          column: 10
        }
      },
      "103": {
        start: {
          line: 431,
          column: 6
        },
        end: {
          line: 438,
          column: 7
        }
      },
      "104": {
        start: {
          line: 432,
          column: 27
        },
        end: {
          line: 432,
          column: 81
        }
      },
      "105": {
        start: {
          line: 433,
          column: 8
        },
        end: {
          line: 437,
          column: 10
        }
      },
      "106": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 440,
          column: 51
        }
      },
      "107": {
        start: {
          line: 440,
          column: 24
        },
        end: {
          line: 440,
          column: 48
        }
      },
      "108": {
        start: {
          line: 443,
          column: 6
        },
        end: {
          line: 443,
          column: 59
        }
      },
      "109": {
        start: {
          line: 448,
          column: 2
        },
        end: {
          line: 459,
          column: 82
        }
      },
      "110": {
        start: {
          line: 449,
          column: 4
        },
        end: {
          line: 449,
          column: 65
        }
      },
      "111": {
        start: {
          line: 449,
          column: 58
        },
        end: {
          line: 449,
          column: 65
        }
      },
      "112": {
        start: {
          line: 451,
          column: 21
        },
        end: {
          line: 456,
          column: 35
        }
      },
      "113": {
        start: {
          line: 453,
          column: 6
        },
        end: {
          line: 455,
          column: 7
        }
      },
      "114": {
        start: {
          line: 454,
          column: 8
        },
        end: {
          line: 454,
          column: 55
        }
      },
      "115": {
        start: {
          line: 458,
          column: 4
        },
        end: {
          line: 458,
          column: 41
        }
      },
      "116": {
        start: {
          line: 458,
          column: 17
        },
        end: {
          line: 458,
          column: 40
        }
      },
      "117": {
        start: {
          line: 462,
          column: 2
        },
        end: {
          line: 470,
          column: 45
        }
      },
      "118": {
        start: {
          line: 463,
          column: 4
        },
        end: {
          line: 463,
          column: 37
        }
      },
      "119": {
        start: {
          line: 463,
          column: 30
        },
        end: {
          line: 463,
          column: 37
        }
      },
      "120": {
        start: {
          line: 465,
          column: 21
        },
        end: {
          line: 467,
          column: 13
        }
      },
      "121": {
        start: {
          line: 466,
          column: 6
        },
        end: {
          line: 466,
          column: 24
        }
      },
      "122": {
        start: {
          line: 469,
          column: 4
        },
        end: {
          line: 469,
          column: 41
        }
      },
      "123": {
        start: {
          line: 469,
          column: 17
        },
        end: {
          line: 469,
          column: 40
        }
      },
      "124": {
        start: {
          line: 473,
          column: 2
        },
        end: {
          line: 477,
          column: 40
        }
      },
      "125": {
        start: {
          line: 474,
          column: 4
        },
        end: {
          line: 476,
          column: 5
        }
      },
      "126": {
        start: {
          line: 475,
          column: 6
        },
        end: {
          line: 475,
          column: 19
        }
      },
      "127": {
        start: {
          line: 480,
          column: 2
        },
        end: {
          line: 504,
          column: 5
        }
      },
      "128": {
        start: {
          line: 480,
          column: 24
        },
        end: {
          line: 493,
          column: 3
        }
      }
    },
    fnMap: {
      "0": {
        name: "useEdgeOptimization",
        decl: {
          start: {
            line: 77,
            column: 16
          },
          end: {
            line: 77,
            column: 35
          }
        },
        loc: {
          start: {
            line: 79,
            column: 29
          },
          end: {
            line: 505,
            column: 1
          }
        },
        line: 79
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 124,
            column: 33
          },
          end: {
            line: 124,
            column: 34
          }
        },
        loc: {
          start: {
            line: 124,
            column: 45
          },
          end: {
            line: 173,
            column: 3
          }
        },
        line: 124
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 128,
            column: 15
          },
          end: {
            line: 128,
            column: 16
          }
        },
        loc: {
          start: {
            line: 128,
            column: 24
          },
          end: {
            line: 128,
            column: 55
          }
        },
        line: 128
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 161,
            column: 15
          },
          end: {
            line: 161,
            column: 16
          }
        },
        loc: {
          start: {
            line: 161,
            column: 24
          },
          end: {
            line: 165,
            column: 7
          }
        },
        line: 161
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 171,
            column: 15
          },
          end: {
            line: 171,
            column: 16
          }
        },
        loc: {
          start: {
            line: 171,
            column: 24
          },
          end: {
            line: 171,
            column: 56
          }
        },
        line: 171
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 178,
            column: 38
          },
          end: {
            line: 178,
            column: 39
          }
        },
        loc: {
          start: {
            line: 178,
            column: 86
          },
          end: {
            line: 222,
            column: 3
          }
        },
        line: 178
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 227,
            column: 42
          },
          end: {
            line: 227,
            column: 43
          }
        },
        loc: {
          start: {
            line: 227,
            column: 86
          },
          end: {
            line: 257,
            column: 3
          }
        },
        line: 227
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 262,
            column: 35
          },
          end: {
            line: 262,
            column: 36
          }
        },
        loc: {
          start: {
            line: 262,
            column: 59
          },
          end: {
            line: 299,
            column: 3
          }
        },
        line: 262
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 304,
            column: 38
          },
          end: {
            line: 304,
            column: 39
          }
        },
        loc: {
          start: {
            line: 307,
            column: 7
          },
          end: {
            line: 336,
            column: 3
          }
        },
        line: 307
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 341,
            column: 39
          },
          end: {
            line: 341,
            column: 40
          }
        },
        loc: {
          start: {
            line: 341,
            column: 51
          },
          end: {
            line: 357,
            column: 3
          }
        },
        line: 341
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 362,
            column: 35
          },
          end: {
            line: 362,
            column: 36
          }
        },
        loc: {
          start: {
            line: 362,
            column: 83
          },
          end: {
            line: 364,
            column: 3
          }
        },
        line: 362
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 363,
            column: 14
          },
          end: {
            line: 363,
            column: 15
          }
        },
        loc: {
          start: {
            line: 363,
            column: 23
          },
          end: {
            line: 363,
            column: 48
          }
        },
        line: 363
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 369,
            column: 41
          },
          end: {
            line: 369,
            column: 42
          }
        },
        loc: {
          start: {
            line: 369,
            column: 53
          },
          end: {
            line: 389,
            column: 3
          }
        },
        line: 369
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 380,
            column: 15
          },
          end: {
            line: 380,
            column: 16
          }
        },
        loc: {
          start: {
            line: 380,
            column: 24
          },
          end: {
            line: 383,
            column: 7
          }
        },
        line: 380
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 394,
            column: 38
          },
          end: {
            line: 394,
            column: 39
          }
        },
        loc: {
          start: {
            line: 394,
            column: 50
          },
          end: {
            line: 445,
            column: 3
          }
        },
        line: 394
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 440,
            column: 15
          },
          end: {
            line: 440,
            column: 16
          }
        },
        loc: {
          start: {
            line: 440,
            column: 24
          },
          end: {
            line: 440,
            column: 48
          }
        },
        line: 440
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 448,
            column: 12
          },
          end: {
            line: 448,
            column: 13
          }
        },
        loc: {
          start: {
            line: 448,
            column: 18
          },
          end: {
            line: 459,
            column: 3
          }
        },
        line: 448
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 451,
            column: 33
          },
          end: {
            line: 451,
            column: 34
          }
        },
        loc: {
          start: {
            line: 451,
            column: 45
          },
          end: {
            line: 456,
            column: 5
          }
        },
        line: 451
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 458,
            column: 11
          },
          end: {
            line: 458,
            column: 12
          }
        },
        loc: {
          start: {
            line: 458,
            column: 17
          },
          end: {
            line: 458,
            column: 40
          }
        },
        line: 458
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 462,
            column: 12
          },
          end: {
            line: 462,
            column: 13
          }
        },
        loc: {
          start: {
            line: 462,
            column: 18
          },
          end: {
            line: 470,
            column: 3
          }
        },
        line: 462
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 465,
            column: 33
          },
          end: {
            line: 465,
            column: 34
          }
        },
        loc: {
          start: {
            line: 465,
            column: 39
          },
          end: {
            line: 467,
            column: 5
          }
        },
        line: 465
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 469,
            column: 11
          },
          end: {
            line: 469,
            column: 12
          }
        },
        loc: {
          start: {
            line: 469,
            column: 17
          },
          end: {
            line: 469,
            column: 40
          }
        },
        line: 469
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 473,
            column: 12
          },
          end: {
            line: 473,
            column: 13
          }
        },
        loc: {
          start: {
            line: 473,
            column: 18
          },
          end: {
            line: 477,
            column: 3
          }
        },
        line: 473
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 480,
            column: 17
          },
          end: {
            line: 480,
            column: 18
          }
        },
        loc: {
          start: {
            line: 480,
            column: 24
          },
          end: {
            line: 493,
            column: 3
          }
        },
        line: 480
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 78,
            column: 2
          },
          end: {
            line: 78,
            column: 53
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 78,
            column: 51
          },
          end: {
            line: 78,
            column: 53
          }
        }],
        line: 78
      },
      "1": {
        loc: {
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 125,
            column: 36
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 125,
            column: 4
          },
          end: {
            line: 125,
            column: 36
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 125
      },
      "2": {
        loc: {
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 136,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 6
          },
          end: {
            line: 136,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "3": {
        loc: {
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 141,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 6
          },
          end: {
            line: 141,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "4": {
        loc: {
          start: {
            line: 143,
            column: 6
          },
          end: {
            line: 146,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 143,
            column: 6
          },
          end: {
            line: 146,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 143
      },
      "5": {
        loc: {
          start: {
            line: 148,
            column: 6
          },
          end: {
            line: 151,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 148,
            column: 6
          },
          end: {
            line: 151,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 148
      },
      "6": {
        loc: {
          start: {
            line: 178,
            column: 64
          },
          end: {
            line: 178,
            column: 81
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 178,
            column: 79
          },
          end: {
            line: 178,
            column: 81
          }
        }],
        line: 178
      },
      "7": {
        loc: {
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 182,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 4
          },
          end: {
            line: 182,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "8": {
        loc: {
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 179,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 179,
            column: 28
          }
        }, {
          start: {
            line: 179,
            column: 32
          },
          end: {
            line: 179,
            column: 51
          }
        }],
        line: 179
      },
      "9": {
        loc: {
          start: {
            line: 228,
            column: 4
          },
          end: {
            line: 231,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 4
          },
          end: {
            line: 231,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "10": {
        loc: {
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 266,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 266,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "11": {
        loc: {
          start: {
            line: 271,
            column: 14
          },
          end: {
            line: 271,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 271,
            column: 14
          },
          end: {
            line: 271,
            column: 26
          }
        }, {
          start: {
            line: 271,
            column: 30
          },
          end: {
            line: 271,
            column: 35
          }
        }],
        line: 271
      },
      "12": {
        loc: {
          start: {
            line: 272,
            column: 14
          },
          end: {
            line: 272,
            column: 33
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 272,
            column: 14
          },
          end: {
            line: 272,
            column: 26
          }
        }, {
          start: {
            line: 272,
            column: 30
          },
          end: {
            line: 272,
            column: 33
          }
        }],
        line: 272
      },
      "13": {
        loc: {
          start: {
            line: 273,
            column: 16
          },
          end: {
            line: 273,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 273,
            column: 16
          },
          end: {
            line: 273,
            column: 30
          }
        }, {
          start: {
            line: 273,
            column: 34
          },
          end: {
            line: 273,
            column: 39
          }
        }],
        line: 273
      },
      "14": {
        loc: {
          start: {
            line: 274,
            column: 17
          },
          end: {
            line: 274,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 274,
            column: 17
          },
          end: {
            line: 274,
            column: 32
          }
        }, {
          start: {
            line: 274,
            column: 36
          },
          end: {
            line: 274,
            column: 38
          }
        }],
        line: 274
      },
      "15": {
        loc: {
          start: {
            line: 275,
            column: 22
          },
          end: {
            line: 279,
            column: 21
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 275,
            column: 43
          },
          end: {
            line: 279,
            column: 9
          }
        }, {
          start: {
            line: 279,
            column: 12
          },
          end: {
            line: 279,
            column: 21
          }
        }],
        line: 275
      },
      "16": {
        loc: {
          start: {
            line: 280,
            column: 18
          },
          end: {
            line: 280,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 280,
            column: 18
          },
          end: {
            line: 280,
            column: 34
          }
        }, {
          start: {
            line: 280,
            column: 38
          },
          end: {
            line: 280,
            column: 46
          }
        }],
        line: 280
      },
      "17": {
        loc: {
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 306,
            column: 38
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 306,
            column: 24
          },
          end: {
            line: 306,
            column: 38
          }
        }],
        line: 306
      },
      "18": {
        loc: {
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 311,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 308,
            column: 4
          },
          end: {
            line: 311,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 308
      },
      "19": {
        loc: {
          start: {
            line: 317,
            column: 6
          },
          end: {
            line: 321,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 317,
            column: 6
          },
          end: {
            line: 321,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 317
      },
      "20": {
        loc: {
          start: {
            line: 324,
            column: 6
          },
          end: {
            line: 328,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 324,
            column: 6
          },
          end: {
            line: 328,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 324
      },
      "21": {
        loc: {
          start: {
            line: 342,
            column: 4
          },
          end: {
            line: 342,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 342,
            column: 4
          },
          end: {
            line: 342,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 342
      },
      "22": {
        loc: {
          start: {
            line: 346,
            column: 13
          },
          end: {
            line: 346,
            column: 97
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 346,
            column: 38
          },
          end: {
            line: 346,
            column: 90
          }
        }, {
          start: {
            line: 346,
            column: 93
          },
          end: {
            line: 346,
            column: 97
          }
        }],
        line: 346
      },
      "23": {
        loc: {
          start: {
            line: 347,
            column: 23
          },
          end: {
            line: 347,
            column: 101
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 347,
            column: 52
          },
          end: {
            line: 347,
            column: 94
          }
        }, {
          start: {
            line: 347,
            column: 97
          },
          end: {
            line: 347,
            column: 101
          }
        }],
        line: 347
      },
      "24": {
        loc: {
          start: {
            line: 348,
            column: 23
          },
          end: {
            line: 348,
            column: 107
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 348,
            column: 57
          },
          end: {
            line: 348,
            column: 100
          }
        }, {
          start: {
            line: 348,
            column: 103
          },
          end: {
            line: 348,
            column: 107
          }
        }],
        line: 348
      },
      "25": {
        loc: {
          start: {
            line: 349,
            column: 25
          },
          end: {
            line: 349,
            column: 117
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 349,
            column: 56
          },
          end: {
            line: 349,
            column: 110
          }
        }, {
          start: {
            line: 349,
            column: 113
          },
          end: {
            line: 349,
            column: 117
          }
        }],
        line: 349
      },
      "26": {
        loc: {
          start: {
            line: 399,
            column: 6
          },
          end: {
            line: 407,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 399,
            column: 6
          },
          end: {
            line: 407,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 399
      },
      "27": {
        loc: {
          start: {
            line: 410,
            column: 6
          },
          end: {
            line: 418,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 410,
            column: 6
          },
          end: {
            line: 418,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 410
      },
      "28": {
        loc: {
          start: {
            line: 421,
            column: 6
          },
          end: {
            line: 428,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 421,
            column: 6
          },
          end: {
            line: 428,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 421
      },
      "29": {
        loc: {
          start: {
            line: 431,
            column: 6
          },
          end: {
            line: 438,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 431,
            column: 6
          },
          end: {
            line: 438,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 431
      },
      "30": {
        loc: {
          start: {
            line: 449,
            column: 4
          },
          end: {
            line: 449,
            column: 65
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 449,
            column: 4
          },
          end: {
            line: 449,
            column: 65
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 449
      },
      "31": {
        loc: {
          start: {
            line: 449,
            column: 8
          },
          end: {
            line: 449,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 449,
            column: 8
          },
          end: {
            line: 449,
            column: 32
          }
        }, {
          start: {
            line: 449,
            column: 36
          },
          end: {
            line: 449,
            column: 56
          }
        }],
        line: 449
      },
      "32": {
        loc: {
          start: {
            line: 453,
            column: 6
          },
          end: {
            line: 455,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 453,
            column: 6
          },
          end: {
            line: 455,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 453
      },
      "33": {
        loc: {
          start: {
            line: 463,
            column: 4
          },
          end: {
            line: 463,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 463,
            column: 4
          },
          end: {
            line: 463,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 463
      },
      "34": {
        loc: {
          start: {
            line: 474,
            column: 4
          },
          end: {
            line: 476,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 474,
            column: 4
          },
          end: {
            line: 476,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 474
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f0c0999bb75ddaa9842d5e88d890a892980bfb3a"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1iyq7u5472 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1iyq7u5472();
import { useState, useEffect, useCallback, useMemo } from 'react';
import { globalCDNManager } from "../services/edge/GlobalCDNManager";
import { edgeFunctionManager } from "../services/edge/EdgeFunctionManager";
import { smartLoadBalancer } from "../services/edge/SmartLoadBalancer";
import { geoOptimizedContentManager } from "../services/edge/GeoOptimizedContentManager";
import { useAIOptimization } from "./useAIOptimization";
export function useEdgeOptimization() {
  var initialConfig = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1iyq7u5472().b[0][0]++, {});
  cov_1iyq7u5472().f[0]++;
  var _ref = (cov_1iyq7u5472().s[0]++, useAIOptimization()),
    aiState = _ref.state;
  var _ref2 = (cov_1iyq7u5472().s[1]++, useState(Object.assign({
      enableGlobalCDN: true,
      enableEdgeFunctions: true,
      enableSmartLoadBalancing: true,
      enableGeoOptimization: true,
      autoOptimization: true,
      optimizationInterval: 60000,
      regions: ['us-east-1', 'eu-west-1', 'ap-southeast-1']
    }, initialConfig))),
    _ref3 = _slicedToArray(_ref2, 2),
    config = _ref3[0],
    setConfig = _ref3[1];
  var _ref4 = (cov_1iyq7u5472().s[2]++, useState({
      isInitialized: false,
      isOptimizing: false,
      globalPerformance: {
        averageLatency: 0,
        globalUptime: 0,
        cdnHitRate: 0,
        edgeFunctionLatency: 0
      },
      userLocation: null,
      contentOptimization: {
        bandwidthSavings: 0,
        loadTimeImprovement: 0,
        regionalAdaptation: false
      },
      edgeMetrics: {
        functionsDeployed: 0,
        executionsPerSecond: 0,
        averageExecutionTime: 0,
        successRate: 0
      },
      loadBalancing: {
        activeEndpoints: 0,
        trafficDistribution: {},
        failoverEvents: 0
      }
    })),
    _ref5 = _slicedToArray(_ref4, 2),
    state = _ref5[0],
    setState = _ref5[1];
  var initialize = (cov_1iyq7u5472().s[3]++, useCallback(_asyncToGenerator(function* () {
    cov_1iyq7u5472().f[1]++;
    cov_1iyq7u5472().s[4]++;
    if (state.isInitialized) {
      cov_1iyq7u5472().b[1][0]++;
      cov_1iyq7u5472().s[5]++;
      return;
    } else {
      cov_1iyq7u5472().b[1][1]++;
    }
    cov_1iyq7u5472().s[6]++;
    try {
      cov_1iyq7u5472().s[7]++;
      setState(function (prev) {
        cov_1iyq7u5472().f[2]++;
        cov_1iyq7u5472().s[8]++;
        return Object.assign({}, prev, {
          isOptimizing: true
        });
      });
      var initPromises = (cov_1iyq7u5472().s[9]++, []);
      cov_1iyq7u5472().s[10]++;
      if (config.enableGlobalCDN) {
        cov_1iyq7u5472().b[2][0]++;
        cov_1iyq7u5472().s[11]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_1iyq7u5472().b[2][1]++;
      }
      cov_1iyq7u5472().s[12]++;
      if (config.enableEdgeFunctions) {
        cov_1iyq7u5472().b[3][0]++;
        cov_1iyq7u5472().s[13]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_1iyq7u5472().b[3][1]++;
      }
      cov_1iyq7u5472().s[14]++;
      if (config.enableSmartLoadBalancing) {
        cov_1iyq7u5472().b[4][0]++;
        cov_1iyq7u5472().s[15]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_1iyq7u5472().b[4][1]++;
      }
      cov_1iyq7u5472().s[16]++;
      if (config.enableGeoOptimization) {
        cov_1iyq7u5472().b[5][0]++;
        cov_1iyq7u5472().s[17]++;
        initPromises.push(Promise.resolve());
      } else {
        cov_1iyq7u5472().b[5][1]++;
      }
      cov_1iyq7u5472().s[18]++;
      yield Promise.all(initPromises);
      cov_1iyq7u5472().s[19]++;
      yield detectUserLocation();
      cov_1iyq7u5472().s[20]++;
      yield updateEdgeState();
      cov_1iyq7u5472().s[21]++;
      setState(function (prev) {
        cov_1iyq7u5472().f[3]++;
        cov_1iyq7u5472().s[22]++;
        return Object.assign({}, prev, {
          isInitialized: true,
          isOptimizing: false
        });
      });
      cov_1iyq7u5472().s[23]++;
      console.log('Edge Optimization systems initialized successfully');
    } catch (error) {
      cov_1iyq7u5472().s[24]++;
      console.error('Failed to initialize edge optimization:', error);
      cov_1iyq7u5472().s[25]++;
      setState(function (prev) {
        cov_1iyq7u5472().f[4]++;
        cov_1iyq7u5472().s[26]++;
        return Object.assign({}, prev, {
          isOptimizing: false
        });
      });
    }
  }), [state.isInitialized, config]));
  var optimizeContent = (cov_1iyq7u5472().s[27]++, useCallback(function () {
    var _ref7 = _asyncToGenerator(function* (contentId) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1iyq7u5472().b[6][0]++, {});
      cov_1iyq7u5472().f[5]++;
      cov_1iyq7u5472().s[28]++;
      if ((cov_1iyq7u5472().b[8][0]++, !state.isInitialized) || (cov_1iyq7u5472().b[8][1]++, !state.userLocation)) {
        cov_1iyq7u5472().b[7][0]++;
        cov_1iyq7u5472().s[29]++;
        console.warn('Edge optimization not initialized or location not detected');
        cov_1iyq7u5472().s[30]++;
        return null;
      } else {
        cov_1iyq7u5472().b[7][1]++;
      }
      cov_1iyq7u5472().s[31]++;
      try {
        var optimizationRequest = (cov_1iyq7u5472().s[32]++, {
          contentId: contentId,
          userLocation: {
            country: state.userLocation.country,
            region: state.userLocation.region,
            city: state.userLocation.city,
            timezone: state.userLocation.timezone,
            coordinates: state.userLocation.coordinates,
            language: 'en',
            currency: 'USD',
            locale: 'en-US'
          },
          deviceInfo: {
            type: 'mobile',
            screen: {
              width: 375,
              height: 812
            },
            connection: 'wifi',
            capabilities: ['webp', 'h264']
          },
          preferences: options.preferences
        });
        var optimizedContent = (cov_1iyq7u5472().s[33]++, yield geoOptimizedContentManager.getOptimizedContent(optimizationRequest));
        cov_1iyq7u5472().s[34]++;
        console.log(`Optimized content ${contentId}:`, {
          provider: optimizedContent.deliveryUrls.cdn,
          optimizations: optimizedContent.optimizations.applied,
          savings: optimizedContent.optimizations.estimatedSavings
        });
        cov_1iyq7u5472().s[35]++;
        return optimizedContent;
      } catch (error) {
        cov_1iyq7u5472().s[36]++;
        console.error('Failed to optimize content:', error);
        cov_1iyq7u5472().s[37]++;
        return null;
      }
    });
    return function (_x) {
      return _ref7.apply(this, arguments);
    };
  }(), [state.isInitialized, state.userLocation]));
  var executeEdgeFunction = (cov_1iyq7u5472().s[38]++, useCallback(function () {
    var _ref8 = _asyncToGenerator(function* (functionId, payload) {
      cov_1iyq7u5472().f[6]++;
      cov_1iyq7u5472().s[39]++;
      if (!state.isInitialized) {
        cov_1iyq7u5472().b[9][0]++;
        cov_1iyq7u5472().s[40]++;
        console.warn('Edge optimization not initialized');
        cov_1iyq7u5472().s[41]++;
        return null;
      } else {
        cov_1iyq7u5472().b[9][1]++;
      }
      cov_1iyq7u5472().s[42]++;
      try {
        var _state$userLocation;
        var request = (cov_1iyq7u5472().s[43]++, {
          functionId: functionId,
          region: (_state$userLocation = state.userLocation) == null ? void 0 : _state$userLocation.region,
          payload: payload,
          headers: {},
          priority: 'medium'
        });
        var result = (cov_1iyq7u5472().s[44]++, yield edgeFunctionManager.executeFunction(request));
        cov_1iyq7u5472().s[45]++;
        console.log(`Executed edge function ${functionId}:`, {
          success: result.success,
          executionTime: result.executionTime,
          region: result.region,
          cacheStatus: result.cacheStatus
        });
        cov_1iyq7u5472().s[46]++;
        return result;
      } catch (error) {
        cov_1iyq7u5472().s[47]++;
        console.error('Failed to execute edge function:', error);
        cov_1iyq7u5472().s[48]++;
        return null;
      }
    });
    return function (_x2, _x3) {
      return _ref8.apply(this, arguments);
    };
  }(), [state.isInitialized, state.userLocation]));
  var routeRequest = (cov_1iyq7u5472().s[49]++, useCallback(function () {
    var _ref9 = _asyncToGenerator(function* (request) {
      cov_1iyq7u5472().f[7]++;
      cov_1iyq7u5472().s[50]++;
      if (!state.isInitialized) {
        cov_1iyq7u5472().b[10][0]++;
        cov_1iyq7u5472().s[51]++;
        console.warn('Edge optimization not initialized');
        cov_1iyq7u5472().s[52]++;
        return null;
      } else {
        cov_1iyq7u5472().b[10][1]++;
      }
      cov_1iyq7u5472().s[53]++;
      try {
        var trafficRequest = (cov_1iyq7u5472().s[54]++, {
          id: `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: (cov_1iyq7u5472().b[11][0]++, request.type) || (cov_1iyq7u5472().b[11][1]++, 'api'),
          path: (cov_1iyq7u5472().b[12][0]++, request.path) || (cov_1iyq7u5472().b[12][1]++, '/'),
          method: (cov_1iyq7u5472().b[13][0]++, request.method) || (cov_1iyq7u5472().b[13][1]++, 'GET'),
          headers: (cov_1iyq7u5472().b[14][0]++, request.headers) || (cov_1iyq7u5472().b[14][1]++, {}),
          userLocation: state.userLocation ? (cov_1iyq7u5472().b[15][0]++, {
            country: state.userLocation.country,
            region: state.userLocation.region,
            coordinates: state.userLocation.coordinates
          }) : (cov_1iyq7u5472().b[15][1]++, undefined),
          priority: (cov_1iyq7u5472().b[16][0]++, request.priority) || (cov_1iyq7u5472().b[16][1]++, 'medium'),
          sessionId: request.sessionId
        });
        var routingDecision = (cov_1iyq7u5472().s[55]++, yield smartLoadBalancer.routeRequest(trafficRequest));
        cov_1iyq7u5472().s[56]++;
        console.log(`Routed request to:`, {
          endpoint: routingDecision.endpoint.url,
          reason: routingDecision.reason,
          confidence: routingDecision.confidence,
          estimatedResponseTime: routingDecision.estimatedResponseTime
        });
        cov_1iyq7u5472().s[57]++;
        return routingDecision;
      } catch (error) {
        cov_1iyq7u5472().s[58]++;
        console.error('Failed to route request:', error);
        cov_1iyq7u5472().s[59]++;
        return null;
      }
    });
    return function (_x4) {
      return _ref9.apply(this, arguments);
    };
  }(), [state.isInitialized, state.userLocation]));
  var preloadGlobally = (cov_1iyq7u5472().s[60]++, useCallback(function () {
    var _ref0 = _asyncToGenerator(function* (contentIds) {
      var regions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1iyq7u5472().b[17][0]++, config.regions);
      cov_1iyq7u5472().f[8]++;
      cov_1iyq7u5472().s[61]++;
      if (!state.isInitialized) {
        cov_1iyq7u5472().b[18][0]++;
        cov_1iyq7u5472().s[62]++;
        console.warn('Edge optimization not initialized');
        cov_1iyq7u5472().s[63]++;
        return;
      } else {
        cov_1iyq7u5472().b[18][1]++;
      }
      cov_1iyq7u5472().s[64]++;
      try {
        var preloadPromises = (cov_1iyq7u5472().s[65]++, []);
        cov_1iyq7u5472().s[66]++;
        if (config.enableGlobalCDN) {
          cov_1iyq7u5472().b[19][0]++;
          cov_1iyq7u5472().s[67]++;
          preloadPromises.push(globalCDNManager.preloadContentGlobally(contentIds, regions));
        } else {
          cov_1iyq7u5472().b[19][1]++;
        }
        cov_1iyq7u5472().s[68]++;
        if (config.enableGeoOptimization) {
          cov_1iyq7u5472().b[20][0]++;
          cov_1iyq7u5472().s[69]++;
          preloadPromises.push(geoOptimizedContentManager.preloadContentGlobally(contentIds, regions));
        } else {
          cov_1iyq7u5472().b[20][1]++;
        }
        cov_1iyq7u5472().s[70]++;
        yield Promise.allSettled(preloadPromises);
        cov_1iyq7u5472().s[71]++;
        console.log(`Preloaded ${contentIds.length} content items globally`);
      } catch (error) {
        cov_1iyq7u5472().s[72]++;
        console.error('Failed to preload content globally:', error);
      }
    });
    return function (_x5) {
      return _ref0.apply(this, arguments);
    };
  }(), [state.isInitialized, config]));
  var getGlobalMetrics = (cov_1iyq7u5472().s[73]++, useCallback(_asyncToGenerator(function* () {
    cov_1iyq7u5472().f[9]++;
    cov_1iyq7u5472().s[74]++;
    if (!state.isInitialized) {
      cov_1iyq7u5472().b[21][0]++;
      cov_1iyq7u5472().s[75]++;
      return null;
    } else {
      cov_1iyq7u5472().b[21][1]++;
    }
    cov_1iyq7u5472().s[76]++;
    try {
      var metrics = (cov_1iyq7u5472().s[77]++, {
        cdn: config.enableGlobalCDN ? (cov_1iyq7u5472().b[22][0]++, yield globalCDNManager.getGlobalPerformanceMetrics()) : (cov_1iyq7u5472().b[22][1]++, null),
        edgeFunctions: config.enableEdgeFunctions ? (cov_1iyq7u5472().b[23][0]++, edgeFunctionManager.getDeployedFunctions()) : (cov_1iyq7u5472().b[23][1]++, null),
        loadBalancing: config.enableSmartLoadBalancing ? (cov_1iyq7u5472().b[24][0]++, smartLoadBalancer.getLoadBalancingMetrics()) : (cov_1iyq7u5472().b[24][1]++, null),
        geoOptimization: config.enableGeoOptimization ? (cov_1iyq7u5472().b[25][0]++, geoOptimizedContentManager.getGeoOptimizationMetrics()) : (cov_1iyq7u5472().b[25][1]++, null)
      });
      cov_1iyq7u5472().s[78]++;
      return metrics;
    } catch (error) {
      cov_1iyq7u5472().s[79]++;
      console.error('Failed to get global metrics:', error);
      cov_1iyq7u5472().s[80]++;
      return null;
    }
  }), [state.isInitialized, config]));
  var updateConfig = (cov_1iyq7u5472().s[81]++, useCallback(function (newConfig) {
    cov_1iyq7u5472().f[10]++;
    cov_1iyq7u5472().s[82]++;
    setConfig(function (prev) {
      cov_1iyq7u5472().f[11]++;
      cov_1iyq7u5472().s[83]++;
      return Object.assign({}, prev, newConfig);
    });
  }, []));
  var detectUserLocation = (cov_1iyq7u5472().s[84]++, useCallback(_asyncToGenerator(function* () {
    cov_1iyq7u5472().f[12]++;
    cov_1iyq7u5472().s[85]++;
    try {
      var mockLocation = (cov_1iyq7u5472().s[86]++, {
        country: 'US',
        region: 'us-east-1',
        city: 'New York',
        timezone: 'America/New_York',
        coordinates: {
          lat: 40.7128,
          lng: -74.0060
        }
      });
      cov_1iyq7u5472().s[87]++;
      setState(function (prev) {
        cov_1iyq7u5472().f[13]++;
        cov_1iyq7u5472().s[88]++;
        return Object.assign({}, prev, {
          userLocation: mockLocation
        });
      });
      cov_1iyq7u5472().s[89]++;
      console.log('Detected user location:', mockLocation);
    } catch (error) {
      cov_1iyq7u5472().s[90]++;
      console.error('Failed to detect user location:', error);
    }
  }), []));
  var updateEdgeState = (cov_1iyq7u5472().s[91]++, useCallback(_asyncToGenerator(function* () {
    cov_1iyq7u5472().f[14]++;
    cov_1iyq7u5472().s[92]++;
    try {
      var newState = (cov_1iyq7u5472().s[93]++, {});
      cov_1iyq7u5472().s[94]++;
      if (config.enableGlobalCDN) {
        cov_1iyq7u5472().b[26][0]++;
        var cdnMetrics = (cov_1iyq7u5472().s[95]++, yield globalCDNManager.getGlobalPerformanceMetrics());
        cov_1iyq7u5472().s[96]++;
        newState.globalPerformance = Object.assign({}, state.globalPerformance, {
          averageLatency: cdnMetrics.averageLatency,
          globalUptime: cdnMetrics.globalUptime,
          cdnHitRate: 85
        });
      } else {
        cov_1iyq7u5472().b[26][1]++;
      }
      cov_1iyq7u5472().s[97]++;
      if (config.enableEdgeFunctions) {
        cov_1iyq7u5472().b[27][0]++;
        var deployedFunctions = (cov_1iyq7u5472().s[98]++, edgeFunctionManager.getDeployedFunctions());
        cov_1iyq7u5472().s[99]++;
        newState.edgeMetrics = {
          functionsDeployed: deployedFunctions.length,
          executionsPerSecond: 50,
          averageExecutionTime: 25,
          successRate: 98.5
        };
      } else {
        cov_1iyq7u5472().b[27][1]++;
      }
      cov_1iyq7u5472().s[100]++;
      if (config.enableSmartLoadBalancing) {
        cov_1iyq7u5472().b[28][0]++;
        var lbMetrics = (cov_1iyq7u5472().s[101]++, smartLoadBalancer.getLoadBalancingMetrics());
        cov_1iyq7u5472().s[102]++;
        newState.loadBalancing = {
          activeEndpoints: lbMetrics.healthyEndpoints,
          trafficDistribution: lbMetrics.regionalDistribution,
          failoverEvents: 2
        };
      } else {
        cov_1iyq7u5472().b[28][1]++;
      }
      cov_1iyq7u5472().s[103]++;
      if (config.enableGeoOptimization) {
        cov_1iyq7u5472().b[29][0]++;
        var geoMetrics = (cov_1iyq7u5472().s[104]++, geoOptimizedContentManager.getGeoOptimizationMetrics());
        cov_1iyq7u5472().s[105]++;
        newState.contentOptimization = {
          bandwidthSavings: geoMetrics.bandwidthSavings,
          loadTimeImprovement: 45,
          regionalAdaptation: geoMetrics.regionalCoverage > 80
        };
      } else {
        cov_1iyq7u5472().b[29][1]++;
      }
      cov_1iyq7u5472().s[106]++;
      setState(function (prev) {
        cov_1iyq7u5472().f[15]++;
        cov_1iyq7u5472().s[107]++;
        return Object.assign({}, prev, newState);
      });
    } catch (error) {
      cov_1iyq7u5472().s[108]++;
      console.error('Failed to update edge state:', error);
    }
  }), [config, state.globalPerformance]));
  cov_1iyq7u5472().s[109]++;
  useEffect(function () {
    cov_1iyq7u5472().f[16]++;
    cov_1iyq7u5472().s[110]++;
    if ((cov_1iyq7u5472().b[31][0]++, !config.autoOptimization) || (cov_1iyq7u5472().b[31][1]++, !state.isInitialized)) {
      cov_1iyq7u5472().b[30][0]++;
      cov_1iyq7u5472().s[111]++;
      return;
    } else {
      cov_1iyq7u5472().b[30][1]++;
    }
    var interval = (cov_1iyq7u5472().s[112]++, setInterval(_asyncToGenerator(function* () {
      cov_1iyq7u5472().f[17]++;
      cov_1iyq7u5472().s[113]++;
      if (config.enableGlobalCDN) {
        cov_1iyq7u5472().b[32][0]++;
        cov_1iyq7u5472().s[114]++;
        yield globalCDNManager.optimizeGlobalRouting();
      } else {
        cov_1iyq7u5472().b[32][1]++;
      }
    }), config.optimizationInterval));
    cov_1iyq7u5472().s[115]++;
    return function () {
      cov_1iyq7u5472().f[18]++;
      cov_1iyq7u5472().s[116]++;
      return clearInterval(interval);
    };
  }, [config.autoOptimization, config.optimizationInterval, state.isInitialized]);
  cov_1iyq7u5472().s[117]++;
  useEffect(function () {
    cov_1iyq7u5472().f[19]++;
    cov_1iyq7u5472().s[118]++;
    if (!state.isInitialized) {
      cov_1iyq7u5472().b[33][0]++;
      cov_1iyq7u5472().s[119]++;
      return;
    } else {
      cov_1iyq7u5472().b[33][1]++;
    }
    var interval = (cov_1iyq7u5472().s[120]++, setInterval(function () {
      cov_1iyq7u5472().f[20]++;
      cov_1iyq7u5472().s[121]++;
      updateEdgeState();
    }, 30000));
    cov_1iyq7u5472().s[122]++;
    return function () {
      cov_1iyq7u5472().f[21]++;
      cov_1iyq7u5472().s[123]++;
      return clearInterval(interval);
    };
  }, [state.isInitialized, updateEdgeState]);
  cov_1iyq7u5472().s[124]++;
  useEffect(function () {
    cov_1iyq7u5472().f[22]++;
    cov_1iyq7u5472().s[125]++;
    if (!state.isInitialized) {
      cov_1iyq7u5472().b[34][0]++;
      cov_1iyq7u5472().s[126]++;
      initialize();
    } else {
      cov_1iyq7u5472().b[34][1]++;
    }
  }, [state.isInitialized, initialize]);
  cov_1iyq7u5472().s[127]++;
  return useMemo(function () {
    cov_1iyq7u5472().f[23]++;
    cov_1iyq7u5472().s[128]++;
    return {
      state: state,
      actions: {
        initialize: initialize,
        optimizeContent: optimizeContent,
        executeEdgeFunction: executeEdgeFunction,
        routeRequest: routeRequest,
        preloadGlobally: preloadGlobally,
        getGlobalMetrics: getGlobalMetrics,
        updateConfig: updateConfig,
        detectUserLocation: detectUserLocation
      },
      config: config
    };
  }, [state, initialize, optimizeContent, executeEdgeFunction, routeRequest, preloadGlobally, getGlobalMetrics, updateConfig, detectUserLocation, config]);
}
export default useEdgeOptimization;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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