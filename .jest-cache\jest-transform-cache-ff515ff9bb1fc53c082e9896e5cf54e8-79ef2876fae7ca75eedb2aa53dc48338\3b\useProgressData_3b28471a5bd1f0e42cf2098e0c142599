00f6b820d94b0acd7409b15a653def95
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2hzhz2h3qp() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useProgressData.ts";
  var hash = "385119e98ccd5998416cf5fd8682794bc44e51d6";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useProgressData.ts",
    statementMap: {
      "0": {
        start: {
          line: 74,
          column: 26
        },
        end: {
          line: 74,
          column: 61
        }
      },
      "1": {
        start: {
          line: 75,
          column: 32
        },
        end: {
          line: 75,
          column: 46
        }
      },
      "2": {
        start: {
          line: 76,
          column: 28
        },
        end: {
          line: 76,
          column: 57
        }
      },
      "3": {
        start: {
          line: 77,
          column: 38
        },
        end: {
          line: 77,
          column: 53
        }
      },
      "4": {
        start: {
          line: 80,
          column: 41
        },
        end: {
          line: 200,
          column: 3
        }
      },
      "5": {
        start: {
          line: 202,
          column: 27
        },
        end: {
          line: 267,
          column: 8
        }
      },
      "6": {
        start: {
          line: 203,
          column: 4
        },
        end: {
          line: 266,
          column: 5
        }
      },
      "7": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 208,
          column: 7
        }
      },
      "8": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 205,
          column: 28
        }
      },
      "9": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 207,
          column: 25
        }
      },
      "10": {
        start: {
          line: 209,
          column: 6
        },
        end: {
          line: 209,
          column: 21
        }
      },
      "11": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 216,
          column: 7
        }
      },
      "12": {
        start: {
          line: 213,
          column: 8
        },
        end: {
          line: 213,
          column: 63
        }
      },
      "13": {
        start: {
          line: 214,
          column: 8
        },
        end: {
          line: 214,
          column: 34
        }
      },
      "14": {
        start: {
          line: 215,
          column: 8
        },
        end: {
          line: 215,
          column: 15
        }
      },
      "15": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 257,
          column: 7
        }
      },
      "16": {
        start: {
          line: 220,
          column: 55
        },
        end: {
          line: 220,
          column: 98
        }
      },
      "17": {
        start: {
          line: 222,
          column: 8
        },
        end: {
          line: 226,
          column: 9
        }
      },
      "18": {
        start: {
          line: 223,
          column: 10
        },
        end: {
          line: 223,
          column: 90
        }
      },
      "19": {
        start: {
          line: 224,
          column: 10
        },
        end: {
          line: 224,
          column: 36
        }
      },
      "20": {
        start: {
          line: 225,
          column: 10
        },
        end: {
          line: 225,
          column: 17
        }
      },
      "21": {
        start: {
          line: 229,
          column: 46
        },
        end: {
          line: 250,
          column: 9
        }
      },
      "22": {
        start: {
          line: 252,
          column: 8
        },
        end: {
          line: 252,
          column: 67
        }
      },
      "23": {
        start: {
          line: 253,
          column: 8
        },
        end: {
          line: 253,
          column: 33
        }
      },
      "24": {
        start: {
          line: 255,
          column: 8
        },
        end: {
          line: 255,
          column: 76
        }
      },
      "25": {
        start: {
          line: 256,
          column: 8
        },
        end: {
          line: 256,
          column: 34
        }
      },
      "26": {
        start: {
          line: 259,
          column: 6
        },
        end: {
          line: 259,
          column: 84
        }
      },
      "27": {
        start: {
          line: 260,
          column: 6
        },
        end: {
          line: 260,
          column: 57
        }
      },
      "28": {
        start: {
          line: 262,
          column: 6
        },
        end: {
          line: 262,
          column: 32
        }
      },
      "29": {
        start: {
          line: 264,
          column: 6
        },
        end: {
          line: 264,
          column: 24
        }
      },
      "30": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 27
        }
      },
      "31": {
        start: {
          line: 269,
          column: 22
        },
        end: {
          line: 271,
          column: 24
        }
      },
      "32": {
        start: {
          line: 270,
          column: 4
        },
        end: {
          line: 270,
          column: 33
        }
      },
      "33": {
        start: {
          line: 273,
          column: 2
        },
        end: {
          line: 275,
          column: 25
        }
      },
      "34": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 274,
          column: 23
        }
      },
      "35": {
        start: {
          line: 277,
          column: 2
        },
        end: {
          line: 283,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useProgressData",
        decl: {
          start: {
            line: 73,
            column: 16
          },
          end: {
            line: 73,
            column: 31
          }
        },
        loc: {
          start: {
            line: 73,
            column: 57
          },
          end: {
            line: 284,
            column: 1
          }
        },
        line: 73
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 202,
            column: 39
          },
          end: {
            line: 202,
            column: 40
          }
        },
        loc: {
          start: {
            line: 202,
            column: 68
          },
          end: {
            line: 267,
            column: 3
          }
        },
        line: 202
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 269,
            column: 34
          },
          end: {
            line: 269,
            column: 35
          }
        },
        loc: {
          start: {
            line: 269,
            column: 46
          },
          end: {
            line: 271,
            column: 3
          }
        },
        line: 269
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 273,
            column: 12
          },
          end: {
            line: 273,
            column: 13
          }
        },
        loc: {
          start: {
            line: 273,
            column: 18
          },
          end: {
            line: 275,
            column: 3
          }
        },
        line: 273
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 202,
            column: 46
          },
          end: {
            line: 202,
            column: 63
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 202,
            column: 58
          },
          end: {
            line: 202,
            column: 63
          }
        }],
        line: 202
      },
      "1": {
        loc: {
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 208,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 208,
            column: 7
          }
        }, {
          start: {
            line: 206,
            column: 13
          },
          end: {
            line: 208,
            column: 7
          }
        }],
        line: 204
      },
      "2": {
        loc: {
          start: {
            line: 212,
            column: 6
          },
          end: {
            line: 216,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 212,
            column: 6
          },
          end: {
            line: 216,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 212
      },
      "3": {
        loc: {
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 226,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 222,
            column: 8
          },
          end: {
            line: 226,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 222
      },
      "4": {
        loc: {
          start: {
            line: 231,
            column: 27
          },
          end: {
            line: 231,
            column: 63
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 231,
            column: 27
          },
          end: {
            line: 231,
            column: 58
          }
        }, {
          start: {
            line: 231,
            column: 62
          },
          end: {
            line: 231,
            column: 63
          }
        }],
        line: 231
      },
      "5": {
        loc: {
          start: {
            line: 232,
            column: 36
          },
          end: {
            line: 232,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 232,
            column: 36
          },
          end: {
            line: 232,
            column: 64
          }
        }, {
          start: {
            line: 232,
            column: 68
          },
          end: {
            line: 232,
            column: 69
          }
        }],
        line: 232
      },
      "6": {
        loc: {
          start: {
            line: 259,
            column: 15
          },
          end: {
            line: 259,
            column: 82
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 259,
            column: 38
          },
          end: {
            line: 259,
            column: 49
          }
        }, {
          start: {
            line: 259,
            column: 52
          },
          end: {
            line: 259,
            column: 82
          }
        }],
        line: 259
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "385119e98ccd5998416cf5fd8682794bc44e51d6"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2hzhz2h3qp = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2hzhz2h3qp();
import { useState, useEffect, useCallback } from 'react';
import { databaseService } from "../services/database/DatabaseService";
import { authService } from "../services/auth/AuthService";
export function useProgressData() {
  cov_2hzhz2h3qp().f[0]++;
  var _ref = (cov_2hzhz2h3qp().s[0]++, useState(null)),
    _ref2 = _slicedToArray(_ref, 2),
    data = _ref2[0],
    setData = _ref2[1];
  var _ref3 = (cov_2hzhz2h3qp().s[1]++, useState(true)),
    _ref4 = _slicedToArray(_ref3, 2),
    loading = _ref4[0],
    setLoading = _ref4[1];
  var _ref5 = (cov_2hzhz2h3qp().s[2]++, useState(null)),
    _ref6 = _slicedToArray(_ref5, 2),
    error = _ref6[0],
    setError = _ref6[1];
  var _ref7 = (cov_2hzhz2h3qp().s[3]++, useState(false)),
    _ref8 = _slicedToArray(_ref7, 2),
    refreshing = _ref8[0],
    setRefreshing = _ref8[1];
  var mockProgressData = (cov_2hzhz2h3qp().s[4]++, {
    overallProgress: {
      totalSessions: 127,
      hoursPlayed: 89,
      skillImprovement: 12,
      currentStreak: 7,
      weeklyImprovement: 8
    },
    skillProgress: {
      forehand: {
        current: 78,
        change: 5,
        trend: 'up'
      },
      backhand: {
        current: 65,
        change: 8,
        trend: 'up'
      },
      serve: {
        current: 82,
        change: 2,
        trend: 'up'
      },
      volley: {
        current: 70,
        change: 3,
        trend: 'up'
      },
      footwork: {
        current: 75,
        change: 6,
        trend: 'up'
      },
      strategy: {
        current: 68,
        change: 4,
        trend: 'up'
      }
    },
    weeklyStats: [{
      day: 'Mon',
      sessions: 1,
      duration: 45
    }, {
      day: 'Tue',
      sessions: 0,
      duration: 0
    }, {
      day: 'Wed',
      sessions: 2,
      duration: 90
    }, {
      day: 'Thu',
      sessions: 1,
      duration: 60
    }, {
      day: 'Fri',
      sessions: 0,
      duration: 0
    }, {
      day: 'Sat',
      sessions: 3,
      duration: 120
    }, {
      day: 'Sun',
      sessions: 1,
      duration: 30
    }],
    monthlyGoals: [{
      goal: 'Complete 20 training sessions',
      progress: 14,
      total: 20,
      icon: 'calendar'
    }, {
      goal: 'Improve serve accuracy to 85%',
      progress: 82,
      total: 85,
      icon: 'target'
    }, {
      goal: 'Upload 15 practice videos',
      progress: 7,
      total: 15,
      icon: 'bar-chart'
    }],
    performanceTrends: [{
      metric: 'First Serve Percentage',
      change: 12,
      description: 'Significant improvement in serve consistency over the past month'
    }, {
      metric: 'Unforced Errors',
      change: -8,
      description: 'Reduced unforced errors through better shot selection'
    }, {
      metric: 'Net Approaches',
      change: 15,
      description: 'More aggressive net play leading to higher point conversion'
    }, {
      metric: 'Break Point Conversion',
      change: 6,
      description: 'Better performance under pressure situations'
    }],
    achievements: [{
      id: 'achievement-1',
      title: 'Serve Master',
      description: 'Achieved 80%+ serve accuracy',
      color: '#ffe600',
      unlocked: true,
      unlocked_at: '2024-12-18T20:00:00Z'
    }, {
      id: 'achievement-2',
      title: 'Consistency King',
      description: 'Complete 7 days of training',
      color: '#23ba16',
      unlocked: true,
      unlocked_at: '2024-12-15T09:00:00Z'
    }, {
      id: 'achievement-3',
      title: 'Video Analyst',
      description: 'Upload 10 training videos',
      color: '#23ba16',
      unlocked: false,
      unlocked_at: '',
      progress: 7,
      total: 10
    }, {
      id: 'achievement-4',
      title: 'Rally Master',
      description: 'Win 20+ shot rallies',
      color: '#3b82f6',
      unlocked: true,
      unlocked_at: '2024-12-10T14:30:00Z'
    }],
    aiInsights: [{
      title: 'Serve Improvement Trend',
      description: 'Your serve accuracy has improved by 12% this month, particularly on second serves.',
      recommendation: 'Continue practicing serve placement drills to maintain this momentum.'
    }, {
      title: 'Backhand Development',
      description: 'Significant progress in backhand technique with 8% improvement in consistency.',
      recommendation: 'Focus on adding more topspin to your backhand for better court positioning.'
    }, {
      title: 'Match Performance',
      description: 'Your break point conversion rate has improved, showing better mental toughness.',
      recommendation: 'Practice high-pressure scenarios to further enhance clutch performance.'
    }]
  });
  var loadProgressData = (cov_2hzhz2h3qp().s[5]++, useCallback(_asyncToGenerator(function* () {
    var isRefresh = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2hzhz2h3qp().b[0][0]++, false);
    cov_2hzhz2h3qp().f[1]++;
    cov_2hzhz2h3qp().s[6]++;
    try {
      cov_2hzhz2h3qp().s[7]++;
      if (isRefresh) {
        cov_2hzhz2h3qp().b[1][0]++;
        cov_2hzhz2h3qp().s[8]++;
        setRefreshing(true);
      } else {
        cov_2hzhz2h3qp().b[1][1]++;
        cov_2hzhz2h3qp().s[9]++;
        setLoading(true);
      }
      cov_2hzhz2h3qp().s[10]++;
      setError(null);
      cov_2hzhz2h3qp().s[11]++;
      if (!authService.isAuthenticated()) {
        cov_2hzhz2h3qp().b[2][0]++;
        cov_2hzhz2h3qp().s[12]++;
        console.log('User not authenticated, using mock data');
        cov_2hzhz2h3qp().s[13]++;
        setData(mockProgressData);
        cov_2hzhz2h3qp().s[14]++;
        return;
      } else {
        cov_2hzhz2h3qp().b[2][1]++;
      }
      cov_2hzhz2h3qp().s[15]++;
      try {
        var _ref0 = (cov_2hzhz2h3qp().s[16]++, yield databaseService.getUserStatsSummary()),
          statsData = _ref0.data,
          statsError = _ref0.error;
        cov_2hzhz2h3qp().s[17]++;
        if (statsError) {
          cov_2hzhz2h3qp().b[3][0]++;
          cov_2hzhz2h3qp().s[18]++;
          console.warn('Failed to load real progress data, using mock data:', statsError);
          cov_2hzhz2h3qp().s[19]++;
          setData(mockProgressData);
          cov_2hzhz2h3qp().s[20]++;
          return;
        } else {
          cov_2hzhz2h3qp().b[3][1]++;
        }
        var transformedData = (cov_2hzhz2h3qp().s[21]++, {
          overallProgress: {
            totalSessions: (cov_2hzhz2h3qp().b[4][0]++, statsData.totalTrainingSessions) || (cov_2hzhz2h3qp().b[4][1]++, 0),
            hoursPlayed: Math.round((cov_2hzhz2h3qp().b[5][0]++, statsData.totalTrainingHours) || (cov_2hzhz2h3qp().b[5][1]++, 0)),
            skillImprovement: 12,
            currentStreak: 7,
            weeklyImprovement: 8
          },
          skillProgress: {
            forehand: {
              current: 78,
              change: 5,
              trend: 'up'
            },
            backhand: {
              current: 65,
              change: 8,
              trend: 'up'
            },
            serve: {
              current: 82,
              change: 2,
              trend: 'up'
            },
            volley: {
              current: 70,
              change: 3,
              trend: 'up'
            },
            footwork: {
              current: 75,
              change: 6,
              trend: 'up'
            },
            strategy: {
              current: 68,
              change: 4,
              trend: 'up'
            }
          },
          weeklyStats: mockProgressData.weeklyStats,
          monthlyGoals: mockProgressData.monthlyGoals,
          performanceTrends: mockProgressData.performanceTrends,
          achievements: mockProgressData.achievements,
          aiInsights: mockProgressData.aiInsights
        });
        cov_2hzhz2h3qp().s[22]++;
        console.log('Loaded real progress data:', transformedData);
        cov_2hzhz2h3qp().s[23]++;
        setData(transformedData);
      } catch (dbError) {
        cov_2hzhz2h3qp().s[24]++;
        console.warn('Database error, falling back to mock data:', dbError);
        cov_2hzhz2h3qp().s[25]++;
        setData(mockProgressData);
      }
    } catch (err) {
      cov_2hzhz2h3qp().s[26]++;
      setError(err instanceof Error ? (cov_2hzhz2h3qp().b[6][0]++, err.message) : (cov_2hzhz2h3qp().b[6][1]++, 'Failed to load progress data'));
      cov_2hzhz2h3qp().s[27]++;
      console.error('Progress data loading error:', err);
      cov_2hzhz2h3qp().s[28]++;
      setData(mockProgressData);
    } finally {
      cov_2hzhz2h3qp().s[29]++;
      setLoading(false);
      cov_2hzhz2h3qp().s[30]++;
      setRefreshing(false);
    }
  }), []));
  var refreshData = (cov_2hzhz2h3qp().s[31]++, useCallback(_asyncToGenerator(function* () {
    cov_2hzhz2h3qp().f[2]++;
    cov_2hzhz2h3qp().s[32]++;
    yield loadProgressData(true);
  }), [loadProgressData]));
  cov_2hzhz2h3qp().s[33]++;
  useEffect(function () {
    cov_2hzhz2h3qp().f[3]++;
    cov_2hzhz2h3qp().s[34]++;
    loadProgressData();
  }, [loadProgressData]);
  cov_2hzhz2h3qp().s[35]++;
  return {
    data: data,
    loading: loading,
    error: error,
    refreshing: refreshing,
    refreshData: refreshData
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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