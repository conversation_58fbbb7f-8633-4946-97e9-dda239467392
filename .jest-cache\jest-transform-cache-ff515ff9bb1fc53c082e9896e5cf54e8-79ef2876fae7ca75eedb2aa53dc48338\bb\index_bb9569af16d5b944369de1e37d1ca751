7c4402ff22c290ae90153073ac82cb6a
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _react = _interopRequireDefault(require("react"));
var _UnimplementedView = _interopRequireDefault(require("../../modules/UnimplementedView"));
function YellowBox(props) {
  return _react.default.createElement(_UnimplementedView.default, props);
}
YellowBox.ignoreWarnings = function () {};
var _default = exports.default = YellowBox;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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