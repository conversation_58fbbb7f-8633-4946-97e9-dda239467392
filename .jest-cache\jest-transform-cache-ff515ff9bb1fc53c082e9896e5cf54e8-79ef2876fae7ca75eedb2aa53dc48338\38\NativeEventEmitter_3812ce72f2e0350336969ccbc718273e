6e2d04b6ad92193ee865bc5f1a14a071
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _Platform = _interopRequireDefault(require("../../../exports/Platform"));
var _RCTDeviceEventEmitter = _interopRequireDefault(require("./RCTDeviceEventEmitter"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var NativeEventEmitter = function () {
  function NativeEventEmitter(nativeModule) {
    (0, _classCallCheck2.default)(this, NativeEventEmitter);
    if (_Platform.default.OS === 'ios') {
      (0, _invariant.default)(nativeModule != null, '`new NativeEventEmitter()` requires a non-null argument.');
      this._nativeModule = nativeModule;
    }
  }
  return (0, _createClass2.default)(NativeEventEmitter, [{
    key: "addListener",
    value: function addListener(eventType, listener, context) {
      var _this = this;
      var _this$_nativeModule;
      (_this$_nativeModule = this._nativeModule) == null ? void 0 : _this$_nativeModule.addListener(eventType);
      var subscription = _RCTDeviceEventEmitter.default.addListener(eventType, listener, context);
      return {
        remove: function remove() {
          if (subscription != null) {
            var _this$_nativeModule2;
            (_this$_nativeModule2 = _this._nativeModule) == null ? void 0 : _this$_nativeModule2.removeListeners(1);
            subscription.remove();
            subscription = null;
          }
        }
      };
    }
  }, {
    key: "removeListener",
    value: function removeListener(eventType, listener) {
      var _this$_nativeModule3;
      (_this$_nativeModule3 = this._nativeModule) == null ? void 0 : _this$_nativeModule3.removeListeners(1);
      _RCTDeviceEventEmitter.default.removeListener(eventType, listener);
    }
  }, {
    key: "emit",
    value: function emit(eventType) {
      var _RCTDeviceEventEmitte;
      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
        args[_key - 1] = arguments[_key];
      }
      (_RCTDeviceEventEmitte = _RCTDeviceEventEmitter.default).emit.apply(_RCTDeviceEventEmitte, [eventType].concat(args));
    }
  }, {
    key: "removeAllListeners",
    value: function removeAllListeners(eventType) {
      var _this$_nativeModule4;
      (0, _invariant.default)(eventType != null, '`NativeEventEmitter.removeAllListener()` requires a non-null argument.');
      (_this$_nativeModule4 = this._nativeModule) == null ? void 0 : _this$_nativeModule4.removeListeners(this.listenerCount(eventType));
      _RCTDeviceEventEmitter.default.removeAllListeners(eventType);
    }
  }, {
    key: "listenerCount",
    value: function listenerCount(eventType) {
      return _RCTDeviceEventEmitter.default.listenerCount(eventType);
    }
  }]);
}();
exports.default = NativeEventEmitter;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0MiIsInJlcXVpcmUiLCJfY2xhc3NDYWxsQ2hlY2syIiwiX2NyZWF0ZUNsYXNzMiIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJkZWZhdWx0IiwiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJfUGxhdGZvcm0iLCJfUkNURGV2aWNlRXZlbnRFbWl0dGVyIiwiX2ludmFyaWFudCIsIk5hdGl2ZUV2ZW50RW1pdHRlciIsIm5hdGl2ZU1vZHVsZSIsIk9TIiwiX25hdGl2ZU1vZHVsZSIsImtleSIsInZhbHVlIiwiYWRkTGlzdGVuZXIiLCJldmVudFR5cGUiLCJsaXN0ZW5lciIsImNvbnRleHQiLCJfdGhpcyIsIl90aGlzJF9uYXRpdmVNb2R1bGUiLCJzdWJzY3JpcHRpb24iLCJyZW1vdmUiLCJfdGhpcyRfbmF0aXZlTW9kdWxlMiIsInJlbW92ZUxpc3RlbmVycyIsInJlbW92ZUxpc3RlbmVyIiwiX3RoaXMkX25hdGl2ZU1vZHVsZTMiLCJlbWl0IiwiX1JDVERldmljZUV2ZW50RW1pdHRlIiwiX2xlbiIsImFyZ3VtZW50cyIsImxlbmd0aCIsImFyZ3MiLCJBcnJheSIsIl9rZXkiLCJhcHBseSIsImNvbmNhdCIsInJlbW92ZUFsbExpc3RlbmVycyIsIl90aGlzJF9uYXRpdmVNb2R1bGU0IiwibGlzdGVuZXJDb3VudCIsIm1vZHVsZSJdLCJzb3VyY2VzIjpbIk5hdGl2ZUV2ZW50RW1pdHRlci5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKiBAZm9ybWF0XG4gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG52YXIgX2ludGVyb3BSZXF1aXJlRGVmYXVsdCA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlRGVmYXVsdFwiKS5kZWZhdWx0O1xuZXhwb3J0cy5fX2VzTW9kdWxlID0gdHJ1ZTtcbmV4cG9ydHMuZGVmYXVsdCA9IHZvaWQgMDtcbnZhciBfUGxhdGZvcm0gPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi8uLi8uLi9leHBvcnRzL1BsYXRmb3JtXCIpKTtcbnZhciBfUkNURGV2aWNlRXZlbnRFbWl0dGVyID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi9SQ1REZXZpY2VFdmVudEVtaXR0ZXJcIikpO1xudmFyIF9pbnZhcmlhbnQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCJmYmpzL2xpYi9pbnZhcmlhbnRcIikpO1xuLyoqXG4gKiBgTmF0aXZlRXZlbnRFbWl0dGVyYCBpcyBpbnRlbmRlZCBmb3IgdXNlIGJ5IE5hdGl2ZSBNb2R1bGVzIHRvIGVtaXQgZXZlbnRzIHRvXG4gKiBKYXZhU2NyaXB0IGxpc3RlbmVycy4gSWYgYSBgTmF0aXZlTW9kdWxlYCBpcyBzdXBwbGllZCB0byB0aGUgY29uc3RydWN0b3IsIGl0XG4gKiB3aWxsIGJlIG5vdGlmaWVkICh2aWEgYGFkZExpc3RlbmVyYCBhbmQgYHJlbW92ZUxpc3RlbmVyc2ApIHdoZW4gdGhlIGxpc3RlbmVyXG4gKiBjb3VudCBjaGFuZ2VzIHRvIG1hbmFnZSBcIm5hdGl2ZSBtZW1vcnlcIi5cbiAqXG4gKiBDdXJyZW50bHksIGFsbCBuYXRpdmUgZXZlbnRzIGFyZSBmaXJlZCB2aWEgYSBnbG9iYWwgYFJDVERldmljZUV2ZW50RW1pdHRlcmAuXG4gKiBUaGlzIG1lYW5zIGV2ZW50IG5hbWVzIG11c3QgYmUgZ2xvYmFsbHkgdW5pcXVlLCBhbmQgaXQgbWVhbnMgdGhhdCBjYWxsIHNpdGVzXG4gKiBjYW4gdGhlb3JldGljYWxseSBsaXN0ZW4gdG8gYFJDVERldmljZUV2ZW50RW1pdHRlcmAgKGFsdGhvdWdoIGRpc2NvdXJhZ2VkKS5cbiAqL1xuY2xhc3MgTmF0aXZlRXZlbnRFbWl0dGVyIHtcbiAgY29uc3RydWN0b3IobmF0aXZlTW9kdWxlKSB7XG4gICAgaWYgKF9QbGF0Zm9ybS5kZWZhdWx0Lk9TID09PSAnaW9zJykge1xuICAgICAgKDAsIF9pbnZhcmlhbnQuZGVmYXVsdCkobmF0aXZlTW9kdWxlICE9IG51bGwsICdgbmV3IE5hdGl2ZUV2ZW50RW1pdHRlcigpYCByZXF1aXJlcyBhIG5vbi1udWxsIGFyZ3VtZW50LicpO1xuICAgICAgdGhpcy5fbmF0aXZlTW9kdWxlID0gbmF0aXZlTW9kdWxlO1xuICAgIH1cbiAgfVxuICBhZGRMaXN0ZW5lcihldmVudFR5cGUsIGxpc3RlbmVyLCBjb250ZXh0KSB7XG4gICAgdmFyIF90aGlzJF9uYXRpdmVNb2R1bGU7XG4gICAgKF90aGlzJF9uYXRpdmVNb2R1bGUgPSB0aGlzLl9uYXRpdmVNb2R1bGUpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRfbmF0aXZlTW9kdWxlLmFkZExpc3RlbmVyKGV2ZW50VHlwZSk7XG4gICAgdmFyIHN1YnNjcmlwdGlvbiA9IF9SQ1REZXZpY2VFdmVudEVtaXR0ZXIuZGVmYXVsdC5hZGRMaXN0ZW5lcihldmVudFR5cGUsIGxpc3RlbmVyLCBjb250ZXh0KTtcbiAgICByZXR1cm4ge1xuICAgICAgcmVtb3ZlOiAoKSA9PiB7XG4gICAgICAgIGlmIChzdWJzY3JpcHRpb24gIT0gbnVsbCkge1xuICAgICAgICAgIHZhciBfdGhpcyRfbmF0aXZlTW9kdWxlMjtcbiAgICAgICAgICAoX3RoaXMkX25hdGl2ZU1vZHVsZTIgPSB0aGlzLl9uYXRpdmVNb2R1bGUpID09IG51bGwgPyB2b2lkIDAgOiBfdGhpcyRfbmF0aXZlTW9kdWxlMi5yZW1vdmVMaXN0ZW5lcnMoMSk7XG4gICAgICAgICAgLy8gJEZsb3dGaXhNZVtpbmNvbXBhdGlibGUtdXNlXVxuICAgICAgICAgIHN1YnNjcmlwdGlvbi5yZW1vdmUoKTtcbiAgICAgICAgICBzdWJzY3JpcHRpb24gPSBudWxsO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfTtcbiAgfVxuXG4gIC8qKlxuICAgKiBAZGVwcmVjYXRlZCBVc2UgYHJlbW92ZWAgb24gdGhlIEV2ZW50U3Vic2NyaXB0aW9uIGZyb20gYGFkZExpc3RlbmVyYC5cbiAgICovXG4gIHJlbW92ZUxpc3RlbmVyKGV2ZW50VHlwZSwgbGlzdGVuZXIpIHtcbiAgICB2YXIgX3RoaXMkX25hdGl2ZU1vZHVsZTM7XG4gICAgKF90aGlzJF9uYXRpdmVNb2R1bGUzID0gdGhpcy5fbmF0aXZlTW9kdWxlKSA9PSBudWxsID8gdm9pZCAwIDogX3RoaXMkX25hdGl2ZU1vZHVsZTMucmVtb3ZlTGlzdGVuZXJzKDEpO1xuICAgIC8vIE5PVEU6IFRoaXMgd2lsbCByZXBvcnQgYSBkZXByZWNhdGlvbiBub3RpY2UgdmlhIGBjb25zb2xlLmVycm9yYC5cbiAgICAvLyAkRmxvd0ZpeE1lW3Byb3AtbWlzc2luZ10gLSBgcmVtb3ZlTGlzdGVuZXJgIGV4aXN0cyBidXQgaXMgZGVwcmVjYXRlZC5cbiAgICBfUkNURGV2aWNlRXZlbnRFbWl0dGVyLmRlZmF1bHQucmVtb3ZlTGlzdGVuZXIoZXZlbnRUeXBlLCBsaXN0ZW5lcik7XG4gIH1cbiAgZW1pdChldmVudFR5cGUpIHtcbiAgICBmb3IgKHZhciBfbGVuID0gYXJndW1lbnRzLmxlbmd0aCwgYXJncyA9IG5ldyBBcnJheShfbGVuID4gMSA/IF9sZW4gLSAxIDogMCksIF9rZXkgPSAxOyBfa2V5IDwgX2xlbjsgX2tleSsrKSB7XG4gICAgICBhcmdzW19rZXkgLSAxXSA9IGFyZ3VtZW50c1tfa2V5XTtcbiAgICB9XG4gICAgLy8gR2VuZXJhbGx5LCBgUkNURGV2aWNlRXZlbnRFbWl0dGVyYCBpcyBkaXJlY3RseSBpbnZva2VkLiBCdXQgdGhpcyBpc1xuICAgIC8vIGluY2x1ZGVkIGZvciBjb21wbGV0ZW5lc3MuXG4gICAgX1JDVERldmljZUV2ZW50RW1pdHRlci5kZWZhdWx0LmVtaXQoZXZlbnRUeXBlLCAuLi5hcmdzKTtcbiAgfVxuICByZW1vdmVBbGxMaXN0ZW5lcnMoZXZlbnRUeXBlKSB7XG4gICAgdmFyIF90aGlzJF9uYXRpdmVNb2R1bGU0O1xuICAgICgwLCBfaW52YXJpYW50LmRlZmF1bHQpKGV2ZW50VHlwZSAhPSBudWxsLCAnYE5hdGl2ZUV2ZW50RW1pdHRlci5yZW1vdmVBbGxMaXN0ZW5lcigpYCByZXF1aXJlcyBhIG5vbi1udWxsIGFyZ3VtZW50LicpO1xuICAgIChfdGhpcyRfbmF0aXZlTW9kdWxlNCA9IHRoaXMuX25hdGl2ZU1vZHVsZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF90aGlzJF9uYXRpdmVNb2R1bGU0LnJlbW92ZUxpc3RlbmVycyh0aGlzLmxpc3RlbmVyQ291bnQoZXZlbnRUeXBlKSk7XG4gICAgX1JDVERldmljZUV2ZW50RW1pdHRlci5kZWZhdWx0LnJlbW92ZUFsbExpc3RlbmVycyhldmVudFR5cGUpO1xuICB9XG4gIGxpc3RlbmVyQ291bnQoZXZlbnRUeXBlKSB7XG4gICAgcmV0dXJuIF9SQ1REZXZpY2VFdmVudEVtaXR0ZXIuZGVmYXVsdC5saXN0ZW5lckNvdW50KGV2ZW50VHlwZSk7XG4gIH1cbn1cbmV4cG9ydHMuZGVmYXVsdCA9IE5hdGl2ZUV2ZW50RW1pdHRlcjtcbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBVUEsWUFBWTs7QUFBQyxJQUFBQSx1QkFBQSxHQUFBQyxPQUFBO0FBQUEsSUFBQUMsZ0JBQUEsR0FBQUYsdUJBQUEsQ0FBQUMsT0FBQTtBQUFBLElBQUFFLGFBQUEsR0FBQUgsdUJBQUEsQ0FBQUMsT0FBQTtBQUViLElBQUlHLHNCQUFzQixHQUFHSCxPQUFPLENBQUMsOENBQThDLENBQUMsQ0FBQ0ksT0FBTztBQUM1RkMsT0FBTyxDQUFDQyxVQUFVLEdBQUcsSUFBSTtBQUN6QkQsT0FBTyxDQUFDRCxPQUFPLEdBQUcsS0FBSyxDQUFDO0FBQ3hCLElBQUlHLFNBQVMsR0FBR0osc0JBQXNCLENBQUNILE9BQU8sNEJBQTRCLENBQUMsQ0FBQztBQUM1RSxJQUFJUSxzQkFBc0IsR0FBR0wsc0JBQXNCLENBQUNILE9BQU8sMEJBQTBCLENBQUMsQ0FBQztBQUN2RixJQUFJUyxVQUFVLEdBQUdOLHNCQUFzQixDQUFDSCxPQUFPLENBQUMsb0JBQW9CLENBQUMsQ0FBQztBQUFDLElBV2pFVSxrQkFBa0I7RUFDdEIsU0FBQUEsbUJBQVlDLFlBQVksRUFBRTtJQUFBLElBQUFWLGdCQUFBLENBQUFHLE9BQUEsUUFBQU0sa0JBQUE7SUFDeEIsSUFBSUgsU0FBUyxDQUFDSCxPQUFPLENBQUNRLEVBQUUsS0FBSyxLQUFLLEVBQUU7TUFDbEMsQ0FBQyxDQUFDLEVBQUVILFVBQVUsQ0FBQ0wsT0FBTyxFQUFFTyxZQUFZLElBQUksSUFBSSxFQUFFLDBEQUEwRCxDQUFDO01BQ3pHLElBQUksQ0FBQ0UsYUFBYSxHQUFHRixZQUFZO0lBQ25DO0VBQ0Y7RUFBQyxXQUFBVCxhQUFBLENBQUFFLE9BQUEsRUFBQU0sa0JBQUE7SUFBQUksR0FBQTtJQUFBQyxLQUFBLEVBQ0QsU0FBQUMsV0FBV0EsQ0FBQ0MsU0FBUyxFQUFFQyxRQUFRLEVBQUVDLE9BQU8sRUFBRTtNQUFBLElBQUFDLEtBQUE7TUFDeEMsSUFBSUMsbUJBQW1CO01BQ3ZCLENBQUNBLG1CQUFtQixHQUFHLElBQUksQ0FBQ1IsYUFBYSxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR1EsbUJBQW1CLENBQUNMLFdBQVcsQ0FBQ0MsU0FBUyxDQUFDO01BQ3hHLElBQUlLLFlBQVksR0FBR2Qsc0JBQXNCLENBQUNKLE9BQU8sQ0FBQ1ksV0FBVyxDQUFDQyxTQUFTLEVBQUVDLFFBQVEsRUFBRUMsT0FBTyxDQUFDO01BQzNGLE9BQU87UUFDTEksTUFBTSxFQUFFLFNBQVJBLE1BQU1BLENBQUEsRUFBUTtVQUNaLElBQUlELFlBQVksSUFBSSxJQUFJLEVBQUU7WUFDeEIsSUFBSUUsb0JBQW9CO1lBQ3hCLENBQUNBLG9CQUFvQixHQUFHSixLQUFJLENBQUNQLGFBQWEsS0FBSyxJQUFJLEdBQUcsS0FBSyxDQUFDLEdBQUdXLG9CQUFvQixDQUFDQyxlQUFlLENBQUMsQ0FBQyxDQUFDO1lBRXRHSCxZQUFZLENBQUNDLE1BQU0sQ0FBQyxDQUFDO1lBQ3JCRCxZQUFZLEdBQUcsSUFBSTtVQUNyQjtRQUNGO01BQ0YsQ0FBQztJQUNIO0VBQUM7SUFBQVIsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQVcsY0FBY0EsQ0FBQ1QsU0FBUyxFQUFFQyxRQUFRLEVBQUU7TUFDbEMsSUFBSVMsb0JBQW9CO01BQ3hCLENBQUNBLG9CQUFvQixHQUFHLElBQUksQ0FBQ2QsYUFBYSxLQUFLLElBQUksR0FBRyxLQUFLLENBQUMsR0FBR2Msb0JBQW9CLENBQUNGLGVBQWUsQ0FBQyxDQUFDLENBQUM7TUFHdEdqQixzQkFBc0IsQ0FBQ0osT0FBTyxDQUFDc0IsY0FBYyxDQUFDVCxTQUFTLEVBQUVDLFFBQVEsQ0FBQztJQUNwRTtFQUFDO0lBQUFKLEdBQUE7SUFBQUMsS0FBQSxFQUNELFNBQUFhLElBQUlBLENBQUNYLFNBQVMsRUFBRTtNQUFBLElBQUFZLHFCQUFBO01BQ2QsS0FBSyxJQUFJQyxJQUFJLEdBQUdDLFNBQVMsQ0FBQ0MsTUFBTSxFQUFFQyxJQUFJLEdBQUcsSUFBSUMsS0FBSyxDQUFDSixJQUFJLEdBQUcsQ0FBQyxHQUFHQSxJQUFJLEdBQUcsQ0FBQyxHQUFHLENBQUMsQ0FBQyxFQUFFSyxJQUFJLEdBQUcsQ0FBQyxFQUFFQSxJQUFJLEdBQUdMLElBQUksRUFBRUssSUFBSSxFQUFFLEVBQUU7UUFDMUdGLElBQUksQ0FBQ0UsSUFBSSxHQUFHLENBQUMsQ0FBQyxHQUFHSixTQUFTLENBQUNJLElBQUksQ0FBQztNQUNsQztNQUdBLENBQUFOLHFCQUFBLEdBQUFyQixzQkFBc0IsQ0FBQ0osT0FBTyxFQUFDd0IsSUFBSSxDQUFBUSxLQUFBLENBQUFQLHFCQUFBLEdBQUNaLFNBQVMsRUFBQW9CLE1BQUEsQ0FBS0osSUFBSSxFQUFDO0lBQ3pEO0VBQUM7SUFBQW5CLEdBQUE7SUFBQUMsS0FBQSxFQUNELFNBQUF1QixrQkFBa0JBLENBQUNyQixTQUFTLEVBQUU7TUFDNUIsSUFBSXNCLG9CQUFvQjtNQUN4QixDQUFDLENBQUMsRUFBRTlCLFVBQVUsQ0FBQ0wsT0FBTyxFQUFFYSxTQUFTLElBQUksSUFBSSxFQUFFLHdFQUF3RSxDQUFDO01BQ3BILENBQUNzQixvQkFBb0IsR0FBRyxJQUFJLENBQUMxQixhQUFhLEtBQUssSUFBSSxHQUFHLEtBQUssQ0FBQyxHQUFHMEIsb0JBQW9CLENBQUNkLGVBQWUsQ0FBQyxJQUFJLENBQUNlLGFBQWEsQ0FBQ3ZCLFNBQVMsQ0FBQyxDQUFDO01BQ2xJVCxzQkFBc0IsQ0FBQ0osT0FBTyxDQUFDa0Msa0JBQWtCLENBQUNyQixTQUFTLENBQUM7SUFDOUQ7RUFBQztJQUFBSCxHQUFBO0lBQUFDLEtBQUEsRUFDRCxTQUFBeUIsYUFBYUEsQ0FBQ3ZCLFNBQVMsRUFBRTtNQUN2QixPQUFPVCxzQkFBc0IsQ0FBQ0osT0FBTyxDQUFDb0MsYUFBYSxDQUFDdkIsU0FBUyxDQUFDO0lBQ2hFO0VBQUM7QUFBQTtBQUVIWixPQUFPLENBQUNELE9BQU8sR0FBR00sa0JBQWtCO0FBQ3BDK0IsTUFBTSxDQUFDcEMsT0FBTyxHQUFHQSxPQUFPLENBQUNELE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=