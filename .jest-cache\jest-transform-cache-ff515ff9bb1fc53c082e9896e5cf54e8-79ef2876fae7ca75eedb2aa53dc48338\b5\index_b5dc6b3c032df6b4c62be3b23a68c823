01e6feffcbd884da62290f964afe4567
"use strict";

exports.__esModule = true;
exports.default = void 0;
var TouchHistoryMath = {
  centroidDimension: function centroidDimension(touchHistory, touchesChangedAfter, isXAxis, ofCurrent) {
    var touchBank = touchHistory.touchBank;
    var total = 0;
    var count = 0;
    var oneTouchData = touchHistory.numberActiveTouches === 1 ? touchHistory.touchBank[touchHistory.indexOfSingleActiveTouch] : null;
    if (oneTouchData !== null) {
      if (oneTouchData.touchActive && oneTouchData.currentTimeStamp > touchesChangedAfter) {
        total += ofCurrent && isXAxis ? oneTouchData.currentPageX : ofCurrent && !isXAxis ? oneTouchData.currentPageY : !ofCurrent && isXAxis ? oneTouchData.previousPageX : oneTouchData.previousPageY;
        count = 1;
      }
    } else {
      for (var i = 0; i < touchBank.length; i++) {
        var touchTrack = touchBank[i];
        if (touchTrack !== null && touchTrack !== undefined && touchTrack.touchActive && touchTrack.currentTimeStamp >= touchesChangedAfter) {
          var toAdd = void 0;
          if (ofCurrent && isXAxis) {
            toAdd = touchTrack.currentPageX;
          } else if (ofCurrent && !isXAxis) {
            toAdd = touchTrack.currentPageY;
          } else if (!ofCurrent && isXAxis) {
            toAdd = touchTrack.previousPageX;
          } else {
            toAdd = touchTrack.previousPageY;
          }
          total += toAdd;
          count++;
        }
      }
    }
    return count > 0 ? total / count : TouchHistoryMath.noCentroid;
  },
  currentCentroidXOfTouchesChangedAfter: function currentCentroidXOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {
    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true, true);
  },
  currentCentroidYOfTouchesChangedAfter: function currentCentroidYOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {
    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false, true);
  },
  previousCentroidXOfTouchesChangedAfter: function previousCentroidXOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {
    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, true, false);
  },
  previousCentroidYOfTouchesChangedAfter: function previousCentroidYOfTouchesChangedAfter(touchHistory, touchesChangedAfter) {
    return TouchHistoryMath.centroidDimension(touchHistory, touchesChangedAfter, false, false);
  },
  currentCentroidX: function currentCentroidX(touchHistory) {
    return TouchHistoryMath.centroidDimension(touchHistory, 0, true, true);
  },
  currentCentroidY: function currentCentroidY(touchHistory) {
    return TouchHistoryMath.centroidDimension(touchHistory, 0, false, true);
  },
  noCentroid: -1
};
var _default = exports.default = TouchHistoryMath;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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