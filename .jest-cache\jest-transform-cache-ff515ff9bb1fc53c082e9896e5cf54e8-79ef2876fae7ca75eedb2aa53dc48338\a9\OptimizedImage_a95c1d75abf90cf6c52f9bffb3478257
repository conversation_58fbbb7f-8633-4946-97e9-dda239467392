350101cd26f1c9e671b7fab2ea801c62
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_294oa57f0x() {
  var path = "C:\\_SaaS\\AceMind\\project\\components\\ui\\OptimizedImage.tsx";
  var hash = "a209b39336d3cbc51ea14fe0ee8bcda9bfb99dbb";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\components\\ui\\OptimizedImage.tsx",
    statementMap: {
      "0": {
        start: {
          line: 14,
          column: 15
        },
        end: {
          line: 20,
          column: 1
        }
      },
      "1": {
        start: {
          line: 37,
          column: 54
        },
        end: {
          line: 153,
          column: 2
        }
      },
      "2": {
        start: {
          line: 51,
          column: 32
        },
        end: {
          line: 51,
          column: 46
        }
      },
      "3": {
        start: {
          line: 52,
          column: 28
        },
        end: {
          line: 52,
          column: 43
        }
      },
      "4": {
        start: {
          line: 53,
          column: 40
        },
        end: {
          line: 53,
          column: 55
        }
      },
      "5": {
        start: {
          line: 55,
          column: 21
        },
        end: {
          line: 59,
          column: 14
        }
      },
      "6": {
        start: {
          line: 56,
          column: 4
        },
        end: {
          line: 56,
          column: 22
        }
      },
      "7": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 57,
          column: 25
        }
      },
      "8": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 58,
          column: 15
        }
      },
      "9": {
        start: {
          line: 61,
          column: 22
        },
        end: {
          line: 65,
          column: 15
        }
      },
      "10": {
        start: {
          line: 62,
          column: 4
        },
        end: {
          line: 62,
          column: 22
        }
      },
      "11": {
        start: {
          line: 63,
          column: 4
        },
        end: {
          line: 63,
          column: 19
        }
      },
      "12": {
        start: {
          line: 64,
          column: 4
        },
        end: {
          line: 64,
          column: 26
        }
      },
      "13": {
        start: {
          line: 67,
          column: 25
        },
        end: {
          line: 77,
          column: 3
        }
      },
      "14": {
        start: {
          line: 68,
          column: 4
        },
        end: {
          line: 70,
          column: 5
        }
      },
      "15": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 69,
          column: 31
        }
      },
      "16": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 74,
          column: 5
        }
      },
      "17": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 73,
          column: 29
        }
      },
      "18": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 76,
          column: 18
        }
      },
      "19": {
        start: {
          line: 79,
          column: 29
        },
        end: {
          line: 96,
          column: 3
        }
      },
      "20": {
        start: {
          line: 80,
          column: 24
        },
        end: {
          line: 80,
          column: 40
        }
      },
      "21": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "22": {
        start: {
          line: 84,
          column: 18
        },
        end: {
          line: 84,
          column: 33
        }
      },
      "23": {
        start: {
          line: 87,
          column: 6
        },
        end: {
          line: 92,
          column: 7
        }
      },
      "24": {
        start: {
          line: 90,
          column: 29
        },
        end: {
          line: 90,
          column: 100
        }
      },
      "25": {
        start: {
          line: 91,
          column: 8
        },
        end: {
          line: 91,
          column: 53
        }
      },
      "26": {
        start: {
          line: 95,
          column: 4
        },
        end: {
          line: 95,
          column: 23
        }
      },
      "27": {
        start: {
          line: 98,
          column: 28
        },
        end: {
          line: 115,
          column: 3
        }
      },
      "28": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 108,
          column: 5
        }
      },
      "29": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 107,
          column: 8
        }
      },
      "30": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 114,
          column: 6
        }
      },
      "31": {
        start: {
          line: 117,
          column: 22
        },
        end: {
          line: 121,
          column: 3
        }
      },
      "32": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 120,
          column: 11
        }
      },
      "33": {
        start: {
          line: 123,
          column: 2
        },
        end: {
          line: 125,
          column: 3
        }
      },
      "34": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 124,
          column: 25
        }
      },
      "35": {
        start: {
          line: 127,
          column: 2
        },
        end: {
          line: 152,
          column: 4
        }
      },
      "36": {
        start: {
          line: 155,
          column: 0
        },
        end: {
          line: 155,
          column: 46
        }
      },
      "37": {
        start: {
          line: 157,
          column: 15
        },
        end: {
          line: 190,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 37,
            column: 59
          },
          end: {
            line: 37,
            column: 60
          }
        },
        loc: {
          start: {
            line: 50,
            column: 6
          },
          end: {
            line: 153,
            column: 1
          }
        },
        line: 50
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 55,
            column: 33
          },
          end: {
            line: 55,
            column: 34
          }
        },
        loc: {
          start: {
            line: 55,
            column: 39
          },
          end: {
            line: 59,
            column: 3
          }
        },
        line: 55
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 61,
            column: 34
          },
          end: {
            line: 61,
            column: 35
          }
        },
        loc: {
          start: {
            line: 61,
            column: 55
          },
          end: {
            line: 65,
            column: 3
          }
        },
        line: 61
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 67,
            column: 25
          },
          end: {
            line: 67,
            column: 26
          }
        },
        loc: {
          start: {
            line: 67,
            column: 31
          },
          end: {
            line: 77,
            column: 3
          }
        },
        line: 67
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 79,
            column: 29
          },
          end: {
            line: 79,
            column: 30
          }
        },
        loc: {
          start: {
            line: 79,
            column: 35
          },
          end: {
            line: 96,
            column: 3
          }
        },
        line: 79
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 98,
            column: 28
          },
          end: {
            line: 98,
            column: 29
          }
        },
        loc: {
          start: {
            line: 98,
            column: 34
          },
          end: {
            line: 115,
            column: 3
          }
        },
        line: 98
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 117,
            column: 22
          },
          end: {
            line: 117,
            column: 23
          }
        },
        loc: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 120,
            column: 11
          }
        },
        line: 118
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 42,
            column: 2
          },
          end: {
            line: 42,
            column: 22
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 42,
            column: 15
          },
          end: {
            line: 42,
            column: 22
          }
        }],
        line: 42
      },
      "1": {
        loc: {
          start: {
            line: 43,
            column: 2
          },
          end: {
            line: 43,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 43,
            column: 13
          },
          end: {
            line: 43,
            column: 21
          }
        }],
        line: 43
      },
      "2": {
        loc: {
          start: {
            line: 44,
            column: 2
          },
          end: {
            line: 44,
            column: 29
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 44,
            column: 16
          },
          end: {
            line: 44,
            column: 29
          }
        }],
        line: 44
      },
      "3": {
        loc: {
          start: {
            line: 45,
            column: 2
          },
          end: {
            line: 45,
            column: 13
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 45,
            column: 9
          },
          end: {
            line: 45,
            column: 13
          }
        }],
        line: 45
      },
      "4": {
        loc: {
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 68,
            column: 4
          },
          end: {
            line: 70,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 68
      },
      "5": {
        loc: {
          start: {
            line: 68,
            column: 8
          },
          end: {
            line: 68,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 8
          },
          end: {
            line: 68,
            column: 13
          }
        }, {
          start: {
            line: 68,
            column: 17
          },
          end: {
            line: 68,
            column: 25
          }
        }],
        line: 68
      },
      "6": {
        loc: {
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 72,
            column: 4
          },
          end: {
            line: 74,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 72
      },
      "7": {
        loc: {
          start: {
            line: 82,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 4
          },
          end: {
            line: 93,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "8": {
        loc: {
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 82,
            column: 89
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 82,
            column: 8
          },
          end: {
            line: 82,
            column: 39
          }
        }, {
          start: {
            line: 82,
            column: 43
          },
          end: {
            line: 82,
            column: 70
          }
        }, {
          start: {
            line: 82,
            column: 74
          },
          end: {
            line: 82,
            column: 89
          }
        }],
        line: 82
      },
      "9": {
        loc: {
          start: {
            line: 87,
            column: 6
          },
          end: {
            line: 92,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 87,
            column: 6
          },
          end: {
            line: 92,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 87
      },
      "10": {
        loc: {
          start: {
            line: 90,
            column: 38
          },
          end: {
            line: 90,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 90,
            column: 58
          },
          end: {
            line: 90,
            column: 61
          }
        }, {
          start: {
            line: 90,
            column: 64
          },
          end: {
            line: 90,
            column: 67
          }
        }],
        line: 90
      },
      "11": {
        loc: {
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 108,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 99
      },
      "12": {
        loc: {
          start: {
            line: 104,
            column: 22
          },
          end: {
            line: 104,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 104,
            column: 48
          },
          end: {
            line: 104,
            column: 55
          }
        }, {
          start: {
            line: 104,
            column: 58
          },
          end: {
            line: 104,
            column: 75
          }
        }],
        line: 104
      },
      "13": {
        loc: {
          start: {
            line: 123,
            column: 2
          },
          end: {
            line: 125,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 123,
            column: 2
          },
          end: {
            line: 125,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 123
      },
      "14": {
        loc: {
          start: {
            line: 123,
            column: 6
          },
          end: {
            line: 123,
            column: 24
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 123,
            column: 6
          },
          end: {
            line: 123,
            column: 11
          }
        }, {
          start: {
            line: 123,
            column: 15
          },
          end: {
            line: 123,
            column: 24
          }
        }],
        line: 123
      },
      "15": {
        loc: {
          start: {
            line: 130,
            column: 7
          },
          end: {
            line: 130,
            column: 37
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 130,
            column: 7
          },
          end: {
            line: 130,
            column: 14
          }
        }, {
          start: {
            line: 130,
            column: 18
          },
          end: {
            line: 130,
            column: 37
          }
        }],
        line: 130
      },
      "16": {
        loc: {
          start: {
            line: 138,
            column: 10
          },
          end: {
            line: 138,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 138,
            column: 10
          },
          end: {
            line: 138,
            column: 17
          }
        }, {
          start: {
            line: 138,
            column: 21
          },
          end: {
            line: 138,
            column: 34
          }
        }],
        line: 138
      },
      "17": {
        loc: {
          start: {
            line: 140,
            column: 20
          },
          end: {
            line: 140,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 140,
            column: 46
          },
          end: {
            line: 140,
            column: 53
          }
        }, {
          start: {
            line: 140,
            column: 56
          },
          end: {
            line: 140,
            column: 73
          }
        }],
        line: 140
      },
      "18": {
        loc: {
          start: {
            line: 149,
            column: 31
          },
          end: {
            line: 149,
            column: 84
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 149,
            column: 57
          },
          end: {
            line: 149,
            column: 64
          }
        }, {
          start: {
            line: 149,
            column: 67
          },
          end: {
            line: 149,
            column: 84
          }
        }],
        line: 149
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a209b39336d3cbc51ea14fe0ee8bcda9bfb99dbb"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_294oa57f0x = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_294oa57f0x();
import React, { useState, useCallback, memo } from 'react';
import { View, StyleSheet, ActivityIndicator, Text } from 'react-native';
import { Image as ExpoImage } from 'expo-image';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_294oa57f0x().s[0]++, {
  primary: '#23ba16',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb'
});
var OptimizedImage = (cov_294oa57f0x().s[1]++, memo(function (_ref) {
  var source = _ref.source,
    style = _ref.style,
    placeholder = _ref.placeholder,
    fallback = _ref.fallback,
    _ref$resizeMode = _ref.resizeMode,
    resizeMode = _ref$resizeMode === void 0 ? (cov_294oa57f0x().b[0][0]++, 'cover') : _ref$resizeMode,
    _ref$priority = _ref.priority,
    priority = _ref$priority === void 0 ? (cov_294oa57f0x().b[1][0]++, 'normal') : _ref$priority,
    _ref$cachePolicy = _ref.cachePolicy,
    cachePolicy = _ref$cachePolicy === void 0 ? (cov_294oa57f0x().b[2][0]++, 'memory-disk') : _ref$cachePolicy,
    _ref$lazy = _ref.lazy,
    lazy = _ref$lazy === void 0 ? (cov_294oa57f0x().b[3][0]++, true) : _ref$lazy,
    onLoad = _ref.onLoad,
    onError = _ref.onError,
    accessibilityLabel = _ref.accessibilityLabel,
    testID = _ref.testID;
  cov_294oa57f0x().f[0]++;
  var _ref2 = (cov_294oa57f0x().s[2]++, useState(true)),
    _ref3 = _slicedToArray(_ref2, 2),
    loading = _ref3[0],
    setLoading = _ref3[1];
  var _ref4 = (cov_294oa57f0x().s[3]++, useState(false)),
    _ref5 = _slicedToArray(_ref4, 2),
    error = _ref5[0],
    setError = _ref5[1];
  var _ref6 = (cov_294oa57f0x().s[4]++, useState(false)),
    _ref7 = _slicedToArray(_ref6, 2),
    imageLoaded = _ref7[0],
    setImageLoaded = _ref7[1];
  var handleLoad = (cov_294oa57f0x().s[5]++, useCallback(function () {
    cov_294oa57f0x().f[1]++;
    cov_294oa57f0x().s[6]++;
    setLoading(false);
    cov_294oa57f0x().s[7]++;
    setImageLoaded(true);
    cov_294oa57f0x().s[8]++;
    onLoad == null || onLoad();
  }, [onLoad]));
  var handleError = (cov_294oa57f0x().s[9]++, useCallback(function (errorEvent) {
    cov_294oa57f0x().f[2]++;
    cov_294oa57f0x().s[10]++;
    setLoading(false);
    cov_294oa57f0x().s[11]++;
    setError(true);
    cov_294oa57f0x().s[12]++;
    onError == null || onError(errorEvent);
  }, [onError]));
  cov_294oa57f0x().s[13]++;
  var getImageSource = function getImageSource() {
    cov_294oa57f0x().f[3]++;
    cov_294oa57f0x().s[14]++;
    if ((cov_294oa57f0x().b[5][0]++, error) && (cov_294oa57f0x().b[5][1]++, fallback)) {
      cov_294oa57f0x().b[4][0]++;
      cov_294oa57f0x().s[15]++;
      return {
        uri: fallback
      };
    } else {
      cov_294oa57f0x().b[4][1]++;
    }
    cov_294oa57f0x().s[16]++;
    if (typeof source === 'string') {
      cov_294oa57f0x().b[6][0]++;
      cov_294oa57f0x().s[17]++;
      return {
        uri: source
      };
    } else {
      cov_294oa57f0x().b[6][1]++;
    }
    cov_294oa57f0x().s[18]++;
    return source;
  };
  cov_294oa57f0x().s[19]++;
  var getOptimizedSource = function getOptimizedSource() {
    cov_294oa57f0x().f[4]++;
    var imageSource = (cov_294oa57f0x().s[20]++, getImageSource());
    cov_294oa57f0x().s[21]++;
    if ((cov_294oa57f0x().b[8][0]++, typeof imageSource === 'object') && (cov_294oa57f0x().b[8][1]++, !Array.isArray(imageSource)) && (cov_294oa57f0x().b[8][2]++, imageSource.uri)) {
      cov_294oa57f0x().b[7][0]++;
      var uri = (cov_294oa57f0x().s[22]++, imageSource.uri);
      cov_294oa57f0x().s[23]++;
      if (uri.startsWith('http')) {
        cov_294oa57f0x().b[9][0]++;
        var optimizedUri = (cov_294oa57f0x().s[24]++, `${uri}${uri.includes('?') ? (cov_294oa57f0x().b[10][0]++, '&') : (cov_294oa57f0x().b[10][1]++, '?')}auto=format&fit=crop&w=800&q=80`);
        cov_294oa57f0x().s[25]++;
        return Object.assign({}, imageSource, {
          uri: optimizedUri
        });
      } else {
        cov_294oa57f0x().b[9][1]++;
      }
    } else {
      cov_294oa57f0x().b[7][1]++;
    }
    cov_294oa57f0x().s[26]++;
    return imageSource;
  };
  cov_294oa57f0x().s[27]++;
  var renderPlaceholder = function renderPlaceholder() {
    cov_294oa57f0x().f[5]++;
    cov_294oa57f0x().s[28]++;
    if (placeholder) {
      cov_294oa57f0x().b[11][0]++;
      cov_294oa57f0x().s[29]++;
      return _jsx(ExpoImage, {
        source: {
          uri: placeholder
        },
        style: [styles.image, style],
        contentFit: resizeMode === 'repeat' ? (cov_294oa57f0x().b[12][0]++, 'cover') : (cov_294oa57f0x().b[12][1]++, resizeMode),
        transition: 200
      });
    } else {
      cov_294oa57f0x().b[11][1]++;
    }
    cov_294oa57f0x().s[30]++;
    return _jsx(View, {
      style: [styles.placeholder, style],
      children: _jsx(ActivityIndicator, {
        size: "small",
        color: colors.primary
      })
    });
  };
  cov_294oa57f0x().s[31]++;
  var renderError = function renderError() {
    cov_294oa57f0x().f[6]++;
    cov_294oa57f0x().s[32]++;
    return _jsx(View, {
      style: [styles.errorContainer, style],
      children: _jsx(Text, {
        style: styles.errorText,
        children: "Failed to load image"
      })
    });
  };
  cov_294oa57f0x().s[33]++;
  if ((cov_294oa57f0x().b[14][0]++, error) && (cov_294oa57f0x().b[14][1]++, !fallback)) {
    cov_294oa57f0x().b[13][0]++;
    cov_294oa57f0x().s[34]++;
    return renderError();
  } else {
    cov_294oa57f0x().b[13][1]++;
  }
  cov_294oa57f0x().s[35]++;
  return _jsxs(View, {
    style: [styles.container, style],
    testID: testID,
    children: [(cov_294oa57f0x().b[15][0]++, loading) && (cov_294oa57f0x().b[15][1]++, renderPlaceholder()), _jsx(ExpoImage, {
      source: getOptimizedSource(),
      style: [styles.image, style, (cov_294oa57f0x().b[16][0]++, loading) && (cov_294oa57f0x().b[16][1]++, styles.hidden)],
      contentFit: resizeMode === 'repeat' ? (cov_294oa57f0x().b[17][0]++, 'cover') : (cov_294oa57f0x().b[17][1]++, resizeMode),
      priority: priority,
      cachePolicy: cachePolicy,
      transition: 300,
      onLoad: handleLoad,
      onError: handleError,
      accessible: !!accessibilityLabel,
      accessibilityLabel: accessibilityLabel,
      placeholder: placeholder,
      placeholderContentFit: resizeMode === 'repeat' ? (cov_294oa57f0x().b[18][0]++, 'cover') : (cov_294oa57f0x().b[18][1]++, resizeMode)
    })]
  });
}));
cov_294oa57f0x().s[36]++;
OptimizedImage.displayName = 'OptimizedImage';
var styles = (cov_294oa57f0x().s[37]++, StyleSheet.create({
  container: {
    position: 'relative'
  },
  image: {
    width: '100%',
    height: '100%'
  },
  hidden: {
    opacity: 0
  },
  placeholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1
  },
  errorContainer: {
    backgroundColor: colors.lightGray,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20
  },
  errorText: {
    fontSize: 12,
    color: colors.gray,
    textAlign: 'center'
  }
}));
export default OptimizedImage;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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