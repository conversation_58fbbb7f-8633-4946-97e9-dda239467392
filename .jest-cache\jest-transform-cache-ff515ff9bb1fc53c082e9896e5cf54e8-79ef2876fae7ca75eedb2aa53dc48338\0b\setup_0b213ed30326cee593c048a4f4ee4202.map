{"version": 3, "names": ["_getJest<PERSON>bj", "mock", "Platform", "OS", "select", "jest", "fn", "obj", "ios", "Dimensions", "get", "width", "height", "<PERSON><PERSON>", "alert", "default", "expoConfig", "extra", "supabaseUrl", "supabaseAnonKey", "getInfoAsync", "readAsStringAsync", "writeAsStringAsync", "copyAsync", "deleteAsync", "makeDirectoryAsync", "cacheDirectory", "documentDirectory", "getFreeDiskStorageAsync", "Promise", "resolve", "Camera", "requestCameraPermissionsAsync", "status", "requestMicrophonePermissionsAsync", "Constants", "VideoQuality", "FlashMode", "off", "on", "auto", "requestPermissionsAsync", "saveToLibraryAsync", "supabase", "from", "eq", "single", "data", "error", "order", "limit", "insert", "update", "auth", "getUser", "user", "signInWithPassword", "signUp", "signOut", "storage", "upload", "getPublicUrl", "publicUrl", "performanceMonitor", "start", "end", "trackDatabaseError", "trackAPIError", "handleError", "type", "severity", "message", "userMessage", "timestamp", "Date", "toISOString", "logError", "openAIService", "generateCompletion", "success", "generateCoachingAdvice", "personalizedTip", "recommendations", "env", "key", "mockEnv", "EXPO_PUBLIC_SUPABASE_URL", "EXPO_PUBLIC_SUPABASE_ANON_KEY", "OPENAI_API_KEY", "EXPO_PUBLIC_MEDIAPIPE_MODEL_URL", "isFeatureEnabled", "_require", "require", "global", "__DEV__", "originalWarn", "console", "warn", "originalError", "beforeEach", "after<PERSON>ach", "setTimeout"], "sources": ["setup.ts"], "sourcesContent": ["/**\n * Test Setup\n * Global test configuration and mocks\n */\n\n// Define global variables\nglobal.__DEV__ = true;\n\n// Mock React Native modules\njest.mock('react-native', () => ({\n  Platform: {\n    OS: 'ios',\n    select: jest.fn((obj) => obj.ios),\n  },\n  Dimensions: {\n    get: jest.fn(() => ({ width: 375, height: 812 })),\n  },\n  Alert: {\n    alert: jest.fn(),\n  },\n}));\n\n// Mock Expo modules\njest.mock('expo-constants', () => ({\n  default: {\n    expoConfig: {\n      extra: {\n        supabaseUrl: 'https://test.supabase.co',\n        supabaseAnonKey: 'test-anon-key',\n      },\n    },\n  },\n}));\n\njest.mock('expo-file-system', () => ({\n  getInfoAsync: jest.fn(),\n  readAsStringAsync: jest.fn(),\n  writeAsStringAsync: jest.fn(),\n  copyAsync: jest.fn(),\n  deleteAsync: jest.fn(),\n  makeDirectoryAsync: jest.fn(),\n  cacheDirectory: '/mock/cache/',\n  documentDirectory: '/mock/documents/',\n  getFreeDiskStorageAsync: jest.fn(() => Promise.resolve(1000000000)), // 1GB\n}));\n\njest.mock('expo-camera', () => ({\n  Camera: {\n    requestCameraPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),\n    requestMicrophonePermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),\n    Constants: {\n      VideoQuality: {\n        '720p': '720p',\n        '1080p': '1080p',\n      },\n      FlashMode: {\n        off: 'off',\n        on: 'on',\n        auto: 'auto',\n      },\n    },\n  },\n}));\n\njest.mock('expo-media-library', () => ({\n  requestPermissionsAsync: jest.fn(() => Promise.resolve({ status: 'granted' })),\n  saveToLibraryAsync: jest.fn(),\n}));\n\n// Mock Supabase\njest.mock('@/lib/supabase', () => ({\n  supabase: {\n    from: jest.fn(() => ({\n      select: jest.fn(() => ({\n        eq: jest.fn(() => ({\n          single: jest.fn(() => Promise.resolve({ data: null, error: null })),\n          order: jest.fn(() => ({\n            limit: jest.fn(() => Promise.resolve({ data: [], error: null })),\n          })),\n        })),\n      })),\n      insert: jest.fn(() => ({\n        select: jest.fn(() => ({\n          single: jest.fn(() => Promise.resolve({ data: null, error: null })),\n        })),\n      })),\n      update: jest.fn(() => ({\n        eq: jest.fn(() => Promise.resolve({ data: null, error: null })),\n      })),\n    })),\n    auth: {\n      getUser: jest.fn(() => Promise.resolve({ data: { user: null }, error: null })),\n      signInWithPassword: jest.fn(),\n      signUp: jest.fn(),\n      signOut: jest.fn(),\n    },\n    storage: {\n      from: jest.fn(() => ({\n        upload: jest.fn(() => Promise.resolve({ data: null, error: null })),\n        getPublicUrl: jest.fn(() => ({ data: { publicUrl: 'https://test.com/file.mp4' } })),\n      })),\n    },\n  },\n}));\n\n// Mock performance monitoring\njest.mock('@/utils/performance', () => ({\n  performanceMonitor: {\n    start: jest.fn(),\n    end: jest.fn(() => 100), // Return 100ms as default\n    trackDatabaseError: jest.fn(),\n    trackAPIError: jest.fn(),\n  },\n}));\n\n// Mock error handling\njest.mock('@/utils/errorHandling', () => ({\n  handleError: jest.fn((error) => ({\n    type: 'UnknownError',\n    severity: 'medium',\n    message: error.message || 'Unknown error',\n    userMessage: error.message || 'Something went wrong',\n    timestamp: new Date().toISOString(),\n  })),\n  logError: jest.fn(),\n}));\n\n// Mock OpenAI service\njest.mock('@/services/openai', () => ({\n  openAIService: {\n    generateCompletion: jest.fn(() => Promise.resolve({\n      success: true,\n      data: 'Mock AI response',\n    })),\n    generateCoachingAdvice: jest.fn(() => Promise.resolve({\n      personalizedTip: 'Mock coaching tip',\n      recommendations: ['Tip 1', 'Tip 2'],\n    })),\n  },\n}));\n\n// Mock environment\njest.mock('@/config/environment', () => ({\n  env: {\n    get: jest.fn((key) => {\n      const mockEnv = {\n        EXPO_PUBLIC_SUPABASE_URL: 'https://test.supabase.co',\n        EXPO_PUBLIC_SUPABASE_ANON_KEY: 'test-anon-key',\n        OPENAI_API_KEY: 'test-openai-key',\n        EXPO_PUBLIC_MEDIAPIPE_MODEL_URL: 'https://test.com/model.task',\n      };\n      return mockEnv[key as keyof typeof mockEnv];\n    }),\n    isFeatureEnabled: jest.fn(() => true),\n  },\n}));\n\n// Suppress console warnings in tests\nconst originalWarn = console.warn;\nconst originalError = console.error;\n\nbeforeEach(() => {\n  console.warn = jest.fn();\n  console.error = jest.fn();\n});\n\nafterEach(() => {\n  console.warn = originalWarn;\n  console.error = originalError;\n});\n\n// Global test timeout\njest.setTimeout(30000);\n"], "mappings": "AASAA,WAAA,GAAKC,IAAI,CAAC,cAAc,EAAE;EAAA,OAAO;IAC/BC,QAAQ,EAAE;MACRC,EAAE,EAAE,KAAK;MACTC,MAAM,EAAEC,IAAI,CAACC,EAAE,CAAC,UAACC,GAAG;QAAA,OAAKA,GAAG,CAACC,GAAG;MAAA;IAClC,CAAC;IACDC,UAAU,EAAE;MACVC,GAAG,EAAEL,IAAI,CAACC,EAAE,CAAC;QAAA,OAAO;UAAEK,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE;QAAI,CAAC;MAAA,CAAC;IAClD,CAAC;IACDC,KAAK,EAAE;MACLC,KAAK,EAAET,IAAI,CAACC,EAAE,CAAC;IACjB;EACF,CAAC;AAAA,CAAC,CAAC;AAGHN,WAAA,GAAKC,IAAI,CAAC,gBAAgB,EAAE;EAAA,OAAO;IACjCc,OAAO,EAAE;MACPC,UAAU,EAAE;QACVC,KAAK,EAAE;UACLC,WAAW,EAAE,0BAA0B;UACvCC,eAAe,EAAE;QACnB;MACF;IACF;EACF,CAAC;AAAA,CAAC,CAAC;AAEHnB,WAAA,GAAKC,IAAI,CAAC,kBAAkB,EAAE;EAAA,OAAO;IACnCmB,YAAY,EAAEf,IAAI,CAACC,EAAE,CAAC,CAAC;IACvBe,iBAAiB,EAAEhB,IAAI,CAACC,EAAE,CAAC,CAAC;IAC5BgB,kBAAkB,EAAEjB,IAAI,CAACC,EAAE,CAAC,CAAC;IAC7BiB,SAAS,EAAElB,IAAI,CAACC,EAAE,CAAC,CAAC;IACpBkB,WAAW,EAAEnB,IAAI,CAACC,EAAE,CAAC,CAAC;IACtBmB,kBAAkB,EAAEpB,IAAI,CAACC,EAAE,CAAC,CAAC;IAC7BoB,cAAc,EAAE,cAAc;IAC9BC,iBAAiB,EAAE,kBAAkB;IACrCC,uBAAuB,EAAEvB,IAAI,CAACC,EAAE,CAAC;MAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC,UAAU,CAAC;IAAA;EACpE,CAAC;AAAA,CAAC,CAAC;AAEH9B,WAAA,GAAKC,IAAI,CAAC,aAAa,EAAE;EAAA,OAAO;IAC9B8B,MAAM,EAAE;MACNC,6BAA6B,EAAE3B,IAAI,CAACC,EAAE,CAAC;QAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;UAAEG,MAAM,EAAE;QAAU,CAAC,CAAC;MAAA,EAAC;MACpFC,iCAAiC,EAAE7B,IAAI,CAACC,EAAE,CAAC;QAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;UAAEG,MAAM,EAAE;QAAU,CAAC,CAAC;MAAA,EAAC;MACxFE,SAAS,EAAE;QACTC,YAAY,EAAE;UACZ,MAAM,EAAE,MAAM;UACd,OAAO,EAAE;QACX,CAAC;QACDC,SAAS,EAAE;UACTC,GAAG,EAAE,KAAK;UACVC,EAAE,EAAE,IAAI;UACRC,IAAI,EAAE;QACR;MACF;IACF;EACF,CAAC;AAAA,CAAC,CAAC;AAEHxC,WAAA,GAAKC,IAAI,CAAC,oBAAoB,EAAE;EAAA,OAAO;IACrCwC,uBAAuB,EAAEpC,IAAI,CAACC,EAAE,CAAC;MAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;QAAEG,MAAM,EAAE;MAAU,CAAC,CAAC;IAAA,EAAC;IAC9ES,kBAAkB,EAAErC,IAAI,CAACC,EAAE,CAAC;EAC9B,CAAC;AAAA,CAAC,CAAC;AAGHN,WAAA,GAAKC,IAAI,oBAAmB;EAAA,OAAO;IACjC0C,QAAQ,EAAE;MACRC,IAAI,EAAEvC,IAAI,CAACC,EAAE,CAAC;QAAA,OAAO;UACnBF,MAAM,EAAEC,IAAI,CAACC,EAAE,CAAC;YAAA,OAAO;cACrBuC,EAAE,EAAExC,IAAI,CAACC,EAAE,CAAC;gBAAA,OAAO;kBACjBwC,MAAM,EAAEzC,IAAI,CAACC,EAAE,CAAC;oBAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;sBAAEiB,IAAI,EAAE,IAAI;sBAAEC,KAAK,EAAE;oBAAK,CAAC,CAAC;kBAAA,EAAC;kBACnEC,KAAK,EAAE5C,IAAI,CAACC,EAAE,CAAC;oBAAA,OAAO;sBACpB4C,KAAK,EAAE7C,IAAI,CAACC,EAAE,CAAC;wBAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;0BAAEiB,IAAI,EAAE,EAAE;0BAAEC,KAAK,EAAE;wBAAK,CAAC,CAAC;sBAAA;oBACjE,CAAC;kBAAA,CAAC;gBACJ,CAAC;cAAA,CAAC;YACJ,CAAC;UAAA,CAAC,CAAC;UACHG,MAAM,EAAE9C,IAAI,CAACC,EAAE,CAAC;YAAA,OAAO;cACrBF,MAAM,EAAEC,IAAI,CAACC,EAAE,CAAC;gBAAA,OAAO;kBACrBwC,MAAM,EAAEzC,IAAI,CAACC,EAAE,CAAC;oBAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;sBAAEiB,IAAI,EAAE,IAAI;sBAAEC,KAAK,EAAE;oBAAK,CAAC,CAAC;kBAAA;gBACpE,CAAC;cAAA,CAAC;YACJ,CAAC;UAAA,CAAC,CAAC;UACHI,MAAM,EAAE/C,IAAI,CAACC,EAAE,CAAC;YAAA,OAAO;cACrBuC,EAAE,EAAExC,IAAI,CAACC,EAAE,CAAC;gBAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;kBAAEiB,IAAI,EAAE,IAAI;kBAAEC,KAAK,EAAE;gBAAK,CAAC,CAAC;cAAA;YAChE,CAAC;UAAA,CAAC;QACJ,CAAC;MAAA,CAAC,CAAC;MACHK,IAAI,EAAE;QACJC,OAAO,EAAEjD,IAAI,CAACC,EAAE,CAAC;UAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;YAAEiB,IAAI,EAAE;cAAEQ,IAAI,EAAE;YAAK,CAAC;YAAEP,KAAK,EAAE;UAAK,CAAC,CAAC;QAAA,EAAC;QAC9EQ,kBAAkB,EAAEnD,IAAI,CAACC,EAAE,CAAC,CAAC;QAC7BmD,MAAM,EAAEpD,IAAI,CAACC,EAAE,CAAC,CAAC;QACjBoD,OAAO,EAAErD,IAAI,CAACC,EAAE,CAAC;MACnB,CAAC;MACDqD,OAAO,EAAE;QACPf,IAAI,EAAEvC,IAAI,CAACC,EAAE,CAAC;UAAA,OAAO;YACnBsD,MAAM,EAAEvD,IAAI,CAACC,EAAE,CAAC;cAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;gBAAEiB,IAAI,EAAE,IAAI;gBAAEC,KAAK,EAAE;cAAK,CAAC,CAAC;YAAA,EAAC;YACnEa,YAAY,EAAExD,IAAI,CAACC,EAAE,CAAC;cAAA,OAAO;gBAAEyC,IAAI,EAAE;kBAAEe,SAAS,EAAE;gBAA4B;cAAE,CAAC;YAAA,CAAC;UACpF,CAAC;QAAA,CAAC;MACJ;IACF;EACF,CAAC;AAAA,CAAC,CAAC;AAGH9D,WAAA,GAAKC,IAAI,yBAAwB;EAAA,OAAO;IACtC8D,kBAAkB,EAAE;MAClBC,KAAK,EAAE3D,IAAI,CAACC,EAAE,CAAC,CAAC;MAChB2D,GAAG,EAAE5D,IAAI,CAACC,EAAE,CAAC;QAAA,OAAM,GAAG;MAAA,EAAC;MACvB4D,kBAAkB,EAAE7D,IAAI,CAACC,EAAE,CAAC,CAAC;MAC7B6D,aAAa,EAAE9D,IAAI,CAACC,EAAE,CAAC;IACzB;EACF,CAAC;AAAA,CAAC,CAAC;AAGHN,WAAA,GAAKC,IAAI,2BAA0B;EAAA,OAAO;IACxCmE,WAAW,EAAE/D,IAAI,CAACC,EAAE,CAAC,UAAC0C,KAAK;MAAA,OAAM;QAC/BqB,IAAI,EAAE,cAAc;QACpBC,QAAQ,EAAE,QAAQ;QAClBC,OAAO,EAAEvB,KAAK,CAACuB,OAAO,IAAI,eAAe;QACzCC,WAAW,EAAExB,KAAK,CAACuB,OAAO,IAAI,sBAAsB;QACpDE,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;MACpC,CAAC;IAAA,CAAC,CAAC;IACHC,QAAQ,EAAEvE,IAAI,CAACC,EAAE,CAAC;EACpB,CAAC;AAAA,CAAC,CAAC;AAGHN,WAAA,GAAKC,IAAI,uBAAsB;EAAA,OAAO;IACpC4E,aAAa,EAAE;MACbC,kBAAkB,EAAEzE,IAAI,CAACC,EAAE,CAAC;QAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;UAChDiD,OAAO,EAAE,IAAI;UACbhC,IAAI,EAAE;QACR,CAAC,CAAC;MAAA,EAAC;MACHiC,sBAAsB,EAAE3E,IAAI,CAACC,EAAE,CAAC;QAAA,OAAMuB,OAAO,CAACC,OAAO,CAAC;UACpDmD,eAAe,EAAE,mBAAmB;UACpCC,eAAe,EAAE,CAAC,OAAO,EAAE,OAAO;QACpC,CAAC,CAAC;MAAA;IACJ;EACF,CAAC;AAAA,CAAC,CAAC;AAGHlF,WAAA,GAAKC,IAAI,0BAAyB;EAAA,OAAO;IACvCkF,GAAG,EAAE;MACHzE,GAAG,EAAEL,IAAI,CAACC,EAAE,CAAC,UAAC8E,GAAG,EAAK;QACpB,IAAMC,OAAO,GAAG;UACdC,wBAAwB,EAAE,0BAA0B;UACpDC,6BAA6B,EAAE,eAAe;UAC9CC,cAAc,EAAE,iBAAiB;UACjCC,+BAA+B,EAAE;QACnC,CAAC;QACD,OAAOJ,OAAO,CAACD,GAAG,CAAyB;MAC7C,CAAC,CAAC;MACFM,gBAAgB,EAAErF,IAAI,CAACC,EAAE,CAAC;QAAA,OAAM,IAAI;MAAA;IACtC;EACF,CAAC;AAAA,CAAC,CAAC;AAAC,SAAAN,YAAA;EAAA,IAAA2F,QAAA,GAAAC,OAAA;IAAAvF,IAAA,GAAAsF,QAAA,CAAAtF,IAAA;EAAAL,WAAA,YAAAA,YAAA;IAAA,OAAAK,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AArJJwF,MAAM,CAACC,OAAO,GAAG,IAAI;AAwJrB,IAAMC,YAAY,GAAGC,OAAO,CAACC,IAAI;AACjC,IAAMC,aAAa,GAAGF,OAAO,CAAChD,KAAK;AAEnCmD,UAAU,CAAC,YAAM;EACfH,OAAO,CAACC,IAAI,GAAG5F,IAAI,CAACC,EAAE,CAAC,CAAC;EACxB0F,OAAO,CAAChD,KAAK,GAAG3C,IAAI,CAACC,EAAE,CAAC,CAAC;AAC3B,CAAC,CAAC;AAEF8F,SAAS,CAAC,YAAM;EACdJ,OAAO,CAACC,IAAI,GAAGF,YAAY;EAC3BC,OAAO,CAAChD,KAAK,GAAGkD,aAAa;AAC/B,CAAC,CAAC;AAGF7F,IAAI,CAACgG,UAAU,CAAC,KAAK,CAAC", "ignoreList": []}