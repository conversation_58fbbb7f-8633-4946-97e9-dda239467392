ee709254d54591098c09bba6d0a4878f
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _react = _interopRequireWildcard(require("react"));
var React = _react;
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _useHover = _interopRequireDefault(require("../../modules/useHover"));
var _usePressEvents = _interopRequireDefault(require("../../modules/usePressEvents"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["children", "delayLongPress", "delayPressIn", "delayPressOut", "disabled", "onBlur", "onContextMenu", "onFocus", "onHoverIn", "onHoverOut", "onKeyDown", "onLongPress", "onPress", "onPressMove", "onPressIn", "onPressOut", "style", "tabIndex", "testOnly_hovered", "testOnly_pressed"];
function Pressable(props, forwardedRef) {
  var children = props.children,
    delayLongPress = props.delayLongPress,
    delayPressIn = props.delayPressIn,
    delayPressOut = props.delayPressOut,
    disabled = props.disabled,
    onBlur = props.onBlur,
    onContextMenu = props.onContextMenu,
    onFocus = props.onFocus,
    onHoverIn = props.onHoverIn,
    onHoverOut = props.onHoverOut,
    onKeyDown = props.onKeyDown,
    onLongPress = props.onLongPress,
    onPress = props.onPress,
    onPressMove = props.onPressMove,
    onPressIn = props.onPressIn,
    onPressOut = props.onPressOut,
    style = props.style,
    tabIndex = props.tabIndex,
    testOnly_hovered = props.testOnly_hovered,
    testOnly_pressed = props.testOnly_pressed,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var _useForceableState = useForceableState(testOnly_hovered === true),
    hovered = _useForceableState[0],
    setHovered = _useForceableState[1];
  var _useForceableState2 = useForceableState(false),
    focused = _useForceableState2[0],
    setFocused = _useForceableState2[1];
  var _useForceableState3 = useForceableState(testOnly_pressed === true),
    pressed = _useForceableState3[0],
    setPressed = _useForceableState3[1];
  var hostRef = (0, _react.useRef)(null);
  var setRef = (0, _useMergeRefs.default)(forwardedRef, hostRef);
  var pressConfig = (0, _react.useMemo)(function () {
    return {
      delayLongPress: delayLongPress,
      delayPressStart: delayPressIn,
      delayPressEnd: delayPressOut,
      disabled: disabled,
      onLongPress: onLongPress,
      onPress: onPress,
      onPressChange: setPressed,
      onPressStart: onPressIn,
      onPressMove: onPressMove,
      onPressEnd: onPressOut
    };
  }, [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressMove, onPressOut, setPressed]);
  var pressEventHandlers = (0, _usePressEvents.default)(hostRef, pressConfig);
  var onContextMenuPress = pressEventHandlers.onContextMenu,
    onKeyDownPress = pressEventHandlers.onKeyDown;
  (0, _useHover.default)(hostRef, {
    contain: true,
    disabled: disabled,
    onHoverChange: setHovered,
    onHoverStart: onHoverIn,
    onHoverEnd: onHoverOut
  });
  var interactionState = {
    hovered: hovered,
    focused: focused,
    pressed: pressed
  };
  var blurHandler = React.useCallback(function (e) {
    if (e.nativeEvent.target === hostRef.current) {
      setFocused(false);
      if (onBlur != null) {
        onBlur(e);
      }
    }
  }, [hostRef, setFocused, onBlur]);
  var focusHandler = React.useCallback(function (e) {
    if (e.nativeEvent.target === hostRef.current) {
      setFocused(true);
      if (onFocus != null) {
        onFocus(e);
      }
    }
  }, [hostRef, setFocused, onFocus]);
  var contextMenuHandler = React.useCallback(function (e) {
    if (onContextMenuPress != null) {
      onContextMenuPress(e);
    }
    if (onContextMenu != null) {
      onContextMenu(e);
    }
  }, [onContextMenu, onContextMenuPress]);
  var keyDownHandler = React.useCallback(function (e) {
    if (onKeyDownPress != null) {
      onKeyDownPress(e);
    }
    if (onKeyDown != null) {
      onKeyDown(e);
    }
  }, [onKeyDown, onKeyDownPress]);
  var _tabIndex;
  if (tabIndex !== undefined) {
    _tabIndex = tabIndex;
  } else {
    _tabIndex = disabled ? -1 : 0;
  }
  return React.createElement(_View.default, (0, _extends2.default)({}, rest, pressEventHandlers, {
    "aria-disabled": disabled,
    onBlur: blurHandler,
    onContextMenu: contextMenuHandler,
    onFocus: focusHandler,
    onKeyDown: keyDownHandler,
    ref: setRef,
    style: [disabled ? styles.disabled : styles.active, typeof style === 'function' ? style(interactionState) : style],
    tabIndex: _tabIndex
  }), typeof children === 'function' ? children(interactionState) : children);
}
function useForceableState(forced) {
  var _useState = (0, _react.useState)(false),
    bool = _useState[0],
    setBool = _useState[1];
  return [bool || forced, setBool];
}
var styles = _StyleSheet.default.create({
  active: {
    cursor: 'pointer',
    touchAction: 'manipulation'
  },
  disabled: {
    pointerEvents: 'box-none'
  }
});
var MemoedPressable = (0, _react.memo)((0, _react.forwardRef)(Pressable));
MemoedPressable.displayName = 'Pressable';
var _default = exports.default = MemoedPressable;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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