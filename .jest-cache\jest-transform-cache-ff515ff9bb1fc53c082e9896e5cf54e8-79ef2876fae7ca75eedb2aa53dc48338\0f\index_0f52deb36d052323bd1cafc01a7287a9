325fe329818f770f15857ceca7d79d39
"use strict";

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _Dimensions = _interopRequireDefault(require("../Dimensions"));
var PixelRatio = function () {
  function PixelRatio() {
    (0, _classCallCheck2.default)(this, PixelRatio);
  }
  return (0, _createClass2.default)(PixelRatio, null, [{
    key: "get",
    value: function get() {
      return _Dimensions.default.get('window').scale;
    }
  }, {
    key: "getFontScale",
    value: function getFontScale() {
      return _Dimensions.default.get('window').fontScale || PixelRatio.get();
    }
  }, {
    key: "getPixelSizeForLayoutSize",
    value: function getPixelSizeForLayoutSize(layoutSize) {
      return Math.round(layoutSize * PixelRatio.get());
    }
  }, {
    key: "roundToNearestPixel",
    value: function roundToNearestPixel(layoutSize) {
      var ratio = PixelRatio.get();
      return Math.round(layoutSize * ratio) / ratio;
    }
  }]);
}();
exports.default = PixelRatio;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0MiIsInJlcXVpcmUiLCJfY2xhc3NDYWxsQ2hlY2syIiwiX2NyZWF0ZUNsYXNzMiIsIl9pbnRlcm9wUmVxdWlyZURlZmF1bHQiLCJkZWZhdWx0IiwiZXhwb3J0cyIsIl9fZXNNb2R1bGUiLCJfRGltZW5zaW9ucyIsIlBpeGVsUmF0aW8iLCJrZXkiLCJ2YWx1ZSIsImdldCIsInNjYWxlIiwiZ2V0Rm9udFNjYWxlIiwiZm9udFNjYWxlIiwiZ2V0UGl4ZWxTaXplRm9yTGF5b3V0U2l6ZSIsImxheW91dFNpemUiLCJNYXRoIiwicm91bmQiLCJyb3VuZFRvTmVhcmVzdFBpeGVsIiwicmF0aW8iLCJtb2R1bGUiXSwic291cmNlcyI6WyJpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIikuZGVmYXVsdDtcbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG52YXIgX0RpbWVuc2lvbnMgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi9EaW1lbnNpb25zXCIpKTtcbi8qKlxuICogQ29weXJpZ2h0IChjKSBOaWNvbGFzIEdhbGxhZ2hlci5cbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICogXG4gKi9cblxuLyoqXG4gKiBQaXhlbFJhdGlvIGdpdmVzIGFjY2VzcyB0byB0aGUgZGV2aWNlIHBpeGVsIGRlbnNpdHkuXG4gKi9cbmNsYXNzIFBpeGVsUmF0aW8ge1xuICAvKipcbiAgICogUmV0dXJucyB0aGUgZGV2aWNlIHBpeGVsIGRlbnNpdHkuXG4gICAqL1xuICBzdGF0aWMgZ2V0KCkge1xuICAgIHJldHVybiBfRGltZW5zaW9ucy5kZWZhdWx0LmdldCgnd2luZG93Jykuc2NhbGU7XG4gIH1cblxuICAvKipcbiAgICogTm8gZXF1aXZhbGVudCBmb3IgV2ViXG4gICAqL1xuICBzdGF0aWMgZ2V0Rm9udFNjYWxlKCkge1xuICAgIHJldHVybiBfRGltZW5zaW9ucy5kZWZhdWx0LmdldCgnd2luZG93JykuZm9udFNjYWxlIHx8IFBpeGVsUmF0aW8uZ2V0KCk7XG4gIH1cblxuICAvKipcbiAgICogQ29udmVydHMgYSBsYXlvdXQgc2l6ZSAoZHApIHRvIHBpeGVsIHNpemUgKHB4KS5cbiAgICogR3VhcmFudGVlZCB0byByZXR1cm4gYW4gaW50ZWdlciBudW1iZXIuXG4gICAqL1xuICBzdGF0aWMgZ2V0UGl4ZWxTaXplRm9yTGF5b3V0U2l6ZShsYXlvdXRTaXplKSB7XG4gICAgcmV0dXJuIE1hdGgucm91bmQobGF5b3V0U2l6ZSAqIFBpeGVsUmF0aW8uZ2V0KCkpO1xuICB9XG5cbiAgLyoqXG4gICAqIFJvdW5kcyBhIGxheW91dCBzaXplIChkcCkgdG8gdGhlIG5lYXJlc3QgbGF5b3V0IHNpemUgdGhhdCBjb3JyZXNwb25kcyB0b1xuICAgKiBhbiBpbnRlZ2VyIG51bWJlciBvZiBwaXhlbHMuIEZvciBleGFtcGxlLCBvbiBhIGRldmljZSB3aXRoIGEgUGl4ZWxSYXRpb1xuICAgKiBvZiAzLCBgUGl4ZWxSYXRpby5yb3VuZFRvTmVhcmVzdFBpeGVsKDguNCkgPSA4LjMzYCwgd2hpY2ggY29ycmVzcG9uZHMgdG9cbiAgICogZXhhY3RseSAoOC4zMyAqIDMpID0gMjUgcGl4ZWxzLlxuICAgKi9cbiAgc3RhdGljIHJvdW5kVG9OZWFyZXN0UGl4ZWwobGF5b3V0U2l6ZSkge1xuICAgIHZhciByYXRpbyA9IFBpeGVsUmF0aW8uZ2V0KCk7XG4gICAgcmV0dXJuIE1hdGgucm91bmQobGF5b3V0U2l6ZSAqIHJhdGlvKSAvIHJhdGlvO1xuICB9XG59XG5leHBvcnRzLmRlZmF1bHQgPSBQaXhlbFJhdGlvO1xubW9kdWxlLmV4cG9ydHMgPSBleHBvcnRzLmRlZmF1bHQ7Il0sIm1hcHBpbmdzIjoiQUFBQSxZQUFZOztBQUFDLElBQUFBLHVCQUFBLEdBQUFDLE9BQUE7QUFBQSxJQUFBQyxnQkFBQSxHQUFBRix1QkFBQSxDQUFBQyxPQUFBO0FBQUEsSUFBQUUsYUFBQSxHQUFBSCx1QkFBQSxDQUFBQyxPQUFBO0FBRWIsSUFBSUcsc0JBQXNCLEdBQUdILE9BQU8sQ0FBQyw4Q0FBOEMsQ0FBQyxDQUFDSSxPQUFPO0FBQzVGQyxPQUFPLENBQUNDLFVBQVUsR0FBRyxJQUFJO0FBQ3pCRCxPQUFPLENBQUNELE9BQU8sR0FBRyxLQUFLLENBQUM7QUFDeEIsSUFBSUcsV0FBVyxHQUFHSixzQkFBc0IsQ0FBQ0gsT0FBTyxnQkFBZ0IsQ0FBQyxDQUFDO0FBQUMsSUFjN0RRLFVBQVU7RUFBQSxTQUFBQSxXQUFBO0lBQUEsSUFBQVAsZ0JBQUEsQ0FBQUcsT0FBQSxRQUFBSSxVQUFBO0VBQUE7RUFBQSxXQUFBTixhQUFBLENBQUFFLE9BQUEsRUFBQUksVUFBQTtJQUFBQyxHQUFBO0lBQUFDLEtBQUEsRUFJZCxTQUFPQyxHQUFHQSxDQUFBLEVBQUc7TUFDWCxPQUFPSixXQUFXLENBQUNILE9BQU8sQ0FBQ08sR0FBRyxDQUFDLFFBQVEsQ0FBQyxDQUFDQyxLQUFLO0lBQ2hEO0VBQUM7SUFBQUgsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBT0csWUFBWUEsQ0FBQSxFQUFHO01BQ3BCLE9BQU9OLFdBQVcsQ0FBQ0gsT0FBTyxDQUFDTyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUNHLFNBQVMsSUFBSU4sVUFBVSxDQUFDRyxHQUFHLENBQUMsQ0FBQztJQUN4RTtFQUFDO0lBQUFGLEdBQUE7SUFBQUMsS0FBQSxFQU1ELFNBQU9LLHlCQUF5QkEsQ0FBQ0MsVUFBVSxFQUFFO01BQzNDLE9BQU9DLElBQUksQ0FBQ0MsS0FBSyxDQUFDRixVQUFVLEdBQUdSLFVBQVUsQ0FBQ0csR0FBRyxDQUFDLENBQUMsQ0FBQztJQUNsRDtFQUFDO0lBQUFGLEdBQUE7SUFBQUMsS0FBQSxFQVFELFNBQU9TLG1CQUFtQkEsQ0FBQ0gsVUFBVSxFQUFFO01BQ3JDLElBQUlJLEtBQUssR0FBR1osVUFBVSxDQUFDRyxHQUFHLENBQUMsQ0FBQztNQUM1QixPQUFPTSxJQUFJLENBQUNDLEtBQUssQ0FBQ0YsVUFBVSxHQUFHSSxLQUFLLENBQUMsR0FBR0EsS0FBSztJQUMvQztFQUFDO0FBQUE7QUFFSGYsT0FBTyxDQUFDRCxPQUFPLEdBQUdJLFVBQVU7QUFDNUJhLE1BQU0sQ0FBQ2hCLE9BQU8sR0FBR0EsT0FBTyxDQUFDRCxPQUFPIiwiaWdub3JlTGlzdCI6W119