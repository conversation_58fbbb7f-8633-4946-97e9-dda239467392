892a1076e5ddd144c42a59fb651e4307
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.AnimatedEvent = void 0;
exports.attachNativeEvent = attachNativeEvent;
var _AnimatedValue = _interopRequireDefault(require("./nodes/AnimatedValue"));
var _NativeAnimatedHelper = _interopRequireWildcard(require("./NativeAnimatedHelper"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var __DEV__ = process.env.NODE_ENV !== 'production';
function attachNativeEvent(viewRef, eventName, argMapping) {
  var eventMappings = [];
  var _traverse = function traverse(value, path) {
    if (value instanceof _AnimatedValue.default) {
      value.__makeNative();
      eventMappings.push({
        nativeEventPath: path,
        animatedValueTag: value.__getNativeTag()
      });
    } else if (typeof value === 'object') {
      for (var _key in value) {
        _traverse(value[_key], path.concat(_key));
      }
    }
  };
  (0, _invariant.default)(argMapping[0] && argMapping[0].nativeEvent, 'Native driven events only support animated values contained inside `nativeEvent`.');
  _traverse(argMapping[0].nativeEvent, []);
  if (viewRef != null) {
    eventMappings.forEach(function (mapping) {
      _NativeAnimatedHelper.default.API.addAnimatedEventToView(viewRef, eventName, mapping);
    });
  }
  return {
    detach: function detach() {
      if (viewRef != null) {
        eventMappings.forEach(function (mapping) {
          _NativeAnimatedHelper.default.API.removeAnimatedEventFromView(viewRef, eventName, mapping.animatedValueTag);
        });
      }
    }
  };
}
function validateMapping(argMapping, args) {
  var _validate = function validate(recMapping, recEvt, key) {
    if (recMapping instanceof _AnimatedValue.default) {
      (0, _invariant.default)(typeof recEvt === 'number', 'Bad mapping of event key ' + key + ', should be number but got ' + typeof recEvt);
      return;
    }
    if (typeof recEvt === 'number') {
      (0, _invariant.default)(recMapping instanceof _AnimatedValue.default, 'Bad mapping of type ' + typeof recMapping + ' for key ' + key + ', event value must map to AnimatedValue');
      return;
    }
    (0, _invariant.default)(typeof recMapping === 'object', 'Bad mapping of type ' + typeof recMapping + ' for key ' + key);
    (0, _invariant.default)(typeof recEvt === 'object', 'Bad event of type ' + typeof recEvt + ' for key ' + key);
    for (var mappingKey in recMapping) {
      _validate(recMapping[mappingKey], recEvt[mappingKey], mappingKey);
    }
  };
  (0, _invariant.default)(args.length >= argMapping.length, 'Event has less arguments than mapping');
  argMapping.forEach(function (mapping, idx) {
    _validate(mapping, args[idx], 'arg' + idx);
  });
}
var AnimatedEvent = function () {
  function AnimatedEvent(argMapping, config) {
    (0, _classCallCheck2.default)(this, AnimatedEvent);
    this._listeners = [];
    this._argMapping = argMapping;
    if (config == null) {
      console.warn('Animated.event now requires a second argument for options');
      config = {
        useNativeDriver: false
      };
    }
    if (config.listener) {
      this.__addListener(config.listener);
    }
    this._callListeners = this._callListeners.bind(this);
    this._attachedEvent = null;
    this.__isNative = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(config);
  }
  return (0, _createClass2.default)(AnimatedEvent, [{
    key: "__addListener",
    value: function __addListener(callback) {
      this._listeners.push(callback);
    }
  }, {
    key: "__removeListener",
    value: function __removeListener(callback) {
      this._listeners = this._listeners.filter(function (listener) {
        return listener !== callback;
      });
    }
  }, {
    key: "__attach",
    value: function __attach(viewRef, eventName) {
      (0, _invariant.default)(this.__isNative, 'Only native driven events need to be attached.');
      this._attachedEvent = attachNativeEvent(viewRef, eventName, this._argMapping);
    }
  }, {
    key: "__detach",
    value: function __detach(viewTag, eventName) {
      (0, _invariant.default)(this.__isNative, 'Only native driven events need to be detached.');
      this._attachedEvent && this._attachedEvent.detach();
    }
  }, {
    key: "__getHandler",
    value: function __getHandler() {
      var _this = this;
      if (this.__isNative) {
        if (__DEV__) {
          var _validatedMapping = false;
          return function () {
            for (var _len = arguments.length, args = new Array(_len), _key2 = 0; _key2 < _len; _key2++) {
              args[_key2] = arguments[_key2];
            }
            if (!_validatedMapping) {
              validateMapping(_this._argMapping, args);
              _validatedMapping = true;
            }
            _this._callListeners.apply(_this, args);
          };
        } else {
          return this._callListeners;
        }
      }
      var validatedMapping = false;
      return function () {
        for (var _len2 = arguments.length, args = new Array(_len2), _key3 = 0; _key3 < _len2; _key3++) {
          args[_key3] = arguments[_key3];
        }
        if (__DEV__ && !validatedMapping) {
          validateMapping(_this._argMapping, args);
          validatedMapping = true;
        }
        var _traverse2 = function traverse(recMapping, recEvt, key) {
          if (recMapping instanceof _AnimatedValue.default) {
            if (typeof recEvt === 'number') {
              recMapping.setValue(recEvt);
            }
          } else if (typeof recMapping === 'object') {
            for (var mappingKey in recMapping) {
              _traverse2(recMapping[mappingKey], recEvt[mappingKey], mappingKey);
            }
          }
        };
        _this._argMapping.forEach(function (mapping, idx) {
          _traverse2(mapping, args[idx], 'arg' + idx);
        });
        _this._callListeners.apply(_this, args);
      };
    }
  }, {
    key: "_callListeners",
    value: function _callListeners() {
      for (var _len3 = arguments.length, args = new Array(_len3), _key4 = 0; _key4 < _len3; _key4++) {
        args[_key4] = arguments[_key4];
      }
      this._listeners.forEach(function (listener) {
        return listener.apply(void 0, args);
      });
    }
  }]);
}();
exports.AnimatedEvent = AnimatedEvent;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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