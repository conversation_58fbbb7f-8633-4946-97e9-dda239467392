{"version": 3, "names": ["_interopRequireDefault", "require", "_classCallCheck2", "_createClass2", "exports", "__esModule", "default", "ImageUriCache", "dataUriPattern", "key", "value", "has", "uri", "entries", "_entries", "isDataUri", "test", "Boolean", "add", "lastUsedTimestamp", "Date", "now", "refCount", "remove", "_cleanUpIfNeeded", "imageUris", "Object", "keys", "length", "_maximumEntries", "leastRecentlyUsed<PERSON>ey", "leastRecentlyUsedEntry", "for<PERSON>ach", "entry", "id", "requests", "ImageLoader", "abort", "requestId", "image", "onerror", "onload", "getSize", "success", "failure", "complete", "interval", "setInterval", "callback", "load", "<PERSON><PERSON><PERSON><PERSON>", "naturalHeight", "naturalWidth", "clearInterval", "onLoad", "onError", "window", "Image", "e", "onDecode", "nativeEvent", "decode", "then", "setTimeout", "src", "prefetch", "Promise", "resolve", "reject", "queryCache", "uris", "result", "u", "_default"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = exports.ImageUriCache = void 0;\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar dataUriPattern = /^data:/;\nclass ImageUriCache {\n  static has(uri) {\n    var entries = ImageUriCache._entries;\n    var isDataUri = dataUriPattern.test(uri);\n    return isDataUri || Boolean(entries[uri]);\n  }\n  static add(uri) {\n    var entries = ImageUriCache._entries;\n    var lastUsedTimestamp = Date.now();\n    if (entries[uri]) {\n      entries[uri].lastUsedTimestamp = lastUsedTimestamp;\n      entries[uri].refCount += 1;\n    } else {\n      entries[uri] = {\n        lastUsedTimestamp,\n        refCount: 1\n      };\n    }\n  }\n  static remove(uri) {\n    var entries = ImageUriCache._entries;\n    if (entries[uri]) {\n      entries[uri].refCount -= 1;\n    }\n    // Free up entries when the cache is \"full\"\n    ImageUriCache._cleanUpIfNeeded();\n  }\n  static _cleanUpIfNeeded() {\n    var entries = ImageUriCache._entries;\n    var imageUris = Object.keys(entries);\n    if (imageUris.length + 1 > ImageUriCache._maximumEntries) {\n      var leastRecentlyUsedKey;\n      var leastRecentlyUsedEntry;\n      imageUris.forEach(uri => {\n        var entry = entries[uri];\n        if ((!leastRecentlyUsedEntry || entry.lastUsedTimestamp < leastRecentlyUsedEntry.lastUsedTimestamp) && entry.refCount === 0) {\n          leastRecentlyUsedKey = uri;\n          leastRecentlyUsedEntry = entry;\n        }\n      });\n      if (leastRecentlyUsedKey) {\n        delete entries[leastRecentlyUsedKey];\n      }\n    }\n  }\n}\nexports.ImageUriCache = ImageUriCache;\nImageUriCache._maximumEntries = 256;\nImageUriCache._entries = {};\nvar id = 0;\nvar requests = {};\nvar ImageLoader = {\n  abort(requestId) {\n    var image = requests[\"\" + requestId];\n    if (image) {\n      image.onerror = null;\n      image.onload = null;\n      image = null;\n      delete requests[\"\" + requestId];\n    }\n  },\n  getSize(uri, success, failure) {\n    var complete = false;\n    var interval = setInterval(callback, 16);\n    var requestId = ImageLoader.load(uri, callback, errorCallback);\n    function callback() {\n      var image = requests[\"\" + requestId];\n      if (image) {\n        var naturalHeight = image.naturalHeight,\n          naturalWidth = image.naturalWidth;\n        if (naturalHeight && naturalWidth) {\n          success(naturalWidth, naturalHeight);\n          complete = true;\n        }\n      }\n      if (complete) {\n        ImageLoader.abort(requestId);\n        clearInterval(interval);\n      }\n    }\n    function errorCallback() {\n      if (typeof failure === 'function') {\n        failure();\n      }\n      ImageLoader.abort(requestId);\n      clearInterval(interval);\n    }\n  },\n  has(uri) {\n    return ImageUriCache.has(uri);\n  },\n  load(uri, onLoad, onError) {\n    id += 1;\n    var image = new window.Image();\n    image.onerror = onError;\n    image.onload = e => {\n      // avoid blocking the main thread\n      var onDecode = () => onLoad({\n        nativeEvent: e\n      });\n      if (typeof image.decode === 'function') {\n        // Safari currently throws exceptions when decoding svgs.\n        // We want to catch that error and allow the load handler\n        // to be forwarded to the onLoad handler in this case\n        image.decode().then(onDecode, onDecode);\n      } else {\n        setTimeout(onDecode, 0);\n      }\n    };\n    image.src = uri;\n    requests[\"\" + id] = image;\n    return id;\n  },\n  prefetch(uri) {\n    return new Promise((resolve, reject) => {\n      ImageLoader.load(uri, () => {\n        // Add the uri to the cache so it can be immediately displayed when used\n        // but also immediately remove it to correctly reflect that it has no active references\n        ImageUriCache.add(uri);\n        ImageUriCache.remove(uri);\n        resolve();\n      }, reject);\n    });\n  },\n  queryCache(uris) {\n    var result = {};\n    uris.forEach(u => {\n      if (ImageUriCache.has(u)) {\n        result[u] = 'disk/memory';\n      }\n    });\n    return Promise.resolve(result);\n  }\n};\nvar _default = exports.default = ImageLoader;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEbG,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGF,OAAO,CAACG,aAAa,GAAG,KAAK,CAAC;AAUhD,IAAIC,cAAc,GAAG,QAAQ;AAAC,IACxBD,aAAa;EAAA,SAAAA,cAAA;IAAA,IAAAL,gBAAA,CAAAI,OAAA,QAAAC,aAAA;EAAA;EAAA,WAAAJ,aAAA,CAAAG,OAAA,EAAAC,aAAA;IAAAE,GAAA;IAAAC,KAAA,EACjB,SAAOC,GAAGA,CAACC,GAAG,EAAE;MACd,IAAIC,OAAO,GAAGN,aAAa,CAACO,QAAQ;MACpC,IAAIC,SAAS,GAAGP,cAAc,CAACQ,IAAI,CAACJ,GAAG,CAAC;MACxC,OAAOG,SAAS,IAAIE,OAAO,CAACJ,OAAO,CAACD,GAAG,CAAC,CAAC;IAC3C;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAOQ,GAAGA,CAACN,GAAG,EAAE;MACd,IAAIC,OAAO,GAAGN,aAAa,CAACO,QAAQ;MACpC,IAAIK,iBAAiB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;MAClC,IAAIR,OAAO,CAACD,GAAG,CAAC,EAAE;QAChBC,OAAO,CAACD,GAAG,CAAC,CAACO,iBAAiB,GAAGA,iBAAiB;QAClDN,OAAO,CAACD,GAAG,CAAC,CAACU,QAAQ,IAAI,CAAC;MAC5B,CAAC,MAAM;QACLT,OAAO,CAACD,GAAG,CAAC,GAAG;UACbO,iBAAiB,EAAjBA,iBAAiB;UACjBG,QAAQ,EAAE;QACZ,CAAC;MACH;IACF;EAAC;IAAAb,GAAA;IAAAC,KAAA,EACD,SAAOa,MAAMA,CAACX,GAAG,EAAE;MACjB,IAAIC,OAAO,GAAGN,aAAa,CAACO,QAAQ;MACpC,IAAID,OAAO,CAACD,GAAG,CAAC,EAAE;QAChBC,OAAO,CAACD,GAAG,CAAC,CAACU,QAAQ,IAAI,CAAC;MAC5B;MAEAf,aAAa,CAACiB,gBAAgB,CAAC,CAAC;IAClC;EAAC;IAAAf,GAAA;IAAAC,KAAA,EACD,SAAOc,gBAAgBA,CAAA,EAAG;MACxB,IAAIX,OAAO,GAAGN,aAAa,CAACO,QAAQ;MACpC,IAAIW,SAAS,GAAGC,MAAM,CAACC,IAAI,CAACd,OAAO,CAAC;MACpC,IAAIY,SAAS,CAACG,MAAM,GAAG,CAAC,GAAGrB,aAAa,CAACsB,eAAe,EAAE;QACxD,IAAIC,oBAAoB;QACxB,IAAIC,sBAAsB;QAC1BN,SAAS,CAACO,OAAO,CAAC,UAAApB,GAAG,EAAI;UACvB,IAAIqB,KAAK,GAAGpB,OAAO,CAACD,GAAG,CAAC;UACxB,IAAI,CAAC,CAACmB,sBAAsB,IAAIE,KAAK,CAACd,iBAAiB,GAAGY,sBAAsB,CAACZ,iBAAiB,KAAKc,KAAK,CAACX,QAAQ,KAAK,CAAC,EAAE;YAC3HQ,oBAAoB,GAAGlB,GAAG;YAC1BmB,sBAAsB,GAAGE,KAAK;UAChC;QACF,CAAC,CAAC;QACF,IAAIH,oBAAoB,EAAE;UACxB,OAAOjB,OAAO,CAACiB,oBAAoB,CAAC;QACtC;MACF;IACF;EAAC;AAAA;AAEH1B,OAAO,CAACG,aAAa,GAAGA,aAAa;AACrCA,aAAa,CAACsB,eAAe,GAAG,GAAG;AACnCtB,aAAa,CAACO,QAAQ,GAAG,CAAC,CAAC;AAC3B,IAAIoB,EAAE,GAAG,CAAC;AACV,IAAIC,QAAQ,GAAG,CAAC,CAAC;AACjB,IAAIC,WAAW,GAAG;EAChBC,KAAK,WAALA,KAAKA,CAACC,SAAS,EAAE;IACf,IAAIC,KAAK,GAAGJ,QAAQ,CAAC,EAAE,GAAGG,SAAS,CAAC;IACpC,IAAIC,KAAK,EAAE;MACTA,KAAK,CAACC,OAAO,GAAG,IAAI;MACpBD,KAAK,CAACE,MAAM,GAAG,IAAI;MACnBF,KAAK,GAAG,IAAI;MACZ,OAAOJ,QAAQ,CAAC,EAAE,GAAGG,SAAS,CAAC;IACjC;EACF,CAAC;EACDI,OAAO,WAAPA,OAAOA,CAAC9B,GAAG,EAAE+B,OAAO,EAAEC,OAAO,EAAE;IAC7B,IAAIC,QAAQ,GAAG,KAAK;IACpB,IAAIC,QAAQ,GAAGC,WAAW,CAACC,QAAQ,EAAE,EAAE,CAAC;IACxC,IAAIV,SAAS,GAAGF,WAAW,CAACa,IAAI,CAACrC,GAAG,EAAEoC,QAAQ,EAAEE,aAAa,CAAC;IAC9D,SAASF,QAAQA,CAAA,EAAG;MAClB,IAAIT,KAAK,GAAGJ,QAAQ,CAAC,EAAE,GAAGG,SAAS,CAAC;MACpC,IAAIC,KAAK,EAAE;QACT,IAAIY,aAAa,GAAGZ,KAAK,CAACY,aAAa;UACrCC,YAAY,GAAGb,KAAK,CAACa,YAAY;QACnC,IAAID,aAAa,IAAIC,YAAY,EAAE;UACjCT,OAAO,CAACS,YAAY,EAAED,aAAa,CAAC;UACpCN,QAAQ,GAAG,IAAI;QACjB;MACF;MACA,IAAIA,QAAQ,EAAE;QACZT,WAAW,CAACC,KAAK,CAACC,SAAS,CAAC;QAC5Be,aAAa,CAACP,QAAQ,CAAC;MACzB;IACF;IACA,SAASI,aAAaA,CAAA,EAAG;MACvB,IAAI,OAAON,OAAO,KAAK,UAAU,EAAE;QACjCA,OAAO,CAAC,CAAC;MACX;MACAR,WAAW,CAACC,KAAK,CAACC,SAAS,CAAC;MAC5Be,aAAa,CAACP,QAAQ,CAAC;IACzB;EACF,CAAC;EACDnC,GAAG,WAAHA,GAAGA,CAACC,GAAG,EAAE;IACP,OAAOL,aAAa,CAACI,GAAG,CAACC,GAAG,CAAC;EAC/B,CAAC;EACDqC,IAAI,WAAJA,IAAIA,CAACrC,GAAG,EAAE0C,MAAM,EAAEC,OAAO,EAAE;IACzBrB,EAAE,IAAI,CAAC;IACP,IAAIK,KAAK,GAAG,IAAIiB,MAAM,CAACC,KAAK,CAAC,CAAC;IAC9BlB,KAAK,CAACC,OAAO,GAAGe,OAAO;IACvBhB,KAAK,CAACE,MAAM,GAAG,UAAAiB,CAAC,EAAI;MAElB,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAA;QAAA,OAASL,MAAM,CAAC;UAC1BM,WAAW,EAAEF;QACf,CAAC,CAAC;MAAA;MACF,IAAI,OAAOnB,KAAK,CAACsB,MAAM,KAAK,UAAU,EAAE;QAItCtB,KAAK,CAACsB,MAAM,CAAC,CAAC,CAACC,IAAI,CAACH,QAAQ,EAAEA,QAAQ,CAAC;MACzC,CAAC,MAAM;QACLI,UAAU,CAACJ,QAAQ,EAAE,CAAC,CAAC;MACzB;IACF,CAAC;IACDpB,KAAK,CAACyB,GAAG,GAAGpD,GAAG;IACfuB,QAAQ,CAAC,EAAE,GAAGD,EAAE,CAAC,GAAGK,KAAK;IACzB,OAAOL,EAAE;EACX,CAAC;EACD+B,QAAQ,WAARA,QAAQA,CAACrD,GAAG,EAAE;IACZ,OAAO,IAAIsD,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtChC,WAAW,CAACa,IAAI,CAACrC,GAAG,EAAE,YAAM;QAG1BL,aAAa,CAACW,GAAG,CAACN,GAAG,CAAC;QACtBL,aAAa,CAACgB,MAAM,CAACX,GAAG,CAAC;QACzBuD,OAAO,CAAC,CAAC;MACX,CAAC,EAAEC,MAAM,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;EACDC,UAAU,WAAVA,UAAUA,CAACC,IAAI,EAAE;IACf,IAAIC,MAAM,GAAG,CAAC,CAAC;IACfD,IAAI,CAACtC,OAAO,CAAC,UAAAwC,CAAC,EAAI;MAChB,IAAIjE,aAAa,CAACI,GAAG,CAAC6D,CAAC,CAAC,EAAE;QACxBD,MAAM,CAACC,CAAC,CAAC,GAAG,aAAa;MAC3B;IACF,CAAC,CAAC;IACF,OAAON,OAAO,CAACC,OAAO,CAACI,MAAM,CAAC;EAChC;AACF,CAAC;AACD,IAAIE,QAAQ,GAAGrE,OAAO,CAACE,OAAO,GAAG8B,WAAW", "ignoreList": []}