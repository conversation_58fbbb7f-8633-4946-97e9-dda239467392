f1ee22d051482e7ce62e4e397bea6ef6
"use strict";
'use client';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
var dimensions = {
  window: {
    fontScale: 1,
    height: 0,
    scale: 1,
    width: 0
  },
  screen: {
    fontScale: 1,
    height: 0,
    scale: 1,
    width: 0
  }
};
var listeners = {};
var shouldInit = _canUseDom.default;
function update() {
  if (!_canUseDom.default) {
    return;
  }
  var win = window;
  var height;
  var width;
  if (win.visualViewport) {
    var visualViewport = win.visualViewport;
    height = Math.round(visualViewport.height * visualViewport.scale);
    width = Math.round(visualViewport.width * visualViewport.scale);
  } else {
    var docEl = win.document.documentElement;
    height = docEl.clientHeight;
    width = docEl.clientWidth;
  }
  dimensions.window = {
    fontScale: 1,
    height: height,
    scale: win.devicePixelRatio || 1,
    width: width
  };
  dimensions.screen = {
    fontScale: 1,
    height: win.screen.height,
    scale: win.devicePixelRatio || 1,
    width: win.screen.width
  };
}
function handleResize() {
  update();
  if (Array.isArray(listeners['change'])) {
    listeners['change'].forEach(function (handler) {
      return handler(dimensions);
    });
  }
}
var Dimensions = function () {
  function Dimensions() {
    (0, _classCallCheck2.default)(this, Dimensions);
  }
  return (0, _createClass2.default)(Dimensions, null, [{
    key: "get",
    value: function get(dimension) {
      if (shouldInit) {
        shouldInit = false;
        update();
      }
      (0, _invariant.default)(dimensions[dimension], "No dimension set for key " + dimension);
      return dimensions[dimension];
    }
  }, {
    key: "set",
    value: function set(initialDimensions) {
      if (initialDimensions) {
        if (_canUseDom.default) {
          (0, _invariant.default)(false, 'Dimensions cannot be set in the browser');
        } else {
          if (initialDimensions.screen != null) {
            dimensions.screen = initialDimensions.screen;
          }
          if (initialDimensions.window != null) {
            dimensions.window = initialDimensions.window;
          }
        }
      }
    }
  }, {
    key: "addEventListener",
    value: function addEventListener(type, handler) {
      var _this = this;
      listeners[type] = listeners[type] || [];
      listeners[type].push(handler);
      return {
        remove: function remove() {
          _this.removeEventListener(type, handler);
        }
      };
    }
  }, {
    key: "removeEventListener",
    value: function removeEventListener(type, handler) {
      if (Array.isArray(listeners[type])) {
        listeners[type] = listeners[type].filter(function (_handler) {
          return _handler !== handler;
        });
      }
    }
  }]);
}();
exports.default = Dimensions;
if (_canUseDom.default) {
  if (window.visualViewport) {
    window.visualViewport.addEventListener('resize', handleResize, false);
  } else {
    window.addEventListener('resize', handleResize, false);
  }
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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