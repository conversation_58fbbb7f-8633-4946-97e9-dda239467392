f44df594909c9c4a0eec84fc4a3bc915
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1gey8j1jx8() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\auth\\forgot-password.tsx";
  var hash = "505b153dc147ce44a71066013e1da30f51f33c59";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\auth\\forgot-password.tsx",
    statementMap: {
      "0": {
        start: {
          line: 19,
          column: 28
        },
        end: {
          line: 19,
          column: 40
        }
      },
      "1": {
        start: {
          line: 20,
          column: 32
        },
        end: {
          line: 20,
          column: 47
        }
      },
      "2": {
        start: {
          line: 21,
          column: 28
        },
        end: {
          line: 21,
          column: 37
        }
      },
      "3": {
        start: {
          line: 23,
          column: 30
        },
        end: {
          line: 52,
          column: 3
        }
      },
      "4": {
        start: {
          line: 24,
          column: 4
        },
        end: {
          line: 27,
          column: 5
        }
      },
      "5": {
        start: {
          line: 25,
          column: 6
        },
        end: {
          line: 25,
          column: 62
        }
      },
      "6": {
        start: {
          line: 26,
          column: 6
        },
        end: {
          line: 26,
          column: 13
        }
      },
      "7": {
        start: {
          line: 29,
          column: 4
        },
        end: {
          line: 29,
          column: 21
        }
      },
      "8": {
        start: {
          line: 30,
          column: 4
        },
        end: {
          line: 51,
          column: 5
        }
      },
      "9": {
        start: {
          line: 31,
          column: 24
        },
        end: {
          line: 31,
          column: 50
        }
      },
      "10": {
        start: {
          line: 33,
          column: 6
        },
        end: {
          line: 46,
          column: 7
        }
      },
      "11": {
        start: {
          line: 34,
          column: 8
        },
        end: {
          line: 34,
          column: 36
        }
      },
      "12": {
        start: {
          line: 36,
          column: 8
        },
        end: {
          line: 45,
          column: 10
        }
      },
      "13": {
        start: {
          line: 42,
          column: 29
        },
        end: {
          line: 42,
          column: 42
        }
      },
      "14": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 48,
          column: 78
        }
      },
      "15": {
        start: {
          line: 50,
          column: 6
        },
        end: {
          line: 50,
          column: 24
        }
      },
      "16": {
        start: {
          line: 54,
          column: 21
        },
        end: {
          line: 56,
          column: 3
        }
      },
      "17": {
        start: {
          line: 55,
          column: 4
        },
        end: {
          line: 55,
          column: 18
        }
      },
      "18": {
        start: {
          line: 58,
          column: 2
        },
        end: {
          line: 113,
          column: 4
        }
      },
      "19": {
        start: {
          line: 116,
          column: 15
        },
        end: {
          line: 206,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "ForgotPasswordScreen",
        decl: {
          start: {
            line: 18,
            column: 24
          },
          end: {
            line: 18,
            column: 44
          }
        },
        loc: {
          start: {
            line: 18,
            column: 47
          },
          end: {
            line: 114,
            column: 1
          }
        },
        line: 18
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 23,
            column: 30
          },
          end: {
            line: 23,
            column: 31
          }
        },
        loc: {
          start: {
            line: 23,
            column: 42
          },
          end: {
            line: 52,
            column: 3
          }
        },
        line: 23
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 42,
            column: 23
          },
          end: {
            line: 42,
            column: 24
          }
        },
        loc: {
          start: {
            line: 42,
            column: 29
          },
          end: {
            line: 42,
            column: 42
          }
        },
        line: 42
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 54,
            column: 21
          },
          end: {
            line: 54,
            column: 22
          }
        },
        loc: {
          start: {
            line: 54,
            column: 27
          },
          end: {
            line: 56,
            column: 3
          }
        },
        line: 54
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 27,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 24,
            column: 4
          },
          end: {
            line: 27,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 24
      },
      "1": {
        loc: {
          start: {
            line: 33,
            column: 6
          },
          end: {
            line: 46,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 33,
            column: 6
          },
          end: {
            line: 46,
            column: 7
          }
        }, {
          start: {
            line: 35,
            column: 13
          },
          end: {
            line: 46,
            column: 7
          }
        }],
        line: 33
      },
      "2": {
        loc: {
          start: {
            line: 65,
            column: 20
          },
          end: {
            line: 65,
            column: 64
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 65,
            column: 44
          },
          end: {
            line: 65,
            column: 53
          }
        }, {
          start: {
            line: 65,
            column: 56
          },
          end: {
            line: 65,
            column: 64
          }
        }],
        line: 65
      },
      "3": {
        loc: {
          start: {
            line: 96,
            column: 44
          },
          end: {
            line: 96,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 44
          },
          end: {
            line: 96,
            column: 51
          }
        }, {
          start: {
            line: 96,
            column: 55
          },
          end: {
            line: 96,
            column: 81
          }
        }],
        line: 96
      },
      "4": {
        loc: {
          start: {
            line: 101,
            column: 19
          },
          end: {
            line: 101,
            column: 69
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 101,
            column: 29
          },
          end: {
            line: 101,
            column: 41
          }
        }, {
          start: {
            line: 101,
            column: 44
          },
          end: {
            line: 101,
            column: 69
          }
        }],
        line: 101
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "505b153dc147ce44a71066013e1da30f51f33c59"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1gey8j1jx8 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1gey8j1jx8();
import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, StyleSheet, SafeAreaView, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { useAuth } from "../../contexts/AuthContext";
import { Mail, ArrowLeft } from 'lucide-react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
export default function ForgotPasswordScreen() {
  cov_1gey8j1jx8().f[0]++;
  var _ref = (cov_1gey8j1jx8().s[0]++, useState('')),
    _ref2 = _slicedToArray(_ref, 2),
    email = _ref2[0],
    setEmail = _ref2[1];
  var _ref3 = (cov_1gey8j1jx8().s[1]++, useState(false)),
    _ref4 = _slicedToArray(_ref3, 2),
    loading = _ref4[0],
    setLoading = _ref4[1];
  var _ref5 = (cov_1gey8j1jx8().s[2]++, useAuth()),
    resetPassword = _ref5.resetPassword;
  cov_1gey8j1jx8().s[3]++;
  var handleResetPassword = function () {
    var _ref6 = _asyncToGenerator(function* () {
      cov_1gey8j1jx8().f[1]++;
      cov_1gey8j1jx8().s[4]++;
      if (!email) {
        cov_1gey8j1jx8().b[0][0]++;
        cov_1gey8j1jx8().s[5]++;
        Alert.alert('Error', 'Please enter your email address');
        cov_1gey8j1jx8().s[6]++;
        return;
      } else {
        cov_1gey8j1jx8().b[0][1]++;
      }
      cov_1gey8j1jx8().s[7]++;
      setLoading(true);
      cov_1gey8j1jx8().s[8]++;
      try {
        var _ref7 = (cov_1gey8j1jx8().s[9]++, yield resetPassword(email)),
          error = _ref7.error;
        cov_1gey8j1jx8().s[10]++;
        if (error) {
          cov_1gey8j1jx8().b[1][0]++;
          cov_1gey8j1jx8().s[11]++;
          Alert.alert('Error', error);
        } else {
          cov_1gey8j1jx8().b[1][1]++;
          cov_1gey8j1jx8().s[12]++;
          Alert.alert('Reset Email Sent', 'Check your email for password reset instructions.', [{
            text: 'OK',
            onPress: function onPress() {
              cov_1gey8j1jx8().f[2]++;
              cov_1gey8j1jx8().s[13]++;
              return router.back();
            }
          }]);
        }
      } catch (error) {
        cov_1gey8j1jx8().s[14]++;
        Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      } finally {
        cov_1gey8j1jx8().s[15]++;
        setLoading(false);
      }
    });
    return function handleResetPassword() {
      return _ref6.apply(this, arguments);
    };
  }();
  cov_1gey8j1jx8().s[16]++;
  var handleBack = function handleBack() {
    cov_1gey8j1jx8().f[3]++;
    cov_1gey8j1jx8().s[17]++;
    router.back();
  };
  cov_1gey8j1jx8().s[18]++;
  return _jsx(SafeAreaView, {
    style: styles.container,
    children: _jsx(LinearGradient, {
      colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
      style: styles.gradient,
      children: _jsx(KeyboardAvoidingView, {
        behavior: Platform.OS === 'ios' ? (cov_1gey8j1jx8().b[2][0]++, 'padding') : (cov_1gey8j1jx8().b[2][1]++, 'height'),
        style: styles.keyboardView,
        children: _jsxs(View, {
          style: styles.content,
          children: [_jsx(TouchableOpacity, {
            onPress: handleBack,
            style: styles.backButton,
            children: _jsx(ArrowLeft, {
              size: 24,
              color: "white"
            })
          }), _jsxs(View, {
            style: styles.header,
            children: [_jsx(Text, {
              style: styles.title,
              children: "Reset Password"
            }), _jsx(Text, {
              style: styles.subtitle,
              children: "Enter your email address and we'll send you instructions to reset your password"
            })]
          }), _jsxs(View, {
            style: styles.form,
            children: [_jsxs(View, {
              style: styles.inputContainer,
              children: [_jsx(Mail, {
                size: 20,
                color: "#6b7280",
                style: styles.inputIcon
              }), _jsx(TextInput, {
                style: styles.input,
                placeholder: "Email",
                placeholderTextColor: "#6b7280",
                value: email,
                onChangeText: setEmail,
                keyboardType: "email-address",
                autoCapitalize: "none",
                autoCorrect: false
              })]
            }), _jsx(TouchableOpacity, {
              style: [styles.resetButton, (cov_1gey8j1jx8().b[3][0]++, loading) && (cov_1gey8j1jx8().b[3][1]++, styles.resetButtonDisabled)],
              onPress: handleResetPassword,
              disabled: loading,
              children: _jsx(Text, {
                style: styles.resetButtonText,
                children: loading ? (cov_1gey8j1jx8().b[4][0]++, 'Sending...') : (cov_1gey8j1jx8().b[4][1]++, 'Send Reset Instructions')
              })
            }), _jsx(TouchableOpacity, {
              onPress: handleBack,
              style: styles.backToLogin,
              children: _jsx(Text, {
                style: styles.backToLoginText,
                children: "Back to Sign In"
              })
            })]
          })]
        })
      })
    })
  });
}
var styles = (cov_1gey8j1jx8().s[19]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  keyboardView: {
    flex: 1
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: 20
  },
  backButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    zIndex: 1
  },
  header: {
    alignItems: 'center',
    marginBottom: 40
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 16
  },
  subtitle: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
    lineHeight: 24
  },
  form: {
    backgroundColor: 'white',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    borderRadius: 12,
    marginBottom: 24,
    paddingHorizontal: 16,
    paddingVertical: 12
  },
  inputIcon: {
    marginRight: 12
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#111827'
  },
  resetButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
    marginBottom: 24
  },
  resetButtonDisabled: {
    opacity: 0.6
  },
  resetButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600'
  },
  backToLogin: {
    alignItems: 'center'
  },
  backToLoginText: {
    color: '#3b82f6',
    fontSize: 14,
    fontWeight: '500'
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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