{"version": 3, "names": ["databaseService", "performanceMonitor", "SecurityService", "config", "arguments", "length", "undefined", "cov_m56dx3rw1", "b", "_classCallCheck", "rateLimitStore", "s", "Map", "<PERSON><PERSON><PERSON>", "metrics", "rateLimitViolations", "validationFailures", "auditLogsGenerated", "encryptionOperations", "securityIncidents", "lastSecurityScan", "Date", "toISOString", "f", "rateLimiting", "Object", "assign", "enabled", "windowMs", "maxRequests", "skipSuccessfulRequests", "inputValidation", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "allowedFileTypes", "maxFileSize", "encryption", "algorithm", "keyRotationDays", "auditLogging", "logLevel", "retentionDays", "cors", "<PERSON><PERSON><PERSON><PERSON>", "allowedMethods", "allowedHeaders", "initializeEncryption", "startCleanupTasks", "_createClass", "key", "value", "_initialize", "_asyncToGenerator", "start", "createSecurityTables", "loadEncryptionKey", "startSecurityMonitoring", "end", "console", "log", "error", "initialize", "apply", "checkRateLimit", "ip", "endpoint", "allowed", "now", "windowStart", "entry", "get", "count", "blocked", "set", "logAuditEvent", "action", "resource", "details", "limit", "ip<PERSON><PERSON><PERSON>", "userAgent", "severity", "retryAfter", "Math", "ceil", "validateInput", "data", "rules", "valid", "errors", "rule", "field", "required", "push", "validateType", "type", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "pattern", "test", "<PERSON><PERSON><PERSON><PERSON>", "includes", "join", "customValidator", "sanitizeInput", "input", "replace", "trim", "_encryptData", "encrypted", "<PERSON><PERSON><PERSON>", "from", "toString", "Error", "encryptData", "_x", "_decryptData", "encryptedData", "decrypted", "decryptData", "_x2", "_logAuditEvent", "event", "auditLog", "id", "generateId", "timestamp", "query", "warn", "_x3", "validateFileUpload", "file", "_file$name$split$pop", "size", "extension", "name", "split", "pop", "toLowerCase", "allowedMimeTypes", "jpg", "jpeg", "png", "mp4", "mov", "expectedMimeType", "getCorsHeaders", "origin", "headers", "getSecurityMetrics", "_performSecurityHealthCheck", "issues", "recommendations", "keyAge", "getEncryptionKeyAge", "checkAuditLogRetention", "status", "performSecurityHealthCheck", "_cleanup", "_ref", "entries", "_ref2", "_slicedToArray", "delete", "cutoffDate", "setDate", "getDate", "filter", "lt", "cleanup", "isNaN", "parse", "_createSecurityTables", "process", "env", "ENCRYPTION_KEY", "_loadEncryptionKey", "_this", "setInterval", "catch", "_this2", "_getEncryptionKeyAge", "_checkAuditLogRetention", "random", "substr", "securityService"], "sources": ["SecurityService.ts"], "sourcesContent": ["/**\n * Security Service\n * Comprehensive security implementation for production deployment\n */\n\nimport { databaseService } from '../database/DatabaseService';\nimport { performanceMonitor } from '@/utils/performance';\n\nexport interface SecurityConfig {\n  rateLimiting: {\n    enabled: boolean;\n    windowMs: number;\n    maxRequests: number;\n    skipSuccessfulRequests: boolean;\n  };\n  inputValidation: {\n    enabled: boolean;\n    maxStringLength: number;\n    allowedFileTypes: string[];\n    maxFileSize: number;\n  };\n  encryption: {\n    enabled: boolean;\n    algorithm: string;\n    keyRotationDays: number;\n  };\n  auditLogging: {\n    enabled: boolean;\n    logLevel: 'info' | 'warn' | 'error';\n    retentionDays: number;\n  };\n  cors: {\n    enabled: boolean;\n    allowedOrigins: string[];\n    allowedMethods: string[];\n    allowedHeaders: string[];\n  };\n}\n\nexport interface RateLimitEntry {\n  ip: string;\n  endpoint: string;\n  count: number;\n  windowStart: number;\n  blocked: boolean;\n}\n\nexport interface AuditLogEntry {\n  id: string;\n  userId?: string;\n  action: string;\n  resource: string;\n  details: Record<string, any>;\n  ipAddress: string;\n  userAgent: string;\n  timestamp: string;\n  severity: 'info' | 'warn' | 'error';\n}\n\nexport interface ValidationRule {\n  field: string;\n  type: 'string' | 'number' | 'email' | 'url' | 'uuid' | 'date';\n  required: boolean;\n  minLength?: number;\n  maxLength?: number;\n  pattern?: RegExp;\n  allowedValues?: any[];\n  customValidator?: (value: any) => boolean;\n}\n\nexport interface SecurityMetrics {\n  rateLimitViolations: number;\n  validationFailures: number;\n  auditLogsGenerated: number;\n  encryptionOperations: number;\n  securityIncidents: number;\n  lastSecurityScan: string;\n}\n\nclass SecurityService {\n  private config: SecurityConfig;\n  private rateLimitStore: Map<string, RateLimitEntry> = new Map();\n  private encryptionKey: string = '';\n  private metrics: SecurityMetrics = {\n    rateLimitViolations: 0,\n    validationFailures: 0,\n    auditLogsGenerated: 0,\n    encryptionOperations: 0,\n    securityIncidents: 0,\n    lastSecurityScan: new Date().toISOString(),\n  };\n\n  constructor(config: Partial<SecurityConfig> = {}) {\n    this.config = {\n      rateLimiting: {\n        enabled: true,\n        windowMs: 15 * 60 * 1000, // 15 minutes\n        maxRequests: 100,\n        skipSuccessfulRequests: false,\n        ...config.rateLimiting,\n      },\n      inputValidation: {\n        enabled: true,\n        maxStringLength: 10000,\n        allowedFileTypes: ['jpg', 'jpeg', 'png', 'mp4', 'mov'],\n        maxFileSize: 100 * 1024 * 1024, // 100MB\n        ...config.inputValidation,\n      },\n      encryption: {\n        enabled: true,\n        algorithm: 'AES-256-GCM',\n        keyRotationDays: 90,\n        ...config.encryption,\n      },\n      auditLogging: {\n        enabled: true,\n        logLevel: 'info',\n        retentionDays: 365,\n        ...config.auditLogging,\n      },\n      cors: {\n        enabled: true,\n        allowedOrigins: ['https://acemind.app', 'https://app.acemind.com'],\n        allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],\n        allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],\n        ...config.cors,\n      },\n    };\n\n    this.initializeEncryption();\n    this.startCleanupTasks();\n  }\n\n  /**\n   * Initialize security service\n   */\n  async initialize(): Promise<void> {\n    try {\n      performanceMonitor.start('security_init');\n\n      // Create security tables if they don't exist\n      await this.createSecurityTables();\n\n      // Load encryption key\n      await this.loadEncryptionKey();\n\n      // Start security monitoring\n      this.startSecurityMonitoring();\n\n      performanceMonitor.end('security_init');\n      console.log('Security service initialized successfully');\n    } catch (error) {\n      console.error('Security service initialization failed:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Check rate limiting for IP and endpoint\n   */\n  checkRateLimit(ip: string, endpoint: string): { allowed: boolean; retryAfter?: number } {\n    if (!this.config.rateLimiting.enabled) {\n      return { allowed: true };\n    }\n\n    const key = `${ip}:${endpoint}`;\n    const now = Date.now();\n    const windowStart = now - this.config.rateLimiting.windowMs;\n\n    let entry = this.rateLimitStore.get(key);\n\n    if (!entry || entry.windowStart < windowStart) {\n      // New window or expired entry\n      entry = {\n        ip,\n        endpoint,\n        count: 1,\n        windowStart: now,\n        blocked: false,\n      };\n      this.rateLimitStore.set(key, entry);\n      return { allowed: true };\n    }\n\n    entry.count++;\n\n    if (entry.count > this.config.rateLimiting.maxRequests) {\n      entry.blocked = true;\n      this.metrics.rateLimitViolations++;\n      \n      // Log security incident\n      this.logAuditEvent({\n        action: 'rate_limit_exceeded',\n        resource: endpoint,\n        details: { ip, count: entry.count, limit: this.config.rateLimiting.maxRequests },\n        ipAddress: ip,\n        userAgent: '',\n        severity: 'warn',\n      });\n\n      const retryAfter = Math.ceil((entry.windowStart + this.config.rateLimiting.windowMs - now) / 1000);\n      return { allowed: false, retryAfter };\n    }\n\n    return { allowed: true };\n  }\n\n  /**\n   * Validate input data against rules\n   */\n  validateInput(data: Record<string, any>, rules: ValidationRule[]): { valid: boolean; errors: string[] } {\n    if (!this.config.inputValidation.enabled) {\n      return { valid: true, errors: [] };\n    }\n\n    const errors: string[] = [];\n\n    for (const rule of rules) {\n      const value = data[rule.field];\n\n      // Check required fields\n      if (rule.required && (value === undefined || value === null || value === '')) {\n        errors.push(`Field '${rule.field}' is required`);\n        continue;\n      }\n\n      // Skip validation for optional empty fields\n      if (!rule.required && (value === undefined || value === null || value === '')) {\n        continue;\n      }\n\n      // Type validation\n      if (!this.validateType(value, rule.type)) {\n        errors.push(`Field '${rule.field}' must be of type ${rule.type}`);\n        continue;\n      }\n\n      // Length validation for strings\n      if (rule.type === 'string' && typeof value === 'string') {\n        if (rule.minLength && value.length < rule.minLength) {\n          errors.push(`Field '${rule.field}' must be at least ${rule.minLength} characters`);\n        }\n        if (rule.maxLength && value.length > rule.maxLength) {\n          errors.push(`Field '${rule.field}' must be at most ${rule.maxLength} characters`);\n        }\n        if (value.length > this.config.inputValidation.maxStringLength) {\n          errors.push(`Field '${rule.field}' exceeds maximum allowed length`);\n        }\n      }\n\n      // Pattern validation\n      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {\n        errors.push(`Field '${rule.field}' format is invalid`);\n      }\n\n      // Allowed values validation\n      if (rule.allowedValues && !rule.allowedValues.includes(value)) {\n        errors.push(`Field '${rule.field}' must be one of: ${rule.allowedValues.join(', ')}`);\n      }\n\n      // Custom validation\n      if (rule.customValidator && !rule.customValidator(value)) {\n        errors.push(`Field '${rule.field}' failed custom validation`);\n      }\n    }\n\n    if (errors.length > 0) {\n      this.metrics.validationFailures++;\n    }\n\n    return { valid: errors.length === 0, errors };\n  }\n\n  /**\n   * Sanitize input to prevent XSS and injection attacks\n   */\n  sanitizeInput(input: string): string {\n    if (!this.config.inputValidation.enabled) {\n      return input;\n    }\n\n    return input\n      .replace(/[<>]/g, '') // Remove potential HTML tags\n      .replace(/['\"]/g, '') // Remove quotes\n      .replace(/[;&|`$]/g, '') // Remove command injection characters\n      .trim();\n  }\n\n  /**\n   * Encrypt sensitive data\n   */\n  async encryptData(data: string): Promise<string> {\n    if (!this.config.encryption.enabled) {\n      return data;\n    }\n\n    try {\n      // Note: In a real implementation, use proper encryption library\n      // This is a simplified example\n      const encrypted = Buffer.from(data).toString('base64');\n      this.metrics.encryptionOperations++;\n      return encrypted;\n    } catch (error) {\n      console.error('Encryption failed:', error);\n      throw new Error('Data encryption failed');\n    }\n  }\n\n  /**\n   * Decrypt sensitive data\n   */\n  async decryptData(encryptedData: string): Promise<string> {\n    if (!this.config.encryption.enabled) {\n      return encryptedData;\n    }\n\n    try {\n      // Note: In a real implementation, use proper decryption\n      const decrypted = Buffer.from(encryptedData, 'base64').toString();\n      this.metrics.encryptionOperations++;\n      return decrypted;\n    } catch (error) {\n      console.error('Decryption failed:', error);\n      throw new Error('Data decryption failed');\n    }\n  }\n\n  /**\n   * Log audit event\n   */\n  async logAuditEvent(event: Omit<AuditLogEntry, 'id' | 'timestamp'>): Promise<void> {\n    if (!this.config.auditLogging.enabled) {\n      return;\n    }\n\n    try {\n      const auditLog: AuditLogEntry = {\n        id: this.generateId(),\n        timestamp: new Date().toISOString(),\n        ...event,\n      };\n\n      // Store in database\n      await databaseService.query('audit_logs', 'insert', {\n        data: auditLog,\n      });\n\n      this.metrics.auditLogsGenerated++;\n\n      // Log to console for immediate visibility\n      if (event.severity === 'error') {\n        console.error('Security Audit:', auditLog);\n      } else if (event.severity === 'warn') {\n        console.warn('Security Audit:', auditLog);\n      } else {\n        console.log('Security Audit:', auditLog);\n      }\n    } catch (error) {\n      console.error('Failed to log audit event:', error);\n    }\n  }\n\n  /**\n   * Validate file upload\n   */\n  validateFileUpload(file: { name: string; size: number; type: string }): { valid: boolean; error?: string } {\n    if (!this.config.inputValidation.enabled) {\n      return { valid: true };\n    }\n\n    // Check file size\n    if (file.size > this.config.inputValidation.maxFileSize) {\n      return { valid: false, error: 'File size exceeds maximum allowed size' };\n    }\n\n    // Check file type\n    const extension = file.name.split('.').pop()?.toLowerCase();\n    if (!extension || !this.config.inputValidation.allowedFileTypes.includes(extension)) {\n      return { valid: false, error: 'File type not allowed' };\n    }\n\n    // Check MIME type\n    const allowedMimeTypes = {\n      jpg: 'image/jpeg',\n      jpeg: 'image/jpeg',\n      png: 'image/png',\n      mp4: 'video/mp4',\n      mov: 'video/quicktime',\n    };\n\n    const expectedMimeType = allowedMimeTypes[extension as keyof typeof allowedMimeTypes];\n    if (expectedMimeType && file.type !== expectedMimeType) {\n      return { valid: false, error: 'File type mismatch' };\n    }\n\n    return { valid: true };\n  }\n\n  /**\n   * Generate CORS headers\n   */\n  getCorsHeaders(origin?: string): Record<string, string> {\n    if (!this.config.cors.enabled) {\n      return {};\n    }\n\n    const headers: Record<string, string> = {\n      'Access-Control-Allow-Methods': this.config.cors.allowedMethods.join(', '),\n      'Access-Control-Allow-Headers': this.config.cors.allowedHeaders.join(', '),\n      'Access-Control-Max-Age': '86400', // 24 hours\n    };\n\n    if (origin && this.config.cors.allowedOrigins.includes(origin)) {\n      headers['Access-Control-Allow-Origin'] = origin;\n      headers['Access-Control-Allow-Credentials'] = 'true';\n    }\n\n    return headers;\n  }\n\n  /**\n   * Get security metrics\n   */\n  getSecurityMetrics(): SecurityMetrics {\n    return { ...this.metrics };\n  }\n\n  /**\n   * Perform security health check\n   */\n  async performSecurityHealthCheck(): Promise<{\n    status: 'secure' | 'warning' | 'critical';\n    issues: string[];\n    recommendations: string[];\n  }> {\n    const issues: string[] = [];\n    const recommendations: string[] = [];\n\n    // Check rate limiting violations\n    if (this.metrics.rateLimitViolations > 100) {\n      issues.push('High number of rate limiting violations detected');\n      recommendations.push('Review rate limiting configuration and monitor for potential attacks');\n    }\n\n    // Check validation failures\n    if (this.metrics.validationFailures > 50) {\n      issues.push('High number of input validation failures');\n      recommendations.push('Review input validation rules and user education');\n    }\n\n    // Check encryption key age\n    const keyAge = await this.getEncryptionKeyAge();\n    if (keyAge > this.config.encryption.keyRotationDays) {\n      issues.push('Encryption key requires rotation');\n      recommendations.push('Rotate encryption key according to security policy');\n    }\n\n    // Check audit log retention\n    await this.checkAuditLogRetention();\n\n    let status: 'secure' | 'warning' | 'critical' = 'secure';\n    if (issues.length > 0) {\n      status = issues.length > 3 ? 'critical' : 'warning';\n    }\n\n    this.metrics.lastSecurityScan = new Date().toISOString();\n\n    return { status, issues, recommendations };\n  }\n\n  /**\n   * Clean up expired data\n   */\n  async cleanup(): Promise<void> {\n    // Clean up rate limit entries\n    const now = Date.now();\n    for (const [key, entry] of this.rateLimitStore.entries()) {\n      if (entry.windowStart < now - this.config.rateLimiting.windowMs) {\n        this.rateLimitStore.delete(key);\n      }\n    }\n\n    // Clean up old audit logs\n    if (this.config.auditLogging.enabled) {\n      const cutoffDate = new Date();\n      cutoffDate.setDate(cutoffDate.getDate() - this.config.auditLogging.retentionDays);\n\n      await databaseService.query('audit_logs', 'delete', {\n        filter: {\n          timestamp: { lt: cutoffDate.toISOString() },\n        },\n      });\n    }\n  }\n\n  // Private methods\n\n  private validateType(value: any, type: string): boolean {\n    switch (type) {\n      case 'string':\n        return typeof value === 'string';\n      case 'number':\n        return typeof value === 'number' && !isNaN(value);\n      case 'email':\n        return typeof value === 'string' && /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(value);\n      case 'url':\n        return typeof value === 'string' && /^https?:\\/\\/.+/.test(value);\n      case 'uuid':\n        return typeof value === 'string' && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value);\n      case 'date':\n        return !isNaN(Date.parse(value));\n      default:\n        return true;\n    }\n  }\n\n  private async createSecurityTables(): Promise<void> {\n    // Note: In a real implementation, this would create actual database tables\n    console.log('Security tables created/verified');\n  }\n\n  private initializeEncryption(): void {\n    // Note: In a real implementation, this would initialize proper encryption\n    this.encryptionKey = process.env.ENCRYPTION_KEY || 'default-key';\n  }\n\n  private async loadEncryptionKey(): Promise<void> {\n    // Note: In a real implementation, this would load the key from secure storage\n    console.log('Encryption key loaded');\n  }\n\n  private startSecurityMonitoring(): void {\n    // Start periodic security checks\n    setInterval(() => {\n      this.performSecurityHealthCheck().catch(console.error);\n    }, 60000); // Every minute\n  }\n\n  private startCleanupTasks(): void {\n    // Start periodic cleanup\n    setInterval(() => {\n      this.cleanup().catch(console.error);\n    }, 300000); // Every 5 minutes\n  }\n\n  private async getEncryptionKeyAge(): Promise<number> {\n    // Note: In a real implementation, this would check actual key age\n    return 30; // Mock 30 days\n  }\n\n  private async checkAuditLogRetention(): Promise<void> {\n    // Note: In a real implementation, this would check actual log retention\n    console.log('Audit log retention checked');\n  }\n\n  private generateId(): string {\n    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n}\n\n// Export singleton instance\nexport const securityService = new SecurityService();\n\nexport default SecurityService;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAASA,eAAe;AACxB,SAASC,kBAAkB;AAA8B,IAyEnDC,eAAe;EAanB,SAAAA,gBAAA,EAAkD;IAAA,IAAtCC,MAA+B,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,aAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;IAAAC,eAAA,OAAAP,eAAA;IAAA,KAXxCQ,cAAc,IAAAH,aAAA,GAAAI,CAAA,OAAgC,IAAIC,GAAG,CAAC,CAAC;IAAA,KACvDC,aAAa,IAAAN,aAAA,GAAAI,CAAA,OAAW,EAAE;IAAA,KAC1BG,OAAO,IAAAP,aAAA,GAAAI,CAAA,OAAoB;MACjCI,mBAAmB,EAAE,CAAC;MACtBC,kBAAkB,EAAE,CAAC;MACrBC,kBAAkB,EAAE,CAAC;MACrBC,oBAAoB,EAAE,CAAC;MACvBC,iBAAiB,EAAE,CAAC;MACpBC,gBAAgB,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;IAC3C,CAAC;IAAAf,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAI,CAAA;IAGC,IAAI,CAACR,MAAM,GAAG;MACZqB,YAAY,EAAAC,MAAA,CAAAC,MAAA;QACVC,OAAO,EAAE,IAAI;QACbC,QAAQ,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI;QACxBC,WAAW,EAAE,GAAG;QAChBC,sBAAsB,EAAE;MAAK,GAC1B3B,MAAM,CAACqB,YAAY,CACvB;MACDO,eAAe,EAAAN,MAAA,CAAAC,MAAA;QACbC,OAAO,EAAE,IAAI;QACbK,eAAe,EAAE,KAAK;QACtBC,gBAAgB,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;QACtDC,WAAW,EAAE,GAAG,GAAG,IAAI,GAAG;MAAI,GAC3B/B,MAAM,CAAC4B,eAAe,CAC1B;MACDI,UAAU,EAAAV,MAAA,CAAAC,MAAA;QACRC,OAAO,EAAE,IAAI;QACbS,SAAS,EAAE,aAAa;QACxBC,eAAe,EAAE;MAAE,GAChBlC,MAAM,CAACgC,UAAU,CACrB;MACDG,YAAY,EAAAb,MAAA,CAAAC,MAAA;QACVC,OAAO,EAAE,IAAI;QACbY,QAAQ,EAAE,MAAM;QAChBC,aAAa,EAAE;MAAG,GACfrC,MAAM,CAACmC,YAAY,CACvB;MACDG,IAAI,EAAAhB,MAAA,CAAAC,MAAA;QACFC,OAAO,EAAE,IAAI;QACbe,cAAc,EAAE,CAAC,qBAAqB,EAAE,yBAAyB,CAAC;QAClEC,cAAc,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,CAAC;QAC3DC,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,kBAAkB;MAAC,GAClEzC,MAAM,CAACsC,IAAI;IAElB,CAAC;IAAClC,aAAA,GAAAI,CAAA;IAEF,IAAI,CAACkC,oBAAoB,CAAC,CAAC;IAACtC,aAAA,GAAAI,CAAA;IAC5B,IAAI,CAACmC,iBAAiB,CAAC,CAAC;EAC1B;EAAC,OAAAC,YAAA,CAAA7C,eAAA;IAAA8C,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,GAAAC,iBAAA,CAKD,aAAkC;QAAA5C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QAChC,IAAI;UAAAJ,aAAA,GAAAI,CAAA;UACFV,kBAAkB,CAACmD,KAAK,CAAC,eAAe,CAAC;UAAC7C,aAAA,GAAAI,CAAA;UAG1C,MAAM,IAAI,CAAC0C,oBAAoB,CAAC,CAAC;UAAC9C,aAAA,GAAAI,CAAA;UAGlC,MAAM,IAAI,CAAC2C,iBAAiB,CAAC,CAAC;UAAC/C,aAAA,GAAAI,CAAA;UAG/B,IAAI,CAAC4C,uBAAuB,CAAC,CAAC;UAAChD,aAAA,GAAAI,CAAA;UAE/BV,kBAAkB,CAACuD,GAAG,CAAC,eAAe,CAAC;UAACjD,aAAA,GAAAI,CAAA;UACxC8C,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;QAC1D,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAApD,aAAA,GAAAI,CAAA;UACd8C,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;UAACpD,aAAA,GAAAI,CAAA;UAChE,MAAMgD,KAAK;QACb;MACF,CAAC;MAAA,SAnBKC,UAAUA,CAAA;QAAA,OAAAV,WAAA,CAAAW,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAAVwD,UAAU;IAAA;EAAA;IAAAZ,GAAA;IAAAC,KAAA,EAwBhB,SAAAa,cAAcA,CAACC,EAAU,EAAEC,QAAgB,EAA6C;MAAAzD,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MACtF,IAAI,CAAC,IAAI,CAACR,MAAM,CAACqB,YAAY,CAACG,OAAO,EAAE;QAAApB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACrC,OAAO;UAAEsD,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC;QAAA1D,aAAA,GAAAC,CAAA;MAAA;MAED,IAAMwC,GAAG,IAAAzC,aAAA,GAAAI,CAAA,QAAG,GAAGoD,EAAE,IAAIC,QAAQ,EAAE;MAC/B,IAAME,GAAG,IAAA3D,aAAA,GAAAI,CAAA,QAAGU,IAAI,CAAC6C,GAAG,CAAC,CAAC;MACtB,IAAMC,WAAW,IAAA5D,aAAA,GAAAI,CAAA,QAAGuD,GAAG,GAAG,IAAI,CAAC/D,MAAM,CAACqB,YAAY,CAACI,QAAQ;MAE3D,IAAIwC,KAAK,IAAA7D,aAAA,GAAAI,CAAA,QAAG,IAAI,CAACD,cAAc,CAAC2D,GAAG,CAACrB,GAAG,CAAC;MAACzC,aAAA,GAAAI,CAAA;MAEzC,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAC4D,KAAK,MAAA7D,aAAA,GAAAC,CAAA,UAAI4D,KAAK,CAACD,WAAW,GAAGA,WAAW,GAAE;QAAA5D,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAE7CyD,KAAK,GAAG;UACNL,EAAE,EAAFA,EAAE;UACFC,QAAQ,EAARA,QAAQ;UACRM,KAAK,EAAE,CAAC;UACRH,WAAW,EAAED,GAAG;UAChBK,OAAO,EAAE;QACX,CAAC;QAAChE,aAAA,GAAAI,CAAA;QACF,IAAI,CAACD,cAAc,CAAC8D,GAAG,CAACxB,GAAG,EAAEoB,KAAK,CAAC;QAAC7D,aAAA,GAAAI,CAAA;QACpC,OAAO;UAAEsD,OAAO,EAAE;QAAK,CAAC;MAC1B,CAAC;QAAA1D,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAEDyD,KAAK,CAACE,KAAK,EAAE;MAAC/D,aAAA,GAAAI,CAAA;MAEd,IAAIyD,KAAK,CAACE,KAAK,GAAG,IAAI,CAACnE,MAAM,CAACqB,YAAY,CAACK,WAAW,EAAE;QAAAtB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACtDyD,KAAK,CAACG,OAAO,GAAG,IAAI;QAAChE,aAAA,GAAAI,CAAA;QACrB,IAAI,CAACG,OAAO,CAACC,mBAAmB,EAAE;QAACR,aAAA,GAAAI,CAAA;QAGnC,IAAI,CAAC8D,aAAa,CAAC;UACjBC,MAAM,EAAE,qBAAqB;UAC7BC,QAAQ,EAAEX,QAAQ;UAClBY,OAAO,EAAE;YAAEb,EAAE,EAAFA,EAAE;YAAEO,KAAK,EAAEF,KAAK,CAACE,KAAK;YAAEO,KAAK,EAAE,IAAI,CAAC1E,MAAM,CAACqB,YAAY,CAACK;UAAY,CAAC;UAChFiD,SAAS,EAAEf,EAAE;UACbgB,SAAS,EAAE,EAAE;UACbC,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,IAAMC,UAAU,IAAA1E,aAAA,GAAAI,CAAA,QAAGuE,IAAI,CAACC,IAAI,CAAC,CAACf,KAAK,CAACD,WAAW,GAAG,IAAI,CAAChE,MAAM,CAACqB,YAAY,CAACI,QAAQ,GAAGsC,GAAG,IAAI,IAAI,CAAC;QAAC3D,aAAA,GAAAI,CAAA;QACnG,OAAO;UAAEsD,OAAO,EAAE,KAAK;UAAEgB,UAAU,EAAVA;QAAW,CAAC;MACvC,CAAC;QAAA1E,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,OAAO;QAAEsD,OAAO,EAAE;MAAK,CAAC;IAC1B;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAKD,SAAAmC,aAAaA,CAACC,IAAyB,EAAEC,KAAuB,EAAwC;MAAA/E,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MACtG,IAAI,CAAC,IAAI,CAACR,MAAM,CAAC4B,eAAe,CAACJ,OAAO,EAAE;QAAApB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACxC,OAAO;UAAE4E,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE;QAAG,CAAC;MACpC,CAAC;QAAAjF,aAAA,GAAAC,CAAA;MAAA;MAED,IAAMgF,MAAgB,IAAAjF,aAAA,GAAAI,CAAA,QAAG,EAAE;MAACJ,aAAA,GAAAI,CAAA;MAE5B,KAAK,IAAM8E,IAAI,IAAIH,KAAK,EAAE;QACxB,IAAMrC,KAAK,IAAA1C,aAAA,GAAAI,CAAA,QAAG0E,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC;QAACnF,aAAA,GAAAI,CAAA;QAG/B,IAAI,CAAAJ,aAAA,GAAAC,CAAA,UAAAiF,IAAI,CAACE,QAAQ,MAAK,CAAApF,aAAA,GAAAC,CAAA,UAAAyC,KAAK,KAAK3C,SAAS,MAAAC,aAAA,GAAAC,CAAA,UAAIyC,KAAK,KAAK,IAAI,MAAA1C,aAAA,GAAAC,CAAA,UAAIyC,KAAK,KAAK,EAAE,EAAC,EAAE;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UAC5E6E,MAAM,CAACI,IAAI,CAAC,UAAUH,IAAI,CAACC,KAAK,eAAe,CAAC;UAACnF,aAAA,GAAAI,CAAA;UACjD;QACF,CAAC;UAAAJ,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAACiF,IAAI,CAACE,QAAQ,MAAK,CAAApF,aAAA,GAAAC,CAAA,UAAAyC,KAAK,KAAK3C,SAAS,MAAAC,aAAA,GAAAC,CAAA,UAAIyC,KAAK,KAAK,IAAI,MAAA1C,aAAA,GAAAC,CAAA,UAAIyC,KAAK,KAAK,EAAE,EAAC,EAAE;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UAC7E;QACF,CAAC;UAAAJ,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,IAAI,CAAC,IAAI,CAACkF,YAAY,CAAC5C,KAAK,EAAEwC,IAAI,CAACK,IAAI,CAAC,EAAE;UAAAvF,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACxC6E,MAAM,CAACI,IAAI,CAAC,UAAUH,IAAI,CAACC,KAAK,qBAAqBD,IAAI,CAACK,IAAI,EAAE,CAAC;UAACvF,aAAA,GAAAI,CAAA;UAClE;QACF,CAAC;UAAAJ,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAiF,IAAI,CAACK,IAAI,KAAK,QAAQ,MAAAvF,aAAA,GAAAC,CAAA,WAAI,OAAOyC,KAAK,KAAK,QAAQ,GAAE;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACvD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAiF,IAAI,CAACM,SAAS,MAAAxF,aAAA,GAAAC,CAAA,WAAIyC,KAAK,CAAC5C,MAAM,GAAGoF,IAAI,CAACM,SAAS,GAAE;YAAAxF,aAAA,GAAAC,CAAA;YAAAD,aAAA,GAAAI,CAAA;YACnD6E,MAAM,CAACI,IAAI,CAAC,UAAUH,IAAI,CAACC,KAAK,sBAAsBD,IAAI,CAACM,SAAS,aAAa,CAAC;UACpF,CAAC;YAAAxF,aAAA,GAAAC,CAAA;UAAA;UAAAD,aAAA,GAAAI,CAAA;UACD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAiF,IAAI,CAACO,SAAS,MAAAzF,aAAA,GAAAC,CAAA,WAAIyC,KAAK,CAAC5C,MAAM,GAAGoF,IAAI,CAACO,SAAS,GAAE;YAAAzF,aAAA,GAAAC,CAAA;YAAAD,aAAA,GAAAI,CAAA;YACnD6E,MAAM,CAACI,IAAI,CAAC,UAAUH,IAAI,CAACC,KAAK,qBAAqBD,IAAI,CAACO,SAAS,aAAa,CAAC;UACnF,CAAC;YAAAzF,aAAA,GAAAC,CAAA;UAAA;UAAAD,aAAA,GAAAI,CAAA;UACD,IAAIsC,KAAK,CAAC5C,MAAM,GAAG,IAAI,CAACF,MAAM,CAAC4B,eAAe,CAACC,eAAe,EAAE;YAAAzB,aAAA,GAAAC,CAAA;YAAAD,aAAA,GAAAI,CAAA;YAC9D6E,MAAM,CAACI,IAAI,CAAC,UAAUH,IAAI,CAACC,KAAK,kCAAkC,CAAC;UACrE,CAAC;YAAAnF,aAAA,GAAAC,CAAA;UAAA;QACH,CAAC;UAAAD,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAiF,IAAI,CAACQ,OAAO,MAAA1F,aAAA,GAAAC,CAAA,WAAI,OAAOyC,KAAK,KAAK,QAAQ,MAAA1C,aAAA,GAAAC,CAAA,WAAI,CAACiF,IAAI,CAACQ,OAAO,CAACC,IAAI,CAACjD,KAAK,CAAC,GAAE;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UAC1E6E,MAAM,CAACI,IAAI,CAAC,UAAUH,IAAI,CAACC,KAAK,qBAAqB,CAAC;QACxD,CAAC;UAAAnF,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAiF,IAAI,CAACU,aAAa,MAAA5F,aAAA,GAAAC,CAAA,WAAI,CAACiF,IAAI,CAACU,aAAa,CAACC,QAAQ,CAACnD,KAAK,CAAC,GAAE;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UAC7D6E,MAAM,CAACI,IAAI,CAAC,UAAUH,IAAI,CAACC,KAAK,qBAAqBD,IAAI,CAACU,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QACvF,CAAC;UAAA9F,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAiF,IAAI,CAACa,eAAe,MAAA/F,aAAA,GAAAC,CAAA,WAAI,CAACiF,IAAI,CAACa,eAAe,CAACrD,KAAK,CAAC,GAAE;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACxD6E,MAAM,CAACI,IAAI,CAAC,UAAUH,IAAI,CAACC,KAAK,4BAA4B,CAAC;QAC/D,CAAC;UAAAnF,aAAA,GAAAC,CAAA;QAAA;MACH;MAACD,aAAA,GAAAI,CAAA;MAED,IAAI6E,MAAM,CAACnF,MAAM,GAAG,CAAC,EAAE;QAAAE,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACrB,IAAI,CAACG,OAAO,CAACE,kBAAkB,EAAE;MACnC,CAAC;QAAAT,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,OAAO;QAAE4E,KAAK,EAAEC,MAAM,CAACnF,MAAM,KAAK,CAAC;QAAEmF,MAAM,EAANA;MAAO,CAAC;IAC/C;EAAC;IAAAxC,GAAA;IAAAC,KAAA,EAKD,SAAAsD,aAAaA,CAACC,KAAa,EAAU;MAAAjG,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MACnC,IAAI,CAAC,IAAI,CAACR,MAAM,CAAC4B,eAAe,CAACJ,OAAO,EAAE;QAAApB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACxC,OAAO6F,KAAK;MACd,CAAC;QAAAjG,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,OAAO6F,KAAK,CACTC,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC,CACvBC,IAAI,CAAC,CAAC;IACX;EAAC;IAAA1D,GAAA;IAAAC,KAAA;MAAA,IAAA0D,YAAA,GAAAxD,iBAAA,CAKD,WAAkBkC,IAAY,EAAmB;QAAA9E,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QAC/C,IAAI,CAAC,IAAI,CAACR,MAAM,CAACgC,UAAU,CAACR,OAAO,EAAE;UAAApB,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACnC,OAAO0E,IAAI;QACb,CAAC;UAAA9E,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAED,IAAI;UAGF,IAAMiG,SAAS,IAAArG,aAAA,GAAAI,CAAA,QAAGkG,MAAM,CAACC,IAAI,CAACzB,IAAI,CAAC,CAAC0B,QAAQ,CAAC,QAAQ,CAAC;UAACxG,aAAA,GAAAI,CAAA;UACvD,IAAI,CAACG,OAAO,CAACI,oBAAoB,EAAE;UAACX,aAAA,GAAAI,CAAA;UACpC,OAAOiG,SAAS;QAClB,CAAC,CAAC,OAAOjD,KAAK,EAAE;UAAApD,aAAA,GAAAI,CAAA;UACd8C,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAACpD,aAAA,GAAAI,CAAA;UAC3C,MAAM,IAAIqG,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF,CAAC;MAAA,SAfKC,WAAWA,CAAAC,EAAA;QAAA,OAAAP,YAAA,CAAA9C,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAAX6G,WAAW;IAAA;EAAA;IAAAjE,GAAA;IAAAC,KAAA;MAAA,IAAAkE,YAAA,GAAAhE,iBAAA,CAoBjB,WAAkBiE,aAAqB,EAAmB;QAAA7G,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QACxD,IAAI,CAAC,IAAI,CAACR,MAAM,CAACgC,UAAU,CAACR,OAAO,EAAE;UAAApB,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACnC,OAAOyG,aAAa;QACtB,CAAC;UAAA7G,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAED,IAAI;UAEF,IAAM0G,SAAS,IAAA9G,aAAA,GAAAI,CAAA,QAAGkG,MAAM,CAACC,IAAI,CAACM,aAAa,EAAE,QAAQ,CAAC,CAACL,QAAQ,CAAC,CAAC;UAACxG,aAAA,GAAAI,CAAA;UAClE,IAAI,CAACG,OAAO,CAACI,oBAAoB,EAAE;UAACX,aAAA,GAAAI,CAAA;UACpC,OAAO0G,SAAS;QAClB,CAAC,CAAC,OAAO1D,KAAK,EAAE;UAAApD,aAAA,GAAAI,CAAA;UACd8C,OAAO,CAACE,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;UAACpD,aAAA,GAAAI,CAAA;UAC3C,MAAM,IAAIqG,KAAK,CAAC,wBAAwB,CAAC;QAC3C;MACF,CAAC;MAAA,SAdKM,WAAWA,CAAAC,GAAA;QAAA,OAAAJ,YAAA,CAAAtD,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAAXkH,WAAW;IAAA;EAAA;IAAAtE,GAAA;IAAAC,KAAA;MAAA,IAAAuE,cAAA,GAAArE,iBAAA,CAmBjB,WAAoBsE,KAA8C,EAAiB;QAAAlH,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QACjF,IAAI,CAAC,IAAI,CAACR,MAAM,CAACmC,YAAY,CAACX,OAAO,EAAE;UAAApB,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACrC;QACF,CAAC;UAAAJ,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAED,IAAI;UACF,IAAM+G,QAAuB,IAAAnH,aAAA,GAAAI,CAAA,QAAAc,MAAA,CAAAC,MAAA;YAC3BiG,EAAE,EAAE,IAAI,CAACC,UAAU,CAAC,CAAC;YACrBC,SAAS,EAAE,IAAIxG,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC;UAAC,GAChCmG,KAAK,EACT;UAAClH,aAAA,GAAAI,CAAA;UAGF,MAAMX,eAAe,CAAC8H,KAAK,CAAC,YAAY,EAAE,QAAQ,EAAE;YAClDzC,IAAI,EAAEqC;UACR,CAAC,CAAC;UAACnH,aAAA,GAAAI,CAAA;UAEH,IAAI,CAACG,OAAO,CAACG,kBAAkB,EAAE;UAACV,aAAA,GAAAI,CAAA;UAGlC,IAAI8G,KAAK,CAACzC,QAAQ,KAAK,OAAO,EAAE;YAAAzE,aAAA,GAAAC,CAAA;YAAAD,aAAA,GAAAI,CAAA;YAC9B8C,OAAO,CAACE,KAAK,CAAC,iBAAiB,EAAE+D,QAAQ,CAAC;UAC5C,CAAC,MAAM;YAAAnH,aAAA,GAAAC,CAAA;YAAAD,aAAA,GAAAI,CAAA;YAAA,IAAI8G,KAAK,CAACzC,QAAQ,KAAK,MAAM,EAAE;cAAAzE,aAAA,GAAAC,CAAA;cAAAD,aAAA,GAAAI,CAAA;cACpC8C,OAAO,CAACsE,IAAI,CAAC,iBAAiB,EAAEL,QAAQ,CAAC;YAC3C,CAAC,MAAM;cAAAnH,aAAA,GAAAC,CAAA;cAAAD,aAAA,GAAAI,CAAA;cACL8C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEgE,QAAQ,CAAC;YAC1C;UAAA;QACF,CAAC,CAAC,OAAO/D,KAAK,EAAE;UAAApD,aAAA,GAAAI,CAAA;UACd8C,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QACpD;MACF,CAAC;MAAA,SA9BKc,aAAaA,CAAAuD,GAAA;QAAA,OAAAR,cAAA,CAAA3D,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAAbqE,aAAa;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA,EAmCnB,SAAAgF,kBAAkBA,CAACC,IAAkD,EAAsC;MAAA,IAAAC,oBAAA;MAAA5H,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MACzG,IAAI,CAAC,IAAI,CAACR,MAAM,CAAC4B,eAAe,CAACJ,OAAO,EAAE;QAAApB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACxC,OAAO;UAAE4E,KAAK,EAAE;QAAK,CAAC;MACxB,CAAC;QAAAhF,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAGD,IAAIuH,IAAI,CAACE,IAAI,GAAG,IAAI,CAACjI,MAAM,CAAC4B,eAAe,CAACG,WAAW,EAAE;QAAA3B,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACvD,OAAO;UAAE4E,KAAK,EAAE,KAAK;UAAE5B,KAAK,EAAE;QAAyC,CAAC;MAC1E,CAAC;QAAApD,aAAA,GAAAC,CAAA;MAAA;MAGD,IAAM6H,SAAS,IAAA9H,aAAA,GAAAI,CAAA,SAAAwH,oBAAA,GAAGD,IAAI,CAACI,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,qBAA1BL,oBAAA,CAA4BM,WAAW,CAAC,CAAC;MAAClI,aAAA,GAAAI,CAAA;MAC5D,IAAI,CAAAJ,aAAA,GAAAC,CAAA,YAAC6H,SAAS,MAAA9H,aAAA,GAAAC,CAAA,WAAI,CAAC,IAAI,CAACL,MAAM,CAAC4B,eAAe,CAACE,gBAAgB,CAACmE,QAAQ,CAACiC,SAAS,CAAC,GAAE;QAAA9H,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACnF,OAAO;UAAE4E,KAAK,EAAE,KAAK;UAAE5B,KAAK,EAAE;QAAwB,CAAC;MACzD,CAAC;QAAApD,aAAA,GAAAC,CAAA;MAAA;MAGD,IAAMkI,gBAAgB,IAAAnI,aAAA,GAAAI,CAAA,SAAG;QACvBgI,GAAG,EAAE,YAAY;QACjBC,IAAI,EAAE,YAAY;QAClBC,GAAG,EAAE,WAAW;QAChBC,GAAG,EAAE,WAAW;QAChBC,GAAG,EAAE;MACP,CAAC;MAED,IAAMC,gBAAgB,IAAAzI,aAAA,GAAAI,CAAA,SAAG+H,gBAAgB,CAACL,SAAS,CAAkC;MAAC9H,aAAA,GAAAI,CAAA;MACtF,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAAwI,gBAAgB,MAAAzI,aAAA,GAAAC,CAAA,WAAI0H,IAAI,CAACpC,IAAI,KAAKkD,gBAAgB,GAAE;QAAAzI,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QACtD,OAAO;UAAE4E,KAAK,EAAE,KAAK;UAAE5B,KAAK,EAAE;QAAqB,CAAC;MACtD,CAAC;QAAApD,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,OAAO;QAAE4E,KAAK,EAAE;MAAK,CAAC;IACxB;EAAC;IAAAvC,GAAA;IAAAC,KAAA,EAKD,SAAAgG,cAAcA,CAACC,MAAe,EAA0B;MAAA3I,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MACtD,IAAI,CAAC,IAAI,CAACR,MAAM,CAACsC,IAAI,CAACd,OAAO,EAAE;QAAApB,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAC7B,OAAO,CAAC,CAAC;MACX,CAAC;QAAAJ,aAAA,GAAAC,CAAA;MAAA;MAED,IAAM2I,OAA+B,IAAA5I,aAAA,GAAAI,CAAA,SAAG;QACtC,8BAA8B,EAAE,IAAI,CAACR,MAAM,CAACsC,IAAI,CAACE,cAAc,CAAC0D,IAAI,CAAC,IAAI,CAAC;QAC1E,8BAA8B,EAAE,IAAI,CAAClG,MAAM,CAACsC,IAAI,CAACG,cAAc,CAACyD,IAAI,CAAC,IAAI,CAAC;QAC1E,wBAAwB,EAAE;MAC5B,CAAC;MAAC9F,aAAA,GAAAI,CAAA;MAEF,IAAI,CAAAJ,aAAA,GAAAC,CAAA,WAAA0I,MAAM,MAAA3I,aAAA,GAAAC,CAAA,WAAI,IAAI,CAACL,MAAM,CAACsC,IAAI,CAACC,cAAc,CAAC0D,QAAQ,CAAC8C,MAAM,CAAC,GAAE;QAAA3I,aAAA,GAAAC,CAAA;QAAAD,aAAA,GAAAI,CAAA;QAC9DwI,OAAO,CAAC,6BAA6B,CAAC,GAAGD,MAAM;QAAC3I,aAAA,GAAAI,CAAA;QAChDwI,OAAO,CAAC,kCAAkC,CAAC,GAAG,MAAM;MACtD,CAAC;QAAA5I,aAAA,GAAAC,CAAA;MAAA;MAAAD,aAAA,GAAAI,CAAA;MAED,OAAOwI,OAAO;IAChB;EAAC;IAAAnG,GAAA;IAAAC,KAAA,EAKD,SAAAmG,kBAAkBA,CAAA,EAAoB;MAAA7I,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MACpC,OAAAc,MAAA,CAAAC,MAAA,KAAY,IAAI,CAACZ,OAAO;IAC1B;EAAC;IAAAkC,GAAA;IAAAC,KAAA;MAAA,IAAAoG,2BAAA,GAAAlG,iBAAA,CAKD,aAIG;QAAA5C,aAAA,GAAAgB,CAAA;QACD,IAAM+H,MAAgB,IAAA/I,aAAA,GAAAI,CAAA,SAAG,EAAE;QAC3B,IAAM4I,eAAyB,IAAAhJ,aAAA,GAAAI,CAAA,SAAG,EAAE;QAACJ,aAAA,GAAAI,CAAA;QAGrC,IAAI,IAAI,CAACG,OAAO,CAACC,mBAAmB,GAAG,GAAG,EAAE;UAAAR,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UAC1C2I,MAAM,CAAC1D,IAAI,CAAC,kDAAkD,CAAC;UAACrF,aAAA,GAAAI,CAAA;UAChE4I,eAAe,CAAC3D,IAAI,CAAC,sEAAsE,CAAC;QAC9F,CAAC;UAAArF,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,IAAI,IAAI,CAACG,OAAO,CAACE,kBAAkB,GAAG,EAAE,EAAE;UAAAT,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACxC2I,MAAM,CAAC1D,IAAI,CAAC,0CAA0C,CAAC;UAACrF,aAAA,GAAAI,CAAA;UACxD4I,eAAe,CAAC3D,IAAI,CAAC,kDAAkD,CAAC;QAC1E,CAAC;UAAArF,aAAA,GAAAC,CAAA;QAAA;QAGD,IAAMgJ,MAAM,IAAAjJ,aAAA,GAAAI,CAAA,eAAS,IAAI,CAAC8I,mBAAmB,CAAC,CAAC;QAAClJ,aAAA,GAAAI,CAAA;QAChD,IAAI6I,MAAM,GAAG,IAAI,CAACrJ,MAAM,CAACgC,UAAU,CAACE,eAAe,EAAE;UAAA9B,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACnD2I,MAAM,CAAC1D,IAAI,CAAC,kCAAkC,CAAC;UAACrF,aAAA,GAAAI,CAAA;UAChD4I,eAAe,CAAC3D,IAAI,CAAC,oDAAoD,CAAC;QAC5E,CAAC;UAAArF,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAGD,MAAM,IAAI,CAAC+I,sBAAsB,CAAC,CAAC;QAEnC,IAAIC,MAAyC,IAAApJ,aAAA,GAAAI,CAAA,SAAG,QAAQ;QAACJ,aAAA,GAAAI,CAAA;QACzD,IAAI2I,MAAM,CAACjJ,MAAM,GAAG,CAAC,EAAE;UAAAE,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACrBgJ,MAAM,GAAGL,MAAM,CAACjJ,MAAM,GAAG,CAAC,IAAAE,aAAA,GAAAC,CAAA,WAAG,UAAU,KAAAD,aAAA,GAAAC,CAAA,WAAG,SAAS;QACrD,CAAC;UAAAD,aAAA,GAAAC,CAAA;QAAA;QAAAD,aAAA,GAAAI,CAAA;QAED,IAAI,CAACG,OAAO,CAACM,gBAAgB,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAACf,aAAA,GAAAI,CAAA;QAEzD,OAAO;UAAEgJ,MAAM,EAANA,MAAM;UAAEL,MAAM,EAANA,MAAM;UAAEC,eAAe,EAAfA;QAAgB,CAAC;MAC5C,CAAC;MAAA,SAtCKK,0BAA0BA,CAAA;QAAA,OAAAP,2BAAA,CAAAxF,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAA1BwJ,0BAA0B;IAAA;EAAA;IAAA5G,GAAA;IAAAC,KAAA;MAAA,IAAA4G,QAAA,GAAA1G,iBAAA,CA2ChC,aAA+B;QAAA5C,aAAA,GAAAgB,CAAA;QAE7B,IAAM2C,GAAG,IAAA3D,aAAA,GAAAI,CAAA,SAAGU,IAAI,CAAC6C,GAAG,CAAC,CAAC;QAAC3D,aAAA,GAAAI,CAAA;QACvB,SAAAmJ,IAAA,IAA2B,IAAI,CAACpJ,cAAc,CAACqJ,OAAO,CAAC,CAAC,EAAE;UAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAH,IAAA;UAAA,IAA9C9G,GAAG,GAAAgH,KAAA;UAAA,IAAE5F,KAAK,GAAA4F,KAAA;UAAAzJ,aAAA,GAAAI,CAAA;UACpB,IAAIyD,KAAK,CAACD,WAAW,GAAGD,GAAG,GAAG,IAAI,CAAC/D,MAAM,CAACqB,YAAY,CAACI,QAAQ,EAAE;YAAArB,aAAA,GAAAC,CAAA;YAAAD,aAAA,GAAAI,CAAA;YAC/D,IAAI,CAACD,cAAc,CAACwJ,MAAM,CAAClH,GAAG,CAAC;UACjC,CAAC;YAAAzC,aAAA,GAAAC,CAAA;UAAA;QACH;QAACD,aAAA,GAAAI,CAAA;QAGD,IAAI,IAAI,CAACR,MAAM,CAACmC,YAAY,CAACX,OAAO,EAAE;UAAApB,aAAA,GAAAC,CAAA;UACpC,IAAM2J,UAAU,IAAA5J,aAAA,GAAAI,CAAA,SAAG,IAAIU,IAAI,CAAC,CAAC;UAACd,aAAA,GAAAI,CAAA;UAC9BwJ,UAAU,CAACC,OAAO,CAACD,UAAU,CAACE,OAAO,CAAC,CAAC,GAAG,IAAI,CAAClK,MAAM,CAACmC,YAAY,CAACE,aAAa,CAAC;UAACjC,aAAA,GAAAI,CAAA;UAElF,MAAMX,eAAe,CAAC8H,KAAK,CAAC,YAAY,EAAE,QAAQ,EAAE;YAClDwC,MAAM,EAAE;cACNzC,SAAS,EAAE;gBAAE0C,EAAE,EAAEJ,UAAU,CAAC7I,WAAW,CAAC;cAAE;YAC5C;UACF,CAAC,CAAC;QACJ,CAAC;UAAAf,aAAA,GAAAC,CAAA;QAAA;MACH,CAAC;MAAA,SApBKgK,OAAOA,CAAA;QAAA,OAAAX,QAAA,CAAAhG,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAAPoK,OAAO;IAAA;EAAA;IAAAxH,GAAA;IAAAC,KAAA,EAwBb,SAAQ4C,YAAYA,CAAC5C,KAAU,EAAE6C,IAAY,EAAW;MAAAvF,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MACtD,QAAQmF,IAAI;QACV,KAAK,QAAQ;UAAAvF,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACX,OAAO,OAAOsC,KAAK,KAAK,QAAQ;QAClC,KAAK,QAAQ;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACX,OAAO,CAAAJ,aAAA,GAAAC,CAAA,kBAAOyC,KAAK,KAAK,QAAQ,MAAA1C,aAAA,GAAAC,CAAA,WAAI,CAACiK,KAAK,CAACxH,KAAK,CAAC;QACnD,KAAK,OAAO;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACV,OAAO,CAAAJ,aAAA,GAAAC,CAAA,kBAAOyC,KAAK,KAAK,QAAQ,MAAA1C,aAAA,GAAAC,CAAA,WAAI,4BAA4B,CAAC0F,IAAI,CAACjD,KAAK,CAAC;QAC9E,KAAK,KAAK;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACR,OAAO,CAAAJ,aAAA,GAAAC,CAAA,kBAAOyC,KAAK,KAAK,QAAQ,MAAA1C,aAAA,GAAAC,CAAA,WAAI,gBAAgB,CAAC0F,IAAI,CAACjD,KAAK,CAAC;QAClE,KAAK,MAAM;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACT,OAAO,CAAAJ,aAAA,GAAAC,CAAA,kBAAOyC,KAAK,KAAK,QAAQ,MAAA1C,aAAA,GAAAC,CAAA,WAAI,iEAAiE,CAAC0F,IAAI,CAACjD,KAAK,CAAC;QACnH,KAAK,MAAM;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACT,OAAO,CAAC8J,KAAK,CAACpJ,IAAI,CAACqJ,KAAK,CAACzH,KAAK,CAAC,CAAC;QAClC;UAAA1C,aAAA,GAAAC,CAAA;UAAAD,aAAA,GAAAI,CAAA;UACE,OAAO,IAAI;MACf;IACF;EAAC;IAAAqC,GAAA;IAAAC,KAAA;MAAA,IAAA0H,qBAAA,GAAAxH,iBAAA,CAED,aAAoD;QAAA5C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QAElD8C,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD,CAAC;MAAA,SAHaL,oBAAoBA,CAAA;QAAA,OAAAsH,qBAAA,CAAA9G,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAApBiD,oBAAoB;IAAA;EAAA;IAAAL,GAAA;IAAAC,KAAA,EAKlC,SAAQJ,oBAAoBA,CAAA,EAAS;MAAAtC,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MAEnC,IAAI,CAACE,aAAa,GAAG,CAAAN,aAAA,GAAAC,CAAA,WAAAoK,OAAO,CAACC,GAAG,CAACC,cAAc,MAAAvK,aAAA,GAAAC,CAAA,WAAI,aAAa;IAClE;EAAC;IAAAwC,GAAA;IAAAC,KAAA;MAAA,IAAA8H,kBAAA,GAAA5H,iBAAA,CAED,aAAiD;QAAA5C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QAE/C8C,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;MACtC,CAAC;MAAA,SAHaJ,iBAAiBA,CAAA;QAAA,OAAAyH,kBAAA,CAAAlH,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAAjBkD,iBAAiB;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAK/B,SAAQM,uBAAuBA,CAAA,EAAS;MAAA,IAAAyH,KAAA;MAAAzK,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MAEtCsK,WAAW,CAAC,YAAM;QAAA1K,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QAChBqK,KAAI,CAACpB,0BAA0B,CAAC,CAAC,CAACsB,KAAK,CAACzH,OAAO,CAACE,KAAK,CAAC;MACxD,CAAC,EAAE,KAAK,CAAC;IACX;EAAC;IAAAX,GAAA;IAAAC,KAAA,EAED,SAAQH,iBAAiBA,CAAA,EAAS;MAAA,IAAAqI,MAAA;MAAA5K,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MAEhCsK,WAAW,CAAC,YAAM;QAAA1K,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QAChBwK,MAAI,CAACX,OAAO,CAAC,CAAC,CAACU,KAAK,CAACzH,OAAO,CAACE,KAAK,CAAC;MACrC,CAAC,EAAE,MAAM,CAAC;IACZ;EAAC;IAAAX,GAAA;IAAAC,KAAA;MAAA,IAAAmI,oBAAA,GAAAjI,iBAAA,CAED,aAAqD;QAAA5C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QAEnD,OAAO,EAAE;MACX,CAAC;MAAA,SAHa8I,mBAAmBA,CAAA;QAAA,OAAA2B,oBAAA,CAAAvH,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAAnBqJ,mBAAmB;IAAA;EAAA;IAAAzG,GAAA;IAAAC,KAAA;MAAA,IAAAoI,uBAAA,GAAAlI,iBAAA,CAKjC,aAAsD;QAAA5C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAI,CAAA;QAEpD8C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC5C,CAAC;MAAA,SAHagG,sBAAsBA,CAAA;QAAA,OAAA2B,uBAAA,CAAAxH,KAAA,OAAAzD,SAAA;MAAA;MAAA,OAAtBsJ,sBAAsB;IAAA;EAAA;IAAA1G,GAAA;IAAAC,KAAA,EAKpC,SAAQ2E,UAAUA,CAAA,EAAW;MAAArH,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAI,CAAA;MAC3B,OAAO,GAAGU,IAAI,CAAC6C,GAAG,CAAC,CAAC,IAAIgB,IAAI,CAACoG,MAAM,CAAC,CAAC,CAACvE,QAAQ,CAAC,EAAE,CAAC,CAACwE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACnE;EAAC;AAAA;AAIH,OAAO,IAAMC,eAAe,IAAAjL,aAAA,GAAAI,CAAA,SAAG,IAAIT,eAAe,CAAC,CAAC;AAEpD,eAAeA,eAAe", "ignoreList": []}