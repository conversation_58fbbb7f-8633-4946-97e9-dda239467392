bcfb2f4f34b32d3b9842bc5366dd5858
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2hr17ar071() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\monitoring\\UnifiedPerformanceMonitor.ts";
  var hash = "414eef700eb1d92bd2f739da69bca8088c51b820";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\monitoring\\UnifiedPerformanceMonitor.ts",
    statementMap: {
      "0": {
        start: {
          line: 86,
          column: 54
        },
        end: {
          line: 86,
          column: 58
        }
      },
      "1": {
        start: {
          line: 91,
          column: 37
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "2": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 114,
          column: 6
        }
      },
      "3": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 116,
          column: 44
        }
      },
      "4": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 117,
          column: 45
        }
      },
      "5": {
        start: {
          line: 118,
          column: 4
        },
        end: {
          line: 118,
          column: 70
        }
      },
      "6": {
        start: {
          line: 119,
          column: 4
        },
        end: {
          line: 119,
          column: 57
        }
      },
      "7": {
        start: {
          line: 121,
          column: 4
        },
        end: {
          line: 121,
          column: 32
        }
      },
      "8": {
        start: {
          line: 128,
          column: 4
        },
        end: {
          line: 147,
          column: 5
        }
      },
      "9": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 132,
          column: 7
        }
      },
      "10": {
        start: {
          line: 131,
          column: 8
        },
        end: {
          line: 131,
          column: 39
        }
      },
      "11": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 137,
          column: 7
        }
      },
      "12": {
        start: {
          line: 136,
          column: 8
        },
        end: {
          line: 136,
          column: 46
        }
      },
      "13": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 142,
          column: 7
        }
      },
      "14": {
        start: {
          line: 141,
          column: 8
        },
        end: {
          line: 141,
          column: 47
        }
      },
      "15": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 74
        }
      },
      "16": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 80
        }
      },
      "17": {
        start: {
          line: 154,
          column: 4
        },
        end: {
          line: 178,
          column: 5
        }
      },
      "18": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 38
        }
      },
      "19": {
        start: {
          line: 159,
          column: 6
        },
        end: {
          line: 159,
          column: 37
        }
      },
      "20": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 41
        }
      },
      "21": {
        start: {
          line: 165,
          column: 6
        },
        end: {
          line: 167,
          column: 7
        }
      },
      "22": {
        start: {
          line: 166,
          column: 8
        },
        end: {
          line: 166,
          column: 41
        }
      },
      "23": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 172,
          column: 7
        }
      },
      "24": {
        start: {
          line: 171,
          column: 8
        },
        end: {
          line: 171,
          column: 33
        }
      },
      "25": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 33
        }
      },
      "26": {
        start: {
          line: 176,
          column: 6
        },
        end: {
          line: 176,
          column: 61
        }
      },
      "27": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 26
        }
      },
      "28": {
        start: {
          line: 204,
          column: 21
        },
        end: {
          line: 204,
          column: 50
        }
      },
      "29": {
        start: {
          line: 205,
          column: 26
        },
        end: {
          line: 205,
          column: 72
        }
      },
      "30": {
        start: {
          line: 206,
          column: 25
        },
        end: {
          line: 206,
          column: 58
        }
      },
      "31": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 212,
          column: 6
        }
      },
      "32": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 219,
          column: 78
        }
      },
      "33": {
        start: {
          line: 226,
          column: 17
        },
        end: {
          line: 231,
          column: 5
        }
      },
      "34": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 242,
          column: 5
        }
      },
      "35": {
        start: {
          line: 235,
          column: 8
        },
        end: {
          line: 235,
          column: 45
        }
      },
      "36": {
        start: {
          line: 237,
          column: 8
        },
        end: {
          line: 237,
          column: 39
        }
      },
      "37": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 239,
          column: 41
        }
      },
      "38": {
        start: {
          line: 241,
          column: 8
        },
        end: {
          line: 241,
          column: 45
        }
      },
      "39": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 248,
          column: 26
        }
      },
      "40": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 314,
          column: 6
        }
      },
      "41": {
        start: {
          line: 318,
          column: 16
        },
        end: {
          line: 318,
          column: 26
        }
      },
      "42": {
        start: {
          line: 321,
          column: 26
        },
        end: {
          line: 321,
          column: 55
        }
      },
      "43": {
        start: {
          line: 322,
          column: 4
        },
        end: {
          line: 322,
          column: 55
        }
      },
      "44": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 323,
          column: 103
        }
      },
      "45": {
        start: {
          line: 324,
          column: 4
        },
        end: {
          line: 324,
          column: 49
        }
      },
      "46": {
        start: {
          line: 327,
          column: 26
        },
        end: {
          line: 327,
          column: 55
        }
      },
      "47": {
        start: {
          line: 328,
          column: 4
        },
        end: {
          line: 328,
          column: 55
        }
      },
      "48": {
        start: {
          line: 329,
          column: 4
        },
        end: {
          line: 329,
          column: 103
        }
      },
      "49": {
        start: {
          line: 330,
          column: 4
        },
        end: {
          line: 330,
          column: 49
        }
      },
      "50": {
        start: {
          line: 333,
          column: 27
        },
        end: {
          line: 333,
          column: 57
        }
      },
      "51": {
        start: {
          line: 334,
          column: 4
        },
        end: {
          line: 334,
          column: 57
        }
      },
      "52": {
        start: {
          line: 335,
          column: 4
        },
        end: {
          line: 335,
          column: 106
        }
      },
      "53": {
        start: {
          line: 336,
          column: 4
        },
        end: {
          line: 336,
          column: 50
        }
      },
      "54": {
        start: {
          line: 339,
          column: 27
        },
        end: {
          line: 339,
          column: 57
        }
      },
      "55": {
        start: {
          line: 340,
          column: 4
        },
        end: {
          line: 340,
          column: 57
        }
      },
      "56": {
        start: {
          line: 341,
          column: 4
        },
        end: {
          line: 341,
          column: 106
        }
      },
      "57": {
        start: {
          line: 342,
          column: 4
        },
        end: {
          line: 342,
          column: 50
        }
      },
      "58": {
        start: {
          line: 345,
          column: 27
        },
        end: {
          line: 345,
          column: 57
        }
      },
      "59": {
        start: {
          line: 346,
          column: 4
        },
        end: {
          line: 346,
          column: 57
        }
      },
      "60": {
        start: {
          line: 347,
          column: 4
        },
        end: {
          line: 347,
          column: 106
        }
      },
      "61": {
        start: {
          line: 348,
          column: 4
        },
        end: {
          line: 348,
          column: 50
        }
      },
      "62": {
        start: {
          line: 353,
          column: 4
        },
        end: {
          line: 359,
          column: 6
        }
      },
      "63": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 370,
          column: 6
        }
      },
      "64": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 381,
          column: 6
        }
      },
      "65": {
        start: {
          line: 386,
          column: 4
        },
        end: {
          line: 392,
          column: 6
        }
      },
      "66": {
        start: {
          line: 397,
          column: 4
        },
        end: {
          line: 403,
          column: 6
        }
      },
      "67": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 430,
          column: 5
        }
      },
      "68": {
        start: {
          line: 410,
          column: 34
        },
        end: {
          line: 410,
          column: 80
        }
      },
      "69": {
        start: {
          line: 411,
          column: 36
        },
        end: {
          line: 411,
          column: 74
        }
      },
      "70": {
        start: {
          line: 412,
          column: 34
        },
        end: {
          line: 412,
          column: 72
        }
      },
      "71": {
        start: {
          line: 413,
          column: 8
        },
        end: {
          line: 413,
          column: 87
        }
      },
      "72": {
        start: {
          line: 416,
          column: 8
        },
        end: {
          line: 416,
          column: 104
        }
      },
      "73": {
        start: {
          line: 419,
          column: 8
        },
        end: {
          line: 419,
          column: 104
        }
      },
      "74": {
        start: {
          line: 422,
          column: 35
        },
        end: {
          line: 422,
          column: 78
        }
      },
      "75": {
        start: {
          line: 423,
          column: 8
        },
        end: {
          line: 423,
          column: 93
        }
      },
      "76": {
        start: {
          line: 426,
          column: 8
        },
        end: {
          line: 426,
          column: 108
        }
      },
      "77": {
        start: {
          line: 429,
          column: 8
        },
        end: {
          line: 429,
          column: 19
        }
      },
      "78": {
        start: {
          line: 434,
          column: 19
        },
        end: {
          line: 434,
          column: 53
        }
      },
      "79": {
        start: {
          line: 437,
          column: 29
        },
        end: {
          line: 440,
          column: 9
        }
      },
      "80": {
        start: {
          line: 438,
          column: 26
        },
        end: {
          line: 438,
          column: 119
        }
      },
      "81": {
        start: {
          line: 439,
          column: 6
        },
        end: {
          line: 439,
          column: 31
        }
      },
      "82": {
        start: {
          line: 443,
          column: 29
        },
        end: {
          line: 446,
          column: 9
        }
      },
      "83": {
        start: {
          line: 444,
          column: 21
        },
        end: {
          line: 444,
          column: 44
        }
      },
      "84": {
        start: {
          line: 445,
          column: 6
        },
        end: {
          line: 445,
          column: 56
        }
      },
      "85": {
        start: {
          line: 449,
          column: 30
        },
        end: {
          line: 449,
          column: 73
        }
      },
      "86": {
        start: {
          line: 452,
          column: 24
        },
        end: {
          line: 452,
          column: 83
        }
      },
      "87": {
        start: {
          line: 452,
          column: 46
        },
        end: {
          line: 452,
          column: 71
        }
      },
      "88": {
        start: {
          line: 454,
          column: 4
        },
        end: {
          line: 459,
          column: 6
        }
      },
      "89": {
        start: {
          line: 464,
          column: 4
        },
        end: {
          line: 472,
          column: 6
        }
      },
      "90": {
        start: {
          line: 476,
          column: 25
        },
        end: {
          line: 476,
          column: 62
        }
      },
      "91": {
        start: {
          line: 477,
          column: 16
        },
        end: {
          line: 477,
          column: 26
        }
      },
      "92": {
        start: {
          line: 480,
          column: 4
        },
        end: {
          line: 480,
          column: 77
        }
      },
      "93": {
        start: {
          line: 481,
          column: 4
        },
        end: {
          line: 483,
          column: 5
        }
      },
      "94": {
        start: {
          line: 482,
          column: 6
        },
        end: {
          line: 482,
          column: 41
        }
      },
      "95": {
        start: {
          line: 486,
          column: 4
        },
        end: {
          line: 492,
          column: 5
        }
      },
      "96": {
        start: {
          line: 487,
          column: 27
        },
        end: {
          line: 487,
          column: 103
        }
      },
      "97": {
        start: {
          line: 487,
          column: 77
        },
        end: {
          line: 487,
          column: 94
        }
      },
      "98": {
        start: {
          line: 488,
          column: 6
        },
        end: {
          line: 488,
          column: 78
        }
      },
      "99": {
        start: {
          line: 489,
          column: 6
        },
        end: {
          line: 491,
          column: 7
        }
      },
      "100": {
        start: {
          line: 490,
          column: 8
        },
        end: {
          line: 490,
          column: 42
        }
      },
      "101": {
        start: {
          line: 495,
          column: 4
        },
        end: {
          line: 501,
          column: 5
        }
      },
      "102": {
        start: {
          line: 496,
          column: 28
        },
        end: {
          line: 496,
          column: 112
        }
      },
      "103": {
        start: {
          line: 496,
          column: 87
        },
        end: {
          line: 496,
          column: 104
        }
      },
      "104": {
        start: {
          line: 497,
          column: 6
        },
        end: {
          line: 497,
          column: 80
        }
      },
      "105": {
        start: {
          line: 498,
          column: 6
        },
        end: {
          line: 500,
          column: 7
        }
      },
      "106": {
        start: {
          line: 499,
          column: 8
        },
        end: {
          line: 499,
          column: 43
        }
      },
      "107": {
        start: {
          line: 505,
          column: 19
        },
        end: {
          line: 505,
          column: 68
        }
      },
      "108": {
        start: {
          line: 506,
          column: 4
        },
        end: {
          line: 506,
          column: 40
        }
      },
      "109": {
        start: {
          line: 509,
          column: 4
        },
        end: {
          line: 511,
          column: 5
        }
      },
      "110": {
        start: {
          line: 510,
          column: 6
        },
        end: {
          line: 510,
          column: 40
        }
      },
      "111": {
        start: {
          line: 516,
          column: 21
        },
        end: {
          line: 516,
          column: 23
        }
      },
      "112": {
        start: {
          line: 518,
          column: 4
        },
        end: {
          line: 525,
          column: 5
        }
      },
      "113": {
        start: {
          line: 519,
          column: 6
        },
        end: {
          line: 524,
          column: 9
        }
      },
      "114": {
        start: {
          line: 527,
          column: 4
        },
        end: {
          line: 534,
          column: 5
        }
      },
      "115": {
        start: {
          line: 528,
          column: 6
        },
        end: {
          line: 533,
          column: 9
        }
      },
      "116": {
        start: {
          line: 536,
          column: 4
        },
        end: {
          line: 536,
          column: 20
        }
      },
      "117": {
        start: {
          line: 541,
          column: 26
        },
        end: {
          line: 541,
          column: 28
        }
      },
      "118": {
        start: {
          line: 544,
          column: 4
        },
        end: {
          line: 554,
          column: 7
        }
      },
      "119": {
        start: {
          line: 545,
          column: 24
        },
        end: {
          line: 545,
          column: 76
        }
      },
      "120": {
        start: {
          line: 546,
          column: 6
        },
        end: {
          line: 553,
          column: 7
        }
      },
      "121": {
        start: {
          line: 547,
          column: 8
        },
        end: {
          line: 552,
          column: 11
        }
      },
      "122": {
        start: {
          line: 556,
          column: 4
        },
        end: {
          line: 556,
          column: 25
        }
      },
      "123": {
        start: {
          line: 560,
          column: 48
        },
        end: {
          line: 560,
          column: 50
        }
      },
      "124": {
        start: {
          line: 561,
          column: 37
        },
        end: {
          line: 561,
          column: 39
        }
      },
      "125": {
        start: {
          line: 562,
          column: 38
        },
        end: {
          line: 562,
          column: 40
        }
      },
      "126": {
        start: {
          line: 565,
          column: 4
        },
        end: {
          line: 573,
          column: 7
        }
      },
      "127": {
        start: {
          line: 566,
          column: 21
        },
        end: {
          line: 566,
          column: 88
        }
      },
      "128": {
        start: {
          line: 567,
          column: 6
        },
        end: {
          line: 567,
          column: 37
        }
      },
      "129": {
        start: {
          line: 569,
          column: 6
        },
        end: {
          line: 572,
          column: 7
        }
      },
      "130": {
        start: {
          line: 570,
          column: 8
        },
        end: {
          line: 570,
          column: 65
        }
      },
      "131": {
        start: {
          line: 571,
          column: 8
        },
        end: {
          line: 571,
          column: 75
        }
      },
      "132": {
        start: {
          line: 575,
          column: 4
        },
        end: {
          line: 580,
          column: 6
        }
      },
      "133": {
        start: {
          line: 584,
          column: 4
        },
        end: {
          line: 586,
          column: 35
        }
      },
      "134": {
        start: {
          line: 585,
          column: 6
        },
        end: {
          line: 585,
          column: 37
        }
      },
      "135": {
        start: {
          line: 591,
          column: 4
        },
        end: {
          line: 591,
          column: 44
        }
      },
      "136": {
        start: {
          line: 596,
          column: 4
        },
        end: {
          line: 596,
          column: 46
        }
      },
      "137": {
        start: {
          line: 605,
          column: 4
        },
        end: {
          line: 605,
          column: 46
        }
      },
      "138": {
        start: {
          line: 616,
          column: 4
        },
        end: {
          line: 616,
          column: 33
        }
      },
      "139": {
        start: {
          line: 620,
          column: 19
        },
        end: {
          line: 620,
          column: 21
        }
      },
      "140": {
        start: {
          line: 623,
          column: 4
        },
        end: {
          line: 632,
          column: 5
        }
      },
      "141": {
        start: {
          line: 624,
          column: 6
        },
        end: {
          line: 631,
          column: 9
        }
      },
      "142": {
        start: {
          line: 634,
          column: 4
        },
        end: {
          line: 634,
          column: 18
        }
      },
      "143": {
        start: {
          line: 643,
          column: 4
        },
        end: {
          line: 643,
          column: 48
        }
      },
      "144": {
        start: {
          line: 647,
          column: 22
        },
        end: {
          line: 647,
          column: 45
        }
      },
      "145": {
        start: {
          line: 648,
          column: 4
        },
        end: {
          line: 648,
          column: 76
        }
      },
      "146": {
        start: {
          line: 649,
          column: 4
        },
        end: {
          line: 649,
          column: 21
        }
      },
      "147": {
        start: {
          line: 654,
          column: 41
        },
        end: {
          line: 654,
          column: 72
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 99,
            column: 2
          },
          end: {
            line: 99,
            column: 3
          }
        },
        loc: {
          start: {
            line: 99,
            column: 54
          },
          end: {
            line: 122,
            column: 3
          }
        },
        line: 99
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 127,
            column: 2
          },
          end: {
            line: 127,
            column: 3
          }
        },
        loc: {
          start: {
            line: 127,
            column: 54
          },
          end: {
            line: 148,
            column: 3
          }
        },
        line: 127
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 153,
            column: 2
          },
          end: {
            line: 153,
            column: 3
          }
        },
        loc: {
          start: {
            line: 153,
            column: 53
          },
          end: {
            line: 179,
            column: 3
          }
        },
        line: 153
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 184,
            column: 2
          },
          end: {
            line: 184,
            column: 3
          }
        },
        loc: {
          start: {
            line: 203,
            column: 5
          },
          end: {
            line: 213,
            column: 3
          }
        },
        line: 203
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 218,
            column: 2
          },
          end: {
            line: 218,
            column: 3
          }
        },
        loc: {
          start: {
            line: 218,
            column: 94
          },
          end: {
            line: 220,
            column: 3
          }
        },
        line: 218
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 225,
            column: 2
          },
          end: {
            line: 225,
            column: 3
          }
        },
        loc: {
          start: {
            line: 225,
            column: 89
          },
          end: {
            line: 243,
            column: 3
          }
        },
        line: 225
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 247,
            column: 2
          },
          end: {
            line: 247,
            column: 3
          }
        },
        loc: {
          start: {
            line: 247,
            column: 46
          },
          end: {
            line: 315,
            column: 3
          }
        },
        line: 247
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 317,
            column: 2
          },
          end: {
            line: 317,
            column: 3
          }
        },
        loc: {
          start: {
            line: 317,
            column: 52
          },
          end: {
            line: 349,
            column: 3
          }
        },
        line: 317
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 351,
            column: 2
          },
          end: {
            line: 351,
            column: 3
          }
        },
        loc: {
          start: {
            line: 351,
            column: 49
          },
          end: {
            line: 360,
            column: 3
          }
        },
        line: 351
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 362,
            column: 2
          },
          end: {
            line: 362,
            column: 3
          }
        },
        loc: {
          start: {
            line: 362,
            column: 49
          },
          end: {
            line: 371,
            column: 3
          }
        },
        line: 362
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 373,
            column: 50
          },
          end: {
            line: 382,
            column: 3
          }
        },
        line: 373
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 384,
            column: 2
          },
          end: {
            line: 384,
            column: 3
          }
        },
        loc: {
          start: {
            line: 384,
            column: 50
          },
          end: {
            line: 393,
            column: 3
          }
        },
        line: 384
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 395,
            column: 2
          },
          end: {
            line: 395,
            column: 3
          }
        },
        loc: {
          start: {
            line: 395,
            column: 50
          },
          end: {
            line: 404,
            column: 3
          }
        },
        line: 395
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 406,
            column: 2
          },
          end: {
            line: 406,
            column: 3
          }
        },
        loc: {
          start: {
            line: 406,
            column: 67
          },
          end: {
            line: 431,
            column: 3
          }
        },
        line: 406
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 433,
            column: 2
          },
          end: {
            line: 433,
            column: 3
          }
        },
        loc: {
          start: {
            line: 433,
            column: 42
          },
          end: {
            line: 460,
            column: 3
          }
        },
        line: 433
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 437,
            column: 43
          },
          end: {
            line: 437,
            column: 44
          }
        },
        loc: {
          start: {
            line: 437,
            column: 59
          },
          end: {
            line: 440,
            column: 5
          }
        },
        line: 437
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 443,
            column: 43
          },
          end: {
            line: 443,
            column: 44
          }
        },
        loc: {
          start: {
            line: 443,
            column: 66
          },
          end: {
            line: 446,
            column: 5
          }
        },
        line: 443
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 452,
            column: 37
          },
          end: {
            line: 452,
            column: 38
          }
        },
        loc: {
          start: {
            line: 452,
            column: 46
          },
          end: {
            line: 452,
            column: 71
          }
        },
        line: 452
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 462,
            column: 2
          },
          end: {
            line: 462,
            column: 3
          }
        },
        loc: {
          start: {
            line: 462,
            column: 55
          },
          end: {
            line: 473,
            column: 3
          }
        },
        line: 462
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 475,
            column: 2
          },
          end: {
            line: 475,
            column: 3
          }
        },
        loc: {
          start: {
            line: 475,
            column: 53
          },
          end: {
            line: 502,
            column: 3
          }
        },
        line: 475
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 487,
            column: 61
          },
          end: {
            line: 487,
            column: 62
          }
        },
        loc: {
          start: {
            line: 487,
            column: 77
          },
          end: {
            line: 487,
            column: 94
          }
        },
        line: 487
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 496,
            column: 71
          },
          end: {
            line: 496,
            column: 72
          }
        },
        loc: {
          start: {
            line: 496,
            column: 87
          },
          end: {
            line: 496,
            column: 104
          }
        },
        line: 496
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 504,
            column: 2
          },
          end: {
            line: 504,
            column: 3
          }
        },
        loc: {
          start: {
            line: 504,
            column: 45
          },
          end: {
            line: 512,
            column: 3
          }
        },
        line: 504
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 514,
            column: 2
          },
          end: {
            line: 514,
            column: 3
          }
        },
        loc: {
          start: {
            line: 514,
            column: 51
          },
          end: {
            line: 537,
            column: 3
          }
        },
        line: 514
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 539,
            column: 2
          },
          end: {
            line: 539,
            column: 3
          }
        },
        loc: {
          start: {
            line: 539,
            column: 68
          },
          end: {
            line: 557,
            column: 3
          }
        },
        line: 539
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 544,
            column: 48
          },
          end: {
            line: 544,
            column: 49
          }
        },
        loc: {
          start: {
            line: 544,
            column: 71
          },
          end: {
            line: 554,
            column: 5
          }
        },
        line: 544
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 559,
            column: 2
          },
          end: {
            line: 559,
            column: 3
          }
        },
        loc: {
          start: {
            line: 559,
            column: 53
          },
          end: {
            line: 581,
            column: 3
          }
        },
        line: 559
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 565,
            column: 48
          },
          end: {
            line: 565,
            column: 49
          }
        },
        loc: {
          start: {
            line: 565,
            column: 71
          },
          end: {
            line: 573,
            column: 5
          }
        },
        line: 565
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 583,
            column: 2
          },
          end: {
            line: 583,
            column: 3
          }
        },
        loc: {
          start: {
            line: 583,
            column: 42
          },
          end: {
            line: 587,
            column: 3
          }
        },
        line: 583
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 584,
            column: 42
          },
          end: {
            line: 584,
            column: 43
          }
        },
        loc: {
          start: {
            line: 584,
            column: 54
          },
          end: {
            line: 586,
            column: 5
          }
        },
        line: 584
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 589,
            column: 2
          },
          end: {
            line: 589,
            column: 3
          }
        },
        loc: {
          start: {
            line: 589,
            column: 42
          },
          end: {
            line: 592,
            column: 3
          }
        },
        line: 589
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 594,
            column: 2
          },
          end: {
            line: 594,
            column: 3
          }
        },
        loc: {
          start: {
            line: 594,
            column: 44
          },
          end: {
            line: 597,
            column: 3
          }
        },
        line: 594
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 604,
            column: 2
          },
          end: {
            line: 604,
            column: 3
          }
        },
        loc: {
          start: {
            line: 604,
            column: 36
          },
          end: {
            line: 606,
            column: 3
          }
        },
        line: 604
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 615,
            column: 2
          },
          end: {
            line: 615,
            column: 3
          }
        },
        loc: {
          start: {
            line: 615,
            column: 31
          },
          end: {
            line: 617,
            column: 3
          }
        },
        line: 615
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 619,
            column: 2
          },
          end: {
            line: 619,
            column: 3
          }
        },
        loc: {
          start: {
            line: 619,
            column: 61
          },
          end: {
            line: 635,
            column: 3
          }
        },
        line: 619
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 642,
            column: 2
          },
          end: {
            line: 642,
            column: 3
          }
        },
        loc: {
          start: {
            line: 642,
            column: 31
          },
          end: {
            line: 644,
            column: 3
          }
        },
        line: 642
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 646,
            column: 2
          },
          end: {
            line: 646,
            column: 3
          }
        },
        loc: {
          start: {
            line: 646,
            column: 77
          },
          end: {
            line: 650,
            column: 3
          }
        },
        line: 646
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 99,
            column: 14
          },
          end: {
            line: 99,
            column: 52
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 99,
            column: 50
          },
          end: {
            line: 99,
            column: 52
          }
        }],
        line: 99
      },
      "1": {
        loc: {
          start: {
            line: 130,
            column: 6
          },
          end: {
            line: 132,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 6
          },
          end: {
            line: 132,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "2": {
        loc: {
          start: {
            line: 135,
            column: 6
          },
          end: {
            line: 137,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 135,
            column: 6
          },
          end: {
            line: 137,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 135
      },
      "3": {
        loc: {
          start: {
            line: 140,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 140,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 140
      },
      "4": {
        loc: {
          start: {
            line: 165,
            column: 6
          },
          end: {
            line: 167,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 165,
            column: 6
          },
          end: {
            line: 167,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 165
      },
      "5": {
        loc: {
          start: {
            line: 170,
            column: 6
          },
          end: {
            line: 172,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 6
          },
          end: {
            line: 172,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "6": {
        loc: {
          start: {
            line: 218,
            column: 51
          },
          end: {
            line: 218,
            column: 75
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 218,
            column: 70
          },
          end: {
            line: 218,
            column: 75
          }
        }],
        line: 218
      },
      "7": {
        loc: {
          start: {
            line: 225,
            column: 29
          },
          end: {
            line: 225,
            column: 70
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 225,
            column: 64
          },
          end: {
            line: 225,
            column: 70
          }
        }],
        line: 225
      },
      "8": {
        loc: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 242,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 234,
            column: 6
          },
          end: {
            line: 235,
            column: 45
          }
        }, {
          start: {
            line: 236,
            column: 6
          },
          end: {
            line: 237,
            column: 39
          }
        }, {
          start: {
            line: 238,
            column: 6
          },
          end: {
            line: 239,
            column: 41
          }
        }, {
          start: {
            line: 240,
            column: 6
          },
          end: {
            line: 241,
            column: 45
          }
        }],
        line: 233
      },
      "9": {
        loc: {
          start: {
            line: 408,
            column: 4
          },
          end: {
            line: 430,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 409,
            column: 6
          },
          end: {
            line: 413,
            column: 87
          }
        }, {
          start: {
            line: 415,
            column: 6
          },
          end: {
            line: 416,
            column: 104
          }
        }, {
          start: {
            line: 418,
            column: 6
          },
          end: {
            line: 419,
            column: 104
          }
        }, {
          start: {
            line: 421,
            column: 6
          },
          end: {
            line: 423,
            column: 93
          }
        }, {
          start: {
            line: 425,
            column: 6
          },
          end: {
            line: 426,
            column: 108
          }
        }, {
          start: {
            line: 428,
            column: 6
          },
          end: {
            line: 429,
            column: 19
          }
        }],
        line: 408
      },
      "10": {
        loc: {
          start: {
            line: 444,
            column: 21
          },
          end: {
            line: 444,
            column: 44
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 444,
            column: 33
          },
          end: {
            line: 444,
            column: 37
          }
        }, {
          start: {
            line: 444,
            column: 40
          },
          end: {
            line: 444,
            column: 44
          }
        }],
        line: 444
      },
      "11": {
        loc: {
          start: {
            line: 452,
            column: 24
          },
          end: {
            line: 452,
            column: 83
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 452,
            column: 75
          },
          end: {
            line: 452,
            column: 78
          }
        }, {
          start: {
            line: 452,
            column: 81
          },
          end: {
            line: 452,
            column: 83
          }
        }],
        line: 452
      },
      "12": {
        loc: {
          start: {
            line: 481,
            column: 4
          },
          end: {
            line: 483,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 481,
            column: 4
          },
          end: {
            line: 483,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 481
      },
      "13": {
        loc: {
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 492,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 492,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 486
      },
      "14": {
        loc: {
          start: {
            line: 489,
            column: 6
          },
          end: {
            line: 491,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 489,
            column: 6
          },
          end: {
            line: 491,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 489
      },
      "15": {
        loc: {
          start: {
            line: 495,
            column: 4
          },
          end: {
            line: 501,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 495,
            column: 4
          },
          end: {
            line: 501,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 495
      },
      "16": {
        loc: {
          start: {
            line: 498,
            column: 6
          },
          end: {
            line: 500,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 498,
            column: 6
          },
          end: {
            line: 500,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 498
      },
      "17": {
        loc: {
          start: {
            line: 509,
            column: 4
          },
          end: {
            line: 511,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 509,
            column: 4
          },
          end: {
            line: 511,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 509
      },
      "18": {
        loc: {
          start: {
            line: 518,
            column: 4
          },
          end: {
            line: 525,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 518,
            column: 4
          },
          end: {
            line: 525,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 518
      },
      "19": {
        loc: {
          start: {
            line: 527,
            column: 4
          },
          end: {
            line: 534,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 527,
            column: 4
          },
          end: {
            line: 534,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 527
      },
      "20": {
        loc: {
          start: {
            line: 546,
            column: 6
          },
          end: {
            line: 553,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 546,
            column: 6
          },
          end: {
            line: 553,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 546
      },
      "21": {
        loc: {
          start: {
            line: 551,
            column: 18
          },
          end: {
            line: 551,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 551,
            column: 35
          },
          end: {
            line: 551,
            column: 41
          }
        }, {
          start: {
            line: 551,
            column: 44
          },
          end: {
            line: 551,
            column: 77
          }
        }],
        line: 551
      },
      "22": {
        loc: {
          start: {
            line: 551,
            column: 44
          },
          end: {
            line: 551,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 551,
            column: 61
          },
          end: {
            line: 551,
            column: 69
          }
        }, {
          start: {
            line: 551,
            column: 72
          },
          end: {
            line: 551,
            column: 77
          }
        }],
        line: 551
      },
      "23": {
        loc: {
          start: {
            line: 566,
            column: 21
          },
          end: {
            line: 566,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 566,
            column: 49
          },
          end: {
            line: 566,
            column: 52
          }
        }, {
          start: {
            line: 566,
            column: 55
          },
          end: {
            line: 566,
            column: 88
          }
        }],
        line: 566
      },
      "24": {
        loc: {
          start: {
            line: 566,
            column: 55
          },
          end: {
            line: 566,
            column: 88
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 566,
            column: 82
          },
          end: {
            line: 566,
            column: 83
          }
        }, {
          start: {
            line: 566,
            column: 86
          },
          end: {
            line: 566,
            column: 88
          }
        }],
        line: 566
      },
      "25": {
        loc: {
          start: {
            line: 569,
            column: 6
          },
          end: {
            line: 572,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 569,
            column: 6
          },
          end: {
            line: 572,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 569
      },
      "26": {
        loc: {
          start: {
            line: 623,
            column: 4
          },
          end: {
            line: 632,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 623,
            column: 4
          },
          end: {
            line: 632,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 623
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0],
      "7": [0],
      "8": [0, 0, 0, 0],
      "9": [0, 0, 0, 0, 0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "414eef700eb1d92bd2f739da69bca8088c51b820"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2hr17ar071 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2hr17ar071();
var UnifiedPerformanceMonitor = function () {
  function UnifiedPerformanceMonitor() {
    var config = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2hr17ar071().b[0][0]++, {});
    _classCallCheck(this, UnifiedPerformanceMonitor);
    this.monitoringInterval = (cov_2hr17ar071().s[0]++, null);
    this.PHASE_BASELINES = (cov_2hr17ar071().s[1]++, {
      phase1: {
        bundleSize: 1024000,
        loadTime: 3000,
        renderTime: 100
      },
      phase2: {
        cacheHitRate: 60,
        offlineCapability: 0,
        imageOptimization: 0
      },
      phase3a: {
        aiAccuracy: 0,
        predictionSpeed: 0,
        adaptationRate: 0
      },
      phase3b: {
        globalLatency: 3000,
        edgeHitRate: 0,
        cdnEfficiency: 0
      },
      phase3c: {
        gpuUtilization: 0,
        nativePerformance: 0,
        memoryEfficiency: 60
      }
    });
    cov_2hr17ar071().f[0]++;
    cov_2hr17ar071().s[2]++;
    this.config = Object.assign({
      enableRealTimeTracking: true,
      enableTrendAnalysis: true,
      enableAlerts: true,
      enablePerformanceProfiling: true,
      updateInterval: 10000,
      retentionPeriod: 30,
      alertThresholds: {
        performanceDropThreshold: 10,
        memoryUsageThreshold: 80,
        errorRateThreshold: 5,
        latencyThreshold: 1000
      }
    }, config);
    cov_2hr17ar071().s[3]++;
    this.metrics = this.initializeMetrics();
    cov_2hr17ar071().s[4]++;
    this.trendAnalyzer = new TrendAnalyzer();
    cov_2hr17ar071().s[5]++;
    this.alertManager = new AlertManager(this.config.alertThresholds);
    cov_2hr17ar071().s[6]++;
    this.performanceProfiler = new PerformanceProfiler();
    cov_2hr17ar071().s[7]++;
    this.initializeMonitoring();
  }
  return _createClass(UnifiedPerformanceMonitor, [{
    key: "initializeMonitoring",
    value: (function () {
      var _initializeMonitoring = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[1]++;
        cov_2hr17ar071().s[8]++;
        try {
          cov_2hr17ar071().s[9]++;
          if (this.config.enableRealTimeTracking) {
            cov_2hr17ar071().b[1][0]++;
            cov_2hr17ar071().s[10]++;
            this.startRealTimeMonitoring();
          } else {
            cov_2hr17ar071().b[1][1]++;
          }
          cov_2hr17ar071().s[11]++;
          if (this.config.enableTrendAnalysis) {
            cov_2hr17ar071().b[2][0]++;
            cov_2hr17ar071().s[12]++;
            yield this.trendAnalyzer.initialize();
          } else {
            cov_2hr17ar071().b[2][1]++;
          }
          cov_2hr17ar071().s[13]++;
          if (this.config.enablePerformanceProfiling) {
            cov_2hr17ar071().b[3][0]++;
            cov_2hr17ar071().s[14]++;
            yield this.performanceProfiler.start();
          } else {
            cov_2hr17ar071().b[3][1]++;
          }
          cov_2hr17ar071().s[15]++;
          console.log('Unified Performance Monitor initialized successfully');
        } catch (error) {
          cov_2hr17ar071().s[16]++;
          console.error('Failed to initialize Unified Performance Monitor:', error);
        }
      });
      function initializeMonitoring() {
        return _initializeMonitoring.apply(this, arguments);
      }
      return initializeMonitoring;
    }())
  }, {
    key: "getUnifiedMetrics",
    value: (function () {
      var _getUnifiedMetrics = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[2]++;
        cov_2hr17ar071().s[17]++;
        try {
          cov_2hr17ar071().s[18]++;
          yield this.updatePhaseMetrics();
          cov_2hr17ar071().s[19]++;
          this.calculateOverallMetrics();
          cov_2hr17ar071().s[20]++;
          yield this.updateRealTimeMetrics();
          cov_2hr17ar071().s[21]++;
          if (this.config.enableTrendAnalysis) {
            cov_2hr17ar071().b[4][0]++;
            cov_2hr17ar071().s[22]++;
            yield this.updateTrendAnalysis();
          } else {
            cov_2hr17ar071().b[4][1]++;
          }
          cov_2hr17ar071().s[23]++;
          if (this.config.enableAlerts) {
            cov_2hr17ar071().b[5][0]++;
            cov_2hr17ar071().s[24]++;
            yield this.checkAlerts();
          } else {
            cov_2hr17ar071().b[5][1]++;
          }
          cov_2hr17ar071().s[25]++;
          return Object.assign({}, this.metrics);
        } catch (error) {
          cov_2hr17ar071().s[26]++;
          console.error('Failed to get unified metrics:', error);
          cov_2hr17ar071().s[27]++;
          return this.metrics;
        }
      });
      function getUnifiedMetrics() {
        return _getUnifiedMetrics.apply(this, arguments);
      }
      return getUnifiedMetrics;
    }())
  }, {
    key: "getPerformanceInsights",
    value: (function () {
      var _getPerformanceInsights = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[3]++;
        var insights = (cov_2hr17ar071().s[28]++, yield this.generateInsights());
        var opportunities = (cov_2hr17ar071().s[29]++, yield this.identifyOptimizationOpportunities());
        var healthReport = (cov_2hr17ar071().s[30]++, yield this.generateHealthReport());
        cov_2hr17ar071().s[31]++;
        return {
          insights: insights,
          optimizationOpportunities: opportunities,
          healthReport: healthReport
        };
      });
      function getPerformanceInsights() {
        return _getPerformanceInsights.apply(this, arguments);
      }
      return getPerformanceInsights;
    }())
  }, {
    key: "startProfilingSession",
    value: (function () {
      var _startProfilingSession = _asyncToGenerator(function* (sessionName) {
        var duration = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2hr17ar071().b[6][0]++, 60000);
        cov_2hr17ar071().f[4]++;
        cov_2hr17ar071().s[32]++;
        return yield this.performanceProfiler.startSession(sessionName, duration);
      });
      function startProfilingSession(_x) {
        return _startProfilingSession.apply(this, arguments);
      }
      return startProfilingSession;
    }())
  }, {
    key: "exportMonitoringData",
    value: (function () {
      var _exportMonitoringData = _asyncToGenerator(function* () {
        var format = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_2hr17ar071().b[7][0]++, 'json');
        cov_2hr17ar071().f[5]++;
        var data = (cov_2hr17ar071().s[33]++, {
          metrics: this.metrics,
          config: this.config,
          exportTimestamp: Date.now(),
          version: '1.0.0'
        });
        cov_2hr17ar071().s[34]++;
        switch (format) {
          case 'json':
            cov_2hr17ar071().b[8][0]++;
            cov_2hr17ar071().s[35]++;
            return JSON.stringify(data, null, 2);
          case 'csv':
            cov_2hr17ar071().b[8][1]++;
            cov_2hr17ar071().s[36]++;
            return this.convertToCSV(data);
          case 'excel':
            cov_2hr17ar071().b[8][2]++;
            cov_2hr17ar071().s[37]++;
            return this.convertToExcel(data);
          default:
            cov_2hr17ar071().b[8][3]++;
            cov_2hr17ar071().s[38]++;
            return JSON.stringify(data, null, 2);
        }
      });
      function exportMonitoringData() {
        return _exportMonitoringData.apply(this, arguments);
      }
      return exportMonitoringData;
    }())
  }, {
    key: "initializeMetrics",
    value: function initializeMetrics() {
      cov_2hr17ar071().f[6]++;
      var now = (cov_2hr17ar071().s[39]++, Date.now());
      cov_2hr17ar071().s[40]++;
      return {
        overall: {
          totalImprovement: 0,
          performanceScore: 0,
          optimizationLevel: 0,
          healthScore: 100
        },
        phases: {
          phase1: {
            phase: 'Foundation Optimizations',
            version: '1.0.0',
            status: 'active',
            performance: {
              improvement: 0,
              baseline: 100,
              current: 100,
              target: 125
            },
            metrics: {},
            lastUpdated: now
          },
          phase2: {
            phase: 'Advanced Caching & Monitoring',
            version: '1.0.0',
            status: 'active',
            performance: {
              improvement: 0,
              baseline: 125,
              current: 125,
              target: 155
            },
            metrics: {},
            lastUpdated: now
          },
          phase3a: {
            phase: 'AI-Powered Intelligence',
            version: '1.0.0',
            status: 'active',
            performance: {
              improvement: 0,
              baseline: 155,
              current: 155,
              target: 185
            },
            metrics: {},
            lastUpdated: now
          },
          phase3b: {
            phase: 'Global Edge Excellence',
            version: '1.0.0',
            status: 'active',
            performance: {
              improvement: 0,
              baseline: 185,
              current: 185,
              target: 215
            },
            metrics: {},
            lastUpdated: now
          },
          phase3c: {
            phase: 'Ultimate Hardware Acceleration',
            version: '1.0.0',
            status: 'active',
            performance: {
              improvement: 0,
              baseline: 215,
              current: 215,
              target: 500
            },
            metrics: {},
            lastUpdated: now
          }
        },
        realTime: {
          bundleSize: 0,
          loadTime: 0,
          renderTime: 0,
          memoryUsage: 0,
          cpuUsage: 0,
          networkLatency: 0,
          batteryDrain: 0
        },
        trends: {
          hourly: [],
          daily: [],
          weekly: []
        },
        alerts: []
      };
    }
  }, {
    key: "updatePhaseMetrics",
    value: function () {
      var _updatePhaseMetrics = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[7]++;
        var now = (cov_2hr17ar071().s[41]++, Date.now());
        var phase1Metrics = (cov_2hr17ar071().s[42]++, yield this.getPhase1Metrics());
        cov_2hr17ar071().s[43]++;
        this.metrics.phases.phase1.metrics = phase1Metrics;
        cov_2hr17ar071().s[44]++;
        this.metrics.phases.phase1.performance.current = this.calculatePhaseScore('phase1', phase1Metrics);
        cov_2hr17ar071().s[45]++;
        this.metrics.phases.phase1.lastUpdated = now;
        var phase2Metrics = (cov_2hr17ar071().s[46]++, yield this.getPhase2Metrics());
        cov_2hr17ar071().s[47]++;
        this.metrics.phases.phase2.metrics = phase2Metrics;
        cov_2hr17ar071().s[48]++;
        this.metrics.phases.phase2.performance.current = this.calculatePhaseScore('phase2', phase2Metrics);
        cov_2hr17ar071().s[49]++;
        this.metrics.phases.phase2.lastUpdated = now;
        var phase3aMetrics = (cov_2hr17ar071().s[50]++, yield this.getPhase3AMetrics());
        cov_2hr17ar071().s[51]++;
        this.metrics.phases.phase3a.metrics = phase3aMetrics;
        cov_2hr17ar071().s[52]++;
        this.metrics.phases.phase3a.performance.current = this.calculatePhaseScore('phase3a', phase3aMetrics);
        cov_2hr17ar071().s[53]++;
        this.metrics.phases.phase3a.lastUpdated = now;
        var phase3bMetrics = (cov_2hr17ar071().s[54]++, yield this.getPhase3BMetrics());
        cov_2hr17ar071().s[55]++;
        this.metrics.phases.phase3b.metrics = phase3bMetrics;
        cov_2hr17ar071().s[56]++;
        this.metrics.phases.phase3b.performance.current = this.calculatePhaseScore('phase3b', phase3bMetrics);
        cov_2hr17ar071().s[57]++;
        this.metrics.phases.phase3b.lastUpdated = now;
        var phase3cMetrics = (cov_2hr17ar071().s[58]++, yield this.getPhase3CMetrics());
        cov_2hr17ar071().s[59]++;
        this.metrics.phases.phase3c.metrics = phase3cMetrics;
        cov_2hr17ar071().s[60]++;
        this.metrics.phases.phase3c.performance.current = this.calculatePhaseScore('phase3c', phase3cMetrics);
        cov_2hr17ar071().s[61]++;
        this.metrics.phases.phase3c.lastUpdated = now;
      });
      function updatePhaseMetrics() {
        return _updatePhaseMetrics.apply(this, arguments);
      }
      return updatePhaseMetrics;
    }()
  }, {
    key: "getPhase1Metrics",
    value: function () {
      var _getPhase1Metrics = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[8]++;
        cov_2hr17ar071().s[62]++;
        return {
          bundleSize: 446000,
          loadTime: 910,
          renderTime: 16,
          hookOptimization: 95,
          databaseOptimization: 90
        };
      });
      function getPhase1Metrics() {
        return _getPhase1Metrics.apply(this, arguments);
      }
      return getPhase1Metrics;
    }()
  }, {
    key: "getPhase2Metrics",
    value: function () {
      var _getPhase2Metrics = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[9]++;
        cov_2hr17ar071().s[63]++;
        return {
          cacheHitRate: 95,
          offlineCapability: 90,
          imageOptimization: 85,
          progressiveLoading: 88,
          realTimeMonitoring: 92
        };
      });
      function getPhase2Metrics() {
        return _getPhase2Metrics.apply(this, arguments);
      }
      return getPhase2Metrics;
    }()
  }, {
    key: "getPhase3AMetrics",
    value: function () {
      var _getPhase3AMetrics = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[10]++;
        cov_2hr17ar071().s[64]++;
        return {
          predictionAccuracy: 85,
          adaptationSpeed: 92,
          behaviorAnalysis: 88,
          resourceOptimization: 90,
          aiIntegration: 95
        };
      });
      function getPhase3AMetrics() {
        return _getPhase3AMetrics.apply(this, arguments);
      }
      return getPhase3AMetrics;
    }()
  }, {
    key: "getPhase3BMetrics",
    value: function () {
      var _getPhase3BMetrics = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[11]++;
        cov_2hr17ar071().s[65]++;
        return {
          globalLatency: 400,
          cdnHitRate: 95,
          edgeFunctionLatency: 25,
          loadBalancingEfficiency: 98,
          geoOptimization: 90
        };
      });
      function getPhase3BMetrics() {
        return _getPhase3BMetrics.apply(this, arguments);
      }
      return getPhase3BMetrics;
    }()
  }, {
    key: "getPhase3CMetrics",
    value: function () {
      var _getPhase3CMetrics = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[12]++;
        cov_2hr17ar071().s[66]++;
        return {
          gpuUtilization: 85,
          nativePerformance: 300,
          memoryEfficiency: 91,
          backgroundProcessing: 98,
          hardwareAcceleration: 85
        };
      });
      function getPhase3CMetrics() {
        return _getPhase3CMetrics.apply(this, arguments);
      }
      return getPhase3CMetrics;
    }()
  }, {
    key: "calculatePhaseScore",
    value: function calculatePhaseScore(phase, metrics) {
      cov_2hr17ar071().f[13]++;
      cov_2hr17ar071().s[67]++;
      switch (phase) {
        case 'phase1':
          cov_2hr17ar071().b[9][0]++;
          var bundleImprovement = (cov_2hr17ar071().s[68]++, (1024000 - metrics.bundleSize) / 1024000 * 100);
          var loadTimeImprovement = (cov_2hr17ar071().s[69]++, (3000 - metrics.loadTime) / 3000 * 100);
          var renderImprovement = (cov_2hr17ar071().s[70]++, (100 - metrics.renderTime) / 100 * 100);
          cov_2hr17ar071().s[71]++;
          return (bundleImprovement + loadTimeImprovement + renderImprovement) / 3 + 100;
        case 'phase2':
          cov_2hr17ar071().b[9][1]++;
          cov_2hr17ar071().s[72]++;
          return (metrics.cacheHitRate + metrics.offlineCapability + metrics.imageOptimization) / 3 + 125;
        case 'phase3a':
          cov_2hr17ar071().b[9][2]++;
          cov_2hr17ar071().s[73]++;
          return (metrics.predictionAccuracy + metrics.adaptationSpeed + metrics.aiIntegration) / 3 + 155;
        case 'phase3b':
          cov_2hr17ar071().b[9][3]++;
          var latencyImprovement = (cov_2hr17ar071().s[74]++, (3000 - metrics.globalLatency) / 3000 * 100);
          cov_2hr17ar071().s[75]++;
          return (latencyImprovement + metrics.cdnHitRate + metrics.geoOptimization) / 3 + 185;
        case 'phase3c':
          cov_2hr17ar071().b[9][4]++;
          cov_2hr17ar071().s[76]++;
          return (metrics.gpuUtilization + metrics.memoryEfficiency + metrics.hardwareAcceleration) / 3 + 215;
        default:
          cov_2hr17ar071().b[9][5]++;
          cov_2hr17ar071().s[77]++;
          return 100;
      }
    }
  }, {
    key: "calculateOverallMetrics",
    value: function calculateOverallMetrics() {
      cov_2hr17ar071().f[14]++;
      var phases = (cov_2hr17ar071().s[78]++, Object.values(this.metrics.phases));
      var totalImprovement = (cov_2hr17ar071().s[79]++, phases.reduce(function (sum, phase) {
        cov_2hr17ar071().f[15]++;
        var improvement = (cov_2hr17ar071().s[80]++, (phase.performance.current - phase.performance.baseline) / phase.performance.baseline * 100);
        cov_2hr17ar071().s[81]++;
        return sum + improvement;
      }, 0));
      var performanceScore = (cov_2hr17ar071().s[82]++, phases.reduce(function (sum, phase, index) {
        cov_2hr17ar071().f[16]++;
        var weight = (cov_2hr17ar071().s[83]++, index < 2 ? (cov_2hr17ar071().b[10][0]++, 0.15) : (cov_2hr17ar071().b[10][1]++, 0.23));
        cov_2hr17ar071().s[84]++;
        return sum + phase.performance.current * weight;
      }, 0));
      var optimizationLevel = (cov_2hr17ar071().s[85]++, Math.min((performanceScore - 100) / 4, 100));
      var healthScore = (cov_2hr17ar071().s[86]++, phases.every(function (phase) {
        cov_2hr17ar071().f[17]++;
        cov_2hr17ar071().s[87]++;
        return phase.status === 'active';
      }) ? (cov_2hr17ar071().b[11][0]++, 100) : (cov_2hr17ar071().b[11][1]++, 85));
      cov_2hr17ar071().s[88]++;
      this.metrics.overall = {
        totalImprovement: totalImprovement,
        performanceScore: performanceScore,
        optimizationLevel: optimizationLevel,
        healthScore: healthScore
      };
    }
  }, {
    key: "updateRealTimeMetrics",
    value: function () {
      var _updateRealTimeMetrics = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[18]++;
        cov_2hr17ar071().s[89]++;
        this.metrics.realTime = {
          bundleSize: 446000,
          loadTime: 910,
          renderTime: 16,
          memoryUsage: 65,
          cpuUsage: 25,
          networkLatency: 45,
          batteryDrain: 3
        };
      });
      function updateRealTimeMetrics() {
        return _updateRealTimeMetrics.apply(this, arguments);
      }
      return updateRealTimeMetrics;
    }()
  }, {
    key: "updateTrendAnalysis",
    value: function () {
      var _updateTrendAnalysis = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[19]++;
        var currentScore = (cov_2hr17ar071().s[90]++, this.metrics.overall.performanceScore);
        var now = (cov_2hr17ar071().s[91]++, Date.now());
        cov_2hr17ar071().s[92]++;
        this.metrics.trends.hourly.push({
          timestamp: now,
          score: currentScore
        });
        cov_2hr17ar071().s[93]++;
        if (this.metrics.trends.hourly.length > 24) {
          cov_2hr17ar071().b[12][0]++;
          cov_2hr17ar071().s[94]++;
          this.metrics.trends.hourly.shift();
        } else {
          cov_2hr17ar071().b[12][1]++;
        }
        cov_2hr17ar071().s[95]++;
        if (this.metrics.trends.hourly.length === 24) {
          cov_2hr17ar071().b[13][0]++;
          var dailyAverage = (cov_2hr17ar071().s[96]++, this.metrics.trends.hourly.reduce(function (sum, point) {
            cov_2hr17ar071().f[20]++;
            cov_2hr17ar071().s[97]++;
            return sum + point.score;
          }, 0) / 24);
          cov_2hr17ar071().s[98]++;
          this.metrics.trends.daily.push({
            timestamp: now,
            score: dailyAverage
          });
          cov_2hr17ar071().s[99]++;
          if (this.metrics.trends.daily.length > 30) {
            cov_2hr17ar071().b[14][0]++;
            cov_2hr17ar071().s[100]++;
            this.metrics.trends.daily.shift();
          } else {
            cov_2hr17ar071().b[14][1]++;
          }
        } else {
          cov_2hr17ar071().b[13][1]++;
        }
        cov_2hr17ar071().s[101]++;
        if (this.metrics.trends.daily.length === 7) {
          cov_2hr17ar071().b[15][0]++;
          var weeklyAverage = (cov_2hr17ar071().s[102]++, this.metrics.trends.daily.slice(-7).reduce(function (sum, point) {
            cov_2hr17ar071().f[21]++;
            cov_2hr17ar071().s[103]++;
            return sum + point.score;
          }, 0) / 7);
          cov_2hr17ar071().s[104]++;
          this.metrics.trends.weekly.push({
            timestamp: now,
            score: weeklyAverage
          });
          cov_2hr17ar071().s[105]++;
          if (this.metrics.trends.weekly.length > 12) {
            cov_2hr17ar071().b[16][0]++;
            cov_2hr17ar071().s[106]++;
            this.metrics.trends.weekly.shift();
          } else {
            cov_2hr17ar071().b[16][1]++;
          }
        } else {
          cov_2hr17ar071().b[15][1]++;
        }
      });
      function updateTrendAnalysis() {
        return _updateTrendAnalysis.apply(this, arguments);
      }
      return updateTrendAnalysis;
    }()
  }, {
    key: "checkAlerts",
    value: function () {
      var _checkAlerts = _asyncToGenerator(function* () {
        var _this$metrics$alerts;
        cov_2hr17ar071().f[22]++;
        var alerts = (cov_2hr17ar071().s[107]++, yield this.alertManager.checkAlerts(this.metrics));
        cov_2hr17ar071().s[108]++;
        (_this$metrics$alerts = this.metrics.alerts).push.apply(_this$metrics$alerts, _toConsumableArray(alerts));
        cov_2hr17ar071().s[109]++;
        if (this.metrics.alerts.length > 100) {
          cov_2hr17ar071().b[17][0]++;
          cov_2hr17ar071().s[110]++;
          this.metrics.alerts.splice(0, 50);
        } else {
          cov_2hr17ar071().b[17][1]++;
        }
      });
      function checkAlerts() {
        return _checkAlerts.apply(this, arguments);
      }
      return checkAlerts;
    }()
  }, {
    key: "generateInsights",
    value: function () {
      var _generateInsights = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[23]++;
        var insights = (cov_2hr17ar071().s[111]++, []);
        cov_2hr17ar071().s[112]++;
        if (this.metrics.overall.performanceScore > 400) {
          cov_2hr17ar071().b[18][0]++;
          cov_2hr17ar071().s[113]++;
          insights.push({
            category: 'Performance',
            insight: 'Exceptional performance achieved across all optimization phases',
            impact: 'high',
            recommendation: 'Maintain current optimization levels and monitor for regressions'
          });
        } else {
          cov_2hr17ar071().b[18][1]++;
        }
        cov_2hr17ar071().s[114]++;
        if (this.metrics.phases.phase3c.metrics.gpuUtilization > 80) {
          cov_2hr17ar071().b[19][0]++;
          cov_2hr17ar071().s[115]++;
          insights.push({
            category: 'Hardware',
            insight: 'High GPU utilization indicates excellent hardware acceleration',
            impact: 'high',
            recommendation: 'Consider expanding GPU-accelerated features'
          });
        } else {
          cov_2hr17ar071().b[19][1]++;
        }
        cov_2hr17ar071().s[116]++;
        return insights;
      });
      function generateInsights() {
        return _generateInsights.apply(this, arguments);
      }
      return generateInsights;
    }()
  }, {
    key: "identifyOptimizationOpportunities",
    value: function () {
      var _identifyOptimizationOpportunities = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[24]++;
        var opportunities = (cov_2hr17ar071().s[117]++, []);
        cov_2hr17ar071().s[118]++;
        Object.entries(this.metrics.phases).forEach(function (_ref) {
          var _ref2 = _slicedToArray(_ref, 2),
            phaseKey = _ref2[0],
            phase = _ref2[1];
          cov_2hr17ar071().f[25]++;
          var potential = (cov_2hr17ar071().s[119]++, phase.performance.target - phase.performance.current);
          cov_2hr17ar071().s[120]++;
          if (potential > 10) {
            cov_2hr17ar071().b[20][0]++;
            cov_2hr17ar071().s[121]++;
            opportunities.push({
              phase: phase.phase,
              opportunity: `Further optimization potential of ${potential.toFixed(1)}%`,
              potentialGain: potential,
              effort: potential > 50 ? (cov_2hr17ar071().b[21][0]++, 'high') : (cov_2hr17ar071().b[21][1]++, potential > 20 ? (cov_2hr17ar071().b[22][0]++, 'medium') : (cov_2hr17ar071().b[22][1]++, 'low'))
            });
          } else {
            cov_2hr17ar071().b[20][1]++;
          }
        });
        cov_2hr17ar071().s[122]++;
        return opportunities;
      });
      function identifyOptimizationOpportunities() {
        return _identifyOptimizationOpportunities.apply(this, arguments);
      }
      return identifyOptimizationOpportunities;
    }()
  }, {
    key: "generateHealthReport",
    value: function () {
      var _generateHealthReport = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[26]++;
        var phaseHealth = (cov_2hr17ar071().s[123]++, {});
        var criticalIssues = (cov_2hr17ar071().s[124]++, []);
        var recommendations = (cov_2hr17ar071().s[125]++, []);
        cov_2hr17ar071().s[126]++;
        Object.entries(this.metrics.phases).forEach(function (_ref3) {
          var _ref4 = _slicedToArray(_ref3, 2),
            phaseKey = _ref4[0],
            phase = _ref4[1];
          cov_2hr17ar071().f[27]++;
          var health = (cov_2hr17ar071().s[127]++, phase.status === 'active' ? (cov_2hr17ar071().b[23][0]++, 100) : (cov_2hr17ar071().b[23][1]++, phase.status === 'error' ? (cov_2hr17ar071().b[24][0]++, 0) : (cov_2hr17ar071().b[24][1]++, 50)));
          cov_2hr17ar071().s[128]++;
          phaseHealth[phaseKey] = health;
          cov_2hr17ar071().s[129]++;
          if (health < 80) {
            cov_2hr17ar071().b[25][0]++;
            cov_2hr17ar071().s[130]++;
            criticalIssues.push(`${phase.phase} requires attention`);
            cov_2hr17ar071().s[131]++;
            recommendations.push(`Review and optimize ${phase.phase} systems`);
          } else {
            cov_2hr17ar071().b[25][1]++;
          }
        });
        cov_2hr17ar071().s[132]++;
        return {
          overallHealth: this.metrics.overall.healthScore,
          phaseHealth: phaseHealth,
          criticalIssues: criticalIssues,
          recommendations: recommendations
        };
      });
      function generateHealthReport() {
        return _generateHealthReport.apply(this, arguments);
      }
      return generateHealthReport;
    }()
  }, {
    key: "startRealTimeMonitoring",
    value: function startRealTimeMonitoring() {
      var _this = this;
      cov_2hr17ar071().f[28]++;
      cov_2hr17ar071().s[133]++;
      this.monitoringInterval = setInterval(_asyncToGenerator(function* () {
        cov_2hr17ar071().f[29]++;
        cov_2hr17ar071().s[134]++;
        yield _this.getUnifiedMetrics();
      }), this.config.updateInterval);
    }
  }, {
    key: "convertToCSV",
    value: function convertToCSV(data) {
      cov_2hr17ar071().f[30]++;
      cov_2hr17ar071().s[135]++;
      return 'CSV export not implemented yet';
    }
  }, {
    key: "convertToExcel",
    value: function convertToExcel(data) {
      cov_2hr17ar071().f[31]++;
      cov_2hr17ar071().s[136]++;
      return 'Excel export not implemented yet';
    }
  }]);
}();
var TrendAnalyzer = function () {
  function TrendAnalyzer() {
    _classCallCheck(this, TrendAnalyzer);
  }
  return _createClass(TrendAnalyzer, [{
    key: "initialize",
    value: function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[32]++;
        cov_2hr17ar071().s[137]++;
        console.log('Trend Analyzer initialized');
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }()
  }]);
}();
var AlertManager = function () {
  function AlertManager(thresholds) {
    _classCallCheck(this, AlertManager);
    cov_2hr17ar071().f[33]++;
    cov_2hr17ar071().s[138]++;
    this.thresholds = thresholds;
  }
  return _createClass(AlertManager, [{
    key: "checkAlerts",
    value: function () {
      var _checkAlerts2 = _asyncToGenerator(function* (metrics) {
        cov_2hr17ar071().f[34]++;
        var alerts = (cov_2hr17ar071().s[139]++, []);
        cov_2hr17ar071().s[140]++;
        if (metrics.overall.performanceScore < 300) {
          cov_2hr17ar071().b[26][0]++;
          cov_2hr17ar071().s[141]++;
          alerts.push({
            id: `alert_${Date.now()}`,
            severity: 'warning',
            phase: 'overall',
            message: 'Performance score below expected threshold',
            timestamp: Date.now(),
            resolved: false
          });
        } else {
          cov_2hr17ar071().b[26][1]++;
        }
        cov_2hr17ar071().s[142]++;
        return alerts;
      });
      function checkAlerts(_x2) {
        return _checkAlerts2.apply(this, arguments);
      }
      return checkAlerts;
    }()
  }]);
}();
var PerformanceProfiler = function () {
  function PerformanceProfiler() {
    _classCallCheck(this, PerformanceProfiler);
  }
  return _createClass(PerformanceProfiler, [{
    key: "start",
    value: function () {
      var _start = _asyncToGenerator(function* () {
        cov_2hr17ar071().f[35]++;
        cov_2hr17ar071().s[143]++;
        console.log('Performance Profiler started');
      });
      function start() {
        return _start.apply(this, arguments);
      }
      return start;
    }()
  }, {
    key: "startSession",
    value: function () {
      var _startSession = _asyncToGenerator(function* (sessionName, duration) {
        cov_2hr17ar071().f[36]++;
        var sessionId = (cov_2hr17ar071().s[144]++, `session_${Date.now()}`);
        cov_2hr17ar071().s[145]++;
        console.log(`Started profiling session: ${sessionName} (${sessionId})`);
        cov_2hr17ar071().s[146]++;
        return sessionId;
      });
      function startSession(_x3, _x4) {
        return _startSession.apply(this, arguments);
      }
      return startSession;
    }()
  }]);
}();
export var unifiedPerformanceMonitor = (cov_2hr17ar071().s[147]++, new UnifiedPerformanceMonitor());
export default unifiedPerformanceMonitor;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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