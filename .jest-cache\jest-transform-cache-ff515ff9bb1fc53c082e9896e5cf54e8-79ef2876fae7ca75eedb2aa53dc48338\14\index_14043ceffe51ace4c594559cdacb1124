98cb2497374213bb3dd51b56bdf20bca
"use strict";
'use client';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["behavior", "contentContainerStyle", "keyboardVerticalOffset"];
var KeyboardAvoidingView = function (_React$Component) {
  function KeyboardAvoidingView() {
    var _this;
    (0, _classCallCheck2.default)(this, KeyboardAvoidingView);
    _this = _callSuper(this, KeyboardAvoidingView, arguments);
    _this.frame = null;
    _this.onLayout = function (event) {
      _this.frame = event.nativeEvent.layout;
    };
    return _this;
  }
  (0, _inherits2.default)(KeyboardAvoidingView, _React$Component);
  return (0, _createClass2.default)(KeyboardAvoidingView, [{
    key: "relativeKeyboardHeight",
    value: function relativeKeyboardHeight(keyboardFrame) {
      var frame = this.frame;
      if (!frame || !keyboardFrame) {
        return 0;
      }
      var keyboardY = keyboardFrame.screenY - (this.props.keyboardVerticalOffset || 0);
      return Math.max(frame.y + frame.height - keyboardY, 0);
    }
  }, {
    key: "onKeyboardChange",
    value: function onKeyboardChange(event) {}
  }, {
    key: "render",
    value: function render() {
      var _this$props = this.props,
        behavior = _this$props.behavior,
        contentContainerStyle = _this$props.contentContainerStyle,
        keyboardVerticalOffset = _this$props.keyboardVerticalOffset,
        rest = (0, _objectWithoutPropertiesLoose2.default)(_this$props, _excluded);
      return React.createElement(_View.default, (0, _extends2.default)({
        onLayout: this.onLayout
      }, rest));
    }
  }]);
}(React.Component);
var _default = exports.default = KeyboardAvoidingView;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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