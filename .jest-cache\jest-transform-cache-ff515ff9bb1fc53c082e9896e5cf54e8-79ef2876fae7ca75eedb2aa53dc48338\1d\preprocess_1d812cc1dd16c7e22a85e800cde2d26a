a4b9da513673efbac79fb2fbf3fa96a7
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.preprocess = exports.default = exports.createTransformValue = exports.createTransformOriginValue = exports.createTextShadowValue = exports.createBoxShadowValue = exports.createBoxShadowArrayValue = void 0;
var _normalizeColor = _interopRequireDefault(require("./compiler/normalizeColor"));
var _normalizeValueWithProperty = _interopRequireDefault(require("./compiler/normalizeValueWithProperty"));
var _warnOnce = require("../../modules/warnOnce");
var emptyObject = {};
var defaultOffset = {
  height: 0,
  width: 0
};
var createBoxShadowValue = function createBoxShadowValue(style) {
  var shadowColor = style.shadowColor,
    shadowOffset = style.shadowOffset,
    shadowOpacity = style.shadowOpacity,
    shadowRadius = style.shadowRadius;
  var _ref = shadowOffset || defaultOffset,
    height = _ref.height,
    width = _ref.width;
  var offsetX = (0, _normalizeValueWithProperty.default)(width);
  var offsetY = (0, _normalizeValueWithProperty.default)(height);
  var blurRadius = (0, _normalizeValueWithProperty.default)(shadowRadius || 0);
  var color = (0, _normalizeColor.default)(shadowColor || 'black', shadowOpacity);
  if (color != null && offsetX != null && offsetY != null && blurRadius != null) {
    return offsetX + " " + offsetY + " " + blurRadius + " " + color;
  }
};
exports.createBoxShadowValue = createBoxShadowValue;
var createTextShadowValue = function createTextShadowValue(style) {
  var textShadowColor = style.textShadowColor,
    textShadowOffset = style.textShadowOffset,
    textShadowRadius = style.textShadowRadius;
  var _ref2 = textShadowOffset || defaultOffset,
    height = _ref2.height,
    width = _ref2.width;
  var radius = textShadowRadius || 0;
  var offsetX = (0, _normalizeValueWithProperty.default)(width);
  var offsetY = (0, _normalizeValueWithProperty.default)(height);
  var blurRadius = (0, _normalizeValueWithProperty.default)(radius);
  var color = (0, _normalizeValueWithProperty.default)(textShadowColor, 'textShadowColor');
  if (color && (height !== 0 || width !== 0 || radius !== 0) && offsetX != null && offsetY != null && blurRadius != null) {
    return offsetX + " " + offsetY + " " + blurRadius + " " + color;
  }
};
exports.createTextShadowValue = createTextShadowValue;
var mapBoxShadow = function mapBoxShadow(boxShadow) {
  if (typeof boxShadow === 'string') {
    return boxShadow;
  }
  var offsetX = (0, _normalizeValueWithProperty.default)(boxShadow.offsetX) || 0;
  var offsetY = (0, _normalizeValueWithProperty.default)(boxShadow.offsetY) || 0;
  var blurRadius = (0, _normalizeValueWithProperty.default)(boxShadow.blurRadius) || 0;
  var spreadDistance = (0, _normalizeValueWithProperty.default)(boxShadow.spreadDistance) || 0;
  var color = (0, _normalizeColor.default)(boxShadow.color) || 'black';
  var position = boxShadow.inset ? 'inset ' : '';
  return "" + position + offsetX + " " + offsetY + " " + blurRadius + " " + spreadDistance + " " + color;
};
var createBoxShadowArrayValue = function createBoxShadowArrayValue(value) {
  return value.map(mapBoxShadow).join(', ');
};
exports.createBoxShadowArrayValue = createBoxShadowArrayValue;
var mapTransform = function mapTransform(transform) {
  var type = Object.keys(transform)[0];
  var value = transform[type];
  if (type === 'matrix' || type === 'matrix3d') {
    return type + "(" + value.join(',') + ")";
  } else {
    var normalizedValue = (0, _normalizeValueWithProperty.default)(value, type);
    return type + "(" + normalizedValue + ")";
  }
};
var createTransformValue = function createTransformValue(value) {
  return value.map(mapTransform).join(' ');
};
exports.createTransformValue = createTransformValue;
var createTransformOriginValue = function createTransformOriginValue(value) {
  return value.map(function (v) {
    return (0, _normalizeValueWithProperty.default)(v);
  }).join(' ');
};
exports.createTransformOriginValue = createTransformOriginValue;
var PROPERTIES_STANDARD = {
  borderBottomEndRadius: 'borderEndEndRadius',
  borderBottomStartRadius: 'borderEndStartRadius',
  borderTopEndRadius: 'borderStartEndRadius',
  borderTopStartRadius: 'borderStartStartRadius',
  borderEndColor: 'borderInlineEndColor',
  borderEndStyle: 'borderInlineEndStyle',
  borderEndWidth: 'borderInlineEndWidth',
  borderStartColor: 'borderInlineStartColor',
  borderStartStyle: 'borderInlineStartStyle',
  borderStartWidth: 'borderInlineStartWidth',
  end: 'insetInlineEnd',
  marginEnd: 'marginInlineEnd',
  marginHorizontal: 'marginInline',
  marginStart: 'marginInlineStart',
  marginVertical: 'marginBlock',
  paddingEnd: 'paddingInlineEnd',
  paddingHorizontal: 'paddingInline',
  paddingStart: 'paddingInlineStart',
  paddingVertical: 'paddingBlock',
  start: 'insetInlineStart'
};
var ignoredProps = {
  elevation: true,
  overlayColor: true,
  resizeMode: true,
  tintColor: true
};
var preprocess = exports.preprocess = function preprocess(originalStyle, options) {
  if (options === void 0) {
    options = {};
  }
  var style = originalStyle || emptyObject;
  var nextStyle = {};
  if (options.shadow === true, style.shadowColor != null || style.shadowOffset != null || style.shadowOpacity != null || style.shadowRadius != null) {
    (0, _warnOnce.warnOnce)('shadowStyles', "\"shadow*\" style props are deprecated. Use \"boxShadow\".");
    var boxShadowValue = createBoxShadowValue(style);
    if (boxShadowValue != null) {
      nextStyle.boxShadow = boxShadowValue;
    }
  }
  if (options.textShadow === true, style.textShadowColor != null || style.textShadowOffset != null || style.textShadowRadius != null) {
    (0, _warnOnce.warnOnce)('textShadowStyles', "\"textShadow*\" style props are deprecated. Use \"textShadow\".");
    var textShadowValue = createTextShadowValue(style);
    if (textShadowValue != null && nextStyle.textShadow == null) {
      var textShadow = style.textShadow;
      var value = textShadow ? textShadow + ", " + textShadowValue : textShadowValue;
      nextStyle.textShadow = value;
    }
  }
  for (var originalProp in style) {
    if (ignoredProps[originalProp] != null || originalProp === 'shadowColor' || originalProp === 'shadowOffset' || originalProp === 'shadowOpacity' || originalProp === 'shadowRadius' || originalProp === 'textShadowColor' || originalProp === 'textShadowOffset' || originalProp === 'textShadowRadius') {
      continue;
    }
    var originalValue = style[originalProp];
    var prop = PROPERTIES_STANDARD[originalProp] || originalProp;
    var _value = originalValue;
    if (!Object.prototype.hasOwnProperty.call(style, originalProp) || prop !== originalProp && style[prop] != null) {
      continue;
    }
    if (prop === 'aspectRatio' && typeof _value === 'number') {
      nextStyle[prop] = _value.toString();
    } else if (prop === 'boxShadow') {
      if (Array.isArray(_value)) {
        _value = createBoxShadowArrayValue(_value);
      }
      var boxShadow = nextStyle.boxShadow;
      nextStyle.boxShadow = boxShadow ? _value + ", " + boxShadow : _value;
    } else if (prop === 'fontVariant') {
      if (Array.isArray(_value) && _value.length > 0) {
        _value = _value.join(' ');
      }
      nextStyle[prop] = _value;
    } else if (prop === 'textAlignVertical') {
      if (style.verticalAlign == null) {
        nextStyle.verticalAlign = _value === 'center' ? 'middle' : _value;
      }
    } else if (prop === 'transform') {
      if (Array.isArray(_value)) {
        _value = createTransformValue(_value);
      }
      nextStyle.transform = _value;
    } else if (prop === 'transformOrigin') {
      if (Array.isArray(_value)) {
        _value = createTransformOriginValue(_value);
      }
      nextStyle.transformOrigin = _value;
    } else {
      nextStyle[prop] = _value;
    }
  }
  return nextStyle;
};
var _default = exports.default = preprocess;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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