39d83af801a160fbc36ba6b8140b8455
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _View = _interopRequireDefault(require("../View"));
var _react = _interopRequireDefault(require("react"));
var _excluded = ["colors", "enabled", "onRefresh", "progressBackgroundColor", "progressViewOffset", "refreshing", "size", "tintColor", "title", "titleColor"];
function RefreshControl(props) {
  var colors = props.colors,
    enabled = props.enabled,
    onRefresh = props.onRefresh,
    progressBackgroundColor = props.progressBackgroundColor,
    progressViewOffset = props.progressViewOffset,
    refreshing = props.refreshing,
    size = props.size,
    tintColor = props.tintColor,
    title = props.title,
    titleColor = props.titleColor,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  return _react.default.createElement(_View.default, rest);
}
var _default = exports.default = RefreshControl;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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