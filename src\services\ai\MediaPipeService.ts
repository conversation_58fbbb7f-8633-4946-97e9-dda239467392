/**
 * MediaPipe Pose Detection Service
 * Integrates Google MediaPipe for tennis technique analysis
 */

import { performanceMonitor } from '@/utils/performance';

// MediaPipe imports for browser/Node.js
let Pose: any;
let Camera: any;
let drawConnectors: any;
let drawLandmarks: any;
let POSE_CONNECTIONS: any;
let POSE_LANDMARKS: any;

// Dynamic import for MediaPipe (browser environment)
const initializeMediaPipe = async () => {
  if (typeof window !== 'undefined') {
    // Browser environment
    const poseModule = await import('@mediapipe/pose');
    const cameraModule = await import('@mediapipe/camera_utils');
    const drawingModule = await import('@mediapipe/drawing_utils');

    Pose = poseModule.Pose;
    Camera = cameraModule.Camera;
    drawConnectors = drawingModule.drawConnectors;
    drawLandmarks = drawingModule.drawLandmarks;
    POSE_CONNECTIONS = poseModule.POSE_CONNECTIONS;
    POSE_LANDMARKS = poseModule.POSE_LANDMARKS;
  }
};

// MediaPipe pose landmark indices for tennis-specific analysis
export const TENNIS_POSE_LANDMARKS = {
  // Upper body landmarks critical for tennis
  LEFT_SHOULDER: 11,
  RIGHT_SHOULDER: 12,
  LEFT_ELBOW: 13,
  RIGHT_ELBOW: 14,
  LEFT_WRIST: 15,
  RIGHT_WRIST: 16,
  LEFT_HIP: 23,
  RIGHT_HIP: 24,
  LEFT_KNEE: 25,
  RIGHT_KNEE: 26,
  LEFT_ANKLE: 27,
  RIGHT_ANKLE: 28,
  // Additional landmarks for tennis analysis
  NOSE: 0,
  LEFT_EYE: 1,
  RIGHT_EYE: 2,
  LEFT_EAR: 3,
  RIGHT_EAR: 4,
  MOUTH_LEFT: 9,
  MOUTH_RIGHT: 10,
} as const;

export interface PoseLandmark {
  x: number; // Normalized x coordinate (0-1)
  y: number; // Normalized y coordinate (0-1)
  z: number; // Depth coordinate
  visibility: number; // Visibility confidence (0-1)
}

export interface PoseDetectionResult {
  landmarks: PoseLandmark[];
  confidence: number;
  timestamp: number;
  frameIndex: number;
  worldLandmarks?: PoseLandmark[]; // 3D world coordinates
}

export interface TennisMovementAnalysis {
  movementType: 'serve' | 'forehand' | 'backhand' | 'volley' | 'overhead' | 'unknown';
  confidence: number;
  keyAngles: {
    shoulderAngle: number;
    elbowAngle: number;
    wristAngle: number;
    hipAngle: number;
    kneeAngle: number;
  };
  bodyPosition: {
    stance: 'open' | 'closed' | 'neutral';
    weight: 'forward' | 'backward' | 'centered';
    balance: number; // 0-1 score
  };
  technicalMetrics: {
    racketPosition: 'high' | 'medium' | 'low';
    followThrough: 'complete' | 'partial' | 'none';
    footwork: 'excellent' | 'good' | 'needs_improvement';
  };
}

export interface PoseAnalysisConfig {
  minDetectionConfidence: number;
  minTrackingConfidence: number;
  modelComplexity: 0 | 1 | 2; // 0=lite, 1=full, 2=heavy
  smoothLandmarks: boolean;
  enableSegmentation: boolean;
  smoothSegmentation: boolean;
}

class MediaPipeService {
  private isInitialized = false;
  private pose: any = null;
  private camera: any = null;
  private config: PoseAnalysisConfig = {
    minDetectionConfidence: 0.7,
    minTrackingConfidence: 0.5,
    modelComplexity: 1,
    smoothLandmarks: true,
    enableSegmentation: false,
    smoothSegmentation: true,
  };
  private modelUrl: string;

  constructor() {
    this.modelUrl = process.env.EXPO_PUBLIC_MEDIAPIPE_MODEL_URL ||
      'https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task';
  }

  /**
   * Initialize MediaPipe pose detection
   */
  async initialize(config?: Partial<PoseAnalysisConfig>): Promise<void> {
    try {
      performanceMonitor.start('mediapipe_init');

      if (config) {
        this.config = { ...this.config, ...config };
      }

      // Initialize MediaPipe modules
      await initializeMediaPipe();

      // Create and configure the pose detection model
      await this.loadMediaPipeModel();

      this.isInitialized = true;
      performanceMonitor.end('mediapipe_init');
      console.log('MediaPipe service initialized successfully');
    } catch (error) {
      console.error('Failed to initialize MediaPipe:', error);
      throw new Error('MediaPipe initialization failed');
    }
  }

  /**
   * Detect pose landmarks in a video frame
   */
  async detectPose(
    imageData: ImageData | HTMLVideoElement | HTMLCanvasElement,
    frameIndex: number = 0
  ): Promise<PoseDetectionResult | null> {
    if (!this.isInitialized || !this.pose) {
      throw new Error('MediaPipe service not initialized');
    }

    try {
      performanceMonitor.start('pose_detection');

      // Process the image with MediaPipe
      const results = await this.processImageWithMediaPipe(imageData);

      if (!results || !results.poseLandmarks || results.poseLandmarks.length === 0) {
        performanceMonitor.end('pose_detection');
        return null;
      }

      // Convert MediaPipe results to our format
      const poseResult: PoseDetectionResult = {
        landmarks: results.poseLandmarks.map((landmark: any) => ({
          x: landmark.x,
          y: landmark.y,
          z: landmark.z || 0,
          visibility: landmark.visibility || 1,
        })),
        confidence: this.calculateOverallConfidence(results.poseLandmarks),
        timestamp: Date.now(),
        frameIndex,
        worldLandmarks: results.poseWorldLandmarks?.map((landmark: any) => ({
          x: landmark.x,
          y: landmark.y,
          z: landmark.z,
          visibility: landmark.visibility || 1,
        })),
      };

      performanceMonitor.end('pose_detection');
      return poseResult;
    } catch (error) {
      console.error('Pose detection failed:', error);
      performanceMonitor.end('pose_detection');
      return null;
    }
  }

  /**
   * Analyze tennis-specific movements from pose data
   */
  analyzeTennisMovement(poseResult: PoseDetectionResult): TennisMovementAnalysis {
    try {
      performanceMonitor.start('tennis_movement_analysis');

      const landmarks = poseResult.landmarks;
      
      // Calculate key angles for tennis analysis
      const keyAngles = this.calculateKeyAngles(landmarks);
      
      // Determine movement type based on pose patterns
      const movementType = this.classifyTennisMovement(landmarks, keyAngles);
      
      // Analyze body position and stance
      const bodyPosition = this.analyzeBodyPosition(landmarks);
      
      // Evaluate technical metrics
      const technicalMetrics = this.evaluateTechnicalMetrics(landmarks, keyAngles);

      const analysis: TennisMovementAnalysis = {
        movementType: movementType.type,
        confidence: movementType.confidence,
        keyAngles,
        bodyPosition,
        technicalMetrics,
      };

      performanceMonitor.end('tennis_movement_analysis');
      return analysis;
    } catch (error) {
      console.error('Tennis movement analysis failed:', error);
      throw error;
    }
  }

  /**
   * Process multiple frames for continuous analysis
   */
  async processVideoFrames(
    frames: (ImageData | HTMLVideoElement | HTMLCanvasElement)[],
    onProgress?: (progress: number) => void
  ): Promise<TennisMovementAnalysis[]> {
    const results: TennisMovementAnalysis[] = [];
    
    for (let i = 0; i < frames.length; i++) {
      const poseResult = await this.detectPose(frames[i], i);
      
      if (poseResult) {
        const analysis = this.analyzeTennisMovement(poseResult);
        results.push(analysis);
      }
      
      if (onProgress) {
        onProgress((i + 1) / frames.length);
      }
    }
    
    return results;
  }

  /**
   * Calculate angles between key body joints
   */
  private calculateKeyAngles(landmarks: PoseLandmark[]): TennisMovementAnalysis['keyAngles'] {
    // Calculate shoulder angle (shoulder-elbow-wrist)
    const shoulderAngle = this.calculateAngle(
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER],
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_ELBOW],
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST]
    );

    // Calculate elbow angle
    const elbowAngle = this.calculateAngle(
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER],
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_ELBOW],
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST]
    );

    // Calculate hip angle
    const hipAngle = this.calculateAngle(
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER],
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP],
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_KNEE]
    );

    // Calculate knee angle
    const kneeAngle = this.calculateAngle(
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP],
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_KNEE],
      landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE]
    );

    return {
      shoulderAngle,
      elbowAngle,
      wristAngle: 0, // Simplified for now
      hipAngle,
      kneeAngle,
    };
  }

  /**
   * Calculate angle between three points
   */
  private calculateAngle(point1: PoseLandmark, point2: PoseLandmark, point3: PoseLandmark): number {
    const vector1 = {
      x: point1.x - point2.x,
      y: point1.y - point2.y,
    };
    
    const vector2 = {
      x: point3.x - point2.x,
      y: point3.y - point2.y,
    };
    
    const dotProduct = vector1.x * vector2.x + vector1.y * vector2.y;
    const magnitude1 = Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y);
    const magnitude2 = Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y);
    
    const cosAngle = dotProduct / (magnitude1 * magnitude2);
    const angle = Math.acos(Math.max(-1, Math.min(1, cosAngle)));
    
    return (angle * 180) / Math.PI; // Convert to degrees
  }

  /**
   * Classify tennis movement type based on pose patterns
   */
  private classifyTennisMovement(
    landmarks: PoseLandmark[], 
    angles: TennisMovementAnalysis['keyAngles']
  ): { type: TennisMovementAnalysis['movementType']; confidence: number } {
    // Simplified classification logic
    // In a real implementation, this would use machine learning models
    
    const rightWrist = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_WRIST];
    const leftWrist = landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST];
    const rightShoulder = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER];
    const leftShoulder = landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER];

    // Determine dominant hand based on wrist position
    const isRightHanded = rightWrist.y < leftWrist.y;
    
    // Classify based on arm position and angles
    if (angles.shoulderAngle > 160 && rightWrist.y < rightShoulder.y) {
      return { type: 'serve', confidence: 0.85 };
    } else if (isRightHanded && rightWrist.x > rightShoulder.x) {
      return { type: 'forehand', confidence: 0.80 };
    } else if (isRightHanded && rightWrist.x < rightShoulder.x) {
      return { type: 'backhand', confidence: 0.75 };
    } else if (rightWrist.y < rightShoulder.y - 0.1) {
      return { type: 'volley', confidence: 0.70 };
    } else {
      return { type: 'unknown', confidence: 0.50 };
    }
  }

  /**
   * Analyze body position and stance
   */
  private analyzeBodyPosition(landmarks: PoseLandmark[]): TennisMovementAnalysis['bodyPosition'] {
    const leftFoot = landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE];
    const rightFoot = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_ANKLE];
    const leftHip = landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP];
    const rightHip = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_HIP];

    // Calculate stance based on foot positioning
    const footDistance = Math.abs(leftFoot.x - rightFoot.x);
    let stance: 'open' | 'closed' | 'neutral';
    
    if (footDistance > 0.3) {
      stance = 'open';
    } else if (footDistance < 0.1) {
      stance = 'closed';
    } else {
      stance = 'neutral';
    }

    // Calculate weight distribution
    const hipCenter = (leftHip.x + rightHip.x) / 2;
    const footCenter = (leftFoot.x + rightFoot.x) / 2;
    const weightShift = hipCenter - footCenter;
    
    let weight: 'forward' | 'backward' | 'centered';
    if (weightShift > 0.05) {
      weight = 'forward';
    } else if (weightShift < -0.05) {
      weight = 'backward';
    } else {
      weight = 'centered';
    }

    // Calculate balance score
    const balance = Math.max(0, 1 - Math.abs(weightShift) * 10);

    return {
      stance,
      weight,
      balance,
    };
  }

  /**
   * Evaluate technical metrics
   */
  private evaluateTechnicalMetrics(
    landmarks: PoseLandmark[], 
    angles: TennisMovementAnalysis['keyAngles']
  ): TennisMovementAnalysis['technicalMetrics'] {
    const rightWrist = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_WRIST];
    const rightShoulder = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER];

    // Evaluate racket position
    let racketPosition: 'high' | 'medium' | 'low';
    const wristHeight = rightShoulder.y - rightWrist.y;
    
    if (wristHeight > 0.2) {
      racketPosition = 'high';
    } else if (wristHeight > -0.1) {
      racketPosition = 'medium';
    } else {
      racketPosition = 'low';
    }

    // Evaluate follow-through based on arm extension
    let followThrough: 'complete' | 'partial' | 'none';
    if (angles.elbowAngle > 150) {
      followThrough = 'complete';
    } else if (angles.elbowAngle > 120) {
      followThrough = 'partial';
    } else {
      followThrough = 'none';
    }

    // Evaluate footwork based on stance and balance
    let footwork: 'excellent' | 'good' | 'needs_improvement';
    const leftFoot = landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE];
    const rightFoot = landmarks[TENNIS_POSE_LANDMARKS.RIGHT_ANKLE];
    const footStability = 1 - Math.abs(leftFoot.y - rightFoot.y);
    
    if (footStability > 0.9) {
      footwork = 'excellent';
    } else if (footStability > 0.7) {
      footwork = 'good';
    } else {
      footwork = 'needs_improvement';
    }

    return {
      racketPosition,
      followThrough,
      footwork,
    };
  }

  /**
   * Load and configure MediaPipe model
   */
  private async loadMediaPipeModel(): Promise<void> {
    if (!Pose) {
      throw new Error('MediaPipe Pose module not loaded');
    }

    try {
      this.pose = new Pose({
        locateFile: (file: string) => {
          return `https://cdn.jsdelivr.net/npm/@mediapipe/pose/${file}`;
        }
      });

      this.pose.setOptions({
        modelComplexity: this.config.modelComplexity,
        smoothLandmarks: this.config.smoothLandmarks,
        enableSegmentation: this.config.enableSegmentation,
        smoothSegmentation: this.config.smoothSegmentation,
        minDetectionConfidence: this.config.minDetectionConfidence,
        minTrackingConfidence: this.config.minTrackingConfidence,
      });

      console.log('MediaPipe pose model configured successfully');
    } catch (error) {
      console.error('Failed to load MediaPipe model:', error);
      throw error;
    }
  }

  /**
   * Process image with MediaPipe pose detection
   */
  private async processImageWithMediaPipe(imageData: ImageData | HTMLVideoElement | HTMLCanvasElement): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.pose) {
        reject(new Error('MediaPipe pose model not initialized'));
        return;
      }

      // Set up result callback
      this.pose.onResults((results: any) => {
        resolve(results);
      });

      // Process the image
      try {
        this.pose.send({ image: imageData });
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Calculate overall confidence from landmarks
   */
  private calculateOverallConfidence(landmarks: any[]): number {
    if (!landmarks || landmarks.length === 0) return 0;

    const visibilitySum = landmarks.reduce((sum, landmark) =>
      sum + (landmark.visibility || 1), 0
    );

    return visibilitySum / landmarks.length;
  }

  /**
   * Simulate pose detection for development/testing
   */
  private async simulatePoseDetection(frameIndex: number): Promise<PoseDetectionResult> {
    // Generate mock pose landmarks
    const landmarks: PoseLandmark[] = [];
    
    // Generate 33 pose landmarks (MediaPipe standard)
    for (let i = 0; i < 33; i++) {
      landmarks.push({
        x: Math.random() * 0.8 + 0.1, // Keep within reasonable bounds
        y: Math.random() * 0.8 + 0.1,
        z: Math.random() * 0.2 - 0.1,
        visibility: Math.random() * 0.3 + 0.7, // High visibility
      });
    }

    return {
      landmarks,
      confidence: Math.random() * 0.2 + 0.8, // High confidence
      timestamp: Date.now(),
      frameIndex,
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<PoseAnalysisConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  getConfig(): PoseAnalysisConfig {
    return { ...this.config };
  }

  /**
   * Check if service is initialized
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.isInitialized = false;
    if (this.pose) {
      this.pose.close();
      this.pose = null;
    }
    if (this.camera) {
      this.camera.stop();
      this.camera = null;
    }
    console.log('MediaPipe resources cleaned up');
  }

  /**
   * Create camera instance for live video processing
   */
  async createCamera(videoElement: HTMLVideoElement, onResults: (results: any) => void): Promise<void> {
    if (!Camera || !this.pose) {
      throw new Error('MediaPipe not initialized');
    }

    this.pose.onResults(onResults);

    this.camera = new Camera(videoElement, {
      onFrame: async () => {
        await this.pose.send({ image: videoElement });
      },
      width: 640,
      height: 480
    });

    await this.camera.start();
  }

  /**
   * Process video file and extract pose data from all frames
   */
  async processVideoFile(videoFile: File | Blob): Promise<TennisMovementAnalysis[]> {
    const video = document.createElement('video');
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) {
      throw new Error('Could not get canvas context');
    }

    return new Promise((resolve, reject) => {
      video.onloadedmetadata = async () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        const results: TennisMovementAnalysis[] = [];
        const frameRate = 30; // Process every 30th frame for performance
        const duration = video.duration;
        const totalFrames = Math.floor(duration * frameRate);

        for (let i = 0; i < totalFrames; i++) {
          const time = (i / frameRate);
          video.currentTime = time;

          await new Promise(resolve => {
            video.onseeked = resolve;
          });

          // Draw frame to canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // Get image data
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);

          // Detect pose
          const poseResult = await this.detectPose(imageData, i);

          if (poseResult) {
            const analysis = this.analyzeTennisMovement(poseResult);
            results.push(analysis);
          }
        }

        resolve(results);
      };

      video.onerror = reject;
      video.src = URL.createObjectURL(videoFile);
    });
  }
}

// Export singleton instance
export const mediaPipeService = new MediaPipeService();
