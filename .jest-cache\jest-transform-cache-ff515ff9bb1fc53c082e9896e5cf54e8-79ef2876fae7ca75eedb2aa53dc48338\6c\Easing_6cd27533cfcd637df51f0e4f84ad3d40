d1034c751e01cbf36d9b3840cb417ef2
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _bezier2 = _interopRequireDefault(require("./bezier"));
var _ease;
var Easing = function () {
  function Easing() {
    (0, _classCallCheck2.default)(this, Easing);
  }
  return (0, _createClass2.default)(Easing, null, [{
    key: "step0",
    value: function step0(n) {
      return n > 0 ? 1 : 0;
    }
  }, {
    key: "step1",
    value: function step1(n) {
      return n >= 1 ? 1 : 0;
    }
  }, {
    key: "linear",
    value: function linear(t) {
      return t;
    }
  }, {
    key: "ease",
    value: function ease(t) {
      if (!_ease) {
        _ease = Easing.bezier(0.42, 0, 1, 1);
      }
      return _ease(t);
    }
  }, {
    key: "quad",
    value: function quad(t) {
      return t * t;
    }
  }, {
    key: "cubic",
    value: function cubic(t) {
      return t * t * t;
    }
  }, {
    key: "poly",
    value: function poly(n) {
      return function (t) {
        return Math.pow(t, n);
      };
    }
  }, {
    key: "sin",
    value: function sin(t) {
      return 1 - Math.cos(t * Math.PI / 2);
    }
  }, {
    key: "circle",
    value: function circle(t) {
      return 1 - Math.sqrt(1 - t * t);
    }
  }, {
    key: "exp",
    value: function exp(t) {
      return Math.pow(2, 10 * (t - 1));
    }
  }, {
    key: "elastic",
    value: function elastic(bounciness) {
      if (bounciness === void 0) {
        bounciness = 1;
      }
      var p = bounciness * Math.PI;
      return function (t) {
        return 1 - Math.pow(Math.cos(t * Math.PI / 2), 3) * Math.cos(t * p);
      };
    }
  }, {
    key: "back",
    value: function back(s) {
      if (s === void 0) {
        s = 1.70158;
      }
      return function (t) {
        return t * t * ((s + 1) * t - s);
      };
    }
  }, {
    key: "bounce",
    value: function bounce(t) {
      if (t < 1 / 2.75) {
        return 7.5625 * t * t;
      }
      if (t < 2 / 2.75) {
        var _t = t - 1.5 / 2.75;
        return 7.5625 * _t * _t + 0.75;
      }
      if (t < 2.5 / 2.75) {
        var _t2 = t - 2.25 / 2.75;
        return 7.5625 * _t2 * _t2 + 0.9375;
      }
      var t2 = t - 2.625 / 2.75;
      return 7.5625 * t2 * t2 + 0.984375;
    }
  }, {
    key: "bezier",
    value: function bezier(x1, y1, x2, y2) {
      return (0, _bezier2.default)(x1, y1, x2, y2);
    }
  }, {
    key: "in",
    value: function _in(easing) {
      return easing;
    }
  }, {
    key: "out",
    value: function out(easing) {
      return function (t) {
        return 1 - easing(1 - t);
      };
    }
  }, {
    key: "inOut",
    value: function inOut(easing) {
      return function (t) {
        if (t < 0.5) {
          return easing(t * 2) / 2;
        }
        return 1 - easing((1 - t) * 2) / 2;
      };
    }
  }]);
}();
var _default = exports.default = Easing;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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