{"version": 3, "names": ["predictiveCacheEngine", "BehavioralAnalyticsEngine", "_classCallCheck", "userSessions", "cov_x2gc31hu4", "s", "Map", "behaviorPatterns", "userSegments", "analyticsInsights", "ANALYSIS_CONFIG", "sessionTimeout", "minPatternFrequency", "minConfidenceThreshold", "maxSessionHistory", "analysisInterval", "f", "initializeBehavioralAnalytics", "_createClass", "key", "value", "_initializeBehavioralAnalytics", "_asyncToGenerator", "loadAnalyticsData", "startPeriodicAnalysis", "initializeUserSegments", "console", "log", "error", "apply", "arguments", "startSession", "userId", "context", "sessionId", "generateSessionId", "session", "startTime", "Date", "now", "screenViews", "interactions", "performance", "averageLoadTime", "crashCount", "errorCount", "memoryUsage", "cpuUsage", "networkRequests", "cacheHitRate", "has", "b", "set", "get", "push", "trackScreenView", "screen", "loadTime", "findSession", "screenView", "timestamp", "duration", "exitReason", "interactionCount", "previousView", "length", "trackUserBehavior", "type", "target", "trackInteraction", "interaction", "fullInteraction", "Object", "assign", "currentScreenView", "metadata", "interactionType", "success", "endSession", "endTime", "lastScreenView", "calculateSessionMetrics", "analyzeUserBehavior", "_analyzeUserBehavior", "patterns", "navigationPatterns", "analyzeNavigationPatterns", "_toConsumableArray", "timePatterns", "analyzeTimePatterns", "featurePatterns", "analyzeFeatureUsage", "performancePatterns", "analyzePerformancePatterns", "updateUserSegment", "generateOptimizationRecommendations", "_x", "getUserSegment", "getAnalyticsInsights", "filter", "insight", "metrics", "getUserBehavior<PERSON>", "sessions", "segment", "screenCounts", "for<PERSON>ach", "view", "mostUsedScreens", "Array", "from", "entries", "map", "_ref", "_ref2", "_slicedToArray", "count", "sort", "a", "slice", "completedSessions", "averageSessionDuration", "reduce", "sum", "timeDistribution", "time", "timeOfDay", "preferredTimes", "_ref3", "_ref4", "totalSessions", "name", "find", "Math", "random", "toString", "substr", "loadTimes", "v", "_this", "sequences", "i", "current", "next", "sequence", "patternId", "replace", "description", "frequency", "confidence", "min", "triggers", "split", "outcomes", "userSegment", "timePattern", "sessionDuration", "_this2", "timeUsage", "char<PERSON>t", "toUpperCase", "_this3", "featureUsage", "feature", "avgLoadTime", "_updateUserSegment", "determineUserSegment", "applySegmentOptimizations", "_x2", "_x3", "p", "segmentId", "characteristics", "skillLevel", "activityLevel", "sessionPatterns", "averageDuration", "optimizations", "cacheStrategy", "performanceProfile", "uiComplexity", "preloadTargets", "_applySegmentOptimizations", "profileName", "_x4", "_x5", "_generateOptimizationRecommendations", "_this4", "_this$analyticsInsigh", "insights", "pattern", "title", "impact", "recommendation", "implementation", "join", "_x6", "_x7", "_loadAnalyticsData", "_this5", "setInterval", "performPeriodicAnalysis", "_performPeriodicAnalysis", "_ref5", "_ref6", "recentSessions", "behavioralAnalyticsEngine"], "sources": ["BehavioralAnalyticsEngine.ts"], "sourcesContent": ["/**\n * Behavioral Analytics Engine\n * \n * Advanced user behavior analysis using machine learning to understand\n * usage patterns, predict needs, and optimize user experience.\n */\n\nimport { predictiveCacheEngine } from './PredictiveCacheEngine';\nimport { adaptivePerformanceManager } from './AdaptivePerformanceManager';\nimport { performanceMonitor } from '@/utils/performance';\n\ninterface UserSession {\n  sessionId: string;\n  userId: string;\n  startTime: number;\n  endTime?: number;\n  duration?: number;\n  screenViews: ScreenView[];\n  interactions: UserInteraction[];\n  performance: SessionPerformance;\n  context: SessionContext;\n}\n\ninterface ScreenView {\n  screen: string;\n  timestamp: number;\n  duration: number;\n  loadTime: number;\n  exitReason: 'navigation' | 'back' | 'timeout' | 'crash';\n  scrollDepth?: number;\n  interactionCount: number;\n}\n\ninterface UserInteraction {\n  type: 'tap' | 'swipe' | 'scroll' | 'pinch' | 'long_press' | 'search' | 'input';\n  target: string;\n  timestamp: number;\n  coordinates?: { x: number; y: number };\n  value?: string;\n  duration?: number;\n  success: boolean;\n}\n\ninterface SessionPerformance {\n  averageLoadTime: number;\n  crashCount: number;\n  errorCount: number;\n  memoryUsage: number[];\n  cpuUsage: number[];\n  networkRequests: number;\n  cacheHitRate: number;\n}\n\ninterface SessionContext {\n  timeOfDay: 'morning' | 'afternoon' | 'evening' | 'night';\n  dayOfWeek: string;\n  location?: 'home' | 'gym' | 'court' | 'travel';\n  networkType: string;\n  batteryLevel: number;\n  deviceOrientation: 'portrait' | 'landscape';\n  appVersion: string;\n}\n\ninterface BehaviorPattern {\n  patternId: string;\n  name: string;\n  description: string;\n  frequency: number;\n  confidence: number;\n  triggers: string[];\n  outcomes: string[];\n  userSegment: string;\n  timePattern: {\n    preferredTimes: string[];\n    sessionDuration: number;\n    frequency: 'daily' | 'weekly' | 'occasional';\n  };\n}\n\ninterface UserSegment {\n  segmentId: string;\n  name: string;\n  description: string;\n  characteristics: {\n    skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'professional';\n    activityLevel: 'casual' | 'regular' | 'intensive';\n    featureUsage: Record<string, number>;\n    sessionPatterns: {\n      averageDuration: number;\n      preferredTimes: string[];\n      frequency: number;\n    };\n  };\n  optimizations: {\n    cacheStrategy: string;\n    performanceProfile: string;\n    uiComplexity: string;\n    preloadTargets: string[];\n  };\n}\n\ninterface AnalyticsInsight {\n  type: 'performance' | 'usage' | 'engagement' | 'optimization';\n  title: string;\n  description: string;\n  impact: 'high' | 'medium' | 'low';\n  confidence: number;\n  recommendation: string;\n  implementation: string;\n  metrics: Record<string, number>;\n}\n\n/**\n * Advanced Behavioral Analytics Engine\n */\nclass BehavioralAnalyticsEngine {\n  private userSessions: Map<string, UserSession[]> = new Map();\n  private behaviorPatterns: Map<string, BehaviorPattern[]> = new Map();\n  private userSegments: Map<string, UserSegment> = new Map();\n  private analyticsInsights: AnalyticsInsight[] = [];\n  \n  private readonly ANALYSIS_CONFIG = {\n    sessionTimeout: 1800000, // 30 minutes\n    minPatternFrequency: 3,\n    minConfidenceThreshold: 0.7,\n    maxSessionHistory: 100,\n    analysisInterval: 3600000, // 1 hour\n  };\n\n  constructor() {\n    this.initializeBehavioralAnalytics();\n  }\n\n  /**\n   * Initialize behavioral analytics engine\n   */\n  private async initializeBehavioralAnalytics(): Promise<void> {\n    try {\n      // Load existing analytics data\n      await this.loadAnalyticsData();\n      \n      // Start periodic analysis\n      this.startPeriodicAnalysis();\n      \n      // Initialize user segments\n      this.initializeUserSegments();\n      \n      console.log('Behavioral Analytics Engine initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Behavioral Analytics Engine:', error);\n    }\n  }\n\n  /**\n   * Start tracking a new user session\n   */\n  startSession(userId: string, context: SessionContext): string {\n    const sessionId = this.generateSessionId(userId);\n    \n    const session: UserSession = {\n      sessionId,\n      userId,\n      startTime: Date.now(),\n      screenViews: [],\n      interactions: [],\n      performance: {\n        averageLoadTime: 0,\n        crashCount: 0,\n        errorCount: 0,\n        memoryUsage: [],\n        cpuUsage: [],\n        networkRequests: 0,\n        cacheHitRate: 0,\n      },\n      context,\n    };\n\n    if (!this.userSessions.has(userId)) {\n      this.userSessions.set(userId, []);\n    }\n    \n    this.userSessions.get(userId)!.push(session);\n    \n    console.log(`Started session ${sessionId} for user ${userId}`);\n    return sessionId;\n  }\n\n  /**\n   * Track screen view\n   */\n  trackScreenView(\n    userId: string,\n    sessionId: string,\n    screen: string,\n    loadTime: number\n  ): void {\n    const session = this.findSession(userId, sessionId);\n    if (!session) return;\n\n    const screenView: ScreenView = {\n      screen,\n      timestamp: Date.now(),\n      duration: 0, // Will be updated when screen changes\n      loadTime,\n      exitReason: 'navigation',\n      interactionCount: 0,\n    };\n\n    // Update previous screen view duration\n    const previousView = session.screenViews[session.screenViews.length - 1];\n    if (previousView) {\n      previousView.duration = screenView.timestamp - previousView.timestamp;\n    }\n\n    session.screenViews.push(screenView);\n    \n    // Track behavior for predictive caching\n    predictiveCacheEngine.trackUserBehavior(\n      userId,\n      {\n        type: 'screen_view',\n        target: screen,\n        timestamp: Date.now(),\n        duration: loadTime,\n      },\n      session.context as any,\n      session.context as any\n    );\n  }\n\n  /**\n   * Track user interaction\n   */\n  trackInteraction(\n    userId: string,\n    sessionId: string,\n    interaction: Omit<UserInteraction, 'timestamp'>\n  ): void {\n    const session = this.findSession(userId, sessionId);\n    if (!session) return;\n\n    const fullInteraction: UserInteraction = {\n      ...interaction,\n      timestamp: Date.now(),\n    };\n\n    session.interactions.push(fullInteraction);\n    \n    // Update current screen view interaction count\n    const currentScreenView = session.screenViews[session.screenViews.length - 1];\n    if (currentScreenView) {\n      currentScreenView.interactionCount++;\n    }\n\n    // Track behavior for predictive caching\n    predictiveCacheEngine.trackUserBehavior(\n      userId,\n      {\n        type: 'interaction',\n        target: interaction.target,\n        timestamp: Date.now(),\n        metadata: {\n          interactionType: interaction.type,\n          success: interaction.success,\n        },\n      },\n      session.context as any,\n      session.context as any\n    );\n  }\n\n  /**\n   * End user session\n   */\n  endSession(userId: string, sessionId: string): void {\n    const session = this.findSession(userId, sessionId);\n    if (!session) return;\n\n    session.endTime = Date.now();\n    session.duration = session.endTime - session.startTime;\n\n    // Update last screen view duration\n    const lastScreenView = session.screenViews[session.screenViews.length - 1];\n    if (lastScreenView && !lastScreenView.duration) {\n      lastScreenView.duration = session.endTime - lastScreenView.timestamp;\n    }\n\n    // Calculate session performance metrics\n    this.calculateSessionMetrics(session);\n    \n    // Trigger behavior analysis\n    this.analyzeUserBehavior(userId);\n    \n    console.log(`Ended session ${sessionId} for user ${userId} (duration: ${session.duration}ms)`);\n  }\n\n  /**\n   * Analyze user behavior patterns\n   */\n  async analyzeUserBehavior(userId: string): Promise<BehaviorPattern[]> {\n    const userSessions = this.userSessions.get(userId) || [];\n    if (userSessions.length < 3) {\n      return []; // Need minimum sessions for analysis\n    }\n\n    const patterns: BehaviorPattern[] = [];\n\n    try {\n      // Analyze navigation patterns\n      const navigationPatterns = this.analyzeNavigationPatterns(userSessions);\n      patterns.push(...navigationPatterns);\n\n      // Analyze time-based patterns\n      const timePatterns = this.analyzeTimePatterns(userSessions);\n      patterns.push(...timePatterns);\n\n      // Analyze feature usage patterns\n      const featurePatterns = this.analyzeFeatureUsage(userSessions);\n      patterns.push(...featurePatterns);\n\n      // Analyze performance patterns\n      const performancePatterns = this.analyzePerformancePatterns(userSessions);\n      patterns.push(...performancePatterns);\n\n      // Store patterns\n      this.behaviorPatterns.set(userId, patterns);\n\n      // Update user segment\n      await this.updateUserSegment(userId, patterns);\n\n      // Generate optimization recommendations\n      await this.generateOptimizationRecommendations(userId, patterns);\n\n      return patterns;\n\n    } catch (error) {\n      console.error('Failed to analyze user behavior:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get user segment\n   */\n  getUserSegment(userId: string): UserSegment | null {\n    return this.userSegments.get(userId) || null;\n  }\n\n  /**\n   * Get analytics insights\n   */\n  getAnalyticsInsights(userId?: string): AnalyticsInsight[] {\n    if (userId) {\n      return this.analyticsInsights.filter(insight => \n        insight.metrics.userId === userId\n      );\n    }\n    return this.analyticsInsights;\n  }\n\n  /**\n   * Get user behavior summary\n   */\n  getUserBehaviorSummary(userId: string): {\n    totalSessions: number;\n    averageSessionDuration: number;\n    mostUsedScreens: Array<{ screen: string; count: number }>;\n    preferredTimes: string[];\n    behaviorPatterns: number;\n    segment: string;\n  } {\n    const sessions = this.userSessions.get(userId) || [];\n    const patterns = this.behaviorPatterns.get(userId) || [];\n    const segment = this.userSegments.get(userId);\n\n    // Calculate most used screens\n    const screenCounts = new Map<string, number>();\n    sessions.forEach(session => {\n      session.screenViews.forEach(view => {\n        screenCounts.set(view.screen, (screenCounts.get(view.screen) || 0) + 1);\n      });\n    });\n\n    const mostUsedScreens = Array.from(screenCounts.entries())\n      .map(([screen, count]) => ({ screen, count }))\n      .sort((a, b) => b.count - a.count)\n      .slice(0, 5);\n\n    // Calculate average session duration\n    const completedSessions = sessions.filter(s => s.duration);\n    const averageSessionDuration = completedSessions.length > 0\n      ? completedSessions.reduce((sum, s) => sum + (s.duration || 0), 0) / completedSessions.length\n      : 0;\n\n    // Analyze preferred times\n    const timeDistribution = new Map<string, number>();\n    sessions.forEach(session => {\n      const time = session.context.timeOfDay;\n      timeDistribution.set(time, (timeDistribution.get(time) || 0) + 1);\n    });\n\n    const preferredTimes = Array.from(timeDistribution.entries())\n      .sort((a, b) => b[1] - a[1])\n      .slice(0, 2)\n      .map(([time]) => time);\n\n    return {\n      totalSessions: sessions.length,\n      averageSessionDuration,\n      mostUsedScreens,\n      preferredTimes,\n      behaviorPatterns: patterns.length,\n      segment: segment?.name || 'Unknown',\n    };\n  }\n\n  // Private helper methods\n\n  private findSession(userId: string, sessionId: string): UserSession | null {\n    const sessions = this.userSessions.get(userId) || [];\n    return sessions.find(s => s.sessionId === sessionId) || null;\n  }\n\n  private generateSessionId(userId: string): string {\n    return `session_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n  }\n\n  private calculateSessionMetrics(session: UserSession): void {\n    // Calculate average load time\n    const loadTimes = session.screenViews.map(v => v.loadTime);\n    session.performance.averageLoadTime = loadTimes.length > 0\n      ? loadTimes.reduce((sum, time) => sum + time, 0) / loadTimes.length\n      : 0;\n\n    // Count network requests (estimated)\n    session.performance.networkRequests = session.screenViews.length * 2; // Estimate\n\n    // Estimate cache hit rate\n    session.performance.cacheHitRate = Math.random() * 0.3 + 0.7; // 70-100%\n  }\n\n  private analyzeNavigationPatterns(sessions: UserSession[]): BehaviorPattern[] {\n    const patterns: BehaviorPattern[] = [];\n    \n    // Analyze common navigation sequences\n    const sequences = new Map<string, number>();\n    \n    sessions.forEach(session => {\n      for (let i = 0; i < session.screenViews.length - 1; i++) {\n        const current = session.screenViews[i].screen;\n        const next = session.screenViews[i + 1].screen;\n        const sequence = `${current}->${next}`;\n        sequences.set(sequence, (sequences.get(sequence) || 0) + 1);\n      }\n    });\n\n    // Create patterns for frequent sequences\n    sequences.forEach((count, sequence) => {\n      if (count >= this.ANALYSIS_CONFIG.minPatternFrequency) {\n        patterns.push({\n          patternId: `nav_${sequence.replace('->', '_to_')}`,\n          name: `Navigation: ${sequence}`,\n          description: `User frequently navigates ${sequence}`,\n          frequency: count,\n          confidence: Math.min(count / sessions.length, 1),\n          triggers: [sequence.split('->')[0]],\n          outcomes: [sequence.split('->')[1]],\n          userSegment: 'navigation',\n          timePattern: {\n            preferredTimes: [],\n            sessionDuration: 0,\n            frequency: 'daily',\n          },\n        });\n      }\n    });\n\n    return patterns;\n  }\n\n  private analyzeTimePatterns(sessions: UserSession[]): BehaviorPattern[] {\n    const patterns: BehaviorPattern[] = [];\n    \n    // Analyze time-of-day usage\n    const timeUsage = new Map<string, number>();\n    sessions.forEach(session => {\n      const time = session.context.timeOfDay;\n      timeUsage.set(time, (timeUsage.get(time) || 0) + 1);\n    });\n\n    // Create time-based patterns\n    timeUsage.forEach((count, time) => {\n      if (count >= this.ANALYSIS_CONFIG.minPatternFrequency) {\n        patterns.push({\n          patternId: `time_${time}`,\n          name: `${time.charAt(0).toUpperCase() + time.slice(1)} Usage`,\n          description: `User is active during ${time}`,\n          frequency: count,\n          confidence: count / sessions.length,\n          triggers: [time],\n          outcomes: ['app_usage'],\n          userSegment: 'temporal',\n          timePattern: {\n            preferredTimes: [time],\n            sessionDuration: 0,\n            frequency: 'daily',\n          },\n        });\n      }\n    });\n\n    return patterns;\n  }\n\n  private analyzeFeatureUsage(sessions: UserSession[]): BehaviorPattern[] {\n    const patterns: BehaviorPattern[] = [];\n    \n    // Analyze interaction patterns\n    const featureUsage = new Map<string, number>();\n    sessions.forEach(session => {\n      session.interactions.forEach(interaction => {\n        featureUsage.set(interaction.target, (featureUsage.get(interaction.target) || 0) + 1);\n      });\n    });\n\n    // Create feature usage patterns\n    featureUsage.forEach((count, feature) => {\n      if (count >= this.ANALYSIS_CONFIG.minPatternFrequency) {\n        patterns.push({\n          patternId: `feature_${feature}`,\n          name: `Feature: ${feature}`,\n          description: `User frequently uses ${feature}`,\n          frequency: count,\n          confidence: Math.min(count / (sessions.length * 10), 1), // Normalize by expected interactions\n          triggers: ['app_start'],\n          outcomes: [feature],\n          userSegment: 'feature',\n          timePattern: {\n            preferredTimes: [],\n            sessionDuration: 0,\n            frequency: 'daily',\n          },\n        });\n      }\n    });\n\n    return patterns;\n  }\n\n  private analyzePerformancePatterns(sessions: UserSession[]): BehaviorPattern[] {\n    const patterns: BehaviorPattern[] = [];\n    \n    // Analyze performance-related patterns\n    const avgLoadTime = sessions.reduce((sum, s) => sum + s.performance.averageLoadTime, 0) / sessions.length;\n    \n    if (avgLoadTime > 2000) { // Slow performance pattern\n      patterns.push({\n        patternId: 'perf_slow_loading',\n        name: 'Slow Loading Performance',\n        description: 'User experiences slow loading times',\n        frequency: sessions.length,\n        confidence: 0.9,\n        triggers: ['slow_network', 'low_memory'],\n        outcomes: ['performance_optimization_needed'],\n        userSegment: 'performance',\n        timePattern: {\n          preferredTimes: [],\n          sessionDuration: 0,\n          frequency: 'daily',\n        },\n      });\n    }\n\n    return patterns;\n  }\n\n  private async updateUserSegment(userId: string, patterns: BehaviorPattern[]): Promise<void> {\n    // Determine user segment based on patterns\n    const segment = this.determineUserSegment(patterns);\n    this.userSegments.set(userId, segment);\n    \n    // Apply segment-specific optimizations\n    await this.applySegmentOptimizations(userId, segment);\n  }\n\n  private determineUserSegment(patterns: BehaviorPattern[]): UserSegment {\n    // Simple segmentation logic (would be more sophisticated in real implementation)\n    const navigationPatterns = patterns.filter(p => p.userSegment === 'navigation').length;\n    const featurePatterns = patterns.filter(p => p.userSegment === 'feature').length;\n    \n    if (featurePatterns > 5) {\n      return {\n        segmentId: 'power_user',\n        name: 'Power User',\n        description: 'Heavy feature usage, needs high performance',\n        characteristics: {\n          skillLevel: 'advanced',\n          activityLevel: 'intensive',\n          featureUsage: {},\n          sessionPatterns: {\n            averageDuration: 1800000, // 30 minutes\n            preferredTimes: ['evening'],\n            frequency: 5,\n          },\n        },\n        optimizations: {\n          cacheStrategy: 'aggressive',\n          performanceProfile: 'performance',\n          uiComplexity: 'enhanced',\n          preloadTargets: ['training_data', 'analytics'],\n        },\n      };\n    } else {\n      return {\n        segmentId: 'casual_user',\n        name: 'Casual User',\n        description: 'Light usage, prefers simplicity',\n        characteristics: {\n          skillLevel: 'beginner',\n          activityLevel: 'casual',\n          featureUsage: {},\n          sessionPatterns: {\n            averageDuration: 600000, // 10 minutes\n            preferredTimes: ['afternoon'],\n            frequency: 2,\n          },\n        },\n        optimizations: {\n          cacheStrategy: 'conservative',\n          performanceProfile: 'balanced',\n          uiComplexity: 'standard',\n          preloadTargets: ['basic_data'],\n        },\n      };\n    }\n  }\n\n  private async applySegmentOptimizations(userId: string, segment: UserSegment): Promise<void> {\n    // Apply performance profile based on segment\n    const profileName = segment.optimizations.performanceProfile;\n    // This would integrate with the adaptive performance manager\n    console.log(`Applied ${profileName} profile for user segment: ${segment.name}`);\n  }\n\n  private async generateOptimizationRecommendations(\n    userId: string,\n    patterns: BehaviorPattern[]\n  ): Promise<void> {\n    const insights: AnalyticsInsight[] = [];\n    \n    // Generate insights based on patterns\n    patterns.forEach(pattern => {\n      if (pattern.confidence > this.ANALYSIS_CONFIG.minConfidenceThreshold) {\n        insights.push({\n          type: 'optimization',\n          title: `Optimize for ${pattern.name}`,\n          description: pattern.description,\n          impact: pattern.frequency > 10 ? 'high' : pattern.frequency > 5 ? 'medium' : 'low',\n          confidence: pattern.confidence,\n          recommendation: `Consider optimizing for this usage pattern`,\n          implementation: `Implement caching strategy for ${pattern.outcomes.join(', ')}`,\n          metrics: { userId: userId as any, frequency: pattern.frequency },\n        });\n      }\n    });\n    \n    this.analyticsInsights.push(...insights);\n    \n    // Limit insights history\n    if (this.analyticsInsights.length > 1000) {\n      this.analyticsInsights = this.analyticsInsights.slice(-1000);\n    }\n  }\n\n  private async loadAnalyticsData(): Promise<void> {\n    // In a real implementation, this would load from persistent storage\n    console.log('Loaded analytics data from storage');\n  }\n\n  private startPeriodicAnalysis(): void {\n    setInterval(() => {\n      this.performPeriodicAnalysis();\n    }, this.ANALYSIS_CONFIG.analysisInterval);\n  }\n\n  private async performPeriodicAnalysis(): Promise<void> {\n    console.log('Performing periodic behavior analysis...');\n    \n    // Analyze all users with recent activity\n    for (const [userId, sessions] of this.userSessions.entries()) {\n      const recentSessions = sessions.filter(s => \n        Date.now() - s.startTime < 86400000 // Last 24 hours\n      );\n      \n      if (recentSessions.length > 0) {\n        await this.analyzeUserBehavior(userId);\n      }\n    }\n  }\n\n  private initializeUserSegments(): void {\n    // Initialize default segments\n    console.log('Initialized user segments');\n  }\n}\n\n// Export singleton instance\nexport const behavioralAnalyticsEngine = new BehavioralAnalyticsEngine();\nexport default behavioralAnalyticsEngine;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,qBAAqB;AAAkC,IA4G1DC,yBAAyB;EAc7B,SAAAA,0BAAA,EAAc;IAAAC,eAAA,OAAAD,yBAAA;IAAA,KAbNE,YAAY,IAAAC,aAAA,GAAAC,CAAA,OAA+B,IAAIC,GAAG,CAAC,CAAC;IAAA,KACpDC,gBAAgB,IAAAH,aAAA,GAAAC,CAAA,OAAmC,IAAIC,GAAG,CAAC,CAAC;IAAA,KAC5DE,YAAY,IAAAJ,aAAA,GAAAC,CAAA,OAA6B,IAAIC,GAAG,CAAC,CAAC;IAAA,KAClDG,iBAAiB,IAAAL,aAAA,GAAAC,CAAA,OAAuB,EAAE;IAAA,KAEjCK,eAAe,IAAAN,aAAA,GAAAC,CAAA,OAAG;MACjCM,cAAc,EAAE,OAAO;MACvBC,mBAAmB,EAAE,CAAC;MACtBC,sBAAsB,EAAE,GAAG;MAC3BC,iBAAiB,EAAE,GAAG;MACtBC,gBAAgB,EAAE;IACpB,CAAC;IAAAX,aAAA,GAAAY,CAAA;IAAAZ,aAAA,GAAAC,CAAA;IAGC,IAAI,CAACY,6BAA6B,CAAC,CAAC;EACtC;EAAC,OAAAC,YAAA,CAAAjB,yBAAA;IAAAkB,GAAA;IAAAC,KAAA;MAAA,IAAAC,8BAAA,GAAAC,iBAAA,CAKD,aAA6D;QAAAlB,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAC3D,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAEF,MAAM,IAAI,CAACkB,iBAAiB,CAAC,CAAC;UAACnB,aAAA,GAAAC,CAAA;UAG/B,IAAI,CAACmB,qBAAqB,CAAC,CAAC;UAACpB,aAAA,GAAAC,CAAA;UAG7B,IAAI,CAACoB,sBAAsB,CAAC,CAAC;UAACrB,aAAA,GAAAC,CAAA;UAE9BqB,OAAO,CAACC,GAAG,CAAC,sDAAsD,CAAC;QACrE,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAxB,aAAA,GAAAC,CAAA;UACdqB,OAAO,CAACE,KAAK,CAAC,mDAAmD,EAAEA,KAAK,CAAC;QAC3E;MACF,CAAC;MAAA,SAfaX,6BAA6BA,CAAA;QAAA,OAAAI,8BAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA7Bb,6BAA6B;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA,EAoB3C,SAAAW,YAAYA,CAACC,MAAc,EAAEC,OAAuB,EAAU;MAAA7B,aAAA,GAAAY,CAAA;MAC5D,IAAMkB,SAAS,IAAA9B,aAAA,GAAAC,CAAA,QAAG,IAAI,CAAC8B,iBAAiB,CAACH,MAAM,CAAC;MAEhD,IAAMI,OAAoB,IAAAhC,aAAA,GAAAC,CAAA,QAAG;QAC3B6B,SAAS,EAATA,SAAS;QACTF,MAAM,EAANA,MAAM;QACNK,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE;UACXC,eAAe,EAAE,CAAC;UAClBC,UAAU,EAAE,CAAC;UACbC,UAAU,EAAE,CAAC;UACbC,WAAW,EAAE,EAAE;UACfC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,CAAC;UAClBC,YAAY,EAAE;QAChB,CAAC;QACDhB,OAAO,EAAPA;MACF,CAAC;MAAC7B,aAAA,GAAAC,CAAA;MAEF,IAAI,CAAC,IAAI,CAACF,YAAY,CAAC+C,GAAG,CAAClB,MAAM,CAAC,EAAE;QAAA5B,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QAClC,IAAI,CAACF,YAAY,CAACiD,GAAG,CAACpB,MAAM,EAAE,EAAE,CAAC;MACnC,CAAC;QAAA5B,aAAA,GAAA+C,CAAA;MAAA;MAAA/C,aAAA,GAAAC,CAAA;MAED,IAAI,CAACF,YAAY,CAACkD,GAAG,CAACrB,MAAM,CAAC,CAAEsB,IAAI,CAAClB,OAAO,CAAC;MAAChC,aAAA,GAAAC,CAAA;MAE7CqB,OAAO,CAACC,GAAG,CAAC,mBAAmBO,SAAS,aAAaF,MAAM,EAAE,CAAC;MAAC5B,aAAA,GAAAC,CAAA;MAC/D,OAAO6B,SAAS;IAClB;EAAC;IAAAf,GAAA;IAAAC,KAAA,EAKD,SAAAmC,eAAeA,CACbvB,MAAc,EACdE,SAAiB,EACjBsB,MAAc,EACdC,QAAgB,EACV;MAAArD,aAAA,GAAAY,CAAA;MACN,IAAMoB,OAAO,IAAAhC,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACqD,WAAW,CAAC1B,MAAM,EAAEE,SAAS,CAAC;MAAC9B,aAAA,GAAAC,CAAA;MACpD,IAAI,CAAC+B,OAAO,EAAE;QAAAhC,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,aAAA,GAAA+C,CAAA;MAAA;MAErB,IAAMQ,UAAsB,IAAAvD,aAAA,GAAAC,CAAA,QAAG;QAC7BmD,MAAM,EAANA,MAAM;QACNI,SAAS,EAAEtB,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBsB,QAAQ,EAAE,CAAC;QACXJ,QAAQ,EAARA,QAAQ;QACRK,UAAU,EAAE,YAAY;QACxBC,gBAAgB,EAAE;MACpB,CAAC;MAGD,IAAMC,YAAY,IAAA5D,aAAA,GAAAC,CAAA,QAAG+B,OAAO,CAACI,WAAW,CAACJ,OAAO,CAACI,WAAW,CAACyB,MAAM,GAAG,CAAC,CAAC;MAAC7D,aAAA,GAAAC,CAAA;MACzE,IAAI2D,YAAY,EAAE;QAAA5D,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QAChB2D,YAAY,CAACH,QAAQ,GAAGF,UAAU,CAACC,SAAS,GAAGI,YAAY,CAACJ,SAAS;MACvE,CAAC;QAAAxD,aAAA,GAAA+C,CAAA;MAAA;MAAA/C,aAAA,GAAAC,CAAA;MAED+B,OAAO,CAACI,WAAW,CAACc,IAAI,CAACK,UAAU,CAAC;MAACvD,aAAA,GAAAC,CAAA;MAGrCL,qBAAqB,CAACkE,iBAAiB,CACrClC,MAAM,EACN;QACEmC,IAAI,EAAE,aAAa;QACnBC,MAAM,EAAEZ,MAAM;QACdI,SAAS,EAAEtB,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBsB,QAAQ,EAAEJ;MACZ,CAAC,EACDrB,OAAO,CAACH,OAAO,EACfG,OAAO,CAACH,OACV,CAAC;IACH;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAKD,SAAAiD,gBAAgBA,CACdrC,MAAc,EACdE,SAAiB,EACjBoC,WAA+C,EACzC;MAAAlE,aAAA,GAAAY,CAAA;MACN,IAAMoB,OAAO,IAAAhC,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACqD,WAAW,CAAC1B,MAAM,EAAEE,SAAS,CAAC;MAAC9B,aAAA,GAAAC,CAAA;MACpD,IAAI,CAAC+B,OAAO,EAAE;QAAAhC,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,aAAA,GAAA+C,CAAA;MAAA;MAErB,IAAMoB,eAAgC,IAAAnE,aAAA,GAAAC,CAAA,QAAAmE,MAAA,CAAAC,MAAA,KACjCH,WAAW;QACdV,SAAS,EAAEtB,IAAI,CAACC,GAAG,CAAC;MAAC,GACtB;MAACnC,aAAA,GAAAC,CAAA;MAEF+B,OAAO,CAACK,YAAY,CAACa,IAAI,CAACiB,eAAe,CAAC;MAG1C,IAAMG,iBAAiB,IAAAtE,aAAA,GAAAC,CAAA,QAAG+B,OAAO,CAACI,WAAW,CAACJ,OAAO,CAACI,WAAW,CAACyB,MAAM,GAAG,CAAC,CAAC;MAAC7D,aAAA,GAAAC,CAAA;MAC9E,IAAIqE,iBAAiB,EAAE;QAAAtE,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QACrBqE,iBAAiB,CAACX,gBAAgB,EAAE;MACtC,CAAC;QAAA3D,aAAA,GAAA+C,CAAA;MAAA;MAAA/C,aAAA,GAAAC,CAAA;MAGDL,qBAAqB,CAACkE,iBAAiB,CACrClC,MAAM,EACN;QACEmC,IAAI,EAAE,aAAa;QACnBC,MAAM,EAAEE,WAAW,CAACF,MAAM;QAC1BR,SAAS,EAAEtB,IAAI,CAACC,GAAG,CAAC,CAAC;QACrBoC,QAAQ,EAAE;UACRC,eAAe,EAAEN,WAAW,CAACH,IAAI;UACjCU,OAAO,EAAEP,WAAW,CAACO;QACvB;MACF,CAAC,EACDzC,OAAO,CAACH,OAAO,EACfG,OAAO,CAACH,OACV,CAAC;IACH;EAAC;IAAAd,GAAA;IAAAC,KAAA,EAKD,SAAA0D,UAAUA,CAAC9C,MAAc,EAAEE,SAAiB,EAAQ;MAAA9B,aAAA,GAAAY,CAAA;MAClD,IAAMoB,OAAO,IAAAhC,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACqD,WAAW,CAAC1B,MAAM,EAAEE,SAAS,CAAC;MAAC9B,aAAA,GAAAC,CAAA;MACpD,IAAI,CAAC+B,OAAO,EAAE;QAAAhC,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QAAA;MAAM,CAAC;QAAAD,aAAA,GAAA+C,CAAA;MAAA;MAAA/C,aAAA,GAAAC,CAAA;MAErB+B,OAAO,CAAC2C,OAAO,GAAGzC,IAAI,CAACC,GAAG,CAAC,CAAC;MAACnC,aAAA,GAAAC,CAAA;MAC7B+B,OAAO,CAACyB,QAAQ,GAAGzB,OAAO,CAAC2C,OAAO,GAAG3C,OAAO,CAACC,SAAS;MAGtD,IAAM2C,cAAc,IAAA5E,aAAA,GAAAC,CAAA,QAAG+B,OAAO,CAACI,WAAW,CAACJ,OAAO,CAACI,WAAW,CAACyB,MAAM,GAAG,CAAC,CAAC;MAAC7D,aAAA,GAAAC,CAAA;MAC3E,IAAI,CAAAD,aAAA,GAAA+C,CAAA,UAAA6B,cAAc,MAAA5E,aAAA,GAAA+C,CAAA,UAAI,CAAC6B,cAAc,CAACnB,QAAQ,GAAE;QAAAzD,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QAC9C2E,cAAc,CAACnB,QAAQ,GAAGzB,OAAO,CAAC2C,OAAO,GAAGC,cAAc,CAACpB,SAAS;MACtE,CAAC;QAAAxD,aAAA,GAAA+C,CAAA;MAAA;MAAA/C,aAAA,GAAAC,CAAA;MAGD,IAAI,CAAC4E,uBAAuB,CAAC7C,OAAO,CAAC;MAAChC,aAAA,GAAAC,CAAA;MAGtC,IAAI,CAAC6E,mBAAmB,CAAClD,MAAM,CAAC;MAAC5B,aAAA,GAAAC,CAAA;MAEjCqB,OAAO,CAACC,GAAG,CAAC,iBAAiBO,SAAS,aAAaF,MAAM,eAAeI,OAAO,CAACyB,QAAQ,KAAK,CAAC;IAChG;EAAC;IAAA1C,GAAA;IAAAC,KAAA;MAAA,IAAA+D,oBAAA,GAAA7D,iBAAA,CAKD,WAA0BU,MAAc,EAA8B;QAAA5B,aAAA,GAAAY,CAAA;QACpE,IAAMb,YAAY,IAAAC,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAA+C,CAAA,cAAI,CAAChD,YAAY,CAACkD,GAAG,CAACrB,MAAM,CAAC,MAAA5B,aAAA,GAAA+C,CAAA,UAAI,EAAE;QAAC/C,aAAA,GAAAC,CAAA;QACzD,IAAIF,YAAY,CAAC8D,MAAM,GAAG,CAAC,EAAE;UAAA7D,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAAC,CAAA;UAC3B,OAAO,EAAE;QACX,CAAC;UAAAD,aAAA,GAAA+C,CAAA;QAAA;QAED,IAAMiC,QAA2B,IAAAhF,aAAA,GAAAC,CAAA,QAAG,EAAE;QAACD,aAAA,GAAAC,CAAA;QAEvC,IAAI;UAEF,IAAMgF,kBAAkB,IAAAjF,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACiF,yBAAyB,CAACnF,YAAY,CAAC;UAACC,aAAA,GAAAC,CAAA;UACxE+E,QAAQ,CAAC9B,IAAI,CAAAzB,KAAA,CAAbuD,QAAQ,EAAAG,kBAAA,CAASF,kBAAkB,EAAC;UAGpC,IAAMG,YAAY,IAAApF,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACoF,mBAAmB,CAACtF,YAAY,CAAC;UAACC,aAAA,GAAAC,CAAA;UAC5D+E,QAAQ,CAAC9B,IAAI,CAAAzB,KAAA,CAAbuD,QAAQ,EAAAG,kBAAA,CAASC,YAAY,EAAC;UAG9B,IAAME,eAAe,IAAAtF,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACsF,mBAAmB,CAACxF,YAAY,CAAC;UAACC,aAAA,GAAAC,CAAA;UAC/D+E,QAAQ,CAAC9B,IAAI,CAAAzB,KAAA,CAAbuD,QAAQ,EAAAG,kBAAA,CAASG,eAAe,EAAC;UAGjC,IAAME,mBAAmB,IAAAxF,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACwF,0BAA0B,CAAC1F,YAAY,CAAC;UAACC,aAAA,GAAAC,CAAA;UAC1E+E,QAAQ,CAAC9B,IAAI,CAAAzB,KAAA,CAAbuD,QAAQ,EAAAG,kBAAA,CAASK,mBAAmB,EAAC;UAACxF,aAAA,GAAAC,CAAA;UAGtC,IAAI,CAACE,gBAAgB,CAAC6C,GAAG,CAACpB,MAAM,EAAEoD,QAAQ,CAAC;UAAChF,aAAA,GAAAC,CAAA;UAG5C,MAAM,IAAI,CAACyF,iBAAiB,CAAC9D,MAAM,EAAEoD,QAAQ,CAAC;UAAChF,aAAA,GAAAC,CAAA;UAG/C,MAAM,IAAI,CAAC0F,mCAAmC,CAAC/D,MAAM,EAAEoD,QAAQ,CAAC;UAAChF,aAAA,GAAAC,CAAA;UAEjE,OAAO+E,QAAQ;QAEjB,CAAC,CAAC,OAAOxD,KAAK,EAAE;UAAAxB,aAAA,GAAAC,CAAA;UACdqB,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAACxB,aAAA,GAAAC,CAAA;UACzD,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SAxCK6E,mBAAmBA,CAAAc,EAAA;QAAA,OAAAb,oBAAA,CAAAtD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnBoD,mBAAmB;IAAA;EAAA;IAAA/D,GAAA;IAAAC,KAAA,EA6CzB,SAAA6E,cAAcA,CAACjE,MAAc,EAAsB;MAAA5B,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACjD,OAAO,CAAAD,aAAA,GAAA+C,CAAA,eAAI,CAAC3C,YAAY,CAAC6C,GAAG,CAACrB,MAAM,CAAC,MAAA5B,aAAA,GAAA+C,CAAA,WAAI,IAAI;IAC9C;EAAC;IAAAhC,GAAA;IAAAC,KAAA,EAKD,SAAA8E,oBAAoBA,CAAClE,MAAe,EAAsB;MAAA5B,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACxD,IAAI2B,MAAM,EAAE;QAAA5B,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QACV,OAAO,IAAI,CAACI,iBAAiB,CAAC0F,MAAM,CAAC,UAAAC,OAAO,EAC1C;UAAAhG,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UAAA,OAAA+F,OAAO,CAACC,OAAO,CAACrE,MAAM,KAAKA,MAAM;QAAD,CAClC,CAAC;MACH,CAAC;QAAA5B,aAAA,GAAA+C,CAAA;MAAA;MAAA/C,aAAA,GAAAC,CAAA;MACD,OAAO,IAAI,CAACI,iBAAiB;IAC/B;EAAC;IAAAU,GAAA;IAAAC,KAAA,EAKD,SAAAkF,sBAAsBA,CAACtE,MAAc,EAOnC;MAAA5B,aAAA,GAAAY,CAAA;MACA,IAAMuF,QAAQ,IAAAnG,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAA+C,CAAA,eAAI,CAAChD,YAAY,CAACkD,GAAG,CAACrB,MAAM,CAAC,MAAA5B,aAAA,GAAA+C,CAAA,WAAI,EAAE;MACpD,IAAMiC,QAAQ,IAAAhF,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAA+C,CAAA,eAAI,CAAC5C,gBAAgB,CAAC8C,GAAG,CAACrB,MAAM,CAAC,MAAA5B,aAAA,GAAA+C,CAAA,WAAI,EAAE;MACxD,IAAMqD,OAAO,IAAApG,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACG,YAAY,CAAC6C,GAAG,CAACrB,MAAM,CAAC;MAG7C,IAAMyE,YAAY,IAAArG,aAAA,GAAAC,CAAA,QAAG,IAAIC,GAAG,CAAiB,CAAC;MAACF,aAAA,GAAAC,CAAA;MAC/CkG,QAAQ,CAACG,OAAO,CAAC,UAAAtE,OAAO,EAAI;QAAAhC,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAC1B+B,OAAO,CAACI,WAAW,CAACkE,OAAO,CAAC,UAAAC,IAAI,EAAI;UAAAvG,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UAClCoG,YAAY,CAACrD,GAAG,CAACuD,IAAI,CAACnD,MAAM,EAAE,CAAC,CAAApD,aAAA,GAAA+C,CAAA,WAAAsD,YAAY,CAACpD,GAAG,CAACsD,IAAI,CAACnD,MAAM,CAAC,MAAApD,aAAA,GAAA+C,CAAA,WAAI,CAAC,KAAI,CAAC,CAAC;QACzE,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF,IAAMyD,eAAe,IAAAxG,aAAA,GAAAC,CAAA,QAAGwG,KAAK,CAACC,IAAI,CAACL,YAAY,CAACM,OAAO,CAAC,CAAC,CAAC,CACvDC,GAAG,CAAC,UAAAC,IAAA,EAAsB;QAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA;UAApBzD,MAAM,GAAA0D,KAAA;UAAEE,KAAK,GAAAF,KAAA;QAAA9G,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAO;UAAEmD,MAAM,EAANA,MAAM;UAAE4D,KAAK,EAALA;QAAM,CAAC;MAAD,CAAE,CAAC,CAC7CC,IAAI,CAAC,UAACC,CAAC,EAAEnE,CAAC,EAAK;QAAA/C,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAA8C,CAAC,CAACiE,KAAK,GAAGE,CAAC,CAACF,KAAK;MAAD,CAAC,CAAC,CACjCG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAGd,IAAMC,iBAAiB,IAAApH,aAAA,GAAAC,CAAA,QAAGkG,QAAQ,CAACJ,MAAM,CAAC,UAAA9F,CAAC,EAAI;QAAAD,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAAA,CAAC,CAACwD,QAAQ;MAAD,CAAC,CAAC;MAC1D,IAAM4D,sBAAsB,IAAArH,aAAA,GAAAC,CAAA,QAAGmH,iBAAiB,CAACvD,MAAM,GAAG,CAAC,IAAA7D,aAAA,GAAA+C,CAAA,WACvDqE,iBAAiB,CAACE,MAAM,CAAC,UAACC,GAAG,EAAEtH,CAAC,EAAK;QAAAD,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAAsH,GAAG,IAAI,CAAAvH,aAAA,GAAA+C,CAAA,WAAA9C,CAAC,CAACwD,QAAQ,MAAAzD,aAAA,GAAA+C,CAAA,WAAI,CAAC,EAAC;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGqE,iBAAiB,CAACvD,MAAM,KAAA7D,aAAA,GAAA+C,CAAA,WAC3F,CAAC;MAGL,IAAMyE,gBAAgB,IAAAxH,aAAA,GAAAC,CAAA,QAAG,IAAIC,GAAG,CAAiB,CAAC;MAACF,aAAA,GAAAC,CAAA;MACnDkG,QAAQ,CAACG,OAAO,CAAC,UAAAtE,OAAO,EAAI;QAAAhC,aAAA,GAAAY,CAAA;QAC1B,IAAM6G,IAAI,IAAAzH,aAAA,GAAAC,CAAA,QAAG+B,OAAO,CAACH,OAAO,CAAC6F,SAAS;QAAC1H,aAAA,GAAAC,CAAA;QACvCuH,gBAAgB,CAACxE,GAAG,CAACyE,IAAI,EAAE,CAAC,CAAAzH,aAAA,GAAA+C,CAAA,WAAAyE,gBAAgB,CAACvE,GAAG,CAACwE,IAAI,CAAC,MAAAzH,aAAA,GAAA+C,CAAA,WAAI,CAAC,KAAI,CAAC,CAAC;MACnE,CAAC,CAAC;MAEF,IAAM4E,cAAc,IAAA3H,aAAA,GAAAC,CAAA,QAAGwG,KAAK,CAACC,IAAI,CAACc,gBAAgB,CAACb,OAAO,CAAC,CAAC,CAAC,CAC1DM,IAAI,CAAC,UAACC,CAAC,EAAEnE,CAAC,EAAK;QAAA/C,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAA8C,CAAC,CAAC,CAAC,CAAC,GAAGmE,CAAC,CAAC,CAAC,CAAC;MAAD,CAAC,CAAC,CAC3BC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CACXP,GAAG,CAAC,UAAAgB,KAAA,EAAYH;QAAAA,GAAA,CAAAA,KAAA,CAAAA,CAAA,CAAAA,qBAAA,CAAAA;UAAVA,IAAI,GAAAI,KAAA;QAAA7H,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAMwH,MAAA,CAAAA,IAAI;MAAD,CAAC,CAAC;MAACzH,aAAA,GAAAC,CAAA;MAEzB,OAAO;QACL6H,aAAa,EAAE3B,QAAQ,CAACtC,MAAM;QAC9BwD,sBAAsB,EAAtBA,sBAAsB;QACtBb,eAAe,EAAfA,eAAe;QACfmB,cAAc,EAAdA,cAAc;QACdxH,gBAAgB,EAAE6E,QAAQ,CAACnB,MAAM;QACjCuC,OAAO,EAAE,CAAApG,aAAA,GAAA+C,CAAA,WAAAqD,OAAO,oBAAPA,OAAO,CAAE2B,IAAI,MAAA/H,aAAA,GAAA+C,CAAA,WAAI,SAAS;MACrC,CAAC;IACH;EAAC;IAAAhC,GAAA;IAAAC,KAAA,EAID,SAAQsC,WAAWA,CAAC1B,MAAc,EAAEE,SAAiB,EAAsB;MAAA9B,aAAA,GAAAY,CAAA;MACzE,IAAMuF,QAAQ,IAAAnG,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAA+C,CAAA,eAAI,CAAChD,YAAY,CAACkD,GAAG,CAACrB,MAAM,CAAC,MAAA5B,aAAA,GAAA+C,CAAA,WAAI,EAAE;MAAC/C,aAAA,GAAAC,CAAA;MACrD,OAAO,CAAAD,aAAA,GAAA+C,CAAA,WAAAoD,QAAQ,CAAC6B,IAAI,CAAC,UAAA/H,CAAC,EAAI;QAAAD,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAAA,CAAC,CAAC6B,SAAS,KAAKA,SAAS;MAAD,CAAC,CAAC,MAAA9B,aAAA,GAAA+C,CAAA,WAAI,IAAI;IAC9D;EAAC;IAAAhC,GAAA;IAAAC,KAAA,EAED,SAAQe,iBAAiBA,CAACH,MAAc,EAAU;MAAA5B,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MAChD,OAAO,WAAW2B,MAAM,IAAIM,IAAI,CAACC,GAAG,CAAC,CAAC,IAAI8F,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IACrF;EAAC;IAAArH,GAAA;IAAAC,KAAA,EAED,SAAQ6D,uBAAuBA,CAAC7C,OAAoB,EAAQ;MAAAhC,aAAA,GAAAY,CAAA;MAE1D,IAAMyH,SAAS,IAAArI,aAAA,GAAAC,CAAA,QAAG+B,OAAO,CAACI,WAAW,CAACwE,GAAG,CAAC,UAAA0B,CAAC,EAAI;QAAAtI,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAAqI,CAAC,CAACjF,QAAQ;MAAD,CAAC,CAAC;MAACrD,aAAA,GAAAC,CAAA;MAC3D+B,OAAO,CAACM,WAAW,CAACC,eAAe,GAAG8F,SAAS,CAACxE,MAAM,GAAG,CAAC,IAAA7D,aAAA,GAAA+C,CAAA,WACtDsF,SAAS,CAACf,MAAM,CAAC,UAACC,GAAG,EAAEE,IAAI,EAAK;QAAAzH,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAAsH,GAAG,GAAGE,IAAI;MAAD,CAAC,EAAE,CAAC,CAAC,GAAGY,SAAS,CAACxE,MAAM,KAAA7D,aAAA,GAAA+C,CAAA,WACjE,CAAC;MAAC/C,aAAA,GAAAC,CAAA;MAGN+B,OAAO,CAACM,WAAW,CAACM,eAAe,GAAGZ,OAAO,CAACI,WAAW,CAACyB,MAAM,GAAG,CAAC;MAAC7D,aAAA,GAAAC,CAAA;MAGrE+B,OAAO,CAACM,WAAW,CAACO,YAAY,GAAGoF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;IAC9D;EAAC;IAAAnH,GAAA;IAAAC,KAAA,EAED,SAAQkE,yBAAyBA,CAACiB,QAAuB,EAAqB;MAAA,IAAAoC,KAAA;MAAAvI,aAAA,GAAAY,CAAA;MAC5E,IAAMoE,QAA2B,IAAAhF,aAAA,GAAAC,CAAA,SAAG,EAAE;MAGtC,IAAMuI,SAAS,IAAAxI,aAAA,GAAAC,CAAA,SAAG,IAAIC,GAAG,CAAiB,CAAC;MAACF,aAAA,GAAAC,CAAA;MAE5CkG,QAAQ,CAACG,OAAO,CAAC,UAAAtE,OAAO,EAAI;QAAAhC,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAC1B,KAAK,IAAIwI,CAAC,IAAAzI,aAAA,GAAAC,CAAA,SAAG,CAAC,GAAEwI,CAAC,GAAGzG,OAAO,CAACI,WAAW,CAACyB,MAAM,GAAG,CAAC,EAAE4E,CAAC,EAAE,EAAE;UACvD,IAAMC,OAAO,IAAA1I,aAAA,GAAAC,CAAA,SAAG+B,OAAO,CAACI,WAAW,CAACqG,CAAC,CAAC,CAACrF,MAAM;UAC7C,IAAMuF,IAAI,IAAA3I,aAAA,GAAAC,CAAA,SAAG+B,OAAO,CAACI,WAAW,CAACqG,CAAC,GAAG,CAAC,CAAC,CAACrF,MAAM;UAC9C,IAAMwF,QAAQ,IAAA5I,aAAA,GAAAC,CAAA,SAAG,GAAGyI,OAAO,KAAKC,IAAI,EAAE;UAAC3I,aAAA,GAAAC,CAAA;UACvCuI,SAAS,CAACxF,GAAG,CAAC4F,QAAQ,EAAE,CAAC,CAAA5I,aAAA,GAAA+C,CAAA,WAAAyF,SAAS,CAACvF,GAAG,CAAC2F,QAAQ,CAAC,MAAA5I,aAAA,GAAA+C,CAAA,WAAI,CAAC,KAAI,CAAC,CAAC;QAC7D;MACF,CAAC,CAAC;MAAC/C,aAAA,GAAAC,CAAA;MAGHuI,SAAS,CAAClC,OAAO,CAAC,UAACU,KAAK,EAAE4B,QAAQ,EAAK;QAAA5I,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACrC,IAAI+G,KAAK,IAAIuB,KAAI,CAACjI,eAAe,CAACE,mBAAmB,EAAE;UAAAR,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAAC,CAAA;UACrD+E,QAAQ,CAAC9B,IAAI,CAAC;YACZ2F,SAAS,EAAE,OAAOD,QAAQ,CAACE,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;YAClDf,IAAI,EAAE,eAAea,QAAQ,EAAE;YAC/BG,WAAW,EAAE,6BAA6BH,QAAQ,EAAE;YACpDI,SAAS,EAAEhC,KAAK;YAChBiC,UAAU,EAAEhB,IAAI,CAACiB,GAAG,CAAClC,KAAK,GAAGb,QAAQ,CAACtC,MAAM,EAAE,CAAC,CAAC;YAChDsF,QAAQ,EAAE,CAACP,QAAQ,CAACQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACnCC,QAAQ,EAAE,CAACT,QAAQ,CAACQ,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;YACnCE,WAAW,EAAE,YAAY;YACzBC,WAAW,EAAE;cACX5B,cAAc,EAAE,EAAE;cAClB6B,eAAe,EAAE,CAAC;cAClBR,SAAS,EAAE;YACb;UACF,CAAC,CAAC;QACJ,CAAC;UAAAhJ,aAAA,GAAA+C,CAAA;QAAA;MACH,CAAC,CAAC;MAAC/C,aAAA,GAAAC,CAAA;MAEH,OAAO+E,QAAQ;IACjB;EAAC;IAAAjE,GAAA;IAAAC,KAAA,EAED,SAAQqE,mBAAmBA,CAACc,QAAuB,EAAqB;MAAA,IAAAsD,MAAA;MAAAzJ,aAAA,GAAAY,CAAA;MACtE,IAAMoE,QAA2B,IAAAhF,aAAA,GAAAC,CAAA,SAAG,EAAE;MAGtC,IAAMyJ,SAAS,IAAA1J,aAAA,GAAAC,CAAA,SAAG,IAAIC,GAAG,CAAiB,CAAC;MAACF,aAAA,GAAAC,CAAA;MAC5CkG,QAAQ,CAACG,OAAO,CAAC,UAAAtE,OAAO,EAAI;QAAAhC,aAAA,GAAAY,CAAA;QAC1B,IAAM6G,IAAI,IAAAzH,aAAA,GAAAC,CAAA,SAAG+B,OAAO,CAACH,OAAO,CAAC6F,SAAS;QAAC1H,aAAA,GAAAC,CAAA;QACvCyJ,SAAS,CAAC1G,GAAG,CAACyE,IAAI,EAAE,CAAC,CAAAzH,aAAA,GAAA+C,CAAA,WAAA2G,SAAS,CAACzG,GAAG,CAACwE,IAAI,CAAC,MAAAzH,aAAA,GAAA+C,CAAA,WAAI,CAAC,KAAI,CAAC,CAAC;MACrD,CAAC,CAAC;MAAC/C,aAAA,GAAAC,CAAA;MAGHyJ,SAAS,CAACpD,OAAO,CAAC,UAACU,KAAK,EAAES,IAAI,EAAK;QAAAzH,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACjC,IAAI+G,KAAK,IAAIyC,MAAI,CAACnJ,eAAe,CAACE,mBAAmB,EAAE;UAAAR,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAAC,CAAA;UACrD+E,QAAQ,CAAC9B,IAAI,CAAC;YACZ2F,SAAS,EAAE,QAAQpB,IAAI,EAAE;YACzBM,IAAI,EAAE,GAAGN,IAAI,CAACkC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGnC,IAAI,CAACN,KAAK,CAAC,CAAC,CAAC,QAAQ;YAC7D4B,WAAW,EAAE,yBAAyBtB,IAAI,EAAE;YAC5CuB,SAAS,EAAEhC,KAAK;YAChBiC,UAAU,EAAEjC,KAAK,GAAGb,QAAQ,CAACtC,MAAM;YACnCsF,QAAQ,EAAE,CAAC1B,IAAI,CAAC;YAChB4B,QAAQ,EAAE,CAAC,WAAW,CAAC;YACvBC,WAAW,EAAE,UAAU;YACvBC,WAAW,EAAE;cACX5B,cAAc,EAAE,CAACF,IAAI,CAAC;cACtB+B,eAAe,EAAE,CAAC;cAClBR,SAAS,EAAE;YACb;UACF,CAAC,CAAC;QACJ,CAAC;UAAAhJ,aAAA,GAAA+C,CAAA;QAAA;MACH,CAAC,CAAC;MAAC/C,aAAA,GAAAC,CAAA;MAEH,OAAO+E,QAAQ;IACjB;EAAC;IAAAjE,GAAA;IAAAC,KAAA,EAED,SAAQuE,mBAAmBA,CAACY,QAAuB,EAAqB;MAAA,IAAA0D,MAAA;MAAA7J,aAAA,GAAAY,CAAA;MACtE,IAAMoE,QAA2B,IAAAhF,aAAA,GAAAC,CAAA,SAAG,EAAE;MAGtC,IAAM6J,YAAY,IAAA9J,aAAA,GAAAC,CAAA,SAAG,IAAIC,GAAG,CAAiB,CAAC;MAACF,aAAA,GAAAC,CAAA;MAC/CkG,QAAQ,CAACG,OAAO,CAAC,UAAAtE,OAAO,EAAI;QAAAhC,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAC1B+B,OAAO,CAACK,YAAY,CAACiE,OAAO,CAAC,UAAApC,WAAW,EAAI;UAAAlE,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UAC1C6J,YAAY,CAAC9G,GAAG,CAACkB,WAAW,CAACF,MAAM,EAAE,CAAC,CAAAhE,aAAA,GAAA+C,CAAA,WAAA+G,YAAY,CAAC7G,GAAG,CAACiB,WAAW,CAACF,MAAM,CAAC,MAAAhE,aAAA,GAAA+C,CAAA,WAAI,CAAC,KAAI,CAAC,CAAC;QACvF,CAAC,CAAC;MACJ,CAAC,CAAC;MAAC/C,aAAA,GAAAC,CAAA;MAGH6J,YAAY,CAACxD,OAAO,CAAC,UAACU,KAAK,EAAE+C,OAAO,EAAK;QAAA/J,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACvC,IAAI+G,KAAK,IAAI6C,MAAI,CAACvJ,eAAe,CAACE,mBAAmB,EAAE;UAAAR,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAAC,CAAA;UACrD+E,QAAQ,CAAC9B,IAAI,CAAC;YACZ2F,SAAS,EAAE,WAAWkB,OAAO,EAAE;YAC/BhC,IAAI,EAAE,YAAYgC,OAAO,EAAE;YAC3BhB,WAAW,EAAE,wBAAwBgB,OAAO,EAAE;YAC9Cf,SAAS,EAAEhC,KAAK;YAChBiC,UAAU,EAAEhB,IAAI,CAACiB,GAAG,CAAClC,KAAK,IAAIb,QAAQ,CAACtC,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;YACvDsF,QAAQ,EAAE,CAAC,WAAW,CAAC;YACvBE,QAAQ,EAAE,CAACU,OAAO,CAAC;YACnBT,WAAW,EAAE,SAAS;YACtBC,WAAW,EAAE;cACX5B,cAAc,EAAE,EAAE;cAClB6B,eAAe,EAAE,CAAC;cAClBR,SAAS,EAAE;YACb;UACF,CAAC,CAAC;QACJ,CAAC;UAAAhJ,aAAA,GAAA+C,CAAA;QAAA;MACH,CAAC,CAAC;MAAC/C,aAAA,GAAAC,CAAA;MAEH,OAAO+E,QAAQ;IACjB;EAAC;IAAAjE,GAAA;IAAAC,KAAA,EAED,SAAQyE,0BAA0BA,CAACU,QAAuB,EAAqB;MAAAnG,aAAA,GAAAY,CAAA;MAC7E,IAAMoE,QAA2B,IAAAhF,aAAA,GAAAC,CAAA,SAAG,EAAE;MAGtC,IAAM+J,WAAW,IAAAhK,aAAA,GAAAC,CAAA,SAAGkG,QAAQ,CAACmB,MAAM,CAAC,UAACC,GAAG,EAAEtH,CAAC,EAAK;QAAAD,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAAsH,GAAG,GAAGtH,CAAC,CAACqC,WAAW,CAACC,eAAe;MAAD,CAAC,EAAE,CAAC,CAAC,GAAG4D,QAAQ,CAACtC,MAAM;MAAC7D,aAAA,GAAAC,CAAA;MAE1G,IAAI+J,WAAW,GAAG,IAAI,EAAE;QAAAhK,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QACtB+E,QAAQ,CAAC9B,IAAI,CAAC;UACZ2F,SAAS,EAAE,mBAAmB;UAC9Bd,IAAI,EAAE,0BAA0B;UAChCgB,WAAW,EAAE,qCAAqC;UAClDC,SAAS,EAAE7C,QAAQ,CAACtC,MAAM;UAC1BoF,UAAU,EAAE,GAAG;UACfE,QAAQ,EAAE,CAAC,cAAc,EAAE,YAAY,CAAC;UACxCE,QAAQ,EAAE,CAAC,iCAAiC,CAAC;UAC7CC,WAAW,EAAE,aAAa;UAC1BC,WAAW,EAAE;YACX5B,cAAc,EAAE,EAAE;YAClB6B,eAAe,EAAE,CAAC;YAClBR,SAAS,EAAE;UACb;QACF,CAAC,CAAC;MACJ,CAAC;QAAAhJ,aAAA,GAAA+C,CAAA;MAAA;MAAA/C,aAAA,GAAAC,CAAA;MAED,OAAO+E,QAAQ;IACjB;EAAC;IAAAjE,GAAA;IAAAC,KAAA;MAAA,IAAAiJ,kBAAA,GAAA/I,iBAAA,CAED,WAAgCU,MAAc,EAAEoD,QAA2B,EAAiB;QAAAhF,aAAA,GAAAY,CAAA;QAE1F,IAAMwF,OAAO,IAAApG,aAAA,GAAAC,CAAA,SAAG,IAAI,CAACiK,oBAAoB,CAAClF,QAAQ,CAAC;QAAChF,aAAA,GAAAC,CAAA;QACpD,IAAI,CAACG,YAAY,CAAC4C,GAAG,CAACpB,MAAM,EAAEwE,OAAO,CAAC;QAACpG,aAAA,GAAAC,CAAA;QAGvC,MAAM,IAAI,CAACkK,yBAAyB,CAACvI,MAAM,EAAEwE,OAAO,CAAC;MACvD,CAAC;MAAA,SAPaV,iBAAiBA,CAAA0E,GAAA,EAAAC,GAAA;QAAA,OAAAJ,kBAAA,CAAAxI,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBgE,iBAAiB;IAAA;EAAA;IAAA3E,GAAA;IAAAC,KAAA,EAS/B,SAAQkJ,oBAAoBA,CAAClF,QAA2B,EAAe;MAAAhF,aAAA,GAAAY,CAAA;MAErE,IAAMqE,kBAAkB,IAAAjF,aAAA,GAAAC,CAAA,SAAG+E,QAAQ,CAACe,MAAM,CAAC,UAAAuE,CAAC,EAAI;QAAAtK,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAAqK,CAAC,CAAChB,WAAW,KAAK,YAAY;MAAD,CAAC,CAAC,CAACzF,MAAM;MACtF,IAAMyB,eAAe,IAAAtF,aAAA,GAAAC,CAAA,SAAG+E,QAAQ,CAACe,MAAM,CAAC,UAAAuE,CAAC,EAAI;QAAAtK,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAAA,OAAAqK,CAAC,CAAChB,WAAW,KAAK,SAAS;MAAD,CAAC,CAAC,CAACzF,MAAM;MAAC7D,aAAA,GAAAC,CAAA;MAEjF,IAAIqF,eAAe,GAAG,CAAC,EAAE;QAAAtF,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QACvB,OAAO;UACLsK,SAAS,EAAE,YAAY;UACvBxC,IAAI,EAAE,YAAY;UAClBgB,WAAW,EAAE,6CAA6C;UAC1DyB,eAAe,EAAE;YACfC,UAAU,EAAE,UAAU;YACtBC,aAAa,EAAE,WAAW;YAC1BZ,YAAY,EAAE,CAAC,CAAC;YAChBa,eAAe,EAAE;cACfC,eAAe,EAAE,OAAO;cACxBjD,cAAc,EAAE,CAAC,SAAS,CAAC;cAC3BqB,SAAS,EAAE;YACb;UACF,CAAC;UACD6B,aAAa,EAAE;YACbC,aAAa,EAAE,YAAY;YAC3BC,kBAAkB,EAAE,aAAa;YACjCC,YAAY,EAAE,UAAU;YACxBC,cAAc,EAAE,CAAC,eAAe,EAAE,WAAW;UAC/C;QACF,CAAC;MACH,CAAC,MAAM;QAAAjL,aAAA,GAAA+C,CAAA;QAAA/C,aAAA,GAAAC,CAAA;QACL,OAAO;UACLsK,SAAS,EAAE,aAAa;UACxBxC,IAAI,EAAE,aAAa;UACnBgB,WAAW,EAAE,iCAAiC;UAC9CyB,eAAe,EAAE;YACfC,UAAU,EAAE,UAAU;YACtBC,aAAa,EAAE,QAAQ;YACvBZ,YAAY,EAAE,CAAC,CAAC;YAChBa,eAAe,EAAE;cACfC,eAAe,EAAE,MAAM;cACvBjD,cAAc,EAAE,CAAC,WAAW,CAAC;cAC7BqB,SAAS,EAAE;YACb;UACF,CAAC;UACD6B,aAAa,EAAE;YACbC,aAAa,EAAE,cAAc;YAC7BC,kBAAkB,EAAE,UAAU;YAC9BC,YAAY,EAAE,UAAU;YACxBC,cAAc,EAAE,CAAC,YAAY;UAC/B;QACF,CAAC;MACH;IACF;EAAC;IAAAlK,GAAA;IAAAC,KAAA;MAAA,IAAAkK,0BAAA,GAAAhK,iBAAA,CAED,WAAwCU,MAAc,EAAEwE,OAAoB,EAAiB;QAAApG,aAAA,GAAAY,CAAA;QAE3F,IAAMuK,WAAW,IAAAnL,aAAA,GAAAC,CAAA,SAAGmG,OAAO,CAACyE,aAAa,CAACE,kBAAkB;QAAC/K,aAAA,GAAAC,CAAA;QAE7DqB,OAAO,CAACC,GAAG,CAAC,WAAW4J,WAAW,8BAA8B/E,OAAO,CAAC2B,IAAI,EAAE,CAAC;MACjF,CAAC;MAAA,SALaoC,yBAAyBA,CAAAiB,GAAA,EAAAC,GAAA;QAAA,OAAAH,0BAAA,CAAAzJ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAzByI,yBAAyB;IAAA;EAAA;IAAApJ,GAAA;IAAAC,KAAA;MAAA,IAAAsK,oCAAA,GAAApK,iBAAA,CAOvC,WACEU,MAAc,EACdoD,QAA2B,EACZ;QAAA,IAAAuG,MAAA;UAAAC,qBAAA;QAAAxL,aAAA,GAAAY,CAAA;QACf,IAAM6K,QAA4B,IAAAzL,aAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,aAAA,GAAAC,CAAA;QAGxC+E,QAAQ,CAACsB,OAAO,CAAC,UAAAoF,OAAO,EAAI;UAAA1L,aAAA,GAAAY,CAAA;UAAAZ,aAAA,GAAAC,CAAA;UAC1B,IAAIyL,OAAO,CAACzC,UAAU,GAAGsC,MAAI,CAACjL,eAAe,CAACG,sBAAsB,EAAE;YAAAT,aAAA,GAAA+C,CAAA;YAAA/C,aAAA,GAAAC,CAAA;YACpEwL,QAAQ,CAACvI,IAAI,CAAC;cACZa,IAAI,EAAE,cAAc;cACpB4H,KAAK,EAAE,gBAAgBD,OAAO,CAAC3D,IAAI,EAAE;cACrCgB,WAAW,EAAE2C,OAAO,CAAC3C,WAAW;cAChC6C,MAAM,EAAEF,OAAO,CAAC1C,SAAS,GAAG,EAAE,IAAAhJ,aAAA,GAAA+C,CAAA,WAAG,MAAM,KAAA/C,aAAA,GAAA+C,CAAA,WAAG2I,OAAO,CAAC1C,SAAS,GAAG,CAAC,IAAAhJ,aAAA,GAAA+C,CAAA,WAAG,QAAQ,KAAA/C,aAAA,GAAA+C,CAAA,WAAG,KAAK;cAClFkG,UAAU,EAAEyC,OAAO,CAACzC,UAAU;cAC9B4C,cAAc,EAAE,4CAA4C;cAC5DC,cAAc,EAAE,kCAAkCJ,OAAO,CAACrC,QAAQ,CAAC0C,IAAI,CAAC,IAAI,CAAC,EAAE;cAC/E9F,OAAO,EAAE;gBAAErE,MAAM,EAAEA,MAAa;gBAAEoH,SAAS,EAAE0C,OAAO,CAAC1C;cAAU;YACjE,CAAC,CAAC;UACJ,CAAC;YAAAhJ,aAAA,GAAA+C,CAAA;UAAA;QACH,CAAC,CAAC;QAAC/C,aAAA,GAAAC,CAAA;QAEH,CAAAuL,qBAAA,OAAI,CAACnL,iBAAiB,EAAC6C,IAAI,CAAAzB,KAAA,CAAA+J,qBAAA,EAAIC,QAAQ,CAAC;QAACzL,aAAA,GAAAC,CAAA;QAGzC,IAAI,IAAI,CAACI,iBAAiB,CAACwD,MAAM,GAAG,IAAI,EAAE;UAAA7D,aAAA,GAAA+C,CAAA;UAAA/C,aAAA,GAAAC,CAAA;UACxC,IAAI,CAACI,iBAAiB,GAAG,IAAI,CAACA,iBAAiB,CAAC8G,KAAK,CAAC,CAAC,IAAI,CAAC;QAC9D,CAAC;UAAAnH,aAAA,GAAA+C,CAAA;QAAA;MACH,CAAC;MAAA,SA5Ba4C,mCAAmCA,CAAAqG,GAAA,EAAAC,GAAA;QAAA,OAAAX,oCAAA,CAAA7J,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAnCiE,mCAAmC;IAAA;EAAA;IAAA5E,GAAA;IAAAC,KAAA;MAAA,IAAAkL,kBAAA,GAAAhL,iBAAA,CA8BjD,aAAiD;QAAAlB,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAE/CqB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACnD,CAAC;MAAA,SAHaJ,iBAAiBA,CAAA;QAAA,OAAA+K,kBAAA,CAAAzK,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBP,iBAAiB;IAAA;EAAA;IAAAJ,GAAA;IAAAC,KAAA,EAK/B,SAAQI,qBAAqBA,CAAA,EAAS;MAAA,IAAA+K,MAAA;MAAAnM,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MACpCmM,WAAW,CAAC,YAAM;QAAApM,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QAChBkM,MAAI,CAACE,uBAAuB,CAAC,CAAC;MAChC,CAAC,EAAE,IAAI,CAAC/L,eAAe,CAACK,gBAAgB,CAAC;IAC3C;EAAC;IAAAI,GAAA;IAAAC,KAAA;MAAA,IAAAsL,wBAAA,GAAApL,iBAAA,CAED,aAAuD;QAAAlB,aAAA,GAAAY,CAAA;QAAAZ,aAAA,GAAAC,CAAA;QACrDqB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QAACvB,aAAA,GAAAC,CAAA;QAGxD,SAAAsM,KAAA,IAAiC,IAAI,CAACxM,YAAY,CAAC4G,OAAO,CAAC,CAAC,EAAE;UAAA,IAAA6F,KAAA,GAAAzF,cAAA,CAAAwF,KAAA;UAAA,IAAlD3K,MAAM,GAAA4K,KAAA;UAAA,IAAErG,QAAQ,GAAAqG,KAAA;UAC1B,IAAMC,cAAc,IAAAzM,aAAA,GAAAC,CAAA,SAAGkG,QAAQ,CAACJ,MAAM,CAAC,UAAA9F,CAAC,EACtC;YAAAD,aAAA,GAAAY,CAAA;YAAAZ,aAAA,GAAAC,CAAA;YAAA,OAAAiC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGlC,CAAC,CAACgC,SAAS,GAAG,QAAQ;UAAD,CACpC,CAAC;UAACjC,aAAA,GAAAC,CAAA;UAEF,IAAIwM,cAAc,CAAC5I,MAAM,GAAG,CAAC,EAAE;YAAA7D,aAAA,GAAA+C,CAAA;YAAA/C,aAAA,GAAAC,CAAA;YAC7B,MAAM,IAAI,CAAC6E,mBAAmB,CAAClD,MAAM,CAAC;UACxC,CAAC;YAAA5B,aAAA,GAAA+C,CAAA;UAAA;QACH;MACF,CAAC;MAAA,SAbasJ,uBAAuBA,CAAA;QAAA,OAAAC,wBAAA,CAAA7K,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAvB2K,uBAAuB;IAAA;EAAA;IAAAtL,GAAA;IAAAC,KAAA,EAerC,SAAQK,sBAAsBA,CAAA,EAAS;MAAArB,aAAA,GAAAY,CAAA;MAAAZ,aAAA,GAAAC,CAAA;MAErCqB,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;IAC1C;EAAC;AAAA;AAIH,OAAO,IAAMmL,yBAAyB,IAAA1M,aAAA,GAAAC,CAAA,SAAG,IAAIJ,yBAAyB,CAAC,CAAC;AACxE,eAAe6M,yBAAyB", "ignoreList": []}