/**
 * Magic.link Authentication Service
 * Provides passwordless authentication using Magic.link SDK
 */

import { Magic } from 'magic-sdk';
import { Platform } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface MagicUser {
  issuer: string;
  publicAddress: string;
  email: string;
}

export interface AuthResult {
  success: boolean;
  user?: MagicUser;
  token?: string;
  error?: string;
}

export interface LoginOptions {
  email: string;
  showUI?: boolean;
  redirectURI?: string;
}

class MagicLinkService {
  private magic: Magic | null = null;
  private isInitialized = false;
  private publishableKey: string;

  constructor() {
    this.publishableKey = process.env.MAGIC_PUBLISHABLE_KEY || '';
  }

  /**
   * Initialize Magic.link SDK
   */
  async initialize(): Promise<void> {
    try {
      if (!this.publishableKey) {
        console.warn('Magic.link publishable key not configured');
        return;
      }

      if (Platform.OS === 'web') {
        // Initialize Magic for web
        this.magic = new Magic(this.publishableKey, {
          network: 'mainnet', // or 'testnet' for development
        });
      } else {
        // For React Native, Magic.link has specific setup requirements
        // This would typically use @magic-ext/react-native-expo
        console.log('Magic.link React Native initialization would go here');
        
        // Mock Magic instance for React Native
        this.magic = {
          auth: {
            loginWithMagicLink: this.mockLoginWithMagicLink.bind(this),
            loginWithSMS: this.mockLoginWithSMS.bind(this),
            logout: this.mockLogout.bind(this),
            getRedirectResult: this.mockGetRedirectResult.bind(this),
          },
          user: {
            isLoggedIn: this.mockIsLoggedIn.bind(this),
            getMetadata: this.mockGetMetadata.bind(this),
            getIdToken: this.mockGetIdToken.bind(this),
            logout: this.mockLogout.bind(this),
          },
        } as any;
      }

      this.isInitialized = true;
      console.log('Magic.link service initialized');
    } catch (error) {
      console.error('Failed to initialize Magic.link:', error);
      throw error;
    }
  }

  /**
   * Login with email (passwordless)
   */
  async loginWithEmail(options: LoginOptions): Promise<AuthResult> {
    if (!this.isInitialized || !this.magic) {
      return { success: false, error: 'Magic.link not initialized' };
    }

    try {
      console.log('Initiating Magic.link email login for:', options.email);

      // Send magic link
      const didToken = await this.magic.auth.loginWithMagicLink({
        email: options.email,
        showUI: options.showUI !== false,
        redirectURI: options.redirectURI,
      });

      if (!didToken) {
        return { success: false, error: 'Failed to get authentication token' };
      }

      // Get user metadata
      const userMetadata = await this.magic.user.getMetadata();
      
      if (!userMetadata) {
        return { success: false, error: 'Failed to get user metadata' };
      }

      const user: MagicUser = {
        issuer: userMetadata.issuer || '',
        publicAddress: userMetadata.publicAddress || '',
        email: userMetadata.email || options.email,
      };

      // Store token
      await AsyncStorage.setItem('magic_token', didToken);
      await AsyncStorage.setItem('magic_user', JSON.stringify(user));

      console.log('Magic.link login successful:', user.email);
      
      return {
        success: true,
        user,
        token: didToken,
      };
    } catch (error) {
      console.error('Magic.link login failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    }
  }

  /**
   * Login with SMS (phone number)
   */
  async loginWithSMS(phoneNumber: string): Promise<AuthResult> {
    if (!this.isInitialized || !this.magic) {
      return { success: false, error: 'Magic.link not initialized' };
    }

    try {
      console.log('Initiating Magic.link SMS login for:', phoneNumber);

      const didToken = await this.magic.auth.loginWithSMS({
        phoneNumber,
      });

      if (!didToken) {
        return { success: false, error: 'Failed to get authentication token' };
      }

      const userMetadata = await this.magic.user.getMetadata();
      
      if (!userMetadata) {
        return { success: false, error: 'Failed to get user metadata' };
      }

      const user: MagicUser = {
        issuer: userMetadata.issuer || '',
        publicAddress: userMetadata.publicAddress || '',
        email: userMetadata.email || '',
      };

      await AsyncStorage.setItem('magic_token', didToken);
      await AsyncStorage.setItem('magic_user', JSON.stringify(user));

      console.log('Magic.link SMS login successful');
      
      return {
        success: true,
        user,
        token: didToken,
      };
    } catch (error) {
      console.error('Magic.link SMS login failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'SMS login failed',
      };
    }
  }

  /**
   * Check if user is logged in
   */
  async isLoggedIn(): Promise<boolean> {
    if (!this.isInitialized || !this.magic) {
      return false;
    }

    try {
      return await this.magic.user.isLoggedIn();
    } catch (error) {
      console.error('Failed to check Magic.link login status:', error);
      return false;
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<MagicUser | null> {
    if (!this.isInitialized || !this.magic) {
      return null;
    }

    try {
      const isLoggedIn = await this.isLoggedIn();
      if (!isLoggedIn) {
        return null;
      }

      const userMetadata = await this.magic.user.getMetadata();
      
      if (!userMetadata) {
        return null;
      }

      return {
        issuer: userMetadata.issuer || '',
        publicAddress: userMetadata.publicAddress || '',
        email: userMetadata.email || '',
      };
    } catch (error) {
      console.error('Failed to get current Magic.link user:', error);
      return null;
    }
  }

  /**
   * Get ID token
   */
  async getIdToken(): Promise<string | null> {
    if (!this.isInitialized || !this.magic) {
      return null;
    }

    try {
      return await this.magic.user.getIdToken();
    } catch (error) {
      console.error('Failed to get Magic.link ID token:', error);
      return null;
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<{ success: boolean; error?: string }> {
    if (!this.isInitialized || !this.magic) {
      return { success: false, error: 'Magic.link not initialized' };
    }

    try {
      await this.magic.user.logout();
      
      // Clear stored data
      await AsyncStorage.removeItem('magic_token');
      await AsyncStorage.removeItem('magic_user');
      
      console.log('Magic.link logout successful');
      return { success: true };
    } catch (error) {
      console.error('Magic.link logout failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Logout failed',
      };
    }
  }

  /**
   * Handle redirect result (for web)
   */
  async handleRedirectResult(): Promise<AuthResult | null> {
    if (!this.isInitialized || !this.magic || Platform.OS !== 'web') {
      return null;
    }

    try {
      const result = await this.magic.auth.getRedirectResult();
      
      if (!result) {
        return null;
      }

      const userMetadata = await this.magic.user.getMetadata();
      
      if (!userMetadata) {
        return { success: false, error: 'Failed to get user metadata' };
      }

      const user: MagicUser = {
        issuer: userMetadata.issuer || '',
        publicAddress: userMetadata.publicAddress || '',
        email: userMetadata.email || '',
      };

      return {
        success: true,
        user,
        token: result,
      };
    } catch (error) {
      console.error('Failed to handle Magic.link redirect result:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Redirect handling failed',
      };
    }
  }

  // Mock methods for React Native (until proper React Native SDK is integrated)
  private async mockLoginWithMagicLink(options: any): Promise<string> {
    console.log('[Magic.link Mock] Email login:', options.email);
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate network delay
    return 'mock-did-token-' + Date.now();
  }

  private async mockLoginWithSMS(options: any): Promise<string> {
    console.log('[Magic.link Mock] SMS login:', options.phoneNumber);
    await new Promise(resolve => setTimeout(resolve, 1000));
    return 'mock-did-token-sms-' + Date.now();
  }

  private async mockIsLoggedIn(): Promise<boolean> {
    const token = await AsyncStorage.getItem('magic_token');
    return !!token;
  }

  private async mockGetMetadata(): Promise<any> {
    const userStr = await AsyncStorage.getItem('magic_user');
    return userStr ? JSON.parse(userStr) : null;
  }

  private async mockGetIdToken(): Promise<string | null> {
    return await AsyncStorage.getItem('magic_token');
  }

  private async mockLogout(): Promise<void> {
    await AsyncStorage.removeItem('magic_token');
    await AsyncStorage.removeItem('magic_user');
  }

  private async mockGetRedirectResult(): Promise<string | null> {
    return await AsyncStorage.getItem('magic_token');
  }

  /**
   * Check if service is ready
   */
  isReady(): boolean {
    return this.isInitialized && !!this.magic;
  }
}

// Export singleton instance
export const magicLinkService = new MagicLinkService();
