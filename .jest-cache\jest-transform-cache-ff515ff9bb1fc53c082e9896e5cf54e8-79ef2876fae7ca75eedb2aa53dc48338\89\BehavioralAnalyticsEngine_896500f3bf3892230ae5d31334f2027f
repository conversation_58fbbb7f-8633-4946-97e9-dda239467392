64f7f7bc1e77800ace8a6f959e45cb36
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_x2gc31hu4() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\ai\\BehavioralAnalyticsEngine.ts";
  var hash = "39638cb347803eb872d16097964e4743c74fa901";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\ai\\BehavioralAnalyticsEngine.ts",
    statementMap: {
      "0": {
        start: {
          line: 117,
          column: 53
        },
        end: {
          line: 117,
          column: 62
        }
      },
      "1": {
        start: {
          line: 118,
          column: 61
        },
        end: {
          line: 118,
          column: 70
        }
      },
      "2": {
        start: {
          line: 119,
          column: 51
        },
        end: {
          line: 119,
          column: 60
        }
      },
      "3": {
        start: {
          line: 120,
          column: 50
        },
        end: {
          line: 120,
          column: 52
        }
      },
      "4": {
        start: {
          line: 122,
          column: 37
        },
        end: {
          line: 128,
          column: 3
        }
      },
      "5": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 131,
          column: 41
        }
      },
      "6": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 151,
          column: 5
        }
      },
      "7": {
        start: {
          line: 140,
          column: 6
        },
        end: {
          line: 140,
          column: 37
        }
      },
      "8": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 35
        }
      },
      "9": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 36
        }
      },
      "10": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 74
        }
      },
      "11": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 80
        }
      },
      "12": {
        start: {
          line: 158,
          column: 22
        },
        end: {
          line: 158,
          column: 52
        }
      },
      "13": {
        start: {
          line: 160,
          column: 33
        },
        end: {
          line: 176,
          column: 5
        }
      },
      "14": {
        start: {
          line: 178,
          column: 4
        },
        end: {
          line: 180,
          column: 5
        }
      },
      "15": {
        start: {
          line: 179,
          column: 6
        },
        end: {
          line: 179,
          column: 40
        }
      },
      "16": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 182,
          column: 49
        }
      },
      "17": {
        start: {
          line: 184,
          column: 4
        },
        end: {
          line: 184,
          column: 67
        }
      },
      "18": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 185,
          column: 21
        }
      },
      "19": {
        start: {
          line: 197,
          column: 20
        },
        end: {
          line: 197,
          column: 55
        }
      },
      "20": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 198,
          column: 25
        }
      },
      "21": {
        start: {
          line: 198,
          column: 18
        },
        end: {
          line: 198,
          column: 25
        }
      },
      "22": {
        start: {
          line: 200,
          column: 35
        },
        end: {
          line: 207,
          column: 5
        }
      },
      "23": {
        start: {
          line: 210,
          column: 25
        },
        end: {
          line: 210,
          column: 76
        }
      },
      "24": {
        start: {
          line: 211,
          column: 4
        },
        end: {
          line: 213,
          column: 5
        }
      },
      "25": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 76
        }
      },
      "26": {
        start: {
          line: 215,
          column: 4
        },
        end: {
          line: 215,
          column: 41
        }
      },
      "27": {
        start: {
          line: 218,
          column: 4
        },
        end: {
          line: 228,
          column: 6
        }
      },
      "28": {
        start: {
          line: 239,
          column: 20
        },
        end: {
          line: 239,
          column: 55
        }
      },
      "29": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 240,
          column: 25
        }
      },
      "30": {
        start: {
          line: 240,
          column: 18
        },
        end: {
          line: 240,
          column: 25
        }
      },
      "31": {
        start: {
          line: 242,
          column: 45
        },
        end: {
          line: 245,
          column: 5
        }
      },
      "32": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 47
        }
      },
      "33": {
        start: {
          line: 250,
          column: 30
        },
        end: {
          line: 250,
          column: 81
        }
      },
      "34": {
        start: {
          line: 251,
          column: 4
        },
        end: {
          line: 253,
          column: 5
        }
      },
      "35": {
        start: {
          line: 252,
          column: 6
        },
        end: {
          line: 252,
          column: 43
        }
      },
      "36": {
        start: {
          line: 256,
          column: 4
        },
        end: {
          line: 269,
          column: 6
        }
      },
      "37": {
        start: {
          line: 276,
          column: 20
        },
        end: {
          line: 276,
          column: 55
        }
      },
      "38": {
        start: {
          line: 277,
          column: 4
        },
        end: {
          line: 277,
          column: 25
        }
      },
      "39": {
        start: {
          line: 277,
          column: 18
        },
        end: {
          line: 277,
          column: 25
        }
      },
      "40": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 279,
          column: 33
        }
      },
      "41": {
        start: {
          line: 280,
          column: 4
        },
        end: {
          line: 280,
          column: 59
        }
      },
      "42": {
        start: {
          line: 283,
          column: 27
        },
        end: {
          line: 283,
          column: 78
        }
      },
      "43": {
        start: {
          line: 284,
          column: 4
        },
        end: {
          line: 286,
          column: 5
        }
      },
      "44": {
        start: {
          line: 285,
          column: 6
        },
        end: {
          line: 285,
          column: 75
        }
      },
      "45": {
        start: {
          line: 289,
          column: 4
        },
        end: {
          line: 289,
          column: 42
        }
      },
      "46": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 292,
          column: 37
        }
      },
      "47": {
        start: {
          line: 294,
          column: 4
        },
        end: {
          line: 294,
          column: 99
        }
      },
      "48": {
        start: {
          line: 301,
          column: 25
        },
        end: {
          line: 301,
          column: 60
        }
      },
      "49": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 304,
          column: 5
        }
      },
      "50": {
        start: {
          line: 303,
          column: 6
        },
        end: {
          line: 303,
          column: 16
        }
      },
      "51": {
        start: {
          line: 306,
          column: 40
        },
        end: {
          line: 306,
          column: 42
        }
      },
      "52": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 339,
          column: 5
        }
      },
      "53": {
        start: {
          line: 310,
          column: 33
        },
        end: {
          line: 310,
          column: 77
        }
      },
      "54": {
        start: {
          line: 311,
          column: 6
        },
        end: {
          line: 311,
          column: 43
        }
      },
      "55": {
        start: {
          line: 314,
          column: 27
        },
        end: {
          line: 314,
          column: 65
        }
      },
      "56": {
        start: {
          line: 315,
          column: 6
        },
        end: {
          line: 315,
          column: 37
        }
      },
      "57": {
        start: {
          line: 318,
          column: 30
        },
        end: {
          line: 318,
          column: 68
        }
      },
      "58": {
        start: {
          line: 319,
          column: 6
        },
        end: {
          line: 319,
          column: 40
        }
      },
      "59": {
        start: {
          line: 322,
          column: 34
        },
        end: {
          line: 322,
          column: 79
        }
      },
      "60": {
        start: {
          line: 323,
          column: 6
        },
        end: {
          line: 323,
          column: 44
        }
      },
      "61": {
        start: {
          line: 326,
          column: 6
        },
        end: {
          line: 326,
          column: 50
        }
      },
      "62": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 329,
          column: 53
        }
      },
      "63": {
        start: {
          line: 332,
          column: 6
        },
        end: {
          line: 332,
          column: 71
        }
      },
      "64": {
        start: {
          line: 334,
          column: 6
        },
        end: {
          line: 334,
          column: 22
        }
      },
      "65": {
        start: {
          line: 337,
          column: 6
        },
        end: {
          line: 337,
          column: 63
        }
      },
      "66": {
        start: {
          line: 338,
          column: 6
        },
        end: {
          line: 338,
          column: 16
        }
      },
      "67": {
        start: {
          line: 346,
          column: 4
        },
        end: {
          line: 346,
          column: 49
        }
      },
      "68": {
        start: {
          line: 353,
          column: 4
        },
        end: {
          line: 357,
          column: 5
        }
      },
      "69": {
        start: {
          line: 354,
          column: 6
        },
        end: {
          line: 356,
          column: 8
        }
      },
      "70": {
        start: {
          line: 355,
          column: 8
        },
        end: {
          line: 355,
          column: 41
        }
      },
      "71": {
        start: {
          line: 358,
          column: 4
        },
        end: {
          line: 358,
          column: 34
        }
      },
      "72": {
        start: {
          line: 372,
          column: 21
        },
        end: {
          line: 372,
          column: 56
        }
      },
      "73": {
        start: {
          line: 373,
          column: 21
        },
        end: {
          line: 373,
          column: 60
        }
      },
      "74": {
        start: {
          line: 374,
          column: 20
        },
        end: {
          line: 374,
          column: 49
        }
      },
      "75": {
        start: {
          line: 377,
          column: 25
        },
        end: {
          line: 377,
          column: 50
        }
      },
      "76": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 382,
          column: 7
        }
      },
      "77": {
        start: {
          line: 379,
          column: 6
        },
        end: {
          line: 381,
          column: 9
        }
      },
      "78": {
        start: {
          line: 380,
          column: 8
        },
        end: {
          line: 380,
          column: 80
        }
      },
      "79": {
        start: {
          line: 384,
          column: 28
        },
        end: {
          line: 387,
          column: 18
        }
      },
      "80": {
        start: {
          line: 385,
          column: 33
        },
        end: {
          line: 385,
          column: 50
        }
      },
      "81": {
        start: {
          line: 386,
          column: 22
        },
        end: {
          line: 386,
          column: 39
        }
      },
      "82": {
        start: {
          line: 390,
          column: 30
        },
        end: {
          line: 390,
          column: 62
        }
      },
      "83": {
        start: {
          line: 390,
          column: 51
        },
        end: {
          line: 390,
          column: 61
        }
      },
      "84": {
        start: {
          line: 391,
          column: 35
        },
        end: {
          line: 393,
          column: 9
        }
      },
      "85": {
        start: {
          line: 392,
          column: 45
        },
        end: {
          line: 392,
          column: 68
        }
      },
      "86": {
        start: {
          line: 396,
          column: 29
        },
        end: {
          line: 396,
          column: 54
        }
      },
      "87": {
        start: {
          line: 397,
          column: 4
        },
        end: {
          line: 400,
          column: 7
        }
      },
      "88": {
        start: {
          line: 398,
          column: 19
        },
        end: {
          line: 398,
          column: 44
        }
      },
      "89": {
        start: {
          line: 399,
          column: 6
        },
        end: {
          line: 399,
          column: 72
        }
      },
      "90": {
        start: {
          line: 402,
          column: 27
        },
        end: {
          line: 405,
          column: 28
        }
      },
      "91": {
        start: {
          line: 403,
          column: 22
        },
        end: {
          line: 403,
          column: 33
        }
      },
      "92": {
        start: {
          line: 405,
          column: 23
        },
        end: {
          line: 405,
          column: 27
        }
      },
      "93": {
        start: {
          line: 407,
          column: 4
        },
        end: {
          line: 414,
          column: 6
        }
      },
      "94": {
        start: {
          line: 420,
          column: 21
        },
        end: {
          line: 420,
          column: 56
        }
      },
      "95": {
        start: {
          line: 421,
          column: 4
        },
        end: {
          line: 421,
          column: 65
        }
      },
      "96": {
        start: {
          line: 421,
          column: 30
        },
        end: {
          line: 421,
          column: 55
        }
      },
      "97": {
        start: {
          line: 425,
          column: 4
        },
        end: {
          line: 425,
          column: 88
        }
      },
      "98": {
        start: {
          line: 430,
          column: 22
        },
        end: {
          line: 430,
          column: 62
        }
      },
      "99": {
        start: {
          line: 430,
          column: 51
        },
        end: {
          line: 430,
          column: 61
        }
      },
      "100": {
        start: {
          line: 431,
          column: 4
        },
        end: {
          line: 433,
          column: 10
        }
      },
      "101": {
        start: {
          line: 432,
          column: 40
        },
        end: {
          line: 432,
          column: 50
        }
      },
      "102": {
        start: {
          line: 436,
          column: 4
        },
        end: {
          line: 436,
          column: 73
        }
      },
      "103": {
        start: {
          line: 439,
          column: 4
        },
        end: {
          line: 439,
          column: 65
        }
      },
      "104": {
        start: {
          line: 443,
          column: 40
        },
        end: {
          line: 443,
          column: 42
        }
      },
      "105": {
        start: {
          line: 446,
          column: 22
        },
        end: {
          line: 446,
          column: 47
        }
      },
      "106": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 455,
          column: 7
        }
      },
      "107": {
        start: {
          line: 449,
          column: 6
        },
        end: {
          line: 454,
          column: 7
        }
      },
      "108": {
        start: {
          line: 449,
          column: 19
        },
        end: {
          line: 449,
          column: 20
        }
      },
      "109": {
        start: {
          line: 450,
          column: 24
        },
        end: {
          line: 450,
          column: 53
        }
      },
      "110": {
        start: {
          line: 451,
          column: 21
        },
        end: {
          line: 451,
          column: 54
        }
      },
      "111": {
        start: {
          line: 452,
          column: 25
        },
        end: {
          line: 452,
          column: 46
        }
      },
      "112": {
        start: {
          line: 453,
          column: 8
        },
        end: {
          line: 453,
          column: 68
        }
      },
      "113": {
        start: {
          line: 458,
          column: 4
        },
        end: {
          line: 476,
          column: 7
        }
      },
      "114": {
        start: {
          line: 459,
          column: 6
        },
        end: {
          line: 475,
          column: 7
        }
      },
      "115": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 474,
          column: 11
        }
      },
      "116": {
        start: {
          line: 478,
          column: 4
        },
        end: {
          line: 478,
          column: 20
        }
      },
      "117": {
        start: {
          line: 482,
          column: 40
        },
        end: {
          line: 482,
          column: 42
        }
      },
      "118": {
        start: {
          line: 485,
          column: 22
        },
        end: {
          line: 485,
          column: 47
        }
      },
      "119": {
        start: {
          line: 486,
          column: 4
        },
        end: {
          line: 489,
          column: 7
        }
      },
      "120": {
        start: {
          line: 487,
          column: 19
        },
        end: {
          line: 487,
          column: 44
        }
      },
      "121": {
        start: {
          line: 488,
          column: 6
        },
        end: {
          line: 488,
          column: 58
        }
      },
      "122": {
        start: {
          line: 492,
          column: 4
        },
        end: {
          line: 510,
          column: 7
        }
      },
      "123": {
        start: {
          line: 493,
          column: 6
        },
        end: {
          line: 509,
          column: 7
        }
      },
      "124": {
        start: {
          line: 494,
          column: 8
        },
        end: {
          line: 508,
          column: 11
        }
      },
      "125": {
        start: {
          line: 512,
          column: 4
        },
        end: {
          line: 512,
          column: 20
        }
      },
      "126": {
        start: {
          line: 516,
          column: 40
        },
        end: {
          line: 516,
          column: 42
        }
      },
      "127": {
        start: {
          line: 519,
          column: 25
        },
        end: {
          line: 519,
          column: 50
        }
      },
      "128": {
        start: {
          line: 520,
          column: 4
        },
        end: {
          line: 524,
          column: 7
        }
      },
      "129": {
        start: {
          line: 521,
          column: 6
        },
        end: {
          line: 523,
          column: 9
        }
      },
      "130": {
        start: {
          line: 522,
          column: 8
        },
        end: {
          line: 522,
          column: 94
        }
      },
      "131": {
        start: {
          line: 527,
          column: 4
        },
        end: {
          line: 545,
          column: 7
        }
      },
      "132": {
        start: {
          line: 528,
          column: 6
        },
        end: {
          line: 544,
          column: 7
        }
      },
      "133": {
        start: {
          line: 529,
          column: 8
        },
        end: {
          line: 543,
          column: 11
        }
      },
      "134": {
        start: {
          line: 547,
          column: 4
        },
        end: {
          line: 547,
          column: 20
        }
      },
      "135": {
        start: {
          line: 551,
          column: 40
        },
        end: {
          line: 551,
          column: 42
        }
      },
      "136": {
        start: {
          line: 554,
          column: 24
        },
        end: {
          line: 554,
          column: 109
        }
      },
      "137": {
        start: {
          line: 554,
          column: 52
        },
        end: {
          line: 554,
          column: 87
        }
      },
      "138": {
        start: {
          line: 556,
          column: 4
        },
        end: {
          line: 572,
          column: 5
        }
      },
      "139": {
        start: {
          line: 557,
          column: 6
        },
        end: {
          line: 571,
          column: 9
        }
      },
      "140": {
        start: {
          line: 574,
          column: 4
        },
        end: {
          line: 574,
          column: 20
        }
      },
      "141": {
        start: {
          line: 579,
          column: 20
        },
        end: {
          line: 579,
          column: 55
        }
      },
      "142": {
        start: {
          line: 580,
          column: 4
        },
        end: {
          line: 580,
          column: 43
        }
      },
      "143": {
        start: {
          line: 583,
          column: 4
        },
        end: {
          line: 583,
          column: 58
        }
      },
      "144": {
        start: {
          line: 588,
          column: 31
        },
        end: {
          line: 588,
          column: 90
        }
      },
      "145": {
        start: {
          line: 588,
          column: 52
        },
        end: {
          line: 588,
          column: 82
        }
      },
      "146": {
        start: {
          line: 589,
          column: 28
        },
        end: {
          line: 589,
          column: 84
        }
      },
      "147": {
        start: {
          line: 589,
          column: 49
        },
        end: {
          line: 589,
          column: 76
        }
      },
      "148": {
        start: {
          line: 591,
          column: 4
        },
        end: {
          line: 635,
          column: 5
        }
      },
      "149": {
        start: {
          line: 592,
          column: 6
        },
        end: {
          line: 612,
          column: 8
        }
      },
      "150": {
        start: {
          line: 614,
          column: 6
        },
        end: {
          line: 634,
          column: 8
        }
      },
      "151": {
        start: {
          line: 640,
          column: 24
        },
        end: {
          line: 640,
          column: 64
        }
      },
      "152": {
        start: {
          line: 642,
          column: 4
        },
        end: {
          line: 642,
          column: 84
        }
      },
      "153": {
        start: {
          line: 649,
          column: 41
        },
        end: {
          line: 649,
          column: 43
        }
      },
      "154": {
        start: {
          line: 652,
          column: 4
        },
        end: {
          line: 665,
          column: 7
        }
      },
      "155": {
        start: {
          line: 653,
          column: 6
        },
        end: {
          line: 664,
          column: 7
        }
      },
      "156": {
        start: {
          line: 654,
          column: 8
        },
        end: {
          line: 663,
          column: 11
        }
      },
      "157": {
        start: {
          line: 667,
          column: 4
        },
        end: {
          line: 667,
          column: 45
        }
      },
      "158": {
        start: {
          line: 670,
          column: 4
        },
        end: {
          line: 672,
          column: 5
        }
      },
      "159": {
        start: {
          line: 671,
          column: 6
        },
        end: {
          line: 671,
          column: 67
        }
      },
      "160": {
        start: {
          line: 677,
          column: 4
        },
        end: {
          line: 677,
          column: 54
        }
      },
      "161": {
        start: {
          line: 681,
          column: 4
        },
        end: {
          line: 683,
          column: 46
        }
      },
      "162": {
        start: {
          line: 682,
          column: 6
        },
        end: {
          line: 682,
          column: 37
        }
      },
      "163": {
        start: {
          line: 687,
          column: 4
        },
        end: {
          line: 687,
          column: 60
        }
      },
      "164": {
        start: {
          line: 690,
          column: 4
        },
        end: {
          line: 698,
          column: 5
        }
      },
      "165": {
        start: {
          line: 691,
          column: 29
        },
        end: {
          line: 693,
          column: 7
        }
      },
      "166": {
        start: {
          line: 692,
          column: 8
        },
        end: {
          line: 692,
          column: 43
        }
      },
      "167": {
        start: {
          line: 695,
          column: 6
        },
        end: {
          line: 697,
          column: 7
        }
      },
      "168": {
        start: {
          line: 696,
          column: 8
        },
        end: {
          line: 696,
          column: 47
        }
      },
      "169": {
        start: {
          line: 703,
          column: 4
        },
        end: {
          line: 703,
          column: 45
        }
      },
      "170": {
        start: {
          line: 708,
          column: 41
        },
        end: {
          line: 708,
          column: 72
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 130,
            column: 2
          },
          end: {
            line: 130,
            column: 3
          }
        },
        loc: {
          start: {
            line: 130,
            column: 16
          },
          end: {
            line: 132,
            column: 3
          }
        },
        line: 130
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 137,
            column: 2
          },
          end: {
            line: 137,
            column: 3
          }
        },
        loc: {
          start: {
            line: 137,
            column: 63
          },
          end: {
            line: 152,
            column: 3
          }
        },
        line: 137
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 157,
            column: 2
          },
          end: {
            line: 157,
            column: 3
          }
        },
        loc: {
          start: {
            line: 157,
            column: 64
          },
          end: {
            line: 186,
            column: 3
          }
        },
        line: 157
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 191,
            column: 2
          },
          end: {
            line: 191,
            column: 3
          }
        },
        loc: {
          start: {
            line: 196,
            column: 10
          },
          end: {
            line: 229,
            column: 3
          }
        },
        line: 196
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 234,
            column: 2
          },
          end: {
            line: 234,
            column: 3
          }
        },
        loc: {
          start: {
            line: 238,
            column: 10
          },
          end: {
            line: 270,
            column: 3
          }
        },
        line: 238
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 275,
            column: 2
          },
          end: {
            line: 275,
            column: 3
          }
        },
        loc: {
          start: {
            line: 275,
            column: 54
          },
          end: {
            line: 295,
            column: 3
          }
        },
        line: 275
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 300,
            column: 2
          },
          end: {
            line: 300,
            column: 3
          }
        },
        loc: {
          start: {
            line: 300,
            column: 72
          },
          end: {
            line: 340,
            column: 3
          }
        },
        line: 300
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 345,
            column: 2
          },
          end: {
            line: 345,
            column: 3
          }
        },
        loc: {
          start: {
            line: 345,
            column: 53
          },
          end: {
            line: 347,
            column: 3
          }
        },
        line: 345
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 352,
            column: 2
          },
          end: {
            line: 352,
            column: 3
          }
        },
        loc: {
          start: {
            line: 352,
            column: 60
          },
          end: {
            line: 359,
            column: 3
          }
        },
        line: 352
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 354,
            column: 43
          },
          end: {
            line: 354,
            column: 44
          }
        },
        loc: {
          start: {
            line: 355,
            column: 8
          },
          end: {
            line: 355,
            column: 41
          }
        },
        line: 355
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 364,
            column: 2
          },
          end: {
            line: 364,
            column: 3
          }
        },
        loc: {
          start: {
            line: 371,
            column: 4
          },
          end: {
            line: 415,
            column: 3
          }
        },
        line: 371
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 378,
            column: 21
          },
          end: {
            line: 378,
            column: 22
          }
        },
        loc: {
          start: {
            line: 378,
            column: 32
          },
          end: {
            line: 382,
            column: 5
          }
        },
        line: 378
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 379,
            column: 34
          },
          end: {
            line: 379,
            column: 35
          }
        },
        loc: {
          start: {
            line: 379,
            column: 42
          },
          end: {
            line: 381,
            column: 7
          }
        },
        line: 379
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 385,
            column: 11
          },
          end: {
            line: 385,
            column: 12
          }
        },
        loc: {
          start: {
            line: 385,
            column: 33
          },
          end: {
            line: 385,
            column: 50
          }
        },
        line: 385
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 386,
            column: 12
          },
          end: {
            line: 386,
            column: 13
          }
        },
        loc: {
          start: {
            line: 386,
            column: 22
          },
          end: {
            line: 386,
            column: 39
          }
        },
        line: 386
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 390,
            column: 46
          },
          end: {
            line: 390,
            column: 47
          }
        },
        loc: {
          start: {
            line: 390,
            column: 51
          },
          end: {
            line: 390,
            column: 61
          }
        },
        line: 390
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 392,
            column: 33
          },
          end: {
            line: 392,
            column: 34
          }
        },
        loc: {
          start: {
            line: 392,
            column: 45
          },
          end: {
            line: 392,
            column: 68
          }
        },
        line: 392
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 397,
            column: 21
          },
          end: {
            line: 397,
            column: 22
          }
        },
        loc: {
          start: {
            line: 397,
            column: 32
          },
          end: {
            line: 400,
            column: 5
          }
        },
        line: 397
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 403,
            column: 12
          },
          end: {
            line: 403,
            column: 13
          }
        },
        loc: {
          start: {
            line: 403,
            column: 22
          },
          end: {
            line: 403,
            column: 33
          }
        },
        line: 403
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 405,
            column: 11
          },
          end: {
            line: 405,
            column: 12
          }
        },
        loc: {
          start: {
            line: 405,
            column: 23
          },
          end: {
            line: 405,
            column: 27
          }
        },
        line: 405
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 419,
            column: 2
          },
          end: {
            line: 419,
            column: 3
          }
        },
        loc: {
          start: {
            line: 419,
            column: 77
          },
          end: {
            line: 422,
            column: 3
          }
        },
        line: 419
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 421,
            column: 25
          },
          end: {
            line: 421,
            column: 26
          }
        },
        loc: {
          start: {
            line: 421,
            column: 30
          },
          end: {
            line: 421,
            column: 55
          }
        },
        line: 421
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 424,
            column: 2
          },
          end: {
            line: 424,
            column: 3
          }
        },
        loc: {
          start: {
            line: 424,
            column: 52
          },
          end: {
            line: 426,
            column: 3
          }
        },
        line: 424
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 428,
            column: 2
          },
          end: {
            line: 428,
            column: 3
          }
        },
        loc: {
          start: {
            line: 428,
            column: 62
          },
          end: {
            line: 440,
            column: 3
          }
        },
        line: 428
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 430,
            column: 46
          },
          end: {
            line: 430,
            column: 47
          }
        },
        loc: {
          start: {
            line: 430,
            column: 51
          },
          end: {
            line: 430,
            column: 61
          }
        },
        line: 430
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 432,
            column: 25
          },
          end: {
            line: 432,
            column: 26
          }
        },
        loc: {
          start: {
            line: 432,
            column: 40
          },
          end: {
            line: 432,
            column: 50
          }
        },
        line: 432
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 442,
            column: 2
          },
          end: {
            line: 442,
            column: 3
          }
        },
        loc: {
          start: {
            line: 442,
            column: 80
          },
          end: {
            line: 479,
            column: 3
          }
        },
        line: 442
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 448,
            column: 21
          },
          end: {
            line: 448,
            column: 22
          }
        },
        loc: {
          start: {
            line: 448,
            column: 32
          },
          end: {
            line: 455,
            column: 5
          }
        },
        line: 448
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 458,
            column: 22
          },
          end: {
            line: 458,
            column: 23
          }
        },
        loc: {
          start: {
            line: 458,
            column: 43
          },
          end: {
            line: 476,
            column: 5
          }
        },
        line: 458
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 481,
            column: 2
          },
          end: {
            line: 481,
            column: 3
          }
        },
        loc: {
          start: {
            line: 481,
            column: 74
          },
          end: {
            line: 513,
            column: 3
          }
        },
        line: 481
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 486,
            column: 21
          },
          end: {
            line: 486,
            column: 22
          }
        },
        loc: {
          start: {
            line: 486,
            column: 32
          },
          end: {
            line: 489,
            column: 5
          }
        },
        line: 486
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 492,
            column: 22
          },
          end: {
            line: 492,
            column: 23
          }
        },
        loc: {
          start: {
            line: 492,
            column: 39
          },
          end: {
            line: 510,
            column: 5
          }
        },
        line: 492
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 515,
            column: 2
          },
          end: {
            line: 515,
            column: 3
          }
        },
        loc: {
          start: {
            line: 515,
            column: 74
          },
          end: {
            line: 548,
            column: 3
          }
        },
        line: 515
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 520,
            column: 21
          },
          end: {
            line: 520,
            column: 22
          }
        },
        loc: {
          start: {
            line: 520,
            column: 32
          },
          end: {
            line: 524,
            column: 5
          }
        },
        line: 520
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 521,
            column: 35
          },
          end: {
            line: 521,
            column: 36
          }
        },
        loc: {
          start: {
            line: 521,
            column: 50
          },
          end: {
            line: 523,
            column: 7
          }
        },
        line: 521
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 527,
            column: 25
          },
          end: {
            line: 527,
            column: 26
          }
        },
        loc: {
          start: {
            line: 527,
            column: 45
          },
          end: {
            line: 545,
            column: 5
          }
        },
        line: 527
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 550,
            column: 2
          },
          end: {
            line: 550,
            column: 3
          }
        },
        loc: {
          start: {
            line: 550,
            column: 81
          },
          end: {
            line: 575,
            column: 3
          }
        },
        line: 550
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 554,
            column: 40
          },
          end: {
            line: 554,
            column: 41
          }
        },
        loc: {
          start: {
            line: 554,
            column: 52
          },
          end: {
            line: 554,
            column: 87
          }
        },
        line: 554
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 577,
            column: 2
          },
          end: {
            line: 577,
            column: 3
          }
        },
        loc: {
          start: {
            line: 577,
            column: 94
          },
          end: {
            line: 584,
            column: 3
          }
        },
        line: 577
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 586,
            column: 2
          },
          end: {
            line: 586,
            column: 3
          }
        },
        loc: {
          start: {
            line: 586,
            column: 73
          },
          end: {
            line: 636,
            column: 3
          }
        },
        line: 586
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 588,
            column: 47
          },
          end: {
            line: 588,
            column: 48
          }
        },
        loc: {
          start: {
            line: 588,
            column: 52
          },
          end: {
            line: 588,
            column: 82
          }
        },
        line: 588
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 589,
            column: 44
          },
          end: {
            line: 589,
            column: 45
          }
        },
        loc: {
          start: {
            line: 589,
            column: 49
          },
          end: {
            line: 589,
            column: 76
          }
        },
        line: 589
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 638,
            column: 2
          },
          end: {
            line: 638,
            column: 3
          }
        },
        loc: {
          start: {
            line: 638,
            column: 95
          },
          end: {
            line: 643,
            column: 3
          }
        },
        line: 638
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 645,
            column: 2
          },
          end: {
            line: 645,
            column: 3
          }
        },
        loc: {
          start: {
            line: 648,
            column: 19
          },
          end: {
            line: 673,
            column: 3
          }
        },
        line: 648
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 652,
            column: 21
          },
          end: {
            line: 652,
            column: 22
          }
        },
        loc: {
          start: {
            line: 652,
            column: 32
          },
          end: {
            line: 665,
            column: 5
          }
        },
        line: 652
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 675,
            column: 2
          },
          end: {
            line: 675,
            column: 3
          }
        },
        loc: {
          start: {
            line: 675,
            column: 51
          },
          end: {
            line: 678,
            column: 3
          }
        },
        line: 675
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 680,
            column: 2
          },
          end: {
            line: 680,
            column: 3
          }
        },
        loc: {
          start: {
            line: 680,
            column: 40
          },
          end: {
            line: 684,
            column: 3
          }
        },
        line: 680
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 681,
            column: 16
          },
          end: {
            line: 681,
            column: 17
          }
        },
        loc: {
          start: {
            line: 681,
            column: 22
          },
          end: {
            line: 683,
            column: 5
          }
        },
        line: 681
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 686,
            column: 2
          },
          end: {
            line: 686,
            column: 3
          }
        },
        loc: {
          start: {
            line: 686,
            column: 57
          },
          end: {
            line: 699,
            column: 3
          }
        },
        line: 686
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 691,
            column: 45
          },
          end: {
            line: 691,
            column: 46
          }
        },
        loc: {
          start: {
            line: 692,
            column: 8
          },
          end: {
            line: 692,
            column: 43
          }
        },
        line: 692
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 701,
            column: 2
          },
          end: {
            line: 701,
            column: 3
          }
        },
        loc: {
          start: {
            line: 701,
            column: 41
          },
          end: {
            line: 704,
            column: 3
          }
        },
        line: 701
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 178,
            column: 4
          },
          end: {
            line: 180,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 178,
            column: 4
          },
          end: {
            line: 180,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 178
      },
      "1": {
        loc: {
          start: {
            line: 198,
            column: 4
          },
          end: {
            line: 198,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 198,
            column: 4
          },
          end: {
            line: 198,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 198
      },
      "2": {
        loc: {
          start: {
            line: 211,
            column: 4
          },
          end: {
            line: 213,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 211,
            column: 4
          },
          end: {
            line: 213,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 211
      },
      "3": {
        loc: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 240,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 240,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "4": {
        loc: {
          start: {
            line: 251,
            column: 4
          },
          end: {
            line: 253,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 4
          },
          end: {
            line: 253,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "5": {
        loc: {
          start: {
            line: 277,
            column: 4
          },
          end: {
            line: 277,
            column: 25
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 277,
            column: 4
          },
          end: {
            line: 277,
            column: 25
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 277
      },
      "6": {
        loc: {
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 286,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 284,
            column: 4
          },
          end: {
            line: 286,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 284
      },
      "7": {
        loc: {
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 284,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 284,
            column: 8
          },
          end: {
            line: 284,
            column: 22
          }
        }, {
          start: {
            line: 284,
            column: 26
          },
          end: {
            line: 284,
            column: 50
          }
        }],
        line: 284
      },
      "8": {
        loc: {
          start: {
            line: 301,
            column: 25
          },
          end: {
            line: 301,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 301,
            column: 25
          },
          end: {
            line: 301,
            column: 54
          }
        }, {
          start: {
            line: 301,
            column: 58
          },
          end: {
            line: 301,
            column: 60
          }
        }],
        line: 301
      },
      "9": {
        loc: {
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 304,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 304,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "10": {
        loc: {
          start: {
            line: 346,
            column: 11
          },
          end: {
            line: 346,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 346,
            column: 11
          },
          end: {
            line: 346,
            column: 40
          }
        }, {
          start: {
            line: 346,
            column: 44
          },
          end: {
            line: 346,
            column: 48
          }
        }],
        line: 346
      },
      "11": {
        loc: {
          start: {
            line: 353,
            column: 4
          },
          end: {
            line: 357,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 353,
            column: 4
          },
          end: {
            line: 357,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 353
      },
      "12": {
        loc: {
          start: {
            line: 372,
            column: 21
          },
          end: {
            line: 372,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 372,
            column: 21
          },
          end: {
            line: 372,
            column: 50
          }
        }, {
          start: {
            line: 372,
            column: 54
          },
          end: {
            line: 372,
            column: 56
          }
        }],
        line: 372
      },
      "13": {
        loc: {
          start: {
            line: 373,
            column: 21
          },
          end: {
            line: 373,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 373,
            column: 21
          },
          end: {
            line: 373,
            column: 54
          }
        }, {
          start: {
            line: 373,
            column: 58
          },
          end: {
            line: 373,
            column: 60
          }
        }],
        line: 373
      },
      "14": {
        loc: {
          start: {
            line: 380,
            column: 39
          },
          end: {
            line: 380,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 380,
            column: 39
          },
          end: {
            line: 380,
            column: 68
          }
        }, {
          start: {
            line: 380,
            column: 72
          },
          end: {
            line: 380,
            column: 73
          }
        }],
        line: 380
      },
      "15": {
        loc: {
          start: {
            line: 391,
            column: 35
          },
          end: {
            line: 393,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 392,
            column: 8
          },
          end: {
            line: 392,
            column: 99
          }
        }, {
          start: {
            line: 393,
            column: 8
          },
          end: {
            line: 393,
            column: 9
          }
        }],
        line: 391
      },
      "16": {
        loc: {
          start: {
            line: 392,
            column: 52
          },
          end: {
            line: 392,
            column: 67
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 392,
            column: 52
          },
          end: {
            line: 392,
            column: 62
          }
        }, {
          start: {
            line: 392,
            column: 66
          },
          end: {
            line: 392,
            column: 67
          }
        }],
        line: 392
      },
      "17": {
        loc: {
          start: {
            line: 399,
            column: 34
          },
          end: {
            line: 399,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 399,
            column: 34
          },
          end: {
            line: 399,
            column: 60
          }
        }, {
          start: {
            line: 399,
            column: 64
          },
          end: {
            line: 399,
            column: 65
          }
        }],
        line: 399
      },
      "18": {
        loc: {
          start: {
            line: 413,
            column: 15
          },
          end: {
            line: 413,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 413,
            column: 15
          },
          end: {
            line: 413,
            column: 28
          }
        }, {
          start: {
            line: 413,
            column: 32
          },
          end: {
            line: 413,
            column: 41
          }
        }],
        line: 413
      },
      "19": {
        loc: {
          start: {
            line: 420,
            column: 21
          },
          end: {
            line: 420,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 420,
            column: 21
          },
          end: {
            line: 420,
            column: 50
          }
        }, {
          start: {
            line: 420,
            column: 54
          },
          end: {
            line: 420,
            column: 56
          }
        }],
        line: 420
      },
      "20": {
        loc: {
          start: {
            line: 421,
            column: 11
          },
          end: {
            line: 421,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 421,
            column: 11
          },
          end: {
            line: 421,
            column: 56
          }
        }, {
          start: {
            line: 421,
            column: 60
          },
          end: {
            line: 421,
            column: 64
          }
        }],
        line: 421
      },
      "21": {
        loc: {
          start: {
            line: 431,
            column: 42
          },
          end: {
            line: 433,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 432,
            column: 8
          },
          end: {
            line: 432,
            column: 73
          }
        }, {
          start: {
            line: 433,
            column: 8
          },
          end: {
            line: 433,
            column: 9
          }
        }],
        line: 431
      },
      "22": {
        loc: {
          start: {
            line: 453,
            column: 33
          },
          end: {
            line: 453,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 453,
            column: 33
          },
          end: {
            line: 453,
            column: 56
          }
        }, {
          start: {
            line: 453,
            column: 60
          },
          end: {
            line: 453,
            column: 61
          }
        }],
        line: 453
      },
      "23": {
        loc: {
          start: {
            line: 459,
            column: 6
          },
          end: {
            line: 475,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 459,
            column: 6
          },
          end: {
            line: 475,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 459
      },
      "24": {
        loc: {
          start: {
            line: 488,
            column: 27
          },
          end: {
            line: 488,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 488,
            column: 27
          },
          end: {
            line: 488,
            column: 46
          }
        }, {
          start: {
            line: 488,
            column: 50
          },
          end: {
            line: 488,
            column: 51
          }
        }],
        line: 488
      },
      "25": {
        loc: {
          start: {
            line: 493,
            column: 6
          },
          end: {
            line: 509,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 493,
            column: 6
          },
          end: {
            line: 509,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 493
      },
      "26": {
        loc: {
          start: {
            line: 522,
            column: 46
          },
          end: {
            line: 522,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 522,
            column: 46
          },
          end: {
            line: 522,
            column: 82
          }
        }, {
          start: {
            line: 522,
            column: 86
          },
          end: {
            line: 522,
            column: 87
          }
        }],
        line: 522
      },
      "27": {
        loc: {
          start: {
            line: 528,
            column: 6
          },
          end: {
            line: 544,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 528,
            column: 6
          },
          end: {
            line: 544,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 528
      },
      "28": {
        loc: {
          start: {
            line: 556,
            column: 4
          },
          end: {
            line: 572,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 556,
            column: 4
          },
          end: {
            line: 572,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 556
      },
      "29": {
        loc: {
          start: {
            line: 591,
            column: 4
          },
          end: {
            line: 635,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 591,
            column: 4
          },
          end: {
            line: 635,
            column: 5
          }
        }, {
          start: {
            line: 613,
            column: 11
          },
          end: {
            line: 635,
            column: 5
          }
        }],
        line: 591
      },
      "30": {
        loc: {
          start: {
            line: 653,
            column: 6
          },
          end: {
            line: 664,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 653,
            column: 6
          },
          end: {
            line: 664,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 653
      },
      "31": {
        loc: {
          start: {
            line: 658,
            column: 18
          },
          end: {
            line: 658,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 658,
            column: 43
          },
          end: {
            line: 658,
            column: 49
          }
        }, {
          start: {
            line: 658,
            column: 52
          },
          end: {
            line: 658,
            column: 92
          }
        }],
        line: 658
      },
      "32": {
        loc: {
          start: {
            line: 658,
            column: 52
          },
          end: {
            line: 658,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 658,
            column: 76
          },
          end: {
            line: 658,
            column: 84
          }
        }, {
          start: {
            line: 658,
            column: 87
          },
          end: {
            line: 658,
            column: 92
          }
        }],
        line: 658
      },
      "33": {
        loc: {
          start: {
            line: 670,
            column: 4
          },
          end: {
            line: 672,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 670,
            column: 4
          },
          end: {
            line: 672,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 670
      },
      "34": {
        loc: {
          start: {
            line: 695,
            column: 6
          },
          end: {
            line: 697,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 695,
            column: 6
          },
          end: {
            line: 697,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 695
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "39638cb347803eb872d16097964e4743c74fa901"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_x2gc31hu4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_x2gc31hu4();
import { predictiveCacheEngine } from "./PredictiveCacheEngine";
var BehavioralAnalyticsEngine = function () {
  function BehavioralAnalyticsEngine() {
    _classCallCheck(this, BehavioralAnalyticsEngine);
    this.userSessions = (cov_x2gc31hu4().s[0]++, new Map());
    this.behaviorPatterns = (cov_x2gc31hu4().s[1]++, new Map());
    this.userSegments = (cov_x2gc31hu4().s[2]++, new Map());
    this.analyticsInsights = (cov_x2gc31hu4().s[3]++, []);
    this.ANALYSIS_CONFIG = (cov_x2gc31hu4().s[4]++, {
      sessionTimeout: 1800000,
      minPatternFrequency: 3,
      minConfidenceThreshold: 0.7,
      maxSessionHistory: 100,
      analysisInterval: 3600000
    });
    cov_x2gc31hu4().f[0]++;
    cov_x2gc31hu4().s[5]++;
    this.initializeBehavioralAnalytics();
  }
  return _createClass(BehavioralAnalyticsEngine, [{
    key: "initializeBehavioralAnalytics",
    value: (function () {
      var _initializeBehavioralAnalytics = _asyncToGenerator(function* () {
        cov_x2gc31hu4().f[1]++;
        cov_x2gc31hu4().s[6]++;
        try {
          cov_x2gc31hu4().s[7]++;
          yield this.loadAnalyticsData();
          cov_x2gc31hu4().s[8]++;
          this.startPeriodicAnalysis();
          cov_x2gc31hu4().s[9]++;
          this.initializeUserSegments();
          cov_x2gc31hu4().s[10]++;
          console.log('Behavioral Analytics Engine initialized successfully');
        } catch (error) {
          cov_x2gc31hu4().s[11]++;
          console.error('Failed to initialize Behavioral Analytics Engine:', error);
        }
      });
      function initializeBehavioralAnalytics() {
        return _initializeBehavioralAnalytics.apply(this, arguments);
      }
      return initializeBehavioralAnalytics;
    }())
  }, {
    key: "startSession",
    value: function startSession(userId, context) {
      cov_x2gc31hu4().f[2]++;
      var sessionId = (cov_x2gc31hu4().s[12]++, this.generateSessionId(userId));
      var session = (cov_x2gc31hu4().s[13]++, {
        sessionId: sessionId,
        userId: userId,
        startTime: Date.now(),
        screenViews: [],
        interactions: [],
        performance: {
          averageLoadTime: 0,
          crashCount: 0,
          errorCount: 0,
          memoryUsage: [],
          cpuUsage: [],
          networkRequests: 0,
          cacheHitRate: 0
        },
        context: context
      });
      cov_x2gc31hu4().s[14]++;
      if (!this.userSessions.has(userId)) {
        cov_x2gc31hu4().b[0][0]++;
        cov_x2gc31hu4().s[15]++;
        this.userSessions.set(userId, []);
      } else {
        cov_x2gc31hu4().b[0][1]++;
      }
      cov_x2gc31hu4().s[16]++;
      this.userSessions.get(userId).push(session);
      cov_x2gc31hu4().s[17]++;
      console.log(`Started session ${sessionId} for user ${userId}`);
      cov_x2gc31hu4().s[18]++;
      return sessionId;
    }
  }, {
    key: "trackScreenView",
    value: function trackScreenView(userId, sessionId, screen, loadTime) {
      cov_x2gc31hu4().f[3]++;
      var session = (cov_x2gc31hu4().s[19]++, this.findSession(userId, sessionId));
      cov_x2gc31hu4().s[20]++;
      if (!session) {
        cov_x2gc31hu4().b[1][0]++;
        cov_x2gc31hu4().s[21]++;
        return;
      } else {
        cov_x2gc31hu4().b[1][1]++;
      }
      var screenView = (cov_x2gc31hu4().s[22]++, {
        screen: screen,
        timestamp: Date.now(),
        duration: 0,
        loadTime: loadTime,
        exitReason: 'navigation',
        interactionCount: 0
      });
      var previousView = (cov_x2gc31hu4().s[23]++, session.screenViews[session.screenViews.length - 1]);
      cov_x2gc31hu4().s[24]++;
      if (previousView) {
        cov_x2gc31hu4().b[2][0]++;
        cov_x2gc31hu4().s[25]++;
        previousView.duration = screenView.timestamp - previousView.timestamp;
      } else {
        cov_x2gc31hu4().b[2][1]++;
      }
      cov_x2gc31hu4().s[26]++;
      session.screenViews.push(screenView);
      cov_x2gc31hu4().s[27]++;
      predictiveCacheEngine.trackUserBehavior(userId, {
        type: 'screen_view',
        target: screen,
        timestamp: Date.now(),
        duration: loadTime
      }, session.context, session.context);
    }
  }, {
    key: "trackInteraction",
    value: function trackInteraction(userId, sessionId, interaction) {
      cov_x2gc31hu4().f[4]++;
      var session = (cov_x2gc31hu4().s[28]++, this.findSession(userId, sessionId));
      cov_x2gc31hu4().s[29]++;
      if (!session) {
        cov_x2gc31hu4().b[3][0]++;
        cov_x2gc31hu4().s[30]++;
        return;
      } else {
        cov_x2gc31hu4().b[3][1]++;
      }
      var fullInteraction = (cov_x2gc31hu4().s[31]++, Object.assign({}, interaction, {
        timestamp: Date.now()
      }));
      cov_x2gc31hu4().s[32]++;
      session.interactions.push(fullInteraction);
      var currentScreenView = (cov_x2gc31hu4().s[33]++, session.screenViews[session.screenViews.length - 1]);
      cov_x2gc31hu4().s[34]++;
      if (currentScreenView) {
        cov_x2gc31hu4().b[4][0]++;
        cov_x2gc31hu4().s[35]++;
        currentScreenView.interactionCount++;
      } else {
        cov_x2gc31hu4().b[4][1]++;
      }
      cov_x2gc31hu4().s[36]++;
      predictiveCacheEngine.trackUserBehavior(userId, {
        type: 'interaction',
        target: interaction.target,
        timestamp: Date.now(),
        metadata: {
          interactionType: interaction.type,
          success: interaction.success
        }
      }, session.context, session.context);
    }
  }, {
    key: "endSession",
    value: function endSession(userId, sessionId) {
      cov_x2gc31hu4().f[5]++;
      var session = (cov_x2gc31hu4().s[37]++, this.findSession(userId, sessionId));
      cov_x2gc31hu4().s[38]++;
      if (!session) {
        cov_x2gc31hu4().b[5][0]++;
        cov_x2gc31hu4().s[39]++;
        return;
      } else {
        cov_x2gc31hu4().b[5][1]++;
      }
      cov_x2gc31hu4().s[40]++;
      session.endTime = Date.now();
      cov_x2gc31hu4().s[41]++;
      session.duration = session.endTime - session.startTime;
      var lastScreenView = (cov_x2gc31hu4().s[42]++, session.screenViews[session.screenViews.length - 1]);
      cov_x2gc31hu4().s[43]++;
      if ((cov_x2gc31hu4().b[7][0]++, lastScreenView) && (cov_x2gc31hu4().b[7][1]++, !lastScreenView.duration)) {
        cov_x2gc31hu4().b[6][0]++;
        cov_x2gc31hu4().s[44]++;
        lastScreenView.duration = session.endTime - lastScreenView.timestamp;
      } else {
        cov_x2gc31hu4().b[6][1]++;
      }
      cov_x2gc31hu4().s[45]++;
      this.calculateSessionMetrics(session);
      cov_x2gc31hu4().s[46]++;
      this.analyzeUserBehavior(userId);
      cov_x2gc31hu4().s[47]++;
      console.log(`Ended session ${sessionId} for user ${userId} (duration: ${session.duration}ms)`);
    }
  }, {
    key: "analyzeUserBehavior",
    value: (function () {
      var _analyzeUserBehavior = _asyncToGenerator(function* (userId) {
        cov_x2gc31hu4().f[6]++;
        var userSessions = (cov_x2gc31hu4().s[48]++, (cov_x2gc31hu4().b[8][0]++, this.userSessions.get(userId)) || (cov_x2gc31hu4().b[8][1]++, []));
        cov_x2gc31hu4().s[49]++;
        if (userSessions.length < 3) {
          cov_x2gc31hu4().b[9][0]++;
          cov_x2gc31hu4().s[50]++;
          return [];
        } else {
          cov_x2gc31hu4().b[9][1]++;
        }
        var patterns = (cov_x2gc31hu4().s[51]++, []);
        cov_x2gc31hu4().s[52]++;
        try {
          var navigationPatterns = (cov_x2gc31hu4().s[53]++, this.analyzeNavigationPatterns(userSessions));
          cov_x2gc31hu4().s[54]++;
          patterns.push.apply(patterns, _toConsumableArray(navigationPatterns));
          var timePatterns = (cov_x2gc31hu4().s[55]++, this.analyzeTimePatterns(userSessions));
          cov_x2gc31hu4().s[56]++;
          patterns.push.apply(patterns, _toConsumableArray(timePatterns));
          var featurePatterns = (cov_x2gc31hu4().s[57]++, this.analyzeFeatureUsage(userSessions));
          cov_x2gc31hu4().s[58]++;
          patterns.push.apply(patterns, _toConsumableArray(featurePatterns));
          var performancePatterns = (cov_x2gc31hu4().s[59]++, this.analyzePerformancePatterns(userSessions));
          cov_x2gc31hu4().s[60]++;
          patterns.push.apply(patterns, _toConsumableArray(performancePatterns));
          cov_x2gc31hu4().s[61]++;
          this.behaviorPatterns.set(userId, patterns);
          cov_x2gc31hu4().s[62]++;
          yield this.updateUserSegment(userId, patterns);
          cov_x2gc31hu4().s[63]++;
          yield this.generateOptimizationRecommendations(userId, patterns);
          cov_x2gc31hu4().s[64]++;
          return patterns;
        } catch (error) {
          cov_x2gc31hu4().s[65]++;
          console.error('Failed to analyze user behavior:', error);
          cov_x2gc31hu4().s[66]++;
          return [];
        }
      });
      function analyzeUserBehavior(_x) {
        return _analyzeUserBehavior.apply(this, arguments);
      }
      return analyzeUserBehavior;
    }())
  }, {
    key: "getUserSegment",
    value: function getUserSegment(userId) {
      cov_x2gc31hu4().f[7]++;
      cov_x2gc31hu4().s[67]++;
      return (cov_x2gc31hu4().b[10][0]++, this.userSegments.get(userId)) || (cov_x2gc31hu4().b[10][1]++, null);
    }
  }, {
    key: "getAnalyticsInsights",
    value: function getAnalyticsInsights(userId) {
      cov_x2gc31hu4().f[8]++;
      cov_x2gc31hu4().s[68]++;
      if (userId) {
        cov_x2gc31hu4().b[11][0]++;
        cov_x2gc31hu4().s[69]++;
        return this.analyticsInsights.filter(function (insight) {
          cov_x2gc31hu4().f[9]++;
          cov_x2gc31hu4().s[70]++;
          return insight.metrics.userId === userId;
        });
      } else {
        cov_x2gc31hu4().b[11][1]++;
      }
      cov_x2gc31hu4().s[71]++;
      return this.analyticsInsights;
    }
  }, {
    key: "getUserBehaviorSummary",
    value: function getUserBehaviorSummary(userId) {
      cov_x2gc31hu4().f[10]++;
      var sessions = (cov_x2gc31hu4().s[72]++, (cov_x2gc31hu4().b[12][0]++, this.userSessions.get(userId)) || (cov_x2gc31hu4().b[12][1]++, []));
      var patterns = (cov_x2gc31hu4().s[73]++, (cov_x2gc31hu4().b[13][0]++, this.behaviorPatterns.get(userId)) || (cov_x2gc31hu4().b[13][1]++, []));
      var segment = (cov_x2gc31hu4().s[74]++, this.userSegments.get(userId));
      var screenCounts = (cov_x2gc31hu4().s[75]++, new Map());
      cov_x2gc31hu4().s[76]++;
      sessions.forEach(function (session) {
        cov_x2gc31hu4().f[11]++;
        cov_x2gc31hu4().s[77]++;
        session.screenViews.forEach(function (view) {
          cov_x2gc31hu4().f[12]++;
          cov_x2gc31hu4().s[78]++;
          screenCounts.set(view.screen, ((cov_x2gc31hu4().b[14][0]++, screenCounts.get(view.screen)) || (cov_x2gc31hu4().b[14][1]++, 0)) + 1);
        });
      });
      var mostUsedScreens = (cov_x2gc31hu4().s[79]++, Array.from(screenCounts.entries()).map(function (_ref) {
        var _ref2 = _slicedToArray(_ref, 2),
          screen = _ref2[0],
          count = _ref2[1];
        cov_x2gc31hu4().f[13]++;
        cov_x2gc31hu4().s[80]++;
        return {
          screen: screen,
          count: count
        };
      }).sort(function (a, b) {
        cov_x2gc31hu4().f[14]++;
        cov_x2gc31hu4().s[81]++;
        return b.count - a.count;
      }).slice(0, 5));
      var completedSessions = (cov_x2gc31hu4().s[82]++, sessions.filter(function (s) {
        cov_x2gc31hu4().f[15]++;
        cov_x2gc31hu4().s[83]++;
        return s.duration;
      }));
      var averageSessionDuration = (cov_x2gc31hu4().s[84]++, completedSessions.length > 0 ? (cov_x2gc31hu4().b[15][0]++, completedSessions.reduce(function (sum, s) {
        cov_x2gc31hu4().f[16]++;
        cov_x2gc31hu4().s[85]++;
        return sum + ((cov_x2gc31hu4().b[16][0]++, s.duration) || (cov_x2gc31hu4().b[16][1]++, 0));
      }, 0) / completedSessions.length) : (cov_x2gc31hu4().b[15][1]++, 0));
      var timeDistribution = (cov_x2gc31hu4().s[86]++, new Map());
      cov_x2gc31hu4().s[87]++;
      sessions.forEach(function (session) {
        cov_x2gc31hu4().f[17]++;
        var time = (cov_x2gc31hu4().s[88]++, session.context.timeOfDay);
        cov_x2gc31hu4().s[89]++;
        timeDistribution.set(time, ((cov_x2gc31hu4().b[17][0]++, timeDistribution.get(time)) || (cov_x2gc31hu4().b[17][1]++, 0)) + 1);
      });
      var preferredTimes = (cov_x2gc31hu4().s[90]++, Array.from(timeDistribution.entries()).sort(function (a, b) {
        cov_x2gc31hu4().f[18]++;
        cov_x2gc31hu4().s[91]++;
        return b[1] - a[1];
      }).slice(0, 2).map(function (_ref3) {
        var _ref4 = _slicedToArray(_ref3, 1),
          time = _ref4[0];
        cov_x2gc31hu4().f[19]++;
        cov_x2gc31hu4().s[92]++;
        return time;
      }));
      cov_x2gc31hu4().s[93]++;
      return {
        totalSessions: sessions.length,
        averageSessionDuration: averageSessionDuration,
        mostUsedScreens: mostUsedScreens,
        preferredTimes: preferredTimes,
        behaviorPatterns: patterns.length,
        segment: (cov_x2gc31hu4().b[18][0]++, segment == null ? void 0 : segment.name) || (cov_x2gc31hu4().b[18][1]++, 'Unknown')
      };
    }
  }, {
    key: "findSession",
    value: function findSession(userId, sessionId) {
      cov_x2gc31hu4().f[20]++;
      var sessions = (cov_x2gc31hu4().s[94]++, (cov_x2gc31hu4().b[19][0]++, this.userSessions.get(userId)) || (cov_x2gc31hu4().b[19][1]++, []));
      cov_x2gc31hu4().s[95]++;
      return (cov_x2gc31hu4().b[20][0]++, sessions.find(function (s) {
        cov_x2gc31hu4().f[21]++;
        cov_x2gc31hu4().s[96]++;
        return s.sessionId === sessionId;
      })) || (cov_x2gc31hu4().b[20][1]++, null);
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId(userId) {
      cov_x2gc31hu4().f[22]++;
      cov_x2gc31hu4().s[97]++;
      return `session_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "calculateSessionMetrics",
    value: function calculateSessionMetrics(session) {
      cov_x2gc31hu4().f[23]++;
      var loadTimes = (cov_x2gc31hu4().s[98]++, session.screenViews.map(function (v) {
        cov_x2gc31hu4().f[24]++;
        cov_x2gc31hu4().s[99]++;
        return v.loadTime;
      }));
      cov_x2gc31hu4().s[100]++;
      session.performance.averageLoadTime = loadTimes.length > 0 ? (cov_x2gc31hu4().b[21][0]++, loadTimes.reduce(function (sum, time) {
        cov_x2gc31hu4().f[25]++;
        cov_x2gc31hu4().s[101]++;
        return sum + time;
      }, 0) / loadTimes.length) : (cov_x2gc31hu4().b[21][1]++, 0);
      cov_x2gc31hu4().s[102]++;
      session.performance.networkRequests = session.screenViews.length * 2;
      cov_x2gc31hu4().s[103]++;
      session.performance.cacheHitRate = Math.random() * 0.3 + 0.7;
    }
  }, {
    key: "analyzeNavigationPatterns",
    value: function analyzeNavigationPatterns(sessions) {
      var _this = this;
      cov_x2gc31hu4().f[26]++;
      var patterns = (cov_x2gc31hu4().s[104]++, []);
      var sequences = (cov_x2gc31hu4().s[105]++, new Map());
      cov_x2gc31hu4().s[106]++;
      sessions.forEach(function (session) {
        cov_x2gc31hu4().f[27]++;
        cov_x2gc31hu4().s[107]++;
        for (var i = (cov_x2gc31hu4().s[108]++, 0); i < session.screenViews.length - 1; i++) {
          var current = (cov_x2gc31hu4().s[109]++, session.screenViews[i].screen);
          var next = (cov_x2gc31hu4().s[110]++, session.screenViews[i + 1].screen);
          var sequence = (cov_x2gc31hu4().s[111]++, `${current}->${next}`);
          cov_x2gc31hu4().s[112]++;
          sequences.set(sequence, ((cov_x2gc31hu4().b[22][0]++, sequences.get(sequence)) || (cov_x2gc31hu4().b[22][1]++, 0)) + 1);
        }
      });
      cov_x2gc31hu4().s[113]++;
      sequences.forEach(function (count, sequence) {
        cov_x2gc31hu4().f[28]++;
        cov_x2gc31hu4().s[114]++;
        if (count >= _this.ANALYSIS_CONFIG.minPatternFrequency) {
          cov_x2gc31hu4().b[23][0]++;
          cov_x2gc31hu4().s[115]++;
          patterns.push({
            patternId: `nav_${sequence.replace('->', '_to_')}`,
            name: `Navigation: ${sequence}`,
            description: `User frequently navigates ${sequence}`,
            frequency: count,
            confidence: Math.min(count / sessions.length, 1),
            triggers: [sequence.split('->')[0]],
            outcomes: [sequence.split('->')[1]],
            userSegment: 'navigation',
            timePattern: {
              preferredTimes: [],
              sessionDuration: 0,
              frequency: 'daily'
            }
          });
        } else {
          cov_x2gc31hu4().b[23][1]++;
        }
      });
      cov_x2gc31hu4().s[116]++;
      return patterns;
    }
  }, {
    key: "analyzeTimePatterns",
    value: function analyzeTimePatterns(sessions) {
      var _this2 = this;
      cov_x2gc31hu4().f[29]++;
      var patterns = (cov_x2gc31hu4().s[117]++, []);
      var timeUsage = (cov_x2gc31hu4().s[118]++, new Map());
      cov_x2gc31hu4().s[119]++;
      sessions.forEach(function (session) {
        cov_x2gc31hu4().f[30]++;
        var time = (cov_x2gc31hu4().s[120]++, session.context.timeOfDay);
        cov_x2gc31hu4().s[121]++;
        timeUsage.set(time, ((cov_x2gc31hu4().b[24][0]++, timeUsage.get(time)) || (cov_x2gc31hu4().b[24][1]++, 0)) + 1);
      });
      cov_x2gc31hu4().s[122]++;
      timeUsage.forEach(function (count, time) {
        cov_x2gc31hu4().f[31]++;
        cov_x2gc31hu4().s[123]++;
        if (count >= _this2.ANALYSIS_CONFIG.minPatternFrequency) {
          cov_x2gc31hu4().b[25][0]++;
          cov_x2gc31hu4().s[124]++;
          patterns.push({
            patternId: `time_${time}`,
            name: `${time.charAt(0).toUpperCase() + time.slice(1)} Usage`,
            description: `User is active during ${time}`,
            frequency: count,
            confidence: count / sessions.length,
            triggers: [time],
            outcomes: ['app_usage'],
            userSegment: 'temporal',
            timePattern: {
              preferredTimes: [time],
              sessionDuration: 0,
              frequency: 'daily'
            }
          });
        } else {
          cov_x2gc31hu4().b[25][1]++;
        }
      });
      cov_x2gc31hu4().s[125]++;
      return patterns;
    }
  }, {
    key: "analyzeFeatureUsage",
    value: function analyzeFeatureUsage(sessions) {
      var _this3 = this;
      cov_x2gc31hu4().f[32]++;
      var patterns = (cov_x2gc31hu4().s[126]++, []);
      var featureUsage = (cov_x2gc31hu4().s[127]++, new Map());
      cov_x2gc31hu4().s[128]++;
      sessions.forEach(function (session) {
        cov_x2gc31hu4().f[33]++;
        cov_x2gc31hu4().s[129]++;
        session.interactions.forEach(function (interaction) {
          cov_x2gc31hu4().f[34]++;
          cov_x2gc31hu4().s[130]++;
          featureUsage.set(interaction.target, ((cov_x2gc31hu4().b[26][0]++, featureUsage.get(interaction.target)) || (cov_x2gc31hu4().b[26][1]++, 0)) + 1);
        });
      });
      cov_x2gc31hu4().s[131]++;
      featureUsage.forEach(function (count, feature) {
        cov_x2gc31hu4().f[35]++;
        cov_x2gc31hu4().s[132]++;
        if (count >= _this3.ANALYSIS_CONFIG.minPatternFrequency) {
          cov_x2gc31hu4().b[27][0]++;
          cov_x2gc31hu4().s[133]++;
          patterns.push({
            patternId: `feature_${feature}`,
            name: `Feature: ${feature}`,
            description: `User frequently uses ${feature}`,
            frequency: count,
            confidence: Math.min(count / (sessions.length * 10), 1),
            triggers: ['app_start'],
            outcomes: [feature],
            userSegment: 'feature',
            timePattern: {
              preferredTimes: [],
              sessionDuration: 0,
              frequency: 'daily'
            }
          });
        } else {
          cov_x2gc31hu4().b[27][1]++;
        }
      });
      cov_x2gc31hu4().s[134]++;
      return patterns;
    }
  }, {
    key: "analyzePerformancePatterns",
    value: function analyzePerformancePatterns(sessions) {
      cov_x2gc31hu4().f[36]++;
      var patterns = (cov_x2gc31hu4().s[135]++, []);
      var avgLoadTime = (cov_x2gc31hu4().s[136]++, sessions.reduce(function (sum, s) {
        cov_x2gc31hu4().f[37]++;
        cov_x2gc31hu4().s[137]++;
        return sum + s.performance.averageLoadTime;
      }, 0) / sessions.length);
      cov_x2gc31hu4().s[138]++;
      if (avgLoadTime > 2000) {
        cov_x2gc31hu4().b[28][0]++;
        cov_x2gc31hu4().s[139]++;
        patterns.push({
          patternId: 'perf_slow_loading',
          name: 'Slow Loading Performance',
          description: 'User experiences slow loading times',
          frequency: sessions.length,
          confidence: 0.9,
          triggers: ['slow_network', 'low_memory'],
          outcomes: ['performance_optimization_needed'],
          userSegment: 'performance',
          timePattern: {
            preferredTimes: [],
            sessionDuration: 0,
            frequency: 'daily'
          }
        });
      } else {
        cov_x2gc31hu4().b[28][1]++;
      }
      cov_x2gc31hu4().s[140]++;
      return patterns;
    }
  }, {
    key: "updateUserSegment",
    value: function () {
      var _updateUserSegment = _asyncToGenerator(function* (userId, patterns) {
        cov_x2gc31hu4().f[38]++;
        var segment = (cov_x2gc31hu4().s[141]++, this.determineUserSegment(patterns));
        cov_x2gc31hu4().s[142]++;
        this.userSegments.set(userId, segment);
        cov_x2gc31hu4().s[143]++;
        yield this.applySegmentOptimizations(userId, segment);
      });
      function updateUserSegment(_x2, _x3) {
        return _updateUserSegment.apply(this, arguments);
      }
      return updateUserSegment;
    }()
  }, {
    key: "determineUserSegment",
    value: function determineUserSegment(patterns) {
      cov_x2gc31hu4().f[39]++;
      var navigationPatterns = (cov_x2gc31hu4().s[144]++, patterns.filter(function (p) {
        cov_x2gc31hu4().f[40]++;
        cov_x2gc31hu4().s[145]++;
        return p.userSegment === 'navigation';
      }).length);
      var featurePatterns = (cov_x2gc31hu4().s[146]++, patterns.filter(function (p) {
        cov_x2gc31hu4().f[41]++;
        cov_x2gc31hu4().s[147]++;
        return p.userSegment === 'feature';
      }).length);
      cov_x2gc31hu4().s[148]++;
      if (featurePatterns > 5) {
        cov_x2gc31hu4().b[29][0]++;
        cov_x2gc31hu4().s[149]++;
        return {
          segmentId: 'power_user',
          name: 'Power User',
          description: 'Heavy feature usage, needs high performance',
          characteristics: {
            skillLevel: 'advanced',
            activityLevel: 'intensive',
            featureUsage: {},
            sessionPatterns: {
              averageDuration: 1800000,
              preferredTimes: ['evening'],
              frequency: 5
            }
          },
          optimizations: {
            cacheStrategy: 'aggressive',
            performanceProfile: 'performance',
            uiComplexity: 'enhanced',
            preloadTargets: ['training_data', 'analytics']
          }
        };
      } else {
        cov_x2gc31hu4().b[29][1]++;
        cov_x2gc31hu4().s[150]++;
        return {
          segmentId: 'casual_user',
          name: 'Casual User',
          description: 'Light usage, prefers simplicity',
          characteristics: {
            skillLevel: 'beginner',
            activityLevel: 'casual',
            featureUsage: {},
            sessionPatterns: {
              averageDuration: 600000,
              preferredTimes: ['afternoon'],
              frequency: 2
            }
          },
          optimizations: {
            cacheStrategy: 'conservative',
            performanceProfile: 'balanced',
            uiComplexity: 'standard',
            preloadTargets: ['basic_data']
          }
        };
      }
    }
  }, {
    key: "applySegmentOptimizations",
    value: function () {
      var _applySegmentOptimizations = _asyncToGenerator(function* (userId, segment) {
        cov_x2gc31hu4().f[42]++;
        var profileName = (cov_x2gc31hu4().s[151]++, segment.optimizations.performanceProfile);
        cov_x2gc31hu4().s[152]++;
        console.log(`Applied ${profileName} profile for user segment: ${segment.name}`);
      });
      function applySegmentOptimizations(_x4, _x5) {
        return _applySegmentOptimizations.apply(this, arguments);
      }
      return applySegmentOptimizations;
    }()
  }, {
    key: "generateOptimizationRecommendations",
    value: function () {
      var _generateOptimizationRecommendations = _asyncToGenerator(function* (userId, patterns) {
        var _this4 = this,
          _this$analyticsInsigh;
        cov_x2gc31hu4().f[43]++;
        var insights = (cov_x2gc31hu4().s[153]++, []);
        cov_x2gc31hu4().s[154]++;
        patterns.forEach(function (pattern) {
          cov_x2gc31hu4().f[44]++;
          cov_x2gc31hu4().s[155]++;
          if (pattern.confidence > _this4.ANALYSIS_CONFIG.minConfidenceThreshold) {
            cov_x2gc31hu4().b[30][0]++;
            cov_x2gc31hu4().s[156]++;
            insights.push({
              type: 'optimization',
              title: `Optimize for ${pattern.name}`,
              description: pattern.description,
              impact: pattern.frequency > 10 ? (cov_x2gc31hu4().b[31][0]++, 'high') : (cov_x2gc31hu4().b[31][1]++, pattern.frequency > 5 ? (cov_x2gc31hu4().b[32][0]++, 'medium') : (cov_x2gc31hu4().b[32][1]++, 'low')),
              confidence: pattern.confidence,
              recommendation: `Consider optimizing for this usage pattern`,
              implementation: `Implement caching strategy for ${pattern.outcomes.join(', ')}`,
              metrics: {
                userId: userId,
                frequency: pattern.frequency
              }
            });
          } else {
            cov_x2gc31hu4().b[30][1]++;
          }
        });
        cov_x2gc31hu4().s[157]++;
        (_this$analyticsInsigh = this.analyticsInsights).push.apply(_this$analyticsInsigh, insights);
        cov_x2gc31hu4().s[158]++;
        if (this.analyticsInsights.length > 1000) {
          cov_x2gc31hu4().b[33][0]++;
          cov_x2gc31hu4().s[159]++;
          this.analyticsInsights = this.analyticsInsights.slice(-1000);
        } else {
          cov_x2gc31hu4().b[33][1]++;
        }
      });
      function generateOptimizationRecommendations(_x6, _x7) {
        return _generateOptimizationRecommendations.apply(this, arguments);
      }
      return generateOptimizationRecommendations;
    }()
  }, {
    key: "loadAnalyticsData",
    value: function () {
      var _loadAnalyticsData = _asyncToGenerator(function* () {
        cov_x2gc31hu4().f[45]++;
        cov_x2gc31hu4().s[160]++;
        console.log('Loaded analytics data from storage');
      });
      function loadAnalyticsData() {
        return _loadAnalyticsData.apply(this, arguments);
      }
      return loadAnalyticsData;
    }()
  }, {
    key: "startPeriodicAnalysis",
    value: function startPeriodicAnalysis() {
      var _this5 = this;
      cov_x2gc31hu4().f[46]++;
      cov_x2gc31hu4().s[161]++;
      setInterval(function () {
        cov_x2gc31hu4().f[47]++;
        cov_x2gc31hu4().s[162]++;
        _this5.performPeriodicAnalysis();
      }, this.ANALYSIS_CONFIG.analysisInterval);
    }
  }, {
    key: "performPeriodicAnalysis",
    value: function () {
      var _performPeriodicAnalysis = _asyncToGenerator(function* () {
        cov_x2gc31hu4().f[48]++;
        cov_x2gc31hu4().s[163]++;
        console.log('Performing periodic behavior analysis...');
        cov_x2gc31hu4().s[164]++;
        for (var _ref5 of this.userSessions.entries()) {
          var _ref6 = _slicedToArray(_ref5, 2);
          var userId = _ref6[0];
          var sessions = _ref6[1];
          var recentSessions = (cov_x2gc31hu4().s[165]++, sessions.filter(function (s) {
            cov_x2gc31hu4().f[49]++;
            cov_x2gc31hu4().s[166]++;
            return Date.now() - s.startTime < 86400000;
          }));
          cov_x2gc31hu4().s[167]++;
          if (recentSessions.length > 0) {
            cov_x2gc31hu4().b[34][0]++;
            cov_x2gc31hu4().s[168]++;
            yield this.analyzeUserBehavior(userId);
          } else {
            cov_x2gc31hu4().b[34][1]++;
          }
        }
      });
      function performPeriodicAnalysis() {
        return _performPeriodicAnalysis.apply(this, arguments);
      }
      return performPeriodicAnalysis;
    }()
  }, {
    key: "initializeUserSegments",
    value: function initializeUserSegments() {
      cov_x2gc31hu4().f[50]++;
      cov_x2gc31hu4().s[169]++;
      console.log('Initialized user segments');
    }
  }]);
}();
export var behavioralAnalyticsEngine = (cov_x2gc31hu4().s[170]++, new BehavioralAnalyticsEngine());
export default behavioralAnalyticsEngine;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJwcmVkaWN0aXZlQ2FjaGVFbmdpbmUiLCJCZWhhdmlvcmFsQW5hbHl0aWNzRW5naW5lIiwiX2NsYXNzQ2FsbENoZWNrIiwidXNlclNlc3Npb25zIiwiY292X3gyZ2MzMWh1NCIsInMiLCJNYXAiLCJiZWhhdmlvclBhdHRlcm5zIiwidXNlclNlZ21lbnRzIiwiYW5hbHl0aWNzSW5zaWdodHMiLCJBTkFMWVNJU19DT05GSUciLCJzZXNzaW9uVGltZW91dCIsIm1pblBhdHRlcm5GcmVxdWVuY3kiLCJtaW5Db25maWRlbmNlVGhyZXNob2xkIiwibWF4U2Vzc2lvbkhpc3RvcnkiLCJhbmFseXNpc0ludGVydmFsIiwiZiIsImluaXRpYWxpemVCZWhhdmlvcmFsQW5hbHl0aWNzIiwiX2NyZWF0ZUNsYXNzIiwia2V5IiwidmFsdWUiLCJfaW5pdGlhbGl6ZUJlaGF2aW9yYWxBbmFseXRpY3MiLCJfYXN5bmNUb0dlbmVyYXRvciIsImxvYWRBbmFseXRpY3NEYXRhIiwic3RhcnRQZXJpb2RpY0FuYWx5c2lzIiwiaW5pdGlhbGl6ZVVzZXJTZWdtZW50cyIsImNvbnNvbGUiLCJsb2ciLCJlcnJvciIsImFwcGx5IiwiYXJndW1lbnRzIiwic3RhcnRTZXNzaW9uIiwidXNlcklkIiwiY29udGV4dCIsInNlc3Npb25JZCIsImdlbmVyYXRlU2Vzc2lvbklkIiwic2Vzc2lvbiIsInN0YXJ0VGltZSIsIkRhdGUiLCJub3ciLCJzY3JlZW5WaWV3cyIsImludGVyYWN0aW9ucyIsInBlcmZvcm1hbmNlIiwiYXZlcmFnZUxvYWRUaW1lIiwiY3Jhc2hDb3VudCIsImVycm9yQ291bnQiLCJtZW1vcnlVc2FnZSIsImNwdVVzYWdlIiwibmV0d29ya1JlcXVlc3RzIiwiY2FjaGVIaXRSYXRlIiwiaGFzIiwiYiIsInNldCIsImdldCIsInB1c2giLCJ0cmFja1NjcmVlblZpZXciLCJzY3JlZW4iLCJsb2FkVGltZSIsImZpbmRTZXNzaW9uIiwic2NyZWVuVmlldyIsInRpbWVzdGFtcCIsImR1cmF0aW9uIiwiZXhpdFJlYXNvbiIsImludGVyYWN0aW9uQ291bnQiLCJwcmV2aW91c1ZpZXciLCJsZW5ndGgiLCJ0cmFja1VzZXJCZWhhdmlvciIsInR5cGUiLCJ0YXJnZXQiLCJ0cmFja0ludGVyYWN0aW9uIiwiaW50ZXJhY3Rpb24iLCJmdWxsSW50ZXJhY3Rpb24iLCJPYmplY3QiLCJhc3NpZ24iLCJjdXJyZW50U2NyZWVuVmlldyIsIm1ldGFkYXRhIiwiaW50ZXJhY3Rpb25UeXBlIiwic3VjY2VzcyIsImVuZFNlc3Npb24iLCJlbmRUaW1lIiwibGFzdFNjcmVlblZpZXciLCJjYWxjdWxhdGVTZXNzaW9uTWV0cmljcyIsImFuYWx5emVVc2VyQmVoYXZpb3IiLCJfYW5hbHl6ZVVzZXJCZWhhdmlvciIsInBhdHRlcm5zIiwibmF2aWdhdGlvblBhdHRlcm5zIiwiYW5hbHl6ZU5hdmlnYXRpb25QYXR0ZXJucyIsIl90b0NvbnN1bWFibGVBcnJheSIsInRpbWVQYXR0ZXJucyIsImFuYWx5emVUaW1lUGF0dGVybnMiLCJmZWF0dXJlUGF0dGVybnMiLCJhbmFseXplRmVhdHVyZVVzYWdlIiwicGVyZm9ybWFuY2VQYXR0ZXJucyIsImFuYWx5emVQZXJmb3JtYW5jZVBhdHRlcm5zIiwidXBkYXRlVXNlclNlZ21lbnQiLCJnZW5lcmF0ZU9wdGltaXphdGlvblJlY29tbWVuZGF0aW9ucyIsIl94IiwiZ2V0VXNlclNlZ21lbnQiLCJnZXRBbmFseXRpY3NJbnNpZ2h0cyIsImZpbHRlciIsImluc2lnaHQiLCJtZXRyaWNzIiwiZ2V0VXNlckJlaGF2aW9yU3VtbWFyeSIsInNlc3Npb25zIiwic2VnbWVudCIsInNjcmVlbkNvdW50cyIsImZvckVhY2giLCJ2aWV3IiwibW9zdFVzZWRTY3JlZW5zIiwiQXJyYXkiLCJmcm9tIiwiZW50cmllcyIsIm1hcCIsIl9yZWYiLCJfcmVmMiIsIl9zbGljZWRUb0FycmF5IiwiY291bnQiLCJzb3J0IiwiYSIsInNsaWNlIiwiY29tcGxldGVkU2Vzc2lvbnMiLCJhdmVyYWdlU2Vzc2lvbkR1cmF0aW9uIiwicmVkdWNlIiwic3VtIiwidGltZURpc3RyaWJ1dGlvbiIsInRpbWUiLCJ0aW1lT2ZEYXkiLCJwcmVmZXJyZWRUaW1lcyIsIl9yZWYzIiwiX3JlZjQiLCJ0b3RhbFNlc3Npb25zIiwibmFtZSIsImZpbmQiLCJNYXRoIiwicmFuZG9tIiwidG9TdHJpbmciLCJzdWJzdHIiLCJsb2FkVGltZXMiLCJ2IiwiX3RoaXMiLCJzZXF1ZW5jZXMiLCJpIiwiY3VycmVudCIsIm5leHQiLCJzZXF1ZW5jZSIsInBhdHRlcm5JZCIsInJlcGxhY2UiLCJkZXNjcmlwdGlvbiIsImZyZXF1ZW5jeSIsImNvbmZpZGVuY2UiLCJtaW4iLCJ0cmlnZ2VycyIsInNwbGl0Iiwib3V0Y29tZXMiLCJ1c2VyU2VnbWVudCIsInRpbWVQYXR0ZXJuIiwic2Vzc2lvbkR1cmF0aW9uIiwiX3RoaXMyIiwidGltZVVzYWdlIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJfdGhpczMiLCJmZWF0dXJlVXNhZ2UiLCJmZWF0dXJlIiwiYXZnTG9hZFRpbWUiLCJfdXBkYXRlVXNlclNlZ21lbnQiLCJkZXRlcm1pbmVVc2VyU2VnbWVudCIsImFwcGx5U2VnbWVudE9wdGltaXphdGlvbnMiLCJfeDIiLCJfeDMiLCJwIiwic2VnbWVudElkIiwiY2hhcmFjdGVyaXN0aWNzIiwic2tpbGxMZXZlbCIsImFjdGl2aXR5TGV2ZWwiLCJzZXNzaW9uUGF0dGVybnMiLCJhdmVyYWdlRHVyYXRpb24iLCJvcHRpbWl6YXRpb25zIiwiY2FjaGVTdHJhdGVneSIsInBlcmZvcm1hbmNlUHJvZmlsZSIsInVpQ29tcGxleGl0eSIsInByZWxvYWRUYXJnZXRzIiwiX2FwcGx5U2VnbWVudE9wdGltaXphdGlvbnMiLCJwcm9maWxlTmFtZSIsIl94NCIsIl94NSIsIl9nZW5lcmF0ZU9wdGltaXphdGlvblJlY29tbWVuZGF0aW9ucyIsIl90aGlzNCIsIl90aGlzJGFuYWx5dGljc0luc2lnaCIsImluc2lnaHRzIiwicGF0dGVybiIsInRpdGxlIiwiaW1wYWN0IiwicmVjb21tZW5kYXRpb24iLCJpbXBsZW1lbnRhdGlvbiIsImpvaW4iLCJfeDYiLCJfeDciLCJfbG9hZEFuYWx5dGljc0RhdGEiLCJfdGhpczUiLCJzZXRJbnRlcnZhbCIsInBlcmZvcm1QZXJpb2RpY0FuYWx5c2lzIiwiX3BlcmZvcm1QZXJpb2RpY0FuYWx5c2lzIiwiX3JlZjUiLCJfcmVmNiIsInJlY2VudFNlc3Npb25zIiwiYmVoYXZpb3JhbEFuYWx5dGljc0VuZ2luZSJdLCJzb3VyY2VzIjpbIkJlaGF2aW9yYWxBbmFseXRpY3NFbmdpbmUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBCZWhhdmlvcmFsIEFuYWx5dGljcyBFbmdpbmVcbiAqIFxuICogQWR2YW5jZWQgdXNlciBiZWhhdmlvciBhbmFseXNpcyB1c2luZyBtYWNoaW5lIGxlYXJuaW5nIHRvIHVuZGVyc3RhbmRcbiAqIHVzYWdlIHBhdHRlcm5zLCBwcmVkaWN0IG5lZWRzLCBhbmQgb3B0aW1pemUgdXNlciBleHBlcmllbmNlLlxuICovXG5cbmltcG9ydCB7IHByZWRpY3RpdmVDYWNoZUVuZ2luZSB9IGZyb20gJy4vUHJlZGljdGl2ZUNhY2hlRW5naW5lJztcbmltcG9ydCB7IGFkYXB0aXZlUGVyZm9ybWFuY2VNYW5hZ2VyIH0gZnJvbSAnLi9BZGFwdGl2ZVBlcmZvcm1hbmNlTWFuYWdlcic7XG5pbXBvcnQgeyBwZXJmb3JtYW5jZU1vbml0b3IgfSBmcm9tICdAL3V0aWxzL3BlcmZvcm1hbmNlJztcblxuaW50ZXJmYWNlIFVzZXJTZXNzaW9uIHtcbiAgc2Vzc2lvbklkOiBzdHJpbmc7XG4gIHVzZXJJZDogc3RyaW5nO1xuICBzdGFydFRpbWU6IG51bWJlcjtcbiAgZW5kVGltZT86IG51bWJlcjtcbiAgZHVyYXRpb24/OiBudW1iZXI7XG4gIHNjcmVlblZpZXdzOiBTY3JlZW5WaWV3W107XG4gIGludGVyYWN0aW9uczogVXNlckludGVyYWN0aW9uW107XG4gIHBlcmZvcm1hbmNlOiBTZXNzaW9uUGVyZm9ybWFuY2U7XG4gIGNvbnRleHQ6IFNlc3Npb25Db250ZXh0O1xufVxuXG5pbnRlcmZhY2UgU2NyZWVuVmlldyB7XG4gIHNjcmVlbjogc3RyaW5nO1xuICB0aW1lc3RhbXA6IG51bWJlcjtcbiAgZHVyYXRpb246IG51bWJlcjtcbiAgbG9hZFRpbWU6IG51bWJlcjtcbiAgZXhpdFJlYXNvbjogJ25hdmlnYXRpb24nIHwgJ2JhY2snIHwgJ3RpbWVvdXQnIHwgJ2NyYXNoJztcbiAgc2Nyb2xsRGVwdGg/OiBudW1iZXI7XG4gIGludGVyYWN0aW9uQ291bnQ6IG51bWJlcjtcbn1cblxuaW50ZXJmYWNlIFVzZXJJbnRlcmFjdGlvbiB7XG4gIHR5cGU6ICd0YXAnIHwgJ3N3aXBlJyB8ICdzY3JvbGwnIHwgJ3BpbmNoJyB8ICdsb25nX3ByZXNzJyB8ICdzZWFyY2gnIHwgJ2lucHV0JztcbiAgdGFyZ2V0OiBzdHJpbmc7XG4gIHRpbWVzdGFtcDogbnVtYmVyO1xuICBjb29yZGluYXRlcz86IHsgeDogbnVtYmVyOyB5OiBudW1iZXIgfTtcbiAgdmFsdWU/OiBzdHJpbmc7XG4gIGR1cmF0aW9uPzogbnVtYmVyO1xuICBzdWNjZXNzOiBib29sZWFuO1xufVxuXG5pbnRlcmZhY2UgU2Vzc2lvblBlcmZvcm1hbmNlIHtcbiAgYXZlcmFnZUxvYWRUaW1lOiBudW1iZXI7XG4gIGNyYXNoQ291bnQ6IG51bWJlcjtcbiAgZXJyb3JDb3VudDogbnVtYmVyO1xuICBtZW1vcnlVc2FnZTogbnVtYmVyW107XG4gIGNwdVVzYWdlOiBudW1iZXJbXTtcbiAgbmV0d29ya1JlcXVlc3RzOiBudW1iZXI7XG4gIGNhY2hlSGl0UmF0ZTogbnVtYmVyO1xufVxuXG5pbnRlcmZhY2UgU2Vzc2lvbkNvbnRleHQge1xuICB0aW1lT2ZEYXk6ICdtb3JuaW5nJyB8ICdhZnRlcm5vb24nIHwgJ2V2ZW5pbmcnIHwgJ25pZ2h0JztcbiAgZGF5T2ZXZWVrOiBzdHJpbmc7XG4gIGxvY2F0aW9uPzogJ2hvbWUnIHwgJ2d5bScgfCAnY291cnQnIHwgJ3RyYXZlbCc7XG4gIG5ldHdvcmtUeXBlOiBzdHJpbmc7XG4gIGJhdHRlcnlMZXZlbDogbnVtYmVyO1xuICBkZXZpY2VPcmllbnRhdGlvbjogJ3BvcnRyYWl0JyB8ICdsYW5kc2NhcGUnO1xuICBhcHBWZXJzaW9uOiBzdHJpbmc7XG59XG5cbmludGVyZmFjZSBCZWhhdmlvclBhdHRlcm4ge1xuICBwYXR0ZXJuSWQ6IHN0cmluZztcbiAgbmFtZTogc3RyaW5nO1xuICBkZXNjcmlwdGlvbjogc3RyaW5nO1xuICBmcmVxdWVuY3k6IG51bWJlcjtcbiAgY29uZmlkZW5jZTogbnVtYmVyO1xuICB0cmlnZ2Vyczogc3RyaW5nW107XG4gIG91dGNvbWVzOiBzdHJpbmdbXTtcbiAgdXNlclNlZ21lbnQ6IHN0cmluZztcbiAgdGltZVBhdHRlcm46IHtcbiAgICBwcmVmZXJyZWRUaW1lczogc3RyaW5nW107XG4gICAgc2Vzc2lvbkR1cmF0aW9uOiBudW1iZXI7XG4gICAgZnJlcXVlbmN5OiAnZGFpbHknIHwgJ3dlZWtseScgfCAnb2NjYXNpb25hbCc7XG4gIH07XG59XG5cbmludGVyZmFjZSBVc2VyU2VnbWVudCB7XG4gIHNlZ21lbnRJZDogc3RyaW5nO1xuICBuYW1lOiBzdHJpbmc7XG4gIGRlc2NyaXB0aW9uOiBzdHJpbmc7XG4gIGNoYXJhY3RlcmlzdGljczoge1xuICAgIHNraWxsTGV2ZWw6ICdiZWdpbm5lcicgfCAnaW50ZXJtZWRpYXRlJyB8ICdhZHZhbmNlZCcgfCAncHJvZmVzc2lvbmFsJztcbiAgICBhY3Rpdml0eUxldmVsOiAnY2FzdWFsJyB8ICdyZWd1bGFyJyB8ICdpbnRlbnNpdmUnO1xuICAgIGZlYXR1cmVVc2FnZTogUmVjb3JkPHN0cmluZywgbnVtYmVyPjtcbiAgICBzZXNzaW9uUGF0dGVybnM6IHtcbiAgICAgIGF2ZXJhZ2VEdXJhdGlvbjogbnVtYmVyO1xuICAgICAgcHJlZmVycmVkVGltZXM6IHN0cmluZ1tdO1xuICAgICAgZnJlcXVlbmN5OiBudW1iZXI7XG4gICAgfTtcbiAgfTtcbiAgb3B0aW1pemF0aW9uczoge1xuICAgIGNhY2hlU3RyYXRlZ3k6IHN0cmluZztcbiAgICBwZXJmb3JtYW5jZVByb2ZpbGU6IHN0cmluZztcbiAgICB1aUNvbXBsZXhpdHk6IHN0cmluZztcbiAgICBwcmVsb2FkVGFyZ2V0czogc3RyaW5nW107XG4gIH07XG59XG5cbmludGVyZmFjZSBBbmFseXRpY3NJbnNpZ2h0IHtcbiAgdHlwZTogJ3BlcmZvcm1hbmNlJyB8ICd1c2FnZScgfCAnZW5nYWdlbWVudCcgfCAnb3B0aW1pemF0aW9uJztcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgaW1wYWN0OiAnaGlnaCcgfCAnbWVkaXVtJyB8ICdsb3cnO1xuICBjb25maWRlbmNlOiBudW1iZXI7XG4gIHJlY29tbWVuZGF0aW9uOiBzdHJpbmc7XG4gIGltcGxlbWVudGF0aW9uOiBzdHJpbmc7XG4gIG1ldHJpY3M6IFJlY29yZDxzdHJpbmcsIG51bWJlcj47XG59XG5cbi8qKlxuICogQWR2YW5jZWQgQmVoYXZpb3JhbCBBbmFseXRpY3MgRW5naW5lXG4gKi9cbmNsYXNzIEJlaGF2aW9yYWxBbmFseXRpY3NFbmdpbmUge1xuICBwcml2YXRlIHVzZXJTZXNzaW9uczogTWFwPHN0cmluZywgVXNlclNlc3Npb25bXT4gPSBuZXcgTWFwKCk7XG4gIHByaXZhdGUgYmVoYXZpb3JQYXR0ZXJuczogTWFwPHN0cmluZywgQmVoYXZpb3JQYXR0ZXJuW10+ID0gbmV3IE1hcCgpO1xuICBwcml2YXRlIHVzZXJTZWdtZW50czogTWFwPHN0cmluZywgVXNlclNlZ21lbnQ+ID0gbmV3IE1hcCgpO1xuICBwcml2YXRlIGFuYWx5dGljc0luc2lnaHRzOiBBbmFseXRpY3NJbnNpZ2h0W10gPSBbXTtcbiAgXG4gIHByaXZhdGUgcmVhZG9ubHkgQU5BTFlTSVNfQ09ORklHID0ge1xuICAgIHNlc3Npb25UaW1lb3V0OiAxODAwMDAwLCAvLyAzMCBtaW51dGVzXG4gICAgbWluUGF0dGVybkZyZXF1ZW5jeTogMyxcbiAgICBtaW5Db25maWRlbmNlVGhyZXNob2xkOiAwLjcsXG4gICAgbWF4U2Vzc2lvbkhpc3Rvcnk6IDEwMCxcbiAgICBhbmFseXNpc0ludGVydmFsOiAzNjAwMDAwLCAvLyAxIGhvdXJcbiAgfTtcblxuICBjb25zdHJ1Y3RvcigpIHtcbiAgICB0aGlzLmluaXRpYWxpemVCZWhhdmlvcmFsQW5hbHl0aWNzKCk7XG4gIH1cblxuICAvKipcbiAgICogSW5pdGlhbGl6ZSBiZWhhdmlvcmFsIGFuYWx5dGljcyBlbmdpbmVcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgaW5pdGlhbGl6ZUJlaGF2aW9yYWxBbmFseXRpY3MoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgdHJ5IHtcbiAgICAgIC8vIExvYWQgZXhpc3RpbmcgYW5hbHl0aWNzIGRhdGFcbiAgICAgIGF3YWl0IHRoaXMubG9hZEFuYWx5dGljc0RhdGEoKTtcbiAgICAgIFxuICAgICAgLy8gU3RhcnQgcGVyaW9kaWMgYW5hbHlzaXNcbiAgICAgIHRoaXMuc3RhcnRQZXJpb2RpY0FuYWx5c2lzKCk7XG4gICAgICBcbiAgICAgIC8vIEluaXRpYWxpemUgdXNlciBzZWdtZW50c1xuICAgICAgdGhpcy5pbml0aWFsaXplVXNlclNlZ21lbnRzKCk7XG4gICAgICBcbiAgICAgIGNvbnNvbGUubG9nKCdCZWhhdmlvcmFsIEFuYWx5dGljcyBFbmdpbmUgaW5pdGlhbGl6ZWQgc3VjY2Vzc2Z1bGx5Jyk7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0ZhaWxlZCB0byBpbml0aWFsaXplIEJlaGF2aW9yYWwgQW5hbHl0aWNzIEVuZ2luZTonLCBlcnJvcik7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIFN0YXJ0IHRyYWNraW5nIGEgbmV3IHVzZXIgc2Vzc2lvblxuICAgKi9cbiAgc3RhcnRTZXNzaW9uKHVzZXJJZDogc3RyaW5nLCBjb250ZXh0OiBTZXNzaW9uQ29udGV4dCk6IHN0cmluZyB7XG4gICAgY29uc3Qgc2Vzc2lvbklkID0gdGhpcy5nZW5lcmF0ZVNlc3Npb25JZCh1c2VySWQpO1xuICAgIFxuICAgIGNvbnN0IHNlc3Npb246IFVzZXJTZXNzaW9uID0ge1xuICAgICAgc2Vzc2lvbklkLFxuICAgICAgdXNlcklkLFxuICAgICAgc3RhcnRUaW1lOiBEYXRlLm5vdygpLFxuICAgICAgc2NyZWVuVmlld3M6IFtdLFxuICAgICAgaW50ZXJhY3Rpb25zOiBbXSxcbiAgICAgIHBlcmZvcm1hbmNlOiB7XG4gICAgICAgIGF2ZXJhZ2VMb2FkVGltZTogMCxcbiAgICAgICAgY3Jhc2hDb3VudDogMCxcbiAgICAgICAgZXJyb3JDb3VudDogMCxcbiAgICAgICAgbWVtb3J5VXNhZ2U6IFtdLFxuICAgICAgICBjcHVVc2FnZTogW10sXG4gICAgICAgIG5ldHdvcmtSZXF1ZXN0czogMCxcbiAgICAgICAgY2FjaGVIaXRSYXRlOiAwLFxuICAgICAgfSxcbiAgICAgIGNvbnRleHQsXG4gICAgfTtcblxuICAgIGlmICghdGhpcy51c2VyU2Vzc2lvbnMuaGFzKHVzZXJJZCkpIHtcbiAgICAgIHRoaXMudXNlclNlc3Npb25zLnNldCh1c2VySWQsIFtdKTtcbiAgICB9XG4gICAgXG4gICAgdGhpcy51c2VyU2Vzc2lvbnMuZ2V0KHVzZXJJZCkhLnB1c2goc2Vzc2lvbik7XG4gICAgXG4gICAgY29uc29sZS5sb2coYFN0YXJ0ZWQgc2Vzc2lvbiAke3Nlc3Npb25JZH0gZm9yIHVzZXIgJHt1c2VySWR9YCk7XG4gICAgcmV0dXJuIHNlc3Npb25JZDtcbiAgfVxuXG4gIC8qKlxuICAgKiBUcmFjayBzY3JlZW4gdmlld1xuICAgKi9cbiAgdHJhY2tTY3JlZW5WaWV3KFxuICAgIHVzZXJJZDogc3RyaW5nLFxuICAgIHNlc3Npb25JZDogc3RyaW5nLFxuICAgIHNjcmVlbjogc3RyaW5nLFxuICAgIGxvYWRUaW1lOiBudW1iZXJcbiAgKTogdm9pZCB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuZmluZFNlc3Npb24odXNlcklkLCBzZXNzaW9uSWQpO1xuICAgIGlmICghc2Vzc2lvbikgcmV0dXJuO1xuXG4gICAgY29uc3Qgc2NyZWVuVmlldzogU2NyZWVuVmlldyA9IHtcbiAgICAgIHNjcmVlbixcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgIGR1cmF0aW9uOiAwLCAvLyBXaWxsIGJlIHVwZGF0ZWQgd2hlbiBzY3JlZW4gY2hhbmdlc1xuICAgICAgbG9hZFRpbWUsXG4gICAgICBleGl0UmVhc29uOiAnbmF2aWdhdGlvbicsXG4gICAgICBpbnRlcmFjdGlvbkNvdW50OiAwLFxuICAgIH07XG5cbiAgICAvLyBVcGRhdGUgcHJldmlvdXMgc2NyZWVuIHZpZXcgZHVyYXRpb25cbiAgICBjb25zdCBwcmV2aW91c1ZpZXcgPSBzZXNzaW9uLnNjcmVlblZpZXdzW3Nlc3Npb24uc2NyZWVuVmlld3MubGVuZ3RoIC0gMV07XG4gICAgaWYgKHByZXZpb3VzVmlldykge1xuICAgICAgcHJldmlvdXNWaWV3LmR1cmF0aW9uID0gc2NyZWVuVmlldy50aW1lc3RhbXAgLSBwcmV2aW91c1ZpZXcudGltZXN0YW1wO1xuICAgIH1cblxuICAgIHNlc3Npb24uc2NyZWVuVmlld3MucHVzaChzY3JlZW5WaWV3KTtcbiAgICBcbiAgICAvLyBUcmFjayBiZWhhdmlvciBmb3IgcHJlZGljdGl2ZSBjYWNoaW5nXG4gICAgcHJlZGljdGl2ZUNhY2hlRW5naW5lLnRyYWNrVXNlckJlaGF2aW9yKFxuICAgICAgdXNlcklkLFxuICAgICAge1xuICAgICAgICB0eXBlOiAnc2NyZWVuX3ZpZXcnLFxuICAgICAgICB0YXJnZXQ6IHNjcmVlbixcbiAgICAgICAgdGltZXN0YW1wOiBEYXRlLm5vdygpLFxuICAgICAgICBkdXJhdGlvbjogbG9hZFRpbWUsXG4gICAgICB9LFxuICAgICAgc2Vzc2lvbi5jb250ZXh0IGFzIGFueSxcbiAgICAgIHNlc3Npb24uY29udGV4dCBhcyBhbnlcbiAgICApO1xuICB9XG5cbiAgLyoqXG4gICAqIFRyYWNrIHVzZXIgaW50ZXJhY3Rpb25cbiAgICovXG4gIHRyYWNrSW50ZXJhY3Rpb24oXG4gICAgdXNlcklkOiBzdHJpbmcsXG4gICAgc2Vzc2lvbklkOiBzdHJpbmcsXG4gICAgaW50ZXJhY3Rpb246IE9taXQ8VXNlckludGVyYWN0aW9uLCAndGltZXN0YW1wJz5cbiAgKTogdm9pZCB7XG4gICAgY29uc3Qgc2Vzc2lvbiA9IHRoaXMuZmluZFNlc3Npb24odXNlcklkLCBzZXNzaW9uSWQpO1xuICAgIGlmICghc2Vzc2lvbikgcmV0dXJuO1xuXG4gICAgY29uc3QgZnVsbEludGVyYWN0aW9uOiBVc2VySW50ZXJhY3Rpb24gPSB7XG4gICAgICAuLi5pbnRlcmFjdGlvbixcbiAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICB9O1xuXG4gICAgc2Vzc2lvbi5pbnRlcmFjdGlvbnMucHVzaChmdWxsSW50ZXJhY3Rpb24pO1xuICAgIFxuICAgIC8vIFVwZGF0ZSBjdXJyZW50IHNjcmVlbiB2aWV3IGludGVyYWN0aW9uIGNvdW50XG4gICAgY29uc3QgY3VycmVudFNjcmVlblZpZXcgPSBzZXNzaW9uLnNjcmVlblZpZXdzW3Nlc3Npb24uc2NyZWVuVmlld3MubGVuZ3RoIC0gMV07XG4gICAgaWYgKGN1cnJlbnRTY3JlZW5WaWV3KSB7XG4gICAgICBjdXJyZW50U2NyZWVuVmlldy5pbnRlcmFjdGlvbkNvdW50Kys7XG4gICAgfVxuXG4gICAgLy8gVHJhY2sgYmVoYXZpb3IgZm9yIHByZWRpY3RpdmUgY2FjaGluZ1xuICAgIHByZWRpY3RpdmVDYWNoZUVuZ2luZS50cmFja1VzZXJCZWhhdmlvcihcbiAgICAgIHVzZXJJZCxcbiAgICAgIHtcbiAgICAgICAgdHlwZTogJ2ludGVyYWN0aW9uJyxcbiAgICAgICAgdGFyZ2V0OiBpbnRlcmFjdGlvbi50YXJnZXQsXG4gICAgICAgIHRpbWVzdGFtcDogRGF0ZS5ub3coKSxcbiAgICAgICAgbWV0YWRhdGE6IHtcbiAgICAgICAgICBpbnRlcmFjdGlvblR5cGU6IGludGVyYWN0aW9uLnR5cGUsXG4gICAgICAgICAgc3VjY2VzczogaW50ZXJhY3Rpb24uc3VjY2VzcyxcbiAgICAgICAgfSxcbiAgICAgIH0sXG4gICAgICBzZXNzaW9uLmNvbnRleHQgYXMgYW55LFxuICAgICAgc2Vzc2lvbi5jb250ZXh0IGFzIGFueVxuICAgICk7XG4gIH1cblxuICAvKipcbiAgICogRW5kIHVzZXIgc2Vzc2lvblxuICAgKi9cbiAgZW5kU2Vzc2lvbih1c2VySWQ6IHN0cmluZywgc2Vzc2lvbklkOiBzdHJpbmcpOiB2b2lkIHtcbiAgICBjb25zdCBzZXNzaW9uID0gdGhpcy5maW5kU2Vzc2lvbih1c2VySWQsIHNlc3Npb25JZCk7XG4gICAgaWYgKCFzZXNzaW9uKSByZXR1cm47XG5cbiAgICBzZXNzaW9uLmVuZFRpbWUgPSBEYXRlLm5vdygpO1xuICAgIHNlc3Npb24uZHVyYXRpb24gPSBzZXNzaW9uLmVuZFRpbWUgLSBzZXNzaW9uLnN0YXJ0VGltZTtcblxuICAgIC8vIFVwZGF0ZSBsYXN0IHNjcmVlbiB2aWV3IGR1cmF0aW9uXG4gICAgY29uc3QgbGFzdFNjcmVlblZpZXcgPSBzZXNzaW9uLnNjcmVlblZpZXdzW3Nlc3Npb24uc2NyZWVuVmlld3MubGVuZ3RoIC0gMV07XG4gICAgaWYgKGxhc3RTY3JlZW5WaWV3ICYmICFsYXN0U2NyZWVuVmlldy5kdXJhdGlvbikge1xuICAgICAgbGFzdFNjcmVlblZpZXcuZHVyYXRpb24gPSBzZXNzaW9uLmVuZFRpbWUgLSBsYXN0U2NyZWVuVmlldy50aW1lc3RhbXA7XG4gICAgfVxuXG4gICAgLy8gQ2FsY3VsYXRlIHNlc3Npb24gcGVyZm9ybWFuY2UgbWV0cmljc1xuICAgIHRoaXMuY2FsY3VsYXRlU2Vzc2lvbk1ldHJpY3Moc2Vzc2lvbik7XG4gICAgXG4gICAgLy8gVHJpZ2dlciBiZWhhdmlvciBhbmFseXNpc1xuICAgIHRoaXMuYW5hbHl6ZVVzZXJCZWhhdmlvcih1c2VySWQpO1xuICAgIFxuICAgIGNvbnNvbGUubG9nKGBFbmRlZCBzZXNzaW9uICR7c2Vzc2lvbklkfSBmb3IgdXNlciAke3VzZXJJZH0gKGR1cmF0aW9uOiAke3Nlc3Npb24uZHVyYXRpb259bXMpYCk7XG4gIH1cblxuICAvKipcbiAgICogQW5hbHl6ZSB1c2VyIGJlaGF2aW9yIHBhdHRlcm5zXG4gICAqL1xuICBhc3luYyBhbmFseXplVXNlckJlaGF2aW9yKHVzZXJJZDogc3RyaW5nKTogUHJvbWlzZTxCZWhhdmlvclBhdHRlcm5bXT4ge1xuICAgIGNvbnN0IHVzZXJTZXNzaW9ucyA9IHRoaXMudXNlclNlc3Npb25zLmdldCh1c2VySWQpIHx8IFtdO1xuICAgIGlmICh1c2VyU2Vzc2lvbnMubGVuZ3RoIDwgMykge1xuICAgICAgcmV0dXJuIFtdOyAvLyBOZWVkIG1pbmltdW0gc2Vzc2lvbnMgZm9yIGFuYWx5c2lzXG4gICAgfVxuXG4gICAgY29uc3QgcGF0dGVybnM6IEJlaGF2aW9yUGF0dGVybltdID0gW107XG5cbiAgICB0cnkge1xuICAgICAgLy8gQW5hbHl6ZSBuYXZpZ2F0aW9uIHBhdHRlcm5zXG4gICAgICBjb25zdCBuYXZpZ2F0aW9uUGF0dGVybnMgPSB0aGlzLmFuYWx5emVOYXZpZ2F0aW9uUGF0dGVybnModXNlclNlc3Npb25zKTtcbiAgICAgIHBhdHRlcm5zLnB1c2goLi4ubmF2aWdhdGlvblBhdHRlcm5zKTtcblxuICAgICAgLy8gQW5hbHl6ZSB0aW1lLWJhc2VkIHBhdHRlcm5zXG4gICAgICBjb25zdCB0aW1lUGF0dGVybnMgPSB0aGlzLmFuYWx5emVUaW1lUGF0dGVybnModXNlclNlc3Npb25zKTtcbiAgICAgIHBhdHRlcm5zLnB1c2goLi4udGltZVBhdHRlcm5zKTtcblxuICAgICAgLy8gQW5hbHl6ZSBmZWF0dXJlIHVzYWdlIHBhdHRlcm5zXG4gICAgICBjb25zdCBmZWF0dXJlUGF0dGVybnMgPSB0aGlzLmFuYWx5emVGZWF0dXJlVXNhZ2UodXNlclNlc3Npb25zKTtcbiAgICAgIHBhdHRlcm5zLnB1c2goLi4uZmVhdHVyZVBhdHRlcm5zKTtcblxuICAgICAgLy8gQW5hbHl6ZSBwZXJmb3JtYW5jZSBwYXR0ZXJuc1xuICAgICAgY29uc3QgcGVyZm9ybWFuY2VQYXR0ZXJucyA9IHRoaXMuYW5hbHl6ZVBlcmZvcm1hbmNlUGF0dGVybnModXNlclNlc3Npb25zKTtcbiAgICAgIHBhdHRlcm5zLnB1c2goLi4ucGVyZm9ybWFuY2VQYXR0ZXJucyk7XG5cbiAgICAgIC8vIFN0b3JlIHBhdHRlcm5zXG4gICAgICB0aGlzLmJlaGF2aW9yUGF0dGVybnMuc2V0KHVzZXJJZCwgcGF0dGVybnMpO1xuXG4gICAgICAvLyBVcGRhdGUgdXNlciBzZWdtZW50XG4gICAgICBhd2FpdCB0aGlzLnVwZGF0ZVVzZXJTZWdtZW50KHVzZXJJZCwgcGF0dGVybnMpO1xuXG4gICAgICAvLyBHZW5lcmF0ZSBvcHRpbWl6YXRpb24gcmVjb21tZW5kYXRpb25zXG4gICAgICBhd2FpdCB0aGlzLmdlbmVyYXRlT3B0aW1pemF0aW9uUmVjb21tZW5kYXRpb25zKHVzZXJJZCwgcGF0dGVybnMpO1xuXG4gICAgICByZXR1cm4gcGF0dGVybnM7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIGFuYWx5emUgdXNlciBiZWhhdmlvcjonLCBlcnJvcik7XG4gICAgICByZXR1cm4gW107XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCB1c2VyIHNlZ21lbnRcbiAgICovXG4gIGdldFVzZXJTZWdtZW50KHVzZXJJZDogc3RyaW5nKTogVXNlclNlZ21lbnQgfCBudWxsIHtcbiAgICByZXR1cm4gdGhpcy51c2VyU2VnbWVudHMuZ2V0KHVzZXJJZCkgfHwgbnVsbDtcbiAgfVxuXG4gIC8qKlxuICAgKiBHZXQgYW5hbHl0aWNzIGluc2lnaHRzXG4gICAqL1xuICBnZXRBbmFseXRpY3NJbnNpZ2h0cyh1c2VySWQ/OiBzdHJpbmcpOiBBbmFseXRpY3NJbnNpZ2h0W10ge1xuICAgIGlmICh1c2VySWQpIHtcbiAgICAgIHJldHVybiB0aGlzLmFuYWx5dGljc0luc2lnaHRzLmZpbHRlcihpbnNpZ2h0ID0+IFxuICAgICAgICBpbnNpZ2h0Lm1ldHJpY3MudXNlcklkID09PSB1c2VySWRcbiAgICAgICk7XG4gICAgfVxuICAgIHJldHVybiB0aGlzLmFuYWx5dGljc0luc2lnaHRzO1xuICB9XG5cbiAgLyoqXG4gICAqIEdldCB1c2VyIGJlaGF2aW9yIHN1bW1hcnlcbiAgICovXG4gIGdldFVzZXJCZWhhdmlvclN1bW1hcnkodXNlcklkOiBzdHJpbmcpOiB7XG4gICAgdG90YWxTZXNzaW9uczogbnVtYmVyO1xuICAgIGF2ZXJhZ2VTZXNzaW9uRHVyYXRpb246IG51bWJlcjtcbiAgICBtb3N0VXNlZFNjcmVlbnM6IEFycmF5PHsgc2NyZWVuOiBzdHJpbmc7IGNvdW50OiBudW1iZXIgfT47XG4gICAgcHJlZmVycmVkVGltZXM6IHN0cmluZ1tdO1xuICAgIGJlaGF2aW9yUGF0dGVybnM6IG51bWJlcjtcbiAgICBzZWdtZW50OiBzdHJpbmc7XG4gIH0ge1xuICAgIGNvbnN0IHNlc3Npb25zID0gdGhpcy51c2VyU2Vzc2lvbnMuZ2V0KHVzZXJJZCkgfHwgW107XG4gICAgY29uc3QgcGF0dGVybnMgPSB0aGlzLmJlaGF2aW9yUGF0dGVybnMuZ2V0KHVzZXJJZCkgfHwgW107XG4gICAgY29uc3Qgc2VnbWVudCA9IHRoaXMudXNlclNlZ21lbnRzLmdldCh1c2VySWQpO1xuXG4gICAgLy8gQ2FsY3VsYXRlIG1vc3QgdXNlZCBzY3JlZW5zXG4gICAgY29uc3Qgc2NyZWVuQ291bnRzID0gbmV3IE1hcDxzdHJpbmcsIG51bWJlcj4oKTtcbiAgICBzZXNzaW9ucy5mb3JFYWNoKHNlc3Npb24gPT4ge1xuICAgICAgc2Vzc2lvbi5zY3JlZW5WaWV3cy5mb3JFYWNoKHZpZXcgPT4ge1xuICAgICAgICBzY3JlZW5Db3VudHMuc2V0KHZpZXcuc2NyZWVuLCAoc2NyZWVuQ291bnRzLmdldCh2aWV3LnNjcmVlbikgfHwgMCkgKyAxKTtcbiAgICAgIH0pO1xuICAgIH0pO1xuXG4gICAgY29uc3QgbW9zdFVzZWRTY3JlZW5zID0gQXJyYXkuZnJvbShzY3JlZW5Db3VudHMuZW50cmllcygpKVxuICAgICAgLm1hcCgoW3NjcmVlbiwgY291bnRdKSA9PiAoeyBzY3JlZW4sIGNvdW50IH0pKVxuICAgICAgLnNvcnQoKGEsIGIpID0+IGIuY291bnQgLSBhLmNvdW50KVxuICAgICAgLnNsaWNlKDAsIDUpO1xuXG4gICAgLy8gQ2FsY3VsYXRlIGF2ZXJhZ2Ugc2Vzc2lvbiBkdXJhdGlvblxuICAgIGNvbnN0IGNvbXBsZXRlZFNlc3Npb25zID0gc2Vzc2lvbnMuZmlsdGVyKHMgPT4gcy5kdXJhdGlvbik7XG4gICAgY29uc3QgYXZlcmFnZVNlc3Npb25EdXJhdGlvbiA9IGNvbXBsZXRlZFNlc3Npb25zLmxlbmd0aCA+IDBcbiAgICAgID8gY29tcGxldGVkU2Vzc2lvbnMucmVkdWNlKChzdW0sIHMpID0+IHN1bSArIChzLmR1cmF0aW9uIHx8IDApLCAwKSAvIGNvbXBsZXRlZFNlc3Npb25zLmxlbmd0aFxuICAgICAgOiAwO1xuXG4gICAgLy8gQW5hbHl6ZSBwcmVmZXJyZWQgdGltZXNcbiAgICBjb25zdCB0aW1lRGlzdHJpYnV0aW9uID0gbmV3IE1hcDxzdHJpbmcsIG51bWJlcj4oKTtcbiAgICBzZXNzaW9ucy5mb3JFYWNoKHNlc3Npb24gPT4ge1xuICAgICAgY29uc3QgdGltZSA9IHNlc3Npb24uY29udGV4dC50aW1lT2ZEYXk7XG4gICAgICB0aW1lRGlzdHJpYnV0aW9uLnNldCh0aW1lLCAodGltZURpc3RyaWJ1dGlvbi5nZXQodGltZSkgfHwgMCkgKyAxKTtcbiAgICB9KTtcblxuICAgIGNvbnN0IHByZWZlcnJlZFRpbWVzID0gQXJyYXkuZnJvbSh0aW1lRGlzdHJpYnV0aW9uLmVudHJpZXMoKSlcbiAgICAgIC5zb3J0KChhLCBiKSA9PiBiWzFdIC0gYVsxXSlcbiAgICAgIC5zbGljZSgwLCAyKVxuICAgICAgLm1hcCgoW3RpbWVdKSA9PiB0aW1lKTtcblxuICAgIHJldHVybiB7XG4gICAgICB0b3RhbFNlc3Npb25zOiBzZXNzaW9ucy5sZW5ndGgsXG4gICAgICBhdmVyYWdlU2Vzc2lvbkR1cmF0aW9uLFxuICAgICAgbW9zdFVzZWRTY3JlZW5zLFxuICAgICAgcHJlZmVycmVkVGltZXMsXG4gICAgICBiZWhhdmlvclBhdHRlcm5zOiBwYXR0ZXJucy5sZW5ndGgsXG4gICAgICBzZWdtZW50OiBzZWdtZW50Py5uYW1lIHx8ICdVbmtub3duJyxcbiAgICB9O1xuICB9XG5cbiAgLy8gUHJpdmF0ZSBoZWxwZXIgbWV0aG9kc1xuXG4gIHByaXZhdGUgZmluZFNlc3Npb24odXNlcklkOiBzdHJpbmcsIHNlc3Npb25JZDogc3RyaW5nKTogVXNlclNlc3Npb24gfCBudWxsIHtcbiAgICBjb25zdCBzZXNzaW9ucyA9IHRoaXMudXNlclNlc3Npb25zLmdldCh1c2VySWQpIHx8IFtdO1xuICAgIHJldHVybiBzZXNzaW9ucy5maW5kKHMgPT4gcy5zZXNzaW9uSWQgPT09IHNlc3Npb25JZCkgfHwgbnVsbDtcbiAgfVxuXG4gIHByaXZhdGUgZ2VuZXJhdGVTZXNzaW9uSWQodXNlcklkOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIHJldHVybiBgc2Vzc2lvbl8ke3VzZXJJZH1fJHtEYXRlLm5vdygpfV8ke01hdGgucmFuZG9tKCkudG9TdHJpbmcoMzYpLnN1YnN0cigyLCA5KX1gO1xuICB9XG5cbiAgcHJpdmF0ZSBjYWxjdWxhdGVTZXNzaW9uTWV0cmljcyhzZXNzaW9uOiBVc2VyU2Vzc2lvbik6IHZvaWQge1xuICAgIC8vIENhbGN1bGF0ZSBhdmVyYWdlIGxvYWQgdGltZVxuICAgIGNvbnN0IGxvYWRUaW1lcyA9IHNlc3Npb24uc2NyZWVuVmlld3MubWFwKHYgPT4gdi5sb2FkVGltZSk7XG4gICAgc2Vzc2lvbi5wZXJmb3JtYW5jZS5hdmVyYWdlTG9hZFRpbWUgPSBsb2FkVGltZXMubGVuZ3RoID4gMFxuICAgICAgPyBsb2FkVGltZXMucmVkdWNlKChzdW0sIHRpbWUpID0+IHN1bSArIHRpbWUsIDApIC8gbG9hZFRpbWVzLmxlbmd0aFxuICAgICAgOiAwO1xuXG4gICAgLy8gQ291bnQgbmV0d29yayByZXF1ZXN0cyAoZXN0aW1hdGVkKVxuICAgIHNlc3Npb24ucGVyZm9ybWFuY2UubmV0d29ya1JlcXVlc3RzID0gc2Vzc2lvbi5zY3JlZW5WaWV3cy5sZW5ndGggKiAyOyAvLyBFc3RpbWF0ZVxuXG4gICAgLy8gRXN0aW1hdGUgY2FjaGUgaGl0IHJhdGVcbiAgICBzZXNzaW9uLnBlcmZvcm1hbmNlLmNhY2hlSGl0UmF0ZSA9IE1hdGgucmFuZG9tKCkgKiAwLjMgKyAwLjc7IC8vIDcwLTEwMCVcbiAgfVxuXG4gIHByaXZhdGUgYW5hbHl6ZU5hdmlnYXRpb25QYXR0ZXJucyhzZXNzaW9uczogVXNlclNlc3Npb25bXSk6IEJlaGF2aW9yUGF0dGVybltdIHtcbiAgICBjb25zdCBwYXR0ZXJuczogQmVoYXZpb3JQYXR0ZXJuW10gPSBbXTtcbiAgICBcbiAgICAvLyBBbmFseXplIGNvbW1vbiBuYXZpZ2F0aW9uIHNlcXVlbmNlc1xuICAgIGNvbnN0IHNlcXVlbmNlcyA9IG5ldyBNYXA8c3RyaW5nLCBudW1iZXI+KCk7XG4gICAgXG4gICAgc2Vzc2lvbnMuZm9yRWFjaChzZXNzaW9uID0+IHtcbiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgc2Vzc2lvbi5zY3JlZW5WaWV3cy5sZW5ndGggLSAxOyBpKyspIHtcbiAgICAgICAgY29uc3QgY3VycmVudCA9IHNlc3Npb24uc2NyZWVuVmlld3NbaV0uc2NyZWVuO1xuICAgICAgICBjb25zdCBuZXh0ID0gc2Vzc2lvbi5zY3JlZW5WaWV3c1tpICsgMV0uc2NyZWVuO1xuICAgICAgICBjb25zdCBzZXF1ZW5jZSA9IGAke2N1cnJlbnR9LT4ke25leHR9YDtcbiAgICAgICAgc2VxdWVuY2VzLnNldChzZXF1ZW5jZSwgKHNlcXVlbmNlcy5nZXQoc2VxdWVuY2UpIHx8IDApICsgMSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICAvLyBDcmVhdGUgcGF0dGVybnMgZm9yIGZyZXF1ZW50IHNlcXVlbmNlc1xuICAgIHNlcXVlbmNlcy5mb3JFYWNoKChjb3VudCwgc2VxdWVuY2UpID0+IHtcbiAgICAgIGlmIChjb3VudCA+PSB0aGlzLkFOQUxZU0lTX0NPTkZJRy5taW5QYXR0ZXJuRnJlcXVlbmN5KSB7XG4gICAgICAgIHBhdHRlcm5zLnB1c2goe1xuICAgICAgICAgIHBhdHRlcm5JZDogYG5hdl8ke3NlcXVlbmNlLnJlcGxhY2UoJy0+JywgJ190b18nKX1gLFxuICAgICAgICAgIG5hbWU6IGBOYXZpZ2F0aW9uOiAke3NlcXVlbmNlfWAsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGBVc2VyIGZyZXF1ZW50bHkgbmF2aWdhdGVzICR7c2VxdWVuY2V9YCxcbiAgICAgICAgICBmcmVxdWVuY3k6IGNvdW50LFxuICAgICAgICAgIGNvbmZpZGVuY2U6IE1hdGgubWluKGNvdW50IC8gc2Vzc2lvbnMubGVuZ3RoLCAxKSxcbiAgICAgICAgICB0cmlnZ2VyczogW3NlcXVlbmNlLnNwbGl0KCctPicpWzBdXSxcbiAgICAgICAgICBvdXRjb21lczogW3NlcXVlbmNlLnNwbGl0KCctPicpWzFdXSxcbiAgICAgICAgICB1c2VyU2VnbWVudDogJ25hdmlnYXRpb24nLFxuICAgICAgICAgIHRpbWVQYXR0ZXJuOiB7XG4gICAgICAgICAgICBwcmVmZXJyZWRUaW1lczogW10sXG4gICAgICAgICAgICBzZXNzaW9uRHVyYXRpb246IDAsXG4gICAgICAgICAgICBmcmVxdWVuY3k6ICdkYWlseScsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gcGF0dGVybnM7XG4gIH1cblxuICBwcml2YXRlIGFuYWx5emVUaW1lUGF0dGVybnMoc2Vzc2lvbnM6IFVzZXJTZXNzaW9uW10pOiBCZWhhdmlvclBhdHRlcm5bXSB7XG4gICAgY29uc3QgcGF0dGVybnM6IEJlaGF2aW9yUGF0dGVybltdID0gW107XG4gICAgXG4gICAgLy8gQW5hbHl6ZSB0aW1lLW9mLWRheSB1c2FnZVxuICAgIGNvbnN0IHRpbWVVc2FnZSA9IG5ldyBNYXA8c3RyaW5nLCBudW1iZXI+KCk7XG4gICAgc2Vzc2lvbnMuZm9yRWFjaChzZXNzaW9uID0+IHtcbiAgICAgIGNvbnN0IHRpbWUgPSBzZXNzaW9uLmNvbnRleHQudGltZU9mRGF5O1xuICAgICAgdGltZVVzYWdlLnNldCh0aW1lLCAodGltZVVzYWdlLmdldCh0aW1lKSB8fCAwKSArIDEpO1xuICAgIH0pO1xuXG4gICAgLy8gQ3JlYXRlIHRpbWUtYmFzZWQgcGF0dGVybnNcbiAgICB0aW1lVXNhZ2UuZm9yRWFjaCgoY291bnQsIHRpbWUpID0+IHtcbiAgICAgIGlmIChjb3VudCA+PSB0aGlzLkFOQUxZU0lTX0NPTkZJRy5taW5QYXR0ZXJuRnJlcXVlbmN5KSB7XG4gICAgICAgIHBhdHRlcm5zLnB1c2goe1xuICAgICAgICAgIHBhdHRlcm5JZDogYHRpbWVfJHt0aW1lfWAsXG4gICAgICAgICAgbmFtZTogYCR7dGltZS5jaGFyQXQoMCkudG9VcHBlckNhc2UoKSArIHRpbWUuc2xpY2UoMSl9IFVzYWdlYCxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogYFVzZXIgaXMgYWN0aXZlIGR1cmluZyAke3RpbWV9YCxcbiAgICAgICAgICBmcmVxdWVuY3k6IGNvdW50LFxuICAgICAgICAgIGNvbmZpZGVuY2U6IGNvdW50IC8gc2Vzc2lvbnMubGVuZ3RoLFxuICAgICAgICAgIHRyaWdnZXJzOiBbdGltZV0sXG4gICAgICAgICAgb3V0Y29tZXM6IFsnYXBwX3VzYWdlJ10sXG4gICAgICAgICAgdXNlclNlZ21lbnQ6ICd0ZW1wb3JhbCcsXG4gICAgICAgICAgdGltZVBhdHRlcm46IHtcbiAgICAgICAgICAgIHByZWZlcnJlZFRpbWVzOiBbdGltZV0sXG4gICAgICAgICAgICBzZXNzaW9uRHVyYXRpb246IDAsXG4gICAgICAgICAgICBmcmVxdWVuY3k6ICdkYWlseScsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICByZXR1cm4gcGF0dGVybnM7XG4gIH1cblxuICBwcml2YXRlIGFuYWx5emVGZWF0dXJlVXNhZ2Uoc2Vzc2lvbnM6IFVzZXJTZXNzaW9uW10pOiBCZWhhdmlvclBhdHRlcm5bXSB7XG4gICAgY29uc3QgcGF0dGVybnM6IEJlaGF2aW9yUGF0dGVybltdID0gW107XG4gICAgXG4gICAgLy8gQW5hbHl6ZSBpbnRlcmFjdGlvbiBwYXR0ZXJuc1xuICAgIGNvbnN0IGZlYXR1cmVVc2FnZSA9IG5ldyBNYXA8c3RyaW5nLCBudW1iZXI+KCk7XG4gICAgc2Vzc2lvbnMuZm9yRWFjaChzZXNzaW9uID0+IHtcbiAgICAgIHNlc3Npb24uaW50ZXJhY3Rpb25zLmZvckVhY2goaW50ZXJhY3Rpb24gPT4ge1xuICAgICAgICBmZWF0dXJlVXNhZ2Uuc2V0KGludGVyYWN0aW9uLnRhcmdldCwgKGZlYXR1cmVVc2FnZS5nZXQoaW50ZXJhY3Rpb24udGFyZ2V0KSB8fCAwKSArIDEpO1xuICAgICAgfSk7XG4gICAgfSk7XG5cbiAgICAvLyBDcmVhdGUgZmVhdHVyZSB1c2FnZSBwYXR0ZXJuc1xuICAgIGZlYXR1cmVVc2FnZS5mb3JFYWNoKChjb3VudCwgZmVhdHVyZSkgPT4ge1xuICAgICAgaWYgKGNvdW50ID49IHRoaXMuQU5BTFlTSVNfQ09ORklHLm1pblBhdHRlcm5GcmVxdWVuY3kpIHtcbiAgICAgICAgcGF0dGVybnMucHVzaCh7XG4gICAgICAgICAgcGF0dGVybklkOiBgZmVhdHVyZV8ke2ZlYXR1cmV9YCxcbiAgICAgICAgICBuYW1lOiBgRmVhdHVyZTogJHtmZWF0dXJlfWAsXG4gICAgICAgICAgZGVzY3JpcHRpb246IGBVc2VyIGZyZXF1ZW50bHkgdXNlcyAke2ZlYXR1cmV9YCxcbiAgICAgICAgICBmcmVxdWVuY3k6IGNvdW50LFxuICAgICAgICAgIGNvbmZpZGVuY2U6IE1hdGgubWluKGNvdW50IC8gKHNlc3Npb25zLmxlbmd0aCAqIDEwKSwgMSksIC8vIE5vcm1hbGl6ZSBieSBleHBlY3RlZCBpbnRlcmFjdGlvbnNcbiAgICAgICAgICB0cmlnZ2VyczogWydhcHBfc3RhcnQnXSxcbiAgICAgICAgICBvdXRjb21lczogW2ZlYXR1cmVdLFxuICAgICAgICAgIHVzZXJTZWdtZW50OiAnZmVhdHVyZScsXG4gICAgICAgICAgdGltZVBhdHRlcm46IHtcbiAgICAgICAgICAgIHByZWZlcnJlZFRpbWVzOiBbXSxcbiAgICAgICAgICAgIHNlc3Npb25EdXJhdGlvbjogMCxcbiAgICAgICAgICAgIGZyZXF1ZW5jeTogJ2RhaWx5JyxcbiAgICAgICAgICB9LFxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIHJldHVybiBwYXR0ZXJucztcbiAgfVxuXG4gIHByaXZhdGUgYW5hbHl6ZVBlcmZvcm1hbmNlUGF0dGVybnMoc2Vzc2lvbnM6IFVzZXJTZXNzaW9uW10pOiBCZWhhdmlvclBhdHRlcm5bXSB7XG4gICAgY29uc3QgcGF0dGVybnM6IEJlaGF2aW9yUGF0dGVybltdID0gW107XG4gICAgXG4gICAgLy8gQW5hbHl6ZSBwZXJmb3JtYW5jZS1yZWxhdGVkIHBhdHRlcm5zXG4gICAgY29uc3QgYXZnTG9hZFRpbWUgPSBzZXNzaW9ucy5yZWR1Y2UoKHN1bSwgcykgPT4gc3VtICsgcy5wZXJmb3JtYW5jZS5hdmVyYWdlTG9hZFRpbWUsIDApIC8gc2Vzc2lvbnMubGVuZ3RoO1xuICAgIFxuICAgIGlmIChhdmdMb2FkVGltZSA+IDIwMDApIHsgLy8gU2xvdyBwZXJmb3JtYW5jZSBwYXR0ZXJuXG4gICAgICBwYXR0ZXJucy5wdXNoKHtcbiAgICAgICAgcGF0dGVybklkOiAncGVyZl9zbG93X2xvYWRpbmcnLFxuICAgICAgICBuYW1lOiAnU2xvdyBMb2FkaW5nIFBlcmZvcm1hbmNlJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdVc2VyIGV4cGVyaWVuY2VzIHNsb3cgbG9hZGluZyB0aW1lcycsXG4gICAgICAgIGZyZXF1ZW5jeTogc2Vzc2lvbnMubGVuZ3RoLFxuICAgICAgICBjb25maWRlbmNlOiAwLjksXG4gICAgICAgIHRyaWdnZXJzOiBbJ3Nsb3dfbmV0d29yaycsICdsb3dfbWVtb3J5J10sXG4gICAgICAgIG91dGNvbWVzOiBbJ3BlcmZvcm1hbmNlX29wdGltaXphdGlvbl9uZWVkZWQnXSxcbiAgICAgICAgdXNlclNlZ21lbnQ6ICdwZXJmb3JtYW5jZScsXG4gICAgICAgIHRpbWVQYXR0ZXJuOiB7XG4gICAgICAgICAgcHJlZmVycmVkVGltZXM6IFtdLFxuICAgICAgICAgIHNlc3Npb25EdXJhdGlvbjogMCxcbiAgICAgICAgICBmcmVxdWVuY3k6ICdkYWlseScsXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICB9XG5cbiAgICByZXR1cm4gcGF0dGVybnM7XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIHVwZGF0ZVVzZXJTZWdtZW50KHVzZXJJZDogc3RyaW5nLCBwYXR0ZXJuczogQmVoYXZpb3JQYXR0ZXJuW10pOiBQcm9taXNlPHZvaWQ+IHtcbiAgICAvLyBEZXRlcm1pbmUgdXNlciBzZWdtZW50IGJhc2VkIG9uIHBhdHRlcm5zXG4gICAgY29uc3Qgc2VnbWVudCA9IHRoaXMuZGV0ZXJtaW5lVXNlclNlZ21lbnQocGF0dGVybnMpO1xuICAgIHRoaXMudXNlclNlZ21lbnRzLnNldCh1c2VySWQsIHNlZ21lbnQpO1xuICAgIFxuICAgIC8vIEFwcGx5IHNlZ21lbnQtc3BlY2lmaWMgb3B0aW1pemF0aW9uc1xuICAgIGF3YWl0IHRoaXMuYXBwbHlTZWdtZW50T3B0aW1pemF0aW9ucyh1c2VySWQsIHNlZ21lbnQpO1xuICB9XG5cbiAgcHJpdmF0ZSBkZXRlcm1pbmVVc2VyU2VnbWVudChwYXR0ZXJuczogQmVoYXZpb3JQYXR0ZXJuW10pOiBVc2VyU2VnbWVudCB7XG4gICAgLy8gU2ltcGxlIHNlZ21lbnRhdGlvbiBsb2dpYyAod291bGQgYmUgbW9yZSBzb3BoaXN0aWNhdGVkIGluIHJlYWwgaW1wbGVtZW50YXRpb24pXG4gICAgY29uc3QgbmF2aWdhdGlvblBhdHRlcm5zID0gcGF0dGVybnMuZmlsdGVyKHAgPT4gcC51c2VyU2VnbWVudCA9PT0gJ25hdmlnYXRpb24nKS5sZW5ndGg7XG4gICAgY29uc3QgZmVhdHVyZVBhdHRlcm5zID0gcGF0dGVybnMuZmlsdGVyKHAgPT4gcC51c2VyU2VnbWVudCA9PT0gJ2ZlYXR1cmUnKS5sZW5ndGg7XG4gICAgXG4gICAgaWYgKGZlYXR1cmVQYXR0ZXJucyA+IDUpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHNlZ21lbnRJZDogJ3Bvd2VyX3VzZXInLFxuICAgICAgICBuYW1lOiAnUG93ZXIgVXNlcicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnSGVhdnkgZmVhdHVyZSB1c2FnZSwgbmVlZHMgaGlnaCBwZXJmb3JtYW5jZScsXG4gICAgICAgIGNoYXJhY3RlcmlzdGljczoge1xuICAgICAgICAgIHNraWxsTGV2ZWw6ICdhZHZhbmNlZCcsXG4gICAgICAgICAgYWN0aXZpdHlMZXZlbDogJ2ludGVuc2l2ZScsXG4gICAgICAgICAgZmVhdHVyZVVzYWdlOiB7fSxcbiAgICAgICAgICBzZXNzaW9uUGF0dGVybnM6IHtcbiAgICAgICAgICAgIGF2ZXJhZ2VEdXJhdGlvbjogMTgwMDAwMCwgLy8gMzAgbWludXRlc1xuICAgICAgICAgICAgcHJlZmVycmVkVGltZXM6IFsnZXZlbmluZyddLFxuICAgICAgICAgICAgZnJlcXVlbmN5OiA1LFxuICAgICAgICAgIH0sXG4gICAgICAgIH0sXG4gICAgICAgIG9wdGltaXphdGlvbnM6IHtcbiAgICAgICAgICBjYWNoZVN0cmF0ZWd5OiAnYWdncmVzc2l2ZScsXG4gICAgICAgICAgcGVyZm9ybWFuY2VQcm9maWxlOiAncGVyZm9ybWFuY2UnLFxuICAgICAgICAgIHVpQ29tcGxleGl0eTogJ2VuaGFuY2VkJyxcbiAgICAgICAgICBwcmVsb2FkVGFyZ2V0czogWyd0cmFpbmluZ19kYXRhJywgJ2FuYWx5dGljcyddLFxuICAgICAgICB9LFxuICAgICAgfTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIHtcbiAgICAgICAgc2VnbWVudElkOiAnY2FzdWFsX3VzZXInLFxuICAgICAgICBuYW1lOiAnQ2FzdWFsIFVzZXInLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0xpZ2h0IHVzYWdlLCBwcmVmZXJzIHNpbXBsaWNpdHknLFxuICAgICAgICBjaGFyYWN0ZXJpc3RpY3M6IHtcbiAgICAgICAgICBza2lsbExldmVsOiAnYmVnaW5uZXInLFxuICAgICAgICAgIGFjdGl2aXR5TGV2ZWw6ICdjYXN1YWwnLFxuICAgICAgICAgIGZlYXR1cmVVc2FnZToge30sXG4gICAgICAgICAgc2Vzc2lvblBhdHRlcm5zOiB7XG4gICAgICAgICAgICBhdmVyYWdlRHVyYXRpb246IDYwMDAwMCwgLy8gMTAgbWludXRlc1xuICAgICAgICAgICAgcHJlZmVycmVkVGltZXM6IFsnYWZ0ZXJub29uJ10sXG4gICAgICAgICAgICBmcmVxdWVuY3k6IDIsXG4gICAgICAgICAgfSxcbiAgICAgICAgfSxcbiAgICAgICAgb3B0aW1pemF0aW9uczoge1xuICAgICAgICAgIGNhY2hlU3RyYXRlZ3k6ICdjb25zZXJ2YXRpdmUnLFxuICAgICAgICAgIHBlcmZvcm1hbmNlUHJvZmlsZTogJ2JhbGFuY2VkJyxcbiAgICAgICAgICB1aUNvbXBsZXhpdHk6ICdzdGFuZGFyZCcsXG4gICAgICAgICAgcHJlbG9hZFRhcmdldHM6IFsnYmFzaWNfZGF0YSddLFxuICAgICAgICB9LFxuICAgICAgfTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIGFwcGx5U2VnbWVudE9wdGltaXphdGlvbnModXNlcklkOiBzdHJpbmcsIHNlZ21lbnQ6IFVzZXJTZWdtZW50KTogUHJvbWlzZTx2b2lkPiB7XG4gICAgLy8gQXBwbHkgcGVyZm9ybWFuY2UgcHJvZmlsZSBiYXNlZCBvbiBzZWdtZW50XG4gICAgY29uc3QgcHJvZmlsZU5hbWUgPSBzZWdtZW50Lm9wdGltaXphdGlvbnMucGVyZm9ybWFuY2VQcm9maWxlO1xuICAgIC8vIFRoaXMgd291bGQgaW50ZWdyYXRlIHdpdGggdGhlIGFkYXB0aXZlIHBlcmZvcm1hbmNlIG1hbmFnZXJcbiAgICBjb25zb2xlLmxvZyhgQXBwbGllZCAke3Byb2ZpbGVOYW1lfSBwcm9maWxlIGZvciB1c2VyIHNlZ21lbnQ6ICR7c2VnbWVudC5uYW1lfWApO1xuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBnZW5lcmF0ZU9wdGltaXphdGlvblJlY29tbWVuZGF0aW9ucyhcbiAgICB1c2VySWQ6IHN0cmluZyxcbiAgICBwYXR0ZXJuczogQmVoYXZpb3JQYXR0ZXJuW11cbiAgKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgY29uc3QgaW5zaWdodHM6IEFuYWx5dGljc0luc2lnaHRbXSA9IFtdO1xuICAgIFxuICAgIC8vIEdlbmVyYXRlIGluc2lnaHRzIGJhc2VkIG9uIHBhdHRlcm5zXG4gICAgcGF0dGVybnMuZm9yRWFjaChwYXR0ZXJuID0+IHtcbiAgICAgIGlmIChwYXR0ZXJuLmNvbmZpZGVuY2UgPiB0aGlzLkFOQUxZU0lTX0NPTkZJRy5taW5Db25maWRlbmNlVGhyZXNob2xkKSB7XG4gICAgICAgIGluc2lnaHRzLnB1c2goe1xuICAgICAgICAgIHR5cGU6ICdvcHRpbWl6YXRpb24nLFxuICAgICAgICAgIHRpdGxlOiBgT3B0aW1pemUgZm9yICR7cGF0dGVybi5uYW1lfWAsXG4gICAgICAgICAgZGVzY3JpcHRpb246IHBhdHRlcm4uZGVzY3JpcHRpb24sXG4gICAgICAgICAgaW1wYWN0OiBwYXR0ZXJuLmZyZXF1ZW5jeSA+IDEwID8gJ2hpZ2gnIDogcGF0dGVybi5mcmVxdWVuY3kgPiA1ID8gJ21lZGl1bScgOiAnbG93JyxcbiAgICAgICAgICBjb25maWRlbmNlOiBwYXR0ZXJuLmNvbmZpZGVuY2UsXG4gICAgICAgICAgcmVjb21tZW5kYXRpb246IGBDb25zaWRlciBvcHRpbWl6aW5nIGZvciB0aGlzIHVzYWdlIHBhdHRlcm5gLFxuICAgICAgICAgIGltcGxlbWVudGF0aW9uOiBgSW1wbGVtZW50IGNhY2hpbmcgc3RyYXRlZ3kgZm9yICR7cGF0dGVybi5vdXRjb21lcy5qb2luKCcsICcpfWAsXG4gICAgICAgICAgbWV0cmljczogeyB1c2VySWQ6IHVzZXJJZCBhcyBhbnksIGZyZXF1ZW5jeTogcGF0dGVybi5mcmVxdWVuY3kgfSxcbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfSk7XG4gICAgXG4gICAgdGhpcy5hbmFseXRpY3NJbnNpZ2h0cy5wdXNoKC4uLmluc2lnaHRzKTtcbiAgICBcbiAgICAvLyBMaW1pdCBpbnNpZ2h0cyBoaXN0b3J5XG4gICAgaWYgKHRoaXMuYW5hbHl0aWNzSW5zaWdodHMubGVuZ3RoID4gMTAwMCkge1xuICAgICAgdGhpcy5hbmFseXRpY3NJbnNpZ2h0cyA9IHRoaXMuYW5hbHl0aWNzSW5zaWdodHMuc2xpY2UoLTEwMDApO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgbG9hZEFuYWx5dGljc0RhdGEoKTogUHJvbWlzZTx2b2lkPiB7XG4gICAgLy8gSW4gYSByZWFsIGltcGxlbWVudGF0aW9uLCB0aGlzIHdvdWxkIGxvYWQgZnJvbSBwZXJzaXN0ZW50IHN0b3JhZ2VcbiAgICBjb25zb2xlLmxvZygnTG9hZGVkIGFuYWx5dGljcyBkYXRhIGZyb20gc3RvcmFnZScpO1xuICB9XG5cbiAgcHJpdmF0ZSBzdGFydFBlcmlvZGljQW5hbHlzaXMoKTogdm9pZCB7XG4gICAgc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgdGhpcy5wZXJmb3JtUGVyaW9kaWNBbmFseXNpcygpO1xuICAgIH0sIHRoaXMuQU5BTFlTSVNfQ09ORklHLmFuYWx5c2lzSW50ZXJ2YWwpO1xuICB9XG5cbiAgcHJpdmF0ZSBhc3luYyBwZXJmb3JtUGVyaW9kaWNBbmFseXNpcygpOiBQcm9taXNlPHZvaWQ+IHtcbiAgICBjb25zb2xlLmxvZygnUGVyZm9ybWluZyBwZXJpb2RpYyBiZWhhdmlvciBhbmFseXNpcy4uLicpO1xuICAgIFxuICAgIC8vIEFuYWx5emUgYWxsIHVzZXJzIHdpdGggcmVjZW50IGFjdGl2aXR5XG4gICAgZm9yIChjb25zdCBbdXNlcklkLCBzZXNzaW9uc10gb2YgdGhpcy51c2VyU2Vzc2lvbnMuZW50cmllcygpKSB7XG4gICAgICBjb25zdCByZWNlbnRTZXNzaW9ucyA9IHNlc3Npb25zLmZpbHRlcihzID0+IFxuICAgICAgICBEYXRlLm5vdygpIC0gcy5zdGFydFRpbWUgPCA4NjQwMDAwMCAvLyBMYXN0IDI0IGhvdXJzXG4gICAgICApO1xuICAgICAgXG4gICAgICBpZiAocmVjZW50U2Vzc2lvbnMubGVuZ3RoID4gMCkge1xuICAgICAgICBhd2FpdCB0aGlzLmFuYWx5emVVc2VyQmVoYXZpb3IodXNlcklkKTtcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGluaXRpYWxpemVVc2VyU2VnbWVudHMoKTogdm9pZCB7XG4gICAgLy8gSW5pdGlhbGl6ZSBkZWZhdWx0IHNlZ21lbnRzXG4gICAgY29uc29sZS5sb2coJ0luaXRpYWxpemVkIHVzZXIgc2VnbWVudHMnKTtcbiAgfVxufVxuXG4vLyBFeHBvcnQgc2luZ2xldG9uIGluc3RhbmNlXG5leHBvcnQgY29uc3QgYmVoYXZpb3JhbEFuYWx5dGljc0VuZ2luZSA9IG5ldyBCZWhhdmlvcmFsQW5hbHl0aWNzRW5naW5lKCk7XG5leHBvcnQgZGVmYXVsdCBiZWhhdmlvcmFsQW5hbHl0aWNzRW5naW5lO1xuIl0sIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPQSxTQUFTQSxxQkFBcUI7QUFBa0MsSUE0RzFEQyx5QkFBeUI7RUFjN0IsU0FBQUEsMEJBQUEsRUFBYztJQUFBQyxlQUFBLE9BQUFELHlCQUFBO0lBQUEsS0FiTkUsWUFBWSxJQUFBQyxhQUFBLEdBQUFDLENBQUEsT0FBK0IsSUFBSUMsR0FBRyxDQUFDLENBQUM7SUFBQSxLQUNwREMsZ0JBQWdCLElBQUFILGFBQUEsR0FBQUMsQ0FBQSxPQUFtQyxJQUFJQyxHQUFHLENBQUMsQ0FBQztJQUFBLEtBQzVERSxZQUFZLElBQUFKLGFBQUEsR0FBQUMsQ0FBQSxPQUE2QixJQUFJQyxHQUFHLENBQUMsQ0FBQztJQUFBLEtBQ2xERyxpQkFBaUIsSUFBQUwsYUFBQSxHQUFBQyxDQUFBLE9BQXVCLEVBQUU7SUFBQSxLQUVqQ0ssZUFBZSxJQUFBTixhQUFBLEdBQUFDLENBQUEsT0FBRztNQUNqQ00sY0FBYyxFQUFFLE9BQU87TUFDdkJDLG1CQUFtQixFQUFFLENBQUM7TUFDdEJDLHNCQUFzQixFQUFFLEdBQUc7TUFDM0JDLGlCQUFpQixFQUFFLEdBQUc7TUFDdEJDLGdCQUFnQixFQUFFO0lBQ3BCLENBQUM7SUFBQVgsYUFBQSxHQUFBWSxDQUFBO0lBQUFaLGFBQUEsR0FBQUMsQ0FBQTtJQUdDLElBQUksQ0FBQ1ksNkJBQTZCLENBQUMsQ0FBQztFQUN0QztFQUFDLE9BQUFDLFlBQUEsQ0FBQWpCLHlCQUFBO0lBQUFrQixHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBQyw4QkFBQSxHQUFBQyxpQkFBQSxDQUtELGFBQTZEO1FBQUFsQixhQUFBLEdBQUFZLENBQUE7UUFBQVosYUFBQSxHQUFBQyxDQUFBO1FBQzNELElBQUk7VUFBQUQsYUFBQSxHQUFBQyxDQUFBO1VBRUYsTUFBTSxJQUFJLENBQUNrQixpQkFBaUIsQ0FBQyxDQUFDO1VBQUNuQixhQUFBLEdBQUFDLENBQUE7VUFHL0IsSUFBSSxDQUFDbUIscUJBQXFCLENBQUMsQ0FBQztVQUFDcEIsYUFBQSxHQUFBQyxDQUFBO1VBRzdCLElBQUksQ0FBQ29CLHNCQUFzQixDQUFDLENBQUM7VUFBQ3JCLGFBQUEsR0FBQUMsQ0FBQTtVQUU5QnFCLE9BQU8sQ0FBQ0MsR0FBRyxDQUFDLHNEQUFzRCxDQUFDO1FBQ3JFLENBQUMsQ0FBQyxPQUFPQyxLQUFLLEVBQUU7VUFBQXhCLGFBQUEsR0FBQUMsQ0FBQTtVQUNkcUIsT0FBTyxDQUFDRSxLQUFLLENBQUMsbURBQW1ELEVBQUVBLEtBQUssQ0FBQztRQUMzRTtNQUNGLENBQUM7TUFBQSxTQWZhWCw2QkFBNkJBLENBQUE7UUFBQSxPQUFBSSw4QkFBQSxDQUFBUSxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQTdCYiw2QkFBNkI7SUFBQTtFQUFBO0lBQUFFLEdBQUE7SUFBQUMsS0FBQSxFQW9CM0MsU0FBQVcsWUFBWUEsQ0FBQ0MsTUFBYyxFQUFFQyxPQUF1QixFQUFVO01BQUE3QixhQUFBLEdBQUFZLENBQUE7TUFDNUQsSUFBTWtCLFNBQVMsSUFBQTlCLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLElBQUksQ0FBQzhCLGlCQUFpQixDQUFDSCxNQUFNLENBQUM7TUFFaEQsSUFBTUksT0FBb0IsSUFBQWhDLGFBQUEsR0FBQUMsQ0FBQSxRQUFHO1FBQzNCNkIsU0FBUyxFQUFUQSxTQUFTO1FBQ1RGLE1BQU0sRUFBTkEsTUFBTTtRQUNOSyxTQUFTLEVBQUVDLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7UUFDckJDLFdBQVcsRUFBRSxFQUFFO1FBQ2ZDLFlBQVksRUFBRSxFQUFFO1FBQ2hCQyxXQUFXLEVBQUU7VUFDWEMsZUFBZSxFQUFFLENBQUM7VUFDbEJDLFVBQVUsRUFBRSxDQUFDO1VBQ2JDLFVBQVUsRUFBRSxDQUFDO1VBQ2JDLFdBQVcsRUFBRSxFQUFFO1VBQ2ZDLFFBQVEsRUFBRSxFQUFFO1VBQ1pDLGVBQWUsRUFBRSxDQUFDO1VBQ2xCQyxZQUFZLEVBQUU7UUFDaEIsQ0FBQztRQUNEaEIsT0FBTyxFQUFQQTtNQUNGLENBQUM7TUFBQzdCLGFBQUEsR0FBQUMsQ0FBQTtNQUVGLElBQUksQ0FBQyxJQUFJLENBQUNGLFlBQVksQ0FBQytDLEdBQUcsQ0FBQ2xCLE1BQU0sQ0FBQyxFQUFFO1FBQUE1QixhQUFBLEdBQUErQyxDQUFBO1FBQUEvQyxhQUFBLEdBQUFDLENBQUE7UUFDbEMsSUFBSSxDQUFDRixZQUFZLENBQUNpRCxHQUFHLENBQUNwQixNQUFNLEVBQUUsRUFBRSxDQUFDO01BQ25DLENBQUM7UUFBQTVCLGFBQUEsR0FBQStDLENBQUE7TUFBQTtNQUFBL0MsYUFBQSxHQUFBQyxDQUFBO01BRUQsSUFBSSxDQUFDRixZQUFZLENBQUNrRCxHQUFHLENBQUNyQixNQUFNLENBQUMsQ0FBRXNCLElBQUksQ0FBQ2xCLE9BQU8sQ0FBQztNQUFDaEMsYUFBQSxHQUFBQyxDQUFBO01BRTdDcUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsbUJBQW1CTyxTQUFTLGFBQWFGLE1BQU0sRUFBRSxDQUFDO01BQUM1QixhQUFBLEdBQUFDLENBQUE7TUFDL0QsT0FBTzZCLFNBQVM7SUFDbEI7RUFBQztJQUFBZixHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBbUMsZUFBZUEsQ0FDYnZCLE1BQWMsRUFDZEUsU0FBaUIsRUFDakJzQixNQUFjLEVBQ2RDLFFBQWdCLEVBQ1Y7TUFBQXJELGFBQUEsR0FBQVksQ0FBQTtNQUNOLElBQU1vQixPQUFPLElBQUFoQyxhQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNxRCxXQUFXLENBQUMxQixNQUFNLEVBQUVFLFNBQVMsQ0FBQztNQUFDOUIsYUFBQSxHQUFBQyxDQUFBO01BQ3BELElBQUksQ0FBQytCLE9BQU8sRUFBRTtRQUFBaEMsYUFBQSxHQUFBK0MsQ0FBQTtRQUFBL0MsYUFBQSxHQUFBQyxDQUFBO1FBQUE7TUFBTSxDQUFDO1FBQUFELGFBQUEsR0FBQStDLENBQUE7TUFBQTtNQUVyQixJQUFNUSxVQUFzQixJQUFBdkQsYUFBQSxHQUFBQyxDQUFBLFFBQUc7UUFDN0JtRCxNQUFNLEVBQU5BLE1BQU07UUFDTkksU0FBUyxFQUFFdEIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztRQUNyQnNCLFFBQVEsRUFBRSxDQUFDO1FBQ1hKLFFBQVEsRUFBUkEsUUFBUTtRQUNSSyxVQUFVLEVBQUUsWUFBWTtRQUN4QkMsZ0JBQWdCLEVBQUU7TUFDcEIsQ0FBQztNQUdELElBQU1DLFlBQVksSUFBQTVELGFBQUEsR0FBQUMsQ0FBQSxRQUFHK0IsT0FBTyxDQUFDSSxXQUFXLENBQUNKLE9BQU8sQ0FBQ0ksV0FBVyxDQUFDeUIsTUFBTSxHQUFHLENBQUMsQ0FBQztNQUFDN0QsYUFBQSxHQUFBQyxDQUFBO01BQ3pFLElBQUkyRCxZQUFZLEVBQUU7UUFBQTVELGFBQUEsR0FBQStDLENBQUE7UUFBQS9DLGFBQUEsR0FBQUMsQ0FBQTtRQUNoQjJELFlBQVksQ0FBQ0gsUUFBUSxHQUFHRixVQUFVLENBQUNDLFNBQVMsR0FBR0ksWUFBWSxDQUFDSixTQUFTO01BQ3ZFLENBQUM7UUFBQXhELGFBQUEsR0FBQStDLENBQUE7TUFBQTtNQUFBL0MsYUFBQSxHQUFBQyxDQUFBO01BRUQrQixPQUFPLENBQUNJLFdBQVcsQ0FBQ2MsSUFBSSxDQUFDSyxVQUFVLENBQUM7TUFBQ3ZELGFBQUEsR0FBQUMsQ0FBQTtNQUdyQ0wscUJBQXFCLENBQUNrRSxpQkFBaUIsQ0FDckNsQyxNQUFNLEVBQ047UUFDRW1DLElBQUksRUFBRSxhQUFhO1FBQ25CQyxNQUFNLEVBQUVaLE1BQU07UUFDZEksU0FBUyxFQUFFdEIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztRQUNyQnNCLFFBQVEsRUFBRUo7TUFDWixDQUFDLEVBQ0RyQixPQUFPLENBQUNILE9BQU8sRUFDZkcsT0FBTyxDQUFDSCxPQUNWLENBQUM7SUFDSDtFQUFDO0lBQUFkLEdBQUE7SUFBQUMsS0FBQSxFQUtELFNBQUFpRCxnQkFBZ0JBLENBQ2RyQyxNQUFjLEVBQ2RFLFNBQWlCLEVBQ2pCb0MsV0FBK0MsRUFDekM7TUFBQWxFLGFBQUEsR0FBQVksQ0FBQTtNQUNOLElBQU1vQixPQUFPLElBQUFoQyxhQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNxRCxXQUFXLENBQUMxQixNQUFNLEVBQUVFLFNBQVMsQ0FBQztNQUFDOUIsYUFBQSxHQUFBQyxDQUFBO01BQ3BELElBQUksQ0FBQytCLE9BQU8sRUFBRTtRQUFBaEMsYUFBQSxHQUFBK0MsQ0FBQTtRQUFBL0MsYUFBQSxHQUFBQyxDQUFBO1FBQUE7TUFBTSxDQUFDO1FBQUFELGFBQUEsR0FBQStDLENBQUE7TUFBQTtNQUVyQixJQUFNb0IsZUFBZ0MsSUFBQW5FLGFBQUEsR0FBQUMsQ0FBQSxRQUFBbUUsTUFBQSxDQUFBQyxNQUFBLEtBQ2pDSCxXQUFXO1FBQ2RWLFNBQVMsRUFBRXRCLElBQUksQ0FBQ0MsR0FBRyxDQUFDO01BQUMsR0FDdEI7TUFBQ25DLGFBQUEsR0FBQUMsQ0FBQTtNQUVGK0IsT0FBTyxDQUFDSyxZQUFZLENBQUNhLElBQUksQ0FBQ2lCLGVBQWUsQ0FBQztNQUcxQyxJQUFNRyxpQkFBaUIsSUFBQXRFLGFBQUEsR0FBQUMsQ0FBQSxRQUFHK0IsT0FBTyxDQUFDSSxXQUFXLENBQUNKLE9BQU8sQ0FBQ0ksV0FBVyxDQUFDeUIsTUFBTSxHQUFHLENBQUMsQ0FBQztNQUFDN0QsYUFBQSxHQUFBQyxDQUFBO01BQzlFLElBQUlxRSxpQkFBaUIsRUFBRTtRQUFBdEUsYUFBQSxHQUFBK0MsQ0FBQTtRQUFBL0MsYUFBQSxHQUFBQyxDQUFBO1FBQ3JCcUUsaUJBQWlCLENBQUNYLGdCQUFnQixFQUFFO01BQ3RDLENBQUM7UUFBQTNELGFBQUEsR0FBQStDLENBQUE7TUFBQTtNQUFBL0MsYUFBQSxHQUFBQyxDQUFBO01BR0RMLHFCQUFxQixDQUFDa0UsaUJBQWlCLENBQ3JDbEMsTUFBTSxFQUNOO1FBQ0VtQyxJQUFJLEVBQUUsYUFBYTtRQUNuQkMsTUFBTSxFQUFFRSxXQUFXLENBQUNGLE1BQU07UUFDMUJSLFNBQVMsRUFBRXRCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7UUFDckJvQyxRQUFRLEVBQUU7VUFDUkMsZUFBZSxFQUFFTixXQUFXLENBQUNILElBQUk7VUFDakNVLE9BQU8sRUFBRVAsV0FBVyxDQUFDTztRQUN2QjtNQUNGLENBQUMsRUFDRHpDLE9BQU8sQ0FBQ0gsT0FBTyxFQUNmRyxPQUFPLENBQUNILE9BQ1YsQ0FBQztJQUNIO0VBQUM7SUFBQWQsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQTBELFVBQVVBLENBQUM5QyxNQUFjLEVBQUVFLFNBQWlCLEVBQVE7TUFBQTlCLGFBQUEsR0FBQVksQ0FBQTtNQUNsRCxJQUFNb0IsT0FBTyxJQUFBaEMsYUFBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSSxDQUFDcUQsV0FBVyxDQUFDMUIsTUFBTSxFQUFFRSxTQUFTLENBQUM7TUFBQzlCLGFBQUEsR0FBQUMsQ0FBQTtNQUNwRCxJQUFJLENBQUMrQixPQUFPLEVBQUU7UUFBQWhDLGFBQUEsR0FBQStDLENBQUE7UUFBQS9DLGFBQUEsR0FBQUMsQ0FBQTtRQUFBO01BQU0sQ0FBQztRQUFBRCxhQUFBLEdBQUErQyxDQUFBO01BQUE7TUFBQS9DLGFBQUEsR0FBQUMsQ0FBQTtNQUVyQitCLE9BQU8sQ0FBQzJDLE9BQU8sR0FBR3pDLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7TUFBQ25DLGFBQUEsR0FBQUMsQ0FBQTtNQUM3QitCLE9BQU8sQ0FBQ3lCLFFBQVEsR0FBR3pCLE9BQU8sQ0FBQzJDLE9BQU8sR0FBRzNDLE9BQU8sQ0FBQ0MsU0FBUztNQUd0RCxJQUFNMkMsY0FBYyxJQUFBNUUsYUFBQSxHQUFBQyxDQUFBLFFBQUcrQixPQUFPLENBQUNJLFdBQVcsQ0FBQ0osT0FBTyxDQUFDSSxXQUFXLENBQUN5QixNQUFNLEdBQUcsQ0FBQyxDQUFDO01BQUM3RCxhQUFBLEdBQUFDLENBQUE7TUFDM0UsSUFBSSxDQUFBRCxhQUFBLEdBQUErQyxDQUFBLFVBQUE2QixjQUFjLE1BQUE1RSxhQUFBLEdBQUErQyxDQUFBLFVBQUksQ0FBQzZCLGNBQWMsQ0FBQ25CLFFBQVEsR0FBRTtRQUFBekQsYUFBQSxHQUFBK0MsQ0FBQTtRQUFBL0MsYUFBQSxHQUFBQyxDQUFBO1FBQzlDMkUsY0FBYyxDQUFDbkIsUUFBUSxHQUFHekIsT0FBTyxDQUFDMkMsT0FBTyxHQUFHQyxjQUFjLENBQUNwQixTQUFTO01BQ3RFLENBQUM7UUFBQXhELGFBQUEsR0FBQStDLENBQUE7TUFBQTtNQUFBL0MsYUFBQSxHQUFBQyxDQUFBO01BR0QsSUFBSSxDQUFDNEUsdUJBQXVCLENBQUM3QyxPQUFPLENBQUM7TUFBQ2hDLGFBQUEsR0FBQUMsQ0FBQTtNQUd0QyxJQUFJLENBQUM2RSxtQkFBbUIsQ0FBQ2xELE1BQU0sQ0FBQztNQUFDNUIsYUFBQSxHQUFBQyxDQUFBO01BRWpDcUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsaUJBQWlCTyxTQUFTLGFBQWFGLE1BQU0sZUFBZUksT0FBTyxDQUFDeUIsUUFBUSxLQUFLLENBQUM7SUFDaEc7RUFBQztJQUFBMUMsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQStELG9CQUFBLEdBQUE3RCxpQkFBQSxDQUtELFdBQTBCVSxNQUFjLEVBQThCO1FBQUE1QixhQUFBLEdBQUFZLENBQUE7UUFDcEUsSUFBTWIsWUFBWSxJQUFBQyxhQUFBLEdBQUFDLENBQUEsUUFBRyxDQUFBRCxhQUFBLEdBQUErQyxDQUFBLGNBQUksQ0FBQ2hELFlBQVksQ0FBQ2tELEdBQUcsQ0FBQ3JCLE1BQU0sQ0FBQyxNQUFBNUIsYUFBQSxHQUFBK0MsQ0FBQSxVQUFJLEVBQUU7UUFBQy9DLGFBQUEsR0FBQUMsQ0FBQTtRQUN6RCxJQUFJRixZQUFZLENBQUM4RCxNQUFNLEdBQUcsQ0FBQyxFQUFFO1VBQUE3RCxhQUFBLEdBQUErQyxDQUFBO1VBQUEvQyxhQUFBLEdBQUFDLENBQUE7VUFDM0IsT0FBTyxFQUFFO1FBQ1gsQ0FBQztVQUFBRCxhQUFBLEdBQUErQyxDQUFBO1FBQUE7UUFFRCxJQUFNaUMsUUFBMkIsSUFBQWhGLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLEVBQUU7UUFBQ0QsYUFBQSxHQUFBQyxDQUFBO1FBRXZDLElBQUk7VUFFRixJQUFNZ0Ysa0JBQWtCLElBQUFqRixhQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNpRix5QkFBeUIsQ0FBQ25GLFlBQVksQ0FBQztVQUFDQyxhQUFBLEdBQUFDLENBQUE7VUFDeEUrRSxRQUFRLENBQUM5QixJQUFJLENBQUF6QixLQUFBLENBQWJ1RCxRQUFRLEVBQUFHLGtCQUFBLENBQVNGLGtCQUFrQixFQUFDO1VBR3BDLElBQU1HLFlBQVksSUFBQXBGLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLElBQUksQ0FBQ29GLG1CQUFtQixDQUFDdEYsWUFBWSxDQUFDO1VBQUNDLGFBQUEsR0FBQUMsQ0FBQTtVQUM1RCtFLFFBQVEsQ0FBQzlCLElBQUksQ0FBQXpCLEtBQUEsQ0FBYnVELFFBQVEsRUFBQUcsa0JBQUEsQ0FBU0MsWUFBWSxFQUFDO1VBRzlCLElBQU1FLGVBQWUsSUFBQXRGLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLElBQUksQ0FBQ3NGLG1CQUFtQixDQUFDeEYsWUFBWSxDQUFDO1VBQUNDLGFBQUEsR0FBQUMsQ0FBQTtVQUMvRCtFLFFBQVEsQ0FBQzlCLElBQUksQ0FBQXpCLEtBQUEsQ0FBYnVELFFBQVEsRUFBQUcsa0JBQUEsQ0FBU0csZUFBZSxFQUFDO1VBR2pDLElBQU1FLG1CQUFtQixJQUFBeEYsYUFBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSSxDQUFDd0YsMEJBQTBCLENBQUMxRixZQUFZLENBQUM7VUFBQ0MsYUFBQSxHQUFBQyxDQUFBO1VBQzFFK0UsUUFBUSxDQUFDOUIsSUFBSSxDQUFBekIsS0FBQSxDQUFidUQsUUFBUSxFQUFBRyxrQkFBQSxDQUFTSyxtQkFBbUIsRUFBQztVQUFDeEYsYUFBQSxHQUFBQyxDQUFBO1VBR3RDLElBQUksQ0FBQ0UsZ0JBQWdCLENBQUM2QyxHQUFHLENBQUNwQixNQUFNLEVBQUVvRCxRQUFRLENBQUM7VUFBQ2hGLGFBQUEsR0FBQUMsQ0FBQTtVQUc1QyxNQUFNLElBQUksQ0FBQ3lGLGlCQUFpQixDQUFDOUQsTUFBTSxFQUFFb0QsUUFBUSxDQUFDO1VBQUNoRixhQUFBLEdBQUFDLENBQUE7VUFHL0MsTUFBTSxJQUFJLENBQUMwRixtQ0FBbUMsQ0FBQy9ELE1BQU0sRUFBRW9ELFFBQVEsQ0FBQztVQUFDaEYsYUFBQSxHQUFBQyxDQUFBO1VBRWpFLE9BQU8rRSxRQUFRO1FBRWpCLENBQUMsQ0FBQyxPQUFPeEQsS0FBSyxFQUFFO1VBQUF4QixhQUFBLEdBQUFDLENBQUE7VUFDZHFCLE9BQU8sQ0FBQ0UsS0FBSyxDQUFDLGtDQUFrQyxFQUFFQSxLQUFLLENBQUM7VUFBQ3hCLGFBQUEsR0FBQUMsQ0FBQTtVQUN6RCxPQUFPLEVBQUU7UUFDWDtNQUNGLENBQUM7TUFBQSxTQXhDSzZFLG1CQUFtQkEsQ0FBQWMsRUFBQTtRQUFBLE9BQUFiLG9CQUFBLENBQUF0RCxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQW5Cb0QsbUJBQW1CO0lBQUE7RUFBQTtJQUFBL0QsR0FBQTtJQUFBQyxLQUFBLEVBNkN6QixTQUFBNkUsY0FBY0EsQ0FBQ2pFLE1BQWMsRUFBc0I7TUFBQTVCLGFBQUEsR0FBQVksQ0FBQTtNQUFBWixhQUFBLEdBQUFDLENBQUE7TUFDakQsT0FBTyxDQUFBRCxhQUFBLEdBQUErQyxDQUFBLGVBQUksQ0FBQzNDLFlBQVksQ0FBQzZDLEdBQUcsQ0FBQ3JCLE1BQU0sQ0FBQyxNQUFBNUIsYUFBQSxHQUFBK0MsQ0FBQSxXQUFJLElBQUk7SUFDOUM7RUFBQztJQUFBaEMsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQThFLG9CQUFvQkEsQ0FBQ2xFLE1BQWUsRUFBc0I7TUFBQTVCLGFBQUEsR0FBQVksQ0FBQTtNQUFBWixhQUFBLEdBQUFDLENBQUE7TUFDeEQsSUFBSTJCLE1BQU0sRUFBRTtRQUFBNUIsYUFBQSxHQUFBK0MsQ0FBQTtRQUFBL0MsYUFBQSxHQUFBQyxDQUFBO1FBQ1YsT0FBTyxJQUFJLENBQUNJLGlCQUFpQixDQUFDMEYsTUFBTSxDQUFDLFVBQUFDLE9BQU8sRUFDMUM7VUFBQWhHLGFBQUEsR0FBQVksQ0FBQTtVQUFBWixhQUFBLEdBQUFDLENBQUE7VUFBQSxPQUFBK0YsT0FBTyxDQUFDQyxPQUFPLENBQUNyRSxNQUFNLEtBQUtBLE1BQU07UUFBRCxDQUNsQyxDQUFDO01BQ0gsQ0FBQztRQUFBNUIsYUFBQSxHQUFBK0MsQ0FBQTtNQUFBO01BQUEvQyxhQUFBLEdBQUFDLENBQUE7TUFDRCxPQUFPLElBQUksQ0FBQ0ksaUJBQWlCO0lBQy9CO0VBQUM7SUFBQVUsR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQWtGLHNCQUFzQkEsQ0FBQ3RFLE1BQWMsRUFPbkM7TUFBQTVCLGFBQUEsR0FBQVksQ0FBQTtNQUNBLElBQU11RixRQUFRLElBQUFuRyxhQUFBLEdBQUFDLENBQUEsUUFBRyxDQUFBRCxhQUFBLEdBQUErQyxDQUFBLGVBQUksQ0FBQ2hELFlBQVksQ0FBQ2tELEdBQUcsQ0FBQ3JCLE1BQU0sQ0FBQyxNQUFBNUIsYUFBQSxHQUFBK0MsQ0FBQSxXQUFJLEVBQUU7TUFDcEQsSUFBTWlDLFFBQVEsSUFBQWhGLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLENBQUFELGFBQUEsR0FBQStDLENBQUEsZUFBSSxDQUFDNUMsZ0JBQWdCLENBQUM4QyxHQUFHLENBQUNyQixNQUFNLENBQUMsTUFBQTVCLGFBQUEsR0FBQStDLENBQUEsV0FBSSxFQUFFO01BQ3hELElBQU1xRCxPQUFPLElBQUFwRyxhQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNHLFlBQVksQ0FBQzZDLEdBQUcsQ0FBQ3JCLE1BQU0sQ0FBQztNQUc3QyxJQUFNeUUsWUFBWSxJQUFBckcsYUFBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSUMsR0FBRyxDQUFpQixDQUFDO01BQUNGLGFBQUEsR0FBQUMsQ0FBQTtNQUMvQ2tHLFFBQVEsQ0FBQ0csT0FBTyxDQUFDLFVBQUF0RSxPQUFPLEVBQUk7UUFBQWhDLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFDMUIrQixPQUFPLENBQUNJLFdBQVcsQ0FBQ2tFLE9BQU8sQ0FBQyxVQUFBQyxJQUFJLEVBQUk7VUFBQXZHLGFBQUEsR0FBQVksQ0FBQTtVQUFBWixhQUFBLEdBQUFDLENBQUE7VUFDbENvRyxZQUFZLENBQUNyRCxHQUFHLENBQUN1RCxJQUFJLENBQUNuRCxNQUFNLEVBQUUsQ0FBQyxDQUFBcEQsYUFBQSxHQUFBK0MsQ0FBQSxXQUFBc0QsWUFBWSxDQUFDcEQsR0FBRyxDQUFDc0QsSUFBSSxDQUFDbkQsTUFBTSxDQUFDLE1BQUFwRCxhQUFBLEdBQUErQyxDQUFBLFdBQUksQ0FBQyxLQUFJLENBQUMsQ0FBQztRQUN6RSxDQUFDLENBQUM7TUFDSixDQUFDLENBQUM7TUFFRixJQUFNeUQsZUFBZSxJQUFBeEcsYUFBQSxHQUFBQyxDQUFBLFFBQUd3RyxLQUFLLENBQUNDLElBQUksQ0FBQ0wsWUFBWSxDQUFDTSxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQ3ZEQyxHQUFHLENBQUMsVUFBQUMsSUFBQSxFQUFzQjtRQUFBLElBQUFDLEtBQUEsR0FBQUMsY0FBQSxDQUFBRixJQUFBO1VBQXBCekQsTUFBTSxHQUFBMEQsS0FBQTtVQUFFRSxLQUFLLEdBQUFGLEtBQUE7UUFBQTlHLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFBTztVQUFFbUQsTUFBTSxFQUFOQSxNQUFNO1VBQUU0RCxLQUFLLEVBQUxBO1FBQU0sQ0FBQztNQUFELENBQUUsQ0FBQyxDQUM3Q0MsSUFBSSxDQUFDLFVBQUNDLENBQUMsRUFBRW5FLENBQUMsRUFBSztRQUFBL0MsYUFBQSxHQUFBWSxDQUFBO1FBQUFaLGFBQUEsR0FBQUMsQ0FBQTtRQUFBLE9BQUE4QyxDQUFDLENBQUNpRSxLQUFLLEdBQUdFLENBQUMsQ0FBQ0YsS0FBSztNQUFELENBQUMsQ0FBQyxDQUNqQ0csS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7TUFHZCxJQUFNQyxpQkFBaUIsSUFBQXBILGFBQUEsR0FBQUMsQ0FBQSxRQUFHa0csUUFBUSxDQUFDSixNQUFNLENBQUMsVUFBQTlGLENBQUMsRUFBSTtRQUFBRCxhQUFBLEdBQUFZLENBQUE7UUFBQVosYUFBQSxHQUFBQyxDQUFBO1FBQUEsT0FBQUEsQ0FBQyxDQUFDd0QsUUFBUTtNQUFELENBQUMsQ0FBQztNQUMxRCxJQUFNNEQsc0JBQXNCLElBQUFySCxhQUFBLEdBQUFDLENBQUEsUUFBR21ILGlCQUFpQixDQUFDdkQsTUFBTSxHQUFHLENBQUMsSUFBQTdELGFBQUEsR0FBQStDLENBQUEsV0FDdkRxRSxpQkFBaUIsQ0FBQ0UsTUFBTSxDQUFDLFVBQUNDLEdBQUcsRUFBRXRILENBQUMsRUFBSztRQUFBRCxhQUFBLEdBQUFZLENBQUE7UUFBQVosYUFBQSxHQUFBQyxDQUFBO1FBQUEsT0FBQXNILEdBQUcsSUFBSSxDQUFBdkgsYUFBQSxHQUFBK0MsQ0FBQSxXQUFBOUMsQ0FBQyxDQUFDd0QsUUFBUSxNQUFBekQsYUFBQSxHQUFBK0MsQ0FBQSxXQUFJLENBQUMsRUFBQztNQUFELENBQUMsRUFBRSxDQUFDLENBQUMsR0FBR3FFLGlCQUFpQixDQUFDdkQsTUFBTSxLQUFBN0QsYUFBQSxHQUFBK0MsQ0FBQSxXQUMzRixDQUFDO01BR0wsSUFBTXlFLGdCQUFnQixJQUFBeEgsYUFBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSUMsR0FBRyxDQUFpQixDQUFDO01BQUNGLGFBQUEsR0FBQUMsQ0FBQTtNQUNuRGtHLFFBQVEsQ0FBQ0csT0FBTyxDQUFDLFVBQUF0RSxPQUFPLEVBQUk7UUFBQWhDLGFBQUEsR0FBQVksQ0FBQTtRQUMxQixJQUFNNkcsSUFBSSxJQUFBekgsYUFBQSxHQUFBQyxDQUFBLFFBQUcrQixPQUFPLENBQUNILE9BQU8sQ0FBQzZGLFNBQVM7UUFBQzFILGFBQUEsR0FBQUMsQ0FBQTtRQUN2Q3VILGdCQUFnQixDQUFDeEUsR0FBRyxDQUFDeUUsSUFBSSxFQUFFLENBQUMsQ0FBQXpILGFBQUEsR0FBQStDLENBQUEsV0FBQXlFLGdCQUFnQixDQUFDdkUsR0FBRyxDQUFDd0UsSUFBSSxDQUFDLE1BQUF6SCxhQUFBLEdBQUErQyxDQUFBLFdBQUksQ0FBQyxLQUFJLENBQUMsQ0FBQztNQUNuRSxDQUFDLENBQUM7TUFFRixJQUFNNEUsY0FBYyxJQUFBM0gsYUFBQSxHQUFBQyxDQUFBLFFBQUd3RyxLQUFLLENBQUNDLElBQUksQ0FBQ2MsZ0JBQWdCLENBQUNiLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FDMURNLElBQUksQ0FBQyxVQUFDQyxDQUFDLEVBQUVuRSxDQUFDLEVBQUs7UUFBQS9DLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFBQSxPQUFBOEMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxHQUFHbUUsQ0FBQyxDQUFDLENBQUMsQ0FBQztNQUFELENBQUMsQ0FBQyxDQUMzQkMsS0FBSyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FDWFAsR0FBRyxDQUFDLFVBQUFnQixLQUFBLEVBQVlIO1FBQUFBLEdBQUEsQ0FBQUEsS0FBQSxDQUFBQSxDQUFBLENBQUFBLHFCQUFBLENBQUFBO1VBQVZBLElBQUksR0FBQUksS0FBQTtRQUFBN0gsYUFBQSxHQUFBWSxDQUFBO1FBQUFaLGFBQUEsR0FBQUMsQ0FBQTtRQUFNd0gsTUFBQSxDQUFBQSxJQUFJO01BQUQsQ0FBQyxDQUFDO01BQUN6SCxhQUFBLEdBQUFDLENBQUE7TUFFekIsT0FBTztRQUNMNkgsYUFBYSxFQUFFM0IsUUFBUSxDQUFDdEMsTUFBTTtRQUM5QndELHNCQUFzQixFQUF0QkEsc0JBQXNCO1FBQ3RCYixlQUFlLEVBQWZBLGVBQWU7UUFDZm1CLGNBQWMsRUFBZEEsY0FBYztRQUNkeEgsZ0JBQWdCLEVBQUU2RSxRQUFRLENBQUNuQixNQUFNO1FBQ2pDdUMsT0FBTyxFQUFFLENBQUFwRyxhQUFBLEdBQUErQyxDQUFBLFdBQUFxRCxPQUFPLG9CQUFQQSxPQUFPLENBQUUyQixJQUFJLE1BQUEvSCxhQUFBLEdBQUErQyxDQUFBLFdBQUksU0FBUztNQUNyQyxDQUFDO0lBQ0g7RUFBQztJQUFBaEMsR0FBQTtJQUFBQyxLQUFBLEVBSUQsU0FBUXNDLFdBQVdBLENBQUMxQixNQUFjLEVBQUVFLFNBQWlCLEVBQXNCO01BQUE5QixhQUFBLEdBQUFZLENBQUE7TUFDekUsSUFBTXVGLFFBQVEsSUFBQW5HLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLENBQUFELGFBQUEsR0FBQStDLENBQUEsZUFBSSxDQUFDaEQsWUFBWSxDQUFDa0QsR0FBRyxDQUFDckIsTUFBTSxDQUFDLE1BQUE1QixhQUFBLEdBQUErQyxDQUFBLFdBQUksRUFBRTtNQUFDL0MsYUFBQSxHQUFBQyxDQUFBO01BQ3JELE9BQU8sQ0FBQUQsYUFBQSxHQUFBK0MsQ0FBQSxXQUFBb0QsUUFBUSxDQUFDNkIsSUFBSSxDQUFDLFVBQUEvSCxDQUFDLEVBQUk7UUFBQUQsYUFBQSxHQUFBWSxDQUFBO1FBQUFaLGFBQUEsR0FBQUMsQ0FBQTtRQUFBLE9BQUFBLENBQUMsQ0FBQzZCLFNBQVMsS0FBS0EsU0FBUztNQUFELENBQUMsQ0FBQyxNQUFBOUIsYUFBQSxHQUFBK0MsQ0FBQSxXQUFJLElBQUk7SUFDOUQ7RUFBQztJQUFBaEMsR0FBQTtJQUFBQyxLQUFBLEVBRUQsU0FBUWUsaUJBQWlCQSxDQUFDSCxNQUFjLEVBQVU7TUFBQTVCLGFBQUEsR0FBQVksQ0FBQTtNQUFBWixhQUFBLEdBQUFDLENBQUE7TUFDaEQsT0FBTyxXQUFXMkIsTUFBTSxJQUFJTSxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLElBQUk4RixJQUFJLENBQUNDLE1BQU0sQ0FBQyxDQUFDLENBQUNDLFFBQVEsQ0FBQyxFQUFFLENBQUMsQ0FBQ0MsTUFBTSxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRTtJQUNyRjtFQUFDO0lBQUFySCxHQUFBO0lBQUFDLEtBQUEsRUFFRCxTQUFRNkQsdUJBQXVCQSxDQUFDN0MsT0FBb0IsRUFBUTtNQUFBaEMsYUFBQSxHQUFBWSxDQUFBO01BRTFELElBQU15SCxTQUFTLElBQUFySSxhQUFBLEdBQUFDLENBQUEsUUFBRytCLE9BQU8sQ0FBQ0ksV0FBVyxDQUFDd0UsR0FBRyxDQUFDLFVBQUEwQixDQUFDLEVBQUk7UUFBQXRJLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFBQSxPQUFBcUksQ0FBQyxDQUFDakYsUUFBUTtNQUFELENBQUMsQ0FBQztNQUFDckQsYUFBQSxHQUFBQyxDQUFBO01BQzNEK0IsT0FBTyxDQUFDTSxXQUFXLENBQUNDLGVBQWUsR0FBRzhGLFNBQVMsQ0FBQ3hFLE1BQU0sR0FBRyxDQUFDLElBQUE3RCxhQUFBLEdBQUErQyxDQUFBLFdBQ3REc0YsU0FBUyxDQUFDZixNQUFNLENBQUMsVUFBQ0MsR0FBRyxFQUFFRSxJQUFJLEVBQUs7UUFBQXpILGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFBQSxPQUFBc0gsR0FBRyxHQUFHRSxJQUFJO01BQUQsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHWSxTQUFTLENBQUN4RSxNQUFNLEtBQUE3RCxhQUFBLEdBQUErQyxDQUFBLFdBQ2pFLENBQUM7TUFBQy9DLGFBQUEsR0FBQUMsQ0FBQTtNQUdOK0IsT0FBTyxDQUFDTSxXQUFXLENBQUNNLGVBQWUsR0FBR1osT0FBTyxDQUFDSSxXQUFXLENBQUN5QixNQUFNLEdBQUcsQ0FBQztNQUFDN0QsYUFBQSxHQUFBQyxDQUFBO01BR3JFK0IsT0FBTyxDQUFDTSxXQUFXLENBQUNPLFlBQVksR0FBR29GLElBQUksQ0FBQ0MsTUFBTSxDQUFDLENBQUMsR0FBRyxHQUFHLEdBQUcsR0FBRztJQUM5RDtFQUFDO0lBQUFuSCxHQUFBO0lBQUFDLEtBQUEsRUFFRCxTQUFRa0UseUJBQXlCQSxDQUFDaUIsUUFBdUIsRUFBcUI7TUFBQSxJQUFBb0MsS0FBQTtNQUFBdkksYUFBQSxHQUFBWSxDQUFBO01BQzVFLElBQU1vRSxRQUEyQixJQUFBaEYsYUFBQSxHQUFBQyxDQUFBLFNBQUcsRUFBRTtNQUd0QyxJQUFNdUksU0FBUyxJQUFBeEksYUFBQSxHQUFBQyxDQUFBLFNBQUcsSUFBSUMsR0FBRyxDQUFpQixDQUFDO01BQUNGLGFBQUEsR0FBQUMsQ0FBQTtNQUU1Q2tHLFFBQVEsQ0FBQ0csT0FBTyxDQUFDLFVBQUF0RSxPQUFPLEVBQUk7UUFBQWhDLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFDMUIsS0FBSyxJQUFJd0ksQ0FBQyxJQUFBekksYUFBQSxHQUFBQyxDQUFBLFNBQUcsQ0FBQyxHQUFFd0ksQ0FBQyxHQUFHekcsT0FBTyxDQUFDSSxXQUFXLENBQUN5QixNQUFNLEdBQUcsQ0FBQyxFQUFFNEUsQ0FBQyxFQUFFLEVBQUU7VUFDdkQsSUFBTUMsT0FBTyxJQUFBMUksYUFBQSxHQUFBQyxDQUFBLFNBQUcrQixPQUFPLENBQUNJLFdBQVcsQ0FBQ3FHLENBQUMsQ0FBQyxDQUFDckYsTUFBTTtVQUM3QyxJQUFNdUYsSUFBSSxJQUFBM0ksYUFBQSxHQUFBQyxDQUFBLFNBQUcrQixPQUFPLENBQUNJLFdBQVcsQ0FBQ3FHLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQ3JGLE1BQU07VUFDOUMsSUFBTXdGLFFBQVEsSUFBQTVJLGFBQUEsR0FBQUMsQ0FBQSxTQUFHLEdBQUd5SSxPQUFPLEtBQUtDLElBQUksRUFBRTtVQUFDM0ksYUFBQSxHQUFBQyxDQUFBO1VBQ3ZDdUksU0FBUyxDQUFDeEYsR0FBRyxDQUFDNEYsUUFBUSxFQUFFLENBQUMsQ0FBQTVJLGFBQUEsR0FBQStDLENBQUEsV0FBQXlGLFNBQVMsQ0FBQ3ZGLEdBQUcsQ0FBQzJGLFFBQVEsQ0FBQyxNQUFBNUksYUFBQSxHQUFBK0MsQ0FBQSxXQUFJLENBQUMsS0FBSSxDQUFDLENBQUM7UUFDN0Q7TUFDRixDQUFDLENBQUM7TUFBQy9DLGFBQUEsR0FBQUMsQ0FBQTtNQUdIdUksU0FBUyxDQUFDbEMsT0FBTyxDQUFDLFVBQUNVLEtBQUssRUFBRTRCLFFBQVEsRUFBSztRQUFBNUksYUFBQSxHQUFBWSxDQUFBO1FBQUFaLGFBQUEsR0FBQUMsQ0FBQTtRQUNyQyxJQUFJK0csS0FBSyxJQUFJdUIsS0FBSSxDQUFDakksZUFBZSxDQUFDRSxtQkFBbUIsRUFBRTtVQUFBUixhQUFBLEdBQUErQyxDQUFBO1VBQUEvQyxhQUFBLEdBQUFDLENBQUE7VUFDckQrRSxRQUFRLENBQUM5QixJQUFJLENBQUM7WUFDWjJGLFNBQVMsRUFBRSxPQUFPRCxRQUFRLENBQUNFLE9BQU8sQ0FBQyxJQUFJLEVBQUUsTUFBTSxDQUFDLEVBQUU7WUFDbERmLElBQUksRUFBRSxlQUFlYSxRQUFRLEVBQUU7WUFDL0JHLFdBQVcsRUFBRSw2QkFBNkJILFFBQVEsRUFBRTtZQUNwREksU0FBUyxFQUFFaEMsS0FBSztZQUNoQmlDLFVBQVUsRUFBRWhCLElBQUksQ0FBQ2lCLEdBQUcsQ0FBQ2xDLEtBQUssR0FBR2IsUUFBUSxDQUFDdEMsTUFBTSxFQUFFLENBQUMsQ0FBQztZQUNoRHNGLFFBQVEsRUFBRSxDQUFDUCxRQUFRLENBQUNRLEtBQUssQ0FBQyxJQUFJLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztZQUNuQ0MsUUFBUSxFQUFFLENBQUNULFFBQVEsQ0FBQ1EsS0FBSyxDQUFDLElBQUksQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1lBQ25DRSxXQUFXLEVBQUUsWUFBWTtZQUN6QkMsV0FBVyxFQUFFO2NBQ1g1QixjQUFjLEVBQUUsRUFBRTtjQUNsQjZCLGVBQWUsRUFBRSxDQUFDO2NBQ2xCUixTQUFTLEVBQUU7WUFDYjtVQUNGLENBQUMsQ0FBQztRQUNKLENBQUM7VUFBQWhKLGFBQUEsR0FBQStDLENBQUE7UUFBQTtNQUNILENBQUMsQ0FBQztNQUFDL0MsYUFBQSxHQUFBQyxDQUFBO01BRUgsT0FBTytFLFFBQVE7SUFDakI7RUFBQztJQUFBakUsR0FBQTtJQUFBQyxLQUFBLEVBRUQsU0FBUXFFLG1CQUFtQkEsQ0FBQ2MsUUFBdUIsRUFBcUI7TUFBQSxJQUFBc0QsTUFBQTtNQUFBekosYUFBQSxHQUFBWSxDQUFBO01BQ3RFLElBQU1vRSxRQUEyQixJQUFBaEYsYUFBQSxHQUFBQyxDQUFBLFNBQUcsRUFBRTtNQUd0QyxJQUFNeUosU0FBUyxJQUFBMUosYUFBQSxHQUFBQyxDQUFBLFNBQUcsSUFBSUMsR0FBRyxDQUFpQixDQUFDO01BQUNGLGFBQUEsR0FBQUMsQ0FBQTtNQUM1Q2tHLFFBQVEsQ0FBQ0csT0FBTyxDQUFDLFVBQUF0RSxPQUFPLEVBQUk7UUFBQWhDLGFBQUEsR0FBQVksQ0FBQTtRQUMxQixJQUFNNkcsSUFBSSxJQUFBekgsYUFBQSxHQUFBQyxDQUFBLFNBQUcrQixPQUFPLENBQUNILE9BQU8sQ0FBQzZGLFNBQVM7UUFBQzFILGFBQUEsR0FBQUMsQ0FBQTtRQUN2Q3lKLFNBQVMsQ0FBQzFHLEdBQUcsQ0FBQ3lFLElBQUksRUFBRSxDQUFDLENBQUF6SCxhQUFBLEdBQUErQyxDQUFBLFdBQUEyRyxTQUFTLENBQUN6RyxHQUFHLENBQUN3RSxJQUFJLENBQUMsTUFBQXpILGFBQUEsR0FBQStDLENBQUEsV0FBSSxDQUFDLEtBQUksQ0FBQyxDQUFDO01BQ3JELENBQUMsQ0FBQztNQUFDL0MsYUFBQSxHQUFBQyxDQUFBO01BR0h5SixTQUFTLENBQUNwRCxPQUFPLENBQUMsVUFBQ1UsS0FBSyxFQUFFUyxJQUFJLEVBQUs7UUFBQXpILGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFDakMsSUFBSStHLEtBQUssSUFBSXlDLE1BQUksQ0FBQ25KLGVBQWUsQ0FBQ0UsbUJBQW1CLEVBQUU7VUFBQVIsYUFBQSxHQUFBK0MsQ0FBQTtVQUFBL0MsYUFBQSxHQUFBQyxDQUFBO1VBQ3JEK0UsUUFBUSxDQUFDOUIsSUFBSSxDQUFDO1lBQ1oyRixTQUFTLEVBQUUsUUFBUXBCLElBQUksRUFBRTtZQUN6Qk0sSUFBSSxFQUFFLEdBQUdOLElBQUksQ0FBQ2tDLE1BQU0sQ0FBQyxDQUFDLENBQUMsQ0FBQ0MsV0FBVyxDQUFDLENBQUMsR0FBR25DLElBQUksQ0FBQ04sS0FBSyxDQUFDLENBQUMsQ0FBQyxRQUFRO1lBQzdENEIsV0FBVyxFQUFFLHlCQUF5QnRCLElBQUksRUFBRTtZQUM1Q3VCLFNBQVMsRUFBRWhDLEtBQUs7WUFDaEJpQyxVQUFVLEVBQUVqQyxLQUFLLEdBQUdiLFFBQVEsQ0FBQ3RDLE1BQU07WUFDbkNzRixRQUFRLEVBQUUsQ0FBQzFCLElBQUksQ0FBQztZQUNoQjRCLFFBQVEsRUFBRSxDQUFDLFdBQVcsQ0FBQztZQUN2QkMsV0FBVyxFQUFFLFVBQVU7WUFDdkJDLFdBQVcsRUFBRTtjQUNYNUIsY0FBYyxFQUFFLENBQUNGLElBQUksQ0FBQztjQUN0QitCLGVBQWUsRUFBRSxDQUFDO2NBQ2xCUixTQUFTLEVBQUU7WUFDYjtVQUNGLENBQUMsQ0FBQztRQUNKLENBQUM7VUFBQWhKLGFBQUEsR0FBQStDLENBQUE7UUFBQTtNQUNILENBQUMsQ0FBQztNQUFDL0MsYUFBQSxHQUFBQyxDQUFBO01BRUgsT0FBTytFLFFBQVE7SUFDakI7RUFBQztJQUFBakUsR0FBQTtJQUFBQyxLQUFBLEVBRUQsU0FBUXVFLG1CQUFtQkEsQ0FBQ1ksUUFBdUIsRUFBcUI7TUFBQSxJQUFBMEQsTUFBQTtNQUFBN0osYUFBQSxHQUFBWSxDQUFBO01BQ3RFLElBQU1vRSxRQUEyQixJQUFBaEYsYUFBQSxHQUFBQyxDQUFBLFNBQUcsRUFBRTtNQUd0QyxJQUFNNkosWUFBWSxJQUFBOUosYUFBQSxHQUFBQyxDQUFBLFNBQUcsSUFBSUMsR0FBRyxDQUFpQixDQUFDO01BQUNGLGFBQUEsR0FBQUMsQ0FBQTtNQUMvQ2tHLFFBQVEsQ0FBQ0csT0FBTyxDQUFDLFVBQUF0RSxPQUFPLEVBQUk7UUFBQWhDLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFDMUIrQixPQUFPLENBQUNLLFlBQVksQ0FBQ2lFLE9BQU8sQ0FBQyxVQUFBcEMsV0FBVyxFQUFJO1VBQUFsRSxhQUFBLEdBQUFZLENBQUE7VUFBQVosYUFBQSxHQUFBQyxDQUFBO1VBQzFDNkosWUFBWSxDQUFDOUcsR0FBRyxDQUFDa0IsV0FBVyxDQUFDRixNQUFNLEVBQUUsQ0FBQyxDQUFBaEUsYUFBQSxHQUFBK0MsQ0FBQSxXQUFBK0csWUFBWSxDQUFDN0csR0FBRyxDQUFDaUIsV0FBVyxDQUFDRixNQUFNLENBQUMsTUFBQWhFLGFBQUEsR0FBQStDLENBQUEsV0FBSSxDQUFDLEtBQUksQ0FBQyxDQUFDO1FBQ3ZGLENBQUMsQ0FBQztNQUNKLENBQUMsQ0FBQztNQUFDL0MsYUFBQSxHQUFBQyxDQUFBO01BR0g2SixZQUFZLENBQUN4RCxPQUFPLENBQUMsVUFBQ1UsS0FBSyxFQUFFK0MsT0FBTyxFQUFLO1FBQUEvSixhQUFBLEdBQUFZLENBQUE7UUFBQVosYUFBQSxHQUFBQyxDQUFBO1FBQ3ZDLElBQUkrRyxLQUFLLElBQUk2QyxNQUFJLENBQUN2SixlQUFlLENBQUNFLG1CQUFtQixFQUFFO1VBQUFSLGFBQUEsR0FBQStDLENBQUE7VUFBQS9DLGFBQUEsR0FBQUMsQ0FBQTtVQUNyRCtFLFFBQVEsQ0FBQzlCLElBQUksQ0FBQztZQUNaMkYsU0FBUyxFQUFFLFdBQVdrQixPQUFPLEVBQUU7WUFDL0JoQyxJQUFJLEVBQUUsWUFBWWdDLE9BQU8sRUFBRTtZQUMzQmhCLFdBQVcsRUFBRSx3QkFBd0JnQixPQUFPLEVBQUU7WUFDOUNmLFNBQVMsRUFBRWhDLEtBQUs7WUFDaEJpQyxVQUFVLEVBQUVoQixJQUFJLENBQUNpQixHQUFHLENBQUNsQyxLQUFLLElBQUliLFFBQVEsQ0FBQ3RDLE1BQU0sR0FBRyxFQUFFLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDdkRzRixRQUFRLEVBQUUsQ0FBQyxXQUFXLENBQUM7WUFDdkJFLFFBQVEsRUFBRSxDQUFDVSxPQUFPLENBQUM7WUFDbkJULFdBQVcsRUFBRSxTQUFTO1lBQ3RCQyxXQUFXLEVBQUU7Y0FDWDVCLGNBQWMsRUFBRSxFQUFFO2NBQ2xCNkIsZUFBZSxFQUFFLENBQUM7Y0FDbEJSLFNBQVMsRUFBRTtZQUNiO1VBQ0YsQ0FBQyxDQUFDO1FBQ0osQ0FBQztVQUFBaEosYUFBQSxHQUFBK0MsQ0FBQTtRQUFBO01BQ0gsQ0FBQyxDQUFDO01BQUMvQyxhQUFBLEdBQUFDLENBQUE7TUFFSCxPQUFPK0UsUUFBUTtJQUNqQjtFQUFDO0lBQUFqRSxHQUFBO0lBQUFDLEtBQUEsRUFFRCxTQUFReUUsMEJBQTBCQSxDQUFDVSxRQUF1QixFQUFxQjtNQUFBbkcsYUFBQSxHQUFBWSxDQUFBO01BQzdFLElBQU1vRSxRQUEyQixJQUFBaEYsYUFBQSxHQUFBQyxDQUFBLFNBQUcsRUFBRTtNQUd0QyxJQUFNK0osV0FBVyxJQUFBaEssYUFBQSxHQUFBQyxDQUFBLFNBQUdrRyxRQUFRLENBQUNtQixNQUFNLENBQUMsVUFBQ0MsR0FBRyxFQUFFdEgsQ0FBQyxFQUFLO1FBQUFELGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFBQSxPQUFBc0gsR0FBRyxHQUFHdEgsQ0FBQyxDQUFDcUMsV0FBVyxDQUFDQyxlQUFlO01BQUQsQ0FBQyxFQUFFLENBQUMsQ0FBQyxHQUFHNEQsUUFBUSxDQUFDdEMsTUFBTTtNQUFDN0QsYUFBQSxHQUFBQyxDQUFBO01BRTFHLElBQUkrSixXQUFXLEdBQUcsSUFBSSxFQUFFO1FBQUFoSyxhQUFBLEdBQUErQyxDQUFBO1FBQUEvQyxhQUFBLEdBQUFDLENBQUE7UUFDdEIrRSxRQUFRLENBQUM5QixJQUFJLENBQUM7VUFDWjJGLFNBQVMsRUFBRSxtQkFBbUI7VUFDOUJkLElBQUksRUFBRSwwQkFBMEI7VUFDaENnQixXQUFXLEVBQUUscUNBQXFDO1VBQ2xEQyxTQUFTLEVBQUU3QyxRQUFRLENBQUN0QyxNQUFNO1VBQzFCb0YsVUFBVSxFQUFFLEdBQUc7VUFDZkUsUUFBUSxFQUFFLENBQUMsY0FBYyxFQUFFLFlBQVksQ0FBQztVQUN4Q0UsUUFBUSxFQUFFLENBQUMsaUNBQWlDLENBQUM7VUFDN0NDLFdBQVcsRUFBRSxhQUFhO1VBQzFCQyxXQUFXLEVBQUU7WUFDWDVCLGNBQWMsRUFBRSxFQUFFO1lBQ2xCNkIsZUFBZSxFQUFFLENBQUM7WUFDbEJSLFNBQVMsRUFBRTtVQUNiO1FBQ0YsQ0FBQyxDQUFDO01BQ0osQ0FBQztRQUFBaEosYUFBQSxHQUFBK0MsQ0FBQTtNQUFBO01BQUEvQyxhQUFBLEdBQUFDLENBQUE7TUFFRCxPQUFPK0UsUUFBUTtJQUNqQjtFQUFDO0lBQUFqRSxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBaUosa0JBQUEsR0FBQS9JLGlCQUFBLENBRUQsV0FBZ0NVLE1BQWMsRUFBRW9ELFFBQTJCLEVBQWlCO1FBQUFoRixhQUFBLEdBQUFZLENBQUE7UUFFMUYsSUFBTXdGLE9BQU8sSUFBQXBHLGFBQUEsR0FBQUMsQ0FBQSxTQUFHLElBQUksQ0FBQ2lLLG9CQUFvQixDQUFDbEYsUUFBUSxDQUFDO1FBQUNoRixhQUFBLEdBQUFDLENBQUE7UUFDcEQsSUFBSSxDQUFDRyxZQUFZLENBQUM0QyxHQUFHLENBQUNwQixNQUFNLEVBQUV3RSxPQUFPLENBQUM7UUFBQ3BHLGFBQUEsR0FBQUMsQ0FBQTtRQUd2QyxNQUFNLElBQUksQ0FBQ2tLLHlCQUF5QixDQUFDdkksTUFBTSxFQUFFd0UsT0FBTyxDQUFDO01BQ3ZELENBQUM7TUFBQSxTQVBhVixpQkFBaUJBLENBQUEwRSxHQUFBLEVBQUFDLEdBQUE7UUFBQSxPQUFBSixrQkFBQSxDQUFBeEksS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUFqQmdFLGlCQUFpQjtJQUFBO0VBQUE7SUFBQTNFLEdBQUE7SUFBQUMsS0FBQSxFQVMvQixTQUFRa0osb0JBQW9CQSxDQUFDbEYsUUFBMkIsRUFBZTtNQUFBaEYsYUFBQSxHQUFBWSxDQUFBO01BRXJFLElBQU1xRSxrQkFBa0IsSUFBQWpGLGFBQUEsR0FBQUMsQ0FBQSxTQUFHK0UsUUFBUSxDQUFDZSxNQUFNLENBQUMsVUFBQXVFLENBQUMsRUFBSTtRQUFBdEssYUFBQSxHQUFBWSxDQUFBO1FBQUFaLGFBQUEsR0FBQUMsQ0FBQTtRQUFBLE9BQUFxSyxDQUFDLENBQUNoQixXQUFXLEtBQUssWUFBWTtNQUFELENBQUMsQ0FBQyxDQUFDekYsTUFBTTtNQUN0RixJQUFNeUIsZUFBZSxJQUFBdEYsYUFBQSxHQUFBQyxDQUFBLFNBQUcrRSxRQUFRLENBQUNlLE1BQU0sQ0FBQyxVQUFBdUUsQ0FBQyxFQUFJO1FBQUF0SyxhQUFBLEdBQUFZLENBQUE7UUFBQVosYUFBQSxHQUFBQyxDQUFBO1FBQUEsT0FBQXFLLENBQUMsQ0FBQ2hCLFdBQVcsS0FBSyxTQUFTO01BQUQsQ0FBQyxDQUFDLENBQUN6RixNQUFNO01BQUM3RCxhQUFBLEdBQUFDLENBQUE7TUFFakYsSUFBSXFGLGVBQWUsR0FBRyxDQUFDLEVBQUU7UUFBQXRGLGFBQUEsR0FBQStDLENBQUE7UUFBQS9DLGFBQUEsR0FBQUMsQ0FBQTtRQUN2QixPQUFPO1VBQ0xzSyxTQUFTLEVBQUUsWUFBWTtVQUN2QnhDLElBQUksRUFBRSxZQUFZO1VBQ2xCZ0IsV0FBVyxFQUFFLDZDQUE2QztVQUMxRHlCLGVBQWUsRUFBRTtZQUNmQyxVQUFVLEVBQUUsVUFBVTtZQUN0QkMsYUFBYSxFQUFFLFdBQVc7WUFDMUJaLFlBQVksRUFBRSxDQUFDLENBQUM7WUFDaEJhLGVBQWUsRUFBRTtjQUNmQyxlQUFlLEVBQUUsT0FBTztjQUN4QmpELGNBQWMsRUFBRSxDQUFDLFNBQVMsQ0FBQztjQUMzQnFCLFNBQVMsRUFBRTtZQUNiO1VBQ0YsQ0FBQztVQUNENkIsYUFBYSxFQUFFO1lBQ2JDLGFBQWEsRUFBRSxZQUFZO1lBQzNCQyxrQkFBa0IsRUFBRSxhQUFhO1lBQ2pDQyxZQUFZLEVBQUUsVUFBVTtZQUN4QkMsY0FBYyxFQUFFLENBQUMsZUFBZSxFQUFFLFdBQVc7VUFDL0M7UUFDRixDQUFDO01BQ0gsQ0FBQyxNQUFNO1FBQUFqTCxhQUFBLEdBQUErQyxDQUFBO1FBQUEvQyxhQUFBLEdBQUFDLENBQUE7UUFDTCxPQUFPO1VBQ0xzSyxTQUFTLEVBQUUsYUFBYTtVQUN4QnhDLElBQUksRUFBRSxhQUFhO1VBQ25CZ0IsV0FBVyxFQUFFLGlDQUFpQztVQUM5Q3lCLGVBQWUsRUFBRTtZQUNmQyxVQUFVLEVBQUUsVUFBVTtZQUN0QkMsYUFBYSxFQUFFLFFBQVE7WUFDdkJaLFlBQVksRUFBRSxDQUFDLENBQUM7WUFDaEJhLGVBQWUsRUFBRTtjQUNmQyxlQUFlLEVBQUUsTUFBTTtjQUN2QmpELGNBQWMsRUFBRSxDQUFDLFdBQVcsQ0FBQztjQUM3QnFCLFNBQVMsRUFBRTtZQUNiO1VBQ0YsQ0FBQztVQUNENkIsYUFBYSxFQUFFO1lBQ2JDLGFBQWEsRUFBRSxjQUFjO1lBQzdCQyxrQkFBa0IsRUFBRSxVQUFVO1lBQzlCQyxZQUFZLEVBQUUsVUFBVTtZQUN4QkMsY0FBYyxFQUFFLENBQUMsWUFBWTtVQUMvQjtRQUNGLENBQUM7TUFDSDtJQUNGO0VBQUM7SUFBQWxLLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFrSywwQkFBQSxHQUFBaEssaUJBQUEsQ0FFRCxXQUF3Q1UsTUFBYyxFQUFFd0UsT0FBb0IsRUFBaUI7UUFBQXBHLGFBQUEsR0FBQVksQ0FBQTtRQUUzRixJQUFNdUssV0FBVyxJQUFBbkwsYUFBQSxHQUFBQyxDQUFBLFNBQUdtRyxPQUFPLENBQUN5RSxhQUFhLENBQUNFLGtCQUFrQjtRQUFDL0ssYUFBQSxHQUFBQyxDQUFBO1FBRTdEcUIsT0FBTyxDQUFDQyxHQUFHLENBQUMsV0FBVzRKLFdBQVcsOEJBQThCL0UsT0FBTyxDQUFDMkIsSUFBSSxFQUFFLENBQUM7TUFDakYsQ0FBQztNQUFBLFNBTGFvQyx5QkFBeUJBLENBQUFpQixHQUFBLEVBQUFDLEdBQUE7UUFBQSxPQUFBSCwwQkFBQSxDQUFBekosS0FBQSxPQUFBQyxTQUFBO01BQUE7TUFBQSxPQUF6QnlJLHlCQUF5QjtJQUFBO0VBQUE7SUFBQXBKLEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFzSyxvQ0FBQSxHQUFBcEssaUJBQUEsQ0FPdkMsV0FDRVUsTUFBYyxFQUNkb0QsUUFBMkIsRUFDWjtRQUFBLElBQUF1RyxNQUFBO1VBQUFDLHFCQUFBO1FBQUF4TCxhQUFBLEdBQUFZLENBQUE7UUFDZixJQUFNNkssUUFBNEIsSUFBQXpMLGFBQUEsR0FBQUMsQ0FBQSxTQUFHLEVBQUU7UUFBQ0QsYUFBQSxHQUFBQyxDQUFBO1FBR3hDK0UsUUFBUSxDQUFDc0IsT0FBTyxDQUFDLFVBQUFvRixPQUFPLEVBQUk7VUFBQTFMLGFBQUEsR0FBQVksQ0FBQTtVQUFBWixhQUFBLEdBQUFDLENBQUE7VUFDMUIsSUFBSXlMLE9BQU8sQ0FBQ3pDLFVBQVUsR0FBR3NDLE1BQUksQ0FBQ2pMLGVBQWUsQ0FBQ0csc0JBQXNCLEVBQUU7WUFBQVQsYUFBQSxHQUFBK0MsQ0FBQTtZQUFBL0MsYUFBQSxHQUFBQyxDQUFBO1lBQ3BFd0wsUUFBUSxDQUFDdkksSUFBSSxDQUFDO2NBQ1phLElBQUksRUFBRSxjQUFjO2NBQ3BCNEgsS0FBSyxFQUFFLGdCQUFnQkQsT0FBTyxDQUFDM0QsSUFBSSxFQUFFO2NBQ3JDZ0IsV0FBVyxFQUFFMkMsT0FBTyxDQUFDM0MsV0FBVztjQUNoQzZDLE1BQU0sRUFBRUYsT0FBTyxDQUFDMUMsU0FBUyxHQUFHLEVBQUUsSUFBQWhKLGFBQUEsR0FBQStDLENBQUEsV0FBRyxNQUFNLEtBQUEvQyxhQUFBLEdBQUErQyxDQUFBLFdBQUcySSxPQUFPLENBQUMxQyxTQUFTLEdBQUcsQ0FBQyxJQUFBaEosYUFBQSxHQUFBK0MsQ0FBQSxXQUFHLFFBQVEsS0FBQS9DLGFBQUEsR0FBQStDLENBQUEsV0FBRyxLQUFLO2NBQ2xGa0csVUFBVSxFQUFFeUMsT0FBTyxDQUFDekMsVUFBVTtjQUM5QjRDLGNBQWMsRUFBRSw0Q0FBNEM7Y0FDNURDLGNBQWMsRUFBRSxrQ0FBa0NKLE9BQU8sQ0FBQ3JDLFFBQVEsQ0FBQzBDLElBQUksQ0FBQyxJQUFJLENBQUMsRUFBRTtjQUMvRTlGLE9BQU8sRUFBRTtnQkFBRXJFLE1BQU0sRUFBRUEsTUFBYTtnQkFBRW9ILFNBQVMsRUFBRTBDLE9BQU8sQ0FBQzFDO2NBQVU7WUFDakUsQ0FBQyxDQUFDO1VBQ0osQ0FBQztZQUFBaEosYUFBQSxHQUFBK0MsQ0FBQTtVQUFBO1FBQ0gsQ0FBQyxDQUFDO1FBQUMvQyxhQUFBLEdBQUFDLENBQUE7UUFFSCxDQUFBdUwscUJBQUEsT0FBSSxDQUFDbkwsaUJBQWlCLEVBQUM2QyxJQUFJLENBQUF6QixLQUFBLENBQUErSixxQkFBQSxFQUFJQyxRQUFRLENBQUM7UUFBQ3pMLGFBQUEsR0FBQUMsQ0FBQTtRQUd6QyxJQUFJLElBQUksQ0FBQ0ksaUJBQWlCLENBQUN3RCxNQUFNLEdBQUcsSUFBSSxFQUFFO1VBQUE3RCxhQUFBLEdBQUErQyxDQUFBO1VBQUEvQyxhQUFBLEdBQUFDLENBQUE7VUFDeEMsSUFBSSxDQUFDSSxpQkFBaUIsR0FBRyxJQUFJLENBQUNBLGlCQUFpQixDQUFDOEcsS0FBSyxDQUFDLENBQUMsSUFBSSxDQUFDO1FBQzlELENBQUM7VUFBQW5ILGFBQUEsR0FBQStDLENBQUE7UUFBQTtNQUNILENBQUM7TUFBQSxTQTVCYTRDLG1DQUFtQ0EsQ0FBQXFHLEdBQUEsRUFBQUMsR0FBQTtRQUFBLE9BQUFYLG9DQUFBLENBQUE3SixLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQW5DaUUsbUNBQW1DO0lBQUE7RUFBQTtJQUFBNUUsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWtMLGtCQUFBLEdBQUFoTCxpQkFBQSxDQThCakQsYUFBaUQ7UUFBQWxCLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFFL0NxQixPQUFPLENBQUNDLEdBQUcsQ0FBQyxvQ0FBb0MsQ0FBQztNQUNuRCxDQUFDO01BQUEsU0FIYUosaUJBQWlCQSxDQUFBO1FBQUEsT0FBQStLLGtCQUFBLENBQUF6SyxLQUFBLE9BQUFDLFNBQUE7TUFBQTtNQUFBLE9BQWpCUCxpQkFBaUI7SUFBQTtFQUFBO0lBQUFKLEdBQUE7SUFBQUMsS0FBQSxFQUsvQixTQUFRSSxxQkFBcUJBLENBQUEsRUFBUztNQUFBLElBQUErSyxNQUFBO01BQUFuTSxhQUFBLEdBQUFZLENBQUE7TUFBQVosYUFBQSxHQUFBQyxDQUFBO01BQ3BDbU0sV0FBVyxDQUFDLFlBQU07UUFBQXBNLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFDaEJrTSxNQUFJLENBQUNFLHVCQUF1QixDQUFDLENBQUM7TUFDaEMsQ0FBQyxFQUFFLElBQUksQ0FBQy9MLGVBQWUsQ0FBQ0ssZ0JBQWdCLENBQUM7SUFDM0M7RUFBQztJQUFBSSxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBc0wsd0JBQUEsR0FBQXBMLGlCQUFBLENBRUQsYUFBdUQ7UUFBQWxCLGFBQUEsR0FBQVksQ0FBQTtRQUFBWixhQUFBLEdBQUFDLENBQUE7UUFDckRxQixPQUFPLENBQUNDLEdBQUcsQ0FBQywwQ0FBMEMsQ0FBQztRQUFDdkIsYUFBQSxHQUFBQyxDQUFBO1FBR3hELFNBQUFzTSxLQUFBLElBQWlDLElBQUksQ0FBQ3hNLFlBQVksQ0FBQzRHLE9BQU8sQ0FBQyxDQUFDLEVBQUU7VUFBQSxJQUFBNkYsS0FBQSxHQUFBekYsY0FBQSxDQUFBd0YsS0FBQTtVQUFBLElBQWxEM0ssTUFBTSxHQUFBNEssS0FBQTtVQUFBLElBQUVyRyxRQUFRLEdBQUFxRyxLQUFBO1VBQzFCLElBQU1DLGNBQWMsSUFBQXpNLGFBQUEsR0FBQUMsQ0FBQSxTQUFHa0csUUFBUSxDQUFDSixNQUFNLENBQUMsVUFBQTlGLENBQUMsRUFDdEM7WUFBQUQsYUFBQSxHQUFBWSxDQUFBO1lBQUFaLGFBQUEsR0FBQUMsQ0FBQTtZQUFBLE9BQUFpQyxJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEdBQUdsQyxDQUFDLENBQUNnQyxTQUFTLEdBQUcsUUFBUTtVQUFELENBQ3BDLENBQUM7VUFBQ2pDLGFBQUEsR0FBQUMsQ0FBQTtVQUVGLElBQUl3TSxjQUFjLENBQUM1SSxNQUFNLEdBQUcsQ0FBQyxFQUFFO1lBQUE3RCxhQUFBLEdBQUErQyxDQUFBO1lBQUEvQyxhQUFBLEdBQUFDLENBQUE7WUFDN0IsTUFBTSxJQUFJLENBQUM2RSxtQkFBbUIsQ0FBQ2xELE1BQU0sQ0FBQztVQUN4QyxDQUFDO1lBQUE1QixhQUFBLEdBQUErQyxDQUFBO1VBQUE7UUFDSDtNQUNGLENBQUM7TUFBQSxTQWJhc0osdUJBQXVCQSxDQUFBO1FBQUEsT0FBQUMsd0JBQUEsQ0FBQTdLLEtBQUEsT0FBQUMsU0FBQTtNQUFBO01BQUEsT0FBdkIySyx1QkFBdUI7SUFBQTtFQUFBO0lBQUF0TCxHQUFBO0lBQUFDLEtBQUEsRUFlckMsU0FBUUssc0JBQXNCQSxDQUFBLEVBQVM7TUFBQXJCLGFBQUEsR0FBQVksQ0FBQTtNQUFBWixhQUFBLEdBQUFDLENBQUE7TUFFckNxQixPQUFPLENBQUNDLEdBQUcsQ0FBQywyQkFBMkIsQ0FBQztJQUMxQztFQUFDO0FBQUE7QUFJSCxPQUFPLElBQU1tTCx5QkFBeUIsSUFBQTFNLGFBQUEsR0FBQUMsQ0FBQSxTQUFHLElBQUlKLHlCQUF5QixDQUFDLENBQUM7QUFDeEUsZUFBZTZNLHlCQUF5QiIsImlnbm9yZUxpc3QiOltdfQ==