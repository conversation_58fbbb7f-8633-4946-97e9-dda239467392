{"version": 3, "names": ["exports", "__esModule", "default", "useRefEffect", "_react", "require", "effect", "cleanupRef", "useRef", "undefined", "useCallback", "instance", "current", "module"], "sources": ["useRefEffect.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = useRefEffect;\nvar _react = require(\"react\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n/**\n * Constructs a callback ref that provides similar semantics as `useEffect`. The\n * supplied `effect` callback will be called with non-null component instances.\n * The `effect` callback can also optionally return a cleanup function.\n *\n * When a component is updated or unmounted, the cleanup function is called. The\n * `effect` callback will then be called again, if applicable.\n *\n * When a new `effect` callback is supplied, the previously returned cleanup\n * function will be called before the new `effect` callback is called with the\n * same instance.\n *\n * WARNING: The `effect` callback should be stable (e.g. using `useCallback`).\n */\nfunction useRefEffect(effect) {\n  var cleanupRef = (0, _react.useRef)(undefined);\n  return (0, _react.useCallback)(instance => {\n    if (cleanupRef.current) {\n      cleanupRef.current();\n      cleanupRef.current = undefined;\n    }\n    if (instance != null) {\n      cleanupRef.current = effect(instance);\n    }\n  }, [effect]);\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,YAAY;AAC9B,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAyB7B,SAASF,YAAYA,CAACG,MAAM,EAAE;EAC5B,IAAIC,UAAU,GAAG,CAAC,CAAC,EAAEH,MAAM,CAACI,MAAM,EAAEC,SAAS,CAAC;EAC9C,OAAO,CAAC,CAAC,EAAEL,MAAM,CAACM,WAAW,EAAE,UAAAC,QAAQ,EAAI;IACzC,IAAIJ,UAAU,CAACK,OAAO,EAAE;MACtBL,UAAU,CAACK,OAAO,CAAC,CAAC;MACpBL,UAAU,CAACK,OAAO,GAAGH,SAAS;IAChC;IACA,IAAIE,QAAQ,IAAI,IAAI,EAAE;MACpBJ,UAAU,CAACK,OAAO,GAAGN,MAAM,CAACK,QAAQ,CAAC;IACvC;EACF,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;AACd;AACAO,MAAM,CAACb,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}