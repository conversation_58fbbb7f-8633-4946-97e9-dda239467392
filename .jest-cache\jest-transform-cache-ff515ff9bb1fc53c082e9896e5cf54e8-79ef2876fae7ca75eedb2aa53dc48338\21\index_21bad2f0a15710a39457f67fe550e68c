af3e4cf79f53277ec38f4e937a6754cb
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var Info = (0, _createClass2.default)(function Info() {
  (0, _classCallCheck2.default)(this, Info);
  this.any_blank_count = 0;
  this.any_blank_ms = 0;
  this.any_blank_speed_sum = 0;
  this.mostly_blank_count = 0;
  this.mostly_blank_ms = 0;
  this.pixels_blank = 0;
  this.pixels_sampled = 0;
  this.pixels_scrolled = 0;
  this.total_time_spent = 0;
  this.sample_count = 0;
});
var DEBUG = false;
var _listeners = [];
var _minSampleCount = 10;
var _sampleRate = DEBUG ? 1 : null;
var FillRateHelper = function () {
  function FillRateHelper(getFrameMetrics) {
    (0, _classCallCheck2.default)(this, FillRateHelper);
    this._anyBlankStartTime = null;
    this._enabled = false;
    this._info = new Info();
    this._mostlyBlankStartTime = null;
    this._samplesStartTime = null;
    this._getFrameMetrics = getFrameMetrics;
    this._enabled = (_sampleRate || 0) > Math.random();
    this._resetData();
  }
  return (0, _createClass2.default)(FillRateHelper, [{
    key: "activate",
    value: function activate() {
      if (this._enabled && this._samplesStartTime == null) {
        DEBUG && console.debug('FillRateHelper: activate');
        this._samplesStartTime = global.performance.now();
      }
    }
  }, {
    key: "deactivateAndFlush",
    value: function deactivateAndFlush() {
      if (!this._enabled) {
        return;
      }
      var start = this._samplesStartTime;
      if (start == null) {
        DEBUG && console.debug('FillRateHelper: bail on deactivate with no start time');
        return;
      }
      if (this._info.sample_count < _minSampleCount) {
        this._resetData();
        return;
      }
      var total_time_spent = global.performance.now() - start;
      var info = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, this._info), {}, {
        total_time_spent: total_time_spent
      });
      if (DEBUG) {
        var derived = {
          avg_blankness: this._info.pixels_blank / this._info.pixels_sampled,
          avg_speed: this._info.pixels_scrolled / (total_time_spent / 1000),
          avg_speed_when_any_blank: this._info.any_blank_speed_sum / this._info.any_blank_count,
          any_blank_per_min: this._info.any_blank_count / (total_time_spent / 1000 / 60),
          any_blank_time_frac: this._info.any_blank_ms / total_time_spent,
          mostly_blank_per_min: this._info.mostly_blank_count / (total_time_spent / 1000 / 60),
          mostly_blank_time_frac: this._info.mostly_blank_ms / total_time_spent
        };
        for (var key in derived) {
          derived[key] = Math.round(1000 * derived[key]) / 1000;
        }
        console.debug('FillRateHelper deactivateAndFlush: ', {
          derived: derived,
          info: info
        });
      }
      _listeners.forEach(function (listener) {
        return listener(info);
      });
      this._resetData();
    }
  }, {
    key: "computeBlankness",
    value: function computeBlankness(props, cellsAroundViewport, scrollMetrics) {
      if (!this._enabled || props.getItemCount(props.data) === 0 || cellsAroundViewport.last < cellsAroundViewport.first || this._samplesStartTime == null) {
        return 0;
      }
      var dOffset = scrollMetrics.dOffset,
        offset = scrollMetrics.offset,
        velocity = scrollMetrics.velocity,
        visibleLength = scrollMetrics.visibleLength;
      this._info.sample_count++;
      this._info.pixels_sampled += Math.round(visibleLength);
      this._info.pixels_scrolled += Math.round(Math.abs(dOffset));
      var scrollSpeed = Math.round(Math.abs(velocity) * 1000);
      var now = global.performance.now();
      if (this._anyBlankStartTime != null) {
        this._info.any_blank_ms += now - this._anyBlankStartTime;
      }
      this._anyBlankStartTime = null;
      if (this._mostlyBlankStartTime != null) {
        this._info.mostly_blank_ms += now - this._mostlyBlankStartTime;
      }
      this._mostlyBlankStartTime = null;
      var blankTop = 0;
      var first = cellsAroundViewport.first;
      var firstFrame = this._getFrameMetrics(first, props);
      while (first <= cellsAroundViewport.last && (!firstFrame || !firstFrame.inLayout)) {
        firstFrame = this._getFrameMetrics(first, props);
        first++;
      }
      if (firstFrame && first > 0) {
        blankTop = Math.min(visibleLength, Math.max(0, firstFrame.offset - offset));
      }
      var blankBottom = 0;
      var last = cellsAroundViewport.last;
      var lastFrame = this._getFrameMetrics(last, props);
      while (last >= cellsAroundViewport.first && (!lastFrame || !lastFrame.inLayout)) {
        lastFrame = this._getFrameMetrics(last, props);
        last--;
      }
      if (lastFrame && last < props.getItemCount(props.data) - 1) {
        var bottomEdge = lastFrame.offset + lastFrame.length;
        blankBottom = Math.min(visibleLength, Math.max(0, offset + visibleLength - bottomEdge));
      }
      var pixels_blank = Math.round(blankTop + blankBottom);
      var blankness = pixels_blank / visibleLength;
      if (blankness > 0) {
        this._anyBlankStartTime = now;
        this._info.any_blank_speed_sum += scrollSpeed;
        this._info.any_blank_count++;
        this._info.pixels_blank += pixels_blank;
        if (blankness > 0.5) {
          this._mostlyBlankStartTime = now;
          this._info.mostly_blank_count++;
        }
      } else if (scrollSpeed < 0.01 || Math.abs(dOffset) < 1) {
        this.deactivateAndFlush();
      }
      return blankness;
    }
  }, {
    key: "enabled",
    value: function enabled() {
      return this._enabled;
    }
  }, {
    key: "_resetData",
    value: function _resetData() {
      this._anyBlankStartTime = null;
      this._info = new Info();
      this._mostlyBlankStartTime = null;
      this._samplesStartTime = null;
    }
  }], [{
    key: "addListener",
    value: function addListener(callback) {
      if (_sampleRate === null) {
        console.warn('Call `FillRateHelper.setSampleRate` before `addListener`.');
      }
      _listeners.push(callback);
      return {
        remove: function remove() {
          _listeners = _listeners.filter(function (listener) {
            return callback !== listener;
          });
        }
      };
    }
  }, {
    key: "setSampleRate",
    value: function setSampleRate(sampleRate) {
      _sampleRate = sampleRate;
    }
  }, {
    key: "setMinSampleCount",
    value: function setMinSampleCount(minSampleCount) {
      _minSampleCount = minSampleCount;
    }
  }]);
}();
var _default = exports.default = FillRateHelper;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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