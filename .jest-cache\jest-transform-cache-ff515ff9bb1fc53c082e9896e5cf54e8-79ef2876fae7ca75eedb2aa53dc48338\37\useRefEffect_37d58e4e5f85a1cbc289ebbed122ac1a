eb03b824a9f2510c90373956146e4e4c
"use strict";

exports.__esModule = true;
exports.default = useRefEffect;
var _react = require("react");
function useRefEffect(effect) {
  var cleanupRef = (0, _react.useRef)(undefined);
  return (0, _react.useCallback)(function (instance) {
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = undefined;
    }
    if (instance != null) {
      cleanupRef.current = effect(instance);
    }
  }, [effect]);
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJleHBvcnRzIiwiX19lc01vZHVsZSIsImRlZmF1bHQiLCJ1c2VSZWZFZmZlY3QiLCJfcmVhY3QiLCJyZXF1aXJlIiwiZWZmZWN0IiwiY2xlYW51cFJlZiIsInVzZVJlZiIsInVuZGVmaW5lZCIsInVzZUNhbGxiYWNrIiwiaW5zdGFuY2UiLCJjdXJyZW50IiwibW9kdWxlIl0sInNvdXJjZXMiOlsidXNlUmVmRWZmZWN0LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuXG5leHBvcnRzLl9fZXNNb2R1bGUgPSB0cnVlO1xuZXhwb3J0cy5kZWZhdWx0ID0gdXNlUmVmRWZmZWN0O1xudmFyIF9yZWFjdCA9IHJlcXVpcmUoXCJyZWFjdFwiKTtcbi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKiBcbiAqIEBmb3JtYXRcbiAqL1xuXG4vKipcbiAqIENvbnN0cnVjdHMgYSBjYWxsYmFjayByZWYgdGhhdCBwcm92aWRlcyBzaW1pbGFyIHNlbWFudGljcyBhcyBgdXNlRWZmZWN0YC4gVGhlXG4gKiBzdXBwbGllZCBgZWZmZWN0YCBjYWxsYmFjayB3aWxsIGJlIGNhbGxlZCB3aXRoIG5vbi1udWxsIGNvbXBvbmVudCBpbnN0YW5jZXMuXG4gKiBUaGUgYGVmZmVjdGAgY2FsbGJhY2sgY2FuIGFsc28gb3B0aW9uYWxseSByZXR1cm4gYSBjbGVhbnVwIGZ1bmN0aW9uLlxuICpcbiAqIFdoZW4gYSBjb21wb25lbnQgaXMgdXBkYXRlZCBvciB1bm1vdW50ZWQsIHRoZSBjbGVhbnVwIGZ1bmN0aW9uIGlzIGNhbGxlZC4gVGhlXG4gKiBgZWZmZWN0YCBjYWxsYmFjayB3aWxsIHRoZW4gYmUgY2FsbGVkIGFnYWluLCBpZiBhcHBsaWNhYmxlLlxuICpcbiAqIFdoZW4gYSBuZXcgYGVmZmVjdGAgY2FsbGJhY2sgaXMgc3VwcGxpZWQsIHRoZSBwcmV2aW91c2x5IHJldHVybmVkIGNsZWFudXBcbiAqIGZ1bmN0aW9uIHdpbGwgYmUgY2FsbGVkIGJlZm9yZSB0aGUgbmV3IGBlZmZlY3RgIGNhbGxiYWNrIGlzIGNhbGxlZCB3aXRoIHRoZVxuICogc2FtZSBpbnN0YW5jZS5cbiAqXG4gKiBXQVJOSU5HOiBUaGUgYGVmZmVjdGAgY2FsbGJhY2sgc2hvdWxkIGJlIHN0YWJsZSAoZS5nLiB1c2luZyBgdXNlQ2FsbGJhY2tgKS5cbiAqL1xuZnVuY3Rpb24gdXNlUmVmRWZmZWN0KGVmZmVjdCkge1xuICB2YXIgY2xlYW51cFJlZiA9ICgwLCBfcmVhY3QudXNlUmVmKSh1bmRlZmluZWQpO1xuICByZXR1cm4gKDAsIF9yZWFjdC51c2VDYWxsYmFjaykoaW5zdGFuY2UgPT4ge1xuICAgIGlmIChjbGVhbnVwUmVmLmN1cnJlbnQpIHtcbiAgICAgIGNsZWFudXBSZWYuY3VycmVudCgpO1xuICAgICAgY2xlYW51cFJlZi5jdXJyZW50ID0gdW5kZWZpbmVkO1xuICAgIH1cbiAgICBpZiAoaW5zdGFuY2UgIT0gbnVsbCkge1xuICAgICAgY2xlYW51cFJlZi5jdXJyZW50ID0gZWZmZWN0KGluc3RhbmNlKTtcbiAgICB9XG4gIH0sIFtlZmZlY3RdKTtcbn1cbm1vZHVsZS5leHBvcnRzID0gZXhwb3J0cy5kZWZhdWx0OyJdLCJtYXBwaW5ncyI6IkFBQUEsWUFBWTs7QUFFWkEsT0FBTyxDQUFDQyxVQUFVLEdBQUcsSUFBSTtBQUN6QkQsT0FBTyxDQUFDRSxPQUFPLEdBQUdDLFlBQVk7QUFDOUIsSUFBSUMsTUFBTSxHQUFHQyxPQUFPLENBQUMsT0FBTyxDQUFDO0FBeUI3QixTQUFTRixZQUFZQSxDQUFDRyxNQUFNLEVBQUU7RUFDNUIsSUFBSUMsVUFBVSxHQUFHLENBQUMsQ0FBQyxFQUFFSCxNQUFNLENBQUNJLE1BQU0sRUFBRUMsU0FBUyxDQUFDO0VBQzlDLE9BQU8sQ0FBQyxDQUFDLEVBQUVMLE1BQU0sQ0FBQ00sV0FBVyxFQUFFLFVBQUFDLFFBQVEsRUFBSTtJQUN6QyxJQUFJSixVQUFVLENBQUNLLE9BQU8sRUFBRTtNQUN0QkwsVUFBVSxDQUFDSyxPQUFPLENBQUMsQ0FBQztNQUNwQkwsVUFBVSxDQUFDSyxPQUFPLEdBQUdILFNBQVM7SUFDaEM7SUFDQSxJQUFJRSxRQUFRLElBQUksSUFBSSxFQUFFO01BQ3BCSixVQUFVLENBQUNLLE9BQU8sR0FBR04sTUFBTSxDQUFDSyxRQUFRLENBQUM7SUFDdkM7RUFDRixDQUFDLEVBQUUsQ0FBQ0wsTUFBTSxDQUFDLENBQUM7QUFDZDtBQUNBTyxNQUFNLENBQUNiLE9BQU8sR0FBR0EsT0FBTyxDQUFDRSxPQUFPIiwiaWdub3JlTGlzdCI6W119