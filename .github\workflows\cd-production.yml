name: 🌟 Deploy to Production

on:
  push:
    tags:
      - 'v*.*.*'
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      version:
        description: 'Version to deploy (e.g., v1.0.0)'
        required: true
        type: string
      skip_tests:
        description: 'Skip tests (emergency deployment)'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: acemind/backend

jobs:
  # Production readiness validation
  production-validation:
    name: 🔍 Production Validation
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    outputs:
      version: ${{ steps.version.outputs.version }}
      should_deploy: ${{ steps.validation.outputs.should_deploy }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🏷️ Extract version
        id: version
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            VERSION="${{ github.event.inputs.version }}"
          elif [[ "${{ github.event_name }}" == "release" ]]; then
            VERSION="${{ github.event.release.tag_name }}"
          else
            VERSION="${GITHUB_REF#refs/tags/}"
          fi
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "📦 Deploying version: $VERSION"

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Production readiness check
        id: validation
        run: |
          echo "🔍 Validating production readiness..."
          
          # Check version format
          if [[ ! "${{ steps.version.outputs.version }}" =~ ^v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            echo "❌ Invalid version format. Expected: vX.Y.Z"
            echo "should_deploy=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          # Validate configuration files
          if [ ! -f "app.json" ] || [ ! -f "eas.json" ]; then
            echo "❌ Missing configuration files"
            echo "should_deploy=false" >> $GITHUB_OUTPUT
            exit 1
          fi
          
          # Check for production environment variables
          if ! grep -q "EXPO_PUBLIC_APP_ENV.*production" .env.example; then
            echo "⚠️ Production environment not configured in .env.example"
          fi
          
          echo "✅ Production validation passed"
          echo "should_deploy=true" >> $GITHUB_OUTPUT

      - name: 🧪 Run comprehensive tests
        if: github.event.inputs.skip_tests != 'true'
        run: |
          npm run lint
          npm run type-check
          npm run test:ci
          npm run test:technical-debt

      - name: 🔒 Security audit
        run: |
          npm audit --audit-level=moderate
          npm run validate:security

  # Build production images
  build-production:
    name: 🏭 Build Production
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: production-validation
    if: needs.production-validation.outputs.should_deploy == 'true'
    
    outputs:
      backend_image: ${{ steps.backend.outputs.image }}
      backend_digest: ${{ steps.backend.outputs.digest }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏗️ Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 📝 Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=tag
            type=raw,value=production
            type=raw,value=latest
            type=raw,value=${{ needs.production-validation.outputs.version }}

      - name: 🔨 Build and push backend image
        id: backend
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64
          build-args: |
            NODE_ENV=production
            VERSION=${{ needs.production-validation.outputs.version }}

      - name: 📋 Output image details
        run: |
          echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ needs.production-validation.outputs.version }}" >> $GITHUB_OUTPUT

  # Build mobile app for production
  build-mobile-production:
    name: 📱 Build Mobile Production
    runs-on: ubuntu-latest
    timeout-minutes: 45
    needs: production-validation
    if: needs.production-validation.outputs.should_deploy == 'true'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo and EAS CLI
        run: npm install -g @expo/cli eas-cli

      - name: 🔐 Expo authentication
        run: expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 📱 Build production mobile app
        run: |
          # Update app.json for production
          jq --arg VERSION "${{ needs.production-validation.outputs.version }}" \
             '.expo.version = $VERSION | .expo.extra.environment = "production"' \
             app.json > app.production.json
          mv app.production.json app.json
          
          # Build production version
          eas build --platform all --profile production --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 📤 Upload production builds
        uses: actions/upload-artifact@v3
        with:
          name: mobile-production-build
          path: |
            *.apk
            *.ipa
          retention-days: 90

  # Deploy to production
  deploy-production:
    name: 🌟 Deploy Production
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [production-validation, build-production, build-mobile-production]
    environment: production
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: 🚀 Deploy backend to production
        run: |
          # Update ECS task definition for production
          aws ecs describe-task-definition \
            --task-definition acemind-backend-production \
            --query taskDefinition > task-definition.json
          
          # Update image and environment
          jq --arg IMAGE "${{ needs.build-production.outputs.backend_image }}" \
             --arg VERSION "${{ needs.production-validation.outputs.version }}" \
             '.containerDefinitions[0].image = $IMAGE |
              .containerDefinitions[0].environment += [
                {"name": "NODE_ENV", "value": "production"},
                {"name": "APP_VERSION", "value": $VERSION}
              ]' \
             task-definition.json > updated-task-definition.json
          
          # Register new task definition
          aws ecs register-task-definition \
            --cli-input-json file://updated-task-definition.json
          
          # Blue-green deployment
          aws ecs update-service \
            --cluster acemind-production \
            --service acemind-backend-production \
            --task-definition acemind-backend-production \
            --force-new-deployment

      - name: ⏳ Wait for backend deployment
        run: |
          aws ecs wait services-stable \
            --cluster acemind-production \
            --services acemind-backend-production

      - name: 🌐 Deploy frontend to production
        run: |
          # Build web version for production
          npm ci
          npm run build:web:prod
        env:
          EXPO_PUBLIC_APP_ENV: production
          EXPO_PUBLIC_API_URL: https://api.acemind.app
          EXPO_PUBLIC_APP_VERSION: ${{ needs.production-validation.outputs.version }}

      - name: 📤 Deploy to production S3
        run: |
          # Sync to production bucket
          aws s3 sync dist/ s3://${{ secrets.PRODUCTION_S3_BUCKET }} \
            --delete \
            --cache-control "public, max-age=31536000" \
            --exclude "*.html" \
            --exclude "service-worker.js"
          
          # Upload HTML files with no cache
          aws s3 sync dist/ s3://${{ secrets.PRODUCTION_S3_BUCKET }} \
            --delete \
            --cache-control "no-cache" \
            --include "*.html" \
            --include "service-worker.js"

      - name: 🔄 Invalidate production CloudFront
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.PRODUCTION_CLOUDFRONT_ID }} \
            --paths "/*"

  # Production health checks
  production-health-check:
    name: 🏥 Production Health Check
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: deploy-production
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏥 Comprehensive health check
        run: |
          echo "🔍 Checking production health..."
          
          # Wait for deployment to propagate
          sleep 60
          
          # Check backend health
          curl -f https://api.acemind.app/health
          curl -f https://api.acemind.app/health/detailed
          
          # Check frontend
          curl -f https://acemind.app
          
          # Check API endpoints
          curl -f https://api.acemind.app/api/v1/docs

      - name: ⚡ Production performance check
        run: |
          npx lighthouse https://acemind.app \
            --chrome-flags="--headless" \
            --output=json \
            --output-path=lighthouse-production.json
          
          # Check performance scores
          PERFORMANCE=$(jq '.categories.performance.score * 100' lighthouse-production.json)
          ACCESSIBILITY=$(jq '.categories.accessibility.score * 100' lighthouse-production.json)
          BEST_PRACTICES=$(jq '.categories["best-practices"].score * 100' lighthouse-production.json)
          SEO=$(jq '.categories.seo.score * 100' lighthouse-production.json)
          
          echo "📊 Lighthouse Scores:"
          echo "Performance: $PERFORMANCE"
          echo "Accessibility: $ACCESSIBILITY"
          echo "Best Practices: $BEST_PRACTICES"
          echo "SEO: $SEO"
          
          # Fail if performance is below 90
          if (( $(echo "$PERFORMANCE < 90" | bc -l) )); then
            echo "❌ Performance score below production threshold (90)"
            exit 1
          fi

  # Submit to app stores
  submit-to-stores:
    name: 📱 Submit to App Stores
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [production-validation, build-mobile-production, production-health-check]
    if: github.event_name == 'release'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔧 Setup Expo CLI
        run: npm install -g @expo/cli eas-cli

      - name: 🔐 Expo authentication
        run: expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 🍎 Submit to App Store
        run: eas submit --platform ios --profile production --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
          EXPO_APPLE_ID: ${{ secrets.EXPO_APPLE_ID }}
          EXPO_APPLE_APP_SPECIFIC_PASSWORD: ${{ secrets.EXPO_APPLE_APP_SPECIFIC_PASSWORD }}

      - name: 🤖 Submit to Google Play
        run: eas submit --platform android --profile production --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

  # Post-deployment notifications
  notify-production:
    name: 📢 Production Notifications
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [production-validation, deploy-production, production-health-check]
    if: always()
    
    steps:
      - name: 📢 Slack notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#production'
          text: |
            🌟 Production Deployment ${{ job.status }}
            
            📦 Version: ${{ needs.production-validation.outputs.version }}
            🌐 Frontend: https://acemind.app
            🔧 Backend: https://api.acemind.app
            📱 Mobile: Submitted to stores
            
            🔗 Release: ${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ needs.production-validation.outputs.version }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📧 Email stakeholders
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 587
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: '🌟 AceMind Production Deployment - ${{ needs.production-validation.outputs.version }}'
          to: ${{ secrets.STAKEHOLDER_EMAILS }}
          from: 'AceMind CI/CD <<EMAIL>>'
          body: |
            🎉 AceMind has been successfully deployed to production!
            
            📦 Version: ${{ needs.production-validation.outputs.version }}
            🌐 Website: https://acemind.app
            🔧 API: https://api.acemind.app
            📱 Mobile: Available in app stores
            
            🔗 Release Notes: ${{ github.server_url }}/${{ github.repository }}/releases/tag/${{ needs.production-validation.outputs.version }}

  # Create deployment record
  create-deployment-record:
    name: 📊 Create Deployment Record
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [production-validation, production-health-check]
    if: always()
    
    steps:
      - name: 📊 Update deployment status
        uses: actions/github-script@v7
        with:
          script: |
            const { owner, repo } = context.repo;
            const sha = context.sha;
            
            const state = '${{ needs.production-health-check.result }}' === 'success' ? 'success' : 'failure';
            const description = state === 'success' 
              ? '✅ Production deployment successful' 
              : '❌ Production deployment failed';
            
            await github.rest.repos.createCommitStatus({
              owner,
              repo,
              sha,
              state,
              target_url: 'https://acemind.app',
              description,
              context: 'deployment/production'
            });

      - name: 📝 Create deployment summary
        run: |
          echo "## 🌟 Production Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Component | Status | URL |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|-----|" >> $GITHUB_STEP_SUMMARY
          echo "| Frontend | ✅ Deployed | https://acemind.app |" >> $GITHUB_STEP_SUMMARY
          echo "| Backend API | ✅ Deployed | https://api.acemind.app |" >> $GITHUB_STEP_SUMMARY
          echo "| Mobile App | ✅ Submitted | App Stores |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "📦 **Version:** ${{ needs.production-validation.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "🕐 **Deployed at:** $(date -u)" >> $GITHUB_STEP_SUMMARY
          echo "🎯 **Status:** Production Ready" >> $GITHUB_STEP_SUMMARY
