59ae4e8feeb7fa9ca1ecd2fc54b62791
import { env as _env } from "expo/virtual/env";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2dpflxg6dk() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\payment\\PaymentService.ts";
  var hash = "b44df5e653591ae9240cd6fd839b1cfb703e0d1e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\payment\\PaymentService.ts",
    statementMap: {
      "0": {
        start: {
          line: 79,
          column: 34
        },
        end: {
          line: 79,
          column: 38
        }
      },
      "1": {
        start: {
          line: 81,
          column: 61
        },
        end: {
          line: 81,
          column: 70
        }
      },
      "2": {
        start: {
          line: 82,
          column: 26
        },
        end: {
          line: 82,
          column: 31
        }
      },
      "3": {
        start: {
          line: 85,
          column: 4
        },
        end: {
          line: 85,
          column: 25
        }
      },
      "4": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 33
        }
      },
      "5": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 108,
          column: 5
        }
      },
      "6": {
        start: {
          line: 94,
          column: 6
        },
        end: {
          line: 94,
          column: 47
        }
      },
      "7": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 65
        }
      },
      "8": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 99,
          column: 7
        }
      },
      "9": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 98,
          column: 49
        }
      },
      "10": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 32
        }
      },
      "11": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 45
        }
      },
      "12": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 62
        }
      },
      "13": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 106,
          column: 69
        }
      },
      "14": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 18
        }
      },
      "15": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 115,
          column: 55
        }
      },
      "16": {
        start: {
          line: 122,
          column: 4
        },
        end: {
          line: 122,
          column: 54
        }
      },
      "17": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 135,
          column: 5
        }
      },
      "18": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 57
        }
      },
      "19": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 165,
          column: 5
        }
      },
      "20": {
        start: {
          line: 138,
          column: 23
        },
        end: {
          line: 148,
          column: 8
        }
      },
      "21": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 152,
          column: 7
        }
      },
      "22": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 151,
          column: 82
        }
      },
      "23": {
        start: {
          line: 154,
          column: 19
        },
        end: {
          line: 154,
          column: 40
        }
      },
      "24": {
        start: {
          line: 155,
          column: 6
        },
        end: {
          line: 161,
          column: 8
        }
      },
      "25": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 63
        }
      },
      "26": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 18
        }
      },
      "27": {
        start: {
          line: 176,
          column: 4
        },
        end: {
          line: 178,
          column: 5
        }
      },
      "28": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 57
        }
      },
      "29": {
        start: {
          line: 180,
          column: 17
        },
        end: {
          line: 180,
          column: 51
        }
      },
      "30": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 183,
          column: 5
        }
      },
      "31": {
        start: {
          line: 182,
          column: 6
        },
        end: {
          line: 182,
          column: 63
        }
      },
      "32": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 230,
          column: 5
        }
      },
      "33": {
        start: {
          line: 186,
          column: 23
        },
        end: {
          line: 197,
          column: 8
        }
      },
      "34": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 201,
          column: 7
        }
      },
      "35": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 200,
          column: 80
        }
      },
      "36": {
        start: {
          line: 203,
          column: 19
        },
        end: {
          line: 203,
          column: 40
        }
      },
      "37": {
        start: {
          line: 206,
          column: 41
        },
        end: {
          line: 219,
          column: 7
        }
      },
      "38": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 221,
          column: 85
        }
      },
      "39": {
        start: {
          line: 223,
          column: 6
        },
        end: {
          line: 226,
          column: 8
        }
      },
      "40": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 228,
          column: 61
        }
      },
      "41": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 229,
          column: 18
        }
      },
      "42": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 243,
          column: 5
        }
      },
      "43": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 57
        }
      },
      "44": {
        start: {
          line: 245,
          column: 20
        },
        end: {
          line: 245,
          column: 57
        }
      },
      "45": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 248,
          column: 5
        }
      },
      "46": {
        start: {
          line: 247,
          column: 6
        },
        end: {
          line: 247,
          column: 66
        }
      },
      "47": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 292,
          column: 5
        }
      },
      "48": {
        start: {
          line: 251,
          column: 23
        },
        end: {
          line: 261,
          column: 8
        }
      },
      "49": {
        start: {
          line: 263,
          column: 6
        },
        end: {
          line: 265,
          column: 7
        }
      },
      "50": {
        start: {
          line: 264,
          column: 8
        },
        end: {
          line: 264,
          column: 78
        }
      },
      "51": {
        start: {
          line: 267,
          column: 19
        },
        end: {
          line: 267,
          column: 40
        }
      },
      "52": {
        start: {
          line: 270,
          column: 34
        },
        end: {
          line: 276,
          column: 7
        }
      },
      "53": {
        start: {
          line: 278,
          column: 6
        },
        end: {
          line: 281,
          column: 9
        }
      },
      "54": {
        start: {
          line: 283,
          column: 37
        },
        end: {
          line: 286,
          column: 8
        }
      },
      "55": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 29
        }
      },
      "56": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 61
        }
      },
      "57": {
        start: {
          line: 291,
          column: 6
        },
        end: {
          line: 291,
          column: 18
        }
      },
      "58": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 304,
          column: 5
        }
      },
      "59": {
        start: {
          line: 303,
          column: 6
        },
        end: {
          line: 303,
          column: 57
        }
      },
      "60": {
        start: {
          line: 306,
          column: 4
        },
        end: {
          line: 345,
          column: 5
        }
      },
      "61": {
        start: {
          line: 307,
          column: 23
        },
        end: {
          line: 316,
          column: 8
        }
      },
      "62": {
        start: {
          line: 318,
          column: 6
        },
        end: {
          line: 320,
          column: 7
        }
      },
      "63": {
        start: {
          line: 319,
          column: 8
        },
        end: {
          line: 319,
          column: 84
        }
      },
      "64": {
        start: {
          line: 322,
          column: 19
        },
        end: {
          line: 322,
          column: 40
        }
      },
      "65": {
        start: {
          line: 325,
          column: 34
        },
        end: {
          line: 329,
          column: 7
        }
      },
      "66": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 334,
          column: 9
        }
      },
      "67": {
        start: {
          line: 336,
          column: 37
        },
        end: {
          line: 339,
          column: 8
        }
      },
      "68": {
        start: {
          line: 341,
          column: 6
        },
        end: {
          line: 341,
          column: 29
        }
      },
      "69": {
        start: {
          line: 343,
          column: 6
        },
        end: {
          line: 343,
          column: 61
        }
      },
      "70": {
        start: {
          line: 344,
          column: 6
        },
        end: {
          line: 344,
          column: 18
        }
      },
      "71": {
        start: {
          line: 352,
          column: 4
        },
        end: {
          line: 363,
          column: 5
        }
      },
      "72": {
        start: {
          line: 353,
          column: 38
        },
        end: {
          line: 357,
          column: 8
        }
      },
      "73": {
        start: {
          line: 359,
          column: 6
        },
        end: {
          line: 359,
          column: 81
        }
      },
      "74": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 361,
          column: 63
        }
      },
      "75": {
        start: {
          line: 362,
          column: 6
        },
        end: {
          line: 362,
          column: 18
        }
      },
      "76": {
        start: {
          line: 370,
          column: 4
        },
        end: {
          line: 406,
          column: 5
        }
      },
      "77": {
        start: {
          line: 371,
          column: 27
        },
        end: {
          line: 371,
          column: 65
        }
      },
      "78": {
        start: {
          line: 372,
          column: 6
        },
        end: {
          line: 374,
          column: 7
        }
      },
      "79": {
        start: {
          line: 373,
          column: 8
        },
        end: {
          line: 373,
          column: 18
        }
      },
      "80": {
        start: {
          line: 376,
          column: 23
        },
        end: {
          line: 384,
          column: 8
        }
      },
      "81": {
        start: {
          line: 386,
          column: 6
        },
        end: {
          line: 388,
          column: 7
        }
      },
      "82": {
        start: {
          line: 387,
          column: 8
        },
        end: {
          line: 387,
          column: 83
        }
      },
      "83": {
        start: {
          line: 390,
          column: 19
        },
        end: {
          line: 390,
          column: 40
        }
      },
      "84": {
        start: {
          line: 391,
          column: 6
        },
        end: {
          line: 402,
          column: 10
        }
      },
      "85": {
        start: {
          line: 391,
          column: 52
        },
        end: {
          line: 402,
          column: 7
        }
      },
      "86": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 404,
          column: 61
        }
      },
      "87": {
        start: {
          line: 405,
          column: 6
        },
        end: {
          line: 405,
          column: 18
        }
      },
      "88": {
        start: {
          line: 417,
          column: 4
        },
        end: {
          line: 455,
          column: 5
        }
      },
      "89": {
        start: {
          line: 418,
          column: 27
        },
        end: {
          line: 418,
          column: 65
        }
      },
      "90": {
        start: {
          line: 419,
          column: 6
        },
        end: {
          line: 421,
          column: 7
        }
      },
      "91": {
        start: {
          line: 420,
          column: 8
        },
        end: {
          line: 420,
          column: 56
        }
      },
      "92": {
        start: {
          line: 423,
          column: 23
        },
        end: {
          line: 433,
          column: 8
        }
      },
      "93": {
        start: {
          line: 435,
          column: 6
        },
        end: {
          line: 437,
          column: 7
        }
      },
      "94": {
        start: {
          line: 436,
          column: 8
        },
        end: {
          line: 436,
          column: 80
        }
      },
      "95": {
        start: {
          line: 439,
          column: 19
        },
        end: {
          line: 439,
          column: 40
        }
      },
      "96": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 451,
          column: 8
        }
      },
      "97": {
        start: {
          line: 453,
          column: 6
        },
        end: {
          line: 453,
          column: 60
        }
      },
      "98": {
        start: {
          line: 454,
          column: 6
        },
        end: {
          line: 454,
          column: 18
        }
      },
      "99": {
        start: {
          line: 462,
          column: 4
        },
        end: {
          line: 479,
          column: 5
        }
      },
      "100": {
        start: {
          line: 463,
          column: 23
        },
        end: {
          line: 471,
          column: 8
        }
      },
      "101": {
        start: {
          line: 473,
          column: 6
        },
        end: {
          line: 475,
          column: 7
        }
      },
      "102": {
        start: {
          line: 474,
          column: 8
        },
        end: {
          line: 474,
          column: 83
        }
      },
      "103": {
        start: {
          line: 477,
          column: 6
        },
        end: {
          line: 477,
          column: 63
        }
      },
      "104": {
        start: {
          line: 478,
          column: 6
        },
        end: {
          line: 478,
          column: 18
        }
      },
      "105": {
        start: {
          line: 486,
          column: 4
        },
        end: {
          line: 523,
          column: 5
        }
      },
      "106": {
        start: {
          line: 487,
          column: 27
        },
        end: {
          line: 487,
          column: 65
        }
      },
      "107": {
        start: {
          line: 488,
          column: 6
        },
        end: {
          line: 490,
          column: 7
        }
      },
      "108": {
        start: {
          line: 489,
          column: 8
        },
        end: {
          line: 489,
          column: 18
        }
      },
      "109": {
        start: {
          line: 492,
          column: 23
        },
        end: {
          line: 501,
          column: 8
        }
      },
      "110": {
        start: {
          line: 503,
          column: 6
        },
        end: {
          line: 505,
          column: 7
        }
      },
      "111": {
        start: {
          line: 504,
          column: 8
        },
        end: {
          line: 504,
          column: 76
        }
      },
      "112": {
        start: {
          line: 507,
          column: 19
        },
        end: {
          line: 507,
          column: 40
        }
      },
      "113": {
        start: {
          line: 508,
          column: 6
        },
        end: {
          line: 519,
          column: 10
        }
      },
      "114": {
        start: {
          line: 508,
          column: 50
        },
        end: {
          line: 519,
          column: 7
        }
      },
      "115": {
        start: {
          line: 521,
          column: 6
        },
        end: {
          line: 521,
          column: 54
        }
      },
      "116": {
        start: {
          line: 522,
          column: 6
        },
        end: {
          line: 522,
          column: 18
        }
      },
      "117": {
        start: {
          line: 530,
          column: 4
        },
        end: {
          line: 546,
          column: 5
        }
      },
      "118": {
        start: {
          line: 531,
          column: 6
        },
        end: {
          line: 542,
          column: 7
        }
      },
      "119": {
        start: {
          line: 534,
          column: 10
        },
        end: {
          line: 534,
          column: 65
        }
      },
      "120": {
        start: {
          line: 535,
          column: 10
        },
        end: {
          line: 535,
          column: 16
        }
      },
      "121": {
        start: {
          line: 538,
          column: 10
        },
        end: {
          line: 538,
          column: 60
        }
      },
      "122": {
        start: {
          line: 539,
          column: 10
        },
        end: {
          line: 539,
          column: 16
        }
      },
      "123": {
        start: {
          line: 541,
          column: 10
        },
        end: {
          line: 541,
          column: 64
        }
      },
      "124": {
        start: {
          line: 544,
          column: 6
        },
        end: {
          line: 544,
          column: 57
        }
      },
      "125": {
        start: {
          line: 545,
          column: 6
        },
        end: {
          line: 545,
          column: 18
        }
      },
      "126": {
        start: {
          line: 553,
          column: 4
        },
        end: {
          line: 553,
          column: 23
        }
      },
      "127": {
        start: {
          line: 559,
          column: 38
        },
        end: {
          line: 610,
          column: 5
        }
      },
      "128": {
        start: {
          line: 612,
          column: 4
        },
        end: {
          line: 614,
          column: 7
        }
      },
      "129": {
        start: {
          line: 613,
          column: 6
        },
        end: {
          line: 613,
          column: 48
        }
      },
      "130": {
        start: {
          line: 618,
          column: 32
        },
        end: {
          line: 624,
          column: 5
        }
      },
      "131": {
        start: {
          line: 626,
          column: 4
        },
        end: {
          line: 629,
          column: 7
        }
      },
      "132": {
        start: {
          line: 634,
          column: 4
        },
        end: {
          line: 634,
          column: 67
        }
      },
      "133": {
        start: {
          line: 639,
          column: 30
        },
        end: {
          line: 642,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 84,
            column: 2
          },
          end: {
            line: 84,
            column: 3
          }
        },
        loc: {
          start: {
            line: 84,
            column: 37
          },
          end: {
            line: 87,
            column: 3
          }
        },
        line: 84
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 92,
            column: 2
          },
          end: {
            line: 92,
            column: 3
          }
        },
        loc: {
          start: {
            line: 92,
            column: 36
          },
          end: {
            line: 109,
            column: 3
          }
        },
        line: 92
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 114,
            column: 2
          },
          end: {
            line: 114,
            column: 3
          }
        },
        loc: {
          start: {
            line: 114,
            column: 45
          },
          end: {
            line: 116,
            column: 3
          }
        },
        line: 114
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 121,
            column: 3
          }
        },
        loc: {
          start: {
            line: 121,
            column: 63
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 121
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 128,
            column: 2
          },
          end: {
            line: 128,
            column: 3
          }
        },
        loc: {
          start: {
            line: 132,
            column: 28
          },
          end: {
            line: 166,
            column: 3
          }
        },
        line: 132
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 171,
            column: 2
          },
          end: {
            line: 171,
            column: 3
          }
        },
        loc: {
          start: {
            line: 175,
            column: 68
          },
          end: {
            line: 231,
            column: 3
          }
        },
        line: 175
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 236,
            column: 2
          },
          end: {
            line: 236,
            column: 3
          }
        },
        loc: {
          start: {
            line: 240,
            column: 27
          },
          end: {
            line: 293,
            column: 3
          }
        },
        line: 240
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 298,
            column: 2
          },
          end: {
            line: 298,
            column: 3
          }
        },
        loc: {
          start: {
            line: 301,
            column: 27
          },
          end: {
            line: 346,
            column: 3
          }
        },
        line: 301
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 351,
            column: 2
          },
          end: {
            line: 351,
            column: 3
          }
        },
        loc: {
          start: {
            line: 351,
            column: 74
          },
          end: {
            line: 364,
            column: 3
          }
        },
        line: 351
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 369,
            column: 2
          },
          end: {
            line: 369,
            column: 3
          }
        },
        loc: {
          start: {
            line: 369,
            column: 68
          },
          end: {
            line: 407,
            column: 3
          }
        },
        line: 369
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 391,
            column: 38
          },
          end: {
            line: 391,
            column: 39
          }
        },
        loc: {
          start: {
            line: 391,
            column: 52
          },
          end: {
            line: 402,
            column: 7
          }
        },
        line: 391
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 412,
            column: 2
          },
          end: {
            line: 412,
            column: 3
          }
        },
        loc: {
          start: {
            line: 416,
            column: 28
          },
          end: {
            line: 456,
            column: 3
          }
        },
        line: 416
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 461,
            column: 2
          },
          end: {
            line: 461,
            column: 3
          }
        },
        loc: {
          start: {
            line: 461,
            column: 68
          },
          end: {
            line: 480,
            column: 3
          }
        },
        line: 461
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 485,
            column: 2
          },
          end: {
            line: 485,
            column: 3
          }
        },
        loc: {
          start: {
            line: 485,
            column: 76
          },
          end: {
            line: 524,
            column: 3
          }
        },
        line: 485
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 508,
            column: 31
          },
          end: {
            line: 508,
            column: 32
          }
        },
        loc: {
          start: {
            line: 508,
            column: 50
          },
          end: {
            line: 519,
            column: 7
          }
        },
        line: 508
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 529,
            column: 2
          },
          end: {
            line: 529,
            column: 3
          }
        },
        loc: {
          start: {
            line: 529,
            column: 50
          },
          end: {
            line: 547,
            column: 3
          }
        },
        line: 529
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 552,
            column: 2
          },
          end: {
            line: 552,
            column: 3
          }
        },
        loc: {
          start: {
            line: 552,
            column: 29
          },
          end: {
            line: 554,
            column: 3
          }
        },
        line: 552
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 558,
            column: 2
          },
          end: {
            line: 558,
            column: 3
          }
        },
        loc: {
          start: {
            line: 558,
            column: 40
          },
          end: {
            line: 615,
            column: 3
          }
        },
        line: 558
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 612,
            column: 18
          },
          end: {
            line: 612,
            column: 19
          }
        },
        loc: {
          start: {
            line: 612,
            column: 26
          },
          end: {
            line: 614,
            column: 5
          }
        },
        line: 612
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 617,
            column: 2
          },
          end: {
            line: 617,
            column: 3
          }
        },
        loc: {
          start: {
            line: 617,
            column: 75
          },
          end: {
            line: 630,
            column: 3
          }
        },
        line: 617
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 632,
            column: 2
          },
          end: {
            line: 632,
            column: 3
          }
        },
        loc: {
          start: {
            line: 632,
            column: 65
          },
          end: {
            line: 635,
            column: 3
          }
        },
        line: 632
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 97,
            column: 6
          },
          end: {
            line: 99,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 6
          },
          end: {
            line: 99,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "1": {
        loc: {
          start: {
            line: 122,
            column: 11
          },
          end: {
            line: 122,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 122,
            column: 11
          },
          end: {
            line: 122,
            column: 45
          }
        }, {
          start: {
            line: 122,
            column: 49
          },
          end: {
            line: 122,
            column: 53
          }
        }],
        line: 122
      },
      "2": {
        loc: {
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 130,
            column: 28
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 130,
            column: 23
          },
          end: {
            line: 130,
            column: 28
          }
        }],
        line: 130
      },
      "3": {
        loc: {
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 135,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 133,
            column: 4
          },
          end: {
            line: 135,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 133
      },
      "4": {
        loc: {
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 152,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 152,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "5": {
        loc: {
          start: {
            line: 176,
            column: 4
          },
          end: {
            line: 178,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 4
          },
          end: {
            line: 178,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "6": {
        loc: {
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 183,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 4
          },
          end: {
            line: 183,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 181
      },
      "7": {
        loc: {
          start: {
            line: 199,
            column: 6
          },
          end: {
            line: 201,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 6
          },
          end: {
            line: 201,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 199
      },
      "8": {
        loc: {
          start: {
            line: 214,
            column: 18
          },
          end: {
            line: 214,
            column: 118
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 214,
            column: 48
          },
          end: {
            line: 214,
            column: 106
          }
        }, {
          start: {
            line: 214,
            column: 109
          },
          end: {
            line: 214,
            column: 118
          }
        }],
        line: 214
      },
      "9": {
        loc: {
          start: {
            line: 239,
            column: 4
          },
          end: {
            line: 239,
            column: 73
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 239,
            column: 54
          },
          end: {
            line: 239,
            column: 73
          }
        }],
        line: 239
      },
      "10": {
        loc: {
          start: {
            line: 241,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 241,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 241
      },
      "11": {
        loc: {
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 4
          },
          end: {
            line: 248,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 246
      },
      "12": {
        loc: {
          start: {
            line: 263,
            column: 6
          },
          end: {
            line: 265,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 6
          },
          end: {
            line: 265,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "13": {
        loc: {
          start: {
            line: 300,
            column: 4
          },
          end: {
            line: 300,
            column: 37
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 300,
            column: 33
          },
          end: {
            line: 300,
            column: 37
          }
        }],
        line: 300
      },
      "14": {
        loc: {
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 304,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 304,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "15": {
        loc: {
          start: {
            line: 318,
            column: 6
          },
          end: {
            line: 320,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 318,
            column: 6
          },
          end: {
            line: 320,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 318
      },
      "16": {
        loc: {
          start: {
            line: 359,
            column: 13
          },
          end: {
            line: 359,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 359,
            column: 57
          },
          end: {
            line: 359,
            column: 73
          }
        }, {
          start: {
            line: 359,
            column: 76
          },
          end: {
            line: 359,
            column: 80
          }
        }],
        line: 359
      },
      "17": {
        loc: {
          start: {
            line: 359,
            column: 13
          },
          end: {
            line: 359,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 359,
            column: 13
          },
          end: {
            line: 359,
            column: 26
          }
        }, {
          start: {
            line: 359,
            column: 30
          },
          end: {
            line: 359,
            column: 54
          }
        }],
        line: 359
      },
      "18": {
        loc: {
          start: {
            line: 372,
            column: 6
          },
          end: {
            line: 374,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 372,
            column: 6
          },
          end: {
            line: 374,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 372
      },
      "19": {
        loc: {
          start: {
            line: 386,
            column: 6
          },
          end: {
            line: 388,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 386,
            column: 6
          },
          end: {
            line: 388,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 386
      },
      "20": {
        loc: {
          start: {
            line: 415,
            column: 4
          },
          end: {
            line: 415,
            column: 33
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 415,
            column: 28
          },
          end: {
            line: 415,
            column: 33
          }
        }],
        line: 415
      },
      "21": {
        loc: {
          start: {
            line: 419,
            column: 6
          },
          end: {
            line: 421,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 419,
            column: 6
          },
          end: {
            line: 421,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 419
      },
      "22": {
        loc: {
          start: {
            line: 435,
            column: 6
          },
          end: {
            line: 437,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 435,
            column: 6
          },
          end: {
            line: 437,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 435
      },
      "23": {
        loc: {
          start: {
            line: 473,
            column: 6
          },
          end: {
            line: 475,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 473,
            column: 6
          },
          end: {
            line: 475,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 473
      },
      "24": {
        loc: {
          start: {
            line: 485,
            column: 36
          },
          end: {
            line: 485,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 485,
            column: 52
          },
          end: {
            line: 485,
            column: 54
          }
        }],
        line: 485
      },
      "25": {
        loc: {
          start: {
            line: 488,
            column: 6
          },
          end: {
            line: 490,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 488,
            column: 6
          },
          end: {
            line: 490,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 488
      },
      "26": {
        loc: {
          start: {
            line: 503,
            column: 6
          },
          end: {
            line: 505,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 503,
            column: 6
          },
          end: {
            line: 505,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 503
      },
      "27": {
        loc: {
          start: {
            line: 515,
            column: 16
          },
          end: {
            line: 515,
            column: 130
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 515,
            column: 53
          },
          end: {
            line: 515,
            column: 118
          }
        }, {
          start: {
            line: 515,
            column: 121
          },
          end: {
            line: 515,
            column: 130
          }
        }],
        line: 515
      },
      "28": {
        loc: {
          start: {
            line: 531,
            column: 6
          },
          end: {
            line: 542,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 532,
            column: 8
          },
          end: {
            line: 532,
            column: 45
          }
        }, {
          start: {
            line: 533,
            column: 8
          },
          end: {
            line: 535,
            column: 16
          }
        }, {
          start: {
            line: 536,
            column: 8
          },
          end: {
            line: 536,
            column: 41
          }
        }, {
          start: {
            line: 537,
            column: 8
          },
          end: {
            line: 539,
            column: 16
          }
        }, {
          start: {
            line: 540,
            column: 8
          },
          end: {
            line: 541,
            column: 64
          }
        }],
        line: 531
      },
      "29": {
        loc: {
          start: {
            line: 589,
            column: 23
          },
          end: {
            line: 589,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 589,
            column: 23
          },
          end: {
            line: 589,
            column: 66
          }
        }, {
          start: {
            line: 589,
            column: 70
          },
          end: {
            line: 589,
            column: 72
          }
        }],
        line: 589
      },
      "30": {
        loc: {
          start: {
            line: 607,
            column: 23
          },
          end: {
            line: 607,
            column: 76
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 607,
            column: 23
          },
          end: {
            line: 607,
            column: 70
          }
        }, {
          start: {
            line: 607,
            column: 74
          },
          end: {
            line: 607,
            column: 76
          }
        }],
        line: 607
      },
      "31": {
        loc: {
          start: {
            line: 640,
            column: 18
          },
          end: {
            line: 640,
            column: 70
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 640,
            column: 18
          },
          end: {
            line: 640,
            column: 64
          }
        }, {
          start: {
            line: 640,
            column: 68
          },
          end: {
            line: 640,
            column: 70
          }
        }],
        line: 640
      },
      "32": {
        loc: {
          start: {
            line: 641,
            column: 10
          },
          end: {
            line: 641,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 641,
            column: 10
          },
          end: {
            line: 641,
            column: 41
          }
        }, {
          start: {
            line: 641,
            column: 45
          },
          end: {
            line: 641,
            column: 47
          }
        }],
        line: 641
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0, 0, 0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "b44df5e653591ae9240cd6fd839b1cfb703e0d1e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2dpflxg6dk = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2dpflxg6dk();
import { loadStripe } from '@stripe/stripe-js';
import { databaseService } from "../database/DatabaseService";
import { performanceMonitor } from "../../../utils/performance";
var PaymentService = function () {
  function PaymentService(config) {
    _classCallCheck(this, PaymentService);
    this.stripe = (cov_2dpflxg6dk().s[0]++, null);
    this.subscriptionTiers = (cov_2dpflxg6dk().s[1]++, new Map());
    this.isInitialized = (cov_2dpflxg6dk().s[2]++, false);
    cov_2dpflxg6dk().f[0]++;
    cov_2dpflxg6dk().s[3]++;
    this.config = config;
    cov_2dpflxg6dk().s[4]++;
    this.loadSubscriptionTiers();
  }
  return _createClass(PaymentService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_2dpflxg6dk().f[1]++;
        cov_2dpflxg6dk().s[5]++;
        try {
          cov_2dpflxg6dk().s[6]++;
          performanceMonitor.start('payment_init');
          cov_2dpflxg6dk().s[7]++;
          this.stripe = yield loadStripe(this.config.publishableKey);
          cov_2dpflxg6dk().s[8]++;
          if (!this.stripe) {
            cov_2dpflxg6dk().b[0][0]++;
            cov_2dpflxg6dk().s[9]++;
            throw new Error('Failed to load Stripe');
          } else {
            cov_2dpflxg6dk().b[0][1]++;
          }
          cov_2dpflxg6dk().s[10]++;
          this.isInitialized = true;
          cov_2dpflxg6dk().s[11]++;
          performanceMonitor.end('payment_init');
          cov_2dpflxg6dk().s[12]++;
          console.log('Payment service initialized successfully');
        } catch (error) {
          cov_2dpflxg6dk().s[13]++;
          console.error('Payment service initialization failed:', error);
          cov_2dpflxg6dk().s[14]++;
          throw error;
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "getSubscriptionTiers",
    value: function getSubscriptionTiers() {
      cov_2dpflxg6dk().f[2]++;
      cov_2dpflxg6dk().s[15]++;
      return Array.from(this.subscriptionTiers.values());
    }
  }, {
    key: "getSubscriptionTier",
    value: function getSubscriptionTier(tierId) {
      cov_2dpflxg6dk().f[3]++;
      cov_2dpflxg6dk().s[16]++;
      return (cov_2dpflxg6dk().b[1][0]++, this.subscriptionTiers.get(tierId)) || (cov_2dpflxg6dk().b[1][1]++, null);
    }
  }, {
    key: "createPaymentIntent",
    value: (function () {
      var _createPaymentIntent = _asyncToGenerator(function* (amount) {
        var currency = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2dpflxg6dk().b[2][0]++, 'usd');
        var metadata = arguments.length > 2 ? arguments[2] : undefined;
        cov_2dpflxg6dk().f[4]++;
        cov_2dpflxg6dk().s[17]++;
        if (!this.isInitialized) {
          cov_2dpflxg6dk().b[3][0]++;
          cov_2dpflxg6dk().s[18]++;
          throw new Error('Payment service not initialized');
        } else {
          cov_2dpflxg6dk().b[3][1]++;
        }
        cov_2dpflxg6dk().s[19]++;
        try {
          var response = (cov_2dpflxg6dk().s[20]++, yield fetch(`${this.config.apiUrl}/create-payment-intent`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              amount: amount,
              currency: currency,
              metadata: metadata
            })
          }));
          cov_2dpflxg6dk().s[21]++;
          if (!response.ok) {
            cov_2dpflxg6dk().b[4][0]++;
            cov_2dpflxg6dk().s[22]++;
            throw new Error(`Payment intent creation failed: ${response.statusText}`);
          } else {
            cov_2dpflxg6dk().b[4][1]++;
          }
          var data = (cov_2dpflxg6dk().s[23]++, yield response.json());
          cov_2dpflxg6dk().s[24]++;
          return {
            id: data.id,
            clientSecret: data.client_secret,
            amount: data.amount,
            currency: data.currency,
            status: data.status
          };
        } catch (error) {
          cov_2dpflxg6dk().s[25]++;
          console.error('Failed to create payment intent:', error);
          cov_2dpflxg6dk().s[26]++;
          throw error;
        }
      });
      function createPaymentIntent(_x) {
        return _createPaymentIntent.apply(this, arguments);
      }
      return createPaymentIntent;
    }())
  }, {
    key: "createSubscription",
    value: (function () {
      var _createSubscription = _asyncToGenerator(function* (userId, tierId, paymentMethodId) {
        cov_2dpflxg6dk().f[5]++;
        cov_2dpflxg6dk().s[27]++;
        if (!this.isInitialized) {
          cov_2dpflxg6dk().b[5][0]++;
          cov_2dpflxg6dk().s[28]++;
          throw new Error('Payment service not initialized');
        } else {
          cov_2dpflxg6dk().b[5][1]++;
        }
        var tier = (cov_2dpflxg6dk().s[29]++, this.subscriptionTiers.get(tierId));
        cov_2dpflxg6dk().s[30]++;
        if (!tier) {
          cov_2dpflxg6dk().b[6][0]++;
          cov_2dpflxg6dk().s[31]++;
          throw new Error(`Subscription tier ${tierId} not found`);
        } else {
          cov_2dpflxg6dk().b[6][1]++;
        }
        cov_2dpflxg6dk().s[32]++;
        try {
          var response = (cov_2dpflxg6dk().s[33]++, yield fetch(`${this.config.apiUrl}/create-subscription`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              userId: userId,
              priceId: tier.stripePriceId,
              paymentMethodId: paymentMethodId,
              trialDays: tier.trialDays
            })
          }));
          cov_2dpflxg6dk().s[34]++;
          if (!response.ok) {
            cov_2dpflxg6dk().b[7][0]++;
            cov_2dpflxg6dk().s[35]++;
            throw new Error(`Subscription creation failed: ${response.statusText}`);
          } else {
            cov_2dpflxg6dk().b[7][1]++;
          }
          var data = (cov_2dpflxg6dk().s[36]++, yield response.json());
          var subscription = (cov_2dpflxg6dk().s[37]++, {
            id: data.subscription.id,
            userId: userId,
            tierId: tierId,
            status: data.subscription.status,
            currentPeriodStart: new Date(data.subscription.current_period_start * 1000).toISOString(),
            currentPeriodEnd: new Date(data.subscription.current_period_end * 1000).toISOString(),
            cancelAtPeriodEnd: data.subscription.cancel_at_period_end,
            trialEnd: data.subscription.trial_end ? (cov_2dpflxg6dk().b[8][0]++, new Date(data.subscription.trial_end * 1000).toISOString()) : (cov_2dpflxg6dk().b[8][1]++, undefined),
            stripeSubscriptionId: data.subscription.id,
            stripeCustomerId: data.customer.id,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          });
          cov_2dpflxg6dk().s[38]++;
          yield databaseService.query('subscriptions', 'insert', {
            data: subscription
          });
          cov_2dpflxg6dk().s[39]++;
          return {
            subscription: subscription,
            clientSecret: data.client_secret
          };
        } catch (error) {
          cov_2dpflxg6dk().s[40]++;
          console.error('Failed to create subscription:', error);
          cov_2dpflxg6dk().s[41]++;
          throw error;
        }
      });
      function createSubscription(_x2, _x3, _x4) {
        return _createSubscription.apply(this, arguments);
      }
      return createSubscription;
    }())
  }, {
    key: "updateSubscription",
    value: (function () {
      var _updateSubscription = _asyncToGenerator(function* (subscriptionId, newTierId) {
        var prorationBehavior = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_2dpflxg6dk().b[9][0]++, 'create_prorations');
        cov_2dpflxg6dk().f[6]++;
        cov_2dpflxg6dk().s[42]++;
        if (!this.isInitialized) {
          cov_2dpflxg6dk().b[10][0]++;
          cov_2dpflxg6dk().s[43]++;
          throw new Error('Payment service not initialized');
        } else {
          cov_2dpflxg6dk().b[10][1]++;
        }
        var newTier = (cov_2dpflxg6dk().s[44]++, this.subscriptionTiers.get(newTierId));
        cov_2dpflxg6dk().s[45]++;
        if (!newTier) {
          cov_2dpflxg6dk().b[11][0]++;
          cov_2dpflxg6dk().s[46]++;
          throw new Error(`Subscription tier ${newTierId} not found`);
        } else {
          cov_2dpflxg6dk().b[11][1]++;
        }
        cov_2dpflxg6dk().s[47]++;
        try {
          var response = (cov_2dpflxg6dk().s[48]++, yield fetch(`${this.config.apiUrl}/update-subscription`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subscriptionId: subscriptionId,
              priceId: newTier.stripePriceId,
              prorationBehavior: prorationBehavior
            })
          }));
          cov_2dpflxg6dk().s[49]++;
          if (!response.ok) {
            cov_2dpflxg6dk().b[12][0]++;
            cov_2dpflxg6dk().s[50]++;
            throw new Error(`Subscription update failed: ${response.statusText}`);
          } else {
            cov_2dpflxg6dk().b[12][1]++;
          }
          var data = (cov_2dpflxg6dk().s[51]++, yield response.json());
          var updatedSubscription = (cov_2dpflxg6dk().s[52]++, {
            tierId: newTierId,
            status: data.status,
            currentPeriodStart: new Date(data.current_period_start * 1000).toISOString(),
            currentPeriodEnd: new Date(data.current_period_end * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          });
          cov_2dpflxg6dk().s[53]++;
          yield databaseService.query('subscriptions', 'update', {
            data: updatedSubscription,
            filter: {
              stripe_subscription_id: subscriptionId
            }
          });
          var _ref = (cov_2dpflxg6dk().s[54]++, yield databaseService.query('subscriptions', 'select', {
              filter: {
                stripe_subscription_id: subscriptionId
              },
              limit: 1
            })),
            subscription = _ref.data;
          cov_2dpflxg6dk().s[55]++;
          return subscription[0];
        } catch (error) {
          cov_2dpflxg6dk().s[56]++;
          console.error('Failed to update subscription:', error);
          cov_2dpflxg6dk().s[57]++;
          throw error;
        }
      });
      function updateSubscription(_x5, _x6) {
        return _updateSubscription.apply(this, arguments);
      }
      return updateSubscription;
    }())
  }, {
    key: "cancelSubscription",
    value: (function () {
      var _cancelSubscription = _asyncToGenerator(function* (subscriptionId) {
        var cancelAtPeriodEnd = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2dpflxg6dk().b[13][0]++, true);
        cov_2dpflxg6dk().f[7]++;
        cov_2dpflxg6dk().s[58]++;
        if (!this.isInitialized) {
          cov_2dpflxg6dk().b[14][0]++;
          cov_2dpflxg6dk().s[59]++;
          throw new Error('Payment service not initialized');
        } else {
          cov_2dpflxg6dk().b[14][1]++;
        }
        cov_2dpflxg6dk().s[60]++;
        try {
          var response = (cov_2dpflxg6dk().s[61]++, yield fetch(`${this.config.apiUrl}/cancel-subscription`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              subscriptionId: subscriptionId,
              cancelAtPeriodEnd: cancelAtPeriodEnd
            })
          }));
          cov_2dpflxg6dk().s[62]++;
          if (!response.ok) {
            cov_2dpflxg6dk().b[15][0]++;
            cov_2dpflxg6dk().s[63]++;
            throw new Error(`Subscription cancellation failed: ${response.statusText}`);
          } else {
            cov_2dpflxg6dk().b[15][1]++;
          }
          var data = (cov_2dpflxg6dk().s[64]++, yield response.json());
          var updatedSubscription = (cov_2dpflxg6dk().s[65]++, {
            status: data.status,
            cancelAtPeriodEnd: data.cancel_at_period_end,
            updatedAt: new Date().toISOString()
          });
          cov_2dpflxg6dk().s[66]++;
          yield databaseService.query('subscriptions', 'update', {
            data: updatedSubscription,
            filter: {
              stripe_subscription_id: subscriptionId
            }
          });
          var _ref2 = (cov_2dpflxg6dk().s[67]++, yield databaseService.query('subscriptions', 'select', {
              filter: {
                stripe_subscription_id: subscriptionId
              },
              limit: 1
            })),
            subscription = _ref2.data;
          cov_2dpflxg6dk().s[68]++;
          return subscription[0];
        } catch (error) {
          cov_2dpflxg6dk().s[69]++;
          console.error('Failed to cancel subscription:', error);
          cov_2dpflxg6dk().s[70]++;
          throw error;
        }
      });
      function cancelSubscription(_x7) {
        return _cancelSubscription.apply(this, arguments);
      }
      return cancelSubscription;
    }())
  }, {
    key: "getUserSubscription",
    value: (function () {
      var _getUserSubscription = _asyncToGenerator(function* (userId) {
        cov_2dpflxg6dk().f[8]++;
        cov_2dpflxg6dk().s[71]++;
        try {
          var _ref3 = (cov_2dpflxg6dk().s[72]++, yield databaseService.query('subscriptions', 'select', {
              filter: {
                user_id: userId
              },
              orderBy: {
                column: 'created_at',
                ascending: false
              },
              limit: 1
            })),
            subscriptions = _ref3.data;
          cov_2dpflxg6dk().s[73]++;
          return (cov_2dpflxg6dk().b[17][0]++, subscriptions) && (cov_2dpflxg6dk().b[17][1]++, subscriptions.length > 0) ? (cov_2dpflxg6dk().b[16][0]++, subscriptions[0]) : (cov_2dpflxg6dk().b[16][1]++, null);
        } catch (error) {
          cov_2dpflxg6dk().s[74]++;
          console.error('Failed to get user subscription:', error);
          cov_2dpflxg6dk().s[75]++;
          throw error;
        }
      });
      function getUserSubscription(_x8) {
        return _getUserSubscription.apply(this, arguments);
      }
      return getUserSubscription;
    }())
  }, {
    key: "getPaymentMethods",
    value: (function () {
      var _getPaymentMethods = _asyncToGenerator(function* (userId) {
        cov_2dpflxg6dk().f[9]++;
        cov_2dpflxg6dk().s[76]++;
        try {
          var subscription = (cov_2dpflxg6dk().s[77]++, yield this.getUserSubscription(userId));
          cov_2dpflxg6dk().s[78]++;
          if (!subscription) {
            cov_2dpflxg6dk().b[18][0]++;
            cov_2dpflxg6dk().s[79]++;
            return [];
          } else {
            cov_2dpflxg6dk().b[18][1]++;
          }
          var response = (cov_2dpflxg6dk().s[80]++, yield fetch(`${this.config.apiUrl}/payment-methods`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              customerId: subscription.stripeCustomerId
            })
          }));
          cov_2dpflxg6dk().s[81]++;
          if (!response.ok) {
            cov_2dpflxg6dk().b[19][0]++;
            cov_2dpflxg6dk().s[82]++;
            throw new Error(`Failed to fetch payment methods: ${response.statusText}`);
          } else {
            cov_2dpflxg6dk().b[19][1]++;
          }
          var data = (cov_2dpflxg6dk().s[83]++, yield response.json());
          cov_2dpflxg6dk().s[84]++;
          return data.payment_methods.map(function (pm) {
            cov_2dpflxg6dk().f[10]++;
            cov_2dpflxg6dk().s[85]++;
            return {
              id: pm.id,
              type: pm.type,
              card: {
                brand: pm.card.brand,
                last4: pm.card.last4,
                expMonth: pm.card.exp_month,
                expYear: pm.card.exp_year
              },
              isDefault: pm.id === data.default_payment_method,
              createdAt: new Date(pm.created * 1000).toISOString()
            };
          });
        } catch (error) {
          cov_2dpflxg6dk().s[86]++;
          console.error('Failed to get payment methods:', error);
          cov_2dpflxg6dk().s[87]++;
          throw error;
        }
      });
      function getPaymentMethods(_x9) {
        return _getPaymentMethods.apply(this, arguments);
      }
      return getPaymentMethods;
    }())
  }, {
    key: "addPaymentMethod",
    value: (function () {
      var _addPaymentMethod = _asyncToGenerator(function* (userId, paymentMethodId) {
        var setAsDefault = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_2dpflxg6dk().b[20][0]++, false);
        cov_2dpflxg6dk().f[11]++;
        cov_2dpflxg6dk().s[88]++;
        try {
          var subscription = (cov_2dpflxg6dk().s[89]++, yield this.getUserSubscription(userId));
          cov_2dpflxg6dk().s[90]++;
          if (!subscription) {
            cov_2dpflxg6dk().b[21][0]++;
            cov_2dpflxg6dk().s[91]++;
            throw new Error('No active subscription found');
          } else {
            cov_2dpflxg6dk().b[21][1]++;
          }
          var response = (cov_2dpflxg6dk().s[92]++, yield fetch(`${this.config.apiUrl}/attach-payment-method`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              paymentMethodId: paymentMethodId,
              customerId: subscription.stripeCustomerId,
              setAsDefault: setAsDefault
            })
          }));
          cov_2dpflxg6dk().s[93]++;
          if (!response.ok) {
            cov_2dpflxg6dk().b[22][0]++;
            cov_2dpflxg6dk().s[94]++;
            throw new Error(`Failed to add payment method: ${response.statusText}`);
          } else {
            cov_2dpflxg6dk().b[22][1]++;
          }
          var data = (cov_2dpflxg6dk().s[95]++, yield response.json());
          cov_2dpflxg6dk().s[96]++;
          return {
            id: data.id,
            type: data.type,
            card: {
              brand: data.card.brand,
              last4: data.card.last4,
              expMonth: data.card.exp_month,
              expYear: data.card.exp_year
            },
            isDefault: setAsDefault,
            createdAt: new Date(data.created * 1000).toISOString()
          };
        } catch (error) {
          cov_2dpflxg6dk().s[97]++;
          console.error('Failed to add payment method:', error);
          cov_2dpflxg6dk().s[98]++;
          throw error;
        }
      });
      function addPaymentMethod(_x0, _x1) {
        return _addPaymentMethod.apply(this, arguments);
      }
      return addPaymentMethod;
    }())
  }, {
    key: "removePaymentMethod",
    value: (function () {
      var _removePaymentMethod = _asyncToGenerator(function* (paymentMethodId) {
        cov_2dpflxg6dk().f[12]++;
        cov_2dpflxg6dk().s[99]++;
        try {
          var response = (cov_2dpflxg6dk().s[100]++, yield fetch(`${this.config.apiUrl}/detach-payment-method`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              paymentMethodId: paymentMethodId
            })
          }));
          cov_2dpflxg6dk().s[101]++;
          if (!response.ok) {
            cov_2dpflxg6dk().b[23][0]++;
            cov_2dpflxg6dk().s[102]++;
            throw new Error(`Failed to remove payment method: ${response.statusText}`);
          } else {
            cov_2dpflxg6dk().b[23][1]++;
          }
        } catch (error) {
          cov_2dpflxg6dk().s[103]++;
          console.error('Failed to remove payment method:', error);
          cov_2dpflxg6dk().s[104]++;
          throw error;
        }
      });
      function removePaymentMethod(_x10) {
        return _removePaymentMethod.apply(this, arguments);
      }
      return removePaymentMethod;
    }())
  }, {
    key: "getInvoices",
    value: (function () {
      var _getInvoices = _asyncToGenerator(function* (userId) {
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2dpflxg6dk().b[24][0]++, 10);
        cov_2dpflxg6dk().f[13]++;
        cov_2dpflxg6dk().s[105]++;
        try {
          var subscription = (cov_2dpflxg6dk().s[106]++, yield this.getUserSubscription(userId));
          cov_2dpflxg6dk().s[107]++;
          if (!subscription) {
            cov_2dpflxg6dk().b[25][0]++;
            cov_2dpflxg6dk().s[108]++;
            return [];
          } else {
            cov_2dpflxg6dk().b[25][1]++;
          }
          var response = (cov_2dpflxg6dk().s[109]++, yield fetch(`${this.config.apiUrl}/invoices`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              customerId: subscription.stripeCustomerId,
              limit: limit
            })
          }));
          cov_2dpflxg6dk().s[110]++;
          if (!response.ok) {
            cov_2dpflxg6dk().b[26][0]++;
            cov_2dpflxg6dk().s[111]++;
            throw new Error(`Failed to fetch invoices: ${response.statusText}`);
          } else {
            cov_2dpflxg6dk().b[26][1]++;
          }
          var data = (cov_2dpflxg6dk().s[112]++, yield response.json());
          cov_2dpflxg6dk().s[113]++;
          return data.invoices.map(function (invoice) {
            cov_2dpflxg6dk().f[14]++;
            cov_2dpflxg6dk().s[114]++;
            return {
              id: invoice.id,
              subscriptionId: invoice.subscription,
              amount: invoice.amount_paid,
              currency: invoice.currency,
              status: invoice.status,
              dueDate: new Date(invoice.due_date * 1000).toISOString(),
              paidAt: invoice.status_transitions.paid_at ? (cov_2dpflxg6dk().b[27][0]++, new Date(invoice.status_transitions.paid_at * 1000).toISOString()) : (cov_2dpflxg6dk().b[27][1]++, undefined),
              invoiceUrl: invoice.hosted_invoice_url,
              downloadUrl: invoice.invoice_pdf,
              createdAt: new Date(invoice.created * 1000).toISOString()
            };
          });
        } catch (error) {
          cov_2dpflxg6dk().s[115]++;
          console.error('Failed to get invoices:', error);
          cov_2dpflxg6dk().s[116]++;
          throw error;
        }
      });
      function getInvoices(_x11) {
        return _getInvoices.apply(this, arguments);
      }
      return getInvoices;
    }())
  }, {
    key: "processWebhook",
    value: (function () {
      var _processWebhook = _asyncToGenerator(function* (event) {
        cov_2dpflxg6dk().f[15]++;
        cov_2dpflxg6dk().s[117]++;
        try {
          cov_2dpflxg6dk().s[118]++;
          switch (event.type) {
            case 'customer.subscription.updated':
              cov_2dpflxg6dk().b[28][0]++;
            case 'customer.subscription.deleted':
              cov_2dpflxg6dk().b[28][1]++;
              cov_2dpflxg6dk().s[119]++;
              yield this.handleSubscriptionUpdate(event.data.object);
              cov_2dpflxg6dk().s[120]++;
              break;
            case 'invoice.payment_succeeded':
              cov_2dpflxg6dk().b[28][2]++;
            case 'invoice.payment_failed':
              cov_2dpflxg6dk().b[28][3]++;
              cov_2dpflxg6dk().s[121]++;
              yield this.handleInvoiceUpdate(event.data.object);
              cov_2dpflxg6dk().s[122]++;
              break;
            default:
              cov_2dpflxg6dk().b[28][4]++;
              cov_2dpflxg6dk().s[123]++;
              console.log(`Unhandled webhook event: ${event.type}`);
          }
        } catch (error) {
          cov_2dpflxg6dk().s[124]++;
          console.error('Failed to process webhook:', error);
          cov_2dpflxg6dk().s[125]++;
          throw error;
        }
      });
      function processWebhook(_x12) {
        return _processWebhook.apply(this, arguments);
      }
      return processWebhook;
    }())
  }, {
    key: "getStripe",
    value: function getStripe() {
      cov_2dpflxg6dk().f[16]++;
      cov_2dpflxg6dk().s[126]++;
      return this.stripe;
    }
  }, {
    key: "loadSubscriptionTiers",
    value: function loadSubscriptionTiers() {
      var _this = this;
      cov_2dpflxg6dk().f[17]++;
      var tiers = (cov_2dpflxg6dk().s[127]++, [{
        id: 'free',
        name: 'Free',
        description: 'Basic match recording',
        price: 0,
        currency: 'usd',
        interval: 'month',
        features: ['Basic match recording', 'Score tracking', 'Match history', 'Up to 5 matches per month'],
        stripePriceId: ''
      }, {
        id: 'pro',
        name: 'Pro',
        description: 'Advanced features with AI analysis',
        price: 999,
        currency: 'usd',
        interval: 'month',
        features: ['Unlimited match recording', 'AI video analysis', 'Coaching insights', 'Performance tracking', 'Advanced statistics'],
        stripePriceId: (cov_2dpflxg6dk().b[29][0]++, _env.EXPO_PUBLIC_STRIPE_PRO_PRICE_ID) || (cov_2dpflxg6dk().b[29][1]++, ''),
        isPopular: true,
        trialDays: 7
      }, {
        id: 'premium',
        name: 'Premium',
        description: 'Professional coaching features',
        price: 1999,
        currency: 'usd',
        interval: 'month',
        features: ['Everything in Pro', 'Real-time coaching', 'Technique comparison', 'Custom training plans', 'Priority support'],
        stripePriceId: (cov_2dpflxg6dk().b[30][0]++, _env.EXPO_PUBLIC_STRIPE_PREMIUM_PRICE_ID) || (cov_2dpflxg6dk().b[30][1]++, ''),
        trialDays: 14
      }]);
      cov_2dpflxg6dk().s[128]++;
      tiers.forEach(function (tier) {
        cov_2dpflxg6dk().f[18]++;
        cov_2dpflxg6dk().s[129]++;
        _this.subscriptionTiers.set(tier.id, tier);
      });
    }
  }, {
    key: "handleSubscriptionUpdate",
    value: function () {
      var _handleSubscriptionUpdate = _asyncToGenerator(function* (subscription) {
        cov_2dpflxg6dk().f[19]++;
        var updatedSubscription = (cov_2dpflxg6dk().s[130]++, {
          status: subscription.status,
          current_period_start: new Date(subscription.current_period_start * 1000).toISOString(),
          current_period_end: new Date(subscription.current_period_end * 1000).toISOString(),
          cancel_at_period_end: subscription.cancel_at_period_end,
          updated_at: new Date().toISOString()
        });
        cov_2dpflxg6dk().s[131]++;
        yield databaseService.query('subscriptions', 'update', {
          data: updatedSubscription,
          filter: {
            stripe_subscription_id: subscription.id
          }
        });
      });
      function handleSubscriptionUpdate(_x13) {
        return _handleSubscriptionUpdate.apply(this, arguments);
      }
      return handleSubscriptionUpdate;
    }()
  }, {
    key: "handleInvoiceUpdate",
    value: function () {
      var _handleInvoiceUpdate = _asyncToGenerator(function* (invoice) {
        cov_2dpflxg6dk().f[20]++;
        cov_2dpflxg6dk().s[132]++;
        console.log(`Invoice ${invoice.id} status: ${invoice.status}`);
      });
      function handleInvoiceUpdate(_x14) {
        return _handleInvoiceUpdate.apply(this, arguments);
      }
      return handleInvoiceUpdate;
    }()
  }]);
}();
export var paymentService = (cov_2dpflxg6dk().s[133]++, new PaymentService({
  publishableKey: (cov_2dpflxg6dk().b[31][0]++, _env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY) || (cov_2dpflxg6dk().b[31][1]++, ''),
  apiUrl: (cov_2dpflxg6dk().b[32][0]++, _env.EXPO_PUBLIC_API_URL) || (cov_2dpflxg6dk().b[32][1]++, '')
}));
export default PaymentService;
export * from "./PaymentService";
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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