ebf5c5e6d02e393759ebd3436bd3c03f
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import { env as _env } from "expo/virtual/env";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1vxmo407f4() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\paymentService.ts";
  var hash = "772213d562d108374261d767d84727316acaacc5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\paymentService.ts",
    statementMap: {
      "0": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 86,
          column: 85
        }
      },
      "1": {
        start: {
          line: 87,
          column: 4
        },
        end: {
          line: 87,
          column: 83
        }
      },
      "2": {
        start: {
          line: 94,
          column: 19
        },
        end: {
          line: 111,
          column: 5
        }
      },
      "3": {
        start: {
          line: 96,
          column: 8
        },
        end: {
          line: 98,
          column: 9
        }
      },
      "4": {
        start: {
          line: 97,
          column: 10
        },
        end: {
          line: 97,
          column: 93
        }
      },
      "5": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 55
        }
      },
      "6": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 20
        }
      },
      "7": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 112,
          column: 27
        }
      },
      "8": {
        start: {
          line: 119,
          column: 19
        },
        end: {
          line: 132,
          column: 5
        }
      },
      "9": {
        start: {
          line: 121,
          column: 32
        },
        end: {
          line: 125,
          column: 46
        }
      },
      "10": {
        start: {
          line: 127,
          column: 8
        },
        end: {
          line: 127,
          column: 31
        }
      },
      "11": {
        start: {
          line: 127,
          column: 19
        },
        end: {
          line: 127,
          column: 31
        }
      },
      "12": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 128,
          column: 26
        }
      },
      "13": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 133,
          column: 24
        }
      },
      "14": {
        start: {
          line: 140,
          column: 19
        },
        end: {
          line: 184,
          column: 5
        }
      },
      "15": {
        start: {
          line: 142,
          column: 25
        },
        end: {
          line: 153,
          column: 10
        }
      },
      "16": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 158,
          column: 9
        }
      },
      "17": {
        start: {
          line: 156,
          column: 24
        },
        end: {
          line: 156,
          column: 45
        }
      },
      "18": {
        start: {
          line: 157,
          column: 10
        },
        end: {
          line: 157,
          column: 76
        }
      },
      "19": {
        start: {
          line: 160,
          column: 29
        },
        end: {
          line: 160,
          column: 50
        }
      },
      "20": {
        start: {
          line: 163,
          column: 32
        },
        end: {
          line: 177,
          column: 19
        }
      },
      "21": {
        start: {
          line: 179,
          column: 8
        },
        end: {
          line: 179,
          column: 31
        }
      },
      "22": {
        start: {
          line: 179,
          column: 19
        },
        end: {
          line: 179,
          column: 31
        }
      },
      "23": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 20
        }
      },
      "24": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 185,
          column: 26
        }
      },
      "25": {
        start: {
          line: 192,
          column: 19
        },
        end: {
          line: 224,
          column: 5
        }
      },
      "26": {
        start: {
          line: 194,
          column: 25
        },
        end: {
          line: 203,
          column: 10
        }
      },
      "27": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 208,
          column: 9
        }
      },
      "28": {
        start: {
          line: 206,
          column: 24
        },
        end: {
          line: 206,
          column: 45
        }
      },
      "29": {
        start: {
          line: 207,
          column: 10
        },
        end: {
          line: 207,
          column: 76
        }
      },
      "30": {
        start: {
          line: 211,
          column: 26
        },
        end: {
          line: 217,
          column: 35
        }
      },
      "31": {
        start: {
          line: 219,
          column: 8
        },
        end: {
          line: 219,
          column: 31
        }
      },
      "32": {
        start: {
          line: 219,
          column: 19
        },
        end: {
          line: 219,
          column: 31
        }
      },
      "33": {
        start: {
          line: 220,
          column: 8
        },
        end: {
          line: 220,
          column: 20
        }
      },
      "34": {
        start: {
          line: 225,
          column: 4
        },
        end: {
          line: 225,
          column: 27
        }
      },
      "35": {
        start: {
          line: 232,
          column: 19
        },
        end: {
          line: 261,
          column: 5
        }
      },
      "36": {
        start: {
          line: 234,
          column: 25
        },
        end: {
          line: 243,
          column: 10
        }
      },
      "37": {
        start: {
          line: 245,
          column: 8
        },
        end: {
          line: 248,
          column: 9
        }
      },
      "38": {
        start: {
          line: 246,
          column: 24
        },
        end: {
          line: 246,
          column: 45
        }
      },
      "39": {
        start: {
          line: 247,
          column: 10
        },
        end: {
          line: 247,
          column: 76
        }
      },
      "40": {
        start: {
          line: 251,
          column: 26
        },
        end: {
          line: 254,
          column: 35
        }
      },
      "41": {
        start: {
          line: 256,
          column: 8
        },
        end: {
          line: 256,
          column: 31
        }
      },
      "42": {
        start: {
          line: 256,
          column: 19
        },
        end: {
          line: 256,
          column: 31
        }
      },
      "43": {
        start: {
          line: 257,
          column: 8
        },
        end: {
          line: 257,
          column: 20
        }
      },
      "44": {
        start: {
          line: 262,
          column: 4
        },
        end: {
          line: 262,
          column: 27
        }
      },
      "45": {
        start: {
          line: 269,
          column: 19
        },
        end: {
          line: 312,
          column: 5
        }
      },
      "46": {
        start: {
          line: 271,
          column: 25
        },
        end: {
          line: 281,
          column: 10
        }
      },
      "47": {
        start: {
          line: 283,
          column: 8
        },
        end: {
          line: 286,
          column: 9
        }
      },
      "48": {
        start: {
          line: 284,
          column: 24
        },
        end: {
          line: 284,
          column: 45
        }
      },
      "49": {
        start: {
          line: 285,
          column: 10
        },
        end: {
          line: 285,
          column: 75
        }
      },
      "50": {
        start: {
          line: 288,
          column: 30
        },
        end: {
          line: 288,
          column: 51
        }
      },
      "51": {
        start: {
          line: 291,
          column: 32
        },
        end: {
          line: 305,
          column: 19
        }
      },
      "52": {
        start: {
          line: 307,
          column: 8
        },
        end: {
          line: 307,
          column: 31
        }
      },
      "53": {
        start: {
          line: 307,
          column: 19
        },
        end: {
          line: 307,
          column: 31
        }
      },
      "54": {
        start: {
          line: 308,
          column: 8
        },
        end: {
          line: 308,
          column: 20
        }
      },
      "55": {
        start: {
          line: 313,
          column: 4
        },
        end: {
          line: 313,
          column: 26
        }
      },
      "56": {
        start: {
          line: 320,
          column: 19
        },
        end: {
          line: 333,
          column: 5
        }
      },
      "57": {
        start: {
          line: 322,
          column: 32
        },
        end: {
          line: 326,
          column: 52
        }
      },
      "58": {
        start: {
          line: 328,
          column: 8
        },
        end: {
          line: 328,
          column: 31
        }
      },
      "59": {
        start: {
          line: 328,
          column: 19
        },
        end: {
          line: 328,
          column: 31
        }
      },
      "60": {
        start: {
          line: 329,
          column: 8
        },
        end: {
          line: 329,
          column: 26
        }
      },
      "61": {
        start: {
          line: 334,
          column: 4
        },
        end: {
          line: 334,
          column: 24
        }
      },
      "62": {
        start: {
          line: 348,
          column: 19
        },
        end: {
          line: 392,
          column: 5
        }
      },
      "63": {
        start: {
          line: 350,
          column: 25
        },
        end: {
          line: 360,
          column: 10
        }
      },
      "64": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 365,
          column: 9
        }
      },
      "65": {
        start: {
          line: 363,
          column: 24
        },
        end: {
          line: 363,
          column: 45
        }
      },
      "66": {
        start: {
          line: 364,
          column: 10
        },
        end: {
          line: 364,
          column: 73
        }
      },
      "67": {
        start: {
          line: 367,
          column: 25
        },
        end: {
          line: 367,
          column: 46
        }
      },
      "68": {
        start: {
          line: 370,
          column: 32
        },
        end: {
          line: 385,
          column: 19
        }
      },
      "69": {
        start: {
          line: 387,
          column: 8
        },
        end: {
          line: 387,
          column: 31
        }
      },
      "70": {
        start: {
          line: 387,
          column: 19
        },
        end: {
          line: 387,
          column: 31
        }
      },
      "71": {
        start: {
          line: 388,
          column: 8
        },
        end: {
          line: 388,
          column: 20
        }
      },
      "72": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 393,
          column: 26
        }
      },
      "73": {
        start: {
          line: 400,
          column: 19
        },
        end: {
          line: 414,
          column: 5
        }
      },
      "74": {
        start: {
          line: 402,
          column: 32
        },
        end: {
          line: 407,
          column: 19
        }
      },
      "75": {
        start: {
          line: 409,
          column: 8
        },
        end: {
          line: 409,
          column: 60
        }
      },
      "76": {
        start: {
          line: 409,
          column: 48
        },
        end: {
          line: 409,
          column: 60
        }
      },
      "77": {
        start: {
          line: 410,
          column: 8
        },
        end: {
          line: 410,
          column: 20
        }
      },
      "78": {
        start: {
          line: 415,
          column: 4
        },
        end: {
          line: 415,
          column: 26
        }
      },
      "79": {
        start: {
          line: 422,
          column: 19
        },
        end: {
          line: 436,
          column: 5
        }
      },
      "80": {
        start: {
          line: 424,
          column: 32
        },
        end: {
          line: 429,
          column: 23
        }
      },
      "81": {
        start: {
          line: 431,
          column: 8
        },
        end: {
          line: 431,
          column: 31
        }
      },
      "82": {
        start: {
          line: 431,
          column: 19
        },
        end: {
          line: 431,
          column: 31
        }
      },
      "83": {
        start: {
          line: 432,
          column: 8
        },
        end: {
          line: 432,
          column: 26
        }
      },
      "84": {
        start: {
          line: 437,
          column: 4
        },
        end: {
          line: 437,
          column: 24
        }
      },
      "85": {
        start: {
          line: 444,
          column: 19
        },
        end: {
          line: 467,
          column: 5
        }
      },
      "86": {
        start: {
          line: 446,
          column: 25
        },
        end: {
          line: 456,
          column: 10
        }
      },
      "87": {
        start: {
          line: 458,
          column: 8
        },
        end: {
          line: 461,
          column: 9
        }
      },
      "88": {
        start: {
          line: 459,
          column: 24
        },
        end: {
          line: 459,
          column: 45
        }
      },
      "89": {
        start: {
          line: 460,
          column: 10
        },
        end: {
          line: 460,
          column: 71
        }
      },
      "90": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 463,
          column: 20
        }
      },
      "91": {
        start: {
          line: 468,
          column: 4
        },
        end: {
          line: 468,
          column: 27
        }
      },
      "92": {
        start: {
          line: 479,
          column: 19
        },
        end: {
          line: 510,
          column: 5
        }
      },
      "93": {
        start: {
          line: 481,
          column: 29
        },
        end: {
          line: 481,
          column: 67
        }
      },
      "94": {
        start: {
          line: 483,
          column: 8
        },
        end: {
          line: 485,
          column: 9
        }
      },
      "95": {
        start: {
          line: 484,
          column: 10
        },
        end: {
          line: 484,
          column: 36
        }
      },
      "96": {
        start: {
          line: 487,
          column: 24
        },
        end: {
          line: 487,
          column: 63
        }
      },
      "97": {
        start: {
          line: 488,
          column: 20
        },
        end: {
          line: 488,
          column: 30
        }
      },
      "98": {
        start: {
          line: 489,
          column: 30
        },
        end: {
          line: 489,
          column: 100
        }
      },
      "99": {
        start: {
          line: 491,
          column: 8
        },
        end: {
          line: 493,
          column: 9
        }
      },
      "100": {
        start: {
          line: 492,
          column: 10
        },
        end: {
          line: 492,
          column: 36
        }
      },
      "101": {
        start: {
          line: 496,
          column: 31
        },
        end: {
          line: 500,
          column: 19
        }
      },
      "102": {
        start: {
          line: 502,
          column: 8
        },
        end: {
          line: 506,
          column: 10
        }
      },
      "103": {
        start: {
          line: 511,
          column: 4
        },
        end: {
          line: 511,
          column: 40
        }
      },
      "104": {
        start: {
          line: 518,
          column: 34
        },
        end: {
          line: 518,
          column: 66
        }
      },
      "105": {
        start: {
          line: 519,
          column: 4
        },
        end: {
          line: 519,
          column: 39
        }
      },
      "106": {
        start: {
          line: 523,
          column: 30
        },
        end: {
          line: 523,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 85,
            column: 2
          },
          end: {
            line: 85,
            column: 3
          }
        },
        loc: {
          start: {
            line: 85,
            column: 16
          },
          end: {
            line: 88,
            column: 3
          }
        },
        line: 85
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 93,
            column: 2
          },
          end: {
            line: 93,
            column: 3
          }
        },
        loc: {
          start: {
            line: 93,
            column: 45
          },
          end: {
            line: 113,
            column: 3
          }
        },
        line: 93
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 95,
            column: 6
          },
          end: {
            line: 95,
            column: 7
          }
        },
        loc: {
          start: {
            line: 95,
            column: 18
          },
          end: {
            line: 108,
            column: 7
          }
        },
        line: 95
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 118,
            column: 2
          },
          end: {
            line: 118,
            column: 3
          }
        },
        loc: {
          start: {
            line: 118,
            column: 60
          },
          end: {
            line: 134,
            column: 3
          }
        },
        line: 118
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 120,
            column: 6
          },
          end: {
            line: 120,
            column: 7
          }
        },
        loc: {
          start: {
            line: 120,
            column: 18
          },
          end: {
            line: 129,
            column: 7
          }
        },
        line: 120
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 139,
            column: 2
          },
          end: {
            line: 139,
            column: 3
          }
        },
        loc: {
          start: {
            line: 139,
            column: 114
          },
          end: {
            line: 186,
            column: 3
          }
        },
        line: 139
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 141,
            column: 6
          },
          end: {
            line: 141,
            column: 7
          }
        },
        loc: {
          start: {
            line: 141,
            column: 18
          },
          end: {
            line: 181,
            column: 7
          }
        },
        line: 141
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 191,
            column: 2
          },
          end: {
            line: 191,
            column: 3
          }
        },
        loc: {
          start: {
            line: 191,
            column: 96
          },
          end: {
            line: 226,
            column: 3
          }
        },
        line: 191
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 193,
            column: 6
          },
          end: {
            line: 193,
            column: 7
          }
        },
        loc: {
          start: {
            line: 193,
            column: 18
          },
          end: {
            line: 221,
            column: 7
          }
        },
        line: 193
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 231,
            column: 2
          },
          end: {
            line: 231,
            column: 3
          }
        },
        loc: {
          start: {
            line: 231,
            column: 92
          },
          end: {
            line: 263,
            column: 3
          }
        },
        line: 231
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 233,
            column: 6
          },
          end: {
            line: 233,
            column: 7
          }
        },
        loc: {
          start: {
            line: 233,
            column: 18
          },
          end: {
            line: 258,
            column: 7
          }
        },
        line: 233
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 268,
            column: 2
          },
          end: {
            line: 268,
            column: 3
          }
        },
        loc: {
          start: {
            line: 268,
            column: 96
          },
          end: {
            line: 314,
            column: 3
          }
        },
        line: 268
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 270,
            column: 6
          },
          end: {
            line: 270,
            column: 7
          }
        },
        loc: {
          start: {
            line: 270,
            column: 18
          },
          end: {
            line: 309,
            column: 7
          }
        },
        line: 270
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 319,
            column: 2
          },
          end: {
            line: 319,
            column: 3
          }
        },
        loc: {
          start: {
            line: 319,
            column: 68
          },
          end: {
            line: 335,
            column: 3
          }
        },
        line: 319
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 321,
            column: 6
          },
          end: {
            line: 321,
            column: 7
          }
        },
        loc: {
          start: {
            line: 321,
            column: 18
          },
          end: {
            line: 330,
            column: 7
          }
        },
        line: 321
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 340,
            column: 2
          },
          end: {
            line: 340,
            column: 3
          }
        },
        loc: {
          start: {
            line: 347,
            column: 31
          },
          end: {
            line: 394,
            column: 3
          }
        },
        line: 347
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 349,
            column: 6
          },
          end: {
            line: 349,
            column: 7
          }
        },
        loc: {
          start: {
            line: 349,
            column: 18
          },
          end: {
            line: 389,
            column: 7
          }
        },
        line: 349
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 399,
            column: 2
          },
          end: {
            line: 399,
            column: 3
          }
        },
        loc: {
          start: {
            line: 399,
            column: 74
          },
          end: {
            line: 416,
            column: 3
          }
        },
        line: 399
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 401,
            column: 6
          },
          end: {
            line: 401,
            column: 7
          }
        },
        loc: {
          start: {
            line: 401,
            column: 18
          },
          end: {
            line: 411,
            column: 7
          }
        },
        line: 401
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 421,
            column: 2
          },
          end: {
            line: 421,
            column: 3
          }
        },
        loc: {
          start: {
            line: 421,
            column: 81
          },
          end: {
            line: 438,
            column: 3
          }
        },
        line: 421
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 423,
            column: 6
          },
          end: {
            line: 423,
            column: 7
          }
        },
        loc: {
          start: {
            line: 423,
            column: 18
          },
          end: {
            line: 433,
            column: 7
          }
        },
        line: 423
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 443,
            column: 2
          },
          end: {
            line: 443,
            column: 3
          }
        },
        loc: {
          start: {
            line: 443,
            column: 76
          },
          end: {
            line: 469,
            column: 3
          }
        },
        line: 443
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 445,
            column: 6
          },
          end: {
            line: 445,
            column: 7
          }
        },
        loc: {
          start: {
            line: 445,
            column: 18
          },
          end: {
            line: 464,
            column: 7
          }
        },
        line: 445
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 474,
            column: 2
          },
          end: {
            line: 474,
            column: 3
          }
        },
        loc: {
          start: {
            line: 478,
            column: 5
          },
          end: {
            line: 512,
            column: 3
          }
        },
        line: 478
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 480,
            column: 6
          },
          end: {
            line: 480,
            column: 7
          }
        },
        loc: {
          start: {
            line: 480,
            column: 18
          },
          end: {
            line: 507,
            column: 7
          }
        },
        line: 480
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 517,
            column: 2
          },
          end: {
            line: 517,
            column: 3
          }
        },
        loc: {
          start: {
            line: 517,
            column: 48
          },
          end: {
            line: 520,
            column: 3
          }
        },
        line: 517
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 86,
            column: 32
          },
          end: {
            line: 86,
            column: 84
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 86,
            column: 32
          },
          end: {
            line: 86,
            column: 78
          }
        }, {
          start: {
            line: 86,
            column: 82
          },
          end: {
            line: 86,
            column: 84
          }
        }],
        line: 86
      },
      "1": {
        loc: {
          start: {
            line: 87,
            column: 22
          },
          end: {
            line: 87,
            column: 82
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 87,
            column: 22
          },
          end: {
            line: 87,
            column: 53
          }
        }, {
          start: {
            line: 87,
            column: 57
          },
          end: {
            line: 87,
            column: 82
          }
        }],
        line: 87
      },
      "2": {
        loc: {
          start: {
            line: 96,
            column: 8
          },
          end: {
            line: 98,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 8
          },
          end: {
            line: 98,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "3": {
        loc: {
          start: {
            line: 112,
            column: 11
          },
          end: {
            line: 112,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 112,
            column: 11
          },
          end: {
            line: 112,
            column: 17
          }
        }, {
          start: {
            line: 112,
            column: 21
          },
          end: {
            line: 112,
            column: 26
          }
        }],
        line: 112
      },
      "4": {
        loc: {
          start: {
            line: 127,
            column: 8
          },
          end: {
            line: 127,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 8
          },
          end: {
            line: 127,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "5": {
        loc: {
          start: {
            line: 128,
            column: 15
          },
          end: {
            line: 128,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 128,
            column: 15
          },
          end: {
            line: 128,
            column: 19
          }
        }, {
          start: {
            line: 128,
            column: 23
          },
          end: {
            line: 128,
            column: 25
          }
        }],
        line: 128
      },
      "6": {
        loc: {
          start: {
            line: 133,
            column: 11
          },
          end: {
            line: 133,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 133,
            column: 11
          },
          end: {
            line: 133,
            column: 17
          }
        }, {
          start: {
            line: 133,
            column: 21
          },
          end: {
            line: 133,
            column: 23
          }
        }],
        line: 133
      },
      "7": {
        loc: {
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 158,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 155,
            column: 8
          },
          end: {
            line: 158,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 155
      },
      "8": {
        loc: {
          start: {
            line: 157,
            column: 26
          },
          end: {
            line: 157,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 157,
            column: 26
          },
          end: {
            line: 157,
            column: 39
          }
        }, {
          start: {
            line: 157,
            column: 43
          },
          end: {
            line: 157,
            column: 74
          }
        }],
        line: 157
      },
      "9": {
        loc: {
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 179,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 179,
            column: 8
          },
          end: {
            line: 179,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 179
      },
      "10": {
        loc: {
          start: {
            line: 185,
            column: 11
          },
          end: {
            line: 185,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 185,
            column: 11
          },
          end: {
            line: 185,
            column: 17
          }
        }, {
          start: {
            line: 185,
            column: 21
          },
          end: {
            line: 185,
            column: 25
          }
        }],
        line: 185
      },
      "11": {
        loc: {
          start: {
            line: 191,
            column: 51
          },
          end: {
            line: 191,
            column: 76
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 191,
            column: 71
          },
          end: {
            line: 191,
            column: 76
          }
        }],
        line: 191
      },
      "12": {
        loc: {
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 208,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 208,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "13": {
        loc: {
          start: {
            line: 207,
            column: 26
          },
          end: {
            line: 207,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 207,
            column: 26
          },
          end: {
            line: 207,
            column: 39
          }
        }, {
          start: {
            line: 207,
            column: 43
          },
          end: {
            line: 207,
            column: 74
          }
        }],
        line: 207
      },
      "14": {
        loc: {
          start: {
            line: 215,
            column: 20
          },
          end: {
            line: 215,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 215,
            column: 40
          },
          end: {
            line: 215,
            column: 50
          }
        }, {
          start: {
            line: 215,
            column: 53
          },
          end: {
            line: 215,
            column: 61
          }
        }],
        line: 215
      },
      "15": {
        loc: {
          start: {
            line: 219,
            column: 8
          },
          end: {
            line: 219,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 8
          },
          end: {
            line: 219,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 219
      },
      "16": {
        loc: {
          start: {
            line: 225,
            column: 11
          },
          end: {
            line: 225,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 225,
            column: 11
          },
          end: {
            line: 225,
            column: 17
          }
        }, {
          start: {
            line: 225,
            column: 21
          },
          end: {
            line: 225,
            column: 26
          }
        }],
        line: 225
      },
      "17": {
        loc: {
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 248,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 248,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "18": {
        loc: {
          start: {
            line: 247,
            column: 26
          },
          end: {
            line: 247,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 247,
            column: 26
          },
          end: {
            line: 247,
            column: 39
          }
        }, {
          start: {
            line: 247,
            column: 43
          },
          end: {
            line: 247,
            column: 74
          }
        }],
        line: 247
      },
      "19": {
        loc: {
          start: {
            line: 256,
            column: 8
          },
          end: {
            line: 256,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 256,
            column: 8
          },
          end: {
            line: 256,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 256
      },
      "20": {
        loc: {
          start: {
            line: 262,
            column: 11
          },
          end: {
            line: 262,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 262,
            column: 11
          },
          end: {
            line: 262,
            column: 17
          }
        }, {
          start: {
            line: 262,
            column: 21
          },
          end: {
            line: 262,
            column: 26
          }
        }],
        line: 262
      },
      "21": {
        loc: {
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 283,
            column: 8
          },
          end: {
            line: 286,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 283
      },
      "22": {
        loc: {
          start: {
            line: 285,
            column: 26
          },
          end: {
            line: 285,
            column: 73
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 26
          },
          end: {
            line: 285,
            column: 39
          }
        }, {
          start: {
            line: 285,
            column: 43
          },
          end: {
            line: 285,
            column: 73
          }
        }],
        line: 285
      },
      "23": {
        loc: {
          start: {
            line: 307,
            column: 8
          },
          end: {
            line: 307,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 307,
            column: 8
          },
          end: {
            line: 307,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 307
      },
      "24": {
        loc: {
          start: {
            line: 313,
            column: 11
          },
          end: {
            line: 313,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 313,
            column: 11
          },
          end: {
            line: 313,
            column: 17
          }
        }, {
          start: {
            line: 313,
            column: 21
          },
          end: {
            line: 313,
            column: 25
          }
        }],
        line: 313
      },
      "25": {
        loc: {
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 328,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 328,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "26": {
        loc: {
          start: {
            line: 329,
            column: 15
          },
          end: {
            line: 329,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 329,
            column: 15
          },
          end: {
            line: 329,
            column: 19
          }
        }, {
          start: {
            line: 329,
            column: 23
          },
          end: {
            line: 329,
            column: 25
          }
        }],
        line: 329
      },
      "27": {
        loc: {
          start: {
            line: 334,
            column: 11
          },
          end: {
            line: 334,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 334,
            column: 11
          },
          end: {
            line: 334,
            column: 17
          }
        }, {
          start: {
            line: 334,
            column: 21
          },
          end: {
            line: 334,
            column: 23
          }
        }],
        line: 334
      },
      "28": {
        loc: {
          start: {
            line: 362,
            column: 8
          },
          end: {
            line: 365,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 362,
            column: 8
          },
          end: {
            line: 365,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 362
      },
      "29": {
        loc: {
          start: {
            line: 364,
            column: 26
          },
          end: {
            line: 364,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 364,
            column: 26
          },
          end: {
            line: 364,
            column: 39
          }
        }, {
          start: {
            line: 364,
            column: 43
          },
          end: {
            line: 364,
            column: 71
          }
        }],
        line: 364
      },
      "30": {
        loc: {
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 387,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 387,
            column: 8
          },
          end: {
            line: 387,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 387
      },
      "31": {
        loc: {
          start: {
            line: 393,
            column: 11
          },
          end: {
            line: 393,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 393,
            column: 11
          },
          end: {
            line: 393,
            column: 17
          }
        }, {
          start: {
            line: 393,
            column: 21
          },
          end: {
            line: 393,
            column: 25
          }
        }],
        line: 393
      },
      "32": {
        loc: {
          start: {
            line: 409,
            column: 8
          },
          end: {
            line: 409,
            column: 60
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 409,
            column: 8
          },
          end: {
            line: 409,
            column: 60
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 409
      },
      "33": {
        loc: {
          start: {
            line: 409,
            column: 12
          },
          end: {
            line: 409,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 409,
            column: 12
          },
          end: {
            line: 409,
            column: 17
          }
        }, {
          start: {
            line: 409,
            column: 21
          },
          end: {
            line: 409,
            column: 46
          }
        }],
        line: 409
      },
      "34": {
        loc: {
          start: {
            line: 415,
            column: 11
          },
          end: {
            line: 415,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 415,
            column: 11
          },
          end: {
            line: 415,
            column: 17
          }
        }, {
          start: {
            line: 415,
            column: 21
          },
          end: {
            line: 415,
            column: 25
          }
        }],
        line: 415
      },
      "35": {
        loc: {
          start: {
            line: 421,
            column: 42
          },
          end: {
            line: 421,
            column: 52
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 421,
            column: 50
          },
          end: {
            line: 421,
            column: 52
          }
        }],
        line: 421
      },
      "36": {
        loc: {
          start: {
            line: 431,
            column: 8
          },
          end: {
            line: 431,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 431,
            column: 8
          },
          end: {
            line: 431,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 431
      },
      "37": {
        loc: {
          start: {
            line: 432,
            column: 15
          },
          end: {
            line: 432,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 432,
            column: 15
          },
          end: {
            line: 432,
            column: 19
          }
        }, {
          start: {
            line: 432,
            column: 23
          },
          end: {
            line: 432,
            column: 25
          }
        }],
        line: 432
      },
      "38": {
        loc: {
          start: {
            line: 437,
            column: 11
          },
          end: {
            line: 437,
            column: 23
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 437,
            column: 11
          },
          end: {
            line: 437,
            column: 17
          }
        }, {
          start: {
            line: 437,
            column: 21
          },
          end: {
            line: 437,
            column: 23
          }
        }],
        line: 437
      },
      "39": {
        loc: {
          start: {
            line: 458,
            column: 8
          },
          end: {
            line: 461,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 458,
            column: 8
          },
          end: {
            line: 461,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 458
      },
      "40": {
        loc: {
          start: {
            line: 460,
            column: 26
          },
          end: {
            line: 460,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 460,
            column: 26
          },
          end: {
            line: 460,
            column: 39
          }
        }, {
          start: {
            line: 460,
            column: 43
          },
          end: {
            line: 460,
            column: 69
          }
        }],
        line: 460
      },
      "41": {
        loc: {
          start: {
            line: 468,
            column: 11
          },
          end: {
            line: 468,
            column: 26
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 468,
            column: 11
          },
          end: {
            line: 468,
            column: 17
          }
        }, {
          start: {
            line: 468,
            column: 21
          },
          end: {
            line: 468,
            column: 26
          }
        }],
        line: 468
      },
      "42": {
        loc: {
          start: {
            line: 483,
            column: 8
          },
          end: {
            line: 485,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 483,
            column: 8
          },
          end: {
            line: 485,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 483
      },
      "43": {
        loc: {
          start: {
            line: 491,
            column: 8
          },
          end: {
            line: 493,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 491,
            column: 8
          },
          end: {
            line: 493,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 491
      },
      "44": {
        loc: {
          start: {
            line: 491,
            column: 12
          },
          end: {
            line: 491,
            column: 66
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 491,
            column: 12
          },
          end: {
            line: 491,
            column: 30
          }
        }, {
          start: {
            line: 491,
            column: 34
          },
          end: {
            line: 491,
            column: 66
          }
        }],
        line: 491
      },
      "45": {
        loc: {
          start: {
            line: 511,
            column: 11
          },
          end: {
            line: 511,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 511,
            column: 11
          },
          end: {
            line: 511,
            column: 17
          }
        }, {
          start: {
            line: 511,
            column: 21
          },
          end: {
            line: 511,
            column: 39
          }
        }],
        line: 511
      },
      "46": {
        loc: {
          start: {
            line: 519,
            column: 11
          },
          end: {
            line: 519,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 519,
            column: 11
          },
          end: {
            line: 519,
            column: 32
          }
        }, {
          start: {
            line: 519,
            column: 36
          },
          end: {
            line: 519,
            column: 38
          }
        }],
        line: 519
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "772213d562d108374261d767d84727316acaacc5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1vxmo407f4 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1vxmo407f4();
import { withErrorHandling, createValidationError } from "../utils/errorHandler";
import { supabase } from "../lib/supabase";
var PaymentService = function () {
  function PaymentService() {
    _classCallCheck(this, PaymentService);
    cov_1vxmo407f4().f[0]++;
    cov_1vxmo407f4().s[0]++;
    this.stripePublishableKey = (cov_1vxmo407f4().b[0][0]++, _env.EXPO_PUBLIC_STRIPE_PUBLISHABLE_KEY) || (cov_1vxmo407f4().b[0][1]++, '');
    cov_1vxmo407f4().s[1]++;
    this.apiBaseUrl = (cov_1vxmo407f4().b[1][0]++, _env.EXPO_PUBLIC_API_URL) || (cov_1vxmo407f4().b[1][1]++, 'https://api.acemind.app');
  }
  return _createClass(PaymentService, [{
    key: "initializeStripe",
    value: (function () {
      var _initializeStripe = _asyncToGenerator(function* () {
        var _this = this,
          _ref2;
        cov_1vxmo407f4().f[1]++;
        var result = (cov_1vxmo407f4().s[2]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[2]++;
          cov_1vxmo407f4().s[3]++;
          if (!_this.stripePublishableKey) {
            cov_1vxmo407f4().b[2][0]++;
            cov_1vxmo407f4().s[4]++;
            throw createValidationError('stripe_key', 'Stripe publishable key not configured');
          } else {
            cov_1vxmo407f4().b[2][1]++;
          }
          cov_1vxmo407f4().s[5]++;
          console.log('Stripe initialized successfully');
          cov_1vxmo407f4().s[6]++;
          return true;
        }), {
          service: 'Payment',
          action: 'initializeStripe'
        }, {
          showUserError: true
        }));
        cov_1vxmo407f4().s[7]++;
        return (_ref2 = (cov_1vxmo407f4().b[3][0]++, result)) != null ? _ref2 : (cov_1vxmo407f4().b[3][1]++, false);
      });
      function initializeStripe() {
        return _initializeStripe.apply(this, arguments);
      }
      return initializeStripe;
    }())
  }, {
    key: "getSubscriptionPlans",
    value: (function () {
      var _getSubscriptionPlans = _asyncToGenerator(function* () {
        var _ref5;
        cov_1vxmo407f4().f[3]++;
        var result = (cov_1vxmo407f4().s[8]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[4]++;
          var _ref4 = (cov_1vxmo407f4().s[9]++, yield supabase.from('subscription_plans').select('*').eq('is_active', true).order('price', {
              ascending: true
            })),
            data = _ref4.data,
            error = _ref4.error;
          cov_1vxmo407f4().s[10]++;
          if (error) {
            cov_1vxmo407f4().b[4][0]++;
            cov_1vxmo407f4().s[11]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[4][1]++;
          }
          cov_1vxmo407f4().s[12]++;
          return (cov_1vxmo407f4().b[5][0]++, data) || (cov_1vxmo407f4().b[5][1]++, []);
        }), {
          service: 'Payment',
          action: 'getSubscriptionPlans'
        }, {
          showUserError: false
        }));
        cov_1vxmo407f4().s[13]++;
        return (_ref5 = (cov_1vxmo407f4().b[6][0]++, result)) != null ? _ref5 : (cov_1vxmo407f4().b[6][1]++, []);
      });
      function getSubscriptionPlans() {
        return _getSubscriptionPlans.apply(this, arguments);
      }
      return getSubscriptionPlans;
    }())
  }, {
    key: "createSubscription",
    value: (function () {
      var _createSubscription = _asyncToGenerator(function* (userId, planId, paymentMethodId) {
        var _this2 = this,
          _ref8;
        cov_1vxmo407f4().f[5]++;
        var result = (cov_1vxmo407f4().s[14]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[6]++;
          var response = (cov_1vxmo407f4().s[15]++, yield fetch(`${_this2.apiBaseUrl}/payments/subscriptions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${yield _this2.getAuthToken()}`
            },
            body: JSON.stringify({
              userId: userId,
              planId: planId,
              paymentMethodId: paymentMethodId
            })
          }));
          cov_1vxmo407f4().s[16]++;
          if (!response.ok) {
            cov_1vxmo407f4().b[7][0]++;
            var _error = (cov_1vxmo407f4().s[17]++, yield response.json());
            cov_1vxmo407f4().s[18]++;
            throw new Error((cov_1vxmo407f4().b[8][0]++, _error.message) || (cov_1vxmo407f4().b[8][1]++, 'Failed to create subscription'));
          } else {
            cov_1vxmo407f4().b[7][1]++;
          }
          var subscription = (cov_1vxmo407f4().s[19]++, yield response.json());
          var _ref7 = (cov_1vxmo407f4().s[20]++, yield supabase.from('subscriptions').insert({
              id: subscription.id,
              user_id: userId,
              plan_id: planId,
              status: subscription.status,
              current_period_start: subscription.current_period_start,
              current_period_end: subscription.current_period_end,
              cancel_at_period_end: subscription.cancel_at_period_end,
              stripe_subscription_id: subscription.stripe_subscription_id,
              payment_method_id: paymentMethodId
            }).select().single()),
            data = _ref7.data,
            error = _ref7.error;
          cov_1vxmo407f4().s[21]++;
          if (error) {
            cov_1vxmo407f4().b[9][0]++;
            cov_1vxmo407f4().s[22]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[9][1]++;
          }
          cov_1vxmo407f4().s[23]++;
          return data;
        }), {
          service: 'Payment',
          action: 'createSubscription',
          userId: userId,
          planId: planId
        }, {
          showUserError: true
        }));
        cov_1vxmo407f4().s[24]++;
        return (_ref8 = (cov_1vxmo407f4().b[10][0]++, result)) != null ? _ref8 : (cov_1vxmo407f4().b[10][1]++, null);
      });
      function createSubscription(_x, _x2, _x3) {
        return _createSubscription.apply(this, arguments);
      }
      return createSubscription;
    }())
  }, {
    key: "cancelSubscription",
    value: (function () {
      var _cancelSubscription = _asyncToGenerator(function* (subscriptionId) {
        var _this3 = this,
          _ref1;
        var cancelImmediately = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1vxmo407f4().b[11][0]++, false);
        cov_1vxmo407f4().f[7]++;
        var result = (cov_1vxmo407f4().s[25]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[8]++;
          var response = (cov_1vxmo407f4().s[26]++, yield fetch(`${_this3.apiBaseUrl}/payments/subscriptions/${subscriptionId}/cancel`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${yield _this3.getAuthToken()}`
            },
            body: JSON.stringify({
              cancelImmediately: cancelImmediately
            })
          }));
          cov_1vxmo407f4().s[27]++;
          if (!response.ok) {
            cov_1vxmo407f4().b[12][0]++;
            var _error2 = (cov_1vxmo407f4().s[28]++, yield response.json());
            cov_1vxmo407f4().s[29]++;
            throw new Error((cov_1vxmo407f4().b[13][0]++, _error2.message) || (cov_1vxmo407f4().b[13][1]++, 'Failed to cancel subscription'));
          } else {
            cov_1vxmo407f4().b[12][1]++;
          }
          var _ref0 = (cov_1vxmo407f4().s[30]++, yield supabase.from('subscriptions').update({
              cancel_at_period_end: !cancelImmediately,
              status: cancelImmediately ? (cov_1vxmo407f4().b[14][0]++, 'canceled') : (cov_1vxmo407f4().b[14][1]++, 'active')
            }).eq('id', subscriptionId)),
            error = _ref0.error;
          cov_1vxmo407f4().s[31]++;
          if (error) {
            cov_1vxmo407f4().b[15][0]++;
            cov_1vxmo407f4().s[32]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[15][1]++;
          }
          cov_1vxmo407f4().s[33]++;
          return true;
        }), {
          service: 'Payment',
          action: 'cancelSubscription',
          subscriptionId: subscriptionId
        }, {
          showUserError: true
        }));
        cov_1vxmo407f4().s[34]++;
        return (_ref1 = (cov_1vxmo407f4().b[16][0]++, result)) != null ? _ref1 : (cov_1vxmo407f4().b[16][1]++, false);
      });
      function cancelSubscription(_x4) {
        return _cancelSubscription.apply(this, arguments);
      }
      return cancelSubscription;
    }())
  }, {
    key: "updateSubscriptionPlan",
    value: (function () {
      var _updateSubscriptionPlan = _asyncToGenerator(function* (subscriptionId, newPlanId) {
        var _this4 = this,
          _ref12;
        cov_1vxmo407f4().f[9]++;
        var result = (cov_1vxmo407f4().s[35]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[10]++;
          var response = (cov_1vxmo407f4().s[36]++, yield fetch(`${_this4.apiBaseUrl}/payments/subscriptions/${subscriptionId}/update`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${yield _this4.getAuthToken()}`
            },
            body: JSON.stringify({
              planId: newPlanId
            })
          }));
          cov_1vxmo407f4().s[37]++;
          if (!response.ok) {
            cov_1vxmo407f4().b[17][0]++;
            var _error3 = (cov_1vxmo407f4().s[38]++, yield response.json());
            cov_1vxmo407f4().s[39]++;
            throw new Error((cov_1vxmo407f4().b[18][0]++, _error3.message) || (cov_1vxmo407f4().b[18][1]++, 'Failed to update subscription'));
          } else {
            cov_1vxmo407f4().b[17][1]++;
          }
          var _ref11 = (cov_1vxmo407f4().s[40]++, yield supabase.from('subscriptions').update({
              plan_id: newPlanId
            }).eq('id', subscriptionId)),
            error = _ref11.error;
          cov_1vxmo407f4().s[41]++;
          if (error) {
            cov_1vxmo407f4().b[19][0]++;
            cov_1vxmo407f4().s[42]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[19][1]++;
          }
          cov_1vxmo407f4().s[43]++;
          return true;
        }), {
          service: 'Payment',
          action: 'updateSubscriptionPlan',
          subscriptionId: subscriptionId,
          newPlanId: newPlanId
        }, {
          showUserError: true
        }));
        cov_1vxmo407f4().s[44]++;
        return (_ref12 = (cov_1vxmo407f4().b[20][0]++, result)) != null ? _ref12 : (cov_1vxmo407f4().b[20][1]++, false);
      });
      function updateSubscriptionPlan(_x5, _x6) {
        return _updateSubscriptionPlan.apply(this, arguments);
      }
      return updateSubscriptionPlan;
    }())
  }, {
    key: "addPaymentMethod",
    value: (function () {
      var _addPaymentMethod = _asyncToGenerator(function* (userId, paymentMethodData) {
        var _this5 = this,
          _ref15;
        cov_1vxmo407f4().f[11]++;
        var result = (cov_1vxmo407f4().s[45]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[12]++;
          var response = (cov_1vxmo407f4().s[46]++, yield fetch(`${_this5.apiBaseUrl}/payments/payment-methods`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${yield _this5.getAuthToken()}`
            },
            body: JSON.stringify(Object.assign({
              userId: userId
            }, paymentMethodData))
          }));
          cov_1vxmo407f4().s[47]++;
          if (!response.ok) {
            cov_1vxmo407f4().b[21][0]++;
            var _error4 = (cov_1vxmo407f4().s[48]++, yield response.json());
            cov_1vxmo407f4().s[49]++;
            throw new Error((cov_1vxmo407f4().b[22][0]++, _error4.message) || (cov_1vxmo407f4().b[22][1]++, 'Failed to add payment method'));
          } else {
            cov_1vxmo407f4().b[21][1]++;
          }
          var paymentMethod = (cov_1vxmo407f4().s[50]++, yield response.json());
          var _ref14 = (cov_1vxmo407f4().s[51]++, yield supabase.from('payment_methods').insert({
              id: paymentMethod.id,
              user_id: userId,
              type: paymentMethod.type,
              last4: paymentMethod.last4,
              brand: paymentMethod.brand,
              expiry_month: paymentMethod.expiry_month,
              expiry_year: paymentMethod.expiry_year,
              is_default: paymentMethod.is_default,
              billing_address: paymentMethod.billing_address
            }).select().single()),
            data = _ref14.data,
            error = _ref14.error;
          cov_1vxmo407f4().s[52]++;
          if (error) {
            cov_1vxmo407f4().b[23][0]++;
            cov_1vxmo407f4().s[53]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[23][1]++;
          }
          cov_1vxmo407f4().s[54]++;
          return data;
        }), {
          service: 'Payment',
          action: 'addPaymentMethod',
          userId: userId
        }, {
          showUserError: true
        }));
        cov_1vxmo407f4().s[55]++;
        return (_ref15 = (cov_1vxmo407f4().b[24][0]++, result)) != null ? _ref15 : (cov_1vxmo407f4().b[24][1]++, null);
      });
      function addPaymentMethod(_x7, _x8) {
        return _addPaymentMethod.apply(this, arguments);
      }
      return addPaymentMethod;
    }())
  }, {
    key: "getPaymentMethods",
    value: (function () {
      var _getPaymentMethods = _asyncToGenerator(function* (userId) {
        var _ref18;
        cov_1vxmo407f4().f[13]++;
        var result = (cov_1vxmo407f4().s[56]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[14]++;
          var _ref17 = (cov_1vxmo407f4().s[57]++, yield supabase.from('payment_methods').select('*').eq('user_id', userId).order('is_default', {
              ascending: false
            })),
            data = _ref17.data,
            error = _ref17.error;
          cov_1vxmo407f4().s[58]++;
          if (error) {
            cov_1vxmo407f4().b[25][0]++;
            cov_1vxmo407f4().s[59]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[25][1]++;
          }
          cov_1vxmo407f4().s[60]++;
          return (cov_1vxmo407f4().b[26][0]++, data) || (cov_1vxmo407f4().b[26][1]++, []);
        }), {
          service: 'Payment',
          action: 'getPaymentMethods',
          userId: userId
        }, {
          showUserError: false
        }));
        cov_1vxmo407f4().s[61]++;
        return (_ref18 = (cov_1vxmo407f4().b[27][0]++, result)) != null ? _ref18 : (cov_1vxmo407f4().b[27][1]++, []);
      });
      function getPaymentMethods(_x9) {
        return _getPaymentMethods.apply(this, arguments);
      }
      return getPaymentMethods;
    }())
  }, {
    key: "processPurchase",
    value: (function () {
      var _processPurchase = _asyncToGenerator(function* (userId, purchaseData) {
        var _this6 = this,
          _ref21;
        cov_1vxmo407f4().f[15]++;
        var result = (cov_1vxmo407f4().s[62]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[16]++;
          var response = (cov_1vxmo407f4().s[63]++, yield fetch(`${_this6.apiBaseUrl}/payments/purchases`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${yield _this6.getAuthToken()}`
            },
            body: JSON.stringify(Object.assign({
              userId: userId
            }, purchaseData))
          }));
          cov_1vxmo407f4().s[64]++;
          if (!response.ok) {
            cov_1vxmo407f4().b[28][0]++;
            var _error5 = (cov_1vxmo407f4().s[65]++, yield response.json());
            cov_1vxmo407f4().s[66]++;
            throw new Error((cov_1vxmo407f4().b[29][0]++, _error5.message) || (cov_1vxmo407f4().b[29][1]++, 'Failed to process purchase'));
          } else {
            cov_1vxmo407f4().b[28][1]++;
          }
          var purchase = (cov_1vxmo407f4().s[67]++, yield response.json());
          var _ref20 = (cov_1vxmo407f4().s[68]++, yield supabase.from('purchases').insert({
              id: purchase.id,
              user_id: userId,
              type: purchaseData.type,
              item_id: purchaseData.itemId,
              amount: purchaseData.amount,
              currency: purchaseData.currency,
              status: purchase.status,
              stripe_payment_intent_id: purchase.stripe_payment_intent_id,
              metadata: purchaseData.metadata,
              created_at: new Date().toISOString()
            }).select().single()),
            data = _ref20.data,
            error = _ref20.error;
          cov_1vxmo407f4().s[69]++;
          if (error) {
            cov_1vxmo407f4().b[30][0]++;
            cov_1vxmo407f4().s[70]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[30][1]++;
          }
          cov_1vxmo407f4().s[71]++;
          return data;
        }), {
          service: 'Payment',
          action: 'processPurchase',
          userId: userId
        }, {
          showUserError: true
        }));
        cov_1vxmo407f4().s[72]++;
        return (_ref21 = (cov_1vxmo407f4().b[31][0]++, result)) != null ? _ref21 : (cov_1vxmo407f4().b[31][1]++, null);
      });
      function processPurchase(_x0, _x1) {
        return _processPurchase.apply(this, arguments);
      }
      return processPurchase;
    }())
  }, {
    key: "getUserSubscription",
    value: (function () {
      var _getUserSubscription = _asyncToGenerator(function* (userId) {
        var _ref24;
        cov_1vxmo407f4().f[17]++;
        var result = (cov_1vxmo407f4().s[73]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[18]++;
          var _ref23 = (cov_1vxmo407f4().s[74]++, yield supabase.from('subscriptions').select('*').eq('user_id', userId).eq('status', 'active').single()),
            data = _ref23.data,
            error = _ref23.error;
          cov_1vxmo407f4().s[75]++;
          if ((cov_1vxmo407f4().b[33][0]++, error) && (cov_1vxmo407f4().b[33][1]++, error.code !== 'PGRST116')) {
            cov_1vxmo407f4().b[32][0]++;
            cov_1vxmo407f4().s[76]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[32][1]++;
          }
          cov_1vxmo407f4().s[77]++;
          return data;
        }), {
          service: 'Payment',
          action: 'getUserSubscription',
          userId: userId
        }, {
          showUserError: false
        }));
        cov_1vxmo407f4().s[78]++;
        return (_ref24 = (cov_1vxmo407f4().b[34][0]++, result)) != null ? _ref24 : (cov_1vxmo407f4().b[34][1]++, null);
      });
      function getUserSubscription(_x10) {
        return _getUserSubscription.apply(this, arguments);
      }
      return getUserSubscription;
    }())
  }, {
    key: "getBillingHistory",
    value: (function () {
      var _getBillingHistory = _asyncToGenerator(function* (userId) {
        var _ref27;
        var limit = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1vxmo407f4().b[35][0]++, 50);
        cov_1vxmo407f4().f[19]++;
        var result = (cov_1vxmo407f4().s[79]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[20]++;
          var _ref26 = (cov_1vxmo407f4().s[80]++, yield supabase.from('billing_history').select('*').eq('user_id', userId).order('created_at', {
              ascending: false
            }).limit(limit)),
            data = _ref26.data,
            error = _ref26.error;
          cov_1vxmo407f4().s[81]++;
          if (error) {
            cov_1vxmo407f4().b[36][0]++;
            cov_1vxmo407f4().s[82]++;
            throw error;
          } else {
            cov_1vxmo407f4().b[36][1]++;
          }
          cov_1vxmo407f4().s[83]++;
          return (cov_1vxmo407f4().b[37][0]++, data) || (cov_1vxmo407f4().b[37][1]++, []);
        }), {
          service: 'Payment',
          action: 'getBillingHistory',
          userId: userId
        }, {
          showUserError: false
        }));
        cov_1vxmo407f4().s[84]++;
        return (_ref27 = (cov_1vxmo407f4().b[38][0]++, result)) != null ? _ref27 : (cov_1vxmo407f4().b[38][1]++, []);
      });
      function getBillingHistory(_x11) {
        return _getBillingHistory.apply(this, arguments);
      }
      return getBillingHistory;
    }())
  }, {
    key: "requestRefund",
    value: (function () {
      var _requestRefund = _asyncToGenerator(function* (purchaseId, reason) {
        var _this7 = this,
          _ref29;
        cov_1vxmo407f4().f[21]++;
        var result = (cov_1vxmo407f4().s[85]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[22]++;
          var response = (cov_1vxmo407f4().s[86]++, yield fetch(`${_this7.apiBaseUrl}/payments/refunds`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${yield _this7.getAuthToken()}`
            },
            body: JSON.stringify({
              purchaseId: purchaseId,
              reason: reason
            })
          }));
          cov_1vxmo407f4().s[87]++;
          if (!response.ok) {
            cov_1vxmo407f4().b[39][0]++;
            var error = (cov_1vxmo407f4().s[88]++, yield response.json());
            cov_1vxmo407f4().s[89]++;
            throw new Error((cov_1vxmo407f4().b[40][0]++, error.message) || (cov_1vxmo407f4().b[40][1]++, 'Failed to request refund'));
          } else {
            cov_1vxmo407f4().b[39][1]++;
          }
          cov_1vxmo407f4().s[90]++;
          return true;
        }), {
          service: 'Payment',
          action: 'requestRefund',
          purchaseId: purchaseId
        }, {
          showUserError: true
        }));
        cov_1vxmo407f4().s[91]++;
        return (_ref29 = (cov_1vxmo407f4().b[41][0]++, result)) != null ? _ref29 : (cov_1vxmo407f4().b[41][1]++, false);
      });
      function requestRefund(_x12, _x13) {
        return _requestRefund.apply(this, arguments);
      }
      return requestRefund;
    }())
  }, {
    key: "validateSubscription",
    value: (function () {
      var _validateSubscription = _asyncToGenerator(function* (userId) {
        var _this8 = this,
          _ref32;
        cov_1vxmo407f4().f[23]++;
        var result = (cov_1vxmo407f4().s[92]++, yield withErrorHandling(_asyncToGenerator(function* () {
          cov_1vxmo407f4().f[24]++;
          var subscription = (cov_1vxmo407f4().s[93]++, yield _this8.getUserSubscription(userId));
          cov_1vxmo407f4().s[94]++;
          if (!subscription) {
            cov_1vxmo407f4().b[42][0]++;
            cov_1vxmo407f4().s[95]++;
            return {
              isValid: false
            };
          } else {
            cov_1vxmo407f4().b[42][1]++;
          }
          var endDate = (cov_1vxmo407f4().s[96]++, new Date(subscription.currentPeriodEnd));
          var now = (cov_1vxmo407f4().s[97]++, new Date());
          var daysRemaining = (cov_1vxmo407f4().s[98]++, Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)));
          cov_1vxmo407f4().s[99]++;
          if ((cov_1vxmo407f4().b[44][0]++, daysRemaining <= 0) && (cov_1vxmo407f4().b[44][1]++, subscription.status !== 'active')) {
            cov_1vxmo407f4().b[43][0]++;
            cov_1vxmo407f4().s[100]++;
            return {
              isValid: false
            };
          } else {
            cov_1vxmo407f4().b[43][1]++;
          }
          var _ref31 = (cov_1vxmo407f4().s[101]++, yield supabase.from('subscription_plans').select('*').eq('id', subscription.planId).single()),
            plan = _ref31.data;
          cov_1vxmo407f4().s[102]++;
          return {
            isValid: true,
            plan: plan,
            daysRemaining: Math.max(0, daysRemaining)
          };
        }), {
          service: 'Payment',
          action: 'validateSubscription',
          userId: userId
        }, {
          showUserError: false
        }));
        cov_1vxmo407f4().s[103]++;
        return (_ref32 = (cov_1vxmo407f4().b[45][0]++, result)) != null ? _ref32 : (cov_1vxmo407f4().b[45][1]++, {
          isValid: false
        });
      });
      function validateSubscription(_x14) {
        return _validateSubscription.apply(this, arguments);
      }
      return validateSubscription;
    }())
  }, {
    key: "getAuthToken",
    value: (function () {
      var _getAuthToken = _asyncToGenerator(function* () {
        cov_1vxmo407f4().f[25]++;
        var _ref33 = (cov_1vxmo407f4().s[104]++, yield supabase.auth.getSession()),
          session = _ref33.data.session;
        cov_1vxmo407f4().s[105]++;
        return (cov_1vxmo407f4().b[46][0]++, session == null ? void 0 : session.access_token) || (cov_1vxmo407f4().b[46][1]++, '');
      });
      function getAuthToken() {
        return _getAuthToken.apply(this, arguments);
      }
      return getAuthToken;
    }())
  }]);
}();
export var paymentService = (cov_1vxmo407f4().s[106]++, new PaymentService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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