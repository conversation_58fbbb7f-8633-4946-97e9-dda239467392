5d63460b1d594949768254443036e42e
'use strict';

exports.__esModule = true;
exports.default = void 0;
function stiffnessFromOrigamiValue(oValue) {
  return (oValue - 30) * 3.62 + 194;
}
function dampingFromOrigamiValue(oValue) {
  return (oValue - 8) * 3 + 25;
}
function fromOrigamiTensionAndFriction(tension, friction) {
  return {
    stiffness: stiffnessFromOrigamiValue(tension),
    damping: dampingFromOrigamiValue(friction)
  };
}
function fromBouncinessAndSpeed(bounciness, speed) {
  function normalize(value, startValue, endValue) {
    return (value - startValue) / (endValue - startValue);
  }
  function projectNormal(n, start, end) {
    return start + n * (end - start);
  }
  function linearInterpolation(t, start, end) {
    return t * end + (1 - t) * start;
  }
  function quadraticOutInterpolation(t, start, end) {
    return linearInterpolation(2 * t - t * t, start, end);
  }
  function b3Friction1(x) {
    return 0.0007 * Math.pow(x, 3) - 0.031 * Math.pow(x, 2) + 0.64 * x + 1.28;
  }
  function b3Friction2(x) {
    return 0.000044 * Math.pow(x, 3) - 0.006 * Math.pow(x, 2) + 0.36 * x + 2;
  }
  function b3Friction3(x) {
    return 0.00000045 * Math.pow(x, 3) - 0.000332 * Math.pow(x, 2) + 0.1078 * x + 5.84;
  }
  function b3Nobounce(tension) {
    if (tension <= 18) {
      return b3Friction1(tension);
    } else if (tension > 18 && tension <= 44) {
      return b3Friction2(tension);
    } else {
      return b3Friction3(tension);
    }
  }
  var b = normalize(bounciness / 1.7, 0, 20);
  b = projectNormal(b, 0, 0.8);
  var s = normalize(speed / 1.7, 0, 20);
  var bouncyTension = projectNormal(s, 0.5, 200);
  var bouncyFriction = quadraticOutInterpolation(b, b3Nobounce(bouncyTension), 0.01);
  return {
    stiffness: stiffnessFromOrigamiValue(bouncyTension),
    damping: dampingFromOrigamiValue(bouncyFriction)
  };
}
var _default = exports.default = {
  fromOrigamiTensionAndFriction: fromOrigamiTensionAndFriction,
  fromBouncinessAndSpeed: fromBouncinessAndSpeed
};
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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