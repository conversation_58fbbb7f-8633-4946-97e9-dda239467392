{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_SectionList", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _SectionList = _interopRequireDefault(require(\"../../vendor/react-native/SectionList\"));\nvar _default = exports.default = _SectionList.default;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAUZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,YAAY,GAAGL,sBAAsB,CAACC,OAAO,wCAAwC,CAAC,CAAC;AAC3F,IAAIK,QAAQ,GAAGH,OAAO,CAACD,OAAO,GAAGG,YAAY,CAACH,OAAO;AACrDK,MAAM,CAACJ,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}