d85e1141b495db04e212aee51613ec74
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _excluded = ["animating", "color", "hidesWhenStopped", "size", "style"];
var createSvgCircle = function createSvgCircle(style) {
  return React.createElement("circle", {
    cx: "16",
    cy: "16",
    fill: "none",
    r: "14",
    strokeWidth: "4",
    style: style
  });
};
var ActivityIndicator = React.forwardRef(function (props, forwardedRef) {
  var _props$animating = props.animating,
    animating = _props$animating === void 0 ? true : _props$animating,
    _props$color = props.color,
    color = _props$color === void 0 ? '#1976D2' : _props$color,
    _props$hidesWhenStopp = props.hidesWhenStopped,
    hidesWhenStopped = _props$hidesWhenStopp === void 0 ? true : _props$hidesWhenStopp,
    _props$size = props.size,
    size = _props$size === void 0 ? 'small' : _props$size,
    style = props.style,
    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var svg = React.createElement("svg", {
    height: "100%",
    viewBox: "0 0 32 32",
    width: "100%"
  }, createSvgCircle({
    stroke: color,
    opacity: 0.2
  }), createSvgCircle({
    stroke: color,
    strokeDasharray: 80,
    strokeDashoffset: 60
  }));
  return React.createElement(_View.default, (0, _extends2.default)({}, other, {
    "aria-valuemax": 1,
    "aria-valuemin": 0,
    ref: forwardedRef,
    role: "progressbar",
    style: [styles.container, style]
  }), React.createElement(_View.default, {
    children: svg,
    style: [typeof size === 'number' ? {
      height: size,
      width: size
    } : indicatorSizes[size], styles.animation, !animating && styles.animationPause, !animating && hidesWhenStopped && styles.hidesWhenStopped]
  }));
});
ActivityIndicator.displayName = 'ActivityIndicator';
var styles = _StyleSheet.default.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center'
  },
  hidesWhenStopped: {
    visibility: 'hidden'
  },
  animation: {
    animationDuration: '0.75s',
    animationKeyframes: [{
      '0%': {
        transform: 'rotate(0deg)'
      },
      '100%': {
        transform: 'rotate(360deg)'
      }
    }],
    animationTimingFunction: 'linear',
    animationIterationCount: 'infinite'
  },
  animationPause: {
    animationPlayState: 'paused'
  }
});
var indicatorSizes = _StyleSheet.default.create({
  small: {
    width: 20,
    height: 20
  },
  large: {
    width: 36,
    height: 36
  }
});
var _default = exports.default = ActivityIndicator;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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