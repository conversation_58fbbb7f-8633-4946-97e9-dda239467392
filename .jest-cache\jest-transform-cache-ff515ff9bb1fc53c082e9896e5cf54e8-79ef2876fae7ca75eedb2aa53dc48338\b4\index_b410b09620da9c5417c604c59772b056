d63d41ec4a116d694a6ae3e2673b5600
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _Platform = _interopRequireDefault(require("../../../exports/Platform"));
var _UIManager = _interopRequireDefault(require("../../../exports/UIManager"));
var __DEV__ = process.env.NODE_ENV !== 'production';
function configureNext(config, onAnimationDidEnd) {
  if (!_Platform.default.isTesting) {
    _UIManager.default.configureNextLayoutAnimation(config, onAnimationDidEnd !== null && onAnimationDidEnd !== void 0 ? onAnimationDidEnd : function () {}, function () {});
  }
}
function create(duration, type, property) {
  return {
    duration: duration,
    create: {
      type: type,
      property: property
    },
    update: {
      type: type
    },
    delete: {
      type: type,
      property: property
    }
  };
}
var Presets = {
  easeInEaseOut: create(300, 'easeInEaseOut', 'opacity'),
  linear: create(500, 'linear', 'opacity'),
  spring: {
    duration: 700,
    create: {
      type: 'linear',
      property: 'opacity'
    },
    update: {
      type: 'spring',
      springDamping: 0.4
    },
    delete: {
      type: 'linear',
      property: 'opacity'
    }
  }
};
var LayoutAnimation = {
  configureNext: configureNext,
  create: create,
  Types: Object.freeze({
    spring: 'spring',
    linear: 'linear',
    easeInEaseOut: 'easeInEaseOut',
    easeIn: 'easeIn',
    easeOut: 'easeOut',
    keyboard: 'keyboard'
  }),
  Properties: Object.freeze({
    opacity: 'opacity',
    scaleX: 'scaleX',
    scaleY: 'scaleY',
    scaleXY: 'scaleXY'
  }),
  checkConfig: function checkConfig() {
    console.error('LayoutAnimation.checkConfig(...) has been disabled.');
  },
  Presets: Presets,
  easeInEaseOut: configureNext.bind(null, Presets.easeInEaseOut),
  linear: configureNext.bind(null, Presets.linear),
  spring: configureNext.bind(null, Presets.spring)
};
var _default = exports.default = LayoutAnimation;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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