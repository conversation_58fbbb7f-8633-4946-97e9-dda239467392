{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_react", "_UnimplementedView", "YellowBox", "props", "createElement", "ignoreWarnings", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _UnimplementedView = _interopRequireDefault(require(\"../../modules/UnimplementedView\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction YellowBox(props) {\n  return /*#__PURE__*/_react.default.createElement(_UnimplementedView.default, props);\n}\nYellowBox.ignoreWarnings = () => {};\nvar _default = exports.default = YellowBox;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,MAAM,GAAGL,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIK,kBAAkB,GAAGN,sBAAsB,CAACC,OAAO,kCAAkC,CAAC,CAAC;AAW3F,SAASM,SAASA,CAACC,KAAK,EAAE;EACxB,OAAoBH,MAAM,CAACH,OAAO,CAACO,aAAa,CAACH,kBAAkB,CAACJ,OAAO,EAAEM,KAAK,CAAC;AACrF;AACAD,SAAS,CAACG,cAAc,GAAG,YAAM,CAAC,CAAC;AACnC,IAAIC,QAAQ,GAAGR,OAAO,CAACD,OAAO,GAAGK,SAAS;AAC1CK,MAAM,CAACT,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}