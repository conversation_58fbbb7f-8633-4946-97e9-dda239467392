55aa820ccab2d247ecad1a9a0d8e7719
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_205auhcslx() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\offlineService.ts";
  var hash = "9b8fd1b34af448d6279a3474a33f9d22d3ca809d";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\offlineService.ts",
    statementMap: {
      "0": {
        start: {
          line: 27,
          column: 21
        },
        end: {
          line: 27,
          column: 25
        }
      },
      "1": {
        start: {
          line: 28,
          column: 60
        },
        end: {
          line: 28,
          column: 62
        }
      },
      "2": {
        start: {
          line: 29,
          column: 34
        },
        end: {
          line: 29,
          column: 50
        }
      },
      "3": {
        start: {
          line: 30,
          column: 41
        },
        end: {
          line: 30,
          column: 66
        }
      },
      "4": {
        start: {
          line: 31,
          column: 35
        },
        end: {
          line: 31,
          column: 54
        }
      },
      "5": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 41
        }
      },
      "6": {
        start: {
          line: 38,
          column: 4
        },
        end: {
          line: 59,
          column: 5
        }
      },
      "7": {
        start: {
          line: 40,
          column: 6
        },
        end: {
          line: 50,
          column: 9
        }
      },
      "8": {
        start: {
          line: 41,
          column: 26
        },
        end: {
          line: 41,
          column: 39
        }
      },
      "9": {
        start: {
          line: 42,
          column: 8
        },
        end: {
          line: 42,
          column: 51
        }
      },
      "10": {
        start: {
          line: 44,
          column: 8
        },
        end: {
          line: 47,
          column: 9
        }
      },
      "11": {
        start: {
          line: 46,
          column: 10
        },
        end: {
          line: 46,
          column: 36
        }
      },
      "12": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 35
        }
      },
      "13": {
        start: {
          line: 53,
          column: 22
        },
        end: {
          line: 53,
          column: 43
        }
      },
      "14": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 54,
          column: 51
        }
      },
      "15": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 73
        }
      },
      "16": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 66
        }
      },
      "17": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 79,
          column: 5
        }
      },
      "18": {
        start: {
          line: 67,
          column: 38
        },
        end: {
          line: 71,
          column: 7
        }
      },
      "19": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 76,
          column: 8
        }
      },
      "20": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 50
        }
      },
      "21": {
        start: {
          line: 86,
          column: 4
        },
        end: {
          line: 105,
          column: 5
        }
      },
      "22": {
        start: {
          line: 87,
          column: 25
        },
        end: {
          line: 87,
          column: 81
        }
      },
      "23": {
        start: {
          line: 89,
          column: 6
        },
        end: {
          line: 91,
          column: 7
        }
      },
      "24": {
        start: {
          line: 90,
          column: 8
        },
        end: {
          line: 90,
          column: 20
        }
      },
      "25": {
        start: {
          line: 93,
          column: 38
        },
        end: {
          line: 93,
          column: 60
        }
      },
      "26": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 99,
          column: 7
        }
      },
      "27": {
        start: {
          line: 97,
          column: 8
        },
        end: {
          line: 97,
          column: 41
        }
      },
      "28": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 98,
          column: 20
        }
      },
      "29": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 28
        }
      },
      "30": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 57
        }
      },
      "31": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 18
        }
      },
      "32": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 116,
          column: 5
        }
      },
      "33": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 113,
          column: 66
        }
      },
      "34": {
        start: {
          line: 115,
          column: 6
        },
        end: {
          line: 115,
          column: 58
        }
      },
      "35": {
        start: {
          line: 123,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "36": {
        start: {
          line: 124,
          column: 19
        },
        end: {
          line: 124,
          column: 50
        }
      },
      "37": {
        start: {
          line: 125,
          column: 24
        },
        end: {
          line: 125,
          column: 77
        }
      },
      "38": {
        start: {
          line: 125,
          column: 43
        },
        end: {
          line: 125,
          column: 76
        }
      },
      "39": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 126,
          column: 48
        }
      },
      "40": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 52
        }
      },
      "41": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 162,
          column: 5
        }
      },
      "42": {
        start: {
          line: 137,
          column: 36
        },
        end: {
          line: 143,
          column: 7
        }
      },
      "43": {
        start: {
          line: 145,
          column: 30
        },
        end: {
          line: 145,
          column: 60
        }
      },
      "44": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 146,
          column: 35
        }
      },
      "45": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 151,
          column: 8
        }
      },
      "46": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 156,
          column: 7
        }
      },
      "47": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 155,
          column: 34
        }
      },
      "48": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 23
        }
      },
      "49": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 60
        }
      },
      "50": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 161,
          column: 18
        }
      },
      "51": {
        start: {
          line: 169,
          column: 4
        },
        end: {
          line: 175,
          column: 5
        }
      },
      "52": {
        start: {
          line: 170,
          column: 26
        },
        end: {
          line: 170,
          column: 78
        }
      },
      "53": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 56
        }
      },
      "54": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 173,
          column: 61
        }
      },
      "55": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 16
        }
      },
      "56": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 184,
          column: 5
        }
      },
      "57": {
        start: {
          line: 183,
          column: 6
        },
        end: {
          line: 183,
          column: 13
        }
      },
      "58": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 227,
          column: 5
        }
      },
      "59": {
        start: {
          line: 187,
          column: 29
        },
        end: {
          line: 187,
          column: 59
        }
      },
      "60": {
        start: {
          line: 188,
          column: 42
        },
        end: {
          line: 188,
          column: 44
        }
      },
      "61": {
        start: {
          line: 189,
          column: 45
        },
        end: {
          line: 189,
          column: 47
        }
      },
      "62": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 210,
          column: 7
        }
      },
      "63": {
        start: {
          line: 192,
          column: 8
        },
        end: {
          line: 209,
          column: 9
        }
      },
      "64": {
        start: {
          line: 193,
          column: 26
        },
        end: {
          line: 193,
          column: 58
        }
      },
      "65": {
        start: {
          line: 195,
          column: 10
        },
        end: {
          line: 202,
          column: 11
        }
      },
      "66": {
        start: {
          line: 196,
          column: 12
        },
        end: {
          line: 196,
          column: 46
        }
      },
      "67": {
        start: {
          line: 198,
          column: 12
        },
        end: {
          line: 198,
          column: 32
        }
      },
      "68": {
        start: {
          line: 199,
          column: 12
        },
        end: {
          line: 201,
          column: 13
        }
      },
      "69": {
        start: {
          line: 200,
          column: 14
        },
        end: {
          line: 200,
          column: 41
        }
      },
      "70": {
        start: {
          line: 204,
          column: 10
        },
        end: {
          line: 204,
          column: 58
        }
      },
      "71": {
        start: {
          line: 205,
          column: 10
        },
        end: {
          line: 205,
          column: 30
        }
      },
      "72": {
        start: {
          line: 206,
          column: 10
        },
        end: {
          line: 208,
          column: 11
        }
      },
      "73": {
        start: {
          line: 207,
          column: 12
        },
        end: {
          line: 207,
          column: 39
        }
      },
      "74": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 216,
          column: 8
        }
      },
      "75": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 222,
          column: 8
        }
      },
      "76": {
        start: {
          line: 224,
          column: 6
        },
        end: {
          line: 224,
          column: 33
        }
      },
      "77": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 61
        }
      },
      "78": {
        start: {
          line: 234,
          column: 4
        },
        end: {
          line: 258,
          column: 5
        }
      },
      "79": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 254,
          column: 7
        }
      },
      "80": {
        start: {
          line: 237,
          column: 10
        },
        end: {
          line: 237,
          column: 61
        }
      },
      "81": {
        start: {
          line: 240,
          column: 10
        },
        end: {
          line: 240,
          column: 56
        }
      },
      "82": {
        start: {
          line: 243,
          column: 10
        },
        end: {
          line: 243,
          column: 57
        }
      },
      "83": {
        start: {
          line: 246,
          column: 10
        },
        end: {
          line: 246,
          column: 57
        }
      },
      "84": {
        start: {
          line: 249,
          column: 10
        },
        end: {
          line: 249,
          column: 57
        }
      },
      "85": {
        start: {
          line: 252,
          column: 10
        },
        end: {
          line: 252,
          column: 60
        }
      },
      "86": {
        start: {
          line: 253,
          column: 10
        },
        end: {
          line: 253,
          column: 23
        }
      },
      "87": {
        start: {
          line: 256,
          column: 6
        },
        end: {
          line: 256,
          column: 54
        }
      },
      "88": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 257,
          column: 19
        }
      },
      "89": {
        start: {
          line: 265,
          column: 4
        },
        end: {
          line: 283,
          column: 5
        }
      },
      "90": {
        start: {
          line: 266,
          column: 29
        },
        end: {
          line: 266,
          column: 59
        }
      },
      "91": {
        start: {
          line: 267,
          column: 27
        },
        end: {
          line: 267,
          column: 73
        }
      },
      "92": {
        start: {
          line: 269,
          column: 6
        },
        end: {
          line: 274,
          column: 8
        }
      },
      "93": {
        start: {
          line: 273,
          column: 50
        },
        end: {
          line: 273,
          column: 66
        }
      },
      "94": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 57
        }
      },
      "95": {
        start: {
          line: 277,
          column: 6
        },
        end: {
          line: 282,
          column: 8
        }
      },
      "96": {
        start: {
          line: 290,
          column: 4
        },
        end: {
          line: 290,
          column: 38
        }
      },
      "97": {
        start: {
          line: 293,
          column: 4
        },
        end: {
          line: 298,
          column: 6
        }
      },
      "98": {
        start: {
          line: 294,
          column: 20
        },
        end: {
          line: 294,
          column: 56
        }
      },
      "99": {
        start: {
          line: 295,
          column: 6
        },
        end: {
          line: 297,
          column: 7
        }
      },
      "100": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 296,
          column: 44
        }
      },
      "101": {
        start: {
          line: 305,
          column: 4
        },
        end: {
          line: 305,
          column: 25
        }
      },
      "102": {
        start: {
          line: 312,
          column: 4
        },
        end: {
          line: 312,
          column: 57
        }
      },
      "103": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 319,
          column: 70
        }
      },
      "104": {
        start: {
          line: 326,
          column: 4
        },
        end: {
          line: 326,
          column: 61
        }
      },
      "105": {
        start: {
          line: 333,
          column: 4
        },
        end: {
          line: 333,
          column: 54
        }
      },
      "106": {
        start: {
          line: 340,
          column: 4
        },
        end: {
          line: 340,
          column: 73
        }
      },
      "107": {
        start: {
          line: 347,
          column: 4
        },
        end: {
          line: 347,
          column: 61
        }
      },
      "108": {
        start: {
          line: 353,
          column: 4
        },
        end: {
          line: 365,
          column: 5
        }
      },
      "109": {
        start: {
          line: 355,
          column: 27
        },
        end: {
          line: 355,
          column: 57
        }
      },
      "110": {
        start: {
          line: 357,
          column: 24
        },
        end: {
          line: 359,
          column: 21
        }
      },
      "111": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 361,
          column: 20
        }
      },
      "112": {
        start: {
          line: 363,
          column: 6
        },
        end: {
          line: 363,
          column: 62
        }
      },
      "113": {
        start: {
          line: 364,
          column: 6
        },
        end: {
          line: 364,
          column: 19
        }
      },
      "114": {
        start: {
          line: 369,
          column: 4
        },
        end: {
          line: 381,
          column: 5
        }
      },
      "115": {
        start: {
          line: 370,
          column: 27
        },
        end: {
          line: 370,
          column: 57
        }
      },
      "116": {
        start: {
          line: 372,
          column: 24
        },
        end: {
          line: 375,
          column: 35
        }
      },
      "117": {
        start: {
          line: 377,
          column: 6
        },
        end: {
          line: 377,
          column: 20
        }
      },
      "118": {
        start: {
          line: 379,
          column: 6
        },
        end: {
          line: 379,
          column: 57
        }
      },
      "119": {
        start: {
          line: 380,
          column: 6
        },
        end: {
          line: 380,
          column: 19
        }
      },
      "120": {
        start: {
          line: 385,
          column: 4
        },
        end: {
          line: 396,
          column: 5
        }
      },
      "121": {
        start: {
          line: 386,
          column: 27
        },
        end: {
          line: 386,
          column: 57
        }
      },
      "122": {
        start: {
          line: 388,
          column: 24
        },
        end: {
          line: 390,
          column: 21
        }
      },
      "123": {
        start: {
          line: 392,
          column: 6
        },
        end: {
          line: 392,
          column: 20
        }
      },
      "124": {
        start: {
          line: 394,
          column: 6
        },
        end: {
          line: 394,
          column: 58
        }
      },
      "125": {
        start: {
          line: 395,
          column: 6
        },
        end: {
          line: 395,
          column: 19
        }
      },
      "126": {
        start: {
          line: 400,
          column: 4
        },
        end: {
          line: 412,
          column: 5
        }
      },
      "127": {
        start: {
          line: 401,
          column: 27
        },
        end: {
          line: 401,
          column: 57
        }
      },
      "128": {
        start: {
          line: 403,
          column: 24
        },
        end: {
          line: 406,
          column: 30
        }
      },
      "129": {
        start: {
          line: 408,
          column: 6
        },
        end: {
          line: 408,
          column: 20
        }
      },
      "130": {
        start: {
          line: 410,
          column: 6
        },
        end: {
          line: 410,
          column: 58
        }
      },
      "131": {
        start: {
          line: 411,
          column: 6
        },
        end: {
          line: 411,
          column: 19
        }
      },
      "132": {
        start: {
          line: 416,
          column: 4
        },
        end: {
          line: 427,
          column: 5
        }
      },
      "133": {
        start: {
          line: 417,
          column: 27
        },
        end: {
          line: 417,
          column: 57
        }
      },
      "134": {
        start: {
          line: 419,
          column: 24
        },
        end: {
          line: 421,
          column: 21
        }
      },
      "135": {
        start: {
          line: 423,
          column: 6
        },
        end: {
          line: 423,
          column: 20
        }
      },
      "136": {
        start: {
          line: 425,
          column: 6
        },
        end: {
          line: 425,
          column: 57
        }
      },
      "137": {
        start: {
          line: 426,
          column: 6
        },
        end: {
          line: 426,
          column: 19
        }
      },
      "138": {
        start: {
          line: 431,
          column: 19
        },
        end: {
          line: 431,
          column: 45
        }
      },
      "139": {
        start: {
          line: 432,
          column: 4
        },
        end: {
          line: 432,
          column: 61
        }
      },
      "140": {
        start: {
          line: 432,
          column: 43
        },
        end: {
          line: 432,
          column: 59
        }
      },
      "141": {
        start: {
          line: 436,
          column: 30
        },
        end: {
          line: 436,
          column: 50
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 37,
            column: 2
          },
          end: {
            line: 37,
            column: 3
          }
        },
        loc: {
          start: {
            line: 37,
            column: 36
          },
          end: {
            line: 60,
            column: 3
          }
        },
        line: 37
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 40,
            column: 31
          },
          end: {
            line: 40,
            column: 32
          }
        },
        loc: {
          start: {
            line: 40,
            column: 40
          },
          end: {
            line: 50,
            column: 7
          }
        },
        line: 40
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 65,
            column: 2
          },
          end: {
            line: 65,
            column: 3
          }
        },
        loc: {
          start: {
            line: 65,
            column: 86
          },
          end: {
            line: 80,
            column: 3
          }
        },
        line: 65
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 85,
            column: 2
          },
          end: {
            line: 85,
            column: 3
          }
        },
        loc: {
          start: {
            line: 85,
            column: 57
          },
          end: {
            line: 106,
            column: 3
          }
        },
        line: 85
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 111,
            column: 2
          },
          end: {
            line: 111,
            column: 3
          }
        },
        loc: {
          start: {
            line: 111,
            column: 53
          },
          end: {
            line: 117,
            column: 3
          }
        },
        line: 111
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 122,
            column: 2
          },
          end: {
            line: 122,
            column: 3
          }
        },
        loc: {
          start: {
            line: 122,
            column: 36
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 122
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 125,
            column: 36
          },
          end: {
            line: 125,
            column: 37
          }
        },
        loc: {
          start: {
            line: 125,
            column: 43
          },
          end: {
            line: 125,
            column: 76
          }
        },
        line: 125
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 135,
            column: 2
          },
          end: {
            line: 135,
            column: 3
          }
        },
        loc: {
          start: {
            line: 135,
            column: 69
          },
          end: {
            line: 163,
            column: 3
          }
        },
        line: 135
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 168,
            column: 2
          },
          end: {
            line: 168,
            column: 3
          }
        },
        loc: {
          start: {
            line: 168,
            column: 54
          },
          end: {
            line: 176,
            column: 3
          }
        },
        line: 168
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 181,
            column: 2
          },
          end: {
            line: 181,
            column: 3
          }
        },
        loc: {
          start: {
            line: 181,
            column: 44
          },
          end: {
            line: 228,
            column: 3
          }
        },
        line: 181
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 233,
            column: 2
          },
          end: {
            line: 233,
            column: 3
          }
        },
        loc: {
          start: {
            line: 233,
            column: 71
          },
          end: {
            line: 259,
            column: 3
          }
        },
        line: 233
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 264,
            column: 2
          },
          end: {
            line: 264,
            column: 3
          }
        },
        loc: {
          start: {
            line: 264,
            column: 45
          },
          end: {
            line: 284,
            column: 3
          }
        },
        line: 264
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 273,
            column: 45
          },
          end: {
            line: 273,
            column: 46
          }
        },
        loc: {
          start: {
            line: 273,
            column: 50
          },
          end: {
            line: 273,
            column: 66
          }
        },
        line: 273
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 289,
            column: 2
          },
          end: {
            line: 289,
            column: 3
          }
        },
        loc: {
          start: {
            line: 289,
            column: 73
          },
          end: {
            line: 299,
            column: 3
          }
        },
        line: 289
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 293,
            column: 11
          },
          end: {
            line: 293,
            column: 12
          }
        },
        loc: {
          start: {
            line: 293,
            column: 17
          },
          end: {
            line: 298,
            column: 5
          }
        },
        line: 293
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 304,
            column: 2
          },
          end: {
            line: 304,
            column: 3
          }
        },
        loc: {
          start: {
            line: 304,
            column: 28
          },
          end: {
            line: 306,
            column: 3
          }
        },
        line: 304
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 311,
            column: 2
          },
          end: {
            line: 311,
            column: 3
          }
        },
        loc: {
          start: {
            line: 311,
            column: 68
          },
          end: {
            line: 313,
            column: 3
          }
        },
        line: 311
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 318,
            column: 2
          },
          end: {
            line: 318,
            column: 3
          }
        },
        loc: {
          start: {
            line: 318,
            column: 78
          },
          end: {
            line: 320,
            column: 3
          }
        },
        line: 318
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 325,
            column: 2
          },
          end: {
            line: 325,
            column: 3
          }
        },
        loc: {
          start: {
            line: 325,
            column: 67
          },
          end: {
            line: 327,
            column: 3
          }
        },
        line: 325
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 332,
            column: 2
          },
          end: {
            line: 332,
            column: 3
          }
        },
        loc: {
          start: {
            line: 332,
            column: 56
          },
          end: {
            line: 334,
            column: 3
          }
        },
        line: 332
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 339,
            column: 2
          },
          end: {
            line: 339,
            column: 3
          }
        },
        loc: {
          start: {
            line: 339,
            column: 66
          },
          end: {
            line: 341,
            column: 3
          }
        },
        line: 339
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 346,
            column: 2
          },
          end: {
            line: 346,
            column: 3
          }
        },
        loc: {
          start: {
            line: 346,
            column: 58
          },
          end: {
            line: 348,
            column: 3
          }
        },
        line: 346
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 352,
            column: 2
          },
          end: {
            line: 352,
            column: 3
          }
        },
        loc: {
          start: {
            line: 352,
            column: 65
          },
          end: {
            line: 366,
            column: 3
          }
        },
        line: 352
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 368,
            column: 2
          },
          end: {
            line: 368,
            column: 3
          }
        },
        loc: {
          start: {
            line: 368,
            column: 60
          },
          end: {
            line: 382,
            column: 3
          }
        },
        line: 368
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 384,
            column: 2
          },
          end: {
            line: 384,
            column: 3
          }
        },
        loc: {
          start: {
            line: 384,
            column: 61
          },
          end: {
            line: 397,
            column: 3
          }
        },
        line: 384
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 399,
            column: 2
          },
          end: {
            line: 399,
            column: 3
          }
        },
        loc: {
          start: {
            line: 399,
            column: 61
          },
          end: {
            line: 413,
            column: 3
          }
        },
        line: 399
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 415,
            column: 2
          },
          end: {
            line: 415,
            column: 3
          }
        },
        loc: {
          start: {
            line: 415,
            column: 61
          },
          end: {
            line: 428,
            column: 3
          }
        },
        line: 415
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 430,
            column: 2
          },
          end: {
            line: 430,
            column: 3
          }
        },
        loc: {
          start: {
            line: 430,
            column: 53
          },
          end: {
            line: 433,
            column: 3
          }
        },
        line: 430
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 432,
            column: 31
          },
          end: {
            line: 432,
            column: 32
          }
        },
        loc: {
          start: {
            line: 432,
            column: 43
          },
          end: {
            line: 432,
            column: 59
          }
        },
        line: 432
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 42,
            column: 24
          },
          end: {
            line: 42,
            column: 41
          }
        }, {
          start: {
            line: 42,
            column: 45
          },
          end: {
            line: 42,
            column: 50
          }
        }],
        line: 42
      },
      "1": {
        loc: {
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 47,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 44,
            column: 8
          },
          end: {
            line: 47,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 44
      },
      "2": {
        loc: {
          start: {
            line: 44,
            column: 12
          },
          end: {
            line: 44,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 44,
            column: 12
          },
          end: {
            line: 44,
            column: 22
          }
        }, {
          start: {
            line: 44,
            column: 26
          },
          end: {
            line: 44,
            column: 39
          }
        }],
        line: 44
      },
      "3": {
        loc: {
          start: {
            line: 54,
            column: 22
          },
          end: {
            line: 54,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 54,
            column: 22
          },
          end: {
            line: 54,
            column: 41
          }
        }, {
          start: {
            line: 54,
            column: 45
          },
          end: {
            line: 54,
            column: 50
          }
        }],
        line: 54
      },
      "4": {
        loc: {
          start: {
            line: 70,
            column: 19
          },
          end: {
            line: 70,
            column: 95
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 70,
            column: 39
          },
          end: {
            line: 70,
            column: 83
          }
        }, {
          start: {
            line: 70,
            column: 86
          },
          end: {
            line: 70,
            column: 95
          }
        }],
        line: 70
      },
      "5": {
        loc: {
          start: {
            line: 89,
            column: 6
          },
          end: {
            line: 91,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 89,
            column: 6
          },
          end: {
            line: 91,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 89
      },
      "6": {
        loc: {
          start: {
            line: 96,
            column: 6
          },
          end: {
            line: 99,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 96,
            column: 6
          },
          end: {
            line: 99,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 96
      },
      "7": {
        loc: {
          start: {
            line: 96,
            column: 10
          },
          end: {
            line: 96,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 96,
            column: 10
          },
          end: {
            line: 96,
            column: 29
          }
        }, {
          start: {
            line: 96,
            column: 33
          },
          end: {
            line: 96,
            column: 65
          }
        }],
        line: 96
      },
      "8": {
        loc: {
          start: {
            line: 154,
            column: 6
          },
          end: {
            line: 156,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 6
          },
          end: {
            line: 156,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "9": {
        loc: {
          start: {
            line: 171,
            column: 13
          },
          end: {
            line: 171,
            column: 55
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 171,
            column: 27
          },
          end: {
            line: 171,
            column: 50
          }
        }, {
          start: {
            line: 171,
            column: 53
          },
          end: {
            line: 171,
            column: 55
          }
        }],
        line: 171
      },
      "10": {
        loc: {
          start: {
            line: 182,
            column: 4
          },
          end: {
            line: 184,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 182,
            column: 4
          },
          end: {
            line: 184,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 182
      },
      "11": {
        loc: {
          start: {
            line: 195,
            column: 10
          },
          end: {
            line: 202,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 195,
            column: 10
          },
          end: {
            line: 202,
            column: 11
          }
        }, {
          start: {
            line: 197,
            column: 17
          },
          end: {
            line: 202,
            column: 11
          }
        }],
        line: 195
      },
      "12": {
        loc: {
          start: {
            line: 199,
            column: 12
          },
          end: {
            line: 201,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 199,
            column: 12
          },
          end: {
            line: 201,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 199
      },
      "13": {
        loc: {
          start: {
            line: 206,
            column: 10
          },
          end: {
            line: 208,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 206,
            column: 10
          },
          end: {
            line: 208,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 206
      },
      "14": {
        loc: {
          start: {
            line: 235,
            column: 6
          },
          end: {
            line: 254,
            column: 7
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 236,
            column: 8
          },
          end: {
            line: 237,
            column: 61
          }
        }, {
          start: {
            line: 239,
            column: 8
          },
          end: {
            line: 240,
            column: 56
          }
        }, {
          start: {
            line: 242,
            column: 8
          },
          end: {
            line: 243,
            column: 57
          }
        }, {
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 246,
            column: 57
          }
        }, {
          start: {
            line: 248,
            column: 8
          },
          end: {
            line: 249,
            column: 57
          }
        }, {
          start: {
            line: 251,
            column: 8
          },
          end: {
            line: 253,
            column: 23
          }
        }],
        line: 235
      },
      "15": {
        loc: {
          start: {
            line: 271,
            column: 22
          },
          end: {
            line: 271,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 271,
            column: 37
          },
          end: {
            line: 271,
            column: 59
          }
        }, {
          start: {
            line: 271,
            column: 62
          },
          end: {
            line: 271,
            column: 63
          }
        }],
        line: 271
      },
      "16": {
        loc: {
          start: {
            line: 295,
            column: 6
          },
          end: {
            line: 297,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 295,
            column: 6
          },
          end: {
            line: 297,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 295
      },
      "17": {
        loc: {
          start: {
            line: 340,
            column: 11
          },
          end: {
            line: 340,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 340,
            column: 11
          },
          end: {
            line: 340,
            column: 66
          }
        }, {
          start: {
            line: 340,
            column: 70
          },
          end: {
            line: 340,
            column: 72
          }
        }],
        line: 340
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0, 0, 0, 0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "9b8fd1b34af448d6279a3474a33f9d22d3ca809d"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_205auhcslx = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_205auhcslx();
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function _getRequireWildcardCache(e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
import AsyncStorage from '@react-native-async-storage/async-storage';
import NetInfo from '@react-native-community/netinfo';
var OfflineService = function () {
  function OfflineService() {
    _classCallCheck(this, OfflineService);
    this.isOnline = (cov_205auhcslx().s[0]++, true);
    this.syncCallbacks = (cov_205auhcslx().s[1]++, []);
    this.CACHE_PREFIX = (cov_205auhcslx().s[2]++, 'acemind_cache_');
    this.OFFLINE_ACTIONS_KEY = (cov_205auhcslx().s[3]++, 'acemind_offline_actions');
    this.LAST_SYNC_KEY = (cov_205auhcslx().s[4]++, 'acemind_last_sync');
    this.MAX_RETRY_ATTEMPTS = (cov_205auhcslx().s[5]++, 3);
  }
  return _createClass(OfflineService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* () {
        var _this = this;
        cov_205auhcslx().f[0]++;
        cov_205auhcslx().s[6]++;
        try {
          var _ref2;
          cov_205auhcslx().s[7]++;
          NetInfo.addEventListener(function (state) {
            var _ref;
            cov_205auhcslx().f[1]++;
            var wasOnline = (cov_205auhcslx().s[8]++, _this.isOnline);
            cov_205auhcslx().s[9]++;
            _this.isOnline = (_ref = (cov_205auhcslx().b[0][0]++, state.isConnected)) != null ? _ref : (cov_205auhcslx().b[0][1]++, false);
            cov_205auhcslx().s[10]++;
            if ((cov_205auhcslx().b[2][0]++, !wasOnline) && (cov_205auhcslx().b[2][1]++, _this.isOnline)) {
              cov_205auhcslx().b[1][0]++;
              cov_205auhcslx().s[11]++;
              _this.syncPendingActions();
            } else {
              cov_205auhcslx().b[1][1]++;
            }
            cov_205auhcslx().s[12]++;
            _this.notifySyncCallbacks();
          });
          var netInfo = (cov_205auhcslx().s[13]++, yield NetInfo.fetch());
          cov_205auhcslx().s[14]++;
          this.isOnline = (_ref2 = (cov_205auhcslx().b[3][0]++, netInfo.isConnected)) != null ? _ref2 : (cov_205auhcslx().b[3][1]++, false);
          cov_205auhcslx().s[15]++;
          console.log('Offline service initialized, online:', this.isOnline);
        } catch (error) {
          cov_205auhcslx().s[16]++;
          console.error('Error initializing offline service:', error);
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "cacheData",
    value: (function () {
      var _cacheData = _asyncToGenerator(function* (key, data, expirationMinutes) {
        cov_205auhcslx().f[2]++;
        cov_205auhcslx().s[17]++;
        try {
          var cacheItem = (cov_205auhcslx().s[18]++, {
            data: data,
            timestamp: Date.now(),
            expiresAt: expirationMinutes ? (cov_205auhcslx().b[4][0]++, Date.now() + expirationMinutes * 60 * 1000) : (cov_205auhcslx().b[4][1]++, undefined)
          });
          cov_205auhcslx().s[19]++;
          yield AsyncStorage.setItem(`${this.CACHE_PREFIX}${key}`, JSON.stringify(cacheItem));
        } catch (error) {
          cov_205auhcslx().s[20]++;
          console.error('Error caching data:', error);
        }
      });
      function cacheData(_x, _x2, _x3) {
        return _cacheData.apply(this, arguments);
      }
      return cacheData;
    }())
  }, {
    key: "getCachedData",
    value: (function () {
      var _getCachedData = _asyncToGenerator(function* (key) {
        cov_205auhcslx().f[3]++;
        cov_205auhcslx().s[21]++;
        try {
          var cachedItem = (cov_205auhcslx().s[22]++, yield AsyncStorage.getItem(`${this.CACHE_PREFIX}${key}`));
          cov_205auhcslx().s[23]++;
          if (!cachedItem) {
            cov_205auhcslx().b[5][0]++;
            cov_205auhcslx().s[24]++;
            return null;
          } else {
            cov_205auhcslx().b[5][1]++;
          }
          var cacheItem = (cov_205auhcslx().s[25]++, JSON.parse(cachedItem));
          cov_205auhcslx().s[26]++;
          if ((cov_205auhcslx().b[7][0]++, cacheItem.expiresAt) && (cov_205auhcslx().b[7][1]++, Date.now() > cacheItem.expiresAt)) {
            cov_205auhcslx().b[6][0]++;
            cov_205auhcslx().s[27]++;
            yield this.removeCachedData(key);
            cov_205auhcslx().s[28]++;
            return null;
          } else {
            cov_205auhcslx().b[6][1]++;
          }
          cov_205auhcslx().s[29]++;
          return cacheItem.data;
        } catch (error) {
          cov_205auhcslx().s[30]++;
          console.error('Error getting cached data:', error);
          cov_205auhcslx().s[31]++;
          return null;
        }
      });
      function getCachedData(_x4) {
        return _getCachedData.apply(this, arguments);
      }
      return getCachedData;
    }())
  }, {
    key: "removeCachedData",
    value: (function () {
      var _removeCachedData = _asyncToGenerator(function* (key) {
        cov_205auhcslx().f[4]++;
        cov_205auhcslx().s[32]++;
        try {
          cov_205auhcslx().s[33]++;
          yield AsyncStorage.removeItem(`${this.CACHE_PREFIX}${key}`);
        } catch (error) {
          cov_205auhcslx().s[34]++;
          console.error('Error removing cached data:', error);
        }
      });
      function removeCachedData(_x5) {
        return _removeCachedData.apply(this, arguments);
      }
      return removeCachedData;
    }())
  }, {
    key: "clearCache",
    value: (function () {
      var _clearCache = _asyncToGenerator(function* () {
        var _this2 = this;
        cov_205auhcslx().f[5]++;
        cov_205auhcslx().s[35]++;
        try {
          var keys = (cov_205auhcslx().s[36]++, yield AsyncStorage.getAllKeys());
          var cacheKeys = (cov_205auhcslx().s[37]++, keys.filter(function (key) {
            cov_205auhcslx().f[6]++;
            cov_205auhcslx().s[38]++;
            return key.startsWith(_this2.CACHE_PREFIX);
          }));
          cov_205auhcslx().s[39]++;
          yield AsyncStorage.multiRemove(cacheKeys);
        } catch (error) {
          cov_205auhcslx().s[40]++;
          console.error('Error clearing cache:', error);
        }
      });
      function clearCache() {
        return _clearCache.apply(this, arguments);
      }
      return clearCache;
    }())
  }, {
    key: "queueOfflineAction",
    value: (function () {
      var _queueOfflineAction = _asyncToGenerator(function* (type, data) {
        cov_205auhcslx().f[7]++;
        cov_205auhcslx().s[41]++;
        try {
          var action = (cov_205auhcslx().s[42]++, {
            id: `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: type,
            data: data,
            timestamp: Date.now(),
            retryCount: 0
          });
          var existingActions = (cov_205auhcslx().s[43]++, yield this.getPendingActions());
          cov_205auhcslx().s[44]++;
          existingActions.push(action);
          cov_205auhcslx().s[45]++;
          yield AsyncStorage.setItem(this.OFFLINE_ACTIONS_KEY, JSON.stringify(existingActions));
          cov_205auhcslx().s[46]++;
          if (this.isOnline) {
            cov_205auhcslx().b[8][0]++;
            cov_205auhcslx().s[47]++;
            this.syncPendingActions();
          } else {
            cov_205auhcslx().b[8][1]++;
          }
          cov_205auhcslx().s[48]++;
          return action.id;
        } catch (error) {
          cov_205auhcslx().s[49]++;
          console.error('Error queuing offline action:', error);
          cov_205auhcslx().s[50]++;
          throw error;
        }
      });
      function queueOfflineAction(_x6, _x7) {
        return _queueOfflineAction.apply(this, arguments);
      }
      return queueOfflineAction;
    }())
  }, {
    key: "getPendingActions",
    value: (function () {
      var _getPendingActions = _asyncToGenerator(function* () {
        cov_205auhcslx().f[8]++;
        cov_205auhcslx().s[51]++;
        try {
          var actionsJson = (cov_205auhcslx().s[52]++, yield AsyncStorage.getItem(this.OFFLINE_ACTIONS_KEY));
          cov_205auhcslx().s[53]++;
          return actionsJson ? (cov_205auhcslx().b[9][0]++, JSON.parse(actionsJson)) : (cov_205auhcslx().b[9][1]++, []);
        } catch (error) {
          cov_205auhcslx().s[54]++;
          console.error('Error getting pending actions:', error);
          cov_205auhcslx().s[55]++;
          return [];
        }
      });
      function getPendingActions() {
        return _getPendingActions.apply(this, arguments);
      }
      return getPendingActions;
    }())
  }, {
    key: "syncPendingActions",
    value: (function () {
      var _syncPendingActions = _asyncToGenerator(function* () {
        cov_205auhcslx().f[9]++;
        cov_205auhcslx().s[56]++;
        if (!this.isOnline) {
          cov_205auhcslx().b[10][0]++;
          cov_205auhcslx().s[57]++;
          return;
        } else {
          cov_205auhcslx().b[10][1]++;
        }
        cov_205auhcslx().s[58]++;
        try {
          var pendingActions = (cov_205auhcslx().s[59]++, yield this.getPendingActions());
          var successfulActions = (cov_205auhcslx().s[60]++, []);
          var failedActions = (cov_205auhcslx().s[61]++, []);
          cov_205auhcslx().s[62]++;
          for (var action of pendingActions) {
            cov_205auhcslx().s[63]++;
            try {
              var success = (cov_205auhcslx().s[64]++, yield this.executeAction(action));
              cov_205auhcslx().s[65]++;
              if (success) {
                cov_205auhcslx().b[11][0]++;
                cov_205auhcslx().s[66]++;
                successfulActions.push(action.id);
              } else {
                cov_205auhcslx().b[11][1]++;
                cov_205auhcslx().s[67]++;
                action.retryCount++;
                cov_205auhcslx().s[68]++;
                if (action.retryCount < this.MAX_RETRY_ATTEMPTS) {
                  cov_205auhcslx().b[12][0]++;
                  cov_205auhcslx().s[69]++;
                  failedActions.push(action);
                } else {
                  cov_205auhcslx().b[12][1]++;
                }
              }
            } catch (error) {
              cov_205auhcslx().s[70]++;
              console.error('Error executing action:', error);
              cov_205auhcslx().s[71]++;
              action.retryCount++;
              cov_205auhcslx().s[72]++;
              if (action.retryCount < this.MAX_RETRY_ATTEMPTS) {
                cov_205auhcslx().b[13][0]++;
                cov_205auhcslx().s[73]++;
                failedActions.push(action);
              } else {
                cov_205auhcslx().b[13][1]++;
              }
            }
          }
          cov_205auhcslx().s[74]++;
          yield AsyncStorage.setItem(this.OFFLINE_ACTIONS_KEY, JSON.stringify(failedActions));
          cov_205auhcslx().s[75]++;
          yield AsyncStorage.setItem(this.LAST_SYNC_KEY, Date.now().toString());
          cov_205auhcslx().s[76]++;
          this.notifySyncCallbacks();
        } catch (error) {
          cov_205auhcslx().s[77]++;
          console.error('Error syncing pending actions:', error);
        }
      });
      function syncPendingActions() {
        return _syncPendingActions.apply(this, arguments);
      }
      return syncPendingActions;
    }())
  }, {
    key: "executeAction",
    value: (function () {
      var _executeAction = _asyncToGenerator(function* (action) {
        cov_205auhcslx().f[10]++;
        cov_205auhcslx().s[78]++;
        try {
          cov_205auhcslx().s[79]++;
          switch (action.type) {
            case 'CREATE_TRAINING_SESSION':
              cov_205auhcslx().b[14][0]++;
              cov_205auhcslx().s[80]++;
              return yield this.syncTrainingSession(action.data);
            case 'UPDATE_SKILL_STATS':
              cov_205auhcslx().b[14][1]++;
              cov_205auhcslx().s[81]++;
              return yield this.syncSkillStats(action.data);
            case 'CREATE_MATCH_RESULT':
              cov_205auhcslx().b[14][2]++;
              cov_205auhcslx().s[82]++;
              return yield this.syncMatchResult(action.data);
            case 'UPDATE_USER_PROFILE':
              cov_205auhcslx().b[14][3]++;
              cov_205auhcslx().s[83]++;
              return yield this.syncUserProfile(action.data);
            case 'CREATE_ACHIEVEMENT':
              cov_205auhcslx().b[14][4]++;
              cov_205auhcslx().s[84]++;
              return yield this.syncAchievement(action.data);
            default:
              cov_205auhcslx().b[14][5]++;
              cov_205auhcslx().s[85]++;
              console.warn('Unknown action type:', action.type);
              cov_205auhcslx().s[86]++;
              return false;
          }
        } catch (error) {
          cov_205auhcslx().s[87]++;
          console.error('Error executing action:', error);
          cov_205auhcslx().s[88]++;
          return false;
        }
      });
      function executeAction(_x8) {
        return _executeAction.apply(this, arguments);
      }
      return executeAction;
    }())
  }, {
    key: "getSyncStatus",
    value: (function () {
      var _getSyncStatus = _asyncToGenerator(function* () {
        cov_205auhcslx().f[11]++;
        cov_205auhcslx().s[89]++;
        try {
          var pendingActions = (cov_205auhcslx().s[90]++, yield this.getPendingActions());
          var lastSyncTime = (cov_205auhcslx().s[91]++, yield AsyncStorage.getItem(this.LAST_SYNC_KEY));
          cov_205auhcslx().s[92]++;
          return {
            isOnline: this.isOnline,
            lastSyncTime: lastSyncTime ? (cov_205auhcslx().b[15][0]++, parseInt(lastSyncTime)) : (cov_205auhcslx().b[15][1]++, 0),
            pendingActions: pendingActions.length,
            failedActions: pendingActions.filter(function (a) {
              cov_205auhcslx().f[12]++;
              cov_205auhcslx().s[93]++;
              return a.retryCount > 0;
            }).length
          };
        } catch (error) {
          cov_205auhcslx().s[94]++;
          console.error('Error getting sync status:', error);
          cov_205auhcslx().s[95]++;
          return {
            isOnline: this.isOnline,
            lastSyncTime: 0,
            pendingActions: 0,
            failedActions: 0
          };
        }
      });
      function getSyncStatus() {
        return _getSyncStatus.apply(this, arguments);
      }
      return getSyncStatus;
    }())
  }, {
    key: "onSyncStatusChange",
    value: function onSyncStatusChange(callback) {
      var _this3 = this;
      cov_205auhcslx().f[13]++;
      cov_205auhcslx().s[96]++;
      this.syncCallbacks.push(callback);
      cov_205auhcslx().s[97]++;
      return function () {
        cov_205auhcslx().f[14]++;
        var index = (cov_205auhcslx().s[98]++, _this3.syncCallbacks.indexOf(callback));
        cov_205auhcslx().s[99]++;
        if (index > -1) {
          cov_205auhcslx().b[16][0]++;
          cov_205auhcslx().s[100]++;
          _this3.syncCallbacks.splice(index, 1);
        } else {
          cov_205auhcslx().b[16][1]++;
        }
      };
    }
  }, {
    key: "isDeviceOnline",
    value: function isDeviceOnline() {
      cov_205auhcslx().f[15]++;
      cov_205auhcslx().s[101]++;
      return this.isOnline;
    }
  }, {
    key: "cacheUserData",
    value: (function () {
      var _cacheUserData = _asyncToGenerator(function* (userId, userData) {
        cov_205auhcslx().f[16]++;
        cov_205auhcslx().s[102]++;
        yield this.cacheData(`user_${userId}`, userData, 60);
      });
      function cacheUserData(_x9, _x0) {
        return _cacheUserData.apply(this, arguments);
      }
      return cacheUserData;
    }())
  }, {
    key: "cacheTrainingSessions",
    value: (function () {
      var _cacheTrainingSessions = _asyncToGenerator(function* (userId, sessions) {
        cov_205auhcslx().f[17]++;
        cov_205auhcslx().s[103]++;
        yield this.cacheData(`training_sessions_${userId}`, sessions, 30);
      });
      function cacheTrainingSessions(_x1, _x10) {
        return _cacheTrainingSessions.apply(this, arguments);
      }
      return cacheTrainingSessions;
    }())
  }, {
    key: "cacheSkillStats",
    value: (function () {
      var _cacheSkillStats = _asyncToGenerator(function* (userId, stats) {
        cov_205auhcslx().f[18]++;
        cov_205auhcslx().s[104]++;
        yield this.cacheData(`skill_stats_${userId}`, stats, 60);
      });
      function cacheSkillStats(_x11, _x12) {
        return _cacheSkillStats.apply(this, arguments);
      }
      return cacheSkillStats;
    }())
  }, {
    key: "getCachedUserData",
    value: (function () {
      var _getCachedUserData = _asyncToGenerator(function* (userId) {
        cov_205auhcslx().f[19]++;
        cov_205auhcslx().s[105]++;
        return yield this.getCachedData(`user_${userId}`);
      });
      function getCachedUserData(_x13) {
        return _getCachedUserData.apply(this, arguments);
      }
      return getCachedUserData;
    }())
  }, {
    key: "getCachedTrainingSessions",
    value: (function () {
      var _getCachedTrainingSessions = _asyncToGenerator(function* (userId) {
        cov_205auhcslx().f[20]++;
        cov_205auhcslx().s[106]++;
        return (cov_205auhcslx().b[17][0]++, yield this.getCachedData(`training_sessions_${userId}`)) || (cov_205auhcslx().b[17][1]++, []);
      });
      function getCachedTrainingSessions(_x14) {
        return _getCachedTrainingSessions.apply(this, arguments);
      }
      return getCachedTrainingSessions;
    }())
  }, {
    key: "getCachedSkillStats",
    value: (function () {
      var _getCachedSkillStats = _asyncToGenerator(function* (userId) {
        cov_205auhcslx().f[21]++;
        cov_205auhcslx().s[107]++;
        return yield this.getCachedData(`skill_stats_${userId}`);
      });
      function getCachedSkillStats(_x15) {
        return _getCachedSkillStats.apply(this, arguments);
      }
      return getCachedSkillStats;
    }())
  }, {
    key: "syncTrainingSession",
    value: function () {
      var _syncTrainingSession = _asyncToGenerator(function* (data) {
        cov_205auhcslx().f[22]++;
        cov_205auhcslx().s[108]++;
        try {
          var _ref3 = (cov_205auhcslx().s[109]++, yield Promise.resolve().then(function () {
              return _interopRequireWildcard(require("../lib/supabase"));
            })),
            supabase = _ref3.supabase;
          var _ref4 = (cov_205auhcslx().s[110]++, yield supabase.from('training_sessions').insert(data)),
            error = _ref4.error;
          cov_205auhcslx().s[111]++;
          return !error;
        } catch (error) {
          cov_205auhcslx().s[112]++;
          console.error('Error syncing training session:', error);
          cov_205auhcslx().s[113]++;
          return false;
        }
      });
      function syncTrainingSession(_x16) {
        return _syncTrainingSession.apply(this, arguments);
      }
      return syncTrainingSession;
    }()
  }, {
    key: "syncSkillStats",
    value: function () {
      var _syncSkillStats = _asyncToGenerator(function* (data) {
        cov_205auhcslx().f[23]++;
        cov_205auhcslx().s[114]++;
        try {
          var _ref5 = (cov_205auhcslx().s[115]++, yield Promise.resolve().then(function () {
              return _interopRequireWildcard(require("../lib/supabase"));
            })),
            supabase = _ref5.supabase;
          var _ref6 = (cov_205auhcslx().s[116]++, yield supabase.from('skill_stats').update(data.stats).eq('user_id', data.userId)),
            error = _ref6.error;
          cov_205auhcslx().s[117]++;
          return !error;
        } catch (error) {
          cov_205auhcslx().s[118]++;
          console.error('Error syncing skill stats:', error);
          cov_205auhcslx().s[119]++;
          return false;
        }
      });
      function syncSkillStats(_x17) {
        return _syncSkillStats.apply(this, arguments);
      }
      return syncSkillStats;
    }()
  }, {
    key: "syncMatchResult",
    value: function () {
      var _syncMatchResult = _asyncToGenerator(function* (data) {
        cov_205auhcslx().f[24]++;
        cov_205auhcslx().s[120]++;
        try {
          var _ref7 = (cov_205auhcslx().s[121]++, yield Promise.resolve().then(function () {
              return _interopRequireWildcard(require("../lib/supabase"));
            })),
            supabase = _ref7.supabase;
          var _ref8 = (cov_205auhcslx().s[122]++, yield supabase.from('match_results').insert(data)),
            error = _ref8.error;
          cov_205auhcslx().s[123]++;
          return !error;
        } catch (error) {
          cov_205auhcslx().s[124]++;
          console.error('Error syncing match result:', error);
          cov_205auhcslx().s[125]++;
          return false;
        }
      });
      function syncMatchResult(_x18) {
        return _syncMatchResult.apply(this, arguments);
      }
      return syncMatchResult;
    }()
  }, {
    key: "syncUserProfile",
    value: function () {
      var _syncUserProfile = _asyncToGenerator(function* (data) {
        cov_205auhcslx().f[25]++;
        cov_205auhcslx().s[126]++;
        try {
          var _ref9 = (cov_205auhcslx().s[127]++, yield Promise.resolve().then(function () {
              return _interopRequireWildcard(require("../lib/supabase"));
            })),
            supabase = _ref9.supabase;
          var _ref0 = (cov_205auhcslx().s[128]++, yield supabase.from('users').update(data.profile).eq('id', data.userId)),
            error = _ref0.error;
          cov_205auhcslx().s[129]++;
          return !error;
        } catch (error) {
          cov_205auhcslx().s[130]++;
          console.error('Error syncing user profile:', error);
          cov_205auhcslx().s[131]++;
          return false;
        }
      });
      function syncUserProfile(_x19) {
        return _syncUserProfile.apply(this, arguments);
      }
      return syncUserProfile;
    }()
  }, {
    key: "syncAchievement",
    value: function () {
      var _syncAchievement = _asyncToGenerator(function* (data) {
        cov_205auhcslx().f[26]++;
        cov_205auhcslx().s[132]++;
        try {
          var _ref1 = (cov_205auhcslx().s[133]++, yield Promise.resolve().then(function () {
              return _interopRequireWildcard(require("../lib/supabase"));
            })),
            supabase = _ref1.supabase;
          var _ref10 = (cov_205auhcslx().s[134]++, yield supabase.from('achievements').insert(data)),
            error = _ref10.error;
          cov_205auhcslx().s[135]++;
          return !error;
        } catch (error) {
          cov_205auhcslx().s[136]++;
          console.error('Error syncing achievement:', error);
          cov_205auhcslx().s[137]++;
          return false;
        }
      });
      function syncAchievement(_x20) {
        return _syncAchievement.apply(this, arguments);
      }
      return syncAchievement;
    }()
  }, {
    key: "notifySyncCallbacks",
    value: function () {
      var _notifySyncCallbacks = _asyncToGenerator(function* () {
        cov_205auhcslx().f[27]++;
        var status = (cov_205auhcslx().s[138]++, yield this.getSyncStatus());
        cov_205auhcslx().s[139]++;
        this.syncCallbacks.forEach(function (callback) {
          cov_205auhcslx().f[28]++;
          cov_205auhcslx().s[140]++;
          return callback(status);
        });
      });
      function notifySyncCallbacks() {
        return _notifySyncCallbacks.apply(this, arguments);
      }
      return notifySyncCallbacks;
    }()
  }]);
}();
export var offlineService = (cov_205auhcslx().s[141]++, new OfflineService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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