a1b72ee0e054a99c06b616e29b6dc798
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1iz0px6adx() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\edge\\SmartLoadBalancer.ts";
  var hash = "6b3f2d9da3e6f9971572df1c0aeae2e756e2d044";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\edge\\SmartLoadBalancer.ts",
    statementMap: {
      "0": {
        start: {
          line: 88,
          column: 59
        },
        end: {
          line: 88,
          column: 68
        }
      },
      "1": {
        start: {
          line: 90,
          column: 97
        },
        end: {
          line: 90,
          column: 99
        }
      },
      "2": {
        start: {
          line: 91,
          column: 101
        },
        end: {
          line: 91,
          column: 110
        }
      },
      "3": {
        start: {
          line: 92,
          column: 49
        },
        end: {
          line: 92,
          column: 58
        }
      },
      "4": {
        start: {
          line: 95,
          column: 43
        },
        end: {
          line: 95,
          column: 48
        }
      },
      "5": {
        start: {
          line: 96,
          column: 47
        },
        end: {
          line: 96,
          column: 48
        }
      },
      "6": {
        start: {
          line: 97,
          column: 45
        },
        end: {
          line: 97,
          column: 50
        }
      },
      "7": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 107,
          column: 6
        }
      },
      "8": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 63
        }
      },
      "9": {
        start: {
          line: 110,
          column: 4
        },
        end: {
          line: 110,
          column: 34
        }
      },
      "10": {
        start: {
          line: 117,
          column: 4
        },
        end: {
          line: 133,
          column: 5
        }
      },
      "11": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 44
        }
      },
      "12": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 122,
          column: 35
        }
      },
      "13": {
        start: {
          line: 125,
          column: 6
        },
        end: {
          line: 125,
          column: 53
        }
      },
      "14": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 34
        }
      },
      "15": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 66
        }
      },
      "16": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 132,
          column: 72
        }
      },
      "17": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 167,
          column: 5
        }
      },
      "18": {
        start: {
          line: 141,
          column: 24
        },
        end: {
          line: 141,
          column: 34
        }
      },
      "19": {
        start: {
          line: 144,
          column: 33
        },
        end: {
          line: 144,
          column: 73
        }
      },
      "20": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 148,
          column: 7
        }
      },
      "21": {
        start: {
          line: 147,
          column: 8
        },
        end: {
          line: 147,
          column: 76
        }
      },
      "22": {
        start: {
          line: 151,
          column: 30
        },
        end: {
          line: 151,
          column: 91
        }
      },
      "23": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 154,
          column: 71
        }
      },
      "24": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 58
        }
      },
      "25": {
        start: {
          line: 159,
          column: 29
        },
        end: {
          line: 159,
          column: 51
        }
      },
      "26": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 85
        }
      },
      "27": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 29
        }
      },
      "28": {
        start: {
          line: 165,
          column: 6
        },
        end: {
          line: 165,
          column: 55
        }
      },
      "29": {
        start: {
          line: 166,
          column: 6
        },
        end: {
          line: 166,
          column: 54
        }
      },
      "30": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 186,
          column: 5
        }
      },
      "31": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 89
        }
      },
      "32": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 65
        }
      },
      "33": {
        start: {
          line: 178,
          column: 6
        },
        end: {
          line: 185,
          column: 8
        }
      },
      "34": {
        start: {
          line: 201,
          column: 22
        },
        end: {
          line: 201,
          column: 64
        }
      },
      "35": {
        start: {
          line: 202,
          column: 29
        },
        end: {
          line: 202,
          column: 83
        }
      },
      "36": {
        start: {
          line: 202,
          column: 52
        },
        end: {
          line: 202,
          column: 82
        }
      },
      "37": {
        start: {
          line: 204,
          column: 21
        },
        end: {
          line: 204,
          column: 89
        }
      },
      "38": {
        start: {
          line: 204,
          column: 51
        },
        end: {
          line: 204,
          column: 85
        }
      },
      "39": {
        start: {
          line: 205,
          column: 28
        },
        end: {
          line: 207,
          column: 9
        }
      },
      "40": {
        start: {
          line: 206,
          column: 38
        },
        end: {
          line: 206,
          column: 66
        }
      },
      "41": {
        start: {
          line: 210,
          column: 26
        },
        end: {
          line: 210,
          column: 99
        }
      },
      "42": {
        start: {
          line: 210,
          column: 56
        },
        end: {
          line: 210,
          column: 95
        }
      },
      "43": {
        start: {
          line: 211,
          column: 31
        },
        end: {
          line: 212,
          column: 82
        }
      },
      "44": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 78
        }
      },
      "45": {
        start: {
          line: 213,
          column: 22
        },
        end: {
          line: 213,
          column: 106
        }
      },
      "46": {
        start: {
          line: 216,
          column: 57
        },
        end: {
          line: 216,
          column: 59
        }
      },
      "47": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 219,
          column: 7
        }
      },
      "48": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 83
        }
      },
      "49": {
        start: {
          line: 222,
          column: 56
        },
        end: {
          line: 222,
          column: 58
        }
      },
      "50": {
        start: {
          line: 223,
          column: 4
        },
        end: {
          line: 226,
          column: 7
        }
      },
      "51": {
        start: {
          line: 224,
          column: 26
        },
        end: {
          line: 224,
          column: 75
        }
      },
      "52": {
        start: {
          line: 225,
          column: 6
        },
        end: {
          line: 225,
          column: 47
        }
      },
      "53": {
        start: {
          line: 228,
          column: 4
        },
        end: {
          line: 236,
          column: 6
        }
      },
      "54": {
        start: {
          line: 243,
          column: 4
        },
        end: {
          line: 243,
          column: 80
        }
      },
      "55": {
        start: {
          line: 244,
          column: 4
        },
        end: {
          line: 244,
          column: 80
        }
      },
      "56": {
        start: {
          line: 251,
          column: 42
        },
        end: {
          line: 266,
          column: 5
        }
      },
      "57": {
        start: {
          line: 268,
          column: 4
        },
        end: {
          line: 268,
          column: 57
        }
      },
      "58": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 269,
          column: 58
        }
      },
      "59": {
        start: {
          line: 276,
          column: 4
        },
        end: {
          line: 276,
          column: 45
        }
      },
      "60": {
        start: {
          line: 277,
          column: 4
        },
        end: {
          line: 277,
          column: 44
        }
      },
      "61": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 278,
          column: 59
        }
      },
      "62": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 294,
          column: 7
        }
      },
      "63": {
        start: {
          line: 296,
          column: 4
        },
        end: {
          line: 305,
          column: 7
        }
      },
      "64": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 317,
          column: 7
        }
      },
      "65": {
        start: {
          line: 319,
          column: 4
        },
        end: {
          line: 319,
          column: 48
        }
      },
      "66": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 329,
          column: 8
        }
      },
      "67": {
        start: {
          line: 325,
          column: 8
        },
        end: {
          line: 328,
          column: 61
        }
      },
      "68": {
        start: {
          line: 340,
          column: 4
        },
        end: {
          line: 367,
          column: 5
        }
      },
      "69": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 342,
          column: 124
        }
      },
      "70": {
        start: {
          line: 343,
          column: 8
        },
        end: {
          line: 343,
          column: 14
        }
      },
      "71": {
        start: {
          line: 346,
          column: 8
        },
        end: {
          line: 346,
          column: 78
        }
      },
      "72": {
        start: {
          line: 347,
          column: 8
        },
        end: {
          line: 347,
          column: 47
        }
      },
      "73": {
        start: {
          line: 348,
          column: 8
        },
        end: {
          line: 348,
          column: 25
        }
      },
      "74": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 349,
          column: 14
        }
      },
      "75": {
        start: {
          line: 352,
          column: 8
        },
        end: {
          line: 352,
          column: 80
        }
      },
      "76": {
        start: {
          line: 353,
          column: 8
        },
        end: {
          line: 353,
          column: 50
        }
      },
      "77": {
        start: {
          line: 354,
          column: 8
        },
        end: {
          line: 354,
          column: 25
        }
      },
      "78": {
        start: {
          line: 355,
          column: 8
        },
        end: {
          line: 355,
          column: 14
        }
      },
      "79": {
        start: {
          line: 358,
          column: 8
        },
        end: {
          line: 358,
          column: 77
        }
      },
      "80": {
        start: {
          line: 359,
          column: 8
        },
        end: {
          line: 359,
          column: 37
        }
      },
      "81": {
        start: {
          line: 360,
          column: 8
        },
        end: {
          line: 360,
          column: 25
        }
      },
      "82": {
        start: {
          line: 361,
          column: 8
        },
        end: {
          line: 361,
          column: 14
        }
      },
      "83": {
        start: {
          line: 364,
          column: 8
        },
        end: {
          line: 364,
          column: 72
        }
      },
      "84": {
        start: {
          line: 365,
          column: 8
        },
        end: {
          line: 365,
          column: 41
        }
      },
      "85": {
        start: {
          line: 366,
          column: 8
        },
        end: {
          line: 366,
          column: 25
        }
      },
      "86": {
        start: {
          line: 370,
          column: 30
        },
        end: {
          line: 373,
          column: 18
        }
      },
      "87": {
        start: {
          line: 371,
          column: 20
        },
        end: {
          line: 371,
          column: 49
        }
      },
      "88": {
        start: {
          line: 372,
          column: 22
        },
        end: {
          line: 372,
          column: 67
        }
      },
      "89": {
        start: {
          line: 376,
          column: 34
        },
        end: {
          line: 376,
          column: 86
        }
      },
      "90": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 385,
          column: 6
        }
      },
      "91": {
        start: {
          line: 393,
          column: 28
        },
        end: {
          line: 420,
          column: 6
        }
      },
      "92": {
        start: {
          line: 394,
          column: 18
        },
        end: {
          line: 394,
          column: 19
        }
      },
      "93": {
        start: {
          line: 397,
          column: 32
        },
        end: {
          line: 397,
          column: 84
        }
      },
      "94": {
        start: {
          line: 398,
          column: 6
        },
        end: {
          line: 398,
          column: 39
        }
      },
      "95": {
        start: {
          line: 401,
          column: 28
        },
        end: {
          line: 403,
          column: 12
        }
      },
      "96": {
        start: {
          line: 404,
          column: 6
        },
        end: {
          line: 404,
          column: 36
        }
      },
      "97": {
        start: {
          line: 407,
          column: 23
        },
        end: {
          line: 407,
          column: 71
        }
      },
      "98": {
        start: {
          line: 408,
          column: 6
        },
        end: {
          line: 408,
          column: 30
        }
      },
      "99": {
        start: {
          line: 411,
          column: 24
        },
        end: {
          line: 411,
          column: 104
        }
      },
      "100": {
        start: {
          line: 412,
          column: 6
        },
        end: {
          line: 412,
          column: 32
        }
      },
      "101": {
        start: {
          line: 415,
          column: 6
        },
        end: {
          line: 417,
          column: 7
        }
      },
      "102": {
        start: {
          line: 416,
          column: 8
        },
        end: {
          line: 416,
          column: 20
        }
      },
      "103": {
        start: {
          line: 419,
          column: 6
        },
        end: {
          line: 419,
          column: 33
        }
      },
      "104": {
        start: {
          line: 423,
          column: 4
        },
        end: {
          line: 423,
          column: 54
        }
      },
      "105": {
        start: {
          line: 423,
          column: 35
        },
        end: {
          line: 423,
          column: 52
        }
      },
      "106": {
        start: {
          line: 424,
          column: 21
        },
        end: {
          line: 424,
          column: 39
        }
      },
      "107": {
        start: {
          line: 427,
          column: 4
        },
        end: {
          line: 429,
          column: 5
        }
      },
      "108": {
        start: {
          line: 428,
          column: 6
        },
        end: {
          line: 428,
          column: 72
        }
      },
      "109": {
        start: {
          line: 431,
          column: 4
        },
        end: {
          line: 435,
          column: 6
        }
      },
      "110": {
        start: {
          line: 439,
          column: 4
        },
        end: {
          line: 441,
          column: 6
        }
      },
      "111": {
        start: {
          line: 440,
          column: 6
        },
        end: {
          line: 440,
          column: 73
        }
      },
      "112": {
        start: {
          line: 446,
          column: 24
        },
        end: {
          line: 446,
          column: 73
        }
      },
      "113": {
        start: {
          line: 446,
          column: 54
        },
        end: {
          line: 446,
          column: 69
        }
      },
      "114": {
        start: {
          line: 447,
          column: 19
        },
        end: {
          line: 447,
          column: 46
        }
      },
      "115": {
        start: {
          line: 449,
          column: 24
        },
        end: {
          line: 449,
          column: 25
        }
      },
      "116": {
        start: {
          line: 450,
          column: 4
        },
        end: {
          line: 455,
          column: 5
        }
      },
      "117": {
        start: {
          line: 451,
          column: 6
        },
        end: {
          line: 451,
          column: 39
        }
      },
      "118": {
        start: {
          line: 452,
          column: 6
        },
        end: {
          line: 454,
          column: 7
        }
      },
      "119": {
        start: {
          line: 453,
          column: 8
        },
        end: {
          line: 453,
          column: 24
        }
      },
      "120": {
        start: {
          line: 457,
          column: 4
        },
        end: {
          line: 457,
          column: 24
        }
      },
      "121": {
        start: {
          line: 462,
          column: 18
        },
        end: {
          line: 462,
          column: 47
        }
      },
      "122": {
        start: {
          line: 463,
          column: 4
        },
        end: {
          line: 463,
          column: 28
        }
      },
      "123": {
        start: {
          line: 468,
          column: 17
        },
        end: {
          line: 468,
          column: 81
        }
      },
      "124": {
        start: {
          line: 469,
          column: 18
        },
        end: {
          line: 469,
          column: 41
        }
      },
      "125": {
        start: {
          line: 470,
          column: 4
        },
        end: {
          line: 470,
          column: 28
        }
      },
      "126": {
        start: {
          line: 474,
          column: 4
        },
        end: {
          line: 476,
          column: 5
        }
      },
      "127": {
        start: {
          line: 475,
          column: 6
        },
        end: {
          line: 475,
          column: 16
        }
      },
      "128": {
        start: {
          line: 479,
          column: 48
        },
        end: {
          line: 483,
          column: 5
        }
      },
      "129": {
        start: {
          line: 485,
          column: 30
        },
        end: {
          line: 485,
          column: 62
        }
      },
      "130": {
        start: {
          line: 486,
          column: 4
        },
        end: {
          line: 488,
          column: 5
        }
      },
      "131": {
        start: {
          line: 487,
          column: 6
        },
        end: {
          line: 487,
          column: 17
        }
      },
      "132": {
        start: {
          line: 490,
          column: 4
        },
        end: {
          line: 490,
          column: 14
        }
      },
      "133": {
        start: {
          line: 495,
          column: 24
        },
        end: {
          line: 495,
          column: 52
        }
      },
      "134": {
        start: {
          line: 498,
          column: 23
        },
        end: {
          line: 498,
          column: 76
        }
      },
      "135": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 499,
          column: 37
        }
      },
      "136": {
        start: {
          line: 502,
          column: 48
        },
        end: {
          line: 506,
          column: 5
        }
      },
      "137": {
        start: {
          line: 507,
          column: 4
        },
        end: {
          line: 507,
          column: 54
        }
      },
      "138": {
        start: {
          line: 509,
          column: 4
        },
        end: {
          line: 509,
          column: 37
        }
      },
      "139": {
        start: {
          line: 516,
          column: 4
        },
        end: {
          line: 516,
          column: 47
        }
      },
      "140": {
        start: {
          line: 516,
          column: 32
        },
        end: {
          line: 516,
          column: 47
        }
      },
      "141": {
        start: {
          line: 517,
          column: 4
        },
        end: {
          line: 517,
          column: 87
        }
      },
      "142": {
        start: {
          line: 517,
          column: 60
        },
        end: {
          line: 517,
          column: 87
        }
      },
      "143": {
        start: {
          line: 518,
          column: 4
        },
        end: {
          line: 518,
          column: 20
        }
      },
      "144": {
        start: {
          line: 522,
          column: 21
        },
        end: {
          line: 522,
          column: 58
        }
      },
      "145": {
        start: {
          line: 523,
          column: 4
        },
        end: {
          line: 523,
          column: 26
        }
      },
      "146": {
        start: {
          line: 523,
          column: 19
        },
        end: {
          line: 523,
          column: 26
        }
      },
      "147": {
        start: {
          line: 526,
          column: 4
        },
        end: {
          line: 526,
          column: 34
        }
      },
      "148": {
        start: {
          line: 529,
          column: 4
        },
        end: {
          line: 529,
          column: 46
        }
      },
      "149": {
        start: {
          line: 533,
          column: 4
        },
        end: {
          line: 537,
          column: 7
        }
      },
      "150": {
        start: {
          line: 540,
          column: 4
        },
        end: {
          line: 542,
          column: 5
        }
      },
      "151": {
        start: {
          line: 541,
          column: 6
        },
        end: {
          line: 541,
          column: 42
        }
      },
      "152": {
        start: {
          line: 547,
          column: 46
        },
        end: {
          line: 569,
          column: 5
        }
      },
      "153": {
        start: {
          line: 571,
          column: 4
        },
        end: {
          line: 578,
          column: 6
        }
      },
      "154": {
        start: {
          line: 582,
          column: 4
        },
        end: {
          line: 582,
          column: 72
        }
      },
      "155": {
        start: {
          line: 582,
          column: 59
        },
        end: {
          line: 582,
          column: 72
        }
      },
      "156": {
        start: {
          line: 584,
          column: 20
        },
        end: {
          line: 584,
          column: 56
        }
      },
      "157": {
        start: {
          line: 585,
          column: 4
        },
        end: {
          line: 585,
          column: 31
        }
      },
      "158": {
        start: {
          line: 585,
          column: 18
        },
        end: {
          line: 585,
          column: 31
        }
      },
      "159": {
        start: {
          line: 587,
          column: 4
        },
        end: {
          line: 595,
          column: 5
        }
      },
      "160": {
        start: {
          line: 589,
          column: 6
        },
        end: {
          line: 593,
          column: 7
        }
      },
      "161": {
        start: {
          line: 590,
          column: 8
        },
        end: {
          line: 590,
          column: 31
        }
      },
      "162": {
        start: {
          line: 591,
          column: 8
        },
        end: {
          line: 591,
          column: 29
        }
      },
      "163": {
        start: {
          line: 592,
          column: 8
        },
        end: {
          line: 592,
          column: 21
        }
      },
      "164": {
        start: {
          line: 594,
          column: 6
        },
        end: {
          line: 594,
          column: 18
        }
      },
      "165": {
        start: {
          line: 597,
          column: 4
        },
        end: {
          line: 597,
          column: 17
        }
      },
      "166": {
        start: {
          line: 601,
          column: 4
        },
        end: {
          line: 603,
          column: 55
        }
      },
      "167": {
        start: {
          line: 602,
          column: 6
        },
        end: {
          line: 602,
          column: 33
        }
      },
      "168": {
        start: {
          line: 607,
          column: 22
        },
        end: {
          line: 607,
          column: 64
        }
      },
      "169": {
        start: {
          line: 609,
          column: 4
        },
        end: {
          line: 617,
          column: 5
        }
      },
      "170": {
        start: {
          line: 610,
          column: 6
        },
        end: {
          line: 616,
          column: 7
        }
      },
      "171": {
        start: {
          line: 611,
          column: 29
        },
        end: {
          line: 611,
          column: 69
        }
      },
      "172": {
        start: {
          line: 612,
          column: 8
        },
        end: {
          line: 612,
          column: 58
        }
      },
      "173": {
        start: {
          line: 614,
          column: 8
        },
        end: {
          line: 614,
          column: 72
        }
      },
      "174": {
        start: {
          line: 615,
          column: 8
        },
        end: {
          line: 615,
          column: 45
        }
      },
      "175": {
        start: {
          line: 626,
          column: 25
        },
        end: {
          line: 626,
          column: 49
        }
      },
      "176": {
        start: {
          line: 627,
          column: 20
        },
        end: {
          line: 627,
          column: 40
        }
      },
      "177": {
        start: {
          line: 629,
          column: 4
        },
        end: {
          line: 633,
          column: 6
        }
      },
      "178": {
        start: {
          line: 637,
          column: 4
        },
        end: {
          line: 637,
          column: 43
        }
      },
      "179": {
        start: {
          line: 638,
          column: 4
        },
        end: {
          line: 638,
          column: 61
        }
      },
      "180": {
        start: {
          line: 640,
          column: 4
        },
        end: {
          line: 649,
          column: 5
        }
      },
      "181": {
        start: {
          line: 641,
          column: 6
        },
        end: {
          line: 641,
          column: 41
        }
      },
      "182": {
        start: {
          line: 643,
          column: 22
        },
        end: {
          line: 643,
          column: 59
        }
      },
      "183": {
        start: {
          line: 644,
          column: 6
        },
        end: {
          line: 646,
          column: 7
        }
      },
      "184": {
        start: {
          line: 645,
          column: 8
        },
        end: {
          line: 645,
          column: 29
        }
      },
      "185": {
        start: {
          line: 648,
          column: 6
        },
        end: {
          line: 648,
          column: 43
        }
      },
      "186": {
        start: {
          line: 653,
          column: 4
        },
        end: {
          line: 653,
          column: 41
        }
      },
      "187": {
        start: {
          line: 656,
          column: 4
        },
        end: {
          line: 658,
          column: 5
        }
      },
      "188": {
        start: {
          line: 657,
          column: 6
        },
        end: {
          line: 657,
          column: 92
        }
      },
      "189": {
        start: {
          line: 660,
          column: 20
        },
        end: {
          line: 660,
          column: 58
        }
      },
      "190": {
        start: {
          line: 661,
          column: 4
        },
        end: {
          line: 661,
          column: 23
        }
      },
      "191": {
        start: {
          line: 662,
          column: 4
        },
        end: {
          line: 662,
          column: 37
        }
      },
      "192": {
        start: {
          line: 664,
          column: 4
        },
        end: {
          line: 667,
          column: 5
        }
      },
      "193": {
        start: {
          line: 665,
          column: 6
        },
        end: {
          line: 665,
          column: 28
        }
      },
      "194": {
        start: {
          line: 666,
          column: 6
        },
        end: {
          line: 666,
          column: 74
        }
      },
      "195": {
        start: {
          line: 672,
          column: 4
        },
        end: {
          line: 674,
          column: 15
        }
      },
      "196": {
        start: {
          line: 673,
          column: 6
        },
        end: {
          line: 673,
          column: 36
        }
      },
      "197": {
        start: {
          line: 679,
          column: 26
        },
        end: {
          line: 681,
          column: 5
        }
      },
      "198": {
        start: {
          line: 680,
          column: 15
        },
        end: {
          line: 680,
          column: 53
        }
      },
      "199": {
        start: {
          line: 683,
          column: 4
        },
        end: {
          line: 685,
          column: 5
        }
      },
      "200": {
        start: {
          line: 684,
          column: 6
        },
        end: {
          line: 684,
          column: 87
        }
      },
      "201": {
        start: {
          line: 689,
          column: 15
        },
        end: {
          line: 689,
          column: 16
        }
      },
      "202": {
        start: {
          line: 690,
          column: 4
        },
        end: {
          line: 694,
          column: 5
        }
      },
      "203": {
        start: {
          line: 690,
          column: 17
        },
        end: {
          line: 690,
          column: 18
        }
      },
      "204": {
        start: {
          line: 691,
          column: 19
        },
        end: {
          line: 691,
          column: 36
        }
      },
      "205": {
        start: {
          line: 692,
          column: 6
        },
        end: {
          line: 692,
          column: 41
        }
      },
      "206": {
        start: {
          line: 693,
          column: 6
        },
        end: {
          line: 693,
          column: 25
        }
      },
      "207": {
        start: {
          line: 695,
          column: 4
        },
        end: {
          line: 695,
          column: 26
        }
      },
      "208": {
        start: {
          line: 704,
          column: 4
        },
        end: {
          line: 704,
          column: 56
        }
      },
      "209": {
        start: {
          line: 712,
          column: 26
        },
        end: {
          line: 714,
          column: 5
        }
      },
      "210": {
        start: {
          line: 713,
          column: 15
        },
        end: {
          line: 713,
          column: 53
        }
      },
      "211": {
        start: {
          line: 716,
          column: 29
        },
        end: {
          line: 716,
          column: 70
        }
      },
      "212": {
        start: {
          line: 718,
          column: 4
        },
        end: {
          line: 727,
          column: 6
        }
      },
      "213": {
        start: {
          line: 732,
          column: 33
        },
        end: {
          line: 732,
          column: 56
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 99,
            column: 2
          },
          end: {
            line: 99,
            column: 3
          }
        },
        loc: {
          start: {
            line: 99,
            column: 16
          },
          end: {
            line: 111,
            column: 3
          }
        },
        line: 99
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 116,
            column: 2
          },
          end: {
            line: 116,
            column: 3
          }
        },
        loc: {
          start: {
            line: 116,
            column: 56
          },
          end: {
            line: 134,
            column: 3
          }
        },
        line: 116
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 139,
            column: 2
          },
          end: {
            line: 139,
            column: 3
          }
        },
        loc: {
          start: {
            line: 139,
            column: 72
          },
          end: {
            line: 168,
            column: 3
          }
        },
        line: 139
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 173,
            column: 2
          },
          end: {
            line: 173,
            column: 3
          }
        },
        loc: {
          start: {
            line: 173,
            column: 84
          },
          end: {
            line: 187,
            column: 3
          }
        },
        line: 173
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 192,
            column: 2
          },
          end: {
            line: 192,
            column: 3
          }
        },
        loc: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 237,
            column: 3
          }
        },
        line: 200
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 202,
            column: 46
          },
          end: {
            line: 202,
            column: 47
          }
        },
        loc: {
          start: {
            line: 202,
            column: 52
          },
          end: {
            line: 202,
            column: 82
          }
        },
        line: 202
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 204,
            column: 38
          },
          end: {
            line: 204,
            column: 39
          }
        },
        loc: {
          start: {
            line: 204,
            column: 51
          },
          end: {
            line: 204,
            column: 85
          }
        },
        line: 204
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 206,
            column: 25
          },
          end: {
            line: 206,
            column: 26
          }
        },
        loc: {
          start: {
            line: 206,
            column: 38
          },
          end: {
            line: 206,
            column: 66
          }
        },
        line: 206
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 210,
            column: 43
          },
          end: {
            line: 210,
            column: 44
          }
        },
        loc: {
          start: {
            line: 210,
            column: 56
          },
          end: {
            line: 210,
            column: 95
          }
        },
        line: 210
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 211,
            column: 48
          },
          end: {
            line: 211,
            column: 49
          }
        },
        loc: {
          start: {
            line: 212,
            column: 6
          },
          end: {
            line: 212,
            column: 78
          }
        },
        line: 212
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 217,
            column: 22
          },
          end: {
            line: 217,
            column: 23
          }
        },
        loc: {
          start: {
            line: 217,
            column: 28
          },
          end: {
            line: 219,
            column: 5
          }
        },
        line: 217
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 223,
            column: 22
          },
          end: {
            line: 223,
            column: 23
          }
        },
        loc: {
          start: {
            line: 223,
            column: 28
          },
          end: {
            line: 226,
            column: 5
          }
        },
        line: 223
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 242,
            column: 2
          },
          end: {
            line: 242,
            column: 3
          }
        },
        loc: {
          start: {
            line: 242,
            column: 65
          },
          end: {
            line: 245,
            column: 3
          }
        },
        line: 242
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 250,
            column: 2
          },
          end: {
            line: 250,
            column: 3
          }
        },
        loc: {
          start: {
            line: 250,
            column: 82
          },
          end: {
            line: 270,
            column: 3
          }
        },
        line: 250
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 275,
            column: 2
          },
          end: {
            line: 275,
            column: 3
          }
        },
        loc: {
          start: {
            line: 275,
            column: 50
          },
          end: {
            line: 279,
            column: 3
          }
        },
        line: 275
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 283,
            column: 2
          },
          end: {
            line: 283,
            column: 3
          }
        },
        loc: {
          start: {
            line: 283,
            column: 58
          },
          end: {
            line: 320,
            column: 3
          }
        },
        line: 283
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 322,
            column: 2
          },
          end: {
            line: 322,
            column: 3
          }
        },
        loc: {
          start: {
            line: 322,
            column: 65
          },
          end: {
            line: 330,
            column: 3
          }
        },
        line: 322
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 324,
            column: 14
          },
          end: {
            line: 324,
            column: 15
          }
        },
        loc: {
          start: {
            line: 325,
            column: 8
          },
          end: {
            line: 328,
            column: 61
          }
        },
        line: 325
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 332,
            column: 2
          },
          end: {
            line: 332,
            column: 3
          }
        },
        loc: {
          start: {
            line: 335,
            column: 30
          },
          end: {
            line: 386,
            column: 3
          }
        },
        line: 335
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 371,
            column: 14
          },
          end: {
            line: 371,
            column: 15
          }
        },
        loc: {
          start: {
            line: 371,
            column: 20
          },
          end: {
            line: 371,
            column: 49
          }
        },
        line: 371
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 372,
            column: 12
          },
          end: {
            line: 372,
            column: 13
          }
        },
        loc: {
          start: {
            line: 372,
            column: 22
          },
          end: {
            line: 372,
            column: 67
          }
        },
        line: 372
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 388,
            column: 2
          },
          end: {
            line: 388,
            column: 3
          }
        },
        loc: {
          start: {
            line: 391,
            column: 80
          },
          end: {
            line: 436,
            column: 3
          }
        },
        line: 391
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 393,
            column: 42
          },
          end: {
            line: 393,
            column: 43
          }
        },
        loc: {
          start: {
            line: 393,
            column: 54
          },
          end: {
            line: 420,
            column: 5
          }
        },
        line: 393
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 423,
            column: 25
          },
          end: {
            line: 423,
            column: 26
          }
        },
        loc: {
          start: {
            line: 423,
            column: 35
          },
          end: {
            line: 423,
            column: 52
          }
        },
        line: 423
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 438,
            column: 2
          },
          end: {
            line: 438,
            column: 3
          }
        },
        loc: {
          start: {
            line: 438,
            column: 83
          },
          end: {
            line: 442,
            column: 3
          }
        },
        line: 438
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 439,
            column: 28
          },
          end: {
            line: 439,
            column: 29
          }
        },
        loc: {
          start: {
            line: 440,
            column: 6
          },
          end: {
            line: 440,
            column: 73
          }
        },
        line: 440
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 444,
            column: 2
          },
          end: {
            line: 444,
            column: 3
          }
        },
        loc: {
          start: {
            line: 444,
            column: 85
          },
          end: {
            line: 458,
            column: 3
          }
        },
        line: 444
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 446,
            column: 41
          },
          end: {
            line: 446,
            column: 42
          }
        },
        loc: {
          start: {
            line: 446,
            column: 54
          },
          end: {
            line: 446,
            column: 69
          }
        },
        line: 446
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 460,
            column: 2
          },
          end: {
            line: 460,
            column: 3
          }
        },
        loc: {
          start: {
            line: 460,
            column: 77
          },
          end: {
            line: 464,
            column: 3
          }
        },
        line: 460
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 466,
            column: 2
          },
          end: {
            line: 466,
            column: 3
          }
        },
        loc: {
          start: {
            line: 466,
            column: 98
          },
          end: {
            line: 471,
            column: 3
          }
        },
        line: 466
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 473,
            column: 2
          },
          end: {
            line: 473,
            column: 3
          }
        },
        loc: {
          start: {
            line: 473,
            column: 95
          },
          end: {
            line: 491,
            column: 3
          }
        },
        line: 473
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 493,
            column: 2
          },
          end: {
            line: 493,
            column: 3
          }
        },
        loc: {
          start: {
            line: 493,
            column: 91
          },
          end: {
            line: 510,
            column: 3
          }
        },
        line: 493
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 512,
            column: 2
          },
          end: {
            line: 512,
            column: 3
          }
        },
        loc: {
          start: {
            line: 515,
            column: 46
          },
          end: {
            line: 519,
            column: 3
          }
        },
        line: 515
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 521,
            column: 2
          },
          end: {
            line: 521,
            column: 3
          }
        },
        loc: {
          start: {
            line: 521,
            column: 83
          },
          end: {
            line: 530,
            column: 3
          }
        },
        line: 521
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 532,
            column: 2
          },
          end: {
            line: 532,
            column: 3
          }
        },
        loc: {
          start: {
            line: 532,
            column: 89
          },
          end: {
            line: 543,
            column: 3
          }
        },
        line: 532
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 545,
            column: 2
          },
          end: {
            line: 545,
            column: 3
          }
        },
        loc: {
          start: {
            line: 545,
            column: 79
          },
          end: {
            line: 579,
            column: 3
          }
        },
        line: 545
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 581,
            column: 2
          },
          end: {
            line: 581,
            column: 3
          }
        },
        loc: {
          start: {
            line: 581,
            column: 60
          },
          end: {
            line: 598,
            column: 3
          }
        },
        line: 581
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 600,
            column: 2
          },
          end: {
            line: 600,
            column: 3
          }
        },
        loc: {
          start: {
            line: 600,
            column: 40
          },
          end: {
            line: 604,
            column: 3
          }
        },
        line: 600
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 601,
            column: 16
          },
          end: {
            line: 601,
            column: 17
          }
        },
        loc: {
          start: {
            line: 601,
            column: 22
          },
          end: {
            line: 603,
            column: 5
          }
        },
        line: 601
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 606,
            column: 2
          },
          end: {
            line: 606,
            column: 3
          }
        },
        loc: {
          start: {
            line: 606,
            column: 53
          },
          end: {
            line: 618,
            column: 3
          }
        },
        line: 606
      },
      "40": {
        name: "(anonymous_40)",
        decl: {
          start: {
            line: 620,
            column: 2
          },
          end: {
            line: 620,
            column: 3
          }
        },
        loc: {
          start: {
            line: 624,
            column: 5
          },
          end: {
            line: 634,
            column: 3
          }
        },
        line: 624
      },
      "41": {
        name: "(anonymous_41)",
        decl: {
          start: {
            line: 636,
            column: 2
          },
          end: {
            line: 636,
            column: 3
          }
        },
        loc: {
          start: {
            line: 636,
            column: 83
          },
          end: {
            line: 650,
            column: 3
          }
        },
        line: 636
      },
      "42": {
        name: "(anonymous_42)",
        decl: {
          start: {
            line: 652,
            column: 2
          },
          end: {
            line: 652,
            column: 3
          }
        },
        loc: {
          start: {
            line: 652,
            column: 65
          },
          end: {
            line: 668,
            column: 3
          }
        },
        line: 652
      },
      "43": {
        name: "(anonymous_43)",
        decl: {
          start: {
            line: 670,
            column: 2
          },
          end: {
            line: 670,
            column: 3
          }
        },
        loc: {
          start: {
            line: 670,
            column: 39
          },
          end: {
            line: 675,
            column: 3
          }
        },
        line: 670
      },
      "44": {
        name: "(anonymous_44)",
        decl: {
          start: {
            line: 672,
            column: 16
          },
          end: {
            line: 672,
            column: 17
          }
        },
        loc: {
          start: {
            line: 672,
            column: 22
          },
          end: {
            line: 674,
            column: 5
          }
        },
        line: 672
      },
      "45": {
        name: "(anonymous_45)",
        decl: {
          start: {
            line: 677,
            column: 2
          },
          end: {
            line: 677,
            column: 3
          }
        },
        loc: {
          start: {
            line: 677,
            column: 41
          },
          end: {
            line: 686,
            column: 3
          }
        },
        line: 677
      },
      "46": {
        name: "(anonymous_46)",
        decl: {
          start: {
            line: 680,
            column: 6
          },
          end: {
            line: 680,
            column: 7
          }
        },
        loc: {
          start: {
            line: 680,
            column: 15
          },
          end: {
            line: 680,
            column: 53
          }
        },
        line: 680
      },
      "47": {
        name: "(anonymous_47)",
        decl: {
          start: {
            line: 688,
            column: 2
          },
          end: {
            line: 688,
            column: 3
          }
        },
        loc: {
          start: {
            line: 688,
            column: 42
          },
          end: {
            line: 696,
            column: 3
          }
        },
        line: 688
      },
      "48": {
        name: "(anonymous_48)",
        decl: {
          start: {
            line: 703,
            column: 2
          },
          end: {
            line: 703,
            column: 3
          }
        },
        loc: {
          start: {
            line: 703,
            column: 36
          },
          end: {
            line: 705,
            column: 3
          }
        },
        line: 703
      },
      "49": {
        name: "(anonymous_49)",
        decl: {
          start: {
            line: 707,
            column: 2
          },
          end: {
            line: 707,
            column: 3
          }
        },
        loc: {
          start: {
            line: 710,
            column: 32
          },
          end: {
            line: 728,
            column: 3
          }
        },
        line: 710
      },
      "50": {
        name: "(anonymous_50)",
        decl: {
          start: {
            line: 713,
            column: 6
          },
          end: {
            line: 713,
            column: 7
          }
        },
        loc: {
          start: {
            line: 713,
            column: 15
          },
          end: {
            line: 713,
            column: 53
          }
        },
        line: 713
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 146,
            column: 6
          },
          end: {
            line: 148,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 146,
            column: 6
          },
          end: {
            line: 148,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 146
      },
      "1": {
        loc: {
          start: {
            line: 173,
            column: 30
          },
          end: {
            line: 173,
            column: 54
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 173,
            column: 52
          },
          end: {
            line: 173,
            column: 54
          }
        }],
        line: 173
      },
      "2": {
        loc: {
          start: {
            line: 205,
            column: 28
          },
          end: {
            line: 207,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 206,
            column: 8
          },
          end: {
            line: 206,
            column: 89
          }
        }, {
          start: {
            line: 207,
            column: 8
          },
          end: {
            line: 207,
            column: 9
          }
        }],
        line: 205
      },
      "3": {
        loc: {
          start: {
            line: 213,
            column: 22
          },
          end: {
            line: 213,
            column: 106
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 213,
            column: 42
          },
          end: {
            line: 213,
            column: 102
          }
        }, {
          start: {
            line: 213,
            column: 105
          },
          end: {
            line: 213,
            column: 106
          }
        }],
        line: 213
      },
      "4": {
        loc: {
          start: {
            line: 218,
            column: 41
          },
          end: {
            line: 218,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 218,
            column: 41
          },
          end: {
            line: 218,
            column: 72
          }
        }, {
          start: {
            line: 218,
            column: 76
          },
          end: {
            line: 218,
            column: 77
          }
        }],
        line: 218
      },
      "5": {
        loc: {
          start: {
            line: 325,
            column: 8
          },
          end: {
            line: 328,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 325,
            column: 8
          },
          end: {
            line: 325,
            column: 30
          }
        }, {
          start: {
            line: 326,
            column: 8
          },
          end: {
            line: 326,
            column: 44
          }
        }, {
          start: {
            line: 327,
            column: 8
          },
          end: {
            line: 327,
            column: 47
          }
        }, {
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 328,
            column: 61
          }
        }],
        line: 325
      },
      "6": {
        loc: {
          start: {
            line: 340,
            column: 4
          },
          end: {
            line: 367,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 341,
            column: 6
          },
          end: {
            line: 343,
            column: 14
          }
        }, {
          start: {
            line: 345,
            column: 6
          },
          end: {
            line: 349,
            column: 14
          }
        }, {
          start: {
            line: 351,
            column: 6
          },
          end: {
            line: 355,
            column: 14
          }
        }, {
          start: {
            line: 357,
            column: 6
          },
          end: {
            line: 361,
            column: 14
          }
        }, {
          start: {
            line: 363,
            column: 6
          },
          end: {
            line: 366,
            column: 25
          }
        }],
        line: 340
      },
      "7": {
        loc: {
          start: {
            line: 415,
            column: 6
          },
          end: {
            line: 417,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 415,
            column: 6
          },
          end: {
            line: 417,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 415
      },
      "8": {
        loc: {
          start: {
            line: 415,
            column: 10
          },
          end: {
            line: 415,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 415,
            column: 10
          },
          end: {
            line: 415,
            column: 27
          }
        }, {
          start: {
            line: 415,
            column: 31
          },
          end: {
            line: 415,
            column: 90
          }
        }],
        line: 415
      },
      "9": {
        loc: {
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 429,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 429,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 427
      },
      "10": {
        loc: {
          start: {
            line: 427,
            column: 8
          },
          end: {
            line: 427,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 427,
            column: 8
          },
          end: {
            line: 427,
            column: 25
          }
        }, {
          start: {
            line: 427,
            column: 29
          },
          end: {
            line: 427,
            column: 69
          }
        }],
        line: 427
      },
      "11": {
        loc: {
          start: {
            line: 440,
            column: 6
          },
          end: {
            line: 440,
            column: 73
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 440,
            column: 60
          },
          end: {
            line: 440,
            column: 67
          }
        }, {
          start: {
            line: 440,
            column: 70
          },
          end: {
            line: 440,
            column: 73
          }
        }],
        line: 440
      },
      "12": {
        loc: {
          start: {
            line: 452,
            column: 6
          },
          end: {
            line: 454,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 452,
            column: 6
          },
          end: {
            line: 454,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 452
      },
      "13": {
        loc: {
          start: {
            line: 468,
            column: 33
          },
          end: {
            line: 468,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 468,
            column: 33
          },
          end: {
            line: 468,
            column: 67
          }
        }, {
          start: {
            line: 468,
            column: 71
          },
          end: {
            line: 468,
            column: 80
          }
        }],
        line: 468
      },
      "14": {
        loc: {
          start: {
            line: 474,
            column: 4
          },
          end: {
            line: 476,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 474,
            column: 4
          },
          end: {
            line: 476,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 474
      },
      "15": {
        loc: {
          start: {
            line: 474,
            column: 8
          },
          end: {
            line: 474,
            column: 71
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 474,
            column: 8
          },
          end: {
            line: 474,
            column: 29
          }
        }, {
          start: {
            line: 474,
            column: 33
          },
          end: {
            line: 474,
            column: 71
          }
        }],
        line: 474
      },
      "16": {
        loc: {
          start: {
            line: 485,
            column: 30
          },
          end: {
            line: 485,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 485,
            column: 30
          },
          end: {
            line: 485,
            column: 56
          }
        }, {
          start: {
            line: 485,
            column: 60
          },
          end: {
            line: 485,
            column: 62
          }
        }],
        line: 485
      },
      "17": {
        loc: {
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 488,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 486,
            column: 4
          },
          end: {
            line: 488,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 486
      },
      "18": {
        loc: {
          start: {
            line: 507,
            column: 21
          },
          end: {
            line: 507,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 507,
            column: 21
          },
          end: {
            line: 507,
            column: 46
          }
        }, {
          start: {
            line: 507,
            column: 50
          },
          end: {
            line: 507,
            column: 53
          }
        }],
        line: 507
      },
      "19": {
        loc: {
          start: {
            line: 516,
            column: 4
          },
          end: {
            line: 516,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 516,
            column: 4
          },
          end: {
            line: 516,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 516
      },
      "20": {
        loc: {
          start: {
            line: 517,
            column: 4
          },
          end: {
            line: 517,
            column: 87
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 517,
            column: 4
          },
          end: {
            line: 517,
            column: 87
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 517
      },
      "21": {
        loc: {
          start: {
            line: 517,
            column: 8
          },
          end: {
            line: 517,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 517,
            column: 8
          },
          end: {
            line: 517,
            column: 32
          }
        }, {
          start: {
            line: 517,
            column: 36
          },
          end: {
            line: 517,
            column: 58
          }
        }],
        line: 517
      },
      "22": {
        loc: {
          start: {
            line: 523,
            column: 4
          },
          end: {
            line: 523,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 523,
            column: 4
          },
          end: {
            line: 523,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 523
      },
      "23": {
        loc: {
          start: {
            line: 540,
            column: 4
          },
          end: {
            line: 542,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 540,
            column: 4
          },
          end: {
            line: 542,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 540
      },
      "24": {
        loc: {
          start: {
            line: 582,
            column: 4
          },
          end: {
            line: 582,
            column: 72
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 582,
            column: 4
          },
          end: {
            line: 582,
            column: 72
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 582
      },
      "25": {
        loc: {
          start: {
            line: 585,
            column: 4
          },
          end: {
            line: 585,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 585,
            column: 4
          },
          end: {
            line: 585,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 585
      },
      "26": {
        loc: {
          start: {
            line: 587,
            column: 4
          },
          end: {
            line: 595,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 587,
            column: 4
          },
          end: {
            line: 595,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 587
      },
      "27": {
        loc: {
          start: {
            line: 589,
            column: 6
          },
          end: {
            line: 593,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 589,
            column: 6
          },
          end: {
            line: 593,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 589
      },
      "28": {
        loc: {
          start: {
            line: 631,
            column: 14
          },
          end: {
            line: 631,
            column: 33
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 631,
            column: 24
          },
          end: {
            line: 631,
            column: 27
          }
        }, {
          start: {
            line: 631,
            column: 30
          },
          end: {
            line: 631,
            column: 33
          }
        }],
        line: 631
      },
      "29": {
        loc: {
          start: {
            line: 640,
            column: 4
          },
          end: {
            line: 649,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 640,
            column: 4
          },
          end: {
            line: 649,
            column: 5
          }
        }, {
          start: {
            line: 647,
            column: 11
          },
          end: {
            line: 649,
            column: 5
          }
        }],
        line: 640
      },
      "30": {
        loc: {
          start: {
            line: 644,
            column: 6
          },
          end: {
            line: 646,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 644,
            column: 6
          },
          end: {
            line: 646,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 644
      },
      "31": {
        loc: {
          start: {
            line: 656,
            column: 4
          },
          end: {
            line: 658,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 656,
            column: 4
          },
          end: {
            line: 658,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 656
      },
      "32": {
        loc: {
          start: {
            line: 664,
            column: 4
          },
          end: {
            line: 667,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 664,
            column: 4
          },
          end: {
            line: 667,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 664
      },
      "33": {
        loc: {
          start: {
            line: 683,
            column: 4
          },
          end: {
            line: 685,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 683,
            column: 4
          },
          end: {
            line: 685,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 683
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0
    },
    b: {
      "0": [0, 0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0, 0, 0],
      "6": [0, 0, 0, 0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "6b3f2d9da3e6f9971572df1c0aeae2e756e2d044"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1iz0px6adx = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1iz0px6adx();
import { performanceMonitor } from "../../utils/performance";
var SmartLoadBalancer = function () {
  function SmartLoadBalancer() {
    _classCallCheck(this, SmartLoadBalancer);
    this.serviceEndpoints = (cov_1iz0px6adx().s[0]++, new Map());
    this.trafficHistory = (cov_1iz0px6adx().s[1]++, []);
    this.circuitBreakers = (cov_1iz0px6adx().s[2]++, new Map());
    this.sessionAffinity = (cov_1iz0px6adx().s[3]++, new Map());
    this.HEALTH_CHECK_INTERVAL = (cov_1iz0px6adx().s[4]++, 30000);
    this.CIRCUIT_BREAKER_THRESHOLD = (cov_1iz0px6adx().s[5]++, 5);
    this.CIRCUIT_BREAKER_TIMEOUT = (cov_1iz0px6adx().s[6]++, 60000);
    cov_1iz0px6adx().f[0]++;
    cov_1iz0px6adx().s[7]++;
    this.loadBalancingStrategy = {
      algorithm: 'ai_optimized',
      healthCheckInterval: this.HEALTH_CHECK_INTERVAL,
      failoverThreshold: 0.1,
      circuitBreakerEnabled: true,
      stickySession: true,
      geoRouting: true
    };
    cov_1iz0px6adx().s[8]++;
    this.trafficPredictionModel = new TrafficPredictionModel();
    cov_1iz0px6adx().s[9]++;
    this.initializeLoadBalancer();
  }
  return _createClass(SmartLoadBalancer, [{
    key: "initializeLoadBalancer",
    value: (function () {
      var _initializeLoadBalancer = _asyncToGenerator(function* () {
        cov_1iz0px6adx().f[1]++;
        cov_1iz0px6adx().s[10]++;
        try {
          cov_1iz0px6adx().s[11]++;
          yield this.discoverServiceEndpoints();
          cov_1iz0px6adx().s[12]++;
          this.startHealthMonitoring();
          cov_1iz0px6adx().s[13]++;
          yield this.trafficPredictionModel.initialize();
          cov_1iz0px6adx().s[14]++;
          this.startTrafficAnalysis();
          cov_1iz0px6adx().s[15]++;
          console.log('Smart Load Balancer initialized successfully');
        } catch (error) {
          cov_1iz0px6adx().s[16]++;
          console.error('Failed to initialize Smart Load Balancer:', error);
        }
      });
      function initializeLoadBalancer() {
        return _initializeLoadBalancer.apply(this, arguments);
      }
      return initializeLoadBalancer;
    }())
  }, {
    key: "routeRequest",
    value: (function () {
      var _routeRequest = _asyncToGenerator(function* (request) {
        cov_1iz0px6adx().f[2]++;
        cov_1iz0px6adx().s[17]++;
        try {
          var startTime = (cov_1iz0px6adx().s[18]++, Date.now());
          var availableEndpoints = (cov_1iz0px6adx().s[19]++, this.getAvailableEndpoints(request.type));
          cov_1iz0px6adx().s[20]++;
          if (availableEndpoints.length === 0) {
            cov_1iz0px6adx().b[0][0]++;
            cov_1iz0px6adx().s[21]++;
            throw new Error(`No available endpoints for type: ${request.type}`);
          } else {
            cov_1iz0px6adx().b[0][1]++;
          }
          var routingDecision = (cov_1iz0px6adx().s[22]++, yield this.selectOptimalEndpoint(request, availableEndpoints));
          cov_1iz0px6adx().s[23]++;
          this.updateEndpointMetrics(routingDecision.endpoint.id, request);
          cov_1iz0px6adx().s[24]++;
          this.trackRoutingDecision(request, routingDecision);
          var processingTime = (cov_1iz0px6adx().s[25]++, Date.now() - startTime);
          cov_1iz0px6adx().s[26]++;
          performanceMonitor.trackDatabaseQuery('load_balancer_routing', processingTime);
          cov_1iz0px6adx().s[27]++;
          return routingDecision;
        } catch (error) {
          cov_1iz0px6adx().s[28]++;
          console.error('Failed to route request:', error);
          cov_1iz0px6adx().s[29]++;
          return this.getFallbackRoutingDecision(request);
        }
      });
      function routeRequest(_x) {
        return _routeRequest.apply(this, arguments);
      }
      return routeRequest;
    }())
  }, {
    key: "getTrafficPredictions",
    value: (function () {
      var _getTrafficPredictions = _asyncToGenerator(function* () {
        var timeHorizon = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1iz0px6adx().b[1][0]++, 60);
        cov_1iz0px6adx().f[3]++;
        cov_1iz0px6adx().s[30]++;
        try {
          cov_1iz0px6adx().s[31]++;
          return yield this.trafficPredictionModel.predict(this.trafficHistory, timeHorizon);
        } catch (error) {
          cov_1iz0px6adx().s[32]++;
          console.error('Failed to get traffic predictions:', error);
          cov_1iz0px6adx().s[33]++;
          return {
            timeHorizon: timeHorizon,
            expectedRequests: 0,
            peakTimes: [],
            regionalDistribution: {},
            typeDistribution: {},
            confidence: 0
          };
        }
      });
      function getTrafficPredictions() {
        return _getTrafficPredictions.apply(this, arguments);
      }
      return getTrafficPredictions;
    }())
  }, {
    key: "getLoadBalancingMetrics",
    value: function getLoadBalancingMetrics() {
      cov_1iz0px6adx().f[4]++;
      var endpoints = (cov_1iz0px6adx().s[34]++, Array.from(this.serviceEndpoints.values()));
      var healthyEndpoints = (cov_1iz0px6adx().s[35]++, endpoints.filter(function (ep) {
        cov_1iz0px6adx().f[5]++;
        cov_1iz0px6adx().s[36]++;
        return ep.health.status === 'healthy';
      }));
      var totalRPS = (cov_1iz0px6adx().s[37]++, endpoints.reduce(function (sum, ep) {
        cov_1iz0px6adx().f[6]++;
        cov_1iz0px6adx().s[38]++;
        return sum + ep.metrics.requestsPerSecond;
      }, 0));
      var avgResponseTime = (cov_1iz0px6adx().s[39]++, endpoints.length > 0 ? (cov_1iz0px6adx().b[2][0]++, endpoints.reduce(function (sum, ep) {
        cov_1iz0px6adx().f[7]++;
        cov_1iz0px6adx().s[40]++;
        return sum + ep.health.responseTime;
      }, 0) / endpoints.length) : (cov_1iz0px6adx().b[2][1]++, 0));
      var totalRequests = (cov_1iz0px6adx().s[41]++, endpoints.reduce(function (sum, ep) {
        cov_1iz0px6adx().f[8]++;
        cov_1iz0px6adx().s[42]++;
        return sum + ep.metrics.requestsPerSecond * 60;
      }, 0));
      var successfulRequests = (cov_1iz0px6adx().s[43]++, endpoints.reduce(function (sum, ep) {
        cov_1iz0px6adx().f[9]++;
        cov_1iz0px6adx().s[44]++;
        return sum + ep.metrics.requestsPerSecond * 60 * ep.metrics.successRate / 100;
      }, 0));
      var errorRate = (cov_1iz0px6adx().s[45]++, totalRequests > 0 ? (cov_1iz0px6adx().b[3][0]++, (totalRequests - successfulRequests) / totalRequests * 100) : (cov_1iz0px6adx().b[3][1]++, 0));
      var regionalDistribution = (cov_1iz0px6adx().s[46]++, {});
      cov_1iz0px6adx().s[47]++;
      endpoints.forEach(function (ep) {
        cov_1iz0px6adx().f[10]++;
        cov_1iz0px6adx().s[48]++;
        regionalDistribution[ep.region] = ((cov_1iz0px6adx().b[4][0]++, regionalDistribution[ep.region]) || (cov_1iz0px6adx().b[4][1]++, 0)) + 1;
      });
      var endpointUtilization = (cov_1iz0px6adx().s[49]++, {});
      cov_1iz0px6adx().s[50]++;
      endpoints.forEach(function (ep) {
        cov_1iz0px6adx().f[11]++;
        var utilization = (cov_1iz0px6adx().s[51]++, ep.currentConnections / ep.maxConnections * 100);
        cov_1iz0px6adx().s[52]++;
        endpointUtilization[ep.id] = utilization;
      });
      cov_1iz0px6adx().s[53]++;
      return {
        totalEndpoints: endpoints.length,
        healthyEndpoints: healthyEndpoints.length,
        averageResponseTime: avgResponseTime,
        totalRequestsPerSecond: totalRPS,
        errorRate: errorRate,
        regionalDistribution: regionalDistribution,
        endpointUtilization: endpointUtilization
      };
    }
  }, {
    key: "updateStrategy",
    value: function updateStrategy(strategy) {
      cov_1iz0px6adx().f[12]++;
      cov_1iz0px6adx().s[54]++;
      this.loadBalancingStrategy = Object.assign({}, this.loadBalancingStrategy, strategy);
      cov_1iz0px6adx().s[55]++;
      console.log('Updated load balancing strategy:', this.loadBalancingStrategy);
    }
  }, {
    key: "addServiceEndpoint",
    value: function addServiceEndpoint(endpoint) {
      cov_1iz0px6adx().f[13]++;
      var fullEndpoint = (cov_1iz0px6adx().s[56]++, Object.assign({}, endpoint, {
        health: {
          status: 'healthy',
          lastCheck: Date.now(),
          responseTime: 0,
          errorRate: 0,
          uptime: 100
        },
        metrics: {
          requestsPerSecond: 0,
          averageResponseTime: 0,
          throughput: 0,
          successRate: 100
        }
      }));
      cov_1iz0px6adx().s[57]++;
      this.serviceEndpoints.set(endpoint.id, fullEndpoint);
      cov_1iz0px6adx().s[58]++;
      console.log(`Added service endpoint: ${endpoint.id}`);
    }
  }, {
    key: "removeServiceEndpoint",
    value: function removeServiceEndpoint(endpointId) {
      cov_1iz0px6adx().f[14]++;
      cov_1iz0px6adx().s[59]++;
      this.serviceEndpoints.delete(endpointId);
      cov_1iz0px6adx().s[60]++;
      this.circuitBreakers.delete(endpointId);
      cov_1iz0px6adx().s[61]++;
      console.log(`Removed service endpoint: ${endpointId}`);
    }
  }, {
    key: "discoverServiceEndpoints",
    value: function () {
      var _discoverServiceEndpoints = _asyncToGenerator(function* () {
        cov_1iz0px6adx().f[15]++;
        cov_1iz0px6adx().s[62]++;
        this.addServiceEndpoint({
          id: 'api_us_east',
          url: 'https://api-us-east.acemind.app',
          region: 'us-east-1',
          type: 'api',
          weight: 100,
          maxConnections: 1000,
          currentConnections: 0,
          capacity: {
            cpu: 30,
            memory: 40,
            bandwidth: 1000,
            storage: 20
          }
        });
        cov_1iz0px6adx().s[63]++;
        this.addServiceEndpoint({
          id: 'api_eu_west',
          url: 'https://api-eu-west.acemind.app',
          region: 'eu-west-1',
          type: 'api',
          weight: 100,
          maxConnections: 800,
          currentConnections: 0,
          capacity: {
            cpu: 25,
            memory: 35,
            bandwidth: 800,
            storage: 15
          }
        });
        cov_1iz0px6adx().s[64]++;
        this.addServiceEndpoint({
          id: 'cdn_global',
          url: 'https://cdn.acemind.app',
          region: 'global',
          type: 'cdn',
          weight: 100,
          maxConnections: 10000,
          currentConnections: 0,
          capacity: {
            cpu: 10,
            memory: 20,
            bandwidth: 10000,
            storage: 50
          }
        });
        cov_1iz0px6adx().s[65]++;
        console.log('Discovered service endpoints');
      });
      function discoverServiceEndpoints() {
        return _discoverServiceEndpoints.apply(this, arguments);
      }
      return discoverServiceEndpoints;
    }()
  }, {
    key: "getAvailableEndpoints",
    value: function getAvailableEndpoints(type) {
      var _this = this;
      cov_1iz0px6adx().f[16]++;
      cov_1iz0px6adx().s[66]++;
      return Array.from(this.serviceEndpoints.values()).filter(function (endpoint) {
        cov_1iz0px6adx().f[17]++;
        cov_1iz0px6adx().s[67]++;
        return (cov_1iz0px6adx().b[5][0]++, endpoint.type === type) && (cov_1iz0px6adx().b[5][1]++, endpoint.health.status === 'healthy') && (cov_1iz0px6adx().b[5][2]++, !_this.isCircuitBreakerOpen(endpoint.id)) && (cov_1iz0px6adx().b[5][3]++, endpoint.currentConnections < endpoint.maxConnections);
      });
    }
  }, {
    key: "selectOptimalEndpoint",
    value: function () {
      var _selectOptimalEndpoint = _asyncToGenerator(function* (request, availableEndpoints) {
        cov_1iz0px6adx().f[18]++;
        var selectedEndpoint;
        var reason;
        var confidence;
        cov_1iz0px6adx().s[68]++;
        switch (this.loadBalancingStrategy.algorithm) {
          case 'ai_optimized':
            cov_1iz0px6adx().b[6][0]++;
            cov_1iz0px6adx().s[69]++;
            var _yield$this$aiOptimiz = yield this.aiOptimizedSelection(request, availableEndpoints);
            selectedEndpoint = _yield$this$aiOptimiz.endpoint;
            reason = _yield$this$aiOptimiz.reason;
            confidence = _yield$this$aiOptimiz.confidence;
            cov_1iz0px6adx().s[70]++;
            break;
          case 'least_connections':
            cov_1iz0px6adx().b[6][1]++;
            cov_1iz0px6adx().s[71]++;
            selectedEndpoint = this.leastConnectionsSelection(availableEndpoints);
            cov_1iz0px6adx().s[72]++;
            reason = 'Least connections algorithm';
            cov_1iz0px6adx().s[73]++;
            confidence = 0.8;
            cov_1iz0px6adx().s[74]++;
            break;
          case 'weighted_round_robin':
            cov_1iz0px6adx().b[6][2]++;
            cov_1iz0px6adx().s[75]++;
            selectedEndpoint = this.weightedRoundRobinSelection(availableEndpoints);
            cov_1iz0px6adx().s[76]++;
            reason = 'Weighted round robin algorithm';
            cov_1iz0px6adx().s[77]++;
            confidence = 0.7;
            cov_1iz0px6adx().s[78]++;
            break;
          case 'ip_hash':
            cov_1iz0px6adx().b[6][3]++;
            cov_1iz0px6adx().s[79]++;
            selectedEndpoint = this.ipHashSelection(request, availableEndpoints);
            cov_1iz0px6adx().s[80]++;
            reason = 'IP hash algorithm';
            cov_1iz0px6adx().s[81]++;
            confidence = 0.9;
            cov_1iz0px6adx().s[82]++;
            break;
          default:
            cov_1iz0px6adx().b[6][4]++;
            cov_1iz0px6adx().s[83]++;
            selectedEndpoint = this.roundRobinSelection(availableEndpoints);
            cov_1iz0px6adx().s[84]++;
            reason = 'Round robin algorithm';
            cov_1iz0px6adx().s[85]++;
            confidence = 0.6;
        }
        var fallbackEndpoints = (cov_1iz0px6adx().s[86]++, availableEndpoints.filter(function (ep) {
          cov_1iz0px6adx().f[19]++;
          cov_1iz0px6adx().s[87]++;
          return ep.id !== selectedEndpoint.id;
        }).sort(function (a, b) {
          cov_1iz0px6adx().f[20]++;
          cov_1iz0px6adx().s[88]++;
          return a.health.responseTime - b.health.responseTime;
        }).slice(0, 3));
        var estimatedResponseTime = (cov_1iz0px6adx().s[89]++, this.estimateResponseTime(selectedEndpoint, request));
        cov_1iz0px6adx().s[90]++;
        return {
          endpoint: selectedEndpoint,
          reason: reason,
          confidence: confidence,
          fallbackEndpoints: fallbackEndpoints,
          estimatedResponseTime: estimatedResponseTime,
          cacheStrategy: this.determineCacheStrategy(request, selectedEndpoint)
        };
      });
      function selectOptimalEndpoint(_x2, _x3) {
        return _selectOptimalEndpoint.apply(this, arguments);
      }
      return selectOptimalEndpoint;
    }()
  }, {
    key: "aiOptimizedSelection",
    value: function () {
      var _aiOptimizedSelection = _asyncToGenerator(function* (request, endpoints) {
        var _this2 = this;
        cov_1iz0px6adx().f[21]++;
        var scoredEndpoints = (cov_1iz0px6adx().s[91]++, endpoints.map(function (endpoint) {
          cov_1iz0px6adx().f[22]++;
          var score = (cov_1iz0px6adx().s[92]++, 0);
          var responseTimeScore = (cov_1iz0px6adx().s[93]++, Math.max(0, 100 - endpoint.health.responseTime / 10));
          cov_1iz0px6adx().s[94]++;
          score += responseTimeScore * 0.4;
          var capacityScore = (cov_1iz0px6adx().s[95]++, Math.max(0, 100 - (endpoint.capacity.cpu + endpoint.capacity.memory) / 2));
          cov_1iz0px6adx().s[96]++;
          score += capacityScore * 0.25;
          var geoScore = (cov_1iz0px6adx().s[97]++, _this2.calculateGeographicScore(request, endpoint));
          cov_1iz0px6adx().s[98]++;
          score += geoScore * 0.2;
          var loadScore = (cov_1iz0px6adx().s[99]++, Math.max(0, 100 - endpoint.currentConnections / endpoint.maxConnections * 100));
          cov_1iz0px6adx().s[100]++;
          score += loadScore * 0.15;
          cov_1iz0px6adx().s[101]++;
          if ((cov_1iz0px6adx().b[8][0]++, request.sessionId) && (cov_1iz0px6adx().b[8][1]++, _this2.sessionAffinity.get(request.sessionId) === endpoint.id)) {
            cov_1iz0px6adx().b[7][0]++;
            cov_1iz0px6adx().s[102]++;
            score += 10;
          } else {
            cov_1iz0px6adx().b[7][1]++;
          }
          cov_1iz0px6adx().s[103]++;
          return {
            endpoint: endpoint,
            score: score
          };
        }));
        cov_1iz0px6adx().s[104]++;
        scoredEndpoints.sort(function (a, b) {
          cov_1iz0px6adx().f[23]++;
          cov_1iz0px6adx().s[105]++;
          return b.score - a.score;
        });
        var selected = (cov_1iz0px6adx().s[106]++, scoredEndpoints[0]);
        cov_1iz0px6adx().s[107]++;
        if ((cov_1iz0px6adx().b[10][0]++, request.sessionId) && (cov_1iz0px6adx().b[10][1]++, this.loadBalancingStrategy.stickySession)) {
          cov_1iz0px6adx().b[9][0]++;
          cov_1iz0px6adx().s[108]++;
          this.sessionAffinity.set(request.sessionId, selected.endpoint.id);
        } else {
          cov_1iz0px6adx().b[9][1]++;
        }
        cov_1iz0px6adx().s[109]++;
        return {
          endpoint: selected.endpoint,
          reason: `AI-optimized selection (score: ${selected.score.toFixed(1)})`,
          confidence: Math.min(selected.score / 100, 1)
        };
      });
      function aiOptimizedSelection(_x4, _x5) {
        return _aiOptimizedSelection.apply(this, arguments);
      }
      return aiOptimizedSelection;
    }()
  }, {
    key: "leastConnectionsSelection",
    value: function leastConnectionsSelection(endpoints) {
      cov_1iz0px6adx().f[24]++;
      cov_1iz0px6adx().s[110]++;
      return endpoints.reduce(function (min, current) {
        cov_1iz0px6adx().f[25]++;
        cov_1iz0px6adx().s[111]++;
        return current.currentConnections < min.currentConnections ? (cov_1iz0px6adx().b[11][0]++, current) : (cov_1iz0px6adx().b[11][1]++, min);
      });
    }
  }, {
    key: "weightedRoundRobinSelection",
    value: function weightedRoundRobinSelection(endpoints) {
      cov_1iz0px6adx().f[26]++;
      var totalWeight = (cov_1iz0px6adx().s[112]++, endpoints.reduce(function (sum, ep) {
        cov_1iz0px6adx().f[27]++;
        cov_1iz0px6adx().s[113]++;
        return sum + ep.weight;
      }, 0));
      var random = (cov_1iz0px6adx().s[114]++, Math.random() * totalWeight);
      var currentWeight = (cov_1iz0px6adx().s[115]++, 0);
      cov_1iz0px6adx().s[116]++;
      for (var endpoint of endpoints) {
        cov_1iz0px6adx().s[117]++;
        currentWeight += endpoint.weight;
        cov_1iz0px6adx().s[118]++;
        if (random <= currentWeight) {
          cov_1iz0px6adx().b[12][0]++;
          cov_1iz0px6adx().s[119]++;
          return endpoint;
        } else {
          cov_1iz0px6adx().b[12][1]++;
        }
      }
      cov_1iz0px6adx().s[120]++;
      return endpoints[0];
    }
  }, {
    key: "roundRobinSelection",
    value: function roundRobinSelection(endpoints) {
      cov_1iz0px6adx().f[28]++;
      var index = (cov_1iz0px6adx().s[121]++, Date.now() % endpoints.length);
      cov_1iz0px6adx().s[122]++;
      return endpoints[index];
    }
  }, {
    key: "ipHashSelection",
    value: function ipHashSelection(request, endpoints) {
      cov_1iz0px6adx().f[29]++;
      var hash = (cov_1iz0px6adx().s[123]++, this.simpleHash((cov_1iz0px6adx().b[13][0]++, request.headers['x-forwarded-for']) || (cov_1iz0px6adx().b[13][1]++, 'unknown')));
      var index = (cov_1iz0px6adx().s[124]++, hash % endpoints.length);
      cov_1iz0px6adx().s[125]++;
      return endpoints[index];
    }
  }, {
    key: "calculateGeographicScore",
    value: function calculateGeographicScore(request, endpoint) {
      cov_1iz0px6adx().f[30]++;
      cov_1iz0px6adx().s[126]++;
      if ((cov_1iz0px6adx().b[15][0]++, !request.userLocation) || (cov_1iz0px6adx().b[15][1]++, !this.loadBalancingStrategy.geoRouting)) {
        cov_1iz0px6adx().b[14][0]++;
        cov_1iz0px6adx().s[127]++;
        return 50;
      } else {
        cov_1iz0px6adx().b[14][1]++;
      }
      var regionMap = (cov_1iz0px6adx().s[128]++, {
        'us-east-1': ['US', 'CA'],
        'eu-west-1': ['GB', 'FR', 'DE', 'ES', 'IT'],
        'ap-southeast-1': ['SG', 'MY', 'TH', 'ID']
      });
      var endpointCountries = (cov_1iz0px6adx().s[129]++, (cov_1iz0px6adx().b[16][0]++, regionMap[endpoint.region]) || (cov_1iz0px6adx().b[16][1]++, []));
      cov_1iz0px6adx().s[130]++;
      if (endpointCountries.includes(request.userLocation.country)) {
        cov_1iz0px6adx().b[17][0]++;
        cov_1iz0px6adx().s[131]++;
        return 100;
      } else {
        cov_1iz0px6adx().b[17][1]++;
      }
      cov_1iz0px6adx().s[132]++;
      return 30;
    }
  }, {
    key: "estimateResponseTime",
    value: function estimateResponseTime(endpoint, request) {
      cov_1iz0px6adx().f[31]++;
      var estimatedTime = (cov_1iz0px6adx().s[133]++, endpoint.health.responseTime);
      var loadFactor = (cov_1iz0px6adx().s[134]++, endpoint.currentConnections / endpoint.maxConnections);
      cov_1iz0px6adx().s[135]++;
      estimatedTime += loadFactor * 50;
      var typeFactors = (cov_1iz0px6adx().s[136]++, {
        'api': 1.0,
        'cdn': 0.3,
        'edge_function': 0.5
      });
      cov_1iz0px6adx().s[137]++;
      estimatedTime *= (cov_1iz0px6adx().b[18][0]++, typeFactors[request.type]) || (cov_1iz0px6adx().b[18][1]++, 1.0);
      cov_1iz0px6adx().s[138]++;
      return Math.round(estimatedTime);
    }
  }, {
    key: "determineCacheStrategy",
    value: function determineCacheStrategy(request, endpoint) {
      cov_1iz0px6adx().f[32]++;
      cov_1iz0px6adx().s[139]++;
      if (request.type === 'cdn') {
        cov_1iz0px6adx().b[19][0]++;
        cov_1iz0px6adx().s[140]++;
        return 'cache';
      } else {
        cov_1iz0px6adx().b[19][1]++;
      }
      cov_1iz0px6adx().s[141]++;
      if ((cov_1iz0px6adx().b[21][0]++, request.method === 'GET') && (cov_1iz0px6adx().b[21][1]++, request.type === 'api')) {
        cov_1iz0px6adx().b[20][0]++;
        cov_1iz0px6adx().s[142]++;
        return 'cache_and_forward';
      } else {
        cov_1iz0px6adx().b[20][1]++;
      }
      cov_1iz0px6adx().s[143]++;
      return 'bypass';
    }
  }, {
    key: "updateEndpointMetrics",
    value: function updateEndpointMetrics(endpointId, request) {
      cov_1iz0px6adx().f[33]++;
      var endpoint = (cov_1iz0px6adx().s[144]++, this.serviceEndpoints.get(endpointId));
      cov_1iz0px6adx().s[145]++;
      if (!endpoint) {
        cov_1iz0px6adx().b[22][0]++;
        cov_1iz0px6adx().s[146]++;
        return;
      } else {
        cov_1iz0px6adx().b[22][1]++;
      }
      cov_1iz0px6adx().s[147]++;
      endpoint.currentConnections++;
      cov_1iz0px6adx().s[148]++;
      endpoint.metrics.requestsPerSecond += 0.1;
    }
  }, {
    key: "trackRoutingDecision",
    value: function trackRoutingDecision(request, decision) {
      cov_1iz0px6adx().f[34]++;
      cov_1iz0px6adx().s[149]++;
      this.trafficHistory.push({
        timestamp: Date.now(),
        request: request,
        response: {
          endpointId: decision.endpoint.id,
          responseTime: decision.estimatedResponseTime
        }
      });
      cov_1iz0px6adx().s[150]++;
      if (this.trafficHistory.length > 10000) {
        cov_1iz0px6adx().b[23][0]++;
        cov_1iz0px6adx().s[151]++;
        this.trafficHistory.splice(0, 1000);
      } else {
        cov_1iz0px6adx().b[23][1]++;
      }
    }
  }, {
    key: "getFallbackRoutingDecision",
    value: function getFallbackRoutingDecision(request) {
      cov_1iz0px6adx().f[35]++;
      var fallbackEndpoint = (cov_1iz0px6adx().s[152]++, {
        id: 'fallback',
        url: 'https://fallback.acemind.app',
        region: 'global',
        type: request.type,
        weight: 1,
        maxConnections: 100,
        currentConnections: 0,
        health: {
          status: 'healthy',
          lastCheck: Date.now(),
          responseTime: 500,
          errorRate: 0,
          uptime: 99
        },
        capacity: {
          cpu: 50,
          memory: 50,
          bandwidth: 100,
          storage: 50
        },
        metrics: {
          requestsPerSecond: 1,
          averageResponseTime: 500,
          throughput: 10,
          successRate: 99
        }
      });
      cov_1iz0px6adx().s[153]++;
      return {
        endpoint: fallbackEndpoint,
        reason: 'Fallback endpoint due to routing failure',
        confidence: 0.1,
        fallbackEndpoints: [],
        estimatedResponseTime: 500,
        cacheStrategy: 'bypass'
      };
    }
  }, {
    key: "isCircuitBreakerOpen",
    value: function isCircuitBreakerOpen(endpointId) {
      cov_1iz0px6adx().f[36]++;
      cov_1iz0px6adx().s[154]++;
      if (!this.loadBalancingStrategy.circuitBreakerEnabled) {
        cov_1iz0px6adx().b[24][0]++;
        cov_1iz0px6adx().s[155]++;
        return false;
      } else {
        cov_1iz0px6adx().b[24][1]++;
      }
      var breaker = (cov_1iz0px6adx().s[156]++, this.circuitBreakers.get(endpointId));
      cov_1iz0px6adx().s[157]++;
      if (!breaker) {
        cov_1iz0px6adx().b[25][0]++;
        cov_1iz0px6adx().s[158]++;
        return false;
      } else {
        cov_1iz0px6adx().b[25][1]++;
      }
      cov_1iz0px6adx().s[159]++;
      if (breaker.isOpen) {
        cov_1iz0px6adx().b[26][0]++;
        cov_1iz0px6adx().s[160]++;
        if (Date.now() - breaker.lastFailure > this.CIRCUIT_BREAKER_TIMEOUT) {
          cov_1iz0px6adx().b[27][0]++;
          cov_1iz0px6adx().s[161]++;
          breaker.isOpen = false;
          cov_1iz0px6adx().s[162]++;
          breaker.failures = 0;
          cov_1iz0px6adx().s[163]++;
          return false;
        } else {
          cov_1iz0px6adx().b[27][1]++;
        }
        cov_1iz0px6adx().s[164]++;
        return true;
      } else {
        cov_1iz0px6adx().b[26][1]++;
      }
      cov_1iz0px6adx().s[165]++;
      return false;
    }
  }, {
    key: "startHealthMonitoring",
    value: function startHealthMonitoring() {
      var _this3 = this;
      cov_1iz0px6adx().f[37]++;
      cov_1iz0px6adx().s[166]++;
      setInterval(function () {
        cov_1iz0px6adx().f[38]++;
        cov_1iz0px6adx().s[167]++;
        _this3.performHealthChecks();
      }, this.loadBalancingStrategy.healthCheckInterval);
    }
  }, {
    key: "performHealthChecks",
    value: function () {
      var _performHealthChecks = _asyncToGenerator(function* () {
        cov_1iz0px6adx().f[39]++;
        var endpoints = (cov_1iz0px6adx().s[168]++, Array.from(this.serviceEndpoints.values()));
        cov_1iz0px6adx().s[169]++;
        for (var endpoint of endpoints) {
          cov_1iz0px6adx().s[170]++;
          try {
            var healthResult = (cov_1iz0px6adx().s[171]++, yield this.checkEndpointHealth(endpoint));
            cov_1iz0px6adx().s[172]++;
            this.updateEndpointHealth(endpoint, healthResult);
          } catch (error) {
            cov_1iz0px6adx().s[173]++;
            console.error(`Health check failed for ${endpoint.id}:`, error);
            cov_1iz0px6adx().s[174]++;
            this.markEndpointUnhealthy(endpoint);
          }
        }
      });
      function performHealthChecks() {
        return _performHealthChecks.apply(this, arguments);
      }
      return performHealthChecks;
    }()
  }, {
    key: "checkEndpointHealth",
    value: function () {
      var _checkEndpointHealth = _asyncToGenerator(function* (endpoint) {
        cov_1iz0px6adx().f[40]++;
        var responseTime = (cov_1iz0px6adx().s[175]++, Math.random() * 200 + 50);
        var success = (cov_1iz0px6adx().s[176]++, Math.random() > 0.05);
        cov_1iz0px6adx().s[177]++;
        return {
          responseTime: responseTime,
          status: success ? (cov_1iz0px6adx().b[28][0]++, 200) : (cov_1iz0px6adx().b[28][1]++, 500),
          success: success
        };
      });
      function checkEndpointHealth(_x6) {
        return _checkEndpointHealth.apply(this, arguments);
      }
      return checkEndpointHealth;
    }()
  }, {
    key: "updateEndpointHealth",
    value: function updateEndpointHealth(endpoint, healthResult) {
      cov_1iz0px6adx().f[41]++;
      cov_1iz0px6adx().s[178]++;
      endpoint.health.lastCheck = Date.now();
      cov_1iz0px6adx().s[179]++;
      endpoint.health.responseTime = healthResult.responseTime;
      cov_1iz0px6adx().s[180]++;
      if (healthResult.success) {
        cov_1iz0px6adx().b[29][0]++;
        cov_1iz0px6adx().s[181]++;
        endpoint.health.status = 'healthy';
        var breaker = (cov_1iz0px6adx().s[182]++, this.circuitBreakers.get(endpoint.id));
        cov_1iz0px6adx().s[183]++;
        if (breaker) {
          cov_1iz0px6adx().b[30][0]++;
          cov_1iz0px6adx().s[184]++;
          breaker.failures = 0;
        } else {
          cov_1iz0px6adx().b[30][1]++;
        }
      } else {
        cov_1iz0px6adx().b[29][1]++;
        cov_1iz0px6adx().s[185]++;
        this.markEndpointUnhealthy(endpoint);
      }
    }
  }, {
    key: "markEndpointUnhealthy",
    value: function markEndpointUnhealthy(endpoint) {
      cov_1iz0px6adx().f[42]++;
      cov_1iz0px6adx().s[186]++;
      endpoint.health.status = 'unhealthy';
      cov_1iz0px6adx().s[187]++;
      if (!this.circuitBreakers.has(endpoint.id)) {
        cov_1iz0px6adx().b[31][0]++;
        cov_1iz0px6adx().s[188]++;
        this.circuitBreakers.set(endpoint.id, {
          isOpen: false,
          failures: 0,
          lastFailure: 0
        });
      } else {
        cov_1iz0px6adx().b[31][1]++;
      }
      var breaker = (cov_1iz0px6adx().s[189]++, this.circuitBreakers.get(endpoint.id));
      cov_1iz0px6adx().s[190]++;
      breaker.failures++;
      cov_1iz0px6adx().s[191]++;
      breaker.lastFailure = Date.now();
      cov_1iz0px6adx().s[192]++;
      if (breaker.failures >= this.CIRCUIT_BREAKER_THRESHOLD) {
        cov_1iz0px6adx().b[32][0]++;
        cov_1iz0px6adx().s[193]++;
        breaker.isOpen = true;
        cov_1iz0px6adx().s[194]++;
        console.warn(`Circuit breaker opened for endpoint: ${endpoint.id}`);
      } else {
        cov_1iz0px6adx().b[32][1]++;
      }
    }
  }, {
    key: "startTrafficAnalysis",
    value: function startTrafficAnalysis() {
      var _this4 = this;
      cov_1iz0px6adx().f[43]++;
      cov_1iz0px6adx().s[195]++;
      setInterval(function () {
        cov_1iz0px6adx().f[44]++;
        cov_1iz0px6adx().s[196]++;
        _this4.analyzeTrafficPatterns();
      }, 300000);
    }
  }, {
    key: "analyzeTrafficPatterns",
    value: function analyzeTrafficPatterns() {
      cov_1iz0px6adx().f[45]++;
      var recentTraffic = (cov_1iz0px6adx().s[197]++, this.trafficHistory.filter(function (entry) {
        cov_1iz0px6adx().f[46]++;
        cov_1iz0px6adx().s[198]++;
        return Date.now() - entry.timestamp < 3600000;
      }));
      cov_1iz0px6adx().s[199]++;
      if (recentTraffic.length > 0) {
        cov_1iz0px6adx().b[33][0]++;
        cov_1iz0px6adx().s[200]++;
        console.log(`Analyzed ${recentTraffic.length} traffic entries for optimization`);
      } else {
        cov_1iz0px6adx().b[33][1]++;
      }
    }
  }, {
    key: "simpleHash",
    value: function simpleHash(str) {
      cov_1iz0px6adx().f[47]++;
      var hash = (cov_1iz0px6adx().s[201]++, 0);
      cov_1iz0px6adx().s[202]++;
      for (var i = (cov_1iz0px6adx().s[203]++, 0); i < str.length; i++) {
        var char = (cov_1iz0px6adx().s[204]++, str.charCodeAt(i));
        cov_1iz0px6adx().s[205]++;
        hash = (hash << 5) - hash + char;
        cov_1iz0px6adx().s[206]++;
        hash = hash & hash;
      }
      cov_1iz0px6adx().s[207]++;
      return Math.abs(hash);
    }
  }]);
}();
var TrafficPredictionModel = function () {
  function TrafficPredictionModel() {
    _classCallCheck(this, TrafficPredictionModel);
  }
  return _createClass(TrafficPredictionModel, [{
    key: "initialize",
    value: function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_1iz0px6adx().f[48]++;
        cov_1iz0px6adx().s[208]++;
        console.log('Initialized traffic prediction model');
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }()
  }, {
    key: "predict",
    value: function () {
      var _predict = _asyncToGenerator(function* (trafficHistory, timeHorizon) {
        cov_1iz0px6adx().f[49]++;
        var recentTraffic = (cov_1iz0px6adx().s[209]++, trafficHistory.filter(function (entry) {
          cov_1iz0px6adx().f[50]++;
          cov_1iz0px6adx().s[210]++;
          return Date.now() - entry.timestamp < 3600000;
        }));
        var expectedRequests = (cov_1iz0px6adx().s[211]++, recentTraffic.length * (timeHorizon / 60));
        cov_1iz0px6adx().s[212]++;
        return {
          timeHorizon: timeHorizon,
          expectedRequests: expectedRequests,
          peakTimes: [{
            start: Date.now() + 1800000,
            end: Date.now() + 3600000,
            intensity: 1.5
          }],
          regionalDistribution: {
            'us-east-1': 0.6,
            'eu-west-1': 0.3,
            'ap-southeast-1': 0.1
          },
          typeDistribution: {
            'api': 0.7,
            'cdn': 0.2,
            'edge_function': 0.1
          },
          confidence: 0.75
        };
      });
      function predict(_x7, _x8) {
        return _predict.apply(this, arguments);
      }
      return predict;
    }()
  }]);
}();
export var smartLoadBalancer = (cov_1iz0px6adx().s[213]++, new SmartLoadBalancer());
export default smartLoadBalancer;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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