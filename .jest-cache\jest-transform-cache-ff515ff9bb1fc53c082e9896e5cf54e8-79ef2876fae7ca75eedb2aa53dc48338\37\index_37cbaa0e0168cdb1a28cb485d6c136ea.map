{"version": 3, "names": ["exports", "__esModule", "default", "Platform", "OS", "select", "obj", "web", "isTesting", "process", "env", "NODE_ENV", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar Platform = {\n  OS: 'web',\n  select: obj => 'web' in obj ? obj.web : obj.default,\n  get isTesting() {\n    if (process.env.NODE_ENV === 'test') {\n      return true;\n    }\n    return false;\n  }\n};\nvar _default = exports.default = Platform;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAWxB,IAAIC,QAAQ,GAAG;EACbC,EAAE,EAAE,KAAK;EACTC,MAAM,EAAE,SAARA,MAAMA,CAAEC,GAAG;IAAA,OAAI,KAAK,IAAIA,GAAG,GAAGA,GAAG,CAACC,GAAG,GAAGD,GAAG,CAACJ,OAAO;EAAA;EACnD,IAAIM,SAASA,CAAA,EAAG;IACd,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM,EAAE;MACnC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;AACF,CAAC;AACD,IAAIC,QAAQ,GAAGZ,OAAO,CAACE,OAAO,GAAGC,QAAQ;AACzCU,MAAM,CAACb,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}