40e5fb7a8209ab3352f32a7d69e9c144
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_e4hcxgn74() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\optimized\\databaseService.ts";
  var hash = "0a54ef669fbcdc9eae5fff587c8849ee844d088f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\optimized\\databaseService.ts",
    statementMap: {
      "0": {
        start: {
          line: 32,
          column: 23
        },
        end: {
          line: 32,
          column: 87
        }
      },
      "1": {
        start: {
          line: 33,
          column: 37
        },
        end: {
          line: 33,
          column: 39
        }
      },
      "2": {
        start: {
          line: 34,
          column: 48
        },
        end: {
          line: 34,
          column: 52
        }
      },
      "3": {
        start: {
          line: 35,
          column: 33
        },
        end: {
          line: 35,
          column: 35
        }
      },
      "4": {
        start: {
          line: 36,
          column: 36
        },
        end: {
          line: 36,
          column: 38
        }
      },
      "5": {
        start: {
          line: 46,
          column: 8
        },
        end: {
          line: 46,
          column: 15
        }
      },
      "6": {
        start: {
          line: 48,
          column: 21
        },
        end: {
          line: 48,
          column: 42
        }
      },
      "7": {
        start: {
          line: 51,
          column: 4
        },
        end: {
          line: 53,
          column: 5
        }
      },
      "8": {
        start: {
          line: 52,
          column: 6
        },
        end: {
          line: 52,
          column: 41
        }
      },
      "9": {
        start: {
          line: 55,
          column: 22
        },
        end: {
          line: 55,
          column: 32
        }
      },
      "10": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 186,
          column: 5
        }
      },
      "11": {
        start: {
          line: 59,
          column: 30
        },
        end: {
          line: 140,
          column: 8
        }
      },
      "12": {
        start: {
          line: 138,
          column: 10
        },
        end: {
          line: 138,
          column: 71
        }
      },
      "13": {
        start: {
          line: 138,
          column: 27
        },
        end: {
          line: 138,
          column: 61
        }
      },
      "14": {
        start: {
          line: 142,
          column: 6
        },
        end: {
          line: 144,
          column: 7
        }
      },
      "15": {
        start: {
          line: 143,
          column: 8
        },
        end: {
          line: 143,
          column: 67
        }
      },
      "16": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 46
        }
      },
      "17": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 149,
          column: 79
        }
      },
      "18": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 153,
          column: 7
        }
      },
      "19": {
        start: {
          line: 152,
          column: 8
        },
        end: {
          line: 152,
          column: 86
        }
      },
      "20": {
        start: {
          line: 156,
          column: 21
        },
        end: {
          line: 174,
          column: 7
        }
      },
      "21": {
        start: {
          line: 171,
          column: 55
        },
        end: {
          line: 171,
          column: 62
        }
      },
      "22": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 179,
          column: 7
        }
      },
      "23": {
        start: {
          line: 178,
          column: 8
        },
        end: {
          line: 178,
          column: 54
        }
      },
      "24": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 20
        }
      },
      "25": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 184,
          column: 84
        }
      },
      "26": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 185,
          column: 18
        }
      },
      "27": {
        start: {
          line: 200,
          column: 8
        },
        end: {
          line: 200,
          column: 15
        }
      },
      "28": {
        start: {
          line: 202,
          column: 21
        },
        end: {
          line: 202,
          column: 57
        }
      },
      "29": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 206,
          column: 5
        }
      },
      "30": {
        start: {
          line: 205,
          column: 6
        },
        end: {
          line: 205,
          column: 41
        }
      },
      "31": {
        start: {
          line: 208,
          column: 22
        },
        end: {
          line: 208,
          column: 32
        }
      },
      "32": {
        start: {
          line: 209,
          column: 23
        },
        end: {
          line: 209,
          column: 57
        }
      },
      "33": {
        start: {
          line: 211,
          column: 4
        },
        end: {
          line: 260,
          column: 5
        }
      },
      "34": {
        start: {
          line: 213,
          column: 64
        },
        end: {
          line: 237,
          column: 8
        }
      },
      "35": {
        start: {
          line: 239,
          column: 24
        },
        end: {
          line: 239,
          column: 46
        }
      },
      "36": {
        start: {
          line: 240,
          column: 6
        },
        end: {
          line: 240,
          column: 77
        }
      },
      "37": {
        start: {
          line: 242,
          column: 21
        },
        end: {
          line: 249,
          column: 7
        }
      },
      "38": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 253,
          column: 7
        }
      },
      "39": {
        start: {
          line: 252,
          column: 8
        },
        end: {
          line: 252,
          column: 54
        }
      },
      "40": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 20
        }
      },
      "41": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 258,
          column: 82
        }
      },
      "42": {
        start: {
          line: 259,
          column: 6
        },
        end: {
          line: 259,
          column: 18
        }
      },
      "43": {
        start: {
          line: 267,
          column: 38
        },
        end: {
          line: 267,
          column: 45
        }
      },
      "44": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 273,
          column: 5
        }
      },
      "45": {
        start: {
          line: 270,
          column: 30
        },
        end: {
          line: 270,
          column: 81
        }
      },
      "46": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 271,
          column: 29
        }
      },
      "47": {
        start: {
          line: 271,
          column: 17
        },
        end: {
          line: 271,
          column: 29
        }
      },
      "48": {
        start: {
          line: 272,
          column: 6
        },
        end: {
          line: 272,
          column: 18
        }
      },
      "49": {
        start: {
          line: 275,
          column: 22
        },
        end: {
          line: 275,
          column: 32
        }
      },
      "50": {
        start: {
          line: 276,
          column: 22
        },
        end: {
          line: 276,
          column: 25
        }
      },
      "51": {
        start: {
          line: 277,
          column: 25
        },
        end: {
          line: 277,
          column: 27
        }
      },
      "52": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 297,
          column: 5
        }
      },
      "53": {
        start: {
          line: 281,
          column: 6
        },
        end: {
          line: 287,
          column: 7
        }
      },
      "54": {
        start: {
          line: 281,
          column: 19
        },
        end: {
          line: 281,
          column: 20
        }
      },
      "55": {
        start: {
          line: 282,
          column: 22
        },
        end: {
          line: 282,
          column: 53
        }
      },
      "56": {
        start: {
          line: 283,
          column: 32
        },
        end: {
          line: 283,
          column: 81
        }
      },
      "57": {
        start: {
          line: 285,
          column: 8
        },
        end: {
          line: 285,
          column: 31
        }
      },
      "58": {
        start: {
          line: 285,
          column: 19
        },
        end: {
          line: 285,
          column: 31
        }
      },
      "59": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 286,
          column: 40
        }
      },
      "60": {
        start: {
          line: 286,
          column: 18
        },
        end: {
          line: 286,
          column: 40
        }
      },
      "61": {
        start: {
          line: 289,
          column: 24
        },
        end: {
          line: 289,
          column: 46
        }
      },
      "62": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 70
        }
      },
      "63": {
        start: {
          line: 292,
          column: 6
        },
        end: {
          line: 292,
          column: 21
        }
      },
      "64": {
        start: {
          line: 295,
          column: 6
        },
        end: {
          line: 295,
          column: 75
        }
      },
      "65": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 296,
          column: 18
        }
      },
      "66": {
        start: {
          line: 308,
          column: 55
        },
        end: {
          line: 308,
          column: 62
        }
      },
      "67": {
        start: {
          line: 309,
          column: 21
        },
        end: {
          line: 309,
          column: 58
        }
      },
      "68": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 313,
          column: 5
        }
      },
      "69": {
        start: {
          line: 312,
          column: 6
        },
        end: {
          line: 312,
          column: 41
        }
      },
      "70": {
        start: {
          line: 315,
          column: 22
        },
        end: {
          line: 315,
          column: 32
        }
      },
      "71": {
        start: {
          line: 317,
          column: 4
        },
        end: {
          line: 350,
          column: 5
        }
      },
      "72": {
        start: {
          line: 319,
          column: 29
        },
        end: {
          line: 325,
          column: 7
        }
      },
      "73": {
        start: {
          line: 320,
          column: 8
        },
        end: {
          line: 324,
          column: 20
        }
      },
      "74": {
        start: {
          line: 327,
          column: 22
        },
        end: {
          line: 327,
          column: 55
        }
      },
      "75": {
        start: {
          line: 328,
          column: 24
        },
        end: {
          line: 328,
          column: 46
        }
      },
      "76": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 72
        }
      },
      "77": {
        start: {
          line: 332,
          column: 28
        },
        end: {
          line: 339,
          column: 7
        }
      },
      "78": {
        start: {
          line: 335,
          column: 10
        },
        end: {
          line: 335,
          column: 49
        }
      },
      "79": {
        start: {
          line: 336,
          column: 10
        },
        end: {
          line: 336,
          column: 21
        }
      },
      "80": {
        start: {
          line: 341,
          column: 6
        },
        end: {
          line: 343,
          column: 7
        }
      },
      "81": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 342,
          column: 61
        }
      },
      "82": {
        start: {
          line: 345,
          column: 6
        },
        end: {
          line: 345,
          column: 27
        }
      },
      "83": {
        start: {
          line: 348,
          column: 6
        },
        end: {
          line: 348,
          column: 77
        }
      },
      "84": {
        start: {
          line: 349,
          column: 6
        },
        end: {
          line: 349,
          column: 18
        }
      },
      "85": {
        start: {
          line: 357,
          column: 19
        },
        end: {
          line: 357,
          column: 43
        }
      },
      "86": {
        start: {
          line: 358,
          column: 4
        },
        end: {
          line: 358,
          column: 30
        }
      },
      "87": {
        start: {
          line: 358,
          column: 17
        },
        end: {
          line: 358,
          column: 30
        }
      },
      "88": {
        start: {
          line: 359,
          column: 4
        },
        end: {
          line: 359,
          column: 47
        }
      },
      "89": {
        start: {
          line: 363,
          column: 19
        },
        end: {
          line: 363,
          column: 43
        }
      },
      "90": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 364,
          column: 32
        }
      },
      "91": {
        start: {
          line: 368,
          column: 4
        },
        end: {
          line: 372,
          column: 7
        }
      },
      "92": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 377,
          column: 5
        }
      },
      "93": {
        start: {
          line: 376,
          column: 6
        },
        end: {
          line: 376,
          column: 26
        }
      },
      "94": {
        start: {
          line: 381,
          column: 16
        },
        end: {
          line: 381,
          column: 26
        }
      },
      "95": {
        start: {
          line: 382,
          column: 4
        },
        end: {
          line: 386,
          column: 5
        }
      },
      "96": {
        start: {
          line: 383,
          column: 6
        },
        end: {
          line: 385,
          column: 7
        }
      },
      "97": {
        start: {
          line: 384,
          column: 8
        },
        end: {
          line: 384,
          column: 36
        }
      },
      "98": {
        start: {
          line: 390,
          column: 16
        },
        end: {
          line: 390,
          column: 26
        }
      },
      "99": {
        start: {
          line: 391,
          column: 4
        },
        end: {
          line: 400,
          column: 5
        }
      },
      "100": {
        start: {
          line: 393,
          column: 8
        },
        end: {
          line: 393,
          column: 65
        }
      },
      "101": {
        start: {
          line: 395,
          column: 8
        },
        end: {
          line: 395,
          column: 66
        }
      },
      "102": {
        start: {
          line: 397,
          column: 8
        },
        end: {
          line: 397,
          column: 67
        }
      },
      "103": {
        start: {
          line: 399,
          column: 8
        },
        end: {
          line: 399,
          column: 66
        }
      },
      "104": {
        start: {
          line: 407,
          column: 4
        },
        end: {
          line: 411,
          column: 6
        }
      },
      "105": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 418,
          column: 28
        }
      },
      "106": {
        start: {
          line: 423,
          column: 40
        },
        end: {
          line: 423,
          column: 70
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 41,
            column: 2
          },
          end: {
            line: 41,
            column: 3
          }
        },
        loc: {
          start: {
            line: 41,
            column: 87
          },
          end: {
            line: 187,
            column: 3
          }
        },
        line: 41
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 137,
            column: 20
          },
          end: {
            line: 137,
            column: 21
          }
        },
        loc: {
          start: {
            line: 138,
            column: 10
          },
          end: {
            line: 138,
            column: 71
          }
        },
        line: 138
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 138,
            column: 21
          },
          end: {
            line: 138,
            column: 22
          }
        },
        loc: {
          start: {
            line: 138,
            column: 27
          },
          end: {
            line: 138,
            column: 61
          }
        },
        line: 138
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 171,
            column: 50
          },
          end: {
            line: 171,
            column: 51
          }
        },
        loc: {
          start: {
            line: 171,
            column: 55
          },
          end: {
            line: 171,
            column: 62
          }
        },
        line: 171
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 192,
            column: 2
          },
          end: {
            line: 192,
            column: 3
          }
        },
        loc: {
          start: {
            line: 196,
            column: 18
          },
          end: {
            line: 261,
            column: 3
          }
        },
        line: 196
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 266,
            column: 2
          },
          end: {
            line: 266,
            column: 3
          }
        },
        loc: {
          start: {
            line: 266,
            column: 94
          },
          end: {
            line: 298,
            column: 3
          }
        },
        line: 266
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 303,
            column: 2
          },
          end: {
            line: 303,
            column: 3
          }
        },
        loc: {
          start: {
            line: 307,
            column: 18
          },
          end: {
            line: 351,
            column: 3
          }
        },
        line: 307
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 319,
            column: 40
          },
          end: {
            line: 319,
            column: 41
          }
        },
        loc: {
          start: {
            line: 320,
            column: 8
          },
          end: {
            line: 324,
            column: 20
          }
        },
        line: 320
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 334,
            column: 32
          },
          end: {
            line: 334,
            column: 33
          }
        },
        loc: {
          start: {
            line: 334,
            column: 56
          },
          end: {
            line: 337,
            column: 9
          }
        },
        line: 334
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 356,
            column: 2
          },
          end: {
            line: 356,
            column: 3
          }
        },
        loc: {
          start: {
            line: 356,
            column: 58
          },
          end: {
            line: 360,
            column: 3
          }
        },
        line: 356
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 362,
            column: 2
          },
          end: {
            line: 362,
            column: 3
          }
        },
        loc: {
          start: {
            line: 362,
            column: 41
          },
          end: {
            line: 365,
            column: 3
          }
        },
        line: 362
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 367,
            column: 2
          },
          end: {
            line: 367,
            column: 3
          }
        },
        loc: {
          start: {
            line: 367,
            column: 62
          },
          end: {
            line: 378,
            column: 3
          }
        },
        line: 367
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 380,
            column: 2
          },
          end: {
            line: 380,
            column: 3
          }
        },
        loc: {
          start: {
            line: 380,
            column: 31
          },
          end: {
            line: 387,
            column: 3
          }
        },
        line: 380
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 389,
            column: 2
          },
          end: {
            line: 389,
            column: 3
          }
        },
        loc: {
          start: {
            line: 389,
            column: 73
          },
          end: {
            line: 401,
            column: 3
          }
        },
        line: 389
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 406,
            column: 2
          },
          end: {
            line: 406,
            column: 3
          }
        },
        loc: {
          start: {
            line: 406,
            column: 18
          },
          end: {
            line: 412,
            column: 3
          }
        },
        line: 406
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 417,
            column: 2
          },
          end: {
            line: 417,
            column: 3
          }
        },
        loc: {
          start: {
            line: 417,
            column: 21
          },
          end: {
            line: 419,
            column: 3
          }
        },
        line: 417
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 41,
            column: 45
          },
          end: {
            line: 41,
            column: 71
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 41,
            column: 69
          },
          end: {
            line: 41,
            column: 71
          }
        }],
        line: 41
      },
      "1": {
        loc: {
          start: {
            line: 43,
            column: 6
          },
          end: {
            line: 43,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 43,
            column: 17
          },
          end: {
            line: 43,
            column: 21
          }
        }],
        line: 43
      },
      "2": {
        loc: {
          start: {
            line: 44,
            column: 6
          },
          end: {
            line: 44,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 44,
            column: 21
          },
          end: {
            line: 44,
            column: 27
          }
        }],
        line: 44
      },
      "3": {
        loc: {
          start: {
            line: 45,
            column: 6
          },
          end: {
            line: 45,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 45,
            column: 16
          },
          end: {
            line: 45,
            column: 21
          }
        }],
        line: 45
      },
      "4": {
        loc: {
          start: {
            line: 51,
            column: 4
          },
          end: {
            line: 53,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 51,
            column: 4
          },
          end: {
            line: 53,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 51
      },
      "5": {
        loc: {
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 51,
            column: 8
          },
          end: {
            line: 51,
            column: 16
          }
        }, {
          start: {
            line: 51,
            column: 20
          },
          end: {
            line: 51,
            column: 61
          }
        }],
        line: 51
      },
      "6": {
        loc: {
          start: {
            line: 142,
            column: 6
          },
          end: {
            line: 144,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 142,
            column: 6
          },
          end: {
            line: 144,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 142
      },
      "7": {
        loc: {
          start: {
            line: 151,
            column: 6
          },
          end: {
            line: 153,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 151,
            column: 6
          },
          end: {
            line: 153,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 151
      },
      "8": {
        loc: {
          start: {
            line: 167,
            column: 20
          },
          end: {
            line: 167,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 20
          },
          end: {
            line: 167,
            column: 41
          }
        }, {
          start: {
            line: 167,
            column: 45
          },
          end: {
            line: 167,
            column: 49
          }
        }],
        line: 167
      },
      "9": {
        loc: {
          start: {
            line: 168,
            column: 24
          },
          end: {
            line: 168,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 168,
            column: 24
          },
          end: {
            line: 168,
            column: 46
          }
        }, {
          start: {
            line: 168,
            column: 50
          },
          end: {
            line: 168,
            column: 52
          }
        }],
        line: 168
      },
      "10": {
        loc: {
          start: {
            line: 169,
            column: 23
          },
          end: {
            line: 169,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 23
          },
          end: {
            line: 169,
            column: 41
          }
        }, {
          start: {
            line: 169,
            column: 45
          },
          end: {
            line: 169,
            column: 47
          }
        }],
        line: 169
      },
      "11": {
        loc: {
          start: {
            line: 170,
            column: 22
          },
          end: {
            line: 170,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 170,
            column: 22
          },
          end: {
            line: 170,
            column: 39
          }
        }, {
          start: {
            line: 170,
            column: 43
          },
          end: {
            line: 170,
            column: 45
          }
        }],
        line: 170
      },
      "12": {
        loc: {
          start: {
            line: 171,
            column: 23
          },
          end: {
            line: 171,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 171,
            column: 23
          },
          end: {
            line: 171,
            column: 63
          }
        }, {
          start: {
            line: 171,
            column: 67
          },
          end: {
            line: 171,
            column: 69
          }
        }],
        line: 171
      },
      "13": {
        loc: {
          start: {
            line: 172,
            column: 18
          },
          end: {
            line: 172,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 172,
            column: 18
          },
          end: {
            line: 172,
            column: 35
          }
        }, {
          start: {
            line: 172,
            column: 39
          },
          end: {
            line: 172,
            column: 43
          }
        }],
        line: 172
      },
      "14": {
        loc: {
          start: {
            line: 177,
            column: 6
          },
          end: {
            line: 179,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 177,
            column: 6
          },
          end: {
            line: 179,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 177
      },
      "15": {
        loc: {
          start: {
            line: 194,
            column: 4
          },
          end: {
            line: 194,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 194,
            column: 43
          },
          end: {
            line: 194,
            column: 50
          }
        }],
        line: 194
      },
      "16": {
        loc: {
          start: {
            line: 195,
            column: 4
          },
          end: {
            line: 195,
            column: 30
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 195,
            column: 28
          },
          end: {
            line: 195,
            column: 30
          }
        }],
        line: 195
      },
      "17": {
        loc: {
          start: {
            line: 198,
            column: 6
          },
          end: {
            line: 198,
            column: 21
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 198,
            column: 17
          },
          end: {
            line: 198,
            column: 21
          }
        }],
        line: 198
      },
      "18": {
        loc: {
          start: {
            line: 199,
            column: 6
          },
          end: {
            line: 199,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 199,
            column: 21
          },
          end: {
            line: 199,
            column: 27
          }
        }],
        line: 199
      },
      "19": {
        loc: {
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 206,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 4
          },
          end: {
            line: 206,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "20": {
        loc: {
          start: {
            line: 204,
            column: 8
          },
          end: {
            line: 204,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 8
          },
          end: {
            line: 204,
            column: 16
          }
        }, {
          start: {
            line: 204,
            column: 20
          },
          end: {
            line: 204,
            column: 61
          }
        }],
        line: 204
      },
      "21": {
        loc: {
          start: {
            line: 243,
            column: 17
          },
          end: {
            line: 243,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 243,
            column: 17
          },
          end: {
            line: 243,
            column: 35
          }
        }, {
          start: {
            line: 243,
            column: 39
          },
          end: {
            line: 243,
            column: 41
          }
        }],
        line: 243
      },
      "22": {
        loc: {
          start: {
            line: 244,
            column: 18
          },
          end: {
            line: 244,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 244,
            column: 18
          },
          end: {
            line: 244,
            column: 37
          }
        }, {
          start: {
            line: 244,
            column: 41
          },
          end: {
            line: 244,
            column: 43
          }
        }],
        line: 244
      },
      "23": {
        loc: {
          start: {
            line: 245,
            column: 20
          },
          end: {
            line: 245,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 245,
            column: 20
          },
          end: {
            line: 245,
            column: 41
          }
        }, {
          start: {
            line: 245,
            column: 45
          },
          end: {
            line: 245,
            column: 47
          }
        }],
        line: 245
      },
      "24": {
        loc: {
          start: {
            line: 251,
            column: 6
          },
          end: {
            line: 253,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 251,
            column: 6
          },
          end: {
            line: 253,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 251
      },
      "25": {
        loc: {
          start: {
            line: 266,
            column: 52
          },
          end: {
            line: 266,
            column: 78
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 266,
            column: 76
          },
          end: {
            line: 266,
            column: 78
          }
        }],
        line: 266
      },
      "26": {
        loc: {
          start: {
            line: 267,
            column: 12
          },
          end: {
            line: 267,
            column: 33
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 267,
            column: 29
          },
          end: {
            line: 267,
            column: 33
          }
        }],
        line: 267
      },
      "27": {
        loc: {
          start: {
            line: 269,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 269,
            column: 4
          },
          end: {
            line: 273,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 269
      },
      "28": {
        loc: {
          start: {
            line: 269,
            column: 8
          },
          end: {
            line: 269,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 269,
            column: 8
          },
          end: {
            line: 269,
            column: 23
          }
        }, {
          start: {
            line: 269,
            column: 27
          },
          end: {
            line: 269,
            column: 46
          }
        }],
        line: 269
      },
      "29": {
        loc: {
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 271,
            column: 29
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 271,
            column: 29
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "30": {
        loc: {
          start: {
            line: 285,
            column: 8
          },
          end: {
            line: 285,
            column: 31
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 285,
            column: 8
          },
          end: {
            line: 285,
            column: 31
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 285
      },
      "31": {
        loc: {
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 286,
            column: 40
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 286,
            column: 8
          },
          end: {
            line: 286,
            column: 40
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 286
      },
      "32": {
        loc: {
          start: {
            line: 306,
            column: 4
          },
          end: {
            line: 306,
            column: 30
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 306,
            column: 28
          },
          end: {
            line: 306,
            column: 30
          }
        }],
        line: 306
      },
      "33": {
        loc: {
          start: {
            line: 308,
            column: 12
          },
          end: {
            line: 308,
            column: 27
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 308,
            column: 23
          },
          end: {
            line: 308,
            column: 27
          }
        }],
        line: 308
      },
      "34": {
        loc: {
          start: {
            line: 308,
            column: 29
          },
          end: {
            line: 308,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 308,
            column: 44
          },
          end: {
            line: 308,
            column: 50
          }
        }],
        line: 308
      },
      "35": {
        loc: {
          start: {
            line: 311,
            column: 4
          },
          end: {
            line: 313,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 311,
            column: 4
          },
          end: {
            line: 313,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 311
      },
      "36": {
        loc: {
          start: {
            line: 311,
            column: 8
          },
          end: {
            line: 311,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 311,
            column: 8
          },
          end: {
            line: 311,
            column: 16
          }
        }, {
          start: {
            line: 311,
            column: 20
          },
          end: {
            line: 311,
            column: 61
          }
        }],
        line: 311
      },
      "37": {
        loc: {
          start: {
            line: 335,
            column: 31
          },
          end: {
            line: 335,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 335,
            column: 31
          },
          end: {
            line: 335,
            column: 42
          }
        }, {
          start: {
            line: 335,
            column: 46
          },
          end: {
            line: 335,
            column: 48
          }
        }],
        line: 335
      },
      "38": {
        loc: {
          start: {
            line: 341,
            column: 6
          },
          end: {
            line: 343,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 341,
            column: 6
          },
          end: {
            line: 343,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 341
      },
      "39": {
        loc: {
          start: {
            line: 358,
            column: 4
          },
          end: {
            line: 358,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 4
          },
          end: {
            line: 358,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 358
      },
      "40": {
        loc: {
          start: {
            line: 364,
            column: 11
          },
          end: {
            line: 364,
            column: 31
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 364,
            column: 11
          },
          end: {
            line: 364,
            column: 23
          }
        }, {
          start: {
            line: 364,
            column: 27
          },
          end: {
            line: 364,
            column: 31
          }
        }],
        line: 364
      },
      "41": {
        loc: {
          start: {
            line: 375,
            column: 4
          },
          end: {
            line: 377,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 375,
            column: 4
          },
          end: {
            line: 377,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 375
      },
      "42": {
        loc: {
          start: {
            line: 383,
            column: 6
          },
          end: {
            line: 385,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 383,
            column: 6
          },
          end: {
            line: 385,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 383
      },
      "43": {
        loc: {
          start: {
            line: 391,
            column: 4
          },
          end: {
            line: 400,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 392,
            column: 6
          },
          end: {
            line: 393,
            column: 65
          }
        }, {
          start: {
            line: 394,
            column: 6
          },
          end: {
            line: 395,
            column: 66
          }
        }, {
          start: {
            line: 396,
            column: 6
          },
          end: {
            line: 397,
            column: 67
          }
        }, {
          start: {
            line: 398,
            column: 6
          },
          end: {
            line: 399,
            column: 66
          }
        }],
        line: 391
      },
      "44": {
        loc: {
          start: {
            line: 409,
            column: 15
          },
          end: {
            line: 409,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 409,
            column: 15
          },
          end: {
            line: 409,
            column: 53
          }
        }, {
          start: {
            line: 409,
            column: 57
          },
          end: {
            line: 409,
            column: 58
          }
        }],
        line: 409
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0
    },
    b: {
      "0": [0],
      "1": [0],
      "2": [0],
      "3": [0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0],
      "16": [0],
      "17": [0],
      "18": [0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0],
      "26": [0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0],
      "33": [0],
      "34": [0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0, 0, 0],
      "44": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "0a54ef669fbcdc9eae5fff587c8849ee844d088f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_e4hcxgn74 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_e4hcxgn74();
import { supabase } from "../../lib/supabase";
import { performanceMonitor } from "../../utils/performance";
var OptimizedDatabaseService = function () {
  function OptimizedDatabaseService() {
    _classCallCheck(this, OptimizedDatabaseService);
    this.queryCache = (cov_e4hcxgn74().s[0]++, new Map());
    this.batchQueue = (cov_e4hcxgn74().s[1]++, []);
    this.batchTimeout = (cov_e4hcxgn74().s[2]++, null);
    this.BATCH_DELAY = (cov_e4hcxgn74().s[3]++, 50);
    this.MAX_BATCH_SIZE = (cov_e4hcxgn74().s[4]++, 10);
  }
  return _createClass(OptimizedDatabaseService, [{
    key: "getUserDashboardData",
    value: (function () {
      var _getUserDashboardData = _asyncToGenerator(function* (userId) {
        var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_e4hcxgn74().b[0][0]++, {});
        cov_e4hcxgn74().f[0]++;
        var _ref = (cov_e4hcxgn74().s[5]++, options),
          _ref$useCache = _ref.useCache,
          useCache = _ref$useCache === void 0 ? (cov_e4hcxgn74().b[1][0]++, true) : _ref$useCache,
          _ref$cacheTimeout = _ref.cacheTimeout,
          cacheTimeout = _ref$cacheTimeout === void 0 ? (cov_e4hcxgn74().b[2][0]++, 300000) : _ref$cacheTimeout,
          _ref$timeout = _ref.timeout,
          timeout = _ref$timeout === void 0 ? (cov_e4hcxgn74().b[3][0]++, 10000) : _ref$timeout;
        var cacheKey = (cov_e4hcxgn74().s[6]++, `dashboard_${userId}`);
        cov_e4hcxgn74().s[7]++;
        if ((cov_e4hcxgn74().b[5][0]++, useCache) && (cov_e4hcxgn74().b[5][1]++, this.isCacheValid(cacheKey, cacheTimeout))) {
          cov_e4hcxgn74().b[4][0]++;
          cov_e4hcxgn74().s[8]++;
          return this.getFromCache(cacheKey);
        } else {
          cov_e4hcxgn74().b[4][1]++;
        }
        var startTime = (cov_e4hcxgn74().s[9]++, Date.now());
        cov_e4hcxgn74().s[10]++;
        try {
          var _data$skill_stats, _data$notifications, _data$ai_tips;
          var _ref2 = (cov_e4hcxgn74().s[11]++, yield Promise.race([supabase.from('users').select(`
            id,
            email,
            full_name,
            skill_level,
            preferred_surface,
            goals,
            created_at,
            updated_at,
            skill_stats!inner (
              forehand,
              backhand,
              serve,
              volley,
              footwork,
              strategy,
              mental_game,
              updated_at
            ),
            training_sessions!inner (
              id,
              title,
              session_type,
              overall_score,
              created_at,
              duration_minutes,
              improvement_areas
            ),
            match_results!inner (
              id,
              opponent_name,
              result,
              match_score,
              created_at,
              duration_minutes,
              match_stats
            ),
            achievements!inner (
              id,
              title,
              description,
              icon,
              earned_at,
              category
            ),
            notifications!inner (
              id,
              title,
              message,
              type,
              read,
              created_at
            ),
            ai_tips!inner (
              id,
              tip_text,
              category,
              created_at,
              personalized
            )
          `).eq('id', userId).order('training_sessions(created_at)', {
              ascending: false
            }).order('match_results(created_at)', {
              ascending: false
            }).order('achievements(earned_at)', {
              ascending: false
            }).order('notifications(created_at)', {
              ascending: false
            }).order('ai_tips(created_at)', {
              ascending: false
            }).limit(20, {
              foreignTable: 'training_sessions'
            }).limit(10, {
              foreignTable: 'match_results'
            }).limit(10, {
              foreignTable: 'achievements'
            }).limit(5, {
              foreignTable: 'notifications'
            }).limit(1, {
              foreignTable: 'ai_tips'
            }).single(), new Promise(function (_, reject) {
              cov_e4hcxgn74().f[1]++;
              cov_e4hcxgn74().s[12]++;
              return setTimeout(function () {
                cov_e4hcxgn74().f[2]++;
                cov_e4hcxgn74().s[13]++;
                return reject(new Error('Query timeout'));
              }, timeout);
            })])),
            data = _ref2.data,
            error = _ref2.error;
          cov_e4hcxgn74().s[14]++;
          if (error) {
            cov_e4hcxgn74().b[6][0]++;
            cov_e4hcxgn74().s[15]++;
            throw new Error(`Database query failed: ${error.message}`);
          } else {
            cov_e4hcxgn74().b[6][1]++;
          }
          var queryTime = (cov_e4hcxgn74().s[16]++, Date.now() - startTime);
          cov_e4hcxgn74().s[17]++;
          performanceMonitor.trackDatabaseQuery('getUserDashboardData', queryTime);
          cov_e4hcxgn74().s[18]++;
          if (queryTime > 2000) {
            cov_e4hcxgn74().b[7][0]++;
            cov_e4hcxgn74().s[19]++;
            console.warn(`Slow database query: getUserDashboardData took ${queryTime}ms`);
          } else {
            cov_e4hcxgn74().b[7][1]++;
          }
          var result = (cov_e4hcxgn74().s[20]++, {
            user: {
              id: data.id,
              email: data.email,
              full_name: data.full_name,
              skill_level: data.skill_level,
              preferred_surface: data.preferred_surface,
              goals: data.goals,
              created_at: data.created_at,
              updated_at: data.updated_at
            },
            skillStats: (cov_e4hcxgn74().b[8][0]++, (_data$skill_stats = data.skill_stats) == null ? void 0 : _data$skill_stats[0]) || (cov_e4hcxgn74().b[8][1]++, null),
            recentSessions: (cov_e4hcxgn74().b[9][0]++, data.training_sessions) || (cov_e4hcxgn74().b[9][1]++, []),
            recentMatches: (cov_e4hcxgn74().b[10][0]++, data.match_results) || (cov_e4hcxgn74().b[10][1]++, []),
            achievements: (cov_e4hcxgn74().b[11][0]++, data.achievements) || (cov_e4hcxgn74().b[11][1]++, []),
            notifications: (cov_e4hcxgn74().b[12][0]++, (_data$notifications = data.notifications) == null ? void 0 : _data$notifications.filter(function (n) {
              cov_e4hcxgn74().f[3]++;
              cov_e4hcxgn74().s[21]++;
              return !n.read;
            })) || (cov_e4hcxgn74().b[12][1]++, []),
            dailyTip: (cov_e4hcxgn74().b[13][0]++, (_data$ai_tips = data.ai_tips) == null ? void 0 : _data$ai_tips[0]) || (cov_e4hcxgn74().b[13][1]++, null),
            queryTime: queryTime
          });
          cov_e4hcxgn74().s[22]++;
          if (useCache) {
            cov_e4hcxgn74().b[14][0]++;
            cov_e4hcxgn74().s[23]++;
            this.setCache(cacheKey, result, cacheTimeout);
          } else {
            cov_e4hcxgn74().b[14][1]++;
          }
          cov_e4hcxgn74().s[24]++;
          return result;
        } catch (error) {
          cov_e4hcxgn74().s[25]++;
          performanceMonitor.trackDatabaseError('getUserDashboardData', error);
          cov_e4hcxgn74().s[26]++;
          throw error;
        }
      });
      function getUserDashboardData(_x) {
        return _getUserDashboardData.apply(this, arguments);
      }
      return getUserDashboardData;
    }())
  }, {
    key: "getPerformanceData",
    value: (function () {
      var _getPerformanceData = _asyncToGenerator(function* (userId) {
        var timeframe = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_e4hcxgn74().b[15][0]++, 'month');
        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_e4hcxgn74().b[16][0]++, {});
        cov_e4hcxgn74().f[4]++;
        var _ref3 = (cov_e4hcxgn74().s[27]++, options),
          _ref3$useCache = _ref3.useCache,
          useCache = _ref3$useCache === void 0 ? (cov_e4hcxgn74().b[17][0]++, true) : _ref3$useCache,
          _ref3$cacheTimeout = _ref3.cacheTimeout,
          cacheTimeout = _ref3$cacheTimeout === void 0 ? (cov_e4hcxgn74().b[18][0]++, 600000) : _ref3$cacheTimeout;
        var cacheKey = (cov_e4hcxgn74().s[28]++, `performance_${userId}_${timeframe}`);
        cov_e4hcxgn74().s[29]++;
        if ((cov_e4hcxgn74().b[20][0]++, useCache) && (cov_e4hcxgn74().b[20][1]++, this.isCacheValid(cacheKey, cacheTimeout))) {
          cov_e4hcxgn74().b[19][0]++;
          cov_e4hcxgn74().s[30]++;
          return this.getFromCache(cacheKey);
        } else {
          cov_e4hcxgn74().b[19][1]++;
        }
        var startTime = (cov_e4hcxgn74().s[31]++, Date.now());
        var cutoffDate = (cov_e4hcxgn74().s[32]++, this.getTimeframeCutoff(timeframe));
        cov_e4hcxgn74().s[33]++;
        try {
          var _ref4 = (cov_e4hcxgn74().s[34]++, yield Promise.all([supabase.from('match_results').select('*').eq('user_id', userId).gte('created_at', cutoffDate.toISOString()).order('created_at', {
              ascending: false
            }).limit(50), supabase.from('training_sessions').select('*').eq('user_id', userId).gte('created_at', cutoffDate.toISOString()).order('created_at', {
              ascending: false
            }).limit(100), supabase.from('skill_stats').select('*').eq('user_id', userId).gte('updated_at', cutoffDate.toISOString()).order('updated_at', {
              ascending: false
            }).limit(20)])),
            _ref5 = _slicedToArray(_ref4, 3),
            matchesResult = _ref5[0],
            sessionsResult = _ref5[1],
            skillStatsResult = _ref5[2];
          var queryTime = (cov_e4hcxgn74().s[35]++, Date.now() - startTime);
          cov_e4hcxgn74().s[36]++;
          performanceMonitor.trackDatabaseQuery('getPerformanceData', queryTime);
          var result = (cov_e4hcxgn74().s[37]++, {
            matches: (cov_e4hcxgn74().b[21][0]++, matchesResult.data) || (cov_e4hcxgn74().b[21][1]++, []),
            sessions: (cov_e4hcxgn74().b[22][0]++, sessionsResult.data) || (cov_e4hcxgn74().b[22][1]++, []),
            skillStats: (cov_e4hcxgn74().b[23][0]++, skillStatsResult.data) || (cov_e4hcxgn74().b[23][1]++, []),
            timeframe: timeframe,
            queryTime: queryTime,
            lastFetched: Date.now()
          });
          cov_e4hcxgn74().s[38]++;
          if (useCache) {
            cov_e4hcxgn74().b[24][0]++;
            cov_e4hcxgn74().s[39]++;
            this.setCache(cacheKey, result, cacheTimeout);
          } else {
            cov_e4hcxgn74().b[24][1]++;
          }
          cov_e4hcxgn74().s[40]++;
          return result;
        } catch (error) {
          cov_e4hcxgn74().s[41]++;
          performanceMonitor.trackDatabaseError('getPerformanceData', error);
          cov_e4hcxgn74().s[42]++;
          throw error;
        }
      });
      function getPerformanceData(_x2) {
        return _getPerformanceData.apply(this, arguments);
      }
      return getPerformanceData;
    }())
  }, {
    key: "batchInsert",
    value: (function () {
      var _batchInsert = _asyncToGenerator(function* (table, records) {
        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_e4hcxgn74().b[25][0]++, {});
        cov_e4hcxgn74().f[5]++;
        var _ref6 = (cov_e4hcxgn74().s[43]++, options),
          _ref6$enableBatching = _ref6.enableBatching,
          enableBatching = _ref6$enableBatching === void 0 ? (cov_e4hcxgn74().b[26][0]++, true) : _ref6$enableBatching;
        cov_e4hcxgn74().s[44]++;
        if ((cov_e4hcxgn74().b[28][0]++, !enableBatching) || (cov_e4hcxgn74().b[28][1]++, records.length <= 1)) {
          cov_e4hcxgn74().b[27][0]++;
          var _ref7 = (cov_e4hcxgn74().s[45]++, yield supabase.from(table).insert(records).select()),
            data = _ref7.data,
            error = _ref7.error;
          cov_e4hcxgn74().s[46]++;
          if (error) {
            cov_e4hcxgn74().b[29][0]++;
            cov_e4hcxgn74().s[47]++;
            throw error;
          } else {
            cov_e4hcxgn74().b[29][1]++;
          }
          cov_e4hcxgn74().s[48]++;
          return data;
        } else {
          cov_e4hcxgn74().b[27][1]++;
        }
        var startTime = (cov_e4hcxgn74().s[49]++, Date.now());
        var batchSize = (cov_e4hcxgn74().s[50]++, 100);
        var results = (cov_e4hcxgn74().s[51]++, []);
        cov_e4hcxgn74().s[52]++;
        try {
          cov_e4hcxgn74().s[53]++;
          for (var i = (cov_e4hcxgn74().s[54]++, 0); i < records.length; i += batchSize) {
            var batch = (cov_e4hcxgn74().s[55]++, records.slice(i, i + batchSize));
            var _ref8 = (cov_e4hcxgn74().s[56]++, yield supabase.from(table).insert(batch).select()),
              _data = _ref8.data,
              _error = _ref8.error;
            cov_e4hcxgn74().s[57]++;
            if (_error) {
              cov_e4hcxgn74().b[30][0]++;
              cov_e4hcxgn74().s[58]++;
              throw _error;
            } else {
              cov_e4hcxgn74().b[30][1]++;
            }
            cov_e4hcxgn74().s[59]++;
            if (_data) {
              cov_e4hcxgn74().b[31][0]++;
              cov_e4hcxgn74().s[60]++;
              results.push.apply(results, _toConsumableArray(_data));
            } else {
              cov_e4hcxgn74().b[31][1]++;
            }
          }
          var queryTime = (cov_e4hcxgn74().s[61]++, Date.now() - startTime);
          cov_e4hcxgn74().s[62]++;
          performanceMonitor.trackDatabaseQuery('batchInsert', queryTime);
          cov_e4hcxgn74().s[63]++;
          return results;
        } catch (error) {
          cov_e4hcxgn74().s[64]++;
          performanceMonitor.trackDatabaseError('batchInsert', error);
          cov_e4hcxgn74().s[65]++;
          throw error;
        }
      });
      function batchInsert(_x3, _x4) {
        return _batchInsert.apply(this, arguments);
      }
      return batchInsert;
    }())
  }, {
    key: "searchContent",
    value: (function () {
      var _searchContent = _asyncToGenerator(function* (query, tables) {
        var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_e4hcxgn74().b[32][0]++, {});
        cov_e4hcxgn74().f[6]++;
        var _ref9 = (cov_e4hcxgn74().s[66]++, options),
          _ref9$useCache = _ref9.useCache,
          useCache = _ref9$useCache === void 0 ? (cov_e4hcxgn74().b[33][0]++, true) : _ref9$useCache,
          _ref9$cacheTimeout = _ref9.cacheTimeout,
          cacheTimeout = _ref9$cacheTimeout === void 0 ? (cov_e4hcxgn74().b[34][0]++, 300000) : _ref9$cacheTimeout;
        var cacheKey = (cov_e4hcxgn74().s[67]++, `search_${query}_${tables.join('_')}`);
        cov_e4hcxgn74().s[68]++;
        if ((cov_e4hcxgn74().b[36][0]++, useCache) && (cov_e4hcxgn74().b[36][1]++, this.isCacheValid(cacheKey, cacheTimeout))) {
          cov_e4hcxgn74().b[35][0]++;
          cov_e4hcxgn74().s[69]++;
          return this.getFromCache(cacheKey);
        } else {
          cov_e4hcxgn74().b[35][1]++;
        }
        var startTime = (cov_e4hcxgn74().s[70]++, Date.now());
        cov_e4hcxgn74().s[71]++;
        try {
          var searchPromises = (cov_e4hcxgn74().s[72]++, tables.map(function (table) {
            cov_e4hcxgn74().f[7]++;
            cov_e4hcxgn74().s[73]++;
            return supabase.from(table).select('*').textSearch('title', query, {
              type: 'websearch'
            }).limit(10);
          }));
          var results = (cov_e4hcxgn74().s[74]++, yield Promise.all(searchPromises));
          var queryTime = (cov_e4hcxgn74().s[75]++, Date.now() - startTime);
          cov_e4hcxgn74().s[76]++;
          performanceMonitor.trackDatabaseQuery('searchContent', queryTime);
          var searchResults = (cov_e4hcxgn74().s[77]++, {
            query: query,
            results: results.reduce(function (acc, result, index) {
              cov_e4hcxgn74().f[8]++;
              cov_e4hcxgn74().s[78]++;
              acc[tables[index]] = (cov_e4hcxgn74().b[37][0]++, result.data) || (cov_e4hcxgn74().b[37][1]++, []);
              cov_e4hcxgn74().s[79]++;
              return acc;
            }, {}),
            queryTime: queryTime
          });
          cov_e4hcxgn74().s[80]++;
          if (useCache) {
            cov_e4hcxgn74().b[38][0]++;
            cov_e4hcxgn74().s[81]++;
            this.setCache(cacheKey, searchResults, cacheTimeout);
          } else {
            cov_e4hcxgn74().b[38][1]++;
          }
          cov_e4hcxgn74().s[82]++;
          return searchResults;
        } catch (error) {
          cov_e4hcxgn74().s[83]++;
          performanceMonitor.trackDatabaseError('searchContent', error);
          cov_e4hcxgn74().s[84]++;
          throw error;
        }
      });
      function searchContent(_x5, _x6) {
        return _searchContent.apply(this, arguments);
      }
      return searchContent;
    }())
  }, {
    key: "isCacheValid",
    value: function isCacheValid(key, ttl) {
      cov_e4hcxgn74().f[9]++;
      var cached = (cov_e4hcxgn74().s[85]++, this.queryCache.get(key));
      cov_e4hcxgn74().s[86]++;
      if (!cached) {
        cov_e4hcxgn74().b[39][0]++;
        cov_e4hcxgn74().s[87]++;
        return false;
      } else {
        cov_e4hcxgn74().b[39][1]++;
      }
      cov_e4hcxgn74().s[88]++;
      return Date.now() - cached.timestamp < ttl;
    }
  }, {
    key: "getFromCache",
    value: function getFromCache(key) {
      cov_e4hcxgn74().f[10]++;
      var cached = (cov_e4hcxgn74().s[89]++, this.queryCache.get(key));
      cov_e4hcxgn74().s[90]++;
      return (cov_e4hcxgn74().b[40][0]++, cached == null ? void 0 : cached.data) || (cov_e4hcxgn74().b[40][1]++, null);
    }
  }, {
    key: "setCache",
    value: function setCache(key, data, ttl) {
      cov_e4hcxgn74().f[11]++;
      cov_e4hcxgn74().s[91]++;
      this.queryCache.set(key, {
        data: data,
        timestamp: Date.now(),
        ttl: ttl
      });
      cov_e4hcxgn74().s[92]++;
      if (this.queryCache.size > 100) {
        cov_e4hcxgn74().b[41][0]++;
        cov_e4hcxgn74().s[93]++;
        this.cleanupCache();
      } else {
        cov_e4hcxgn74().b[41][1]++;
      }
    }
  }, {
    key: "cleanupCache",
    value: function cleanupCache() {
      cov_e4hcxgn74().f[12]++;
      var now = (cov_e4hcxgn74().s[94]++, Date.now());
      cov_e4hcxgn74().s[95]++;
      for (var _ref0 of this.queryCache.entries()) {
        var _ref1 = _slicedToArray(_ref0, 2);
        var key = _ref1[0];
        var value = _ref1[1];
        cov_e4hcxgn74().s[96]++;
        if (now - value.timestamp > value.ttl) {
          cov_e4hcxgn74().b[42][0]++;
          cov_e4hcxgn74().s[97]++;
          this.queryCache.delete(key);
        } else {
          cov_e4hcxgn74().b[42][1]++;
        }
      }
    }
  }, {
    key: "getTimeframeCutoff",
    value: function getTimeframeCutoff(timeframe) {
      cov_e4hcxgn74().f[13]++;
      var now = (cov_e4hcxgn74().s[98]++, new Date());
      cov_e4hcxgn74().s[99]++;
      switch (timeframe) {
        case 'week':
          cov_e4hcxgn74().b[43][0]++;
          cov_e4hcxgn74().s[100]++;
          return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        case 'month':
          cov_e4hcxgn74().b[43][1]++;
          cov_e4hcxgn74().s[101]++;
          return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        case 'year':
          cov_e4hcxgn74().b[43][2]++;
          cov_e4hcxgn74().s[102]++;
          return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        default:
          cov_e4hcxgn74().b[43][3]++;
          cov_e4hcxgn74().s[103]++;
          return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      }
    }
  }, {
    key: "getCacheStats",
    value: function getCacheStats() {
      cov_e4hcxgn74().f[14]++;
      cov_e4hcxgn74().s[104]++;
      return {
        size: this.queryCache.size,
        hitRate: (cov_e4hcxgn74().b[44][0]++, performanceMonitor.getCacheHitRate == null ? void 0 : performanceMonitor.getCacheHitRate()) || (cov_e4hcxgn74().b[44][1]++, 0),
        entries: Array.from(this.queryCache.keys())
      };
    }
  }, {
    key: "clearCache",
    value: function clearCache() {
      cov_e4hcxgn74().f[15]++;
      cov_e4hcxgn74().s[105]++;
      this.queryCache.clear();
    }
  }]);
}();
export var optimizedDatabaseService = (cov_e4hcxgn74().s[106]++, new OptimizedDatabaseService());
export default optimizedDatabaseService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJzdXBhYmFzZSIsInBlcmZvcm1hbmNlTW9uaXRvciIsIk9wdGltaXplZERhdGFiYXNlU2VydmljZSIsIl9jbGFzc0NhbGxDaGVjayIsInF1ZXJ5Q2FjaGUiLCJjb3ZfZTRoY3hnbjc0IiwicyIsIk1hcCIsImJhdGNoUXVldWUiLCJiYXRjaFRpbWVvdXQiLCJCQVRDSF9ERUxBWSIsIk1BWF9CQVRDSF9TSVpFIiwiX2NyZWF0ZUNsYXNzIiwia2V5IiwidmFsdWUiLCJfZ2V0VXNlckRhc2hib2FyZERhdGEiLCJfYXN5bmNUb0dlbmVyYXRvciIsInVzZXJJZCIsIm9wdGlvbnMiLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJ1bmRlZmluZWQiLCJiIiwiZiIsIl9yZWYiLCJfcmVmJHVzZUNhY2hlIiwidXNlQ2FjaGUiLCJfcmVmJGNhY2hlVGltZW91dCIsImNhY2hlVGltZW91dCIsIl9yZWYkdGltZW91dCIsInRpbWVvdXQiLCJjYWNoZUtleSIsImlzQ2FjaGVWYWxpZCIsImdldEZyb21DYWNoZSIsInN0YXJ0VGltZSIsIkRhdGUiLCJub3ciLCJfZGF0YSRza2lsbF9zdGF0cyIsIl9kYXRhJG5vdGlmaWNhdGlvbnMiLCJfZGF0YSRhaV90aXBzIiwiX3JlZjIiLCJQcm9taXNlIiwicmFjZSIsImZyb20iLCJzZWxlY3QiLCJlcSIsIm9yZGVyIiwiYXNjZW5kaW5nIiwibGltaXQiLCJmb3JlaWduVGFibGUiLCJzaW5nbGUiLCJfIiwicmVqZWN0Iiwic2V0VGltZW91dCIsIkVycm9yIiwiZGF0YSIsImVycm9yIiwibWVzc2FnZSIsInF1ZXJ5VGltZSIsInRyYWNrRGF0YWJhc2VRdWVyeSIsImNvbnNvbGUiLCJ3YXJuIiwicmVzdWx0IiwidXNlciIsImlkIiwiZW1haWwiLCJmdWxsX25hbWUiLCJza2lsbF9sZXZlbCIsInByZWZlcnJlZF9zdXJmYWNlIiwiZ29hbHMiLCJjcmVhdGVkX2F0IiwidXBkYXRlZF9hdCIsInNraWxsU3RhdHMiLCJza2lsbF9zdGF0cyIsInJlY2VudFNlc3Npb25zIiwidHJhaW5pbmdfc2Vzc2lvbnMiLCJyZWNlbnRNYXRjaGVzIiwibWF0Y2hfcmVzdWx0cyIsImFjaGlldmVtZW50cyIsIm5vdGlmaWNhdGlvbnMiLCJmaWx0ZXIiLCJuIiwicmVhZCIsImRhaWx5VGlwIiwiYWlfdGlwcyIsInNldENhY2hlIiwidHJhY2tEYXRhYmFzZUVycm9yIiwiZ2V0VXNlckRhc2hib2FyZERhdGEiLCJfeCIsImFwcGx5IiwiX2dldFBlcmZvcm1hbmNlRGF0YSIsInRpbWVmcmFtZSIsIl9yZWYzIiwiX3JlZjMkdXNlQ2FjaGUiLCJfcmVmMyRjYWNoZVRpbWVvdXQiLCJjdXRvZmZEYXRlIiwiZ2V0VGltZWZyYW1lQ3V0b2ZmIiwiX3JlZjQiLCJhbGwiLCJndGUiLCJ0b0lTT1N0cmluZyIsIl9yZWY1IiwiX3NsaWNlZFRvQXJyYXkiLCJtYXRjaGVzUmVzdWx0Iiwic2Vzc2lvbnNSZXN1bHQiLCJza2lsbFN0YXRzUmVzdWx0IiwibWF0Y2hlcyIsInNlc3Npb25zIiwibGFzdEZldGNoZWQiLCJnZXRQZXJmb3JtYW5jZURhdGEiLCJfeDIiLCJfYmF0Y2hJbnNlcnQiLCJ0YWJsZSIsInJlY29yZHMiLCJfcmVmNiIsIl9yZWY2JGVuYWJsZUJhdGNoaW5nIiwiZW5hYmxlQmF0Y2hpbmciLCJfcmVmNyIsImluc2VydCIsImJhdGNoU2l6ZSIsInJlc3VsdHMiLCJpIiwiYmF0Y2giLCJzbGljZSIsIl9yZWY4IiwicHVzaCIsIl90b0NvbnN1bWFibGVBcnJheSIsImJhdGNoSW5zZXJ0IiwiX3gzIiwiX3g0IiwiX3NlYXJjaENvbnRlbnQiLCJxdWVyeSIsInRhYmxlcyIsIl9yZWY5IiwiX3JlZjkkdXNlQ2FjaGUiLCJfcmVmOSRjYWNoZVRpbWVvdXQiLCJqb2luIiwic2VhcmNoUHJvbWlzZXMiLCJtYXAiLCJ0ZXh0U2VhcmNoIiwidHlwZSIsInNlYXJjaFJlc3VsdHMiLCJyZWR1Y2UiLCJhY2MiLCJpbmRleCIsInNlYXJjaENvbnRlbnQiLCJfeDUiLCJfeDYiLCJ0dGwiLCJjYWNoZWQiLCJnZXQiLCJ0aW1lc3RhbXAiLCJzZXQiLCJzaXplIiwiY2xlYW51cENhY2hlIiwiX3JlZjAiLCJlbnRyaWVzIiwiX3JlZjEiLCJkZWxldGUiLCJnZXRUaW1lIiwiZ2V0Q2FjaGVTdGF0cyIsImhpdFJhdGUiLCJnZXRDYWNoZUhpdFJhdGUiLCJBcnJheSIsImtleXMiLCJjbGVhckNhY2hlIiwiY2xlYXIiLCJvcHRpbWl6ZWREYXRhYmFzZVNlcnZpY2UiXSwic291cmNlcyI6WyJkYXRhYmFzZVNlcnZpY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBPcHRpbWl6ZWQgRGF0YWJhc2UgU2VydmljZVxuICogXG4gKiBQcm92aWRlcyBvcHRpbWl6ZWQgZGF0YWJhc2UgcXVlcmllcyB3aXRoIGJhdGNoaW5nLCBjYWNoaW5nLFxuICogYW5kIHBlcmZvcm1hbmNlIG1vbml0b3JpbmcgZm9yIFN1cGFiYXNlIG9wZXJhdGlvbnMuXG4gKi9cblxuaW1wb3J0IHsgc3VwYWJhc2UgfSBmcm9tICdAL2xpYi9zdXBhYmFzZSc7XG5pbXBvcnQgeyBwZXJmb3JtYW5jZU1vbml0b3IgfSBmcm9tICdAL3V0aWxzL3BlcmZvcm1hbmNlJztcblxuaW50ZXJmYWNlIFF1ZXJ5T3B0aW9ucyB7XG4gIHVzZUNhY2hlPzogYm9vbGVhbjtcbiAgY2FjaGVUaW1lb3V0PzogbnVtYmVyO1xuICBlbmFibGVCYXRjaGluZz86IGJvb2xlYW47XG4gIHRpbWVvdXQ/OiBudW1iZXI7XG4gIHJldHJpZXM/OiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBCYXRjaFF1ZXJ5IHtcbiAgaWQ6IHN0cmluZztcbiAgdGFibGU6IHN0cmluZztcbiAgb3BlcmF0aW9uOiAnc2VsZWN0JyB8ICdpbnNlcnQnIHwgJ3VwZGF0ZScgfCAnZGVsZXRlJztcbiAgcXVlcnk6IGFueTtcbiAgcmVzb2x2ZTogKGRhdGE6IGFueSkgPT4gdm9pZDtcbiAgcmVqZWN0OiAoZXJyb3I6IGFueSkgPT4gdm9pZDtcbn1cblxuLyoqXG4gKiBPcHRpbWl6ZWQgRGF0YWJhc2UgU2VydmljZSB3aXRoIGludGVsbGlnZW50IHF1ZXJ5IG9wdGltaXphdGlvblxuICovXG5jbGFzcyBPcHRpbWl6ZWREYXRhYmFzZVNlcnZpY2Uge1xuICBwcml2YXRlIHF1ZXJ5Q2FjaGUgPSBuZXcgTWFwPHN0cmluZywgeyBkYXRhOiBhbnk7IHRpbWVzdGFtcDogbnVtYmVyOyB0dGw6IG51bWJlciB9PigpO1xuICBwcml2YXRlIGJhdGNoUXVldWU6IEJhdGNoUXVlcnlbXSA9IFtdO1xuICBwcml2YXRlIGJhdGNoVGltZW91dDogTm9kZUpTLlRpbWVvdXQgfCBudWxsID0gbnVsbDtcbiAgcHJpdmF0ZSByZWFkb25seSBCQVRDSF9ERUxBWSA9IDUwOyAvLyBtc1xuICBwcml2YXRlIHJlYWRvbmx5IE1BWF9CQVRDSF9TSVpFID0gMTA7XG5cbiAgLyoqXG4gICAqIE9wdGltaXplZCB1c2VyIGRhdGEgZmV0Y2hpbmcgd2l0aCBzaW5nbGUgcXVlcnkgYW5kIGpvaW5zXG4gICAqL1xuICBhc3luYyBnZXRVc2VyRGFzaGJvYXJkRGF0YSh1c2VySWQ6IHN0cmluZywgb3B0aW9uczogUXVlcnlPcHRpb25zID0ge30pOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIHVzZUNhY2hlID0gdHJ1ZSxcbiAgICAgIGNhY2hlVGltZW91dCA9IDMwMDAwMCwgLy8gNSBtaW51dGVzXG4gICAgICB0aW1lb3V0ID0gMTAwMDAsXG4gICAgfSA9IG9wdGlvbnM7XG5cbiAgICBjb25zdCBjYWNoZUtleSA9IGBkYXNoYm9hcmRfJHt1c2VySWR9YDtcbiAgICBcbiAgICAvLyBDaGVjayBjYWNoZSBmaXJzdFxuICAgIGlmICh1c2VDYWNoZSAmJiB0aGlzLmlzQ2FjaGVWYWxpZChjYWNoZUtleSwgY2FjaGVUaW1lb3V0KSkge1xuICAgICAgcmV0dXJuIHRoaXMuZ2V0RnJvbUNhY2hlKGNhY2hlS2V5KTtcbiAgICB9XG5cbiAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgIFxuICAgIHRyeSB7XG4gICAgICAvLyBTaW5nbGUgb3B0aW1pemVkIHF1ZXJ5IHdpdGggYWxsIG5lY2Vzc2FyeSBqb2luc1xuICAgICAgY29uc3QgeyBkYXRhLCBlcnJvciB9ID0gYXdhaXQgUHJvbWlzZS5yYWNlKFtcbiAgICAgICAgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgndXNlcnMnKVxuICAgICAgICAgIC5zZWxlY3QoYFxuICAgICAgICAgICAgaWQsXG4gICAgICAgICAgICBlbWFpbCxcbiAgICAgICAgICAgIGZ1bGxfbmFtZSxcbiAgICAgICAgICAgIHNraWxsX2xldmVsLFxuICAgICAgICAgICAgcHJlZmVycmVkX3N1cmZhY2UsXG4gICAgICAgICAgICBnb2FscyxcbiAgICAgICAgICAgIGNyZWF0ZWRfYXQsXG4gICAgICAgICAgICB1cGRhdGVkX2F0LFxuICAgICAgICAgICAgc2tpbGxfc3RhdHMhaW5uZXIgKFxuICAgICAgICAgICAgICBmb3JlaGFuZCxcbiAgICAgICAgICAgICAgYmFja2hhbmQsXG4gICAgICAgICAgICAgIHNlcnZlLFxuICAgICAgICAgICAgICB2b2xsZXksXG4gICAgICAgICAgICAgIGZvb3R3b3JrLFxuICAgICAgICAgICAgICBzdHJhdGVneSxcbiAgICAgICAgICAgICAgbWVudGFsX2dhbWUsXG4gICAgICAgICAgICAgIHVwZGF0ZWRfYXRcbiAgICAgICAgICAgICksXG4gICAgICAgICAgICB0cmFpbmluZ19zZXNzaW9ucyFpbm5lciAoXG4gICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICB0aXRsZSxcbiAgICAgICAgICAgICAgc2Vzc2lvbl90eXBlLFxuICAgICAgICAgICAgICBvdmVyYWxsX3Njb3JlLFxuICAgICAgICAgICAgICBjcmVhdGVkX2F0LFxuICAgICAgICAgICAgICBkdXJhdGlvbl9taW51dGVzLFxuICAgICAgICAgICAgICBpbXByb3ZlbWVudF9hcmVhc1xuICAgICAgICAgICAgKSxcbiAgICAgICAgICAgIG1hdGNoX3Jlc3VsdHMhaW5uZXIgKFxuICAgICAgICAgICAgICBpZCxcbiAgICAgICAgICAgICAgb3Bwb25lbnRfbmFtZSxcbiAgICAgICAgICAgICAgcmVzdWx0LFxuICAgICAgICAgICAgICBtYXRjaF9zY29yZSxcbiAgICAgICAgICAgICAgY3JlYXRlZF9hdCxcbiAgICAgICAgICAgICAgZHVyYXRpb25fbWludXRlcyxcbiAgICAgICAgICAgICAgbWF0Y2hfc3RhdHNcbiAgICAgICAgICAgICksXG4gICAgICAgICAgICBhY2hpZXZlbWVudHMhaW5uZXIgKFxuICAgICAgICAgICAgICBpZCxcbiAgICAgICAgICAgICAgdGl0bGUsXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uLFxuICAgICAgICAgICAgICBpY29uLFxuICAgICAgICAgICAgICBlYXJuZWRfYXQsXG4gICAgICAgICAgICAgIGNhdGVnb3J5XG4gICAgICAgICAgICApLFxuICAgICAgICAgICAgbm90aWZpY2F0aW9ucyFpbm5lciAoXG4gICAgICAgICAgICAgIGlkLFxuICAgICAgICAgICAgICB0aXRsZSxcbiAgICAgICAgICAgICAgbWVzc2FnZSxcbiAgICAgICAgICAgICAgdHlwZSxcbiAgICAgICAgICAgICAgcmVhZCxcbiAgICAgICAgICAgICAgY3JlYXRlZF9hdFxuICAgICAgICAgICAgKSxcbiAgICAgICAgICAgIGFpX3RpcHMhaW5uZXIgKFxuICAgICAgICAgICAgICBpZCxcbiAgICAgICAgICAgICAgdGlwX3RleHQsXG4gICAgICAgICAgICAgIGNhdGVnb3J5LFxuICAgICAgICAgICAgICBjcmVhdGVkX2F0LFxuICAgICAgICAgICAgICBwZXJzb25hbGl6ZWRcbiAgICAgICAgICAgIClcbiAgICAgICAgICBgKVxuICAgICAgICAgIC5lcSgnaWQnLCB1c2VySWQpXG4gICAgICAgICAgLm9yZGVyKCd0cmFpbmluZ19zZXNzaW9ucyhjcmVhdGVkX2F0KScsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAgIC5vcmRlcignbWF0Y2hfcmVzdWx0cyhjcmVhdGVkX2F0KScsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAgIC5vcmRlcignYWNoaWV2ZW1lbnRzKGVhcm5lZF9hdCknLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgICAgICAub3JkZXIoJ25vdGlmaWNhdGlvbnMoY3JlYXRlZF9hdCknLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgICAgICAub3JkZXIoJ2FpX3RpcHMoY3JlYXRlZF9hdCknLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgICAgICAubGltaXQoMjAsIHsgZm9yZWlnblRhYmxlOiAndHJhaW5pbmdfc2Vzc2lvbnMnIH0pXG4gICAgICAgICAgLmxpbWl0KDEwLCB7IGZvcmVpZ25UYWJsZTogJ21hdGNoX3Jlc3VsdHMnIH0pXG4gICAgICAgICAgLmxpbWl0KDEwLCB7IGZvcmVpZ25UYWJsZTogJ2FjaGlldmVtZW50cycgfSlcbiAgICAgICAgICAubGltaXQoNSwgeyBmb3JlaWduVGFibGU6ICdub3RpZmljYXRpb25zJyB9KVxuICAgICAgICAgIC5saW1pdCgxLCB7IGZvcmVpZ25UYWJsZTogJ2FpX3RpcHMnIH0pXG4gICAgICAgICAgLnNpbmdsZSgpLFxuICAgICAgICBcbiAgICAgICAgLy8gVGltZW91dCBwcm9taXNlXG4gICAgICAgIG5ldyBQcm9taXNlKChfLCByZWplY3QpID0+IFxuICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gcmVqZWN0KG5ldyBFcnJvcignUXVlcnkgdGltZW91dCcpKSwgdGltZW91dClcbiAgICAgICAgKSxcbiAgICAgIF0pO1xuXG4gICAgICBpZiAoZXJyb3IpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBEYXRhYmFzZSBxdWVyeSBmYWlsZWQ6ICR7ZXJyb3IubWVzc2FnZX1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcXVlcnlUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICAgIFxuICAgICAgLy8gTG9nIHBlcmZvcm1hbmNlIG1ldHJpY3NcbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci50cmFja0RhdGFiYXNlUXVlcnkoJ2dldFVzZXJEYXNoYm9hcmREYXRhJywgcXVlcnlUaW1lKTtcbiAgICAgIFxuICAgICAgaWYgKHF1ZXJ5VGltZSA+IDIwMDApIHtcbiAgICAgICAgY29uc29sZS53YXJuKGBTbG93IGRhdGFiYXNlIHF1ZXJ5OiBnZXRVc2VyRGFzaGJvYXJkRGF0YSB0b29rICR7cXVlcnlUaW1lfW1zYCk7XG4gICAgICB9XG5cbiAgICAgIC8vIFRyYW5zZm9ybSBhbmQgc3RydWN0dXJlIHRoZSBkYXRhXG4gICAgICBjb25zdCByZXN1bHQgPSB7XG4gICAgICAgIHVzZXI6IHtcbiAgICAgICAgICBpZDogZGF0YS5pZCxcbiAgICAgICAgICBlbWFpbDogZGF0YS5lbWFpbCxcbiAgICAgICAgICBmdWxsX25hbWU6IGRhdGEuZnVsbF9uYW1lLFxuICAgICAgICAgIHNraWxsX2xldmVsOiBkYXRhLnNraWxsX2xldmVsLFxuICAgICAgICAgIHByZWZlcnJlZF9zdXJmYWNlOiBkYXRhLnByZWZlcnJlZF9zdXJmYWNlLFxuICAgICAgICAgIGdvYWxzOiBkYXRhLmdvYWxzLFxuICAgICAgICAgIGNyZWF0ZWRfYXQ6IGRhdGEuY3JlYXRlZF9hdCxcbiAgICAgICAgICB1cGRhdGVkX2F0OiBkYXRhLnVwZGF0ZWRfYXQsXG4gICAgICAgIH0sXG4gICAgICAgIHNraWxsU3RhdHM6IGRhdGEuc2tpbGxfc3RhdHM/LlswXSB8fCBudWxsLFxuICAgICAgICByZWNlbnRTZXNzaW9uczogZGF0YS50cmFpbmluZ19zZXNzaW9ucyB8fCBbXSxcbiAgICAgICAgcmVjZW50TWF0Y2hlczogZGF0YS5tYXRjaF9yZXN1bHRzIHx8IFtdLFxuICAgICAgICBhY2hpZXZlbWVudHM6IGRhdGEuYWNoaWV2ZW1lbnRzIHx8IFtdLFxuICAgICAgICBub3RpZmljYXRpb25zOiBkYXRhLm5vdGlmaWNhdGlvbnM/LmZpbHRlcihuID0+ICFuLnJlYWQpIHx8IFtdLFxuICAgICAgICBkYWlseVRpcDogZGF0YS5haV90aXBzPy5bMF0gfHwgbnVsbCxcbiAgICAgICAgcXVlcnlUaW1lLFxuICAgICAgfTtcblxuICAgICAgLy8gQ2FjaGUgdGhlIHJlc3VsdFxuICAgICAgaWYgKHVzZUNhY2hlKSB7XG4gICAgICAgIHRoaXMuc2V0Q2FjaGUoY2FjaGVLZXksIHJlc3VsdCwgY2FjaGVUaW1lb3V0KTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHJlc3VsdDtcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBwZXJmb3JtYW5jZU1vbml0b3IudHJhY2tEYXRhYmFzZUVycm9yKCdnZXRVc2VyRGFzaGJvYXJkRGF0YScsIGVycm9yIGFzIEVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBPcHRpbWl6ZWQgcGVyZm9ybWFuY2UgZGF0YSBmZXRjaGluZyB3aXRoIGludGVsbGlnZW50IHBhZ2luYXRpb25cbiAgICovXG4gIGFzeW5jIGdldFBlcmZvcm1hbmNlRGF0YShcbiAgICB1c2VySWQ6IHN0cmluZywgXG4gICAgdGltZWZyYW1lOiAnd2VlaycgfCAnbW9udGgnIHwgJ3llYXInID0gJ21vbnRoJyxcbiAgICBvcHRpb25zOiBRdWVyeU9wdGlvbnMgPSB7fVxuICApOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIHVzZUNhY2hlID0gdHJ1ZSxcbiAgICAgIGNhY2hlVGltZW91dCA9IDYwMDAwMCwgLy8gMTAgbWludXRlc1xuICAgIH0gPSBvcHRpb25zO1xuXG4gICAgY29uc3QgY2FjaGVLZXkgPSBgcGVyZm9ybWFuY2VfJHt1c2VySWR9XyR7dGltZWZyYW1lfWA7XG4gICAgXG4gICAgaWYgKHVzZUNhY2hlICYmIHRoaXMuaXNDYWNoZVZhbGlkKGNhY2hlS2V5LCBjYWNoZVRpbWVvdXQpKSB7XG4gICAgICByZXR1cm4gdGhpcy5nZXRGcm9tQ2FjaGUoY2FjaGVLZXkpO1xuICAgIH1cblxuICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG4gICAgY29uc3QgY3V0b2ZmRGF0ZSA9IHRoaXMuZ2V0VGltZWZyYW1lQ3V0b2ZmKHRpbWVmcmFtZSk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gUGFyYWxsZWwgcXVlcmllcyBmb3IgYmV0dGVyIHBlcmZvcm1hbmNlXG4gICAgICBjb25zdCBbbWF0Y2hlc1Jlc3VsdCwgc2Vzc2lvbnNSZXN1bHQsIHNraWxsU3RhdHNSZXN1bHRdID0gYXdhaXQgUHJvbWlzZS5hbGwoW1xuICAgICAgICBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKCdtYXRjaF9yZXN1bHRzJylcbiAgICAgICAgICAuc2VsZWN0KCcqJylcbiAgICAgICAgICAuZXEoJ3VzZXJfaWQnLCB1c2VySWQpXG4gICAgICAgICAgLmd0ZSgnY3JlYXRlZF9hdCcsIGN1dG9mZkRhdGUudG9JU09TdHJpbmcoKSlcbiAgICAgICAgICAub3JkZXIoJ2NyZWF0ZWRfYXQnLCB7IGFzY2VuZGluZzogZmFsc2UgfSlcbiAgICAgICAgICAubGltaXQoNTApLFxuICAgICAgICBcbiAgICAgICAgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgndHJhaW5pbmdfc2Vzc2lvbnMnKVxuICAgICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgICAgICAuZ3RlKCdjcmVhdGVkX2F0JywgY3V0b2ZmRGF0ZS50b0lTT1N0cmluZygpKVxuICAgICAgICAgIC5vcmRlcignY3JlYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAgIC5saW1pdCgxMDApLFxuICAgICAgICBcbiAgICAgICAgc3VwYWJhc2VcbiAgICAgICAgICAuZnJvbSgnc2tpbGxfc3RhdHMnKVxuICAgICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAgIC5lcSgndXNlcl9pZCcsIHVzZXJJZClcbiAgICAgICAgICAuZ3RlKCd1cGRhdGVkX2F0JywgY3V0b2ZmRGF0ZS50b0lTT1N0cmluZygpKVxuICAgICAgICAgIC5vcmRlcigndXBkYXRlZF9hdCcsIHsgYXNjZW5kaW5nOiBmYWxzZSB9KVxuICAgICAgICAgIC5saW1pdCgyMCksXG4gICAgICBdKTtcblxuICAgICAgY29uc3QgcXVlcnlUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci50cmFja0RhdGFiYXNlUXVlcnkoJ2dldFBlcmZvcm1hbmNlRGF0YScsIHF1ZXJ5VGltZSk7XG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IHtcbiAgICAgICAgbWF0Y2hlczogbWF0Y2hlc1Jlc3VsdC5kYXRhIHx8IFtdLFxuICAgICAgICBzZXNzaW9uczogc2Vzc2lvbnNSZXN1bHQuZGF0YSB8fCBbXSxcbiAgICAgICAgc2tpbGxTdGF0czogc2tpbGxTdGF0c1Jlc3VsdC5kYXRhIHx8IFtdLFxuICAgICAgICB0aW1lZnJhbWUsXG4gICAgICAgIHF1ZXJ5VGltZSxcbiAgICAgICAgbGFzdEZldGNoZWQ6IERhdGUubm93KCksXG4gICAgICB9O1xuXG4gICAgICBpZiAodXNlQ2FjaGUpIHtcbiAgICAgICAgdGhpcy5zZXRDYWNoZShjYWNoZUtleSwgcmVzdWx0LCBjYWNoZVRpbWVvdXQpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gcmVzdWx0O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci50cmFja0RhdGFiYXNlRXJyb3IoJ2dldFBlcmZvcm1hbmNlRGF0YScsIGVycm9yIGFzIEVycm9yKTtcbiAgICAgIHRocm93IGVycm9yO1xuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBCYXRjaCBpbnNlcnQgb3BlcmF0aW9ucyBmb3IgYmV0dGVyIHBlcmZvcm1hbmNlXG4gICAqL1xuICBhc3luYyBiYXRjaEluc2VydDxUPih0YWJsZTogc3RyaW5nLCByZWNvcmRzOiBUW10sIG9wdGlvbnM6IFF1ZXJ5T3B0aW9ucyA9IHt9KTogUHJvbWlzZTxUW10+IHtcbiAgICBjb25zdCB7IGVuYWJsZUJhdGNoaW5nID0gdHJ1ZSB9ID0gb3B0aW9ucztcbiAgICBcbiAgICBpZiAoIWVuYWJsZUJhdGNoaW5nIHx8IHJlY29yZHMubGVuZ3RoIDw9IDEpIHtcbiAgICAgIGNvbnN0IHsgZGF0YSwgZXJyb3IgfSA9IGF3YWl0IHN1cGFiYXNlLmZyb20odGFibGUpLmluc2VydChyZWNvcmRzKS5zZWxlY3QoKTtcbiAgICAgIGlmIChlcnJvcikgdGhyb3cgZXJyb3I7XG4gICAgICByZXR1cm4gZGF0YTtcbiAgICB9XG5cbiAgICBjb25zdCBzdGFydFRpbWUgPSBEYXRlLm5vdygpO1xuICAgIGNvbnN0IGJhdGNoU2l6ZSA9IDEwMDsgLy8gU3VwYWJhc2UgcmVjb21tZW5kZWQgYmF0Y2ggc2l6ZVxuICAgIGNvbnN0IHJlc3VsdHM6IFRbXSA9IFtdO1xuXG4gICAgdHJ5IHtcbiAgICAgIC8vIFByb2Nlc3MgaW4gYmF0Y2hlc1xuICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCByZWNvcmRzLmxlbmd0aDsgaSArPSBiYXRjaFNpemUpIHtcbiAgICAgICAgY29uc3QgYmF0Y2ggPSByZWNvcmRzLnNsaWNlKGksIGkgKyBiYXRjaFNpemUpO1xuICAgICAgICBjb25zdCB7IGRhdGEsIGVycm9yIH0gPSBhd2FpdCBzdXBhYmFzZS5mcm9tKHRhYmxlKS5pbnNlcnQoYmF0Y2gpLnNlbGVjdCgpO1xuICAgICAgICBcbiAgICAgICAgaWYgKGVycm9yKSB0aHJvdyBlcnJvcjtcbiAgICAgICAgaWYgKGRhdGEpIHJlc3VsdHMucHVzaCguLi5kYXRhKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcXVlcnlUaW1lID0gRGF0ZS5ub3coKSAtIHN0YXJ0VGltZTtcbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci50cmFja0RhdGFiYXNlUXVlcnkoJ2JhdGNoSW5zZXJ0JywgcXVlcnlUaW1lKTtcblxuICAgICAgcmV0dXJuIHJlc3VsdHM7XG5cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgcGVyZm9ybWFuY2VNb25pdG9yLnRyYWNrRGF0YWJhc2VFcnJvcignYmF0Y2hJbnNlcnQnLCBlcnJvciBhcyBFcnJvcik7XG4gICAgICB0aHJvdyBlcnJvcjtcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogT3B0aW1pemVkIHNlYXJjaCB3aXRoIGZ1bGwtdGV4dCBzZWFyY2ggYW5kIGluZGV4aW5nXG4gICAqL1xuICBhc3luYyBzZWFyY2hDb250ZW50KFxuICAgIHF1ZXJ5OiBzdHJpbmcsXG4gICAgdGFibGVzOiBzdHJpbmdbXSxcbiAgICBvcHRpb25zOiBRdWVyeU9wdGlvbnMgPSB7fVxuICApOiBQcm9taXNlPGFueT4ge1xuICAgIGNvbnN0IHsgdXNlQ2FjaGUgPSB0cnVlLCBjYWNoZVRpbWVvdXQgPSAzMDAwMDAgfSA9IG9wdGlvbnM7XG4gICAgY29uc3QgY2FjaGVLZXkgPSBgc2VhcmNoXyR7cXVlcnl9XyR7dGFibGVzLmpvaW4oJ18nKX1gO1xuXG4gICAgaWYgKHVzZUNhY2hlICYmIHRoaXMuaXNDYWNoZVZhbGlkKGNhY2hlS2V5LCBjYWNoZVRpbWVvdXQpKSB7XG4gICAgICByZXR1cm4gdGhpcy5nZXRGcm9tQ2FjaGUoY2FjaGVLZXkpO1xuICAgIH1cblxuICAgIGNvbnN0IHN0YXJ0VGltZSA9IERhdGUubm93KCk7XG5cbiAgICB0cnkge1xuICAgICAgLy8gVXNlIFN1cGFiYXNlIGZ1bGwtdGV4dCBzZWFyY2ggZm9yIGJldHRlciBwZXJmb3JtYW5jZVxuICAgICAgY29uc3Qgc2VhcmNoUHJvbWlzZXMgPSB0YWJsZXMubWFwKHRhYmxlID0+IFxuICAgICAgICBzdXBhYmFzZVxuICAgICAgICAgIC5mcm9tKHRhYmxlKVxuICAgICAgICAgIC5zZWxlY3QoJyonKVxuICAgICAgICAgIC50ZXh0U2VhcmNoKCd0aXRsZScsIHF1ZXJ5LCB7IHR5cGU6ICd3ZWJzZWFyY2gnIH0pXG4gICAgICAgICAgLmxpbWl0KDEwKVxuICAgICAgKTtcblxuICAgICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IFByb21pc2UuYWxsKHNlYXJjaFByb21pc2VzKTtcbiAgICAgIGNvbnN0IHF1ZXJ5VGltZSA9IERhdGUubm93KCkgLSBzdGFydFRpbWU7XG5cbiAgICAgIHBlcmZvcm1hbmNlTW9uaXRvci50cmFja0RhdGFiYXNlUXVlcnkoJ3NlYXJjaENvbnRlbnQnLCBxdWVyeVRpbWUpO1xuXG4gICAgICBjb25zdCBzZWFyY2hSZXN1bHRzID0ge1xuICAgICAgICBxdWVyeSxcbiAgICAgICAgcmVzdWx0czogcmVzdWx0cy5yZWR1Y2UoKGFjYywgcmVzdWx0LCBpbmRleCkgPT4ge1xuICAgICAgICAgIGFjY1t0YWJsZXNbaW5kZXhdXSA9IHJlc3VsdC5kYXRhIHx8IFtdO1xuICAgICAgICAgIHJldHVybiBhY2M7XG4gICAgICAgIH0sIHt9IGFzIFJlY29yZDxzdHJpbmcsIGFueVtdPiksXG4gICAgICAgIHF1ZXJ5VGltZSxcbiAgICAgIH07XG5cbiAgICAgIGlmICh1c2VDYWNoZSkge1xuICAgICAgICB0aGlzLnNldENhY2hlKGNhY2hlS2V5LCBzZWFyY2hSZXN1bHRzLCBjYWNoZVRpbWVvdXQpO1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gc2VhcmNoUmVzdWx0cztcblxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBwZXJmb3JtYW5jZU1vbml0b3IudHJhY2tEYXRhYmFzZUVycm9yKCdzZWFyY2hDb250ZW50JywgZXJyb3IgYXMgRXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIENhY2hlIG1hbmFnZW1lbnQgbWV0aG9kc1xuICAgKi9cbiAgcHJpdmF0ZSBpc0NhY2hlVmFsaWQoa2V5OiBzdHJpbmcsIHR0bDogbnVtYmVyKTogYm9vbGVhbiB7XG4gICAgY29uc3QgY2FjaGVkID0gdGhpcy5xdWVyeUNhY2hlLmdldChrZXkpO1xuICAgIGlmICghY2FjaGVkKSByZXR1cm4gZmFsc2U7XG4gICAgcmV0dXJuIERhdGUubm93KCkgLSBjYWNoZWQudGltZXN0YW1wIDwgdHRsO1xuICB9XG5cbiAgcHJpdmF0ZSBnZXRGcm9tQ2FjaGUoa2V5OiBzdHJpbmcpOiBhbnkge1xuICAgIGNvbnN0IGNhY2hlZCA9IHRoaXMucXVlcnlDYWNoZS5nZXQoa2V5KTtcbiAgICByZXR1cm4gY2FjaGVkPy5kYXRhIHx8IG51bGw7XG4gIH1cblxuICBwcml2YXRlIHNldENhY2hlKGtleTogc3RyaW5nLCBkYXRhOiBhbnksIHR0bDogbnVtYmVyKTogdm9pZCB7XG4gICAgdGhpcy5xdWVyeUNhY2hlLnNldChrZXksIHtcbiAgICAgIGRhdGEsXG4gICAgICB0aW1lc3RhbXA6IERhdGUubm93KCksXG4gICAgICB0dGwsXG4gICAgfSk7XG5cbiAgICAvLyBDbGVhbiB1cCBvbGQgY2FjaGUgZW50cmllc1xuICAgIGlmICh0aGlzLnF1ZXJ5Q2FjaGUuc2l6ZSA+IDEwMCkge1xuICAgICAgdGhpcy5jbGVhbnVwQ2FjaGUoKTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGNsZWFudXBDYWNoZSgpOiB2b2lkIHtcbiAgICBjb25zdCBub3cgPSBEYXRlLm5vdygpO1xuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIHRoaXMucXVlcnlDYWNoZS5lbnRyaWVzKCkpIHtcbiAgICAgIGlmIChub3cgLSB2YWx1ZS50aW1lc3RhbXAgPiB2YWx1ZS50dGwpIHtcbiAgICAgICAgdGhpcy5xdWVyeUNhY2hlLmRlbGV0ZShrZXkpO1xuICAgICAgfVxuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgZ2V0VGltZWZyYW1lQ3V0b2ZmKHRpbWVmcmFtZTogJ3dlZWsnIHwgJ21vbnRoJyB8ICd5ZWFyJyk6IERhdGUge1xuICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7XG4gICAgc3dpdGNoICh0aW1lZnJhbWUpIHtcbiAgICAgIGNhc2UgJ3dlZWsnOlxuICAgICAgICByZXR1cm4gbmV3IERhdGUobm93LmdldFRpbWUoKSAtIDcgKiAyNCAqIDYwICogNjAgKiAxMDAwKTtcbiAgICAgIGNhc2UgJ21vbnRoJzpcbiAgICAgICAgcmV0dXJuIG5ldyBEYXRlKG5vdy5nZXRUaW1lKCkgLSAzMCAqIDI0ICogNjAgKiA2MCAqIDEwMDApO1xuICAgICAgY2FzZSAneWVhcic6XG4gICAgICAgIHJldHVybiBuZXcgRGF0ZShub3cuZ2V0VGltZSgpIC0gMzY1ICogMjQgKiA2MCAqIDYwICogMTAwMCk7XG4gICAgICBkZWZhdWx0OlxuICAgICAgICByZXR1cm4gbmV3IERhdGUobm93LmdldFRpbWUoKSAtIDMwICogMjQgKiA2MCAqIDYwICogMTAwMCk7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBjYWNoZSBzdGF0aXN0aWNzIGZvciBtb25pdG9yaW5nXG4gICAqL1xuICBnZXRDYWNoZVN0YXRzKCkge1xuICAgIHJldHVybiB7XG4gICAgICBzaXplOiB0aGlzLnF1ZXJ5Q2FjaGUuc2l6ZSxcbiAgICAgIGhpdFJhdGU6IHBlcmZvcm1hbmNlTW9uaXRvci5nZXRDYWNoZUhpdFJhdGU/LigpIHx8IDAsXG4gICAgICBlbnRyaWVzOiBBcnJheS5mcm9tKHRoaXMucXVlcnlDYWNoZS5rZXlzKCkpLFxuICAgIH07XG4gIH1cblxuICAvKipcbiAgICogQ2xlYXIgYWxsIGNhY2hlXG4gICAqL1xuICBjbGVhckNhY2hlKCk6IHZvaWQge1xuICAgIHRoaXMucXVlcnlDYWNoZS5jbGVhcigpO1xuICB9XG59XG5cbi8vIEV4cG9ydCBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBvcHRpbWl6ZWREYXRhYmFzZVNlcnZpY2UgPSBuZXcgT3B0aW1pemVkRGF0YWJhc2VTZXJ2aWNlKCk7XG5leHBvcnQgZGVmYXVsdCBvcHRpbWl6ZWREYXRhYmFzZVNlcnZpY2U7XG4iXSwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU9BLFNBQVNBLFFBQVE7QUFDakIsU0FBU0Msa0JBQWtCO0FBQThCLElBc0JuREMsd0JBQXdCO0VBQUEsU0FBQUEseUJBQUE7SUFBQUMsZUFBQSxPQUFBRCx3QkFBQTtJQUFBLEtBQ3BCRSxVQUFVLElBQUFDLGFBQUEsR0FBQUMsQ0FBQSxPQUFHLElBQUlDLEdBQUcsQ0FBd0QsQ0FBQztJQUFBLEtBQzdFQyxVQUFVLElBQUFILGFBQUEsR0FBQUMsQ0FBQSxPQUFpQixFQUFFO0lBQUEsS0FDN0JHLFlBQVksSUFBQUosYUFBQSxHQUFBQyxDQUFBLE9BQTBCLElBQUk7SUFBQSxLQUNqQ0ksV0FBVyxJQUFBTCxhQUFBLEdBQUFDLENBQUEsT0FBRyxFQUFFO0lBQUEsS0FDaEJLLGNBQWMsSUFBQU4sYUFBQSxHQUFBQyxDQUFBLE9BQUcsRUFBRTtFQUFBO0VBQUEsT0FBQU0sWUFBQSxDQUFBVix3QkFBQTtJQUFBVyxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBQyxxQkFBQSxHQUFBQyxpQkFBQSxDQUtwQyxXQUEyQkMsTUFBYyxFQUE0QztRQUFBLElBQTFDQyxPQUFxQixHQUFBQyxTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsT0FBQWQsYUFBQSxHQUFBaUIsQ0FBQSxVQUFHLENBQUMsQ0FBQztRQUFBakIsYUFBQSxHQUFBa0IsQ0FBQTtRQUNuRSxJQUFBQyxJQUFBLElBQUFuQixhQUFBLEdBQUFDLENBQUEsT0FJSVksT0FBTztVQUFBTyxhQUFBLEdBQUFELElBQUEsQ0FIVEUsUUFBUTtVQUFSQSxRQUFRLEdBQUFELGFBQUEsZUFBQXBCLGFBQUEsR0FBQWlCLENBQUEsVUFBRyxJQUFJLElBQUFHLGFBQUE7VUFBQUUsaUJBQUEsR0FBQUgsSUFBQSxDQUNmSSxZQUFZO1VBQVpBLFlBQVksR0FBQUQsaUJBQUEsZUFBQXRCLGFBQUEsR0FBQWlCLENBQUEsVUFBRyxNQUFNLElBQUFLLGlCQUFBO1VBQUFFLFlBQUEsR0FBQUwsSUFBQSxDQUNyQk0sT0FBTztVQUFQQSxPQUFPLEdBQUFELFlBQUEsZUFBQXhCLGFBQUEsR0FBQWlCLENBQUEsVUFBRyxLQUFLLElBQUFPLFlBQUE7UUFHakIsSUFBTUUsUUFBUSxJQUFBMUIsYUFBQSxHQUFBQyxDQUFBLE9BQUcsYUFBYVcsTUFBTSxFQUFFO1FBQUNaLGFBQUEsR0FBQUMsQ0FBQTtRQUd2QyxJQUFJLENBQUFELGFBQUEsR0FBQWlCLENBQUEsVUFBQUksUUFBUSxNQUFBckIsYUFBQSxHQUFBaUIsQ0FBQSxVQUFJLElBQUksQ0FBQ1UsWUFBWSxDQUFDRCxRQUFRLEVBQUVILFlBQVksQ0FBQyxHQUFFO1VBQUF2QixhQUFBLEdBQUFpQixDQUFBO1VBQUFqQixhQUFBLEdBQUFDLENBQUE7VUFDekQsT0FBTyxJQUFJLENBQUMyQixZQUFZLENBQUNGLFFBQVEsQ0FBQztRQUNwQyxDQUFDO1VBQUExQixhQUFBLEdBQUFpQixDQUFBO1FBQUE7UUFFRCxJQUFNWSxTQUFTLElBQUE3QixhQUFBLEdBQUFDLENBQUEsT0FBRzZCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7UUFBQy9CLGFBQUEsR0FBQUMsQ0FBQTtRQUU3QixJQUFJO1VBQUEsSUFBQStCLGlCQUFBLEVBQUFDLG1CQUFBLEVBQUFDLGFBQUE7VUFFRixJQUFBQyxLQUFBLElBQUFuQyxhQUFBLEdBQUFDLENBQUEsY0FBOEJtQyxPQUFPLENBQUNDLElBQUksQ0FBQyxDQUN6QzFDLFFBQVEsQ0FDTDJDLElBQUksQ0FBQyxPQUFPLENBQUMsQ0FDYkMsTUFBTSxDQUFDO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLENBQUMsQ0FDREMsRUFBRSxDQUFDLElBQUksRUFBRTVCLE1BQU0sQ0FBQyxDQUNoQjZCLEtBQUssQ0FBQywrQkFBK0IsRUFBRTtjQUFFQyxTQUFTLEVBQUU7WUFBTSxDQUFDLENBQUMsQ0FDNURELEtBQUssQ0FBQywyQkFBMkIsRUFBRTtjQUFFQyxTQUFTLEVBQUU7WUFBTSxDQUFDLENBQUMsQ0FDeERELEtBQUssQ0FBQyx5QkFBeUIsRUFBRTtjQUFFQyxTQUFTLEVBQUU7WUFBTSxDQUFDLENBQUMsQ0FDdERELEtBQUssQ0FBQywyQkFBMkIsRUFBRTtjQUFFQyxTQUFTLEVBQUU7WUFBTSxDQUFDLENBQUMsQ0FDeERELEtBQUssQ0FBQyxxQkFBcUIsRUFBRTtjQUFFQyxTQUFTLEVBQUU7WUFBTSxDQUFDLENBQUMsQ0FDbERDLEtBQUssQ0FBQyxFQUFFLEVBQUU7Y0FBRUMsWUFBWSxFQUFFO1lBQW9CLENBQUMsQ0FBQyxDQUNoREQsS0FBSyxDQUFDLEVBQUUsRUFBRTtjQUFFQyxZQUFZLEVBQUU7WUFBZ0IsQ0FBQyxDQUFDLENBQzVDRCxLQUFLLENBQUMsRUFBRSxFQUFFO2NBQUVDLFlBQVksRUFBRTtZQUFlLENBQUMsQ0FBQyxDQUMzQ0QsS0FBSyxDQUFDLENBQUMsRUFBRTtjQUFFQyxZQUFZLEVBQUU7WUFBZ0IsQ0FBQyxDQUFDLENBQzNDRCxLQUFLLENBQUMsQ0FBQyxFQUFFO2NBQUVDLFlBQVksRUFBRTtZQUFVLENBQUMsQ0FBQyxDQUNyQ0MsTUFBTSxDQUFDLENBQUMsRUFHWCxJQUFJVCxPQUFPLENBQUMsVUFBQ1UsQ0FBQyxFQUFFQyxNQUFNLEVBQ3BCO2NBQUEvQyxhQUFBLEdBQUFrQixDQUFBO2NBQUFsQixhQUFBLEdBQUFDLENBQUE7Y0FBQSxPQUFBK0MsVUFBVSxDQUFDLFlBQU07Z0JBQUFoRCxhQUFBLEdBQUFrQixDQUFBO2dCQUFBbEIsYUFBQSxHQUFBQyxDQUFBO2dCQUFBLE9BQUE4QyxNQUFNLENBQUMsSUFBSUUsS0FBSyxDQUFDLGVBQWUsQ0FBQyxDQUFDO2NBQUQsQ0FBQyxFQUFFeEIsT0FBTyxDQUFDO1lBQUQsQ0FDOUQsQ0FBQyxDQUNGLENBQUM7WUFqRk15QixJQUFJLEdBQUFmLEtBQUEsQ0FBSmUsSUFBSTtZQUFFQyxLQUFLLEdBQUFoQixLQUFBLENBQUxnQixLQUFLO1VBaUZoQm5ELGFBQUEsR0FBQUMsQ0FBQTtVQUVILElBQUlrRCxLQUFLLEVBQUU7WUFBQW5ELGFBQUEsR0FBQWlCLENBQUE7WUFBQWpCLGFBQUEsR0FBQUMsQ0FBQTtZQUNULE1BQU0sSUFBSWdELEtBQUssQ0FBQywwQkFBMEJFLEtBQUssQ0FBQ0MsT0FBTyxFQUFFLENBQUM7VUFDNUQsQ0FBQztZQUFBcEQsYUFBQSxHQUFBaUIsQ0FBQTtVQUFBO1VBRUQsSUFBTW9DLFNBQVMsSUFBQXJELGFBQUEsR0FBQUMsQ0FBQSxRQUFHNkIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHRixTQUFTO1VBQUM3QixhQUFBLEdBQUFDLENBQUE7VUFHekNMLGtCQUFrQixDQUFDMEQsa0JBQWtCLENBQUMsc0JBQXNCLEVBQUVELFNBQVMsQ0FBQztVQUFDckQsYUFBQSxHQUFBQyxDQUFBO1VBRXpFLElBQUlvRCxTQUFTLEdBQUcsSUFBSSxFQUFFO1lBQUFyRCxhQUFBLEdBQUFpQixDQUFBO1lBQUFqQixhQUFBLEdBQUFDLENBQUE7WUFDcEJzRCxPQUFPLENBQUNDLElBQUksQ0FBQyxrREFBa0RILFNBQVMsSUFBSSxDQUFDO1VBQy9FLENBQUM7WUFBQXJELGFBQUEsR0FBQWlCLENBQUE7VUFBQTtVQUdELElBQU13QyxNQUFNLElBQUF6RCxhQUFBLEdBQUFDLENBQUEsUUFBRztZQUNieUQsSUFBSSxFQUFFO2NBQ0pDLEVBQUUsRUFBRVQsSUFBSSxDQUFDUyxFQUFFO2NBQ1hDLEtBQUssRUFBRVYsSUFBSSxDQUFDVSxLQUFLO2NBQ2pCQyxTQUFTLEVBQUVYLElBQUksQ0FBQ1csU0FBUztjQUN6QkMsV0FBVyxFQUFFWixJQUFJLENBQUNZLFdBQVc7Y0FDN0JDLGlCQUFpQixFQUFFYixJQUFJLENBQUNhLGlCQUFpQjtjQUN6Q0MsS0FBSyxFQUFFZCxJQUFJLENBQUNjLEtBQUs7Y0FDakJDLFVBQVUsRUFBRWYsSUFBSSxDQUFDZSxVQUFVO2NBQzNCQyxVQUFVLEVBQUVoQixJQUFJLENBQUNnQjtZQUNuQixDQUFDO1lBQ0RDLFVBQVUsRUFBRSxDQUFBbkUsYUFBQSxHQUFBaUIsQ0FBQSxXQUFBZSxpQkFBQSxHQUFBa0IsSUFBSSxDQUFDa0IsV0FBVyxxQkFBaEJwQyxpQkFBQSxDQUFtQixDQUFDLENBQUMsTUFBQWhDLGFBQUEsR0FBQWlCLENBQUEsVUFBSSxJQUFJO1lBQ3pDb0QsY0FBYyxFQUFFLENBQUFyRSxhQUFBLEdBQUFpQixDQUFBLFVBQUFpQyxJQUFJLENBQUNvQixpQkFBaUIsTUFBQXRFLGFBQUEsR0FBQWlCLENBQUEsVUFBSSxFQUFFO1lBQzVDc0QsYUFBYSxFQUFFLENBQUF2RSxhQUFBLEdBQUFpQixDQUFBLFdBQUFpQyxJQUFJLENBQUNzQixhQUFhLE1BQUF4RSxhQUFBLEdBQUFpQixDQUFBLFdBQUksRUFBRTtZQUN2Q3dELFlBQVksRUFBRSxDQUFBekUsYUFBQSxHQUFBaUIsQ0FBQSxXQUFBaUMsSUFBSSxDQUFDdUIsWUFBWSxNQUFBekUsYUFBQSxHQUFBaUIsQ0FBQSxXQUFJLEVBQUU7WUFDckN5RCxhQUFhLEVBQUUsQ0FBQTFFLGFBQUEsR0FBQWlCLENBQUEsWUFBQWdCLG1CQUFBLEdBQUFpQixJQUFJLENBQUN3QixhQUFhLHFCQUFsQnpDLG1CQUFBLENBQW9CMEMsTUFBTSxDQUFDLFVBQUFDLENBQUMsRUFBSTtjQUFBNUUsYUFBQSxHQUFBa0IsQ0FBQTtjQUFBbEIsYUFBQSxHQUFBQyxDQUFBO2NBQUEsUUFBQzJFLENBQUMsQ0FBQ0MsSUFBSTtZQUFELENBQUMsQ0FBQyxNQUFBN0UsYUFBQSxHQUFBaUIsQ0FBQSxXQUFJLEVBQUU7WUFDN0Q2RCxRQUFRLEVBQUUsQ0FBQTlFLGFBQUEsR0FBQWlCLENBQUEsWUFBQWlCLGFBQUEsR0FBQWdCLElBQUksQ0FBQzZCLE9BQU8scUJBQVo3QyxhQUFBLENBQWUsQ0FBQyxDQUFDLE1BQUFsQyxhQUFBLEdBQUFpQixDQUFBLFdBQUksSUFBSTtZQUNuQ29DLFNBQVMsRUFBVEE7VUFDRixDQUFDO1VBQUNyRCxhQUFBLEdBQUFDLENBQUE7VUFHRixJQUFJb0IsUUFBUSxFQUFFO1lBQUFyQixhQUFBLEdBQUFpQixDQUFBO1lBQUFqQixhQUFBLEdBQUFDLENBQUE7WUFDWixJQUFJLENBQUMrRSxRQUFRLENBQUN0RCxRQUFRLEVBQUUrQixNQUFNLEVBQUVsQyxZQUFZLENBQUM7VUFDL0MsQ0FBQztZQUFBdkIsYUFBQSxHQUFBaUIsQ0FBQTtVQUFBO1VBQUFqQixhQUFBLEdBQUFDLENBQUE7VUFFRCxPQUFPd0QsTUFBTTtRQUVmLENBQUMsQ0FBQyxPQUFPTixLQUFLLEVBQUU7VUFBQW5ELGFBQUEsR0FBQUMsQ0FBQTtVQUNkTCxrQkFBa0IsQ0FBQ3FGLGtCQUFrQixDQUFDLHNCQUFzQixFQUFFOUIsS0FBYyxDQUFDO1VBQUNuRCxhQUFBLEdBQUFDLENBQUE7VUFDOUUsTUFBTWtELEtBQUs7UUFDYjtNQUNGLENBQUM7TUFBQSxTQWxKSytCLG9CQUFvQkEsQ0FBQUMsRUFBQTtRQUFBLE9BQUF6RSxxQkFBQSxDQUFBMEUsS0FBQSxPQUFBdEUsU0FBQTtNQUFBO01BQUEsT0FBcEJvRSxvQkFBb0I7SUFBQTtFQUFBO0lBQUExRSxHQUFBO0lBQUFDLEtBQUE7TUFBQSxJQUFBNEUsbUJBQUEsR0FBQTFFLGlCQUFBLENBdUoxQixXQUNFQyxNQUFjLEVBR0E7UUFBQSxJQUZkMEUsU0FBb0MsR0FBQXhFLFNBQUEsQ0FBQUMsTUFBQSxRQUFBRCxTQUFBLFFBQUFFLFNBQUEsR0FBQUYsU0FBQSxPQUFBZCxhQUFBLEdBQUFpQixDQUFBLFdBQUcsT0FBTztRQUFBLElBQzlDSixPQUFxQixHQUFBQyxTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsT0FBQWQsYUFBQSxHQUFBaUIsQ0FBQSxXQUFHLENBQUMsQ0FBQztRQUFBakIsYUFBQSxHQUFBa0IsQ0FBQTtRQUUxQixJQUFBcUUsS0FBQSxJQUFBdkYsYUFBQSxHQUFBQyxDQUFBLFFBR0lZLE9BQU87VUFBQTJFLGNBQUEsR0FBQUQsS0FBQSxDQUZUbEUsUUFBUTtVQUFSQSxRQUFRLEdBQUFtRSxjQUFBLGVBQUF4RixhQUFBLEdBQUFpQixDQUFBLFdBQUcsSUFBSSxJQUFBdUUsY0FBQTtVQUFBQyxrQkFBQSxHQUFBRixLQUFBLENBQ2ZoRSxZQUFZO1VBQVpBLFlBQVksR0FBQWtFLGtCQUFBLGVBQUF6RixhQUFBLEdBQUFpQixDQUFBLFdBQUcsTUFBTSxJQUFBd0Usa0JBQUE7UUFHdkIsSUFBTS9ELFFBQVEsSUFBQTFCLGFBQUEsR0FBQUMsQ0FBQSxRQUFHLGVBQWVXLE1BQU0sSUFBSTBFLFNBQVMsRUFBRTtRQUFDdEYsYUFBQSxHQUFBQyxDQUFBO1FBRXRELElBQUksQ0FBQUQsYUFBQSxHQUFBaUIsQ0FBQSxXQUFBSSxRQUFRLE1BQUFyQixhQUFBLEdBQUFpQixDQUFBLFdBQUksSUFBSSxDQUFDVSxZQUFZLENBQUNELFFBQVEsRUFBRUgsWUFBWSxDQUFDLEdBQUU7VUFBQXZCLGFBQUEsR0FBQWlCLENBQUE7VUFBQWpCLGFBQUEsR0FBQUMsQ0FBQTtVQUN6RCxPQUFPLElBQUksQ0FBQzJCLFlBQVksQ0FBQ0YsUUFBUSxDQUFDO1FBQ3BDLENBQUM7VUFBQTFCLGFBQUEsR0FBQWlCLENBQUE7UUFBQTtRQUVELElBQU1ZLFNBQVMsSUFBQTdCLGFBQUEsR0FBQUMsQ0FBQSxRQUFHNkIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztRQUM1QixJQUFNMkQsVUFBVSxJQUFBMUYsYUFBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSSxDQUFDMEYsa0JBQWtCLENBQUNMLFNBQVMsQ0FBQztRQUFDdEYsYUFBQSxHQUFBQyxDQUFBO1FBRXRELElBQUk7VUFFRixJQUFBMkYsS0FBQSxJQUFBNUYsYUFBQSxHQUFBQyxDQUFBLGNBQWdFbUMsT0FBTyxDQUFDeUQsR0FBRyxDQUFDLENBQzFFbEcsUUFBUSxDQUNMMkMsSUFBSSxDQUFDLGVBQWUsQ0FBQyxDQUNyQkMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUNYQyxFQUFFLENBQUMsU0FBUyxFQUFFNUIsTUFBTSxDQUFDLENBQ3JCa0YsR0FBRyxDQUFDLFlBQVksRUFBRUosVUFBVSxDQUFDSyxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQzNDdEQsS0FBSyxDQUFDLFlBQVksRUFBRTtjQUFFQyxTQUFTLEVBQUU7WUFBTSxDQUFDLENBQUMsQ0FDekNDLEtBQUssQ0FBQyxFQUFFLENBQUMsRUFFWmhELFFBQVEsQ0FDTDJDLElBQUksQ0FBQyxtQkFBbUIsQ0FBQyxDQUN6QkMsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUNYQyxFQUFFLENBQUMsU0FBUyxFQUFFNUIsTUFBTSxDQUFDLENBQ3JCa0YsR0FBRyxDQUFDLFlBQVksRUFBRUosVUFBVSxDQUFDSyxXQUFXLENBQUMsQ0FBQyxDQUFDLENBQzNDdEQsS0FBSyxDQUFDLFlBQVksRUFBRTtjQUFFQyxTQUFTLEVBQUU7WUFBTSxDQUFDLENBQUMsQ0FDekNDLEtBQUssQ0FBQyxHQUFHLENBQUMsRUFFYmhELFFBQVEsQ0FDTDJDLElBQUksQ0FBQyxhQUFhLENBQUMsQ0FDbkJDLE1BQU0sQ0FBQyxHQUFHLENBQUMsQ0FDWEMsRUFBRSxDQUFDLFNBQVMsRUFBRTVCLE1BQU0sQ0FBQyxDQUNyQmtGLEdBQUcsQ0FBQyxZQUFZLEVBQUVKLFVBQVUsQ0FBQ0ssV0FBVyxDQUFDLENBQUMsQ0FBQyxDQUMzQ3RELEtBQUssQ0FBQyxZQUFZLEVBQUU7Y0FBRUMsU0FBUyxFQUFFO1lBQU0sQ0FBQyxDQUFDLENBQ3pDQyxLQUFLLENBQUMsRUFBRSxDQUFDLENBQ2IsQ0FBQztZQUFBcUQsS0FBQSxHQUFBQyxjQUFBLENBQUFMLEtBQUE7WUF4QktNLGFBQWEsR0FBQUYsS0FBQTtZQUFFRyxjQUFjLEdBQUFILEtBQUE7WUFBRUksZ0JBQWdCLEdBQUFKLEtBQUE7VUEwQnRELElBQU0zQyxTQUFTLElBQUFyRCxhQUFBLEdBQUFDLENBQUEsUUFBRzZCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUMsR0FBR0YsU0FBUztVQUFDN0IsYUFBQSxHQUFBQyxDQUFBO1VBQ3pDTCxrQkFBa0IsQ0FBQzBELGtCQUFrQixDQUFDLG9CQUFvQixFQUFFRCxTQUFTLENBQUM7VUFFdEUsSUFBTUksTUFBTSxJQUFBekQsYUFBQSxHQUFBQyxDQUFBLFFBQUc7WUFDYm9HLE9BQU8sRUFBRSxDQUFBckcsYUFBQSxHQUFBaUIsQ0FBQSxXQUFBaUYsYUFBYSxDQUFDaEQsSUFBSSxNQUFBbEQsYUFBQSxHQUFBaUIsQ0FBQSxXQUFJLEVBQUU7WUFDakNxRixRQUFRLEVBQUUsQ0FBQXRHLGFBQUEsR0FBQWlCLENBQUEsV0FBQWtGLGNBQWMsQ0FBQ2pELElBQUksTUFBQWxELGFBQUEsR0FBQWlCLENBQUEsV0FBSSxFQUFFO1lBQ25Da0QsVUFBVSxFQUFFLENBQUFuRSxhQUFBLEdBQUFpQixDQUFBLFdBQUFtRixnQkFBZ0IsQ0FBQ2xELElBQUksTUFBQWxELGFBQUEsR0FBQWlCLENBQUEsV0FBSSxFQUFFO1lBQ3ZDcUUsU0FBUyxFQUFUQSxTQUFTO1lBQ1RqQyxTQUFTLEVBQVRBLFNBQVM7WUFDVGtELFdBQVcsRUFBRXpFLElBQUksQ0FBQ0MsR0FBRyxDQUFDO1VBQ3hCLENBQUM7VUFBQy9CLGFBQUEsR0FBQUMsQ0FBQTtVQUVGLElBQUlvQixRQUFRLEVBQUU7WUFBQXJCLGFBQUEsR0FBQWlCLENBQUE7WUFBQWpCLGFBQUEsR0FBQUMsQ0FBQTtZQUNaLElBQUksQ0FBQytFLFFBQVEsQ0FBQ3RELFFBQVEsRUFBRStCLE1BQU0sRUFBRWxDLFlBQVksQ0FBQztVQUMvQyxDQUFDO1lBQUF2QixhQUFBLEdBQUFpQixDQUFBO1VBQUE7VUFBQWpCLGFBQUEsR0FBQUMsQ0FBQTtVQUVELE9BQU93RCxNQUFNO1FBRWYsQ0FBQyxDQUFDLE9BQU9OLEtBQUssRUFBRTtVQUFBbkQsYUFBQSxHQUFBQyxDQUFBO1VBQ2RMLGtCQUFrQixDQUFDcUYsa0JBQWtCLENBQUMsb0JBQW9CLEVBQUU5QixLQUFjLENBQUM7VUFBQ25ELGFBQUEsR0FBQUMsQ0FBQTtVQUM1RSxNQUFNa0QsS0FBSztRQUNiO01BQ0YsQ0FBQztNQUFBLFNBckVLcUQsa0JBQWtCQSxDQUFBQyxHQUFBO1FBQUEsT0FBQXBCLG1CQUFBLENBQUFELEtBQUEsT0FBQXRFLFNBQUE7TUFBQTtNQUFBLE9BQWxCMEYsa0JBQWtCO0lBQUE7RUFBQTtJQUFBaEcsR0FBQTtJQUFBQyxLQUFBO01BQUEsSUFBQWlHLFlBQUEsR0FBQS9GLGlCQUFBLENBMEV4QixXQUFxQmdHLEtBQWEsRUFBRUMsT0FBWSxFQUE0QztRQUFBLElBQTFDL0YsT0FBcUIsR0FBQUMsU0FBQSxDQUFBQyxNQUFBLFFBQUFELFNBQUEsUUFBQUUsU0FBQSxHQUFBRixTQUFBLE9BQUFkLGFBQUEsR0FBQWlCLENBQUEsV0FBRyxDQUFDLENBQUM7UUFBQWpCLGFBQUEsR0FBQWtCLENBQUE7UUFDMUUsSUFBQTJGLEtBQUEsSUFBQTdHLGFBQUEsR0FBQUMsQ0FBQSxRQUFrQ1ksT0FBTztVQUFBaUcsb0JBQUEsR0FBQUQsS0FBQSxDQUFqQ0UsY0FBYztVQUFkQSxjQUFjLEdBQUFELG9CQUFBLGVBQUE5RyxhQUFBLEdBQUFpQixDQUFBLFdBQUcsSUFBSSxJQUFBNkYsb0JBQUE7UUFBYTlHLGFBQUEsR0FBQUMsQ0FBQTtRQUUxQyxJQUFJLENBQUFELGFBQUEsR0FBQWlCLENBQUEsWUFBQzhGLGNBQWMsTUFBQS9HLGFBQUEsR0FBQWlCLENBQUEsV0FBSTJGLE9BQU8sQ0FBQzdGLE1BQU0sSUFBSSxDQUFDLEdBQUU7VUFBQWYsYUFBQSxHQUFBaUIsQ0FBQTtVQUMxQyxJQUFBK0YsS0FBQSxJQUFBaEgsYUFBQSxHQUFBQyxDQUFBLGNBQThCTixRQUFRLENBQUMyQyxJQUFJLENBQUNxRSxLQUFLLENBQUMsQ0FBQ00sTUFBTSxDQUFDTCxPQUFPLENBQUMsQ0FBQ3JFLE1BQU0sQ0FBQyxDQUFDO1lBQW5FVyxJQUFJLEdBQUE4RCxLQUFBLENBQUo5RCxJQUFJO1lBQUVDLEtBQUssR0FBQTZELEtBQUEsQ0FBTDdELEtBQUs7VUFBeURuRCxhQUFBLEdBQUFDLENBQUE7VUFDNUUsSUFBSWtELEtBQUssRUFBRTtZQUFBbkQsYUFBQSxHQUFBaUIsQ0FBQTtZQUFBakIsYUFBQSxHQUFBQyxDQUFBO1lBQUEsTUFBTWtELEtBQUs7VUFBQSxDQUFDO1lBQUFuRCxhQUFBLEdBQUFpQixDQUFBO1VBQUE7VUFBQWpCLGFBQUEsR0FBQUMsQ0FBQTtVQUN2QixPQUFPaUQsSUFBSTtRQUNiLENBQUM7VUFBQWxELGFBQUEsR0FBQWlCLENBQUE7UUFBQTtRQUVELElBQU1ZLFNBQVMsSUFBQTdCLGFBQUEsR0FBQUMsQ0FBQSxRQUFHNkIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQztRQUM1QixJQUFNbUYsU0FBUyxJQUFBbEgsYUFBQSxHQUFBQyxDQUFBLFFBQUcsR0FBRztRQUNyQixJQUFNa0gsT0FBWSxJQUFBbkgsYUFBQSxHQUFBQyxDQUFBLFFBQUcsRUFBRTtRQUFDRCxhQUFBLEdBQUFDLENBQUE7UUFFeEIsSUFBSTtVQUFBRCxhQUFBLEdBQUFDLENBQUE7VUFFRixLQUFLLElBQUltSCxDQUFDLElBQUFwSCxhQUFBLEdBQUFDLENBQUEsUUFBRyxDQUFDLEdBQUVtSCxDQUFDLEdBQUdSLE9BQU8sQ0FBQzdGLE1BQU0sRUFBRXFHLENBQUMsSUFBSUYsU0FBUyxFQUFFO1lBQ2xELElBQU1HLEtBQUssSUFBQXJILGFBQUEsR0FBQUMsQ0FBQSxRQUFHMkcsT0FBTyxDQUFDVSxLQUFLLENBQUNGLENBQUMsRUFBRUEsQ0FBQyxHQUFHRixTQUFTLENBQUM7WUFDN0MsSUFBQUssS0FBQSxJQUFBdkgsYUFBQSxHQUFBQyxDQUFBLGNBQThCTixRQUFRLENBQUMyQyxJQUFJLENBQUNxRSxLQUFLLENBQUMsQ0FBQ00sTUFBTSxDQUFDSSxLQUFLLENBQUMsQ0FBQzlFLE1BQU0sQ0FBQyxDQUFDO2NBQWpFVyxLQUFJLEdBQUFxRSxLQUFBLENBQUpyRSxJQUFJO2NBQUVDLE1BQUssR0FBQW9FLEtBQUEsQ0FBTHBFLEtBQUs7WUFBdURuRCxhQUFBLEdBQUFDLENBQUE7WUFFMUUsSUFBSWtELE1BQUssRUFBRTtjQUFBbkQsYUFBQSxHQUFBaUIsQ0FBQTtjQUFBakIsYUFBQSxHQUFBQyxDQUFBO2NBQUEsTUFBTWtELE1BQUs7WUFBQSxDQUFDO2NBQUFuRCxhQUFBLEdBQUFpQixDQUFBO1lBQUE7WUFBQWpCLGFBQUEsR0FBQUMsQ0FBQTtZQUN2QixJQUFJaUQsS0FBSSxFQUFFO2NBQUFsRCxhQUFBLEdBQUFpQixDQUFBO2NBQUFqQixhQUFBLEdBQUFDLENBQUE7Y0FBQWtILE9BQU8sQ0FBQ0ssSUFBSSxDQUFBcEMsS0FBQSxDQUFaK0IsT0FBTyxFQUFBTSxrQkFBQSxDQUFTdkUsS0FBSSxFQUFDO1lBQUEsQ0FBQztjQUFBbEQsYUFBQSxHQUFBaUIsQ0FBQTtZQUFBO1VBQ2xDO1VBRUEsSUFBTW9DLFNBQVMsSUFBQXJELGFBQUEsR0FBQUMsQ0FBQSxRQUFHNkIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHRixTQUFTO1VBQUM3QixhQUFBLEdBQUFDLENBQUE7VUFDekNMLGtCQUFrQixDQUFDMEQsa0JBQWtCLENBQUMsYUFBYSxFQUFFRCxTQUFTLENBQUM7VUFBQ3JELGFBQUEsR0FBQUMsQ0FBQTtVQUVoRSxPQUFPa0gsT0FBTztRQUVoQixDQUFDLENBQUMsT0FBT2hFLEtBQUssRUFBRTtVQUFBbkQsYUFBQSxHQUFBQyxDQUFBO1VBQ2RMLGtCQUFrQixDQUFDcUYsa0JBQWtCLENBQUMsYUFBYSxFQUFFOUIsS0FBYyxDQUFDO1VBQUNuRCxhQUFBLEdBQUFDLENBQUE7VUFDckUsTUFBTWtELEtBQUs7UUFDYjtNQUNGLENBQUM7TUFBQSxTQWhDS3VFLFdBQVdBLENBQUFDLEdBQUEsRUFBQUMsR0FBQTtRQUFBLE9BQUFsQixZQUFBLENBQUF0QixLQUFBLE9BQUF0RSxTQUFBO01BQUE7TUFBQSxPQUFYNEcsV0FBVztJQUFBO0VBQUE7SUFBQWxILEdBQUE7SUFBQUMsS0FBQTtNQUFBLElBQUFvSCxjQUFBLEdBQUFsSCxpQkFBQSxDQXFDakIsV0FDRW1ILEtBQWEsRUFDYkMsTUFBZ0IsRUFFRjtRQUFBLElBRGRsSCxPQUFxQixHQUFBQyxTQUFBLENBQUFDLE1BQUEsUUFBQUQsU0FBQSxRQUFBRSxTQUFBLEdBQUFGLFNBQUEsT0FBQWQsYUFBQSxHQUFBaUIsQ0FBQSxXQUFHLENBQUMsQ0FBQztRQUFBakIsYUFBQSxHQUFBa0IsQ0FBQTtRQUUxQixJQUFBOEcsS0FBQSxJQUFBaEksYUFBQSxHQUFBQyxDQUFBLFFBQW1EWSxPQUFPO1VBQUFvSCxjQUFBLEdBQUFELEtBQUEsQ0FBbEQzRyxRQUFRO1VBQVJBLFFBQVEsR0FBQTRHLGNBQUEsZUFBQWpJLGFBQUEsR0FBQWlCLENBQUEsV0FBRyxJQUFJLElBQUFnSCxjQUFBO1VBQUFDLGtCQUFBLEdBQUFGLEtBQUEsQ0FBRXpHLFlBQVk7VUFBWkEsWUFBWSxHQUFBMkcsa0JBQUEsZUFBQWxJLGFBQUEsR0FBQWlCLENBQUEsV0FBRyxNQUFNLElBQUFpSCxrQkFBQTtRQUM5QyxJQUFNeEcsUUFBUSxJQUFBMUIsYUFBQSxHQUFBQyxDQUFBLFFBQUcsVUFBVTZILEtBQUssSUFBSUMsTUFBTSxDQUFDSSxJQUFJLENBQUMsR0FBRyxDQUFDLEVBQUU7UUFBQ25JLGFBQUEsR0FBQUMsQ0FBQTtRQUV2RCxJQUFJLENBQUFELGFBQUEsR0FBQWlCLENBQUEsV0FBQUksUUFBUSxNQUFBckIsYUFBQSxHQUFBaUIsQ0FBQSxXQUFJLElBQUksQ0FBQ1UsWUFBWSxDQUFDRCxRQUFRLEVBQUVILFlBQVksQ0FBQyxHQUFFO1VBQUF2QixhQUFBLEdBQUFpQixDQUFBO1VBQUFqQixhQUFBLEdBQUFDLENBQUE7VUFDekQsT0FBTyxJQUFJLENBQUMyQixZQUFZLENBQUNGLFFBQVEsQ0FBQztRQUNwQyxDQUFDO1VBQUExQixhQUFBLEdBQUFpQixDQUFBO1FBQUE7UUFFRCxJQUFNWSxTQUFTLElBQUE3QixhQUFBLEdBQUFDLENBQUEsUUFBRzZCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7UUFBQy9CLGFBQUEsR0FBQUMsQ0FBQTtRQUU3QixJQUFJO1VBRUYsSUFBTW1JLGNBQWMsSUFBQXBJLGFBQUEsR0FBQUMsQ0FBQSxRQUFHOEgsTUFBTSxDQUFDTSxHQUFHLENBQUMsVUFBQTFCLEtBQUssRUFDckM7WUFBQTNHLGFBQUEsR0FBQWtCLENBQUE7WUFBQWxCLGFBQUEsR0FBQUMsQ0FBQTtZQUFBLE9BQUFOLFFBQVEsQ0FDTDJDLElBQUksQ0FBQ3FFLEtBQUssQ0FBQyxDQUNYcEUsTUFBTSxDQUFDLEdBQUcsQ0FBQyxDQUNYK0YsVUFBVSxDQUFDLE9BQU8sRUFBRVIsS0FBSyxFQUFFO2NBQUVTLElBQUksRUFBRTtZQUFZLENBQUMsQ0FBQyxDQUNqRDVGLEtBQUssQ0FBQyxFQUFFLENBQUM7VUFBRCxDQUNiLENBQUM7VUFFRCxJQUFNd0UsT0FBTyxJQUFBbkgsYUFBQSxHQUFBQyxDQUFBLGNBQVNtQyxPQUFPLENBQUN5RCxHQUFHLENBQUN1QyxjQUFjLENBQUM7VUFDakQsSUFBTS9FLFNBQVMsSUFBQXJELGFBQUEsR0FBQUMsQ0FBQSxRQUFHNkIsSUFBSSxDQUFDQyxHQUFHLENBQUMsQ0FBQyxHQUFHRixTQUFTO1VBQUM3QixhQUFBLEdBQUFDLENBQUE7VUFFekNMLGtCQUFrQixDQUFDMEQsa0JBQWtCLENBQUMsZUFBZSxFQUFFRCxTQUFTLENBQUM7VUFFakUsSUFBTW1GLGFBQWEsSUFBQXhJLGFBQUEsR0FBQUMsQ0FBQSxRQUFHO1lBQ3BCNkgsS0FBSyxFQUFMQSxLQUFLO1lBQ0xYLE9BQU8sRUFBRUEsT0FBTyxDQUFDc0IsTUFBTSxDQUFDLFVBQUNDLEdBQUcsRUFBRWpGLE1BQU0sRUFBRWtGLEtBQUssRUFBSztjQUFBM0ksYUFBQSxHQUFBa0IsQ0FBQTtjQUFBbEIsYUFBQSxHQUFBQyxDQUFBO2NBQzlDeUksR0FBRyxDQUFDWCxNQUFNLENBQUNZLEtBQUssQ0FBQyxDQUFDLEdBQUcsQ0FBQTNJLGFBQUEsR0FBQWlCLENBQUEsV0FBQXdDLE1BQU0sQ0FBQ1AsSUFBSSxNQUFBbEQsYUFBQSxHQUFBaUIsQ0FBQSxXQUFJLEVBQUU7Y0FBQ2pCLGFBQUEsR0FBQUMsQ0FBQTtjQUN2QyxPQUFPeUksR0FBRztZQUNaLENBQUMsRUFBRSxDQUFDLENBQTBCLENBQUM7WUFDL0JyRixTQUFTLEVBQVRBO1VBQ0YsQ0FBQztVQUFDckQsYUFBQSxHQUFBQyxDQUFBO1VBRUYsSUFBSW9CLFFBQVEsRUFBRTtZQUFBckIsYUFBQSxHQUFBaUIsQ0FBQTtZQUFBakIsYUFBQSxHQUFBQyxDQUFBO1lBQ1osSUFBSSxDQUFDK0UsUUFBUSxDQUFDdEQsUUFBUSxFQUFFOEcsYUFBYSxFQUFFakgsWUFBWSxDQUFDO1VBQ3RELENBQUM7WUFBQXZCLGFBQUEsR0FBQWlCLENBQUE7VUFBQTtVQUFBakIsYUFBQSxHQUFBQyxDQUFBO1VBRUQsT0FBT3VJLGFBQWE7UUFFdEIsQ0FBQyxDQUFDLE9BQU9yRixLQUFLLEVBQUU7VUFBQW5ELGFBQUEsR0FBQUMsQ0FBQTtVQUNkTCxrQkFBa0IsQ0FBQ3FGLGtCQUFrQixDQUFDLGVBQWUsRUFBRTlCLEtBQWMsQ0FBQztVQUFDbkQsYUFBQSxHQUFBQyxDQUFBO1VBQ3ZFLE1BQU1rRCxLQUFLO1FBQ2I7TUFDRixDQUFDO01BQUEsU0FoREt5RixhQUFhQSxDQUFBQyxHQUFBLEVBQUFDLEdBQUE7UUFBQSxPQUFBakIsY0FBQSxDQUFBekMsS0FBQSxPQUFBdEUsU0FBQTtNQUFBO01BQUEsT0FBYjhILGFBQWE7SUFBQTtFQUFBO0lBQUFwSSxHQUFBO0lBQUFDLEtBQUEsRUFxRG5CLFNBQVFrQixZQUFZQSxDQUFDbkIsR0FBVyxFQUFFdUksR0FBVyxFQUFXO01BQUEvSSxhQUFBLEdBQUFrQixDQUFBO01BQ3RELElBQU04SCxNQUFNLElBQUFoSixhQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNGLFVBQVUsQ0FBQ2tKLEdBQUcsQ0FBQ3pJLEdBQUcsQ0FBQztNQUFDUixhQUFBLEdBQUFDLENBQUE7TUFDeEMsSUFBSSxDQUFDK0ksTUFBTSxFQUFFO1FBQUFoSixhQUFBLEdBQUFpQixDQUFBO1FBQUFqQixhQUFBLEdBQUFDLENBQUE7UUFBQSxPQUFPLEtBQUs7TUFBQSxDQUFDO1FBQUFELGFBQUEsR0FBQWlCLENBQUE7TUFBQTtNQUFBakIsYUFBQSxHQUFBQyxDQUFBO01BQzFCLE9BQU82QixJQUFJLENBQUNDLEdBQUcsQ0FBQyxDQUFDLEdBQUdpSCxNQUFNLENBQUNFLFNBQVMsR0FBR0gsR0FBRztJQUM1QztFQUFDO0lBQUF2SSxHQUFBO0lBQUFDLEtBQUEsRUFFRCxTQUFRbUIsWUFBWUEsQ0FBQ3BCLEdBQVcsRUFBTztNQUFBUixhQUFBLEdBQUFrQixDQUFBO01BQ3JDLElBQU04SCxNQUFNLElBQUFoSixhQUFBLEdBQUFDLENBQUEsUUFBRyxJQUFJLENBQUNGLFVBQVUsQ0FBQ2tKLEdBQUcsQ0FBQ3pJLEdBQUcsQ0FBQztNQUFDUixhQUFBLEdBQUFDLENBQUE7TUFDeEMsT0FBTyxDQUFBRCxhQUFBLEdBQUFpQixDQUFBLFdBQUErSCxNQUFNLG9CQUFOQSxNQUFNLENBQUU5RixJQUFJLE1BQUFsRCxhQUFBLEdBQUFpQixDQUFBLFdBQUksSUFBSTtJQUM3QjtFQUFDO0lBQUFULEdBQUE7SUFBQUMsS0FBQSxFQUVELFNBQVF1RSxRQUFRQSxDQUFDeEUsR0FBVyxFQUFFMEMsSUFBUyxFQUFFNkYsR0FBVyxFQUFRO01BQUEvSSxhQUFBLEdBQUFrQixDQUFBO01BQUFsQixhQUFBLEdBQUFDLENBQUE7TUFDMUQsSUFBSSxDQUFDRixVQUFVLENBQUNvSixHQUFHLENBQUMzSSxHQUFHLEVBQUU7UUFDdkIwQyxJQUFJLEVBQUpBLElBQUk7UUFDSmdHLFNBQVMsRUFBRXBILElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7UUFDckJnSCxHQUFHLEVBQUhBO01BQ0YsQ0FBQyxDQUFDO01BQUMvSSxhQUFBLEdBQUFDLENBQUE7TUFHSCxJQUFJLElBQUksQ0FBQ0YsVUFBVSxDQUFDcUosSUFBSSxHQUFHLEdBQUcsRUFBRTtRQUFBcEosYUFBQSxHQUFBaUIsQ0FBQTtRQUFBakIsYUFBQSxHQUFBQyxDQUFBO1FBQzlCLElBQUksQ0FBQ29KLFlBQVksQ0FBQyxDQUFDO01BQ3JCLENBQUM7UUFBQXJKLGFBQUEsR0FBQWlCLENBQUE7TUFBQTtJQUNIO0VBQUM7SUFBQVQsR0FBQTtJQUFBQyxLQUFBLEVBRUQsU0FBUTRJLFlBQVlBLENBQUEsRUFBUztNQUFBckosYUFBQSxHQUFBa0IsQ0FBQTtNQUMzQixJQUFNYSxHQUFHLElBQUEvQixhQUFBLEdBQUFDLENBQUEsUUFBRzZCLElBQUksQ0FBQ0MsR0FBRyxDQUFDLENBQUM7TUFBQy9CLGFBQUEsR0FBQUMsQ0FBQTtNQUN2QixTQUFBcUosS0FBQSxJQUEyQixJQUFJLENBQUN2SixVQUFVLENBQUN3SixPQUFPLENBQUMsQ0FBQyxFQUFFO1FBQUEsSUFBQUMsS0FBQSxHQUFBdkQsY0FBQSxDQUFBcUQsS0FBQTtRQUFBLElBQTFDOUksR0FBRyxHQUFBZ0osS0FBQTtRQUFBLElBQUUvSSxLQUFLLEdBQUErSSxLQUFBO1FBQUF4SixhQUFBLEdBQUFDLENBQUE7UUFDcEIsSUFBSThCLEdBQUcsR0FBR3RCLEtBQUssQ0FBQ3lJLFNBQVMsR0FBR3pJLEtBQUssQ0FBQ3NJLEdBQUcsRUFBRTtVQUFBL0ksYUFBQSxHQUFBaUIsQ0FBQTtVQUFBakIsYUFBQSxHQUFBQyxDQUFBO1VBQ3JDLElBQUksQ0FBQ0YsVUFBVSxDQUFDMEosTUFBTSxDQUFDakosR0FBRyxDQUFDO1FBQzdCLENBQUM7VUFBQVIsYUFBQSxHQUFBaUIsQ0FBQTtRQUFBO01BQ0g7SUFDRjtFQUFDO0lBQUFULEdBQUE7SUFBQUMsS0FBQSxFQUVELFNBQVFrRixrQkFBa0JBLENBQUNMLFNBQW9DLEVBQVE7TUFBQXRGLGFBQUEsR0FBQWtCLENBQUE7TUFDckUsSUFBTWEsR0FBRyxJQUFBL0IsYUFBQSxHQUFBQyxDQUFBLFFBQUcsSUFBSTZCLElBQUksQ0FBQyxDQUFDO01BQUM5QixhQUFBLEdBQUFDLENBQUE7TUFDdkIsUUFBUXFGLFNBQVM7UUFDZixLQUFLLE1BQU07VUFBQXRGLGFBQUEsR0FBQWlCLENBQUE7VUFBQWpCLGFBQUEsR0FBQUMsQ0FBQTtVQUNULE9BQU8sSUFBSTZCLElBQUksQ0FBQ0MsR0FBRyxDQUFDMkgsT0FBTyxDQUFDLENBQUMsR0FBRyxDQUFDLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsSUFBSSxDQUFDO1FBQzFELEtBQUssT0FBTztVQUFBMUosYUFBQSxHQUFBaUIsQ0FBQTtVQUFBakIsYUFBQSxHQUFBQyxDQUFBO1VBQ1YsT0FBTyxJQUFJNkIsSUFBSSxDQUFDQyxHQUFHLENBQUMySCxPQUFPLENBQUMsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUM7UUFDM0QsS0FBSyxNQUFNO1VBQUExSixhQUFBLEdBQUFpQixDQUFBO1VBQUFqQixhQUFBLEdBQUFDLENBQUE7VUFDVCxPQUFPLElBQUk2QixJQUFJLENBQUNDLEdBQUcsQ0FBQzJILE9BQU8sQ0FBQyxDQUFDLEdBQUcsR0FBRyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLElBQUksQ0FBQztRQUM1RDtVQUFBMUosYUFBQSxHQUFBaUIsQ0FBQTtVQUFBakIsYUFBQSxHQUFBQyxDQUFBO1VBQ0UsT0FBTyxJQUFJNkIsSUFBSSxDQUFDQyxHQUFHLENBQUMySCxPQUFPLENBQUMsQ0FBQyxHQUFHLEVBQUUsR0FBRyxFQUFFLEdBQUcsRUFBRSxHQUFHLEVBQUUsR0FBRyxJQUFJLENBQUM7TUFDN0Q7SUFDRjtFQUFDO0lBQUFsSixHQUFBO0lBQUFDLEtBQUEsRUFLRCxTQUFBa0osYUFBYUEsQ0FBQSxFQUFHO01BQUEzSixhQUFBLEdBQUFrQixDQUFBO01BQUFsQixhQUFBLEdBQUFDLENBQUE7TUFDZCxPQUFPO1FBQ0xtSixJQUFJLEVBQUUsSUFBSSxDQUFDckosVUFBVSxDQUFDcUosSUFBSTtRQUMxQlEsT0FBTyxFQUFFLENBQUE1SixhQUFBLEdBQUFpQixDQUFBLFdBQUFyQixrQkFBa0IsQ0FBQ2lLLGVBQWUsb0JBQWxDakssa0JBQWtCLENBQUNpSyxlQUFlLENBQUcsQ0FBQyxNQUFBN0osYUFBQSxHQUFBaUIsQ0FBQSxXQUFJLENBQUM7UUFDcERzSSxPQUFPLEVBQUVPLEtBQUssQ0FBQ3hILElBQUksQ0FBQyxJQUFJLENBQUN2QyxVQUFVLENBQUNnSyxJQUFJLENBQUMsQ0FBQztNQUM1QyxDQUFDO0lBQ0g7RUFBQztJQUFBdkosR0FBQTtJQUFBQyxLQUFBLEVBS0QsU0FBQXVKLFVBQVVBLENBQUEsRUFBUztNQUFBaEssYUFBQSxHQUFBa0IsQ0FBQTtNQUFBbEIsYUFBQSxHQUFBQyxDQUFBO01BQ2pCLElBQUksQ0FBQ0YsVUFBVSxDQUFDa0ssS0FBSyxDQUFDLENBQUM7SUFDekI7RUFBQztBQUFBO0FBSUgsT0FBTyxJQUFNQyx3QkFBd0IsSUFBQWxLLGFBQUEsR0FBQUMsQ0FBQSxTQUFHLElBQUlKLHdCQUF3QixDQUFDLENBQUM7QUFDdEUsZUFBZXFLLHdCQUF3QiIsImlnbm9yZUxpc3QiOltdfQ==