{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_objectSpread2", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_createElement", "_AssetRegistry", "_preprocess", "_ImageLoader", "_PixelRatio", "_StyleSheet", "_TextAncestorContext", "_View", "_warnOnce", "_excluded", "ERRORED", "LOADED", "LOADING", "IDLE", "_filterId", "svgDataUriPattern", "createTintColorSVG", "tintColor", "id", "createElement", "style", "position", "height", "visibility", "width", "suppressHydrationWarning", "floodColor", "key", "in2", "operator", "extractNonStandardStyleProps", "blurRadius", "filterId", "tintColorProp", "flatStyle", "flatten", "filter", "resizeMode", "shadowOffset", "warnOnce", "filters", "_filter", "push", "shadowString", "createBoxShadowValue", "length", "join", "resolveAssetDimensions", "source", "_getAssetByID", "getAssetByID", "_height", "_width", "Array", "isArray", "_height2", "_width2", "resolveAssetUri", "uri", "asset", "Error", "scale", "scales", "preferredScale", "get", "reduce", "prev", "curr", "Math", "abs", "scaleSuffix", "httpServerLocation", "name", "type", "match", "prefix", "svg", "encodedSvg", "encodeURIComponent", "Image", "forwardRef", "props", "ref", "_a<PERSON><PERSON><PERSON><PERSON>", "accessibilityLabel", "defaultSource", "draggable", "onError", "onLayout", "onLoad", "onLoadEnd", "onLoadStart", "pointerEvents", "rest", "aria<PERSON><PERSON><PERSON>", "process", "env", "NODE_ENV", "children", "_React$useState", "useState", "isLoaded", "has", "state", "updateState", "_React$useState2", "layout", "updateLayout", "hasTextAncestor", "useContext", "hiddenImageRef", "useRef", "filterRef", "requestRef", "shouldDisplaySource", "_extractNonStandardSt", "current", "_resizeMode", "_tintColor", "selectedSource", "displayImageUri", "imageSizeStyle", "backgroundImage", "backgroundSize", "getBackgroundSize", "hiddenImage", "alt", "styles", "accessibilityImage$raw", "src", "_hiddenImageRef$curre", "naturalHeight", "naturalWidth", "_height3", "_width3", "scaleFactor", "min", "x", "ceil", "y", "handleLayout", "e", "_layout", "nativeEvent", "useEffect", "abortPendingRequest", "load", "error", "abort", "root", "inline", "undo", "boxShadow", "image", "resizeModeStyles", "displayName", "ImageWithStatics", "getSize", "success", "failure", "prefetch", "queryCache", "uris", "create", "flexBasis", "overflow", "zIndex", "display", "shadowColor", "shadowOpacity", "shadowRadius", "overlayColor", "absoluteFillObject", "backgroundColor", "backgroundPosition", "backgroundRepeat", "opacity", "center", "contain", "cover", "none", "repeat", "stretch", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _createElement = _interopRequireDefault(require(\"../createElement\"));\nvar _AssetRegistry = require(\"../../modules/AssetRegistry\");\nvar _preprocess = require(\"../StyleSheet/preprocess\");\nvar _ImageLoader = _interopRequireDefault(require(\"../../modules/ImageLoader\"));\nvar _PixelRatio = _interopRequireDefault(require(\"../PixelRatio\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _TextAncestorContext = _interopRequireDefault(require(\"../Text/TextAncestorContext\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _warnOnce = require(\"../../modules/warnOnce\");\nvar _excluded = [\"aria-label\", \"accessibilityLabel\", \"blurRadius\", \"defaultSource\", \"draggable\", \"onError\", \"onLayout\", \"onLoad\", \"onLoadEnd\", \"onLoadStart\", \"pointerEvents\", \"source\", \"style\"];\nvar ERRORED = 'ERRORED';\nvar LOADED = 'LOADED';\nvar LOADING = 'LOADING';\nvar IDLE = 'IDLE';\nvar _filterId = 0;\nvar svgDataUriPattern = /^(data:image\\/svg\\+xml;utf8,)(.*)/;\nfunction createTintColorSVG(tintColor, id) {\n  return tintColor && id != null ? /*#__PURE__*/React.createElement(\"svg\", {\n    style: {\n      position: 'absolute',\n      height: 0,\n      visibility: 'hidden',\n      width: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"defs\", null, /*#__PURE__*/React.createElement(\"filter\", {\n    id: \"tint-\" + id,\n    suppressHydrationWarning: true\n  }, /*#__PURE__*/React.createElement(\"feFlood\", {\n    floodColor: \"\" + tintColor,\n    key: tintColor\n  }), /*#__PURE__*/React.createElement(\"feComposite\", {\n    in2: \"SourceAlpha\",\n    operator: \"in\"\n  })))) : null;\n}\nfunction extractNonStandardStyleProps(style, blurRadius, filterId, tintColorProp) {\n  var flatStyle = _StyleSheet.default.flatten(style);\n  var filter = flatStyle.filter,\n    resizeMode = flatStyle.resizeMode,\n    shadowOffset = flatStyle.shadowOffset,\n    tintColor = flatStyle.tintColor;\n  if (flatStyle.resizeMode) {\n    (0, _warnOnce.warnOnce)('Image.style.resizeMode', 'Image: style.resizeMode is deprecated. Please use props.resizeMode.');\n  }\n  if (flatStyle.tintColor) {\n    (0, _warnOnce.warnOnce)('Image.style.tintColor', 'Image: style.tintColor is deprecated. Please use props.tintColor.');\n  }\n\n  // Add CSS filters\n  // React Native exposes these features as props and proprietary styles\n  var filters = [];\n  var _filter = null;\n  if (filter) {\n    filters.push(filter);\n  }\n  if (blurRadius) {\n    filters.push(\"blur(\" + blurRadius + \"px)\");\n  }\n  if (shadowOffset) {\n    var shadowString = (0, _preprocess.createBoxShadowValue)(flatStyle);\n    if (shadowString) {\n      filters.push(\"drop-shadow(\" + shadowString + \")\");\n    }\n  }\n  if ((tintColorProp || tintColor) && filterId != null) {\n    filters.push(\"url(#tint-\" + filterId + \")\");\n  }\n  if (filters.length > 0) {\n    _filter = filters.join(' ');\n  }\n  return [resizeMode, _filter, tintColor];\n}\nfunction resolveAssetDimensions(source) {\n  if (typeof source === 'number') {\n    var _getAssetByID = (0, _AssetRegistry.getAssetByID)(source),\n      _height = _getAssetByID.height,\n      _width = _getAssetByID.width;\n    return {\n      height: _height,\n      width: _width\n    };\n  } else if (source != null && !Array.isArray(source) && typeof source === 'object') {\n    var _height2 = source.height,\n      _width2 = source.width;\n    return {\n      height: _height2,\n      width: _width2\n    };\n  }\n}\nfunction resolveAssetUri(source) {\n  var uri = null;\n  if (typeof source === 'number') {\n    // get the URI from the packager\n    var asset = (0, _AssetRegistry.getAssetByID)(source);\n    if (asset == null) {\n      throw new Error(\"Image: asset with ID \\\"\" + source + \"\\\" could not be found. Please check the image source or packager.\");\n    }\n    var scale = asset.scales[0];\n    if (asset.scales.length > 1) {\n      var preferredScale = _PixelRatio.default.get();\n      // Get the scale which is closest to the preferred scale\n      scale = asset.scales.reduce((prev, curr) => Math.abs(curr - preferredScale) < Math.abs(prev - preferredScale) ? curr : prev);\n    }\n    var scaleSuffix = scale !== 1 ? \"@\" + scale + \"x\" : '';\n    uri = asset ? asset.httpServerLocation + \"/\" + asset.name + scaleSuffix + \".\" + asset.type : '';\n  } else if (typeof source === 'string') {\n    uri = source;\n  } else if (source && typeof source.uri === 'string') {\n    uri = source.uri;\n  }\n  if (uri) {\n    var match = uri.match(svgDataUriPattern);\n    // inline SVG markup may contain characters (e.g., #, \") that need to be escaped\n    if (match) {\n      var prefix = match[1],\n        svg = match[2];\n      var encodedSvg = encodeURIComponent(svg);\n      return \"\" + prefix + encodedSvg;\n    }\n  }\n  return uri;\n}\nvar Image = /*#__PURE__*/React.forwardRef((props, ref) => {\n  var _ariaLabel = props['aria-label'],\n    accessibilityLabel = props.accessibilityLabel,\n    blurRadius = props.blurRadius,\n    defaultSource = props.defaultSource,\n    draggable = props.draggable,\n    onError = props.onError,\n    onLayout = props.onLayout,\n    onLoad = props.onLoad,\n    onLoadEnd = props.onLoadEnd,\n    onLoadStart = props.onLoadStart,\n    pointerEvents = props.pointerEvents,\n    source = props.source,\n    style = props.style,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  var ariaLabel = _ariaLabel || accessibilityLabel;\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.children) {\n      throw new Error('The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.');\n    }\n  }\n  var _React$useState = React.useState(() => {\n      var uri = resolveAssetUri(source);\n      if (uri != null) {\n        var isLoaded = _ImageLoader.default.has(uri);\n        if (isLoaded) {\n          return LOADED;\n        }\n      }\n      return IDLE;\n    }),\n    state = _React$useState[0],\n    updateState = _React$useState[1];\n  var _React$useState2 = React.useState({}),\n    layout = _React$useState2[0],\n    updateLayout = _React$useState2[1];\n  var hasTextAncestor = React.useContext(_TextAncestorContext.default);\n  var hiddenImageRef = React.useRef(null);\n  var filterRef = React.useRef(_filterId++);\n  var requestRef = React.useRef(null);\n  var shouldDisplaySource = state === LOADED || state === LOADING && defaultSource == null;\n  var _extractNonStandardSt = extractNonStandardStyleProps(style, blurRadius, filterRef.current, props.tintColor),\n    _resizeMode = _extractNonStandardSt[0],\n    filter = _extractNonStandardSt[1],\n    _tintColor = _extractNonStandardSt[2];\n  var resizeMode = props.resizeMode || _resizeMode || 'cover';\n  var tintColor = props.tintColor || _tintColor;\n  var selectedSource = shouldDisplaySource ? source : defaultSource;\n  var displayImageUri = resolveAssetUri(selectedSource);\n  var imageSizeStyle = resolveAssetDimensions(selectedSource);\n  var backgroundImage = displayImageUri ? \"url(\\\"\" + displayImageUri + \"\\\")\" : null;\n  var backgroundSize = getBackgroundSize();\n\n  // Accessibility image allows users to trigger the browser's image context menu\n  var hiddenImage = displayImageUri ? (0, _createElement.default)('img', {\n    alt: ariaLabel || '',\n    style: styles.accessibilityImage$raw,\n    draggable: draggable || false,\n    ref: hiddenImageRef,\n    src: displayImageUri\n  }) : null;\n  function getBackgroundSize() {\n    if (hiddenImageRef.current != null && (resizeMode === 'center' || resizeMode === 'repeat')) {\n      var _hiddenImageRef$curre = hiddenImageRef.current,\n        naturalHeight = _hiddenImageRef$curre.naturalHeight,\n        naturalWidth = _hiddenImageRef$curre.naturalWidth;\n      var _height3 = layout.height,\n        _width3 = layout.width;\n      if (naturalHeight && naturalWidth && _height3 && _width3) {\n        var scaleFactor = Math.min(1, _width3 / naturalWidth, _height3 / naturalHeight);\n        var x = Math.ceil(scaleFactor * naturalWidth);\n        var y = Math.ceil(scaleFactor * naturalHeight);\n        return x + \"px \" + y + \"px\";\n      }\n    }\n  }\n  function handleLayout(e) {\n    if (resizeMode === 'center' || resizeMode === 'repeat' || onLayout) {\n      var _layout = e.nativeEvent.layout;\n      onLayout && onLayout(e);\n      updateLayout(_layout);\n    }\n  }\n\n  // Image loading\n  var uri = resolveAssetUri(source);\n  React.useEffect(() => {\n    abortPendingRequest();\n    if (uri != null) {\n      updateState(LOADING);\n      if (onLoadStart) {\n        onLoadStart();\n      }\n      requestRef.current = _ImageLoader.default.load(uri, function load(e) {\n        updateState(LOADED);\n        if (onLoad) {\n          onLoad(e);\n        }\n        if (onLoadEnd) {\n          onLoadEnd();\n        }\n      }, function error() {\n        updateState(ERRORED);\n        if (onError) {\n          onError({\n            nativeEvent: {\n              error: \"Failed to load resource \" + uri\n            }\n          });\n        }\n        if (onLoadEnd) {\n          onLoadEnd();\n        }\n      });\n    }\n    function abortPendingRequest() {\n      if (requestRef.current != null) {\n        _ImageLoader.default.abort(requestRef.current);\n        requestRef.current = null;\n      }\n    }\n    return abortPendingRequest;\n  }, [uri, requestRef, updateState, onError, onLoad, onLoadEnd, onLoadStart]);\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, {\n    \"aria-label\": ariaLabel,\n    onLayout: handleLayout,\n    pointerEvents: pointerEvents,\n    ref: ref,\n    style: [styles.root, hasTextAncestor && styles.inline, imageSizeStyle, style, styles.undo,\n    // TEMP: avoid deprecated shadow props regression\n    // until Image refactored to use createElement.\n    {\n      boxShadow: null\n    }]\n  }), /*#__PURE__*/React.createElement(_View.default, {\n    style: [styles.image, resizeModeStyles[resizeMode], {\n      backgroundImage,\n      filter\n    }, backgroundSize != null && {\n      backgroundSize\n    }],\n    suppressHydrationWarning: true\n  }), hiddenImage, createTintColorSVG(tintColor, filterRef.current));\n});\nImage.displayName = 'Image';\n\n// $FlowIgnore: This is the correct type, but casting makes it unhappy since the variables aren't defined yet\nvar ImageWithStatics = Image;\nImageWithStatics.getSize = function (uri, success, failure) {\n  _ImageLoader.default.getSize(uri, success, failure);\n};\nImageWithStatics.prefetch = function (uri) {\n  return _ImageLoader.default.prefetch(uri);\n};\nImageWithStatics.queryCache = function (uris) {\n  return _ImageLoader.default.queryCache(uris);\n};\nvar styles = _StyleSheet.default.create({\n  root: {\n    flexBasis: 'auto',\n    overflow: 'hidden',\n    zIndex: 0\n  },\n  inline: {\n    display: 'inline-flex'\n  },\n  undo: {\n    // These styles are converted to CSS filters applied to the\n    // element displaying the background image.\n    blurRadius: null,\n    shadowColor: null,\n    shadowOpacity: null,\n    shadowOffset: null,\n    shadowRadius: null,\n    tintColor: null,\n    // These styles are not supported\n    overlayColor: null,\n    resizeMode: null\n  },\n  image: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _StyleSheet.default.absoluteFillObject), {}, {\n    backgroundColor: 'transparent',\n    backgroundPosition: 'center',\n    backgroundRepeat: 'no-repeat',\n    backgroundSize: 'cover',\n    height: '100%',\n    width: '100%',\n    zIndex: -1\n  }),\n  accessibilityImage$raw: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _StyleSheet.default.absoluteFillObject), {}, {\n    height: '100%',\n    opacity: 0,\n    width: '100%',\n    zIndex: -1\n  })\n});\nvar resizeModeStyles = _StyleSheet.default.create({\n  center: {\n    backgroundSize: 'auto'\n  },\n  contain: {\n    backgroundSize: 'contain'\n  },\n  cover: {\n    backgroundSize: 'cover'\n  },\n  none: {\n    backgroundPosition: '0',\n    backgroundSize: 'auto'\n  },\n  repeat: {\n    backgroundPosition: '0',\n    backgroundRepeat: 'repeat',\n    backgroundSize: 'auto'\n  },\n  stretch: {\n    backgroundSize: '100% 100%'\n  }\n});\nvar _default = exports.default = ImageWithStatics;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,cAAc,GAAGN,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIM,SAAS,GAAGP,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIO,8BAA8B,GAAGR,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIQ,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,cAAc,GAAGV,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AACxE,IAAIU,cAAc,GAAGV,OAAO,8BAA8B,CAAC;AAC3D,IAAIW,WAAW,GAAGX,OAAO,2BAA2B,CAAC;AACrD,IAAIY,YAAY,GAAGb,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAC/E,IAAIa,WAAW,GAAGd,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIc,WAAW,GAAGf,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIe,oBAAoB,GAAGhB,sBAAsB,CAACC,OAAO,8BAA8B,CAAC,CAAC;AACzF,IAAIgB,KAAK,GAAGjB,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIiB,SAAS,GAAGjB,OAAO,yBAAyB,CAAC;AACjD,IAAIkB,SAAS,GAAG,CAAC,YAAY,EAAE,oBAAoB,EAAE,YAAY,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,OAAO,CAAC;AACjM,IAAIC,OAAO,GAAG,SAAS;AACvB,IAAIC,MAAM,GAAG,QAAQ;AACrB,IAAIC,OAAO,GAAG,SAAS;AACvB,IAAIC,IAAI,GAAG,MAAM;AACjB,IAAIC,SAAS,GAAG,CAAC;AACjB,IAAIC,iBAAiB,GAAG,mCAAmC;AAC3D,SAASC,kBAAkBA,CAACC,SAAS,EAAEC,EAAE,EAAE;EACzC,OAAOD,SAAS,IAAIC,EAAE,IAAI,IAAI,GAAgBnB,KAAK,CAACoB,aAAa,CAAC,KAAK,EAAE;IACvEC,KAAK,EAAE;MACLC,QAAQ,EAAE,UAAU;MACpBC,MAAM,EAAE,CAAC;MACTC,UAAU,EAAE,QAAQ;MACpBC,KAAK,EAAE;IACT;EACF,CAAC,EAAezB,KAAK,CAACoB,aAAa,CAAC,MAAM,EAAE,IAAI,EAAepB,KAAK,CAACoB,aAAa,CAAC,QAAQ,EAAE;IAC3FD,EAAE,EAAE,OAAO,GAAGA,EAAE;IAChBO,wBAAwB,EAAE;EAC5B,CAAC,EAAe1B,KAAK,CAACoB,aAAa,CAAC,SAAS,EAAE;IAC7CO,UAAU,EAAE,EAAE,GAAGT,SAAS;IAC1BU,GAAG,EAAEV;EACP,CAAC,CAAC,EAAelB,KAAK,CAACoB,aAAa,CAAC,aAAa,EAAE;IAClDS,GAAG,EAAE,aAAa;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;AACd;AACA,SAASC,4BAA4BA,CAACV,KAAK,EAAEW,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;EAChF,IAAIC,SAAS,GAAG7B,WAAW,CAACb,OAAO,CAAC2C,OAAO,CAACf,KAAK,CAAC;EAClD,IAAIgB,MAAM,GAAGF,SAAS,CAACE,MAAM;IAC3BC,UAAU,GAAGH,SAAS,CAACG,UAAU;IACjCC,YAAY,GAAGJ,SAAS,CAACI,YAAY;IACrCrB,SAAS,GAAGiB,SAAS,CAACjB,SAAS;EACjC,IAAIiB,SAAS,CAACG,UAAU,EAAE;IACxB,CAAC,CAAC,EAAE7B,SAAS,CAAC+B,QAAQ,EAAE,wBAAwB,EAAE,qEAAqE,CAAC;EAC1H;EACA,IAAIL,SAAS,CAACjB,SAAS,EAAE;IACvB,CAAC,CAAC,EAAET,SAAS,CAAC+B,QAAQ,EAAE,uBAAuB,EAAE,mEAAmE,CAAC;EACvH;EAIA,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,OAAO,GAAG,IAAI;EAClB,IAAIL,MAAM,EAAE;IACVI,OAAO,CAACE,IAAI,CAACN,MAAM,CAAC;EACtB;EACA,IAAIL,UAAU,EAAE;IACdS,OAAO,CAACE,IAAI,CAAC,OAAO,GAAGX,UAAU,GAAG,KAAK,CAAC;EAC5C;EACA,IAAIO,YAAY,EAAE;IAChB,IAAIK,YAAY,GAAG,CAAC,CAAC,EAAEzC,WAAW,CAAC0C,oBAAoB,EAAEV,SAAS,CAAC;IACnE,IAAIS,YAAY,EAAE;MAChBH,OAAO,CAACE,IAAI,CAAC,cAAc,GAAGC,YAAY,GAAG,GAAG,CAAC;IACnD;EACF;EACA,IAAI,CAACV,aAAa,IAAIhB,SAAS,KAAKe,QAAQ,IAAI,IAAI,EAAE;IACpDQ,OAAO,CAACE,IAAI,CAAC,YAAY,GAAGV,QAAQ,GAAG,GAAG,CAAC;EAC7C;EACA,IAAIQ,OAAO,CAACK,MAAM,GAAG,CAAC,EAAE;IACtBJ,OAAO,GAAGD,OAAO,CAACM,IAAI,CAAC,GAAG,CAAC;EAC7B;EACA,OAAO,CAACT,UAAU,EAAEI,OAAO,EAAExB,SAAS,CAAC;AACzC;AACA,SAAS8B,sBAAsBA,CAACC,MAAM,EAAE;EACtC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAC9B,IAAIC,aAAa,GAAG,CAAC,CAAC,EAAEhD,cAAc,CAACiD,YAAY,EAAEF,MAAM,CAAC;MAC1DG,OAAO,GAAGF,aAAa,CAAC3B,MAAM;MAC9B8B,MAAM,GAAGH,aAAa,CAACzB,KAAK;IAC9B,OAAO;MACLF,MAAM,EAAE6B,OAAO;MACf3B,KAAK,EAAE4B;IACT,CAAC;EACH,CAAC,MAAM,IAAIJ,MAAM,IAAI,IAAI,IAAI,CAACK,KAAK,CAACC,OAAO,CAACN,MAAM,CAAC,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IACjF,IAAIO,QAAQ,GAAGP,MAAM,CAAC1B,MAAM;MAC1BkC,OAAO,GAAGR,MAAM,CAACxB,KAAK;IACxB,OAAO;MACLF,MAAM,EAAEiC,QAAQ;MAChB/B,KAAK,EAAEgC;IACT,CAAC;EACH;AACF;AACA,SAASC,eAAeA,CAACT,MAAM,EAAE;EAC/B,IAAIU,GAAG,GAAG,IAAI;EACd,IAAI,OAAOV,MAAM,KAAK,QAAQ,EAAE;IAE9B,IAAIW,KAAK,GAAG,CAAC,CAAC,EAAE1D,cAAc,CAACiD,YAAY,EAAEF,MAAM,CAAC;IACpD,IAAIW,KAAK,IAAI,IAAI,EAAE;MACjB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,GAAGZ,MAAM,GAAG,mEAAmE,CAAC;IAC3H;IACA,IAAIa,KAAK,GAAGF,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC;IAC3B,IAAIH,KAAK,CAACG,MAAM,CAACjB,MAAM,GAAG,CAAC,EAAE;MAC3B,IAAIkB,cAAc,GAAG3D,WAAW,CAACZ,OAAO,CAACwE,GAAG,CAAC,CAAC;MAE9CH,KAAK,GAAGF,KAAK,CAACG,MAAM,CAACG,MAAM,CAAC,UAACC,IAAI,EAAEC,IAAI;QAAA,OAAKC,IAAI,CAACC,GAAG,CAACF,IAAI,GAAGJ,cAAc,CAAC,GAAGK,IAAI,CAACC,GAAG,CAACH,IAAI,GAAGH,cAAc,CAAC,GAAGI,IAAI,GAAGD,IAAI;MAAA,EAAC;IAC9H;IACA,IAAII,WAAW,GAAGT,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,KAAK,GAAG,GAAG,GAAG,EAAE;IACtDH,GAAG,GAAGC,KAAK,GAAGA,KAAK,CAACY,kBAAkB,GAAG,GAAG,GAAGZ,KAAK,CAACa,IAAI,GAAGF,WAAW,GAAG,GAAG,GAAGX,KAAK,CAACc,IAAI,GAAG,EAAE;EACjG,CAAC,MAAM,IAAI,OAAOzB,MAAM,KAAK,QAAQ,EAAE;IACrCU,GAAG,GAAGV,MAAM;EACd,CAAC,MAAM,IAAIA,MAAM,IAAI,OAAOA,MAAM,CAACU,GAAG,KAAK,QAAQ,EAAE;IACnDA,GAAG,GAAGV,MAAM,CAACU,GAAG;EAClB;EACA,IAAIA,GAAG,EAAE;IACP,IAAIgB,KAAK,GAAGhB,GAAG,CAACgB,KAAK,CAAC3D,iBAAiB,CAAC;IAExC,IAAI2D,KAAK,EAAE;MACT,IAAIC,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;QACnBE,GAAG,GAAGF,KAAK,CAAC,CAAC,CAAC;MAChB,IAAIG,UAAU,GAAGC,kBAAkB,CAACF,GAAG,CAAC;MACxC,OAAO,EAAE,GAAGD,MAAM,GAAGE,UAAU;IACjC;EACF;EACA,OAAOnB,GAAG;AACZ;AACA,IAAIqB,KAAK,GAAgBhF,KAAK,CAACiF,UAAU,CAAC,UAACC,KAAK,EAAEC,GAAG,EAAK;EACxD,IAAIC,UAAU,GAAGF,KAAK,CAAC,YAAY,CAAC;IAClCG,kBAAkB,GAAGH,KAAK,CAACG,kBAAkB;IAC7CrD,UAAU,GAAGkD,KAAK,CAAClD,UAAU;IAC7BsD,aAAa,GAAGJ,KAAK,CAACI,aAAa;IACnCC,SAAS,GAAGL,KAAK,CAACK,SAAS;IAC3BC,OAAO,GAAGN,KAAK,CAACM,OAAO;IACvBC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,MAAM,GAAGR,KAAK,CAACQ,MAAM;IACrBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,WAAW,GAAGV,KAAK,CAACU,WAAW;IAC/BC,aAAa,GAAGX,KAAK,CAACW,aAAa;IACnC5C,MAAM,GAAGiC,KAAK,CAACjC,MAAM;IACrB5B,KAAK,GAAG6D,KAAK,CAAC7D,KAAK;IACnByE,IAAI,GAAG,CAAC,CAAC,EAAE/F,8BAA8B,CAACN,OAAO,EAAEyF,KAAK,EAAExE,SAAS,CAAC;EACtE,IAAIqF,SAAS,GAAGX,UAAU,IAAIC,kBAAkB;EAChD,IAAIW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAIhB,KAAK,CAACiB,QAAQ,EAAE;MAClB,MAAM,IAAItC,KAAK,CAAC,2KAA2K,CAAC;IAC9L;EACF;EACA,IAAIuC,eAAe,GAAGpG,KAAK,CAACqG,QAAQ,CAAC,YAAM;MACvC,IAAI1C,GAAG,GAAGD,eAAe,CAACT,MAAM,CAAC;MACjC,IAAIU,GAAG,IAAI,IAAI,EAAE;QACf,IAAI2C,QAAQ,GAAGlG,YAAY,CAACX,OAAO,CAAC8G,GAAG,CAAC5C,GAAG,CAAC;QAC5C,IAAI2C,QAAQ,EAAE;UACZ,OAAO1F,MAAM;QACf;MACF;MACA,OAAOE,IAAI;IACb,CAAC,CAAC;IACF0F,KAAK,GAAGJ,eAAe,CAAC,CAAC,CAAC;IAC1BK,WAAW,GAAGL,eAAe,CAAC,CAAC,CAAC;EAClC,IAAIM,gBAAgB,GAAG1G,KAAK,CAACqG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvCM,MAAM,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC5BE,YAAY,GAAGF,gBAAgB,CAAC,CAAC,CAAC;EACpC,IAAIG,eAAe,GAAG7G,KAAK,CAAC8G,UAAU,CAACvG,oBAAoB,CAACd,OAAO,CAAC;EACpE,IAAIsH,cAAc,GAAG/G,KAAK,CAACgH,MAAM,CAAC,IAAI,CAAC;EACvC,IAAIC,SAAS,GAAGjH,KAAK,CAACgH,MAAM,CAACjG,SAAS,EAAE,CAAC;EACzC,IAAImG,UAAU,GAAGlH,KAAK,CAACgH,MAAM,CAAC,IAAI,CAAC;EACnC,IAAIG,mBAAmB,GAAGX,KAAK,KAAK5F,MAAM,IAAI4F,KAAK,KAAK3F,OAAO,IAAIyE,aAAa,IAAI,IAAI;EACxF,IAAI8B,qBAAqB,GAAGrF,4BAA4B,CAACV,KAAK,EAAEW,UAAU,EAAEiF,SAAS,CAACI,OAAO,EAAEnC,KAAK,CAAChE,SAAS,CAAC;IAC7GoG,WAAW,GAAGF,qBAAqB,CAAC,CAAC,CAAC;IACtC/E,MAAM,GAAG+E,qBAAqB,CAAC,CAAC,CAAC;IACjCG,UAAU,GAAGH,qBAAqB,CAAC,CAAC,CAAC;EACvC,IAAI9E,UAAU,GAAG4C,KAAK,CAAC5C,UAAU,IAAIgF,WAAW,IAAI,OAAO;EAC3D,IAAIpG,SAAS,GAAGgE,KAAK,CAAChE,SAAS,IAAIqG,UAAU;EAC7C,IAAIC,cAAc,GAAGL,mBAAmB,GAAGlE,MAAM,GAAGqC,aAAa;EACjE,IAAImC,eAAe,GAAG/D,eAAe,CAAC8D,cAAc,CAAC;EACrD,IAAIE,cAAc,GAAG1E,sBAAsB,CAACwE,cAAc,CAAC;EAC3D,IAAIG,eAAe,GAAGF,eAAe,GAAG,QAAQ,GAAGA,eAAe,GAAG,KAAK,GAAG,IAAI;EACjF,IAAIG,cAAc,GAAGC,iBAAiB,CAAC,CAAC;EAGxC,IAAIC,WAAW,GAAGL,eAAe,GAAG,CAAC,CAAC,EAAExH,cAAc,CAACR,OAAO,EAAE,KAAK,EAAE;IACrEsI,GAAG,EAAEhC,SAAS,IAAI,EAAE;IACpB1E,KAAK,EAAE2G,MAAM,CAACC,sBAAsB;IACpC1C,SAAS,EAAEA,SAAS,IAAI,KAAK;IAC7BJ,GAAG,EAAE4B,cAAc;IACnBmB,GAAG,EAAET;EACP,CAAC,CAAC,GAAG,IAAI;EACT,SAASI,iBAAiBA,CAAA,EAAG;IAC3B,IAAId,cAAc,CAACM,OAAO,IAAI,IAAI,KAAK/E,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,QAAQ,CAAC,EAAE;MAC1F,IAAI6F,qBAAqB,GAAGpB,cAAc,CAACM,OAAO;QAChDe,aAAa,GAAGD,qBAAqB,CAACC,aAAa;QACnDC,YAAY,GAAGF,qBAAqB,CAACE,YAAY;MACnD,IAAIC,QAAQ,GAAG3B,MAAM,CAACpF,MAAM;QAC1BgH,OAAO,GAAG5B,MAAM,CAAClF,KAAK;MACxB,IAAI2G,aAAa,IAAIC,YAAY,IAAIC,QAAQ,IAAIC,OAAO,EAAE;QACxD,IAAIC,WAAW,GAAGnE,IAAI,CAACoE,GAAG,CAAC,CAAC,EAAEF,OAAO,GAAGF,YAAY,EAAEC,QAAQ,GAAGF,aAAa,CAAC;QAC/E,IAAIM,CAAC,GAAGrE,IAAI,CAACsE,IAAI,CAACH,WAAW,GAAGH,YAAY,CAAC;QAC7C,IAAIO,CAAC,GAAGvE,IAAI,CAACsE,IAAI,CAACH,WAAW,GAAGJ,aAAa,CAAC;QAC9C,OAAOM,CAAC,GAAG,KAAK,GAAGE,CAAC,GAAG,IAAI;MAC7B;IACF;EACF;EACA,SAASC,YAAYA,CAACC,CAAC,EAAE;IACvB,IAAIxG,UAAU,KAAK,QAAQ,IAAIA,UAAU,KAAK,QAAQ,IAAImD,QAAQ,EAAE;MAClE,IAAIsD,OAAO,GAAGD,CAAC,CAACE,WAAW,CAACrC,MAAM;MAClClB,QAAQ,IAAIA,QAAQ,CAACqD,CAAC,CAAC;MACvBlC,YAAY,CAACmC,OAAO,CAAC;IACvB;EACF;EAGA,IAAIpF,GAAG,GAAGD,eAAe,CAACT,MAAM,CAAC;EACjCjD,KAAK,CAACiJ,SAAS,CAAC,YAAM;IACpBC,mBAAmB,CAAC,CAAC;IACrB,IAAIvF,GAAG,IAAI,IAAI,EAAE;MACf8C,WAAW,CAAC5F,OAAO,CAAC;MACpB,IAAI+E,WAAW,EAAE;QACfA,WAAW,CAAC,CAAC;MACf;MACAsB,UAAU,CAACG,OAAO,GAAGjH,YAAY,CAACX,OAAO,CAAC0J,IAAI,CAACxF,GAAG,EAAE,SAASwF,IAAIA,CAACL,CAAC,EAAE;QACnErC,WAAW,CAAC7F,MAAM,CAAC;QACnB,IAAI8E,MAAM,EAAE;UACVA,MAAM,CAACoD,CAAC,CAAC;QACX;QACA,IAAInD,SAAS,EAAE;UACbA,SAAS,CAAC,CAAC;QACb;MACF,CAAC,EAAE,SAASyD,KAAKA,CAAA,EAAG;QAClB3C,WAAW,CAAC9F,OAAO,CAAC;QACpB,IAAI6E,OAAO,EAAE;UACXA,OAAO,CAAC;YACNwD,WAAW,EAAE;cACXI,KAAK,EAAE,0BAA0B,GAAGzF;YACtC;UACF,CAAC,CAAC;QACJ;QACA,IAAIgC,SAAS,EAAE;UACbA,SAAS,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ;IACA,SAASuD,mBAAmBA,CAAA,EAAG;MAC7B,IAAIhC,UAAU,CAACG,OAAO,IAAI,IAAI,EAAE;QAC9BjH,YAAY,CAACX,OAAO,CAAC4J,KAAK,CAACnC,UAAU,CAACG,OAAO,CAAC;QAC9CH,UAAU,CAACG,OAAO,GAAG,IAAI;MAC3B;IACF;IACA,OAAO6B,mBAAmB;EAC5B,CAAC,EAAE,CAACvF,GAAG,EAAEuD,UAAU,EAAET,WAAW,EAAEjB,OAAO,EAAEE,MAAM,EAAEC,SAAS,EAAEC,WAAW,CAAC,CAAC;EAC3E,OAAoB5F,KAAK,CAACoB,aAAa,CAACZ,KAAK,CAACf,OAAO,EAAE,CAAC,CAAC,EAAEK,SAAS,CAACL,OAAO,EAAE,CAAC,CAAC,EAAEqG,IAAI,EAAE;IACtF,YAAY,EAAEC,SAAS;IACvBN,QAAQ,EAAEoD,YAAY;IACtBhD,aAAa,EAAEA,aAAa;IAC5BV,GAAG,EAAEA,GAAG;IACR9D,KAAK,EAAE,CAAC2G,MAAM,CAACsB,IAAI,EAAEzC,eAAe,IAAImB,MAAM,CAACuB,MAAM,EAAE7B,cAAc,EAAErG,KAAK,EAAE2G,MAAM,CAACwB,IAAI,EAGzF;MACEC,SAAS,EAAE;IACb,CAAC;EACH,CAAC,CAAC,EAAezJ,KAAK,CAACoB,aAAa,CAACZ,KAAK,CAACf,OAAO,EAAE;IAClD4B,KAAK,EAAE,CAAC2G,MAAM,CAAC0B,KAAK,EAAEC,gBAAgB,CAACrH,UAAU,CAAC,EAAE;MAClDqF,eAAe,EAAfA,eAAe;MACftF,MAAM,EAANA;IACF,CAAC,EAAEuF,cAAc,IAAI,IAAI,IAAI;MAC3BA,cAAc,EAAdA;IACF,CAAC,CAAC;IACFlG,wBAAwB,EAAE;EAC5B,CAAC,CAAC,EAAEoG,WAAW,EAAE7G,kBAAkB,CAACC,SAAS,EAAE+F,SAAS,CAACI,OAAO,CAAC,CAAC;AACpE,CAAC,CAAC;AACFrC,KAAK,CAAC4E,WAAW,GAAG,OAAO;AAG3B,IAAIC,gBAAgB,GAAG7E,KAAK;AAC5B6E,gBAAgB,CAACC,OAAO,GAAG,UAAUnG,GAAG,EAAEoG,OAAO,EAAEC,OAAO,EAAE;EAC1D5J,YAAY,CAACX,OAAO,CAACqK,OAAO,CAACnG,GAAG,EAAEoG,OAAO,EAAEC,OAAO,CAAC;AACrD,CAAC;AACDH,gBAAgB,CAACI,QAAQ,GAAG,UAAUtG,GAAG,EAAE;EACzC,OAAOvD,YAAY,CAACX,OAAO,CAACwK,QAAQ,CAACtG,GAAG,CAAC;AAC3C,CAAC;AACDkG,gBAAgB,CAACK,UAAU,GAAG,UAAUC,IAAI,EAAE;EAC5C,OAAO/J,YAAY,CAACX,OAAO,CAACyK,UAAU,CAACC,IAAI,CAAC;AAC9C,CAAC;AACD,IAAInC,MAAM,GAAG1H,WAAW,CAACb,OAAO,CAAC2K,MAAM,CAAC;EACtCd,IAAI,EAAE;IACJe,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,MAAM,EAAE;EACV,CAAC;EACDhB,MAAM,EAAE;IACNiB,OAAO,EAAE;EACX,CAAC;EACDhB,IAAI,EAAE;IAGJxH,UAAU,EAAE,IAAI;IAChByI,WAAW,EAAE,IAAI;IACjBC,aAAa,EAAE,IAAI;IACnBnI,YAAY,EAAE,IAAI;IAClBoI,YAAY,EAAE,IAAI;IAClBzJ,SAAS,EAAE,IAAI;IAEf0J,YAAY,EAAE,IAAI;IAClBtI,UAAU,EAAE;EACd,CAAC;EACDoH,KAAK,EAAE,CAAC,CAAC,EAAE7J,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEI,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEa,WAAW,CAACb,OAAO,CAACoL,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE;IAC9GC,eAAe,EAAE,aAAa;IAC9BC,kBAAkB,EAAE,QAAQ;IAC5BC,gBAAgB,EAAE,WAAW;IAC7BpD,cAAc,EAAE,OAAO;IACvBrG,MAAM,EAAE,MAAM;IACdE,KAAK,EAAE,MAAM;IACb8I,MAAM,EAAE,CAAC;EACX,CAAC,CAAC;EACFtC,sBAAsB,EAAE,CAAC,CAAC,EAAEpI,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEI,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEa,WAAW,CAACb,OAAO,CAACoL,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE;IAC/HtJ,MAAM,EAAE,MAAM;IACd0J,OAAO,EAAE,CAAC;IACVxJ,KAAK,EAAE,MAAM;IACb8I,MAAM,EAAE,CAAC;EACX,CAAC;AACH,CAAC,CAAC;AACF,IAAIZ,gBAAgB,GAAGrJ,WAAW,CAACb,OAAO,CAAC2K,MAAM,CAAC;EAChDc,MAAM,EAAE;IACNtD,cAAc,EAAE;EAClB,CAAC;EACDuD,OAAO,EAAE;IACPvD,cAAc,EAAE;EAClB,CAAC;EACDwD,KAAK,EAAE;IACLxD,cAAc,EAAE;EAClB,CAAC;EACDyD,IAAI,EAAE;IACJN,kBAAkB,EAAE,GAAG;IACvBnD,cAAc,EAAE;EAClB,CAAC;EACD0D,MAAM,EAAE;IACNP,kBAAkB,EAAE,GAAG;IACvBC,gBAAgB,EAAE,QAAQ;IAC1BpD,cAAc,EAAE;EAClB,CAAC;EACD2D,OAAO,EAAE;IACP3D,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AACF,IAAI4D,QAAQ,GAAG7L,OAAO,CAACF,OAAO,GAAGoK,gBAAgB;AACjD4B,MAAM,CAAC9L,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}