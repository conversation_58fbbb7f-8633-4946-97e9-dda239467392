{"version": 3, "names": ["useState", "useEffect", "useCallback", "useMemo", "unifiedPerformanceMonitor", "useAIOptimization", "useEdgeOptimization", "useNativeOptimization", "usePerformanceDashboard", "initialConfig", "arguments", "length", "undefined", "cov_27rx7fc5md", "b", "f", "aiOptimization", "s", "edgeOptimization", "nativeOptimization", "_ref", "Object", "assign", "autoRefresh", "refreshInterval", "enableAlerts", "enableTrends", "enableInsights", "maxDataPoints", "_ref2", "_slicedToArray", "config", "setConfig", "_ref3", "isLoading", "lastUpdated", "unifiedMetrics", "insights", "alerts", "trends", "performance", "memory", "network", "phaseStatus", "phase1", "status", "score", "health", "phase2", "phase3a", "phase3b", "phase3c", "realTimeMetrics", "bundleSize", "loadTime", "renderTime", "memoryUsage", "cpuUsage", "networkLatency", "batteryDrain", "_ref4", "state", "setState", "refreshMetrics", "_asyncToGenerator", "prev", "getUnifiedMetrics", "getPerformanceInsights", "phases", "current", "now", "Date", "newTrends", "push", "timestamp", "value", "overall", "performanceScore", "realTime", "keys", "for<PERSON>ach", "key", "shift", "error", "console", "exportData", "format", "exportMonitoringData", "startProfiling", "_ref7", "<PERSON><PERSON><PERSON>", "sessionId", "startProfilingSession", "log", "_x", "apply", "<PERSON><PERSON><PERSON><PERSON>", "alertId", "map", "alert", "id", "resolved", "getPhaseDetails", "phase", "phaseData", "improvement", "baseline", "targetProgress", "target", "getOptimizationRecommendations", "optimizationOpportunities", "toggleAutoRefresh", "interval", "setInterval", "clearInterval", "summary", "_state$insights", "overallScore", "totalImprovement", "healthScore", "active<PERSON>lerts", "topInsight", "filter", "insight", "Math", "round", "actions"], "sources": ["usePerformanceDashboard.ts"], "sourcesContent": ["/**\n * Performance Dashboard Hook\n * \n * Real-time monitoring dashboard that provides comprehensive visibility\n * across all optimization phases with live metrics and insights.\n */\n\nimport { useState, useEffect, useCallback, useMemo } from 'react';\nimport { unifiedPerformanceMonitor } from '@/services/monitoring/UnifiedPerformanceMonitor';\nimport { useAIOptimization } from './useAIOptimization';\nimport { useEdgeOptimization } from './useEdgeOptimization';\nimport { useNativeOptimization } from './useNativeOptimization';\n\ninterface DashboardState {\n  isLoading: boolean;\n  lastUpdated: number;\n  unifiedMetrics: any;\n  insights: any;\n  alerts: any[];\n  trends: {\n    performance: Array<{ timestamp: number; value: number }>;\n    memory: Array<{ timestamp: number; value: number }>;\n    network: Array<{ timestamp: number; value: number }>;\n  };\n  phaseStatus: {\n    phase1: { status: string; score: number; health: number };\n    phase2: { status: string; score: number; health: number };\n    phase3a: { status: string; score: number; health: number };\n    phase3b: { status: string; score: number; health: number };\n    phase3c: { status: string; score: number; health: number };\n  };\n  realTimeMetrics: {\n    bundleSize: number;\n    loadTime: number;\n    renderTime: number;\n    memoryUsage: number;\n    cpuUsage: number;\n    networkLatency: number;\n    batteryDrain: number;\n  };\n}\n\ninterface DashboardConfig {\n  autoRefresh: boolean;\n  refreshInterval: number;\n  enableAlerts: boolean;\n  enableTrends: boolean;\n  enableInsights: boolean;\n  maxDataPoints: number;\n}\n\ninterface UsePerformanceDashboardReturn {\n  state: DashboardState;\n  actions: {\n    refreshMetrics: () => Promise<void>;\n    exportData: (format: 'json' | 'csv' | 'excel') => Promise<string>;\n    startProfiling: (sessionName: string) => Promise<string>;\n    acknowledgeAlert: (alertId: string) => void;\n    getPhaseDetails: (phase: string) => any;\n    getOptimizationRecommendations: () => Promise<any[]>;\n    toggleAutoRefresh: () => void;\n  };\n  config: DashboardConfig;\n  summary: {\n    overallScore: number;\n    totalImprovement: number;\n    healthScore: number;\n    activeAlerts: number;\n    topInsight: string;\n  };\n}\n\n/**\n * Performance Dashboard Hook\n */\nexport function usePerformanceDashboard(\n  initialConfig: Partial<DashboardConfig> = {}\n): UsePerformanceDashboardReturn {\n  const aiOptimization = useAIOptimization();\n  const edgeOptimization = useEdgeOptimization();\n  const nativeOptimization = useNativeOptimization();\n\n  const [config, setConfig] = useState<DashboardConfig>({\n    autoRefresh: true,\n    refreshInterval: 10000, // 10 seconds\n    enableAlerts: true,\n    enableTrends: true,\n    enableInsights: true,\n    maxDataPoints: 100,\n    ...initialConfig,\n  });\n\n  const [state, setState] = useState<DashboardState>({\n    isLoading: true,\n    lastUpdated: 0,\n    unifiedMetrics: null,\n    insights: null,\n    alerts: [],\n    trends: {\n      performance: [],\n      memory: [],\n      network: [],\n    },\n    phaseStatus: {\n      phase1: { status: 'unknown', score: 0, health: 0 },\n      phase2: { status: 'unknown', score: 0, health: 0 },\n      phase3a: { status: 'unknown', score: 0, health: 0 },\n      phase3b: { status: 'unknown', score: 0, health: 0 },\n      phase3c: { status: 'unknown', score: 0, health: 0 },\n    },\n    realTimeMetrics: {\n      bundleSize: 0,\n      loadTime: 0,\n      renderTime: 0,\n      memoryUsage: 0,\n      cpuUsage: 0,\n      networkLatency: 0,\n      batteryDrain: 0,\n    },\n  });\n\n  /**\n   * Refresh all performance metrics\n   */\n  const refreshMetrics = useCallback(async () => {\n    try {\n      setState(prev => ({ ...prev, isLoading: true }));\n\n      // Get unified metrics from monitoring system\n      const unifiedMetrics = await unifiedPerformanceMonitor.getUnifiedMetrics();\n      \n      // Get insights and recommendations\n      const insights = config.enableInsights \n        ? await unifiedPerformanceMonitor.getPerformanceInsights()\n        : null;\n\n      // Update phase status\n      const phaseStatus = {\n        phase1: {\n          status: unifiedMetrics.phases.phase1.status,\n          score: unifiedMetrics.phases.phase1.performance.current,\n          health: unifiedMetrics.phases.phase1.status === 'active' ? 100 : 50,\n        },\n        phase2: {\n          status: unifiedMetrics.phases.phase2.status,\n          score: unifiedMetrics.phases.phase2.performance.current,\n          health: unifiedMetrics.phases.phase2.status === 'active' ? 100 : 50,\n        },\n        phase3a: {\n          status: unifiedMetrics.phases.phase3a.status,\n          score: unifiedMetrics.phases.phase3a.performance.current,\n          health: unifiedMetrics.phases.phase3a.status === 'active' ? 100 : 50,\n        },\n        phase3b: {\n          status: unifiedMetrics.phases.phase3b.status,\n          score: unifiedMetrics.phases.phase3b.performance.current,\n          health: unifiedMetrics.phases.phase3b.status === 'active' ? 100 : 50,\n        },\n        phase3c: {\n          status: unifiedMetrics.phases.phase3c.status,\n          score: unifiedMetrics.phases.phase3c.performance.current,\n          health: unifiedMetrics.phases.phase3c.status === 'active' ? 100 : 50,\n        },\n      };\n\n      // Update trends\n      const now = Date.now();\n      const newTrends = { ...state.trends };\n      \n      if (config.enableTrends) {\n        // Add performance trend point\n        newTrends.performance.push({\n          timestamp: now,\n          value: unifiedMetrics.overall.performanceScore,\n        });\n        \n        // Add memory trend point\n        newTrends.memory.push({\n          timestamp: now,\n          value: unifiedMetrics.realTime.memoryUsage,\n        });\n        \n        // Add network trend point\n        newTrends.network.push({\n          timestamp: now,\n          value: unifiedMetrics.realTime.networkLatency,\n        });\n\n        // Limit data points\n        Object.keys(newTrends).forEach(key => {\n          if (newTrends[key as keyof typeof newTrends].length > config.maxDataPoints) {\n            newTrends[key as keyof typeof newTrends].shift();\n          }\n        });\n      }\n\n      setState(prev => ({\n        ...prev,\n        isLoading: false,\n        lastUpdated: now,\n        unifiedMetrics,\n        insights,\n        alerts: config.enableAlerts ? unifiedMetrics.alerts : [],\n        trends: newTrends,\n        phaseStatus,\n        realTimeMetrics: unifiedMetrics.realTime,\n      }));\n\n    } catch (error) {\n      console.error('Failed to refresh metrics:', error);\n      setState(prev => ({ ...prev, isLoading: false }));\n    }\n  }, [config, state.trends]);\n\n  /**\n   * Export dashboard data\n   */\n  const exportData = useCallback(async (format: 'json' | 'csv' | 'excel' = 'json') => {\n    try {\n      const exportData = await unifiedPerformanceMonitor.exportMonitoringData(format);\n      return exportData;\n    } catch (error) {\n      console.error('Failed to export data:', error);\n      return '';\n    }\n  }, []);\n\n  /**\n   * Start performance profiling session\n   */\n  const startProfiling = useCallback(async (sessionName: string) => {\n    try {\n      const sessionId = await unifiedPerformanceMonitor.startProfilingSession(sessionName);\n      console.log(`Started profiling session: ${sessionId}`);\n      return sessionId;\n    } catch (error) {\n      console.error('Failed to start profiling:', error);\n      return '';\n    }\n  }, []);\n\n  /**\n   * Acknowledge alert\n   */\n  const acknowledgeAlert = useCallback((alertId: string) => {\n    setState(prev => ({\n      ...prev,\n      alerts: prev.alerts.map(alert =>\n        alert.id === alertId ? { ...alert, resolved: true } : alert\n      ),\n    }));\n  }, []);\n\n  /**\n   * Get detailed phase information\n   */\n  const getPhaseDetails = useCallback((phase: string) => {\n    if (!state.unifiedMetrics) return null;\n    \n    const phaseData = state.unifiedMetrics.phases[phase];\n    if (!phaseData) return null;\n\n    return {\n      ...phaseData,\n      improvement: ((phaseData.performance.current - phaseData.performance.baseline) / phaseData.performance.baseline) * 100,\n      targetProgress: ((phaseData.performance.current - phaseData.performance.baseline) / (phaseData.performance.target - phaseData.performance.baseline)) * 100,\n    };\n  }, [state.unifiedMetrics]);\n\n  /**\n   * Get optimization recommendations\n   */\n  const getOptimizationRecommendations = useCallback(async () => {\n    try {\n      if (!state.insights) return [];\n      return state.insights.optimizationOpportunities || [];\n    } catch (error) {\n      console.error('Failed to get recommendations:', error);\n      return [];\n    }\n  }, [state.insights]);\n\n  /**\n   * Toggle auto refresh\n   */\n  const toggleAutoRefresh = useCallback(() => {\n    setConfig(prev => ({ ...prev, autoRefresh: !prev.autoRefresh }));\n  }, []);\n\n  // Auto refresh effect\n  useEffect(() => {\n    if (!config.autoRefresh) return;\n\n    const interval = setInterval(() => {\n      refreshMetrics();\n    }, config.refreshInterval);\n\n    return () => clearInterval(interval);\n  }, [config.autoRefresh, config.refreshInterval, refreshMetrics]);\n\n  // Initial load\n  useEffect(() => {\n    refreshMetrics();\n  }, []);\n\n  // Calculate summary metrics\n  const summary = useMemo(() => {\n    if (!state.unifiedMetrics) {\n      return {\n        overallScore: 0,\n        totalImprovement: 0,\n        healthScore: 0,\n        activeAlerts: 0,\n        topInsight: 'Loading...',\n      };\n    }\n\n    const activeAlerts = state.alerts.filter(alert => !alert.resolved).length;\n    const topInsight = state.insights?.insights?.[0]?.insight || 'No insights available';\n\n    return {\n      overallScore: Math.round(state.unifiedMetrics.overall.performanceScore),\n      totalImprovement: Math.round(state.unifiedMetrics.overall.totalImprovement),\n      healthScore: Math.round(state.unifiedMetrics.overall.healthScore),\n      activeAlerts,\n      topInsight,\n    };\n  }, [state.unifiedMetrics, state.alerts, state.insights]);\n\n  return useMemo(() => ({\n    state,\n    actions: {\n      refreshMetrics,\n      exportData,\n      startProfiling,\n      acknowledgeAlert,\n      getPhaseDetails,\n      getOptimizationRecommendations,\n      toggleAutoRefresh,\n    },\n    config,\n    summary,\n  }), [\n    state,\n    refreshMetrics,\n    exportData,\n    startProfiling,\n    acknowledgeAlert,\n    getPhaseDetails,\n    getOptimizationRecommendations,\n    toggleAutoRefresh,\n    config,\n    summary,\n  ]);\n}\n\nexport default usePerformanceDashboard;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACjE,SAASC,yBAAyB;AAClC,SAASC,iBAAiB;AAC1B,SAASC,mBAAmB;AAC5B,SAASC,qBAAqB;AAgE9B,OAAO,SAASC,uBAAuBA,CAAA,EAEN;EAAA,IAD/BC,aAAuC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;EAAAD,cAAA,GAAAE,CAAA;EAE5C,IAAMC,cAAc,IAAAH,cAAA,GAAAI,CAAA,OAAGZ,iBAAiB,CAAC,CAAC;EAC1C,IAAMa,gBAAgB,IAAAL,cAAA,GAAAI,CAAA,OAAGX,mBAAmB,CAAC,CAAC;EAC9C,IAAMa,kBAAkB,IAAAN,cAAA,GAAAI,CAAA,OAAGV,qBAAqB,CAAC,CAAC;EAElD,IAAAa,IAAA,IAAAP,cAAA,GAAAI,CAAA,OAA4BjB,QAAQ,CAAAqB,MAAA,CAAAC,MAAA;MAClCC,WAAW,EAAE,IAAI;MACjBC,eAAe,EAAE,KAAK;MACtBC,YAAY,EAAE,IAAI;MAClBC,YAAY,EAAE,IAAI;MAClBC,cAAc,EAAE,IAAI;MACpBC,aAAa,EAAE;IAAG,GACfnB,aAAa,CACjB,CAAC;IAAAoB,KAAA,GAAAC,cAAA,CAAAV,IAAA;IARKW,MAAM,GAAAF,KAAA;IAAEG,SAAS,GAAAH,KAAA;EAUxB,IAAAI,KAAA,IAAApB,cAAA,GAAAI,CAAA,OAA0BjB,QAAQ,CAAiB;MACjDkC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,CAAC;MACdC,cAAc,EAAE,IAAI;MACpBC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE;QACNC,WAAW,EAAE,EAAE;QACfC,MAAM,EAAE,EAAE;QACVC,OAAO,EAAE;MACX,CAAC;MACDC,WAAW,EAAE;QACXC,MAAM,EAAE;UAAEC,MAAM,EAAE,SAAS;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;QAClDC,MAAM,EAAE;UAAEH,MAAM,EAAE,SAAS;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;QAClDE,OAAO,EAAE;UAAEJ,MAAM,EAAE,SAAS;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;QACnDG,OAAO,EAAE;UAAEL,MAAM,EAAE,SAAS;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE,CAAC;QACnDI,OAAO,EAAE;UAAEN,MAAM,EAAE,SAAS;UAAEC,KAAK,EAAE,CAAC;UAAEC,MAAM,EAAE;QAAE;MACpD,CAAC;MACDK,eAAe,EAAE;QACfC,UAAU,EAAE,CAAC;QACbC,QAAQ,EAAE,CAAC;QACXC,UAAU,EAAE,CAAC;QACbC,WAAW,EAAE,CAAC;QACdC,QAAQ,EAAE,CAAC;QACXC,cAAc,EAAE,CAAC;QACjBC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IAAAC,KAAA,GAAA9B,cAAA,CAAAG,KAAA;IA3BK4B,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EAgCtB,IAAMG,cAAc,IAAAlD,cAAA,GAAAI,CAAA,OAAGf,WAAW,CAAA8D,iBAAA,CAAC,aAAY;IAAAnD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC7C,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACF6C,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAApD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAI,MAAA,CAAAC,MAAA,KAAK2C,IAAI;UAAE/B,SAAS,EAAE;QAAI;MAAC,CAAE,CAAC;MAGhD,IAAME,cAAc,IAAAvB,cAAA,GAAAI,CAAA,aAASb,yBAAyB,CAAC8D,iBAAiB,CAAC,CAAC;MAG1E,IAAM7B,QAAQ,IAAAxB,cAAA,GAAAI,CAAA,QAAGc,MAAM,CAACJ,cAAc,IAAAd,cAAA,GAAAC,CAAA,gBAC5BV,yBAAyB,CAAC+D,sBAAsB,CAAC,CAAC,KAAAtD,cAAA,GAAAC,CAAA,UACxD,IAAI;MAGR,IAAM6B,WAAW,IAAA9B,cAAA,GAAAI,CAAA,QAAG;QAClB2B,MAAM,EAAE;UACNC,MAAM,EAAET,cAAc,CAACgC,MAAM,CAACxB,MAAM,CAACC,MAAM;UAC3CC,KAAK,EAAEV,cAAc,CAACgC,MAAM,CAACxB,MAAM,CAACJ,WAAW,CAAC6B,OAAO;UACvDtB,MAAM,EAAEX,cAAc,CAACgC,MAAM,CAACxB,MAAM,CAACC,MAAM,KAAK,QAAQ,IAAAhC,cAAA,GAAAC,CAAA,UAAG,GAAG,KAAAD,cAAA,GAAAC,CAAA,UAAG,EAAE;QACrE,CAAC;QACDkC,MAAM,EAAE;UACNH,MAAM,EAAET,cAAc,CAACgC,MAAM,CAACpB,MAAM,CAACH,MAAM;UAC3CC,KAAK,EAAEV,cAAc,CAACgC,MAAM,CAACpB,MAAM,CAACR,WAAW,CAAC6B,OAAO;UACvDtB,MAAM,EAAEX,cAAc,CAACgC,MAAM,CAACpB,MAAM,CAACH,MAAM,KAAK,QAAQ,IAAAhC,cAAA,GAAAC,CAAA,UAAG,GAAG,KAAAD,cAAA,GAAAC,CAAA,UAAG,EAAE;QACrE,CAAC;QACDmC,OAAO,EAAE;UACPJ,MAAM,EAAET,cAAc,CAACgC,MAAM,CAACnB,OAAO,CAACJ,MAAM;UAC5CC,KAAK,EAAEV,cAAc,CAACgC,MAAM,CAACnB,OAAO,CAACT,WAAW,CAAC6B,OAAO;UACxDtB,MAAM,EAAEX,cAAc,CAACgC,MAAM,CAACnB,OAAO,CAACJ,MAAM,KAAK,QAAQ,IAAAhC,cAAA,GAAAC,CAAA,UAAG,GAAG,KAAAD,cAAA,GAAAC,CAAA,UAAG,EAAE;QACtE,CAAC;QACDoC,OAAO,EAAE;UACPL,MAAM,EAAET,cAAc,CAACgC,MAAM,CAAClB,OAAO,CAACL,MAAM;UAC5CC,KAAK,EAAEV,cAAc,CAACgC,MAAM,CAAClB,OAAO,CAACV,WAAW,CAAC6B,OAAO;UACxDtB,MAAM,EAAEX,cAAc,CAACgC,MAAM,CAAClB,OAAO,CAACL,MAAM,KAAK,QAAQ,IAAAhC,cAAA,GAAAC,CAAA,UAAG,GAAG,KAAAD,cAAA,GAAAC,CAAA,UAAG,EAAE;QACtE,CAAC;QACDqC,OAAO,EAAE;UACPN,MAAM,EAAET,cAAc,CAACgC,MAAM,CAACjB,OAAO,CAACN,MAAM;UAC5CC,KAAK,EAAEV,cAAc,CAACgC,MAAM,CAACjB,OAAO,CAACX,WAAW,CAAC6B,OAAO;UACxDtB,MAAM,EAAEX,cAAc,CAACgC,MAAM,CAACjB,OAAO,CAACN,MAAM,KAAK,QAAQ,IAAAhC,cAAA,GAAAC,CAAA,UAAG,GAAG,KAAAD,cAAA,GAAAC,CAAA,UAAG,EAAE;QACtE;MACF,CAAC;MAGD,IAAMwD,GAAG,IAAAzD,cAAA,GAAAI,CAAA,QAAGsD,IAAI,CAACD,GAAG,CAAC,CAAC;MACtB,IAAME,SAAS,IAAA3D,cAAA,GAAAI,CAAA,QAAAI,MAAA,CAAAC,MAAA,KAAQuC,KAAK,CAACtB,MAAM,EAAE;MAAC1B,cAAA,GAAAI,CAAA;MAEtC,IAAIc,MAAM,CAACL,YAAY,EAAE;QAAAb,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAEvBuD,SAAS,CAAChC,WAAW,CAACiC,IAAI,CAAC;UACzBC,SAAS,EAAEJ,GAAG;UACdK,KAAK,EAAEvC,cAAc,CAACwC,OAAO,CAACC;QAChC,CAAC,CAAC;QAAChE,cAAA,GAAAI,CAAA;QAGHuD,SAAS,CAAC/B,MAAM,CAACgC,IAAI,CAAC;UACpBC,SAAS,EAAEJ,GAAG;UACdK,KAAK,EAAEvC,cAAc,CAAC0C,QAAQ,CAACtB;QACjC,CAAC,CAAC;QAAC3C,cAAA,GAAAI,CAAA;QAGHuD,SAAS,CAAC9B,OAAO,CAAC+B,IAAI,CAAC;UACrBC,SAAS,EAAEJ,GAAG;UACdK,KAAK,EAAEvC,cAAc,CAAC0C,QAAQ,CAACpB;QACjC,CAAC,CAAC;QAAC7C,cAAA,GAAAI,CAAA;QAGHI,MAAM,CAAC0D,IAAI,CAACP,SAAS,CAAC,CAACQ,OAAO,CAAC,UAAAC,GAAG,EAAI;UAAApE,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UACpC,IAAIuD,SAAS,CAACS,GAAG,CAA2B,CAACtE,MAAM,GAAGoB,MAAM,CAACH,aAAa,EAAE;YAAAf,cAAA,GAAAC,CAAA;YAAAD,cAAA,GAAAI,CAAA;YAC1EuD,SAAS,CAACS,GAAG,CAA2B,CAACC,KAAK,CAAC,CAAC;UAClD,CAAC;YAAArE,cAAA,GAAAC,CAAA;UAAA;QACH,CAAC,CAAC;MACJ,CAAC;QAAAD,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED6C,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAApD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAI,MAAA,CAAAC,MAAA,KACb2C,IAAI;UACP/B,SAAS,EAAE,KAAK;UAChBC,WAAW,EAAEmC,GAAG;UAChBlC,cAAc,EAAdA,cAAc;UACdC,QAAQ,EAARA,QAAQ;UACRC,MAAM,EAAEP,MAAM,CAACN,YAAY,IAAAZ,cAAA,GAAAC,CAAA,UAAGsB,cAAc,CAACE,MAAM,KAAAzB,cAAA,GAAAC,CAAA,UAAG,EAAE;UACxDyB,MAAM,EAAEiC,SAAS;UACjB7B,WAAW,EAAXA,WAAW;UACXS,eAAe,EAAEhB,cAAc,CAAC0C;QAAQ;MAC1C,CAAE,CAAC;IAEL,CAAC,CAAC,OAAOK,KAAK,EAAE;MAAAtE,cAAA,GAAAI,CAAA;MACdmE,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAACtE,cAAA,GAAAI,CAAA;MACnD6C,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAApD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAI,MAAA,CAAAC,MAAA,KAAK2C,IAAI;UAAE/B,SAAS,EAAE;QAAK;MAAC,CAAE,CAAC;IACnD;EACF,CAAC,GAAE,CAACH,MAAM,EAAE8B,KAAK,CAACtB,MAAM,CAAC,CAAC;EAK1B,IAAM8C,UAAU,IAAAxE,cAAA,GAAAI,CAAA,QAAGf,WAAW,CAAA8D,iBAAA,CAAC,aAAqD;IAAA,IAA9CsB,MAAgC,GAAA5E,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,WAAG,MAAM;IAAAD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC7E,IAAI;MACF,IAAMoE,WAAU,IAAAxE,cAAA,GAAAI,CAAA,cAASb,yBAAyB,CAACmF,oBAAoB,CAACD,MAAM,CAAC;MAACzE,cAAA,GAAAI,CAAA;MAChF,OAAOoE,WAAU;IACnB,CAAC,CAAC,OAAOF,KAAK,EAAE;MAAAtE,cAAA,GAAAI,CAAA;MACdmE,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAACtE,cAAA,GAAAI,CAAA;MAC/C,OAAO,EAAE;IACX;EACF,CAAC,GAAE,EAAE,CAAC;EAKN,IAAMuE,cAAc,IAAA3E,cAAA,GAAAI,CAAA,QAAGf,WAAW;IAAA,IAAAuF,KAAA,GAAAzB,iBAAA,CAAC,WAAO0B,WAAmB,EAAK;MAAA7E,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAChE,IAAI;QACF,IAAM0E,SAAS,IAAA9E,cAAA,GAAAI,CAAA,cAASb,yBAAyB,CAACwF,qBAAqB,CAACF,WAAW,CAAC;QAAC7E,cAAA,GAAAI,CAAA;QACrFmE,OAAO,CAACS,GAAG,CAAC,8BAA8BF,SAAS,EAAE,CAAC;QAAC9E,cAAA,GAAAI,CAAA;QACvD,OAAO0E,SAAS;MAClB,CAAC,CAAC,OAAOR,KAAK,EAAE;QAAAtE,cAAA,GAAAI,CAAA;QACdmE,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAACtE,cAAA,GAAAI,CAAA;QACnD,OAAO,EAAE;MACX;IACF,CAAC;IAAA,iBAAA6E,EAAA;MAAA,OAAAL,KAAA,CAAAM,KAAA,OAAArF,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAKN,IAAMsF,gBAAgB,IAAAnF,cAAA,GAAAI,CAAA,QAAGf,WAAW,CAAC,UAAC+F,OAAe,EAAK;IAAApF,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACxD6C,QAAQ,CAAC,UAAAG,IAAI,EAAK;MAAApD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAI,MAAA,CAAAC,MAAA,KACb2C,IAAI;QACP3B,MAAM,EAAE2B,IAAI,CAAC3B,MAAM,CAAC4D,GAAG,CAAC,UAAAC,KAAK,EAC3B;UAAAtF,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAkF,KAAK,CAACC,EAAE,KAAKH,OAAO,IAAApF,cAAA,GAAAC,CAAA,WAAAO,MAAA,CAAAC,MAAA,KAAQ6E,KAAK;YAAEE,QAAQ,EAAE;UAAI,OAAAxF,cAAA,GAAAC,CAAA,WAAKqF,KAAK;QAAD,CAC5D;MAAC;IACH,CAAE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMG,eAAe,IAAAzF,cAAA,GAAAI,CAAA,QAAGf,WAAW,CAAC,UAACqG,KAAa,EAAK;IAAA1F,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACrD,IAAI,CAAC4C,KAAK,CAACzB,cAAc,EAAE;MAAAvB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAEvC,IAAM0F,SAAS,IAAA3F,cAAA,GAAAI,CAAA,QAAG4C,KAAK,CAACzB,cAAc,CAACgC,MAAM,CAACmC,KAAK,CAAC;IAAC1F,cAAA,GAAAI,CAAA;IACrD,IAAI,CAACuF,SAAS,EAAE;MAAA3F,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,IAAI;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAE5B,OAAAI,MAAA,CAAAC,MAAA,KACKkF,SAAS;MACZC,WAAW,EAAG,CAACD,SAAS,CAAChE,WAAW,CAAC6B,OAAO,GAAGmC,SAAS,CAAChE,WAAW,CAACkE,QAAQ,IAAIF,SAAS,CAAChE,WAAW,CAACkE,QAAQ,GAAI,GAAG;MACtHC,cAAc,EAAG,CAACH,SAAS,CAAChE,WAAW,CAAC6B,OAAO,GAAGmC,SAAS,CAAChE,WAAW,CAACkE,QAAQ,KAAKF,SAAS,CAAChE,WAAW,CAACoE,MAAM,GAAGJ,SAAS,CAAChE,WAAW,CAACkE,QAAQ,CAAC,GAAI;IAAG;EAE9J,CAAC,EAAE,CAAC7C,KAAK,CAACzB,cAAc,CAAC,CAAC;EAK1B,IAAMyE,8BAA8B,IAAAhG,cAAA,GAAAI,CAAA,QAAGf,WAAW,CAAA8D,iBAAA,CAAC,aAAY;IAAAnD,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC7D,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACF,IAAI,CAAC4C,KAAK,CAACxB,QAAQ,EAAE;QAAAxB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAAA,OAAO,EAAE;MAAA,CAAC;QAAAJ,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAC/B,OAAO,CAAAJ,cAAA,GAAAC,CAAA,WAAA+C,KAAK,CAACxB,QAAQ,CAACyE,yBAAyB,MAAAjG,cAAA,GAAAC,CAAA,WAAI,EAAE;IACvD,CAAC,CAAC,OAAOqE,KAAK,EAAE;MAAAtE,cAAA,GAAAI,CAAA;MACdmE,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAACtE,cAAA,GAAAI,CAAA;MACvD,OAAO,EAAE;IACX;EACF,CAAC,GAAE,CAAC4C,KAAK,CAACxB,QAAQ,CAAC,CAAC;EAKpB,IAAM0E,iBAAiB,IAAAlG,cAAA,GAAAI,CAAA,QAAGf,WAAW,CAAC,YAAM;IAAAW,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC1Ce,SAAS,CAAC,UAAAiC,IAAI,EAAK;MAAApD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAI,MAAA,CAAAC,MAAA,KAAK2C,IAAI;QAAE1C,WAAW,EAAE,CAAC0C,IAAI,CAAC1C;MAAW;IAAC,CAAE,CAAC;EAClE,CAAC,EAAE,EAAE,CAAC;EAACV,cAAA,GAAAI,CAAA;EAGPhB,SAAS,CAAC,YAAM;IAAAY,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd,IAAI,CAACc,MAAM,CAACR,WAAW,EAAE;MAAAV,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAEhC,IAAMkG,QAAQ,IAAAnG,cAAA,GAAAI,CAAA,QAAGgG,WAAW,CAAC,YAAM;MAAApG,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACjC8C,cAAc,CAAC,CAAC;IAClB,CAAC,EAAEhC,MAAM,CAACP,eAAe,CAAC;IAACX,cAAA,GAAAI,CAAA;IAE3B,OAAO,YAAM;MAAAJ,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAiG,aAAa,CAACF,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAACjF,MAAM,CAACR,WAAW,EAAEQ,MAAM,CAACP,eAAe,EAAEuC,cAAc,CAAC,CAAC;EAAClD,cAAA,GAAAI,CAAA;EAGjEhB,SAAS,CAAC,YAAM;IAAAY,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd8C,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMoD,OAAO,IAAAtG,cAAA,GAAAI,CAAA,QAAGd,OAAO,CAAC,YAAM;IAAA,IAAAiH,eAAA;IAAAvG,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC5B,IAAI,CAAC4C,KAAK,CAACzB,cAAc,EAAE;MAAAvB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MACzB,OAAO;QACLoG,YAAY,EAAE,CAAC;QACfC,gBAAgB,EAAE,CAAC;QACnBC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,UAAU,EAAE;MACd,CAAC;IACH,CAAC;MAAA5G,cAAA,GAAAC,CAAA;IAAA;IAED,IAAM0G,YAAY,IAAA3G,cAAA,GAAAI,CAAA,QAAG4C,KAAK,CAACvB,MAAM,CAACoF,MAAM,CAAC,UAAAvB,KAAK,EAAI;MAAAtF,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,QAACkF,KAAK,CAACE,QAAQ;IAAD,CAAC,CAAC,CAAC1F,MAAM;IACzE,IAAM8G,UAAU,IAAA5G,cAAA,GAAAI,CAAA,QAAG,CAAAJ,cAAA,GAAAC,CAAA,YAAAsG,eAAA,GAAAvD,KAAK,CAACxB,QAAQ,cAAA+E,eAAA,GAAdA,eAAA,CAAgB/E,QAAQ,cAAA+E,eAAA,GAAxBA,eAAA,CAA2B,CAAC,CAAC,qBAA7BA,eAAA,CAA+BO,OAAO,MAAA9G,cAAA,GAAAC,CAAA,WAAI,uBAAuB;IAACD,cAAA,GAAAI,CAAA;IAErF,OAAO;MACLoG,YAAY,EAAEO,IAAI,CAACC,KAAK,CAAChE,KAAK,CAACzB,cAAc,CAACwC,OAAO,CAACC,gBAAgB,CAAC;MACvEyC,gBAAgB,EAAEM,IAAI,CAACC,KAAK,CAAChE,KAAK,CAACzB,cAAc,CAACwC,OAAO,CAAC0C,gBAAgB,CAAC;MAC3EC,WAAW,EAAEK,IAAI,CAACC,KAAK,CAAChE,KAAK,CAACzB,cAAc,CAACwC,OAAO,CAAC2C,WAAW,CAAC;MACjEC,YAAY,EAAZA,YAAY;MACZC,UAAU,EAAVA;IACF,CAAC;EACH,CAAC,EAAE,CAAC5D,KAAK,CAACzB,cAAc,EAAEyB,KAAK,CAACvB,MAAM,EAAEuB,KAAK,CAACxB,QAAQ,CAAC,CAAC;EAACxB,cAAA,GAAAI,CAAA;EAEzD,OAAOd,OAAO,CAAC,YAAO;IAAAU,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAAA;MACpB4C,KAAK,EAALA,KAAK;MACLiE,OAAO,EAAE;QACP/D,cAAc,EAAdA,cAAc;QACdsB,UAAU,EAAVA,UAAU;QACVG,cAAc,EAAdA,cAAc;QACdQ,gBAAgB,EAAhBA,gBAAgB;QAChBM,eAAe,EAAfA,eAAe;QACfO,8BAA8B,EAA9BA,8BAA8B;QAC9BE,iBAAiB,EAAjBA;MACF,CAAC;MACDhF,MAAM,EAANA,MAAM;MACNoF,OAAO,EAAPA;IACF,CAAC;EAAD,CAAE,EAAE,CACFtD,KAAK,EACLE,cAAc,EACdsB,UAAU,EACVG,cAAc,EACdQ,gBAAgB,EAChBM,eAAe,EACfO,8BAA8B,EAC9BE,iBAAiB,EACjBhF,MAAM,EACNoF,OAAO,CACR,CAAC;AACJ;AAEA,eAAe3G,uBAAuB", "ignoreList": []}