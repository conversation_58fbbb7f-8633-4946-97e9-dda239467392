7c8ab20cad1f95382cc1147d1d98bc41
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2p11j46nvo() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\components\\payment\\SubscriptionPlans.tsx";
  var hash = "c50a432a5d2c72e2fa56d7a143fd3b9b17054ab4";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\components\\payment\\SubscriptionPlans.tsx",
    statementMap: {
      "0": {
        start: {
          line: 23,
          column: 15
        },
        end: {
          line: 33,
          column: 1
        }
      },
      "1": {
        start: {
          line: 48,
          column: 19
        },
        end: {
          line: 48,
          column: 28
        }
      },
      "2": {
        start: {
          line: 49,
          column: 28
        },
        end: {
          line: 49,
          column: 60
        }
      },
      "3": {
        start: {
          line: 50,
          column: 32
        },
        end: {
          line: 50,
          column: 46
        }
      },
      "4": {
        start: {
          line: 51,
          column: 42
        },
        end: {
          line: 51,
          column: 71
        }
      },
      "5": {
        start: {
          line: 53,
          column: 2
        },
        end: {
          line: 55,
          column: 9
        }
      },
      "6": {
        start: {
          line: 54,
          column: 4
        },
        end: {
          line: 54,
          column: 28
        }
      },
      "7": {
        start: {
          line: 57,
          column: 32
        },
        end: {
          line: 67,
          column: 3
        }
      },
      "8": {
        start: {
          line: 58,
          column: 4
        },
        end: {
          line: 66,
          column: 5
        }
      },
      "9": {
        start: {
          line: 59,
          column: 29
        },
        end: {
          line: 59,
          column: 66
        }
      },
      "10": {
        start: {
          line: 60,
          column: 6
        },
        end: {
          line: 60,
          column: 31
        }
      },
      "11": {
        start: {
          line: 62,
          column: 6
        },
        end: {
          line: 62,
          column: 65
        }
      },
      "12": {
        start: {
          line: 63,
          column: 6
        },
        end: {
          line: 63,
          column: 64
        }
      },
      "13": {
        start: {
          line: 65,
          column: 6
        },
        end: {
          line: 65,
          column: 24
        }
      },
      "14": {
        start: {
          line: 69,
          column: 27
        },
        end: {
          line: 90,
          column: 3
        }
      },
      "15": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 73,
          column: 5
        }
      },
      "16": {
        start: {
          line: 71,
          column: 6
        },
        end: {
          line: 71,
          column: 86
        }
      },
      "17": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 72,
          column: 13
        }
      },
      "18": {
        start: {
          line: 75,
          column: 4
        },
        end: {
          line: 75,
          column: 29
        }
      },
      "19": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 81,
          column: 5
        }
      },
      "20": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 27
        }
      },
      "21": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 80,
          column: 13
        }
      },
      "22": {
        start: {
          line: 83,
          column: 4
        },
        end: {
          line: 89,
          column: 5
        }
      },
      "23": {
        start: {
          line: 85,
          column: 6
        },
        end: {
          line: 85,
          column: 24
        }
      },
      "24": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 27
        }
      },
      "25": {
        start: {
          line: 92,
          column: 22
        },
        end: {
          line: 97,
          column: 3
        }
      },
      "26": {
        start: {
          line: 93,
          column: 4
        },
        end: {
          line: 93,
          column: 35
        }
      },
      "27": {
        start: {
          line: 93,
          column: 21
        },
        end: {
          line: 93,
          column: 35
        }
      },
      "28": {
        start: {
          line: 95,
          column: 27
        },
        end: {
          line: 95,
          column: 51
        }
      },
      "29": {
        start: {
          line: 96,
          column: 4
        },
        end: {
          line: 96,
          column: 44
        }
      },
      "30": {
        start: {
          line: 99,
          column: 22
        },
        end: {
          line: 110,
          column: 3
        }
      },
      "31": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 109,
          column: 5
        }
      },
      "32": {
        start: {
          line: 102,
          column: 8
        },
        end: {
          line: 102,
          column: 54
        }
      },
      "33": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 104,
          column: 56
        }
      },
      "34": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 55
        }
      },
      "35": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 54
        }
      },
      "36": {
        start: {
          line: 112,
          column: 23
        },
        end: {
          line: 123,
          column: 3
        }
      },
      "37": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 122,
          column: 5
        }
      },
      "38": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 115,
          column: 27
        }
      },
      "39": {
        start: {
          line: 117,
          column: 8
        },
        end: {
          line: 117,
          column: 30
        }
      },
      "40": {
        start: {
          line: 119,
          column: 8
        },
        end: {
          line: 119,
          column: 27
        }
      },
      "41": {
        start: {
          line: 121,
          column: 8
        },
        end: {
          line: 121,
          column: 27
        }
      },
      "42": {
        start: {
          line: 125,
          column: 24
        },
        end: {
          line: 127,
          column: 3
        }
      },
      "43": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 126,
          column: 36
        }
      },
      "44": {
        start: {
          line: 129,
          column: 21
        },
        end: {
          line: 133,
          column: 3
        }
      },
      "45": {
        start: {
          line: 130,
          column: 4
        },
        end: {
          line: 130,
          column: 77
        }
      },
      "46": {
        start: {
          line: 130,
          column: 52
        },
        end: {
          line: 130,
          column: 77
        }
      },
      "47": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 131,
          column: 61
        }
      },
      "48": {
        start: {
          line: 131,
          column: 33
        },
        end: {
          line: 131,
          column: 61
        }
      },
      "49": {
        start: {
          line: 132,
          column: 4
        },
        end: {
          line: 132,
          column: 17
        }
      },
      "50": {
        start: {
          line: 135,
          column: 23
        },
        end: {
          line: 140,
          column: 3
        }
      },
      "51": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 136,
          column: 37
        }
      },
      "52": {
        start: {
          line: 136,
          column: 24
        },
        end: {
          line: 136,
          column: 37
        }
      },
      "53": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 137,
          column: 82
        }
      },
      "54": {
        start: {
          line: 137,
          column: 37
        },
        end: {
          line: 137,
          column: 82
        }
      },
      "55": {
        start: {
          line: 138,
          column: 4
        },
        end: {
          line: 138,
          column: 58
        }
      },
      "56": {
        start: {
          line: 138,
          column: 33
        },
        end: {
          line: 138,
          column: 58
        }
      },
      "57": {
        start: {
          line: 139,
          column: 4
        },
        end: {
          line: 139,
          column: 17
        }
      },
      "58": {
        start: {
          line: 142,
          column: 25
        },
        end: {
          line: 223,
          column: 3
        }
      },
      "59": {
        start: {
          line: 143,
          column: 22
        },
        end: {
          line: 143,
          column: 44
        }
      },
      "60": {
        start: {
          line: 144,
          column: 29
        },
        end: {
          line: 144,
          column: 48
        }
      },
      "61": {
        start: {
          line: 145,
          column: 31
        },
        end: {
          line: 145,
          column: 52
        }
      },
      "62": {
        start: {
          line: 146,
          column: 23
        },
        end: {
          line: 146,
          column: 47
        }
      },
      "63": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 222,
          column: 6
        }
      },
      "64": {
        start: {
          line: 191,
          column: 12
        },
        end: {
          line: 194,
          column: 19
        }
      },
      "65": {
        start: {
          line: 212,
          column: 29
        },
        end: {
          line: 212,
          column: 51
        }
      },
      "66": {
        start: {
          line: 225,
          column: 2
        },
        end: {
          line: 232,
          column: 3
        }
      },
      "67": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 231,
          column: 6
        }
      },
      "68": {
        start: {
          line: 234,
          column: 2
        },
        end: {
          line: 254,
          column: 4
        }
      },
      "69": {
        start: {
          line: 257,
          column: 15
        },
        end: {
          line: 405,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "SubscriptionPlans",
        decl: {
          start: {
            line: 42,
            column: 16
          },
          end: {
            line: 42,
            column: 33
          }
        },
        loc: {
          start: {
            line: 47,
            column: 27
          },
          end: {
            line: 255,
            column: 1
          }
        },
        line: 47
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 53,
            column: 12
          },
          end: {
            line: 53,
            column: 13
          }
        },
        loc: {
          start: {
            line: 53,
            column: 18
          },
          end: {
            line: 55,
            column: 3
          }
        },
        line: 53
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 57,
            column: 32
          },
          end: {
            line: 57,
            column: 33
          }
        },
        loc: {
          start: {
            line: 57,
            column: 44
          },
          end: {
            line: 67,
            column: 3
          }
        },
        line: 57
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 69,
            column: 27
          },
          end: {
            line: 69,
            column: 28
          }
        },
        loc: {
          start: {
            line: 69,
            column: 55
          },
          end: {
            line: 90,
            column: 3
          }
        },
        line: 69
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 92,
            column: 22
          },
          end: {
            line: 92,
            column: 23
          }
        },
        loc: {
          start: {
            line: 92,
            column: 77
          },
          end: {
            line: 97,
            column: 3
          }
        },
        line: 92
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 99,
            column: 22
          },
          end: {
            line: 99,
            column: 23
          }
        },
        loc: {
          start: {
            line: 99,
            column: 42
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 99
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 112,
            column: 23
          },
          end: {
            line: 112,
            column: 24
          }
        },
        loc: {
          start: {
            line: 112,
            column: 43
          },
          end: {
            line: 123,
            column: 3
          }
        },
        line: 112
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 125,
            column: 24
          },
          end: {
            line: 125,
            column: 25
          }
        },
        loc: {
          start: {
            line: 125,
            column: 44
          },
          end: {
            line: 127,
            column: 3
          }
        },
        line: 125
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 129,
            column: 21
          },
          end: {
            line: 129,
            column: 22
          }
        },
        loc: {
          start: {
            line: 129,
            column: 41
          },
          end: {
            line: 133,
            column: 3
          }
        },
        line: 129
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 135,
            column: 23
          },
          end: {
            line: 135,
            column: 24
          }
        },
        loc: {
          start: {
            line: 135,
            column: 43
          },
          end: {
            line: 140,
            column: 3
          }
        },
        line: 135
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 142,
            column: 25
          },
          end: {
            line: 142,
            column: 26
          }
        },
        loc: {
          start: {
            line: 142,
            column: 53
          },
          end: {
            line: 223,
            column: 3
          }
        },
        line: 142
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 190,
            column: 29
          },
          end: {
            line: 190,
            column: 30
          }
        },
        loc: {
          start: {
            line: 191,
            column: 12
          },
          end: {
            line: 194,
            column: 19
          }
        },
        line: 191
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 212,
            column: 23
          },
          end: {
            line: 212,
            column: 24
          }
        },
        loc: {
          start: {
            line: 212,
            column: 29
          },
          end: {
            line: 212,
            column: 51
          }
        },
        line: 212
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 46,
            column: 2
          },
          end: {
            line: 46,
            column: 24
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 46,
            column: 20
          },
          end: {
            line: 46,
            column: 24
          }
        }],
        line: 46
      },
      "1": {
        loc: {
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 70,
            column: 4
          },
          end: {
            line: 73,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 70
      },
      "2": {
        loc: {
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 81,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "3": {
        loc: {
          start: {
            line: 83,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 83,
            column: 4
          },
          end: {
            line: 89,
            column: 5
          }
        }, {
          start: {
            line: 86,
            column: 11
          },
          end: {
            line: 89,
            column: 5
          }
        }],
        line: 83
      },
      "4": {
        loc: {
          start: {
            line: 83,
            column: 8
          },
          end: {
            line: 83,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 83,
            column: 8
          },
          end: {
            line: 83,
            column: 21
          }
        }, {
          start: {
            line: 83,
            column: 25
          },
          end: {
            line: 83,
            column: 49
          }
        }],
        line: 83
      },
      "5": {
        loc: {
          start: {
            line: 93,
            column: 4
          },
          end: {
            line: 93,
            column: 35
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 93,
            column: 4
          },
          end: {
            line: 93,
            column: 35
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 93
      },
      "6": {
        loc: {
          start: {
            line: 100,
            column: 4
          },
          end: {
            line: 109,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 101,
            column: 6
          },
          end: {
            line: 102,
            column: 54
          }
        }, {
          start: {
            line: 103,
            column: 6
          },
          end: {
            line: 104,
            column: 56
          }
        }, {
          start: {
            line: 105,
            column: 6
          },
          end: {
            line: 106,
            column: 55
          }
        }, {
          start: {
            line: 107,
            column: 6
          },
          end: {
            line: 108,
            column: 54
          }
        }],
        line: 100
      },
      "7": {
        loc: {
          start: {
            line: 113,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 114,
            column: 6
          },
          end: {
            line: 115,
            column: 27
          }
        }, {
          start: {
            line: 116,
            column: 6
          },
          end: {
            line: 117,
            column: 30
          }
        }, {
          start: {
            line: 118,
            column: 6
          },
          end: {
            line: 119,
            column: 27
          }
        }, {
          start: {
            line: 120,
            column: 6
          },
          end: {
            line: 121,
            column: 27
          }
        }],
        line: 113
      },
      "8": {
        loc: {
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 130,
            column: 77
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 130,
            column: 4
          },
          end: {
            line: 130,
            column: 77
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 130
      },
      "9": {
        loc: {
          start: {
            line: 130,
            column: 8
          },
          end: {
            line: 130,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 130,
            column: 8
          },
          end: {
            line: 130,
            column: 22
          }
        }, {
          start: {
            line: 130,
            column: 26
          },
          end: {
            line: 130,
            column: 50
          }
        }],
        line: 130
      },
      "10": {
        loc: {
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 131,
            column: 61
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 131,
            column: 4
          },
          end: {
            line: 131,
            column: 61
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 131
      },
      "11": {
        loc: {
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 136,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 136,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "12": {
        loc: {
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 82
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 4
          },
          end: {
            line: 137,
            column: 82
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "13": {
        loc: {
          start: {
            line: 137,
            column: 44
          },
          end: {
            line: 137,
            column: 81
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 137,
            column: 44
          },
          end: {
            line: 137,
            column: 60
          }
        }, {
          start: {
            line: 137,
            column: 64
          },
          end: {
            line: 137,
            column: 81
          }
        }],
        line: 137
      },
      "14": {
        loc: {
          start: {
            line: 138,
            column: 4
          },
          end: {
            line: 138,
            column: 58
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 138,
            column: 4
          },
          end: {
            line: 138,
            column: 58
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 138
      },
      "15": {
        loc: {
          start: {
            line: 153,
            column: 10
          },
          end: {
            line: 153,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 153,
            column: 10
          },
          end: {
            line: 153,
            column: 19
          }
        }, {
          start: {
            line: 153,
            column: 23
          },
          end: {
            line: 153,
            column: 45
          }
        }],
        line: 153
      },
      "16": {
        loc: {
          start: {
            line: 154,
            column: 10
          },
          end: {
            line: 154,
            column: 50
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 154,
            column: 10
          },
          end: {
            line: 154,
            column: 24
          }
        }, {
          start: {
            line: 154,
            column: 28
          },
          end: {
            line: 154,
            column: 50
          }
        }],
        line: 154
      },
      "17": {
        loc: {
          start: {
            line: 155,
            column: 10
          },
          end: {
            line: 155,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 155,
            column: 10
          },
          end: {
            line: 155,
            column: 20
          }
        }, {
          start: {
            line: 155,
            column: 24
          },
          end: {
            line: 155,
            column: 47
          }
        }],
        line: 155
      },
      "18": {
        loc: {
          start: {
            line: 158,
            column: 9
          },
          end: {
            line: 162,
            column: 9
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 158,
            column: 9
          },
          end: {
            line: 158,
            column: 23
          }
        }, {
          start: {
            line: 159,
            column: 10
          },
          end: {
            line: 161,
            column: 17
          }
        }],
        line: 158
      },
      "19": {
        loc: {
          start: {
            line: 169,
            column: 11
          },
          end: {
            line: 173,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 11
          },
          end: {
            line: 169,
            column: 20
          }
        }, {
          start: {
            line: 169,
            column: 24
          },
          end: {
            line: 169,
            column: 39
          }
        }, {
          start: {
            line: 170,
            column: 12
          },
          end: {
            line: 172,
            column: 19
          }
        }],
        line: 169
      },
      "20": {
        loc: {
          start: {
            line: 182,
            column: 11
          },
          end: {
            line: 186,
            column: 11
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 182,
            column: 11
          },
          end: {
            line: 182,
            column: 25
          }
        }, {
          start: {
            line: 182,
            column: 29
          },
          end: {
            line: 182,
            column: 43
          }
        }, {
          start: {
            line: 183,
            column: 12
          },
          end: {
            line: 185,
            column: 19
          }
        }],
        line: 182
      },
      "21": {
        loc: {
          start: {
            line: 199,
            column: 11
          },
          end: {
            line: 219,
            column: 11
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 200,
            column: 12
          },
          end: {
            line: 204,
            column: 14
          }
        }, {
          start: {
            line: 206,
            column: 12
          },
          end: {
            line: 218,
            column: 14
          }
        }],
        line: 199
      },
      "22": {
        loc: {
          start: {
            line: 208,
            column: 16
          },
          end: {
            line: 210,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 208,
            column: 35
          },
          end: {
            line: 208,
            column: 44
          }
        }, {
          start: {
            line: 209,
            column: 16
          },
          end: {
            line: 210,
            column: 62
          }
        }],
        line: 208
      },
      "23": {
        loc: {
          start: {
            line: 209,
            column: 16
          },
          end: {
            line: 210,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 209,
            column: 37
          },
          end: {
            line: 209,
            column: 48
          }
        }, {
          start: {
            line: 210,
            column: 16
          },
          end: {
            line: 210,
            column: 62
          }
        }],
        line: 209
      },
      "24": {
        loc: {
          start: {
            line: 210,
            column: 16
          },
          end: {
            line: 210,
            column: 62
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 210,
            column: 35
          },
          end: {
            line: 210,
            column: 48
          }
        }, {
          start: {
            line: 210,
            column: 51
          },
          end: {
            line: 210,
            column: 62
          }
        }],
        line: 210
      },
      "25": {
        loc: {
          start: {
            line: 215,
            column: 16
          },
          end: {
            line: 215,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 215,
            column: 16
          },
          end: {
            line: 215,
            column: 30
          }
        }, {
          start: {
            line: 215,
            column: 34
          },
          end: {
            line: 215,
            column: 54
          }
        }],
        line: 215
      },
      "26": {
        loc: {
          start: {
            line: 217,
            column: 23
          },
          end: {
            line: 217,
            column: 61
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 217,
            column: 40
          },
          end: {
            line: 217,
            column: 49
          }
        }, {
          start: {
            line: 217,
            column: 52
          },
          end: {
            line: 217,
            column: 61
          }
        }],
        line: 217
      },
      "27": {
        loc: {
          start: {
            line: 225,
            column: 2
          },
          end: {
            line: 232,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 225,
            column: 2
          },
          end: {
            line: 232,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 225
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0, 0, 0],
      "7": [0, 0, 0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0, 0],
      "20": [0, 0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c50a432a5d2c72e2fa56d7a143fd3b9b17054ab4"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2p11j46nvo = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2p11j46nvo();
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, ActivityIndicator } from 'react-native';
import { Check, Crown, Star, Zap } from 'lucide-react-native';
import { Card } from "../../../components/ui/Card";
import { Button } from "../../../components/ui/Button";
import { paymentService } from "../../services/payment/PaymentService";
import { useAuth } from "../../../contexts/AuthContext";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_2p11j46nvo().s[0]++, {
  primary: '#23ba16',
  secondary: '#1a5e1a',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  blue: '#3b82f6',
  purple: '#8b5cf6',
  gold: '#f59e0b'
});
export function SubscriptionPlans(_ref) {
  var currentTierId = _ref.currentTierId,
    onSelectPlan = _ref.onSelectPlan,
    onUpgrade = _ref.onUpgrade,
    _ref$showCurrentPlan = _ref.showCurrentPlan,
    showCurrentPlan = _ref$showCurrentPlan === void 0 ? (cov_2p11j46nvo().b[0][0]++, true) : _ref$showCurrentPlan;
  cov_2p11j46nvo().f[0]++;
  var _ref2 = (cov_2p11j46nvo().s[1]++, useAuth()),
    user = _ref2.user;
  var _ref3 = (cov_2p11j46nvo().s[2]++, useState([])),
    _ref4 = _slicedToArray(_ref3, 2),
    tiers = _ref4[0],
    setTiers = _ref4[1];
  var _ref5 = (cov_2p11j46nvo().s[3]++, useState(true)),
    _ref6 = _slicedToArray(_ref5, 2),
    loading = _ref6[0],
    setLoading = _ref6[1];
  var _ref7 = (cov_2p11j46nvo().s[4]++, useState(null)),
    _ref8 = _slicedToArray(_ref7, 2),
    selectedTier = _ref8[0],
    setSelectedTier = _ref8[1];
  cov_2p11j46nvo().s[5]++;
  useEffect(function () {
    cov_2p11j46nvo().f[1]++;
    cov_2p11j46nvo().s[6]++;
    loadSubscriptionTiers();
  }, []);
  cov_2p11j46nvo().s[7]++;
  var loadSubscriptionTiers = function () {
    var _ref9 = _asyncToGenerator(function* () {
      cov_2p11j46nvo().f[2]++;
      cov_2p11j46nvo().s[8]++;
      try {
        var availableTiers = (cov_2p11j46nvo().s[9]++, paymentService.getSubscriptionTiers());
        cov_2p11j46nvo().s[10]++;
        setTiers(availableTiers);
      } catch (error) {
        cov_2p11j46nvo().s[11]++;
        console.error('Failed to load subscription tiers:', error);
        cov_2p11j46nvo().s[12]++;
        Alert.alert('Error', 'Failed to load subscription plans');
      } finally {
        cov_2p11j46nvo().s[13]++;
        setLoading(false);
      }
    });
    return function loadSubscriptionTiers() {
      return _ref9.apply(this, arguments);
    };
  }();
  cov_2p11j46nvo().s[14]++;
  var handleSelectPlan = function handleSelectPlan(tier) {
    cov_2p11j46nvo().f[3]++;
    cov_2p11j46nvo().s[15]++;
    if (!user) {
      cov_2p11j46nvo().b[1][0]++;
      cov_2p11j46nvo().s[16]++;
      Alert.alert('Sign In Required', 'Please sign in to select a subscription plan');
      cov_2p11j46nvo().s[17]++;
      return;
    } else {
      cov_2p11j46nvo().b[1][1]++;
    }
    cov_2p11j46nvo().s[18]++;
    setSelectedTier(tier.id);
    cov_2p11j46nvo().s[19]++;
    if (tier.id === 'free') {
      cov_2p11j46nvo().b[2][0]++;
      cov_2p11j46nvo().s[20]++;
      onSelectPlan == null || onSelectPlan(tier);
      cov_2p11j46nvo().s[21]++;
      return;
    } else {
      cov_2p11j46nvo().b[2][1]++;
    }
    cov_2p11j46nvo().s[22]++;
    if ((cov_2p11j46nvo().b[4][0]++, currentTierId) && (cov_2p11j46nvo().b[4][1]++, currentTierId !== 'free')) {
      cov_2p11j46nvo().b[3][0]++;
      cov_2p11j46nvo().s[23]++;
      onUpgrade == null || onUpgrade(tier);
    } else {
      cov_2p11j46nvo().b[3][1]++;
      cov_2p11j46nvo().s[24]++;
      onSelectPlan == null || onSelectPlan(tier);
    }
  };
  cov_2p11j46nvo().s[25]++;
  var formatPrice = function formatPrice(price, currency, interval) {
    cov_2p11j46nvo().f[4]++;
    cov_2p11j46nvo().s[26]++;
    if (price === 0) {
      cov_2p11j46nvo().b[5][0]++;
      cov_2p11j46nvo().s[27]++;
      return 'Free';
    } else {
      cov_2p11j46nvo().b[5][1]++;
    }
    var formattedPrice = (cov_2p11j46nvo().s[28]++, (price / 100).toFixed(2));
    cov_2p11j46nvo().s[29]++;
    return `$${formattedPrice}/${interval}`;
  };
  cov_2p11j46nvo().s[30]++;
  var getTierIcon = function getTierIcon(tierId) {
    cov_2p11j46nvo().f[5]++;
    cov_2p11j46nvo().s[31]++;
    switch (tierId) {
      case 'free':
        cov_2p11j46nvo().b[6][0]++;
        cov_2p11j46nvo().s[32]++;
        return _jsx(Star, {
          size: 24,
          color: colors.gray
        });
      case 'pro':
        cov_2p11j46nvo().b[6][1]++;
        cov_2p11j46nvo().s[33]++;
        return _jsx(Zap, {
          size: 24,
          color: colors.primary
        });
      case 'premium':
        cov_2p11j46nvo().b[6][2]++;
        cov_2p11j46nvo().s[34]++;
        return _jsx(Crown, {
          size: 24,
          color: colors.gold
        });
      default:
        cov_2p11j46nvo().b[6][3]++;
        cov_2p11j46nvo().s[35]++;
        return _jsx(Star, {
          size: 24,
          color: colors.gray
        });
    }
  };
  cov_2p11j46nvo().s[36]++;
  var getTierColor = function getTierColor(tierId) {
    cov_2p11j46nvo().f[6]++;
    cov_2p11j46nvo().s[37]++;
    switch (tierId) {
      case 'free':
        cov_2p11j46nvo().b[7][0]++;
        cov_2p11j46nvo().s[38]++;
        return colors.gray;
      case 'pro':
        cov_2p11j46nvo().b[7][1]++;
        cov_2p11j46nvo().s[39]++;
        return colors.primary;
      case 'premium':
        cov_2p11j46nvo().b[7][2]++;
        cov_2p11j46nvo().s[40]++;
        return colors.gold;
      default:
        cov_2p11j46nvo().b[7][3]++;
        cov_2p11j46nvo().s[41]++;
        return colors.gray;
    }
  };
  cov_2p11j46nvo().s[42]++;
  var isCurrentPlan = function isCurrentPlan(tierId) {
    cov_2p11j46nvo().f[7]++;
    cov_2p11j46nvo().s[43]++;
    return currentTierId === tierId;
  };
  cov_2p11j46nvo().s[44]++;
  var canUpgrade = function canUpgrade(tierId) {
    cov_2p11j46nvo().f[8]++;
    cov_2p11j46nvo().s[45]++;
    if ((cov_2p11j46nvo().b[9][0]++, !currentTierId) || (cov_2p11j46nvo().b[9][1]++, currentTierId === 'free')) {
      cov_2p11j46nvo().b[8][0]++;
      cov_2p11j46nvo().s[46]++;
      return tierId !== 'free';
    } else {
      cov_2p11j46nvo().b[8][1]++;
    }
    cov_2p11j46nvo().s[47]++;
    if (currentTierId === 'pro') {
      cov_2p11j46nvo().b[10][0]++;
      cov_2p11j46nvo().s[48]++;
      return tierId === 'premium';
    } else {
      cov_2p11j46nvo().b[10][1]++;
    }
    cov_2p11j46nvo().s[49]++;
    return false;
  };
  cov_2p11j46nvo().s[50]++;
  var canDowngrade = function canDowngrade(tierId) {
    cov_2p11j46nvo().f[9]++;
    cov_2p11j46nvo().s[51]++;
    if (!currentTierId) {
      cov_2p11j46nvo().b[11][0]++;
      cov_2p11j46nvo().s[52]++;
      return false;
    } else {
      cov_2p11j46nvo().b[11][1]++;
    }
    cov_2p11j46nvo().s[53]++;
    if (currentTierId === 'premium') {
      cov_2p11j46nvo().b[12][0]++;
      cov_2p11j46nvo().s[54]++;
      return (cov_2p11j46nvo().b[13][0]++, tierId === 'pro') || (cov_2p11j46nvo().b[13][1]++, tierId === 'free');
    } else {
      cov_2p11j46nvo().b[12][1]++;
    }
    cov_2p11j46nvo().s[55]++;
    if (currentTierId === 'pro') {
      cov_2p11j46nvo().b[14][0]++;
      cov_2p11j46nvo().s[56]++;
      return tierId === 'free';
    } else {
      cov_2p11j46nvo().b[14][1]++;
    }
    cov_2p11j46nvo().s[57]++;
    return false;
  };
  cov_2p11j46nvo().s[58]++;
  var renderPlanCard = function renderPlanCard(tier) {
    cov_2p11j46nvo().f[10]++;
    var isCurrent = (cov_2p11j46nvo().s[59]++, isCurrentPlan(tier.id));
    var canUpgradeToThis = (cov_2p11j46nvo().s[60]++, canUpgrade(tier.id));
    var canDowngradeToThis = (cov_2p11j46nvo().s[61]++, canDowngrade(tier.id));
    var isSelected = (cov_2p11j46nvo().s[62]++, selectedTier === tier.id);
    cov_2p11j46nvo().s[63]++;
    return _jsxs(Card, {
      style: [styles.planCard, (cov_2p11j46nvo().b[15][0]++, isCurrent) && (cov_2p11j46nvo().b[15][1]++, styles.currentPlanCard), (cov_2p11j46nvo().b[16][0]++, tier.isPopular) && (cov_2p11j46nvo().b[16][1]++, styles.popularPlanCard), (cov_2p11j46nvo().b[17][0]++, isSelected) && (cov_2p11j46nvo().b[17][1]++, styles.selectedPlanCard)],
      children: [(cov_2p11j46nvo().b[18][0]++, tier.isPopular) && (cov_2p11j46nvo().b[18][1]++, _jsx(View, {
        style: styles.popularBadge,
        children: _jsx(Text, {
          style: styles.popularBadgeText,
          children: "Most Popular"
        })
      })), _jsxs(View, {
        style: styles.planHeader,
        children: [getTierIcon(tier.id), _jsx(Text, {
          style: [styles.planName, {
            color: getTierColor(tier.id)
          }],
          children: tier.name
        }), (cov_2p11j46nvo().b[19][0]++, isCurrent) && (cov_2p11j46nvo().b[19][1]++, showCurrentPlan) && (cov_2p11j46nvo().b[19][2]++, _jsx(View, {
          style: styles.currentBadge,
          children: _jsx(Text, {
            style: styles.currentBadgeText,
            children: "Current"
          })
        }))]
      }), _jsx(Text, {
        style: styles.planDescription,
        children: tier.description
      }), _jsxs(View, {
        style: styles.priceContainer,
        children: [_jsx(Text, {
          style: styles.price,
          children: formatPrice(tier.price, tier.currency, tier.interval)
        }), (cov_2p11j46nvo().b[20][0]++, tier.trialDays) && (cov_2p11j46nvo().b[20][1]++, tier.price > 0) && (cov_2p11j46nvo().b[20][2]++, _jsxs(Text, {
          style: styles.trialText,
          children: [tier.trialDays, "-day free trial"]
        }))]
      }), _jsx(View, {
        style: styles.featuresContainer,
        children: tier.features.map(function (feature, index) {
          cov_2p11j46nvo().f[11]++;
          cov_2p11j46nvo().s[64]++;
          return _jsxs(View, {
            style: styles.featureItem,
            children: [_jsx(Check, {
              size: 16,
              color: colors.primary
            }), _jsx(Text, {
              style: styles.featureText,
              children: feature
            })]
          }, index);
        })
      }), _jsx(View, {
        style: styles.planActions,
        children: isCurrent ? (cov_2p11j46nvo().b[21][0]++, _jsx(Button, {
          title: "Current Plan",
          disabled: true,
          style: styles.currentButton
        })) : (cov_2p11j46nvo().b[21][1]++, _jsx(Button, {
          title: canUpgradeToThis ? (cov_2p11j46nvo().b[22][0]++, 'Upgrade') : (cov_2p11j46nvo().b[22][1]++, canDowngradeToThis ? (cov_2p11j46nvo().b[23][0]++, 'Downgrade') : (cov_2p11j46nvo().b[23][1]++, tier.price === 0 ? (cov_2p11j46nvo().b[24][0]++, 'Select Free') : (cov_2p11j46nvo().b[24][1]++, 'Subscribe'))),
          onPress: function onPress() {
            cov_2p11j46nvo().f[12]++;
            cov_2p11j46nvo().s[65]++;
            return handleSelectPlan(tier);
          },
          style: [styles.selectButton, (cov_2p11j46nvo().b[25][0]++, tier.isPopular) && (cov_2p11j46nvo().b[25][1]++, styles.popularButton)],
          variant: tier.isPopular ? (cov_2p11j46nvo().b[26][0]++, 'primary') : (cov_2p11j46nvo().b[26][1]++, 'outline')
        }))
      })]
    }, tier.id);
  };
  cov_2p11j46nvo().s[66]++;
  if (loading) {
    cov_2p11j46nvo().b[27][0]++;
    cov_2p11j46nvo().s[67]++;
    return _jsxs(View, {
      style: styles.loadingContainer,
      children: [_jsx(ActivityIndicator, {
        size: "large",
        color: colors.primary
      }), _jsx(Text, {
        style: styles.loadingText,
        children: "Loading subscription plans..."
      })]
    });
  } else {
    cov_2p11j46nvo().b[27][1]++;
  }
  cov_2p11j46nvo().s[68]++;
  return _jsxs(ScrollView, {
    style: styles.container,
    showsVerticalScrollIndicator: false,
    children: [_jsxs(View, {
      style: styles.header,
      children: [_jsx(Text, {
        style: styles.title,
        children: "Choose Your Plan"
      }), _jsx(Text, {
        style: styles.subtitle,
        children: "Unlock advanced features and AI-powered coaching insights"
      })]
    }), _jsx(View, {
      style: styles.plansContainer,
      children: tiers.map(renderPlanCard)
    }), _jsx(View, {
      style: styles.footer,
      children: _jsx(Text, {
        style: styles.footerText,
        children: "All plans include basic match recording and score tracking. Upgrade anytime to unlock premium features."
      })
    })]
  });
}
var styles = (cov_2p11j46nvo().s[69]++, StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.lightGray
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.lightGray
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: colors.gray
  },
  header: {
    padding: 20,
    alignItems: 'center'
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: colors.dark,
    marginBottom: 8
  },
  subtitle: {
    fontSize: 16,
    color: colors.gray,
    textAlign: 'center',
    lineHeight: 22
  },
  plansContainer: {
    paddingHorizontal: 20,
    gap: 16
  },
  planCard: {
    padding: 20,
    position: 'relative',
    borderWidth: 2,
    borderColor: 'transparent'
  },
  currentPlanCard: {
    borderColor: colors.primary,
    backgroundColor: colors.white
  },
  popularPlanCard: {
    borderColor: colors.gold,
    backgroundColor: colors.white
  },
  selectedPlanCard: {
    borderColor: colors.blue,
    backgroundColor: colors.white
  },
  popularBadge: {
    position: 'absolute',
    top: -10,
    left: 20,
    backgroundColor: colors.gold,
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12
  },
  popularBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.white
  },
  planHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8
  },
  planName: {
    fontSize: 20,
    fontWeight: 'bold',
    flex: 1
  },
  currentBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 8
  },
  currentBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    color: colors.white
  },
  planDescription: {
    fontSize: 14,
    color: colors.gray,
    marginBottom: 16,
    lineHeight: 20
  },
  priceContainer: {
    marginBottom: 20
  },
  price: {
    fontSize: 32,
    fontWeight: 'bold',
    color: colors.dark
  },
  trialText: {
    fontSize: 12,
    color: colors.primary,
    fontWeight: '500',
    marginTop: 4
  },
  featuresContainer: {
    marginBottom: 24,
    gap: 8
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  featureText: {
    fontSize: 14,
    color: colors.dark,
    flex: 1
  },
  planActions: {
    marginTop: 'auto'
  },
  selectButton: {
    width: '100%'
  },
  popularButton: {
    backgroundColor: colors.gold,
    borderColor: colors.gold
  },
  currentButton: {
    backgroundColor: colors.lightGray,
    borderColor: colors.gray
  },
  footer: {
    padding: 20,
    alignItems: 'center'
  },
  footerText: {
    fontSize: 12,
    color: colors.gray,
    textAlign: 'center',
    lineHeight: 18
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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