0b5593fe2e4810aabeabcb0d598aef32
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _View = _interopRequireDefault(require("../View"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
var _excluded = ["active", "children", "onRequestClose", "transparent"];
var ModalContent = React.forwardRef(function (props, forwardedRef) {
  var active = props.active,
    children = props.children,
    onRequestClose = props.onRequestClose,
    transparent = props.transparent,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  React.useEffect(function () {
    if (_canUseDom.default) {
      var closeOnEscape = function closeOnEscape(e) {
        if (active && e.key === 'Escape') {
          e.stopPropagation();
          if (onRequestClose) {
            onRequestClose();
          }
        }
      };
      document.addEventListener('keyup', closeOnEscape, false);
      return function () {
        return document.removeEventListener('keyup', closeOnEscape, false);
      };
    }
  }, [active, onRequestClose]);
  var style = React.useMemo(function () {
    return [styles.modal, transparent ? styles.modalTransparent : styles.modalOpaque];
  }, [transparent]);
  return React.createElement(_View.default, (0, _extends2.default)({}, rest, {
    "aria-modal": true,
    ref: forwardedRef,
    role: active ? 'dialog' : null,
    style: style
  }), React.createElement(_View.default, {
    style: styles.container
  }, children));
});
var styles = _StyleSheet.default.create({
  modal: {
    position: 'fixed',
    top: 0,
    right: 0,
    bottom: 0,
    left: 0
  },
  modalTransparent: {
    backgroundColor: 'transparent'
  },
  modalOpaque: {
    backgroundColor: 'white'
  },
  container: {
    top: 0,
    flex: 1
  }
});
var _default = exports.default = ModalContent;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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