70283c9dc828d93dac00d1ec80742fe8
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AnimatedInterpolation = _interopRequireDefault(require("./AnimatedInterpolation"));
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedWithChildren = _interopRequireDefault(require("./AnimatedWithChildren"));
var AnimatedDivision = function (_AnimatedWithChildren2) {
  function AnimatedDivision(a, b) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedDivision);
    _this = _callSuper(this, AnimatedDivision);
    _this._warnedAboutDivideByZero = false;
    if (b === 0 || b instanceof _AnimatedNode.default && b.__getValue() === 0) {
      console.error('Detected potential division by zero in AnimatedDivision');
    }
    _this._a = typeof a === 'number' ? new _AnimatedValue.default(a) : a;
    _this._b = typeof b === 'number' ? new _AnimatedValue.default(b) : b;
    return _this;
  }
  (0, _inherits2.default)(AnimatedDivision, _AnimatedWithChildren2);
  return (0, _createClass2.default)(AnimatedDivision, [{
    key: "__makeNative",
    value: function __makeNative(platformConfig) {
      this._a.__makeNative(platformConfig);
      this._b.__makeNative(platformConfig);
      _superPropGet(AnimatedDivision, "__makeNative", this, 3)([platformConfig]);
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      var a = this._a.__getValue();
      var b = this._b.__getValue();
      if (b === 0) {
        if (!this._warnedAboutDivideByZero) {
          console.error('Detected division by zero in AnimatedDivision');
          this._warnedAboutDivideByZero = true;
        }
        return 0;
      }
      this._warnedAboutDivideByZero = false;
      return a / b;
    }
  }, {
    key: "interpolate",
    value: function interpolate(config) {
      return new _AnimatedInterpolation.default(this, config);
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._a.__addChild(this);
      this._b.__addChild(this);
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._a.__removeChild(this);
      this._b.__removeChild(this);
      _superPropGet(AnimatedDivision, "__detach", this, 3)([]);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      return {
        type: 'division',
        input: [this._a.__getNativeTag(), this._b.__getNativeTag()]
      };
    }
  }]);
}(_AnimatedWithChildren.default);
var _default = exports.default = AnimatedDivision;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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