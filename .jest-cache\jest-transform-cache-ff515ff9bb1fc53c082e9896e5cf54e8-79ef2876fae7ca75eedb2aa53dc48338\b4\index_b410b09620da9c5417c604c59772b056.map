{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_Platform", "_UIManager", "__DEV__", "process", "env", "NODE_ENV", "configureNext", "config", "onAnimationDidEnd", "isTesting", "configureNextLayoutAnimation", "create", "duration", "type", "property", "update", "delete", "Presets", "easeInEaseOut", "linear", "spring", "springDamping", "LayoutAnimation", "Types", "Object", "freeze", "easeIn", "easeOut", "keyboard", "Properties", "opacity", "scaleX", "scaleY", "scaleXY", "checkConfig", "console", "error", "bind", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _Platform = _interopRequireDefault(require(\"../../../exports/Platform\"));\nvar _UIManager = _interopRequireDefault(require(\"../../../exports/UIManager\"));\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nfunction configureNext(config, onAnimationDidEnd) {\n  if (!_Platform.default.isTesting) {\n    _UIManager.default.configureNextLayoutAnimation(config, onAnimationDidEnd !== null && onAnimationDidEnd !== void 0 ? onAnimationDidEnd : function () {}, function () {} /* unused onError */);\n  }\n}\nfunction create(duration, type, property) {\n  return {\n    duration,\n    create: {\n      type,\n      property\n    },\n    update: {\n      type\n    },\n    delete: {\n      type,\n      property\n    }\n  };\n}\nvar Presets = {\n  easeInEaseOut: create(300, 'easeInEaseOut', 'opacity'),\n  linear: create(500, 'linear', 'opacity'),\n  spring: {\n    duration: 700,\n    create: {\n      type: 'linear',\n      property: 'opacity'\n    },\n    update: {\n      type: 'spring',\n      springDamping: 0.4\n    },\n    delete: {\n      type: 'linear',\n      property: 'opacity'\n    }\n  }\n};\n\n/**\n * Automatically animates views to their new positions when the\n * next layout happens.\n *\n * A common way to use this API is to call it before calling `setState`.\n *\n * Note that in order to get this to work on **Android** you need to set the following flags via `UIManager`:\n *\n *     UIManager.setLayoutAnimationEnabledExperimental && UIManager.setLayoutAnimationEnabledExperimental(true);\n */\nvar LayoutAnimation = {\n  /**\n   * Schedules an animation to happen on the next layout.\n   *\n   * @param config Specifies animation properties:\n   *\n   *   - `duration` in milliseconds\n   *   - `create`, `AnimationConfig` for animating in new views\n   *   - `update`, `AnimationConfig` for animating views that have been updated\n   *\n   * @param onAnimationDidEnd Called when the animation finished.\n   * Only supported on iOS.\n   * @param onError Called on error. Only supported on iOS.\n   */\n  configureNext,\n  /**\n   * Helper for creating a config for `configureNext`.\n   */\n  create,\n  Types: Object.freeze({\n    spring: 'spring',\n    linear: 'linear',\n    easeInEaseOut: 'easeInEaseOut',\n    easeIn: 'easeIn',\n    easeOut: 'easeOut',\n    keyboard: 'keyboard'\n  }),\n  Properties: Object.freeze({\n    opacity: 'opacity',\n    scaleX: 'scaleX',\n    scaleY: 'scaleY',\n    scaleXY: 'scaleXY'\n  }),\n  checkConfig() {\n    console.error('LayoutAnimation.checkConfig(...) has been disabled.');\n  },\n  Presets,\n  easeInEaseOut: configureNext.bind(null, Presets.easeInEaseOut),\n  linear: configureNext.bind(null, Presets.linear),\n  spring: configureNext.bind(null, Presets.spring)\n};\nvar _default = exports.default = LayoutAnimation;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,SAAS,GAAGL,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AAC5E,IAAIK,UAAU,GAAGN,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AAC9E,IAAIM,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACnD,SAASC,aAAaA,CAACC,MAAM,EAAEC,iBAAiB,EAAE;EAChD,IAAI,CAACR,SAAS,CAACH,OAAO,CAACY,SAAS,EAAE;IAChCR,UAAU,CAACJ,OAAO,CAACa,4BAA4B,CAACH,MAAM,EAAEC,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAGA,iBAAiB,GAAG,YAAY,CAAC,CAAC,EAAE,YAAY,CAAC,CAAsB,CAAC;EAC/L;AACF;AACA,SAASG,MAAMA,CAACC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAE;EACxC,OAAO;IACLF,QAAQ,EAARA,QAAQ;IACRD,MAAM,EAAE;MACNE,IAAI,EAAJA,IAAI;MACJC,QAAQ,EAARA;IACF,CAAC;IACDC,MAAM,EAAE;MACNF,IAAI,EAAJA;IACF,CAAC;IACDG,MAAM,EAAE;MACNH,IAAI,EAAJA,IAAI;MACJC,QAAQ,EAARA;IACF;EACF,CAAC;AACH;AACA,IAAIG,OAAO,GAAG;EACZC,aAAa,EAAEP,MAAM,CAAC,GAAG,EAAE,eAAe,EAAE,SAAS,CAAC;EACtDQ,MAAM,EAAER,MAAM,CAAC,GAAG,EAAE,QAAQ,EAAE,SAAS,CAAC;EACxCS,MAAM,EAAE;IACNR,QAAQ,EAAE,GAAG;IACbD,MAAM,EAAE;MACNE,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE;IACZ,CAAC;IACDC,MAAM,EAAE;MACNF,IAAI,EAAE,QAAQ;MACdQ,aAAa,EAAE;IACjB,CAAC;IACDL,MAAM,EAAE;MACNH,IAAI,EAAE,QAAQ;MACdC,QAAQ,EAAE;IACZ;EACF;AACF,CAAC;AAYD,IAAIQ,eAAe,GAAG;EAcpBhB,aAAa,EAAbA,aAAa;EAIbK,MAAM,EAANA,MAAM;EACNY,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IACnBL,MAAM,EAAE,QAAQ;IAChBD,MAAM,EAAE,QAAQ;IAChBD,aAAa,EAAE,eAAe;IAC9BQ,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE,SAAS;IAClBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACFC,UAAU,EAAEL,MAAM,CAACC,MAAM,CAAC;IACxBK,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,QAAQ;IAChBC,OAAO,EAAE;EACX,CAAC,CAAC;EACFC,WAAW,WAAXA,WAAWA,CAAA,EAAG;IACZC,OAAO,CAACC,KAAK,CAAC,qDAAqD,CAAC;EACtE,CAAC;EACDnB,OAAO,EAAPA,OAAO;EACPC,aAAa,EAAEZ,aAAa,CAAC+B,IAAI,CAAC,IAAI,EAAEpB,OAAO,CAACC,aAAa,CAAC;EAC9DC,MAAM,EAAEb,aAAa,CAAC+B,IAAI,CAAC,IAAI,EAAEpB,OAAO,CAACE,MAAM,CAAC;EAChDC,MAAM,EAAEd,aAAa,CAAC+B,IAAI,CAAC,IAAI,EAAEpB,OAAO,CAACG,MAAM;AACjD,CAAC;AACD,IAAIkB,QAAQ,GAAGxC,OAAO,CAACD,OAAO,GAAGyB,eAAe;AAChDiB,MAAM,CAACzC,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}