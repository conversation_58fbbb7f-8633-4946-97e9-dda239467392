{"version": 3, "names": ["createLazyComponent", "preloadComponents", "LazyDashboard", "cov_1xzjlns84d", "s", "f", "Promise", "resolve", "then", "_interopRequireWildcard", "require", "preload", "timeout", "LazyTraining", "LazyProgress", "LazyProfile", "LazyVideoAnalysis", "LazyAICoaching", "LazyMatchAnalysis", "LazySkillAssessment", "LazySocial", "LazyLeaderboard", "LazyChallenges", "LazyPremium", "LazySubscription", "LazyAdvancedAnalytics", "LazySettings", "LazyNotifications", "LazyHelp", "LazyPrivacy", "LazyLogin", "LazyRegister", "LazyOnboarding", "initializePreloading", "userContext", "coreComponents", "name", "importFunc", "priority", "authComponents", "featureComponents", "premiumComponents", "componentsToPreload", "isAuthenticated", "b", "push", "apply", "hasCompletedOnboarding", "isPremium", "getBundleInfo", "coreScreens", "featureScreens", "socialScreens", "premiumScreens", "utilityScreens", "authScreens", "trackLazyLoadingPerformance", "bundleInfo", "getTotalScreens", "Object", "values", "flat", "length", "getLoadedScreens", "getBundleSizeEstimate", "core", "features", "social", "premium", "utility", "auth"], "sources": ["LazyScreens.tsx"], "sourcesContent": ["/**\n * Lazy-Loaded Screen Components for Bundle Splitting\n * \n * Implements route-based code splitting for all major screens\n * with performance tracking and optimized loading states.\n */\n\nimport { createLazyComponent, preloadComponents } from '@/utils/lazyLoading';\n\n// =============================================================================\n// CORE SCREENS - High Priority (Preloaded)\n// =============================================================================\n\nexport const LazyDashboard = createLazyComponent(\n  () => import('@/app/(tabs)/index'),\n  'Dashboard',\n  { preload: true, timeout: 5000 }\n);\n\nexport const LazyTraining = createLazyComponent(\n  () => import('@/app/(tabs)/training'),\n  'Training',\n  { preload: true, timeout: 5000 }\n);\n\nexport const LazyProgress = createLazyComponent(\n  () => import('@/app/(tabs)/progress'),\n  'Progress',\n  { preload: true, timeout: 5000 }\n);\n\nexport const LazyProfile = createLazyComponent(\n  () => import('@/app/(tabs)/profile'),\n  'Profile',\n  { preload: true, timeout: 5000 }\n);\n\n// =============================================================================\n// FEATURE SCREENS - Medium Priority\n// =============================================================================\n\nexport const LazyVideoAnalysis = createLazyComponent(\n  () => import('@/app/video-analysis'),\n  'VideoAnalysis',\n  { preload: false, timeout: 8000 }\n);\n\nexport const LazyAICoaching = createLazyComponent(\n  () => import('@/app/ai-coaching'),\n  'AICoaching',\n  { preload: false, timeout: 8000 }\n);\n\nexport const LazyMatchAnalysis = createLazyComponent(\n  () => import('@/app/match-analysis'),\n  'MatchAnalysis',\n  { preload: false, timeout: 8000 }\n);\n\nexport const LazySkillAssessment = createLazyComponent(\n  () => import('@/app/skill-assessment'),\n  'SkillAssessment',\n  { preload: false, timeout: 8000 }\n);\n\n// =============================================================================\n// SOCIAL FEATURES - Low Priority (Lazy Loaded)\n// =============================================================================\n\nexport const LazySocial = createLazyComponent(\n  () => import('@/app/social'),\n  'Social',\n  { preload: false, timeout: 10000 }\n);\n\nexport const LazyLeaderboard = createLazyComponent(\n  () => import('@/app/leaderboard'),\n  'Leaderboard',\n  { preload: false, timeout: 10000 }\n);\n\nexport const LazyChallenges = createLazyComponent(\n  () => import('@/app/challenges'),\n  'Challenges',\n  { preload: false, timeout: 10000 }\n);\n\n// =============================================================================\n// PREMIUM FEATURES - Low Priority (Lazy Loaded)\n// =============================================================================\n\nexport const LazyPremium = createLazyComponent(\n  () => import('@/app/premium'),\n  'Premium',\n  { preload: false, timeout: 10000 }\n);\n\nexport const LazySubscription = createLazyComponent(\n  () => import('@/app/subscription'),\n  'Subscription',\n  { preload: false, timeout: 10000 }\n);\n\nexport const LazyAdvancedAnalytics = createLazyComponent(\n  () => import('@/app/advanced-analytics'),\n  'AdvancedAnalytics',\n  { preload: false, timeout: 10000 }\n);\n\n// =============================================================================\n// SETTINGS & UTILITY SCREENS - Low Priority\n// =============================================================================\n\nexport const LazySettings = createLazyComponent(\n  () => import('@/app/settings'),\n  'Settings',\n  { preload: false, timeout: 5000 }\n);\n\nexport const LazyNotifications = createLazyComponent(\n  () => import('@/app/notifications'),\n  'Notifications',\n  { preload: false, timeout: 5000 }\n);\n\nexport const LazyHelp = createLazyComponent(\n  () => import('@/app/help'),\n  'Help',\n  { preload: false, timeout: 5000 }\n);\n\nexport const LazyPrivacy = createLazyComponent(\n  () => import('@/app/privacy'),\n  'Privacy',\n  { preload: false, timeout: 5000 }\n);\n\n// =============================================================================\n// AUTH SCREENS - Critical (Preloaded)\n// =============================================================================\n\nexport const LazyLogin = createLazyComponent(\n  () => import('@/app/auth/login'),\n  'Login',\n  { preload: true, timeout: 3000 }\n);\n\nexport const LazyRegister = createLazyComponent(\n  () => import('@/app/auth/register'),\n  'Register',\n  { preload: true, timeout: 3000 }\n);\n\nexport const LazyOnboarding = createLazyComponent(\n  () => import('@/app/(tabs)/onboarding'),\n  'Onboarding',\n  { preload: true, timeout: 5000 }\n);\n\n// =============================================================================\n// PRELOADING STRATEGY\n// =============================================================================\n\n/**\n * Initialize preloading strategy based on user context\n */\nexport function initializePreloading(userContext?: {\n  isAuthenticated: boolean;\n  isPremium: boolean;\n  hasCompletedOnboarding: boolean;\n}) {\n  const coreComponents = [\n    { name: 'Dashboard', importFunc: () => import('@/app/(tabs)/index'), priority: 'high' as const },\n    { name: 'Training', importFunc: () => import('@/app/(tabs)/training'), priority: 'high' as const },\n    { name: 'Progress', importFunc: () => import('@/app/(tabs)/progress'), priority: 'high' as const },\n  ];\n\n  const authComponents = [\n    { name: 'Login', importFunc: () => import('@/app/auth/login'), priority: 'high' as const },\n    { name: 'Register', importFunc: () => import('@/app/auth/register'), priority: 'high' as const },\n  ];\n\n  const featureComponents = [\n    { name: 'VideoAnalysis', importFunc: () => import('@/app/video-analysis'), priority: 'medium' as const },\n    { name: 'AICoaching', importFunc: () => import('@/app/ai-coaching'), priority: 'medium' as const },\n    { name: 'MatchAnalysis', importFunc: () => import('@/app/match-analysis'), priority: 'medium' as const },\n  ];\n\n  const premiumComponents = [\n    { name: 'AdvancedAnalytics', importFunc: () => import('@/app/advanced-analytics'), priority: 'low' as const },\n    { name: 'Premium', importFunc: () => import('@/app/premium'), priority: 'low' as const },\n  ];\n\n  // Determine what to preload based on user context\n  let componentsToPreload = [];\n\n  if (userContext?.isAuthenticated) {\n    componentsToPreload.push(...coreComponents);\n    \n    if (userContext.hasCompletedOnboarding) {\n      componentsToPreload.push(...featureComponents);\n    }\n    \n    if (userContext.isPremium) {\n      componentsToPreload.push(...premiumComponents);\n    }\n  } else {\n    componentsToPreload.push(...authComponents);\n  }\n\n  preloadComponents(componentsToPreload);\n}\n\n// =============================================================================\n// BUNDLE ANALYSIS UTILITIES\n// =============================================================================\n\n/**\n * Get bundle information for analysis\n */\nexport function getBundleInfo() {\n  return {\n    coreScreens: ['Dashboard', 'Training', 'Progress', 'Profile'],\n    featureScreens: ['VideoAnalysis', 'AICoaching', 'MatchAnalysis', 'SkillAssessment'],\n    socialScreens: ['Social', 'Leaderboard', 'Challenges'],\n    premiumScreens: ['Premium', 'Subscription', 'AdvancedAnalytics'],\n    utilityScreens: ['Settings', 'Notifications', 'Help', 'Privacy'],\n    authScreens: ['Login', 'Register', 'Onboarding'],\n  };\n}\n\n/**\n * Performance monitoring for lazy loading\n */\nexport function trackLazyLoadingPerformance() {\n  const bundleInfo = getBundleInfo();\n  \n  return {\n    getTotalScreens: () => {\n      return Object.values(bundleInfo).flat().length;\n    },\n    \n    getLoadedScreens: () => {\n      // This would integrate with the ComponentLoader\n      return [];\n    },\n    \n    getBundleSizeEstimate: () => {\n      // Estimate bundle sizes for different categories\n      return {\n        core: '~150KB',\n        features: '~200KB',\n        social: '~100KB',\n        premium: '~150KB',\n        utility: '~50KB',\n        auth: '~75KB',\n      };\n    },\n  };\n}\n\n// Export all lazy components as default\nexport default {\n  // Core\n  LazyDashboard,\n  LazyTraining,\n  LazyProgress,\n  LazyProfile,\n  \n  // Features\n  LazyVideoAnalysis,\n  LazyAICoaching,\n  LazyMatchAnalysis,\n  LazySkillAssessment,\n  \n  // Social\n  LazySocial,\n  LazyLeaderboard,\n  LazyChallenges,\n  \n  // Premium\n  LazyPremium,\n  LazySubscription,\n  LazyAdvancedAnalytics,\n  \n  // Utility\n  LazySettings,\n  LazyNotifications,\n  LazyHelp,\n  LazyPrivacy,\n  \n  // Auth\n  LazyLogin,\n  LazyRegister,\n  LazyOnboarding,\n  \n  // Utils\n  initializePreloading,\n  getBundleInfo,\n  trackLazyLoadingPerformance,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,mBAAmB,EAAEC,iBAAiB;AAM/C,OAAO,IAAMC,aAAa,IAAAC,cAAA,GAAAC,CAAA,OAAGJ,mBAAmB,CAC9C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA2B,CAAC,EAClC,WAAW,EACX;EAAEC,OAAO,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAK,CACjC,CAAC;AAED,OAAO,IAAMC,YAAY,IAAAV,cAAA,GAAAC,CAAA,OAAGJ,mBAAmB,CAC7C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA8B,CAAC,EACrC,UAAU,EACV;EAAEC,OAAO,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAK,CACjC,CAAC;AAED,OAAO,IAAME,YAAY,IAAAX,cAAA,GAAAC,CAAA,OAAGJ,mBAAmB,CAC7C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA8B,CAAC,EACrC,UAAU,EACV;EAAEC,OAAO,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAK,CACjC,CAAC;AAED,OAAO,IAAMG,WAAW,IAAAZ,cAAA,GAAAC,CAAA,OAAGJ,mBAAmB,CAC5C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA6B,CAAC,EACpC,SAAS,EACT;EAAEC,OAAO,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAK,CACjC,CAAC;AAMD,OAAO,IAAMI,iBAAiB,IAAAb,cAAA,GAAAC,CAAA,OAAGJ,mBAAmB,CAClD,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA6B,CAAC,EACpC,eAAe,EACf;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAK,CAClC,CAAC;AAED,OAAO,IAAMK,cAAc,IAAAd,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC/C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA0B,CAAC,EACjC,YAAY,EACZ;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAK,CAClC,CAAC;AAED,OAAO,IAAMM,iBAAiB,IAAAf,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAClD,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA6B,CAAC,EACpC,eAAe,EACf;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAK,CAClC,CAAC;AAED,OAAO,IAAMO,mBAAmB,IAAAhB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CACpD,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA+B,CAAC,EACtC,iBAAiB,EACjB;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAK,CAClC,CAAC;AAMD,OAAO,IAAMQ,UAAU,IAAAjB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC3C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAqB,CAAC,EAC5B,QAAQ,EACR;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAM,CACnC,CAAC;AAED,OAAO,IAAMS,eAAe,IAAAlB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAChD,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA0B,CAAC,EACjC,aAAa,EACb;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAM,CACnC,CAAC;AAED,OAAO,IAAMU,cAAc,IAAAnB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC/C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAyB,CAAC,EAChC,YAAY,EACZ;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAM,CACnC,CAAC;AAMD,OAAO,IAAMW,WAAW,IAAApB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC5C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAsB,CAAC,EAC7B,SAAS,EACT;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAM,CACnC,CAAC;AAED,OAAO,IAAMY,gBAAgB,IAAArB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CACjD,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA2B,CAAC,EAClC,cAAc,EACd;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAM,CACnC,CAAC;AAED,OAAO,IAAMa,qBAAqB,IAAAtB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CACtD,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAiC,CAAC,EACxC,mBAAmB,EACnB;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAM,CACnC,CAAC;AAMD,OAAO,IAAMc,YAAY,IAAAvB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC7C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAuB,CAAC,EAC9B,UAAU,EACV;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAK,CAClC,CAAC;AAED,OAAO,IAAMe,iBAAiB,IAAAxB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAClD,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA4B,CAAC,EACnC,eAAe,EACf;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAK,CAClC,CAAC;AAED,OAAO,IAAMgB,QAAQ,IAAAzB,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CACzC,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAmB,CAAC,EAC1B,MAAM,EACN;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAK,CAClC,CAAC;AAED,OAAO,IAAMiB,WAAW,IAAA1B,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC5C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAsB,CAAC,EAC7B,SAAS,EACT;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAK,CAClC,CAAC;AAMD,OAAO,IAAMkB,SAAS,IAAA3B,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC1C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAyB,CAAC,EAChC,OAAO,EACP;EAAEC,OAAO,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAK,CACjC,CAAC;AAED,OAAO,IAAMmB,YAAY,IAAA5B,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC7C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAA4B,CAAC,EACnC,UAAU,EACV;EAAEC,OAAO,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAK,CACjC,CAAC;AAED,OAAO,IAAMoB,cAAc,IAAA7B,cAAA,GAAAC,CAAA,QAAGJ,mBAAmB,CAC/C,YAAM;EAAAG,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;IAAA,OAAAC,uBAAA,CAAAC,OAAA;EAAA;AAAgC,CAAC,EACvC,YAAY,EACZ;EAAEC,OAAO,EAAE,IAAI;EAAEC,OAAO,EAAE;AAAK,CACjC,CAAC;AASD,OAAO,SAASqB,oBAAoBA,CAACC,WAIpC,EAAE;EAAA/B,cAAA,GAAAE,CAAA;EACD,IAAM8B,cAAc,IAAAhC,cAAA,GAAAC,CAAA,QAAG,CACrB;IAAEgC,IAAI,EAAE,WAAW;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAA2B,CAAC;IAAE4B,QAAQ,EAAE;EAAgB,CAAC,EAChG;IAAEF,IAAI,EAAE,UAAU;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAA8B,CAAC;IAAE4B,QAAQ,EAAE;EAAgB,CAAC,EAClG;IAAEF,IAAI,EAAE,UAAU;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAA8B,CAAC;IAAE4B,QAAQ,EAAE;EAAgB,CAAC,CACnG;EAED,IAAMC,cAAc,IAAApC,cAAA,GAAAC,CAAA,QAAG,CACrB;IAAEgC,IAAI,EAAE,OAAO;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAAyB,CAAC;IAAE4B,QAAQ,EAAE;EAAgB,CAAC,EAC1F;IAAEF,IAAI,EAAE,UAAU;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAA4B,CAAC;IAAE4B,QAAQ,EAAE;EAAgB,CAAC,CACjG;EAED,IAAME,iBAAiB,IAAArC,cAAA,GAAAC,CAAA,QAAG,CACxB;IAAEgC,IAAI,EAAE,eAAe;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAA6B,CAAC;IAAE4B,QAAQ,EAAE;EAAkB,CAAC,EACxG;IAAEF,IAAI,EAAE,YAAY;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAA0B,CAAC;IAAE4B,QAAQ,EAAE;EAAkB,CAAC,EAClG;IAAEF,IAAI,EAAE,eAAe;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAA6B,CAAC;IAAE4B,QAAQ,EAAE;EAAkB,CAAC,CACzG;EAED,IAAMG,iBAAiB,IAAAtC,cAAA,GAAAC,CAAA,QAAG,CACxB;IAAEgC,IAAI,EAAE,mBAAmB;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAAiC,CAAC;IAAE4B,QAAQ,EAAE;EAAe,CAAC,EAC7G;IAAEF,IAAI,EAAE,SAAS;IAAEC,UAAU,EAAE,SAAZA,UAAUA,CAAA,EAAQ;MAAAlC,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAAA,OAAAE,OAAA,CAAAC,OAAA,GAAAC,IAAA;QAAA,OAAAC,uBAAA,CAAAC,OAAA;MAAA;IAAsB,CAAC;IAAE4B,QAAQ,EAAE;EAAe,CAAC,CACzF;EAGD,IAAII,mBAAmB,IAAAvC,cAAA,GAAAC,CAAA,QAAG,EAAE;EAACD,cAAA,GAAAC,CAAA;EAE7B,IAAI8B,WAAW,YAAXA,WAAW,CAAES,eAAe,EAAE;IAAAxC,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAAC,CAAA;IAChCsC,mBAAmB,CAACG,IAAI,CAAAC,KAAA,CAAxBJ,mBAAmB,EAASP,cAAc,CAAC;IAAChC,cAAA,GAAAC,CAAA;IAE5C,IAAI8B,WAAW,CAACa,sBAAsB,EAAE;MAAA5C,cAAA,GAAAyC,CAAA;MAAAzC,cAAA,GAAAC,CAAA;MACtCsC,mBAAmB,CAACG,IAAI,CAAAC,KAAA,CAAxBJ,mBAAmB,EAASF,iBAAiB,CAAC;IAChD,CAAC;MAAArC,cAAA,GAAAyC,CAAA;IAAA;IAAAzC,cAAA,GAAAC,CAAA;IAED,IAAI8B,WAAW,CAACc,SAAS,EAAE;MAAA7C,cAAA,GAAAyC,CAAA;MAAAzC,cAAA,GAAAC,CAAA;MACzBsC,mBAAmB,CAACG,IAAI,CAAAC,KAAA,CAAxBJ,mBAAmB,EAASD,iBAAiB,CAAC;IAChD,CAAC;MAAAtC,cAAA,GAAAyC,CAAA;IAAA;EACH,CAAC,MAAM;IAAAzC,cAAA,GAAAyC,CAAA;IAAAzC,cAAA,GAAAC,CAAA;IACLsC,mBAAmB,CAACG,IAAI,CAAAC,KAAA,CAAxBJ,mBAAmB,EAASH,cAAc,CAAC;EAC7C;EAACpC,cAAA,GAAAC,CAAA;EAEDH,iBAAiB,CAACyC,mBAAmB,CAAC;AACxC;AASA,OAAO,SAASO,aAAaA,CAAA,EAAG;EAAA9C,cAAA,GAAAE,CAAA;EAAAF,cAAA,GAAAC,CAAA;EAC9B,OAAO;IACL8C,WAAW,EAAE,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,CAAC;IAC7DC,cAAc,EAAE,CAAC,eAAe,EAAE,YAAY,EAAE,eAAe,EAAE,iBAAiB,CAAC;IACnFC,aAAa,EAAE,CAAC,QAAQ,EAAE,aAAa,EAAE,YAAY,CAAC;IACtDC,cAAc,EAAE,CAAC,SAAS,EAAE,cAAc,EAAE,mBAAmB,CAAC;IAChEC,cAAc,EAAE,CAAC,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,SAAS,CAAC;IAChEC,WAAW,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY;EACjD,CAAC;AACH;AAKA,OAAO,SAASC,2BAA2BA,CAAA,EAAG;EAAArD,cAAA,GAAAE,CAAA;EAC5C,IAAMoD,UAAU,IAAAtD,cAAA,GAAAC,CAAA,QAAG6C,aAAa,CAAC,CAAC;EAAC9C,cAAA,GAAAC,CAAA;EAEnC,OAAO;IACLsD,eAAe,EAAE,SAAjBA,eAAeA,CAAA,EAAQ;MAAAvD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MACrB,OAAOuD,MAAM,CAACC,MAAM,CAACH,UAAU,CAAC,CAACI,IAAI,CAAC,CAAC,CAACC,MAAM;IAChD,CAAC;IAEDC,gBAAgB,EAAE,SAAlBA,gBAAgBA,CAAA,EAAQ;MAAA5D,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAEtB,OAAO,EAAE;IACX,CAAC;IAED4D,qBAAqB,EAAE,SAAvBA,qBAAqBA,CAAA,EAAQ;MAAA7D,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAC,CAAA;MAE3B,OAAO;QACL6D,IAAI,EAAE,QAAQ;QACdC,QAAQ,EAAE,QAAQ;QAClBC,MAAM,EAAE,QAAQ;QAChBC,OAAO,EAAE,QAAQ;QACjBC,OAAO,EAAE,OAAO;QAChBC,IAAI,EAAE;MACR,CAAC;IACH;EACF,CAAC;AACH;AAGA,eAAe;EAEbpE,aAAa,EAAbA,aAAa;EACbW,YAAY,EAAZA,YAAY;EACZC,YAAY,EAAZA,YAAY;EACZC,WAAW,EAAXA,WAAW;EAGXC,iBAAiB,EAAjBA,iBAAiB;EACjBC,cAAc,EAAdA,cAAc;EACdC,iBAAiB,EAAjBA,iBAAiB;EACjBC,mBAAmB,EAAnBA,mBAAmB;EAGnBC,UAAU,EAAVA,UAAU;EACVC,eAAe,EAAfA,eAAe;EACfC,cAAc,EAAdA,cAAc;EAGdC,WAAW,EAAXA,WAAW;EACXC,gBAAgB,EAAhBA,gBAAgB;EAChBC,qBAAqB,EAArBA,qBAAqB;EAGrBC,YAAY,EAAZA,YAAY;EACZC,iBAAiB,EAAjBA,iBAAiB;EACjBC,QAAQ,EAARA,QAAQ;EACRC,WAAW,EAAXA,WAAW;EAGXC,SAAS,EAATA,SAAS;EACTC,YAAY,EAAZA,YAAY;EACZC,cAAc,EAAdA,cAAc;EAGdC,oBAAoB,EAApBA,oBAAoB;EACpBgB,aAAa,EAAbA,aAAa;EACbO,2BAA2B,EAA3BA;AACF,CAAC", "ignoreList": []}