5293edf7956106411e4967ae1af8d810
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _reactNative = require("react-native");
var _browser = require("./environment/browser");
if (__DEV__ && typeof undefined === 'undefined') {
  console.warn(`The global process.env.EXPO_OS is not defined. This should be inlined by babel-preset-expo during transformation.`);
}
var nativeSelect = typeof window !== 'undefined' ? _reactNative.Platform.select : function select(specifics) {
  if (!undefined) return undefined;
  if (specifics.hasOwnProperty(undefined)) {
    return specifics[undefined];
  } else if (true && specifics.hasOwnProperty('native')) {
    return specifics.native;
  } else if (specifics.hasOwnProperty('default')) {
    return specifics.default;
  }
  return undefined;
};
var Platform = {
  OS: undefined || _reactNative.Platform.OS,
  select: nativeSelect,
  isDOMAvailable: _browser.isDOMAvailable,
  canUseEventListeners: _browser.canUseEventListeners,
  canUseViewport: _browser.canUseViewport,
  isAsyncDebugging: _browser.isAsyncDebugging
};
var _default = exports.default = Platform;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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