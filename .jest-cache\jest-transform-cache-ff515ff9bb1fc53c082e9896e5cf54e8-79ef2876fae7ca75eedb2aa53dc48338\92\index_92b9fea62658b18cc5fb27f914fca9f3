2a09309b9978959f8e8fb2a6190e1273
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = useMergeRefs;
var React = _interopRequireWildcard(require("react"));
var _mergeRefs = _interopRequireDefault(require("../mergeRefs"));
function useMergeRefs() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  return React.useMemo(function () {
    return (0, _mergeRefs.default).apply(void 0, args);
  }, [].concat(args));
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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