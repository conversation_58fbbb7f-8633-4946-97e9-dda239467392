name: 🚀 Deploy to Staging

on:
  push:
    branches: [ develop ]
  workflow_dispatch:
    inputs:
      force_deploy:
        description: 'Force deployment even if tests fail'
        required: false
        default: false
        type: boolean

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: acemind/backend

jobs:
  # Pre-deployment validation
  pre-deployment:
    name: 🔍 Pre-deployment Validation
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    outputs:
      should_deploy: ${{ steps.check.outputs.should_deploy }}
      version: ${{ steps.version.outputs.version }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔍 Run quick validation
        run: |
          npm run lint
          npm run type-check
          npm run test:ci

      - name: 📋 Check deployment conditions
        id: check
        run: |
          if [[ "${{ github.event.inputs.force_deploy }}" == "true" ]]; then
            echo "should_deploy=true" >> $GITHUB_OUTPUT
            echo "🚨 Force deployment requested"
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            echo "should_deploy=true" >> $GITHUB_OUTPUT
            echo "✅ Deploying from develop branch"
          else
            echo "should_deploy=false" >> $GITHUB_OUTPUT
            echo "❌ Deployment conditions not met"
          fi

      - name: 🏷️ Generate version
        id: version
        run: |
          VERSION="staging-$(date +%Y%m%d)-$(git rev-parse --short HEAD)"
          echo "version=$VERSION" >> $GITHUB_OUTPUT
          echo "📦 Version: $VERSION"

  # Build and push Docker images
  build-backend:
    name: 🐳 Build Backend
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: pre-deployment
    if: needs.pre-deployment.outputs.should_deploy == 'true'
    
    outputs:
      image: ${{ steps.image.outputs.image }}
      digest: ${{ steps.build.outputs.digest }}
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: 🏗️ Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 📝 Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=branch
            type=raw,value=staging
            type=raw,value=${{ needs.pre-deployment.outputs.version }}

      - name: 🔨 Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          platforms: linux/amd64,linux/arm64

      - name: 📋 Output image details
        id: image
        run: |
          echo "image=${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:staging" >> $GITHUB_OUTPUT

  # Build mobile app for staging
  build-mobile:
    name: 📱 Build Mobile App
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: pre-deployment
    if: needs.pre-deployment.outputs.should_deploy == 'true'
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🔧 Setup Expo CLI
        run: npm install -g @expo/cli eas-cli

      - name: 🔐 Expo authentication
        run: expo login --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 📱 Build for staging
        run: |
          # Update app.json for staging
          jq '.expo.extra.environment = "staging"' app.json > app.staging.json
          mv app.staging.json app.json
          
          # Build staging version
          eas build --platform all --profile staging --non-interactive
        env:
          EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}

      - name: 📤 Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: mobile-staging-build
          path: |
            *.apk
            *.ipa
          retention-days: 30

  # Deploy backend to staging
  deploy-backend:
    name: 🚀 Deploy Backend
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [pre-deployment, build-backend]
    environment: staging
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🔐 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: 🚀 Deploy to ECS
        run: |
          # Update ECS task definition
          aws ecs describe-task-definition \
            --task-definition acemind-backend-staging \
            --query taskDefinition > task-definition.json
          
          # Update image in task definition
          jq --arg IMAGE "${{ needs.build-backend.outputs.image }}" \
            '.containerDefinitions[0].image = $IMAGE' \
            task-definition.json > updated-task-definition.json
          
          # Register new task definition
          aws ecs register-task-definition \
            --cli-input-json file://updated-task-definition.json
          
          # Update service
          aws ecs update-service \
            --cluster acemind-staging \
            --service acemind-backend-staging \
            --task-definition acemind-backend-staging \
            --force-new-deployment

      - name: ⏳ Wait for deployment
        run: |
          aws ecs wait services-stable \
            --cluster acemind-staging \
            --services acemind-backend-staging

  # Deploy frontend to staging
  deploy-frontend:
    name: 🌐 Deploy Frontend
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [pre-deployment, build-mobile]
    environment: staging
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🌐 Build web version for staging
        run: npm run build:web:prod
        env:
          EXPO_PUBLIC_APP_ENV: staging
          EXPO_PUBLIC_API_URL: https://api-staging.acemind.app

      - name: 🔐 Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: 📤 Deploy to S3
        run: |
          aws s3 sync dist/ s3://${{ secrets.STAGING_S3_BUCKET }} \
            --delete \
            --cache-control "public, max-age=31536000" \
            --exclude "*.html" \
            --exclude "service-worker.js"
          
          # Upload HTML files with no cache
          aws s3 sync dist/ s3://${{ secrets.STAGING_S3_BUCKET }} \
            --delete \
            --cache-control "no-cache" \
            --include "*.html" \
            --include "service-worker.js"

      - name: 🔄 Invalidate CloudFront
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ secrets.STAGING_CLOUDFRONT_ID }} \
            --paths "/*"

  # Run post-deployment tests
  post-deployment-tests:
    name: 🧪 Post-deployment Tests
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [deploy-backend, deploy-frontend]
    
    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🏗️ Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 📦 Install dependencies
        run: npm ci

      - name: 🏥 Health check
        run: |
          echo "🔍 Checking staging health..."
          curl -f https://api-staging.acemind.app/health
          curl -f https://staging.acemind.app

      - name: 🧪 Run smoke tests
        run: npm run test:smoke
        env:
          TEST_URL: https://staging.acemind.app
          API_URL: https://api-staging.acemind.app

      - name: ⚡ Performance check
        run: |
          npx lighthouse https://staging.acemind.app \
            --chrome-flags="--headless" \
            --output=json \
            --output-path=lighthouse-staging.json
          
          # Check performance score
          SCORE=$(jq '.categories.performance.score * 100' lighthouse-staging.json)
          echo "Performance score: $SCORE"
          
          if (( $(echo "$SCORE < 80" | bc -l) )); then
            echo "⚠️ Performance score below threshold (80)"
            exit 1
          fi

  # Notify deployment status
  notify:
    name: 📢 Notify Deployment
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [pre-deployment, deploy-backend, deploy-frontend, post-deployment-tests]
    if: always()
    
    steps:
      - name: 📢 Slack notification
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          text: |
            🚀 Staging Deployment ${{ job.status }}
            
            📦 Version: ${{ needs.pre-deployment.outputs.version }}
            🌐 Frontend: https://staging.acemind.app
            🔧 Backend: https://api-staging.acemind.app
            📱 Mobile: Available in Expo
            
            🔗 View deployment: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: 📧 Email notification
        if: failure()
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: smtp.gmail.com
          server_port: 587
          username: ${{ secrets.EMAIL_USERNAME }}
          password: ${{ secrets.EMAIL_PASSWORD }}
          subject: '🚨 AceMind Staging Deployment Failed'
          to: ${{ secrets.NOTIFICATION_EMAIL }}
          from: 'AceMind CI/CD <<EMAIL>>'
          body: |
            The staging deployment for AceMind has failed.
            
            Commit: ${{ github.sha }}
            Branch: ${{ github.ref }}
            Workflow: ${{ github.workflow }}
            
            Please check the GitHub Actions logs for more details:
            ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

  # Update deployment status
  update-status:
    name: 📊 Update Status
    runs-on: ubuntu-latest
    timeout-minutes: 5
    needs: [pre-deployment, post-deployment-tests]
    if: always()
    
    steps:
      - name: 📊 Update deployment status
        uses: actions/github-script@v7
        with:
          script: |
            const { owner, repo } = context.repo;
            const sha = context.sha;
            
            const state = '${{ needs.post-deployment-tests.result }}' === 'success' ? 'success' : 'failure';
            const description = state === 'success' 
              ? '✅ Staging deployment successful' 
              : '❌ Staging deployment failed';
            
            await github.rest.repos.createCommitStatus({
              owner,
              repo,
              sha,
              state,
              target_url: 'https://staging.acemind.app',
              description,
              context: 'deployment/staging'
            });

      - name: 📝 Create deployment summary
        run: |
          echo "## 🚀 Staging Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Component | Status | URL |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|-----|" >> $GITHUB_STEP_SUMMARY
          echo "| Frontend | ✅ Deployed | https://staging.acemind.app |" >> $GITHUB_STEP_SUMMARY
          echo "| Backend API | ✅ Deployed | https://api-staging.acemind.app |" >> $GITHUB_STEP_SUMMARY
          echo "| Mobile App | ✅ Built | Expo Staging Channel |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "📦 **Version:** ${{ needs.pre-deployment.outputs.version }}" >> $GITHUB_STEP_SUMMARY
          echo "🕐 **Deployed at:** $(date -u)" >> $GITHUB_STEP_SUMMARY
