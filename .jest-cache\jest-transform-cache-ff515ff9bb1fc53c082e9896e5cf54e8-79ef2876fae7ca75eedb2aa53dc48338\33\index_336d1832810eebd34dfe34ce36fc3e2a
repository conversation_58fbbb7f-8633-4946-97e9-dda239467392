1e277f7f6afd3be146b81ba400a93b3f
"use strict";
'use client';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _invariant = _interopRequireDefault(require("fbjs/lib/invariant"));
var _unmountComponentAtNode = _interopRequireDefault(require("../unmountComponentAtNode"));
var _renderApplication = _interopRequireWildcard(require("./renderApplication"));
var emptyObject = {};
var runnables = {};
var componentProviderInstrumentationHook = function componentProviderInstrumentationHook(component) {
  return component();
};
var wrapperComponentProvider;
var AppRegistry = function () {
  function AppRegistry() {
    (0, _classCallCheck2.default)(this, AppRegistry);
  }
  return (0, _createClass2.default)(AppRegistry, null, [{
    key: "getAppKeys",
    value: function getAppKeys() {
      return Object.keys(runnables);
    }
  }, {
    key: "getApplication",
    value: function getApplication(appKey, appParameters) {
      (0, _invariant.default)(runnables[appKey] && runnables[appKey].getApplication, "Application " + appKey + " has not been registered. " + 'This is either due to an import error during initialization or failure to call AppRegistry.registerComponent.');
      return runnables[appKey].getApplication(appParameters);
    }
  }, {
    key: "registerComponent",
    value: function registerComponent(appKey, componentProvider) {
      runnables[appKey] = {
        getApplication: function getApplication(appParameters) {
          return (0, _renderApplication.getApplication)(componentProviderInstrumentationHook(componentProvider), appParameters ? appParameters.initialProps : emptyObject, wrapperComponentProvider && wrapperComponentProvider(appParameters));
        },
        run: function run(appParameters) {
          return (0, _renderApplication.default)(componentProviderInstrumentationHook(componentProvider), wrapperComponentProvider && wrapperComponentProvider(appParameters), appParameters.callback, {
            hydrate: appParameters.hydrate || false,
            initialProps: appParameters.initialProps || emptyObject,
            mode: appParameters.mode || 'concurrent',
            rootTag: appParameters.rootTag
          });
        }
      };
      return appKey;
    }
  }, {
    key: "registerConfig",
    value: function registerConfig(config) {
      config.forEach(function (_ref) {
        var appKey = _ref.appKey,
          component = _ref.component,
          run = _ref.run;
        if (run) {
          AppRegistry.registerRunnable(appKey, run);
        } else {
          (0, _invariant.default)(component, 'No component provider passed in');
          AppRegistry.registerComponent(appKey, component);
        }
      });
    }
  }, {
    key: "registerRunnable",
    value: function registerRunnable(appKey, run) {
      runnables[appKey] = {
        run: run
      };
      return appKey;
    }
  }, {
    key: "runApplication",
    value: function runApplication(appKey, appParameters) {
      var isDevelopment = process.env.NODE_ENV !== 'production' && process.env.NODE_ENV !== 'test';
      if (isDevelopment) {
        var params = (0, _objectSpread2.default)({}, appParameters);
        params.rootTag = "#" + params.rootTag.id;
        console.log("Running application \"" + appKey + "\" with appParams:\n", params, "\nDevelopment-level warnings: " + (isDevelopment ? 'ON' : 'OFF') + "." + ("\nPerformance optimizations: " + (isDevelopment ? 'OFF' : 'ON') + "."));
      }
      (0, _invariant.default)(runnables[appKey] && runnables[appKey].run, "Application \"" + appKey + "\" has not been registered. " + 'This is either due to an import error during initialization or failure to call AppRegistry.registerComponent.');
      return runnables[appKey].run(appParameters);
    }
  }, {
    key: "setComponentProviderInstrumentationHook",
    value: function setComponentProviderInstrumentationHook(hook) {
      componentProviderInstrumentationHook = hook;
    }
  }, {
    key: "setWrapperComponentProvider",
    value: function setWrapperComponentProvider(provider) {
      wrapperComponentProvider = provider;
    }
  }, {
    key: "unmountApplicationComponentAtRootTag",
    value: function unmountApplicationComponentAtRootTag(rootTag) {
      (0, _unmountComponentAtNode.default)(rootTag);
    }
  }]);
}();
exports.default = AppRegistry;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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