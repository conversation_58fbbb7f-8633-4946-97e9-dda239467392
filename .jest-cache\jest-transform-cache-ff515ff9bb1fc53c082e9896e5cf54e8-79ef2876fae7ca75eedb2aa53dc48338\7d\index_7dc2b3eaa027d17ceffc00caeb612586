2e5ec61cdf8bb82b12570cb160889dd7
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.createSheet = createSheet;
var _canUseDom = _interopRequireDefault(require("../../../modules/canUseDom"));
var _createCSSStyleSheet = _interopRequireDefault(require("./createCSSStyleSheet"));
var _createOrderedCSSStyleSheet = _interopRequireDefault(require("./createOrderedCSSStyleSheet"));
var defaultId = 'react-native-stylesheet';
var roots = new WeakMap();
var sheets = [];
var initialRules = ['html{-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:rgba(0,0,0,0);}', 'body{margin:0;}', 'button::-moz-focus-inner,input::-moz-focus-inner{border:0;padding:0;}', 'input::-webkit-search-cancel-button,input::-webkit-search-decoration,input::-webkit-search-results-button,input::-webkit-search-results-decoration{display:none;}'];
function createSheet(root, id) {
  if (id === void 0) {
    id = defaultId;
  }
  var sheet;
  if (_canUseDom.default) {
    var rootNode = root != null ? root.getRootNode() : document;
    if (sheets.length === 0) {
      sheet = (0, _createOrderedCSSStyleSheet.default)((0, _createCSSStyleSheet.default)(id));
      initialRules.forEach(function (rule) {
        sheet.insert(rule, 0);
      });
      roots.set(rootNode, sheets.length);
      sheets.push(sheet);
    } else {
      var index = roots.get(rootNode);
      if (index == null) {
        var initialSheet = sheets[0];
        var textContent = initialSheet != null ? initialSheet.getTextContent() : '';
        sheet = (0, _createOrderedCSSStyleSheet.default)((0, _createCSSStyleSheet.default)(id, rootNode, textContent));
        roots.set(rootNode, sheets.length);
        sheets.push(sheet);
      } else {
        sheet = sheets[index];
      }
    }
  } else {
    if (sheets.length === 0) {
      sheet = (0, _createOrderedCSSStyleSheet.default)((0, _createCSSStyleSheet.default)(id));
      initialRules.forEach(function (rule) {
        sheet.insert(rule, 0);
      });
      sheets.push(sheet);
    } else {
      sheet = sheets[0];
    }
  }
  return {
    getTextContent: function getTextContent() {
      return sheet.getTextContent();
    },
    id: id,
    insert: function insert(cssText, groupValue) {
      sheets.forEach(function (s) {
        s.insert(cssText, groupValue);
      });
    }
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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