{"version": 3, "names": ["exports", "__esModule", "default", "canUseDOM", "window", "document", "createElement", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar canUseDOM = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nvar _default = exports.default = canUseDOM;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAUxB,IAAIC,SAAS,GAAG,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC;AACrG,IAAIC,QAAQ,GAAGP,OAAO,CAACE,OAAO,GAAGC,SAAS;AAC1CK,MAAM,CAACR,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}