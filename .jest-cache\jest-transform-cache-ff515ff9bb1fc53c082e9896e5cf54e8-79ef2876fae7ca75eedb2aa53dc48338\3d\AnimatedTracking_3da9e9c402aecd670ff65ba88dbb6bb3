256652e928282536fb6a1cdac618ea8b
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _possibleConstructorReturn2 = _interopRequireDefault2(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault2(require("@babel/runtime/helpers/getPrototypeOf"));
var _get2 = _interopRequireDefault2(require("@babel/runtime/helpers/get"));
var _inherits2 = _interopRequireDefault2(require("@babel/runtime/helpers/inherits"));
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
function _superPropGet(t, o, e, r) { var p = (0, _get2.default)((0, _getPrototypeOf2.default)(1 & r ? t.prototype : t), o, e); return 2 & r && "function" == typeof p ? function (t) { return p.apply(e, t); } : p; }
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _AnimatedValue = _interopRequireDefault(require("./AnimatedValue"));
var _AnimatedNode = _interopRequireDefault(require("./AnimatedNode"));
var _NativeAnimatedHelper = require("../NativeAnimatedHelper");
var AnimatedTracking = function (_AnimatedNode$default) {
  function AnimatedTracking(value, parent, animationClass, animationConfig, callback) {
    var _this;
    (0, _classCallCheck2.default)(this, AnimatedTracking);
    _this = _callSuper(this, AnimatedTracking);
    _this._value = value;
    _this._parent = parent;
    _this._animationClass = animationClass;
    _this._animationConfig = animationConfig;
    _this._useNativeDriver = (0, _NativeAnimatedHelper.shouldUseNativeDriver)(animationConfig);
    _this._callback = callback;
    _this.__attach();
    return _this;
  }
  (0, _inherits2.default)(AnimatedTracking, _AnimatedNode$default);
  return (0, _createClass2.default)(AnimatedTracking, [{
    key: "__makeNative",
    value: function __makeNative() {
      this.__isNative = true;
      this._parent.__makeNative();
      _superPropGet(AnimatedTracking, "__makeNative", this, 3)([]);
      this._value.__makeNative();
    }
  }, {
    key: "__getValue",
    value: function __getValue() {
      return this._parent.__getValue();
    }
  }, {
    key: "__attach",
    value: function __attach() {
      this._parent.__addChild(this);
      if (this._useNativeDriver) {
        this.__makeNative();
      }
    }
  }, {
    key: "__detach",
    value: function __detach() {
      this._parent.__removeChild(this);
      _superPropGet(AnimatedTracking, "__detach", this, 3)([]);
    }
  }, {
    key: "update",
    value: function update() {
      this._value.animate(new this._animationClass((0, _objectSpread2.default)((0, _objectSpread2.default)({}, this._animationConfig), {}, {
        toValue: this._animationConfig.toValue.__getValue()
      })), this._callback);
    }
  }, {
    key: "__getNativeConfig",
    value: function __getNativeConfig() {
      var animation = new this._animationClass((0, _objectSpread2.default)((0, _objectSpread2.default)({}, this._animationConfig), {}, {
        toValue: undefined
      }));
      var animationConfig = animation.__getNativeAnimationConfig();
      return {
        type: 'tracking',
        animationId: (0, _NativeAnimatedHelper.generateNewAnimationId)(),
        animationConfig: animationConfig,
        toValue: this._parent.__getNativeTag(),
        value: this._value.__getNativeTag()
      };
    }
  }]);
}(_AnimatedNode.default);
var _default = exports.default = AnimatedTracking;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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