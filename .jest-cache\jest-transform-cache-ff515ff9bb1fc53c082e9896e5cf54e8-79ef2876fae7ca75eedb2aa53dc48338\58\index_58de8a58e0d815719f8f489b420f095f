6f243c324e9cc5f1aea0a48c8965f6df
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _createElement = _interopRequireDefault(require("../createElement"));
var _AssetRegistry = require("../../modules/AssetRegistry");
var _preprocess = require("../StyleSheet/preprocess");
var _ImageLoader = _interopRequireDefault(require("../../modules/ImageLoader"));
var _PixelRatio = _interopRequireDefault(require("../PixelRatio"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _TextAncestorContext = _interopRequireDefault(require("../Text/TextAncestorContext"));
var _View = _interopRequireDefault(require("../View"));
var _warnOnce = require("../../modules/warnOnce");
var _excluded = ["aria-label", "accessibilityLabel", "blurRadius", "defaultSource", "draggable", "onError", "onLayout", "onLoad", "onLoadEnd", "onLoadStart", "pointerEvents", "source", "style"];
var ERRORED = 'ERRORED';
var LOADED = 'LOADED';
var LOADING = 'LOADING';
var IDLE = 'IDLE';
var _filterId = 0;
var svgDataUriPattern = /^(data:image\/svg\+xml;utf8,)(.*)/;
function createTintColorSVG(tintColor, id) {
  return tintColor && id != null ? React.createElement("svg", {
    style: {
      position: 'absolute',
      height: 0,
      visibility: 'hidden',
      width: 0
    }
  }, React.createElement("defs", null, React.createElement("filter", {
    id: "tint-" + id,
    suppressHydrationWarning: true
  }, React.createElement("feFlood", {
    floodColor: "" + tintColor,
    key: tintColor
  }), React.createElement("feComposite", {
    in2: "SourceAlpha",
    operator: "in"
  })))) : null;
}
function extractNonStandardStyleProps(style, blurRadius, filterId, tintColorProp) {
  var flatStyle = _StyleSheet.default.flatten(style);
  var filter = flatStyle.filter,
    resizeMode = flatStyle.resizeMode,
    shadowOffset = flatStyle.shadowOffset,
    tintColor = flatStyle.tintColor;
  if (flatStyle.resizeMode) {
    (0, _warnOnce.warnOnce)('Image.style.resizeMode', 'Image: style.resizeMode is deprecated. Please use props.resizeMode.');
  }
  if (flatStyle.tintColor) {
    (0, _warnOnce.warnOnce)('Image.style.tintColor', 'Image: style.tintColor is deprecated. Please use props.tintColor.');
  }
  var filters = [];
  var _filter = null;
  if (filter) {
    filters.push(filter);
  }
  if (blurRadius) {
    filters.push("blur(" + blurRadius + "px)");
  }
  if (shadowOffset) {
    var shadowString = (0, _preprocess.createBoxShadowValue)(flatStyle);
    if (shadowString) {
      filters.push("drop-shadow(" + shadowString + ")");
    }
  }
  if ((tintColorProp || tintColor) && filterId != null) {
    filters.push("url(#tint-" + filterId + ")");
  }
  if (filters.length > 0) {
    _filter = filters.join(' ');
  }
  return [resizeMode, _filter, tintColor];
}
function resolveAssetDimensions(source) {
  if (typeof source === 'number') {
    var _getAssetByID = (0, _AssetRegistry.getAssetByID)(source),
      _height = _getAssetByID.height,
      _width = _getAssetByID.width;
    return {
      height: _height,
      width: _width
    };
  } else if (source != null && !Array.isArray(source) && typeof source === 'object') {
    var _height2 = source.height,
      _width2 = source.width;
    return {
      height: _height2,
      width: _width2
    };
  }
}
function resolveAssetUri(source) {
  var uri = null;
  if (typeof source === 'number') {
    var asset = (0, _AssetRegistry.getAssetByID)(source);
    if (asset == null) {
      throw new Error("Image: asset with ID \"" + source + "\" could not be found. Please check the image source or packager.");
    }
    var scale = asset.scales[0];
    if (asset.scales.length > 1) {
      var preferredScale = _PixelRatio.default.get();
      scale = asset.scales.reduce(function (prev, curr) {
        return Math.abs(curr - preferredScale) < Math.abs(prev - preferredScale) ? curr : prev;
      });
    }
    var scaleSuffix = scale !== 1 ? "@" + scale + "x" : '';
    uri = asset ? asset.httpServerLocation + "/" + asset.name + scaleSuffix + "." + asset.type : '';
  } else if (typeof source === 'string') {
    uri = source;
  } else if (source && typeof source.uri === 'string') {
    uri = source.uri;
  }
  if (uri) {
    var match = uri.match(svgDataUriPattern);
    if (match) {
      var prefix = match[1],
        svg = match[2];
      var encodedSvg = encodeURIComponent(svg);
      return "" + prefix + encodedSvg;
    }
  }
  return uri;
}
var Image = React.forwardRef(function (props, ref) {
  var _ariaLabel = props['aria-label'],
    accessibilityLabel = props.accessibilityLabel,
    blurRadius = props.blurRadius,
    defaultSource = props.defaultSource,
    draggable = props.draggable,
    onError = props.onError,
    onLayout = props.onLayout,
    onLoad = props.onLoad,
    onLoadEnd = props.onLoadEnd,
    onLoadStart = props.onLoadStart,
    pointerEvents = props.pointerEvents,
    source = props.source,
    style = props.style,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var ariaLabel = _ariaLabel || accessibilityLabel;
  if (process.env.NODE_ENV !== 'production') {
    if (props.children) {
      throw new Error('The <Image> component cannot contain children. If you want to render content on top of the image, consider using the <ImageBackground> component or absolute positioning.');
    }
  }
  var _React$useState = React.useState(function () {
      var uri = resolveAssetUri(source);
      if (uri != null) {
        var isLoaded = _ImageLoader.default.has(uri);
        if (isLoaded) {
          return LOADED;
        }
      }
      return IDLE;
    }),
    state = _React$useState[0],
    updateState = _React$useState[1];
  var _React$useState2 = React.useState({}),
    layout = _React$useState2[0],
    updateLayout = _React$useState2[1];
  var hasTextAncestor = React.useContext(_TextAncestorContext.default);
  var hiddenImageRef = React.useRef(null);
  var filterRef = React.useRef(_filterId++);
  var requestRef = React.useRef(null);
  var shouldDisplaySource = state === LOADED || state === LOADING && defaultSource == null;
  var _extractNonStandardSt = extractNonStandardStyleProps(style, blurRadius, filterRef.current, props.tintColor),
    _resizeMode = _extractNonStandardSt[0],
    filter = _extractNonStandardSt[1],
    _tintColor = _extractNonStandardSt[2];
  var resizeMode = props.resizeMode || _resizeMode || 'cover';
  var tintColor = props.tintColor || _tintColor;
  var selectedSource = shouldDisplaySource ? source : defaultSource;
  var displayImageUri = resolveAssetUri(selectedSource);
  var imageSizeStyle = resolveAssetDimensions(selectedSource);
  var backgroundImage = displayImageUri ? "url(\"" + displayImageUri + "\")" : null;
  var backgroundSize = getBackgroundSize();
  var hiddenImage = displayImageUri ? (0, _createElement.default)('img', {
    alt: ariaLabel || '',
    style: styles.accessibilityImage$raw,
    draggable: draggable || false,
    ref: hiddenImageRef,
    src: displayImageUri
  }) : null;
  function getBackgroundSize() {
    if (hiddenImageRef.current != null && (resizeMode === 'center' || resizeMode === 'repeat')) {
      var _hiddenImageRef$curre = hiddenImageRef.current,
        naturalHeight = _hiddenImageRef$curre.naturalHeight,
        naturalWidth = _hiddenImageRef$curre.naturalWidth;
      var _height3 = layout.height,
        _width3 = layout.width;
      if (naturalHeight && naturalWidth && _height3 && _width3) {
        var scaleFactor = Math.min(1, _width3 / naturalWidth, _height3 / naturalHeight);
        var x = Math.ceil(scaleFactor * naturalWidth);
        var y = Math.ceil(scaleFactor * naturalHeight);
        return x + "px " + y + "px";
      }
    }
  }
  function handleLayout(e) {
    if (resizeMode === 'center' || resizeMode === 'repeat' || onLayout) {
      var _layout = e.nativeEvent.layout;
      onLayout && onLayout(e);
      updateLayout(_layout);
    }
  }
  var uri = resolveAssetUri(source);
  React.useEffect(function () {
    abortPendingRequest();
    if (uri != null) {
      updateState(LOADING);
      if (onLoadStart) {
        onLoadStart();
      }
      requestRef.current = _ImageLoader.default.load(uri, function load(e) {
        updateState(LOADED);
        if (onLoad) {
          onLoad(e);
        }
        if (onLoadEnd) {
          onLoadEnd();
        }
      }, function error() {
        updateState(ERRORED);
        if (onError) {
          onError({
            nativeEvent: {
              error: "Failed to load resource " + uri
            }
          });
        }
        if (onLoadEnd) {
          onLoadEnd();
        }
      });
    }
    function abortPendingRequest() {
      if (requestRef.current != null) {
        _ImageLoader.default.abort(requestRef.current);
        requestRef.current = null;
      }
    }
    return abortPendingRequest;
  }, [uri, requestRef, updateState, onError, onLoad, onLoadEnd, onLoadStart]);
  return React.createElement(_View.default, (0, _extends2.default)({}, rest, {
    "aria-label": ariaLabel,
    onLayout: handleLayout,
    pointerEvents: pointerEvents,
    ref: ref,
    style: [styles.root, hasTextAncestor && styles.inline, imageSizeStyle, style, styles.undo, {
      boxShadow: null
    }]
  }), React.createElement(_View.default, {
    style: [styles.image, resizeModeStyles[resizeMode], {
      backgroundImage: backgroundImage,
      filter: filter
    }, backgroundSize != null && {
      backgroundSize: backgroundSize
    }],
    suppressHydrationWarning: true
  }), hiddenImage, createTintColorSVG(tintColor, filterRef.current));
});
Image.displayName = 'Image';
var ImageWithStatics = Image;
ImageWithStatics.getSize = function (uri, success, failure) {
  _ImageLoader.default.getSize(uri, success, failure);
};
ImageWithStatics.prefetch = function (uri) {
  return _ImageLoader.default.prefetch(uri);
};
ImageWithStatics.queryCache = function (uris) {
  return _ImageLoader.default.queryCache(uris);
};
var styles = _StyleSheet.default.create({
  root: {
    flexBasis: 'auto',
    overflow: 'hidden',
    zIndex: 0
  },
  inline: {
    display: 'inline-flex'
  },
  undo: {
    blurRadius: null,
    shadowColor: null,
    shadowOpacity: null,
    shadowOffset: null,
    shadowRadius: null,
    tintColor: null,
    overlayColor: null,
    resizeMode: null
  },
  image: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _StyleSheet.default.absoluteFillObject), {}, {
    backgroundColor: 'transparent',
    backgroundPosition: 'center',
    backgroundRepeat: 'no-repeat',
    backgroundSize: 'cover',
    height: '100%',
    width: '100%',
    zIndex: -1
  }),
  accessibilityImage$raw: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _StyleSheet.default.absoluteFillObject), {}, {
    height: '100%',
    opacity: 0,
    width: '100%',
    zIndex: -1
  })
});
var resizeModeStyles = _StyleSheet.default.create({
  center: {
    backgroundSize: 'auto'
  },
  contain: {
    backgroundSize: 'contain'
  },
  cover: {
    backgroundSize: 'cover'
  },
  none: {
    backgroundPosition: '0',
    backgroundSize: 'auto'
  },
  repeat: {
    backgroundPosition: '0',
    backgroundRepeat: 'repeat',
    backgroundSize: 'auto'
  },
  stretch: {
    backgroundSize: '100% 100%'
  }
});
var _default = exports.default = ImageWithStatics;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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