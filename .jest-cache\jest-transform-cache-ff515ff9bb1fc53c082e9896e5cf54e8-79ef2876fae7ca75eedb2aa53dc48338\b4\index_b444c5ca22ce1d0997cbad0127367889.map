{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_objectSpread2", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_createElement", "_StyleSheet", "_View", "_excluded", "CheckBox", "forwardRef", "props", "forwardedRef", "ariaReadOnly", "color", "disabled", "onChange", "onValueChange", "readOnly", "style", "value", "other", "handleChange", "event", "nativeEvent", "target", "checked", "fakeControl", "createElement", "styles", "fakeControlChecked", "backgroundColor", "borderColor", "fakeControlDisabled", "fakeControlCheckedAndDisabled", "nativeControl", "accessibilityReadOnly", "ref", "cursor<PERSON>nh<PERSON><PERSON>", "type", "root", "cursor<PERSON><PERSON><PERSON>", "displayName", "create", "cursor", "height", "userSelect", "width", "alignItems", "borderRadius", "borderStyle", "borderWidth", "justifyContent", "backgroundImage", "backgroundRepeat", "absoluteFillObject", "margin", "appearance", "padding", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _createElement = _interopRequireDefault(require(\"../createElement\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _excluded = [\"aria-readonly\", \"color\", \"disabled\", \"onChange\", \"onValueChange\", \"readOnly\", \"style\", \"value\"];\nvar CheckBox = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var ariaReadOnly = props['aria-readonly'],\n    color = props.color,\n    disabled = props.disabled,\n    onChange = props.onChange,\n    onValueChange = props.onValueChange,\n    readOnly = props.readOnly,\n    style = props.style,\n    value = props.value,\n    other = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  function handleChange(event) {\n    var value = event.nativeEvent.target.checked;\n    event.nativeEvent.value = value;\n    onChange && onChange(event);\n    onValueChange && onValueChange(value);\n  }\n  var fakeControl = /*#__PURE__*/React.createElement(_View.default, {\n    style: [styles.fakeControl, value && styles.fakeControlChecked,\n    // custom color\n    value && color && {\n      backgroundColor: color,\n      borderColor: color\n    }, disabled && styles.fakeControlDisabled, value && disabled && styles.fakeControlCheckedAndDisabled]\n  });\n  var nativeControl = (0, _createElement.default)('input', {\n    checked: value,\n    disabled: disabled,\n    onChange: handleChange,\n    readOnly: readOnly === true || ariaReadOnly === true || other.accessibilityReadOnly === true,\n    ref: forwardedRef,\n    style: [styles.nativeControl, styles.cursorInherit],\n    type: 'checkbox'\n  });\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, other, {\n    \"aria-disabled\": disabled,\n    \"aria-readonly\": ariaReadOnly,\n    style: [styles.root, style, disabled && styles.cursorDefault]\n  }), fakeControl, nativeControl);\n});\nCheckBox.displayName = 'CheckBox';\nvar styles = _StyleSheet.default.create({\n  root: {\n    cursor: 'pointer',\n    height: 16,\n    userSelect: 'none',\n    width: 16\n  },\n  cursorDefault: {\n    cursor: 'default'\n  },\n  cursorInherit: {\n    cursor: 'inherit'\n  },\n  fakeControl: {\n    alignItems: 'center',\n    backgroundColor: '#fff',\n    borderColor: '#657786',\n    borderRadius: 2,\n    borderStyle: 'solid',\n    borderWidth: 2,\n    height: '100%',\n    justifyContent: 'center',\n    width: '100%'\n  },\n  fakeControlChecked: {\n    backgroundColor: '#009688',\n    backgroundImage: 'url(\"data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9Im5vIj8+CjxzdmcKICAgeG1sbnM6ZGM9Imh0dHA6Ly9wdXJsLm9yZy9kYy9lbGVtZW50cy8xLjEvIgogICB4bWxuczpjYz0iaHR0cDovL2NyZWF0aXZlY29tbW9ucy5vcmcvbnMjIgogICB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiCiAgIHhtbG5zOnN2Zz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciCiAgIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIKICAgdmVyc2lvbj0iMS4xIgogICB2aWV3Qm94PSIwIDAgMSAxIgogICBwcmVzZXJ2ZUFzcGVjdFJhdGlvPSJ4TWluWU1pbiBtZWV0Ij4KICA8cGF0aAogICAgIGQ9Ik0gMC4wNDAzODA1OSwwLjYyNjc3NjcgMC4xNDY0NDY2MSwwLjUyMDcxMDY4IDAuNDI5Mjg5MzIsMC44MDM1NTMzOSAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IE0gMC4yMTcxNTcyOSwwLjgwMzU1MzM5IDAuODUzNTUzMzksMC4xNjcxNTcyOSAwLjk1OTYxOTQxLDAuMjczMjIzMyAwLjMyMzIyMzMsMC45MDk2MTk0MSB6IgogICAgIGlkPSJyZWN0Mzc4MCIKICAgICBzdHlsZT0iZmlsbDojZmZmZmZmO2ZpbGwtb3BhY2l0eToxO3N0cm9rZTpub25lIiAvPgo8L3N2Zz4K\")',\n    backgroundRepeat: 'no-repeat',\n    borderColor: '#009688'\n  },\n  fakeControlDisabled: {\n    borderColor: '#CCD6DD'\n  },\n  fakeControlCheckedAndDisabled: {\n    backgroundColor: '#AAB8C2',\n    borderColor: '#AAB8C2'\n  },\n  nativeControl: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, _StyleSheet.default.absoluteFillObject), {}, {\n    height: '100%',\n    margin: 0,\n    appearance: 'none',\n    padding: 0,\n    width: '100%'\n  })\n});\nvar _default = exports.default = CheckBox;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,cAAc,GAAGN,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIM,SAAS,GAAGP,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIO,8BAA8B,GAAGR,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIQ,KAAK,GAAGN,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIS,cAAc,GAAGV,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AACxE,IAAIU,WAAW,GAAGX,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIW,KAAK,GAAGZ,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIY,SAAS,GAAG,CAAC,eAAe,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,CAAC;AACjH,IAAIC,QAAQ,GAAgBL,KAAK,CAACM,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EACpE,IAAIC,YAAY,GAAGF,KAAK,CAAC,eAAe,CAAC;IACvCG,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;IACzBC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,aAAa,GAAGN,KAAK,CAACM,aAAa;IACnCC,QAAQ,GAAGP,KAAK,CAACO,QAAQ;IACzBC,KAAK,GAAGR,KAAK,CAACQ,KAAK;IACnBC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,KAAK,GAAG,CAAC,CAAC,EAAElB,8BAA8B,CAACN,OAAO,EAAEc,KAAK,EAAEH,SAAS,CAAC;EACvE,SAASc,YAAYA,CAACC,KAAK,EAAE;IAC3B,IAAIH,KAAK,GAAGG,KAAK,CAACC,WAAW,CAACC,MAAM,CAACC,OAAO;IAC5CH,KAAK,CAACC,WAAW,CAACJ,KAAK,GAAGA,KAAK;IAC/BJ,QAAQ,IAAIA,QAAQ,CAACO,KAAK,CAAC;IAC3BN,aAAa,IAAIA,aAAa,CAACG,KAAK,CAAC;EACvC;EACA,IAAIO,WAAW,GAAgBvB,KAAK,CAACwB,aAAa,CAACrB,KAAK,CAACV,OAAO,EAAE;IAChEsB,KAAK,EAAE,CAACU,MAAM,CAACF,WAAW,EAAEP,KAAK,IAAIS,MAAM,CAACC,kBAAkB,EAE9DV,KAAK,IAAIN,KAAK,IAAI;MAChBiB,eAAe,EAAEjB,KAAK;MACtBkB,WAAW,EAAElB;IACf,CAAC,EAAEC,QAAQ,IAAIc,MAAM,CAACI,mBAAmB,EAAEb,KAAK,IAAIL,QAAQ,IAAIc,MAAM,CAACK,6BAA6B;EACtG,CAAC,CAAC;EACF,IAAIC,aAAa,GAAG,CAAC,CAAC,EAAE9B,cAAc,CAACR,OAAO,EAAE,OAAO,EAAE;IACvD6B,OAAO,EAAEN,KAAK;IACdL,QAAQ,EAAEA,QAAQ;IAClBC,QAAQ,EAAEM,YAAY;IACtBJ,QAAQ,EAAEA,QAAQ,KAAK,IAAI,IAAIL,YAAY,KAAK,IAAI,IAAIQ,KAAK,CAACe,qBAAqB,KAAK,IAAI;IAC5FC,GAAG,EAAEzB,YAAY;IACjBO,KAAK,EAAE,CAACU,MAAM,CAACM,aAAa,EAAEN,MAAM,CAACS,aAAa,CAAC;IACnDC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,OAAoBnC,KAAK,CAACwB,aAAa,CAACrB,KAAK,CAACV,OAAO,EAAE,CAAC,CAAC,EAAEK,SAAS,CAACL,OAAO,EAAE,CAAC,CAAC,EAAEwB,KAAK,EAAE;IACvF,eAAe,EAAEN,QAAQ;IACzB,eAAe,EAAEF,YAAY;IAC7BM,KAAK,EAAE,CAACU,MAAM,CAACW,IAAI,EAAErB,KAAK,EAAEJ,QAAQ,IAAIc,MAAM,CAACY,aAAa;EAC9D,CAAC,CAAC,EAAEd,WAAW,EAAEQ,aAAa,CAAC;AACjC,CAAC,CAAC;AACF1B,QAAQ,CAACiC,WAAW,GAAG,UAAU;AACjC,IAAIb,MAAM,GAAGvB,WAAW,CAACT,OAAO,CAAC8C,MAAM,CAAC;EACtCH,IAAI,EAAE;IACJI,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAE,MAAM;IAClBC,KAAK,EAAE;EACT,CAAC;EACDN,aAAa,EAAE;IACbG,MAAM,EAAE;EACV,CAAC;EACDN,aAAa,EAAE;IACbM,MAAM,EAAE;EACV,CAAC;EACDjB,WAAW,EAAE;IACXqB,UAAU,EAAE,QAAQ;IACpBjB,eAAe,EAAE,MAAM;IACvBC,WAAW,EAAE,SAAS;IACtBiB,YAAY,EAAE,CAAC;IACfC,WAAW,EAAE,OAAO;IACpBC,WAAW,EAAE,CAAC;IACdN,MAAM,EAAE,MAAM;IACdO,cAAc,EAAE,QAAQ;IACxBL,KAAK,EAAE;EACT,CAAC;EACDjB,kBAAkB,EAAE;IAClBC,eAAe,EAAE,SAAS;IAC1BsB,eAAe,EAAE,m4BAAm4B;IACp5BC,gBAAgB,EAAE,WAAW;IAC7BtB,WAAW,EAAE;EACf,CAAC;EACDC,mBAAmB,EAAE;IACnBD,WAAW,EAAE;EACf,CAAC;EACDE,6BAA6B,EAAE;IAC7BH,eAAe,EAAE,SAAS;IAC1BC,WAAW,EAAE;EACf,CAAC;EACDG,aAAa,EAAE,CAAC,CAAC,EAAElC,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEI,cAAc,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAES,WAAW,CAACT,OAAO,CAAC0D,kBAAkB,CAAC,EAAE,CAAC,CAAC,EAAE;IACtHV,MAAM,EAAE,MAAM;IACdW,MAAM,EAAE,CAAC;IACTC,UAAU,EAAE,MAAM;IAClBC,OAAO,EAAE,CAAC;IACVX,KAAK,EAAE;EACT,CAAC;AACH,CAAC,CAAC;AACF,IAAIY,QAAQ,GAAG5D,OAAO,CAACF,OAAO,GAAGY,QAAQ;AACzCmD,MAAM,CAAC7D,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}