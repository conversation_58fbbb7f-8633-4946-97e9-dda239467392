{"version": 3, "names": ["useState", "useEffect", "useCallback", "useMemo", "predictiveCacheEngine", "adaptivePerformanceManager", "behavioralAnalyticsEngine", "smartResourceManager", "useAuth", "useAIOptimization", "initialConfig", "arguments", "length", "undefined", "cov_2crrnrtmks", "b", "f", "_ref", "s", "user", "_ref2", "_ref3", "_slicedToArray", "currentSessionId", "setCurrentSessionId", "_ref4", "Object", "assign", "enablePredictiveCache", "enableAdaptivePerformance", "enableBehaviorAnalytics", "enableResourceManagement", "autoOptimization", "optimizationInterval", "_ref5", "config", "setConfig", "_ref6", "isInitialized", "isOptimizing", "currentProfile", "userSegment", "resourceEfficiency", "cacheHitRate", "predictions", "nextActions", "resourceUsage", "performanceScore", "recommendations", "immediate", "shortTerm", "longTerm", "metrics", "adaptationCount", "optimizationGains", "userSatisfaction", "systemHealth", "_ref7", "state", "setState", "initialize", "_asyncToGenerator", "prev", "initPromises", "push", "Promise", "resolve", "all", "updateAIState", "console", "log", "error", "startSession", "context", "sessionId", "id", "timeOfDay", "getTimeOfDay", "dayOfWeek", "Date", "toLocaleDateString", "weekday", "networkType", "batteryLevel", "deviceOrientation", "appVersion", "trackScreenView", "screen", "loadTime", "executePredictiveCaching", "trackInteraction", "interaction", "endSession", "optimizeNow", "optimizationPromises", "reevaluatePerformance", "optimizeResources", "allSettled", "updateConfig", "newConfig", "getInsights", "getAnalyticsInsights", "getPredictions", "cachePredictions", "apply", "_toConsumableArray", "map", "p", "type", "resourcePredictions", "getResourcePredictions", "newState", "getCurrentProfile", "name", "adaptationMetrics", "getAdaptationMetrics", "averagePerformanceGain", "getUserSegment", "efficiency", "getResourceEfficiencyScore", "overall", "cacheMetrics", "getCachingMetrics", "cacheHitImprovement", "getOptimizationRecommendations", "filter", "key", "interval", "setInterval", "clearInterval", "actions", "hour", "getHours"], "sources": ["useAIOptimization.ts"], "sourcesContent": ["/**\n * AI Optimization Hook\n * \n * Unified hook that integrates all AI-powered optimization systems\n * for seamless performance enhancement and intelligent user experience.\n */\n\nimport { useState, useEffect, useCallback, useMemo } from 'react';\nimport { predictiveCacheEngine } from '@/services/ai/PredictiveCacheEngine';\nimport { adaptivePerformanceManager } from '@/services/ai/AdaptivePerformanceManager';\nimport { behavioralAnalyticsEngine } from '@/services/ai/BehavioralAnalyticsEngine';\nimport { smartResourceManager } from '@/services/ai/SmartResourceManager';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface AIOptimizationState {\n  isInitialized: boolean;\n  isOptimizing: boolean;\n  currentProfile: string;\n  userSegment: string;\n  resourceEfficiency: number;\n  cacheHitRate: number;\n  predictions: {\n    nextActions: string[];\n    resourceUsage: any[];\n    performanceScore: number;\n  };\n  recommendations: {\n    immediate: string[];\n    shortTerm: string[];\n    longTerm: string[];\n  };\n  metrics: {\n    adaptationCount: number;\n    optimizationGains: number;\n    userSatisfaction: number;\n    systemHealth: number;\n  };\n}\n\ninterface AIOptimizationConfig {\n  enablePredictiveCache: boolean;\n  enableAdaptivePerformance: boolean;\n  enableBehaviorAnalytics: boolean;\n  enableResourceManagement: boolean;\n  autoOptimization: boolean;\n  optimizationInterval: number;\n}\n\ninterface UseAIOptimizationReturn {\n  state: AIOptimizationState;\n  actions: {\n    initialize: () => Promise<void>;\n    startSession: (context: any) => string;\n    trackScreenView: (screen: string, loadTime: number) => void;\n    trackInteraction: (interaction: any) => void;\n    endSession: () => void;\n    optimizeNow: () => Promise<void>;\n    updateConfig: (config: Partial<AIOptimizationConfig>) => void;\n    getInsights: () => any[];\n    getPredictions: () => Promise<any[]>;\n  };\n  config: AIOptimizationConfig;\n}\n\n/**\n * AI-Powered Optimization Hook\n */\nexport function useAIOptimization(\n  initialConfig: Partial<AIOptimizationConfig> = {}\n): UseAIOptimizationReturn {\n  const { user } = useAuth();\n  const [currentSessionId, setCurrentSessionId] = useState<string | null>(null);\n  \n  const [config, setConfig] = useState<AIOptimizationConfig>({\n    enablePredictiveCache: true,\n    enableAdaptivePerformance: true,\n    enableBehaviorAnalytics: true,\n    enableResourceManagement: true,\n    autoOptimization: true,\n    optimizationInterval: 30000, // 30 seconds\n    ...initialConfig,\n  });\n\n  const [state, setState] = useState<AIOptimizationState>({\n    isInitialized: false,\n    isOptimizing: false,\n    currentProfile: 'balanced',\n    userSegment: 'unknown',\n    resourceEfficiency: 0,\n    cacheHitRate: 0,\n    predictions: {\n      nextActions: [],\n      resourceUsage: [],\n      performanceScore: 0,\n    },\n    recommendations: {\n      immediate: [],\n      shortTerm: [],\n      longTerm: [],\n    },\n    metrics: {\n      adaptationCount: 0,\n      optimizationGains: 0,\n      userSatisfaction: 0,\n      systemHealth: 0,\n    },\n  });\n\n  /**\n   * Initialize all AI optimization systems\n   */\n  const initialize = useCallback(async () => {\n    if (!user || state.isInitialized) return;\n\n    try {\n      setState(prev => ({ ...prev, isOptimizing: true }));\n\n      // Initialize systems based on configuration\n      const initPromises: Promise<any>[] = [];\n\n      if (config.enableAdaptivePerformance) {\n        // Adaptive performance is auto-initialized\n        initPromises.push(Promise.resolve());\n      }\n\n      if (config.enableResourceManagement) {\n        // Resource manager is auto-initialized\n        initPromises.push(Promise.resolve());\n      }\n\n      await Promise.all(initPromises);\n\n      // Get initial state\n      await updateAIState();\n\n      setState(prev => ({\n        ...prev,\n        isInitialized: true,\n        isOptimizing: false,\n      }));\n\n      console.log('AI Optimization systems initialized successfully');\n\n    } catch (error) {\n      console.error('Failed to initialize AI optimization:', error);\n      setState(prev => ({ ...prev, isOptimizing: false }));\n    }\n  }, [user, state.isInitialized, config]);\n\n  /**\n   * Start a new user session with context\n   */\n  const startSession = useCallback((context: any) => {\n    if (!user || !state.isInitialized) return '';\n\n    try {\n      const sessionId = behavioralAnalyticsEngine.startSession(user.id, {\n        timeOfDay: getTimeOfDay(),\n        dayOfWeek: new Date().toLocaleDateString('en', { weekday: 'long' }),\n        networkType: 'wifi', // Would detect actual network\n        batteryLevel: 75, // Would get actual battery level\n        deviceOrientation: 'portrait',\n        appVersion: '1.0.0',\n        ...context,\n      });\n\n      setCurrentSessionId(sessionId);\n      console.log(`Started AI optimization session: ${sessionId}`);\n      \n      return sessionId;\n    } catch (error) {\n      console.error('Failed to start AI session:', error);\n      return '';\n    }\n  }, [user, state.isInitialized]);\n\n  /**\n   * Track screen view for behavioral analytics\n   */\n  const trackScreenView = useCallback((screen: string, loadTime: number) => {\n    if (!user || !currentSessionId || !config.enableBehaviorAnalytics) return;\n\n    try {\n      behavioralAnalyticsEngine.trackScreenView(\n        user.id,\n        currentSessionId,\n        screen,\n        loadTime\n      );\n\n      // Execute predictive caching if enabled\n      if (config.enablePredictiveCache) {\n        predictiveCacheEngine.executePredictiveCaching(user.id);\n      }\n\n    } catch (error) {\n      console.error('Failed to track screen view:', error);\n    }\n  }, [user, currentSessionId, config.enableBehaviorAnalytics, config.enablePredictiveCache]);\n\n  /**\n   * Track user interaction\n   */\n  const trackInteraction = useCallback((interaction: any) => {\n    if (!user || !currentSessionId || !config.enableBehaviorAnalytics) return;\n\n    try {\n      behavioralAnalyticsEngine.trackInteraction(\n        user.id,\n        currentSessionId,\n        interaction\n      );\n    } catch (error) {\n      console.error('Failed to track interaction:', error);\n    }\n  }, [user, currentSessionId, config.enableBehaviorAnalytics]);\n\n  /**\n   * End current session\n   */\n  const endSession = useCallback(() => {\n    if (!user || !currentSessionId) return;\n\n    try {\n      behavioralAnalyticsEngine.endSession(user.id, currentSessionId);\n      setCurrentSessionId(null);\n      console.log('Ended AI optimization session');\n    } catch (error) {\n      console.error('Failed to end AI session:', error);\n    }\n  }, [user, currentSessionId]);\n\n  /**\n   * Force immediate optimization\n   */\n  const optimizeNow = useCallback(async () => {\n    if (!state.isInitialized) return;\n\n    try {\n      setState(prev => ({ ...prev, isOptimizing: true }));\n\n      const optimizationPromises: Promise<any>[] = [];\n\n      // Adaptive performance optimization\n      if (config.enableAdaptivePerformance) {\n        optimizationPromises.push(\n          adaptivePerformanceManager.reevaluatePerformance()\n        );\n      }\n\n      // Resource optimization\n      if (config.enableResourceManagement) {\n        optimizationPromises.push(\n          smartResourceManager.optimizeResources()\n        );\n      }\n\n      // Predictive caching\n      if (config.enablePredictiveCache && user) {\n        optimizationPromises.push(\n          predictiveCacheEngine.executePredictiveCaching(user.id)\n        );\n      }\n\n      await Promise.allSettled(optimizationPromises);\n\n      // Update state with new metrics\n      await updateAIState();\n\n      console.log('AI optimization completed successfully');\n\n    } catch (error) {\n      console.error('Failed to execute AI optimization:', error);\n    } finally {\n      setState(prev => ({ ...prev, isOptimizing: false }));\n    }\n  }, [state.isInitialized, config, user]);\n\n  /**\n   * Update configuration\n   */\n  const updateConfig = useCallback((newConfig: Partial<AIOptimizationConfig>) => {\n    setConfig(prev => ({ ...prev, ...newConfig }));\n  }, []);\n\n  /**\n   * Get analytics insights\n   */\n  const getInsights = useCallback(() => {\n    if (!user || !config.enableBehaviorAnalytics) return [];\n\n    try {\n      return behavioralAnalyticsEngine.getAnalyticsInsights(user.id);\n    } catch (error) {\n      console.error('Failed to get insights:', error);\n      return [];\n    }\n  }, [user, config.enableBehaviorAnalytics]);\n\n  /**\n   * Get AI predictions\n   */\n  const getPredictions = useCallback(async () => {\n    if (!user || !state.isInitialized) return [];\n\n    try {\n      const predictions: any[] = [];\n\n      // Cache predictions\n      if (config.enablePredictiveCache) {\n        const cachePredictions = await predictiveCacheEngine.getPredictions(user.id);\n        predictions.push(...cachePredictions.map(p => ({\n          type: 'cache',\n          ...p,\n        })));\n      }\n\n      // Resource predictions\n      if (config.enableResourceManagement) {\n        const resourcePredictions = await smartResourceManager.getResourcePredictions();\n        predictions.push(...resourcePredictions.map(p => ({\n          type: 'resource',\n          ...p,\n        })));\n      }\n\n      return predictions;\n    } catch (error) {\n      console.error('Failed to get predictions:', error);\n      return [];\n    }\n  }, [user, state.isInitialized, config]);\n\n  /**\n   * Update AI state with current metrics\n   */\n  const updateAIState = useCallback(async () => {\n    try {\n      const newState: Partial<AIOptimizationState> = {};\n\n      // Performance profile\n      if (config.enableAdaptivePerformance) {\n        const currentProfile = adaptivePerformanceManager.getCurrentProfile();\n        newState.currentProfile = currentProfile?.name || 'balanced';\n        \n        const adaptationMetrics = adaptivePerformanceManager.getAdaptationMetrics();\n        newState.metrics = {\n          ...state.metrics,\n          adaptationCount: adaptationMetrics.adaptationCount,\n          optimizationGains: adaptationMetrics.averagePerformanceGain,\n          userSatisfaction: adaptationMetrics.userSatisfaction,\n        };\n      }\n\n      // User segment\n      if (config.enableBehaviorAnalytics && user) {\n        const userSegment = behavioralAnalyticsEngine.getUserSegment(user.id);\n        newState.userSegment = userSegment?.name || 'unknown';\n      }\n\n      // Resource efficiency\n      if (config.enableResourceManagement) {\n        const efficiency = smartResourceManager.getResourceEfficiencyScore();\n        newState.resourceEfficiency = efficiency.overall;\n        newState.metrics = {\n          ...newState.metrics,\n          systemHealth: efficiency.overall,\n        };\n      }\n\n      // Cache metrics\n      if (config.enablePredictiveCache) {\n        const cacheMetrics = predictiveCacheEngine.getCachingMetrics();\n        newState.cacheHitRate = cacheMetrics.cacheHitImprovement;\n      }\n\n      // Recommendations\n      if (config.enableResourceManagement) {\n        const recommendations = smartResourceManager.getOptimizationRecommendations();\n        newState.recommendations = recommendations;\n      }\n\n      // Predictions\n      const predictions = await getPredictions();\n      newState.predictions = {\n        nextActions: predictions.filter(p => p.type === 'cache').map(p => p.key),\n        resourceUsage: predictions.filter(p => p.type === 'resource'),\n        performanceScore: state.predictions.performanceScore, // Would calculate from metrics\n      };\n\n      setState(prev => ({ ...prev, ...newState }));\n\n    } catch (error) {\n      console.error('Failed to update AI state:', error);\n    }\n  }, [config, user, state.metrics, state.predictions.performanceScore, getPredictions]);\n\n  // Auto-optimization effect\n  useEffect(() => {\n    if (!config.autoOptimization || !state.isInitialized) return;\n\n    const interval = setInterval(() => {\n      optimizeNow();\n    }, config.optimizationInterval);\n\n    return () => clearInterval(interval);\n  }, [config.autoOptimization, config.optimizationInterval, state.isInitialized, optimizeNow]);\n\n  // Periodic state updates\n  useEffect(() => {\n    if (!state.isInitialized) return;\n\n    const interval = setInterval(() => {\n      updateAIState();\n    }, 10000); // Update every 10 seconds\n\n    return () => clearInterval(interval);\n  }, [state.isInitialized, updateAIState]);\n\n  // Initialize on mount\n  useEffect(() => {\n    if (user && !state.isInitialized) {\n      initialize();\n    }\n  }, [user, state.isInitialized, initialize]);\n\n  // Memoized return value\n  return useMemo(() => ({\n    state,\n    actions: {\n      initialize,\n      startSession,\n      trackScreenView,\n      trackInteraction,\n      endSession,\n      optimizeNow,\n      updateConfig,\n      getInsights,\n      getPredictions,\n    },\n    config,\n  }), [\n    state,\n    initialize,\n    startSession,\n    trackScreenView,\n    trackInteraction,\n    endSession,\n    optimizeNow,\n    updateConfig,\n    getInsights,\n    getPredictions,\n    config,\n  ]);\n}\n\n// Helper functions\nfunction getTimeOfDay(): 'morning' | 'afternoon' | 'evening' | 'night' {\n  const hour = new Date().getHours();\n  if (hour < 6) return 'night';\n  if (hour < 12) return 'morning';\n  if (hour < 18) return 'afternoon';\n  if (hour < 22) return 'evening';\n  return 'night';\n}\n\nexport default useAIOptimization;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,OAAO,QAAQ,OAAO;AACjE,SAASC,qBAAqB;AAC9B,SAASC,0BAA0B;AACnC,SAASC,yBAAyB;AAClC,SAASC,oBAAoB;AAC7B,SAASC,OAAO;AAuDhB,OAAO,SAASC,iBAAiBA,CAAA,EAEN;EAAA,IADzBC,aAA4C,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,OAAAG,cAAA,GAAAC,CAAA,UAAG,CAAC,CAAC;EAAAD,cAAA,GAAAE,CAAA;EAEjD,IAAAC,IAAA,IAAAH,cAAA,GAAAI,CAAA,OAAiBV,OAAO,CAAC,CAAC;IAAlBW,IAAI,GAAAF,IAAA,CAAJE,IAAI;EACZ,IAAAC,KAAA,IAAAN,cAAA,GAAAI,CAAA,OAAgDlB,QAAQ,CAAgB,IAAI,CAAC;IAAAqB,KAAA,GAAAC,cAAA,CAAAF,KAAA;IAAtEG,gBAAgB,GAAAF,KAAA;IAAEG,mBAAmB,GAAAH,KAAA;EAE5C,IAAAI,KAAA,IAAAX,cAAA,GAAAI,CAAA,OAA4BlB,QAAQ,CAAA0B,MAAA,CAAAC,MAAA;MAClCC,qBAAqB,EAAE,IAAI;MAC3BC,yBAAyB,EAAE,IAAI;MAC/BC,uBAAuB,EAAE,IAAI;MAC7BC,wBAAwB,EAAE,IAAI;MAC9BC,gBAAgB,EAAE,IAAI;MACtBC,oBAAoB,EAAE;IAAK,GACxBvB,aAAa,CACjB,CAAC;IAAAwB,KAAA,GAAAZ,cAAA,CAAAG,KAAA;IARKU,MAAM,GAAAD,KAAA;IAAEE,SAAS,GAAAF,KAAA;EAUxB,IAAAG,KAAA,IAAAvB,cAAA,GAAAI,CAAA,OAA0BlB,QAAQ,CAAsB;MACtDsC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,KAAK;MACnBC,cAAc,EAAE,UAAU;MAC1BC,WAAW,EAAE,SAAS;MACtBC,kBAAkB,EAAE,CAAC;MACrBC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE;QACXC,WAAW,EAAE,EAAE;QACfC,aAAa,EAAE,EAAE;QACjBC,gBAAgB,EAAE;MACpB,CAAC;MACDC,eAAe,EAAE;QACfC,SAAS,EAAE,EAAE;QACbC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE;MACZ,CAAC;MACDC,OAAO,EAAE;QACPC,eAAe,EAAE,CAAC;QAClBC,iBAAiB,EAAE,CAAC;QACpBC,gBAAgB,EAAE,CAAC;QACnBC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IAAAC,KAAA,GAAAnC,cAAA,CAAAe,KAAA;IAvBKqB,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EA4BtB,IAAMG,UAAU,IAAA9C,cAAA,GAAAI,CAAA,OAAGhB,WAAW,CAAA2D,iBAAA,CAAC,aAAY;IAAA/C,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACzC,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAACI,IAAI,MAAAL,cAAA,GAAAC,CAAA,UAAI2C,KAAK,CAACpB,aAAa,GAAE;MAAAxB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAEzC,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACFyC,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAhD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAQ,MAAA,CAAAC,MAAA,KAAKmC,IAAI;UAAEvB,YAAY,EAAE;QAAI;MAAC,CAAE,CAAC;MAGnD,IAAMwB,YAA4B,IAAAjD,cAAA,GAAAI,CAAA,QAAG,EAAE;MAACJ,cAAA,GAAAI,CAAA;MAExC,IAAIiB,MAAM,CAACN,yBAAyB,EAAE;QAAAf,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAEpC6C,YAAY,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;MACtC,CAAC;QAAApD,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED,IAAIiB,MAAM,CAACJ,wBAAwB,EAAE;QAAAjB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAEnC6C,YAAY,CAACC,IAAI,CAACC,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC;MACtC,CAAC;QAAApD,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED,MAAM+C,OAAO,CAACE,GAAG,CAACJ,YAAY,CAAC;MAACjD,cAAA,GAAAI,CAAA;MAGhC,MAAMkD,aAAa,CAAC,CAAC;MAACtD,cAAA,GAAAI,CAAA;MAEtByC,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAhD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAQ,MAAA,CAAAC,MAAA,KACbmC,IAAI;UACPxB,aAAa,EAAE,IAAI;UACnBC,YAAY,EAAE;QAAK;MACrB,CAAE,CAAC;MAACzB,cAAA,GAAAI,CAAA;MAEJmD,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;IAEjE,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAACzD,cAAA,GAAAI,CAAA;MAC9DyC,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAhD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAQ,MAAA,CAAAC,MAAA,KAAKmC,IAAI;UAAEvB,YAAY,EAAE;QAAK;MAAC,CAAE,CAAC;IACtD;EACF,CAAC,GAAE,CAACpB,IAAI,EAAEuC,KAAK,CAACpB,aAAa,EAAEH,MAAM,CAAC,CAAC;EAKvC,IAAMqC,YAAY,IAAA1D,cAAA,GAAAI,CAAA,QAAGhB,WAAW,CAAC,UAACuE,OAAY,EAAK;IAAA3D,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACjD,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAACI,IAAI,MAAAL,cAAA,GAAAC,CAAA,UAAI,CAAC2C,KAAK,CAACpB,aAAa,GAAE;MAAAxB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAE7C,IAAI;MACF,IAAMwD,SAAS,IAAA5D,cAAA,GAAAI,CAAA,QAAGZ,yBAAyB,CAACkE,YAAY,CAACrD,IAAI,CAACwD,EAAE,EAAAjD,MAAA,CAAAC,MAAA;QAC9DiD,SAAS,EAAEC,YAAY,CAAC,CAAC;QACzBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,IAAI,EAAE;UAAEC,OAAO,EAAE;QAAO,CAAC,CAAC;QACnEC,WAAW,EAAE,MAAM;QACnBC,YAAY,EAAE,EAAE;QAChBC,iBAAiB,EAAE,UAAU;QAC7BC,UAAU,EAAE;MAAO,GAChBZ,OAAO,CACX,CAAC;MAAC3D,cAAA,GAAAI,CAAA;MAEHM,mBAAmB,CAACkD,SAAS,CAAC;MAAC5D,cAAA,GAAAI,CAAA;MAC/BmD,OAAO,CAACC,GAAG,CAAC,oCAAoCI,SAAS,EAAE,CAAC;MAAC5D,cAAA,GAAAI,CAAA;MAE7D,OAAOwD,SAAS;IAClB,CAAC,CAAC,OAAOH,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MAACzD,cAAA,GAAAI,CAAA;MACpD,OAAO,EAAE;IACX;EACF,CAAC,EAAE,CAACC,IAAI,EAAEuC,KAAK,CAACpB,aAAa,CAAC,CAAC;EAK/B,IAAMgD,eAAe,IAAAxE,cAAA,GAAAI,CAAA,QAAGhB,WAAW,CAAC,UAACqF,MAAc,EAAEC,QAAgB,EAAK;IAAA1E,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACxE,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAACI,IAAI,MAAAL,cAAA,GAAAC,CAAA,UAAI,CAACQ,gBAAgB,MAAAT,cAAA,GAAAC,CAAA,UAAI,CAACoB,MAAM,CAACL,uBAAuB,GAAE;MAAAhB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAE1E,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACFZ,yBAAyB,CAACgF,eAAe,CACvCnE,IAAI,CAACwD,EAAE,EACPpD,gBAAgB,EAChBgE,MAAM,EACNC,QACF,CAAC;MAAC1E,cAAA,GAAAI,CAAA;MAGF,IAAIiB,MAAM,CAACP,qBAAqB,EAAE;QAAAd,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QAChCd,qBAAqB,CAACqF,wBAAwB,CAACtE,IAAI,CAACwD,EAAE,CAAC;MACzD,CAAC;QAAA7D,cAAA,GAAAC,CAAA;MAAA;IAEH,CAAC,CAAC,OAAOwD,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAACpD,IAAI,EAAEI,gBAAgB,EAAEY,MAAM,CAACL,uBAAuB,EAAEK,MAAM,CAACP,qBAAqB,CAAC,CAAC;EAK1F,IAAM8D,gBAAgB,IAAA5E,cAAA,GAAAI,CAAA,QAAGhB,WAAW,CAAC,UAACyF,WAAgB,EAAK;IAAA7E,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACzD,IAAI,CAAAJ,cAAA,GAAAC,CAAA,YAACI,IAAI,MAAAL,cAAA,GAAAC,CAAA,WAAI,CAACQ,gBAAgB,MAAAT,cAAA,GAAAC,CAAA,WAAI,CAACoB,MAAM,CAACL,uBAAuB,GAAE;MAAAhB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAE1E,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACFZ,yBAAyB,CAACoF,gBAAgB,CACxCvE,IAAI,CAACwD,EAAE,EACPpD,gBAAgB,EAChBoE,WACF,CAAC;IACH,CAAC,CAAC,OAAOpB,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACtD;EACF,CAAC,EAAE,CAACpD,IAAI,EAAEI,gBAAgB,EAAEY,MAAM,CAACL,uBAAuB,CAAC,CAAC;EAK5D,IAAM8D,UAAU,IAAA9E,cAAA,GAAAI,CAAA,QAAGhB,WAAW,CAAC,YAAM;IAAAY,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACnC,IAAI,CAAAJ,cAAA,GAAAC,CAAA,YAACI,IAAI,MAAAL,cAAA,GAAAC,CAAA,WAAI,CAACQ,gBAAgB,GAAE;MAAAT,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAEvC,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACFZ,yBAAyB,CAACsF,UAAU,CAACzE,IAAI,CAACwD,EAAE,EAAEpD,gBAAgB,CAAC;MAACT,cAAA,GAAAI,CAAA;MAChEM,mBAAmB,CAAC,IAAI,CAAC;MAACV,cAAA,GAAAI,CAAA;MAC1BmD,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACnD;EACF,CAAC,EAAE,CAACpD,IAAI,EAAEI,gBAAgB,CAAC,CAAC;EAK5B,IAAMsE,WAAW,IAAA/E,cAAA,GAAAI,CAAA,QAAGhB,WAAW,CAAA2D,iBAAA,CAAC,aAAY;IAAA/C,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC1C,IAAI,CAACwC,KAAK,CAACpB,aAAa,EAAE;MAAAxB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAEjC,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACFyC,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAhD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAQ,MAAA,CAAAC,MAAA,KAAKmC,IAAI;UAAEvB,YAAY,EAAE;QAAI;MAAC,CAAE,CAAC;MAEnD,IAAMuD,oBAAoC,IAAAhF,cAAA,GAAAI,CAAA,QAAG,EAAE;MAACJ,cAAA,GAAAI,CAAA;MAGhD,IAAIiB,MAAM,CAACN,yBAAyB,EAAE;QAAAf,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACpC4E,oBAAoB,CAAC9B,IAAI,CACvB3D,0BAA0B,CAAC0F,qBAAqB,CAAC,CACnD,CAAC;MACH,CAAC;QAAAjF,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAIiB,MAAM,CAACJ,wBAAwB,EAAE;QAAAjB,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACnC4E,oBAAoB,CAAC9B,IAAI,CACvBzD,oBAAoB,CAACyF,iBAAiB,CAAC,CACzC,CAAC;MACH,CAAC;QAAAlF,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAAAoB,MAAM,CAACP,qBAAqB,MAAAd,cAAA,GAAAC,CAAA,WAAII,IAAI,GAAE;QAAAL,cAAA,GAAAC,CAAA;QAAAD,cAAA,GAAAI,CAAA;QACxC4E,oBAAoB,CAAC9B,IAAI,CACvB5D,qBAAqB,CAACqF,wBAAwB,CAACtE,IAAI,CAACwD,EAAE,CACxD,CAAC;MACH,CAAC;QAAA7D,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED,MAAM+C,OAAO,CAACgC,UAAU,CAACH,oBAAoB,CAAC;MAAChF,cAAA,GAAAI,CAAA;MAG/C,MAAMkD,aAAa,CAAC,CAAC;MAACtD,cAAA,GAAAI,CAAA;MAEtBmD,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;IAEvD,CAAC,CAAC,OAAOC,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;IAC5D,CAAC,SAAS;MAAAzD,cAAA,GAAAI,CAAA;MACRyC,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAhD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAQ,MAAA,CAAAC,MAAA,KAAKmC,IAAI;UAAEvB,YAAY,EAAE;QAAK;MAAC,CAAE,CAAC;IACtD;EACF,CAAC,GAAE,CAACmB,KAAK,CAACpB,aAAa,EAAEH,MAAM,EAAEhB,IAAI,CAAC,CAAC;EAKvC,IAAM+E,YAAY,IAAApF,cAAA,GAAAI,CAAA,QAAGhB,WAAW,CAAC,UAACiG,SAAwC,EAAK;IAAArF,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC7EkB,SAAS,CAAC,UAAA0B,IAAI,EAAK;MAAAhD,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAAQ,MAAA,CAAAC,MAAA,KAAKmC,IAAI,EAAKqC,SAAS;IAAC,CAAE,CAAC;EAChD,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMC,WAAW,IAAAtF,cAAA,GAAAI,CAAA,QAAGhB,WAAW,CAAC,YAAM;IAAAY,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACpC,IAAI,CAAAJ,cAAA,GAAAC,CAAA,YAACI,IAAI,MAAAL,cAAA,GAAAC,CAAA,WAAI,CAACoB,MAAM,CAACL,uBAAuB,GAAE;MAAAhB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAExD,IAAI;MAAAJ,cAAA,GAAAI,CAAA;MACF,OAAOZ,yBAAyB,CAAC+F,oBAAoB,CAAClF,IAAI,CAACwD,EAAE,CAAC;IAChE,CAAC,CAAC,OAAOJ,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAACzD,cAAA,GAAAI,CAAA;MAChD,OAAO,EAAE;IACX;EACF,CAAC,EAAE,CAACC,IAAI,EAAEgB,MAAM,CAACL,uBAAuB,CAAC,CAAC;EAK1C,IAAMwE,cAAc,IAAAxF,cAAA,GAAAI,CAAA,QAAGhB,WAAW,CAAA2D,iBAAA,CAAC,aAAY;IAAA/C,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC7C,IAAI,CAAAJ,cAAA,GAAAC,CAAA,YAACI,IAAI,MAAAL,cAAA,GAAAC,CAAA,WAAI,CAAC2C,KAAK,CAACpB,aAAa,GAAE;MAAAxB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA,OAAO,EAAE;IAAA,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAAAD,cAAA,GAAAI,CAAA;IAE7C,IAAI;MACF,IAAM0B,WAAkB,IAAA9B,cAAA,GAAAI,CAAA,QAAG,EAAE;MAACJ,cAAA,GAAAI,CAAA;MAG9B,IAAIiB,MAAM,CAACP,qBAAqB,EAAE;QAAAd,cAAA,GAAAC,CAAA;QAChC,IAAMwF,gBAAgB,IAAAzF,cAAA,GAAAI,CAAA,cAASd,qBAAqB,CAACkG,cAAc,CAACnF,IAAI,CAACwD,EAAE,CAAC;QAAC7D,cAAA,GAAAI,CAAA;QAC7E0B,WAAW,CAACoB,IAAI,CAAAwC,KAAA,CAAhB5D,WAAW,EAAA6D,kBAAA,CAASF,gBAAgB,CAACG,GAAG,CAAC,UAAAC,CAAC,EAAK;UAAA7F,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAQ,MAAA,CAAAC,MAAA;YAC7CiF,IAAI,EAAE;UAAO,GACVD,CAAC;QACN,CAAE,CAAC,EAAC;MACN,CAAC;QAAA7F,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAIiB,MAAM,CAACJ,wBAAwB,EAAE;QAAAjB,cAAA,GAAAC,CAAA;QACnC,IAAM8F,mBAAmB,IAAA/F,cAAA,GAAAI,CAAA,cAASX,oBAAoB,CAACuG,sBAAsB,CAAC,CAAC;QAAChG,cAAA,GAAAI,CAAA;QAChF0B,WAAW,CAACoB,IAAI,CAAAwC,KAAA,CAAhB5D,WAAW,EAAA6D,kBAAA,CAASI,mBAAmB,CAACH,GAAG,CAAC,UAAAC,CAAC,EAAK;UAAA7F,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAQ,MAAA,CAAAC,MAAA;YAChDiF,IAAI,EAAE;UAAU,GACbD,CAAC;QACN,CAAE,CAAC,EAAC;MACN,CAAC;QAAA7F,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAED,OAAO0B,WAAW;IACpB,CAAC,CAAC,OAAO2B,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAACzD,cAAA,GAAAI,CAAA;MACnD,OAAO,EAAE;IACX;EACF,CAAC,GAAE,CAACC,IAAI,EAAEuC,KAAK,CAACpB,aAAa,EAAEH,MAAM,CAAC,CAAC;EAKvC,IAAMiC,aAAa,IAAAtD,cAAA,GAAAI,CAAA,SAAGhB,WAAW,CAAA2D,iBAAA,CAAC,aAAY;IAAA/C,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAC5C,IAAI;MACF,IAAM6F,QAAsC,IAAAjG,cAAA,GAAAI,CAAA,SAAG,CAAC,CAAC;MAACJ,cAAA,GAAAI,CAAA;MAGlD,IAAIiB,MAAM,CAACN,yBAAyB,EAAE;QAAAf,cAAA,GAAAC,CAAA;QACpC,IAAMyB,cAAc,IAAA1B,cAAA,GAAAI,CAAA,SAAGb,0BAA0B,CAAC2G,iBAAiB,CAAC,CAAC;QAAClG,cAAA,GAAAI,CAAA;QACtE6F,QAAQ,CAACvE,cAAc,GAAG,CAAA1B,cAAA,GAAAC,CAAA,WAAAyB,cAAc,oBAAdA,cAAc,CAAEyE,IAAI,MAAAnG,cAAA,GAAAC,CAAA,WAAI,UAAU;QAE5D,IAAMmG,iBAAiB,IAAApG,cAAA,GAAAI,CAAA,SAAGb,0BAA0B,CAAC8G,oBAAoB,CAAC,CAAC;QAACrG,cAAA,GAAAI,CAAA;QAC5E6F,QAAQ,CAAC3D,OAAO,GAAA1B,MAAA,CAAAC,MAAA,KACX+B,KAAK,CAACN,OAAO;UAChBC,eAAe,EAAE6D,iBAAiB,CAAC7D,eAAe;UAClDC,iBAAiB,EAAE4D,iBAAiB,CAACE,sBAAsB;UAC3D7D,gBAAgB,EAAE2D,iBAAiB,CAAC3D;QAAgB,EACrD;MACH,CAAC;QAAAzC,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAAAoB,MAAM,CAACL,uBAAuB,MAAAhB,cAAA,GAAAC,CAAA,WAAII,IAAI,GAAE;QAAAL,cAAA,GAAAC,CAAA;QAC1C,IAAM0B,WAAW,IAAA3B,cAAA,GAAAI,CAAA,SAAGZ,yBAAyB,CAAC+G,cAAc,CAAClG,IAAI,CAACwD,EAAE,CAAC;QAAC7D,cAAA,GAAAI,CAAA;QACtE6F,QAAQ,CAACtE,WAAW,GAAG,CAAA3B,cAAA,GAAAC,CAAA,WAAA0B,WAAW,oBAAXA,WAAW,CAAEwE,IAAI,MAAAnG,cAAA,GAAAC,CAAA,WAAI,SAAS;MACvD,CAAC;QAAAD,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAIiB,MAAM,CAACJ,wBAAwB,EAAE;QAAAjB,cAAA,GAAAC,CAAA;QACnC,IAAMuG,UAAU,IAAAxG,cAAA,GAAAI,CAAA,SAAGX,oBAAoB,CAACgH,0BAA0B,CAAC,CAAC;QAACzG,cAAA,GAAAI,CAAA;QACrE6F,QAAQ,CAACrE,kBAAkB,GAAG4E,UAAU,CAACE,OAAO;QAAC1G,cAAA,GAAAI,CAAA;QACjD6F,QAAQ,CAAC3D,OAAO,GAAA1B,MAAA,CAAAC,MAAA,KACXoF,QAAQ,CAAC3D,OAAO;UACnBI,YAAY,EAAE8D,UAAU,CAACE;QAAO,EACjC;MACH,CAAC;QAAA1G,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAIiB,MAAM,CAACP,qBAAqB,EAAE;QAAAd,cAAA,GAAAC,CAAA;QAChC,IAAM0G,YAAY,IAAA3G,cAAA,GAAAI,CAAA,SAAGd,qBAAqB,CAACsH,iBAAiB,CAAC,CAAC;QAAC5G,cAAA,GAAAI,CAAA;QAC/D6F,QAAQ,CAACpE,YAAY,GAAG8E,YAAY,CAACE,mBAAmB;MAC1D,CAAC;QAAA7G,cAAA,GAAAC,CAAA;MAAA;MAAAD,cAAA,GAAAI,CAAA;MAGD,IAAIiB,MAAM,CAACJ,wBAAwB,EAAE;QAAAjB,cAAA,GAAAC,CAAA;QACnC,IAAMiC,eAAe,IAAAlC,cAAA,GAAAI,CAAA,SAAGX,oBAAoB,CAACqH,8BAA8B,CAAC,CAAC;QAAC9G,cAAA,GAAAI,CAAA;QAC9E6F,QAAQ,CAAC/D,eAAe,GAAGA,eAAe;MAC5C,CAAC;QAAAlC,cAAA,GAAAC,CAAA;MAAA;MAGD,IAAM6B,WAAW,IAAA9B,cAAA,GAAAI,CAAA,eAASoF,cAAc,CAAC,CAAC;MAACxF,cAAA,GAAAI,CAAA;MAC3C6F,QAAQ,CAACnE,WAAW,GAAG;QACrBC,WAAW,EAAED,WAAW,CAACiF,MAAM,CAAC,UAAAlB,CAAC,EAAI;UAAA7F,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAyF,CAAC,CAACC,IAAI,KAAK,OAAO;QAAD,CAAC,CAAC,CAACF,GAAG,CAAC,UAAAC,CAAC,EAAI;UAAA7F,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAyF,CAAC,CAACmB,GAAG;QAAD,CAAC,CAAC;QACxEhF,aAAa,EAAEF,WAAW,CAACiF,MAAM,CAAC,UAAAlB,CAAC,EAAI;UAAA7F,cAAA,GAAAE,CAAA;UAAAF,cAAA,GAAAI,CAAA;UAAA,OAAAyF,CAAC,CAACC,IAAI,KAAK,UAAU;QAAD,CAAC,CAAC;QAC7D7D,gBAAgB,EAAEW,KAAK,CAACd,WAAW,CAACG;MACtC,CAAC;MAACjC,cAAA,GAAAI,CAAA;MAEFyC,QAAQ,CAAC,UAAAG,IAAI,EAAK;QAAAhD,cAAA,GAAAE,CAAA;QAAAF,cAAA,GAAAI,CAAA;QAAA,OAAAQ,MAAA,CAAAC,MAAA,KAAKmC,IAAI,EAAKiD,QAAQ;MAAC,CAAE,CAAC;IAE9C,CAAC,CAAC,OAAOxC,KAAK,EAAE;MAAAzD,cAAA,GAAAI,CAAA;MACdmD,OAAO,CAACE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC,GAAE,CAACpC,MAAM,EAAEhB,IAAI,EAAEuC,KAAK,CAACN,OAAO,EAAEM,KAAK,CAACd,WAAW,CAACG,gBAAgB,EAAEuD,cAAc,CAAC,CAAC;EAACxF,cAAA,GAAAI,CAAA;EAGtFjB,SAAS,CAAC,YAAM;IAAAa,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd,IAAI,CAAAJ,cAAA,GAAAC,CAAA,YAACoB,MAAM,CAACH,gBAAgB,MAAAlB,cAAA,GAAAC,CAAA,WAAI,CAAC2C,KAAK,CAACpB,aAAa,GAAE;MAAAxB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAE7D,IAAMgH,QAAQ,IAAAjH,cAAA,GAAAI,CAAA,SAAG8G,WAAW,CAAC,YAAM;MAAAlH,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACjC2E,WAAW,CAAC,CAAC;IACf,CAAC,EAAE1D,MAAM,CAACF,oBAAoB,CAAC;IAACnB,cAAA,GAAAI,CAAA;IAEhC,OAAO,YAAM;MAAAJ,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAA+G,aAAa,CAACF,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAAC5F,MAAM,CAACH,gBAAgB,EAAEG,MAAM,CAACF,oBAAoB,EAAEyB,KAAK,CAACpB,aAAa,EAAEuD,WAAW,CAAC,CAAC;EAAC/E,cAAA,GAAAI,CAAA;EAG7FjB,SAAS,CAAC,YAAM;IAAAa,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd,IAAI,CAACwC,KAAK,CAACpB,aAAa,EAAE;MAAAxB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAAA;IAAM,CAAC;MAAAJ,cAAA,GAAAC,CAAA;IAAA;IAEjC,IAAMgH,QAAQ,IAAAjH,cAAA,GAAAI,CAAA,SAAG8G,WAAW,CAAC,YAAM;MAAAlH,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MACjCkD,aAAa,CAAC,CAAC;IACjB,CAAC,EAAE,KAAK,CAAC;IAACtD,cAAA,GAAAI,CAAA;IAEV,OAAO,YAAM;MAAAJ,cAAA,GAAAE,CAAA;MAAAF,cAAA,GAAAI,CAAA;MAAA,OAAA+G,aAAa,CAACF,QAAQ,CAAC;IAAD,CAAC;EACtC,CAAC,EAAE,CAACrE,KAAK,CAACpB,aAAa,EAAE8B,aAAa,CAAC,CAAC;EAACtD,cAAA,GAAAI,CAAA;EAGzCjB,SAAS,CAAC,YAAM;IAAAa,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IACd,IAAI,CAAAJ,cAAA,GAAAC,CAAA,WAAAI,IAAI,MAAAL,cAAA,GAAAC,CAAA,WAAI,CAAC2C,KAAK,CAACpB,aAAa,GAAE;MAAAxB,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAI,CAAA;MAChC0C,UAAU,CAAC,CAAC;IACd,CAAC;MAAA9C,cAAA,GAAAC,CAAA;IAAA;EACH,CAAC,EAAE,CAACI,IAAI,EAAEuC,KAAK,CAACpB,aAAa,EAAEsB,UAAU,CAAC,CAAC;EAAC9C,cAAA,GAAAI,CAAA;EAG5C,OAAOf,OAAO,CAAC,YAAO;IAAAW,cAAA,GAAAE,CAAA;IAAAF,cAAA,GAAAI,CAAA;IAAA;MACpBwC,KAAK,EAALA,KAAK;MACLwE,OAAO,EAAE;QACPtE,UAAU,EAAVA,UAAU;QACVY,YAAY,EAAZA,YAAY;QACZc,eAAe,EAAfA,eAAe;QACfI,gBAAgB,EAAhBA,gBAAgB;QAChBE,UAAU,EAAVA,UAAU;QACVC,WAAW,EAAXA,WAAW;QACXK,YAAY,EAAZA,YAAY;QACZE,WAAW,EAAXA,WAAW;QACXE,cAAc,EAAdA;MACF,CAAC;MACDnE,MAAM,EAANA;IACF,CAAC;EAAD,CAAE,EAAE,CACFuB,KAAK,EACLE,UAAU,EACVY,YAAY,EACZc,eAAe,EACfI,gBAAgB,EAChBE,UAAU,EACVC,WAAW,EACXK,YAAY,EACZE,WAAW,EACXE,cAAc,EACdnE,MAAM,CACP,CAAC;AACJ;AAGA,SAAS0C,YAAYA,CAAA,EAAkD;EAAA/D,cAAA,GAAAE,CAAA;EACrE,IAAMmH,IAAI,IAAArH,cAAA,GAAAI,CAAA,SAAG,IAAI6D,IAAI,CAAC,CAAC,CAACqD,QAAQ,CAAC,CAAC;EAACtH,cAAA,GAAAI,CAAA;EACnC,IAAIiH,IAAI,GAAG,CAAC,EAAE;IAAArH,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAI,CAAA;IAAA,OAAO,OAAO;EAAA,CAAC;IAAAJ,cAAA,GAAAC,CAAA;EAAA;EAAAD,cAAA,GAAAI,CAAA;EAC7B,IAAIiH,IAAI,GAAG,EAAE,EAAE;IAAArH,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAI,CAAA;IAAA,OAAO,SAAS;EAAA,CAAC;IAAAJ,cAAA,GAAAC,CAAA;EAAA;EAAAD,cAAA,GAAAI,CAAA;EAChC,IAAIiH,IAAI,GAAG,EAAE,EAAE;IAAArH,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAI,CAAA;IAAA,OAAO,WAAW;EAAA,CAAC;IAAAJ,cAAA,GAAAC,CAAA;EAAA;EAAAD,cAAA,GAAAI,CAAA;EAClC,IAAIiH,IAAI,GAAG,EAAE,EAAE;IAAArH,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAI,CAAA;IAAA,OAAO,SAAS;EAAA,CAAC;IAAAJ,cAAA,GAAAC,CAAA;EAAA;EAAAD,cAAA,GAAAI,CAAA;EAChC,OAAO,OAAO;AAChB;AAEA,eAAeT,iBAAiB", "ignoreList": []}