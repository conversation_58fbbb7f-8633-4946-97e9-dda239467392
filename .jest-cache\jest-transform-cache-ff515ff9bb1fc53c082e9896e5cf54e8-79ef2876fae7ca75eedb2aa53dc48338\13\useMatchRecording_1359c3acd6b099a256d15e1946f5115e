df044945bc2049283bf5b09d3fb694fe
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_ip539199d() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\hooks\\useMatchRecording.ts";
  var hash = "d35ad6fb91a382a783bec635e1f9a86c463a0287";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\hooks\\useMatchRecording.ts",
    statementMap: {
      "0": {
        start: {
          line: 53,
          column: 49
        },
        end: {
          line: 62,
          column: 1
        }
      },
      "1": {
        start: {
          line: 64,
          column: 55
        },
        end: {
          line: 69,
          column: 1
        }
      },
      "2": {
        start: {
          line: 72,
          column: 19
        },
        end: {
          line: 72,
          column: 28
        }
      },
      "3": {
        start: {
          line: 75,
          column: 28
        },
        end: {
          line: 89,
          column: 4
        }
      },
      "4": {
        start: {
          line: 91,
          column: 52
        },
        end: {
          line: 91,
          column: 92
        }
      },
      "5": {
        start: {
          line: 92,
          column: 21
        },
        end: {
          line: 92,
          column: 54
        }
      },
      "6": {
        start: {
          line: 95,
          column: 2
        },
        end: {
          line: 137,
          column: 9
        }
      },
      "7": {
        start: {
          line: 96,
          column: 31
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "8": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 128,
          column: 7
        }
      },
      "9": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 98,
          column: 49
        }
      },
      "10": {
        start: {
          line: 101,
          column: 8
        },
        end: {
          line: 101,
          column: 72
        }
      },
      "11": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 112,
          column: 11
        }
      },
      "12": {
        start: {
          line: 105,
          column: 10
        },
        end: {
          line: 105,
          column: 39
        }
      },
      "13": {
        start: {
          line: 106,
          column: 10
        },
        end: {
          line: 111,
          column: 14
        }
      },
      "14": {
        start: {
          line: 106,
          column: 28
        },
        end: {
          line: 111,
          column: 11
        }
      },
      "15": {
        start: {
          line: 115,
          column: 8
        },
        end: {
          line: 120,
          column: 11
        }
      },
      "16": {
        start: {
          line: 116,
          column: 10
        },
        end: {
          line: 119,
          column: 14
        }
      },
      "17": {
        start: {
          line: 116,
          column: 28
        },
        end: {
          line: 119,
          column: 11
        }
      },
      "18": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 79
        }
      },
      "19": {
        start: {
          line: 124,
          column: 8
        },
        end: {
          line: 127,
          column: 12
        }
      },
      "20": {
        start: {
          line: 124,
          column: 26
        },
        end: {
          line: 127,
          column: 9
        }
      },
      "21": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 131,
          column: 25
        }
      },
      "22": {
        start: {
          line: 134,
          column: 4
        },
        end: {
          line: 136,
          column: 6
        }
      },
      "23": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 38
        }
      },
      "24": {
        start: {
          line: 140,
          column: 21
        },
        end: {
          line: 166,
          column: 12
        }
      },
      "25": {
        start: {
          line: 144,
          column: 4
        },
        end: {
          line: 146,
          column: 5
        }
      },
      "26": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 77
        }
      },
      "27": {
        start: {
          line: 148,
          column: 4
        },
        end: {
          line: 165,
          column: 5
        }
      },
      "28": {
        start: {
          line: 149,
          column: 6
        },
        end: {
          line: 149,
          column: 66
        }
      },
      "29": {
        start: {
          line: 149,
          column: 24
        },
        end: {
          line: 149,
          column: 63
        }
      },
      "30": {
        start: {
          line: 151,
          column: 26
        },
        end: {
          line: 151,
          column: 68
        }
      },
      "31": {
        start: {
          line: 152,
          column: 27
        },
        end: {
          line: 152,
          column: 59
        }
      },
      "32": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 154,
          column: 72
        }
      },
      "33": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 156,
          column: 54
        }
      },
      "34": {
        start: {
          line: 156,
          column: 24
        },
        end: {
          line: 156,
          column: 51
        }
      },
      "35": {
        start: {
          line: 158,
          column: 27
        },
        end: {
          line: 158,
          column: 91
        }
      },
      "36": {
        start: {
          line: 159,
          column: 6
        },
        end: {
          line: 163,
          column: 10
        }
      },
      "37": {
        start: {
          line: 159,
          column: 24
        },
        end: {
          line: 163,
          column: 7
        }
      },
      "38": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 18
        }
      },
      "39": {
        start: {
          line: 169,
          column: 19
        },
        end: {
          line: 194,
          column: 8
        }
      },
      "40": {
        start: {
          line: 170,
          column: 4
        },
        end: {
          line: 193,
          column: 5
        }
      },
      "41": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 66
        }
      },
      "42": {
        start: {
          line: 171,
          column: 24
        },
        end: {
          line: 171,
          column: 63
        }
      },
      "43": {
        start: {
          line: 173,
          column: 21
        },
        end: {
          line: 173,
          column: 59
        }
      },
      "44": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 181,
          column: 10
        }
      },
      "45": {
        start: {
          line: 175,
          column: 24
        },
        end: {
          line: 181,
          column: 7
        }
      },
      "46": {
        start: {
          line: 183,
          column: 6
        },
        end: {
          line: 183,
          column: 33
        }
      },
      "47": {
        start: {
          line: 184,
          column: 6
        },
        end: {
          line: 184,
          column: 20
        }
      },
      "48": {
        start: {
          line: 186,
          column: 27
        },
        end: {
          line: 186,
          column: 89
        }
      },
      "49": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 191,
          column: 10
        }
      },
      "50": {
        start: {
          line: 187,
          column: 24
        },
        end: {
          line: 191,
          column: 7
        }
      },
      "51": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 18
        }
      },
      "52": {
        start: {
          line: 197,
          column: 21
        },
        end: {
          line: 204,
          column: 8
        }
      },
      "53": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 203,
          column: 5
        }
      },
      "54": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 199,
          column: 47
        }
      },
      "55": {
        start: {
          line: 201,
          column: 27
        },
        end: {
          line: 201,
          column: 91
        }
      },
      "56": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 202,
          column: 59
        }
      },
      "57": {
        start: {
          line: 202,
          column: 24
        },
        end: {
          line: 202,
          column: 56
        }
      },
      "58": {
        start: {
          line: 207,
          column: 22
        },
        end: {
          line: 214,
          column: 8
        }
      },
      "59": {
        start: {
          line: 208,
          column: 4
        },
        end: {
          line: 213,
          column: 5
        }
      },
      "60": {
        start: {
          line: 209,
          column: 6
        },
        end: {
          line: 209,
          column: 48
        }
      },
      "61": {
        start: {
          line: 211,
          column: 27
        },
        end: {
          line: 211,
          column: 92
        }
      },
      "62": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 59
        }
      },
      "63": {
        start: {
          line: 212,
          column: 24
        },
        end: {
          line: 212,
          column: 56
        }
      },
      "64": {
        start: {
          line: 217,
          column: 22
        },
        end: {
          line: 240,
          column: 8
        }
      },
      "65": {
        start: {
          line: 218,
          column: 4
        },
        end: {
          line: 239,
          column: 5
        }
      },
      "66": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 66
        }
      },
      "67": {
        start: {
          line: 219,
          column: 24
        },
        end: {
          line: 219,
          column: 63
        }
      },
      "68": {
        start: {
          line: 221,
          column: 6
        },
        end: {
          line: 221,
          column: 48
        }
      },
      "69": {
        start: {
          line: 223,
          column: 6
        },
        end: {
          line: 229,
          column: 10
        }
      },
      "70": {
        start: {
          line: 223,
          column: 24
        },
        end: {
          line: 229,
          column: 7
        }
      },
      "71": {
        start: {
          line: 231,
          column: 6
        },
        end: {
          line: 231,
          column: 33
        }
      },
      "72": {
        start: {
          line: 233,
          column: 27
        },
        end: {
          line: 233,
          column: 92
        }
      },
      "73": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 238,
          column: 10
        }
      },
      "74": {
        start: {
          line: 234,
          column: 24
        },
        end: {
          line: 238,
          column: 7
        }
      },
      "75": {
        start: {
          line: 243,
          column: 19
        },
        end: {
          line: 254,
          column: 8
        }
      },
      "76": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 253,
          column: 5
        }
      },
      "77": {
        start: {
          line: 249,
          column: 6
        },
        end: {
          line: 249,
          column: 79
        }
      },
      "78": {
        start: {
          line: 251,
          column: 27
        },
        end: {
          line: 251,
          column: 89
        }
      },
      "79": {
        start: {
          line: 252,
          column: 6
        },
        end: {
          line: 252,
          column: 59
        }
      },
      "80": {
        start: {
          line: 252,
          column: 24
        },
        end: {
          line: 252,
          column: 56
        }
      },
      "81": {
        start: {
          line: 257,
          column: 24
        },
        end: {
          line: 265,
          column: 8
        }
      },
      "82": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 264,
          column: 5
        }
      },
      "83": {
        start: {
          line: 260,
          column: 6
        },
        end: {
          line: 260,
          column: 59
        }
      },
      "84": {
        start: {
          line: 262,
          column: 27
        },
        end: {
          line: 262,
          column: 90
        }
      },
      "85": {
        start: {
          line: 263,
          column: 6
        },
        end: {
          line: 263,
          column: 59
        }
      },
      "86": {
        start: {
          line: 263,
          column: 24
        },
        end: {
          line: 263,
          column: 56
        }
      },
      "87": {
        start: {
          line: 268,
          column: 31
        },
        end: {
          line: 281,
          column: 25
        }
      },
      "88": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 280,
          column: 5
        }
      },
      "89": {
        start: {
          line: 270,
          column: 6
        },
        end: {
          line: 276,
          column: 7
        }
      },
      "90": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 272,
          column: 52
        }
      },
      "91": {
        start: {
          line: 275,
          column: 8
        },
        end: {
          line: 275,
          column: 70
        }
      },
      "92": {
        start: {
          line: 278,
          column: 27
        },
        end: {
          line: 278,
          column: 102
        }
      },
      "93": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 59
        }
      },
      "94": {
        start: {
          line: 279,
          column: 24
        },
        end: {
          line: 279,
          column: 56
        }
      },
      "95": {
        start: {
          line: 284,
          column: 28
        },
        end: {
          line: 289,
          column: 8
        }
      },
      "96": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 288,
          column: 8
        }
      },
      "97": {
        start: {
          line: 285,
          column: 22
        },
        end: {
          line: 288,
          column: 5
        }
      },
      "98": {
        start: {
          line: 292,
          column: 28
        },
        end: {
          line: 312,
          column: 8
        }
      },
      "99": {
        start: {
          line: 293,
          column: 42
        },
        end: {
          line: 293,
          column: 44
        }
      },
      "100": {
        start: {
          line: 295,
          column: 4
        },
        end: {
          line: 297,
          column: 5
        }
      },
      "101": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 296,
          column: 56
        }
      },
      "102": {
        start: {
          line: 299,
          column: 4
        },
        end: {
          line: 301,
          column: 5
        }
      },
      "103": {
        start: {
          line: 300,
          column: 6
        },
        end: {
          line: 300,
          column: 50
        }
      },
      "104": {
        start: {
          line: 303,
          column: 4
        },
        end: {
          line: 305,
          column: 5
        }
      },
      "105": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 304,
          column: 54
        }
      },
      "106": {
        start: {
          line: 307,
          column: 4
        },
        end: {
          line: 309,
          column: 5
        }
      },
      "107": {
        start: {
          line: 308,
          column: 6
        },
        end: {
          line: 308,
          column: 51
        }
      },
      "108": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 311,
          column: 18
        }
      },
      "109": {
        start: {
          line: 315,
          column: 30
        },
        end: {
          line: 334,
          column: 12
        }
      },
      "110": {
        start: {
          line: 316,
          column: 4
        },
        end: {
          line: 318,
          column: 5
        }
      },
      "111": {
        start: {
          line: 317,
          column: 6
        },
        end: {
          line: 317,
          column: 52
        }
      },
      "112": {
        start: {
          line: 320,
          column: 4
        },
        end: {
          line: 333,
          column: 6
        }
      },
      "113": {
        start: {
          line: 337,
          column: 27
        },
        end: {
          line: 345,
          column: 8
        }
      },
      "114": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 338,
          column: 38
        }
      },
      "115": {
        start: {
          line: 338,
          column: 29
        },
        end: {
          line: 338,
          column: 38
        }
      },
      "116": {
        start: {
          line: 340,
          column: 24
        },
        end: {
          line: 340,
          column: 34
        }
      },
      "117": {
        start: {
          line: 341,
          column: 22
        },
        end: {
          line: 341,
          column: 50
        }
      },
      "118": {
        start: {
          line: 342,
          column: 27
        },
        end: {
          line: 342,
          column: 65
        }
      },
      "119": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 344,
          column: 73
        }
      },
      "120": {
        start: {
          line: 348,
          column: 28
        },
        end: {
          line: 355,
          column: 26
        }
      },
      "121": {
        start: {
          line: 349,
          column: 18
        },
        end: {
          line: 349,
          column: 36
        }
      },
      "122": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 350,
          column: 46
        }
      },
      "123": {
        start: {
          line: 350,
          column: 33
        },
        end: {
          line: 350,
          column: 46
        }
      },
      "124": {
        start: {
          line: 352,
          column: 4
        },
        end: {
          line: 354,
          column: 18
        }
      },
      "125": {
        start: {
          line: 353,
          column: 18
        },
        end: {
          line: 353,
          column: 57
        }
      },
      "126": {
        start: {
          line: 358,
          column: 28
        },
        end: {
          line: 360,
          column: 41
        }
      },
      "127": {
        start: {
          line: 359,
          column: 4
        },
        end: {
          line: 359,
          column: 48
        }
      },
      "128": {
        start: {
          line: 363,
          column: 22
        },
        end: {
          line: 365,
          column: 25
        }
      },
      "129": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 364,
          column: 62
        }
      },
      "130": {
        start: {
          line: 367,
          column: 2
        },
        end: {
          line: 386,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useMatchRecording",
        decl: {
          start: {
            line: 71,
            column: 16
          },
          end: {
            line: 71,
            column: 33
          }
        },
        loc: {
          start: {
            line: 71,
            column: 61
          },
          end: {
            line: 387,
            column: 1
          }
        },
        line: 71
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 95,
            column: 12
          },
          end: {
            line: 95,
            column: 13
          }
        },
        loc: {
          start: {
            line: 95,
            column: 18
          },
          end: {
            line: 137,
            column: 3
          }
        },
        line: 95
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 96,
            column: 31
          },
          end: {
            line: 96,
            column: 32
          }
        },
        loc: {
          start: {
            line: 96,
            column: 43
          },
          end: {
            line: 129,
            column: 5
          }
        },
        line: 96
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 104,
            column: 49
          },
          end: {
            line: 104,
            column: 50
          }
        },
        loc: {
          start: {
            line: 104,
            column: 62
          },
          end: {
            line: 112,
            column: 9
          }
        },
        line: 104
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 106,
            column: 19
          },
          end: {
            line: 106,
            column: 20
          }
        },
        loc: {
          start: {
            line: 106,
            column: 28
          },
          end: {
            line: 111,
            column: 11
          }
        },
        line: 106
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 115,
            column: 47
          },
          end: {
            line: 115,
            column: 48
          }
        },
        loc: {
          start: {
            line: 115,
            column: 58
          },
          end: {
            line: 120,
            column: 9
          }
        },
        line: 115
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 116,
            column: 19
          },
          end: {
            line: 116,
            column: 20
          }
        },
        loc: {
          start: {
            line: 116,
            column: 28
          },
          end: {
            line: 119,
            column: 11
          }
        },
        line: 116
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 124,
            column: 17
          },
          end: {
            line: 124,
            column: 18
          }
        },
        loc: {
          start: {
            line: 124,
            column: 26
          },
          end: {
            line: 127,
            column: 9
          }
        },
        line: 124
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 134,
            column: 11
          },
          end: {
            line: 134,
            column: 12
          }
        },
        loc: {
          start: {
            line: 134,
            column: 17
          },
          end: {
            line: 136,
            column: 5
          }
        },
        line: 134
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 140,
            column: 33
          },
          end: {
            line: 140,
            column: 34
          }
        },
        loc: {
          start: {
            line: 143,
            column: 7
          },
          end: {
            line: 166,
            column: 3
          }
        },
        line: 143
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 149,
            column: 15
          },
          end: {
            line: 149,
            column: 16
          }
        },
        loc: {
          start: {
            line: 149,
            column: 24
          },
          end: {
            line: 149,
            column: 63
          }
        },
        line: 149
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 156,
            column: 15
          },
          end: {
            line: 156,
            column: 16
          }
        },
        loc: {
          start: {
            line: 156,
            column: 24
          },
          end: {
            line: 156,
            column: 51
          }
        },
        line: 156
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 159,
            column: 15
          },
          end: {
            line: 159,
            column: 16
          }
        },
        loc: {
          start: {
            line: 159,
            column: 24
          },
          end: {
            line: 163,
            column: 7
          }
        },
        line: 159
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 169,
            column: 31
          },
          end: {
            line: 169,
            column: 32
          }
        },
        loc: {
          start: {
            line: 169,
            column: 75
          },
          end: {
            line: 194,
            column: 3
          }
        },
        line: 169
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 171,
            column: 15
          },
          end: {
            line: 171,
            column: 16
          }
        },
        loc: {
          start: {
            line: 171,
            column: 24
          },
          end: {
            line: 171,
            column: 63
          }
        },
        line: 171
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 175,
            column: 15
          },
          end: {
            line: 175,
            column: 16
          }
        },
        loc: {
          start: {
            line: 175,
            column: 24
          },
          end: {
            line: 181,
            column: 7
          }
        },
        line: 175
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 187,
            column: 15
          },
          end: {
            line: 187,
            column: 16
          }
        },
        loc: {
          start: {
            line: 187,
            column: 24
          },
          end: {
            line: 191,
            column: 7
          }
        },
        line: 187
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 197,
            column: 33
          },
          end: {
            line: 197,
            column: 34
          }
        },
        loc: {
          start: {
            line: 197,
            column: 45
          },
          end: {
            line: 204,
            column: 3
          }
        },
        line: 197
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 202,
            column: 15
          },
          end: {
            line: 202,
            column: 16
          }
        },
        loc: {
          start: {
            line: 202,
            column: 24
          },
          end: {
            line: 202,
            column: 56
          }
        },
        line: 202
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 207,
            column: 34
          },
          end: {
            line: 207,
            column: 35
          }
        },
        loc: {
          start: {
            line: 207,
            column: 46
          },
          end: {
            line: 214,
            column: 3
          }
        },
        line: 207
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 212,
            column: 15
          },
          end: {
            line: 212,
            column: 16
          }
        },
        loc: {
          start: {
            line: 212,
            column: 24
          },
          end: {
            line: 212,
            column: 56
          }
        },
        line: 212
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 217,
            column: 34
          },
          end: {
            line: 217,
            column: 35
          }
        },
        loc: {
          start: {
            line: 217,
            column: 46
          },
          end: {
            line: 240,
            column: 3
          }
        },
        line: 217
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 219,
            column: 15
          },
          end: {
            line: 219,
            column: 16
          }
        },
        loc: {
          start: {
            line: 219,
            column: 24
          },
          end: {
            line: 219,
            column: 63
          }
        },
        line: 219
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 223,
            column: 15
          },
          end: {
            line: 223,
            column: 16
          }
        },
        loc: {
          start: {
            line: 223,
            column: 24
          },
          end: {
            line: 229,
            column: 7
          }
        },
        line: 223
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 234,
            column: 15
          },
          end: {
            line: 234,
            column: 16
          }
        },
        loc: {
          start: {
            line: 234,
            column: 24
          },
          end: {
            line: 238,
            column: 7
          }
        },
        line: 234
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 243,
            column: 31
          },
          end: {
            line: 243,
            column: 32
          }
        },
        loc: {
          start: {
            line: 247,
            column: 7
          },
          end: {
            line: 254,
            column: 3
          }
        },
        line: 247
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 252,
            column: 15
          },
          end: {
            line: 252,
            column: 16
          }
        },
        loc: {
          start: {
            line: 252,
            column: 24
          },
          end: {
            line: 252,
            column: 56
          }
        },
        line: 252
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 257,
            column: 36
          },
          end: {
            line: 257,
            column: 37
          }
        },
        loc: {
          start: {
            line: 257,
            column: 48
          },
          end: {
            line: 265,
            column: 3
          }
        },
        line: 257
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 263,
            column: 15
          },
          end: {
            line: 263,
            column: 16
          }
        },
        loc: {
          start: {
            line: 263,
            column: 24
          },
          end: {
            line: 263,
            column: 56
          }
        },
        line: 263
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 268,
            column: 43
          },
          end: {
            line: 268,
            column: 44
          }
        },
        loc: {
          start: {
            line: 268,
            column: 55
          },
          end: {
            line: 281,
            column: 3
          }
        },
        line: 268
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 279,
            column: 15
          },
          end: {
            line: 279,
            column: 16
          }
        },
        loc: {
          start: {
            line: 279,
            column: 24
          },
          end: {
            line: 279,
            column: 56
          }
        },
        line: 279
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 284,
            column: 40
          },
          end: {
            line: 284,
            column: 41
          }
        },
        loc: {
          start: {
            line: 284,
            column: 83
          },
          end: {
            line: 289,
            column: 3
          }
        },
        line: 284
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 285,
            column: 13
          },
          end: {
            line: 285,
            column: 14
          }
        },
        loc: {
          start: {
            line: 285,
            column: 22
          },
          end: {
            line: 288,
            column: 5
          }
        },
        line: 285
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 292,
            column: 40
          },
          end: {
            line: 292,
            column: 41
          }
        },
        loc: {
          start: {
            line: 292,
            column: 92
          },
          end: {
            line: 312,
            column: 3
          }
        },
        line: 292
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 315,
            column: 42
          },
          end: {
            line: 315,
            column: 43
          }
        },
        loc: {
          start: {
            line: 315,
            column: 86
          },
          end: {
            line: 334,
            column: 3
          }
        },
        line: 315
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 337,
            column: 39
          },
          end: {
            line: 337,
            column: 40
          }
        },
        loc: {
          start: {
            line: 337,
            column: 53
          },
          end: {
            line: 345,
            column: 3
          }
        },
        line: 337
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 348,
            column: 40
          },
          end: {
            line: 348,
            column: 41
          }
        },
        loc: {
          start: {
            line: 348,
            column: 54
          },
          end: {
            line: 355,
            column: 3
          }
        },
        line: 348
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 353,
            column: 11
          },
          end: {
            line: 353,
            column: 12
          }
        },
        loc: {
          start: {
            line: 353,
            column: 18
          },
          end: {
            line: 353,
            column: 57
          }
        },
        line: 353
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 358,
            column: 40
          },
          end: {
            line: 358,
            column: 41
          }
        },
        loc: {
          start: {
            line: 358,
            column: 55
          },
          end: {
            line: 360,
            column: 3
          }
        },
        line: 358
      },
      "39": {
        name: "(anonymous_39)",
        decl: {
          start: {
            line: 363,
            column: 34
          },
          end: {
            line: 363,
            column: 35
          }
        },
        loc: {
          start: {
            line: 363,
            column: 49
          },
          end: {
            line: 365,
            column: 3
          }
        },
        line: 363
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 109,
            column: 25
          },
          end: {
            line: 109,
            column: 54
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 109,
            column: 25
          },
          end: {
            line: 109,
            column: 45
          }
        }, {
          start: {
            line: 109,
            column: 49
          },
          end: {
            line: 109,
            column: 54
          }
        }],
        line: 109
      },
      "1": {
        loc: {
          start: {
            line: 110,
            column: 22
          },
          end: {
            line: 110,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 110,
            column: 22
          },
          end: {
            line: 110,
            column: 39
          }
        }, {
          start: {
            line: 110,
            column: 43
          },
          end: {
            line: 110,
            column: 48
          }
        }],
        line: 110
      },
      "2": {
        loc: {
          start: {
            line: 126,
            column: 17
          },
          end: {
            line: 126,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 126,
            column: 42
          },
          end: {
            line: 126,
            column: 55
          }
        }, {
          start: {
            line: 126,
            column: 58
          },
          end: {
            line: 126,
            column: 89
          }
        }],
        line: 126
      },
      "3": {
        loc: {
          start: {
            line: 142,
            column: 4
          },
          end: {
            line: 142,
            column: 48
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 142,
            column: 46
          },
          end: {
            line: 142,
            column: 48
          }
        }],
        line: 142
      },
      "4": {
        loc: {
          start: {
            line: 144,
            column: 4
          },
          end: {
            line: 146,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 144,
            column: 4
          },
          end: {
            line: 146,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 144
      },
      "5": {
        loc: {
          start: {
            line: 158,
            column: 27
          },
          end: {
            line: 158,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 158,
            column: 52
          },
          end: {
            line: 158,
            column: 65
          }
        }, {
          start: {
            line: 158,
            column: 68
          },
          end: {
            line: 158,
            column: 91
          }
        }],
        line: 158
      },
      "6": {
        loc: {
          start: {
            line: 186,
            column: 27
          },
          end: {
            line: 186,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 186,
            column: 52
          },
          end: {
            line: 186,
            column: 65
          }
        }, {
          start: {
            line: 186,
            column: 68
          },
          end: {
            line: 186,
            column: 89
          }
        }],
        line: 186
      },
      "7": {
        loc: {
          start: {
            line: 201,
            column: 27
          },
          end: {
            line: 201,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 201,
            column: 52
          },
          end: {
            line: 201,
            column: 65
          }
        }, {
          start: {
            line: 201,
            column: 68
          },
          end: {
            line: 201,
            column: 91
          }
        }],
        line: 201
      },
      "8": {
        loc: {
          start: {
            line: 211,
            column: 27
          },
          end: {
            line: 211,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 211,
            column: 52
          },
          end: {
            line: 211,
            column: 65
          }
        }, {
          start: {
            line: 211,
            column: 68
          },
          end: {
            line: 211,
            column: 92
          }
        }],
        line: 211
      },
      "9": {
        loc: {
          start: {
            line: 233,
            column: 27
          },
          end: {
            line: 233,
            column: 92
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 233,
            column: 52
          },
          end: {
            line: 233,
            column: 65
          }
        }, {
          start: {
            line: 233,
            column: 68
          },
          end: {
            line: 233,
            column: 92
          }
        }],
        line: 233
      },
      "10": {
        loc: {
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 245,
            column: 32
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 245,
            column: 24
          },
          end: {
            line: 245,
            column: 32
          }
        }],
        line: 245
      },
      "11": {
        loc: {
          start: {
            line: 251,
            column: 27
          },
          end: {
            line: 251,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 251,
            column: 52
          },
          end: {
            line: 251,
            column: 65
          }
        }, {
          start: {
            line: 251,
            column: 68
          },
          end: {
            line: 251,
            column: 89
          }
        }],
        line: 251
      },
      "12": {
        loc: {
          start: {
            line: 262,
            column: 27
          },
          end: {
            line: 262,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 262,
            column: 52
          },
          end: {
            line: 262,
            column: 65
          }
        }, {
          start: {
            line: 262,
            column: 68
          },
          end: {
            line: 262,
            column: 90
          }
        }],
        line: 262
      },
      "13": {
        loc: {
          start: {
            line: 270,
            column: 6
          },
          end: {
            line: 276,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 270,
            column: 6
          },
          end: {
            line: 276,
            column: 7
          }
        }, {
          start: {
            line: 273,
            column: 13
          },
          end: {
            line: 276,
            column: 7
          }
        }],
        line: 270
      },
      "14": {
        loc: {
          start: {
            line: 278,
            column: 27
          },
          end: {
            line: 278,
            column: 102
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 278,
            column: 52
          },
          end: {
            line: 278,
            column: 65
          }
        }, {
          start: {
            line: 278,
            column: 68
          },
          end: {
            line: 278,
            column: 102
          }
        }],
        line: 278
      },
      "15": {
        loc: {
          start: {
            line: 295,
            column: 4
          },
          end: {
            line: 297,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 295,
            column: 4
          },
          end: {
            line: 297,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 295
      },
      "16": {
        loc: {
          start: {
            line: 299,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 299,
            column: 4
          },
          end: {
            line: 301,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 299
      },
      "17": {
        loc: {
          start: {
            line: 303,
            column: 4
          },
          end: {
            line: 305,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 303,
            column: 4
          },
          end: {
            line: 305,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 303
      },
      "18": {
        loc: {
          start: {
            line: 307,
            column: 4
          },
          end: {
            line: 309,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 307,
            column: 4
          },
          end: {
            line: 309,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 307
      },
      "19": {
        loc: {
          start: {
            line: 316,
            column: 4
          },
          end: {
            line: 318,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 316,
            column: 4
          },
          end: {
            line: 318,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 316
      },
      "20": {
        loc: {
          start: {
            line: 338,
            column: 4
          },
          end: {
            line: 338,
            column: 38
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 338,
            column: 4
          },
          end: {
            line: 338,
            column: 38
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 338
      },
      "21": {
        loc: {
          start: {
            line: 350,
            column: 4
          },
          end: {
            line: 350,
            column: 46
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 350,
            column: 4
          },
          end: {
            line: 350,
            column: 46
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 350
      },
      "22": {
        loc: {
          start: {
            line: 359,
            column: 11
          },
          end: {
            line: 359,
            column: 47
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 359,
            column: 11
          },
          end: {
            line: 359,
            column: 28
          }
        }, {
          start: {
            line: 359,
            column: 32
          },
          end: {
            line: 359,
            column: 47
          }
        }],
        line: 359
      },
      "23": {
        loc: {
          start: {
            line: 364,
            column: 11
          },
          end: {
            line: 364,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 364,
            column: 11
          },
          end: {
            line: 364,
            column: 30
          }
        }, {
          start: {
            line: 364,
            column: 34
          },
          end: {
            line: 364,
            column: 61
          }
        }],
        line: 364
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d35ad6fb91a382a783bec635e1f9a86c463a0287"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_ip539199d = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_ip539199d();
import { useState, useEffect, useCallback, useRef } from 'react';
import { matchRecordingService } from "../services/match/MatchRecordingService";
import { videoRecordingService } from "../services/video/VideoRecordingService";
import { useAuth } from "../../contexts/AuthContext";
var defaultVideoConfig = (cov_ip539199d().s[0]++, {
  quality: 'medium',
  fps: 30,
  resolution: '720p',
  codec: 'h264',
  maxDurationMinutes: 180,
  maxFileSizeMB: 500,
  enableAudio: true,
  enableStabilization: true
});
var defaultRecordingOptions = (cov_ip539199d().s[1]++, {
  enableVideoRecording: true,
  enableAutoScoreDetection: false,
  videoConfig: defaultVideoConfig,
  enableStatisticsTracking: true
});
export function useMatchRecording() {
  cov_ip539199d().f[0]++;
  var _ref = (cov_ip539199d().s[2]++, useAuth()),
    user = _ref.user;
  var _ref2 = (cov_ip539199d().s[3]++, useState({
      session: null,
      isRecording: false,
      isPaused: false,
      currentScore: {
        sets: [],
        finalScore: '',
        result: 'win',
        setsWon: 0,
        setsLost: 0
      },
      videoConfig: defaultVideoConfig,
      error: null,
      loading: false
    })),
    _ref3 = _slicedToArray(_ref2, 2),
    state = _ref3[0],
    setState = _ref3[1];
  var _ref4 = (cov_ip539199d().s[4]++, useState(null)),
    _ref5 = _slicedToArray(_ref4, 2),
    recordingProgress = _ref5[0],
    setRecordingProgress = _ref5[1];
  var sessionRef = (cov_ip539199d().s[5]++, useRef(null));
  cov_ip539199d().s[6]++;
  useEffect(function () {
    cov_ip539199d().f[1]++;
    cov_ip539199d().s[7]++;
    var initializeServices = function () {
      var _ref6 = _asyncToGenerator(function* () {
        cov_ip539199d().f[2]++;
        cov_ip539199d().s[8]++;
        try {
          cov_ip539199d().s[9]++;
          yield videoRecordingService.initialize();
          cov_ip539199d().s[10]++;
          videoRecordingService.setProgressCallback(setRecordingProgress);
          cov_ip539199d().s[11]++;
          matchRecordingService.addSessionListener(function (session) {
            cov_ip539199d().f[3]++;
            cov_ip539199d().s[12]++;
            sessionRef.current = session;
            cov_ip539199d().s[13]++;
            setState(function (prev) {
              cov_ip539199d().f[4]++;
              cov_ip539199d().s[14]++;
              return Object.assign({}, prev, {
                session: session,
                isRecording: (cov_ip539199d().b[0][0]++, session == null ? void 0 : session.isRecording) || (cov_ip539199d().b[0][1]++, false),
                isPaused: (cov_ip539199d().b[1][0]++, session == null ? void 0 : session.isPaused) || (cov_ip539199d().b[1][1]++, false)
              });
            });
          });
          cov_ip539199d().s[15]++;
          matchRecordingService.addScoreListener(function (score) {
            cov_ip539199d().f[5]++;
            cov_ip539199d().s[16]++;
            setState(function (prev) {
              cov_ip539199d().f[6]++;
              cov_ip539199d().s[17]++;
              return Object.assign({}, prev, {
                currentScore: score
              });
            });
          });
        } catch (error) {
          cov_ip539199d().s[18]++;
          console.error('Failed to initialize match recording services:', error);
          cov_ip539199d().s[19]++;
          setState(function (prev) {
            cov_ip539199d().f[7]++;
            cov_ip539199d().s[20]++;
            return Object.assign({}, prev, {
              error: error instanceof Error ? (cov_ip539199d().b[2][0]++, error.message) : (cov_ip539199d().b[2][1]++, 'Failed to initialize services')
            });
          });
        }
      });
      return function initializeServices() {
        return _ref6.apply(this, arguments);
      };
    }();
    cov_ip539199d().s[21]++;
    initializeServices();
    cov_ip539199d().s[22]++;
    return function () {
      cov_ip539199d().f[8]++;
      cov_ip539199d().s[23]++;
      videoRecordingService.cleanup();
    };
  }, []);
  var startMatch = (cov_ip539199d().s[24]++, useCallback(function () {
    var _ref7 = _asyncToGenerator(function* (metadata) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_ip539199d().b[3][0]++, {});
      cov_ip539199d().f[9]++;
      cov_ip539199d().s[25]++;
      if (!user) {
        cov_ip539199d().b[4][0]++;
        cov_ip539199d().s[26]++;
        throw new Error('User must be authenticated to start match recording');
      } else {
        cov_ip539199d().b[4][1]++;
      }
      cov_ip539199d().s[27]++;
      try {
        cov_ip539199d().s[28]++;
        setState(function (prev) {
          cov_ip539199d().f[10]++;
          cov_ip539199d().s[29]++;
          return Object.assign({}, prev, {
            loading: true,
            error: null
          });
        });
        var fullOptions = (cov_ip539199d().s[30]++, Object.assign({}, defaultRecordingOptions, options));
        var fullMetadata = (cov_ip539199d().s[31]++, Object.assign({}, metadata, {
          userId: user.id
        }));
        cov_ip539199d().s[32]++;
        yield matchRecordingService.startMatch(fullMetadata, fullOptions);
        cov_ip539199d().s[33]++;
        setState(function (prev) {
          cov_ip539199d().f[11]++;
          cov_ip539199d().s[34]++;
          return Object.assign({}, prev, {
            loading: false
          });
        });
      } catch (error) {
        var errorMessage = (cov_ip539199d().s[35]++, error instanceof Error ? (cov_ip539199d().b[5][0]++, error.message) : (cov_ip539199d().b[5][1]++, 'Failed to start match'));
        cov_ip539199d().s[36]++;
        setState(function (prev) {
          cov_ip539199d().f[12]++;
          cov_ip539199d().s[37]++;
          return Object.assign({}, prev, {
            loading: false,
            error: errorMessage
          });
        });
        cov_ip539199d().s[38]++;
        throw error;
      }
    });
    return function (_x) {
      return _ref7.apply(this, arguments);
    };
  }(), [user]));
  var endMatch = (cov_ip539199d().s[39]++, useCallback(_asyncToGenerator(function* () {
    cov_ip539199d().f[13]++;
    cov_ip539199d().s[40]++;
    try {
      cov_ip539199d().s[41]++;
      setState(function (prev) {
        cov_ip539199d().f[14]++;
        cov_ip539199d().s[42]++;
        return Object.assign({}, prev, {
          loading: true,
          error: null
        });
      });
      var result = (cov_ip539199d().s[43]++, yield matchRecordingService.endMatch());
      cov_ip539199d().s[44]++;
      setState(function (prev) {
        cov_ip539199d().f[15]++;
        cov_ip539199d().s[45]++;
        return Object.assign({}, prev, {
          loading: false,
          session: null,
          isRecording: false,
          isPaused: false
        });
      });
      cov_ip539199d().s[46]++;
      setRecordingProgress(null);
      cov_ip539199d().s[47]++;
      return result;
    } catch (error) {
      var errorMessage = (cov_ip539199d().s[48]++, error instanceof Error ? (cov_ip539199d().b[6][0]++, error.message) : (cov_ip539199d().b[6][1]++, 'Failed to end match'));
      cov_ip539199d().s[49]++;
      setState(function (prev) {
        cov_ip539199d().f[16]++;
        cov_ip539199d().s[50]++;
        return Object.assign({}, prev, {
          loading: false,
          error: errorMessage
        });
      });
      cov_ip539199d().s[51]++;
      return null;
    }
  }), []));
  var pauseMatch = (cov_ip539199d().s[52]++, useCallback(_asyncToGenerator(function* () {
    cov_ip539199d().f[17]++;
    cov_ip539199d().s[53]++;
    try {
      cov_ip539199d().s[54]++;
      yield matchRecordingService.pauseMatch();
    } catch (error) {
      var errorMessage = (cov_ip539199d().s[55]++, error instanceof Error ? (cov_ip539199d().b[7][0]++, error.message) : (cov_ip539199d().b[7][1]++, 'Failed to pause match'));
      cov_ip539199d().s[56]++;
      setState(function (prev) {
        cov_ip539199d().f[18]++;
        cov_ip539199d().s[57]++;
        return Object.assign({}, prev, {
          error: errorMessage
        });
      });
    }
  }), []));
  var resumeMatch = (cov_ip539199d().s[58]++, useCallback(_asyncToGenerator(function* () {
    cov_ip539199d().f[19]++;
    cov_ip539199d().s[59]++;
    try {
      cov_ip539199d().s[60]++;
      yield matchRecordingService.resumeMatch();
    } catch (error) {
      var errorMessage = (cov_ip539199d().s[61]++, error instanceof Error ? (cov_ip539199d().b[8][0]++, error.message) : (cov_ip539199d().b[8][1]++, 'Failed to resume match'));
      cov_ip539199d().s[62]++;
      setState(function (prev) {
        cov_ip539199d().f[20]++;
        cov_ip539199d().s[63]++;
        return Object.assign({}, prev, {
          error: errorMessage
        });
      });
    }
  }), []));
  var cancelMatch = (cov_ip539199d().s[64]++, useCallback(_asyncToGenerator(function* () {
    cov_ip539199d().f[21]++;
    cov_ip539199d().s[65]++;
    try {
      cov_ip539199d().s[66]++;
      setState(function (prev) {
        cov_ip539199d().f[22]++;
        cov_ip539199d().s[67]++;
        return Object.assign({}, prev, {
          loading: true,
          error: null
        });
      });
      cov_ip539199d().s[68]++;
      yield matchRecordingService.cancelMatch();
      cov_ip539199d().s[69]++;
      setState(function (prev) {
        cov_ip539199d().f[23]++;
        cov_ip539199d().s[70]++;
        return Object.assign({}, prev, {
          loading: false,
          session: null,
          isRecording: false,
          isPaused: false
        });
      });
      cov_ip539199d().s[71]++;
      setRecordingProgress(null);
    } catch (error) {
      var errorMessage = (cov_ip539199d().s[72]++, error instanceof Error ? (cov_ip539199d().b[9][0]++, error.message) : (cov_ip539199d().b[9][1]++, 'Failed to cancel match'));
      cov_ip539199d().s[73]++;
      setState(function (prev) {
        cov_ip539199d().f[24]++;
        cov_ip539199d().s[74]++;
        return Object.assign({}, prev, {
          loading: false,
          error: errorMessage
        });
      });
    }
  }), []));
  var addPoint = (cov_ip539199d().s[75]++, useCallback(function () {
    var _ref10 = _asyncToGenerator(function* (winner) {
      var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_ip539199d().b[10][0]++, 'normal');
      var shotType = arguments.length > 2 ? arguments[2] : undefined;
      cov_ip539199d().f[25]++;
      cov_ip539199d().s[76]++;
      try {
        cov_ip539199d().s[77]++;
        yield matchRecordingService.addPoint(winner, eventType, shotType);
      } catch (error) {
        var errorMessage = (cov_ip539199d().s[78]++, error instanceof Error ? (cov_ip539199d().b[11][0]++, error.message) : (cov_ip539199d().b[11][1]++, 'Failed to add point'));
        cov_ip539199d().s[79]++;
        setState(function (prev) {
          cov_ip539199d().f[26]++;
          cov_ip539199d().s[80]++;
          return Object.assign({}, prev, {
            error: errorMessage
          });
        });
      }
    });
    return function (_x2) {
      return _ref10.apply(this, arguments);
    };
  }(), []));
  var undoLastPoint = (cov_ip539199d().s[81]++, useCallback(_asyncToGenerator(function* () {
    cov_ip539199d().f[27]++;
    cov_ip539199d().s[82]++;
    try {
      cov_ip539199d().s[83]++;
      console.log('Undo last point - not yet implemented');
    } catch (error) {
      var errorMessage = (cov_ip539199d().s[84]++, error instanceof Error ? (cov_ip539199d().b[12][0]++, error.message) : (cov_ip539199d().b[12][1]++, 'Failed to undo point'));
      cov_ip539199d().s[85]++;
      setState(function (prev) {
        cov_ip539199d().f[28]++;
        cov_ip539199d().s[86]++;
        return Object.assign({}, prev, {
          error: errorMessage
        });
      });
    }
  }), []));
  var toggleVideoRecording = (cov_ip539199d().s[87]++, useCallback(_asyncToGenerator(function* () {
    cov_ip539199d().f[29]++;
    cov_ip539199d().s[88]++;
    try {
      var _sessionRef$current;
      cov_ip539199d().s[89]++;
      if ((_sessionRef$current = sessionRef.current) != null && _sessionRef$current.videoRecordingActive) {
        cov_ip539199d().b[13][0]++;
        cov_ip539199d().s[90]++;
        yield videoRecordingService.stopRecording();
      } else {
        cov_ip539199d().b[13][1]++;
        cov_ip539199d().s[91]++;
        yield videoRecordingService.startRecording(state.videoConfig);
      }
    } catch (error) {
      var errorMessage = (cov_ip539199d().s[92]++, error instanceof Error ? (cov_ip539199d().b[14][0]++, error.message) : (cov_ip539199d().b[14][1]++, 'Failed to toggle video recording'));
      cov_ip539199d().s[93]++;
      setState(function (prev) {
        cov_ip539199d().f[30]++;
        cov_ip539199d().s[94]++;
        return Object.assign({}, prev, {
          error: errorMessage
        });
      });
    }
  }), [state.videoConfig]));
  var updateVideoConfig = (cov_ip539199d().s[95]++, useCallback(function (config) {
    cov_ip539199d().f[31]++;
    cov_ip539199d().s[96]++;
    setState(function (prev) {
      cov_ip539199d().f[32]++;
      cov_ip539199d().s[97]++;
      return Object.assign({}, prev, {
        videoConfig: Object.assign({}, prev.videoConfig, config)
      });
    });
  }, []));
  var validateMatchForm = (cov_ip539199d().s[98]++, useCallback(function (formData) {
    var _formData$opponentNam;
    cov_ip539199d().f[33]++;
    var errors = (cov_ip539199d().s[99]++, {});
    cov_ip539199d().s[100]++;
    if (!((_formData$opponentNam = formData.opponentName) != null && _formData$opponentNam.trim())) {
      cov_ip539199d().b[15][0]++;
      cov_ip539199d().s[101]++;
      errors.opponentName = 'Opponent name is required';
    } else {
      cov_ip539199d().b[15][1]++;
    }
    cov_ip539199d().s[102]++;
    if (!formData.matchType) {
      cov_ip539199d().b[16][0]++;
      cov_ip539199d().s[103]++;
      errors.matchType = 'Match type is required';
    } else {
      cov_ip539199d().b[16][1]++;
    }
    cov_ip539199d().s[104]++;
    if (!formData.matchFormat) {
      cov_ip539199d().b[17][0]++;
      cov_ip539199d().s[105]++;
      errors.matchFormat = 'Match format is required';
    } else {
      cov_ip539199d().b[17][1]++;
    }
    cov_ip539199d().s[106]++;
    if (!formData.surface) {
      cov_ip539199d().b[18][0]++;
      cov_ip539199d().s[107]++;
      errors.surface = 'Court surface is required';
    } else {
      cov_ip539199d().b[18][1]++;
    }
    cov_ip539199d().s[108]++;
    return errors;
  }, []));
  var createMatchMetadata = (cov_ip539199d().s[109]++, useCallback(function (formData) {
    cov_ip539199d().f[34]++;
    cov_ip539199d().s[110]++;
    if (!user) {
      cov_ip539199d().b[19][0]++;
      cov_ip539199d().s[111]++;
      throw new Error('User must be authenticated');
    } else {
      cov_ip539199d().b[19][1]++;
    }
    cov_ip539199d().s[112]++;
    return {
      userId: user.id,
      opponentName: formData.opponentName,
      matchType: formData.matchType,
      matchFormat: formData.matchFormat,
      surface: formData.surface,
      location: formData.location,
      courtName: formData.courtName,
      weatherConditions: formData.weatherConditions,
      temperature: formData.temperature,
      tournamentName: formData.tournamentName,
      tournamentRound: formData.tournamentRound,
      matchDate: new Date().toISOString().split('T')[0]
    };
  }, [user]));
  var getMatchDuration = (cov_ip539199d().s[113]++, useCallback(function () {
    cov_ip539199d().f[35]++;
    cov_ip539199d().s[114]++;
    if (!sessionRef.current) {
      cov_ip539199d().b[20][0]++;
      cov_ip539199d().s[115]++;
      return 0;
    } else {
      cov_ip539199d().b[20][1]++;
    }
    var currentTime = (cov_ip539199d().s[116]++, Date.now());
    var startTime = (cov_ip539199d().s[117]++, sessionRef.current.startTime);
    var pausedDuration = (cov_ip539199d().s[118]++, sessionRef.current.totalPausedDuration);
    cov_ip539199d().s[119]++;
    return Math.floor((currentTime - startTime - pausedDuration) / 1000);
  }, []));
  var getFormattedScore = (cov_ip539199d().s[120]++, useCallback(function () {
    cov_ip539199d().f[36]++;
    var score = (cov_ip539199d().s[121]++, state.currentScore);
    cov_ip539199d().s[122]++;
    if (score.sets.length === 0) {
      cov_ip539199d().b[21][0]++;
      cov_ip539199d().s[123]++;
      return '0-0';
    } else {
      cov_ip539199d().b[21][1]++;
    }
    cov_ip539199d().s[124]++;
    return score.sets.map(function (set) {
      cov_ip539199d().f[37]++;
      cov_ip539199d().s[125]++;
      return `${set.userGames}-${set.opponentGames}`;
    }).join(', ');
  }, [state.currentScore]));
  var isMatchInProgress = (cov_ip539199d().s[126]++, useCallback(function () {
    cov_ip539199d().f[38]++;
    cov_ip539199d().s[127]++;
    return (cov_ip539199d().b[22][0]++, state.isRecording) && (cov_ip539199d().b[22][1]++, !state.isPaused);
  }, [state.isRecording, state.isPaused]));
  var canAddPoint = (cov_ip539199d().s[128]++, useCallback(function () {
    cov_ip539199d().f[39]++;
    cov_ip539199d().s[129]++;
    return (cov_ip539199d().b[23][0]++, isMatchInProgress()) && (cov_ip539199d().b[23][1]++, sessionRef.current !== null);
  }, [isMatchInProgress]));
  cov_ip539199d().s[130]++;
  return {
    state: state,
    currentSession: sessionRef.current,
    recordingProgress: recordingProgress,
    startMatch: startMatch,
    endMatch: endMatch,
    pauseMatch: pauseMatch,
    resumeMatch: resumeMatch,
    cancelMatch: cancelMatch,
    addPoint: addPoint,
    undoLastPoint: undoLastPoint,
    toggleVideoRecording: toggleVideoRecording,
    updateVideoConfig: updateVideoConfig,
    validateMatchForm: validateMatchForm,
    createMatchMetadata: createMatchMetadata,
    getMatchDuration: getMatchDuration,
    getFormattedScore: getFormattedScore,
    isMatchInProgress: isMatchInProgress,
    canAddPoint: canAddPoint
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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