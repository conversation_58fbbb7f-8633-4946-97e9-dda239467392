{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_interopRequireDefault", "default", "exports", "__esModule", "_Dimensions", "PixelRatio", "key", "value", "get", "scale", "getFontScale", "fontScale", "getPixelSizeForLayoutSize", "layoutSize", "Math", "round", "roundToNearestPixel", "ratio", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _Dimensions = _interopRequireDefault(require(\"../Dimensions\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n/**\n * PixelRatio gives access to the device pixel density.\n */\nclass PixelRatio {\n  /**\n   * Returns the device pixel density.\n   */\n  static get() {\n    return _Dimensions.default.get('window').scale;\n  }\n\n  /**\n   * No equivalent for Web\n   */\n  static getFontScale() {\n    return _Dimensions.default.get('window').fontScale || PixelRatio.get();\n  }\n\n  /**\n   * Converts a layout size (dp) to pixel size (px).\n   * Guaranteed to return an integer number.\n   */\n  static getPixelSizeForLayoutSize(layoutSize) {\n    return Math.round(layoutSize * PixelRatio.get());\n  }\n\n  /**\n   * Rounds a layout size (dp) to the nearest layout size that corresponds to\n   * an integer number of pixels. For example, on a device with a PixelRatio\n   * of 3, `PixelRatio.roundToNearestPixel(8.4) = 8.33`, which corresponds to\n   * exactly (8.33 * 3) = 25 pixels.\n   */\n  static roundToNearestPixel(layoutSize) {\n    var ratio = PixelRatio.get();\n    return Math.round(layoutSize * ratio) / ratio;\n  }\n}\nexports.default = PixelRatio;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAEb,IAAIG,sBAAsB,GAAGH,OAAO,CAAC,8CAA8C,CAAC,CAACI,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,WAAW,GAAGJ,sBAAsB,CAACH,OAAO,gBAAgB,CAAC,CAAC;AAAC,IAc7DQ,UAAU;EAAA,SAAAA,WAAA;IAAA,IAAAP,gBAAA,CAAAG,OAAA,QAAAI,UAAA;EAAA;EAAA,WAAAN,aAAA,CAAAE,OAAA,EAAAI,UAAA;IAAAC,GAAA;IAAAC,KAAA,EAId,SAAOC,GAAGA,CAAA,EAAG;MACX,OAAOJ,WAAW,CAACH,OAAO,CAACO,GAAG,CAAC,QAAQ,CAAC,CAACC,KAAK;IAChD;EAAC;IAAAH,GAAA;IAAAC,KAAA,EAKD,SAAOG,YAAYA,CAAA,EAAG;MACpB,OAAON,WAAW,CAACH,OAAO,CAACO,GAAG,CAAC,QAAQ,CAAC,CAACG,SAAS,IAAIN,UAAU,CAACG,GAAG,CAAC,CAAC;IACxE;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAMD,SAAOK,yBAAyBA,CAACC,UAAU,EAAE;MAC3C,OAAOC,IAAI,CAACC,KAAK,CAACF,UAAU,GAAGR,UAAU,CAACG,GAAG,CAAC,CAAC,CAAC;IAClD;EAAC;IAAAF,GAAA;IAAAC,KAAA,EAQD,SAAOS,mBAAmBA,CAACH,UAAU,EAAE;MACrC,IAAII,KAAK,GAAGZ,UAAU,CAACG,GAAG,CAAC,CAAC;MAC5B,OAAOM,IAAI,CAACC,KAAK,CAACF,UAAU,GAAGI,KAAK,CAAC,GAAGA,KAAK;IAC/C;EAAC;AAAA;AAEHf,OAAO,CAACD,OAAO,GAAGI,UAAU;AAC5Ba,MAAM,CAAChB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}