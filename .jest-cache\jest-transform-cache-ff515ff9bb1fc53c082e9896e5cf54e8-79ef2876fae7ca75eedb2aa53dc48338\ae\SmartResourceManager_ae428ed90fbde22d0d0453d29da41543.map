{"version": 3, "names": ["performanceMonitor", "SmartResourceManager", "_classCallCheck", "currentMetrics", "cov_1zsc58rp56", "s", "resourceHistory", "activeOptimizations", "Map", "MONITORING_INTERVAL", "OPTIMIZATION_INTERVAL", "HISTORY_LIMIT", "THRESHOLDS", "memory", "warning", "critical", "cpu", "battery", "network", "latencyWarning", "failureRateWarning", "f", "predictionModel", "ResourcePredictionModel", "initializeResourceManager", "_createClass", "key", "value", "_initializeResourceManager", "_asyncToGenerator", "startResourceMonitoring", "startOptimizationEngine", "initialize", "console", "log", "error", "apply", "arguments", "getCurrentMetrics", "_getResourcePredictions", "timeHorizon", "length", "undefined", "b", "predictions", "predict", "slice", "getResourcePredictions", "_optimizeResources", "optimizations", "memoryOpts", "generateMemoryOptimizations", "push", "_toConsumableArray", "cpuOpts", "generateCpuOptimizations", "networkOpts", "generateNetworkOptimizations", "batteryOpts", "generateBatteryOptimizations", "highPriorityOpts", "filter", "opt", "risk", "expected<PERSON>ain", "executeOptimizations", "optimizeResources", "getOptimizationRecommendations", "immediate", "shortTerm", "longTerm", "pressure", "usage", "level", "failureRate", "getResourceEfficiencyScore", "overall", "calculateMemoryEfficiency", "calculateCpuEfficiency", "calculateNetworkEfficiency", "calculateBatteryEfficiency", "_this", "setInterval", "collectResourceMetrics", "_this2", "performAutomaticOptimization", "_collectResourceMetrics", "metrics", "collectMemoryMetrics", "collectCpuMetrics", "collectNetworkMetrics", "collectBatteryMetrics", "storage", "collectStorageMetrics", "timestamp", "Date", "now", "shift", "checkCriticalConditions", "_collectMemoryMetrics", "used", "Math", "random", "available", "leaks", "gcFrequency", "_collectCpuMetrics", "temperature", "throttling", "heavyTasks", "_collectNetworkMetrics", "bandwidth", "latency", "activeConnections", "floor", "queuedRequests", "_collectBatteryMetrics", "drainRate", "isCharging", "estimatedTimeRemaining", "_collectStorageMetrics", "ioOperations", "cacheSize", "_checkCriticalConditions", "alerts", "handleCriticalMemory", "handleCriticalCpu", "handleCriticalBattery", "warn", "_x", "_handleCriticalMemory", "_handleCriticalCpu", "_handleCriticalBattery", "_performAutomaticOptimization", "safeOptimizations", "_generateMemoryOptimizations", "type", "action", "target", "implementation", "_implementation", "_generateCpuOptimizations", "_implementation2", "_generateNetworkOptimizations", "_implementation3", "_generateBatteryOptimizations", "_implementation4", "_executeOptimizations", "optimization", "set", "trackDatabaseQuery", "_x2", "max", "latencyScore", "failureScore", "levelScore", "drainScore", "_initialize", "_predict", "history", "latest", "previous", "memoryTrend", "predictedM<PERSON>ory", "resource", "predictedUsage", "confidence", "recommendations", "cpuTrend", "predictedCpu", "_x3", "_x4", "smartResourceManager"], "sources": ["SmartResourceManager.ts"], "sourcesContent": ["/**\n * Smart Resource Manager\n * \n * AI-driven resource management system that optimizes memory, CPU, and\n * network usage based on real-time conditions and predictive analytics.\n */\n\nimport { performanceMonitor } from '@/utils/performance';\nimport { adaptivePerformanceManager } from './AdaptivePerformanceManager';\nimport { behavioralAnalyticsEngine } from './BehavioralAnalyticsEngine';\n\ninterface ResourceMetrics {\n  memory: {\n    used: number;\n    available: number;\n    pressure: 'low' | 'medium' | 'high' | 'critical';\n    leaks: MemoryLeak[];\n    gcFrequency: number;\n  };\n  cpu: {\n    usage: number;\n    temperature: number;\n    throttling: boolean;\n    heavyTasks: CpuTask[];\n  };\n  network: {\n    bandwidth: number;\n    latency: number;\n    activeConnections: number;\n    queuedRequests: number;\n    failureRate: number;\n  };\n  battery: {\n    level: number;\n    drainRate: number;\n    isCharging: boolean;\n    estimatedTimeRemaining: number;\n  };\n  storage: {\n    used: number;\n    available: number;\n    ioOperations: number;\n    cacheSize: number;\n  };\n}\n\ninterface MemoryLeak {\n  component: string;\n  size: number;\n  duration: number;\n  severity: 'minor' | 'moderate' | 'severe';\n}\n\ninterface CpuTask {\n  taskId: string;\n  name: string;\n  cpuUsage: number;\n  duration: number;\n  priority: 'low' | 'medium' | 'high' | 'critical';\n}\n\ninterface ResourceOptimization {\n  type: 'memory' | 'cpu' | 'network' | 'battery' | 'storage';\n  action: string;\n  target: string;\n  expectedGain: number;\n  risk: 'low' | 'medium' | 'high';\n  implementation: () => Promise<void>;\n}\n\ninterface ResourcePrediction {\n  resource: 'memory' | 'cpu' | 'network' | 'battery';\n  timeHorizon: number; // minutes\n  predictedUsage: number;\n  confidence: number;\n  recommendations: string[];\n}\n\n/**\n * AI-Powered Smart Resource Manager\n */\nclass SmartResourceManager {\n  private currentMetrics: ResourceMetrics | null = null;\n  private resourceHistory: Array<{ timestamp: number; metrics: ResourceMetrics }> = [];\n  private activeOptimizations: Map<string, ResourceOptimization> = new Map();\n  private predictionModel: ResourcePredictionModel;\n  \n  private readonly MONITORING_INTERVAL = 5000; // 5 seconds\n  private readonly OPTIMIZATION_INTERVAL = 30000; // 30 seconds\n  private readonly HISTORY_LIMIT = 1000;\n  \n  private readonly THRESHOLDS = {\n    memory: {\n      warning: 0.8, // 80% usage\n      critical: 0.95, // 95% usage\n    },\n    cpu: {\n      warning: 0.7, // 70% usage\n      critical: 0.9, // 90% usage\n    },\n    battery: {\n      warning: 0.2, // 20% remaining\n      critical: 0.1, // 10% remaining\n    },\n    network: {\n      latencyWarning: 1000, // 1 second\n      failureRateWarning: 0.1, // 10% failure rate\n    },\n  };\n\n  constructor() {\n    this.predictionModel = new ResourcePredictionModel();\n    this.initializeResourceManager();\n  }\n\n  /**\n   * Initialize smart resource management\n   */\n  private async initializeResourceManager(): Promise<void> {\n    try {\n      // Start resource monitoring\n      this.startResourceMonitoring();\n      \n      // Start optimization engine\n      this.startOptimizationEngine();\n      \n      // Initialize prediction model\n      await this.predictionModel.initialize();\n      \n      console.log('Smart Resource Manager initialized successfully');\n    } catch (error) {\n      console.error('Failed to initialize Smart Resource Manager:', error);\n    }\n  }\n\n  /**\n   * Get current resource metrics\n   */\n  getCurrentMetrics(): ResourceMetrics | null {\n    return this.currentMetrics;\n  }\n\n  /**\n   * Get resource predictions\n   */\n  async getResourcePredictions(timeHorizon: number = 30): Promise<ResourcePrediction[]> {\n    if (!this.currentMetrics || this.resourceHistory.length < 10) {\n      return [];\n    }\n\n    try {\n      const predictions = await this.predictionModel.predict(\n        this.resourceHistory.slice(-20), // Last 20 data points\n        timeHorizon\n      );\n      \n      return predictions;\n    } catch (error) {\n      console.error('Failed to generate resource predictions:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Force resource optimization\n   */\n  async optimizeResources(): Promise<ResourceOptimization[]> {\n    if (!this.currentMetrics) {\n      return [];\n    }\n\n    const optimizations: ResourceOptimization[] = [];\n\n    try {\n      // Memory optimizations\n      const memoryOpts = await this.generateMemoryOptimizations();\n      optimizations.push(...memoryOpts);\n\n      // CPU optimizations\n      const cpuOpts = await this.generateCpuOptimizations();\n      optimizations.push(...cpuOpts);\n\n      // Network optimizations\n      const networkOpts = await this.generateNetworkOptimizations();\n      optimizations.push(...networkOpts);\n\n      // Battery optimizations\n      const batteryOpts = await this.generateBatteryOptimizations();\n      optimizations.push(...batteryOpts);\n\n      // Execute high-priority optimizations\n      const highPriorityOpts = optimizations.filter(opt => opt.risk === 'low' && opt.expectedGain > 0.2);\n      await this.executeOptimizations(highPriorityOpts);\n\n      return optimizations;\n    } catch (error) {\n      console.error('Failed to optimize resources:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Get resource optimization recommendations\n   */\n  getOptimizationRecommendations(): {\n    immediate: string[];\n    shortTerm: string[];\n    longTerm: string[];\n  } {\n    if (!this.currentMetrics) {\n      return { immediate: [], shortTerm: [], longTerm: [] };\n    }\n\n    const immediate: string[] = [];\n    const shortTerm: string[] = [];\n    const longTerm: string[] = [];\n\n    // Memory recommendations\n    if (this.currentMetrics.memory.pressure === 'critical') {\n      immediate.push('Clear unnecessary caches and free memory');\n    } else if (this.currentMetrics.memory.pressure === 'high') {\n      shortTerm.push('Optimize memory usage patterns');\n    }\n\n    // CPU recommendations\n    if (this.currentMetrics.cpu.usage > this.THRESHOLDS.cpu.critical) {\n      immediate.push('Reduce CPU-intensive operations');\n    } else if (this.currentMetrics.cpu.usage > this.THRESHOLDS.cpu.warning) {\n      shortTerm.push('Optimize computational algorithms');\n    }\n\n    // Battery recommendations\n    if (this.currentMetrics.battery.level < this.THRESHOLDS.battery.critical) {\n      immediate.push('Enable power saving mode');\n    } else if (this.currentMetrics.battery.level < this.THRESHOLDS.battery.warning) {\n      shortTerm.push('Reduce background processing');\n    }\n\n    // Network recommendations\n    if (this.currentMetrics.network.failureRate > this.THRESHOLDS.network.failureRateWarning) {\n      immediate.push('Implement request retry logic');\n      shortTerm.push('Optimize network request patterns');\n    }\n\n    // Long-term recommendations\n    longTerm.push('Implement predictive resource management');\n    longTerm.push('Optimize data structures for memory efficiency');\n    longTerm.push('Implement intelligent caching strategies');\n\n    return { immediate, shortTerm, longTerm };\n  }\n\n  /**\n   * Get resource efficiency score\n   */\n  getResourceEfficiencyScore(): {\n    overall: number;\n    memory: number;\n    cpu: number;\n    network: number;\n    battery: number;\n  } {\n    if (!this.currentMetrics) {\n      return { overall: 0, memory: 0, cpu: 0, network: 0, battery: 0 };\n    }\n\n    const memory = this.calculateMemoryEfficiency();\n    const cpu = this.calculateCpuEfficiency();\n    const network = this.calculateNetworkEfficiency();\n    const battery = this.calculateBatteryEfficiency();\n    \n    const overall = (memory + cpu + network + battery) / 4;\n\n    return { overall, memory, cpu, network, battery };\n  }\n\n  // Private methods\n\n  private startResourceMonitoring(): void {\n    setInterval(async () => {\n      await this.collectResourceMetrics();\n    }, this.MONITORING_INTERVAL);\n  }\n\n  private startOptimizationEngine(): void {\n    setInterval(async () => {\n      await this.performAutomaticOptimization();\n    }, this.OPTIMIZATION_INTERVAL);\n  }\n\n  private async collectResourceMetrics(): Promise<void> {\n    try {\n      const metrics: ResourceMetrics = {\n        memory: await this.collectMemoryMetrics(),\n        cpu: await this.collectCpuMetrics(),\n        network: await this.collectNetworkMetrics(),\n        battery: await this.collectBatteryMetrics(),\n        storage: await this.collectStorageMetrics(),\n      };\n\n      this.currentMetrics = metrics;\n      \n      // Add to history\n      this.resourceHistory.push({\n        timestamp: Date.now(),\n        metrics,\n      });\n\n      // Limit history size\n      if (this.resourceHistory.length > this.HISTORY_LIMIT) {\n        this.resourceHistory.shift();\n      }\n\n      // Check for critical conditions\n      await this.checkCriticalConditions(metrics);\n\n    } catch (error) {\n      console.error('Failed to collect resource metrics:', error);\n    }\n  }\n\n  private async collectMemoryMetrics(): Promise<ResourceMetrics['memory']> {\n    // In a real implementation, this would use native modules to get actual memory info\n    const used = Math.random() * 2048 + 512; // 512MB - 2.5GB\n    const available = 4096 - used; // Assume 4GB total\n    \n    return {\n      used,\n      available,\n      pressure: used / 4096 > 0.9 ? 'critical' : used / 4096 > 0.7 ? 'high' : used / 4096 > 0.5 ? 'medium' : 'low',\n      leaks: [], // Would detect actual memory leaks\n      gcFrequency: Math.random() * 10 + 5, // 5-15 GC cycles per minute\n    };\n  }\n\n  private async collectCpuMetrics(): Promise<ResourceMetrics['cpu']> {\n    return {\n      usage: Math.random() * 0.8 + 0.1, // 10-90% usage\n      temperature: Math.random() * 20 + 40, // 40-60°C\n      throttling: Math.random() > 0.9, // 10% chance of throttling\n      heavyTasks: [], // Would track actual heavy tasks\n    };\n  }\n\n  private async collectNetworkMetrics(): Promise<ResourceMetrics['network']> {\n    return {\n      bandwidth: Math.random() * 50 + 10, // 10-60 Mbps\n      latency: Math.random() * 200 + 50, // 50-250ms\n      activeConnections: Math.floor(Math.random() * 10 + 2), // 2-12 connections\n      queuedRequests: Math.floor(Math.random() * 5), // 0-5 queued\n      failureRate: Math.random() * 0.1, // 0-10% failure rate\n    };\n  }\n\n  private async collectBatteryMetrics(): Promise<ResourceMetrics['battery']> {\n    return {\n      level: Math.random() * 0.8 + 0.2, // 20-100%\n      drainRate: Math.random() * 5 + 1, // 1-6% per hour\n      isCharging: Math.random() > 0.7, // 30% chance of charging\n      estimatedTimeRemaining: Math.random() * 480 + 60, // 1-8 hours\n    };\n  }\n\n  private async collectStorageMetrics(): Promise<ResourceMetrics['storage']> {\n    return {\n      used: Math.random() * 16000 + 4000, // 4-20GB used\n      available: Math.random() * 32000 + 8000, // 8-40GB available\n      ioOperations: Math.floor(Math.random() * 100 + 10), // 10-110 ops/sec\n      cacheSize: Math.random() * 500 + 100, // 100-600MB cache\n    };\n  }\n\n  private async checkCriticalConditions(metrics: ResourceMetrics): Promise<void> {\n    const alerts: string[] = [];\n\n    // Memory critical\n    if (metrics.memory.pressure === 'critical') {\n      alerts.push('Critical memory pressure detected');\n      await this.handleCriticalMemory();\n    }\n\n    // CPU critical\n    if (metrics.cpu.usage > this.THRESHOLDS.cpu.critical) {\n      alerts.push('Critical CPU usage detected');\n      await this.handleCriticalCpu();\n    }\n\n    // Battery critical\n    if (metrics.battery.level < this.THRESHOLDS.battery.critical) {\n      alerts.push('Critical battery level detected');\n      await this.handleCriticalBattery();\n    }\n\n    if (alerts.length > 0) {\n      console.warn('Critical resource conditions:', alerts);\n    }\n  }\n\n  private async handleCriticalMemory(): Promise<void> {\n    // Implement emergency memory cleanup\n    console.log('Executing emergency memory cleanup');\n    \n    // Clear caches\n    // Reduce image quality\n    // Pause non-essential operations\n  }\n\n  private async handleCriticalCpu(): Promise<void> {\n    // Implement CPU load reduction\n    console.log('Reducing CPU load');\n    \n    // Pause heavy computations\n    // Reduce animation frame rate\n    // Defer non-critical tasks\n  }\n\n  private async handleCriticalBattery(): Promise<void> {\n    // Implement power saving measures\n    console.log('Activating power saving mode');\n    \n    // Reduce screen brightness\n    // Disable background sync\n    // Reduce network activity\n  }\n\n  private async performAutomaticOptimization(): Promise<void> {\n    if (!this.currentMetrics) return;\n\n    // Generate and execute safe optimizations\n    const optimizations = await this.optimizeResources();\n    const safeOptimizations = optimizations.filter(opt => opt.risk === 'low');\n    \n    if (safeOptimizations.length > 0) {\n      console.log(`Executing ${safeOptimizations.length} automatic optimizations`);\n    }\n  }\n\n  private async generateMemoryOptimizations(): Promise<ResourceOptimization[]> {\n    const optimizations: ResourceOptimization[] = [];\n    \n    if (this.currentMetrics!.memory.pressure === 'high') {\n      optimizations.push({\n        type: 'memory',\n        action: 'Clear unused caches',\n        target: 'cache_manager',\n        expectedGain: 0.2,\n        risk: 'low',\n        implementation: async () => {\n          // Clear caches\n          console.log('Clearing unused caches');\n        },\n      });\n    }\n\n    return optimizations;\n  }\n\n  private async generateCpuOptimizations(): Promise<ResourceOptimization[]> {\n    const optimizations: ResourceOptimization[] = [];\n    \n    if (this.currentMetrics!.cpu.usage > 0.7) {\n      optimizations.push({\n        type: 'cpu',\n        action: 'Reduce animation frame rate',\n        target: 'animation_engine',\n        expectedGain: 0.15,\n        risk: 'low',\n        implementation: async () => {\n          // Reduce frame rate\n          console.log('Reducing animation frame rate');\n        },\n      });\n    }\n\n    return optimizations;\n  }\n\n  private async generateNetworkOptimizations(): Promise<ResourceOptimization[]> {\n    const optimizations: ResourceOptimization[] = [];\n    \n    if (this.currentMetrics!.network.queuedRequests > 3) {\n      optimizations.push({\n        type: 'network',\n        action: 'Batch network requests',\n        target: 'network_manager',\n        expectedGain: 0.3,\n        risk: 'low',\n        implementation: async () => {\n          // Batch requests\n          console.log('Batching network requests');\n        },\n      });\n    }\n\n    return optimizations;\n  }\n\n  private async generateBatteryOptimizations(): Promise<ResourceOptimization[]> {\n    const optimizations: ResourceOptimization[] = [];\n    \n    if (this.currentMetrics!.battery.level < 0.3) {\n      optimizations.push({\n        type: 'battery',\n        action: 'Reduce background processing',\n        target: 'background_tasks',\n        expectedGain: 0.25,\n        risk: 'low',\n        implementation: async () => {\n          // Reduce background processing\n          console.log('Reducing background processing');\n        },\n      });\n    }\n\n    return optimizations;\n  }\n\n  private async executeOptimizations(optimizations: ResourceOptimization[]): Promise<void> {\n    for (const optimization of optimizations) {\n      try {\n        await optimization.implementation();\n        this.activeOptimizations.set(optimization.target, optimization);\n        \n        performanceMonitor.trackDatabaseQuery(\n          `resource_optimization_${optimization.type}`,\n          Date.now()\n        );\n      } catch (error) {\n        console.error(`Failed to execute optimization ${optimization.action}:`, error);\n      }\n    }\n  }\n\n  private calculateMemoryEfficiency(): number {\n    if (!this.currentMetrics) return 0;\n    \n    const usage = this.currentMetrics.memory.used / (this.currentMetrics.memory.used + this.currentMetrics.memory.available);\n    return Math.max(0, 100 - (usage * 100));\n  }\n\n  private calculateCpuEfficiency(): number {\n    if (!this.currentMetrics) return 0;\n    \n    return Math.max(0, 100 - (this.currentMetrics.cpu.usage * 100));\n  }\n\n  private calculateNetworkEfficiency(): number {\n    if (!this.currentMetrics) return 0;\n    \n    const latencyScore = Math.max(0, 100 - (this.currentMetrics.network.latency / 10));\n    const failureScore = Math.max(0, 100 - (this.currentMetrics.network.failureRate * 1000));\n    return (latencyScore + failureScore) / 2;\n  }\n\n  private calculateBatteryEfficiency(): number {\n    if (!this.currentMetrics) return 0;\n    \n    const levelScore = this.currentMetrics.battery.level * 100;\n    const drainScore = Math.max(0, 100 - (this.currentMetrics.battery.drainRate * 10));\n    return (levelScore + drainScore) / 2;\n  }\n}\n\n/**\n * Resource Prediction Model\n */\nclass ResourcePredictionModel {\n  async initialize(): Promise<void> {\n    console.log('Initialized resource prediction model');\n  }\n\n  async predict(\n    history: Array<{ timestamp: number; metrics: ResourceMetrics }>,\n    timeHorizon: number\n  ): Promise<ResourcePrediction[]> {\n    // Simple linear prediction (would use more sophisticated ML in real implementation)\n    const predictions: ResourcePrediction[] = [];\n    \n    if (history.length < 2) return predictions;\n\n    const latest = history[history.length - 1];\n    const previous = history[history.length - 2];\n    \n    // Memory prediction\n    const memoryTrend = (latest.metrics.memory.used - previous.metrics.memory.used) / \n                       (latest.timestamp - previous.timestamp);\n    const predictedMemory = latest.metrics.memory.used + (memoryTrend * timeHorizon * 60000);\n    \n    predictions.push({\n      resource: 'memory',\n      timeHorizon,\n      predictedUsage: predictedMemory,\n      confidence: 0.7,\n      recommendations: predictedMemory > 3000 ? ['Consider memory cleanup'] : [],\n    });\n\n    // CPU prediction\n    const cpuTrend = (latest.metrics.cpu.usage - previous.metrics.cpu.usage) / \n                    (latest.timestamp - previous.timestamp);\n    const predictedCpu = latest.metrics.cpu.usage + (cpuTrend * timeHorizon * 60000);\n    \n    predictions.push({\n      resource: 'cpu',\n      timeHorizon,\n      predictedUsage: predictedCpu,\n      confidence: 0.6,\n      recommendations: predictedCpu > 0.8 ? ['Reduce computational load'] : [],\n    });\n\n    return predictions;\n  }\n}\n\n// Export singleton instance\nexport const smartResourceManager = new SmartResourceManager();\nexport default smartResourceManager;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,SAASA,kBAAkB;AAA8B,IA0EnDC,oBAAoB;EA6BxB,SAAAA,qBAAA,EAAc;IAAAC,eAAA,OAAAD,oBAAA;IAAA,KA5BNE,cAAc,IAAAC,cAAA,GAAAC,CAAA,OAA2B,IAAI;IAAA,KAC7CC,eAAe,IAAAF,cAAA,GAAAC,CAAA,OAA2D,EAAE;IAAA,KAC5EE,mBAAmB,IAAAH,cAAA,GAAAC,CAAA,OAAsC,IAAIG,GAAG,CAAC,CAAC;IAAA,KAGzDC,mBAAmB,IAAAL,cAAA,GAAAC,CAAA,OAAG,IAAI;IAAA,KAC1BK,qBAAqB,IAAAN,cAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KAC7BM,aAAa,IAAAP,cAAA,GAAAC,CAAA,OAAG,IAAI;IAAA,KAEpBO,UAAU,IAAAR,cAAA,GAAAC,CAAA,OAAG;MAC5BQ,MAAM,EAAE;QACNC,OAAO,EAAE,GAAG;QACZC,QAAQ,EAAE;MACZ,CAAC;MACDC,GAAG,EAAE;QACHF,OAAO,EAAE,GAAG;QACZC,QAAQ,EAAE;MACZ,CAAC;MACDE,OAAO,EAAE;QACPH,OAAO,EAAE,GAAG;QACZC,QAAQ,EAAE;MACZ,CAAC;MACDG,OAAO,EAAE;QACPC,cAAc,EAAE,IAAI;QACpBC,kBAAkB,EAAE;MACtB;IACF,CAAC;IAAAhB,cAAA,GAAAiB,CAAA;IAAAjB,cAAA,GAAAC,CAAA;IAGC,IAAI,CAACiB,eAAe,GAAG,IAAIC,uBAAuB,CAAC,CAAC;IAACnB,cAAA,GAAAC,CAAA;IACrD,IAAI,CAACmB,yBAAyB,CAAC,CAAC;EAClC;EAAC,OAAAC,YAAA,CAAAxB,oBAAA;IAAAyB,GAAA;IAAAC,KAAA;MAAA,IAAAC,0BAAA,GAAAC,iBAAA,CAKD,aAAyD;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACvD,IAAI;UAAAD,cAAA,GAAAC,CAAA;UAEF,IAAI,CAACyB,uBAAuB,CAAC,CAAC;UAAC1B,cAAA,GAAAC,CAAA;UAG/B,IAAI,CAAC0B,uBAAuB,CAAC,CAAC;UAAC3B,cAAA,GAAAC,CAAA;UAG/B,MAAM,IAAI,CAACiB,eAAe,CAACU,UAAU,CAAC,CAAC;UAAC5B,cAAA,GAAAC,CAAA;UAExC4B,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;QAChE,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAA/B,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACE,KAAK,CAAC,8CAA8C,EAAEA,KAAK,CAAC;QACtE;MACF,CAAC;MAAA,SAfaX,yBAAyBA,CAAA;QAAA,OAAAI,0BAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAzBb,yBAAyB;IAAA;EAAA;IAAAE,GAAA;IAAAC,KAAA,EAoBvC,SAAAW,iBAAiBA,CAAA,EAA2B;MAAAlC,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MAC1C,OAAO,IAAI,CAACF,cAAc;IAC5B;EAAC;IAAAuB,GAAA;IAAAC,KAAA;MAAA,IAAAY,uBAAA,GAAAV,iBAAA,CAKD,aAAsF;QAAA,IAAzDW,WAAmB,GAAAH,SAAA,CAAAI,MAAA,QAAAJ,SAAA,QAAAK,SAAA,GAAAL,SAAA,OAAAjC,cAAA,GAAAuC,CAAA,UAAG,EAAE;QAAAvC,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACnD,IAAI,CAAAD,cAAA,GAAAuC,CAAA,WAAC,IAAI,CAACxC,cAAc,MAAAC,cAAA,GAAAuC,CAAA,UAAI,IAAI,CAACrC,eAAe,CAACmC,MAAM,GAAG,EAAE,GAAE;UAAArC,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UAC5D,OAAO,EAAE;QACX,CAAC;UAAAD,cAAA,GAAAuC,CAAA;QAAA;QAAAvC,cAAA,GAAAC,CAAA;QAED,IAAI;UACF,IAAMuC,WAAW,IAAAxC,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACiB,eAAe,CAACuB,OAAO,CACpD,IAAI,CAACvC,eAAe,CAACwC,KAAK,CAAC,CAAC,EAAE,CAAC,EAC/BN,WACF,CAAC;UAACpC,cAAA,GAAAC,CAAA;UAEF,OAAOuC,WAAW;QACpB,CAAC,CAAC,OAAOT,KAAK,EAAE;UAAA/B,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;UAAC/B,cAAA,GAAAC,CAAA;UACjE,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SAhBK0C,sBAAsBA,CAAA;QAAA,OAAAR,uBAAA,CAAAH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBU,sBAAsB;IAAA;EAAA;IAAArB,GAAA;IAAAC,KAAA;MAAA,IAAAqB,kBAAA,GAAAnB,iBAAA,CAqB5B,aAA2D;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACzD,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;UAAAC,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UACxB,OAAO,EAAE;QACX,CAAC;UAAAD,cAAA,GAAAuC,CAAA;QAAA;QAED,IAAMM,aAAqC,IAAA7C,cAAA,GAAAC,CAAA,QAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAEjD,IAAI;UAEF,IAAM6C,UAAU,IAAA9C,cAAA,GAAAC,CAAA,cAAS,IAAI,CAAC8C,2BAA2B,CAAC,CAAC;UAAC/C,cAAA,GAAAC,CAAA;UAC5D4C,aAAa,CAACG,IAAI,CAAAhB,KAAA,CAAlBa,aAAa,EAAAI,kBAAA,CAASH,UAAU,EAAC;UAGjC,IAAMI,OAAO,IAAAlD,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACkD,wBAAwB,CAAC,CAAC;UAACnD,cAAA,GAAAC,CAAA;UACtD4C,aAAa,CAACG,IAAI,CAAAhB,KAAA,CAAlBa,aAAa,EAAAI,kBAAA,CAASC,OAAO,EAAC;UAG9B,IAAME,WAAW,IAAApD,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACoD,4BAA4B,CAAC,CAAC;UAACrD,cAAA,GAAAC,CAAA;UAC9D4C,aAAa,CAACG,IAAI,CAAAhB,KAAA,CAAlBa,aAAa,EAAAI,kBAAA,CAASG,WAAW,EAAC;UAGlC,IAAME,WAAW,IAAAtD,cAAA,GAAAC,CAAA,cAAS,IAAI,CAACsD,4BAA4B,CAAC,CAAC;UAACvD,cAAA,GAAAC,CAAA;UAC9D4C,aAAa,CAACG,IAAI,CAAAhB,KAAA,CAAlBa,aAAa,EAAAI,kBAAA,CAASK,WAAW,EAAC;UAGlC,IAAME,gBAAgB,IAAAxD,cAAA,GAAAC,CAAA,QAAG4C,aAAa,CAACY,MAAM,CAAC,UAAAC,GAAG,EAAI;YAAA1D,cAAA,GAAAiB,CAAA;YAAAjB,cAAA,GAAAC,CAAA;YAAA,QAAAD,cAAA,GAAAuC,CAAA,UAAAmB,GAAG,CAACC,IAAI,KAAK,KAAK,MAAA3D,cAAA,GAAAuC,CAAA,UAAImB,GAAG,CAACE,YAAY,GAAG,GAAG;UAAD,CAAC,CAAC;UAAC5D,cAAA,GAAAC,CAAA;UACnG,MAAM,IAAI,CAAC4D,oBAAoB,CAACL,gBAAgB,CAAC;UAACxD,cAAA,GAAAC,CAAA;UAElD,OAAO4C,aAAa;QACtB,CAAC,CAAC,OAAOd,KAAK,EAAE;UAAA/B,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UAAC/B,cAAA,GAAAC,CAAA;UACtD,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SAjCK6D,iBAAiBA,CAAA;QAAA,OAAAlB,kBAAA,CAAAZ,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjB6B,iBAAiB;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA,EAsCvB,SAAAwC,8BAA8BA,CAAA,EAI5B;MAAA/D,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MACA,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;QAAAC,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QACxB,OAAO;UAAE+D,SAAS,EAAE,EAAE;UAAEC,SAAS,EAAE,EAAE;UAAEC,QAAQ,EAAE;QAAG,CAAC;MACvD,CAAC;QAAAlE,cAAA,GAAAuC,CAAA;MAAA;MAED,IAAMyB,SAAmB,IAAAhE,cAAA,GAAAC,CAAA,QAAG,EAAE;MAC9B,IAAMgE,SAAmB,IAAAjE,cAAA,GAAAC,CAAA,QAAG,EAAE;MAC9B,IAAMiE,QAAkB,IAAAlE,cAAA,GAAAC,CAAA,QAAG,EAAE;MAACD,cAAA,GAAAC,CAAA;MAG9B,IAAI,IAAI,CAACF,cAAc,CAACU,MAAM,CAAC0D,QAAQ,KAAK,UAAU,EAAE;QAAAnE,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QACtD+D,SAAS,CAAChB,IAAI,CAAC,0CAA0C,CAAC;MAC5D,CAAC,MAAM;QAAAhD,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QAAA,IAAI,IAAI,CAACF,cAAc,CAACU,MAAM,CAAC0D,QAAQ,KAAK,MAAM,EAAE;UAAAnE,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UACzDgE,SAAS,CAACjB,IAAI,CAAC,gCAAgC,CAAC;QAClD,CAAC;UAAAhD,cAAA,GAAAuC,CAAA;QAAA;MAAD;MAACvC,cAAA,GAAAC,CAAA;MAGD,IAAI,IAAI,CAACF,cAAc,CAACa,GAAG,CAACwD,KAAK,GAAG,IAAI,CAAC5D,UAAU,CAACI,GAAG,CAACD,QAAQ,EAAE;QAAAX,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QAChE+D,SAAS,CAAChB,IAAI,CAAC,iCAAiC,CAAC;MACnD,CAAC,MAAM;QAAAhD,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QAAA,IAAI,IAAI,CAACF,cAAc,CAACa,GAAG,CAACwD,KAAK,GAAG,IAAI,CAAC5D,UAAU,CAACI,GAAG,CAACF,OAAO,EAAE;UAAAV,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UACtEgE,SAAS,CAACjB,IAAI,CAAC,mCAAmC,CAAC;QACrD,CAAC;UAAAhD,cAAA,GAAAuC,CAAA;QAAA;MAAD;MAACvC,cAAA,GAAAC,CAAA;MAGD,IAAI,IAAI,CAACF,cAAc,CAACc,OAAO,CAACwD,KAAK,GAAG,IAAI,CAAC7D,UAAU,CAACK,OAAO,CAACF,QAAQ,EAAE;QAAAX,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QACxE+D,SAAS,CAAChB,IAAI,CAAC,0BAA0B,CAAC;MAC5C,CAAC,MAAM;QAAAhD,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QAAA,IAAI,IAAI,CAACF,cAAc,CAACc,OAAO,CAACwD,KAAK,GAAG,IAAI,CAAC7D,UAAU,CAACK,OAAO,CAACH,OAAO,EAAE;UAAAV,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UAC9EgE,SAAS,CAACjB,IAAI,CAAC,8BAA8B,CAAC;QAChD,CAAC;UAAAhD,cAAA,GAAAuC,CAAA;QAAA;MAAD;MAACvC,cAAA,GAAAC,CAAA;MAGD,IAAI,IAAI,CAACF,cAAc,CAACe,OAAO,CAACwD,WAAW,GAAG,IAAI,CAAC9D,UAAU,CAACM,OAAO,CAACE,kBAAkB,EAAE;QAAAhB,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QACxF+D,SAAS,CAAChB,IAAI,CAAC,+BAA+B,CAAC;QAAChD,cAAA,GAAAC,CAAA;QAChDgE,SAAS,CAACjB,IAAI,CAAC,mCAAmC,CAAC;MACrD,CAAC;QAAAhD,cAAA,GAAAuC,CAAA;MAAA;MAAAvC,cAAA,GAAAC,CAAA;MAGDiE,QAAQ,CAAClB,IAAI,CAAC,0CAA0C,CAAC;MAAChD,cAAA,GAAAC,CAAA;MAC1DiE,QAAQ,CAAClB,IAAI,CAAC,gDAAgD,CAAC;MAAChD,cAAA,GAAAC,CAAA;MAChEiE,QAAQ,CAAClB,IAAI,CAAC,0CAA0C,CAAC;MAAChD,cAAA,GAAAC,CAAA;MAE1D,OAAO;QAAE+D,SAAS,EAATA,SAAS;QAAEC,SAAS,EAATA,SAAS;QAAEC,QAAQ,EAARA;MAAS,CAAC;IAC3C;EAAC;IAAA5C,GAAA;IAAAC,KAAA,EAKD,SAAAgD,0BAA0BA,CAAA,EAMxB;MAAAvE,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MACA,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;QAAAC,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QACxB,OAAO;UAAEuE,OAAO,EAAE,CAAC;UAAE/D,MAAM,EAAE,CAAC;UAAEG,GAAG,EAAE,CAAC;UAAEE,OAAO,EAAE,CAAC;UAAED,OAAO,EAAE;QAAE,CAAC;MAClE,CAAC;QAAAb,cAAA,GAAAuC,CAAA;MAAA;MAED,IAAM9B,MAAM,IAAAT,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACwE,yBAAyB,CAAC,CAAC;MAC/C,IAAM7D,GAAG,IAAAZ,cAAA,GAAAC,CAAA,QAAG,IAAI,CAACyE,sBAAsB,CAAC,CAAC;MACzC,IAAM5D,OAAO,IAAAd,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC0E,0BAA0B,CAAC,CAAC;MACjD,IAAM9D,OAAO,IAAAb,cAAA,GAAAC,CAAA,QAAG,IAAI,CAAC2E,0BAA0B,CAAC,CAAC;MAEjD,IAAMJ,OAAO,IAAAxE,cAAA,GAAAC,CAAA,QAAG,CAACQ,MAAM,GAAGG,GAAG,GAAGE,OAAO,GAAGD,OAAO,IAAI,CAAC;MAACb,cAAA,GAAAC,CAAA;MAEvD,OAAO;QAAEuE,OAAO,EAAPA,OAAO;QAAE/D,MAAM,EAANA,MAAM;QAAEG,GAAG,EAAHA,GAAG;QAAEE,OAAO,EAAPA,OAAO;QAAED,OAAO,EAAPA;MAAQ,CAAC;IACnD;EAAC;IAAAS,GAAA;IAAAC,KAAA,EAID,SAAQG,uBAAuBA,CAAA,EAAS;MAAA,IAAAmD,KAAA;MAAA7E,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MACtC6E,WAAW,CAAArD,iBAAA,CAAC,aAAY;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACtB,MAAM4E,KAAI,CAACE,sBAAsB,CAAC,CAAC;MACrC,CAAC,GAAE,IAAI,CAAC1E,mBAAmB,CAAC;IAC9B;EAAC;IAAAiB,GAAA;IAAAC,KAAA,EAED,SAAQI,uBAAuBA,CAAA,EAAS;MAAA,IAAAqD,MAAA;MAAAhF,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MACtC6E,WAAW,CAAArD,iBAAA,CAAC,aAAY;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACtB,MAAM+E,MAAI,CAACC,4BAA4B,CAAC,CAAC;MAC3C,CAAC,GAAE,IAAI,CAAC3E,qBAAqB,CAAC;IAChC;EAAC;IAAAgB,GAAA;IAAAC,KAAA;MAAA,IAAA2D,uBAAA,GAAAzD,iBAAA,CAED,aAAsD;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACpD,IAAI;UACF,IAAMkF,OAAwB,IAAAnF,cAAA,GAAAC,CAAA,QAAG;YAC/BQ,MAAM,QAAQ,IAAI,CAAC2E,oBAAoB,CAAC,CAAC;YACzCxE,GAAG,QAAQ,IAAI,CAACyE,iBAAiB,CAAC,CAAC;YACnCvE,OAAO,QAAQ,IAAI,CAACwE,qBAAqB,CAAC,CAAC;YAC3CzE,OAAO,QAAQ,IAAI,CAAC0E,qBAAqB,CAAC,CAAC;YAC3CC,OAAO,QAAQ,IAAI,CAACC,qBAAqB,CAAC;UAC5C,CAAC;UAACzF,cAAA,GAAAC,CAAA;UAEF,IAAI,CAACF,cAAc,GAAGoF,OAAO;UAACnF,cAAA,GAAAC,CAAA;UAG9B,IAAI,CAACC,eAAe,CAAC8C,IAAI,CAAC;YACxB0C,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;YACrBT,OAAO,EAAPA;UACF,CAAC,CAAC;UAACnF,cAAA,GAAAC,CAAA;UAGH,IAAI,IAAI,CAACC,eAAe,CAACmC,MAAM,GAAG,IAAI,CAAC9B,aAAa,EAAE;YAAAP,cAAA,GAAAuC,CAAA;YAAAvC,cAAA,GAAAC,CAAA;YACpD,IAAI,CAACC,eAAe,CAAC2F,KAAK,CAAC,CAAC;UAC9B,CAAC;YAAA7F,cAAA,GAAAuC,CAAA;UAAA;UAAAvC,cAAA,GAAAC,CAAA;UAGD,MAAM,IAAI,CAAC6F,uBAAuB,CAACX,OAAO,CAAC;QAE7C,CAAC,CAAC,OAAOpD,KAAK,EAAE;UAAA/B,cAAA,GAAAC,CAAA;UACd4B,OAAO,CAACE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;QAC7D;MACF,CAAC;MAAA,SA7BagD,sBAAsBA,CAAA;QAAA,OAAAG,uBAAA,CAAAlD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtB8C,sBAAsB;IAAA;EAAA;IAAAzD,GAAA;IAAAC,KAAA;MAAA,IAAAwE,qBAAA,GAAAtE,iBAAA,CA+BpC,aAAyE;QAAAzB,cAAA,GAAAiB,CAAA;QAEvE,IAAM+E,IAAI,IAAAhG,cAAA,GAAAC,CAAA,QAAGgG,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG;QACvC,IAAMC,SAAS,IAAAnG,cAAA,GAAAC,CAAA,QAAG,IAAI,GAAG+F,IAAI;QAAChG,cAAA,GAAAC,CAAA;QAE9B,OAAO;UACL+F,IAAI,EAAJA,IAAI;UACJG,SAAS,EAATA,SAAS;UACThC,QAAQ,EAAE6B,IAAI,GAAG,IAAI,GAAG,GAAG,IAAAhG,cAAA,GAAAuC,CAAA,WAAG,UAAU,KAAAvC,cAAA,GAAAuC,CAAA,WAAGyD,IAAI,GAAG,IAAI,GAAG,GAAG,IAAAhG,cAAA,GAAAuC,CAAA,WAAG,MAAM,KAAAvC,cAAA,GAAAuC,CAAA,WAAGyD,IAAI,GAAG,IAAI,GAAG,GAAG,IAAAhG,cAAA,GAAAuC,CAAA,WAAG,QAAQ,KAAAvC,cAAA,GAAAuC,CAAA,WAAG,KAAK;UAC5G6D,KAAK,EAAE,EAAE;UACTC,WAAW,EAAEJ,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG;QACpC,CAAC;MACH,CAAC;MAAA,SAZad,oBAAoBA,CAAA;QAAA,OAAAW,qBAAA,CAAA/D,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBmD,oBAAoB;IAAA;EAAA;IAAA9D,GAAA;IAAAC,KAAA;MAAA,IAAA+E,kBAAA,GAAA7E,iBAAA,CAclC,aAAmE;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACjE,OAAO;UACLmE,KAAK,EAAE6B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCK,WAAW,EAAEN,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;UACpCM,UAAU,EAAEP,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC/BO,UAAU,EAAE;QACd,CAAC;MACH,CAAC;MAAA,SAPapB,iBAAiBA,CAAA;QAAA,OAAAiB,kBAAA,CAAAtE,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBoD,iBAAiB;IAAA;EAAA;IAAA/D,GAAA;IAAAC,KAAA;MAAA,IAAAmF,sBAAA,GAAAjF,iBAAA,CAS/B,aAA2E;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACzE,OAAO;UACL0G,SAAS,EAAEV,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;UAClCU,OAAO,EAAEX,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE;UACjCW,iBAAiB,EAAEZ,IAAI,CAACa,KAAK,CAACb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;UACrDa,cAAc,EAAEd,IAAI,CAACa,KAAK,CAACb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;UAC7C5B,WAAW,EAAE2B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QAC/B,CAAC;MACH,CAAC;MAAA,SARaZ,qBAAqBA,CAAA;QAAA,OAAAoB,sBAAA,CAAA1E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBqD,qBAAqB;IAAA;EAAA;IAAAhE,GAAA;IAAAC,KAAA;MAAA,IAAAyF,sBAAA,GAAAvF,iBAAA,CAUnC,aAA2E;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACzE,OAAO;UACLoE,KAAK,EAAE4B,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,GAAG;UAChCe,SAAS,EAAEhB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;UAChCgB,UAAU,EAAEjB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;UAC/BiB,sBAAsB,EAAElB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QAChD,CAAC;MACH,CAAC;MAAA,SAPaX,qBAAqBA,CAAA;QAAA,OAAAyB,sBAAA,CAAAhF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBsD,qBAAqB;IAAA;EAAA;IAAAjE,GAAA;IAAAC,KAAA;MAAA,IAAA6F,sBAAA,GAAA3F,iBAAA,CASnC,aAA2E;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACzE,OAAO;UACL+F,IAAI,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI;UAClCC,SAAS,EAAEF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI;UACvCmB,YAAY,EAAEpB,IAAI,CAACa,KAAK,CAACb,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC;UAClDoB,SAAS,EAAErB,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;QACnC,CAAC;MACH,CAAC;MAAA,SAPaT,qBAAqBA,CAAA;QAAA,OAAA2B,sBAAA,CAAApF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBwD,qBAAqB;IAAA;EAAA;IAAAnE,GAAA;IAAAC,KAAA;MAAA,IAAAgG,wBAAA,GAAA9F,iBAAA,CASnC,WAAsC0D,OAAwB,EAAiB;QAAAnF,cAAA,GAAAiB,CAAA;QAC7E,IAAMuG,MAAgB,IAAAxH,cAAA,GAAAC,CAAA,QAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAG5B,IAAIkF,OAAO,CAAC1E,MAAM,CAAC0D,QAAQ,KAAK,UAAU,EAAE;UAAAnE,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UAC1CuH,MAAM,CAACxE,IAAI,CAAC,mCAAmC,CAAC;UAAChD,cAAA,GAAAC,CAAA;UACjD,MAAM,IAAI,CAACwH,oBAAoB,CAAC,CAAC;QACnC,CAAC;UAAAzH,cAAA,GAAAuC,CAAA;QAAA;QAAAvC,cAAA,GAAAC,CAAA;QAGD,IAAIkF,OAAO,CAACvE,GAAG,CAACwD,KAAK,GAAG,IAAI,CAAC5D,UAAU,CAACI,GAAG,CAACD,QAAQ,EAAE;UAAAX,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UACpDuH,MAAM,CAACxE,IAAI,CAAC,6BAA6B,CAAC;UAAChD,cAAA,GAAAC,CAAA;UAC3C,MAAM,IAAI,CAACyH,iBAAiB,CAAC,CAAC;QAChC,CAAC;UAAA1H,cAAA,GAAAuC,CAAA;QAAA;QAAAvC,cAAA,GAAAC,CAAA;QAGD,IAAIkF,OAAO,CAACtE,OAAO,CAACwD,KAAK,GAAG,IAAI,CAAC7D,UAAU,CAACK,OAAO,CAACF,QAAQ,EAAE;UAAAX,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UAC5DuH,MAAM,CAACxE,IAAI,CAAC,iCAAiC,CAAC;UAAChD,cAAA,GAAAC,CAAA;UAC/C,MAAM,IAAI,CAAC0H,qBAAqB,CAAC,CAAC;QACpC,CAAC;UAAA3H,cAAA,GAAAuC,CAAA;QAAA;QAAAvC,cAAA,GAAAC,CAAA;QAED,IAAIuH,MAAM,CAACnF,MAAM,GAAG,CAAC,EAAE;UAAArC,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UACrB4B,OAAO,CAAC+F,IAAI,CAAC,+BAA+B,EAAEJ,MAAM,CAAC;QACvD,CAAC;UAAAxH,cAAA,GAAAuC,CAAA;QAAA;MACH,CAAC;MAAA,SAxBauD,uBAAuBA,CAAA+B,EAAA;QAAA,OAAAN,wBAAA,CAAAvF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAvB6D,uBAAuB;IAAA;EAAA;IAAAxE,GAAA;IAAAC,KAAA;MAAA,IAAAuG,qBAAA,GAAArG,iBAAA,CA0BrC,aAAoD;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QAElD4B,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MAKnD,CAAC;MAAA,SAPa2F,oBAAoBA,CAAA;QAAA,OAAAK,qBAAA,CAAA9F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApBwF,oBAAoB;IAAA;EAAA;IAAAnG,GAAA;IAAAC,KAAA;MAAA,IAAAwG,kBAAA,GAAAtG,iBAAA,CASlC,aAAiD;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QAE/C4B,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;MAKlC,CAAC;MAAA,SAPa4F,iBAAiBA,CAAA;QAAA,OAAAK,kBAAA,CAAA/F,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjByF,iBAAiB;IAAA;EAAA;IAAApG,GAAA;IAAAC,KAAA;MAAA,IAAAyG,sBAAA,GAAAvG,iBAAA,CAS/B,aAAqD;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QAEnD4B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAK7C,CAAC;MAAA,SAPa6F,qBAAqBA,CAAA;QAAA,OAAAK,sBAAA,CAAAhG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArB0F,qBAAqB;IAAA;EAAA;IAAArG,GAAA;IAAAC,KAAA;MAAA,IAAA0G,6BAAA,GAAAxG,iBAAA,CASnC,aAA4D;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QAC1D,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;UAAAC,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UAAA;QAAM,CAAC;UAAAD,cAAA,GAAAuC,CAAA;QAAA;QAGjC,IAAMM,aAAa,IAAA7C,cAAA,GAAAC,CAAA,eAAS,IAAI,CAAC6D,iBAAiB,CAAC,CAAC;QACpD,IAAMoE,iBAAiB,IAAAlI,cAAA,GAAAC,CAAA,SAAG4C,aAAa,CAACY,MAAM,CAAC,UAAAC,GAAG,EAAI;UAAA1D,cAAA,GAAAiB,CAAA;UAAAjB,cAAA,GAAAC,CAAA;UAAA,OAAAyD,GAAG,CAACC,IAAI,KAAK,KAAK;QAAD,CAAC,CAAC;QAAC3D,cAAA,GAAAC,CAAA;QAE1E,IAAIiI,iBAAiB,CAAC7F,MAAM,GAAG,CAAC,EAAE;UAAArC,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UAChC4B,OAAO,CAACC,GAAG,CAAC,aAAaoG,iBAAiB,CAAC7F,MAAM,0BAA0B,CAAC;QAC9E,CAAC;UAAArC,cAAA,GAAAuC,CAAA;QAAA;MACH,CAAC;MAAA,SAVa0C,4BAA4BA,CAAA;QAAA,OAAAgD,6BAAA,CAAAjG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA5BgD,4BAA4B;IAAA;EAAA;IAAA3D,GAAA;IAAAC,KAAA;MAAA,IAAA4G,4BAAA,GAAA1G,iBAAA,CAY1C,aAA6E;QAAAzB,cAAA,GAAAiB,CAAA;QAC3E,IAAM4B,aAAqC,IAAA7C,cAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAEjD,IAAI,IAAI,CAACF,cAAc,CAAEU,MAAM,CAAC0D,QAAQ,KAAK,MAAM,EAAE;UAAAnE,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UACnD4C,aAAa,CAACG,IAAI,CAAC;YACjBoF,IAAI,EAAE,QAAQ;YACdC,MAAM,EAAE,qBAAqB;YAC7BC,MAAM,EAAE,eAAe;YACvB1E,YAAY,EAAE,GAAG;YACjBD,IAAI,EAAE,KAAK;YACX4E,cAAc;cAAA,IAAAC,eAAA,GAAA/G,iBAAA,CAAE,aAAY;gBAAAzB,cAAA,GAAAiB,CAAA;gBAAAjB,cAAA,GAAAC,CAAA;gBAE1B4B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;cACvC,CAAC;cAAA,SAHDyG,cAAcA,CAAA;gBAAA,OAAAC,eAAA,CAAAxG,KAAA,OAAAC,SAAA;cAAA;cAAA,OAAdsG,cAAc;YAAA;UAIhB,CAAC,CAAC;QACJ,CAAC;UAAAvI,cAAA,GAAAuC,CAAA;QAAA;QAAAvC,cAAA,GAAAC,CAAA;QAED,OAAO4C,aAAa;MACtB,CAAC;MAAA,SAlBaE,2BAA2BA,CAAA;QAAA,OAAAoF,4BAAA,CAAAnG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA3Bc,2BAA2B;IAAA;EAAA;IAAAzB,GAAA;IAAAC,KAAA;MAAA,IAAAkH,yBAAA,GAAAhH,iBAAA,CAoBzC,aAA0E;QAAAzB,cAAA,GAAAiB,CAAA;QACxE,IAAM4B,aAAqC,IAAA7C,cAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAEjD,IAAI,IAAI,CAACF,cAAc,CAAEa,GAAG,CAACwD,KAAK,GAAG,GAAG,EAAE;UAAApE,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UACxC4C,aAAa,CAACG,IAAI,CAAC;YACjBoF,IAAI,EAAE,KAAK;YACXC,MAAM,EAAE,6BAA6B;YACrCC,MAAM,EAAE,kBAAkB;YAC1B1E,YAAY,EAAE,IAAI;YAClBD,IAAI,EAAE,KAAK;YACX4E,cAAc;cAAA,IAAAG,gBAAA,GAAAjH,iBAAA,CAAE,aAAY;gBAAAzB,cAAA,GAAAiB,CAAA;gBAAAjB,cAAA,GAAAC,CAAA;gBAE1B4B,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;cAC9C,CAAC;cAAA,SAHDyG,cAAcA,CAAA;gBAAA,OAAAG,gBAAA,CAAA1G,KAAA,OAAAC,SAAA;cAAA;cAAA,OAAdsG,cAAc;YAAA;UAIhB,CAAC,CAAC;QACJ,CAAC;UAAAvI,cAAA,GAAAuC,CAAA;QAAA;QAAAvC,cAAA,GAAAC,CAAA;QAED,OAAO4C,aAAa;MACtB,CAAC;MAAA,SAlBaM,wBAAwBA,CAAA;QAAA,OAAAsF,yBAAA,CAAAzG,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAxBkB,wBAAwB;IAAA;EAAA;IAAA7B,GAAA;IAAAC,KAAA;MAAA,IAAAoH,6BAAA,GAAAlH,iBAAA,CAoBtC,aAA8E;QAAAzB,cAAA,GAAAiB,CAAA;QAC5E,IAAM4B,aAAqC,IAAA7C,cAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAEjD,IAAI,IAAI,CAACF,cAAc,CAAEe,OAAO,CAACiG,cAAc,GAAG,CAAC,EAAE;UAAA/G,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UACnD4C,aAAa,CAACG,IAAI,CAAC;YACjBoF,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,wBAAwB;YAChCC,MAAM,EAAE,iBAAiB;YACzB1E,YAAY,EAAE,GAAG;YACjBD,IAAI,EAAE,KAAK;YACX4E,cAAc;cAAA,IAAAK,gBAAA,GAAAnH,iBAAA,CAAE,aAAY;gBAAAzB,cAAA,GAAAiB,CAAA;gBAAAjB,cAAA,GAAAC,CAAA;gBAE1B4B,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;cAC1C,CAAC;cAAA,SAHDyG,cAAcA,CAAA;gBAAA,OAAAK,gBAAA,CAAA5G,KAAA,OAAAC,SAAA;cAAA;cAAA,OAAdsG,cAAc;YAAA;UAIhB,CAAC,CAAC;QACJ,CAAC;UAAAvI,cAAA,GAAAuC,CAAA;QAAA;QAAAvC,cAAA,GAAAC,CAAA;QAED,OAAO4C,aAAa;MACtB,CAAC;MAAA,SAlBaQ,4BAA4BA,CAAA;QAAA,OAAAsF,6BAAA,CAAA3G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA5BoB,4BAA4B;IAAA;EAAA;IAAA/B,GAAA;IAAAC,KAAA;MAAA,IAAAsH,6BAAA,GAAApH,iBAAA,CAoB1C,aAA8E;QAAAzB,cAAA,GAAAiB,CAAA;QAC5E,IAAM4B,aAAqC,IAAA7C,cAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAEjD,IAAI,IAAI,CAACF,cAAc,CAAEc,OAAO,CAACwD,KAAK,GAAG,GAAG,EAAE;UAAArE,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UAC5C4C,aAAa,CAACG,IAAI,CAAC;YACjBoF,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,8BAA8B;YACtCC,MAAM,EAAE,kBAAkB;YAC1B1E,YAAY,EAAE,IAAI;YAClBD,IAAI,EAAE,KAAK;YACX4E,cAAc;cAAA,IAAAO,gBAAA,GAAArH,iBAAA,CAAE,aAAY;gBAAAzB,cAAA,GAAAiB,CAAA;gBAAAjB,cAAA,GAAAC,CAAA;gBAE1B4B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;cAC/C,CAAC;cAAA,SAHDyG,cAAcA,CAAA;gBAAA,OAAAO,gBAAA,CAAA9G,KAAA,OAAAC,SAAA;cAAA;cAAA,OAAdsG,cAAc;YAAA;UAIhB,CAAC,CAAC;QACJ,CAAC;UAAAvI,cAAA,GAAAuC,CAAA;QAAA;QAAAvC,cAAA,GAAAC,CAAA;QAED,OAAO4C,aAAa;MACtB,CAAC;MAAA,SAlBaU,4BAA4BA,CAAA;QAAA,OAAAsF,6BAAA,CAAA7G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAA5BsB,4BAA4B;IAAA;EAAA;IAAAjC,GAAA;IAAAC,KAAA;MAAA,IAAAwH,qBAAA,GAAAtH,iBAAA,CAoB1C,WAAmCoB,aAAqC,EAAiB;QAAA7C,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QACvF,KAAK,IAAM+I,YAAY,IAAInG,aAAa,EAAE;UAAA7C,cAAA,GAAAC,CAAA;UACxC,IAAI;YAAAD,cAAA,GAAAC,CAAA;YACF,MAAM+I,YAAY,CAACT,cAAc,CAAC,CAAC;YAACvI,cAAA,GAAAC,CAAA;YACpC,IAAI,CAACE,mBAAmB,CAAC8I,GAAG,CAACD,YAAY,CAACV,MAAM,EAAEU,YAAY,CAAC;YAAChJ,cAAA,GAAAC,CAAA;YAEhEL,kBAAkB,CAACsJ,kBAAkB,CACnC,yBAAyBF,YAAY,CAACZ,IAAI,EAAE,EAC5CzC,IAAI,CAACC,GAAG,CAAC,CACX,CAAC;UACH,CAAC,CAAC,OAAO7D,KAAK,EAAE;YAAA/B,cAAA,GAAAC,CAAA;YACd4B,OAAO,CAACE,KAAK,CAAC,kCAAkCiH,YAAY,CAACX,MAAM,GAAG,EAAEtG,KAAK,CAAC;UAChF;QACF;MACF,CAAC;MAAA,SAda8B,oBAAoBA,CAAAsF,GAAA;QAAA,OAAAJ,qBAAA,CAAA/G,KAAA,OAAAC,SAAA;MAAA;MAAA,OAApB4B,oBAAoB;IAAA;EAAA;IAAAvC,GAAA;IAAAC,KAAA,EAgBlC,SAAQkD,yBAAyBA,CAAA,EAAW;MAAAzE,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MAC1C,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;QAAAC,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QAAA,OAAO,CAAC;MAAA,CAAC;QAAAD,cAAA,GAAAuC,CAAA;MAAA;MAEnC,IAAM6B,KAAK,IAAApE,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACF,cAAc,CAACU,MAAM,CAACuF,IAAI,IAAI,IAAI,CAACjG,cAAc,CAACU,MAAM,CAACuF,IAAI,GAAG,IAAI,CAACjG,cAAc,CAACU,MAAM,CAAC0F,SAAS,CAAC;MAACnG,cAAA,GAAAC,CAAA;MACzH,OAAOgG,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAE,GAAG,GAAIhF,KAAK,GAAG,GAAI,CAAC;IACzC;EAAC;IAAA9C,GAAA;IAAAC,KAAA,EAED,SAAQmD,sBAAsBA,CAAA,EAAW;MAAA1E,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MACvC,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;QAAAC,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QAAA,OAAO,CAAC;MAAA,CAAC;QAAAD,cAAA,GAAAuC,CAAA;MAAA;MAAAvC,cAAA,GAAAC,CAAA;MAEnC,OAAOgG,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAE,GAAG,GAAI,IAAI,CAACrJ,cAAc,CAACa,GAAG,CAACwD,KAAK,GAAG,GAAI,CAAC;IACjE;EAAC;IAAA9C,GAAA;IAAAC,KAAA,EAED,SAAQoD,0BAA0BA,CAAA,EAAW;MAAA3E,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MAC3C,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;QAAAC,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QAAA,OAAO,CAAC;MAAA,CAAC;QAAAD,cAAA,GAAAuC,CAAA;MAAA;MAEnC,IAAM8G,YAAY,IAAArJ,cAAA,GAAAC,CAAA,SAAGgG,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAE,GAAG,GAAI,IAAI,CAACrJ,cAAc,CAACe,OAAO,CAAC8F,OAAO,GAAG,EAAG,CAAC;MAClF,IAAM0C,YAAY,IAAAtJ,cAAA,GAAAC,CAAA,SAAGgG,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAE,GAAG,GAAI,IAAI,CAACrJ,cAAc,CAACe,OAAO,CAACwD,WAAW,GAAG,IAAK,CAAC;MAACtE,cAAA,GAAAC,CAAA;MACzF,OAAO,CAACoJ,YAAY,GAAGC,YAAY,IAAI,CAAC;IAC1C;EAAC;IAAAhI,GAAA;IAAAC,KAAA,EAED,SAAQqD,0BAA0BA,CAAA,EAAW;MAAA5E,cAAA,GAAAiB,CAAA;MAAAjB,cAAA,GAAAC,CAAA;MAC3C,IAAI,CAAC,IAAI,CAACF,cAAc,EAAE;QAAAC,cAAA,GAAAuC,CAAA;QAAAvC,cAAA,GAAAC,CAAA;QAAA,OAAO,CAAC;MAAA,CAAC;QAAAD,cAAA,GAAAuC,CAAA;MAAA;MAEnC,IAAMgH,UAAU,IAAAvJ,cAAA,GAAAC,CAAA,SAAG,IAAI,CAACF,cAAc,CAACc,OAAO,CAACwD,KAAK,GAAG,GAAG;MAC1D,IAAMmF,UAAU,IAAAxJ,cAAA,GAAAC,CAAA,SAAGgG,IAAI,CAACmD,GAAG,CAAC,CAAC,EAAE,GAAG,GAAI,IAAI,CAACrJ,cAAc,CAACc,OAAO,CAACoG,SAAS,GAAG,EAAG,CAAC;MAACjH,cAAA,GAAAC,CAAA;MACnF,OAAO,CAACsJ,UAAU,GAAGC,UAAU,IAAI,CAAC;IACtC;EAAC;AAAA;AAAA,IAMGrI,uBAAuB;EAAA,SAAAA,wBAAA;IAAArB,eAAA,OAAAqB,uBAAA;EAAA;EAAA,OAAAE,YAAA,CAAAF,uBAAA;IAAAG,GAAA;IAAAC,KAAA;MAAA,IAAAkI,WAAA,GAAAhI,iBAAA,CAC3B,aAAkC;QAAAzB,cAAA,GAAAiB,CAAA;QAAAjB,cAAA,GAAAC,CAAA;QAChC4B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACtD,CAAC;MAAA,SAFKF,UAAUA,CAAA;QAAA,OAAA6H,WAAA,CAAAzH,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVL,UAAU;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA;MAAA,IAAAmI,QAAA,GAAAjI,iBAAA,CAIhB,WACEkI,OAA+D,EAC/DvH,WAAmB,EACY;QAAApC,cAAA,GAAAiB,CAAA;QAE/B,IAAMuB,WAAiC,IAAAxC,cAAA,GAAAC,CAAA,SAAG,EAAE;QAACD,cAAA,GAAAC,CAAA;QAE7C,IAAI0J,OAAO,CAACtH,MAAM,GAAG,CAAC,EAAE;UAAArC,cAAA,GAAAuC,CAAA;UAAAvC,cAAA,GAAAC,CAAA;UAAA,OAAOuC,WAAW;QAAA,CAAC;UAAAxC,cAAA,GAAAuC,CAAA;QAAA;QAE3C,IAAMqH,MAAM,IAAA5J,cAAA,GAAAC,CAAA,SAAG0J,OAAO,CAACA,OAAO,CAACtH,MAAM,GAAG,CAAC,CAAC;QAC1C,IAAMwH,QAAQ,IAAA7J,cAAA,GAAAC,CAAA,SAAG0J,OAAO,CAACA,OAAO,CAACtH,MAAM,GAAG,CAAC,CAAC;QAG5C,IAAMyH,WAAW,IAAA9J,cAAA,GAAAC,CAAA,SAAG,CAAC2J,MAAM,CAACzE,OAAO,CAAC1E,MAAM,CAACuF,IAAI,GAAG6D,QAAQ,CAAC1E,OAAO,CAAC1E,MAAM,CAACuF,IAAI,KAC1D4D,MAAM,CAAClE,SAAS,GAAGmE,QAAQ,CAACnE,SAAS,CAAC;QAC1D,IAAMqE,eAAe,IAAA/J,cAAA,GAAAC,CAAA,SAAG2J,MAAM,CAACzE,OAAO,CAAC1E,MAAM,CAACuF,IAAI,GAAI8D,WAAW,GAAG1H,WAAW,GAAG,KAAM;QAACpC,cAAA,GAAAC,CAAA;QAEzFuC,WAAW,CAACQ,IAAI,CAAC;UACfgH,QAAQ,EAAE,QAAQ;UAClB5H,WAAW,EAAXA,WAAW;UACX6H,cAAc,EAAEF,eAAe;UAC/BG,UAAU,EAAE,GAAG;UACfC,eAAe,EAAEJ,eAAe,GAAG,IAAI,IAAA/J,cAAA,GAAAuC,CAAA,WAAG,CAAC,yBAAyB,CAAC,KAAAvC,cAAA,GAAAuC,CAAA,WAAG,EAAE;QAC5E,CAAC,CAAC;QAGF,IAAM6H,QAAQ,IAAApK,cAAA,GAAAC,CAAA,SAAG,CAAC2J,MAAM,CAACzE,OAAO,CAACvE,GAAG,CAACwD,KAAK,GAAGyF,QAAQ,CAAC1E,OAAO,CAACvE,GAAG,CAACwD,KAAK,KACtDwF,MAAM,CAAClE,SAAS,GAAGmE,QAAQ,CAACnE,SAAS,CAAC;QACvD,IAAM2E,YAAY,IAAArK,cAAA,GAAAC,CAAA,SAAG2J,MAAM,CAACzE,OAAO,CAACvE,GAAG,CAACwD,KAAK,GAAIgG,QAAQ,GAAGhI,WAAW,GAAG,KAAM;QAACpC,cAAA,GAAAC,CAAA;QAEjFuC,WAAW,CAACQ,IAAI,CAAC;UACfgH,QAAQ,EAAE,KAAK;UACf5H,WAAW,EAAXA,WAAW;UACX6H,cAAc,EAAEI,YAAY;UAC5BH,UAAU,EAAE,GAAG;UACfC,eAAe,EAAEE,YAAY,GAAG,GAAG,IAAArK,cAAA,GAAAuC,CAAA,WAAG,CAAC,2BAA2B,CAAC,KAAAvC,cAAA,GAAAuC,CAAA,WAAG,EAAE;QAC1E,CAAC,CAAC;QAACvC,cAAA,GAAAC,CAAA;QAEH,OAAOuC,WAAW;MACpB,CAAC;MAAA,SAvCKC,OAAOA,CAAA6H,GAAA,EAAAC,GAAA;QAAA,OAAAb,QAAA,CAAA1H,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAPQ,OAAO;IAAA;EAAA;AAAA;AA2Cf,OAAO,IAAM+H,oBAAoB,IAAAxK,cAAA,GAAAC,CAAA,SAAG,IAAIJ,oBAAoB,CAAC,CAAC;AAC9D,eAAe2K,oBAAoB", "ignoreList": []}