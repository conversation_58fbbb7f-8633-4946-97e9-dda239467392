05acc6bcc3f82d435717b0767a687da5
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1zsc58rp56() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\ai\\SmartResourceManager.ts";
  var hash = "153524b3fc8f4399dcad268ec94b258fb29dd16e";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\ai\\SmartResourceManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 83,
          column: 51
        },
        end: {
          line: 83,
          column: 55
        }
      },
      "1": {
        start: {
          line: 84,
          column: 84
        },
        end: {
          line: 84,
          column: 86
        }
      },
      "2": {
        start: {
          line: 85,
          column: 67
        },
        end: {
          line: 85,
          column: 76
        }
      },
      "3": {
        start: {
          line: 88,
          column: 41
        },
        end: {
          line: 88,
          column: 45
        }
      },
      "4": {
        start: {
          line: 89,
          column: 43
        },
        end: {
          line: 89,
          column: 48
        }
      },
      "5": {
        start: {
          line: 90,
          column: 35
        },
        end: {
          line: 90,
          column: 39
        }
      },
      "6": {
        start: {
          line: 92,
          column: 32
        },
        end: {
          line: 109,
          column: 3
        }
      },
      "7": {
        start: {
          line: 112,
          column: 4
        },
        end: {
          line: 112,
          column: 57
        }
      },
      "8": {
        start: {
          line: 113,
          column: 4
        },
        end: {
          line: 113,
          column: 37
        }
      },
      "9": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 133,
          column: 5
        }
      },
      "10": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 122,
          column: 37
        }
      },
      "11": {
        start: {
          line: 125,
          column: 6
        },
        end: {
          line: 125,
          column: 37
        }
      },
      "12": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 46
        }
      },
      "13": {
        start: {
          line: 130,
          column: 6
        },
        end: {
          line: 130,
          column: 69
        }
      },
      "14": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 132,
          column: 75
        }
      },
      "15": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 140,
          column: 31
        }
      },
      "16": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 149,
          column: 5
        }
      },
      "17": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 16
        }
      },
      "18": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 161,
          column: 5
        }
      },
      "19": {
        start: {
          line: 152,
          column: 26
        },
        end: {
          line: 155,
          column: 7
        }
      },
      "20": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 25
        }
      },
      "21": {
        start: {
          line: 159,
          column: 6
        },
        end: {
          line: 159,
          column: 71
        }
      },
      "22": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 16
        }
      },
      "23": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 170,
          column: 5
        }
      },
      "24": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 169,
          column: 16
        }
      },
      "25": {
        start: {
          line: 172,
          column: 50
        },
        end: {
          line: 172,
          column: 52
        }
      },
      "26": {
        start: {
          line: 174,
          column: 4
        },
        end: {
          line: 199,
          column: 5
        }
      },
      "27": {
        start: {
          line: 176,
          column: 25
        },
        end: {
          line: 176,
          column: 65
        }
      },
      "28": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 40
        }
      },
      "29": {
        start: {
          line: 180,
          column: 22
        },
        end: {
          line: 180,
          column: 59
        }
      },
      "30": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 37
        }
      },
      "31": {
        start: {
          line: 184,
          column: 26
        },
        end: {
          line: 184,
          column: 67
        }
      },
      "32": {
        start: {
          line: 185,
          column: 6
        },
        end: {
          line: 185,
          column: 41
        }
      },
      "33": {
        start: {
          line: 188,
          column: 26
        },
        end: {
          line: 188,
          column: 67
        }
      },
      "34": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 41
        }
      },
      "35": {
        start: {
          line: 192,
          column: 31
        },
        end: {
          line: 192,
          column: 104
        }
      },
      "36": {
        start: {
          line: 192,
          column: 59
        },
        end: {
          line: 192,
          column: 103
        }
      },
      "37": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 193,
          column: 56
        }
      },
      "38": {
        start: {
          line: 195,
          column: 6
        },
        end: {
          line: 195,
          column: 27
        }
      },
      "39": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 60
        }
      },
      "40": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 198,
          column: 16
        }
      },
      "41": {
        start: {
          line: 210,
          column: 4
        },
        end: {
          line: 212,
          column: 5
        }
      },
      "42": {
        start: {
          line: 211,
          column: 6
        },
        end: {
          line: 211,
          column: 60
        }
      },
      "43": {
        start: {
          line: 214,
          column: 32
        },
        end: {
          line: 214,
          column: 34
        }
      },
      "44": {
        start: {
          line: 215,
          column: 32
        },
        end: {
          line: 215,
          column: 34
        }
      },
      "45": {
        start: {
          line: 216,
          column: 31
        },
        end: {
          line: 216,
          column: 33
        }
      },
      "46": {
        start: {
          line: 219,
          column: 4
        },
        end: {
          line: 223,
          column: 5
        }
      },
      "47": {
        start: {
          line: 220,
          column: 6
        },
        end: {
          line: 220,
          column: 65
        }
      },
      "48": {
        start: {
          line: 221,
          column: 11
        },
        end: {
          line: 223,
          column: 5
        }
      },
      "49": {
        start: {
          line: 222,
          column: 6
        },
        end: {
          line: 222,
          column: 55
        }
      },
      "50": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 230,
          column: 5
        }
      },
      "51": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 56
        }
      },
      "52": {
        start: {
          line: 228,
          column: 11
        },
        end: {
          line: 230,
          column: 5
        }
      },
      "53": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 229,
          column: 58
        }
      },
      "54": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 237,
          column: 5
        }
      },
      "55": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 49
        }
      },
      "56": {
        start: {
          line: 235,
          column: 11
        },
        end: {
          line: 237,
          column: 5
        }
      },
      "57": {
        start: {
          line: 236,
          column: 6
        },
        end: {
          line: 236,
          column: 53
        }
      },
      "58": {
        start: {
          line: 240,
          column: 4
        },
        end: {
          line: 243,
          column: 5
        }
      },
      "59": {
        start: {
          line: 241,
          column: 6
        },
        end: {
          line: 241,
          column: 54
        }
      },
      "60": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 58
        }
      },
      "61": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 246,
          column: 62
        }
      },
      "62": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 247,
          column: 68
        }
      },
      "63": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 248,
          column: 62
        }
      },
      "64": {
        start: {
          line: 250,
          column: 4
        },
        end: {
          line: 250,
          column: 46
        }
      },
      "65": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 265,
          column: 5
        }
      },
      "66": {
        start: {
          line: 264,
          column: 6
        },
        end: {
          line: 264,
          column: 71
        }
      },
      "67": {
        start: {
          line: 267,
          column: 19
        },
        end: {
          line: 267,
          column: 51
        }
      },
      "68": {
        start: {
          line: 268,
          column: 16
        },
        end: {
          line: 268,
          column: 45
        }
      },
      "69": {
        start: {
          line: 269,
          column: 20
        },
        end: {
          line: 269,
          column: 53
        }
      },
      "70": {
        start: {
          line: 270,
          column: 20
        },
        end: {
          line: 270,
          column: 53
        }
      },
      "71": {
        start: {
          line: 272,
          column: 20
        },
        end: {
          line: 272,
          column: 58
        }
      },
      "72": {
        start: {
          line: 274,
          column: 4
        },
        end: {
          line: 274,
          column: 54
        }
      },
      "73": {
        start: {
          line: 280,
          column: 4
        },
        end: {
          line: 282,
          column: 33
        }
      },
      "74": {
        start: {
          line: 281,
          column: 6
        },
        end: {
          line: 281,
          column: 42
        }
      },
      "75": {
        start: {
          line: 286,
          column: 4
        },
        end: {
          line: 288,
          column: 35
        }
      },
      "76": {
        start: {
          line: 287,
          column: 6
        },
        end: {
          line: 287,
          column: 48
        }
      },
      "77": {
        start: {
          line: 292,
          column: 4
        },
        end: {
          line: 319,
          column: 5
        }
      },
      "78": {
        start: {
          line: 293,
          column: 39
        },
        end: {
          line: 299,
          column: 7
        }
      },
      "79": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 36
        }
      },
      "80": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 307,
          column: 9
        }
      },
      "81": {
        start: {
          line: 310,
          column: 6
        },
        end: {
          line: 312,
          column: 7
        }
      },
      "82": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 311,
          column: 37
        }
      },
      "83": {
        start: {
          line: 315,
          column: 6
        },
        end: {
          line: 315,
          column: 50
        }
      },
      "84": {
        start: {
          line: 318,
          column: 6
        },
        end: {
          line: 318,
          column: 66
        }
      },
      "85": {
        start: {
          line: 324,
          column: 17
        },
        end: {
          line: 324,
          column: 43
        }
      },
      "86": {
        start: {
          line: 325,
          column: 22
        },
        end: {
          line: 325,
          column: 33
        }
      },
      "87": {
        start: {
          line: 327,
          column: 4
        },
        end: {
          line: 333,
          column: 6
        }
      },
      "88": {
        start: {
          line: 337,
          column: 4
        },
        end: {
          line: 342,
          column: 6
        }
      },
      "89": {
        start: {
          line: 346,
          column: 4
        },
        end: {
          line: 352,
          column: 6
        }
      },
      "90": {
        start: {
          line: 356,
          column: 4
        },
        end: {
          line: 361,
          column: 6
        }
      },
      "91": {
        start: {
          line: 365,
          column: 4
        },
        end: {
          line: 370,
          column: 6
        }
      },
      "92": {
        start: {
          line: 374,
          column: 29
        },
        end: {
          line: 374,
          column: 31
        }
      },
      "93": {
        start: {
          line: 377,
          column: 4
        },
        end: {
          line: 380,
          column: 5
        }
      },
      "94": {
        start: {
          line: 378,
          column: 6
        },
        end: {
          line: 378,
          column: 55
        }
      },
      "95": {
        start: {
          line: 379,
          column: 6
        },
        end: {
          line: 379,
          column: 40
        }
      },
      "96": {
        start: {
          line: 383,
          column: 4
        },
        end: {
          line: 386,
          column: 5
        }
      },
      "97": {
        start: {
          line: 384,
          column: 6
        },
        end: {
          line: 384,
          column: 49
        }
      },
      "98": {
        start: {
          line: 385,
          column: 6
        },
        end: {
          line: 385,
          column: 37
        }
      },
      "99": {
        start: {
          line: 389,
          column: 4
        },
        end: {
          line: 392,
          column: 5
        }
      },
      "100": {
        start: {
          line: 390,
          column: 6
        },
        end: {
          line: 390,
          column: 53
        }
      },
      "101": {
        start: {
          line: 391,
          column: 6
        },
        end: {
          line: 391,
          column: 41
        }
      },
      "102": {
        start: {
          line: 394,
          column: 4
        },
        end: {
          line: 396,
          column: 5
        }
      },
      "103": {
        start: {
          line: 395,
          column: 6
        },
        end: {
          line: 395,
          column: 60
        }
      },
      "104": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 401,
          column: 54
        }
      },
      "105": {
        start: {
          line: 410,
          column: 4
        },
        end: {
          line: 410,
          column: 37
        }
      },
      "106": {
        start: {
          line: 419,
          column: 4
        },
        end: {
          line: 419,
          column: 48
        }
      },
      "107": {
        start: {
          line: 427,
          column: 4
        },
        end: {
          line: 427,
          column: 37
        }
      },
      "108": {
        start: {
          line: 427,
          column: 30
        },
        end: {
          line: 427,
          column: 37
        }
      },
      "109": {
        start: {
          line: 430,
          column: 26
        },
        end: {
          line: 430,
          column: 56
        }
      },
      "110": {
        start: {
          line: 431,
          column: 30
        },
        end: {
          line: 431,
          column: 77
        }
      },
      "111": {
        start: {
          line: 431,
          column: 58
        },
        end: {
          line: 431,
          column: 76
        }
      },
      "112": {
        start: {
          line: 433,
          column: 4
        },
        end: {
          line: 435,
          column: 5
        }
      },
      "113": {
        start: {
          line: 434,
          column: 6
        },
        end: {
          line: 434,
          column: 83
        }
      },
      "114": {
        start: {
          line: 439,
          column: 50
        },
        end: {
          line: 439,
          column: 52
        }
      },
      "115": {
        start: {
          line: 441,
          column: 4
        },
        end: {
          line: 453,
          column: 5
        }
      },
      "116": {
        start: {
          line: 442,
          column: 6
        },
        end: {
          line: 452,
          column: 9
        }
      },
      "117": {
        start: {
          line: 450,
          column: 10
        },
        end: {
          line: 450,
          column: 48
        }
      },
      "118": {
        start: {
          line: 455,
          column: 4
        },
        end: {
          line: 455,
          column: 25
        }
      },
      "119": {
        start: {
          line: 459,
          column: 50
        },
        end: {
          line: 459,
          column: 52
        }
      },
      "120": {
        start: {
          line: 461,
          column: 4
        },
        end: {
          line: 473,
          column: 5
        }
      },
      "121": {
        start: {
          line: 462,
          column: 6
        },
        end: {
          line: 472,
          column: 9
        }
      },
      "122": {
        start: {
          line: 470,
          column: 10
        },
        end: {
          line: 470,
          column: 55
        }
      },
      "123": {
        start: {
          line: 475,
          column: 4
        },
        end: {
          line: 475,
          column: 25
        }
      },
      "124": {
        start: {
          line: 479,
          column: 50
        },
        end: {
          line: 479,
          column: 52
        }
      },
      "125": {
        start: {
          line: 481,
          column: 4
        },
        end: {
          line: 493,
          column: 5
        }
      },
      "126": {
        start: {
          line: 482,
          column: 6
        },
        end: {
          line: 492,
          column: 9
        }
      },
      "127": {
        start: {
          line: 490,
          column: 10
        },
        end: {
          line: 490,
          column: 51
        }
      },
      "128": {
        start: {
          line: 495,
          column: 4
        },
        end: {
          line: 495,
          column: 25
        }
      },
      "129": {
        start: {
          line: 499,
          column: 50
        },
        end: {
          line: 499,
          column: 52
        }
      },
      "130": {
        start: {
          line: 501,
          column: 4
        },
        end: {
          line: 513,
          column: 5
        }
      },
      "131": {
        start: {
          line: 502,
          column: 6
        },
        end: {
          line: 512,
          column: 9
        }
      },
      "132": {
        start: {
          line: 510,
          column: 10
        },
        end: {
          line: 510,
          column: 56
        }
      },
      "133": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 515,
          column: 25
        }
      },
      "134": {
        start: {
          line: 519,
          column: 4
        },
        end: {
          line: 531,
          column: 5
        }
      },
      "135": {
        start: {
          line: 520,
          column: 6
        },
        end: {
          line: 530,
          column: 7
        }
      },
      "136": {
        start: {
          line: 521,
          column: 8
        },
        end: {
          line: 521,
          column: 44
        }
      },
      "137": {
        start: {
          line: 522,
          column: 8
        },
        end: {
          line: 522,
          column: 72
        }
      },
      "138": {
        start: {
          line: 524,
          column: 8
        },
        end: {
          line: 527,
          column: 10
        }
      },
      "139": {
        start: {
          line: 529,
          column: 8
        },
        end: {
          line: 529,
          column: 87
        }
      },
      "140": {
        start: {
          line: 535,
          column: 4
        },
        end: {
          line: 535,
          column: 39
        }
      },
      "141": {
        start: {
          line: 535,
          column: 30
        },
        end: {
          line: 535,
          column: 39
        }
      },
      "142": {
        start: {
          line: 537,
          column: 18
        },
        end: {
          line: 537,
          column: 124
        }
      },
      "143": {
        start: {
          line: 538,
          column: 4
        },
        end: {
          line: 538,
          column: 44
        }
      },
      "144": {
        start: {
          line: 542,
          column: 4
        },
        end: {
          line: 542,
          column: 39
        }
      },
      "145": {
        start: {
          line: 542,
          column: 30
        },
        end: {
          line: 542,
          column: 39
        }
      },
      "146": {
        start: {
          line: 544,
          column: 4
        },
        end: {
          line: 544,
          column: 68
        }
      },
      "147": {
        start: {
          line: 548,
          column: 4
        },
        end: {
          line: 548,
          column: 39
        }
      },
      "148": {
        start: {
          line: 548,
          column: 30
        },
        end: {
          line: 548,
          column: 39
        }
      },
      "149": {
        start: {
          line: 550,
          column: 25
        },
        end: {
          line: 550,
          column: 86
        }
      },
      "150": {
        start: {
          line: 551,
          column: 25
        },
        end: {
          line: 551,
          column: 92
        }
      },
      "151": {
        start: {
          line: 552,
          column: 4
        },
        end: {
          line: 552,
          column: 45
        }
      },
      "152": {
        start: {
          line: 556,
          column: 4
        },
        end: {
          line: 556,
          column: 39
        }
      },
      "153": {
        start: {
          line: 556,
          column: 30
        },
        end: {
          line: 556,
          column: 39
        }
      },
      "154": {
        start: {
          line: 558,
          column: 23
        },
        end: {
          line: 558,
          column: 62
        }
      },
      "155": {
        start: {
          line: 559,
          column: 23
        },
        end: {
          line: 559,
          column: 86
        }
      },
      "156": {
        start: {
          line: 560,
          column: 4
        },
        end: {
          line: 560,
          column: 41
        }
      },
      "157": {
        start: {
          line: 569,
          column: 4
        },
        end: {
          line: 569,
          column: 57
        }
      },
      "158": {
        start: {
          line: 577,
          column: 46
        },
        end: {
          line: 577,
          column: 48
        }
      },
      "159": {
        start: {
          line: 579,
          column: 4
        },
        end: {
          line: 579,
          column: 47
        }
      },
      "160": {
        start: {
          line: 579,
          column: 28
        },
        end: {
          line: 579,
          column: 47
        }
      },
      "161": {
        start: {
          line: 581,
          column: 19
        },
        end: {
          line: 581,
          column: 46
        }
      },
      "162": {
        start: {
          line: 582,
          column: 21
        },
        end: {
          line: 582,
          column: 48
        }
      },
      "163": {
        start: {
          line: 585,
          column: 24
        },
        end: {
          line: 586,
          column: 62
        }
      },
      "164": {
        start: {
          line: 587,
          column: 28
        },
        end: {
          line: 587,
          column: 92
        }
      },
      "165": {
        start: {
          line: 589,
          column: 4
        },
        end: {
          line: 595,
          column: 7
        }
      },
      "166": {
        start: {
          line: 598,
          column: 21
        },
        end: {
          line: 599,
          column: 59
        }
      },
      "167": {
        start: {
          line: 600,
          column: 25
        },
        end: {
          line: 600,
          column: 84
        }
      },
      "168": {
        start: {
          line: 602,
          column: 4
        },
        end: {
          line: 608,
          column: 7
        }
      },
      "169": {
        start: {
          line: 610,
          column: 4
        },
        end: {
          line: 610,
          column: 23
        }
      },
      "170": {
        start: {
          line: 615,
          column: 36
        },
        end: {
          line: 615,
          column: 62
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 111,
            column: 2
          },
          end: {
            line: 111,
            column: 3
          }
        },
        loc: {
          start: {
            line: 111,
            column: 16
          },
          end: {
            line: 114,
            column: 3
          }
        },
        line: 111
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 119,
            column: 2
          },
          end: {
            line: 119,
            column: 3
          }
        },
        loc: {
          start: {
            line: 119,
            column: 59
          },
          end: {
            line: 134,
            column: 3
          }
        },
        line: 119
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 139,
            column: 2
          },
          end: {
            line: 139,
            column: 3
          }
        },
        loc: {
          start: {
            line: 139,
            column: 46
          },
          end: {
            line: 141,
            column: 3
          }
        },
        line: 139
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 146,
            column: 2
          },
          end: {
            line: 146,
            column: 3
          }
        },
        loc: {
          start: {
            line: 146,
            column: 88
          },
          end: {
            line: 162,
            column: 3
          }
        },
        line: 146
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 167,
            column: 2
          },
          end: {
            line: 167,
            column: 3
          }
        },
        loc: {
          start: {
            line: 167,
            column: 61
          },
          end: {
            line: 200,
            column: 3
          }
        },
        line: 167
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 192,
            column: 52
          },
          end: {
            line: 192,
            column: 53
          }
        },
        loc: {
          start: {
            line: 192,
            column: 59
          },
          end: {
            line: 192,
            column: 103
          }
        },
        line: 192
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 205,
            column: 2
          },
          end: {
            line: 205,
            column: 3
          }
        },
        loc: {
          start: {
            line: 209,
            column: 4
          },
          end: {
            line: 251,
            column: 3
          }
        },
        line: 209
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 256,
            column: 2
          },
          end: {
            line: 256,
            column: 3
          }
        },
        loc: {
          start: {
            line: 262,
            column: 4
          },
          end: {
            line: 275,
            column: 3
          }
        },
        line: 262
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 279,
            column: 2
          },
          end: {
            line: 279,
            column: 3
          }
        },
        loc: {
          start: {
            line: 279,
            column: 42
          },
          end: {
            line: 283,
            column: 3
          }
        },
        line: 279
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 280,
            column: 16
          },
          end: {
            line: 280,
            column: 17
          }
        },
        loc: {
          start: {
            line: 280,
            column: 28
          },
          end: {
            line: 282,
            column: 5
          }
        },
        line: 280
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 285,
            column: 2
          },
          end: {
            line: 285,
            column: 3
          }
        },
        loc: {
          start: {
            line: 285,
            column: 42
          },
          end: {
            line: 289,
            column: 3
          }
        },
        line: 285
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 286,
            column: 16
          },
          end: {
            line: 286,
            column: 17
          }
        },
        loc: {
          start: {
            line: 286,
            column: 28
          },
          end: {
            line: 288,
            column: 5
          }
        },
        line: 286
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 291,
            column: 2
          },
          end: {
            line: 291,
            column: 3
          }
        },
        loc: {
          start: {
            line: 291,
            column: 56
          },
          end: {
            line: 320,
            column: 3
          }
        },
        line: 291
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 322,
            column: 2
          },
          end: {
            line: 322,
            column: 3
          }
        },
        loc: {
          start: {
            line: 322,
            column: 75
          },
          end: {
            line: 334,
            column: 3
          }
        },
        line: 322
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 336,
            column: 2
          },
          end: {
            line: 336,
            column: 3
          }
        },
        loc: {
          start: {
            line: 336,
            column: 69
          },
          end: {
            line: 343,
            column: 3
          }
        },
        line: 336
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 345,
            column: 2
          },
          end: {
            line: 345,
            column: 3
          }
        },
        loc: {
          start: {
            line: 345,
            column: 77
          },
          end: {
            line: 353,
            column: 3
          }
        },
        line: 345
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 355,
            column: 2
          },
          end: {
            line: 355,
            column: 3
          }
        },
        loc: {
          start: {
            line: 355,
            column: 77
          },
          end: {
            line: 362,
            column: 3
          }
        },
        line: 355
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 364,
            column: 2
          },
          end: {
            line: 364,
            column: 3
          }
        },
        loc: {
          start: {
            line: 364,
            column: 77
          },
          end: {
            line: 371,
            column: 3
          }
        },
        line: 364
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 373,
            column: 81
          },
          end: {
            line: 397,
            column: 3
          }
        },
        line: 373
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 399,
            column: 2
          },
          end: {
            line: 399,
            column: 3
          }
        },
        loc: {
          start: {
            line: 399,
            column: 54
          },
          end: {
            line: 406,
            column: 3
          }
        },
        line: 399
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 408,
            column: 2
          },
          end: {
            line: 408,
            column: 3
          }
        },
        loc: {
          start: {
            line: 408,
            column: 51
          },
          end: {
            line: 415,
            column: 3
          }
        },
        line: 408
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 417,
            column: 2
          },
          end: {
            line: 417,
            column: 3
          }
        },
        loc: {
          start: {
            line: 417,
            column: 55
          },
          end: {
            line: 424,
            column: 3
          }
        },
        line: 417
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 426,
            column: 2
          },
          end: {
            line: 426,
            column: 3
          }
        },
        loc: {
          start: {
            line: 426,
            column: 62
          },
          end: {
            line: 436,
            column: 3
          }
        },
        line: 426
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 431,
            column: 51
          },
          end: {
            line: 431,
            column: 52
          }
        },
        loc: {
          start: {
            line: 431,
            column: 58
          },
          end: {
            line: 431,
            column: 76
          }
        },
        line: 431
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 438,
            column: 2
          },
          end: {
            line: 438,
            column: 3
          }
        },
        loc: {
          start: {
            line: 438,
            column: 79
          },
          end: {
            line: 456,
            column: 3
          }
        },
        line: 438
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 448,
            column: 24
          },
          end: {
            line: 448,
            column: 25
          }
        },
        loc: {
          start: {
            line: 448,
            column: 36
          },
          end: {
            line: 451,
            column: 9
          }
        },
        line: 448
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 458,
            column: 2
          },
          end: {
            line: 458,
            column: 3
          }
        },
        loc: {
          start: {
            line: 458,
            column: 76
          },
          end: {
            line: 476,
            column: 3
          }
        },
        line: 458
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 468,
            column: 24
          },
          end: {
            line: 468,
            column: 25
          }
        },
        loc: {
          start: {
            line: 468,
            column: 36
          },
          end: {
            line: 471,
            column: 9
          }
        },
        line: 468
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 478,
            column: 2
          },
          end: {
            line: 478,
            column: 3
          }
        },
        loc: {
          start: {
            line: 478,
            column: 80
          },
          end: {
            line: 496,
            column: 3
          }
        },
        line: 478
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 488,
            column: 24
          },
          end: {
            line: 488,
            column: 25
          }
        },
        loc: {
          start: {
            line: 488,
            column: 36
          },
          end: {
            line: 491,
            column: 9
          }
        },
        line: 488
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 498,
            column: 2
          },
          end: {
            line: 498,
            column: 3
          }
        },
        loc: {
          start: {
            line: 498,
            column: 80
          },
          end: {
            line: 516,
            column: 3
          }
        },
        line: 498
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 508,
            column: 24
          },
          end: {
            line: 508,
            column: 25
          }
        },
        loc: {
          start: {
            line: 508,
            column: 36
          },
          end: {
            line: 511,
            column: 9
          }
        },
        line: 508
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 518,
            column: 2
          },
          end: {
            line: 518,
            column: 3
          }
        },
        loc: {
          start: {
            line: 518,
            column: 91
          },
          end: {
            line: 532,
            column: 3
          }
        },
        line: 518
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 534,
            column: 2
          },
          end: {
            line: 534,
            column: 3
          }
        },
        loc: {
          start: {
            line: 534,
            column: 46
          },
          end: {
            line: 539,
            column: 3
          }
        },
        line: 534
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 541,
            column: 2
          },
          end: {
            line: 541,
            column: 3
          }
        },
        loc: {
          start: {
            line: 541,
            column: 43
          },
          end: {
            line: 545,
            column: 3
          }
        },
        line: 541
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 547,
            column: 2
          },
          end: {
            line: 547,
            column: 3
          }
        },
        loc: {
          start: {
            line: 547,
            column: 47
          },
          end: {
            line: 553,
            column: 3
          }
        },
        line: 547
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 555,
            column: 2
          },
          end: {
            line: 555,
            column: 3
          }
        },
        loc: {
          start: {
            line: 555,
            column: 47
          },
          end: {
            line: 561,
            column: 3
          }
        },
        line: 555
      },
      "37": {
        name: "(anonymous_37)",
        decl: {
          start: {
            line: 568,
            column: 2
          },
          end: {
            line: 568,
            column: 3
          }
        },
        loc: {
          start: {
            line: 568,
            column: 36
          },
          end: {
            line: 570,
            column: 3
          }
        },
        line: 568
      },
      "38": {
        name: "(anonymous_38)",
        decl: {
          start: {
            line: 572,
            column: 2
          },
          end: {
            line: 572,
            column: 3
          }
        },
        loc: {
          start: {
            line: 575,
            column: 35
          },
          end: {
            line: 611,
            column: 3
          }
        },
        line: 575
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 146,
            column: 31
          },
          end: {
            line: 146,
            column: 55
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 146,
            column: 53
          },
          end: {
            line: 146,
            column: 55
          }
        }],
        line: 146
      },
      "1": {
        loc: {
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "2": {
        loc: {
          start: {
            line: 147,
            column: 8
          },
          end: {
            line: 147,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 147,
            column: 8
          },
          end: {
            line: 147,
            column: 28
          }
        }, {
          start: {
            line: 147,
            column: 32
          },
          end: {
            line: 147,
            column: 64
          }
        }],
        line: 147
      },
      "3": {
        loc: {
          start: {
            line: 168,
            column: 4
          },
          end: {
            line: 170,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 4
          },
          end: {
            line: 170,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "4": {
        loc: {
          start: {
            line: 192,
            column: 59
          },
          end: {
            line: 192,
            column: 103
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 192,
            column: 59
          },
          end: {
            line: 192,
            column: 77
          }
        }, {
          start: {
            line: 192,
            column: 81
          },
          end: {
            line: 192,
            column: 103
          }
        }],
        line: 192
      },
      "5": {
        loc: {
          start: {
            line: 210,
            column: 4
          },
          end: {
            line: 212,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 4
          },
          end: {
            line: 212,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "6": {
        loc: {
          start: {
            line: 219,
            column: 4
          },
          end: {
            line: 223,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 219,
            column: 4
          },
          end: {
            line: 223,
            column: 5
          }
        }, {
          start: {
            line: 221,
            column: 11
          },
          end: {
            line: 223,
            column: 5
          }
        }],
        line: 219
      },
      "7": {
        loc: {
          start: {
            line: 221,
            column: 11
          },
          end: {
            line: 223,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 11
          },
          end: {
            line: 223,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "8": {
        loc: {
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 230,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 230,
            column: 5
          }
        }, {
          start: {
            line: 228,
            column: 11
          },
          end: {
            line: 230,
            column: 5
          }
        }],
        line: 226
      },
      "9": {
        loc: {
          start: {
            line: 228,
            column: 11
          },
          end: {
            line: 230,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 11
          },
          end: {
            line: 230,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "10": {
        loc: {
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 237,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 233,
            column: 4
          },
          end: {
            line: 237,
            column: 5
          }
        }, {
          start: {
            line: 235,
            column: 11
          },
          end: {
            line: 237,
            column: 5
          }
        }],
        line: 233
      },
      "11": {
        loc: {
          start: {
            line: 235,
            column: 11
          },
          end: {
            line: 237,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 235,
            column: 11
          },
          end: {
            line: 237,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 235
      },
      "12": {
        loc: {
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 240,
            column: 4
          },
          end: {
            line: 243,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 240
      },
      "13": {
        loc: {
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 265,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 263,
            column: 4
          },
          end: {
            line: 265,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 263
      },
      "14": {
        loc: {
          start: {
            line: 310,
            column: 6
          },
          end: {
            line: 312,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 310,
            column: 6
          },
          end: {
            line: 312,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 310
      },
      "15": {
        loc: {
          start: {
            line: 330,
            column: 16
          },
          end: {
            line: 330,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 330,
            column: 36
          },
          end: {
            line: 330,
            column: 46
          }
        }, {
          start: {
            line: 330,
            column: 49
          },
          end: {
            line: 330,
            column: 114
          }
        }],
        line: 330
      },
      "16": {
        loc: {
          start: {
            line: 330,
            column: 49
          },
          end: {
            line: 330,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 330,
            column: 69
          },
          end: {
            line: 330,
            column: 75
          }
        }, {
          start: {
            line: 330,
            column: 78
          },
          end: {
            line: 330,
            column: 114
          }
        }],
        line: 330
      },
      "17": {
        loc: {
          start: {
            line: 330,
            column: 78
          },
          end: {
            line: 330,
            column: 114
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 330,
            column: 98
          },
          end: {
            line: 330,
            column: 106
          }
        }, {
          start: {
            line: 330,
            column: 109
          },
          end: {
            line: 330,
            column: 114
          }
        }],
        line: 330
      },
      "18": {
        loc: {
          start: {
            line: 377,
            column: 4
          },
          end: {
            line: 380,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 377,
            column: 4
          },
          end: {
            line: 380,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 377
      },
      "19": {
        loc: {
          start: {
            line: 383,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 383,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 383
      },
      "20": {
        loc: {
          start: {
            line: 389,
            column: 4
          },
          end: {
            line: 392,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 389,
            column: 4
          },
          end: {
            line: 392,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 389
      },
      "21": {
        loc: {
          start: {
            line: 394,
            column: 4
          },
          end: {
            line: 396,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 394,
            column: 4
          },
          end: {
            line: 396,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 394
      },
      "22": {
        loc: {
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 427,
            column: 37
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 427,
            column: 4
          },
          end: {
            line: 427,
            column: 37
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 427
      },
      "23": {
        loc: {
          start: {
            line: 433,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 433,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 433
      },
      "24": {
        loc: {
          start: {
            line: 441,
            column: 4
          },
          end: {
            line: 453,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 441,
            column: 4
          },
          end: {
            line: 453,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 441
      },
      "25": {
        loc: {
          start: {
            line: 461,
            column: 4
          },
          end: {
            line: 473,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 461,
            column: 4
          },
          end: {
            line: 473,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 461
      },
      "26": {
        loc: {
          start: {
            line: 481,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 481,
            column: 4
          },
          end: {
            line: 493,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 481
      },
      "27": {
        loc: {
          start: {
            line: 501,
            column: 4
          },
          end: {
            line: 513,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 501,
            column: 4
          },
          end: {
            line: 513,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 501
      },
      "28": {
        loc: {
          start: {
            line: 535,
            column: 4
          },
          end: {
            line: 535,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 535,
            column: 4
          },
          end: {
            line: 535,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 535
      },
      "29": {
        loc: {
          start: {
            line: 542,
            column: 4
          },
          end: {
            line: 542,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 542,
            column: 4
          },
          end: {
            line: 542,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 542
      },
      "30": {
        loc: {
          start: {
            line: 548,
            column: 4
          },
          end: {
            line: 548,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 548,
            column: 4
          },
          end: {
            line: 548,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 548
      },
      "31": {
        loc: {
          start: {
            line: 556,
            column: 4
          },
          end: {
            line: 556,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 556,
            column: 4
          },
          end: {
            line: 556,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 556
      },
      "32": {
        loc: {
          start: {
            line: 579,
            column: 4
          },
          end: {
            line: 579,
            column: 47
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 579,
            column: 4
          },
          end: {
            line: 579,
            column: 47
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 579
      },
      "33": {
        loc: {
          start: {
            line: 594,
            column: 23
          },
          end: {
            line: 594,
            column: 80
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 594,
            column: 48
          },
          end: {
            line: 594,
            column: 75
          }
        }, {
          start: {
            line: 594,
            column: 78
          },
          end: {
            line: 594,
            column: 80
          }
        }],
        line: 594
      },
      "34": {
        loc: {
          start: {
            line: 607,
            column: 23
          },
          end: {
            line: 607,
            column: 78
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 607,
            column: 44
          },
          end: {
            line: 607,
            column: 73
          }
        }, {
          start: {
            line: 607,
            column: 76
          },
          end: {
            line: 607,
            column: 78
          }
        }],
        line: 607
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "153524b3fc8f4399dcad268ec94b258fb29dd16e"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1zsc58rp56 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zsc58rp56();
import { performanceMonitor } from "../../utils/performance";
var SmartResourceManager = function () {
  function SmartResourceManager() {
    _classCallCheck(this, SmartResourceManager);
    this.currentMetrics = (cov_1zsc58rp56().s[0]++, null);
    this.resourceHistory = (cov_1zsc58rp56().s[1]++, []);
    this.activeOptimizations = (cov_1zsc58rp56().s[2]++, new Map());
    this.MONITORING_INTERVAL = (cov_1zsc58rp56().s[3]++, 5000);
    this.OPTIMIZATION_INTERVAL = (cov_1zsc58rp56().s[4]++, 30000);
    this.HISTORY_LIMIT = (cov_1zsc58rp56().s[5]++, 1000);
    this.THRESHOLDS = (cov_1zsc58rp56().s[6]++, {
      memory: {
        warning: 0.8,
        critical: 0.95
      },
      cpu: {
        warning: 0.7,
        critical: 0.9
      },
      battery: {
        warning: 0.2,
        critical: 0.1
      },
      network: {
        latencyWarning: 1000,
        failureRateWarning: 0.1
      }
    });
    cov_1zsc58rp56().f[0]++;
    cov_1zsc58rp56().s[7]++;
    this.predictionModel = new ResourcePredictionModel();
    cov_1zsc58rp56().s[8]++;
    this.initializeResourceManager();
  }
  return _createClass(SmartResourceManager, [{
    key: "initializeResourceManager",
    value: (function () {
      var _initializeResourceManager = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[1]++;
        cov_1zsc58rp56().s[9]++;
        try {
          cov_1zsc58rp56().s[10]++;
          this.startResourceMonitoring();
          cov_1zsc58rp56().s[11]++;
          this.startOptimizationEngine();
          cov_1zsc58rp56().s[12]++;
          yield this.predictionModel.initialize();
          cov_1zsc58rp56().s[13]++;
          console.log('Smart Resource Manager initialized successfully');
        } catch (error) {
          cov_1zsc58rp56().s[14]++;
          console.error('Failed to initialize Smart Resource Manager:', error);
        }
      });
      function initializeResourceManager() {
        return _initializeResourceManager.apply(this, arguments);
      }
      return initializeResourceManager;
    }())
  }, {
    key: "getCurrentMetrics",
    value: function getCurrentMetrics() {
      cov_1zsc58rp56().f[2]++;
      cov_1zsc58rp56().s[15]++;
      return this.currentMetrics;
    }
  }, {
    key: "getResourcePredictions",
    value: (function () {
      var _getResourcePredictions = _asyncToGenerator(function* () {
        var timeHorizon = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1zsc58rp56().b[0][0]++, 30);
        cov_1zsc58rp56().f[3]++;
        cov_1zsc58rp56().s[16]++;
        if ((cov_1zsc58rp56().b[2][0]++, !this.currentMetrics) || (cov_1zsc58rp56().b[2][1]++, this.resourceHistory.length < 10)) {
          cov_1zsc58rp56().b[1][0]++;
          cov_1zsc58rp56().s[17]++;
          return [];
        } else {
          cov_1zsc58rp56().b[1][1]++;
        }
        cov_1zsc58rp56().s[18]++;
        try {
          var predictions = (cov_1zsc58rp56().s[19]++, yield this.predictionModel.predict(this.resourceHistory.slice(-20), timeHorizon));
          cov_1zsc58rp56().s[20]++;
          return predictions;
        } catch (error) {
          cov_1zsc58rp56().s[21]++;
          console.error('Failed to generate resource predictions:', error);
          cov_1zsc58rp56().s[22]++;
          return [];
        }
      });
      function getResourcePredictions() {
        return _getResourcePredictions.apply(this, arguments);
      }
      return getResourcePredictions;
    }())
  }, {
    key: "optimizeResources",
    value: (function () {
      var _optimizeResources = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[4]++;
        cov_1zsc58rp56().s[23]++;
        if (!this.currentMetrics) {
          cov_1zsc58rp56().b[3][0]++;
          cov_1zsc58rp56().s[24]++;
          return [];
        } else {
          cov_1zsc58rp56().b[3][1]++;
        }
        var optimizations = (cov_1zsc58rp56().s[25]++, []);
        cov_1zsc58rp56().s[26]++;
        try {
          var memoryOpts = (cov_1zsc58rp56().s[27]++, yield this.generateMemoryOptimizations());
          cov_1zsc58rp56().s[28]++;
          optimizations.push.apply(optimizations, _toConsumableArray(memoryOpts));
          var cpuOpts = (cov_1zsc58rp56().s[29]++, yield this.generateCpuOptimizations());
          cov_1zsc58rp56().s[30]++;
          optimizations.push.apply(optimizations, _toConsumableArray(cpuOpts));
          var networkOpts = (cov_1zsc58rp56().s[31]++, yield this.generateNetworkOptimizations());
          cov_1zsc58rp56().s[32]++;
          optimizations.push.apply(optimizations, _toConsumableArray(networkOpts));
          var batteryOpts = (cov_1zsc58rp56().s[33]++, yield this.generateBatteryOptimizations());
          cov_1zsc58rp56().s[34]++;
          optimizations.push.apply(optimizations, _toConsumableArray(batteryOpts));
          var highPriorityOpts = (cov_1zsc58rp56().s[35]++, optimizations.filter(function (opt) {
            cov_1zsc58rp56().f[5]++;
            cov_1zsc58rp56().s[36]++;
            return (cov_1zsc58rp56().b[4][0]++, opt.risk === 'low') && (cov_1zsc58rp56().b[4][1]++, opt.expectedGain > 0.2);
          }));
          cov_1zsc58rp56().s[37]++;
          yield this.executeOptimizations(highPriorityOpts);
          cov_1zsc58rp56().s[38]++;
          return optimizations;
        } catch (error) {
          cov_1zsc58rp56().s[39]++;
          console.error('Failed to optimize resources:', error);
          cov_1zsc58rp56().s[40]++;
          return [];
        }
      });
      function optimizeResources() {
        return _optimizeResources.apply(this, arguments);
      }
      return optimizeResources;
    }())
  }, {
    key: "getOptimizationRecommendations",
    value: function getOptimizationRecommendations() {
      cov_1zsc58rp56().f[6]++;
      cov_1zsc58rp56().s[41]++;
      if (!this.currentMetrics) {
        cov_1zsc58rp56().b[5][0]++;
        cov_1zsc58rp56().s[42]++;
        return {
          immediate: [],
          shortTerm: [],
          longTerm: []
        };
      } else {
        cov_1zsc58rp56().b[5][1]++;
      }
      var immediate = (cov_1zsc58rp56().s[43]++, []);
      var shortTerm = (cov_1zsc58rp56().s[44]++, []);
      var longTerm = (cov_1zsc58rp56().s[45]++, []);
      cov_1zsc58rp56().s[46]++;
      if (this.currentMetrics.memory.pressure === 'critical') {
        cov_1zsc58rp56().b[6][0]++;
        cov_1zsc58rp56().s[47]++;
        immediate.push('Clear unnecessary caches and free memory');
      } else {
        cov_1zsc58rp56().b[6][1]++;
        cov_1zsc58rp56().s[48]++;
        if (this.currentMetrics.memory.pressure === 'high') {
          cov_1zsc58rp56().b[7][0]++;
          cov_1zsc58rp56().s[49]++;
          shortTerm.push('Optimize memory usage patterns');
        } else {
          cov_1zsc58rp56().b[7][1]++;
        }
      }
      cov_1zsc58rp56().s[50]++;
      if (this.currentMetrics.cpu.usage > this.THRESHOLDS.cpu.critical) {
        cov_1zsc58rp56().b[8][0]++;
        cov_1zsc58rp56().s[51]++;
        immediate.push('Reduce CPU-intensive operations');
      } else {
        cov_1zsc58rp56().b[8][1]++;
        cov_1zsc58rp56().s[52]++;
        if (this.currentMetrics.cpu.usage > this.THRESHOLDS.cpu.warning) {
          cov_1zsc58rp56().b[9][0]++;
          cov_1zsc58rp56().s[53]++;
          shortTerm.push('Optimize computational algorithms');
        } else {
          cov_1zsc58rp56().b[9][1]++;
        }
      }
      cov_1zsc58rp56().s[54]++;
      if (this.currentMetrics.battery.level < this.THRESHOLDS.battery.critical) {
        cov_1zsc58rp56().b[10][0]++;
        cov_1zsc58rp56().s[55]++;
        immediate.push('Enable power saving mode');
      } else {
        cov_1zsc58rp56().b[10][1]++;
        cov_1zsc58rp56().s[56]++;
        if (this.currentMetrics.battery.level < this.THRESHOLDS.battery.warning) {
          cov_1zsc58rp56().b[11][0]++;
          cov_1zsc58rp56().s[57]++;
          shortTerm.push('Reduce background processing');
        } else {
          cov_1zsc58rp56().b[11][1]++;
        }
      }
      cov_1zsc58rp56().s[58]++;
      if (this.currentMetrics.network.failureRate > this.THRESHOLDS.network.failureRateWarning) {
        cov_1zsc58rp56().b[12][0]++;
        cov_1zsc58rp56().s[59]++;
        immediate.push('Implement request retry logic');
        cov_1zsc58rp56().s[60]++;
        shortTerm.push('Optimize network request patterns');
      } else {
        cov_1zsc58rp56().b[12][1]++;
      }
      cov_1zsc58rp56().s[61]++;
      longTerm.push('Implement predictive resource management');
      cov_1zsc58rp56().s[62]++;
      longTerm.push('Optimize data structures for memory efficiency');
      cov_1zsc58rp56().s[63]++;
      longTerm.push('Implement intelligent caching strategies');
      cov_1zsc58rp56().s[64]++;
      return {
        immediate: immediate,
        shortTerm: shortTerm,
        longTerm: longTerm
      };
    }
  }, {
    key: "getResourceEfficiencyScore",
    value: function getResourceEfficiencyScore() {
      cov_1zsc58rp56().f[7]++;
      cov_1zsc58rp56().s[65]++;
      if (!this.currentMetrics) {
        cov_1zsc58rp56().b[13][0]++;
        cov_1zsc58rp56().s[66]++;
        return {
          overall: 0,
          memory: 0,
          cpu: 0,
          network: 0,
          battery: 0
        };
      } else {
        cov_1zsc58rp56().b[13][1]++;
      }
      var memory = (cov_1zsc58rp56().s[67]++, this.calculateMemoryEfficiency());
      var cpu = (cov_1zsc58rp56().s[68]++, this.calculateCpuEfficiency());
      var network = (cov_1zsc58rp56().s[69]++, this.calculateNetworkEfficiency());
      var battery = (cov_1zsc58rp56().s[70]++, this.calculateBatteryEfficiency());
      var overall = (cov_1zsc58rp56().s[71]++, (memory + cpu + network + battery) / 4);
      cov_1zsc58rp56().s[72]++;
      return {
        overall: overall,
        memory: memory,
        cpu: cpu,
        network: network,
        battery: battery
      };
    }
  }, {
    key: "startResourceMonitoring",
    value: function startResourceMonitoring() {
      var _this = this;
      cov_1zsc58rp56().f[8]++;
      cov_1zsc58rp56().s[73]++;
      setInterval(_asyncToGenerator(function* () {
        cov_1zsc58rp56().f[9]++;
        cov_1zsc58rp56().s[74]++;
        yield _this.collectResourceMetrics();
      }), this.MONITORING_INTERVAL);
    }
  }, {
    key: "startOptimizationEngine",
    value: function startOptimizationEngine() {
      var _this2 = this;
      cov_1zsc58rp56().f[10]++;
      cov_1zsc58rp56().s[75]++;
      setInterval(_asyncToGenerator(function* () {
        cov_1zsc58rp56().f[11]++;
        cov_1zsc58rp56().s[76]++;
        yield _this2.performAutomaticOptimization();
      }), this.OPTIMIZATION_INTERVAL);
    }
  }, {
    key: "collectResourceMetrics",
    value: function () {
      var _collectResourceMetrics = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[12]++;
        cov_1zsc58rp56().s[77]++;
        try {
          var metrics = (cov_1zsc58rp56().s[78]++, {
            memory: yield this.collectMemoryMetrics(),
            cpu: yield this.collectCpuMetrics(),
            network: yield this.collectNetworkMetrics(),
            battery: yield this.collectBatteryMetrics(),
            storage: yield this.collectStorageMetrics()
          });
          cov_1zsc58rp56().s[79]++;
          this.currentMetrics = metrics;
          cov_1zsc58rp56().s[80]++;
          this.resourceHistory.push({
            timestamp: Date.now(),
            metrics: metrics
          });
          cov_1zsc58rp56().s[81]++;
          if (this.resourceHistory.length > this.HISTORY_LIMIT) {
            cov_1zsc58rp56().b[14][0]++;
            cov_1zsc58rp56().s[82]++;
            this.resourceHistory.shift();
          } else {
            cov_1zsc58rp56().b[14][1]++;
          }
          cov_1zsc58rp56().s[83]++;
          yield this.checkCriticalConditions(metrics);
        } catch (error) {
          cov_1zsc58rp56().s[84]++;
          console.error('Failed to collect resource metrics:', error);
        }
      });
      function collectResourceMetrics() {
        return _collectResourceMetrics.apply(this, arguments);
      }
      return collectResourceMetrics;
    }()
  }, {
    key: "collectMemoryMetrics",
    value: function () {
      var _collectMemoryMetrics = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[13]++;
        var used = (cov_1zsc58rp56().s[85]++, Math.random() * 2048 + 512);
        var available = (cov_1zsc58rp56().s[86]++, 4096 - used);
        cov_1zsc58rp56().s[87]++;
        return {
          used: used,
          available: available,
          pressure: used / 4096 > 0.9 ? (cov_1zsc58rp56().b[15][0]++, 'critical') : (cov_1zsc58rp56().b[15][1]++, used / 4096 > 0.7 ? (cov_1zsc58rp56().b[16][0]++, 'high') : (cov_1zsc58rp56().b[16][1]++, used / 4096 > 0.5 ? (cov_1zsc58rp56().b[17][0]++, 'medium') : (cov_1zsc58rp56().b[17][1]++, 'low'))),
          leaks: [],
          gcFrequency: Math.random() * 10 + 5
        };
      });
      function collectMemoryMetrics() {
        return _collectMemoryMetrics.apply(this, arguments);
      }
      return collectMemoryMetrics;
    }()
  }, {
    key: "collectCpuMetrics",
    value: function () {
      var _collectCpuMetrics = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[14]++;
        cov_1zsc58rp56().s[88]++;
        return {
          usage: Math.random() * 0.8 + 0.1,
          temperature: Math.random() * 20 + 40,
          throttling: Math.random() > 0.9,
          heavyTasks: []
        };
      });
      function collectCpuMetrics() {
        return _collectCpuMetrics.apply(this, arguments);
      }
      return collectCpuMetrics;
    }()
  }, {
    key: "collectNetworkMetrics",
    value: function () {
      var _collectNetworkMetrics = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[15]++;
        cov_1zsc58rp56().s[89]++;
        return {
          bandwidth: Math.random() * 50 + 10,
          latency: Math.random() * 200 + 50,
          activeConnections: Math.floor(Math.random() * 10 + 2),
          queuedRequests: Math.floor(Math.random() * 5),
          failureRate: Math.random() * 0.1
        };
      });
      function collectNetworkMetrics() {
        return _collectNetworkMetrics.apply(this, arguments);
      }
      return collectNetworkMetrics;
    }()
  }, {
    key: "collectBatteryMetrics",
    value: function () {
      var _collectBatteryMetrics = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[16]++;
        cov_1zsc58rp56().s[90]++;
        return {
          level: Math.random() * 0.8 + 0.2,
          drainRate: Math.random() * 5 + 1,
          isCharging: Math.random() > 0.7,
          estimatedTimeRemaining: Math.random() * 480 + 60
        };
      });
      function collectBatteryMetrics() {
        return _collectBatteryMetrics.apply(this, arguments);
      }
      return collectBatteryMetrics;
    }()
  }, {
    key: "collectStorageMetrics",
    value: function () {
      var _collectStorageMetrics = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[17]++;
        cov_1zsc58rp56().s[91]++;
        return {
          used: Math.random() * 16000 + 4000,
          available: Math.random() * 32000 + 8000,
          ioOperations: Math.floor(Math.random() * 100 + 10),
          cacheSize: Math.random() * 500 + 100
        };
      });
      function collectStorageMetrics() {
        return _collectStorageMetrics.apply(this, arguments);
      }
      return collectStorageMetrics;
    }()
  }, {
    key: "checkCriticalConditions",
    value: function () {
      var _checkCriticalConditions = _asyncToGenerator(function* (metrics) {
        cov_1zsc58rp56().f[18]++;
        var alerts = (cov_1zsc58rp56().s[92]++, []);
        cov_1zsc58rp56().s[93]++;
        if (metrics.memory.pressure === 'critical') {
          cov_1zsc58rp56().b[18][0]++;
          cov_1zsc58rp56().s[94]++;
          alerts.push('Critical memory pressure detected');
          cov_1zsc58rp56().s[95]++;
          yield this.handleCriticalMemory();
        } else {
          cov_1zsc58rp56().b[18][1]++;
        }
        cov_1zsc58rp56().s[96]++;
        if (metrics.cpu.usage > this.THRESHOLDS.cpu.critical) {
          cov_1zsc58rp56().b[19][0]++;
          cov_1zsc58rp56().s[97]++;
          alerts.push('Critical CPU usage detected');
          cov_1zsc58rp56().s[98]++;
          yield this.handleCriticalCpu();
        } else {
          cov_1zsc58rp56().b[19][1]++;
        }
        cov_1zsc58rp56().s[99]++;
        if (metrics.battery.level < this.THRESHOLDS.battery.critical) {
          cov_1zsc58rp56().b[20][0]++;
          cov_1zsc58rp56().s[100]++;
          alerts.push('Critical battery level detected');
          cov_1zsc58rp56().s[101]++;
          yield this.handleCriticalBattery();
        } else {
          cov_1zsc58rp56().b[20][1]++;
        }
        cov_1zsc58rp56().s[102]++;
        if (alerts.length > 0) {
          cov_1zsc58rp56().b[21][0]++;
          cov_1zsc58rp56().s[103]++;
          console.warn('Critical resource conditions:', alerts);
        } else {
          cov_1zsc58rp56().b[21][1]++;
        }
      });
      function checkCriticalConditions(_x) {
        return _checkCriticalConditions.apply(this, arguments);
      }
      return checkCriticalConditions;
    }()
  }, {
    key: "handleCriticalMemory",
    value: function () {
      var _handleCriticalMemory = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[19]++;
        cov_1zsc58rp56().s[104]++;
        console.log('Executing emergency memory cleanup');
      });
      function handleCriticalMemory() {
        return _handleCriticalMemory.apply(this, arguments);
      }
      return handleCriticalMemory;
    }()
  }, {
    key: "handleCriticalCpu",
    value: function () {
      var _handleCriticalCpu = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[20]++;
        cov_1zsc58rp56().s[105]++;
        console.log('Reducing CPU load');
      });
      function handleCriticalCpu() {
        return _handleCriticalCpu.apply(this, arguments);
      }
      return handleCriticalCpu;
    }()
  }, {
    key: "handleCriticalBattery",
    value: function () {
      var _handleCriticalBattery = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[21]++;
        cov_1zsc58rp56().s[106]++;
        console.log('Activating power saving mode');
      });
      function handleCriticalBattery() {
        return _handleCriticalBattery.apply(this, arguments);
      }
      return handleCriticalBattery;
    }()
  }, {
    key: "performAutomaticOptimization",
    value: function () {
      var _performAutomaticOptimization = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[22]++;
        cov_1zsc58rp56().s[107]++;
        if (!this.currentMetrics) {
          cov_1zsc58rp56().b[22][0]++;
          cov_1zsc58rp56().s[108]++;
          return;
        } else {
          cov_1zsc58rp56().b[22][1]++;
        }
        var optimizations = (cov_1zsc58rp56().s[109]++, yield this.optimizeResources());
        var safeOptimizations = (cov_1zsc58rp56().s[110]++, optimizations.filter(function (opt) {
          cov_1zsc58rp56().f[23]++;
          cov_1zsc58rp56().s[111]++;
          return opt.risk === 'low';
        }));
        cov_1zsc58rp56().s[112]++;
        if (safeOptimizations.length > 0) {
          cov_1zsc58rp56().b[23][0]++;
          cov_1zsc58rp56().s[113]++;
          console.log(`Executing ${safeOptimizations.length} automatic optimizations`);
        } else {
          cov_1zsc58rp56().b[23][1]++;
        }
      });
      function performAutomaticOptimization() {
        return _performAutomaticOptimization.apply(this, arguments);
      }
      return performAutomaticOptimization;
    }()
  }, {
    key: "generateMemoryOptimizations",
    value: function () {
      var _generateMemoryOptimizations = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[24]++;
        var optimizations = (cov_1zsc58rp56().s[114]++, []);
        cov_1zsc58rp56().s[115]++;
        if (this.currentMetrics.memory.pressure === 'high') {
          cov_1zsc58rp56().b[24][0]++;
          cov_1zsc58rp56().s[116]++;
          optimizations.push({
            type: 'memory',
            action: 'Clear unused caches',
            target: 'cache_manager',
            expectedGain: 0.2,
            risk: 'low',
            implementation: function () {
              var _implementation = _asyncToGenerator(function* () {
                cov_1zsc58rp56().f[25]++;
                cov_1zsc58rp56().s[117]++;
                console.log('Clearing unused caches');
              });
              function implementation() {
                return _implementation.apply(this, arguments);
              }
              return implementation;
            }()
          });
        } else {
          cov_1zsc58rp56().b[24][1]++;
        }
        cov_1zsc58rp56().s[118]++;
        return optimizations;
      });
      function generateMemoryOptimizations() {
        return _generateMemoryOptimizations.apply(this, arguments);
      }
      return generateMemoryOptimizations;
    }()
  }, {
    key: "generateCpuOptimizations",
    value: function () {
      var _generateCpuOptimizations = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[26]++;
        var optimizations = (cov_1zsc58rp56().s[119]++, []);
        cov_1zsc58rp56().s[120]++;
        if (this.currentMetrics.cpu.usage > 0.7) {
          cov_1zsc58rp56().b[25][0]++;
          cov_1zsc58rp56().s[121]++;
          optimizations.push({
            type: 'cpu',
            action: 'Reduce animation frame rate',
            target: 'animation_engine',
            expectedGain: 0.15,
            risk: 'low',
            implementation: function () {
              var _implementation2 = _asyncToGenerator(function* () {
                cov_1zsc58rp56().f[27]++;
                cov_1zsc58rp56().s[122]++;
                console.log('Reducing animation frame rate');
              });
              function implementation() {
                return _implementation2.apply(this, arguments);
              }
              return implementation;
            }()
          });
        } else {
          cov_1zsc58rp56().b[25][1]++;
        }
        cov_1zsc58rp56().s[123]++;
        return optimizations;
      });
      function generateCpuOptimizations() {
        return _generateCpuOptimizations.apply(this, arguments);
      }
      return generateCpuOptimizations;
    }()
  }, {
    key: "generateNetworkOptimizations",
    value: function () {
      var _generateNetworkOptimizations = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[28]++;
        var optimizations = (cov_1zsc58rp56().s[124]++, []);
        cov_1zsc58rp56().s[125]++;
        if (this.currentMetrics.network.queuedRequests > 3) {
          cov_1zsc58rp56().b[26][0]++;
          cov_1zsc58rp56().s[126]++;
          optimizations.push({
            type: 'network',
            action: 'Batch network requests',
            target: 'network_manager',
            expectedGain: 0.3,
            risk: 'low',
            implementation: function () {
              var _implementation3 = _asyncToGenerator(function* () {
                cov_1zsc58rp56().f[29]++;
                cov_1zsc58rp56().s[127]++;
                console.log('Batching network requests');
              });
              function implementation() {
                return _implementation3.apply(this, arguments);
              }
              return implementation;
            }()
          });
        } else {
          cov_1zsc58rp56().b[26][1]++;
        }
        cov_1zsc58rp56().s[128]++;
        return optimizations;
      });
      function generateNetworkOptimizations() {
        return _generateNetworkOptimizations.apply(this, arguments);
      }
      return generateNetworkOptimizations;
    }()
  }, {
    key: "generateBatteryOptimizations",
    value: function () {
      var _generateBatteryOptimizations = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[30]++;
        var optimizations = (cov_1zsc58rp56().s[129]++, []);
        cov_1zsc58rp56().s[130]++;
        if (this.currentMetrics.battery.level < 0.3) {
          cov_1zsc58rp56().b[27][0]++;
          cov_1zsc58rp56().s[131]++;
          optimizations.push({
            type: 'battery',
            action: 'Reduce background processing',
            target: 'background_tasks',
            expectedGain: 0.25,
            risk: 'low',
            implementation: function () {
              var _implementation4 = _asyncToGenerator(function* () {
                cov_1zsc58rp56().f[31]++;
                cov_1zsc58rp56().s[132]++;
                console.log('Reducing background processing');
              });
              function implementation() {
                return _implementation4.apply(this, arguments);
              }
              return implementation;
            }()
          });
        } else {
          cov_1zsc58rp56().b[27][1]++;
        }
        cov_1zsc58rp56().s[133]++;
        return optimizations;
      });
      function generateBatteryOptimizations() {
        return _generateBatteryOptimizations.apply(this, arguments);
      }
      return generateBatteryOptimizations;
    }()
  }, {
    key: "executeOptimizations",
    value: function () {
      var _executeOptimizations = _asyncToGenerator(function* (optimizations) {
        cov_1zsc58rp56().f[32]++;
        cov_1zsc58rp56().s[134]++;
        for (var optimization of optimizations) {
          cov_1zsc58rp56().s[135]++;
          try {
            cov_1zsc58rp56().s[136]++;
            yield optimization.implementation();
            cov_1zsc58rp56().s[137]++;
            this.activeOptimizations.set(optimization.target, optimization);
            cov_1zsc58rp56().s[138]++;
            performanceMonitor.trackDatabaseQuery(`resource_optimization_${optimization.type}`, Date.now());
          } catch (error) {
            cov_1zsc58rp56().s[139]++;
            console.error(`Failed to execute optimization ${optimization.action}:`, error);
          }
        }
      });
      function executeOptimizations(_x2) {
        return _executeOptimizations.apply(this, arguments);
      }
      return executeOptimizations;
    }()
  }, {
    key: "calculateMemoryEfficiency",
    value: function calculateMemoryEfficiency() {
      cov_1zsc58rp56().f[33]++;
      cov_1zsc58rp56().s[140]++;
      if (!this.currentMetrics) {
        cov_1zsc58rp56().b[28][0]++;
        cov_1zsc58rp56().s[141]++;
        return 0;
      } else {
        cov_1zsc58rp56().b[28][1]++;
      }
      var usage = (cov_1zsc58rp56().s[142]++, this.currentMetrics.memory.used / (this.currentMetrics.memory.used + this.currentMetrics.memory.available));
      cov_1zsc58rp56().s[143]++;
      return Math.max(0, 100 - usage * 100);
    }
  }, {
    key: "calculateCpuEfficiency",
    value: function calculateCpuEfficiency() {
      cov_1zsc58rp56().f[34]++;
      cov_1zsc58rp56().s[144]++;
      if (!this.currentMetrics) {
        cov_1zsc58rp56().b[29][0]++;
        cov_1zsc58rp56().s[145]++;
        return 0;
      } else {
        cov_1zsc58rp56().b[29][1]++;
      }
      cov_1zsc58rp56().s[146]++;
      return Math.max(0, 100 - this.currentMetrics.cpu.usage * 100);
    }
  }, {
    key: "calculateNetworkEfficiency",
    value: function calculateNetworkEfficiency() {
      cov_1zsc58rp56().f[35]++;
      cov_1zsc58rp56().s[147]++;
      if (!this.currentMetrics) {
        cov_1zsc58rp56().b[30][0]++;
        cov_1zsc58rp56().s[148]++;
        return 0;
      } else {
        cov_1zsc58rp56().b[30][1]++;
      }
      var latencyScore = (cov_1zsc58rp56().s[149]++, Math.max(0, 100 - this.currentMetrics.network.latency / 10));
      var failureScore = (cov_1zsc58rp56().s[150]++, Math.max(0, 100 - this.currentMetrics.network.failureRate * 1000));
      cov_1zsc58rp56().s[151]++;
      return (latencyScore + failureScore) / 2;
    }
  }, {
    key: "calculateBatteryEfficiency",
    value: function calculateBatteryEfficiency() {
      cov_1zsc58rp56().f[36]++;
      cov_1zsc58rp56().s[152]++;
      if (!this.currentMetrics) {
        cov_1zsc58rp56().b[31][0]++;
        cov_1zsc58rp56().s[153]++;
        return 0;
      } else {
        cov_1zsc58rp56().b[31][1]++;
      }
      var levelScore = (cov_1zsc58rp56().s[154]++, this.currentMetrics.battery.level * 100);
      var drainScore = (cov_1zsc58rp56().s[155]++, Math.max(0, 100 - this.currentMetrics.battery.drainRate * 10));
      cov_1zsc58rp56().s[156]++;
      return (levelScore + drainScore) / 2;
    }
  }]);
}();
var ResourcePredictionModel = function () {
  function ResourcePredictionModel() {
    _classCallCheck(this, ResourcePredictionModel);
  }
  return _createClass(ResourcePredictionModel, [{
    key: "initialize",
    value: function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_1zsc58rp56().f[37]++;
        cov_1zsc58rp56().s[157]++;
        console.log('Initialized resource prediction model');
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }()
  }, {
    key: "predict",
    value: function () {
      var _predict = _asyncToGenerator(function* (history, timeHorizon) {
        cov_1zsc58rp56().f[38]++;
        var predictions = (cov_1zsc58rp56().s[158]++, []);
        cov_1zsc58rp56().s[159]++;
        if (history.length < 2) {
          cov_1zsc58rp56().b[32][0]++;
          cov_1zsc58rp56().s[160]++;
          return predictions;
        } else {
          cov_1zsc58rp56().b[32][1]++;
        }
        var latest = (cov_1zsc58rp56().s[161]++, history[history.length - 1]);
        var previous = (cov_1zsc58rp56().s[162]++, history[history.length - 2]);
        var memoryTrend = (cov_1zsc58rp56().s[163]++, (latest.metrics.memory.used - previous.metrics.memory.used) / (latest.timestamp - previous.timestamp));
        var predictedMemory = (cov_1zsc58rp56().s[164]++, latest.metrics.memory.used + memoryTrend * timeHorizon * 60000);
        cov_1zsc58rp56().s[165]++;
        predictions.push({
          resource: 'memory',
          timeHorizon: timeHorizon,
          predictedUsage: predictedMemory,
          confidence: 0.7,
          recommendations: predictedMemory > 3000 ? (cov_1zsc58rp56().b[33][0]++, ['Consider memory cleanup']) : (cov_1zsc58rp56().b[33][1]++, [])
        });
        var cpuTrend = (cov_1zsc58rp56().s[166]++, (latest.metrics.cpu.usage - previous.metrics.cpu.usage) / (latest.timestamp - previous.timestamp));
        var predictedCpu = (cov_1zsc58rp56().s[167]++, latest.metrics.cpu.usage + cpuTrend * timeHorizon * 60000);
        cov_1zsc58rp56().s[168]++;
        predictions.push({
          resource: 'cpu',
          timeHorizon: timeHorizon,
          predictedUsage: predictedCpu,
          confidence: 0.6,
          recommendations: predictedCpu > 0.8 ? (cov_1zsc58rp56().b[34][0]++, ['Reduce computational load']) : (cov_1zsc58rp56().b[34][1]++, [])
        });
        cov_1zsc58rp56().s[169]++;
        return predictions;
      });
      function predict(_x3, _x4) {
        return _predict.apply(this, arguments);
      }
      return predict;
    }()
  }]);
}();
export var smartResourceManager = (cov_1zsc58rp56().s[170]++, new SmartResourceManager());
export default smartResourceManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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