c1ae4e2eb3b48035a62f667324c09927
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _AccessibilityUtil = _interopRequireDefault(require("../../modules/AccessibilityUtil"));
var _createDOMProps = _interopRequireDefault(require("../../modules/createDOMProps"));
var _react = _interopRequireDefault(require("react"));
var _useLocale = require("../../modules/useLocale");
var createElement = function createElement(component, props, options) {
  var accessibilityComponent;
  if (component && component.constructor === String) {
    accessibilityComponent = _AccessibilityUtil.default.propsToAccessibilityComponent(props);
  }
  var Component = accessibilityComponent || component;
  var domProps = (0, _createDOMProps.default)(Component, props, options);
  var element = _react.default.createElement(Component, domProps);
  var elementWithLocaleProvider = domProps.dir ? _react.default.createElement(_useLocale.LocaleProvider, {
    children: element,
    direction: domProps.dir,
    locale: domProps.lang
  }) : element;
  return elementWithLocaleProvider;
};
var _default = exports.default = createElement;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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