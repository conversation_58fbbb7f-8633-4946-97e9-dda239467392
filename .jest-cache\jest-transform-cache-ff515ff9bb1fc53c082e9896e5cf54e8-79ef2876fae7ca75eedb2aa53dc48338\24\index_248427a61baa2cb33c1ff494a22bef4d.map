{"version": 3, "names": ["exports", "__esModule", "default", "LogBox", "ignoreLogs", "ignoreAllLogs", "uninstall", "install", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) 2016-present, <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar LogBox = {\n  ignoreLogs() {},\n  ignoreAllLogs() {},\n  uninstall() {},\n  install() {}\n};\nvar _default = exports.default = LogBox;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAUxB,IAAIC,MAAM,GAAG;EACXC,UAAU,WAAVA,UAAUA,CAAA,EAAG,CAAC,CAAC;EACfC,aAAa,WAAbA,aAAaA,CAAA,EAAG,CAAC,CAAC;EAClBC,SAAS,WAATA,SAASA,CAAA,EAAG,CAAC,CAAC;EACdC,OAAO,WAAPA,OAAOA,CAAA,EAAG,CAAC;AACb,CAAC;AACD,IAAIC,QAAQ,GAAGR,OAAO,CAACE,OAAO,GAAGC,MAAM;AACvCM,MAAM,CAACT,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}