{"version": 3, "names": ["_interopRequireWildcard", "require", "default", "_interopRequireDefault", "exports", "__esModule", "renderApplication", "getApplication", "_extends2", "_AppContainer", "_invariant", "_render", "_StyleSheet", "_react", "RootComponent", "WrapperComponent", "callback", "options", "shouldHydrate", "hydrate", "initialProps", "rootTag", "renderFn", "createElement", "ref", "element", "getStyleElement", "props", "sheet", "getSheet", "dangerouslySetInnerHTML", "__html", "textContent", "id"], "sources": ["renderApplication.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = renderApplication;\nexports.getApplication = getApplication;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _AppContainer = _interopRequireDefault(require(\"./AppContainer\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nvar _render = _interopRequireWildcard(require(\"../render\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _react = _interopRequireDefault(require(\"react\"));\n/**\n * Copyright (c) Nicolas <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nfunction renderApplication(RootComponent, WrapperComponent, callback, options) {\n  var shouldHydrate = options.hydrate,\n    initialProps = options.initialProps,\n    rootTag = options.rootTag;\n  var renderFn = shouldHydrate ? _render.hydrate : _render.default;\n  (0, _invariant.default)(rootTag, 'Expect to have a valid rootTag, instead got ', rootTag);\n  return renderFn(/*#__PURE__*/_react.default.createElement(_AppContainer.default, {\n    WrapperComponent: WrapperComponent,\n    ref: callback,\n    rootTag: rootTag\n  }, /*#__PURE__*/_react.default.createElement(RootComponent, initialProps)), rootTag);\n}\nfunction getApplication(RootComponent, initialProps, WrapperComponent) {\n  var element = /*#__PURE__*/_react.default.createElement(_AppContainer.default, {\n    WrapperComponent: WrapperComponent,\n    rootTag: {}\n  }, /*#__PURE__*/_react.default.createElement(RootComponent, initialProps));\n  // Don't escape CSS text\n  var getStyleElement = props => {\n    var sheet = _StyleSheet.default.getSheet();\n    return /*#__PURE__*/_react.default.createElement(\"style\", (0, _extends2.default)({}, props, {\n      dangerouslySetInnerHTML: {\n        __html: sheet.textContent\n      },\n      id: sheet.id\n    }));\n  };\n  return {\n    element,\n    getStyleElement\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,uBAAuB,GAAGC,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9F,IAAIC,sBAAsB,GAAGF,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAGI,iBAAiB;AACnCF,OAAO,CAACG,cAAc,GAAGA,cAAc;AACvC,IAAIC,SAAS,GAAGL,sBAAsB,CAACF,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIQ,aAAa,GAAGN,sBAAsB,CAACF,OAAO,iBAAiB,CAAC,CAAC;AACrE,IAAIS,UAAU,GAAGP,sBAAsB,CAACF,OAAO,CAAC,oBAAoB,CAAC,CAAC;AACtE,IAAIU,OAAO,GAAGX,uBAAuB,CAACC,OAAO,YAAY,CAAC,CAAC;AAC3D,IAAIW,WAAW,GAAGT,sBAAsB,CAACF,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIY,MAAM,GAAGV,sBAAsB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AAWrD,SAASK,iBAAiBA,CAACQ,aAAa,EAAEC,gBAAgB,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC7E,IAAIC,aAAa,GAAGD,OAAO,CAACE,OAAO;IACjCC,YAAY,GAAGH,OAAO,CAACG,YAAY;IACnCC,OAAO,GAAGJ,OAAO,CAACI,OAAO;EAC3B,IAAIC,QAAQ,GAAGJ,aAAa,GAAGP,OAAO,CAACQ,OAAO,GAAGR,OAAO,CAACT,OAAO;EAChE,CAAC,CAAC,EAAEQ,UAAU,CAACR,OAAO,EAAEmB,OAAO,EAAE,8CAA8C,EAAEA,OAAO,CAAC;EACzF,OAAOC,QAAQ,CAAcT,MAAM,CAACX,OAAO,CAACqB,aAAa,CAACd,aAAa,CAACP,OAAO,EAAE;IAC/Ea,gBAAgB,EAAEA,gBAAgB;IAClCS,GAAG,EAAER,QAAQ;IACbK,OAAO,EAAEA;EACX,CAAC,EAAeR,MAAM,CAACX,OAAO,CAACqB,aAAa,CAACT,aAAa,EAAEM,YAAY,CAAC,CAAC,EAAEC,OAAO,CAAC;AACtF;AACA,SAASd,cAAcA,CAACO,aAAa,EAAEM,YAAY,EAAEL,gBAAgB,EAAE;EACrE,IAAIU,OAAO,GAAgBZ,MAAM,CAACX,OAAO,CAACqB,aAAa,CAACd,aAAa,CAACP,OAAO,EAAE;IAC7Ea,gBAAgB,EAAEA,gBAAgB;IAClCM,OAAO,EAAE,CAAC;EACZ,CAAC,EAAeR,MAAM,CAACX,OAAO,CAACqB,aAAa,CAACT,aAAa,EAAEM,YAAY,CAAC,CAAC;EAE1E,IAAIM,eAAe,GAAG,SAAlBA,eAAeA,CAAGC,KAAK,EAAI;IAC7B,IAAIC,KAAK,GAAGhB,WAAW,CAACV,OAAO,CAAC2B,QAAQ,CAAC,CAAC;IAC1C,OAAoBhB,MAAM,CAACX,OAAO,CAACqB,aAAa,CAAC,OAAO,EAAE,CAAC,CAAC,EAAEf,SAAS,CAACN,OAAO,EAAE,CAAC,CAAC,EAAEyB,KAAK,EAAE;MAC1FG,uBAAuB,EAAE;QACvBC,MAAM,EAAEH,KAAK,CAACI;MAChB,CAAC;MACDC,EAAE,EAAEL,KAAK,CAACK;IACZ,CAAC,CAAC,CAAC;EACL,CAAC;EACD,OAAO;IACLR,OAAO,EAAPA,OAAO;IACPC,eAAe,EAAfA;EACF,CAAC;AACH", "ignoreList": []}