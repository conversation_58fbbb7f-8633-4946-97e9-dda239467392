{"version": 3, "names": ["exports", "__esModule", "default", "isDisabled", "props", "disabled", "Array", "isArray", "accessibilityStates", "indexOf", "_default", "module"], "sources": ["isDisabled.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar isDisabled = props => props.disabled || Array.isArray(props.accessibilityStates) && props.accessibilityStates.indexOf('disabled') > -1;\nvar _default = exports.default = isDisabled;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAUxB,IAAIC,UAAU,GAAG,SAAbA,UAAUA,CAAGC,KAAK;EAAA,OAAIA,KAAK,CAACC,QAAQ,IAAIC,KAAK,CAACC,OAAO,CAACH,KAAK,CAACI,mBAAmB,CAAC,IAAIJ,KAAK,CAACI,mBAAmB,CAACC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAAA;AAC1I,IAAIC,QAAQ,GAAGV,OAAO,CAACE,OAAO,GAAGC,UAAU;AAC3CQ,MAAM,CAACX,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}