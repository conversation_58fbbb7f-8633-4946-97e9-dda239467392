a3ea18af981aa3b4d8ee2faf1cbc65f6
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _UIManager = _interopRequireDefault(require("../../exports/UIManager"));
var TextInputState = {
  _currentlyFocusedNode: null,
  currentlyFocusedField: function currentlyFocusedField() {
    if (document.activeElement !== this._currentlyFocusedNode) {
      this._currentlyFocusedNode = null;
    }
    return this._currentlyFocusedNode;
  },
  focusTextInput: function focusTextInput(textFieldNode) {
    if (textFieldNode !== null) {
      this._currentlyFocusedNode = textFieldNode;
      if (document.activeElement !== textFieldNode) {
        _UIManager.default.focus(textFieldNode);
      }
    }
  },
  blurTextInput: function blurTextInput(textFieldNode) {
    if (textFieldNode !== null) {
      this._currentlyFocusedNode = null;
      if (document.activeElement === textFieldNode) {
        _UIManager.default.blur(textFieldNode);
      }
    }
  }
};
var _default = exports.default = TextInputState;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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