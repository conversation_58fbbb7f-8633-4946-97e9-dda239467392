{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_objectWithoutPropertiesLoose2", "_View", "_react", "_excluded", "RefreshControl", "props", "colors", "enabled", "onRefresh", "progressBackgroundColor", "progressViewOffset", "refreshing", "size", "tintColor", "title", "titleColor", "rest", "createElement", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _react = _interopRequireDefault(require(\"react\"));\nvar _excluded = [\"colors\", \"enabled\", \"onRefresh\", \"progressBackgroundColor\", \"progressViewOffset\", \"refreshing\", \"size\", \"tintColor\", \"title\", \"titleColor\"];\n/**\n * Copyright (c) Nicolas <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nfunction RefreshControl(props) {\n  var colors = props.colors,\n    enabled = props.enabled,\n    onRefresh = props.onRefresh,\n    progressBackgroundColor = props.progressBackgroundColor,\n    progressViewOffset = props.progressViewOffset,\n    refreshing = props.refreshing,\n    size = props.size,\n    tintColor = props.tintColor,\n    title = props.title,\n    titleColor = props.titleColor,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  return /*#__PURE__*/_react.default.createElement(_View.default, rest);\n}\nvar _default = exports.default = RefreshControl;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,8BAA8B,GAAGL,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIK,KAAK,GAAGN,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIO,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,yBAAyB,EAAE,oBAAoB,EAAE,YAAY,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;AAU7J,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACvBC,OAAO,GAAGF,KAAK,CAACE,OAAO;IACvBC,SAAS,GAAGH,KAAK,CAACG,SAAS;IAC3BC,uBAAuB,GAAGJ,KAAK,CAACI,uBAAuB;IACvDC,kBAAkB,GAAGL,KAAK,CAACK,kBAAkB;IAC7CC,UAAU,GAAGN,KAAK,CAACM,UAAU;IAC7BC,IAAI,GAAGP,KAAK,CAACO,IAAI;IACjBC,SAAS,GAAGR,KAAK,CAACQ,SAAS;IAC3BC,KAAK,GAAGT,KAAK,CAACS,KAAK;IACnBC,UAAU,GAAGV,KAAK,CAACU,UAAU;IAC7BC,IAAI,GAAG,CAAC,CAAC,EAAEhB,8BAA8B,CAACH,OAAO,EAAEQ,KAAK,EAAEF,SAAS,CAAC;EACtE,OAAoBD,MAAM,CAACL,OAAO,CAACoB,aAAa,CAAChB,KAAK,CAACJ,OAAO,EAAEmB,IAAI,CAAC;AACvE;AACA,IAAIE,QAAQ,GAAGpB,OAAO,CAACD,OAAO,GAAGO,cAAc;AAC/Ce,MAAM,CAACrB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}