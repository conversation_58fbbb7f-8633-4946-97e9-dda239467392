5e0fa6405c2ff49379324b1e4e4e5c1f
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_1xkfn0j5kn() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useSecurity.ts";
  var hash = "7858c46f9f0dddee39564a9b614b3aff2ef3ce7f";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useSecurity.ts",
    statementMap: {
      "0": {
        start: {
          line: 32,
          column: 19
        },
        end: {
          line: 32,
          column: 28
        }
      },
      "1": {
        start: {
          line: 33,
          column: 44
        },
        end: {
          line: 40,
          column: 4
        }
      },
      "2": {
        start: {
          line: 42,
          column: 46
        },
        end: {
          line: 42,
          column: 57
        }
      },
      "3": {
        start: {
          line: 43,
          column: 44
        },
        end: {
          line: 43,
          column: 59
        }
      },
      "4": {
        start: {
          line: 45,
          column: 52
        },
        end: {
          line: 51,
          column: 3
        }
      },
      "5": {
        start: {
          line: 56,
          column: 29
        },
        end: {
          line: 85,
          column: 8
        }
      },
      "6": {
        start: {
          line: 57,
          column: 4
        },
        end: {
          line: 84,
          column: 5
        }
      },
      "7": {
        start: {
          line: 59,
          column: 23
        },
        end: {
          line: 59,
          column: 50
        }
      },
      "8": {
        start: {
          line: 62,
          column: 33
        },
        end: {
          line: 62,
          column: 77
        }
      },
      "9": {
        start: {
          line: 63,
          column: 28
        },
        end: {
          line: 63,
          column: 89
        }
      },
      "10": {
        start: {
          line: 66,
          column: 6
        },
        end: {
          line: 66,
          column: 43
        }
      },
      "11": {
        start: {
          line: 69,
          column: 6
        },
        end: {
          line: 69,
          column: 45
        }
      },
      "12": {
        start: {
          line: 71,
          column: 6
        },
        end: {
          line: 78,
          column: 10
        }
      },
      "13": {
        start: {
          line: 71,
          column: 32
        },
        end: {
          line: 78,
          column: 7
        }
      },
      "14": {
        start: {
          line: 80,
          column: 6
        },
        end: {
          line: 80,
          column: 29
        }
      },
      "15": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 82,
          column: 62
        }
      },
      "16": {
        start: {
          line: 83,
          column: 6
        },
        end: {
          line: 83,
          column: 29
        }
      },
      "17": {
        start: {
          line: 90,
          column: 37
        },
        end: {
          line: 120,
          column: 90
        }
      },
      "18": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 119,
          column: 5
        }
      },
      "19": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 94,
          column: 7
        }
      },
      "20": {
        start: {
          line: 93,
          column: 8
        },
        end: {
          line: 93,
          column: 66
        }
      },
      "21": {
        start: {
          line: 96,
          column: 21
        },
        end: {
          line: 101,
          column: 8
        }
      },
      "22": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 115,
          column: 7
        }
      },
      "23": {
        start: {
          line: 104,
          column: 8
        },
        end: {
          line: 104,
          column: 29
        }
      },
      "24": {
        start: {
          line: 105,
          column: 8
        },
        end: {
          line: 105,
          column: 29
        }
      },
      "25": {
        start: {
          line: 106,
          column: 8
        },
        end: {
          line: 106,
          column: 20
        }
      },
      "26": {
        start: {
          line: 108,
          column: 8
        },
        end: {
          line: 108,
          column: 44
        }
      },
      "27": {
        start: {
          line: 108,
          column: 34
        },
        end: {
          line: 108,
          column: 42
        }
      },
      "28": {
        start: {
          line: 110,
          column: 8
        },
        end: {
          line: 112,
          column: 9
        }
      },
      "29": {
        start: {
          line: 111,
          column: 10
        },
        end: {
          line: 111,
          column: 42
        }
      },
      "30": {
        start: {
          line: 114,
          column: 8
        },
        end: {
          line: 114,
          column: 21
        }
      },
      "31": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 63
        }
      },
      "32": {
        start: {
          line: 118,
          column: 6
        },
        end: {
          line: 118,
          column: 19
        }
      },
      "33": {
        start: {
          line: 125,
          column: 18
        },
        end: {
          line: 130,
          column: 8
        }
      },
      "34": {
        start: {
          line: 126,
          column: 4
        },
        end: {
          line: 129,
          column: 8
        }
      },
      "35": {
        start: {
          line: 126,
          column: 30
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "36": {
        start: {
          line: 135,
          column: 20
        },
        end: {
          line: 155,
          column: 101
        }
      },
      "37": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 154,
          column: 5
        }
      },
      "38": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 142,
          column: 7
        }
      },
      "39": {
        start: {
          line: 138,
          column: 30
        },
        end: {
          line: 138,
          column: 64
        }
      },
      "40": {
        start: {
          line: 139,
          column: 8
        },
        end: {
          line: 141,
          column: 9
        }
      },
      "41": {
        start: {
          line: 140,
          column: 10
        },
        end: {
          line: 140,
          column: 23
        }
      },
      "42": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 148,
          column: 10
        }
      },
      "43": {
        start: {
          line: 144,
          column: 32
        },
        end: {
          line: 148,
          column: 7
        }
      },
      "44": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 150,
          column: 18
        }
      },
      "45": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 152,
          column: 49
        }
      },
      "46": {
        start: {
          line: 153,
          column: 6
        },
        end: {
          line: 153,
          column: 19
        }
      },
      "47": {
        start: {
          line: 160,
          column: 29
        },
        end: {
          line: 165,
          column: 8
        }
      },
      "48": {
        start: {
          line: 161,
          column: 4
        },
        end: {
          line: 164,
          column: 8
        }
      },
      "49": {
        start: {
          line: 161,
          column: 30
        },
        end: {
          line: 164,
          column: 5
        }
      },
      "50": {
        start: {
          line: 170,
          column: 24
        },
        end: {
          line: 186,
          column: 8
        }
      },
      "51": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 175,
          column: 5
        }
      },
      "52": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 173,
          column: 59
        }
      },
      "53": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 19
        }
      },
      "54": {
        start: {
          line: 178,
          column: 4
        },
        end: {
          line: 185,
          column: 5
        }
      },
      "55": {
        start: {
          line: 180,
          column: 8
        },
        end: {
          line: 180,
          column: 56
        }
      },
      "56": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 48
        }
      },
      "57": {
        start: {
          line: 184,
          column: 8
        },
        end: {
          line: 184,
          column: 20
        }
      },
      "58": {
        start: {
          line: 191,
          column: 22
        },
        end: {
          line: 200,
          column: 35
        }
      },
      "59": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 199,
          column: 5
        }
      },
      "60": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 195,
          column: 9
        }
      },
      "61": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 53
        }
      },
      "62": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 198,
          column: 55
        }
      },
      "63": {
        start: {
          line: 205,
          column: 25
        },
        end: {
          line: 214,
          column: 35
        }
      },
      "64": {
        start: {
          line: 206,
          column: 4
        },
        end: {
          line: 213,
          column: 5
        }
      },
      "65": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 209,
          column: 9
        }
      },
      "66": {
        start: {
          line: 211,
          column: 6
        },
        end: {
          line: 211,
          column: 55
        }
      },
      "67": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 18
        }
      },
      "68": {
        start: {
          line: 219,
          column: 25
        },
        end: {
          line: 239,
          column: 12
        }
      },
      "69": {
        start: {
          line: 220,
          column: 4
        },
        end: {
          line: 220,
          column: 27
        }
      },
      "70": {
        start: {
          line: 220,
          column: 15
        },
        end: {
          line: 220,
          column: 27
        }
      },
      "71": {
        start: {
          line: 222,
          column: 4
        },
        end: {
          line: 238,
          column: 5
        }
      },
      "72": {
        start: {
          line: 223,
          column: 21
        },
        end: {
          line: 223,
          column: 76
        }
      },
      "73": {
        start: {
          line: 225,
          column: 6
        },
        end: {
          line: 232,
          column: 7
        }
      },
      "74": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 230,
          column: 10
        }
      },
      "75": {
        start: {
          line: 231,
          column: 8
        },
        end: {
          line: 231,
          column: 21
        }
      },
      "76": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 18
        }
      },
      "77": {
        start: {
          line: 236,
          column: 6
        },
        end: {
          line: 236,
          column: 55
        }
      },
      "78": {
        start: {
          line: 237,
          column: 6
        },
        end: {
          line: 237,
          column: 18
        }
      },
      "79": {
        start: {
          line: 244,
          column: 29
        },
        end: {
          line: 253,
          column: 12
        }
      },
      "80": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 245,
          column: 27
        }
      },
      "81": {
        start: {
          line: 245,
          column: 15
        },
        end: {
          line: 245,
          column: 27
        }
      },
      "82": {
        start: {
          line: 247,
          column: 4
        },
        end: {
          line: 252,
          column: 5
        }
      },
      "83": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 62
        }
      },
      "84": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 250,
          column: 62
        }
      },
      "85": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 251,
          column: 18
        }
      },
      "86": {
        start: {
          line: 258,
          column: 32
        },
        end: {
          line: 267,
          column: 12
        }
      },
      "87": {
        start: {
          line: 259,
          column: 4
        },
        end: {
          line: 259,
          column: 57
        }
      },
      "88": {
        start: {
          line: 259,
          column: 15
        },
        end: {
          line: 259,
          column: 57
        }
      },
      "89": {
        start: {
          line: 261,
          column: 4
        },
        end: {
          line: 266,
          column: 5
        }
      },
      "90": {
        start: {
          line: 262,
          column: 6
        },
        end: {
          line: 262,
          column: 75
        }
      },
      "91": {
        start: {
          line: 264,
          column: 6
        },
        end: {
          line: 264,
          column: 65
        }
      },
      "92": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 265,
          column: 18
        }
      },
      "93": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 281,
          column: 12
        }
      },
      "94": {
        start: {
          line: 273,
          column: 4
        },
        end: {
          line: 273,
          column: 57
        }
      },
      "95": {
        start: {
          line: 273,
          column: 15
        },
        end: {
          line: 273,
          column: 57
        }
      },
      "96": {
        start: {
          line: 275,
          column: 4
        },
        end: {
          line: 280,
          column: 5
        }
      },
      "97": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 61
        }
      },
      "98": {
        start: {
          line: 278,
          column: 6
        },
        end: {
          line: 278,
          column: 61
        }
      },
      "99": {
        start: {
          line: 279,
          column: 6
        },
        end: {
          line: 279,
          column: 18
        }
      },
      "100": {
        start: {
          line: 286,
          column: 30
        },
        end: {
          line: 295,
          column: 12
        }
      },
      "101": {
        start: {
          line: 287,
          column: 4
        },
        end: {
          line: 287,
          column: 57
        }
      },
      "102": {
        start: {
          line: 287,
          column: 15
        },
        end: {
          line: 287,
          column: 57
        }
      },
      "103": {
        start: {
          line: 289,
          column: 4
        },
        end: {
          line: 294,
          column: 5
        }
      },
      "104": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 69
        }
      },
      "105": {
        start: {
          line: 292,
          column: 6
        },
        end: {
          line: 292,
          column: 63
        }
      },
      "106": {
        start: {
          line: 293,
          column: 6
        },
        end: {
          line: 293,
          column: 18
        }
      },
      "107": {
        start: {
          line: 300,
          column: 30
        },
        end: {
          line: 319,
          column: 8
        }
      },
      "108": {
        start: {
          line: 301,
          column: 4
        },
        end: {
          line: 318,
          column: 5
        }
      },
      "109": {
        start: {
          line: 303,
          column: 28
        },
        end: {
          line: 303,
          column: 71
        }
      },
      "110": {
        start: {
          line: 306,
          column: 6
        },
        end: {
          line: 312,
          column: 7
        }
      },
      "111": {
        start: {
          line: 308,
          column: 8
        },
        end: {
          line: 308,
          column: 29
        }
      },
      "112": {
        start: {
          line: 309,
          column: 13
        },
        end: {
          line: 312,
          column: 7
        }
      },
      "113": {
        start: {
          line: 311,
          column: 8
        },
        end: {
          line: 311,
          column: 29
        }
      },
      "114": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 314,
          column: 27
        }
      },
      "115": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 316,
          column: 60
        }
      },
      "116": {
        start: {
          line: 317,
          column: 6
        },
        end: {
          line: 317,
          column: 19
        }
      },
      "117": {
        start: {
          line: 324,
          column: 33
        },
        end: {
          line: 331,
          column: 8
        }
      },
      "118": {
        start: {
          line: 328,
          column: 4
        },
        end: {
          line: 328,
          column: 54
        }
      },
      "119": {
        start: {
          line: 328,
          column: 40
        },
        end: {
          line: 328,
          column: 54
        }
      },
      "120": {
        start: {
          line: 329,
          column: 4
        },
        end: {
          line: 329,
          column: 56
        }
      },
      "121": {
        start: {
          line: 329,
          column: 40
        },
        end: {
          line: 329,
          column: 56
        }
      },
      "122": {
        start: {
          line: 330,
          column: 4
        },
        end: {
          line: 330,
          column: 17
        }
      },
      "123": {
        start: {
          line: 336,
          column: 34
        },
        end: {
          line: 349,
          column: 15
        }
      },
      "124": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 338,
          column: 14
        }
      },
      "125": {
        start: {
          line: 341,
          column: 4
        },
        end: {
          line: 341,
          column: 39
        }
      },
      "126": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 348,
          column: 6
        }
      },
      "127": {
        start: {
          line: 354,
          column: 2
        },
        end: {
          line: 369,
          column: 118
        }
      },
      "128": {
        start: {
          line: 355,
          column: 4
        },
        end: {
          line: 355,
          column: 62
        }
      },
      "129": {
        start: {
          line: 355,
          column: 55
        },
        end: {
          line: 355,
          column: 62
        }
      },
      "130": {
        start: {
          line: 357,
          column: 26
        },
        end: {
          line: 364,
          column: 5
        }
      },
      "131": {
        start: {
          line: 358,
          column: 18
        },
        end: {
          line: 358,
          column: 28
        }
      },
      "132": {
        start: {
          line: 359,
          column: 36
        },
        end: {
          line: 359,
          column: 89
        }
      },
      "133": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 363,
          column: 7
        }
      },
      "134": {
        start: {
          line: 362,
          column: 8
        },
        end: {
          line: 362,
          column: 18
        }
      },
      "135": {
        start: {
          line: 366,
          column: 21
        },
        end: {
          line: 366,
          column: 54
        }
      },
      "136": {
        start: {
          line: 368,
          column: 4
        },
        end: {
          line: 368,
          column: 41
        }
      },
      "137": {
        start: {
          line: 368,
          column: 17
        },
        end: {
          line: 368,
          column: 40
        }
      },
      "138": {
        start: {
          line: 374,
          column: 2
        },
        end: {
          line: 376,
          column: 27
        }
      },
      "139": {
        start: {
          line: 375,
          column: 4
        },
        end: {
          line: 375,
          column: 25
        }
      },
      "140": {
        start: {
          line: 378,
          column: 2
        },
        end: {
          line: 408,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useSecurity",
        decl: {
          start: {
            line: 31,
            column: 16
          },
          end: {
            line: 31,
            column: 27
          }
        },
        loc: {
          start: {
            line: 31,
            column: 59
          },
          end: {
            line: 409,
            column: 1
          }
        },
        line: 31
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 56,
            column: 41
          },
          end: {
            line: 56,
            column: 42
          }
        },
        loc: {
          start: {
            line: 56,
            column: 53
          },
          end: {
            line: 85,
            column: 3
          }
        },
        line: 56
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 71,
            column: 23
          },
          end: {
            line: 71,
            column: 24
          }
        },
        loc: {
          start: {
            line: 71,
            column: 32
          },
          end: {
            line: 78,
            column: 7
          }
        },
        line: 71
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 90,
            column: 49
          },
          end: {
            line: 90,
            column: 50
          }
        },
        loc: {
          start: {
            line: 90,
            column: 79
          },
          end: {
            line: 120,
            column: 3
          }
        },
        line: 90
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 108,
            column: 26
          },
          end: {
            line: 108,
            column: 27
          }
        },
        loc: {
          start: {
            line: 108,
            column: 34
          },
          end: {
            line: 108,
            column: 42
          }
        },
        line: 108
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 125,
            column: 30
          },
          end: {
            line: 125,
            column: 31
          }
        },
        loc: {
          start: {
            line: 125,
            column: 36
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 125
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 126,
            column: 21
          },
          end: {
            line: 126,
            column: 22
          }
        },
        loc: {
          start: {
            line: 126,
            column: 30
          },
          end: {
            line: 129,
            column: 5
          }
        },
        line: 126
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 135,
            column: 32
          },
          end: {
            line: 135,
            column: 33
          }
        },
        loc: {
          start: {
            line: 135,
            column: 62
          },
          end: {
            line: 155,
            column: 3
          }
        },
        line: 135
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 144,
            column: 23
          },
          end: {
            line: 144,
            column: 24
          }
        },
        loc: {
          start: {
            line: 144,
            column: 32
          },
          end: {
            line: 148,
            column: 7
          }
        },
        line: 144
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 160,
            column: 41
          },
          end: {
            line: 160,
            column: 42
          }
        },
        loc: {
          start: {
            line: 160,
            column: 47
          },
          end: {
            line: 165,
            column: 3
          }
        },
        line: 160
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 161,
            column: 21
          },
          end: {
            line: 161,
            column: 22
          }
        },
        loc: {
          start: {
            line: 161,
            column: 30
          },
          end: {
            line: 164,
            column: 5
          }
        },
        line: 161
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 170,
            column: 36
          },
          end: {
            line: 170,
            column: 37
          }
        },
        loc: {
          start: {
            line: 170,
            column: 105
          },
          end: {
            line: 186,
            column: 3
          }
        },
        line: 170
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 191,
            column: 34
          },
          end: {
            line: 191,
            column: 35
          }
        },
        loc: {
          start: {
            line: 191,
            column: 87
          },
          end: {
            line: 200,
            column: 3
          }
        },
        line: 191
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 205,
            column: 37
          },
          end: {
            line: 205,
            column: 38
          }
        },
        loc: {
          start: {
            line: 205,
            column: 84
          },
          end: {
            line: 214,
            column: 3
          }
        },
        line: 205
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 219,
            column: 37
          },
          end: {
            line: 219,
            column: 38
          }
        },
        loc: {
          start: {
            line: 219,
            column: 83
          },
          end: {
            line: 239,
            column: 3
          }
        },
        line: 219
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 244,
            column: 41
          },
          end: {
            line: 244,
            column: 42
          }
        },
        loc: {
          start: {
            line: 244,
            column: 53
          },
          end: {
            line: 253,
            column: 3
          }
        },
        line: 244
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 258,
            column: 44
          },
          end: {
            line: 258,
            column: 45
          }
        },
        loc: {
          start: {
            line: 258,
            column: 69
          },
          end: {
            line: 267,
            column: 3
          }
        },
        line: 258
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 272,
            column: 40
          },
          end: {
            line: 272,
            column: 41
          }
        },
        loc: {
          start: {
            line: 272,
            column: 52
          },
          end: {
            line: 281,
            column: 3
          }
        },
        line: 272
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 286,
            column: 42
          },
          end: {
            line: 286,
            column: 43
          }
        },
        loc: {
          start: {
            line: 286,
            column: 95
          },
          end: {
            line: 295,
            column: 3
          }
        },
        line: 286
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 300,
            column: 42
          },
          end: {
            line: 300,
            column: 43
          }
        },
        loc: {
          start: {
            line: 300,
            column: 72
          },
          end: {
            line: 319,
            column: 3
          }
        },
        line: 300
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 324,
            column: 45
          },
          end: {
            line: 324,
            column: 46
          }
        },
        loc: {
          start: {
            line: 327,
            column: 34
          },
          end: {
            line: 331,
            column: 3
          }
        },
        line: 327
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 336,
            column: 46
          },
          end: {
            line: 336,
            column: 47
          }
        },
        loc: {
          start: {
            line: 336,
            column: 58
          },
          end: {
            line: 349,
            column: 3
          }
        },
        line: 336
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 354,
            column: 12
          },
          end: {
            line: 354,
            column: 13
          }
        },
        loc: {
          start: {
            line: 354,
            column: 18
          },
          end: {
            line: 369,
            column: 3
          }
        },
        line: 354
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 357,
            column: 26
          },
          end: {
            line: 357,
            column: 27
          }
        },
        loc: {
          start: {
            line: 357,
            column: 32
          },
          end: {
            line: 364,
            column: 5
          }
        },
        line: 357
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 368,
            column: 11
          },
          end: {
            line: 368,
            column: 12
          }
        },
        loc: {
          start: {
            line: 368,
            column: 17
          },
          end: {
            line: 368,
            column: 40
          }
        },
        line: 368
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 374,
            column: 12
          },
          end: {
            line: 374,
            column: 13
          }
        },
        loc: {
          start: {
            line: 374,
            column: 18
          },
          end: {
            line: 376,
            column: 3
          }
        },
        line: 374
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 31,
            column: 28
          },
          end: {
            line: 31,
            column: 57
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 31,
            column: 55
          },
          end: {
            line: 31,
            column: 57
          }
        }],
        line: 31
      },
      "1": {
        loc: {
          start: {
            line: 92,
            column: 6
          },
          end: {
            line: 94,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 92,
            column: 6
          },
          end: {
            line: 94,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 92
      },
      "2": {
        loc: {
          start: {
            line: 103,
            column: 6
          },
          end: {
            line: 115,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 103,
            column: 6
          },
          end: {
            line: 115,
            column: 7
          }
        }, {
          start: {
            line: 107,
            column: 13
          },
          end: {
            line: 115,
            column: 7
          }
        }],
        line: 103
      },
      "3": {
        loc: {
          start: {
            line: 110,
            column: 8
          },
          end: {
            line: 112,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 8
          },
          end: {
            line: 112,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "4": {
        loc: {
          start: {
            line: 137,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 137,
            column: 6
          },
          end: {
            line: 142,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 137
      },
      "5": {
        loc: {
          start: {
            line: 137,
            column: 10
          },
          end: {
            line: 137,
            column: 77
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 137,
            column: 10
          },
          end: {
            line: 137,
            column: 41
          }
        }, {
          start: {
            line: 137,
            column: 45
          },
          end: {
            line: 137,
            column: 77
          }
        }],
        line: 137
      },
      "6": {
        loc: {
          start: {
            line: 139,
            column: 8
          },
          end: {
            line: 141,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 139,
            column: 8
          },
          end: {
            line: 141,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 139
      },
      "7": {
        loc: {
          start: {
            line: 170,
            column: 52
          },
          end: {
            line: 170,
            column: 91
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 170,
            column: 85
          },
          end: {
            line: 170,
            column: 91
          }
        }],
        line: 170
      },
      "8": {
        loc: {
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 175,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 172,
            column: 4
          },
          end: {
            line: 175,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 172
      },
      "9": {
        loc: {
          start: {
            line: 178,
            column: 4
          },
          end: {
            line: 185,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 179,
            column: 6
          },
          end: {
            line: 180,
            column: 56
          }
        }, {
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 182,
            column: 48
          }
        }, {
          start: {
            line: 183,
            column: 6
          },
          end: {
            line: 184,
            column: 20
          }
        }],
        line: 178
      },
      "10": {
        loc: {
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 220,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 220,
            column: 4
          },
          end: {
            line: 220,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 220
      },
      "11": {
        loc: {
          start: {
            line: 225,
            column: 6
          },
          end: {
            line: 232,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 225,
            column: 6
          },
          end: {
            line: 232,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 225
      },
      "12": {
        loc: {
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 245,
            column: 27
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 245,
            column: 27
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "13": {
        loc: {
          start: {
            line: 259,
            column: 4
          },
          end: {
            line: 259,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 259,
            column: 4
          },
          end: {
            line: 259,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 259
      },
      "14": {
        loc: {
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 273,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 273,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      },
      "15": {
        loc: {
          start: {
            line: 286,
            column: 49
          },
          end: {
            line: 286,
            column: 90
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 286,
            column: 80
          },
          end: {
            line: 286,
            column: 90
          }
        }],
        line: 286
      },
      "16": {
        loc: {
          start: {
            line: 287,
            column: 4
          },
          end: {
            line: 287,
            column: 57
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 4
          },
          end: {
            line: 287,
            column: 57
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 287
      },
      "17": {
        loc: {
          start: {
            line: 306,
            column: 6
          },
          end: {
            line: 312,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 306,
            column: 6
          },
          end: {
            line: 312,
            column: 7
          }
        }, {
          start: {
            line: 309,
            column: 13
          },
          end: {
            line: 312,
            column: 7
          }
        }],
        line: 306
      },
      "18": {
        loc: {
          start: {
            line: 309,
            column: 13
          },
          end: {
            line: 312,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 309,
            column: 13
          },
          end: {
            line: 312,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 309
      },
      "19": {
        loc: {
          start: {
            line: 328,
            column: 4
          },
          end: {
            line: 328,
            column: 54
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 328,
            column: 4
          },
          end: {
            line: 328,
            column: 54
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 328
      },
      "20": {
        loc: {
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 328,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 328,
            column: 8
          },
          end: {
            line: 328,
            column: 16
          }
        }, {
          start: {
            line: 328,
            column: 20
          },
          end: {
            line: 328,
            column: 38
          }
        }],
        line: 328
      },
      "21": {
        loc: {
          start: {
            line: 329,
            column: 4
          },
          end: {
            line: 329,
            column: 56
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 329,
            column: 4
          },
          end: {
            line: 329,
            column: 56
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 329
      },
      "22": {
        loc: {
          start: {
            line: 329,
            column: 8
          },
          end: {
            line: 329,
            column: 38
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 329,
            column: 8
          },
          end: {
            line: 329,
            column: 16
          }
        }, {
          start: {
            line: 329,
            column: 20
          },
          end: {
            line: 329,
            column: 38
          }
        }],
        line: 329
      },
      "23": {
        loc: {
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 355,
            column: 62
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 355,
            column: 4
          },
          end: {
            line: 355,
            column: 62
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 355
      },
      "24": {
        loc: {
          start: {
            line: 355,
            column: 8
          },
          end: {
            line: 355,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 355,
            column: 8
          },
          end: {
            line: 355,
            column: 22
          }
        }, {
          start: {
            line: 355,
            column: 26
          },
          end: {
            line: 355,
            column: 53
          }
        }],
        line: 355
      },
      "25": {
        loc: {
          start: {
            line: 361,
            column: 6
          },
          end: {
            line: 363,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 361,
            column: 6
          },
          end: {
            line: 363,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 361
      },
      "26": {
        loc: {
          start: {
            line: 361,
            column: 10
          },
          end: {
            line: 361,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 361,
            column: 10
          },
          end: {
            line: 361,
            column: 65
          }
        }, {
          start: {
            line: 361,
            column: 69
          },
          end: {
            line: 361,
            column: 95
          }
        }],
        line: 361
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "7858c46f9f0dddee39564a9b614b3aff2ef3ce7f"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1xkfn0j5kn = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1xkfn0j5kn();
import { useState, useEffect, useCallback } from 'react';
import { Alert, Platform } from 'react-native';
import * as LocalAuthentication from 'expo-local-authentication';
import { encryptionService } from "../services/encryption";
import { rateLimitingService } from "../services/rateLimiting";
import { privacyService } from "../services/privacy";
import { ValidationUtils } from "../utils/validation";
import { useAuth } from "./useAuth";
export function useSecurity() {
  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1xkfn0j5kn().b[0][0]++, {});
  cov_1xkfn0j5kn().f[0]++;
  var _ref = (cov_1xkfn0j5kn().s[0]++, useAuth()),
    user = _ref.user;
  var _ref2 = (cov_1xkfn0j5kn().s[1]++, useState({
      isSecureDevice: false,
      biometricAvailable: false,
      biometricType: [],
      isAppLocked: false,
      lastActivity: null,
      securityLevel: 'medium'
    })),
    _ref3 = _slicedToArray(_ref2, 2),
    securityState = _ref3[0],
    setSecurityState = _ref3[1];
  var _ref4 = (cov_1xkfn0j5kn().s[2]++, useState(0)),
    _ref5 = _slicedToArray(_ref4, 2),
    failedAttempts = _ref5[0],
    setFailedAttempts = _ref5[1];
  var _ref6 = (cov_1xkfn0j5kn().s[3]++, useState(false)),
    _ref7 = _slicedToArray(_ref6, 2),
    isInitialized = _ref7[0],
    setIsInitialized = _ref7[1];
  var defaultOptions = (cov_1xkfn0j5kn().s[4]++, Object.assign({
    requireBiometric: false,
    autoLockTimeout: 5 * 60 * 1000,
    maxFailedAttempts: 3,
    enableTamperDetection: true
  }, options));
  var initializeSecurity = (cov_1xkfn0j5kn().s[5]++, useCallback(_asyncToGenerator(function* () {
    cov_1xkfn0j5kn().f[1]++;
    cov_1xkfn0j5kn().s[6]++;
    try {
      var isSecure = (cov_1xkfn0j5kn().s[7]++, yield checkDeviceSecurity());
      var biometricAvailable = (cov_1xkfn0j5kn().s[8]++, yield LocalAuthentication.hasHardwareAsync());
      var biometricType = (cov_1xkfn0j5kn().s[9]++, yield LocalAuthentication.supportedAuthenticationTypesAsync());
      cov_1xkfn0j5kn().s[10]++;
      yield encryptionService.initialize();
      cov_1xkfn0j5kn().s[11]++;
      yield rateLimitingService.initialize();
      cov_1xkfn0j5kn().s[12]++;
      setSecurityState(function (prev) {
        cov_1xkfn0j5kn().f[2]++;
        cov_1xkfn0j5kn().s[13]++;
        return Object.assign({}, prev, {
          isSecureDevice: isSecure,
          biometricAvailable: biometricAvailable,
          biometricType: biometricType,
          lastActivity: new Date(),
          securityLevel: determineSecurityLevel(isSecure, biometricAvailable)
        });
      });
      cov_1xkfn0j5kn().s[14]++;
      setIsInitialized(true);
    } catch (error) {
      cov_1xkfn0j5kn().s[15]++;
      console.error('Security initialization failed:', error);
      cov_1xkfn0j5kn().s[16]++;
      setIsInitialized(true);
    }
  }), []));
  var authenticateWithBiometrics = (cov_1xkfn0j5kn().s[17]++, useCallback(_asyncToGenerator(function* () {
    cov_1xkfn0j5kn().f[3]++;
    cov_1xkfn0j5kn().s[18]++;
    try {
      cov_1xkfn0j5kn().s[19]++;
      if (!securityState.biometricAvailable) {
        cov_1xkfn0j5kn().b[1][0]++;
        cov_1xkfn0j5kn().s[20]++;
        throw new Error('Biometric authentication not available');
      } else {
        cov_1xkfn0j5kn().b[1][1]++;
      }
      var result = (cov_1xkfn0j5kn().s[21]++, yield LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access AceMind',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use Passcode',
        disableDeviceFallback: false
      }));
      cov_1xkfn0j5kn().s[22]++;
      if (result.success) {
        cov_1xkfn0j5kn().b[2][0]++;
        cov_1xkfn0j5kn().s[23]++;
        setFailedAttempts(0);
        cov_1xkfn0j5kn().s[24]++;
        updateLastActivity();
        cov_1xkfn0j5kn().s[25]++;
        return true;
      } else {
        cov_1xkfn0j5kn().b[2][1]++;
        cov_1xkfn0j5kn().s[26]++;
        setFailedAttempts(function (prev) {
          cov_1xkfn0j5kn().f[4]++;
          cov_1xkfn0j5kn().s[27]++;
          return prev + 1;
        });
        cov_1xkfn0j5kn().s[28]++;
        if (failedAttempts >= defaultOptions.maxFailedAttempts - 1) {
          cov_1xkfn0j5kn().b[3][0]++;
          cov_1xkfn0j5kn().s[29]++;
          yield handleMaxFailedAttempts();
        } else {
          cov_1xkfn0j5kn().b[3][1]++;
        }
        cov_1xkfn0j5kn().s[30]++;
        return false;
      }
    } catch (error) {
      cov_1xkfn0j5kn().s[31]++;
      console.error('Biometric authentication failed:', error);
      cov_1xkfn0j5kn().s[32]++;
      return false;
    }
  }), [securityState.biometricAvailable, failedAttempts, defaultOptions.maxFailedAttempts]));
  var lockApp = (cov_1xkfn0j5kn().s[33]++, useCallback(function () {
    cov_1xkfn0j5kn().f[5]++;
    cov_1xkfn0j5kn().s[34]++;
    setSecurityState(function (prev) {
      cov_1xkfn0j5kn().f[6]++;
      cov_1xkfn0j5kn().s[35]++;
      return Object.assign({}, prev, {
        isAppLocked: true
      });
    });
  }, []));
  var unlockApp = (cov_1xkfn0j5kn().s[36]++, useCallback(_asyncToGenerator(function* () {
    cov_1xkfn0j5kn().f[7]++;
    cov_1xkfn0j5kn().s[37]++;
    try {
      cov_1xkfn0j5kn().s[38]++;
      if ((cov_1xkfn0j5kn().b[5][0]++, defaultOptions.requireBiometric) && (cov_1xkfn0j5kn().b[5][1]++, securityState.biometricAvailable)) {
        cov_1xkfn0j5kn().b[4][0]++;
        var authenticated = (cov_1xkfn0j5kn().s[39]++, yield authenticateWithBiometrics());
        cov_1xkfn0j5kn().s[40]++;
        if (!authenticated) {
          cov_1xkfn0j5kn().b[6][0]++;
          cov_1xkfn0j5kn().s[41]++;
          return false;
        } else {
          cov_1xkfn0j5kn().b[6][1]++;
        }
      } else {
        cov_1xkfn0j5kn().b[4][1]++;
      }
      cov_1xkfn0j5kn().s[42]++;
      setSecurityState(function (prev) {
        cov_1xkfn0j5kn().f[8]++;
        cov_1xkfn0j5kn().s[43]++;
        return Object.assign({}, prev, {
          isAppLocked: false,
          lastActivity: new Date()
        });
      });
      cov_1xkfn0j5kn().s[44]++;
      return true;
    } catch (error) {
      cov_1xkfn0j5kn().s[45]++;
      console.error('App unlock failed:', error);
      cov_1xkfn0j5kn().s[46]++;
      return false;
    }
  }), [defaultOptions.requireBiometric, securityState.biometricAvailable, authenticateWithBiometrics]));
  var updateLastActivity = (cov_1xkfn0j5kn().s[47]++, useCallback(function () {
    cov_1xkfn0j5kn().f[9]++;
    cov_1xkfn0j5kn().s[48]++;
    setSecurityState(function (prev) {
      cov_1xkfn0j5kn().f[10]++;
      cov_1xkfn0j5kn().s[49]++;
      return Object.assign({}, prev, {
        lastActivity: new Date()
      });
    });
  }, []));
  var validateInput = (cov_1xkfn0j5kn().s[50]++, useCallback(function (input) {
    var type = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1xkfn0j5kn().b[7][0]++, 'text');
    cov_1xkfn0j5kn().f[11]++;
    cov_1xkfn0j5kn().s[51]++;
    if (ValidationUtils.hasInjectionPatterns(input)) {
      cov_1xkfn0j5kn().b[8][0]++;
      cov_1xkfn0j5kn().s[52]++;
      console.warn('Potentially malicious input detected');
      cov_1xkfn0j5kn().s[53]++;
      return false;
    } else {
      cov_1xkfn0j5kn().b[8][1]++;
    }
    cov_1xkfn0j5kn().s[54]++;
    switch (type) {
      case 'email':
        cov_1xkfn0j5kn().b[9][0]++;
        cov_1xkfn0j5kn().s[55]++;
        return ValidationUtils.isEmailDomainSafe(input);
      case 'url':
        cov_1xkfn0j5kn().b[9][1]++;
        cov_1xkfn0j5kn().s[56]++;
        return ValidationUtils.isUrlSafe(input);
      default:
        cov_1xkfn0j5kn().b[9][2]++;
        cov_1xkfn0j5kn().s[57]++;
        return true;
    }
  }, []));
  var secureStore = (cov_1xkfn0j5kn().s[58]++, useCallback(function () {
    var _ref1 = _asyncToGenerator(function* (key, value) {
      cov_1xkfn0j5kn().f[12]++;
      cov_1xkfn0j5kn().s[59]++;
      try {
        cov_1xkfn0j5kn().s[60]++;
        yield encryptionService.secureStore(key, value, {
          requireAuthentication: securityState.securityLevel === 'high'
        });
      } catch (error) {
        cov_1xkfn0j5kn().s[61]++;
        console.error('Secure storage failed:', error);
        cov_1xkfn0j5kn().s[62]++;
        throw new Error('Failed to store data securely');
      }
    });
    return function (_x, _x2) {
      return _ref1.apply(this, arguments);
    };
  }(), [securityState.securityLevel]));
  var secureRetrieve = (cov_1xkfn0j5kn().s[63]++, useCallback(function () {
    var _ref10 = _asyncToGenerator(function* (key) {
      cov_1xkfn0j5kn().f[13]++;
      cov_1xkfn0j5kn().s[64]++;
      try {
        cov_1xkfn0j5kn().s[65]++;
        return yield encryptionService.secureRetrieve(key, {
          requireAuthentication: securityState.securityLevel === 'high'
        });
      } catch (error) {
        cov_1xkfn0j5kn().s[66]++;
        console.error('Secure retrieval failed:', error);
        cov_1xkfn0j5kn().s[67]++;
        return null;
      }
    });
    return function (_x3) {
      return _ref10.apply(this, arguments);
    };
  }(), [securityState.securityLevel]));
  var checkRateLimit = (cov_1xkfn0j5kn().s[68]++, useCallback(function () {
    var _ref11 = _asyncToGenerator(function* (endpoint) {
      cov_1xkfn0j5kn().f[14]++;
      cov_1xkfn0j5kn().s[69]++;
      if (!user) {
        cov_1xkfn0j5kn().b[10][0]++;
        cov_1xkfn0j5kn().s[70]++;
        return true;
      } else {
        cov_1xkfn0j5kn().b[10][1]++;
      }
      cov_1xkfn0j5kn().s[71]++;
      try {
        var result = (cov_1xkfn0j5kn().s[72]++, yield rateLimitingService.checkLimit(endpoint, user.id));
        cov_1xkfn0j5kn().s[73]++;
        if (!result.allowed) {
          cov_1xkfn0j5kn().b[11][0]++;
          cov_1xkfn0j5kn().s[74]++;
          Alert.alert('Rate Limit Exceeded', `Too many requests. Please try again in ${result.retryAfter} seconds.`, [{
            text: 'OK'
          }]);
          cov_1xkfn0j5kn().s[75]++;
          return false;
        } else {
          cov_1xkfn0j5kn().b[11][1]++;
        }
        cov_1xkfn0j5kn().s[76]++;
        return true;
      } catch (error) {
        cov_1xkfn0j5kn().s[77]++;
        console.error('Rate limit check failed:', error);
        cov_1xkfn0j5kn().s[78]++;
        return true;
      }
    });
    return function (_x4) {
      return _ref11.apply(this, arguments);
    };
  }(), [user]));
  var getPrivacySettings = (cov_1xkfn0j5kn().s[79]++, useCallback(_asyncToGenerator(function* () {
    cov_1xkfn0j5kn().f[15]++;
    cov_1xkfn0j5kn().s[80]++;
    if (!user) {
      cov_1xkfn0j5kn().b[12][0]++;
      cov_1xkfn0j5kn().s[81]++;
      return null;
    } else {
      cov_1xkfn0j5kn().b[12][1]++;
    }
    cov_1xkfn0j5kn().s[82]++;
    try {
      cov_1xkfn0j5kn().s[83]++;
      return yield privacyService.getPrivacySettings(user.id);
    } catch (error) {
      cov_1xkfn0j5kn().s[84]++;
      console.error('Failed to get privacy settings:', error);
      cov_1xkfn0j5kn().s[85]++;
      return null;
    }
  }), [user]));
  var updatePrivacySettings = (cov_1xkfn0j5kn().s[86]++, useCallback(function () {
    var _ref13 = _asyncToGenerator(function* (settings) {
      cov_1xkfn0j5kn().f[16]++;
      cov_1xkfn0j5kn().s[87]++;
      if (!user) {
        cov_1xkfn0j5kn().b[13][0]++;
        cov_1xkfn0j5kn().s[88]++;
        throw new Error('User not authenticated');
      } else {
        cov_1xkfn0j5kn().b[13][1]++;
      }
      cov_1xkfn0j5kn().s[89]++;
      try {
        cov_1xkfn0j5kn().s[90]++;
        return yield privacyService.updatePrivacySettings(user.id, settings);
      } catch (error) {
        cov_1xkfn0j5kn().s[91]++;
        console.error('Failed to update privacy settings:', error);
        cov_1xkfn0j5kn().s[92]++;
        throw error;
      }
    });
    return function (_x5) {
      return _ref13.apply(this, arguments);
    };
  }(), [user]));
  var requestDataExport = (cov_1xkfn0j5kn().s[93]++, useCallback(_asyncToGenerator(function* () {
    cov_1xkfn0j5kn().f[17]++;
    cov_1xkfn0j5kn().s[94]++;
    if (!user) {
      cov_1xkfn0j5kn().b[14][0]++;
      cov_1xkfn0j5kn().s[95]++;
      throw new Error('User not authenticated');
    } else {
      cov_1xkfn0j5kn().b[14][1]++;
    }
    cov_1xkfn0j5kn().s[96]++;
    try {
      cov_1xkfn0j5kn().s[97]++;
      return yield privacyService.requestDataExport(user.id);
    } catch (error) {
      cov_1xkfn0j5kn().s[98]++;
      console.error('Failed to request data export:', error);
      cov_1xkfn0j5kn().s[99]++;
      throw error;
    }
  }), [user]));
  var requestDataDeletion = (cov_1xkfn0j5kn().s[100]++, useCallback(_asyncToGenerator(function* () {
    var type = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : (cov_1xkfn0j5kn().b[15][0]++, 'complete');
    cov_1xkfn0j5kn().f[18]++;
    cov_1xkfn0j5kn().s[101]++;
    if (!user) {
      cov_1xkfn0j5kn().b[16][0]++;
      cov_1xkfn0j5kn().s[102]++;
      throw new Error('User not authenticated');
    } else {
      cov_1xkfn0j5kn().b[16][1]++;
    }
    cov_1xkfn0j5kn().s[103]++;
    try {
      cov_1xkfn0j5kn().s[104]++;
      return yield privacyService.requestDataDeletion(user.id, type);
    } catch (error) {
      cov_1xkfn0j5kn().s[105]++;
      console.error('Failed to request data deletion:', error);
      cov_1xkfn0j5kn().s[106]++;
      throw error;
    }
  }), [user]));
  var checkDeviceSecurity = (cov_1xkfn0j5kn().s[107]++, useCallback(_asyncToGenerator(function* () {
    cov_1xkfn0j5kn().f[19]++;
    cov_1xkfn0j5kn().s[108]++;
    try {
      var hasScreenLock = (cov_1xkfn0j5kn().s[109]++, yield LocalAuthentication.isEnrolledAsync());
      cov_1xkfn0j5kn().s[110]++;
      if (Platform.OS === 'ios') {
        cov_1xkfn0j5kn().b[17][0]++;
        cov_1xkfn0j5kn().s[111]++;
        return hasScreenLock;
      } else {
        cov_1xkfn0j5kn().b[17][1]++;
        cov_1xkfn0j5kn().s[112]++;
        if (Platform.OS === 'android') {
          cov_1xkfn0j5kn().b[18][0]++;
          cov_1xkfn0j5kn().s[113]++;
          return hasScreenLock;
        } else {
          cov_1xkfn0j5kn().b[18][1]++;
        }
      }
      cov_1xkfn0j5kn().s[114]++;
      return hasScreenLock;
    } catch (error) {
      cov_1xkfn0j5kn().s[115]++;
      console.error('Device security check failed:', error);
      cov_1xkfn0j5kn().s[116]++;
      return false;
    }
  }), []));
  var determineSecurityLevel = (cov_1xkfn0j5kn().s[117]++, useCallback(function (isSecure, biometricAvailable) {
    cov_1xkfn0j5kn().f[20]++;
    cov_1xkfn0j5kn().s[118]++;
    if ((cov_1xkfn0j5kn().b[20][0]++, isSecure) && (cov_1xkfn0j5kn().b[20][1]++, biometricAvailable)) {
      cov_1xkfn0j5kn().b[19][0]++;
      cov_1xkfn0j5kn().s[119]++;
      return 'high';
    } else {
      cov_1xkfn0j5kn().b[19][1]++;
    }
    cov_1xkfn0j5kn().s[120]++;
    if ((cov_1xkfn0j5kn().b[22][0]++, isSecure) || (cov_1xkfn0j5kn().b[22][1]++, biometricAvailable)) {
      cov_1xkfn0j5kn().b[21][0]++;
      cov_1xkfn0j5kn().s[121]++;
      return 'medium';
    } else {
      cov_1xkfn0j5kn().b[21][1]++;
    }
    cov_1xkfn0j5kn().s[122]++;
    return 'low';
  }, []));
  var handleMaxFailedAttempts = (cov_1xkfn0j5kn().s[123]++, useCallback(_asyncToGenerator(function* () {
    cov_1xkfn0j5kn().f[21]++;
    cov_1xkfn0j5kn().s[124]++;
    lockApp();
    cov_1xkfn0j5kn().s[125]++;
    yield encryptionService.clearAll();
    cov_1xkfn0j5kn().s[126]++;
    Alert.alert('Security Alert', 'Too many failed authentication attempts. The app has been locked for security.', [{
      text: 'OK'
    }]);
  }), [lockApp]));
  cov_1xkfn0j5kn().s[127]++;
  useEffect(function () {
    cov_1xkfn0j5kn().f[22]++;
    cov_1xkfn0j5kn().s[128]++;
    if ((cov_1xkfn0j5kn().b[24][0]++, !isInitialized) || (cov_1xkfn0j5kn().b[24][1]++, !securityState.lastActivity)) {
      cov_1xkfn0j5kn().b[23][0]++;
      cov_1xkfn0j5kn().s[129]++;
      return;
    } else {
      cov_1xkfn0j5kn().b[23][1]++;
    }
    cov_1xkfn0j5kn().s[130]++;
    var checkAutoLock = function checkAutoLock() {
      cov_1xkfn0j5kn().f[23]++;
      var now = (cov_1xkfn0j5kn().s[131]++, new Date());
      var timeSinceLastActivity = (cov_1xkfn0j5kn().s[132]++, now.getTime() - securityState.lastActivity.getTime());
      cov_1xkfn0j5kn().s[133]++;
      if ((cov_1xkfn0j5kn().b[26][0]++, timeSinceLastActivity >= defaultOptions.autoLockTimeout) && (cov_1xkfn0j5kn().b[26][1]++, !securityState.isAppLocked)) {
        cov_1xkfn0j5kn().b[25][0]++;
        cov_1xkfn0j5kn().s[134]++;
        lockApp();
      } else {
        cov_1xkfn0j5kn().b[25][1]++;
      }
    };
    var interval = (cov_1xkfn0j5kn().s[135]++, setInterval(checkAutoLock, 30000));
    cov_1xkfn0j5kn().s[136]++;
    return function () {
      cov_1xkfn0j5kn().f[24]++;
      cov_1xkfn0j5kn().s[137]++;
      return clearInterval(interval);
    };
  }, [isInitialized, securityState.lastActivity, securityState.isAppLocked, defaultOptions.autoLockTimeout, lockApp]);
  cov_1xkfn0j5kn().s[138]++;
  useEffect(function () {
    cov_1xkfn0j5kn().f[25]++;
    cov_1xkfn0j5kn().s[139]++;
    initializeSecurity();
  }, [initializeSecurity]);
  cov_1xkfn0j5kn().s[140]++;
  return {
    securityState: securityState,
    isInitialized: isInitialized,
    failedAttempts: failedAttempts,
    authenticateWithBiometrics: authenticateWithBiometrics,
    lockApp: lockApp,
    unlockApp: unlockApp,
    updateLastActivity: updateLastActivity,
    validateInput: validateInput,
    secureStore: secureStore,
    secureRetrieve: secureRetrieve,
    checkRateLimit: checkRateLimit,
    getPrivacySettings: getPrivacySettings,
    updatePrivacySettings: updatePrivacySettings,
    requestDataExport: requestDataExport,
    requestDataDeletion: requestDataDeletion,
    checkDeviceSecurity: checkDeviceSecurity
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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