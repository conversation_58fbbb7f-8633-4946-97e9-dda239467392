{"version": 3, "names": ["_interopRequireDefault2", "require", "_classCallCheck2", "_createClass2", "_possibleConstructorReturn2", "_getPrototypeOf2", "_get2", "_inherits2", "_callSuper", "t", "o", "e", "default", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_interopRequireDefault", "exports", "__esModule", "_objectSpread2", "_AnimatedEvent", "_AnimatedNode", "_AnimatedStyle", "_NativeAnimatedHelper", "_invariant", "AnimatedProps", "_AnimatedNode$default", "props", "callback", "_this", "style", "_props", "_callback", "__attach", "key", "value", "__getValue", "__isNative", "AnimatedEvent", "__<PERSON><PERSON><PERSON><PERSON>", "__getAnimatedValue", "__add<PERSON><PERSON>d", "__detach", "_animated<PERSON>iew", "__disconnectAnimatedView", "__remove<PERSON><PERSON>d", "update", "__makeNative", "__connectAnimatedView", "setNativeView", "animatedView", "nativeViewTag", "API", "connectAnimatedNodeToView", "__getNativeTag", "disconnectAnimatedNodeFromView", "__restore<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "restoreDefaultValues", "__getNativeConfig", "propsConfig", "<PERSON><PERSON><PERSON>", "type", "_default", "module"], "sources": ["AnimatedProps.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _AnimatedEvent = require(\"../AnimatedEvent\");\nvar _AnimatedNode = _interopRequireDefault(require(\"./AnimatedNode\"));\nvar _AnimatedStyle = _interopRequireDefault(require(\"./AnimatedStyle\"));\nvar _NativeAnimatedHelper = _interopRequireDefault(require(\"../NativeAnimatedHelper\"));\nvar _invariant = _interopRequireDefault(require(\"fbjs/lib/invariant\"));\nclass AnimatedProps extends _AnimatedNode.default {\n  constructor(props, callback) {\n    super();\n    if (props.style) {\n      props = (0, _objectSpread2.default)((0, _objectSpread2.default)({}, props), {}, {\n        style: new _AnimatedStyle.default(props.style)\n      });\n    }\n    this._props = props;\n    this._callback = callback;\n    this.__attach();\n  }\n  __getValue() {\n    var props = {};\n    for (var key in this._props) {\n      var value = this._props[key];\n      if (value instanceof _AnimatedNode.default) {\n        if (!value.__isNative || value instanceof _AnimatedStyle.default) {\n          // We cannot use value of natively driven nodes this way as the value we have access from\n          // JS may not be up to date.\n          props[key] = value.__getValue();\n        }\n      } else if (value instanceof _AnimatedEvent.AnimatedEvent) {\n        props[key] = value.__getHandler();\n      } else {\n        props[key] = value;\n      }\n    }\n    return props;\n  }\n  __getAnimatedValue() {\n    var props = {};\n    for (var key in this._props) {\n      var value = this._props[key];\n      if (value instanceof _AnimatedNode.default) {\n        props[key] = value.__getAnimatedValue();\n      }\n    }\n    return props;\n  }\n  __attach() {\n    for (var key in this._props) {\n      var value = this._props[key];\n      if (value instanceof _AnimatedNode.default) {\n        value.__addChild(this);\n      }\n    }\n  }\n  __detach() {\n    if (this.__isNative && this._animatedView) {\n      this.__disconnectAnimatedView();\n    }\n    for (var key in this._props) {\n      var value = this._props[key];\n      if (value instanceof _AnimatedNode.default) {\n        value.__removeChild(this);\n      }\n    }\n    super.__detach();\n  }\n  update() {\n    this._callback();\n  }\n  __makeNative() {\n    if (!this.__isNative) {\n      this.__isNative = true;\n      for (var key in this._props) {\n        var value = this._props[key];\n        if (value instanceof _AnimatedNode.default) {\n          value.__makeNative();\n        }\n      }\n      if (this._animatedView) {\n        this.__connectAnimatedView();\n      }\n    }\n  }\n  setNativeView(animatedView) {\n    if (this._animatedView === animatedView) {\n      return;\n    }\n    this._animatedView = animatedView;\n    if (this.__isNative) {\n      this.__connectAnimatedView();\n    }\n  }\n  __connectAnimatedView() {\n    (0, _invariant.default)(this.__isNative, 'Expected node to be marked as \"native\"');\n    var nativeViewTag = this._animatedView;\n    (0, _invariant.default)(nativeViewTag != null, 'Unable to locate attached view in the native tree');\n    _NativeAnimatedHelper.default.API.connectAnimatedNodeToView(this.__getNativeTag(), nativeViewTag);\n  }\n  __disconnectAnimatedView() {\n    (0, _invariant.default)(this.__isNative, 'Expected node to be marked as \"native\"');\n    var nativeViewTag = this._animatedView;\n    (0, _invariant.default)(nativeViewTag != null, 'Unable to locate attached view in the native tree');\n    _NativeAnimatedHelper.default.API.disconnectAnimatedNodeFromView(this.__getNativeTag(), nativeViewTag);\n  }\n  __restoreDefaultValues() {\n    // When using the native driver, view properties need to be restored to\n    // their default values manually since react no longer tracks them. This\n    // is needed to handle cases where a prop driven by native animated is removed\n    // after having been changed natively by an animation.\n    if (this.__isNative) {\n      _NativeAnimatedHelper.default.API.restoreDefaultValues(this.__getNativeTag());\n    }\n  }\n  __getNativeConfig() {\n    var propsConfig = {};\n    for (var propKey in this._props) {\n      var value = this._props[propKey];\n      if (value instanceof _AnimatedNode.default) {\n        value.__makeNative();\n        propsConfig[propKey] = value.__getNativeTag();\n      }\n    }\n    return {\n      type: 'props',\n      props: propsConfig\n    };\n  }\n}\nvar _default = exports.default = AnimatedProps;\nmodule.exports = exports.default;"], "mappings": "AAUA,YAAY;;AAAC,IAAAA,uBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,uBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,uBAAA,CAAAC,OAAA;AAAA,IAAAG,2BAAA,GAAAJ,uBAAA,CAAAC,OAAA;AAAA,IAAAI,gBAAA,GAAAL,uBAAA,CAAAC,OAAA;AAAA,IAAAK,KAAA,GAAAN,uBAAA,CAAAC,OAAA;AAAA,IAAAM,UAAA,GAAAP,uBAAA,CAAAC,OAAA;AAAA,SAAAO,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,OAAAL,gBAAA,CAAAO,OAAA,EAAAF,CAAA,OAAAN,2BAAA,CAAAQ,OAAA,EAAAH,CAAA,EAAAI,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAL,CAAA,EAAAC,CAAA,YAAAN,gBAAA,CAAAO,OAAA,EAAAH,CAAA,EAAAO,WAAA,IAAAN,CAAA,CAAAO,KAAA,CAAAR,CAAA,EAAAE,CAAA;AAAA,SAAAE,0BAAA,cAAAJ,CAAA,IAAAS,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAT,CAAA,aAAAI,yBAAA,YAAAA,0BAAA,aAAAJ,CAAA;AAAA,SAAAa,cAAAb,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAY,CAAA,QAAAC,CAAA,OAAAlB,KAAA,CAAAM,OAAA,MAAAP,gBAAA,CAAAO,OAAA,MAAAW,CAAA,GAAAd,CAAA,CAAAU,SAAA,GAAAV,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAY,CAAA,yBAAAC,CAAA,aAAAf,CAAA,WAAAe,CAAA,CAAAP,KAAA,CAAAN,CAAA,EAAAF,CAAA,OAAAe,CAAA;AAEb,IAAIC,sBAAsB,GAAGxB,OAAO,CAAC,8CAA8C,CAAC,CAACW,OAAO;AAC5Fc,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACd,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIgB,cAAc,GAAGH,sBAAsB,CAACxB,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAI4B,cAAc,GAAG5B,OAAO,mBAAmB,CAAC;AAChD,IAAI6B,aAAa,GAAGL,sBAAsB,CAACxB,OAAO,iBAAiB,CAAC,CAAC;AACrE,IAAI8B,cAAc,GAAGN,sBAAsB,CAACxB,OAAO,kBAAkB,CAAC,CAAC;AACvE,IAAI+B,qBAAqB,GAAGP,sBAAsB,CAACxB,OAAO,0BAA0B,CAAC,CAAC;AACtF,IAAIgC,UAAU,GAAGR,sBAAsB,CAACxB,OAAO,CAAC,oBAAoB,CAAC,CAAC;AAAC,IACjEiC,aAAa,aAAAC,qBAAA;EACjB,SAAAD,cAAYE,KAAK,EAAEC,QAAQ,EAAE;IAAA,IAAAC,KAAA;IAAA,IAAApC,gBAAA,CAAAU,OAAA,QAAAsB,aAAA;IAC3BI,KAAA,GAAA9B,UAAA,OAAA0B,aAAA;IACA,IAAIE,KAAK,CAACG,KAAK,EAAE;MACfH,KAAK,GAAG,CAAC,CAAC,EAAER,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEgB,cAAc,CAAChB,OAAO,EAAE,CAAC,CAAC,EAAEwB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;QAC9EG,KAAK,EAAE,IAAIR,cAAc,CAACnB,OAAO,CAACwB,KAAK,CAACG,KAAK;MAC/C,CAAC,CAAC;IACJ;IACAD,KAAA,CAAKE,MAAM,GAAGJ,KAAK;IACnBE,KAAA,CAAKG,SAAS,GAAGJ,QAAQ;IACzBC,KAAA,CAAKI,QAAQ,CAAC,CAAC;IAAC,OAAAJ,KAAA;EAClB;EAAC,IAAA/B,UAAA,CAAAK,OAAA,EAAAsB,aAAA,EAAAC,qBAAA;EAAA,WAAAhC,aAAA,CAAAS,OAAA,EAAAsB,aAAA;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAC,UAAUA,CAAA,EAAG;MACX,IAAIT,KAAK,GAAG,CAAC,CAAC;MACd,KAAK,IAAIO,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYd,aAAa,CAAClB,OAAO,EAAE;UAC1C,IAAI,CAACgC,KAAK,CAACE,UAAU,IAAIF,KAAK,YAAYb,cAAc,CAACnB,OAAO,EAAE;YAGhEwB,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK,CAACC,UAAU,CAAC,CAAC;UACjC;QACF,CAAC,MAAM,IAAID,KAAK,YAAYf,cAAc,CAACkB,aAAa,EAAE;UACxDX,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK,CAACI,YAAY,CAAC,CAAC;QACnC,CAAC,MAAM;UACLZ,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK;QACpB;MACF;MACA,OAAOR,KAAK;IACd;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAK,kBAAkBA,CAAA,EAAG;MACnB,IAAIb,KAAK,GAAG,CAAC,CAAC;MACd,KAAK,IAAIO,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYd,aAAa,CAAClB,OAAO,EAAE;UAC1CwB,KAAK,CAACO,GAAG,CAAC,GAAGC,KAAK,CAACK,kBAAkB,CAAC,CAAC;QACzC;MACF;MACA,OAAOb,KAAK;IACd;EAAC;IAAAO,GAAA;IAAAC,KAAA,EACD,SAAAF,QAAQA,CAAA,EAAG;MACT,KAAK,IAAIC,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYd,aAAa,CAAClB,OAAO,EAAE;UAC1CgC,KAAK,CAACM,UAAU,CAAC,IAAI,CAAC;QACxB;MACF;IACF;EAAC;IAAAP,GAAA;IAAAC,KAAA,EACD,SAAAO,QAAQA,CAAA,EAAG;MACT,IAAI,IAAI,CAACL,UAAU,IAAI,IAAI,CAACM,aAAa,EAAE;QACzC,IAAI,CAACC,wBAAwB,CAAC,CAAC;MACjC;MACA,KAAK,IAAIV,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;QAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;QAC5B,IAAIC,KAAK,YAAYd,aAAa,CAAClB,OAAO,EAAE;UAC1CgC,KAAK,CAACU,aAAa,CAAC,IAAI,CAAC;QAC3B;MACF;MACAhC,aAAA,CAAAY,aAAA;IACF;EAAC;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAW,MAAMA,CAAA,EAAG;MACP,IAAI,CAACd,SAAS,CAAC,CAAC;IAClB;EAAC;IAAAE,GAAA;IAAAC,KAAA,EACD,SAAAY,YAAYA,CAAA,EAAG;MACb,IAAI,CAAC,IAAI,CAACV,UAAU,EAAE;QACpB,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,KAAK,IAAIH,GAAG,IAAI,IAAI,CAACH,MAAM,EAAE;UAC3B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAACG,GAAG,CAAC;UAC5B,IAAIC,KAAK,YAAYd,aAAa,CAAClB,OAAO,EAAE;YAC1CgC,KAAK,CAACY,YAAY,CAAC,CAAC;UACtB;QACF;QACA,IAAI,IAAI,CAACJ,aAAa,EAAE;UACtB,IAAI,CAACK,qBAAqB,CAAC,CAAC;QAC9B;MACF;IACF;EAAC;IAAAd,GAAA;IAAAC,KAAA,EACD,SAAAc,aAAaA,CAACC,YAAY,EAAE;MAC1B,IAAI,IAAI,CAACP,aAAa,KAAKO,YAAY,EAAE;QACvC;MACF;MACA,IAAI,CAACP,aAAa,GAAGO,YAAY;MACjC,IAAI,IAAI,CAACb,UAAU,EAAE;QACnB,IAAI,CAACW,qBAAqB,CAAC,CAAC;MAC9B;IACF;EAAC;IAAAd,GAAA;IAAAC,KAAA,EACD,SAAAa,qBAAqBA,CAAA,EAAG;MACtB,CAAC,CAAC,EAAExB,UAAU,CAACrB,OAAO,EAAE,IAAI,CAACkC,UAAU,EAAE,wCAAwC,CAAC;MAClF,IAAIc,aAAa,GAAG,IAAI,CAACR,aAAa;MACtC,CAAC,CAAC,EAAEnB,UAAU,CAACrB,OAAO,EAAEgD,aAAa,IAAI,IAAI,EAAE,mDAAmD,CAAC;MACnG5B,qBAAqB,CAACpB,OAAO,CAACiD,GAAG,CAACC,yBAAyB,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEH,aAAa,CAAC;IACnG;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EACD,SAAAS,wBAAwBA,CAAA,EAAG;MACzB,CAAC,CAAC,EAAEpB,UAAU,CAACrB,OAAO,EAAE,IAAI,CAACkC,UAAU,EAAE,wCAAwC,CAAC;MAClF,IAAIc,aAAa,GAAG,IAAI,CAACR,aAAa;MACtC,CAAC,CAAC,EAAEnB,UAAU,CAACrB,OAAO,EAAEgD,aAAa,IAAI,IAAI,EAAE,mDAAmD,CAAC;MACnG5B,qBAAqB,CAACpB,OAAO,CAACiD,GAAG,CAACG,8BAA8B,CAAC,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEH,aAAa,CAAC;IACxG;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EACD,SAAAqB,sBAAsBA,CAAA,EAAG;MAKvB,IAAI,IAAI,CAACnB,UAAU,EAAE;QACnBd,qBAAqB,CAACpB,OAAO,CAACiD,GAAG,CAACK,oBAAoB,CAAC,IAAI,CAACH,cAAc,CAAC,CAAC,CAAC;MAC/E;IACF;EAAC;IAAApB,GAAA;IAAAC,KAAA,EACD,SAAAuB,iBAAiBA,CAAA,EAAG;MAClB,IAAIC,WAAW,GAAG,CAAC,CAAC;MACpB,KAAK,IAAIC,OAAO,IAAI,IAAI,CAAC7B,MAAM,EAAE;QAC/B,IAAII,KAAK,GAAG,IAAI,CAACJ,MAAM,CAAC6B,OAAO,CAAC;QAChC,IAAIzB,KAAK,YAAYd,aAAa,CAAClB,OAAO,EAAE;UAC1CgC,KAAK,CAACY,YAAY,CAAC,CAAC;UACpBY,WAAW,CAACC,OAAO,CAAC,GAAGzB,KAAK,CAACmB,cAAc,CAAC,CAAC;QAC/C;MACF;MACA,OAAO;QACLO,IAAI,EAAE,OAAO;QACblC,KAAK,EAAEgC;MACT,CAAC;IACH;EAAC;AAAA,EAxHyBtC,aAAa,CAAClB,OAAO;AA0HjD,IAAI2D,QAAQ,GAAG7C,OAAO,CAACd,OAAO,GAAGsB,aAAa;AAC9CsC,MAAM,CAAC9C,OAAO,GAAGA,OAAO,CAACd,OAAO", "ignoreList": []}