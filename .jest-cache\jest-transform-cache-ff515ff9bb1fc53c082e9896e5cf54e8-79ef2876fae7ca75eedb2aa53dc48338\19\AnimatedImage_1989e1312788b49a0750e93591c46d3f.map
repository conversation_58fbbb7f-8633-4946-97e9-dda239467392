{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "React", "_Image", "_createAnimatedComponent", "_default", "module"], "sources": ["AnimatedImage.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _Image = _interopRequireDefault(require(\"../../../../exports/Image\"));\nvar _createAnimatedComponent = _interopRequireDefault(require(\"../createAnimatedComponent\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\nvar _default = exports.default = (0, _createAnimatedComponent.default)(_Image.default);\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,KAAK,GAAGH,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIM,MAAM,GAAGP,sBAAsB,CAACC,OAAO,4BAA4B,CAAC,CAAC;AACzE,IAAIO,wBAAwB,GAAGR,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AAU5F,IAAIQ,QAAQ,GAAGL,OAAO,CAACF,OAAO,GAAG,CAAC,CAAC,EAAEM,wBAAwB,CAACN,OAAO,EAAEK,MAAM,CAACL,OAAO,CAAC;AACtFQ,MAAM,CAACN,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}