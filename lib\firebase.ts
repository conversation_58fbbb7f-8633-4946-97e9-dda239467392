// Firebase imports - now available with installed dependencies
import { initializeApp, getApps, getApp, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import { getFirestore as getFirestoreDB, Firestore } from 'firebase/firestore';
import { getFunctions, Functions } from 'firebase/functions';
import { getStorage, FirebaseStorage } from 'firebase/storage';
import { Platform } from 'react-native';
import env from '@/config/environment';

/**
 * Firebase Configuration
 * Uses environment-aware configuration for different deployment environments
 */

// Firebase configuration interface
interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  measurementId?: string;
}

// Firebase services interface
interface FirebaseServices {
  app: FirebaseApp | null;
  auth: Auth | null;
  firestore: Firestore | null;
  functions: Functions | null;
  storage: FirebaseStorage | null;
}

// Initialize Firebase services as null initially
let firebaseServices: FirebaseServices = {
  app: null,
  auth: null,
  firestore: null,
  functions: null,
  storage: null,
};

// Firebase configuration
const firebaseConfig: FirebaseConfig = {
  apiKey: env.get('FIREBASE_API_KEY'),
  authDomain: env.get('FIREBASE_AUTH_DOMAIN'),
  projectId: env.get('FIREBASE_PROJECT_ID'),
  storageBucket: env.get('FIREBASE_STORAGE_BUCKET'),
  messagingSenderId: env.get('FIREBASE_MESSAGING_SENDER_ID'),
  appId: env.get('FIREBASE_APP_ID'),
  measurementId: env.get('FIREBASE_MEASUREMENT_ID'),
};

// Validate Firebase configuration
const requiredKeys = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
const missingKeys = requiredKeys.filter(key => !firebaseConfig[key as keyof typeof firebaseConfig]);

if (missingKeys.length > 0) {
  const errorMessage = `Missing Firebase configuration for ${env.getEnvironment()} environment: ${missingKeys.join(', ')}. Please check your environment variables.`;
  
  if (env.getEnvironment() === 'production') {
    throw new Error(errorMessage);
  } else {
    console.warn('⚠️ ' + errorMessage);
    console.warn('Using fallback Firebase configuration for development.');
  }
}

/**
 * Initialize Firebase with configuration
 */
export async function initializeFirebase(): Promise<FirebaseServices> {
  try {
    // Initialize Firebase App
    const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApp();

    // Initialize Auth
    const auth = getAuth(app);

    // Initialize Firestore
    const firestore = getFirestoreDB(app);

    // Initialize Functions
    const functions = getFunctions(app);

    // Initialize Storage
    const storage = getStorage(app);

    // Note: Emulator connections can be added here for development

    // Update services
    firebaseServices = {
      app,
      auth,
      firestore,
      functions,
      storage,
    };

    console.log('🔥 Firebase initialized successfully');
    return firebaseServices;
  } catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    throw error;
  }
}

/**
 * Get Firebase services (lazy initialization)
 */
export function getFirebaseServices(): FirebaseServices {
  return firebaseServices;
}

/**
 * Get Firebase app instance
 */
export function getFirebaseApp(): FirebaseApp | null {
  return firebaseServices.app;
}

/**
 * Get Firebase auth instance
 */
export function getFirebaseAuth(): Auth | null {
  return firebaseServices.auth;
}

/**
 * Get Firestore instance
 */
export function getFirestore(): Firestore | null {
  return firebaseServices.firestore;
}

/**
 * Get Firebase functions instance
 */
export function getFirebaseFunctions(): Functions | null {
  return firebaseServices.functions;
}

/**
 * Get Firebase storage instance
 */
export function getFirebaseStorage(): FirebaseStorage | null {
  return firebaseServices.storage;
}

// Export Firebase services for backward compatibility
export const firebase = firebaseServices.app;
export const auth = firebaseServices.auth;
export const firestore = firebaseServices.firestore;
export const functions = firebaseServices.functions;
export const storage = firebaseServices.storage;

// Export Firebase configuration for debugging
export const firebaseConfigInfo = {
  projectId: firebaseConfig.projectId,
  authDomain: firebaseConfig.authDomain ? `${firebaseConfig.authDomain.substring(0, 20)}...` : 'Not configured',
  environment: env.getEnvironment(),
  hasApiKey: !!firebaseConfig.apiKey,
  features: {
    auth: true,
    firestore: true,
    functions: true,
    storage: true,
    analytics: !!firebaseConfig.measurementId,
  },
};

// Log configuration (without sensitive data)
if (env.get('DEBUG_MODE')) {
  console.log('🔧 Firebase Configuration:', firebaseConfigInfo);
}