{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_react", "React", "_pick", "_useMergeRefs", "_usePressEvents", "_warnOnce", "forwardPropsList", "accessibilityDisabled", "accessibilityLabel", "accessibilityLiveRegion", "accessibilityRole", "accessibilityState", "accessibilityValue", "children", "disabled", "focusable", "nativeID", "onBlur", "onFocus", "onLayout", "testID", "pickProps", "props", "TouchableWithoutFeedback", "forwardedRef", "warnOnce", "delayPressIn", "delayPressOut", "delayLongPress", "onLongPress", "onPress", "onPressIn", "onPressOut", "rejectResponderTermination", "hostRef", "useRef", "pressConfig", "useMemo", "cancelable", "delayPressStart", "delayPressEnd", "onPressStart", "onPressEnd", "pressEventHandlers", "element", "Children", "only", "supportedProps", "ref", "elementProps", "Object", "assign", "cloneElement", "apply", "concat", "MemoedTouchableWithoutFeedback", "memo", "forwardRef", "displayName", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _pick = _interopRequireDefault(require(\"../../modules/pick\"));\nvar _useMergeRefs = _interopRequireDefault(require(\"../../modules/useMergeRefs\"));\nvar _usePressEvents = _interopRequireDefault(require(\"../../modules/usePressEvents\"));\nvar _warnOnce = require(\"../../modules/warnOnce\");\nvar forwardPropsList = {\n  accessibilityDisabled: true,\n  accessibilityLabel: true,\n  accessibilityLiveRegion: true,\n  accessibilityRole: true,\n  accessibilityState: true,\n  accessibilityValue: true,\n  children: true,\n  disabled: true,\n  focusable: true,\n  nativeID: true,\n  onBlur: true,\n  onFocus: true,\n  onLayout: true,\n  testID: true\n};\nvar pickProps = props => (0, _pick.default)(props, forwardPropsList);\nfunction TouchableWithoutFeedback(props, forwardedRef) {\n  (0, _warnOnce.warnOnce)('TouchableWithoutFeedback', 'TouchableWithoutFeedback is deprecated. Please use Pressable.');\n  var delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    delayLongPress = props.delayLongPress,\n    disabled = props.disabled,\n    focusable = props.focusable,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    rejectResponderTermination = props.rejectResponderTermination;\n  var hostRef = (0, _react.useRef)(null);\n  var pressConfig = (0, _react.useMemo)(() => ({\n    cancelable: !rejectResponderTermination,\n    disabled,\n    delayLongPress,\n    delayPressStart: delayPressIn,\n    delayPressEnd: delayPressOut,\n    onLongPress,\n    onPress,\n    onPressStart: onPressIn,\n    onPressEnd: onPressOut\n  }), [disabled, delayPressIn, delayPressOut, delayLongPress, onLongPress, onPress, onPressIn, onPressOut, rejectResponderTermination]);\n  var pressEventHandlers = (0, _usePressEvents.default)(hostRef, pressConfig);\n  var element = React.Children.only(props.children);\n  var children = [element.props.children];\n  var supportedProps = pickProps(props);\n  supportedProps.accessibilityDisabled = disabled;\n  supportedProps.focusable = !disabled && focusable !== false;\n  supportedProps.ref = (0, _useMergeRefs.default)(forwardedRef, hostRef, element.ref);\n  var elementProps = Object.assign(supportedProps, pressEventHandlers);\n  return /*#__PURE__*/React.cloneElement(element, elementProps, ...children);\n}\nvar MemoedTouchableWithoutFeedback = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(TouchableWithoutFeedback));\nMemoedTouchableWithoutFeedback.displayName = 'TouchableWithoutFeedback';\nvar _default = exports.default = MemoedTouchableWithoutFeedback;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,MAAM,GAAGH,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,IAAIM,KAAK,GAAGD,MAAM;AAClB,IAAIE,KAAK,GAAGR,sBAAsB,CAACC,OAAO,qBAAqB,CAAC,CAAC;AACjE,IAAIQ,aAAa,GAAGT,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AACjF,IAAIS,eAAe,GAAGV,sBAAsB,CAACC,OAAO,+BAA+B,CAAC,CAAC;AACrF,IAAIU,SAAS,GAAGV,OAAO,yBAAyB,CAAC;AACjD,IAAIW,gBAAgB,GAAG;EACrBC,qBAAqB,EAAE,IAAI;EAC3BC,kBAAkB,EAAE,IAAI;EACxBC,uBAAuB,EAAE,IAAI;EAC7BC,iBAAiB,EAAE,IAAI;EACvBC,kBAAkB,EAAE,IAAI;EACxBC,kBAAkB,EAAE,IAAI;EACxBC,QAAQ,EAAE,IAAI;EACdC,QAAQ,EAAE,IAAI;EACdC,SAAS,EAAE,IAAI;EACfC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,SAAS,GAAG,SAAZA,SAASA,CAAGC,KAAK;EAAA,OAAI,CAAC,CAAC,EAAEpB,KAAK,CAACN,OAAO,EAAE0B,KAAK,EAAEhB,gBAAgB,CAAC;AAAA;AACpE,SAASiB,wBAAwBA,CAACD,KAAK,EAAEE,YAAY,EAAE;EACrD,CAAC,CAAC,EAAEnB,SAAS,CAACoB,QAAQ,EAAE,0BAA0B,EAAE,+DAA+D,CAAC;EACpH,IAAIC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACnCC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,cAAc,GAAGN,KAAK,CAACM,cAAc;IACrCd,QAAQ,GAAGQ,KAAK,CAACR,QAAQ;IACzBC,SAAS,GAAGO,KAAK,CAACP,SAAS;IAC3Bc,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,OAAO,GAAGR,KAAK,CAACQ,OAAO;IACvBC,SAAS,GAAGT,KAAK,CAACS,SAAS;IAC3BC,UAAU,GAAGV,KAAK,CAACU,UAAU;IAC7BC,0BAA0B,GAAGX,KAAK,CAACW,0BAA0B;EAC/D,IAAIC,OAAO,GAAG,CAAC,CAAC,EAAElC,MAAM,CAACmC,MAAM,EAAE,IAAI,CAAC;EACtC,IAAIC,WAAW,GAAG,CAAC,CAAC,EAAEpC,MAAM,CAACqC,OAAO,EAAE;IAAA,OAAO;MAC3CC,UAAU,EAAE,CAACL,0BAA0B;MACvCnB,QAAQ,EAARA,QAAQ;MACRc,cAAc,EAAdA,cAAc;MACdW,eAAe,EAAEb,YAAY;MAC7Bc,aAAa,EAAEb,aAAa;MAC5BE,WAAW,EAAXA,WAAW;MACXC,OAAO,EAAPA,OAAO;MACPW,YAAY,EAAEV,SAAS;MACvBW,UAAU,EAAEV;IACd,CAAC;EAAA,CAAC,EAAE,CAAClB,QAAQ,EAAEY,YAAY,EAAEC,aAAa,EAAEC,cAAc,EAAEC,WAAW,EAAEC,OAAO,EAAEC,SAAS,EAAEC,UAAU,EAAEC,0BAA0B,CAAC,CAAC;EACrI,IAAIU,kBAAkB,GAAG,CAAC,CAAC,EAAEvC,eAAe,CAACR,OAAO,EAAEsC,OAAO,EAAEE,WAAW,CAAC;EAC3E,IAAIQ,OAAO,GAAG3C,KAAK,CAAC4C,QAAQ,CAACC,IAAI,CAACxB,KAAK,CAACT,QAAQ,CAAC;EACjD,IAAIA,QAAQ,GAAG,CAAC+B,OAAO,CAACtB,KAAK,CAACT,QAAQ,CAAC;EACvC,IAAIkC,cAAc,GAAG1B,SAAS,CAACC,KAAK,CAAC;EACrCyB,cAAc,CAACxC,qBAAqB,GAAGO,QAAQ;EAC/CiC,cAAc,CAAChC,SAAS,GAAG,CAACD,QAAQ,IAAIC,SAAS,KAAK,KAAK;EAC3DgC,cAAc,CAACC,GAAG,GAAG,CAAC,CAAC,EAAE7C,aAAa,CAACP,OAAO,EAAE4B,YAAY,EAAEU,OAAO,EAAEU,OAAO,CAACI,GAAG,CAAC;EACnF,IAAIC,YAAY,GAAGC,MAAM,CAACC,MAAM,CAACJ,cAAc,EAAEJ,kBAAkB,CAAC;EACpE,OAAoB1C,KAAK,CAACmD,YAAY,CAAAC,KAAA,CAAlBpD,KAAK,GAAc2C,OAAO,EAAEK,YAAY,EAAAK,MAAA,CAAKzC,QAAQ,EAAC;AAC5E;AACA,IAAI0C,8BAA8B,GAAgBtD,KAAK,CAACuD,IAAI,CAAcvD,KAAK,CAACwD,UAAU,CAAClC,wBAAwB,CAAC,CAAC;AACrHgC,8BAA8B,CAACG,WAAW,GAAG,0BAA0B;AACvE,IAAIC,QAAQ,GAAG7D,OAAO,CAACF,OAAO,GAAG2D,8BAA8B;AAC/DK,MAAM,CAAC9D,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}