{"version": 3, "names": ["exports", "__esModule", "default", "useMergeRefs", "_react", "require", "_len", "arguments", "length", "refs", "Array", "_key", "useCallback", "current", "_i", "_refs", "ref", "concat", "module"], "sources": ["useMergeRefs.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = useMergeRefs;\nvar _react = require(\"react\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n/**\n * Constructs a new ref that forwards new values to each of the given refs. The\n * given refs will always be invoked in the order that they are supplied.\n *\n * WARNING: A known problem of merging refs using this approach is that if any\n * of the given refs change, the returned callback ref will also be changed. If\n * the returned callback ref is supplied as a `ref` to a React element, this may\n * lead to problems with the given refs being invoked more times than desired.\n */\nfunction useMergeRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  return (0, _react.useCallback)(current => {\n    for (var _i = 0, _refs = refs; _i < _refs.length; _i++) {\n      var ref = _refs[_i];\n      if (ref != null) {\n        if (typeof ref === 'function') {\n          ref(current);\n        } else {\n          ref.current = current;\n        }\n      }\n    }\n  }, [...refs] // eslint-disable-line react-hooks/exhaustive-deps\n  );\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,YAAY;AAC9B,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAoB7B,SAASF,YAAYA,CAAA,EAAG;EACtB,KAAK,IAAIG,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,OAAO,CAAC,CAAC,EAAEP,MAAM,CAACQ,WAAW,EAAE,UAAAC,OAAO,EAAI;IACxC,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,KAAK,GAAGN,IAAI,EAAEK,EAAE,GAAGC,KAAK,CAACP,MAAM,EAAEM,EAAE,EAAE,EAAE;MACtD,IAAIE,GAAG,GAAGD,KAAK,CAACD,EAAE,CAAC;MACnB,IAAIE,GAAG,IAAI,IAAI,EAAE;QACf,IAAI,OAAOA,GAAG,KAAK,UAAU,EAAE;UAC7BA,GAAG,CAACH,OAAO,CAAC;QACd,CAAC,MAAM;UACLG,GAAG,CAACH,OAAO,GAAGA,OAAO;QACvB;MACF;IACF;EACF,CAAC,KAAAI,MAAA,CAAMR,IAAI,CACX,CAAC;AACH;AACAS,MAAM,CAAClB,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}