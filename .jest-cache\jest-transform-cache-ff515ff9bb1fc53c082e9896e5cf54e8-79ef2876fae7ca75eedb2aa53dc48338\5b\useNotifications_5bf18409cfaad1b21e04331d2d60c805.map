{"version": 3, "names": ["useState", "useCallback", "useEffect", "notificationService", "Notifications", "useNotifications", "cov_2l11fifpkv", "f", "_ref", "s", "_ref2", "_slicedToArray", "isInitialized", "setIsInitialized", "_ref3", "dailyTips", "trainingReminders", "matchReminders", "<PERSON><PERSON><PERSON><PERSON>", "progressUpdates", "socialNotifications", "_ref4", "settings", "setSettings", "_ref5", "_ref6", "pushToken", "setPushToken", "_ref7", "_ref8", "error", "setError", "initialize", "_asyncToGenerator", "initialized", "b", "token", "getPushToken", "currentSettings", "getSettings", "err", "errorMessage", "Error", "message", "sendLocalNotification", "_ref0", "content", "_x", "apply", "arguments", "scheduleNotification", "_ref1", "trigger", "_x2", "_x3", "cancelNotification", "_ref10", "notificationId", "_x4", "scheduleDailyTips", "scheduleTrainingReminder", "_ref12", "title", "scheduledTime", "_x5", "_x6", "_x7", "sendAchievementNotification", "_ref13", "achievementTitle", "description", "_x8", "_x9", "sendProgressUpdate", "_ref14", "skillName", "oldRating", "newRating", "_x0", "_x1", "_x10", "scheduleMatchReminder", "_ref15", "<PERSON><PERSON><PERSON>", "matchTime", "location", "_x11", "_x12", "_x13", "updateSettings", "newSettings", "updatedSettings", "getScheduledNotifications", "notificationListener", "addNotificationReceivedListener", "notification", "console", "log", "responseListener", "addNotificationResponseReceivedListener", "response", "notificationData", "request", "data", "type", "removeNotificationSubscription"], "sources": ["useNotifications.ts"], "sourcesContent": ["import { useState, useCallback, useEffect } from 'react';\nimport { notificationService, NotificationContent, NotificationSettings } from '@/services/notificationService';\nimport * as Notifications from 'expo-notifications';\n\ninterface UseNotificationsReturn {\n  // Notification state\n  isInitialized: boolean;\n  settings: NotificationSettings;\n  pushToken: string | null;\n  \n  // Notification controls\n  initialize: () => Promise<boolean>;\n  sendLocalNotification: (content: NotificationContent) => Promise<string>;\n  scheduleNotification: (content: NotificationContent, trigger: Notifications.NotificationTriggerInput) => Promise<string>;\n  cancelNotification: (notificationId: string) => Promise<void>;\n  \n  // Specific notification types\n  scheduleDailyTips: () => Promise<void>;\n  scheduleTrainingReminder: (title: string, message: string, scheduledTime: Date) => Promise<string>;\n  sendAchievementNotification: (achievementTitle: string, description: string) => Promise<void>;\n  sendProgressUpdate: (skillName: string, oldRating: number, newRating: number) => Promise<void>;\n  scheduleMatchReminder: (opponentName: string, matchTime: Date, location?: string) => Promise<string>;\n  \n  // Settings management\n  updateSettings: (newSettings: Partial<NotificationSettings>) => void;\n  getScheduledNotifications: () => Promise<Notifications.NotificationRequest[]>;\n  \n  // Error handling\n  error: string | null;\n}\n\nexport function useNotifications(): UseNotificationsReturn {\n  const [isInitialized, setIsInitialized] = useState(false);\n  const [settings, setSettings] = useState<NotificationSettings>({\n    dailyTips: true,\n    trainingReminders: true,\n    matchReminders: true,\n    achievementAlerts: true,\n    progressUpdates: true,\n    socialNotifications: false,\n  });\n  const [pushToken, setPushToken] = useState<string | null>(null);\n  const [error, setError] = useState<string | null>(null);\n\n  /**\n   * Initialize notification service\n   */\n  const initialize = useCallback(async (): Promise<boolean> => {\n    try {\n      setError(null);\n      const initialized = await notificationService.initialize();\n      setIsInitialized(initialized);\n      \n      if (initialized) {\n        const token = notificationService.getPushToken();\n        setPushToken(token);\n        \n        const currentSettings = notificationService.getSettings();\n        setSettings(currentSettings);\n      } else {\n        setError('Failed to initialize notifications - permissions may be denied');\n      }\n      \n      return initialized;\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize notifications';\n      setError(errorMessage);\n      setIsInitialized(false);\n      return false;\n    }\n  }, []);\n\n  /**\n   * Send local notification immediately\n   */\n  const sendLocalNotification = useCallback(async (content: NotificationContent): Promise<string> => {\n    if (!isInitialized) {\n      throw new Error('Notification service not initialized');\n    }\n\n    try {\n      setError(null);\n      return await notificationService.sendLocalNotification(content);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to send notification';\n      setError(errorMessage);\n      throw err;\n    }\n  }, [isInitialized]);\n\n  /**\n   * Schedule a notification for later\n   */\n  const scheduleNotification = useCallback(async (\n    content: NotificationContent,\n    trigger: Notifications.NotificationTriggerInput\n  ): Promise<string> => {\n    if (!isInitialized) {\n      throw new Error('Notification service not initialized');\n    }\n\n    try {\n      setError(null);\n      return await notificationService.scheduleNotification(content, trigger);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to schedule notification';\n      setError(errorMessage);\n      throw err;\n    }\n  }, [isInitialized]);\n\n  /**\n   * Cancel specific notification\n   */\n  const cancelNotification = useCallback(async (notificationId: string): Promise<void> => {\n    try {\n      setError(null);\n      await notificationService.cancelNotification(notificationId);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel notification';\n      setError(errorMessage);\n      throw err;\n    }\n  }, []);\n\n  /**\n   * Schedule daily tennis tips\n   */\n  const scheduleDailyTips = useCallback(async (): Promise<void> => {\n    if (!isInitialized) {\n      throw new Error('Notification service not initialized');\n    }\n\n    try {\n      setError(null);\n      await notificationService.scheduleDailyTips();\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to schedule daily tips';\n      setError(errorMessage);\n      throw err;\n    }\n  }, [isInitialized]);\n\n  /**\n   * Schedule training reminder\n   */\n  const scheduleTrainingReminder = useCallback(async (\n    title: string,\n    message: string,\n    scheduledTime: Date\n  ): Promise<string> => {\n    if (!isInitialized) {\n      throw new Error('Notification service not initialized');\n    }\n\n    try {\n      setError(null);\n      return await notificationService.scheduleTrainingReminder(title, message, scheduledTime);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to schedule training reminder';\n      setError(errorMessage);\n      throw err;\n    }\n  }, [isInitialized]);\n\n  /**\n   * Send achievement notification\n   */\n  const sendAchievementNotification = useCallback(async (\n    achievementTitle: string,\n    description: string\n  ): Promise<void> => {\n    if (!isInitialized) {\n      throw new Error('Notification service not initialized');\n    }\n\n    try {\n      setError(null);\n      await notificationService.sendAchievementNotification(achievementTitle, description);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to send achievement notification';\n      setError(errorMessage);\n      throw err;\n    }\n  }, [isInitialized]);\n\n  /**\n   * Send progress update notification\n   */\n  const sendProgressUpdate = useCallback(async (\n    skillName: string,\n    oldRating: number,\n    newRating: number\n  ): Promise<void> => {\n    if (!isInitialized) {\n      throw new Error('Notification service not initialized');\n    }\n\n    try {\n      setError(null);\n      await notificationService.sendProgressUpdate(skillName, oldRating, newRating);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to send progress update';\n      setError(errorMessage);\n      throw err;\n    }\n  }, [isInitialized]);\n\n  /**\n   * Schedule match reminder\n   */\n  const scheduleMatchReminder = useCallback(async (\n    opponentName: string,\n    matchTime: Date,\n    location?: string\n  ): Promise<string> => {\n    if (!isInitialized) {\n      throw new Error('Notification service not initialized');\n    }\n\n    try {\n      setError(null);\n      return await notificationService.scheduleMatchReminder(opponentName, matchTime, location);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to schedule match reminder';\n      setError(errorMessage);\n      throw err;\n    }\n  }, [isInitialized]);\n\n  /**\n   * Update notification settings\n   */\n  const updateSettings = useCallback((newSettings: Partial<NotificationSettings>): void => {\n    try {\n      setError(null);\n      notificationService.updateSettings(newSettings);\n      const updatedSettings = notificationService.getSettings();\n      setSettings(updatedSettings);\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to update settings';\n      setError(errorMessage);\n    }\n  }, []);\n\n  /**\n   * Get all scheduled notifications\n   */\n  const getScheduledNotifications = useCallback(async (): Promise<Notifications.NotificationRequest[]> => {\n    try {\n      setError(null);\n      return await notificationService.getScheduledNotifications();\n    } catch (err) {\n      const errorMessage = err instanceof Error ? err.message : 'Failed to get scheduled notifications';\n      setError(errorMessage);\n      return [];\n    }\n  }, []);\n\n  // Initialize notification service on mount\n  useEffect(() => {\n    initialize();\n  }, [initialize]);\n\n  // Set up notification listeners\n  useEffect(() => {\n    if (!isInitialized) return;\n\n    // Listen for notifications received while app is in foreground\n    const notificationListener = Notifications.addNotificationReceivedListener(notification => {\n      console.log('Notification received:', notification);\n    });\n\n    // Listen for notification responses (when user taps notification)\n    const responseListener = Notifications.addNotificationResponseReceivedListener(response => {\n      console.log('Notification response:', response);\n      \n      // Handle notification tap based on data\n      const notificationData = response.notification.request.content.data;\n      if (notificationData?.type) {\n        // You can navigate to specific screens based on notification type\n        console.log('Notification type:', notificationData.type);\n      }\n    });\n\n    return () => {\n      Notifications.removeNotificationSubscription(notificationListener);\n      Notifications.removeNotificationSubscription(responseListener);\n    };\n  }, [isInitialized]);\n\n  return {\n    // Notification state\n    isInitialized,\n    settings,\n    pushToken,\n    \n    // Notification controls\n    initialize,\n    sendLocalNotification,\n    scheduleNotification,\n    cancelNotification,\n    \n    // Specific notification types\n    scheduleDailyTips,\n    scheduleTrainingReminder,\n    sendAchievementNotification,\n    sendProgressUpdate,\n    scheduleMatchReminder,\n    \n    // Settings management\n    updateSettings,\n    getScheduledNotifications,\n    \n    // Error handling\n    error,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACxD,SAASC,mBAAmB;AAC5B,OAAO,KAAKC,aAAa,MAAM,oBAAoB;AA6BnD,OAAO,SAASC,gBAAgBA,CAAA,EAA2B;EAAAC,cAAA,GAAAC,CAAA;EACzD,IAAAC,IAAA,IAAAF,cAAA,GAAAG,CAAA,OAA0CT,QAAQ,CAAC,KAAK,CAAC;IAAAU,KAAA,GAAAC,cAAA,CAAAH,IAAA;IAAlDI,aAAa,GAAAF,KAAA;IAAEG,gBAAgB,GAAAH,KAAA;EACtC,IAAAI,KAAA,IAAAR,cAAA,GAAAG,CAAA,OAAgCT,QAAQ,CAAuB;MAC7De,SAAS,EAAE,IAAI;MACfC,iBAAiB,EAAE,IAAI;MACvBC,cAAc,EAAE,IAAI;MACpBC,iBAAiB,EAAE,IAAI;MACvBC,eAAe,EAAE,IAAI;MACrBC,mBAAmB,EAAE;IACvB,CAAC,CAAC;IAAAC,KAAA,GAAAV,cAAA,CAAAG,KAAA;IAPKQ,QAAQ,GAAAD,KAAA;IAAEE,WAAW,GAAAF,KAAA;EAQ5B,IAAAG,KAAA,IAAAlB,cAAA,GAAAG,CAAA,OAAkCT,QAAQ,CAAgB,IAAI,CAAC;IAAAyB,KAAA,GAAAd,cAAA,CAAAa,KAAA;IAAxDE,SAAS,GAAAD,KAAA;IAAEE,YAAY,GAAAF,KAAA;EAC9B,IAAAG,KAAA,IAAAtB,cAAA,GAAAG,CAAA,OAA0BT,QAAQ,CAAgB,IAAI,CAAC;IAAA6B,KAAA,GAAAlB,cAAA,CAAAiB,KAAA;IAAhDE,KAAK,GAAAD,KAAA;IAAEE,QAAQ,GAAAF,KAAA;EAKtB,IAAMG,UAAU,IAAA1B,cAAA,GAAAG,CAAA,OAAGR,WAAW,CAAAgC,iBAAA,CAAC,aAA8B;IAAA3B,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC3D,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACFsB,QAAQ,CAAC,IAAI,CAAC;MACd,IAAMG,WAAW,IAAA5B,cAAA,GAAAG,CAAA,aAASN,mBAAmB,CAAC6B,UAAU,CAAC,CAAC;MAAC1B,cAAA,GAAAG,CAAA;MAC3DI,gBAAgB,CAACqB,WAAW,CAAC;MAAC5B,cAAA,GAAAG,CAAA;MAE9B,IAAIyB,WAAW,EAAE;QAAA5B,cAAA,GAAA6B,CAAA;QACf,IAAMC,KAAK,IAAA9B,cAAA,GAAAG,CAAA,QAAGN,mBAAmB,CAACkC,YAAY,CAAC,CAAC;QAAC/B,cAAA,GAAAG,CAAA;QACjDkB,YAAY,CAACS,KAAK,CAAC;QAEnB,IAAME,eAAe,IAAAhC,cAAA,GAAAG,CAAA,QAAGN,mBAAmB,CAACoC,WAAW,CAAC,CAAC;QAACjC,cAAA,GAAAG,CAAA;QAC1Dc,WAAW,CAACe,eAAe,CAAC;MAC9B,CAAC,MAAM;QAAAhC,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAG,CAAA;QACLsB,QAAQ,CAAC,gEAAgE,CAAC;MAC5E;MAACzB,cAAA,GAAAG,CAAA;MAED,OAAOyB,WAAW;IACpB,CAAC,CAAC,OAAOM,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,UAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,UAAG,oCAAoC;MAAC7B,cAAA,GAAAG,CAAA;MAC/FsB,QAAQ,CAACU,YAAY,CAAC;MAACnC,cAAA,GAAAG,CAAA;MACvBI,gBAAgB,CAAC,KAAK,CAAC;MAACP,cAAA,GAAAG,CAAA;MACxB,OAAO,KAAK;IACd;EACF,CAAC,GAAE,EAAE,CAAC;EAKN,IAAMmC,qBAAqB,IAAAtC,cAAA,GAAAG,CAAA,QAAGR,WAAW;IAAA,IAAA4C,KAAA,GAAAZ,iBAAA,CAAC,WAAOa,OAA4B,EAAsB;MAAAxC,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACjG,IAAI,CAACG,aAAa,EAAE;QAAAN,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAG,CAAA;QAClB,MAAM,IAAIiC,KAAK,CAAC,sCAAsC,CAAC;MACzD,CAAC;QAAApC,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MAED,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACFsB,QAAQ,CAAC,IAAI,CAAC;QAACzB,cAAA,GAAAG,CAAA;QACf,aAAaN,mBAAmB,CAACyC,qBAAqB,CAACE,OAAO,CAAC;MACjE,CAAC,CAAC,OAAON,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,UAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,UAAG,6BAA6B;QAAC7B,cAAA,GAAAG,CAAA;QACxFsB,QAAQ,CAACU,YAAY,CAAC;QAACnC,cAAA,GAAAG,CAAA;QACvB,MAAM+B,GAAG;MACX;IACF,CAAC;IAAA,iBAAAO,EAAA;MAAA,OAAAF,KAAA,CAAAG,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAACrC,aAAa,CAAC,CAAC;EAKnB,IAAMsC,oBAAoB,IAAA5C,cAAA,GAAAG,CAAA,QAAGR,WAAW;IAAA,IAAAkD,KAAA,GAAAlB,iBAAA,CAAC,WACvCa,OAA4B,EAC5BM,OAA+C,EAC3B;MAAA9C,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACpB,IAAI,CAACG,aAAa,EAAE;QAAAN,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAG,CAAA;QAClB,MAAM,IAAIiC,KAAK,CAAC,sCAAsC,CAAC;MACzD,CAAC;QAAApC,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MAED,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACFsB,QAAQ,CAAC,IAAI,CAAC;QAACzB,cAAA,GAAAG,CAAA;QACf,aAAaN,mBAAmB,CAAC+C,oBAAoB,CAACJ,OAAO,EAAEM,OAAO,CAAC;MACzE,CAAC,CAAC,OAAOZ,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,UAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,UAAG,iCAAiC;QAAC7B,cAAA,GAAAG,CAAA;QAC5FsB,QAAQ,CAACU,YAAY,CAAC;QAACnC,cAAA,GAAAG,CAAA;QACvB,MAAM+B,GAAG;MACX;IACF,CAAC;IAAA,iBAAAa,GAAA,EAAAC,GAAA;MAAA,OAAAH,KAAA,CAAAH,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAACrC,aAAa,CAAC,CAAC;EAKnB,IAAM2C,kBAAkB,IAAAjD,cAAA,GAAAG,CAAA,QAAGR,WAAW;IAAA,IAAAuD,MAAA,GAAAvB,iBAAA,CAAC,WAAOwB,cAAsB,EAAoB;MAAAnD,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACtF,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACFsB,QAAQ,CAAC,IAAI,CAAC;QAACzB,cAAA,GAAAG,CAAA;QACf,MAAMN,mBAAmB,CAACoD,kBAAkB,CAACE,cAAc,CAAC;MAC9D,CAAC,CAAC,OAAOjB,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,UAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,UAAG,+BAA+B;QAAC7B,cAAA,GAAAG,CAAA;QAC1FsB,QAAQ,CAACU,YAAY,CAAC;QAACnC,cAAA,GAAAG,CAAA;QACvB,MAAM+B,GAAG;MACX;IACF,CAAC;IAAA,iBAAAkB,GAAA;MAAA,OAAAF,MAAA,CAAAR,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAKN,IAAMU,iBAAiB,IAAArD,cAAA,GAAAG,CAAA,QAAGR,WAAW,CAAAgC,iBAAA,CAAC,aAA2B;IAAA3B,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IAC/D,IAAI,CAACG,aAAa,EAAE;MAAAN,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAG,CAAA;MAClB,MAAM,IAAIiC,KAAK,CAAC,sCAAsC,CAAC;IACzD,CAAC;MAAApC,cAAA,GAAA6B,CAAA;IAAA;IAAA7B,cAAA,GAAAG,CAAA;IAED,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACFsB,QAAQ,CAAC,IAAI,CAAC;MAACzB,cAAA,GAAAG,CAAA;MACf,MAAMN,mBAAmB,CAACwD,iBAAiB,CAAC,CAAC;IAC/C,CAAC,CAAC,OAAOnB,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,UAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,UAAG,+BAA+B;MAAC7B,cAAA,GAAAG,CAAA;MAC1FsB,QAAQ,CAACU,YAAY,CAAC;MAACnC,cAAA,GAAAG,CAAA;MACvB,MAAM+B,GAAG;IACX;EACF,CAAC,GAAE,CAAC5B,aAAa,CAAC,CAAC;EAKnB,IAAMgD,wBAAwB,IAAAtD,cAAA,GAAAG,CAAA,QAAGR,WAAW;IAAA,IAAA4D,MAAA,GAAA5B,iBAAA,CAAC,WAC3C6B,KAAa,EACbnB,OAAe,EACfoB,aAAmB,EACC;MAAAzD,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACpB,IAAI,CAACG,aAAa,EAAE;QAAAN,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAG,CAAA;QAClB,MAAM,IAAIiC,KAAK,CAAC,sCAAsC,CAAC;MACzD,CAAC;QAAApC,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MAED,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACFsB,QAAQ,CAAC,IAAI,CAAC;QAACzB,cAAA,GAAAG,CAAA;QACf,aAAaN,mBAAmB,CAACyD,wBAAwB,CAACE,KAAK,EAAEnB,OAAO,EAAEoB,aAAa,CAAC;MAC1F,CAAC,CAAC,OAAOvB,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,WAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,WAAG,sCAAsC;QAAC7B,cAAA,GAAAG,CAAA;QACjGsB,QAAQ,CAACU,YAAY,CAAC;QAACnC,cAAA,GAAAG,CAAA;QACvB,MAAM+B,GAAG;MACX;IACF,CAAC;IAAA,iBAAAwB,GAAA,EAAAC,GAAA,EAAAC,GAAA;MAAA,OAAAL,MAAA,CAAAb,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAACrC,aAAa,CAAC,CAAC;EAKnB,IAAMuD,2BAA2B,IAAA7D,cAAA,GAAAG,CAAA,QAAGR,WAAW;IAAA,IAAAmE,MAAA,GAAAnC,iBAAA,CAAC,WAC9CoC,gBAAwB,EACxBC,WAAmB,EACD;MAAAhE,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MAClB,IAAI,CAACG,aAAa,EAAE;QAAAN,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAG,CAAA;QAClB,MAAM,IAAIiC,KAAK,CAAC,sCAAsC,CAAC;MACzD,CAAC;QAAApC,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MAED,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACFsB,QAAQ,CAAC,IAAI,CAAC;QAACzB,cAAA,GAAAG,CAAA;QACf,MAAMN,mBAAmB,CAACgE,2BAA2B,CAACE,gBAAgB,EAAEC,WAAW,CAAC;MACtF,CAAC,CAAC,OAAO9B,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,WAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,WAAG,yCAAyC;QAAC7B,cAAA,GAAAG,CAAA;QACpGsB,QAAQ,CAACU,YAAY,CAAC;QAACnC,cAAA,GAAAG,CAAA;QACvB,MAAM+B,GAAG;MACX;IACF,CAAC;IAAA,iBAAA+B,GAAA,EAAAC,GAAA;MAAA,OAAAJ,MAAA,CAAApB,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAACrC,aAAa,CAAC,CAAC;EAKnB,IAAM6D,kBAAkB,IAAAnE,cAAA,GAAAG,CAAA,QAAGR,WAAW;IAAA,IAAAyE,MAAA,GAAAzC,iBAAA,CAAC,WACrC0C,SAAiB,EACjBC,SAAiB,EACjBC,SAAiB,EACC;MAAAvE,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MAClB,IAAI,CAACG,aAAa,EAAE;QAAAN,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAG,CAAA;QAClB,MAAM,IAAIiC,KAAK,CAAC,sCAAsC,CAAC;MACzD,CAAC;QAAApC,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MAED,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACFsB,QAAQ,CAAC,IAAI,CAAC;QAACzB,cAAA,GAAAG,CAAA;QACf,MAAMN,mBAAmB,CAACsE,kBAAkB,CAACE,SAAS,EAAEC,SAAS,EAAEC,SAAS,CAAC;MAC/E,CAAC,CAAC,OAAOrC,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,WAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,WAAG,gCAAgC;QAAC7B,cAAA,GAAAG,CAAA;QAC3FsB,QAAQ,CAACU,YAAY,CAAC;QAACnC,cAAA,GAAAG,CAAA;QACvB,MAAM+B,GAAG;MACX;IACF,CAAC;IAAA,iBAAAsC,GAAA,EAAAC,GAAA,EAAAC,IAAA;MAAA,OAAAN,MAAA,CAAA1B,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAACrC,aAAa,CAAC,CAAC;EAKnB,IAAMqE,qBAAqB,IAAA3E,cAAA,GAAAG,CAAA,QAAGR,WAAW;IAAA,IAAAiF,MAAA,GAAAjD,iBAAA,CAAC,WACxCkD,YAAoB,EACpBC,SAAe,EACfC,QAAiB,EACG;MAAA/E,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACpB,IAAI,CAACG,aAAa,EAAE;QAAAN,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAG,CAAA;QAClB,MAAM,IAAIiC,KAAK,CAAC,sCAAsC,CAAC;MACzD,CAAC;QAAApC,cAAA,GAAA6B,CAAA;MAAA;MAAA7B,cAAA,GAAAG,CAAA;MAED,IAAI;QAAAH,cAAA,GAAAG,CAAA;QACFsB,QAAQ,CAAC,IAAI,CAAC;QAACzB,cAAA,GAAAG,CAAA;QACf,aAAaN,mBAAmB,CAAC8E,qBAAqB,CAACE,YAAY,EAAEC,SAAS,EAAEC,QAAQ,CAAC;MAC3F,CAAC,CAAC,OAAO7C,GAAG,EAAE;QACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,WAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,WAAG,mCAAmC;QAAC7B,cAAA,GAAAG,CAAA;QAC9FsB,QAAQ,CAACU,YAAY,CAAC;QAACnC,cAAA,GAAAG,CAAA;QACvB,MAAM+B,GAAG;MACX;IACF,CAAC;IAAA,iBAAA8C,IAAA,EAAAC,IAAA,EAAAC,IAAA;MAAA,OAAAN,MAAA,CAAAlC,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAACrC,aAAa,CAAC,CAAC;EAKnB,IAAM6E,cAAc,IAAAnF,cAAA,GAAAG,CAAA,QAAGR,WAAW,CAAC,UAACyF,WAA0C,EAAW;IAAApF,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACvF,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACFsB,QAAQ,CAAC,IAAI,CAAC;MAACzB,cAAA,GAAAG,CAAA;MACfN,mBAAmB,CAACsF,cAAc,CAACC,WAAW,CAAC;MAC/C,IAAMC,eAAe,IAAArF,cAAA,GAAAG,CAAA,QAAGN,mBAAmB,CAACoC,WAAW,CAAC,CAAC;MAACjC,cAAA,GAAAG,CAAA;MAC1Dc,WAAW,CAACoE,eAAe,CAAC;IAC9B,CAAC,CAAC,OAAOnD,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,QAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,WAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,WAAG,2BAA2B;MAAC7B,cAAA,GAAAG,CAAA;MACtFsB,QAAQ,CAACU,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,EAAE,CAAC;EAKN,IAAMmD,yBAAyB,IAAAtF,cAAA,GAAAG,CAAA,QAAGR,WAAW,CAAAgC,iBAAA,CAAC,aAA0D;IAAA3B,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACtG,IAAI;MAAAH,cAAA,GAAAG,CAAA;MACFsB,QAAQ,CAAC,IAAI,CAAC;MAACzB,cAAA,GAAAG,CAAA;MACf,aAAaN,mBAAmB,CAACyF,yBAAyB,CAAC,CAAC;IAC9D,CAAC,CAAC,OAAOpD,GAAG,EAAE;MACZ,IAAMC,YAAY,IAAAnC,cAAA,GAAAG,CAAA,SAAG+B,GAAG,YAAYE,KAAK,IAAApC,cAAA,GAAA6B,CAAA,WAAGK,GAAG,CAACG,OAAO,KAAArC,cAAA,GAAA6B,CAAA,WAAG,uCAAuC;MAAC7B,cAAA,GAAAG,CAAA;MAClGsB,QAAQ,CAACU,YAAY,CAAC;MAACnC,cAAA,GAAAG,CAAA;MACvB,OAAO,EAAE;IACX;EACF,CAAC,GAAE,EAAE,CAAC;EAACH,cAAA,GAAAG,CAAA;EAGPP,SAAS,CAAC,YAAM;IAAAI,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACduB,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACA,UAAU,CAAC,CAAC;EAAC1B,cAAA,GAAAG,CAAA;EAGjBP,SAAS,CAAC,YAAM;IAAAI,cAAA,GAAAC,CAAA;IAAAD,cAAA,GAAAG,CAAA;IACd,IAAI,CAACG,aAAa,EAAE;MAAAN,cAAA,GAAA6B,CAAA;MAAA7B,cAAA,GAAAG,CAAA;MAAA;IAAM,CAAC;MAAAH,cAAA,GAAA6B,CAAA;IAAA;IAG3B,IAAM0D,oBAAoB,IAAAvF,cAAA,GAAAG,CAAA,SAAGL,aAAa,CAAC0F,+BAA+B,CAAC,UAAAC,YAAY,EAAI;MAAAzF,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACzFuF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,YAAY,CAAC;IACrD,CAAC,CAAC;IAGF,IAAMG,gBAAgB,IAAA5F,cAAA,GAAAG,CAAA,SAAGL,aAAa,CAAC+F,uCAAuC,CAAC,UAAAC,QAAQ,EAAI;MAAA9F,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACzFuF,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,QAAQ,CAAC;MAG/C,IAAMC,gBAAgB,IAAA/F,cAAA,GAAAG,CAAA,SAAG2F,QAAQ,CAACL,YAAY,CAACO,OAAO,CAACxD,OAAO,CAACyD,IAAI;MAACjG,cAAA,GAAAG,CAAA;MACpE,IAAI4F,gBAAgB,YAAhBA,gBAAgB,CAAEG,IAAI,EAAE;QAAAlG,cAAA,GAAA6B,CAAA;QAAA7B,cAAA,GAAAG,CAAA;QAE1BuF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEI,gBAAgB,CAACG,IAAI,CAAC;MAC1D,CAAC;QAAAlG,cAAA,GAAA6B,CAAA;MAAA;IACH,CAAC,CAAC;IAAC7B,cAAA,GAAAG,CAAA;IAEH,OAAO,YAAM;MAAAH,cAAA,GAAAC,CAAA;MAAAD,cAAA,GAAAG,CAAA;MACXL,aAAa,CAACqG,8BAA8B,CAACZ,oBAAoB,CAAC;MAACvF,cAAA,GAAAG,CAAA;MACnEL,aAAa,CAACqG,8BAA8B,CAACP,gBAAgB,CAAC;IAChE,CAAC;EACH,CAAC,EAAE,CAACtF,aAAa,CAAC,CAAC;EAACN,cAAA,GAAAG,CAAA;EAEpB,OAAO;IAELG,aAAa,EAAbA,aAAa;IACbU,QAAQ,EAARA,QAAQ;IACRI,SAAS,EAATA,SAAS;IAGTM,UAAU,EAAVA,UAAU;IACVY,qBAAqB,EAArBA,qBAAqB;IACrBM,oBAAoB,EAApBA,oBAAoB;IACpBK,kBAAkB,EAAlBA,kBAAkB;IAGlBI,iBAAiB,EAAjBA,iBAAiB;IACjBC,wBAAwB,EAAxBA,wBAAwB;IACxBO,2BAA2B,EAA3BA,2BAA2B;IAC3BM,kBAAkB,EAAlBA,kBAAkB;IAClBQ,qBAAqB,EAArBA,qBAAqB;IAGrBQ,cAAc,EAAdA,cAAc;IACdG,yBAAyB,EAAzBA,yBAAyB;IAGzB9D,KAAK,EAALA;EACF,CAAC;AACH", "ignoreList": []}