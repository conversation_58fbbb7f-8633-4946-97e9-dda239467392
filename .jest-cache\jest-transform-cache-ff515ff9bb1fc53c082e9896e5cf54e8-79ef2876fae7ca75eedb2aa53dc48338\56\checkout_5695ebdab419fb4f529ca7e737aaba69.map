{"version": 3, "names": ["React", "useState", "View", "Text", "StyleSheet", "ScrollView", "<PERSON><PERSON>", "SafeAreaView", "router", "useLocalSearchParams", "ArrowLeft", "CreditCard", "Shield", "<PERSON><PERSON>", "Card", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_9qc468kxs", "s", "primary", "white", "dark", "gray", "lightGray", "CheckoutScreen", "f", "params", "_ref", "_ref2", "_slicedToArray", "loading", "setLoading", "planDetails", "name", "b", "plan", "price", "period", "handlePayment", "_ref3", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "alert", "text", "onPress", "replace", "error", "apply", "arguments", "style", "styles", "container", "children", "header", "title", "back", "variant", "backButton", "size", "color", "headerTitle", "placeholder", "content", "planCard", "planName", "planPrice", "planDescription", "paymentCard", "<PERSON><PERSON><PERSON><PERSON>", "cardTitle", "cardInfo", "cardExpiry", "securityCard", "securityH<PERSON>er", "securityTitle", "securityText", "footer", "disabled", "payButton", "create", "flex", "backgroundColor", "flexDirection", "alignItems", "justifyContent", "paddingHorizontal", "paddingVertical", "borderBottomWidth", "borderBottomColor", "width", "height", "fontSize", "fontFamily", "padding", "marginBottom", "textAlign", "marginLeft", "borderTopWidth", "borderTopColor"], "sources": ["checkout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { router, useLocalSearchParams } from 'expo-router';\nimport { ArrowLeft, CreditCard, Shield } from 'lucide-react-native';\n\nimport Button from '@/components/ui/Button';\nimport Card from '@/components/ui/Card';\n\nconst colors = {\n  primary: '#23ba16',\n  white: '#ffffff',\n  dark: '#171717',\n  gray: '#6b7280',\n  lightGray: '#f9fafb',\n};\n\nexport default function CheckoutScreen() {\n  const params = useLocalSearchParams();\n  const [loading, setLoading] = useState(false);\n\n  const planDetails = {\n    name: params.plan || 'Premium Plan',\n    price: params.price || '$9.99',\n    period: params.period || 'month',\n  };\n\n  const handlePayment = async () => {\n    setLoading(true);\n    try {\n      // Simulate payment processing\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      Alert.alert(\n        'Payment Successful',\n        'Your subscription has been activated!',\n        [\n          {\n            text: 'OK',\n            onPress: () => router.replace('/(tabs)/profile'),\n          },\n        ]\n      );\n    } catch (error) {\n      Alert.alert('Payment Failed', 'Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.header}>\n        <Button\n          title=\"\"\n          onPress={() => router.back()}\n          variant=\"ghost\"\n          style={styles.backButton}\n        >\n          <ArrowLeft size={24} color={colors.dark} />\n        </Button>\n        <Text style={styles.headerTitle}>Checkout</Text>\n        <View style={styles.placeholder} />\n      </View>\n\n      <ScrollView style={styles.content}>\n        <Card style={styles.planCard}>\n          <Text style={styles.planName}>{planDetails.name}</Text>\n          <Text style={styles.planPrice}>\n            {planDetails.price}/{planDetails.period}\n          </Text>\n          <Text style={styles.planDescription}>\n            Full access to all premium features\n          </Text>\n        </Card>\n\n        <Card style={styles.paymentCard}>\n          <View style={styles.cardHeader}>\n            <CreditCard size={24} color={colors.primary} />\n            <Text style={styles.cardTitle}>Payment Method</Text>\n          </View>\n          <Text style={styles.cardInfo}>**** **** **** 1234</Text>\n          <Text style={styles.cardExpiry}>Expires 12/25</Text>\n        </Card>\n\n        <Card style={styles.securityCard}>\n          <View style={styles.securityHeader}>\n            <Shield size={24} color={colors.primary} />\n            <Text style={styles.securityTitle}>Secure Payment</Text>\n          </View>\n          <Text style={styles.securityText}>\n            Your payment information is encrypted and secure\n          </Text>\n        </Card>\n      </ScrollView>\n\n      <View style={styles.footer}>\n        <Button\n          title={loading ? 'Processing...' : `Pay ${planDetails.price}`}\n          onPress={handlePayment}\n          disabled={loading}\n          style={styles.payButton}\n        />\n      </View>\n    </SafeAreaView>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: colors.lightGray,\n  },\n  header: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingHorizontal: 20,\n    paddingVertical: 16,\n    backgroundColor: colors.white,\n    borderBottomWidth: 1,\n    borderBottomColor: colors.lightGray,\n  },\n  backButton: {\n    width: 40,\n    height: 40,\n  },\n  headerTitle: {\n    fontSize: 18,\n    fontFamily: 'Inter-SemiBold',\n    color: colors.dark,\n  },\n  placeholder: {\n    width: 40,\n  },\n  content: {\n    flex: 1,\n    padding: 20,\n  },\n  planCard: {\n    marginBottom: 20,\n    alignItems: 'center',\n    padding: 24,\n  },\n  planName: {\n    fontSize: 24,\n    fontFamily: 'Inter-Bold',\n    color: colors.dark,\n    marginBottom: 8,\n  },\n  planPrice: {\n    fontSize: 32,\n    fontFamily: 'Inter-Bold',\n    color: colors.primary,\n    marginBottom: 8,\n  },\n  planDescription: {\n    fontSize: 16,\n    fontFamily: 'Inter-Regular',\n    color: colors.gray,\n    textAlign: 'center',\n  },\n  paymentCard: {\n    marginBottom: 20,\n    padding: 20,\n  },\n  cardHeader: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: 16,\n  },\n  cardTitle: {\n    fontSize: 18,\n    fontFamily: 'Inter-SemiBold',\n    color: colors.dark,\n    marginLeft: 12,\n  },\n  cardInfo: {\n    fontSize: 16,\n    fontFamily: 'Inter-Medium',\n    color: colors.dark,\n    marginBottom: 4,\n  },\n  cardExpiry: {\n    fontSize: 14,\n    fontFamily: 'Inter-Regular',\n    color: colors.gray,\n  },\n  securityCard: {\n    marginBottom: 20,\n    padding: 20,\n  },\n  securityHeader: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: 12,\n  },\n  securityTitle: {\n    fontSize: 16,\n    fontFamily: 'Inter-SemiBold',\n    color: colors.dark,\n    marginLeft: 12,\n  },\n  securityText: {\n    fontSize: 14,\n    fontFamily: 'Inter-Regular',\n    color: colors.gray,\n  },\n  footer: {\n    padding: 20,\n    backgroundColor: colors.white,\n    borderTopWidth: 1,\n    borderTopColor: colors.lightGray,\n  },\n  payButton: {\n    width: '100%',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAEC,KAAK,QAAQ,cAAc;AACxE,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,MAAM,EAAEC,oBAAoB,QAAQ,aAAa;AAC1D,SAASC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,qBAAqB;AAEnE,OAAOC,MAAM;AACb,OAAOC,IAAI;AAA6B,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAExC,IAAMC,MAAM,IAAAC,aAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE;AACb,CAAC;AAED,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAP,aAAA,GAAAQ,CAAA;EACvC,IAAMC,MAAM,IAAAT,aAAA,GAAAC,CAAA,OAAGZ,oBAAoB,CAAC,CAAC;EACrC,IAAAqB,IAAA,IAAAV,aAAA,GAAAC,CAAA,OAA8BpB,QAAQ,CAAC,KAAK,CAAC;IAAA8B,KAAA,GAAAC,cAAA,CAAAF,IAAA;IAAtCG,OAAO,GAAAF,KAAA;IAAEG,UAAU,GAAAH,KAAA;EAE1B,IAAMI,WAAW,IAAAf,aAAA,GAAAC,CAAA,OAAG;IAClBe,IAAI,EAAE,CAAAhB,aAAA,GAAAiB,CAAA,UAAAR,MAAM,CAACS,IAAI,MAAAlB,aAAA,GAAAiB,CAAA,UAAI,cAAc;IACnCE,KAAK,EAAE,CAAAnB,aAAA,GAAAiB,CAAA,UAAAR,MAAM,CAACU,KAAK,MAAAnB,aAAA,GAAAiB,CAAA,UAAI,OAAO;IAC9BG,MAAM,EAAE,CAAApB,aAAA,GAAAiB,CAAA,UAAAR,MAAM,CAACW,MAAM,MAAApB,aAAA,GAAAiB,CAAA,UAAI,OAAO;EAClC,CAAC;EAACjB,aAAA,GAAAC,CAAA;EAEF,IAAMoB,aAAa;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAAAvB,aAAA,GAAAQ,CAAA;MAAAR,aAAA,GAAAC,CAAA;MAChCa,UAAU,CAAC,IAAI,CAAC;MAACd,aAAA,GAAAC,CAAA;MACjB,IAAI;QAAAD,aAAA,GAAAC,CAAA;QAEF,MAAM,IAAIuB,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAAzB,aAAA,GAAAQ,CAAA;UAAAR,aAAA,GAAAC,CAAA;UAAA,OAAAyB,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QAAD,CAAC,CAAC;QAACzB,aAAA,GAAAC,CAAA;QAExDf,KAAK,CAACyC,KAAK,CACT,oBAAoB,EACpB,uCAAuC,EACvC,CACE;UACEC,IAAI,EAAE,IAAI;UACVC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;YAAA7B,aAAA,GAAAQ,CAAA;YAAAR,aAAA,GAAAC,CAAA;YAAA,OAAAb,MAAM,CAAC0C,OAAO,CAAC,iBAAiB,CAAC;UAAD;QACjD,CAAC,CAEL,CAAC;MACH,CAAC,CAAC,OAAOC,KAAK,EAAE;QAAA/B,aAAA,GAAAC,CAAA;QACdf,KAAK,CAACyC,KAAK,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;MACpD,CAAC,SAAS;QAAA3B,aAAA,GAAAC,CAAA;QACRa,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBArBKO,aAAaA,CAAA;MAAA,OAAAC,KAAA,CAAAU,KAAA,OAAAC,SAAA;IAAA;EAAA,GAqBlB;EAACjC,aAAA,GAAAC,CAAA;EAEF,OACEH,KAAA,CAACX,YAAY;IAAC+C,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GACpCvC,KAAA,CAAChB,IAAI;MAACoD,KAAK,EAAEC,MAAM,CAACG,MAAO;MAAAD,QAAA,GACzBzC,IAAA,CAACH,MAAM;QACL8C,KAAK,EAAC,EAAE;QACRV,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;UAAA7B,aAAA,GAAAQ,CAAA;UAAAR,aAAA,GAAAC,CAAA;UAAA,OAAAb,MAAM,CAACoD,IAAI,CAAC,CAAC;QAAD,CAAE;QAC7BC,OAAO,EAAC,OAAO;QACfP,KAAK,EAAEC,MAAM,CAACO,UAAW;QAAAL,QAAA,EAEzBzC,IAAA,CAACN,SAAS;UAACqD,IAAI,EAAE,EAAG;UAACC,KAAK,EAAE7C,MAAM,CAACK;QAAK,CAAE;MAAC,CACrC,CAAC,EACTR,IAAA,CAACb,IAAI;QAACmD,KAAK,EAAEC,MAAM,CAACU,WAAY;QAAAR,QAAA,EAAC;MAAQ,CAAM,CAAC,EAChDzC,IAAA,CAACd,IAAI;QAACoD,KAAK,EAAEC,MAAM,CAACW;MAAY,CAAE,CAAC;IAAA,CAC/B,CAAC,EAEPhD,KAAA,CAACb,UAAU;MAACiD,KAAK,EAAEC,MAAM,CAACY,OAAQ;MAAAV,QAAA,GAChCvC,KAAA,CAACJ,IAAI;QAACwC,KAAK,EAAEC,MAAM,CAACa,QAAS;QAAAX,QAAA,GAC3BzC,IAAA,CAACb,IAAI;UAACmD,KAAK,EAAEC,MAAM,CAACc,QAAS;UAAAZ,QAAA,EAAEtB,WAAW,CAACC;QAAI,CAAO,CAAC,EACvDlB,KAAA,CAACf,IAAI;UAACmD,KAAK,EAAEC,MAAM,CAACe,SAAU;UAAAb,QAAA,GAC3BtB,WAAW,CAACI,KAAK,EAAC,GAAC,EAACJ,WAAW,CAACK,MAAM;QAAA,CACnC,CAAC,EACPxB,IAAA,CAACb,IAAI;UAACmD,KAAK,EAAEC,MAAM,CAACgB,eAAgB;UAAAd,QAAA,EAAC;QAErC,CAAM,CAAC;MAAA,CACH,CAAC,EAEPvC,KAAA,CAACJ,IAAI;QAACwC,KAAK,EAAEC,MAAM,CAACiB,WAAY;QAAAf,QAAA,GAC9BvC,KAAA,CAAChB,IAAI;UAACoD,KAAK,EAAEC,MAAM,CAACkB,UAAW;UAAAhB,QAAA,GAC7BzC,IAAA,CAACL,UAAU;YAACoD,IAAI,EAAE,EAAG;YAACC,KAAK,EAAE7C,MAAM,CAACG;UAAQ,CAAE,CAAC,EAC/CN,IAAA,CAACb,IAAI;YAACmD,KAAK,EAAEC,MAAM,CAACmB,SAAU;YAAAjB,QAAA,EAAC;UAAc,CAAM,CAAC;QAAA,CAChD,CAAC,EACPzC,IAAA,CAACb,IAAI;UAACmD,KAAK,EAAEC,MAAM,CAACoB,QAAS;UAAAlB,QAAA,EAAC;QAAmB,CAAM,CAAC,EACxDzC,IAAA,CAACb,IAAI;UAACmD,KAAK,EAAEC,MAAM,CAACqB,UAAW;UAAAnB,QAAA,EAAC;QAAa,CAAM,CAAC;MAAA,CAChD,CAAC,EAEPvC,KAAA,CAACJ,IAAI;QAACwC,KAAK,EAAEC,MAAM,CAACsB,YAAa;QAAApB,QAAA,GAC/BvC,KAAA,CAAChB,IAAI;UAACoD,KAAK,EAAEC,MAAM,CAACuB,cAAe;UAAArB,QAAA,GACjCzC,IAAA,CAACJ,MAAM;YAACmD,IAAI,EAAE,EAAG;YAACC,KAAK,EAAE7C,MAAM,CAACG;UAAQ,CAAE,CAAC,EAC3CN,IAAA,CAACb,IAAI;YAACmD,KAAK,EAAEC,MAAM,CAACwB,aAAc;YAAAtB,QAAA,EAAC;UAAc,CAAM,CAAC;QAAA,CACpD,CAAC,EACPzC,IAAA,CAACb,IAAI;UAACmD,KAAK,EAAEC,MAAM,CAACyB,YAAa;UAAAvB,QAAA,EAAC;QAElC,CAAM,CAAC;MAAA,CACH,CAAC;IAAA,CACG,CAAC,EAEbzC,IAAA,CAACd,IAAI;MAACoD,KAAK,EAAEC,MAAM,CAAC0B,MAAO;MAAAxB,QAAA,EACzBzC,IAAA,CAACH,MAAM;QACL8C,KAAK,EAAE1B,OAAO,IAAAb,aAAA,GAAAiB,CAAA,UAAG,eAAe,KAAAjB,aAAA,GAAAiB,CAAA,UAAG,OAAOF,WAAW,CAACI,KAAK,EAAE,CAAC;QAC9DU,OAAO,EAAER,aAAc;QACvByC,QAAQ,EAAEjD,OAAQ;QAClBqB,KAAK,EAAEC,MAAM,CAAC4B;MAAU,CACzB;IAAC,CACE,CAAC;EAAA,CACK,CAAC;AAEnB;AAEA,IAAM5B,MAAM,IAAAnC,aAAA,GAAAC,CAAA,QAAGjB,UAAU,CAACgF,MAAM,CAAC;EAC/B5B,SAAS,EAAE;IACT6B,IAAI,EAAE,CAAC;IACPC,eAAe,EAAEnE,MAAM,CAACO;EAC1B,CAAC;EACDgC,MAAM,EAAE;IACN6B,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,EAAE;IACnBL,eAAe,EAAEnE,MAAM,CAACI,KAAK;IAC7BqE,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAE1E,MAAM,CAACO;EAC5B,CAAC;EACDoC,UAAU,EAAE;IACVgC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE;EACV,CAAC;EACD9B,WAAW,EAAE;IACX+B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,gBAAgB;IAC5BjC,KAAK,EAAE7C,MAAM,CAACK;EAChB,CAAC;EACD0C,WAAW,EAAE;IACX4B,KAAK,EAAE;EACT,CAAC;EACD3B,OAAO,EAAE;IACPkB,IAAI,EAAE,CAAC;IACPa,OAAO,EAAE;EACX,CAAC;EACD9B,QAAQ,EAAE;IACR+B,YAAY,EAAE,EAAE;IAChBX,UAAU,EAAE,QAAQ;IACpBU,OAAO,EAAE;EACX,CAAC;EACD7B,QAAQ,EAAE;IACR2B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,YAAY;IACxBjC,KAAK,EAAE7C,MAAM,CAACK,IAAI;IAClB2E,YAAY,EAAE;EAChB,CAAC;EACD7B,SAAS,EAAE;IACT0B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,YAAY;IACxBjC,KAAK,EAAE7C,MAAM,CAACG,OAAO;IACrB6E,YAAY,EAAE;EAChB,CAAC;EACD5B,eAAe,EAAE;IACfyB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3BjC,KAAK,EAAE7C,MAAM,CAACM,IAAI;IAClB2E,SAAS,EAAE;EACb,CAAC;EACD5B,WAAW,EAAE;IACX2B,YAAY,EAAE,EAAE;IAChBD,OAAO,EAAE;EACX,CAAC;EACDzB,UAAU,EAAE;IACVc,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBW,YAAY,EAAE;EAChB,CAAC;EACDzB,SAAS,EAAE;IACTsB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,gBAAgB;IAC5BjC,KAAK,EAAE7C,MAAM,CAACK,IAAI;IAClB6E,UAAU,EAAE;EACd,CAAC;EACD1B,QAAQ,EAAE;IACRqB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,cAAc;IAC1BjC,KAAK,EAAE7C,MAAM,CAACK,IAAI;IAClB2E,YAAY,EAAE;EAChB,CAAC;EACDvB,UAAU,EAAE;IACVoB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3BjC,KAAK,EAAE7C,MAAM,CAACM;EAChB,CAAC;EACDoD,YAAY,EAAE;IACZsB,YAAY,EAAE,EAAE;IAChBD,OAAO,EAAE;EACX,CAAC;EACDpB,cAAc,EAAE;IACdS,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBW,YAAY,EAAE;EAChB,CAAC;EACDpB,aAAa,EAAE;IACbiB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,gBAAgB;IAC5BjC,KAAK,EAAE7C,MAAM,CAACK,IAAI;IAClB6E,UAAU,EAAE;EACd,CAAC;EACDrB,YAAY,EAAE;IACZgB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3BjC,KAAK,EAAE7C,MAAM,CAACM;EAChB,CAAC;EACDwD,MAAM,EAAE;IACNiB,OAAO,EAAE,EAAE;IACXZ,eAAe,EAAEnE,MAAM,CAACI,KAAK;IAC7B+E,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAEpF,MAAM,CAACO;EACzB,CAAC;EACDyD,SAAS,EAAE;IACTW,KAAK,EAAE;EACT;AACF,CAAC,CAAC", "ignoreList": []}