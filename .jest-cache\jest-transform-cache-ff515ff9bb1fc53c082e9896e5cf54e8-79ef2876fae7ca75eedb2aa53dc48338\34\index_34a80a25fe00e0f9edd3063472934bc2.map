{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_ModalPortal", "_ModalAnimation", "_ModalC<PERSON>nt", "_ModalFocusTrap", "_excluded", "uniqueModalIdentifier", "activeModalStack", "activeModalListeners", "notifyActiveModalListeners", "length", "activeModalId", "for<PERSON>ach", "modalId", "removeActiveModal", "index", "indexOf", "splice", "addActiveModal", "listener", "push", "Modal", "forwardRef", "props", "forwardedRef", "animationType", "children", "on<PERSON><PERSON><PERSON>", "onRequestClose", "onShow", "transparent", "_props$visible", "visible", "rest", "useMemo", "_React$useState", "useState", "isActive", "setIsActive", "onDismissCallback", "useCallback", "onShowCallback", "useEffect", "createElement", "active", "ref", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _ModalPortal = _interopRequireDefault(require(\"./ModalPortal\"));\nvar _ModalAnimation = _interopRequireDefault(require(\"./ModalAnimation\"));\nvar _ModalContent = _interopRequireDefault(require(\"./ModalContent\"));\nvar _ModalFocusTrap = _interopRequireDefault(require(\"./ModalFocusTrap\"));\nvar _excluded = [\"animationType\", \"children\", \"onDismiss\", \"onRequestClose\", \"onShow\", \"transparent\", \"visible\"];\nvar uniqueModalIdentifier = 0;\nvar activeModalStack = [];\nvar activeModalListeners = {};\nfunction notifyActiveModalListeners() {\n  if (activeModalStack.length === 0) {\n    return;\n  }\n  var activeModalId = activeModalStack[activeModalStack.length - 1];\n  activeModalStack.forEach(modalId => {\n    if (modalId in activeModalListeners) {\n      activeModalListeners[modalId](modalId === activeModalId);\n    }\n  });\n}\nfunction removeActiveModal(modalId) {\n  if (modalId in activeModalListeners) {\n    // Before removing this listener we should probably tell it\n    // that it's no longer the active modal for sure.\n    activeModalListeners[modalId](false);\n    delete activeModalListeners[modalId];\n  }\n  var index = activeModalStack.indexOf(modalId);\n  if (index !== -1) {\n    activeModalStack.splice(index, 1);\n    notifyActiveModalListeners();\n  }\n}\nfunction addActiveModal(modalId, listener) {\n  removeActiveModal(modalId);\n  activeModalStack.push(modalId);\n  activeModalListeners[modalId] = listener;\n  notifyActiveModalListeners();\n}\nvar Modal = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var animationType = props.animationType,\n    children = props.children,\n    onDismiss = props.onDismiss,\n    onRequestClose = props.onRequestClose,\n    onShow = props.onShow,\n    transparent = props.transparent,\n    _props$visible = props.visible,\n    visible = _props$visible === void 0 ? true : _props$visible,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n\n  // Set a unique model identifier so we can correctly route\n  // dismissals and check the layering of modals.\n  var modalId = React.useMemo(() => uniqueModalIdentifier++, []);\n  var _React$useState = React.useState(false),\n    isActive = _React$useState[0],\n    setIsActive = _React$useState[1];\n  var onDismissCallback = React.useCallback(() => {\n    removeActiveModal(modalId);\n    if (onDismiss) {\n      onDismiss();\n    }\n  }, [modalId, onDismiss]);\n  var onShowCallback = React.useCallback(() => {\n    addActiveModal(modalId, setIsActive);\n    if (onShow) {\n      onShow();\n    }\n  }, [modalId, onShow]);\n  React.useEffect(() => {\n    return () => removeActiveModal(modalId);\n  }, [modalId]);\n  return /*#__PURE__*/React.createElement(_ModalPortal.default, null, /*#__PURE__*/React.createElement(_ModalAnimation.default, {\n    animationType: animationType,\n    onDismiss: onDismissCallback,\n    onShow: onShowCallback,\n    visible: visible\n  }, /*#__PURE__*/React.createElement(_ModalFocusTrap.default, {\n    active: isActive\n  }, /*#__PURE__*/React.createElement(_ModalContent.default, (0, _extends2.default)({}, rest, {\n    active: isActive,\n    onRequestClose: onRequestClose,\n    ref: forwardedRef,\n    transparent: transparent\n  }), children))));\n});\nvar _default = exports.default = Modal;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,KAAK,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,YAAY,GAAGT,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AACnE,IAAIS,eAAe,GAAGV,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AACzE,IAAIU,aAAa,GAAGX,sBAAsB,CAACC,OAAO,iBAAiB,CAAC,CAAC;AACrE,IAAIW,eAAe,GAAGZ,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AACzE,IAAIY,SAAS,GAAG,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,CAAC;AAChH,IAAIC,qBAAqB,GAAG,CAAC;AAC7B,IAAIC,gBAAgB,GAAG,EAAE;AACzB,IAAIC,oBAAoB,GAAG,CAAC,CAAC;AAC7B,SAASC,0BAA0BA,CAAA,EAAG;EACpC,IAAIF,gBAAgB,CAACG,MAAM,KAAK,CAAC,EAAE;IACjC;EACF;EACA,IAAIC,aAAa,GAAGJ,gBAAgB,CAACA,gBAAgB,CAACG,MAAM,GAAG,CAAC,CAAC;EACjEH,gBAAgB,CAACK,OAAO,CAAC,UAAAC,OAAO,EAAI;IAClC,IAAIA,OAAO,IAAIL,oBAAoB,EAAE;MACnCA,oBAAoB,CAACK,OAAO,CAAC,CAACA,OAAO,KAAKF,aAAa,CAAC;IAC1D;EACF,CAAC,CAAC;AACJ;AACA,SAASG,iBAAiBA,CAACD,OAAO,EAAE;EAClC,IAAIA,OAAO,IAAIL,oBAAoB,EAAE;IAGnCA,oBAAoB,CAACK,OAAO,CAAC,CAAC,KAAK,CAAC;IACpC,OAAOL,oBAAoB,CAACK,OAAO,CAAC;EACtC;EACA,IAAIE,KAAK,GAAGR,gBAAgB,CAACS,OAAO,CAACH,OAAO,CAAC;EAC7C,IAAIE,KAAK,KAAK,CAAC,CAAC,EAAE;IAChBR,gBAAgB,CAACU,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;IACjCN,0BAA0B,CAAC,CAAC;EAC9B;AACF;AACA,SAASS,cAAcA,CAACL,OAAO,EAAEM,QAAQ,EAAE;EACzCL,iBAAiB,CAACD,OAAO,CAAC;EAC1BN,gBAAgB,CAACa,IAAI,CAACP,OAAO,CAAC;EAC9BL,oBAAoB,CAACK,OAAO,CAAC,GAAGM,QAAQ;EACxCV,0BAA0B,CAAC,CAAC;AAC9B;AACA,IAAIY,KAAK,GAAgBrB,KAAK,CAACsB,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EACjE,IAAIC,aAAa,GAAGF,KAAK,CAACE,aAAa;IACrCC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC3BC,cAAc,GAAGL,KAAK,CAACK,cAAc;IACrCC,MAAM,GAAGN,KAAK,CAACM,MAAM;IACrBC,WAAW,GAAGP,KAAK,CAACO,WAAW;IAC/BC,cAAc,GAAGR,KAAK,CAACS,OAAO;IAC9BA,OAAO,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,cAAc;IAC3DE,IAAI,GAAG,CAAC,CAAC,EAAElC,8BAA8B,CAACL,OAAO,EAAE6B,KAAK,EAAElB,SAAS,CAAC;EAItE,IAAIQ,OAAO,GAAGb,KAAK,CAACkC,OAAO,CAAC;IAAA,OAAM5B,qBAAqB,EAAE;EAAA,GAAE,EAAE,CAAC;EAC9D,IAAI6B,eAAe,GAAGnC,KAAK,CAACoC,QAAQ,CAAC,KAAK,CAAC;IACzCC,QAAQ,GAAGF,eAAe,CAAC,CAAC,CAAC;IAC7BG,WAAW,GAAGH,eAAe,CAAC,CAAC,CAAC;EAClC,IAAII,iBAAiB,GAAGvC,KAAK,CAACwC,WAAW,CAAC,YAAM;IAC9C1B,iBAAiB,CAACD,OAAO,CAAC;IAC1B,IAAIc,SAAS,EAAE;MACbA,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACd,OAAO,EAAEc,SAAS,CAAC,CAAC;EACxB,IAAIc,cAAc,GAAGzC,KAAK,CAACwC,WAAW,CAAC,YAAM;IAC3CtB,cAAc,CAACL,OAAO,EAAEyB,WAAW,CAAC;IACpC,IAAIT,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC;IACV;EACF,CAAC,EAAE,CAAChB,OAAO,EAAEgB,MAAM,CAAC,CAAC;EACrB7B,KAAK,CAAC0C,SAAS,CAAC,YAAM;IACpB,OAAO;MAAA,OAAM5B,iBAAiB,CAACD,OAAO,CAAC;IAAA;EACzC,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACb,OAAoBb,KAAK,CAAC2C,aAAa,CAAC1C,YAAY,CAACP,OAAO,EAAE,IAAI,EAAeM,KAAK,CAAC2C,aAAa,CAACzC,eAAe,CAACR,OAAO,EAAE;IAC5H+B,aAAa,EAAEA,aAAa;IAC5BE,SAAS,EAAEY,iBAAiB;IAC5BV,MAAM,EAAEY,cAAc;IACtBT,OAAO,EAAEA;EACX,CAAC,EAAehC,KAAK,CAAC2C,aAAa,CAACvC,eAAe,CAACV,OAAO,EAAE;IAC3DkD,MAAM,EAAEP;EACV,CAAC,EAAerC,KAAK,CAAC2C,aAAa,CAACxC,aAAa,CAACT,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEuC,IAAI,EAAE;IAC1FW,MAAM,EAAEP,QAAQ;IAChBT,cAAc,EAAEA,cAAc;IAC9BiB,GAAG,EAAErB,YAAY;IACjBM,WAAW,EAAEA;EACf,CAAC,CAAC,EAAEJ,QAAQ,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC;AACF,IAAIoB,QAAQ,GAAGlD,OAAO,CAACF,OAAO,GAAG2B,KAAK;AACtC0B,MAAM,CAACnD,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}