2c45629f4d05b7ab78eb34d210b71426
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var _compiler = require("./compiler");
var _dom = require("./dom");
var _transformLocalizeStyle = require("styleq/transform-localize-style");
var _preprocess = require("./preprocess");
var _styleq = require("styleq");
var _validate = require("./validate");
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
var _excluded = ["writingDirection"];
var staticStyleMap = new WeakMap();
var sheet = (0, _dom.createSheet)();
var defaultPreprocessOptions = {
  shadow: true,
  textShadow: true
};
function customStyleq(styles, options) {
  if (options === void 0) {
    options = {};
  }
  var _options = options,
    writingDirection = _options.writingDirection,
    preprocessOptions = (0, _objectWithoutPropertiesLoose2.default)(_options, _excluded);
  var isRTL = writingDirection === 'rtl';
  return _styleq.styleq.factory({
    transform: function transform(style) {
      var compiledStyle = staticStyleMap.get(style);
      if (compiledStyle != null) {
        return (0, _transformLocalizeStyle.localizeStyle)(compiledStyle, isRTL);
      }
      return (0, _preprocess.preprocess)(style, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, defaultPreprocessOptions), preprocessOptions));
    }
  })(styles);
}
function insertRules(compiledOrderedRules) {
  compiledOrderedRules.forEach(function (_ref) {
    var rules = _ref[0],
      order = _ref[1];
    if (sheet != null) {
      rules.forEach(function (rule) {
        sheet.insert(rule, order);
      });
    }
  });
}
function compileAndInsertAtomic(style) {
  var _atomic = (0, _compiler.atomic)((0, _preprocess.preprocess)(style, defaultPreprocessOptions)),
    compiledStyle = _atomic[0],
    compiledOrderedRules = _atomic[1];
  insertRules(compiledOrderedRules);
  return compiledStyle;
}
function compileAndInsertReset(style, key) {
  var _classic = (0, _compiler.classic)(style, key),
    compiledStyle = _classic[0],
    compiledOrderedRules = _classic[1];
  insertRules(compiledOrderedRules);
  return compiledStyle;
}
var absoluteFillObject = {
  position: 'absolute',
  left: 0,
  right: 0,
  top: 0,
  bottom: 0
};
var absoluteFill = create({
  x: (0, _objectSpread2.default)({}, absoluteFillObject)
}).x;
function create(styles) {
  Object.keys(styles).forEach(function (key) {
    var styleObj = styles[key];
    if (styleObj != null && styleObj.$$css !== true) {
      var compiledStyles;
      if (key.indexOf('$raw') > -1) {
        compiledStyles = compileAndInsertReset(styleObj, key.split('$raw')[0]);
      } else {
        if (process.env.NODE_ENV !== 'production') {
          (0, _validate.validate)(styleObj);
          styles[key] = Object.freeze(styleObj);
        }
        compiledStyles = compileAndInsertAtomic(styleObj);
      }
      staticStyleMap.set(styleObj, compiledStyles);
    }
  });
  return styles;
}
function compose(style1, style2) {
  if (process.env.NODE_ENV !== 'production') {
    var len = arguments.length;
    if (len > 2) {
      var readableStyles = Array.prototype.slice.call(arguments).map(function (a) {
        return flatten(a);
      });
      throw new Error("StyleSheet.compose() only accepts 2 arguments, received " + len + ": " + JSON.stringify(readableStyles));
    }
  }
  return [style1, style2];
}
function flatten() {
  for (var _len = arguments.length, styles = new Array(_len), _key = 0; _key < _len; _key++) {
    styles[_key] = arguments[_key];
  }
  var flatArray = styles.flat(Infinity);
  var result = {};
  for (var i = 0; i < flatArray.length; i++) {
    var style = flatArray[i];
    if (style != null && typeof style === 'object') {
      Object.assign(result, style);
    }
  }
  return result;
}
function getSheet() {
  return {
    id: sheet.id,
    textContent: sheet.getTextContent()
  };
}
function StyleSheet(styles, options) {
  if (options === void 0) {
    options = {};
  }
  var isRTL = options.writingDirection === 'rtl';
  var styleProps = customStyleq(styles, options);
  if (Array.isArray(styleProps) && styleProps[1] != null) {
    styleProps[1] = (0, _compiler.inline)(styleProps[1], isRTL);
  }
  return styleProps;
}
StyleSheet.absoluteFill = absoluteFill;
StyleSheet.absoluteFillObject = absoluteFillObject;
StyleSheet.create = create;
StyleSheet.compose = compose;
StyleSheet.flatten = flatten;
StyleSheet.getSheet = getSheet;
StyleSheet.hairlineWidth = 1;
if (_canUseDom.default && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.resolveRNStyle = StyleSheet.flatten;
}
var stylesheet = StyleSheet;
var _default = exports.default = stylesheet;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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