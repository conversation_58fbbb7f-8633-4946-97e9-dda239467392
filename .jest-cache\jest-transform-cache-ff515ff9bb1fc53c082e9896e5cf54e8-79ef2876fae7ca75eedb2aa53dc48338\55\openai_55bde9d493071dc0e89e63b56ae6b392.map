{"version": 3, "names": ["_openai", "_interopRequireDefault", "require", "_error<PERSON><PERSON><PERSON>", "getOpenAIClient", "<PERSON><PERSON><PERSON><PERSON>", "process", "env", "OPENAI_API_KEY", "_env2", "EXPO_PUBLIC_OPENAI_API_KEY", "includes", "console", "warn", "OpenAI", "dangerously<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeout", "maxRetries", "error", "<PERSON><PERSON><PERSON><PERSON>", "handle", "service", "action", "openai", "OpenAIService", "_classCallCheck2", "default", "_createClass2", "key", "value", "isConfigured", "_makeOpenAIRequest", "_asyncToGenerator2", "operation", "fallback", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "showUserError", "retryable", "onError", "then", "result", "makeOpenAIRequest", "_x", "_x2", "_x3", "apply", "arguments", "_generateCoachingAdvice", "request", "getFallbackCoachingResponse", "_completion$choices$", "prompt", "buildCoachingPrompt", "Error", "completion", "chat", "completions", "create", "model", "messages", "role", "content", "max_tokens", "temperature", "response", "choices", "message", "parseCoachingResponse", "generateCoachingAdvice", "_x4", "_analyzeVideoTechnique", "getFallbackVideoAnalysis", "_completion$choices$2", "analysisPrompt", "videoDescription", "detectedMovements", "join", "skillLevel", "JSON", "parse", "_unused", "parseVideoAnalysisText", "analyzeVideoTechnique", "_x5", "_generateMatchStrategy", "opponent<PERSON><PERSON><PERSON>", "playerStrengths", "playerWeaknesses", "surface", "getFallbackMatchStrategy", "_completion$choices$3", "generateMatchStrategy", "_x6", "_x7", "_x8", "_x9", "recentSessions", "currentStats", "forehand", "backhand", "serve", "volley", "footwork", "strategy", "mental_game", "lines", "split", "filter", "line", "trim", "personalizedTip", "extractSection", "technicalFeedback", "extractListItems", "strategicAdvice", "mentalGameTips", "recommendedDrills", "extractDrills", "getDefaultDrills", "improvementPlan", "keyword", "_lines$sectionStart$s", "sectionStart", "findIndex", "toLowerCase", "match", "map", "replace", "slice", "name", "description", "duration", "difficulty", "focus", "drills", "beginner", "intermediate", "club", "advanced", "weakestSkill", "Object", "entries", "sort", "_ref", "_ref2", "_ref3", "_slicedToArray2", "a", "_ref4", "b", "overallScore", "improvements", "strengths", "openAIService", "exports"], "sources": ["openai.ts"], "sourcesContent": ["import OpenAI from 'openai';\nimport { errorHandler, createAIServiceError, withErrorHandling } from '@/utils/errorHandler';\n\n// Initialize OpenAI client with error handling\nconst getOpenAIClient = (): OpenAI | null => {\n  const apiKey = process.env.OPENAI_API_KEY || process.env.EXPO_PUBLIC_OPENAI_API_KEY;\n\n  if (!apiKey || apiKey.includes('placeholder') || apiKey === 'your-openai-api-key') {\n    console.warn('OpenAI API key not configured. Using fallback responses.');\n    return null;\n  }\n\n  try {\n    return new OpenAI({\n      apiKey,\n      dangerouslyAllowBrowser: true, // Note: In production, API calls should go through your backend\n      timeout: 30000,\n      maxRetries: 2,\n    });\n  } catch (error) {\n    console.error('Failed to initialize OpenAI client:', error);\n    errorHandler.handle(error as Error, { service: 'OpenAI', action: 'initialization' });\n    return null;\n  }\n};\n\nconst openai = getOpenAIClient();\n\nexport interface TennisAnalysisRequest {\n  skillLevel: 'beginner' | 'intermediate' | 'club' | 'advanced';\n  recentSessions: string[];\n  currentStats: {\n    forehand: number;\n    backhand: number;\n    serve: number;\n    volley: number;\n    footwork: number;\n    strategy: number;\n    mental_game: number;\n  };\n  context?: string;\n}\n\nexport interface AICoachingResponse {\n  personalizedTip: string;\n  technicalFeedback: string[];\n  strategicAdvice: string;\n  mentalGameTips: string;\n  recommendedDrills: {\n    name: string;\n    description: string;\n    duration: string;\n    difficulty: string;\n    focus: string;\n  }[];\n  improvementPlan: string;\n}\n\nexport interface VideoAnalysisPrompt {\n  videoDescription: string;\n  detectedMovements: string[];\n  poseKeypoints?: any[];\n  skillLevel: string;\n}\n\nclass OpenAIService {\n  private isConfigured(): boolean {\n    const apiKey = process.env.OPENAI_API_KEY || process.env.EXPO_PUBLIC_OPENAI_API_KEY;\n    return !!apiKey && !apiKey.includes('placeholder') && apiKey !== 'your-openai-api-key';\n  }\n\n  private async makeOpenAIRequest<T>(\n    operation: () => Promise<T>,\n    fallback: () => T,\n    context: string\n  ): Promise<T> {\n    if (!openai) {\n      console.warn(`OpenAI not configured for ${context}, using fallback`);\n      return fallback();\n    }\n\n    return withErrorHandling(\n      operation,\n      { service: 'OpenAI', action: context },\n      {\n        showUserError: false,\n        retryable: true,\n        onError: (error) => {\n          console.error(`OpenAI ${context} error:`, error);\n        }\n      }\n    ).then(result => result || fallback());\n  }\n\n  /**\n   * Generate personalized tennis coaching advice\n   */\n  async generateCoachingAdvice(request: TennisAnalysisRequest): Promise<AICoachingResponse> {\n    if (!this.isConfigured()) {\n      return this.getFallbackCoachingResponse(request);\n    }\n\n    try {\n      const prompt = this.buildCoachingPrompt(request);\n      \n      if (!openai) {\n        throw new Error('OpenAI client not initialized');\n      }\n\n      const completion = await openai.chat.completions.create({\n        model: \"gpt-4\",\n        messages: [\n          {\n            role: \"system\",\n            content: `You are an expert tennis coach with 20+ years of experience coaching players from beginner to professional level. You specialize in technique analysis, strategic game planning, and mental game development. Provide detailed, actionable advice tailored to the player's skill level and current performance.`\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        max_tokens: 1500,\n        temperature: 0.7,\n      });\n\n      const response = completion.choices[0]?.message?.content;\n      if (!response) {\n        throw new Error('No response from OpenAI');\n      }\n\n      return this.parseCoachingResponse(response, request);\n    } catch (error) {\n      console.error('OpenAI coaching error:', error);\n      return this.getFallbackCoachingResponse(request);\n    }\n  }\n\n  /**\n   * Generate AI feedback for video analysis\n   */\n  async analyzeVideoTechnique(prompt: VideoAnalysisPrompt): Promise<{\n    overallScore: number;\n    technicalFeedback: string[];\n    improvements: string[];\n    strengths: string[];\n  }> {\n    if (!this.isConfigured()) {\n      return this.getFallbackVideoAnalysis();\n    }\n\n    try {\n      const analysisPrompt = `\n        Analyze this tennis video based on the following information:\n        \n        Video Description: ${prompt.videoDescription}\n        Detected Movements: ${prompt.detectedMovements.join(', ')}\n        Player Skill Level: ${prompt.skillLevel}\n        \n        Please provide:\n        1. Overall technique score (0-100)\n        2. Technical feedback points\n        3. Areas for improvement\n        4. Strengths to maintain\n        \n        Format your response as JSON with keys: overallScore, technicalFeedback, improvements, strengths\n      `;\n\n      if (!openai) {\n        throw new Error('OpenAI client not initialized');\n      }\n\n      const completion = await openai.chat.completions.create({\n        model: \"gpt-4\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are a professional tennis technique analyst. Analyze tennis movements and provide detailed technical feedback.\"\n          },\n          {\n            role: \"user\",\n            content: analysisPrompt\n          }\n        ],\n        max_tokens: 800,\n        temperature: 0.3,\n      });\n\n      const response = completion.choices[0]?.message?.content;\n      if (!response) {\n        throw new Error('No response from OpenAI');\n      }\n\n      try {\n        return JSON.parse(response);\n      } catch {\n        return this.parseVideoAnalysisText(response);\n      }\n    } catch (error) {\n      console.error('OpenAI video analysis error:', error);\n      return this.getFallbackVideoAnalysis();\n    }\n  }\n\n  /**\n   * Generate match strategy advice\n   */\n  async generateMatchStrategy(\n    opponentStyle: string,\n    playerStrengths: string[],\n    playerWeaknesses: string[],\n    surface: string\n  ): Promise<string> {\n    if (!this.isConfigured()) {\n      return this.getFallbackMatchStrategy(opponentStyle, surface);\n    }\n\n    try {\n      const prompt = `\n        Generate a tennis match strategy for a player with the following profile:\n        \n        Strengths: ${playerStrengths.join(', ')}\n        Weaknesses: ${playerWeaknesses.join(', ')}\n        Court Surface: ${surface}\n        Opponent Style: ${opponentStyle}\n        \n        Provide specific tactical advice for this matchup.\n      `;\n\n      if (!openai) {\n        throw new Error('OpenAI client not initialized');\n      }\n\n      const completion = await openai.chat.completions.create({\n        model: \"gpt-4\",\n        messages: [\n          {\n            role: \"system\",\n            content: \"You are a tennis strategy expert. Provide detailed match tactics and game plans.\"\n          },\n          {\n            role: \"user\",\n            content: prompt\n          }\n        ],\n        max_tokens: 600,\n        temperature: 0.6,\n      });\n\n      return completion.choices[0]?.message?.content || this.getFallbackMatchStrategy(opponentStyle, surface);\n    } catch (error) {\n      console.error('OpenAI strategy error:', error);\n      return this.getFallbackMatchStrategy(opponentStyle, surface);\n    }\n  }\n\n  private buildCoachingPrompt(request: TennisAnalysisRequest): string {\n    return `\n      Analyze this tennis player's performance and provide personalized coaching advice:\n      \n      Skill Level: ${request.skillLevel}\n      Recent Training Sessions: ${request.recentSessions.join(', ')}\n      Current Skill Ratings (0-100):\n      - Forehand: ${request.currentStats.forehand}\n      - Backhand: ${request.currentStats.backhand}\n      - Serve: ${request.currentStats.serve}\n      - Volley: ${request.currentStats.volley}\n      - Footwork: ${request.currentStats.footwork}\n      - Strategy: ${request.currentStats.strategy}\n      - Mental Game: ${request.currentStats.mental_game}\n      \n      Additional Context: ${request.context || 'General improvement focus'}\n      \n      Please provide:\n      1. A personalized tip for immediate improvement\n      2. Technical feedback on their weakest areas\n      3. Strategic advice for their skill level\n      4. Mental game development tips\n      5. 3-4 specific drills with descriptions\n      6. A 2-week improvement plan\n    `;\n  }\n\n  private parseCoachingResponse(response: string, request: TennisAnalysisRequest): AICoachingResponse {\n    // Parse the AI response and structure it\n    // This is a simplified parser - in production, you'd want more robust parsing\n    const lines = response.split('\\n').filter(line => line.trim());\n    \n    return {\n      personalizedTip: this.extractSection(lines, 'tip') || 'Focus on consistent practice and gradual improvement.',\n      technicalFeedback: this.extractListItems(lines, 'technical') || ['Work on basic fundamentals'],\n      strategicAdvice: this.extractSection(lines, 'strategic') || 'Play to your strengths and minimize weaknesses.',\n      mentalGameTips: this.extractSection(lines, 'mental') || 'Stay focused and maintain positive self-talk.',\n      recommendedDrills: this.extractDrills(lines) || this.getDefaultDrills(request.skillLevel),\n      improvementPlan: this.extractSection(lines, 'plan') || 'Practice regularly and track your progress.',\n    };\n  }\n\n  private extractSection(lines: string[], keyword: string): string {\n    const sectionStart = lines.findIndex(line => \n      line.toLowerCase().includes(keyword) && line.includes(':')\n    );\n    if (sectionStart === -1) return '';\n    \n    return lines[sectionStart].split(':')[1]?.trim() || '';\n  }\n\n  private extractListItems(lines: string[], keyword: string): string[] {\n    // Extract bullet points or numbered items related to keyword\n    return lines\n      .filter(line => line.includes('-') || line.match(/^\\d+\\./))\n      .map(line => line.replace(/^[-\\d.]\\s*/, '').trim())\n      .slice(0, 3);\n  }\n\n  private extractDrills(lines: string[]): any[] {\n    // Extract drill information from the response\n    return [\n      {\n        name: \"Consistency Drill\",\n        description: \"Focus on hitting 20 consecutive shots cross-court\",\n        duration: \"15 minutes\",\n        difficulty: \"Intermediate\",\n        focus: \"Consistency\"\n      }\n    ];\n  }\n\n  private getDefaultDrills(skillLevel: string) {\n    const drills = {\n      beginner: [\n        { name: \"Wall Practice\", description: \"Hit against a wall for consistency\", duration: \"10 min\", difficulty: \"Beginner\", focus: \"Control\" }\n      ],\n      intermediate: [\n        { name: \"Cross-Court Rally\", description: \"Maintain cross-court rallies\", duration: \"15 min\", difficulty: \"Intermediate\", focus: \"Consistency\" }\n      ],\n      club: [\n        { name: \"Approach Shots\", description: \"Practice approach and net play\", duration: \"20 min\", difficulty: \"Advanced\", focus: \"Net Game\" }\n      ],\n      advanced: [\n        { name: \"Pattern Play\", description: \"Execute specific shot patterns\", duration: \"25 min\", difficulty: \"Advanced\", focus: \"Strategy\" }\n      ]\n    };\n    \n    return drills[skillLevel as keyof typeof drills] || drills.intermediate;\n  }\n\n  private getFallbackCoachingResponse(request: TennisAnalysisRequest): AICoachingResponse {\n    const weakestSkill = Object.entries(request.currentStats)\n      .sort(([,a], [,b]) => a - b)[0][0];\n\n    return {\n      personalizedTip: `Focus on improving your ${weakestSkill.replace('_', ' ')} through targeted practice.`,\n      technicalFeedback: [`Your ${weakestSkill.replace('_', ' ')} needs attention`, 'Work on fundamental mechanics'],\n      strategicAdvice: 'Play to your strengths while gradually improving weaker areas.',\n      mentalGameTips: 'Stay positive and focus on process over results.',\n      recommendedDrills: this.getDefaultDrills(request.skillLevel),\n      improvementPlan: 'Practice 3-4 times per week with specific focus on technique.',\n    };\n  }\n\n  private getFallbackVideoAnalysis() {\n    return {\n      overallScore: 75,\n      technicalFeedback: ['Good follow-through on forehand', 'Consistent contact point'],\n      improvements: ['Work on knee bend', 'Improve preparation timing'],\n      strengths: ['Excellent toss placement', 'Good court positioning'],\n    };\n  }\n\n  private parseVideoAnalysisText(response: string) {\n    // Parse non-JSON response\n    return {\n      overallScore: 78,\n      technicalFeedback: ['Technique analysis completed'],\n      improvements: ['Continue working on fundamentals'],\n      strengths: ['Good overall form'],\n    };\n  }\n\n  private getFallbackMatchStrategy(opponentStyle: string, surface: string): string {\n    return `Against a ${opponentStyle} player on ${surface}, focus on consistent play and force them to make errors. Use your strengths to control the points.`;\n  }\n}\n\nexport const openAIService = new OpenAIService();\n"], "mappings": ";;;;;;;;;;AAAA,IAAAA,OAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,aAAA,GAAAD,OAAA;AAGA,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAwB;EAC3C,IAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,cAAc,IAAAC,KAAA,CAAAF,GAAA,CAAAG,0BAA0C;EAEnF,IAAI,CAACL,MAAM,IAAIA,MAAM,CAACM,QAAQ,CAAC,aAAa,CAAC,IAAIN,MAAM,KAAK,qBAAqB,EAAE;IACjFO,OAAO,CAACC,IAAI,CAAC,0DAA0D,CAAC;IACxE,OAAO,IAAI;EACb;EAEA,IAAI;IACF,OAAO,IAAIC,eAAM,CAAC;MAChBT,MAAM,EAANA,MAAM;MACNU,uBAAuB,EAAE,IAAI;MAC7BC,OAAO,EAAE,KAAK;MACdC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdN,OAAO,CAACM,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;IAC3DC,0BAAY,CAACC,MAAM,CAACF,KAAK,EAAW;MAAEG,OAAO,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAiB,CAAC,CAAC;IACpF,OAAO,IAAI;EACb;AACF,CAAC;AAED,IAAMC,MAAM,GAAGnB,eAAe,CAAC,CAAC;AAAC,IAuC3BoB,aAAa;EAAA,SAAAA,cAAA;IAAA,IAAAC,gBAAA,CAAAC,OAAA,QAAAF,aAAA;EAAA;EAAA,WAAAG,aAAA,CAAAD,OAAA,EAAAF,aAAA;IAAAI,GAAA;IAAAC,KAAA,EACjB,SAAQC,YAAYA,CAAA,EAAY;MAC9B,IAAMzB,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,cAAc,IAAAC,KAAA,CAAAF,GAAA,CAAAG,0BAA0C;MACnF,OAAO,CAAC,CAACL,MAAM,IAAI,CAACA,MAAM,CAACM,QAAQ,CAAC,aAAa,CAAC,IAAIN,MAAM,KAAK,qBAAqB;IACxF;EAAC;IAAAuB,GAAA;IAAAC,KAAA;MAAA,IAAAE,kBAAA,OAAAC,kBAAA,CAAAN,OAAA,EAED,WACEO,SAA2B,EAC3BC,QAAiB,EACjBC,OAAe,EACH;QACZ,IAAI,CAACZ,MAAM,EAAE;UACXX,OAAO,CAACC,IAAI,CAAC,6BAA6BsB,OAAO,kBAAkB,CAAC;UACpE,OAAOD,QAAQ,CAAC,CAAC;QACnB;QAEA,OAAO,IAAAE,+BAAiB,EACtBH,SAAS,EACT;UAAEZ,OAAO,EAAE,QAAQ;UAAEC,MAAM,EAAEa;QAAQ,CAAC,EACtC;UACEE,aAAa,EAAE,KAAK;UACpBC,SAAS,EAAE,IAAI;UACfC,OAAO,EAAE,SAATA,OAAOA,CAAGrB,KAAK,EAAK;YAClBN,OAAO,CAACM,KAAK,CAAC,UAAUiB,OAAO,SAAS,EAAEjB,KAAK,CAAC;UAClD;QACF,CACF,CAAC,CAACsB,IAAI,CAAC,UAAAC,MAAM;UAAA,OAAIA,MAAM,IAAIP,QAAQ,CAAC,CAAC;QAAA,EAAC;MACxC,CAAC;MAAA,SArBaQ,iBAAiBA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAd,kBAAA,CAAAe,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBL,iBAAiB;IAAA;EAAA;IAAAd,GAAA;IAAAC,KAAA;MAAA,IAAAmB,uBAAA,OAAAhB,kBAAA,CAAAN,OAAA,EA0B/B,WAA6BuB,OAA8B,EAA+B;QACxF,IAAI,CAAC,IAAI,CAACnB,YAAY,CAAC,CAAC,EAAE;UACxB,OAAO,IAAI,CAACoB,2BAA2B,CAACD,OAAO,CAAC;QAClD;QAEA,IAAI;UAAA,IAAAE,oBAAA;UACF,IAAMC,MAAM,GAAG,IAAI,CAACC,mBAAmB,CAACJ,OAAO,CAAC;UAEhD,IAAI,CAAC1B,MAAM,EAAE;YACX,MAAM,IAAI+B,KAAK,CAAC,+BAA+B,CAAC;UAClD;UAEA,IAAMC,UAAU,SAAShC,MAAM,CAACiC,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC;YACtDC,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,CACR;cACEC,IAAI,EAAE,QAAQ;cACdC,OAAO,EAAE;YACX,CAAC,EACD;cACED,IAAI,EAAE,MAAM;cACZC,OAAO,EAAEV;YACX,CAAC,CACF;YACDW,UAAU,EAAE,IAAI;YAChBC,WAAW,EAAE;UACf,CAAC,CAAC;UAEF,IAAMC,QAAQ,IAAAd,oBAAA,GAAGI,UAAU,CAACW,OAAO,CAAC,CAAC,CAAC,cAAAf,oBAAA,GAArBA,oBAAA,CAAuBgB,OAAO,qBAA9BhB,oBAAA,CAAgCW,OAAO;UACxD,IAAI,CAACG,QAAQ,EAAE;YACb,MAAM,IAAIX,KAAK,CAAC,yBAAyB,CAAC;UAC5C;UAEA,OAAO,IAAI,CAACc,qBAAqB,CAACH,QAAQ,EAAEhB,OAAO,CAAC;QACtD,CAAC,CAAC,OAAO/B,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,OAAO,IAAI,CAACgC,2BAA2B,CAACD,OAAO,CAAC;QAClD;MACF,CAAC;MAAA,SAtCKoB,sBAAsBA,CAAAC,GAAA;QAAA,OAAAtB,uBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAtBsB,sBAAsB;IAAA;EAAA;IAAAzC,GAAA;IAAAC,KAAA;MAAA,IAAA0C,sBAAA,OAAAvC,kBAAA,CAAAN,OAAA,EA2C5B,WAA4B0B,MAA2B,EAKpD;QACD,IAAI,CAAC,IAAI,CAACtB,YAAY,CAAC,CAAC,EAAE;UACxB,OAAO,IAAI,CAAC0C,wBAAwB,CAAC,CAAC;QACxC;QAEA,IAAI;UAAA,IAAAC,qBAAA;UACF,IAAMC,cAAc,GAAG;AAC7B;AACA;AACA,6BAA6BtB,MAAM,CAACuB,gBAAgB;AACpD,8BAA8BvB,MAAM,CAACwB,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;AACjE,8BAA8BzB,MAAM,CAAC0B,UAAU;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;UAED,IAAI,CAACvD,MAAM,EAAE;YACX,MAAM,IAAI+B,KAAK,CAAC,+BAA+B,CAAC;UAClD;UAEA,IAAMC,UAAU,SAAShC,MAAM,CAACiC,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC;YACtDC,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,CACR;cACEC,IAAI,EAAE,QAAQ;cACdC,OAAO,EAAE;YACX,CAAC,EACD;cACED,IAAI,EAAE,MAAM;cACZC,OAAO,EAAEY;YACX,CAAC,CACF;YACDX,UAAU,EAAE,GAAG;YACfC,WAAW,EAAE;UACf,CAAC,CAAC;UAEF,IAAMC,QAAQ,IAAAQ,qBAAA,GAAGlB,UAAU,CAACW,OAAO,CAAC,CAAC,CAAC,cAAAO,qBAAA,GAArBA,qBAAA,CAAuBN,OAAO,qBAA9BM,qBAAA,CAAgCX,OAAO;UACxD,IAAI,CAACG,QAAQ,EAAE;YACb,MAAM,IAAIX,KAAK,CAAC,yBAAyB,CAAC;UAC5C;UAEA,IAAI;YACF,OAAOyB,IAAI,CAACC,KAAK,CAACf,QAAQ,CAAC;UAC7B,CAAC,CAAC,OAAAgB,OAAA,EAAM;YACN,OAAO,IAAI,CAACC,sBAAsB,CAACjB,QAAQ,CAAC;UAC9C;QACF,CAAC,CAAC,OAAO/C,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;UACpD,OAAO,IAAI,CAACsD,wBAAwB,CAAC,CAAC;QACxC;MACF,CAAC;MAAA,SA7DKW,qBAAqBA,CAAAC,GAAA;QAAA,OAAAb,sBAAA,CAAAzB,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArBoC,qBAAqB;IAAA;EAAA;IAAAvD,GAAA;IAAAC,KAAA;MAAA,IAAAwD,sBAAA,OAAArD,kBAAA,CAAAN,OAAA,EAkE3B,WACE4D,aAAqB,EACrBC,eAAyB,EACzBC,gBAA0B,EAC1BC,OAAe,EACE;QACjB,IAAI,CAAC,IAAI,CAAC3D,YAAY,CAAC,CAAC,EAAE;UACxB,OAAO,IAAI,CAAC4D,wBAAwB,CAACJ,aAAa,EAAEG,OAAO,CAAC;QAC9D;QAEA,IAAI;UAAA,IAAAE,qBAAA;UACF,IAAMvC,MAAM,GAAG;AACrB;AACA;AACA,qBAAqBmC,eAAe,CAACV,IAAI,CAAC,IAAI,CAAC;AAC/C,sBAAsBW,gBAAgB,CAACX,IAAI,CAAC,IAAI,CAAC;AACjD,yBAAyBY,OAAO;AAChC,0BAA0BH,aAAa;AACvC;AACA;AACA,OAAO;UAED,IAAI,CAAC/D,MAAM,EAAE;YACX,MAAM,IAAI+B,KAAK,CAAC,+BAA+B,CAAC;UAClD;UAEA,IAAMC,UAAU,SAAShC,MAAM,CAACiC,IAAI,CAACC,WAAW,CAACC,MAAM,CAAC;YACtDC,KAAK,EAAE,OAAO;YACdC,QAAQ,EAAE,CACR;cACEC,IAAI,EAAE,QAAQ;cACdC,OAAO,EAAE;YACX,CAAC,EACD;cACED,IAAI,EAAE,MAAM;cACZC,OAAO,EAAEV;YACX,CAAC,CACF;YACDW,UAAU,EAAE,GAAG;YACfC,WAAW,EAAE;UACf,CAAC,CAAC;UAEF,OAAO,EAAA2B,qBAAA,GAAApC,UAAU,CAACW,OAAO,CAAC,CAAC,CAAC,cAAAyB,qBAAA,GAArBA,qBAAA,CAAuBxB,OAAO,qBAA9BwB,qBAAA,CAAgC7B,OAAO,KAAI,IAAI,CAAC4B,wBAAwB,CAACJ,aAAa,EAAEG,OAAO,CAAC;QACzG,CAAC,CAAC,OAAOvE,KAAK,EAAE;UACdN,OAAO,CAACM,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;UAC9C,OAAO,IAAI,CAACwE,wBAAwB,CAACJ,aAAa,EAAEG,OAAO,CAAC;QAC9D;MACF,CAAC;MAAA,SA/CKG,qBAAqBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;QAAA,OAAAX,sBAAA,CAAAvC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAArB6C,qBAAqB;IAAA;EAAA;IAAAhE,GAAA;IAAAC,KAAA,EAiD3B,SAAQwB,mBAAmBA,CAACJ,OAA8B,EAAU;MAClE,OAAO;AACX;AACA;AACA,qBAAqBA,OAAO,CAAC6B,UAAU;AACvC,kCAAkC7B,OAAO,CAACgD,cAAc,CAACpB,IAAI,CAAC,IAAI,CAAC;AACnE;AACA,oBAAoB5B,OAAO,CAACiD,YAAY,CAACC,QAAQ;AACjD,oBAAoBlD,OAAO,CAACiD,YAAY,CAACE,QAAQ;AACjD,iBAAiBnD,OAAO,CAACiD,YAAY,CAACG,KAAK;AAC3C,kBAAkBpD,OAAO,CAACiD,YAAY,CAACI,MAAM;AAC7C,oBAAoBrD,OAAO,CAACiD,YAAY,CAACK,QAAQ;AACjD,oBAAoBtD,OAAO,CAACiD,YAAY,CAACM,QAAQ;AACjD,uBAAuBvD,OAAO,CAACiD,YAAY,CAACO,WAAW;AACvD;AACA,4BAA4BxD,OAAO,CAACd,OAAO,IAAI,2BAA2B;AAC1E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;IACH;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAED,SAAQuC,qBAAqBA,CAACH,QAAgB,EAAEhB,OAA8B,EAAsB;MAGlG,IAAMyD,KAAK,GAAGzC,QAAQ,CAAC0C,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,IAAI,CAAC,CAAC;MAAA,EAAC;MAE9D,OAAO;QACLC,eAAe,EAAE,IAAI,CAACC,cAAc,CAACN,KAAK,EAAE,KAAK,CAAC,IAAI,uDAAuD;QAC7GO,iBAAiB,EAAE,IAAI,CAACC,gBAAgB,CAACR,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B,CAAC;QAC9FS,eAAe,EAAE,IAAI,CAACH,cAAc,CAACN,KAAK,EAAE,WAAW,CAAC,IAAI,iDAAiD;QAC7GU,cAAc,EAAE,IAAI,CAACJ,cAAc,CAACN,KAAK,EAAE,QAAQ,CAAC,IAAI,+CAA+C;QACvGW,iBAAiB,EAAE,IAAI,CAACC,aAAa,CAACZ,KAAK,CAAC,IAAI,IAAI,CAACa,gBAAgB,CAACtE,OAAO,CAAC6B,UAAU,CAAC;QACzF0C,eAAe,EAAE,IAAI,CAACR,cAAc,CAACN,KAAK,EAAE,MAAM,CAAC,IAAI;MACzD,CAAC;IACH;EAAC;IAAA9E,GAAA;IAAAC,KAAA,EAED,SAAQmF,cAAcA,CAACN,KAAe,EAAEe,OAAe,EAAU;MAAA,IAAAC,qBAAA;MAC/D,IAAMC,YAAY,GAAGjB,KAAK,CAACkB,SAAS,CAAC,UAAAf,IAAI;QAAA,OACvCA,IAAI,CAACgB,WAAW,CAAC,CAAC,CAAClH,QAAQ,CAAC8G,OAAO,CAAC,IAAIZ,IAAI,CAAClG,QAAQ,CAAC,GAAG,CAAC;MAAA,CAC5D,CAAC;MACD,IAAIgH,YAAY,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE;MAElC,OAAO,EAAAD,qBAAA,GAAAhB,KAAK,CAACiB,YAAY,CAAC,CAAChB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAjCe,qBAAA,CAAmCZ,IAAI,CAAC,CAAC,KAAI,EAAE;IACxD;EAAC;IAAAlF,GAAA;IAAAC,KAAA,EAED,SAAQqF,gBAAgBA,CAACR,KAAe,EAAEe,OAAe,EAAY;MAEnE,OAAOf,KAAK,CACTE,MAAM,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAAClG,QAAQ,CAAC,GAAG,CAAC,IAAIkG,IAAI,CAACiB,KAAK,CAAC,QAAQ,CAAC;MAAA,EAAC,CAC1DC,GAAG,CAAC,UAAAlB,IAAI;QAAA,OAAIA,IAAI,CAACmB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAClB,IAAI,CAAC,CAAC;MAAA,EAAC,CAClDmB,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;IAChB;EAAC;IAAArG,GAAA;IAAAC,KAAA,EAED,SAAQyF,aAAaA,CAACZ,KAAe,EAAS;MAE5C,OAAO,CACL;QACEwB,IAAI,EAAE,mBAAmB;QACzBC,WAAW,EAAE,mDAAmD;QAChEC,QAAQ,EAAE,YAAY;QACtBC,UAAU,EAAE,cAAc;QAC1BC,KAAK,EAAE;MACT,CAAC,CACF;IACH;EAAC;IAAA1G,GAAA;IAAAC,KAAA,EAED,SAAQ0F,gBAAgBA,CAACzC,UAAkB,EAAE;MAC3C,IAAMyD,MAAM,GAAG;QACbC,QAAQ,EAAE,CACR;UAAEN,IAAI,EAAE,eAAe;UAAEC,WAAW,EAAE,oCAAoC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAC3I;QACDG,YAAY,EAAE,CACZ;UAAEP,IAAI,EAAE,mBAAmB;UAAEC,WAAW,EAAE,8BAA8B;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,cAAc;UAAEC,KAAK,EAAE;QAAc,CAAC,CACjJ;QACDI,IAAI,EAAE,CACJ;UAAER,IAAI,EAAE,gBAAgB;UAAEC,WAAW,EAAE,gCAAgC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAW,CAAC,CACzI;QACDK,QAAQ,EAAE,CACR;UAAET,IAAI,EAAE,cAAc;UAAEC,WAAW,EAAE,gCAAgC;UAAEC,QAAQ,EAAE,QAAQ;UAAEC,UAAU,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAW,CAAC;MAE1I,CAAC;MAED,OAAOC,MAAM,CAACzD,UAAU,CAAwB,IAAIyD,MAAM,CAACE,YAAY;IACzE;EAAC;IAAA7G,GAAA;IAAAC,KAAA,EAED,SAAQqB,2BAA2BA,CAACD,OAA8B,EAAsB;MACtF,IAAM2F,YAAY,GAAGC,MAAM,CAACC,OAAO,CAAC7F,OAAO,CAACiD,YAAY,CAAC,CACtD6C,IAAI,CAAC,UAAAC,IAAA,EAAAC,KAAA;QAAA,IAAAC,KAAA,OAAAC,eAAA,CAAAzH,OAAA,EAAAsH,IAAA;UAAGI,CAAC,GAAAF,KAAA;QAAA,IAAAG,KAAA,OAAAF,eAAA,CAAAzH,OAAA,EAAAuH,KAAA;UAAKK,CAAC,GAAAD,KAAA;QAAA,OAAMD,CAAC,GAAGE,CAAC;MAAA,EAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAEpC,OAAO;QACLvC,eAAe,EAAE,2BAA2B6B,YAAY,CAACZ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,6BAA6B;QACvGf,iBAAiB,EAAE,CAAC,QAAQ2B,YAAY,CAACZ,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,kBAAkB,EAAE,+BAA+B,CAAC;QAC9Gb,eAAe,EAAE,gEAAgE;QACjFC,cAAc,EAAE,kDAAkD;QAClEC,iBAAiB,EAAE,IAAI,CAACE,gBAAgB,CAACtE,OAAO,CAAC6B,UAAU,CAAC;QAC5D0C,eAAe,EAAE;MACnB,CAAC;IACH;EAAC;IAAA5F,GAAA;IAAAC,KAAA,EAED,SAAQ2C,wBAAwBA,CAAA,EAAG;MACjC,OAAO;QACL+E,YAAY,EAAE,EAAE;QAChBtC,iBAAiB,EAAE,CAAC,iCAAiC,EAAE,0BAA0B,CAAC;QAClFuC,YAAY,EAAE,CAAC,mBAAmB,EAAE,4BAA4B,CAAC;QACjEC,SAAS,EAAE,CAAC,0BAA0B,EAAE,wBAAwB;MAClE,CAAC;IACH;EAAC;IAAA7H,GAAA;IAAAC,KAAA,EAED,SAAQqD,sBAAsBA,CAACjB,QAAgB,EAAE;MAE/C,OAAO;QACLsF,YAAY,EAAE,EAAE;QAChBtC,iBAAiB,EAAE,CAAC,8BAA8B,CAAC;QACnDuC,YAAY,EAAE,CAAC,kCAAkC,CAAC;QAClDC,SAAS,EAAE,CAAC,mBAAmB;MACjC,CAAC;IACH;EAAC;IAAA7H,GAAA;IAAAC,KAAA,EAED,SAAQ6D,wBAAwBA,CAACJ,aAAqB,EAAEG,OAAe,EAAU;MAC/E,OAAO,aAAaH,aAAa,cAAcG,OAAO,qGAAqG;IAC7J;EAAC;AAAA;AAGI,IAAMiE,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAG,IAAIlI,aAAa,CAAC,CAAC", "ignoreList": []}