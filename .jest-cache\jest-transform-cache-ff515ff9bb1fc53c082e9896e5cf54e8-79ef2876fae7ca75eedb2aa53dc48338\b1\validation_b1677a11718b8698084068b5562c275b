7f4881f6664f11dcd7aab270353030a9
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1gg4kzmwm7() {
  var path = "C:\\_SaaS\\AceMind\\project\\utils\\validation.ts";
  var hash = "809ae6c2229afb227d09d88f6f84eaf2bf3edf2c";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\utils\\validation.ts",
    statementMap: {
      "0": {
        start: {
          line: 9,
          column: 27
        },
        end: {
          line: 54,
          column: 1
        }
      },
      "1": {
        start: {
          line: 14,
          column: 24
        },
        end: {
          line: 14,
          column: 50
        }
      },
      "2": {
        start: {
          line: 26,
          column: 23
        },
        end: {
          line: 26,
          column: 34
        }
      },
      "3": {
        start: {
          line: 57,
          column: 29
        },
        end: {
          line: 77,
          column: 1
        }
      },
      "4": {
        start: {
          line: 80,
          column: 27
        },
        end: {
          line: 122,
          column: 1
        }
      },
      "5": {
        start: {
          line: 86,
          column: 20
        },
        end: {
          line: 86,
          column: 58
        }
      },
      "6": {
        start: {
          line: 118,
          column: 20
        },
        end: {
          line: 118,
          column: 61
        }
      },
      "7": {
        start: {
          line: 125,
          column: 31
        },
        end: {
          line: 162,
          column: 1
        }
      },
      "8": {
        start: {
          line: 130,
          column: 26
        },
        end: {
          line: 130,
          column: 38
        }
      },
      "9": {
        start: {
          line: 165,
          column: 28
        },
        end: {
          line: 182,
          column: 1
        }
      },
      "10": {
        start: {
          line: 185,
          column: 25
        },
        end: {
          line: 201,
          column: 1
        }
      },
      "11": {
        start: {
          line: 190,
          column: 24
        },
        end: {
          line: 190,
          column: 34
        }
      },
      "12": {
        start: {
          line: 204,
          column: 29
        },
        end: {
          line: 225,
          column: 1
        }
      },
      "13": {
        start: {
          line: 209,
          column: 28
        },
        end: {
          line: 209,
          column: 42
        }
      },
      "14": {
        start: {
          line: 219,
          column: 28
        },
        end: {
          line: 219,
          column: 42
        }
      },
      "15": {
        start: {
          line: 228,
          column: 30
        },
        end: {
          line: 249,
          column: 1
        }
      },
      "16": {
        start: {
          line: 252,
          column: 27
        },
        end: {
          line: 275,
          column: 1
        }
      },
      "17": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 290,
          column: 32
        }
      },
      "18": {
        start: {
          line: 297,
          column: 4
        },
        end: {
          line: 302,
          column: 28
        }
      },
      "19": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 312,
          column: 59
        }
      },
      "20": {
        start: {
          line: 319,
          column: 22
        },
        end: {
          line: 319,
          column: 61
        }
      },
      "21": {
        start: {
          line: 320,
          column: 4
        },
        end: {
          line: 320,
          column: 64
        }
      },
      "22": {
        start: {
          line: 327,
          column: 4
        },
        end: {
          line: 348,
          column: 5
        }
      },
      "23": {
        start: {
          line: 328,
          column: 24
        },
        end: {
          line: 328,
          column: 36
        }
      },
      "24": {
        start: {
          line: 331,
          column: 28
        },
        end: {
          line: 331,
          column: 47
        }
      },
      "25": {
        start: {
          line: 332,
          column: 6
        },
        end: {
          line: 334,
          column: 7
        }
      },
      "26": {
        start: {
          line: 333,
          column: 8
        },
        end: {
          line: 333,
          column: 21
        }
      },
      "27": {
        start: {
          line: 337,
          column: 33
        },
        end: {
          line: 343,
          column: 7
        }
      },
      "28": {
        start: {
          line: 345,
          column: 6
        },
        end: {
          line: 345,
          column: 68
        }
      },
      "29": {
        start: {
          line: 345,
          column: 49
        },
        end: {
          line: 345,
          column: 66
        }
      },
      "30": {
        start: {
          line: 347,
          column: 6
        },
        end: {
          line: 347,
          column: 19
        }
      },
      "31": {
        start: {
          line: 355,
          column: 19
        },
        end: {
          line: 355,
          column: 38
        }
      },
      "32": {
        start: {
          line: 356,
          column: 4
        },
        end: {
          line: 356,
          column: 30
        }
      },
      "33": {
        start: {
          line: 356,
          column: 17
        },
        end: {
          line: 356,
          column: 30
        }
      },
      "34": {
        start: {
          line: 359,
          column: 30
        },
        end: {
          line: 365,
          column: 5
        }
      },
      "35": {
        start: {
          line: 367,
          column: 4
        },
        end: {
          line: 367,
          column: 61
        }
      },
      "36": {
        start: {
          line: 374,
          column: 30
        },
        end: {
          line: 383,
          column: 5
        }
      },
      "37": {
        start: {
          line: 385,
          column: 4
        },
        end: {
          line: 385,
          column: 66
        }
      },
      "38": {
        start: {
          line: 385,
          column: 45
        },
        end: {
          line: 385,
          column: 64
        }
      },
      "39": {
        start: {
          line: 395,
          column: 31
        },
        end: {
          line: 395,
          column: 33
        }
      },
      "40": {
        start: {
          line: 396,
          column: 16
        },
        end: {
          line: 396,
          column: 17
        }
      },
      "41": {
        start: {
          line: 399,
          column: 4
        },
        end: {
          line: 400,
          column: 52
        }
      },
      "42": {
        start: {
          line: 399,
          column: 30
        },
        end: {
          line: 399,
          column: 41
        }
      },
      "43": {
        start: {
          line: 400,
          column: 9
        },
        end: {
          line: 400,
          column: 52
        }
      },
      "44": {
        start: {
          line: 402,
          column: 4
        },
        end: {
          line: 402,
          column: 42
        }
      },
      "45": {
        start: {
          line: 402,
          column: 31
        },
        end: {
          line: 402,
          column: 42
        }
      },
      "46": {
        start: {
          line: 405,
          column: 4
        },
        end: {
          line: 406,
          column: 52
        }
      },
      "47": {
        start: {
          line: 405,
          column: 32
        },
        end: {
          line: 405,
          column: 43
        }
      },
      "48": {
        start: {
          line: 406,
          column: 9
        },
        end: {
          line: 406,
          column: 52
        }
      },
      "49": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 409,
          column: 52
        }
      },
      "50": {
        start: {
          line: 408,
          column: 32
        },
        end: {
          line: 408,
          column: 43
        }
      },
      "51": {
        start: {
          line: 409,
          column: 9
        },
        end: {
          line: 409,
          column: 52
        }
      },
      "52": {
        start: {
          line: 411,
          column: 4
        },
        end: {
          line: 412,
          column: 42
        }
      },
      "53": {
        start: {
          line: 411,
          column: 29
        },
        end: {
          line: 411,
          column: 40
        }
      },
      "54": {
        start: {
          line: 412,
          column: 9
        },
        end: {
          line: 412,
          column: 42
        }
      },
      "55": {
        start: {
          line: 414,
          column: 4
        },
        end: {
          line: 415,
          column: 53
        }
      },
      "56": {
        start: {
          line: 414,
          column: 39
        },
        end: {
          line: 414,
          column: 50
        }
      },
      "57": {
        start: {
          line: 415,
          column: 9
        },
        end: {
          line: 415,
          column: 53
        }
      },
      "58": {
        start: {
          line: 418,
          column: 4
        },
        end: {
          line: 419,
          column: 52
        }
      },
      "59": {
        start: {
          line: 418,
          column: 37
        },
        end: {
          line: 418,
          column: 48
        }
      },
      "60": {
        start: {
          line: 419,
          column: 9
        },
        end: {
          line: 419,
          column: 52
        }
      },
      "61": {
        start: {
          line: 421,
          column: 4
        },
        end: {
          line: 422,
          column: 48
        }
      },
      "62": {
        start: {
          line: 421,
          column: 55
        },
        end: {
          line: 421,
          column: 66
        }
      },
      "63": {
        start: {
          line: 422,
          column: 9
        },
        end: {
          line: 422,
          column: 48
        }
      },
      "64": {
        start: {
          line: 424,
          column: 4
        },
        end: {
          line: 424,
          column: 31
        }
      },
      "65": {
        start: {
          line: 432,
          column: 2
        },
        end: {
          line: 444,
          column: 4
        }
      },
      "66": {
        start: {
          line: 433,
          column: 4
        },
        end: {
          line: 443,
          column: 5
        }
      },
      "67": {
        start: {
          line: 434,
          column: 6
        },
        end: {
          line: 434,
          column: 33
        }
      },
      "68": {
        start: {
          line: 436,
          column: 6
        },
        end: {
          line: 441,
          column: 7
        }
      },
      "69": {
        start: {
          line: 437,
          column: 30
        },
        end: {
          line: 439,
          column: 20
        }
      },
      "70": {
        start: {
          line: 438,
          column: 10
        },
        end: {
          line: 438,
          column: 49
        }
      },
      "71": {
        start: {
          line: 440,
          column: 8
        },
        end: {
          line: 440,
          column: 63
        }
      },
      "72": {
        start: {
          line: 442,
          column: 6
        },
        end: {
          line: 442,
          column: 18
        }
      },
      "73": {
        start: {
          line: 448,
          column: 23
        },
        end: {
          line: 458,
          column: 1
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 14,
            column: 15
          },
          end: {
            line: 14,
            column: 16
          }
        },
        loc: {
          start: {
            line: 14,
            column: 24
          },
          end: {
            line: 14,
            column: 50
          }
        },
        line: 14
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 26,
            column: 15
          },
          end: {
            line: 26,
            column: 16
          }
        },
        loc: {
          start: {
            line: 26,
            column: 23
          },
          end: {
            line: 26,
            column: 34
          }
        },
        line: 26
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 86,
            column: 12
          },
          end: {
            line: 86,
            column: 13
          }
        },
        loc: {
          start: {
            line: 86,
            column: 20
          },
          end: {
            line: 86,
            column: 58
          }
        },
        line: 86
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 118,
            column: 12
          },
          end: {
            line: 118,
            column: 13
          }
        },
        loc: {
          start: {
            line: 118,
            column: 20
          },
          end: {
            line: 118,
            column: 61
          }
        },
        line: 118
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 130,
            column: 17
          },
          end: {
            line: 130,
            column: 18
          }
        },
        loc: {
          start: {
            line: 130,
            column: 26
          },
          end: {
            line: 130,
            column: 38
          }
        },
        line: 130
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 190,
            column: 17
          },
          end: {
            line: 190,
            column: 18
          }
        },
        loc: {
          start: {
            line: 190,
            column: 24
          },
          end: {
            line: 190,
            column: 34
          }
        },
        line: 190
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 209,
            column: 17
          },
          end: {
            line: 209,
            column: 18
          }
        },
        loc: {
          start: {
            line: 209,
            column: 28
          },
          end: {
            line: 209,
            column: 42
          }
        },
        line: 209
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 219,
            column: 17
          },
          end: {
            line: 219,
            column: 18
          }
        },
        loc: {
          start: {
            line: 219,
            column: 28
          },
          end: {
            line: 219,
            column: 42
          }
        },
        line: 219
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 284,
            column: 2
          },
          end: {
            line: 284,
            column: 3
          }
        },
        loc: {
          start: {
            line: 284,
            column: 45
          },
          end: {
            line: 291,
            column: 3
          }
        },
        line: 284
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 296,
            column: 2
          },
          end: {
            line: 296,
            column: 3
          }
        },
        loc: {
          start: {
            line: 296,
            column: 44
          },
          end: {
            line: 303,
            column: 3
          }
        },
        line: 296
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 308,
            column: 2
          },
          end: {
            line: 308,
            column: 3
          }
        },
        loc: {
          start: {
            line: 308,
            column: 71
          },
          end: {
            line: 313,
            column: 3
          }
        },
        line: 308
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 318,
            column: 2
          },
          end: {
            line: 318,
            column: 3
          }
        },
        loc: {
          start: {
            line: 318,
            column: 77
          },
          end: {
            line: 321,
            column: 3
          }
        },
        line: 318
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 326,
            column: 2
          },
          end: {
            line: 326,
            column: 3
          }
        },
        loc: {
          start: {
            line: 326,
            column: 41
          },
          end: {
            line: 349,
            column: 3
          }
        },
        line: 326
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 345,
            column: 38
          },
          end: {
            line: 345,
            column: 39
          }
        },
        loc: {
          start: {
            line: 345,
            column: 49
          },
          end: {
            line: 345,
            column: 66
          }
        },
        line: 345
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 354,
            column: 2
          },
          end: {
            line: 354,
            column: 3
          }
        },
        loc: {
          start: {
            line: 354,
            column: 51
          },
          end: {
            line: 368,
            column: 3
          }
        },
        line: 354
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 373,
            column: 54
          },
          end: {
            line: 386,
            column: 3
          }
        },
        line: 373
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 385,
            column: 34
          },
          end: {
            line: 385,
            column: 35
          }
        },
        loc: {
          start: {
            line: 385,
            column: 45
          },
          end: {
            line: 385,
            column: 64
          }
        },
        line: 385
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 391,
            column: 2
          },
          end: {
            line: 391,
            column: 3
          }
        },
        loc: {
          start: {
            line: 394,
            column: 4
          },
          end: {
            line: 425,
            column: 3
          }
        },
        line: 394
      },
      "18": {
        name: "validateInput",
        decl: {
          start: {
            line: 431,
            column: 16
          },
          end: {
            line: 431,
            column: 29
          }
        },
        loc: {
          start: {
            line: 431,
            column: 57
          },
          end: {
            line: 445,
            column: 1
          }
        },
        line: 431
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 432,
            column: 9
          },
          end: {
            line: 432,
            column: 10
          }
        },
        loc: {
          start: {
            line: 432,
            column: 32
          },
          end: {
            line: 444,
            column: 3
          }
        },
        line: 432
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 437,
            column: 47
          },
          end: {
            line: 437,
            column: 48
          }
        },
        loc: {
          start: {
            line: 438,
            column: 10
          },
          end: {
            line: 438,
            column: 49
          }
        },
        line: 438
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 308,
            column: 37
          },
          end: {
            line: 308,
            column: 61
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 308,
            column: 57
          },
          end: {
            line: 308,
            column: 61
          }
        }],
        line: 308
      },
      "1": {
        loc: {
          start: {
            line: 320,
            column: 11
          },
          end: {
            line: 320,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 320,
            column: 23
          },
          end: {
            line: 320,
            column: 55
          }
        }, {
          start: {
            line: 320,
            column: 58
          },
          end: {
            line: 320,
            column: 63
          }
        }],
        line: 320
      },
      "2": {
        loc: {
          start: {
            line: 332,
            column: 6
          },
          end: {
            line: 334,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 332,
            column: 6
          },
          end: {
            line: 334,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 332
      },
      "3": {
        loc: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 356,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 356,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 356
      },
      "4": {
        loc: {
          start: {
            line: 399,
            column: 4
          },
          end: {
            line: 400,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 399,
            column: 4
          },
          end: {
            line: 400,
            column: 52
          }
        }, {
          start: {
            line: 400,
            column: 9
          },
          end: {
            line: 400,
            column: 52
          }
        }],
        line: 399
      },
      "5": {
        loc: {
          start: {
            line: 402,
            column: 4
          },
          end: {
            line: 402,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 402,
            column: 4
          },
          end: {
            line: 402,
            column: 42
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 402
      },
      "6": {
        loc: {
          start: {
            line: 405,
            column: 4
          },
          end: {
            line: 406,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 405,
            column: 4
          },
          end: {
            line: 406,
            column: 52
          }
        }, {
          start: {
            line: 406,
            column: 9
          },
          end: {
            line: 406,
            column: 52
          }
        }],
        line: 405
      },
      "7": {
        loc: {
          start: {
            line: 408,
            column: 4
          },
          end: {
            line: 409,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 408,
            column: 4
          },
          end: {
            line: 409,
            column: 52
          }
        }, {
          start: {
            line: 409,
            column: 9
          },
          end: {
            line: 409,
            column: 52
          }
        }],
        line: 408
      },
      "8": {
        loc: {
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 412,
            column: 42
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 411,
            column: 4
          },
          end: {
            line: 412,
            column: 42
          }
        }, {
          start: {
            line: 412,
            column: 9
          },
          end: {
            line: 412,
            column: 42
          }
        }],
        line: 411
      },
      "9": {
        loc: {
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 415,
            column: 53
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 414,
            column: 4
          },
          end: {
            line: 415,
            column: 53
          }
        }, {
          start: {
            line: 415,
            column: 9
          },
          end: {
            line: 415,
            column: 53
          }
        }],
        line: 414
      },
      "10": {
        loc: {
          start: {
            line: 418,
            column: 4
          },
          end: {
            line: 419,
            column: 52
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 418,
            column: 4
          },
          end: {
            line: 419,
            column: 52
          }
        }, {
          start: {
            line: 419,
            column: 9
          },
          end: {
            line: 419,
            column: 52
          }
        }],
        line: 418
      },
      "11": {
        loc: {
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 422,
            column: 48
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 421,
            column: 4
          },
          end: {
            line: 422,
            column: 48
          }
        }, {
          start: {
            line: 422,
            column: 9
          },
          end: {
            line: 422,
            column: 48
          }
        }],
        line: 421
      },
      "12": {
        loc: {
          start: {
            line: 436,
            column: 6
          },
          end: {
            line: 441,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 436,
            column: 6
          },
          end: {
            line: 441,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 436
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0
    },
    b: {
      "0": [0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "809ae6c2229afb227d09d88f6f84eaf2bf3edf2c"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1gg4kzmwm7 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1gg4kzmwm7();
import { z } from 'zod';
export var baseSchemas = (cov_1gg4kzmwm7().s[0]++, {
  email: z.string().email('Invalid email format').min(5, 'Email must be at least 5 characters').max(254, 'Email must be less than 254 characters').transform(function (email) {
    cov_1gg4kzmwm7().f[0]++;
    cov_1gg4kzmwm7().s[1]++;
    return email.toLowerCase().trim();
  }),
  password: z.string().min(8, 'Password must be at least 8 characters').max(128, 'Password must be less than 128 characters').regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters').regex(/^[a-zA-Z\s\-'\.]+$/, 'Name can only contain letters, spaces, hyphens, apostrophes, and periods').transform(function (name) {
    cov_1gg4kzmwm7().f[1]++;
    cov_1gg4kzmwm7().s[2]++;
    return name.trim();
  }),
  phone: z.string().regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format').optional(),
  url: z.string().url('Invalid URL format').max(2048, 'URL must be less than 2048 characters').optional(),
  uuid: z.string().uuid('Invalid UUID format'),
  positiveNumber: z.number().positive('Must be a positive number').finite('Must be a finite number'),
  nonNegativeNumber: z.number().nonnegative('Must be a non-negative number').finite('Must be a finite number'),
  dateString: z.string().datetime('Invalid date format'),
  skillLevel: z.enum(['beginner', 'intermediate', 'club', 'advanced', 'professional']),
  visibility: z.enum(['private', 'friends', 'public'])
});
export var tennisSchemas = (cov_1gg4kzmwm7().s[3]++, {
  score: z.number().int('Score must be an integer').min(0, 'Score cannot be negative').max(100, 'Score cannot exceed 100'),
  matchScore: z.string().regex(/^[0-6]-[0-6](\s+[0-6]-[0-6])*$/, 'Invalid match score format'),
  surface: z.enum(['hard', 'clay', 'grass', 'indoor']),
  hand: z.enum(['right', 'left']),
  backhandStyle: z.enum(['oneHanded', 'twoHanded']),
  playingStyle: z.enum(['aggressive', 'defensive', 'allCourt', 'serveVolley', 'counterPuncher']),
  drillCategory: z.enum(['forehand', 'backhand', 'serve', 'volley', 'footwork', 'strategy']),
  difficulty: z.enum(['beginner', 'intermediate', 'advanced'])
});
export var userSchemas = (cov_1gg4kzmwm7().s[4]++, {
  signUp: z.object({
    fullName: baseSchemas.name,
    email: baseSchemas.email,
    password: baseSchemas.password,
    confirmPassword: z.string()
  }).refine(function (data) {
    cov_1gg4kzmwm7().f[2]++;
    cov_1gg4kzmwm7().s[5]++;
    return data.password === data.confirmPassword;
  }, {
    message: "Passwords don't match",
    path: ["confirmPassword"]
  }),
  signIn: z.object({
    email: baseSchemas.email,
    password: z.string().min(1, 'Password is required')
  }),
  resetPassword: z.object({
    email: baseSchemas.email
  }),
  updateProfile: z.object({
    fullName: baseSchemas.name.optional(),
    phone: baseSchemas.phone,
    birthDate: z.string().datetime().optional(),
    location: z.string().max(100, 'Location must be less than 100 characters').optional(),
    bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
    skillLevel: baseSchemas.skillLevel.optional(),
    dominantHand: tennisSchemas.hand.optional(),
    backhandStyle: tennisSchemas.backhandStyle.optional(),
    playingStyle: tennisSchemas.playingStyle.optional(),
    preferredSurface: tennisSchemas.surface.optional(),
    yearsPlaying: z.number().int().min(0).max(100).optional()
  }),
  changePassword: z.object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: baseSchemas.password,
    confirmPassword: z.string()
  }).refine(function (data) {
    cov_1gg4kzmwm7().f[3]++;
    cov_1gg4kzmwm7().s[6]++;
    return data.newPassword === data.confirmPassword;
  }, {
    message: "Passwords don't match",
    path: ["confirmPassword"]
  })
});
export var trainingSchemas = (cov_1gg4kzmwm7().s[7]++, {
  createSession: z.object({
    title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters').transform(function (title) {
      cov_1gg4kzmwm7().f[4]++;
      cov_1gg4kzmwm7().s[8]++;
      return title.trim();
    }),
    notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
    duration: baseSchemas.positiveNumber,
    surface: tennisSchemas.surface,
    location: z.string().max(100, 'Location must be less than 100 characters').optional(),
    drillCategory: tennisSchemas.drillCategory.optional(),
    difficulty: tennisSchemas.difficulty.optional()
  }),
  updateSession: z.object({
    id: baseSchemas.uuid,
    title: z.string().min(1, 'Title is required').max(100, 'Title must be less than 100 characters').optional(),
    notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
    rating: tennisSchemas.score.optional()
  }),
  skillStats: z.object({
    forehand: tennisSchemas.score,
    backhand: tennisSchemas.score,
    serve: tennisSchemas.score,
    volley: tennisSchemas.score,
    footwork: tennisSchemas.score,
    strategy: tennisSchemas.score,
    mental_game: tennisSchemas.score
  })
});
export var matchSchemas = (cov_1gg4kzmwm7().s[9]++, {
  createMatch: z.object({
    opponentName: baseSchemas.name,
    score: tennisSchemas.matchScore,
    result: z.enum(['win', 'loss']),
    duration: baseSchemas.positiveNumber,
    surface: tennisSchemas.surface,
    location: z.string().max(100, 'Location must be less than 100 characters').optional(),
    notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
    aces: baseSchemas.nonNegativeNumber.optional(),
    doubleFaults: baseSchemas.nonNegativeNumber.optional(),
    winners: baseSchemas.nonNegativeNumber.optional(),
    unforcedErrors: baseSchemas.nonNegativeNumber.optional(),
    firstServePercentage: z.number().min(0).max(100).optional(),
    breakPointsWon: baseSchemas.nonNegativeNumber.optional(),
    breakPointsTotal: baseSchemas.nonNegativeNumber.optional()
  })
});
export var aiSchemas = (cov_1gg4kzmwm7().s[10]++, {
  coachingRequest: z.object({
    message: z.string().min(1, 'Message is required').max(500, 'Message must be less than 500 characters').transform(function (msg) {
      cov_1gg4kzmwm7().f[5]++;
      cov_1gg4kzmwm7().s[11]++;
      return msg.trim();
    }),
    context: z.string().max(200, 'Context must be less than 200 characters').optional(),
    skillLevel: baseSchemas.skillLevel.optional()
  }),
  analysisRequest: z.object({
    videoUrl: baseSchemas.url,
    analysisType: z.enum(['technique', 'pose', 'performance']),
    skillLevel: baseSchemas.skillLevel,
    notes: z.string().max(500, 'Notes must be less than 500 characters').optional()
  })
});
export var socialSchemas = (cov_1gg4kzmwm7().s[12]++, {
  createPost: z.object({
    content: z.string().min(1, 'Content is required').max(1000, 'Content must be less than 1000 characters').transform(function (content) {
      cov_1gg4kzmwm7().f[6]++;
      cov_1gg4kzmwm7().s[13]++;
      return content.trim();
    }),
    visibility: baseSchemas.visibility,
    tags: z.array(z.string().max(50)).max(10, 'Maximum 10 tags allowed').optional()
  }),
  sendMessage: z.object({
    recipientId: baseSchemas.uuid,
    content: z.string().min(1, 'Message content is required').max(500, 'Message must be less than 500 characters').transform(function (content) {
      cov_1gg4kzmwm7().f[7]++;
      cov_1gg4kzmwm7().s[14]++;
      return content.trim();
    })
  }),
  addFriend: z.object({
    friendId: baseSchemas.uuid
  })
});
export var privacySchemas = (cov_1gg4kzmwm7().s[15]++, {
  updateSettings: z.object({
    dataCollection: z.boolean().optional(),
    analytics: z.boolean().optional(),
    marketing: z.boolean().optional(),
    socialFeatures: z.boolean().optional(),
    locationTracking: z.boolean().optional(),
    videoAnalysis: z.boolean().optional(),
    aiCoaching: z.boolean().optional(),
    dataSharing: z.boolean().optional(),
    notifications: z.boolean().optional(),
    profileVisibility: baseSchemas.visibility.optional(),
    activityVisibility: baseSchemas.visibility.optional(),
    dataRetentionDays: z.number().int().min(30).max(2555).optional()
  }),
  consentRecord: z.object({
    consentType: z.string().min(1, 'Consent type is required'),
    granted: z.boolean(),
    version: z.string().min(1, 'Version is required')
  })
});
export var fileSchemas = (cov_1gg4kzmwm7().s[16]++, {
  videoUpload: z.object({
    fileName: z.string().min(1, 'File name is required').max(255, 'File name must be less than 255 characters').regex(/\.(mp4|mov|avi|mkv|webm)$/i, 'Invalid video file format'),
    fileSize: z.number().positive('File size must be positive').max(200 * 1024 * 1024, 'File size must be less than 200MB'),
    duration: z.number().positive('Duration must be positive').max(600, 'Video duration must be less than 10 minutes')
  }),
  imageUpload: z.object({
    fileName: z.string().min(1, 'File name is required').max(255, 'File name must be less than 255 characters').regex(/\.(jpg|jpeg|png|gif|webp)$/i, 'Invalid image file format'),
    fileSize: z.number().positive('File size must be positive').max(10 * 1024 * 1024, 'File size must be less than 10MB')
  })
});
export var ValidationUtils = function () {
  function ValidationUtils() {
    _classCallCheck(this, ValidationUtils);
  }
  return _createClass(ValidationUtils, null, [{
    key: "sanitizeHtml",
    value: function sanitizeHtml(input) {
      cov_1gg4kzmwm7().f[8]++;
      cov_1gg4kzmwm7().s[17]++;
      return input.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/'/g, '&#x27;').replace(/\//g, '&#x2F;');
    }
  }, {
    key: "sanitizeSql",
    value: function sanitizeSql(input) {
      cov_1gg4kzmwm7().f[9]++;
      cov_1gg4kzmwm7().s[18]++;
      return input.replace(/'/g, "''").replace(/;/g, '').replace(/--/g, '').replace(/\/\*/g, '').replace(/\*\//g, '');
    }
  }, {
    key: "sanitizeText",
    value: function sanitizeText(input) {
      var maxLength = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1gg4kzmwm7().b[0][0]++, 1000);
      cov_1gg4kzmwm7().f[10]++;
      cov_1gg4kzmwm7().s[19]++;
      return input.trim().slice(0, maxLength).replace(/[^\w\s\-.,!?@#$%^&*()+={}[\]:;"'<>]/g, '');
    }
  }, {
    key: "validateFileType",
    value: function validateFileType(fileName, allowedTypes) {
      cov_1gg4kzmwm7().f[11]++;
      var extension = (cov_1gg4kzmwm7().s[20]++, fileName.toLowerCase().split('.').pop());
      cov_1gg4kzmwm7().s[21]++;
      return extension ? (cov_1gg4kzmwm7().b[1][0]++, allowedTypes.includes(extension)) : (cov_1gg4kzmwm7().b[1][1]++, false);
    }
  }, {
    key: "isUrlSafe",
    value: function isUrlSafe(url) {
      cov_1gg4kzmwm7().f[12]++;
      cov_1gg4kzmwm7().s[22]++;
      try {
        var parsedUrl = (cov_1gg4kzmwm7().s[23]++, new URL(url));
        var safeProtocols = (cov_1gg4kzmwm7().s[24]++, ['http:', 'https:']);
        cov_1gg4kzmwm7().s[25]++;
        if (!safeProtocols.includes(parsedUrl.protocol)) {
          cov_1gg4kzmwm7().b[2][0]++;
          cov_1gg4kzmwm7().s[26]++;
          return false;
        } else {
          cov_1gg4kzmwm7().b[2][1]++;
        }
        var suspiciousPatterns = (cov_1gg4kzmwm7().s[27]++, [/javascript:/i, /data:/i, /vbscript:/i, /file:/i, /ftp:/i]);
        cov_1gg4kzmwm7().s[28]++;
        return !suspiciousPatterns.some(function (pattern) {
          cov_1gg4kzmwm7().f[13]++;
          cov_1gg4kzmwm7().s[29]++;
          return pattern.test(url);
        });
      } catch (_unused) {
        cov_1gg4kzmwm7().s[30]++;
        return false;
      }
    }
  }, {
    key: "isEmailDomainSafe",
    value: function isEmailDomainSafe(email) {
      cov_1gg4kzmwm7().f[14]++;
      var domain = (cov_1gg4kzmwm7().s[31]++, email.split('@')[1]);
      cov_1gg4kzmwm7().s[32]++;
      if (!domain) {
        cov_1gg4kzmwm7().b[3][0]++;
        cov_1gg4kzmwm7().s[33]++;
        return false;
      } else {
        cov_1gg4kzmwm7().b[3][1]++;
      }
      var disposableDomains = (cov_1gg4kzmwm7().s[34]++, ['10minutemail.com', 'tempmail.org', 'guerrillamail.com', 'mailinator.com']);
      cov_1gg4kzmwm7().s[35]++;
      return !disposableDomains.includes(domain.toLowerCase());
    }
  }, {
    key: "hasInjectionPatterns",
    value: function hasInjectionPatterns(input) {
      cov_1gg4kzmwm7().f[15]++;
      var injectionPatterns = (cov_1gg4kzmwm7().s[36]++, [/<script/i, /javascript:/i, /on\w+\s*=/i, /union\s+select/i, /drop\s+table/i, /insert\s+into/i, /delete\s+from/i, /update\s+set/i]);
      cov_1gg4kzmwm7().s[37]++;
      return injectionPatterns.some(function (pattern) {
        cov_1gg4kzmwm7().f[16]++;
        cov_1gg4kzmwm7().s[38]++;
        return pattern.test(input);
      });
    }
  }, {
    key: "getPasswordStrength",
    value: function getPasswordStrength(password) {
      cov_1gg4kzmwm7().f[17]++;
      var feedback = (cov_1gg4kzmwm7().s[39]++, []);
      var score = (cov_1gg4kzmwm7().s[40]++, 0);
      cov_1gg4kzmwm7().s[41]++;
      if (password.length >= 8) {
        cov_1gg4kzmwm7().b[4][0]++;
        cov_1gg4kzmwm7().s[42]++;
        score += 1;
      } else {
        cov_1gg4kzmwm7().b[4][1]++;
        cov_1gg4kzmwm7().s[43]++;
        feedback.push('Use at least 8 characters');
      }
      cov_1gg4kzmwm7().s[44]++;
      if (password.length >= 12) {
        cov_1gg4kzmwm7().b[5][0]++;
        cov_1gg4kzmwm7().s[45]++;
        score += 1;
      } else {
        cov_1gg4kzmwm7().b[5][1]++;
      }
      cov_1gg4kzmwm7().s[46]++;
      if (/[a-z]/.test(password)) {
        cov_1gg4kzmwm7().b[6][0]++;
        cov_1gg4kzmwm7().s[47]++;
        score += 1;
      } else {
        cov_1gg4kzmwm7().b[6][1]++;
        cov_1gg4kzmwm7().s[48]++;
        feedback.push('Include lowercase letters');
      }
      cov_1gg4kzmwm7().s[49]++;
      if (/[A-Z]/.test(password)) {
        cov_1gg4kzmwm7().b[7][0]++;
        cov_1gg4kzmwm7().s[50]++;
        score += 1;
      } else {
        cov_1gg4kzmwm7().b[7][1]++;
        cov_1gg4kzmwm7().s[51]++;
        feedback.push('Include uppercase letters');
      }
      cov_1gg4kzmwm7().s[52]++;
      if (/\d/.test(password)) {
        cov_1gg4kzmwm7().b[8][0]++;
        cov_1gg4kzmwm7().s[53]++;
        score += 1;
      } else {
        cov_1gg4kzmwm7().b[8][1]++;
        cov_1gg4kzmwm7().s[54]++;
        feedback.push('Include numbers');
      }
      cov_1gg4kzmwm7().s[55]++;
      if (/[^A-Za-z0-9]/.test(password)) {
        cov_1gg4kzmwm7().b[9][0]++;
        cov_1gg4kzmwm7().s[56]++;
        score += 1;
      } else {
        cov_1gg4kzmwm7().b[9][1]++;
        cov_1gg4kzmwm7().s[57]++;
        feedback.push('Include special characters');
      }
      cov_1gg4kzmwm7().s[58]++;
      if (!/(.)\1{2,}/.test(password)) {
        cov_1gg4kzmwm7().b[10][0]++;
        cov_1gg4kzmwm7().s[59]++;
        score += 1;
      } else {
        cov_1gg4kzmwm7().b[10][1]++;
        cov_1gg4kzmwm7().s[60]++;
        feedback.push('Avoid repeated characters');
      }
      cov_1gg4kzmwm7().s[61]++;
      if (!/123|abc|qwe|password|admin/i.test(password)) {
        cov_1gg4kzmwm7().b[11][0]++;
        cov_1gg4kzmwm7().s[62]++;
        score += 1;
      } else {
        cov_1gg4kzmwm7().b[11][1]++;
        cov_1gg4kzmwm7().s[63]++;
        feedback.push('Avoid common patterns');
      }
      cov_1gg4kzmwm7().s[64]++;
      return {
        score: score,
        feedback: feedback
      };
    }
  }]);
}();
export function validateInput(schema) {
  cov_1gg4kzmwm7().f[18]++;
  cov_1gg4kzmwm7().s[65]++;
  return function (input) {
    cov_1gg4kzmwm7().f[19]++;
    cov_1gg4kzmwm7().s[66]++;
    try {
      cov_1gg4kzmwm7().s[67]++;
      return schema.parse(input);
    } catch (error) {
      cov_1gg4kzmwm7().s[68]++;
      if (error instanceof z.ZodError) {
        cov_1gg4kzmwm7().b[12][0]++;
        var errorMessages = (cov_1gg4kzmwm7().s[69]++, error.errors.map(function (err) {
          cov_1gg4kzmwm7().f[20]++;
          cov_1gg4kzmwm7().s[70]++;
          return `${err.path.join('.')}: ${err.message}`;
        }).join(', '));
        cov_1gg4kzmwm7().s[71]++;
        throw new Error(`Validation failed: ${errorMessages}`);
      } else {
        cov_1gg4kzmwm7().b[12][1]++;
      }
      cov_1gg4kzmwm7().s[72]++;
      throw error;
    }
  };
}
export var schemas = (cov_1gg4kzmwm7().s[73]++, {
  base: baseSchemas,
  tennis: tennisSchemas,
  user: userSchemas,
  training: trainingSchemas,
  match: matchSchemas,
  ai: aiSchemas,
  social: socialSchemas,
  privacy: privacySchemas,
  file: fileSchemas
});
export default schemas;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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