{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "_react", "React", "_useMergeRefs", "_useHover", "_usePressEvents", "_StyleSheet", "_View", "_excluded", "Pressable", "props", "forwardedRef", "children", "delayLongPress", "delayPressIn", "delayPressOut", "disabled", "onBlur", "onContextMenu", "onFocus", "onHoverIn", "onHoverOut", "onKeyDown", "onLongPress", "onPress", "onPressMove", "onPressIn", "onPressOut", "style", "tabIndex", "testOnly_hovered", "testOnly_pressed", "rest", "_useForceableState", "useForceableState", "hovered", "setHovered", "_useForceableState2", "focused", "setFocused", "_useForceableState3", "pressed", "setPressed", "hostRef", "useRef", "setRef", "pressConfig", "useMemo", "delayPressStart", "delayPressEnd", "onPressChange", "onPressStart", "onPressEnd", "pressEventHandlers", "onContextMenuPress", "onKeyDownPress", "contain", "onHoverChange", "onHoverStart", "onHoverEnd", "interactionState", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "e", "nativeEvent", "target", "current", "focusHandler", "contextMenuHandler", "keyDownHandler", "_tabIndex", "undefined", "createElement", "ref", "styles", "active", "forced", "_useState", "useState", "bool", "setBool", "create", "cursor", "touchAction", "pointerEvents", "MemoedPressable", "memo", "forwardRef", "displayName", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar React = _react;\nvar _useMergeRefs = _interopRequireDefault(require(\"../../modules/useMergeRefs\"));\nvar _useHover = _interopRequireDefault(require(\"../../modules/useHover\"));\nvar _usePressEvents = _interopRequireDefault(require(\"../../modules/usePressEvents\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _excluded = [\"children\", \"delayLongPress\", \"delayPressIn\", \"delayPressOut\", \"disabled\", \"onBlur\", \"onContextMenu\", \"onFocus\", \"onHoverIn\", \"onHoverOut\", \"onKeyDown\", \"onLongPress\", \"onPress\", \"onPressMove\", \"onPressIn\", \"onPressOut\", \"style\", \"tabIndex\", \"testOnly_hovered\", \"testOnly_pressed\"];\n/**\n * Component used to build display components that should respond to whether the\n * component is currently pressed or not.\n */\nfunction Pressable(props, forwardedRef) {\n  var children = props.children,\n    delayLongPress = props.delayLongPress,\n    delayPressIn = props.delayPressIn,\n    delayPressOut = props.delayPressOut,\n    disabled = props.disabled,\n    onBlur = props.onBlur,\n    onContextMenu = props.onContextMenu,\n    onFocus = props.onFocus,\n    onHoverIn = props.onHoverIn,\n    onHoverOut = props.onHoverOut,\n    onKeyDown = props.onKeyDown,\n    onLongPress = props.onLongPress,\n    onPress = props.onPress,\n    onPressMove = props.onPressMove,\n    onPressIn = props.onPressIn,\n    onPressOut = props.onPressOut,\n    style = props.style,\n    tabIndex = props.tabIndex,\n    testOnly_hovered = props.testOnly_hovered,\n    testOnly_pressed = props.testOnly_pressed,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  var _useForceableState = useForceableState(testOnly_hovered === true),\n    hovered = _useForceableState[0],\n    setHovered = _useForceableState[1];\n  var _useForceableState2 = useForceableState(false),\n    focused = _useForceableState2[0],\n    setFocused = _useForceableState2[1];\n  var _useForceableState3 = useForceableState(testOnly_pressed === true),\n    pressed = _useForceableState3[0],\n    setPressed = _useForceableState3[1];\n  var hostRef = (0, _react.useRef)(null);\n  var setRef = (0, _useMergeRefs.default)(forwardedRef, hostRef);\n  var pressConfig = (0, _react.useMemo)(() => ({\n    delayLongPress,\n    delayPressStart: delayPressIn,\n    delayPressEnd: delayPressOut,\n    disabled,\n    onLongPress,\n    onPress,\n    onPressChange: setPressed,\n    onPressStart: onPressIn,\n    onPressMove,\n    onPressEnd: onPressOut\n  }), [delayLongPress, delayPressIn, delayPressOut, disabled, onLongPress, onPress, onPressIn, onPressMove, onPressOut, setPressed]);\n  var pressEventHandlers = (0, _usePressEvents.default)(hostRef, pressConfig);\n  var onContextMenuPress = pressEventHandlers.onContextMenu,\n    onKeyDownPress = pressEventHandlers.onKeyDown;\n  (0, _useHover.default)(hostRef, {\n    contain: true,\n    disabled,\n    onHoverChange: setHovered,\n    onHoverStart: onHoverIn,\n    onHoverEnd: onHoverOut\n  });\n  var interactionState = {\n    hovered,\n    focused,\n    pressed\n  };\n  var blurHandler = React.useCallback(e => {\n    if (e.nativeEvent.target === hostRef.current) {\n      setFocused(false);\n      if (onBlur != null) {\n        onBlur(e);\n      }\n    }\n  }, [hostRef, setFocused, onBlur]);\n  var focusHandler = React.useCallback(e => {\n    if (e.nativeEvent.target === hostRef.current) {\n      setFocused(true);\n      if (onFocus != null) {\n        onFocus(e);\n      }\n    }\n  }, [hostRef, setFocused, onFocus]);\n  var contextMenuHandler = React.useCallback(e => {\n    if (onContextMenuPress != null) {\n      onContextMenuPress(e);\n    }\n    if (onContextMenu != null) {\n      onContextMenu(e);\n    }\n  }, [onContextMenu, onContextMenuPress]);\n  var keyDownHandler = React.useCallback(e => {\n    if (onKeyDownPress != null) {\n      onKeyDownPress(e);\n    }\n    if (onKeyDown != null) {\n      onKeyDown(e);\n    }\n  }, [onKeyDown, onKeyDownPress]);\n  var _tabIndex;\n  if (tabIndex !== undefined) {\n    _tabIndex = tabIndex;\n  } else {\n    _tabIndex = disabled ? -1 : 0;\n  }\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, pressEventHandlers, {\n    \"aria-disabled\": disabled,\n    onBlur: blurHandler,\n    onContextMenu: contextMenuHandler,\n    onFocus: focusHandler,\n    onKeyDown: keyDownHandler,\n    ref: setRef,\n    style: [disabled ? styles.disabled : styles.active, typeof style === 'function' ? style(interactionState) : style],\n    tabIndex: _tabIndex\n  }), typeof children === 'function' ? children(interactionState) : children);\n}\nfunction useForceableState(forced) {\n  var _useState = (0, _react.useState)(false),\n    bool = _useState[0],\n    setBool = _useState[1];\n  return [bool || forced, setBool];\n}\nvar styles = _StyleSheet.default.create({\n  active: {\n    cursor: 'pointer',\n    touchAction: 'manipulation'\n  },\n  disabled: {\n    pointerEvents: 'box-none'\n  }\n});\nvar MemoedPressable = /*#__PURE__*/(0, _react.memo)(/*#__PURE__*/(0, _react.forwardRef)(Pressable));\nMemoedPressable.displayName = 'Pressable';\nvar _default = exports.default = MemoedPressable;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAUZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,MAAM,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACtD,IAAIQ,KAAK,GAAGD,MAAM;AAClB,IAAIE,aAAa,GAAGV,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AACjF,IAAIU,SAAS,GAAGX,sBAAsB,CAACC,OAAO,yBAAyB,CAAC,CAAC;AACzE,IAAIW,eAAe,GAAGZ,sBAAsB,CAACC,OAAO,+BAA+B,CAAC,CAAC;AACrF,IAAIY,WAAW,GAAGb,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIa,KAAK,GAAGd,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIc,SAAS,GAAG,CAAC,UAAU,EAAE,gBAAgB,EAAE,cAAc,EAAE,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,eAAe,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,OAAO,EAAE,UAAU,EAAE,kBAAkB,EAAE,kBAAkB,CAAC;AAK1S,SAASC,SAASA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACtC,IAAIC,QAAQ,GAAGF,KAAK,CAACE,QAAQ;IAC3BC,cAAc,GAAGH,KAAK,CAACG,cAAc;IACrCC,YAAY,GAAGJ,KAAK,CAACI,YAAY;IACjCC,aAAa,GAAGL,KAAK,CAACK,aAAa;IACnCC,QAAQ,GAAGN,KAAK,CAACM,QAAQ;IACzBC,MAAM,GAAGP,KAAK,CAACO,MAAM;IACrBC,aAAa,GAAGR,KAAK,CAACQ,aAAa;IACnCC,OAAO,GAAGT,KAAK,CAACS,OAAO;IACvBC,SAAS,GAAGV,KAAK,CAACU,SAAS;IAC3BC,UAAU,GAAGX,KAAK,CAACW,UAAU;IAC7BC,SAAS,GAAGZ,KAAK,CAACY,SAAS;IAC3BC,WAAW,GAAGb,KAAK,CAACa,WAAW;IAC/BC,OAAO,GAAGd,KAAK,CAACc,OAAO;IACvBC,WAAW,GAAGf,KAAK,CAACe,WAAW;IAC/BC,SAAS,GAAGhB,KAAK,CAACgB,SAAS;IAC3BC,UAAU,GAAGjB,KAAK,CAACiB,UAAU;IAC7BC,KAAK,GAAGlB,KAAK,CAACkB,KAAK;IACnBC,QAAQ,GAAGnB,KAAK,CAACmB,QAAQ;IACzBC,gBAAgB,GAAGpB,KAAK,CAACoB,gBAAgB;IACzCC,gBAAgB,GAAGrB,KAAK,CAACqB,gBAAgB;IACzCC,IAAI,GAAG,CAAC,CAAC,EAAEhC,8BAA8B,CAACL,OAAO,EAAEe,KAAK,EAAEF,SAAS,CAAC;EACtE,IAAIyB,kBAAkB,GAAGC,iBAAiB,CAACJ,gBAAgB,KAAK,IAAI,CAAC;IACnEK,OAAO,GAAGF,kBAAkB,CAAC,CAAC,CAAC;IAC/BG,UAAU,GAAGH,kBAAkB,CAAC,CAAC,CAAC;EACpC,IAAII,mBAAmB,GAAGH,iBAAiB,CAAC,KAAK,CAAC;IAChDI,OAAO,GAAGD,mBAAmB,CAAC,CAAC,CAAC;IAChCE,UAAU,GAAGF,mBAAmB,CAAC,CAAC,CAAC;EACrC,IAAIG,mBAAmB,GAAGN,iBAAiB,CAACH,gBAAgB,KAAK,IAAI,CAAC;IACpEU,OAAO,GAAGD,mBAAmB,CAAC,CAAC,CAAC;IAChCE,UAAU,GAAGF,mBAAmB,CAAC,CAAC,CAAC;EACrC,IAAIG,OAAO,GAAG,CAAC,CAAC,EAAE1C,MAAM,CAAC2C,MAAM,EAAE,IAAI,CAAC;EACtC,IAAIC,MAAM,GAAG,CAAC,CAAC,EAAE1C,aAAa,CAACR,OAAO,EAAEgB,YAAY,EAAEgC,OAAO,CAAC;EAC9D,IAAIG,WAAW,GAAG,CAAC,CAAC,EAAE7C,MAAM,CAAC8C,OAAO,EAAE;IAAA,OAAO;MAC3ClC,cAAc,EAAdA,cAAc;MACdmC,eAAe,EAAElC,YAAY;MAC7BmC,aAAa,EAAElC,aAAa;MAC5BC,QAAQ,EAARA,QAAQ;MACRO,WAAW,EAAXA,WAAW;MACXC,OAAO,EAAPA,OAAO;MACP0B,aAAa,EAAER,UAAU;MACzBS,YAAY,EAAEzB,SAAS;MACvBD,WAAW,EAAXA,WAAW;MACX2B,UAAU,EAAEzB;IACd,CAAC;EAAA,CAAC,EAAE,CAACd,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,EAAEO,WAAW,EAAEC,OAAO,EAAEE,SAAS,EAAED,WAAW,EAAEE,UAAU,EAAEe,UAAU,CAAC,CAAC;EAClI,IAAIW,kBAAkB,GAAG,CAAC,CAAC,EAAEhD,eAAe,CAACV,OAAO,EAAEgD,OAAO,EAAEG,WAAW,CAAC;EAC3E,IAAIQ,kBAAkB,GAAGD,kBAAkB,CAACnC,aAAa;IACvDqC,cAAc,GAAGF,kBAAkB,CAAC/B,SAAS;EAC/C,CAAC,CAAC,EAAElB,SAAS,CAACT,OAAO,EAAEgD,OAAO,EAAE;IAC9Ba,OAAO,EAAE,IAAI;IACbxC,QAAQ,EAARA,QAAQ;IACRyC,aAAa,EAAErB,UAAU;IACzBsB,YAAY,EAAEtC,SAAS;IACvBuC,UAAU,EAAEtC;EACd,CAAC,CAAC;EACF,IAAIuC,gBAAgB,GAAG;IACrBzB,OAAO,EAAPA,OAAO;IACPG,OAAO,EAAPA,OAAO;IACPG,OAAO,EAAPA;EACF,CAAC;EACD,IAAIoB,WAAW,GAAG3D,KAAK,CAAC4D,WAAW,CAAC,UAAAC,CAAC,EAAI;IACvC,IAAIA,CAAC,CAACC,WAAW,CAACC,MAAM,KAAKtB,OAAO,CAACuB,OAAO,EAAE;MAC5C3B,UAAU,CAAC,KAAK,CAAC;MACjB,IAAItB,MAAM,IAAI,IAAI,EAAE;QAClBA,MAAM,CAAC8C,CAAC,CAAC;MACX;IACF;EACF,CAAC,EAAE,CAACpB,OAAO,EAAEJ,UAAU,EAAEtB,MAAM,CAAC,CAAC;EACjC,IAAIkD,YAAY,GAAGjE,KAAK,CAAC4D,WAAW,CAAC,UAAAC,CAAC,EAAI;IACxC,IAAIA,CAAC,CAACC,WAAW,CAACC,MAAM,KAAKtB,OAAO,CAACuB,OAAO,EAAE;MAC5C3B,UAAU,CAAC,IAAI,CAAC;MAChB,IAAIpB,OAAO,IAAI,IAAI,EAAE;QACnBA,OAAO,CAAC4C,CAAC,CAAC;MACZ;IACF;EACF,CAAC,EAAE,CAACpB,OAAO,EAAEJ,UAAU,EAAEpB,OAAO,CAAC,CAAC;EAClC,IAAIiD,kBAAkB,GAAGlE,KAAK,CAAC4D,WAAW,CAAC,UAAAC,CAAC,EAAI;IAC9C,IAAIT,kBAAkB,IAAI,IAAI,EAAE;MAC9BA,kBAAkB,CAACS,CAAC,CAAC;IACvB;IACA,IAAI7C,aAAa,IAAI,IAAI,EAAE;MACzBA,aAAa,CAAC6C,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAAC7C,aAAa,EAAEoC,kBAAkB,CAAC,CAAC;EACvC,IAAIe,cAAc,GAAGnE,KAAK,CAAC4D,WAAW,CAAC,UAAAC,CAAC,EAAI;IAC1C,IAAIR,cAAc,IAAI,IAAI,EAAE;MAC1BA,cAAc,CAACQ,CAAC,CAAC;IACnB;IACA,IAAIzC,SAAS,IAAI,IAAI,EAAE;MACrBA,SAAS,CAACyC,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACzC,SAAS,EAAEiC,cAAc,CAAC,CAAC;EAC/B,IAAIe,SAAS;EACb,IAAIzC,QAAQ,KAAK0C,SAAS,EAAE;IAC1BD,SAAS,GAAGzC,QAAQ;EACtB,CAAC,MAAM;IACLyC,SAAS,GAAGtD,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;EAC/B;EACA,OAAoBd,KAAK,CAACsE,aAAa,CAACjE,KAAK,CAACZ,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEqC,IAAI,EAAEqB,kBAAkB,EAAE;IAC1G,eAAe,EAAErC,QAAQ;IACzBC,MAAM,EAAE4C,WAAW;IACnB3C,aAAa,EAAEkD,kBAAkB;IACjCjD,OAAO,EAAEgD,YAAY;IACrB7C,SAAS,EAAE+C,cAAc;IACzBI,GAAG,EAAE5B,MAAM;IACXjB,KAAK,EAAE,CAACZ,QAAQ,GAAG0D,MAAM,CAAC1D,QAAQ,GAAG0D,MAAM,CAACC,MAAM,EAAE,OAAO/C,KAAK,KAAK,UAAU,GAAGA,KAAK,CAACgC,gBAAgB,CAAC,GAAGhC,KAAK,CAAC;IAClHC,QAAQ,EAAEyC;EACZ,CAAC,CAAC,EAAE,OAAO1D,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACgD,gBAAgB,CAAC,GAAGhD,QAAQ,CAAC;AAC7E;AACA,SAASsB,iBAAiBA,CAAC0C,MAAM,EAAE;EACjC,IAAIC,SAAS,GAAG,CAAC,CAAC,EAAE5E,MAAM,CAAC6E,QAAQ,EAAE,KAAK,CAAC;IACzCC,IAAI,GAAGF,SAAS,CAAC,CAAC,CAAC;IACnBG,OAAO,GAAGH,SAAS,CAAC,CAAC,CAAC;EACxB,OAAO,CAACE,IAAI,IAAIH,MAAM,EAAEI,OAAO,CAAC;AAClC;AACA,IAAIN,MAAM,GAAGpE,WAAW,CAACX,OAAO,CAACsF,MAAM,CAAC;EACtCN,MAAM,EAAE;IACNO,MAAM,EAAE,SAAS;IACjBC,WAAW,EAAE;EACf,CAAC;EACDnE,QAAQ,EAAE;IACRoE,aAAa,EAAE;EACjB;AACF,CAAC,CAAC;AACF,IAAIC,eAAe,GAAgB,CAAC,CAAC,EAAEpF,MAAM,CAACqF,IAAI,EAAe,CAAC,CAAC,EAAErF,MAAM,CAACsF,UAAU,EAAE9E,SAAS,CAAC,CAAC;AACnG4E,eAAe,CAACG,WAAW,GAAG,WAAW;AACzC,IAAIC,QAAQ,GAAG5F,OAAO,CAACF,OAAO,GAAG0F,eAAe;AAChDK,MAAM,CAAC7F,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}