{"version": 3, "names": ["React", "useEffect", "View", "ActivityIndicator", "StyleSheet", "router", "useSegments", "useAuth", "jsx", "_jsx", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON><PERSON>", "_ref", "children", "cov_b7a5bk17a", "f", "_ref2", "s", "user", "loading", "segments", "b", "pathname", "join", "inAuthGroup", "includes", "inTabsGroup", "inOnboarding", "replace", "style", "styles", "loadingContainer", "size", "color", "create", "flex", "justifyContent", "alignItems", "backgroundColor"], "sources": ["AuthGuard.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { View, ActivityIndicator, StyleSheet } from 'react-native';\nimport { router, useSegments } from 'expo-router';\nimport { useAuth } from '@/contexts/AuthContext';\n\ninterface AuthGuardProps {\n  children: React.ReactNode;\n}\n\nexport function AuthGuard({ children }: AuthGuardProps) {\n  const { user, loading } = useAuth();\n  const segments = useSegments();\n\n  useEffect(() => {\n    if (loading) return;\n\n    // Check if we're in auth-related routes by looking at the pathname\n    const pathname = segments.join('/');\n    const inAuthGroup = pathname.includes('auth') || pathname.includes('login') || pathname.includes('register');\n    const inTabsGroup = segments[0] === '(tabs)';\n    const inOnboarding = segments[0] === 'onboarding';\n\n    if (!user && !inAuthGroup && !inOnboarding) {\n      // User is not authenticated and not in auth screens, redirect to login\n      router.replace('/auth/login' as any);\n    } else if (user && inAuthGroup) {\n      // User is authenticated but in auth screens, redirect to main app\n      router.replace('/(tabs)');\n    }\n  }, [user, loading, segments]);\n\n  if (loading) {\n    return (\n      <View style={styles.loadingContainer}>\n        <ActivityIndicator size=\"large\" color=\"#3b82f6\" />\n      </View>\n    );\n  }\n\n  return <>{children}</>;\n}\n\nconst styles = StyleSheet.create({\n  loadingContainer: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    backgroundColor: '#f9fafb',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,IAAI,EAAEC,iBAAiB,EAAEC,UAAU,QAAQ,cAAc;AAClE,SAASC,MAAM,EAAEC,WAAW,QAAQ,aAAa;AACjD,SAASC,OAAO;AAAiC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,QAAA,IAAAC,SAAA;AAMjD,OAAO,SAASC,SAASA,CAAAC,IAAA,EAA+B;EAAA,IAA5BC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;EAAAC,aAAA,GAAAC,CAAA;EAClC,IAAAC,KAAA,IAAAF,aAAA,GAAAG,CAAA,OAA0BX,OAAO,CAAC,CAAC;IAA3BY,IAAI,GAAAF,KAAA,CAAJE,IAAI;IAAEC,OAAO,GAAAH,KAAA,CAAPG,OAAO;EACrB,IAAMC,QAAQ,IAAAN,aAAA,GAAAG,CAAA,OAAGZ,WAAW,CAAC,CAAC;EAACS,aAAA,GAAAG,CAAA;EAE/BjB,SAAS,CAAC,YAAM;IAAAc,aAAA,GAAAC,CAAA;IAAAD,aAAA,GAAAG,CAAA;IACd,IAAIE,OAAO,EAAE;MAAAL,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAG,CAAA;MAAA;IAAM,CAAC;MAAAH,aAAA,GAAAO,CAAA;IAAA;IAGpB,IAAMC,QAAQ,IAAAR,aAAA,GAAAG,CAAA,OAAGG,QAAQ,CAACG,IAAI,CAAC,GAAG,CAAC;IACnC,IAAMC,WAAW,IAAAV,aAAA,GAAAG,CAAA,OAAG,CAAAH,aAAA,GAAAO,CAAA,UAAAC,QAAQ,CAACG,QAAQ,CAAC,MAAM,CAAC,MAAAX,aAAA,GAAAO,CAAA,UAAIC,QAAQ,CAACG,QAAQ,CAAC,OAAO,CAAC,MAAAX,aAAA,GAAAO,CAAA,UAAIC,QAAQ,CAACG,QAAQ,CAAC,UAAU,CAAC;IAC5G,IAAMC,WAAW,IAAAZ,aAAA,GAAAG,CAAA,OAAGG,QAAQ,CAAC,CAAC,CAAC,KAAK,QAAQ;IAC5C,IAAMO,YAAY,IAAAb,aAAA,GAAAG,CAAA,OAAGG,QAAQ,CAAC,CAAC,CAAC,KAAK,YAAY;IAACN,aAAA,GAAAG,CAAA;IAElD,IAAI,CAAAH,aAAA,GAAAO,CAAA,WAACH,IAAI,MAAAJ,aAAA,GAAAO,CAAA,UAAI,CAACG,WAAW,MAAAV,aAAA,GAAAO,CAAA,UAAI,CAACM,YAAY,GAAE;MAAAb,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAG,CAAA;MAE1Cb,MAAM,CAACwB,OAAO,CAAC,aAAoB,CAAC;IACtC,CAAC,MAAM;MAAAd,aAAA,GAAAO,CAAA;MAAAP,aAAA,GAAAG,CAAA;MAAA,IAAI,CAAAH,aAAA,GAAAO,CAAA,UAAAH,IAAI,MAAAJ,aAAA,GAAAO,CAAA,UAAIG,WAAW,GAAE;QAAAV,aAAA,GAAAO,CAAA;QAAAP,aAAA,GAAAG,CAAA;QAE9Bb,MAAM,CAACwB,OAAO,CAAC,SAAS,CAAC;MAC3B,CAAC;QAAAd,aAAA,GAAAO,CAAA;MAAA;IAAD;EACF,CAAC,EAAE,CAACH,IAAI,EAAEC,OAAO,EAAEC,QAAQ,CAAC,CAAC;EAACN,aAAA,GAAAG,CAAA;EAE9B,IAAIE,OAAO,EAAE;IAAAL,aAAA,GAAAO,CAAA;IAAAP,aAAA,GAAAG,CAAA;IACX,OACET,IAAA,CAACP,IAAI;MAAC4B,KAAK,EAAEC,MAAM,CAACC,gBAAiB;MAAAlB,QAAA,EACnCL,IAAA,CAACN,iBAAiB;QAAC8B,IAAI,EAAC,OAAO;QAACC,KAAK,EAAC;MAAS,CAAE;IAAC,CAC9C,CAAC;EAEX,CAAC;IAAAnB,aAAA,GAAAO,CAAA;EAAA;EAAAP,aAAA,GAAAG,CAAA;EAED,OAAOT,IAAA,CAAAE,SAAA;IAAAG,QAAA,EAAGA;EAAQ,CAAG,CAAC;AACxB;AAEA,IAAMiB,MAAM,IAAAhB,aAAA,GAAAG,CAAA,QAAGd,UAAU,CAAC+B,MAAM,CAAC;EAC/BH,gBAAgB,EAAE;IAChBI,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,eAAe,EAAE;EACnB;AACF,CAAC,CAAC", "ignoreList": []}