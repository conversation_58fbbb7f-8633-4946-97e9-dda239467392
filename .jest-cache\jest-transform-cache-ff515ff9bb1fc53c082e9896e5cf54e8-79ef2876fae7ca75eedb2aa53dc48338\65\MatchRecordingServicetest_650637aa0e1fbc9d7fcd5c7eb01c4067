f194d3a8ec65c433d1d540ba6a4d74fd
_getJestObj().mock("../../services/video/VideoRecordingService");
_getJestObj().mock("../../services/database/MatchRepository");
_getJestObj().mock("../../services/storage/FileUploadService");
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _MatchRecordingService = require("../../services/match/MatchRecordingService");
var _VideoRecordingService = require("../../services/video/VideoRecordingService");
var _MatchRepository = require("../../services/database/MatchRepository");
var _FileUploadService = require("../../services/storage/FileUploadService");
function _getJestObj() {
  var _require = require("@jest/globals"),
    jest = _require.jest;
  _getJestObj = function _getJestObj() {
    return jest;
  };
  return jest;
}
var mockVideoService = _VideoRecordingService.videoRecordingService;
var mockMatchRepository = _MatchRepository.matchRepository;
var mockFileUploadService = _FileUploadService.fileUploadService;
describe('MatchRecordingService', function () {
  var mockMetadata = {
    userId: 'user-123',
    opponentName: 'John Doe',
    matchType: 'friendly',
    matchFormat: 'best_of_3',
    surface: 'hard',
    location: 'Local Tennis Club',
    matchDate: '2024-01-15'
  };
  var mockOptions = {
    enableVideoRecording: true,
    enableAutoScoreDetection: false,
    videoConfig: {
      quality: 'medium',
      fps: 30,
      resolution: '720p',
      codec: 'h264',
      maxDurationMinutes: 180,
      maxFileSizeMB: 500,
      enableAudio: true,
      enableStabilization: true
    },
    enableStatisticsTracking: true
  };
  beforeEach(function () {
    jest.clearAllMocks();
    if (_MatchRecordingService.matchRecordingService.getCurrentSession()) {
      _MatchRecordingService.matchRecordingService.cancelMatch();
    }
  });
  describe('startMatch', function () {
    it('should start a new match recording session successfully', (0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      var session = yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
      expect(session).toBeDefined();
      expect(session.match.metadata.opponentName).toBe('John Doe');
      expect(session.isRecording).toBe(true);
      expect(session.videoRecordingActive).toBe(true);
      expect(mockVideoService.startRecording).toHaveBeenCalledWith(mockOptions.videoConfig);
      expect(mockMatchRepository.createMatch).toHaveBeenCalled();
    }));
    it('should throw error for invalid metadata', (0, _asyncToGenerator2.default)(function* () {
      var invalidMetadata = Object.assign({}, mockMetadata, {
        opponentName: ''
      });
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(invalidMetadata, mockOptions)).rejects.toThrow('Opponent name is required');
    }));
    it('should handle video recording failure gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockRejectedValue(new Error('Camera not available'));
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions)).rejects.toThrow('Camera not available');
    }));
    it('should handle database save failure', (0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: null,
        error: 'Database connection failed'
      });
      yield expect(_MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions)).rejects.toThrow('Database connection failed');
    }));
  });
  describe('addPoint', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
    }));
    it('should add a point for user successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.addPoint('user', 'winner', 'forehand');
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session).toBeDefined();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should add a point for opponent successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.addPoint('opponent', 'ace');
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session).toBeDefined();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should throw error when no active session', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.cancelMatch();
      yield expect(_MatchRecordingService.matchRecordingService.addPoint('user')).rejects.toThrow('No active match session');
    }));
    it('should update statistics correctly', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.addPoint('user', 'ace');
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session == null ? void 0 : session.match.statistics.aces).toBe(1);
      expect(session == null ? void 0 : session.match.statistics.totalPointsWon).toBe(1);
      expect(session == null ? void 0 : session.match.statistics.totalPointsPlayed).toBe(1);
    }));
  });
  describe('pauseMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockVideoService.pauseRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
    }));
    it('should pause match successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session == null ? void 0 : session.isPaused).toBe(true);
      expect(session == null ? void 0 : session.match.status).toBe('paused');
      expect(mockVideoService.pauseRecording).toHaveBeenCalled();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should not pause if already paused', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
      jest.clearAllMocks();
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
      expect(mockVideoService.pauseRecording).not.toHaveBeenCalled();
    }));
  });
  describe('resumeMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockVideoService.pauseRecording.mockResolvedValue(undefined);
      mockVideoService.resumeRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
      yield _MatchRecordingService.matchRecordingService.pauseMatch();
    }));
    it('should resume match successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.resumeMatch();
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session == null ? void 0 : session.isPaused).toBe(false);
      expect(session == null ? void 0 : session.match.status).toBe('recording');
      expect(mockVideoService.resumeRecording).toHaveBeenCalled();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should not resume if not paused', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.resumeMatch();
      jest.clearAllMocks();
      yield _MatchRecordingService.matchRecordingService.resumeMatch();
      expect(mockVideoService.resumeRecording).not.toHaveBeenCalled();
    }));
  });
  describe('endMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockVideoService.stopRecording.mockResolvedValue({
        uri: 'file://video.mp4',
        duration: 3600,
        fileSize: 50000000,
        width: 1920,
        height: 1080,
        thumbnail: 'file://thumbnail.jpg'
      });
      mockFileUploadService.uploadVideo.mockResolvedValue({
        data: {
          url: 'https://storage.supabase.co/video.mp4',
          path: 'matches/match-123/video.mp4',
          size: 50000000,
          type: 'video'
        },
        error: null
      });
      mockFileUploadService.uploadThumbnail.mockResolvedValue({
        data: {
          url: 'https://storage.supabase.co/thumbnail.jpg',
          path: 'matches/match-123/thumbnail.jpg',
          size: 100000,
          type: 'image'
        },
        error: null
      });
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
    }));
    it('should end match successfully with video upload', (0, _asyncToGenerator2.default)(function* () {
      var result = yield _MatchRecordingService.matchRecordingService.endMatch();
      expect(result).toBeDefined();
      expect(result.status).toBe('completed');
      expect(result.videoUrl).toBe('https://storage.supabase.co/video.mp4');
      expect(result.videoThumbnailUrl).toBe('https://storage.supabase.co/thumbnail.jpg');
      expect(mockVideoService.stopRecording).toHaveBeenCalled();
      expect(mockFileUploadService.uploadVideo).toHaveBeenCalled();
      expect(mockFileUploadService.uploadThumbnail).toHaveBeenCalled();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalled();
    }));
    it('should handle video upload failure gracefully', (0, _asyncToGenerator2.default)(function* () {
      mockFileUploadService.uploadVideo.mockResolvedValue({
        data: null,
        error: 'Upload failed'
      });
      var result = yield _MatchRecordingService.matchRecordingService.endMatch();
      expect(result).toBeDefined();
      expect(result.status).toBe('completed');
      expect(result.videoUrl).toBeUndefined();
    }));
    it('should throw error when no active session', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.cancelMatch();
      yield expect(_MatchRecordingService.matchRecordingService.endMatch()).rejects.toThrow('No active match session');
    }));
  });
  describe('cancelMatch', function () {
    beforeEach((0, _asyncToGenerator2.default)(function* () {
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockVideoService.stopRecording.mockResolvedValue({
        uri: 'file://video.mp4',
        duration: 3600,
        fileSize: 50000000,
        width: 1920,
        height: 1080
      });
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      mockMatchRepository.updateMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
    }));
    it('should cancel match successfully', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.cancelMatch();
      var session = _MatchRecordingService.matchRecordingService.getCurrentSession();
      expect(session).toBeNull();
      expect(mockVideoService.stopRecording).toHaveBeenCalled();
      expect(mockMatchRepository.updateMatch).toHaveBeenCalledWith(expect.any(String), expect.objectContaining({
        status: 'cancelled'
      }));
    }));
    it('should handle cancellation when no active session', (0, _asyncToGenerator2.default)(function* () {
      yield _MatchRecordingService.matchRecordingService.cancelMatch();
      yield expect(_MatchRecordingService.matchRecordingService.cancelMatch()).resolves.toBeUndefined();
    }));
  });
  describe('session listeners', function () {
    it('should notify session listeners on session changes', (0, _asyncToGenerator2.default)(function* () {
      var listener = jest.fn();
      _MatchRecordingService.matchRecordingService.addSessionListener(listener);
      mockVideoService.startRecording.mockResolvedValue(undefined);
      mockMatchRepository.createMatch.mockResolvedValue({
        data: {
          id: 'match-123'
        },
        error: null
      });
      yield _MatchRecordingService.matchRecordingService.startMatch(mockMetadata, mockOptions);
      expect(listener).toHaveBeenCalledWith(expect.objectContaining({
        isRecording: true
      }));
    }));
    it('should remove session listeners correctly', function () {
      var listener = jest.fn();
      _MatchRecordingService.matchRecordingService.addSessionListener(listener);
      _MatchRecordingService.matchRecordingService.removeSessionListener(listener);
    });
  });
});
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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