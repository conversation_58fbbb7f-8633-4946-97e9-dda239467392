66ab804ba40a3b2445658d0ab5c0f91d
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchRecordingService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _VideoRecordingService = require("../video/VideoRecordingService");
var _MatchRepository = require("../database/MatchRepository");
var _FileUploadService = require("../storage/FileUploadService");
var _performance = require("../../../utils/performance");
function cov_2k9ta5zj0b() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\match\\MatchRecordingService.ts";
  var hash = "a74cddf9dd12a1d0ea03499a43d630188d9966fd";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\match\\MatchRecordingService.ts",
    statementMap: {
      "0": {
        start: {
          line: 30,
          column: 48
        },
        end: {
          line: 30,
          column: 52
        }
      },
      "1": {
        start: {
          line: 31,
          column: 73
        },
        end: {
          line: 31,
          column: 75
        }
      },
      "2": {
        start: {
          line: 32,
          column: 60
        },
        end: {
          line: 32,
          column: 62
        }
      },
      "3": {
        start: {
          line: 41,
          column: 4
        },
        end: {
          line: 115,
          column: 5
        }
      },
      "4": {
        start: {
          line: 42,
          column: 6
        },
        end: {
          line: 42,
          column: 56
        }
      },
      "5": {
        start: {
          line: 45,
          column: 6
        },
        end: {
          line: 45,
          column: 43
        }
      },
      "6": {
        start: {
          line: 48,
          column: 6
        },
        end: {
          line: 50,
          column: 7
        }
      },
      "7": {
        start: {
          line: 49,
          column: 8
        },
        end: {
          line: 49,
          column: 74
        }
      },
      "8": {
        start: {
          line: 53,
          column: 45
        },
        end: {
          line: 64,
          column: 7
        }
      },
      "9": {
        start: {
          line: 67,
          column: 36
        },
        end: {
          line: 79,
          column: 7
        }
      },
      "10": {
        start: {
          line: 82,
          column: 6
        },
        end: {
          line: 84,
          column: 7
        }
      },
      "11": {
        start: {
          line: 83,
          column: 8
        },
        end: {
          line: 83,
          column: 72
        }
      },
      "12": {
        start: {
          line: 87,
          column: 25
        },
        end: {
          line: 87,
          column: 71
        }
      },
      "13": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 90,
          column: 7
        }
      },
      "14": {
        start: {
          line: 89,
          column: 8
        },
        end: {
          line: 89,
          column: 80
        }
      },
      "15": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 45
        }
      },
      "16": {
        start: {
          line: 93,
          column: 6
        },
        end: {
          line: 93,
          column: 61
        }
      },
      "17": {
        start: {
          line: 96,
          column: 6
        },
        end: {
          line: 96,
          column: 46
        }
      },
      "18": {
        start: {
          line: 98,
          column: 6
        },
        end: {
          line: 98,
          column: 36
        }
      },
      "19": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 36
        }
      },
      "20": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 27
        }
      },
      "21": {
        start: {
          line: 104,
          column: 6
        },
        end: {
          line: 104,
          column: 54
        }
      },
      "22": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 21
        }
      },
      "23": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 63
        }
      },
      "24": {
        start: {
          line: 110,
          column: 6
        },
        end: {
          line: 112,
          column: 7
        }
      },
      "25": {
        start: {
          line: 111,
          column: 8
        },
        end: {
          line: 111,
          column: 42
        }
      },
      "26": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 18
        }
      },
      "27": {
        start: {
          line: 127,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "28": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 49
        }
      },
      "29": {
        start: {
          line: 131,
          column: 4
        },
        end: {
          line: 193,
          column: 5
        }
      },
      "30": {
        start: {
          line: 132,
          column: 22
        },
        end: {
          line: 132,
          column: 41
        }
      },
      "31": {
        start: {
          line: 133,
          column: 25
        },
        end: {
          line: 133,
          column: 43
        }
      },
      "32": {
        start: {
          line: 134,
          column: 26
        },
        end: {
          line: 134,
          column: 45
        }
      },
      "33": {
        start: {
          line: 137,
          column: 35
        },
        end: {
          line: 145,
          column: 7
        }
      },
      "34": {
        start: {
          line: 148,
          column: 27
        },
        end: {
          line: 154,
          column: 7
        }
      },
      "35": {
        start: {
          line: 157,
          column: 6
        },
        end: {
          line: 157,
          column: 65
        }
      },
      "36": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 41
        }
      },
      "37": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 161,
          column: 57
        }
      },
      "38": {
        start: {
          line: 164,
          column: 26
        },
        end: {
          line: 164,
          column: 79
        }
      },
      "39": {
        start: {
          line: 165,
          column: 28
        },
        end: {
          line: 165,
          column: 98
        }
      },
      "40": {
        start: {
          line: 167,
          column: 6
        },
        end: {
          line: 179,
          column: 7
        }
      },
      "41": {
        start: {
          line: 168,
          column: 8
        },
        end: {
          line: 168,
          column: 29
        }
      },
      "42": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 32
        }
      },
      "43": {
        start: {
          line: 170,
          column: 13
        },
        end: {
          line: 179,
          column: 7
        }
      },
      "44": {
        start: {
          line: 172,
          column: 29
        },
        end: {
          line: 175,
          column: 9
        }
      },
      "45": {
        start: {
          line: 176,
          column: 8
        },
        end: {
          line: 178,
          column: 9
        }
      },
      "46": {
        start: {
          line: 177,
          column: 10
        },
        end: {
          line: 177,
          column: 32
        }
      },
      "47": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 186,
          column: 7
        }
      },
      "48": {
        start: {
          line: 182,
          column: 8
        },
        end: {
          line: 182,
          column: 30
        }
      },
      "49": {
        start: {
          line: 185,
          column: 8
        },
        end: {
          line: 185,
          column: 56
        }
      },
      "50": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 34
        }
      },
      "51": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 36
        }
      },
      "52": {
        start: {
          line: 191,
          column: 6
        },
        end: {
          line: 191,
          column: 51
        }
      },
      "53": {
        start: {
          line: 192,
          column: 6
        },
        end: {
          line: 192,
          column: 18
        }
      },
      "54": {
        start: {
          line: 200,
          column: 4
        },
        end: {
          line: 202,
          column: 5
        }
      },
      "55": {
        start: {
          line: 201,
          column: 6
        },
        end: {
          line: 201,
          column: 13
        }
      },
      "56": {
        start: {
          line: 204,
          column: 4
        },
        end: {
          line: 219,
          column: 5
        }
      },
      "57": {
        start: {
          line: 205,
          column: 6
        },
        end: {
          line: 205,
          column: 42
        }
      },
      "58": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 206,
          column: 50
        }
      },
      "59": {
        start: {
          line: 207,
          column: 6
        },
        end: {
          line: 207,
          column: 50
        }
      },
      "60": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 212,
          column: 7
        }
      },
      "61": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 211,
          column: 53
        }
      },
      "62": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 66
        }
      },
      "63": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 36
        }
      },
      "64": {
        start: {
          line: 217,
          column: 6
        },
        end: {
          line: 217,
          column: 53
        }
      },
      "65": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 18
        }
      },
      "66": {
        start: {
          line: 226,
          column: 4
        },
        end: {
          line: 228,
          column: 5
        }
      },
      "67": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 13
        }
      },
      "68": {
        start: {
          line: 230,
          column: 4
        },
        end: {
          line: 247,
          column: 5
        }
      },
      "69": {
        start: {
          line: 231,
          column: 28
        },
        end: {
          line: 231,
          column: 71
        }
      },
      "70": {
        start: {
          line: 232,
          column: 6
        },
        end: {
          line: 232,
          column: 63
        }
      },
      "71": {
        start: {
          line: 233,
          column: 6
        },
        end: {
          line: 233,
          column: 43
        }
      },
      "72": {
        start: {
          line: 234,
          column: 6
        },
        end: {
          line: 234,
          column: 41
        }
      },
      "73": {
        start: {
          line: 235,
          column: 6
        },
        end: {
          line: 235,
          column: 53
        }
      },
      "74": {
        start: {
          line: 238,
          column: 6
        },
        end: {
          line: 240,
          column: 7
        }
      },
      "75": {
        start: {
          line: 239,
          column: 8
        },
        end: {
          line: 239,
          column: 54
        }
      },
      "76": {
        start: {
          line: 242,
          column: 6
        },
        end: {
          line: 242,
          column: 66
        }
      },
      "77": {
        start: {
          line: 243,
          column: 6
        },
        end: {
          line: 243,
          column: 36
        }
      },
      "78": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 54
        }
      },
      "79": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 246,
          column: 18
        }
      },
      "80": {
        start: {
          line: 254,
          column: 4
        },
        end: {
          line: 256,
          column: 5
        }
      },
      "81": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 49
        }
      },
      "82": {
        start: {
          line: 258,
          column: 4
        },
        end: {
          line: 316,
          column: 5
        }
      },
      "83": {
        start: {
          line: 259,
          column: 6
        },
        end: {
          line: 259,
          column: 54
        }
      },
      "84": {
        start: {
          line: 261,
          column: 22
        },
        end: {
          line: 261,
          column: 41
        }
      },
      "85": {
        start: {
          line: 262,
          column: 22
        },
        end: {
          line: 262,
          column: 32
        }
      },
      "86": {
        start: {
          line: 263,
          column: 28
        },
        end: {
          line: 263,
          column: 99
        }
      },
      "87": {
        start: {
          line: 266,
          column: 6
        },
        end: {
          line: 266,
          column: 64
        }
      },
      "88": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 73
        }
      },
      "89": {
        start: {
          line: 268,
          column: 6
        },
        end: {
          line: 268,
          column: 41
        }
      },
      "90": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 299,
          column: 7
        }
      },
      "91": {
        start: {
          line: 272,
          column: 28
        },
        end: {
          line: 272,
          column: 71
        }
      },
      "92": {
        start: {
          line: 275,
          column: 29
        },
        end: {
          line: 277,
          column: 10
        }
      },
      "93": {
        start: {
          line: 279,
          column: 8
        },
        end: {
          line: 298,
          column: 9
        }
      },
      "94": {
        start: {
          line: 280,
          column: 10
        },
        end: {
          line: 280,
          column: 57
        }
      },
      "95": {
        start: {
          line: 281,
          column: 10
        },
        end: {
          line: 281,
          column: 68
        }
      },
      "96": {
        start: {
          line: 282,
          column: 10
        },
        end: {
          line: 282,
          column: 68
        }
      },
      "97": {
        start: {
          line: 285,
          column: 10
        },
        end: {
          line: 297,
          column: 11
        }
      },
      "98": {
        start: {
          line: 286,
          column: 36
        },
        end: {
          line: 292,
          column: 13
        }
      },
      "99": {
        start: {
          line: 294,
          column: 12
        },
        end: {
          line: 296,
          column: 13
        }
      },
      "100": {
        start: {
          line: 295,
          column: 14
        },
        end: {
          line: 295,
          column: 73
        }
      },
      "101": {
        start: {
          line: 302,
          column: 6
        },
        end: {
          line: 302,
          column: 83
        }
      },
      "102": {
        start: {
          line: 305,
          column: 25
        },
        end: {
          line: 305,
          column: 72
        }
      },
      "103": {
        start: {
          line: 308,
          column: 6
        },
        end: {
          line: 308,
          column: 33
        }
      },
      "104": {
        start: {
          line: 309,
          column: 6
        },
        end: {
          line: 309,
          column: 36
        }
      },
      "105": {
        start: {
          line: 311,
          column: 6
        },
        end: {
          line: 311,
          column: 52
        }
      },
      "106": {
        start: {
          line: 312,
          column: 6
        },
        end: {
          line: 312,
          column: 24
        }
      },
      "107": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 314,
          column: 51
        }
      },
      "108": {
        start: {
          line: 315,
          column: 6
        },
        end: {
          line: 315,
          column: 18
        }
      },
      "109": {
        start: {
          line: 323,
          column: 4
        },
        end: {
          line: 325,
          column: 5
        }
      },
      "110": {
        start: {
          line: 324,
          column: 6
        },
        end: {
          line: 324,
          column: 13
        }
      },
      "111": {
        start: {
          line: 327,
          column: 4
        },
        end: {
          line: 343,
          column: 5
        }
      },
      "112": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 331,
          column: 7
        }
      },
      "113": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 330,
          column: 52
        }
      },
      "114": {
        start: {
          line: 334,
          column: 6
        },
        end: {
          line: 334,
          column: 53
        }
      },
      "115": {
        start: {
          line: 335,
          column: 6
        },
        end: {
          line: 335,
          column: 66
        }
      },
      "116": {
        start: {
          line: 338,
          column: 6
        },
        end: {
          line: 338,
          column: 33
        }
      },
      "117": {
        start: {
          line: 339,
          column: 6
        },
        end: {
          line: 339,
          column: 36
        }
      },
      "118": {
        start: {
          line: 341,
          column: 6
        },
        end: {
          line: 341,
          column: 54
        }
      },
      "119": {
        start: {
          line: 342,
          column: 6
        },
        end: {
          line: 342,
          column: 18
        }
      },
      "120": {
        start: {
          line: 350,
          column: 4
        },
        end: {
          line: 350,
          column: 31
        }
      },
      "121": {
        start: {
          line: 357,
          column: 4
        },
        end: {
          line: 357,
          column: 41
        }
      },
      "122": {
        start: {
          line: 364,
          column: 4
        },
        end: {
          line: 364,
          column: 78
        }
      },
      "123": {
        start: {
          line: 364,
          column: 62
        },
        end: {
          line: 364,
          column: 76
        }
      },
      "124": {
        start: {
          line: 371,
          column: 4
        },
        end: {
          line: 371,
          column: 39
        }
      },
      "125": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 74
        }
      },
      "126": {
        start: {
          line: 378,
          column: 58
        },
        end: {
          line: 378,
          column: 72
        }
      },
      "127": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 386,
          column: 5
        }
      },
      "128": {
        start: {
          line: 385,
          column: 6
        },
        end: {
          line: 385,
          column: 51
        }
      },
      "129": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 389,
          column: 5
        }
      },
      "130": {
        start: {
          line: 388,
          column: 6
        },
        end: {
          line: 388,
          column: 45
        }
      },
      "131": {
        start: {
          line: 390,
          column: 4
        },
        end: {
          line: 392,
          column: 5
        }
      },
      "132": {
        start: {
          line: 391,
          column: 6
        },
        end: {
          line: 391,
          column: 48
        }
      },
      "133": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 395,
          column: 5
        }
      },
      "134": {
        start: {
          line: 394,
          column: 6
        },
        end: {
          line: 394,
          column: 50
        }
      },
      "135": {
        start: {
          line: 396,
          column: 4
        },
        end: {
          line: 398,
          column: 5
        }
      },
      "136": {
        start: {
          line: 397,
          column: 6
        },
        end: {
          line: 397,
          column: 51
        }
      },
      "137": {
        start: {
          line: 402,
          column: 20
        },
        end: {
          line: 402,
          column: 50
        }
      },
      "138": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 409,
          column: 6
        }
      },
      "139": {
        start: {
          line: 413,
          column: 4
        },
        end: {
          line: 437,
          column: 6
        }
      },
      "140": {
        start: {
          line: 449,
          column: 25
        },
        end: {
          line: 449,
          column: 44
        }
      },
      "141": {
        start: {
          line: 452,
          column: 4
        },
        end: {
          line: 460,
          column: 5
        }
      },
      "142": {
        start: {
          line: 453,
          column: 6
        },
        end: {
          line: 459,
          column: 9
        }
      },
      "143": {
        start: {
          line: 462,
          column: 23
        },
        end: {
          line: 462,
          column: 55
        }
      },
      "144": {
        start: {
          line: 465,
          column: 4
        },
        end: {
          line: 470,
          column: 5
        }
      },
      "145": {
        start: {
          line: 472,
          column: 4
        },
        end: {
          line: 472,
          column: 24
        }
      },
      "146": {
        start: {
          line: 476,
          column: 4
        },
        end: {
          line: 476,
          column: 35
        }
      },
      "147": {
        start: {
          line: 478,
          column: 4
        },
        end: {
          line: 480,
          column: 5
        }
      },
      "148": {
        start: {
          line: 479,
          column: 6
        },
        end: {
          line: 479,
          column: 34
        }
      },
      "149": {
        start: {
          line: 482,
          column: 4
        },
        end: {
          line: 498,
          column: 5
        }
      },
      "150": {
        start: {
          line: 484,
          column: 8
        },
        end: {
          line: 484,
          column: 26
        }
      },
      "151": {
        start: {
          line: 485,
          column: 8
        },
        end: {
          line: 485,
          column: 14
        }
      },
      "152": {
        start: {
          line: 487,
          column: 8
        },
        end: {
          line: 487,
          column: 34
        }
      },
      "153": {
        start: {
          line: 488,
          column: 8
        },
        end: {
          line: 488,
          column: 14
        }
      },
      "154": {
        start: {
          line: 490,
          column: 8
        },
        end: {
          line: 490,
          column: 29
        }
      },
      "155": {
        start: {
          line: 491,
          column: 8
        },
        end: {
          line: 491,
          column: 14
        }
      },
      "156": {
        start: {
          line: 493,
          column: 8
        },
        end: {
          line: 493,
          column: 36
        }
      },
      "157": {
        start: {
          line: 494,
          column: 8
        },
        end: {
          line: 494,
          column: 14
        }
      },
      "158": {
        start: {
          line: 496,
          column: 8
        },
        end: {
          line: 496,
          column: 34
        }
      },
      "159": {
        start: {
          line: 497,
          column: 8
        },
        end: {
          line: 497,
          column: 14
        }
      },
      "160": {
        start: {
          line: 503,
          column: 4
        },
        end: {
          line: 505,
          column: 26
        }
      },
      "161": {
        start: {
          line: 510,
          column: 4
        },
        end: {
          line: 510,
          column: 16
        }
      },
      "162": {
        start: {
          line: 514,
          column: 22
        },
        end: {
          line: 514,
          column: 52
        }
      },
      "163": {
        start: {
          line: 515,
          column: 4
        },
        end: {
          line: 515,
          column: 69
        }
      },
      "164": {
        start: {
          line: 520,
          column: 4
        },
        end: {
          line: 522,
          column: 5
        }
      },
      "165": {
        start: {
          line: 521,
          column: 6
        },
        end: {
          line: 521,
          column: 107
        }
      },
      "166": {
        start: {
          line: 524,
          column: 4
        },
        end: {
          line: 526,
          column: 5
        }
      },
      "167": {
        start: {
          line: 525,
          column: 6
        },
        end: {
          line: 525,
          column: 114
        }
      },
      "168": {
        start: {
          line: 528,
          column: 4
        },
        end: {
          line: 530,
          column: 5
        }
      },
      "169": {
        start: {
          line: 529,
          column: 6
        },
        end: {
          line: 529,
          column: 98
        }
      },
      "170": {
        start: {
          line: 534,
          column: 4
        },
        end: {
          line: 587,
          column: 5
        }
      },
      "171": {
        start: {
          line: 536,
          column: 24
        },
        end: {
          line: 554,
          column: 7
        }
      },
      "172": {
        start: {
          line: 557,
          column: 21
        },
        end: {
          line: 557,
          column: 22
        }
      },
      "173": {
        start: {
          line: 558,
          column: 26
        },
        end: {
          line: 558,
          column: 27
        }
      },
      "174": {
        start: {
          line: 560,
          column: 6
        },
        end: {
          line: 581,
          column: 7
        }
      },
      "175": {
        start: {
          line: 561,
          column: 8
        },
        end: {
          line: 580,
          column: 9
        }
      },
      "176": {
        start: {
          line: 562,
          column: 25
        },
        end: {
          line: 562,
          column: 69
        }
      },
      "177": {
        start: {
          line: 564,
          column: 10
        },
        end: {
          line: 571,
          column: 11
        }
      },
      "178": {
        start: {
          line: 565,
          column: 12
        },
        end: {
          line: 567,
          column: 13
        }
      },
      "179": {
        start: {
          line: 566,
          column: 14
        },
        end: {
          line: 566,
          column: 61
        }
      },
      "180": {
        start: {
          line: 568,
          column: 12
        },
        end: {
          line: 568,
          column: 23
        }
      },
      "181": {
        start: {
          line: 569,
          column: 12
        },
        end: {
          line: 569,
          column: 79
        }
      },
      "182": {
        start: {
          line: 569,
          column: 41
        },
        end: {
          line: 569,
          column: 77
        }
      },
      "183": {
        start: {
          line: 570,
          column: 12
        },
        end: {
          line: 570,
          column: 21
        }
      },
      "184": {
        start: {
          line: 573,
          column: 10
        },
        end: {
          line: 573,
          column: 88
        }
      },
      "185": {
        start: {
          line: 575,
          column: 10
        },
        end: {
          line: 575,
          column: 21
        }
      },
      "186": {
        start: {
          line: 576,
          column: 10
        },
        end: {
          line: 578,
          column: 11
        }
      },
      "187": {
        start: {
          line: 577,
          column: 12
        },
        end: {
          line: 577,
          column: 24
        }
      },
      "188": {
        start: {
          line: 579,
          column: 10
        },
        end: {
          line: 579,
          column: 77
        }
      },
      "189": {
        start: {
          line: 579,
          column: 39
        },
        end: {
          line: 579,
          column: 75
        }
      },
      "190": {
        start: {
          line: 583,
          column: 6
        },
        end: {
          line: 583,
          column: 81
        }
      },
      "191": {
        start: {
          line: 585,
          column: 6
        },
        end: {
          line: 585,
          column: 62
        }
      },
      "192": {
        start: {
          line: 586,
          column: 6
        },
        end: {
          line: 586,
          column: 69
        }
      },
      "193": {
        start: {
          line: 591,
          column: 4
        },
        end: {
          line: 625,
          column: 5
        }
      },
      "194": {
        start: {
          line: 592,
          column: 6
        },
        end: {
          line: 594,
          column: 7
        }
      },
      "195": {
        start: {
          line: 593,
          column: 8
        },
        end: {
          line: 593,
          column: 76
        }
      },
      "196": {
        start: {
          line: 596,
          column: 25
        },
        end: {
          line: 601,
          column: 7
        }
      },
      "197": {
        start: {
          line: 604,
          column: 6
        },
        end: {
          line: 613,
          column: 7
        }
      },
      "198": {
        start: {
          line: 605,
          column: 8
        },
        end: {
          line: 605,
          column: 92
        }
      },
      "199": {
        start: {
          line: 606,
          column: 8
        },
        end: {
          line: 608,
          column: 10
        }
      },
      "200": {
        start: {
          line: 609,
          column: 8
        },
        end: {
          line: 609,
          column: 76
        }
      },
      "201": {
        start: {
          line: 610,
          column: 8
        },
        end: {
          line: 610,
          column: 90
        }
      },
      "202": {
        start: {
          line: 611,
          column: 8
        },
        end: {
          line: 611,
          column: 50
        }
      },
      "203": {
        start: {
          line: 612,
          column: 8
        },
        end: {
          line: 612,
          column: 52
        }
      },
      "204": {
        start: {
          line: 615,
          column: 21
        },
        end: {
          line: 615,
          column: 76
        }
      },
      "205": {
        start: {
          line: 617,
          column: 6
        },
        end: {
          line: 619,
          column: 7
        }
      },
      "206": {
        start: {
          line: 618,
          column: 8
        },
        end: {
          line: 618,
          column: 55
        }
      },
      "207": {
        start: {
          line: 621,
          column: 6
        },
        end: {
          line: 621,
          column: 31
        }
      },
      "208": {
        start: {
          line: 623,
          column: 6
        },
        end: {
          line: 623,
          column: 64
        }
      },
      "209": {
        start: {
          line: 624,
          column: 6
        },
        end: {
          line: 624,
          column: 69
        }
      },
      "210": {
        start: {
          line: 632,
          column: 4
        },
        end: {
          line: 634,
          column: 5
        }
      },
      "211": {
        start: {
          line: 633,
          column: 6
        },
        end: {
          line: 633,
          column: 19
        }
      },
      "212": {
        start: {
          line: 636,
          column: 4
        },
        end: {
          line: 638,
          column: 18
        }
      },
      "213": {
        start: {
          line: 637,
          column: 18
        },
        end: {
          line: 637,
          column: 57
        }
      },
      "214": {
        start: {
          line: 645,
          column: 4
        },
        end: {
          line: 649,
          column: 5
        }
      },
      "215": {
        start: {
          line: 646,
          column: 6
        },
        end: {
          line: 646,
          column: 19
        }
      },
      "216": {
        start: {
          line: 647,
          column: 11
        },
        end: {
          line: 649,
          column: 5
        }
      },
      "217": {
        start: {
          line: 648,
          column: 6
        },
        end: {
          line: 648,
          column: 20
        }
      },
      "218": {
        start: {
          line: 650,
          column: 4
        },
        end: {
          line: 650,
          column: 18
        }
      },
      "219": {
        start: {
          line: 654,
          column: 4
        },
        end: {
          line: 654,
          column: 78
        }
      },
      "220": {
        start: {
          line: 658,
          column: 4
        },
        end: {
          line: 658,
          column: 76
        }
      },
      "221": {
        start: {
          line: 662,
          column: 4
        },
        end: {
          line: 662,
          column: 77
        }
      },
      "222": {
        start: {
          line: 662,
          column: 46
        },
        end: {
          line: 662,
          column: 75
        }
      },
      "223": {
        start: {
          line: 666,
          column: 4
        },
        end: {
          line: 668,
          column: 5
        }
      },
      "224": {
        start: {
          line: 667,
          column: 6
        },
        end: {
          line: 667,
          column: 90
        }
      },
      "225": {
        start: {
          line: 667,
          column: 46
        },
        end: {
          line: 667,
          column: 88
        }
      },
      "226": {
        start: {
          line: 673,
          column: 37
        },
        end: {
          line: 673,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 37,
            column: 2
          },
          end: {
            line: 37,
            column: 3
          }
        },
        loc: {
          start: {
            line: 40,
            column: 27
          },
          end: {
            line: 116,
            column: 3
          }
        },
        line: 40
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 121,
            column: 2
          },
          end: {
            line: 121,
            column: 3
          }
        },
        loc: {
          start: {
            line: 126,
            column: 19
          },
          end: {
            line: 194,
            column: 3
          }
        },
        line: 126
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 199,
            column: 2
          },
          end: {
            line: 199,
            column: 3
          }
        },
        loc: {
          start: {
            line: 199,
            column: 36
          },
          end: {
            line: 220,
            column: 3
          }
        },
        line: 199
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 225,
            column: 2
          },
          end: {
            line: 225,
            column: 3
          }
        },
        loc: {
          start: {
            line: 225,
            column: 37
          },
          end: {
            line: 248,
            column: 3
          }
        },
        line: 225
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 253,
            column: 2
          },
          end: {
            line: 253,
            column: 3
          }
        },
        loc: {
          start: {
            line: 253,
            column: 44
          },
          end: {
            line: 317,
            column: 3
          }
        },
        line: 253
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 322,
            column: 2
          },
          end: {
            line: 322,
            column: 3
          }
        },
        loc: {
          start: {
            line: 322,
            column: 37
          },
          end: {
            line: 344,
            column: 3
          }
        },
        line: 322
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 349,
            column: 2
          },
          end: {
            line: 349,
            column: 3
          }
        },
        loc: {
          start: {
            line: 349,
            column: 43
          },
          end: {
            line: 351,
            column: 3
          }
        },
        line: 349
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 356,
            column: 2
          },
          end: {
            line: 356,
            column: 3
          }
        },
        loc: {
          start: {
            line: 356,
            column: 77
          },
          end: {
            line: 358,
            column: 3
          }
        },
        line: 356
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 363,
            column: 2
          },
          end: {
            line: 363,
            column: 3
          }
        },
        loc: {
          start: {
            line: 363,
            column: 80
          },
          end: {
            line: 365,
            column: 3
          }
        },
        line: 363
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 364,
            column: 57
          },
          end: {
            line: 364,
            column: 58
          }
        },
        loc: {
          start: {
            line: 364,
            column: 62
          },
          end: {
            line: 364,
            column: 76
          }
        },
        line: 364
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 370,
            column: 2
          },
          end: {
            line: 370,
            column: 3
          }
        },
        loc: {
          start: {
            line: 370,
            column: 64
          },
          end: {
            line: 372,
            column: 3
          }
        },
        line: 370
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 377,
            column: 2
          },
          end: {
            line: 377,
            column: 3
          }
        },
        loc: {
          start: {
            line: 377,
            column: 67
          },
          end: {
            line: 379,
            column: 3
          }
        },
        line: 377
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 378,
            column: 53
          },
          end: {
            line: 378,
            column: 54
          }
        },
        loc: {
          start: {
            line: 378,
            column: 58
          },
          end: {
            line: 378,
            column: 72
          }
        },
        line: 378
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 383,
            column: 2
          },
          end: {
            line: 383,
            column: 3
          }
        },
        loc: {
          start: {
            line: 383,
            column: 63
          },
          end: {
            line: 399,
            column: 3
          }
        },
        line: 383
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 401,
            column: 2
          },
          end: {
            line: 401,
            column: 3
          }
        },
        loc: {
          start: {
            line: 401,
            column: 54
          },
          end: {
            line: 410,
            column: 3
          }
        },
        line: 401
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 412,
            column: 2
          },
          end: {
            line: 412,
            column: 3
          }
        },
        loc: {
          start: {
            line: 412,
            column: 64
          },
          end: {
            line: 438,
            column: 3
          }
        },
        line: 412
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 440,
            column: 2
          },
          end: {
            line: 440,
            column: 3
          }
        },
        loc: {
          start: {
            line: 446,
            column: 16
          },
          end: {
            line: 473,
            column: 3
          }
        },
        line: 446
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 475,
            column: 2
          },
          end: {
            line: 475,
            column: 3
          }
        },
        loc: {
          start: {
            line: 475,
            column: 80
          },
          end: {
            line: 499,
            column: 3
          }
        },
        line: 475
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 501,
            column: 2
          },
          end: {
            line: 501,
            column: 3
          }
        },
        loc: {
          start: {
            line: 501,
            column: 48
          },
          end: {
            line: 506,
            column: 3
          }
        },
        line: 501
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 508,
            column: 2
          },
          end: {
            line: 508,
            column: 3
          }
        },
        loc: {
          start: {
            line: 508,
            column: 69
          },
          end: {
            line: 511,
            column: 3
          }
        },
        line: 508
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 513,
            column: 2
          },
          end: {
            line: 513,
            column: 3
          }
        },
        loc: {
          start: {
            line: 513,
            column: 70
          },
          end: {
            line: 516,
            column: 3
          }
        },
        line: 513
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 518,
            column: 2
          },
          end: {
            line: 518,
            column: 3
          }
        },
        loc: {
          start: {
            line: 518,
            column: 89
          },
          end: {
            line: 531,
            column: 3
          }
        },
        line: 518
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 533,
            column: 2
          },
          end: {
            line: 533,
            column: 3
          }
        },
        loc: {
          start: {
            line: 533,
            column: 118
          },
          end: {
            line: 588,
            column: 3
          }
        },
        line: 533
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 569,
            column: 30
          },
          end: {
            line: 569,
            column: 31
          }
        },
        loc: {
          start: {
            line: 569,
            column: 41
          },
          end: {
            line: 569,
            column: 77
          }
        },
        line: 569
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 579,
            column: 28
          },
          end: {
            line: 579,
            column: 29
          }
        },
        loc: {
          start: {
            line: 579,
            column: 39
          },
          end: {
            line: 579,
            column: 75
          }
        },
        line: 579
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 590,
            column: 2
          },
          end: {
            line: 590,
            column: 3
          }
        },
        loc: {
          start: {
            line: 590,
            column: 108
          },
          end: {
            line: 626,
            column: 3
          }
        },
        line: 590
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 631,
            column: 2
          },
          end: {
            line: 631,
            column: 3
          }
        },
        loc: {
          start: {
            line: 631,
            column: 62
          },
          end: {
            line: 639,
            column: 3
          }
        },
        line: 631
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 637,
            column: 11
          },
          end: {
            line: 637,
            column: 12
          }
        },
        loc: {
          start: {
            line: 637,
            column: 18
          },
          end: {
            line: 637,
            column: 57
          }
        },
        line: 637
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 644,
            column: 2
          },
          end: {
            line: 644,
            column: 3
          }
        },
        loc: {
          start: {
            line: 644,
            column: 91
          },
          end: {
            line: 651,
            column: 3
          }
        },
        line: 644
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 653,
            column: 2
          },
          end: {
            line: 653,
            column: 3
          }
        },
        loc: {
          start: {
            line: 653,
            column: 38
          },
          end: {
            line: 655,
            column: 3
          }
        },
        line: 653
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 657,
            column: 2
          },
          end: {
            line: 657,
            column: 3
          }
        },
        loc: {
          start: {
            line: 657,
            column: 36
          },
          end: {
            line: 659,
            column: 3
          }
        },
        line: 657
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 661,
            column: 2
          },
          end: {
            line: 661,
            column: 3
          }
        },
        loc: {
          start: {
            line: 661,
            column: 41
          },
          end: {
            line: 663,
            column: 3
          }
        },
        line: 661
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 662,
            column: 34
          },
          end: {
            line: 662,
            column: 35
          }
        },
        loc: {
          start: {
            line: 662,
            column: 46
          },
          end: {
            line: 662,
            column: 75
          }
        },
        line: 662
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 665,
            column: 2
          },
          end: {
            line: 665,
            column: 3
          }
        },
        loc: {
          start: {
            line: 665,
            column: 39
          },
          end: {
            line: 669,
            column: 3
          }
        },
        line: 665
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 667,
            column: 34
          },
          end: {
            line: 667,
            column: 35
          }
        },
        loc: {
          start: {
            line: 667,
            column: 46
          },
          end: {
            line: 667,
            column: 88
          }
        },
        line: 667
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 48,
            column: 6
          },
          end: {
            line: 50,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 48,
            column: 6
          },
          end: {
            line: 50,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 48
      },
      "1": {
        loc: {
          start: {
            line: 82,
            column: 6
          },
          end: {
            line: 84,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 6
          },
          end: {
            line: 84,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "2": {
        loc: {
          start: {
            line: 88,
            column: 6
          },
          end: {
            line: 90,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 88,
            column: 6
          },
          end: {
            line: 90,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 88
      },
      "3": {
        loc: {
          start: {
            line: 89,
            column: 24
          },
          end: {
            line: 89,
            column: 78
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 89,
            column: 24
          },
          end: {
            line: 89,
            column: 40
          }
        }, {
          start: {
            line: 89,
            column: 44
          },
          end: {
            line: 89,
            column: 78
          }
        }],
        line: 89
      },
      "4": {
        loc: {
          start: {
            line: 110,
            column: 6
          },
          end: {
            line: 112,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 110,
            column: 6
          },
          end: {
            line: 112,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 110
      },
      "5": {
        loc: {
          start: {
            line: 123,
            column: 4
          },
          end: {
            line: 123,
            column: 89
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 123,
            column: 81
          },
          end: {
            line: 123,
            column: 89
          }
        }],
        line: 123
      },
      "6": {
        loc: {
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 4
          },
          end: {
            line: 129,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "7": {
        loc: {
          start: {
            line: 140,
            column: 19
          },
          end: {
            line: 140,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 140,
            column: 44
          },
          end: {
            line: 140,
            column: 55
          }
        }, {
          start: {
            line: 140,
            column: 58
          },
          end: {
            line: 140,
            column: 67
          }
        }],
        line: 140
      },
      "8": {
        loc: {
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 179,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 167,
            column: 6
          },
          end: {
            line: 179,
            column: 7
          }
        }, {
          start: {
            line: 170,
            column: 13
          },
          end: {
            line: 179,
            column: 7
          }
        }],
        line: 167
      },
      "9": {
        loc: {
          start: {
            line: 167,
            column: 10
          },
          end: {
            line: 167,
            column: 39
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 167,
            column: 10
          },
          end: {
            line: 167,
            column: 21
          }
        }, {
          start: {
            line: 167,
            column: 25
          },
          end: {
            line: 167,
            column: 39
          }
        }],
        line: 167
      },
      "10": {
        loc: {
          start: {
            line: 170,
            column: 13
          },
          end: {
            line: 179,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 170,
            column: 13
          },
          end: {
            line: 179,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 170
      },
      "11": {
        loc: {
          start: {
            line: 176,
            column: 8
          },
          end: {
            line: 178,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 176,
            column: 8
          },
          end: {
            line: 178,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 176
      },
      "12": {
        loc: {
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 186,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 181,
            column: 6
          },
          end: {
            line: 186,
            column: 7
          }
        }, {
          start: {
            line: 183,
            column: 13
          },
          end: {
            line: 186,
            column: 7
          }
        }],
        line: 181
      },
      "13": {
        loc: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 202,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "14": {
        loc: {
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 200,
            column: 60
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 200,
            column: 8
          },
          end: {
            line: 200,
            column: 28
          }
        }, {
          start: {
            line: 200,
            column: 32
          },
          end: {
            line: 200,
            column: 60
          }
        }],
        line: 200
      },
      "15": {
        loc: {
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "16": {
        loc: {
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 228,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 226,
            column: 4
          },
          end: {
            line: 228,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 226
      },
      "17": {
        loc: {
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 226,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 226,
            column: 28
          }
        }, {
          start: {
            line: 226,
            column: 32
          },
          end: {
            line: 226,
            column: 61
          }
        }],
        line: 226
      },
      "18": {
        loc: {
          start: {
            line: 238,
            column: 6
          },
          end: {
            line: 240,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 238,
            column: 6
          },
          end: {
            line: 240,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 238
      },
      "19": {
        loc: {
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 254,
            column: 4
          },
          end: {
            line: 256,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 254
      },
      "20": {
        loc: {
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 299,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "21": {
        loc: {
          start: {
            line: 276,
            column: 29
          },
          end: {
            line: 276,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 276,
            column: 29
          },
          end: {
            line: 276,
            column: 45
          }
        }, {
          start: {
            line: 276,
            column: 49
          },
          end: {
            line: 276,
            column: 55
          }
        }],
        line: 276
      },
      "22": {
        loc: {
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 298,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 279,
            column: 8
          },
          end: {
            line: 298,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 279
      },
      "23": {
        loc: {
          start: {
            line: 285,
            column: 10
          },
          end: {
            line: 297,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 285,
            column: 10
          },
          end: {
            line: 297,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 285
      },
      "24": {
        loc: {
          start: {
            line: 290,
            column: 35
          },
          end: {
            line: 290,
            column: 61
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 35
          },
          end: {
            line: 290,
            column: 51
          }
        }, {
          start: {
            line: 290,
            column: 55
          },
          end: {
            line: 290,
            column: 61
          }
        }],
        line: 290
      },
      "25": {
        loc: {
          start: {
            line: 294,
            column: 12
          },
          end: {
            line: 296,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 294,
            column: 12
          },
          end: {
            line: 296,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 294
      },
      "26": {
        loc: {
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 325,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 323,
            column: 4
          },
          end: {
            line: 325,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 323
      },
      "27": {
        loc: {
          start: {
            line: 329,
            column: 6
          },
          end: {
            line: 331,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 329,
            column: 6
          },
          end: {
            line: 331,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 329
      },
      "28": {
        loc: {
          start: {
            line: 384,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 384,
            column: 4
          },
          end: {
            line: 386,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 384
      },
      "29": {
        loc: {
          start: {
            line: 387,
            column: 4
          },
          end: {
            line: 389,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 387,
            column: 4
          },
          end: {
            line: 389,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 387
      },
      "30": {
        loc: {
          start: {
            line: 390,
            column: 4
          },
          end: {
            line: 392,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 390,
            column: 4
          },
          end: {
            line: 392,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 390
      },
      "31": {
        loc: {
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 395,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 393
      },
      "32": {
        loc: {
          start: {
            line: 396,
            column: 4
          },
          end: {
            line: 398,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 396,
            column: 4
          },
          end: {
            line: 398,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 396
      },
      "33": {
        loc: {
          start: {
            line: 402,
            column: 20
          },
          end: {
            line: 402,
            column: 50
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 402,
            column: 45
          },
          end: {
            line: 402,
            column: 46
          }
        }, {
          start: {
            line: 402,
            column: 49
          },
          end: {
            line: 402,
            column: 50
          }
        }],
        line: 402
      },
      "34": {
        loc: {
          start: {
            line: 465,
            column: 4
          },
          end: {
            line: 470,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 465,
            column: 4
          },
          end: {
            line: 470,
            column: 5
          }
        }, {
          start: {
            line: 468,
            column: 11
          },
          end: {
            line: 470,
            column: 5
          }
        }],
        line: 465
      },
      "35": {
        loc: {
          start: {
            line: 478,
            column: 4
          },
          end: {
            line: 480,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 478,
            column: 4
          },
          end: {
            line: 480,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 478
      },
      "36": {
        loc: {
          start: {
            line: 482,
            column: 4
          },
          end: {
            line: 498,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 483,
            column: 6
          },
          end: {
            line: 485,
            column: 14
          }
        }, {
          start: {
            line: 486,
            column: 6
          },
          end: {
            line: 488,
            column: 14
          }
        }, {
          start: {
            line: 489,
            column: 6
          },
          end: {
            line: 491,
            column: 14
          }
        }, {
          start: {
            line: 492,
            column: 6
          },
          end: {
            line: 494,
            column: 14
          }
        }, {
          start: {
            line: 495,
            column: 6
          },
          end: {
            line: 497,
            column: 14
          }
        }],
        line: 482
      },
      "37": {
        loc: {
          start: {
            line: 503,
            column: 11
          },
          end: {
            line: 505,
            column: 25
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 503,
            column: 12
          },
          end: {
            line: 503,
            column: 30
          }
        }, {
          start: {
            line: 503,
            column: 34
          },
          end: {
            line: 503,
            column: 72
          }
        }, {
          start: {
            line: 504,
            column: 12
          },
          end: {
            line: 504,
            column: 34
          }
        }, {
          start: {
            line: 504,
            column: 38
          },
          end: {
            line: 504,
            column: 76
          }
        }, {
          start: {
            line: 505,
            column: 11
          },
          end: {
            line: 505,
            column: 25
          }
        }],
        line: 503
      },
      "38": {
        loc: {
          start: {
            line: 514,
            column: 22
          },
          end: {
            line: 514,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 514,
            column: 47
          },
          end: {
            line: 514,
            column: 48
          }
        }, {
          start: {
            line: 514,
            column: 51
          },
          end: {
            line: 514,
            column: 52
          }
        }],
        line: 514
      },
      "39": {
        loc: {
          start: {
            line: 515,
            column: 11
          },
          end: {
            line: 515,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 515,
            column: 11
          },
          end: {
            line: 515,
            column: 37
          }
        }, {
          start: {
            line: 515,
            column: 41
          },
          end: {
            line: 515,
            column: 68
          }
        }],
        line: 515
      },
      "40": {
        loc: {
          start: {
            line: 520,
            column: 4
          },
          end: {
            line: 522,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 520,
            column: 4
          },
          end: {
            line: 522,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 520
      },
      "41": {
        loc: {
          start: {
            line: 524,
            column: 4
          },
          end: {
            line: 526,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 524,
            column: 4
          },
          end: {
            line: 526,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 524
      },
      "42": {
        loc: {
          start: {
            line: 528,
            column: 4
          },
          end: {
            line: 530,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 528,
            column: 4
          },
          end: {
            line: 530,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 528
      },
      "43": {
        loc: {
          start: {
            line: 540,
            column: 20
          },
          end: {
            line: 540,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 540,
            column: 20
          },
          end: {
            line: 540,
            column: 44
          }
        }, {
          start: {
            line: 540,
            column: 48
          },
          end: {
            line: 540,
            column: 58
          }
        }],
        line: 540
      },
      "44": {
        loc: {
          start: {
            line: 564,
            column: 10
          },
          end: {
            line: 571,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 564,
            column: 10
          },
          end: {
            line: 571,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 564
      },
      "45": {
        loc: {
          start: {
            line: 565,
            column: 12
          },
          end: {
            line: 567,
            column: 13
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 565,
            column: 12
          },
          end: {
            line: 567,
            column: 13
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 565
      },
      "46": {
        loc: {
          start: {
            line: 576,
            column: 10
          },
          end: {
            line: 578,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 576,
            column: 10
          },
          end: {
            line: 578,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 576
      },
      "47": {
        loc: {
          start: {
            line: 592,
            column: 6
          },
          end: {
            line: 594,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 592,
            column: 6
          },
          end: {
            line: 594,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 592
      },
      "48": {
        loc: {
          start: {
            line: 604,
            column: 6
          },
          end: {
            line: 613,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 604,
            column: 6
          },
          end: {
            line: 613,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 604
      },
      "49": {
        loc: {
          start: {
            line: 604,
            column: 10
          },
          end: {
            line: 604,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 604,
            column: 10
          },
          end: {
            line: 604,
            column: 38
          }
        }, {
          start: {
            line: 604,
            column: 42
          },
          end: {
            line: 604,
            column: 64
          }
        }],
        line: 604
      },
      "50": {
        loc: {
          start: {
            line: 617,
            column: 6
          },
          end: {
            line: 619,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 617,
            column: 6
          },
          end: {
            line: 619,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 617
      },
      "51": {
        loc: {
          start: {
            line: 632,
            column: 4
          },
          end: {
            line: 634,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 632,
            column: 4
          },
          end: {
            line: 634,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 632
      },
      "52": {
        loc: {
          start: {
            line: 632,
            column: 8
          },
          end: {
            line: 632,
            column: 46
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 632,
            column: 8
          },
          end: {
            line: 632,
            column: 19
          }
        }, {
          start: {
            line: 632,
            column: 23
          },
          end: {
            line: 632,
            column: 46
          }
        }],
        line: 632
      },
      "53": {
        loc: {
          start: {
            line: 645,
            column: 4
          },
          end: {
            line: 649,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 645,
            column: 4
          },
          end: {
            line: 649,
            column: 5
          }
        }, {
          start: {
            line: 647,
            column: 11
          },
          end: {
            line: 649,
            column: 5
          }
        }],
        line: 645
      },
      "54": {
        loc: {
          start: {
            line: 647,
            column: 11
          },
          end: {
            line: 649,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 647,
            column: 11
          },
          end: {
            line: 649,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 647
      },
      "55": {
        loc: {
          start: {
            line: 666,
            column: 4
          },
          end: {
            line: 668,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 666,
            column: 4
          },
          end: {
            line: 668,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 666
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0,
      "153": 0,
      "154": 0,
      "155": 0,
      "156": 0,
      "157": 0,
      "158": 0,
      "159": 0,
      "160": 0,
      "161": 0,
      "162": 0,
      "163": 0,
      "164": 0,
      "165": 0,
      "166": 0,
      "167": 0,
      "168": 0,
      "169": 0,
      "170": 0,
      "171": 0,
      "172": 0,
      "173": 0,
      "174": 0,
      "175": 0,
      "176": 0,
      "177": 0,
      "178": 0,
      "179": 0,
      "180": 0,
      "181": 0,
      "182": 0,
      "183": 0,
      "184": 0,
      "185": 0,
      "186": 0,
      "187": 0,
      "188": 0,
      "189": 0,
      "190": 0,
      "191": 0,
      "192": 0,
      "193": 0,
      "194": 0,
      "195": 0,
      "196": 0,
      "197": 0,
      "198": 0,
      "199": 0,
      "200": 0,
      "201": 0,
      "202": 0,
      "203": 0,
      "204": 0,
      "205": 0,
      "206": 0,
      "207": 0,
      "208": 0,
      "209": 0,
      "210": 0,
      "211": 0,
      "212": 0,
      "213": 0,
      "214": 0,
      "215": 0,
      "216": 0,
      "217": 0,
      "218": 0,
      "219": 0,
      "220": 0,
      "221": 0,
      "222": 0,
      "223": 0,
      "224": 0,
      "225": 0,
      "226": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0, 0, 0, 0],
      "37": [0, 0, 0, 0, 0],
      "38": [0, 0],
      "39": [0, 0],
      "40": [0, 0],
      "41": [0, 0],
      "42": [0, 0],
      "43": [0, 0],
      "44": [0, 0],
      "45": [0, 0],
      "46": [0, 0],
      "47": [0, 0],
      "48": [0, 0],
      "49": [0, 0],
      "50": [0, 0],
      "51": [0, 0],
      "52": [0, 0],
      "53": [0, 0],
      "54": [0, 0],
      "55": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a74cddf9dd12a1d0ea03499a43d630188d9966fd"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2k9ta5zj0b = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2k9ta5zj0b();
var MatchRecordingService = function () {
  function MatchRecordingService() {
    (0, _classCallCheck2.default)(this, MatchRecordingService);
    this.currentSession = (cov_2k9ta5zj0b().s[0]++, null);
    this.sessionListeners = (cov_2k9ta5zj0b().s[1]++, []);
    this.scoreListeners = (cov_2k9ta5zj0b().s[2]++, []);
  }
  return (0, _createClass2.default)(MatchRecordingService, [{
    key: "startMatch",
    value: (function () {
      var _startMatch = (0, _asyncToGenerator2.default)(function* (metadata, options) {
        cov_2k9ta5zj0b().f[0]++;
        cov_2k9ta5zj0b().s[3]++;
        try {
          cov_2k9ta5zj0b().s[4]++;
          _performance.performanceMonitor.start('match_recording_start');
          cov_2k9ta5zj0b().s[5]++;
          this.validateMatchMetadata(metadata);
          cov_2k9ta5zj0b().s[6]++;
          if (this.currentSession) {
            cov_2k9ta5zj0b().b[0][0]++;
            cov_2k9ta5zj0b().s[7]++;
            throw new Error('Another match recording is already in progress');
          } else {
            cov_2k9ta5zj0b().b[0][1]++;
          }
          var matchRecording = (cov_2k9ta5zj0b().s[8]++, {
            id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            metadata: Object.assign({}, metadata, {
              startTime: new Date().toISOString()
            }),
            score: this.initializeScore(metadata.matchFormat),
            statistics: this.initializeStatistics(metadata.userId),
            status: 'recording',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          });
          var session = (cov_2k9ta5zj0b().s[9]++, {
            id: this.generateSessionId(),
            match: matchRecording,
            currentSet: 1,
            currentGame: 1,
            isRecording: true,
            isPaused: false,
            startTime: Date.now(),
            pausedTime: 0,
            totalPausedDuration: 0,
            videoRecordingActive: options.enableVideoRecording,
            autoScoreDetection: options.enableAutoScoreDetection
          });
          cov_2k9ta5zj0b().s[10]++;
          if (options.enableVideoRecording) {
            cov_2k9ta5zj0b().b[1][0]++;
            cov_2k9ta5zj0b().s[11]++;
            yield _VideoRecordingService.videoRecordingService.startRecording(options.videoConfig);
          } else {
            cov_2k9ta5zj0b().b[1][1]++;
          }
          var savedMatch = (cov_2k9ta5zj0b().s[12]++, yield this.saveMatchToDatabase(matchRecording));
          cov_2k9ta5zj0b().s[13]++;
          if (!savedMatch.success) {
            cov_2k9ta5zj0b().b[2][0]++;
            cov_2k9ta5zj0b().s[14]++;
            throw new Error((cov_2k9ta5zj0b().b[3][0]++, savedMatch.error) || (cov_2k9ta5zj0b().b[3][1]++, 'Failed to save match to database'));
          } else {
            cov_2k9ta5zj0b().b[2][1]++;
          }
          cov_2k9ta5zj0b().s[15]++;
          session.match.id = savedMatch.data.id;
          cov_2k9ta5zj0b().s[16]++;
          session.match.databaseId = savedMatch.data.databaseId;
          cov_2k9ta5zj0b().s[17]++;
          this.setupOfflineSync(session.match.id);
          cov_2k9ta5zj0b().s[18]++;
          this.currentSession = session;
          cov_2k9ta5zj0b().s[19]++;
          this.notifySessionListeners();
          cov_2k9ta5zj0b().s[20]++;
          this.startAutoSave();
          cov_2k9ta5zj0b().s[21]++;
          _performance.performanceMonitor.end('match_recording_start');
          cov_2k9ta5zj0b().s[22]++;
          return session;
        } catch (error) {
          cov_2k9ta5zj0b().s[23]++;
          console.error('Failed to start match recording:', error);
          cov_2k9ta5zj0b().s[24]++;
          if (this.currentSession) {
            cov_2k9ta5zj0b().b[4][0]++;
            cov_2k9ta5zj0b().s[25]++;
            yield this.cleanupFailedSession();
          } else {
            cov_2k9ta5zj0b().b[4][1]++;
          }
          cov_2k9ta5zj0b().s[26]++;
          throw error;
        }
      });
      function startMatch(_x, _x2) {
        return _startMatch.apply(this, arguments);
      }
      return startMatch;
    }())
  }, {
    key: "addPoint",
    value: (function () {
      var _addPoint = (0, _asyncToGenerator2.default)(function* (winner) {
        var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_2k9ta5zj0b().b[5][0]++, 'normal');
        var shotType = arguments.length > 2 ? arguments[2] : undefined;
        var courtPosition = arguments.length > 3 ? arguments[3] : undefined;
        cov_2k9ta5zj0b().f[1]++;
        cov_2k9ta5zj0b().s[27]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[6][0]++;
          cov_2k9ta5zj0b().s[28]++;
          throw new Error('No active match session');
        } else {
          cov_2k9ta5zj0b().b[6][1]++;
        }
        cov_2k9ta5zj0b().s[29]++;
        try {
          var session = (cov_2k9ta5zj0b().s[30]++, this.currentSession);
          var currentSet = (cov_2k9ta5zj0b().s[31]++, session.currentSet);
          var currentGame = (cov_2k9ta5zj0b().s[32]++, session.currentGame);
          var gameEvent = (cov_2k9ta5zj0b().s[33]++, {
            id: this.generateEventId(),
            timestamp: Date.now(),
            eventType: eventType === 'normal' ? (cov_2k9ta5zj0b().b[7][0]++, 'point_won') : (cov_2k9ta5zj0b().b[7][1]++, eventType),
            player: winner,
            shotType: shotType,
            courtPosition: courtPosition,
            description: `Point won by ${winner}`
          });
          var updatedScore = (cov_2k9ta5zj0b().s[34]++, this.updateScore(session.match.score, currentSet, currentGame, winner, gameEvent));
          cov_2k9ta5zj0b().s[35]++;
          this.updateStatistics(session.match.statistics, gameEvent);
          cov_2k9ta5zj0b().s[36]++;
          session.match.score = updatedScore;
          cov_2k9ta5zj0b().s[37]++;
          session.match.updatedAt = new Date().toISOString();
          var setComplete = (cov_2k9ta5zj0b().s[38]++, this.isSetComplete(updatedScore.sets[currentSet - 1]));
          var matchComplete = (cov_2k9ta5zj0b().s[39]++, this.isMatchComplete(updatedScore, session.match.metadata.matchFormat));
          cov_2k9ta5zj0b().s[40]++;
          if ((cov_2k9ta5zj0b().b[9][0]++, setComplete) && (cov_2k9ta5zj0b().b[9][1]++, !matchComplete)) {
            cov_2k9ta5zj0b().b[8][0]++;
            cov_2k9ta5zj0b().s[41]++;
            session.currentSet++;
            cov_2k9ta5zj0b().s[42]++;
            session.currentGame = 1;
          } else {
            cov_2k9ta5zj0b().b[8][1]++;
            cov_2k9ta5zj0b().s[43]++;
            if (!setComplete) {
              cov_2k9ta5zj0b().b[10][0]++;
              var gameComplete = (cov_2k9ta5zj0b().s[44]++, this.isGameComplete(updatedScore.sets[currentSet - 1], currentGame));
              cov_2k9ta5zj0b().s[45]++;
              if (gameComplete) {
                cov_2k9ta5zj0b().b[11][0]++;
                cov_2k9ta5zj0b().s[46]++;
                session.currentGame++;
              } else {
                cov_2k9ta5zj0b().b[11][1]++;
              }
            } else {
              cov_2k9ta5zj0b().b[10][1]++;
            }
          }
          cov_2k9ta5zj0b().s[47]++;
          if (matchComplete) {
            cov_2k9ta5zj0b().b[12][0]++;
            cov_2k9ta5zj0b().s[48]++;
            yield this.endMatch();
          } else {
            cov_2k9ta5zj0b().b[12][1]++;
            cov_2k9ta5zj0b().s[49]++;
            yield this.updateMatchInDatabase(session.match);
          }
          cov_2k9ta5zj0b().s[50]++;
          this.notifyScoreListeners();
          cov_2k9ta5zj0b().s[51]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[52]++;
          console.error('Failed to add point:', error);
          cov_2k9ta5zj0b().s[53]++;
          throw error;
        }
      });
      function addPoint(_x3) {
        return _addPoint.apply(this, arguments);
      }
      return addPoint;
    }())
  }, {
    key: "pauseMatch",
    value: (function () {
      var _pauseMatch = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[2]++;
        cov_2k9ta5zj0b().s[54]++;
        if ((cov_2k9ta5zj0b().b[14][0]++, !this.currentSession) || (cov_2k9ta5zj0b().b[14][1]++, this.currentSession.isPaused)) {
          cov_2k9ta5zj0b().b[13][0]++;
          cov_2k9ta5zj0b().s[55]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[13][1]++;
        }
        cov_2k9ta5zj0b().s[56]++;
        try {
          cov_2k9ta5zj0b().s[57]++;
          this.currentSession.isPaused = true;
          cov_2k9ta5zj0b().s[58]++;
          this.currentSession.pausedTime = Date.now();
          cov_2k9ta5zj0b().s[59]++;
          this.currentSession.match.status = 'paused';
          cov_2k9ta5zj0b().s[60]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[15][0]++;
            cov_2k9ta5zj0b().s[61]++;
            yield _VideoRecordingService.videoRecordingService.pauseRecording();
          } else {
            cov_2k9ta5zj0b().b[15][1]++;
          }
          cov_2k9ta5zj0b().s[62]++;
          yield this.updateMatchInDatabase(this.currentSession.match);
          cov_2k9ta5zj0b().s[63]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[64]++;
          console.error('Failed to pause match:', error);
          cov_2k9ta5zj0b().s[65]++;
          throw error;
        }
      });
      function pauseMatch() {
        return _pauseMatch.apply(this, arguments);
      }
      return pauseMatch;
    }())
  }, {
    key: "resumeMatch",
    value: (function () {
      var _resumeMatch = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[3]++;
        cov_2k9ta5zj0b().s[66]++;
        if ((cov_2k9ta5zj0b().b[17][0]++, !this.currentSession) || (cov_2k9ta5zj0b().b[17][1]++, !this.currentSession.isPaused)) {
          cov_2k9ta5zj0b().b[16][0]++;
          cov_2k9ta5zj0b().s[67]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[16][1]++;
        }
        cov_2k9ta5zj0b().s[68]++;
        try {
          var pauseDuration = (cov_2k9ta5zj0b().s[69]++, Date.now() - this.currentSession.pausedTime);
          cov_2k9ta5zj0b().s[70]++;
          this.currentSession.totalPausedDuration += pauseDuration;
          cov_2k9ta5zj0b().s[71]++;
          this.currentSession.isPaused = false;
          cov_2k9ta5zj0b().s[72]++;
          this.currentSession.pausedTime = 0;
          cov_2k9ta5zj0b().s[73]++;
          this.currentSession.match.status = 'recording';
          cov_2k9ta5zj0b().s[74]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[18][0]++;
            cov_2k9ta5zj0b().s[75]++;
            yield _VideoRecordingService.videoRecordingService.resumeRecording();
          } else {
            cov_2k9ta5zj0b().b[18][1]++;
          }
          cov_2k9ta5zj0b().s[76]++;
          yield this.updateMatchInDatabase(this.currentSession.match);
          cov_2k9ta5zj0b().s[77]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[78]++;
          console.error('Failed to resume match:', error);
          cov_2k9ta5zj0b().s[79]++;
          throw error;
        }
      });
      function resumeMatch() {
        return _resumeMatch.apply(this, arguments);
      }
      return resumeMatch;
    }())
  }, {
    key: "endMatch",
    value: (function () {
      var _endMatch = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[4]++;
        cov_2k9ta5zj0b().s[80]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[19][0]++;
          cov_2k9ta5zj0b().s[81]++;
          throw new Error('No active match session');
        } else {
          cov_2k9ta5zj0b().b[19][1]++;
        }
        cov_2k9ta5zj0b().s[82]++;
        try {
          cov_2k9ta5zj0b().s[83]++;
          _performance.performanceMonitor.start('match_recording_end');
          var session = (cov_2k9ta5zj0b().s[84]++, this.currentSession);
          var endTime = (cov_2k9ta5zj0b().s[85]++, Date.now());
          var totalDuration = (cov_2k9ta5zj0b().s[86]++, (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60);
          cov_2k9ta5zj0b().s[87]++;
          session.match.metadata.endTime = new Date().toISOString();
          cov_2k9ta5zj0b().s[88]++;
          session.match.metadata.durationMinutes = Math.round(totalDuration);
          cov_2k9ta5zj0b().s[89]++;
          session.match.status = 'completed';
          cov_2k9ta5zj0b().s[90]++;
          if (session.videoRecordingActive) {
            cov_2k9ta5zj0b().b[20][0]++;
            var videoResult = (cov_2k9ta5zj0b().s[91]++, yield _VideoRecordingService.videoRecordingService.stopRecording());
            var uploadResult = (cov_2k9ta5zj0b().s[92]++, yield _FileUploadService.fileUploadService.uploadVideo(videoResult.uri, {
              folder: `matches/${(cov_2k9ta5zj0b().b[21][0]++, session.match.id) || (cov_2k9ta5zj0b().b[21][1]++, 'temp')}`
            }));
            cov_2k9ta5zj0b().s[93]++;
            if (uploadResult.data) {
              cov_2k9ta5zj0b().b[22][0]++;
              cov_2k9ta5zj0b().s[94]++;
              session.match.videoUrl = uploadResult.data.url;
              cov_2k9ta5zj0b().s[95]++;
              session.match.videoDurationSeconds = videoResult.duration;
              cov_2k9ta5zj0b().s[96]++;
              session.match.videoFileSizeBytes = uploadResult.data.size;
              cov_2k9ta5zj0b().s[97]++;
              if (videoResult.thumbnail) {
                cov_2k9ta5zj0b().b[23][0]++;
                var thumbnailResult = (cov_2k9ta5zj0b().s[98]++, yield _FileUploadService.fileUploadService.uploadThumbnail(videoResult.uri, videoResult.thumbnail, {
                  folder: `matches/${(cov_2k9ta5zj0b().b[24][0]++, session.match.id) || (cov_2k9ta5zj0b().b[24][1]++, 'temp')}/thumbnails`
                }));
                cov_2k9ta5zj0b().s[99]++;
                if (thumbnailResult.data) {
                  cov_2k9ta5zj0b().b[25][0]++;
                  cov_2k9ta5zj0b().s[100]++;
                  session.match.videoThumbnailUrl = thumbnailResult.data.url;
                } else {
                  cov_2k9ta5zj0b().b[25][1]++;
                }
              } else {
                cov_2k9ta5zj0b().b[23][1]++;
              }
            } else {
              cov_2k9ta5zj0b().b[22][1]++;
            }
          } else {
            cov_2k9ta5zj0b().b[20][1]++;
          }
          cov_2k9ta5zj0b().s[101]++;
          this.calculateFinalStatistics(session.match.statistics, session.match.score);
          var finalMatch = (cov_2k9ta5zj0b().s[102]++, yield this.updateMatchInDatabase(session.match));
          cov_2k9ta5zj0b().s[103]++;
          this.currentSession = null;
          cov_2k9ta5zj0b().s[104]++;
          this.notifySessionListeners();
          cov_2k9ta5zj0b().s[105]++;
          _performance.performanceMonitor.end('match_recording_end');
          cov_2k9ta5zj0b().s[106]++;
          return finalMatch;
        } catch (error) {
          cov_2k9ta5zj0b().s[107]++;
          console.error('Failed to end match:', error);
          cov_2k9ta5zj0b().s[108]++;
          throw error;
        }
      });
      function endMatch() {
        return _endMatch.apply(this, arguments);
      }
      return endMatch;
    }())
  }, {
    key: "cancelMatch",
    value: (function () {
      var _cancelMatch = (0, _asyncToGenerator2.default)(function* () {
        cov_2k9ta5zj0b().f[5]++;
        cov_2k9ta5zj0b().s[109]++;
        if (!this.currentSession) {
          cov_2k9ta5zj0b().b[26][0]++;
          cov_2k9ta5zj0b().s[110]++;
          return;
        } else {
          cov_2k9ta5zj0b().b[26][1]++;
        }
        cov_2k9ta5zj0b().s[111]++;
        try {
          cov_2k9ta5zj0b().s[112]++;
          if (this.currentSession.videoRecordingActive) {
            cov_2k9ta5zj0b().b[27][0]++;
            cov_2k9ta5zj0b().s[113]++;
            yield _VideoRecordingService.videoRecordingService.stopRecording();
          } else {
            cov_2k9ta5zj0b().b[27][1]++;
          }
          cov_2k9ta5zj0b().s[114]++;
          this.currentSession.match.status = 'cancelled';
          cov_2k9ta5zj0b().s[115]++;
          yield this.updateMatchInDatabase(this.currentSession.match);
          cov_2k9ta5zj0b().s[116]++;
          this.currentSession = null;
          cov_2k9ta5zj0b().s[117]++;
          this.notifySessionListeners();
        } catch (error) {
          cov_2k9ta5zj0b().s[118]++;
          console.error('Failed to cancel match:', error);
          cov_2k9ta5zj0b().s[119]++;
          throw error;
        }
      });
      function cancelMatch() {
        return _cancelMatch.apply(this, arguments);
      }
      return cancelMatch;
    }())
  }, {
    key: "getCurrentSession",
    value: function getCurrentSession() {
      cov_2k9ta5zj0b().f[6]++;
      cov_2k9ta5zj0b().s[120]++;
      return this.currentSession;
    }
  }, {
    key: "addSessionListener",
    value: function addSessionListener(listener) {
      cov_2k9ta5zj0b().f[7]++;
      cov_2k9ta5zj0b().s[121]++;
      this.sessionListeners.push(listener);
    }
  }, {
    key: "removeSessionListener",
    value: function removeSessionListener(listener) {
      cov_2k9ta5zj0b().f[8]++;
      cov_2k9ta5zj0b().s[122]++;
      this.sessionListeners = this.sessionListeners.filter(function (l) {
        cov_2k9ta5zj0b().f[9]++;
        cov_2k9ta5zj0b().s[123]++;
        return l !== listener;
      });
    }
  }, {
    key: "addScoreListener",
    value: function addScoreListener(listener) {
      cov_2k9ta5zj0b().f[10]++;
      cov_2k9ta5zj0b().s[124]++;
      this.scoreListeners.push(listener);
    }
  }, {
    key: "removeScoreListener",
    value: function removeScoreListener(listener) {
      cov_2k9ta5zj0b().f[11]++;
      cov_2k9ta5zj0b().s[125]++;
      this.scoreListeners = this.scoreListeners.filter(function (l) {
        cov_2k9ta5zj0b().f[12]++;
        cov_2k9ta5zj0b().s[126]++;
        return l !== listener;
      });
    }
  }, {
    key: "validateMatchMetadata",
    value: function validateMatchMetadata(metadata) {
      var _metadata$opponentNam;
      cov_2k9ta5zj0b().f[13]++;
      cov_2k9ta5zj0b().s[127]++;
      if (!((_metadata$opponentNam = metadata.opponentName) != null && _metadata$opponentNam.trim())) {
        cov_2k9ta5zj0b().b[28][0]++;
        cov_2k9ta5zj0b().s[128]++;
        throw new Error('Opponent name is required');
      } else {
        cov_2k9ta5zj0b().b[28][1]++;
      }
      cov_2k9ta5zj0b().s[129]++;
      if (!metadata.userId) {
        cov_2k9ta5zj0b().b[29][0]++;
        cov_2k9ta5zj0b().s[130]++;
        throw new Error('User ID is required');
      } else {
        cov_2k9ta5zj0b().b[29][1]++;
      }
      cov_2k9ta5zj0b().s[131]++;
      if (!metadata.matchType) {
        cov_2k9ta5zj0b().b[30][0]++;
        cov_2k9ta5zj0b().s[132]++;
        throw new Error('Match type is required');
      } else {
        cov_2k9ta5zj0b().b[30][1]++;
      }
      cov_2k9ta5zj0b().s[133]++;
      if (!metadata.matchFormat) {
        cov_2k9ta5zj0b().b[31][0]++;
        cov_2k9ta5zj0b().s[134]++;
        throw new Error('Match format is required');
      } else {
        cov_2k9ta5zj0b().b[31][1]++;
      }
      cov_2k9ta5zj0b().s[135]++;
      if (!metadata.surface) {
        cov_2k9ta5zj0b().b[32][0]++;
        cov_2k9ta5zj0b().s[136]++;
        throw new Error('Court surface is required');
      } else {
        cov_2k9ta5zj0b().b[32][1]++;
      }
    }
  }, {
    key: "initializeScore",
    value: function initializeScore(format) {
      cov_2k9ta5zj0b().f[14]++;
      var maxSets = (cov_2k9ta5zj0b().s[137]++, format === 'best_of_5' ? (cov_2k9ta5zj0b().b[33][0]++, 5) : (cov_2k9ta5zj0b().b[33][1]++, 3));
      cov_2k9ta5zj0b().s[138]++;
      return {
        sets: [],
        finalScore: '',
        result: 'win',
        setsWon: 0,
        setsLost: 0
      };
    }
  }, {
    key: "initializeStatistics",
    value: function initializeStatistics(userId) {
      cov_2k9ta5zj0b().f[15]++;
      cov_2k9ta5zj0b().s[139]++;
      return {
        matchId: '',
        userId: userId,
        aces: 0,
        doubleFaults: 0,
        firstServesIn: 0,
        firstServesAttempted: 0,
        firstServePointsWon: 0,
        secondServePointsWon: 0,
        firstServeReturnPointsWon: 0,
        secondServeReturnPointsWon: 0,
        breakPointsConverted: 0,
        breakPointsFaced: 0,
        winners: 0,
        unforcedErrors: 0,
        forcedErrors: 0,
        totalPointsWon: 0,
        totalPointsPlayed: 0,
        netPointsAttempted: 0,
        netPointsWon: 0,
        forehandWinners: 0,
        backhandWinners: 0,
        forehandErrors: 0,
        backhandErrors: 0
      };
    }
  }, {
    key: "updateScore",
    value: function updateScore(currentScore, setNumber, gameNumber, winner, event) {
      cov_2k9ta5zj0b().f[16]++;
      var updatedScore = (cov_2k9ta5zj0b().s[140]++, Object.assign({}, currentScore));
      cov_2k9ta5zj0b().s[141]++;
      while (updatedScore.sets.length < setNumber) {
        cov_2k9ta5zj0b().s[142]++;
        updatedScore.sets.push({
          setNumber: updatedScore.sets.length + 1,
          userGames: 0,
          opponentGames: 0,
          isTiebreak: false,
          isCompleted: false
        });
      }
      var currentSet = (cov_2k9ta5zj0b().s[143]++, updatedScore.sets[setNumber - 1]);
      cov_2k9ta5zj0b().s[144]++;
      if (winner === 'user') {
        cov_2k9ta5zj0b().b[34][0]++;
      } else {
        cov_2k9ta5zj0b().b[34][1]++;
      }
      cov_2k9ta5zj0b().s[145]++;
      return updatedScore;
    }
  }, {
    key: "updateStatistics",
    value: function updateStatistics(statistics, event) {
      cov_2k9ta5zj0b().f[17]++;
      cov_2k9ta5zj0b().s[146]++;
      statistics.totalPointsPlayed++;
      cov_2k9ta5zj0b().s[147]++;
      if (event.player === 'user') {
        cov_2k9ta5zj0b().b[35][0]++;
        cov_2k9ta5zj0b().s[148]++;
        statistics.totalPointsWon++;
      } else {
        cov_2k9ta5zj0b().b[35][1]++;
      }
      cov_2k9ta5zj0b().s[149]++;
      switch (event.eventType) {
        case 'ace':
          cov_2k9ta5zj0b().b[36][0]++;
          cov_2k9ta5zj0b().s[150]++;
          statistics.aces++;
          cov_2k9ta5zj0b().s[151]++;
          break;
        case 'double_fault':
          cov_2k9ta5zj0b().b[36][1]++;
          cov_2k9ta5zj0b().s[152]++;
          statistics.doubleFaults++;
          cov_2k9ta5zj0b().s[153]++;
          break;
        case 'winner':
          cov_2k9ta5zj0b().b[36][2]++;
          cov_2k9ta5zj0b().s[154]++;
          statistics.winners++;
          cov_2k9ta5zj0b().s[155]++;
          break;
        case 'unforced_error':
          cov_2k9ta5zj0b().b[36][3]++;
          cov_2k9ta5zj0b().s[156]++;
          statistics.unforcedErrors++;
          cov_2k9ta5zj0b().s[157]++;
          break;
        case 'forced_error':
          cov_2k9ta5zj0b().b[36][4]++;
          cov_2k9ta5zj0b().s[158]++;
          statistics.forcedErrors++;
          cov_2k9ta5zj0b().s[159]++;
          break;
      }
    }
  }, {
    key: "isSetComplete",
    value: function isSetComplete(set) {
      cov_2k9ta5zj0b().f[18]++;
      cov_2k9ta5zj0b().s[160]++;
      return (cov_2k9ta5zj0b().b[37][0]++, set.userGames >= 6) && (cov_2k9ta5zj0b().b[37][1]++, set.userGames - set.opponentGames >= 2) || (cov_2k9ta5zj0b().b[37][2]++, set.opponentGames >= 6) && (cov_2k9ta5zj0b().b[37][3]++, set.opponentGames - set.userGames >= 2) || (cov_2k9ta5zj0b().b[37][4]++, set.isTiebreak);
    }
  }, {
    key: "isGameComplete",
    value: function isGameComplete(set, gameNumber) {
      cov_2k9ta5zj0b().f[19]++;
      cov_2k9ta5zj0b().s[161]++;
      return true;
    }
  }, {
    key: "isMatchComplete",
    value: function isMatchComplete(score, format) {
      cov_2k9ta5zj0b().f[20]++;
      var setsToWin = (cov_2k9ta5zj0b().s[162]++, format === 'best_of_5' ? (cov_2k9ta5zj0b().b[38][0]++, 3) : (cov_2k9ta5zj0b().b[38][1]++, 2));
      cov_2k9ta5zj0b().s[163]++;
      return (cov_2k9ta5zj0b().b[39][0]++, score.setsWon >= setsToWin) || (cov_2k9ta5zj0b().b[39][1]++, score.setsLost >= setsToWin);
    }
  }, {
    key: "calculateFinalStatistics",
    value: function calculateFinalStatistics(statistics, score) {
      cov_2k9ta5zj0b().f[21]++;
      cov_2k9ta5zj0b().s[164]++;
      if (statistics.firstServesAttempted > 0) {
        cov_2k9ta5zj0b().b[40][0]++;
        cov_2k9ta5zj0b().s[165]++;
        statistics.firstServePercentage = statistics.firstServesIn / statistics.firstServesAttempted * 100;
      } else {
        cov_2k9ta5zj0b().b[40][1]++;
      }
      cov_2k9ta5zj0b().s[166]++;
      if (statistics.breakPointsFaced > 0) {
        cov_2k9ta5zj0b().b[41][0]++;
        cov_2k9ta5zj0b().s[167]++;
        statistics.breakPointConversionRate = statistics.breakPointsConverted / statistics.breakPointsFaced * 100;
      } else {
        cov_2k9ta5zj0b().b[41][1]++;
      }
      cov_2k9ta5zj0b().s[168]++;
      if (statistics.netPointsAttempted > 0) {
        cov_2k9ta5zj0b().b[42][0]++;
        cov_2k9ta5zj0b().s[169]++;
        statistics.netSuccessRate = statistics.netPointsWon / statistics.netPointsAttempted * 100;
      } else {
        cov_2k9ta5zj0b().b[42][1]++;
      }
    }
  }, {
    key: "saveMatchToDatabase",
    value: function () {
      var _saveMatchToDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        cov_2k9ta5zj0b().f[22]++;
        cov_2k9ta5zj0b().s[170]++;
        try {
          var matchData = (cov_2k9ta5zj0b().s[171]++, {
            id: match.id,
            user_id: match.metadata.userId,
            opponent_name: match.metadata.opponentName,
            match_type: (cov_2k9ta5zj0b().b[43][0]++, match.metadata.matchType) || (cov_2k9ta5zj0b().b[43][1]++, 'friendly'),
            match_format: match.metadata.matchFormat,
            surface: match.metadata.surface,
            location: match.metadata.location,
            court_name: match.metadata.courtName,
            weather_conditions: match.metadata.weather,
            temperature: match.metadata.temperature,
            match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],
            start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],
            status: match.status,
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            created_at: match.createdAt,
            updated_at: match.updatedAt
          });
          var attempts = (cov_2k9ta5zj0b().s[172]++, 0);
          var maxAttempts = (cov_2k9ta5zj0b().s[173]++, 3);
          cov_2k9ta5zj0b().s[174]++;
          while (attempts < maxAttempts) {
            cov_2k9ta5zj0b().s[175]++;
            try {
              var _result$data;
              var result = (cov_2k9ta5zj0b().s[176]++, yield _MatchRepository.matchRepository.createMatch(matchData));
              cov_2k9ta5zj0b().s[177]++;
              if (result.error) {
                cov_2k9ta5zj0b().b[44][0]++;
                cov_2k9ta5zj0b().s[178]++;
                if (attempts === maxAttempts - 1) {
                  cov_2k9ta5zj0b().b[45][0]++;
                  cov_2k9ta5zj0b().s[179]++;
                  return {
                    success: false,
                    error: result.error
                  };
                } else {
                  cov_2k9ta5zj0b().b[45][1]++;
                }
                cov_2k9ta5zj0b().s[180]++;
                attempts++;
                cov_2k9ta5zj0b().s[181]++;
                yield new Promise(function (resolve) {
                  cov_2k9ta5zj0b().f[23]++;
                  cov_2k9ta5zj0b().s[182]++;
                  return setTimeout(resolve, 1000 * attempts);
                });
                cov_2k9ta5zj0b().s[183]++;
                continue;
              } else {
                cov_2k9ta5zj0b().b[44][1]++;
              }
              cov_2k9ta5zj0b().s[184]++;
              return {
                success: true,
                data: {
                  id: match.id,
                  databaseId: (_result$data = result.data) == null ? void 0 : _result$data.id
                }
              };
            } catch (error) {
              cov_2k9ta5zj0b().s[185]++;
              attempts++;
              cov_2k9ta5zj0b().s[186]++;
              if (attempts === maxAttempts) {
                cov_2k9ta5zj0b().b[46][0]++;
                cov_2k9ta5zj0b().s[187]++;
                throw error;
              } else {
                cov_2k9ta5zj0b().b[46][1]++;
              }
              cov_2k9ta5zj0b().s[188]++;
              yield new Promise(function (resolve) {
                cov_2k9ta5zj0b().f[24]++;
                cov_2k9ta5zj0b().s[189]++;
                return setTimeout(resolve, 1000 * attempts);
              });
            }
          }
          cov_2k9ta5zj0b().s[190]++;
          return {
            success: false,
            error: 'Failed to save after multiple attempts'
          };
        } catch (error) {
          cov_2k9ta5zj0b().s[191]++;
          console.error('Error saving match to database:', error);
          cov_2k9ta5zj0b().s[192]++;
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function saveMatchToDatabase(_x4) {
        return _saveMatchToDatabase.apply(this, arguments);
      }
      return saveMatchToDatabase;
    }()
  }, {
    key: "updateMatchInDatabase",
    value: function () {
      var _updateMatchInDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        cov_2k9ta5zj0b().f[25]++;
        cov_2k9ta5zj0b().s[193]++;
        try {
          cov_2k9ta5zj0b().s[194]++;
          if (!match.id) {
            cov_2k9ta5zj0b().b[47][0]++;
            cov_2k9ta5zj0b().s[195]++;
            return {
              success: false,
              error: 'Match ID is required for update'
            };
          } else {
            cov_2k9ta5zj0b().b[47][1]++;
          }
          var updateData = (cov_2k9ta5zj0b().s[196]++, {
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            status: match.status,
            updated_at: new Date().toISOString()
          });
          cov_2k9ta5zj0b().s[197]++;
          if ((cov_2k9ta5zj0b().b[49][0]++, match.status === 'completed') && (cov_2k9ta5zj0b().b[49][1]++, match.metadata.endTime)) {
            cov_2k9ta5zj0b().b[48][0]++;
            cov_2k9ta5zj0b().s[198]++;
            updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];
            cov_2k9ta5zj0b().s[199]++;
            updateData.duration_minutes = Math.round((new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60));
            cov_2k9ta5zj0b().s[200]++;
            updateData.final_score = this.generateFinalScoreString(match.score);
            cov_2k9ta5zj0b().s[201]++;
            updateData.result = this.determineMatchResult(match.score, match.metadata.userId);
            cov_2k9ta5zj0b().s[202]++;
            updateData.sets_won = match.score.setsWon;
            cov_2k9ta5zj0b().s[203]++;
            updateData.sets_lost = match.score.setsLost;
          } else {
            cov_2k9ta5zj0b().b[48][1]++;
          }
          var result = (cov_2k9ta5zj0b().s[204]++, yield _MatchRepository.matchRepository.updateMatch(match.id, updateData));
          cov_2k9ta5zj0b().s[205]++;
          if (result.error) {
            cov_2k9ta5zj0b().b[50][0]++;
            cov_2k9ta5zj0b().s[206]++;
            return {
              success: false,
              error: result.error
            };
          } else {
            cov_2k9ta5zj0b().b[50][1]++;
          }
          cov_2k9ta5zj0b().s[207]++;
          return {
            success: true
          };
        } catch (error) {
          cov_2k9ta5zj0b().s[208]++;
          console.error('Error updating match in database:', error);
          cov_2k9ta5zj0b().s[209]++;
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function updateMatchInDatabase(_x5) {
        return _updateMatchInDatabase.apply(this, arguments);
      }
      return updateMatchInDatabase;
    }()
  }, {
    key: "generateFinalScoreString",
    value: function generateFinalScoreString(score) {
      cov_2k9ta5zj0b().f[26]++;
      cov_2k9ta5zj0b().s[210]++;
      if ((cov_2k9ta5zj0b().b[52][0]++, !score.sets) || (cov_2k9ta5zj0b().b[52][1]++, score.sets.length === 0)) {
        cov_2k9ta5zj0b().b[51][0]++;
        cov_2k9ta5zj0b().s[211]++;
        return '0-0';
      } else {
        cov_2k9ta5zj0b().b[51][1]++;
      }
      cov_2k9ta5zj0b().s[212]++;
      return score.sets.map(function (set) {
        cov_2k9ta5zj0b().f[27]++;
        cov_2k9ta5zj0b().s[213]++;
        return `${set.userGames}-${set.opponentGames}`;
      }).join(', ');
    }
  }, {
    key: "determineMatchResult",
    value: function determineMatchResult(score, userId) {
      cov_2k9ta5zj0b().f[28]++;
      cov_2k9ta5zj0b().s[214]++;
      if (score.setsWon > score.setsLost) {
        cov_2k9ta5zj0b().b[53][0]++;
        cov_2k9ta5zj0b().s[215]++;
        return 'win';
      } else {
        cov_2k9ta5zj0b().b[53][1]++;
        cov_2k9ta5zj0b().s[216]++;
        if (score.setsLost > score.setsWon) {
          cov_2k9ta5zj0b().b[54][0]++;
          cov_2k9ta5zj0b().s[217]++;
          return 'loss';
        } else {
          cov_2k9ta5zj0b().b[54][1]++;
        }
      }
      cov_2k9ta5zj0b().s[218]++;
      return 'draw';
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      cov_2k9ta5zj0b().f[29]++;
      cov_2k9ta5zj0b().s[219]++;
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateEventId",
    value: function generateEventId() {
      cov_2k9ta5zj0b().f[30]++;
      cov_2k9ta5zj0b().s[220]++;
      return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "notifySessionListeners",
    value: function notifySessionListeners() {
      var _this = this;
      cov_2k9ta5zj0b().f[31]++;
      cov_2k9ta5zj0b().s[221]++;
      this.sessionListeners.forEach(function (listener) {
        cov_2k9ta5zj0b().f[32]++;
        cov_2k9ta5zj0b().s[222]++;
        return listener(_this.currentSession);
      });
    }
  }, {
    key: "notifyScoreListeners",
    value: function notifyScoreListeners() {
      var _this2 = this;
      cov_2k9ta5zj0b().f[33]++;
      cov_2k9ta5zj0b().s[223]++;
      if (this.currentSession) {
        cov_2k9ta5zj0b().b[55][0]++;
        cov_2k9ta5zj0b().s[224]++;
        this.scoreListeners.forEach(function (listener) {
          cov_2k9ta5zj0b().f[34]++;
          cov_2k9ta5zj0b().s[225]++;
          return listener(_this2.currentSession.match.score);
        });
      } else {
        cov_2k9ta5zj0b().b[55][1]++;
      }
    }
  }]);
}();
var matchRecordingService = exports.matchRecordingService = (cov_2k9ta5zj0b().s[226]++, new MatchRecordingService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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