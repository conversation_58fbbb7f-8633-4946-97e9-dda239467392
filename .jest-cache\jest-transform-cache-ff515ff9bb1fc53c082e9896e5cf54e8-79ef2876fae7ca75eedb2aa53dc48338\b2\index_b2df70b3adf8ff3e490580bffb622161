0c75978b7f8376245fd8f2fd9927ee40
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _normalizeColors = _interopRequireDefault(require("@react-native/normalize-colors"));
var processColor = function processColor(color) {
  if (color === undefined || color === null) {
    return color;
  }
  var int32Color = (0, _normalizeColors.default)(color);
  if (int32Color === undefined || int32Color === null) {
    return undefined;
  }
  int32Color = (int32Color << 24 | int32Color >>> 8) >>> 0;
  return int32Color;
};
var _default = exports.default = processColor;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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