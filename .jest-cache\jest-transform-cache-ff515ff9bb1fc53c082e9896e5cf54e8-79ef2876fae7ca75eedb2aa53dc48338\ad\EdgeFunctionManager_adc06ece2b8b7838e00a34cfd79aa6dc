0f57a0c918113a66475884f30894a4c4
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_h66wr1861() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\edge\\EdgeFunctionManager.ts";
  var hash = "59cdbbeb4135e0c387607b38f0d9a4de1d9e0110";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\edge\\EdgeFunctionManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 86,
          column: 53
        },
        end: {
          line: 86,
          column: 62
        }
      },
      "1": {
        start: {
          line: 87,
          column: 46
        },
        end: {
          line: 87,
          column: 48
        }
      },
      "2": {
        start: {
          line: 88,
          column: 45
        },
        end: {
          line: 88,
          column: 54
        }
      },
      "3": {
        start: {
          line: 89,
          column: 51
        },
        end: {
          line: 89,
          column: 60
        }
      },
      "4": {
        start: {
          line: 91,
          column: 34
        },
        end: {
          line: 95,
          column: 3
        }
      },
      "5": {
        start: {
          line: 97,
          column: 67
        },
        end: {
          line: 178,
          column: 3
        }
      },
      "6": {
        start: {
          line: 181,
          column: 4
        },
        end: {
          line: 181,
          column: 41
        }
      },
      "7": {
        start: {
          line: 188,
          column: 4
        },
        end: {
          line: 201,
          column: 5
        }
      },
      "8": {
        start: {
          line: 190,
          column: 6
        },
        end: {
          line: 190,
          column: 45
        }
      },
      "9": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 193,
          column: 40
        }
      },
      "10": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 38
        }
      },
      "11": {
        start: {
          line: 198,
          column: 6
        },
        end: {
          line: 198,
          column: 68
        }
      },
      "12": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 200,
          column: 74
        }
      },
      "13": {
        start: {
          line: 211,
          column: 4
        },
        end: {
          line: 260,
          column: 5
        }
      },
      "14": {
        start: {
          line: 212,
          column: 25
        },
        end: {
          line: 212,
          column: 75
        }
      },
      "15": {
        start: {
          line: 215,
          column: 41
        },
        end: {
          line: 242,
          column: 7
        }
      },
      "16": {
        start: {
          line: 245,
          column: 6
        },
        end: {
          line: 245,
          column: 55
        }
      },
      "17": {
        start: {
          line: 248,
          column: 47
        },
        end: {
          line: 251,
          column: 7
        }
      },
      "18": {
        start: {
          line: 252,
          column: 6
        },
        end: {
          line: 252,
          column: 50
        }
      },
      "19": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 254,
          column: 72
        }
      },
      "20": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 24
        }
      },
      "21": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 258,
          column: 62
        }
      },
      "22": {
        start: {
          line: 259,
          column: 6
        },
        end: {
          line: 259,
          column: 18
        }
      },
      "23": {
        start: {
          line: 267,
          column: 4
        },
        end: {
          line: 344,
          column: 5
        }
      },
      "24": {
        start: {
          line: 268,
          column: 24
        },
        end: {
          line: 268,
          column: 34
        }
      },
      "25": {
        start: {
          line: 269,
          column: 27
        },
        end: {
          line: 269,
          column: 69
        }
      },
      "26": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 273,
          column: 7
        }
      },
      "27": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 272,
          column: 74
        }
      },
      "28": {
        start: {
          line: 276,
          column: 30
        },
        end: {
          line: 279,
          column: 7
        }
      },
      "29": {
        start: {
          line: 282,
          column: 23
        },
        end: {
          line: 282,
          column: 53
        }
      },
      "30": {
        start: {
          line: 283,
          column: 27
        },
        end: {
          line: 283,
          column: 60
        }
      },
      "31": {
        start: {
          line: 285,
          column: 6
        },
        end: {
          line: 294,
          column: 7
        }
      },
      "32": {
        start: {
          line: 286,
          column: 8
        },
        end: {
          line: 293,
          column: 10
        }
      },
      "33": {
        start: {
          line: 297,
          column: 21
        },
        end: {
          line: 301,
          column: 7
        }
      },
      "34": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 310,
          column: 7
        }
      },
      "35": {
        start: {
          line: 305,
          column: 8
        },
        end: {
          line: 309,
          column: 11
        }
      },
      "36": {
        start: {
          line: 312,
          column: 28
        },
        end: {
          line: 312,
          column: 50
        }
      },
      "37": {
        start: {
          line: 315,
          column: 6
        },
        end: {
          line: 315,
          column: 77
        }
      },
      "38": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 316,
          column: 86
        }
      },
      "39": {
        start: {
          line: 318,
          column: 6
        },
        end: {
          line: 325,
          column: 8
        }
      },
      "40": {
        start: {
          line: 328,
          column: 28
        },
        end: {
          line: 328,
          column: 51
        }
      },
      "41": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 329,
          column: 78
        }
      },
      "42": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 343,
          column: 8
        }
      },
      "43": {
        start: {
          line: 357,
          column: 25
        },
        end: {
          line: 357,
          column: 59
        }
      },
      "44": {
        start: {
          line: 358,
          column: 20
        },
        end: {
          line: 358,
          column: 65
        }
      },
      "45": {
        start: {
          line: 360,
          column: 4
        },
        end: {
          line: 368,
          column: 5
        }
      },
      "46": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 367,
          column: 8
        }
      },
      "47": {
        start: {
          line: 371,
          column: 26
        },
        end: {
          line: 371,
          column: 45
        }
      },
      "48": {
        start: {
          line: 372,
          column: 33
        },
        end: {
          line: 372,
          column: 69
        }
      },
      "49": {
        start: {
          line: 372,
          column: 59
        },
        end: {
          line: 372,
          column: 68
        }
      },
      "50": {
        start: {
          line: 374,
          column: 33
        },
        end: {
          line: 376,
          column: 9
        }
      },
      "51": {
        start: {
          line: 375,
          column: 48
        },
        end: {
          line: 375,
          column: 69
        }
      },
      "52": {
        start: {
          line: 378,
          column: 24
        },
        end: {
          line: 380,
          column: 9
        }
      },
      "53": {
        start: {
          line: 383,
          column: 25
        },
        end: {
          line: 383,
          column: 43
        }
      },
      "54": {
        start: {
          line: 384,
          column: 30
        },
        end: {
          line: 384,
          column: 77
        }
      },
      "55": {
        start: {
          line: 384,
          column: 50
        },
        end: {
          line: 384,
          column: 76
        }
      },
      "56": {
        start: {
          line: 385,
          column: 33
        },
        end: {
          line: 385,
          column: 57
        }
      },
      "57": {
        start: {
          line: 388,
          column: 56
        },
        end: {
          line: 388,
          column: 58
        }
      },
      "58": {
        start: {
          line: 389,
          column: 25
        },
        end: {
          line: 393,
          column: 35
        }
      },
      "59": {
        start: {
          line: 390,
          column: 6
        },
        end: {
          line: 390,
          column: 61
        }
      },
      "60": {
        start: {
          line: 390,
          column: 34
        },
        end: {
          line: 390,
          column: 61
        }
      },
      "61": {
        start: {
          line: 391,
          column: 6
        },
        end: {
          line: 391,
          column: 41
        }
      },
      "62": {
        start: {
          line: 392,
          column: 6
        },
        end: {
          line: 392,
          column: 20
        }
      },
      "63": {
        start: {
          line: 395,
          column: 4
        },
        end: {
          line: 398,
          column: 7
        }
      },
      "64": {
        start: {
          line: 396,
          column: 22
        },
        end: {
          line: 396,
          column: 103
        }
      },
      "65": {
        start: {
          line: 396,
          column: 55
        },
        end: {
          line: 396,
          column: 76
        }
      },
      "66": {
        start: {
          line: 397,
          column: 6
        },
        end: {
          line: 397,
          column: 44
        }
      },
      "67": {
        start: {
          line: 401,
          column: 51
        },
        end: {
          line: 401,
          column: 53
        }
      },
      "68": {
        start: {
          line: 402,
          column: 29
        },
        end: {
          line: 402,
          column: 66
        }
      },
      "69": {
        start: {
          line: 402,
          column: 55
        },
        end: {
          line: 402,
          column: 65
        }
      },
      "70": {
        start: {
          line: 403,
          column: 4
        },
        end: {
          line: 406,
          column: 7
        }
      },
      "71": {
        start: {
          line: 404,
          column: 24
        },
        end: {
          line: 404,
          column: 52
        }
      },
      "72": {
        start: {
          line: 405,
          column: 6
        },
        end: {
          line: 405,
          column: 71
        }
      },
      "73": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 414,
          column: 6
        }
      },
      "74": {
        start: {
          line: 421,
          column: 4
        },
        end: {
          line: 422,
          column: 61
        }
      },
      "75": {
        start: {
          line: 422,
          column: 22
        },
        end: {
          line: 422,
          column: 59
        }
      },
      "76": {
        start: {
          line: 432,
          column: 25
        },
        end: {
          line: 432,
          column: 59
        }
      },
      "77": {
        start: {
          line: 433,
          column: 4
        },
        end: {
          line: 435,
          column: 5
        }
      },
      "78": {
        start: {
          line: 434,
          column: 6
        },
        end: {
          line: 434,
          column: 59
        }
      },
      "79": {
        start: {
          line: 438,
          column: 4
        },
        end: {
          line: 438,
          column: 78
        }
      },
      "80": {
        start: {
          line: 441,
          column: 4
        },
        end: {
          line: 441,
          column: 44
        }
      },
      "81": {
        start: {
          line: 447,
          column: 4
        },
        end: {
          line: 462,
          column: 5
        }
      },
      "82": {
        start: {
          line: 448,
          column: 6
        },
        end: {
          line: 461,
          column: 7
        }
      },
      "83": {
        start: {
          line: 449,
          column: 8
        },
        end: {
          line: 458,
          column: 11
        }
      },
      "84": {
        start: {
          line: 460,
          column: 8
        },
        end: {
          line: 460,
          column: 84
        }
      },
      "85": {
        start: {
          line: 470,
          column: 4
        },
        end: {
          line: 472,
          column: 5
        }
      },
      "86": {
        start: {
          line: 471,
          column: 6
        },
        end: {
          line: 471,
          column: 29
        }
      },
      "87": {
        start: {
          line: 475,
          column: 30
        },
        end: {
          line: 475,
          column: 55
        }
      },
      "88": {
        start: {
          line: 477,
          column: 4
        },
        end: {
          line: 481,
          column: 5
        }
      },
      "89": {
        start: {
          line: 479,
          column: 22
        },
        end: {
          line: 479,
          column: 46
        }
      },
      "90": {
        start: {
          line: 480,
          column: 6
        },
        end: {
          line: 480,
          column: 45
        }
      },
      "91": {
        start: {
          line: 484,
          column: 26
        },
        end: {
          line: 485,
          column: 34
        }
      },
      "92": {
        start: {
          line: 485,
          column: 22
        },
        end: {
          line: 485,
          column: 33
        }
      },
      "93": {
        start: {
          line: 487,
          column: 4
        },
        end: {
          line: 487,
          column: 56
        }
      },
      "94": {
        start: {
          line: 498,
          column: 26
        },
        end: {
          line: 498,
          column: 50
        }
      },
      "95": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 499,
          column: 69
        }
      },
      "96": {
        start: {
          line: 499,
          column: 33
        },
        end: {
          line: 499,
          column: 67
        }
      },
      "97": {
        start: {
          line: 502,
          column: 4
        },
        end: {
          line: 526,
          column: 5
        }
      },
      "98": {
        start: {
          line: 504,
          column: 8
        },
        end: {
          line: 504,
          column: 82
        }
      },
      "99": {
        start: {
          line: 507,
          column: 8
        },
        end: {
          line: 512,
          column: 10
        }
      },
      "100": {
        start: {
          line: 515,
          column: 8
        },
        end: {
          line: 519,
          column: 10
        }
      },
      "101": {
        start: {
          line: 522,
          column: 8
        },
        end: {
          line: 522,
          column: 57
        }
      },
      "102": {
        start: {
          line: 525,
          column: 8
        },
        end: {
          line: 525,
          column: 60
        }
      },
      "103": {
        start: {
          line: 530,
          column: 4
        },
        end: {
          line: 530,
          column: 80
        }
      },
      "104": {
        start: {
          line: 534,
          column: 4
        },
        end: {
          line: 534,
          column: 28
        }
      },
      "105": {
        start: {
          line: 538,
          column: 20
        },
        end: {
          line: 538,
          column: 51
        }
      },
      "106": {
        start: {
          line: 539,
          column: 4
        },
        end: {
          line: 539,
          column: 65
        }
      },
      "107": {
        start: {
          line: 543,
          column: 16
        },
        end: {
          line: 543,
          column: 51
        }
      },
      "108": {
        start: {
          line: 544,
          column: 4
        },
        end: {
          line: 544,
          column: 34
        }
      },
      "109": {
        start: {
          line: 549,
          column: 27
        },
        end: {
          line: 549,
          column: 64
        }
      },
      "110": {
        start: {
          line: 550,
          column: 4
        },
        end: {
          line: 550,
          column: 52
        }
      },
      "111": {
        start: {
          line: 555,
          column: 43
        },
        end: {
          line: 560,
          column: 5
        }
      },
      "112": {
        start: {
          line: 562,
          column: 4
        },
        end: {
          line: 562,
          column: 45
        }
      },
      "113": {
        start: {
          line: 572,
          column: 4
        },
        end: {
          line: 574,
          column: 5
        }
      },
      "114": {
        start: {
          line: 573,
          column: 6
        },
        end: {
          line: 573,
          column: 50
        }
      },
      "115": {
        start: {
          line: 576,
          column: 20
        },
        end: {
          line: 576,
          column: 60
        }
      },
      "116": {
        start: {
          line: 577,
          column: 4
        },
        end: {
          line: 583,
          column: 7
        }
      },
      "117": {
        start: {
          line: 586,
          column: 4
        },
        end: {
          line: 588,
          column: 5
        }
      },
      "118": {
        start: {
          line: 587,
          column: 6
        },
        end: {
          line: 587,
          column: 29
        }
      },
      "119": {
        start: {
          line: 593,
          column: 4
        },
        end: {
          line: 595,
          column: 14
        }
      },
      "120": {
        start: {
          line: 594,
          column: 6
        },
        end: {
          line: 594,
          column: 46
        }
      },
      "121": {
        start: {
          line: 599,
          column: 4
        },
        end: {
          line: 609,
          column: 5
        }
      },
      "122": {
        start: {
          line: 600,
          column: 22
        },
        end: {
          line: 600,
          column: 57
        }
      },
      "123": {
        start: {
          line: 603,
          column: 6
        },
        end: {
          line: 608,
          column: 8
        }
      },
      "124": {
        start: {
          line: 614,
          column: 4
        },
        end: {
          line: 616,
          column: 14
        }
      },
      "125": {
        start: {
          line: 615,
          column: 6
        },
        end: {
          line: 615,
          column: 36
        }
      },
      "126": {
        start: {
          line: 620,
          column: 4
        },
        end: {
          line: 620,
          column: 50
        }
      },
      "127": {
        start: {
          line: 620,
          column: 43
        },
        end: {
          line: 620,
          column: 50
        }
      },
      "128": {
        start: {
          line: 622,
          column: 23
        },
        end: {
          line: 622,
          column: 52
        }
      },
      "129": {
        start: {
          line: 623,
          column: 4
        },
        end: {
          line: 623,
          column: 45
        }
      },
      "130": {
        start: {
          line: 627,
          column: 4
        },
        end: {
          line: 654,
          column: 5
        }
      },
      "131": {
        start: {
          line: 628,
          column: 27
        },
        end: {
          line: 628,
          column: 72
        }
      },
      "132": {
        start: {
          line: 629,
          column: 6
        },
        end: {
          line: 629,
          column: 32
        }
      },
      "133": {
        start: {
          line: 629,
          column: 25
        },
        end: {
          line: 629,
          column: 32
        }
      },
      "134": {
        start: {
          line: 631,
          column: 6
        },
        end: {
          line: 631,
          column: 105
        }
      },
      "135": {
        start: {
          line: 634,
          column: 6
        },
        end: {
          line: 634,
          column: 51
        }
      },
      "136": {
        start: {
          line: 637,
          column: 6
        },
        end: {
          line: 640,
          column: 7
        }
      },
      "137": {
        start: {
          line: 637,
          column: 28
        },
        end: {
          line: 637,
          column: 29
        }
      },
      "138": {
        start: {
          line: 638,
          column: 8
        },
        end: {
          line: 638,
          column: 63
        }
      },
      "139": {
        start: {
          line: 639,
          column: 8
        },
        end: {
          line: 639,
          column: 64
        }
      },
      "140": {
        start: {
          line: 639,
          column: 37
        },
        end: {
          line: 639,
          column: 62
        }
      },
      "141": {
        start: {
          line: 642,
          column: 6
        },
        end: {
          line: 642,
          column: 50
        }
      },
      "142": {
        start: {
          line: 643,
          column: 6
        },
        end: {
          line: 643,
          column: 54
        }
      },
      "143": {
        start: {
          line: 645,
          column: 6
        },
        end: {
          line: 645,
          column: 77
        }
      },
      "144": {
        start: {
          line: 648,
          column: 6
        },
        end: {
          line: 648,
          column: 82
        }
      },
      "145": {
        start: {
          line: 650,
          column: 27
        },
        end: {
          line: 650,
          column: 72
        }
      },
      "146": {
        start: {
          line: 651,
          column: 6
        },
        end: {
          line: 653,
          column: 7
        }
      },
      "147": {
        start: {
          line: 652,
          column: 8
        },
        end: {
          line: 652,
          column: 50
        }
      },
      "148": {
        start: {
          line: 658,
          column: 25
        },
        end: {
          line: 658,
          column: 59
        }
      },
      "149": {
        start: {
          line: 659,
          column: 4
        },
        end: {
          line: 659,
          column: 30
        }
      },
      "150": {
        start: {
          line: 659,
          column: 23
        },
        end: {
          line: 659,
          column: 30
        }
      },
      "151": {
        start: {
          line: 662,
          column: 4
        },
        end: {
          line: 672,
          column: 7
        }
      },
      "152": {
        start: {
          line: 677,
          column: 35
        },
        end: {
          line: 677,
          column: 60
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 180,
            column: 2
          },
          end: {
            line: 180,
            column: 3
          }
        },
        loc: {
          start: {
            line: 180,
            column: 16
          },
          end: {
            line: 182,
            column: 3
          }
        },
        line: 180
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 187,
            column: 2
          },
          end: {
            line: 187,
            column: 3
          }
        },
        loc: {
          start: {
            line: 187,
            column: 63
          },
          end: {
            line: 202,
            column: 3
          }
        },
        line: 187
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 207,
            column: 2
          },
          end: {
            line: 207,
            column: 3
          }
        },
        loc: {
          start: {
            line: 210,
            column: 21
          },
          end: {
            line: 261,
            column: 3
          }
        },
        line: 210
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 266,
            column: 2
          },
          end: {
            line: 266,
            column: 3
          }
        },
        loc: {
          start: {
            line: 266,
            column: 69
          },
          end: {
            line: 345,
            column: 3
          }
        },
        line: 266
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 350,
            column: 2
          },
          end: {
            line: 350,
            column: 3
          }
        },
        loc: {
          start: {
            line: 356,
            column: 4
          },
          end: {
            line: 415,
            column: 3
          }
        },
        line: 356
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 372,
            column: 54
          },
          end: {
            line: 372,
            column: 55
          }
        },
        loc: {
          start: {
            line: 372,
            column: 59
          },
          end: {
            line: 372,
            column: 68
          }
        },
        line: 372
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 375,
            column: 36
          },
          end: {
            line: 375,
            column: 37
          }
        },
        loc: {
          start: {
            line: 375,
            column: 48
          },
          end: {
            line: 375,
            column: 69
          }
        },
        line: 375
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 384,
            column: 45
          },
          end: {
            line: 384,
            column: 46
          }
        },
        loc: {
          start: {
            line: 384,
            column: 50
          },
          end: {
            line: 384,
            column: 76
          }
        },
        line: 384
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 389,
            column: 46
          },
          end: {
            line: 389,
            column: 47
          }
        },
        loc: {
          start: {
            line: 389,
            column: 66
          },
          end: {
            line: 393,
            column: 5
          }
        },
        line: 389
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 395,
            column: 41
          },
          end: {
            line: 395,
            column: 42
          }
        },
        loc: {
          start: {
            line: 395,
            column: 70
          },
          end: {
            line: 398,
            column: 5
          }
        },
        line: 395
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 396,
            column: 43
          },
          end: {
            line: 396,
            column: 44
          }
        },
        loc: {
          start: {
            line: 396,
            column: 55
          },
          end: {
            line: 396,
            column: 76
          }
        },
        line: 396
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 402,
            column: 50
          },
          end: {
            line: 402,
            column: 51
          }
        },
        loc: {
          start: {
            line: 402,
            column: 55
          },
          end: {
            line: 402,
            column: 65
          }
        },
        line: 402
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 403,
            column: 29
          },
          end: {
            line: 403,
            column: 30
          }
        },
        loc: {
          start: {
            line: 403,
            column: 42
          },
          end: {
            line: 406,
            column: 5
          }
        },
        line: 403
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 420,
            column: 2
          },
          end: {
            line: 420,
            column: 3
          }
        },
        loc: {
          start: {
            line: 420,
            column: 41
          },
          end: {
            line: 423,
            column: 3
          }
        },
        line: 420
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 422,
            column: 14
          },
          end: {
            line: 422,
            column: 15
          }
        },
        loc: {
          start: {
            line: 422,
            column: 22
          },
          end: {
            line: 422,
            column: 59
          }
        },
        line: 422
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 428,
            column: 2
          },
          end: {
            line: 428,
            column: 3
          }
        },
        loc: {
          start: {
            line: 431,
            column: 19
          },
          end: {
            line: 442,
            column: 3
          }
        },
        line: 431
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 446,
            column: 2
          },
          end: {
            line: 446,
            column: 3
          }
        },
        loc: {
          start: {
            line: 446,
            column: 59
          },
          end: {
            line: 463,
            column: 3
          }
        },
        line: 446
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 465,
            column: 2
          },
          end: {
            line: 465,
            column: 3
          }
        },
        loc: {
          start: {
            line: 468,
            column: 21
          },
          end: {
            line: 488,
            column: 3
          }
        },
        line: 468
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 485,
            column: 12
          },
          end: {
            line: 485,
            column: 13
          }
        },
        loc: {
          start: {
            line: 485,
            column: 22
          },
          end: {
            line: 485,
            column: 33
          }
        },
        line: 485
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 490,
            column: 2
          },
          end: {
            line: 490,
            column: 3
          }
        },
        loc: {
          start: {
            line: 494,
            column: 18
          },
          end: {
            line: 527,
            column: 3
          }
        },
        line: 494
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 499,
            column: 22
          },
          end: {
            line: 499,
            column: 23
          }
        },
        loc: {
          start: {
            line: 499,
            column: 33
          },
          end: {
            line: 499,
            column: 67
          }
        },
        line: 499
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 529,
            column: 2
          },
          end: {
            line: 529,
            column: 3
          }
        },
        loc: {
          start: {
            line: 529,
            column: 39
          },
          end: {
            line: 531,
            column: 3
          }
        },
        line: 529
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 533,
            column: 2
          },
          end: {
            line: 533,
            column: 3
          }
        },
        loc: {
          start: {
            line: 533,
            column: 36
          },
          end: {
            line: 535,
            column: 3
          }
        },
        line: 533
      },
      "23": {
        name: "(anonymous_23)",
        decl: {
          start: {
            line: 537,
            column: 2
          },
          end: {
            line: 537,
            column: 3
          }
        },
        loc: {
          start: {
            line: 537,
            column: 57
          },
          end: {
            line: 540,
            column: 3
          }
        },
        line: 537
      },
      "24": {
        name: "(anonymous_24)",
        decl: {
          start: {
            line: 542,
            column: 2
          },
          end: {
            line: 542,
            column: 3
          }
        },
        loc: {
          start: {
            line: 542,
            column: 51
          },
          end: {
            line: 545,
            column: 3
          }
        },
        line: 542
      },
      "25": {
        name: "(anonymous_25)",
        decl: {
          start: {
            line: 547,
            column: 2
          },
          end: {
            line: 547,
            column: 3
          }
        },
        loc: {
          start: {
            line: 547,
            column: 78
          },
          end: {
            line: 551,
            column: 3
          }
        },
        line: 547
      },
      "26": {
        name: "(anonymous_26)",
        decl: {
          start: {
            line: 553,
            column: 2
          },
          end: {
            line: 553,
            column: 3
          }
        },
        loc: {
          start: {
            line: 553,
            column: 64
          },
          end: {
            line: 563,
            column: 3
          }
        },
        line: 553
      },
      "27": {
        name: "(anonymous_27)",
        decl: {
          start: {
            line: 565,
            column: 2
          },
          end: {
            line: 565,
            column: 3
          }
        },
        loc: {
          start: {
            line: 571,
            column: 10
          },
          end: {
            line: 589,
            column: 3
          }
        },
        line: 571
      },
      "28": {
        name: "(anonymous_28)",
        decl: {
          start: {
            line: 591,
            column: 2
          },
          end: {
            line: 591,
            column: 3
          }
        },
        loc: {
          start: {
            line: 591,
            column: 45
          },
          end: {
            line: 596,
            column: 3
          }
        },
        line: 591
      },
      "29": {
        name: "(anonymous_29)",
        decl: {
          start: {
            line: 593,
            column: 16
          },
          end: {
            line: 593,
            column: 17
          }
        },
        loc: {
          start: {
            line: 593,
            column: 22
          },
          end: {
            line: 595,
            column: 5
          }
        },
        line: 593
      },
      "30": {
        name: "(anonymous_30)",
        decl: {
          start: {
            line: 598,
            column: 2
          },
          end: {
            line: 598,
            column: 3
          }
        },
        loc: {
          start: {
            line: 598,
            column: 51
          },
          end: {
            line: 610,
            column: 3
          }
        },
        line: 598
      },
      "31": {
        name: "(anonymous_31)",
        decl: {
          start: {
            line: 612,
            column: 2
          },
          end: {
            line: 612,
            column: 3
          }
        },
        loc: {
          start: {
            line: 612,
            column: 43
          },
          end: {
            line: 617,
            column: 3
          }
        },
        line: 612
      },
      "32": {
        name: "(anonymous_32)",
        decl: {
          start: {
            line: 614,
            column: 16
          },
          end: {
            line: 614,
            column: 17
          }
        },
        loc: {
          start: {
            line: 614,
            column: 22
          },
          end: {
            line: 616,
            column: 5
          }
        },
        line: 614
      },
      "33": {
        name: "(anonymous_33)",
        decl: {
          start: {
            line: 619,
            column: 2
          },
          end: {
            line: 619,
            column: 3
          }
        },
        loc: {
          start: {
            line: 619,
            column: 56
          },
          end: {
            line: 624,
            column: 3
          }
        },
        line: 619
      },
      "34": {
        name: "(anonymous_34)",
        decl: {
          start: {
            line: 626,
            column: 2
          },
          end: {
            line: 626,
            column: 3
          }
        },
        loc: {
          start: {
            line: 626,
            column: 77
          },
          end: {
            line: 655,
            column: 3
          }
        },
        line: 626
      },
      "35": {
        name: "(anonymous_35)",
        decl: {
          start: {
            line: 639,
            column: 26
          },
          end: {
            line: 639,
            column: 27
          }
        },
        loc: {
          start: {
            line: 639,
            column: 37
          },
          end: {
            line: 639,
            column: 62
          }
        },
        line: 639
      },
      "36": {
        name: "(anonymous_36)",
        decl: {
          start: {
            line: 657,
            column: 2
          },
          end: {
            line: 657,
            column: 3
          }
        },
        loc: {
          start: {
            line: 657,
            column: 68
          },
          end: {
            line: 673,
            column: 3
          }
        },
        line: 657
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 212,
            column: 25
          },
          end: {
            line: 212,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 212,
            column: 25
          },
          end: {
            line: 212,
            column: 46
          }
        }, {
          start: {
            line: 212,
            column: 50
          },
          end: {
            line: 212,
            column: 75
          }
        }],
        line: 212
      },
      "1": {
        loc: {
          start: {
            line: 217,
            column: 14
          },
          end: {
            line: 217,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 217,
            column: 14
          },
          end: {
            line: 217,
            column: 37
          }
        }, {
          start: {
            line: 217,
            column: 41
          },
          end: {
            line: 217,
            column: 59
          }
        }],
        line: 217
      },
      "2": {
        loc: {
          start: {
            line: 218,
            column: 21
          },
          end: {
            line: 218,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 218,
            column: 21
          },
          end: {
            line: 218,
            column: 51
          }
        }, {
          start: {
            line: 218,
            column: 55
          },
          end: {
            line: 218,
            column: 57
          }
        }],
        line: 218
      },
      "3": {
        loc: {
          start: {
            line: 219,
            column: 14
          },
          end: {
            line: 219,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 219,
            column: 14
          },
          end: {
            line: 219,
            column: 37
          }
        }, {
          start: {
            line: 219,
            column: 41
          },
          end: {
            line: 219,
            column: 43
          }
        }],
        line: 219
      },
      "4": {
        loc: {
          start: {
            line: 220,
            column: 17
          },
          end: {
            line: 220,
            column: 59
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 220,
            column: 17
          },
          end: {
            line: 220,
            column: 43
          }
        }, {
          start: {
            line: 220,
            column: 47
          },
          end: {
            line: 220,
            column: 59
          }
        }],
        line: 220
      },
      "5": {
        loc: {
          start: {
            line: 221,
            column: 18
          },
          end: {
            line: 221,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 221,
            column: 18
          },
          end: {
            line: 221,
            column: 45
          }
        }, {
          start: {
            line: 221,
            column: 49
          },
          end: {
            line: 221,
            column: 51
          }
        }],
        line: 221
      },
      "6": {
        loc: {
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "7": {
        loc: {
          start: {
            line: 285,
            column: 6
          },
          end: {
            line: 294,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 285,
            column: 6
          },
          end: {
            line: 294,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 285
      },
      "8": {
        loc: {
          start: {
            line: 285,
            column: 10
          },
          end: {
            line: 285,
            column: 57
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 10
          },
          end: {
            line: 285,
            column: 22
          }
        }, {
          start: {
            line: 285,
            column: 26
          },
          end: {
            line: 285,
            column: 57
          }
        }],
        line: 285
      },
      "9": {
        loc: {
          start: {
            line: 304,
            column: 6
          },
          end: {
            line: 310,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 304,
            column: 6
          },
          end: {
            line: 310,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 304
      },
      "10": {
        loc: {
          start: {
            line: 324,
            column: 21
          },
          end: {
            line: 324,
            column: 52
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 324,
            column: 36
          },
          end: {
            line: 324,
            column: 43
          }
        }, {
          start: {
            line: 324,
            column: 46
          },
          end: {
            line: 324,
            column: 52
          }
        }],
        line: 324
      },
      "11": {
        loc: {
          start: {
            line: 335,
            column: 16
          },
          end: {
            line: 335,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 335,
            column: 16
          },
          end: {
            line: 335,
            column: 30
          }
        }, {
          start: {
            line: 335,
            column: 34
          },
          end: {
            line: 335,
            column: 43
          }
        }],
        line: 335
      },
      "12": {
        loc: {
          start: {
            line: 340,
            column: 19
          },
          end: {
            line: 340,
            column: 75
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 340,
            column: 44
          },
          end: {
            line: 340,
            column: 57
          }
        }, {
          start: {
            line: 340,
            column: 60
          },
          end: {
            line: 340,
            column: 75
          }
        }],
        line: 340
      },
      "13": {
        loc: {
          start: {
            line: 341,
            column: 17
          },
          end: {
            line: 341,
            column: 65
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 341,
            column: 42
          },
          end: {
            line: 341,
            column: 53
          }
        }, {
          start: {
            line: 341,
            column: 56
          },
          end: {
            line: 341,
            column: 65
          }
        }],
        line: 341
      },
      "14": {
        loc: {
          start: {
            line: 358,
            column: 20
          },
          end: {
            line: 358,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 358,
            column: 20
          },
          end: {
            line: 358,
            column: 59
          }
        }, {
          start: {
            line: 358,
            column: 63
          },
          end: {
            line: 358,
            column: 65
          }
        }],
        line: 358
      },
      "15": {
        loc: {
          start: {
            line: 360,
            column: 4
          },
          end: {
            line: 368,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 360,
            column: 4
          },
          end: {
            line: 368,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 360
      },
      "16": {
        loc: {
          start: {
            line: 360,
            column: 8
          },
          end: {
            line: 360,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 360,
            column: 8
          },
          end: {
            line: 360,
            column: 21
          }
        }, {
          start: {
            line: 360,
            column: 25
          },
          end: {
            line: 360,
            column: 45
          }
        }],
        line: 360
      },
      "17": {
        loc: {
          start: {
            line: 374,
            column: 33
          },
          end: {
            line: 376,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 375,
            column: 8
          },
          end: {
            line: 375,
            column: 103
          }
        }, {
          start: {
            line: 376,
            column: 8
          },
          end: {
            line: 376,
            column: 9
          }
        }],
        line: 374
      },
      "18": {
        loc: {
          start: {
            line: 378,
            column: 24
          },
          end: {
            line: 380,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 379,
            column: 8
          },
          end: {
            line: 379,
            column: 66
          }
        }, {
          start: {
            line: 380,
            column: 8
          },
          end: {
            line: 380,
            column: 9
          }
        }],
        line: 378
      },
      "19": {
        loc: {
          start: {
            line: 390,
            column: 6
          },
          end: {
            line: 390,
            column: 61
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 390,
            column: 6
          },
          end: {
            line: 390,
            column: 61
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 390
      },
      "20": {
        loc: {
          start: {
            line: 404,
            column: 24
          },
          end: {
            line: 404,
            column: 52
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 404,
            column: 24
          },
          end: {
            line: 404,
            column: 39
          }
        }, {
          start: {
            line: 404,
            column: 43
          },
          end: {
            line: 404,
            column: 52
          }
        }],
        line: 404
      },
      "21": {
        loc: {
          start: {
            line: 405,
            column: 35
          },
          end: {
            line: 405,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 405,
            column: 35
          },
          end: {
            line: 405,
            column: 60
          }
        }, {
          start: {
            line: 405,
            column: 64
          },
          end: {
            line: 405,
            column: 65
          }
        }],
        line: 405
      },
      "22": {
        loc: {
          start: {
            line: 433,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 433,
            column: 4
          },
          end: {
            line: 435,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 433
      },
      "23": {
        loc: {
          start: {
            line: 467,
            column: 4
          },
          end: {
            line: 467,
            column: 50
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 467,
            column: 33
          },
          end: {
            line: 467,
            column: 50
          }
        }],
        line: 467
      },
      "24": {
        loc: {
          start: {
            line: 470,
            column: 4
          },
          end: {
            line: 472,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 470,
            column: 4
          },
          end: {
            line: 472,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 470
      },
      "25": {
        loc: {
          start: {
            line: 470,
            column: 8
          },
          end: {
            line: 470,
            column: 69
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 470,
            column: 8
          },
          end: {
            line: 470,
            column: 23
          }
        }, {
          start: {
            line: 470,
            column: 27
          },
          end: {
            line: 470,
            column: 69
          }
        }],
        line: 470
      },
      "26": {
        loc: {
          start: {
            line: 487,
            column: 11
          },
          end: {
            line: 487,
            column: 55
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 487,
            column: 11
          },
          end: {
            line: 487,
            column: 32
          }
        }, {
          start: {
            line: 487,
            column: 36
          },
          end: {
            line: 487,
            column: 55
          }
        }],
        line: 487
      },
      "27": {
        loc: {
          start: {
            line: 502,
            column: 4
          },
          end: {
            line: 526,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 503,
            column: 6
          },
          end: {
            line: 504,
            column: 82
          }
        }, {
          start: {
            line: 506,
            column: 6
          },
          end: {
            line: 512,
            column: 10
          }
        }, {
          start: {
            line: 514,
            column: 6
          },
          end: {
            line: 519,
            column: 10
          }
        }, {
          start: {
            line: 521,
            column: 6
          },
          end: {
            line: 522,
            column: 57
          }
        }, {
          start: {
            line: 524,
            column: 6
          },
          end: {
            line: 525,
            column: 60
          }
        }],
        line: 502
      },
      "28": {
        loc: {
          start: {
            line: 562,
            column: 11
          },
          end: {
            line: 562,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 562,
            column: 11
          },
          end: {
            line: 562,
            column: 34
          }
        }, {
          start: {
            line: 562,
            column: 38
          },
          end: {
            line: 562,
            column: 44
          }
        }],
        line: 562
      },
      "29": {
        loc: {
          start: {
            line: 569,
            column: 4
          },
          end: {
            line: 569,
            column: 30
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 569,
            column: 21
          },
          end: {
            line: 569,
            column: 30
          }
        }],
        line: 569
      },
      "30": {
        loc: {
          start: {
            line: 572,
            column: 4
          },
          end: {
            line: 574,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 572,
            column: 4
          },
          end: {
            line: 574,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 572
      },
      "31": {
        loc: {
          start: {
            line: 586,
            column: 4
          },
          end: {
            line: 588,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 586,
            column: 4
          },
          end: {
            line: 588,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 586
      },
      "32": {
        loc: {
          start: {
            line: 620,
            column: 4
          },
          end: {
            line: 620,
            column: 50
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 620,
            column: 4
          },
          end: {
            line: 620,
            column: 50
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 620
      },
      "33": {
        loc: {
          start: {
            line: 629,
            column: 6
          },
          end: {
            line: 629,
            column: 32
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 629,
            column: 6
          },
          end: {
            line: 629,
            column: 32
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 629
      },
      "34": {
        loc: {
          start: {
            line: 651,
            column: 6
          },
          end: {
            line: 653,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 651,
            column: 6
          },
          end: {
            line: 653,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 651
      },
      "35": {
        loc: {
          start: {
            line: 659,
            column: 4
          },
          end: {
            line: 659,
            column: 30
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 659,
            column: 4
          },
          end: {
            line: 659,
            column: 30
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 659
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0,
      "130": 0,
      "131": 0,
      "132": 0,
      "133": 0,
      "134": 0,
      "135": 0,
      "136": 0,
      "137": 0,
      "138": 0,
      "139": 0,
      "140": 0,
      "141": 0,
      "142": 0,
      "143": 0,
      "144": 0,
      "145": 0,
      "146": 0,
      "147": 0,
      "148": 0,
      "149": 0,
      "150": 0,
      "151": 0,
      "152": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0, 0, 0, 0],
      "28": [0, 0],
      "29": [0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0],
      "34": [0, 0],
      "35": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "59cdbbeb4135e0c387607b38f0d9a4de1d9e0110"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_h66wr1861 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_h66wr1861();
import { performanceMonitor } from "../../utils/performance";
var EdgeFunctionManager = function () {
  function EdgeFunctionManager() {
    _classCallCheck(this, EdgeFunctionManager);
    this.edgeFunctions = (cov_h66wr1861().s[0]++, new Map());
    this.deploymentQueue = (cov_h66wr1861().s[1]++, []);
    this.executionCache = (cov_h66wr1861().s[2]++, new Map());
    this.performanceMetrics = (cov_h66wr1861().s[3]++, new Map());
    this.EDGE_REGIONS = (cov_h66wr1861().s[4]++, ['us-east-1', 'us-west-2', 'eu-west-1', 'eu-central-1', 'ap-southeast-1', 'ap-northeast-1', 'ap-south-1', 'sa-east-1', 'af-south-1', 'me-south-1']);
    this.PREDEFINED_FUNCTIONS = (cov_h66wr1861().s[5]++, [{
      id: 'auth_validator',
      name: 'Authentication Validator',
      description: 'Validates JWT tokens and API keys at edge',
      runtime: 'javascript',
      triggers: [{
        type: 'http',
        pattern: '/api/auth/*',
        method: 'POST',
        authentication: 'none'
      }],
      configuration: {
        timeout: 5000,
        memory: 128,
        environment: {},
        secrets: ['JWT_SECRET', 'API_KEY_SALT']
      }
    }, {
      id: 'image_optimizer',
      name: 'Image Optimizer',
      description: 'Optimizes images on-the-fly at edge locations',
      runtime: 'javascript',
      triggers: [{
        type: 'http',
        pattern: '/images/*',
        method: 'GET',
        authentication: 'none'
      }],
      configuration: {
        timeout: 10000,
        memory: 256,
        environment: {
          MAX_WIDTH: '2048',
          MAX_HEIGHT: '2048'
        },
        secrets: []
      }
    }, {
      id: 'api_aggregator',
      name: 'API Aggregator',
      description: 'Aggregates multiple API calls into single response',
      runtime: 'typescript',
      triggers: [{
        type: 'http',
        pattern: '/api/aggregate/*',
        method: 'POST',
        authentication: 'jwt'
      }],
      configuration: {
        timeout: 15000,
        memory: 512,
        environment: {},
        secrets: ['API_ENDPOINTS']
      }
    }, {
      id: 'performance_collector',
      name: 'Performance Data Collector',
      description: 'Collects and processes performance metrics',
      runtime: 'javascript',
      triggers: [{
        type: 'http',
        pattern: '/api/metrics',
        method: 'POST',
        authentication: 'api_key'
      }],
      configuration: {
        timeout: 3000,
        memory: 128,
        environment: {},
        secrets: ['METRICS_API_KEY']
      }
    }]);
    cov_h66wr1861().f[0]++;
    cov_h66wr1861().s[6]++;
    this.initializeEdgeFunctionManager();
  }
  return _createClass(EdgeFunctionManager, [{
    key: "initializeEdgeFunctionManager",
    value: (function () {
      var _initializeEdgeFunctionManager = _asyncToGenerator(function* () {
        cov_h66wr1861().f[1]++;
        cov_h66wr1861().s[7]++;
        try {
          cov_h66wr1861().s[8]++;
          yield this.deployPredefinedFunctions();
          cov_h66wr1861().s[9]++;
          this.startPerformanceMonitoring();
          cov_h66wr1861().s[10]++;
          this.startDeploymentProcessor();
          cov_h66wr1861().s[11]++;
          console.log('Edge Function Manager initialized successfully');
        } catch (error) {
          cov_h66wr1861().s[12]++;
          console.error('Failed to initialize Edge Function Manager:', error);
        }
      });
      function initializeEdgeFunctionManager() {
        return _initializeEdgeFunctionManager.apply(this, arguments);
      }
      return initializeEdgeFunctionManager;
    }())
  }, {
    key: "deployFunction",
    value: (function () {
      var _deployFunction = _asyncToGenerator(function* (functionDefinition, deployment) {
        cov_h66wr1861().f[2]++;
        cov_h66wr1861().s[13]++;
        try {
          var functionId = (cov_h66wr1861().s[14]++, (cov_h66wr1861().b[0][0]++, functionDefinition.id) || (cov_h66wr1861().b[0][1]++, this.generateFunctionId()));
          var edgeFunction = (cov_h66wr1861().s[15]++, {
            id: functionId,
            name: (cov_h66wr1861().b[1][0]++, functionDefinition.name) || (cov_h66wr1861().b[1][1]++, 'Unnamed Function'),
            description: (cov_h66wr1861().b[2][0]++, functionDefinition.description) || (cov_h66wr1861().b[2][1]++, ''),
            code: (cov_h66wr1861().b[3][0]++, functionDefinition.code) || (cov_h66wr1861().b[3][1]++, ''),
            runtime: (cov_h66wr1861().b[4][0]++, functionDefinition.runtime) || (cov_h66wr1861().b[4][1]++, 'javascript'),
            triggers: (cov_h66wr1861().b[5][0]++, functionDefinition.triggers) || (cov_h66wr1861().b[5][1]++, []),
            regions: deployment.regions,
            configuration: Object.assign({
              timeout: 10000,
              memory: 256,
              environment: {},
              secrets: []
            }, functionDefinition.configuration),
            performance: {
              averageExecutionTime: 0,
              successRate: 100,
              errorRate: 0,
              invocationsPerSecond: 0
            },
            deployment: {
              version: this.generateVersion(),
              deployedAt: Date.now(),
              status: 'deploying',
              rolloutPercentage: 0
            }
          });
          cov_h66wr1861().s[16]++;
          this.edgeFunctions.set(functionId, edgeFunction);
          var deploymentConfig = (cov_h66wr1861().s[17]++, Object.assign({
            functionId: functionId
          }, deployment));
          cov_h66wr1861().s[18]++;
          this.deploymentQueue.push(deploymentConfig);
          cov_h66wr1861().s[19]++;
          console.log(`Queued deployment for edge function: ${functionId}`);
          cov_h66wr1861().s[20]++;
          return functionId;
        } catch (error) {
          cov_h66wr1861().s[21]++;
          console.error('Failed to deploy edge function:', error);
          cov_h66wr1861().s[22]++;
          throw error;
        }
      });
      function deployFunction(_x, _x2) {
        return _deployFunction.apply(this, arguments);
      }
      return deployFunction;
    }())
  }, {
    key: "executeFunction",
    value: (function () {
      var _executeFunction = _asyncToGenerator(function* (request) {
        cov_h66wr1861().f[3]++;
        cov_h66wr1861().s[23]++;
        try {
          var startTime = (cov_h66wr1861().s[24]++, Date.now());
          var edgeFunction = (cov_h66wr1861().s[25]++, this.edgeFunctions.get(request.functionId));
          cov_h66wr1861().s[26]++;
          if (!edgeFunction) {
            cov_h66wr1861().b[6][0]++;
            cov_h66wr1861().s[27]++;
            throw new Error(`Edge function not found: ${request.functionId}`);
          } else {
            cov_h66wr1861().b[6][1]++;
          }
          var executionRegion = (cov_h66wr1861().s[28]++, yield this.selectOptimalRegion(request.region, edgeFunction.regions));
          var cacheKey = (cov_h66wr1861().s[29]++, this.generateCacheKey(request));
          var cachedResult = (cov_h66wr1861().s[30]++, this.executionCache.get(cacheKey));
          cov_h66wr1861().s[31]++;
          if ((cov_h66wr1861().b[8][0]++, cachedResult) && (cov_h66wr1861().b[8][1]++, this.isCacheValid(cachedResult))) {
            cov_h66wr1861().b[7][0]++;
            cov_h66wr1861().s[32]++;
            return {
              success: true,
              data: cachedResult.data,
              executionTime: Date.now() - startTime,
              region: executionRegion,
              functionVersion: edgeFunction.deployment.version,
              cacheStatus: 'hit'
            };
          } else {
            cov_h66wr1861().b[7][1]++;
          }
          var result = (cov_h66wr1861().s[33]++, yield this.executeInRegion(edgeFunction, request, executionRegion));
          cov_h66wr1861().s[34]++;
          if (this.shouldCacheResult(edgeFunction, result)) {
            cov_h66wr1861().b[9][0]++;
            cov_h66wr1861().s[35]++;
            this.executionCache.set(cacheKey, {
              data: result,
              timestamp: Date.now(),
              ttl: this.calculateCacheTTL(edgeFunction)
            });
          } else {
            cov_h66wr1861().b[9][1]++;
          }
          var executionTime = (cov_h66wr1861().s[36]++, Date.now() - startTime);
          cov_h66wr1861().s[37]++;
          this.trackFunctionPerformance(request.functionId, executionTime, true);
          cov_h66wr1861().s[38]++;
          performanceMonitor.trackDatabaseQuery('edge_function_execution', executionTime);
          cov_h66wr1861().s[39]++;
          return {
            success: true,
            data: result,
            executionTime: executionTime,
            region: executionRegion,
            functionVersion: edgeFunction.deployment.version,
            cacheStatus: cachedResult ? (cov_h66wr1861().b[10][0]++, 'stale') : (cov_h66wr1861().b[10][1]++, 'miss')
          };
        } catch (error) {
          var _executionTime = (cov_h66wr1861().s[40]++, Date.now() - Date.now());
          cov_h66wr1861().s[41]++;
          this.trackFunctionPerformance(request.functionId, _executionTime, false);
          cov_h66wr1861().s[42]++;
          return {
            success: false,
            data: null,
            executionTime: _executionTime,
            region: (cov_h66wr1861().b[11][0]++, request.region) || (cov_h66wr1861().b[11][1]++, 'unknown'),
            functionVersion: 'unknown',
            cacheStatus: 'bypass',
            error: {
              code: 'EXECUTION_ERROR',
              message: error instanceof Error ? (cov_h66wr1861().b[12][0]++, error.message) : (cov_h66wr1861().b[12][1]++, 'Unknown error'),
              stack: error instanceof Error ? (cov_h66wr1861().b[13][0]++, error.stack) : (cov_h66wr1861().b[13][1]++, undefined)
            }
          };
        }
      });
      function executeFunction(_x3) {
        return _executeFunction.apply(this, arguments);
      }
      return executeFunction;
    }())
  }, {
    key: "getFunctionMetrics",
    value: function getFunctionMetrics(functionId) {
      cov_h66wr1861().f[4]++;
      var edgeFunction = (cov_h66wr1861().s[43]++, this.edgeFunctions.get(functionId));
      var metrics = (cov_h66wr1861().s[44]++, (cov_h66wr1861().b[14][0]++, this.performanceMetrics.get(functionId)) || (cov_h66wr1861().b[14][1]++, []));
      cov_h66wr1861().s[45]++;
      if ((cov_h66wr1861().b[16][0]++, !edgeFunction) || (cov_h66wr1861().b[16][1]++, metrics.length === 0)) {
        cov_h66wr1861().b[15][0]++;
        cov_h66wr1861().s[46]++;
        return {
          averageExecutionTime: 0,
          successRate: 0,
          invocationsPerMinute: 0,
          regionalPerformance: {},
          errorBreakdown: {}
        };
      } else {
        cov_h66wr1861().b[15][1]++;
      }
      var recentMetrics = (cov_h66wr1861().s[47]++, metrics.slice(-100));
      var successfulExecutions = (cov_h66wr1861().s[48]++, recentMetrics.filter(function (m) {
        cov_h66wr1861().f[5]++;
        cov_h66wr1861().s[49]++;
        return m.success;
      }));
      var averageExecutionTime = (cov_h66wr1861().s[50]++, successfulExecutions.length > 0 ? (cov_h66wr1861().b[17][0]++, successfulExecutions.reduce(function (sum, m) {
        cov_h66wr1861().f[6]++;
        cov_h66wr1861().s[51]++;
        return sum + m.executionTime;
      }, 0) / successfulExecutions.length) : (cov_h66wr1861().b[17][1]++, 0));
      var successRate = (cov_h66wr1861().s[52]++, recentMetrics.length > 0 ? (cov_h66wr1861().b[18][0]++, successfulExecutions.length / recentMetrics.length * 100) : (cov_h66wr1861().b[18][1]++, 0));
      var oneMinuteAgo = (cov_h66wr1861().s[53]++, Date.now() - 60000);
      var recentInvocations = (cov_h66wr1861().s[54]++, metrics.filter(function (m) {
        cov_h66wr1861().f[7]++;
        cov_h66wr1861().s[55]++;
        return m.timestamp > oneMinuteAgo;
      }));
      var invocationsPerMinute = (cov_h66wr1861().s[56]++, recentInvocations.length);
      var regionalPerformance = (cov_h66wr1861().s[57]++, {});
      var regionGroups = (cov_h66wr1861().s[58]++, recentMetrics.reduce(function (groups, metric) {
        cov_h66wr1861().f[8]++;
        cov_h66wr1861().s[59]++;
        if (!groups[metric.region]) {
          cov_h66wr1861().b[19][0]++;
          cov_h66wr1861().s[60]++;
          groups[metric.region] = [];
        } else {
          cov_h66wr1861().b[19][1]++;
        }
        cov_h66wr1861().s[61]++;
        groups[metric.region].push(metric);
        cov_h66wr1861().s[62]++;
        return groups;
      }, {}));
      cov_h66wr1861().s[63]++;
      Object.entries(regionGroups).forEach(function (_ref) {
        var _ref2 = _slicedToArray(_ref, 2),
          region = _ref2[0],
          regionMetrics = _ref2[1];
        cov_h66wr1861().f[9]++;
        var avgTime = (cov_h66wr1861().s[64]++, regionMetrics.reduce(function (sum, m) {
          cov_h66wr1861().f[10]++;
          cov_h66wr1861().s[65]++;
          return sum + m.executionTime;
        }, 0) / regionMetrics.length);
        cov_h66wr1861().s[66]++;
        regionalPerformance[region] = avgTime;
      });
      var errorBreakdown = (cov_h66wr1861().s[67]++, {});
      var failedExecutions = (cov_h66wr1861().s[68]++, recentMetrics.filter(function (m) {
        cov_h66wr1861().f[11]++;
        cov_h66wr1861().s[69]++;
        return !m.success;
      }));
      cov_h66wr1861().s[70]++;
      failedExecutions.forEach(function (execution) {
        cov_h66wr1861().f[12]++;
        var errorType = (cov_h66wr1861().s[71]++, (cov_h66wr1861().b[20][0]++, execution.error) || (cov_h66wr1861().b[20][1]++, 'unknown'));
        cov_h66wr1861().s[72]++;
        errorBreakdown[errorType] = ((cov_h66wr1861().b[21][0]++, errorBreakdown[errorType]) || (cov_h66wr1861().b[21][1]++, 0)) + 1;
      });
      cov_h66wr1861().s[73]++;
      return {
        averageExecutionTime: averageExecutionTime,
        successRate: successRate,
        invocationsPerMinute: invocationsPerMinute,
        regionalPerformance: regionalPerformance,
        errorBreakdown: errorBreakdown
      };
    }
  }, {
    key: "getDeployedFunctions",
    value: function getDeployedFunctions() {
      cov_h66wr1861().f[13]++;
      cov_h66wr1861().s[74]++;
      return Array.from(this.edgeFunctions.values()).filter(function (func) {
        cov_h66wr1861().f[14]++;
        cov_h66wr1861().s[75]++;
        return func.deployment.status === 'deployed';
      });
    }
  }, {
    key: "updateFunctionConfig",
    value: (function () {
      var _updateFunctionConfig = _asyncToGenerator(function* (functionId, config) {
        cov_h66wr1861().f[15]++;
        var edgeFunction = (cov_h66wr1861().s[76]++, this.edgeFunctions.get(functionId));
        cov_h66wr1861().s[77]++;
        if (!edgeFunction) {
          cov_h66wr1861().b[22][0]++;
          cov_h66wr1861().s[78]++;
          throw new Error(`Function not found: ${functionId}`);
        } else {
          cov_h66wr1861().b[22][1]++;
        }
        cov_h66wr1861().s[79]++;
        edgeFunction.configuration = Object.assign({}, edgeFunction.configuration, config);
        cov_h66wr1861().s[80]++;
        yield this.redeployFunction(functionId);
      });
      function updateFunctionConfig(_x4, _x5) {
        return _updateFunctionConfig.apply(this, arguments);
      }
      return updateFunctionConfig;
    }())
  }, {
    key: "deployPredefinedFunctions",
    value: function () {
      var _deployPredefinedFunctions = _asyncToGenerator(function* () {
        cov_h66wr1861().f[16]++;
        cov_h66wr1861().s[81]++;
        for (var funcDef of this.PREDEFINED_FUNCTIONS) {
          cov_h66wr1861().s[82]++;
          try {
            cov_h66wr1861().s[83]++;
            yield this.deployFunction(funcDef, {
              regions: ['us-east-1', 'eu-west-1', 'ap-southeast-1'],
              strategy: 'rolling',
              rolloutConfig: {
                percentage: 100,
                duration: 300000,
                healthChecks: true,
                rollbackOnError: true
              }
            });
          } catch (error) {
            cov_h66wr1861().s[84]++;
            console.error(`Failed to deploy predefined function ${funcDef.id}:`, error);
          }
        }
      });
      function deployPredefinedFunctions() {
        return _deployPredefinedFunctions.apply(this, arguments);
      }
      return deployPredefinedFunctions;
    }()
  }, {
    key: "selectOptimalRegion",
    value: function () {
      var _selectOptimalRegion = _asyncToGenerator(function* (preferredRegion) {
        var _sortedRegions$;
        var availableRegions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_h66wr1861().b[23][0]++, this.EDGE_REGIONS);
        cov_h66wr1861().f[17]++;
        cov_h66wr1861().s[85]++;
        if ((cov_h66wr1861().b[25][0]++, preferredRegion) && (cov_h66wr1861().b[25][1]++, availableRegions.includes(preferredRegion))) {
          cov_h66wr1861().b[24][0]++;
          cov_h66wr1861().s[86]++;
          return preferredRegion;
        } else {
          cov_h66wr1861().b[24][1]++;
        }
        var regionPerformance = (cov_h66wr1861().s[87]++, new Map());
        cov_h66wr1861().s[88]++;
        for (var region of availableRegions) {
          var latency = (cov_h66wr1861().s[89]++, Math.random() * 100 + 50);
          cov_h66wr1861().s[90]++;
          regionPerformance.set(region, latency);
        }
        var sortedRegions = (cov_h66wr1861().s[91]++, Array.from(regionPerformance.entries()).sort(function (a, b) {
          cov_h66wr1861().f[18]++;
          cov_h66wr1861().s[92]++;
          return a[1] - b[1];
        }));
        cov_h66wr1861().s[93]++;
        return (cov_h66wr1861().b[26][0]++, (_sortedRegions$ = sortedRegions[0]) == null ? void 0 : _sortedRegions$[0]) || (cov_h66wr1861().b[26][1]++, availableRegions[0]);
      });
      function selectOptimalRegion(_x6) {
        return _selectOptimalRegion.apply(this, arguments);
      }
      return selectOptimalRegion;
    }()
  }, {
    key: "executeInRegion",
    value: function () {
      var _executeInRegion = _asyncToGenerator(function* (edgeFunction, request, region) {
        cov_h66wr1861().f[19]++;
        var executionTime = (cov_h66wr1861().s[94]++, Math.random() * 100 + 10);
        cov_h66wr1861().s[95]++;
        yield new Promise(function (resolve) {
          cov_h66wr1861().f[20]++;
          cov_h66wr1861().s[96]++;
          return setTimeout(resolve, executionTime);
        });
        cov_h66wr1861().s[97]++;
        switch (edgeFunction.id) {
          case 'auth_validator':
            cov_h66wr1861().b[27][0]++;
            cov_h66wr1861().s[98]++;
            return {
              valid: true,
              userId: 'user123',
              permissions: ['read', 'write']
            };
          case 'image_optimizer':
            cov_h66wr1861().b[27][1]++;
            cov_h66wr1861().s[99]++;
            return {
              optimizedUrl: 'https://optimized.example.com/image.webp',
              originalSize: 1024000,
              optimizedSize: 512000,
              compressionRatio: 0.5
            };
          case 'api_aggregator':
            cov_h66wr1861().b[27][2]++;
            cov_h66wr1861().s[100]++;
            return {
              userProfile: {
                id: 'user123',
                name: 'John Doe'
              },
              recentActivity: [{
                type: 'training',
                timestamp: Date.now()
              }],
              stats: {
                totalSessions: 42,
                averageScore: 85
              }
            };
          case 'performance_collector':
            cov_h66wr1861().b[27][3]++;
            cov_h66wr1861().s[101]++;
            return {
              received: true,
              processed: Date.now()
            };
          default:
            cov_h66wr1861().b[27][4]++;
            cov_h66wr1861().s[102]++;
            return {
              result: 'success',
              data: request.payload
            };
        }
      });
      function executeInRegion(_x7, _x8, _x9) {
        return _executeInRegion.apply(this, arguments);
      }
      return executeInRegion;
    }()
  }, {
    key: "generateFunctionId",
    value: function generateFunctionId() {
      cov_h66wr1861().f[21]++;
      cov_h66wr1861().s[103]++;
      return `edge_func_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateVersion",
    value: function generateVersion() {
      cov_h66wr1861().f[22]++;
      cov_h66wr1861().s[104]++;
      return `v${Date.now()}`;
    }
  }, {
    key: "generateCacheKey",
    value: function generateCacheKey(request) {
      cov_h66wr1861().f[23]++;
      var payload = (cov_h66wr1861().s[105]++, JSON.stringify(request.payload));
      cov_h66wr1861().s[106]++;
      return `${request.functionId}_${btoa(payload).slice(0, 20)}`;
    }
  }, {
    key: "isCacheValid",
    value: function isCacheValid(cachedResult) {
      cov_h66wr1861().f[24]++;
      var age = (cov_h66wr1861().s[107]++, Date.now() - cachedResult.timestamp);
      cov_h66wr1861().s[108]++;
      return age < cachedResult.ttl;
    }
  }, {
    key: "shouldCacheResult",
    value: function shouldCacheResult(edgeFunction, result) {
      cov_h66wr1861().f[25]++;
      var cacheableTypes = (cov_h66wr1861().s[109]++, ['auth_validator', 'image_optimizer']);
      cov_h66wr1861().s[110]++;
      return cacheableTypes.includes(edgeFunction.id);
    }
  }, {
    key: "calculateCacheTTL",
    value: function calculateCacheTTL(edgeFunction) {
      cov_h66wr1861().f[26]++;
      var ttlMap = (cov_h66wr1861().s[111]++, {
        'auth_validator': 300000,
        'image_optimizer': 3600000,
        'api_aggregator': 60000,
        'performance_collector': 0
      });
      cov_h66wr1861().s[112]++;
      return (cov_h66wr1861().b[28][0]++, ttlMap[edgeFunction.id]) || (cov_h66wr1861().b[28][1]++, 300000);
    }
  }, {
    key: "trackFunctionPerformance",
    value: function trackFunctionPerformance(functionId, executionTime, success) {
      var region = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : (cov_h66wr1861().b[29][0]++, 'unknown');
      var error = arguments.length > 4 ? arguments[4] : undefined;
      cov_h66wr1861().f[27]++;
      cov_h66wr1861().s[113]++;
      if (!this.performanceMetrics.has(functionId)) {
        cov_h66wr1861().b[30][0]++;
        cov_h66wr1861().s[114]++;
        this.performanceMetrics.set(functionId, []);
      } else {
        cov_h66wr1861().b[30][1]++;
      }
      var metrics = (cov_h66wr1861().s[115]++, this.performanceMetrics.get(functionId));
      cov_h66wr1861().s[116]++;
      metrics.push({
        timestamp: Date.now(),
        executionTime: executionTime,
        success: success,
        region: region,
        error: error
      });
      cov_h66wr1861().s[117]++;
      if (metrics.length > 1000) {
        cov_h66wr1861().b[31][0]++;
        cov_h66wr1861().s[118]++;
        metrics.splice(0, 100);
      } else {
        cov_h66wr1861().b[31][1]++;
      }
    }
  }, {
    key: "startPerformanceMonitoring",
    value: function startPerformanceMonitoring() {
      var _this = this;
      cov_h66wr1861().f[28]++;
      cov_h66wr1861().s[119]++;
      setInterval(function () {
        cov_h66wr1861().f[29]++;
        cov_h66wr1861().s[120]++;
        _this.updateFunctionPerformanceMetrics();
      }, 60000);
    }
  }, {
    key: "updateFunctionPerformanceMetrics",
    value: function updateFunctionPerformanceMetrics() {
      cov_h66wr1861().f[30]++;
      cov_h66wr1861().s[121]++;
      for (var _ref3 of this.edgeFunctions.entries()) {
        var _ref4 = _slicedToArray(_ref3, 2);
        var functionId = _ref4[0];
        var edgeFunction = _ref4[1];
        var metrics = (cov_h66wr1861().s[122]++, this.getFunctionMetrics(functionId));
        cov_h66wr1861().s[123]++;
        edgeFunction.performance = {
          averageExecutionTime: metrics.averageExecutionTime,
          successRate: metrics.successRate,
          errorRate: 100 - metrics.successRate,
          invocationsPerSecond: metrics.invocationsPerMinute / 60
        };
      }
    }
  }, {
    key: "startDeploymentProcessor",
    value: function startDeploymentProcessor() {
      var _this2 = this;
      cov_h66wr1861().f[31]++;
      cov_h66wr1861().s[124]++;
      setInterval(function () {
        cov_h66wr1861().f[32]++;
        cov_h66wr1861().s[125]++;
        _this2.processDeploymentQueue();
      }, 30000);
    }
  }, {
    key: "processDeploymentQueue",
    value: function () {
      var _processDeploymentQueue = _asyncToGenerator(function* () {
        cov_h66wr1861().f[33]++;
        cov_h66wr1861().s[126]++;
        if (this.deploymentQueue.length === 0) {
          cov_h66wr1861().b[32][0]++;
          cov_h66wr1861().s[127]++;
          return;
        } else {
          cov_h66wr1861().b[32][1]++;
        }
        var deployment = (cov_h66wr1861().s[128]++, this.deploymentQueue.shift());
        cov_h66wr1861().s[129]++;
        yield this.executeDeployment(deployment);
      });
      function processDeploymentQueue() {
        return _processDeploymentQueue.apply(this, arguments);
      }
      return processDeploymentQueue;
    }()
  }, {
    key: "executeDeployment",
    value: function () {
      var _executeDeployment = _asyncToGenerator(function* (deployment) {
        cov_h66wr1861().f[34]++;
        cov_h66wr1861().s[130]++;
        try {
          var edgeFunction = (cov_h66wr1861().s[131]++, this.edgeFunctions.get(deployment.functionId));
          cov_h66wr1861().s[132]++;
          if (!edgeFunction) {
            cov_h66wr1861().b[33][0]++;
            cov_h66wr1861().s[133]++;
            return;
          } else {
            cov_h66wr1861().b[33][1]++;
          }
          cov_h66wr1861().s[134]++;
          console.log(`Deploying function ${deployment.functionId} to ${deployment.regions.length} regions`);
          cov_h66wr1861().s[135]++;
          edgeFunction.deployment.status = 'deploying';
          cov_h66wr1861().s[136]++;
          for (var percentage = (cov_h66wr1861().s[137]++, 0); percentage <= 100; percentage += 25) {
            cov_h66wr1861().s[138]++;
            edgeFunction.deployment.rolloutPercentage = percentage;
            cov_h66wr1861().s[139]++;
            yield new Promise(function (resolve) {
              cov_h66wr1861().f[35]++;
              cov_h66wr1861().s[140]++;
              return setTimeout(resolve, 1000);
            });
          }
          cov_h66wr1861().s[141]++;
          edgeFunction.deployment.status = 'deployed';
          cov_h66wr1861().s[142]++;
          edgeFunction.deployment.rolloutPercentage = 100;
          cov_h66wr1861().s[143]++;
          console.log(`Successfully deployed function ${deployment.functionId}`);
        } catch (error) {
          cov_h66wr1861().s[144]++;
          console.error(`Failed to deploy function ${deployment.functionId}:`, error);
          var _edgeFunction = (cov_h66wr1861().s[145]++, this.edgeFunctions.get(deployment.functionId));
          cov_h66wr1861().s[146]++;
          if (_edgeFunction) {
            cov_h66wr1861().b[34][0]++;
            cov_h66wr1861().s[147]++;
            _edgeFunction.deployment.status = 'failed';
          } else {
            cov_h66wr1861().b[34][1]++;
          }
        }
      });
      function executeDeployment(_x0) {
        return _executeDeployment.apply(this, arguments);
      }
      return executeDeployment;
    }()
  }, {
    key: "redeployFunction",
    value: function () {
      var _redeployFunction = _asyncToGenerator(function* (functionId) {
        cov_h66wr1861().f[36]++;
        var edgeFunction = (cov_h66wr1861().s[148]++, this.edgeFunctions.get(functionId));
        cov_h66wr1861().s[149]++;
        if (!edgeFunction) {
          cov_h66wr1861().b[35][0]++;
          cov_h66wr1861().s[150]++;
          return;
        } else {
          cov_h66wr1861().b[35][1]++;
        }
        cov_h66wr1861().s[151]++;
        this.deploymentQueue.push({
          functionId: functionId,
          regions: edgeFunction.regions,
          strategy: 'rolling',
          rolloutConfig: {
            percentage: 100,
            duration: 180000,
            healthChecks: true,
            rollbackOnError: true
          }
        });
      });
      function redeployFunction(_x1) {
        return _redeployFunction.apply(this, arguments);
      }
      return redeployFunction;
    }()
  }]);
}();
export var edgeFunctionManager = (cov_h66wr1861().s[152]++, new EdgeFunctionManager());
export default edgeFunctionManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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