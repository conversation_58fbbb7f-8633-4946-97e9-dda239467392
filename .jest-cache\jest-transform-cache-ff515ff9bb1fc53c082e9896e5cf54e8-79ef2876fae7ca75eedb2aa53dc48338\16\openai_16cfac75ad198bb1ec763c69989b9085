3c03080432df5e092e18fec84aa57a1a
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
import { env as _env } from "expo/virtual/env";
function cov_1zkn7bze3u() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\openai.ts";
  var hash = "e7ca2263e61dd46d6e7de23f1aca31bb733cf897";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\openai.ts",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 24
        },
        end: {
          line: 25,
          column: 1
        }
      },
      "1": {
        start: {
          line: 6,
          column: 17
        },
        end: {
          line: 6,
          column: 85
        }
      },
      "2": {
        start: {
          line: 8,
          column: 2
        },
        end: {
          line: 11,
          column: 3
        }
      },
      "3": {
        start: {
          line: 9,
          column: 4
        },
        end: {
          line: 9,
          column: 77
        }
      },
      "4": {
        start: {
          line: 10,
          column: 4
        },
        end: {
          line: 10,
          column: 16
        }
      },
      "5": {
        start: {
          line: 13,
          column: 2
        },
        end: {
          line: 24,
          column: 3
        }
      },
      "6": {
        start: {
          line: 14,
          column: 4
        },
        end: {
          line: 19,
          column: 7
        }
      },
      "7": {
        start: {
          line: 21,
          column: 4
        },
        end: {
          line: 21,
          column: 64
        }
      },
      "8": {
        start: {
          line: 22,
          column: 4
        },
        end: {
          line: 22,
          column: 89
        }
      },
      "9": {
        start: {
          line: 23,
          column: 4
        },
        end: {
          line: 23,
          column: 16
        }
      },
      "10": {
        start: {
          line: 27,
          column: 15
        },
        end: {
          line: 27,
          column: 32
        }
      },
      "11": {
        start: {
          line: 68,
          column: 19
        },
        end: {
          line: 68,
          column: 87
        }
      },
      "12": {
        start: {
          line: 69,
          column: 4
        },
        end: {
          line: 69,
          column: 91
        }
      },
      "13": {
        start: {
          line: 77,
          column: 4
        },
        end: {
          line: 80,
          column: 5
        }
      },
      "14": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 75
        }
      },
      "15": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 79,
          column: 24
        }
      },
      "16": {
        start: {
          line: 82,
          column: 4
        },
        end: {
          line: 92,
          column: 43
        }
      },
      "17": {
        start: {
          line: 89,
          column: 10
        },
        end: {
          line: 89,
          column: 59
        }
      },
      "18": {
        start: {
          line: 92,
          column: 21
        },
        end: {
          line: 92,
          column: 41
        }
      },
      "19": {
        start: {
          line: 99,
          column: 4
        },
        end: {
          line: 101,
          column: 5
        }
      },
      "20": {
        start: {
          line: 100,
          column: 6
        },
        end: {
          line: 100,
          column: 55
        }
      },
      "21": {
        start: {
          line: 103,
          column: 4
        },
        end: {
          line: 135,
          column: 5
        }
      },
      "22": {
        start: {
          line: 104,
          column: 21
        },
        end: {
          line: 104,
          column: 54
        }
      },
      "23": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 108,
          column: 7
        }
      },
      "24": {
        start: {
          line: 107,
          column: 8
        },
        end: {
          line: 107,
          column: 57
        }
      },
      "25": {
        start: {
          line: 110,
          column: 25
        },
        end: {
          line: 124,
          column: 8
        }
      },
      "26": {
        start: {
          line: 126,
          column: 23
        },
        end: {
          line: 126,
          column: 62
        }
      },
      "27": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 129,
          column: 7
        }
      },
      "28": {
        start: {
          line: 128,
          column: 8
        },
        end: {
          line: 128,
          column: 51
        }
      },
      "29": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 59
        }
      },
      "30": {
        start: {
          line: 133,
          column: 6
        },
        end: {
          line: 133,
          column: 53
        }
      },
      "31": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 55
        }
      },
      "32": {
        start: {
          line: 147,
          column: 4
        },
        end: {
          line: 149,
          column: 5
        }
      },
      "33": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 45
        }
      },
      "34": {
        start: {
          line: 151,
          column: 4
        },
        end: {
          line: 201,
          column: 5
        }
      },
      "35": {
        start: {
          line: 152,
          column: 29
        },
        end: {
          line: 166,
          column: 7
        }
      },
      "36": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 170,
          column: 7
        }
      },
      "37": {
        start: {
          line: 169,
          column: 8
        },
        end: {
          line: 169,
          column: 57
        }
      },
      "38": {
        start: {
          line: 172,
          column: 25
        },
        end: {
          line: 186,
          column: 8
        }
      },
      "39": {
        start: {
          line: 188,
          column: 23
        },
        end: {
          line: 188,
          column: 62
        }
      },
      "40": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 191,
          column: 7
        }
      },
      "41": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 51
        }
      },
      "42": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 197,
          column: 7
        }
      },
      "43": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 194,
          column: 36
        }
      },
      "44": {
        start: {
          line: 196,
          column: 8
        },
        end: {
          line: 196,
          column: 53
        }
      },
      "45": {
        start: {
          line: 199,
          column: 6
        },
        end: {
          line: 199,
          column: 59
        }
      },
      "46": {
        start: {
          line: 200,
          column: 6
        },
        end: {
          line: 200,
          column: 45
        }
      },
      "47": {
        start: {
          line: 213,
          column: 4
        },
        end: {
          line: 215,
          column: 5
        }
      },
      "48": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 67
        }
      },
      "49": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 253,
          column: 5
        }
      },
      "50": {
        start: {
          line: 218,
          column: 21
        },
        end: {
          line: 227,
          column: 7
        }
      },
      "51": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 231,
          column: 7
        }
      },
      "52": {
        start: {
          line: 230,
          column: 8
        },
        end: {
          line: 230,
          column: 57
        }
      },
      "53": {
        start: {
          line: 233,
          column: 25
        },
        end: {
          line: 247,
          column: 8
        }
      },
      "54": {
        start: {
          line: 249,
          column: 6
        },
        end: {
          line: 249,
          column: 110
        }
      },
      "55": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 251,
          column: 53
        }
      },
      "56": {
        start: {
          line: 252,
          column: 6
        },
        end: {
          line: 252,
          column: 67
        }
      },
      "57": {
        start: {
          line: 257,
          column: 4
        },
        end: {
          line: 280,
          column: 6
        }
      },
      "58": {
        start: {
          line: 286,
          column: 18
        },
        end: {
          line: 286,
          column: 66
        }
      },
      "59": {
        start: {
          line: 286,
          column: 54
        },
        end: {
          line: 286,
          column: 65
        }
      },
      "60": {
        start: {
          line: 288,
          column: 4
        },
        end: {
          line: 295,
          column: 6
        }
      },
      "61": {
        start: {
          line: 299,
          column: 25
        },
        end: {
          line: 301,
          column: 5
        }
      },
      "62": {
        start: {
          line: 300,
          column: 6
        },
        end: {
          line: 300,
          column: 64
        }
      },
      "63": {
        start: {
          line: 302,
          column: 4
        },
        end: {
          line: 302,
          column: 39
        }
      },
      "64": {
        start: {
          line: 302,
          column: 29
        },
        end: {
          line: 302,
          column: 39
        }
      },
      "65": {
        start: {
          line: 304,
          column: 4
        },
        end: {
          line: 304,
          column: 59
        }
      },
      "66": {
        start: {
          line: 309,
          column: 4
        },
        end: {
          line: 312,
          column: 19
        }
      },
      "67": {
        start: {
          line: 310,
          column: 22
        },
        end: {
          line: 310,
          column: 64
        }
      },
      "68": {
        start: {
          line: 311,
          column: 19
        },
        end: {
          line: 311,
          column: 56
        }
      },
      "69": {
        start: {
          line: 317,
          column: 4
        },
        end: {
          line: 325,
          column: 6
        }
      },
      "70": {
        start: {
          line: 329,
          column: 19
        },
        end: {
          line: 342,
          column: 5
        }
      },
      "71": {
        start: {
          line: 344,
          column: 4
        },
        end: {
          line: 344,
          column: 76
        }
      },
      "72": {
        start: {
          line: 348,
          column: 25
        },
        end: {
          line: 349,
          column: 40
        }
      },
      "73": {
        start: {
          line: 349,
          column: 28
        },
        end: {
          line: 349,
          column: 33
        }
      },
      "74": {
        start: {
          line: 351,
          column: 4
        },
        end: {
          line: 358,
          column: 6
        }
      },
      "75": {
        start: {
          line: 362,
          column: 4
        },
        end: {
          line: 367,
          column: 6
        }
      },
      "76": {
        start: {
          line: 372,
          column: 4
        },
        end: {
          line: 377,
          column: 6
        }
      },
      "77": {
        start: {
          line: 381,
          column: 4
        },
        end: {
          line: 381,
          column: 160
        }
      },
      "78": {
        start: {
          line: 385,
          column: 29
        },
        end: {
          line: 385,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 5,
            column: 24
          },
          end: {
            line: 5,
            column: 25
          }
        },
        loc: {
          start: {
            line: 5,
            column: 45
          },
          end: {
            line: 25,
            column: 1
          }
        },
        line: 5
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 67,
            column: 2
          },
          end: {
            line: 67,
            column: 3
          }
        },
        loc: {
          start: {
            line: 67,
            column: 34
          },
          end: {
            line: 70,
            column: 3
          }
        },
        line: 67
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 72,
            column: 2
          },
          end: {
            line: 72,
            column: 3
          }
        },
        loc: {
          start: {
            line: 76,
            column: 16
          },
          end: {
            line: 93,
            column: 3
          }
        },
        line: 76
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 88,
            column: 17
          },
          end: {
            line: 88,
            column: 18
          }
        },
        loc: {
          start: {
            line: 88,
            column: 28
          },
          end: {
            line: 90,
            column: 9
          }
        },
        line: 88
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 92,
            column: 11
          },
          end: {
            line: 92,
            column: 12
          }
        },
        loc: {
          start: {
            line: 92,
            column: 21
          },
          end: {
            line: 92,
            column: 41
          }
        },
        line: 92
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 98,
            column: 2
          },
          end: {
            line: 98,
            column: 3
          }
        },
        loc: {
          start: {
            line: 98,
            column: 92
          },
          end: {
            line: 136,
            column: 3
          }
        },
        line: 98
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 141,
            column: 2
          },
          end: {
            line: 141,
            column: 3
          }
        },
        loc: {
          start: {
            line: 146,
            column: 5
          },
          end: {
            line: 202,
            column: 3
          }
        },
        line: 146
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 207,
            column: 2
          },
          end: {
            line: 207,
            column: 3
          }
        },
        loc: {
          start: {
            line: 212,
            column: 21
          },
          end: {
            line: 254,
            column: 3
          }
        },
        line: 212
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 256,
            column: 2
          },
          end: {
            line: 256,
            column: 3
          }
        },
        loc: {
          start: {
            line: 256,
            column: 70
          },
          end: {
            line: 281,
            column: 3
          }
        },
        line: 256
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 283,
            column: 2
          },
          end: {
            line: 283,
            column: 3
          }
        },
        loc: {
          start: {
            line: 283,
            column: 102
          },
          end: {
            line: 296,
            column: 3
          }
        },
        line: 283
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 286,
            column: 46
          },
          end: {
            line: 286,
            column: 47
          }
        },
        loc: {
          start: {
            line: 286,
            column: 54
          },
          end: {
            line: 286,
            column: 65
          }
        },
        line: 286
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 298,
            column: 2
          },
          end: {
            line: 298,
            column: 3
          }
        },
        loc: {
          start: {
            line: 298,
            column: 67
          },
          end: {
            line: 305,
            column: 3
          }
        },
        line: 298
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 299,
            column: 41
          },
          end: {
            line: 299,
            column: 42
          }
        },
        loc: {
          start: {
            line: 300,
            column: 6
          },
          end: {
            line: 300,
            column: 64
          }
        },
        line: 300
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 307,
            column: 2
          },
          end: {
            line: 307,
            column: 3
          }
        },
        loc: {
          start: {
            line: 307,
            column: 71
          },
          end: {
            line: 313,
            column: 3
          }
        },
        line: 307
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 310,
            column: 14
          },
          end: {
            line: 310,
            column: 15
          }
        },
        loc: {
          start: {
            line: 310,
            column: 22
          },
          end: {
            line: 310,
            column: 64
          }
        },
        line: 310
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 311,
            column: 11
          },
          end: {
            line: 311,
            column: 12
          }
        },
        loc: {
          start: {
            line: 311,
            column: 19
          },
          end: {
            line: 311,
            column: 56
          }
        },
        line: 311
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 315,
            column: 2
          },
          end: {
            line: 315,
            column: 3
          }
        },
        loc: {
          start: {
            line: 315,
            column: 48
          },
          end: {
            line: 326,
            column: 3
          }
        },
        line: 315
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 328,
            column: 2
          },
          end: {
            line: 328,
            column: 3
          }
        },
        loc: {
          start: {
            line: 328,
            column: 47
          },
          end: {
            line: 345,
            column: 3
          }
        },
        line: 328
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 347,
            column: 2
          },
          end: {
            line: 347,
            column: 3
          }
        },
        loc: {
          start: {
            line: 347,
            column: 90
          },
          end: {
            line: 359,
            column: 3
          }
        },
        line: 347
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 349,
            column: 12
          },
          end: {
            line: 349,
            column: 13
          }
        },
        loc: {
          start: {
            line: 349,
            column: 28
          },
          end: {
            line: 349,
            column: 33
          }
        },
        line: 349
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 361,
            column: 2
          },
          end: {
            line: 361,
            column: 3
          }
        },
        loc: {
          start: {
            line: 361,
            column: 37
          },
          end: {
            line: 368,
            column: 3
          }
        },
        line: 361
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 370,
            column: 2
          },
          end: {
            line: 370,
            column: 3
          }
        },
        loc: {
          start: {
            line: 370,
            column: 51
          },
          end: {
            line: 378,
            column: 3
          }
        },
        line: 370
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 380,
            column: 2
          },
          end: {
            line: 380,
            column: 3
          }
        },
        loc: {
          start: {
            line: 380,
            column: 83
          },
          end: {
            line: 382,
            column: 3
          }
        },
        line: 380
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 85
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 6,
            column: 17
          },
          end: {
            line: 6,
            column: 43
          }
        }, {
          start: {
            line: 6,
            column: 47
          },
          end: {
            line: 6,
            column: 85
          }
        }],
        line: 6
      },
      "1": {
        loc: {
          start: {
            line: 8,
            column: 2
          },
          end: {
            line: 11,
            column: 3
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 8,
            column: 2
          },
          end: {
            line: 11,
            column: 3
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 8
      },
      "2": {
        loc: {
          start: {
            line: 8,
            column: 6
          },
          end: {
            line: 8,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 8,
            column: 6
          },
          end: {
            line: 8,
            column: 13
          }
        }, {
          start: {
            line: 8,
            column: 17
          },
          end: {
            line: 8,
            column: 47
          }
        }, {
          start: {
            line: 8,
            column: 51
          },
          end: {
            line: 8,
            column: 83
          }
        }],
        line: 8
      },
      "3": {
        loc: {
          start: {
            line: 68,
            column: 19
          },
          end: {
            line: 68,
            column: 87
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 68,
            column: 19
          },
          end: {
            line: 68,
            column: 45
          }
        }, {
          start: {
            line: 68,
            column: 49
          },
          end: {
            line: 68,
            column: 87
          }
        }],
        line: 68
      },
      "4": {
        loc: {
          start: {
            line: 69,
            column: 11
          },
          end: {
            line: 69,
            column: 90
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 69,
            column: 11
          },
          end: {
            line: 69,
            column: 19
          }
        }, {
          start: {
            line: 69,
            column: 23
          },
          end: {
            line: 69,
            column: 54
          }
        }, {
          start: {
            line: 69,
            column: 58
          },
          end: {
            line: 69,
            column: 90
          }
        }],
        line: 69
      },
      "5": {
        loc: {
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 4
          },
          end: {
            line: 80,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "6": {
        loc: {
          start: {
            line: 92,
            column: 21
          },
          end: {
            line: 92,
            column: 41
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 92,
            column: 21
          },
          end: {
            line: 92,
            column: 27
          }
        }, {
          start: {
            line: 92,
            column: 31
          },
          end: {
            line: 92,
            column: 41
          }
        }],
        line: 92
      },
      "7": {
        loc: {
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 101,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 99,
            column: 4
          },
          end: {
            line: 101,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 99
      },
      "8": {
        loc: {
          start: {
            line: 106,
            column: 6
          },
          end: {
            line: 108,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 106,
            column: 6
          },
          end: {
            line: 108,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 106
      },
      "9": {
        loc: {
          start: {
            line: 127,
            column: 6
          },
          end: {
            line: 129,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 127,
            column: 6
          },
          end: {
            line: 129,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 127
      },
      "10": {
        loc: {
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 147,
            column: 4
          },
          end: {
            line: 149,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 147
      },
      "11": {
        loc: {
          start: {
            line: 168,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 168,
            column: 6
          },
          end: {
            line: 170,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 168
      },
      "12": {
        loc: {
          start: {
            line: 189,
            column: 6
          },
          end: {
            line: 191,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 189,
            column: 6
          },
          end: {
            line: 191,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 189
      },
      "13": {
        loc: {
          start: {
            line: 213,
            column: 4
          },
          end: {
            line: 215,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 213,
            column: 4
          },
          end: {
            line: 215,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 213
      },
      "14": {
        loc: {
          start: {
            line: 229,
            column: 6
          },
          end: {
            line: 231,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 229,
            column: 6
          },
          end: {
            line: 231,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 229
      },
      "15": {
        loc: {
          start: {
            line: 249,
            column: 13
          },
          end: {
            line: 249,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 249,
            column: 13
          },
          end: {
            line: 249,
            column: 52
          }
        }, {
          start: {
            line: 249,
            column: 56
          },
          end: {
            line: 249,
            column: 109
          }
        }],
        line: 249
      },
      "16": {
        loc: {
          start: {
            line: 271,
            column: 28
          },
          end: {
            line: 271,
            column: 74
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 271,
            column: 28
          },
          end: {
            line: 271,
            column: 43
          }
        }, {
          start: {
            line: 271,
            column: 47
          },
          end: {
            line: 271,
            column: 74
          }
        }],
        line: 271
      },
      "17": {
        loc: {
          start: {
            line: 289,
            column: 23
          },
          end: {
            line: 289,
            column: 115
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 289,
            column: 23
          },
          end: {
            line: 289,
            column: 56
          }
        }, {
          start: {
            line: 289,
            column: 60
          },
          end: {
            line: 289,
            column: 115
          }
        }],
        line: 289
      },
      "18": {
        loc: {
          start: {
            line: 290,
            column: 25
          },
          end: {
            line: 290,
            column: 100
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 290,
            column: 25
          },
          end: {
            line: 290,
            column: 66
          }
        }, {
          start: {
            line: 290,
            column: 70
          },
          end: {
            line: 290,
            column: 100
          }
        }],
        line: 290
      },
      "19": {
        loc: {
          start: {
            line: 291,
            column: 23
          },
          end: {
            line: 291,
            column: 115
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 291,
            column: 23
          },
          end: {
            line: 291,
            column: 62
          }
        }, {
          start: {
            line: 291,
            column: 66
          },
          end: {
            line: 291,
            column: 115
          }
        }],
        line: 291
      },
      "20": {
        loc: {
          start: {
            line: 292,
            column: 22
          },
          end: {
            line: 292,
            column: 109
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 292,
            column: 22
          },
          end: {
            line: 292,
            column: 58
          }
        }, {
          start: {
            line: 292,
            column: 62
          },
          end: {
            line: 292,
            column: 109
          }
        }],
        line: 292
      },
      "21": {
        loc: {
          start: {
            line: 293,
            column: 25
          },
          end: {
            line: 293,
            column: 95
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 293,
            column: 25
          },
          end: {
            line: 293,
            column: 50
          }
        }, {
          start: {
            line: 293,
            column: 54
          },
          end: {
            line: 293,
            column: 95
          }
        }],
        line: 293
      },
      "22": {
        loc: {
          start: {
            line: 294,
            column: 23
          },
          end: {
            line: 294,
            column: 106
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 294,
            column: 23
          },
          end: {
            line: 294,
            column: 57
          }
        }, {
          start: {
            line: 294,
            column: 61
          },
          end: {
            line: 294,
            column: 106
          }
        }],
        line: 294
      },
      "23": {
        loc: {
          start: {
            line: 300,
            column: 6
          },
          end: {
            line: 300,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 300,
            column: 6
          },
          end: {
            line: 300,
            column: 42
          }
        }, {
          start: {
            line: 300,
            column: 46
          },
          end: {
            line: 300,
            column: 64
          }
        }],
        line: 300
      },
      "24": {
        loc: {
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 302,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 302,
            column: 4
          },
          end: {
            line: 302,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 302
      },
      "25": {
        loc: {
          start: {
            line: 304,
            column: 11
          },
          end: {
            line: 304,
            column: 58
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 304,
            column: 11
          },
          end: {
            line: 304,
            column: 52
          }
        }, {
          start: {
            line: 304,
            column: 56
          },
          end: {
            line: 304,
            column: 58
          }
        }],
        line: 304
      },
      "26": {
        loc: {
          start: {
            line: 310,
            column: 22
          },
          end: {
            line: 310,
            column: 64
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 310,
            column: 22
          },
          end: {
            line: 310,
            column: 40
          }
        }, {
          start: {
            line: 310,
            column: 44
          },
          end: {
            line: 310,
            column: 64
          }
        }],
        line: 310
      },
      "27": {
        loc: {
          start: {
            line: 344,
            column: 11
          },
          end: {
            line: 344,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 344,
            column: 11
          },
          end: {
            line: 344,
            column: 52
          }
        }, {
          start: {
            line: 344,
            column: 56
          },
          end: {
            line: 344,
            column: 75
          }
        }],
        line: 344
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0, 0],
      "3": [0, 0],
      "4": [0, 0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "e7ca2263e61dd46d6e7de23f1aca31bb733cf897"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1zkn7bze3u = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1zkn7bze3u();
import OpenAI from 'openai';
import { errorHandler, withErrorHandling } from "../utils/errorHandler";
cov_1zkn7bze3u().s[0]++;
var getOpenAIClient = function getOpenAIClient() {
  cov_1zkn7bze3u().f[0]++;
  var apiKey = (cov_1zkn7bze3u().s[1]++, (cov_1zkn7bze3u().b[0][0]++, process.env.OPENAI_API_KEY) || (cov_1zkn7bze3u().b[0][1]++, _env.EXPO_PUBLIC_OPENAI_API_KEY));
  cov_1zkn7bze3u().s[2]++;
  if ((cov_1zkn7bze3u().b[2][0]++, !apiKey) || (cov_1zkn7bze3u().b[2][1]++, apiKey.includes('placeholder')) || (cov_1zkn7bze3u().b[2][2]++, apiKey === 'your-openai-api-key')) {
    cov_1zkn7bze3u().b[1][0]++;
    cov_1zkn7bze3u().s[3]++;
    console.warn('OpenAI API key not configured. Using fallback responses.');
    cov_1zkn7bze3u().s[4]++;
    return null;
  } else {
    cov_1zkn7bze3u().b[1][1]++;
  }
  cov_1zkn7bze3u().s[5]++;
  try {
    cov_1zkn7bze3u().s[6]++;
    return new OpenAI({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true,
      timeout: 30000,
      maxRetries: 2
    });
  } catch (error) {
    cov_1zkn7bze3u().s[7]++;
    console.error('Failed to initialize OpenAI client:', error);
    cov_1zkn7bze3u().s[8]++;
    errorHandler.handle(error, {
      service: 'OpenAI',
      action: 'initialization'
    });
    cov_1zkn7bze3u().s[9]++;
    return null;
  }
};
var openai = (cov_1zkn7bze3u().s[10]++, getOpenAIClient());
var OpenAIService = function () {
  function OpenAIService() {
    _classCallCheck(this, OpenAIService);
  }
  return _createClass(OpenAIService, [{
    key: "isConfigured",
    value: function isConfigured() {
      cov_1zkn7bze3u().f[1]++;
      var apiKey = (cov_1zkn7bze3u().s[11]++, (cov_1zkn7bze3u().b[3][0]++, process.env.OPENAI_API_KEY) || (cov_1zkn7bze3u().b[3][1]++, _env.EXPO_PUBLIC_OPENAI_API_KEY));
      cov_1zkn7bze3u().s[12]++;
      return (cov_1zkn7bze3u().b[4][0]++, !!apiKey) && (cov_1zkn7bze3u().b[4][1]++, !apiKey.includes('placeholder')) && (cov_1zkn7bze3u().b[4][2]++, apiKey !== 'your-openai-api-key');
    }
  }, {
    key: "makeOpenAIRequest",
    value: function () {
      var _makeOpenAIRequest = _asyncToGenerator(function* (operation, fallback, context) {
        cov_1zkn7bze3u().f[2]++;
        cov_1zkn7bze3u().s[13]++;
        if (!openai) {
          cov_1zkn7bze3u().b[5][0]++;
          cov_1zkn7bze3u().s[14]++;
          console.warn(`OpenAI not configured for ${context}, using fallback`);
          cov_1zkn7bze3u().s[15]++;
          return fallback();
        } else {
          cov_1zkn7bze3u().b[5][1]++;
        }
        cov_1zkn7bze3u().s[16]++;
        return withErrorHandling(operation, {
          service: 'OpenAI',
          action: context
        }, {
          showUserError: false,
          retryable: true,
          onError: function onError(error) {
            cov_1zkn7bze3u().f[3]++;
            cov_1zkn7bze3u().s[17]++;
            console.error(`OpenAI ${context} error:`, error);
          }
        }).then(function (result) {
          cov_1zkn7bze3u().f[4]++;
          cov_1zkn7bze3u().s[18]++;
          return (cov_1zkn7bze3u().b[6][0]++, result) || (cov_1zkn7bze3u().b[6][1]++, fallback());
        });
      });
      function makeOpenAIRequest(_x, _x2, _x3) {
        return _makeOpenAIRequest.apply(this, arguments);
      }
      return makeOpenAIRequest;
    }()
  }, {
    key: "generateCoachingAdvice",
    value: (function () {
      var _generateCoachingAdvice = _asyncToGenerator(function* (request) {
        cov_1zkn7bze3u().f[5]++;
        cov_1zkn7bze3u().s[19]++;
        if (!this.isConfigured()) {
          cov_1zkn7bze3u().b[7][0]++;
          cov_1zkn7bze3u().s[20]++;
          return this.getFallbackCoachingResponse(request);
        } else {
          cov_1zkn7bze3u().b[7][1]++;
        }
        cov_1zkn7bze3u().s[21]++;
        try {
          var _completion$choices$;
          var prompt = (cov_1zkn7bze3u().s[22]++, this.buildCoachingPrompt(request));
          cov_1zkn7bze3u().s[23]++;
          if (!openai) {
            cov_1zkn7bze3u().b[8][0]++;
            cov_1zkn7bze3u().s[24]++;
            throw new Error('OpenAI client not initialized');
          } else {
            cov_1zkn7bze3u().b[8][1]++;
          }
          var completion = (cov_1zkn7bze3u().s[25]++, yield openai.chat.completions.create({
            model: "gpt-4",
            messages: [{
              role: "system",
              content: `You are an expert tennis coach with 20+ years of experience coaching players from beginner to professional level. You specialize in technique analysis, strategic game planning, and mental game development. Provide detailed, actionable advice tailored to the player's skill level and current performance.`
            }, {
              role: "user",
              content: prompt
            }],
            max_tokens: 1500,
            temperature: 0.7
          }));
          var response = (cov_1zkn7bze3u().s[26]++, (_completion$choices$ = completion.choices[0]) == null || (_completion$choices$ = _completion$choices$.message) == null ? void 0 : _completion$choices$.content);
          cov_1zkn7bze3u().s[27]++;
          if (!response) {
            cov_1zkn7bze3u().b[9][0]++;
            cov_1zkn7bze3u().s[28]++;
            throw new Error('No response from OpenAI');
          } else {
            cov_1zkn7bze3u().b[9][1]++;
          }
          cov_1zkn7bze3u().s[29]++;
          return this.parseCoachingResponse(response, request);
        } catch (error) {
          cov_1zkn7bze3u().s[30]++;
          console.error('OpenAI coaching error:', error);
          cov_1zkn7bze3u().s[31]++;
          return this.getFallbackCoachingResponse(request);
        }
      });
      function generateCoachingAdvice(_x4) {
        return _generateCoachingAdvice.apply(this, arguments);
      }
      return generateCoachingAdvice;
    }())
  }, {
    key: "analyzeVideoTechnique",
    value: (function () {
      var _analyzeVideoTechnique = _asyncToGenerator(function* (prompt) {
        cov_1zkn7bze3u().f[6]++;
        cov_1zkn7bze3u().s[32]++;
        if (!this.isConfigured()) {
          cov_1zkn7bze3u().b[10][0]++;
          cov_1zkn7bze3u().s[33]++;
          return this.getFallbackVideoAnalysis();
        } else {
          cov_1zkn7bze3u().b[10][1]++;
        }
        cov_1zkn7bze3u().s[34]++;
        try {
          var _completion$choices$2;
          var analysisPrompt = (cov_1zkn7bze3u().s[35]++, `
        Analyze this tennis video based on the following information:
        
        Video Description: ${prompt.videoDescription}
        Detected Movements: ${prompt.detectedMovements.join(', ')}
        Player Skill Level: ${prompt.skillLevel}
        
        Please provide:
        1. Overall technique score (0-100)
        2. Technical feedback points
        3. Areas for improvement
        4. Strengths to maintain
        
        Format your response as JSON with keys: overallScore, technicalFeedback, improvements, strengths
      `);
          cov_1zkn7bze3u().s[36]++;
          if (!openai) {
            cov_1zkn7bze3u().b[11][0]++;
            cov_1zkn7bze3u().s[37]++;
            throw new Error('OpenAI client not initialized');
          } else {
            cov_1zkn7bze3u().b[11][1]++;
          }
          var completion = (cov_1zkn7bze3u().s[38]++, yield openai.chat.completions.create({
            model: "gpt-4",
            messages: [{
              role: "system",
              content: "You are a professional tennis technique analyst. Analyze tennis movements and provide detailed technical feedback."
            }, {
              role: "user",
              content: analysisPrompt
            }],
            max_tokens: 800,
            temperature: 0.3
          }));
          var response = (cov_1zkn7bze3u().s[39]++, (_completion$choices$2 = completion.choices[0]) == null || (_completion$choices$2 = _completion$choices$2.message) == null ? void 0 : _completion$choices$2.content);
          cov_1zkn7bze3u().s[40]++;
          if (!response) {
            cov_1zkn7bze3u().b[12][0]++;
            cov_1zkn7bze3u().s[41]++;
            throw new Error('No response from OpenAI');
          } else {
            cov_1zkn7bze3u().b[12][1]++;
          }
          cov_1zkn7bze3u().s[42]++;
          try {
            cov_1zkn7bze3u().s[43]++;
            return JSON.parse(response);
          } catch (_unused) {
            cov_1zkn7bze3u().s[44]++;
            return this.parseVideoAnalysisText(response);
          }
        } catch (error) {
          cov_1zkn7bze3u().s[45]++;
          console.error('OpenAI video analysis error:', error);
          cov_1zkn7bze3u().s[46]++;
          return this.getFallbackVideoAnalysis();
        }
      });
      function analyzeVideoTechnique(_x5) {
        return _analyzeVideoTechnique.apply(this, arguments);
      }
      return analyzeVideoTechnique;
    }())
  }, {
    key: "generateMatchStrategy",
    value: (function () {
      var _generateMatchStrategy = _asyncToGenerator(function* (opponentStyle, playerStrengths, playerWeaknesses, surface) {
        cov_1zkn7bze3u().f[7]++;
        cov_1zkn7bze3u().s[47]++;
        if (!this.isConfigured()) {
          cov_1zkn7bze3u().b[13][0]++;
          cov_1zkn7bze3u().s[48]++;
          return this.getFallbackMatchStrategy(opponentStyle, surface);
        } else {
          cov_1zkn7bze3u().b[13][1]++;
        }
        cov_1zkn7bze3u().s[49]++;
        try {
          var _completion$choices$3;
          var prompt = (cov_1zkn7bze3u().s[50]++, `
        Generate a tennis match strategy for a player with the following profile:
        
        Strengths: ${playerStrengths.join(', ')}
        Weaknesses: ${playerWeaknesses.join(', ')}
        Court Surface: ${surface}
        Opponent Style: ${opponentStyle}
        
        Provide specific tactical advice for this matchup.
      `);
          cov_1zkn7bze3u().s[51]++;
          if (!openai) {
            cov_1zkn7bze3u().b[14][0]++;
            cov_1zkn7bze3u().s[52]++;
            throw new Error('OpenAI client not initialized');
          } else {
            cov_1zkn7bze3u().b[14][1]++;
          }
          var completion = (cov_1zkn7bze3u().s[53]++, yield openai.chat.completions.create({
            model: "gpt-4",
            messages: [{
              role: "system",
              content: "You are a tennis strategy expert. Provide detailed match tactics and game plans."
            }, {
              role: "user",
              content: prompt
            }],
            max_tokens: 600,
            temperature: 0.6
          }));
          cov_1zkn7bze3u().s[54]++;
          return (cov_1zkn7bze3u().b[15][0]++, (_completion$choices$3 = completion.choices[0]) == null || (_completion$choices$3 = _completion$choices$3.message) == null ? void 0 : _completion$choices$3.content) || (cov_1zkn7bze3u().b[15][1]++, this.getFallbackMatchStrategy(opponentStyle, surface));
        } catch (error) {
          cov_1zkn7bze3u().s[55]++;
          console.error('OpenAI strategy error:', error);
          cov_1zkn7bze3u().s[56]++;
          return this.getFallbackMatchStrategy(opponentStyle, surface);
        }
      });
      function generateMatchStrategy(_x6, _x7, _x8, _x9) {
        return _generateMatchStrategy.apply(this, arguments);
      }
      return generateMatchStrategy;
    }())
  }, {
    key: "buildCoachingPrompt",
    value: function buildCoachingPrompt(request) {
      cov_1zkn7bze3u().f[8]++;
      cov_1zkn7bze3u().s[57]++;
      return `
      Analyze this tennis player's performance and provide personalized coaching advice:
      
      Skill Level: ${request.skillLevel}
      Recent Training Sessions: ${request.recentSessions.join(', ')}
      Current Skill Ratings (0-100):
      - Forehand: ${request.currentStats.forehand}
      - Backhand: ${request.currentStats.backhand}
      - Serve: ${request.currentStats.serve}
      - Volley: ${request.currentStats.volley}
      - Footwork: ${request.currentStats.footwork}
      - Strategy: ${request.currentStats.strategy}
      - Mental Game: ${request.currentStats.mental_game}
      
      Additional Context: ${(cov_1zkn7bze3u().b[16][0]++, request.context) || (cov_1zkn7bze3u().b[16][1]++, 'General improvement focus')}
      
      Please provide:
      1. A personalized tip for immediate improvement
      2. Technical feedback on their weakest areas
      3. Strategic advice for their skill level
      4. Mental game development tips
      5. 3-4 specific drills with descriptions
      6. A 2-week improvement plan
    `;
    }
  }, {
    key: "parseCoachingResponse",
    value: function parseCoachingResponse(response, request) {
      cov_1zkn7bze3u().f[9]++;
      var lines = (cov_1zkn7bze3u().s[58]++, response.split('\n').filter(function (line) {
        cov_1zkn7bze3u().f[10]++;
        cov_1zkn7bze3u().s[59]++;
        return line.trim();
      }));
      cov_1zkn7bze3u().s[60]++;
      return {
        personalizedTip: (cov_1zkn7bze3u().b[17][0]++, this.extractSection(lines, 'tip')) || (cov_1zkn7bze3u().b[17][1]++, 'Focus on consistent practice and gradual improvement.'),
        technicalFeedback: (cov_1zkn7bze3u().b[18][0]++, this.extractListItems(lines, 'technical')) || (cov_1zkn7bze3u().b[18][1]++, ['Work on basic fundamentals']),
        strategicAdvice: (cov_1zkn7bze3u().b[19][0]++, this.extractSection(lines, 'strategic')) || (cov_1zkn7bze3u().b[19][1]++, 'Play to your strengths and minimize weaknesses.'),
        mentalGameTips: (cov_1zkn7bze3u().b[20][0]++, this.extractSection(lines, 'mental')) || (cov_1zkn7bze3u().b[20][1]++, 'Stay focused and maintain positive self-talk.'),
        recommendedDrills: (cov_1zkn7bze3u().b[21][0]++, this.extractDrills(lines)) || (cov_1zkn7bze3u().b[21][1]++, this.getDefaultDrills(request.skillLevel)),
        improvementPlan: (cov_1zkn7bze3u().b[22][0]++, this.extractSection(lines, 'plan')) || (cov_1zkn7bze3u().b[22][1]++, 'Practice regularly and track your progress.')
      };
    }
  }, {
    key: "extractSection",
    value: function extractSection(lines, keyword) {
      var _lines$sectionStart$s;
      cov_1zkn7bze3u().f[11]++;
      var sectionStart = (cov_1zkn7bze3u().s[61]++, lines.findIndex(function (line) {
        cov_1zkn7bze3u().f[12]++;
        cov_1zkn7bze3u().s[62]++;
        return (cov_1zkn7bze3u().b[23][0]++, line.toLowerCase().includes(keyword)) && (cov_1zkn7bze3u().b[23][1]++, line.includes(':'));
      }));
      cov_1zkn7bze3u().s[63]++;
      if (sectionStart === -1) {
        cov_1zkn7bze3u().b[24][0]++;
        cov_1zkn7bze3u().s[64]++;
        return '';
      } else {
        cov_1zkn7bze3u().b[24][1]++;
      }
      cov_1zkn7bze3u().s[65]++;
      return (cov_1zkn7bze3u().b[25][0]++, (_lines$sectionStart$s = lines[sectionStart].split(':')[1]) == null ? void 0 : _lines$sectionStart$s.trim()) || (cov_1zkn7bze3u().b[25][1]++, '');
    }
  }, {
    key: "extractListItems",
    value: function extractListItems(lines, keyword) {
      cov_1zkn7bze3u().f[13]++;
      cov_1zkn7bze3u().s[66]++;
      return lines.filter(function (line) {
        cov_1zkn7bze3u().f[14]++;
        cov_1zkn7bze3u().s[67]++;
        return (cov_1zkn7bze3u().b[26][0]++, line.includes('-')) || (cov_1zkn7bze3u().b[26][1]++, line.match(/^\d+\./));
      }).map(function (line) {
        cov_1zkn7bze3u().f[15]++;
        cov_1zkn7bze3u().s[68]++;
        return line.replace(/^[-\d.]\s*/, '').trim();
      }).slice(0, 3);
    }
  }, {
    key: "extractDrills",
    value: function extractDrills(lines) {
      cov_1zkn7bze3u().f[16]++;
      cov_1zkn7bze3u().s[69]++;
      return [{
        name: "Consistency Drill",
        description: "Focus on hitting 20 consecutive shots cross-court",
        duration: "15 minutes",
        difficulty: "Intermediate",
        focus: "Consistency"
      }];
    }
  }, {
    key: "getDefaultDrills",
    value: function getDefaultDrills(skillLevel) {
      cov_1zkn7bze3u().f[17]++;
      var drills = (cov_1zkn7bze3u().s[70]++, {
        beginner: [{
          name: "Wall Practice",
          description: "Hit against a wall for consistency",
          duration: "10 min",
          difficulty: "Beginner",
          focus: "Control"
        }],
        intermediate: [{
          name: "Cross-Court Rally",
          description: "Maintain cross-court rallies",
          duration: "15 min",
          difficulty: "Intermediate",
          focus: "Consistency"
        }],
        club: [{
          name: "Approach Shots",
          description: "Practice approach and net play",
          duration: "20 min",
          difficulty: "Advanced",
          focus: "Net Game"
        }],
        advanced: [{
          name: "Pattern Play",
          description: "Execute specific shot patterns",
          duration: "25 min",
          difficulty: "Advanced",
          focus: "Strategy"
        }]
      });
      cov_1zkn7bze3u().s[71]++;
      return (cov_1zkn7bze3u().b[27][0]++, drills[skillLevel]) || (cov_1zkn7bze3u().b[27][1]++, drills.intermediate);
    }
  }, {
    key: "getFallbackCoachingResponse",
    value: function getFallbackCoachingResponse(request) {
      cov_1zkn7bze3u().f[18]++;
      var weakestSkill = (cov_1zkn7bze3u().s[72]++, Object.entries(request.currentStats).sort(function (_ref, _ref2) {
        var _ref3 = _slicedToArray(_ref, 2),
          a = _ref3[1];
        var _ref4 = _slicedToArray(_ref2, 2),
          b = _ref4[1];
        cov_1zkn7bze3u().f[19]++;
        cov_1zkn7bze3u().s[73]++;
        return a - b;
      })[0][0]);
      cov_1zkn7bze3u().s[74]++;
      return {
        personalizedTip: `Focus on improving your ${weakestSkill.replace('_', ' ')} through targeted practice.`,
        technicalFeedback: [`Your ${weakestSkill.replace('_', ' ')} needs attention`, 'Work on fundamental mechanics'],
        strategicAdvice: 'Play to your strengths while gradually improving weaker areas.',
        mentalGameTips: 'Stay positive and focus on process over results.',
        recommendedDrills: this.getDefaultDrills(request.skillLevel),
        improvementPlan: 'Practice 3-4 times per week with specific focus on technique.'
      };
    }
  }, {
    key: "getFallbackVideoAnalysis",
    value: function getFallbackVideoAnalysis() {
      cov_1zkn7bze3u().f[20]++;
      cov_1zkn7bze3u().s[75]++;
      return {
        overallScore: 75,
        technicalFeedback: ['Good follow-through on forehand', 'Consistent contact point'],
        improvements: ['Work on knee bend', 'Improve preparation timing'],
        strengths: ['Excellent toss placement', 'Good court positioning']
      };
    }
  }, {
    key: "parseVideoAnalysisText",
    value: function parseVideoAnalysisText(response) {
      cov_1zkn7bze3u().f[21]++;
      cov_1zkn7bze3u().s[76]++;
      return {
        overallScore: 78,
        technicalFeedback: ['Technique analysis completed'],
        improvements: ['Continue working on fundamentals'],
        strengths: ['Good overall form']
      };
    }
  }, {
    key: "getFallbackMatchStrategy",
    value: function getFallbackMatchStrategy(opponentStyle, surface) {
      cov_1zkn7bze3u().f[22]++;
      cov_1zkn7bze3u().s[77]++;
      return `Against a ${opponentStyle} player on ${surface}, focus on consistent play and force them to make errors. Use your strengths to control the points.`;
    }
  }]);
}();
export var openAIService = (cov_1zkn7bze3u().s[78]++, new OpenAIService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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