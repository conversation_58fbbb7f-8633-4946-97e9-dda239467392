{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_extends2", "_objectWithoutPropertiesLoose2", "React", "_View", "_StyleSheet", "_canUseDom", "_excluded", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "props", "forwardedRef", "active", "children", "onRequestClose", "transparent", "rest", "useEffect", "closeOnEscape", "e", "key", "stopPropagation", "document", "addEventListener", "removeEventListener", "style", "useMemo", "styles", "modal", "modalTransparent", "modalOpaque", "createElement", "ref", "role", "container", "create", "position", "top", "right", "bottom", "left", "backgroundColor", "flex", "_default", "module"], "sources": ["ModalContent.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _extends2 = _interopRequireDefault(require(\"@babel/runtime/helpers/extends\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _View = _interopRequireDefault(require(\"../View\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _canUseDom = _interopRequireDefault(require(\"../../modules/canUseDom\"));\nvar _excluded = [\"active\", \"children\", \"onRequestClose\", \"transparent\"];\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nvar ModalContent = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var active = props.active,\n    children = props.children,\n    onRequestClose = props.onRequestClose,\n    transparent = props.transparent,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  React.useEffect(() => {\n    if (_canUseDom.default) {\n      var closeOnEscape = e => {\n        if (active && e.key === 'Escape') {\n          e.stopPropagation();\n          if (onRequestClose) {\n            onRequestClose();\n          }\n        }\n      };\n      document.addEventListener('keyup', closeOnEscape, false);\n      return () => document.removeEventListener('keyup', closeOnEscape, false);\n    }\n  }, [active, onRequestClose]);\n  var style = React.useMemo(() => {\n    return [styles.modal, transparent ? styles.modalTransparent : styles.modalOpaque];\n  }, [transparent]);\n  return /*#__PURE__*/React.createElement(_View.default, (0, _extends2.default)({}, rest, {\n    \"aria-modal\": true,\n    ref: forwardedRef,\n    role: active ? 'dialog' : null,\n    style: style\n  }), /*#__PURE__*/React.createElement(_View.default, {\n    style: styles.container\n  }, children));\n});\nvar styles = _StyleSheet.default.create({\n  modal: {\n    position: 'fixed',\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  },\n  modalTransparent: {\n    backgroundColor: 'transparent'\n  },\n  modalOpaque: {\n    backgroundColor: 'white'\n  },\n  container: {\n    top: 0,\n    flex: 1\n  }\n});\nvar _default = exports.default = ModalContent;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,SAAS,GAAGN,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AACjF,IAAIM,8BAA8B,GAAGP,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIO,KAAK,GAAGL,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIQ,KAAK,GAAGT,sBAAsB,CAACC,OAAO,UAAU,CAAC,CAAC;AACtD,IAAIS,WAAW,GAAGV,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIU,UAAU,GAAGX,sBAAsB,CAACC,OAAO,0BAA0B,CAAC,CAAC;AAC3E,IAAIW,SAAS,GAAG,CAAC,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,CAAC;AAUvE,IAAIC,YAAY,GAAgBL,KAAK,CAACM,UAAU,CAAC,UAACC,KAAK,EAAEC,YAAY,EAAK;EACxE,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACvBC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,cAAc,GAAGJ,KAAK,CAACI,cAAc;IACrCC,WAAW,GAAGL,KAAK,CAACK,WAAW;IAC/BC,IAAI,GAAG,CAAC,CAAC,EAAEd,8BAA8B,CAACL,OAAO,EAAEa,KAAK,EAAEH,SAAS,CAAC;EACtEJ,KAAK,CAACc,SAAS,CAAC,YAAM;IACpB,IAAIX,UAAU,CAACT,OAAO,EAAE;MACtB,IAAIqB,aAAa,GAAG,SAAhBA,aAAaA,CAAGC,CAAC,EAAI;QACvB,IAAIP,MAAM,IAAIO,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;UAChCD,CAAC,CAACE,eAAe,CAAC,CAAC;UACnB,IAAIP,cAAc,EAAE;YAClBA,cAAc,CAAC,CAAC;UAClB;QACF;MACF,CAAC;MACDQ,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAEL,aAAa,EAAE,KAAK,CAAC;MACxD,OAAO;QAAA,OAAMI,QAAQ,CAACE,mBAAmB,CAAC,OAAO,EAAEN,aAAa,EAAE,KAAK,CAAC;MAAA;IAC1E;EACF,CAAC,EAAE,CAACN,MAAM,EAAEE,cAAc,CAAC,CAAC;EAC5B,IAAIW,KAAK,GAAGtB,KAAK,CAACuB,OAAO,CAAC,YAAM;IAC9B,OAAO,CAACC,MAAM,CAACC,KAAK,EAAEb,WAAW,GAAGY,MAAM,CAACE,gBAAgB,GAAGF,MAAM,CAACG,WAAW,CAAC;EACnF,CAAC,EAAE,CAACf,WAAW,CAAC,CAAC;EACjB,OAAoBZ,KAAK,CAAC4B,aAAa,CAAC3B,KAAK,CAACP,OAAO,EAAE,CAAC,CAAC,EAAEI,SAAS,CAACJ,OAAO,EAAE,CAAC,CAAC,EAAEmB,IAAI,EAAE;IACtF,YAAY,EAAE,IAAI;IAClBgB,GAAG,EAAErB,YAAY;IACjBsB,IAAI,EAAErB,MAAM,GAAG,QAAQ,GAAG,IAAI;IAC9Ba,KAAK,EAAEA;EACT,CAAC,CAAC,EAAetB,KAAK,CAAC4B,aAAa,CAAC3B,KAAK,CAACP,OAAO,EAAE;IAClD4B,KAAK,EAAEE,MAAM,CAACO;EAChB,CAAC,EAAErB,QAAQ,CAAC,CAAC;AACf,CAAC,CAAC;AACF,IAAIc,MAAM,GAAGtB,WAAW,CAACR,OAAO,CAACsC,MAAM,CAAC;EACtCP,KAAK,EAAE;IACLQ,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC;EACDX,gBAAgB,EAAE;IAChBY,eAAe,EAAE;EACnB,CAAC;EACDX,WAAW,EAAE;IACXW,eAAe,EAAE;EACnB,CAAC;EACDP,SAAS,EAAE;IACTG,GAAG,EAAE,CAAC;IACNK,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AACF,IAAIC,QAAQ,GAAG5C,OAAO,CAACF,OAAO,GAAGW,YAAY;AAC7CoC,MAAM,CAAC7C,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}