927bb1bf7700d6dbe47ed29b82932df6
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchRecordingService = void 0;
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _VideoRecordingService = require("../video/VideoRecordingService");
var _MatchRepository = require("../database/MatchRepository");
var _FileUploadService = require("../storage/FileUploadService");
var _performance = require("../../../utils/performance");
var MatchRecordingService = function () {
  function MatchRecordingService() {
    (0, _classCallCheck2.default)(this, MatchRecordingService);
    this.currentSession = null;
    this.sessionListeners = [];
    this.scoreListeners = [];
  }
  return (0, _createClass2.default)(MatchRecordingService, [{
    key: "startMatch",
    value: (function () {
      var _startMatch = (0, _asyncToGenerator2.default)(function* (metadata, options) {
        try {
          _performance.performanceMonitor.start('match_recording_start');
          this.validateMatchMetadata(metadata);
          if (this.currentSession) {
            throw new Error('Another match recording is already in progress');
          }
          var matchRecording = {
            id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            metadata: Object.assign({}, metadata, {
              startTime: new Date().toISOString()
            }),
            score: this.initializeScore(metadata.matchFormat),
            statistics: this.initializeStatistics(metadata.userId),
            status: 'recording',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          var session = {
            id: this.generateSessionId(),
            match: matchRecording,
            currentSet: 1,
            currentGame: 1,
            isRecording: true,
            isPaused: false,
            startTime: Date.now(),
            pausedTime: 0,
            totalPausedDuration: 0,
            videoRecordingActive: options.enableVideoRecording,
            autoScoreDetection: options.enableAutoScoreDetection
          };
          if (options.enableVideoRecording) {
            yield _VideoRecordingService.videoRecordingService.startRecording(options.videoConfig);
          }
          var savedMatch = yield this.saveMatchToDatabase(matchRecording);
          if (!savedMatch.success) {
            throw new Error(savedMatch.error || 'Failed to save match to database');
          }
          session.match.id = savedMatch.data.id;
          session.match.databaseId = savedMatch.data.databaseId;
          this.setupOfflineSync(session.match.id);
          this.currentSession = session;
          this.notifySessionListeners();
          this.startAutoSave();
          _performance.performanceMonitor.end('match_recording_start');
          return session;
        } catch (error) {
          console.error('Failed to start match recording:', error);
          if (this.currentSession) {
            yield this.cleanupFailedSession();
          }
          throw error;
        }
      });
      function startMatch(_x, _x2) {
        return _startMatch.apply(this, arguments);
      }
      return startMatch;
    }())
  }, {
    key: "addPoint",
    value: (function () {
      var _addPoint = (0, _asyncToGenerator2.default)(function* (winner) {
        var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'normal';
        var shotType = arguments.length > 2 ? arguments[2] : undefined;
        var courtPosition = arguments.length > 3 ? arguments[3] : undefined;
        if (!this.currentSession) {
          throw new Error('No active match session');
        }
        try {
          var session = this.currentSession;
          var currentSet = session.currentSet;
          var currentGame = session.currentGame;
          var gameEvent = {
            id: this.generateEventId(),
            timestamp: Date.now(),
            eventType: eventType === 'normal' ? 'point_won' : eventType,
            player: winner,
            shotType: shotType,
            courtPosition: courtPosition,
            description: `Point won by ${winner}`
          };
          var updatedScore = this.updateScore(session.match.score, currentSet, currentGame, winner, gameEvent);
          this.updateStatistics(session.match.statistics, gameEvent);
          session.match.score = updatedScore;
          session.match.updatedAt = new Date().toISOString();
          var setComplete = this.isSetComplete(updatedScore.sets[currentSet - 1]);
          var matchComplete = this.isMatchComplete(updatedScore, session.match.metadata.matchFormat);
          if (setComplete && !matchComplete) {
            session.currentSet++;
            session.currentGame = 1;
          } else if (!setComplete) {
            var gameComplete = this.isGameComplete(updatedScore.sets[currentSet - 1], currentGame);
            if (gameComplete) {
              session.currentGame++;
            }
          }
          if (matchComplete) {
            yield this.endMatch();
          } else {
            yield this.updateMatchInDatabase(session.match);
          }
          this.notifyScoreListeners();
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to add point:', error);
          throw error;
        }
      });
      function addPoint(_x3) {
        return _addPoint.apply(this, arguments);
      }
      return addPoint;
    }())
  }, {
    key: "pauseMatch",
    value: (function () {
      var _pauseMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession || this.currentSession.isPaused) {
          return;
        }
        try {
          this.currentSession.isPaused = true;
          this.currentSession.pausedTime = Date.now();
          this.currentSession.match.status = 'paused';
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.pauseRecording();
          }
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to pause match:', error);
          throw error;
        }
      });
      function pauseMatch() {
        return _pauseMatch.apply(this, arguments);
      }
      return pauseMatch;
    }())
  }, {
    key: "resumeMatch",
    value: (function () {
      var _resumeMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession || !this.currentSession.isPaused) {
          return;
        }
        try {
          var pauseDuration = Date.now() - this.currentSession.pausedTime;
          this.currentSession.totalPausedDuration += pauseDuration;
          this.currentSession.isPaused = false;
          this.currentSession.pausedTime = 0;
          this.currentSession.match.status = 'recording';
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.resumeRecording();
          }
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to resume match:', error);
          throw error;
        }
      });
      function resumeMatch() {
        return _resumeMatch.apply(this, arguments);
      }
      return resumeMatch;
    }())
  }, {
    key: "endMatch",
    value: (function () {
      var _endMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession) {
          throw new Error('No active match session');
        }
        try {
          _performance.performanceMonitor.start('match_recording_end');
          var session = this.currentSession;
          var endTime = Date.now();
          var totalDuration = (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60;
          session.match.metadata.endTime = new Date().toISOString();
          session.match.metadata.durationMinutes = Math.round(totalDuration);
          session.match.status = 'completed';
          if (session.videoRecordingActive) {
            var videoResult = yield _VideoRecordingService.videoRecordingService.stopRecording();
            var uploadResult = yield _FileUploadService.fileUploadService.uploadVideo(videoResult.uri, {
              folder: `matches/${session.match.id || 'temp'}`
            });
            if (uploadResult.data) {
              session.match.videoUrl = uploadResult.data.url;
              session.match.videoDurationSeconds = videoResult.duration;
              session.match.videoFileSizeBytes = uploadResult.data.size;
              if (videoResult.thumbnail) {
                var thumbnailResult = yield _FileUploadService.fileUploadService.uploadThumbnail(videoResult.uri, videoResult.thumbnail, {
                  folder: `matches/${session.match.id || 'temp'}/thumbnails`
                });
                if (thumbnailResult.data) {
                  session.match.videoThumbnailUrl = thumbnailResult.data.url;
                }
              }
            }
          }
          this.calculateFinalStatistics(session.match.statistics, session.match.score);
          var finalMatch = yield this.updateMatchInDatabase(session.match);
          this.currentSession = null;
          this.notifySessionListeners();
          _performance.performanceMonitor.end('match_recording_end');
          return finalMatch;
        } catch (error) {
          console.error('Failed to end match:', error);
          throw error;
        }
      });
      function endMatch() {
        return _endMatch.apply(this, arguments);
      }
      return endMatch;
    }())
  }, {
    key: "cancelMatch",
    value: (function () {
      var _cancelMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession) {
          return;
        }
        try {
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.stopRecording();
          }
          this.currentSession.match.status = 'cancelled';
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.currentSession = null;
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to cancel match:', error);
          throw error;
        }
      });
      function cancelMatch() {
        return _cancelMatch.apply(this, arguments);
      }
      return cancelMatch;
    }())
  }, {
    key: "getCurrentSession",
    value: function getCurrentSession() {
      return this.currentSession;
    }
  }, {
    key: "addSessionListener",
    value: function addSessionListener(listener) {
      this.sessionListeners.push(listener);
    }
  }, {
    key: "removeSessionListener",
    value: function removeSessionListener(listener) {
      this.sessionListeners = this.sessionListeners.filter(function (l) {
        return l !== listener;
      });
    }
  }, {
    key: "addScoreListener",
    value: function addScoreListener(listener) {
      this.scoreListeners.push(listener);
    }
  }, {
    key: "removeScoreListener",
    value: function removeScoreListener(listener) {
      this.scoreListeners = this.scoreListeners.filter(function (l) {
        return l !== listener;
      });
    }
  }, {
    key: "validateMatchMetadata",
    value: function validateMatchMetadata(metadata) {
      var _metadata$opponentNam;
      if (!((_metadata$opponentNam = metadata.opponentName) != null && _metadata$opponentNam.trim())) {
        throw new Error('Opponent name is required');
      }
      if (!metadata.userId) {
        throw new Error('User ID is required');
      }
      if (!metadata.matchType) {
        throw new Error('Match type is required');
      }
      if (!metadata.matchFormat) {
        throw new Error('Match format is required');
      }
      if (!metadata.surface) {
        throw new Error('Court surface is required');
      }
    }
  }, {
    key: "initializeScore",
    value: function initializeScore(format) {
      var maxSets = format === 'best_of_5' ? 5 : 3;
      return {
        sets: [],
        finalScore: '',
        result: 'win',
        setsWon: 0,
        setsLost: 0
      };
    }
  }, {
    key: "initializeStatistics",
    value: function initializeStatistics(userId) {
      return {
        matchId: '',
        userId: userId,
        aces: 0,
        doubleFaults: 0,
        firstServesIn: 0,
        firstServesAttempted: 0,
        firstServePointsWon: 0,
        secondServePointsWon: 0,
        firstServeReturnPointsWon: 0,
        secondServeReturnPointsWon: 0,
        breakPointsConverted: 0,
        breakPointsFaced: 0,
        winners: 0,
        unforcedErrors: 0,
        forcedErrors: 0,
        totalPointsWon: 0,
        totalPointsPlayed: 0,
        netPointsAttempted: 0,
        netPointsWon: 0,
        forehandWinners: 0,
        backhandWinners: 0,
        forehandErrors: 0,
        backhandErrors: 0
      };
    }
  }, {
    key: "updateScore",
    value: function updateScore(currentScore, setNumber, gameNumber, winner, event) {
      var updatedScore = Object.assign({}, currentScore);
      while (updatedScore.sets.length < setNumber) {
        updatedScore.sets.push({
          setNumber: updatedScore.sets.length + 1,
          userGames: 0,
          opponentGames: 0,
          isTiebreak: false,
          isCompleted: false
        });
      }
      var currentSet = updatedScore.sets[setNumber - 1];
      if (winner === 'user') {} else {}
      return updatedScore;
    }
  }, {
    key: "updateStatistics",
    value: function updateStatistics(statistics, event) {
      statistics.totalPointsPlayed++;
      if (event.player === 'user') {
        statistics.totalPointsWon++;
      }
      switch (event.eventType) {
        case 'ace':
          statistics.aces++;
          break;
        case 'double_fault':
          statistics.doubleFaults++;
          break;
        case 'winner':
          statistics.winners++;
          break;
        case 'unforced_error':
          statistics.unforcedErrors++;
          break;
        case 'forced_error':
          statistics.forcedErrors++;
          break;
      }
    }
  }, {
    key: "isSetComplete",
    value: function isSetComplete(set) {
      return set.userGames >= 6 && set.userGames - set.opponentGames >= 2 || set.opponentGames >= 6 && set.opponentGames - set.userGames >= 2 || set.isTiebreak;
    }
  }, {
    key: "isGameComplete",
    value: function isGameComplete(set, gameNumber) {
      return true;
    }
  }, {
    key: "isMatchComplete",
    value: function isMatchComplete(score, format) {
      var setsToWin = format === 'best_of_5' ? 3 : 2;
      return score.setsWon >= setsToWin || score.setsLost >= setsToWin;
    }
  }, {
    key: "calculateFinalStatistics",
    value: function calculateFinalStatistics(statistics, score) {
      if (statistics.firstServesAttempted > 0) {
        statistics.firstServePercentage = statistics.firstServesIn / statistics.firstServesAttempted * 100;
      }
      if (statistics.breakPointsFaced > 0) {
        statistics.breakPointConversionRate = statistics.breakPointsConverted / statistics.breakPointsFaced * 100;
      }
      if (statistics.netPointsAttempted > 0) {
        statistics.netSuccessRate = statistics.netPointsWon / statistics.netPointsAttempted * 100;
      }
    }
  }, {
    key: "saveMatchToDatabase",
    value: function () {
      var _saveMatchToDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        try {
          var matchData = {
            id: match.id,
            user_id: match.metadata.userId,
            opponent_name: match.metadata.opponentName,
            match_type: match.metadata.matchType || 'friendly',
            match_format: match.metadata.matchFormat,
            surface: match.metadata.surface,
            location: match.metadata.location,
            court_name: match.metadata.courtName,
            weather_conditions: match.metadata.weather,
            temperature: match.metadata.temperature,
            match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],
            start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],
            status: match.status,
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            created_at: match.createdAt,
            updated_at: match.updatedAt
          };
          var attempts = 0;
          var maxAttempts = 3;
          while (attempts < maxAttempts) {
            try {
              var _result$data;
              var result = yield _MatchRepository.matchRepository.createMatch(matchData);
              if (result.error) {
                if (attempts === maxAttempts - 1) {
                  return {
                    success: false,
                    error: result.error
                  };
                }
                attempts++;
                yield new Promise(function (resolve) {
                  return setTimeout(resolve, 1000 * attempts);
                });
                continue;
              }
              return {
                success: true,
                data: {
                  id: match.id,
                  databaseId: (_result$data = result.data) == null ? void 0 : _result$data.id
                }
              };
            } catch (error) {
              attempts++;
              if (attempts === maxAttempts) {
                throw error;
              }
              yield new Promise(function (resolve) {
                return setTimeout(resolve, 1000 * attempts);
              });
            }
          }
          return {
            success: false,
            error: 'Failed to save after multiple attempts'
          };
        } catch (error) {
          console.error('Error saving match to database:', error);
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function saveMatchToDatabase(_x4) {
        return _saveMatchToDatabase.apply(this, arguments);
      }
      return saveMatchToDatabase;
    }()
  }, {
    key: "updateMatchInDatabase",
    value: function () {
      var _updateMatchInDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        try {
          if (!match.id) {
            return {
              success: false,
              error: 'Match ID is required for update'
            };
          }
          var updateData = {
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            status: match.status,
            updated_at: new Date().toISOString()
          };
          if (match.status === 'completed' && match.metadata.endTime) {
            updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];
            updateData.duration_minutes = Math.round((new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60));
            updateData.final_score = this.generateFinalScoreString(match.score);
            updateData.result = this.determineMatchResult(match.score, match.metadata.userId);
            updateData.sets_won = match.score.setsWon;
            updateData.sets_lost = match.score.setsLost;
          }
          var result = yield _MatchRepository.matchRepository.updateMatch(match.id, updateData);
          if (result.error) {
            return {
              success: false,
              error: result.error
            };
          }
          return {
            success: true
          };
        } catch (error) {
          console.error('Error updating match in database:', error);
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function updateMatchInDatabase(_x5) {
        return _updateMatchInDatabase.apply(this, arguments);
      }
      return updateMatchInDatabase;
    }()
  }, {
    key: "generateFinalScoreString",
    value: function generateFinalScoreString(score) {
      if (!score.sets || score.sets.length === 0) {
        return '0-0';
      }
      return score.sets.map(function (set) {
        return `${set.userGames}-${set.opponentGames}`;
      }).join(', ');
    }
  }, {
    key: "determineMatchResult",
    value: function determineMatchResult(score, userId) {
      if (score.setsWon > score.setsLost) {
        return 'win';
      } else if (score.setsLost > score.setsWon) {
        return 'loss';
      }
      return 'draw';
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateEventId",
    value: function generateEventId() {
      return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "notifySessionListeners",
    value: function notifySessionListeners() {
      var _this = this;
      this.sessionListeners.forEach(function (listener) {
        return listener(_this.currentSession);
      });
    }
  }, {
    key: "notifyScoreListeners",
    value: function notifyScoreListeners() {
      var _this2 = this;
      if (this.currentSession) {
        this.scoreListeners.forEach(function (listener) {
          return listener(_this2.currentSession.match.score);
        });
      }
    }
  }]);
}();
var matchRecordingService = exports.matchRecordingService = new MatchRecordingService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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