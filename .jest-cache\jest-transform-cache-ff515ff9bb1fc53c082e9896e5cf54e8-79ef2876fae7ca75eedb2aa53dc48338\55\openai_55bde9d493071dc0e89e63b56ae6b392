0667234c44604c420d4556bcbca3ca87
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.openAIService = void 0;
var _slicedToArray2 = _interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _env2 = require("expo/virtual/env");
var _openai = _interopRequireDefault(require("openai"));
var _errorHandler = require("../utils/errorHandler");
var getOpenAIClient = function getOpenAIClient() {
  var apiKey = process.env.OPENAI_API_KEY || _env2.env.EXPO_PUBLIC_OPENAI_API_KEY;
  if (!apiKey || apiKey.includes('placeholder') || apiKey === 'your-openai-api-key') {
    console.warn('OpenAI API key not configured. Using fallback responses.');
    return null;
  }
  try {
    return new _openai.default({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true,
      timeout: 30000,
      maxRetries: 2
    });
  } catch (error) {
    console.error('Failed to initialize OpenAI client:', error);
    _errorHandler.errorHandler.handle(error, {
      service: 'OpenAI',
      action: 'initialization'
    });
    return null;
  }
};
var openai = getOpenAIClient();
var OpenAIService = function () {
  function OpenAIService() {
    (0, _classCallCheck2.default)(this, OpenAIService);
  }
  return (0, _createClass2.default)(OpenAIService, [{
    key: "isConfigured",
    value: function isConfigured() {
      var apiKey = process.env.OPENAI_API_KEY || _env2.env.EXPO_PUBLIC_OPENAI_API_KEY;
      return !!apiKey && !apiKey.includes('placeholder') && apiKey !== 'your-openai-api-key';
    }
  }, {
    key: "makeOpenAIRequest",
    value: function () {
      var _makeOpenAIRequest = (0, _asyncToGenerator2.default)(function* (operation, fallback, context) {
        if (!openai) {
          console.warn(`OpenAI not configured for ${context}, using fallback`);
          return fallback();
        }
        return (0, _errorHandler.withErrorHandling)(operation, {
          service: 'OpenAI',
          action: context
        }, {
          showUserError: false,
          retryable: true,
          onError: function onError(error) {
            console.error(`OpenAI ${context} error:`, error);
          }
        }).then(function (result) {
          return result || fallback();
        });
      });
      function makeOpenAIRequest(_x, _x2, _x3) {
        return _makeOpenAIRequest.apply(this, arguments);
      }
      return makeOpenAIRequest;
    }()
  }, {
    key: "generateCoachingAdvice",
    value: (function () {
      var _generateCoachingAdvice = (0, _asyncToGenerator2.default)(function* (request) {
        if (!this.isConfigured()) {
          return this.getFallbackCoachingResponse(request);
        }
        try {
          var _completion$choices$;
          var prompt = this.buildCoachingPrompt(request);
          if (!openai) {
            throw new Error('OpenAI client not initialized');
          }
          var completion = yield openai.chat.completions.create({
            model: "gpt-4",
            messages: [{
              role: "system",
              content: `You are an expert tennis coach with 20+ years of experience coaching players from beginner to professional level. You specialize in technique analysis, strategic game planning, and mental game development. Provide detailed, actionable advice tailored to the player's skill level and current performance.`
            }, {
              role: "user",
              content: prompt
            }],
            max_tokens: 1500,
            temperature: 0.7
          });
          var response = (_completion$choices$ = completion.choices[0]) == null || (_completion$choices$ = _completion$choices$.message) == null ? void 0 : _completion$choices$.content;
          if (!response) {
            throw new Error('No response from OpenAI');
          }
          return this.parseCoachingResponse(response, request);
        } catch (error) {
          console.error('OpenAI coaching error:', error);
          return this.getFallbackCoachingResponse(request);
        }
      });
      function generateCoachingAdvice(_x4) {
        return _generateCoachingAdvice.apply(this, arguments);
      }
      return generateCoachingAdvice;
    }())
  }, {
    key: "analyzeVideoTechnique",
    value: (function () {
      var _analyzeVideoTechnique = (0, _asyncToGenerator2.default)(function* (prompt) {
        if (!this.isConfigured()) {
          return this.getFallbackVideoAnalysis();
        }
        try {
          var _completion$choices$2;
          var analysisPrompt = `
        Analyze this tennis video based on the following information:
        
        Video Description: ${prompt.videoDescription}
        Detected Movements: ${prompt.detectedMovements.join(', ')}
        Player Skill Level: ${prompt.skillLevel}
        
        Please provide:
        1. Overall technique score (0-100)
        2. Technical feedback points
        3. Areas for improvement
        4. Strengths to maintain
        
        Format your response as JSON with keys: overallScore, technicalFeedback, improvements, strengths
      `;
          if (!openai) {
            throw new Error('OpenAI client not initialized');
          }
          var completion = yield openai.chat.completions.create({
            model: "gpt-4",
            messages: [{
              role: "system",
              content: "You are a professional tennis technique analyst. Analyze tennis movements and provide detailed technical feedback."
            }, {
              role: "user",
              content: analysisPrompt
            }],
            max_tokens: 800,
            temperature: 0.3
          });
          var response = (_completion$choices$2 = completion.choices[0]) == null || (_completion$choices$2 = _completion$choices$2.message) == null ? void 0 : _completion$choices$2.content;
          if (!response) {
            throw new Error('No response from OpenAI');
          }
          try {
            return JSON.parse(response);
          } catch (_unused) {
            return this.parseVideoAnalysisText(response);
          }
        } catch (error) {
          console.error('OpenAI video analysis error:', error);
          return this.getFallbackVideoAnalysis();
        }
      });
      function analyzeVideoTechnique(_x5) {
        return _analyzeVideoTechnique.apply(this, arguments);
      }
      return analyzeVideoTechnique;
    }())
  }, {
    key: "generateMatchStrategy",
    value: (function () {
      var _generateMatchStrategy = (0, _asyncToGenerator2.default)(function* (opponentStyle, playerStrengths, playerWeaknesses, surface) {
        if (!this.isConfigured()) {
          return this.getFallbackMatchStrategy(opponentStyle, surface);
        }
        try {
          var _completion$choices$3;
          var prompt = `
        Generate a tennis match strategy for a player with the following profile:
        
        Strengths: ${playerStrengths.join(', ')}
        Weaknesses: ${playerWeaknesses.join(', ')}
        Court Surface: ${surface}
        Opponent Style: ${opponentStyle}
        
        Provide specific tactical advice for this matchup.
      `;
          if (!openai) {
            throw new Error('OpenAI client not initialized');
          }
          var completion = yield openai.chat.completions.create({
            model: "gpt-4",
            messages: [{
              role: "system",
              content: "You are a tennis strategy expert. Provide detailed match tactics and game plans."
            }, {
              role: "user",
              content: prompt
            }],
            max_tokens: 600,
            temperature: 0.6
          });
          return ((_completion$choices$3 = completion.choices[0]) == null || (_completion$choices$3 = _completion$choices$3.message) == null ? void 0 : _completion$choices$3.content) || this.getFallbackMatchStrategy(opponentStyle, surface);
        } catch (error) {
          console.error('OpenAI strategy error:', error);
          return this.getFallbackMatchStrategy(opponentStyle, surface);
        }
      });
      function generateMatchStrategy(_x6, _x7, _x8, _x9) {
        return _generateMatchStrategy.apply(this, arguments);
      }
      return generateMatchStrategy;
    }())
  }, {
    key: "buildCoachingPrompt",
    value: function buildCoachingPrompt(request) {
      return `
      Analyze this tennis player's performance and provide personalized coaching advice:
      
      Skill Level: ${request.skillLevel}
      Recent Training Sessions: ${request.recentSessions.join(', ')}
      Current Skill Ratings (0-100):
      - Forehand: ${request.currentStats.forehand}
      - Backhand: ${request.currentStats.backhand}
      - Serve: ${request.currentStats.serve}
      - Volley: ${request.currentStats.volley}
      - Footwork: ${request.currentStats.footwork}
      - Strategy: ${request.currentStats.strategy}
      - Mental Game: ${request.currentStats.mental_game}
      
      Additional Context: ${request.context || 'General improvement focus'}
      
      Please provide:
      1. A personalized tip for immediate improvement
      2. Technical feedback on their weakest areas
      3. Strategic advice for their skill level
      4. Mental game development tips
      5. 3-4 specific drills with descriptions
      6. A 2-week improvement plan
    `;
    }
  }, {
    key: "parseCoachingResponse",
    value: function parseCoachingResponse(response, request) {
      var lines = response.split('\n').filter(function (line) {
        return line.trim();
      });
      return {
        personalizedTip: this.extractSection(lines, 'tip') || 'Focus on consistent practice and gradual improvement.',
        technicalFeedback: this.extractListItems(lines, 'technical') || ['Work on basic fundamentals'],
        strategicAdvice: this.extractSection(lines, 'strategic') || 'Play to your strengths and minimize weaknesses.',
        mentalGameTips: this.extractSection(lines, 'mental') || 'Stay focused and maintain positive self-talk.',
        recommendedDrills: this.extractDrills(lines) || this.getDefaultDrills(request.skillLevel),
        improvementPlan: this.extractSection(lines, 'plan') || 'Practice regularly and track your progress.'
      };
    }
  }, {
    key: "extractSection",
    value: function extractSection(lines, keyword) {
      var _lines$sectionStart$s;
      var sectionStart = lines.findIndex(function (line) {
        return line.toLowerCase().includes(keyword) && line.includes(':');
      });
      if (sectionStart === -1) return '';
      return ((_lines$sectionStart$s = lines[sectionStart].split(':')[1]) == null ? void 0 : _lines$sectionStart$s.trim()) || '';
    }
  }, {
    key: "extractListItems",
    value: function extractListItems(lines, keyword) {
      return lines.filter(function (line) {
        return line.includes('-') || line.match(/^\d+\./);
      }).map(function (line) {
        return line.replace(/^[-\d.]\s*/, '').trim();
      }).slice(0, 3);
    }
  }, {
    key: "extractDrills",
    value: function extractDrills(lines) {
      return [{
        name: "Consistency Drill",
        description: "Focus on hitting 20 consecutive shots cross-court",
        duration: "15 minutes",
        difficulty: "Intermediate",
        focus: "Consistency"
      }];
    }
  }, {
    key: "getDefaultDrills",
    value: function getDefaultDrills(skillLevel) {
      var drills = {
        beginner: [{
          name: "Wall Practice",
          description: "Hit against a wall for consistency",
          duration: "10 min",
          difficulty: "Beginner",
          focus: "Control"
        }],
        intermediate: [{
          name: "Cross-Court Rally",
          description: "Maintain cross-court rallies",
          duration: "15 min",
          difficulty: "Intermediate",
          focus: "Consistency"
        }],
        club: [{
          name: "Approach Shots",
          description: "Practice approach and net play",
          duration: "20 min",
          difficulty: "Advanced",
          focus: "Net Game"
        }],
        advanced: [{
          name: "Pattern Play",
          description: "Execute specific shot patterns",
          duration: "25 min",
          difficulty: "Advanced",
          focus: "Strategy"
        }]
      };
      return drills[skillLevel] || drills.intermediate;
    }
  }, {
    key: "getFallbackCoachingResponse",
    value: function getFallbackCoachingResponse(request) {
      var weakestSkill = Object.entries(request.currentStats).sort(function (_ref, _ref2) {
        var _ref3 = (0, _slicedToArray2.default)(_ref, 2),
          a = _ref3[1];
        var _ref4 = (0, _slicedToArray2.default)(_ref2, 2),
          b = _ref4[1];
        return a - b;
      })[0][0];
      return {
        personalizedTip: `Focus on improving your ${weakestSkill.replace('_', ' ')} through targeted practice.`,
        technicalFeedback: [`Your ${weakestSkill.replace('_', ' ')} needs attention`, 'Work on fundamental mechanics'],
        strategicAdvice: 'Play to your strengths while gradually improving weaker areas.',
        mentalGameTips: 'Stay positive and focus on process over results.',
        recommendedDrills: this.getDefaultDrills(request.skillLevel),
        improvementPlan: 'Practice 3-4 times per week with specific focus on technique.'
      };
    }
  }, {
    key: "getFallbackVideoAnalysis",
    value: function getFallbackVideoAnalysis() {
      return {
        overallScore: 75,
        technicalFeedback: ['Good follow-through on forehand', 'Consistent contact point'],
        improvements: ['Work on knee bend', 'Improve preparation timing'],
        strengths: ['Excellent toss placement', 'Good court positioning']
      };
    }
  }, {
    key: "parseVideoAnalysisText",
    value: function parseVideoAnalysisText(response) {
      return {
        overallScore: 78,
        technicalFeedback: ['Technique analysis completed'],
        improvements: ['Continue working on fundamentals'],
        strengths: ['Good overall form']
      };
    }
  }, {
    key: "getFallbackMatchStrategy",
    value: function getFallbackMatchStrategy(opponentStyle, surface) {
      return `Against a ${opponentStyle} player on ${surface}, focus on consistent play and force them to make errors. Use your strengths to control the points.`;
    }
  }]);
}();
var openAIService = exports.openAIService = new OpenAIService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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