9fa7d1fa7b940a5eed0cf029f624ac77
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var originalDefineProperty = Object.defineProperty;
Object.defineProperty = function (obj, prop, descriptor) {
  if (obj === null || obj === undefined) {
    console.warn(`Attempted to define property '${prop}' on null/undefined object`);
    return obj;
  }
  if (typeof obj !== 'object' && typeof obj !== 'function') {
    console.warn(`Attempted to define property '${prop}' on non-object:`, typeof obj);
    return obj;
  }
  try {
    return originalDefineProperty.call(this, obj, prop, descriptor);
  } catch (error) {
    console.warn(`Failed to define property '${prop}':`, error.message);
    return obj;
  }
};
if (typeof global.performance === 'undefined') {
  global.performance = {
    now: function now() {
      return Date.now();
    },
    mark: function mark() {},
    measure: function measure() {},
    getEntriesByName: function getEntriesByName() {
      return [];
    },
    getEntriesByType: function getEntriesByType() {
      return [];
    },
    clearMarks: function clearMarks() {},
    clearMeasures: function clearMeasures() {}
  };
}
if (typeof global.requestAnimationFrame === 'undefined') {
  global.requestAnimationFrame = function (callback) {
    return setTimeout(callback, 16);
  };
}
if (typeof global.cancelAnimationFrame === 'undefined') {
  global.cancelAnimationFrame = function (id) {
    clearTimeout(id);
  };
}
if (typeof global.URL === 'undefined') {
  global.URL = function () {
    function URL(url, base) {
      (0, _classCallCheck2.default)(this, URL);
      this.href = url;
      this.origin = 'http://localhost';
      this.protocol = 'http:';
      this.host = 'localhost';
      this.hostname = 'localhost';
      this.port = '';
      this.pathname = '/';
      this.search = '';
      this.hash = '';
    }
    return (0, _createClass2.default)(URL, [{
      key: "toString",
      value: function toString() {
        return this.href;
      }
    }], [{
      key: "createObjectURL",
      value: function createObjectURL() {
        return 'blob:mock-url';
      }
    }, {
      key: "revokeObjectURL",
      value: function revokeObjectURL() {}
    }]);
  }();
}
if (typeof global.Blob === 'undefined') {
  global.Blob = function () {
    function Blob(parts, options) {
      (0, _classCallCheck2.default)(this, Blob);
      this.parts = parts || [];
      this.options = options || {};
      this.size = 0;
      this.type = this.options.type || '';
    }
    return (0, _createClass2.default)(Blob, [{
      key: "slice",
      value: function slice() {
        return new Blob();
      }
    }, {
      key: "stream",
      value: function stream() {
        return new ReadableStream();
      }
    }, {
      key: "text",
      value: function text() {
        return Promise.resolve('');
      }
    }, {
      key: "arrayBuffer",
      value: function arrayBuffer() {
        return Promise.resolve(new ArrayBuffer(0));
      }
    }]);
  }();
}
if (typeof global.Worker === 'undefined') {
  global.Worker = function () {
    function Worker(url) {
      (0, _classCallCheck2.default)(this, Worker);
      this.url = url;
      this.onmessage = null;
      this.onerror = null;
    }
    return (0, _createClass2.default)(Worker, [{
      key: "postMessage",
      value: function postMessage(data) {
        var _this = this;
        setTimeout(function () {
          if (_this.onmessage) {
            _this.onmessage({
              data: {
                success: true,
                result: data
              }
            });
          }
        }, 0);
      }
    }, {
      key: "terminate",
      value: function terminate() {}
    }]);
  }();
}
if (typeof global.ReadableStream === 'undefined') {
  global.ReadableStream = function () {
    function ReadableStream() {
      (0, _classCallCheck2.default)(this, ReadableStream);
      this.locked = false;
    }
    return (0, _createClass2.default)(ReadableStream, [{
      key: "getReader",
      value: function getReader() {
        return {
          read: function read() {
            return Promise.resolve({
              done: true,
              value: undefined
            });
          },
          releaseLock: function releaseLock() {},
          cancel: function cancel() {
            return Promise.resolve();
          }
        };
      }
    }, {
      key: "cancel",
      value: function cancel() {
        return Promise.resolve();
      }
    }]);
  }();
}
if (typeof global.crypto === 'undefined') {
  global.crypto = {
    getRandomValues: function getRandomValues(array) {
      for (var i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    },
    randomUUID: function randomUUID() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0;
        var v = c === 'x' ? r : r & 0x3 | 0x8;
        return v.toString(16);
      });
    },
    subtle: {
      digest: function digest() {
        return Promise.resolve(new ArrayBuffer(32));
      },
      encrypt: function encrypt() {
        return Promise.resolve(new ArrayBuffer(16));
      },
      decrypt: function decrypt() {
        return Promise.resolve(new ArrayBuffer(16));
      },
      sign: function sign() {
        return Promise.resolve(new ArrayBuffer(64));
      },
      verify: function verify() {
        return Promise.resolve(true);
      },
      generateKey: function generateKey() {
        return Promise.resolve({});
      },
      importKey: function importKey() {
        return Promise.resolve({});
      },
      exportKey: function exportKey() {
        return Promise.resolve(new ArrayBuffer(32));
      },
      deriveBits: function deriveBits() {
        return Promise.resolve(new ArrayBuffer(32));
      },
      deriveKey: function deriveKey() {
        return Promise.resolve({});
      }
    }
  };
}
if (typeof global.TextEncoder === 'undefined') {
  global.TextEncoder = function () {
    function TextEncoder() {
      (0, _classCallCheck2.default)(this, TextEncoder);
    }
    return (0, _createClass2.default)(TextEncoder, [{
      key: "encode",
      value: function encode(string) {
        var utf8 = [];
        for (var i = 0; i < string.length; i++) {
          var charcode = string.charCodeAt(i);
          if (charcode < 0x80) utf8.push(charcode);else if (charcode < 0x800) {
            utf8.push(0xc0 | charcode >> 6, 0x80 | charcode & 0x3f);
          } else if (charcode < 0xd800 || charcode >= 0xe000) {
            utf8.push(0xe0 | charcode >> 12, 0x80 | charcode >> 6 & 0x3f, 0x80 | charcode & 0x3f);
          } else {
            i++;
            charcode = 0x10000 + ((charcode & 0x3ff) << 10 | string.charCodeAt(i) & 0x3ff);
            utf8.push(0xf0 | charcode >> 18, 0x80 | charcode >> 12 & 0x3f, 0x80 | charcode >> 6 & 0x3f, 0x80 | charcode & 0x3f);
          }
        }
        return new Uint8Array(utf8);
      }
    }]);
  }();
}
if (typeof global.TextDecoder === 'undefined') {
  global.TextDecoder = function () {
    function TextDecoder() {
      (0, _classCallCheck2.default)(this, TextDecoder);
    }
    return (0, _createClass2.default)(TextDecoder, [{
      key: "decode",
      value: function decode(bytes) {
        var string = '';
        for (var i = 0; i < bytes.length; i++) {
          string += String.fromCharCode(bytes[i]);
        }
        return string;
      }
    }]);
  }();
}
if (typeof global.navigator === 'undefined') {
  global.navigator = {
    userAgent: 'jest',
    platform: 'jest',
    onLine: true,
    language: 'en-US',
    languages: ['en-US'],
    cookieEnabled: true,
    doNotTrack: null,
    geolocation: {
      getCurrentPosition: jest.fn(),
      watchPosition: jest.fn(),
      clearWatch: jest.fn()
    },
    mediaDevices: {
      getUserMedia: jest.fn(function () {
        return Promise.resolve({});
      }),
      enumerateDevices: jest.fn(function () {
        return Promise.resolve([]);
      })
    },
    permissions: {
      query: jest.fn(function () {
        return Promise.resolve({
          state: 'granted'
        });
      })
    },
    serviceWorker: {
      register: jest.fn(function () {
        return Promise.resolve({});
      }),
      ready: Promise.resolve({})
    }
  };
}
if (typeof global.location === 'undefined') {
  global.location = {
    href: 'http://localhost/',
    origin: 'http://localhost',
    protocol: 'http:',
    host: 'localhost',
    hostname: 'localhost',
    port: '',
    pathname: '/',
    search: '',
    hash: '',
    assign: jest.fn(),
    replace: jest.fn(),
    reload: jest.fn()
  };
}
if (typeof global.localStorage === 'undefined') {
  var storage = {};
  global.localStorage = {
    getItem: function getItem(key) {
      return storage[key] || null;
    },
    setItem: function setItem(key, value) {
      storage[key] = String(value);
    },
    removeItem: function removeItem(key) {
      delete storage[key];
    },
    clear: function clear() {
      Object.keys(storage).forEach(function (key) {
        return delete storage[key];
      });
    },
    key: function key(index) {
      return Object.keys(storage)[index] || null;
    },
    get length() {
      return Object.keys(storage).length;
    }
  };
}
if (typeof global.sessionStorage === 'undefined') {
  global.sessionStorage = global.localStorage;
}
if (typeof global.IntersectionObserver === 'undefined') {
  global.IntersectionObserver = function () {
    function IntersectionObserver(callback, options) {
      (0, _classCallCheck2.default)(this, IntersectionObserver);
      this.callback = callback;
      this.options = options;
    }
    return (0, _createClass2.default)(IntersectionObserver, [{
      key: "observe",
      value: function observe() {}
    }, {
      key: "unobserve",
      value: function unobserve() {}
    }, {
      key: "disconnect",
      value: function disconnect() {}
    }]);
  }();
}
if (typeof global.ResizeObserver === 'undefined') {
  global.ResizeObserver = function () {
    function ResizeObserver(callback) {
      (0, _classCallCheck2.default)(this, ResizeObserver);
      this.callback = callback;
    }
    return (0, _createClass2.default)(ResizeObserver, [{
      key: "observe",
      value: function observe() {}
    }, {
      key: "unobserve",
      value: function unobserve() {}
    }, {
      key: "disconnect",
      value: function disconnect() {}
    }]);
  }();
}
if (typeof global.MutationObserver === 'undefined') {
  global.MutationObserver = function () {
    function MutationObserver(callback) {
      (0, _classCallCheck2.default)(this, MutationObserver);
      this.callback = callback;
    }
    return (0, _createClass2.default)(MutationObserver, [{
      key: "observe",
      value: function observe() {}
    }, {
      key: "disconnect",
      value: function disconnect() {}
    }, {
      key: "takeRecords",
      value: function takeRecords() {
        return [];
      }
    }]);
  }();
}
if (typeof global.console === 'undefined') {
  global.console = {
    log: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    info: jest.fn(),
    debug: jest.fn(),
    trace: jest.fn(),
    group: jest.fn(),
    groupEnd: jest.fn(),
    groupCollapsed: jest.fn(),
    time: jest.fn(),
    timeEnd: jest.fn(),
    count: jest.fn(),
    countReset: jest.fn(),
    clear: jest.fn(),
    table: jest.fn(),
    dir: jest.fn(),
    dirxml: jest.fn(),
    assert: jest.fn()
  };
}
if (typeof global.global === 'undefined') {
  global.global = global;
}
if (typeof global.window !== 'undefined') {
  Object.keys(global).forEach(function (key) {
    if (!(key in global.window)) {
      try {
        global.window[key] = global[key];
      } catch (error) {}
    }
  });
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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