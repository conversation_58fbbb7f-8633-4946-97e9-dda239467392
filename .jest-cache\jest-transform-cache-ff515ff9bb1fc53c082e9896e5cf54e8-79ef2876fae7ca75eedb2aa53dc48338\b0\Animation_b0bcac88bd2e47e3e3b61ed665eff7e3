2a2deceb701f8d03a9c155756630c04a
'use strict';

var _interopRequireDefault2 = require("@babel/runtime/helpers/interopRequireDefault");
var _classCallCheck2 = _interopRequireDefault2(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault2(require("@babel/runtime/helpers/createClass"));
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _NativeAnimatedHelper = _interopRequireDefault(require("../NativeAnimatedHelper"));
var startNativeAnimationNextId = 1;
var Animation = function () {
  function Animation() {
    (0, _classCallCheck2.default)(this, Animation);
  }
  return (0, _createClass2.default)(Animation, [{
    key: "start",
    value: function start(fromValue, onUpdate, onEnd, previousAnimation, animatedValue) {}
  }, {
    key: "stop",
    value: function stop() {
      if (this.__nativeId) {
        _NativeAnimatedHelper.default.API.stopAnimation(this.__nativeId);
      }
    }
  }, {
    key: "__getNativeAnimationConfig",
    value: function __getNativeAnimationConfig() {
      throw new Error('This animation type cannot be offloaded to native');
    }
  }, {
    key: "__debouncedOnEnd",
    value: function __debouncedOnEnd(result) {
      var onEnd = this.__onEnd;
      this.__onEnd = null;
      onEnd && onEnd(result);
    }
  }, {
    key: "__startNativeAnimation",
    value: function __startNativeAnimation(animatedValue) {
      var startNativeAnimationWaitId = startNativeAnimationNextId + ":startAnimation";
      startNativeAnimationNextId += 1;
      _NativeAnimatedHelper.default.API.setWaitingForIdentifier(startNativeAnimationWaitId);
      try {
        var config = this.__getNativeAnimationConfig();
        animatedValue.__makeNative(config.platformConfig);
        this.__nativeId = _NativeAnimatedHelper.default.generateNewAnimationId();
        _NativeAnimatedHelper.default.API.startAnimatingNode(this.__nativeId, animatedValue.__getNativeTag(), config, this.__debouncedOnEnd.bind(this));
      } catch (e) {
        throw e;
      } finally {
        _NativeAnimatedHelper.default.API.unsetWaitingForIdentifier(startNativeAnimationWaitId);
      }
    }
  }]);
}();
var _default = exports.default = Animation;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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