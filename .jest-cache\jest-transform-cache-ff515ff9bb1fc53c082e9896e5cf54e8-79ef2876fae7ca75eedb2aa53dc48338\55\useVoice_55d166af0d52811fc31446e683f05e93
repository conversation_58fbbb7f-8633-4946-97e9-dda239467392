3c7c050f268efc206c8f77eb8a1a3bf8
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_122bcgj3d0() {
  var path = "C:\\_SaaS\\AceMind\\project\\hooks\\useVoice.ts";
  var hash = "a6c8eb82a8d628baaac4f6fa1663fd1df9b2cc91";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\hooks\\useVoice.ts",
    statementMap: {
      "0": {
        start: {
          line: 32,
          column: 40
        },
        end: {
          line: 32,
          column: 55
        }
      },
      "1": {
        start: {
          line: 33,
          column: 38
        },
        end: {
          line: 33,
          column: 53
        }
      },
      "2": {
        start: {
          line: 34,
          column: 44
        },
        end: {
          line: 34,
          column: 59
        }
      },
      "3": {
        start: {
          line: 35,
          column: 46
        },
        end: {
          line: 35,
          column: 58
        }
      },
      "4": {
        start: {
          line: 36,
          column: 40
        },
        end: {
          line: 36,
          column: 75
        }
      },
      "5": {
        start: {
          line: 37,
          column: 38
        },
        end: {
          line: 37,
          column: 49
        }
      },
      "6": {
        start: {
          line: 38,
          column: 28
        },
        end: {
          line: 38,
          column: 57
        }
      },
      "7": {
        start: {
          line: 43,
          column: 26
        },
        end: {
          line: 60,
          column: 8
        }
      },
      "8": {
        start: {
          line: 44,
          column: 4
        },
        end: {
          line: 59,
          column: 5
        }
      },
      "9": {
        start: {
          line: 45,
          column: 6
        },
        end: {
          line: 45,
          column: 21
        }
      },
      "10": {
        start: {
          line: 46,
          column: 26
        },
        end: {
          line: 46,
          column: 73
        }
      },
      "11": {
        start: {
          line: 47,
          column: 6
        },
        end: {
          line: 47,
          column: 36
        }
      },
      "12": {
        start: {
          line: 49,
          column: 6
        },
        end: {
          line: 51,
          column: 7
        }
      },
      "13": {
        start: {
          line: 50,
          column: 8
        },
        end: {
          line: 50,
          column: 67
        }
      },
      "14": {
        start: {
          line: 53,
          column: 6
        },
        end: {
          line: 53,
          column: 25
        }
      },
      "15": {
        start: {
          line: 55,
          column: 27
        },
        end: {
          line: 55,
          column: 104
        }
      },
      "16": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 29
        }
      },
      "17": {
        start: {
          line: 57,
          column: 6
        },
        end: {
          line: 57,
          column: 30
        }
      },
      "18": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 19
        }
      },
      "19": {
        start: {
          line: 65,
          column: 25
        },
        end: {
          line: 94,
          column: 21
        }
      },
      "20": {
        start: {
          line: 66,
          column: 4
        },
        end: {
          line: 68,
          column: 5
        }
      },
      "21": {
        start: {
          line: 67,
          column: 6
        },
        end: {
          line: 67,
          column: 59
        }
      },
      "22": {
        start: {
          line: 70,
          column: 4
        },
        end: {
          line: 93,
          column: 5
        }
      },
      "23": {
        start: {
          line: 71,
          column: 6
        },
        end: {
          line: 71,
          column: 21
        }
      },
      "24": {
        start: {
          line: 73,
          column: 6
        },
        end: {
          line: 86,
          column: 9
        }
      },
      "25": {
        start: {
          line: 74,
          column: 8
        },
        end: {
          line: 74,
          column: 45
        }
      },
      "26": {
        start: {
          line: 75,
          column: 8
        },
        end: {
          line: 75,
          column: 41
        }
      },
      "27": {
        start: {
          line: 77,
          column: 8
        },
        end: {
          line: 85,
          column: 9
        }
      },
      "28": {
        start: {
          line: 79,
          column: 26
        },
        end: {
          line: 79,
          column: 77
        }
      },
      "29": {
        start: {
          line: 80,
          column: 10
        },
        end: {
          line: 80,
          column: 34
        }
      },
      "30": {
        start: {
          line: 82,
          column: 10
        },
        end: {
          line: 84,
          column: 11
        }
      },
      "31": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 62
        }
      },
      "32": {
        start: {
          line: 88,
          column: 6
        },
        end: {
          line: 88,
          column: 27
        }
      },
      "33": {
        start: {
          line: 90,
          column: 27
        },
        end: {
          line: 90,
          column: 91
        }
      },
      "34": {
        start: {
          line: 91,
          column: 6
        },
        end: {
          line: 91,
          column: 29
        }
      },
      "35": {
        start: {
          line: 92,
          column: 6
        },
        end: {
          line: 92,
          column: 16
        }
      },
      "36": {
        start: {
          line: 99,
          column: 24
        },
        end: {
          line: 109,
          column: 8
        }
      },
      "37": {
        start: {
          line: 100,
          column: 4
        },
        end: {
          line: 108,
          column: 5
        }
      },
      "38": {
        start: {
          line: 101,
          column: 6
        },
        end: {
          line: 101,
          column: 21
        }
      },
      "39": {
        start: {
          line: 102,
          column: 6
        },
        end: {
          line: 102,
          column: 41
        }
      },
      "40": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 28
        }
      },
      "41": {
        start: {
          line: 105,
          column: 27
        },
        end: {
          line: 105,
          column: 90
        }
      },
      "42": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 106,
          column: 29
        }
      },
      "43": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 16
        }
      },
      "44": {
        start: {
          line: 114,
          column: 16
        },
        end: {
          line: 127,
          column: 8
        }
      },
      "45": {
        start: {
          line: 115,
          column: 4
        },
        end: {
          line: 126,
          column: 5
        }
      },
      "46": {
        start: {
          line: 116,
          column: 6
        },
        end: {
          line: 116,
          column: 21
        }
      },
      "47": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 117,
          column: 26
        }
      },
      "48": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 46
        }
      },
      "49": {
        start: {
          line: 120,
          column: 6
        },
        end: {
          line: 120,
          column: 27
        }
      },
      "50": {
        start: {
          line: 122,
          column: 27
        },
        end: {
          line: 122,
          column: 86
        }
      },
      "51": {
        start: {
          line: 123,
          column: 6
        },
        end: {
          line: 123,
          column: 29
        }
      },
      "52": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 124,
          column: 27
        }
      },
      "53": {
        start: {
          line: 125,
          column: 6
        },
        end: {
          line: 125,
          column: 16
        }
      },
      "54": {
        start: {
          line: 132,
          column: 23
        },
        end: {
          line: 141,
          column: 8
        }
      },
      "55": {
        start: {
          line: 133,
          column: 4
        },
        end: {
          line: 140,
          column: 5
        }
      },
      "56": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 21
        }
      },
      "57": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 40
        }
      },
      "58": {
        start: {
          line: 136,
          column: 6
        },
        end: {
          line: 136,
          column: 27
        }
      },
      "59": {
        start: {
          line: 138,
          column: 27
        },
        end: {
          line: 138,
          column: 89
        }
      },
      "60": {
        start: {
          line: 139,
          column: 6
        },
        end: {
          line: 139,
          column: 29
        }
      },
      "61": {
        start: {
          line: 146,
          column: 32
        },
        end: {
          line: 162,
          column: 8
        }
      },
      "62": {
        start: {
          line: 150,
          column: 4
        },
        end: {
          line: 161,
          column: 5
        }
      },
      "63": {
        start: {
          line: 151,
          column: 6
        },
        end: {
          line: 151,
          column: 21
        }
      },
      "64": {
        start: {
          line: 152,
          column: 6
        },
        end: {
          line: 152,
          column: 26
        }
      },
      "65": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 154,
          column: 65
        }
      },
      "66": {
        start: {
          line: 155,
          column: 6
        },
        end: {
          line: 155,
          column: 27
        }
      },
      "67": {
        start: {
          line: 157,
          column: 27
        },
        end: {
          line: 157,
          column: 99
        }
      },
      "68": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 29
        }
      },
      "69": {
        start: {
          line: 159,
          column: 6
        },
        end: {
          line: 159,
          column: 27
        }
      },
      "70": {
        start: {
          line: 160,
          column: 6
        },
        end: {
          line: 160,
          column: 16
        }
      },
      "71": {
        start: {
          line: 167,
          column: 25
        },
        end: {
          line: 176,
          column: 8
        }
      },
      "72": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 175,
          column: 5
        }
      },
      "73": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 169,
          column: 21
        }
      },
      "74": {
        start: {
          line: 170,
          column: 6
        },
        end: {
          line: 170,
          column: 58
        }
      },
      "75": {
        start: {
          line: 172,
          column: 27
        },
        end: {
          line: 172,
          column: 91
        }
      },
      "76": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 173,
          column: 29
        }
      },
      "77": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 18
        }
      },
      "78": {
        start: {
          line: 181,
          column: 31
        },
        end: {
          line: 183,
          column: 8
        }
      },
      "79": {
        start: {
          line: 182,
          column: 4
        },
        end: {
          line: 182,
          column: 47
        }
      },
      "80": {
        start: {
          line: 186,
          column: 2
        },
        end: {
          line: 193,
          column: 9
        }
      },
      "81": {
        start: {
          line: 187,
          column: 21
        },
        end: {
          line: 190,
          column: 11
        }
      },
      "82": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 188,
          column: 58
        }
      },
      "83": {
        start: {
          line: 189,
          column: 6
        },
        end: {
          line: 189,
          column: 56
        }
      },
      "84": {
        start: {
          line: 192,
          column: 4
        },
        end: {
          line: 192,
          column: 41
        }
      },
      "85": {
        start: {
          line: 192,
          column: 17
        },
        end: {
          line: 192,
          column: 40
        }
      },
      "86": {
        start: {
          line: 196,
          column: 2
        },
        end: {
          line: 198,
          column: 24
        }
      },
      "87": {
        start: {
          line: 197,
          column: 4
        },
        end: {
          line: 197,
          column: 22
        }
      },
      "88": {
        start: {
          line: 200,
          column: 2
        },
        end: {
          line: 225,
          column: 4
        }
      }
    },
    fnMap: {
      "0": {
        name: "useVoice",
        decl: {
          start: {
            line: 31,
            column: 16
          },
          end: {
            line: 31,
            column: 24
          }
        },
        loc: {
          start: {
            line: 31,
            column: 43
          },
          end: {
            line: 226,
            column: 1
          }
        },
        line: 31
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 43,
            column: 38
          },
          end: {
            line: 43,
            column: 39
          }
        },
        loc: {
          start: {
            line: 43,
            column: 68
          },
          end: {
            line: 60,
            column: 3
          }
        },
        line: 43
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 65,
            column: 37
          },
          end: {
            line: 65,
            column: 38
          }
        },
        loc: {
          start: {
            line: 65,
            column: 64
          },
          end: {
            line: 94,
            column: 3
          }
        },
        line: 65
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 73,
            column: 40
          },
          end: {
            line: 73,
            column: 41
          }
        },
        loc: {
          start: {
            line: 73,
            column: 76
          },
          end: {
            line: 86,
            column: 7
          }
        },
        line: 73
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 99,
            column: 36
          },
          end: {
            line: 99,
            column: 37
          }
        },
        loc: {
          start: {
            line: 99,
            column: 63
          },
          end: {
            line: 109,
            column: 3
          }
        },
        line: 99
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 114,
            column: 28
          },
          end: {
            line: 114,
            column: 29
          }
        },
        loc: {
          start: {
            line: 114,
            column: 96
          },
          end: {
            line: 127,
            column: 3
          }
        },
        line: 114
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 132,
            column: 35
          },
          end: {
            line: 132,
            column: 36
          }
        },
        loc: {
          start: {
            line: 132,
            column: 62
          },
          end: {
            line: 141,
            column: 3
          }
        },
        line: 132
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 146,
            column: 44
          },
          end: {
            line: 146,
            column: 45
          }
        },
        loc: {
          start: {
            line: 149,
            column: 22
          },
          end: {
            line: 162,
            column: 3
          }
        },
        line: 149
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 167,
            column: 37
          },
          end: {
            line: 167,
            column: 38
          }
        },
        loc: {
          start: {
            line: 167,
            column: 82
          },
          end: {
            line: 176,
            column: 3
          }
        },
        line: 167
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 181,
            column: 43
          },
          end: {
            line: 181,
            column: 44
          }
        },
        loc: {
          start: {
            line: 181,
            column: 59
          },
          end: {
            line: 183,
            column: 3
          }
        },
        line: 181
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 186,
            column: 12
          },
          end: {
            line: 186,
            column: 13
          }
        },
        loc: {
          start: {
            line: 186,
            column: 18
          },
          end: {
            line: 193,
            column: 3
          }
        },
        line: 186
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 187,
            column: 33
          },
          end: {
            line: 187,
            column: 34
          }
        },
        loc: {
          start: {
            line: 187,
            column: 39
          },
          end: {
            line: 190,
            column: 5
          }
        },
        line: 187
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 192,
            column: 11
          },
          end: {
            line: 192,
            column: 12
          }
        },
        loc: {
          start: {
            line: 192,
            column: 17
          },
          end: {
            line: 192,
            column: 40
          }
        },
        line: 192
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 196,
            column: 12
          },
          end: {
            line: 196,
            column: 13
          }
        },
        loc: {
          start: {
            line: 196,
            column: 18
          },
          end: {
            line: 198,
            column: 3
          }
        },
        line: 196
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 49,
            column: 6
          },
          end: {
            line: 51,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 49,
            column: 6
          },
          end: {
            line: 51,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 49
      },
      "1": {
        loc: {
          start: {
            line: 55,
            column: 27
          },
          end: {
            line: 55,
            column: 104
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 55,
            column: 50
          },
          end: {
            line: 55,
            column: 61
          }
        }, {
          start: {
            line: 55,
            column: 64
          },
          end: {
            line: 55,
            column: 104
          }
        }],
        line: 55
      },
      "2": {
        loc: {
          start: {
            line: 66,
            column: 4
          },
          end: {
            line: 68,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 66,
            column: 4
          },
          end: {
            line: 68,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 66
      },
      "3": {
        loc: {
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 85,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 77,
            column: 8
          },
          end: {
            line: 85,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 77
      },
      "4": {
        loc: {
          start: {
            line: 82,
            column: 10
          },
          end: {
            line: 84,
            column: 11
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 82,
            column: 10
          },
          end: {
            line: 84,
            column: 11
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 82
      },
      "5": {
        loc: {
          start: {
            line: 90,
            column: 27
          },
          end: {
            line: 90,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 90,
            column: 50
          },
          end: {
            line: 90,
            column: 61
          }
        }, {
          start: {
            line: 90,
            column: 64
          },
          end: {
            line: 90,
            column: 91
          }
        }],
        line: 90
      },
      "6": {
        loc: {
          start: {
            line: 105,
            column: 27
          },
          end: {
            line: 105,
            column: 90
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 105,
            column: 50
          },
          end: {
            line: 105,
            column: 61
          }
        }, {
          start: {
            line: 105,
            column: 64
          },
          end: {
            line: 105,
            column: 90
          }
        }],
        line: 105
      },
      "7": {
        loc: {
          start: {
            line: 114,
            column: 49
          },
          end: {
            line: 114,
            column: 76
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 114,
            column: 74
          },
          end: {
            line: 114,
            column: 76
          }
        }],
        line: 114
      },
      "8": {
        loc: {
          start: {
            line: 122,
            column: 27
          },
          end: {
            line: 122,
            column: 86
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 122,
            column: 50
          },
          end: {
            line: 122,
            column: 61
          }
        }, {
          start: {
            line: 122,
            column: 64
          },
          end: {
            line: 122,
            column: 86
          }
        }],
        line: 122
      },
      "9": {
        loc: {
          start: {
            line: 138,
            column: 27
          },
          end: {
            line: 138,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 138,
            column: 50
          },
          end: {
            line: 138,
            column: 61
          }
        }, {
          start: {
            line: 138,
            column: 64
          },
          end: {
            line: 138,
            column: 89
          }
        }],
        line: 138
      },
      "10": {
        loc: {
          start: {
            line: 157,
            column: 27
          },
          end: {
            line: 157,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 157,
            column: 50
          },
          end: {
            line: 157,
            column: 61
          }
        }, {
          start: {
            line: 157,
            column: 64
          },
          end: {
            line: 157,
            column: 99
          }
        }],
        line: 157
      },
      "11": {
        loc: {
          start: {
            line: 172,
            column: 27
          },
          end: {
            line: 172,
            column: 91
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 172,
            column: 50
          },
          end: {
            line: 172,
            column: 61
          }
        }, {
          start: {
            line: 172,
            column: 64
          },
          end: {
            line: 172,
            column: 91
          }
        }],
        line: 172
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "a6c8eb82a8d628baaac4f6fa1663fd1df9b2cc91"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_122bcgj3d0 = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_122bcgj3d0();
import { useState, useCallback, useEffect } from 'react';
import { voiceService } from "../services/voiceService";
export function useVoice() {
  cov_122bcgj3d0().f[0]++;
  var _ref = (cov_122bcgj3d0().s[0]++, useState(false)),
    _ref2 = _slicedToArray(_ref, 2),
    isListening = _ref2[0],
    setIsListening = _ref2[1];
  var _ref3 = (cov_122bcgj3d0().s[1]++, useState(false)),
    _ref4 = _slicedToArray(_ref3, 2),
    isSpeaking = _ref4[0],
    setIsSpeaking = _ref4[1];
  var _ref5 = (cov_122bcgj3d0().s[2]++, useState(false)),
    _ref6 = _slicedToArray(_ref5, 2),
    isInitialized = _ref6[0],
    setIsInitialized = _ref6[1];
  var _ref7 = (cov_122bcgj3d0().s[3]++, useState('')),
    _ref8 = _slicedToArray(_ref7, 2),
    lastTranscript = _ref8[0],
    setLastTranscript = _ref8[1];
  var _ref9 = (cov_122bcgj3d0().s[4]++, useState(null)),
    _ref0 = _slicedToArray(_ref9, 2),
    lastCommand = _ref0[0],
    setLastCommand = _ref0[1];
  var _ref1 = (cov_122bcgj3d0().s[5]++, useState(0)),
    _ref10 = _slicedToArray(_ref1, 2),
    confidence = _ref10[0],
    setConfidence = _ref10[1];
  var _ref11 = (cov_122bcgj3d0().s[6]++, useState(null)),
    _ref12 = _slicedToArray(_ref11, 2),
    error = _ref12[0],
    setError = _ref12[1];
  var initializeVoice = (cov_122bcgj3d0().s[7]++, useCallback(_asyncToGenerator(function* () {
    cov_122bcgj3d0().f[1]++;
    cov_122bcgj3d0().s[8]++;
    try {
      cov_122bcgj3d0().s[9]++;
      setError(null);
      var initialized = (cov_122bcgj3d0().s[10]++, yield voiceService.initializeVoiceRecognition());
      cov_122bcgj3d0().s[11]++;
      setIsInitialized(initialized);
      cov_122bcgj3d0().s[12]++;
      if (!initialized) {
        cov_122bcgj3d0().b[0][0]++;
        cov_122bcgj3d0().s[13]++;
        setError('Voice recognition not available on this device');
      } else {
        cov_122bcgj3d0().b[0][1]++;
      }
      cov_122bcgj3d0().s[14]++;
      return initialized;
    } catch (err) {
      var errorMessage = (cov_122bcgj3d0().s[15]++, err instanceof Error ? (cov_122bcgj3d0().b[1][0]++, err.message) : (cov_122bcgj3d0().b[1][1]++, 'Failed to initialize voice recognition'));
      cov_122bcgj3d0().s[16]++;
      setError(errorMessage);
      cov_122bcgj3d0().s[17]++;
      setIsInitialized(false);
      cov_122bcgj3d0().s[18]++;
      return false;
    }
  }), []));
  var startListening = (cov_122bcgj3d0().s[19]++, useCallback(_asyncToGenerator(function* () {
    cov_122bcgj3d0().f[2]++;
    cov_122bcgj3d0().s[20]++;
    if (!isInitialized) {
      cov_122bcgj3d0().b[2][0]++;
      cov_122bcgj3d0().s[21]++;
      throw new Error('Voice recognition not initialized');
    } else {
      cov_122bcgj3d0().b[2][1]++;
    }
    cov_122bcgj3d0().s[22]++;
    try {
      cov_122bcgj3d0().s[23]++;
      setError(null);
      cov_122bcgj3d0().s[24]++;
      yield voiceService.startListening(function (result) {
        cov_122bcgj3d0().f[3]++;
        cov_122bcgj3d0().s[25]++;
        setLastTranscript(result.transcript);
        cov_122bcgj3d0().s[26]++;
        setConfidence(result.confidence);
        cov_122bcgj3d0().s[27]++;
        if (result.isFinal) {
          cov_122bcgj3d0().b[3][0]++;
          var command = (cov_122bcgj3d0().s[28]++, voiceService.processVoiceCommand(result.transcript));
          cov_122bcgj3d0().s[29]++;
          setLastCommand(command);
          cov_122bcgj3d0().s[30]++;
          if (command) {
            cov_122bcgj3d0().b[4][0]++;
            cov_122bcgj3d0().s[31]++;
            console.log('Voice command recognized:', command);
          } else {
            cov_122bcgj3d0().b[4][1]++;
          }
        } else {
          cov_122bcgj3d0().b[3][1]++;
        }
      });
      cov_122bcgj3d0().s[32]++;
      setIsListening(true);
    } catch (err) {
      var errorMessage = (cov_122bcgj3d0().s[33]++, err instanceof Error ? (cov_122bcgj3d0().b[5][0]++, err.message) : (cov_122bcgj3d0().b[5][1]++, 'Failed to start listening'));
      cov_122bcgj3d0().s[34]++;
      setError(errorMessage);
      cov_122bcgj3d0().s[35]++;
      throw err;
    }
  }), [isInitialized]));
  var stopListening = (cov_122bcgj3d0().s[36]++, useCallback(_asyncToGenerator(function* () {
    cov_122bcgj3d0().f[4]++;
    cov_122bcgj3d0().s[37]++;
    try {
      cov_122bcgj3d0().s[38]++;
      setError(null);
      cov_122bcgj3d0().s[39]++;
      yield voiceService.stopListening();
      cov_122bcgj3d0().s[40]++;
      setIsListening(false);
    } catch (err) {
      var errorMessage = (cov_122bcgj3d0().s[41]++, err instanceof Error ? (cov_122bcgj3d0().b[6][0]++, err.message) : (cov_122bcgj3d0().b[6][1]++, 'Failed to stop listening'));
      cov_122bcgj3d0().s[42]++;
      setError(errorMessage);
      cov_122bcgj3d0().s[43]++;
      throw err;
    }
  }), []));
  var speak = (cov_122bcgj3d0().s[44]++, useCallback(function () {
    var _ref16 = _asyncToGenerator(function* (text) {
      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_122bcgj3d0().b[7][0]++, {});
      cov_122bcgj3d0().f[5]++;
      cov_122bcgj3d0().s[45]++;
      try {
        cov_122bcgj3d0().s[46]++;
        setError(null);
        cov_122bcgj3d0().s[47]++;
        setIsSpeaking(true);
        cov_122bcgj3d0().s[48]++;
        yield voiceService.speak(text, options);
        cov_122bcgj3d0().s[49]++;
        setIsSpeaking(false);
      } catch (err) {
        var errorMessage = (cov_122bcgj3d0().s[50]++, err instanceof Error ? (cov_122bcgj3d0().b[8][0]++, err.message) : (cov_122bcgj3d0().b[8][1]++, 'Failed to speak text'));
        cov_122bcgj3d0().s[51]++;
        setError(errorMessage);
        cov_122bcgj3d0().s[52]++;
        setIsSpeaking(false);
        cov_122bcgj3d0().s[53]++;
        throw err;
      }
    });
    return function (_x) {
      return _ref16.apply(this, arguments);
    };
  }(), []));
  var stopSpeaking = (cov_122bcgj3d0().s[54]++, useCallback(_asyncToGenerator(function* () {
    cov_122bcgj3d0().f[6]++;
    cov_122bcgj3d0().s[55]++;
    try {
      cov_122bcgj3d0().s[56]++;
      setError(null);
      cov_122bcgj3d0().s[57]++;
      yield voiceService.stopSpeaking();
      cov_122bcgj3d0().s[58]++;
      setIsSpeaking(false);
    } catch (err) {
      var errorMessage = (cov_122bcgj3d0().s[59]++, err instanceof Error ? (cov_122bcgj3d0().b[9][0]++, err.message) : (cov_122bcgj3d0().b[9][1]++, 'Failed to stop speaking'));
      cov_122bcgj3d0().s[60]++;
      setError(errorMessage);
    }
  }), []));
  var speakCoachingFeedback = (cov_122bcgj3d0().s[61]++, useCallback(function () {
    var _ref18 = _asyncToGenerator(function* (type, customText) {
      cov_122bcgj3d0().f[7]++;
      cov_122bcgj3d0().s[62]++;
      try {
        cov_122bcgj3d0().s[63]++;
        setError(null);
        cov_122bcgj3d0().s[64]++;
        setIsSpeaking(true);
        cov_122bcgj3d0().s[65]++;
        yield voiceService.speakCoachingFeedback(type, customText);
        cov_122bcgj3d0().s[66]++;
        setIsSpeaking(false);
      } catch (err) {
        var errorMessage = (cov_122bcgj3d0().s[67]++, err instanceof Error ? (cov_122bcgj3d0().b[10][0]++, err.message) : (cov_122bcgj3d0().b[10][1]++, 'Failed to speak coaching feedback'));
        cov_122bcgj3d0().s[68]++;
        setError(errorMessage);
        cov_122bcgj3d0().s[69]++;
        setIsSpeaking(false);
        cov_122bcgj3d0().s[70]++;
        throw err;
      }
    });
    return function (_x2, _x3) {
      return _ref18.apply(this, arguments);
    };
  }(), []));
  var processCommand = (cov_122bcgj3d0().s[71]++, useCallback(function (transcript) {
    cov_122bcgj3d0().f[8]++;
    cov_122bcgj3d0().s[72]++;
    try {
      cov_122bcgj3d0().s[73]++;
      setError(null);
      cov_122bcgj3d0().s[74]++;
      return voiceService.processVoiceCommand(transcript);
    } catch (err) {
      var errorMessage = (cov_122bcgj3d0().s[75]++, err instanceof Error ? (cov_122bcgj3d0().b[11][0]++, err.message) : (cov_122bcgj3d0().b[11][1]++, 'Failed to process command'));
      cov_122bcgj3d0().s[76]++;
      setError(errorMessage);
      cov_122bcgj3d0().s[77]++;
      return null;
    }
  }, []));
  var getAvailableCommands = (cov_122bcgj3d0().s[78]++, useCallback(function () {
    cov_122bcgj3d0().f[9]++;
    cov_122bcgj3d0().s[79]++;
    return voiceService.getAvailableCommands();
  }, []));
  cov_122bcgj3d0().s[80]++;
  useEffect(function () {
    cov_122bcgj3d0().f[10]++;
    var interval = (cov_122bcgj3d0().s[81]++, setInterval(function () {
      cov_122bcgj3d0().f[11]++;
      cov_122bcgj3d0().s[82]++;
      setIsListening(voiceService.isCurrentlyListening());
      cov_122bcgj3d0().s[83]++;
      setIsSpeaking(voiceService.isCurrentlySpeaking());
    }, 500));
    cov_122bcgj3d0().s[84]++;
    return function () {
      cov_122bcgj3d0().f[12]++;
      cov_122bcgj3d0().s[85]++;
      return clearInterval(interval);
    };
  }, []);
  cov_122bcgj3d0().s[86]++;
  useEffect(function () {
    cov_122bcgj3d0().f[13]++;
    cov_122bcgj3d0().s[87]++;
    initializeVoice();
  }, [initializeVoice]);
  cov_122bcgj3d0().s[88]++;
  return {
    isListening: isListening,
    isSpeaking: isSpeaking,
    isInitialized: isInitialized,
    lastTranscript: lastTranscript,
    lastCommand: lastCommand,
    confidence: confidence,
    initializeVoice: initializeVoice,
    startListening: startListening,
    stopListening: stopListening,
    speak: speak,
    stopSpeaking: stopSpeaking,
    speakCoachingFeedback: speakCoachingFeedback,
    processCommand: processCommand,
    getAvailableCommands: getAvailableCommands,
    error: error
  };
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwidXNlRWZmZWN0Iiwidm9pY2VTZXJ2aWNlIiwidXNlVm9pY2UiLCJjb3ZfMTIyYmNnajNkMCIsImYiLCJfcmVmIiwicyIsIl9yZWYyIiwiX3NsaWNlZFRvQXJyYXkiLCJpc0xpc3RlbmluZyIsInNldElzTGlzdGVuaW5nIiwiX3JlZjMiLCJfcmVmNCIsImlzU3BlYWtpbmciLCJzZXRJc1NwZWFraW5nIiwiX3JlZjUiLCJfcmVmNiIsImlzSW5pdGlhbGl6ZWQiLCJzZXRJc0luaXRpYWxpemVkIiwiX3JlZjciLCJfcmVmOCIsImxhc3RUcmFuc2NyaXB0Iiwic2V0TGFzdFRyYW5zY3JpcHQiLCJfcmVmOSIsIl9yZWYwIiwibGFzdENvbW1hbmQiLCJzZXRMYXN0Q29tbWFuZCIsIl9yZWYxIiwiX3JlZjEwIiwiY29uZmlkZW5jZSIsInNldENvbmZpZGVuY2UiLCJfcmVmMTEiLCJfcmVmMTIiLCJlcnJvciIsInNldEVycm9yIiwiaW5pdGlhbGl6ZVZvaWNlIiwiX2FzeW5jVG9HZW5lcmF0b3IiLCJpbml0aWFsaXplZCIsImluaXRpYWxpemVWb2ljZVJlY29nbml0aW9uIiwiYiIsImVyciIsImVycm9yTWVzc2FnZSIsIkVycm9yIiwibWVzc2FnZSIsInN0YXJ0TGlzdGVuaW5nIiwicmVzdWx0IiwidHJhbnNjcmlwdCIsImlzRmluYWwiLCJjb21tYW5kIiwicHJvY2Vzc1ZvaWNlQ29tbWFuZCIsImNvbnNvbGUiLCJsb2ciLCJzdG9wTGlzdGVuaW5nIiwic3BlYWsiLCJfcmVmMTYiLCJ0ZXh0Iiwib3B0aW9ucyIsImFyZ3VtZW50cyIsImxlbmd0aCIsInVuZGVmaW5lZCIsIl94IiwiYXBwbHkiLCJzdG9wU3BlYWtpbmciLCJzcGVha0NvYWNoaW5nRmVlZGJhY2siLCJfcmVmMTgiLCJ0eXBlIiwiY3VzdG9tVGV4dCIsIl94MiIsIl94MyIsInByb2Nlc3NDb21tYW5kIiwiZ2V0QXZhaWxhYmxlQ29tbWFuZHMiLCJpbnRlcnZhbCIsInNldEludGVydmFsIiwiaXNDdXJyZW50bHlMaXN0ZW5pbmciLCJpc0N1cnJlbnRseVNwZWFraW5nIiwiY2xlYXJJbnRlcnZhbCJdLCJzb3VyY2VzIjpbInVzZVZvaWNlLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdm9pY2VTZXJ2aWNlLCBWb2ljZVJlY29nbml0aW9uUmVzdWx0LCBWb2ljZUNvbW1hbmQsIFNwZWVjaE9wdGlvbnMgfSBmcm9tICdAL3NlcnZpY2VzL3ZvaWNlU2VydmljZSc7XG5cbmludGVyZmFjZSBVc2VWb2ljZVJldHVybiB7XG4gIC8vIFZvaWNlIHJlY29nbml0aW9uIHN0YXRlXG4gIGlzTGlzdGVuaW5nOiBib29sZWFuO1xuICBpc1NwZWFraW5nOiBib29sZWFuO1xuICBpc0luaXRpYWxpemVkOiBib29sZWFuO1xuICBsYXN0VHJhbnNjcmlwdDogc3RyaW5nO1xuICBsYXN0Q29tbWFuZDogVm9pY2VDb21tYW5kIHwgbnVsbDtcbiAgY29uZmlkZW5jZTogbnVtYmVyO1xuICBcbiAgLy8gVm9pY2UgcmVjb2duaXRpb24gY29udHJvbHNcbiAgaW5pdGlhbGl6ZVZvaWNlOiAoKSA9PiBQcm9taXNlPGJvb2xlYW4+O1xuICBzdGFydExpc3RlbmluZzogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgc3RvcExpc3RlbmluZzogKCkgPT4gUHJvbWlzZTx2b2lkPjtcbiAgXG4gIC8vIFRleHQtdG8tc3BlZWNoIGNvbnRyb2xzXG4gIHNwZWFrOiAodGV4dDogc3RyaW5nLCBvcHRpb25zPzogU3BlZWNoT3B0aW9ucykgPT4gUHJvbWlzZTx2b2lkPjtcbiAgc3RvcFNwZWFraW5nOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xuICBzcGVha0NvYWNoaW5nRmVlZGJhY2s6ICh0eXBlOiAnZW5jb3VyYWdlbWVudCcgfCAnY29ycmVjdGlvbicgfCAndGlwJyB8ICdpbnN0cnVjdGlvbicsIGN1c3RvbVRleHQ/OiBzdHJpbmcpID0+IFByb21pc2U8dm9pZD47XG4gIFxuICAvLyBDb21tYW5kIHByb2Nlc3NpbmdcbiAgcHJvY2Vzc0NvbW1hbmQ6ICh0cmFuc2NyaXB0OiBzdHJpbmcpID0+IFZvaWNlQ29tbWFuZCB8IG51bGw7XG4gIGdldEF2YWlsYWJsZUNvbW1hbmRzOiAoKSA9PiBzdHJpbmdbXTtcbiAgXG4gIC8vIEVycm9yIGhhbmRsaW5nXG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlVm9pY2UoKTogVXNlVm9pY2VSZXR1cm4ge1xuICBjb25zdCBbaXNMaXN0ZW5pbmcsIHNldElzTGlzdGVuaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2lzU3BlYWtpbmcsIHNldElzU3BlYWtpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNJbml0aWFsaXplZCwgc2V0SXNJbml0aWFsaXplZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtsYXN0VHJhbnNjcmlwdCwgc2V0TGFzdFRyYW5zY3JpcHRdID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbbGFzdENvbW1hbmQsIHNldExhc3RDb21tYW5kXSA9IHVzZVN0YXRlPFZvaWNlQ29tbWFuZCB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbY29uZmlkZW5jZSwgc2V0Q29uZmlkZW5jZV0gPSB1c2VTdGF0ZSgwKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcblxuICAvKipcbiAgICogSW5pdGlhbGl6ZSB2b2ljZSByZWNvZ25pdGlvblxuICAgKi9cbiAgY29uc3QgaW5pdGlhbGl6ZVZvaWNlID0gdXNlQ2FsbGJhY2soYXN5bmMgKCk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRFcnJvcihudWxsKTtcbiAgICAgIGNvbnN0IGluaXRpYWxpemVkID0gYXdhaXQgdm9pY2VTZXJ2aWNlLmluaXRpYWxpemVWb2ljZVJlY29nbml0aW9uKCk7XG4gICAgICBzZXRJc0luaXRpYWxpemVkKGluaXRpYWxpemVkKTtcbiAgICAgIFxuICAgICAgaWYgKCFpbml0aWFsaXplZCkge1xuICAgICAgICBzZXRFcnJvcignVm9pY2UgcmVjb2duaXRpb24gbm90IGF2YWlsYWJsZSBvbiB0aGlzIGRldmljZScpO1xuICAgICAgfVxuICAgICAgXG4gICAgICByZXR1cm4gaW5pdGlhbGl6ZWQ7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBpbml0aWFsaXplIHZvaWNlIHJlY29nbml0aW9uJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICBzZXRJc0luaXRpYWxpemVkKGZhbHNlKTtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvKipcbiAgICogU3RhcnQgbGlzdGVuaW5nIGZvciB2b2ljZSBpbnB1dFxuICAgKi9cbiAgY29uc3Qgc3RhcnRMaXN0ZW5pbmcgPSB1c2VDYWxsYmFjayhhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgaWYgKCFpc0luaXRpYWxpemVkKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ1ZvaWNlIHJlY29nbml0aW9uIG5vdCBpbml0aWFsaXplZCcpO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBzZXRFcnJvcihudWxsKTtcbiAgICAgIFxuICAgICAgYXdhaXQgdm9pY2VTZXJ2aWNlLnN0YXJ0TGlzdGVuaW5nKChyZXN1bHQ6IFZvaWNlUmVjb2duaXRpb25SZXN1bHQpID0+IHtcbiAgICAgICAgc2V0TGFzdFRyYW5zY3JpcHQocmVzdWx0LnRyYW5zY3JpcHQpO1xuICAgICAgICBzZXRDb25maWRlbmNlKHJlc3VsdC5jb25maWRlbmNlKTtcbiAgICAgICAgXG4gICAgICAgIGlmIChyZXN1bHQuaXNGaW5hbCkge1xuICAgICAgICAgIC8vIFByb2Nlc3MgdGhlIGNvbW1hbmRcbiAgICAgICAgICBjb25zdCBjb21tYW5kID0gdm9pY2VTZXJ2aWNlLnByb2Nlc3NWb2ljZUNvbW1hbmQocmVzdWx0LnRyYW5zY3JpcHQpO1xuICAgICAgICAgIHNldExhc3RDb21tYW5kKGNvbW1hbmQpO1xuICAgICAgICAgIFxuICAgICAgICAgIGlmIChjb21tYW5kKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZygnVm9pY2UgY29tbWFuZCByZWNvZ25pemVkOicsIGNvbW1hbmQpO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfSk7XG4gICAgICBcbiAgICAgIHNldElzTGlzdGVuaW5nKHRydWUpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gc3RhcnQgbGlzdGVuaW5nJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICB0aHJvdyBlcnI7XG4gICAgfVxuICB9LCBbaXNJbml0aWFsaXplZF0pO1xuXG4gIC8qKlxuICAgKiBTdG9wIGxpc3RlbmluZyBmb3Igdm9pY2UgaW5wdXRcbiAgICovXG4gIGNvbnN0IHN0b3BMaXN0ZW5pbmcgPSB1c2VDYWxsYmFjayhhc3luYyAoKTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgYXdhaXQgdm9pY2VTZXJ2aWNlLnN0b3BMaXN0ZW5pbmcoKTtcbiAgICAgIHNldElzTGlzdGVuaW5nKGZhbHNlKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIHN0b3AgbGlzdGVuaW5nJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICB0aHJvdyBlcnI7XG4gICAgfVxuICB9LCBbXSk7XG5cbiAgLyoqXG4gICAqIFNwZWFrIHRleHQgdXNpbmcgdGV4dC10by1zcGVlY2hcbiAgICovXG4gIGNvbnN0IHNwZWFrID0gdXNlQ2FsbGJhY2soYXN5bmMgKHRleHQ6IHN0cmluZywgb3B0aW9uczogU3BlZWNoT3B0aW9ucyA9IHt9KTogUHJvbWlzZTx2b2lkPiA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldEVycm9yKG51bGwpO1xuICAgICAgc2V0SXNTcGVha2luZyh0cnVlKTtcbiAgICAgIFxuICAgICAgYXdhaXQgdm9pY2VTZXJ2aWNlLnNwZWFrKHRleHQsIG9wdGlvbnMpO1xuICAgICAgc2V0SXNTcGVha2luZyhmYWxzZSk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBzcGVhayB0ZXh0JztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICBzZXRJc1NwZWFraW5nKGZhbHNlKTtcbiAgICAgIHRocm93IGVycjtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvKipcbiAgICogU3RvcCBjdXJyZW50IHNwZWVjaFxuICAgKi9cbiAgY29uc3Qgc3RvcFNwZWFraW5nID0gdXNlQ2FsbGJhY2soYXN5bmMgKCk6IFByb21pc2U8dm9pZD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRFcnJvcihudWxsKTtcbiAgICAgIGF3YWl0IHZvaWNlU2VydmljZS5zdG9wU3BlYWtpbmcoKTtcbiAgICAgIHNldElzU3BlYWtpbmcoZmFsc2UpO1xuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgY29uc3QgZXJyb3JNZXNzYWdlID0gZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdGYWlsZWQgdG8gc3RvcCBzcGVha2luZyc7XG4gICAgICBzZXRFcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8qKlxuICAgKiBTcGVhayBjb2FjaGluZyBmZWVkYmFja1xuICAgKi9cbiAgY29uc3Qgc3BlYWtDb2FjaGluZ0ZlZWRiYWNrID0gdXNlQ2FsbGJhY2soYXN5bmMgKFxuICAgIHR5cGU6ICdlbmNvdXJhZ2VtZW50JyB8ICdjb3JyZWN0aW9uJyB8ICd0aXAnIHwgJ2luc3RydWN0aW9uJyxcbiAgICBjdXN0b21UZXh0Pzogc3RyaW5nXG4gICk6IFByb21pc2U8dm9pZD4gPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRFcnJvcihudWxsKTtcbiAgICAgIHNldElzU3BlYWtpbmcodHJ1ZSk7XG4gICAgICBcbiAgICAgIGF3YWl0IHZvaWNlU2VydmljZS5zcGVha0NvYWNoaW5nRmVlZGJhY2sodHlwZSwgY3VzdG9tVGV4dCk7XG4gICAgICBzZXRJc1NwZWFraW5nKGZhbHNlKTtcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIGNvbnN0IGVycm9yTWVzc2FnZSA9IGVyciBpbnN0YW5jZW9mIEVycm9yID8gZXJyLm1lc3NhZ2UgOiAnRmFpbGVkIHRvIHNwZWFrIGNvYWNoaW5nIGZlZWRiYWNrJztcbiAgICAgIHNldEVycm9yKGVycm9yTWVzc2FnZSk7XG4gICAgICBzZXRJc1NwZWFraW5nKGZhbHNlKTtcbiAgICAgIHRocm93IGVycjtcbiAgICB9XG4gIH0sIFtdKTtcblxuICAvKipcbiAgICogUHJvY2VzcyB2b2ljZSBjb21tYW5kXG4gICAqL1xuICBjb25zdCBwcm9jZXNzQ29tbWFuZCA9IHVzZUNhbGxiYWNrKCh0cmFuc2NyaXB0OiBzdHJpbmcpOiBWb2ljZUNvbW1hbmQgfCBudWxsID0+IHtcbiAgICB0cnkge1xuICAgICAgc2V0RXJyb3IobnVsbCk7XG4gICAgICByZXR1cm4gdm9pY2VTZXJ2aWNlLnByb2Nlc3NWb2ljZUNvbW1hbmQodHJhbnNjcmlwdCk7XG4gICAgfSBjYXRjaCAoZXJyKSB7XG4gICAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ0ZhaWxlZCB0byBwcm9jZXNzIGNvbW1hbmQnO1xuICAgICAgc2V0RXJyb3IoZXJyb3JNZXNzYWdlKTtcbiAgICAgIHJldHVybiBudWxsO1xuICAgIH1cbiAgfSwgW10pO1xuXG4gIC8qKlxuICAgKiBHZXQgYXZhaWxhYmxlIHZvaWNlIGNvbW1hbmRzXG4gICAqL1xuICBjb25zdCBnZXRBdmFpbGFibGVDb21tYW5kcyA9IHVzZUNhbGxiYWNrKCgpOiBzdHJpbmdbXSA9PiB7XG4gICAgcmV0dXJuIHZvaWNlU2VydmljZS5nZXRBdmFpbGFibGVDb21tYW5kcygpO1xuICB9LCBbXSk7XG5cbiAgLy8gVXBkYXRlIGxpc3RlbmluZyBhbmQgc3BlYWtpbmcgc3RhdGUgYmFzZWQgb24gc2VydmljZSBzdGF0ZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgICAgc2V0SXNMaXN0ZW5pbmcodm9pY2VTZXJ2aWNlLmlzQ3VycmVudGx5TGlzdGVuaW5nKCkpO1xuICAgICAgc2V0SXNTcGVha2luZyh2b2ljZVNlcnZpY2UuaXNDdXJyZW50bHlTcGVha2luZygpKTtcbiAgICB9LCA1MDApO1xuXG4gICAgcmV0dXJuICgpID0+IGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICB9LCBbXSk7XG5cbiAgLy8gSW5pdGlhbGl6ZSB2b2ljZSBzZXJ2aWNlIG9uIG1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaW5pdGlhbGl6ZVZvaWNlKCk7XG4gIH0sIFtpbml0aWFsaXplVm9pY2VdKTtcblxuICByZXR1cm4ge1xuICAgIC8vIFZvaWNlIHJlY29nbml0aW9uIHN0YXRlXG4gICAgaXNMaXN0ZW5pbmcsXG4gICAgaXNTcGVha2luZyxcbiAgICBpc0luaXRpYWxpemVkLFxuICAgIGxhc3RUcmFuc2NyaXB0LFxuICAgIGxhc3RDb21tYW5kLFxuICAgIGNvbmZpZGVuY2UsXG4gICAgXG4gICAgLy8gVm9pY2UgcmVjb2duaXRpb24gY29udHJvbHNcbiAgICBpbml0aWFsaXplVm9pY2UsXG4gICAgc3RhcnRMaXN0ZW5pbmcsXG4gICAgc3RvcExpc3RlbmluZyxcbiAgICBcbiAgICAvLyBUZXh0LXRvLXNwZWVjaCBjb250cm9sc1xuICAgIHNwZWFrLFxuICAgIHN0b3BTcGVha2luZyxcbiAgICBzcGVha0NvYWNoaW5nRmVlZGJhY2ssXG4gICAgXG4gICAgLy8gQ29tbWFuZCBwcm9jZXNzaW5nXG4gICAgcHJvY2Vzc0NvbW1hbmQsXG4gICAgZ2V0QXZhaWxhYmxlQ29tbWFuZHMsXG4gICAgXG4gICAgLy8gRXJyb3IgaGFuZGxpbmdcbiAgICBlcnJvcixcbiAgfTtcbn1cbiJdLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBLFNBQVNBLFFBQVEsRUFBRUMsV0FBVyxFQUFFQyxTQUFTLFFBQVEsT0FBTztBQUN4RCxTQUFTQyxZQUFZO0FBNkJyQixPQUFPLFNBQVNDLFFBQVFBLENBQUEsRUFBbUI7RUFBQUMsY0FBQSxHQUFBQyxDQUFBO0VBQ3pDLElBQUFDLElBQUEsSUFBQUYsY0FBQSxHQUFBRyxDQUFBLE9BQXNDUixRQUFRLENBQUMsS0FBSyxDQUFDO0lBQUFTLEtBQUEsR0FBQUMsY0FBQSxDQUFBSCxJQUFBO0lBQTlDSSxXQUFXLEdBQUFGLEtBQUE7SUFBRUcsY0FBYyxHQUFBSCxLQUFBO0VBQ2xDLElBQUFJLEtBQUEsSUFBQVIsY0FBQSxHQUFBRyxDQUFBLE9BQW9DUixRQUFRLENBQUMsS0FBSyxDQUFDO0lBQUFjLEtBQUEsR0FBQUosY0FBQSxDQUFBRyxLQUFBO0lBQTVDRSxVQUFVLEdBQUFELEtBQUE7SUFBRUUsYUFBYSxHQUFBRixLQUFBO0VBQ2hDLElBQUFHLEtBQUEsSUFBQVosY0FBQSxHQUFBRyxDQUFBLE9BQTBDUixRQUFRLENBQUMsS0FBSyxDQUFDO0lBQUFrQixLQUFBLEdBQUFSLGNBQUEsQ0FBQU8sS0FBQTtJQUFsREUsYUFBYSxHQUFBRCxLQUFBO0lBQUVFLGdCQUFnQixHQUFBRixLQUFBO0VBQ3RDLElBQUFHLEtBQUEsSUFBQWhCLGNBQUEsR0FBQUcsQ0FBQSxPQUE0Q1IsUUFBUSxDQUFDLEVBQUUsQ0FBQztJQUFBc0IsS0FBQSxHQUFBWixjQUFBLENBQUFXLEtBQUE7SUFBakRFLGNBQWMsR0FBQUQsS0FBQTtJQUFFRSxpQkFBaUIsR0FBQUYsS0FBQTtFQUN4QyxJQUFBRyxLQUFBLElBQUFwQixjQUFBLEdBQUFHLENBQUEsT0FBc0NSLFFBQVEsQ0FBc0IsSUFBSSxDQUFDO0lBQUEwQixLQUFBLEdBQUFoQixjQUFBLENBQUFlLEtBQUE7SUFBbEVFLFdBQVcsR0FBQUQsS0FBQTtJQUFFRSxjQUFjLEdBQUFGLEtBQUE7RUFDbEMsSUFBQUcsS0FBQSxJQUFBeEIsY0FBQSxHQUFBRyxDQUFBLE9BQW9DUixRQUFRLENBQUMsQ0FBQyxDQUFDO0lBQUE4QixNQUFBLEdBQUFwQixjQUFBLENBQUFtQixLQUFBO0lBQXhDRSxVQUFVLEdBQUFELE1BQUE7SUFBRUUsYUFBYSxHQUFBRixNQUFBO0VBQ2hDLElBQUFHLE1BQUEsSUFBQTVCLGNBQUEsR0FBQUcsQ0FBQSxPQUEwQlIsUUFBUSxDQUFnQixJQUFJLENBQUM7SUFBQWtDLE1BQUEsR0FBQXhCLGNBQUEsQ0FBQXVCLE1BQUE7SUFBaERFLEtBQUssR0FBQUQsTUFBQTtJQUFFRSxRQUFRLEdBQUFGLE1BQUE7RUFLdEIsSUFBTUcsZUFBZSxJQUFBaEMsY0FBQSxHQUFBRyxDQUFBLE9BQUdQLFdBQVcsQ0FBQXFDLGlCQUFBLENBQUMsYUFBOEI7SUFBQWpDLGNBQUEsR0FBQUMsQ0FBQTtJQUFBRCxjQUFBLEdBQUFHLENBQUE7SUFDaEUsSUFBSTtNQUFBSCxjQUFBLEdBQUFHLENBQUE7TUFDRjRCLFFBQVEsQ0FBQyxJQUFJLENBQUM7TUFDZCxJQUFNRyxXQUFXLElBQUFsQyxjQUFBLEdBQUFHLENBQUEsY0FBU0wsWUFBWSxDQUFDcUMsMEJBQTBCLENBQUMsQ0FBQztNQUFDbkMsY0FBQSxHQUFBRyxDQUFBO01BQ3BFWSxnQkFBZ0IsQ0FBQ21CLFdBQVcsQ0FBQztNQUFDbEMsY0FBQSxHQUFBRyxDQUFBO01BRTlCLElBQUksQ0FBQytCLFdBQVcsRUFBRTtRQUFBbEMsY0FBQSxHQUFBb0MsQ0FBQTtRQUFBcEMsY0FBQSxHQUFBRyxDQUFBO1FBQ2hCNEIsUUFBUSxDQUFDLGdEQUFnRCxDQUFDO01BQzVELENBQUM7UUFBQS9CLGNBQUEsR0FBQW9DLENBQUE7TUFBQTtNQUFBcEMsY0FBQSxHQUFBRyxDQUFBO01BRUQsT0FBTytCLFdBQVc7SUFDcEIsQ0FBQyxDQUFDLE9BQU9HLEdBQUcsRUFBRTtNQUNaLElBQU1DLFlBQVksSUFBQXRDLGNBQUEsR0FBQUcsQ0FBQSxRQUFHa0MsR0FBRyxZQUFZRSxLQUFLLElBQUF2QyxjQUFBLEdBQUFvQyxDQUFBLFVBQUdDLEdBQUcsQ0FBQ0csT0FBTyxLQUFBeEMsY0FBQSxHQUFBb0MsQ0FBQSxVQUFHLHdDQUF3QztNQUFDcEMsY0FBQSxHQUFBRyxDQUFBO01BQ25HNEIsUUFBUSxDQUFDTyxZQUFZLENBQUM7TUFBQ3RDLGNBQUEsR0FBQUcsQ0FBQTtNQUN2QlksZ0JBQWdCLENBQUMsS0FBSyxDQUFDO01BQUNmLGNBQUEsR0FBQUcsQ0FBQTtNQUN4QixPQUFPLEtBQUs7SUFDZDtFQUNGLENBQUMsR0FBRSxFQUFFLENBQUM7RUFLTixJQUFNc0MsY0FBYyxJQUFBekMsY0FBQSxHQUFBRyxDQUFBLFFBQUdQLFdBQVcsQ0FBQXFDLGlCQUFBLENBQUMsYUFBMkI7SUFBQWpDLGNBQUEsR0FBQUMsQ0FBQTtJQUFBRCxjQUFBLEdBQUFHLENBQUE7SUFDNUQsSUFBSSxDQUFDVyxhQUFhLEVBQUU7TUFBQWQsY0FBQSxHQUFBb0MsQ0FBQTtNQUFBcEMsY0FBQSxHQUFBRyxDQUFBO01BQ2xCLE1BQU0sSUFBSW9DLEtBQUssQ0FBQyxtQ0FBbUMsQ0FBQztJQUN0RCxDQUFDO01BQUF2QyxjQUFBLEdBQUFvQyxDQUFBO0lBQUE7SUFBQXBDLGNBQUEsR0FBQUcsQ0FBQTtJQUVELElBQUk7TUFBQUgsY0FBQSxHQUFBRyxDQUFBO01BQ0Y0QixRQUFRLENBQUMsSUFBSSxDQUFDO01BQUMvQixjQUFBLEdBQUFHLENBQUE7TUFFZixNQUFNTCxZQUFZLENBQUMyQyxjQUFjLENBQUMsVUFBQ0MsTUFBOEIsRUFBSztRQUFBMUMsY0FBQSxHQUFBQyxDQUFBO1FBQUFELGNBQUEsR0FBQUcsQ0FBQTtRQUNwRWdCLGlCQUFpQixDQUFDdUIsTUFBTSxDQUFDQyxVQUFVLENBQUM7UUFBQzNDLGNBQUEsR0FBQUcsQ0FBQTtRQUNyQ3dCLGFBQWEsQ0FBQ2UsTUFBTSxDQUFDaEIsVUFBVSxDQUFDO1FBQUMxQixjQUFBLEdBQUFHLENBQUE7UUFFakMsSUFBSXVDLE1BQU0sQ0FBQ0UsT0FBTyxFQUFFO1VBQUE1QyxjQUFBLEdBQUFvQyxDQUFBO1VBRWxCLElBQU1TLE9BQU8sSUFBQTdDLGNBQUEsR0FBQUcsQ0FBQSxRQUFHTCxZQUFZLENBQUNnRCxtQkFBbUIsQ0FBQ0osTUFBTSxDQUFDQyxVQUFVLENBQUM7VUFBQzNDLGNBQUEsR0FBQUcsQ0FBQTtVQUNwRW9CLGNBQWMsQ0FBQ3NCLE9BQU8sQ0FBQztVQUFDN0MsY0FBQSxHQUFBRyxDQUFBO1VBRXhCLElBQUkwQyxPQUFPLEVBQUU7WUFBQTdDLGNBQUEsR0FBQW9DLENBQUE7WUFBQXBDLGNBQUEsR0FBQUcsQ0FBQTtZQUNYNEMsT0FBTyxDQUFDQyxHQUFHLENBQUMsMkJBQTJCLEVBQUVILE9BQU8sQ0FBQztVQUNuRCxDQUFDO1lBQUE3QyxjQUFBLEdBQUFvQyxDQUFBO1VBQUE7UUFDSCxDQUFDO1VBQUFwQyxjQUFBLEdBQUFvQyxDQUFBO1FBQUE7TUFDSCxDQUFDLENBQUM7TUFBQ3BDLGNBQUEsR0FBQUcsQ0FBQTtNQUVISSxjQUFjLENBQUMsSUFBSSxDQUFDO0lBQ3RCLENBQUMsQ0FBQyxPQUFPOEIsR0FBRyxFQUFFO01BQ1osSUFBTUMsWUFBWSxJQUFBdEMsY0FBQSxHQUFBRyxDQUFBLFFBQUdrQyxHQUFHLFlBQVlFLEtBQUssSUFBQXZDLGNBQUEsR0FBQW9DLENBQUEsVUFBR0MsR0FBRyxDQUFDRyxPQUFPLEtBQUF4QyxjQUFBLEdBQUFvQyxDQUFBLFVBQUcsMkJBQTJCO01BQUNwQyxjQUFBLEdBQUFHLENBQUE7TUFDdEY0QixRQUFRLENBQUNPLFlBQVksQ0FBQztNQUFDdEMsY0FBQSxHQUFBRyxDQUFBO01BQ3ZCLE1BQU1rQyxHQUFHO0lBQ1g7RUFDRixDQUFDLEdBQUUsQ0FBQ3ZCLGFBQWEsQ0FBQyxDQUFDO0VBS25CLElBQU1tQyxhQUFhLElBQUFqRCxjQUFBLEdBQUFHLENBQUEsUUFBR1AsV0FBVyxDQUFBcUMsaUJBQUEsQ0FBQyxhQUEyQjtJQUFBakMsY0FBQSxHQUFBQyxDQUFBO0lBQUFELGNBQUEsR0FBQUcsQ0FBQTtJQUMzRCxJQUFJO01BQUFILGNBQUEsR0FBQUcsQ0FBQTtNQUNGNEIsUUFBUSxDQUFDLElBQUksQ0FBQztNQUFDL0IsY0FBQSxHQUFBRyxDQUFBO01BQ2YsTUFBTUwsWUFBWSxDQUFDbUQsYUFBYSxDQUFDLENBQUM7TUFBQ2pELGNBQUEsR0FBQUcsQ0FBQTtNQUNuQ0ksY0FBYyxDQUFDLEtBQUssQ0FBQztJQUN2QixDQUFDLENBQUMsT0FBTzhCLEdBQUcsRUFBRTtNQUNaLElBQU1DLFlBQVksSUFBQXRDLGNBQUEsR0FBQUcsQ0FBQSxRQUFHa0MsR0FBRyxZQUFZRSxLQUFLLElBQUF2QyxjQUFBLEdBQUFvQyxDQUFBLFVBQUdDLEdBQUcsQ0FBQ0csT0FBTyxLQUFBeEMsY0FBQSxHQUFBb0MsQ0FBQSxVQUFHLDBCQUEwQjtNQUFDcEMsY0FBQSxHQUFBRyxDQUFBO01BQ3JGNEIsUUFBUSxDQUFDTyxZQUFZLENBQUM7TUFBQ3RDLGNBQUEsR0FBQUcsQ0FBQTtNQUN2QixNQUFNa0MsR0FBRztJQUNYO0VBQ0YsQ0FBQyxHQUFFLEVBQUUsQ0FBQztFQUtOLElBQU1hLEtBQUssSUFBQWxELGNBQUEsR0FBQUcsQ0FBQSxRQUFHUCxXQUFXO0lBQUEsSUFBQXVELE1BQUEsR0FBQWxCLGlCQUFBLENBQUMsV0FBT21CLElBQVksRUFBaUQ7TUFBQSxJQUEvQ0MsT0FBc0IsR0FBQUMsU0FBQSxDQUFBQyxNQUFBLFFBQUFELFNBQUEsUUFBQUUsU0FBQSxHQUFBRixTQUFBLE9BQUF0RCxjQUFBLEdBQUFvQyxDQUFBLFVBQUcsQ0FBQyxDQUFDO01BQUFwQyxjQUFBLEdBQUFDLENBQUE7TUFBQUQsY0FBQSxHQUFBRyxDQUFBO01BQ3hFLElBQUk7UUFBQUgsY0FBQSxHQUFBRyxDQUFBO1FBQ0Y0QixRQUFRLENBQUMsSUFBSSxDQUFDO1FBQUMvQixjQUFBLEdBQUFHLENBQUE7UUFDZlEsYUFBYSxDQUFDLElBQUksQ0FBQztRQUFDWCxjQUFBLEdBQUFHLENBQUE7UUFFcEIsTUFBTUwsWUFBWSxDQUFDb0QsS0FBSyxDQUFDRSxJQUFJLEVBQUVDLE9BQU8sQ0FBQztRQUFDckQsY0FBQSxHQUFBRyxDQUFBO1FBQ3hDUSxhQUFhLENBQUMsS0FBSyxDQUFDO01BQ3RCLENBQUMsQ0FBQyxPQUFPMEIsR0FBRyxFQUFFO1FBQ1osSUFBTUMsWUFBWSxJQUFBdEMsY0FBQSxHQUFBRyxDQUFBLFFBQUdrQyxHQUFHLFlBQVlFLEtBQUssSUFBQXZDLGNBQUEsR0FBQW9DLENBQUEsVUFBR0MsR0FBRyxDQUFDRyxPQUFPLEtBQUF4QyxjQUFBLEdBQUFvQyxDQUFBLFVBQUcsc0JBQXNCO1FBQUNwQyxjQUFBLEdBQUFHLENBQUE7UUFDakY0QixRQUFRLENBQUNPLFlBQVksQ0FBQztRQUFDdEMsY0FBQSxHQUFBRyxDQUFBO1FBQ3ZCUSxhQUFhLENBQUMsS0FBSyxDQUFDO1FBQUNYLGNBQUEsR0FBQUcsQ0FBQTtRQUNyQixNQUFNa0MsR0FBRztNQUNYO0lBQ0YsQ0FBQztJQUFBLGlCQUFBb0IsRUFBQTtNQUFBLE9BQUFOLE1BQUEsQ0FBQU8sS0FBQSxPQUFBSixTQUFBO0lBQUE7RUFBQSxLQUFFLEVBQUUsQ0FBQztFQUtOLElBQU1LLFlBQVksSUFBQTNELGNBQUEsR0FBQUcsQ0FBQSxRQUFHUCxXQUFXLENBQUFxQyxpQkFBQSxDQUFDLGFBQTJCO0lBQUFqQyxjQUFBLEdBQUFDLENBQUE7SUFBQUQsY0FBQSxHQUFBRyxDQUFBO0lBQzFELElBQUk7TUFBQUgsY0FBQSxHQUFBRyxDQUFBO01BQ0Y0QixRQUFRLENBQUMsSUFBSSxDQUFDO01BQUMvQixjQUFBLEdBQUFHLENBQUE7TUFDZixNQUFNTCxZQUFZLENBQUM2RCxZQUFZLENBQUMsQ0FBQztNQUFDM0QsY0FBQSxHQUFBRyxDQUFBO01BQ2xDUSxhQUFhLENBQUMsS0FBSyxDQUFDO0lBQ3RCLENBQUMsQ0FBQyxPQUFPMEIsR0FBRyxFQUFFO01BQ1osSUFBTUMsWUFBWSxJQUFBdEMsY0FBQSxHQUFBRyxDQUFBLFFBQUdrQyxHQUFHLFlBQVlFLEtBQUssSUFBQXZDLGNBQUEsR0FBQW9DLENBQUEsVUFBR0MsR0FBRyxDQUFDRyxPQUFPLEtBQUF4QyxjQUFBLEdBQUFvQyxDQUFBLFVBQUcseUJBQXlCO01BQUNwQyxjQUFBLEdBQUFHLENBQUE7TUFDcEY0QixRQUFRLENBQUNPLFlBQVksQ0FBQztJQUN4QjtFQUNGLENBQUMsR0FBRSxFQUFFLENBQUM7RUFLTixJQUFNc0IscUJBQXFCLElBQUE1RCxjQUFBLEdBQUFHLENBQUEsUUFBR1AsV0FBVztJQUFBLElBQUFpRSxNQUFBLEdBQUE1QixpQkFBQSxDQUFDLFdBQ3hDNkIsSUFBNEQsRUFDNURDLFVBQW1CLEVBQ0Q7TUFBQS9ELGNBQUEsR0FBQUMsQ0FBQTtNQUFBRCxjQUFBLEdBQUFHLENBQUE7TUFDbEIsSUFBSTtRQUFBSCxjQUFBLEdBQUFHLENBQUE7UUFDRjRCLFFBQVEsQ0FBQyxJQUFJLENBQUM7UUFBQy9CLGNBQUEsR0FBQUcsQ0FBQTtRQUNmUSxhQUFhLENBQUMsSUFBSSxDQUFDO1FBQUNYLGNBQUEsR0FBQUcsQ0FBQTtRQUVwQixNQUFNTCxZQUFZLENBQUM4RCxxQkFBcUIsQ0FBQ0UsSUFBSSxFQUFFQyxVQUFVLENBQUM7UUFBQy9ELGNBQUEsR0FBQUcsQ0FBQTtRQUMzRFEsYUFBYSxDQUFDLEtBQUssQ0FBQztNQUN0QixDQUFDLENBQUMsT0FBTzBCLEdBQUcsRUFBRTtRQUNaLElBQU1DLFlBQVksSUFBQXRDLGNBQUEsR0FBQUcsQ0FBQSxRQUFHa0MsR0FBRyxZQUFZRSxLQUFLLElBQUF2QyxjQUFBLEdBQUFvQyxDQUFBLFdBQUdDLEdBQUcsQ0FBQ0csT0FBTyxLQUFBeEMsY0FBQSxHQUFBb0MsQ0FBQSxXQUFHLG1DQUFtQztRQUFDcEMsY0FBQSxHQUFBRyxDQUFBO1FBQzlGNEIsUUFBUSxDQUFDTyxZQUFZLENBQUM7UUFBQ3RDLGNBQUEsR0FBQUcsQ0FBQTtRQUN2QlEsYUFBYSxDQUFDLEtBQUssQ0FBQztRQUFDWCxjQUFBLEdBQUFHLENBQUE7UUFDckIsTUFBTWtDLEdBQUc7TUFDWDtJQUNGLENBQUM7SUFBQSxpQkFBQTJCLEdBQUEsRUFBQUMsR0FBQTtNQUFBLE9BQUFKLE1BQUEsQ0FBQUgsS0FBQSxPQUFBSixTQUFBO0lBQUE7RUFBQSxLQUFFLEVBQUUsQ0FBQztFQUtOLElBQU1ZLGNBQWMsSUFBQWxFLGNBQUEsR0FBQUcsQ0FBQSxRQUFHUCxXQUFXLENBQUMsVUFBQytDLFVBQWtCLEVBQTBCO0lBQUEzQyxjQUFBLEdBQUFDLENBQUE7SUFBQUQsY0FBQSxHQUFBRyxDQUFBO0lBQzlFLElBQUk7TUFBQUgsY0FBQSxHQUFBRyxDQUFBO01BQ0Y0QixRQUFRLENBQUMsSUFBSSxDQUFDO01BQUMvQixjQUFBLEdBQUFHLENBQUE7TUFDZixPQUFPTCxZQUFZLENBQUNnRCxtQkFBbUIsQ0FBQ0gsVUFBVSxDQUFDO0lBQ3JELENBQUMsQ0FBQyxPQUFPTixHQUFHLEVBQUU7TUFDWixJQUFNQyxZQUFZLElBQUF0QyxjQUFBLEdBQUFHLENBQUEsUUFBR2tDLEdBQUcsWUFBWUUsS0FBSyxJQUFBdkMsY0FBQSxHQUFBb0MsQ0FBQSxXQUFHQyxHQUFHLENBQUNHLE9BQU8sS0FBQXhDLGNBQUEsR0FBQW9DLENBQUEsV0FBRywyQkFBMkI7TUFBQ3BDLGNBQUEsR0FBQUcsQ0FBQTtNQUN0RjRCLFFBQVEsQ0FBQ08sWUFBWSxDQUFDO01BQUN0QyxjQUFBLEdBQUFHLENBQUE7TUFDdkIsT0FBTyxJQUFJO0lBQ2I7RUFDRixDQUFDLEVBQUUsRUFBRSxDQUFDO0VBS04sSUFBTWdFLG9CQUFvQixJQUFBbkUsY0FBQSxHQUFBRyxDQUFBLFFBQUdQLFdBQVcsQ0FBQyxZQUFnQjtJQUFBSSxjQUFBLEdBQUFDLENBQUE7SUFBQUQsY0FBQSxHQUFBRyxDQUFBO0lBQ3ZELE9BQU9MLFlBQVksQ0FBQ3FFLG9CQUFvQixDQUFDLENBQUM7RUFDNUMsQ0FBQyxFQUFFLEVBQUUsQ0FBQztFQUFDbkUsY0FBQSxHQUFBRyxDQUFBO0VBR1BOLFNBQVMsQ0FBQyxZQUFNO0lBQUFHLGNBQUEsR0FBQUMsQ0FBQTtJQUNkLElBQU1tRSxRQUFRLElBQUFwRSxjQUFBLEdBQUFHLENBQUEsUUFBR2tFLFdBQVcsQ0FBQyxZQUFNO01BQUFyRSxjQUFBLEdBQUFDLENBQUE7TUFBQUQsY0FBQSxHQUFBRyxDQUFBO01BQ2pDSSxjQUFjLENBQUNULFlBQVksQ0FBQ3dFLG9CQUFvQixDQUFDLENBQUMsQ0FBQztNQUFDdEUsY0FBQSxHQUFBRyxDQUFBO01BQ3BEUSxhQUFhLENBQUNiLFlBQVksQ0FBQ3lFLG1CQUFtQixDQUFDLENBQUMsQ0FBQztJQUNuRCxDQUFDLEVBQUUsR0FBRyxDQUFDO0lBQUN2RSxjQUFBLEdBQUFHLENBQUE7SUFFUixPQUFPLFlBQU07TUFBQUgsY0FBQSxHQUFBQyxDQUFBO01BQUFELGNBQUEsR0FBQUcsQ0FBQTtNQUFBLE9BQUFxRSxhQUFhLENBQUNKLFFBQVEsQ0FBQztJQUFELENBQUM7RUFDdEMsQ0FBQyxFQUFFLEVBQUUsQ0FBQztFQUFDcEUsY0FBQSxHQUFBRyxDQUFBO0VBR1BOLFNBQVMsQ0FBQyxZQUFNO0lBQUFHLGNBQUEsR0FBQUMsQ0FBQTtJQUFBRCxjQUFBLEdBQUFHLENBQUE7SUFDZDZCLGVBQWUsQ0FBQyxDQUFDO0VBQ25CLENBQUMsRUFBRSxDQUFDQSxlQUFlLENBQUMsQ0FBQztFQUFDaEMsY0FBQSxHQUFBRyxDQUFBO0VBRXRCLE9BQU87SUFFTEcsV0FBVyxFQUFYQSxXQUFXO0lBQ1hJLFVBQVUsRUFBVkEsVUFBVTtJQUNWSSxhQUFhLEVBQWJBLGFBQWE7SUFDYkksY0FBYyxFQUFkQSxjQUFjO0lBQ2RJLFdBQVcsRUFBWEEsV0FBVztJQUNYSSxVQUFVLEVBQVZBLFVBQVU7SUFHVk0sZUFBZSxFQUFmQSxlQUFlO0lBQ2ZTLGNBQWMsRUFBZEEsY0FBYztJQUNkUSxhQUFhLEVBQWJBLGFBQWE7SUFHYkMsS0FBSyxFQUFMQSxLQUFLO0lBQ0xTLFlBQVksRUFBWkEsWUFBWTtJQUNaQyxxQkFBcUIsRUFBckJBLHFCQUFxQjtJQUdyQk0sY0FBYyxFQUFkQSxjQUFjO0lBQ2RDLG9CQUFvQixFQUFwQkEsb0JBQW9CO0lBR3BCckMsS0FBSyxFQUFMQTtFQUNGLENBQUM7QUFDSCIsImlnbm9yZUxpc3QiOltdfQ==