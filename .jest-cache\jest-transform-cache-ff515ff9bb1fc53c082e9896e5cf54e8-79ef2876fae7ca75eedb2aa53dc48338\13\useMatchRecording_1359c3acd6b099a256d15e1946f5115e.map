{"version": 3, "names": ["useState", "useEffect", "useCallback", "useRef", "matchRecordingService", "videoRecordingService", "useAuth", "defaultVideoConfig", "cov_ip539199d", "s", "quality", "fps", "resolution", "codec", "maxDurationMinutes", "maxFileSizeMB", "enableAudio", "enableStabilization", "defaultRecordingOptions", "enableVideoRecording", "enableAutoScoreDetection", "videoConfig", "enableStatisticsTracking", "useMatchRecording", "f", "_ref", "user", "_ref2", "session", "isRecording", "isPaused", "currentScore", "sets", "finalScore", "result", "setsWon", "setsLost", "error", "loading", "_ref3", "_slicedToArray", "state", "setState", "_ref4", "_ref5", "recordingProgress", "setRecordingProgress", "sessionRef", "initializeServices", "_ref6", "_asyncToGenerator", "initialize", "setProgressCallback", "addSessionListener", "current", "prev", "Object", "assign", "b", "addScoreListener", "score", "console", "Error", "message", "apply", "arguments", "cleanup", "startMatch", "_ref7", "metadata", "options", "length", "undefined", "fullOptions", "fullMetadata", "userId", "id", "errorMessage", "_x", "endMatch", "pauseMatch", "resumeMatch", "cancelMatch", "addPoint", "_ref10", "winner", "eventType", "shotType", "_x2", "undoLastPoint", "log", "toggleVideoRecording", "_sessionRef$current", "videoRecordingActive", "stopRecording", "startRecording", "updateVideoConfig", "config", "validateMatchForm", "formData", "_formData$opponentNam", "errors", "<PERSON><PERSON><PERSON>", "trim", "matchType", "matchFormat", "surface", "createMatchMetadata", "location", "<PERSON><PERSON><PERSON>", "weatherConditions", "temperature", "tournamentName", "tournamentRound", "matchDate", "Date", "toISOString", "split", "getMatchDuration", "currentTime", "now", "startTime", "pausedDuration", "totalPausedDuration", "Math", "floor", "getFormattedScore", "map", "set", "userGames", "<PERSON><PERSON><PERSON><PERSON>", "join", "isMatchInProgress", "canAddPoint", "currentSession"], "sources": ["useMatchRecording.ts"], "sourcesContent": ["/**\n * Match Recording Hook\n * React hook for managing tennis match recording state and operations\n */\n\nimport { useState, useEffect, useCallback, useRef } from 'react';\nimport { \n  MatchRecording, \n  MatchSession, \n  MatchMetadata, \n  MatchScore, \n  MatchRecordingState,\n  VideoRecordingConfig,\n  MatchFormData,\n  MatchValidationErrors\n} from '@/src/types/match';\nimport { matchRecordingService, MatchRecordingOptions } from '@/src/services/match/MatchRecordingService';\nimport { videoRecordingService, RecordingProgress } from '@/src/services/video/VideoRecordingService';\nimport { useAuth } from '@/contexts/AuthContext';\n\nexport interface UseMatchRecordingReturn {\n  // State\n  state: MatchRecordingState;\n  currentSession: MatchSession | null;\n  recordingProgress: RecordingProgress | null;\n  \n  // Actions\n  startMatch: (metadata: MatchMetadata, options?: Partial<MatchRecordingOptions>) => Promise<void>;\n  endMatch: () => Promise<MatchRecording | null>;\n  pauseMatch: () => Promise<void>;\n  resumeMatch: () => Promise<void>;\n  cancelMatch: () => Promise<void>;\n  \n  // Score management\n  addPoint: (winner: 'user' | 'opponent', eventType?: string, shotType?: string) => Promise<void>;\n  undoLastPoint: () => Promise<void>;\n  \n  // Video controls\n  toggleVideoRecording: () => Promise<void>;\n  updateVideoConfig: (config: Partial<VideoRecordingConfig>) => void;\n  \n  // Form helpers\n  validateMatchForm: (formData: MatchFormData) => MatchValidationErrors;\n  createMatchMetadata: (formData: MatchFormData) => MatchMetadata;\n  \n  // Utilities\n  getMatchDuration: () => number;\n  getFormattedScore: () => string;\n  isMatchInProgress: () => boolean;\n  canAddPoint: () => boolean;\n}\n\nconst defaultVideoConfig: VideoRecordingConfig = {\n  quality: 'medium',\n  fps: 30,\n  resolution: '720p',\n  codec: 'h264',\n  maxDurationMinutes: 180, // 3 hours\n  maxFileSizeMB: 500,\n  enableAudio: true,\n  enableStabilization: true,\n};\n\nconst defaultRecordingOptions: MatchRecordingOptions = {\n  enableVideoRecording: true,\n  enableAutoScoreDetection: false,\n  videoConfig: defaultVideoConfig,\n  enableStatisticsTracking: true,\n};\n\nexport function useMatchRecording(): UseMatchRecordingReturn {\n  const { user } = useAuth();\n  \n  // State\n  const [state, setState] = useState<MatchRecordingState>({\n    session: null,\n    isRecording: false,\n    isPaused: false,\n    currentScore: {\n      sets: [],\n      finalScore: '',\n      result: 'win',\n      setsWon: 0,\n      setsLost: 0,\n    },\n    videoConfig: defaultVideoConfig,\n    error: null,\n    loading: false,\n  });\n  \n  const [recordingProgress, setRecordingProgress] = useState<RecordingProgress | null>(null);\n  const sessionRef = useRef<MatchSession | null>(null);\n\n  // Initialize services and listeners\n  useEffect(() => {\n    const initializeServices = async () => {\n      try {\n        await videoRecordingService.initialize();\n        \n        // Set up video progress callback\n        videoRecordingService.setProgressCallback(setRecordingProgress);\n        \n        // Set up match session listener\n        matchRecordingService.addSessionListener((session) => {\n          sessionRef.current = session;\n          setState(prev => ({\n            ...prev,\n            session,\n            isRecording: session?.isRecording || false,\n            isPaused: session?.isPaused || false,\n          }));\n        });\n        \n        // Set up score listener\n        matchRecordingService.addScoreListener((score) => {\n          setState(prev => ({\n            ...prev,\n            currentScore: score,\n          }));\n        });\n        \n      } catch (error) {\n        console.error('Failed to initialize match recording services:', error);\n        setState(prev => ({\n          ...prev,\n          error: error instanceof Error ? error.message : 'Failed to initialize services',\n        }));\n      }\n    };\n\n    initializeServices();\n\n    // Cleanup\n    return () => {\n      videoRecordingService.cleanup();\n    };\n  }, []);\n\n  // Start match recording\n  const startMatch = useCallback(async (\n    metadata: MatchMetadata, \n    options: Partial<MatchRecordingOptions> = {}\n  ) => {\n    if (!user) {\n      throw new Error('User must be authenticated to start match recording');\n    }\n\n    try {\n      setState(prev => ({ ...prev, loading: true, error: null }));\n      \n      const fullOptions = { ...defaultRecordingOptions, ...options };\n      const fullMetadata = { ...metadata, userId: user.id };\n      \n      await matchRecordingService.startMatch(fullMetadata, fullOptions);\n      \n      setState(prev => ({ ...prev, loading: false }));\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to start match';\n      setState(prev => ({ \n        ...prev, \n        loading: false, \n        error: errorMessage \n      }));\n      throw error;\n    }\n  }, [user]);\n\n  // End match recording\n  const endMatch = useCallback(async (): Promise<MatchRecording | null> => {\n    try {\n      setState(prev => ({ ...prev, loading: true, error: null }));\n      \n      const result = await matchRecordingService.endMatch();\n      \n      setState(prev => ({ \n        ...prev, \n        loading: false,\n        session: null,\n        isRecording: false,\n        isPaused: false,\n      }));\n      \n      setRecordingProgress(null);\n      return result;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to end match';\n      setState(prev => ({ \n        ...prev, \n        loading: false, \n        error: errorMessage \n      }));\n      return null;\n    }\n  }, []);\n\n  // Pause match\n  const pauseMatch = useCallback(async () => {\n    try {\n      await matchRecordingService.pauseMatch();\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to pause match';\n      setState(prev => ({ ...prev, error: errorMessage }));\n    }\n  }, []);\n\n  // Resume match\n  const resumeMatch = useCallback(async () => {\n    try {\n      await matchRecordingService.resumeMatch();\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to resume match';\n      setState(prev => ({ ...prev, error: errorMessage }));\n    }\n  }, []);\n\n  // Cancel match\n  const cancelMatch = useCallback(async () => {\n    try {\n      setState(prev => ({ ...prev, loading: true, error: null }));\n      \n      await matchRecordingService.cancelMatch();\n      \n      setState(prev => ({ \n        ...prev, \n        loading: false,\n        session: null,\n        isRecording: false,\n        isPaused: false,\n      }));\n      \n      setRecordingProgress(null);\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to cancel match';\n      setState(prev => ({ \n        ...prev, \n        loading: false, \n        error: errorMessage \n      }));\n    }\n  }, []);\n\n  // Add point\n  const addPoint = useCallback(async (\n    winner: 'user' | 'opponent', \n    eventType: string = 'normal',\n    shotType?: string\n  ) => {\n    try {\n      await matchRecordingService.addPoint(winner, eventType as any, shotType);\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to add point';\n      setState(prev => ({ ...prev, error: errorMessage }));\n    }\n  }, []);\n\n  // Undo last point (placeholder)\n  const undoLastPoint = useCallback(async () => {\n    try {\n      // TODO: Implement undo functionality\n      console.log('Undo last point - not yet implemented');\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to undo point';\n      setState(prev => ({ ...prev, error: errorMessage }));\n    }\n  }, []);\n\n  // Toggle video recording\n  const toggleVideoRecording = useCallback(async () => {\n    try {\n      if (sessionRef.current?.videoRecordingActive) {\n        // Stop video recording\n        await videoRecordingService.stopRecording();\n      } else {\n        // Start video recording\n        await videoRecordingService.startRecording(state.videoConfig);\n      }\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : 'Failed to toggle video recording';\n      setState(prev => ({ ...prev, error: errorMessage }));\n    }\n  }, [state.videoConfig]);\n\n  // Update video config\n  const updateVideoConfig = useCallback((config: Partial<VideoRecordingConfig>) => {\n    setState(prev => ({\n      ...prev,\n      videoConfig: { ...prev.videoConfig, ...config },\n    }));\n  }, []);\n\n  // Validate match form\n  const validateMatchForm = useCallback((formData: MatchFormData): MatchValidationErrors => {\n    const errors: MatchValidationErrors = {};\n\n    if (!formData.opponentName?.trim()) {\n      errors.opponentName = 'Opponent name is required';\n    }\n\n    if (!formData.matchType) {\n      errors.matchType = 'Match type is required';\n    }\n\n    if (!formData.matchFormat) {\n      errors.matchFormat = 'Match format is required';\n    }\n\n    if (!formData.surface) {\n      errors.surface = 'Court surface is required';\n    }\n\n    return errors;\n  }, []);\n\n  // Create match metadata from form data\n  const createMatchMetadata = useCallback((formData: MatchFormData): MatchMetadata => {\n    if (!user) {\n      throw new Error('User must be authenticated');\n    }\n\n    return {\n      userId: user.id,\n      opponentName: formData.opponentName,\n      matchType: formData.matchType,\n      matchFormat: formData.matchFormat,\n      surface: formData.surface,\n      location: formData.location,\n      courtName: formData.courtName,\n      weatherConditions: formData.weatherConditions,\n      temperature: formData.temperature,\n      tournamentName: formData.tournamentName,\n      tournamentRound: formData.tournamentRound,\n      matchDate: new Date().toISOString().split('T')[0],\n    };\n  }, [user]);\n\n  // Get match duration\n  const getMatchDuration = useCallback((): number => {\n    if (!sessionRef.current) return 0;\n    \n    const currentTime = Date.now();\n    const startTime = sessionRef.current.startTime;\n    const pausedDuration = sessionRef.current.totalPausedDuration;\n    \n    return Math.floor((currentTime - startTime - pausedDuration) / 1000);\n  }, []);\n\n  // Get formatted score\n  const getFormattedScore = useCallback((): string => {\n    const score = state.currentScore;\n    if (score.sets.length === 0) return '0-0';\n    \n    return score.sets\n      .map(set => `${set.userGames}-${set.opponentGames}`)\n      .join(', ');\n  }, [state.currentScore]);\n\n  // Check if match is in progress\n  const isMatchInProgress = useCallback((): boolean => {\n    return state.isRecording && !state.isPaused;\n  }, [state.isRecording, state.isPaused]);\n\n  // Check if can add point\n  const canAddPoint = useCallback((): boolean => {\n    return isMatchInProgress() && sessionRef.current !== null;\n  }, [isMatchInProgress]);\n\n  return {\n    state,\n    currentSession: sessionRef.current,\n    recordingProgress,\n    startMatch,\n    endMatch,\n    pauseMatch,\n    resumeMatch,\n    cancelMatch,\n    addPoint,\n    undoLastPoint,\n    toggleVideoRecording,\n    updateVideoConfig,\n    validateMatchForm,\n    createMatchMetadata,\n    getMatchDuration,\n    getFormattedScore,\n    isMatchInProgress,\n    canAddPoint,\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AAWhE,SAASC,qBAAqB;AAC9B,SAASC,qBAAqB;AAC9B,SAASC,OAAO;AAkChB,IAAMC,kBAAwC,IAAAC,aAAA,GAAAC,CAAA,OAAG;EAC/CC,OAAO,EAAE,QAAQ;EACjBC,GAAG,EAAE,EAAE;EACPC,UAAU,EAAE,MAAM;EAClBC,KAAK,EAAE,MAAM;EACbC,kBAAkB,EAAE,GAAG;EACvBC,aAAa,EAAE,GAAG;EAClBC,WAAW,EAAE,IAAI;EACjBC,mBAAmB,EAAE;AACvB,CAAC;AAED,IAAMC,uBAA8C,IAAAV,aAAA,GAAAC,CAAA,OAAG;EACrDU,oBAAoB,EAAE,IAAI;EAC1BC,wBAAwB,EAAE,KAAK;EAC/BC,WAAW,EAAEd,kBAAkB;EAC/Be,wBAAwB,EAAE;AAC5B,CAAC;AAED,OAAO,SAASC,iBAAiBA,CAAA,EAA4B;EAAAf,aAAA,GAAAgB,CAAA;EAC3D,IAAAC,IAAA,IAAAjB,aAAA,GAAAC,CAAA,OAAiBH,OAAO,CAAC,CAAC;IAAlBoB,IAAI,GAAAD,IAAA,CAAJC,IAAI;EAGZ,IAAAC,KAAA,IAAAnB,aAAA,GAAAC,CAAA,OAA0BT,QAAQ,CAAsB;MACtD4B,OAAO,EAAE,IAAI;MACbC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE,KAAK;MACfC,YAAY,EAAE;QACZC,IAAI,EAAE,EAAE;QACRC,UAAU,EAAE,EAAE;QACdC,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE;MACZ,CAAC;MACDf,WAAW,EAAEd,kBAAkB;MAC/B8B,KAAK,EAAE,IAAI;MACXC,OAAO,EAAE;IACX,CAAC,CAAC;IAAAC,KAAA,GAAAC,cAAA,CAAAb,KAAA;IAdKc,KAAK,GAAAF,KAAA;IAAEG,QAAQ,GAAAH,KAAA;EAgBtB,IAAAI,KAAA,IAAAnC,aAAA,GAAAC,CAAA,OAAkDT,QAAQ,CAA2B,IAAI,CAAC;IAAA4C,KAAA,GAAAJ,cAAA,CAAAG,KAAA;IAAnFE,iBAAiB,GAAAD,KAAA;IAAEE,oBAAoB,GAAAF,KAAA;EAC9C,IAAMG,UAAU,IAAAvC,aAAA,GAAAC,CAAA,OAAGN,MAAM,CAAsB,IAAI,CAAC;EAACK,aAAA,GAAAC,CAAA;EAGrDR,SAAS,CAAC,YAAM;IAAAO,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IACd,IAAMuC,kBAAkB;MAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;QAAA1C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QACrC,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACF,MAAMJ,qBAAqB,CAAC8C,UAAU,CAAC,CAAC;UAAC3C,aAAA,GAAAC,CAAA;UAGzCJ,qBAAqB,CAAC+C,mBAAmB,CAACN,oBAAoB,CAAC;UAACtC,aAAA,GAAAC,CAAA;UAGhEL,qBAAqB,CAACiD,kBAAkB,CAAC,UAACzB,OAAO,EAAK;YAAApB,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAC,CAAA;YACpDsC,UAAU,CAACO,OAAO,GAAG1B,OAAO;YAACpB,aAAA,GAAAC,CAAA;YAC7BiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;cAAA/C,aAAA,GAAAgB,CAAA;cAAAhB,aAAA,GAAAC,CAAA;cAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;gBACP3B,OAAO,EAAPA,OAAO;gBACPC,WAAW,EAAE,CAAArB,aAAA,GAAAkD,CAAA,UAAA9B,OAAO,oBAAPA,OAAO,CAAEC,WAAW,MAAArB,aAAA,GAAAkD,CAAA,UAAI,KAAK;gBAC1C5B,QAAQ,EAAE,CAAAtB,aAAA,GAAAkD,CAAA,UAAA9B,OAAO,oBAAPA,OAAO,CAAEE,QAAQ,MAAAtB,aAAA,GAAAkD,CAAA,UAAI,KAAK;cAAA;YACtC,CAAE,CAAC;UACL,CAAC,CAAC;UAAClD,aAAA,GAAAC,CAAA;UAGHL,qBAAqB,CAACuD,gBAAgB,CAAC,UAACC,KAAK,EAAK;YAAApD,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAC,CAAA;YAChDiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;cAAA/C,aAAA,GAAAgB,CAAA;cAAAhB,aAAA,GAAAC,CAAA;cAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;gBACPxB,YAAY,EAAE6B;cAAK;YACrB,CAAE,CAAC;UACL,CAAC,CAAC;QAEJ,CAAC,CAAC,OAAOvB,KAAK,EAAE;UAAA7B,aAAA,GAAAC,CAAA;UACdoD,OAAO,CAACxB,KAAK,CAAC,gDAAgD,EAAEA,KAAK,CAAC;UAAC7B,aAAA,GAAAC,CAAA;UACvEiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;YAAA/C,aAAA,GAAAgB,CAAA;YAAAhB,aAAA,GAAAC,CAAA;YAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;cACPlB,KAAK,EAAEA,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,UAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,UAAG,+BAA+B;YAAA;UACjF,CAAE,CAAC;QACL;MACF,CAAC;MAAA,gBAjCKV,kBAAkBA,CAAA;QAAA,OAAAC,KAAA,CAAAe,KAAA,OAAAC,SAAA;MAAA;IAAA,GAiCvB;IAACzD,aAAA,GAAAC,CAAA;IAEFuC,kBAAkB,CAAC,CAAC;IAACxC,aAAA,GAAAC,CAAA;IAGrB,OAAO,YAAM;MAAAD,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAC,CAAA;MACXJ,qBAAqB,CAAC6D,OAAO,CAAC,CAAC;IACjC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMC,UAAU,IAAA3D,aAAA,GAAAC,CAAA,QAAGP,WAAW;IAAA,IAAAkE,KAAA,GAAAlB,iBAAA,CAAC,WAC7BmB,QAAuB,EAEpB;MAAA,IADHC,OAAuC,GAAAL,SAAA,CAAAM,MAAA,QAAAN,SAAA,QAAAO,SAAA,GAAAP,SAAA,OAAAzD,aAAA,GAAAkD,CAAA,UAAG,CAAC,CAAC;MAAAlD,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAC,CAAA;MAE5C,IAAI,CAACiB,IAAI,EAAE;QAAAlB,aAAA,GAAAkD,CAAA;QAAAlD,aAAA,GAAAC,CAAA;QACT,MAAM,IAAIqD,KAAK,CAAC,qDAAqD,CAAC;MACxE,CAAC;QAAAtD,aAAA,GAAAkD,CAAA;MAAA;MAAAlD,aAAA,GAAAC,CAAA;MAED,IAAI;QAAAD,aAAA,GAAAC,CAAA;QACFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;UAAA/C,aAAA,GAAAgB,CAAA;UAAAhB,aAAA,GAAAC,CAAA;UAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;YAAEjB,OAAO,EAAE,IAAI;YAAED,KAAK,EAAE;UAAI;QAAC,CAAE,CAAC;QAE3D,IAAMoC,WAAW,IAAAjE,aAAA,GAAAC,CAAA,QAAA+C,MAAA,CAAAC,MAAA,KAAQvC,uBAAuB,EAAKoD,OAAO,EAAE;QAC9D,IAAMI,YAAY,IAAAlE,aAAA,GAAAC,CAAA,QAAA+C,MAAA,CAAAC,MAAA,KAAQY,QAAQ;UAAEM,MAAM,EAAEjD,IAAI,CAACkD;QAAE,GAAE;QAACpE,aAAA,GAAAC,CAAA;QAEtD,MAAML,qBAAqB,CAAC+D,UAAU,CAACO,YAAY,EAAED,WAAW,CAAC;QAACjE,aAAA,GAAAC,CAAA;QAElEiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;UAAA/C,aAAA,GAAAgB,CAAA;UAAAhB,aAAA,GAAAC,CAAA;UAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;YAAEjB,OAAO,EAAE;UAAK;QAAC,CAAE,CAAC;MACjD,CAAC,CAAC,OAAOD,KAAK,EAAE;QACd,IAAMwC,YAAY,IAAArE,aAAA,GAAAC,CAAA,QAAG4B,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,UAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,UAAG,uBAAuB;QAAClD,aAAA,GAAAC,CAAA;QACtFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;UAAA/C,aAAA,GAAAgB,CAAA;UAAAhB,aAAA,GAAAC,CAAA;UAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;YACPjB,OAAO,EAAE,KAAK;YACdD,KAAK,EAAEwC;UAAY;QACrB,CAAE,CAAC;QAACrE,aAAA,GAAAC,CAAA;QACJ,MAAM4B,KAAK;MACb;IACF,CAAC;IAAA,iBAAAyC,EAAA;MAAA,OAAAV,KAAA,CAAAJ,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,CAACvC,IAAI,CAAC,CAAC;EAGV,IAAMqD,QAAQ,IAAAvE,aAAA,GAAAC,CAAA,QAAGP,WAAW,CAAAgD,iBAAA,CAAC,aAA4C;IAAA1C,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IACvE,IAAI;MAAAD,aAAA,GAAAC,CAAA;MACFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;UAAEjB,OAAO,EAAE,IAAI;UAAED,KAAK,EAAE;QAAI;MAAC,CAAE,CAAC;MAE3D,IAAMH,MAAM,IAAA1B,aAAA,GAAAC,CAAA,cAASL,qBAAqB,CAAC2E,QAAQ,CAAC,CAAC;MAACvE,aAAA,GAAAC,CAAA;MAEtDiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;UACPjB,OAAO,EAAE,KAAK;UACdV,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE,KAAK;UAClBC,QAAQ,EAAE;QAAK;MACjB,CAAE,CAAC;MAACtB,aAAA,GAAAC,CAAA;MAEJqC,oBAAoB,CAAC,IAAI,CAAC;MAACtC,aAAA,GAAAC,CAAA;MAC3B,OAAOyB,MAAM;IACf,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,IAAMwC,YAAY,IAAArE,aAAA,GAAAC,CAAA,QAAG4B,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,UAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,UAAG,qBAAqB;MAAClD,aAAA,GAAAC,CAAA;MACpFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;UACPjB,OAAO,EAAE,KAAK;UACdD,KAAK,EAAEwC;QAAY;MACrB,CAAE,CAAC;MAACrE,aAAA,GAAAC,CAAA;MACJ,OAAO,IAAI;IACb;EACF,CAAC,GAAE,EAAE,CAAC;EAGN,IAAMuE,UAAU,IAAAxE,aAAA,GAAAC,CAAA,QAAGP,WAAW,CAAAgD,iBAAA,CAAC,aAAY;IAAA1C,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IACzC,IAAI;MAAAD,aAAA,GAAAC,CAAA;MACF,MAAML,qBAAqB,CAAC4E,UAAU,CAAC,CAAC;IAC1C,CAAC,CAAC,OAAO3C,KAAK,EAAE;MACd,IAAMwC,YAAY,IAAArE,aAAA,GAAAC,CAAA,QAAG4B,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,UAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,UAAG,uBAAuB;MAAClD,aAAA,GAAAC,CAAA;MACtFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;UAAElB,KAAK,EAAEwC;QAAY;MAAC,CAAE,CAAC;IACtD;EACF,CAAC,GAAE,EAAE,CAAC;EAGN,IAAMI,WAAW,IAAAzE,aAAA,GAAAC,CAAA,QAAGP,WAAW,CAAAgD,iBAAA,CAAC,aAAY;IAAA1C,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IAC1C,IAAI;MAAAD,aAAA,GAAAC,CAAA;MACF,MAAML,qBAAqB,CAAC6E,WAAW,CAAC,CAAC;IAC3C,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACd,IAAMwC,YAAY,IAAArE,aAAA,GAAAC,CAAA,QAAG4B,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,UAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,UAAG,wBAAwB;MAAClD,aAAA,GAAAC,CAAA;MACvFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;UAAElB,KAAK,EAAEwC;QAAY;MAAC,CAAE,CAAC;IACtD;EACF,CAAC,GAAE,EAAE,CAAC;EAGN,IAAMK,WAAW,IAAA1E,aAAA,GAAAC,CAAA,QAAGP,WAAW,CAAAgD,iBAAA,CAAC,aAAY;IAAA1C,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IAC1C,IAAI;MAAAD,aAAA,GAAAC,CAAA;MACFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;UAAEjB,OAAO,EAAE,IAAI;UAAED,KAAK,EAAE;QAAI;MAAC,CAAE,CAAC;MAAC7B,aAAA,GAAAC,CAAA;MAE5D,MAAML,qBAAqB,CAAC8E,WAAW,CAAC,CAAC;MAAC1E,aAAA,GAAAC,CAAA;MAE1CiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;UACPjB,OAAO,EAAE,KAAK;UACdV,OAAO,EAAE,IAAI;UACbC,WAAW,EAAE,KAAK;UAClBC,QAAQ,EAAE;QAAK;MACjB,CAAE,CAAC;MAACtB,aAAA,GAAAC,CAAA;MAEJqC,oBAAoB,CAAC,IAAI,CAAC;IAC5B,CAAC,CAAC,OAAOT,KAAK,EAAE;MACd,IAAMwC,YAAY,IAAArE,aAAA,GAAAC,CAAA,QAAG4B,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,UAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,UAAG,wBAAwB;MAAClD,aAAA,GAAAC,CAAA;MACvFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;UACPjB,OAAO,EAAE,KAAK;UACdD,KAAK,EAAEwC;QAAY;MACrB,CAAE,CAAC;IACL;EACF,CAAC,GAAE,EAAE,CAAC;EAGN,IAAMM,QAAQ,IAAA3E,aAAA,GAAAC,CAAA,QAAGP,WAAW;IAAA,IAAAkF,MAAA,GAAAlC,iBAAA,CAAC,WAC3BmC,MAA2B,EAGxB;MAAA,IAFHC,SAAiB,GAAArB,SAAA,CAAAM,MAAA,QAAAN,SAAA,QAAAO,SAAA,GAAAP,SAAA,OAAAzD,aAAA,GAAAkD,CAAA,WAAG,QAAQ;MAAA,IAC5B6B,QAAiB,GAAAtB,SAAA,CAAAM,MAAA,OAAAN,SAAA,MAAAO,SAAA;MAAAhE,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAC,CAAA;MAEjB,IAAI;QAAAD,aAAA,GAAAC,CAAA;QACF,MAAML,qBAAqB,CAAC+E,QAAQ,CAACE,MAAM,EAAEC,SAAS,EAASC,QAAQ,CAAC;MAC1E,CAAC,CAAC,OAAOlD,KAAK,EAAE;QACd,IAAMwC,YAAY,IAAArE,aAAA,GAAAC,CAAA,QAAG4B,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,WAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,WAAG,qBAAqB;QAAClD,aAAA,GAAAC,CAAA;QACpFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;UAAA/C,aAAA,GAAAgB,CAAA;UAAAhB,aAAA,GAAAC,CAAA;UAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;YAAElB,KAAK,EAAEwC;UAAY;QAAC,CAAE,CAAC;MACtD;IACF,CAAC;IAAA,iBAAAW,GAAA;MAAA,OAAAJ,MAAA,CAAApB,KAAA,OAAAC,SAAA;IAAA;EAAA,KAAE,EAAE,CAAC;EAGN,IAAMwB,aAAa,IAAAjF,aAAA,GAAAC,CAAA,QAAGP,WAAW,CAAAgD,iBAAA,CAAC,aAAY;IAAA1C,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IAC5C,IAAI;MAAAD,aAAA,GAAAC,CAAA;MAEFoD,OAAO,CAAC6B,GAAG,CAAC,uCAAuC,CAAC;IACtD,CAAC,CAAC,OAAOrD,KAAK,EAAE;MACd,IAAMwC,YAAY,IAAArE,aAAA,GAAAC,CAAA,QAAG4B,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,WAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,WAAG,sBAAsB;MAAClD,aAAA,GAAAC,CAAA;MACrFiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;UAAElB,KAAK,EAAEwC;QAAY;MAAC,CAAE,CAAC;IACtD;EACF,CAAC,GAAE,EAAE,CAAC;EAGN,IAAMc,oBAAoB,IAAAnF,aAAA,GAAAC,CAAA,QAAGP,WAAW,CAAAgD,iBAAA,CAAC,aAAY;IAAA1C,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IACnD,IAAI;MAAA,IAAAmF,mBAAA;MAAApF,aAAA,GAAAC,CAAA;MACF,KAAAmF,mBAAA,GAAI7C,UAAU,CAACO,OAAO,aAAlBsC,mBAAA,CAAoBC,oBAAoB,EAAE;QAAArF,aAAA,GAAAkD,CAAA;QAAAlD,aAAA,GAAAC,CAAA;QAE5C,MAAMJ,qBAAqB,CAACyF,aAAa,CAAC,CAAC;MAC7C,CAAC,MAAM;QAAAtF,aAAA,GAAAkD,CAAA;QAAAlD,aAAA,GAAAC,CAAA;QAEL,MAAMJ,qBAAqB,CAAC0F,cAAc,CAACtD,KAAK,CAACpB,WAAW,CAAC;MAC/D;IACF,CAAC,CAAC,OAAOgB,KAAK,EAAE;MACd,IAAMwC,YAAY,IAAArE,aAAA,GAAAC,CAAA,QAAG4B,KAAK,YAAYyB,KAAK,IAAAtD,aAAA,GAAAkD,CAAA,WAAGrB,KAAK,CAAC0B,OAAO,KAAAvD,aAAA,GAAAkD,CAAA,WAAG,kCAAkC;MAAClD,aAAA,GAAAC,CAAA;MACjGiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;QAAA/C,aAAA,GAAAgB,CAAA;QAAAhB,aAAA,GAAAC,CAAA;QAAA,OAAA+C,MAAA,CAAAC,MAAA,KAAKF,IAAI;UAAElB,KAAK,EAAEwC;QAAY;MAAC,CAAE,CAAC;IACtD;EACF,CAAC,GAAE,CAACpC,KAAK,CAACpB,WAAW,CAAC,CAAC;EAGvB,IAAM2E,iBAAiB,IAAAxF,aAAA,GAAAC,CAAA,QAAGP,WAAW,CAAC,UAAC+F,MAAqC,EAAK;IAAAzF,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IAC/EiC,QAAQ,CAAC,UAAAa,IAAI,EAAK;MAAA/C,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAC,CAAA;MAAA,OAAA+C,MAAA,CAAAC,MAAA,KACbF,IAAI;QACPlC,WAAW,EAAAmC,MAAA,CAAAC,MAAA,KAAOF,IAAI,CAAClC,WAAW,EAAK4E,MAAM;MAAE;IACjD,CAAE,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMC,iBAAiB,IAAA1F,aAAA,GAAAC,CAAA,QAAGP,WAAW,CAAC,UAACiG,QAAuB,EAA4B;IAAA,IAAAC,qBAAA;IAAA5F,aAAA,GAAAgB,CAAA;IACxF,IAAM6E,MAA6B,IAAA7F,aAAA,GAAAC,CAAA,QAAG,CAAC,CAAC;IAACD,aAAA,GAAAC,CAAA;IAEzC,IAAI,GAAA2F,qBAAA,GAACD,QAAQ,CAACG,YAAY,aAArBF,qBAAA,CAAuBG,IAAI,CAAC,CAAC,GAAE;MAAA/F,aAAA,GAAAkD,CAAA;MAAAlD,aAAA,GAAAC,CAAA;MAClC4F,MAAM,CAACC,YAAY,GAAG,2BAA2B;IACnD,CAAC;MAAA9F,aAAA,GAAAkD,CAAA;IAAA;IAAAlD,aAAA,GAAAC,CAAA;IAED,IAAI,CAAC0F,QAAQ,CAACK,SAAS,EAAE;MAAAhG,aAAA,GAAAkD,CAAA;MAAAlD,aAAA,GAAAC,CAAA;MACvB4F,MAAM,CAACG,SAAS,GAAG,wBAAwB;IAC7C,CAAC;MAAAhG,aAAA,GAAAkD,CAAA;IAAA;IAAAlD,aAAA,GAAAC,CAAA;IAED,IAAI,CAAC0F,QAAQ,CAACM,WAAW,EAAE;MAAAjG,aAAA,GAAAkD,CAAA;MAAAlD,aAAA,GAAAC,CAAA;MACzB4F,MAAM,CAACI,WAAW,GAAG,0BAA0B;IACjD,CAAC;MAAAjG,aAAA,GAAAkD,CAAA;IAAA;IAAAlD,aAAA,GAAAC,CAAA;IAED,IAAI,CAAC0F,QAAQ,CAACO,OAAO,EAAE;MAAAlG,aAAA,GAAAkD,CAAA;MAAAlD,aAAA,GAAAC,CAAA;MACrB4F,MAAM,CAACK,OAAO,GAAG,2BAA2B;IAC9C,CAAC;MAAAlG,aAAA,GAAAkD,CAAA;IAAA;IAAAlD,aAAA,GAAAC,CAAA;IAED,OAAO4F,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMM,mBAAmB,IAAAnG,aAAA,GAAAC,CAAA,SAAGP,WAAW,CAAC,UAACiG,QAAuB,EAAoB;IAAA3F,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IAClF,IAAI,CAACiB,IAAI,EAAE;MAAAlB,aAAA,GAAAkD,CAAA;MAAAlD,aAAA,GAAAC,CAAA;MACT,MAAM,IAAIqD,KAAK,CAAC,4BAA4B,CAAC;IAC/C,CAAC;MAAAtD,aAAA,GAAAkD,CAAA;IAAA;IAAAlD,aAAA,GAAAC,CAAA;IAED,OAAO;MACLkE,MAAM,EAAEjD,IAAI,CAACkD,EAAE;MACf0B,YAAY,EAAEH,QAAQ,CAACG,YAAY;MACnCE,SAAS,EAAEL,QAAQ,CAACK,SAAS;MAC7BC,WAAW,EAAEN,QAAQ,CAACM,WAAW;MACjCC,OAAO,EAAEP,QAAQ,CAACO,OAAO;MACzBE,QAAQ,EAAET,QAAQ,CAACS,QAAQ;MAC3BC,SAAS,EAAEV,QAAQ,CAACU,SAAS;MAC7BC,iBAAiB,EAAEX,QAAQ,CAACW,iBAAiB;MAC7CC,WAAW,EAAEZ,QAAQ,CAACY,WAAW;MACjCC,cAAc,EAAEb,QAAQ,CAACa,cAAc;MACvCC,eAAe,EAAEd,QAAQ,CAACc,eAAe;MACzCC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,CAAC3F,IAAI,CAAC,CAAC;EAGV,IAAM4F,gBAAgB,IAAA9G,aAAA,GAAAC,CAAA,SAAGP,WAAW,CAAC,YAAc;IAAAM,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IACjD,IAAI,CAACsC,UAAU,CAACO,OAAO,EAAE;MAAA9C,aAAA,GAAAkD,CAAA;MAAAlD,aAAA,GAAAC,CAAA;MAAA,OAAO,CAAC;IAAA,CAAC;MAAAD,aAAA,GAAAkD,CAAA;IAAA;IAElC,IAAM6D,WAAW,IAAA/G,aAAA,GAAAC,CAAA,SAAG0G,IAAI,CAACK,GAAG,CAAC,CAAC;IAC9B,IAAMC,SAAS,IAAAjH,aAAA,GAAAC,CAAA,SAAGsC,UAAU,CAACO,OAAO,CAACmE,SAAS;IAC9C,IAAMC,cAAc,IAAAlH,aAAA,GAAAC,CAAA,SAAGsC,UAAU,CAACO,OAAO,CAACqE,mBAAmB;IAACnH,aAAA,GAAAC,CAAA;IAE9D,OAAOmH,IAAI,CAACC,KAAK,CAAC,CAACN,WAAW,GAAGE,SAAS,GAAGC,cAAc,IAAI,IAAI,CAAC;EACtE,CAAC,EAAE,EAAE,CAAC;EAGN,IAAMI,iBAAiB,IAAAtH,aAAA,GAAAC,CAAA,SAAGP,WAAW,CAAC,YAAc;IAAAM,aAAA,GAAAgB,CAAA;IAClD,IAAMoC,KAAK,IAAApD,aAAA,GAAAC,CAAA,SAAGgC,KAAK,CAACV,YAAY;IAACvB,aAAA,GAAAC,CAAA;IACjC,IAAImD,KAAK,CAAC5B,IAAI,CAACuC,MAAM,KAAK,CAAC,EAAE;MAAA/D,aAAA,GAAAkD,CAAA;MAAAlD,aAAA,GAAAC,CAAA;MAAA,OAAO,KAAK;IAAA,CAAC;MAAAD,aAAA,GAAAkD,CAAA;IAAA;IAAAlD,aAAA,GAAAC,CAAA;IAE1C,OAAOmD,KAAK,CAAC5B,IAAI,CACd+F,GAAG,CAAC,UAAAC,GAAG,EAAI;MAAAxH,aAAA,GAAAgB,CAAA;MAAAhB,aAAA,GAAAC,CAAA;MAAA,UAAGuH,GAAG,CAACC,SAAS,IAAID,GAAG,CAACE,aAAa,EAAE;IAAD,CAAC,CAAC,CACnDC,IAAI,CAAC,IAAI,CAAC;EACf,CAAC,EAAE,CAAC1F,KAAK,CAACV,YAAY,CAAC,CAAC;EAGxB,IAAMqG,iBAAiB,IAAA5H,aAAA,GAAAC,CAAA,SAAGP,WAAW,CAAC,YAAe;IAAAM,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IACnD,OAAO,CAAAD,aAAA,GAAAkD,CAAA,WAAAjB,KAAK,CAACZ,WAAW,MAAArB,aAAA,GAAAkD,CAAA,WAAI,CAACjB,KAAK,CAACX,QAAQ;EAC7C,CAAC,EAAE,CAACW,KAAK,CAACZ,WAAW,EAAEY,KAAK,CAACX,QAAQ,CAAC,CAAC;EAGvC,IAAMuG,WAAW,IAAA7H,aAAA,GAAAC,CAAA,SAAGP,WAAW,CAAC,YAAe;IAAAM,aAAA,GAAAgB,CAAA;IAAAhB,aAAA,GAAAC,CAAA;IAC7C,OAAO,CAAAD,aAAA,GAAAkD,CAAA,WAAA0E,iBAAiB,CAAC,CAAC,MAAA5H,aAAA,GAAAkD,CAAA,WAAIX,UAAU,CAACO,OAAO,KAAK,IAAI;EAC3D,CAAC,EAAE,CAAC8E,iBAAiB,CAAC,CAAC;EAAC5H,aAAA,GAAAC,CAAA;EAExB,OAAO;IACLgC,KAAK,EAALA,KAAK;IACL6F,cAAc,EAAEvF,UAAU,CAACO,OAAO;IAClCT,iBAAiB,EAAjBA,iBAAiB;IACjBsB,UAAU,EAAVA,UAAU;IACVY,QAAQ,EAARA,QAAQ;IACRC,UAAU,EAAVA,UAAU;IACVC,WAAW,EAAXA,WAAW;IACXC,WAAW,EAAXA,WAAW;IACXC,QAAQ,EAARA,QAAQ;IACRM,aAAa,EAAbA,aAAa;IACbE,oBAAoB,EAApBA,oBAAoB;IACpBK,iBAAiB,EAAjBA,iBAAiB;IACjBE,iBAAiB,EAAjBA,iBAAiB;IACjBS,mBAAmB,EAAnBA,mBAAmB;IACnBW,gBAAgB,EAAhBA,gBAAgB;IAChBQ,iBAAiB,EAAjBA,iBAAiB;IACjBM,iBAAiB,EAAjBA,iBAAiB;IACjBC,WAAW,EAAXA;EACF,CAAC;AACH", "ignoreList": []}