{"version": 3, "names": ["React", "useState", "View", "Text", "StyleSheet", "SafeAreaView", "TouchableOpacity", "ScrollView", "Switch", "<PERSON><PERSON>", "LinearGradient", "router", "Card", "<PERSON><PERSON>", "ArrowLeft", "Shield", "Eye", "Users", "Database", "Lock", "Globe", "Camera", "Mic", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_13drk2ekyo", "s", "primary", "yellow", "white", "dark", "gray", "lightGray", "red", "PrivacyScreen", "f", "_ref", "_ref2", "_slicedToArray", "loading", "setLoading", "_ref3", "id", "title", "description", "icon", "enabled", "category", "critical", "_ref4", "settings", "setSettings", "toggleSetting", "setting", "find", "b", "alert", "text", "style", "onPress", "updateSetting", "prev", "map", "Object", "assign", "handleSave", "_ref5", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "back", "error", "apply", "arguments", "handleDataExport", "handleDeleteAccount", "renderPrivacyCategory", "categorySettings", "filter", "styles", "categoryCard", "children", "categoryTitle", "IconComponent", "settingRow", "settingInfo", "settingIcon", "criticalIcon", "size", "color", "settingText", "settingTitleRow", "setting<PERSON>itle", "criticalBadge", "criticalText", "settingDescription", "value", "onValueChange", "trackColor", "false", "true", "thumbColor", "container", "gradient", "header", "backButton", "placeholder", "content", "showsVerticalScrollIndicator", "overviewCard", "overviewHeader", "overviewTitle", "overviewText", "dataCard", "dataTitle", "dataDescription", "dataActions", "dataButton", "dataButtonText", "deleteButton", "deleteButtonText", "saveContainer", "saveButton", "create", "flex", "flexDirection", "alignItems", "justifyContent", "paddingHorizontal", "paddingTop", "paddingBottom", "padding", "fontSize", "fontFamily", "width", "marginBottom", "marginLeft", "lineHeight", "paddingVertical", "borderBottomWidth", "borderBottomColor", "height", "borderRadius", "backgroundColor", "marginRight", "gap"], "sources": ["privacy.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  View,\n  Text,\n  StyleSheet,\n  SafeAreaView,\n  TouchableOpacity,\n  ScrollView,\n  Switch,\n  Alert,\n} from 'react-native';\nimport { LinearGradient } from 'expo-linear-gradient';\nimport { router } from 'expo-router';\nimport Card from '@/components/ui/Card';\nimport Button from '@/components/ui/Button';\nimport { \n  ArrowLeft, \n  Shield, \n  Eye, \n  Users, \n  Database,\n  Lock,\n  Globe,\n  Camera,\n  Mic\n} from 'lucide-react-native';\n\nconst colors = {\n  primary: '#23ba16',\n  yellow: '#ffe600',\n  white: '#ffffff',\n  dark: '#171717',\n  gray: '#6b7280',\n  lightGray: '#f9fafb',\n  red: '#ef4444',\n};\n\ninterface PrivacySetting {\n  id: string;\n  title: string;\n  description: string;\n  icon: React.ComponentType<any>;\n  enabled: boolean;\n  category: 'profile' | 'data' | 'permissions';\n  critical?: boolean;\n}\n\nexport default function PrivacyScreen() {\n  const [loading, setLoading] = useState(false);\n  const [settings, setSettings] = useState<PrivacySetting[]>([\n    {\n      id: 'profile_visibility',\n      title: 'Public Profile',\n      description: 'Allow other users to find and view your profile',\n      icon: Eye,\n      enabled: true,\n      category: 'profile',\n    },\n    {\n      id: 'match_history_public',\n      title: 'Public Match History',\n      description: 'Show your match results to other players',\n      icon: Globe,\n      enabled: false,\n      category: 'profile',\n    },\n    {\n      id: 'social_interactions',\n      title: 'Social Interactions',\n      description: 'Allow friend requests and messages',\n      icon: Users,\n      enabled: true,\n      category: 'profile',\n    },\n    {\n      id: 'data_analytics',\n      title: 'Performance Analytics',\n      description: 'Use your data to improve AI coaching recommendations',\n      icon: Database,\n      enabled: true,\n      category: 'data',\n    },\n    {\n      id: 'data_sharing',\n      title: 'Anonymous Data Sharing',\n      description: 'Help improve the app with anonymous usage data',\n      icon: Shield,\n      enabled: false,\n      category: 'data',\n    },\n    {\n      id: 'camera_access',\n      title: 'Camera Access',\n      description: 'Required for video analysis and photo uploads',\n      icon: Camera,\n      enabled: true,\n      category: 'permissions',\n      critical: true,\n    },\n    {\n      id: 'microphone_access',\n      title: 'Microphone Access',\n      description: 'For voice coaching and audio feedback',\n      icon: Mic,\n      enabled: false,\n      category: 'permissions',\n    },\n  ]);\n\n  const toggleSetting = (id: string) => {\n    const setting = settings.find(s => s.id === id);\n    \n    if (setting?.critical && setting.enabled) {\n      Alert.alert(\n        'Critical Permission',\n        'This permission is required for core app functionality. Disabling it may limit your experience.',\n        [\n          { text: 'Cancel', style: 'cancel' },\n          { \n            text: 'Disable Anyway', \n            style: 'destructive',\n            onPress: () => updateSetting(id)\n          }\n        ]\n      );\n    } else {\n      updateSetting(id);\n    }\n  };\n\n  const updateSetting = (id: string) => {\n    setSettings(prev => \n      prev.map(setting => \n        setting.id === id \n          ? { ...setting, enabled: !setting.enabled }\n          : setting\n      )\n    );\n  };\n\n  const handleSave = async () => {\n    setLoading(true);\n    try {\n      // In a real app, save to backend\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      Alert.alert(\n        'Success',\n        'Privacy settings saved successfully!',\n        [{ text: 'OK', onPress: () => router.back() }]\n      );\n    } catch (error) {\n      Alert.alert('Error', 'Failed to save settings. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDataExport = () => {\n    Alert.alert(\n      'Export Data',\n      'We will prepare your data export and send it to your email within 24 hours.',\n      [\n        { text: 'Cancel', style: 'cancel' },\n        { text: 'Request Export', onPress: () => {\n          Alert.alert('Export Requested', 'You will receive an email with your data export link.');\n        }}\n      ]\n    );\n  };\n\n  const handleDeleteAccount = () => {\n    Alert.alert(\n      'Delete Account',\n      'This action cannot be undone. All your data, including match history, training progress, and social connections will be permanently deleted.',\n      [\n        { text: 'Cancel', style: 'cancel' },\n        { \n          text: 'Delete Account', \n          style: 'destructive',\n          onPress: () => {\n            Alert.alert('Account Deletion', 'Please contact support to complete account deletion.');\n          }\n        }\n      ]\n    );\n  };\n\n  const renderPrivacyCategory = (category: string, title: string) => {\n    const categorySettings = settings.filter(s => s.category === category);\n    \n    return (\n      <Card key={category} style={styles.categoryCard}>\n        <Text style={styles.categoryTitle}>{title}</Text>\n        {categorySettings.map((setting) => {\n          const IconComponent = setting.icon;\n          return (\n            <View key={setting.id} style={styles.settingRow}>\n              <View style={styles.settingInfo}>\n                <View style={[\n                  styles.settingIcon,\n                  setting.critical && styles.criticalIcon\n                ]}>\n                  <IconComponent \n                    size={20} \n                    color={setting.critical ? colors.red : colors.primary} \n                  />\n                </View>\n                <View style={styles.settingText}>\n                  <View style={styles.settingTitleRow}>\n                    <Text style={styles.settingTitle}>{setting.title}</Text>\n                    {setting.critical && (\n                      <View style={styles.criticalBadge}>\n                        <Text style={styles.criticalText}>Required</Text>\n                      </View>\n                    )}\n                  </View>\n                  <Text style={styles.settingDescription}>{setting.description}</Text>\n                </View>\n              </View>\n              <Switch\n                value={setting.enabled}\n                onValueChange={() => toggleSetting(setting.id)}\n                trackColor={{ false: colors.lightGray, true: colors.primary }}\n                thumbColor={setting.enabled ? colors.white : colors.gray}\n              />\n            </View>\n          );\n        })}\n      </Card>\n    );\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <LinearGradient\n        colors={['#1e3a8a', '#3b82f6', '#60a5fa']}\n        style={styles.gradient}\n      >\n        {/* Header */}\n        <View style={styles.header}>\n          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>\n            <ArrowLeft size={24} color=\"white\" />\n          </TouchableOpacity>\n          <Text style={styles.title}>Privacy & Security</Text>\n          <View style={styles.placeholder} />\n        </View>\n\n        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>\n          {/* Overview */}\n          <Card style={styles.overviewCard}>\n            <View style={styles.overviewHeader}>\n              <Shield size={24} color={colors.primary} />\n              <Text style={styles.overviewTitle}>Your Privacy Matters</Text>\n            </View>\n            <Text style={styles.overviewText}>\n              Control how your data is used and shared. We're committed to protecting your privacy while providing the best tennis coaching experience.\n            </Text>\n          </Card>\n\n          {/* Privacy Categories */}\n          {renderPrivacyCategory('profile', 'Profile & Visibility')}\n          {renderPrivacyCategory('data', 'Data Usage')}\n          {renderPrivacyCategory('permissions', 'App Permissions')}\n\n          {/* Data Management */}\n          <Card style={styles.dataCard}>\n            <Text style={styles.dataTitle}>Data Management</Text>\n            <Text style={styles.dataDescription}>\n              You have full control over your personal data.\n            </Text>\n            \n            <View style={styles.dataActions}>\n              <TouchableOpacity \n                style={styles.dataButton}\n                onPress={handleDataExport}\n              >\n                <Database size={20} color={colors.primary} />\n                <Text style={styles.dataButtonText}>Export My Data</Text>\n              </TouchableOpacity>\n              \n              <TouchableOpacity \n                style={[styles.dataButton, styles.deleteButton]}\n                onPress={handleDeleteAccount}\n              >\n                <Lock size={20} color={colors.red} />\n                <Text style={[styles.dataButtonText, styles.deleteButtonText]}>\n                  Delete Account\n                </Text>\n              </TouchableOpacity>\n            </View>\n          </Card>\n\n          {/* Save Button */}\n          <View style={styles.saveContainer}>\n            <Button\n              title=\"Save Privacy Settings\"\n              onPress={handleSave}\n              loading={loading}\n              style={styles.saveButton}\n            />\n          </View>\n        </ScrollView>\n      </LinearGradient>\n    </SafeAreaView>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n  },\n  gradient: {\n    flex: 1,\n  },\n  header: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingHorizontal: 20,\n    paddingTop: 20,\n    paddingBottom: 10,\n  },\n  backButton: {\n    padding: 8,\n  },\n  title: {\n    fontSize: 20,\n    fontFamily: 'Inter-SemiBold',\n    color: colors.white,\n  },\n  placeholder: {\n    width: 40,\n  },\n  content: {\n    flex: 1,\n    paddingHorizontal: 20,\n  },\n  overviewCard: {\n    padding: 20,\n    marginBottom: 16,\n  },\n  overviewHeader: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: 12,\n  },\n  overviewTitle: {\n    fontSize: 18,\n    fontFamily: 'Inter-SemiBold',\n    color: colors.dark,\n    marginLeft: 12,\n  },\n  overviewText: {\n    fontSize: 14,\n    fontFamily: 'Inter-Regular',\n    color: colors.gray,\n    lineHeight: 20,\n  },\n  categoryCard: {\n    padding: 20,\n    marginBottom: 16,\n  },\n  categoryTitle: {\n    fontSize: 16,\n    fontFamily: 'Inter-SemiBold',\n    color: colors.dark,\n    marginBottom: 16,\n  },\n  settingRow: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n    paddingVertical: 12,\n    borderBottomWidth: 1,\n    borderBottomColor: colors.lightGray,\n  },\n  settingInfo: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    flex: 1,\n  },\n  settingIcon: {\n    width: 40,\n    height: 40,\n    borderRadius: 20,\n    backgroundColor: colors.lightGray,\n    alignItems: 'center',\n    justifyContent: 'center',\n    marginRight: 12,\n  },\n  criticalIcon: {\n    backgroundColor: '#fef2f2',\n  },\n  settingText: {\n    flex: 1,\n  },\n  settingTitleRow: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginBottom: 2,\n  },\n  settingTitle: {\n    fontSize: 16,\n    fontFamily: 'Inter-Medium',\n    color: colors.dark,\n  },\n  criticalBadge: {\n    marginLeft: 8,\n    paddingHorizontal: 6,\n    paddingVertical: 2,\n    backgroundColor: colors.red,\n    borderRadius: 4,\n  },\n  criticalText: {\n    fontSize: 10,\n    fontFamily: 'Inter-Medium',\n    color: colors.white,\n  },\n  settingDescription: {\n    fontSize: 14,\n    fontFamily: 'Inter-Regular',\n    color: colors.gray,\n  },\n  dataCard: {\n    padding: 20,\n    marginBottom: 16,\n  },\n  dataTitle: {\n    fontSize: 16,\n    fontFamily: 'Inter-SemiBold',\n    color: colors.dark,\n    marginBottom: 8,\n  },\n  dataDescription: {\n    fontSize: 14,\n    fontFamily: 'Inter-Regular',\n    color: colors.gray,\n    marginBottom: 16,\n  },\n  dataActions: {\n    gap: 12,\n  },\n  dataButton: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    paddingVertical: 12,\n    paddingHorizontal: 16,\n    borderRadius: 8,\n    backgroundColor: colors.lightGray,\n  },\n  deleteButton: {\n    backgroundColor: '#fef2f2',\n  },\n  dataButtonText: {\n    fontSize: 14,\n    fontFamily: 'Inter-Medium',\n    color: colors.dark,\n    marginLeft: 8,\n  },\n  deleteButtonText: {\n    color: colors.red,\n  },\n  saveContainer: {\n    paddingVertical: 20,\n  },\n  saveButton: {\n    width: '100%',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,IAAI,EACJC,IAAI,EACJC,UAAU,EACVC,YAAY,EACZC,gBAAgB,EAChBC,UAAU,EACVC,MAAM,EACNC,KAAK,QACA,cAAc;AACrB,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,MAAM,QAAQ,aAAa;AACpC,OAAOC,IAAI;AACX,OAAOC,MAAM;AACb,SACEC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,QAAQ,EACRC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,GAAG,QACE,qBAAqB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE7B,IAAMC,MAAM,IAAAC,cAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,SAAS;EAChBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,SAAS;EACpBC,GAAG,EAAE;AACP,CAAC;AAYD,eAAe,SAASC,aAAaA,CAAA,EAAG;EAAAT,cAAA,GAAAU,CAAA;EACtC,IAAAC,IAAA,IAAAX,cAAA,GAAAC,CAAA,OAA8B5B,QAAQ,CAAC,KAAK,CAAC;IAAAuC,KAAA,GAAAC,cAAA,CAAAF,IAAA;IAAtCG,OAAO,GAAAF,KAAA;IAAEG,UAAU,GAAAH,KAAA;EAC1B,IAAAI,KAAA,IAAAhB,cAAA,GAAAC,CAAA,OAAgC5B,QAAQ,CAAmB,CACzD;MACE4C,EAAE,EAAE,oBAAoB;MACxBC,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE,iDAAiD;MAC9DC,IAAI,EAAEhC,GAAG;MACTiC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,sBAAsB;MAC1BC,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE,0CAA0C;MACvDC,IAAI,EAAE5B,KAAK;MACX6B,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,qBAAqB;MACzBC,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,oCAAoC;MACjDC,IAAI,EAAE/B,KAAK;MACXgC,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,gBAAgB;MACpBC,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,sDAAsD;MACnEC,IAAI,EAAE9B,QAAQ;MACd+B,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,cAAc;MAClBC,KAAK,EAAE,wBAAwB;MAC/BC,WAAW,EAAE,gDAAgD;MAC7DC,IAAI,EAAEjC,MAAM;MACZkC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEL,EAAE,EAAE,eAAe;MACnBC,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,+CAA+C;MAC5DC,IAAI,EAAE3B,MAAM;MACZ4B,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAE,aAAa;MACvBC,QAAQ,EAAE;IACZ,CAAC,EACD;MACEN,EAAE,EAAE,mBAAmB;MACvBC,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,uCAAuC;MACpDC,IAAI,EAAE1B,GAAG;MACT2B,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE;IACZ,CAAC,CACF,CAAC;IAAAE,KAAA,GAAAX,cAAA,CAAAG,KAAA;IA1DKS,QAAQ,GAAAD,KAAA;IAAEE,WAAW,GAAAF,KAAA;EA0DzBxB,cAAA,GAAAC,CAAA;EAEH,IAAM0B,aAAa,GAAG,SAAhBA,aAAaA,CAAIV,EAAU,EAAK;IAAAjB,cAAA,GAAAU,CAAA;IACpC,IAAMkB,OAAO,IAAA5B,cAAA,GAAAC,CAAA,OAAGwB,QAAQ,CAACI,IAAI,CAAC,UAAA5B,CAAC,EAAI;MAAAD,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAAA,OAAAA,CAAC,CAACgB,EAAE,KAAKA,EAAE;IAAD,CAAC,CAAC;IAACjB,cAAA,GAAAC,CAAA;IAEhD,IAAI,CAAAD,cAAA,GAAA8B,CAAA,UAAAF,OAAO,YAAPA,OAAO,CAAEL,QAAQ,MAAAvB,cAAA,GAAA8B,CAAA,UAAIF,OAAO,CAACP,OAAO,GAAE;MAAArB,cAAA,GAAA8B,CAAA;MAAA9B,cAAA,GAAAC,CAAA;MACxCpB,KAAK,CAACkD,KAAK,CACT,qBAAqB,EACrB,iGAAiG,EACjG,CACE;QAAEC,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE;MAAS,CAAC,EACnC;QACED,IAAI,EAAE,gBAAgB;QACtBC,KAAK,EAAE,aAAa;QACpBC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;UAAAlC,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UAAA,OAAAkC,aAAa,CAAClB,EAAE,CAAC;QAAD;MACjC,CAAC,CAEL,CAAC;IACH,CAAC,MAAM;MAAAjB,cAAA,GAAA8B,CAAA;MAAA9B,cAAA,GAAAC,CAAA;MACLkC,aAAa,CAAClB,EAAE,CAAC;IACnB;EACF,CAAC;EAACjB,cAAA,GAAAC,CAAA;EAEF,IAAMkC,aAAa,GAAG,SAAhBA,aAAaA,CAAIlB,EAAU,EAAK;IAAAjB,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IACpCyB,WAAW,CAAC,UAAAU,IAAI,EACd;MAAApC,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAAA,OAAAmC,IAAI,CAACC,GAAG,CAAC,UAAAT,OAAO,EACd;QAAA5B,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QAAA,OAAA2B,OAAO,CAACX,EAAE,KAAKA,EAAE,IAAAjB,cAAA,GAAA8B,CAAA,UAAAQ,MAAA,CAAAC,MAAA,KACRX,OAAO;UAAEP,OAAO,EAAE,CAACO,OAAO,CAACP;QAAO,OAAArB,cAAA,GAAA8B,CAAA,UACvCF,OAAO;MAAD,CACZ,CAAC;IAAD,CACF,CAAC;EACH,CAAC;EAAC5B,cAAA,GAAAC,CAAA;EAEF,IAAMuC,UAAU;IAAA,IAAAC,KAAA,GAAAC,iBAAA,CAAG,aAAY;MAAA1C,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAC7Bc,UAAU,CAAC,IAAI,CAAC;MAACf,cAAA,GAAAC,CAAA;MACjB,IAAI;QAAAD,cAAA,GAAAC,CAAA;QAEF,MAAM,IAAI0C,OAAO,CAAC,UAAAC,OAAO,EAAI;UAAA5C,cAAA,GAAAU,CAAA;UAAAV,cAAA,GAAAC,CAAA;UAAA,OAAA4C,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QAAD,CAAC,CAAC;QAAC5C,cAAA,GAAAC,CAAA;QAExDpB,KAAK,CAACkD,KAAK,CACT,SAAS,EACT,sCAAsC,EACtC,CAAC;UAAEC,IAAI,EAAE,IAAI;UAAEE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;YAAAlC,cAAA,GAAAU,CAAA;YAAAV,cAAA,GAAAC,CAAA;YAAA,OAAAlB,MAAM,CAAC+D,IAAI,CAAC,CAAC;UAAD;QAAE,CAAC,CAC/C,CAAC;MACH,CAAC,CAAC,OAAOC,KAAK,EAAE;QAAA/C,cAAA,GAAAC,CAAA;QACdpB,KAAK,CAACkD,KAAK,CAAC,OAAO,EAAE,4CAA4C,CAAC;MACpE,CAAC,SAAS;QAAA/B,cAAA,GAAAC,CAAA;QACRc,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAAA,gBAhBKyB,UAAUA,CAAA;MAAA,OAAAC,KAAA,CAAAO,KAAA,OAAAC,SAAA;IAAA;EAAA,GAgBf;EAACjD,cAAA,GAAAC,CAAA;EAEF,IAAMiD,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAAAlD,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IAC7BpB,KAAK,CAACkD,KAAK,CACT,aAAa,EACb,6EAA6E,EAC7E,CACE;MAAEC,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACnC;MAAED,IAAI,EAAE,gBAAgB;MAAEE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;QAAAlC,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QACvCpB,KAAK,CAACkD,KAAK,CAAC,kBAAkB,EAAE,uDAAuD,CAAC;MAC1F;IAAC,CAAC,CAEN,CAAC;EACH,CAAC;EAAC/B,cAAA,GAAAC,CAAA;EAEF,IAAMkD,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA,EAAS;IAAAnD,cAAA,GAAAU,CAAA;IAAAV,cAAA,GAAAC,CAAA;IAChCpB,KAAK,CAACkD,KAAK,CACT,gBAAgB,EAChB,8IAA8I,EAC9I,CACE;MAAEC,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAS,CAAC,EACnC;MACED,IAAI,EAAE,gBAAgB;MACtBC,KAAK,EAAE,aAAa;MACpBC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;QAAAlC,cAAA,GAAAU,CAAA;QAAAV,cAAA,GAAAC,CAAA;QACbpB,KAAK,CAACkD,KAAK,CAAC,kBAAkB,EAAE,sDAAsD,CAAC;MACzF;IACF,CAAC,CAEL,CAAC;EACH,CAAC;EAAC/B,cAAA,GAAAC,CAAA;EAEF,IAAMmD,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAI9B,QAAgB,EAAEJ,KAAa,EAAK;IAAAlB,cAAA,GAAAU,CAAA;IACjE,IAAM2C,gBAAgB,IAAArD,cAAA,GAAAC,CAAA,QAAGwB,QAAQ,CAAC6B,MAAM,CAAC,UAAArD,CAAC,EAAI;MAAAD,cAAA,GAAAU,CAAA;MAAAV,cAAA,GAAAC,CAAA;MAAA,OAAAA,CAAC,CAACqB,QAAQ,KAAKA,QAAQ;IAAD,CAAC,CAAC;IAACtB,cAAA,GAAAC,CAAA;IAEvE,OACEH,KAAA,CAACd,IAAI;MAAgBiD,KAAK,EAAEsB,MAAM,CAACC,YAAa;MAAAC,QAAA,GAC9C7D,IAAA,CAACrB,IAAI;QAAC0D,KAAK,EAAEsB,MAAM,CAACG,aAAc;QAAAD,QAAA,EAAEvC;MAAK,CAAO,CAAC,EAChDmC,gBAAgB,CAAChB,GAAG,CAAC,UAACT,OAAO,EAAK;QAAA5B,cAAA,GAAAU,CAAA;QACjC,IAAMiD,aAAa,IAAA3D,cAAA,GAAAC,CAAA,QAAG2B,OAAO,CAACR,IAAI;QAACpB,cAAA,GAAAC,CAAA;QACnC,OACEH,KAAA,CAACxB,IAAI;UAAkB2D,KAAK,EAAEsB,MAAM,CAACK,UAAW;UAAAH,QAAA,GAC9C3D,KAAA,CAACxB,IAAI;YAAC2D,KAAK,EAAEsB,MAAM,CAACM,WAAY;YAAAJ,QAAA,GAC9B7D,IAAA,CAACtB,IAAI;cAAC2D,KAAK,EAAE,CACXsB,MAAM,CAACO,WAAW,EAClB,CAAA9D,cAAA,GAAA8B,CAAA,UAAAF,OAAO,CAACL,QAAQ,MAAAvB,cAAA,GAAA8B,CAAA,UAAIyB,MAAM,CAACQ,YAAY,EACvC;cAAAN,QAAA,EACA7D,IAAA,CAAC+D,aAAa;gBACZK,IAAI,EAAE,EAAG;gBACTC,KAAK,EAAErC,OAAO,CAACL,QAAQ,IAAAvB,cAAA,GAAA8B,CAAA,UAAG/B,MAAM,CAACS,GAAG,KAAAR,cAAA,GAAA8B,CAAA,UAAG/B,MAAM,CAACG,OAAO;cAAC,CACvD;YAAC,CACE,CAAC,EACPJ,KAAA,CAACxB,IAAI;cAAC2D,KAAK,EAAEsB,MAAM,CAACW,WAAY;cAAAT,QAAA,GAC9B3D,KAAA,CAACxB,IAAI;gBAAC2D,KAAK,EAAEsB,MAAM,CAACY,eAAgB;gBAAAV,QAAA,GAClC7D,IAAA,CAACrB,IAAI;kBAAC0D,KAAK,EAAEsB,MAAM,CAACa,YAAa;kBAAAX,QAAA,EAAE7B,OAAO,CAACV;gBAAK,CAAO,CAAC,EACvD,CAAAlB,cAAA,GAAA8B,CAAA,UAAAF,OAAO,CAACL,QAAQ,MAAAvB,cAAA,GAAA8B,CAAA,UACflC,IAAA,CAACtB,IAAI;kBAAC2D,KAAK,EAAEsB,MAAM,CAACc,aAAc;kBAAAZ,QAAA,EAChC7D,IAAA,CAACrB,IAAI;oBAAC0D,KAAK,EAAEsB,MAAM,CAACe,YAAa;oBAAAb,QAAA,EAAC;kBAAQ,CAAM;gBAAC,CAC7C,CAAC,CACR;cAAA,CACG,CAAC,EACP7D,IAAA,CAACrB,IAAI;gBAAC0D,KAAK,EAAEsB,MAAM,CAACgB,kBAAmB;gBAAAd,QAAA,EAAE7B,OAAO,CAACT;cAAW,CAAO,CAAC;YAAA,CAChE,CAAC;UAAA,CACH,CAAC,EACPvB,IAAA,CAAChB,MAAM;YACL4F,KAAK,EAAE5C,OAAO,CAACP,OAAQ;YACvBoD,aAAa,EAAE,SAAfA,aAAaA,CAAA,EAAQ;cAAAzE,cAAA,GAAAU,CAAA;cAAAV,cAAA,GAAAC,CAAA;cAAA,OAAA0B,aAAa,CAACC,OAAO,CAACX,EAAE,CAAC;YAAD,CAAE;YAC/CyD,UAAU,EAAE;cAAEC,KAAK,EAAE5E,MAAM,CAACQ,SAAS;cAAEqE,IAAI,EAAE7E,MAAM,CAACG;YAAQ,CAAE;YAC9D2E,UAAU,EAAEjD,OAAO,CAACP,OAAO,IAAArB,cAAA,GAAA8B,CAAA,UAAG/B,MAAM,CAACK,KAAK,KAAAJ,cAAA,GAAA8B,CAAA,UAAG/B,MAAM,CAACO,IAAI;UAAC,CAC1D,CAAC;QAAA,GA5BOsB,OAAO,CAACX,EA6Bb,CAAC;MAEX,CAAC,CAAC;IAAA,GApCOK,QAqCL,CAAC;EAEX,CAAC;EAACtB,cAAA,GAAAC,CAAA;EAEF,OACEL,IAAA,CAACnB,YAAY;IAACwD,KAAK,EAAEsB,MAAM,CAACuB,SAAU;IAAArB,QAAA,EACpC3D,KAAA,CAAChB,cAAc;MACbiB,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAE;MAC1CkC,KAAK,EAAEsB,MAAM,CAACwB,QAAS;MAAAtB,QAAA,GAGvB3D,KAAA,CAACxB,IAAI;QAAC2D,KAAK,EAAEsB,MAAM,CAACyB,MAAO;QAAAvB,QAAA,GACzB7D,IAAA,CAAClB,gBAAgB;UAACwD,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;YAAAlC,cAAA,GAAAU,CAAA;YAAAV,cAAA,GAAAC,CAAA;YAAA,OAAAlB,MAAM,CAAC+D,IAAI,CAAC,CAAC;UAAD,CAAE;UAACb,KAAK,EAAEsB,MAAM,CAAC0B,UAAW;UAAAxB,QAAA,EACvE7D,IAAA,CAACV,SAAS;YAAC8E,IAAI,EAAE,EAAG;YAACC,KAAK,EAAC;UAAO,CAAE;QAAC,CACrB,CAAC,EACnBrE,IAAA,CAACrB,IAAI;UAAC0D,KAAK,EAAEsB,MAAM,CAACrC,KAAM;UAAAuC,QAAA,EAAC;QAAkB,CAAM,CAAC,EACpD7D,IAAA,CAACtB,IAAI;UAAC2D,KAAK,EAAEsB,MAAM,CAAC2B;QAAY,CAAE,CAAC;MAAA,CAC/B,CAAC,EAEPpF,KAAA,CAACnB,UAAU;QAACsD,KAAK,EAAEsB,MAAM,CAAC4B,OAAQ;QAACC,4BAA4B,EAAE,KAAM;QAAA3B,QAAA,GAErE3D,KAAA,CAACd,IAAI;UAACiD,KAAK,EAAEsB,MAAM,CAAC8B,YAAa;UAAA5B,QAAA,GAC/B3D,KAAA,CAACxB,IAAI;YAAC2D,KAAK,EAAEsB,MAAM,CAAC+B,cAAe;YAAA7B,QAAA,GACjC7D,IAAA,CAACT,MAAM;cAAC6E,IAAI,EAAE,EAAG;cAACC,KAAK,EAAElE,MAAM,CAACG;YAAQ,CAAE,CAAC,EAC3CN,IAAA,CAACrB,IAAI;cAAC0D,KAAK,EAAEsB,MAAM,CAACgC,aAAc;cAAA9B,QAAA,EAAC;YAAoB,CAAM,CAAC;UAAA,CAC1D,CAAC,EACP7D,IAAA,CAACrB,IAAI;YAAC0D,KAAK,EAAEsB,MAAM,CAACiC,YAAa;YAAA/B,QAAA,EAAC;UAElC,CAAM,CAAC;QAAA,CACH,CAAC,EAGNL,qBAAqB,CAAC,SAAS,EAAE,sBAAsB,CAAC,EACxDA,qBAAqB,CAAC,MAAM,EAAE,YAAY,CAAC,EAC3CA,qBAAqB,CAAC,aAAa,EAAE,iBAAiB,CAAC,EAGxDtD,KAAA,CAACd,IAAI;UAACiD,KAAK,EAAEsB,MAAM,CAACkC,QAAS;UAAAhC,QAAA,GAC3B7D,IAAA,CAACrB,IAAI;YAAC0D,KAAK,EAAEsB,MAAM,CAACmC,SAAU;YAAAjC,QAAA,EAAC;UAAe,CAAM,CAAC,EACrD7D,IAAA,CAACrB,IAAI;YAAC0D,KAAK,EAAEsB,MAAM,CAACoC,eAAgB;YAAAlC,QAAA,EAAC;UAErC,CAAM,CAAC,EAEP3D,KAAA,CAACxB,IAAI;YAAC2D,KAAK,EAAEsB,MAAM,CAACqC,WAAY;YAAAnC,QAAA,GAC9B3D,KAAA,CAACpB,gBAAgB;cACfuD,KAAK,EAAEsB,MAAM,CAACsC,UAAW;cACzB3D,OAAO,EAAEgB,gBAAiB;cAAAO,QAAA,GAE1B7D,IAAA,CAACN,QAAQ;gBAAC0E,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAElE,MAAM,CAACG;cAAQ,CAAE,CAAC,EAC7CN,IAAA,CAACrB,IAAI;gBAAC0D,KAAK,EAAEsB,MAAM,CAACuC,cAAe;gBAAArC,QAAA,EAAC;cAAc,CAAM,CAAC;YAAA,CACzC,CAAC,EAEnB3D,KAAA,CAACpB,gBAAgB;cACfuD,KAAK,EAAE,CAACsB,MAAM,CAACsC,UAAU,EAAEtC,MAAM,CAACwC,YAAY,CAAE;cAChD7D,OAAO,EAAEiB,mBAAoB;cAAAM,QAAA,GAE7B7D,IAAA,CAACL,IAAI;gBAACyE,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAElE,MAAM,CAACS;cAAI,CAAE,CAAC,EACrCZ,IAAA,CAACrB,IAAI;gBAAC0D,KAAK,EAAE,CAACsB,MAAM,CAACuC,cAAc,EAAEvC,MAAM,CAACyC,gBAAgB,CAAE;gBAAAvC,QAAA,EAAC;cAE/D,CAAM,CAAC;YAAA,CACS,CAAC;UAAA,CACf,CAAC;QAAA,CACH,CAAC,EAGP7D,IAAA,CAACtB,IAAI;UAAC2D,KAAK,EAAEsB,MAAM,CAAC0C,aAAc;UAAAxC,QAAA,EAChC7D,IAAA,CAACX,MAAM;YACLiC,KAAK,EAAC,uBAAuB;YAC7BgB,OAAO,EAAEM,UAAW;YACpB1B,OAAO,EAAEA,OAAQ;YACjBmB,KAAK,EAAEsB,MAAM,CAAC2C;UAAW,CAC1B;QAAC,CACE,CAAC;MAAA,CACG,CAAC;IAAA,CACC;EAAC,CACL,CAAC;AAEnB;AAEA,IAAM3C,MAAM,IAAAvD,cAAA,GAAAC,CAAA,QAAGzB,UAAU,CAAC2H,MAAM,CAAC;EAC/BrB,SAAS,EAAE;IACTsB,IAAI,EAAE;EACR,CAAC;EACDrB,QAAQ,EAAE;IACRqB,IAAI,EAAE;EACR,CAAC;EACDpB,MAAM,EAAE;IACNqB,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BC,iBAAiB,EAAE,EAAE;IACrBC,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACjB,CAAC;EACDzB,UAAU,EAAE;IACV0B,OAAO,EAAE;EACX,CAAC;EACDzF,KAAK,EAAE;IACL0F,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,gBAAgB;IAC5B5C,KAAK,EAAElE,MAAM,CAACK;EAChB,CAAC;EACD8E,WAAW,EAAE;IACX4B,KAAK,EAAE;EACT,CAAC;EACD3B,OAAO,EAAE;IACPiB,IAAI,EAAE,CAAC;IACPI,iBAAiB,EAAE;EACrB,CAAC;EACDnB,YAAY,EAAE;IACZsB,OAAO,EAAE,EAAE;IACXI,YAAY,EAAE;EAChB,CAAC;EACDzB,cAAc,EAAE;IACde,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBS,YAAY,EAAE;EAChB,CAAC;EACDxB,aAAa,EAAE;IACbqB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,gBAAgB;IAC5B5C,KAAK,EAAElE,MAAM,CAACM,IAAI;IAClB2G,UAAU,EAAE;EACd,CAAC;EACDxB,YAAY,EAAE;IACZoB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3B5C,KAAK,EAAElE,MAAM,CAACO,IAAI;IAClB2G,UAAU,EAAE;EACd,CAAC;EACDzD,YAAY,EAAE;IACZmD,OAAO,EAAE,EAAE;IACXI,YAAY,EAAE;EAChB,CAAC;EACDrD,aAAa,EAAE;IACbkD,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,gBAAgB;IAC5B5C,KAAK,EAAElE,MAAM,CAACM,IAAI;IAClB0G,YAAY,EAAE;EAChB,CAAC;EACDnD,UAAU,EAAE;IACVyC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BW,eAAe,EAAE,EAAE;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,iBAAiB,EAAErH,MAAM,CAACQ;EAC5B,CAAC;EACDsD,WAAW,EAAE;IACXwC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBF,IAAI,EAAE;EACR,CAAC;EACDtC,WAAW,EAAE;IACXgD,KAAK,EAAE,EAAE;IACTO,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,EAAE;IAChBC,eAAe,EAAExH,MAAM,CAACQ,SAAS;IACjC+F,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBiB,WAAW,EAAE;EACf,CAAC;EACDzD,YAAY,EAAE;IACZwD,eAAe,EAAE;EACnB,CAAC;EACDrD,WAAW,EAAE;IACXkC,IAAI,EAAE;EACR,CAAC;EACDjC,eAAe,EAAE;IACfkC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBS,YAAY,EAAE;EAChB,CAAC;EACD3C,YAAY,EAAE;IACZwC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,cAAc;IAC1B5C,KAAK,EAAElE,MAAM,CAACM;EAChB,CAAC;EACDgE,aAAa,EAAE;IACb2C,UAAU,EAAE,CAAC;IACbR,iBAAiB,EAAE,CAAC;IACpBU,eAAe,EAAE,CAAC;IAClBK,eAAe,EAAExH,MAAM,CAACS,GAAG;IAC3B8G,YAAY,EAAE;EAChB,CAAC;EACDhD,YAAY,EAAE;IACZsC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,cAAc;IAC1B5C,KAAK,EAAElE,MAAM,CAACK;EAChB,CAAC;EACDmE,kBAAkB,EAAE;IAClBqC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3B5C,KAAK,EAAElE,MAAM,CAACO;EAChB,CAAC;EACDmF,QAAQ,EAAE;IACRkB,OAAO,EAAE,EAAE;IACXI,YAAY,EAAE;EAChB,CAAC;EACDrB,SAAS,EAAE;IACTkB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,gBAAgB;IAC5B5C,KAAK,EAAElE,MAAM,CAACM,IAAI;IAClB0G,YAAY,EAAE;EAChB,CAAC;EACDpB,eAAe,EAAE;IACfiB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,eAAe;IAC3B5C,KAAK,EAAElE,MAAM,CAACO,IAAI;IAClByG,YAAY,EAAE;EAChB,CAAC;EACDnB,WAAW,EAAE;IACX6B,GAAG,EAAE;EACP,CAAC;EACD5B,UAAU,EAAE;IACVQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBY,eAAe,EAAE,EAAE;IACnBV,iBAAiB,EAAE,EAAE;IACrBc,YAAY,EAAE,CAAC;IACfC,eAAe,EAAExH,MAAM,CAACQ;EAC1B,CAAC;EACDwF,YAAY,EAAE;IACZwB,eAAe,EAAE;EACnB,CAAC;EACDzB,cAAc,EAAE;IACdc,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,cAAc;IAC1B5C,KAAK,EAAElE,MAAM,CAACM,IAAI;IAClB2G,UAAU,EAAE;EACd,CAAC;EACDhB,gBAAgB,EAAE;IAChB/B,KAAK,EAAElE,MAAM,CAACS;EAChB,CAAC;EACDyF,aAAa,EAAE;IACbiB,eAAe,EAAE;EACnB,CAAC;EACDhB,UAAU,EAAE;IACVY,KAAK,EAAE;EACT;AACF,CAAC,CAAC", "ignoreList": []}