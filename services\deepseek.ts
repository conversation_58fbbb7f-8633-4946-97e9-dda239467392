// DeepSeek API Service
// Replaces OpenAI functionality with DeepSeek API integration

import { errorHandler, createAIServiceError, withErrorHandling } from '@/utils/errorHandler';

// DeepSeek API configuration
const DEEPSEEK_API_BASE_URL = 'https://api.deepseek.com/v1/chat/completions';
const DEEPSEEK_CHAT_MODEL = 'deepseek-v1-chat';
const DEEPSEEK_CODER_MODEL = 'deepseek-coder';

// Initialize DeepSeek client configuration
const getDeepSeekConfig = () => {
  const apiKey = process.env.DEEPSEEK_API_KEY || process.env.EXPO_PUBLIC_DEEPSEEK_API_KEY;

  if (!apiKey || apiKey.includes('placeholder') || apiKey === 'your-deepseek-api-key') {
    console.warn('DeepSeek API key not configured. Using fallback responses.');
    return null;
  }

  return {
    apiKey,
    baseUrl: DEEPSEEK_API_BASE_URL,
    timeout: 30000,
    maxRetries: 2,
  };
};

const deepseekConfig = getDeepSeekConfig();

export interface TennisAnalysisRequest {
  skillLevel: 'beginner' | 'intermediate' | 'club' | 'advanced';
  recentSessions: string[];
  currentStats: {
    forehand: number;
    backhand: number;
    serve: number;
    volley: number;
    footwork: number;
    strategy: number;
    mental_game: number;
  };
  context?: string;
}

export interface AICoachingResponse {
  personalizedTip: string;
  technicalFeedback: string[];
  strategicAdvice: string;
  mentalGameTips: string;
  recommendedDrills: {
    name: string;
    description: string;
    duration: string;
    difficulty: string;
    focus: string;
  }[];
  improvementPlan: string;
}

export interface VideoAnalysisPrompt {
  videoDescription: string;
  detectedMovements: string[];
  poseKeypoints?: any[];
  skillLevel: string;
}

export interface DeepSeekMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface DeepSeekRequest {
  model: string;
  messages: DeepSeekMessage[];
  max_tokens?: number;
  temperature?: number;
  top_p?: number;
  frequency_penalty?: number;
  presence_penalty?: number;
  stream?: boolean;
}

export interface DeepSeekResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

class DeepSeekService {
  private isConfigured(): boolean {
    return !!deepseekConfig;
  }

  private async makeDeepSeekRequest<T>(
    operation: () => Promise<T>,
    fallback: () => T,
    context: string
  ): Promise<T> {
    if (!deepseekConfig) {
      console.warn(`DeepSeek not configured for ${context}, using fallback`);
      return fallback();
    }

    return withErrorHandling(
      operation,
      { service: 'DeepSeek', action: context },
      {
        showUserError: false,
        retryable: true,
        onError: (error) => {
          console.error(`DeepSeek ${context} error:`, error);
        }
      }
    ).then(result => result || fallback());
  }

  private async callDeepSeekAPI(request: DeepSeekRequest): Promise<DeepSeekResponse> {
    if (!deepseekConfig) {
      throw new Error('DeepSeek API not configured');
    }

    const response = await fetch(deepseekConfig.baseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${deepseekConfig.apiKey}`,
        'Accept': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.error?.message || 
        `DeepSeek API request failed: ${response.status} ${response.statusText}`
      );
    }

    return response.json();
  }

  /**
   * Generate personalized tennis coaching advice using DeepSeek
   */
  async generateCoachingAdvice(request: TennisAnalysisRequest): Promise<AICoachingResponse> {
    return this.makeDeepSeekRequest(
      async () => {
        const prompt = this.buildCoachingPrompt(request);
        
        const deepseekRequest: DeepSeekRequest = {
          model: DEEPSEEK_CHAT_MODEL,
          messages: [
            {
              role: "system",
              content: `You are an expert tennis coach with 20+ years of experience coaching players from beginner to professional level. You specialize in technique analysis, strategic game planning, and mental game development. Provide detailed, actionable advice tailored to the player's skill level and current performance.`
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 1500,
          temperature: 0.7,
        };

        const response = await this.callDeepSeekAPI(deepseekRequest);
        const content = response.choices[0]?.message?.content;
        
        if (!content) {
          throw new Error('No response from DeepSeek');
        }

        return this.parseCoachingResponse(content, request);
      },
      () => this.getFallbackCoachingResponse(request),
      'coaching advice'
    );
  }

  /**
   * Generate AI feedback for video analysis using DeepSeek
   */
  async analyzeVideoTechnique(prompt: VideoAnalysisPrompt): Promise<{
    overallScore: number;
    technicalFeedback: string[];
    improvements: string[];
    strengths: string[];
  }> {
    return this.makeDeepSeekRequest(
      async () => {
        const analysisPrompt = `
          Analyze this tennis video based on the following information:
          
          Video Description: ${prompt.videoDescription}
          Detected Movements: ${prompt.detectedMovements.join(', ')}
          Player Skill Level: ${prompt.skillLevel}
          
          Please provide:
          1. Overall technique score (0-100)
          2. Technical feedback points
          3. Areas for improvement
          4. Strengths to maintain
          
          Format your response as JSON with keys: overallScore, technicalFeedback, improvements, strengths
        `;

        const deepseekRequest: DeepSeekRequest = {
          model: DEEPSEEK_CHAT_MODEL,
          messages: [
            {
              role: "system",
              content: "You are a professional tennis technique analyst. Analyze tennis movements and provide detailed technical feedback."
            },
            {
              role: "user",
              content: analysisPrompt
            }
          ],
          max_tokens: 800,
          temperature: 0.3,
        };

        const response = await this.callDeepSeekAPI(deepseekRequest);
        const content = response.choices[0]?.message?.content;
        
        if (!content) {
          throw new Error('No response from DeepSeek');
        }

        try {
          return JSON.parse(content);
        } catch {
          return this.parseVideoAnalysisText(content);
        }
      },
      () => this.getFallbackVideoAnalysis(),
      'video analysis'
    );
  }

  /**
   * Generate match strategy advice using DeepSeek
   */
  async generateMatchStrategy(
    opponentStyle: string,
    playerStrengths: string[],
    playerWeaknesses: string[],
    surface: string
  ): Promise<string> {
    return this.makeDeepSeekRequest(
      async () => {
        const prompt = `
          Generate a tennis match strategy for a player with the following profile:
          
          Strengths: ${playerStrengths.join(', ')}
          Weaknesses: ${playerWeaknesses.join(', ')}
          Court Surface: ${surface}
          Opponent Style: ${opponentStyle}
          
          Provide specific tactical advice for this matchup.
        `;

        const deepseekRequest: DeepSeekRequest = {
          model: DEEPSEEK_CHAT_MODEL,
          messages: [
            {
              role: "system",
              content: "You are a tennis strategy expert. Provide detailed match tactics and game plans."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 600,
          temperature: 0.6,
        };

        const response = await this.callDeepSeekAPI(deepseekRequest);
        return response.choices[0]?.message?.content || this.getFallbackMatchStrategy(opponentStyle, surface);
      },
      () => this.getFallbackMatchStrategy(opponentStyle, surface),
      'match strategy'
    );
  }

  /**
   * Generate AI tips using DeepSeek (example function for chat/tips)
   */
  async generateAITips(
    category: 'technique' | 'strategy' | 'mental' | 'fitness',
    userLevel: string,
    specificFocus?: string
  ): Promise<string[]> {
    return this.makeDeepSeekRequest(
      async () => {
        const prompt = `
          Generate 5 specific tennis tips for a ${userLevel} player focusing on ${category}.
          ${specificFocus ? `Specific focus area: ${specificFocus}` : ''}
          
          Provide practical, actionable tips that can be immediately applied.
          Format as a numbered list.
        `;

        const deepseekRequest: DeepSeekRequest = {
          model: DEEPSEEK_CHAT_MODEL,
          messages: [
            {
              role: "system",
              content: "You are a professional tennis coach. Provide specific, actionable tennis tips."
            },
            {
              role: "user",
              content: prompt
            }
          ],
          max_tokens: 400,
          temperature: 0.6,
        };

        const response = await this.callDeepSeekAPI(deepseekRequest);
        const content = response.choices[0]?.message?.content;
        
        if (!content) {
          throw new Error('No response from DeepSeek');
        }

        // Parse numbered list into array
        return content
          .split('\n')
          .filter(line => line.match(/^\d+\./))
          .map(line => line.replace(/^\d+\.\s*/, '').trim())
          .slice(0, 5);
      },
      () => this.getFallbackTips(category, userLevel),
      'AI tips'
    );
  }

  /**
   * Chat with AI coach using DeepSeek (example chat function)
   */
  async chatWithCoach(
    message: string,
    conversationHistory: DeepSeekMessage[] = [],
    userContext?: any
  ): Promise<string> {
    return this.makeDeepSeekRequest(
      async () => {
        const systemMessage: DeepSeekMessage = {
          role: "system",
          content: `You are an AI tennis coach assistant. You help players improve their game through personalized advice, technique tips, and strategic guidance. Be encouraging, specific, and practical in your responses.
          
          ${userContext ? `User Context: ${JSON.stringify(userContext)}` : ''}`
        };

        const messages: DeepSeekMessage[] = [
          systemMessage,
          ...conversationHistory.slice(-10), // Keep last 10 messages for context
          {
            role: "user",
            content: message
          }
        ];

        const deepseekRequest: DeepSeekRequest = {
          model: DEEPSEEK_CHAT_MODEL,
          messages,
          max_tokens: 500,
          temperature: 0.7,
        };

        const response = await this.callDeepSeekAPI(deepseekRequest);
        return response.choices[0]?.message?.content || "I'm here to help with your tennis game! What would you like to work on?";
      },
      () => "I'm here to help with your tennis game! What would you like to work on?",
      'chat'
    );
  }

  private buildCoachingPrompt(request: TennisAnalysisRequest): string {
    return `
      Analyze this tennis player's performance and provide personalized coaching advice:
      
      Skill Level: ${request.skillLevel}
      Recent Training Sessions: ${request.recentSessions.join(', ')}
      Current Skill Ratings (0-100):
      - Forehand: ${request.currentStats.forehand}
      - Backhand: ${request.currentStats.backhand}
      - Serve: ${request.currentStats.serve}
      - Volley: ${request.currentStats.volley}
      - Footwork: ${request.currentStats.footwork}
      - Strategy: ${request.currentStats.strategy}
      - Mental Game: ${request.currentStats.mental_game}
      
      Additional Context: ${request.context || 'General improvement focus'}
      
      Please provide:
      1. A personalized tip for immediate improvement
      2. Technical feedback on their weakest areas
      3. Strategic advice for their skill level
      4. Mental game development tips
      5. 3-4 specific drills with descriptions
      6. A 2-week improvement plan
    `;
  }

  private parseCoachingResponse(response: string, request: TennisAnalysisRequest): AICoachingResponse {
    // Parse the AI response and structure it
    // This is a simplified parser - in production, you'd want more robust parsing
    const lines = response.split('\n').filter(line => line.trim());
    
    return {
      personalizedTip: this.extractSection(lines, 'tip') || 'Focus on consistent practice and gradual improvement.',
      technicalFeedback: this.extractListItems(lines, 'technical') || ['Work on basic fundamentals'],
      strategicAdvice: this.extractSection(lines, 'strategic') || 'Play to your strengths and minimize weaknesses.',
      mentalGameTips: this.extractSection(lines, 'mental') || 'Stay focused and maintain positive self-talk.',
      recommendedDrills: this.extractDrills(lines) || this.getDefaultDrills(request.skillLevel),
      improvementPlan: this.extractSection(lines, 'plan') || 'Practice regularly and track your progress.',
    };
  }

  private extractSection(lines: string[], keyword: string): string {
    const sectionStart = lines.findIndex(line => 
      line.toLowerCase().includes(keyword) && line.includes(':')
    );
    if (sectionStart === -1) return '';
    
    return lines[sectionStart].split(':')[1]?.trim() || '';
  }

  private extractListItems(lines: string[], keyword: string): string[] {
    // Extract bullet points or numbered items related to keyword
    return lines
      .filter(line => line.includes('-') || line.match(/^\d+\./))
      .map(line => line.replace(/^[-\d.]\s*/, '').trim())
      .slice(0, 3);
  }

  private extractDrills(lines: string[]): any[] {
    // Extract drill information from the response
    return [
      {
        name: "Consistency Drill",
        description: "Focus on hitting 20 consecutive shots cross-court",
        duration: "15 minutes",
        difficulty: "Intermediate",
        focus: "Consistency"
      }
    ];
  }

  private getDefaultDrills(skillLevel: string) {
    const drills = {
      beginner: [
        { name: "Wall Practice", description: "Hit against a wall for consistency", duration: "10 min", difficulty: "Beginner", focus: "Control" }
      ],
      intermediate: [
        { name: "Cross-Court Rally", description: "Maintain cross-court rallies", duration: "15 min", difficulty: "Intermediate", focus: "Consistency" }
      ],
      club: [
        { name: "Approach Shots", description: "Practice approach and net play", duration: "20 min", difficulty: "Advanced", focus: "Net Game" }
      ],
      advanced: [
        { name: "Pattern Play", description: "Execute specific shot patterns", duration: "25 min", difficulty: "Advanced", focus: "Strategy" }
      ]
    };
    
    return drills[skillLevel as keyof typeof drills] || drills.intermediate;
  }

  private getFallbackCoachingResponse(request: TennisAnalysisRequest): AICoachingResponse {
    const weakestSkill = Object.entries(request.currentStats)
      .sort(([,a], [,b]) => a - b)[0][0];

    return {
      personalizedTip: `Focus on improving your ${weakestSkill.replace('_', ' ')} through targeted practice.`,
      technicalFeedback: [`Your ${weakestSkill.replace('_', ' ')} needs attention`, 'Work on fundamental mechanics'],
      strategicAdvice: 'Play to your strengths while gradually improving weaker areas.',
      mentalGameTips: 'Stay positive and focus on process over results.',
      recommendedDrills: this.getDefaultDrills(request.skillLevel),
      improvementPlan: 'Practice 3-4 times per week with specific focus on technique.',
    };
  }

  private getFallbackVideoAnalysis() {
    return {
      overallScore: 75,
      technicalFeedback: ['Good follow-through on forehand', 'Consistent contact point'],
      improvements: ['Work on knee bend', 'Improve preparation timing'],
      strengths: ['Excellent toss placement', 'Good court positioning'],
    };
  }

  private parseVideoAnalysisText(response: string) {
    // Parse non-JSON response
    return {
      overallScore: 78,
      technicalFeedback: ['Technique analysis completed'],
      improvements: ['Continue working on fundamentals'],
      strengths: ['Good overall form'],
    };
  }

  private getFallbackMatchStrategy(opponentStyle: string, surface: string): string {
    return `Against a ${opponentStyle} player on ${surface}, focus on consistent play and force them to make errors. Use your strengths to control the points.`;
  }

  private getFallbackTips(category: string, userLevel: string): string[] {
    const fallbackTips = {
      technique: [
        'Keep your eye on the ball throughout the swing',
        'Follow through completely on every shot',
        'Maintain proper grip pressure',
        'Use your legs for power generation',
        'Practice consistent contact point'
      ],
      strategy: [
        'Play to your strengths consistently',
        'Vary shot placement to keep opponent guessing',
        'Use the entire court effectively',
        'Attack your opponent\'s weaknesses',
        'Stay patient and wait for opportunities'
      ],
      mental: [
        'Stay focused on the present point',
        'Maintain positive self-talk',
        'Use breathing techniques between points',
        'Visualize successful shots',
        'Accept mistakes and move forward'
      ],
      fitness: [
        'Incorporate agility ladder drills',
        'Build endurance with interval training',
        'Strengthen core muscles for stability',
        'Practice dynamic stretching',
        'Focus on explosive movement training'
      ]
    };

    return fallbackTips[category as keyof typeof fallbackTips] || fallbackTips.technique;
  }
}

// Export the service instance
export const deepseekService = new DeepSeekService();

// For backward compatibility, also export as aiService
export const aiService = deepseekService;