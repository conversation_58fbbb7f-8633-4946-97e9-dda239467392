61dbe27f85140dbd056be3a4fc5c31ce
'use strict';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = usePressEvents;
var _PressResponder = _interopRequireDefault(require("./PressResponder"));
var _react = require("react");
function usePressEvents(hostRef, config) {
  var pressResponderRef = (0, _react.useRef)(null);
  if (pressResponderRef.current == null) {
    pressResponderRef.current = new _PressResponder.default(config);
  }
  var pressResponder = pressResponderRef.current;
  (0, _react.useEffect)(function () {
    pressResponder.configure(config);
  }, [config, pressResponder]);
  (0, _react.useEffect)(function () {
    return function () {
      pressResponder.reset();
    };
  }, [pressResponder]);
  (0, _react.useDebugValue)(config);
  return pressResponder.getEventHandlers();
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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