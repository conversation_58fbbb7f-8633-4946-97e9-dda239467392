{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_normalizeColors", "processColor", "color", "undefined", "int32Color", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _normalizeColors = _interopRequireDefault(require(\"@react-native/normalize-colors\"));\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar processColor = color => {\n  if (color === undefined || color === null) {\n    return color;\n  }\n\n  // convert number and hex\n  var int32Color = (0, _normalizeColors.default)(color);\n  if (int32Color === undefined || int32Color === null) {\n    return undefined;\n  }\n  int32Color = (int32Color << 24 | int32Color >>> 8) >>> 0;\n  return int32Color;\n};\nvar _default = exports.default = processColor;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,gBAAgB,GAAGL,sBAAsB,CAACC,OAAO,CAAC,gCAAgC,CAAC,CAAC;AAWxF,IAAIK,YAAY,GAAG,SAAfA,YAAYA,CAAGC,KAAK,EAAI;EAC1B,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,IAAI,EAAE;IACzC,OAAOA,KAAK;EACd;EAGA,IAAIE,UAAU,GAAG,CAAC,CAAC,EAAEJ,gBAAgB,CAACH,OAAO,EAAEK,KAAK,CAAC;EACrD,IAAIE,UAAU,KAAKD,SAAS,IAAIC,UAAU,KAAK,IAAI,EAAE;IACnD,OAAOD,SAAS;EAClB;EACAC,UAAU,GAAG,CAACA,UAAU,IAAI,EAAE,GAAGA,UAAU,KAAK,CAAC,MAAM,CAAC;EACxD,OAAOA,UAAU;AACnB,CAAC;AACD,IAAIC,QAAQ,GAAGP,OAAO,CAACD,OAAO,GAAGI,YAAY;AAC7CK,MAAM,CAACR,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}