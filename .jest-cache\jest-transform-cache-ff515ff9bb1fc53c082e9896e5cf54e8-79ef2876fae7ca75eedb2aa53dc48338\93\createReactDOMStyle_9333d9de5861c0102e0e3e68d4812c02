b90fe1f91e8bb1d025719775a80d4b19
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _normalizeValueWithProperty = _interopRequireDefault(require("./normalizeValueWithProperty"));
var _canUseDom = _interopRequireDefault(require("../../../modules/canUseDom"));
var emptyObject = {};
var supportsCSS3TextDecoration = !_canUseDom.default || window.CSS != null && window.CSS.supports != null && (window.CSS.supports('text-decoration-line', 'none') || window.CSS.supports('-webkit-text-decoration-line', 'none'));
var MONOSPACE_FONT_STACK = 'monospace,monospace';
var SYSTEM_FONT_STACK = '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Helvetica,Arial,sans-serif';
var STYLE_SHORT_FORM_EXPANSIONS = {
  borderColor: ['borderTopColor', 'borderRightColor', 'borderBottomColor', 'borderLeftColor'],
  borderBlockColor: ['borderTopColor', 'borderBottomColor'],
  borderInlineColor: ['borderRightColor', 'borderLeftColor'],
  borderRadius: ['borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomRightRadius', 'borderBottomLeftRadius'],
  borderStyle: ['borderTopStyle', 'borderRightStyle', 'borderBottomStyle', 'borderLeftStyle'],
  borderBlockStyle: ['borderTopStyle', 'borderBottomStyle'],
  borderInlineStyle: ['borderRightStyle', 'borderLeftStyle'],
  borderWidth: ['borderTopWidth', 'borderRightWidth', 'borderBottomWidth', 'borderLeftWidth'],
  borderBlockWidth: ['borderTopWidth', 'borderBottomWidth'],
  borderInlineWidth: ['borderRightWidth', 'borderLeftWidth'],
  insetBlock: ['top', 'bottom'],
  insetInline: ['left', 'right'],
  marginBlock: ['marginTop', 'marginBottom'],
  marginInline: ['marginRight', 'marginLeft'],
  paddingBlock: ['paddingTop', 'paddingBottom'],
  paddingInline: ['paddingRight', 'paddingLeft'],
  overflow: ['overflowX', 'overflowY'],
  overscrollBehavior: ['overscrollBehaviorX', 'overscrollBehaviorY'],
  borderBlockStartColor: ['borderTopColor'],
  borderBlockStartStyle: ['borderTopStyle'],
  borderBlockStartWidth: ['borderTopWidth'],
  borderBlockEndColor: ['borderBottomColor'],
  borderBlockEndStyle: ['borderBottomStyle'],
  borderBlockEndWidth: ['borderBottomWidth'],
  borderEndStartRadius: ['borderBottomLeftRadius'],
  borderEndEndRadius: ['borderBottomRightRadius'],
  borderStartStartRadius: ['borderTopLeftRadius'],
  borderStartEndRadius: ['borderTopRightRadius'],
  insetBlockEnd: ['bottom'],
  insetBlockStart: ['top'],
  marginBlockStart: ['marginTop'],
  marginBlockEnd: ['marginBottom'],
  paddingBlockStart: ['paddingTop'],
  paddingBlockEnd: ['paddingBottom']
};
var createReactDOMStyle = function createReactDOMStyle(style, isInline) {
  if (!style) {
    return emptyObject;
  }
  var resolvedStyle = {};
  var _loop = function _loop() {
    var value = style[prop];
    if (value == null) {
      return "continue";
    }
    if (prop === 'backgroundClip') {
      if (value === 'text') {
        resolvedStyle.backgroundClip = value;
        resolvedStyle.WebkitBackgroundClip = value;
      }
    } else if (prop === 'flex') {
      if (value === -1) {
        resolvedStyle.flexGrow = 0;
        resolvedStyle.flexShrink = 1;
        resolvedStyle.flexBasis = 'auto';
      } else {
        resolvedStyle.flex = value;
      }
    } else if (prop === 'font') {
      resolvedStyle[prop] = value.replace('System', SYSTEM_FONT_STACK);
    } else if (prop === 'fontFamily') {
      if (value.indexOf('System') > -1) {
        var stack = value.split(/,\s*/);
        stack[stack.indexOf('System')] = SYSTEM_FONT_STACK;
        resolvedStyle[prop] = stack.join(',');
      } else if (value === 'monospace') {
        resolvedStyle[prop] = MONOSPACE_FONT_STACK;
      } else {
        resolvedStyle[prop] = value;
      }
    } else if (prop === 'textDecorationLine') {
      if (!supportsCSS3TextDecoration) {
        resolvedStyle.textDecoration = value;
      } else {
        resolvedStyle.textDecorationLine = value;
      }
    } else if (prop === 'writingDirection') {
      resolvedStyle.direction = value;
    } else {
      var _value = (0, _normalizeValueWithProperty.default)(style[prop], prop);
      var longFormProperties = STYLE_SHORT_FORM_EXPANSIONS[prop];
      if (isInline && prop === 'inset') {
        if (style.insetInline == null) {
          resolvedStyle.left = _value;
          resolvedStyle.right = _value;
        }
        if (style.insetBlock == null) {
          resolvedStyle.top = _value;
          resolvedStyle.bottom = _value;
        }
      } else if (isInline && prop === 'margin') {
        if (style.marginInline == null) {
          resolvedStyle.marginLeft = _value;
          resolvedStyle.marginRight = _value;
        }
        if (style.marginBlock == null) {
          resolvedStyle.marginTop = _value;
          resolvedStyle.marginBottom = _value;
        }
      } else if (isInline && prop === 'padding') {
        if (style.paddingInline == null) {
          resolvedStyle.paddingLeft = _value;
          resolvedStyle.paddingRight = _value;
        }
        if (style.paddingBlock == null) {
          resolvedStyle.paddingTop = _value;
          resolvedStyle.paddingBottom = _value;
        }
      } else if (longFormProperties) {
        longFormProperties.forEach(function (longForm, i) {
          if (style[longForm] == null) {
            resolvedStyle[longForm] = _value;
          }
        });
      } else {
        resolvedStyle[prop] = _value;
      }
    }
  };
  for (var prop in style) {
    var _ret = _loop();
    if (_ret === "continue") continue;
  }
  return resolvedStyle;
};
var _default = exports.default = createReactDOMStyle;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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