{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_react", "_canUseDom", "useLayoutEffectImpl", "useLayoutEffect", "useEffect", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _react = require(\"react\");\nvar _canUseDom = _interopRequireDefault(require(\"../canUseDom\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * useLayoutEffect throws an error on the server. On the few occasions where is\n * problematic, use this hook.\n *\n * \n */\n\nvar useLayoutEffectImpl = _canUseDom.default ? _react.useLayoutEffect : _react.useEffect;\nvar _default = exports.default = useLayoutEffectImpl;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,MAAM,GAAGJ,OAAO,CAAC,OAAO,CAAC;AAC7B,IAAIK,UAAU,GAAGN,sBAAsB,CAACC,OAAO,eAAe,CAAC,CAAC;AAahE,IAAIM,mBAAmB,GAAGD,UAAU,CAACJ,OAAO,GAAGG,MAAM,CAACG,eAAe,GAAGH,MAAM,CAACI,SAAS;AACxF,IAAIC,QAAQ,GAAGP,OAAO,CAACD,OAAO,GAAGK,mBAAmB;AACpDI,MAAM,CAACR,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}