f9c7da1710dab3e1f5a5bcf946e78ebc
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.validate = validate;
var _postcssValueParser = _interopRequireDefault(require("postcss-value-parser"));
var invalidShortforms = {
  background: true,
  borderBottom: true,
  borderLeft: true,
  borderRight: true,
  borderTop: true,
  font: true,
  grid: true,
  outline: true,
  textDecoration: true
};
var invalidMultiValueShortforms = {
  flex: true,
  margin: true,
  padding: true,
  borderColor: true,
  borderRadius: true,
  borderStyle: true,
  borderWidth: true,
  inset: true,
  insetBlock: true,
  insetInline: true,
  marginBlock: true,
  marginInline: true,
  marginHorizontal: true,
  marginVertical: true,
  paddingBlock: true,
  paddingInline: true,
  paddingHorizontal: true,
  paddingVertical: true,
  overflow: true,
  overscrollBehavior: true,
  backgroundPosition: true
};
function error(message) {
  console.error(message);
}
function validate(obj) {
  for (var k in obj) {
    var prop = k.trim();
    var value = obj[prop];
    var isInvalid = false;
    if (value === null) {
      continue;
    }
    if (typeof value === 'string' && value.indexOf('!important') > -1) {
      error("Invalid style declaration \"" + prop + ":" + value + "\". Values cannot include \"!important\"");
      isInvalid = true;
    } else {
      var suggestion = '';
      if (prop === 'animation' || prop === 'animationName') {
        suggestion = 'Did you mean "animationKeyframes"?';
        isInvalid = true;
      } else if (prop === 'direction') {
        suggestion = 'Did you mean "writingDirection"?';
        isInvalid = true;
      } else if (invalidShortforms[prop]) {
        suggestion = 'Please use long-form properties.';
        isInvalid = true;
      } else if (invalidMultiValueShortforms[prop]) {
        if (typeof value === 'string' && (0, _postcssValueParser.default)(value).nodes.length > 1) {
          suggestion = "Value is \"" + value + "\" but only single values are supported.";
          isInvalid = true;
        }
      }
      if (suggestion !== '') {
        error("Invalid style property of \"" + prop + "\". " + suggestion);
      }
    }
    if (isInvalid) {
      delete obj[k];
    }
  }
}
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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