/**
 * Integration Demo Page
 * Showcases Firebase FCM, PostHog Analytics, Sentry Error Monitoring, 
 * Magic.link Auth, and Stripe Payments integration
 */

import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { ArrowLeft } from 'lucide-react-native';
import { TouchableOpacity } from 'react-native';

import IntegrationDemo from '@/components/IntegrationDemo';
import ErrorBoundary from '@/components/ErrorBoundary';

export default function IntegrationDemoPage() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Service Integrations</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content}>
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>🔧 Integrated Services</Text>
          <Text style={styles.infoText}>
            This demo showcases the complete integration of modern services that replace the previous stack:
          </Text>
          
          <View style={styles.serviceList}>
            <View style={styles.serviceItem}>
              <Text style={styles.serviceIcon}>📱</Text>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>Firebase Cloud Messaging + Expo Push</Text>
                <Text style={styles.serviceDescription}>Replaces OneSignal for push notifications</Text>
              </View>
            </View>

            <View style={styles.serviceItem}>
              <Text style={styles.serviceIcon}>📊</Text>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>PostHog Analytics</Text>
                <Text style={styles.serviceDescription}>Replaces Amplitude/Mixpanel for user analytics</Text>
              </View>
            </View>

            <View style={styles.serviceItem}>
              <Text style={styles.serviceIcon}>🐛</Text>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>Sentry Error Monitoring</Text>
                <Text style={styles.serviceDescription}>Replaces LogRocket/OpenReplay for error tracking</Text>
              </View>
            </View>

            <View style={styles.serviceItem}>
              <Text style={styles.serviceIcon}>🔐</Text>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>Magic.link + Social Auth</Text>
                <Text style={styles.serviceDescription}>Passwordless auth with Google/Facebook support</Text>
              </View>
            </View>

            <View style={styles.serviceItem}>
              <Text style={styles.serviceIcon}>💳</Text>
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>Stripe Payments</Text>
                <Text style={styles.serviceDescription}>Complete payment processing with Apple/Google Pay</Text>
              </View>
            </View>
          </View>
        </View>

        <ErrorBoundary
          showDetails={__DEV__}
          onError={(error, errorInfo) => {
            console.log('Demo page error caught:', error.message);
          }}
        >
          <IntegrationDemo />
        </ErrorBoundary>

        <View style={styles.implementationSection}>
          <Text style={styles.implementationTitle}>📋 Implementation Details</Text>
          
          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>1. Notifications</Text>
            <Text style={styles.detailText}>
              • Firebase Cloud Messaging for Android/web{'\n'}
              • Expo Push Notifications for Expo apps{'\n'}
              • Unified service with automatic token management{'\n'}
              • Backend API for sending targeted notifications
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>2. Analytics</Text>
            <Text style={styles.detailText}>
              • PostHog for event tracking and user analytics{'\n'}
              • Automatic user identification and properties{'\n'}
              • Tennis-specific event tracking methods{'\n'}
              • Real-time dashboards and insights
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>3. Error Monitoring</Text>
            <Text style={styles.detailText}>
              • Sentry for error capture and performance monitoring{'\n'}
              • Enhanced Error Boundary with automatic reporting{'\n'}
              • Breadcrumb tracking for debugging context{'\n'}
              • Performance transaction monitoring
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>4. Authentication</Text>
            <Text style={styles.detailText}>
              • Magic.link for passwordless authentication{'\n'}
              • Google and Facebook social login{'\n'}
              • Supabase integration for user management{'\n'}
              • Session persistence and automatic refresh
            </Text>
          </View>

          <View style={styles.detailSection}>
            <Text style={styles.detailSubtitle}>5. Payments</Text>
            <Text style={styles.detailText}>
              • Stripe for web and mobile payments{'\n'}
              • Apple Pay and Google Pay integration{'\n'}
              • Subscription management and billing{'\n'}
              • Secure payment processing with webhooks
            </Text>
          </View>
        </View>

        <View style={styles.configSection}>
          <Text style={styles.configTitle}>⚙️ Environment Configuration</Text>
          <View style={styles.configBlock}>
            <Text style={styles.configText}>
{`# Firebase & Notifications
FCM_SERVER_KEY=your-fcm-server-key
EXPO_PUSH_TOKEN=your-expo-push-token

# Analytics & Monitoring  
POSTHOG_PROJECT_API_KEY=phc_your_key
SENTRY_DSN=https://<EMAIL>/project

# Authentication
MAGIC_PUBLISHABLE_KEY=pk_live_your_key
GOOGLE_CLIENT_ID=your-google-client-id
FACEBOOK_APP_ID=your-facebook-app-id

# Payments
STRIPE_PUBLISHABLE_KEY=pk_test_your_key
APPLE_PAY_MERCHANT_ID=merchant.com.acemind
GOOGLE_PAY_MERCHANT_ID=your-google-pay-id`}
            </Text>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  placeholder: {
    width: 34,
  },
  content: {
    flex: 1,
  },
  infoSection: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  serviceList: {
    gap: 15,
  },
  serviceItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  serviceIcon: {
    fontSize: 24,
    marginTop: 2,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  serviceDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  implementationSection: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  implementationTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  detailSection: {
    marginBottom: 20,
  },
  detailSubtitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#007AFF',
    marginBottom: 8,
  },
  detailText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  configSection: {
    backgroundColor: '#fff',
    margin: 20,
    marginTop: 0,
    padding: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  configTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  configBlock: {
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#007AFF',
  },
  configText: {
    fontSize: 12,
    fontFamily: 'monospace',
    color: '#333',
    lineHeight: 18,
  },
});
