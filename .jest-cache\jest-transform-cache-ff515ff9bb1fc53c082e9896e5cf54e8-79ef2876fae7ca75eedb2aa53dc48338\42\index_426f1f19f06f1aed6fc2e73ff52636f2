b5ff45e748c4a474281b51ea8489e1da
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _canUseDom = _interopRequireDefault(require("../../modules/canUseDom"));
function isScreenReaderEnabled() {
  return new Promise(function (resolve, reject) {
    resolve(true);
  });
}
var prefersReducedMotionMedia = _canUseDom.default && typeof window.matchMedia === 'function' ? window.matchMedia('(prefers-reduced-motion: reduce)') : null;
function isReduceMotionEnabled() {
  return new Promise(function (resolve, reject) {
    resolve(prefersReducedMotionMedia ? prefersReducedMotionMedia.matches : true);
  });
}
function addChangeListener(fn) {
  if (prefersReducedMotionMedia != null) {
    prefersReducedMotionMedia.addEventListener != null ? prefersReducedMotionMedia.addEventListener('change', fn) : prefersReducedMotionMedia.addListener(fn);
  }
}
function removeChangeListener(fn) {
  if (prefersReducedMotionMedia != null) {
    prefersReducedMotionMedia.removeEventListener != null ? prefersReducedMotionMedia.removeEventListener('change', fn) : prefersReducedMotionMedia.removeListener(fn);
  }
}
var handlers = {};
var AccessibilityInfo = {
  isScreenReaderEnabled: isScreenReaderEnabled,
  isReduceMotionEnabled: isReduceMotionEnabled,
  fetch: isScreenReaderEnabled,
  addEventListener: function addEventListener(eventName, handler) {
    if (eventName === 'reduceMotionChanged') {
      if (!prefersReducedMotionMedia) {
        return;
      }
      var listener = function listener(event) {
        handler(event.matches);
      };
      addChangeListener(listener);
      handlers[handler] = listener;
    }
    return {
      remove: function remove() {
        return AccessibilityInfo.removeEventListener(eventName, handler);
      }
    };
  },
  setAccessibilityFocus: function setAccessibilityFocus(reactTag) {},
  announceForAccessibility: function announceForAccessibility(announcement) {},
  removeEventListener: function removeEventListener(eventName, handler) {
    if (eventName === 'reduceMotionChanged') {
      var listener = handlers[handler];
      if (!listener || !prefersReducedMotionMedia) {
        return;
      }
      removeChangeListener(listener);
    }
    return;
  }
};
var _default = exports.default = AccessibilityInfo;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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