691e92242ab7e4e1315a3d541d99928d
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var React = _interopRequireWildcard(require("react"));
var _createElement = _interopRequireDefault(require("../createElement"));
var forwardedProps = _interopRequireWildcard(require("../../modules/forwardedProps"));
var _pick = _interopRequireDefault(require("../../modules/pick"));
var _useElementLayout = _interopRequireDefault(require("../../modules/useElementLayout"));
var _useLayoutEffect = _interopRequireDefault(require("../../modules/useLayoutEffect"));
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _usePlatformMethods = _interopRequireDefault(require("../../modules/usePlatformMethods"));
var _useResponderEvents = _interopRequireDefault(require("../../modules/useResponderEvents"));
var _useLocale = require("../../modules/useLocale");
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _TextInputState = _interopRequireDefault(require("../../modules/TextInputState"));
var isSelectionStale = function isSelectionStale(node, selection) {
  var selectionEnd = node.selectionEnd,
    selectionStart = node.selectionStart;
  var start = selection.start,
    end = selection.end;
  return start !== selectionStart || end !== selectionEnd;
};
var setSelection = function setSelection(node, selection) {
  if (isSelectionStale(node, selection)) {
    var start = selection.start,
      end = selection.end;
    try {
      node.setSelectionRange(start, end || start);
    } catch (e) {}
  }
};
var forwardPropsList = Object.assign({}, forwardedProps.defaultProps, forwardedProps.accessibilityProps, forwardedProps.clickProps, forwardedProps.focusProps, forwardedProps.keyboardProps, forwardedProps.mouseProps, forwardedProps.touchProps, forwardedProps.styleProps, {
  autoCapitalize: true,
  autoComplete: true,
  autoCorrect: true,
  autoFocus: true,
  defaultValue: true,
  disabled: true,
  lang: true,
  maxLength: true,
  onChange: true,
  onScroll: true,
  placeholder: true,
  pointerEvents: true,
  readOnly: true,
  rows: true,
  spellCheck: true,
  value: true,
  type: true
});
var pickProps = function pickProps(props) {
  return (0, _pick.default)(props, forwardPropsList);
};
function isEventComposing(nativeEvent) {
  return nativeEvent.isComposing || nativeEvent.keyCode === 229;
}
var focusTimeout = null;
var TextInput = React.forwardRef(function (props, forwardedRef) {
  var _props$autoCapitalize = props.autoCapitalize,
    autoCapitalize = _props$autoCapitalize === void 0 ? 'sentences' : _props$autoCapitalize,
    autoComplete = props.autoComplete,
    autoCompleteType = props.autoCompleteType,
    _props$autoCorrect = props.autoCorrect,
    autoCorrect = _props$autoCorrect === void 0 ? true : _props$autoCorrect,
    blurOnSubmit = props.blurOnSubmit,
    caretHidden = props.caretHidden,
    clearTextOnFocus = props.clearTextOnFocus,
    dir = props.dir,
    editable = props.editable,
    enterKeyHint = props.enterKeyHint,
    inputMode = props.inputMode,
    keyboardType = props.keyboardType,
    _props$multiline = props.multiline,
    multiline = _props$multiline === void 0 ? false : _props$multiline,
    numberOfLines = props.numberOfLines,
    onBlur = props.onBlur,
    onChange = props.onChange,
    onChangeText = props.onChangeText,
    onContentSizeChange = props.onContentSizeChange,
    onFocus = props.onFocus,
    onKeyPress = props.onKeyPress,
    onLayout = props.onLayout,
    onMoveShouldSetResponder = props.onMoveShouldSetResponder,
    onMoveShouldSetResponderCapture = props.onMoveShouldSetResponderCapture,
    onResponderEnd = props.onResponderEnd,
    onResponderGrant = props.onResponderGrant,
    onResponderMove = props.onResponderMove,
    onResponderReject = props.onResponderReject,
    onResponderRelease = props.onResponderRelease,
    onResponderStart = props.onResponderStart,
    onResponderTerminate = props.onResponderTerminate,
    onResponderTerminationRequest = props.onResponderTerminationRequest,
    onScrollShouldSetResponder = props.onScrollShouldSetResponder,
    onScrollShouldSetResponderCapture = props.onScrollShouldSetResponderCapture,
    onSelectionChange = props.onSelectionChange,
    onSelectionChangeShouldSetResponder = props.onSelectionChangeShouldSetResponder,
    onSelectionChangeShouldSetResponderCapture = props.onSelectionChangeShouldSetResponderCapture,
    onStartShouldSetResponder = props.onStartShouldSetResponder,
    onStartShouldSetResponderCapture = props.onStartShouldSetResponderCapture,
    onSubmitEditing = props.onSubmitEditing,
    placeholderTextColor = props.placeholderTextColor,
    _props$readOnly = props.readOnly,
    readOnly = _props$readOnly === void 0 ? false : _props$readOnly,
    returnKeyType = props.returnKeyType,
    rows = props.rows,
    _props$secureTextEntr = props.secureTextEntry,
    secureTextEntry = _props$secureTextEntr === void 0 ? false : _props$secureTextEntr,
    selection = props.selection,
    selectTextOnFocus = props.selectTextOnFocus,
    showSoftInputOnFocus = props.showSoftInputOnFocus,
    spellCheck = props.spellCheck;
  var type;
  var _inputMode;
  if (inputMode != null) {
    _inputMode = inputMode;
    if (inputMode === 'email') {
      type = 'email';
    } else if (inputMode === 'tel') {
      type = 'tel';
    } else if (inputMode === 'search') {
      type = 'search';
    } else if (inputMode === 'url') {
      type = 'url';
    } else {
      type = 'text';
    }
  } else if (keyboardType != null) {
    switch (keyboardType) {
      case 'email-address':
        type = 'email';
        break;
      case 'number-pad':
      case 'numeric':
        _inputMode = 'numeric';
        break;
      case 'decimal-pad':
        _inputMode = 'decimal';
        break;
      case 'phone-pad':
        type = 'tel';
        break;
      case 'search':
      case 'web-search':
        type = 'search';
        break;
      case 'url':
        type = 'url';
        break;
      default:
        type = 'text';
    }
  }
  if (secureTextEntry) {
    type = 'password';
  }
  var dimensions = React.useRef({
    height: null,
    width: null
  });
  var hostRef = React.useRef(null);
  var prevSelection = React.useRef(null);
  var prevSecureTextEntry = React.useRef(false);
  React.useEffect(function () {
    if (hostRef.current && prevSelection.current) {
      setSelection(hostRef.current, prevSelection.current);
    }
    prevSecureTextEntry.current = secureTextEntry;
  }, [secureTextEntry]);
  var handleContentSizeChange = React.useCallback(function (hostNode) {
    if (multiline && onContentSizeChange && hostNode != null) {
      var newHeight = hostNode.scrollHeight;
      var newWidth = hostNode.scrollWidth;
      if (newHeight !== dimensions.current.height || newWidth !== dimensions.current.width) {
        dimensions.current.height = newHeight;
        dimensions.current.width = newWidth;
        onContentSizeChange({
          nativeEvent: {
            contentSize: {
              height: dimensions.current.height,
              width: dimensions.current.width
            }
          }
        });
      }
    }
  }, [multiline, onContentSizeChange]);
  var imperativeRef = React.useMemo(function () {
    return function (hostNode) {
      if (hostNode != null) {
        hostNode.clear = function () {
          if (hostNode != null) {
            hostNode.value = '';
          }
        };
        hostNode.isFocused = function () {
          return hostNode != null && _TextInputState.default.currentlyFocusedField() === hostNode;
        };
        handleContentSizeChange(hostNode);
      }
    };
  }, [handleContentSizeChange]);
  function handleBlur(e) {
    _TextInputState.default._currentlyFocusedNode = null;
    if (onBlur) {
      e.nativeEvent.text = e.target.value;
      onBlur(e);
    }
  }
  function handleChange(e) {
    var hostNode = e.target;
    var text = hostNode.value;
    e.nativeEvent.text = text;
    handleContentSizeChange(hostNode);
    if (onChange) {
      onChange(e);
    }
    if (onChangeText) {
      onChangeText(text);
    }
  }
  function handleFocus(e) {
    var hostNode = e.target;
    if (onFocus) {
      e.nativeEvent.text = hostNode.value;
      onFocus(e);
    }
    if (hostNode != null) {
      _TextInputState.default._currentlyFocusedNode = hostNode;
      if (clearTextOnFocus) {
        hostNode.value = '';
      }
      if (selectTextOnFocus) {
        if (focusTimeout != null) {
          clearTimeout(focusTimeout);
        }
        focusTimeout = setTimeout(function () {
          if (hostNode != null && document.activeElement === hostNode) {
            hostNode.select();
          }
        }, 0);
      }
    }
  }
  function handleKeyDown(e) {
    var hostNode = e.target;
    e.stopPropagation();
    var blurOnSubmitDefault = !multiline;
    var shouldBlurOnSubmit = blurOnSubmit == null ? blurOnSubmitDefault : blurOnSubmit;
    var nativeEvent = e.nativeEvent;
    var isComposing = isEventComposing(nativeEvent);
    if (onKeyPress) {
      onKeyPress(e);
    }
    if (e.key === 'Enter' && !e.shiftKey && !isComposing && !e.isDefaultPrevented()) {
      if ((blurOnSubmit || !multiline) && onSubmitEditing) {
        e.preventDefault();
        nativeEvent.text = e.target.value;
        onSubmitEditing(e);
      }
      if (shouldBlurOnSubmit && hostNode != null) {
        setTimeout(function () {
          return hostNode.blur();
        }, 0);
      }
    }
  }
  function handleSelectionChange(e) {
    try {
      var _e$target = e.target,
        selectionStart = _e$target.selectionStart,
        selectionEnd = _e$target.selectionEnd;
      var _selection = {
        start: selectionStart,
        end: selectionEnd
      };
      if (onSelectionChange) {
        e.nativeEvent.selection = _selection;
        e.nativeEvent.text = e.target.value;
        onSelectionChange(e);
      }
      if (prevSecureTextEntry.current === secureTextEntry) {
        prevSelection.current = _selection;
      }
    } catch (e) {}
  }
  (0, _useLayoutEffect.default)(function () {
    var node = hostRef.current;
    if (node != null && selection != null) {
      setSelection(node, selection);
    }
    if (document.activeElement === node) {
      _TextInputState.default._currentlyFocusedNode = node;
    }
  }, [hostRef, selection]);
  var component = multiline ? 'textarea' : 'input';
  (0, _useElementLayout.default)(hostRef, onLayout);
  (0, _useResponderEvents.default)(hostRef, {
    onMoveShouldSetResponder: onMoveShouldSetResponder,
    onMoveShouldSetResponderCapture: onMoveShouldSetResponderCapture,
    onResponderEnd: onResponderEnd,
    onResponderGrant: onResponderGrant,
    onResponderMove: onResponderMove,
    onResponderReject: onResponderReject,
    onResponderRelease: onResponderRelease,
    onResponderStart: onResponderStart,
    onResponderTerminate: onResponderTerminate,
    onResponderTerminationRequest: onResponderTerminationRequest,
    onScrollShouldSetResponder: onScrollShouldSetResponder,
    onScrollShouldSetResponderCapture: onScrollShouldSetResponderCapture,
    onSelectionChangeShouldSetResponder: onSelectionChangeShouldSetResponder,
    onSelectionChangeShouldSetResponderCapture: onSelectionChangeShouldSetResponderCapture,
    onStartShouldSetResponder: onStartShouldSetResponder,
    onStartShouldSetResponderCapture: onStartShouldSetResponderCapture
  });
  var _useLocaleContext = (0, _useLocale.useLocaleContext)(),
    contextDirection = _useLocaleContext.direction;
  var supportedProps = pickProps(props);
  supportedProps.autoCapitalize = autoCapitalize;
  supportedProps.autoComplete = autoComplete || autoCompleteType || 'on';
  supportedProps.autoCorrect = autoCorrect ? 'on' : 'off';
  supportedProps.dir = dir !== undefined ? dir : 'auto';
  supportedProps.enterKeyHint = enterKeyHint || returnKeyType;
  supportedProps.inputMode = _inputMode;
  supportedProps.onBlur = handleBlur;
  supportedProps.onChange = handleChange;
  supportedProps.onFocus = handleFocus;
  supportedProps.onKeyDown = handleKeyDown;
  supportedProps.onSelect = handleSelectionChange;
  supportedProps.readOnly = readOnly === true || editable === false;
  supportedProps.rows = multiline ? rows != null ? rows : numberOfLines : 1;
  supportedProps.spellCheck = spellCheck != null ? spellCheck : autoCorrect;
  supportedProps.style = [{
    '--placeholderTextColor': placeholderTextColor
  }, styles.textinput$raw, styles.placeholder, props.style, caretHidden && styles.caretHidden];
  supportedProps.type = multiline ? undefined : type;
  supportedProps.virtualkeyboardpolicy = showSoftInputOnFocus === false ? 'manual' : 'auto';
  var platformMethodsRef = (0, _usePlatformMethods.default)(supportedProps);
  var setRef = (0, _useMergeRefs.default)(hostRef, platformMethodsRef, imperativeRef, forwardedRef);
  supportedProps.ref = setRef;
  var langDirection = props.lang != null ? (0, _useLocale.getLocaleDirection)(props.lang) : null;
  var componentDirection = props.dir || langDirection;
  var writingDirection = componentDirection || contextDirection;
  var element = (0, _createElement.default)(component, supportedProps, {
    writingDirection: writingDirection
  });
  return element;
});
TextInput.displayName = 'TextInput';
TextInput.State = _TextInputState.default;
var styles = _StyleSheet.default.create({
  textinput$raw: {
    MozAppearance: 'textfield',
    WebkitAppearance: 'none',
    backgroundColor: 'transparent',
    border: '0 solid black',
    borderRadius: 0,
    boxSizing: 'border-box',
    font: '14px System',
    margin: 0,
    padding: 0,
    resize: 'none'
  },
  placeholder: {
    placeholderTextColor: 'var(--placeholderTextColor)'
  },
  caretHidden: {
    caretColor: 'transparent'
  }
});
var _default = exports.default = TextInput;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCIsImV4cG9ydHMiLCJfX2VzTW9kdWxlIiwiUmVhY3QiLCJfY3JlYXRlRWxlbWVudCIsImZvcndhcmRlZFByb3BzIiwiX3BpY2siLCJfdXNlRWxlbWVudExheW91dCIsIl91c2VMYXlvdXRFZmZlY3QiLCJfdXNlTWVyZ2VSZWZzIiwiX3VzZVBsYXRmb3JtTWV0aG9kcyIsIl91c2VSZXNwb25kZXJFdmVudHMiLCJfdXNlTG9jYWxlIiwiX1N0eWxlU2hlZXQiLCJfVGV4dElucHV0U3RhdGUiLCJpc1NlbGVjdGlvblN0YWxlIiwibm9kZSIsInNlbGVjdGlvbiIsInNlbGVjdGlvbkVuZCIsInNlbGVjdGlvblN0YXJ0Iiwic3RhcnQiLCJlbmQiLCJzZXRTZWxlY3Rpb24iLCJzZXRTZWxlY3Rpb25SYW5nZSIsImUiLCJmb3J3YXJkUHJvcHNMaXN0IiwiT2JqZWN0IiwiYXNzaWduIiwiZGVmYXVsdFByb3BzIiwiYWNjZXNzaWJpbGl0eVByb3BzIiwiY2xpY2tQcm9wcyIsImZvY3VzUHJvcHMiLCJrZXlib2FyZFByb3BzIiwibW91c2VQcm9wcyIsInRvdWNoUHJvcHMiLCJzdHlsZVByb3BzIiwiYXV0b0NhcGl0YWxpemUiLCJhdXRvQ29tcGxldGUiLCJhdXRvQ29ycmVjdCIsImF1dG9Gb2N1cyIsImRlZmF1bHRWYWx1ZSIsImRpc2FibGVkIiwibGFuZyIsIm1heExlbmd0aCIsIm9uQ2hhbmdlIiwib25TY3JvbGwiLCJwbGFjZWhvbGRlciIsInBvaW50ZXJFdmVudHMiLCJyZWFkT25seSIsInJvd3MiLCJzcGVsbENoZWNrIiwidmFsdWUiLCJ0eXBlIiwicGlja1Byb3BzIiwicHJvcHMiLCJpc0V2ZW50Q29tcG9zaW5nIiwibmF0aXZlRXZlbnQiLCJpc0NvbXBvc2luZyIsImtleUNvZGUiLCJmb2N1c1RpbWVvdXQiLCJUZXh0SW5wdXQiLCJmb3J3YXJkUmVmIiwiZm9yd2FyZGVkUmVmIiwiX3Byb3BzJGF1dG9DYXBpdGFsaXplIiwiYXV0b0NvbXBsZXRlVHlwZSIsIl9wcm9wcyRhdXRvQ29ycmVjdCIsImJsdXJPblN1Ym1pdCIsImNhcmV0SGlkZGVuIiwiY2xlYXJUZXh0T25Gb2N1cyIsImRpciIsImVkaXRhYmxlIiwiZW50ZXJLZXlIaW50IiwiaW5wdXRNb2RlIiwia2V5Ym9hcmRUeXBlIiwiX3Byb3BzJG11bHRpbGluZSIsIm11bHRpbGluZSIsIm51bWJlck9mTGluZXMiLCJvbkJsdXIiLCJvbkNoYW5nZVRleHQiLCJvbkNvbnRlbnRTaXplQ2hhbmdlIiwib25Gb2N1cyIsIm9uS2V5UHJlc3MiLCJvbkxheW91dCIsIm9uTW92ZVNob3VsZFNldFJlc3BvbmRlciIsIm9uTW92ZVNob3VsZFNldFJlc3BvbmRlckNhcHR1cmUiLCJvblJlc3BvbmRlckVuZCIsIm9uUmVzcG9uZGVyR3JhbnQiLCJvblJlc3BvbmRlck1vdmUiLCJvblJlc3BvbmRlclJlamVjdCIsIm9uUmVzcG9uZGVyUmVsZWFzZSIsIm9uUmVzcG9uZGVyU3RhcnQiLCJvblJlc3BvbmRlclRlcm1pbmF0ZSIsIm9uUmVzcG9uZGVyVGVybWluYXRpb25SZXF1ZXN0Iiwib25TY3JvbGxTaG91bGRTZXRSZXNwb25kZXIiLCJvblNjcm9sbFNob3VsZFNldFJlc3BvbmRlckNhcHR1cmUiLCJvblNlbGVjdGlvbkNoYW5nZSIsIm9uU2VsZWN0aW9uQ2hhbmdlU2hvdWxkU2V0UmVzcG9uZGVyIiwib25TZWxlY3Rpb25DaGFuZ2VTaG91bGRTZXRSZXNwb25kZXJDYXB0dXJlIiwib25TdGFydFNob3VsZFNldFJlc3BvbmRlciIsIm9uU3RhcnRTaG91bGRTZXRSZXNwb25kZXJDYXB0dXJlIiwib25TdWJtaXRFZGl0aW5nIiwicGxhY2Vob2xkZXJUZXh0Q29sb3IiLCJfcHJvcHMkcmVhZE9ubHkiLCJyZXR1cm5LZXlUeXBlIiwiX3Byb3BzJHNlY3VyZVRleHRFbnRyIiwic2VjdXJlVGV4dEVudHJ5Iiwic2VsZWN0VGV4dE9uRm9jdXMiLCJzaG93U29mdElucHV0T25Gb2N1cyIsIl9pbnB1dE1vZGUiLCJkaW1lbnNpb25zIiwidXNlUmVmIiwiaGVpZ2h0Iiwid2lkdGgiLCJob3N0UmVmIiwicHJldlNlbGVjdGlvbiIsInByZXZTZWN1cmVUZXh0RW50cnkiLCJ1c2VFZmZlY3QiLCJjdXJyZW50IiwiaGFuZGxlQ29udGVudFNpemVDaGFuZ2UiLCJ1c2VDYWxsYmFjayIsImhvc3ROb2RlIiwibmV3SGVpZ2h0Iiwic2Nyb2xsSGVpZ2h0IiwibmV3V2lkdGgiLCJzY3JvbGxXaWR0aCIsImNvbnRlbnRTaXplIiwiaW1wZXJhdGl2ZVJlZiIsInVzZU1lbW8iLCJjbGVhciIsImlzRm9jdXNlZCIsImN1cnJlbnRseUZvY3VzZWRGaWVsZCIsImhhbmRsZUJsdXIiLCJfY3VycmVudGx5Rm9jdXNlZE5vZGUiLCJ0ZXh0IiwidGFyZ2V0IiwiaGFuZGxlQ2hhbmdlIiwiaGFuZGxlRm9jdXMiLCJjbGVhclRpbWVvdXQiLCJzZXRUaW1lb3V0IiwiZG9jdW1lbnQiLCJhY3RpdmVFbGVtZW50Iiwic2VsZWN0IiwiaGFuZGxlS2V5RG93biIsInN0b3BQcm9wYWdhdGlvbiIsImJsdXJPblN1Ym1pdERlZmF1bHQiLCJzaG91bGRCbHVyT25TdWJtaXQiLCJrZXkiLCJzaGlmdEtleSIsImlzRGVmYXVsdFByZXZlbnRlZCIsInByZXZlbnREZWZhdWx0IiwiYmx1ciIsImhhbmRsZVNlbGVjdGlvbkNoYW5nZSIsIl9lJHRhcmdldCIsIl9zZWxlY3Rpb24iLCJjb21wb25lbnQiLCJfdXNlTG9jYWxlQ29udGV4dCIsInVzZUxvY2FsZUNvbnRleHQiLCJjb250ZXh0RGlyZWN0aW9uIiwiZGlyZWN0aW9uIiwic3VwcG9ydGVkUHJvcHMiLCJ1bmRlZmluZWQiLCJvbktleURvd24iLCJvblNlbGVjdCIsInN0eWxlIiwic3R5bGVzIiwidGV4dGlucHV0JHJhdyIsInZpcnR1YWxrZXlib2FyZHBvbGljeSIsInBsYXRmb3JtTWV0aG9kc1JlZiIsInNldFJlZiIsInJlZiIsImxhbmdEaXJlY3Rpb24iLCJnZXRMb2NhbGVEaXJlY3Rpb24iLCJjb21wb25lbnREaXJlY3Rpb24iLCJ3cml0aW5nRGlyZWN0aW9uIiwiZWxlbWVudCIsImRpc3BsYXlOYW1lIiwiU3RhdGUiLCJjcmVhdGUiLCJNb3pBcHBlYXJhbmNlIiwiV2Via2l0QXBwZWFyYW5jZSIsImJhY2tncm91bmRDb2xvciIsImJvcmRlciIsImJvcmRlclJhZGl1cyIsImJveFNpemluZyIsImZvbnQiLCJtYXJnaW4iLCJwYWRkaW5nIiwicmVzaXplIiwiY2FyZXRDb2xvciIsIl9kZWZhdWx0IiwibW9kdWxlIl0sInNvdXJjZXMiOlsiaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG4vKipcbiAqIENvcHlyaWdodCAoYykgTmljb2xhcyBHYWxsYWdoZXIuXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIFxuICovXG5cbid1c2UgY2xpZW50JztcblxudmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIikuZGVmYXVsdDtcbnZhciBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZCA9IHJlcXVpcmUoXCJAYmFiZWwvcnVudGltZS9oZWxwZXJzL2ludGVyb3BSZXF1aXJlV2lsZGNhcmRcIikuZGVmYXVsdDtcbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG52YXIgUmVhY3QgPSBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKFwicmVhY3RcIikpO1xudmFyIF9jcmVhdGVFbGVtZW50ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vY3JlYXRlRWxlbWVudFwiKSk7XG52YXIgZm9yd2FyZGVkUHJvcHMgPSBfaW50ZXJvcFJlcXVpcmVXaWxkY2FyZChyZXF1aXJlKFwiLi4vLi4vbW9kdWxlcy9mb3J3YXJkZWRQcm9wc1wiKSk7XG52YXIgX3BpY2sgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi8uLi9tb2R1bGVzL3BpY2tcIikpO1xudmFyIF91c2VFbGVtZW50TGF5b3V0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vLi4vbW9kdWxlcy91c2VFbGVtZW50TGF5b3V0XCIpKTtcbnZhciBfdXNlTGF5b3V0RWZmZWN0ID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vLi4vbW9kdWxlcy91c2VMYXlvdXRFZmZlY3RcIikpO1xudmFyIF91c2VNZXJnZVJlZnMgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi8uLi9tb2R1bGVzL3VzZU1lcmdlUmVmc1wiKSk7XG52YXIgX3VzZVBsYXRmb3JtTWV0aG9kcyA9IF9pbnRlcm9wUmVxdWlyZURlZmF1bHQocmVxdWlyZShcIi4uLy4uL21vZHVsZXMvdXNlUGxhdGZvcm1NZXRob2RzXCIpKTtcbnZhciBfdXNlUmVzcG9uZGVyRXZlbnRzID0gX2ludGVyb3BSZXF1aXJlRGVmYXVsdChyZXF1aXJlKFwiLi4vLi4vbW9kdWxlcy91c2VSZXNwb25kZXJFdmVudHNcIikpO1xudmFyIF91c2VMb2NhbGUgPSByZXF1aXJlKFwiLi4vLi4vbW9kdWxlcy91c2VMb2NhbGVcIik7XG52YXIgX1N0eWxlU2hlZXQgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi9TdHlsZVNoZWV0XCIpKTtcbnZhciBfVGV4dElucHV0U3RhdGUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuLi8uLi9tb2R1bGVzL1RleHRJbnB1dFN0YXRlXCIpKTtcbi8vaW1wb3J0IHsgd2Fybk9uY2UgfSBmcm9tICcuLi8uLi9tb2R1bGVzL3dhcm5PbmNlJztcblxuLyoqXG4gKiBEZXRlcm1pbmVzIHdoZXRoZXIgYSAnc2VsZWN0aW9uJyBwcm9wIGRpZmZlcnMgZnJvbSBhIG5vZGUncyBleGlzdGluZ1xuICogc2VsZWN0aW9uIHN0YXRlLlxuICovXG52YXIgaXNTZWxlY3Rpb25TdGFsZSA9IChub2RlLCBzZWxlY3Rpb24pID0+IHtcbiAgdmFyIHNlbGVjdGlvbkVuZCA9IG5vZGUuc2VsZWN0aW9uRW5kLFxuICAgIHNlbGVjdGlvblN0YXJ0ID0gbm9kZS5zZWxlY3Rpb25TdGFydDtcbiAgdmFyIHN0YXJ0ID0gc2VsZWN0aW9uLnN0YXJ0LFxuICAgIGVuZCA9IHNlbGVjdGlvbi5lbmQ7XG4gIHJldHVybiBzdGFydCAhPT0gc2VsZWN0aW9uU3RhcnQgfHwgZW5kICE9PSBzZWxlY3Rpb25FbmQ7XG59O1xuXG4vKipcbiAqIENlcnRhaW4gaW5wdXQgdHlwZXMgZG8gbm8gc3VwcG9ydCAnc2VsZWN0U2VsZWN0aW9uUmFuZ2UnIGFuZCB3aWxsIHRocm93IGFuXG4gKiBlcnJvci5cbiAqL1xudmFyIHNldFNlbGVjdGlvbiA9IChub2RlLCBzZWxlY3Rpb24pID0+IHtcbiAgaWYgKGlzU2VsZWN0aW9uU3RhbGUobm9kZSwgc2VsZWN0aW9uKSkge1xuICAgIHZhciBzdGFydCA9IHNlbGVjdGlvbi5zdGFydCxcbiAgICAgIGVuZCA9IHNlbGVjdGlvbi5lbmQ7XG4gICAgdHJ5IHtcbiAgICAgIG5vZGUuc2V0U2VsZWN0aW9uUmFuZ2Uoc3RhcnQsIGVuZCB8fCBzdGFydCk7XG4gICAgfSBjYXRjaCAoZSkge31cbiAgfVxufTtcbnZhciBmb3J3YXJkUHJvcHNMaXN0ID0gT2JqZWN0LmFzc2lnbih7fSwgZm9yd2FyZGVkUHJvcHMuZGVmYXVsdFByb3BzLCBmb3J3YXJkZWRQcm9wcy5hY2Nlc3NpYmlsaXR5UHJvcHMsIGZvcndhcmRlZFByb3BzLmNsaWNrUHJvcHMsIGZvcndhcmRlZFByb3BzLmZvY3VzUHJvcHMsIGZvcndhcmRlZFByb3BzLmtleWJvYXJkUHJvcHMsIGZvcndhcmRlZFByb3BzLm1vdXNlUHJvcHMsIGZvcndhcmRlZFByb3BzLnRvdWNoUHJvcHMsIGZvcndhcmRlZFByb3BzLnN0eWxlUHJvcHMsIHtcbiAgYXV0b0NhcGl0YWxpemU6IHRydWUsXG4gIGF1dG9Db21wbGV0ZTogdHJ1ZSxcbiAgYXV0b0NvcnJlY3Q6IHRydWUsXG4gIGF1dG9Gb2N1czogdHJ1ZSxcbiAgZGVmYXVsdFZhbHVlOiB0cnVlLFxuICBkaXNhYmxlZDogdHJ1ZSxcbiAgbGFuZzogdHJ1ZSxcbiAgbWF4TGVuZ3RoOiB0cnVlLFxuICBvbkNoYW5nZTogdHJ1ZSxcbiAgb25TY3JvbGw6IHRydWUsXG4gIHBsYWNlaG9sZGVyOiB0cnVlLFxuICBwb2ludGVyRXZlbnRzOiB0cnVlLFxuICByZWFkT25seTogdHJ1ZSxcbiAgcm93czogdHJ1ZSxcbiAgc3BlbGxDaGVjazogdHJ1ZSxcbiAgdmFsdWU6IHRydWUsXG4gIHR5cGU6IHRydWVcbn0pO1xudmFyIHBpY2tQcm9wcyA9IHByb3BzID0+ICgwLCBfcGljay5kZWZhdWx0KShwcm9wcywgZm9yd2FyZFByb3BzTGlzdCk7XG5cbi8vIElmIGFuIElucHV0IE1ldGhvZCBFZGl0b3IgaXMgcHJvY2Vzc2luZyBrZXkgaW5wdXQsIHRoZSAna2V5Q29kZScgaXMgMjI5LlxuLy8gaHR0cHM6Ly93d3cudzMub3JnL1RSL3VpZXZlbnRzLyNkZXRlcm1pbmUta2V5ZG93bi1rZXl1cC1rZXlDb2RlXG5mdW5jdGlvbiBpc0V2ZW50Q29tcG9zaW5nKG5hdGl2ZUV2ZW50KSB7XG4gIHJldHVybiBuYXRpdmVFdmVudC5pc0NvbXBvc2luZyB8fCBuYXRpdmVFdmVudC5rZXlDb2RlID09PSAyMjk7XG59XG52YXIgZm9jdXNUaW1lb3V0ID0gbnVsbDtcbnZhciBUZXh0SW5wdXQgPSAvKiNfX1BVUkVfXyovUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICB2YXIgX3Byb3BzJGF1dG9DYXBpdGFsaXplID0gcHJvcHMuYXV0b0NhcGl0YWxpemUsXG4gICAgYXV0b0NhcGl0YWxpemUgPSBfcHJvcHMkYXV0b0NhcGl0YWxpemUgPT09IHZvaWQgMCA/ICdzZW50ZW5jZXMnIDogX3Byb3BzJGF1dG9DYXBpdGFsaXplLFxuICAgIGF1dG9Db21wbGV0ZSA9IHByb3BzLmF1dG9Db21wbGV0ZSxcbiAgICBhdXRvQ29tcGxldGVUeXBlID0gcHJvcHMuYXV0b0NvbXBsZXRlVHlwZSxcbiAgICBfcHJvcHMkYXV0b0NvcnJlY3QgPSBwcm9wcy5hdXRvQ29ycmVjdCxcbiAgICBhdXRvQ29ycmVjdCA9IF9wcm9wcyRhdXRvQ29ycmVjdCA9PT0gdm9pZCAwID8gdHJ1ZSA6IF9wcm9wcyRhdXRvQ29ycmVjdCxcbiAgICBibHVyT25TdWJtaXQgPSBwcm9wcy5ibHVyT25TdWJtaXQsXG4gICAgY2FyZXRIaWRkZW4gPSBwcm9wcy5jYXJldEhpZGRlbixcbiAgICBjbGVhclRleHRPbkZvY3VzID0gcHJvcHMuY2xlYXJUZXh0T25Gb2N1cyxcbiAgICBkaXIgPSBwcm9wcy5kaXIsXG4gICAgZWRpdGFibGUgPSBwcm9wcy5lZGl0YWJsZSxcbiAgICBlbnRlcktleUhpbnQgPSBwcm9wcy5lbnRlcktleUhpbnQsXG4gICAgaW5wdXRNb2RlID0gcHJvcHMuaW5wdXRNb2RlLFxuICAgIGtleWJvYXJkVHlwZSA9IHByb3BzLmtleWJvYXJkVHlwZSxcbiAgICBfcHJvcHMkbXVsdGlsaW5lID0gcHJvcHMubXVsdGlsaW5lLFxuICAgIG11bHRpbGluZSA9IF9wcm9wcyRtdWx0aWxpbmUgPT09IHZvaWQgMCA/IGZhbHNlIDogX3Byb3BzJG11bHRpbGluZSxcbiAgICBudW1iZXJPZkxpbmVzID0gcHJvcHMubnVtYmVyT2ZMaW5lcyxcbiAgICBvbkJsdXIgPSBwcm9wcy5vbkJsdXIsXG4gICAgb25DaGFuZ2UgPSBwcm9wcy5vbkNoYW5nZSxcbiAgICBvbkNoYW5nZVRleHQgPSBwcm9wcy5vbkNoYW5nZVRleHQsXG4gICAgb25Db250ZW50U2l6ZUNoYW5nZSA9IHByb3BzLm9uQ29udGVudFNpemVDaGFuZ2UsXG4gICAgb25Gb2N1cyA9IHByb3BzLm9uRm9jdXMsXG4gICAgb25LZXlQcmVzcyA9IHByb3BzLm9uS2V5UHJlc3MsXG4gICAgb25MYXlvdXQgPSBwcm9wcy5vbkxheW91dCxcbiAgICBvbk1vdmVTaG91bGRTZXRSZXNwb25kZXIgPSBwcm9wcy5vbk1vdmVTaG91bGRTZXRSZXNwb25kZXIsXG4gICAgb25Nb3ZlU2hvdWxkU2V0UmVzcG9uZGVyQ2FwdHVyZSA9IHByb3BzLm9uTW92ZVNob3VsZFNldFJlc3BvbmRlckNhcHR1cmUsXG4gICAgb25SZXNwb25kZXJFbmQgPSBwcm9wcy5vblJlc3BvbmRlckVuZCxcbiAgICBvblJlc3BvbmRlckdyYW50ID0gcHJvcHMub25SZXNwb25kZXJHcmFudCxcbiAgICBvblJlc3BvbmRlck1vdmUgPSBwcm9wcy5vblJlc3BvbmRlck1vdmUsXG4gICAgb25SZXNwb25kZXJSZWplY3QgPSBwcm9wcy5vblJlc3BvbmRlclJlamVjdCxcbiAgICBvblJlc3BvbmRlclJlbGVhc2UgPSBwcm9wcy5vblJlc3BvbmRlclJlbGVhc2UsXG4gICAgb25SZXNwb25kZXJTdGFydCA9IHByb3BzLm9uUmVzcG9uZGVyU3RhcnQsXG4gICAgb25SZXNwb25kZXJUZXJtaW5hdGUgPSBwcm9wcy5vblJlc3BvbmRlclRlcm1pbmF0ZSxcbiAgICBvblJlc3BvbmRlclRlcm1pbmF0aW9uUmVxdWVzdCA9IHByb3BzLm9uUmVzcG9uZGVyVGVybWluYXRpb25SZXF1ZXN0LFxuICAgIG9uU2Nyb2xsU2hvdWxkU2V0UmVzcG9uZGVyID0gcHJvcHMub25TY3JvbGxTaG91bGRTZXRSZXNwb25kZXIsXG4gICAgb25TY3JvbGxTaG91bGRTZXRSZXNwb25kZXJDYXB0dXJlID0gcHJvcHMub25TY3JvbGxTaG91bGRTZXRSZXNwb25kZXJDYXB0dXJlLFxuICAgIG9uU2VsZWN0aW9uQ2hhbmdlID0gcHJvcHMub25TZWxlY3Rpb25DaGFuZ2UsXG4gICAgb25TZWxlY3Rpb25DaGFuZ2VTaG91bGRTZXRSZXNwb25kZXIgPSBwcm9wcy5vblNlbGVjdGlvbkNoYW5nZVNob3VsZFNldFJlc3BvbmRlcixcbiAgICBvblNlbGVjdGlvbkNoYW5nZVNob3VsZFNldFJlc3BvbmRlckNhcHR1cmUgPSBwcm9wcy5vblNlbGVjdGlvbkNoYW5nZVNob3VsZFNldFJlc3BvbmRlckNhcHR1cmUsXG4gICAgb25TdGFydFNob3VsZFNldFJlc3BvbmRlciA9IHByb3BzLm9uU3RhcnRTaG91bGRTZXRSZXNwb25kZXIsXG4gICAgb25TdGFydFNob3VsZFNldFJlc3BvbmRlckNhcHR1cmUgPSBwcm9wcy5vblN0YXJ0U2hvdWxkU2V0UmVzcG9uZGVyQ2FwdHVyZSxcbiAgICBvblN1Ym1pdEVkaXRpbmcgPSBwcm9wcy5vblN1Ym1pdEVkaXRpbmcsXG4gICAgcGxhY2Vob2xkZXJUZXh0Q29sb3IgPSBwcm9wcy5wbGFjZWhvbGRlclRleHRDb2xvcixcbiAgICBfcHJvcHMkcmVhZE9ubHkgPSBwcm9wcy5yZWFkT25seSxcbiAgICByZWFkT25seSA9IF9wcm9wcyRyZWFkT25seSA9PT0gdm9pZCAwID8gZmFsc2UgOiBfcHJvcHMkcmVhZE9ubHksXG4gICAgcmV0dXJuS2V5VHlwZSA9IHByb3BzLnJldHVybktleVR5cGUsXG4gICAgcm93cyA9IHByb3BzLnJvd3MsXG4gICAgX3Byb3BzJHNlY3VyZVRleHRFbnRyID0gcHJvcHMuc2VjdXJlVGV4dEVudHJ5LFxuICAgIHNlY3VyZVRleHRFbnRyeSA9IF9wcm9wcyRzZWN1cmVUZXh0RW50ciA9PT0gdm9pZCAwID8gZmFsc2UgOiBfcHJvcHMkc2VjdXJlVGV4dEVudHIsXG4gICAgc2VsZWN0aW9uID0gcHJvcHMuc2VsZWN0aW9uLFxuICAgIHNlbGVjdFRleHRPbkZvY3VzID0gcHJvcHMuc2VsZWN0VGV4dE9uRm9jdXMsXG4gICAgc2hvd1NvZnRJbnB1dE9uRm9jdXMgPSBwcm9wcy5zaG93U29mdElucHV0T25Gb2N1cyxcbiAgICBzcGVsbENoZWNrID0gcHJvcHMuc3BlbGxDaGVjaztcbiAgdmFyIHR5cGU7XG4gIHZhciBfaW5wdXRNb2RlO1xuICBpZiAoaW5wdXRNb2RlICE9IG51bGwpIHtcbiAgICBfaW5wdXRNb2RlID0gaW5wdXRNb2RlO1xuICAgIGlmIChpbnB1dE1vZGUgPT09ICdlbWFpbCcpIHtcbiAgICAgIHR5cGUgPSAnZW1haWwnO1xuICAgIH0gZWxzZSBpZiAoaW5wdXRNb2RlID09PSAndGVsJykge1xuICAgICAgdHlwZSA9ICd0ZWwnO1xuICAgIH0gZWxzZSBpZiAoaW5wdXRNb2RlID09PSAnc2VhcmNoJykge1xuICAgICAgdHlwZSA9ICdzZWFyY2gnO1xuICAgIH0gZWxzZSBpZiAoaW5wdXRNb2RlID09PSAndXJsJykge1xuICAgICAgdHlwZSA9ICd1cmwnO1xuICAgIH0gZWxzZSB7XG4gICAgICB0eXBlID0gJ3RleHQnO1xuICAgIH1cbiAgfSBlbHNlIGlmIChrZXlib2FyZFR5cGUgIT0gbnVsbCkge1xuICAgIC8vIHdhcm5PbmNlKCdrZXlib2FyZFR5cGUnLCAna2V5Ym9hcmRUeXBlIGlzIGRlcHJlY2F0ZWQuIFVzZSBpbnB1dE1vZGUuJyk7XG4gICAgc3dpdGNoIChrZXlib2FyZFR5cGUpIHtcbiAgICAgIGNhc2UgJ2VtYWlsLWFkZHJlc3MnOlxuICAgICAgICB0eXBlID0gJ2VtYWlsJztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdudW1iZXItcGFkJzpcbiAgICAgIGNhc2UgJ251bWVyaWMnOlxuICAgICAgICBfaW5wdXRNb2RlID0gJ251bWVyaWMnO1xuICAgICAgICBicmVhaztcbiAgICAgIGNhc2UgJ2RlY2ltYWwtcGFkJzpcbiAgICAgICAgX2lucHV0TW9kZSA9ICdkZWNpbWFsJztcbiAgICAgICAgYnJlYWs7XG4gICAgICBjYXNlICdwaG9uZS1wYWQnOlxuICAgICAgICB0eXBlID0gJ3RlbCc7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAnc2VhcmNoJzpcbiAgICAgIGNhc2UgJ3dlYi1zZWFyY2gnOlxuICAgICAgICB0eXBlID0gJ3NlYXJjaCc7XG4gICAgICAgIGJyZWFrO1xuICAgICAgY2FzZSAndXJsJzpcbiAgICAgICAgdHlwZSA9ICd1cmwnO1xuICAgICAgICBicmVhaztcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHR5cGUgPSAndGV4dCc7XG4gICAgfVxuICB9XG4gIGlmIChzZWN1cmVUZXh0RW50cnkpIHtcbiAgICB0eXBlID0gJ3Bhc3N3b3JkJztcbiAgfVxuICB2YXIgZGltZW5zaW9ucyA9IFJlYWN0LnVzZVJlZih7XG4gICAgaGVpZ2h0OiBudWxsLFxuICAgIHdpZHRoOiBudWxsXG4gIH0pO1xuICB2YXIgaG9zdFJlZiA9IFJlYWN0LnVzZVJlZihudWxsKTtcbiAgdmFyIHByZXZTZWxlY3Rpb24gPSBSZWFjdC51c2VSZWYobnVsbCk7XG4gIHZhciBwcmV2U2VjdXJlVGV4dEVudHJ5ID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcbiAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaG9zdFJlZi5jdXJyZW50ICYmIHByZXZTZWxlY3Rpb24uY3VycmVudCkge1xuICAgICAgc2V0U2VsZWN0aW9uKGhvc3RSZWYuY3VycmVudCwgcHJldlNlbGVjdGlvbi5jdXJyZW50KTtcbiAgICB9XG4gICAgcHJldlNlY3VyZVRleHRFbnRyeS5jdXJyZW50ID0gc2VjdXJlVGV4dEVudHJ5O1xuICB9LCBbc2VjdXJlVGV4dEVudHJ5XSk7XG4gIHZhciBoYW5kbGVDb250ZW50U2l6ZUNoYW5nZSA9IFJlYWN0LnVzZUNhbGxiYWNrKGhvc3ROb2RlID0+IHtcbiAgICBpZiAobXVsdGlsaW5lICYmIG9uQ29udGVudFNpemVDaGFuZ2UgJiYgaG9zdE5vZGUgIT0gbnVsbCkge1xuICAgICAgdmFyIG5ld0hlaWdodCA9IGhvc3ROb2RlLnNjcm9sbEhlaWdodDtcbiAgICAgIHZhciBuZXdXaWR0aCA9IGhvc3ROb2RlLnNjcm9sbFdpZHRoO1xuICAgICAgaWYgKG5ld0hlaWdodCAhPT0gZGltZW5zaW9ucy5jdXJyZW50LmhlaWdodCB8fCBuZXdXaWR0aCAhPT0gZGltZW5zaW9ucy5jdXJyZW50LndpZHRoKSB7XG4gICAgICAgIGRpbWVuc2lvbnMuY3VycmVudC5oZWlnaHQgPSBuZXdIZWlnaHQ7XG4gICAgICAgIGRpbWVuc2lvbnMuY3VycmVudC53aWR0aCA9IG5ld1dpZHRoO1xuICAgICAgICBvbkNvbnRlbnRTaXplQ2hhbmdlKHtcbiAgICAgICAgICBuYXRpdmVFdmVudDoge1xuICAgICAgICAgICAgY29udGVudFNpemU6IHtcbiAgICAgICAgICAgICAgaGVpZ2h0OiBkaW1lbnNpb25zLmN1cnJlbnQuaGVpZ2h0LFxuICAgICAgICAgICAgICB3aWR0aDogZGltZW5zaW9ucy5jdXJyZW50LndpZHRoXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgIH1cbiAgICB9XG4gIH0sIFttdWx0aWxpbmUsIG9uQ29udGVudFNpemVDaGFuZ2VdKTtcbiAgdmFyIGltcGVyYXRpdmVSZWYgPSBSZWFjdC51c2VNZW1vKCgpID0+IGhvc3ROb2RlID0+IHtcbiAgICAvLyBUZXh0SW5wdXQgbmVlZHMgdG8gYWRkIG1vcmUgbWV0aG9kcyB0byB0aGUgaG9zdE5vZGUgaW4gYWRkaXRpb24gdG8gdGhvc2VcbiAgICAvLyBhZGRlZCBieSBgdXNlUGxhdGZvcm1NZXRob2RzYC4gVGhpcyBpcyB0ZW1wb3JhcmlseSB1bnRpbCBhbiBBUEkgbGlrZVxuICAgIC8vIGBUZXh0SW5wdXQuY2xlYXIoaG9zdFJlZilgIGlzIGFkZGVkIHRvIFJlYWN0IE5hdGl2ZS5cbiAgICBpZiAoaG9zdE5vZGUgIT0gbnVsbCkge1xuICAgICAgaG9zdE5vZGUuY2xlYXIgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIGlmIChob3N0Tm9kZSAhPSBudWxsKSB7XG4gICAgICAgICAgaG9zdE5vZGUudmFsdWUgPSAnJztcbiAgICAgICAgfVxuICAgICAgfTtcbiAgICAgIGhvc3ROb2RlLmlzRm9jdXNlZCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgcmV0dXJuIGhvc3ROb2RlICE9IG51bGwgJiYgX1RleHRJbnB1dFN0YXRlLmRlZmF1bHQuY3VycmVudGx5Rm9jdXNlZEZpZWxkKCkgPT09IGhvc3ROb2RlO1xuICAgICAgfTtcbiAgICAgIGhhbmRsZUNvbnRlbnRTaXplQ2hhbmdlKGhvc3ROb2RlKTtcbiAgICB9XG4gIH0sIFtoYW5kbGVDb250ZW50U2l6ZUNoYW5nZV0pO1xuICBmdW5jdGlvbiBoYW5kbGVCbHVyKGUpIHtcbiAgICBfVGV4dElucHV0U3RhdGUuZGVmYXVsdC5fY3VycmVudGx5Rm9jdXNlZE5vZGUgPSBudWxsO1xuICAgIGlmIChvbkJsdXIpIHtcbiAgICAgIGUubmF0aXZlRXZlbnQudGV4dCA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgb25CbHVyKGUpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBoYW5kbGVDaGFuZ2UoZSkge1xuICAgIHZhciBob3N0Tm9kZSA9IGUudGFyZ2V0O1xuICAgIHZhciB0ZXh0ID0gaG9zdE5vZGUudmFsdWU7XG4gICAgZS5uYXRpdmVFdmVudC50ZXh0ID0gdGV4dDtcbiAgICBoYW5kbGVDb250ZW50U2l6ZUNoYW5nZShob3N0Tm9kZSk7XG4gICAgaWYgKG9uQ2hhbmdlKSB7XG4gICAgICBvbkNoYW5nZShlKTtcbiAgICB9XG4gICAgaWYgKG9uQ2hhbmdlVGV4dCkge1xuICAgICAgb25DaGFuZ2VUZXh0KHRleHQpO1xuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBoYW5kbGVGb2N1cyhlKSB7XG4gICAgdmFyIGhvc3ROb2RlID0gZS50YXJnZXQ7XG4gICAgaWYgKG9uRm9jdXMpIHtcbiAgICAgIGUubmF0aXZlRXZlbnQudGV4dCA9IGhvc3ROb2RlLnZhbHVlO1xuICAgICAgb25Gb2N1cyhlKTtcbiAgICB9XG4gICAgaWYgKGhvc3ROb2RlICE9IG51bGwpIHtcbiAgICAgIF9UZXh0SW5wdXRTdGF0ZS5kZWZhdWx0Ll9jdXJyZW50bHlGb2N1c2VkTm9kZSA9IGhvc3ROb2RlO1xuICAgICAgaWYgKGNsZWFyVGV4dE9uRm9jdXMpIHtcbiAgICAgICAgaG9zdE5vZGUudmFsdWUgPSAnJztcbiAgICAgIH1cbiAgICAgIGlmIChzZWxlY3RUZXh0T25Gb2N1cykge1xuICAgICAgICAvLyBTYWZhcmkgcmVxdWlyZXMgc2VsZWN0aW9uIHRvIG9jY3VyIGluIGEgc2V0VGltZW91dFxuICAgICAgICBpZiAoZm9jdXNUaW1lb3V0ICE9IG51bGwpIHtcbiAgICAgICAgICBjbGVhclRpbWVvdXQoZm9jdXNUaW1lb3V0KTtcbiAgICAgICAgfVxuICAgICAgICBmb2N1c1RpbWVvdXQgPSBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAvLyBDaGVjayBpZiB0aGUgaW5wdXQgaXMgc3RpbGwgZm9jdXNlZCBhZnRlciB0aGUgdGltZW91dFxuICAgICAgICAgIC8vIChzZWUgIzI3MDQpXG4gICAgICAgICAgaWYgKGhvc3ROb2RlICE9IG51bGwgJiYgZG9jdW1lbnQuYWN0aXZlRWxlbWVudCA9PT0gaG9zdE5vZGUpIHtcbiAgICAgICAgICAgIGhvc3ROb2RlLnNlbGVjdCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSwgMCk7XG4gICAgICB9XG4gICAgfVxuICB9XG4gIGZ1bmN0aW9uIGhhbmRsZUtleURvd24oZSkge1xuICAgIHZhciBob3N0Tm9kZSA9IGUudGFyZ2V0O1xuICAgIC8vIFByZXZlbnQga2V5IGV2ZW50cyBidWJibGluZyAoc2VlICM2MTIpXG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICB2YXIgYmx1ck9uU3VibWl0RGVmYXVsdCA9ICFtdWx0aWxpbmU7XG4gICAgdmFyIHNob3VsZEJsdXJPblN1Ym1pdCA9IGJsdXJPblN1Ym1pdCA9PSBudWxsID8gYmx1ck9uU3VibWl0RGVmYXVsdCA6IGJsdXJPblN1Ym1pdDtcbiAgICB2YXIgbmF0aXZlRXZlbnQgPSBlLm5hdGl2ZUV2ZW50O1xuICAgIHZhciBpc0NvbXBvc2luZyA9IGlzRXZlbnRDb21wb3NpbmcobmF0aXZlRXZlbnQpO1xuICAgIGlmIChvbktleVByZXNzKSB7XG4gICAgICBvbktleVByZXNzKGUpO1xuICAgIH1cbiAgICBpZiAoZS5rZXkgPT09ICdFbnRlcicgJiYgIWUuc2hpZnRLZXkgJiZcbiAgICAvLyBEbyBub3QgY2FsbCBzdWJtaXQgaWYgY29tcG9zaXRpb24gaXMgb2NjdXJpbmcuXG4gICAgIWlzQ29tcG9zaW5nICYmICFlLmlzRGVmYXVsdFByZXZlbnRlZCgpKSB7XG4gICAgICBpZiAoKGJsdXJPblN1Ym1pdCB8fCAhbXVsdGlsaW5lKSAmJiBvblN1Ym1pdEVkaXRpbmcpIHtcbiAgICAgICAgLy8gcHJldmVudCBcIkVudGVyXCIgZnJvbSBpbnNlcnRpbmcgYSBuZXdsaW5lIG9yIHN1Ym1pdHRpbmcgYSBmb3JtXG4gICAgICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgbmF0aXZlRXZlbnQudGV4dCA9IGUudGFyZ2V0LnZhbHVlO1xuICAgICAgICBvblN1Ym1pdEVkaXRpbmcoZSk7XG4gICAgICB9XG4gICAgICBpZiAoc2hvdWxkQmx1ck9uU3VibWl0ICYmIGhvc3ROb2RlICE9IG51bGwpIHtcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiBob3N0Tm9kZS5ibHVyKCksIDApO1xuICAgICAgfVxuICAgIH1cbiAgfVxuICBmdW5jdGlvbiBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UoZSkge1xuICAgIHRyeSB7XG4gICAgICB2YXIgX2UkdGFyZ2V0ID0gZS50YXJnZXQsXG4gICAgICAgIHNlbGVjdGlvblN0YXJ0ID0gX2UkdGFyZ2V0LnNlbGVjdGlvblN0YXJ0LFxuICAgICAgICBzZWxlY3Rpb25FbmQgPSBfZSR0YXJnZXQuc2VsZWN0aW9uRW5kO1xuICAgICAgdmFyIF9zZWxlY3Rpb24gPSB7XG4gICAgICAgIHN0YXJ0OiBzZWxlY3Rpb25TdGFydCxcbiAgICAgICAgZW5kOiBzZWxlY3Rpb25FbmRcbiAgICAgIH07XG4gICAgICBpZiAob25TZWxlY3Rpb25DaGFuZ2UpIHtcbiAgICAgICAgZS5uYXRpdmVFdmVudC5zZWxlY3Rpb24gPSBfc2VsZWN0aW9uO1xuICAgICAgICBlLm5hdGl2ZUV2ZW50LnRleHQgPSBlLnRhcmdldC52YWx1ZTtcbiAgICAgICAgb25TZWxlY3Rpb25DaGFuZ2UoZSk7XG4gICAgICB9XG4gICAgICBpZiAocHJldlNlY3VyZVRleHRFbnRyeS5jdXJyZW50ID09PSBzZWN1cmVUZXh0RW50cnkpIHtcbiAgICAgICAgcHJldlNlbGVjdGlvbi5jdXJyZW50ID0gX3NlbGVjdGlvbjtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlKSB7fVxuICB9XG4gICgwLCBfdXNlTGF5b3V0RWZmZWN0LmRlZmF1bHQpKCgpID0+IHtcbiAgICB2YXIgbm9kZSA9IGhvc3RSZWYuY3VycmVudDtcbiAgICBpZiAobm9kZSAhPSBudWxsICYmIHNlbGVjdGlvbiAhPSBudWxsKSB7XG4gICAgICBzZXRTZWxlY3Rpb24obm9kZSwgc2VsZWN0aW9uKTtcbiAgICB9XG4gICAgaWYgKGRvY3VtZW50LmFjdGl2ZUVsZW1lbnQgPT09IG5vZGUpIHtcbiAgICAgIF9UZXh0SW5wdXRTdGF0ZS5kZWZhdWx0Ll9jdXJyZW50bHlGb2N1c2VkTm9kZSA9IG5vZGU7XG4gICAgfVxuICB9LCBbaG9zdFJlZiwgc2VsZWN0aW9uXSk7XG4gIHZhciBjb21wb25lbnQgPSBtdWx0aWxpbmUgPyAndGV4dGFyZWEnIDogJ2lucHV0JztcbiAgKDAsIF91c2VFbGVtZW50TGF5b3V0LmRlZmF1bHQpKGhvc3RSZWYsIG9uTGF5b3V0KTtcbiAgKDAsIF91c2VSZXNwb25kZXJFdmVudHMuZGVmYXVsdCkoaG9zdFJlZiwge1xuICAgIG9uTW92ZVNob3VsZFNldFJlc3BvbmRlcixcbiAgICBvbk1vdmVTaG91bGRTZXRSZXNwb25kZXJDYXB0dXJlLFxuICAgIG9uUmVzcG9uZGVyRW5kLFxuICAgIG9uUmVzcG9uZGVyR3JhbnQsXG4gICAgb25SZXNwb25kZXJNb3ZlLFxuICAgIG9uUmVzcG9uZGVyUmVqZWN0LFxuICAgIG9uUmVzcG9uZGVyUmVsZWFzZSxcbiAgICBvblJlc3BvbmRlclN0YXJ0LFxuICAgIG9uUmVzcG9uZGVyVGVybWluYXRlLFxuICAgIG9uUmVzcG9uZGVyVGVybWluYXRpb25SZXF1ZXN0LFxuICAgIG9uU2Nyb2xsU2hvdWxkU2V0UmVzcG9uZGVyLFxuICAgIG9uU2Nyb2xsU2hvdWxkU2V0UmVzcG9uZGVyQ2FwdHVyZSxcbiAgICBvblNlbGVjdGlvbkNoYW5nZVNob3VsZFNldFJlc3BvbmRlcixcbiAgICBvblNlbGVjdGlvbkNoYW5nZVNob3VsZFNldFJlc3BvbmRlckNhcHR1cmUsXG4gICAgb25TdGFydFNob3VsZFNldFJlc3BvbmRlcixcbiAgICBvblN0YXJ0U2hvdWxkU2V0UmVzcG9uZGVyQ2FwdHVyZVxuICB9KTtcbiAgdmFyIF91c2VMb2NhbGVDb250ZXh0ID0gKDAsIF91c2VMb2NhbGUudXNlTG9jYWxlQ29udGV4dCkoKSxcbiAgICBjb250ZXh0RGlyZWN0aW9uID0gX3VzZUxvY2FsZUNvbnRleHQuZGlyZWN0aW9uO1xuICB2YXIgc3VwcG9ydGVkUHJvcHMgPSBwaWNrUHJvcHMocHJvcHMpO1xuICBzdXBwb3J0ZWRQcm9wcy5hdXRvQ2FwaXRhbGl6ZSA9IGF1dG9DYXBpdGFsaXplO1xuICBzdXBwb3J0ZWRQcm9wcy5hdXRvQ29tcGxldGUgPSBhdXRvQ29tcGxldGUgfHwgYXV0b0NvbXBsZXRlVHlwZSB8fCAnb24nO1xuICBzdXBwb3J0ZWRQcm9wcy5hdXRvQ29ycmVjdCA9IGF1dG9Db3JyZWN0ID8gJ29uJyA6ICdvZmYnO1xuICAvLyAnYXV0bycgYnkgZGVmYXVsdCBhbGxvd3MgYnJvd3NlcnMgdG8gaW5mZXIgd3JpdGluZyBkaXJlY3Rpb25cbiAgc3VwcG9ydGVkUHJvcHMuZGlyID0gZGlyICE9PSB1bmRlZmluZWQgPyBkaXIgOiAnYXV0byc7XG4gIC8qXG4gIGlmIChyZXR1cm5LZXlUeXBlICE9IG51bGwpIHtcbiAgICB3YXJuT25jZSgncmV0dXJuS2V5VHlwZScsICdyZXR1cm5LZXlUeXBlIGlzIGRlcHJlY2F0ZWQuIFVzZSBlbnRlcktleUhpbnQuJyk7XG4gIH1cbiAgKi9cbiAgc3VwcG9ydGVkUHJvcHMuZW50ZXJLZXlIaW50ID0gZW50ZXJLZXlIaW50IHx8IHJldHVybktleVR5cGU7XG4gIHN1cHBvcnRlZFByb3BzLmlucHV0TW9kZSA9IF9pbnB1dE1vZGU7XG4gIHN1cHBvcnRlZFByb3BzLm9uQmx1ciA9IGhhbmRsZUJsdXI7XG4gIHN1cHBvcnRlZFByb3BzLm9uQ2hhbmdlID0gaGFuZGxlQ2hhbmdlO1xuICBzdXBwb3J0ZWRQcm9wcy5vbkZvY3VzID0gaGFuZGxlRm9jdXM7XG4gIHN1cHBvcnRlZFByb3BzLm9uS2V5RG93biA9IGhhbmRsZUtleURvd247XG4gIHN1cHBvcnRlZFByb3BzLm9uU2VsZWN0ID0gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlO1xuICAvKlxuICBpZiAoZWRpdGFibGUgIT0gbnVsbCkge1xuICAgIHdhcm5PbmNlKCdlZGl0YWJsZScsICdlZGl0YWJsZSBpcyBkZXByZWNhdGVkLiBVc2UgcmVhZE9ubHkuJyk7XG4gIH1cbiAgKi9cbiAgc3VwcG9ydGVkUHJvcHMucmVhZE9ubHkgPSByZWFkT25seSA9PT0gdHJ1ZSB8fCBlZGl0YWJsZSA9PT0gZmFsc2U7XG4gIC8qXG4gIGlmIChudW1iZXJPZkxpbmVzICE9IG51bGwpIHtcbiAgICB3YXJuT25jZShcbiAgICAgICdudW1iZXJPZkxpbmVzJyxcbiAgICAgICdUZXh0SW5wdXQgbnVtYmVyT2ZMaW5lcyBpcyBkZXByZWNhdGVkLiBVc2Ugcm93cy4nXG4gICAgKTtcbiAgfVxuICAqL1xuICBzdXBwb3J0ZWRQcm9wcy5yb3dzID0gbXVsdGlsaW5lID8gcm93cyAhPSBudWxsID8gcm93cyA6IG51bWJlck9mTGluZXMgOiAxO1xuICBzdXBwb3J0ZWRQcm9wcy5zcGVsbENoZWNrID0gc3BlbGxDaGVjayAhPSBudWxsID8gc3BlbGxDaGVjayA6IGF1dG9Db3JyZWN0O1xuICBzdXBwb3J0ZWRQcm9wcy5zdHlsZSA9IFt7XG4gICAgJy0tcGxhY2Vob2xkZXJUZXh0Q29sb3InOiBwbGFjZWhvbGRlclRleHRDb2xvclxuICB9LCBzdHlsZXMudGV4dGlucHV0JHJhdywgc3R5bGVzLnBsYWNlaG9sZGVyLCBwcm9wcy5zdHlsZSwgY2FyZXRIaWRkZW4gJiYgc3R5bGVzLmNhcmV0SGlkZGVuXTtcbiAgc3VwcG9ydGVkUHJvcHMudHlwZSA9IG11bHRpbGluZSA/IHVuZGVmaW5lZCA6IHR5cGU7XG4gIHN1cHBvcnRlZFByb3BzLnZpcnR1YWxrZXlib2FyZHBvbGljeSA9IHNob3dTb2Z0SW5wdXRPbkZvY3VzID09PSBmYWxzZSA/ICdtYW51YWwnIDogJ2F1dG8nO1xuICB2YXIgcGxhdGZvcm1NZXRob2RzUmVmID0gKDAsIF91c2VQbGF0Zm9ybU1ldGhvZHMuZGVmYXVsdCkoc3VwcG9ydGVkUHJvcHMpO1xuICB2YXIgc2V0UmVmID0gKDAsIF91c2VNZXJnZVJlZnMuZGVmYXVsdCkoaG9zdFJlZiwgcGxhdGZvcm1NZXRob2RzUmVmLCBpbXBlcmF0aXZlUmVmLCBmb3J3YXJkZWRSZWYpO1xuICBzdXBwb3J0ZWRQcm9wcy5yZWYgPSBzZXRSZWY7XG4gIHZhciBsYW5nRGlyZWN0aW9uID0gcHJvcHMubGFuZyAhPSBudWxsID8gKDAsIF91c2VMb2NhbGUuZ2V0TG9jYWxlRGlyZWN0aW9uKShwcm9wcy5sYW5nKSA6IG51bGw7XG4gIHZhciBjb21wb25lbnREaXJlY3Rpb24gPSBwcm9wcy5kaXIgfHwgbGFuZ0RpcmVjdGlvbjtcbiAgdmFyIHdyaXRpbmdEaXJlY3Rpb24gPSBjb21wb25lbnREaXJlY3Rpb24gfHwgY29udGV4dERpcmVjdGlvbjtcbiAgdmFyIGVsZW1lbnQgPSAoMCwgX2NyZWF0ZUVsZW1lbnQuZGVmYXVsdCkoY29tcG9uZW50LCBzdXBwb3J0ZWRQcm9wcywge1xuICAgIHdyaXRpbmdEaXJlY3Rpb25cbiAgfSk7XG4gIHJldHVybiBlbGVtZW50O1xufSk7XG5UZXh0SW5wdXQuZGlzcGxheU5hbWUgPSAnVGV4dElucHV0Jztcbi8vICRGbG93Rml4TWVcblRleHRJbnB1dC5TdGF0ZSA9IF9UZXh0SW5wdXRTdGF0ZS5kZWZhdWx0O1xudmFyIHN0eWxlcyA9IF9TdHlsZVNoZWV0LmRlZmF1bHQuY3JlYXRlKHtcbiAgdGV4dGlucHV0JHJhdzoge1xuICAgIE1vekFwcGVhcmFuY2U6ICd0ZXh0ZmllbGQnLFxuICAgIFdlYmtpdEFwcGVhcmFuY2U6ICdub25lJyxcbiAgICBiYWNrZ3JvdW5kQ29sb3I6ICd0cmFuc3BhcmVudCcsXG4gICAgYm9yZGVyOiAnMCBzb2xpZCBibGFjaycsXG4gICAgYm9yZGVyUmFkaXVzOiAwLFxuICAgIGJveFNpemluZzogJ2JvcmRlci1ib3gnLFxuICAgIGZvbnQ6ICcxNHB4IFN5c3RlbScsXG4gICAgbWFyZ2luOiAwLFxuICAgIHBhZGRpbmc6IDAsXG4gICAgcmVzaXplOiAnbm9uZSdcbiAgfSxcbiAgcGxhY2Vob2xkZXI6IHtcbiAgICBwbGFjZWhvbGRlclRleHRDb2xvcjogJ3ZhcigtLXBsYWNlaG9sZGVyVGV4dENvbG9yKSdcbiAgfSxcbiAgY2FyZXRIaWRkZW46IHtcbiAgICBjYXJldENvbG9yOiAndHJhbnNwYXJlbnQnXG4gIH1cbn0pO1xudmFyIF9kZWZhdWx0ID0gZXhwb3J0cy5kZWZhdWx0ID0gVGV4dElucHV0O1xubW9kdWxlLmV4cG9ydHMgPSBleHBvcnRzLmRlZmF1bHQ7Il0sIm1hcHBpbmdzIjoiQUFBQSxZQUFZO0FBV1osWUFBWTs7QUFFWixJQUFJQSxzQkFBc0IsR0FBR0MsT0FBTyxDQUFDLDhDQUE4QyxDQUFDLENBQUNDLE9BQU87QUFDNUYsSUFBSUMsdUJBQXVCLEdBQUdGLE9BQU8sQ0FBQywrQ0FBK0MsQ0FBQyxDQUFDQyxPQUFPO0FBQzlGRSxPQUFPLENBQUNDLFVBQVUsR0FBRyxJQUFJO0FBQ3pCRCxPQUFPLENBQUNGLE9BQU8sR0FBRyxLQUFLLENBQUM7QUFDeEIsSUFBSUksS0FBSyxHQUFHSCx1QkFBdUIsQ0FBQ0YsT0FBTyxDQUFDLE9BQU8sQ0FBQyxDQUFDO0FBQ3JELElBQUlNLGNBQWMsR0FBR1Asc0JBQXNCLENBQUNDLE9BQU8sbUJBQW1CLENBQUMsQ0FBQztBQUN4RSxJQUFJTyxjQUFjLEdBQUdMLHVCQUF1QixDQUFDRixPQUFPLCtCQUErQixDQUFDLENBQUM7QUFDckYsSUFBSVEsS0FBSyxHQUFHVCxzQkFBc0IsQ0FBQ0MsT0FBTyxxQkFBcUIsQ0FBQyxDQUFDO0FBQ2pFLElBQUlTLGlCQUFpQixHQUFHVixzQkFBc0IsQ0FBQ0MsT0FBTyxpQ0FBaUMsQ0FBQyxDQUFDO0FBQ3pGLElBQUlVLGdCQUFnQixHQUFHWCxzQkFBc0IsQ0FBQ0MsT0FBTyxnQ0FBZ0MsQ0FBQyxDQUFDO0FBQ3ZGLElBQUlXLGFBQWEsR0FBR1osc0JBQXNCLENBQUNDLE9BQU8sNkJBQTZCLENBQUMsQ0FBQztBQUNqRixJQUFJWSxtQkFBbUIsR0FBR2Isc0JBQXNCLENBQUNDLE9BQU8sbUNBQW1DLENBQUMsQ0FBQztBQUM3RixJQUFJYSxtQkFBbUIsR0FBR2Qsc0JBQXNCLENBQUNDLE9BQU8sbUNBQW1DLENBQUMsQ0FBQztBQUM3RixJQUFJYyxVQUFVLEdBQUdkLE9BQU8sMEJBQTBCLENBQUM7QUFDbkQsSUFBSWUsV0FBVyxHQUFHaEIsc0JBQXNCLENBQUNDLE9BQU8sZ0JBQWdCLENBQUMsQ0FBQztBQUNsRSxJQUFJZ0IsZUFBZSxHQUFHakIsc0JBQXNCLENBQUNDLE9BQU8sK0JBQStCLENBQUMsQ0FBQztBQU9yRixJQUFJaUIsZ0JBQWdCLEdBQUcsU0FBbkJBLGdCQUFnQkEsQ0FBSUMsSUFBSSxFQUFFQyxTQUFTLEVBQUs7RUFDMUMsSUFBSUMsWUFBWSxHQUFHRixJQUFJLENBQUNFLFlBQVk7SUFDbENDLGNBQWMsR0FBR0gsSUFBSSxDQUFDRyxjQUFjO0VBQ3RDLElBQUlDLEtBQUssR0FBR0gsU0FBUyxDQUFDRyxLQUFLO0lBQ3pCQyxHQUFHLEdBQUdKLFNBQVMsQ0FBQ0ksR0FBRztFQUNyQixPQUFPRCxLQUFLLEtBQUtELGNBQWMsSUFBSUUsR0FBRyxLQUFLSCxZQUFZO0FBQ3pELENBQUM7QUFNRCxJQUFJSSxZQUFZLEdBQUcsU0FBZkEsWUFBWUEsQ0FBSU4sSUFBSSxFQUFFQyxTQUFTLEVBQUs7RUFDdEMsSUFBSUYsZ0JBQWdCLENBQUNDLElBQUksRUFBRUMsU0FBUyxDQUFDLEVBQUU7SUFDckMsSUFBSUcsS0FBSyxHQUFHSCxTQUFTLENBQUNHLEtBQUs7TUFDekJDLEdBQUcsR0FBR0osU0FBUyxDQUFDSSxHQUFHO0lBQ3JCLElBQUk7TUFDRkwsSUFBSSxDQUFDTyxpQkFBaUIsQ0FBQ0gsS0FBSyxFQUFFQyxHQUFHLElBQUlELEtBQUssQ0FBQztJQUM3QyxDQUFDLENBQUMsT0FBT0ksQ0FBQyxFQUFFLENBQUM7RUFDZjtBQUNGLENBQUM7QUFDRCxJQUFJQyxnQkFBZ0IsR0FBR0MsTUFBTSxDQUFDQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUV0QixjQUFjLENBQUN1QixZQUFZLEVBQUV2QixjQUFjLENBQUN3QixrQkFBa0IsRUFBRXhCLGNBQWMsQ0FBQ3lCLFVBQVUsRUFBRXpCLGNBQWMsQ0FBQzBCLFVBQVUsRUFBRTFCLGNBQWMsQ0FBQzJCLGFBQWEsRUFBRTNCLGNBQWMsQ0FBQzRCLFVBQVUsRUFBRTVCLGNBQWMsQ0FBQzZCLFVBQVUsRUFBRTdCLGNBQWMsQ0FBQzhCLFVBQVUsRUFBRTtFQUM1UUMsY0FBYyxFQUFFLElBQUk7RUFDcEJDLFlBQVksRUFBRSxJQUFJO0VBQ2xCQyxXQUFXLEVBQUUsSUFBSTtFQUNqQkMsU0FBUyxFQUFFLElBQUk7RUFDZkMsWUFBWSxFQUFFLElBQUk7RUFDbEJDLFFBQVEsRUFBRSxJQUFJO0VBQ2RDLElBQUksRUFBRSxJQUFJO0VBQ1ZDLFNBQVMsRUFBRSxJQUFJO0VBQ2ZDLFFBQVEsRUFBRSxJQUFJO0VBQ2RDLFFBQVEsRUFBRSxJQUFJO0VBQ2RDLFdBQVcsRUFBRSxJQUFJO0VBQ2pCQyxhQUFhLEVBQUUsSUFBSTtFQUNuQkMsUUFBUSxFQUFFLElBQUk7RUFDZEMsSUFBSSxFQUFFLElBQUk7RUFDVkMsVUFBVSxFQUFFLElBQUk7RUFDaEJDLEtBQUssRUFBRSxJQUFJO0VBQ1hDLElBQUksRUFBRTtBQUNSLENBQUMsQ0FBQztBQUNGLElBQUlDLFNBQVMsR0FBRyxTQUFaQSxTQUFTQSxDQUFHQyxLQUFLO0VBQUEsT0FBSSxDQUFDLENBQUMsRUFBRWhELEtBQUssQ0FBQ1AsT0FBTyxFQUFFdUQsS0FBSyxFQUFFN0IsZ0JBQWdCLENBQUM7QUFBQTtBQUlwRSxTQUFTOEIsZ0JBQWdCQSxDQUFDQyxXQUFXLEVBQUU7RUFDckMsT0FBT0EsV0FBVyxDQUFDQyxXQUFXLElBQUlELFdBQVcsQ0FBQ0UsT0FBTyxLQUFLLEdBQUc7QUFDL0Q7QUFDQSxJQUFJQyxZQUFZLEdBQUcsSUFBSTtBQUN2QixJQUFJQyxTQUFTLEdBQWdCekQsS0FBSyxDQUFDMEQsVUFBVSxDQUFDLFVBQUNQLEtBQUssRUFBRVEsWUFBWSxFQUFLO0VBQ3JFLElBQUlDLHFCQUFxQixHQUFHVCxLQUFLLENBQUNsQixjQUFjO0lBQzlDQSxjQUFjLEdBQUcyQixxQkFBcUIsS0FBSyxLQUFLLENBQUMsR0FBRyxXQUFXLEdBQUdBLHFCQUFxQjtJQUN2RjFCLFlBQVksR0FBR2lCLEtBQUssQ0FBQ2pCLFlBQVk7SUFDakMyQixnQkFBZ0IsR0FBR1YsS0FBSyxDQUFDVSxnQkFBZ0I7SUFDekNDLGtCQUFrQixHQUFHWCxLQUFLLENBQUNoQixXQUFXO0lBQ3RDQSxXQUFXLEdBQUcyQixrQkFBa0IsS0FBSyxLQUFLLENBQUMsR0FBRyxJQUFJLEdBQUdBLGtCQUFrQjtJQUN2RUMsWUFBWSxHQUFHWixLQUFLLENBQUNZLFlBQVk7SUFDakNDLFdBQVcsR0FBR2IsS0FBSyxDQUFDYSxXQUFXO0lBQy9CQyxnQkFBZ0IsR0FBR2QsS0FBSyxDQUFDYyxnQkFBZ0I7SUFDekNDLEdBQUcsR0FBR2YsS0FBSyxDQUFDZSxHQUFHO0lBQ2ZDLFFBQVEsR0FBR2hCLEtBQUssQ0FBQ2dCLFFBQVE7SUFDekJDLFlBQVksR0FBR2pCLEtBQUssQ0FBQ2lCLFlBQVk7SUFDakNDLFNBQVMsR0FBR2xCLEtBQUssQ0FBQ2tCLFNBQVM7SUFDM0JDLFlBQVksR0FBR25CLEtBQUssQ0FBQ21CLFlBQVk7SUFDakNDLGdCQUFnQixHQUFHcEIsS0FBSyxDQUFDcUIsU0FBUztJQUNsQ0EsU0FBUyxHQUFHRCxnQkFBZ0IsS0FBSyxLQUFLLENBQUMsR0FBRyxLQUFLLEdBQUdBLGdCQUFnQjtJQUNsRUUsYUFBYSxHQUFHdEIsS0FBSyxDQUFDc0IsYUFBYTtJQUNuQ0MsTUFBTSxHQUFHdkIsS0FBSyxDQUFDdUIsTUFBTTtJQUNyQmpDLFFBQVEsR0FBR1UsS0FBSyxDQUFDVixRQUFRO0lBQ3pCa0MsWUFBWSxHQUFHeEIsS0FBSyxDQUFDd0IsWUFBWTtJQUNqQ0MsbUJBQW1CLEdBQUd6QixLQUFLLENBQUN5QixtQkFBbUI7SUFDL0NDLE9BQU8sR0FBRzFCLEtBQUssQ0FBQzBCLE9BQU87SUFDdkJDLFVBQVUsR0FBRzNCLEtBQUssQ0FBQzJCLFVBQVU7SUFDN0JDLFFBQVEsR0FBRzVCLEtBQUssQ0FBQzRCLFFBQVE7SUFDekJDLHdCQUF3QixHQUFHN0IsS0FBSyxDQUFDNkIsd0JBQXdCO0lBQ3pEQywrQkFBK0IsR0FBRzlCLEtBQUssQ0FBQzhCLCtCQUErQjtJQUN2RUMsY0FBYyxHQUFHL0IsS0FBSyxDQUFDK0IsY0FBYztJQUNyQ0MsZ0JBQWdCLEdBQUdoQyxLQUFLLENBQUNnQyxnQkFBZ0I7SUFDekNDLGVBQWUsR0FBR2pDLEtBQUssQ0FBQ2lDLGVBQWU7SUFDdkNDLGlCQUFpQixHQUFHbEMsS0FBSyxDQUFDa0MsaUJBQWlCO0lBQzNDQyxrQkFBa0IsR0FBR25DLEtBQUssQ0FBQ21DLGtCQUFrQjtJQUM3Q0MsZ0JBQWdCLEdBQUdwQyxLQUFLLENBQUNvQyxnQkFBZ0I7SUFDekNDLG9CQUFvQixHQUFHckMsS0FBSyxDQUFDcUMsb0JBQW9CO0lBQ2pEQyw2QkFBNkIsR0FBR3RDLEtBQUssQ0FBQ3NDLDZCQUE2QjtJQUNuRUMsMEJBQTBCLEdBQUd2QyxLQUFLLENBQUN1QywwQkFBMEI7SUFDN0RDLGlDQUFpQyxHQUFHeEMsS0FBSyxDQUFDd0MsaUNBQWlDO0lBQzNFQyxpQkFBaUIsR0FBR3pDLEtBQUssQ0FBQ3lDLGlCQUFpQjtJQUMzQ0MsbUNBQW1DLEdBQUcxQyxLQUFLLENBQUMwQyxtQ0FBbUM7SUFDL0VDLDBDQUEwQyxHQUFHM0MsS0FBSyxDQUFDMkMsMENBQTBDO0lBQzdGQyx5QkFBeUIsR0FBRzVDLEtBQUssQ0FBQzRDLHlCQUF5QjtJQUMzREMsZ0NBQWdDLEdBQUc3QyxLQUFLLENBQUM2QyxnQ0FBZ0M7SUFDekVDLGVBQWUsR0FBRzlDLEtBQUssQ0FBQzhDLGVBQWU7SUFDdkNDLG9CQUFvQixHQUFHL0MsS0FBSyxDQUFDK0Msb0JBQW9CO0lBQ2pEQyxlQUFlLEdBQUdoRCxLQUFLLENBQUNOLFFBQVE7SUFDaENBLFFBQVEsR0FBR3NELGVBQWUsS0FBSyxLQUFLLENBQUMsR0FBRyxLQUFLLEdBQUdBLGVBQWU7SUFDL0RDLGFBQWEsR0FBR2pELEtBQUssQ0FBQ2lELGFBQWE7SUFDbkN0RCxJQUFJLEdBQUdLLEtBQUssQ0FBQ0wsSUFBSTtJQUNqQnVELHFCQUFxQixHQUFHbEQsS0FBSyxDQUFDbUQsZUFBZTtJQUM3Q0EsZUFBZSxHQUFHRCxxQkFBcUIsS0FBSyxLQUFLLENBQUMsR0FBRyxLQUFLLEdBQUdBLHFCQUFxQjtJQUNsRnZGLFNBQVMsR0FBR3FDLEtBQUssQ0FBQ3JDLFNBQVM7SUFDM0J5RixpQkFBaUIsR0FBR3BELEtBQUssQ0FBQ29ELGlCQUFpQjtJQUMzQ0Msb0JBQW9CLEdBQUdyRCxLQUFLLENBQUNxRCxvQkFBb0I7SUFDakR6RCxVQUFVLEdBQUdJLEtBQUssQ0FBQ0osVUFBVTtFQUMvQixJQUFJRSxJQUFJO0VBQ1IsSUFBSXdELFVBQVU7RUFDZCxJQUFJcEMsU0FBUyxJQUFJLElBQUksRUFBRTtJQUNyQm9DLFVBQVUsR0FBR3BDLFNBQVM7SUFDdEIsSUFBSUEsU0FBUyxLQUFLLE9BQU8sRUFBRTtNQUN6QnBCLElBQUksR0FBRyxPQUFPO0lBQ2hCLENBQUMsTUFBTSxJQUFJb0IsU0FBUyxLQUFLLEtBQUssRUFBRTtNQUM5QnBCLElBQUksR0FBRyxLQUFLO0lBQ2QsQ0FBQyxNQUFNLElBQUlvQixTQUFTLEtBQUssUUFBUSxFQUFFO01BQ2pDcEIsSUFBSSxHQUFHLFFBQVE7SUFDakIsQ0FBQyxNQUFNLElBQUlvQixTQUFTLEtBQUssS0FBSyxFQUFFO01BQzlCcEIsSUFBSSxHQUFHLEtBQUs7SUFDZCxDQUFDLE1BQU07TUFDTEEsSUFBSSxHQUFHLE1BQU07SUFDZjtFQUNGLENBQUMsTUFBTSxJQUFJcUIsWUFBWSxJQUFJLElBQUksRUFBRTtJQUUvQixRQUFRQSxZQUFZO01BQ2xCLEtBQUssZUFBZTtRQUNsQnJCLElBQUksR0FBRyxPQUFPO1FBQ2Q7TUFDRixLQUFLLFlBQVk7TUFDakIsS0FBSyxTQUFTO1FBQ1p3RCxVQUFVLEdBQUcsU0FBUztRQUN0QjtNQUNGLEtBQUssYUFBYTtRQUNoQkEsVUFBVSxHQUFHLFNBQVM7UUFDdEI7TUFDRixLQUFLLFdBQVc7UUFDZHhELElBQUksR0FBRyxLQUFLO1FBQ1o7TUFDRixLQUFLLFFBQVE7TUFDYixLQUFLLFlBQVk7UUFDZkEsSUFBSSxHQUFHLFFBQVE7UUFDZjtNQUNGLEtBQUssS0FBSztRQUNSQSxJQUFJLEdBQUcsS0FBSztRQUNaO01BQ0Y7UUFDRUEsSUFBSSxHQUFHLE1BQU07SUFDakI7RUFDRjtFQUNBLElBQUlxRCxlQUFlLEVBQUU7SUFDbkJyRCxJQUFJLEdBQUcsVUFBVTtFQUNuQjtFQUNBLElBQUl5RCxVQUFVLEdBQUcxRyxLQUFLLENBQUMyRyxNQUFNLENBQUM7SUFDNUJDLE1BQU0sRUFBRSxJQUFJO0lBQ1pDLEtBQUssRUFBRTtFQUNULENBQUMsQ0FBQztFQUNGLElBQUlDLE9BQU8sR0FBRzlHLEtBQUssQ0FBQzJHLE1BQU0sQ0FBQyxJQUFJLENBQUM7RUFDaEMsSUFBSUksYUFBYSxHQUFHL0csS0FBSyxDQUFDMkcsTUFBTSxDQUFDLElBQUksQ0FBQztFQUN0QyxJQUFJSyxtQkFBbUIsR0FBR2hILEtBQUssQ0FBQzJHLE1BQU0sQ0FBQyxLQUFLLENBQUM7RUFDN0MzRyxLQUFLLENBQUNpSCxTQUFTLENBQUMsWUFBTTtJQUNwQixJQUFJSCxPQUFPLENBQUNJLE9BQU8sSUFBSUgsYUFBYSxDQUFDRyxPQUFPLEVBQUU7TUFDNUMvRixZQUFZLENBQUMyRixPQUFPLENBQUNJLE9BQU8sRUFBRUgsYUFBYSxDQUFDRyxPQUFPLENBQUM7SUFDdEQ7SUFDQUYsbUJBQW1CLENBQUNFLE9BQU8sR0FBR1osZUFBZTtFQUMvQyxDQUFDLEVBQUUsQ0FBQ0EsZUFBZSxDQUFDLENBQUM7RUFDckIsSUFBSWEsdUJBQXVCLEdBQUduSCxLQUFLLENBQUNvSCxXQUFXLENBQUMsVUFBQUMsUUFBUSxFQUFJO0lBQzFELElBQUk3QyxTQUFTLElBQUlJLG1CQUFtQixJQUFJeUMsUUFBUSxJQUFJLElBQUksRUFBRTtNQUN4RCxJQUFJQyxTQUFTLEdBQUdELFFBQVEsQ0FBQ0UsWUFBWTtNQUNyQyxJQUFJQyxRQUFRLEdBQUdILFFBQVEsQ0FBQ0ksV0FBVztNQUNuQyxJQUFJSCxTQUFTLEtBQUtaLFVBQVUsQ0FBQ1EsT0FBTyxDQUFDTixNQUFNLElBQUlZLFFBQVEsS0FBS2QsVUFBVSxDQUFDUSxPQUFPLENBQUNMLEtBQUssRUFBRTtRQUNwRkgsVUFBVSxDQUFDUSxPQUFPLENBQUNOLE1BQU0sR0FBR1UsU0FBUztRQUNyQ1osVUFBVSxDQUFDUSxPQUFPLENBQUNMLEtBQUssR0FBR1csUUFBUTtRQUNuQzVDLG1CQUFtQixDQUFDO1VBQ2xCdkIsV0FBVyxFQUFFO1lBQ1hxRSxXQUFXLEVBQUU7Y0FDWGQsTUFBTSxFQUFFRixVQUFVLENBQUNRLE9BQU8sQ0FBQ04sTUFBTTtjQUNqQ0MsS0FBSyxFQUFFSCxVQUFVLENBQUNRLE9BQU8sQ0FBQ0w7WUFDNUI7VUFDRjtRQUNGLENBQUMsQ0FBQztNQUNKO0lBQ0Y7RUFDRixDQUFDLEVBQUUsQ0FBQ3JDLFNBQVMsRUFBRUksbUJBQW1CLENBQUMsQ0FBQztFQUNwQyxJQUFJK0MsYUFBYSxHQUFHM0gsS0FBSyxDQUFDNEgsT0FBTyxDQUFDO0lBQUEsT0FBTSxVQUFBUCxRQUFRLEVBQUk7TUFJbEQsSUFBSUEsUUFBUSxJQUFJLElBQUksRUFBRTtRQUNwQkEsUUFBUSxDQUFDUSxLQUFLLEdBQUcsWUFBWTtVQUMzQixJQUFJUixRQUFRLElBQUksSUFBSSxFQUFFO1lBQ3BCQSxRQUFRLENBQUNyRSxLQUFLLEdBQUcsRUFBRTtVQUNyQjtRQUNGLENBQUM7UUFDRHFFLFFBQVEsQ0FBQ1MsU0FBUyxHQUFHLFlBQVk7VUFDL0IsT0FBT1QsUUFBUSxJQUFJLElBQUksSUFBSTFHLGVBQWUsQ0FBQ2YsT0FBTyxDQUFDbUkscUJBQXFCLENBQUMsQ0FBQyxLQUFLVixRQUFRO1FBQ3pGLENBQUM7UUFDREYsdUJBQXVCLENBQUNFLFFBQVEsQ0FBQztNQUNuQztJQUNGLENBQUM7RUFBQSxHQUFFLENBQUNGLHVCQUF1QixDQUFDLENBQUM7RUFDN0IsU0FBU2EsVUFBVUEsQ0FBQzNHLENBQUMsRUFBRTtJQUNyQlYsZUFBZSxDQUFDZixPQUFPLENBQUNxSSxxQkFBcUIsR0FBRyxJQUFJO0lBQ3BELElBQUl2RCxNQUFNLEVBQUU7TUFDVnJELENBQUMsQ0FBQ2dDLFdBQVcsQ0FBQzZFLElBQUksR0FBRzdHLENBQUMsQ0FBQzhHLE1BQU0sQ0FBQ25GLEtBQUs7TUFDbkMwQixNQUFNLENBQUNyRCxDQUFDLENBQUM7SUFDWDtFQUNGO0VBQ0EsU0FBUytHLFlBQVlBLENBQUMvRyxDQUFDLEVBQUU7SUFDdkIsSUFBSWdHLFFBQVEsR0FBR2hHLENBQUMsQ0FBQzhHLE1BQU07SUFDdkIsSUFBSUQsSUFBSSxHQUFHYixRQUFRLENBQUNyRSxLQUFLO0lBQ3pCM0IsQ0FBQyxDQUFDZ0MsV0FBVyxDQUFDNkUsSUFBSSxHQUFHQSxJQUFJO0lBQ3pCZix1QkFBdUIsQ0FBQ0UsUUFBUSxDQUFDO0lBQ2pDLElBQUk1RSxRQUFRLEVBQUU7TUFDWkEsUUFBUSxDQUFDcEIsQ0FBQyxDQUFDO0lBQ2I7SUFDQSxJQUFJc0QsWUFBWSxFQUFFO01BQ2hCQSxZQUFZLENBQUN1RCxJQUFJLENBQUM7SUFDcEI7RUFDRjtFQUNBLFNBQVNHLFdBQVdBLENBQUNoSCxDQUFDLEVBQUU7SUFDdEIsSUFBSWdHLFFBQVEsR0FBR2hHLENBQUMsQ0FBQzhHLE1BQU07SUFDdkIsSUFBSXRELE9BQU8sRUFBRTtNQUNYeEQsQ0FBQyxDQUFDZ0MsV0FBVyxDQUFDNkUsSUFBSSxHQUFHYixRQUFRLENBQUNyRSxLQUFLO01BQ25DNkIsT0FBTyxDQUFDeEQsQ0FBQyxDQUFDO0lBQ1o7SUFDQSxJQUFJZ0csUUFBUSxJQUFJLElBQUksRUFBRTtNQUNwQjFHLGVBQWUsQ0FBQ2YsT0FBTyxDQUFDcUkscUJBQXFCLEdBQUdaLFFBQVE7TUFDeEQsSUFBSXBELGdCQUFnQixFQUFFO1FBQ3BCb0QsUUFBUSxDQUFDckUsS0FBSyxHQUFHLEVBQUU7TUFDckI7TUFDQSxJQUFJdUQsaUJBQWlCLEVBQUU7UUFFckIsSUFBSS9DLFlBQVksSUFBSSxJQUFJLEVBQUU7VUFDeEI4RSxZQUFZLENBQUM5RSxZQUFZLENBQUM7UUFDNUI7UUFDQUEsWUFBWSxHQUFHK0UsVUFBVSxDQUFDLFlBQU07VUFHOUIsSUFBSWxCLFFBQVEsSUFBSSxJQUFJLElBQUltQixRQUFRLENBQUNDLGFBQWEsS0FBS3BCLFFBQVEsRUFBRTtZQUMzREEsUUFBUSxDQUFDcUIsTUFBTSxDQUFDLENBQUM7VUFDbkI7UUFDRixDQUFDLEVBQUUsQ0FBQyxDQUFDO01BQ1A7SUFDRjtFQUNGO0VBQ0EsU0FBU0MsYUFBYUEsQ0FBQ3RILENBQUMsRUFBRTtJQUN4QixJQUFJZ0csUUFBUSxHQUFHaEcsQ0FBQyxDQUFDOEcsTUFBTTtJQUV2QjlHLENBQUMsQ0FBQ3VILGVBQWUsQ0FBQyxDQUFDO0lBQ25CLElBQUlDLG1CQUFtQixHQUFHLENBQUNyRSxTQUFTO0lBQ3BDLElBQUlzRSxrQkFBa0IsR0FBRy9FLFlBQVksSUFBSSxJQUFJLEdBQUc4RSxtQkFBbUIsR0FBRzlFLFlBQVk7SUFDbEYsSUFBSVYsV0FBVyxHQUFHaEMsQ0FBQyxDQUFDZ0MsV0FBVztJQUMvQixJQUFJQyxXQUFXLEdBQUdGLGdCQUFnQixDQUFDQyxXQUFXLENBQUM7SUFDL0MsSUFBSXlCLFVBQVUsRUFBRTtNQUNkQSxVQUFVLENBQUN6RCxDQUFDLENBQUM7SUFDZjtJQUNBLElBQUlBLENBQUMsQ0FBQzBILEdBQUcsS0FBSyxPQUFPLElBQUksQ0FBQzFILENBQUMsQ0FBQzJILFFBQVEsSUFFcEMsQ0FBQzFGLFdBQVcsSUFBSSxDQUFDakMsQ0FBQyxDQUFDNEgsa0JBQWtCLENBQUMsQ0FBQyxFQUFFO01BQ3ZDLElBQUksQ0FBQ2xGLFlBQVksSUFBSSxDQUFDUyxTQUFTLEtBQUt5QixlQUFlLEVBQUU7UUFFbkQ1RSxDQUFDLENBQUM2SCxjQUFjLENBQUMsQ0FBQztRQUNsQjdGLFdBQVcsQ0FBQzZFLElBQUksR0FBRzdHLENBQUMsQ0FBQzhHLE1BQU0sQ0FBQ25GLEtBQUs7UUFDakNpRCxlQUFlLENBQUM1RSxDQUFDLENBQUM7TUFDcEI7TUFDQSxJQUFJeUgsa0JBQWtCLElBQUl6QixRQUFRLElBQUksSUFBSSxFQUFFO1FBQzFDa0IsVUFBVSxDQUFDO1VBQUEsT0FBTWxCLFFBQVEsQ0FBQzhCLElBQUksQ0FBQyxDQUFDO1FBQUEsR0FBRSxDQUFDLENBQUM7TUFDdEM7SUFDRjtFQUNGO0VBQ0EsU0FBU0MscUJBQXFCQSxDQUFDL0gsQ0FBQyxFQUFFO0lBQ2hDLElBQUk7TUFDRixJQUFJZ0ksU0FBUyxHQUFHaEksQ0FBQyxDQUFDOEcsTUFBTTtRQUN0Qm5ILGNBQWMsR0FBR3FJLFNBQVMsQ0FBQ3JJLGNBQWM7UUFDekNELFlBQVksR0FBR3NJLFNBQVMsQ0FBQ3RJLFlBQVk7TUFDdkMsSUFBSXVJLFVBQVUsR0FBRztRQUNmckksS0FBSyxFQUFFRCxjQUFjO1FBQ3JCRSxHQUFHLEVBQUVIO01BQ1AsQ0FBQztNQUNELElBQUk2RSxpQkFBaUIsRUFBRTtRQUNyQnZFLENBQUMsQ0FBQ2dDLFdBQVcsQ0FBQ3ZDLFNBQVMsR0FBR3dJLFVBQVU7UUFDcENqSSxDQUFDLENBQUNnQyxXQUFXLENBQUM2RSxJQUFJLEdBQUc3RyxDQUFDLENBQUM4RyxNQUFNLENBQUNuRixLQUFLO1FBQ25DNEMsaUJBQWlCLENBQUN2RSxDQUFDLENBQUM7TUFDdEI7TUFDQSxJQUFJMkYsbUJBQW1CLENBQUNFLE9BQU8sS0FBS1osZUFBZSxFQUFFO1FBQ25EUyxhQUFhLENBQUNHLE9BQU8sR0FBR29DLFVBQVU7TUFDcEM7SUFDRixDQUFDLENBQUMsT0FBT2pJLENBQUMsRUFBRSxDQUFDO0VBQ2Y7RUFDQSxDQUFDLENBQUMsRUFBRWhCLGdCQUFnQixDQUFDVCxPQUFPLEVBQUUsWUFBTTtJQUNsQyxJQUFJaUIsSUFBSSxHQUFHaUcsT0FBTyxDQUFDSSxPQUFPO0lBQzFCLElBQUlyRyxJQUFJLElBQUksSUFBSSxJQUFJQyxTQUFTLElBQUksSUFBSSxFQUFFO01BQ3JDSyxZQUFZLENBQUNOLElBQUksRUFBRUMsU0FBUyxDQUFDO0lBQy9CO0lBQ0EsSUFBSTBILFFBQVEsQ0FBQ0MsYUFBYSxLQUFLNUgsSUFBSSxFQUFFO01BQ25DRixlQUFlLENBQUNmLE9BQU8sQ0FBQ3FJLHFCQUFxQixHQUFHcEgsSUFBSTtJQUN0RDtFQUNGLENBQUMsRUFBRSxDQUFDaUcsT0FBTyxFQUFFaEcsU0FBUyxDQUFDLENBQUM7RUFDeEIsSUFBSXlJLFNBQVMsR0FBRy9FLFNBQVMsR0FBRyxVQUFVLEdBQUcsT0FBTztFQUNoRCxDQUFDLENBQUMsRUFBRXBFLGlCQUFpQixDQUFDUixPQUFPLEVBQUVrSCxPQUFPLEVBQUUvQixRQUFRLENBQUM7RUFDakQsQ0FBQyxDQUFDLEVBQUV2RSxtQkFBbUIsQ0FBQ1osT0FBTyxFQUFFa0gsT0FBTyxFQUFFO0lBQ3hDOUIsd0JBQXdCLEVBQXhCQSx3QkFBd0I7SUFDeEJDLCtCQUErQixFQUEvQkEsK0JBQStCO0lBQy9CQyxjQUFjLEVBQWRBLGNBQWM7SUFDZEMsZ0JBQWdCLEVBQWhCQSxnQkFBZ0I7SUFDaEJDLGVBQWUsRUFBZkEsZUFBZTtJQUNmQyxpQkFBaUIsRUFBakJBLGlCQUFpQjtJQUNqQkMsa0JBQWtCLEVBQWxCQSxrQkFBa0I7SUFDbEJDLGdCQUFnQixFQUFoQkEsZ0JBQWdCO0lBQ2hCQyxvQkFBb0IsRUFBcEJBLG9CQUFvQjtJQUNwQkMsNkJBQTZCLEVBQTdCQSw2QkFBNkI7SUFDN0JDLDBCQUEwQixFQUExQkEsMEJBQTBCO0lBQzFCQyxpQ0FBaUMsRUFBakNBLGlDQUFpQztJQUNqQ0UsbUNBQW1DLEVBQW5DQSxtQ0FBbUM7SUFDbkNDLDBDQUEwQyxFQUExQ0EsMENBQTBDO0lBQzFDQyx5QkFBeUIsRUFBekJBLHlCQUF5QjtJQUN6QkMsZ0NBQWdDLEVBQWhDQTtFQUNGLENBQUMsQ0FBQztFQUNGLElBQUl3RCxpQkFBaUIsR0FBRyxDQUFDLENBQUMsRUFBRS9JLFVBQVUsQ0FBQ2dKLGdCQUFnQixFQUFFLENBQUM7SUFDeERDLGdCQUFnQixHQUFHRixpQkFBaUIsQ0FBQ0csU0FBUztFQUNoRCxJQUFJQyxjQUFjLEdBQUcxRyxTQUFTLENBQUNDLEtBQUssQ0FBQztFQUNyQ3lHLGNBQWMsQ0FBQzNILGNBQWMsR0FBR0EsY0FBYztFQUM5QzJILGNBQWMsQ0FBQzFILFlBQVksR0FBR0EsWUFBWSxJQUFJMkIsZ0JBQWdCLElBQUksSUFBSTtFQUN0RStGLGNBQWMsQ0FBQ3pILFdBQVcsR0FBR0EsV0FBVyxHQUFHLElBQUksR0FBRyxLQUFLO0VBRXZEeUgsY0FBYyxDQUFDMUYsR0FBRyxHQUFHQSxHQUFHLEtBQUsyRixTQUFTLEdBQUczRixHQUFHLEdBQUcsTUFBTTtFQU1yRDBGLGNBQWMsQ0FBQ3hGLFlBQVksR0FBR0EsWUFBWSxJQUFJZ0MsYUFBYTtFQUMzRHdELGNBQWMsQ0FBQ3ZGLFNBQVMsR0FBR29DLFVBQVU7RUFDckNtRCxjQUFjLENBQUNsRixNQUFNLEdBQUdzRCxVQUFVO0VBQ2xDNEIsY0FBYyxDQUFDbkgsUUFBUSxHQUFHMkYsWUFBWTtFQUN0Q3dCLGNBQWMsQ0FBQy9FLE9BQU8sR0FBR3dELFdBQVc7RUFDcEN1QixjQUFjLENBQUNFLFNBQVMsR0FBR25CLGFBQWE7RUFDeENpQixjQUFjLENBQUNHLFFBQVEsR0FBR1gscUJBQXFCO0VBTS9DUSxjQUFjLENBQUMvRyxRQUFRLEdBQUdBLFFBQVEsS0FBSyxJQUFJLElBQUlzQixRQUFRLEtBQUssS0FBSztFQVNqRXlGLGNBQWMsQ0FBQzlHLElBQUksR0FBRzBCLFNBQVMsR0FBRzFCLElBQUksSUFBSSxJQUFJLEdBQUdBLElBQUksR0FBRzJCLGFBQWEsR0FBRyxDQUFDO0VBQ3pFbUYsY0FBYyxDQUFDN0csVUFBVSxHQUFHQSxVQUFVLElBQUksSUFBSSxHQUFHQSxVQUFVLEdBQUdaLFdBQVc7RUFDekV5SCxjQUFjLENBQUNJLEtBQUssR0FBRyxDQUFDO0lBQ3RCLHdCQUF3QixFQUFFOUQ7RUFDNUIsQ0FBQyxFQUFFK0QsTUFBTSxDQUFDQyxhQUFhLEVBQUVELE1BQU0sQ0FBQ3RILFdBQVcsRUFBRVEsS0FBSyxDQUFDNkcsS0FBSyxFQUFFaEcsV0FBVyxJQUFJaUcsTUFBTSxDQUFDakcsV0FBVyxDQUFDO0VBQzVGNEYsY0FBYyxDQUFDM0csSUFBSSxHQUFHdUIsU0FBUyxHQUFHcUYsU0FBUyxHQUFHNUcsSUFBSTtFQUNsRDJHLGNBQWMsQ0FBQ08scUJBQXFCLEdBQUczRCxvQkFBb0IsS0FBSyxLQUFLLEdBQUcsUUFBUSxHQUFHLE1BQU07RUFDekYsSUFBSTRELGtCQUFrQixHQUFHLENBQUMsQ0FBQyxFQUFFN0osbUJBQW1CLENBQUNYLE9BQU8sRUFBRWdLLGNBQWMsQ0FBQztFQUN6RSxJQUFJUyxNQUFNLEdBQUcsQ0FBQyxDQUFDLEVBQUUvSixhQUFhLENBQUNWLE9BQU8sRUFBRWtILE9BQU8sRUFBRXNELGtCQUFrQixFQUFFekMsYUFBYSxFQUFFaEUsWUFBWSxDQUFDO0VBQ2pHaUcsY0FBYyxDQUFDVSxHQUFHLEdBQUdELE1BQU07RUFDM0IsSUFBSUUsYUFBYSxHQUFHcEgsS0FBSyxDQUFDWixJQUFJLElBQUksSUFBSSxHQUFHLENBQUMsQ0FBQyxFQUFFOUIsVUFBVSxDQUFDK0osa0JBQWtCLEVBQUVySCxLQUFLLENBQUNaLElBQUksQ0FBQyxHQUFHLElBQUk7RUFDOUYsSUFBSWtJLGtCQUFrQixHQUFHdEgsS0FBSyxDQUFDZSxHQUFHLElBQUlxRyxhQUFhO0VBQ25ELElBQUlHLGdCQUFnQixHQUFHRCxrQkFBa0IsSUFBSWYsZ0JBQWdCO0VBQzdELElBQUlpQixPQUFPLEdBQUcsQ0FBQyxDQUFDLEVBQUUxSyxjQUFjLENBQUNMLE9BQU8sRUFBRTJKLFNBQVMsRUFBRUssY0FBYyxFQUFFO0lBQ25FYyxnQkFBZ0IsRUFBaEJBO0VBQ0YsQ0FBQyxDQUFDO0VBQ0YsT0FBT0MsT0FBTztBQUNoQixDQUFDLENBQUM7QUFDRmxILFNBQVMsQ0FBQ21ILFdBQVcsR0FBRyxXQUFXO0FBRW5DbkgsU0FBUyxDQUFDb0gsS0FBSyxHQUFHbEssZUFBZSxDQUFDZixPQUFPO0FBQ3pDLElBQUlxSyxNQUFNLEdBQUd2SixXQUFXLENBQUNkLE9BQU8sQ0FBQ2tMLE1BQU0sQ0FBQztFQUN0Q1osYUFBYSxFQUFFO0lBQ2JhLGFBQWEsRUFBRSxXQUFXO0lBQzFCQyxnQkFBZ0IsRUFBRSxNQUFNO0lBQ3hCQyxlQUFlLEVBQUUsYUFBYTtJQUM5QkMsTUFBTSxFQUFFLGVBQWU7SUFDdkJDLFlBQVksRUFBRSxDQUFDO0lBQ2ZDLFNBQVMsRUFBRSxZQUFZO0lBQ3ZCQyxJQUFJLEVBQUUsYUFBYTtJQUNuQkMsTUFBTSxFQUFFLENBQUM7SUFDVEMsT0FBTyxFQUFFLENBQUM7SUFDVkMsTUFBTSxFQUFFO0VBQ1YsQ0FBQztFQUNEN0ksV0FBVyxFQUFFO0lBQ1h1RCxvQkFBb0IsRUFBRTtFQUN4QixDQUFDO0VBQ0RsQyxXQUFXLEVBQUU7SUFDWHlILFVBQVUsRUFBRTtFQUNkO0FBQ0YsQ0FBQyxDQUFDO0FBQ0YsSUFBSUMsUUFBUSxHQUFHNUwsT0FBTyxDQUFDRixPQUFPLEdBQUc2RCxTQUFTO0FBQzFDa0ksTUFBTSxDQUFDN0wsT0FBTyxHQUFHQSxPQUFPLENBQUNGLE9BQU8iLCJpZ25vcmVMaXN0IjpbXX0=