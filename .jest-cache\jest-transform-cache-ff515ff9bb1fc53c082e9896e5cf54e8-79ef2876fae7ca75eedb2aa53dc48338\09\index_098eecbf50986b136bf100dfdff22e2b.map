{"version": 3, "names": ["_interopRequireDefault", "require", "default", "_interopRequireWildcard", "exports", "__esModule", "_objectWithoutPropertiesLoose2", "React", "_createElement", "forwardedProps", "_pick", "_useElementLayout", "_useMergeRefs", "_usePlatformMethods", "_useResponderEvents", "_StyleSheet", "_TextAncestorContext", "_useLocale", "_excluded", "forwardPropsList", "Object", "assign", "defaultProps", "accessibilityProps", "clickProps", "focusProps", "keyboardProps", "mouseProps", "touchProps", "styleProps", "href", "lang", "onScroll", "onWheel", "pointerEvents", "pickProps", "props", "View", "forwardRef", "forwardedRef", "hrefAttrs", "onLayout", "onMoveShouldSetResponder", "onMoveShouldSetResponderCapture", "onResponderEnd", "onResponderGrant", "onResponderMove", "onResponderReject", "onResponderRelease", "onResponderStart", "onResponderTerminate", "onResponderTerminationRequest", "onScrollShouldSetResponder", "onScrollShouldSetResponderCapture", "onSelectionChangeShouldSetResponder", "onSelectionChangeShouldSetResponderCapture", "onStartShouldSetResponder", "onStartShouldSetResponderCapture", "rest", "process", "env", "NODE_ENV", "Children", "toArray", "children", "for<PERSON>ach", "item", "console", "error", "hasTextAncestor", "useContext", "hostRef", "useRef", "_useLocaleContext", "useLocaleContext", "contextDirection", "direction", "component", "langDirection", "getLocaleDirection", "componentDirection", "dir", "writingDirection", "supportedProps", "style", "styles", "view$raw", "inline", "download", "rel", "target", "char<PERSON>t", "platformMethodsRef", "setRef", "ref", "displayName", "create", "align<PERSON><PERSON><PERSON>", "alignItems", "backgroundColor", "border", "boxSizing", "display", "flexBasis", "flexDirection", "flexShrink", "listStyle", "margin", "minHeight", "min<PERSON><PERSON><PERSON>", "padding", "position", "textDecoration", "zIndex", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nvar _interopRequireWildcard = require(\"@babel/runtime/helpers/interopRequireWildcard\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _createElement = _interopRequireDefault(require(\"../createElement\"));\nvar forwardedProps = _interopRequireWildcard(require(\"../../modules/forwardedProps\"));\nvar _pick = _interopRequireDefault(require(\"../../modules/pick\"));\nvar _useElementLayout = _interopRequireDefault(require(\"../../modules/useElementLayout\"));\nvar _useMergeRefs = _interopRequireDefault(require(\"../../modules/useMergeRefs\"));\nvar _usePlatformMethods = _interopRequireDefault(require(\"../../modules/usePlatformMethods\"));\nvar _useResponderEvents = _interopRequireDefault(require(\"../../modules/useResponderEvents\"));\nvar _StyleSheet = _interopRequireDefault(require(\"../StyleSheet\"));\nvar _TextAncestorContext = _interopRequireDefault(require(\"../Text/TextAncestorContext\"));\nvar _useLocale = require(\"../../modules/useLocale\");\nvar _excluded = [\"hrefAttrs\", \"onLayout\", \"onMoveShouldSetResponder\", \"onMoveShouldSetResponderCapture\", \"onResponderEnd\", \"onResponderGrant\", \"onResponderMove\", \"onResponderReject\", \"onResponderRelease\", \"onResponderStart\", \"onResponderTerminate\", \"onResponderTerminationRequest\", \"onScrollShouldSetResponder\", \"onScrollShouldSetResponderCapture\", \"onSelectionChangeShouldSetResponder\", \"onSelectionChangeShouldSetResponderCapture\", \"onStartShouldSetResponder\", \"onStartShouldSetResponderCapture\"];\nvar forwardPropsList = Object.assign({}, forwardedProps.defaultProps, forwardedProps.accessibilityProps, forwardedProps.clickProps, forwardedProps.focusProps, forwardedProps.keyboardProps, forwardedProps.mouseProps, forwardedProps.touchProps, forwardedProps.styleProps, {\n  href: true,\n  lang: true,\n  onScroll: true,\n  onWheel: true,\n  pointerEvents: true\n});\nvar pickProps = props => (0, _pick.default)(props, forwardPropsList);\nvar View = /*#__PURE__*/React.forwardRef((props, forwardedRef) => {\n  var hrefAttrs = props.hrefAttrs,\n    onLayout = props.onLayout,\n    onMoveShouldSetResponder = props.onMoveShouldSetResponder,\n    onMoveShouldSetResponderCapture = props.onMoveShouldSetResponderCapture,\n    onResponderEnd = props.onResponderEnd,\n    onResponderGrant = props.onResponderGrant,\n    onResponderMove = props.onResponderMove,\n    onResponderReject = props.onResponderReject,\n    onResponderRelease = props.onResponderRelease,\n    onResponderStart = props.onResponderStart,\n    onResponderTerminate = props.onResponderTerminate,\n    onResponderTerminationRequest = props.onResponderTerminationRequest,\n    onScrollShouldSetResponder = props.onScrollShouldSetResponder,\n    onScrollShouldSetResponderCapture = props.onScrollShouldSetResponderCapture,\n    onSelectionChangeShouldSetResponder = props.onSelectionChangeShouldSetResponder,\n    onSelectionChangeShouldSetResponderCapture = props.onSelectionChangeShouldSetResponderCapture,\n    onStartShouldSetResponder = props.onStartShouldSetResponder,\n    onStartShouldSetResponderCapture = props.onStartShouldSetResponderCapture,\n    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);\n  if (process.env.NODE_ENV !== 'production') {\n    React.Children.toArray(props.children).forEach(item => {\n      if (typeof item === 'string') {\n        console.error(\"Unexpected text node: \" + item + \". A text node cannot be a child of a <View>.\");\n      }\n    });\n  }\n  var hasTextAncestor = React.useContext(_TextAncestorContext.default);\n  var hostRef = React.useRef(null);\n  var _useLocaleContext = (0, _useLocale.useLocaleContext)(),\n    contextDirection = _useLocaleContext.direction;\n  (0, _useElementLayout.default)(hostRef, onLayout);\n  (0, _useResponderEvents.default)(hostRef, {\n    onMoveShouldSetResponder,\n    onMoveShouldSetResponderCapture,\n    onResponderEnd,\n    onResponderGrant,\n    onResponderMove,\n    onResponderReject,\n    onResponderRelease,\n    onResponderStart,\n    onResponderTerminate,\n    onResponderTerminationRequest,\n    onScrollShouldSetResponder,\n    onScrollShouldSetResponderCapture,\n    onSelectionChangeShouldSetResponder,\n    onSelectionChangeShouldSetResponderCapture,\n    onStartShouldSetResponder,\n    onStartShouldSetResponderCapture\n  });\n  var component = 'div';\n  var langDirection = props.lang != null ? (0, _useLocale.getLocaleDirection)(props.lang) : null;\n  var componentDirection = props.dir || langDirection;\n  var writingDirection = componentDirection || contextDirection;\n  var supportedProps = pickProps(rest);\n  supportedProps.dir = componentDirection;\n  supportedProps.style = [styles.view$raw, hasTextAncestor && styles.inline, props.style];\n  if (props.href != null) {\n    component = 'a';\n    if (hrefAttrs != null) {\n      var download = hrefAttrs.download,\n        rel = hrefAttrs.rel,\n        target = hrefAttrs.target;\n      if (download != null) {\n        supportedProps.download = download;\n      }\n      if (rel != null) {\n        supportedProps.rel = rel;\n      }\n      if (typeof target === 'string') {\n        supportedProps.target = target.charAt(0) !== '_' ? '_' + target : target;\n      }\n    }\n  }\n  var platformMethodsRef = (0, _usePlatformMethods.default)(supportedProps);\n  var setRef = (0, _useMergeRefs.default)(hostRef, platformMethodsRef, forwardedRef);\n  supportedProps.ref = setRef;\n  return (0, _createElement.default)(component, supportedProps, {\n    writingDirection\n  });\n});\nView.displayName = 'View';\nvar styles = _StyleSheet.default.create({\n  view$raw: {\n    alignContent: 'flex-start',\n    alignItems: 'stretch',\n    backgroundColor: 'transparent',\n    border: '0 solid black',\n    boxSizing: 'border-box',\n    display: 'flex',\n    flexBasis: 'auto',\n    flexDirection: 'column',\n    flexShrink: 0,\n    listStyle: 'none',\n    margin: 0,\n    minHeight: 0,\n    minWidth: 0,\n    padding: 0,\n    position: 'relative',\n    textDecoration: 'none',\n    zIndex: 0\n  },\n  inline: {\n    display: 'inline-flex'\n  }\n});\nvar _default = exports.default = View;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;AAWZ,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5F,IAAIC,uBAAuB,GAAGF,OAAO,CAAC,+CAA+C,CAAC,CAACC,OAAO;AAC9FE,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACF,OAAO,GAAG,KAAK,CAAC;AACxB,IAAII,8BAA8B,GAAGN,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIM,KAAK,GAAGJ,uBAAuB,CAACF,OAAO,CAAC,OAAO,CAAC,CAAC;AACrD,IAAIO,cAAc,GAAGR,sBAAsB,CAACC,OAAO,mBAAmB,CAAC,CAAC;AACxE,IAAIQ,cAAc,GAAGN,uBAAuB,CAACF,OAAO,+BAA+B,CAAC,CAAC;AACrF,IAAIS,KAAK,GAAGV,sBAAsB,CAACC,OAAO,qBAAqB,CAAC,CAAC;AACjE,IAAIU,iBAAiB,GAAGX,sBAAsB,CAACC,OAAO,iCAAiC,CAAC,CAAC;AACzF,IAAIW,aAAa,GAAGZ,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AACjF,IAAIY,mBAAmB,GAAGb,sBAAsB,CAACC,OAAO,mCAAmC,CAAC,CAAC;AAC7F,IAAIa,mBAAmB,GAAGd,sBAAsB,CAACC,OAAO,mCAAmC,CAAC,CAAC;AAC7F,IAAIc,WAAW,GAAGf,sBAAsB,CAACC,OAAO,gBAAgB,CAAC,CAAC;AAClE,IAAIe,oBAAoB,GAAGhB,sBAAsB,CAACC,OAAO,8BAA8B,CAAC,CAAC;AACzF,IAAIgB,UAAU,GAAGhB,OAAO,0BAA0B,CAAC;AACnD,IAAIiB,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,0BAA0B,EAAE,iCAAiC,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,sBAAsB,EAAE,+BAA+B,EAAE,4BAA4B,EAAE,mCAAmC,EAAE,qCAAqC,EAAE,4CAA4C,EAAE,2BAA2B,EAAE,kCAAkC,CAAC;AAClf,IAAIC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEZ,cAAc,CAACa,YAAY,EAAEb,cAAc,CAACc,kBAAkB,EAAEd,cAAc,CAACe,UAAU,EAAEf,cAAc,CAACgB,UAAU,EAAEhB,cAAc,CAACiB,aAAa,EAAEjB,cAAc,CAACkB,UAAU,EAAElB,cAAc,CAACmB,UAAU,EAAEnB,cAAc,CAACoB,UAAU,EAAE;EAC5QC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,OAAO,EAAE,IAAI;EACbC,aAAa,EAAE;AACjB,CAAC,CAAC;AACF,IAAIC,SAAS,GAAG,SAAZA,SAASA,CAAGC,KAAK;EAAA,OAAI,CAAC,CAAC,EAAE1B,KAAK,CAACR,OAAO,EAAEkC,KAAK,EAAEjB,gBAAgB,CAAC;AAAA;AACpE,IAAIkB,IAAI,GAAgB9B,KAAK,CAAC+B,UAAU,CAAC,UAACF,KAAK,EAAEG,YAAY,EAAK;EAChE,IAAIC,SAAS,GAAGJ,KAAK,CAACI,SAAS;IAC7BC,QAAQ,GAAGL,KAAK,CAACK,QAAQ;IACzBC,wBAAwB,GAAGN,KAAK,CAACM,wBAAwB;IACzDC,+BAA+B,GAAGP,KAAK,CAACO,+BAA+B;IACvEC,cAAc,GAAGR,KAAK,CAACQ,cAAc;IACrCC,gBAAgB,GAAGT,KAAK,CAACS,gBAAgB;IACzCC,eAAe,GAAGV,KAAK,CAACU,eAAe;IACvCC,iBAAiB,GAAGX,KAAK,CAACW,iBAAiB;IAC3CC,kBAAkB,GAAGZ,KAAK,CAACY,kBAAkB;IAC7CC,gBAAgB,GAAGb,KAAK,CAACa,gBAAgB;IACzCC,oBAAoB,GAAGd,KAAK,CAACc,oBAAoB;IACjDC,6BAA6B,GAAGf,KAAK,CAACe,6BAA6B;IACnEC,0BAA0B,GAAGhB,KAAK,CAACgB,0BAA0B;IAC7DC,iCAAiC,GAAGjB,KAAK,CAACiB,iCAAiC;IAC3EC,mCAAmC,GAAGlB,KAAK,CAACkB,mCAAmC;IAC/EC,0CAA0C,GAAGnB,KAAK,CAACmB,0CAA0C;IAC7FC,yBAAyB,GAAGpB,KAAK,CAACoB,yBAAyB;IAC3DC,gCAAgC,GAAGrB,KAAK,CAACqB,gCAAgC;IACzEC,IAAI,GAAG,CAAC,CAAC,EAAEpD,8BAA8B,CAACJ,OAAO,EAAEkC,KAAK,EAAElB,SAAS,CAAC;EACtE,IAAIyC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCtD,KAAK,CAACuD,QAAQ,CAACC,OAAO,CAAC3B,KAAK,CAAC4B,QAAQ,CAAC,CAACC,OAAO,CAAC,UAAAC,IAAI,EAAI;MACrD,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;QAC5BC,OAAO,CAACC,KAAK,CAAC,wBAAwB,GAAGF,IAAI,GAAG,8CAA8C,CAAC;MACjG;IACF,CAAC,CAAC;EACJ;EACA,IAAIG,eAAe,GAAG9D,KAAK,CAAC+D,UAAU,CAACtD,oBAAoB,CAACd,OAAO,CAAC;EACpE,IAAIqE,OAAO,GAAGhE,KAAK,CAACiE,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIC,iBAAiB,GAAG,CAAC,CAAC,EAAExD,UAAU,CAACyD,gBAAgB,EAAE,CAAC;IACxDC,gBAAgB,GAAGF,iBAAiB,CAACG,SAAS;EAChD,CAAC,CAAC,EAAEjE,iBAAiB,CAACT,OAAO,EAAEqE,OAAO,EAAE9B,QAAQ,CAAC;EACjD,CAAC,CAAC,EAAE3B,mBAAmB,CAACZ,OAAO,EAAEqE,OAAO,EAAE;IACxC7B,wBAAwB,EAAxBA,wBAAwB;IACxBC,+BAA+B,EAA/BA,+BAA+B;IAC/BC,cAAc,EAAdA,cAAc;IACdC,gBAAgB,EAAhBA,gBAAgB;IAChBC,eAAe,EAAfA,eAAe;IACfC,iBAAiB,EAAjBA,iBAAiB;IACjBC,kBAAkB,EAAlBA,kBAAkB;IAClBC,gBAAgB,EAAhBA,gBAAgB;IAChBC,oBAAoB,EAApBA,oBAAoB;IACpBC,6BAA6B,EAA7BA,6BAA6B;IAC7BC,0BAA0B,EAA1BA,0BAA0B;IAC1BC,iCAAiC,EAAjCA,iCAAiC;IACjCC,mCAAmC,EAAnCA,mCAAmC;IACnCC,0CAA0C,EAA1CA,0CAA0C;IAC1CC,yBAAyB,EAAzBA,yBAAyB;IACzBC,gCAAgC,EAAhCA;EACF,CAAC,CAAC;EACF,IAAIoB,SAAS,GAAG,KAAK;EACrB,IAAIC,aAAa,GAAG1C,KAAK,CAACL,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,EAAEd,UAAU,CAAC8D,kBAAkB,EAAE3C,KAAK,CAACL,IAAI,CAAC,GAAG,IAAI;EAC9F,IAAIiD,kBAAkB,GAAG5C,KAAK,CAAC6C,GAAG,IAAIH,aAAa;EACnD,IAAII,gBAAgB,GAAGF,kBAAkB,IAAIL,gBAAgB;EAC7D,IAAIQ,cAAc,GAAGhD,SAAS,CAACuB,IAAI,CAAC;EACpCyB,cAAc,CAACF,GAAG,GAAGD,kBAAkB;EACvCG,cAAc,CAACC,KAAK,GAAG,CAACC,MAAM,CAACC,QAAQ,EAAEjB,eAAe,IAAIgB,MAAM,CAACE,MAAM,EAAEnD,KAAK,CAACgD,KAAK,CAAC;EACvF,IAAIhD,KAAK,CAACN,IAAI,IAAI,IAAI,EAAE;IACtB+C,SAAS,GAAG,GAAG;IACf,IAAIrC,SAAS,IAAI,IAAI,EAAE;MACrB,IAAIgD,QAAQ,GAAGhD,SAAS,CAACgD,QAAQ;QAC/BC,GAAG,GAAGjD,SAAS,CAACiD,GAAG;QACnBC,MAAM,GAAGlD,SAAS,CAACkD,MAAM;MAC3B,IAAIF,QAAQ,IAAI,IAAI,EAAE;QACpBL,cAAc,CAACK,QAAQ,GAAGA,QAAQ;MACpC;MACA,IAAIC,GAAG,IAAI,IAAI,EAAE;QACfN,cAAc,CAACM,GAAG,GAAGA,GAAG;MAC1B;MACA,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAAE;QAC9BP,cAAc,CAACO,MAAM,GAAGA,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,GAAG,GAAG,GAAGD,MAAM,GAAGA,MAAM;MAC1E;IACF;EACF;EACA,IAAIE,kBAAkB,GAAG,CAAC,CAAC,EAAE/E,mBAAmB,CAACX,OAAO,EAAEiF,cAAc,CAAC;EACzE,IAAIU,MAAM,GAAG,CAAC,CAAC,EAAEjF,aAAa,CAACV,OAAO,EAAEqE,OAAO,EAAEqB,kBAAkB,EAAErD,YAAY,CAAC;EAClF4C,cAAc,CAACW,GAAG,GAAGD,MAAM;EAC3B,OAAO,CAAC,CAAC,EAAErF,cAAc,CAACN,OAAO,EAAE2E,SAAS,EAAEM,cAAc,EAAE;IAC5DD,gBAAgB,EAAhBA;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AACF7C,IAAI,CAAC0D,WAAW,GAAG,MAAM;AACzB,IAAIV,MAAM,GAAGtE,WAAW,CAACb,OAAO,CAAC8F,MAAM,CAAC;EACtCV,QAAQ,EAAE;IACRW,YAAY,EAAE,YAAY;IAC1BC,UAAU,EAAE,SAAS;IACrBC,eAAe,EAAE,aAAa;IAC9BC,MAAM,EAAE,eAAe;IACvBC,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,aAAa,EAAE,QAAQ;IACvBC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE,MAAM;IACjBC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAC;IACXC,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,UAAU;IACpBC,cAAc,EAAE,MAAM;IACtBC,MAAM,EAAE;EACV,CAAC;EACD1B,MAAM,EAAE;IACNe,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AACF,IAAIY,QAAQ,GAAG9G,OAAO,CAACF,OAAO,GAAGmC,IAAI;AACrC8E,MAAM,CAAC/G,OAAO,GAAGA,OAAO,CAACF,OAAO", "ignoreList": []}