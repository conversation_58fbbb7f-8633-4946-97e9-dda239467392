fdc5b00d0edf122f8746a99618e8f015
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _NativeModulesProxy = _interopRequireDefault(require("../NativeModulesProxy"));
var _default = exports.default = _NativeModulesProxy.default.ExpoModulesCoreErrorManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfTmF0aXZlTW9kdWxlc1Byb3h5IiwiX2ludGVyb3BSZXF1aXJlRGVmYXVsdCIsInJlcXVpcmUiLCJfZGVmYXVsdCIsImV4cG9ydHMiLCJkZWZhdWx0IiwiTmF0aXZlTW9kdWxlc1Byb3h5IiwiRXhwb01vZHVsZXNDb3JlRXJyb3JNYW5hZ2VyIl0sInNvdXJjZXMiOlsiTmF0aXZlRXJyb3JNYW5hZ2VyLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOYXRpdmVNb2R1bGVzUHJveHkgZnJvbSAnLi4vTmF0aXZlTW9kdWxlc1Byb3h5JztcbmV4cG9ydCBkZWZhdWx0IE5hdGl2ZU1vZHVsZXNQcm94eS5FeHBvTW9kdWxlc0NvcmVFcnJvck1hbmFnZXI7XG4iXSwibWFwcGluZ3MiOiI7Ozs7O0FBQUEsSUFBQUEsbUJBQUEsR0FBQUMsc0JBQUEsQ0FBQUMsT0FBQTtBQUF1RCxJQUFBQyxRQUFBLEdBQUFDLE9BQUEsQ0FBQUMsT0FBQSxHQUN4Q0MsMkJBQWtCLENBQUNDLDJCQUEyQiIsImlnbm9yZUxpc3QiOltdfQ==