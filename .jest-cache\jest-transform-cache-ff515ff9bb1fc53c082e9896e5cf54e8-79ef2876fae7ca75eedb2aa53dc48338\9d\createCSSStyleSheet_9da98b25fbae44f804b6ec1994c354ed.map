{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "createCSSStyleSheet", "_canUseDom", "id", "rootNode", "textContent", "root", "document", "element", "getElementById", "createElement", "setAttribute", "append<PERSON><PERSON><PERSON>", "createTextNode", "ShadowRoot", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "head", "sheet", "module"], "sources": ["createCSSStyleSheet.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = createCSSStyleSheet;\nvar _canUseDom = _interopRequireDefault(require(\"../../../modules/canUseDom\"));\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n// $FlowFixMe: HTMLStyleElement is incorrectly typed - https://github.com/facebook/flow/issues/2696\nfunction createCSSStyleSheet(id, rootNode, textContent) {\n  if (_canUseDom.default) {\n    var root = rootNode != null ? rootNode : document;\n    var element = root.getElementById(id);\n    if (element == null) {\n      element = document.createElement('style');\n      element.setAttribute('id', id);\n      if (typeof textContent === 'string') {\n        element.appendChild(document.createTextNode(textContent));\n      }\n      if (root instanceof ShadowRoot) {\n        root.insertBefore(element, root.firstChild);\n      } else {\n        var head = root.head;\n        if (head) {\n          head.insertBefore(element, head.firstChild);\n        }\n      }\n    }\n    // $FlowFixMe: HTMLElement is incorrectly typed\n    return element.sheet;\n  } else {\n    return null;\n  }\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,mBAAmB;AACrC,IAAIC,UAAU,GAAGN,sBAAsB,CAACC,OAAO,6BAA6B,CAAC,CAAC;AAW9E,SAASI,mBAAmBA,CAACE,EAAE,EAAEC,QAAQ,EAAEC,WAAW,EAAE;EACtD,IAAIH,UAAU,CAACJ,OAAO,EAAE;IACtB,IAAIQ,IAAI,GAAGF,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGG,QAAQ;IACjD,IAAIC,OAAO,GAAGF,IAAI,CAACG,cAAc,CAACN,EAAE,CAAC;IACrC,IAAIK,OAAO,IAAI,IAAI,EAAE;MACnBA,OAAO,GAAGD,QAAQ,CAACG,aAAa,CAAC,OAAO,CAAC;MACzCF,OAAO,CAACG,YAAY,CAAC,IAAI,EAAER,EAAE,CAAC;MAC9B,IAAI,OAAOE,WAAW,KAAK,QAAQ,EAAE;QACnCG,OAAO,CAACI,WAAW,CAACL,QAAQ,CAACM,cAAc,CAACR,WAAW,CAAC,CAAC;MAC3D;MACA,IAAIC,IAAI,YAAYQ,UAAU,EAAE;QAC9BR,IAAI,CAACS,YAAY,CAACP,OAAO,EAAEF,IAAI,CAACU,UAAU,CAAC;MAC7C,CAAC,MAAM;QACL,IAAIC,IAAI,GAAGX,IAAI,CAACW,IAAI;QACpB,IAAIA,IAAI,EAAE;UACRA,IAAI,CAACF,YAAY,CAACP,OAAO,EAAES,IAAI,CAACD,UAAU,CAAC;QAC7C;MACF;IACF;IAEA,OAAOR,OAAO,CAACU,KAAK;EACtB,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF;AACAC,MAAM,CAACpB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}