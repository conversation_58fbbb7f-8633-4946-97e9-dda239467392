{"version": 3, "names": [], "sources": ["NativeModulesProxy.ts"], "sourcesContent": ["import type { ProxyNativeModule } from './NativeModulesProxy.types';\n\n// We default to an empty object shim wherever we don't have an environment-specific implementation\n\n/**\n * @deprecated `NativeModulesProxy` is deprecated and might be removed in the future releases.\n * Use `requireNativeModule` or `requireOptionalNativeModule` instead.\n */\nexport default {} as Record<string, ProxyNativeModule>;\n"], "mappings": ";;;;iCAQe,CAAC,CAAC", "ignoreList": []}