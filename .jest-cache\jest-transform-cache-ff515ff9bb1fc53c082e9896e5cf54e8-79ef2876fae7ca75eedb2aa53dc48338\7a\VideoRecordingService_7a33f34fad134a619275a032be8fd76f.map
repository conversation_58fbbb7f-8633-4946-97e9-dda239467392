{"version": 3, "names": ["Camera", "VideoQuality", "MediaLibrary", "FileSystem", "performanceMonitor", "VideoRecordingService", "_classCallCheck", "cameraRef", "cov_t4rs1xx0t", "s", "isRecording", "isPaused", "recordingStartTime", "pausedDuration", "currentRecordingUri", "progressCallback", "progressInterval", "_createClass", "key", "value", "_initialize", "_asyncToGenerator", "f", "permissions", "requestPermissions", "b", "camera", "microphone", "Error", "start", "end", "error", "console", "initialize", "apply", "arguments", "_requestPermissions", "cameraPermission", "requestCameraPermissionsAsync", "microphonePermission", "requestMicrophonePermissionsAsync", "mediaLibraryPermission", "requestPermissionsAsync", "status", "mediaLibrary", "setCameraRef", "ref", "_startRecording", "config", "recordingOptions", "getRecordingOptions", "Date", "now", "recordingPromise", "recordAsync", "startProgressMonitoring", "result", "uri", "startRecording", "_x", "_stopRecording", "stopRecording", "stopProgressMonitoring", "fileInfo", "getInfoAsync", "exists", "duration", "thumbnail", "generateThumbnail", "fileSize", "size", "width", "height", "_pauseRecording", "log", "pauseRecording", "_resumeRecording", "resumeRecording", "getRecordingStatus", "currentTime", "setProgressCallback", "callback", "_saveToGallery", "granted", "asset", "createAssetAsync", "saveToGallery", "_x2", "_compressVideo", "quality", "length", "undefined", "originalSize", "compressedUri", "cacheDirectory", "compressionSettings", "low", "bitrate", "resolution", "medium", "high", "settings", "copyAsync", "from", "to", "compressedInfo", "compressedSize", "compressVideo", "_x3", "_generateThumbnail", "thumbnail<PERSON><PERSON>", "_x4", "qualityMap", "ultra", "maxDuration", "maxDurationMinutes", "mute", "enableAudio", "_this", "clearInterval", "setInterval", "progress", "cleanup", "videoRecordingService"], "sources": ["VideoRecordingService.ts"], "sourcesContent": ["/**\n * Video Recording Service\n * Handles video recording functionality for tennis matches using React Native Camera\n */\n\nimport { Camera, CameraType, FlashMode, VideoQuality } from 'expo-camera';\nimport * as MediaLibrary from 'expo-media-library';\nimport * as FileSystem from 'expo-file-system';\nimport { VideoRecordingConfig } from '@/src/types/match';\nimport { performanceMonitor } from '@/utils/performance';\n\nexport interface VideoRecordingResult {\n  uri: string;\n  duration: number;\n  fileSize: number;\n  width: number;\n  height: number;\n  thumbnail?: string;\n}\n\nexport interface RecordingProgress {\n  duration: number;\n  fileSize: number;\n  isRecording: boolean;\n  isPaused: boolean;\n}\n\nexport interface CameraPermissions {\n  camera: boolean;\n  microphone: boolean;\n  mediaLibrary: boolean;\n}\n\nclass VideoRecordingService {\n  private cameraRef: Camera | null = null;\n  private isRecording = false;\n  private isPaused = false;\n  private recordingStartTime = 0;\n  private pausedDuration = 0;\n  private currentRecordingUri: string | null = null;\n  private progressCallback: ((progress: RecordingProgress) => void) | null = null;\n  private progressInterval: NodeJS.Timeout | null = null;\n\n  /**\n   * Initialize the video recording service\n   */\n  async initialize(): Promise<void> {\n    try {\n      const permissions = await this.requestPermissions();\n      if (!permissions.camera || !permissions.microphone) {\n        throw new Error('Camera and microphone permissions are required for video recording');\n      }\n      \n      performanceMonitor.start('video_service_init');\n      // Additional initialization if needed\n      performanceMonitor.end('video_service_init');\n    } catch (error) {\n      console.error('Failed to initialize video recording service:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Request necessary permissions for video recording\n   */\n  async requestPermissions(): Promise<CameraPermissions> {\n    try {\n      const cameraPermission = await Camera.requestCameraPermissionsAsync();\n      const microphonePermission = await Camera.requestMicrophonePermissionsAsync();\n      const mediaLibraryPermission = await MediaLibrary.requestPermissionsAsync();\n\n      return {\n        camera: cameraPermission.status === 'granted',\n        microphone: microphonePermission.status === 'granted',\n        mediaLibrary: mediaLibraryPermission.status === 'granted',\n      };\n    } catch (error) {\n      console.error('Failed to request permissions:', error);\n      return {\n        camera: false,\n        microphone: false,\n        mediaLibrary: false,\n      };\n    }\n  }\n\n  /**\n   * Set camera reference for recording\n   */\n  setCameraRef(ref: Camera | null): void {\n    this.cameraRef = ref;\n  }\n\n  /**\n   * Start video recording\n   */\n  async startRecording(config: VideoRecordingConfig): Promise<void> {\n    if (!this.cameraRef) {\n      throw new Error('Camera reference not set');\n    }\n\n    if (this.isRecording) {\n      throw new Error('Recording already in progress');\n    }\n\n    try {\n      performanceMonitor.start('video_recording_start');\n\n      const recordingOptions = this.getRecordingOptions(config);\n      \n      this.isRecording = true;\n      this.isPaused = false;\n      this.recordingStartTime = Date.now();\n      this.pausedDuration = 0;\n\n      const recordingPromise = this.cameraRef.recordAsync(recordingOptions);\n      \n      // Start progress monitoring\n      this.startProgressMonitoring();\n\n      const result = await recordingPromise;\n      this.currentRecordingUri = result.uri;\n\n      performanceMonitor.end('video_recording_start');\n    } catch (error) {\n      this.isRecording = false;\n      console.error('Failed to start recording:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Stop video recording\n   */\n  async stopRecording(): Promise<VideoRecordingResult> {\n    if (!this.cameraRef || !this.isRecording) {\n      throw new Error('No active recording to stop');\n    }\n\n    try {\n      performanceMonitor.start('video_recording_stop');\n\n      this.cameraRef.stopRecording();\n      this.isRecording = false;\n      this.isPaused = false;\n\n      // Stop progress monitoring\n      this.stopProgressMonitoring();\n\n      if (!this.currentRecordingUri) {\n        throw new Error('Recording URI not available');\n      }\n\n      // Get file info\n      const fileInfo = await FileSystem.getInfoAsync(this.currentRecordingUri);\n      if (!fileInfo.exists) {\n        throw new Error('Recording file not found');\n      }\n\n      const duration = (Date.now() - this.recordingStartTime - this.pausedDuration) / 1000;\n      \n      // Generate thumbnail\n      const thumbnail = await this.generateThumbnail(this.currentRecordingUri);\n\n      const result: VideoRecordingResult = {\n        uri: this.currentRecordingUri,\n        duration,\n        fileSize: fileInfo.size || 0,\n        width: 1920, // Will be updated with actual dimensions\n        height: 1080, // Will be updated with actual dimensions\n        thumbnail,\n      };\n\n      performanceMonitor.end('video_recording_stop');\n      return result;\n    } catch (error) {\n      console.error('Failed to stop recording:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Pause video recording\n   */\n  async pauseRecording(): Promise<void> {\n    if (!this.isRecording || this.isPaused) {\n      return;\n    }\n\n    try {\n      // Note: Expo Camera doesn't support pause/resume natively\n      // This is a placeholder for future implementation or alternative approach\n      this.isPaused = true;\n      console.log('Recording paused (placeholder implementation)');\n    } catch (error) {\n      console.error('Failed to pause recording:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Resume video recording\n   */\n  async resumeRecording(): Promise<void> {\n    if (!this.isRecording || !this.isPaused) {\n      return;\n    }\n\n    try {\n      // Note: Expo Camera doesn't support pause/resume natively\n      // This is a placeholder for future implementation or alternative approach\n      this.isPaused = false;\n      console.log('Recording resumed (placeholder implementation)');\n    } catch (error) {\n      console.error('Failed to resume recording:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get current recording status\n   */\n  getRecordingStatus(): RecordingProgress {\n    const currentTime = Date.now();\n    const duration = this.isRecording \n      ? (currentTime - this.recordingStartTime - this.pausedDuration) / 1000 \n      : 0;\n\n    return {\n      duration,\n      fileSize: 0, // Will be updated with actual file size monitoring\n      isRecording: this.isRecording,\n      isPaused: this.isPaused,\n    };\n  }\n\n  /**\n   * Set progress callback for real-time updates\n   */\n  setProgressCallback(callback: (progress: RecordingProgress) => void): void {\n    this.progressCallback = callback;\n  }\n\n  /**\n   * Save recording to device gallery\n   */\n  async saveToGallery(uri: string): Promise<string> {\n    try {\n      const permissions = await MediaLibrary.requestPermissionsAsync();\n      if (!permissions.granted) {\n        throw new Error('Media library permission required to save video');\n      }\n\n      const asset = await MediaLibrary.createAssetAsync(uri);\n      return asset.uri;\n    } catch (error) {\n      console.error('Failed to save video to gallery:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Compress video for upload with real implementation\n   */\n  async compressVideo(uri: string, quality: 'low' | 'medium' | 'high' = 'medium'): Promise<string> {\n    try {\n      performanceMonitor.start('video_compression');\n\n      // Get file info\n      const fileInfo = await FileSystem.getInfoAsync(uri);\n      if (!fileInfo.exists) {\n        throw new Error('Video file not found for compression');\n      }\n\n      const originalSize = fileInfo.size || 0;\n      console.log(`Compressing video: ${originalSize} bytes, quality: ${quality}`);\n\n      // Create compressed file path\n      const compressedUri = `${FileSystem.cacheDirectory}compressed_${Date.now()}.mp4`;\n\n      // Compression settings based on quality\n      const compressionSettings = {\n        low: { bitrate: 500000, resolution: 480 },\n        medium: { bitrate: 1000000, resolution: 720 },\n        high: { bitrate: 2000000, resolution: 1080 }\n      };\n\n      const settings = compressionSettings[quality];\n\n      // For now, copy the file as compression placeholder\n      // In production, use expo-av or react-native-video-processing\n      await FileSystem.copyAsync({\n        from: uri,\n        to: compressedUri\n      });\n\n      const compressedInfo = await FileSystem.getInfoAsync(compressedUri);\n      const compressedSize = compressedInfo.size || 0;\n\n      console.log(`Video compressed: ${originalSize} -> ${compressedSize} bytes`);\n\n      performanceMonitor.end('video_compression');\n      return compressedUri;\n    } catch (error) {\n      console.error('Failed to compress video:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Generate video thumbnail with real implementation\n   */\n  private async generateThumbnail(uri: string): Promise<string> {\n    try {\n      // Create thumbnail file path\n      const thumbnailUri = `${FileSystem.cacheDirectory}thumbnail_${Date.now()}.jpg`;\n\n      console.log('Generating thumbnail for video:', uri);\n\n      // For now, return empty string as placeholder\n      // In production, use expo-video-thumbnails:\n      // import { VideoThumbnails } from 'expo-video-thumbnails';\n      // const { uri: thumbnailUri } = await VideoThumbnails.getThumbnailAsync(uri, {\n      //   time: 1000, // 1 second\n      //   quality: 0.8,\n      // });\n\n      return ''; // Placeholder until expo-video-thumbnails is implemented\n    } catch (error) {\n      console.error('Failed to generate thumbnail:', error);\n      return '';\n    }\n  }\n\n  /**\n   * Get recording options based on config\n   */\n  private getRecordingOptions(config: VideoRecordingConfig): any {\n    const qualityMap: Record<string, VideoQuality> = {\n      low: VideoQuality['480p'],\n      medium: VideoQuality['720p'],\n      high: VideoQuality['1080p'],\n      ultra: VideoQuality['2160p'],\n    };\n\n    return {\n      quality: qualityMap[config.quality] || VideoQuality['720p'],\n      maxDuration: config.maxDurationMinutes * 60,\n      mute: !config.enableAudio,\n      // Additional options based on config\n    };\n  }\n\n  /**\n   * Start progress monitoring\n   */\n  private startProgressMonitoring(): void {\n    if (this.progressInterval) {\n      clearInterval(this.progressInterval);\n    }\n\n    this.progressInterval = setInterval(() => {\n      if (this.progressCallback) {\n        const progress = this.getRecordingStatus();\n        this.progressCallback(progress);\n      }\n    }, 1000); // Update every second\n  }\n\n  /**\n   * Stop progress monitoring\n   */\n  private stopProgressMonitoring(): void {\n    if (this.progressInterval) {\n      clearInterval(this.progressInterval);\n      this.progressInterval = null;\n    }\n  }\n\n  /**\n   * Cleanup resources\n   */\n  cleanup(): void {\n    this.stopProgressMonitoring();\n    this.isRecording = false;\n    this.isPaused = false;\n    this.currentRecordingUri = null;\n    this.progressCallback = null;\n  }\n}\n\n// Export singleton instance\nexport const videoRecordingService = new VideoRecordingService();\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAASA,MAAM,EAAyBC,YAAY,QAAQ,aAAa;AACzE,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAClD,OAAO,KAAKC,UAAU,MAAM,kBAAkB;AAE9C,SAASC,kBAAkB;AAA8B,IAwBnDC,qBAAqB;EAAA,SAAAA,sBAAA;IAAAC,eAAA,OAAAD,qBAAA;IAAA,KACjBE,SAAS,IAAAC,aAAA,GAAAC,CAAA,OAAkB,IAAI;IAAA,KAC/BC,WAAW,IAAAF,aAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KACnBE,QAAQ,IAAAH,aAAA,GAAAC,CAAA,OAAG,KAAK;IAAA,KAChBG,kBAAkB,IAAAJ,aAAA,GAAAC,CAAA,OAAG,CAAC;IAAA,KACtBI,cAAc,IAAAL,aAAA,GAAAC,CAAA,OAAG,CAAC;IAAA,KAClBK,mBAAmB,IAAAN,aAAA,GAAAC,CAAA,OAAkB,IAAI;IAAA,KACzCM,gBAAgB,IAAAP,aAAA,GAAAC,CAAA,OAAmD,IAAI;IAAA,KACvEO,gBAAgB,IAAAR,aAAA,GAAAC,CAAA,OAA0B,IAAI;EAAA;EAAA,OAAAQ,YAAA,CAAAZ,qBAAA;IAAAa,GAAA;IAAAC,KAAA;MAAA,IAAAC,WAAA,GAAAC,iBAAA,CAKtD,aAAkC;QAAAb,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QAChC,IAAI;UACF,IAAMc,WAAW,IAAAf,aAAA,GAAAC,CAAA,aAAS,IAAI,CAACe,kBAAkB,CAAC,CAAC;UAAChB,aAAA,GAAAC,CAAA;UACpD,IAAI,CAAAD,aAAA,GAAAiB,CAAA,WAACF,WAAW,CAACG,MAAM,MAAAlB,aAAA,GAAAiB,CAAA,UAAI,CAACF,WAAW,CAACI,UAAU,GAAE;YAAAnB,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAC,CAAA;YAClD,MAAM,IAAImB,KAAK,CAAC,oEAAoE,CAAC;UACvF,CAAC;YAAApB,aAAA,GAAAiB,CAAA;UAAA;UAAAjB,aAAA,GAAAC,CAAA;UAEDL,kBAAkB,CAACyB,KAAK,CAAC,oBAAoB,CAAC;UAACrB,aAAA,GAAAC,CAAA;UAE/CL,kBAAkB,CAAC0B,GAAG,CAAC,oBAAoB,CAAC;QAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACduB,OAAO,CAACD,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UACtE,MAAMsB,KAAK;QACb;MACF,CAAC;MAAA,SAdKE,UAAUA,CAAA;QAAA,OAAAb,WAAA,CAAAc,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAVF,UAAU;IAAA;EAAA;IAAAf,GAAA;IAAAC,KAAA;MAAA,IAAAiB,mBAAA,GAAAf,iBAAA,CAmBhB,aAAuD;QAAAb,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QACrD,IAAI;UACF,IAAM4B,gBAAgB,IAAA7B,aAAA,GAAAC,CAAA,cAAST,MAAM,CAACsC,6BAA6B,CAAC,CAAC;UACrE,IAAMC,oBAAoB,IAAA/B,aAAA,GAAAC,CAAA,cAAST,MAAM,CAACwC,iCAAiC,CAAC,CAAC;UAC7E,IAAMC,sBAAsB,IAAAjC,aAAA,GAAAC,CAAA,cAASP,YAAY,CAACwC,uBAAuB,CAAC,CAAC;UAAClC,aAAA,GAAAC,CAAA;UAE5E,OAAO;YACLiB,MAAM,EAAEW,gBAAgB,CAACM,MAAM,KAAK,SAAS;YAC7ChB,UAAU,EAAEY,oBAAoB,CAACI,MAAM,KAAK,SAAS;YACrDC,YAAY,EAAEH,sBAAsB,CAACE,MAAM,KAAK;UAClD,CAAC;QACH,CAAC,CAAC,OAAOZ,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACduB,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UACvD,OAAO;YACLiB,MAAM,EAAE,KAAK;YACbC,UAAU,EAAE,KAAK;YACjBiB,YAAY,EAAE;UAChB,CAAC;QACH;MACF,CAAC;MAAA,SAnBKpB,kBAAkBA,CAAA;QAAA,OAAAY,mBAAA,CAAAF,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAlBX,kBAAkB;IAAA;EAAA;IAAAN,GAAA;IAAAC,KAAA,EAwBxB,SAAA0B,YAAYA,CAACC,GAAkB,EAAQ;MAAAtC,aAAA,GAAAc,CAAA;MAAAd,aAAA,GAAAC,CAAA;MACrC,IAAI,CAACF,SAAS,GAAGuC,GAAG;IACtB;EAAC;IAAA5B,GAAA;IAAAC,KAAA;MAAA,IAAA4B,eAAA,GAAA1B,iBAAA,CAKD,WAAqB2B,MAA4B,EAAiB;QAAAxC,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QAChE,IAAI,CAAC,IAAI,CAACF,SAAS,EAAE;UAAAC,aAAA,GAAAiB,CAAA;UAAAjB,aAAA,GAAAC,CAAA;UACnB,MAAM,IAAImB,KAAK,CAAC,0BAA0B,CAAC;QAC7C,CAAC;UAAApB,aAAA,GAAAiB,CAAA;QAAA;QAAAjB,aAAA,GAAAC,CAAA;QAED,IAAI,IAAI,CAACC,WAAW,EAAE;UAAAF,aAAA,GAAAiB,CAAA;UAAAjB,aAAA,GAAAC,CAAA;UACpB,MAAM,IAAImB,KAAK,CAAC,+BAA+B,CAAC;QAClD,CAAC;UAAApB,aAAA,GAAAiB,CAAA;QAAA;QAAAjB,aAAA,GAAAC,CAAA;QAED,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFL,kBAAkB,CAACyB,KAAK,CAAC,uBAAuB,CAAC;UAEjD,IAAMoB,gBAAgB,IAAAzC,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACyC,mBAAmB,CAACF,MAAM,CAAC;UAACxC,aAAA,GAAAC,CAAA;UAE1D,IAAI,CAACC,WAAW,GAAG,IAAI;UAACF,aAAA,GAAAC,CAAA;UACxB,IAAI,CAACE,QAAQ,GAAG,KAAK;UAACH,aAAA,GAAAC,CAAA;UACtB,IAAI,CAACG,kBAAkB,GAAGuC,IAAI,CAACC,GAAG,CAAC,CAAC;UAAC5C,aAAA,GAAAC,CAAA;UACrC,IAAI,CAACI,cAAc,GAAG,CAAC;UAEvB,IAAMwC,gBAAgB,IAAA7C,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACF,SAAS,CAAC+C,WAAW,CAACL,gBAAgB,CAAC;UAACzC,aAAA,GAAAC,CAAA;UAGtE,IAAI,CAAC8C,uBAAuB,CAAC,CAAC;UAE9B,IAAMC,MAAM,IAAAhD,aAAA,GAAAC,CAAA,cAAS4C,gBAAgB;UAAC7C,aAAA,GAAAC,CAAA;UACtC,IAAI,CAACK,mBAAmB,GAAG0C,MAAM,CAACC,GAAG;UAACjD,aAAA,GAAAC,CAAA;UAEtCL,kBAAkB,CAAC0B,GAAG,CAAC,uBAAuB,CAAC;QACjD,CAAC,CAAC,OAAOC,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACd,IAAI,CAACC,WAAW,GAAG,KAAK;UAACF,aAAA,GAAAC,CAAA;UACzBuB,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UACnD,MAAMsB,KAAK;QACb;MACF,CAAC;MAAA,SAjCK2B,cAAcA,CAAAC,EAAA;QAAA,OAAAZ,eAAA,CAAAb,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAduB,cAAc;IAAA;EAAA;IAAAxC,GAAA;IAAAC,KAAA;MAAA,IAAAyC,cAAA,GAAAvC,iBAAA,CAsCpB,aAAqD;QAAAb,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QACnD,IAAI,CAAAD,aAAA,GAAAiB,CAAA,WAAC,IAAI,CAAClB,SAAS,MAAAC,aAAA,GAAAiB,CAAA,UAAI,CAAC,IAAI,CAACf,WAAW,GAAE;UAAAF,aAAA,GAAAiB,CAAA;UAAAjB,aAAA,GAAAC,CAAA;UACxC,MAAM,IAAImB,KAAK,CAAC,6BAA6B,CAAC;QAChD,CAAC;UAAApB,aAAA,GAAAiB,CAAA;QAAA;QAAAjB,aAAA,GAAAC,CAAA;QAED,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFL,kBAAkB,CAACyB,KAAK,CAAC,sBAAsB,CAAC;UAACrB,aAAA,GAAAC,CAAA;UAEjD,IAAI,CAACF,SAAS,CAACsD,aAAa,CAAC,CAAC;UAACrD,aAAA,GAAAC,CAAA;UAC/B,IAAI,CAACC,WAAW,GAAG,KAAK;UAACF,aAAA,GAAAC,CAAA;UACzB,IAAI,CAACE,QAAQ,GAAG,KAAK;UAACH,aAAA,GAAAC,CAAA;UAGtB,IAAI,CAACqD,sBAAsB,CAAC,CAAC;UAACtD,aAAA,GAAAC,CAAA;UAE9B,IAAI,CAAC,IAAI,CAACK,mBAAmB,EAAE;YAAAN,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAC,CAAA;YAC7B,MAAM,IAAImB,KAAK,CAAC,6BAA6B,CAAC;UAChD,CAAC;YAAApB,aAAA,GAAAiB,CAAA;UAAA;UAGD,IAAMsC,QAAQ,IAAAvD,aAAA,GAAAC,CAAA,cAASN,UAAU,CAAC6D,YAAY,CAAC,IAAI,CAAClD,mBAAmB,CAAC;UAACN,aAAA,GAAAC,CAAA;UACzE,IAAI,CAACsD,QAAQ,CAACE,MAAM,EAAE;YAAAzD,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAC,CAAA;YACpB,MAAM,IAAImB,KAAK,CAAC,0BAA0B,CAAC;UAC7C,CAAC;YAAApB,aAAA,GAAAiB,CAAA;UAAA;UAED,IAAMyC,QAAQ,IAAA1D,aAAA,GAAAC,CAAA,QAAG,CAAC0C,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACxC,kBAAkB,GAAG,IAAI,CAACC,cAAc,IAAI,IAAI;UAGpF,IAAMsD,SAAS,IAAA3D,aAAA,GAAAC,CAAA,cAAS,IAAI,CAAC2D,iBAAiB,CAAC,IAAI,CAACtD,mBAAmB,CAAC;UAExE,IAAM0C,MAA4B,IAAAhD,aAAA,GAAAC,CAAA,QAAG;YACnCgD,GAAG,EAAE,IAAI,CAAC3C,mBAAmB;YAC7BoD,QAAQ,EAARA,QAAQ;YACRG,QAAQ,EAAE,CAAA7D,aAAA,GAAAiB,CAAA,UAAAsC,QAAQ,CAACO,IAAI,MAAA9D,aAAA,GAAAiB,CAAA,UAAI,CAAC;YAC5B8C,KAAK,EAAE,IAAI;YACXC,MAAM,EAAE,IAAI;YACZL,SAAS,EAATA;UACF,CAAC;UAAC3D,aAAA,GAAAC,CAAA;UAEFL,kBAAkB,CAAC0B,GAAG,CAAC,sBAAsB,CAAC;UAACtB,aAAA,GAAAC,CAAA;UAC/C,OAAO+C,MAAM;QACf,CAAC,CAAC,OAAOzB,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACduB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UAClD,MAAMsB,KAAK;QACb;MACF,CAAC;MAAA,SA7CK8B,aAAaA,CAAA;QAAA,OAAAD,cAAA,CAAA1B,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAb0B,aAAa;IAAA;EAAA;IAAA3C,GAAA;IAAAC,KAAA;MAAA,IAAAsD,eAAA,GAAApD,iBAAA,CAkDnB,aAAsC;QAAAb,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QACpC,IAAI,CAAAD,aAAA,GAAAiB,CAAA,YAAC,IAAI,CAACf,WAAW,MAAAF,aAAA,GAAAiB,CAAA,WAAI,IAAI,CAACd,QAAQ,GAAE;UAAAH,aAAA,GAAAiB,CAAA;UAAAjB,aAAA,GAAAC,CAAA;UACtC;QACF,CAAC;UAAAD,aAAA,GAAAiB,CAAA;QAAA;QAAAjB,aAAA,GAAAC,CAAA;QAED,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAGF,IAAI,CAACE,QAAQ,GAAG,IAAI;UAACH,aAAA,GAAAC,CAAA;UACrBuB,OAAO,CAAC0C,GAAG,CAAC,+CAA+C,CAAC;QAC9D,CAAC,CAAC,OAAO3C,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACduB,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UACnD,MAAMsB,KAAK;QACb;MACF,CAAC;MAAA,SAdK4C,cAAcA,CAAA;QAAA,OAAAF,eAAA,CAAAvC,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAdwC,cAAc;IAAA;EAAA;IAAAzD,GAAA;IAAAC,KAAA;MAAA,IAAAyD,gBAAA,GAAAvD,iBAAA,CAmBpB,aAAuC;QAAAb,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QACrC,IAAI,CAAAD,aAAA,GAAAiB,CAAA,YAAC,IAAI,CAACf,WAAW,MAAAF,aAAA,GAAAiB,CAAA,WAAI,CAAC,IAAI,CAACd,QAAQ,GAAE;UAAAH,aAAA,GAAAiB,CAAA;UAAAjB,aAAA,GAAAC,CAAA;UACvC;QACF,CAAC;UAAAD,aAAA,GAAAiB,CAAA;QAAA;QAAAjB,aAAA,GAAAC,CAAA;QAED,IAAI;UAAAD,aAAA,GAAAC,CAAA;UAGF,IAAI,CAACE,QAAQ,GAAG,KAAK;UAACH,aAAA,GAAAC,CAAA;UACtBuB,OAAO,CAAC0C,GAAG,CAAC,gDAAgD,CAAC;QAC/D,CAAC,CAAC,OAAO3C,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACduB,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UACpD,MAAMsB,KAAK;QACb;MACF,CAAC;MAAA,SAdK8C,eAAeA,CAAA;QAAA,OAAAD,gBAAA,CAAA1C,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAf0C,eAAe;IAAA;EAAA;IAAA3D,GAAA;IAAAC,KAAA,EAmBrB,SAAA2D,kBAAkBA,CAAA,EAAsB;MAAAtE,aAAA,GAAAc,CAAA;MACtC,IAAMyD,WAAW,IAAAvE,aAAA,GAAAC,CAAA,QAAG0C,IAAI,CAACC,GAAG,CAAC,CAAC;MAC9B,IAAMc,QAAQ,IAAA1D,aAAA,GAAAC,CAAA,QAAG,IAAI,CAACC,WAAW,IAAAF,aAAA,GAAAiB,CAAA,WAC7B,CAACsD,WAAW,GAAG,IAAI,CAACnE,kBAAkB,GAAG,IAAI,CAACC,cAAc,IAAI,IAAI,KAAAL,aAAA,GAAAiB,CAAA,WACpE,CAAC;MAACjB,aAAA,GAAAC,CAAA;MAEN,OAAO;QACLyD,QAAQ,EAARA,QAAQ;QACRG,QAAQ,EAAE,CAAC;QACX3D,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BC,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC;IACH;EAAC;IAAAO,GAAA;IAAAC,KAAA,EAKD,SAAA6D,mBAAmBA,CAACC,QAA+C,EAAQ;MAAAzE,aAAA,GAAAc,CAAA;MAAAd,aAAA,GAAAC,CAAA;MACzE,IAAI,CAACM,gBAAgB,GAAGkE,QAAQ;IAClC;EAAC;IAAA/D,GAAA;IAAAC,KAAA;MAAA,IAAA+D,cAAA,GAAA7D,iBAAA,CAKD,WAAoBoC,GAAW,EAAmB;QAAAjD,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QAChD,IAAI;UACF,IAAMc,WAAW,IAAAf,aAAA,GAAAC,CAAA,cAASP,YAAY,CAACwC,uBAAuB,CAAC,CAAC;UAAClC,aAAA,GAAAC,CAAA;UACjE,IAAI,CAACc,WAAW,CAAC4D,OAAO,EAAE;YAAA3E,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAC,CAAA;YACxB,MAAM,IAAImB,KAAK,CAAC,iDAAiD,CAAC;UACpE,CAAC;YAAApB,aAAA,GAAAiB,CAAA;UAAA;UAED,IAAM2D,KAAK,IAAA5E,aAAA,GAAAC,CAAA,cAASP,YAAY,CAACmF,gBAAgB,CAAC5B,GAAG,CAAC;UAACjD,aAAA,GAAAC,CAAA;UACvD,OAAO2E,KAAK,CAAC3B,GAAG;QAClB,CAAC,CAAC,OAAO1B,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACduB,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UACzD,MAAMsB,KAAK;QACb;MACF,CAAC;MAAA,SAbKuD,aAAaA,CAAAC,GAAA;QAAA,OAAAL,cAAA,CAAAhD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbmD,aAAa;IAAA;EAAA;IAAApE,GAAA;IAAAC,KAAA;MAAA,IAAAqE,cAAA,GAAAnE,iBAAA,CAkBnB,WAAoBoC,GAAW,EAAkE;QAAA,IAAhEgC,OAAkC,GAAAtD,SAAA,CAAAuD,MAAA,QAAAvD,SAAA,QAAAwD,SAAA,GAAAxD,SAAA,OAAA3B,aAAA,GAAAiB,CAAA,WAAG,QAAQ;QAAAjB,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QAC5E,IAAI;UAAAD,aAAA,GAAAC,CAAA;UACFL,kBAAkB,CAACyB,KAAK,CAAC,mBAAmB,CAAC;UAG7C,IAAMkC,QAAQ,IAAAvD,aAAA,GAAAC,CAAA,cAASN,UAAU,CAAC6D,YAAY,CAACP,GAAG,CAAC;UAACjD,aAAA,GAAAC,CAAA;UACpD,IAAI,CAACsD,QAAQ,CAACE,MAAM,EAAE;YAAAzD,aAAA,GAAAiB,CAAA;YAAAjB,aAAA,GAAAC,CAAA;YACpB,MAAM,IAAImB,KAAK,CAAC,sCAAsC,CAAC;UACzD,CAAC;YAAApB,aAAA,GAAAiB,CAAA;UAAA;UAED,IAAMmE,YAAY,IAAApF,aAAA,GAAAC,CAAA,QAAG,CAAAD,aAAA,GAAAiB,CAAA,WAAAsC,QAAQ,CAACO,IAAI,MAAA9D,aAAA,GAAAiB,CAAA,WAAI,CAAC;UAACjB,aAAA,GAAAC,CAAA;UACxCuB,OAAO,CAAC0C,GAAG,CAAC,sBAAsBkB,YAAY,oBAAoBH,OAAO,EAAE,CAAC;UAG5E,IAAMI,aAAa,IAAArF,aAAA,GAAAC,CAAA,QAAG,GAAGN,UAAU,CAAC2F,cAAc,cAAc3C,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;UAGhF,IAAM2C,mBAAmB,IAAAvF,aAAA,GAAAC,CAAA,QAAG;YAC1BuF,GAAG,EAAE;cAAEC,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAI,CAAC;YACzCC,MAAM,EAAE;cAAEF,OAAO,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAI,CAAC;YAC7CE,IAAI,EAAE;cAAEH,OAAO,EAAE,OAAO;cAAEC,UAAU,EAAE;YAAK;UAC7C,CAAC;UAED,IAAMG,QAAQ,IAAA7F,aAAA,GAAAC,CAAA,QAAGsF,mBAAmB,CAACN,OAAO,CAAC;UAACjF,aAAA,GAAAC,CAAA;UAI9C,MAAMN,UAAU,CAACmG,SAAS,CAAC;YACzBC,IAAI,EAAE9C,GAAG;YACT+C,EAAE,EAAEX;UACN,CAAC,CAAC;UAEF,IAAMY,cAAc,IAAAjG,aAAA,GAAAC,CAAA,eAASN,UAAU,CAAC6D,YAAY,CAAC6B,aAAa,CAAC;UACnE,IAAMa,cAAc,IAAAlG,aAAA,GAAAC,CAAA,SAAG,CAAAD,aAAA,GAAAiB,CAAA,WAAAgF,cAAc,CAACnC,IAAI,MAAA9D,aAAA,GAAAiB,CAAA,WAAI,CAAC;UAACjB,aAAA,GAAAC,CAAA;UAEhDuB,OAAO,CAAC0C,GAAG,CAAC,qBAAqBkB,YAAY,OAAOc,cAAc,QAAQ,CAAC;UAAClG,aAAA,GAAAC,CAAA;UAE5EL,kBAAkB,CAAC0B,GAAG,CAAC,mBAAmB,CAAC;UAACtB,aAAA,GAAAC,CAAA;UAC5C,OAAOoF,aAAa;QACtB,CAAC,CAAC,OAAO9D,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACduB,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UAClD,MAAMsB,KAAK;QACb;MACF,CAAC;MAAA,SA3CK4E,aAAaA,CAAAC,GAAA;QAAA,OAAApB,cAAA,CAAAtD,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAbwE,aAAa;IAAA;EAAA;IAAAzF,GAAA;IAAAC,KAAA;MAAA,IAAA0F,kBAAA,GAAAxF,iBAAA,CAgDnB,WAAgCoC,GAAW,EAAmB;QAAAjD,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QAC5D,IAAI;UAEF,IAAMqG,YAAY,IAAAtG,aAAA,GAAAC,CAAA,SAAG,GAAGN,UAAU,CAAC2F,cAAc,aAAa3C,IAAI,CAACC,GAAG,CAAC,CAAC,MAAM;UAAC5C,aAAA,GAAAC,CAAA;UAE/EuB,OAAO,CAAC0C,GAAG,CAAC,iCAAiC,EAAEjB,GAAG,CAAC;UAACjD,aAAA,GAAAC,CAAA;UAUpD,OAAO,EAAE;QACX,CAAC,CAAC,OAAOsB,KAAK,EAAE;UAAAvB,aAAA,GAAAC,CAAA;UACduB,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;UAACvB,aAAA,GAAAC,CAAA;UACtD,OAAO,EAAE;QACX;MACF,CAAC;MAAA,SApBa2D,iBAAiBA,CAAA2C,GAAA;QAAA,OAAAF,kBAAA,CAAA3E,KAAA,OAAAC,SAAA;MAAA;MAAA,OAAjBiC,iBAAiB;IAAA;EAAA;IAAAlD,GAAA;IAAAC,KAAA,EAyB/B,SAAQ+B,mBAAmBA,CAACF,MAA4B,EAAO;MAAAxC,aAAA,GAAAc,CAAA;MAC7D,IAAM0F,UAAwC,IAAAxG,aAAA,GAAAC,CAAA,SAAG;QAC/CuF,GAAG,EAAE/F,YAAY,CAAC,MAAM,CAAC;QACzBkG,MAAM,EAAElG,YAAY,CAAC,MAAM,CAAC;QAC5BmG,IAAI,EAAEnG,YAAY,CAAC,OAAO,CAAC;QAC3BgH,KAAK,EAAEhH,YAAY,CAAC,OAAO;MAC7B,CAAC;MAACO,aAAA,GAAAC,CAAA;MAEF,OAAO;QACLgF,OAAO,EAAE,CAAAjF,aAAA,GAAAiB,CAAA,WAAAuF,UAAU,CAAChE,MAAM,CAACyC,OAAO,CAAC,MAAAjF,aAAA,GAAAiB,CAAA,WAAIxB,YAAY,CAAC,MAAM,CAAC;QAC3DiH,WAAW,EAAElE,MAAM,CAACmE,kBAAkB,GAAG,EAAE;QAC3CC,IAAI,EAAE,CAACpE,MAAM,CAACqE;MAEhB,CAAC;IACH;EAAC;IAAAnG,GAAA;IAAAC,KAAA,EAKD,SAAQoC,uBAAuBA,CAAA,EAAS;MAAA,IAAA+D,KAAA;MAAA9G,aAAA,GAAAc,CAAA;MAAAd,aAAA,GAAAC,CAAA;MACtC,IAAI,IAAI,CAACO,gBAAgB,EAAE;QAAAR,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAC,CAAA;QACzB8G,aAAa,CAAC,IAAI,CAACvG,gBAAgB,CAAC;MACtC,CAAC;QAAAR,aAAA,GAAAiB,CAAA;MAAA;MAAAjB,aAAA,GAAAC,CAAA;MAED,IAAI,CAACO,gBAAgB,GAAGwG,WAAW,CAAC,YAAM;QAAAhH,aAAA,GAAAc,CAAA;QAAAd,aAAA,GAAAC,CAAA;QACxC,IAAI6G,KAAI,CAACvG,gBAAgB,EAAE;UAAAP,aAAA,GAAAiB,CAAA;UACzB,IAAMgG,QAAQ,IAAAjH,aAAA,GAAAC,CAAA,SAAG6G,KAAI,CAACxC,kBAAkB,CAAC,CAAC;UAACtE,aAAA,GAAAC,CAAA;UAC3C6G,KAAI,CAACvG,gBAAgB,CAAC0G,QAAQ,CAAC;QACjC,CAAC;UAAAjH,aAAA,GAAAiB,CAAA;QAAA;MACH,CAAC,EAAE,IAAI,CAAC;IACV;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAQ2C,sBAAsBA,CAAA,EAAS;MAAAtD,aAAA,GAAAc,CAAA;MAAAd,aAAA,GAAAC,CAAA;MACrC,IAAI,IAAI,CAACO,gBAAgB,EAAE;QAAAR,aAAA,GAAAiB,CAAA;QAAAjB,aAAA,GAAAC,CAAA;QACzB8G,aAAa,CAAC,IAAI,CAACvG,gBAAgB,CAAC;QAACR,aAAA,GAAAC,CAAA;QACrC,IAAI,CAACO,gBAAgB,GAAG,IAAI;MAC9B,CAAC;QAAAR,aAAA,GAAAiB,CAAA;MAAA;IACH;EAAC;IAAAP,GAAA;IAAAC,KAAA,EAKD,SAAAuG,OAAOA,CAAA,EAAS;MAAAlH,aAAA,GAAAc,CAAA;MAAAd,aAAA,GAAAC,CAAA;MACd,IAAI,CAACqD,sBAAsB,CAAC,CAAC;MAACtD,aAAA,GAAAC,CAAA;MAC9B,IAAI,CAACC,WAAW,GAAG,KAAK;MAACF,aAAA,GAAAC,CAAA;MACzB,IAAI,CAACE,QAAQ,GAAG,KAAK;MAACH,aAAA,GAAAC,CAAA;MACtB,IAAI,CAACK,mBAAmB,GAAG,IAAI;MAACN,aAAA,GAAAC,CAAA;MAChC,IAAI,CAACM,gBAAgB,GAAG,IAAI;IAC9B;EAAC;AAAA;AAIH,OAAO,IAAM4G,qBAAqB,IAAAnH,aAAA,GAAAC,CAAA,SAAG,IAAIJ,qBAAqB,CAAC,CAAC", "ignoreList": []}