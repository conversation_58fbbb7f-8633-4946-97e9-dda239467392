193496b207e562e9c301da51dd6fcb30
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_1als4ma6pd() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\ai\\MediaPipeService.ts";
  var hash = "ef847c5770b0723278d8fc5abc31f83d06796dd7";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\ai\\MediaPipeService.ts",
    statementMap: {
      "0": {
        start: {
          line: 9,
          column: 37
        },
        end: {
          line: 31,
          column: 10
        }
      },
      "1": {
        start: {
          line: 80,
          column: 26
        },
        end: {
          line: 80,
          column: 31
        }
      },
      "2": {
        start: {
          line: 81,
          column: 39
        },
        end: {
          line: 88,
          column: 3
        }
      },
      "3": {
        start: {
          line: 94,
          column: 4
        },
        end: {
          line: 110,
          column: 5
        }
      },
      "4": {
        start: {
          line: 95,
          column: 6
        },
        end: {
          line: 95,
          column: 49
        }
      },
      "5": {
        start: {
          line: 97,
          column: 6
        },
        end: {
          line: 99,
          column: 7
        }
      },
      "6": {
        start: {
          line: 98,
          column: 8
        },
        end: {
          line: 98,
          column: 52
        }
      },
      "7": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 38
        }
      },
      "8": {
        start: {
          line: 105,
          column: 6
        },
        end: {
          line: 105,
          column: 32
        }
      },
      "9": {
        start: {
          line: 106,
          column: 6
        },
        end: {
          line: 106,
          column: 47
        }
      },
      "10": {
        start: {
          line: 108,
          column: 6
        },
        end: {
          line: 108,
          column: 62
        }
      },
      "11": {
        start: {
          line: 109,
          column: 6
        },
        end: {
          line: 109,
          column: 57
        }
      },
      "12": {
        start: {
          line: 120,
          column: 4
        },
        end: {
          line: 122,
          column: 5
        }
      },
      "13": {
        start: {
          line: 121,
          column: 6
        },
        end: {
          line: 121,
          column: 59
        }
      },
      "14": {
        start: {
          line: 124,
          column: 4
        },
        end: {
          line: 136,
          column: 5
        }
      },
      "15": {
        start: {
          line: 125,
          column: 6
        },
        end: {
          line: 125,
          column: 49
        }
      },
      "16": {
        start: {
          line: 129,
          column: 25
        },
        end: {
          line: 129,
          column: 69
        }
      },
      "17": {
        start: {
          line: 131,
          column: 6
        },
        end: {
          line: 131,
          column: 47
        }
      },
      "18": {
        start: {
          line: 132,
          column: 6
        },
        end: {
          line: 132,
          column: 24
        }
      },
      "19": {
        start: {
          line: 134,
          column: 6
        },
        end: {
          line: 134,
          column: 53
        }
      },
      "20": {
        start: {
          line: 135,
          column: 6
        },
        end: {
          line: 135,
          column: 18
        }
      },
      "21": {
        start: {
          line: 143,
          column: 4
        },
        end: {
          line: 173,
          column: 5
        }
      },
      "22": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 59
        }
      },
      "23": {
        start: {
          line: 146,
          column: 24
        },
        end: {
          line: 146,
          column: 44
        }
      },
      "24": {
        start: {
          line: 149,
          column: 24
        },
        end: {
          line: 149,
          column: 58
        }
      },
      "25": {
        start: {
          line: 152,
          column: 27
        },
        end: {
          line: 152,
          column: 76
        }
      },
      "26": {
        start: {
          line: 155,
          column: 27
        },
        end: {
          line: 155,
          column: 62
        }
      },
      "27": {
        start: {
          line: 158,
          column: 31
        },
        end: {
          line: 158,
          column: 82
        }
      },
      "28": {
        start: {
          line: 160,
          column: 47
        },
        end: {
          line: 166,
          column: 7
        }
      },
      "29": {
        start: {
          line: 168,
          column: 6
        },
        end: {
          line: 168,
          column: 57
        }
      },
      "30": {
        start: {
          line: 169,
          column: 6
        },
        end: {
          line: 169,
          column: 22
        }
      },
      "31": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 63
        }
      },
      "32": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 18
        }
      },
      "33": {
        start: {
          line: 183,
          column: 46
        },
        end: {
          line: 183,
          column: 48
        }
      },
      "34": {
        start: {
          line: 185,
          column: 4
        },
        end: {
          line: 196,
          column: 5
        }
      },
      "35": {
        start: {
          line: 185,
          column: 17
        },
        end: {
          line: 185,
          column: 18
        }
      },
      "36": {
        start: {
          line: 186,
          column: 25
        },
        end: {
          line: 186,
          column: 60
        }
      },
      "37": {
        start: {
          line: 188,
          column: 6
        },
        end: {
          line: 191,
          column: 7
        }
      },
      "38": {
        start: {
          line: 189,
          column: 25
        },
        end: {
          line: 189,
          column: 63
        }
      },
      "39": {
        start: {
          line: 190,
          column: 8
        },
        end: {
          line: 190,
          column: 31
        }
      },
      "40": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 195,
          column: 7
        }
      },
      "41": {
        start: {
          line: 194,
          column: 8
        },
        end: {
          line: 194,
          column: 44
        }
      },
      "42": {
        start: {
          line: 198,
          column: 4
        },
        end: {
          line: 198,
          column: 19
        }
      },
      "43": {
        start: {
          line: 206,
          column: 26
        },
        end: {
          line: 210,
          column: 5
        }
      },
      "44": {
        start: {
          line: 213,
          column: 23
        },
        end: {
          line: 217,
          column: 5
        }
      },
      "45": {
        start: {
          line: 220,
          column: 21
        },
        end: {
          line: 224,
          column: 5
        }
      },
      "46": {
        start: {
          line: 227,
          column: 22
        },
        end: {
          line: 231,
          column: 5
        }
      },
      "47": {
        start: {
          line: 233,
          column: 4
        },
        end: {
          line: 239,
          column: 6
        }
      },
      "48": {
        start: {
          line: 246,
          column: 20
        },
        end: {
          line: 249,
          column: 5
        }
      },
      "49": {
        start: {
          line: 251,
          column: 20
        },
        end: {
          line: 254,
          column: 5
        }
      },
      "50": {
        start: {
          line: 256,
          column: 23
        },
        end: {
          line: 256,
          column: 68
        }
      },
      "51": {
        start: {
          line: 257,
          column: 23
        },
        end: {
          line: 257,
          column: 79
        }
      },
      "52": {
        start: {
          line: 258,
          column: 23
        },
        end: {
          line: 258,
          column: 79
        }
      },
      "53": {
        start: {
          line: 260,
          column: 21
        },
        end: {
          line: 260,
          column: 59
        }
      },
      "54": {
        start: {
          line: 261,
          column: 18
        },
        end: {
          line: 261,
          column: 64
        }
      },
      "55": {
        start: {
          line: 263,
          column: 4
        },
        end: {
          line: 263,
          column: 35
        }
      },
      "56": {
        start: {
          line: 276,
          column: 23
        },
        end: {
          line: 276,
          column: 67
        }
      },
      "57": {
        start: {
          line: 277,
          column: 22
        },
        end: {
          line: 277,
          column: 65
        }
      },
      "58": {
        start: {
          line: 278,
          column: 26
        },
        end: {
          line: 278,
          column: 73
        }
      },
      "59": {
        start: {
          line: 279,
          column: 25
        },
        end: {
          line: 279,
          column: 71
        }
      },
      "60": {
        start: {
          line: 282,
          column: 26
        },
        end: {
          line: 282,
          column: 52
        }
      },
      "61": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 295,
          column: 5
        }
      },
      "62": {
        start: {
          line: 286,
          column: 6
        },
        end: {
          line: 286,
          column: 49
        }
      },
      "63": {
        start: {
          line: 287,
          column: 11
        },
        end: {
          line: 295,
          column: 5
        }
      },
      "64": {
        start: {
          line: 288,
          column: 6
        },
        end: {
          line: 288,
          column: 52
        }
      },
      "65": {
        start: {
          line: 289,
          column: 11
        },
        end: {
          line: 295,
          column: 5
        }
      },
      "66": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 290,
          column: 52
        }
      },
      "67": {
        start: {
          line: 291,
          column: 11
        },
        end: {
          line: 295,
          column: 5
        }
      },
      "68": {
        start: {
          line: 292,
          column: 6
        },
        end: {
          line: 292,
          column: 50
        }
      },
      "69": {
        start: {
          line: 294,
          column: 6
        },
        end: {
          line: 294,
          column: 51
        }
      },
      "70": {
        start: {
          line: 302,
          column: 21
        },
        end: {
          line: 302,
          column: 64
        }
      },
      "71": {
        start: {
          line: 303,
          column: 22
        },
        end: {
          line: 303,
          column: 66
        }
      },
      "72": {
        start: {
          line: 304,
          column: 20
        },
        end: {
          line: 304,
          column: 61
        }
      },
      "73": {
        start: {
          line: 305,
          column: 21
        },
        end: {
          line: 305,
          column: 63
        }
      },
      "74": {
        start: {
          line: 308,
          column: 25
        },
        end: {
          line: 308,
          column: 59
        }
      },
      "75": {
        start: {
          line: 311,
          column: 4
        },
        end: {
          line: 317,
          column: 5
        }
      },
      "76": {
        start: {
          line: 312,
          column: 6
        },
        end: {
          line: 312,
          column: 22
        }
      },
      "77": {
        start: {
          line: 313,
          column: 11
        },
        end: {
          line: 317,
          column: 5
        }
      },
      "78": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 314,
          column: 24
        }
      },
      "79": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 316,
          column: 25
        }
      },
      "80": {
        start: {
          line: 320,
          column: 22
        },
        end: {
          line: 320,
          column: 50
        }
      },
      "81": {
        start: {
          line: 321,
          column: 23
        },
        end: {
          line: 321,
          column: 53
        }
      },
      "82": {
        start: {
          line: 322,
          column: 24
        },
        end: {
          line: 322,
          column: 46
        }
      },
      "83": {
        start: {
          line: 325,
          column: 4
        },
        end: {
          line: 331,
          column: 5
        }
      },
      "84": {
        start: {
          line: 326,
          column: 6
        },
        end: {
          line: 326,
          column: 25
        }
      },
      "85": {
        start: {
          line: 327,
          column: 11
        },
        end: {
          line: 331,
          column: 5
        }
      },
      "86": {
        start: {
          line: 328,
          column: 6
        },
        end: {
          line: 328,
          column: 26
        }
      },
      "87": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 26
        }
      },
      "88": {
        start: {
          line: 334,
          column: 20
        },
        end: {
          line: 334,
          column: 63
        }
      },
      "89": {
        start: {
          line: 336,
          column: 4
        },
        end: {
          line: 340,
          column: 6
        }
      },
      "90": {
        start: {
          line: 350,
          column: 23
        },
        end: {
          line: 350,
          column: 67
        }
      },
      "91": {
        start: {
          line: 351,
          column: 26
        },
        end: {
          line: 351,
          column: 73
        }
      },
      "92": {
        start: {
          line: 355,
          column: 24
        },
        end: {
          line: 355,
          column: 54
        }
      },
      "93": {
        start: {
          line: 357,
          column: 4
        },
        end: {
          line: 363,
          column: 5
        }
      },
      "94": {
        start: {
          line: 358,
          column: 6
        },
        end: {
          line: 358,
          column: 30
        }
      },
      "95": {
        start: {
          line: 359,
          column: 11
        },
        end: {
          line: 363,
          column: 5
        }
      },
      "96": {
        start: {
          line: 360,
          column: 6
        },
        end: {
          line: 360,
          column: 32
        }
      },
      "97": {
        start: {
          line: 362,
          column: 6
        },
        end: {
          line: 362,
          column: 29
        }
      },
      "98": {
        start: {
          line: 367,
          column: 4
        },
        end: {
          line: 373,
          column: 5
        }
      },
      "99": {
        start: {
          line: 368,
          column: 6
        },
        end: {
          line: 368,
          column: 33
        }
      },
      "100": {
        start: {
          line: 369,
          column: 11
        },
        end: {
          line: 373,
          column: 5
        }
      },
      "101": {
        start: {
          line: 370,
          column: 6
        },
        end: {
          line: 370,
          column: 32
        }
      },
      "102": {
        start: {
          line: 372,
          column: 6
        },
        end: {
          line: 372,
          column: 29
        }
      },
      "103": {
        start: {
          line: 377,
          column: 21
        },
        end: {
          line: 377,
          column: 64
        }
      },
      "104": {
        start: {
          line: 378,
          column: 22
        },
        end: {
          line: 378,
          column: 66
        }
      },
      "105": {
        start: {
          line: 379,
          column: 26
        },
        end: {
          line: 379,
          column: 64
        }
      },
      "106": {
        start: {
          line: 381,
          column: 4
        },
        end: {
          line: 387,
          column: 5
        }
      },
      "107": {
        start: {
          line: 382,
          column: 6
        },
        end: {
          line: 382,
          column: 29
        }
      },
      "108": {
        start: {
          line: 383,
          column: 11
        },
        end: {
          line: 387,
          column: 5
        }
      },
      "109": {
        start: {
          line: 384,
          column: 6
        },
        end: {
          line: 384,
          column: 24
        }
      },
      "110": {
        start: {
          line: 386,
          column: 6
        },
        end: {
          line: 386,
          column: 37
        }
      },
      "111": {
        start: {
          line: 389,
          column: 4
        },
        end: {
          line: 393,
          column: 6
        }
      },
      "112": {
        start: {
          line: 401,
          column: 4
        },
        end: {
          line: 401,
          column: 60
        }
      },
      "113": {
        start: {
          line: 401,
          column: 33
        },
        end: {
          line: 401,
          column: 58
        }
      },
      "114": {
        start: {
          line: 402,
          column: 4
        },
        end: {
          line: 402,
          column: 55
        }
      },
      "115": {
        start: {
          line: 410,
          column: 38
        },
        end: {
          line: 410,
          column: 40
        }
      },
      "116": {
        start: {
          line: 413,
          column: 4
        },
        end: {
          line: 420,
          column: 5
        }
      },
      "117": {
        start: {
          line: 413,
          column: 17
        },
        end: {
          line: 413,
          column: 18
        }
      },
      "118": {
        start: {
          line: 414,
          column: 6
        },
        end: {
          line: 419,
          column: 9
        }
      },
      "119": {
        start: {
          line: 422,
          column: 4
        },
        end: {
          line: 427,
          column: 6
        }
      },
      "120": {
        start: {
          line: 434,
          column: 4
        },
        end: {
          line: 434,
          column: 48
        }
      },
      "121": {
        start: {
          line: 441,
          column: 4
        },
        end: {
          line: 441,
          column: 30
        }
      },
      "122": {
        start: {
          line: 448,
          column: 4
        },
        end: {
          line: 448,
          column: 30
        }
      },
      "123": {
        start: {
          line: 455,
          column: 4
        },
        end: {
          line: 455,
          column: 31
        }
      },
      "124": {
        start: {
          line: 461,
          column: 32
        },
        end: {
          line: 461,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 93,
            column: 2
          },
          end: {
            line: 93,
            column: 3
          }
        },
        loc: {
          start: {
            line: 93,
            column: 72
          },
          end: {
            line: 111,
            column: 3
          }
        },
        line: 93
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 116,
            column: 2
          },
          end: {
            line: 116,
            column: 3
          }
        },
        loc: {
          start: {
            line: 119,
            column: 41
          },
          end: {
            line: 137,
            column: 3
          }
        },
        line: 119
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 142,
            column: 2
          },
          end: {
            line: 142,
            column: 3
          }
        },
        loc: {
          start: {
            line: 142,
            column: 81
          },
          end: {
            line: 174,
            column: 3
          }
        },
        line: 142
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 179,
            column: 2
          },
          end: {
            line: 179,
            column: 3
          }
        },
        loc: {
          start: {
            line: 182,
            column: 39
          },
          end: {
            line: 199,
            column: 3
          }
        },
        line: 182
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 204,
            column: 2
          },
          end: {
            line: 204,
            column: 3
          }
        },
        loc: {
          start: {
            line: 204,
            column: 93
          },
          end: {
            line: 240,
            column: 3
          }
        },
        line: 204
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 245,
            column: 2
          },
          end: {
            line: 245,
            column: 3
          }
        },
        loc: {
          start: {
            line: 245,
            column: 99
          },
          end: {
            line: 264,
            column: 3
          }
        },
        line: 245
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 269,
            column: 2
          },
          end: {
            line: 269,
            column: 3
          }
        },
        loc: {
          start: {
            line: 272,
            column: 74
          },
          end: {
            line: 296,
            column: 3
          }
        },
        line: 272
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 301,
            column: 2
          },
          end: {
            line: 301,
            column: 3
          }
        },
        loc: {
          start: {
            line: 301,
            column: 97
          },
          end: {
            line: 341,
            column: 3
          }
        },
        line: 301
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 346,
            column: 2
          },
          end: {
            line: 346,
            column: 3
          }
        },
        loc: {
          start: {
            line: 349,
            column: 48
          },
          end: {
            line: 394,
            column: 3
          }
        },
        line: 349
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 399,
            column: 2
          },
          end: {
            line: 399,
            column: 3
          }
        },
        loc: {
          start: {
            line: 399,
            column: 52
          },
          end: {
            line: 403,
            column: 3
          }
        },
        line: 399
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 401,
            column: 22
          },
          end: {
            line: 401,
            column: 23
          }
        },
        loc: {
          start: {
            line: 401,
            column: 33
          },
          end: {
            line: 401,
            column: 58
          }
        },
        line: 401
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 408,
            column: 2
          },
          end: {
            line: 408,
            column: 3
          }
        },
        loc: {
          start: {
            line: 408,
            column: 88
          },
          end: {
            line: 428,
            column: 3
          }
        },
        line: 408
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 433,
            column: 2
          },
          end: {
            line: 433,
            column: 3
          }
        },
        loc: {
          start: {
            line: 433,
            column: 58
          },
          end: {
            line: 435,
            column: 3
          }
        },
        line: 433
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 440,
            column: 2
          },
          end: {
            line: 440,
            column: 3
          }
        },
        loc: {
          start: {
            line: 440,
            column: 34
          },
          end: {
            line: 442,
            column: 3
          }
        },
        line: 440
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 447,
            column: 2
          },
          end: {
            line: 447,
            column: 3
          }
        },
        loc: {
          start: {
            line: 447,
            column: 21
          },
          end: {
            line: 449,
            column: 3
          }
        },
        line: 447
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 454,
            column: 2
          },
          end: {
            line: 454,
            column: 3
          }
        },
        loc: {
          start: {
            line: 454,
            column: 18
          },
          end: {
            line: 457,
            column: 3
          }
        },
        line: 454
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 97,
            column: 6
          },
          end: {
            line: 99,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 97,
            column: 6
          },
          end: {
            line: 99,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 97
      },
      "1": {
        loc: {
          start: {
            line: 118,
            column: 4
          },
          end: {
            line: 118,
            column: 26
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 118,
            column: 25
          },
          end: {
            line: 118,
            column: 26
          }
        }],
        line: 118
      },
      "2": {
        loc: {
          start: {
            line: 120,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 120,
            column: 4
          },
          end: {
            line: 122,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 120
      },
      "3": {
        loc: {
          start: {
            line: 188,
            column: 6
          },
          end: {
            line: 191,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 188,
            column: 6
          },
          end: {
            line: 191,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 188
      },
      "4": {
        loc: {
          start: {
            line: 193,
            column: 6
          },
          end: {
            line: 195,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 193,
            column: 6
          },
          end: {
            line: 195,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 193
      },
      "5": {
        loc: {
          start: {
            line: 285,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 285,
            column: 4
          },
          end: {
            line: 295,
            column: 5
          }
        }, {
          start: {
            line: 287,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        }],
        line: 285
      },
      "6": {
        loc: {
          start: {
            line: 285,
            column: 8
          },
          end: {
            line: 285,
            column: 68
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 285,
            column: 8
          },
          end: {
            line: 285,
            column: 34
          }
        }, {
          start: {
            line: 285,
            column: 38
          },
          end: {
            line: 285,
            column: 68
          }
        }],
        line: 285
      },
      "7": {
        loc: {
          start: {
            line: 287,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 287,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        }, {
          start: {
            line: 289,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        }],
        line: 287
      },
      "8": {
        loc: {
          start: {
            line: 287,
            column: 15
          },
          end: {
            line: 287,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 287,
            column: 15
          },
          end: {
            line: 287,
            column: 28
          }
        }, {
          start: {
            line: 287,
            column: 32
          },
          end: {
            line: 287,
            column: 62
          }
        }],
        line: 287
      },
      "9": {
        loc: {
          start: {
            line: 289,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 289,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        }, {
          start: {
            line: 291,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        }],
        line: 289
      },
      "10": {
        loc: {
          start: {
            line: 289,
            column: 15
          },
          end: {
            line: 289,
            column: 62
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 289,
            column: 15
          },
          end: {
            line: 289,
            column: 28
          }
        }, {
          start: {
            line: 289,
            column: 32
          },
          end: {
            line: 289,
            column: 62
          }
        }],
        line: 289
      },
      "11": {
        loc: {
          start: {
            line: 291,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 291,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        }, {
          start: {
            line: 293,
            column: 11
          },
          end: {
            line: 295,
            column: 5
          }
        }],
        line: 291
      },
      "12": {
        loc: {
          start: {
            line: 311,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 311,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        }, {
          start: {
            line: 313,
            column: 11
          },
          end: {
            line: 317,
            column: 5
          }
        }],
        line: 311
      },
      "13": {
        loc: {
          start: {
            line: 313,
            column: 11
          },
          end: {
            line: 317,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 313,
            column: 11
          },
          end: {
            line: 317,
            column: 5
          }
        }, {
          start: {
            line: 315,
            column: 11
          },
          end: {
            line: 317,
            column: 5
          }
        }],
        line: 313
      },
      "14": {
        loc: {
          start: {
            line: 325,
            column: 4
          },
          end: {
            line: 331,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 325,
            column: 4
          },
          end: {
            line: 331,
            column: 5
          }
        }, {
          start: {
            line: 327,
            column: 11
          },
          end: {
            line: 331,
            column: 5
          }
        }],
        line: 325
      },
      "15": {
        loc: {
          start: {
            line: 327,
            column: 11
          },
          end: {
            line: 331,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 11
          },
          end: {
            line: 331,
            column: 5
          }
        }, {
          start: {
            line: 329,
            column: 11
          },
          end: {
            line: 331,
            column: 5
          }
        }],
        line: 327
      },
      "16": {
        loc: {
          start: {
            line: 357,
            column: 4
          },
          end: {
            line: 363,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 357,
            column: 4
          },
          end: {
            line: 363,
            column: 5
          }
        }, {
          start: {
            line: 359,
            column: 11
          },
          end: {
            line: 363,
            column: 5
          }
        }],
        line: 357
      },
      "17": {
        loc: {
          start: {
            line: 359,
            column: 11
          },
          end: {
            line: 363,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 359,
            column: 11
          },
          end: {
            line: 363,
            column: 5
          }
        }, {
          start: {
            line: 361,
            column: 11
          },
          end: {
            line: 363,
            column: 5
          }
        }],
        line: 359
      },
      "18": {
        loc: {
          start: {
            line: 367,
            column: 4
          },
          end: {
            line: 373,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 367,
            column: 4
          },
          end: {
            line: 373,
            column: 5
          }
        }, {
          start: {
            line: 369,
            column: 11
          },
          end: {
            line: 373,
            column: 5
          }
        }],
        line: 367
      },
      "19": {
        loc: {
          start: {
            line: 369,
            column: 11
          },
          end: {
            line: 373,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 369,
            column: 11
          },
          end: {
            line: 373,
            column: 5
          }
        }, {
          start: {
            line: 371,
            column: 11
          },
          end: {
            line: 373,
            column: 5
          }
        }],
        line: 369
      },
      "20": {
        loc: {
          start: {
            line: 381,
            column: 4
          },
          end: {
            line: 387,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 381,
            column: 4
          },
          end: {
            line: 387,
            column: 5
          }
        }, {
          start: {
            line: 383,
            column: 11
          },
          end: {
            line: 387,
            column: 5
          }
        }],
        line: 381
      },
      "21": {
        loc: {
          start: {
            line: 383,
            column: 11
          },
          end: {
            line: 387,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 383,
            column: 11
          },
          end: {
            line: 387,
            column: 5
          }
        }, {
          start: {
            line: 385,
            column: 11
          },
          end: {
            line: 387,
            column: 5
          }
        }],
        line: 383
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0
    },
    b: {
      "0": [0, 0],
      "1": [0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "ef847c5770b0723278d8fc5abc31f83d06796dd7"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_1als4ma6pd = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_1als4ma6pd();
import { performanceMonitor } from "../../../utils/performance";
export var TENNIS_POSE_LANDMARKS = (cov_1als4ma6pd().s[0]++, {
  LEFT_SHOULDER: 11,
  RIGHT_SHOULDER: 12,
  LEFT_ELBOW: 13,
  RIGHT_ELBOW: 14,
  LEFT_WRIST: 15,
  RIGHT_WRIST: 16,
  LEFT_HIP: 23,
  RIGHT_HIP: 24,
  LEFT_KNEE: 25,
  RIGHT_KNEE: 26,
  LEFT_ANKLE: 27,
  RIGHT_ANKLE: 28,
  NOSE: 0,
  LEFT_EYE: 1,
  RIGHT_EYE: 2,
  LEFT_EAR: 3,
  RIGHT_EAR: 4,
  MOUTH_LEFT: 9,
  MOUTH_RIGHT: 10
});
var MediaPipeService = function () {
  function MediaPipeService() {
    _classCallCheck(this, MediaPipeService);
    this.isInitialized = (cov_1als4ma6pd().s[1]++, false);
    this.config = (cov_1als4ma6pd().s[2]++, {
      minDetectionConfidence: 0.7,
      minTrackingConfidence: 0.5,
      modelComplexity: 1,
      smoothLandmarks: true,
      enableSegmentation: false,
      smoothSegmentation: true
    });
  }
  return _createClass(MediaPipeService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* (config) {
        cov_1als4ma6pd().f[0]++;
        cov_1als4ma6pd().s[3]++;
        try {
          cov_1als4ma6pd().s[4]++;
          performanceMonitor.start('mediapipe_init');
          cov_1als4ma6pd().s[5]++;
          if (config) {
            cov_1als4ma6pd().b[0][0]++;
            cov_1als4ma6pd().s[6]++;
            this.config = Object.assign({}, this.config, config);
          } else {
            cov_1als4ma6pd().b[0][1]++;
          }
          cov_1als4ma6pd().s[7]++;
          yield this.loadMediaPipeModel();
          cov_1als4ma6pd().s[8]++;
          this.isInitialized = true;
          cov_1als4ma6pd().s[9]++;
          performanceMonitor.end('mediapipe_init');
        } catch (error) {
          cov_1als4ma6pd().s[10]++;
          console.error('Failed to initialize MediaPipe:', error);
          cov_1als4ma6pd().s[11]++;
          throw new Error('MediaPipe initialization failed');
        }
      });
      function initialize(_x) {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "detectPose",
    value: (function () {
      var _detectPose = _asyncToGenerator(function* (imageData) {
        var frameIndex = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_1als4ma6pd().b[1][0]++, 0);
        cov_1als4ma6pd().f[1]++;
        cov_1als4ma6pd().s[12]++;
        if (!this.isInitialized) {
          cov_1als4ma6pd().b[2][0]++;
          cov_1als4ma6pd().s[13]++;
          throw new Error('MediaPipe service not initialized');
        } else {
          cov_1als4ma6pd().b[2][1]++;
        }
        cov_1als4ma6pd().s[14]++;
        try {
          cov_1als4ma6pd().s[15]++;
          performanceMonitor.start('pose_detection');
          var mockResult = (cov_1als4ma6pd().s[16]++, yield this.simulatePoseDetection(frameIndex));
          cov_1als4ma6pd().s[17]++;
          performanceMonitor.end('pose_detection');
          cov_1als4ma6pd().s[18]++;
          return mockResult;
        } catch (error) {
          cov_1als4ma6pd().s[19]++;
          console.error('Pose detection failed:', error);
          cov_1als4ma6pd().s[20]++;
          return null;
        }
      });
      function detectPose(_x2) {
        return _detectPose.apply(this, arguments);
      }
      return detectPose;
    }())
  }, {
    key: "analyzeTennisMovement",
    value: function analyzeTennisMovement(poseResult) {
      cov_1als4ma6pd().f[2]++;
      cov_1als4ma6pd().s[21]++;
      try {
        cov_1als4ma6pd().s[22]++;
        performanceMonitor.start('tennis_movement_analysis');
        var landmarks = (cov_1als4ma6pd().s[23]++, poseResult.landmarks);
        var keyAngles = (cov_1als4ma6pd().s[24]++, this.calculateKeyAngles(landmarks));
        var movementType = (cov_1als4ma6pd().s[25]++, this.classifyTennisMovement(landmarks, keyAngles));
        var bodyPosition = (cov_1als4ma6pd().s[26]++, this.analyzeBodyPosition(landmarks));
        var technicalMetrics = (cov_1als4ma6pd().s[27]++, this.evaluateTechnicalMetrics(landmarks, keyAngles));
        var analysis = (cov_1als4ma6pd().s[28]++, {
          movementType: movementType.type,
          confidence: movementType.confidence,
          keyAngles: keyAngles,
          bodyPosition: bodyPosition,
          technicalMetrics: technicalMetrics
        });
        cov_1als4ma6pd().s[29]++;
        performanceMonitor.end('tennis_movement_analysis');
        cov_1als4ma6pd().s[30]++;
        return analysis;
      } catch (error) {
        cov_1als4ma6pd().s[31]++;
        console.error('Tennis movement analysis failed:', error);
        cov_1als4ma6pd().s[32]++;
        throw error;
      }
    }
  }, {
    key: "processVideoFrames",
    value: (function () {
      var _processVideoFrames = _asyncToGenerator(function* (frames, onProgress) {
        cov_1als4ma6pd().f[3]++;
        var results = (cov_1als4ma6pd().s[33]++, []);
        cov_1als4ma6pd().s[34]++;
        for (var i = (cov_1als4ma6pd().s[35]++, 0); i < frames.length; i++) {
          var poseResult = (cov_1als4ma6pd().s[36]++, yield this.detectPose(frames[i], i));
          cov_1als4ma6pd().s[37]++;
          if (poseResult) {
            cov_1als4ma6pd().b[3][0]++;
            var analysis = (cov_1als4ma6pd().s[38]++, this.analyzeTennisMovement(poseResult));
            cov_1als4ma6pd().s[39]++;
            results.push(analysis);
          } else {
            cov_1als4ma6pd().b[3][1]++;
          }
          cov_1als4ma6pd().s[40]++;
          if (onProgress) {
            cov_1als4ma6pd().b[4][0]++;
            cov_1als4ma6pd().s[41]++;
            onProgress((i + 1) / frames.length);
          } else {
            cov_1als4ma6pd().b[4][1]++;
          }
        }
        cov_1als4ma6pd().s[42]++;
        return results;
      });
      function processVideoFrames(_x3, _x4) {
        return _processVideoFrames.apply(this, arguments);
      }
      return processVideoFrames;
    }())
  }, {
    key: "calculateKeyAngles",
    value: function calculateKeyAngles(landmarks) {
      cov_1als4ma6pd().f[4]++;
      var shoulderAngle = (cov_1als4ma6pd().s[43]++, this.calculateAngle(landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER], landmarks[TENNIS_POSE_LANDMARKS.LEFT_ELBOW], landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST]));
      var elbowAngle = (cov_1als4ma6pd().s[44]++, this.calculateAngle(landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER], landmarks[TENNIS_POSE_LANDMARKS.LEFT_ELBOW], landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST]));
      var hipAngle = (cov_1als4ma6pd().s[45]++, this.calculateAngle(landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER], landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP], landmarks[TENNIS_POSE_LANDMARKS.LEFT_KNEE]));
      var kneeAngle = (cov_1als4ma6pd().s[46]++, this.calculateAngle(landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP], landmarks[TENNIS_POSE_LANDMARKS.LEFT_KNEE], landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE]));
      cov_1als4ma6pd().s[47]++;
      return {
        shoulderAngle: shoulderAngle,
        elbowAngle: elbowAngle,
        wristAngle: 0,
        hipAngle: hipAngle,
        kneeAngle: kneeAngle
      };
    }
  }, {
    key: "calculateAngle",
    value: function calculateAngle(point1, point2, point3) {
      cov_1als4ma6pd().f[5]++;
      var vector1 = (cov_1als4ma6pd().s[48]++, {
        x: point1.x - point2.x,
        y: point1.y - point2.y
      });
      var vector2 = (cov_1als4ma6pd().s[49]++, {
        x: point3.x - point2.x,
        y: point3.y - point2.y
      });
      var dotProduct = (cov_1als4ma6pd().s[50]++, vector1.x * vector2.x + vector1.y * vector2.y);
      var magnitude1 = (cov_1als4ma6pd().s[51]++, Math.sqrt(vector1.x * vector1.x + vector1.y * vector1.y));
      var magnitude2 = (cov_1als4ma6pd().s[52]++, Math.sqrt(vector2.x * vector2.x + vector2.y * vector2.y));
      var cosAngle = (cov_1als4ma6pd().s[53]++, dotProduct / (magnitude1 * magnitude2));
      var angle = (cov_1als4ma6pd().s[54]++, Math.acos(Math.max(-1, Math.min(1, cosAngle))));
      cov_1als4ma6pd().s[55]++;
      return angle * 180 / Math.PI;
    }
  }, {
    key: "classifyTennisMovement",
    value: function classifyTennisMovement(landmarks, angles) {
      cov_1als4ma6pd().f[6]++;
      var rightWrist = (cov_1als4ma6pd().s[56]++, landmarks[TENNIS_POSE_LANDMARKS.RIGHT_WRIST]);
      var leftWrist = (cov_1als4ma6pd().s[57]++, landmarks[TENNIS_POSE_LANDMARKS.LEFT_WRIST]);
      var rightShoulder = (cov_1als4ma6pd().s[58]++, landmarks[TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER]);
      var leftShoulder = (cov_1als4ma6pd().s[59]++, landmarks[TENNIS_POSE_LANDMARKS.LEFT_SHOULDER]);
      var isRightHanded = (cov_1als4ma6pd().s[60]++, rightWrist.y < leftWrist.y);
      cov_1als4ma6pd().s[61]++;
      if ((cov_1als4ma6pd().b[6][0]++, angles.shoulderAngle > 160) && (cov_1als4ma6pd().b[6][1]++, rightWrist.y < rightShoulder.y)) {
        cov_1als4ma6pd().b[5][0]++;
        cov_1als4ma6pd().s[62]++;
        return {
          type: 'serve',
          confidence: 0.85
        };
      } else {
        cov_1als4ma6pd().b[5][1]++;
        cov_1als4ma6pd().s[63]++;
        if ((cov_1als4ma6pd().b[8][0]++, isRightHanded) && (cov_1als4ma6pd().b[8][1]++, rightWrist.x > rightShoulder.x)) {
          cov_1als4ma6pd().b[7][0]++;
          cov_1als4ma6pd().s[64]++;
          return {
            type: 'forehand',
            confidence: 0.80
          };
        } else {
          cov_1als4ma6pd().b[7][1]++;
          cov_1als4ma6pd().s[65]++;
          if ((cov_1als4ma6pd().b[10][0]++, isRightHanded) && (cov_1als4ma6pd().b[10][1]++, rightWrist.x < rightShoulder.x)) {
            cov_1als4ma6pd().b[9][0]++;
            cov_1als4ma6pd().s[66]++;
            return {
              type: 'backhand',
              confidence: 0.75
            };
          } else {
            cov_1als4ma6pd().b[9][1]++;
            cov_1als4ma6pd().s[67]++;
            if (rightWrist.y < rightShoulder.y - 0.1) {
              cov_1als4ma6pd().b[11][0]++;
              cov_1als4ma6pd().s[68]++;
              return {
                type: 'volley',
                confidence: 0.70
              };
            } else {
              cov_1als4ma6pd().b[11][1]++;
              cov_1als4ma6pd().s[69]++;
              return {
                type: 'unknown',
                confidence: 0.50
              };
            }
          }
        }
      }
    }
  }, {
    key: "analyzeBodyPosition",
    value: function analyzeBodyPosition(landmarks) {
      cov_1als4ma6pd().f[7]++;
      var leftFoot = (cov_1als4ma6pd().s[70]++, landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE]);
      var rightFoot = (cov_1als4ma6pd().s[71]++, landmarks[TENNIS_POSE_LANDMARKS.RIGHT_ANKLE]);
      var leftHip = (cov_1als4ma6pd().s[72]++, landmarks[TENNIS_POSE_LANDMARKS.LEFT_HIP]);
      var rightHip = (cov_1als4ma6pd().s[73]++, landmarks[TENNIS_POSE_LANDMARKS.RIGHT_HIP]);
      var footDistance = (cov_1als4ma6pd().s[74]++, Math.abs(leftFoot.x - rightFoot.x));
      var stance;
      cov_1als4ma6pd().s[75]++;
      if (footDistance > 0.3) {
        cov_1als4ma6pd().b[12][0]++;
        cov_1als4ma6pd().s[76]++;
        stance = 'open';
      } else {
        cov_1als4ma6pd().b[12][1]++;
        cov_1als4ma6pd().s[77]++;
        if (footDistance < 0.1) {
          cov_1als4ma6pd().b[13][0]++;
          cov_1als4ma6pd().s[78]++;
          stance = 'closed';
        } else {
          cov_1als4ma6pd().b[13][1]++;
          cov_1als4ma6pd().s[79]++;
          stance = 'neutral';
        }
      }
      var hipCenter = (cov_1als4ma6pd().s[80]++, (leftHip.x + rightHip.x) / 2);
      var footCenter = (cov_1als4ma6pd().s[81]++, (leftFoot.x + rightFoot.x) / 2);
      var weightShift = (cov_1als4ma6pd().s[82]++, hipCenter - footCenter);
      var weight;
      cov_1als4ma6pd().s[83]++;
      if (weightShift > 0.05) {
        cov_1als4ma6pd().b[14][0]++;
        cov_1als4ma6pd().s[84]++;
        weight = 'forward';
      } else {
        cov_1als4ma6pd().b[14][1]++;
        cov_1als4ma6pd().s[85]++;
        if (weightShift < -0.05) {
          cov_1als4ma6pd().b[15][0]++;
          cov_1als4ma6pd().s[86]++;
          weight = 'backward';
        } else {
          cov_1als4ma6pd().b[15][1]++;
          cov_1als4ma6pd().s[87]++;
          weight = 'centered';
        }
      }
      var balance = (cov_1als4ma6pd().s[88]++, Math.max(0, 1 - Math.abs(weightShift) * 10));
      cov_1als4ma6pd().s[89]++;
      return {
        stance: stance,
        weight: weight,
        balance: balance
      };
    }
  }, {
    key: "evaluateTechnicalMetrics",
    value: function evaluateTechnicalMetrics(landmarks, angles) {
      cov_1als4ma6pd().f[8]++;
      var rightWrist = (cov_1als4ma6pd().s[90]++, landmarks[TENNIS_POSE_LANDMARKS.RIGHT_WRIST]);
      var rightShoulder = (cov_1als4ma6pd().s[91]++, landmarks[TENNIS_POSE_LANDMARKS.RIGHT_SHOULDER]);
      var racketPosition;
      var wristHeight = (cov_1als4ma6pd().s[92]++, rightShoulder.y - rightWrist.y);
      cov_1als4ma6pd().s[93]++;
      if (wristHeight > 0.2) {
        cov_1als4ma6pd().b[16][0]++;
        cov_1als4ma6pd().s[94]++;
        racketPosition = 'high';
      } else {
        cov_1als4ma6pd().b[16][1]++;
        cov_1als4ma6pd().s[95]++;
        if (wristHeight > -0.1) {
          cov_1als4ma6pd().b[17][0]++;
          cov_1als4ma6pd().s[96]++;
          racketPosition = 'medium';
        } else {
          cov_1als4ma6pd().b[17][1]++;
          cov_1als4ma6pd().s[97]++;
          racketPosition = 'low';
        }
      }
      var followThrough;
      cov_1als4ma6pd().s[98]++;
      if (angles.elbowAngle > 150) {
        cov_1als4ma6pd().b[18][0]++;
        cov_1als4ma6pd().s[99]++;
        followThrough = 'complete';
      } else {
        cov_1als4ma6pd().b[18][1]++;
        cov_1als4ma6pd().s[100]++;
        if (angles.elbowAngle > 120) {
          cov_1als4ma6pd().b[19][0]++;
          cov_1als4ma6pd().s[101]++;
          followThrough = 'partial';
        } else {
          cov_1als4ma6pd().b[19][1]++;
          cov_1als4ma6pd().s[102]++;
          followThrough = 'none';
        }
      }
      var footwork;
      var leftFoot = (cov_1als4ma6pd().s[103]++, landmarks[TENNIS_POSE_LANDMARKS.LEFT_ANKLE]);
      var rightFoot = (cov_1als4ma6pd().s[104]++, landmarks[TENNIS_POSE_LANDMARKS.RIGHT_ANKLE]);
      var footStability = (cov_1als4ma6pd().s[105]++, 1 - Math.abs(leftFoot.y - rightFoot.y));
      cov_1als4ma6pd().s[106]++;
      if (footStability > 0.9) {
        cov_1als4ma6pd().b[20][0]++;
        cov_1als4ma6pd().s[107]++;
        footwork = 'excellent';
      } else {
        cov_1als4ma6pd().b[20][1]++;
        cov_1als4ma6pd().s[108]++;
        if (footStability > 0.7) {
          cov_1als4ma6pd().b[21][0]++;
          cov_1als4ma6pd().s[109]++;
          footwork = 'good';
        } else {
          cov_1als4ma6pd().b[21][1]++;
          cov_1als4ma6pd().s[110]++;
          footwork = 'needs_improvement';
        }
      }
      cov_1als4ma6pd().s[111]++;
      return {
        racketPosition: racketPosition,
        followThrough: followThrough,
        footwork: footwork
      };
    }
  }, {
    key: "loadMediaPipeModel",
    value: (function () {
      var _loadMediaPipeModel = _asyncToGenerator(function* () {
        cov_1als4ma6pd().f[9]++;
        cov_1als4ma6pd().s[112]++;
        yield new Promise(function (resolve) {
          cov_1als4ma6pd().f[10]++;
          cov_1als4ma6pd().s[113]++;
          return setTimeout(resolve, 1000);
        });
        cov_1als4ma6pd().s[114]++;
        console.log('MediaPipe model loaded successfully');
      });
      function loadMediaPipeModel() {
        return _loadMediaPipeModel.apply(this, arguments);
      }
      return loadMediaPipeModel;
    }())
  }, {
    key: "simulatePoseDetection",
    value: (function () {
      var _simulatePoseDetection = _asyncToGenerator(function* (frameIndex) {
        cov_1als4ma6pd().f[11]++;
        var landmarks = (cov_1als4ma6pd().s[115]++, []);
        cov_1als4ma6pd().s[116]++;
        for (var i = (cov_1als4ma6pd().s[117]++, 0); i < 33; i++) {
          cov_1als4ma6pd().s[118]++;
          landmarks.push({
            x: Math.random() * 0.8 + 0.1,
            y: Math.random() * 0.8 + 0.1,
            z: Math.random() * 0.2 - 0.1,
            visibility: Math.random() * 0.3 + 0.7
          });
        }
        cov_1als4ma6pd().s[119]++;
        return {
          landmarks: landmarks,
          confidence: Math.random() * 0.2 + 0.8,
          timestamp: Date.now(),
          frameIndex: frameIndex
        };
      });
      function simulatePoseDetection(_x5) {
        return _simulatePoseDetection.apply(this, arguments);
      }
      return simulatePoseDetection;
    }())
  }, {
    key: "updateConfig",
    value: function updateConfig(config) {
      cov_1als4ma6pd().f[12]++;
      cov_1als4ma6pd().s[120]++;
      this.config = Object.assign({}, this.config, config);
    }
  }, {
    key: "getConfig",
    value: function getConfig() {
      cov_1als4ma6pd().f[13]++;
      cov_1als4ma6pd().s[121]++;
      return Object.assign({}, this.config);
    }
  }, {
    key: "isReady",
    value: function isReady() {
      cov_1als4ma6pd().f[14]++;
      cov_1als4ma6pd().s[122]++;
      return this.isInitialized;
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      cov_1als4ma6pd().f[15]++;
      cov_1als4ma6pd().s[123]++;
      this.isInitialized = false;
    }
  }]);
}();
export var mediaPipeService = (cov_1als4ma6pd().s[124]++, new MediaPipeService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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