1f354deb750f583037cbbc70832bcee4
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2eowt29e6q() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\ai\\OpenAIService.ts";
  var hash = "f0c0628c99c351ca21a2cebb90bd1aae10f242c0";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\ai\\OpenAIService.ts",
    statementMap: {
      "0": {
        start: {
          line: 105,
          column: 31
        },
        end: {
          line: 105,
          column: 58
        }
      },
      "1": {
        start: {
          line: 106,
          column: 26
        },
        end: {
          line: 106,
          column: 33
        }
      },
      "2": {
        start: {
          line: 109,
          column: 4
        },
        end: {
          line: 109,
          column: 50
        }
      },
      "3": {
        start: {
          line: 116,
          column: 4
        },
        end: {
          line: 165,
          column: 5
        }
      },
      "4": {
        start: {
          line: 117,
          column: 6
        },
        end: {
          line: 119,
          column: 7
        }
      },
      "5": {
        start: {
          line: 118,
          column: 8
        },
        end: {
          line: 118,
          column: 57
        }
      },
      "6": {
        start: {
          line: 121,
          column: 21
        },
        end: {
          line: 121,
          column: 54
        }
      },
      "7": {
        start: {
          line: 123,
          column: 23
        },
        end: {
          line: 144,
          column: 8
        }
      },
      "8": {
        start: {
          line: 146,
          column: 6
        },
        end: {
          line: 149,
          column: 7
        }
      },
      "9": {
        start: {
          line: 147,
          column: 22
        },
        end: {
          line: 147,
          column: 43
        }
      },
      "10": {
        start: {
          line: 148,
          column: 8
        },
        end: {
          line: 148,
          column: 77
        }
      },
      "11": {
        start: {
          line: 151,
          column: 19
        },
        end: {
          line: 151,
          column: 40
        }
      },
      "12": {
        start: {
          line: 152,
          column: 22
        },
        end: {
          line: 152,
          column: 55
        }
      },
      "13": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 156,
          column: 7
        }
      },
      "14": {
        start: {
          line: 155,
          column: 8
        },
        end: {
          line: 155,
          column: 51
        }
      },
      "15": {
        start: {
          line: 158,
          column: 6
        },
        end: {
          line: 158,
          column: 49
        }
      },
      "16": {
        start: {
          line: 160,
          column: 23
        },
        end: {
          line: 160,
          column: 63
        }
      },
      "17": {
        start: {
          line: 161,
          column: 6
        },
        end: {
          line: 161,
          column: 73
        }
      },
      "18": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 55
        }
      },
      "19": {
        start: {
          line: 172,
          column: 4
        },
        end: {
          line: 220,
          column: 5
        }
      },
      "20": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 175,
          column: 7
        }
      },
      "21": {
        start: {
          line: 174,
          column: 8
        },
        end: {
          line: 174,
          column: 57
        }
      },
      "22": {
        start: {
          line: 177,
          column: 21
        },
        end: {
          line: 177,
          column: 59
        }
      },
      "23": {
        start: {
          line: 179,
          column: 23
        },
        end: {
          line: 200,
          column: 8
        }
      },
      "24": {
        start: {
          line: 202,
          column: 6
        },
        end: {
          line: 205,
          column: 7
        }
      },
      "25": {
        start: {
          line: 203,
          column: 22
        },
        end: {
          line: 203,
          column: 43
        }
      },
      "26": {
        start: {
          line: 204,
          column: 8
        },
        end: {
          line: 204,
          column: 77
        }
      },
      "27": {
        start: {
          line: 207,
          column: 19
        },
        end: {
          line: 207,
          column: 40
        }
      },
      "28": {
        start: {
          line: 208,
          column: 22
        },
        end: {
          line: 208,
          column: 55
        }
      },
      "29": {
        start: {
          line: 210,
          column: 6
        },
        end: {
          line: 212,
          column: 7
        }
      },
      "30": {
        start: {
          line: 211,
          column: 8
        },
        end: {
          line: 211,
          column: 51
        }
      },
      "31": {
        start: {
          line: 214,
          column: 6
        },
        end: {
          line: 214,
          column: 54
        }
      },
      "32": {
        start: {
          line: 216,
          column: 23
        },
        end: {
          line: 216,
          column: 63
        }
      },
      "33": {
        start: {
          line: 217,
          column: 6
        },
        end: {
          line: 217,
          column: 74
        }
      },
      "34": {
        start: {
          line: 219,
          column: 6
        },
        end: {
          line: 219,
          column: 52
        }
      },
      "35": {
        start: {
          line: 227,
          column: 4
        },
        end: {
          line: 275,
          column: 5
        }
      },
      "36": {
        start: {
          line: 228,
          column: 6
        },
        end: {
          line: 230,
          column: 7
        }
      },
      "37": {
        start: {
          line: 229,
          column: 8
        },
        end: {
          line: 229,
          column: 57
        }
      },
      "38": {
        start: {
          line: 232,
          column: 21
        },
        end: {
          line: 232,
          column: 58
        }
      },
      "39": {
        start: {
          line: 234,
          column: 23
        },
        end: {
          line: 255,
          column: 8
        }
      },
      "40": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 260,
          column: 7
        }
      },
      "41": {
        start: {
          line: 258,
          column: 22
        },
        end: {
          line: 258,
          column: 43
        }
      },
      "42": {
        start: {
          line: 259,
          column: 8
        },
        end: {
          line: 259,
          column: 77
        }
      },
      "43": {
        start: {
          line: 262,
          column: 19
        },
        end: {
          line: 262,
          column: 40
        }
      },
      "44": {
        start: {
          line: 263,
          column: 22
        },
        end: {
          line: 263,
          column: 55
        }
      },
      "45": {
        start: {
          line: 265,
          column: 6
        },
        end: {
          line: 267,
          column: 7
        }
      },
      "46": {
        start: {
          line: 266,
          column: 8
        },
        end: {
          line: 266,
          column: 51
        }
      },
      "47": {
        start: {
          line: 269,
          column: 6
        },
        end: {
          line: 269,
          column: 53
        }
      },
      "48": {
        start: {
          line: 271,
          column: 23
        },
        end: {
          line: 271,
          column: 63
        }
      },
      "49": {
        start: {
          line: 272,
          column: 6
        },
        end: {
          line: 272,
          column: 71
        }
      },
      "50": {
        start: {
          line: 274,
          column: 6
        },
        end: {
          line: 274,
          column: 51
        }
      },
      "51": {
        start: {
          line: 282,
          column: 4
        },
        end: {
          line: 313,
          column: 13
        }
      },
      "52": {
        start: {
          line: 320,
          column: 4
        },
        end: {
          line: 347,
          column: 13
        }
      },
      "53": {
        start: {
          line: 354,
          column: 4
        },
        end: {
          line: 374,
          column: 13
        }
      },
      "54": {
        start: {
          line: 382,
          column: 21
        },
        end: {
          line: 382,
          column: 42
        }
      },
      "55": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 395,
          column: 6
        }
      },
      "56": {
        start: {
          line: 402,
          column: 4
        },
        end: {
          line: 408,
          column: 6
        }
      },
      "57": {
        start: {
          line: 415,
          column: 4
        },
        end: {
          line: 432,
          column: 6
        }
      },
      "58": {
        start: {
          line: 439,
          column: 18
        },
        end: {
          line: 439,
          column: 37
        }
      },
      "59": {
        start: {
          line: 440,
          column: 4
        },
        end: {
          line: 444,
          column: 5
        }
      },
      "60": {
        start: {
          line: 440,
          column: 17
        },
        end: {
          line: 440,
          column: 18
        }
      },
      "61": {
        start: {
          line: 441,
          column: 6
        },
        end: {
          line: 443,
          column: 7
        }
      },
      "62": {
        start: {
          line: 442,
          column: 8
        },
        end: {
          line: 442,
          column: 36
        }
      },
      "63": {
        start: {
          line: 445,
          column: 4
        },
        end: {
          line: 445,
          column: 16
        }
      },
      "64": {
        start: {
          line: 452,
          column: 18
        },
        end: {
          line: 452,
          column: 37
        }
      },
      "65": {
        start: {
          line: 453,
          column: 28
        },
        end: {
          line: 453,
          column: 30
        }
      },
      "66": {
        start: {
          line: 454,
          column: 20
        },
        end: {
          line: 454,
          column: 25
        }
      },
      "67": {
        start: {
          line: 456,
          column: 4
        },
        end: {
          line: 467,
          column: 5
        }
      },
      "68": {
        start: {
          line: 457,
          column: 6
        },
        end: {
          line: 460,
          column: 7
        }
      },
      "69": {
        start: {
          line: 458,
          column: 8
        },
        end: {
          line: 458,
          column: 25
        }
      },
      "70": {
        start: {
          line: 459,
          column: 8
        },
        end: {
          line: 459,
          column: 17
        }
      },
      "71": {
        start: {
          line: 462,
          column: 6
        },
        end: {
          line: 466,
          column: 7
        }
      },
      "72": {
        start: {
          line: 463,
          column: 8
        },
        end: {
          line: 463,
          column: 59
        }
      },
      "73": {
        start: {
          line: 464,
          column: 13
        },
        end: {
          line: 466,
          column: 7
        }
      },
      "74": {
        start: {
          line: 465,
          column: 8
        },
        end: {
          line: 465,
          column: 14
        }
      },
      "75": {
        start: {
          line: 469,
          column: 4
        },
        end: {
          line: 469,
          column: 43
        }
      },
      "76": {
        start: {
          line: 476,
          column: 4
        },
        end: {
          line: 487,
          column: 6
        }
      },
      "77": {
        start: {
          line: 494,
          column: 4
        },
        end: {
          line: 500,
          column: 6
        }
      },
      "78": {
        start: {
          line: 507,
          column: 4
        },
        end: {
          line: 524,
          column: 6
        }
      },
      "79": {
        start: {
          line: 529,
          column: 29
        },
        end: {
          line: 529,
          column: 48
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 108,
            column: 2
          },
          end: {
            line: 108,
            column: 3
          }
        },
        loc: {
          start: {
            line: 108,
            column: 16
          },
          end: {
            line: 110,
            column: 3
          }
        },
        line: 108
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 115,
            column: 2
          },
          end: {
            line: 115,
            column: 3
          }
        },
        loc: {
          start: {
            line: 115,
            column: 90
          },
          end: {
            line: 166,
            column: 3
          }
        },
        line: 115
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 171,
            column: 2
          },
          end: {
            line: 171,
            column: 3
          }
        },
        loc: {
          start: {
            line: 171,
            column: 95
          },
          end: {
            line: 221,
            column: 3
          }
        },
        line: 171
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 226,
            column: 2
          },
          end: {
            line: 226,
            column: 3
          }
        },
        loc: {
          start: {
            line: 226,
            column: 90
          },
          end: {
            line: 276,
            column: 3
          }
        },
        line: 226
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 281,
            column: 2
          },
          end: {
            line: 281,
            column: 3
          }
        },
        loc: {
          start: {
            line: 281,
            column: 70
          },
          end: {
            line: 314,
            column: 3
          }
        },
        line: 281
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 319,
            column: 2
          },
          end: {
            line: 319,
            column: 3
          }
        },
        loc: {
          start: {
            line: 319,
            column: 74
          },
          end: {
            line: 348,
            column: 3
          }
        },
        line: 319
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 353,
            column: 2
          },
          end: {
            line: 353,
            column: 3
          }
        },
        loc: {
          start: {
            line: 353,
            column: 72
          },
          end: {
            line: 375,
            column: 3
          }
        },
        line: 353
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 380,
            column: 2
          },
          end: {
            line: 380,
            column: 3
          }
        },
        loc: {
          start: {
            line: 380,
            column: 67
          },
          end: {
            line: 396,
            column: 3
          }
        },
        line: 380
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 401,
            column: 2
          },
          end: {
            line: 401,
            column: 3
          }
        },
        loc: {
          start: {
            line: 401,
            column: 77
          },
          end: {
            line: 409,
            column: 3
          }
        },
        line: 401
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 414,
            column: 2
          },
          end: {
            line: 414,
            column: 3
          }
        },
        loc: {
          start: {
            line: 414,
            column: 75
          },
          end: {
            line: 433,
            column: 3
          }
        },
        line: 414
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 438,
            column: 2
          },
          end: {
            line: 438,
            column: 3
          }
        },
        loc: {
          start: {
            line: 438,
            column: 74
          },
          end: {
            line: 446,
            column: 3
          }
        },
        line: 438
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 451,
            column: 2
          },
          end: {
            line: 451,
            column: 3
          }
        },
        loc: {
          start: {
            line: 451,
            column: 78
          },
          end: {
            line: 470,
            column: 3
          }
        },
        line: 451
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 475,
            column: 2
          },
          end: {
            line: 475,
            column: 3
          }
        },
        loc: {
          start: {
            line: 475,
            column: 88
          },
          end: {
            line: 488,
            column: 3
          }
        },
        line: 475
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 493,
            column: 2
          },
          end: {
            line: 493,
            column: 3
          }
        },
        loc: {
          start: {
            line: 493,
            column: 89
          },
          end: {
            line: 501,
            column: 3
          }
        },
        line: 493
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 506,
            column: 2
          },
          end: {
            line: 506,
            column: 3
          }
        },
        loc: {
          start: {
            line: 506,
            column: 86
          },
          end: {
            line: 525,
            column: 3
          }
        },
        line: 506
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 109,
            column: 18
          },
          end: {
            line: 109,
            column: 49
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 109,
            column: 18
          },
          end: {
            line: 109,
            column: 43
          }
        }, {
          start: {
            line: 109,
            column: 47
          },
          end: {
            line: 109,
            column: 49
          }
        }],
        line: 109
      },
      "1": {
        loc: {
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 117,
            column: 6
          },
          end: {
            line: 119,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 117
      },
      "2": {
        loc: {
          start: {
            line: 146,
            column: 6
          },
          end: {
            line: 149,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 146,
            column: 6
          },
          end: {
            line: 149,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 146
      },
      "3": {
        loc: {
          start: {
            line: 148,
            column: 24
          },
          end: {
            line: 148,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 148,
            column: 24
          },
          end: {
            line: 148,
            column: 44
          }
        }, {
          start: {
            line: 148,
            column: 48
          },
          end: {
            line: 148,
            column: 75
          }
        }],
        line: 148
      },
      "4": {
        loc: {
          start: {
            line: 154,
            column: 6
          },
          end: {
            line: 156,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 6
          },
          end: {
            line: 156,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "5": {
        loc: {
          start: {
            line: 173,
            column: 6
          },
          end: {
            line: 175,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 173,
            column: 6
          },
          end: {
            line: 175,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 173
      },
      "6": {
        loc: {
          start: {
            line: 202,
            column: 6
          },
          end: {
            line: 205,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 202,
            column: 6
          },
          end: {
            line: 205,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 202
      },
      "7": {
        loc: {
          start: {
            line: 204,
            column: 24
          },
          end: {
            line: 204,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 24
          },
          end: {
            line: 204,
            column: 44
          }
        }, {
          start: {
            line: 204,
            column: 48
          },
          end: {
            line: 204,
            column: 75
          }
        }],
        line: 204
      },
      "8": {
        loc: {
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 210,
            column: 6
          },
          end: {
            line: 212,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 210
      },
      "9": {
        loc: {
          start: {
            line: 228,
            column: 6
          },
          end: {
            line: 230,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 228,
            column: 6
          },
          end: {
            line: 230,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 228
      },
      "10": {
        loc: {
          start: {
            line: 257,
            column: 6
          },
          end: {
            line: 260,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 257,
            column: 6
          },
          end: {
            line: 260,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 257
      },
      "11": {
        loc: {
          start: {
            line: 259,
            column: 24
          },
          end: {
            line: 259,
            column: 75
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 259,
            column: 24
          },
          end: {
            line: 259,
            column: 44
          }
        }, {
          start: {
            line: 259,
            column: 48
          },
          end: {
            line: 259,
            column: 75
          }
        }],
        line: 259
      },
      "12": {
        loc: {
          start: {
            line: 265,
            column: 6
          },
          end: {
            line: 267,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 265,
            column: 6
          },
          end: {
            line: 267,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 265
      },
      "13": {
        loc: {
          start: {
            line: 286,
            column: 16
          },
          end: {
            line: 286,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 286,
            column: 16
          },
          end: {
            line: 286,
            column: 33
          }
        }, {
          start: {
            line: 286,
            column: 37
          },
          end: {
            line: 286,
            column: 51
          }
        }],
        line: 286
      },
      "14": {
        loc: {
          start: {
            line: 299,
            column: 19
          },
          end: {
            line: 299,
            column: 99
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 299,
            column: 68
          },
          end: {
            line: 299,
            column: 77
          }
        }, {
          start: {
            line: 299,
            column: 80
          },
          end: {
            line: 299,
            column: 99
          }
        }],
        line: 299
      },
      "15": {
        loc: {
          start: {
            line: 301,
            column: 2
          },
          end: {
            line: 301,
            column: 93
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 301,
            column: 29
          },
          end: {
            line: 301,
            column: 88
          }
        }, {
          start: {
            line: 301,
            column: 91
          },
          end: {
            line: 301,
            column: 93
          }
        }],
        line: 301
      },
      "16": {
        loc: {
          start: {
            line: 337,
            column: 2
          },
          end: {
            line: 337,
            column: 63
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 337,
            column: 22
          },
          end: {
            line: 337,
            column: 58
          }
        }, {
          start: {
            line: 337,
            column: 61
          },
          end: {
            line: 337,
            column: 63
          }
        }],
        line: 337
      },
      "17": {
        loc: {
          start: {
            line: 385,
            column: 25
          },
          end: {
            line: 385,
            column: 114
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 385,
            column: 25
          },
          end: {
            line: 385,
            column: 67
          }
        }, {
          start: {
            line: 385,
            column: 71
          },
          end: {
            line: 385,
            column: 114
          }
        }],
        line: 385
      },
      "18": {
        loc: {
          start: {
            line: 386,
            column: 17
          },
          end: {
            line: 386,
            column: 101
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 386,
            column: 17
          },
          end: {
            line: 386,
            column: 59
          }
        }, {
          start: {
            line: 386,
            column: 63
          },
          end: {
            line: 386,
            column: 101
          }
        }],
        line: 386
      },
      "19": {
        loc: {
          start: {
            line: 387,
            column: 20
          },
          end: {
            line: 387,
            column: 113
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 387,
            column: 20
          },
          end: {
            line: 387,
            column: 65
          }
        }, {
          start: {
            line: 387,
            column: 69
          },
          end: {
            line: 387,
            column: 113
          }
        }],
        line: 387
      },
      "20": {
        loc: {
          start: {
            line: 388,
            column: 23
          },
          end: {
            line: 388,
            column: 125
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 388,
            column: 23
          },
          end: {
            line: 388,
            column: 71
          }
        }, {
          start: {
            line: 388,
            column: 75
          },
          end: {
            line: 388,
            column: 125
          }
        }],
        line: 388
      },
      "21": {
        loc: {
          start: {
            line: 393,
            column: 21
          },
          end: {
            line: 393,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 393,
            column: 21
          },
          end: {
            line: 393,
            column: 64
          }
        }, {
          start: {
            line: 393,
            column: 68
          },
          end: {
            line: 393,
            column: 117
          }
        }],
        line: 393
      },
      "22": {
        loc: {
          start: {
            line: 394,
            column: 18
          },
          end: {
            line: 394,
            column: 102
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 394,
            column: 18
          },
          end: {
            line: 394,
            column: 58
          }
        }, {
          start: {
            line: 394,
            column: 62
          },
          end: {
            line: 394,
            column: 102
          }
        }],
        line: 394
      },
      "23": {
        loc: {
          start: {
            line: 403,
            column: 27
          },
          end: {
            line: 403,
            column: 124
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 403,
            column: 27
          },
          end: {
            line: 403,
            column: 70
          }
        }, {
          start: {
            line: 403,
            column: 74
          },
          end: {
            line: 403,
            column: 124
          }
        }],
        line: 403
      },
      "24": {
        loc: {
          start: {
            line: 404,
            column: 19
          },
          end: {
            line: 404,
            column: 117
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 404,
            column: 19
          },
          end: {
            line: 404,
            column: 60
          }
        }, {
          start: {
            line: 404,
            column: 64
          },
          end: {
            line: 404,
            column: 117
          }
        }],
        line: 404
      },
      "25": {
        loc: {
          start: {
            line: 405,
            column: 27
          },
          end: {
            line: 405,
            column: 107
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 405,
            column: 27
          },
          end: {
            line: 405,
            column: 72
          }
        }, {
          start: {
            line: 405,
            column: 76
          },
          end: {
            line: 405,
            column: 107
          }
        }],
        line: 405
      },
      "26": {
        loc: {
          start: {
            line: 406,
            column: 27
          },
          end: {
            line: 406,
            column: 119
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 406,
            column: 27
          },
          end: {
            line: 406,
            column: 69
          }
        }, {
          start: {
            line: 406,
            column: 73
          },
          end: {
            line: 406,
            column: 119
          }
        }],
        line: 406
      },
      "27": {
        loc: {
          start: {
            line: 407,
            column: 25
          },
          end: {
            line: 407,
            column: 116
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 407,
            column: 25
          },
          end: {
            line: 407,
            column: 67
          }
        }, {
          start: {
            line: 407,
            column: 71
          },
          end: {
            line: 407,
            column: 116
          }
        }],
        line: 407
      },
      "28": {
        loc: {
          start: {
            line: 416,
            column: 20
          },
          end: {
            line: 416,
            column: 128
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 416,
            column: 20
          },
          end: {
            line: 416,
            column: 60
          }
        }, {
          start: {
            line: 416,
            column: 64
          },
          end: {
            line: 416,
            column: 128
          }
        }],
        line: 416
      },
      "29": {
        loc: {
          start: {
            line: 441,
            column: 6
          },
          end: {
            line: 443,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 441,
            column: 6
          },
          end: {
            line: 443,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 441
      },
      "30": {
        loc: {
          start: {
            line: 442,
            column: 15
          },
          end: {
            line: 442,
            column: 35
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 442,
            column: 15
          },
          end: {
            line: 442,
            column: 27
          }
        }, {
          start: {
            line: 442,
            column: 31
          },
          end: {
            line: 442,
            column: 35
          }
        }],
        line: 442
      },
      "31": {
        loc: {
          start: {
            line: 457,
            column: 6
          },
          end: {
            line: 460,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 457,
            column: 6
          },
          end: {
            line: 460,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 457
      },
      "32": {
        loc: {
          start: {
            line: 462,
            column: 6
          },
          end: {
            line: 466,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 462,
            column: 6
          },
          end: {
            line: 466,
            column: 7
          }
        }, {
          start: {
            line: 464,
            column: 13
          },
          end: {
            line: 466,
            column: 7
          }
        }],
        line: 462
      },
      "33": {
        loc: {
          start: {
            line: 462,
            column: 10
          },
          end: {
            line: 462,
            column: 93
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 462,
            column: 10
          },
          end: {
            line: 462,
            column: 19
          }
        }, {
          start: {
            line: 462,
            column: 24
          },
          end: {
            line: 462,
            column: 44
          }
        }, {
          start: {
            line: 462,
            column: 48
          },
          end: {
            line: 462,
            column: 68
          }
        }, {
          start: {
            line: 462,
            column: 72
          },
          end: {
            line: 462,
            column: 92
          }
        }],
        line: 462
      },
      "34": {
        loc: {
          start: {
            line: 464,
            column: 13
          },
          end: {
            line: 466,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 464,
            column: 13
          },
          end: {
            line: 466,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 464
      },
      "35": {
        loc: {
          start: {
            line: 464,
            column: 17
          },
          end: {
            line: 464,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 464,
            column: 17
          },
          end: {
            line: 464,
            column: 26
          }
        }, {
          start: {
            line: 464,
            column: 30
          },
          end: {
            line: 464,
            column: 48
          }
        }],
        line: 464
      },
      "36": {
        loc: {
          start: {
            line: 469,
            column: 11
          },
          end: {
            line: 469,
            column: 42
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 469,
            column: 30
          },
          end: {
            line: 469,
            column: 35
          }
        }, {
          start: {
            line: 469,
            column: 38
          },
          end: {
            line: 469,
            column: 42
          }
        }],
        line: 469
      },
      "37": {
        loc: {
          start: {
            line: 495,
            column: 30
          },
          end: {
            line: 495,
            column: 89
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 495,
            column: 67
          },
          end: {
            line: 495,
            column: 73
          }
        }, {
          start: {
            line: 495,
            column: 76
          },
          end: {
            line: 495,
            column: 89
          }
        }],
        line: 495
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0],
      "23": [0, 0],
      "24": [0, 0],
      "25": [0, 0],
      "26": [0, 0],
      "27": [0, 0],
      "28": [0, 0],
      "29": [0, 0],
      "30": [0, 0],
      "31": [0, 0],
      "32": [0, 0],
      "33": [0, 0, 0, 0],
      "34": [0, 0],
      "35": [0, 0],
      "36": [0, 0],
      "37": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "f0c0628c99c351ca21a2cebb90bd1aae10f242c0"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2eowt29e6q = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2eowt29e6q();
import { handleError, logError } from "../../utils/errorHandling";
import env from "../../config/environment";
var OpenAIService = function () {
  function OpenAIService() {
    _classCallCheck(this, OpenAIService);
    this.apiBaseUrl = (cov_2eowt29e6q().s[0]++, 'https://api.openai.com/v1');
    this.model = (cov_2eowt29e6q().s[1]++, 'gpt-4');
    cov_2eowt29e6q().f[0]++;
    cov_2eowt29e6q().s[2]++;
    this.apiKey = (cov_2eowt29e6q().b[0][0]++, env.get('OPENAI_API_KEY')) || (cov_2eowt29e6q().b[0][1]++, '');
  }
  return _createClass(OpenAIService, [{
    key: "generateTennisCoaching",
    value: (function () {
      var _generateTennisCoaching = _asyncToGenerator(function* (request) {
        cov_2eowt29e6q().f[1]++;
        cov_2eowt29e6q().s[3]++;
        try {
          var _data$choices$;
          cov_2eowt29e6q().s[4]++;
          if (!this.apiKey) {
            cov_2eowt29e6q().b[1][0]++;
            cov_2eowt29e6q().s[5]++;
            throw new Error('OpenAI API key not configured');
          } else {
            cov_2eowt29e6q().b[1][1]++;
          }
          var prompt = (cov_2eowt29e6q().s[6]++, this.buildCoachingPrompt(request));
          var response = (cov_2eowt29e6q().s[7]++, yield fetch(`${this.apiBaseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.apiKey}`
            },
            body: JSON.stringify({
              model: this.model,
              messages: [{
                role: 'system',
                content: 'You are a professional tennis coach with 20+ years of experience coaching players at all levels. Provide detailed, actionable coaching advice based on technical analysis data.'
              }, {
                role: 'user',
                content: prompt
              }],
              temperature: 0.7,
              max_tokens: 1500
            })
          }));
          cov_2eowt29e6q().s[8]++;
          if (!response.ok) {
            var _error$error;
            cov_2eowt29e6q().b[2][0]++;
            var error = (cov_2eowt29e6q().s[9]++, yield response.json());
            cov_2eowt29e6q().s[10]++;
            throw new Error((cov_2eowt29e6q().b[3][0]++, (_error$error = error.error) == null ? void 0 : _error$error.message) || (cov_2eowt29e6q().b[3][1]++, 'OpenAI API request failed'));
          } else {
            cov_2eowt29e6q().b[2][1]++;
          }
          var data = (cov_2eowt29e6q().s[11]++, yield response.json());
          var content = (cov_2eowt29e6q().s[12]++, (_data$choices$ = data.choices[0]) == null || (_data$choices$ = _data$choices$.message) == null ? void 0 : _data$choices$.content);
          cov_2eowt29e6q().s[13]++;
          if (!content) {
            cov_2eowt29e6q().b[4][0]++;
            cov_2eowt29e6q().s[14]++;
            throw new Error('No response from OpenAI');
          } else {
            cov_2eowt29e6q().b[4][1]++;
          }
          cov_2eowt29e6q().s[15]++;
          return this.parseCoachingResponse(content);
        } catch (error) {
          var appError = (cov_2eowt29e6q().s[16]++, handleError(error, {
            showAlert: false
          }));
          cov_2eowt29e6q().s[17]++;
          logError(appError, {
            context: 'generateTennisCoaching',
            request: request
          });
          cov_2eowt29e6q().s[18]++;
          return this.getFallbackCoachingResponse(request);
        }
      });
      function generateTennisCoaching(_x) {
        return _generateTennisCoaching.apply(this, arguments);
      }
      return generateTennisCoaching;
    }())
  }, {
    key: "analyzeMatchPerformance",
    value: (function () {
      var _analyzeMatchPerformance = _asyncToGenerator(function* (request) {
        cov_2eowt29e6q().f[2]++;
        cov_2eowt29e6q().s[19]++;
        try {
          var _data$choices$2;
          cov_2eowt29e6q().s[20]++;
          if (!this.apiKey) {
            cov_2eowt29e6q().b[5][0]++;
            cov_2eowt29e6q().s[21]++;
            throw new Error('OpenAI API key not configured');
          } else {
            cov_2eowt29e6q().b[5][1]++;
          }
          var prompt = (cov_2eowt29e6q().s[22]++, this.buildMatchAnalysisPrompt(request));
          var response = (cov_2eowt29e6q().s[23]++, yield fetch(`${this.apiBaseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.apiKey}`
            },
            body: JSON.stringify({
              model: this.model,
              messages: [{
                role: 'system',
                content: 'You are a tennis analyst and coach. Analyze match statistics and provide insights for improvement.'
              }, {
                role: 'user',
                content: prompt
              }],
              temperature: 0.6,
              max_tokens: 1200
            })
          }));
          cov_2eowt29e6q().s[24]++;
          if (!response.ok) {
            var _error$error2;
            cov_2eowt29e6q().b[6][0]++;
            var error = (cov_2eowt29e6q().s[25]++, yield response.json());
            cov_2eowt29e6q().s[26]++;
            throw new Error((cov_2eowt29e6q().b[7][0]++, (_error$error2 = error.error) == null ? void 0 : _error$error2.message) || (cov_2eowt29e6q().b[7][1]++, 'OpenAI API request failed'));
          } else {
            cov_2eowt29e6q().b[6][1]++;
          }
          var data = (cov_2eowt29e6q().s[27]++, yield response.json());
          var content = (cov_2eowt29e6q().s[28]++, (_data$choices$2 = data.choices[0]) == null || (_data$choices$2 = _data$choices$2.message) == null ? void 0 : _data$choices$2.content);
          cov_2eowt29e6q().s[29]++;
          if (!content) {
            cov_2eowt29e6q().b[8][0]++;
            cov_2eowt29e6q().s[30]++;
            throw new Error('No response from OpenAI');
          } else {
            cov_2eowt29e6q().b[8][1]++;
          }
          cov_2eowt29e6q().s[31]++;
          return this.parseMatchAnalysisResponse(content);
        } catch (error) {
          var appError = (cov_2eowt29e6q().s[32]++, handleError(error, {
            showAlert: false
          }));
          cov_2eowt29e6q().s[33]++;
          logError(appError, {
            context: 'analyzeMatchPerformance',
            request: request
          });
          cov_2eowt29e6q().s[34]++;
          return this.getFallbackMatchAnalysis(request);
        }
      });
      function analyzeMatchPerformance(_x2) {
        return _analyzeMatchPerformance.apply(this, arguments);
      }
      return analyzeMatchPerformance;
    }())
  }, {
    key: "generateTrainingPlan",
    value: (function () {
      var _generateTrainingPlan = _asyncToGenerator(function* (request) {
        cov_2eowt29e6q().f[3]++;
        cov_2eowt29e6q().s[35]++;
        try {
          var _data$choices$3;
          cov_2eowt29e6q().s[36]++;
          if (!this.apiKey) {
            cov_2eowt29e6q().b[9][0]++;
            cov_2eowt29e6q().s[37]++;
            throw new Error('OpenAI API key not configured');
          } else {
            cov_2eowt29e6q().b[9][1]++;
          }
          var prompt = (cov_2eowt29e6q().s[38]++, this.buildTrainingPlanPrompt(request));
          var response = (cov_2eowt29e6q().s[39]++, yield fetch(`${this.apiBaseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${this.apiKey}`
            },
            body: JSON.stringify({
              model: this.model,
              messages: [{
                role: 'system',
                content: 'You are a professional tennis coach specializing in creating personalized training programs. Design comprehensive training plans based on player profiles and goals.'
              }, {
                role: 'user',
                content: prompt
              }],
              temperature: 0.7,
              max_tokens: 2000
            })
          }));
          cov_2eowt29e6q().s[40]++;
          if (!response.ok) {
            var _error$error3;
            cov_2eowt29e6q().b[10][0]++;
            var error = (cov_2eowt29e6q().s[41]++, yield response.json());
            cov_2eowt29e6q().s[42]++;
            throw new Error((cov_2eowt29e6q().b[11][0]++, (_error$error3 = error.error) == null ? void 0 : _error$error3.message) || (cov_2eowt29e6q().b[11][1]++, 'OpenAI API request failed'));
          } else {
            cov_2eowt29e6q().b[10][1]++;
          }
          var data = (cov_2eowt29e6q().s[43]++, yield response.json());
          var content = (cov_2eowt29e6q().s[44]++, (_data$choices$3 = data.choices[0]) == null || (_data$choices$3 = _data$choices$3.message) == null ? void 0 : _data$choices$3.content);
          cov_2eowt29e6q().s[45]++;
          if (!content) {
            cov_2eowt29e6q().b[12][0]++;
            cov_2eowt29e6q().s[46]++;
            throw new Error('No response from OpenAI');
          } else {
            cov_2eowt29e6q().b[12][1]++;
          }
          cov_2eowt29e6q().s[47]++;
          return this.parseTrainingPlanResponse(content);
        } catch (error) {
          var appError = (cov_2eowt29e6q().s[48]++, handleError(error, {
            showAlert: false
          }));
          cov_2eowt29e6q().s[49]++;
          logError(appError, {
            context: 'generateTrainingPlan',
            request: request
          });
          cov_2eowt29e6q().s[50]++;
          return this.getFallbackTrainingPlan(request);
        }
      });
      function generateTrainingPlan(_x3) {
        return _generateTrainingPlan.apply(this, arguments);
      }
      return generateTrainingPlan;
    }())
  }, {
    key: "buildCoachingPrompt",
    value: function buildCoachingPrompt(request) {
      cov_2eowt29e6q().f[4]++;
      cov_2eowt29e6q().s[51]++;
      return `
Analyze this tennis stroke and provide coaching advice:

Stroke Type: ${request.strokeType}
Player Level: ${(cov_2eowt29e6q().b[13][0]++, request.userLevel) || (cov_2eowt29e6q().b[13][1]++, 'intermediate')}

Biomechanics Scores (0-100):
- Preparation: ${request.biomechanicsScores.preparation}
- Execution: ${request.biomechanicsScores.execution}
- Follow-through: ${request.biomechanicsScores.followThrough}
- Timing: ${request.biomechanicsScores.timing}
- Balance: ${request.biomechanicsScores.balance}

Technical Analysis:
- Body Rotation: ${request.technicalAnalysis.bodyRotation}/100
- Weight Transfer: ${request.technicalAnalysis.weightTransfer}/100
- Footwork: ${request.technicalAnalysis.footwork}/100
- Contact Point: ${request.technicalAnalysis.contactPoint.optimal ? (cov_2eowt29e6q().b[14][0]++, 'Optimal') : (cov_2eowt29e6q().b[14][1]++, 'Needs improvement')}

${request.specificConcerns ? (cov_2eowt29e6q().b[15][0]++, `Specific Concerns: ${request.specificConcerns.join(', ')}`) : (cov_2eowt29e6q().b[15][1]++, '')}

Please provide:
1. Overall assessment
2. Top 3 strengths
3. Top 3 areas for improvement
4. Specific recommendations
5. 2-3 drill suggestions with difficulty levels
6. Technical tips
7. Mental game advice

Format your response clearly with sections.
    `.trim();
    }
  }, {
    key: "buildMatchAnalysisPrompt",
    value: function buildMatchAnalysisPrompt(request) {
      cov_2eowt29e6q().f[5]++;
      cov_2eowt29e6q().s[52]++;
      return `
Analyze this tennis match performance:

Match Result: ${request.matchData.result.toUpperCase()}
Score: ${request.matchData.score}
Duration: ${request.matchData.duration} minutes
Surface: ${request.matchData.surface}
Opponent Level: ${request.matchData.opponentLevel}

Statistics:
- Aces: ${request.statistics.aces}
- Double Faults: ${request.statistics.doubleFaults}
- First Serve %: ${request.statistics.firstServePercentage}%
- Winners: ${request.statistics.winnersCount}
- Unforced Errors: ${request.statistics.unforcedErrors}
- Break Points Converted: ${request.statistics.breakPointsConverted}/${request.statistics.breakPointsFaced}

${request.userNotes ? (cov_2eowt29e6q().b[16][0]++, `Player Notes: ${request.userNotes}`) : (cov_2eowt29e6q().b[16][1]++, '')}

Please provide:
1. Performance analysis
2. Key insights from the match
3. Areas for improvement
4. Tactical suggestions
5. Strategy for next match

Be specific and actionable in your advice.
    `.trim();
    }
  }, {
    key: "buildTrainingPlanPrompt",
    value: function buildTrainingPlanPrompt(request) {
      cov_2eowt29e6q().f[6]++;
      cov_2eowt29e6q().s[53]++;
      return `
Create a ${request.timeframe}-week tennis training plan:

Player Profile:
- Level: ${request.userProfile.level}
- Goals: ${request.userProfile.goals.join(', ')}
- Available Time: ${request.userProfile.availableTime} hours/week
- Strengths: ${request.userProfile.strengths.join(', ')}
- Weaknesses: ${request.userProfile.weaknesses.join(', ')}

Focus Areas: ${request.focusAreas.join(', ')}

Please provide:
1. Plan overview
2. Weekly schedule breakdown
3. Progress milestones
4. Session types and activities
5. Assessment criteria

Make it practical and progressive.
    `.trim();
    }
  }, {
    key: "parseCoachingResponse",
    value: function parseCoachingResponse(content) {
      cov_2eowt29e6q().f[7]++;
      var sections = (cov_2eowt29e6q().s[54]++, content.split('\n\n'));
      cov_2eowt29e6q().s[55]++;
      return {
        overallAssessment: (cov_2eowt29e6q().b[17][0]++, this.extractSection(content, 'assessment')) || (cov_2eowt29e6q().b[17][1]++, 'Good technique with room for improvement.'),
        strengths: (cov_2eowt29e6q().b[18][0]++, this.extractListItems(content, 'strength')) || (cov_2eowt29e6q().b[18][1]++, ['Consistent contact', 'Good balance']),
        improvements: (cov_2eowt29e6q().b[19][0]++, this.extractListItems(content, 'improvement')) || (cov_2eowt29e6q().b[19][1]++, ['Work on follow-through', 'Improve timing']),
        recommendations: (cov_2eowt29e6q().b[20][0]++, this.extractListItems(content, 'recommendation')) || (cov_2eowt29e6q().b[20][1]++, ['Practice with a coach', 'Focus on fundamentals']),
        drillSuggestions: [{
          name: 'Shadow Swings',
          description: 'Practice stroke motion without ball',
          difficulty: 'easy',
          duration: '10 minutes'
        }, {
          name: 'Wall Practice',
          description: 'Hit against wall for consistency',
          difficulty: 'medium',
          duration: '15 minutes'
        }],
        technicalTips: (cov_2eowt29e6q().b[21][0]++, this.extractListItems(content, 'technical')) || (cov_2eowt29e6q().b[21][1]++, ['Keep eye on ball', 'Follow through completely']),
        mentalTips: (cov_2eowt29e6q().b[22][0]++, this.extractListItems(content, 'mental')) || (cov_2eowt29e6q().b[22][1]++, ['Stay focused', 'Trust your technique'])
      };
    }
  }, {
    key: "parseMatchAnalysisResponse",
    value: function parseMatchAnalysisResponse(content) {
      cov_2eowt29e6q().f[8]++;
      cov_2eowt29e6q().s[56]++;
      return {
        performanceAnalysis: (cov_2eowt29e6q().b[23][0]++, this.extractSection(content, 'performance')) || (cov_2eowt29e6q().b[23][1]++, 'Solid overall performance with areas to improve.'),
        keyInsights: (cov_2eowt29e6q().b[24][0]++, this.extractListItems(content, 'insight')) || (cov_2eowt29e6q().b[24][1]++, ['Good serving performance', 'Need to reduce errors']),
        areasForImprovement: (cov_2eowt29e6q().b[25][0]++, this.extractListItems(content, 'improvement')) || (cov_2eowt29e6q().b[25][1]++, ['Return of serve', 'Net play']),
        tacticalSuggestions: (cov_2eowt29e6q().b[26][0]++, this.extractListItems(content, 'tactical')) || (cov_2eowt29e6q().b[26][1]++, ['Vary serve placement', 'Attack short balls']),
        nextMatchStrategy: (cov_2eowt29e6q().b[27][0]++, this.extractListItems(content, 'strategy')) || (cov_2eowt29e6q().b[27][1]++, ['Focus on consistency', 'Play to strengths'])
      };
    }
  }, {
    key: "parseTrainingPlanResponse",
    value: function parseTrainingPlanResponse(content) {
      cov_2eowt29e6q().f[9]++;
      cov_2eowt29e6q().s[57]++;
      return {
        planOverview: (cov_2eowt29e6q().b[28][0]++, this.extractSection(content, 'overview')) || (cov_2eowt29e6q().b[28][1]++, 'Comprehensive training plan focusing on technique and fitness.'),
        weeklySchedule: [{
          week: 1,
          focus: 'Foundation',
          sessions: [{
            day: 'Monday',
            type: 'technique',
            duration: 60,
            activities: ['Forehand practice', 'Footwork drills']
          }, {
            day: 'Wednesday',
            type: 'fitness',
            duration: 45,
            activities: ['Cardio', 'Agility training']
          }, {
            day: 'Friday',
            type: 'match_play',
            duration: 90,
            activities: ['Practice match', 'Point play']
          }]
        }],
        progressMilestones: [{
          week: 2,
          milestone: 'Improved consistency',
          assessmentCriteria: ['10 consecutive forehands', 'Proper footwork']
        }, {
          week: 4,
          milestone: 'Match readiness',
          assessmentCriteria: ['Competitive match play', 'Strategic thinking']
        }]
      };
    }
  }, {
    key: "extractSection",
    value: function extractSection(content, keyword) {
      cov_2eowt29e6q().f[10]++;
      var lines = (cov_2eowt29e6q().s[58]++, content.split('\n'));
      cov_2eowt29e6q().s[59]++;
      for (var i = (cov_2eowt29e6q().s[60]++, 0); i < lines.length; i++) {
        cov_2eowt29e6q().s[61]++;
        if (lines[i].toLowerCase().includes(keyword)) {
          cov_2eowt29e6q().b[29][0]++;
          cov_2eowt29e6q().s[62]++;
          return (cov_2eowt29e6q().b[30][0]++, lines[i + 1]) || (cov_2eowt29e6q().b[30][1]++, null);
        } else {
          cov_2eowt29e6q().b[29][1]++;
        }
      }
      cov_2eowt29e6q().s[63]++;
      return null;
    }
  }, {
    key: "extractListItems",
    value: function extractListItems(content, keyword) {
      cov_2eowt29e6q().f[11]++;
      var lines = (cov_2eowt29e6q().s[64]++, content.split('\n'));
      var items = (cov_2eowt29e6q().s[65]++, []);
      var inSection = (cov_2eowt29e6q().s[66]++, false);
      cov_2eowt29e6q().s[67]++;
      for (var line of lines) {
        cov_2eowt29e6q().s[68]++;
        if (line.toLowerCase().includes(keyword)) {
          cov_2eowt29e6q().b[31][0]++;
          cov_2eowt29e6q().s[69]++;
          inSection = true;
          cov_2eowt29e6q().s[70]++;
          continue;
        } else {
          cov_2eowt29e6q().b[31][1]++;
        }
        cov_2eowt29e6q().s[71]++;
        if ((cov_2eowt29e6q().b[33][0]++, inSection) && ((cov_2eowt29e6q().b[33][1]++, line.startsWith('-')) || (cov_2eowt29e6q().b[33][2]++, line.startsWith('•')) || (cov_2eowt29e6q().b[33][3]++, line.match(/^\d+\./)))) {
          cov_2eowt29e6q().b[32][0]++;
          cov_2eowt29e6q().s[72]++;
          items.push(line.replace(/^[-•\d.]\s*/, '').trim());
        } else {
          cov_2eowt29e6q().b[32][1]++;
          cov_2eowt29e6q().s[73]++;
          if ((cov_2eowt29e6q().b[35][0]++, inSection) && (cov_2eowt29e6q().b[35][1]++, line.trim() === '')) {
            cov_2eowt29e6q().b[34][0]++;
            cov_2eowt29e6q().s[74]++;
            break;
          } else {
            cov_2eowt29e6q().b[34][1]++;
          }
        }
      }
      cov_2eowt29e6q().s[75]++;
      return items.length > 0 ? (cov_2eowt29e6q().b[36][0]++, items) : (cov_2eowt29e6q().b[36][1]++, null);
    }
  }, {
    key: "getFallbackCoachingResponse",
    value: function getFallbackCoachingResponse(request) {
      cov_2eowt29e6q().f[12]++;
      cov_2eowt29e6q().s[76]++;
      return {
        overallAssessment: `Your ${request.strokeType} shows good fundamentals with room for improvement in timing and consistency.`,
        strengths: ['Good preparation', 'Consistent contact point'],
        improvements: ['Improve follow-through', 'Work on timing', 'Enhance balance'],
        recommendations: ['Practice with a coach', 'Focus on slow, controlled swings', 'Work on footwork'],
        drillSuggestions: [{
          name: 'Shadow Swings',
          description: 'Practice stroke motion without ball',
          difficulty: 'easy',
          duration: '10 minutes'
        }, {
          name: 'Wall Practice',
          description: 'Hit against wall for consistency',
          difficulty: 'medium',
          duration: '15 minutes'
        }],
        technicalTips: ['Keep your eye on the ball', 'Complete your follow-through', 'Stay balanced'],
        mentalTips: ['Stay relaxed', 'Trust your technique', 'Focus on process, not outcome']
      };
    }
  }, {
    key: "getFallbackMatchAnalysis",
    value: function getFallbackMatchAnalysis(request) {
      cov_2eowt29e6q().f[13]++;
      cov_2eowt29e6q().s[77]++;
      return {
        performanceAnalysis: `${request.matchData.result === 'win' ? (cov_2eowt29e6q().b[37][0]++, 'Good') : (cov_2eowt29e6q().b[37][1]++, 'Challenging')} match with valuable learning opportunities.`,
        keyInsights: ['Serving was a key factor', 'Consistency played a major role'],
        areasForImprovement: ['Return of serve', 'Reducing unforced errors', 'Net play'],
        tacticalSuggestions: ['Vary serve placement', 'Be more aggressive on short balls', 'Improve court positioning'],
        nextMatchStrategy: ['Focus on consistency', 'Play to your strengths', 'Stay mentally tough']
      };
    }
  }, {
    key: "getFallbackTrainingPlan",
    value: function getFallbackTrainingPlan(request) {
      cov_2eowt29e6q().f[14]++;
      cov_2eowt29e6q().s[78]++;
      return {
        planOverview: `${request.timeframe}-week progressive training plan focusing on ${request.focusAreas.join(' and ')}.`,
        weeklySchedule: [{
          week: 1,
          focus: 'Foundation Building',
          sessions: [{
            day: 'Monday',
            type: 'technique',
            duration: 60,
            activities: ['Stroke fundamentals', 'Footwork']
          }, {
            day: 'Wednesday',
            type: 'fitness',
            duration: 45,
            activities: ['Cardio training', 'Agility']
          }, {
            day: 'Friday',
            type: 'match_play',
            duration: 90,
            activities: ['Practice points', 'Strategy']
          }]
        }],
        progressMilestones: [{
          week: 2,
          milestone: 'Improved stroke consistency',
          assessmentCriteria: ['Better timing', 'Fewer errors']
        }, {
          week: 4,
          milestone: 'Enhanced match play',
          assessmentCriteria: ['Strategic thinking', 'Competitive performance']
        }]
      };
    }
  }]);
}();
export var openAIService = (cov_2eowt29e6q().s[79]++, new OpenAIService());
export default openAIService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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