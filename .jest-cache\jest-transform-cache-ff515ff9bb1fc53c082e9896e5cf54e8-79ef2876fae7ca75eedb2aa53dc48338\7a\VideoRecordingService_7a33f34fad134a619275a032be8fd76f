2bc49ade56f4198b0dd8eca46012bd2a
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_t4rs1xx0t() {
  var path = "C:\\_SaaS\\AceMind\\project\\src\\services\\video\\VideoRecordingService.ts";
  var hash = "c2d81470d07194971cd0f212eac41cba598a7be3";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\src\\services\\video\\VideoRecordingService.ts",
    statementMap: {
      "0": {
        start: {
          line: 35,
          column: 37
        },
        end: {
          line: 35,
          column: 41
        }
      },
      "1": {
        start: {
          line: 36,
          column: 24
        },
        end: {
          line: 36,
          column: 29
        }
      },
      "2": {
        start: {
          line: 37,
          column: 21
        },
        end: {
          line: 37,
          column: 26
        }
      },
      "3": {
        start: {
          line: 38,
          column: 31
        },
        end: {
          line: 38,
          column: 32
        }
      },
      "4": {
        start: {
          line: 39,
          column: 27
        },
        end: {
          line: 39,
          column: 28
        }
      },
      "5": {
        start: {
          line: 40,
          column: 47
        },
        end: {
          line: 40,
          column: 51
        }
      },
      "6": {
        start: {
          line: 41,
          column: 77
        },
        end: {
          line: 41,
          column: 81
        }
      },
      "7": {
        start: {
          line: 42,
          column: 52
        },
        end: {
          line: 42,
          column: 56
        }
      },
      "8": {
        start: {
          line: 48,
          column: 4
        },
        end: {
          line: 60,
          column: 5
        }
      },
      "9": {
        start: {
          line: 49,
          column: 26
        },
        end: {
          line: 49,
          column: 57
        }
      },
      "10": {
        start: {
          line: 50,
          column: 6
        },
        end: {
          line: 52,
          column: 7
        }
      },
      "11": {
        start: {
          line: 51,
          column: 8
        },
        end: {
          line: 51,
          column: 94
        }
      },
      "12": {
        start: {
          line: 54,
          column: 6
        },
        end: {
          line: 54,
          column: 53
        }
      },
      "13": {
        start: {
          line: 56,
          column: 6
        },
        end: {
          line: 56,
          column: 51
        }
      },
      "14": {
        start: {
          line: 58,
          column: 6
        },
        end: {
          line: 58,
          column: 76
        }
      },
      "15": {
        start: {
          line: 59,
          column: 6
        },
        end: {
          line: 59,
          column: 18
        }
      },
      "16": {
        start: {
          line: 67,
          column: 4
        },
        end: {
          line: 84,
          column: 5
        }
      },
      "17": {
        start: {
          line: 68,
          column: 31
        },
        end: {
          line: 68,
          column: 75
        }
      },
      "18": {
        start: {
          line: 69,
          column: 35
        },
        end: {
          line: 69,
          column: 83
        }
      },
      "19": {
        start: {
          line: 70,
          column: 37
        },
        end: {
          line: 70,
          column: 81
        }
      },
      "20": {
        start: {
          line: 72,
          column: 6
        },
        end: {
          line: 76,
          column: 8
        }
      },
      "21": {
        start: {
          line: 78,
          column: 6
        },
        end: {
          line: 78,
          column: 61
        }
      },
      "22": {
        start: {
          line: 79,
          column: 6
        },
        end: {
          line: 83,
          column: 8
        }
      },
      "23": {
        start: {
          line: 91,
          column: 4
        },
        end: {
          line: 91,
          column: 25
        }
      },
      "24": {
        start: {
          line: 98,
          column: 4
        },
        end: {
          line: 100,
          column: 5
        }
      },
      "25": {
        start: {
          line: 99,
          column: 6
        },
        end: {
          line: 99,
          column: 50
        }
      },
      "26": {
        start: {
          line: 102,
          column: 4
        },
        end: {
          line: 104,
          column: 5
        }
      },
      "27": {
        start: {
          line: 103,
          column: 6
        },
        end: {
          line: 103,
          column: 55
        }
      },
      "28": {
        start: {
          line: 106,
          column: 4
        },
        end: {
          line: 129,
          column: 5
        }
      },
      "29": {
        start: {
          line: 107,
          column: 6
        },
        end: {
          line: 107,
          column: 56
        }
      },
      "30": {
        start: {
          line: 109,
          column: 31
        },
        end: {
          line: 109,
          column: 63
        }
      },
      "31": {
        start: {
          line: 111,
          column: 6
        },
        end: {
          line: 111,
          column: 30
        }
      },
      "32": {
        start: {
          line: 112,
          column: 6
        },
        end: {
          line: 112,
          column: 28
        }
      },
      "33": {
        start: {
          line: 113,
          column: 6
        },
        end: {
          line: 113,
          column: 43
        }
      },
      "34": {
        start: {
          line: 114,
          column: 6
        },
        end: {
          line: 114,
          column: 30
        }
      },
      "35": {
        start: {
          line: 116,
          column: 31
        },
        end: {
          line: 116,
          column: 75
        }
      },
      "36": {
        start: {
          line: 119,
          column: 6
        },
        end: {
          line: 119,
          column: 37
        }
      },
      "37": {
        start: {
          line: 121,
          column: 21
        },
        end: {
          line: 121,
          column: 43
        }
      },
      "38": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 122,
          column: 44
        }
      },
      "39": {
        start: {
          line: 124,
          column: 6
        },
        end: {
          line: 124,
          column: 54
        }
      },
      "40": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 126,
          column: 31
        }
      },
      "41": {
        start: {
          line: 127,
          column: 6
        },
        end: {
          line: 127,
          column: 57
        }
      },
      "42": {
        start: {
          line: 128,
          column: 6
        },
        end: {
          line: 128,
          column: 18
        }
      },
      "43": {
        start: {
          line: 136,
          column: 4
        },
        end: {
          line: 138,
          column: 5
        }
      },
      "44": {
        start: {
          line: 137,
          column: 6
        },
        end: {
          line: 137,
          column: 53
        }
      },
      "45": {
        start: {
          line: 140,
          column: 4
        },
        end: {
          line: 179,
          column: 5
        }
      },
      "46": {
        start: {
          line: 141,
          column: 6
        },
        end: {
          line: 141,
          column: 55
        }
      },
      "47": {
        start: {
          line: 143,
          column: 6
        },
        end: {
          line: 143,
          column: 37
        }
      },
      "48": {
        start: {
          line: 144,
          column: 6
        },
        end: {
          line: 144,
          column: 31
        }
      },
      "49": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 145,
          column: 28
        }
      },
      "50": {
        start: {
          line: 148,
          column: 6
        },
        end: {
          line: 148,
          column: 36
        }
      },
      "51": {
        start: {
          line: 150,
          column: 6
        },
        end: {
          line: 152,
          column: 7
        }
      },
      "52": {
        start: {
          line: 151,
          column: 8
        },
        end: {
          line: 151,
          column: 55
        }
      },
      "53": {
        start: {
          line: 155,
          column: 23
        },
        end: {
          line: 155,
          column: 78
        }
      },
      "54": {
        start: {
          line: 156,
          column: 6
        },
        end: {
          line: 158,
          column: 7
        }
      },
      "55": {
        start: {
          line: 157,
          column: 8
        },
        end: {
          line: 157,
          column: 52
        }
      },
      "56": {
        start: {
          line: 160,
          column: 23
        },
        end: {
          line: 160,
          column: 90
        }
      },
      "57": {
        start: {
          line: 163,
          column: 24
        },
        end: {
          line: 163,
          column: 78
        }
      },
      "58": {
        start: {
          line: 165,
          column: 43
        },
        end: {
          line: 172,
          column: 7
        }
      },
      "59": {
        start: {
          line: 174,
          column: 6
        },
        end: {
          line: 174,
          column: 53
        }
      },
      "60": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 175,
          column: 20
        }
      },
      "61": {
        start: {
          line: 177,
          column: 6
        },
        end: {
          line: 177,
          column: 56
        }
      },
      "62": {
        start: {
          line: 178,
          column: 6
        },
        end: {
          line: 178,
          column: 18
        }
      },
      "63": {
        start: {
          line: 186,
          column: 4
        },
        end: {
          line: 188,
          column: 5
        }
      },
      "64": {
        start: {
          line: 187,
          column: 6
        },
        end: {
          line: 187,
          column: 13
        }
      },
      "65": {
        start: {
          line: 190,
          column: 4
        },
        end: {
          line: 198,
          column: 5
        }
      },
      "66": {
        start: {
          line: 193,
          column: 6
        },
        end: {
          line: 193,
          column: 27
        }
      },
      "67": {
        start: {
          line: 194,
          column: 6
        },
        end: {
          line: 194,
          column: 67
        }
      },
      "68": {
        start: {
          line: 196,
          column: 6
        },
        end: {
          line: 196,
          column: 57
        }
      },
      "69": {
        start: {
          line: 197,
          column: 6
        },
        end: {
          line: 197,
          column: 18
        }
      },
      "70": {
        start: {
          line: 205,
          column: 4
        },
        end: {
          line: 207,
          column: 5
        }
      },
      "71": {
        start: {
          line: 206,
          column: 6
        },
        end: {
          line: 206,
          column: 13
        }
      },
      "72": {
        start: {
          line: 209,
          column: 4
        },
        end: {
          line: 217,
          column: 5
        }
      },
      "73": {
        start: {
          line: 212,
          column: 6
        },
        end: {
          line: 212,
          column: 28
        }
      },
      "74": {
        start: {
          line: 213,
          column: 6
        },
        end: {
          line: 213,
          column: 68
        }
      },
      "75": {
        start: {
          line: 215,
          column: 6
        },
        end: {
          line: 215,
          column: 58
        }
      },
      "76": {
        start: {
          line: 216,
          column: 6
        },
        end: {
          line: 216,
          column: 18
        }
      },
      "77": {
        start: {
          line: 224,
          column: 24
        },
        end: {
          line: 224,
          column: 34
        }
      },
      "78": {
        start: {
          line: 225,
          column: 21
        },
        end: {
          line: 227,
          column: 9
        }
      },
      "79": {
        start: {
          line: 229,
          column: 4
        },
        end: {
          line: 234,
          column: 6
        }
      },
      "80": {
        start: {
          line: 241,
          column: 4
        },
        end: {
          line: 241,
          column: 37
        }
      },
      "81": {
        start: {
          line: 248,
          column: 4
        },
        end: {
          line: 259,
          column: 5
        }
      },
      "82": {
        start: {
          line: 249,
          column: 26
        },
        end: {
          line: 249,
          column: 70
        }
      },
      "83": {
        start: {
          line: 250,
          column: 6
        },
        end: {
          line: 252,
          column: 7
        }
      },
      "84": {
        start: {
          line: 251,
          column: 8
        },
        end: {
          line: 251,
          column: 75
        }
      },
      "85": {
        start: {
          line: 254,
          column: 20
        },
        end: {
          line: 254,
          column: 60
        }
      },
      "86": {
        start: {
          line: 255,
          column: 6
        },
        end: {
          line: 255,
          column: 23
        }
      },
      "87": {
        start: {
          line: 257,
          column: 6
        },
        end: {
          line: 257,
          column: 63
        }
      },
      "88": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 258,
          column: 18
        }
      },
      "89": {
        start: {
          line: 266,
          column: 4
        },
        end: {
          line: 307,
          column: 5
        }
      },
      "90": {
        start: {
          line: 267,
          column: 6
        },
        end: {
          line: 267,
          column: 52
        }
      },
      "91": {
        start: {
          line: 270,
          column: 23
        },
        end: {
          line: 270,
          column: 57
        }
      },
      "92": {
        start: {
          line: 271,
          column: 6
        },
        end: {
          line: 273,
          column: 7
        }
      },
      "93": {
        start: {
          line: 272,
          column: 8
        },
        end: {
          line: 272,
          column: 64
        }
      },
      "94": {
        start: {
          line: 275,
          column: 27
        },
        end: {
          line: 275,
          column: 45
        }
      },
      "95": {
        start: {
          line: 276,
          column: 6
        },
        end: {
          line: 276,
          column: 83
        }
      },
      "96": {
        start: {
          line: 279,
          column: 28
        },
        end: {
          line: 279,
          column: 86
        }
      },
      "97": {
        start: {
          line: 282,
          column: 34
        },
        end: {
          line: 286,
          column: 7
        }
      },
      "98": {
        start: {
          line: 288,
          column: 23
        },
        end: {
          line: 288,
          column: 51
        }
      },
      "99": {
        start: {
          line: 292,
          column: 6
        },
        end: {
          line: 295,
          column: 9
        }
      },
      "100": {
        start: {
          line: 297,
          column: 29
        },
        end: {
          line: 297,
          column: 73
        }
      },
      "101": {
        start: {
          line: 298,
          column: 29
        },
        end: {
          line: 298,
          column: 53
        }
      },
      "102": {
        start: {
          line: 300,
          column: 6
        },
        end: {
          line: 300,
          column: 82
        }
      },
      "103": {
        start: {
          line: 302,
          column: 6
        },
        end: {
          line: 302,
          column: 50
        }
      },
      "104": {
        start: {
          line: 303,
          column: 6
        },
        end: {
          line: 303,
          column: 27
        }
      },
      "105": {
        start: {
          line: 305,
          column: 6
        },
        end: {
          line: 305,
          column: 56
        }
      },
      "106": {
        start: {
          line: 306,
          column: 6
        },
        end: {
          line: 306,
          column: 18
        }
      },
      "107": {
        start: {
          line: 314,
          column: 4
        },
        end: {
          line: 332,
          column: 5
        }
      },
      "108": {
        start: {
          line: 316,
          column: 27
        },
        end: {
          line: 316,
          column: 84
        }
      },
      "109": {
        start: {
          line: 318,
          column: 6
        },
        end: {
          line: 318,
          column: 58
        }
      },
      "110": {
        start: {
          line: 328,
          column: 6
        },
        end: {
          line: 328,
          column: 16
        }
      },
      "111": {
        start: {
          line: 330,
          column: 6
        },
        end: {
          line: 330,
          column: 60
        }
      },
      "112": {
        start: {
          line: 331,
          column: 6
        },
        end: {
          line: 331,
          column: 16
        }
      },
      "113": {
        start: {
          line: 339,
          column: 53
        },
        end: {
          line: 344,
          column: 5
        }
      },
      "114": {
        start: {
          line: 346,
          column: 4
        },
        end: {
          line: 351,
          column: 6
        }
      },
      "115": {
        start: {
          line: 358,
          column: 4
        },
        end: {
          line: 360,
          column: 5
        }
      },
      "116": {
        start: {
          line: 359,
          column: 6
        },
        end: {
          line: 359,
          column: 43
        }
      },
      "117": {
        start: {
          line: 362,
          column: 4
        },
        end: {
          line: 367,
          column: 13
        }
      },
      "118": {
        start: {
          line: 363,
          column: 6
        },
        end: {
          line: 366,
          column: 7
        }
      },
      "119": {
        start: {
          line: 364,
          column: 25
        },
        end: {
          line: 364,
          column: 50
        }
      },
      "120": {
        start: {
          line: 365,
          column: 8
        },
        end: {
          line: 365,
          column: 40
        }
      },
      "121": {
        start: {
          line: 374,
          column: 4
        },
        end: {
          line: 377,
          column: 5
        }
      },
      "122": {
        start: {
          line: 375,
          column: 6
        },
        end: {
          line: 375,
          column: 43
        }
      },
      "123": {
        start: {
          line: 376,
          column: 6
        },
        end: {
          line: 376,
          column: 35
        }
      },
      "124": {
        start: {
          line: 384,
          column: 4
        },
        end: {
          line: 384,
          column: 34
        }
      },
      "125": {
        start: {
          line: 385,
          column: 4
        },
        end: {
          line: 385,
          column: 29
        }
      },
      "126": {
        start: {
          line: 386,
          column: 4
        },
        end: {
          line: 386,
          column: 26
        }
      },
      "127": {
        start: {
          line: 387,
          column: 4
        },
        end: {
          line: 387,
          column: 36
        }
      },
      "128": {
        start: {
          line: 388,
          column: 4
        },
        end: {
          line: 388,
          column: 33
        }
      },
      "129": {
        start: {
          line: 393,
          column: 37
        },
        end: {
          line: 393,
          column: 64
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 47,
            column: 2
          },
          end: {
            line: 47,
            column: 3
          }
        },
        loc: {
          start: {
            line: 47,
            column: 36
          },
          end: {
            line: 61,
            column: 3
          }
        },
        line: 47
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 66,
            column: 2
          },
          end: {
            line: 66,
            column: 3
          }
        },
        loc: {
          start: {
            line: 66,
            column: 57
          },
          end: {
            line: 85,
            column: 3
          }
        },
        line: 66
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 90,
            column: 2
          },
          end: {
            line: 90,
            column: 3
          }
        },
        loc: {
          start: {
            line: 90,
            column: 41
          },
          end: {
            line: 92,
            column: 3
          }
        },
        line: 90
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 97,
            column: 2
          },
          end: {
            line: 97,
            column: 3
          }
        },
        loc: {
          start: {
            line: 97,
            column: 68
          },
          end: {
            line: 130,
            column: 3
          }
        },
        line: 97
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 135,
            column: 2
          },
          end: {
            line: 135,
            column: 3
          }
        },
        loc: {
          start: {
            line: 135,
            column: 55
          },
          end: {
            line: 180,
            column: 3
          }
        },
        line: 135
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 185,
            column: 2
          },
          end: {
            line: 185,
            column: 3
          }
        },
        loc: {
          start: {
            line: 185,
            column: 40
          },
          end: {
            line: 199,
            column: 3
          }
        },
        line: 185
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 204,
            column: 2
          },
          end: {
            line: 204,
            column: 3
          }
        },
        loc: {
          start: {
            line: 204,
            column: 41
          },
          end: {
            line: 218,
            column: 3
          }
        },
        line: 204
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 223,
            column: 2
          },
          end: {
            line: 223,
            column: 3
          }
        },
        loc: {
          start: {
            line: 223,
            column: 42
          },
          end: {
            line: 235,
            column: 3
          }
        },
        line: 223
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 240,
            column: 2
          },
          end: {
            line: 240,
            column: 3
          }
        },
        loc: {
          start: {
            line: 240,
            column: 77
          },
          end: {
            line: 242,
            column: 3
          }
        },
        line: 240
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 247,
            column: 2
          },
          end: {
            line: 247,
            column: 3
          }
        },
        loc: {
          start: {
            line: 247,
            column: 52
          },
          end: {
            line: 260,
            column: 3
          }
        },
        line: 247
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 265,
            column: 2
          },
          end: {
            line: 265,
            column: 3
          }
        },
        loc: {
          start: {
            line: 265,
            column: 99
          },
          end: {
            line: 308,
            column: 3
          }
        },
        line: 265
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 313,
            column: 2
          },
          end: {
            line: 313,
            column: 3
          }
        },
        loc: {
          start: {
            line: 313,
            column: 64
          },
          end: {
            line: 333,
            column: 3
          }
        },
        line: 313
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 338,
            column: 2
          },
          end: {
            line: 338,
            column: 3
          }
        },
        loc: {
          start: {
            line: 338,
            column: 65
          },
          end: {
            line: 352,
            column: 3
          }
        },
        line: 338
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 357,
            column: 2
          },
          end: {
            line: 357,
            column: 3
          }
        },
        loc: {
          start: {
            line: 357,
            column: 42
          },
          end: {
            line: 368,
            column: 3
          }
        },
        line: 357
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 362,
            column: 40
          },
          end: {
            line: 362,
            column: 41
          }
        },
        loc: {
          start: {
            line: 362,
            column: 46
          },
          end: {
            line: 367,
            column: 5
          }
        },
        line: 362
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 373,
            column: 2
          },
          end: {
            line: 373,
            column: 3
          }
        },
        loc: {
          start: {
            line: 373,
            column: 41
          },
          end: {
            line: 378,
            column: 3
          }
        },
        line: 373
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 383,
            column: 2
          },
          end: {
            line: 383,
            column: 3
          }
        },
        loc: {
          start: {
            line: 383,
            column: 18
          },
          end: {
            line: 389,
            column: 3
          }
        },
        line: 383
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 50,
            column: 6
          },
          end: {
            line: 52,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 50,
            column: 6
          },
          end: {
            line: 52,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 50
      },
      "1": {
        loc: {
          start: {
            line: 50,
            column: 10
          },
          end: {
            line: 50,
            column: 56
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 50,
            column: 10
          },
          end: {
            line: 50,
            column: 29
          }
        }, {
          start: {
            line: 50,
            column: 33
          },
          end: {
            line: 50,
            column: 56
          }
        }],
        line: 50
      },
      "2": {
        loc: {
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 100,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 98,
            column: 4
          },
          end: {
            line: 100,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 98
      },
      "3": {
        loc: {
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 102,
            column: 4
          },
          end: {
            line: 104,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 102
      },
      "4": {
        loc: {
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 136,
            column: 4
          },
          end: {
            line: 138,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 136
      },
      "5": {
        loc: {
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 136,
            column: 44
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 136,
            column: 8
          },
          end: {
            line: 136,
            column: 23
          }
        }, {
          start: {
            line: 136,
            column: 27
          },
          end: {
            line: 136,
            column: 44
          }
        }],
        line: 136
      },
      "6": {
        loc: {
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 152,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 150,
            column: 6
          },
          end: {
            line: 152,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 150
      },
      "7": {
        loc: {
          start: {
            line: 156,
            column: 6
          },
          end: {
            line: 158,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 156,
            column: 6
          },
          end: {
            line: 158,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 156
      },
      "8": {
        loc: {
          start: {
            line: 168,
            column: 18
          },
          end: {
            line: 168,
            column: 36
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 168,
            column: 18
          },
          end: {
            line: 168,
            column: 31
          }
        }, {
          start: {
            line: 168,
            column: 35
          },
          end: {
            line: 168,
            column: 36
          }
        }],
        line: 168
      },
      "9": {
        loc: {
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 186,
            column: 4
          },
          end: {
            line: 188,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 186
      },
      "10": {
        loc: {
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 186,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 186,
            column: 8
          },
          end: {
            line: 186,
            column: 25
          }
        }, {
          start: {
            line: 186,
            column: 29
          },
          end: {
            line: 186,
            column: 42
          }
        }],
        line: 186
      },
      "11": {
        loc: {
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 205,
            column: 4
          },
          end: {
            line: 207,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 205
      },
      "12": {
        loc: {
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 205,
            column: 43
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 205,
            column: 8
          },
          end: {
            line: 205,
            column: 25
          }
        }, {
          start: {
            line: 205,
            column: 29
          },
          end: {
            line: 205,
            column: 43
          }
        }],
        line: 205
      },
      "13": {
        loc: {
          start: {
            line: 225,
            column: 21
          },
          end: {
            line: 227,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 226,
            column: 8
          },
          end: {
            line: 226,
            column: 76
          }
        }, {
          start: {
            line: 227,
            column: 8
          },
          end: {
            line: 227,
            column: 9
          }
        }],
        line: 225
      },
      "14": {
        loc: {
          start: {
            line: 250,
            column: 6
          },
          end: {
            line: 252,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 250,
            column: 6
          },
          end: {
            line: 252,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 250
      },
      "15": {
        loc: {
          start: {
            line: 265,
            column: 35
          },
          end: {
            line: 265,
            column: 80
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 265,
            column: 72
          },
          end: {
            line: 265,
            column: 80
          }
        }],
        line: 265
      },
      "16": {
        loc: {
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 271,
            column: 6
          },
          end: {
            line: 273,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 271
      },
      "17": {
        loc: {
          start: {
            line: 275,
            column: 27
          },
          end: {
            line: 275,
            column: 45
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 275,
            column: 27
          },
          end: {
            line: 275,
            column: 40
          }
        }, {
          start: {
            line: 275,
            column: 44
          },
          end: {
            line: 275,
            column: 45
          }
        }],
        line: 275
      },
      "18": {
        loc: {
          start: {
            line: 298,
            column: 29
          },
          end: {
            line: 298,
            column: 53
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 298,
            column: 29
          },
          end: {
            line: 298,
            column: 48
          }
        }, {
          start: {
            line: 298,
            column: 52
          },
          end: {
            line: 298,
            column: 53
          }
        }],
        line: 298
      },
      "19": {
        loc: {
          start: {
            line: 347,
            column: 15
          },
          end: {
            line: 347,
            column: 65
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 347,
            column: 15
          },
          end: {
            line: 347,
            column: 41
          }
        }, {
          start: {
            line: 347,
            column: 45
          },
          end: {
            line: 347,
            column: 65
          }
        }],
        line: 347
      },
      "20": {
        loc: {
          start: {
            line: 358,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 358,
            column: 4
          },
          end: {
            line: 360,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 358
      },
      "21": {
        loc: {
          start: {
            line: 363,
            column: 6
          },
          end: {
            line: 366,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 363,
            column: 6
          },
          end: {
            line: 366,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 363
      },
      "22": {
        loc: {
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 377,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 374,
            column: 4
          },
          end: {
            line: 377,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 374
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0,
      "113": 0,
      "114": 0,
      "115": 0,
      "116": 0,
      "117": 0,
      "118": 0,
      "119": 0,
      "120": 0,
      "121": 0,
      "122": 0,
      "123": 0,
      "124": 0,
      "125": 0,
      "126": 0,
      "127": 0,
      "128": 0,
      "129": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "c2d81470d07194971cd0f212eac41cba598a7be3"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_t4rs1xx0t = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_t4rs1xx0t();
import { Camera, VideoQuality } from 'expo-camera';
import * as MediaLibrary from 'expo-media-library';
import * as FileSystem from 'expo-file-system';
import { performanceMonitor } from "../../../utils/performance";
var VideoRecordingService = function () {
  function VideoRecordingService() {
    _classCallCheck(this, VideoRecordingService);
    this.cameraRef = (cov_t4rs1xx0t().s[0]++, null);
    this.isRecording = (cov_t4rs1xx0t().s[1]++, false);
    this.isPaused = (cov_t4rs1xx0t().s[2]++, false);
    this.recordingStartTime = (cov_t4rs1xx0t().s[3]++, 0);
    this.pausedDuration = (cov_t4rs1xx0t().s[4]++, 0);
    this.currentRecordingUri = (cov_t4rs1xx0t().s[5]++, null);
    this.progressCallback = (cov_t4rs1xx0t().s[6]++, null);
    this.progressInterval = (cov_t4rs1xx0t().s[7]++, null);
  }
  return _createClass(VideoRecordingService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* () {
        cov_t4rs1xx0t().f[0]++;
        cov_t4rs1xx0t().s[8]++;
        try {
          var permissions = (cov_t4rs1xx0t().s[9]++, yield this.requestPermissions());
          cov_t4rs1xx0t().s[10]++;
          if ((cov_t4rs1xx0t().b[1][0]++, !permissions.camera) || (cov_t4rs1xx0t().b[1][1]++, !permissions.microphone)) {
            cov_t4rs1xx0t().b[0][0]++;
            cov_t4rs1xx0t().s[11]++;
            throw new Error('Camera and microphone permissions are required for video recording');
          } else {
            cov_t4rs1xx0t().b[0][1]++;
          }
          cov_t4rs1xx0t().s[12]++;
          performanceMonitor.start('video_service_init');
          cov_t4rs1xx0t().s[13]++;
          performanceMonitor.end('video_service_init');
        } catch (error) {
          cov_t4rs1xx0t().s[14]++;
          console.error('Failed to initialize video recording service:', error);
          cov_t4rs1xx0t().s[15]++;
          throw error;
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "requestPermissions",
    value: (function () {
      var _requestPermissions = _asyncToGenerator(function* () {
        cov_t4rs1xx0t().f[1]++;
        cov_t4rs1xx0t().s[16]++;
        try {
          var cameraPermission = (cov_t4rs1xx0t().s[17]++, yield Camera.requestCameraPermissionsAsync());
          var microphonePermission = (cov_t4rs1xx0t().s[18]++, yield Camera.requestMicrophonePermissionsAsync());
          var mediaLibraryPermission = (cov_t4rs1xx0t().s[19]++, yield MediaLibrary.requestPermissionsAsync());
          cov_t4rs1xx0t().s[20]++;
          return {
            camera: cameraPermission.status === 'granted',
            microphone: microphonePermission.status === 'granted',
            mediaLibrary: mediaLibraryPermission.status === 'granted'
          };
        } catch (error) {
          cov_t4rs1xx0t().s[21]++;
          console.error('Failed to request permissions:', error);
          cov_t4rs1xx0t().s[22]++;
          return {
            camera: false,
            microphone: false,
            mediaLibrary: false
          };
        }
      });
      function requestPermissions() {
        return _requestPermissions.apply(this, arguments);
      }
      return requestPermissions;
    }())
  }, {
    key: "setCameraRef",
    value: function setCameraRef(ref) {
      cov_t4rs1xx0t().f[2]++;
      cov_t4rs1xx0t().s[23]++;
      this.cameraRef = ref;
    }
  }, {
    key: "startRecording",
    value: (function () {
      var _startRecording = _asyncToGenerator(function* (config) {
        cov_t4rs1xx0t().f[3]++;
        cov_t4rs1xx0t().s[24]++;
        if (!this.cameraRef) {
          cov_t4rs1xx0t().b[2][0]++;
          cov_t4rs1xx0t().s[25]++;
          throw new Error('Camera reference not set');
        } else {
          cov_t4rs1xx0t().b[2][1]++;
        }
        cov_t4rs1xx0t().s[26]++;
        if (this.isRecording) {
          cov_t4rs1xx0t().b[3][0]++;
          cov_t4rs1xx0t().s[27]++;
          throw new Error('Recording already in progress');
        } else {
          cov_t4rs1xx0t().b[3][1]++;
        }
        cov_t4rs1xx0t().s[28]++;
        try {
          cov_t4rs1xx0t().s[29]++;
          performanceMonitor.start('video_recording_start');
          var recordingOptions = (cov_t4rs1xx0t().s[30]++, this.getRecordingOptions(config));
          cov_t4rs1xx0t().s[31]++;
          this.isRecording = true;
          cov_t4rs1xx0t().s[32]++;
          this.isPaused = false;
          cov_t4rs1xx0t().s[33]++;
          this.recordingStartTime = Date.now();
          cov_t4rs1xx0t().s[34]++;
          this.pausedDuration = 0;
          var recordingPromise = (cov_t4rs1xx0t().s[35]++, this.cameraRef.recordAsync(recordingOptions));
          cov_t4rs1xx0t().s[36]++;
          this.startProgressMonitoring();
          var result = (cov_t4rs1xx0t().s[37]++, yield recordingPromise);
          cov_t4rs1xx0t().s[38]++;
          this.currentRecordingUri = result.uri;
          cov_t4rs1xx0t().s[39]++;
          performanceMonitor.end('video_recording_start');
        } catch (error) {
          cov_t4rs1xx0t().s[40]++;
          this.isRecording = false;
          cov_t4rs1xx0t().s[41]++;
          console.error('Failed to start recording:', error);
          cov_t4rs1xx0t().s[42]++;
          throw error;
        }
      });
      function startRecording(_x) {
        return _startRecording.apply(this, arguments);
      }
      return startRecording;
    }())
  }, {
    key: "stopRecording",
    value: (function () {
      var _stopRecording = _asyncToGenerator(function* () {
        cov_t4rs1xx0t().f[4]++;
        cov_t4rs1xx0t().s[43]++;
        if ((cov_t4rs1xx0t().b[5][0]++, !this.cameraRef) || (cov_t4rs1xx0t().b[5][1]++, !this.isRecording)) {
          cov_t4rs1xx0t().b[4][0]++;
          cov_t4rs1xx0t().s[44]++;
          throw new Error('No active recording to stop');
        } else {
          cov_t4rs1xx0t().b[4][1]++;
        }
        cov_t4rs1xx0t().s[45]++;
        try {
          cov_t4rs1xx0t().s[46]++;
          performanceMonitor.start('video_recording_stop');
          cov_t4rs1xx0t().s[47]++;
          this.cameraRef.stopRecording();
          cov_t4rs1xx0t().s[48]++;
          this.isRecording = false;
          cov_t4rs1xx0t().s[49]++;
          this.isPaused = false;
          cov_t4rs1xx0t().s[50]++;
          this.stopProgressMonitoring();
          cov_t4rs1xx0t().s[51]++;
          if (!this.currentRecordingUri) {
            cov_t4rs1xx0t().b[6][0]++;
            cov_t4rs1xx0t().s[52]++;
            throw new Error('Recording URI not available');
          } else {
            cov_t4rs1xx0t().b[6][1]++;
          }
          var fileInfo = (cov_t4rs1xx0t().s[53]++, yield FileSystem.getInfoAsync(this.currentRecordingUri));
          cov_t4rs1xx0t().s[54]++;
          if (!fileInfo.exists) {
            cov_t4rs1xx0t().b[7][0]++;
            cov_t4rs1xx0t().s[55]++;
            throw new Error('Recording file not found');
          } else {
            cov_t4rs1xx0t().b[7][1]++;
          }
          var duration = (cov_t4rs1xx0t().s[56]++, (Date.now() - this.recordingStartTime - this.pausedDuration) / 1000);
          var thumbnail = (cov_t4rs1xx0t().s[57]++, yield this.generateThumbnail(this.currentRecordingUri));
          var result = (cov_t4rs1xx0t().s[58]++, {
            uri: this.currentRecordingUri,
            duration: duration,
            fileSize: (cov_t4rs1xx0t().b[8][0]++, fileInfo.size) || (cov_t4rs1xx0t().b[8][1]++, 0),
            width: 1920,
            height: 1080,
            thumbnail: thumbnail
          });
          cov_t4rs1xx0t().s[59]++;
          performanceMonitor.end('video_recording_stop');
          cov_t4rs1xx0t().s[60]++;
          return result;
        } catch (error) {
          cov_t4rs1xx0t().s[61]++;
          console.error('Failed to stop recording:', error);
          cov_t4rs1xx0t().s[62]++;
          throw error;
        }
      });
      function stopRecording() {
        return _stopRecording.apply(this, arguments);
      }
      return stopRecording;
    }())
  }, {
    key: "pauseRecording",
    value: (function () {
      var _pauseRecording = _asyncToGenerator(function* () {
        cov_t4rs1xx0t().f[5]++;
        cov_t4rs1xx0t().s[63]++;
        if ((cov_t4rs1xx0t().b[10][0]++, !this.isRecording) || (cov_t4rs1xx0t().b[10][1]++, this.isPaused)) {
          cov_t4rs1xx0t().b[9][0]++;
          cov_t4rs1xx0t().s[64]++;
          return;
        } else {
          cov_t4rs1xx0t().b[9][1]++;
        }
        cov_t4rs1xx0t().s[65]++;
        try {
          cov_t4rs1xx0t().s[66]++;
          this.isPaused = true;
          cov_t4rs1xx0t().s[67]++;
          console.log('Recording paused (placeholder implementation)');
        } catch (error) {
          cov_t4rs1xx0t().s[68]++;
          console.error('Failed to pause recording:', error);
          cov_t4rs1xx0t().s[69]++;
          throw error;
        }
      });
      function pauseRecording() {
        return _pauseRecording.apply(this, arguments);
      }
      return pauseRecording;
    }())
  }, {
    key: "resumeRecording",
    value: (function () {
      var _resumeRecording = _asyncToGenerator(function* () {
        cov_t4rs1xx0t().f[6]++;
        cov_t4rs1xx0t().s[70]++;
        if ((cov_t4rs1xx0t().b[12][0]++, !this.isRecording) || (cov_t4rs1xx0t().b[12][1]++, !this.isPaused)) {
          cov_t4rs1xx0t().b[11][0]++;
          cov_t4rs1xx0t().s[71]++;
          return;
        } else {
          cov_t4rs1xx0t().b[11][1]++;
        }
        cov_t4rs1xx0t().s[72]++;
        try {
          cov_t4rs1xx0t().s[73]++;
          this.isPaused = false;
          cov_t4rs1xx0t().s[74]++;
          console.log('Recording resumed (placeholder implementation)');
        } catch (error) {
          cov_t4rs1xx0t().s[75]++;
          console.error('Failed to resume recording:', error);
          cov_t4rs1xx0t().s[76]++;
          throw error;
        }
      });
      function resumeRecording() {
        return _resumeRecording.apply(this, arguments);
      }
      return resumeRecording;
    }())
  }, {
    key: "getRecordingStatus",
    value: function getRecordingStatus() {
      cov_t4rs1xx0t().f[7]++;
      var currentTime = (cov_t4rs1xx0t().s[77]++, Date.now());
      var duration = (cov_t4rs1xx0t().s[78]++, this.isRecording ? (cov_t4rs1xx0t().b[13][0]++, (currentTime - this.recordingStartTime - this.pausedDuration) / 1000) : (cov_t4rs1xx0t().b[13][1]++, 0));
      cov_t4rs1xx0t().s[79]++;
      return {
        duration: duration,
        fileSize: 0,
        isRecording: this.isRecording,
        isPaused: this.isPaused
      };
    }
  }, {
    key: "setProgressCallback",
    value: function setProgressCallback(callback) {
      cov_t4rs1xx0t().f[8]++;
      cov_t4rs1xx0t().s[80]++;
      this.progressCallback = callback;
    }
  }, {
    key: "saveToGallery",
    value: (function () {
      var _saveToGallery = _asyncToGenerator(function* (uri) {
        cov_t4rs1xx0t().f[9]++;
        cov_t4rs1xx0t().s[81]++;
        try {
          var permissions = (cov_t4rs1xx0t().s[82]++, yield MediaLibrary.requestPermissionsAsync());
          cov_t4rs1xx0t().s[83]++;
          if (!permissions.granted) {
            cov_t4rs1xx0t().b[14][0]++;
            cov_t4rs1xx0t().s[84]++;
            throw new Error('Media library permission required to save video');
          } else {
            cov_t4rs1xx0t().b[14][1]++;
          }
          var asset = (cov_t4rs1xx0t().s[85]++, yield MediaLibrary.createAssetAsync(uri));
          cov_t4rs1xx0t().s[86]++;
          return asset.uri;
        } catch (error) {
          cov_t4rs1xx0t().s[87]++;
          console.error('Failed to save video to gallery:', error);
          cov_t4rs1xx0t().s[88]++;
          throw error;
        }
      });
      function saveToGallery(_x2) {
        return _saveToGallery.apply(this, arguments);
      }
      return saveToGallery;
    }())
  }, {
    key: "compressVideo",
    value: (function () {
      var _compressVideo = _asyncToGenerator(function* (uri) {
        var quality = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : (cov_t4rs1xx0t().b[15][0]++, 'medium');
        cov_t4rs1xx0t().f[10]++;
        cov_t4rs1xx0t().s[89]++;
        try {
          cov_t4rs1xx0t().s[90]++;
          performanceMonitor.start('video_compression');
          var fileInfo = (cov_t4rs1xx0t().s[91]++, yield FileSystem.getInfoAsync(uri));
          cov_t4rs1xx0t().s[92]++;
          if (!fileInfo.exists) {
            cov_t4rs1xx0t().b[16][0]++;
            cov_t4rs1xx0t().s[93]++;
            throw new Error('Video file not found for compression');
          } else {
            cov_t4rs1xx0t().b[16][1]++;
          }
          var originalSize = (cov_t4rs1xx0t().s[94]++, (cov_t4rs1xx0t().b[17][0]++, fileInfo.size) || (cov_t4rs1xx0t().b[17][1]++, 0));
          cov_t4rs1xx0t().s[95]++;
          console.log(`Compressing video: ${originalSize} bytes, quality: ${quality}`);
          var compressedUri = (cov_t4rs1xx0t().s[96]++, `${FileSystem.cacheDirectory}compressed_${Date.now()}.mp4`);
          var compressionSettings = (cov_t4rs1xx0t().s[97]++, {
            low: {
              bitrate: 500000,
              resolution: 480
            },
            medium: {
              bitrate: 1000000,
              resolution: 720
            },
            high: {
              bitrate: 2000000,
              resolution: 1080
            }
          });
          var settings = (cov_t4rs1xx0t().s[98]++, compressionSettings[quality]);
          cov_t4rs1xx0t().s[99]++;
          yield FileSystem.copyAsync({
            from: uri,
            to: compressedUri
          });
          var compressedInfo = (cov_t4rs1xx0t().s[100]++, yield FileSystem.getInfoAsync(compressedUri));
          var compressedSize = (cov_t4rs1xx0t().s[101]++, (cov_t4rs1xx0t().b[18][0]++, compressedInfo.size) || (cov_t4rs1xx0t().b[18][1]++, 0));
          cov_t4rs1xx0t().s[102]++;
          console.log(`Video compressed: ${originalSize} -> ${compressedSize} bytes`);
          cov_t4rs1xx0t().s[103]++;
          performanceMonitor.end('video_compression');
          cov_t4rs1xx0t().s[104]++;
          return compressedUri;
        } catch (error) {
          cov_t4rs1xx0t().s[105]++;
          console.error('Failed to compress video:', error);
          cov_t4rs1xx0t().s[106]++;
          throw error;
        }
      });
      function compressVideo(_x3) {
        return _compressVideo.apply(this, arguments);
      }
      return compressVideo;
    }())
  }, {
    key: "generateThumbnail",
    value: (function () {
      var _generateThumbnail = _asyncToGenerator(function* (uri) {
        cov_t4rs1xx0t().f[11]++;
        cov_t4rs1xx0t().s[107]++;
        try {
          var thumbnailUri = (cov_t4rs1xx0t().s[108]++, `${FileSystem.cacheDirectory}thumbnail_${Date.now()}.jpg`);
          cov_t4rs1xx0t().s[109]++;
          console.log('Generating thumbnail for video:', uri);
          cov_t4rs1xx0t().s[110]++;
          return '';
        } catch (error) {
          cov_t4rs1xx0t().s[111]++;
          console.error('Failed to generate thumbnail:', error);
          cov_t4rs1xx0t().s[112]++;
          return '';
        }
      });
      function generateThumbnail(_x4) {
        return _generateThumbnail.apply(this, arguments);
      }
      return generateThumbnail;
    }())
  }, {
    key: "getRecordingOptions",
    value: function getRecordingOptions(config) {
      cov_t4rs1xx0t().f[12]++;
      var qualityMap = (cov_t4rs1xx0t().s[113]++, {
        low: VideoQuality['480p'],
        medium: VideoQuality['720p'],
        high: VideoQuality['1080p'],
        ultra: VideoQuality['2160p']
      });
      cov_t4rs1xx0t().s[114]++;
      return {
        quality: (cov_t4rs1xx0t().b[19][0]++, qualityMap[config.quality]) || (cov_t4rs1xx0t().b[19][1]++, VideoQuality['720p']),
        maxDuration: config.maxDurationMinutes * 60,
        mute: !config.enableAudio
      };
    }
  }, {
    key: "startProgressMonitoring",
    value: function startProgressMonitoring() {
      var _this = this;
      cov_t4rs1xx0t().f[13]++;
      cov_t4rs1xx0t().s[115]++;
      if (this.progressInterval) {
        cov_t4rs1xx0t().b[20][0]++;
        cov_t4rs1xx0t().s[116]++;
        clearInterval(this.progressInterval);
      } else {
        cov_t4rs1xx0t().b[20][1]++;
      }
      cov_t4rs1xx0t().s[117]++;
      this.progressInterval = setInterval(function () {
        cov_t4rs1xx0t().f[14]++;
        cov_t4rs1xx0t().s[118]++;
        if (_this.progressCallback) {
          cov_t4rs1xx0t().b[21][0]++;
          var progress = (cov_t4rs1xx0t().s[119]++, _this.getRecordingStatus());
          cov_t4rs1xx0t().s[120]++;
          _this.progressCallback(progress);
        } else {
          cov_t4rs1xx0t().b[21][1]++;
        }
      }, 1000);
    }
  }, {
    key: "stopProgressMonitoring",
    value: function stopProgressMonitoring() {
      cov_t4rs1xx0t().f[15]++;
      cov_t4rs1xx0t().s[121]++;
      if (this.progressInterval) {
        cov_t4rs1xx0t().b[22][0]++;
        cov_t4rs1xx0t().s[122]++;
        clearInterval(this.progressInterval);
        cov_t4rs1xx0t().s[123]++;
        this.progressInterval = null;
      } else {
        cov_t4rs1xx0t().b[22][1]++;
      }
    }
  }, {
    key: "cleanup",
    value: function cleanup() {
      cov_t4rs1xx0t().f[16]++;
      cov_t4rs1xx0t().s[124]++;
      this.stopProgressMonitoring();
      cov_t4rs1xx0t().s[125]++;
      this.isRecording = false;
      cov_t4rs1xx0t().s[126]++;
      this.isPaused = false;
      cov_t4rs1xx0t().s[127]++;
      this.currentRecordingUri = null;
      cov_t4rs1xx0t().s[128]++;
      this.progressCallback = null;
    }
  }]);
}();
export var videoRecordingService = (cov_t4rs1xx0t().s[129]++, new VideoRecordingService());
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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