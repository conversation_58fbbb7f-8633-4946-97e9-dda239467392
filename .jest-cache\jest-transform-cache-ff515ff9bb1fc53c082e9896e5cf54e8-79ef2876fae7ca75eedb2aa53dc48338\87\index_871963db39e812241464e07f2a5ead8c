9957688ffe7f4544cd058fe107fb0fce
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
exports.__esModule = true;
exports.default = void 0;
var _dangerousStyleValue = _interopRequireDefault(require("./dangerousStyleValue"));
function setValueForStyles(node, styles) {
  var style = node.style;
  for (var styleName in styles) {
    if (!styles.hasOwnProperty(styleName)) {
      continue;
    }
    var isCustomProperty = styleName.indexOf('--') === 0;
    var styleValue = (0, _dangerousStyleValue.default)(styleName, styles[styleName], isCustomProperty);
    if (styleName === 'float') {
      styleName = 'cssFloat';
    }
    if (isCustomProperty) {
      style.setProperty(styleName, styleValue);
    } else {
      style[styleName] = styleValue;
    }
  }
}
var _default = exports.default = setValueForStyles;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJuYW1lcyI6WyJfaW50ZXJvcFJlcXVpcmVEZWZhdWx0IiwicmVxdWlyZSIsImRlZmF1bHQiLCJleHBvcnRzIiwiX19lc01vZHVsZSIsIl9kYW5nZXJvdXNTdHlsZVZhbHVlIiwic2V0VmFsdWVGb3JTdHlsZXMiLCJub2RlIiwic3R5bGVzIiwic3R5bGUiLCJzdHlsZU5hbWUiLCJoYXNPd25Qcm9wZXJ0eSIsImlzQ3VzdG9tUHJvcGVydHkiLCJpbmRleE9mIiwic3R5bGVWYWx1ZSIsInNldFByb3BlcnR5IiwiX2RlZmF1bHQiLCJtb2R1bGUiXSwic291cmNlcyI6WyJpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIF9pbnRlcm9wUmVxdWlyZURlZmF1bHQgPSByZXF1aXJlKFwiQGJhYmVsL3J1bnRpbWUvaGVscGVycy9pbnRlcm9wUmVxdWlyZURlZmF1bHRcIikuZGVmYXVsdDtcbmV4cG9ydHMuX19lc01vZHVsZSA9IHRydWU7XG5leHBvcnRzLmRlZmF1bHQgPSB2b2lkIDA7XG52YXIgX2Rhbmdlcm91c1N0eWxlVmFsdWUgPSBfaW50ZXJvcFJlcXVpcmVEZWZhdWx0KHJlcXVpcmUoXCIuL2Rhbmdlcm91c1N0eWxlVmFsdWVcIikpO1xuLyogZXNsaW50LWRpc2FibGUgKi9cblxuLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqIEZyb20gUmVhY3QgMTYuMy4wXG4gKiBcbiAqL1xuXG4vKipcbiAqIFNldHMgdGhlIHZhbHVlIGZvciBtdWx0aXBsZSBzdHlsZXMgb24gYSBub2RlLiAgSWYgYSB2YWx1ZSBpcyBzcGVjaWZpZWQgYXNcbiAqICcnIChlbXB0eSBzdHJpbmcpLCB0aGUgY29ycmVzcG9uZGluZyBzdHlsZSBwcm9wZXJ0eSB3aWxsIGJlIHVuc2V0LlxuICpcbiAqIEBwYXJhbSB7RE9NRWxlbWVudH0gbm9kZVxuICogQHBhcmFtIHtvYmplY3R9IHN0eWxlc1xuICovXG5mdW5jdGlvbiBzZXRWYWx1ZUZvclN0eWxlcyhub2RlLCBzdHlsZXMpIHtcbiAgdmFyIHN0eWxlID0gbm9kZS5zdHlsZTtcbiAgZm9yICh2YXIgc3R5bGVOYW1lIGluIHN0eWxlcykge1xuICAgIGlmICghc3R5bGVzLmhhc093blByb3BlcnR5KHN0eWxlTmFtZSkpIHtcbiAgICAgIGNvbnRpbnVlO1xuICAgIH1cbiAgICB2YXIgaXNDdXN0b21Qcm9wZXJ0eSA9IHN0eWxlTmFtZS5pbmRleE9mKCctLScpID09PSAwO1xuICAgIHZhciBzdHlsZVZhbHVlID0gKDAsIF9kYW5nZXJvdXNTdHlsZVZhbHVlLmRlZmF1bHQpKHN0eWxlTmFtZSwgc3R5bGVzW3N0eWxlTmFtZV0sIGlzQ3VzdG9tUHJvcGVydHkpO1xuICAgIGlmIChzdHlsZU5hbWUgPT09ICdmbG9hdCcpIHtcbiAgICAgIHN0eWxlTmFtZSA9ICdjc3NGbG9hdCc7XG4gICAgfVxuICAgIGlmIChpc0N1c3RvbVByb3BlcnR5KSB7XG4gICAgICBzdHlsZS5zZXRQcm9wZXJ0eShzdHlsZU5hbWUsIHN0eWxlVmFsdWUpO1xuICAgIH0gZWxzZSB7XG4gICAgICBzdHlsZVtzdHlsZU5hbWVdID0gc3R5bGVWYWx1ZTtcbiAgICB9XG4gIH1cbn1cbnZhciBfZGVmYXVsdCA9IGV4cG9ydHMuZGVmYXVsdCA9IHNldFZhbHVlRm9yU3R5bGVzO1xubW9kdWxlLmV4cG9ydHMgPSBleHBvcnRzLmRlZmF1bHQ7Il0sIm1hcHBpbmdzIjoiQUFBQSxZQUFZOztBQUVaLElBQUlBLHNCQUFzQixHQUFHQyxPQUFPLENBQUMsOENBQThDLENBQUMsQ0FBQ0MsT0FBTztBQUM1RkMsT0FBTyxDQUFDQyxVQUFVLEdBQUcsSUFBSTtBQUN6QkQsT0FBTyxDQUFDRCxPQUFPLEdBQUcsS0FBSyxDQUFDO0FBQ3hCLElBQUlHLG9CQUFvQixHQUFHTCxzQkFBc0IsQ0FBQ0MsT0FBTyx3QkFBd0IsQ0FBQyxDQUFDO0FBb0JuRixTQUFTSyxpQkFBaUJBLENBQUNDLElBQUksRUFBRUMsTUFBTSxFQUFFO0VBQ3ZDLElBQUlDLEtBQUssR0FBR0YsSUFBSSxDQUFDRSxLQUFLO0VBQ3RCLEtBQUssSUFBSUMsU0FBUyxJQUFJRixNQUFNLEVBQUU7SUFDNUIsSUFBSSxDQUFDQSxNQUFNLENBQUNHLGNBQWMsQ0FBQ0QsU0FBUyxDQUFDLEVBQUU7TUFDckM7SUFDRjtJQUNBLElBQUlFLGdCQUFnQixHQUFHRixTQUFTLENBQUNHLE9BQU8sQ0FBQyxJQUFJLENBQUMsS0FBSyxDQUFDO0lBQ3BELElBQUlDLFVBQVUsR0FBRyxDQUFDLENBQUMsRUFBRVQsb0JBQW9CLENBQUNILE9BQU8sRUFBRVEsU0FBUyxFQUFFRixNQUFNLENBQUNFLFNBQVMsQ0FBQyxFQUFFRSxnQkFBZ0IsQ0FBQztJQUNsRyxJQUFJRixTQUFTLEtBQUssT0FBTyxFQUFFO01BQ3pCQSxTQUFTLEdBQUcsVUFBVTtJQUN4QjtJQUNBLElBQUlFLGdCQUFnQixFQUFFO01BQ3BCSCxLQUFLLENBQUNNLFdBQVcsQ0FBQ0wsU0FBUyxFQUFFSSxVQUFVLENBQUM7SUFDMUMsQ0FBQyxNQUFNO01BQ0xMLEtBQUssQ0FBQ0MsU0FBUyxDQUFDLEdBQUdJLFVBQVU7SUFDL0I7RUFDRjtBQUNGO0FBQ0EsSUFBSUUsUUFBUSxHQUFHYixPQUFPLENBQUNELE9BQU8sR0FBR0ksaUJBQWlCO0FBQ2xEVyxNQUFNLENBQUNkLE9BQU8sR0FBR0EsT0FBTyxDQUFDRCxPQUFPIiwiaWdub3JlTGlzdCI6W119