bb50046a7baf42425956a57c6820998f
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.cacheDirectory = exports.bundleDirectory = exports.UploadTask = exports.StorageAccessFramework = exports.FileSystemCancellableNetworkTask = exports.DownloadResumable = void 0;
exports.copyAsync = copyAsync;
exports.createDownloadResumable = createDownloadResumable;
exports.createUploadTask = createUploadTask;
exports.deleteAsync = deleteAsync;
exports.deleteLegacyDocumentDirectoryAndroid = deleteLegacyDocumentDirectoryAndroid;
exports.documentDirectory = void 0;
exports.downloadAsync = downloadAsync;
exports.getContentUriAsync = getContentUriAsync;
exports.getFreeDiskStorageAsync = getFreeDiskStorageAsync;
exports.getInfoAsync = getInfoAsync;
exports.getTotalDiskCapacityAsync = getTotalDiskCapacityAsync;
exports.makeDirectoryAsync = makeDirectoryAsync;
exports.moveAsync = moveAsync;
exports.readAsStringAsync = readAsStringAsync;
exports.readDirectoryAsync = readDirectoryAsync;
exports.uploadAsync = uploadAsync;
exports.writeAsStringAsync = writeAsStringAsync;
var _possibleConstructorReturn2 = _interopRequireDefault(require("@babel/runtime/helpers/possibleConstructorReturn"));
var _getPrototypeOf2 = _interopRequireDefault(require("@babel/runtime/helpers/getPrototypeOf"));
var _inherits2 = _interopRequireDefault(require("@babel/runtime/helpers/inherits"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _expoModulesCore = require("expo-modules-core");
var _reactNative = require("react-native");
var _ExponentFileSystem = _interopRequireDefault(require("./ExponentFileSystem"));
var _FileSystem = require("./FileSystem.types");
function _callSuper(t, o, e) { return o = (0, _getPrototypeOf2.default)(o), (0, _possibleConstructorReturn2.default)(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], (0, _getPrototypeOf2.default)(t).constructor) : o.apply(t, e)); }
function _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }
if (!_ExponentFileSystem.default) {
  console.warn("No native ExponentFileSystem module found, are you sure the expo-file-system's module is linked properly?");
}
function normalizeEndingSlash(p) {
  if (p != null) {
    return p.replace(/\/*$/, '') + '/';
  }
  return null;
}
var documentDirectory = exports.documentDirectory = normalizeEndingSlash(_ExponentFileSystem.default.documentDirectory);
var cacheDirectory = exports.cacheDirectory = normalizeEndingSlash(_ExponentFileSystem.default.cacheDirectory);
var bundleDirectory = exports.bundleDirectory = normalizeEndingSlash(_ExponentFileSystem.default.bundleDirectory);
function getInfoAsync(_x) {
  return _getInfoAsync.apply(this, arguments);
}
function _getInfoAsync() {
  _getInfoAsync = (0, _asyncToGenerator2.default)(function* (fileUri) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    if (!_ExponentFileSystem.default.getInfoAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'getInfoAsync');
    }
    return yield _ExponentFileSystem.default.getInfoAsync(fileUri, options);
  });
  return _getInfoAsync.apply(this, arguments);
}
function readAsStringAsync(_x2) {
  return _readAsStringAsync.apply(this, arguments);
}
function _readAsStringAsync() {
  _readAsStringAsync = (0, _asyncToGenerator2.default)(function* (fileUri) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    if (!_ExponentFileSystem.default.readAsStringAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'readAsStringAsync');
    }
    return yield _ExponentFileSystem.default.readAsStringAsync(fileUri, options);
  });
  return _readAsStringAsync.apply(this, arguments);
}
function getContentUriAsync(_x3) {
  return _getContentUriAsync.apply(this, arguments);
}
function _getContentUriAsync() {
  _getContentUriAsync = (0, _asyncToGenerator2.default)(function* (fileUri) {
    if (_reactNative.Platform.OS === 'android') {
      if (!_ExponentFileSystem.default.getContentUriAsync) {
        throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'getContentUriAsync');
      }
      return yield _ExponentFileSystem.default.getContentUriAsync(fileUri);
    } else {
      return fileUri;
    }
  });
  return _getContentUriAsync.apply(this, arguments);
}
function writeAsStringAsync(_x4, _x5) {
  return _writeAsStringAsync.apply(this, arguments);
}
function _writeAsStringAsync() {
  _writeAsStringAsync = (0, _asyncToGenerator2.default)(function* (fileUri, contents) {
    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    if (!_ExponentFileSystem.default.writeAsStringAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'writeAsStringAsync');
    }
    return yield _ExponentFileSystem.default.writeAsStringAsync(fileUri, contents, options);
  });
  return _writeAsStringAsync.apply(this, arguments);
}
function deleteAsync(_x6) {
  return _deleteAsync.apply(this, arguments);
}
function _deleteAsync() {
  _deleteAsync = (0, _asyncToGenerator2.default)(function* (fileUri) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    if (!_ExponentFileSystem.default.deleteAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'deleteAsync');
    }
    return yield _ExponentFileSystem.default.deleteAsync(fileUri, options);
  });
  return _deleteAsync.apply(this, arguments);
}
function deleteLegacyDocumentDirectoryAndroid() {
  return _deleteLegacyDocumentDirectoryAndroid.apply(this, arguments);
}
function _deleteLegacyDocumentDirectoryAndroid() {
  _deleteLegacyDocumentDirectoryAndroid = (0, _asyncToGenerator2.default)(function* () {
    if (_reactNative.Platform.OS !== 'android' || documentDirectory == null) {
      return;
    }
    var legacyDocumentDirectory = `${documentDirectory}ExperienceData/`;
    return yield deleteAsync(legacyDocumentDirectory, {
      idempotent: true
    });
  });
  return _deleteLegacyDocumentDirectoryAndroid.apply(this, arguments);
}
function moveAsync(_x7) {
  return _moveAsync.apply(this, arguments);
}
function _moveAsync() {
  _moveAsync = (0, _asyncToGenerator2.default)(function* (options) {
    if (!_ExponentFileSystem.default.moveAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'moveAsync');
    }
    return yield _ExponentFileSystem.default.moveAsync(options);
  });
  return _moveAsync.apply(this, arguments);
}
function copyAsync(_x8) {
  return _copyAsync.apply(this, arguments);
}
function _copyAsync() {
  _copyAsync = (0, _asyncToGenerator2.default)(function* (options) {
    if (!_ExponentFileSystem.default.copyAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'copyAsync');
    }
    return yield _ExponentFileSystem.default.copyAsync(options);
  });
  return _copyAsync.apply(this, arguments);
}
function makeDirectoryAsync(_x9) {
  return _makeDirectoryAsync.apply(this, arguments);
}
function _makeDirectoryAsync() {
  _makeDirectoryAsync = (0, _asyncToGenerator2.default)(function* (fileUri) {
    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
    if (!_ExponentFileSystem.default.makeDirectoryAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'makeDirectoryAsync');
    }
    return yield _ExponentFileSystem.default.makeDirectoryAsync(fileUri, options);
  });
  return _makeDirectoryAsync.apply(this, arguments);
}
function readDirectoryAsync(_x0) {
  return _readDirectoryAsync.apply(this, arguments);
}
function _readDirectoryAsync() {
  _readDirectoryAsync = (0, _asyncToGenerator2.default)(function* (fileUri) {
    if (!_ExponentFileSystem.default.readDirectoryAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'readDirectoryAsync');
    }
    return yield _ExponentFileSystem.default.readDirectoryAsync(fileUri);
  });
  return _readDirectoryAsync.apply(this, arguments);
}
function getFreeDiskStorageAsync() {
  return _getFreeDiskStorageAsync.apply(this, arguments);
}
function _getFreeDiskStorageAsync() {
  _getFreeDiskStorageAsync = (0, _asyncToGenerator2.default)(function* () {
    if (!_ExponentFileSystem.default.getFreeDiskStorageAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'getFreeDiskStorageAsync');
    }
    return yield _ExponentFileSystem.default.getFreeDiskStorageAsync();
  });
  return _getFreeDiskStorageAsync.apply(this, arguments);
}
function getTotalDiskCapacityAsync() {
  return _getTotalDiskCapacityAsync.apply(this, arguments);
}
function _getTotalDiskCapacityAsync() {
  _getTotalDiskCapacityAsync = (0, _asyncToGenerator2.default)(function* () {
    if (!_ExponentFileSystem.default.getTotalDiskCapacityAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'getTotalDiskCapacityAsync');
    }
    return yield _ExponentFileSystem.default.getTotalDiskCapacityAsync();
  });
  return _getTotalDiskCapacityAsync.apply(this, arguments);
}
function downloadAsync(_x1, _x10) {
  return _downloadAsync.apply(this, arguments);
}
function _downloadAsync() {
  _downloadAsync = (0, _asyncToGenerator2.default)(function* (uri, fileUri) {
    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    if (!_ExponentFileSystem.default.downloadAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'downloadAsync');
    }
    return yield _ExponentFileSystem.default.downloadAsync(uri, fileUri, Object.assign({
      sessionType: _FileSystem.FileSystemSessionType.BACKGROUND
    }, options));
  });
  return _downloadAsync.apply(this, arguments);
}
function uploadAsync(_x11, _x12) {
  return _uploadAsync.apply(this, arguments);
}
function _uploadAsync() {
  _uploadAsync = (0, _asyncToGenerator2.default)(function* (url, fileUri) {
    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    if (!_ExponentFileSystem.default.uploadAsync) {
      throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'uploadAsync');
    }
    return yield _ExponentFileSystem.default.uploadAsync(url, fileUri, Object.assign({
      sessionType: _FileSystem.FileSystemSessionType.BACKGROUND,
      uploadType: _FileSystem.FileSystemUploadType.BINARY_CONTENT
    }, options, {
      httpMethod: (options.httpMethod || 'POST').toUpperCase()
    }));
  });
  return _uploadAsync.apply(this, arguments);
}
function createDownloadResumable(uri, fileUri, options, callback, resumeData) {
  return new DownloadResumable(uri, fileUri, options, callback, resumeData);
}
function createUploadTask(url, fileUri, options, callback) {
  return new UploadTask(url, fileUri, options, callback);
}
var FileSystemCancellableNetworkTask = exports.FileSystemCancellableNetworkTask = function () {
  function FileSystemCancellableNetworkTask() {
    (0, _classCallCheck2.default)(this, FileSystemCancellableNetworkTask);
    this._uuid = _expoModulesCore.uuid.v4();
    this.taskWasCanceled = false;
  }
  return (0, _createClass2.default)(FileSystemCancellableNetworkTask, [{
    key: "cancelAsync",
    value: function () {
      var _cancelAsync = (0, _asyncToGenerator2.default)(function* () {
        if (!_ExponentFileSystem.default.networkTaskCancelAsync) {
          throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'networkTaskCancelAsync');
        }
        this.removeSubscription();
        this.taskWasCanceled = true;
        return yield _ExponentFileSystem.default.networkTaskCancelAsync(this.uuid);
      });
      function cancelAsync() {
        return _cancelAsync.apply(this, arguments);
      }
      return cancelAsync;
    }()
  }, {
    key: "isTaskCancelled",
    value: function isTaskCancelled() {
      if (this.taskWasCanceled) {
        console.warn('This task was already canceled.');
        return true;
      }
      return false;
    }
  }, {
    key: "uuid",
    get: function get() {
      return this._uuid;
    }
  }, {
    key: "addSubscription",
    value: function addSubscription() {
      var _this = this;
      if (this.subscription) {
        return;
      }
      this.subscription = _ExponentFileSystem.default.addListener(this.getEventName(), function (event) {
        if (event.uuid === _this.uuid) {
          var _callback = _this.getCallback();
          if (_callback) {
            _callback(event.data);
          }
        }
      });
    }
  }, {
    key: "removeSubscription",
    value: function removeSubscription() {
      if (!this.subscription) {
        return;
      }
      this.subscription.remove();
      this.subscription = null;
    }
  }]);
}();
var UploadTask = exports.UploadTask = function (_FileSystemCancellabl) {
  function UploadTask(url, fileUri, options, callback) {
    var _options$httpMethod;
    var _this2;
    (0, _classCallCheck2.default)(this, UploadTask);
    _this2 = _callSuper(this, UploadTask);
    _this2.url = url;
    _this2.fileUri = fileUri;
    _this2.callback = callback;
    var httpMethod = (options == null || (_options$httpMethod = options.httpMethod) == null ? void 0 : _options$httpMethod.toUpperCase()) || 'POST';
    _this2.options = Object.assign({
      sessionType: _FileSystem.FileSystemSessionType.BACKGROUND,
      uploadType: _FileSystem.FileSystemUploadType.BINARY_CONTENT
    }, options, {
      httpMethod: httpMethod
    });
    return _this2;
  }
  (0, _inherits2.default)(UploadTask, _FileSystemCancellabl);
  return (0, _createClass2.default)(UploadTask, [{
    key: "getEventName",
    value: function getEventName() {
      return 'expo-file-system.uploadProgress';
    }
  }, {
    key: "getCallback",
    value: function getCallback() {
      return this.callback;
    }
  }, {
    key: "uploadAsync",
    value: function () {
      var _uploadAsync2 = (0, _asyncToGenerator2.default)(function* () {
        if (!_ExponentFileSystem.default.uploadTaskStartAsync) {
          throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'uploadTaskStartAsync');
        }
        if (this.isTaskCancelled()) {
          return;
        }
        this.addSubscription();
        var result = yield _ExponentFileSystem.default.uploadTaskStartAsync(this.url, this.fileUri, this.uuid, this.options);
        this.removeSubscription();
        return result;
      });
      function uploadAsync() {
        return _uploadAsync2.apply(this, arguments);
      }
      return uploadAsync;
    }()
  }]);
}(FileSystemCancellableNetworkTask);
var DownloadResumable = exports.DownloadResumable = function (_FileSystemCancellabl2) {
  function DownloadResumable(url, _fileUri) {
    var _this3;
    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};
    var callback = arguments.length > 3 ? arguments[3] : undefined;
    var resumeData = arguments.length > 4 ? arguments[4] : undefined;
    (0, _classCallCheck2.default)(this, DownloadResumable);
    _this3 = _callSuper(this, DownloadResumable);
    _this3.url = url;
    _this3._fileUri = _fileUri;
    _this3.options = options;
    _this3.callback = callback;
    _this3.resumeData = resumeData;
    return _this3;
  }
  (0, _inherits2.default)(DownloadResumable, _FileSystemCancellabl2);
  return (0, _createClass2.default)(DownloadResumable, [{
    key: "fileUri",
    get: function get() {
      return this._fileUri;
    }
  }, {
    key: "getEventName",
    value: function getEventName() {
      return 'expo-file-system.downloadProgress';
    }
  }, {
    key: "getCallback",
    value: function getCallback() {
      return this.callback;
    }
  }, {
    key: "downloadAsync",
    value: (function () {
      var _downloadAsync2 = (0, _asyncToGenerator2.default)(function* () {
        if (!_ExponentFileSystem.default.downloadResumableStartAsync) {
          throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');
        }
        if (this.isTaskCancelled()) {
          return;
        }
        this.addSubscription();
        return yield _ExponentFileSystem.default.downloadResumableStartAsync(this.url, this._fileUri, this.uuid, this.options, this.resumeData);
      });
      function downloadAsync() {
        return _downloadAsync2.apply(this, arguments);
      }
      return downloadAsync;
    }())
  }, {
    key: "pauseAsync",
    value: (function () {
      var _pauseAsync = (0, _asyncToGenerator2.default)(function* () {
        if (!_ExponentFileSystem.default.downloadResumablePauseAsync) {
          throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'downloadResumablePauseAsync');
        }
        if (this.isTaskCancelled()) {
          return {
            fileUri: this._fileUri,
            options: this.options,
            url: this.url
          };
        }
        var pauseResult = yield _ExponentFileSystem.default.downloadResumablePauseAsync(this.uuid);
        this.removeSubscription();
        if (pauseResult) {
          this.resumeData = pauseResult.resumeData;
          return this.savable();
        } else {
          throw new Error('Unable to generate a savable pause state');
        }
      });
      function pauseAsync() {
        return _pauseAsync.apply(this, arguments);
      }
      return pauseAsync;
    }())
  }, {
    key: "resumeAsync",
    value: (function () {
      var _resumeAsync = (0, _asyncToGenerator2.default)(function* () {
        if (!_ExponentFileSystem.default.downloadResumableStartAsync) {
          throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'downloadResumableStartAsync');
        }
        if (this.isTaskCancelled()) {
          return;
        }
        this.addSubscription();
        return yield _ExponentFileSystem.default.downloadResumableStartAsync(this.url, this.fileUri, this.uuid, this.options, this.resumeData);
      });
      function resumeAsync() {
        return _resumeAsync.apply(this, arguments);
      }
      return resumeAsync;
    }())
  }, {
    key: "savable",
    value: function savable() {
      return {
        url: this.url,
        fileUri: this.fileUri,
        options: this.options,
        resumeData: this.resumeData
      };
    }
  }]);
}(FileSystemCancellableNetworkTask);
var baseReadAsStringAsync = readAsStringAsync;
var baseWriteAsStringAsync = writeAsStringAsync;
var baseDeleteAsync = deleteAsync;
var baseMoveAsync = moveAsync;
var baseCopyAsync = copyAsync;
var StorageAccessFramework;
(function (_StorageAccessFramework) {
  function getUriForDirectoryInRoot(folderName) {
    return `content://com.android.externalstorage.documents/tree/primary:${folderName}/document/primary:${folderName}`;
  }
  _StorageAccessFramework.getUriForDirectoryInRoot = getUriForDirectoryInRoot;
  function requestDirectoryPermissionsAsync() {
    return _requestDirectoryPermissionsAsync.apply(this, arguments);
  }
  function _requestDirectoryPermissionsAsync() {
    _requestDirectoryPermissionsAsync = (0, _asyncToGenerator2.default)(function* () {
      var initialFileUrl = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : null;
      if (!_ExponentFileSystem.default.requestDirectoryPermissionsAsync) {
        throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'StorageAccessFramework.requestDirectoryPermissionsAsync');
      }
      return yield _ExponentFileSystem.default.requestDirectoryPermissionsAsync(initialFileUrl);
    });
    return _requestDirectoryPermissionsAsync.apply(this, arguments);
  }
  _StorageAccessFramework.requestDirectoryPermissionsAsync = requestDirectoryPermissionsAsync;
  function readDirectoryAsync(_x13) {
    return _readDirectoryAsync2.apply(this, arguments);
  }
  function _readDirectoryAsync2() {
    _readDirectoryAsync2 = (0, _asyncToGenerator2.default)(function* (dirUri) {
      if (!_ExponentFileSystem.default.readSAFDirectoryAsync) {
        throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'StorageAccessFramework.readDirectoryAsync');
      }
      return yield _ExponentFileSystem.default.readSAFDirectoryAsync(dirUri);
    });
    return _readDirectoryAsync2.apply(this, arguments);
  }
  _StorageAccessFramework.readDirectoryAsync = readDirectoryAsync;
  function makeDirectoryAsync(_x14, _x15) {
    return _makeDirectoryAsync2.apply(this, arguments);
  }
  function _makeDirectoryAsync2() {
    _makeDirectoryAsync2 = (0, _asyncToGenerator2.default)(function* (parentUri, dirName) {
      if (!_ExponentFileSystem.default.makeSAFDirectoryAsync) {
        throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'StorageAccessFramework.makeDirectoryAsync');
      }
      return yield _ExponentFileSystem.default.makeSAFDirectoryAsync(parentUri, dirName);
    });
    return _makeDirectoryAsync2.apply(this, arguments);
  }
  _StorageAccessFramework.makeDirectoryAsync = makeDirectoryAsync;
  function createFileAsync(_x16, _x17, _x18) {
    return _createFileAsync.apply(this, arguments);
  }
  function _createFileAsync() {
    _createFileAsync = (0, _asyncToGenerator2.default)(function* (parentUri, fileName, mimeType) {
      if (!_ExponentFileSystem.default.createSAFFileAsync) {
        throw new _expoModulesCore.UnavailabilityError('expo-file-system', 'StorageAccessFramework.createFileAsync');
      }
      return yield _ExponentFileSystem.default.createSAFFileAsync(parentUri, fileName, mimeType);
    });
    return _createFileAsync.apply(this, arguments);
  }
  _StorageAccessFramework.createFileAsync = createFileAsync;
  var writeAsStringAsync = _StorageAccessFramework.writeAsStringAsync = baseWriteAsStringAsync;
  var readAsStringAsync = _StorageAccessFramework.readAsStringAsync = baseReadAsStringAsync;
  var deleteAsync = _StorageAccessFramework.deleteAsync = baseDeleteAsync;
  var moveAsync = _StorageAccessFramework.moveAsync = baseMoveAsync;
  var copyAsync = _StorageAccessFramework.copyAsync = baseCopyAsync;
})(StorageAccessFramework || (exports.StorageAccessFramework = StorageAccessFramework = {}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************