4ceb780275bdb3d61782deb2125621be
"use strict";
'use client';

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _objectSpread2 = _interopRequireDefault(require("@babel/runtime/helpers/objectSpread2"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _createElement = _interopRequireDefault(require("../createElement"));
var forwardedProps = _interopRequireWildcard(require("../../modules/forwardedProps"));
var _pick = _interopRequireDefault(require("../../modules/pick"));
var _useElementLayout = _interopRequireDefault(require("../../modules/useElementLayout"));
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _usePlatformMethods = _interopRequireDefault(require("../../modules/usePlatformMethods"));
var _useResponderEvents = _interopRequireDefault(require("../../modules/useResponderEvents"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _TextAncestorContext = _interopRequireDefault(require("./TextAncestorContext"));
var _useLocale = require("../../modules/useLocale");
var _excluded = ["hrefAttrs", "numberOfLines", "onClick", "onLayout", "onPress", "onMoveShouldSetResponder", "onMoveShouldSetResponderCapture", "onResponderEnd", "onResponderGrant", "onResponderMove", "onResponderReject", "onResponderRelease", "onResponderStart", "onResponderTerminate", "onResponderTerminationRequest", "onScrollShouldSetResponder", "onScrollShouldSetResponderCapture", "onSelectionChangeShouldSetResponder", "onSelectionChangeShouldSetResponderCapture", "onStartShouldSetResponder", "onStartShouldSetResponderCapture", "selectable"];
var forwardPropsList = Object.assign({}, forwardedProps.defaultProps, forwardedProps.accessibilityProps, forwardedProps.clickProps, forwardedProps.focusProps, forwardedProps.keyboardProps, forwardedProps.mouseProps, forwardedProps.touchProps, forwardedProps.styleProps, {
  href: true,
  lang: true,
  pointerEvents: true
});
var pickProps = function pickProps(props) {
  return (0, _pick.default)(props, forwardPropsList);
};
var Text = React.forwardRef(function (props, forwardedRef) {
  var hrefAttrs = props.hrefAttrs,
    numberOfLines = props.numberOfLines,
    onClick = props.onClick,
    onLayout = props.onLayout,
    onPress = props.onPress,
    onMoveShouldSetResponder = props.onMoveShouldSetResponder,
    onMoveShouldSetResponderCapture = props.onMoveShouldSetResponderCapture,
    onResponderEnd = props.onResponderEnd,
    onResponderGrant = props.onResponderGrant,
    onResponderMove = props.onResponderMove,
    onResponderReject = props.onResponderReject,
    onResponderRelease = props.onResponderRelease,
    onResponderStart = props.onResponderStart,
    onResponderTerminate = props.onResponderTerminate,
    onResponderTerminationRequest = props.onResponderTerminationRequest,
    onScrollShouldSetResponder = props.onScrollShouldSetResponder,
    onScrollShouldSetResponderCapture = props.onScrollShouldSetResponderCapture,
    onSelectionChangeShouldSetResponder = props.onSelectionChangeShouldSetResponder,
    onSelectionChangeShouldSetResponderCapture = props.onSelectionChangeShouldSetResponderCapture,
    onStartShouldSetResponder = props.onStartShouldSetResponder,
    onStartShouldSetResponderCapture = props.onStartShouldSetResponderCapture,
    selectable = props.selectable,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var hasTextAncestor = React.useContext(_TextAncestorContext.default);
  var hostRef = React.useRef(null);
  var _useLocaleContext = (0, _useLocale.useLocaleContext)(),
    contextDirection = _useLocaleContext.direction;
  (0, _useElementLayout.default)(hostRef, onLayout);
  (0, _useResponderEvents.default)(hostRef, {
    onMoveShouldSetResponder: onMoveShouldSetResponder,
    onMoveShouldSetResponderCapture: onMoveShouldSetResponderCapture,
    onResponderEnd: onResponderEnd,
    onResponderGrant: onResponderGrant,
    onResponderMove: onResponderMove,
    onResponderReject: onResponderReject,
    onResponderRelease: onResponderRelease,
    onResponderStart: onResponderStart,
    onResponderTerminate: onResponderTerminate,
    onResponderTerminationRequest: onResponderTerminationRequest,
    onScrollShouldSetResponder: onScrollShouldSetResponder,
    onScrollShouldSetResponderCapture: onScrollShouldSetResponderCapture,
    onSelectionChangeShouldSetResponder: onSelectionChangeShouldSetResponder,
    onSelectionChangeShouldSetResponderCapture: onSelectionChangeShouldSetResponderCapture,
    onStartShouldSetResponder: onStartShouldSetResponder,
    onStartShouldSetResponderCapture: onStartShouldSetResponderCapture
  });
  var handleClick = React.useCallback(function (e) {
    if (onClick != null) {
      onClick(e);
    } else if (onPress != null) {
      e.stopPropagation();
      onPress(e);
    }
  }, [onClick, onPress]);
  var component = hasTextAncestor ? 'span' : 'div';
  var langDirection = props.lang != null ? (0, _useLocale.getLocaleDirection)(props.lang) : null;
  var componentDirection = props.dir || langDirection;
  var writingDirection = componentDirection || contextDirection;
  var supportedProps = pickProps(rest);
  supportedProps.dir = componentDirection;
  if (!hasTextAncestor) {
    supportedProps.dir = componentDirection != null ? componentDirection : 'auto';
  }
  if (onClick || onPress) {
    supportedProps.onClick = handleClick;
  }
  supportedProps.style = [numberOfLines != null && numberOfLines > 1 && {
    WebkitLineClamp: numberOfLines
  }, hasTextAncestor === true ? styles.textHasAncestor$raw : styles.text$raw, numberOfLines === 1 && styles.textOneLine, numberOfLines != null && numberOfLines > 1 && styles.textMultiLine, props.style, selectable === true && styles.selectable, selectable === false && styles.notSelectable, onPress && styles.pressable];
  if (props.href != null) {
    component = 'a';
    if (hrefAttrs != null) {
      var download = hrefAttrs.download,
        rel = hrefAttrs.rel,
        target = hrefAttrs.target;
      if (download != null) {
        supportedProps.download = download;
      }
      if (rel != null) {
        supportedProps.rel = rel;
      }
      if (typeof target === 'string') {
        supportedProps.target = target.charAt(0) !== '_' ? '_' + target : target;
      }
    }
  }
  var platformMethodsRef = (0, _usePlatformMethods.default)(supportedProps);
  var setRef = (0, _useMergeRefs.default)(hostRef, platformMethodsRef, forwardedRef);
  supportedProps.ref = setRef;
  var element = (0, _createElement.default)(component, supportedProps, {
    writingDirection: writingDirection
  });
  return hasTextAncestor ? element : React.createElement(_TextAncestorContext.default.Provider, {
    value: true
  }, element);
});
Text.displayName = 'Text';
var textStyle = {
  backgroundColor: 'transparent',
  border: '0 solid black',
  boxSizing: 'border-box',
  color: 'black',
  display: 'inline',
  font: '14px System',
  listStyle: 'none',
  margin: 0,
  padding: 0,
  position: 'relative',
  textAlign: 'start',
  textDecoration: 'none',
  whiteSpace: 'pre-wrap',
  wordWrap: 'break-word'
};
var styles = _StyleSheet.default.create({
  text$raw: textStyle,
  textHasAncestor$raw: (0, _objectSpread2.default)((0, _objectSpread2.default)({}, textStyle), {}, {
    color: 'inherit',
    font: 'inherit',
    textAlign: 'inherit',
    whiteSpace: 'inherit'
  }),
  textOneLine: {
    maxWidth: '100%',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    wordWrap: 'normal'
  },
  textMultiLine: {
    display: '-webkit-box',
    maxWidth: '100%',
    overflow: 'clip',
    textOverflow: 'ellipsis',
    WebkitBoxOrient: 'vertical'
  },
  notSelectable: {
    userSelect: 'none'
  },
  selectable: {
    userSelect: 'text'
  },
  pressable: {
    cursor: 'pointer'
  }
});
var _default = exports.default = Text;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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