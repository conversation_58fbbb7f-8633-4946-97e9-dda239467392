62506414b9557e95e0b2c64dff901a28
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = useResponderEvents;
var React = _interopRequireWildcard(require("react"));
var ResponderSystem = _interopRequireWildcard(require("./ResponderSystem"));
var emptyObject = {};
var idCounter = 0;
function useStable(getInitialValue) {
  var ref = React.useRef(null);
  if (ref.current == null) {
    ref.current = getInitialValue();
  }
  return ref.current;
}
function useResponderEvents(hostRef, config) {
  if (config === void 0) {
    config = emptyObject;
  }
  var id = useStable(function () {
    return idCounter++;
  });
  var isAttachedRef = React.useRef(false);
  React.useEffect(function () {
    ResponderSystem.attachListeners();
    return function () {
      ResponderSystem.removeNode(id);
    };
  }, [id]);
  React.useEffect(function () {
    var _config = config,
      onMoveShouldSetResponder = _config.onMoveShouldSetResponder,
      onMoveShouldSetResponderCapture = _config.onMoveShouldSetResponderCapture,
      onScrollShouldSetResponder = _config.onScrollShouldSetResponder,
      onScrollShouldSetResponderCapture = _config.onScrollShouldSetResponderCapture,
      onSelectionChangeShouldSetResponder = _config.onSelectionChangeShouldSetResponder,
      onSelectionChangeShouldSetResponderCapture = _config.onSelectionChangeShouldSetResponderCapture,
      onStartShouldSetResponder = _config.onStartShouldSetResponder,
      onStartShouldSetResponderCapture = _config.onStartShouldSetResponderCapture;
    var requiresResponderSystem = onMoveShouldSetResponder != null || onMoveShouldSetResponderCapture != null || onScrollShouldSetResponder != null || onScrollShouldSetResponderCapture != null || onSelectionChangeShouldSetResponder != null || onSelectionChangeShouldSetResponderCapture != null || onStartShouldSetResponder != null || onStartShouldSetResponderCapture != null;
    var node = hostRef.current;
    if (requiresResponderSystem) {
      ResponderSystem.addNode(id, node, config);
      isAttachedRef.current = true;
    } else if (isAttachedRef.current) {
      ResponderSystem.removeNode(id);
      isAttachedRef.current = false;
    }
  }, [config, hostRef, id]);
  React.useDebugValue({
    isResponder: hostRef.current === ResponderSystem.getResponderNode()
  });
  React.useDebugValue(config);
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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