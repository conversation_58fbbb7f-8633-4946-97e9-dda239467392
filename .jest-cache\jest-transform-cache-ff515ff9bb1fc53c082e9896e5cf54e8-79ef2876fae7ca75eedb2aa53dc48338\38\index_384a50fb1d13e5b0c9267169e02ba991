9fa25c14e3ef14d6d82b38b256ba0d9a
"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = mergeRefs;
var React = _interopRequireWildcard(require("react"));
function mergeRefs() {
  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
    args[_key] = arguments[_key];
  }
  return function forwardRef(node) {
    args.forEach(function (ref) {
      if (ref == null) {
        return;
      }
      if (typeof ref === 'function') {
        ref(node);
        return;
      }
      if (typeof ref === 'object') {
        ref.current = node;
        return;
      }
      console.error("mergeRefs cannot handle Refs of type boolean, number or string, received ref " + String(ref));
    });
  };
}
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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