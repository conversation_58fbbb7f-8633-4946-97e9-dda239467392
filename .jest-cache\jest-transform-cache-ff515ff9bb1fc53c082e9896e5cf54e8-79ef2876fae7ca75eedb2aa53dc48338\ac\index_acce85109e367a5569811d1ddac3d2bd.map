{"version": 3, "names": ["exports", "__esModule", "getAssetByID", "registerAsset", "assets", "asset", "push", "assetId"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.getAssetByID = getAssetByID;\nexports.registerAsset = registerAsset;\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar assets = [];\nfunction registerAsset(asset) {\n  // `push` returns new array length, so the first asset will\n  // get id 1 (not 0) to make the value truthy\n  return assets.push(asset);\n}\nfunction getAssetByID(assetId) {\n  return assets[assetId - 1];\n}"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,YAAY,GAAGA,YAAY;AACnCF,OAAO,CAACG,aAAa,GAAGA,aAAa;AAUrC,IAAIC,MAAM,GAAG,EAAE;AACf,SAASD,aAAaA,CAACE,KAAK,EAAE;EAG5B,OAAOD,MAAM,CAACE,IAAI,CAACD,KAAK,CAAC;AAC3B;AACA,SAASH,YAAYA,CAACK,OAAO,EAAE;EAC7B,OAAOH,MAAM,CAACG,OAAO,GAAG,CAAC,CAAC;AAC5B", "ignoreList": []}