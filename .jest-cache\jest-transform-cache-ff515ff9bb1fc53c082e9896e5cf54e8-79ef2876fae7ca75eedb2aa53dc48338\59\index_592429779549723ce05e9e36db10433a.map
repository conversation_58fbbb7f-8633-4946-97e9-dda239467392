{"version": 3, "names": ["exports", "__esModule", "default", "render", "hydrate", "_client", "require", "_dom", "element", "root", "createSheet", "hydrateRoot", "reactRoot", "createRoot"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n'use client';\n\nexports.__esModule = true;\nexports.default = render;\nexports.hydrate = hydrate;\nvar _client = require(\"react-dom/client\");\nvar _dom = require(\"../StyleSheet/dom\");\nfunction hydrate(element, root) {\n  (0, _dom.createSheet)(root);\n  return (0, _client.hydrateRoot)(root, element);\n}\nfunction render(element, root) {\n  (0, _dom.createSheet)(root);\n  var reactRoot = (0, _client.createRoot)(root);\n  reactRoot.render(element);\n  return reactRoot;\n}"], "mappings": "AAAA,YAAY;AAUZ,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAGC,MAAM;AACxBH,OAAO,CAACI,OAAO,GAAGA,OAAO;AACzB,IAAIC,OAAO,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AACzC,IAAIC,IAAI,GAAGD,OAAO,oBAAoB,CAAC;AACvC,SAASF,OAAOA,CAACI,OAAO,EAAEC,IAAI,EAAE;EAC9B,CAAC,CAAC,EAAEF,IAAI,CAACG,WAAW,EAAED,IAAI,CAAC;EAC3B,OAAO,CAAC,CAAC,EAAEJ,OAAO,CAACM,WAAW,EAAEF,IAAI,EAAED,OAAO,CAAC;AAChD;AACA,SAASL,MAAMA,CAACK,OAAO,EAAEC,IAAI,EAAE;EAC7B,CAAC,CAAC,EAAEF,IAAI,CAACG,WAAW,EAAED,IAAI,CAAC;EAC3B,IAAIG,SAAS,GAAG,CAAC,CAAC,EAAEP,OAAO,CAACQ,UAAU,EAAEJ,IAAI,CAAC;EAC7CG,SAAS,CAACT,MAAM,CAACK,OAAO,CAAC;EACzB,OAAOI,SAAS;AAClB", "ignoreList": []}