0cce1eaea679ac310efc690c3a35f1a6
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_tmoc3trla() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\customization\\AdvancedCustomizationManager.ts";
  var hash = "09b76e48efe59ae5d267641e1cd48deec6e381c5";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\customization\\AdvancedCustomizationManager.ts",
    statementMap: {
      "0": {
        start: {
          line: 149,
          column: 69
        },
        end: {
          line: 149,
          column: 78
        }
      },
      "1": {
        start: {
          line: 150,
          column: 41
        },
        end: {
          line: 150,
          column: 45
        }
      },
      "2": {
        start: {
          line: 151,
          column: 50
        },
        end: {
          line: 151,
          column: 59
        }
      },
      "3": {
        start: {
          line: 152,
          column: 54
        },
        end: {
          line: 152,
          column: 63
        }
      },
      "4": {
        start: {
          line: 153,
          column: 96
        },
        end: {
          line: 153,
          column: 98
        }
      },
      "5": {
        start: {
          line: 155,
          column: 83
        },
        end: {
          line: 236,
          column: 3
        }
      },
      "6": {
        start: {
          line: 239,
          column: 4
        },
        end: {
          line: 239,
          column: 42
        }
      },
      "7": {
        start: {
          line: 246,
          column: 4
        },
        end: {
          line: 259,
          column: 5
        }
      },
      "8": {
        start: {
          line: 248,
          column: 6
        },
        end: {
          line: 248,
          column: 33
        }
      },
      "9": {
        start: {
          line: 251,
          column: 6
        },
        end: {
          line: 251,
          column: 46
        }
      },
      "10": {
        start: {
          line: 254,
          column: 6
        },
        end: {
          line: 254,
          column: 49
        }
      },
      "11": {
        start: {
          line: 256,
          column: 6
        },
        end: {
          line: 256,
          column: 77
        }
      },
      "12": {
        start: {
          line: 258,
          column: 6
        },
        end: {
          line: 258,
          column: 83
        }
      },
      "13": {
        start: {
          line: 268,
          column: 46
        },
        end: {
          line: 276,
          column: 5
        }
      },
      "14": {
        start: {
          line: 278,
          column: 4
        },
        end: {
          line: 278,
          column: 60
        }
      },
      "15": {
        start: {
          line: 279,
          column: 4
        },
        end: {
          line: 279,
          column: 57
        }
      },
      "16": {
        start: {
          line: 281,
          column: 4
        },
        end: {
          line: 281,
          column: 22
        }
      },
      "17": {
        start: {
          line: 288,
          column: 4
        },
        end: {
          line: 315,
          column: 5
        }
      },
      "18": {
        start: {
          line: 289,
          column: 22
        },
        end: {
          line: 289,
          column: 63
        }
      },
      "19": {
        start: {
          line: 290,
          column: 6
        },
        end: {
          line: 292,
          column: 7
        }
      },
      "20": {
        start: {
          line: 291,
          column: 8
        },
        end: {
          line: 291,
          column: 59
        }
      },
      "21": {
        start: {
          line: 295,
          column: 6
        },
        end: {
          line: 298,
          column: 7
        }
      },
      "22": {
        start: {
          line: 296,
          column: 8
        },
        end: {
          line: 296,
          column: 69
        }
      },
      "23": {
        start: {
          line: 297,
          column: 8
        },
        end: {
          line: 297,
          column: 21
        }
      },
      "24": {
        start: {
          line: 301,
          column: 6
        },
        end: {
          line: 301,
          column: 65
        }
      },
      "25": {
        start: {
          line: 304,
          column: 6
        },
        end: {
          line: 304,
          column: 37
        }
      },
      "26": {
        start: {
          line: 307,
          column: 6
        },
        end: {
          line: 307,
          column: 46
        }
      },
      "27": {
        start: {
          line: 309,
          column: 6
        },
        end: {
          line: 309,
          column: 65
        }
      },
      "28": {
        start: {
          line: 310,
          column: 6
        },
        end: {
          line: 310,
          column: 18
        }
      },
      "29": {
        start: {
          line: 313,
          column: 6
        },
        end: {
          line: 313,
          column: 55
        }
      },
      "30": {
        start: {
          line: 314,
          column: 6
        },
        end: {
          line: 314,
          column: 19
        }
      },
      "31": {
        start: {
          line: 326,
          column: 4
        },
        end: {
          line: 358,
          column: 5
        }
      },
      "32": {
        start: {
          line: 328,
          column: 27
        },
        end: {
          line: 328,
          column: 96
        }
      },
      "33": {
        start: {
          line: 328,
          column: 61
        },
        end: {
          line: 328,
          column: 92
        }
      },
      "34": {
        start: {
          line: 329,
          column: 6
        },
        end: {
          line: 331,
          column: 7
        }
      },
      "35": {
        start: {
          line: 330,
          column: 8
        },
        end: {
          line: 330,
          column: 72
        }
      },
      "36": {
        start: {
          line: 334,
          column: 30
        },
        end: {
          line: 343,
          column: 9
        }
      },
      "37": {
        start: {
          line: 334,
          column: 55
        },
        end: {
          line: 343,
          column: 7
        }
      },
      "38": {
        start: {
          line: 345,
          column: 6
        },
        end: {
          line: 345,
          column: 48
        }
      },
      "39": {
        start: {
          line: 348,
          column: 6
        },
        end: {
          line: 350,
          column: 19
        }
      },
      "40": {
        start: {
          line: 349,
          column: 8
        },
        end: {
          line: 349,
          column: 36
        }
      },
      "41": {
        start: {
          line: 352,
          column: 6
        },
        end: {
          line: 352,
          column: 82
        }
      },
      "42": {
        start: {
          line: 353,
          column: 6
        },
        end: {
          line: 353,
          column: 18
        }
      },
      "43": {
        start: {
          line: 356,
          column: 6
        },
        end: {
          line: 356,
          column: 56
        }
      },
      "44": {
        start: {
          line: 357,
          column: 6
        },
        end: {
          line: 357,
          column: 19
        }
      },
      "45": {
        start: {
          line: 372,
          column: 28
        },
        end: {
          line: 372,
          column: 30
        }
      },
      "46": {
        start: {
          line: 375,
          column: 27
        },
        end: {
          line: 375,
          column: 68
        }
      },
      "47": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 391,
          column: 5
        }
      },
      "48": {
        start: {
          line: 379,
          column: 6
        },
        end: {
          line: 390,
          column: 9
        }
      },
      "49": {
        start: {
          line: 393,
          column: 4
        },
        end: {
          line: 406,
          column: 5
        }
      },
      "50": {
        start: {
          line: 394,
          column: 6
        },
        end: {
          line: 405,
          column: 9
        }
      },
      "51": {
        start: {
          line: 408,
          column: 4
        },
        end: {
          line: 408,
          column: 27
        }
      },
      "52": {
        start: {
          line: 421,
          column: 55
        },
        end: {
          line: 421,
          column: 57
        }
      },
      "53": {
        start: {
          line: 424,
          column: 4
        },
        end: {
          line: 426,
          column: 7
        }
      },
      "54": {
        start: {
          line: 425,
          column: 6
        },
        end: {
          line: 425,
          column: 69
        }
      },
      "55": {
        start: {
          line: 429,
          column: 47
        },
        end: {
          line: 429,
          column: 49
        }
      },
      "56": {
        start: {
          line: 430,
          column: 4
        },
        end: {
          line: 436,
          column: 7
        }
      },
      "57": {
        start: {
          line: 431,
          column: 6
        },
        end: {
          line: 435,
          column: 8
        }
      },
      "58": {
        start: {
          line: 438,
          column: 4
        },
        end: {
          line: 444,
          column: 6
        }
      },
      "59": {
        start: {
          line: 450,
          column: 4
        },
        end: {
          line: 461,
          column: 7
        }
      },
      "60": {
        start: {
          line: 451,
          column: 48
        },
        end: {
          line: 459,
          column: 7
        }
      },
      "61": {
        start: {
          line: 460,
          column: 6
        },
        end: {
          line: 460,
          column: 62
        }
      },
      "62": {
        start: {
          line: 466,
          column: 4
        },
        end: {
          line: 466,
          column: 57
        }
      },
      "63": {
        start: {
          line: 467,
          column: 4
        },
        end: {
          line: 467,
          column: 52
        }
      },
      "64": {
        start: {
          line: 468,
          column: 4
        },
        end: {
          line: 468,
          column: 53
        }
      },
      "65": {
        start: {
          line: 469,
          column: 4
        },
        end: {
          line: 469,
          column: 53
        }
      },
      "66": {
        start: {
          line: 475,
          column: 4
        },
        end: {
          line: 475,
          column: 16
        }
      },
      "67": {
        start: {
          line: 481,
          column: 4
        },
        end: {
          line: 481,
          column: 58
        }
      },
      "68": {
        start: {
          line: 485,
          column: 4
        },
        end: {
          line: 489,
          column: 7
        }
      },
      "69": {
        start: {
          line: 492,
          column: 4
        },
        end: {
          line: 494,
          column: 5
        }
      },
      "70": {
        start: {
          line: 493,
          column: 6
        },
        end: {
          line: 493,
          column: 40
        }
      },
      "71": {
        start: {
          line: 498,
          column: 21
        },
        end: {
          line: 498,
          column: 45
        }
      },
      "72": {
        start: {
          line: 499,
          column: 4
        },
        end: {
          line: 499,
          column: 26
        }
      },
      "73": {
        start: {
          line: 499,
          column: 19
        },
        end: {
          line: 499,
          column: 26
        }
      },
      "74": {
        start: {
          line: 502,
          column: 30
        },
        end: {
          line: 505,
          column: 7
        }
      },
      "75": {
        start: {
          line: 502,
          column: 55
        },
        end: {
          line: 505,
          column: 5
        }
      },
      "76": {
        start: {
          line: 507,
          column: 4
        },
        end: {
          line: 507,
          column: 48
        }
      },
      "77": {
        start: {
          line: 510,
          column: 19
        },
        end: {
          line: 510,
          column: 64
        }
      },
      "78": {
        start: {
          line: 511,
          column: 4
        },
        end: {
          line: 513,
          column: 5
        }
      },
      "79": {
        start: {
          line: 512,
          column: 6
        },
        end: {
          line: 512,
          column: 72
        }
      },
      "80": {
        start: {
          line: 517,
          column: 4
        },
        end: {
          line: 517,
          column: 43
        }
      },
      "81": {
        start: {
          line: 517,
          column: 31
        },
        end: {
          line: 517,
          column: 43
        }
      },
      "82": {
        start: {
          line: 520,
          column: 4
        },
        end: {
          line: 522,
          column: 6
        }
      },
      "83": {
        start: {
          line: 521,
          column: 6
        },
        end: {
          line: 521,
          column: 77
        }
      },
      "84": {
        start: {
          line: 527,
          column: 4
        },
        end: {
          line: 532,
          column: 6
        }
      },
      "85": {
        start: {
          line: 537,
          column: 44
        },
        end: {
          line: 537,
          column: 78
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 238,
            column: 2
          },
          end: {
            line: 238,
            column: 3
          }
        },
        loc: {
          start: {
            line: 238,
            column: 16
          },
          end: {
            line: 240,
            column: 3
          }
        },
        line: 238
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 245,
            column: 2
          },
          end: {
            line: 245,
            column: 3
          }
        },
        loc: {
          start: {
            line: 245,
            column: 64
          },
          end: {
            line: 260,
            column: 3
          }
        },
        line: 245
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 265,
            column: 2
          },
          end: {
            line: 265,
            column: 3
          }
        },
        loc: {
          start: {
            line: 267,
            column: 12
          },
          end: {
            line: 282,
            column: 3
          }
        },
        line: 267
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 287,
            column: 2
          },
          end: {
            line: 287,
            column: 3
          }
        },
        loc: {
          start: {
            line: 287,
            column: 58
          },
          end: {
            line: 316,
            column: 3
          }
        },
        line: 287
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 321,
            column: 2
          },
          end: {
            line: 321,
            column: 3
          }
        },
        loc: {
          start: {
            line: 325,
            column: 22
          },
          end: {
            line: 359,
            column: 3
          }
        },
        line: 325
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 328,
            column: 43
          },
          end: {
            line: 328,
            column: 44
          }
        },
        loc: {
          start: {
            line: 328,
            column: 61
          },
          end: {
            line: 328,
            column: 92
          }
        },
        line: 328
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 334,
            column: 43
          },
          end: {
            line: 334,
            column: 44
          }
        },
        loc: {
          start: {
            line: 334,
            column: 55
          },
          end: {
            line: 343,
            column: 7
          }
        },
        line: 334
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 348,
            column: 17
          },
          end: {
            line: 348,
            column: 18
          }
        },
        loc: {
          start: {
            line: 348,
            column: 23
          },
          end: {
            line: 350,
            column: 7
          }
        },
        line: 348
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 364,
            column: 2
          },
          end: {
            line: 364,
            column: 3
          }
        },
        loc: {
          start: {
            line: 371,
            column: 6
          },
          end: {
            line: 409,
            column: 3
          }
        },
        line: 371
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 414,
            column: 2
          },
          end: {
            line: 414,
            column: 3
          }
        },
        loc: {
          start: {
            line: 420,
            column: 4
          },
          end: {
            line: 445,
            column: 3
          }
        },
        line: 420
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 424,
            column: 39
          },
          end: {
            line: 424,
            column: 40
          }
        },
        loc: {
          start: {
            line: 424,
            column: 56
          },
          end: {
            line: 426,
            column: 5
          }
        },
        line: 424
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 430,
            column: 25
          },
          end: {
            line: 430,
            column: 26
          }
        },
        loc: {
          start: {
            line: 430,
            column: 47
          },
          end: {
            line: 436,
            column: 5
          }
        },
        line: 430
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 449,
            column: 2
          },
          end: {
            line: 449,
            column: 3
          }
        },
        loc: {
          start: {
            line: 449,
            column: 38
          },
          end: {
            line: 462,
            column: 3
          }
        },
        line: 449
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 450,
            column: 34
          },
          end: {
            line: 450,
            column: 35
          }
        },
        loc: {
          start: {
            line: 450,
            column: 45
          },
          end: {
            line: 461,
            column: 5
          }
        },
        line: 450
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 464,
            column: 2
          },
          end: {
            line: 464,
            column: 3
          }
        },
        loc: {
          start: {
            line: 464,
            column: 63
          },
          end: {
            line: 470,
            column: 3
          }
        },
        line: 464
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 472,
            column: 2
          },
          end: {
            line: 472,
            column: 3
          }
        },
        loc: {
          start: {
            line: 472,
            column: 73
          },
          end: {
            line: 476,
            column: 3
          }
        },
        line: 472
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 478,
            column: 2
          },
          end: {
            line: 478,
            column: 3
          }
        },
        loc: {
          start: {
            line: 478,
            column: 103
          },
          end: {
            line: 482,
            column: 3
          }
        },
        line: 478
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 484,
            column: 2
          },
          end: {
            line: 484,
            column: 3
          }
        },
        loc: {
          start: {
            line: 484,
            column: 59
          },
          end: {
            line: 495,
            column: 3
          }
        },
        line: 484
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 497,
            column: 2
          },
          end: {
            line: 497,
            column: 3
          }
        },
        loc: {
          start: {
            line: 497,
            column: 62
          },
          end: {
            line: 514,
            column: 3
          }
        },
        line: 497
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 502,
            column: 43
          },
          end: {
            line: 502,
            column: 44
          }
        },
        loc: {
          start: {
            line: 502,
            column: 55
          },
          end: {
            line: 505,
            column: 5
          }
        },
        line: 502
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 516,
            column: 2
          },
          end: {
            line: 516,
            column: 3
          }
        },
        loc: {
          start: {
            line: 516,
            column: 81
          },
          end: {
            line: 523,
            column: 3
          }
        },
        line: 516
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 520,
            column: 27
          },
          end: {
            line: 520,
            column: 28
          }
        },
        loc: {
          start: {
            line: 521,
            column: 6
          },
          end: {
            line: 521,
            column: 77
          }
        },
        line: 521
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 525,
            column: 2
          },
          end: {
            line: 525,
            column: 3
          }
        },
        loc: {
          start: {
            line: 525,
            column: 61
          },
          end: {
            line: 533,
            column: 3
          }
        },
        line: 525
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 290,
            column: 6
          },
          end: {
            line: 292,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 290,
            column: 6
          },
          end: {
            line: 292,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 290
      },
      "1": {
        loc: {
          start: {
            line: 295,
            column: 6
          },
          end: {
            line: 298,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 295,
            column: 6
          },
          end: {
            line: 298,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 295
      },
      "2": {
        loc: {
          start: {
            line: 324,
            column: 4
          },
          end: {
            line: 324,
            column: 46
          }
        },
        type: "default-arg",
        locations: [{
          start: {
            line: 324,
            column: 23
          },
          end: {
            line: 324,
            column: 46
          }
        }],
        line: 324
      },
      "3": {
        loc: {
          start: {
            line: 329,
            column: 6
          },
          end: {
            line: 331,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 329,
            column: 6
          },
          end: {
            line: 331,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 329
      },
      "4": {
        loc: {
          start: {
            line: 378,
            column: 4
          },
          end: {
            line: 391,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 378,
            column: 4
          },
          end: {
            line: 391,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 378
      },
      "5": {
        loc: {
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 393,
            column: 4
          },
          end: {
            line: 406,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 393
      },
      "6": {
        loc: {
          start: {
            line: 432,
            column: 16
          },
          end: {
            line: 432,
            column: 48
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 432,
            column: 16
          },
          end: {
            line: 432,
            column: 35
          }
        }, {
          start: {
            line: 432,
            column: 39
          },
          end: {
            line: 432,
            column: 48
          }
        }],
        line: 432
      },
      "7": {
        loc: {
          start: {
            line: 492,
            column: 4
          },
          end: {
            line: 494,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 492,
            column: 4
          },
          end: {
            line: 494,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 492
      },
      "8": {
        loc: {
          start: {
            line: 499,
            column: 4
          },
          end: {
            line: 499,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 499,
            column: 4
          },
          end: {
            line: 499,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 499
      },
      "9": {
        loc: {
          start: {
            line: 511,
            column: 4
          },
          end: {
            line: 513,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 511,
            column: 4
          },
          end: {
            line: 513,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 511
      },
      "10": {
        loc: {
          start: {
            line: 517,
            column: 4
          },
          end: {
            line: 517,
            column: 43
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 517,
            column: 4
          },
          end: {
            line: 517,
            column: 43
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 517
      },
      "11": {
        loc: {
          start: {
            line: 521,
            column: 6
          },
          end: {
            line: 521,
            column: 77
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 521,
            column: 63
          },
          end: {
            line: 521,
            column: 70
          }
        }, {
          start: {
            line: 521,
            column: 73
          },
          end: {
            line: 521,
            column: 77
          }
        }],
        line: 521
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "09b76e48efe59ae5d267641e1cd48deec6e381c5"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_tmoc3trla = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_tmoc3trla();
var AdvancedCustomizationManager = function () {
  function AdvancedCustomizationManager() {
    _classCallCheck(this, AdvancedCustomizationManager);
    this.customizationProfiles = (cov_tmoc3trla().s[0]++, new Map());
    this.activeProfile = (cov_tmoc3trla().s[1]++, null);
    this.abTests = (cov_tmoc3trla().s[2]++, new Map());
    this.performanceBaselines = (cov_tmoc3trla().s[3]++, new Map());
    this.customizationHistory = (cov_tmoc3trla().s[4]++, []);
    this.DEFAULT_PROFILES = (cov_tmoc3trla().s[5]++, [{
      id: 'high_performance',
      name: 'High Performance',
      description: 'Maximum performance optimization for flagship devices',
      category: 'performance',
      priority: 'high',
      configuration: {
        phase1: {
          bundleSplitting: {
            strategy: 'hybrid',
            chunkSize: 50000,
            preloadStrategy: 'aggressive'
          },
          hookOptimization: {
            memoizationLevel: 'aggressive',
            dependencyTracking: true,
            renderOptimization: true
          },
          databaseOptimization: {
            queryBatching: true,
            indexStrategy: 'comprehensive',
            cacheStrategy: 'hybrid'
          }
        },
        phase2: {
          caching: {
            strategy: 'predictive',
            maxSize: 100 * 1024 * 1024,
            ttl: 3600000,
            compressionEnabled: true
          },
          imageOptimization: {
            format: 'avif',
            quality: 85,
            progressiveLoading: true,
            lazyLoadingThreshold: 200
          },
          offlineStrategy: {
            cacheFirst: true,
            networkFirst: false,
            staleWhileRevalidate: true,
            backgroundSync: true
          }
        },
        phase3a: {
          aiOptimization: {
            predictionModel: 'neural',
            adaptationSpeed: 0.8,
            learningRate: 0.01,
            confidenceThreshold: 0.9
          },
          behaviorAnalysis: {
            trackingLevel: 'comprehensive',
            privacyMode: false,
            realTimeAnalysis: true
          },
          resourceManagement: {
            predictiveLoading: true,
            adaptiveQuality: true,
            intelligentPrefetch: true
          }
        },
        phase3b: {
          edgeOptimization: {
            cdnStrategy: 'adaptive',
            edgeFunctions: true,
            geoOptimization: true,
            loadBalancing: 'ai_optimized'
          },
          globalDelivery: {
            regions: ['us-east-1', 'eu-west-1', 'ap-southeast-1'],
            failoverStrategy: 'intelligent',
            compressionLevel: 'high'
          }
        },
        phase3c: {
          nativeOptimization: {
            gpuAcceleration: true,
            nativeModules: ['all'],
            memoryPoolSize: 256 * 1024 * 1024,
            backgroundProcessing: true
          },
          hardwareUtilization: {
            cpuCores: 8,
            memoryStrategy: 'aggressive',
            batteryOptimization: false
          }
        }
      },
      conditions: {
        deviceType: ['flagship', 'high_end'],
        networkType: ['wifi', '5g'],
        batteryLevel: 50,
        memoryAvailable: 4096,
        userBehavior: ['power_user', 'frequent']
      },
      abTesting: {
        enabled: false,
        variants: [],
        trafficSplit: [],
        metrics: []
      }
    }, {
      id: 'battery_optimized',
      name: 'Battery Optimized',
      description: 'Optimized for maximum battery life and efficiency',
      category: 'performance',
      priority: 'medium',
      configuration: {
        phase1: {
          bundleSplitting: {
            strategy: 'route',
            chunkSize: 30000,
            preloadStrategy: 'conservative'
          },
          hookOptimization: {
            memoizationLevel: 'basic',
            dependencyTracking: false,
            renderOptimization: true
          },
          databaseOptimization: {
            queryBatching: true,
            indexStrategy: 'minimal',
            cacheStrategy: 'memory'
          }
        },
        phase2: {
          caching: {
            strategy: 'lru',
            maxSize: 32 * 1024 * 1024,
            ttl: 1800000,
            compressionEnabled: true
          },
          imageOptimization: {
            format: 'webp',
            quality: 70,
            progressiveLoading: false,
            lazyLoadingThreshold: 500
          },
          offlineStrategy: {
            cacheFirst: true,
            networkFirst: false,
            staleWhileRevalidate: false,
            backgroundSync: false
          }
        },
        phase3a: {
          aiOptimization: {
            predictionModel: 'basic',
            adaptationSpeed: 0.3,
            learningRate: 0.001,
            confidenceThreshold: 0.7
          },
          behaviorAnalysis: {
            trackingLevel: 'basic',
            privacyMode: true,
            realTimeAnalysis: false
          },
          resourceManagement: {
            predictiveLoading: false,
            adaptiveQuality: true,
            intelligentPrefetch: false
          }
        },
        phase3b: {
          edgeOptimization: {
            cdnStrategy: 'single',
            edgeFunctions: false,
            geoOptimization: true,
            loadBalancing: 'round_robin'
          },
          globalDelivery: {
            regions: ['auto'],
            failoverStrategy: 'gradual',
            compressionLevel: 'high'
          }
        },
        phase3c: {
          nativeOptimization: {
            gpuAcceleration: false,
            nativeModules: ['essential'],
            memoryPoolSize: 64 * 1024 * 1024,
            backgroundProcessing: false
          },
          hardwareUtilization: {
            cpuCores: 2,
            memoryStrategy: 'conservative',
            batteryOptimization: true
          }
        }
      },
      conditions: {
        deviceType: ['mid_range', 'budget'],
        networkType: ['4g', '3g'],
        batteryLevel: 20,
        memoryAvailable: 2048,
        userBehavior: ['casual', 'occasional']
      },
      abTesting: {
        enabled: false,
        variants: [],
        trafficSplit: [],
        metrics: []
      }
    }]);
    cov_tmoc3trla().f[0]++;
    cov_tmoc3trla().s[6]++;
    this.initializeCustomizationManager();
  }
  return _createClass(AdvancedCustomizationManager, [{
    key: "initializeCustomizationManager",
    value: (function () {
      var _initializeCustomizationManager = _asyncToGenerator(function* () {
        cov_tmoc3trla().f[1]++;
        cov_tmoc3trla().s[7]++;
        try {
          cov_tmoc3trla().s[8]++;
          this.loadDefaultProfiles();
          cov_tmoc3trla().s[9]++;
          this.activeProfile = 'high_performance';
          cov_tmoc3trla().s[10]++;
          yield this.establishPerformanceBaselines();
          cov_tmoc3trla().s[11]++;
          console.log('Advanced Customization Manager initialized successfully');
        } catch (error) {
          cov_tmoc3trla().s[12]++;
          console.error('Failed to initialize Advanced Customization Manager:', error);
        }
      });
      function initializeCustomizationManager() {
        return _initializeCustomizationManager.apply(this, arguments);
      }
      return initializeCustomizationManager;
    }())
  }, {
    key: "createCustomProfile",
    value: function createCustomProfile(profile) {
      cov_tmoc3trla().f[2]++;
      var fullProfile = (cov_tmoc3trla().s[13]++, Object.assign({}, profile, {
        performance: {
          expectedImprovement: 0,
          actualImprovement: 0,
          confidence: 0,
          sampleSize: 0
        }
      }));
      cov_tmoc3trla().s[14]++;
      this.customizationProfiles.set(profile.id, fullProfile);
      cov_tmoc3trla().s[15]++;
      console.log(`Created custom profile: ${profile.id}`);
      cov_tmoc3trla().s[16]++;
      return profile.id;
    }
  }, {
    key: "applyProfile",
    value: (function () {
      var _applyProfile = _asyncToGenerator(function* (profileId) {
        cov_tmoc3trla().f[3]++;
        cov_tmoc3trla().s[17]++;
        try {
          var profile = (cov_tmoc3trla().s[18]++, this.customizationProfiles.get(profileId));
          cov_tmoc3trla().s[19]++;
          if (!profile) {
            cov_tmoc3trla().b[0][0]++;
            cov_tmoc3trla().s[20]++;
            throw new Error(`Profile not found: ${profileId}`);
          } else {
            cov_tmoc3trla().b[0][1]++;
          }
          cov_tmoc3trla().s[21]++;
          if (!this.checkProfileConditions(profile)) {
            cov_tmoc3trla().b[1][0]++;
            cov_tmoc3trla().s[22]++;
            console.warn(`Profile conditions not met for: ${profileId}`);
            cov_tmoc3trla().s[23]++;
            return false;
          } else {
            cov_tmoc3trla().b[1][1]++;
          }
          cov_tmoc3trla().s[24]++;
          yield this.applyPhaseConfigurations(profile.configuration);
          cov_tmoc3trla().s[25]++;
          this.activeProfile = profileId;
          cov_tmoc3trla().s[26]++;
          this.trackProfileApplication(profileId);
          cov_tmoc3trla().s[27]++;
          console.log(`Applied customization profile: ${profileId}`);
          cov_tmoc3trla().s[28]++;
          return true;
        } catch (error) {
          cov_tmoc3trla().s[29]++;
          console.error('Failed to apply profile:', error);
          cov_tmoc3trla().s[30]++;
          return false;
        }
      });
      function applyProfile(_x) {
        return _applyProfile.apply(this, arguments);
      }
      return applyProfile;
    }())
  }, {
    key: "startABTest",
    value: (function () {
      var _startABTest = _asyncToGenerator(function* (testId, variants) {
        var _this = this;
        var duration = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : (cov_tmoc3trla().b[2][0]++, 7 * 24 * 60 * 60 * 1000);
        cov_tmoc3trla().f[4]++;
        cov_tmoc3trla().s[31]++;
        try {
          var totalTraffic = (cov_tmoc3trla().s[32]++, variants.reduce(function (sum, variant) {
            cov_tmoc3trla().f[5]++;
            cov_tmoc3trla().s[33]++;
            return sum + variant.trafficPercentage;
          }, 0));
          cov_tmoc3trla().s[34]++;
          if (Math.abs(totalTraffic - 100) > 0.01) {
            cov_tmoc3trla().b[3][0]++;
            cov_tmoc3trla().s[35]++;
            throw new Error('Variant traffic percentages must sum to 100%');
          } else {
            cov_tmoc3trla().b[3][1]++;
          }
          var runningVariants = (cov_tmoc3trla().s[36]++, variants.map(function (variant) {
            cov_tmoc3trla().f[6]++;
            cov_tmoc3trla().s[37]++;
            return Object.assign({}, variant, {
              status: 'running',
              metrics: {
                conversions: 0,
                performance: 0,
                userSatisfaction: 0,
                technicalMetrics: {}
              }
            });
          }));
          cov_tmoc3trla().s[38]++;
          this.abTests.set(testId, runningVariants);
          cov_tmoc3trla().s[39]++;
          setTimeout(function () {
            cov_tmoc3trla().f[7]++;
            cov_tmoc3trla().s[40]++;
            _this.completeABTest(testId);
          }, duration);
          cov_tmoc3trla().s[41]++;
          console.log(`Started A/B test: ${testId} with ${variants.length} variants`);
          cov_tmoc3trla().s[42]++;
          return true;
        } catch (error) {
          cov_tmoc3trla().s[43]++;
          console.error('Failed to start A/B test:', error);
          cov_tmoc3trla().s[44]++;
          return false;
        }
      });
      function startABTest(_x2, _x3) {
        return _startABTest.apply(this, arguments);
      }
      return startABTest;
    }())
  }, {
    key: "getOptimizationRecommendations",
    value: (function () {
      var _getOptimizationRecommendations = _asyncToGenerator(function* () {
        cov_tmoc3trla().f[8]++;
        var recommendations = (cov_tmoc3trla().s[45]++, []);
        var currentMetrics = (cov_tmoc3trla().s[46]++, yield this.getCurrentPerformanceMetrics());
        cov_tmoc3trla().s[47]++;
        if (currentMetrics.bundleSize > 500000) {
          cov_tmoc3trla().b[4][0]++;
          cov_tmoc3trla().s[48]++;
          recommendations.push({
            category: 'Bundle Optimization',
            recommendation: 'Implement more aggressive bundle splitting',
            impact: 'high',
            effort: 'medium',
            expectedImprovement: 15,
            configuration: {
              phase1: {
                bundleSplitting: {
                  strategy: 'hybrid',
                  chunkSize: 30000
                }
              }
            }
          });
        } else {
          cov_tmoc3trla().b[4][1]++;
        }
        cov_tmoc3trla().s[49]++;
        if (currentMetrics.memoryUsage > 80) {
          cov_tmoc3trla().b[5][0]++;
          cov_tmoc3trla().s[50]++;
          recommendations.push({
            category: 'Memory Optimization',
            recommendation: 'Enable advanced memory management',
            impact: 'high',
            effort: 'low',
            expectedImprovement: 20,
            configuration: {
              phase3c: {
                nativeOptimization: {
                  memoryPoolSize: 128 * 1024 * 1024
                }
              }
            }
          });
        } else {
          cov_tmoc3trla().b[5][1]++;
        }
        cov_tmoc3trla().s[51]++;
        return recommendations;
      });
      function getOptimizationRecommendations() {
        return _getOptimizationRecommendations.apply(this, arguments);
      }
      return getOptimizationRecommendations;
    }())
  }, {
    key: "getCustomizationAnalytics",
    value: function getCustomizationAnalytics() {
      var _this2 = this;
      cov_tmoc3trla().f[9]++;
      var profilePerformance = (cov_tmoc3trla().s[52]++, {});
      cov_tmoc3trla().s[53]++;
      this.customizationProfiles.forEach(function (profile, id) {
        cov_tmoc3trla().f[10]++;
        cov_tmoc3trla().s[54]++;
        profilePerformance[id] = profile.performance.actualImprovement;
      });
      var abTestResults = (cov_tmoc3trla().s[55]++, {});
      cov_tmoc3trla().s[56]++;
      this.abTests.forEach(function (variants, testId) {
        var _variants$;
        cov_tmoc3trla().f[11]++;
        cov_tmoc3trla().s[57]++;
        abTestResults[testId] = {
          status: (cov_tmoc3trla().b[6][0]++, (_variants$ = variants[0]) == null ? void 0 : _variants$.status) || (cov_tmoc3trla().b[6][1]++, 'unknown'),
          variants: variants.length,
          winner: _this2.determineABTestWinner(variants)
        };
      });
      cov_tmoc3trla().s[58]++;
      return {
        activeProfile: this.activeProfile,
        profilePerformance: profilePerformance,
        abTestResults: abTestResults,
        optimizationHistory: this.customizationHistory.slice(-10),
        recommendations: 5
      };
    }
  }, {
    key: "loadDefaultProfiles",
    value: function loadDefaultProfiles() {
      var _this3 = this;
      cov_tmoc3trla().f[12]++;
      cov_tmoc3trla().s[59]++;
      this.DEFAULT_PROFILES.forEach(function (profile) {
        cov_tmoc3trla().f[13]++;
        var fullProfile = (cov_tmoc3trla().s[60]++, Object.assign({}, profile, {
          performance: {
            expectedImprovement: 25,
            actualImprovement: 0,
            confidence: 0,
            sampleSize: 0
          }
        }));
        cov_tmoc3trla().s[61]++;
        _this3.customizationProfiles.set(profile.id, fullProfile);
      });
    }
  }, {
    key: "establishPerformanceBaselines",
    value: function () {
      var _establishPerformanceBaselines = _asyncToGenerator(function* () {
        cov_tmoc3trla().f[14]++;
        cov_tmoc3trla().s[62]++;
        this.performanceBaselines.set('bundleSize', 1024000);
        cov_tmoc3trla().s[63]++;
        this.performanceBaselines.set('loadTime', 3000);
        cov_tmoc3trla().s[64]++;
        this.performanceBaselines.set('renderTime', 100);
        cov_tmoc3trla().s[65]++;
        this.performanceBaselines.set('memoryUsage', 60);
      });
      function establishPerformanceBaselines() {
        return _establishPerformanceBaselines.apply(this, arguments);
      }
      return establishPerformanceBaselines;
    }()
  }, {
    key: "checkProfileConditions",
    value: function checkProfileConditions(profile) {
      cov_tmoc3trla().f[15]++;
      cov_tmoc3trla().s[66]++;
      return true;
    }
  }, {
    key: "applyPhaseConfigurations",
    value: function () {
      var _applyPhaseConfigurations = _asyncToGenerator(function* (config) {
        cov_tmoc3trla().f[16]++;
        cov_tmoc3trla().s[67]++;
        console.log('Applying phase configurations:', config);
      });
      function applyPhaseConfigurations(_x4) {
        return _applyPhaseConfigurations.apply(this, arguments);
      }
      return applyPhaseConfigurations;
    }()
  }, {
    key: "trackProfileApplication",
    value: function trackProfileApplication(profileId) {
      cov_tmoc3trla().f[17]++;
      cov_tmoc3trla().s[68]++;
      this.customizationHistory.push({
        timestamp: Date.now(),
        profileId: profileId,
        metrics: {}
      });
      cov_tmoc3trla().s[69]++;
      if (this.customizationHistory.length > 100) {
        cov_tmoc3trla().b[7][0]++;
        cov_tmoc3trla().s[70]++;
        this.customizationHistory.shift();
      } else {
        cov_tmoc3trla().b[7][1]++;
      }
    }
  }, {
    key: "completeABTest",
    value: function () {
      var _completeABTest = _asyncToGenerator(function* (testId) {
        cov_tmoc3trla().f[18]++;
        var variants = (cov_tmoc3trla().s[71]++, this.abTests.get(testId));
        cov_tmoc3trla().s[72]++;
        if (!variants) {
          cov_tmoc3trla().b[8][0]++;
          cov_tmoc3trla().s[73]++;
          return;
        } else {
          cov_tmoc3trla().b[8][1]++;
        }
        var completedVariants = (cov_tmoc3trla().s[74]++, variants.map(function (variant) {
          cov_tmoc3trla().f[19]++;
          cov_tmoc3trla().s[75]++;
          return Object.assign({}, variant, {
            status: 'completed'
          });
        }));
        cov_tmoc3trla().s[76]++;
        this.abTests.set(testId, completedVariants);
        var winner = (cov_tmoc3trla().s[77]++, this.determineABTestWinner(completedVariants));
        cov_tmoc3trla().s[78]++;
        if (winner) {
          cov_tmoc3trla().b[9][0]++;
          cov_tmoc3trla().s[79]++;
          console.log(`A/B test ${testId} completed. Winner: ${winner.id}`);
        } else {
          cov_tmoc3trla().b[9][1]++;
        }
      });
      function completeABTest(_x5) {
        return _completeABTest.apply(this, arguments);
      }
      return completeABTest;
    }()
  }, {
    key: "determineABTestWinner",
    value: function determineABTestWinner(variants) {
      cov_tmoc3trla().f[20]++;
      cov_tmoc3trla().s[80]++;
      if (variants.length === 0) {
        cov_tmoc3trla().b[10][0]++;
        cov_tmoc3trla().s[81]++;
        return null;
      } else {
        cov_tmoc3trla().b[10][1]++;
      }
      cov_tmoc3trla().s[82]++;
      return variants.reduce(function (best, current) {
        cov_tmoc3trla().f[21]++;
        cov_tmoc3trla().s[83]++;
        return current.metrics.performance > best.metrics.performance ? (cov_tmoc3trla().b[11][0]++, current) : (cov_tmoc3trla().b[11][1]++, best);
      });
    }
  }, {
    key: "getCurrentPerformanceMetrics",
    value: function () {
      var _getCurrentPerformanceMetrics = _asyncToGenerator(function* () {
        cov_tmoc3trla().f[22]++;
        cov_tmoc3trla().s[84]++;
        return {
          bundleSize: 446000,
          loadTime: 910,
          renderTime: 16,
          memoryUsage: 65
        };
      });
      function getCurrentPerformanceMetrics() {
        return _getCurrentPerformanceMetrics.apply(this, arguments);
      }
      return getCurrentPerformanceMetrics;
    }()
  }]);
}();
export var advancedCustomizationManager = (cov_tmoc3trla().s[85]++, new AdvancedCustomizationManager());
export default advancedCustomizationManager;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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