{"version": 3, "names": ["_interopRequireDefault", "require", "_classCallCheck2", "_createClass2", "exports", "__esModule", "default", "<PERSON><PERSON>", "key", "value", "alert", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nclass Alert {\n  static alert() {}\n}\nvar _default = exports.default = Alert;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAAC,IAAAA,sBAAA,GAAAC,OAAA;AAAA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAA,IAAAE,aAAA,GAAAH,sBAAA,CAAAC,OAAA;AAEbG,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAAC,IAUnBC,KAAK;EAAA,SAAAA,MAAA;IAAA,IAAAL,gBAAA,CAAAI,OAAA,QAAAC,KAAA;EAAA;EAAA,WAAAJ,aAAA,CAAAG,OAAA,EAAAC,KAAA;IAAAC,GAAA;IAAAC,KAAA,EACT,SAAOC,KAAKA,CAAA,EAAG,CAAC;EAAC;AAAA;AAEnB,IAAIC,QAAQ,GAAGP,OAAO,CAACE,OAAO,GAAGC,KAAK;AACtCK,MAAM,CAACR,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}