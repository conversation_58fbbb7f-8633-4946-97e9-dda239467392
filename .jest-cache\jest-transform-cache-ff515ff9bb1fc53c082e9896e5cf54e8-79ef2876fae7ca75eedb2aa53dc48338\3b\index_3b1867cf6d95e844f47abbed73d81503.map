{"version": 3, "names": ["exports", "__esModule", "default", "findNodeHandle", "component", "Error", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.default = void 0;\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nvar findNodeHandle = component => {\n  throw new Error('findNodeHandle is not supported on web. ' + 'Use the ref property on the component instead.');\n};\nvar _default = exports.default = findNodeHandle;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,OAAO,GAAG,KAAK,CAAC;AAWxB,IAAIC,cAAc,GAAG,SAAjBA,cAAcA,CAAGC,SAAS,EAAI;EAChC,MAAM,IAAIC,KAAK,CAAC,0CAA0C,GAAG,gDAAgD,CAAC;AAChH,CAAC;AACD,IAAIC,QAAQ,GAAGN,OAAO,CAACE,OAAO,GAAGC,cAAc;AAC/CI,MAAM,CAACP,OAAO,GAAGA,OAAO,CAACE,OAAO", "ignoreList": []}