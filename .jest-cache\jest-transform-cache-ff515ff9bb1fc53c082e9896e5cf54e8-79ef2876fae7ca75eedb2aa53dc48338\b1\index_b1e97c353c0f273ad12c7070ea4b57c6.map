{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "_objectSpread2", "_objectWithoutPropertiesLoose2", "_compiler", "_dom", "_transformLocalizeStyle", "_preprocess", "_styleq", "_validate", "_canUseDom", "_excluded", "staticStyleMap", "WeakMap", "sheet", "createSheet", "defaultPreprocessOptions", "shadow", "textShadow", "customStyleq", "styles", "options", "_options", "writingDirection", "preprocessOptions", "isRTL", "styleq", "factory", "transform", "style", "compiledStyle", "get", "localizeStyle", "preprocess", "insertRules", "compiledOrderedRules", "for<PERSON>ach", "_ref", "rules", "order", "rule", "insert", "compileAndInsertAtomic", "_atomic", "atomic", "compileAndInsertReset", "key", "_classic", "classic", "absoluteFillObject", "position", "left", "right", "top", "bottom", "absoluteFill", "create", "x", "Object", "keys", "styleObj", "$$css", "compiledStyles", "indexOf", "split", "process", "env", "NODE_ENV", "validate", "freeze", "set", "compose", "style1", "style2", "len", "arguments", "length", "readableStyles", "Array", "prototype", "slice", "call", "map", "a", "flatten", "Error", "JSON", "stringify", "_len", "_key", "flatArray", "flat", "Infinity", "result", "i", "assign", "getSheet", "id", "textContent", "getTextContent", "StyleSheet", "styleProps", "isArray", "inline", "hairlineWidth", "window", "__REACT_DEVTOOLS_GLOBAL_HOOK__", "resolveRNStyle", "stylesheet", "_default", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = void 0;\nvar _objectSpread2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectSpread2\"));\nvar _objectWithoutPropertiesLoose2 = _interopRequireDefault(require(\"@babel/runtime/helpers/objectWithoutPropertiesLoose\"));\nvar _compiler = require(\"./compiler\");\nvar _dom = require(\"./dom\");\nvar _transformLocalizeStyle = require(\"styleq/transform-localize-style\");\nvar _preprocess = require(\"./preprocess\");\nvar _styleq = require(\"styleq\");\nvar _validate = require(\"./validate\");\nvar _canUseDom = _interopRequireDefault(require(\"../../modules/canUseDom\"));\nvar _excluded = [\"writingDirection\"];\n/**\n * Copyright (c) <PERSON>.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\nvar staticStyleMap = new WeakMap();\nvar sheet = (0, _dom.createSheet)();\nvar defaultPreprocessOptions = {\n  shadow: true,\n  textShadow: true\n};\nfunction customStyleq(styles, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _options = options,\n    writingDirection = _options.writingDirection,\n    preprocessOptions = (0, _objectWithoutPropertiesLoose2.default)(_options, _excluded);\n  var isRTL = writingDirection === 'rtl';\n  return _styleq.styleq.factory({\n    transform(style) {\n      var compiledStyle = staticStyleMap.get(style);\n      if (compiledStyle != null) {\n        return (0, _transformLocalizeStyle.localizeStyle)(compiledStyle, isRTL);\n      }\n      return (0, _preprocess.preprocess)(style, (0, _objectSpread2.default)((0, _objectSpread2.default)({}, defaultPreprocessOptions), preprocessOptions));\n    }\n  })(styles);\n}\nfunction insertRules(compiledOrderedRules) {\n  compiledOrderedRules.forEach(_ref => {\n    var rules = _ref[0],\n      order = _ref[1];\n    if (sheet != null) {\n      rules.forEach(rule => {\n        sheet.insert(rule, order);\n      });\n    }\n  });\n}\nfunction compileAndInsertAtomic(style) {\n  var _atomic = (0, _compiler.atomic)((0, _preprocess.preprocess)(style, defaultPreprocessOptions)),\n    compiledStyle = _atomic[0],\n    compiledOrderedRules = _atomic[1];\n  insertRules(compiledOrderedRules);\n  return compiledStyle;\n}\nfunction compileAndInsertReset(style, key) {\n  var _classic = (0, _compiler.classic)(style, key),\n    compiledStyle = _classic[0],\n    compiledOrderedRules = _classic[1];\n  insertRules(compiledOrderedRules);\n  return compiledStyle;\n}\n\n/* ----- API ----- */\n\nvar absoluteFillObject = {\n  position: 'absolute',\n  left: 0,\n  right: 0,\n  top: 0,\n  bottom: 0\n};\nvar absoluteFill = create({\n  x: (0, _objectSpread2.default)({}, absoluteFillObject)\n}).x;\n\n/**\n * create\n */\nfunction create(styles) {\n  Object.keys(styles).forEach(key => {\n    var styleObj = styles[key];\n    // Only compile at runtime if the style is not already compiled\n    if (styleObj != null && styleObj.$$css !== true) {\n      var compiledStyles;\n      if (key.indexOf('$raw') > -1) {\n        compiledStyles = compileAndInsertReset(styleObj, key.split('$raw')[0]);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          (0, _validate.validate)(styleObj);\n          styles[key] = Object.freeze(styleObj);\n        }\n        compiledStyles = compileAndInsertAtomic(styleObj);\n      }\n      staticStyleMap.set(styleObj, compiledStyles);\n    }\n  });\n  return styles;\n}\n\n/**\n * compose\n */\nfunction compose(style1, style2) {\n  if (process.env.NODE_ENV !== 'production') {\n    /* eslint-disable prefer-rest-params */\n    var len = arguments.length;\n    if (len > 2) {\n      var readableStyles = [...arguments].map(a => flatten(a));\n      throw new Error(\"StyleSheet.compose() only accepts 2 arguments, received \" + len + \": \" + JSON.stringify(readableStyles));\n    }\n    /* eslint-enable prefer-rest-params */\n    /*\n    console.warn(\n      'StyleSheet.compose(a, b) is deprecated; use array syntax, i.e., [a,b].'\n    );\n    */\n  }\n  return [style1, style2];\n}\n\n/**\n * flatten\n */\nfunction flatten() {\n  for (var _len = arguments.length, styles = new Array(_len), _key = 0; _key < _len; _key++) {\n    styles[_key] = arguments[_key];\n  }\n  var flatArray = styles.flat(Infinity);\n  var result = {};\n  for (var i = 0; i < flatArray.length; i++) {\n    var style = flatArray[i];\n    if (style != null && typeof style === 'object') {\n      // $FlowFixMe\n      Object.assign(result, style);\n    }\n  }\n  return result;\n}\n\n/**\n * getSheet\n */\nfunction getSheet() {\n  return {\n    id: sheet.id,\n    textContent: sheet.getTextContent()\n  };\n}\n\n/**\n * resolve\n */\n\nfunction StyleSheet(styles, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var isRTL = options.writingDirection === 'rtl';\n  var styleProps = customStyleq(styles, options);\n  if (Array.isArray(styleProps) && styleProps[1] != null) {\n    styleProps[1] = (0, _compiler.inline)(styleProps[1], isRTL);\n  }\n  return styleProps;\n}\nStyleSheet.absoluteFill = absoluteFill;\nStyleSheet.absoluteFillObject = absoluteFillObject;\nStyleSheet.create = create;\nStyleSheet.compose = compose;\nStyleSheet.flatten = flatten;\nStyleSheet.getSheet = getSheet;\n// `hairlineWidth` is not implemented using screen density as browsers may\n// round sub-pixel values down to `0`, causing the line not to be rendered.\nStyleSheet.hairlineWidth = 1;\nif (_canUseDom.default && window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {\n  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.resolveRNStyle = StyleSheet.flatten;\n}\nvar stylesheet = StyleSheet;\nvar _default = exports.default = stylesheet;\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAG,KAAK,CAAC;AACxB,IAAIG,cAAc,GAAGL,sBAAsB,CAACC,OAAO,CAAC,sCAAsC,CAAC,CAAC;AAC5F,IAAIK,8BAA8B,GAAGN,sBAAsB,CAACC,OAAO,CAAC,qDAAqD,CAAC,CAAC;AAC3H,IAAIM,SAAS,GAAGN,OAAO,aAAa,CAAC;AACrC,IAAIO,IAAI,GAAGP,OAAO,QAAQ,CAAC;AAC3B,IAAIQ,uBAAuB,GAAGR,OAAO,CAAC,iCAAiC,CAAC;AACxE,IAAIS,WAAW,GAAGT,OAAO,eAAe,CAAC;AACzC,IAAIU,OAAO,GAAGV,OAAO,CAAC,QAAQ,CAAC;AAC/B,IAAIW,SAAS,GAAGX,OAAO,aAAa,CAAC;AACrC,IAAIY,UAAU,GAAGb,sBAAsB,CAACC,OAAO,0BAA0B,CAAC,CAAC;AAC3E,IAAIa,SAAS,GAAG,CAAC,kBAAkB,CAAC;AASpC,IAAIC,cAAc,GAAG,IAAIC,OAAO,CAAC,CAAC;AAClC,IAAIC,KAAK,GAAG,CAAC,CAAC,EAAET,IAAI,CAACU,WAAW,EAAE,CAAC;AACnC,IAAIC,wBAAwB,GAAG;EAC7BC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE;AACd,CAAC;AACD,SAASC,YAAYA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACrC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,QAAQ,GAAGD,OAAO;IACpBE,gBAAgB,GAAGD,QAAQ,CAACC,gBAAgB;IAC5CC,iBAAiB,GAAG,CAAC,CAAC,EAAErB,8BAA8B,CAACJ,OAAO,EAAEuB,QAAQ,EAAEX,SAAS,CAAC;EACtF,IAAIc,KAAK,GAAGF,gBAAgB,KAAK,KAAK;EACtC,OAAOf,OAAO,CAACkB,MAAM,CAACC,OAAO,CAAC;IAC5BC,SAAS,WAATA,SAASA,CAACC,KAAK,EAAE;MACf,IAAIC,aAAa,GAAGlB,cAAc,CAACmB,GAAG,CAACF,KAAK,CAAC;MAC7C,IAAIC,aAAa,IAAI,IAAI,EAAE;QACzB,OAAO,CAAC,CAAC,EAAExB,uBAAuB,CAAC0B,aAAa,EAAEF,aAAa,EAAEL,KAAK,CAAC;MACzE;MACA,OAAO,CAAC,CAAC,EAAElB,WAAW,CAAC0B,UAAU,EAAEJ,KAAK,EAAE,CAAC,CAAC,EAAE3B,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEG,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEiB,wBAAwB,CAAC,EAAEQ,iBAAiB,CAAC,CAAC;IACtJ;EACF,CAAC,CAAC,CAACJ,MAAM,CAAC;AACZ;AACA,SAASc,WAAWA,CAACC,oBAAoB,EAAE;EACzCA,oBAAoB,CAACC,OAAO,CAAC,UAAAC,IAAI,EAAI;IACnC,IAAIC,KAAK,GAAGD,IAAI,CAAC,CAAC,CAAC;MACjBE,KAAK,GAAGF,IAAI,CAAC,CAAC,CAAC;IACjB,IAAIvB,KAAK,IAAI,IAAI,EAAE;MACjBwB,KAAK,CAACF,OAAO,CAAC,UAAAI,IAAI,EAAI;QACpB1B,KAAK,CAAC2B,MAAM,CAACD,IAAI,EAAED,KAAK,CAAC;MAC3B,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ;AACA,SAASG,sBAAsBA,CAACb,KAAK,EAAE;EACrC,IAAIc,OAAO,GAAG,CAAC,CAAC,EAAEvC,SAAS,CAACwC,MAAM,EAAE,CAAC,CAAC,EAAErC,WAAW,CAAC0B,UAAU,EAAEJ,KAAK,EAAEb,wBAAwB,CAAC,CAAC;IAC/Fc,aAAa,GAAGa,OAAO,CAAC,CAAC,CAAC;IAC1BR,oBAAoB,GAAGQ,OAAO,CAAC,CAAC,CAAC;EACnCT,WAAW,CAACC,oBAAoB,CAAC;EACjC,OAAOL,aAAa;AACtB;AACA,SAASe,qBAAqBA,CAAChB,KAAK,EAAEiB,GAAG,EAAE;EACzC,IAAIC,QAAQ,GAAG,CAAC,CAAC,EAAE3C,SAAS,CAAC4C,OAAO,EAAEnB,KAAK,EAAEiB,GAAG,CAAC;IAC/ChB,aAAa,GAAGiB,QAAQ,CAAC,CAAC,CAAC;IAC3BZ,oBAAoB,GAAGY,QAAQ,CAAC,CAAC,CAAC;EACpCb,WAAW,CAACC,oBAAoB,CAAC;EACjC,OAAOL,aAAa;AACtB;AAIA,IAAImB,kBAAkB,GAAG;EACvBC,QAAQ,EAAE,UAAU;EACpBC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,GAAG,EAAE,CAAC;EACNC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,YAAY,GAAGC,MAAM,CAAC;EACxBC,CAAC,EAAE,CAAC,CAAC,EAAEvD,cAAc,CAACH,OAAO,EAAE,CAAC,CAAC,EAAEkD,kBAAkB;AACvD,CAAC,CAAC,CAACQ,CAAC;AAKJ,SAASD,MAAMA,CAACpC,MAAM,EAAE;EACtBsC,MAAM,CAACC,IAAI,CAACvC,MAAM,CAAC,CAACgB,OAAO,CAAC,UAAAU,GAAG,EAAI;IACjC,IAAIc,QAAQ,GAAGxC,MAAM,CAAC0B,GAAG,CAAC;IAE1B,IAAIc,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACC,KAAK,KAAK,IAAI,EAAE;MAC/C,IAAIC,cAAc;MAClB,IAAIhB,GAAG,CAACiB,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE;QAC5BD,cAAc,GAAGjB,qBAAqB,CAACe,QAAQ,EAAEd,GAAG,CAACkB,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;MACxE,CAAC,MAAM;QACL,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;UACzC,CAAC,CAAC,EAAE1D,SAAS,CAAC2D,QAAQ,EAAER,QAAQ,CAAC;UACjCxC,MAAM,CAAC0B,GAAG,CAAC,GAAGY,MAAM,CAACW,MAAM,CAACT,QAAQ,CAAC;QACvC;QACAE,cAAc,GAAGpB,sBAAsB,CAACkB,QAAQ,CAAC;MACnD;MACAhD,cAAc,CAAC0D,GAAG,CAACV,QAAQ,EAAEE,cAAc,CAAC;IAC9C;EACF,CAAC,CAAC;EACF,OAAO1C,MAAM;AACf;AAKA,SAASmD,OAAOA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/B,IAAIR,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IAEzC,IAAIO,GAAG,GAAGC,SAAS,CAACC,MAAM;IAC1B,IAAIF,GAAG,GAAG,CAAC,EAAE;MACX,IAAIG,cAAc,GAAGC,KAAA,CAAAC,SAAA,CAAAC,KAAA,CAAAC,IAAA,CAAIN,SAAS,EAAEO,GAAG,CAAC,UAAAC,CAAC;QAAA,OAAIC,OAAO,CAACD,CAAC,CAAC;MAAA,EAAC;MACxD,MAAM,IAAIE,KAAK,CAAC,0DAA0D,GAAGX,GAAG,GAAG,IAAI,GAAGY,IAAI,CAACC,SAAS,CAACV,cAAc,CAAC,CAAC;IAC3H;EAOF;EACA,OAAO,CAACL,MAAM,EAAEC,MAAM,CAAC;AACzB;AAKA,SAASW,OAAOA,CAAA,EAAG;EACjB,KAAK,IAAII,IAAI,GAAGb,SAAS,CAACC,MAAM,EAAExD,MAAM,GAAG,IAAI0D,KAAK,CAACU,IAAI,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;IACzFrE,MAAM,CAACqE,IAAI,CAAC,GAAGd,SAAS,CAACc,IAAI,CAAC;EAChC;EACA,IAAIC,SAAS,GAAGtE,MAAM,CAACuE,IAAI,CAACC,QAAQ,CAAC;EACrC,IAAIC,MAAM,GAAG,CAAC,CAAC;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,SAAS,CAACd,MAAM,EAAEkB,CAAC,EAAE,EAAE;IACzC,IAAIjE,KAAK,GAAG6D,SAAS,CAACI,CAAC,CAAC;IACxB,IAAIjE,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAE9C6B,MAAM,CAACqC,MAAM,CAACF,MAAM,EAAEhE,KAAK,CAAC;IAC9B;EACF;EACA,OAAOgE,MAAM;AACf;AAKA,SAASG,QAAQA,CAAA,EAAG;EAClB,OAAO;IACLC,EAAE,EAAEnF,KAAK,CAACmF,EAAE;IACZC,WAAW,EAAEpF,KAAK,CAACqF,cAAc,CAAC;EACpC,CAAC;AACH;AAMA,SAASC,UAAUA,CAAChF,MAAM,EAAEC,OAAO,EAAE;EACnC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAII,KAAK,GAAGJ,OAAO,CAACE,gBAAgB,KAAK,KAAK;EAC9C,IAAI8E,UAAU,GAAGlF,YAAY,CAACC,MAAM,EAAEC,OAAO,CAAC;EAC9C,IAAIyD,KAAK,CAACwB,OAAO,CAACD,UAAU,CAAC,IAAIA,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;IACtDA,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAEjG,SAAS,CAACmG,MAAM,EAAEF,UAAU,CAAC,CAAC,CAAC,EAAE5E,KAAK,CAAC;EAC7D;EACA,OAAO4E,UAAU;AACnB;AACAD,UAAU,CAAC7C,YAAY,GAAGA,YAAY;AACtC6C,UAAU,CAACnD,kBAAkB,GAAGA,kBAAkB;AAClDmD,UAAU,CAAC5C,MAAM,GAAGA,MAAM;AAC1B4C,UAAU,CAAC7B,OAAO,GAAGA,OAAO;AAC5B6B,UAAU,CAAChB,OAAO,GAAGA,OAAO;AAC5BgB,UAAU,CAACJ,QAAQ,GAAGA,QAAQ;AAG9BI,UAAU,CAACI,aAAa,GAAG,CAAC;AAC5B,IAAI9F,UAAU,CAACX,OAAO,IAAI0G,MAAM,CAACC,8BAA8B,EAAE;EAC/DD,MAAM,CAACC,8BAA8B,CAACC,cAAc,GAAGP,UAAU,CAAChB,OAAO;AAC3E;AACA,IAAIwB,UAAU,GAAGR,UAAU;AAC3B,IAAIS,QAAQ,GAAG7G,OAAO,CAACD,OAAO,GAAG6G,UAAU;AAC3CE,MAAM,CAAC9G,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}