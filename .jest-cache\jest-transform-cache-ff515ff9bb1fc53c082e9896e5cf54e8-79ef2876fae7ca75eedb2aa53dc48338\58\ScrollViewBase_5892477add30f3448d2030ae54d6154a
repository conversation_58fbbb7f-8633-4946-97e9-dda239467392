713d7134666d92770b11e6b1c3c77243
"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
exports.__esModule = true;
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var _objectWithoutPropertiesLoose2 = _interopRequireDefault(require("@babel/runtime/helpers/objectWithoutPropertiesLoose"));
var React = _interopRequireWildcard(require("react"));
var _StyleSheet = _interopRequireDefault(require("../StyleSheet"));
var _View = _interopRequireDefault(require("../View"));
var _useMergeRefs = _interopRequireDefault(require("../../modules/useMergeRefs"));
var _excluded = ["onScroll", "onTouchMove", "onWheel", "scrollEnabled", "scrollEventThrottle", "showsHorizontalScrollIndicator", "showsVerticalScrollIndicator", "style"];
function normalizeScrollEvent(e) {
  return {
    nativeEvent: {
      contentOffset: {
        get x() {
          return e.target.scrollLeft;
        },
        get y() {
          return e.target.scrollTop;
        }
      },
      contentSize: {
        get height() {
          return e.target.scrollHeight;
        },
        get width() {
          return e.target.scrollWidth;
        }
      },
      layoutMeasurement: {
        get height() {
          return e.target.offsetHeight;
        },
        get width() {
          return e.target.offsetWidth;
        }
      }
    },
    timeStamp: Date.now()
  };
}
function shouldEmitScrollEvent(lastTick, eventThrottle) {
  var timeSinceLastTick = Date.now() - lastTick;
  return eventThrottle > 0 && timeSinceLastTick >= eventThrottle;
}
var ScrollViewBase = React.forwardRef(function (props, forwardedRef) {
  var onScroll = props.onScroll,
    onTouchMove = props.onTouchMove,
    onWheel = props.onWheel,
    _props$scrollEnabled = props.scrollEnabled,
    scrollEnabled = _props$scrollEnabled === void 0 ? true : _props$scrollEnabled,
    _props$scrollEventThr = props.scrollEventThrottle,
    scrollEventThrottle = _props$scrollEventThr === void 0 ? 0 : _props$scrollEventThr,
    showsHorizontalScrollIndicator = props.showsHorizontalScrollIndicator,
    showsVerticalScrollIndicator = props.showsVerticalScrollIndicator,
    style = props.style,
    rest = (0, _objectWithoutPropertiesLoose2.default)(props, _excluded);
  var scrollState = React.useRef({
    isScrolling: false,
    scrollLastTick: 0
  });
  var scrollTimeout = React.useRef(null);
  var scrollRef = React.useRef(null);
  function createPreventableScrollHandler(handler) {
    return function (e) {
      if (scrollEnabled) {
        if (handler) {
          handler(e);
        }
      }
    };
  }
  function handleScroll(e) {
    e.stopPropagation();
    if (e.target === scrollRef.current) {
      e.persist();
      if (scrollTimeout.current != null) {
        clearTimeout(scrollTimeout.current);
      }
      scrollTimeout.current = setTimeout(function () {
        handleScrollEnd(e);
      }, 100);
      if (scrollState.current.isScrolling) {
        if (shouldEmitScrollEvent(scrollState.current.scrollLastTick, scrollEventThrottle)) {
          handleScrollTick(e);
        }
      } else {
        handleScrollStart(e);
      }
    }
  }
  function handleScrollStart(e) {
    scrollState.current.isScrolling = true;
    handleScrollTick(e);
  }
  function handleScrollTick(e) {
    scrollState.current.scrollLastTick = Date.now();
    if (onScroll) {
      onScroll(normalizeScrollEvent(e));
    }
  }
  function handleScrollEnd(e) {
    scrollState.current.isScrolling = false;
    if (onScroll) {
      onScroll(normalizeScrollEvent(e));
    }
  }
  var hideScrollbar = showsHorizontalScrollIndicator === false || showsVerticalScrollIndicator === false;
  return React.createElement(_View.default, (0, _extends2.default)({}, rest, {
    onScroll: handleScroll,
    onTouchMove: createPreventableScrollHandler(onTouchMove),
    onWheel: createPreventableScrollHandler(onWheel),
    ref: (0, _useMergeRefs.default)(scrollRef, forwardedRef),
    style: [style, !scrollEnabled && styles.scrollDisabled, hideScrollbar && styles.hideScrollbar]
  }));
});
var styles = _StyleSheet.default.create({
  scrollDisabled: {
    overflowX: 'hidden',
    overflowY: 'hidden',
    touchAction: 'none'
  },
  hideScrollbar: {
    scrollbarWidth: 'none'
  }
});
var _default = exports.default = ScrollViewBase;
module.exports = exports.default;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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