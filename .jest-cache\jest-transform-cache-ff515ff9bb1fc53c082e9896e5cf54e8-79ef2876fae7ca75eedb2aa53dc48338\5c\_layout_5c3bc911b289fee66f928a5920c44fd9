f17a6ec564e0427996de91102fe8e4f5
function cov_kidrxquod() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\_layout.tsx";
  var hash = "58621928b835eae06205870811e1f34ee5d9a7df";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\(tabs)\\_layout.tsx",
    statementMap: {
      "0": {
        start: {
          line: 5,
          column: 15
        },
        end: {
          line: 12,
          column: 1
        }
      },
      "1": {
        start: {
          line: 15,
          column: 2
        },
        end: {
          line: 97,
          column: 4
        }
      },
      "2": {
        start: {
          line: 29,
          column: 12
        },
        end: {
          line: 29,
          column: 62
        }
      },
      "3": {
        start: {
          line: 38,
          column: 12
        },
        end: {
          line: 38,
          column: 62
        }
      },
      "4": {
        start: {
          line: 47,
          column: 12
        },
        end: {
          line: 47,
          column: 66
        }
      },
      "5": {
        start: {
          line: 56,
          column: 12
        },
        end: {
          line: 56,
          column: 71
        }
      },
      "6": {
        start: {
          line: 65,
          column: 12
        },
        end: {
          line: 65,
          column: 63
        }
      },
      "7": {
        start: {
          line: 74,
          column: 12
        },
        end: {
          line: 74,
          column: 68
        }
      },
      "8": {
        start: {
          line: 83,
          column: 12
        },
        end: {
          line: 83,
          column: 68
        }
      },
      "9": {
        start: {
          line: 92,
          column: 12
        },
        end: {
          line: 92,
          column: 62
        }
      },
      "10": {
        start: {
          line: 100,
          column: 15
        },
        end: {
          line: 114,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "TabLayout",
        decl: {
          start: {
            line: 14,
            column: 24
          },
          end: {
            line: 14,
            column: 33
          }
        },
        loc: {
          start: {
            line: 14,
            column: 36
          },
          end: {
            line: 98,
            column: 1
          }
        },
        line: 14
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 28,
            column: 22
          },
          end: {
            line: 28,
            column: 23
          }
        },
        loc: {
          start: {
            line: 29,
            column: 12
          },
          end: {
            line: 29,
            column: 62
          }
        },
        line: 29
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 37,
            column: 22
          },
          end: {
            line: 37,
            column: 23
          }
        },
        loc: {
          start: {
            line: 38,
            column: 12
          },
          end: {
            line: 38,
            column: 62
          }
        },
        line: 38
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 46,
            column: 22
          },
          end: {
            line: 46,
            column: 23
          }
        },
        loc: {
          start: {
            line: 47,
            column: 12
          },
          end: {
            line: 47,
            column: 66
          }
        },
        line: 47
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 55,
            column: 22
          },
          end: {
            line: 55,
            column: 23
          }
        },
        loc: {
          start: {
            line: 56,
            column: 12
          },
          end: {
            line: 56,
            column: 71
          }
        },
        line: 56
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 64,
            column: 22
          },
          end: {
            line: 64,
            column: 23
          }
        },
        loc: {
          start: {
            line: 65,
            column: 12
          },
          end: {
            line: 65,
            column: 63
          }
        },
        line: 65
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 73,
            column: 22
          },
          end: {
            line: 73,
            column: 23
          }
        },
        loc: {
          start: {
            line: 74,
            column: 12
          },
          end: {
            line: 74,
            column: 68
          }
        },
        line: 74
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 82,
            column: 22
          },
          end: {
            line: 82,
            column: 23
          }
        },
        loc: {
          start: {
            line: 83,
            column: 12
          },
          end: {
            line: 83,
            column: 68
          }
        },
        line: 83
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 91,
            column: 22
          },
          end: {
            line: 91,
            column: 23
          }
        },
        loc: {
          start: {
            line: 92,
            column: 12
          },
          end: {
            line: 92,
            column: 62
          }
        },
        line: 92
      }
    },
    branchMap: {},
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0
    },
    b: {},
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "58621928b835eae06205870811e1f34ee5d9a7df"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_kidrxquod = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_kidrxquod();
import { Tabs } from 'expo-router';
import { Chrome as Home, Play, MessageCircle, TrendingUp, User, Gamepad2, CreditCard, Users } from 'lucide-react-native';
import { StyleSheet } from 'react-native';
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_kidrxquod().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f3f4f6'
});
export default function TabLayout() {
  cov_kidrxquod().f[0]++;
  cov_kidrxquod().s[1]++;
  return _jsxs(Tabs, {
    screenOptions: {
      headerShown: false,
      tabBarStyle: styles.tabBar,
      tabBarActiveTintColor: colors.primary,
      tabBarInactiveTintColor: colors.gray,
      tabBarLabelStyle: styles.tabBarLabel
    },
    children: [_jsx(Tabs.Screen, {
      name: "index",
      options: {
        title: 'Home',
        tabBarIcon: function tabBarIcon(_ref) {
          var size = _ref.size,
            color = _ref.color;
          cov_kidrxquod().f[1]++;
          cov_kidrxquod().s[2]++;
          return _jsx(Home, {
            size: size,
            color: color,
            strokeWidth: 2
          });
        }
      }
    }), _jsx(Tabs.Screen, {
      name: "training",
      options: {
        title: 'Training',
        tabBarIcon: function tabBarIcon(_ref2) {
          var size = _ref2.size,
            color = _ref2.color;
          cov_kidrxquod().f[2]++;
          cov_kidrxquod().s[3]++;
          return _jsx(Play, {
            size: size,
            color: color,
            strokeWidth: 2
          });
        }
      }
    }), _jsx(Tabs.Screen, {
      name: "simulation",
      options: {
        title: 'Match Sim',
        tabBarIcon: function tabBarIcon(_ref3) {
          var size = _ref3.size,
            color = _ref3.color;
          cov_kidrxquod().f[3]++;
          cov_kidrxquod().s[4]++;
          return _jsx(Gamepad2, {
            size: size,
            color: color,
            strokeWidth: 2
          });
        }
      }
    }), _jsx(Tabs.Screen, {
      name: "coach",
      options: {
        title: 'Coach',
        tabBarIcon: function tabBarIcon(_ref4) {
          var size = _ref4.size,
            color = _ref4.color;
          cov_kidrxquod().f[4]++;
          cov_kidrxquod().s[5]++;
          return _jsx(MessageCircle, {
            size: size,
            color: color,
            strokeWidth: 2
          });
        }
      }
    }), _jsx(Tabs.Screen, {
      name: "social",
      options: {
        title: 'Social',
        tabBarIcon: function tabBarIcon(_ref5) {
          var size = _ref5.size,
            color = _ref5.color;
          cov_kidrxquod().f[5]++;
          cov_kidrxquod().s[6]++;
          return _jsx(Users, {
            size: size,
            color: color,
            strokeWidth: 2
          });
        }
      }
    }), _jsx(Tabs.Screen, {
      name: "progress",
      options: {
        title: 'Progress',
        tabBarIcon: function tabBarIcon(_ref6) {
          var size = _ref6.size,
            color = _ref6.color;
          cov_kidrxquod().f[6]++;
          cov_kidrxquod().s[7]++;
          return _jsx(TrendingUp, {
            size: size,
            color: color,
            strokeWidth: 2
          });
        }
      }
    }), _jsx(Tabs.Screen, {
      name: "subscription",
      options: {
        title: 'Premium',
        tabBarIcon: function tabBarIcon(_ref7) {
          var size = _ref7.size,
            color = _ref7.color;
          cov_kidrxquod().f[7]++;
          cov_kidrxquod().s[8]++;
          return _jsx(CreditCard, {
            size: size,
            color: color,
            strokeWidth: 2
          });
        }
      }
    }), _jsx(Tabs.Screen, {
      name: "profile",
      options: {
        title: 'Profile',
        tabBarIcon: function tabBarIcon(_ref8) {
          var size = _ref8.size,
            color = _ref8.color;
          cov_kidrxquod().f[8]++;
          cov_kidrxquod().s[9]++;
          return _jsx(User, {
            size: size,
            color: color,
            strokeWidth: 2
          });
        }
      }
    })]
  });
}
var styles = (cov_kidrxquod().s[10]++, StyleSheet.create({
  tabBar: {
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.lightGray,
    paddingTop: 8,
    paddingBottom: 8,
    height: 70
  },
  tabBarLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    marginTop: 4
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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