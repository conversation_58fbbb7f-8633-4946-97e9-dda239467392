a9d55ad1c7279b09c0c649af60d39207
import _toConsumableArray from "@babel/runtime/helpers/toConsumableArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
function cov_2obylvc8lc() {
  var path = "C:\\_SaaS\\AceMind\\project\\app\\social\\challenges.tsx";
  var hash = "d3af83a6d53528dd01610fdc01af953364fcde04";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\app\\social\\challenges.tsx",
    statementMap: {
      "0": {
        start: {
          line: 34,
          column: 15
        },
        end: {
          line: 45,
          column: 1
        }
      },
      "1": {
        start: {
          line: 47,
          column: 25
        },
        end: {
          line: 52,
          column: 1
        }
      },
      "2": {
        start: {
          line: 61,
          column: 52
        },
        end: {
          line: 151,
          column: 1
        }
      },
      "3": {
        start: {
          line: 67,
          column: 27
        },
        end: {
          line: 73,
          column: 3
        }
      },
      "4": {
        start: {
          line: 68,
          column: 20
        },
        end: {
          line: 68,
          column: 47
        }
      },
      "5": {
        start: {
          line: 69,
          column: 16
        },
        end: {
          line: 69,
          column: 26
        }
      },
      "6": {
        start: {
          line: 70,
          column: 21
        },
        end: {
          line: 70,
          column: 54
        }
      },
      "7": {
        start: {
          line: 71,
          column: 21
        },
        end: {
          line: 71,
          column: 64
        }
      },
      "8": {
        start: {
          line: 72,
          column: 4
        },
        end: {
          line: 72,
          column: 33
        }
      },
      "9": {
        start: {
          line: 75,
          column: 22
        },
        end: {
          line: 83,
          column: 3
        }
      },
      "10": {
        start: {
          line: 76,
          column: 4
        },
        end: {
          line: 82,
          column: 5
        }
      },
      "11": {
        start: {
          line: 77,
          column: 20
        },
        end: {
          line: 77,
          column: 71
        }
      },
      "12": {
        start: {
          line: 78,
          column: 24
        },
        end: {
          line: 78,
          column: 71
        }
      },
      "13": {
        start: {
          line: 79,
          column: 23
        },
        end: {
          line: 79,
          column: 72
        }
      },
      "14": {
        start: {
          line: 80,
          column: 24
        },
        end: {
          line: 80,
          column: 72
        }
      },
      "15": {
        start: {
          line: 81,
          column: 15
        },
        end: {
          line: 81,
          column: 66
        }
      },
      "16": {
        start: {
          line: 85,
          column: 2
        },
        end: {
          line: 150,
          column: 4
        }
      },
      "17": {
        start: {
          line: 122,
          column: 12
        },
        end: {
          line: 125,
          column: 19
        }
      },
      "18": {
        start: {
          line: 133,
          column: 25
        },
        end: {
          line: 133,
          column: 49
        }
      },
      "19": {
        start: {
          line: 140,
          column: 27
        },
        end: {
          line: 140,
          column: 47
        }
      },
      "20": {
        start: {
          line: 158,
          column: 48
        },
        end: {
          line: 202,
          column: 1
        }
      },
      "21": {
        start: {
          line: 159,
          column: 32
        },
        end: {
          line: 188,
          column: 3
        }
      },
      "22": {
        start: {
          line: 160,
          column: 26
        },
        end: {
          line: 160,
          column: 55
        }
      },
      "23": {
        start: {
          line: 161,
          column: 25
        },
        end: {
          line: 166,
          column: 5
        }
      },
      "24": {
        start: {
          line: 162,
          column: 6
        },
        end: {
          line: 162,
          column: 39
        }
      },
      "25": {
        start: {
          line: 162,
          column: 22
        },
        end: {
          line: 162,
          column: 39
        }
      },
      "26": {
        start: {
          line: 163,
          column: 6
        },
        end: {
          line: 163,
          column: 39
        }
      },
      "27": {
        start: {
          line: 163,
          column: 22
        },
        end: {
          line: 163,
          column: 39
        }
      },
      "28": {
        start: {
          line: 164,
          column: 6
        },
        end: {
          line: 164,
          column: 39
        }
      },
      "29": {
        start: {
          line: 164,
          column: 22
        },
        end: {
          line: 164,
          column: 39
        }
      },
      "30": {
        start: {
          line: 165,
          column: 6
        },
        end: {
          line: 165,
          column: 25
        }
      },
      "31": {
        start: {
          line: 168,
          column: 4
        },
        end: {
          line: 187,
          column: 6
        }
      },
      "32": {
        start: {
          line: 190,
          column: 2
        },
        end: {
          line: 201,
          column: 4
        }
      },
      "33": {
        start: {
          line: 196,
          column: 32
        },
        end: {
          line: 196,
          column: 43
        }
      },
      "34": {
        start: {
          line: 205,
          column: 19
        },
        end: {
          line: 205,
          column: 28
        }
      },
      "35": {
        start: {
          line: 206,
          column: 38
        },
        end: {
          line: 206,
          column: 63
        }
      },
      "36": {
        start: {
          line: 207,
          column: 32
        },
        end: {
          line: 207,
          column: 46
        }
      },
      "37": {
        start: {
          line: 208,
          column: 52
        },
        end: {
          line: 208,
          column: 84
        }
      },
      "38": {
        start: {
          line: 209,
          column: 40
        },
        end: {
          line: 209,
          column: 55
        }
      },
      "39": {
        start: {
          line: 210,
          column: 64
        },
        end: {
          line: 210,
          column: 96
        }
      },
      "40": {
        start: {
          line: 212,
          column: 2
        },
        end: {
          line: 214,
          column: 9
        }
      },
      "41": {
        start: {
          line: 213,
          column: 4
        },
        end: {
          line: 213,
          column: 21
        }
      },
      "42": {
        start: {
          line: 216,
          column: 25
        },
        end: {
          line: 231,
          column: 3
        }
      },
      "43": {
        start: {
          line: 217,
          column: 4
        },
        end: {
          line: 230,
          column: 5
        }
      },
      "44": {
        start: {
          line: 218,
          column: 6
        },
        end: {
          line: 218,
          column: 23
        }
      },
      "45": {
        start: {
          line: 219,
          column: 31
        },
        end: {
          line: 219,
          column: 72
        }
      },
      "46": {
        start: {
          line: 220,
          column: 6
        },
        end: {
          line: 220,
          column: 38
        }
      },
      "47": {
        start: {
          line: 226,
          column: 6
        },
        end: {
          line: 226,
          column: 57
        }
      },
      "48": {
        start: {
          line: 227,
          column: 6
        },
        end: {
          line: 227,
          column: 56
        }
      },
      "49": {
        start: {
          line: 229,
          column: 6
        },
        end: {
          line: 229,
          column: 24
        }
      },
      "50": {
        start: {
          line: 233,
          column: 30
        },
        end: {
          line: 261,
          column: 3
        }
      },
      "51": {
        start: {
          line: 234,
          column: 4
        },
        end: {
          line: 234,
          column: 26
        }
      },
      "52": {
        start: {
          line: 234,
          column: 19
        },
        end: {
          line: 234,
          column: 26
        }
      },
      "53": {
        start: {
          line: 236,
          column: 4
        },
        end: {
          line: 260,
          column: 6
        }
      },
      "54": {
        start: {
          line: 244,
          column: 12
        },
        end: {
          line: 256,
          column: 13
        }
      },
      "55": {
        start: {
          line: 245,
          column: 30
        },
        end: {
          line: 245,
          column: 85
        }
      },
      "56": {
        start: {
          line: 246,
          column: 14
        },
        end: {
          line: 252,
          column: 15
        }
      },
      "57": {
        start: {
          line: 247,
          column: 16
        },
        end: {
          line: 247,
          column: 84
        }
      },
      "58": {
        start: {
          line: 247,
          column: 51
        },
        end: {
          line: 247,
          column: 82
        }
      },
      "59": {
        start: {
          line: 248,
          column: 16
        },
        end: {
          line: 248,
          column: 73
        }
      },
      "60": {
        start: {
          line: 249,
          column: 16
        },
        end: {
          line: 249,
          column: 33
        }
      },
      "61": {
        start: {
          line: 251,
          column: 16
        },
        end: {
          line: 251,
          column: 65
        }
      },
      "62": {
        start: {
          line: 254,
          column: 14
        },
        end: {
          line: 254,
          column: 64
        }
      },
      "63": {
        start: {
          line: 255,
          column: 14
        },
        end: {
          line: 255,
          column: 63
        }
      },
      "64": {
        start: {
          line: 263,
          column: 28
        },
        end: {
          line: 266,
          column: 3
        }
      },
      "65": {
        start: {
          line: 264,
          column: 4
        },
        end: {
          line: 264,
          column: 36
        }
      },
      "66": {
        start: {
          line: 265,
          column: 4
        },
        end: {
          line: 265,
          column: 25
        }
      },
      "67": {
        start: {
          line: 268,
          column: 32
        },
        end: {
          line: 270,
          column: 3
        }
      },
      "68": {
        start: {
          line: 269,
          column: 4
        },
        end: {
          line: 269,
          column: 51
        }
      },
      "69": {
        start: {
          line: 272,
          column: 26
        },
        end: {
          line: 279,
          column: 3
        }
      },
      "70": {
        start: {
          line: 273,
          column: 4
        },
        end: {
          line: 278,
          column: 6
        }
      },
      "71": {
        start: {
          line: 281,
          column: 2
        },
        end: {
          line: 389,
          column: 4
        }
      },
      "72": {
        start: {
          line: 290,
          column: 45
        },
        end: {
          line: 290,
          column: 58
        }
      },
      "73": {
        start: {
          line: 322,
          column: 40
        },
        end: {
          line: 322,
          column: 47
        }
      },
      "74": {
        start: {
          line: 334,
          column: 34
        },
        end: {
          line: 334,
          column: 55
        }
      },
      "75": {
        start: {
          line: 338,
          column: 49
        },
        end: {
          line: 338,
          column: 70
        }
      },
      "76": {
        start: {
          line: 392,
          column: 15
        },
        end: {
          line: 694,
          column: 2
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 61,
            column: 52
          },
          end: {
            line: 61,
            column: 53
          }
        },
        loc: {
          start: {
            line: 66,
            column: 6
          },
          end: {
            line: 151,
            column: 1
          }
        },
        line: 66
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 67,
            column: 27
          },
          end: {
            line: 67,
            column: 28
          }
        },
        loc: {
          start: {
            line: 67,
            column: 33
          },
          end: {
            line: 73,
            column: 3
          }
        },
        line: 67
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 75,
            column: 22
          },
          end: {
            line: 75,
            column: 23
          }
        },
        loc: {
          start: {
            line: 75,
            column: 28
          },
          end: {
            line: 83,
            column: 3
          }
        },
        line: 75
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 121,
            column: 40
          },
          end: {
            line: 121,
            column: 41
          }
        },
        loc: {
          start: {
            line: 122,
            column: 12
          },
          end: {
            line: 125,
            column: 19
          }
        },
        line: 122
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 133,
            column: 19
          },
          end: {
            line: 133,
            column: 20
          }
        },
        loc: {
          start: {
            line: 133,
            column: 25
          },
          end: {
            line: 133,
            column: 49
          }
        },
        line: 133
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 140,
            column: 21
          },
          end: {
            line: 140,
            column: 22
          }
        },
        loc: {
          start: {
            line: 140,
            column: 27
          },
          end: {
            line: 140,
            column: 47
          }
        },
        line: 140
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 158,
            column: 48
          },
          end: {
            line: 158,
            column: 49
          }
        },
        loc: {
          start: {
            line: 158,
            column: 84
          },
          end: {
            line: 202,
            column: 1
          }
        },
        line: 158
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 159,
            column: 32
          },
          end: {
            line: 159,
            column: 33
          }
        },
        loc: {
          start: {
            line: 159,
            column: 94
          },
          end: {
            line: 188,
            column: 3
          }
        },
        line: 159
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 161,
            column: 25
          },
          end: {
            line: 161,
            column: 26
          }
        },
        loc: {
          start: {
            line: 161,
            column: 43
          },
          end: {
            line: 166,
            column: 5
          }
        },
        line: 161
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 196,
            column: 22
          },
          end: {
            line: 196,
            column: 23
          }
        },
        loc: {
          start: {
            line: 196,
            column: 32
          },
          end: {
            line: 196,
            column: 43
          }
        },
        line: 196
      },
      "10": {
        name: "ChallengesScreen",
        decl: {
          start: {
            line: 204,
            column: 24
          },
          end: {
            line: 204,
            column: 40
          }
        },
        loc: {
          start: {
            line: 204,
            column: 43
          },
          end: {
            line: 390,
            column: 1
          }
        },
        line: 204
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 212,
            column: 12
          },
          end: {
            line: 212,
            column: 13
          }
        },
        loc: {
          start: {
            line: 212,
            column: 18
          },
          end: {
            line: 214,
            column: 3
          }
        },
        line: 212
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 216,
            column: 25
          },
          end: {
            line: 216,
            column: 26
          }
        },
        loc: {
          start: {
            line: 216,
            column: 37
          },
          end: {
            line: 231,
            column: 3
          }
        },
        line: 216
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 233,
            column: 30
          },
          end: {
            line: 233,
            column: 31
          }
        },
        loc: {
          start: {
            line: 233,
            column: 61
          },
          end: {
            line: 261,
            column: 3
          }
        },
        line: 233
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 243,
            column: 19
          },
          end: {
            line: 243,
            column: 20
          }
        },
        loc: {
          start: {
            line: 243,
            column: 31
          },
          end: {
            line: 257,
            column: 11
          }
        },
        line: 243
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 247,
            column: 43
          },
          end: {
            line: 247,
            column: 44
          }
        },
        loc: {
          start: {
            line: 247,
            column: 51
          },
          end: {
            line: 247,
            column: 82
          }
        },
        line: 247
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 263,
            column: 28
          },
          end: {
            line: 263,
            column: 29
          }
        },
        loc: {
          start: {
            line: 263,
            column: 54
          },
          end: {
            line: 266,
            column: 3
          }
        },
        line: 263
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 268,
            column: 32
          },
          end: {
            line: 268,
            column: 33
          }
        },
        loc: {
          start: {
            line: 268,
            column: 38
          },
          end: {
            line: 270,
            column: 3
          }
        },
        line: 268
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 272,
            column: 26
          },
          end: {
            line: 272,
            column: 27
          }
        },
        loc: {
          start: {
            line: 273,
            column: 4
          },
          end: {
            line: 278,
            column: 6
          }
        },
        line: 273
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 290,
            column: 39
          },
          end: {
            line: 290,
            column: 40
          }
        },
        loc: {
          start: {
            line: 290,
            column: 45
          },
          end: {
            line: 290,
            column: 58
          }
        },
        line: 290
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 322,
            column: 30
          },
          end: {
            line: 322,
            column: 31
          }
        },
        loc: {
          start: {
            line: 322,
            column: 40
          },
          end: {
            line: 322,
            column: 47
          }
        },
        line: 322
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 334,
            column: 28
          },
          end: {
            line: 334,
            column: 29
          }
        },
        loc: {
          start: {
            line: 334,
            column: 34
          },
          end: {
            line: 334,
            column: 55
          }
        },
        line: 334
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 338,
            column: 43
          },
          end: {
            line: 338,
            column: 44
          }
        },
        loc: {
          start: {
            line: 338,
            column: 49
          },
          end: {
            line: 338,
            column: 70
          }
        },
        line: 338
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 76,
            column: 4
          },
          end: {
            line: 82,
            column: 5
          }
        },
        type: "switch",
        locations: [{
          start: {
            line: 77,
            column: 6
          },
          end: {
            line: 77,
            column: 71
          }
        }, {
          start: {
            line: 78,
            column: 6
          },
          end: {
            line: 78,
            column: 71
          }
        }, {
          start: {
            line: 79,
            column: 6
          },
          end: {
            line: 79,
            column: 72
          }
        }, {
          start: {
            line: 80,
            column: 6
          },
          end: {
            line: 80,
            column: 72
          }
        }, {
          start: {
            line: 81,
            column: 6
          },
          end: {
            line: 81,
            column: 66
          }
        }],
        line: 76
      },
      "1": {
        loc: {
          start: {
            line: 137,
            column: 9
          },
          end: {
            line: 147,
            column: 9
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 138,
            column: 10
          },
          end: {
            line: 142,
            column: 12
          }
        }, {
          start: {
            line: 144,
            column: 10
          },
          end: {
            line: 146,
            column: 17
          }
        }],
        line: 137
      },
      "2": {
        loc: {
          start: {
            line: 162,
            column: 6
          },
          end: {
            line: 162,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 162,
            column: 6
          },
          end: {
            line: 162,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 162
      },
      "3": {
        loc: {
          start: {
            line: 163,
            column: 6
          },
          end: {
            line: 163,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 163,
            column: 6
          },
          end: {
            line: 163,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 163
      },
      "4": {
        loc: {
          start: {
            line: 164,
            column: 6
          },
          end: {
            line: 164,
            column: 39
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 164,
            column: 6
          },
          end: {
            line: 164,
            column: 39
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 164
      },
      "5": {
        loc: {
          start: {
            line: 169,
            column: 44
          },
          end: {
            line: 169,
            column: 83
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 169,
            column: 44
          },
          end: {
            line: 169,
            column: 57
          }
        }, {
          start: {
            line: 169,
            column: 61
          },
          end: {
            line: 169,
            column: 83
          }
        }],
        line: 169
      },
      "6": {
        loc: {
          start: {
            line: 176,
            column: 41
          },
          end: {
            line: 176,
            column: 80
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 176,
            column: 41
          },
          end: {
            line: 176,
            column: 54
          }
        }, {
          start: {
            line: 176,
            column: 58
          },
          end: {
            line: 176,
            column: 80
          }
        }],
        line: 176
      },
      "7": {
        loc: {
          start: {
            line: 183,
            column: 36
          },
          end: {
            line: 183,
            column: 72
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 183,
            column: 36
          },
          end: {
            line: 183,
            column: 49
          }
        }, {
          start: {
            line: 183,
            column: 53
          },
          end: {
            line: 183,
            column: 72
          }
        }],
        line: 183
      },
      "8": {
        loc: {
          start: {
            line: 234,
            column: 4
          },
          end: {
            line: 234,
            column: 26
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 234,
            column: 4
          },
          end: {
            line: 234,
            column: 26
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 234
      },
      "9": {
        loc: {
          start: {
            line: 246,
            column: 14
          },
          end: {
            line: 252,
            column: 15
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 246,
            column: 14
          },
          end: {
            line: 252,
            column: 15
          }
        }, {
          start: {
            line: 250,
            column: 21
          },
          end: {
            line: 252,
            column: 15
          }
        }],
        line: 246
      },
      "10": {
        loc: {
          start: {
            line: 301,
            column: 13
          },
          end: {
            line: 326,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 302,
            column: 14
          },
          end: {
            line: 304,
            column: 21
          }
        }, {
          start: {
            line: 305,
            column: 16
          },
          end: {
            line: 326,
            column: 13
          }
        }],
        line: 301
      },
      "11": {
        loc: {
          start: {
            line: 305,
            column: 16
          },
          end: {
            line: 326,
            column: 13
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 306,
            column: 14
          },
          end: {
            line: 317,
            column: 21
          }
        }, {
          start: {
            line: 319,
            column: 14
          },
          end: {
            line: 325,
            column: 16
          }
        }],
        line: 305
      },
      "12": {
        loc: {
          start: {
            line: 345,
            column: 15
          },
          end: {
            line: 383,
            column: 15
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 345,
            column: 15
          },
          end: {
            line: 345,
            column: 32
          }
        }, {
          start: {
            line: 346,
            column: 16
          },
          end: {
            line: 382,
            column: 29
          }
        }],
        line: 345
      },
      "13": {
        loc: {
          start: {
            line: 354,
            column: 21
          },
          end: {
            line: 358,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 354,
            column: 21
          },
          end: {
            line: 354,
            column: 62
          }
        }, {
          start: {
            line: 355,
            column: 22
          },
          end: {
            line: 357,
            column: 29
          }
        }],
        line: 354
      },
      "14": {
        loc: {
          start: {
            line: 359,
            column: 21
          },
          end: {
            line: 363,
            column: 21
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 359,
            column: 21
          },
          end: {
            line: 359,
            column: 61
          }
        }, {
          start: {
            line: 360,
            column: 22
          },
          end: {
            line: 362,
            column: 29
          }
        }],
        line: 359
      },
      "15": {
        loc: {
          start: {
            line: 376,
            column: 19
          },
          end: {
            line: 381,
            column: 19
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 376,
            column: 19
          },
          end: {
            line: 376,
            column: 59
          }
        }, {
          start: {
            line: 377,
            column: 20
          },
          end: {
            line: 380,
            column: 22
          }
        }],
        line: 376
      },
      "16": {
        loc: {
          start: {
            line: 379,
            column: 37
          },
          end: {
            line: 379,
            column: 51
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 379,
            column: 37
          },
          end: {
            line: 379,
            column: 45
          }
        }, {
          start: {
            line: 379,
            column: 49
          },
          end: {
            line: 379,
            column: 51
          }
        }],
        line: 379
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0, 0, 0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "d3af83a6d53528dd01610fdc01af953364fcde04"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2obylvc8lc = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2obylvc8lc();
import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, SafeAreaView, ScrollView, TouchableOpacity, FlatList, Alert, Modal } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { router } from 'expo-router';
import { ArrowLeft, Trophy, Users, Calendar, Target, Medal, Plus, Clock, Star, Award } from 'lucide-react-native';
import { socialService } from "../../services/socialService";
import { useAuth } from "../../contexts/AuthContext";
import Card from "../../components/ui/Card";
import Button from "../../components/ui/Button";
import ErrorBoundary from "../../components/ui/ErrorBoundary";
import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
var colors = (cov_2obylvc8lc().s[0]++, {
  primary: '#23ba16',
  yellow: '#ffe600',
  white: '#ffffff',
  dark: '#171717',
  gray: '#6b7280',
  lightGray: '#f9fafb',
  blue: '#3b82f6',
  green: '#10b981',
  orange: '#f59e0b',
  red: '#ef4444'
});
var difficultyColors = (cov_2obylvc8lc().s[1]++, {
  easy: colors.green,
  medium: colors.orange,
  hard: colors.red,
  expert: '#8b5cf6'
});
cov_2obylvc8lc().s[2]++;
var ChallengeCard = function ChallengeCard(_ref) {
  var challenge = _ref.challenge,
    onJoin = _ref.onJoin,
    onViewDetails = _ref.onViewDetails,
    isParticipating = _ref.isParticipating;
  cov_2obylvc8lc().f[0]++;
  cov_2obylvc8lc().s[3]++;
  var getDaysRemaining = function getDaysRemaining() {
    cov_2obylvc8lc().f[1]++;
    var endDate = (cov_2obylvc8lc().s[4]++, new Date(challenge.endDate));
    var now = (cov_2obylvc8lc().s[5]++, new Date());
    var diffTime = (cov_2obylvc8lc().s[6]++, endDate.getTime() - now.getTime());
    var diffDays = (cov_2obylvc8lc().s[7]++, Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
    cov_2obylvc8lc().s[8]++;
    return Math.max(0, diffDays);
  };
  cov_2obylvc8lc().s[9]++;
  var getTypeIcon = function getTypeIcon() {
    cov_2obylvc8lc().f[2]++;
    cov_2obylvc8lc().s[10]++;
    switch (challenge.type) {
      case 'skill':
        cov_2obylvc8lc().b[0][0]++;
        cov_2obylvc8lc().s[11]++;
        return _jsx(Target, {
          size: 20,
          color: colors.primary
        });
      case 'endurance':
        cov_2obylvc8lc().b[0][1]++;
        cov_2obylvc8lc().s[12]++;
        return _jsx(Clock, {
          size: 20,
          color: colors.blue
        });
      case 'accuracy':
        cov_2obylvc8lc().b[0][2]++;
        cov_2obylvc8lc().s[13]++;
        return _jsx(Medal, {
          size: 20,
          color: colors.orange
        });
      case 'community':
        cov_2obylvc8lc().b[0][3]++;
        cov_2obylvc8lc().s[14]++;
        return _jsx(Users, {
          size: 20,
          color: colors.green
        });
      default:
        cov_2obylvc8lc().b[0][4]++;
        cov_2obylvc8lc().s[15]++;
        return _jsx(Trophy, {
          size: 20,
          color: colors.primary
        });
    }
  };
  cov_2obylvc8lc().s[16]++;
  return _jsxs(Card, {
    style: styles.challengeCard,
    children: [_jsxs(View, {
      style: styles.challengeHeader,
      children: [_jsxs(View, {
        style: styles.challengeTypeContainer,
        children: [getTypeIcon(), _jsx(Text, {
          style: styles.challengeType,
          children: challenge.type.toUpperCase()
        })]
      }), _jsx(View, {
        style: [styles.difficultyBadge, {
          backgroundColor: difficultyColors[challenge.difficulty]
        }],
        children: _jsx(Text, {
          style: styles.difficultyText,
          children: challenge.difficulty
        })
      })]
    }), _jsx(Text, {
      style: styles.challengeTitle,
      children: challenge.title
    }), _jsx(Text, {
      style: styles.challengeDescription,
      children: challenge.description
    }), _jsxs(View, {
      style: styles.challengeStats,
      children: [_jsxs(View, {
        style: styles.statItem,
        children: [_jsx(Users, {
          size: 16,
          color: colors.gray
        }), _jsxs(Text, {
          style: styles.statText,
          children: [challenge.participants, " participants"]
        })]
      }), _jsxs(View, {
        style: styles.statItem,
        children: [_jsx(Calendar, {
          size: 16,
          color: colors.gray
        }), _jsxs(Text, {
          style: styles.statText,
          children: [getDaysRemaining(), " days left"]
        })]
      })]
    }), _jsxs(View, {
      style: styles.rewardsContainer,
      children: [_jsx(Text, {
        style: styles.rewardsTitle,
        children: "Rewards:"
      }), _jsxs(View, {
        style: styles.rewardsList,
        children: [_jsxs(View, {
          style: styles.rewardItem,
          children: [_jsx(Star, {
            size: 14,
            color: colors.yellow
          }), _jsxs(Text, {
            style: styles.rewardText,
            children: [challenge.rewards.points, " points"]
          })]
        }), challenge.rewards.badges.map(function (badge, index) {
          cov_2obylvc8lc().f[3]++;
          cov_2obylvc8lc().s[17]++;
          return _jsxs(View, {
            style: styles.rewardItem,
            children: [_jsx(Award, {
              size: 14,
              color: colors.orange
            }), _jsx(Text, {
              style: styles.rewardText,
              children: badge
            })]
          }, index);
        })]
      })]
    }), _jsxs(View, {
      style: styles.challengeActions,
      children: [_jsx(Button, {
        title: "View Details",
        onPress: function onPress() {
          cov_2obylvc8lc().f[4]++;
          cov_2obylvc8lc().s[18]++;
          return onViewDetails(challenge);
        },
        variant: "outline",
        style: styles.detailsButton
      }), !isParticipating ? (cov_2obylvc8lc().b[1][0]++, _jsx(Button, {
        title: "Join Challenge",
        onPress: function onPress() {
          cov_2obylvc8lc().f[5]++;
          cov_2obylvc8lc().s[19]++;
          return onJoin(challenge.id);
        },
        style: styles.joinButton
      })) : (cov_2obylvc8lc().b[1][1]++, _jsx(View, {
        style: styles.participatingBadge,
        children: _jsx(Text, {
          style: styles.participatingText,
          children: "Participating"
        })
      }))]
    })]
  });
};
cov_2obylvc8lc().s[20]++;
var Leaderboard = function Leaderboard(_ref2) {
  var leaderboard = _ref2.leaderboard,
    currentUserId = _ref2.currentUserId;
  cov_2obylvc8lc().f[6]++;
  cov_2obylvc8lc().s[21]++;
  var renderLeaderboardItem = function renderLeaderboardItem(_ref3) {
    var item = _ref3.item,
      index = _ref3.index;
    cov_2obylvc8lc().f[7]++;
    var isCurrentUser = (cov_2obylvc8lc().s[22]++, item.userId === currentUserId);
    cov_2obylvc8lc().s[23]++;
    var getRankColor = function getRankColor(rank) {
      cov_2obylvc8lc().f[8]++;
      cov_2obylvc8lc().s[24]++;
      if (rank === 1) {
        cov_2obylvc8lc().b[2][0]++;
        cov_2obylvc8lc().s[25]++;
        return '#ffd700';
      } else {
        cov_2obylvc8lc().b[2][1]++;
      }
      cov_2obylvc8lc().s[26]++;
      if (rank === 2) {
        cov_2obylvc8lc().b[3][0]++;
        cov_2obylvc8lc().s[27]++;
        return '#c0c0c0';
      } else {
        cov_2obylvc8lc().b[3][1]++;
      }
      cov_2obylvc8lc().s[28]++;
      if (rank === 3) {
        cov_2obylvc8lc().b[4][0]++;
        cov_2obylvc8lc().s[29]++;
        return '#cd7f32';
      } else {
        cov_2obylvc8lc().b[4][1]++;
      }
      cov_2obylvc8lc().s[30]++;
      return colors.gray;
    };
    cov_2obylvc8lc().s[31]++;
    return _jsxs(View, {
      style: [styles.leaderboardItem, (cov_2obylvc8lc().b[5][0]++, isCurrentUser) && (cov_2obylvc8lc().b[5][1]++, styles.currentUserItem)],
      children: [_jsx(View, {
        style: styles.rankContainer,
        children: _jsxs(Text, {
          style: [styles.rankText, {
            color: getRankColor(item.rank)
          }],
          children: ["#", item.rank]
        })
      }), _jsxs(View, {
        style: styles.userInfo,
        children: [_jsx(Text, {
          style: [styles.username, (cov_2obylvc8lc().b[6][0]++, isCurrentUser) && (cov_2obylvc8lc().b[6][1]++, styles.currentUsername)],
          children: item.username
        }), _jsx(Text, {
          style: styles.completedAt,
          children: new Date(item.completedAt).toLocaleDateString()
        })]
      }), _jsx(Text, {
        style: [styles.score, (cov_2obylvc8lc().b[7][0]++, isCurrentUser) && (cov_2obylvc8lc().b[7][1]++, styles.currentScore)],
        children: item.score
      })]
    });
  };
  cov_2obylvc8lc().s[32]++;
  return _jsxs(View, {
    style: styles.leaderboardContainer,
    children: [_jsx(Text, {
      style: styles.leaderboardTitle,
      children: "Leaderboard"
    }), _jsx(FlatList, {
      data: leaderboard,
      renderItem: renderLeaderboardItem,
      keyExtractor: function keyExtractor(item) {
        cov_2obylvc8lc().f[9]++;
        cov_2obylvc8lc().s[33]++;
        return item.userId;
      },
      style: styles.leaderboardList,
      showsVerticalScrollIndicator: false
    })]
  });
};
export default function ChallengesScreen() {
  cov_2obylvc8lc().f[10]++;
  var _ref4 = (cov_2obylvc8lc().s[34]++, useAuth()),
    user = _ref4.user;
  var _ref5 = (cov_2obylvc8lc().s[35]++, useState([])),
    _ref6 = _slicedToArray(_ref5, 2),
    challenges = _ref6[0],
    setChallenges = _ref6[1];
  var _ref7 = (cov_2obylvc8lc().s[36]++, useState(true)),
    _ref8 = _slicedToArray(_ref7, 2),
    loading = _ref8[0],
    setLoading = _ref8[1];
  var _ref9 = (cov_2obylvc8lc().s[37]++, useState(null)),
    _ref0 = _slicedToArray(_ref9, 2),
    selectedChallenge = _ref0[0],
    setSelectedChallenge = _ref0[1];
  var _ref1 = (cov_2obylvc8lc().s[38]++, useState(false)),
    _ref10 = _slicedToArray(_ref1, 2),
    showDetails = _ref10[0],
    setShowDetails = _ref10[1];
  var _ref11 = (cov_2obylvc8lc().s[39]++, useState(new Set())),
    _ref12 = _slicedToArray(_ref11, 2),
    participatingChallenges = _ref12[0],
    setParticipatingChallenges = _ref12[1];
  cov_2obylvc8lc().s[40]++;
  useEffect(function () {
    cov_2obylvc8lc().f[11]++;
    cov_2obylvc8lc().s[41]++;
    loadChallenges();
  }, []);
  cov_2obylvc8lc().s[42]++;
  var loadChallenges = function () {
    var _ref13 = _asyncToGenerator(function* () {
      cov_2obylvc8lc().f[12]++;
      cov_2obylvc8lc().s[43]++;
      try {
        cov_2obylvc8lc().s[44]++;
        setLoading(true);
        var activeChallenges = (cov_2obylvc8lc().s[45]++, yield socialService.getActiveChallenges());
        cov_2obylvc8lc().s[46]++;
        setChallenges(activeChallenges);
      } catch (error) {
        cov_2obylvc8lc().s[47]++;
        console.error('Failed to load challenges:', error);
        cov_2obylvc8lc().s[48]++;
        Alert.alert('Error', 'Failed to load challenges');
      } finally {
        cov_2obylvc8lc().s[49]++;
        setLoading(false);
      }
    });
    return function loadChallenges() {
      return _ref13.apply(this, arguments);
    };
  }();
  cov_2obylvc8lc().s[50]++;
  var handleJoinChallenge = function () {
    var _ref14 = _asyncToGenerator(function* (challengeId) {
      cov_2obylvc8lc().f[13]++;
      cov_2obylvc8lc().s[51]++;
      if (!(user != null && user.id)) {
        cov_2obylvc8lc().b[8][0]++;
        cov_2obylvc8lc().s[52]++;
        return;
      } else {
        cov_2obylvc8lc().b[8][1]++;
      }
      cov_2obylvc8lc().s[53]++;
      Alert.alert('Join Challenge', 'Are you sure you want to join this challenge?', [{
        text: 'Cancel',
        style: 'cancel'
      }, {
        text: 'Join',
        onPress: function () {
          var _onPress = _asyncToGenerator(function* () {
            cov_2obylvc8lc().f[14]++;
            cov_2obylvc8lc().s[54]++;
            try {
              var success = (cov_2obylvc8lc().s[55]++, yield socialService.joinChallenge(challengeId, user.id));
              cov_2obylvc8lc().s[56]++;
              if (success) {
                cov_2obylvc8lc().b[9][0]++;
                cov_2obylvc8lc().s[57]++;
                setParticipatingChallenges(function (prev) {
                  cov_2obylvc8lc().f[15]++;
                  cov_2obylvc8lc().s[58]++;
                  return new Set([].concat(_toConsumableArray(prev), [challengeId]));
                });
                cov_2obylvc8lc().s[59]++;
                Alert.alert('Success', 'You have joined the challenge!');
                cov_2obylvc8lc().s[60]++;
                loadChallenges();
              } else {
                cov_2obylvc8lc().b[9][1]++;
                cov_2obylvc8lc().s[61]++;
                Alert.alert('Error', 'Failed to join challenge');
              }
            } catch (error) {
              cov_2obylvc8lc().s[62]++;
              console.error('Failed to join challenge:', error);
              cov_2obylvc8lc().s[63]++;
              Alert.alert('Error', 'Failed to join challenge');
            }
          });
          function onPress() {
            return _onPress.apply(this, arguments);
          }
          return onPress;
        }()
      }]);
    });
    return function handleJoinChallenge(_x) {
      return _ref14.apply(this, arguments);
    };
  }();
  cov_2obylvc8lc().s[64]++;
  var handleViewDetails = function handleViewDetails(challenge) {
    cov_2obylvc8lc().f[16]++;
    cov_2obylvc8lc().s[65]++;
    setSelectedChallenge(challenge);
    cov_2obylvc8lc().s[66]++;
    setShowDetails(true);
  };
  cov_2obylvc8lc().s[67]++;
  var handleCreateChallenge = function handleCreateChallenge() {
    cov_2obylvc8lc().f[17]++;
    cov_2obylvc8lc().s[68]++;
    router.push('/social/create-challenge');
  };
  cov_2obylvc8lc().s[69]++;
  var renderChallenge = function renderChallenge(_ref15) {
    var item = _ref15.item;
    cov_2obylvc8lc().f[18]++;
    cov_2obylvc8lc().s[70]++;
    return _jsx(ChallengeCard, {
      challenge: item,
      onJoin: handleJoinChallenge,
      onViewDetails: handleViewDetails,
      isParticipating: participatingChallenges.has(item.id)
    });
  };
  cov_2obylvc8lc().s[71]++;
  return _jsx(ErrorBoundary, {
    context: "ChallengesScreen",
    children: _jsx(SafeAreaView, {
      style: styles.container,
      children: _jsxs(LinearGradient, {
        colors: ['#1e3a8a', '#3b82f6', '#60a5fa'],
        style: styles.gradient,
        children: [_jsxs(View, {
          style: styles.header,
          children: [_jsx(TouchableOpacity, {
            onPress: function onPress() {
              cov_2obylvc8lc().f[19]++;
              cov_2obylvc8lc().s[72]++;
              return router.back();
            },
            style: styles.backButton,
            children: _jsx(ArrowLeft, {
              size: 24,
              color: "white"
            })
          }), _jsx(Text, {
            style: styles.title,
            children: "Community Challenges"
          }), _jsx(TouchableOpacity, {
            onPress: handleCreateChallenge,
            style: styles.createButton,
            children: _jsx(Plus, {
              size: 24,
              color: "white"
            })
          })]
        }), _jsx(View, {
          style: styles.content,
          children: loading ? (cov_2obylvc8lc().b[10][0]++, _jsx(View, {
            style: styles.loadingContainer,
            children: _jsx(Text, {
              style: styles.loadingText,
              children: "Loading challenges..."
            })
          })) : (cov_2obylvc8lc().b[10][1]++, challenges.length === 0 ? (cov_2obylvc8lc().b[11][0]++, _jsxs(View, {
            style: styles.emptyContainer,
            children: [_jsx(Trophy, {
              size: 64,
              color: colors.gray
            }), _jsx(Text, {
              style: styles.emptyTitle,
              children: "No Active Challenges"
            }), _jsx(Text, {
              style: styles.emptyText,
              children: "Be the first to create a challenge for the community!"
            }), _jsx(Button, {
              title: "Create Challenge",
              onPress: handleCreateChallenge,
              style: styles.createChallengeButton
            })]
          })) : (cov_2obylvc8lc().b[11][1]++, _jsx(FlatList, {
            data: challenges,
            renderItem: renderChallenge,
            keyExtractor: function keyExtractor(item) {
              cov_2obylvc8lc().f[20]++;
              cov_2obylvc8lc().s[73]++;
              return item.id;
            },
            showsVerticalScrollIndicator: false,
            contentContainerStyle: styles.challengesList
          })))
        }), _jsx(Modal, {
          visible: showDetails,
          animationType: "slide",
          presentationStyle: "pageSheet",
          onRequestClose: function onRequestClose() {
            cov_2obylvc8lc().f[21]++;
            cov_2obylvc8lc().s[74]++;
            return setShowDetails(false);
          },
          children: _jsxs(SafeAreaView, {
            style: styles.modalContainer,
            children: [_jsxs(View, {
              style: styles.modalHeader,
              children: [_jsx(TouchableOpacity, {
                onPress: function onPress() {
                  cov_2obylvc8lc().f[22]++;
                  cov_2obylvc8lc().s[75]++;
                  return setShowDetails(false);
                },
                children: _jsx(Text, {
                  style: styles.modalCloseText,
                  children: "Close"
                })
              }), _jsx(Text, {
                style: styles.modalTitle,
                children: "Challenge Details"
              }), _jsx(View, {
                style: styles.placeholder
              })]
            }), (cov_2obylvc8lc().b[12][0]++, selectedChallenge) && (cov_2obylvc8lc().b[12][1]++, _jsxs(ScrollView, {
              style: styles.modalContent,
              children: [_jsx(Text, {
                style: styles.modalChallengeTitle,
                children: selectedChallenge.title
              }), _jsx(Text, {
                style: styles.modalChallengeDescription,
                children: selectedChallenge.description
              }), _jsxs(View, {
                style: styles.modalSection,
                children: [_jsx(Text, {
                  style: styles.modalSectionTitle,
                  children: "Requirements"
                }), (cov_2obylvc8lc().b[13][0]++, selectedChallenge.requirements.skillLevel) && (cov_2obylvc8lc().b[13][1]++, _jsxs(Text, {
                  style: styles.modalRequirement,
                  children: ["Skill Level: ", selectedChallenge.requirements.skillLevel.join(', ')]
                })), (cov_2obylvc8lc().b[14][0]++, selectedChallenge.requirements.equipment) && (cov_2obylvc8lc().b[14][1]++, _jsxs(Text, {
                  style: styles.modalRequirement,
                  children: ["Equipment: ", selectedChallenge.requirements.equipment.join(', ')]
                }))]
              }), _jsxs(View, {
                style: styles.modalSection,
                children: [_jsx(Text, {
                  style: styles.modalSectionTitle,
                  children: "Timeline"
                }), _jsxs(Text, {
                  style: styles.modalDate,
                  children: ["Start: ", new Date(selectedChallenge.startDate).toLocaleDateString()]
                }), _jsxs(Text, {
                  style: styles.modalDate,
                  children: ["End: ", new Date(selectedChallenge.endDate).toLocaleDateString()]
                })]
              }), (cov_2obylvc8lc().b[15][0]++, selectedChallenge.leaderboard.length > 0) && (cov_2obylvc8lc().b[15][1]++, _jsx(Leaderboard, {
                leaderboard: selectedChallenge.leaderboard,
                currentUserId: (cov_2obylvc8lc().b[16][0]++, user == null ? void 0 : user.id) || (cov_2obylvc8lc().b[16][1]++, '')
              }))]
            }))]
          })
        })]
      })
    })
  });
}
var styles = (cov_2obylvc8lc().s[76]++, StyleSheet.create({
  container: {
    flex: 1
  },
  gradient: {
    flex: 1
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 10
  },
  backButton: {
    padding: 8
  },
  title: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: colors.white
  },
  createButton: {
    padding: 8
  },
  content: {
    flex: 1,
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center'
  },
  loadingText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: colors.dark,
    marginTop: 20,
    marginBottom: 8
  },
  emptyText: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: 30
  },
  createChallengeButton: {
    paddingHorizontal: 30
  },
  challengesList: {
    paddingHorizontal: 20,
    paddingBottom: 20
  },
  challengeCard: {
    marginBottom: 16,
    padding: 20
  },
  challengeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12
  },
  challengeTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  challengeType: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: colors.primary
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12
  },
  difficultyText: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: colors.white,
    textTransform: 'uppercase'
  },
  challengeTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 8
  },
  challengeDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 20,
    marginBottom: 16
  },
  challengeStats: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  statText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  rewardsContainer: {
    marginBottom: 20
  },
  rewardsTitle: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.dark,
    marginBottom: 8
  },
  rewardsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12
  },
  rewardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4
  },
  rewardText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray
  },
  challengeActions: {
    flexDirection: 'row',
    gap: 12
  },
  detailsButton: {
    flex: 1
  },
  joinButton: {
    flex: 1
  },
  participatingBadge: {
    flex: 1,
    backgroundColor: colors.green,
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    justifyContent: 'center'
  },
  participatingText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: colors.white
  },
  modalContainer: {
    flex: 1,
    backgroundColor: colors.white
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGray
  },
  modalCloseText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: colors.primary
  },
  modalTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark
  },
  placeholder: {
    width: 50
  },
  modalContent: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20
  },
  modalChallengeTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: colors.dark,
    marginBottom: 12
  },
  modalChallengeDescription: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    lineHeight: 22,
    marginBottom: 24
  },
  modalSection: {
    marginBottom: 24
  },
  modalSectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 12
  },
  modalRequirement: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 4
  },
  modalDate: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginBottom: 4
  },
  leaderboardContainer: {
    marginTop: 24
  },
  leaderboardTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: colors.dark,
    marginBottom: 16
  },
  leaderboardList: {
    maxHeight: 300
  },
  leaderboardItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: colors.lightGray,
    borderRadius: 8,
    marginBottom: 8
  },
  currentUserItem: {
    backgroundColor: colors.primary
  },
  rankContainer: {
    width: 40,
    alignItems: 'center'
  },
  rankText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold'
  },
  userInfo: {
    flex: 1,
    marginLeft: 12
  },
  username: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: colors.dark
  },
  currentUsername: {
    color: colors.white
  },
  completedAt: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: colors.gray,
    marginTop: 2
  },
  score: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: colors.dark
  },
  currentScore: {
    color: colors.white
  }
}));
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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