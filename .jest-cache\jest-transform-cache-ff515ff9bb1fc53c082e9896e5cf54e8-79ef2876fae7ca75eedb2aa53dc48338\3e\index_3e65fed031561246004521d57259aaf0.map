{"version": 3, "names": ["_interopRequireDefault", "require", "default", "exports", "__esModule", "useEvent", "_addEventListener", "_useLayoutEffect", "_useStable", "eventType", "options", "targetListeners", "Map", "addListener", "target", "callback", "removeTargetListener", "get", "delete", "removeEventListener", "addEventListener", "set", "for<PERSON>ach", "removeListener", "clear", "module"], "sources": ["index.js"], "sourcesContent": ["\"use strict\";\n\nvar _interopRequireDefault = require(\"@babel/runtime/helpers/interopRequireDefault\").default;\nexports.__esModule = true;\nexports.default = useEvent;\nvar _addEventListener = require(\"../addEventListener\");\nvar _useLayoutEffect = _interopRequireDefault(require(\"../useLayoutEffect\"));\nvar _useStable = _interopRequireDefault(require(\"../useStable\"));\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\n/**\n * This can be used with any event type include custom events.\n *\n * const click = useEvent('click', options);\n * useEffect(() => {\n *   click.setListener(target, onClick);\n *   return () => click.clear();\n * }).\n */\nfunction useEvent(eventType, options) {\n  var targetListeners = (0, _useStable.default)(() => new Map());\n  var addListener = (0, _useStable.default)(() => {\n    return (target, callback) => {\n      var removeTargetListener = targetListeners.get(target);\n      if (removeTargetListener != null) {\n        removeTargetListener();\n      }\n      if (callback == null) {\n        targetListeners.delete(target);\n        callback = () => {};\n      }\n      var removeEventListener = (0, _addEventListener.addEventListener)(target, eventType, callback, options);\n      targetListeners.set(target, removeEventListener);\n      return removeEventListener;\n    };\n  });\n  (0, _useLayoutEffect.default)(() => {\n    return () => {\n      targetListeners.forEach(removeListener => {\n        removeListener();\n      });\n      targetListeners.clear();\n    };\n  }, [targetListeners]);\n  return addListener;\n}\nmodule.exports = exports.default;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,sBAAsB,GAAGC,OAAO,CAAC,8CAA8C,CAAC,CAACC,OAAO;AAC5FC,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACD,OAAO,GAAGG,QAAQ;AAC1B,IAAIC,iBAAiB,GAAGL,OAAO,sBAAsB,CAAC;AACtD,IAAIM,gBAAgB,GAAGP,sBAAsB,CAACC,OAAO,qBAAqB,CAAC,CAAC;AAC5E,IAAIO,UAAU,GAAGR,sBAAsB,CAACC,OAAO,eAAe,CAAC,CAAC;AAmBhE,SAASI,QAAQA,CAACI,SAAS,EAAEC,OAAO,EAAE;EACpC,IAAIC,eAAe,GAAG,CAAC,CAAC,EAAEH,UAAU,CAACN,OAAO,EAAE;IAAA,OAAM,IAAIU,GAAG,CAAC,CAAC;EAAA,EAAC;EAC9D,IAAIC,WAAW,GAAG,CAAC,CAAC,EAAEL,UAAU,CAACN,OAAO,EAAE,YAAM;IAC9C,OAAO,UAACY,MAAM,EAAEC,QAAQ,EAAK;MAC3B,IAAIC,oBAAoB,GAAGL,eAAe,CAACM,GAAG,CAACH,MAAM,CAAC;MACtD,IAAIE,oBAAoB,IAAI,IAAI,EAAE;QAChCA,oBAAoB,CAAC,CAAC;MACxB;MACA,IAAID,QAAQ,IAAI,IAAI,EAAE;QACpBJ,eAAe,CAACO,MAAM,CAACJ,MAAM,CAAC;QAC9BC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS,CAAC,CAAC;MACrB;MACA,IAAII,mBAAmB,GAAG,CAAC,CAAC,EAAEb,iBAAiB,CAACc,gBAAgB,EAAEN,MAAM,EAAEL,SAAS,EAAEM,QAAQ,EAAEL,OAAO,CAAC;MACvGC,eAAe,CAACU,GAAG,CAACP,MAAM,EAAEK,mBAAmB,CAAC;MAChD,OAAOA,mBAAmB;IAC5B,CAAC;EACH,CAAC,CAAC;EACF,CAAC,CAAC,EAAEZ,gBAAgB,CAACL,OAAO,EAAE,YAAM;IAClC,OAAO,YAAM;MACXS,eAAe,CAACW,OAAO,CAAC,UAAAC,cAAc,EAAI;QACxCA,cAAc,CAAC,CAAC;MAClB,CAAC,CAAC;MACFZ,eAAe,CAACa,KAAK,CAAC,CAAC;IACzB,CAAC;EACH,CAAC,EAAE,CAACb,eAAe,CAAC,CAAC;EACrB,OAAOE,WAAW;AACpB;AACAY,MAAM,CAACtB,OAAO,GAAGA,OAAO,CAACD,OAAO", "ignoreList": []}