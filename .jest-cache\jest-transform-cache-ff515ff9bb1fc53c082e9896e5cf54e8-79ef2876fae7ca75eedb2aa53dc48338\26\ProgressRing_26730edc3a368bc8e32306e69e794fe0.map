{"version": 3, "names": ["React", "View", "Text", "StyleSheet", "Svg", "Circle", "jsx", "_jsx", "jsxs", "_jsxs", "colors", "cov_13a93lw55j", "s", "primary", "lightGray", "dark", "ProgressRing", "_ref", "progress", "_ref$size", "size", "b", "_ref$strokeWidth", "strokeWidth", "label", "value", "_ref$color", "color", "f", "radius", "circumference", "Math", "PI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "style", "styles", "container", "width", "height", "children", "svg", "cx", "cy", "r", "stroke", "fill", "strokeLinecap", "transform", "content", "create", "position", "alignItems", "justifyContent", "fontSize", "fontFamily", "opacity", "textAlign"], "sources": ["ProgressRing.tsx"], "sourcesContent": ["import React from 'react';\nimport { View, Text, StyleSheet } from 'react-native';\nimport Svg, { Circle } from 'react-native-svg';\n\nconst colors = {\n  primary: '#23ba16',\n  lightGray: '#e5e7eb',\n  dark: '#171717',\n};\n\ninterface ProgressRingProps {\n  progress: number; // 0-100\n  size?: number;\n  strokeWidth?: number;\n  label?: string;\n  value?: string;\n  color?: string;\n}\n\nexport default function ProgressRing({\n  progress,\n  size = 80,\n  strokeWidth = 8,\n  label,\n  value,\n  color = colors.primary,\n}: ProgressRingProps) {\n  const radius = (size - strokeWidth) / 2;\n  const circumference = radius * 2 * Math.PI;\n  const strokeDasharray = circumference;\n  const strokeDashoffset = circumference - (progress / 100) * circumference;\n\n  return (\n    <View style={[styles.container, { width: size, height: size }]}>\n      <Svg width={size} height={size} style={styles.svg}>\n        {/* Background circle */}\n        <Circle\n          cx={size / 2}\n          cy={size / 2}\n          r={radius}\n          stroke={colors.lightGray}\n          strokeWidth={strokeWidth}\n          fill=\"transparent\"\n        />\n        {/* Progress circle */}\n        <Circle\n          cx={size / 2}\n          cy={size / 2}\n          r={radius}\n          stroke={color}\n          strokeWidth={strokeWidth}\n          fill=\"transparent\"\n          strokeDasharray={strokeDasharray}\n          strokeDashoffset={strokeDashoffset}\n          strokeLinecap=\"round\"\n          transform={`rotate(-90 ${size / 2} ${size / 2})`}\n        />\n      </Svg>\n      <View style={styles.content}>\n        {value && <Text style={styles.value}>{value}</Text>}\n        {label && <Text style={styles.label}>{label}</Text>}\n      </View>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    position: 'relative',\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  svg: {\n    position: 'absolute',\n  },\n  content: {\n    alignItems: 'center',\n    justifyContent: 'center',\n  },\n  value: {\n    fontSize: 16,\n    fontFamily: 'Inter-Bold',\n    color: colors.dark,\n  },\n  label: {\n    fontSize: 10,\n    fontFamily: 'Inter-Medium',\n    color: colors.dark,\n    opacity: 0.7,\n    textAlign: 'center',\n  },\n});"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,cAAc;AACrD,OAAOC,GAAG,IAAIC,MAAM,QAAQ,kBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAE/C,IAAMC,MAAM,IAAAC,cAAA,GAAAC,CAAA,OAAG;EACbC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,IAAI,EAAE;AACR,CAAC;AAWD,eAAe,SAASC,YAAYA,CAAAC,IAAA,EAOd;EAAA,IANpBC,QAAQ,GAAAD,IAAA,CAARC,QAAQ;IAAAC,SAAA,GAAAF,IAAA,CACRG,IAAI;IAAJA,IAAI,GAAAD,SAAA,eAAAR,cAAA,GAAAU,CAAA,UAAG,EAAE,IAAAF,SAAA;IAAAG,gBAAA,GAAAL,IAAA,CACTM,WAAW;IAAXA,WAAW,GAAAD,gBAAA,eAAAX,cAAA,GAAAU,CAAA,UAAG,CAAC,IAAAC,gBAAA;IACfE,KAAK,GAAAP,IAAA,CAALO,KAAK;IACLC,KAAK,GAAAR,IAAA,CAALQ,KAAK;IAAAC,UAAA,GAAAT,IAAA,CACLU,KAAK;IAALA,KAAK,GAAAD,UAAA,eAAAf,cAAA,GAAAU,CAAA,UAAGX,MAAM,CAACG,OAAO,IAAAa,UAAA;EAAAf,cAAA,GAAAiB,CAAA;EAEtB,IAAMC,MAAM,IAAAlB,cAAA,GAAAC,CAAA,OAAG,CAACQ,IAAI,GAAGG,WAAW,IAAI,CAAC;EACvC,IAAMO,aAAa,IAAAnB,cAAA,GAAAC,CAAA,OAAGiB,MAAM,GAAG,CAAC,GAAGE,IAAI,CAACC,EAAE;EAC1C,IAAMC,eAAe,IAAAtB,cAAA,GAAAC,CAAA,OAAGkB,aAAa;EACrC,IAAMI,gBAAgB,IAAAvB,cAAA,GAAAC,CAAA,OAAGkB,aAAa,GAAIZ,QAAQ,GAAG,GAAG,GAAIY,aAAa;EAACnB,cAAA,GAAAC,CAAA;EAE1E,OACEH,KAAA,CAACR,IAAI;IAACkC,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAE;MAAEC,KAAK,EAAElB,IAAI;MAAEmB,MAAM,EAAEnB;IAAK,CAAC,CAAE;IAAAoB,QAAA,GAC7D/B,KAAA,CAACL,GAAG;MAACkC,KAAK,EAAElB,IAAK;MAACmB,MAAM,EAAEnB,IAAK;MAACe,KAAK,EAAEC,MAAM,CAACK,GAAI;MAAAD,QAAA,GAEhDjC,IAAA,CAACF,MAAM;QACLqC,EAAE,EAAEtB,IAAI,GAAG,CAAE;QACbuB,EAAE,EAAEvB,IAAI,GAAG,CAAE;QACbwB,CAAC,EAAEf,MAAO;QACVgB,MAAM,EAAEnC,MAAM,CAACI,SAAU;QACzBS,WAAW,EAAEA,WAAY;QACzBuB,IAAI,EAAC;MAAa,CACnB,CAAC,EAEFvC,IAAA,CAACF,MAAM;QACLqC,EAAE,EAAEtB,IAAI,GAAG,CAAE;QACbuB,EAAE,EAAEvB,IAAI,GAAG,CAAE;QACbwB,CAAC,EAAEf,MAAO;QACVgB,MAAM,EAAElB,KAAM;QACdJ,WAAW,EAAEA,WAAY;QACzBuB,IAAI,EAAC,aAAa;QAClBb,eAAe,EAAEA,eAAgB;QACjCC,gBAAgB,EAAEA,gBAAiB;QACnCa,aAAa,EAAC,OAAO;QACrBC,SAAS,EAAE,cAAc5B,IAAI,GAAG,CAAC,IAAIA,IAAI,GAAG,CAAC;MAAI,CAClD,CAAC;IAAA,CACC,CAAC,EACNX,KAAA,CAACR,IAAI;MAACkC,KAAK,EAAEC,MAAM,CAACa,OAAQ;MAAAT,QAAA,GACzB,CAAA7B,cAAA,GAAAU,CAAA,UAAAI,KAAK,MAAAd,cAAA,GAAAU,CAAA,UAAId,IAAA,CAACL,IAAI;QAACiC,KAAK,EAAEC,MAAM,CAACX,KAAM;QAAAe,QAAA,EAAEf;MAAK,CAAO,CAAC,GAClD,CAAAd,cAAA,GAAAU,CAAA,UAAAG,KAAK,MAAAb,cAAA,GAAAU,CAAA,UAAId,IAAA,CAACL,IAAI;QAACiC,KAAK,EAAEC,MAAM,CAACZ,KAAM;QAAAgB,QAAA,EAAEhB;MAAK,CAAO,CAAC;IAAA,CAC/C,CAAC;EAAA,CACH,CAAC;AAEX;AAEA,IAAMY,MAAM,IAAAzB,cAAA,GAAAC,CAAA,OAAGT,UAAU,CAAC+C,MAAM,CAAC;EAC/Bb,SAAS,EAAE;IACTc,QAAQ,EAAE,UAAU;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDZ,GAAG,EAAE;IACHU,QAAQ,EAAE;EACZ,CAAC;EACDF,OAAO,EAAE;IACPG,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACD5B,KAAK,EAAE;IACL6B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,YAAY;IACxB5B,KAAK,EAAEjB,MAAM,CAACK;EAChB,CAAC;EACDS,KAAK,EAAE;IACL8B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,cAAc;IAC1B5B,KAAK,EAAEjB,MAAM,CAACK,IAAI;IAClByC,OAAO,EAAE,GAAG;IACZC,SAAS,EAAE;EACb;AACF,CAAC,CAAC", "ignoreList": []}