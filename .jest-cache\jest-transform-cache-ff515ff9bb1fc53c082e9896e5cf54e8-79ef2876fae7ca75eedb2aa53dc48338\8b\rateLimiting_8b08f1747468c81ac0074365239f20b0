92238488a67b3f6658451552390234fc
import _slicedToArray from "@babel/runtime/helpers/slicedToArray";
import _asyncToGenerator from "@babel/runtime/helpers/asyncToGenerator";
import _classCallCheck from "@babel/runtime/helpers/classCallCheck";
import _createClass from "@babel/runtime/helpers/createClass";
function cov_2kt21rq1zn() {
  var path = "C:\\_SaaS\\AceMind\\project\\services\\rateLimiting.ts";
  var hash = "90e4e60c332a55cb4d56c8cb81376c604ea4f824";
  var global = new Function("return this")();
  var gcv = "__coverage__";
  var coverageData = {
    path: "C:\\_SaaS\\AceMind\\project\\services\\rateLimiting.ts",
    statementMap: {
      "0": {
        start: {
          line: 54,
          column: 49
        },
        end: {
          line: 54,
          column: 58
        }
      },
      "1": {
        start: {
          line: 55,
          column: 32
        },
        end: {
          line: 55,
          column: 49
        }
      },
      "2": {
        start: {
          line: 57,
          column: 52
        },
        end: {
          line: 108,
          column: 3
        }
      },
      "3": {
        start: {
          line: 62,
          column: 65
        },
        end: {
          line: 62,
          column: 115
        }
      },
      "4": {
        start: {
          line: 114,
          column: 4
        },
        end: {
          line: 127,
          column: 5
        }
      },
      "5": {
        start: {
          line: 115,
          column: 6
        },
        end: {
          line: 115,
          column: 34
        }
      },
      "6": {
        start: {
          line: 118,
          column: 6
        },
        end: {
          line: 120,
          column: 24
        }
      },
      "7": {
        start: {
          line: 119,
          column: 8
        },
        end: {
          line: 119,
          column: 37
        }
      },
      "8": {
        start: {
          line: 122,
          column: 6
        },
        end: {
          line: 124,
          column: 7
        }
      },
      "9": {
        start: {
          line: 123,
          column: 8
        },
        end: {
          line: 123,
          column: 60
        }
      },
      "10": {
        start: {
          line: 126,
          column: 6
        },
        end: {
          line: 126,
          column: 74
        }
      },
      "11": {
        start: {
          line: 137,
          column: 4
        },
        end: {
          line: 188,
          column: 5
        }
      },
      "12": {
        start: {
          line: 138,
          column: 21
        },
        end: {
          line: 138,
          column: 53
        }
      },
      "13": {
        start: {
          line: 139,
          column: 18
        },
        end: {
          line: 139,
          column: 56
        }
      },
      "14": {
        start: {
          line: 140,
          column: 18
        },
        end: {
          line: 140,
          column: 28
        }
      },
      "15": {
        start: {
          line: 142,
          column: 18
        },
        end: {
          line: 142,
          column: 39
        }
      },
      "16": {
        start: {
          line: 145,
          column: 6
        },
        end: {
          line: 151,
          column: 7
        }
      },
      "17": {
        start: {
          line: 146,
          column: 8
        },
        end: {
          line: 150,
          column: 10
        }
      },
      "18": {
        start: {
          line: 154,
          column: 6
        },
        end: {
          line: 168,
          column: 7
        }
      },
      "19": {
        start: {
          line: 155,
          column: 27
        },
        end: {
          line: 155,
          column: 68
        }
      },
      "20": {
        start: {
          line: 158,
          column: 8
        },
        end: {
          line: 160,
          column: 9
        }
      },
      "21": {
        start: {
          line: 159,
          column: 10
        },
        end: {
          line: 159,
          column: 61
        }
      },
      "22": {
        start: {
          line: 162,
          column: 8
        },
        end: {
          line: 167,
          column: 10
        }
      },
      "23": {
        start: {
          line: 171,
          column: 6
        },
        end: {
          line: 171,
          column: 20
        }
      },
      "24": {
        start: {
          line: 172,
          column: 6
        },
        end: {
          line: 172,
          column: 35
        }
      },
      "25": {
        start: {
          line: 173,
          column: 6
        },
        end: {
          line: 173,
          column: 31
        }
      },
      "26": {
        start: {
          line: 175,
          column: 6
        },
        end: {
          line: 179,
          column: 8
        }
      },
      "27": {
        start: {
          line: 181,
          column: 6
        },
        end: {
          line: 181,
          column: 55
        }
      },
      "28": {
        start: {
          line: 183,
          column: 6
        },
        end: {
          line: 187,
          column: 8
        }
      },
      "29": {
        start: {
          line: 198,
          column: 19
        },
        end: {
          line: 198,
          column: 51
        }
      },
      "30": {
        start: {
          line: 200,
          column: 4
        },
        end: {
          line: 209,
          column: 5
        }
      },
      "31": {
        start: {
          line: 201,
          column: 18
        },
        end: {
          line: 201,
          column: 56
        }
      },
      "32": {
        start: {
          line: 202,
          column: 20
        },
        end: {
          line: 202,
          column: 41
        }
      },
      "33": {
        start: {
          line: 204,
          column: 6
        },
        end: {
          line: 208,
          column: 7
        }
      },
      "34": {
        start: {
          line: 205,
          column: 8
        },
        end: {
          line: 205,
          column: 22
        }
      },
      "35": {
        start: {
          line: 206,
          column: 8
        },
        end: {
          line: 206,
          column: 37
        }
      },
      "36": {
        start: {
          line: 207,
          column: 8
        },
        end: {
          line: 207,
          column: 33
        }
      },
      "37": {
        start: {
          line: 219,
          column: 19
        },
        end: {
          line: 219,
          column: 51
        }
      },
      "38": {
        start: {
          line: 221,
          column: 4
        },
        end: {
          line: 230,
          column: 5
        }
      },
      "39": {
        start: {
          line: 222,
          column: 18
        },
        end: {
          line: 222,
          column: 56
        }
      },
      "40": {
        start: {
          line: 223,
          column: 20
        },
        end: {
          line: 223,
          column: 41
        }
      },
      "41": {
        start: {
          line: 225,
          column: 6
        },
        end: {
          line: 229,
          column: 7
        }
      },
      "42": {
        start: {
          line: 226,
          column: 8
        },
        end: {
          line: 226,
          column: 22
        }
      },
      "43": {
        start: {
          line: 227,
          column: 8
        },
        end: {
          line: 227,
          column: 37
        }
      },
      "44": {
        start: {
          line: 228,
          column: 8
        },
        end: {
          line: 228,
          column: 33
        }
      },
      "45": {
        start: {
          line: 240,
          column: 19
        },
        end: {
          line: 240,
          column: 51
        }
      },
      "46": {
        start: {
          line: 241,
          column: 16
        },
        end: {
          line: 241,
          column: 54
        }
      },
      "47": {
        start: {
          line: 242,
          column: 18
        },
        end: {
          line: 242,
          column: 39
        }
      },
      "48": {
        start: {
          line: 243,
          column: 16
        },
        end: {
          line: 243,
          column: 26
        }
      },
      "49": {
        start: {
          line: 245,
          column: 4
        },
        end: {
          line: 247,
          column: 5
        }
      },
      "50": {
        start: {
          line: 246,
          column: 6
        },
        end: {
          line: 246,
          column: 32
        }
      },
      "51": {
        start: {
          line: 249,
          column: 4
        },
        end: {
          line: 249,
          column: 57
        }
      },
      "52": {
        start: {
          line: 259,
          column: 16
        },
        end: {
          line: 259,
          column: 54
        }
      },
      "53": {
        start: {
          line: 260,
          column: 4
        },
        end: {
          line: 260,
          column: 29
        }
      },
      "54": {
        start: {
          line: 261,
          column: 4
        },
        end: {
          line: 261,
          column: 29
        }
      },
      "55": {
        start: {
          line: 268,
          column: 52
        },
        end: {
          line: 268,
          column: 54
        }
      },
      "56": {
        start: {
          line: 270,
          column: 4
        },
        end: {
          line: 282,
          column: 5
        }
      },
      "57": {
        start: {
          line: 271,
          column: 21
        },
        end: {
          line: 271,
          column: 64
        }
      },
      "58": {
        start: {
          line: 273,
          column: 6
        },
        end: {
          line: 280,
          column: 7
        }
      },
      "59": {
        start: {
          line: 274,
          column: 20
        },
        end: {
          line: 274,
          column: 58
        }
      },
      "60": {
        start: {
          line: 275,
          column: 22
        },
        end: {
          line: 275,
          column: 43
        }
      },
      "61": {
        start: {
          line: 276,
          column: 8
        },
        end: {
          line: 279,
          column: 9
        }
      },
      "62": {
        start: {
          line: 277,
          column: 10
        },
        end: {
          line: 277,
          column: 24
        }
      },
      "63": {
        start: {
          line: 278,
          column: 10
        },
        end: {
          line: 278,
          column: 39
        }
      },
      "64": {
        start: {
          line: 281,
          column: 6
        },
        end: {
          line: 281,
          column: 32
        }
      },
      "65": {
        start: {
          line: 284,
          column: 4
        },
        end: {
          line: 284,
          column: 29
        }
      },
      "66": {
        start: {
          line: 285,
          column: 4
        },
        end: {
          line: 285,
          column: 18
        }
      },
      "67": {
        start: {
          line: 295,
          column: 4
        },
        end: {
          line: 300,
          column: 5
        }
      },
      "68": {
        start: {
          line: 296,
          column: 6
        },
        end: {
          line: 299,
          column: 8
        }
      },
      "69": {
        start: {
          line: 307,
          column: 4
        },
        end: {
          line: 307,
          column: 25
        }
      },
      "70": {
        start: {
          line: 308,
          column: 4
        },
        end: {
          line: 308,
          column: 51
        }
      },
      "71": {
        start: {
          line: 315,
          column: 4
        },
        end: {
          line: 317,
          column: 5
        }
      },
      "72": {
        start: {
          line: 316,
          column: 6
        },
        end: {
          line: 316,
          column: 67
        }
      },
      "73": {
        start: {
          line: 318,
          column: 4
        },
        end: {
          line: 318,
          column: 39
        }
      },
      "74": {
        start: {
          line: 325,
          column: 19
        },
        end: {
          line: 325,
          column: 51
        }
      },
      "75": {
        start: {
          line: 327,
          column: 4
        },
        end: {
          line: 329,
          column: 5
        }
      },
      "76": {
        start: {
          line: 328,
          column: 6
        },
        end: {
          line: 328,
          column: 45
        }
      },
      "77": {
        start: {
          line: 331,
          column: 4
        },
        end: {
          line: 331,
          column: 39
        }
      },
      "78": {
        start: {
          line: 338,
          column: 4
        },
        end: {
          line: 350,
          column: 5
        }
      },
      "79": {
        start: {
          line: 339,
          column: 21
        },
        end: {
          line: 339,
          column: 64
        }
      },
      "80": {
        start: {
          line: 340,
          column: 6
        },
        end: {
          line: 346,
          column: 7
        }
      },
      "81": {
        start: {
          line: 341,
          column: 21
        },
        end: {
          line: 341,
          column: 39
        }
      },
      "82": {
        start: {
          line: 342,
          column: 8
        },
        end: {
          line: 342,
          column: 53
        }
      },
      "83": {
        start: {
          line: 345,
          column: 8
        },
        end: {
          line: 345,
          column: 37
        }
      },
      "84": {
        start: {
          line: 348,
          column: 6
        },
        end: {
          line: 348,
          column: 62
        }
      },
      "85": {
        start: {
          line: 349,
          column: 6
        },
        end: {
          line: 349,
          column: 27
        }
      },
      "86": {
        start: {
          line: 357,
          column: 4
        },
        end: {
          line: 362,
          column: 5
        }
      },
      "87": {
        start: {
          line: 358,
          column: 19
        },
        end: {
          line: 358,
          column: 51
        }
      },
      "88": {
        start: {
          line: 359,
          column: 6
        },
        end: {
          line: 359,
          column: 72
        }
      },
      "89": {
        start: {
          line: 361,
          column: 6
        },
        end: {
          line: 361,
          column: 65
        }
      },
      "90": {
        start: {
          line: 369,
          column: 16
        },
        end: {
          line: 369,
          column: 26
        }
      },
      "91": {
        start: {
          line: 370,
          column: 35
        },
        end: {
          line: 370,
          column: 37
        }
      },
      "92": {
        start: {
          line: 372,
          column: 4
        },
        end: {
          line: 376,
          column: 5
        }
      },
      "93": {
        start: {
          line: 373,
          column: 6
        },
        end: {
          line: 375,
          column: 7
        }
      },
      "94": {
        start: {
          line: 374,
          column: 8
        },
        end: {
          line: 374,
          column: 31
        }
      },
      "95": {
        start: {
          line: 378,
          column: 4
        },
        end: {
          line: 378,
          column: 58
        }
      },
      "96": {
        start: {
          line: 378,
          column: 32
        },
        end: {
          line: 378,
          column: 56
        }
      },
      "97": {
        start: {
          line: 380,
          column: 4
        },
        end: {
          line: 382,
          column: 5
        }
      },
      "98": {
        start: {
          line: 381,
          column: 6
        },
        end: {
          line: 381,
          column: 25
        }
      },
      "99": {
        start: {
          line: 388,
          column: 35
        },
        end: {
          line: 398,
          column: 3
        }
      },
      "100": {
        start: {
          line: 389,
          column: 23
        },
        end: {
          line: 389,
          column: 65
        }
      },
      "101": {
        start: {
          line: 391,
          column: 4
        },
        end: {
          line: 391,
          column: 109
        }
      },
      "102": {
        start: {
          line: 402,
          column: 35
        },
        end: {
          line: 402,
          column: 60
        }
      },
      "103": {
        start: {
          line: 405,
          column: 30
        },
        end: {
          line: 406,
          column: 54
        }
      },
      "104": {
        start: {
          line: 406,
          column: 2
        },
        end: {
          line: 406,
          column: 54
        }
      },
      "105": {
        start: {
          line: 408,
          column: 29
        },
        end: {
          line: 409,
          column: 57
        }
      },
      "106": {
        start: {
          line: 409,
          column: 2
        },
        end: {
          line: 409,
          column: 57
        }
      },
      "107": {
        start: {
          line: 411,
          column: 29
        },
        end: {
          line: 412,
          column: 57
        }
      },
      "108": {
        start: {
          line: 412,
          column: 2
        },
        end: {
          line: 412,
          column: 57
        }
      },
      "109": {
        start: {
          line: 414,
          column: 36
        },
        end: {
          line: 415,
          column: 64
        }
      },
      "110": {
        start: {
          line: 415,
          column: 2
        },
        end: {
          line: 415,
          column: 64
        }
      },
      "111": {
        start: {
          line: 417,
          column: 30
        },
        end: {
          line: 418,
          column: 54
        }
      },
      "112": {
        start: {
          line: 418,
          column: 2
        },
        end: {
          line: 418,
          column: 54
        }
      }
    },
    fnMap: {
      "0": {
        name: "(anonymous_0)",
        decl: {
          start: {
            line: 62,
            column: 22
          },
          end: {
            line: 62,
            column: 23
          }
        },
        loc: {
          start: {
            line: 62,
            column: 65
          },
          end: {
            line: 62,
            column: 115
          }
        },
        line: 62
      },
      "1": {
        name: "(anonymous_1)",
        decl: {
          start: {
            line: 113,
            column: 2
          },
          end: {
            line: 113,
            column: 3
          }
        },
        loc: {
          start: {
            line: 113,
            column: 36
          },
          end: {
            line: 128,
            column: 3
          }
        },
        line: 113
      },
      "2": {
        name: "(anonymous_2)",
        decl: {
          start: {
            line: 118,
            column: 18
          },
          end: {
            line: 118,
            column: 19
          }
        },
        loc: {
          start: {
            line: 118,
            column: 24
          },
          end: {
            line: 120,
            column: 7
          }
        },
        line: 118
      },
      "3": {
        name: "(anonymous_3)",
        decl: {
          start: {
            line: 133,
            column: 2
          },
          end: {
            line: 133,
            column: 3
          }
        },
        loc: {
          start: {
            line: 136,
            column: 30
          },
          end: {
            line: 189,
            column: 3
          }
        },
        line: 136
      },
      "4": {
        name: "(anonymous_4)",
        decl: {
          start: {
            line: 194,
            column: 2
          },
          end: {
            line: 194,
            column: 3
          }
        },
        loc: {
          start: {
            line: 197,
            column: 19
          },
          end: {
            line: 210,
            column: 3
          }
        },
        line: 197
      },
      "5": {
        name: "(anonymous_5)",
        decl: {
          start: {
            line: 215,
            column: 2
          },
          end: {
            line: 215,
            column: 3
          }
        },
        loc: {
          start: {
            line: 218,
            column: 19
          },
          end: {
            line: 231,
            column: 3
          }
        },
        line: 218
      },
      "6": {
        name: "(anonymous_6)",
        decl: {
          start: {
            line: 236,
            column: 2
          },
          end: {
            line: 236,
            column: 3
          }
        },
        loc: {
          start: {
            line: 239,
            column: 21
          },
          end: {
            line: 250,
            column: 3
          }
        },
        line: 239
      },
      "7": {
        name: "(anonymous_7)",
        decl: {
          start: {
            line: 255,
            column: 2
          },
          end: {
            line: 255,
            column: 3
          }
        },
        loc: {
          start: {
            line: 258,
            column: 19
          },
          end: {
            line: 262,
            column: 3
          }
        },
        line: 258
      },
      "8": {
        name: "(anonymous_8)",
        decl: {
          start: {
            line: 267,
            column: 2
          },
          end: {
            line: 267,
            column: 3
          }
        },
        loc: {
          start: {
            line: 267,
            column: 85
          },
          end: {
            line: 286,
            column: 3
          }
        },
        line: 267
      },
      "9": {
        name: "(anonymous_9)",
        decl: {
          start: {
            line: 291,
            column: 2
          },
          end: {
            line: 291,
            column: 3
          }
        },
        loc: {
          start: {
            line: 294,
            column: 10
          },
          end: {
            line: 301,
            column: 3
          }
        },
        line: 294
      },
      "10": {
        name: "(anonymous_10)",
        decl: {
          start: {
            line: 306,
            column: 2
          },
          end: {
            line: 306,
            column: 3
          }
        },
        loc: {
          start: {
            line: 306,
            column: 34
          },
          end: {
            line: 309,
            column: 3
          }
        },
        line: 306
      },
      "11": {
        name: "(anonymous_11)",
        decl: {
          start: {
            line: 314,
            column: 2
          },
          end: {
            line: 314,
            column: 3
          }
        },
        loc: {
          start: {
            line: 314,
            column: 86
          },
          end: {
            line: 319,
            column: 3
          }
        },
        line: 314
      },
      "12": {
        name: "(anonymous_12)",
        decl: {
          start: {
            line: 324,
            column: 2
          },
          end: {
            line: 324,
            column: 3
          }
        },
        loc: {
          start: {
            line: 324,
            column: 68
          },
          end: {
            line: 332,
            column: 3
          }
        },
        line: 324
      },
      "13": {
        name: "(anonymous_13)",
        decl: {
          start: {
            line: 337,
            column: 2
          },
          end: {
            line: 337,
            column: 3
          }
        },
        loc: {
          start: {
            line: 337,
            column: 48
          },
          end: {
            line: 351,
            column: 3
          }
        },
        line: 337
      },
      "14": {
        name: "(anonymous_14)",
        decl: {
          start: {
            line: 356,
            column: 2
          },
          end: {
            line: 356,
            column: 3
          }
        },
        loc: {
          start: {
            line: 356,
            column: 45
          },
          end: {
            line: 363,
            column: 3
          }
        },
        line: 356
      },
      "15": {
        name: "(anonymous_15)",
        decl: {
          start: {
            line: 368,
            column: 2
          },
          end: {
            line: 368,
            column: 3
          }
        },
        loc: {
          start: {
            line: 368,
            column: 40
          },
          end: {
            line: 383,
            column: 3
          }
        },
        line: 368
      },
      "16": {
        name: "(anonymous_16)",
        decl: {
          start: {
            line: 378,
            column: 25
          },
          end: {
            line: 378,
            column: 26
          }
        },
        loc: {
          start: {
            line: 378,
            column: 32
          },
          end: {
            line: 378,
            column: 56
          }
        },
        line: 378
      },
      "17": {
        name: "(anonymous_17)",
        decl: {
          start: {
            line: 388,
            column: 35
          },
          end: {
            line: 388,
            column: 36
          }
        },
        loc: {
          start: {
            line: 388,
            column: 84
          },
          end: {
            line: 398,
            column: 3
          }
        },
        line: 388
      },
      "18": {
        name: "(anonymous_18)",
        decl: {
          start: {
            line: 405,
            column: 30
          },
          end: {
            line: 405,
            column: 31
          }
        },
        loc: {
          start: {
            line: 406,
            column: 2
          },
          end: {
            line: 406,
            column: 54
          }
        },
        line: 406
      },
      "19": {
        name: "(anonymous_19)",
        decl: {
          start: {
            line: 408,
            column: 29
          },
          end: {
            line: 408,
            column: 30
          }
        },
        loc: {
          start: {
            line: 409,
            column: 2
          },
          end: {
            line: 409,
            column: 57
          }
        },
        line: 409
      },
      "20": {
        name: "(anonymous_20)",
        decl: {
          start: {
            line: 411,
            column: 29
          },
          end: {
            line: 411,
            column: 30
          }
        },
        loc: {
          start: {
            line: 412,
            column: 2
          },
          end: {
            line: 412,
            column: 57
          }
        },
        line: 412
      },
      "21": {
        name: "(anonymous_21)",
        decl: {
          start: {
            line: 414,
            column: 36
          },
          end: {
            line: 414,
            column: 37
          }
        },
        loc: {
          start: {
            line: 415,
            column: 2
          },
          end: {
            line: 415,
            column: 64
          }
        },
        line: 415
      },
      "22": {
        name: "(anonymous_22)",
        decl: {
          start: {
            line: 417,
            column: 30
          },
          end: {
            line: 417,
            column: 31
          }
        },
        loc: {
          start: {
            line: 418,
            column: 2
          },
          end: {
            line: 418,
            column: 54
          }
        },
        line: 418
      }
    },
    branchMap: {
      "0": {
        loc: {
          start: {
            line: 76,
            column: 19
          },
          end: {
            line: 76,
            column: 66
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 76,
            column: 59
          },
          end: {
            line: 76,
            column: 61
          }
        }, {
          start: {
            line: 76,
            column: 64
          },
          end: {
            line: 76,
            column: 66
          }
        }],
        line: 76
      },
      "1": {
        loc: {
          start: {
            line: 80,
            column: 19
          },
          end: {
            line: 80,
            column: 67
          }
        },
        type: "cond-expr",
        locations: [{
          start: {
            line: 80,
            column: 59
          },
          end: {
            line: 80,
            column: 61
          }
        }, {
          start: {
            line: 80,
            column: 64
          },
          end: {
            line: 80,
            column: 67
          }
        }],
        line: 80
      },
      "2": {
        loc: {
          start: {
            line: 122,
            column: 6
          },
          end: {
            line: 124,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 122,
            column: 6
          },
          end: {
            line: 124,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 122
      },
      "3": {
        loc: {
          start: {
            line: 145,
            column: 6
          },
          end: {
            line: 151,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 145,
            column: 6
          },
          end: {
            line: 151,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 145
      },
      "4": {
        loc: {
          start: {
            line: 145,
            column: 10
          },
          end: {
            line: 145,
            column: 42
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 145,
            column: 10
          },
          end: {
            line: 145,
            column: 16
          }
        }, {
          start: {
            line: 145,
            column: 20
          },
          end: {
            line: 145,
            column: 42
          }
        }],
        line: 145
      },
      "5": {
        loc: {
          start: {
            line: 154,
            column: 6
          },
          end: {
            line: 168,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 154,
            column: 6
          },
          end: {
            line: 168,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 154
      },
      "6": {
        loc: {
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 160,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 158,
            column: 8
          },
          end: {
            line: 160,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 158
      },
      "7": {
        loc: {
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 209,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 200,
            column: 4
          },
          end: {
            line: 209,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 200
      },
      "8": {
        loc: {
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 208,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 204,
            column: 6
          },
          end: {
            line: 208,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 204
      },
      "9": {
        loc: {
          start: {
            line: 204,
            column: 10
          },
          end: {
            line: 204,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 204,
            column: 10
          },
          end: {
            line: 204,
            column: 15
          }
        }, {
          start: {
            line: 204,
            column: 19
          },
          end: {
            line: 204,
            column: 34
          }
        }],
        line: 204
      },
      "10": {
        loc: {
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 230,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 221,
            column: 4
          },
          end: {
            line: 230,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 221
      },
      "11": {
        loc: {
          start: {
            line: 225,
            column: 6
          },
          end: {
            line: 229,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 225,
            column: 6
          },
          end: {
            line: 229,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 225
      },
      "12": {
        loc: {
          start: {
            line: 225,
            column: 10
          },
          end: {
            line: 225,
            column: 34
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 225,
            column: 10
          },
          end: {
            line: 225,
            column: 15
          }
        }, {
          start: {
            line: 225,
            column: 19
          },
          end: {
            line: 225,
            column: 34
          }
        }],
        line: 225
      },
      "13": {
        loc: {
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 247,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 245,
            column: 4
          },
          end: {
            line: 247,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 245
      },
      "14": {
        loc: {
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 245,
            column: 40
          }
        },
        type: "binary-expr",
        locations: [{
          start: {
            line: 245,
            column: 8
          },
          end: {
            line: 245,
            column: 14
          }
        }, {
          start: {
            line: 245,
            column: 18
          },
          end: {
            line: 245,
            column: 40
          }
        }],
        line: 245
      },
      "15": {
        loc: {
          start: {
            line: 273,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 273,
            column: 6
          },
          end: {
            line: 280,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 273
      },
      "16": {
        loc: {
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 279,
            column: 9
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 276,
            column: 8
          },
          end: {
            line: 279,
            column: 9
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 276
      },
      "17": {
        loc: {
          start: {
            line: 295,
            column: 4
          },
          end: {
            line: 300,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 295,
            column: 4
          },
          end: {
            line: 300,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 295
      },
      "18": {
        loc: {
          start: {
            line: 315,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 315,
            column: 4
          },
          end: {
            line: 317,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 315
      },
      "19": {
        loc: {
          start: {
            line: 327,
            column: 4
          },
          end: {
            line: 329,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 327,
            column: 4
          },
          end: {
            line: 329,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 327
      },
      "20": {
        loc: {
          start: {
            line: 340,
            column: 6
          },
          end: {
            line: 346,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 340,
            column: 6
          },
          end: {
            line: 346,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 340
      },
      "21": {
        loc: {
          start: {
            line: 373,
            column: 6
          },
          end: {
            line: 375,
            column: 7
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 373,
            column: 6
          },
          end: {
            line: 375,
            column: 7
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 373
      },
      "22": {
        loc: {
          start: {
            line: 380,
            column: 4
          },
          end: {
            line: 382,
            column: 5
          }
        },
        type: "if",
        locations: [{
          start: {
            line: 380,
            column: 4
          },
          end: {
            line: 382,
            column: 5
          }
        }, {
          start: {
            line: undefined,
            column: undefined
          },
          end: {
            line: undefined,
            column: undefined
          }
        }],
        line: 380
      }
    },
    s: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0,
      "23": 0,
      "24": 0,
      "25": 0,
      "26": 0,
      "27": 0,
      "28": 0,
      "29": 0,
      "30": 0,
      "31": 0,
      "32": 0,
      "33": 0,
      "34": 0,
      "35": 0,
      "36": 0,
      "37": 0,
      "38": 0,
      "39": 0,
      "40": 0,
      "41": 0,
      "42": 0,
      "43": 0,
      "44": 0,
      "45": 0,
      "46": 0,
      "47": 0,
      "48": 0,
      "49": 0,
      "50": 0,
      "51": 0,
      "52": 0,
      "53": 0,
      "54": 0,
      "55": 0,
      "56": 0,
      "57": 0,
      "58": 0,
      "59": 0,
      "60": 0,
      "61": 0,
      "62": 0,
      "63": 0,
      "64": 0,
      "65": 0,
      "66": 0,
      "67": 0,
      "68": 0,
      "69": 0,
      "70": 0,
      "71": 0,
      "72": 0,
      "73": 0,
      "74": 0,
      "75": 0,
      "76": 0,
      "77": 0,
      "78": 0,
      "79": 0,
      "80": 0,
      "81": 0,
      "82": 0,
      "83": 0,
      "84": 0,
      "85": 0,
      "86": 0,
      "87": 0,
      "88": 0,
      "89": 0,
      "90": 0,
      "91": 0,
      "92": 0,
      "93": 0,
      "94": 0,
      "95": 0,
      "96": 0,
      "97": 0,
      "98": 0,
      "99": 0,
      "100": 0,
      "101": 0,
      "102": 0,
      "103": 0,
      "104": 0,
      "105": 0,
      "106": 0,
      "107": 0,
      "108": 0,
      "109": 0,
      "110": 0,
      "111": 0,
      "112": 0
    },
    f: {
      "0": 0,
      "1": 0,
      "2": 0,
      "3": 0,
      "4": 0,
      "5": 0,
      "6": 0,
      "7": 0,
      "8": 0,
      "9": 0,
      "10": 0,
      "11": 0,
      "12": 0,
      "13": 0,
      "14": 0,
      "15": 0,
      "16": 0,
      "17": 0,
      "18": 0,
      "19": 0,
      "20": 0,
      "21": 0,
      "22": 0
    },
    b: {
      "0": [0, 0],
      "1": [0, 0],
      "2": [0, 0],
      "3": [0, 0],
      "4": [0, 0],
      "5": [0, 0],
      "6": [0, 0],
      "7": [0, 0],
      "8": [0, 0],
      "9": [0, 0],
      "10": [0, 0],
      "11": [0, 0],
      "12": [0, 0],
      "13": [0, 0],
      "14": [0, 0],
      "15": [0, 0],
      "16": [0, 0],
      "17": [0, 0],
      "18": [0, 0],
      "19": [0, 0],
      "20": [0, 0],
      "21": [0, 0],
      "22": [0, 0]
    },
    _coverageSchema: "1a1c01bbd47fc00a2c39e90264f33305004495a9",
    hash: "90e4e60c332a55cb4d56c8cb81376c604ea4f824"
  };
  var coverage = global[gcv] || (global[gcv] = {});
  if (!coverage[path] || coverage[path].hash !== hash) {
    coverage[path] = coverageData;
  }
  var actualCoverage = coverage[path];
  {
    cov_2kt21rq1zn = function () {
      return actualCoverage;
    };
  }
  return actualCoverage;
}
cov_2kt21rq1zn();
import AsyncStorage from '@react-native-async-storage/async-storage';
import env from "../config/environment";
var RateLimitingService = function () {
  function RateLimitingService() {
    var _this = this;
    _classCallCheck(this, RateLimitingService);
    this.storage = (cov_2kt21rq1zn().s[0]++, new Map());
    this.storageKey = (cov_2kt21rq1zn().s[1]++, 'rate_limit_data');
    this.endpointLimits = (cov_2kt21rq1zn().s[2]++, {
      login: {
        windowMs: 15 * 60 * 1000,
        maxRequests: 5,
        onLimitReached: function onLimitReached(identifier, resetTime) {
          cov_2kt21rq1zn().f[0]++;
          cov_2kt21rq1zn().s[3]++;
          return _this.handleAuthLimitReached(identifier, resetTime);
        }
      },
      signup: {
        windowMs: 60 * 60 * 1000,
        maxRequests: 3
      },
      resetPassword: {
        windowMs: 60 * 60 * 1000,
        maxRequests: 3
      },
      aiAnalysis: {
        windowMs: 60 * 60 * 1000,
        maxRequests: env.getEnvironment() === 'production' ? (cov_2kt21rq1zn().b[0][0]++, 10) : (cov_2kt21rq1zn().b[0][1]++, 50)
      },
      aiCoaching: {
        windowMs: 60 * 60 * 1000,
        maxRequests: env.getEnvironment() === 'production' ? (cov_2kt21rq1zn().b[1][0]++, 20) : (cov_2kt21rq1zn().b[1][1]++, 100)
      },
      uploadVideo: {
        windowMs: 60 * 60 * 1000,
        maxRequests: 20
      },
      saveSession: {
        windowMs: 60 * 60 * 1000,
        maxRequests: 50
      },
      sendMessage: {
        windowMs: 60 * 1000,
        maxRequests: 10
      },
      createPost: {
        windowMs: 60 * 60 * 1000,
        maxRequests: 20
      },
      general: {
        windowMs: 60 * 1000,
        maxRequests: 100
      }
    });
    this.handleAuthLimitReached = (cov_2kt21rq1zn().s[99]++, function (identifier, resetTime) {
      cov_2kt21rq1zn().f[17]++;
      var retryAfter = (cov_2kt21rq1zn().s[100]++, Math.ceil((resetTime - Date.now()) / 1000));
      cov_2kt21rq1zn().s[101]++;
      console.warn(`Authentication rate limit exceeded for ${identifier}. Retry after ${retryAfter} seconds.`);
    });
  }
  return _createClass(RateLimitingService, [{
    key: "initialize",
    value: (function () {
      var _initialize = _asyncToGenerator(function* () {
        var _this2 = this;
        cov_2kt21rq1zn().f[1]++;
        cov_2kt21rq1zn().s[4]++;
        try {
          cov_2kt21rq1zn().s[5]++;
          yield this.loadStoredData();
          cov_2kt21rq1zn().s[6]++;
          setInterval(function () {
            cov_2kt21rq1zn().f[2]++;
            cov_2kt21rq1zn().s[7]++;
            _this2.cleanupExpiredEntries();
          }, 5 * 60 * 1000);
          cov_2kt21rq1zn().s[8]++;
          if (env.get('DEBUG_MODE')) {
            cov_2kt21rq1zn().b[2][0]++;
            cov_2kt21rq1zn().s[9]++;
            console.log('🚦 Rate limiting service initialized');
          } else {
            cov_2kt21rq1zn().b[2][1]++;
          }
        } catch (error) {
          cov_2kt21rq1zn().s[10]++;
          console.error('Failed to initialize rate limiting service:', error);
        }
      });
      function initialize() {
        return _initialize.apply(this, arguments);
      }
      return initialize;
    }())
  }, {
    key: "checkLimit",
    value: (function () {
      var _checkLimit = _asyncToGenerator(function* (endpoint, identifier) {
        cov_2kt21rq1zn().f[3]++;
        cov_2kt21rq1zn().s[11]++;
        try {
          var config = (cov_2kt21rq1zn().s[12]++, this.getEndpointConfig(endpoint));
          var key = (cov_2kt21rq1zn().s[13]++, this.generateKey(endpoint, identifier));
          var now = (cov_2kt21rq1zn().s[14]++, Date.now());
          var entry = (cov_2kt21rq1zn().s[15]++, this.storage.get(key));
          cov_2kt21rq1zn().s[16]++;
          if ((cov_2kt21rq1zn().b[4][0]++, !entry) || (cov_2kt21rq1zn().b[4][1]++, now >= entry.resetTime)) {
            cov_2kt21rq1zn().b[3][0]++;
            cov_2kt21rq1zn().s[17]++;
            entry = {
              count: 0,
              resetTime: now + config.windowMs,
              firstRequest: now
            };
          } else {
            cov_2kt21rq1zn().b[3][1]++;
          }
          cov_2kt21rq1zn().s[18]++;
          if (entry.count >= config.maxRequests) {
            cov_2kt21rq1zn().b[5][0]++;
            var retryAfter = (cov_2kt21rq1zn().s[19]++, Math.ceil((entry.resetTime - now) / 1000));
            cov_2kt21rq1zn().s[20]++;
            if (config.onLimitReached) {
              cov_2kt21rq1zn().b[6][0]++;
              cov_2kt21rq1zn().s[21]++;
              config.onLimitReached(identifier, entry.resetTime);
            } else {
              cov_2kt21rq1zn().b[6][1]++;
            }
            cov_2kt21rq1zn().s[22]++;
            return {
              allowed: false,
              remaining: 0,
              resetTime: entry.resetTime,
              retryAfter: retryAfter
            };
          } else {
            cov_2kt21rq1zn().b[5][1]++;
          }
          cov_2kt21rq1zn().s[23]++;
          entry.count++;
          cov_2kt21rq1zn().s[24]++;
          this.storage.set(key, entry);
          cov_2kt21rq1zn().s[25]++;
          yield this.persistData();
          cov_2kt21rq1zn().s[26]++;
          return {
            allowed: true,
            remaining: config.maxRequests - entry.count,
            resetTime: entry.resetTime
          };
        } catch (error) {
          cov_2kt21rq1zn().s[27]++;
          console.error('Rate limit check failed:', error);
          cov_2kt21rq1zn().s[28]++;
          return {
            allowed: true,
            remaining: 999,
            resetTime: Date.now() + 60000
          };
        }
      });
      function checkLimit(_x, _x2) {
        return _checkLimit.apply(this, arguments);
      }
      return checkLimit;
    }())
  }, {
    key: "recordSuccess",
    value: (function () {
      var _recordSuccess = _asyncToGenerator(function* (endpoint, identifier) {
        cov_2kt21rq1zn().f[4]++;
        var config = (cov_2kt21rq1zn().s[29]++, this.getEndpointConfig(endpoint));
        cov_2kt21rq1zn().s[30]++;
        if (config.skipSuccessfulRequests) {
          cov_2kt21rq1zn().b[7][0]++;
          var key = (cov_2kt21rq1zn().s[31]++, this.generateKey(endpoint, identifier));
          var entry = (cov_2kt21rq1zn().s[32]++, this.storage.get(key));
          cov_2kt21rq1zn().s[33]++;
          if ((cov_2kt21rq1zn().b[9][0]++, entry) && (cov_2kt21rq1zn().b[9][1]++, entry.count > 0)) {
            cov_2kt21rq1zn().b[8][0]++;
            cov_2kt21rq1zn().s[34]++;
            entry.count--;
            cov_2kt21rq1zn().s[35]++;
            this.storage.set(key, entry);
            cov_2kt21rq1zn().s[36]++;
            yield this.persistData();
          } else {
            cov_2kt21rq1zn().b[8][1]++;
          }
        } else {
          cov_2kt21rq1zn().b[7][1]++;
        }
      });
      function recordSuccess(_x3, _x4) {
        return _recordSuccess.apply(this, arguments);
      }
      return recordSuccess;
    }())
  }, {
    key: "recordFailure",
    value: (function () {
      var _recordFailure = _asyncToGenerator(function* (endpoint, identifier) {
        cov_2kt21rq1zn().f[5]++;
        var config = (cov_2kt21rq1zn().s[37]++, this.getEndpointConfig(endpoint));
        cov_2kt21rq1zn().s[38]++;
        if (config.skipFailedRequests) {
          cov_2kt21rq1zn().b[10][0]++;
          var key = (cov_2kt21rq1zn().s[39]++, this.generateKey(endpoint, identifier));
          var entry = (cov_2kt21rq1zn().s[40]++, this.storage.get(key));
          cov_2kt21rq1zn().s[41]++;
          if ((cov_2kt21rq1zn().b[12][0]++, entry) && (cov_2kt21rq1zn().b[12][1]++, entry.count > 0)) {
            cov_2kt21rq1zn().b[11][0]++;
            cov_2kt21rq1zn().s[42]++;
            entry.count--;
            cov_2kt21rq1zn().s[43]++;
            this.storage.set(key, entry);
            cov_2kt21rq1zn().s[44]++;
            yield this.persistData();
          } else {
            cov_2kt21rq1zn().b[11][1]++;
          }
        } else {
          cov_2kt21rq1zn().b[10][1]++;
        }
      });
      function recordFailure(_x5, _x6) {
        return _recordFailure.apply(this, arguments);
      }
      return recordFailure;
    }())
  }, {
    key: "getRemainingRequests",
    value: (function () {
      var _getRemainingRequests = _asyncToGenerator(function* (endpoint, identifier) {
        cov_2kt21rq1zn().f[6]++;
        var config = (cov_2kt21rq1zn().s[45]++, this.getEndpointConfig(endpoint));
        var key = (cov_2kt21rq1zn().s[46]++, this.generateKey(endpoint, identifier));
        var entry = (cov_2kt21rq1zn().s[47]++, this.storage.get(key));
        var now = (cov_2kt21rq1zn().s[48]++, Date.now());
        cov_2kt21rq1zn().s[49]++;
        if ((cov_2kt21rq1zn().b[14][0]++, !entry) || (cov_2kt21rq1zn().b[14][1]++, now >= entry.resetTime)) {
          cov_2kt21rq1zn().b[13][0]++;
          cov_2kt21rq1zn().s[50]++;
          return config.maxRequests;
        } else {
          cov_2kt21rq1zn().b[13][1]++;
        }
        cov_2kt21rq1zn().s[51]++;
        return Math.max(0, config.maxRequests - entry.count);
      });
      function getRemainingRequests(_x7, _x8) {
        return _getRemainingRequests.apply(this, arguments);
      }
      return getRemainingRequests;
    }())
  }, {
    key: "resetLimit",
    value: (function () {
      var _resetLimit = _asyncToGenerator(function* (endpoint, identifier) {
        cov_2kt21rq1zn().f[7]++;
        var key = (cov_2kt21rq1zn().s[52]++, this.generateKey(endpoint, identifier));
        cov_2kt21rq1zn().s[53]++;
        this.storage.delete(key);
        cov_2kt21rq1zn().s[54]++;
        yield this.persistData();
      });
      function resetLimit(_x9, _x0) {
        return _resetLimit.apply(this, arguments);
      }
      return resetLimit;
    }())
  }, {
    key: "getLimitStatus",
    value: (function () {
      var _getLimitStatus = _asyncToGenerator(function* (identifier) {
        cov_2kt21rq1zn().f[8]++;
        var status = (cov_2kt21rq1zn().s[55]++, {});
        cov_2kt21rq1zn().s[56]++;
        for (var endpoint of Object.keys(this.endpointLimits)) {
          var result = (cov_2kt21rq1zn().s[57]++, yield this.checkLimit(endpoint, identifier));
          cov_2kt21rq1zn().s[58]++;
          if (result.allowed) {
            cov_2kt21rq1zn().b[15][0]++;
            var key = (cov_2kt21rq1zn().s[59]++, this.generateKey(endpoint, identifier));
            var entry = (cov_2kt21rq1zn().s[60]++, this.storage.get(key));
            cov_2kt21rq1zn().s[61]++;
            if (entry) {
              cov_2kt21rq1zn().b[16][0]++;
              cov_2kt21rq1zn().s[62]++;
              entry.count--;
              cov_2kt21rq1zn().s[63]++;
              this.storage.set(key, entry);
            } else {
              cov_2kt21rq1zn().b[16][1]++;
            }
          } else {
            cov_2kt21rq1zn().b[15][1]++;
          }
          cov_2kt21rq1zn().s[64]++;
          status[endpoint] = result;
        }
        cov_2kt21rq1zn().s[65]++;
        yield this.persistData();
        cov_2kt21rq1zn().s[66]++;
        return status;
      });
      function getLimitStatus(_x1) {
        return _getLimitStatus.apply(this, arguments);
      }
      return getLimitStatus;
    }())
  }, {
    key: "updateEndpointConfig",
    value: function updateEndpointConfig(endpoint, config) {
      cov_2kt21rq1zn().f[9]++;
      cov_2kt21rq1zn().s[67]++;
      if (endpoint in this.endpointLimits) {
        cov_2kt21rq1zn().b[17][0]++;
        cov_2kt21rq1zn().s[68]++;
        this.endpointLimits[endpoint] = Object.assign({}, this.endpointLimits[endpoint], config);
      } else {
        cov_2kt21rq1zn().b[17][1]++;
      }
    }
  }, {
    key: "clearAll",
    value: (function () {
      var _clearAll = _asyncToGenerator(function* () {
        cov_2kt21rq1zn().f[10]++;
        cov_2kt21rq1zn().s[69]++;
        this.storage.clear();
        cov_2kt21rq1zn().s[70]++;
        yield AsyncStorage.removeItem(this.storageKey);
      });
      function clearAll() {
        return _clearAll.apply(this, arguments);
      }
      return clearAll;
    }())
  }, {
    key: "getEndpointConfig",
    value: function getEndpointConfig(endpoint) {
      cov_2kt21rq1zn().f[11]++;
      cov_2kt21rq1zn().s[71]++;
      if (endpoint in this.endpointLimits) {
        cov_2kt21rq1zn().b[18][0]++;
        cov_2kt21rq1zn().s[72]++;
        return this.endpointLimits[endpoint];
      } else {
        cov_2kt21rq1zn().b[18][1]++;
      }
      cov_2kt21rq1zn().s[73]++;
      return this.endpointLimits.general;
    }
  }, {
    key: "generateKey",
    value: function generateKey(endpoint, identifier) {
      cov_2kt21rq1zn().f[12]++;
      var config = (cov_2kt21rq1zn().s[74]++, this.getEndpointConfig(endpoint));
      cov_2kt21rq1zn().s[75]++;
      if (config.keyGenerator) {
        cov_2kt21rq1zn().b[19][0]++;
        cov_2kt21rq1zn().s[76]++;
        return config.keyGenerator(identifier);
      } else {
        cov_2kt21rq1zn().b[19][1]++;
      }
      cov_2kt21rq1zn().s[77]++;
      return `${endpoint}:${identifier}`;
    }
  }, {
    key: "loadStoredData",
    value: (function () {
      var _loadStoredData = _asyncToGenerator(function* () {
        cov_2kt21rq1zn().f[13]++;
        cov_2kt21rq1zn().s[78]++;
        try {
          var stored = (cov_2kt21rq1zn().s[79]++, yield AsyncStorage.getItem(this.storageKey));
          cov_2kt21rq1zn().s[80]++;
          if (stored) {
            cov_2kt21rq1zn().b[20][0]++;
            var data = (cov_2kt21rq1zn().s[81]++, JSON.parse(stored));
            cov_2kt21rq1zn().s[82]++;
            this.storage = new Map(Object.entries(data));
            cov_2kt21rq1zn().s[83]++;
            this.cleanupExpiredEntries();
          } else {
            cov_2kt21rq1zn().b[20][1]++;
          }
        } catch (error) {
          cov_2kt21rq1zn().s[84]++;
          console.error('Failed to load rate limit data:', error);
          cov_2kt21rq1zn().s[85]++;
          this.storage.clear();
        }
      });
      function loadStoredData() {
        return _loadStoredData.apply(this, arguments);
      }
      return loadStoredData;
    }())
  }, {
    key: "persistData",
    value: (function () {
      var _persistData = _asyncToGenerator(function* () {
        cov_2kt21rq1zn().f[14]++;
        cov_2kt21rq1zn().s[86]++;
        try {
          var data = (cov_2kt21rq1zn().s[87]++, Object.fromEntries(this.storage));
          cov_2kt21rq1zn().s[88]++;
          yield AsyncStorage.setItem(this.storageKey, JSON.stringify(data));
        } catch (error) {
          cov_2kt21rq1zn().s[89]++;
          console.error('Failed to persist rate limit data:', error);
        }
      });
      function persistData() {
        return _persistData.apply(this, arguments);
      }
      return persistData;
    }())
  }, {
    key: "cleanupExpiredEntries",
    value: function cleanupExpiredEntries() {
      var _this3 = this;
      cov_2kt21rq1zn().f[15]++;
      var now = (cov_2kt21rq1zn().s[90]++, Date.now());
      var keysToDelete = (cov_2kt21rq1zn().s[91]++, []);
      cov_2kt21rq1zn().s[92]++;
      for (var _ref of this.storage) {
        var _ref2 = _slicedToArray(_ref, 2);
        var key = _ref2[0];
        var entry = _ref2[1];
        cov_2kt21rq1zn().s[93]++;
        if (now >= entry.resetTime) {
          cov_2kt21rq1zn().b[21][0]++;
          cov_2kt21rq1zn().s[94]++;
          keysToDelete.push(key);
        } else {
          cov_2kt21rq1zn().b[21][1]++;
        }
      }
      cov_2kt21rq1zn().s[95]++;
      keysToDelete.forEach(function (key) {
        cov_2kt21rq1zn().f[16]++;
        cov_2kt21rq1zn().s[96]++;
        return _this3.storage.delete(key);
      });
      cov_2kt21rq1zn().s[97]++;
      if (keysToDelete.length > 0) {
        cov_2kt21rq1zn().b[22][0]++;
        cov_2kt21rq1zn().s[98]++;
        this.persistData();
      } else {
        cov_2kt21rq1zn().b[22][1]++;
      }
    }
  }]);
}();
export var rateLimitingService = (cov_2kt21rq1zn().s[102]++, new RateLimitingService());
cov_2kt21rq1zn().s[103]++;
export var checkRateLimit = function checkRateLimit(endpoint, identifier) {
  cov_2kt21rq1zn().f[18]++;
  cov_2kt21rq1zn().s[104]++;
  return rateLimitingService.checkLimit(endpoint, identifier);
};
cov_2kt21rq1zn().s[105]++;
export var recordSuccess = function recordSuccess(endpoint, identifier) {
  cov_2kt21rq1zn().f[19]++;
  cov_2kt21rq1zn().s[106]++;
  return rateLimitingService.recordSuccess(endpoint, identifier);
};
cov_2kt21rq1zn().s[107]++;
export var recordFailure = function recordFailure(endpoint, identifier) {
  cov_2kt21rq1zn().f[20]++;
  cov_2kt21rq1zn().s[108]++;
  return rateLimitingService.recordFailure(endpoint, identifier);
};
cov_2kt21rq1zn().s[109]++;
export var getRemainingRequests = function getRemainingRequests(endpoint, identifier) {
  cov_2kt21rq1zn().f[21]++;
  cov_2kt21rq1zn().s[110]++;
  return rateLimitingService.getRemainingRequests(endpoint, identifier);
};
cov_2kt21rq1zn().s[111]++;
export var resetRateLimit = function resetRateLimit(endpoint, identifier) {
  cov_2kt21rq1zn().f[22]++;
  cov_2kt21rq1zn().s[112]++;
  return rateLimitingService.resetLimit(endpoint, identifier);
};
export default rateLimitingService;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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