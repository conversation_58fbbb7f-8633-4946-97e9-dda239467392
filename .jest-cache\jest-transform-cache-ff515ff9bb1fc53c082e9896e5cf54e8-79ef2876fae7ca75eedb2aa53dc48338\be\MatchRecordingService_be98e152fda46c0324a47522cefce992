a11520fb7d4f63eb95caf789577e0540
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.matchRecordingService = void 0;
var _toConsumableArray2 = _interopRequireDefault(require("@babel/runtime/helpers/toConsumableArray"));
var _asyncToGenerator2 = _interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));
var _classCallCheck2 = _interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));
var _createClass2 = _interopRequireDefault(require("@babel/runtime/helpers/createClass"));
var _VideoRecordingService = require("../video/VideoRecordingService");
var _MatchRepository = require("../database/MatchRepository");
var _FileUploadService = require("../storage/FileUploadService");
var _performance = require("../../../utils/performance");
var MatchRecordingService = function () {
  function MatchRecordingService() {
    (0, _classCallCheck2.default)(this, MatchRecordingService);
    this.currentSession = null;
    this.sessionListeners = [];
    this.scoreListeners = [];
    this.offlineSyncQueue = null;
    this.syncInterval = null;
    this.autoSaveInterval = null;
  }
  return (0, _createClass2.default)(MatchRecordingService, [{
    key: "startMatch",
    value: (function () {
      var _startMatch = (0, _asyncToGenerator2.default)(function* (metadata, options) {
        try {
          _performance.performanceMonitor.start('match_recording_start');
          this.validateMatchMetadata(metadata);
          if (this.currentSession) {
            throw new Error('Another match recording is already in progress');
          }
          var matchRecording = {
            id: `match_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            metadata: Object.assign({}, metadata, {
              startTime: new Date().toISOString()
            }),
            score: this.initializeScore(metadata.matchFormat),
            statistics: this.initializeStatistics(metadata.userId),
            status: 'recording',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          };
          var session = {
            id: this.generateSessionId(),
            match: matchRecording,
            currentSet: 1,
            currentGame: 1,
            isRecording: true,
            isPaused: false,
            startTime: Date.now(),
            pausedTime: 0,
            totalPausedDuration: 0,
            videoRecordingActive: options.enableVideoRecording,
            autoScoreDetection: options.enableAutoScoreDetection
          };
          if (options.enableVideoRecording) {
            yield _VideoRecordingService.videoRecordingService.startRecording(options.videoConfig);
          }
          var savedMatch = yield this.saveMatchToDatabase(matchRecording);
          if (!savedMatch.success) {
            throw new Error(savedMatch.error || 'Failed to save match to database');
          }
          session.match.id = savedMatch.data.id;
          session.match.databaseId = savedMatch.data.databaseId;
          this.setupOfflineSync(session.match.id);
          this.currentSession = session;
          this.notifySessionListeners();
          this.startAutoSave();
          _performance.performanceMonitor.end('match_recording_start');
          return session;
        } catch (error) {
          console.error('Failed to start match recording:', error);
          if (this.currentSession) {
            yield this.cleanupFailedSession();
          }
          throw error;
        }
      });
      function startMatch(_x, _x2) {
        return _startMatch.apply(this, arguments);
      }
      return startMatch;
    }())
  }, {
    key: "addPoint",
    value: (function () {
      var _addPoint = (0, _asyncToGenerator2.default)(function* (winner) {
        var eventType = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'normal';
        var shotType = arguments.length > 2 ? arguments[2] : undefined;
        var courtPosition = arguments.length > 3 ? arguments[3] : undefined;
        if (!this.currentSession) {
          throw new Error('No active match session');
        }
        try {
          var session = this.currentSession;
          var currentSet = session.currentSet;
          var currentGame = session.currentGame;
          var gameEvent = {
            id: this.generateEventId(),
            timestamp: Date.now(),
            eventType: eventType === 'normal' ? 'point_won' : eventType,
            player: winner,
            shotType: shotType,
            courtPosition: courtPosition,
            description: `Point won by ${winner}`
          };
          var updatedScore = this.updateScore(session.match.score, currentSet, currentGame, winner, gameEvent);
          this.updateStatistics(session.match.statistics, gameEvent);
          session.match.score = updatedScore;
          session.match.updatedAt = new Date().toISOString();
          var setComplete = this.isSetComplete(updatedScore.sets[currentSet - 1]);
          var matchComplete = this.isMatchComplete(updatedScore, session.match.metadata.matchFormat);
          if (setComplete && !matchComplete) {
            session.currentSet++;
            session.currentGame = 1;
          } else if (!setComplete) {
            var gameComplete = this.isGameComplete(updatedScore.sets[currentSet - 1], currentGame);
            if (gameComplete) {
              session.currentGame++;
            }
          }
          if (matchComplete) {
            yield this.endMatch();
          } else {
            yield this.updateMatchInDatabase(session.match);
          }
          this.notifyScoreListeners();
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to add point:', error);
          throw error;
        }
      });
      function addPoint(_x3) {
        return _addPoint.apply(this, arguments);
      }
      return addPoint;
    }())
  }, {
    key: "pauseMatch",
    value: (function () {
      var _pauseMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession || this.currentSession.isPaused) {
          return;
        }
        try {
          this.currentSession.isPaused = true;
          this.currentSession.pausedTime = Date.now();
          this.currentSession.match.status = 'paused';
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.pauseRecording();
          }
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to pause match:', error);
          throw error;
        }
      });
      function pauseMatch() {
        return _pauseMatch.apply(this, arguments);
      }
      return pauseMatch;
    }())
  }, {
    key: "resumeMatch",
    value: (function () {
      var _resumeMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession || !this.currentSession.isPaused) {
          return;
        }
        try {
          var pauseDuration = Date.now() - this.currentSession.pausedTime;
          this.currentSession.totalPausedDuration += pauseDuration;
          this.currentSession.isPaused = false;
          this.currentSession.pausedTime = 0;
          this.currentSession.match.status = 'recording';
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.resumeRecording();
          }
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to resume match:', error);
          throw error;
        }
      });
      function resumeMatch() {
        return _resumeMatch.apply(this, arguments);
      }
      return resumeMatch;
    }())
  }, {
    key: "endMatch",
    value: (function () {
      var _endMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession) {
          throw new Error('No active match session');
        }
        try {
          _performance.performanceMonitor.start('match_recording_end');
          var session = this.currentSession;
          var endTime = Date.now();
          var totalDuration = (endTime - session.startTime - session.totalPausedDuration) / 1000 / 60;
          session.match.metadata.endTime = new Date().toISOString();
          session.match.metadata.durationMinutes = Math.round(totalDuration);
          session.match.status = 'completed';
          if (session.videoRecordingActive) {
            var videoResult = yield _VideoRecordingService.videoRecordingService.stopRecording();
            var uploadResult = yield _FileUploadService.fileUploadService.uploadVideo(videoResult.uri, {
              folder: `matches/${session.match.id || 'temp'}`
            });
            if (uploadResult.data) {
              session.match.videoUrl = uploadResult.data.url;
              session.match.videoDurationSeconds = videoResult.duration;
              session.match.videoFileSizeBytes = uploadResult.data.size;
              if (videoResult.thumbnail) {
                var thumbnailResult = yield _FileUploadService.fileUploadService.uploadThumbnail(videoResult.uri, videoResult.thumbnail, {
                  folder: `matches/${session.match.id || 'temp'}/thumbnails`
                });
                if (thumbnailResult.data) {
                  session.match.videoThumbnailUrl = thumbnailResult.data.url;
                }
              }
            }
          }
          this.calculateFinalStatistics(session.match.statistics, session.match.score);
          var finalMatch = yield this.updateMatchInDatabase(session.match);
          this.currentSession = null;
          this.notifySessionListeners();
          _performance.performanceMonitor.end('match_recording_end');
          return finalMatch;
        } catch (error) {
          console.error('Failed to end match:', error);
          throw error;
        }
      });
      function endMatch() {
        return _endMatch.apply(this, arguments);
      }
      return endMatch;
    }())
  }, {
    key: "cancelMatch",
    value: (function () {
      var _cancelMatch = (0, _asyncToGenerator2.default)(function* () {
        if (!this.currentSession) {
          return;
        }
        try {
          if (this.currentSession.videoRecordingActive) {
            yield _VideoRecordingService.videoRecordingService.stopRecording();
          }
          this.currentSession.match.status = 'cancelled';
          yield this.updateMatchInDatabase(this.currentSession.match);
          this.currentSession = null;
          this.notifySessionListeners();
        } catch (error) {
          console.error('Failed to cancel match:', error);
          throw error;
        }
      });
      function cancelMatch() {
        return _cancelMatch.apply(this, arguments);
      }
      return cancelMatch;
    }())
  }, {
    key: "getCurrentSession",
    value: function getCurrentSession() {
      return this.currentSession;
    }
  }, {
    key: "addSessionListener",
    value: function addSessionListener(listener) {
      this.sessionListeners.push(listener);
    }
  }, {
    key: "removeSessionListener",
    value: function removeSessionListener(listener) {
      this.sessionListeners = this.sessionListeners.filter(function (l) {
        return l !== listener;
      });
    }
  }, {
    key: "addScoreListener",
    value: function addScoreListener(listener) {
      this.scoreListeners.push(listener);
    }
  }, {
    key: "removeScoreListener",
    value: function removeScoreListener(listener) {
      this.scoreListeners = this.scoreListeners.filter(function (l) {
        return l !== listener;
      });
    }
  }, {
    key: "validateMatchMetadata",
    value: function validateMatchMetadata(metadata) {
      var _metadata$opponentNam;
      if (!((_metadata$opponentNam = metadata.opponentName) != null && _metadata$opponentNam.trim())) {
        throw new Error('Opponent name is required');
      }
      if (!metadata.userId) {
        throw new Error('User ID is required');
      }
      if (!metadata.matchType) {
        throw new Error('Match type is required');
      }
      if (!metadata.matchFormat) {
        throw new Error('Match format is required');
      }
      if (!metadata.surface) {
        throw new Error('Court surface is required');
      }
    }
  }, {
    key: "initializeScore",
    value: function initializeScore(format) {
      var maxSets = format === 'best_of_5' ? 5 : 3;
      return {
        sets: [],
        finalScore: '',
        result: 'win',
        setsWon: 0,
        setsLost: 0
      };
    }
  }, {
    key: "initializeStatistics",
    value: function initializeStatistics(userId) {
      return {
        matchId: '',
        userId: userId,
        aces: 0,
        doubleFaults: 0,
        firstServesIn: 0,
        firstServesAttempted: 0,
        firstServePointsWon: 0,
        secondServePointsWon: 0,
        firstServeReturnPointsWon: 0,
        secondServeReturnPointsWon: 0,
        breakPointsConverted: 0,
        breakPointsFaced: 0,
        winners: 0,
        unforcedErrors: 0,
        forcedErrors: 0,
        totalPointsWon: 0,
        totalPointsPlayed: 0,
        netPointsAttempted: 0,
        netPointsWon: 0,
        forehandWinners: 0,
        backhandWinners: 0,
        forehandErrors: 0,
        backhandErrors: 0
      };
    }
  }, {
    key: "updateScore",
    value: function updateScore(currentScore, setNumber, gameNumber, winner, event) {
      var updatedScore = Object.assign({}, currentScore);
      while (updatedScore.sets.length < setNumber) {
        updatedScore.sets.push({
          setNumber: updatedScore.sets.length + 1,
          userGames: 0,
          opponentGames: 0,
          isTiebreak: false,
          isCompleted: false
        });
      }
      var currentSet = updatedScore.sets[setNumber - 1];
      if (winner === 'user') {} else {}
      return updatedScore;
    }
  }, {
    key: "updateStatistics",
    value: function updateStatistics(statistics, event) {
      statistics.totalPointsPlayed++;
      if (event.player === 'user') {
        statistics.totalPointsWon++;
      }
      switch (event.eventType) {
        case 'ace':
          statistics.aces++;
          break;
        case 'double_fault':
          statistics.doubleFaults++;
          break;
        case 'winner':
          statistics.winners++;
          break;
        case 'unforced_error':
          statistics.unforcedErrors++;
          break;
        case 'forced_error':
          statistics.forcedErrors++;
          break;
      }
    }
  }, {
    key: "isSetComplete",
    value: function isSetComplete(set) {
      return set.userGames >= 6 && set.userGames - set.opponentGames >= 2 || set.opponentGames >= 6 && set.opponentGames - set.userGames >= 2 || set.isTiebreak;
    }
  }, {
    key: "isGameComplete",
    value: function isGameComplete(set, gameNumber) {
      return true;
    }
  }, {
    key: "isMatchComplete",
    value: function isMatchComplete(score, format) {
      var setsToWin = format === 'best_of_5' ? 3 : 2;
      return score.setsWon >= setsToWin || score.setsLost >= setsToWin;
    }
  }, {
    key: "calculateFinalStatistics",
    value: function calculateFinalStatistics(statistics, score) {
      if (statistics.firstServesAttempted > 0) {
        statistics.firstServePercentage = statistics.firstServesIn / statistics.firstServesAttempted * 100;
      }
      if (statistics.breakPointsFaced > 0) {
        statistics.breakPointConversionRate = statistics.breakPointsConverted / statistics.breakPointsFaced * 100;
      }
      if (statistics.netPointsAttempted > 0) {
        statistics.netSuccessRate = statistics.netPointsWon / statistics.netPointsAttempted * 100;
      }
    }
  }, {
    key: "saveMatchToDatabase",
    value: function () {
      var _saveMatchToDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        try {
          var matchData = {
            id: match.id,
            user_id: match.metadata.userId,
            opponent_name: match.metadata.opponentName,
            match_type: match.metadata.matchType || 'friendly',
            match_format: match.metadata.matchFormat,
            surface: match.metadata.surface,
            location: match.metadata.location,
            court_name: match.metadata.courtName,
            weather_conditions: match.metadata.weather,
            temperature: match.metadata.temperature,
            match_date: new Date(match.metadata.startTime).toISOString().split('T')[0],
            start_time: new Date(match.metadata.startTime).toTimeString().split(' ')[0],
            status: match.status,
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            created_at: match.createdAt,
            updated_at: match.updatedAt
          };
          var attempts = 0;
          var maxAttempts = 3;
          while (attempts < maxAttempts) {
            try {
              var _result$data;
              var result = yield _MatchRepository.matchRepository.createMatch(matchData);
              if (result.error) {
                if (attempts === maxAttempts - 1) {
                  return {
                    success: false,
                    error: result.error
                  };
                }
                attempts++;
                yield new Promise(function (resolve) {
                  return setTimeout(resolve, 1000 * attempts);
                });
                continue;
              }
              return {
                success: true,
                data: {
                  id: match.id,
                  databaseId: (_result$data = result.data) == null ? void 0 : _result$data.id
                }
              };
            } catch (error) {
              attempts++;
              if (attempts === maxAttempts) {
                throw error;
              }
              yield new Promise(function (resolve) {
                return setTimeout(resolve, 1000 * attempts);
              });
            }
          }
          return {
            success: false,
            error: 'Failed to save after multiple attempts'
          };
        } catch (error) {
          console.error('Error saving match to database:', error);
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function saveMatchToDatabase(_x4) {
        return _saveMatchToDatabase.apply(this, arguments);
      }
      return saveMatchToDatabase;
    }()
  }, {
    key: "updateMatchInDatabase",
    value: function () {
      var _updateMatchInDatabase = (0, _asyncToGenerator2.default)(function* (match) {
        try {
          if (!match.id) {
            return {
              success: false,
              error: 'Match ID is required for update'
            };
          }
          var updateData = {
            current_score: JSON.stringify(match.score),
            statistics: JSON.stringify(match.statistics),
            status: match.status,
            updated_at: new Date().toISOString()
          };
          if (match.status === 'completed' && match.metadata.endTime) {
            updateData.end_time = new Date(match.metadata.endTime).toTimeString().split(' ')[0];
            updateData.duration_minutes = Math.round((new Date(match.metadata.endTime).getTime() - new Date(match.metadata.startTime).getTime()) / (1000 * 60));
            updateData.final_score = this.generateFinalScoreString(match.score);
            updateData.result = this.determineMatchResult(match.score, match.metadata.userId);
            updateData.sets_won = match.score.setsWon;
            updateData.sets_lost = match.score.setsLost;
          }
          var result = yield _MatchRepository.matchRepository.updateMatch(match.id, updateData);
          if (result.error) {
            return {
              success: false,
              error: result.error
            };
          }
          return {
            success: true
          };
        } catch (error) {
          console.error('Error updating match in database:', error);
          return {
            success: false,
            error: 'Database connection failed'
          };
        }
      });
      function updateMatchInDatabase(_x5) {
        return _updateMatchInDatabase.apply(this, arguments);
      }
      return updateMatchInDatabase;
    }()
  }, {
    key: "generateFinalScoreString",
    value: function generateFinalScoreString(score) {
      if (!score.sets || score.sets.length === 0) {
        return '0-0';
      }
      return score.sets.map(function (set) {
        return `${set.userGames}-${set.opponentGames}`;
      }).join(', ');
    }
  }, {
    key: "determineMatchResult",
    value: function determineMatchResult(score, userId) {
      if (score.setsWon > score.setsLost) {
        return 'win';
      } else if (score.setsLost > score.setsWon) {
        return 'loss';
      }
      return 'draw';
    }
  }, {
    key: "generateSessionId",
    value: function generateSessionId() {
      return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "generateEventId",
    value: function generateEventId() {
      return `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }, {
    key: "notifySessionListeners",
    value: function notifySessionListeners() {
      var _this = this;
      this.sessionListeners.forEach(function (listener) {
        return listener(_this.currentSession);
      });
    }
  }, {
    key: "notifyScoreListeners",
    value: function notifyScoreListeners() {
      var _this2 = this;
      if (this.currentSession) {
        this.scoreListeners.forEach(function (listener) {
          return listener(_this2.currentSession.match.score);
        });
      }
    }
  }, {
    key: "setupOfflineSync",
    value: function setupOfflineSync(matchId) {
      try {
        if (!this.offlineSyncQueue) {
          this.offlineSyncQueue = new Map();
        }
        this.offlineSyncQueue.set(matchId, []);
        this.startOfflineSync(matchId);
      } catch (error) {
        console.error('Failed to setup offline sync:', error);
      }
    }
  }, {
    key: "startOfflineSync",
    value: function startOfflineSync(matchId) {
      var _this3 = this;
      if (this.syncInterval) {
        clearInterval(this.syncInterval);
      }
      this.syncInterval = setInterval((0, _asyncToGenerator2.default)(function* () {
        yield _this3.syncOfflineData(matchId);
      }), 30000);
    }
  }, {
    key: "syncOfflineData",
    value: (function () {
      var _syncOfflineData = (0, _asyncToGenerator2.default)(function* (matchId) {
        try {
          var _this$offlineSyncQueu, _this$offlineSyncQueu2;
          var queue = (_this$offlineSyncQueu = this.offlineSyncQueue) == null ? void 0 : _this$offlineSyncQueu.get(matchId);
          if (!queue || queue.length === 0) {
            return;
          }
          var updates = (0, _toConsumableArray2.default)(queue);
          (_this$offlineSyncQueu2 = this.offlineSyncQueue) == null || _this$offlineSyncQueu2.set(matchId, []);
          for (var update of updates) {
            try {
              yield this.processOfflineUpdate(update);
            } catch (error) {
              var _this$offlineSyncQueu3;
              console.error('Failed to sync update:', error);
              (_this$offlineSyncQueu3 = this.offlineSyncQueue) == null || (_this$offlineSyncQueu3 = _this$offlineSyncQueu3.get(matchId)) == null || _this$offlineSyncQueu3.push(update);
            }
          }
        } catch (error) {
          console.error('Failed to sync offline data:', error);
        }
      });
      function syncOfflineData(_x6) {
        return _syncOfflineData.apply(this, arguments);
      }
      return syncOfflineData;
    }())
  }, {
    key: "processOfflineUpdate",
    value: (function () {
      var _processOfflineUpdate = (0, _asyncToGenerator2.default)(function* (update) {
        switch (update.type) {
          case 'match_update':
            yield this.updateMatchInDatabase(update.data);
            break;
          case 'score_update':
            break;
          case 'statistics_update':
            break;
          default:
            console.warn('Unknown update type:', update.type);
        }
      });
      function processOfflineUpdate(_x7) {
        return _processOfflineUpdate.apply(this, arguments);
      }
      return processOfflineUpdate;
    }())
  }, {
    key: "startAutoSave",
    value: function startAutoSave() {
      var _this4 = this;
      if (this.autoSaveInterval) {
        clearInterval(this.autoSaveInterval);
      }
      this.autoSaveInterval = setInterval((0, _asyncToGenerator2.default)(function* () {
        if (_this4.currentSession) {
          try {
            yield _this4.updateMatchInDatabase(_this4.currentSession.match);
          } catch (error) {
            console.error('Auto-save failed:', error);
          }
        }
      }), 120000);
    }
  }, {
    key: "cleanupFailedSession",
    value: (function () {
      var _cleanupFailedSession = (0, _asyncToGenerator2.default)(function* () {
        try {
          if (this.currentSession) {
            if (this.currentSession.videoRecordingActive) {
              yield _VideoRecordingService.videoRecordingService.stopRecording();
            }
            if (this.autoSaveInterval) {
              clearInterval(this.autoSaveInterval);
              this.autoSaveInterval = null;
            }
            if (this.syncInterval) {
              clearInterval(this.syncInterval);
              this.syncInterval = null;
            }
            this.currentSession = null;
          }
        } catch (error) {
          console.error('Failed to cleanup session:', error);
        }
      });
      function cleanupFailedSession() {
        return _cleanupFailedSession.apply(this, arguments);
      }
      return cleanupFailedSession;
    }())
  }]);
}();
var matchRecordingService = exports.matchRecordingService = new MatchRecordingService();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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